[{"type": "component", "target": ":showcase-rishiji-ump", "path": "shared/components/showcase/rishiji-ump/index"}, {"name": "global-new-goods", "type": "component", "target": ":showcase-goods-new", "path": "@youzan/captain-weapp/dist/packages/showcase/components/new-goods/index"}, {"type": "component", "target": ":showcase-feature-video", "path": "shared/components/showcase/feature-video/index"}, {"type": "component", "target": ":showcase-feature-video-search", "path": "shared/components/showcase/feature-video-search/index"}, {"type": "component", "target": ":i18n", "path": "components/retail/i18n/index"}]