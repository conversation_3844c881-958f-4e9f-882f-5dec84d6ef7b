const { getDynamicBuildConfig, CONFIG_FILE } = require('@youzan/diff-pack');
const { writeJsonSync, createFileSync } = require('fs-extra');

// 立即执行
(async function generateDynamicConfig() {
  const { APP_NAME, APP_VERSION } = process.env;
  console.log(`[diff-pack-core]${APP_NAME}(${APP_VERSION})拉取动态规则`);
  const config = await getDynamicBuildConfig(APP_NAME, APP_VERSION);
  if (!config) {
    console.log(`[diff-pack-core]${APP_NAME}无远端动态规则`);
  } else {
    // 创建文件
    createFileSync(CONFIG_FILE);
    writeJsonSync(CONFIG_FILE, config);
    console.log(
      `[diff-pack-core]${APP_NAME}动态规则初始化成功.\n\n ${JSON.stringify(
        config,
        null,
        2
      )}`
    );
  }
})();
