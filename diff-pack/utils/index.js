const { isDev } = require('@kokojs/shared');
const {
  getYunDesignBuildConfig,
  yunAbilityName,
} = require('@youzan/diff-pack');

const YunPlugin = require(`../plugins/yun.js`);

const isYun = process.env.BUILD_ENV === 'youzanyun';

const isBuild = process.argv.includes('build') || isDev();

const getPlugins = (app = 'wsc') => {
  const diffPackPlugins = [];
  if (isYun) {
    diffPackPlugins.push(new YunPlugin());
  }
  if (!isBuild) {
    return [];
  }

  const pluginPath = `../plugins/${app}.js`;
  try {
    // eslint-disable-next-line import/no-dynamic-require, global-require
    const AssignPlugin = require(pluginPath);
    console.log(`使用app插件: ${pluginPath}`);
    diffPackPlugins.push(new AssignPlugin());
  } catch (error) {
    console.log(`未找到app同名插件: ${pluginPath}`);
  }

  return diffPackPlugins;
};

// 如果构建上下文传入具名app配置
// 则读取并使用
const getAppConfig = (app = 'wsc') => {
  if (!isBuild) {
    return {};
  }

  const overwriteAppConfig = isYun
    ? getYunDesignBuildConfig()
    : { overwrite: false };
  let config;

  // 判断是否是云 & 是否存在覆盖式构建配置
  if (overwriteAppConfig.overwrite) {
    config = overwriteAppConfig.content;
    console.log(
      `使用定制差异化构建配置: \n${JSON.stringify(
        overwriteAppConfig.content,
        2
      )}`
    );
  } else {
    const appPath = `../app/${app}.json`;
    try {
      // eslint-disable-next-line import/no-dynamic-require, global-require
      config = require(appPath);
      console.log(`使用app构建配置: ${app}.json`);
    } catch (error) {
      config = null;
    }
  }

  if (isYun) {
    // 注入有赞云ability入口
    config.use.push(yunAbilityName);
  }

  return config;
};

module.exports = {
  getPlugins,
  getAppConfig,
};
