const { fs, root } = require('@kokojs/shared');
const { resolve } = require('path');
const { yunAbilityName } = require('@youzan/diff-pack');
const { initYunAddonResource } = require('@youzan/diff-pack');

class YunBuildPlugin {
  resource = [];

  apply(hooks) {
    hooks.initResource.tap('YunBuildPlugin', (ctx) => {
      // console.log('===================================================');
      // console.log(JSON.stringify(ctx.hydrateDynamicConfig.use));
      // console.log('===================================================');

      // 写入云构建配置数据源
      this.resource = initYunAddonResource();
      ctx.resource = ctx.resource.concat(this.resource);

      /* 在有赞云下 对标品配置资源进行修改 */

      // 1. 移除定制preload依赖
      const duplicate = ctx.resource.map((rs) => {
        if (rs.name !== 'bizs/goods-detail/index') {
          return rs;
        }
        if (!rs.preloadRule) {
          return rs;
        }
        const UNNEED_PACKAGE = 'packages/goods-main';
        const pkg = rs.preloadRule.packages.filter(
          (pack) => pack !== UNNEED_PACKAGE
        );
        return {
          ...rs,
          preloadRule: {
            network: 'all',
            packages: pkg,
          },
        };
      });
      ctx.resource = duplicate;
    });

    // 修改上下文ability
    hooks.initAbility.tap('YunBuildPlugin', (ctx) => {
      // 关联ability数据
      ctx.ability = ctx.ability.map((ab) => {
        // 查询到对应的ability 修改resource属性
        // 在注册App构建信息时会默认写入
        if (ab.name === yunAbilityName) {
          const addon = this.resource.map((rs) => rs.target);
          ab.resource = addon;
          return ab;
        }
        return ab;
      });
    });
  }
}

module.exports = YunBuildPlugin;
