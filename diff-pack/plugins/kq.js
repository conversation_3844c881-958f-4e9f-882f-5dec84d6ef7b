const { fs, root } = require('@kokojs/shared');
const { resolve } = require('path');
const { adaptorTabRouter } = require('../../build/script/biz/retail');

class KQBuildPlugin {
  apply(hooks) {
    hooks.initResource.tap('KQBuildPlugin', () => {
      // 移除分包重定向
      const rantaGoodsRedirectPath = resolve(
        root,
        'src/ranta-config/bizs/goods-redirect/redirect.page.json'
      );
      const rantaGoodsRedirectJSON = fs.readJsonSync(rantaGoodsRedirectPath);
      // ['/packages/goods/detail/index'] ----> []
      rantaGoodsRedirectJSON.routes = rantaGoodsRedirectJSON.routes.filter(
        (route) => route !== '/packages/goods/detail/index'
      );
      fs.writeFileSync(
        rantaGoodsRedirectPath,
        JSON.stringify(rantaGoodsRedirectJSON, 2),
        'utf-8'
      );
      console.log(`零售客群构建前商品详情处理：删除分包重定向`);

      // 新增导航tab关联retail-shelf组件
      adaptorTabRouter();
    });
  }
}

module.exports = KQBuildPlugin;
