# onEdit 逻辑迁移总结

## 概述
将 `address-item.vue` 组件中的 `onEdit` 逻辑迁移到父组件 `select-address-popup/index.vue` 中，以实现更好的组件解耦和数据管理。

## 修改内容

### 1. 子组件 (address-item.vue) 修改

#### 模板修改
- **文件**: `src/ext-tee-wsc-trade/extensions/detail-receiver-modify/widgets/select-address-popup/address-item.vue`
- **行数**: 第9行
- **修改**: 将 `@click="onEdit"` 改为 `@click="handleEdit"`

#### 脚本修改
1. **移除不必要的导入** (第25-35行):
   ```javascript
   // 移除了以下导入
   import { setStorage } from '@youzan/tee-api';
   import { PAGE_TYPE, navigateToRantaPage } from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
   import Tee from '@youzan/tee';
   import { AUTO_SHOW_MODIFY_ADDRESS_POPUP } from '../../constants';
   ```

2. **简化方法** (第59-69行):
   ```javascript
   // 原来的 onEdit 方法被替换为简单的事件触发
   handleEdit() {
     // 触发父组件的编辑事件，传递当前地址信息
     this.$emit('edit', this.address);
   }
   ```

### 2. 父组件 (index.vue) 修改

#### 模板修改
1. **第23-30行**: 为活跃地址列表添加 `@edit="handleEdit"` 事件监听
2. **第37-43行**: 为禁用地址列表添加 `@edit="handleEdit"` 事件监听

#### 脚本修改
1. **添加计算属性** (第104-122行):
   ```javascript
   computed: {
     // 获取格式化的地址列表（活跃地址）
     formattedActiveList() {
       return this.addressList || [];
     },
     // 获取格式化的地址列表（非活跃地址）
     formattedInactiveList() {
       if (!this.disableAddressList) return [];
       return this.disableAddressList.reduce((acc, item) => {
         return acc.concat(item.list || []);
       }, []);
     },
     // 是否强制选择POI
     forcePoiSelect() {
       return false; // 可根据实际业务需求调整
     },
   }
   ```

2. **添加 handleEdit 方法** (第135-158行):
   ```javascript
   handleEdit(address) {
     // 处理地址编辑逻辑
     setStorage(AUTO_SHOW_MODIFY_ADDRESS_POPUP, true);

     const id = address.id || null;
     /* #ifdef weapp */
     const dbid = this.ctx.lambdas.setDb({
       id,
       list: [...this.formattedActiveList, ...this.formattedInactiveList],
       forcePoiSelect: this.forcePoiSelect,
       delta: 1,
     });
     navigateToRantaPage({
       url: `/packages/trade-buy-subpage/order/address-edit/index?dbid=${dbid}`,
       pageType: PAGE_TYPE.ADDRESS_EDIT,
     });
     /* #endif */

     /* #ifdef web */
     Tee.navigate({
       url: `/wsctrade/order/tee-address/edit?address_id=${id}`,
     });
     /* #endif */
   }
   ```

## 优势

### 1. 组件解耦
- 子组件不再直接处理业务逻辑，只负责UI展示和事件触发
- 父组件统一管理所有地址相关的业务逻辑

### 2. 数据访问优化
- 父组件可以直接访问 `addressList` 和 `disableAddressList`
- 通过计算属性提供格式化的数据，便于维护

### 3. 代码复用
- 编辑逻辑集中在父组件，便于统一维护和修改
- 子组件更加纯粹，可以在其他场景下复用

### 4. 维护性提升
- 业务逻辑集中，减少了代码重复
- 数据流更加清晰，便于调试和维护

## 注意事项

1. **forcePoiSelect 逻辑**: 当前设置为 `false`，如果有特殊业务需求，需要根据实际情况调整
2. **兼容性**: 修改后的代码保持了原有的平台兼容性（weapp/web）
3. **事件传递**: 确保所有使用 `address-item` 的地方都正确处理 `edit` 事件

## 测试建议

1. 测试地址编辑功能是否正常工作
2. 验证在不同平台（小程序/Web）下的表现
3. 确认禁用地址的编辑功能
4. 检查数据传递是否正确
