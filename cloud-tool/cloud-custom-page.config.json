{"containers": [{"contentType": "container", "layout": "column", "contents": [{"contentType": "module", "layout": "column", "contents": ["@ext-tee-wsc-decorate/page-style~${random}", "@wsc-tee-common/custom-page~${random}", "@passport-tee/protocol~${random}"]}]}], "modules": [{"id": "@ext-tee-wsc-decorate/page-style~${random}", "extensionName": "@ext-tee-wsc-decorate/page-style", "extensionVersion": "0.0.1", "stage": "pre", "isRemote": false, "properties": {"useAppStyleIcon": true}}, {"id": "@wsc-tee-common/custom-page~${random}", "extensionName": "@wsc-tee-common/custom-page", "extensionVersion": "0.0.0", "isRemote": false}, {"id": "@passport-tee/protocol~${random}", "extensionName": "@passport-tee/protocol", "extensionVersion": "1.0.0", "isRemote": false}]}