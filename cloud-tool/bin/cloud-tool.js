#!/usr/bin/env node
const path = require('path');

const program = require('commander');
const fs = require('fs-extra');

const build = require('../src/build');
const tar = require('../src/tar');
const prebuild = require('../src/prebuild');

const packageInfo = require('../../package.json');

process.on('uncaughtException', (err) => {
  console.log('运行出错，请查看日志', err);
  process.exit(1);
});
process.on('unhandledRejection', (reason, p) => {
  console.log('运行出错，请查看日志', p, ' reason: ', reason);
  process.exit(1);
});

program.version(packageInfo.version);

program.command('build <buildFile>').action(async (buildFile) => {
  await handle('build', buildFile);
});

program.command('tar <buildFile>').action(async (buildFile) => {
  await handle('tar', buildFile);
});

program.command('prebuild <buildFile>').action(async (buildFile) => {
  await handle('prebuild', buildFile);
});

program.parse(process.argv);

async function handle(cmd, buildFile) {
  console.log(`${packageInfo.name}@${packageInfo.version}`);

  const start = Date.now();
  if (!path.isAbsolute(buildFile)) {
    // 如果没有指定构建文件，则到当前目录下寻找cloud-task.json文件
    buildFile = path.resolve(buildFile);
  }
  const buildConfig = await fs.readJson(buildFile);
  buildConfig.youzanMpDir = path.resolve(buildConfig.youzanMpDir);
  buildConfig.buildArtifactPath = path.resolve(buildConfig.buildArtifactPath);
  buildConfig.buildSrcPath = path.resolve(buildConfig.buildSrcPath);

  console.log(`${cmd}任务信息`, buildConfig);
  if (cmd === 'build') {
    await build(buildConfig);
  } else if (cmd === 'prebuild') {
    await prebuild(buildConfig);
  } else {
    await tar(buildConfig);
  }
  const end = Date.now();
  console.log(`${cmd} 运行结束, 耗时 ${(end - start) / 1000}s`);
}
