## 合并商家定制代码

### 配置文件

```

-- wsc/cloud-task.json # 指定定制规则，主要是两个仓库目录等信息

// 示例内容
{
  "isvUiDir": "/Users/<USER>/workspace/gitlab/weapp/weapp-custom-demo", // 定制仓库目录
  "youzanMpDir": "/Users/<USER>/workspace/gitlab/weapp/wsc", // 微商城仓库目录
  "shopType": "wsc",
  "buildArtifactPath": "/Users/<USER>/workspace/gitlab/weapp/weapp-cloud-space/artifact.tar.gz",
  "buildSrcPath": "/Users/<USER>/workspace/gitlab/weapp/weapp-cloud-space/src.tar.gz"
}
```

### 合成过程

1. 合并的配置

```
{
  "app": {}
}
```
