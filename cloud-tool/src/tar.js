/* eslint-disable import/no-extraneous-dependencies */
const path = require('path');
const { promiseSpawn } = require('./utils/run');
const _ = require('lodash');

module.exports = async function postBuild(buildConfig) {
  await collectSource(buildConfig);
  await collectArticfact(buildConfig);
};

async function collectSource({ youzanMpDir, buildSrcPath, shopType }) {
  const packageName = path.basename(youzanMpDir);
  const cwd = path.dirname(youzanMpDir);
  const param = [
    '--exclude',
    `${packageName}/dist`,
    '--exclude',
    `${packageName}/.git`,
    '--exclude',
    `${packageName}/node_modules`,
    '--exclude',
    `${packageName}/.cache-loader`,
    '-czf',
    buildSrcPath,
    packageName,
  ];
  await promiseSpawn('tar', param, {
    cwd,
  });
}

async function collectArticfact({ youzanMpDir, buildArtifactPath, shopType }) {
  await promiseSpawn('tar', ['-czf', buildArtifactPath, 'dist'], {
    cwd: youzanMpDir,
  });
}
