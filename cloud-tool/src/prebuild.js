/* eslint-disable @youzan/koko/no-async-await */

// @ts-check
const path = require('path');
const { axios, fs, glob } = require('@kokojs/shared');
const { generateYunAdapter } = require('@youzan/ranta-mp-ext-adapter-builder');

const { cloudPageReplace } = require('@youzan/diff-pack');

// 商品目录处理
function linkGoods() {
  // const SRC_PATH = path.join(process.cwd(), 'src');
  // const GOODS_PATH = path.join(SRC_PATH, './packages/goods');
  // const GOODS_V2_PATH = path.join(SRC_PATH, './packages/goods-v2');
  // const GOODS_V3_PATH = path.join(SRC_PATH, './packages/goods-v3');
  // try {
  //   if (fs.existsSync(GOODS_PATH)) {
  //     fs.removeSync(GOODS_PATH);
  //   }
  //   fs.copySync(GOODS_V2_PATH, GOODS_PATH);
  //   console.log('copy goods', GOODS_V2_PATH, GOODS_PATH);
  //   fs.removeSync(GOODS_V2_PATH);
  //   console.log('remove goods-v2 success', GOODS_V2_PATH);
  //   fs.copySync(GOODS_V3_PATH, GOODS_V2_PATH);
  //   console.log('copy goods-v3', GOODS_V3_PATH, GOODS_V2_PATH);
  // } catch (error) {
  //   console.log('copy goods error', error);
  // }
}

async function getNativeCustomPage(buildConfig, uiDir) {
  const { shopType } = buildConfig;
  const extensionExist = fs.existsSync(uiDir);
  if (!extensionExist) {
    return [];
  }
  const extensionPath = path.resolve(
    uiDir,
    shopType === 'wsc' ? 'mp-extension' : 'retail-mp-extension',
    'extension.json'
  );
  const extensionJson = await fs.readJSON(extensionPath);
  const modifiedPageNames = Object.keys(extensionJson.pageCustom);

  const PathMap = {
    'packages/order-extra/paid/index': 'packages/order/paid-v1/index', // 支付结果
    'packages/goods/cart/index': 'packages/trade/cart-v1/index', // 购物车
    'pages/goods/cart/index': 'packages/trade/cart-v1/index', // 购物车
    'pages/home/<USER>/index': 'pages/home/<USER>/index', // 微页面定制
    'components/account/user-authorize/index': 'pages/home/<USER>/index', // 微页面定制
    'pages/home/<USER>/index': 'pages/home/<USER>/index', // 首页定制
    'packages/trade/order/result/index': 'packages/trade/order-detail-v1/index', // 订单详情
    'packages/order/index': 'packages/order-native/index', // 下单&待支付
    'packages/trade/order/list/index': 'packages/trade/order/list-native/index', // 订单列表
  };
  const customedPageNames = modifiedPageNames.map(
    (item) => PathMap[item] || item
  );
  // tabbar 定制 或者 微页面定制，加入 home 分包的所有路径
  const appExt = extensionJson.app;
  if (appExt && appExt.tabbar && appExt.tabbar.custom) {
    customedPageNames.push('pages/home/<USER>/index');
  }
  if (modifiedPageNames.includes('packages/order/index')) {
    customedPageNames.push('trade-buy');
    customedPageNames.push('trade-pay');
  }
  return customedPageNames;
}

async function setMpDiyConfigNative(buildConfig) {
  // 生成@youzan/mp-diy-config包需要的配置文件
  const { isvUiDir, extensions = [] } = buildConfig;
  const customedPageNames = await getNativeCustomPage(buildConfig, isvUiDir);
  await Promise.all(
    extensions.map(async (ext) => {
      const currentDiyPage = await getNativeCustomPage(buildConfig, ext);
      customedPageNames.push(...currentDiyPage);
    })
  );

  await fs.writeJSON('mp-diy-config-native.json', customedPageNames, {
    spaces: 2,
  });
}

// eslint-disable-next-line no-unused-vars
async function setMpDiyConfigRanta(buildConfig) {
  const { kdtId } = buildConfig;
  //  请求接口拿数据
  let api;
  // @ts-ignore
  if (['qa'].indexOf(process.env.ISV_BUILD_ENV) > -1) {
    api = 'http://montage-db-driver.qa.s.qima-inc.com/ranta/route';
  } else {
    api = 'http://montage-db-driver.s.qima-inc.com/ranta/route';
  }
  const response = await axios.get(api, {
    params: { kdtId, configType: 3 },
  });
  const responseBody = response.data;
  if (!responseBody.success) {
    throw new Error(`${api} 访问出错 ${JSON.stringify(responseBody)}`);
  }
  let rantaDiyPages = [];
  if (responseBody.data) {
    rantaDiyPages = responseBody.data.originalIdList;
  }

  await fs.writeJSON('mp-diy-config-ranta.json', rantaDiyPages, {
    spaces: 2,
  });
}

// eslint-disable-next-line no-unused-vars
async function generateRantaPageInfo(buildConfig) {
  const { youzanMpDir } = buildConfig;
  const rantaPageInfo = {};
  const rantaConfigDir = path.resolve(youzanMpDir, 'src/ranta-config');
  const pageJsonPathList = await glob(['**/*.page.json'], {
    cwd: rantaConfigDir,
  });
  for (const pageJsonPath of pageJsonPathList) {
    const pageJson = fs.readJsonSync(
      path.resolve(rantaConfigDir, pageJsonPath)
    );
    for (const route of pageJson.routes) {
      const rantaPagePath = route.startsWith('/') ? route.substring(1) : route;
      rantaPageInfo[rantaPagePath] = {
        uuid: pageJson.id,
      };
    }
  }
  await fs.writeJSON('mp-ranta-page-info.json', rantaPageInfo, { spaces: 2 });
}

/**
 * 判断老定制中是否存在整页替换
 * 1. 订正中台化页面定制配置，通过编译切流，删除中台化页面
 * 2. 订正 diff-pack 中的页面定制
 */
async function modifyDiyRantaConfig(buildConfig) {
  const { youzanMpDir, isvUiDir, shopType, extensions = [] } = buildConfig;
  const uiDirs = [...extensions, isvUiDir];

  const pageCustomEntities = [];
  for await (const uiDir of uiDirs) {
    if (!fs.existsSync(uiDir)) continue;
    const extensionPath = path.resolve(
      uiDir,
      shopType === 'wsc' ? 'mp-extension' : 'retail-mp-extension',
      'extension.json'
    );
    const extensionJson = await fs.readJSON(extensionPath);
    pageCustomEntities.push(...Object.entries(extensionJson.pageCustom || {}));
  }

  const rantaPageInfoPath = path.resolve(
    youzanMpDir,
    'mp-ranta-page-info.json'
  );
  const diyRantaConfigPath = path.resolve(
    youzanMpDir,
    'mp-diy-config-ranta.json'
  );
  const rantaPageInfo = await fs.readJSON(rantaPageInfoPath);
  let diyRantaConfig = await fs.readJSON(diyRantaConfigPath);

  pageCustomEntities
    .filter(([_, { customType }]) => customType === 'page-replace')
    .forEach(([key]) => {
      const uuid = rantaPageInfo[key]?.uuid;

      // 整页替换时移除中台化定制页面
      if (uuid) {
        diyRantaConfig = diyRantaConfig.filter((item) => !item.endsWith(uuid));
      }

      // 整页替换时定制 diff-pack 配置
      cloudPageReplace(key, { path: key });
    });

  await fs.writeJSON(diyRantaConfigPath, diyRantaConfig, {
    spaces: 2,
  });
}

async function processDiffPackOrderList(buildConfig) {
  const { shopType, youzanMpDir } = buildConfig;

  // -- config -------------------------------------------------------------------------------------
  const orderListRetailPagePath = path.resolve(
    youzanMpDir,
    'src/ranta-config/bizs/order-list/order-list-retail.page.json'
  );
  const orderListWscPagePath = path.resolve(
    youzanMpDir,
    'src/ranta-config/bizs/order-list/order-list.page.json'
  );
  const orderListRetailYunPath = path.resolve(
    youzanMpDir,
    'src/ranta-config/bizs/order-list/order-list-retail.yun.json'
  );
  const orderListWscYunPath = path.resolve(
    youzanMpDir,
    'src/ranta-config/bizs/order-list/order-list.yun.json'
  );
  const diffPackTradeAbilityPath = path.resolve(
    youzanMpDir,
    'diff-pack/ability/trade.json'
  );
  const diffPackTradeResourcePath = path.resolve(
    youzanMpDir,
    'diff-pack/resource/trade.json'
  );

  // -- helper -------------------------------------------------------------------------------------
  const filterDiffPackConfig = async (needFilterBiz, needFilterAbilityName) => {
    const diffPackTradeResourceJson = (
      await fs.readJSON(diffPackTradeResourcePath)
    ).filter((item) => item.path !== needFilterBiz);
    await fs.writeJSON(diffPackTradeResourcePath, diffPackTradeResourceJson, {
      spaces: 2,
    });
    const diffPackTradeAbilityJson = (
      await fs.readJSON(diffPackTradeAbilityPath)
    ).map((item) => {
      if (item.name === needFilterAbilityName) {
        item.resource = item.resource.filter(
          (source) => source !== needFilterBiz
        );
      }
      return item;
    });
    await fs.writeJSON(diffPackTradeAbilityPath, diffPackTradeAbilityJson, {
      spaces: 2,
    });
  };
  const safeRenameFile = async (oldPath, newPath) => {
    if (await fs.pathExists(oldPath)) {
      await fs.rename(oldPath, newPath);
    }
  };
  const safeRemoveFile = async (path) => {
    if (await fs.existsSync(path)) {
      await fs.unlink(path);
    }
  };

  // -- process ------------------------------------------------------------------------------------
  if (['kq', 'retail'].includes(shopType)) {
    // 如果有赞云打包的时候是零售或者客群，则使用独有的云配置进行覆盖，以便定制能够生效
    // 后续如果 diff-pack 支持云配置的diff能力，可以交由 diff-pack 实现
    await Promise.all([
      safeRemoveFile(orderListWscPagePath),
      safeRemoveFile(orderListWscYunPath),
      filterDiffPackConfig('bizs/order-list/order-list', 'wsc-order-list'),
    ]);
  } else {
    // 如果是微商城云定制打包则删除零售和客群的配置，因为如果留着这两个零售的配置文件定制的UI会有问题
    await Promise.all([
      safeRemoveFile(orderListRetailPagePath),
      safeRemoveFile(orderListRetailYunPath),
      filterDiffPackConfig(
        'bizs/order-list/order-list-retail',
        'ls-order-list'
      ),
    ]);
  }
}

module.exports = async function preBuild(buildConfig) {
  linkGoods();

  await processDiffPackOrderList(buildConfig);

  await setMpDiyConfigNative(buildConfig);
  // await setMpDiyConfigRanta(buildConfig); 中台化定制1.0下线
  await generateRantaPageInfo(buildConfig);
  await generateYunAdapter(buildConfig);
  await modifyDiyRantaConfig(buildConfig);
};
