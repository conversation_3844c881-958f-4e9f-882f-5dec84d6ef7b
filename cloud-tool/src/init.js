const axios = require('axios');
const path = require('path');
const fs = require('fs');
const fse = require('fs-extra');

const TOKEN = 'kyhSuzjKcxFR4JAs5qMm';
const GITLAB_API_PREFIX = 'https://gitlab.qima-inc.com/api/v4';

async function getRepoIdByName(name) {
  const searchQuery = encodeURIComponent(name);
  return axios({
    url: `${GITLAB_API_PREFIX}/projects?search_namespaces=true&search=${searchQuery}`,
    headers: {
      'PRIVATE-TOKEN': TOKEN,
    },
  })
    .then((res) => {
      return res;
    })
    .catch((err) => {
      console.log(`Git 仓库 ${name} 获取ID失败`);
      throw err;
    });
}

async function downloadRepo(url, tarPath, name) {
  return axios({
    url,
    responseType: 'stream',
    headers: {
      'PRIVATE-TOKEN': TOKEN,
    },
  })
    .then((res) => {
      return new Promise((resolve) => {
        const buf = [];
        const filename = 'dist';
        res.data.on('data', (chunk) => {
          buf.push(chunk);
        });

        res.data.on('end', () => {
          const content = Buffer.concat(buf);
          fs.writeFileSync(tarPath, content);
          resolve(filename);
        });
      });
    })
    .catch((err) => {
      console.log(`Git 仓库 ${decodeURIComponent(name)} 下载失败，请重试。`);
      console.log(`请求 gitlab 地址：${url}`);
      throw err;
    });
}

async function unpackTar(url, dest, name) {
  const tar = await import('tar');
  const tarPath = path.join(__dirname, `${name}.tar`);
  await downloadRepo(url, tarPath, name);
  console.log(`代码包 ${url} 下载成功\n`);
  await tar.extract({
    cwd: dest,
    file: tarPath,
  });
  let isvPath = '';

  fs.readdirSync(dest).forEach((root) => {
    const rootPath = path.join(dest, root);
    fs.readdirSync(rootPath).forEach((item) => {
      if (item.endsWith('-ui')) {
        isvPath = path.join(dest, `${root}/${item}`);
      }
    });
  });
  console.log(`isv代码包解压路径 ${isvPath}\n`);
  fs.unlinkSync(tarPath);
  return isvPath;
}

function getProjectName(url) {
  const regex = /https?:\/\/gitlab.qima-inc.com\/(.*)\/-.*/;
  const match = url.match(regex);

  if (match && match[1]) {
    return match[1];
  } else {
    throw new Error('未匹配到projectName');
  }
}

function getProjectBranch(url) {
  const regex = /https?:\/\/gitlab.qima-inc.com\/.*\/(.*)+$/;
  const match = url.match(regex);

  if (match && match[1]) {
    const fileName = match[1];
    const [file] = fileName.split('.');
    const [_, branch] = file.split('-');
    return branch;
  } else {
    throw new Error('未匹配到对应的分支信息');
  }
}

function deleteDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.readdirSync(dirPath).forEach((file) => {
      const curPath = path.join(dirPath, file);
      if (fs.lstatSync(curPath).isDirectory()) {
        // 递归删除子目录
        deleteDirectory(curPath);
      } else {
        // 删除文件
        fs.unlinkSync(curPath);
      }
    });
    // 删除空目录
    fs.rmdirSync(dirPath);
  }
}

async function init() {
  const { HARDWORKER_TASK_EXTRA_JSON } = process.env;
  const APP_NAME = JSON.parse(HARDWORKER_TASK_EXTRA_JSON).appName;

  const taskJsonPath = path.join(process.cwd(), 'cloud-task.json');
  // 获取cloud-task.json的文件配置信息
  const task = fse.readJsonSync(taskJsonPath);
  /** 设置isvUiDir */
  const { isvUiDir } = task;
  const branch = getProjectBranch(isvUiDir);
  const requestUrl = `${GITLAB_API_PREFIX}/projects/15172/repository/archive?sha=${branch}`;

  const isvDir = path.join(__dirname, 'isv-temp');
  if (fs.existsSync(isvDir)) {
    deleteDirectory(isvDir);
  }
  fs.mkdirSync(isvDir);
  // 下载产物
  const isvPath = await unpackTar(requestUrl, isvDir, 'dist');
  // 设置产物路径
  task.isvUiDir = isvPath;
  /** 设置isvUiDir */

  /** 设置shopType */
  task.shopType = APP_NAME === 'ls' ? 'retail' : 'wsc';
  /** 设置shopType */

  fse.writeFileSync(taskJsonPath, JSON.stringify(task, null, 2));
}

init();
