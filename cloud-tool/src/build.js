/* eslint-disable import/no-extraneous-dependencies, @youzan/koko/no-async-await */
const fs = require('fs-extra');
const path = require('path');
const { promiseSpawn } = require('./utils/run');
const _ = require('lodash');
const mergeNeededPages = require('./utils/merge-needed-pages');

const WITH_LIVE_KDTID = [43901154, 41255504];

module.exports = async function build(buildConfig) {
  const {
    youzanMpDir,
    shopType,
    kdtId,
    appId,
    taskId,
    environment,
    notMinifyIsv,
    setExtJson,
    setProjectConfigJson,
    buildDesc = {},
  } = buildConfig;
  const isWscMp = _.toLower(shopType) === 'wsc';
  const {
    CLOUD_V2_DEV_PAGES,
    CLOUD_V2_DEV_CUSTOM_PAGE,
    CLOUD_V2_DEV_ENTRY_PATH,
  } = buildDesc;
  const { allPageRoutes, packages, realEntryPath, isCloudDev } =
    await mergeNeededPages(
      youzanMpDir,
      CLOUD_V2_DEV_PAGES,
      CLOUD_V2_DEV_CUSTOM_PAGE,
      CLOUD_V2_DEV_ENTRY_PATH
    );
  const entryApp = process.env.APP_NAME;
  const ufile = process.env.ufile === 'true';
  if (isWscMp) {
    await promiseSpawn(
      'sh',
      // eslint-disable-next-line no-restricted-properties
      ['./build/run-build.sh']
        .concat(
          // eslint-disable-next-line no-restricted-properties
          WITH_LIVE_KDTID.includes(kdtId) ? ['-e', 'live'] : []
        )
        .concat(isCloudDev ? ['-p', packages] : [])
        .concat(ufile ? ['-u'] : []),
      {
        env: {
          NOT_MINIFY_ISV: notMinifyIsv ? 'true' : 'false',
          ISV_BUILD_SERVER: process.env.ISV_BUILD_SERVER,
          SOURCE_MAP: process.env.SOURCE_MAP,
          YOUZAN_GITLAB_TOKEN: process.env.YOUZAN_GITLAB_TOKEN,
          // ISV_KDTID: kdtId, // 打包不再依赖店铺id
          MP_BUILD_TASK_ID: taskId,
          APP_NAME: entryApp || 'wsc',
          APP_VERSION: process.env.APP_VERSION,
          ISV_BUILD_ENV: process.env.ISV_BUILD_ENV,
          IS_FAIL_CUSTOM_TAB_BAR: process.env.IS_FAIL_CUSTOM_TAB_BAR,
          NEEDED_PAGES: allPageRoutes,
          CLOUD_V2_DEV_ENTRY_PATH: realEntryPath,
        },
        environment,
        cwd: youzanMpDir,
      }
    );
    if (setExtJson) {
      const extJson = await fs.readJSON(
        path.resolve(youzanMpDir, 'dist/ext.json')
      );
      extJson.extAppid = appId;
      extJson.ext.appId = appId;
      extJson.ext.kdtId = kdtId;
      await fs.writeJSON(path.resolve(youzanMpDir, 'dist/ext.json'), extJson);
    }
    if (setProjectConfigJson) {
      const projectJson = await fs.readJSON(
        path.resolve(youzanMpDir, 'dist/project.config.json')
      );
      projectJson.appid = 'wxb93d02b8b02392c7';
      await fs.writeJSON(
        path.resolve(youzanMpDir, 'dist/project.config.json'),
        projectJson
      );
    }
  } else {
    await promiseSpawn(
      'sh',
      ['./build/run-build.sh']
        .concat(isCloudDev ? ['-p', packages] : [])
        .concat(ufile ? ['-u'] : []),
      {
        env: {
          NOT_MINIFY_ISV: notMinifyIsv ? 'true' : 'false',
          ISV_BUILD_SERVER: process.env.ISV_BUILD_SERVER,
          SOURCE_MAP: process.env.SOURCE_MAP,
          YOUZAN_GITLAB_TOKEN: process.env.YOUZAN_GITLAB_TOKEN,
          // ISV_KDTID: kdtId, // 打包不再依赖店铺id
          MP_BUILD_TASK_ID: taskId,
          APP_NAME: entryApp || 'ls',
          APP_VERSION: process.env.APP_VERSION,
          ISV_BUILD_ENV: process.env.ISV_BUILD_ENV,
          IS_FAIL_CUSTOM_TAB_BAR: process.env.IS_FAIL_CUSTOM_TAB_BAR,
          NEEDED_PAGES: allPageRoutes,
          CLOUD_V2_DEV_ENTRY_PATH: realEntryPath,
        },
        environment,
        cwd: youzanMpDir,
      }
    );
  }
};
