/* eslint-disable @youzan/koko/no-async-await */
const path = require('path');
const { glob, fs } = require('@kokojs/shared');

async function readRantaYunInfo(root) {
  const rantaConfigDir = path.resolve(root, 'src/ranta-config');
  return glob(['bizs/**/*.yun.json'], {
    cwd: rantaConfigDir,
    absolute: true,
  }).then((yunJsonPathList) => {
    return Promise.all(
      yunJsonPathList.map((item) =>
        Promise.all([
          fs.readJson(item).then(async (yunJSON) => {
            return {
              grayRuleKey: yunJSON.grayRuleKey || yunJSON.cloudKey,
            };
          }),
          fs.readJson(item.replace('yun.json', 'page.json')),
        ])
      )
    );
  });
}

module.exports = async function mergeNeededPages(
  root,
  pageKeys,
  customRoutes,
  entryPath
) {
  const defaultPageRoutes = ['pages/bear/index'];

  const pageKeysArr = pageKeys ? pageKeys.split(',') : [];
  const customRoutesArr = customRoutes ? customRoutes.split(',') : [];
  let realEntryPath = entryPath;

  const yunInfo = await readRantaYunInfo(root);

  const packageSet = yunInfo.reduce((set, cur) => {
    const [yunDesc, pageDesc] = cur;
    const route = pageDesc.routes[0].startsWith('/')
      ? pageDesc.routes[0].slice(1)
      : pageDesc.routes[0];

    if (yunDesc.grayRuleKey) {
      if (pageKeysArr.some((key) => key === yunDesc.grayRuleKey))
        set.add(route);
      if (yunDesc.grayRuleKey === realEntryPath) realEntryPath = route;
    }
    return set;
  }, new Set());

  const allPageRoutes = Array.from(packageSet).concat(customRoutesArr);
  const isCloudDev = allPageRoutes.length;
  if (!isCloudDev) {
    return {
      isCloudDev,
      allPageRoutes: '',
      packages: '',
      realEntryPath: '',
    };
  }

  const pageRoutes = allPageRoutes.concat(defaultPageRoutes);
  const packagesArr = Array.from(
    pageRoutes.reduce((set, cur) => {
      const pkg = cur.split('/').slice(0, 2).join('/');
      if (!set.has(pkg)) set.add(pkg);
      return set;
    }, new Set())
  );

  return {
    isCloudDev,
    allPageRoutes: pageRoutes.join(','),
    packages: `packages/async-video,${packagesArr.join(',')}`,
    realEntryPath,
  };
};
