const { spawn } = require('child_process');

const serverEnv = [
  '/opt/tsar/bin',
  '/opt/git/bin',
  '/usr/local/bin',
  '/bin',
  '/usr/bin',
  '/usr/local/sbin',
  '/usr/sbin',
  '/sbin',
  '/opt/isys/bin',
  '/opt/isys/sbin',
  '/opt/node12/bin',
  '/opt/node8/bin',
];

exports.promiseSpawn = function promiseSpawn(cmd, param, options) {
  return new Promise((resolve, reject) => {
    const cloudEnv = process.env.ISV_BUILD_ENV;
    console.log(`执行命令 ${cmd} ${param.join(' ')}`);
    console.log(`cwd: ${options.cwd}`);
    console.log(`cloudEnv: ${cloudEnv}`);

    options.stdio = 'inherit';
    //options.encoding = 'utf8';
    //options.shell = true;
    options.env = options.env || {};
    options.env.PATH = cloudEnv
      ? serverEnv.join(':') + ':' + process.env.PATH
      : process.env.PATH;
    options.env.LC_CTYPE = 'UTF-8';
    options.env.CLOUD_ENV = options.environment || 'prod';
    options.env.ENABLE_CONSOLE = true;
    options.env.BUILD_ENV = 'youzanyun';

    console.log(`options: ${JSON.stringify(options)}`);

    const ls = spawn(cmd, param, options);
    ls.on('error', (err) => {
      console.error(err);
      reject(err);
    });

    ls.on('close', (code) => {
      console.log(`return code ${code}`);
      if (code == 0) {
        resolve(code);
      } else {
        reject(code);
      }
    });
  });
};
