/* eslint-disable @youzan/koko/no-async-await */
const path = require('path');
const fse = require('fs-extra');
const nunjucks = require('nunjucks');

const { resolve, dirname } = path;

async function replaceComponentByRanta(options) {
  const {
    baseRepoDir,
    customRepoDir,
    contextPath = 'src',
    customPackPath = '',
    customInfo,
  } = options;

  const customComponentsDir = 'custom-components';
  const sourcePath = dirname(
    resolve(customRepoDir, contextPath, customPackPath)
  );
  const targetPath = resolve(
    baseRepoDir,
    contextPath,
    'packages/usercenter/dashboard'
  );

  fse.ensureDir(resolve(targetPath, customComponentsDir));
  if (fse.existsSync(resolve(sourcePath, 'custom-components'))) {
    const subDirs = await fse.readdir(resolve(sourcePath, customComponentsDir));
    for await (const dir of subDirs) {
      await fse.copy(
        resolve(sourcePath, customComponentsDir, dir),
        resolve(targetPath, customComponentsDir, dir)
      );
    }
  }

  const design = customInfo.components.map((component) =>
    component.type === 'custom'
      ? {
          type: 'custom-' + component.name,
          custom: true,
          customProps: { imgUrl: component.imgUrl },
        }
      : { type: component.name, customProps: { imgUrl: component.imgUrl } }
  );
  design.length &&
    (await fse.writeJson(
      resolve(targetPath, 'design.json'),
      { design },
      { spaces: 2 }
    ));
}

const PageComponentsDir = 'components/usercenter/dashboard/components';

module.exports = async function replaceComponent(options) {
  const {
    baseRepoDir,
    customRepoDir,
    contextPath = 'src',
    customPackPath = '',
    customInfo,
  } = options;

  console.log('[execute MergeCustomComponents UserCenter]:', customPackPath);

  await replaceComponentByRanta(options);

  const sourcePath = dirname(
    resolve(customRepoDir, contextPath, customPackPath)
  );
  const usComponentsDir = resolve(baseRepoDir, contextPath, PageComponentsDir);

  // 复制自定义组件
  if (fse.existsSync(resolve(sourcePath, 'custom-components'))) {
    await fse.copy(
      resolve(sourcePath, 'custom-components'),
      resolve(usComponentsDir, 'custom-components')
    );
  }

  // 修改页面的index.json
  const pageJson = await fse.readJson(resolve(usComponentsDir, 'index.json'));

  for (const component of customInfo.components) {
    if (component.type == 'custom') {
      pageJson.usingComponents[
        'custom-' + component.name
      ] = `./custom-components/${component.name}/index`;
    }
  }
  await fse.writeJson(resolve(usComponentsDir, 'index.json'), pageJson, {
    spaces: 2,
  });

  // 生成index.wxml
  const njkEnv = nunjucks.configure(__dirname, {
    tags: {
      blockStart: '{%',
      blockEnd: '%}',
      variableStart: '{$',
      variableEnd: '$}',
      commentStart: '{#',
      commentEnd: '#}',
    },
  });
  const tpl = await fse.readFile(resolve(__dirname, 'index.njk.wxml'), 'utf8');
  const template = nunjucks.compile(tpl, njkEnv);
  const wxml = template.render({ components: customInfo.components });
  await fse.writeFile(resolve(usComponentsDir, 'index.wxml'), wxml);
};
