{% for component in components %}
  {% if component.name === 'uc_user_info' %}
  <user-info
    userAgreePrivacy="{{userAgreePrivacy}}"
    avatar="{{ avatar }}"
    user-name="{{ userName }}"
    background-image="{{ componentConfigsMap['uc_user_info'].backgroundImage }}"
    grade="{{ grade }}"
    links="{{ baseLinks }}"
    show-bind-user="{{ showBindUser }}"
    bind-tips="{{ bindTips }}"
    is-not-valid="{{ isNotValid }}"
    is-support-sign="{{ isSupportSign }}"
    is-signed="{{ isSigned }}"
    user-name-color="{{ componentConfigsMap['uc_user_info'].userNameColor }}"
    align="{{ componentConfigsMap['uc_user_info'].align }}"
    gradient="{{ componentConfigsMap['uc_user_info'].gradient }}"
    member-type="{{ componentConfigsMap['uc_user_info'].memberType }}"
    is-member="{{ isMember }}"
    show-level="{{ showLevel }}"
    is-support-growth-value="{{ isSupportGrowthValue }}"
    show-growth="{{ showGrowth }}"
    level-value="{{ levelValue }}"
    growth-value="{{ growthValue }}"
    bind:bindUser="handleBindUser"
    bind:tapUserInfo="handleTapUserInfo"
    bind:bindGetUserInfo="handleBindGetUserInfo"
    bind:tapSign="handleSign"
    bind:tapLevel="handleTapUserInfo"
  />

  {% elif component.name === 'uc_image_ad' %}
  <image-ad
    wx:if="{{ componentConfigsMap['uc_image_ad'].images.length > 0}}"
    template="{{ componentConfigsMap['uc_image_ad'].template }}"
    images="{{ componentConfigsMap['uc_image_ad'].images }}"
    pageMargin="20"
    swipeFill
    bind:itemClick="handleImageClick"
    bind:navigate="handleImageClick"
    bind:contactback="handleContactBack"
  />

  {% elif component.name === 'uc_stats' %}
  <stats
    userAgreePrivacy="{{userAgreePrivacy}}"
    show-change="{{ stats.showChange }}"
    stats-names="{{statsNames}}"
    show-balance="{{ stats.showBalance }}"
    show-coupon-dot="{{ stats.showCouponDot }}"
    balance="{{ stats.balance }}"
    points="{{ stats.points }}"
    coupons="{{ stats.coupons }}"
    cards="{{ stats.cards }}"
    links="{{ baseLinks }}"
    is-not-valid="{{ isNotValid }}"
    stats-list="{{ statsList }}"
    bind:statsItemClicked="onStatsItemClicked"
    bind:bindGetUserInfo="handleBindGetUserInfo"
  />

  {% elif component.name === 'uc_order' %}
  <order
    userAgreePrivacy="{{userAgreePrivacy}}"
    links="{{ orderLinks }}"
    to-pay="{{ orders.toPay }}"
    evaluate="{{ orders.evaluate }}"
    feedback="{{ orders.feedback }}"
    confirm="{{ orders.confirm }}"
    paid="{{ orders.paid }}"
    sent="{{ orders.sent }}"
    is-not-valid="{{ isNotValid }}"
    bind:bindGetUserInfo="handleBindGetUserInfo"
    bind:pluginItemClicked="onPluginItemClicked"
  />

  {% elif component.name === 'uc_widgets' %}
  <widgets
    userAgreePrivacy="{{userAgreePrivacy}}"
    mode="{{ componentConfigsMap['uc_widgets'].mode }}"
    icon-mode="{{ componentConfigsMap['uc_widgets'].iconMode }}"
    plugins="{{ pluginList }}"
    values="{{ pluginValues }}"
    links="{{ pluginLinks }}"
    is-not-valid="{{ isNotValid }}"
    bind:pluginItemClicked="onPluginItemClicked"
    bind:bindGetUserInfo="handleBindGetUserInfo"
  />
  {% else %}
    <custom-{$ component.name $}/>
  {% endif %}
{% endfor %}
