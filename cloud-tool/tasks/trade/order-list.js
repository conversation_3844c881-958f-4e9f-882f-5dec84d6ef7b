const path = require('path');
const fse = require('fs-extra');
const { getNestedCustomTpl } = require('../utils/tpl-utils');

const { resolve, dirname } = path;

/**
 * 订单列表页 - 组件替换任务
 */
module.exports = async function replaceComponent(options) {
  const {
    baseRepoDir,
    customRepoDir,
    contextPath = 'src',
    wscPackPath = '',
    customPackPath = '',
    customInfo
  } = options;

  console.log('[execute MergeCustomComponents OrderList]:', customPackPath);

  const sourcePath = dirname(resolve(customRepoDir, contextPath, customPackPath));
  const targetPath = dirname(resolve(baseRepoDir, contextPath, wscPackPath));

  // 复制自定义组件
  if (fse.existsSync(resolve(sourcePath, 'custom-components'))) {
    await fse.copy(
      resolve(sourcePath, 'custom-components'),
      resolve(targetPath, 'custom-components')
    );
  }

  // 修改页面的index.json
  const pageJson = await fse.readJson(resolve(targetPath, 'index.json'));

  // 修改components/list index.json
  const listComJson = await fse.readJson(resolve(targetPath, 'components/list', 'index.json'));

  const designCompList = customInfo.components;
  for (const component of designCompList) {
    // 第一层组件
    if (component.type == 'custom') {
      pageJson.usingComponents[
        'custom-' + component.name
      ] = `./custom-components/${component.name}/index`;
    }

    // 嵌套组件 针对 list 组件 子组件定制化
    if (component.name === 'list' && component.children) {
      component.children.forEach(childrenComponent => {
        if (childrenComponent.type === 'custom') {
          listComJson.usingComponents[
            'custom-' + childrenComponent.name
          ] = `../../custom-components/${childrenComponent.name}/index`;
        }
      });
    }
  }
  await fse.writeJson(resolve(targetPath, 'index.json'), pageJson, { spaces: 2 });
  await fse.writeJson(resolve(targetPath, 'components/list', 'index.json'), listComJson, { spaces: 2 });

  // 生成custom-tpl.wxml
  const tplWxmlFile = resolve(targetPath, 'custom-tpl.wxml');

  await fse.writeFile(
    tplWxmlFile,
    getNestedCustomTpl(designCompList, 'list', [])
  );

  // 更新页面配置
  const design = [];
  const designConfig = { type: 'config', profile: {} };
  for (const component of designCompList) {
    const custom = component.type === 'custom';
    const props = component.props;
    const children = component.children || [];
    const cusChildren = children.length && children.map((item) => {
      const childIsCus = item.type === 'custom';
      return {
        type: childIsCus ? 'custom-' + item.name : item.name,
        custom: childIsCus
      };
    });
    const param = {
      type: custom ? 'custom-' + component.name : component.name,
      custom,
    };
    if (props) {
      param.props = props;
    }
    if (cusChildren) {
      param.children = cusChildren;
    }
    design.push(param);
  }
  if (customInfo.version === '1.0.0') {
    const orderList = [
      {
        type: 'bind-phone',
        custom: false,
      },
      {
        type: 'search-block',
        custom: false,
      },
    ];
    design.unshift(...orderList);
  }

  design.unshift(designConfig);
  await fse.writeJson(resolve(targetPath, 'design.json'), { design }, { spaces: 2 });
}
