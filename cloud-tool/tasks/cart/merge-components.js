const path = require('path');
const fs = require('fs-extra');

const { resolve, dirname, relative } = path;

function getCustomTpl(compList) {
  let tpl = '';
  for (const comp of compList) {
    if (comp.type === 'custom') {
      tpl += `<template name="custom-${comp.name}">
      <custom-${comp.name} data="{{ data }}" />
      </template>\n`;
    }
  }

  return tpl;
}

module.exports = async function replaceComponent(options) {
  const {
    baseRepoDir,
    customRepoDir,
    contextPath = 'src',
    wscPackPath = '',
    customPackPath = '',
    customInfo
  } = options;

  console.log('[execute MergeCustomComponents Cart]:', customPackPath);

  const sourcePath = dirname(resolve(customRepoDir, contextPath, customPackPath));
  const targetPath = dirname(resolve(baseRepoDir, contextPath, wscPackPath));
  const shareCartPath = resolve(baseRepoDir, 'shared/components/cart');
  // const groupPath = resolve(shareCartPath, 'goods-group');

  // 复制自定义组件
  if (fs.existsSync(resolve(sourcePath, 'custom-components'))) {
    await fs.copy(
      resolve(sourcePath, 'custom-components'),
      resolve(targetPath, 'custom-components')
    );
  }

  // 修改页面的index.json
  const pageJson = await fs.readJson(resolve(targetPath, 'index.json'));

  // 修改二级组件 index.json
  // const groupComJson = await fs.readJson(resolve(groupPath, 'index.json'));

  const designCompList = customInfo.components;
  for (const component of designCompList) {
    // 第一层组件
    if (component.type === 'custom') {
      pageJson.usingComponents[
        'custom-' + component.name
      ] = `./custom-components/${component.name}/index`;
    }

    // // 嵌套组件 子组件定制化
    // if (component.name === 'valid-block' && component.children) {
    //   component.children.forEach(childrenComponent => {
    //     if (childrenComponent.type === 'custom') {
    //       groupComJson.usingComponents[
    //         'custom-' + childrenComponent.name
    //       ] = `${relative(groupPath, targetPath)}/custom-components/${childrenComponent.name}/index`;
    //     }
    //   });
    // }
  }
  await fs.writeJson(resolve(targetPath, 'index.json'), pageJson, { spaces: 2 });
  // await fs.writeJson(resolve(groupPath, 'index.json'), groupComJson, { spaces: 2 });

  // 生成custom-tpl.wxml
  const tplWxmlFile = resolve(targetPath, 'custom-tpl.wxml');

  // const groupWxmFile = resolve(groupPath, 'custom-tpl.wxml');

  // let groupList = [];

  // for (const component of designCompList) {
  //   if (component.name === 'valid-block') {
  //     if (component.children && component.children.length) {
  //       groupList = component.children;
  //     }
  //   }
  // }

  // 第一层定制组件注入
  await fs.writeFile(tplWxmlFile, getCustomTpl(designCompList));
  // // 第二层嵌套组件注入
  // await fs.writeFile(groupWxmFile, getCustomTpl(groupList));

  // 更新页面配置
  const baseDesign = await fs.readJson(resolve(shareCartPath, 'design.json'));

  let cartAll = baseDesign.design;
  if (customInfo.version === '2.0.0') {
    cartAll = cartAll.filter((item) => item.type !== 'empty-block');
  }

  let design = [];
  for (const component of designCompList) {
    if (component.name === 'cart-all') {
      design = [...design, ...cartAll];
    } else if (component.type === 'custom') {
      design.push({ type: 'custom-' + component.name, custom: true });
    } else {
      design.push({ type: component.name, custom: false });
    }
  }
  await fs.writeJson(resolve(shareCartPath, 'design.json'), { design }, { spaces: 2 });
  await fs.writeJson(resolve(resolve(baseRepoDir, 'src/packages/trade/cart-v1'), 'design.json'), { design }, { spaces: 2 });
};
