{% for component in components %}
{% if component.name == 'cart-all' %}
<page-container fixed-bottom isTabPage>
    <view class="{{ isFetching ? 'skeleton' : '' }}">
        <block wx:if="{{ shopList.length || unAvailableGoodsList.length }}">
            <valid-block
                    wx:for="{{ shopList }}"
                    wx:for-item="shop"
                    wx:key="kdtId"
                    data-shop-index="{{ index }}"
                    shop="{{ shop }}"
                    un-available-goods-list="{{ unAvailableGoodsList }}"
                    edit-mode="{{ editShopIndex === index }}"
                    show-edit="{{ editShopIndex === -1 || editShopIndex === index }}"
                    theme-main-bg-color="{{ themeMainBgColor }}"
                    theme-main-bg-alpha-10-color="{{ themeMainBgAlpha10Color }}"
                    bind:edit-mode-change="handleEditModeChange"
                    bind:shop-check-all="handleShopCheck"
                    bind:change-item-checked="handleItemChecked"
                    bind:change-batch-checked="handleBatchChecked"
                    bind:change-item-num="handleItemNumChange"
                    bind:change-item-delete="handleItemDelete"
                    bind:goods-img-click="goGoodsDetail"
                    bind:refresh-cart="refreshCartData"
            />

            <invalid-block
                    custom-class="{{ shopList.length ? '' : 'no-valid-goods' }}"
                    goods-list="{{ unAvailableGoodsList }}"
                    theme-main-bg-color="{{ themeMainBgColor }}"
                    data-shop-index="{{ -1 }}"
                    bind:goods-img-click="goGoodsDetail"
                    bind:clear="handleUnavailableClear"
                    bind:change-item-delete="handleItemDelete"
            />

            <submit-block
                edit-mode="{{ editShopIndex !== -1 }}"
                total-price="{{ totalCheckedPrice }}"
                cart-tips="{{  cartTips }}"
                total-num="{{ totalCheckedNum }}"
                checked-all="{{ checkedAll }}"
                is-cross-shop="{{ shopList.length > 1 }}"
                bind:check-all-goods="handleAllShopCheck"
                bind:pay-checked-goods="handlePayCheckedGoods"
                bind:batch-delete-goods="handleBatchDelete"
            />
        </block>

        <empty-block
                wx:else
                bind:jump-click="goHome"
        />
    </view>

    <shopping-list wx:if="{{ !isFetching && CURRENT_GLOBAL_SHOP.wechat_sync_shopping_list }}" style-set="{{ 1 }}"/>

    <recommend-goods
      wx:if="{{ !isCrossShop && allGoodsIds }}"
      id="recommend-goods"
      biz-name="cart"
      goods-ids="{{ allGoodsIds }}"
      item-size="{{ 20 }}"
    />
</page-container>
{% else %}
<!-- 自定义组件 -->
<custom-{$ component.name $}
  {% for event in component.events %}
    bind:{$ event.name $}="{$ event.handler $}"
  {% endfor %}
/>
{% endif %}
{% endfor %}

<separate-buy-popup
        separate-buy="{{ separateBuy }}"
        bind:buy="handleSeparateBuy"
        bind:close-separate-buy-popup="handleCloseSeparateBuyPopup"
/>

<van-dialog id="van-dialog" show-cancel-button />
<van-toast id="van-toast" />

<custom-tab-bar></custom-tab-bar>

<base-sku
  theme-class="{{ themeClass }}"
  goods="{{ skuDataGoods }}"
  sku="{{ skuData }}"
  properties="{{ goodsProperties }}"
  show="{{ isShowSkuPopup }}"
  initial-sku="{{ initialSku }}"
  message-config="{{ skuMessageConfig }}"
  extra-data="{{ skuExtraData }}"
  generic:sku-header-price="custom-sku-header-price"
  buy-text="确定"
  quota="1"
  show-add-cart-btn="{{ false }}"
  reset-stepper-on-hide="{{ true }}"
  show-add-cart-btn="{{ false }}"
  show-buy-btn
  bind:buy="onConfirmSku"
  bind:sku-close="onSkuClose"
  bind:sku-prop-selected="onAsyncPropPrice"
  bind:sku-selected="onAsyncPropPrice"
/>