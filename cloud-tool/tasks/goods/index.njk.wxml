<page-container
  fixed-bottom
  show-service-due
  show-shop-status
  show-store-switch
  forbid-copyright="{{ !showGoods }}"
  title-text="{{ title }}"
  open-custom-nav
>
  <view class="feature-page__top-hook" />
  <!-- <service-due></service-due> -->
{% for component in beforeLoadComponent %}
  {% if component.name == 'goods-image' %}
  <view class="image-container">
    <!-- 主图 -->
    <goods-image
            brief-goods="{{ briefData.goods }}"
    />
  </view>
  {% elif component.name == 'activity-banner' %}
  <!-- 活动倒计时 -->
  <activity-banner
          wx:if="{{ showGoods }}"
  />
  {% elif component.name == 'goods-info' %}
  <!-- 商品标题，价格 -->
  <goods-info bind:share="handleShareClick">
    <slot name="goods-info-price-tag" slot="goods-info-price-tag"/>
    <slot name="goods-info-tip" slot="goods-info-tip"/>
  </goods-info>
  <!-- 成交记录轮播 -->
  <goods-trade-carousel wx:if="{{ showGoods }}" />
  {% else %}
  <!-- 自定义组件 -->
  <custom-{$ component.name $} />
  {% endif %}
{% endfor %}

  <block wx:if="{{ showGoods }}">
  {% for component in afterLoadComponent %}
    {% if component.name == 'recommend-store' %}
    <!-- 推荐网点 -->
    <recommend-store />
    {% elif component.name == 'order-preference' %}
    <!-- 优惠信息、促销 -->
    <order-preference>
      <!-- popup里的活动信息 -->
      <slot name="order-preference-item" slot="order-preference-item"/>
    </order-preference>
    {% elif component.name == 'discount-packages' %}
    <!--优惠套餐推荐区域-->
    <discount-packages/>
    {% elif component.name == 'goods-bundle' %}
    <!-- 打包一口价 -->
    <goods-bundle/>
    {% elif component.name == 'plus-buy' %}
    <!-- 加价购 -->
    <plus-buy/>
    {% elif component.name == 'activity' %}
    <!-- 诸如groupon-presale -->
    <slot name="activity"></slot>
    {% elif component.name == 'buy-info' %}
    <!-- 商品购买信息区域 -->
    <buy-info/>
    {% elif component.name == 'buy-info-extra' %}
    <!-- 商品购买额外信息，诸如凑团 -->
    <slot name="buy-info-extra"></slot>
    {% elif component.name == 'shop-info' %}
    <!-- 店铺信息 -->
    <shop-info/>
    {% elif component.name == 'goods-review' %}
    <!-- 评价区域 -->
    <goods-review />
    {% elif component.name == 'buyer-show' %}
    <!-- 买家秀区域 -->
    <buyer-show />
    {% elif component.name == 'goods-detail' %}
    <!-- 商品详情 -->
    <goods-detail />
    {% elif component.name == 'goods-bottom' %}
    <!-- 底部按钮 -->
    <goods-bottom
            bind:share="handleShareClick"
    />
    {% elif component.name == 'share-goods' %}
    <!-- 分享按钮 -->
    <share-goods
            id="share-goods"
    ></share-goods>
    {% elif component.name == 'salesman-icon' %}
    <!-- 分销员按钮 -->
    <salesman-icon
      bind:set-share="onSalesmanSetShare"
      bind:share="onSalesmanShareCard"
    />
    {% elif component.name == 'floating-nav' %}
    <!-- 悬浮窗 -->
    <floating-nav bind:share="handleShareClick" />
    {% elif component.name == 'act-custom-block' %}
    <!-- 活动自定义模块 -->
    <slot name="act-custom-block"/>
    {% elif component.name == 'goods-sku' %}
    <!-- 新版sku -->
    <goods-sku/>
    {% else %}
    <!-- 自定义组件 -->
    <custom-{$ component.name $} />
    {% endif %}
  {% endfor %}
  </block>
</page-container>

<shop-pop-manager wx:if="{{ showShopPopManager }}" source="{{ 2 }}" />
