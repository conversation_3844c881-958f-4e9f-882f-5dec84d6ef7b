const path = require('path');
const fse = require('fs-extra');

const { resolve, dirname } = path;

function getCustomTpl(compList) {
  const tpl = [];

  const tplPrefix = '<block>';
  for (const comp of compList) {
    if (comp.type === 'custom') {
      const compName = `custom-${comp.name}`;
      tpl.push(`  <${compName} wx:if="{{ component === '${compName}' }}" />`);
    }
  }
  const tplSuffix = '</block>';

  return [tplPrefix, ...tpl, tplSuffix].join('\n');
}

/**
 * 旧版商品详情页 - 组件替换任务
 */
module.exports = async function replaceComponent(options) {
  const {
    baseRepoDir,
    customRepoDir,
    contextPath = 'src',
    wscPackPath = '',
    customPackPath = '',
    customInfo
  } = options;

  console.log('[execute MergeCustomComponents GoodsDetailV1]:', customPackPath);

  const sourcePath = dirname(resolve(customRepoDir, contextPath, customPackPath));
  const targetPath = dirname(resolve(baseRepoDir, contextPath, wscPackPath));
  const targetCustomComponentsPath = resolve(targetPath, 'custom-components');

  // 将goods/components/detail-base复制到 实际的页面中
  await fse.copy(
    resolve(targetPath, '../components/detail-base'),
    resolve(targetPath, 'detail-base')
  );

  // 复制自定义组件
  if (fse.existsSync(resolve(sourcePath, 'custom-components'))) {
    await fse.copy(resolve(sourcePath, 'custom-components'), targetCustomComponentsPath);
  }

  // 修改custom-components/index.json
  const componentJson = {
    component: true
  };

  const designCompList = customInfo.components;
  for (let component of designCompList) {
    if (component.type == 'custom') {
      componentJson.usingComponents = componentJson.usingComponents || {};
      componentJson.usingComponents['custom-' + component.name] = `./${component.name}/index`;
    }
  }
  await fse.writeJson(resolve(targetCustomComponentsPath, 'index.json'), componentJson, {
    spaces: 2
  });

  // 生成custom-tpl.wxml
  const tplWxmlFile = resolve(targetCustomComponentsPath, 'index.wxml');
  await fse.writeFile(tplWxmlFile, getCustomTpl(designCompList));

  // 生成index.js
  const scriptContent = `
  Component({
    properties: {
      component: {
        type: String
      }
    },
  });
  `;
  await fse.writeFile(resolve(targetCustomComponentsPath, 'index.js'), scriptContent);

  // 更新页面配置
  const design = [];
  for (const component of designCompList) {
    const custom = component.type === 'custom';
    design.push({
      type: custom ? 'custom-' + component.name : component.name,
      custom,
      needAllData: component.needAllData
    });
  }
  await fse.writeJson(resolve(targetPath, 'detail-base', 'design.json'), { design }, { spaces: 2 });

  // 修改页面的index.json
  const pageJson = await fse.readJson(resolve(targetPath, 'index.json'));
  pageJson.usingComponents['detail-base'] = './detail-base/index';
  await fse.writeJson(resolve(targetPath, 'index.json'), pageJson, {
    spaces: 2
  });
};
