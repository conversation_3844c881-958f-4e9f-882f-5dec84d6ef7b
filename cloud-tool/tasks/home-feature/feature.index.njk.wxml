<view class="showcase-components-container">
  <view
    wx:for="{{ curComponents }}"
    wx:for-item="component"
    wx:for-index="index"
    wx:key="unique"
    class="{{ component.uuidClass }}"
    id="{{ component.uuidId }}"
  >
    <dc-goods
      wx:if="{{component.component === 'dc-goods' || component.type === 'ump_limitdiscount' || component.type === 'member_goods' || component.type === 'ump_seckill' || component.type === 'period_buy' || component.type === 'groupon' || component.type === 'point_goods'}}"
      _opt="{{ component }}"
      _index="{{ index }}"
      kdt-id="{{ extra.kdtId }}"
      uuid="{{ uuid }}"
      data-component="{{ component }}"
      bind:button-click="handleBuyClick"
      bind:item-click="handleGoodsClick"
      bind:component-loaded="handleComponentLoaded"
    />

    <dc-new-zone
      wx:elif="{{ component.type === 'new_zone' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      need-subscribe-message="{{ true }}"
      data-component="{{ component }}"
      bind:item-click="handleGoodsClick"
      bind:handleCouponClick="requestSubscribeMessagePushForTpl"
    />

    <dc-elevator-navigation
      wx:elif="{{ component.type === 'elevator_navigation' }}"
      _opt="{{ component }}"
      page-scroll-key="{{ pageScrollEvent }}"
      data-component="{{ component }}"
      bind:item-jump="handleGoodsClick"
      bind:item-click="handleElevatorNavigationClick"
    />

    <dc-text-nav
      wx:elif="{{ component.type === 'link' }}"
      _opt="{{ component }}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-coupon
      wx:elif="{{ component.component === 'dc-coupon' }}"
      kdt-id="{{ extra.kdtId }}"
      _opt="{{ component }}"
      need-subscribe-message="{{ true }}"
      bind:handleCouponClick="requestSubscribeMessagePushForTpl"
    />

    <dc-new-zone
      wx:elif="{{ component.type === 'new_zone' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      need-subscribe-message="{{ true }}"
      data-component="{{ component }}"
      bind:item-click="handleGoodsClick"
      bind:handleCouponClick="requestSubscribeMessagePushForTpl"
    />

    <dc-cube
      wx:elif="{{ component.component === 'dc-cube' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      extra="{{ extra }}"
      need-subscribe-message="{{true}}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
      bind:handleCouponClick="requestSubscribeMessagePushForTpl"
    />

    <dc-line
      wx:elif="{{ component.component === 'dc-line' || component.type === 'white' }}"
      _opt="{{ component }}"
    />

    <dc-notice
      wx:elif="{{ component.component === 'dc-notice' }}"
      _opt="{{ component }}"
    />

    <dc-rich-text
      wx:elif="{{ component.component === 'dc-rich-text' }}"
      kdt-id="{{ extra.kdtId }}"
      _opt="{{ component }}"
      index="{{ index }}"
    />

    <showcase-contact-us
      wx:elif="{{ component.itemType === 'contact_us' }}"
      business-id="{{ extra.businessId }}"
      source-param="{{ extra.sourceParam }}"
      tab-page="{{ extra.inTabPage }}"
      goods-iphonex="{{ extra.isGoodsIphonex }}"
      component-data="{{ component }}"
      index="{{ index }}"
    />

    <dc-present-gift
      wx:elif="{{ component.component === 'dc-present-gift' && !extra.hideGift }}"
      _opt="{{ component }}"
    />

    <dc-store
      wx:elif="{{ component.component === 'dc-store' }}"
      _opt="{{ component }}"
      bind:jumpToLink="jumpToLink"
    />

    <dc-title-text
      wx:elif="{{ component.component === 'dc-title-text' || component.type === 'text' || component.type === 'title' }}"
      _opt="{{ component }}"
      extra-data="{{ extra }}"
      need-subscribe-message="{{true}}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
      bind:handleCouponClick="requestSubscribeMessagePushForTpl"
    />

    <dc-image
      wx:elif="{{ component.component === 'dc-image' }}"
      kdt-id="{{ extra.kdtId }}"
      _opt="{{ component }}"
      extra-data="{{ extra }}"
      extra="{{ extra }}"
      need-subscribe-message="{{true}}"
      data-component="{{ component }}"
      bind:component-loaded="handleComponentLoaded"
      bind:jumpToLink="handleItemClick"
      bind:handleCouponClick="onCouponImageClick"
      bind:indexChange="onImageIndexChange"
    />

    <dc-video
      wx:elif="{{ component.type === 'video' }}"
      _opt="{{ component }}"
      app-id="{{ extra.appId }}"
      kdt-id="{{ extra.hqKdtId || extra.lsKdtId ||  extra.kdtId }}"
      root-kdt-id="{{ extra.hqKdtId }}"
      multi-size="{{ true }}"
      bind:play="handlePlay"
      bind:ended="handleEnded"
      bind:pause="handlePause"
      bind:videoContext="handleVideoContext"
    />

    <dc-line
      wx:elif="{{ component.component === 'dc-line' || component.type === 'white' }}"
      _opt="{{ component }}"
    />

    <dc-search
      class="cap-search-view"
      wx:elif="{{ component.component === 'dc-search' }}"
      page-scroll-key="{{ pageScrollEvent }}"
      _opt="{{ component }}"
      kdt-id="{{extra.kdtId}}"
      feature-alias="{{ alias }}"
      data-component="{{ component }}"
      bind:tap="loggerComponentClick"
    />

    <dc-hot-words
      wx:elif="{{ component.component === 'dc-hot-words' }}"
      _opt="{{ component }}"
      kdt-id="{{extra.kdtId}}"
      theme-colors="{{themeColors}}"
      feature-alias="{{ alias }}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
    />

    <!-- 日食记定制 -->
    <showcase-feature-video-search
      class="cap-search-view"
      wx:elif="{{ component.itemType === 'feature_video_search' }}"
      component-data="{{ component }}"
    />

    <showcase-goods-new
      wx:elif="{{ component.itemType == 'goods_new'}}"
      kdt-id="{{ extra.kdtId }}"
      component-data="{{ component }}"
    />

    <dc-goods-tags-left
      wx:elif="{{ component.type === 'tag_list_left' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      offline-id="{{ extra.offlineId }}"
      app-id="{{ extra.appId }}"
      data-component="{{ component }}"
      page-scroll-key="{{ pageScrollEvent }}"
      bind:button-click="handleBuyClick"
      bind:item-click="handleGoodsClick"
    />

    <dc-goods-tags-top
      wx:elif="{{ component.type === 'tag_list_top' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      page-scroll-key="{{pageScrollEvent}}"
      data-component="{{ component }}"
      bind:button-click="handleBuyClick"
      bind:component-loaded="handleComponentLoaded"
      bind:item-click="handleGoodsClick"
    />


    <dc-note-card
      wx:elif="{{ component.component === 'dc-notecard' }}"
      _opt="{{ component }}"
    />

    <dc-poster
      wx:elif="{{ component.type === 'oriented_poster'}}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      buyer-id="{{ extra.buyerId }}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
    />


    <dc-crowds-image
      wx:elif="{{ component.component === 'dc-crowds-image'}}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      extra="{{extra}}"
      feature-alias="alias"
      data-component="{{ component }}"
      bind:jumpToLink="onCrowdsImageClick"
      bind:indexChange="onCrowdsImageIndexChange"
    />

    <showcase-feature-video
      wx:elif="{{ component.itemType === 'feature_video'}}"
      kdt-id="{{ extra.kdtId }}"
      buyer-id="{{ extra.buyerId }}"
      component-data="{{ component }}"
    />

    <showcase-rishiji-ump
      wx:elif="{{ component.itemType === 'rishiji_ump'}}"
      kdt-id="{{ extra.kdtId }}"
      buyer-id="{{ extra.buyerId }}"
      component-data="{{ component }}"
      page-lifetimes="{{ ['onPullDownRefresh'] }}"
      page-random-string="{{ pageCommonData.pageRandomString }}"
    />

    <dc-unicashier
      wx:elif="{{ component.type === 'unicashier'}}"
      _opt="{{ component }}"
    />

    <dc-recommend
      wx:elif="{{ component.type === 'goods_recommend' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      offline-id="{{ extra.offlineId }}"
      buyer-id="{{ extra.buyerId }}"
      data-component="{{ component }}"
      bind:item-click="handleGoodsClick"
      bind:button-click="handleBuyClick"
    />

    <dc-enter-shop
      wx:elif="{{ component.component === 'dc-enter-shop'}}"
      _opt="{{ component }}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-shop
      wx:elif="{{ component.component === 'dc-shop'}}"
      _opt="{{ component }}"
      data-component="{{ component }}"
      bind:jumpToLink="shopClick"
    />

    <dc-fans
      wx:elif="{{ component.component === 'dc-fans' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      business-id="{{ extra.businessId }}"
      session-from="{{ extra.sourceParam }}"
      shop-info="{{ shopInfo }}"
      data-component="{{ component }}"
      bind:handleFansClick="onFansClick"
      bind:jumpToLink="handleItemClick"
    />

    <sc-double-eleven
      wx:elif="{{ component.itemType === 'double_eleven' }}"
      component-data="{{ component }}"
      bind:navigate="onNavigate"
      bind:contactback="onContactBack"
    />


    <dc-audio
      wx:elif="{{ component.component === 'dc-audio' }}"
      index="{{ index }}"
      _opt="{{ component }}"
      shop-info="{{ shopInfo }}"
      bind:btn-click="audio__trigger"
      bind:slider-drag="audio__updateProgress"
      bind:src-change="audio__srcChange"
    />

    <sc-category
      wx:elif="{{ component.itemType === 'classification' }}"
      component-data="{{ component }}"
      data-component="{{ component }}"
      extra-data="{{ extra }}"
      bind:navigate="onNavigate"
      bind:contactback="onContactBack"
    />

     <!-- 课程组件 -->
    <dc-course
      wx:elif="{{ component.component === 'dc-course' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{ shopInfo }}"
      theme-colors="{{ themeColors }}"
      buyer-id="{{ extra.buyerId }}"
    />

    <!-- 课程分组 -->
    <dc-course-group
      wx:elif="{{ component.component === 'dc-course-group' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{ shopInfo }}"
      theme-colors="{{ themeColors }}"
      buyer-id="{{ extra.buyerId }}"
    />

    <!-- 知识付费组件 -->
    <dc-content
      wx:elif="{{ component.component === 'dc-content' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{ shopInfo }}"
      theme-colors="{{ themeColors }}"
    />

    <dc-column
      wx:elif="{{ component.component === 'dc-column' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{shopInfo}}"
      theme-colors="{{themeColors}}"
    />

    <dc-live
      wx:elif="{{ component.component === 'dc-live' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{ shopInfo }}"
      theme-colors="{{ themeColors }}"
    />

    <dc-member
      wx:elif="{{ component.component === 'dc-member' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{shopInfo}}"
      theme-colors="{{themeColors}}"
    />

    <!-- punch 没有对应？ -->
    <showcase-punch
      wx:elif="{{ component.itemType === 'punch' }}"
      component-data="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <dc-teacher
      wx:elif="{{ component.component === 'dc-teacher'}}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <dc-shop-ranking-list
      wx:elif="{{ component.type === 'shop_ranking_list' }}"
      _opt="{{ component }}"
      offline-id="{{ extra.offlineId }}"
      theme-colors="{{ themeColors }}"
      data-component="{{ component }}"
      bind:click-content="rankingClick"
      bind:click-more="rankingMoreClick"
      bind:item-click="rankingItemClick"
    />

    <dc-guang
      wx:elif="{{ component.component === 'dc-guang' }}"
      kdt-id="{{ extra.kdtId }}"
      _opt="{{ component }}"
    />

    <dc-regis-form
      wx:elif="{{ component.component === 'dc-regis-form' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      is-home-page="{{ extra.isHomePage}}"
      feature-alias="{{ alias }}"
      user-id="{{ userId }}"
      theme-colors="{{ themeColors }}"
    />


    <dc-weapp-live
      wx:elif="{{ component.type === 'weapp_live' }}"
      _opt="{{ component }}"
      app-id="{{ extra.appId }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <dc-hotel
      wx:elif="{{ component.component === 'dc-hotel' || component.type === 'hotel' || component.type === 'room_type' || component.type === 'combo' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      theme-colors="{{ themeColors }}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-hotel-search
      wx:elif="{{ component.component === 'dc-hotel-search' || component.type === 'hotel_search' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      alias="{{ alias }}"
      city="{{ city }}"
      theme-colors="{{ themeColors }}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-member-value
      wx:elif="{{ component.type === 'member_value' }}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <dc-shelf-banner
      wx:elif="{{ component.type === 'shelf_banner' && component.isShowCpn === 0 }}"
      _opt="{{ component }}"
      theme-colors="{{ themeColors }}"
      kdt-id="{{ extra.kdtId }}"
      extra-data="{{ extra }}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
      bind:handleCouponTake="requestSubscribeMessage"
    />

    <dc-shelf-entry
      wx:elif="{{ component.type === 'shelf_order' && component.isShowCpn === 0 }}"
      component-data="{{ component }}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-shelf-order-pool
      wx:elif="{{ component.type === 'shelf_order_pool' }}"
      _opt="{{ component }}"
    />

    <dc-shelf-customer-asset
      wx:elif="{{ component.type === 'shelf_asset' }}"
      component-data="{{ component }}"
      theme-colors="{{ themeColors }}"
      kdt-id="{{ extra.kdtId }}"
      data-component="{{ component }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-shelf-store-card
      wx:elif="{{ component.type === 'shelf_nearby_store' }}"
      component-data="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
    />
    <dc-shelf-member-card
      wx:elif="{{ component.type === 'shelf_member' && component.isShowCpn === 0 }}"
      kdt-id="{{ extra.kdtId }}"
      component-data="{{ component }}"
    />

    <dc-reward-points
      wx:elif="{{ component.type === 'reward_points' }}"
      _opt="{{ component }}"
    />

    <dc-registration-guide
      wx:elif="{{ component.type === 'registration_guide'}}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <dc-wxvideo-live
      wx:elif="{{component.component === 'dc-wxvideo-live'}}"
      _opt="{{ component }}"
      kdt-id="{{ extra.kdtId }}"
      theme-colors="{{ themeColors }}"
    />
    
    {% for com in components %}
    <custom-showcase-{$ com.name $}
    wx:elif="{{ component.type === '{$ com.name $}' }}"
    component-data="{{ component }}"
    />
    {% endfor %}
  </view>

  <view class="showcase-loading" wx:if="{{ loading }}">
    <van-loading />
  </view>

  <view id="theme-feature__content-end-hook" style="height: 1px;"></view>
</view>
