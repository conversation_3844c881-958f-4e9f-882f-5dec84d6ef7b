/* eslint-disable @youzan/koko/no-async-await */
const path = require('path');
const fse = require('fs-extra');
const nunjucks = require('nunjucks');
const { Project } = require('@youzan/rename-references');
const forEach = require('lodash/forEach');

const { resolve, dirname } = path;

async function replaceComponentByRanta(options) {
  const {
    baseRepoDir,
    customRepoDir,
    contextPath = 'src',
    customPackPath = '',
    customInfo,
  } = options;

  const customComponentsDir = 'custom-components';
  const [uiDir] = customRepoDir.split('/').slice(-2);
  const components = customInfo.components.map((item) => item.name);

  const sourcePath = resolve(
    dirname(
      resolve(customRepoDir, contextPath, customPackPath),
      customComponentsDir
    ),
    customComponentsDir
  );
  const targetPath = resolve(
    baseRepoDir,
    contextPath,
    'packages/third-yun/yun-showcase',
    customComponentsDir
  );

  fse.ensureDir(targetPath);
  if (fse.existsSync(sourcePath)) {
    const subDirs = await fse.readdir(sourcePath);
    for await (const dir of subDirs) {
      await fse.copy(resolve(sourcePath, dir), resolve(targetPath, dir));
    }
    const otherDirs = subDirs.filter((dir) => !components.includes(dir));
    for await (const dir of otherDirs) {
      // 将非组件目录重命名，避免多模板下会覆盖
      new Project(targetPath).rename(
        resolve(targetPath, dir),
        resolve(targetPath, [uiDir, dir].join('-'))
      );
    }
  }
}

let allCustomComponents = [];
module.exports = async function replaceComponent(options) {
  const {
    baseRepoDir,
    customRepoDir,
    contextPath = 'src',
    customPackPath = '',
    customInfo,
    appJSON,
  } = options;
  console.log('[execute MergeCustomComponents HomeFeature]:', customPackPath);

  await replaceComponentByRanta(options);

  const sourcePath = dirname(
    resolve(customRepoDir, contextPath, customPackPath)
  );
  const [uiDir] = customRepoDir.split('/').slice(-2);
  const compBasePath = `shared/components/showcase/extension/${uiDir}`;

  // 复制自定义组件
  if (fse.existsSync(resolve(sourcePath, 'custom-components'))) {
    await fse.copy(
      resolve(sourcePath, 'custom-components'),
      resolve(baseRepoDir, compBasePath)
    );
  }

  // 注册组件 appJson中写入组件
  // const appJSON = await fse.readJson(resolve(baseRepoDir, 'src/app.json'));

  forEach(customInfo.components, (component) => {
    appJSON.usingComponents[
      `custom-showcase-${component.name}`
    ] = `${compBasePath}/${component.name}/index`;
  });

  // await fse.writeJson(resolve(baseRepoDir, 'src/app.json'), appJSON, { spaces: 2 });

  // 微页面中加入组件
  const njkEnv = nunjucks.configure(__dirname, {
    tags: {
      blockStart: '{%',
      blockEnd: '%}',
      variableStart: '{$',
      variableEnd: '$}',
      commentStart: '{#',
      commentEnd: '#}',
    },
  });
  const tpl = await fse.readFile(
    resolve(__dirname, 'feature.index.njk.wxml'),
    'utf8'
  );
  const template = nunjucks.compile(tpl, njkEnv);
  allCustomComponents = allCustomComponents.concat(customInfo.components);
  const wxml = template.render({ components: allCustomComponents });
  await fse.writeFile(
    resolve(baseRepoDir, 'shared/components/showcase-container/index.wxml'),
    wxml
  );

  if (customInfo.profile && customInfo.profile.asyncConfig) {
    const componentsPath = customInfo.profile.asyncConfig.map(
      (item) => `${compBasePath}/${item}/`
    );
    await fse.writeFile(
      resolve(baseRepoDir, 'build/async-config/cloud/async-files.js'),
      `module.exports = ${JSON.stringify(componentsPath)};`
    );
  }
};
