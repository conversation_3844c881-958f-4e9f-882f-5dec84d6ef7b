exports.getCustomTpl = function getCustomTpl(compList) {
  let tpl = '';
  for (const comp of compList) {
    if (comp.type === 'custom') {
      tpl += `<template name="custom-${comp.name}">
      <custom-${comp.name} />
      </template>\n`;
    }
  }

  return tpl;
};

exports.getNestedCustomTpl = function getNestedCustomTpl(compList, nestCom, exclude = []) {
  let tpl = '';
  for (const comp of compList) {
    if (comp.type === 'custom') {
      tpl += `<template name="custom-${comp.name}">
      <custom-${comp.name} />
      </template>\n`;
    }
    if (comp.name === nestCom) {
      const child = comp.children;
      child.forEach((element) => {
        if (element.type === 'custom') {
          if (exclude.indexOf(element.name) < 0) {
            tpl += `<template name="custom-${element.name}">
                <custom-${element.name} data="{{ data }}"/>
                </template>\n`;
          }
        }
      });
    }
  }

  return tpl;
};
