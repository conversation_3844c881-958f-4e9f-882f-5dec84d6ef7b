<view
  wx:if="{{ goodsList.length }}"
  class="unavailable-goods"
>
  <view class="zan-cell">
    <view class="zan-cell__bd unavailable_title">失效{{ goodsList.length }}件商品，不计入支付金额</view>
    <view
      wx:if="{{ !hideClear }}"
      class="zan-cell__ft"
    >
      <form-view bindtap="handleClearClick">
        <view
          class="unavailable-goods__clear van-hairline--surround"
        >清空失效商品</view>
      </form-view>
    </view>
  </view>

  
  <view>
    <swipe-cell
      class="swipe-cell"
      wx:for="{{ goodsList }}"
      wx:key="goodsId"
      swipe="{{ swipeDelete }}"
    >
      <view
        slot="body"
        class="goods zan-cell__bd {{ showGoodsImage ? 'goods-with-image' : '' }}"
        data-alias="{{ item.alias }}"
        bindtap="handleGoodsClick"
      >
        <view
          wx:if="{{ showGoodsImage }}"
          class="goods-image zan-card__thumb"
        >
          <image class="zan-card__img" src="{{ item.imgUrl }}" />
        </view>
        <view class="goods-desc-wrapper">
          <view class="goods__title van-multi-ellipsis--l2">{{ item.title }}</view>
          <view wx:if="{{item.sku}}" class="goods__sku van-ellipsis">
            {{ item.sku }}
          </view>
          <view class="goods__desc-info van-clearfix">
            <view class="goods__desc">{{ item.desc }}</view>
            <view class="goods__num">x{{ item.num }}</view>
          </view>
        </view>
      </view>
      <view
        slot="swipe"
        data-index="{{index}}"
        class="delete-btn"
        bindtouchstart="handleDeleteGoods"
      >
        删除
      </view>
    </swipe-cell>
  </view>
</view>
