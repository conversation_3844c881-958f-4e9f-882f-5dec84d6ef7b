### 不可用商品列表

#### 使用
对应 json 文件引入此自定义组件就可以使用
```json
"usingComponents": {
  "unavailable-goods": "path/to/components/unavailable-goods/index"
}
```
页面 wxml 中
```html
<unavailable-goods></unavailable-goods>
```

#### 参数说明
| 参数       | 说明      | 类型       | 默认值       | 必须      |
|-----------|-----------|-----------|-------------|-------------|
| goodsList | 商品列表 | Array | - | |

goodsList 具体格式如下
```js
{
  // 商品名称
  title: '2商品名称是什么，我也不知道，最少显示一行最多显示两行多余',
  // 商品 sku 具体描述
  sku: '商品SKU2',
  // 商品数量
  num: 2,
  // 商品不可购买的原因
  desc: '商品已售罄'
}
```

#### 事件说明
| 事件名       | 说明      | 参数       |
|-----------|-----------|-----------|
| clear | 清除所有商品按钮被点击时 | - |
