import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    goodsList: <PERSON><PERSON>y,
    hideClear: <PERSON><PERSON><PERSON>,
    showGoodsImage: <PERSON>olean,
    swipeDelete: <PERSON>olean
  },

  methods: {
    handleClearClick() {
      this.triggerEvent('clear');
    },

    handleDeleteGoods({ currentTarget }) {
      const { dataset = {} } = currentTarget;
      this.triggerEvent('delete', {
        item: this.data.goodsList[dataset.index]
      });
    },

    handleGoodsClick({ currentTarget }) {
      const { dataset = {} } = currentTarget;
      this.triggerEvent('goodsclick', {
        alias: dataset.alias
      });
    }
  }
});
