@import "shared/common/css/helper/index.wxss";

:host {
  font-size: 14px;
}

.unavailable_title {
  font-size: 12px;
  color: #999999;
}

.unavailable-goods {
  background: #fff;
  overflow: hidden;
}

.unavailable-goods__clear {
  padding: 5px 10px;
  color: #111;
}

.unavailable-goods__clear::after {
  border-radius: 30px;
}

.goods {
  line-height: 1.4;
  color: #999;
}

.goods-info{
  flex: 1;
}

.goods__title{
  display: flex;
  justify-content: space-between;
}

.goods-image {
  margin-right: 5px;
}

.desc{
  font-size: 12px;
  color: #999999;
}

.goods__itm {
  background:#fafafa;
  padding:7px 10px;
  display: flex;
}

.goods__desc-info {
  position: relative;
  font-size: 12px;
  min-height: 16px;
}

.goods__title {
  word-break: break-all;
}

.goods__sku {
  margin: 8px 0;
  position: relative;
  max-width: 490rpx;
}

.goods__num {
  display: inline-block;
  position: absolute;
  bottom: 0;
  right: 0;
}

.goods__desc {
  margin-top: 8px;
  font-size: 12px;
  color: #FF8855;
  position: absolute;
  bottom: 0;
}

.goods-with-image .goods-image {
  width: 90px;
  height: 90px;
}

.goods-with-image .goods-desc-wrapper {
  margin-left: 100px;
}

.swipe-cell {
  width: 100%;
}

.delete-btn {
  display:flex;
  width:60px;
  height:100%;
  align-items:center;
  justify-content:center;
  color:#fff;
  font-size:16px;
  background-color:#f44;
}
