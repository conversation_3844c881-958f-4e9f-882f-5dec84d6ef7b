import WscComponent from 'pages/common/wsc-component';

WscComponent({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },

  properties: {
    swipe: Boolean // 控制是否显示swipe
  },

  data: {
    move: {
      translate: false,
      start: { x: 0, y: 0 }
    }
  },

  methods: {
    handleCellTouchStart(e) {
      console.log(e);
      if (!this.data.swipe) return;
      if (e.changedTouches.length > 1) return;
      // 已经开始记录的，不重复记录
      if (this.data.move && this.data.move.recording) return;

      var touch = e.changedTouches[0];

      if (!touch) return;

      this.setData({
        // 开始记录滑动
        'move.recording': true,
        'move.start': {
          x: touch.pageX,
          y: touch.pageY
        }
      });
    },

    handleCellTouchEnd(e) {
      if (!this.data.swipe) return;
      if (e.changedTouches.length > 1) return;

      var touch = e.changedTouches[0];
      var move = this.data.move;

      // touch starting 还没开始记录
      if (!move.recording) return;

      this.setData({
        'move.recording': false
      });

      var xDistance = touch.pageX - move.start.x;
      var yDistance = touch.pageY - move.start.y;

      if (xDistance < -50 && (Math.abs(xDistance) > Math.abs(yDistance))) {
        this._updateTranslateMove();
      } else {
        this.setData({
          'move.translate': false,
          'move.translateStyle': ''
        });
      }
    },

    _updateTranslateMove() {
      // 根据划出的操作区域开始动画
      wx
        .createSelectorQuery()
        .in(this)
        .select('.zan-cell__swipe')
        .boundingClientRect()
        .exec((res) => {
          const [rectData] = res;
          this.setData({
            'move.translate': true,
            'move.translateStyle': `transform: translateX(-${rectData.width}px)`
          });
        });
    },

    forbidCellTouchStart() {
    }
  }
});
