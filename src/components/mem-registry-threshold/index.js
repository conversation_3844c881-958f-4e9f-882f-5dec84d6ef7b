import navigate from '@/helpers/navigate';
import accDiv from '@youzan/utils/number/accDiv';
import WscComponent from 'pages/common/wsc-component/index';
import { ThresholdType } from './constants';
import Args from '@youzan/weapp-utils/lib/args';

const app = getApp();
const NEXT_ROUTE_MAP = {
  [ThresholdType.GROW]: '/packages/shop/levelcenter/free/index', // 会员中心
  [ThresholdType.CONSUME]: '/packages/shop/levelcenter/free/index', // 会员中心
  [ThresholdType.SAVING]: `/packages/pre-card/recharge/index?entry=1&kdt_id=${app.getKdtId()}`, // 充值中心
};

WscComponent({
  options: {
    multipleSlots: true,
  },

  properties: {
    thresholdType: {
      type: Number, // 值类型参考 ThresholdType 常量
      value: null,
    },

    thresholdValue: {
      type: Number,
      value: null,
    },
    jumpTo: {
      type: String,
      value: 'Home',
    },
  },

  data: {
    serializedThresholdValue: 0,
  },

  methods: {
    handleAction() {
      const url = Args.add(
        NEXT_ROUTE_MAP[this.data.thresholdType] ??
          NEXT_ROUTE_MAP[ThresholdType.GROW],
        {
          fromScene: this.properties.jumpTo,
        }
      );

      navigate.navigate({
        url,
      });
    },
  },

  observers: {
    thresholdValue(value) {
      if (this.data.thresholdType === ThresholdType.SAVING) {
        this.setYZData({ serializedThresholdValue: accDiv(value, 100) });
        return;
      }
      this.setYZData({ serializedThresholdValue: value });
    },
  },
});
