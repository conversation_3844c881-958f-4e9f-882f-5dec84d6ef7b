import WscComponent from 'pages/common/wsc-component';
import { LanguageCore } from '@youzan/shop-tee-language';

const app = getApp();

WscComponent({
  properties: {
    t: String, // 占位的
    type: String,
    eps: {
      // ellipsis的缩写, 为了体积
      // 是否单行文本显示
      type: String,
      value: '',
    },
    w: {
      type: String,
      optionalTypes: [String, Number],
      value: '',
    },
    force: {
      type: Boolean,
      value: false,
    },
    // 下面的是继承text组件的
    'user-select': {
      type: Boolean,
      default: false,
    },
    overflow: {
      type: String,
      value: 'visible',
    },
    space: {
      type: String,
      value: '',
    },
    decode: {
      type: Boolean,
      value: false,
    },
  },
  options: {
    virtualHost: true,
  },
  observers: {
    t(val) {
      this.updateDate(val);
    },
  },
  externalClasses: ['c-class'],
  data: {
    currentLang: app.globalData.currentLang,
  },
  methods: {
    setEllipsis(val) {
      let width = 0;
      const { w } = this.data;
      if (val && w) {
        if (w.endsWith('px') || w.endsWith('%')) {
          width = w;
        } else {
          width = w + 'px';
        }
      }
      this.setYZData({
        ellipsis: val,
        width: width ? `max-width:${width}` : '',
      });
    },
    setVal(val) {
      this.setYZData({
        tt: val,
      });
    },
    async updateDate(val) {
      const { force, eps, tt } = this.data;
      const isEmpty = val === undefined || !val.length;
      if (isEmpty && tt === undefined) return;
      if (this.data.currentLang !== app.globalData.currentLang) {
        this.setYZData({
          currentLang: app.globalData.currentLang,
        });
      }
      try {
        const needTranslate = force || (await LanguageCore.needTranslate(val));
        if (!isEmpty && needTranslate) {
          this.setEllipsis(!!eps);
          // this.setVal(val);
          LanguageCore.addLang({
            text: val,
            callback: (val) => {
              this.setVal(val);
            },
          });
          return;
        }
        this.setEllipsis(false);
        this.setVal(val);
      } catch (_) {
        this.setEllipsis(false);
        this.setVal(val);
      }
    },
  },
});
