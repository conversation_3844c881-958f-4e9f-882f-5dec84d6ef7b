import WscComponent from 'pages/common/wsc-component/index';
import { featureSkuUtilsPromise } from 'shared/utils/async-base-sku';

const app = getApp();

WscComponent({
  name: 'external-sku',

  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        if (val && this.data.alias) {
          this.fetchSkuData();
        }
      },
    },

    alias: String,
  },

  data: {
    themeClass: app.themeClass,
  },

  lifetimes: {
    attached() {
      this.on('component:sku:cart', this.handleAddCart);
    },
    detached() {
      this.off('component:sku:cart', this.handleAddCart);
    },
  },

  methods: {
    // 获取sku数据
    fetchSkuData() {
      const { alias } = this.data;

      featureSkuUtilsPromise()
        .then(({ getSkuData }) => getSkuData(alias))
        .then((skuData) => {
          this.setYZData({
            skuData,
          });
        })
        .catch((err) => {
          const title = err.msg || '请求数据失败';
          wx.showToast({
            title,
            icon: 'none',
          });

          app.logger.requestError({
            name: 'external-sku-error',
            message: title,
            alert: 'warn',
            detail: err,
          });
        });
    },

    // 添加购物车回调
    handleAddCart(res) {
      if (res.type == 'add') {
        wx.showToast({
          title: '添加购物车成功',
          icon: 'none',
        });
      }
    },

    // 关闭sku弹窗
    handleSkuClose() {
      this.setYZData({
        'skuData.showGoodsSku': false,
      });
      this.triggerEvent('close');
    },
  },
});
