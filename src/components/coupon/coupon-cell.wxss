.coupon-cell {
  border-radius: 4px;
  margin: 30rpx 30rpx;
  overflow: hidden;
  position: relative;
  background: linear-gradient(to right,#FF6565,#FF8C8C);
}

.coupon-cell.code {
  background: linear-gradient(to right,#5087F9,#25B0FF);
}

.coupon-cell.invalid
{
  background: linear-gradient(to right,#C8CACC,#D3D6D9);
}


.coupon-cell .up {
  display: flex;
  height: 162rpx;
  position: relative;
}
.coupon-cell .up .border_bottom_image {
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  height: 2px;
  background-image: url("https://img.yzcdn.cn/weapp/wsc/P7AMxB.png");
  background-repeat: repeat; 
}
.coupon-cell .up .info1 {
  display: inline-block;
  width: 25%;
  height: 102rpx;
  margin: 30rpx 20rpx;
}
.coupon-cell .up .info1 .price_view {
  display: flex;
  align-items:flex-end;
  justify-content:center;
}
.coupon-cell .up .info1 .price_view .symbol {
  font-size: 28rpx;
  color: white;
}
.coupon-cell .up .info1 .price_view .yuan {
  font-size: 44rpx;
  color: white;
  margin-bottom: -6rpx;
  margin-left: 4rpx;
}
.coupon-cell .up .info1 .price_view .cent {
  font-size: 28rpx;
  color: white;
}

.coupon-cell .up .info1 .discount_view{
  font-size: 44rpx;
  color: white;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.coupon-cell .up .info1 .discount_view .discount_suffix {
  font-size: 28rpx;
  opacity: 0.8;
  margin: 0px 0px 10rpx 8rpx;
}

.coupon-cell .up .info1 .threshold {
  font-size: 24rpx;
  color: white;
  opacity: 0.8;
  text-align: center;
}
.coupon-cell .up .dotted_line {
  display: inline-block;
  width: 0px;
  height: 114rpx; 
  margin-top: 30rpx;
  border-left: 1px dashed hsla(0,0%,100%,.6); 
  background: white; 
  background-clip: padding-box;
}

.coupon-cell .up .circle-bg {
  position: absolute;
  left: 0px;
  right: 0px;
  bottom: -8px;
  height: 10px;
  width: 100%;
}

.coupon-cell .up .info2 {
  display: inline-block;
  width: 73%; 
  height: 102rpx;
  margin: 30rpx 30rpx 30rpx 20rpx;
  position: relative;
}
.coupon-cell .up .info2 .title {
  padding: 12rpx 0px 10rpx 0px;
  font-size: 28rpx;
  color: white;
  width: 280rpx;
  text-align: left;
}
.coupon-cell .up .info2 .time {
  font-size: 24rpx;
  color: white;
  opacity: 0.8;
  width: 290rpx;
  text-align: left;
}
.coupon-cell .up .info2 .button {
  position: absolute;
  right: 0px;
  top: 30rpx;
  border-radius: 2px;
  background-color: white;
  opacity: 0.8;
  width: 134rpx;
  height: 40rpx;
  line-height: 40rpx;
  color: #FF4444;
  text-align: center;
  font-size: 24rpx;
}
.coupon-cell .up .info2 .button.code {
  color: #3388FF;
}

.coupon-cell .down {
  padding: 20rpx 60rpx 20rpx 30rpx;
  background-color: white;
  position: relative;
}

.coupon-cell .down .desc {
  font-size: 24rpx;
  color: #999999;
  line-height:40rpx;
  min-height: 20rpx;
  text-align: left;
}
.coupon-cell .down .arrow {
  position: absolute;
  right: 15rpx;
  width: 36rpx;
  height: 28rpx;
  transition-duration: 0.3s;
  background: url("https://img.yzcdn.cn/weapp/wsc/hlBMoxu.png") center no-repeat;
  background-size: 36rpx 28rpx;
}
.coupon-cell .down .arrowRotate0 {
  top: 30rpx;
  transform: rotate(0deg);
}
.coupon-cell .down .arrowRotate180 {
  top: 25rpx;
  transform: rotate(180deg);
}

.coupon-cell .stamp {
  position: absolute;
  top: -60rpx;
  right: -60rpx;
  width: 150rpx;
  height: 150rpx;
  background: url("https://img.yzcdn.cn/weapp/wsc/2fnzIED.png") center no-repeat;
  background-size: 150rpx;
  transform: rotate(-30deg);
  color: #9E9E9E;
  font-size: 28rpx;
  text-align: center;
  line-height: 150rpx;
}

.coupon-cell .selected {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 90rpx;
  height: 90rpx;
  background: url("https://img.yzcdn.cn/weapp/wsc/MwDMEEb.png") center no-repeat;
  background-size: 90rpx;
}