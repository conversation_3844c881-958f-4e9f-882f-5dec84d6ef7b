<!-- 传入数据格式
coupon:
        {
        ...
        show_more_desc:
        disable_button:
        disable_charge:
        component:{
            type:
            name:
            yuan:
            yuan_to:
            cent:
            cent_to
            discount:
            condition:
            valid_time:
            desc:
            status:
            invalid_content:
            valid_content:
          }
        },
index:2,
selected_coupon:{}
-->
<template name="coupon_card">
  <view class="coupon-cell {{coupon.component.type}} {{coupon.component.status}}"
        bindtap="_onCouponCellTaped" data-coupon="{{coupon}}" data-index="{{index}}">
    <view class="up">
      <view class="info1">
        <view wx:if="{{coupon.component.yuan}}" class="price_view">
          <view class="symbol">¥ </view>
          <view class="yuan">{{coupon.component.yuan}}</view>
          <view wx:if="{{coupon.component.cent}}"
                class="cent">.{{coupon.component.cent}}</view>
        </view>
        <view wx:elif="{{coupon.component.discount}}" class="discount_view">
          <view class="discount_value">{{coupon.component.discount}}</view>
          <view class="discount_suffix">折</view>
        </view>
        <view class="threshold">{{coupon.component.condition}}</view>
      </view>
      <view class="dotted_line"></view>
      <view class="info2">
        <view class="van-ellipsis title">{{coupon.component.name}}</view>
        <view class="time">{{coupon.component.valid_time}}</view>
        <!-- 此处是显示 不可用置灰的图标 -->
        <view
          wx:if="{{coupon.component.status == 'invalid' && coupon.component.invalid_content.length > 0}}"
          class="stamp"
        >{{coupon.component.invalid_content}}</view>
        <!-- 显示优惠券跳转按钮 -->
        <view
          wx:elif="{{!coupon.disable_button && !disablejump}}"
          class="button {{coupon.component.type}}"
          catchtap="_onCouponButtonTaped"
          data-coupon="{{coupon}}"
          data-index="{{index}}"
        >
          {{coupon.component.valid_content}}
        </view>
      </view>
      <view class="border_bottom_image"></view>
      <image class="circle-bg" src="https://img.yzcdn.cn/weapp/wsc/SJEnQ7.png"></image>
    </view>
    <view class="down" catchtap="_onMoredescTaped" data-coupon="{{coupon}}" data-index="{{index}}">
      <block>
        <block wx:if="{{coupon.component.desclist.length <= 1 || !coupon.show_more_desc}}">
          <view class="desc {{coupon.show_more_desc ? '' : 'van-ellipsis'}}">{{coupon.component.desclist[0] || ''}}</view>
        </block>
        <block wx:else>
          <view wx:for="{{coupon.component.desclist}}" wx:key="unique" class="desc">{{item}}</view>
        </block>
      </block>
      <view wx:if="{{coupon.component.desclist.length > 1 || coupon.component.desclist[0].length > 26}}"
            class="arrow {{coupon.show_more_desc ? 'arrowRotate0':'arrowRotate180'}}"></view>
    </view>
    <view wx:if="{{selected_coupon.id == coupon.id}}" class="selected"></view>
  </view>
</template>
