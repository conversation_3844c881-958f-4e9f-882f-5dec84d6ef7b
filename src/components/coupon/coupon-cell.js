/*
外部需要实现
0.require
1.通过extended()函数合并到page对象中
2.在page对象中获取到优惠券列表后，需要调用handleCouponData处理数据再setYZData
3.实现一下三个函数，couponlist为当前page.data中的优惠券列表属性
1)优惠券领取成功的回调
onCouponObtainedSuccess : function(coupon, index) {
  coupon.component.valid_content = '立即使用'
  this.setYZData({
    [`couponlist[${index}]`]:coupon
  })
}
2)优惠券行点击事件回调
onCouponCellTaped : function(coupon, index) {
  //做页面跳转
  //...
}
3)使用说明箭头点击事件回调
onMoredescTaped: function(coupon, index) {
  this.setYZData({
    [`couponlist[${index}]`]:coupon
  })
}
*/

import navigate from '@/helpers/navigate';
import Toast from '@vant/weapp/dist/toast/toast';

var app = getApp();

const COUPON_STATUS_VALID = 'valid';
const COUPON_STATUS_INVALID = 'invalid';

let util = {
  handleCouponData(coupon) {
    let usedCoupon = coupon.group ? Object.assign({}, coupon.group, coupon) : coupon;
    delete usedCoupon.group;
    let newCoupon = usedCoupon;
    newCoupon.show_more_desc = false;
    newCoupon.disable_button = coupon.disable_button || false;
    newCoupon.disable_charge = coupon.disable_charge || false;
    newCoupon.component = {};
    newCoupon.component.name = usedCoupon.name;
    newCoupon.component.type = (usedCoupon.group_type == 9 || usedCoupon.group_type == 10) ? 'code' : 'card';

    if (usedCoupon.preferential_type == 1 || newCoupon.component.type == 'code') {
      let priceArray = usedCoupon.value.split('.');
      let yuan = priceArray[0];
      let cent = priceArray.length == 2 ? priceArray[1] : null;
      cent = cent == '00' ? null : cent;
      newCoupon.component.yuan = yuan;
      newCoupon.component.cent = cent;
    } else if (usedCoupon.preferential_type == 2) {
      let discountArray = usedCoupon.discount.split('.');
      if (discountArray.length == 2) {
        newCoupon.component.discount = discountArray[1] == 0 ? discountArray[0] : usedCoupon.discount;
      } else {
        newCoupon.component.discount = discountArray[0];
      }
    }
    let realCondition;
    if (usedCoupon.originCondition == undefined) {
      realCondition = parseFloat(usedCoupon.at_least) * 100;
    } else {
      realCondition = usedCoupon.originCondition;
    }
    newCoupon.component.condition = realCondition > 0 ? ('满' + realCondition / 100 + '元可用') : '无使用门槛';
    if (usedCoupon.valid_start_at && usedCoupon.expire_at) {
      let start_at = usedCoupon.valid_start_at.substring(0, 10).replace(/-/g, '.');
      let end_at = usedCoupon.expire_at.substring(0, 10).replace(/-/g, '.');
      newCoupon.component.valid_time = start_at + ' - ' + end_at;
    } else if (usedCoupon.date_type == 1 || newCoupon.component.type == 'code') {
      let start_at = usedCoupon.start_at.substring(0, 10).replace(/-/g, '.');
      let end_at = usedCoupon.end_at.substring(0, 10).replace(/-/g, '.');
      newCoupon.component.valid_time = start_at + ' - ' + end_at;
    } else if (usedCoupon.date_type == 2) {
      newCoupon.component.valid_time = '领到券' + (usedCoupon.fixed_begin_term == 0 ? '当日' : '次日') + '开始' + usedCoupon.fixed_term + '天内有效';
    }
    newCoupon.component.desclist = usedCoupon.instructions.split('\n');
    let status = COUPON_STATUS_VALID;
    let invalid_content = '';
    let valid_content = '';
    if (usedCoupon.is_invalid == 1) {
      status = COUPON_STATUS_INVALID;
      invalid_content = '已失效';
    } else if (usedCoupon.is_used == 1) {
      status = COUPON_STATUS_INVALID;
      invalid_content = '已使用';
    } else if (usedCoupon.stock == 0) {
      status = COUPON_STATUS_INVALID;
      invalid_content = '已抢完';
    } else if (usedCoupon.buyer_taked_limit) {
      status = COUPON_STATUS_INVALID;
      invalid_content = '已领取';
    } else {
      valid_content = '立即领取';
    }
    newCoupon.component.status = coupon.status || status;
    newCoupon.component.invalid_content = invalid_content;
    newCoupon.component.valid_content = coupon.valid_content || valid_content;
    return newCoupon;
  },
};

let event = {
  _onMoredescTaped(e) {
    let coupon = e.currentTarget.dataset.coupon;
    let index = e.currentTarget.dataset.index;
    coupon.show_more_desc = !coupon.show_more_desc;
    if (this.onMoredescTaped) {
      this.onMoredescTaped(coupon, index);
    }
  },

  _onCouponButtonTaped(e) {
    let that = this;
    let coupon = e.currentTarget.dataset.coupon;
    let index = e.currentTarget.dataset.index;
    let valid_content = coupon.component.valid_content;
    if (valid_content == '立即领取') {
      wx.showLoading({
        title: '领取中',
      });
      app.request({
        path: 'wscump/coupon/take_coupon_result.json',
        query: {
          groupId: coupon.id
        }
      }).then(() => {
        Toast('领取成功');
        coupon.component.valid_content = '立即使用';
        if (that.onCouponObtainedSuccess) { // 在外面更新this.data
          that.onCouponObtainedSuccess(coupon, index);
        }
      }).catch(e => {
        Toast(e.msg || '领取失败');
      });
    } else if (valid_content == '立即使用') {
      let range_type = coupon.range_type;
      if (range_type == 'all') {
        navigate.switchTab({ url: '/packages/home/<USER>/index' });
      } else {
        let range_value = coupon.range_value;
        if (range_value && range_value.length == 0) {
          navigate.switchTab({ url: '/packages/home/<USER>/index' });
          return;
        }

        navigate.navigate({
          url: '/packages/shop/goods/group/index?pageType=coupon&group_id=' + (coupon.coupon_group_id || coupon.id),
        });
      }
    }
  },

  _onCouponCellTaped(e) {
    // 不可以跳转的直接返回
    if (this.data.disablejump) {
      return;
    }

    let coupon = e.currentTarget.dataset.coupon;
    if (coupon.component.status == COUPON_STATUS_INVALID) {
      return;
    }
    if (this.onCouponCellTaped) {
      this.onCouponCellTaped(coupon, e.currentTarget.dataset.index);
    }
  }
};

export default Object.assign({}, event, util);
