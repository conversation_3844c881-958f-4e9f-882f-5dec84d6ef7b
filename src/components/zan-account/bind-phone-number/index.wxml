<template name="bind_phone_number">
    <view class="bind__container">
        <view class="bind__title">{{ bindTips }}</view>
        <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" class="bindphone__bind-btn bind_action bind-userinfo" is-once catchtap='tapBindZanAccount'>
            <theme-view bg="main-bg" border="main-bg" border-after="main-bg">登录</theme-view>
        </user-authorize>
    </view>
</template>
