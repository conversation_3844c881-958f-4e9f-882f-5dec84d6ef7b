<view class="shop-pop-manager">
  <!-- 订阅消息蒙尘 -->
  <subscribe-guide show="{{ showSubscribeMask }}" bind:close="toggleSubscribeMask" zIndex="{{ 10010 }}" />
  <order-pay-prompt-popup wx:if="{{ showOrderPayPromptPopup }}" source="{{ pageSource }}" bind:close-pop="handleClosePop" />
  <shop-ad wx:if="{{ showShopAd }}" popPosition="{{ popPosition }}" featureId="{{ featureId }}" targetKdtId="{{ targetKdtId }}" bind:pop-open="handleOpenPop" bind:close-pop="handleClosePop" />
  <visit-gift wx:if="{{ showVisitGift }}" source="{{ source }}" bind:close-pop="handleClosePop" bind:pop-open="handleOpenPop" bind:toggle-subscribe-mask="toggleSubscribeMask" />
  <marketing-page wx:if="{{ showMarketingPage }}" source="{{ source }}" active-ump-id="{{activeUmpId}}" bind:pop-open="handleOpenPop" bind:close-pop="handleClosePop" />
  <sign-pop wx:if="{{ showSignPop }}" source="{{ source }}" bind:close-pop="handleClosePop" bind:pop-open="handleOpenPop" bind:toggle-subscribe-mask="toggleSubscribeMask" />
  <member-guide wx:if="{{ showMemberGuide }}" bind:close-pop="handleClosePop" bind:pop-open="handleOpenPop" />
  <customer-rebuy wx:if="{{ showCustomerRebuy }}" source="{{ source }}" bind:close-pop="handleClosePop" bind:toggle-subscribe-mask="toggleSubscribeMask" />
</view>