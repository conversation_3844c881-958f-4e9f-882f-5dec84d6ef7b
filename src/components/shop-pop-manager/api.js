const app = getApp();

const waiting1Second = () => {
  return new Promise((resolve) => {
    setTimeout(resolve, 1000);
  });
};

export const getPopConfig = function ({ keys, popPosition }) {
  return waiting1Second().then(() => {
    return app.requestUseCdn({
      path: '/wscshop/shopad/pop-config-with-keys.json',
      data: {
        popPosition,
        queryJson: JSON.stringify({ keys }),
      },
    });
  });
};
