import WscComponent from 'pages/common/wsc-component/index';
import { getPopConfig } from './api';
import { getScene } from 'shared/utils/channel';
import loggerBehaviors from './behaviors/logger-behaviors';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';

function isObject(value) {
  const type = typeof value;
  return value != null && (type == 'object' || type == 'function');
}

const app = getApp();
const POP_TYPE = {
  pop_ad: 'pop_ad',
  check_in: 'check_in',
  enter_shop: 'enter_shop',
  member_guide: 'member_guide',
  payment_reminders: 'payment_reminders',
  ump_conference: 'ump_conference',
  member_repurchase_coupon: 'member_repurchase_coupon',
};
const POP_MAP = {
  [POP_TYPE.payment_reminders]: 'showOrderPayPromptPopup',
  [POP_TYPE.pop_ad]: 'showShopAd',
  [POP_TYPE.check_in]: 'showSignPop',
  [POP_TYPE.enter_shop]: 'showVisitGift',
  [POP_TYPE.member_guide]: 'showMemberGuide',
  [POP_TYPE.ump_conference]: 'showMarketingPage',
  [POP_TYPE.member_repurchase_coupon]: 'showCustomerRebuy',
};

const SOURCE = {
  HOME: 1,
  GOODS: 2,
  TUAN: 3,
  MICRO: 4,
  GOODS_LIST: 5,
  RETAIL_SHELF: 6,
  RETAIL_INDEX: 7,
};

const MAX_NUMBER_FOR_PAGE = {
  [SOURCE.HOME]: 'home',
  [SOURCE.MICRO]: 'micro',
};

// 广告弹窗管理，目前只有主页、微页面
const SOURCE_POSITION = {
  [SOURCE.HOME]: 1,
  [SOURCE.MICRO]: 2,
  [SOURCE.GOODS_LIST]: 4,
  [SOURCE.RETAIL_SHELF]: 6,
  [SOURCE.RETAIL_INDEX]: 7,
};

const ORDERPAYSOURCE = {
  1: 'SHOP_HOME', // 店铺首页
  2: 'ITEM_DETAIL', // 商品详情
  4: 'MICRO', // 微页面
  5: 'GOODS_LIST', // 商品列表页
  6: 'RETAIL_SHELF', // 点单宝
  7: 'RETAIL_INDEX', // 扫码点单
};

const NEEDS_CACULATE_SHOP_AD_NUMBERS = [
  POP_TYPE.pop_ad,
  POP_TYPE.check_in,
  POP_TYPE.enter_shop,
  POP_TYPE.member_guide,
  POP_TYPE.payment_reminders,
  POP_TYPE.ump_conference,
  POP_TYPE.member_repurchase_coupon,
];

WscComponent({
  behaviors: [loggerBehaviors],
  properties: {
    // 页面来源 1—店铺主页 2—商详页 3—社区团购活动页
    source: {
      type: Number,
      value: null,
      observer(value) {
        this.setPopPosition(value);
      },
    },
    featureId: Number,
    targetKdtId: Number,
  },
  // 因为没有component is 这种方式，用wx:if 做切换
  data: {
    showOrderPayPromptPopup: false,
    showShopAd: false,
    showVisitGift: false,
    showMarketingPage: false,
    showSignPop: false,
    showMemberGuide: false,
    order: [],
    currentIndex: 0,
    pageSource: '',
    popPosition: SOURCE_POSITION[SOURCE.HOME],
    showSubscribeMask: false,
    activeUmpId: 0,
  },

  methods: {
    handleOpenPop() {
      this.triggerEvent('open-pop');
    },

    getAdImageInfo() {
      return this.image_url;
    },
    setPopPosition(source) {
      this.setData({
        popPosition: SOURCE_POSITION[source],
      });
    },
    handleClosePop(e) {
      const app = getApp();
      const { logger } = app;
      // 正常点击关闭弹框传入的是一个object
      const isClickClose = isObject(e.detail);
      this.triggerEvent('close-shop-pop');

      const {
        currentShow = false,
        image_url = '',
        isRealClose = false,
      } = isClickClose
        ? e.detail
        : {
            currentShow: e.detail || false,
          };
      // 销毁当前组件，并渲染下一个
      const { currentIndex, order } = this.data;
      const currentPopType = order[currentIndex];
      const nextPopType = order[currentIndex + 1];

      const currentShowKey = POP_MAP[currentPopType];
      const nextShowKey = POP_MAP[nextPopType];
      let nextShow = true;

      if (isRealClose) {
        this.image_url = image_url;
        const cloudLoggerInfo = getLoggerExtraParams('shop_ad_pop', {
          ...this.getCloudLoggerInfo(currentPopType),
        });
        logger &&
          logger.log({
            et: 'click',
            ei: 'Ad_Close',
            en: '关闭弹框广告',
            si: app.getKdtId(), // 店铺id，没有则置空
            params: {
              component: 'popup_ad',
              ...cloudLoggerInfo,
            },
          });
      }

      if (
        this.isPageShopAdNeedsMaxNumbers &&
        isRealClose &&
        this.pageMaxNumbers &&
        NEEDS_CACULATE_SHOP_AD_NUMBERS.indexOf(currentPopType) !== -1
      ) {
        this.currentShopAdIndex++;
      }

      // 若当前页面有受数量管控的弹窗，且已经弹到最大数值，则不再继续：注意不受数量控制的弹窗一般优先展示
      if (
        this.pageMaxNumbers &&
        this.currentShopAdIndex === this.pageMaxNumbers
      ) {
        nextShow = false;
      }

      setTimeout(() => {
        this.setData({
          currentIndex: currentIndex + 1,
          [currentShowKey]: currentShow,
          [nextShowKey]: nextShow,
        });
        if (currentPopType === POP_TYPE.pop_ad) {
          this.triggerEvent('closePop', {
            type: POP_TYPE.pop_ad,
          });
        }
      }, 1000);
    },

    initData(order) {
      const currentPopType = order[0];
      const currentShowKey = POP_MAP[currentPopType];
      const { source = '' } = this.data;
      const pageSource = ORDERPAYSOURCE[source];
      // 设置order
      this.setData({
        order,
        currentIndex: 0,
        pageSource,
        [currentShowKey]: true,
      });
    },

    // 收敛各个页面默认展示的弹窗类型
    getDefaultKeys() {
      const { source = '' } = this.data;
      const scene = getScene();
      const WECHAT_LIVE_SENCE = 1177;

      // 【场景：商详视频号直播】不弹进店有礼
      if (+source === SOURCE.GOODS && scene === WECHAT_LIVE_SENCE) {
        return [];
      }
      if ([SOURCE.RETAIL_INDEX, SOURCE.RETAIL_SHELF].includes(source)) {
        return [POP_TYPE.pop_ad];
      }
      this.triggerEvent('closePop', {
        type: POP_TYPE.pop_ad,
      });

      if (source === SOURCE.GOODS_LIST) {
        return [POP_TYPE.pop_ad];
      }

      if (source === SOURCE.MICRO) {
        return [
          POP_TYPE.pop_ad,
          POP_TYPE.payment_reminders,
          POP_TYPE.enter_shop,
        ];
      }

      if (source === SOURCE.HOME) {
        return [
          POP_TYPE.pop_ad,
          POP_TYPE.check_in,
          POP_TYPE.enter_shop,
          POP_TYPE.member_guide,
          POP_TYPE.payment_reminders,
          POP_TYPE.ump_conference,
          POP_TYPE.member_repurchase_coupon,
        ];
      }

      return [POP_TYPE.payment_reminders, POP_TYPE.enter_shop];
    },

    getOrder() {
      const { source = '', popPosition } = this.data;

      const parseExt = (ext) => {
        try {
          return JSON.parse(ext);
        } catch (e) {
          throw new Error('pop ads ump_confrerence ext ext parse error');
        }
      };

      // 顺序及个数受控的页面：店铺主页、微页面
      if (source === SOURCE.HOME || source === SOURCE.MICRO) {
        const keys = this.getDefaultKeys();
        // ext 为扩展字段，包括活动会场id
        getPopConfig({
          keys,
          popPosition,
        })
          .then((resp) => {
            const order = [];
            resp
              .filter((item) => item.status === 1) // 优化：过滤已关闭的类型
              .sort((a, b) => a.sort - b.sort)
              .forEach((item, index) => {
                order[index] = item.popType;
              });

            resp
              .filter((item) => item.status === 1)
              .some((item) => {
                if (item.ext) {
                  this.setData({
                    activeUmpId: Number(parseExt(item.ext).id),
                  });
                  return true;
                }
                return false;
              });

            this.initData(order);
          })
          .catch(() => {
            this.initData(keys);
          });

        return;
      }

      // 其他不受控页面
      const order = this.getDefaultKeys();
      this.initData(order);
    },

    toggleSubscribeMask(e) {
      this.setData({
        showSubscribeMask: e.detail,
      });
    },
  },

  attached() {
    this.currentShopAdIndex = 0;
    app.getShopConfigData().then((shopConfig = {}) => {
      const { shop_ad_numbers_for_pages: shopAdNumbersForPages = '{}' } =
        shopConfig;
      const { source } = this.data;

      try {
        this.pageMaxNumbers = (JSON.parse(shopAdNumbersForPages) || {})[
          MAX_NUMBER_FOR_PAGE[source]
        ];
      } catch (e) {
        console.log('获取页面弹窗个数失败', e);
      }

      this.isPageShopAdNeedsMaxNumbers = true;
      this.getOrder();
    });
  },
});
