export default Behavior({
  methods: {
    getCloudLoggerInfo(currentPopType, extraInfo = {}) {
      const pages = getCurrentPages();
      return {
        ...extraInfo,
        ad_name: this.getAdNameByType(currentPopType),
        page_url: pages[pages.length - 1].route,
        img_url: this.getAdImageInfo(),
      };
    },
    // 图片信息
    getAdExtraParams() {},
    getAdNameByType(type) {
      const POP_MAP = {
        payment_reminders: '催付弹窗',
        pop_ad: '弹窗广告',
        check_in: '签到弹窗',
        enter_shop: '进店有礼弹窗',
        member_guide: '引导办会员',
        ump_conference: '活动会场',
      };
      return POP_MAP[type];
    },
  },
});
