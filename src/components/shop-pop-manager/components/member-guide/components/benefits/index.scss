@import '~mixins/index.scss';

.benefits {
  margin: 0 auto;
  padding: 12px;
  background: url('https://img01.yzcdn.cn/upload_files/2022/12/15/FgZDx72I7MalgUHFUeYz-QJoE1_t.png')
    no-repeat white;
  background-position: bottom center;
  background-size: cover;
  box-sizing: border-box;
  width: 315px;
  min-height: 168px;
  border-radius: 16px;

  &__head {
    text-align: right;
  }

  &__body {
    padding: 0 20px 20px;
  }

  &__title {
    font-size: 24px;
    line-height: 36px;
    font-weight: 800;
    color: #333;
  }

  &__subtitle {
    margin-top: 6px;
    color: #666;
    font-size: 14px;

    .highlight {
      display: inline-block;
      color: #a8884c;
      font-weight: 500;
    }
  }

  &__summary {
    & + & {
      &::before {
        content: '，';
      }
    }
  }

  &__empty {
    margin: 30px 0;
    height: 160px;
    background: url('https://img01.yzcdn.cn/upload_files/2022/11/17/FjgwklhmxZx0qLQFslI8ww1g7JTx.png')
      no-repeat;
    background-size: auto 100%;
    background-position: 40px center;
  }

  &__actions {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    font-size: 16px;
    color: white;
    font-weight: 600;
  }
}

.items {
  margin: 30px 0;
}

.item {
  position: relative;
  display: flex;
  align-items: center;
  background: url('https://img01.yzcdn.cn/upload_files/2022/11/17/FqtHjPwSwxjg-XBiU756iVNgYXoK.png')
    no-repeat center;
  background-size: contain;
  height: 64px;

  & + & {
    margin-top: 14px;
  }

  &::after {
    content: '新人礼';
    position: absolute;
    padding: 2px 6px;
    top: -8px;
    right: 0;
    font-size: 10px;
    background-color: #e7d2a4;
    border-radius: 20px 20px 20px 0;
    color: #333;
  }

  &__info {
    flex: 1;
    padding-left: 18px;
  }

  &__icon {
    display: inline-block;
    flex: 0 0 75px;
    width: 36px;
    height: 36px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    &.present {
      background-image: url('https://img01.yzcdn.cn/upload_files/2022/12/15/FgxCwa9NkrsUqChjGwYVP-LNS6zv.png');
    }

    &.coupon {
      background-image: url('https://img01.yzcdn.cn/upload_files/2022/12/15/FpsuWrs6bTVQK1CPNbZkQACr-HkT.png');
    }

    &.points {
      background-image: url('https://img01.yzcdn.cn/upload_files/2022/12/15/Fj9-KFtB8F69IXuN6tiW00ZxuvRx.png');
    }
  }

  &__label,
  &__desc {
    max-width: 150px;
    @include ellipsis;
  }

  &__label {
    color: #333;
    font-size: 14px;
    font-weight: 600;
  }

  &__desc {
    margin-top: 4px;
    font-size: 12px;
    color: #666;
  }
}
