import WscComponent from 'pages/common/wsc-component';

const formatThresholdText = (
  couponData = {
    thresholdAmount: -1,
    thresholdCopywriting: '',
  }
) => {
  const { thresholdAmount, thresholdCopywriting } = couponData;
  const normalizedThreshold = Number(thresholdAmount);
  if (normalizedThreshold <= -1 || Number.isNaN(normalizedThreshold)) {
    return '';
  }
  if (normalizedThreshold === 0) {
    return '满任意金额可用';
  }
  return thresholdCopywriting;
};

const app = getApp();
WscComponent({
  options: {
    multipleSlots: true,
  },
  properties: {
    benefits: {
      type: Object,
    },
    memberConfig: {
      type: Object,
    },
  },

  data: {
    benefitNum: 0,
    coupons: [],
    couponsNum: 0,
    points: {},
    presents: [],
    tickets: [],
  },

  observers: {
    benefits(value) {
      const { presentList, couponList, points } = value ?? {};
      const benefitNum = Math.min(
        [presentList.length, couponList.length, points.points ? 1 : 0].reduce(
          (num, cur) => num + (cur ?? 0),
          0
        ),
        3
      );

      app.getPointsName().then(({ pointsName = '积分' }) => {
        const tickets = [];
        presentList.forEach((present) => {
          tickets.push({
            icon: 'present',
            label: present.presentName,
            desc: '',
          });
        });
        couponList.forEach((coupon) => {
          tickets.push({
            icon: 'coupon',
            label: `${coupon.number > 1 ? `${coupon.number}张` : ''}${
              coupon.couponCopyWritingStr
            }`,
            desc: formatThresholdText(coupon),
          });
        });
        if (points.points > 0) {
          tickets.push({
            icon: 'points',
            label: `${points.points ?? 0} ${pointsName}`,
            desc: '',
          });
        }
        this.setYZData({
          benefitNum,
          coupons: couponList ?? [],
          couponsNum: couponList.reduce((sum, c) => sum + (c.number || 0), 0),
          points: {
            value: points?.points ?? 0,
            unit: pointsName,
          },
          presents: presentList,
          tickets: tickets.slice(0, 2),
        });
      });
    },
  },
});
