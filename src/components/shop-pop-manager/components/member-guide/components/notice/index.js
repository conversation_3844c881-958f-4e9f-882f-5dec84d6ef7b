import navigate from '@/helpers/navigate';
import WscComponent from 'pages/common/wsc-component';

const ROUTE_MAP = {
  present: '/packages/ump/presents/index', // 赠品列表
  coupon: '/packages/user/coupon/list/index?type=promocard&title=我的优惠券', // 优惠券列表
  right: '/packages/shop/levelcenter/free/index', // 会员中心
  default: '/packages/shop/levelcenter/free/index',
};

WscComponent({
  options: {
    multipleSlots: true,
  },

  properties: {
    benefits: {
      type: Object,
    },
    memberConfig: {
      type: Object,
    },
  },

  data: {
    buttons: [],
    renderedBenefits: [],
  },

  methods: {
    handleConfirm(ev) {
      const url = ev?.target?.dataset?.url ?? ROUTE_MAP.default;
      navigate.navigate({ url });
    },
  },

  observers: {
    benefits({ presentList, couponList, points } = {}) {
      const [presentsNum, couponsNum, pointsNum] = [
        presentList?.reduce((acc, cur) => acc + (cur?.number || 0), 0),
        couponList?.reduce((acc, cur) => acc + (cur?.number || 0), 0),
        points?.points,
      ].map((item) => item ?? 0);

      const hasPresents = presentsNum > 0;
      const hasCoupons = couponsNum > 0;
      const hasPoints = pointsNum > 0;

      const buttons = [
        // 跳转赠品列表
        hasPresents && {
          label: '领取赠品',
          type: 'primary',
          url: ROUTE_MAP.present,
        },
        // 跳转优惠券列表
        hasCoupons && {
          label: '查看优惠券',
          type: hasPresents ? 'normal' : 'primary',
          url: ROUTE_MAP.coupon,
        },
        // 跳转会员中心
        !hasPresents &&
          !hasCoupons && {
            label: '查看权益',
            type: 'primary',
            url: ROUTE_MAP.right,
          },
      ].filter(Boolean);

      this.setYZData({
        buttons: buttons.slice(0, 2).reverse(),
        renderedBenefits: [
          hasPresents && {
            label: '赠品',
            icon: 'present',
            count: presentsNum,
          },
          hasCoupons && {
            label: '优惠券',
            icon: 'coupon',
            count: couponsNum,
          },
          hasPoints && {
            label: '积分',
            icon: 'points',
            count: pointsNum,
            operator: false,
          },
        ].filter(Boolean),
      });
    },
  },
});
