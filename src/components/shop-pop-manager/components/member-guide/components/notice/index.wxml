<view class="notice">
  <view class="head"><slot name="head"></slot></view>
  <view class="title">恭喜你成为{{memberConfig.name}}</view>
  <view class="subtitle">
    <block wx:if="{{ renderedBenefits.length > 0 }}">
      <view wx:for="{{renderedBenefits}}" wx:key="{{label}}" class="item">
        <text class="label">
          <text class="icon {{item.icon}}"></text>
          {{item.label}}
        </text>
        <text class="highlight" decode="{{true}}">
          <block wx:if="{{item.operator !== false}}">
            x&nbsp;
          </block>
          {{item.count}}
        </text>
      </view>
    </block>
  </view>

  <view class="actions {{ renderedBenefits.length >= 3 ? 'sm-bg' : ''}}" bindtap="handleConfirm">
    <block wx:for="{{buttons}}" wx:key="label">
      <view data-url="{{ item.url }}" class="action {{item.type}}">{{item.label}}</view>
    </block>
  </view>
  <text wx:if="{{ renderedBenefits.length > 1 }}" class="desc">可到{{memberConfig.name}}中心解锁更多权益</text>
</view>