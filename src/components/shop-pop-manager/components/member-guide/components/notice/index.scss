.notice {
  margin: 0 auto;
  padding: 12px 12px 18px 12px;
  background: url('https://img01.yzcdn.cn/upload_files/2022/12/26/Fuq-vqm_spGyiIf3oehU9PwK64lL.png!middle.png')
    no-repeat white;
  background-position: bottom center;
  background-size: cover;
  box-sizing: border-box;
  min-width: 270px;
  max-width: 290px;
  border-radius: 16px;
}

.head {
  text-align: right;
}

.title {
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  color: #333;
}

.subtitle {
  margin-top: 16px;
  font-size: 14px;
}

.highlight {
  color: #a8884c;
  font-weight: 500;
}

.item {
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  max-width: 44%;

  & + & {
    margin-top: 4px;
  }
}

.label {
  flex: 0 0 80%;
  color: #666;
  vertical-align: middle;
  text-align: left;
}

.icon {
  flex: 0 0 20%;
  display: inline-block;
  margin-right: 4px;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: -5px;

  &.present {
    background-image: url('https://img01.yzcdn.cn/upload_files/2022/11/30/Fp_8loyPDe9UpL5AfN4cmIFjEHGE.png');
  }

  &.coupon {
    background-image: url('https://img01.yzcdn.cn/upload_files/2022/11/30/FpV6iRe_UD9LXmkclWWJvyHubjl8.png');
  }

  &.points {
    background-image: url('https://img01.yzcdn.cn/upload_files/2022/11/30/FhYapbZ2qyrFEO-C1FVFlDGn1c7W.png');
  }
}

.actions {
  display: flex;
  margin-bottom: 14px;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-weight: 600;
  margin-top: 24px;
  padding: 0 12px;

  &.sm-bg {
    margin-top: 16px;
  }
}


.action {
  flex: 1;
  padding: 11px 0;
  border-radius: 40px;
  background-color: rgba(215, 215, 215, 0.6);
  max-width: 180px;
  color: #333;
  & + & {
    margin-left: 8px;
  }

  &.primary {
    background-color: #333;
    color: white;
    border: none;
  }
}

.desc {
  display: block;
  margin: 12px 0 2px;
  font-size: 10px;
  color: #999;
  text-align: center;
}
