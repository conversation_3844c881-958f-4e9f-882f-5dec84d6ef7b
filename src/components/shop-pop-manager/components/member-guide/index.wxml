<view wx:if="{{ visible }}">
  <van-popup show="{{ visible }}" z-index="9996" class="pop" bind:close="next" custom-style="background: transparent">
    <view class="member-guide_content {{ registryState }}">
      <block wx:if="{{registryState === 'success' || registryState === 'threshold'}}">
        <benefits-notice wx:if="{{ registryState === 'success' }}" benefits="{{ benefitData }}" member-config="{{ memberConfig }}">
          <view class="close" bindtap="next" slot="head"></view>
        </benefits-notice>
        <mem-registry-threshold wx:elif="{{ registryState === 'threshold' }}" threshold-type="{{memThresholdType}}" threshold-value="{{memThresholdValue}}">
          <view class="close" bindtap="next" slot="head"></view>
        </mem-registry-threshold>
      </block>
      <block wx:elif="{{!showDefault}}">
        <view class="action">
          <view class="close" bindtap="next" />
        </view>
        <view class="box" bindtap="handleGetReward">
          <image src="{{ url }}" class="image" mode="widthFix" />
        </view>
      </block>
      <block wx:else>
        <benefits benefits="{{ benefitData }}" member-config="{{ memberConfig }}">
          <view class="close" bindtap="next" slot="head"></view>
          <view slot="actions" class="benefits__confirm" bindtap="handleGetReward">
            <text class="accept">立即领取</text>
          </view>
        </benefits>
      </block>
    </view>
  </van-popup>
</view>