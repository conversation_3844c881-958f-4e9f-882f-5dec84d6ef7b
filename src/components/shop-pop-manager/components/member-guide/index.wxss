.action {
  height: 48rpx;
  padding-bottom: 32rpx;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  position: relative;
  margin-top: -32rpx;
}

.close {
  display: inline-block;
  width: 48rpx;
  height: 48rpx;
  background: url(https://img01.yzcdn.cn/upload_files/2022/11/18/Ftd1VOajNCaaTeOXgSO8yApC3cgx.png)
    no-repeat center;
  background-size: cover;
}

.box {
  text-align: center;
}

.image {
  max-width: 300px;
}

.member-guide_content {
  display: flex;
  width: 100vw;
  height: 100vh;
  flex-direction: column;
  justify-content: center;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: top center;
}

.success {
  background-image: url('https://img01.yzcdn.cn/upload_files/2022/12/16/FsAYDlGR5ggm7lnRTV0tTlDiYzMY.png!middle.png');
}

.benefits__confirm {
  width: 100%;
  text-align: center;
}

.accept {
  display: inline-block;
  padding: 10px 0;
  width: 92%;
  border-radius: 40px;
  background-color: #333;
}
