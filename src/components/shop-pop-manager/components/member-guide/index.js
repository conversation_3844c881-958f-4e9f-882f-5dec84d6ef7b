import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'utils/open-web-view';
import args from '@youzan/weapp-utils/lib/args';
import { LevelGroupType, RegistryStateEnum } from './constants';
import * as API from './api';
import closePopBehavior from '../behaviors/close-pop';
import loggerBehaviors from 'components/shop-pop-manager/behaviors/logger-behaviors';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
import { ThresholdType } from 'components/mem-registry-threshold/constants';
import getAuthorizedState from 'shared/utils/get-authorized-state';
import { openWechatCard } from 'utils/wechat-card';

const app = getApp();
const { logger } = app;
WscComponent({
  behaviors: [closePopBehavior, loggerBehaviors],
  data: {
    visible: false,
    url: '',
    benefitData: {},
    registryState: RegistryStateEnum.UNKNOWN,
    memThresholdType: ThresholdType.UNKNOWN,
    memThresholdValue: 0,
    isWechatCard: null,
    memberConfig: { name: '会员' },
  },

  methods: {
    showUserImage(url) {
      this.viewLog();
      this.setYZData({
        visible: true,
        url,
        showDefault: false,
      });
      this.onPopOpen();
    },

    viewLog() {
      logger &&
        logger.log({
          et: 'view', // 事件类型
          ei: 'view_member_poster', // 事件标识
          en: '会员弹窗曝光', // 事件名称
        });
    },

    next() {
      this.onClickClose();
    },
    getAdImageInfo() {
      return this.data.url;
    },

    goToFillPage() {
      const { isWechatCard } = this.data;
      if (isWechatCard) {
        return openWechatCard({}, () => this.next);
      }
      openWebView(
        args.add('/wscuser/levelcenter/simplified-fill', {
          kdt_id: getApp().getKdtId(),
          levelType: LevelGroupType.Free,
          fromScene: 'complete',
          jumpTo: 'Home',
          eT: Date.now(),
        }),
        { title: '完善信息' }
      );
    },

    handleGetReward() {
      getAuthorizedState()
        .then(({ mobile }) => {
          if (mobile) {
            API.checkMemRegistrationState().then(
              ({
                hasCondition = false,
                needFillInfo = false,
                conditionType: memThresholdType = ThresholdType.UNKNOWN,
                conditionValue: memThresholdValue = 0,
              } = {}) => {
                if (needFillInfo) {
                  this.goToFillPage();
                  return;
                }

                logger &&
                  logger.log({
                    et: 'click', // 事件类型
                    ei: 'click_member_poster', // 事件标识
                    en: '点击会员弹窗', // 事件名称
                  });

                this.setYZData({
                  registryState: hasCondition
                    ? RegistryStateEnum.THRESHOLD
                    : RegistryStateEnum.SUCCESS,
                  memThresholdType,
                  memThresholdValue,
                });
              }
            );
            logger &&
              logger.log({
                et: 'click',
                ei: 'Ad_Click',
                en: '图片广告打开次数',
                si: app.getKdtId(), // 店铺id，没有则置空
                params: {
                  component: 'popup_ad',
                  ...this.cloudLoggerInfo,
                },
              });
          } else {
            this.goToFillPage();
          }
        })
        .catch(() => {
          this.goToFillPage();
        });
    },

    showDefaultGuide() {
      API.getLevelGiftBag().then((data) => {
        this.viewLog();
        this.setYZData({
          showDefault: true,
          visible: true,
          benefitData: {
            couponList: data?.couponList || [],
            presentList: data?.presentList || [],
            points: data?.points || {},
          },
        });
        this.onPopOpen();
      });
    },
  },
  attached() {
    this.cloudLoggerInfo = getLoggerExtraParams('shop_ad_pop', {
      ...this.getCloudLoggerInfo('member_guide'),
    });
    API.getRedirectWechatCardPageInfo()
      .then((res) => {
        if (res) {
          this.data.isWechatCard = res && res.url;
        }
      })
      .catch(() => null);
    API.getMemberConfig().then((res) => {
      this.setYZData({
        memberConfig: res,
      });
    });
    API.showMemberGuide()
      .then(({ isPop = false, url = '', isDefaultUrl }) => {
        if (isPop) {
          if (!isDefaultUrl && url) {
            // 曝光商家自定义图片弹框
            this.showUserImage(url);
          } else {
            // 曝光默认会员引导弹框
            this.showDefaultGuide();
          }
          logger &&
            logger.log({
              et: 'view',
              ei: 'Ad_view',
              en: '图片广告曝光',
              si: app.getKdtId(),
              params: {
                component: 'popup_ad',
                ...this.cloudLoggerInfo,
              },
            });
        } else {
          this.onOtherClose();
        }
      })
      .catch(() => {
        this.onOtherClose();
      });
  },
});
