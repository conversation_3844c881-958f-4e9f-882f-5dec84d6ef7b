import { LevelGroupType } from './constants';

const app = getApp();

const requestWithTimeStamp = (opts = {}) => {
  opts.data = opts.data || {};
  opts.data.timeStamp = opts.data.timeStamp || Date.now();

  return app.request(opts);
};

// 判断是否需要展示等级进店引导
export const showMemberGuide = () => {
  return requestWithTimeStamp({
    path: '/wscuser/level/api/guide/homePageShowGuide.json',
  });
};

// 获取用户等级信息
export const getSimpleUserLevelInfo = (type = LevelGroupType.Free) => {
  return requestWithTimeStamp({
    path: '/wscuser/levelcenter/api/simpleUserLevelDetail.json',
    data: { type },
  });
};

// 获取免费等级1礼包
export const getLevelGiftBag = () =>
  requestWithTimeStamp({
    path: '/wscuser/level/api/getLevelGiftBag.json',
  });

export function checkMemRegistrationState() {
  return app.request({
    path: '/wscuser/level/api/checkMemRegistrationState.json',
  });
}

export function getRedirectWechatCardPageInfo() {
  return app.request({
    path: '/wscuser/levelcenter/fill/api/getRedirectWechatCardPageInfo',
  });
}

// 获取自定义会员名称
export function getMemberConfig() {
  return app.request({
    path: '/wscump/member-config.json',
  });
}
