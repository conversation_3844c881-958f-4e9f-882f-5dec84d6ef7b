import WscComponent from 'pages/common/wsc-component/index';
import fullfillImage from '@youzan/utils/url/fullfillImage';
import { getMarketingPageData } from '../../api';

WscComponent({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    detail: {
      type: Object,
      value: {},
    },
    activeUmpId: {
      type: Number,
      value: 0,
    },
  },
  data: {
    imageUrl: fullfillImage(
      'https://img01.yzcdn.cn/upload_files/2021/10/28/FtkIMD0ItzOrw7mSEI5yXbXQOzmZ.png',
      'large'
    ),
    btnImageUrl: fullfillImage(
      'https://img01.yzcdn.cn/upload_files/2021/10/28/FtgjCymAGEs-YxHzRMobwaPPMgVx.png',
      'large'
    ),
  },
  attached() {
    this.getMarketingTitleData();
  },

  methods: {
    onClose() {
      this.triggerEvent('close', {
        isClickClose: true,
      });
    },
    onGoMarketingPage() {
      this.triggerEvent('gomarketing');
      this.triggerEvent('close');
    },
    // 获取营销会场组件数据，用于取头图中的标题
    getMarketingTitleData() {
      // 添加 活动会场的微页面 id
      getMarketingPageData({ id: this.data.activeUmpId })
        .then((res) => {
          this.setYZData({
            detail: this.getMarketingPagePopData(res?.components),
          });
        })
        .catch(() => {
          this.setYZData({
            detail: this.getMarketingPagePopData(),
          });
        });
    },
    getMarketingPagePopData(components) {
      const popData = {
        title: '活动会场',
        subTitle: '全场优惠抢不停',
      };
      if (Array.isArray(components)) {
        components.forEach((element) => {
          if (element.type === 'venue_banner') {
            popData.title = element.title || '';
            popData.subTitle = element.subTitle || '';
          }
        });
      }
      return popData;
    },
  },
});
