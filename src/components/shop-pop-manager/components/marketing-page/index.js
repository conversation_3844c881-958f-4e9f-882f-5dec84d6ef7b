import WscComponent from 'pages/common/wsc-component/index';
import spm from 'shared/utils/spm';
import { getMarketingPageConfig } from './api';
import navigate from '@/helpers/navigate';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import closePopBehavior from '../behaviors/close-pop';
import loggerBehaviors from 'components/shop-pop-manager/behaviors/logger-behaviors';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';

const app = getApp();
const { logger } = app;

WscComponent({
  behaviors: [closePopBehavior, loggerBehaviors],
  properties: {
    // 页面来源 1—店铺主页 2—商详页 3—社区团购活动页 4-微页面
    source: {
      type: Number,
      value: null,
    },
    activeUmpId: {
      type: Number,
      value: 0,
    },
  },

  data: {
    visible: false,
    detail: {},
  },

  // 挂载时触发请求营销会场配置
  attached() {
    this.cloudLoggerInfo = getLoggerExtraParams('shop_ad_pop', {
      ...this.getCloudLoggerInfo('ump_conference'),
    });
    this.getMarketingConfig();
  },
  methods: {
    getMarketingConfig() {
      getMarketingPageConfig()
        .then((res) => {
          if (res?.bomb_window_enable === '1') {
            this.setYZData({
              visible: true,
            });
            this.onPopOpen();
            // 曝光埋点
            logger &&
              logger.log({
                et: 'view', // 事件类型
                ei: 'component_view', // 事件标识
                en: '组件曝光', // 事件名称
                params: {
                  component: 'popup_ad',
                  banner_id: this.getBannerId(),
                }, // 事件参数
              });
            // 广告曝光
            logger &&
              logger.log({
                et: 'view',
                ei: 'Ad_view',
                en: '图片广告曝光',
                si: app.getKdtId(),
                params: {
                  component: 'popup_ad',
                  ...this.cloudLoggerInfo,
                },
              });
          } else {
            this.onOtherClose();
          }
        })
        .catch((error) => {
          console.error('获取营销会场入口配置失败', error);
          this.onOtherClose();
        });
    },
    getAdImageInfo() {
      return 'https://img01.yzcdn.cn/upload_files/2021/05/31/FmpGwix9FOK03HKKGxtM9TnGRrJM.gif';
    },
    onGoMarketingPage() {
      logger &&
        logger.log({
          et: 'click',
          ei: 'Ad_Click',
          en: '图片广告打开次数',
          si: app.getKdtId(), // 店铺id，没有则置空
          params: {
            component: 'popup_ad',
            ...this.cloudLoggerInfo,
          },
        });
      // add marketing id /packages/ext-marketing-page/index?id = xxxx
      let url = '/packages/ext-marketing-page/index';
      if (this.data.activeUmpId) {
        url = `${url}?id=${this.data.activeUmpId}`;
      }
      navigate.navigate({
        url,
      });
    },
    closeMarketingPagePop(e) {
      const { isClickClose } = e.detail || {};
      if (isClickClose) {
        this.onClickClose();
      } else {
        this.onOtherClose();
      }
    },
    getBannerId() {
      return `${spm.getPageSpmTypeId()}~popup_ad.0~0~${makeRandomString(8)}`;
    },
  },
});
