<view wx:if="{{ show && couponList.length > 0 }}">
  <van-popup show="{{ show && couponList.length > 0 }}" z-index="10000" class="customer-rebuy__pop" bind:close="handleClosePop" custom-style="background: transparent">
    <view class="customer-rebuy__wrapper">
      <view class="container-layer">
        <view class="customer-rebuy__container">
          <view class="customer-rebuy__title">你有 {{ couponList.length }} 张优惠券！</view>
          <view class="customer-rebuy__meta">请尽快享用哦～</view>
          <view class="coupon__list">
            <view wx:for="{{ couponList }}" class="coupon__item">
              <view class="coupon__amount">
                <view class="coupon__amount-content">
                  <view class="coupon__amount-num" style="font-size:{{ item.moneyFontSize }}rpx">
                    {{ item.amount }}
                  </view>
                  <view class="coupon__amount-suffix">{{ item.moneySuffix }}元</view>
                </view>
              </view>
              <view class="coupon__info">
                <view class="coupon__info-type">{{ item.title }}</view>
                <view class="coupon__info-desc">满 {{ item.condition}} 元使用</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 底部区域：按钮 -->
      <view class="confirm-action__container">
        <view class="confirm-action" bind:tap="handleClosePop">去享用吧</view>
      </view>
      <view class="close-action" bind:tap="handleClosePop"></view>
    </view>
  </van-popup>
</view>