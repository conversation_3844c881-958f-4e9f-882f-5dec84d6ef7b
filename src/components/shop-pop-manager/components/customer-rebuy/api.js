const app = getApp();

const basePath = '/retail/scrm/api/';

/**
 * 曝光数据上报，后端用于统计数据
 * @returns {Boolean}
 */
export const behaviorReport = async ({ extraParams }) =>
  app.request({
    path: `${basePath}behaviorReport.json`,
    data: {
      scene: 'member_repurchase_coupon',
      extraParams,
    },
    method: 'POST',
  });

/**
 * 获取老客复购优惠券列表
 * @typedef {Object} CouponItem
 * @property {number} condition - 使用门槛 0：无门槛 n：门槛值 （单位：分）
 * @property {string} alias - 优惠券别名
 * @property {number} denominations - 优惠面额（单位：分）
 * @property {string} title - 优惠券标题
 * @typedef {Object} IQueryCustomerCouponTipsResponse
 * @property {number} userId
 * @property {number} planId 计划的id
 * @property {CouponItem[]} couponTipList
 * @returns {Promise<IQueryCustomerCouponTipsResponse>}
 *
 */
export const getUserCouponTips = async () =>
  app.request({
    path: `${basePath}getUserCouponTips.json`,
    method: 'GET',
  });
