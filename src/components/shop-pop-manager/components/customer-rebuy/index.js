import WscComponent from 'pages/common/wsc-component/index';
import { behaviorReport, getUserCouponTips } from './api';
import {
  queryWechatSubscribeResult,
  getTemplateByScene,
  requestSubscribeMessage,
} from 'utils/subscribe-message';

const MemberRepurchaseScene = 'retial_coupon_notice_scene';
export const SOURCE_LOG_MAP = {
  1: '首页',
  2: '商详页',
  3: '社区团购',
  4: '微页面',
};
const moneyFormatLimit = 10000;
const zoomOutNum = 100;
const formatMoney2Str = (rawMoneyWithZoomOut) => {
  const rawMoney = rawMoneyWithZoomOut / zoomOutNum;

  return rawMoney >= moneyFormatLimit
    ? (rawMoney | 0) / moneyFormatLimit
    : rawMoney;
};
// 返回的数字是设计稿的字体大小
const getMoneyFontSize = (money) => {
  const num =
    money >= moneyFormatLimit
      ? String((money | 0) / moneyFormatLimit).length
      : String(money).length;
  if (num >= 5) {
    return 22;
  }
  if (num >= 4) {
    return 38;
  }

  if (num >= 3) {
    return 56;
  }

  return 60;
};

WscComponent({
  properties: {
    show: {
      type: Boolean,
      value: true,
    },
    // 页面来源 1—店铺主页 2—商详页 3—社区团购活动页 4-微页面
    source: {
      type: Number,
      value: null,
    },
  },
  data: {
    showSubscribeMask: false,
    couponList: [],
    suffix: '',
  },

  attached() {
    this.initCouponData();
  },

  methods: {
    initCouponData() {
      getUserCouponTips()
        .then(async (res) => {
          this.setYZData({
            couponList: res.couponTipList.map((item) => ({
              ...item,
              condition: item.condition / zoomOutNum,
              amount: formatMoney2Str(item.denominations),
              moneySuffix:
                item.denominations / zoomOutNum >= moneyFormatLimit ? '万' : '',
              moneyFontSize: getMoneyFontSize(item.denominations / zoomOutNum),
            })),
          });

          if (this.data.show && res.couponTipList.length > 0) {
            behaviorReport({
              extraParams: {
                plan_id: res.planId,
              },
            }).catch((err) => {
              console.log(err, 'error');
            });
            this._templateIdList = await this.getTemplateIdList();
          }
          if (!res.couponTipList.length) {
            this.triggerEvent('close-pop', {
              currentShow: false,
              isRealClose: true,
            });
          }
        })
        .catch((err) => {
          console.log(err, 'error');
        });
    },
    toggleSubscribeMask(e) {
      // 开关订阅蒙层
      this.triggerEvent('toggle-subscribe-mask', e.detail);
    },
    async getTemplateIdList() {
      let templateIdList = [];
      try {
        const [
          { templateIdList: sceneTemplateIdList = [] },
          { templateList: pageIdTemplateIdList = [] },
        ] = await Promise.all([
          getTemplateByScene(MemberRepurchaseScene),
          queryWechatSubscribeResult(4),
        ]);
        const formattedSceneTemplateIdList = sceneTemplateIdList.map(
          (templateId) => {
            return {
              templateId,
            };
          }
        );
        templateIdList = [
          ...formattedSceneTemplateIdList,
          ...pageIdTemplateIdList,
        ];
      } catch (error) {
        templateIdList = [];
      }
      return templateIdList;
    },
    // 关闭弹窗
    async handleClosePop() {
      requestSubscribeMessage({
        templates:
          this._templateIdList.length === 0
            ? await this.getTemplateIdList()
            : this._templateIdList,

        next: () => {
          this.triggerEvent('close-pop', {
            currentShow: false,
            isRealClose: true,
          });
        },
        onShowTips: () => {
          // 开订阅蒙层
          this.toggleSubscribeMask({ detail: true });
        },
        onCloseTips: () => {
          // 关订阅蒙层
          this.toggleSubscribeMask({ detail: false });

          this.triggerEvent('close-pop', {
            currentShow: false,
            isRealClose: true,
          });
        },
        onFail: () => {
          // 关订阅蒙层
          this.toggleSubscribeMask({ detail: false });

          this.triggerEvent('close-pop', {
            currentShow: false,
            isRealClose: true,
          });
        },
      });
    },
  },
});
