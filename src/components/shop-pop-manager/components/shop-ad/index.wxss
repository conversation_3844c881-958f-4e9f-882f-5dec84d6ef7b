.ad-popup {
  width: 100%;
  height: 100%;
  background: transparent;
  overflow: hidden;
}

.close-icon {
  position: absolute;
  width: 29px;
  height: 29px;
  background-image: url(https://img01.yzcdn.cn/weapp/wsc/B2MVjv.png);
  background-repeat: no-repeat;
  background-size: 29px;
  left: 50%;
  bottom: -66px;
  transform: translateX(-50%);
}
/* 覆盖 button 上的默认样式 */
.ad-button {
  background: transparent;
  margin: 0;
  padding: 0;
}
.ad-button::after {
  border: none !important;
}
.hotare-view-container {
  position: relative;
}
.hotare-view {
  position: absolute;
}
.dots {
  margin-top: 32rpx;
  display: flex;
  justify-content: center;
  gap: 5px;
}
.dots .dot {
  width: 8rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
}
.dots .active-dot {
  width: 8rpx;
  height: 8rpx;
  background: #fff;
  border-radius: 50%;
}
