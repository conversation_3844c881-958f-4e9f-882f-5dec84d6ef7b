import navigate from 'shared/utils/navigate';
import WscComponent from 'pages/common/wsc-component/index';
import jumpToLink from 'shared/components/showcase/utils/jumpToLink';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { getCurrentUserShopAd, closeCurrentAdPop } from './api';
import closePopBehavior from '../behaviors/close-pop';
import loggerBehaviors from 'components/shop-pop-manager/behaviors/logger-behaviors';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
import { getSystemInfoSync } from '@youzan/tee-api';

const app = getApp();
const { logger } = app;

WscComponent({
  behaviors: [closePopBehavior, loggerBehaviors],
  properties: {
    // 页面来源 1—店铺主页 2—商详页 3—社区团购活动页
    popPosition: {
      type: Number,
      value: 1,
    },
    featureId: Number,
    targetKdtId: Number,
  },

  data: {
    adImage: '',
    adLinkType: '',
    adId: 0,
    visible: false,
    imageStyle: {},
    slideStyleInfo: {
      swipeStyle: '',
      swipePreOrNextMargin: '0rpx',
    },
    closeIconStyle: {},
    adResource: {},
    isImLink: false,
    isSwipe: false,
    isSlide: false,
    autoPlay: 1,
    swipeResource: [],
    swipeTime: 3000,
    im: {
      sourceParam: '',
      businessId: '',
    },
    isShowHotArea: false,
    swipeIndex: 0,
  },

  methods: {
    onContactBack: navigate.contactBack,
    closeAdPop() {
      this.onClickClose();
      this.handleCloseAdpop();
    },
    getAdImageInfo() {
      return this.data.adImage;
    },
    handleSwiperChange(e) {
      const { current } = e.detail;
      this.setYZData({
        swipeIndex: current,
      });
    },
    handleCloseAdpop() {
      const { adId } = this.data;
      closeCurrentAdPop(adId)
        .then((success) => {
          if (!success) throw new Error('关闭广告弹窗失败');
        })
        .catch((err) => {
          throw new Error(`关闭广告弹窗失败, ${err}`);
        });
    },
    handleClickAdPop(e) {
      const { isSwipe, swipeData } = e?.target?.dataset || {};
      if (isSwipe) {
        this.handleAreClick({ target: { dataset: { area: swipeData } } });
        return;
      }
      const { adLinkType, adResource } = this.data;
      this.logger();
      // 进入图片广告链接的同时，关闭广告

      this.onOtherClose(true);
      this.handleCloseAdpop();

      jumpToLink(adLinkType, adResource);
    },
    handleAreClick(e) {
      const { area } = e.target.dataset;
      const { link_type: adLinkType } = area;

      const handleCloseAdpop = () => {
        this.onOtherClose(true);
        this.handleCloseAdpop();
      };

      this.logger();

      handleCloseAdpop();

      jumpToLink(adLinkType, area);
    },
    getHotAreaData(shopAd, opt = {}) {
      const { isSlide } = opt;
      const { screenWidth = 300 } = getSystemInfoSync();
      // 根据 rpx 换算图片的真实宽度
      const displayWidth = Math.floor(
        (isSlide ? 525 : 600) * (screenWidth / 750)
      );
      const hotImgItem = shopAd;
      const rate = displayWidth / hotImgItem.image_width;

      hotImgItem.hot_areas = hotImgItem.hot_areas || [];
      const hotArea = hotImgItem.hot_areas;
      for (let i = 0; i < hotArea.length; i++) {
        // 加上关闭按钮的高度
        hotArea[i].top = Math.floor(Number(hotArea[i].start_y) * rate);
        hotArea[i].left =
          Math.floor(Number(hotArea[i].start_x) * rate) + (isSlide ? 8 : 0);
        hotArea[i].width = Math.floor(
          (Number(hotArea[i].end_x) - Number(hotArea[i].start_x)) * rate
        );
        hotArea[i].height = Math.floor(
          (Number(hotArea[i].end_y) - Number(hotArea[i].start_y)) * rate
        );
      }

      return hotImgItem;
    },
    logger(params = {}) {
      logger &&
        logger.log({
          et: 'click',
          ei: 'Ad_Click',
          en: '图片广告打开次数',
          si: app.getKdtId(), // 店铺id，没有则置空
          params: {
            component: 'popup_ad',
            ad_id: this.data.adId,
            ...this.cloudLoggerInfo,
            ...params.params,
          },
          ...params,
        });
    },
  },

  attached() {
    this.cloudLoggerInfo = getLoggerExtraParams('shop_ad_pop', {
      ...this.getCloudLoggerInfo('pop_ad'),
    });
    app.waitForEnterShop().then(() => {
      const { popPosition } = this.data;
      const params = { popPosition };
      // 指定店铺页面
      const { targetKdtId, featureId } = this.properties;
      if (popPosition === 2 && featureId) {
        params.featureId = featureId;
      }
      if (targetKdtId) {
        params.kdt_id = targetKdtId;
      }

      getCurrentUserShopAd(params)
        .then((adData) => {
          const adId = adData && adData.id;
          if (!adId) {
            return this.onOtherClose();
          }

          const shopAd = JSON.parse(adData.resource);
          const { swipeTime, imageCount, swipeResource, autoPlay = 1 } = shopAd;
          const hotImgItem = this.getHotAreaData(shopAd);

          let imageStyle = `width: 600rpx; height: ${
            (shopAd.image_height / shopAd.image_width) * 600
          }rpx`;
          let isShowHotArea = false;
          const isImLink = shopAd.weapp_link_type === 'chat';
          const hotAreaImLink = shopAd?.hot_areas.some((item) => {
            return item.link_type === 'chat';
          });

          if (shopAd?.hot_areas && shopAd?.hot_areas.length) {
            isShowHotArea = true;
          }

          // 以下开始处理轮播相关
          const isSwipe = +imageCount === 2;
          const isSlide = +imageCount === 3;
          let swipeImages = [];
          const { slideStyleInfo } = this.data;

          // 有layerUrl代表是AI生成，需要提高清晰度
          const getCdnImageUrl = (item) => {
            return cdnImage(
              item.image_url,
              `!730x0${item.is_ai_image ? 'q90' : ''}.jpg`
            );
          };
          if (isSwipe || isSlide) {
            // todo 处理热区数据等
            // 获取第一张图片的宽高
            const firstSwipeImg = swipeResource[0];
            const { image_width: width, image_height: height } = firstSwipeImg;
            imageStyle = isSlide
              ? `width: 525rpx; height: ${
                  (height / width) * 525
                }rpx; margin: 0 16rpx`
              : `width: 600rpx; height: ${(height / width) * 600}rpx;`;
            slideStyleInfo.swipePreOrNextMargin = isSlide ? '96.5rpx' : '0rpx';
            slideStyleInfo.swipeStyle = `width: 100vw; height: ${
              (height / width) * 525
            }rpx`;
            swipeImages = swipeResource.map((item) => {
              const hotareas =
                this.getHotAreaData(item, {
                  isSlide,
                }).hot_areas || [];
              return {
                ...item,
                isHotArea: hotareas.length > 0,
                image_url: getCdnImageUrl(item),
              };
            });
          }

          // todo 处理客服

          this.setYZData({
            adImage: getCdnImageUrl(shopAd),
            adLinkType: shopAd.weapp_link_type || shopAd.link_type,
            adId,
            visible: true,
            imageStyle,
            adResource: hotImgItem,
            isImLink,
            isShowHotArea,
            isSwipe,
            swipeTime,
            isSlide,
            slideStyleInfo,
            autoPlay,
            swipeResource: swipeImages,
          });
          this.onPopOpen();

          const hasSwiperImLink =
            isSwipe || isSlide
              ? (swipeResource || []).some((i) => {
                  if (i.link_type === 'chat') {
                    return true;
                  }

                  if (i.hot_areas?.length) {
                    return i.hot_areas.some((item) => {
                      return item.link_type === 'chat';
                    });
                  }
                })
              : false;

          // 设置客服跳转数据
          if (isImLink || hotAreaImLink || hasSwiperImLink) {
            app.getDefaultImData().then((im) => {
              this.setYZData({ im });
            });
          }

          // 查看广告埋点
          this.logger({
            et: 'view',
            ei: 'Ad_view',
            en: '图片广告曝光',
          });
        })
        .catch((err) => {
          this.onOtherClose();
          console.error(err);
        });
    });
  },
});
