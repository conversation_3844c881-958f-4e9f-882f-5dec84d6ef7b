const app = getApp();

const getCurrentUserShopAd = (data) => {
  const noCacheFlag = Date.now();
  return app.request({
    path: '/wscshop/shopad/current-ad.json',
    data: {
      noCacheFlag,
      ...data,
    },
  });
};

const closeCurrentAdPop = (adId) => {
  const noCacheFlag = Date.now();
  return app.request({
    path: '/wscshop/shopad/close.json',
    data: {
      adId,
      noCacheFlag,
    },
  });
};

export { getCurrentUserShopAd, closeCurrentAdPop };
