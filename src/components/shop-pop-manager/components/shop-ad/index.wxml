<template name="ad-hot-area">
  <image src="{{ adImage }}" class="ad-image" mode="{{ (isSwipe || isSlide) ? 'aspectFill' : '' }}" style="{{ imageStyle }}" />
  <view wx:for="{{hotAreas}}" wx:for-item="area">
    <view class="hotare-view" wx:if="{{area.link_type !== 'chat'}}" bind:tap="handleAreClick" data-area="{{area}}" style="width:{{area.width}}px;height:{{area.height}}px;top:{{area.top}}px;left:{{area.left}}px"></view>
    <view class="hotare-view" wx:else style="width:{{area.width}}px;height:{{area.height}}px;top:{{area.top}}px;left:{{area.left}}px">
      <message-contact contact-class="ad-button" open-type="contact" bind:contact="onContactBack" session-from="{{ im.sourceParam }}" business-id="{{ im.businessId }}">
        <view style="width:{{area.width}}px;height:{{area.height}}px"></view>
      </message-contact>
    </view>
  </view>
</template>
<template name="ad-image">
  <image wx:if="{{ !isImLink }}" src="{{ adImage }}" mode="{{ (isSwipe || isSlide) ? 'aspectFill' : '' }}" data-is-swipe="{{ isSwipe || isSlide }}" data-swipe-data="{{ swipeItemData }}" class="ad-image" bind:tap="handleClickAdPop" style="{{ imageStyle }}" />
  <message-contact wx:else contact-class="ad-button" open-type="contact" bind:contact="onContactBack" session-from="{{ im.sourceParam }}" business-id="{{ im.businessId }}">
    <image src="{{ adImage }}" class="ad-image" style="{{ imageStyle }}" />
  </message-contact>
</template>
<view wx:if="{{ adImage || isSwipe || isSlide }}">
  <van-popup show="{{ visible }}" z-index="170" class="ad-popup" bind:close="closeAdPop" custom-style="background: transparent;overflow-y: unset;">
    <view class="close-icon" bind:tap="closeAdPop" />
    <view wx:if="{{isSwipe || isSlide }}">
      <swiper bindchange="handleSwiperChange" style="{{ isSlide ? slideStyleInfo.swipeStyle : imageStyle }}" indicator-dots="{{ !isSlide }}" previous-margin="{{ slideStyleInfo.swipePreOrNextMargin }}" next-margin="{{ slideStyleInfo.swipePreOrNextMargin }}" circular="{{ true }}" autoplay="{{ isSlide && autoPlay !== 1 ? false : true }}" interval="{{ swipeTime }}" duration="{{500}}">
        <block wx:for="{{swipeResource}}" wx:for-item="item">
          <swiper-item>
            <template is="ad-image" wx:if="{{ !item.isHotArea }}" data="{{ isSwipe, isSlide, isImLink: item.link_type === 'chat', adImage: item.image_url, imageStyle, im, swipeItemData: item }}"></template>
            <template is="ad-hot-area" wx:else data="{{ isSwipe, isSlide, adImage: item.image_url, imageStyle, im, hotAreas: item.hot_areas }}"></template>
          </swiper-item>
        </block>
      </swiper>
      <view wx:if="{{ isSlide }}" class="dots">
        <block wx:for="{{swipeResource}}" wx:for-item="item" wx:for-index="index">
          <view class="{{ swipeIndex === index ? 'active-dot' : 'dot' }}" />
        </block>
      </view>
    </view>
    <view class="hotare-view-container" wx:elif="{{isShowHotArea}}">
      <template is="ad-hot-area" data="{{ isSwipe, isSlide, isImLink, adImage, imageStyle, hotAreas: adResource.hot_areas, im }}"></template>
    </view>
    <view wx:else>
      <template is="ad-image" data="{{ isSwipe, isSlide, isImLink, adImage, imageStyle, im }}"></template>
    </view>
  </van-popup>
</view>