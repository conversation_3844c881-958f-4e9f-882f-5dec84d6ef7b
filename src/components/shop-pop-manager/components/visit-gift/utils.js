import { moment as formatDate } from '@youzan/weapp-utils/lib/time';
import { ABSOLUTE_DATE_TYPE, PREFERENTIAL_MODE } from './constants';

export const SHOW_TYPE = {
  SELF: 0, // 自定义样式
  ONE: 1, // 样式1
  TWO: 2, // 样式2
  THREE: 3, // 样式3
  FOUR: 4, // 样式4
  ERR: 99, // 异常弹窗
};

const getCouponValue = (coupon) => {
  let value;
  let unit = '';

  // eslint-disable-next-line default-case
  switch (coupon.preferentialMode) {
    // 代金券（满减及随机）
    case PREFERENTIAL_MODE.MONEY:
      value = coupon.value
        ? coupon.value / 100
        : `${coupon.minValue / 100}~${coupon.maxValue / 100}`;
      unit = '元';
      break;
    // 折扣券
    case PREFERENTIAL_MODE.DISCOUNT:
      value = coupon.value / 10;
      unit = '折';
      break;
    // 兑换券
    case PREFERENTIAL_MODE.GOODS:
      value = '兑换券';
      break;
  }
  return {
    value,
    unit,
  };
};

const getValueSize = (val, preferentialMode) => {
  const value = String(val);
  const len = value.length;
  // 商品兑换券
  if (preferentialMode === PREFERENTIAL_MODE.GOODS) {
    return 22;
  }
  if (len < 5) {
    return 28;
  }
  if (len === 5) {
    return 22;
  }
  if (len === 6) {
    return 18;
  }
  return 12;
};

const formatCouponList = (list) => {
  return list.map((coupon) => {
    const {
      couponId,
      title,
      activityType,
      preferentialMode,
      thresholdCopyWriting,
      dateType,
      fetchId,
      activityTypeGroup,
      validTimeCopywriting,
    } = coupon;
    const { value, unit } = getCouponValue(coupon);
    const size = getValueSize(value, preferentialMode);
    let time = '';

    if (dateType === ABSOLUTE_DATE_TYPE) {
      // 固定日期
      time = `${formatDate(coupon.validStartTime, 'YYYY.MM.DD')}-${formatDate(
        coupon.validEndTime,
        'YYYY.MM.DD'
      )}`;
    } else {
      // 相对有效期
      time = `领到券${
        coupon.relativeValidTimeBeginInterval === 0 ? '当' : '次'
      }日起${coupon.relativeValidTimeDuration}天内有效`;
    }

    return {
      value,
      unit,
      couponId,
      fetchId,
      title,
      activityType,
      condition: thresholdCopyWriting,
      time: validTimeCopywriting || time,
      size,
      activityTypeGroup,
    };
  });
};

// 判断是否含除满减券之外的券
const hasOther = (arr) => arr.some((v) => v.preferentialMode !== 1);
// 获得总金额
const getTotalMoney = (list) => {
  let totalMoney = 0;
  list.forEach((v) => {
    totalMoney += v.value || v.maxValue;
  });

  return totalMoney / 100;
};

const CODE_MAP = {
  1000030029: 'FETCHED',
  1000030071: 'FETCHED',
  1000030072: 'ASYNC',
};

const ERR_POP_DETAIL = {
  FETCHED: {
    title: '你已领过该礼包',
    btnText: '查看详情',
  },
  ASYNC: {
    title: '送你一个优惠礼包',
    desc: '请在个人中心优惠券列表查看',
    btnText: '立即查看',
  },
  FAIL: {
    title: '来晚一步，抢光了',
  },
};

export const APP_TYPE_STR = {
  10007: '新人',
  10008: '进店',
};

const APP_TYPE = {
  NEW: 10007,
  OLD: 10008,
};

const getTitles = (fetchedCoupons, payCoupons, appType) => {
  const hasFetch = fetchedCoupons.length;
  const payCouponLen = payCoupons.length;
  let title = '';
  let subTitle = '';
  let totalMoney = 0;

  if (hasFetch) {
    const fetchedHasOtherCoupon = hasOther(fetchedCoupons);
    totalMoney = fetchedHasOtherCoupon ? 0 : getTotalMoney(fetchedCoupons);
    title = fetchedHasOtherCoupon
      ? `送你${APP_TYPE_STR[appType]}红包`
      : `送你${totalMoney}元红包`;
  }

  if (payCouponLen) {
    const fetchText = hasFetch ? '再' : '';
    const preText = `购买消费后${fetchText}送`;
    const payHasOtherCoupon = hasOther(payCoupons);
    const payTitle = payHasOtherCoupon
      ? `${preText}${payCouponLen}张券`
      : `${preText}${getTotalMoney(payCoupons)}元`;

    title = !hasFetch ? payTitle : title;
    subTitle = !hasFetch ? '' : payTitle;
  }

  return {
    title,
    subTitle,
    totalMoney,
  };
};

export const formatPopData = (res, kdtId, errCode) => {
  // 异常情况
  if (errCode || !res) {
    const key = CODE_MAP[errCode] || 'FAIL';
    const detail = ERR_POP_DETAIL[key];
    detail.showType = SHOW_TYPE.ERR;

    if (detail.btnText) {
      const link = '/packages/user/coupon/list/index';
      detail.link = link;
    }

    return detail;
  }

  // 领取成功
  const { activityInfo, sendImmediatelyCoupons, sendAfterPayCoupons } = res;
  const {
    shopName,
    showType = SHOW_TYPE.ONE,
    imageUrl,
    color,
    appIdentity,
    participateFrequency,
  } = activityInfo;
  const { title, subTitle, totalMoney } = getTitles(
    sendImmediatelyCoupons,
    sendAfterPayCoupons,
    appIdentity
  );

  const fetchedCoupons = formatCouponList(sendImmediatelyCoupons);
  const payCoupons = formatCouponList(sendAfterPayCoupons);
  const style = showType === SHOW_TYPE.SELF ? `background: ${color};` : '';
  const titleClass =
    !fetchedCoupons.length && showType === SHOW_TYPE.THREE ? 'small-title' : '';
  const typeRule =
    appIdentity === APP_TYPE.NEW
      ? '新客进店礼包在首次进店时发放，'
      : `老客进店礼包每${participateFrequency}天进店时发放`;

  return {
    title,
    fetchedCoupons,
    subTitle,
    payCoupons,
    kdtId,
    showType,
    imageUrl,
    color,
    style,
    hasCoupon: true,
    titleClass,
    useSelfStyle: showType === SHOW_TYPE.SELF,
    shopName,
    typeRule,
    totalMoney,
  };
};
