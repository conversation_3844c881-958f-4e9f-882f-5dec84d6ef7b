import WscComponent from 'pages/common/wsc-component/index';
import IconController from 'components/showcase/components/floating-nav/icon-controller/index';
import { formatPopData } from './utils';
import { sendCoupons } from './api';
import { ERR_CODE } from './constants';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
import loggerBehaviors from 'components/shop-pop-manager/behaviors/logger-behaviors';

const app = getApp();
const { logger } = app;
WscComponent({
  behaviors: [loggerBehaviors],
  properties: {
    // 页面来源 1—店铺主页 2—商详页 3—社区团购活动页 4-微页面
    source: {
      type: Number,
      value: null,
    },
  },

  data: {
    showGiftPop: false,
    showCouponIcon: false,
    ruleVisible: false,
    detail: {},
  },

  // 挂载时触发请求进店礼
  attached() {
    this.cloudLoggerInfo = getLoggerExtraParams('shop_ad_pop', {
      ...this.getCloudLoggerInfo('enter_shop'),
    });
    this.joinVisitGift();
  },
  detached() {
    this.iconController && this.iconController.destroy(this);
  },
  methods: {
    getAdImageInfo() {
      return '';
    },
    sendViewLogger() {
      // 曝光埋点
      logger &&
        logger.log({
          et: 'view',
          ei: 'Ad_view',
          en: '图片广告曝光',
          si: app.getKdtId(),
          params: {
            component: 'popup_ad',
            ...this.cloudLoggerInfo,
          },
        });
    },
    joinVisitGift(afterLogin = false) {
      const { source } = this.data;
      const kdtId = app.getKdtId();
      sendCoupons(source)
        .then((res) => {
          const detail = formatPopData(res, kdtId);
          this.setYZData({
            showGiftPop: true,
            detail,
          });
          this.triggerEvent('pop-open');
          this.sendViewLogger();
        })
        .catch(({ code }) => {
          // 异步发券
          if (afterLogin || code === ERR_CODE.ASYNC_SEND) {
            const detail = formatPopData({}, kdtId, code);
            this.setYZData({
              showGiftPop: true,
              detail,
            });
            this.triggerEvent('pop-open');
            this.sendViewLogger();
            return;
          }

          // 需要绑定手机号
          if (code === ERR_CODE.BIND_MOBILE) {
            this.setYZData({
              showCouponIcon: true,
            });
            this.iconController = new IconController()?.setIcon(this, {
              priority: 70,
              height: 48,
              cb: [
                (bottom) => {
                  this.setYZData({ bottom: bottom + 'px' });
                },
                (goaway) => {
                  this.setYZData({ goaway });
                },
              ],
            });
          }
          // 触发下一个弹窗
          this.triggerEvent('close-pop', {
            currentShow: this.data.showCouponIcon,
          });
        });
    },

    closeVisitGift(e, fromJump = false) {
      this.setYZData({
        showGiftPop: false,
      });
      // 触发下一个弹窗
      if (!fromJump) {
        this.triggerEvent('close-pop', {
          currentShow: false,
          isRealClose: true,
        });
      }
    },

    showRule(e) {
      this.setYZData({
        ruleVisible: e.detail,
      });
    },

    handleLogin() {
      this.setYZData({
        showCouponIcon: false,
      });
      this.joinVisitGift(true);
    },

    toggleSubscribeMask(e) {
      // 开关订阅蒙层
      this.triggerEvent('toggle-subscribe-mask', e.detail);
    },
  },
});
