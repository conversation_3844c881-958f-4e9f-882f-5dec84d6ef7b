<view class="visit-gift-container">
  <!-- 进店礼小图标 需要绑定手机号时使用 -->
  <user-authorize
    wx:if="{{ showCouponIcon }}"
    authTypeList="{{ ['mobile'] }}"
    bind:next="handleLogin"
  >
    <view class="icon__send-coupon" style="{{ bottom ? 'bottom:' + bottom : '' }}"></view>
  </user-authorize>

  <!-- 活动弹窗 -->
  <gift-pop
    show="{{ showGiftPop }}"
    detail="{{ detail }}"
    bind:close="closeVisitGift"
    bind:toggleRules="showRule"
    bind:toggle-subscribe-mask="toggleSubscribeMask"
    source="{{ source }}"
  />

  <rule-pop
    show="{{ ruleVisible }}"
    shop-name="{{ detail.shopName }}"
    type-rule="{{ detail.typeRule }}"
    bind:toggleRules="showRule"
  />
</div>