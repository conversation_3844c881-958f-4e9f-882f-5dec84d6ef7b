<van-popup
  show="{{ show }}"
  round
  position="bottom"
  title="活动规则"
  custom-class="rules-popup"
  z-index="{{ 10002 }}"
  bind:close="onClose"
>
  <view class="rules-header">活动规则</view>
  <view class="rule">
    1.<block wx:if="{{ typeRule }}">{{ typeRule }}</block>可在“会员中心”-“优惠券”列表查看获得的优惠券；
  </view>
  <view class="rule">
    2.
    需购买消费后获得的优惠券，在24小时内完成支付首笔订单后即可获得，完成支付后可在“会员中心”-“优惠券”列表查看获得的优惠券；
  </view>
  <view class="rule">
    3.
    需购买消费后获得的优惠券数量有限，优惠券发放完后，即使完成支付也不再发放；
  </view>
  <view class="rule">
    4. 若购买消费后获得优惠券的订单发生了全额退款，则所获得的优惠券失效。
  </view>
  <view class="rules-btn" bind:tap="onClose">
    我知道了
  </view>
</van-popup>