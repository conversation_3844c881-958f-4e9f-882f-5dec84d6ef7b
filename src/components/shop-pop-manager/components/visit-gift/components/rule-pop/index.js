import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    typeRule: {
      type: String,
      value: ''
    },
    shopName: {
      type: String,
      value: '本店铺'
    }
  },

  data: {
    curShopName: ''
  },

  attached() {
    const { shop_name: curShopName } = app.getShopInfoSync();
    this.setYZData({
      curShopName
    });
  },

  methods: {
    onClose() {
      this.triggerEvent('toggleRules', false);
    }
  }
});
