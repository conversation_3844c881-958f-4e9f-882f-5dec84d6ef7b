<van-popup show="{{ show }}" z-index="{{ 10000 }}" custom-class="gift-container" bind:close="onClose">
  <view class="gift-icon--close" bind:tap="onClose" />
  <view>
    <!-- 自定义头部 -->
    <view
      wx:if="{{ detail.useSelfStyle }}"
      style="background-image: url({{ detail.imageUrl }});"
      class="gift-header"
    />

    <!-- 内容区域 -->
    <view
      class="gift-wrap gift-wrap-{{ detail.showType }}"
      style="{{ detail.style }}"
    >
      <!-- 活动规则 -->
      <view class="rule-tag" bind:tap="toggleRules">
        活动规则
      </view>

      <!-- 券列表 -->
      <block wx:if="{{ detail.hasCoupon }}">
        <view class="gift-wrap__title {{ detail.titleClass }}">
          <block wx:if="{{ detail.showType === 2 && detail.totalMoney }}">送你<text class="gift-wrap__title--strong">{{ detail.totalMoney }}</text>元红包</block>
          <block wx:else>{{ detail.title }}</block>
        </view>

        <coupon-list detail="{{ detail }}" source="{{ source }}" bind:close="onClose" bind:toggle-subscribe-mask="toggleSubscribeMask" />
      </block>

      <!-- 异常信息 -->
      <error-info wx:else detail="{{ detail }}" />
    </view>
  </view>
</van-popup>