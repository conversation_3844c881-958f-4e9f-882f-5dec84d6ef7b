import WscComponent from 'pages/common/wsc-component/index';
import { wscSubscribeMessage, SCENE } from '@/utils/subscribe-message';
import { SOURCE_LOG_MAP } from '../../constants';

WscComponent({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    detail: {
      type: Object,
      value: {},
    },
    // 页面来源 1—店铺主页 2—商详页 3—社区团购活动页 4-微页面
    source: {
      type: Number,
      value: null,
    },
  },
  data: {
    showSubscribeMask: false,
  },

  methods: {
    toggleSubscribeMask(e) {
      // 开关订阅蒙层
      this.triggerEvent('toggle-subscribe-mask', e.detail);
    },
    onClose() {
      const self = this;
      const { source } = this.data;
      wscSubscribeMessage({
        scene: SCENE.COUPON,
        subscribePage: SOURCE_LOG_MAP[source],
        subscribeType: '进店有礼',
        authorizationType: 'coupon',
        windowType: 'micro_visit_gift',
        options: {
          next: () => {
            this.triggerEvent('close');
          },
          onShowTips: () => {
            // 开订阅蒙层
            self.toggleSubscribeMask({ detail: true });
          },
          onCloseTips: () => {
            // 关订阅蒙层
            self.toggleSubscribeMask({ detail: false });
            this.triggerEvent('close');
          },
        },
      });
    },

    toggleRules() {
      this.triggerEvent('toggleRules', true);
    },
  },
});
