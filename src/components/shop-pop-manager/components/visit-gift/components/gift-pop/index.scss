.gift-container {
  padding: 48px 0;
  width: 320px;
  max-width: 320px;
  background-color: transparent !important;
  z-index: 200;
}

.gift-icon--close {
  position: absolute;
  right: 0;
  top: 0;
  width: 32px;
  height: 32px;
  background-image: url(https://img01.yzcdn.cn/public_files/2018/05/16/c29879b23772965a3af7f5689ee61f58.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.gift-header {
  width: 320px;
  height: 57px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.gift-wrap {
  position: relative;
  margin: 0 auto;
  padding: 15px 0 10px;
  width: 100%;
  background-image: url(https://img01.yzcdn.cn/upload_files/2020/07/10/FpSInHgJQ-m5BfwT1t_DuK74q-Ex.png);
  background-position: 0 0;
  border-radius: 8px;
  background-repeat: no-repeat;
  background-size: 100% auto;
  box-sizing: border-box;
  background-clip: padding-box;

  &__title {
    font-family: PingFangSC-Semibold;
    font-size: 24px;
    color: #fff;
    text-align: center;
    line-height: 33px;

    &--strong {
      color: #ffe500;
    }
  }

  .small-title {
    width: 188px;
    font-size: 20px;
    line-height: 22px;
  }

  .rule-tag {
    position: absolute;
    right: -25px;
    top: -9px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0 0 8px 8px;
    font-size: 20px;
    color: #fff;
    text-align: center;
    width: 100px;
    height: 36px;
    line-height: 36px;
    transform: scale(0.5);
    z-index: 10;
  }
}

.gift-wrap-0 {
  border-radius: 0 0 8px 8px;
}

.gift-wrap-1 {
  padding-top: 71px;
  max-height: 378px;

  .rule-tag {
    top: 47px;
    right: -17px;
  }
}

.gift-wrap-0,
.gift-wrap-2,
.gift-wrap-3 {
  max-height: 320px;
}

.gift-wrap-2 {
  background-image: url(https://img01.yzcdn.cn/upload_files/2020/07/10/Fs3jodf-_N1s6Qlba5pobut3mv1w.png);

  .rule-tag {
    right: -17px;
  }
}

.gift-wrap-3 {
  background-image: url(https://img01.yzcdn.cn/upload_files/2020/07/10/Fo33SF3eQZs4IUY5NHt5pafZCDxw.png);

  .gift-wrap__title {
    text-align: left;
    padding-left: 12px;
  }

  &::after {
    display: block;
    content: '';
    position: absolute;
    top: -14px;
    right: 5px;
    width: 130px;
    height: 85px;
    background: url(https://img01.yzcdn.cn/upload_files/2020/08/25/FuKv-Q_4p8wePhP1FiNhgD-Z108k.png)
      no-repeat center;
    background-size: contain;
    z-index: 9;
  }
}

.gift-wrap-4 {
  max-height: 344px;
  background-image: url(https://img01.yzcdn.cn/upload_files/2020/07/10/FlLWJ9fnNy7vzydtVHvMykl0IQo6.png);

  .gift-wrap__title {
    margin-bottom: 24px;
    font-size: 26px;
    color: #ff4000;
  }

  .rule-tag {
    border-radius: 0 10px 0 8px;
    right: -8px;
    background: rgba(245, 197, 75, 0.7);
  }
}

.gift-wrap-99 {
  background-image: url(https://img01.yzcdn.cn/upload_files/2020/07/23/FjqEb3a0-TZH8KPepTvlFA98Usuz.png);
  height: 217px;
  padding-top: 71px;
  box-sizing: border-box;

  .rule-tag {
    top: 47px;
    right: -17px;
  }
}
