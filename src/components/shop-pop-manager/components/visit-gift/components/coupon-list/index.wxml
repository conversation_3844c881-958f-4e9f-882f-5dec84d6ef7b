<view class="coupon-list coupon-list-{{ detail.showType }}">
  <!-- 进店即得 -->
  <block wx:if="{{ detail.fetchedCoupons.length }}">
    <coupon-item
      wx:for="{{ detail.fetchedCoupons }}"
      wx:for-item="coupon"
      wx:key="{{ coupon.id }}"
      coupon="{{ coupon }}"
      kdt-id="{{ detail.kdtId }}"
      source="{{ source }}"
      custom-class="coupon-list__item"
      bind:close="onClose"
      bind:toggle-subscribe-mask="toggleSubscribeMask"
    />
  </block>

  <!-- 支付后得 -->
  <view wx:if="{{ detail.subTitle }}" class="coupon-list__tips">
    <view class="tips-content">
      <text class="text">{{ detail.subTitle }}</text>
    </view>
  </view>
  <block wx:if="{{ detail.payCoupons.length }}">
    <coupon-item
      wx:for="{{ detail.payCoupons }}"
      wx:for-item="coupon"
      wx:key="{{ coupon.id }}"
      coupon="{{ coupon }}"
      kdt-id="{{ detail.kdtId }}"
      source="{{ source }}"
      disabled
      custom-class="coupon-list__item"
      bind:close="onClose"
      bind:toggle-subscribe-mask="toggleSubscribeMask"
    />
  </block>
</view>