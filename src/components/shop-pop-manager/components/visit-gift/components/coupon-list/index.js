import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    detail: {
      type: Object,
      value: {},
    },
    // 页面来源 1—店铺主页 2—商详页 3—社区团购活动页 4-微页面
    source: {
      type: Number,
      value: null,
    },
  },

  methods: {
    onClose() {
      this.triggerEvent('close');
    },
    toggleSubscribeMask(e) {
      // 开关订阅蒙层
      this.triggerEvent('toggle-subscribe-mask', e.detail);
    },
  },
});
