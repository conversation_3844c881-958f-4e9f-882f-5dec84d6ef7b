<view class="coupon-item custom-class {{ disabled ? 'coupon-item-disabled'  :'' }}">
  <view class="coupon-item__value">
    <view class="coupon-detail">
      <view class="coupon-value">
        <text style="font-size: {{ coupon.size }}px;">{{ coupon.value }}</text>
        <text wx:if="{{ coupon.unit }}" class="unit">{{ coupon.unit }}</text>
      </view>
    </view>
    <view class="coupon-condition">{{ coupon.condition }}</view>
  </view>
  <view class="coupon-item__content">
    <view class="coupon-title">{{ coupon.title }}</view>
    <view class="coupon-time">{{ coupon.time }}</view>
  </view>
  <view
    wx:if="{{ !disabled }}"
    class="coupon-item__btn"
    bind:tap="subscribeMessage"
  >
    去使用
  </view>
</view>