import WscComponent from 'pages/common/wsc-component/index';
import { getUsePath } from '../../api';
import { SOURCE_LOG_MAP } from '../../constants';
import navigate from '@/helpers/navigate';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
import { wscSubscribeMessage, SCENE } from '@/utils/subscribe-message';

const app = getApp();
const { logger } = app;
const ACTIVITY_TYPE = {
  GROUP_BUY: 11,
  CARD: 7,
};

const GROUP_TYPE = {
  CARD: {
    KEY: 1,
    VALUE: 'card',
  },
  CODE: {
    KEY: 2,
    VALUE: 'code',
  },
};

WscComponent({
  externalClasses: ['custom-class'],

  properties: {
    disabled: {
      type: Boolean,
      default: false,
    },
    coupon: {
      type: Object,
      default: () => ({}),
    },
    // 页面来源 1—店铺主页 2—商详页 3—社区团购活动页 4-微页面
    source: {
      type: Number,
      value: null,
    },
    kdtId: Number,
  },

  methods: {
    subscribeMessage() {
      const self = this;
      const { source } = self.data;
      wscSubscribeMessage({
        scene: SCENE.COUPON,
        subscribePage: SOURCE_LOG_MAP[source],
        subscribeType: '进店有礼',
        authorizationType: 'coupon',
        windowType: 'micro_visit_gift',
        options: {
          next: self.handleUse.bind(this),
          onShowTips: () => {
            // 开关订阅蒙层
            self.triggerEvent('toggle-subscribe-mask', true);
          },
          onCloseTips: () => {
            // 开关订阅蒙层
            self.triggerEvent('toggle-subscribe-mask', false);
            self.handleUse();
          },
        },
      });
    },
    handleUse() {
      this.sendUseLogger();
      const { fetchId, activityType, activityTypeGroup } = this.data.coupon;
      // 社区团购券跳社区团购页
      if (activityType === ACTIVITY_TYPE.GROUP_BUY) {
        navigate.navigate({
          url: '/packages/groupbuying/buyer-trade/buying/index?from_coupon=1',
        });
        return;
      }
      // 兜底逻辑，若activityTypeGroup无值时
      let groupType = 'card';
      if (activityTypeGroup === GROUP_TYPE.CARD.KEY) {
        groupType = GROUP_TYPE.CARD.VALUE;
      }
      if (activityTypeGroup === GROUP_TYPE.CODE.KEY) {
        groupType = GROUP_TYPE.CODE.VALUE;
      }
      getUsePath({
        couponId: fetchId,
        groupType,
        kdtId: this.data.kdtId,
      })
        .then(({ weappUrl, isSwitchTab }) => {
          const methodName = isSwitchTab ? 'switchTab' : 'navigate';
          navigate[methodName]({
            url: weappUrl,
          });
        })
        .catch(({ msg }) => {
          wx.showToast({
            title: msg,
            icon: 'none',
          });
        });
    },
    sendUseLogger() {
      const { openId } = getApp().getUserInfoSync();
      const pages = getCurrentPages();
      const commonLogger = {
        open_id: openId,
      };
      const cloudLoggerInfo = getLoggerExtraParams('shop_ad_pop', {
        ad_name: '进店有礼弹窗',
        page_url: pages[pages.length - 1].route,
      });
      logger &&
        logger.log({
          et: 'click',
          ei: 'Ad_Click',
          en: '图片广告打开次数',
          si: app.getKdtId(), // 店铺id，没有则置空
          params: {
            component: 'popup_ad',
            ...commonLogger,
            ...cloudLoggerInfo,
          },
        });
    },
  },
});
