.coupon-item {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 4px;
  height: 80px;
  padding: 14px 0;
  box-sizing: border-box;

  &__value {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 94px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 138px;
  }

  &__btn {
    margin: 0 12px 0 5px;
    width: 52px;
    height: 20px;
    line-height: 20px;
    background: #fa1919;
    border-radius: 12px;
    color: #fff;
    font-size: 12px;
    text-align: center;
  }

  .coupon-value {
    padding: 0 6px;
    font-family: PingFangSC-Semibold;
    font-size: 30px;
    color: #fa1919;
    line-height: 30px;

    .unit {
      margin-left: 2px;
      font-size: 12px;
      font-family: PingFangSC-Regular;
      line-height: 16px;
    }
  }

  .coupon-condition {
    margin-top: 6px;
    padding: 0 12px;
    color: #969799;
    font-size: 12px;
  }

  .coupon-title {
    font-family: PingFangSC-Semibold;
    line-height: 18px;
    font-size: 14px;
    width: 100%;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }

  .coupon-time {
    margin-top: 10px;
    font-size: 12px;
    line-height: 16px;
  }
}

.coupon-item-disabled {
  .coupon-value,
  .coupon-title,
  .coupon-time {
    color: #969799;
  }
}
