<account-wx-login id="account-wx-login"/>
<!--z-index 顺序 修改请查看 docs/z-index.md-->
<simple-popup
  show="{{ visible }}"
  lock-scroll
  close-on-click-overlay
  z-index="160"
  bind:close="closePopup"
  overlayStyle="background-color: rgba(0, 0, 0, .6);"
  custom-style="background-color: transparent;"
  custom-container-style="background-color: transparent;"
>
  <view class="l-popup__close">
    <image
      class="c-close__img"
      src="https://img01.yzcdn.cn/upload_files/2021/10/18/FjwD8j4OOpmzZJF26rT_-dYgAutG.png"
      bindtap="closePopup">
    </image>
  </view>

  <div wx:if="{{ popupCustomImage }}" class="l-popup__custom">
    <view bind:tap="doCheckin">
      <image
        src="{{popupCustomImage}}"
        class="custom-image"
        mode="widthFix"
        alt="立即签到"
        bindtap="doCheckin"
      />
    </view>
  </div>

  <view wx:else class="l-popup__container">
    <image
      class="c-sign__img"
      src="https://img01.yzcdn.cn/upload_files/2023/03/28/FmEze4O2iUcWrl5aKuRQfDHJYVlv.png"
      alt="签到有礼活动图标"
    >
    </image>
    <view class="l-popup__footer">
      <form-view class="c-sign__form">
        <view bind:tap="doCheckin">
          <button
            class="c-sign__btn"
            disabled="{{ !canSign }}"
          >
            立即签到
          </button>
        </view>
      </form-view>
    <view>
  </view>
</simple-popup>
