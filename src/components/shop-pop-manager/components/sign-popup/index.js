import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';
import { wscSubscribeMessage, SCENE } from '@/utils/subscribe-message';
import closePopBehavior from '../behaviors/close-pop';
import { SOURCE_LOG_MAP } from '../visit-gift/constants';
import loggerBehaviors from 'components/shop-pop-manager/behaviors/logger-behaviors';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
import { checkPureWscSingleStore } from '@youzan/utils-shop';
import getAuthorizedState from 'shared/utils/get-authorized-state';

const app = getApp();
const { logger } = app;

WscComponent({
  behaviors: [closePopBehavior, loggerBehaviors],
  properties: {
    // 页面来源 1—店铺主页 2—商详页 3—社区团购活动页 4-微页面
    source: {
      type: Number,
      value: null,
    },
  },
  data: {
    popupCustomImage: '',
    visible: false,
    chechinId: null,
    canSign: true, // 按钮是否能签到
    isAuthorized: false, // 是否授权过
  },

  attached() {
    this.cloudLoggerInfo = getLoggerExtraParams('shop_ad_pop', {
      ...this.getCloudLoggerInfo('check_in', {
        isCustom: this.popupCustomImage ? '是' : '否',
      }),
    });
    this.canShowCheckinPopup();
  },

  methods: {
    subscribeSignMessage(onComplete) {
      const { source } = this.data;
      const self = this;
      wscSubscribeMessage({
        scene: SCENE.SIGN,
        subscribePage: SOURCE_LOG_MAP[source],
        subscribeType: '签到提醒',
        authorizationType: 'sign',
        windowType: 'micro_sign_message',
        options: {
          next: onComplete,
          onShowTips: () => {
            // 开关订阅蒙层
            self.triggerEvent('toggle-subscribe-mask', true);
          },
          onCloseTips: () => {
            // 开关订阅蒙层
            self.triggerEvent('toggle-subscribe-mask', false);
            onComplete();
          },
        },
      });
    },
    getAdImageInfo() {
      return 'https://img01.yzcdn.cn/public_files/2019/08/21/75c16648a9226541a656f22357aee9bf.png';
    },
    // 请求接口，判断首页是否展示签到弹窗
    canShowCheckinPopup() {
      app
        .request({
          path: 'wscump/checkin/show_checkin_page_v2.json',
        })
        .then((res) => {
          const { isShow, checkinId } = res;
          const popupCustomImage = res.popUpWindowDTO?.pictureUrl ?? '';

          if (isShow) {
            const handleShow = () => {
              this.setYZData({
                visible: true,
                checkinId,
                popupCustomImage,
              });
              this.onPopOpen();

              // 曝光埋点
              logger &&
                logger.log({
                  et: 'view',
                  ei: 'Ad_view',
                  en: '图片广告曝光',
                  si: app.getKdtId(),
                  params: {
                    component: 'popup_ad',
                    isCustom: popupCustomImage ? '是' : '否',
                    ...this.cloudLoggerInfo,
                  },
                });
            };
            getAuthorizedState()
              .then((data) => {
                const isAuthorized = data.mobile;
                this.setYZData({
                  isAuthorized,
                });
                handleShow();
              })
              .catch(() => {
                handleShow();
              });
          } else {
            this.onOtherClose();
          }
        })
        .catch((err) => {
          this.onOtherClose();
          console.error('签到弹窗接口', err);
        });
    },

    // 关闭弹窗
    closePopup() {
      this.onClickClose();
    },

    // 实际执行签到操作
    doCheckin() {
      const { checkinId, canSign, isAuthorized } = this.data;
      if (!canSign) {
        return wx.showLoading({
          title: '签到中，请稍等',
        });
      }
      // 如果未授权过 跳转至签到页面进行手机号授权后签到
      if (!isAuthorized) {
        return navigate.navigate({
          url: `/packages/shop/ump/sign-in/index`,
        });
      }
      wx.showLoading({
        title: '签到中',
      });
      this.setYZData({
        canSign: false,
      });
      app
        .request({
          path: 'wscump/checkin/checkinV2.json',
          query: {
            checkinId,
          },
        })
        .then((res) => {
          // 签到成功后关闭loading弹窗，存储签到成功的返回数据，关闭签到弹窗，跳转到签到页
          wx.hideLoading();
          const onComplate = ({ shopMetaInfo = {} }) => {
            const dbid = app.db.set({
              res,
            });
            const gotoSignIn = () => {
              navigate.navigate({
                url: `/packages/shop/ump/sign-in/index?&dbid=${dbid}`,
              });
            };
            this.onOtherClose();
            if (checkPureWscSingleStore(shopMetaInfo)) {
              this.subscribeSignMessage(gotoSignIn);
            } else {
              gotoSignIn();
            }
          };
          app.getShopInfo().then(onComplate).catch(onComplate);
        })
        .catch((err) => {
          wx.hideLoading();
          this.setYZData({
            canSign: true,
          });
          wx.showToast({
            title: err.msg || '签到失败',
            icon: 'none',
            duration: 1000,
          });
        });
      // 埋点
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'Ad_Click',
          en: '图片广告打开次数',
          si: app.getKdtId(), // 店铺id，没有则置空
          params: {
            component: 'popup_ad',
            isCustom: this.popupCustomImage ? '是' : '否',
            ...this.cloudLoggerInfo,
          },
        });
    },
  },
});
