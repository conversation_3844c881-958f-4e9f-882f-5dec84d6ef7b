export default Behavior({
  methods: {
    closePopContent() {
      this.setYZData({
        visible: false,
      });
    },
    onPopOpen() {
      this.triggerEvent('pop-open');
    },
    onClickClose() {
      this.closePopContent();
      this.triggerEvent('close-pop', {
        currentShow: false,
        image_url: this.getAdImageInfo(),
        isRealClose: true,
      });
    },
    onOtherClose(isRealClose = false) {
      this.closePopContent();
      this.triggerEvent('close-pop', {
        isRealClose,
      });
    },
  },
});
