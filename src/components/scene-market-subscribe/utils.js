import { PlanTypeEnum } from 'constants/member-constants';
import { getEffectiveTime } from 'utils/get-effective-time';

// * 生成权益列表
const generateBenefitList = (
  isFree,
  pointDiscount,
  pointRate,
  point,
  isMemberPrice
) => {
  // * 权益只有五项：包邮、折扣、积分倍率、积分、会员价
  const benefitList = [];

  // * 1、折扣
  if (pointDiscount) {
    benefitList.push({
      icon: 'https://img.yzcdn.cn/upload_files/2020/08/12/FtX6bRYCOkvCMdDSjueHMe0CLlBc.png',
      facadeShowName: pointDiscount / 10 + '折',
    });
  }

  // * 2、会员价
  if (isMemberPrice) {
    benefitList.push({
      icon: 'https://img01.yzcdn.cn/upload_files/2021/04/15/FoD3JBAijPl-8DfIigvRI8VBF9Ej.png',
      facadeShowName: '会员价',
    });
  }

  // * 3、包邮
  if (isFree) {
    benefitList.push({
      icon: 'https://img.yzcdn.cn/upload_files/2020/08/12/Fvv48DyEPCkmXxSCmcIHmVBLN7-N.png',
      facadeShowName: '包邮',
    });
  }

  // * 4、积分倍率
  if (pointRate) {
    benefitList.push({
      icon: 'https://img.yzcdn.cn/upload_files/2020/08/12/FknvorIIUp83mx0fRBmDjJ4alLqv.png',
      facadeShowName: pointRate / 10 + '倍积分',
    });
  }

  // * 5、积分
  if (point) {
    benefitList.push({
      icon: 'https://img.yzcdn.cn/upload_files/2020/08/12/FjLAxv1hsUpNRu_Hp9cj7xjPfbXi.png',
      facadeShowName: `送${point}积分`,
    });
  }

  return benefitList;
};

// * 计划详情数据处理
export const planInfoAdapter = (data) => {
  const {
    planType,
    couponList,
    effectiveTime,
    isFree,
    pointDiscount,
    pointRate,
    point,
    isMemberPrice,
    ...rest
  } = data;

  const benefitList = generateBenefitList(
    isFree,
    pointDiscount,
    pointRate,
    point,
    isMemberPrice
  );
  const planEffectiveTime = getEffectiveTime(effectiveTime, planType);

  return {
    coupons: couponList,
    benefitList,
    planEffectiveTime,
    planType,
    ...rest,
  };
};

/**
 * * 生成不同活动需要的样式
 */
export const getBgSrcByPlanType = (planType) => {
  switch (planType) {
    case PlanTypeEnum.Birthday:
      return {
        popBackgroundSrc:
          'https://b.yzcdn.cn/public_files/d3f9d3c42be9ded24108766e784f5cbf.png',
        popBgTranslate: '4px',
        popBackgroundHeight: '76px',
        popVipMaskTop: '80px',
      };
    case PlanTypeEnum.MemberDay:
      return {
        popBackgroundSrc:
          'https://b.yzcdn.cn/public_files/447a17e7d98639ac88ecfa6301046902.png',
        popBgTranslate: '32px',
        popBackgroundHeight: '125px',
        popVipMaskTop: '129px',
      };
    case PlanTypeEnum.Festival:
      return {
        popBackgroundSrc:
          'https://b.yzcdn.cn/public_files/d6b1f2cfee98bbccb04d8c5c7ef716af.png',
        popBgTranslate: '29px',
        popBackgroundHeight: '112px',
        popVipMaskTop: '119px',
      };
    default:
      return '';
  }
};

const KeywordMap = {
  HOME: 'home/dashboard',
  FEATURE: 'feature',
  LEVEL_CENTER: 'levelcenter',
  OTHER: 'other',
};

/**
 * * 判断参数中的关键字
 * @param {string} path
 */
export const getFromPathKeyword = (path, query = {}) => {
  if (path.includes(KeywordMap.HOME)) {
    return KeywordMap.HOME;
  }
  if (path.includes(KeywordMap.FEATURE)) {
    return query.alias || KeywordMap.FEATURE;
  }
  if (path.includes(KeywordMap.LEVEL_CENTER)) {
    return KeywordMap.LEVEL_CENTER;
  }
  return KeywordMap.OTHER;
};
