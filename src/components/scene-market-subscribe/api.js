import { requestWithGuideParams } from 'utils/guide';

const app = getApp();

/**
 * * 获取用户当前等级信息
 */
export const getUserLevel = (levelType) => {
  return requestWithGuideParams({
    path: '/wscuser/levelcenter/api/userLevelDetail.json',
    data: { type: levelType },
  });
};

// * 根据 planId 获取当前场景营销计划的弹窗信息详情
export const getActivityInfoByPlanId = (planId) => {
  return app.request({
    path: '/wscuser/level/api/getPlanGiftBag.json',
    data: {
      planId,
    },
  });
};
