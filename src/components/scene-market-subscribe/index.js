import WscComponent from 'pages/common/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';
import * as API from './api';
import { properties } from './mixins';
import {
  FromPageMap,
  PlanStateEnum,
  PlanTypeEnum,
  SCENE_MARKET_SCENE,
} from 'constants/member-constants';
import { LevelType } from '@/packages/levelcenter/level-common/utils/constant';

import get from '@youzan/weapp-utils/lib/get';
import {
  getBgSrcByPlanType,
  planInfoAdapter,
  getFromPathKeyword,
} from './utils';
import {
  getIsNeedSubscribeInSceneMarketActivity,
  subscribeInSceneMarketActivity,
} from 'utils/scene-market-subscribe-message';
import openWebView from 'utils/open-web-view';
import args from '@youzan/weapp-utils/lib/args';
import { getCurrentPageOption } from '@/base-api/shop/chain/chain-utils';
import { getSubscribePreInfo } from '@/helpers/subscribe';

WscComponent({
  properties: {
    ...properties,
    bizDataMap: Object,
    specificPlanId: Number,
    planId: {
      type: String,
      observer: 'initialize',
    },
    fromPage: String, // * homeOrFeature 首页或微页面；memberCenter 会员中心；
  },

  data: {
    coupons: [], // * 优惠券信息

    visible: false,
    planType: PlanTypeEnum.Birthday, // * 来自场景营销计划的活动类型

    couponCount: 0,
    benefitCount: 0,
    benefitList: [],
    popBackgroundSrc: '',
    popBgTranslate: '0px',
    needSubscribe: false, // * 从首页进入将执行采集
    showSubscribeFail: false, // * 是否展示订阅失败弹窗
    showCloseBtn: false, // * 是否展示关闭弹窗按钮
    isHasLevel: true, // * 是否获取等级
    planState: PlanStateEnum.InProgress,
    PlanStateEnum,
    isLevelMatchingBenefit: false, // * 用户等级和计划权益礼包是否匹配
    userMatchBenefitLevel: '', // * 当前计划权益礼包对应等级,
    templateIdList: [],
  },

  attached() {
    if (!this.properties.planId) {
      this.next();
    }
  },

  methods: {
    initialize(val) {
      if (val) {
        this.start(val);
      }
    },
    getUserLevel() {
      API.getUserLevel(LevelType.FREE)
        // * 来自任何场景，都有可能是没有等级，要判断是否没有等级
        .then((userLevelDetail) => {
          const levelAlias = get(userLevelDetail, 'level.levelAlias', null);
          if (!levelAlias) {
            // * 未获取等级
            this.setYZData({
              isHasLevel: false,
            });
          }
          this.showPopup();
        })
        .catch(() => {
          Toast(`获取计划等级信息失败`);
          this.next();
        });
    },
    start(planId) {
      // * 区分弹窗来源

      const { scene } = wx?.getLaunchOptionsSync?.();
      const { path, query } = getCurrentPageOption();

      // * 会员中心轮播点击
      const isFromSwipeClick =
        this.properties.fromPage === FromPageMap.memberCenterClick;

      // * 1014 - 模板订阅消息；1017 - 体验版打码进入；1107 - 小程序订阅消息；
      if (!isFromSwipeClick) {
        const pages = getCurrentPages();
        if (![1014, 1017, 1107].includes(scene) || pages.length >= 2) {
          // * 并非从订阅消息场景进来 或者其他页面跳转过来； 并且不是轮播列表点击进入，则不展示；
          this.next();
          return;
        }
      }

      // * 根据 planId 获取计划详情
      API.getActivityInfoByPlanId(planId)
        .then((data) => {
          if (!data.planType) {
            Toast('你所在的会员等级暂无可享受的活动权益');
            this.next();
            return false;
          }

          if (!isFromSwipeClick) {
            // * 非点击轮播列表进入，执行路径匹配校验
            const { jumpPath = '' } = data;
            const fromPathKeyword = getFromPathKeyword(path, query);

            if (!jumpPath.includes(fromPathKeyword)) {
              // * 从无关页面跳入则不弹出
              this.next();
              return false;
            }
          }

          const planInfo = planInfoAdapter(data);

          const {
            planType,
            coupons,
            benefitList,
            planEffectiveTime,
            state,
            userMatchBenefitLevel,
            userCurrentLevel,
          } = planInfo;

          const {
            popBackgroundSrc,
            popBgTranslate,
            popBackgroundHeight,
            popVipMaskTop,
          } = getBgSrcByPlanType(planType);

          this.setYZData({
            planType,
            coupons,
            benefitList,
            couponCount: coupons.length,
            benefitCount: benefitList.length,
            planEffectiveTime,
            popBackgroundSrc,
            popBgTranslate,
            popBackgroundHeight,
            popVipMaskTop,
            planState: state,
            userMatchBenefitLevel,
            isLevelMatchingBenefit: userMatchBenefitLevel === userCurrentLevel,
          });
          return true;
        })
        .then((canNext) => {
          canNext && this.getUserLevel();
        })
        .catch((err) => {
          const errText = err.msg || err.message || JSON.stringify(err);

          Toast(`获取计划${planId}详情失败，` + errText);
          this.next();
        });
      // * 判断是否应该采集订阅消息 - 从首页或微页面进入才采集
      if (this.properties.fromPage === FromPageMap.homeOrFeature) {
        this.getIsNeedSubscribe();
      }
    },
    next(shown = false) {
      setTimeout(() => {
        this.triggerEvent('close'); // * 会员中心 - 轮播列表 - 主动唤起的关闭动作；主页关闭
        this.triggerEvent('next', shown);
      }, 500);
    },

    handleClose(shown = false) {
      this.next(shown);
      this.setYZData({
        visible: false,
      });
    },

    // * 授权回调 - 只在无等级情况下，先授权再跳转到完善信息页
    handleButtonConfirm() {
      if (this.properties.fromPage === FromPageMap.homeOrFeature) {
        // * 来自首页/微页面 & 无等级 - 跳转完善信息页
        this.jumpToComplete();
        this.next();
        return;
      }
      // * 来自会员中心页 & 未获取等级 - 轮播点开/自动弹出 - 走会员中心页授权后的回调
      this.triggerEvent('bindMobile');
      this.handleClose(true); // * true 表示终止后面的跳转
    },

    // * 有等级条件下的直接点击
    handleClick() {
      if (this.data.needSubscribe) {
        // * 来自首页/微页面 & 有采集能力 - 采集消息
        this.handleFeatureSubscribe();
        return;
      }

      // * 来自会员中心页 & 已获取等级 - 轮播点开 -> 直接关闭
      // * 微页面不可采集 && 有等级情况下弹出 -> 直接关闭
      this.handleClose();
    },

    // * 首页采集 - 采集弹窗，无论同意或取消都在点击之后关闭
    handleFeatureSubscribe() {
      const self = this;
      subscribeInSceneMarketActivity({
        templateIdList: this.data.templateIdList,
        onSuccess: () => {
          self.handleClose();
        },
        onReject: () => {
          self.handleClose();
        },
        onFail: () => {
          // * 失败则弹出失败弹窗
          self.setYZData({
            showSubscribeFail: true,
            showCloseBtn: true,
          });
        },
      });
    },

    // * 关闭订阅失败弹窗
    closeSubscribeFailPopup() {
      this.setYZData({
        showSubscribeFail: false,
      });
    },

    showPopup() {
      this.setYZData({
        visible: true,
      });
    },

    // * 首页/微页面跳转到完善信息页
    jumpToComplete() {
      const urlPath = '/wscuser/levelcenter/fill';
      openWebView(
        args.add(urlPath, {
          kdt_id: getApp().getKdtId(),
          levelType: LevelType.FREE,
          fromScene: 'complete',
          eT: Date.now(),
        }),
        { title: '完善信息' }
      );
    },

    // * 判断是否有采集能力
    getIsNeedSubscribe() {
      getIsNeedSubscribeInSceneMarketActivity()
        .then((needSubscribe) => {
          return getSubscribePreInfo({
            scene: SCENE_MARKET_SCENE,
          }).then((templateIdList) => {
            this.setYZData({
              needSubscribe: needSubscribe && templateIdList.length > 0,
              templateIdList,
            });
          });
        })
        .catch((err) => {
          Toast(err);
        });
    },
  },
});
