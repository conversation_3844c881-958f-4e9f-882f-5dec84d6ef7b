.coupon-box {
  display: flex;
  align-items: center;
  height: 64px;
  border-radius: 4px;
  background: linear-gradient(to bottom right, #fff6eb, #fff0db);
  flex-direction: row;
  position: relative;
  margin-bottom: 8px;
  color: #b66f0f;

  .left-icon,
  .right-icon {
    top: 26px;
    width: 6px;
    height: 12px;
    background-color: #fff;
    position: absolute;
  }

  .left-icon {
    left: 0;
    border-radius: 0 12px 12px 0;
  }

  .right-icon {
    right: 0;
    border-radius: 12px 0 0 12px;
  }

  .left-part {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    height: 48px;
    border-right: 1px dashed rgba(182, 111, 15, 0.2);
    width: 29.1%;

    .name {
      font-size: 16px;
      font-weight: bold;
    }

    .num {
      font-size: 30px;
      line-height: 30px;
      font-weight: bold;
      margin-left: 5px;
    }

    .unit {
      margin: 12px 0 0 2px;
    }

  }

  .right-part {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-left: 12px;
    font-size: 12px;
    width: 60%;

    .name {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 6px;
      line-height: 18px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .use-condition {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

  }

}

.disable-coupon {
  background: inherit;
  background-color: #f7f8fa;
  color: #969799 !important;

  .left-part {
    border-right: 1px dashed rgba(150, 151, 153, 0.2);
  }
}

.small-size-style {
  .left-part {
    width: 35.6%;
  }

  .right-part {
    width: 55%;
    .max-width-normal {
      max-width: 122px;
    }
  }
}
