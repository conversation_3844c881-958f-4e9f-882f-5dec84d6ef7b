import WscComponent from 'pages/common/wsc-component/index';
import {
  BeneitNumTypeEnum,
  PreferentialModeEnum,
} from 'constants/member-constants';
import accDiv from '@youzan/utils/number/accDiv';

WscComponent({
  properties: {
    coupon: {
      type: Object,
    },
  },
  data: {
    couponData: {},
    // 字体大小
    valueStyle: '',
    // 最大宽度
    maxWidthStyle: '',
  },
  methods: {
    getCouponWidth(couponStatus) {
      const maxWidthStyle = couponStatus
        ? 'max-width-samll'
        : 'max-width-normal';
      this.setYZData({
        maxWidthStyle,
      });
    },
    computeFontSize(value, smallSize) {
      const { length } = '' + value;
      let size;
      if (length >= 10) size = 12;
      else if (length >= 8) size = 16;
      else if (length >= 6) size = 20;
      else if (length >= 4) size = 24;
      else size = 30;
      return smallSize ? size - 4 : size;
    },
  },
  observers: {
    coupon(val) {
      const {
        // 标题
        title,
        value,
        // 使用限制
        thresholdCopywriting,
        // 优惠券类型
        preferentialMode,
        // 优惠券值类型
        voucherValueGenerateType,

        status,
        smallSize,
      } = val;

      let tempValue;
      let tempUnit;
      let couponName;
      switch (Number(preferentialMode)) {
        case PreferentialModeEnum.NUM:
          if (voucherValueGenerateType === BeneitNumTypeEnum.RANDOM) {
            couponName = '随机金额';
          } else {
            tempValue = value ? accDiv(value, 100) : '';
          }
          tempUnit = '元';
          break;
        case PreferentialModeEnum.DISCOUNT:
          tempValue = value ? accDiv(value, 10) : '';
          tempUnit = '折';
          break;
        case PreferentialModeEnum.EXCHANGE:
          couponName = '兑换商品';
          break;
      }

      const size = this.computeFontSize(tempValue, smallSize);

      const valueStyle = `font-size: ${size}px`;

      const couponData = {
        value: tempValue,
        unit: tempUnit,
        title,
        couponName,
        useCondition: thresholdCopywriting,
      };
      const statusStr = status === 0 ? '已抢完' : status ? '已发放' : '';
      this.getCouponWidth(statusStr);
      this.setYZData({
        couponData,
        valueStyle,
        smallSize,
      });
    },
  },
});
