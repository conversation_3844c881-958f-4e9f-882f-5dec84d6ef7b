import WscComponent from 'pages/common/wsc-component/index';
import { PlanTypeEnum } from 'constants/member-constants';

const app = getApp();

WscComponent({
  properties: {
    planType: Number,
    planEffectiveTime: String,
    userMatchBenefitLevel: String,
  },

  data: {
    nickName: '',
    PlanTypeEnum,
    isFromComplete: false,
  },

  attached() {
    this.getNickName();
  },

  methods: {
    getNickName() {
      const userInfo = app.getUserInfoSync() || {};
      this.setYZData({
        nickName: userInfo.nickname,
      });
    },
  },
});
