import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    benefitList: Array,
    limit: Number,
  },

  data: {
    renderBenefits: [],
  },

  attached() {
    this.initRenderBenefits();
  },

  methods: {
    initRenderBenefits() {
      const { benefitList, limit } = this.properties;

      const renderBenefits = [...benefitList];

      if (renderBenefits.length > limit) {
        const benefitLength = renderBenefits.length;
        renderBenefits.splice(limit - 1, 0, {
          appName: '全部权益',
          benefitCount: benefitLength,
        });
      }

      this.setYZData({
        renderBenefits: renderBenefits.slice(0, limit),
      });
    },
  },
});
