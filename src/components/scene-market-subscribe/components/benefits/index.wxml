<view class="popup-benefit-list" wx:if="{{ renderBenefits.length > 0 }}">
  <view
    wx:for="{{ renderBenefits }}"
    wx:key="appName"
    class="benefit-item"
  >
    <view
      wx:if="{{ item.icon }}"
      class="benefit-item__icon"
      style="background: url({{ item.icon }}) no-repeat center/contain"
    />
    <view wx:if="{{ item.benefitCount }}" class="benefit-item__num">
      {{ item.benefitCount }}项
    </view>
    <view class="benefit-item__name">{{ item.facadeShowName || item.appName }}</view>
  </view>
</view>
