<view>
  <van-popup
    show="{{ visible }}"
    custom-class="benefit-popup"
    custom-style="background-color: rgba(255, 255, 255, 0);"
  >
    <view wx:if="{{ visible }}" class="pop-wrap">
      <image wx:if="{{ !!popBackgroundSrc }}" class="image" style="transform: translateY({{ popBgTranslate }}); height: {{ popBackgroundHeight }}" src="{{ popBackgroundSrc }}" />
      <view class="pop-content">
        <view style="top: {{ popVipMaskTop }}" class="vip-mask"></view>
        <scene-pop-header
          plan-type="{{ planType }}"
          plan-effective-time="{{ planEffectiveTime }}"
          user-match-benefit-level="{{ userMatchBenefitLevel }}"
        />

        <benefits wx:if="{{ benefitList.length }}" benefit-list="{{ benefitList }}" limit="{{ 4 }}" />

        <view class="benefit-list-box">
          <!-- 优惠券 -->
          <coupon
            wx:for="{{ coupons }}"
            wx:key="couponId"
            wx:for-item="coupon"
            coupon="{{ coupon }}"
          />
        </view>

        <view wx:if="{{ planState !== PlanStateEnum.InProgress }}" class="gift-tip">活动已结束，记得下次再来</view>
        <view wx:elif="{{ !isHasLevel }}" class="gift-tip"></view>
        <view wx:elif="{{ !isLevelMatchingBenefit }}" class="gift-tip">你所在等级暂无可享受的会员权益</view>
        <view wx:else class="gift-tip">奖励已发送至你的账户</view>

      </view>
      <view class="pop-actions">
        <user-authorize wx:if="{{ !isHasLevel }}" authTypeList="{{ ['mobile'] }}" bind:next="handleButtonConfirm" bizDataMap="{{ bizDataMap }}">
          <van-button
            custom-class="action-btn"
            block
            plain
          >
            <view >立即入会</view>
          </van-button>
        </user-authorize>
        <van-button
          wx:else
          custom-class="action-btn"
          block
          plain
          bind:tap="handleClick"
        >
          <view >我知道了</view>
        </van-button>
        <van-icon
          wx:if="{{ showCloseBtn || !isHasLevel }}"
          class="close-btn"
          name="close"
          bindtap="handleClose"
        />
      </view>
    </view>
  </van-popup>
  <subscribe-fail-popup show="{{ showSubscirbeFail }}" openImageSrc="https://img.yzcdn.cn/public_files/2019/12/23/7b987495fc6bbd066262153577881592.png" bind:close="closeSubscribeFailPopup"></subscribe-fail-popup>
</view>
