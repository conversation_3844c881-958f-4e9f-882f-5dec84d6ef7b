<import src="./templates/kaishu.wxml" />

<van-toast id="van-toast" />
<van-notify id="van-notify" />



<view
  wx:if="{{ levelGrowth.showInfoCompleted && userAgreePrivacy && hiddenInfoCompleted}}"
  class="member-center__complete-info {{ isImmersive ? 'immersive' : '' }}"
  style="{{ isImmersive ? immersiveCompletedInfoStyle : '' }}"
  bindtap="handleCompleteInfoClick"
>
  <view class="text">
    <van-icon
      custom-class="arrow-custom"
      name="{{ isImmersive ? 'user' : 'https://img01.yzcdn.cn/public_files/f31c2cc64cc43f0ef9e35de9eb29067f.png' }}"
      size="16"
      custom-style="margin-right: 8px;margin-top: {{ isImmersive ? 1 : 3 }}px"
    />
    完善资料尊享更多会员权益
  </view>
  <view>
    <text class="usercenter-level-tip">去完善</text>
    <van-icon
      color="{{ isImmersive ? '#2F2F34' : '#9a640c' }}"
      custom-style="vertical-align: -2px;"
      name="arrow"
      size="{{ isImmersive ? '12' : '14' }}"
      custom-class="arrow-custom"
    />
  </view>
</view>
<view class="usercenter" wx:if="{{ themeFetched && templateName == 'normal' }}">
  <member-center
    componentConfigsMap="{{ userCenterComponentsMap }}"
    component-configs="{{ userCenterComponents }}"
    user-name="{{ userInfo.nickName }}"
    avatar="{{ userInfo.avatarUrl }}"
    grade="{{ memberLevelGradeText }}"
    stats-names="{{statsNames}}"
    stats="{{ stats }}"
    userAgreePrivacy="{{ userAgreePrivacy }}"
    orders="{{ orders }}"
    prompt-order-pay="{{ promptOrderPay }}"
    prompt-order-evaluation="{{promptOrderEvaluation}}"
    head-position="{{ config.headPosition }}"
    background-gradient="{{ config.backgroundGradient }}"
    background-image="{{ backgroundImage }}"
    member-level="{{ config.memberLevel }}"
    page-style="{{ config.pageStyle }}"
    plugin-list="{{ plugins }}"
    plugin-values="{{ pluginValues }}"
    base-links="{{ baseLinks }}"
    order-links="{{ orderLinks }}"
    plugin-links="{{ pluginLinks }}"
    show-bind-user="{{ showBindPhoneNumber }}"
    bind-tips="{{ bindTips }}"
    is-not-valid="{{ isNotValid }}"
    is-support-sign="{{ isSupportSign }}"
    is-signed="{{ isSigned }}"
    is-yz-edu="{{isYzEdu}}"
    is-pure-wsc-single-store="{{isPureWscSingleStore}}"
    show-level="{{showLevel}}"
    reminder-infos="{{ reminderInfos }}"
    appointments="{{ appointments }}"
    no-view-num="{{ noViewNum }}"
    is-read-workset="{{ isReadWorkset }}"
    momentsName="{{ momentsName }}"
    wait-reward-count="{{ waitRewardCount }}"
    review-to-read-num="{{ reviewToReadNum }}"
    show-growth="{{ levelGrowth.showGrowthValue }}"
    is-support-growth-value="{{ isSupportGrowthValue }}"
    level-value="{{ levelGrowth.levelValue }}"
    growth-value="{{ levelGrowth.growthValue }}"
    benefit-text="{{ levelGrowth.benefitDesc }}"
    level-icon-type="{{ levelGrowth.payLevelName || levelGrowth.payBenefitDesc ? 1 : 0 }}"
    can-show-moments-flag="{{ canShowMomentsFlag }}"
    introduction="{{ introduction }}"
    benefit-reminds="{{benefitReminds}}"
    is-member="{{ isMember }}"
    stats-list="{{ statsList }}"
    is-immersive="{{ isImmersive }}"
    show-info-completed="{{ levelGrowth.showInfoCompleted && userAgreePrivacy && hiddenInfoCompleted }}"
    bind:statsItemClicked="statsItemClicked"
    bind:bindUser="tapBindZanAccount"
    bind:bindGetUserInfo="onGetUserInfo"
    bind:tapUserInfo="handleTapUserInfo"
    bind:tapSign="handleSign"
    bind:pluginItemClicked="onPluginItemClicked"
    bind:jumpToLink="jumpToLink"
    bind:imageChange="handleImageChange"
    bind:contactback="handleContactBack"
    bind:onAccountSuccess="onAccountSuccess"
    bind:registryThreshold="handleRegistryThreshold"
  />
  <!-- 教育店铺 -->
  <edu-popup show-pop="{{ showPop }}" bind:close-pop="onClosePop">
    <image src="{{ certImg }}" class="uc-edu__cert {{ certTemplateType === 'vertical' ? 'uc-edu__cert--vertical' : 'uc-edu__cert--horizontal' }}" />
  </edu-popup>
  <edu-popup show-pop="{{ shopRewardPop }}" bind:close-pop="onCloseRewardPop">
    <view class="popup-reward">
      <view class="popup-reward__title">你有{{ rewardCount }}个奖励待领取</view>
      <image src="{{ rewardGift }}" class="popup-reward__img" />
      <van-button
        size="large"
        type="primary"
        round
        bind:tap="linkToReward"
      >
        去领取
      </van-button>
    </view>
  </edu-popup>
</view>
<template
  wx:elif="{{ themeFetched }}"
  is="uc-{{ templateName }}"
  data="{{ kaishuPluginList,kaishuStatsList,userInfo, kdtId, showBindPhoneNumber, bindTips, topay, tosend, send, isSetShoppingCart,isSetGroupCenter,groupName, cartCount, servicePhoneNumber, im, salesman, isNotValid, level, points, nextPoints, hasCheckIn, showCheckIn, pointsDiff }}"
></template>

<account-login
  wx:if="{{ accountLogin }}"
  show="{{ accountLogin }}"
  bind:loginSuccess="onAccountSuccess"
  bind:closeAccountLogin="onAccountClose"
></account-login>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />

<block wx:if="{{ showUserAuthPopup && centerPhoneStatus}}">
  <view class="user-auth-popup-mask"></view>
  <view class="user-auth-popup-layout">
    <view class="user-auth-popup">
      <image class="shop-img" src="{{ userAuthPopupShopInfo.image }}" />
      <view class="shop-name">{{ userAuthPopupShopInfo.name }}</view>
      <view class="userauth-msg">登录手机号，同步全渠道订单和优惠券</view>
      <view class="userauth-btn" style="{{ userauthBtnStyle }}">
        <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="hideUserAuthPopup">
          授权
        </user-authorize>
      </view>
      <van-icon
        bind:click="hideUserAuthPopup"
        name="close"
        size="24px"
        color="#fff"
        style="position: absolute; right: 0; top: -40px;"
      />
    </view>
  </view>
</block>

<activate-card-pop wx:if="{{showActivatePop}}" benefitcard="{{benefitcard}}" />

<guide-becoming-member show-guide-becoming-member="{{showGuideBecomingMember}}" guide-becoming-member-info="{{ guideBecomingMemberInfo }}"  bind:onclose="onGuideBecomingMemberClose"/>
<user-authorize wx:if="{{ showUserAuth && userAgreePrivacy }}" bind:next="hideUserAuthPopup" trigger="auto" scene="usercenter" >
</user-authorize>
