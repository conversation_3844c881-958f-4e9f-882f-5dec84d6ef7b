export const STAS = {
  balance: '0.00',
  points: 0,
  cards: 0,
  coupons: 0,
  showCouponDot: false,
  showCardDot: false,
  showChange: true,
};

export const STAS_NAME = {
  balance: '余额',
  points: '积分',
  cards: '卡',
  coupons: '优惠券/码',
};

const p = 'packages';

export const KAISHU_STATS_LIST = [
  {
    type: 'topay',
    url: `/${p}/trade/order/list/index?type=topay`,
    text: '代付款',
    count: 0,
  },
  {
    type: 'tosend',
    url: `/${p}/trade/order/list/index?type=tosend`,
    text: '待发货',
    count: 0,
  },
  {
    type: 'send',
    url: `/${p}/trade/order/list/index?type=send`,
    text: '已发货',
    count: 0,
  },
];

export const KAISHU_PLUGIN_LIST = [
  [
    {
      type: 'cart',
      text: '购物车',
    },
  ],
  [
    {
      type: 'cashBack',
      text: '我的返现',
      unOnce: true,
      url: `/${p}/user/cashback/list/index`,
    },
    {
      type: 'points',
      text: '我的积分',
      url: `/${p}/user/integral/index`,
    },
    {
      type: 'couponCard',
      text: '我的优惠券',
      url: `/${p}/user/coupon/list/index?type=promocard&title=我的优惠券`,
    },
    {
      type: 'couponCode',
      text: '我的优惠码',
      url: `/${p}/user/coupon/list/index?type=promocode&title=我的优惠码`,
    },
    {
      type: 'gift',
      text: '我的礼物',
      url: `/${p}/gift/goods-list/index`,
    },
    {
      type: 'saleman',
      text: '',
    },
    {
      type: 'paidContent',
      text: '我购买的专栏、内容',
      url: `/${p}/paidcontent/list/index`,
    },
  ],
  [
    {
      type: 'contact',
      text: '联系客服',
    },
  ],
];
export const BASE_LINK = {
  account: '',
  becomeMember: '',
  balance: `/${p}/pre-card/home/<USER>
  points: `/${p}/user/integral/index`,
  cards: `/${p}/card/list/index`,
  coupons: `/${p}/user/coupon/list/index?type=promocard&title=我的优惠券`,
};

export const LIANG_PIN_KDTID = 47999;

export const STATS_DEFAULT_LIST = [
  'points',
  'coupons',
  'cards',
  'balance',
  'change',
];

export const STATS_ICON_MAP = {
  points:
    'https://img01.yzcdn.cn/upload_files/2023/01/11/FqCr8afSlir-dr9b5sAFf33v0OtO.png',
  coupons:
    'https://img01.yzcdn.cn/upload_files/2023/01/11/FnuSWhqLFxVpKD2vtr_23ES-hjGB.png',
  cards:
    'https://img01.yzcdn.cn/upload_files/2023/01/11/Fu740Vp9QGdUqU1NjGSXmgtx56k-.png',
  balance:
    'https://img01.yzcdn.cn/upload_files/2023/01/11/FgAPP8h5Y8fZCW8RlYDseCmv0Twc.png',
  change:
    'https://img01.yzcdn.cn/upload_files/2023/01/11/FuElGCSkb5ja61r4Ywfo9QEbWPoS.png',
};

/**
 * * 会员等级模式
 */
export const LEVEL_MODE = {
  /** 成长值模式 */
  GROWTH: 1,
  /** 消费行为模式 */
  CONSUME: 2,
  /** 储值模式 */
  STORE: 3,
};
