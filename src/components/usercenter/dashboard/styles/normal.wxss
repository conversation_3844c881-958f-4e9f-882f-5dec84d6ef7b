.phone__wrapper {
  background-color: #f9f9f9;
}

/* 订单状态栏 */
.state-list {
  display: flex;
  justify-content: space-around;
  background: #fff;
  font-size: 12px;
}

.state-list__item {
  flex: 1;
  color: #333;
  text-align: center;
}

.state-list__icon {
  position: relative;
  margin: 0 auto 10px;
  width: 24px;
  height: 24px;
  color: #717171;
  font-size: 24px;
  line-height: 0;
}

.state-list__icon .icon__inner {
  width: 100%;
  height: 100%;
}

.cart-count {
  z-index: 10;
  display: inline-block;
  box-sizing: border-box;
  margin-right: 5px;
  padding: 0 .4em;
  min-width: 1.6em;
  height: 1.6em;
  border-radius: .8em;
  background: #f44;
  box-shadow: 0 0 0 2px #fff;
  color: #fff;
  vertical-align: middle;
  text-align: center;
  white-space: nowrap;
  font-size: 10px;
  line-height: 1.6;
}

.uc-normal__formid-btn {
  overflow: visible;
  background: transparent;
  text-align: left;

  appearance: none;
}

.uc-normal__form {
  display: block;
}

.btn-order-userinfo {
  padding: 4px 0;
  width: 100%;
}

.btn-order-userinfo button{
  display:flex;
  align-items:center;
  width:100%;
  text-align:left;
  font-size:14px;
  line-height:1;
}
.state-btn-container button{
  display:flex;
  flex-direction:column;
  padding: 20px 0;
  border-radius:0;
  background-color:#fff;
  text-align:center;
  font-size:12px;
  line-height:18px;
}
.btn-userinfo {
  display: inline-block;
  vertical-align: middle;
  padding: 0;
  border-radius:0;
  background-color:transparent;
  font-size: inherit;
  color: inherit;
  line-height: inherit;
  width: 50px;
  height: 30px;
  line-height: 30px;
  color: #fff;
  font-size: 12px;
}
.btn-userinfo::after {
  border: none;
}

.bind__container .bind__title{
  flex: 1;
  text-align: left;
}

.bind__container .bind__button {
  margin-right: 15px;
}
