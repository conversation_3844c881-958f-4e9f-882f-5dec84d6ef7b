.uc-fantasy .bind__container {
  margin-top: 10px;
  border: 0 none;
}

.uc-fantasy .bind__title {
  color: #acacac;
}

.uc-fantasy .bind__img {
  background-image: url('https://img01.yzcdn.cn/weapp/wsc/lRag0u.png');
}

.uc-status {
  display: flex;
  justify-content: space-around;
  font-size: 12px;
  background: #fff;
}

.uc-status__item {
  position: relative;
  flex: 1;
  text-align: center;
}

.uc-status__item::after {
  content: '';
  position: absolute;
  top: 21px;
  right: 0;
  width: 1px;
  height: 12px;
  background: #f7eceb;
}

.uc-status__item:last-child::after {
  display: none;
}

.uc-status__item .zan-badge__count {
  top: 10px;
  right: calc((100% - 36px) / 2 - 8px);
}

.uc-icons {
  margin-top: 10px;
  padding: 20px 40rpx;
  background: #fff;
}

.uc-icons__list {
  display: flex;
  justify-content: space-between;
  padding: 18px 0;
  flex-wrap: wrap;
}

.uc-icons__item {
  position: relative;
  padding: 40px 0 0 0;
  margin: 0;
  width: 70px;
  line-height: 20px;
  color: #acacac;
  font-size: 12px;
  text-align: center;
  background: #fff;
  margin-bottom: 10px;
}

.uc-icons__item::after {
  display: none !important;
}

.uc-icons__icon {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  width: 30px;
  height: 30px;
  background-image: url(https://img01.yzcdn.cn/weapp/wsc/L3FIV0G.png);
  background-size: 120px 90px;
}
.uc-icons__icon--points {
  background-position: 60px 60px;
}
.uc-icons__icon--address {
  background-position: 0px 0px;
}
.uc-icons__icon--cart {
  background-position: -30px 0px;
}
.uc-icons__icon--colletion {
  background-position: 0px -30px;
}
.uc-icons__icon--coupon {
  background-position: -30px -30px;
}
.uc-icons__icon-memeber-card {
  background-position: -90px 4px;
}
.uc-icons__icon--message {
  background-position: -60px 0px;
}
.uc-icons__icon--promotion-code {
  background-position: 0px -60px;
}
.uc-icons__icon--question {
  background-position: -30px -60px;
}
.uc-icons__icon--setting {
  background-position: -60px -60px;
}
.uc-icons__icon--cashback {
  background-image: none;
  font-size: 30px;
  color: #acacac;
  line-height: 30px;
}

.status-btn-container button{
  position: static;
  font-size:12px;
  text-align:center;
  border-radius:0;
  line-height:18px;
  padding: 18px 0; 
  color: #9a875f;
}

.uc-btn-container {
  display: flex;
  flex-direction: column;
  text-align:center;
  background-color:#fff;
  border-radius: 0;
}

.uc-btn-center button {
  position: relative;
  padding: 40px 0 0 0;
  margin: 0;
  width: 70px;
  line-height: 20px;
  color: #acacac;
  font-size: 12px;
  text-align: center;
  background: #fff;
  margin-bottom: 10px;
}

.btn-margin-left {
  margin-left: -15px;
}
.uc-text {
  font-size: 12px;
  display:block;
  margin-top:35px;
  color: #ACACAC;
}