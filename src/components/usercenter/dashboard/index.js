import WscComponent from 'shared/common/base/wsc-component/index';
import openWebView from 'utils/open-web-view';
import Event from '@youzan/weapp-utils/lib/event';
import { moment } from '@youzan/weapp-utils/lib/time';
import get from '@youzan/weapp-utils/lib/get';
import jumpLink from 'shared/components/showcase/utils/jumpLink';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import navigate from '@/helpers/navigate';
import theme from 'shared/common/components/theme-view/theme';
import formatDate from '@youzan/utils/date/formatDate';
import {
  checkPureWscShop,
  checkRetailShop,
  checkPureWscSingleStore,
  checkEduChainStoreV4,
} from '@youzan/utils-shop';
import tabNavigate, {
  nativeSwitchTab,
} from '@youzan/wsc-tab-bar-utils/lib/weapp/navigate';
import { onUserAuthSuccess } from '@youzan/passport-tee-shared';

import {
  KAISHU_STATS_LIST,
  KAISHU_PLUGIN_LIST,
  STAS_NAME,
  STAS,
  BASE_LINK,
  LIANG_PIN_KDTID,
  STATS_DEFAULT_LIST,
  LEVEL_MODE,
} from './constants';
import { ThresholdType } from 'components/mem-registry-threshold/constants';
import { fetchPromise, fetchFirstScreenData } from '@/base-api/usercenter';
import { getHeight } from 'shared/utils/nav-config';

const app = getApp();
const kaishuWhitelist = [19045118];
const p = 'packages';

WscComponent({
  properties: {
    themeFetched: {
      type: Boolean,
      value: false,
    },

    componentConfigsMap: Object,

    themeClass: {
      type: String,
      value: 'normal',
    },

    isPull: {
      type: Boolean,
      value: false,
      observer: 'refresh',
    },

    needUpdate: {
      type: Boolean,
      value: false,
      observer: 'isNeedUpdate',
    },

    isRetail: {
      type: Boolean,
      value: false,
    },

    userAgreePrivacy: {
      type: Boolean,
      value: false,
      observer: 'onUserAgreePrivacy',
    },
  },
  data: {
    kdtId: 0,
    userInfo: {},
    // 客服号码
    servicePhoneNumber: '',
    // 待付款
    topay: 0,
    // 代发货
    tosend: 0,
    // 待收货
    send: 0,
    hasCheckIn: false,
    showCheckIn: true,
    level: 0,
    points: 0,
    pointsDiff: 0,
    cartCount: 0,
    im: {
      businessId: '',
    },
    // 店铺是否开启了购物车
    isSetShoppingCart: true,
    isSetGroupCenter: false,
    groupName: '团长',
    bindTips: '登录手机号，同步全渠道订单和优惠券',
    showBindPhoneNumber: false,
    salesman: {},
    templateName: 'normal',
    isV2Balance: false,
    showLevel: true,
    user: {},
    shopInfo: {},
    config: {},
    globalConfig: {},
    globalData: {},
    backgroundImage: '',
    kaishuStatsList: KAISHU_STATS_LIST,
    kaishuPluginList: KAISHU_PLUGIN_LIST,
    statsNames: STAS_NAME,
    stats: STAS,
    orders: {
      toPay: 0,
      paid: 0,
      sent: 0,
    },
    promptOrderPay: {},
    promptOrderEvaluation: {},
    plugins: [],
    userLevel: '',
    // 会员等级开关是否禁用
    isEnable: true,
    pluginValues: {
      cart: 0,
      gifts: 0,
      drugList: 0,
    },
    baseLinks: BASE_LINK,
    orderLinks: {
      all: `/${p}/trade/order/list/index`,
      toPay: `/${p}/trade/order/list/index?type=topay`,
      toSend: `/${p}/trade/order/list/index?type=tosend`,
      toReceive: `/${p}/trade/order/list/index?type=send`,
      toEvaluate: `/${p}/trade/order/list/index?type=toevaluate`,
    },
    pluginLinks: {
      promotionCode: `/${p}/user/coupon/list/index?type=promocode&title=我的优惠码`,
      cashBack: `/${p}/user/cashback/list/index`, // 返现
      giftsCommunityVer: `/${p}/gift/goods-list/index`, // 礼物
      purchaseColumnAndContent: `/${p}/paidcontent/list/index`, // 知识付费
      accountSettings: `/${p}/account/settings/index`, // 账号设置暂时不用
      grouponFounder: `/${p}/groupbuying/activity/list/index`,
      taskCenter: `/${p}/user/task-center/index`,
      gifts: `/${p}/ump/presents/index`, // 赠品
      agentCenter: `/${p}/channel/agent/index`,
      blindBox: `/pages/common/webview-page/index?src=${encodeURIComponent(
        '/wscump/blind-box/list'
      )}&title=${encodeURIComponent('盲盒列表')}`,
      indentorCenter: `/${p}/channel/indentor/index`,
      ump_conference: `/${p}/ext-marketing-page/index`, // 营销会场
      drugList: `/${p}/trade/order/list/index?isDrug=true`,
      offlineStore: `/${p}/shop/physical-store/index`, // 线下门店
      exchangeGiftCard: `/${p}/pre-card/exchange/index?from=Usercenter`,
    },
    featureComponents: [],
    backgroundColor: '#f8f8f8',
    isSupportSign: false,
    isSigned: false,
    userCenterComponents: [],
    userCenterComponentsMap: {},
    isYzEdu: false,
    reminderInfos: [],
    appointments: [],
    noViewNum: 0,
    isReadWorkset: false,
    waitRewardCount: 0,
    reviewToReadNum: 0,
    momentsName: '家校圈',
    canShowMomentsFlag: false,
    showPop: false,
    certImg: '',
    certTemplateType: 'vertical',
    rewardCount: 0,
    rewardPopFlag: false,
    shopRewardPop: false,
    rewardGift: cdnImage(
      'https://img01.yzcdn.cn/public_files/2019/05/11/4ffe464d84f0b0b1f7c93724719b23b6.png'
    ),
    // 登录弹窗标志位
    accountLogin: false,
    levelGrowth: {},
    memberLevelGradeText: '',
    introduction: {},

    userauthBtnStyle: 'background-color: #ED0A23;',
    showUserAuthPopup: false, // 拉起授权弹窗
    showUserAuth: false, // 直接调授权组件
    userAuthPopupShopInfo: {
      image: '',
      name: '',
    },
    hiddenInfoCompleted: true, // 是否显示完善资料卡
    showActivatePop: false, // 激活权益卡弹窗
    benefitcard: {}, // 权益卡信息
    // 是否是零售店铺
    isRetail: false,
    /**
     * 是否支持激活权益卡弹窗，小红点功能
     */
    supportAdvancedBenefit: false,
    centerPhoneStatus: true,
    isPureWscSingleStore: false,
    benefitReminds: [],
    showGuideBecomingMember: false,
    guideBecomingMemberInfo: {},
    memThresholdType: ThresholdType.UNKNOWN,
    memThresholdValue: 0,
    isMember: false,
    showMemThresholdPopup: false,
    statsList: STATS_DEFAULT_LIST,
    // 是否是沉浸式效果
    isImmersive: false,
    immersiveCompletedInfoStyle: `top: ${getHeight() + 6}px;`,
  },

  attached() {
    const kdtId = this.getKdtId();
    if (!kdtId) {
      Event.once('shop:kdt_id:change', this.updateUCData, this);
    } else {
      this.setTemplate(kdtId);
      this.fetchUCData();
    }

    // 授权状态更新
    this.$_offAuth = onUserAuthSuccess(() => this.updateUserInfo());

    this.updateUserInfo();

    app.getImData().then((res) => {
      this.setYZData({
        'im.businessId': res.businessId || '',
      });
    });

    this.on('show', () => {
      this.setShopStatus();
    });
    const { shopMetaInfo = {}, chainStoreInfo = {} } =
      app.getShopInfoSync() || {};
    this.setYZData({
      isRetail: checkRetailShop(shopMetaInfo),
      supportAdvancedBenefit: checkPureWscShop(shopMetaInfo),
      isPureWscSingleStore: checkPureWscSingleStore(shopMetaInfo),
    });
    const { isEduShop } = chainStoreInfo;
    if (isEduShop) {
      Promise.all([
        app.getShopInfo(),
        theme.getThemeColor('general').catch(() => '#ED0A23'),
      ]).then(([shopInfo, color]) => {
        this.setYZData({
          userauthBtnStyle: `background-color: ${color};`,
          showUserAuth: true,
          userAuthPopupShopInfo: {
            image: shopInfo.logo,
            name: shopInfo.shop_name,
          },
        });
      });
    }
  },

  detached() {
    Event.off('shop:kdt_id:change', this.updateUCData, this);
    this.$_offAuth?.();
  },

  ready() {
    this.initSdk();
  },

  pageLifetimes: {
    show() {
      let showBindPhoneNumber = false;
      if (!app.getBuyerId()) {
        showBindPhoneNumber = true;
        this.on('app:token:success', () => {
          if (app.getBuyerId()) {
            this.setYZData({
              showBindPhoneNumber: false,
            });
          }
        });
      }
      this.setYZData({
        showBindPhoneNumber,
      });
      this.fetchStats();
    },
    hide() {
      clearInterval(this.intervalTimer);
      this.intervalTimer = null;
      this.off('app:token:success', null);
    },
  },

  methods: {
    updateUserInfo() {
      const kdtId = this.getKdtId();
      // 主动获取用户信息授权
      app.getUserInfo((res) => {
        this.getUserInfoOutOfSensitive(res.userInfo).then((userInfo) => {
          this.setYZData({
            userInfo,
            kdtId,
          });
        });
      });
    },
    updateUCData() {
      const kdtId = this.getKdtId();
      if (kdtId) {
        this.setTemplate(kdtId);
        this.fetchUCData();
      }
    },

    getKdtId(useOnlineKdtId) {
      const { chainStoreInfo = {} } = app.getShopInfoSync();
      const { isMultiOnlineShop } = chainStoreInfo;
      const kdtId = isMultiOnlineShop
        ? useOnlineKdtId
          ? app.getKdtId()
          : app.getHQKdtId()
        : app.getKdtId();
      return kdtId;
    },

    fetchUCData() {
      this.fetchCartCount();
      this.setShopStatus();
      this.fetchSalesmanData();
      this.fetchGroupCenterInfo();
      this.fetchCustomerService();
      // ES数据有延迟
      this.intervalCount = 1;
      this.intervalTimer = setInterval(() => {
        if (!this.intervalCount) {
          clearInterval(this.intervalTimer);
          this.intervalTimer = null;
          return;
        }
        this.intervalCount -= 1;
        this.fetchBuyerCount();
      }, 2000);

      if (this.isNormalTemplate()) {
        this.initData();
      }
    },

    fetchStats() {
      const params = this.getInitAndGlobalParams();
      fetchFirstScreenData(params)
        .then((data) => {
          if (data) {
            const handledData = this.handleInitData(data);
            this.setYZData(handledData);
          }
        })
        .catch(() => {});
    },
    setTemplate(kdtId) {
      if (kaishuWhitelist.indexOf(+kdtId) > -1) {
        this.setYZData({
          templateName: 'kaishu',
        });
        this.triggerEvent('setTemplate', 'kaishu');
      }

      this.setYZData({
        kdtId,
      });
    },

    isNormalTemplate() {
      const kdtId = this.getKdtId();
      if (kaishuWhitelist.indexOf(+kdtId) > -1) return false;
      return true;
    },

    onContactBack: navigate.contactBack,

    setShopStatus() {
      app.getShopConfigData((shopConfig) => {
        const isSetShoppingCart = +shopConfig.hide_shopping_cart !== 1;
        this.setYZData({
          isSetShoppingCart,
        });
      });
    },

    refresh() {
      this.fetchBuyerCount();
      if (this.isNormalTemplate()) {
        this.initData(true);
      }
    },

    fetchBuyerCount() {
      app.carmen({
        api: 'kdt.trade.buyer.count/1.0.2/get',
        success: (res) => {
          const format = (num) => (num > 99 ? '99+' : num);
          this.setYZData({
            topay: format(res.topay),
            tosend: format(res.tosend),
            send: format(res.send),
          });
        },
        complete: () => {
          wx.stopPullDownRefresh();
        },
      });
    },
    fetchGroupCenterInfo() {
      app
        .request({
          path: 'wscuser/membercenter/groupCenterInfo.json',
          method: 'GET',
          data: {
            kdt_id: this.getKdtId(),
          },
        })
        .then((res) => {
          this.setYZData({
            isSetGroupCenter: res.isShow,
            groupName: res.name,
          });
        });
    },

    // 获取购物车数量
    fetchCartCount() {
      app.carmen({
        api: 'kdt.trade.cart/1.0.0/count',
        data: {
          kdt_id: this.getKdtId(),
        },
        success: (res) => {
          this.setYZData({
            cartCount: +res.data,
          });
        },
      });
    },
    // 跳转到专属导购
    jumpToExclusiveGuide() {
      const title = '专属导购';
      const h5URL = `https://h5.youzan.com/guide/customer/exclusive-guide/home?kdt_id=${this.getKdtId()}`;
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(
          h5URL
        )}&title=${title}`,
      });
    },
    // 跳转会员权益卡页面
    jumpToMemberBenefit() {
      navigate.navigate({ url: `/${p}/member-benefit/benefit/index` });
    },
    // 跳转收获地址
    jumpToDeliveryAddress() {
      const dbid = app.db.set({ switchable: false });
      navigate.navigate({
        url: `/${p}/order-native/address-list/index?dbid=${dbid}`,
      });
    },
    // 跳转会员等级页面
    jumpToMemberLevel() {
      const { levelName, payLevelName, freeLevelName } = this.data.levelGrowth;
      if (!levelName) {
        // todo 用户和商家都没有免费和付费会员等级，不跳转
        return;
      }

      if (payLevelName) {
        // todo 跳转付费会员等级页
        return navigate.navigate({ url: `/${p}/levelcenter/plus/index` });
      }

      if (freeLevelName) {
        // todo 跳转免费会员等级页
        navigate.navigate({ url: `/${p}/levelcenter/free/index` });
      }
    },
    // 跳转考试页面
    jumpToMemberExam() {
      navigate.navigate({
        url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
          `https://h5.youzan.com/wscvis/supv/examination/list?kdt_id=${this.getKdtId()}`
        )}`,
      });
    },
    // 跳转证书页面
    jumpToMemberCertificate() {
      navigate.navigate({
        url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
          `https://h5.youzan.com/wscvis/edu/certificate/cert-list?kdt_id=${this.getKdtId()}`
        )}`,
      });
    },
    // 跳转我的作业页面
    jumpToMemberExerciseBook() {
      navigate.navigate({
        url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
          `https://h5.youzan.com/wscvis/supv/homework/list?kdt_id=${this.getKdtId()}`
        )}`,
      });
    },
    // 跳转活动中心页面
    jumpToMemberEduReferral() {
      navigate.navigate({
        url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
          `https://h5.youzan.com/wscvis/ump/activity-entry?kdt_id=${this.getKdtId()}`
        )}`,
      });
    },
    // 成长值
    jumpToFreeMemberLevel() {
      // todo
      navigate.navigate({ url: `/${p}/levelcenter/free/index` });
    },
    // 个人设置页面
    jumpToCustomerSettings() {
      navigate.navigate({ url: `/${p}/user/membercenter/setting/index` });
    },
    // 订单退款维权
    jumpToOrderRefund() {
      navigate.navigate({ url: `/${p}/trade/refund/list/index` });
    },

    jumpToRelationInfo() {
      const h5URL = `https://h5.youzan.com/wscuser/scrm/relationPerson`;
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(
          h5URL
        )}`,
      });
    },
    jumpToLink({ detail }) {
      jumpLink(detail, true);
    },

    handleRegistryThreshold({ detail = {} } = {}) {
      const {
        memThresholdType = ThresholdType.UNKNOWN,
        memThresholdValue = 0,
      } = detail;
      this.setYZData({
        memThresholdType,
        memThresholdValue,
        showMemThresholdPopup: true,
      });
    },

    statsItemClicked(e) {
      const type = e.detail;
      if (type === 'change') {
        openWebView('/wscassets/change/profile');
      }
      if (type === 'cards') {
        // 点击后默认取消小红点
        this.setYZData({ 'stats.showCardDot': false });
        app.request({
          path: '/wscuser/scrm/api/benefitcard/cleanNewCardTag',
        });
      }
    },

    getInactiveCard() {
      const kdtId = this.getKdtId();
      const cDay = formatDate(new Date(), 'YYYY-MM-DD');
      const key = `hasUnActivateBenefitcard_${kdtId}`;
      const value = wx.getStorageSync(key);
      try {
        if (!value || !JSON.parse(value)[cDay]) {
          app
            .request({
              path: '/wscuser/scrm/api/benefitcard/getInactiveCard',
            })
            .then((res) => {
              if (res?.cardAlias) {
                this.setYZData({ benefitcard: res, showActivatePop: true });
                wx.setStorage({
                  key,
                  data: JSON.stringify({ [cDay]: 1 }),
                });
              } else {
                this.setYZData({ showActivatePop: false, benefitcard: {} });
              }
            });
        }
      } catch (err) {
        // Nothing
        console.warn(err);
      }
    },

    guideBecomingMember() {
      const kdtId = this.getKdtId();
      const cDay = formatDate(new Date(), 'YYYY-MM-DD');
      const key = `guideBecomingMember_${kdtId}`;
      const value = wx.getStorageSync(key);

      app
        .resolveTeeAPI()
        .then((api) => api.getUserPrivacy())
        .then(({ mobile }) => {
          try {
            if ((!value || !JSON.parse(value)[cDay]) && !mobile) {
              // 查询入会信息，返回预计入会礼包信息
              app
                .request({
                  path: '/wscuser/membercenter/guideBecomingMemberInfo.json',
                })
                .then(({ sceneAuth = false, ...extra }) => {
                  // 零售店铺的 guide-becoming-member 组件根据产品 @张晴 的要求不要唤起头像授权（即不受后台“用户授权“头像授权控制），故
                  // 不需要判断 sceneAuth 值，其他店铺类型保持现状仍受 sceneAuth 控制
                  // 不在接口侧直接修改 sceneAuth 的原因是不确定该接口是否在其他地方有使用，无法确定影响面，故将以下判断写于此
                  const didSceneAuthPassed =
                    checkRetailShop(app.getShopInfoSync()) || sceneAuth;
                  // 入会场景的配置存在
                  if (didSceneAuthPassed) {
                    app.logger &&
                      app.logger.log({
                        et: 'custom', // 事件类型
                        ei: 'view_uc_guide_becoming_member', // 事件标识
                        en: '曝光个人中心入会引导', // 事件名称
                        pt: 'uc', // 页面类型
                        params: {
                          has_gift: extra?.hasGiftBag ? 1 : 0,
                        }, // 事件参数
                      });
                    wx.setStorage({
                      key,
                      data: JSON.stringify({ [cDay]: 1 }),
                    });
                    this.setYZData({
                      showGuideBecomingMember: true,
                      guideBecomingMemberInfo: extra,
                    });
                  }
                });
            }
          } catch (err) {
            // Nothing
            console.warn(err);
          }
        });
    },

    onGuideBecomingMemberError(errorList) {
      if (
        errorList &&
        Array.isArray(errorList) &&
        errorList.length &&
        errorList.includes(['mobileDeny'])
      ) {
        this.onGuideBecomingMemberClose();
      }
    },

    handleCloseMemThresholdPopup() {
      this.setYZData({
        showMemThresholdPopup: false,
      });
    },

    onGuideBecomingMemberClose(ev) {
      app.logger &&
        app.logger.log({
          et: 'custom', // 事件类型
          ei: 'uc_guide_becoming_member_close', // 事件标识
          en: '个人中心入会引导关闭', // 事件名称
          pt: 'uc', // 页面类型
        });
      this.setYZData({
        showGuideBecomingMember: false,
        guideBecomingMemberInfo: {},
      });

      // 用户主动取消授权，非拒绝授权
      if (!ev || !ev.detail) {
        return;
      }

      // 如果是会员，直接返回
      if (this.data.isMember) {
        return;
      }

      const getFreeLevelGiftBag = () => {
        return app
          .request({
            path: '/wscuser/level/api/getLevelGiftBag.json',
          })
          .then(({ couponList = [], presentList = [], points = {} } = {}) => {
            return (
              couponList.length > 0 || presentList.length > 0 || !!points.points
            );
          })
          .catch(() => {
            return false;
          });
      };

      const checkMemRegistryState = () => {
        // 仅在用户完成授权的情况下检查入会状态，门槛等信息
        return app
          .request({
            path: '/wscuser/level/api/checkMemRegistrationState.json',
          })
          .then(({ hasCondition = false, needFillInfo = false } = {}) => {
            return !hasCondition && !needFillInfo;
          })
          .catch(() => {
            return false;
          });
      };
      Promise.all([getFreeLevelGiftBag(), checkMemRegistryState()]).then(
        ([hasGiftBag, isFreeMember]) => {
          // isFreeMember 由 hasCondition 和 needFillInfo 决定
          // 如果没有门槛 且 无需填写资料，则认为是免费会员等级1

          // 在有礼包，且现在是会员的时候才提示
          if (hasGiftBag && isFreeMember) {
            // 如果有礼包，则提示欢迎入会，已领取新人礼
            wx.showToast({
              title: '欢迎入会\r\n已领取新人礼',
              icon: 'none',
            });
          }
        }
      );
    },

    fetchBenefitRemind() {
      app
        .request({
          path: '/wscuser/level/api/queryPlanRemind.json',
        })
        .then((res) => {
          if (res?.content) {
            const { benefitReminds, userCenterComponents = [] } = this.data;
            benefitReminds.push({
              text: res.content,
              id: res.planId,
            });
            const statsIndex = userCenterComponents.findIndex(
              (item) => item.type === 'uc_stats'
            );
            if (statsIndex > -1) {
              userCenterComponents.splice(statsIndex, 0, {
                type: 'uc_benefit',
              });

              this.setYZData({ benefitReminds, userCenterComponents });
            }
          }
        });
    },

    getNewCardTag() {
      app
        .request({
          path: '/wscuser/scrm/api/benefitcard/getNewCardTag',
        })
        .then((res) => {
          this.setYZData({ 'stats.showCardDot': !!res.value });
        });
    },

    fetchCustomerService() {
      app.carmen({
        api: 'weapp.wsc.shop.returnaddress/1.0.0/get',
        data: {
          kdt_id: this.getKdtId(),
        },
        success: (res) => {
          let servicePhoneNumber = '';
          if (+res.show_notice_mobile) {
            if (res.notice_phone2) {
              servicePhoneNumber = res.notice_phone2;
              // 如果有电话前缀，就需要拼接
              if (res.notice_phone1) {
                servicePhoneNumber =
                  res.notice_phone1 + '-' + servicePhoneNumber;
              }
            } else if (res.notice_mobile) {
              servicePhoneNumber = res.notice_mobile;
            }
          }
          this.setYZData({ servicePhoneNumber });
        },
      });
    },
    // 个人信息脱敏处理 手机号显示111****1111 格式
    getUserInfoOutOfSensitive(userInfo) {
      return new Promise((resolve) => {
        wx.getStorage({
          key: 'app:token',
          success: (res) => {
            const token = res.data;
            const phone = get(token, 'mobile', '');
            const nickName = get(userInfo, 'nickName', '');
            if (nickName === phone && nickName.length === 11) {
              userInfo.nickName = `${nickName.substr(
                0,
                3
              )}****${nickName.substr(7, 10)}`;
            }
            resolve(userInfo);
          },
        });
      });
    },

    onGetUserInfo(e) {
      const userInfo = get(e, 'detail.userInfo', {});
      this.setYZData({
        isNotValid: e.detail && e.detail.isNotValid,
      });

      if (e && e.detail && e.detail.userInfo) {
        const kdtId = getApp().getKdtId();
        this.getUserInfoOutOfSensitive(userInfo).then((res) => {
          this.setYZData({
            userInfo: res,
            kdtId,
          });
        });
      }
    },

    handleTapUserInfo(e) {
      const key = e.detail;
      switch (key) {
        case 'level':
          this.jumpToMemberLevel();
          break;
        case 'growth':
          this.jumpToFreeMemberLevel();
          break;
        case 'account':
          this.jumpToCustomerSettings();
          break;
        default:
          break;
      }
    },

    handleImageChange({ detail }) {
      const { userCenterComponents = [] } = this.data;
      const adImages = userCenterComponents.find(
        (item) => item.type === 'uc_image_ad'
      );
      // 存在流量互换标记
      if (adImages && adImages.is_has_ad_exchange && detail !== undefined) {
        const { images = [] } = adImages;
        if (images[detail]) {
          const { psCode, cooperateKdtId } = images[detail];
          this.adExchangeLogger({ psCode, cooperateKdtId });
        }
      }
    },

    adExchangeLogger({ psCode, cooperateKdtId }) {
      if (psCode && cooperateKdtId) {
        app.logger &&
          app.logger.log({
            et: 'view', // 事件类型
            ei: 'ad_exchange_view', // 事件标识
            en: '图片曝光', // 事件名称
            params: {
              ps_code: psCode,
              activity_kdt_id: cooperateKdtId,
              component: 'ad_exchange_banner',
            }, // 事件参数
          });
      }
    },

    handleSign() {
      const isSupportSign = get(this.data, 'isSupportSign');
      if (!isSupportSign) {
        return;
      }
      return navigate.navigate({ url: `/${p}/shop/ump/sign-in/index` });
    },

    handleSalesmanClick() {
      if (!app.getBuyerId()) {
        // 没有绑定手机号
        return wx.navigateTo({
          url: `/${p}/salesman/tutorial/index`,
        });
      }
      const { salesman } = this.data;
      // 若不是销售员并且没有被清退过，直接进入招募计划
      if (!salesman.salesman && !salesman.fired) {
        return wx.navigateTo({
          url: `/${p}/salesman/tutorial/index`,
        });
      }
      // 跳转招募计划或者销售员中心
      wx.navigateTo({
        url: `/${p}/salesman/salesman-center/index`,
      });
    },

    handleGuideClick() {
      // 跳转导购工作台
      const guideURL = `https://h5.youzan.com/guide/center/home?kdt_id=${this.getKdtId()}`;
      const title = '导购工作台';
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(
          guideURL
        )}&title=${title}`,
      });
    },

    jumpCouponDetail(source) {
      navigate.navigate({
        url:
          `/${p}/user/coupon/list/index?type=` +
          source +
          '&title=' +
          (source === 'promocard' ? '我的优惠券' : '我的优惠码'),
      });
    },

    handleContactCustomerService() {
      wx.showModal({
        title: this.data.servicePhoneNumber,
        confirmText: '呼叫',
        success: (res) => {
          if (res.confirm) {
            wx.makePhoneCall({
              phoneNumber: this.data.servicePhoneNumber,
            });
          }
        },
      });
    },

    tapBindZanAccount() {
      this.setYZData({
        accountLogin: true,
      });
    },

    onAccountSuccess() {
      this.setYZData({ showBindPhoneNumber: false, accountLogin: false });
      if (this.isNormalTemplate()) {
        this.initData();
      }

      this.fetchBuyerCount();
      this.fetchCartCount();
      this.setShopStatus();
      this.fetchGroupCenterInfo();
    },

    onAccountClose() {
      this.setYZData({
        accountLogin: false,
      });
    },

    // 读取会员中心的销售员设置相关配置
    fetchSalesmanData() {
      app.carmen({
        api: 'youzan.salesman.wap.account/1.0.0/get',
        success: (resp) => {
          let list = KAISHU_PLUGIN_LIST[1];
          list = list.map((item) => {
            if (item.type === 'saleman') {
              item.text = `${resp.settingName || '分销员'}中心`;
            }
            return item;
          });
          this.setYZData({
            salesman: resp,
            'kaishuPluginList[1]': list,
          });
        },
      });
    },

    initData(isRefresh = false, updateInitData = false) {
      this.fetchGlobalData(updateInitData)
        .then(() => {
          this.triggerEvent('setFetchUcDataComplete');
          isRefresh ? wx.stopPullDownRefresh() : '';
          this.fetchOrderPrompt();
          this.fetchPointsName();
          this.fetchChannelAgentInfo();
          this.fetchBlindBoxInfo();
          this.fetchChannelIndentorInfo();
          this.fetchBenefitRemind();
          if (this.data.supportAdvancedBenefit) {
            this.getInactiveCard();
            this.getNewCardTag();
          }

          const title = this.data.globalConfig.title || '会员中心';
          this.triggerEvent('setTitle', title);

          const fxPlugin = this.data.plugins.filter(
            (i) => i.type === 'fxCenter'
          )[0];
          const gfPlugin = this.data.plugins.filter(
            (i) => i.type === 'grouponFounder'
          )[0];

          if (fxPlugin) {
            const salesmanName =
              this.data.user.salesman.settingName || '分销员';
            for (let i = 0; i < this.data.plugins.length; i += 1) {
              if (this.data.plugins[i].type === 'fxCenter') {
                this.setYZData({
                  [`plugins[${i}].title`]: `${salesmanName}中心`,
                });
              }
            }
          }

          if (gfPlugin) {
            const groupName =
              this.data.user.grouponFounder.groupName || '团长中心';
            for (let i = 0; i < this.data.plugins.length; i += 1) {
              if (this.data.plugins[i].type === 'grouponFounder') {
                this.setYZData({
                  [`plugins[${i}].title`]: groupName,
                });
              }
            }
          }

          if (this.data.plugins.filter((i) => i.type === 'cart')[0]) {
            this.fetchCartNum();
          }

          if (this.data.plugins.filter((i) => i.type === 'gifts')[0]) {
            this.fetchPresentCount();
          }

          if (this.data.plugins.filter((i) => i.type === 'merchantsCall')[0]) {
            this.fetchContact();
          }
        })
        .catch(() => {
          this.triggerEvent('setFetchUcDataComplete');
          isRefresh ? wx.stopPullDownRefresh() : '';
          wx.showToast({
            title: '网络异常！',
            icon: 'none',
          });
        });
    },
    // 获取渠道版代理商入口信息
    fetchChannelAgentInfo() {
      app
        .request({
          path: '/wscsalesman/channels/common-api/getChannelEntrance.json',
          method: 'get',
          data: {
            type: 1,
          },
        })
        .then((res) => {
          if (res && res.isOpen) {
            const fxIndex = this.data.plugins.findIndex(
              (e) => e.type === 'fxCenter'
            );
            const temp = this.data.plugins.slice();
            const agentCenter = {
              title: res.name,
              type: 'agentCenter',
            };
            if (fxIndex !== -1) {
              temp.splice(fxIndex, 0, agentCenter);
            } else {
              temp.unshift(agentCenter);
            }
            this.setYZData({
              plugins: temp,
            });
          }
        });
    },
    // 获取 我的盲盒 信息
    fetchBlindBoxInfo() {
      app
        .request({
          path: '/wscump/blind-box/checkBlindBoxEntrance.json',
          method: 'get',
        })
        .then((res) => {
          if (res && res.value) {
            const temp = this.data.plugins.slice();
            temp.unshift({
              title: '我的盲盒',
              type: 'blindBox',
            });
            this.setYZData({
              plugins: temp,
            });
          }
        })
        .catch(() => {});
    },
    // 获取渠道版订货商入口信息
    fetchChannelIndentorInfo() {
      app
        .request({
          path: '/wscsalesman/channels/common-api/getChannelEntrance.json',
          method: 'get',
          data: {
            type: 2,
          },
        })
        .then((res) => {
          if (res && res.isOpen) {
            const fxIndex = this.data.plugins.findIndex(
              (e) => e.type === 'fxCenter'
            );
            const temp = this.data.plugins.slice();
            const indentorCenter = {
              title: res.name,
              type: 'indentorCenter',
            };
            if (fxIndex !== -1) {
              temp.splice(fxIndex, 0, indentorCenter);
            } else {
              temp.unshift(indentorCenter);
            }
            this.setYZData({
              plugins: temp,
            });
          }
        });
    },

    onPluginItemClicked(e) {
      const { detail } = e;
      switch (detail) {
        case 'cart': // 购物车
          tabNavigate({
            path: '/pages/goods/cart/index',
            fail() {
              navigate.switchTab({
                url: `/${p}/goods/cart/index`,
              });
            },
          });
          break;
        case 'paidContentExamination':
          this.jumpToMemberExam();
          break;
        case 'paidContentCertificate':
          this.jumpToMemberCertificate();
          break;
        case 'paidContentExerciseBook':
          this.jumpToMemberExerciseBook();
          break;
        case 'paidContentEduReferral':
          this.jumpToMemberEduReferral();
          break;
        case 'deliveryAddress':
          this.jumpToDeliveryAddress();
          break;
        case 'merchantsCall': // 拨打商家电话
          this.handleContactCustomerService();
          break;
        case 'customerSettings': // 拨打商家电话
          this.jumpToCustomerSettings();
          break;
        case 'fxCenter': // 分销员中心
          this.handleSalesmanClick();
          break;
        case 'dgCenter': // 导购工作台
          this.handleGuideClick();
          break;
        case 'refund': // 订单-退款维权
          this.jumpToOrderRefund();
          break;
        case 'scrmRelationInfo':
          this.jumpToRelationInfo();
          break;
        case 'exclusiveGuide':
          this.jumpToExclusiveGuide();
          break;
        case 'memberCode': {
          const { tabbarOriginList } = app.globalData;
          const memberCodePath = `${p}/member-code/index`;
          const index = tabbarOriginList.findIndex(
            (item) => item.pagePath === memberCodePath
          );
          nativeSwitchTab(tabbarOriginList, {
            tabBarIndex: index,
            query: { navIndex: index },
            fail: () => {
              navigate.navigate({
                url: `/${memberCodePath}`,
              });
            },
          });
          break;
        }
        default:
          break;
      }
    },

    checkEduServiceEntry(target, name) {
      let isFound = false;
      target.forEach((el) => {
        if (el.type === 'edu_services') {
          el.plugins.forEach((ell) => {
            if (ell.type === name) {
              isFound = true;
            }
          });
        }
      });
      return isFound;
    },

    getInitAndGlobalParams() {
      return {
        version: app.getVersion(),
        kdtId: this.getKdtId(),
        onlineKdtId: app.getKdtId(),
      };
    },

    fetchGlobalData(updateInitData = false) {
      let promise;
      if (app.globalData.usercenterPromise && !updateInitData) {
        promise = app.globalData.usercenterPromise;
      } else {
        const params = this.getInitAndGlobalParams();
        promise = fetchPromise(params);
      }

      return promise
        .then(([globalData, initData]) => {
          const { shopInfo, userCenterConfigV2 } = globalData;
          const { pageId } = userCenterConfigV2 || {};
          const { kdtId } = shopInfo;
          const { user } = globalData;
          const userLevel = user.level.name || '';

          this.triggerEvent('setPageId', pageId || 0);

          if (this.getKdtId() === LIANG_PIN_KDTID) {
            this.setYZData({
              memberLevelGradeText: userLevel,
            });
          }

          const levelMode = get(initData, 'level.mode', '');
          if (levelMode === LEVEL_MODE.GROWTH) {
            this.setYZData({ isSupportGrowthValue: true });
          }

          let plugins = globalData.plugins || [];
          const components = globalData.components || [];
          const { isV2Balance } = globalData;
          // 是否是有赞教育店铺
          const isYzEdu = globalData.isYZEdu;
          // 判断是否是微商城单店并且判断知识店铺能力或者教务管理任意一个能力有效即可
          const isYzWscSingleStore = get(globalData, 'isYzSingleWsc', false);
          const isOpenEduCard =
            get(globalData, 'manageAbilaty.valid', false) ||
            get(globalData, 'PctAbility.valid', false);

          // 是否是有赞零售店铺
          if (this.data.isRetail) {
            plugins = plugins.reduce((arr, val) => {
              if (val.type === 'memberCode') {
                // 如果没有会员身份标示 不展示会员码插件
                const identityNo = get(user, 'level.identityNo', '');
                if (!identityNo) {
                  return arr;
                }
              }
              return arr.concat([val]);
            }, []);
          }
          // 新版储值跳转新版储值
          if (isV2Balance) {
            this.setYZData({
              'baseLinks.balance': `/${p}/pre-card/home/<USER>
            });
          }

          const config = components.filter(
            (i) => i.type === 'shop_usercenter'
          )[0] || {
            type: 'shop_usercenter',
            pageStyle: 1,
            backgroundImage: '',
            memberLevel: 1,
            headPosition: 1,
            backgroundGradient: 0,
          };
          const globalConfig = components.filter(
            (i) => i.type === 'config'
          )[0] || {
            type: 'config',
            title: '个人中心',
          };
          // 个人设置 temp
          plugins.push({ title: '个人信息', type: 'customerSettings' });
          plugins.push({ title: '账号设置', type: 'accountSettings' });
          const backgroundImage =
            config.backgroundImage || get(globalConfig, 'image.url', '') || '';
          const isImmersiveConfig = get(globalConfig, 'is_immersive', 0);
          const isImmersive = isImmersiveConfig === 1;
          const backgroundColor = globalData.background || '#f8f8f8';
          const featureComponents = (
            globalData.feature_components || []
          ).filter((comp) => comp.type !== 'shop_usercenter');
          let userCenterComponents = get(
            globalData,
            'userCenterComponents',
            []
          );
          const isSupportSign = get(
            globalData,
            'supportSignInfo.isOpen',
            false
          );
          const isSigned = get(globalData, 'isCheckin', false);
          const showLevel = Boolean(
            get(globalData, 'userCenterComponents[0].showLevel', true)
          );
          const userInfoCom =
            userCenterComponents.filter((i) => i.type === 'uc_user_info')[0] ||
            {};

          userInfoCom.memberType = userInfoCom.memberType || 1;
          this.setYZData({
            memberType: userInfoCom.memberType,
          });
          let userImageAdIndex = -1;
          userCenterComponents = userCenterComponents.map((item, index) => {
            if (item.type === 'uc_image_ad') {
              item.images = mapKeysCase.toCamelCase(item.images);
              userImageAdIndex = index;
              // 如果只有一个图片广告位，判断一下是不是流量互换的
              if (item.images.length === 1) {
                const { psCode, cooperateKdtId } = item.images[0];
                this.adExchangeLogger({ psCode, cooperateKdtId });
              }
            }
            return item;
          });

          if (userImageAdIndex > -1) {
            userCenterComponents.splice(userImageAdIndex + 1, 0, {
              type: 'uc_flow_entrance',
            });
          }

          // 有赞教育服务卡片
          const { chainStoreInfo = {} } = app.getShopInfoSync();
          const { isEduShop, isEduChainStore } = chainStoreInfo;

          // 有赞教育课程提醒 + 跳转模块
          if (isYzEdu || isYzWscSingleStore) {
            const EDU_SERVICES = [
              {
                type: 'appointment',
                title: '预约上课',
              },
              {
                type: 'student-card',
                title: '学生证',
              },
              {
                type: 'moments',
                title: '家校圈',
              },
              {
                type: 'appointment-records',
                title: '预约记录',
              },
              {
                type: 'study-records',
                title: '学习记录',
              },
              {
                type: 'schedule',
                title: '查看课表',
              },
              {
                type: 'owned',
                title: '我的课程',
              },
              {
                type: 'certificate',
                title: '证书',
              },
              {
                type: 'reward',
                title: '奖励',
              },
              {
                type: 'family-account',
                title: '家庭账号',
              },
              {
                type: 'activity-entry',
                title: '活动中心',
              },
              {
                type: 'exam',
                title: '考试',
              },
              {
                type: 'homework',
                title: '我的作业',
              },
              {
                type: 'workset',
                title: '作品集',
              },
              {
                type: 'leave',
                title: '请假',
              },
            ];

            let eduServicesCustomed = 0;
            let eduUserComponentsIndex = -1;
            const { shopMetaInfo = {} } = app.getShopInfoSync() || {};
            const isEduChainStoreV4 = checkEduChainStoreV4(shopMetaInfo);
            userCenterComponents.forEach((item, index) => {
              if (item.type === 'edu_services') {
                // undefined时为已定制，0为未定制
                eduServicesCustomed = item.customed;
                eduUserComponentsIndex = index;
              }
            });
            const version = globalData.versionStatus.versionCode;
            // 仅在能找到教育卡片的情况下处理
            if (eduUserComponentsIndex !== -1) {
              if (eduServicesCustomed === 0) {
                // 未定制情况下的过滤规则
                userCenterComponents[eduUserComponentsIndex].plugins =
                  EDU_SERVICES.filter((item) => {
                    if (isYzWscSingleStore) return true;
                    if (item.type === 'exam') {
                      // 7988版和多校区过滤考试
                      if (version === 'edu_version' || isEduChainStore) {
                        return false;
                      }
                      return true;
                      // eslint-disable-next-line no-else-return
                    } else if (
                      item.type === 'homework' &&
                      globalData.isInHomeworkGrayList
                    ) {
                      // 基础版，专业本，多校区支持考试
                      if (
                        version === 'edu_base_version' ||
                        version === 'edu_profession_version' ||
                        isEduChainStore
                      ) {
                        return true;
                      }
                      return false;
                    } else if (item.type === 'workset') {
                      // 基础版，专业本，多校区支持作品集
                      if (
                        version === 'edu_base_version' ||
                        version === 'edu_profession_version' ||
                        isEduChainStore
                      ) {
                        return true;
                      }
                      return false;
                    } else if (item.type === 'leave') {
                      if (
                        version === 'edu_profession_version' ||
                        isEduChainStoreV4
                      ) {
                        return true;
                      }
                      return false;
                    }
                    return true;
                  });
                if (!isOpenEduCard) {
                  userCenterComponents[eduUserComponentsIndex].plugins = [];
                }
              } else {
                // 定制情况下的过滤规则
                userCenterComponents[eduUserComponentsIndex].plugins =
                  userCenterComponents[eduUserComponentsIndex].plugins.filter(
                    (item) => {
                      if (item.type === 'homework') {
                        return globalData.isInHomeworkGrayList && item.selected;
                      }
                      return item.selected;
                    }
                  );
              }
            }

            this.fetchAppoint();
            this.fetchCertificate();
            this.checkEduServiceEntry(userCenterComponents, 'workset') &&
              this.fetchWorkset();
            this.fetchMomentsNotice();
            this.fetchReward();
            this.fetchReviewToReadNum();
            this.getPoster();
            this.fetchMomentsName();

            app
              .request({
                path: '/wscuser/membercenter/course.json',
                method: 'get',
                data: {
                  kdtId: this.getKdtId(),
                },
              })
              .then((data) => {
                if (data.length > 0) {
                  const lessonName = data[0].lessonName || '';
                  const courseAttendDTO = data[0].courseAttendDTO || {};
                  const courseAddress = courseAttendDTO.address || '-';
                  const { startTime } = data[0];
                  const { endTime } = data[0];
                  let courseTime = '-';
                  if (startTime > 0) {
                    courseTime =
                      moment(startTime, 'YYYY-MM-DD HH:mm') +
                      '-' +
                      moment(endTime, 'HH:mm');
                  }
                  const reminderInfos = [
                    {
                      show: true,
                      type: 'course',
                      typeName: '课程提醒',
                      moreName: '查看课程表',
                      title: lessonName,
                      time: courseTime,
                      address: courseAddress,
                      url: `/${p}/edu/webview/index?targetUrl=${encodeURIComponent(
                        'https://h5.youzan.com/wscvis/edu/course-schedule'
                      )}`,
                    },
                  ];
                  this.setYZData({ reminderInfos });
                } else {
                  const reminderInfos = [
                    {
                      type: 'course',
                      typeName: '课程提醒',
                      moreName: '查看课程表',
                      url: `/${p}/edu/webview/index?targetUrl=${encodeURIComponent(
                        'https://h5.youzan.com/wscvis/edu/course-schedule'
                      )}`,
                    },
                  ];
                  this.setYZData({ reminderInfos });
                }
              });
          }
          if (isEduShop) {
            let statsIndex;
            userCenterComponents.forEach((item, index) => {
              if (item.type === 'uc_stats') {
                statsIndex = index;
              }
            });
            if (statsIndex) {
              userCenterComponents.splice(statsIndex + 1, 0, {
                type: 'uc_introduction',
              });
            }
            app
              .request({
                path: '/wscuser/membercenter/getIntroductionActivity.json',
                method: 'get',
                data: {
                  kdtId: this.getKdtId(),
                },
              })
              .then((data) => {
                const { activity } = data || {};
                // 如果有进行中的转介绍活动
                this.setYZData({
                  introduction: activity || {},
                });
              });
          }

          const userCenterComponentsMap = {};
          // eslint-disable-next-line no-undef
          if (ECLOUD_MODE) {
            userCenterComponents.forEach((item) => {
              userCenterComponentsMap[item.type] = item;
            });
          }

          if (plugins.find((item) => item.title === '需求清单')) {
            // 医药店铺请求 需求清单 角标数量
            const params = {
              method: 'GET',
              data: {
                kdtId: this.getKdtId(),
                headKdtId: app.getHQKdtId(),
                orderTags: 'IS_PRESCRIPTION_DRUG_ORDER',
                states: 'CONFIRM',
              },
            };
            app
              .request({
                path: '/wscuser/membercenter/drugList.json',
                ...params,
              })
              .then((count) => {
                this.setYZData({
                  pluginValues: {
                    ...this.data.pluginValues,
                    drugList: count?.totalMap?.CONFIRM || 0,
                  },
                });
              })
              .catch((err) => {
                console.error('获取需求清单列表数量失败', err);
              });
          }

          let statsList = STATS_DEFAULT_LIST;
          userCenterComponents.forEach((item) => {
            if (item.type === 'uc_stats') {
              statsList = item?.statsList || STATS_DEFAULT_LIST;
            }
          });
          const handledData = this.handleInitData(initData);
          this.setYZData(
            {
              globalData,
              shopInfo,
              kdtId,
              user,
              userLevel,
              plugins,
              config,
              globalConfig,
              backgroundImage,
              backgroundColor,
              userCenterComponents,
              userCenterComponentsMap,
              featureComponents,
              isSupportSign,
              isSigned,
              isYzEdu,
              isV2Balance,
              showLevel,
              statsList,
              isImmersive,
              ...handledData,
            },
            {
              immediate: true,
            }
          );
          this.handleYunSkdData();
          this.triggerEvent('setBackGround', backgroundColor);
          this.triggerEvent('showFeatureComponent', featureComponents);
          this.triggerEvent('setNavImmersive', isImmersiveConfig);
        })
        .catch(() => {})
        .finally(() => {
          app.globalData.usercenterPromise = null;
        });
    },

    handleYunSkdData() {
      this.getYunSdk(this.data.stats);
      Event.trigger('UCAssetsInfoLoaded', this.data.stats);

      this.setSdkData({ orderInfo: this.data.orders });
      Event.trigger('UCOrdersLoaded', this.data.orders);
    },

    handleInitData(initData) {
      const result = {};
      const { member, balance, orders, level } = initData;
      const {
        stats: { points, cards, coupons },
        status = {},
      } = member;

      result['stats.points'] = points || 0;
      result['stats.cards'] = cards || 0;
      result['stats.coupons'] = coupons || 0;

      if (balance && balance.length) {
        const { denomination = 0, showAmount = true } = balance[0];
        result['stats.balance'] = (Number(denomination) / 100).toFixed(2);
        result['stats.showBalance'] = showAmount;
      }

      result.orders = orders;

      if (this.getKdtId() !== LIANG_PIN_KDTID) {
        let memberLevelGradeText = level.levelName;
        const { payBenefitDesc, freeBenefitDesc } = level;
        // 如果会员等级不是样式2，后面按钮的文字要做调整
        if (this.data.memberType !== 2) {
          if (payBenefitDesc) {
            memberLevelGradeText = '立即开通';
          }

          if (freeBenefitDesc) {
            memberLevelGradeText = '立即注册';
          }
        }
        result.levelGrowth = level || {};
        result.memberLevelGradeText = memberLevelGradeText;
        result.isMember = !payBenefitDesc && !freeBenefitDesc;
      }

      if (app.checkSpecialShopDZ()) {
        const { pointError } = status;
        if (pointError) {
          result['stats.points'] = '-';
          wx.showToast({
            title: pointError?.msg || pointError?.message || '获取用户积分失败',
            icon: 'none',
            duration: 2500,
          });
        }
      }

      return result;
    },

    // 生成海报图片
    getPoster() {
      app
        .request({
          path: '/wscvis/cert/getCertificatePoster.json',
          method: 'get',
          data: {},
        })
        .then((res) => {
          if (res.redisKey) {
            this.setYZData({
              certTemplateType: res.templateType,
            });
            this.checkPoster(res.redisKey);
          } else if (res.img) {
            this.setYZData({
              certTemplateType: res.templateType,
              certImg: res.img,
              showPop: true,
            });
          } else {
            this.fetchRewardPop();
          }
        });
    },

    // 轮循生成绘制海报服务端的key
    checkPoster(key) {
      new Promise((resolve, reject) => {
        let i = 5;
        const check = () => {
          setTimeout(() => {
            app
              .request({
                path: '/wscvis/cert/getSnopshotByKey.json',
                method: 'get',
                data: {
                  key,
                },
              })
              .then((res) => {
                if (res.img) {
                  this.setYZData({
                    showPop: true,
                    certImg: res.img,
                  });
                  this.fetchRewardPop(true);
                  resolve(res);
                } else if (i > 0 && res.type === 'pending') {
                  i -= 1;
                  check();
                } else {
                  throw new Error('社区团购商品海报生成失败，请重试');
                }
              })
              .catch((e) => {
                reject(e);
              });
          }, 1000);
        };
        check();
      });
    },

    fetchAppoint() {
      app
        .request({
          path: '/wscuser/membercenter/appointments.json',
          method: 'get',
          data: {
            kdt_id: this.getKdtId(),
          },
        })
        .then((data) => {
          this.setYZData({
            appointments: data,
          });
        })
        .catch(() => {});
    },

    // 未查看证书
    fetchCertificate() {
      app
        .request({
          path: '/wscuser/membercenter/findCertificate.json',
          method: 'get',
          data: {
            kdtId: this.getKdtId(),
            findType: 2,
            status: 1,
          },
        })
        .then((data) => {
          if (data && data.total > 0) {
            this.setYZData({
              noViewNum: data.total,
            });
          }
        })
        .catch(() => {});
    },

    // 未查看作品集
    fetchWorkset() {
      app
        .request({
          path: '/wscuser/membercenter/findReadStatusNum.json',
          method: 'get',
        })
        .then((data) => {
          this.setYZData({
            isReadWorkset: !data.isReadNum,
          });
        })
        .catch(() => {
          this.setYZData({
            isReadWorkset: false,
          });
        });
    },

    // 家校圈通知
    fetchMomentsNotice() {
      app
        .request({
          path: '/wscuser/membercenter/momentsFindUserBadge.json',
          method: 'get',
          data: {
            kdtId: this.getKdtId(),
          },
        })
        .then((data = {}) => {
          this.setYZData({
            canShowMomentsFlag: data.value,
          });
        })
        .catch(() => {});
    },

    onClosePop() {
      this.setYZData({ showPop: false });
      if (this.data.rewardPopFlag) {
        this.setYZData({ shopRewardPop: true });
      }
    },

    onCloseRewardPop() {
      this.setYZData({ shopRewardPop: false });
    },

    fetchReward() {
      app
        .request({
          path: '/wscuser/membercenter/findReward.json',
          method: 'get',
          data: {
            kdtId: this.getKdtId(),
          },
        })
        .then((data) => {
          if (data && data.waitRewardCount > 0) {
            this.setYZData({
              waitRewardCount: data.waitRewardCount,
            });
          }
        })
        .catch(() => {});
    },
    fetchReviewToReadNum() {
      app
        .request({
          path: '/wscuser/membercenter/findReviewToReadNum.json',
          method: 'get',
          data: {
            kdtId: this.getKdtId(),
          },
        })
        .then((data) => {
          if (data && data.value > 0) {
            this.setYZData({
              reviewToReadNum: data.value,
            });
          }
        })
        .catch(() => {});
    },
    fetchMomentsName() {
      app
        .request({
          path: '/wscuser/membercenter/momentsgetCeresConfig.json',
          method: 'get',
        })
        .then((data) => {
          const { moduleName = '家校圈' } = data || {};
          this.setYZData({
            momentsName: moduleName,
          });
        })
        .catch(() => {});
    },
    fetchRewardPop(certflag = false) {
      app
        .request({
          path: '/wscuser/membercenter/findRewardPop.json',
          method: 'get',
          data: {
            kdtId: this.getKdtId(),
          },
        })
        .then((res) => {
          const { waitRewardCount = 0, isRead = false } = res || {};
          if (waitRewardCount > 0 && !isRead) {
            this.setYZData({ rewardCount: waitRewardCount });
            if (certflag) {
              this.setYZData({ rewardPopFlag: true });
            } else {
              this.setYZData({ shopRewardPop: true });
            }
          }
        })
        .catch(() => {});
    },

    linkToReward() {
      wx.navigateTo({
        url: `/${p}/edu/webview/index?targetUrl=${encodeURIComponent(
          `https://h5.youzan.com/wscvis/edu/reward/list/active?kdtId=${this.getKdtId()}`
        )}`,
      });
    },

    getYunSdk(assetsInfo) {
      if (!ECLOUD_MODE || !app.getYouZanYunSdk) {
        return;
      }

      const sdk = app.getYouZanYunSdk();
      if (!assetsInfo) return sdk;
      sdk.page.__setData('assetsInfo', assetsInfo);
      return sdk;
    },

    setSdkData({ orderInfo }) {
      if (!ECLOUD_MODE || !app.getYouZanYunSdk) return;
      const sdk = app.getYouZanYunSdk();
      sdk.page.__setData('orderInfo', orderInfo);
    },

    fetchOrderPayPrompt() {
      return new Promise((resolve) => {
        app
          .request({
            path: '/wscuser/membercenter/promptOrderPay.json',
            method: 'get',
            data: {
              kdtId: this.getKdtId(),
            },
          })
          .then((res) => {
            resolve(res);
          })
          .catch(() => {
            resolve({});
          });
      });
    },
    fetchOrderEvaluatePrompt() {
      return new Promise((resolve) => {
        app
          .request({
            path: '/wscuser/membercenter/promptOrderEvaluate.json',
            method: 'get',
            data: {
              kdtId: this.getKdtId(),
            },
          })
          .then((res) => {
            resolve(res);
          })
          .catch(() => {
            resolve({});
          });
      });
    },
    fetchOrderPrompt() {
      const requests = [this.fetchOrderPayPrompt()];
      if (this.data.isPureWscSingleStore) {
        requests.push(this.fetchOrderEvaluatePrompt());
      }
      Promise.all(requests).then(([promptOrderPay, promptOrderEvaluation]) => {
        this.setYZData({
          promptOrderPay,
          promptOrderEvaluation,
        });
      });
    },
    fetchPointsName() {
      app
        .getPointsName()
        .then(({ pointsName = '积分' }) => {
          this.setYZData({
            'statsNames.points': pointsName,
          });
        })
        .catch(() => {});
    },
    fetchCartNum() {
      app
        .request({
          path: '/wscuser/membercenter/cart.json',
          method: 'get',
          data: {
            kdt_id: this.getKdtId(true),
          },
        })
        .then(({ value: count }) => {
          this.setYZData({
            'pluginValues.cart': count,
          });
        })
        .catch(() => {});
    },
    fetchPresentCount() {
      app
        .request({
          path: '/wscuser/membercenter/present.json',
          method: 'get',
          data: {
            kdt_id: this.getKdtId(),
          },
        })
        .then(({ value: count }) => {
          if (count) {
            this.setYZData({
              'pluginValues.gifts': count,
            });
          }
        })
        .catch(() => {});
    },
    fetchContact() {
      app
        .request({
          path: '/wscuser/membercenter/contact.json',
          method: 'get',
          data: {
            kdt_id: this.getKdtId(),
          },
        })
        .then(({ afterSaleContact }) => {
          if (!afterSaleContact) {
            this.setYZData({
              plugins: this.data.plugins.filter(
                (item) => item.type !== 'merchantsCall'
              ),
            });
          } else {
            const { mobileNumber, areaCode, phoneNumber } = afterSaleContact;
            let tel = '';
            if (phoneNumber && areaCode) {
              tel = `${areaCode}-${phoneNumber}`;
            } else if (phoneNumber) {
              tel = phoneNumber;
            } else if (mobileNumber) {
              tel = mobileNumber;
            }
            this.setYZData({
              servicePhoneNumber: tel,
            });
          }
        })
        .catch(() => {});
    },

    handleCompleteInfoClick() {
      // todo 跳转到会员设置页面
      openWebView('/wscuser/levelcenter/fill', {
        query: {
          kdt_id: this.getKdtId(),
          alias: this.data.levelGrowth.freeLevelGroupAlias,
          eT: Date.now(),
        },
      });
    },

    handleKaishuPluginClick(event) {
      const type = get(event, 'currentTarget.dataset.type', '');
      const temp = Array.prototype.concat.apply([], KAISHU_PLUGIN_LIST);
      const url = temp.find((item) => item.type === type).url || '';
      switch (type) {
        case 'contact':
          this.handleContactCustomerService();
          return;
        case 'cart':
          navigate.switchTab({ url: `/${p}/goods/cart/index` });
          return;
        case 'cashBack':
        case 'points':
        case 'gift':
        case 'paidContent':
          navigate.navigate({ url });
          break;
        case 'couponCard':
          this.jumpCouponDetail('promocard');
          break;
        case 'couponCode':
          this.jumpCouponDetail('promocode');
          break;
        case 'saleman':
          this.handleSalesmanClick();
          break;
        default:
          break;
      }
    },

    onUserAgreePrivacy(val) {
      if (val && val === true) {
        const { shopMetaInfo = {} } = app.getShopInfoSync() || {};
        if (
          [checkPureWscSingleStore, checkRetailShop].some((fn) =>
            fn(shopMetaInfo)
          )
        ) {
          this.guideBecomingMember();
        }
      }
    },
    /**
     * 隐藏用户授权popup
     */
    hideUserAuthPopup() {
      this.setYZData({
        showUserAuthPopup: false,
        showUserAuth: false,
      });
    },

    /**
     * 隐藏完善资料入口
     */

    hiddenInfoCompleted(display) {
      this.setYZData({
        hiddenInfoCompleted: display,
      });
    },
    hideshowUserAuthPopup(display) {
      this.setYZData({
        centerPhoneStatus: display,
      });
    },

    hideDrugRequirements() {
      const { plugins = [] } = this.data;
      this.setYZData({
        plugins: plugins.filter((plugin) => plugin.type !== 'drugList'),
      });
    },
    initSdk() {
      const sdk = this.getYunSdk();
      if (sdk) {
        sdk.setPageProcess(
          'hiddenInfoCompleted',
          this.hiddenInfoCompleted.bind(this)
        );

        sdk.setPageProcess(
          'closeCenterPhone',
          this.hideshowUserAuthPopup.bind(this)
        );
        sdk.setPageProcess(
          'hideDrugRequirements',
          this.hideDrugRequirements.bind(this)
        );
      }
    },
    isNeedUpdate(val) {
      if (val && this.isNormalTemplate()) {
        this.initData(false, true);
      }
    },
  },
});
