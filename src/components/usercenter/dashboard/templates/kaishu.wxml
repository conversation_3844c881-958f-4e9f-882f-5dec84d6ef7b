<import src="/components/zan-account/bind-phone-number/index.wxml"/>
<import src="./top_banner.wxml"/>

<template name="uc-kaishu">
  <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" bindgetuserinfo="onGetUserInfo">
    <template is="banner" data="{{ title: userInfo.nickName, logo: userInfo.avatarUrl, type: 'normal' }}"/>
    <view class="grade" catchtap="jumpToMemberBenefit">
      <image class="grade-icon" src="https://img01.yzcdn.cn/weapp/wsc/kaishu-customize/level.png"></image>
      <text>凯叔会员 ></text>
    </view>
  </user-authorize>
  <view class="zan-panel zan-panel--without-border">
    <view class="bind__container" wx:if="{{ showBindPhoneNumber }}">
      <view class="bind__title">{{ bindTips }}</view>
      <view class="bind__button">
        <theme-view bg="main-bg" border="main-bg" border-after="main-bg">
          <user-authorize
            class="btn-userinfo btn-class"
            auth-type-list="{{ ['mobile'] }}"
            bind:next="onAccountSuccess"
          >
            <button class="btn-userinfo btn-class">登录</button>
          </user-authorize>
        </theme-view>
      </view>
    </view>
    <account-wx-login id="account-wx-login" />
    <navigator
      class="zan-cell zan-cell--access"
      url="/packages/trade/order/list/index"
      hover-class="none">
      <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" class="btn-order-userinfo" bindgetuserinfo="onGetUserInfo" is-once>
        <view class="zan-cell__bd" style="color: #000000;">我的订单</view>
        <view class="zan-cell__ft van-font-12 van-c-gray-dark">查看全部订单</view>
      </user-authorize>
    </navigator>
    <view>
      <view class="state-list">
        <block
          wx:for="{{ kaishuStatsList }}"
          wx:key="{{ index }}"
        >
          <navigator
            class="state-list__item"
            url="{{ item.url }}"
            hover-class="none"
          >
            <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" class="state-btn-container" bindgetuserinfo="onGetUserInfo" is-once>
              <view class="state-list__icon zan-badge">
                <view class="wsc-icon wsc-icon-{{item.type}}"></view>
                <theme-view wx:if="{{ item.count != 0 }}" custom-class="zan-badge__count" bg="main-bg">{{ item.count }}</theme-view>
              </view>
              {{ item.text }}
            </user-authorize>
          </navigator>
        </block>
        <navigator
          class="state-list__item"
          url="/packages/trade/order/list/index?type=sign&count=0"
          hover-class="none"
        >
          <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" class="state-btn-container" bindgetuserinfo="onGetUserInfo" is-once>
            <view class="state-list__icon">
              <image class="icon__inner" mode="aspectFit" src="https://img01.yzcdn.cn/weapp/wsc/e1eZRr.png"/>
            </view>
            已完成
          </user-authorize>
        </navigator>
      </view>
    </view>
  </view>
  <block
    wx:for="{{ kaishuPluginList }}"
    wx:for-item="itemData"
    class="uc-normal__form zan-panel zan-panel--without-border"
  >
    <form-view class="uc-normal__form zan-panel zan-panel--without-border">
      <block
        wx:for="{{ itemData}}"
      >
        <user-authorize
          class="zan-cell zan-cell--access btn-get-userinfo"
          is-once="{{ item.unOnce ? false : true }}"
          authTypeList="{{ ['nicknameAndAvatar'] }}"
          bindtap="handleKaishuPluginClick"
          data-type="{{ item.type }}"
          data-url="{{ item.url }}"
        >
          <view class="zan-cell__bd">{{ item.text}}</view>
          <view class="zan-cell__ft"></view>
        </user-authorize>
      </block>
    </form-view>
  </block>
</template>
