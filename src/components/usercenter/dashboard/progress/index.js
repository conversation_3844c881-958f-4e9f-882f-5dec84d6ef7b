Component({
  properties: {
    value: {
      type: Number,
      value: 0
    },
    total: {
      type: Number,
      value: 0
    },
    showPivot: {
      type: Boolean,
      value: true
    },
    foreColor: {
      type: String,
      value: '#3388ff'
    },
    backColor: {
      type: String,
      value: '#e5e5e5'
    },
    textColor: {
      type: String,
      value: '#fff'
    },
    inActive: {
      type: Boolean,
      value: false,
    },
    pivotText: {
      type: String,
      value: ''
    }
  }
});
