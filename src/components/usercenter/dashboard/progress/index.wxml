<view
  class="progress"
  style="background-color:{{ backColor }}"
>
  <view
    wx:if="{{ total }}"
    class="progress__bar"
    style="background-color:{{ foreColor }};width:{{value * 100 / total }}%;"
  />
  <view
    wx:if="{{ showPivot }}"
    class="progress__pivot"
    style="color:{{ textColor }}"
  >
    <view wx:if="{{ pivotText }}">{{ pivotText }}</view>
    <view wx:else>{{ value }} / {{ total }}</view>
  </view>
</view>
