<block wx:for="{{ componentConfigs }}" wx:key="{{ index }}">
  <user-info
    wx:if="{{ item.type === 'uc_user_info' }}"
    avatar="{{ avatar }}"
    user-name="{{ userName }}"
    background-image="{{ item.backgroundImage }}"
    grade="{{ grade }}"
    links="{{ baseLinks }}"
    userAgreePrivacy="{{userAgreePrivacy}}"
    show-bind-user="{{ showBindUser }}"
    bind-tips="{{ bindTips }}"
    is-not-valid="{{ isNotValid }}"
    is-support-sign="{{ isSupportSign }}"
    is-signed="{{ isSigned }}"
    user-name-color="{{ item.userNameColor }}"
    align="{{ item.align }}"
    gradient="{{ item.gradient }}"
    member-type="{{ item.memberType }}"
    is-member="{{ isMember }}"
    show-level="{{ showLevel }}"
    is-support-growth-value="{{ isSupportGrowthValue }}"
    level-value="{{ levelValue }}"
    show-growth="{{ showGrowth }}"
    growth-value="{{ growthValue }}"
    benefit-text="{{ benefitText }}"
    level-icon-type="{{ levelIconType }}"
    is-immersive="{{ isImmersive }}"
    show-info-completed="{{ showInfoCompleted }}"
    bind:bindUser="handleBindUser"
    bind:tapUserInfo="handleTapUserInfo"
    bind:bindGetUserInfo="handleBindGetUserInfo"
    bind:tapSign="handleSign"
    bind:tapLevel="handleTapUserInfo"
    bind:onAccountSuccess="onAccountSuccess"
    bind:registryThreshold="handleRegistryThreshold"
  />

  <reminder
    wx:elif="{{ item.type === 'uc_reminder' && isYzEdu && userAgreePrivacy}}"
    reminders="{{ item.reminders }}"
    reminder-infos="{{ reminderInfos }}"
  />

  <edu-service
    wx:elif="{{ item.type === 'edu_services' && userAgreePrivacy }}"
    is-yz-edu="{{ isYzEdu }}"
    appointments="{{ appointments }}"
    no-view-num="{{ noViewNum }}"
    is-read-workset="{{ isReadWorkset }}"
    wait-reward-count="{{ waitRewardCount }}"
    review-to-read-num="{{ reviewToReadNum }}"
    can-show-moments-flag="{{ canShowMomentsFlag }}"
    services="{{ item.plugins }}"
    mode="{{ item.mode }}"
    icon-mode="{{ item.iconMode }}"
    momentsName="{{ momentsName }}"
  />

  <image-ad
    wx:elif="{{ item.type === 'uc_image_ad' && item.images.length > 0}}"
    template="{{ item.template }}"
    images="{{ item.images }}"
    pageMargin="20"
    use-scene="usercenter-old"
    swipeFill
    bind:itemClick="handleImageClick"
    bind:itemChange="handleImageChange"
    bind:navigate="handleImageClick"
    bind:contactback="handleContactBack"
  />

  <stats
    wx:elif="{{ item.type === 'uc_stats' }}"
    userAgreePrivacy="{{userAgreePrivacy}}"
    stats-names="{{statsNames}}"
    show-balance="{{ stats.showBalance }}"
    show-coupon-dot="{{ stats.showCouponDot }}"
    show-card-dot="{{ stats.showCardDot }}"
    balance="{{ stats.balance }}"
    points="{{ stats.points }}"
    coupons="{{ stats.coupons }}"
    cards="{{ stats.cards }}"
    links="{{ baseLinks }}"
    is-not-valid="{{ isNotValid }}"
    stats-list="{{ statsList }}"
    bind:statsItemClicked="onStatsItemClicked"
    bind:bindGetUserInfo="handleBindGetUserInfo"
  />

  <benefit wx:elif="{{ item.type === 'uc_benefit' }}"
   benefit-reminds="{{benefitReminds}}"
  />

  <order
    wx:elif="{{ item.type === 'uc_order' }}"
    links="{{ orderLinks }}"
    to-pay="{{ orders.toPay }}"
    userAgreePrivacy="{{userAgreePrivacy}}"
    evaluate="{{ orders.evaluate }}"
    feedback="{{ orders.feedback }}"
    confirm="{{ orders.confirm }}"
    paid="{{ orders.paid }}"
    sent="{{ orders.sent }}"
    is-not-valid="{{ isNotValid }}"
    prompt-order-pay="{{ promptOrderPay }}"
    prompt-order-evaluation="{{promptOrderEvaluation}}"
    is-pure-wsc-single-store="{{isPureWscSingleStore}}"
    bind:bindGetUserInfo="handleBindGetUserInfo"
    bind:pluginItemClicked="onPluginItemClicked"
  />

  <widgets
    wx:elif="{{ item.type === 'uc_widgets' }}"
    mode="{{ item.mode }}"
    userAgreePrivacy="{{userAgreePrivacy}}"
    icon-mode="{{ item.iconMode }}"
    plugins="{{ pluginList }}"
    values="{{ pluginValues }}"
    links="{{ pluginLinks }}"
    is-not-valid="{{ isNotValid }}"
    bind:pluginItemClicked="onPluginItemClicked"
    bind:bindGetUserInfo="handleBindGetUserInfo"
  />

  <introduction wx:elif="{{ item.type === 'uc_introduction' }}" introduction="{{ introduction }}" />

</block>

