<van-pop show="{{ visible }}" custom-style="width: 298px;border-radius: 10px;background-color: transparent;" class="activate-pop">
  <view class="activate-pop-content">
    <van-image class="activate-pop-content__bg" width="178" height="85" src="{{BGURL}}" />
    <view class="activate-pop-content__body">
      <view class="activate-pop-content__body-header">
        <view class="pop-title">{{ name }}</view>
        <view class="pop-desc">{{ desc }}</view>
      </view>
      <view wx:if="{{benefitList.length}}" class="activate-pop-content__body-content">
        <view wx:for="{{benefitList}}" wx:key="{{item.key}}" class="pop-benefit-item">
          <van-image wx:if="{{ item.key !== U_KEY }}" width="40" height="40" src="{{item.icon}}" />
          <view wx:else class="pop-benefit-item-num">{{ item.benefitCountText }}</view>
          <view class="pop-benefit-item-text">{{ item.showName }}</view>
        </view>
      </view>
      <view wx:else class="activate-pop-content__body-empty">
        <van-image width="214" height="160" src="{{EMPTYICON}}" />
      </view>
    </view>
  </view>
  <view class="activate-pop-content__footer">
    <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="submit">
      <van-button block color="linear-gradient(90deg, #E2BB7C 0%, #E8C388 100%)" custom-style="opacity: 0.94;font-weight: 500;font-size: 16px; color: #724804;" round type="info">
        立即激活
      </van-button>
    </user-authorize>
    <view class="activate-pop-content__footer-close">
      <van-image bind:tap="close" width="32" height="32" src="{{CLOSEICON}}" />
    </view>
  </view>
</van-pop>