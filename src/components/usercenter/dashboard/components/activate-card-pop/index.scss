.activate-pop {
  background-color: transparent;

  &-content {
    border-radius: 20px;
    position: relative;
    background: #fff;

    &__bg {
      position: absolute;
      right: 0;
    }

    &__body {
      &-header {
        padding-top: 48px;
        .pop-title {
          font-weight: 500;
          font-size: 18px;
          color: #323233;
          letter-spacing: 0;
          text-align: center;
        }
        .pop-desc {
          font-size: 14px;
          color: #7d7e80;
          text-align: center;
          line-height: 20px;
          margin-top: 8px;
        }
      }

      &-empty {
        padding-top: 16px;
        text-align: center;
        padding-bottom: 24px;
      }
      &-content {
        padding: 32px 16px;
        text-align: center;
        display: flex;
        justify-content: center;

        .pop-benefit-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-left: 4px;
          min-width: 66px;
          max-width: 72px;
          &:first-of-type {
            margin-left: 0;
          }
        }

        .pop-benefit-item-text {
          font-size: 12px;
          color: #646566;
          text-align: center;
          margin-top: 4px;
        }

        .pop-benefit-item-num {
          width: 40px;
          height: 40px;
          border-radius: 20px;
          background-color: rgba(209, 161, 54, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          color: #d5a150;
          font-weight: bold;
        }
      }
    }

    &__footer {
      margin-top: 24px;
      width: 100%;
      text-align: center;

      &-close {
        margin-top: 24px;
      }

      .van-button__text {
        opacity: 0.94;
        font-weight: 500;
        font-size: 16px;
        color: #724804;
        text-align: center;
      }
    }
  }
}
