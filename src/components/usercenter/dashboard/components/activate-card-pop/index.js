import WscComponent from 'shared/common/base/wsc-component/index';
import fullfillImage from '@youzan/utils/url/fullfillImage';

const BGURL = fullfillImage(
  'upload_files/2021/11/15/Fp2OuQMKpWewaa6upVwdmO-Bswcq.png',
  'middle'
);

const EMPTYICON = fullfillImage(
  'upload_files/2021/12/07/Fp1oGBTjo7fPZ4d6TD2quT_mvLFJ.png',
  'middle'
);

const CLOSEICON = fullfillImage(
  'upload_files/2021/11/15/FoRtIwFZwemPJqchrDKL-1sX40N8.png',
  'middle'
);

const U_KEY = Symbol('all');

const app = getApp();

WscComponent({
  properties: {
    benefitcard: {
      type: Object,
      value: {},
      observer: 'observerBenefitcard',
    },
  },
  data: {
    visible: true,
    BGURL,
    EMPTYICON,
    CLOSEICON,
    benefitList: [],
    name: '',
    cardAlias: '',
    desc: '',
    U_KEY,
  },

  lifetimes: {
    attached() {
      app.logger.log({
        et: 'view', // 事件类型
        ei: 'view_card_act', // 事件标识
        en: '权益卡激活曝光', // 事件名称
        pt: 'uc', // 页面类型
      });
    },
  },

  methods: {
    close() {
      this.setYZData({
        visible: false,
      });
    },
    submit() {
      app.logger.log({
        et: 'click', // 事件类型
        ei: 'click_card_act', // 事件标识
        en: '权益卡激活点击', // 事件名称
        pt: 'uc', // 页面类型
      });

      wx.navigateTo({
        url: `/packages/card/active/index?alias=${this.data.cardAlias}`,
      });
    },
    observerBenefitcard(value) {
      if (Object.keys(value ?? {}).length) {
        let { benefitList = [] } = value;
        if (benefitList.length > 4) {
          benefitList = benefitList.slice(0, 3).concat({
            showName: `全部权益`,
            benefitCountText: `${benefitList.length}项`,
            key: U_KEY,
          });
        }
        let desc = '尚未激活，激活即享专属特权';
        if (benefitList.length) {
          desc = '尚未激活，激活可享以下权益';
        }
        this.setYZData({
          benefitList,
          name: `恭喜你，获得${value.cardName ?? ''}`,
          cardAlias: value.cardAlias,
          desc,
        });
        return;
      }
      this.setYZData({
        benefitList: [],
      });
    },
  },
});
