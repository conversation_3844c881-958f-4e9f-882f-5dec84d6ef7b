import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';
import openWebView from 'shared/utils/open-web-view';
import { EVALUATION } from 'shared/common/sensetive-words';
import { orderPayPromptLogger } from './utils/index';
import { wscSubscribeMessage } from '@/utils/subscribe-message';

WscComponent({
  properties: {
    toPay: {
      type: Number,
      value: 0,
    },
    paid: {
      type: Number,
      value: 0,
    },
    sent: {
      type: Number,
      value: 0,
    },
    evaluate: {
      type: Number,
      value: 0,
    },
    feedback: {
      type: Number,
      value: 0,
    },
    links: {
      type: Object,
      value: {
        toPay: '',
        toConfirm: '',
        toSend: '',
        sent: '',
        completed: '',
        all: '',
        refund: '',
      },
    },
    promptOrderPay: {
      type: Object,
      value: {},
      observer: 'isShowPromptOrderPay',
    },
    promptOrderEvaluation: {
      type: Object,
      value: {},
      observer: 'isShowPromptOrderEvaluation',
    },
    userAgreePrivacy: {
      type: Boolean,
      default: false,
    },
    isNotValid: Boolean,
    isPureWscSingleStore: {
      type: Boolean,
      value: false,
      observer: 'getEvaluateTitle',
    },
  },

  data: {
    showOrderPayPrompt: false,
    showOrderEvaluatePrompt: false,
    tapData: {},
    showSubscribeMask: false,
    evaluateTitle: EVALUATION,
  },

  methods: {
    isShowPromptOrderPay(value) {
      const { showOrderPayPrompt } = value;
      if (Object.keys(value).length !== 0) {
        orderPayPromptLogger('view', value);
      }
      this.setYZData({
        showOrderPayPrompt,
      });
    },
    isShowPromptOrderEvaluation(value) {
      const { orderNo } = value || {};
      this.setYZData({
        showOrderEvaluatePrompt: !!orderNo,
      });
    },
    getEvaluateTitle(value) {
      this.setYZData({
        evaluateTitle: value ? `待${EVALUATION}` : EVALUATION,
      });
    },
    toggleSubscribeMask(showSubscribeMask) {
      this.setYZData({
        showSubscribeMask,
      });
    },
    handleAuthorize() {
      // 用户权限同意之后，才能进行下一步

      // 判断此次tap的数据
      const key = this.data.tapData?.currentTarget?.dataset?.nav;

      if (!key) {
        return;
      }

      if (key === 'toEvaluate') {
        const { paid, sent } = this.data;

        if (!!paid || !!sent) {
          const next = () => {
            this.openEvaluateCenter(this.data.tapData);
          };
          this.subscribe(next);

          return;
        }

        this.openEvaluateCenter(this.data.tapData);
        return;
      }

      this.goto(this.data.tapData);
    },
    handleTap(e) {
      // 记录当前点击信息
      this.setYZData({
        tapData: e,
      });
    },
    clearTapData() {
      // 清除点击信息
      this.setYZData({
        tapData: {},
      });
    },
    subscribe(next) {
      wscSubscribeMessage({
        scene: 'afterPaySuccess',
        windowType: 'usercenter_order',
        authorizationType: 'order',
        subscribePage: '个人中心',
        subscribeType: '我的订单',
        supportRetail: false,
        options: {
          next,
          onShowTips: () => this.toggleSubscribeMask(true),
          onCloseTips: () => this.toggleSubscribeMask(false),
          onComplete: next,
        },
      });
    },
    goto(e) {
      const key = e.currentTarget.dataset.nav;
      const { paid, sent } = this.data;

      if (this.properties.links[key]) {
        if (!!paid || !!sent) {
          const next = () => {
            navigate.navigate({ url: this.properties.links[key] });
          };
          this.subscribe(next);

          return;
        }

        navigate.navigate({ url: this.properties.links[key] });
      } else {
        if (!!paid || !!sent) {
          const next = () => {
            this.triggerEvent('pluginItemClicked', key);
          };
          this.subscribe(next);

          return;
        }

        this.triggerEvent('pluginItemClicked', key);
      }
    },

    handleBindGetUserInfo(e) {
      this.triggerEvent('bindGetUserInfo', e.detail);
    },

    openEvaluateCenter() {
      openWebView('/wsctrade/order/evaluate/center', { title: '评价中心' });
    },
  },
});
