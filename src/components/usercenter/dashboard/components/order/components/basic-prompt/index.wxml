<view class="mc__basicPrompt" bindtap="handleClick">
  <view class="mc__basicPrompt_wrapper">
    <view class="mc__basicPrompt_wrapper-left">
      <view class="mc__basicPrompt_image-wrapper" >
        <image class="mc__basicPrompt_image" mode="aspectFill"  src="{{ goods[0] ? goods[0].imgUrl : '' }}" />
        <text class="mc__basicPrompt_image-mask" wx:if="{{ goods.length > 1 }}">共{{ goods.length }}件</text>
      </view>
      <view class="mc__basicPrompt_layout">
        <view class="mc__basicPrompt_text">
          <slot></slot>
        </view>
      </view>
    </view>
    <theme-view
      custom-class="pay-btn"
      gradient
      gradientDeg="90">
      {{btnTxt}}
    </theme-view>
  </view>
</view>