import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';
import { parseFormat } from '@vant/weapp/count-down/utils';
import args from '@youzan/weapp-utils/lib/args';
import { orderPayPromptLogger, getBannerId } from '../../utils/index';
import { getLevel } from './api';
import {
  PAGE_TYPE,
  navigateToRantaPage,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';

WscComponent({
  properties: {
    orderPayPrompts: {
      // 催付订单信息
      type: Object,
      observer: 'initOrderPayPrompts',
    },
  },

  data: {
    goods: [],
    text: [],
    remianingSeconds: '',
    orderNo: '',
    formattedTime: '00:00:00',
    field1: '',
  },

  methods: {
    initOrderPayPrompts(value) {
      const { descTemplate = {}, goods = [], orderNo } = value || {};
      const { template, expireTime, field1 = '' } = descTemplate;
      let text = [];
      let remianingSeconds = '';
      if (template) {
        const regex = /(\${field1}|\${field2}|\${expireTime})/g;
        text = template.split(regex);
      }
      if (expireTime) {
        remianingSeconds = Math.round(expireTime - Date.now());
      }
      this.setYZData({
        text,
        goods,
        orderNo,
        field1,
        remianingSeconds,
      });
    },

    handleCountDownChange(e) {
      this.setYZData({
        formattedTime: parseFormat('HH:mm:ss', e.detail),
      });
    },

    handleLevelGoodsUrl(kdtId, goodsId, skuId) {
      // 查询是否属于连续包月商品
      return getLevel({ kdtId, goodsId }).then((data) => {
        const { autoRenew, levelAlias, levelGoods = {} } = data;
        const { skuList } = levelGoods;
        const sku = skuList.find((s) => s.goodsSkuId == skuId);
        const { goodsSkuType } = sku || [];
        // 1 付费规则 2.自动续费规则
        if (autoRenew && goodsSkuType == 2) {
          const payLevelUrl = args.add(
            'https://cashier.youzan.com/pay/wscuser_paylevel',
            {
              kdt_id: kdtId,
              alias: levelAlias,
              banner_id: getBannerId(),
            }
          );
          const wxUrl = `/pages/common/webview-page/index?src=${encodeURIComponent(
            payLevelUrl
          )}`;
          return wxUrl;
        }
        return '';
      });
    },

    async goPay() {
      let url = '';
      const { goods = [] } = this.data.orderPayPrompts || {};
      const { goodsId, skuId, payGradeCard = false, kdtId } = goods[0] || [];

      if (payGradeCard) {
        url = await this.handleLevelGoodsUrl(kdtId, goodsId, skuId);
      }

      if (url) {
        navigate.navigate({ url });
      } else {
        navigateToRantaPage({
          pageType: PAGE_TYPE.PAY,
          query: {
            orderNo: this.data.orderNo,
            banner_id: getBannerId(),
          },
        });
      }

      orderPayPromptLogger('pay_click', this.data.orderPayPrompts);
    },
  },
});
