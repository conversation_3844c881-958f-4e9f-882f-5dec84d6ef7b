<basic-prompt btn-txt="去支付" goods="{{goods}}" bind:prompt-click="goPay">
  <view wx:for="{{ text }}"> 
    <theme-view
      wx:if="{{ '${field1}' === item }}"
      color="main-bg"
      key="{{ item }}"
    >
    {{ field1 }}
    </theme-view>

    <van-count-down
      wx:elif="{{ '${expireTime}' === item }}"
      key="{{ item }}"
      use-slot
      time="{{ remianingSeconds }}"
      bind:change="handleCountDownChange"
    >
      <theme-view color="main-bg">{{ formattedTime }}</theme-view>
    </van-count-down>
    <text wx:elif>{{ item }}</text>
  </view>
</basic-prompt>