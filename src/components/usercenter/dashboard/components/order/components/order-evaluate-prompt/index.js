import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';
import { parseFormat } from '@vant/weapp/count-down/utils';
import { orderEvaluatePromptLogger } from '../../utils/index';

WscComponent({
  properties: {
    orderEvaluatePrompts: {
      type: Object,
      observer: 'initOrderEvaluatePrompts',
    },
  },

  data: {
    goods: [],
    remianingSeconds: '',
    orderNo: '',
    formattedTime: '00:00:00',
  },

  methods: {
    initOrderEvaluatePrompts(value) {
      const { expireTime, goods = [], orderNo } = value || {};
      let remianingSeconds = '';
      if (expireTime) {
        const restTime = Math.round(expireTime - Date.now());
        // 截止时间小于 24 小时时，展示倒计时
        remianingSeconds = restTime > 24 * 60 * 60 * 1000 ? '' : restTime;
      }

      this.setYZData({
        goods,
        orderNo,
        remianingSeconds,
      });
    },

    handleCountDownChange(e) {
      this.setYZData({
        formattedTime: parseFormat('HH:mm:ss', e.detail),
      });
    },

    goEvaluate() {
      const { orderNo, goods } = this.data;
      orderEvaluatePromptLogger('click', orderNo, goods);
      navigate.navigate({
        url: `/packages/evaluation/order/create/index?order_no=${orderNo}&from=remind_evaluation_component`,
      });
    },
  },

  attached() {
    if (!this.data.orderNo) return;
    this._observer = wx.createIntersectionObserver(this, { thresholds: [1] });
    this._observer
      .relativeToViewport()
      .observe('.mc__orderEvaluatePrompt', (res) => {
        if (!this._reported && res.intersectionRatio === 1) {
          const { orderNo, goods } = this.data;
          orderEvaluatePromptLogger('view', orderNo, goods);
          this._reported = true;
        }
      });
  },
  detached() {
    this._observer?.disconnect();
  },
});
