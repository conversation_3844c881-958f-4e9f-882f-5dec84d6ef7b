<basic-prompt class="mc__orderEvaluatePrompt" btn-txt="去评价" goods="{{goods}}" bind:prompt-click="goEvaluate">
  待评价，快和大家分享你的购物体验吧<view wx:if="{{remianingSeconds}}" class="mc__orderEvaluatePrompt_rest-time">
    <text class="mc__orderEvaluatePrompt_text-dec">，剩余</text>
    <van-count-down
      key="{{ item }}"
      use-slot
      time="{{ remianingSeconds }}"
      bind:change="handleCountDownChange"
    >
      <theme-view color="main-bg">{{ formattedTime }}</theme-view>
    </van-count-down>
  </view>
</basic-prompt>