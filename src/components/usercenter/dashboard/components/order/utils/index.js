import { makeRandomString } from '@youzan/weapp-utils/lib/str';

const app = getApp();

/**
 * 获取bannerId
 * @param { Number }
 * @return {string}
 */
export const getBannerId = (index = 1) => {
  return `uc~pending_payment_banner~${index}~${makeRandomString(8)}`;
};

/**
 * 催付订单信息埋点上报
 * @param { String } type
 * @param { String } orderNo
 * @return {Object}
 */
export const orderPayPromptLogger = (type, promptOrderPays) => {
  const { orderNo, abTraceId } = promptOrderPays;
  let loggerMsg = {};
  const params = {
    banner_id: getBannerId(),
    order_no: orderNo,
    abTraceId,
    component: 'pending_payment_banner',
  };
  switch (type) {
    case 'view':
      loggerMsg = {
        et: 'view',
        ei: 'component_view',
        en: '待付款提示通知条曝光',
        params,
      };
      break;

    case 'pay_click':
      loggerMsg = {
        et: 'click',
        ei: 'pay_click',
        en: '去支付点击',
        params,
      };
      break;
  }

  app.logger && app.logger.log(loggerMsg);
};

/**
 * 催评订单信息埋点上报
 * @param { String } type
 * @param { String } orderNo
 * @return {Object}
 */
export const orderEvaluatePromptLogger = (type, orderNo, goods) => {
  let loggerMsg = {};
  const params = {
    order_no: orderNo,
    goods_id: goods?.map((item) => item.goodsId)?.join(','),
    component: 'remind_evaluation_component',
  };
  switch (type) {
    case 'view':
      loggerMsg = {
        et: 'view',
        ei: 'remind_evaluation_component_view',
        en: '待评价提示组件曝光',
        params,
      };
      break;

    case 'click':
      loggerMsg = {
        et: 'click',
        ei: 'evaluate_click',
        en: '去评价点击',
        params,
      };
      break;
  }

  app.logger?.log(loggerMsg);
};
