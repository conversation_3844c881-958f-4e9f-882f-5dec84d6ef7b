<view class="member-center__order">
  <user-authorize
    authTypeList="{{ ['nicknameAndAvatar'] }}"
    btn-class="authorize-userinfo"
    bindgetuserinfo="handleBindGetUserInfo"
    data-nav="all"
    bind:next="handleAuthorize"
    bind:fail="clearTapData"
    bindtap="handleTap"
  >
      <van-cell
        title-class="order-cell__title"
        value-class="order-cell__value"
        title="我的订单"
        custom-class="order-cell__custom"
        value="查看全部订单"
        is-link
      />
  </user-authorize>
  <user-authorize
    authTypeList="{{ ['nicknameAndAvatar'] }}"
    bindgetuserinfo="handleBindGetUserInfo"
    bind:next="handleAuthorize"
    bind:fail="clearTapData"
  >
    <van-tabbar fixed="{{ false }}" safe-area-inset-bottom="{{ false }}" custom-class="order-tabbar {{ showPromptOrderPay ? 'tabbar-margin-bottom' : '' }}" >
      <van-tabbar-item
        info="{{ userAgreePrivacy && toPay > 99 ? '99+' : userAgreePrivacy && toPay > 0 ? toPay : '' }}"
        data-nav="toPay"
        bind:click="handleTap"
      >
        <image slot="icon" src="https://b.yzcdn.cn/public_files/2019/04/28/1be3ce61d72286c7ac6e56fbb7d0735c.svg" class="member-center__order-icon" mode="aspectFit" />
        <image slot="icon-active" src="https://b.yzcdn.cn/public_files/2019/04/28/1be3ce61d72286c7ac6e56fbb7d0735c.svg" class="member-center__order-icon" mode="aspectFit" />
        <text class="icon-text">待付款</text>
      </van-tabbar-item>

      <van-tabbar-item
        info="{{ userAgreePrivacy && paid > 99 ? '99+' : userAgreePrivacy && paid > 0 ? paid : '' }}"
        data-nav="toSend"
        bind:click="handleTap"
      >
        <image slot="icon" src="https://b.yzcdn.cn/public_files/2019/04/28/72cc80f8b65eae45862cafe0d27e78f7.svg" class="member-center__order-icon" mode="aspectFit" />
        <image slot="icon-active" src="https://b.yzcdn.cn/public_files/2019/04/28/72cc80f8b65eae45862cafe0d27e78f7.svg" class="member-center__order-icon" mode="aspectFit" />
        <text class="icon-text">待发货</text>
      </van-tabbar-item>

      <van-tabbar-item
        info="{{ userAgreePrivacy && sent > 99 ? '99+' : userAgreePrivacy && sent > 0 ? sent : '' }}"
        data-nav="toReceive"
        bind:click="handleTap"
      >
        <image slot="icon" src="https://b.yzcdn.cn/public_files/2019/04/28/d729a66ef2fe52dd47ae6998921b43fe.svg" class="member-center__order-icon" mode="aspectFit" />
        <image slot="icon-active" src="https://b.yzcdn.cn/public_files/2019/04/28/d729a66ef2fe52dd47ae6998921b43fe.svg" class="member-center__order-icon" mode="aspectFit" />
        <text class="icon-text">待收货</text>
      </van-tabbar-item>

      <van-tabbar-item
        info="{{ userAgreePrivacy && evaluate > 99 ? '99+' : userAgreePrivacy && evaluate > 0 ? evaluate : '' }}"
        data-nav="toEvaluate"
        bind:click="handleTap"
      >
        <image slot="icon" src="https://b.yzcdn.cn/public_files/2019/04/28/a4a60cccd63f9d3ff476cb22f51146d7.svg" class="member-center__order-icon" mode="aspectFit" />
        <image slot="icon-active" src="https://b.yzcdn.cn/public_files/2019/04/28/a4a60cccd63f9d3ff476cb22f51146d7.svg" class="member-center__order-icon" mode="aspectFit" />
        <text class="icon-text">{{ evaluateTitle }}</text>
      </van-tabbar-item>
      <van-tabbar-item
        info="{{ userAgreePrivacy && feedback > 99 ? '99+' : userAgreePrivacy && feedback > 0 ? feedback : '' }}"
        data-nav="refund"
        bind:click="handleTap"
      >
        <image slot="icon" src="https://b.yzcdn.cn/public_files/2019/04/28/15ed25836631717b7ff77266a1795328.svg" class="member-center__order-icon" mode="aspectFit" />
        <image slot="icon-active" src="https://b.yzcdn.cn/public_files/2019/04/28/15ed25836631717b7ff77266a1795328.svg" class="member-center__order-icon" mode="aspectFit" />
        <text class="icon-text">退款/售后</text>
      </van-tabbar-item>
    </van-tabbar>
  </user-authorize>
  <subscribe-guide show="{{ showSubscribeMask }}" bind:close="toggleSubscribeMask"/>
  <swiper vertical autoplay interval="{{ 4000 }}" style="height: 58px; margin-top: -10px;" wx:if="{{userAgreePrivacy && (showOrderPayPrompt || showOrderEvaluatePrompt)}}" circular>
      <swiper-item wx:if="{{ showOrderPayPrompt }}">
        <order-pay-prompt order-pay-prompts="{{ promptOrderPay }}"  />
      </swiper-item>
      <swiper-item  wx:if="{{ showOrderEvaluatePrompt }}">
        <order-evaluate-prompt order-evaluate-prompts="{{promptOrderEvaluation}}" />
      </swiper-item>
    </swiper>
</view>
