import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    plugins: {
      type: Array,
      value: () => []
    },
    mode: {
      type: Number,
      value: 1
    },
    iconMode: {
      type: Number,
      value: 1
    },
    values: {
      type: Object,
      value: {
        cart: 0,
        giftsCommunityVer: 0,
        gifts: 0,
        drugList: 0
      }
    },
    userAgreePrivacy: {
      type: <PERSON>olean,
      default: false,
    },
    links: Object,
    isNotValid: <PERSON>olean,
  },

  methods: {
    onPluginItemClicked(e) {
      const detail = e.detail;
      this.triggerEvent('pluginItemClicked', detail);
    },
    handleBindGetUserInfo(e) {
      this.triggerEvent('bindGetUserInfo', e.detail);
    }
  }
});
