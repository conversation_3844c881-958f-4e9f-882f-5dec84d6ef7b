<view class="plugin-list">
  <classic
    wx:if="{{ mode === 1 }}"
    icon-mode="{{ iconMode }}"
    plugins="{{ plugins }}"
    values="{{ values }}"
    userAgreePrivacy="{{ userAgreePrivacy }}"
    links="{{ links }}"
    is-not-valid="{{ isNotValid }}"
    bind:pluginItemClicked="onPluginItemClicked"
    bind:bindGetUserInfo="handleBindGetUserInfo"
  />
  <cube
    wx:if="{{ mode === 2 }}"
    icon-mode="{{ iconMode }}"
    plugins="{{ plugins }}"
    values="{{ values }}"
    links="{{ links }}"
    is-not-valid="{{ isNotValid }}"
    bind:pluginItemClicked="onPluginItemClicked"
    bind:bindGetUserInfo="handleBindGetUserInfo"
  />
</view>
