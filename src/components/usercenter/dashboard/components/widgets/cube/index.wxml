<view class="member-center__widget-cube">
  <view
    wx:for="{{ widgets }}"
    wx:for-item="widget"
    wx:for-index="index"
    wx:key="{{ widget.key }}"
    class="widget-item {{ dotClasses[index] }}"
  >
    <view wx:if="{{ widget.key === 'customerServiceChat' }}" class="message__contact-container">
      <message-contact business-id="{{extra.businessId}}" session-from="{{extra.sourceParam}}" open-type="contact" contact-class="message__contact-button" bindcontact="onContactBack">
        <image src="{{ widget.icon }}" class="icon" mode="aspectFit" />
        <text class="message__contact-text">客服聊天</text>
      </message-contact>
    </view>
    <block wx:else>
      <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" bindgetuserinfo="handleBindGetUserInfo"
        data-key="{{ widget.key }}"
        bind:next="handlePluginAuthorize"
      >
        <image src="{{ widget.icon }}" class="icon" mode="aspectFit" />
        <van-info
          wx:if="{{ widget.key === 'cart' && cartInfo }}"
          info="{{ cartInfo }}"
          custom-style="width: auto;right: 38%;margin: 10px auto;"
        />
        <van-info
          wx:if="{{ widget.key === 'gifts' && giftInfo }}"
          info="{{ giftInfo }}"
          custom-style="width: auto;right: 38%;margin: 10px auto;"
        />
        <view class="widget-item-view">{{ widget.title || item.text }}</view>
      </user-authorize>
    </block>
  </view>
</view>
