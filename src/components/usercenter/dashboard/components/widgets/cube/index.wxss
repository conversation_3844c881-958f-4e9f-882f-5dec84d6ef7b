@import "../../index.wxss";

.member-center__widget-cube {
  display: flex;
  flex-flow: row wrap;
  border-radius: 8px;
  background: #fff;
}
.widget-item {
  width: 25%;
  height: 70px;
  text-align: center;
  padding-top: 10px;
  color: #111;
}

.widget-item-view {
  width: 80px;
  margin: 10px auto;
  font-size: 12px;
  text-align: center;
}

.icon {
  padding-top: 5px;
  width: 20px;
  height: 20px;
}

.info {
  position:absolute;
  padding:0 .25em;
  top: 0;
  box-sizing:border-box;
  left: 60%;
  color:#fff;
  text-align:center;
  font-size:12px;
  min-width:1.4em;
  line-height:1.4;
  border-radius:.6em;
  background-color:#f44;
  transform:translateX(-50%);
  font-family:PingFang SC,Helvetica Neue,Arial,sans-serif;
}


@media (min-width: 700px) {
  .info {
    top: 10px;
    left: 55%;
  }
}

.message__contact-container {
  width: 100% !important;
  background-color: #fff;
  margin: 0 !important;
  margin-top: -5px !important;
}

.message__contact-button {
  width: 100%;
  background-color: #fff;
  border: none;
  outline: none;
}

.message__contact-button::after{
  border: none;
  outline: none;
}

.message__contact-text {
  display: block;
  font-size: 12px;
  color: #333;
  margin-top: -8px;
}
