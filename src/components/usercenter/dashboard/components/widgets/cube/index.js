import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';
import navigate from '@/helpers/navigate';
import { widgetsConfigMap, iconModeMap } from '../constant';
import { parseExt } from '../utils';

const app = getApp();

WscComponent({
  properties: {
    iconMode: {
      type: Number,
      default: 1,
    },
    plugins: {
      type: Array,
      value: [],
      observer(newVal) {
        // ?? ob obj
        this._setPlugins(newVal);
      },
    },
    values: {
      type: Object,
      value: {
        cart: 0,
        giftsCommunityVer: 0,
        gifts: 0,
      },
      observer(newVal) {
        // ?? ob obj
        this._setInfo(newVal);
      },
    },
    links: Object,
    isNotValid: Boolean,
  },

  data: {
    widgets: [],
    dotClasses: [],
    giftInfo: '',
    cartInfo: '',
    extra: {
      sourceParam: '',
      businessId: '',
    },
  },
  attached() {
    this.getImInfo();
  },

  methods: {
    onContactBack: navigate.contactBack,

    _setPlugins(plugins) {
      const widgets = plugins
        .filter(
          (item) => item.type !== 'blankLine' && widgetsConfigMap[item.type]
        )
        .map((item) => {
          if (item.type === 'ump_conference') {
            // 营销会场曝光埋点
            this.setViewLogger();
          }
          return {
            key: item.type,
            title: item.title,
            text: widgetsConfigMap[item.type].text,
            icon: widgetsConfigMap[item.type][
              iconModeMap[+this.properties.iconMode || 1]
            ],
          };
        });

      const dotClasses = [];
      for (let i = 0; i < widgets.length; i++) {
        const widget = widgets[i];
        const { key } = widget;
        // eslint-disable-next-line
        let value = this.properties.values[widget.key];

        let dotClass = '';
        if (['giftsCommunityVer'].indexOf(key) >= 0 && value) {
          dotClass = 'widget-item--dot';
        }
        dotClasses.push(dotClass);
      }

      // eslint-disable-next-line
      let value = this.properties.values['cart'];
      const giftValue = get(this.properties, 'values.gifts');
      const cartInfo = value > 99 ? '99+' : value;
      const giftInfo = giftValue > 99 ? '99+' : giftValue;
      this.setYZData({
        widgets,
        dotClasses,
        cartInfo,
        giftInfo,
      });
    },

    _setInfo(values) {
      const value = values.cart;
      const giftInfo = values.gifts;
      const cartInfo = value > 99 ? '99+' : value;
      this.setYZData({
        cartInfo,
        giftInfo,
      });
    },

    handlePluginAuthorize(e) {
      // 用户权限同意之后，才能进行下一步
      this.clickItem(e);
    },

    clickItem(e) {
      const { key } = e.currentTarget.dataset;
      if (this.properties.links[key]) {
        const url = this.properties.links[key];
        // 如果有ext 字段就把跳转链接拼接上 id
        const umpItem = this.properties.plugins.find(
          (item) => item.type === key
        );
        if (umpItem && umpItem.ext) {
          const { id } = parseExt(umpItem.ext);
          const newUrl = `${url}?id=${id}`;
          navigate.navigate({ url: newUrl });
        } else {
          navigate.navigate({ url });
        }
      } else {
        this.triggerEvent('pluginItemClicked', key);
      }
    },
    handleBindGetUserInfo(e) {
      this.triggerEvent('bindGetUserInfo', e.detail);
    },
    setViewLogger() {
      app.logger &&
        app.logger.log({
          et: 'view', // 事件类型
          ei: 'component_view', // 事件标识
          en: '组件曝光', // 事件名称
          params: {
            component: 'mustTools',
          }, // 事件参数
        });
    },
    getImInfo() {
      app.getDefaultImData().then((data) => {
        this.setYZData({
          extra: data,
        });
      });
    },
  },
});
