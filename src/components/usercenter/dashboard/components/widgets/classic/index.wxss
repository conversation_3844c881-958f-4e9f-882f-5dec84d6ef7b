.classic-group__item {
  position: relative;
  overflow: hidden;
}
.classic-group__item:first-of-type {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.classic-group__item:last-of-type {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.classic-group__item + .classic-group__item::after {
  content: "";
  position: absolute;
  left: 15px;
  top: -1px;
  width: 100%;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  transform: scaleY(0.5);
}
.classic-group .classic-group__item--cart {
  font-size: 12px;
  color: #fff;
  padding: 1px .4em;
  text-align: center;
  min-width: 1.2em;
  border-radius: .8em;
  box-sizing: border-box;
  background-color: #f44;
}
.classic-group .van-cell {
  align-items: center;
  color: #111;
}
.classic-group .van-cell:active {
  background: #fff;
}
.classic-group .van-cell:active {
  background: #fff;
}
.classic-group .van-cell__title text {
  position: relative;
}
.classic-group .classic-group__item--gifts {
  font-size: 12px;
  color: #999;
  position: relative;
  display: inline-block;
  padding-right: 12px;
}
.classic-group .classic-group__item--gifts::after {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  position: absolute;
  right: 0px;
  top: 50%;
  margin-top: -3px;
  border-radius: 50%;
  background: #f00;
}
.classic-group .van-cell__left-icon {
  margin-top: -4px;
  margin-right: 10px;
  color: #333;
}
.classic-group .van-cell__right-icon {
  color: #999999;
}
.classic-group__whitespace {
  height: 10px;
}
.classic-group__arrow {
  font-size: 16px;
}
.icon {
  width: 20px;
  height: 20px;
  line-height: 24px;
  margin: 2px 10px 2px 0;
}

.message__contact-container {
  box-sizing:border-box;
  background-color:#fff;
  color:#333;
  display:flex;
  font-size:14px;
  line-height:24px;
  padding:10px 15px;
  position:relative;
  width:100%;
}

.message__contact-button {
  width: 100%;
  background-color: #fff;
  color:#333;
  text-align: left;
  height: 24px;
  line-height: 24px;
  font-size: 14px;
  padding: 0;
  padding-left: 2px;
  margin: 0;
  border: none;
  outline: none;
}

.message__contact-button::after{
  border: none;
  outline: none;
}

.authorize-userinfo {
  text-align: left;
}
