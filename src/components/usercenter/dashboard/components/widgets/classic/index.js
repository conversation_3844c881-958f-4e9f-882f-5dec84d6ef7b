import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';
import { widgetsConfigMap, iconModeMap } from '../constant';
import { parseExt } from '../utils';

const app = getApp();

WscComponent({
  properties: {
    iconMode: {
      type: Number,
      default: 1,
    },
    plugins: {
      type: Array,
      value: [],
      observer(newVal) {
        // ?? ob obj
        this._setPlugins(newVal);
      },
    },
    values: {
      type: Object,
      observer(newVal) {
        this._setPluginValues(newVal);
      },
    },
    userAgreePrivacy: {
      type: Boolean,
      default: false,
    },
    links: Object,
    isNotValid: Boolean,
  },

  data: {
    widgetGroups: [],
    pluginValues: {
      drugList: 0,
    },
    extra: {
      sourceParam: '',
      businessId: '',
    },
  },
  attached() {
    this.getImInfo();
  },

  methods: {
    onContactBack: navigate.contactBack,

    _setPluginValues(val) {
      this.setYZData({
        pluginValues: val,
      });
    },

    _setPlugins(plugins) {
      const widgetGroups = [];
      let blankGroup = {
        type: 'blank-group',
        widgets: [],
      };
      let widgetGroup = {
        type: 'widget-group',
        widgets: [],
      };
      plugins.forEach((item) => {
        if (item.type === 'blankLine') {
          if (widgetGroup.widgets.length > 0) {
            widgetGroups.push(widgetGroup);
            widgetGroup = {
              type: 'widget-group',
              widgets: [],
            };
          }
          blankGroup.widgets.push({
            key: item.type,
            title: item.title,
          });
        } else {
          if (blankGroup.widgets.length > 0) {
            widgetGroups.push(blankGroup);
            blankGroup = {
              type: 'blank-group',
              widgets: [],
            };
          }
          if (widgetsConfigMap[item.type]) {
            widgetGroup.widgets.push({
              ...item,
              key: item.type,
              title: item.title,
              text: widgetsConfigMap[item.type].text,
              icon: widgetsConfigMap[item.type][
                iconModeMap[+this.properties.iconMode || 1]
              ],
            });
            if (item.type === 'ump_conference') {
              // 营销会场曝光埋点
              this.setViewLogger();
            }
          }
        }
      });
      if (blankGroup.widgets.length > 0) {
        widgetGroups.push(blankGroup);
      } else {
        widgetGroups.push(widgetGroup);
      }
      this.setYZData({
        widgetGroups,
      });
    },

    handlePluginAuthorize(e) {
      // 用户权限同意之后，才能进行下一步
      this.clickItem(e);
    },
    clickItem(e) {
      const { key } = e.currentTarget.dataset;
      if (this.properties.links[key]) {
        const url = this.properties.links[key];
        // 如果有ext 字段就把跳转链接拼接上 id
        const umpItem = this.properties.plugins.find(
          (item) => item.type === key
        );
        if (umpItem && umpItem.ext) {
          const { id } = parseExt(umpItem.ext);
          const newUrl = `${url}?id=${id}`;
          navigate.navigate({ url: newUrl });
        } else {
          navigate.navigate({ url });
        }
      } else {
        this.triggerEvent('pluginItemClicked', key);
      }
    },

    handleBindGetUserInfo(e) {
      this.triggerEvent('bindGetUserInfo', e.detail);
    },
    setViewLogger() {
      app.logger &&
        app.logger.log({
          et: 'view', // 事件类型
          ei: 'component_view', // 事件标识
          en: '组件曝光', // 事件名称
          params: {
            component: 'mustTools',
          }, // 事件参数
        });
    },
    getImInfo() {
      app.getDefaultImData().then((data) => {
        this.setYZData({
          extra: data,
        });
      });
    },
  },
});
