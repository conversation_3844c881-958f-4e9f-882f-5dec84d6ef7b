<template name="blank-group">
  <view
    class="classic-group__item-group-empty"
  >
    <block wx:for="{{ widgets }}" wx:key="{{ widget.key + index }}">
      <view class="classic-group__whitespace" />
    </block>
  </view>
</template>

<template name="widget-group">
  <view
    class="classic-group"
  >
    <view
      wx:for="{{ widgets }}"
      wx:for-index="index"
      wx:for-item="widget"
      wx:key="{{ widget.key + index }}"
      class="classic-group__item"
    >
      <view wx:if="{{ widget.key === 'customerServiceChat' }}" class="message__contact-container">
        <image src="{{ widget.icon }}" class="icon" mode="aspectFit" />
        <message-contact style="width: 100%;" open-type="contact" contact-class="message__contact-button" business-id="{{extra.businessId}}" session-from="{{extra.sourceParam}}" bindcontact="onContactBack">客服聊天</message-contact>
        <van-icon name="arrow" color="#999" custom-class="classic-group__arrow"/>
      </view>
      <block wx:else>
        <user-authorize
          authTypeList="{{ ['nicknameAndAvatar'] }}"
          btn-class="authorize-userinfo"
          bindgetuserinfo="handleBindGetUserInfo"
          data-key="{{ widget.key }}"
          bind:next="handlePluginAuthorize"
        >
          <van-cell
            border="{{ false }}"
            title="{{ widget.title || widget.text }}"
            custom-class="classic-group__arrow"
            is-link
          >
            <image slot="icon" src="{{ widget.icon }}" class="icon" mode="aspectFit" />
            <van-info
              wx:if="{{ widget.key === 'cart' && pluginValues.cart }}"
              info="{{ userAgreePrivacy ? (pluginValues.cart > 99 ? '99+' : pluginValues.cart) : '' }}"
              custom-style="right: 50px;top: 14px;"
            />
            <van-info
              wx:if="{{ widget.key === 'drugList' && pluginValues.drugList }}"
              info="{{ userAgreePrivacy ? (pluginValues.drugList > 99 ? '99+' : pluginValues.drugList) : '' }}"
              custom-style="right: 50px;top: 21px;"
            />
            <text
              class="classic-group__item--gifts"
              wx:if="{{ widget.key === 'giftsCommunityVer' && pluginValues[widget.key] && userAgreePrivacy }}"
            >{{ pluginValues[widget.key] }}件未领取</text>
          </van-cell>
        </user-authorize>
      </block>
    </view>
  </view>
</template>

<view class="classic-group-wrapper">
  <block
    wx:for="{{ widgetGroups }}"
    wx:for-index="index"
    wx:for-item="widgetGroup"
    wx:key="{{ index }}"
  >
    <template is="{{ widgetGroup.type }}" data="{{ ...widgetGroup, pluginValues, extra  }}"></template>
  </block>
</view>
