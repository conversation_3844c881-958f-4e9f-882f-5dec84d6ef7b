<wxs src="./index.wxs" module="util" />

<view class="edu-widgets--cube">
  <view
    wx:for="{{ widgets }}"
    wx:for-item="widget"
    wx:for-index="index"
    wx:key="{{ widget.key }}"
    data-key="{{ widget.key }}"
    bindtap="handleWidgetClicked"
    class="widget"
  >
    <block>
      <image src="{{ widget.icon }}" class="widget__icon" mode="aspectFit" />
      <view
        wx:if="{{ util.isShowBadge(badges, widget.key) }}"
        class="widget__badge {{ 'widget__badge--' + util.getBadgesType(badges, widget.key) }}"
      >
        {{ util.getBadgesContent(badges, widget.key) }}
      </view>
      <view class="widget__title">{{ widget.title }}</view>
    </block>
  </view>
</view>
