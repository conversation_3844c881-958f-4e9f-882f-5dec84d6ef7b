function isShowBadge(badges, key) {
  return !!(badges[key] || {}).value;
}

function getBadgesType(badges, key) {
  const badge = badges[key] || {};
  return badge.type || 'red-point';
}

function getBadgesContent(badges, key) {
  const badge = badges[key] || {};
  if (badge.num && typeof badge.num === 'number') {
    if (badge.num > 99) {
      return '99+';
    }
    return badge.num;
  }
  return '';
}

module.exports = {
  isShowBadge,
  getBadgesType,
  getBadgesContent,
};
