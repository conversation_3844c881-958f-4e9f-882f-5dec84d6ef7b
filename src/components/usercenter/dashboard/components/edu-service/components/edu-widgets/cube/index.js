import WscComponent from 'pages/common/wsc-component';
import { WIDGETS_ICON_MAP, ICON_MODE_MAP } from '../constants';

WscComponent({
  properties: {
    services: {
      type: Array,
      value: [],
      observer(newVal) {
        this._setServices(newVal);
      },
    },
    iconMode: {
      // 图标风格（九宫格）：1填色版，2线框版
      type: Number,
      value: 1,
    },
    badges: {
      // 右上角标记
      type: Object,
      value: {},
    },
    links: {
      type: Object,
      value: {},
    },
  },

  data: {
    widgets: [],
  },

  methods: {
    handleWidgetClicked(e) {
      const key = e.currentTarget.dataset.key;
      this.triggerEvent('widgetClicked', key);
    },
    _setServices(services) {
      const widgets = services
        .filter((item) => item.type !== 'blankLine')
        // 过滤不受支持的服务
        .filter((widget) => (WIDGETS_ICON_MAP[widget.type] || {})[ICON_MODE_MAP[+this.iconMode || 1]] )
        .map((item) => ({
          key: item.type,
          title: item.title,
          icon: WIDGETS_ICON_MAP[item.type][ICON_MODE_MAP[+this.iconMode || 1]],
        }));

      this.setYZData({
        widgets,
      });
    },
  },
});
