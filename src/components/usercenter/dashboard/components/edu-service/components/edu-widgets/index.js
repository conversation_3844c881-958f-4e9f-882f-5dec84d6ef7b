import WscComponent from 'pages/common/wsc-component';
import navigate from '@/helpers/navigate';

WscComponent({
  properties: {
    services: {
      type: Array,
      value: [],
    },
    mode: {
      // 页面风格：1经典版，2九宫格版
      type: Number,
      value: 2,
    },
    iconMode: {
      // 图标风格（九宫格）：1填色版，2线框版
      type: Number,
      value: 1,
    },
    badges: {
      // 是否展示右上角小红点。 特例：邀请有礼显示「奖」标
      type: Object,
      value: {},
    },
    links: {
      // 对应的跳转链接
      type: Object,
      value: {},
    },
  },

  methods: {
    handleWidgetClicked(e) {
      const key = e.detail;
      if (this.properties.links[key]) {
        navigate.navigate({
          url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
            this.properties.links[key]
          )}`,
        });
      } else {
        this.triggerEvent('eduServicesWidgetClicked', key);
      }
    },
  },
});
