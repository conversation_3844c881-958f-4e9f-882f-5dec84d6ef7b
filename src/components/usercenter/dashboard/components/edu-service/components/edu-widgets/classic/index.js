import WscComponent from 'pages/common/wsc-component';
import { WIDGETS_ICON_MAP, ICON_MODE_MAP } from '../constants';

WscComponent({
  properties: {
    services: {
      type: Array,
      value: [],
      observer(newVal) {
        this._setServices(newVal);
      },
    },
    iconMode: {
      // 图标风格（九宫格）：1填色版，2线框版
      type: Number,
      value: 1,
    },
    badges: {
      // 右上角标记
      type: Object,
      value: {},
    },
    links: {
      type: Object,
      value: {},
    },
  },

  data: {
    widgetGroups: [],
  },

  methods: {
    _setServices(services) {
      const widgetGroups = [];
      let blankGroup = {
        type: 'blank-group',
        widgets: [],
      };
      let widgetGroup = {
        type: 'widget-group',
        widgets: [],
      };
      services.forEach((item) => {
        if (item.type === 'blankLine') {
          if (widgetGroup.widgets.length > 0) {
            widgetGroups.push(widgetGroup);
            widgetGroup = {
              type: 'widget-group',
              widgets: [],
            };
          }
          blankGroup.widgets.push({
            key: item.type,
            title: item.title,
          });
        } else {
          if (blankGroup.widgets.length > 0) {
            widgetGroups.push(blankGroup);
            blankGroup = {
              type: 'blank-group',
              widgets: [],
            };
          }
          if (WIDGETS_ICON_MAP[item.type]) {
            widgetGroup.widgets.push({
              key: item.type,
              title: item.title,
              icon:
                WIDGETS_ICON_MAP[item.type][
                  ICON_MODE_MAP[+this.properties.iconMode || 1]
                ],
            });
          }
        }
      });
      if (blankGroup.widgets.length > 0) {
        widgetGroups.push(blankGroup);
      } else {
        widgetGroups.push(widgetGroup);
      }
      this.setYZData({
        widgetGroups,
      });
    },

    handleWidgetClicked(e) {
      const key = e.currentTarget.dataset.key;
      this.triggerEvent('widgetClicked', key);
    },
  },
});
