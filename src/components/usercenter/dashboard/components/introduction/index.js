import WscComponent from 'shared/common/base/wsc-component/index';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import { checkEduChainStore } from '@youzan/utils-shop';
import { findAllShowTipByPageType } from './api';

const INTRODUCTION_EXPIRED_KEY = 'introduce-expire';

WscComponent({
  properties: {
    introduction: {
      type: Object,
      value: {},
      observer: 'onIntroductionShow',
    },
  },

  data: {
    showIntroduceCard: false,
    hasActivity: false,
    isShowInviteEntry: false,
  },

  attached() {
    const expiredTime = wx.getStorageSync(INTRODUCTION_EXPIRED_KEY) || 0;
    const currentTime = Date.now();
    if (currentTime > expiredTime) {
      this.setYZData({
        showIntroduceCard: true,
      });
    }
    this.getIsShowInviteEntry();
  },

  methods: {
    onIntroduceCardHide() {
      this.setYZData({
        showIntroduceCard: false,
      });
      const expiredTime = new Date().setHours(23, 59, 59);
      wx.setStorage({
        key: INTRODUCTION_EXPIRED_KEY,
        data: expiredTime,
      });
    },

    onJumpToActivity() {
      const app = getApp();
      const alias = this.data.introduction.alias || '';
      const { shopMetaInfo = {} } = app.getShopInfoSync();
      const isEduChainStore = checkEduChainStore(shopMetaInfo);
      const _kdtId = isEduChainStore ? app.getHQKdtId() : app.getKdtId();
      wx.navigateTo({
        url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
          `https://h5.youzan.com/wscvis/ump/introduction/old-student?kdt_id=${_kdtId}&alias=${alias}&from=usercenter_promotion_ad`
        )}`,
      });
    },

    onIntroductionShow(obj) {
      const hasActivity = !isEmpty(obj);
      this.setYZData({
        hasActivity,
      });
    },

    async getIsShowInviteEntry() {
      const data = await findAllShowTipByPageType();
      // 1作业、2个人中心、3家校圈、4证书
      const isShowInviteEntry = data.some((item) => item.moduleType === 2);
      this.setYZData({
        isShowInviteEntry,
      });
    },
  },
});
