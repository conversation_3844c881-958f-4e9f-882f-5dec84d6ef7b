<view
  class="introduction-card"
  wx:if="{{ hasActivity && showIntroduceCard && isShowInviteEntry }}"
  bindtap="onJumpToActivity"
>
  <van-cell
    title="赢机构大礼"
    center
    use-label-slot
    custom-class="introduction-card__cell"
    label-class="introduction-card__desc"
    title-class="introduction-card__title"
  >
    <view
      slot="label"
      class="introduction-card__label"
    >
      <text>
        邀请好友上课
      </text>
      <view
        class="introduction-card__share"
      >
        去分享
        <van-icon name="arrow" size="10px" />
      </view>
    </view>
    <image
      slot="right-icon"
      class="introduction-card__icon"
      src="https://b.yzcdn.cn/public_files/6adbadf4f5f99c9d70549018a119c187.svg"
    />
  </van-cell>
  <view
    class="introduction-card__close"
    bindtap="onIntroduceCardHide"
  >
    <van-icon name="cross" size="10px" />
  </view>
</view>