.introduction-card {
  position: relative;
  width: 94%;
  margin: 0 auto 10px;
  border-radius: 8px;
  overflow: hidden;

  &__cell {
    padding: 11px 30px 6px 20px;
  }

  &__label {
    display: flex;
    align-items: center;
  }

  &__share {
    display: inline-flex;
    margin-left: 8px;
    padding: 1px 3px 1px 6px;
    line-height: 16px;
    font-size: 11px;
    background-color: #ff9502;
    color: #fff;
    border-radius: 18px;
    align-items: center;
  }

  &__icon {
    width: 74px;
    height: 64px;
  }

  &__close {
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 24px;
    background-color: #ebedf0;
    border-bottom-left-radius: 13px;
    text-align: center;
    line-height: 24px;
    color: #969799;
  }
  
  &__title {
    font-size: 24px;
    font-weight: 900;
  }

  &__desc {
    font-size: 14px;
    font-weight: normal;
  }
}