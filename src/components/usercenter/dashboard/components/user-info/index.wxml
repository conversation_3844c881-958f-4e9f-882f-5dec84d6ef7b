<view class="member-center__user-info-wrapper">
  <user-authorize
    authTypeList="{{ ['nicknameAndAvatar'] }}"
    data-nav="authorize"
    bind:next="handleProtocolAuthorize"
    bind:fail="clearTapData"
    bindgetuserinfo="goto"
  >
    <view style="{{ outerStyle }}">
      <view class="member-center__user-info {{ layoutClx[align-1] }}" style="{{ layoutStyle }}">
        <image src="{{ imageUrl }}" style="display: none" bind:load="handlePerReport"  />
        <view
          wx:if="{{ isSupportSign }}"
          class="user-info__sign"
          style="{{ signAndCodeStyle }}"
          data-nav="sign"
          bindtap="handleTap"
        >
          <image
            class="{{ isSigned && userAgreePrivacy ? 'user-info__sign-signed-img' : 'user-info__sign-img' }}"
            alt="sign"
            src="{{ isSigned && userAgreePrivacy ? 'https://img01.yzcdn.cn/weapp/wsc/bQPAx4.png' : 'https://img01.yzcdn.cn/weapp/wsc/Cmu1rwv.png' }}"
          />
          <text>{{ isSigned && userAgreePrivacy ? '已签到' : '签到' }}</text>
        </view>
        <!-- ⬇️ 非 CRM 打通商家，会员码跳转前不需要校验授权手机号（原逻辑） -->
        <view
          class="user-info__sign user-info__code"
          style="{{ signAndCodeStyle }}"
          data-nav="code"
          bindtap="handleTap"
          wx:if="{{ showMemberCode && !needAuthMobileBeforeMemberCode }}"
        >
          <image
            class="user-info__sign-img"
            alt="code"
            src="https://b.yzcdn.cn/public_files/a86a6f5b68a8f6fdacca865aa3ade4ef.png"
          ></image>
          <text>会员码</text>
        </view>
        <view
          wx:if="{{ align }}"
          class="user-info__avatar"
          data-nav="account"
          style="{{ userAvatarStyle }}"
          bindtap="handleTap"
        >
          <image src="{{ avatar || '//b.yzcdn.cn/showcase/membercenter/2018/08/06/<EMAIL>' }}" alt="avatar" />
        </view>
        <view
          wx:if="{{ align }}"
          class="user-info__info"
          style="{{ userInfoStyle }}"
        >
          <view
            class="user-info__username"
            data-nav="account"
            style="{{ userNameStyle }}"
            bindtap="handleTap"
          >
            {{ userName || '点击显示微信头像' }}
          </view>
          <view
            wx:if="{{ showGrowth && userAgreePrivacy }}"
            data-nav="growth"
            class="user-info__growth {{ levelValue > 0 && !isSupportGrowthValue ? 'user-info__consume-level' : '' }}"          bindtap="handleTap"
          >
            {{ userAgreePrivacy ? levelBtnText : '' }}
          </view>
        </view>
        <view class="user-info__level-wrapper" wx:if="{{ showLevel }}" catchtap="noop">
          <user-authorize
            wx:if="{{ memberType === 1 }}"
            authTypeList="{{['protocol', 'mobile']}}"
            data-nav="authorize"
            bind:next="handleRegistryMem"
            bind:fail="handleRejectRegistryMem"
            bind:popup-show="handleRegistryMemStart"
          >  
            <view class="user-info__level-1" >

              <text class="name {{ levelIconType === 1 ? 'name--vip' : '' }}">{{ benefitText || shopName }}</text>
              <view
                class="{{ levelClx }}"
                data-nav="level"
                bindtap="handleTap"
              >
                <text>{{ userAgreePrivacy ? grade : '' }}</text>
                <van-icon name="arrow" custom-class="user-info__arrow" />
              </view>
            </view>
          </user-authorize>
          <view class="user-info__level-2-wrapper" wx:if="{{ memberType === 2 }}">
            <user-authorize
              authTypeList="{{['protocol', 'mobile']}}"
              data-nav="authorize"
              bind:next="handleRegistryMem"
              bind:fail="handleRejectRegistryMem"
              bind:popup-show="handleRegistryMemStart"
            >  
              <view class="user-info__level-2" wx:if="{{ memberType === 2 }}">
                <view class="level2-header">
                  {{ shopName }}
                </view>
                <view class="level2-body">
                  <van-button
                    class="level"
                    round
                    plain
                    data-nav="level"
                    bindtap="handleTap"
                  >
                    <view class="level-name" style="{{ type2LevelColor }}">
                      <text>{{ userAgreePrivacy ? grade : '' }}</text>
                      <van-icon name="arrow" />
                    </view>
                  </van-button>
                  <image
                    class="level2-vip"
                    src="https://img01.yzcdn.cn/weapp/wsc/YIQ5ItL.png"
                    alt="vip"
                  />
                </view>
              </view>
            </user-authorize>
          </view>
          <user-authorize
            wx:if="{{ memberType === 3 }}"
            authTypeList="{{['protocol', 'mobile']}}"
            data-nav="authorize"
            bind:next="handleRegistryMem"
            bind:fail="handleRejectRegistryMem"
            bind:popup-show="handleRegistryMemStart"
          >  
            <view class="user-info__level-3">
              <text i18n-ellipsis class="name {{ levelIconType === 1 ? 'name--vip' : '' }}">{{ benefitText || shopName }}</text>
              <view
                class="{{ levelClx }}"
                data-nav="level"
                bindtap="handleTap"
              >
                <text>{{ userAgreePrivacy ? grade : '' }}</text>
                <van-icon name="arrow" custom-class="arrow-custom" />
              </view>
              <image
                class="level3-member"
                src="https://img01.yzcdn.cn/weapp/wsc/8StRDac.png"
                alt="member"
              />
            </view>
          </user-authorize>
        </view>
      </view>
    </view>
  </user-authorize>
  <!-- ⬇️ CRM 打通商家，会员码跳转前校验授权手机号 -->
  <user-authorize
    authTypeList="{{ ['nicknameAndAvatar', 'mobile'] }}"
    data-nav="code-authorize-mobile"
    bind:next="handleTapNeedAuthMobileCode"
    bind:fail="handleMobileAuthFail"
    class="user-info__code-auth"
    style="{{ signAndCodeStyle }}"
    wx:if="{{ showMemberCode && needAuthMobileBeforeMemberCode }}"
  >
    <view
      class="user-info__code-auth-btn"
    >
      <image
        class="user-info__code-auth-img"
        alt="code"
        src="https://b.yzcdn.cn/public_files/a86a6f5b68a8f6fdacca865aa3ade4ef.png"
      ></image>
      <text>会员码</text>
    </view>
  </user-authorize>
  <user-authorize
    wx:if="{{ showBindUser }}"
    authTypeList="{{ ['mobile']}}"
    bind:next="onAccountSuccess"
  >
    <view class="bind-user__wrapper">
      <view class="bind__container">
        <view class="bind__title">{{ bindTips }}</view>
        <view class="bind__button">
          <theme-view
            bg="main-bg"
            border="main-bg"
            border-after="main-bg"
          >
            <button class="btn-userinfo btn-class">登录</button>
          </theme-view>
        </view>
      </view>
    </view>
  </user-authorize>
  <account-wx-login id="account-wx-login" />
  <subscribe-message show="{{subscribeMarkShow}}"></subscribe-message>
</view>

