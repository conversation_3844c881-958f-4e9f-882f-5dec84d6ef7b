import WscComponent from 'pages/common/wsc-component/index';
import wxLogin from 'utils/wx-login.js';
import navigate from '@/helpers/navigate';
import {
  subscribeMessage,
  getSubscribeMessageStatus,
  PAGE_ID,
} from 'utils/retail-subscribe-message';
import openWebView from 'utils/open-web-view';
import args from '@youzan/weapp-utils/lib/args';
import fullfillImage from '@youzan/utils/url/fullfillImage';
import { Hummer } from 'shared/utils/hummer';
import { getHeight } from 'shared/utils/nav-config';

const valueBg =
  'https://img01.yzcdn.cn/upload_files/2019/01/31/Fp3kIohim8ZiB1yLepoMMW8is_qY.png';

const areaHeight = 172;
const avatarWidth = 60;

const gradientList = [
  '',
  'linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 1)), ',
];
const app = getApp();

WscComponent({
  properties: {
    avatar: {
      type: String,
      value:
        '//b.yzcdn.cn/showcase/membercenter/2018/08/06/<EMAIL>',
      observer() {
        this._initStyle();
      },
    },
    userName: {
      type: String,
      value: '',
      observer() {
        this._initStyle();
      },
    },
    userNameColor: {
      type: String,
      value: '#333',
      observer() {
        this._initStyle();
      },
    },
    grade: {
      type: String,
      value: '',
      observer() {
        this._initStyle();
      },
    },
    isSupportSign: {
      type: Boolean,
      value: true,
    },
    showLevel: {
      type: Boolean,
      value: true,
    },
    showSign: {
      type: Boolean,
      value: false,
    },
    isMember: {
      type: Boolean,
      value: true,
    },
    memberType: {
      type: Number,
      value: 1,
      observer() {
        this._initStyle();
      },
    },
    backgroundImage: {
      type: String,
      value: valueBg,
      observer() {
        this._initStyle();
      },
    },
    showBindUser: {
      type: Boolean,
      value: false,
    },
    align: {
      type: Number,
      value: 1,
      observer() {
        this._initStyle();
      },
    },
    gradient: {
      type: Number,
      value: 1,
      observer() {
        this._initStyle();
      },
    },
    links: {
      type: Object,
      value: {},
    },
    bindTips: String,
    isNotValid: Boolean,
    isLevelEnable: {
      type: Boolean,
      value: true,
    },
    growthValue: {
      type: Number,
      value: 0,
    },
    showGrowth: {
      type: Boolean,
      value: false,
    },
    isSupportGrowthValue: {
      type: Boolean,
      value: false,
    },
    levelValue: {
      type: Number,
      value: 0,
    },
    benefitText: {
      type: String,
      value: '',
    },
    levelIconType: {
      type: Number,
      value: 0,
    },
    userAgreePrivacy: {
      type: Boolean,
      default: false,
    },
    isImmersive: {
      type: Boolean,
      default: false,
    },
    showInfoCompleted: {
      type: Boolean,
      default: false,
    },
  },

  data: {
    layoutClx: ['avatar-at-left', 'avatar-at-top', 'avatar-at-right'],
    layoutStyle: '',
    outerStyle: '',
    type2LevelColor: '',
    levelClx: '',
    userNameStyle: '',
    userAvatarStyle: '',
    signAndCodeStyle: '',
    shopName: '',
    tapData: {},
    levelBtnText: '', // * 个人中心会员中心按钮显示文字
    showMemberCode: true, // 是否显示会员码
    hasAuthorized: false, // 是否已授权
    subscribeMarkShow: false, // 展示订阅消息引导组件
    needAuthMobileBeforeMemberCode: false, // 跳转会员码之前是否需要授权手机号
    imageUrl: '',
  },
  ready() {
    this._initStyle();
    this.initSdk();
    this.initAuthStatusAndNeedAuthMobileBeforeMemberCode();
  },

  methods: {
    handlePerReport() {
      Hummer.mark?.log?.({ tag: 'usercenter', scene: ['route'] });
    },
    onAccountSuccess() {
      this.triggerEvent('onAccountSuccess');
    },

    noop() {},

    handleRegistryMemStart() {
      app.logger?.log({
        et: 'click',
        ei: 'registry_mem_start',
        en: '尝试注册会员',
        params: {
          where: '个人中心',
        },
      });
    },

    handleRegistryMem() {
      // 已经开通会员或是付费会员时，不需要是否完善信息的校验
      if (this.properties.isMember || this.properties.levelIconType === 1) {
        this.jumpTo('level');
        return;
      }
      app
        .request({
          path: '/wscuser/level/api/checkMemRegistrationState.json',
        })
        .then(({ needFillInfo = false } = {}) => {
          if (needFillInfo) {
            openWebView(
              args.add('/wscuser/levelcenter/simplified-fill', {
                kdt_id: getApp().getKdtId(),
                levelType: 1,
                fromScene: 'complete',
                jumpTo: 'Home',
                eT: Date.now(),
              }),
              { title: '完善信息' }
            );
            return;
          }

          this.jumpTo('level');

          app.logger?.log({
            et: 'click',
            ei: 'registry_mem_succuss',
            en: '注册会员成功',
            params: {
              where: '个人中心',
            },
          });
        });
    },

    handleRejectRegistryMem() {
      app.logger?.log({
        et: 'click',
        ei: 'reject_registry_phone',
        en: '拒绝授权手机号',
        params: {
          where: '个人中心',
        },
      });
    },

    async handleProtocolAuthorize(e) {
      const handleKey = e.currentTarget?.dataset?.nav;

      // 仅当前用户授权完成才可以
      if (handleKey !== 'authorize') {
        return;
      }
      // 点击授权确定之后会触发这个函数，此时不触发订阅消息，需要再次点击之后触发
      if (this.data.hasAuthorized && !getSubscribeMessageStatus()) {
        await subscribeMessage(PAGE_ID.USER_CENTER, {
          onShowTips: () => {
            this.setYZData({
              subscribeMarkShow: true,
            });
          },
          onCloseTips: () => {
            this.setYZData({
              subscribeMarkShow: false,
            });
          },
        }).catch(() => {});
      }
      if (!this.data.hasAuthorized) {
        // 能走到这里说明已经授权成功了，调整 hasAuthorized 的状态，
        this.setYZData({
          hasAuthorized: true,
        });
        // 不再走之后跳转的逻辑
        return;
      }

      // 判断此次tap的数据
      const key = this.data.tapData?.currentTarget?.dataset?.nav;

      if (!key) {
        return;
      }

      // tap签到
      if (key === 'sign') {
        this.handleSign(this.data.tapData);
        return;
      }

      // tap会员码
      if (key === 'code') {
        this.handleCode(this.data.tapData);
        return;
      }

      // 其他原有逻辑
      this.goto(this.data.tapData);
    },
    handleTap(e) {
      this.setYZData({
        tapData: e,
      });
    },
    clearTapData() {
      // 清除点击信息
      this.setYZData({
        tapData: {},
      });
    },
    handleTapNeedAuthMobileCode(e) {
      const handleKey = e.currentTarget?.dataset?.nav;
      if (handleKey !== 'code-authorize-mobile') {
        return;
      }
      this.handleCode();
    },
    handleMobileAuthFail() {
      wx.showToast({ title: '请完成手机号授权后再打开会员码', icon: 'none' });
      this.clearTapData();
    },
    goto(e) {
      // 如果会员等级被禁用 点击不跳转会员等级页面
      if (!this.properties.userName) {
        this.triggerEvent('bindGetUserInfo', e.detail);
        return;
      }
      const key = e.currentTarget.dataset.nav;
      this.jumpTo(key);
    },
    jumpTo(key) {
      if (key === 'level' && app.getKdtId() === 47999) {
        return;
      }
      this.traceLog(key);
      if (this.properties.links[key]) {
        navigate.navigate({ url: this.properties.links[key] });
      } else {
        this.triggerEvent('tapUserInfo', key);
      }
    },
    traceLog(type) {
      if (!app.logger) return;
      switch (type) {
        case 'growth':
          app.logger.log({
            et: 'click', // 事件类型
            ei: 'click_membercenter_grow', // 事件标识
            en: '个人中心-点击成长值', // 事件名称
          });
          break;
        case 'level':
          app.logger.log({
            et: 'click', // 事件类型
            ei: 'click_membercenter_card', // 事件标识
            en: '个人中心-点击会员卡入口', // 事件名称
          });
          break;
      }
    },
    tapBindZanAccount() {
      this.triggerEvent('bindUser');
    },
    handleSign() {
      this.triggerEvent('tapSign');
    },
    handleCode() {
      navigate.navigate({
        url: `/packages/member-code/index?eT=${Date.now()}`,
      });
    },
    // 判断头像是否是居左样式并且卡片样式为样式一或者样式三
    isLayout() {
      const { memberType = 1, align = 1 } = this.properties;
      return align === 1 && (memberType === 1 || memberType === 3);
    },
    _initStyle() {
      const {
        backgroundImage = valueBg,
        gradient = 1,
        memberType = 1,
        align = 1,
        isImmersive = false,
        showInfoCompleted = false,
      } = this.properties;
      const shopInfo = getApp().getShopInfoSync();
      const shopName = shopInfo.shopName || shopInfo.shop_name;
      const imageUrl = fullfillImage(backgroundImage, 'large');
      // 沉浸式导航 - 增加padding-top
      const navHeight = getHeight();

      const style = `height: ${areaHeight}px; ${
        memberType === 2 ? 'overflow: hidden;' : ''
      }${memberType === 3 ? 'margin-bottom: 35px;' : ''};`;
      const extraBgStyle = isImmersive
        ? 'background-size: cover;background-position: center;'
        : 'background-size: 100% 100%;';
      let immersivePaddingTop = isImmersive ? navHeight : 0;
      let signAndCodeStyle = '';
      // 兼容沉浸式下的完善会员资料模块高度
      if (isImmersive) {
        if (showInfoCompleted) {
          immersivePaddingTop += 40;
        }
        signAndCodeStyle = `top:${immersivePaddingTop + 15}px`;
      }
      const outerStyle = `background-image: ${gradientList[gradient]}url('${imageUrl}');padding-top: ${immersivePaddingTop}px;${extraBgStyle}`;
      const levelColor = this.properties.grade ? '#e3c86f' : '#fff';
      const userNameColor = this.properties.userNameColor || '#333';
      // const userAvatarMarginTop = `margin-top: ${avatarMarginTop}px`;
      let userAvatarStyle = `width: ${avatarWidth}px; height: ${avatarWidth}px;`;
      let userInfoStyle = '';
      // ${align === 2 ? userAvatarMarginTop : ''}
      if (align === 2) {
        userInfoStyle += 'margin-top: 10px';
        if (!memberType || memberType === 2) {
          userAvatarStyle += 'margin-top: 55px;';
        } else if (memberType) {
          userAvatarStyle += 'margin-top: 25px';
        }
      }
      if (this.isLayout()) {
        userAvatarStyle += 'margin-bottom: 34px;';
        userInfoStyle += 'margin-bottom: 34px;';
      }
      if (align === 1 && memberType === 2) {
        userInfoStyle += 'margin-bottom: 0px;';
        userAvatarStyle += 'margin-bottom: 0px;';
      }
      this.setYZData({
        layoutStyle: style,
        outerStyle,
        signAndCodeStyle,
        type2LevelColor: `color: ${levelColor}`,
        levelClx: this.properties.grade ? 'level' : 'become-member',
        userNameStyle: `color: ${userNameColor};`,
        userAvatarStyle,
        userInfoStyle,
        shopName,
        imageUrl,
      });
    },

    loginBywxAuthorize(event) {
      const options = {
        event,
        context: this,
      };
      return wxLogin(options);
    },
    getYunSdk() {
      const app = getApp();
      if (!app.getYouZanYunSdk) {
        return false;
      }
      const sdk = app.getYouZanYunSdk();
      return sdk;
    },
    showMemberCode(flag) {
      this.setYZData({
        showMemberCode: flag,
      });
    },
    initSdk() {
      const sdk = this.getYunSdk();
      sdk &&
        sdk.setPageProcess('showMemberCode', this.showMemberCode.bind(this));
    },

    // 获取渲染组件时的授权状态
    initAuthorizeStatus() {
      return app
        .resolveTeeAPI()
        .then((api) => api.getUserPrivacy())
        .then((authState) => {
          const { userName, avatar } = this.properties;
          const hasAuthorized =
            userName !== '' && avatar !== '' && !!authState.nicknameAndAvatar;

          this.setYZData({ hasAuthorized });

          return {
            hasAuthorized,
            hasMobileAuthorized: !!authState.mobile,
          };
        });
    },

    async getNeedAuthMobileOfCode() {
      return app
        .request({
          path: '/wscuser/common/pre-check.json',
          method: 'get',
          data: {
            type: 'memberCode',
          },
        })
        .then((res) => {
          return res?.needAuthMobile;
        })
        .catch((err) => {
          console.warn('获取是否打通店铺失败', err);
          return false;
        });
    },

    async initAuthStatusAndNeedAuthMobileBeforeMemberCode() {
      const [needAuthMobileOfCode, authorizeStatus] = await Promise.all([
        this.getNeedAuthMobileOfCode(),
        this.initAuthorizeStatus(),
      ]);
      this.setYZData({
        needAuthMobileBeforeMemberCode:
          needAuthMobileOfCode && !authorizeStatus?.hasMobileAuthorized,
      });
    },
  },
  observers: {
    // 成长值会员展示为XX成长值，消费和储值会员展示为 VIPN
    'isSupportGrowthValue, levelValue, growthValue': function (
      isSupportGrowthValue,
      levelValue,
      growthValue
    ) {
      let levelBtnText = '';
      if (isSupportGrowthValue) {
        levelBtnText = `成长值${growthValue}`;
      } else if (levelValue > 0) {
        levelBtnText = `LV.${levelValue} >`;
      } else {
        levelBtnText = '会员中心';
      }
      this.setYZData(
        {
          levelBtnText,
        },
        {
          immediate: true,
        }
      );
    },
  },
});
