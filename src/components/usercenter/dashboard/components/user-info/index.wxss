@import "/components/zan-account/bind-phone-number/index.wxss";

.member-center__user-info-wrapper {
  margin-bottom: 10px;
  position: relative;
  z-index: 11;
}

.member-center__user-info {
  display: flex;
  height: 172px;
  background-size: 100% 100%;
}

.member-center__user-info .user-info__sign,
.user-info__code-auth {
  position: absolute;
  z-index: 2;
  top: 15px;
  right: 89px;
  box-sizing: border-box;
  width: 60px;
  height: 22px;
  padding: 2px 11px 2px 7px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  font-size: 12px;
  border-radius: 11px;
  color: #fff;
  background-color: rgba(0, 0, 0, .2);
}

.user-info__code-auth-btn {
  display: flex;
  align-items: center;
}

.member-center__user-info .user-info__code, .user-info__code-auth {
  width: auto;
  right: 14px;
  padding: 2px 9px 2px 8px;
}

.member-center__user-info .user-info__sign .user-info__sign-img,
.user-info__code-auth .user-info__code-auth-img {
  width: 12px;
  height: 12px;
  margin-right: 3px;
}

.member-center__user-info .user-info__sign .user-info__sign-signed-img {
  width: 10px;
  height: 8px;
  margin-right: 3px;
}

.member-center__user-info .user-info__avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  background-size: 60px 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .1);
  position: relative;
  z-index: 1;
}

.member-center__user-info .user-info__avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 1rpx solid rgba(255, 255, 255, .6);
}

.member-center__user-info .user-info__info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.member-center__user-info .user-info__username {
  font-size: 20px;
  line-height: 30px;
  font-weight: bold;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  position: relative;
  z-index: 1;
}

.member-center__user-info .user-info__growth {
  background-image: radial-gradient(circle at 2% 85%, #444039 0, #37332c 82%);
  font-size: 11px;
  color: #FDDDA5;
  letter-spacing: 0;
  padding: 2px 8px;
  line-height: 16px;
  border-radius: 10px;
  z-index: 1;
}

.member-center__user-info .user-info__growth.user-info__consume-level {
  font-style: italic;
}

.member-center__user-info.avatar-at-top {
  flex-direction: column;
  align-items: center;
}

.member-center__user-info.avatar-at-top .user-info__info {
  margin-top: 15px;
  align-items: center;
  flex-direction: row;
}

.member-center__user-info.avatar-at-top .user-info__info .user-info__growth {
  margin-left: 10px;
}

.member-center__user-info.avatar-at-top .user-info__avatar {
  width: 85px;
  height: 85px;
  background-size: 85px 85px;
  margin-top: 25px;
}

.member-center__user-info.avatar-at-top .user-info__username {
  font-size: 16px;
  line-height: 1;
}

.member-center__user-info.avatar-at-right {
  align-items: center;
  flex-direction: row-reverse;
  justify-content: space-between;
  padding: 0 15px;
}

.member-center__user-info.avatar-at-right .user-info__info {
  align-items: flex-start;
}

.member-center__user-info.avatar-at-left {
  align-items: center;
  padding: 0 15px;
}

.member-center__user-info.avatar-at-left .user-info__info {
  align-items: flex-start;
  margin-left: 15px;
}

.member-center__tel-not-bind {
  height: 50px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  font-size: 12px;
  color: #111;
  padding: 0 15px;
  margin-bottom: 10px;
}

.member-center__tel-not-bind .bind-btn {
  color: #f44;
  border-color: #f44;
}

.member-center__user-info-wrapper .bind-user__wrapper {
  margin-bottom: 10px;
}

.btn-userinfo {
  padding: 0;
  border-radius:0;
  background-color:transparent;
  font-size: inherit;
  color: inherit;
  line-height: inherit;
  width: 50px;
  height: 30px;
  line-height: 30px;
  color: #fff;
  font-size: 12px;
}

.btn-userinfo::after {
  border: none;
}

.bind__container .bind__title{
  flex: 1;
  text-align: left;
}

.bind__container .bind__button {
  margin-right: 15px;
}
.member-center__user-info .user-info__level-wrapper {
  position: absolute;
  z-index: 2;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-1 {
  margin: 0 auto;
  width: 94%;
  height: 40px;
  box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, .2);
  border-radius: 8px 8px 0 0;
  background: url(https://img01.yzcdn.cn/weapp/wsc/JzlEg0.png);
  background-size: cover;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-1 .name {
  font-size: 12px;
  padding-left: 10px;
  flex:1;
  text-align: left;
  color: #ffdea0;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-1 .level,
.member-center__user-info .user-info__level-wrapper .user-info__level-1 .become-member {
  font-size: 12px;
  line-height: 12px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  margin-top: 3px;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-1 .level .van-icon,
.member-center__user-info .user-info__level-wrapper .user-info__level-1 .become-member .van-icon {
  margin-left: 2px;
  font-size: 12px;
  opacity: .5;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-1 .name::before {
  content: ' ';
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 5px;
  background-image: url(https://img01.yzcdn.cn/public_files/8678298e07a105ab4402bb2a6c429df3.png);
  background-size: cover;
  vertical-align: -4px;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-1 .name.name--vip::before {
  background-image: url(https://img01.yzcdn.cn/public_files/e7a416a6071525321374eabdde0fbdf1.png);
}

.member-center__user-info .user-info__level-wrapper .user-info__level-2-wrapper {
  position: absolute;
  right: -60px;
  top: -90px;
  width: 179px;
  height: 99px;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-2 {
  box-shadow: 2px 2px 16px 0 rgba(0, 0, 0, .2);
  border-radius: 4px;
  background-image: linear-gradient(147deg, #414653 0%, #373B44 74%);
  color: #e3c86f;
  overflow: hidden;
  transform-origin: left;
  transform: rotate(15deg) translateX(10px);
}

.member-center__user-info .user-info__level-wrapper .user-info__level-2 .level2-header {
  height: 25px;
  line-height: 25px;
  text-align: left;
  font-size: 10px;
  padding-left: 10px;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-2 .level2-header::before {
  content: ' ';
  display: inline-block;
  width: 14px;
  height: 10px;
  margin-right: 5px;
  background-image: url(https://img01.yzcdn.cn/weapp/wsc/2Ek0rWe.png);
  background-size: cover;
  vertical-align: -2px;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-2 .level2-body {
  background-image: linear-gradient(134deg, #4D525E 0%, #343841 79%);
  margin: 0 .5px .5px;
  padding: 5px 0 0 9px;
  height: 74px;
  text-align: left;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-2 .level2-body .level {
  display: inline-block;
  text-align: left;
  margin-top: -1px;
  line-height: 15px;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-2 .level2-body .level button {
  height: 16px;
  line-height: 15px;
  border: .5px solid #E3C86F;
  font-size: 10px;
  background: #646875;
  padding: 0 2px 0 8px;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-2 .level2-body .level-name {
  display: flex;
  align-items: center;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-2 .level2-body .level-name van-icon {
  margin-left: 2px;
  font-weight: 700;
  transform: scale(.7);
  transform-origin: 0;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-2 .level2-vip {
  position: absolute;
  width: 82px;
  height: 50px;
  left: 4px;
  top: 53px;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-3 {
  margin: 0 auto;
  width: 94%;
  height: 70px;
  border-radius: 8px;
  background: url(https://b.yzcdn.cn/weapp/wsc/userbg.png);
  background-size: 100% 100%;
  color: #886c11;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-3 .name {
  font-size: 12px;
  padding-left: 15px;
  line-height: 40px;
  align-self: flex-start;
  flex: 1;
  text-align: left;
  font-weight: bold;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-3 .name::before {
  content: ' ';
  display: inline-block;
  width: 16px;
  height: 12px;
  margin-right: 5px;
  background-image: url(https://b.yzcdn.cn/member-center/free-icon-3.png);
  background-size: cover;
  vertical-align: -1px;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-3 .name.name--vip::before {
  background-image: url(https://b.yzcdn.cn/member-center/vip-icon-3.png);
}

.member-center__user-info .user-info__level-wrapper .user-info__level-3 .level,
.member-center__user-info .user-info__level-wrapper .user-info__level-3 .become-member {
  display: flex;
  align-items: center;
  height: 22px;
  margin-right: 10px;
  padding: 0 4px 0 10px;
  font-size: 12px;
  border-radius: 12px;
  color: #9e8329;
  box-shadow: 0 2px 8px 0 rgba(185, 157, 51, .2);
  background: rgba(255, 255, 255, .6);
}

.member-center__user-info .user-info__level-wrapper .user-info__level-3 .arrow-custom {
  margin-top: 1px;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-3 .level .van-icon,
.member-center__user-info .user-info__level-wrapper .user-info__level-3 .become-member .van-icon {
  font-size: 12px;
  margin-top: 1px;
  transform: scale(.5);
}

.member-center__user-info .user-info__level-wrapper .user-info__level-3 .level {
  background: rgba(255,255,255,0.60);
  box-shadow: 0 2px 8px 0 rgba(187,157,51,0.20);
  border: 1px solid #C9AE53;
  font-size: 12px;
  color: #9E8329;
  letter-spacing: 0;
}

.member-center__user-info .user-info__level-wrapper .user-info__level-3 .level3-member {
  position: absolute;
  left: 14px;
  bottom: 10px;
  width: 126px;
  height: 19px;
}

.member-center__user-info .user-info-inner-box {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
}