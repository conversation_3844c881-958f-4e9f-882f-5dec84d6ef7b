import WscComponent from 'pages/common/wsc-component/index';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { STATS_DEFAULT_LIST } from '../constants/index';

WscComponent({
  properties: {
    componentConfigs: {
      // UserCenter 组件列表
      type: Array,
      value: [],
    },
    // 大客定制组件列表
    componentConfigsMap: {
      type: Object,
      value: {},
    },
    grade: {
      type: String, // 用户会员等级
      value: '',
    },
    avatar: {
      // 用户头像
      type: String,
      value: '',
    },
    userName: {
      // 用户名
      type: String,
      value: '用户昵称',
    },
    backgroundImage: {
      // 背景图
      type: String,
      value: '',
    },
    backgroundGradient: {
      // 背景是否白色渐变
      type: Number,
      value: 0,
    },
    headPosition: {
      // 头像位置
      type: Number,
      value: 1,
    },
    memberLevel: {
      // 是否显示会员等级
      type: Number,
      value: 1,
    },
    statsNames: {
      type: Object,
      value: {
        balance: '余额',
        points: '积分',
        cards: '卡',
        coupons: '优惠券/码',
      },
    },
    stats: {
      // 余额/卡/券等统计数据
      type: Object,
      value: {},
    },
    orders: {
      // 订单相关数据
      type: Object,
      value: {},
    },
    promptOrderPay: {
      // 催付订单数据
      type: Object,
      value: {},
    },
    promptOrderEvaluation: {
      // 催评订单数据
      type: Object,
      value: {},
    },
    pageStyle: {
      // 插件展示模式  [单行分组 | 魔方]
      type: Number,
      value: 1,
    },
    pluginList: {
      // 需要显示的插件列表
      type: Array,
      value: [],
    },
    pluginValues: {
      // 插件的值
      type: Object,
      value: {},
    },
    baseLinks: {
      type: Object,
      value: {},
    }, // 基本跳转链接
    orderLinks: {
      type: Object,
      value: {},
    }, // 订单相关跳转链接
    pluginLinks: {
      type: Object,
      value: {}, // 插件的跳转链接
    },
    showBindUser: {
      type: Boolean,
      value: false,
    },
    bindTips: String,
    isNotValid: Boolean,
    isSupportSign: {
      type: Boolean,
      value: true,
    },
    isSigned: {
      type: Boolean,
      value: false,
    },
    isMember: {
      type: Boolean,
      value: true,
    },
    isYzEdu: {
      type: Boolean,
      value: false,
    },
    isPureWscSingleStore: {
      type: Boolean,
      value: false,
    },
    reminderInfos: {
      type: Array,
      value: [],
    },
    appointments: {
      type: Array,
      value: [],
    },
    noViewNum: {
      type: Number,
      value: 0,
    },
    isReadWorkset: {
      type: Boolean,
      value: false,
    },
    momentsName: {
      type: String,
      value: '家校圈',
    },
    waitRewardCount: {
      type: Number,
      value: 0,
    },
    reviewToReadNum: {
      type: Number,
      value: 0,
    },
    canShowMomentsFlag: {
      type: Boolean,
      value: false,
    },
    showLevel: {
      type: Boolean,
      value: true,
    },
    growthValue: {
      type: Number,
      value: 0,
    },
    showGrowth: {
      type: Boolean,
      value: false,
    },
    isSupportGrowthValue: {
      type: Boolean,
      value: false,
    },
    levelValue: {
      type: Number,
      value: 0,
    },
    benefitText: {
      type: String,
      value: '',
    },
    levelIconType: {
      type: Number,
      value: 0,
    },
    introduction: {
      type: Object,
      default: {},
    },
    userAgreePrivacy: {
      type: Boolean,
      default: false,
    },
    benefitReminds: {
      type: Array,
      value: [],
    },
    statsList: {
      type: Array,
      value: STATS_DEFAULT_LIST,
    },
    isImmersive: {
      type: Boolean,
      default: false,
    },
    showInfoCompleted: {
      type: Boolean,
      default: false,
    },
  },
  data: {
    templateMap: {
      uc_user_info: 'userInfo',
      uc_stats: 'stats',
      uc_benefit: 'benefit',
      uc_image_ad: 'imageAd',
      uc_order: 'order',
      uc_reminder: 'reminder',
      edu_services: 'eduService',
      uc_widgets: 'widgets',
    },
    isLevelEnable: true,
  },
  methods: {
    handleBindUser() {
      this.triggerEvent('bindUser');
    },
    handleBindGetUserInfo(e) {
      this.triggerEvent('bindGetUserInfo', e.detail);
    },
    handleSign() {
      this.triggerEvent('tapSign');
    },
    handleTapUserInfo(e) {
      const { detail } = e;
      this.triggerEvent('tapUserInfo', detail);
    },
    onPluginItemClicked(e) {
      this.triggerEvent('pluginItemClicked', e.detail);
    },
    onStatsItemClicked(e) {
      this.triggerEvent('statsItemClicked', e.detail);
    },
    onAccountSuccess() {
      this.triggerEvent('onAccountSuccess');
    },
    handleImageClick({ detail }) {
      if (detail.extraData) {
        detail.extra_data = mapKeysCase.toSnakeCase(detail.extraData);
      }
      this.triggerEvent('jumpToLink', detail);
    },
    handleImageChange({ detail }) {
      this.triggerEvent('imageChange', detail.value);
    },
    handleContactBack(e) {
      this.triggerEvent('contactback', e.detail);
    },

    handleRegistryThreshold(ev) {
      this.triggerEvent('registryThreshold', ev.detail);
    },
  },
});
