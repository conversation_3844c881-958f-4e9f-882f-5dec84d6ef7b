import WscComponent from 'shared/common/base/wsc-component/index';
import formatMoney from '@youzan/utils/money/format';
import accDiv from '@youzan/utils/number/accDiv';
import theme from 'shared/common/components/theme-view/theme';
import { checkRetailShop } from '@youzan/utils-shop';

const app = getApp();

const PreferentialMode = {
  // 代金券
  Denominations: 1,
  // 折扣券
  Discount: 2,
  // 兑换券
  Exchange: 3,
};

// 优惠券面额生成规则类型 1: 固定值 2: 指定值 3: 范围随机值 4: 无指定值
const BeneitNumTypeEnum = {
  FIXED: 1,
  ASSIGNED: 2,
  RANDOM: 3,
  NOASSIGNED: 4,
};

WscComponent({
  properties: {
    showGuideBecomingMember: {
      type: Boolean,
      value: false,
      observer: 'onGuideBecomingMemberInfo',
    },
    guideBecomingMemberInfo: {
      type: Object,
      value: {},
      observer: 'onGuideBecomingMemberInfo',
    },
  },

  data: {
    subtitle: '',
    buttonText: '',
    hasCoupon: false,
    hasPresent: false,
    hasPoint: false,
    displayInfo: {},
    buttonBgColor: 'linear-gradient(90deg, #ff6034 0%, #ee0a24 100%)',
    isRetailShop: false,
    shopName: '',
    shopLogo: '',
  },

  attached() {
    theme
      .getThemeColor('general')
      .then((color) => this.setYZData({ buttonBgColor: color }));

    // 初始化时可能无法获取有效的店铺信息，故在此设置 isrRetailShop，而不是直接写在 data.isRetailShop
    this.setYZData({ isRetailShop: checkRetailShop(app.getShopInfoSync()) });
  },

  methods: {
    onGuideBecomingMemberInfo() {
      const { hasGiftBag = false, hideGiftBags = false } =
        this.properties.guideBecomingMemberInfo;

      // 有礼包、在白名单 - 展示老版
      // 有礼包、不在白名单 - 展示新版
      // 无礼包 - 直接展示新版
      let displayInfo = {};
      const subtitle = hasGiftBag
        ? '登录手机号即入会，立得'
        : '登录手机号同步全渠道订单和优惠券';

      let buttonText = '授权登录';
      if (hideGiftBags) {
        buttonText = '一键登录';
        const shopInfo = getApp().getShopInfoSync();

        this.setYZData({
          shopName: shopInfo.shopName || shopInfo.shop_name,
          shopLogo: shopInfo.logo,
        });
      }
      const hasCoupon =
        this.properties.guideBecomingMemberInfo.couponList &&
        Array.isArray(this.properties.guideBecomingMemberInfo.couponList) &&
        this.properties.guideBecomingMemberInfo.couponList.length;
      if (hasCoupon) {
        const [coupon = {}] =
          this.properties.guideBecomingMemberInfo.couponList;
        const {
          value,
          title,
          preferentialCopywriting,
          preferentialMode,
          voucherValueGenerateType,
        } = coupon;
        let total = 0;
        this.properties.guideBecomingMemberInfo.couponList.forEach(
          (element) => {
            total += element.number;
          }
        );
        displayInfo = {
          value,
          valueUnit: '',
          textValue: '',
          title,
          subInfo: preferentialCopywriting,
          total,
        };
        if (preferentialMode === PreferentialMode.Denominations) {
          displayInfo.value = formatMoney(value);
          displayInfo.valueUnit = '元';
        }
        if (preferentialMode === PreferentialMode.Discount) {
          displayInfo.value = accDiv(value || 0, 10);
          displayInfo.valueUnit = '折';
        }
        if (preferentialMode === PreferentialMode.Exchange) {
          displayInfo.textValue = '兑换券';
          displayInfo.value = '';
          displayInfo.valueUnit = '';
        }
        if (voucherValueGenerateType === BeneitNumTypeEnum.RANDOM) {
          displayInfo.textValue = '随机金额';
          displayInfo.value = '';
          displayInfo.valueUnit = '';
        }
      }

      const hasPresent =
        this.properties.guideBecomingMemberInfo.presentList &&
        Array.isArray(this.properties.guideBecomingMemberInfo.presentList) &&
        this.properties.guideBecomingMemberInfo.presentList.length;
      if (hasPresent) {
        const [present = {}] =
          this.properties.guideBecomingMemberInfo.presentList;
        const { imageUrl, presentName } = present;
        displayInfo = { img: imageUrl, title: presentName };
      }

      const hasPoint = this.properties.guideBecomingMemberInfo.points;
      if (hasPoint) {
        const { points } = this.properties.guideBecomingMemberInfo.points;
        displayInfo = {
          value: points,
        };
      }
      this.setYZData({
        subtitle,
        buttonText,
        hasCoupon,
        hasPresent,
        hasPoint,
        displayInfo,
      });
    },
    onAuth() {
      this.triggerEvent('onauth');
    },
    onClose(ev) {
      // 需要透传授权信息，父级将用于判断授权成功和取消授权
      this.triggerEvent('onclose', ev.detail);
    },
  },
});
