<view>
	<van-popup
	 show="{{ false }}"
	 round
	 closeable
	 position="bottom"
	 z-index="10001"
	 bind:close="onClose"
	>
		<!-- 白名单店铺/无礼包的店铺，展示新版无礼包的样式（因为微信不允许透出利益诱导，会直接封号，所以特此设计了新版 @Allen） -->
		<view wx:if="{{guideBecomingMemberInfo.hideGiftBags || !guideBecomingMemberInfo.hasGiftBag}}" class="guide-becoming-member">
			<view class="guide-becoming-member_hide-giftbag">
				<image wx:if={{shopLogo}} src={{shopLogo}} class="guide-becoming-member_shop-logo"/>
				<text class="guide-becoming-member_shop-name">{{shopName}}</text>
			</view>
			<view class="guide-becoming-member_hint">登录享会员权益，同步全渠道订单。</view>
		</view>

		<!-- 不在白名单&&配置了礼包的，正常透出礼包内容 -->
		<view wx:else>
			<view class="guide-becoming-member">
				<view class="guide-becoming-member_title">授权手机号</view>
				<view class="guide-becoming-member_subtitle">{{ subtitle }}</view>
				<!-- 优惠券 -->
				<view wx:if="{{hasCoupon}}" class="guide-becoming-member_info">
					<view wx:if="{{displayInfo.total > 1}}" class="guide-becoming-member_coupon-num">
						共{{ displayInfo.total }}张
					</view>
					<view wx:if="{{displayInfo.total > 1}}" class="guide-becoming-member_coupon-extra" />
					<view class="guide-becoming-member_info__left">
						<view wx:if="{{ displayInfo.textValue }}">
							<span class="guide-becoming-member_info__left_text-value">{{
								displayInfo.textValue
								}}</span>
						</view>
						<view wx:if="{{ !displayInfo.textValue }}">
							<span class="guide-becoming-member_info__left_value">{{
								displayInfo.value
								}}</span>
							<span class="guide-becoming-member_info__left_value-unit">{{
								displayInfo.valueUnit
								}}</span>
						</view>
					</view>
					<view class="guide-becoming-member_info__right">
						<view class="guide-becoming-member_info__right_title">
							{{ displayInfo.title }}
						</view>
						<view wx:if="{{displayInfo.subInfo}}" class="guide-becoming-member_info__right_subinfo">
							{{ displayInfo.subInfo }}
						</view>
					</view>
				</view>
				<!-- 赠品 -->
				<view wx:if="{{hasPresent}}" class="guide-becoming-member_info">
					<view class="guide-becoming-member_info__img-left">
						<image mode="aspectFit" style="width: 76px; height: 76px;" src="{{displayInfo.img}}" />
					</view>
					<view class="guide-becoming-member_info__right present">
						<view class="guide-becoming-member_info__right_title">
							{{ displayInfo.title }}
						</view>
					</view>
				</view>
				<!-- 积分 -->
				<view wx:if="{{hasPoint}}" class="guide-becoming-member_info">
					<view class="guide-becoming-member_info__left">
						<view>
							<span class="guide-becoming-member_info__left_value">{{
								displayInfo.value
								}}</span>
						</view>
					</view>
					<view class="guide-becoming-member_info__right">
						<view class="guide-becoming-member_info__right_title">赠送积分</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 授权按钮 -->
		<view class="guide-becoming-member_action">
			<!-- 零售店铺现阶段产品要求此处授权不论后台配置如何都不需要唤起用户名头像授权 -->
			<block wx:if="{{isRetailShop}}">
				<user-authorize
				 class="guide-becoming-member_button"
				 type="info"
				 authTypeList="{{['protocol', 'mobile']}}"
				 bind:next="onClose"
				 style="background-color: {{buttonBgColor}};"
				>
					<view class="guide-becoming-member_button-text">授权登录</view>
				</user-authorize>
			</block>
			<block wx:else>
				<user-authorize
				 class="guide-becoming-member_button"
				 type="info"
				 scene="enter_personal_center"
				 bind:next="onClose"
				 style="background-color: {{buttonBgColor}};"
				>
					<view class="guide-becoming-member_button-text">{{buttonText}}</view>
				</user-authorize>
			</block>
		</view>
	</van-popup>
</view>

