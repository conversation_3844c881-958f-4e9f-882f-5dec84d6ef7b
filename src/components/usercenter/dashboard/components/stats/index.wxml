<block wx:if="{{ statsListInner.length > 1 }}">
  <view class="member-center__stats">
    <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" wx:if="{{ showBalanceInner }}" bindgetuserinfo="handleBindGetUserInfo" bind:next="goto" data-nav="balance" style="flex: 1;">
      <view class="member-center__stats-item">
        <view class="member-center__stats-item__value">{{ userAgreePrivacy ? balance : '-' }}</view>
        <view class="member-center__stats-item__name">{{ statsNames.balance }}</view>
      </view>
    </user-authorize>
    <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" wx:if="{{ showPointsInner }}" bindgetuserinfo="handleBindGetUserInfo" bind:next="goto" data-nav="points" style="flex: 1;">
      <view class="member-center__stats-item">
        <view class="member-center__stats-item__value">{{ userAgreePrivacy ? points : '-' }}</view>
        <view class="member-center__stats-item__name">{{ statsNames.points }}</view>
      </view>
    </user-authorize>
    <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" wx:if="{{ showCardsInner }}" bindgetuserinfo="handleBindGetUserInfo" bind:next="goto" data-nav="cards" style="flex: 1;">
      <view class="member-center__stats-item">
        <view class="member-center__stats-item__value">
          {{ userAgreePrivacy ? cards: '-'}}
          <view class="member-center__stats-item__dot" wx:if="{{ showCardDot }}"></view>
        </view>
        <view class="member-center__stats-item__name">{{  statsNames.cards }}</view>
      </view>
    </user-authorize>
    <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" wx:if="{{ showCouponsInner }}" bindgetuserinfo="handleBindGetUserInfo" bind:next="goto" data-nav="coupons" style="flex: 1;">
      <view class="member-center__stats-item">
        <view class="member-center__stats-item__value">
          {{userAgreePrivacy ? coupons : '-' }}
          <view class="member-center__stats-item__dot" wx:if="{{ showCouponDot }}"></view>
        </view>
        <view class="member-center__stats-item__name">{{  statsNames.coupons}}</view>
      </view>
    </user-authorize>
    <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" wx:if="{{ showChangeInner }}" bindgetuserinfo="handleBindGetUserInfo" bind:next="goto" data-nav="change" style="flex: 1;">
      <view class="member-center__stats-item">
        <view class="member-center__stats-item__value">
          <van-icon name="gold-coin-o" />
        </view>
        <view class="member-center__stats-item__name">零钱</view>
      </view>
    </user-authorize>
    <subscribe-guide show="{{ showSubscribeMask }}" bind:close="toggleSubscribeMask"/>
  </view>
</block>
<block wx:if="{{ statsListInner.length === 1 }}">
  <view class="member-center__single">
    <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" bindgetuserinfo="handleBindGetUserInfo" bind:next="goto" data-nav="{{ showOneKey }}">
      <van-cell
        border="{{ false }}"
        title="{{ statsNames[showOneKey] }}"
        title-class="member-center__cell--title"
        is-link
      >
        <image slot="icon" src="{{ STATS_ICON_MAP[showOneKey] }}" class="icon" mode="aspectFit" />
        <text>{{ showOneValue }}</text>
      </van-cell>
    </user-authorize>
  </view>
</block>