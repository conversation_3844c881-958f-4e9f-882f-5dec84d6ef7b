import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';
import { wscSubscribeMessage } from '@/utils/subscribe-message';
import {
  subscribePointsExpire,
  getUseSubscribePointsExpire,
} from '@/utils/points-expire-subscribe';
import { STATS_DEFAULT_LIST, STATS_ICON_MAP } from '../../constants/index';
import { getCanUsePointNotifyInGray } from './api';

const app = getApp();

WscComponent({
  properties: {
    statsNames: {
      type: Object,
      value: {
        balance: '余额',
        points: '积分',
        cards: '卡',
        coupons: '优惠券/码',
      },
    },
    showBalance: {
      type: Boolean,
      value: true,
    },
    showCouponDot: {
      type: Boolean,
      value: false,
    },
    showCardDot: {
      type: Boolean,
      value: false,
    },
    balance: {
      type: String,
      value: '0.00',
    },
    points: {
      type: Number | String,
      value: 0,
    },
    cards: {
      type: Number,
      value: 0,
    },
    coupons: {
      type: Number,
      value: 0,
    },
    links: {
      type: Object,
      value: {
        balance: '0.00',
        points: 0,
        cards: 0,
        coupons: 0,
      },
    },
    userAgreePrivacy: {
      type: Boolean,
      default: false,
    },
    isNotValid: Boolean,
    statsList: {
      type: Array,
      value: STATS_DEFAULT_LIST,
    },
  },

  observers: {
    'statsList,showBalance': function (statsListNew, showBalanceNew) {
      let statsListInner = statsListNew;
      // 兼容老的余额展示逻辑
      if (statsListNew.includes('balance') && !showBalanceNew) {
        statsListInner = statsListInner.filter((item) => item !== 'balance');
      }
      const showPointsInner = statsListInner.includes('points');
      const showCouponsInner = statsListInner.includes('coupons');
      const showCardsInner = statsListInner.includes('cards');
      const showBalanceInner = statsListInner.includes('balance');
      const showChangeInner = statsListInner.includes('change');
      this.setYZData({
        showPointsInner,
        showCouponsInner,
        showCardsInner,
        showBalanceInner,
        showChangeInner,
        statsListInner,
        showOneKey: statsListNew[0],
        showOneValue: this.data[statsListNew[0]],
      });
    },
  },

  data: {
    STATS_ICON_MAP,
    showSubscribeMask: false,
    hasTapPointsNav: false,
    usePointsExpireScene: false,
    userAuth: false,
    statsListInner: [],
    showOneKey: '', // 只展示一个的时候的标题
    showOneValue: null, // 只展示一个的时候的值
    showPointsInner: false,
    showCouponsInner: false,
    showCardsInner: false,
    showBalanceInner: false,
    showChangeInner: false,
    canUsePointNotify: false,
  },

  attached() {
    this.getScenePointsExpire();
    this.updateUserAuth();
    this.getCanUsePointNotify();
  },

  methods: {
    subscribe(next, key) {
      const params = {
        scene: 'coupon_notice_scene',
        windowType: 'usercenter_stats',
        authorizationType: 'coupon',
        subscribePage: '个人中心',
        subscribeType: '优惠券/码',
        options: {
          next,
          onShowTips: () => this.toggleSubscribeMask(true),
          onCloseTips: () => this.toggleSubscribeMask(false),
          onComplete: next,
        },
      };
      if (key === 'balance') {
        params.scene = 'balance_notice_scene';
        params.authorizationType = 'balance';
        params.subscribeType = '余额';
      } else if (key === 'points') {
        params.scene = 'point_center_and_point_exchange';
        params.authorizationType = 'points';
        params.subscribeType = '积分';
      } else if (key === 'cards') {
        params.scene = 'user_level_center';
        params.authorizationType = key;
        params.subscribeType = '卡';
      }
      wscSubscribeMessage(params);
    },

    getScenePointsExpire() {
      getUseSubscribePointsExpire().then((status) => {
        this.setYZData({
          usePointsExpireScene: status,
        });
      });
    },
    getCanUsePointNotify() {
      getCanUsePointNotifyInGray()
        .then((status) => {
          this.setYZData({
            canUsePointNotify: status,
          });
        })
        .catch(() => { });
    },

    toggleSubscribeMask(showSubscribeMask) {
      this.setYZData({
        showSubscribeMask,
      });
    },

    async updateUserAuth() {
      app.getUserInfo((res) => {
        this.setYZData({
          userAuth: true,
        });
      });
    },

    goto(e) {
      const key = e.currentTarget.dataset.nav;
      const url = this.properties.links[key];
      const { coupons } = this.data;

      if (url) {
        if (key === 'cards') {
          this.triggerEvent('statsItemClicked', key);
        }
        if (
          (key === 'coupons' && !!coupons) ||
          key === 'balance' ||
          key === 'cards'
        ) {
          const next = () => {
            navigate.navigate({ url });
          };
          this.subscribe(next, key);

          return;
        }

        if (key === 'points' && this.data.canUsePointNotify) {
          const next = () => {
            navigate.navigate({ url });
          };
          this.subscribe(next, key);

          return;
        }

        if (
          key === 'points' &&
          this.data.usePointsExpireScene &&
          !this.data.userAuth
        ) {
          // 微信的原生方法只能通过用户点击事件同步唤起，因此需要拦截掉用户授权后的回调事件并更新授权状态
          this.updateUserAuth();
          return;
        }
        if (
          key === 'points' &&
          this.data.usePointsExpireScene &&
          !this.data.hasTapPointsNav
        ) {
          this.setYZData({ hasTapPointsNav: true });
          return subscribePointsExpire({
            onClose() {
              // 不管是onFail、onReject、onSuccess都会触发
              navigate.navigate({ url });
            },
          });
        }

        navigate.navigate({ url });
      } else {
        if (
          (key === 'coupons' && !!coupons) ||
          key === 'balance' ||
          key === 'cards'
        ) {
          const next = () => {
            this.triggerEvent('statsItemClicked', key);
          };
          this.subscribe(next, key);

          return;
        }
        if (key === 'points' && this.data.canUsePointNotify) {
          const next = () => {
            this.triggerEvent('statsItemClicked', key);
          };
          this.subscribe(next, key);

          return;
        }

        this.triggerEvent('statsItemClicked', key);
      }
    },

    handleBindGetUserInfo(e) {
      this.triggerEvent('bindGetUserInfo', e.detail);
    },
  },
});
