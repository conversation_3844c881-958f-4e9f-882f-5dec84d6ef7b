.reminder-container {
  margin: 0 10px 10px;
  padding: 10px;
  background: #fff;
  border-radius: 8px;

  &__title {
    margin-bottom: 10px;
    line-height: 20px;
    font-size: 14px;
    font-weight: 700;
  }

  &__swiper {
    height: 132px !important;
  }

  .van-swipe__indicators {
    bottom: 4px;
  }

  .van-swipe__indicator {
    margin-right: 4px;
    width: 8px;
    height: 2px;
    border-radius: 1px;
  }

  .reminder {
    box-sizing: border-box;
    padding: 10px;
    height: 132px !important;
    color: #999;
    background: #f7f7f7;
    border-radius: 8px;

    &__nocourse {
      width: 100%;
      height: 100px;
      text-align: center;
      padding-top: 30px;
      box-sizing: border-box;

      .nocourse-tips {
        margin-bottom: 10px;
        font-size: 13px;
        color: #999;
      }

      .nocourse-find {
        width: 80px;
        height: 25px;
        padding: 0;
        border: 1px solid #f44;
        font-size: 13px;
        color: #f44;
        line-height: 22px;
        background: transparent;
      }
    }

    &__row {
      display: flex;
    }

    &__col {
      flex: 1 1 auto;

      &--origin {
        flex: 0 0 auto;
      }
    }

    &__header {
      display: flex;
      justify-content: space-between;
      height: 14px;
      line-height: 14px;
      font-size: 10px;
      user-select: none;
    }

    &__more {
      flex: 0 0 auto;

      .van-icon {
        vertical-align: -2px;
      }
    }

    &__type {
      flex: 0 0 auto;
    }

    &--course {
      &__title {
        margin-top: 10px;
        line-height: 17px;
        font-size: 12px;
        color: #333;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      &__time,
      &__address {
        margin-top: 5px;
        line-height: 14px;
        font-size: 10px;
      }

      &__time {
        margin-top: 10px;
      }
  
      &__tag-container {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }
  
      &__tag {
        flex: 0 0 auto;
        padding: 0 4px;
        height: 16px;
        line-height: 16px;
        font-size: 12px;
        color: #fff;
        background: #f44;
        border-radius: 8px;
      }
    }
  }
}

.indicator {
  height: 10px;
  line-height: 0;
  text-align: center;

  &__dot {
    margin-left: 4px;
    display: inline-block;
    width: 8px;
    height: 2px;
    vertical-align: 1px;
    background: #d8d8d8;
    border-radius: 1px;

    &--current {
      background: #f44;
    }
  }
}
