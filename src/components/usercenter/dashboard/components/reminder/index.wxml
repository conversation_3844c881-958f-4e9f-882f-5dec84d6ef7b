<import src="./template.wxml" />

<view class="reminder-container" wx:if="{{ hasReminder }}">
  <view class="reminder-container__title">
    提醒
  </view>

  <!-- 提醒列表 -->
  <swiper class="reminder-container__swiper" bindchange="onCurrentChange">
    <block wx:for="{{ reminderInfos }}" wx:for-item="reminder">
      <swiper-item class="reminder" data-index="{{ index }}" bindtap="onItemTap">
      <form-button business-module="owlMiniProgram">
        <block>
          <view class="reminder__header" bindtap="onHeaderClick(reminder.url)">
            <view class="reminder__type">
              {{ reminder.typeName }}
            </view>
            <view class="reminder__more">
              {{ reminder.moreName }}
              <view class="zan-icon zan-icon-arrow" />
            </view>
          </view>

          <view wx:if="{{ reminder.show > 0 }}" class="reminder__body">
            <template is="{{ reminder.type }}" data="{{ reminder }}" />
          </view>
          <view wx:else class="reminder__nocourse">
            <view class="nocourse-tips">最近没有要上的课</view>
            <van-button
              type="primary"
              round
              custom-class="nocourse-find"
            >
              去找好课
            </van-button>
          </view>
        </block>
      </form-button>
        
      </swiper-item>
    </block>
  </swiper>

  <view class="indicator" wx:if="{{ reminderInfos.length > 1 }}">
    <view
      wx:for="{{ reminderInfos }}"
      class="indicator__dot {{ current === index ? 'indicator__dot--current' : ''}}"
    />
  </view>
</view>