Component({
  properties: {
    reminderInfos: Array,
    reminders: Array,
  },

  data: {
    current: 0,
    hasReminder: false
  },

  lifetimes: {
    attached() {
      console.log(this.data.reminders)
      this.setData({
        hasReminder: !!this.data.reminders.filter((item) => item.status).length
      });
    }
  },

  methods: {

    onCurrentChange({ detail: { current } }) {
      this.setYZData({
        current
      });
    },

    onItemTap(e) {
      const url = this.data.reminderInfos[e.currentTarget.dataset.index].url;
      if (!url) return;

      wx.navigateTo({
        url
      });
    }
  }
});
