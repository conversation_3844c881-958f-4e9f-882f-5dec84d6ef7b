import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';

const app = getApp();

WscComponent({
  properties: {
    benefitReminds: {
      type: Array,
      value: [],
      observer: 'changeBenefitReminds',
    },
  },

  data: {
    memberVoucher: {
      text: '',
      id: '',
    },
  },
  attached() {
    app.logger &&
      app.logger.log({
        et: 'view', // 事件类型
        ei: 'membcp_ucads', // 事件标识
        en: '会员专享券个人中心引导曝光', // 事件名称
      });
  },

  methods: {
    goto() {
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'membcp_ucads_click', // 事件标识
          en: '会员专享券个人中心引导点击', // 事件名称
        });
      app.request({
        method: 'post',
        path: '/wscuser/level/api/cleanPlanRemind.json',
        data: {
          planId: this.data.memberVoucher.id,
        },
      });
      navigate.navigate({ url: '/packages/levelcenter/free/index' });
    },
    changeBenefitReminds(list) {
      if (list?.length) {
        const memberVoucher = list[0];
        this.setYZData({
          memberVoucher,
        });
      }
    },
  },
});
