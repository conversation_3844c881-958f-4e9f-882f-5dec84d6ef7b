@import "./styles/normal.wxss";
@import "./styles/fantasy.wxss";
@import "./home-util.wxss";

.container {
  padding-bottom: 50px;
}

.usercenter-level-tip {
  font-size: 14px;
  color: #9d6200;
}

.member-center__complete-info.immersive .usercenter-level-tip {
  font-size: 12px;
  color: #2F2F34;
}

.member-center__complete-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 40px;
  background: #e8effa;
  font-size: 14px;
  color: #323233;
  line-height: 18px;
  background-color: #fefbf4;
}

.member-center__complete-info.immersive {
  position: absolute;
  margin: 0 16px;
  box-sizing: border-box;
  width: calc(100% - 32px);
  z-index: 12;
  background: rgba(195, 204, 217, 0.4);
  backdrop-filter: blur(8px);
}

.member-center__complete-info .text {
  display: flex;
  align-items: center;
  color: #9a640c;
}

.member-center__complete-info.immersive .text {
  color: #2F2F34;
}

.member-center__complete-info .text .van-icon {
  margin-right: 8px;
  margin-top: 2px;
}

.usercenter {
  margin: auto;
}

/* banner */
.uc-banner {
  position: relative;
  width: 750rpx;
  height: 345rpx;
  color: #fff;
  overflow: hidden;
}

.uc-banner--normal {
  background: #fff;
  color: #333;
}

.uc-banner__filter-blur {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  transform-origin: center center;
}

.uc-banner__logo {
  position: relative;
  display: block;
  margin: 63rpx auto 0;
  width: 161rpx;
  height: 161rpx;
  border: 4rpx solid white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, .15);
}

.uc-banner__title {
  position: relative;
  text-align: center;
  font-size: 14px;
  line-height: 1.5em;
  padding: 15px 29rpx 25px;
  font-weight: bold;
}

.uc-fantasy .uc-banner__title {
  color: #9a875f;
}

/*message*/
.message--contact {
  position: absolute;
  top:0;
  left: 0;
  width:100%;
  height:100%;
  opacity:0;
}

.btn-get-userinfo {
  padding: 16px 15px;
}

.btn-get-userinfo button{
  text-align: left;
  line-height:1;
  width:100%;
  display:flex;
  font-size:14px;
}

/* 等级图标 */

.grade{
  position: absolute;
  top: 50%;
  right: 0;
  height: 20px;
  margin-top: -20px;
  border-radius: 10px;
  background: #ffc100;
  box-shadow: 0 0 4px 0 rgba(255, 193, 0, 0.5);
  transform: scale(0.8);
  transform-origin: 0;
  padding:0 4px;
  line-height: 20px;
  font-weight: 400;
  font-size: 12px;
  color: #fff;
  z-index: 1;
}

.grade-icon{
  width: 16px;
  height: 16px;
  margin-top: 2px;
  padding-right: 3px;
  vertical-align: top;
}

.uc-edu__cert {
  object-fit: scale-down;
}

.uc-edu__cert--vertical{
  width: 292px;
  height: 457px;
  border-radius: 4px;
}

.uc-edu__cert--horizontal{
  width: 290px;
  height: 216px;
  border-radius: 4px;
}

.popup-reward {
  display: flex;
  flex-direction: column;
  padding: 25px 15px;
  height: 346px;
  width: 260px;
  box-sizing: border-box;
  border-radius: 8px;
  background-color: #fff;
  justify-content: space-between;
  align-items: center;
}

.popup-reward__img {
  width: 220px;
  height: 186px;
}

.popup-reward__title {
  font-size: 20px;
  line-height: 28px;
  font-weight: 500;
  color: #323233;
}

.popup-reward .van-button {
  width: 230px;
  height: 44px;
  line-height: 44px;
}

.user-auth-popup-mask {
  background-color: rgba(0, 0, 0, 0.4);
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}
.user-auth-popup-layout {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.user-auth-popup {
  width: 310px;
  box-sizing: border-box;
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-self: center;
  position: relative;
  background-color: #fff;
  border-radius: 14px;
}
.shop-img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  margin-top: 16px;
  border: 1px solid #ebedf0;
}
.shop-name {
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: bold;
  color: #313233;
  margin-top: 16px;
  line-height: 24px;
  text-align: center;
}
.userauth-msg {
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  color: #313233;
  margin-top: 8px;
  line-height: 20px;
  text-align: center;
}
.userauth-btn {
  width: 263px;
  height: 36px;
  line-height: 36px;
  background: #ED0A23;
  border-radius: 18px;
  color: #fff;
  margin-top: 32px;
  text-align: center;
}

.close__threshold {
  display: inline-block;
  width: 48rpx;
  height: 48rpx;
  background: url(https://img01.yzcdn.cn/upload_files/2022/11/18/Ftd1VOajNCaaTeOXgSO8yApC3cgx.png) no-repeat center;
  background-size: cover;
}