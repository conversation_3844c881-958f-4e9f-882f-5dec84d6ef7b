import WscComponent from 'pages/common/wsc-component';

WscComponent({
  externalClasses: ['custom-class'],

  properties: {
    title: {
      type: String,
      value: '',
    },
    subTitle: {
      type: String,
      value: '还能了解最新优惠信息',
    },
    btnText: {
      type: String,
      value: '',
    },
    extraData: {
      type: Object,
      value: {},
    },
    zIndex: {
      type: Number,
      value: 100,
    },
  },

  data: {
    showPop: false,
  },

  methods: {
    handleShowPopup() {
      this.setYZData({
        showPop: true,
      });

      this.triggerEvent('show');
    },

    togglePopup() {
      this.setYZData({
        showPop: false,
      });

      this.triggerEvent('close');
    },
  },
});
