import WscComponent from 'pages/common/wsc-component/index';
import args from '@youzan/weapp-utils/lib/args';
import debounce from '@youzan/weapp-utils/lib/debounce';
import ParseUrl from '@youzan/utils/url/parse';
import { prefixDomain } from 'utils/open-web-view';
import { transformDomain } from 'shared/utils/dmc';
import { getCurrentPage } from 'shared/utils/url';
// import { Hummer } from 'shared/utils/hummer';
import { updateLogSessionOfFromParams } from 'utils/guide';
import { getWebviewLoginTicket } from '@youzan/passport-tee-api/lib/utils/webview';

const app = getApp();

/**
 * 同步登录状态（重新登录）
 *
 * NOTE：由于可能多次触发，需要防抖
 */
const syncLoginState = debounce(
  () => {
    console.log('[web-view] 登录态变更重新登录');
    app.login(); // forceLogin
    app.logger.skynetLog({ message: 'webview_login_sync' });
  },
  1e3,
  true // 保证能触发
);

WscComponent({
  properties: {
    src: {
      type: String,
      observer: 'updateSrc',
    },
  },

  data: {
    completeSrc: '',
  },

  methods: {
    updateSrc() {
      // 注意 == 判断有隐式转换，区别于为 0 的时候
      const version = this.version == null ? 0 : this.version + 1;
      this.version = version;

      const completeSrc = this.data.src;
      // 更新语言设置
      app.LanguageCore && app.LanguageCore.updateLanguage();

      const fillSrc = () => this.fillSrc(completeSrc, version);

      if (completeSrc) {
        app
          .checkSession()
          .then(() => {
            fillSrc();
          })
          .catch(() => {
            this.on('app:token:success', fillSrc);
          });
        return;
      }

      fillSrc();
    },

    fillSrc(completeSrc, version) {
      if (version !== this.version) {
        return;
      }

      if (!completeSrc) {
        this.setData({ completeSrc });
        return;
      }

      const kdtId = app.getKdtId();
      const weappVersion = app.getVersion();
      const accessToken = app.getAccessToken();
      const weappType = 'share';
      const sid = app.getSessionId();

      Promise.all([
        this.processDomain(completeSrc),
        getWebviewLoginTicket(),
      ]).then(([processDomainSrc, ticketInfo]) => {
        const newFromParamsStr = updateLogSessionOfFromParams(processDomainSrc);
        const { ticket, isGray } = ticketInfo;

        const queryOptions = args.getAll(processDomainSrc);
        const znbOptions = {
          login_ticket: ticket,
          weappType,
          weappVersion,
          mpVersion: weappVersion,
          bizEnv: BUILD_TARGET || 'wsc',
          accessToken,
          kdt_id: queryOptions.kdtId || queryOptions.kdt_id || kdtId, // 兼容方案：优先取url里的kdtId，防止在零售多门店下获取错误的kdtId
          isWeapp: 1,
        };

        // 在灰度列表内，只走新逻辑，src不携带sid
        if (!isGray || !ticket) {
          znbOptions.sid = sid;
        }

        const { logger } = app;
        const url = new ParseUrl(completeSrc);
        const isBBS = url.hostname === 'bbs.youzan.com';
        let loggerArgs = '{}';
        // bbs 链接包含双引号时，会触发安全规则，导致无法访问。
        if (logger && !isBBS) {
          const data = logger.getLogParams();
          const contextObj = data.context;
          if (newFromParamsStr) {
            // 解析到最新的from_params进行埋点数据下发 避免logger携带的from_params和h5链接中的不一致
            contextObj.from_params = newFromParamsStr;
            znbOptions.from_params = newFromParamsStr;
          }
          loggerArgs = JSON.stringify({
            user: data.user,
            context: contextObj,
          });
          znbOptions.logger = loggerArgs;
        }

        completeSrc = args.add(processDomainSrc, znbOptions, true);

        this.setData({ completeSrc });
      });
    },

    processDomain(completeSrc) {
      return transformDomain(completeSrc).catch(() => {
        return prefixDomain(completeSrc);
      });
    },

    onLoad(event) {
      const { detail } = event;
      this.triggerEvent('load', detail);

      // 有问题，暂不启用
      // Hummer.setPageGroup('iswebview', 1);
      // Hummer.markRendered('fs');
      // if (app?.$ranta?.callPluginCtx) {
      //   app.$ranta.callPluginCtx('hummer', 'setPageGroup', 'iswebview', 1);
      //   app.$ranta.callPluginCtx('hummer', 'markRendered', 'fs');
      // }

      // 当 web-view 内完成渠道账号绑定后用户信息发生变更（如手机号）后，则刷新原生授权数据，后续可直接使用有赞授权（而非渠道授权）
      const { is_info_changed: isInfoChanged } = args.getAll(detail.src);
      +isInfoChanged && syncLoginState();
    },

    onError(event) {
      this.triggerEvent('error', event.detail);
    },

    onMessage(event) {
      const { detail } = event;
      const messages = Array.isArray(detail.data) ? detail.data : [];

      const page = getCurrentPage();
      if (page.__resolveMessage) {
        page.__resolveMessage.call(page, messages);
      }

      this.triggerEvent('message', detail);

      // 如果有授权变更消息，native 重新登录拉取最新 session;
      const authMsg = messages.find(
        (item) => item && item.type === 'authorize:state-change'
      );

      authMsg && app.syncAuthState();
    },
  },
});
