import WscComponent from 'pages/common/wsc-component';
import { URL_PATTERN_MAP } from './constants';
import { isNewIphone } from 'shared/utils/browser/device-type';
import * as API from './api';

const app = getApp();
const isIphoneX = isNewIphone();

WscComponent({
  name: 'livePop',

  data: {
    pageType: 0,
    liveRoomId: 0,
    logo: '',
    hasLiving: false,
    top: 80,
  },

  lifetimes: {
    attached() {
      const page = getCurPage();
      this.pageType = this.getPageType(page.route); // 获取页面类型 pageType
      if (this.pageType) {
        API.getMpLive({ scene: this.pageType }).then((res) => {
          if (res.roomId) {
            this.setYZData({
              hasLiving: true,
              liveRoomId: res.roomId,
              logo: res.headImageInfo,
            });
          }
          if (!res.headImageInfo) {
            app.getShopInfo().then((data) => {
              this.setYZData({
                logo: data.logo,
              });
            });
          }
        });
      }
    },
  },

  ready() {
    this.setYZData({
      top: this.getBasePosition(),
    });
  },

  methods: {
    onTap() {
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'click_live_pop', // 事件标识
          en: '直播挂件', // 事件名称
          pt: this.pageType === 1 ? 'f' : 'g', // 页面类型
        });
      wx.navigateTo({
        url: `/packages/weapp-live/room/index?id=${this.data.liveRoomId}`,
      });
    },
    getBasePosition() {
      if (isIphoneX) {
        return 128;
      }
      return 104;
    },
    // 获取页面类型
    getPageType(url) {
      let pageType = 0;

      Object.keys(URL_PATTERN_MAP).some((key) => {
        return URL_PATTERN_MAP[key].some((p) => {
          if (p.test(url)) {
            pageType = key;
            return true;
          }
          return false;
        });
      });

      return Number(pageType);
    },
  },
});

// 获取当前页面
function getCurPage() {
  const ps = getCurrentPages();
  return ps[ps.length - 1];
}
