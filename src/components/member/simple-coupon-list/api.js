import { CouponStatusEnum } from 'constants/member-constants-mini';

const app = getApp();

// 获取用户优惠凭证数量
export const getCouponCount = () => {
  return app.request({
    path: '/wscuser/ump/api/getCouponCount',
    data: {
      kdt_id: app.getKdtId(),
      status: CouponStatusEnum.Valid, // 有效的优惠券
    },
  });
};

// 获取用户优惠券列表
export const getCouponList = (data) => {
  return app.request({
    path: '/wscuser/ump/api/getCouponList',
    data: {
      kdt_id: app.getKdtId(),
      status: CouponStatusEnum.Valid,
      withDetail: true,
      ...data,
    },
  });
};
