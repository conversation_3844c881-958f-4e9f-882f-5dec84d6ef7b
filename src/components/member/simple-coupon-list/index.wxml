<view class="coup-list-wrap">
  <view class="coup-list-title">
    可用优惠券
    <view class="coup-count-wrap">
      <van-loading wx:if="{{ initWaiting || (couponTotal === 0 && couponLoading) }}" size="14" class="coup-count" />
      <block wx:else >
        <text class="coup-count">共{{ couponCount }}张</text>
        <van-icon name="replay" bindtap="refreshCouponData" />
      </block>
    </view>
  </view>
  <view wx:if="{{ couponTotal > 0 || couponLoading }}">
    <coupon
      wx:for="{{ couponList }}"
      wx:key="activityId"
      coupon="{{ item }}"
      btnText="券码"
      bind:clickBtn="handleNavigateCouponQrCode"
      bind:clickCoupon="handleCouponClick"
    />
  </view>
  <view wx:elif="{{ initWaiting }}" class="coup-list-empty">加载中</view>
  <view wx:else class="coup-list-empty">暂无可用优惠券</view>
</view>