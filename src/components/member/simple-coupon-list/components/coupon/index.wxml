<view class="coup-wrap" bindtap="handleCouponClick">
  <view class="coup-value-box coup-content-box">
    <view
      class="coup-value {{ isExchange ? 'coup-value-text' : '' }}"
    >
      {{ coupon.value }}<text class="coup-unit">{{ coupon.unit }}</text>
    </view>
    <view class="coup-threshold">{{ coupon.threshold }}</view>
  </view>
  <view class="coup-info-wrap">
    <view class="coup-content coup-content-box">
      <view class="coup-name">
        {{ coupon.name }}
        <text wx:if="{{ coupon.count > 1 }}" class="coup-count"
          >(共{{ coupon.count }}张)</text
        >
      </view>
      <view class="coup-time">{{ coupon.validTime }}</view>
    </view>
    <view class="coup-btn-wrap" wx:if="{{ !!btnText }}">
      <view class="coup-btn" catchtap="handleBtnClick">{{ btnText }}</view>
    </view>
  </view>
</view>