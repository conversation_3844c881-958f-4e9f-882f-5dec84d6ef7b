import { PreferentialModeEnum } from 'constants/member-constants-mini';
import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    coupon: {
      type: Object,
      default: {},
    },
    btnText: {
      type: String,
      default: '',
    },
  },
  data: {
    isExchange: false,
  },
  attached() {
    const { preferentialMode } = this.properties.coupon;
    const isExchange = preferentialMode === PreferentialModeEnum.EXCHANGE;
    this.setYZData({ isExchange });
  },
  methods: {
    handleCouponClick() {
      this.triggerEvent('clickCoupon', { coupon: this.properties.coupon });
    },
    handleBtnClick() {
      this.triggerEvent('clickBtn', { coupon: this.properties.coupon });
    },
  },
});
