$coupColor: #a47339;
$splitColor: #e8d6c1;
$btnBgColor: #fcf5eb;

.coup {
  &-wrap {
    height: 84px;
    background: #fff;
    color: $coupColor;
    display: flex;
    border-radius: 4px;
    padding-bottom: 12px;
  }
  &-content-box {
    display: flex;
    flex-direction: column;
    padding: 16px 4px 16px 10px;
  }
  &-value-box {
    position: relative;
    min-width: 84px;
    padding: 14px 8px;
    justify-content: space-around;
    text-align: center;
    border-right: 1px dashed $splitColor;
    box-shadow: -4px 2px 10px rgba(0, 0, 0, 0.08);
    border-radius: 10px;

    &::before,
    &::after {
      content: '';
      position: absolute;
      width: 16px;
      height: 8px;
      z-index: 100;
    }

    &::before {
      top: 0;
      right: -8px;
      border-radius: 0 0 8px 8px;
      background-color: #f9fbfe;
    }

    &::after {
      bottom: 0;
      right: -8px;
      border-radius: 8px 8px 0 0;
      background-color: #f2f4f7;
    }
  }
  &-value {
    font-size: 30px;
    font-weight: 600;

    &-text {
      font-size: 18px;
      line-height: 30px;
    }
  }
  &-unit,
  &-threshold {
    font-size: 12px;
    line-height: 16px;
  }
  &-info-wrap {
    flex: 1;
    display: flex;
    padding-right: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border-radius: 10px;
  }
  &-content {
    flex: 1;
    justify-content: space-around;
    min-width: 132px;
  }
  &-name {
    font-size: 14px;
    font-weight: 500;
  }
  &-count {
    font-weight: 400;
    font-size: 12px;
    color: $coupColor;
    opacity: 0.8;
  }
  &-time {
    font-size: 12px;
    line-height: 16px;
  }
  &-btn {
    width: 58px;
    height: 20px;
    line-height: 20px;
    padding: 4px 0;
    text-align: center;
    font-size: 14px;
    color: $coupColor;
    background-color: $btnBgColor;
    border-radius: 14px;

    &.van-button--normal {
      padding: 0;
    }
  }
  &-btn-wrap {
    display: flex;
    align-items: center;
  }
}
