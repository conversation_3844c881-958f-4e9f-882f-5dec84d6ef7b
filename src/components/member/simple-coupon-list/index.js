import WscComponent from 'pages/common/wsc-component/index';
import { getPlugins } from '@youzan/ranta-helper-tee';
import { getCouponCount, getCouponList } from './api';
import { CouponType, GroupType } from 'constants/member-constants-mini';

const { dmc } = getPlugins();

const CouponPageSize = 6;

let loadingData = false;

WscComponent({
  properties: {},
  data: {
    initWaiting: true, // 初始化时等1s请求数据，也展示loading
    // 可用优惠券总数(优惠券和三方券)
    couponCount: 0,
    // 优惠券列表分页滚动加载
    couponPage: 0,
    couponTotal: 0, // 优惠券模版总数，和优惠券数量有差异，仅用于滚动加载相关判断
    couponList: [],
    couponLoading: loadingData,
    couponLoadFinished: false,
  },
  attached() {
    setTimeout(() => {
      this.getCouponCount();
      this.getCouponList();
      this.setYZData({ initWaiting: false });
    }, 1000);
  },
  pageLifetimes: {
    show() {
      this.refreshCoupData();
    },
  },
  methods: {
    formatCoupon(coupon) {
      const {
        title,
        voucherIdentities,
        valueCopywriting,
        unitCopywriting,
        thresholdCopywriting,
        couponDetail,
        validTimeCopywriting,
      } = coupon;
      const count = voucherIdentities.length;
      const nameMaxCharNum = count > 1 ? 5 : 8;
      return {
        ...coupon,
        name:
          title?.length > nameMaxCharNum + 1
            ? `${title.slice(0, nameMaxCharNum)}...`
            : title,
        value: valueCopywriting,
        unit: unitCopywriting,
        threshold: thresholdCopywriting,
        verifyCode: couponDetail?.verifyCode,
        validTime: validTimeCopywriting,
        count,
      };
    },
    getCouponCount() {
      getCouponCount().then((res) => {
        const { cardCount = 0, thirdpartyCount = 0 } = res || {};
        this.setYZData({ couponCount: cardCount + thirdpartyCount });
      });
    },
    getCouponList() {
      if (loadingData || this.data.couponLoadFinished) {
        return;
      }
      loadingData = true;
      this.setYZData({
        couponLoading: true,
      });
      const pageNum = this.data.couponPage + 1;
      getCouponList({
        pageNum,
        pageSize: CouponPageSize,
      })
        .then((res) => {
          const { items = [], paginator } = res || {};
          let { couponTotal: total, couponLoadFinished } = this.data;
          if (paginator && !total) {
            total = paginator.totalCount;
          }
          if (pageNum * CouponPageSize >= total) {
            couponLoadFinished = true;
          }
          this.setYZData({
            couponPage: pageNum,
            couponList: this.data.couponList.concat(
              items.map(this.formatCoupon)
            ),
            couponTotal: total,
            couponLoadFinished,
          });
        })
        .finally(() => {
          loadingData = false;
          this.setYZData({ couponLoading: false });
        });
    },
    refreshCoupData() {
      this.getCouponCount();
      this.setYZData({
        couponPage: 0,
        couponTotal: 0,
        couponList: [],
        couponLoadFinished: false,
      });
      this.getCouponList();
    },
    handleNavigateCouponQrCode(event) {
      const { coupon } = event.detail;
      const { verifyCode } = coupon.couponDetail;
      const weappUrl = `/packages/user/coupon/qrcode/index?verifyCode=${verifyCode}`;
      dmc.navigate(weappUrl);
    },
    handleCouponClick(event) {
      const { coupon } = event.detail;
      const { couponId, couponType } =
        coupon.couponDetail.voucherIdentity || {};
      const groupType =
        couponType === CouponType.Code
          ? GroupType.Code
          : couponType === CouponType.Normal
          ? GroupType.Card
          : GroupType.Thirdparty;
      dmc.navigate('CouponDetail', {
        id: couponId,
        from: 'list',
        type: groupType,
        couponType,
      });
    },
  },
});
