import { getHeight } from 'shared/utils/nav-config';

const height = getHeight() || 0;
const STORAGE_KEY = 'homepage:has_close_collect_tip';
const app = getApp();

Component({
  data: {
    isShowTip: false,
    top: height + 7,
    isRetailShop: false,
  },

  attached() {
    const _this = this;

    app.on('feature_close_collect_tip', () => {
      _this.setData({
        isShowTip: false,
      });
    });

    app.getShopInfo().then((res) => {
      _this.setData({
        isRetailShop: res?.shopType?.isRetailShop,
      });
      wx.getStorage({
        key: STORAGE_KEY,
        success(res) {
          _this.setData({
            isShowTip: !res.data,
          });
        },
        fail() {
          _this.setData({
            isShowTip: true,
          });
        },
        complete() {
          setTimeout(() => {
            _this.setData(
              {
                isShowTip: false,
              },
              () => {
                wx.setStorage({
                  key: STORAGE_KEY,
                  data: true,
                });
              }
            );
          }, 5000);
        },
      });
    });
  },
});
