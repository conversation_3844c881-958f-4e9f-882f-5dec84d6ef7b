<template name="theme-topNav">
  <view class="{{ allTheme.topNavFixedNeedDelay ? 'showcase-top-nav-search--fixed' : '' }}">
    <decorate-search wx:if="{{ allTheme.showSearchComponent }}" _opt="{{ allTheme.topNavSearch }}" kdt-id="kdtId" />
  </view>
  <!-- tabs 切换 开启sticky为true 会触发吸顶 暂时加个1px做个延缓 -->
  <view :id="topNavId" class="showcase-top-nav">
    <view 
      class="{{ allTheme.topNavFixed ? 'top-nav__view--fixed' : 'showcase-top-nav--normal'}} showcase-top-nav--{{ allTheme.topNav.type }}" 
      style="{{ 'top:' + allTheme.topNavFixedTop + 'px' }}" 
      hidden="{{ !allTheme.topNav.list.length }}"
    >
    <!-- 因为页面的type设置早于pid设置，pid为空的情况下初始化tabs会导致不能显示，所以增加if -->
      <van-tee-tabs
        wx:if="{{ allTheme.pid }}"
        active="{{ 'tab' + allTheme.topNav.selectedId }}" 
        color="{{ allTheme.themeColor }}" 
        swipe-threshold="{{ allTheme.topNav.count }}" 
        bind:change="handleTabChange" 
        border="{{ false }}"
        class="cap-tabs" 
        pid="{{ allTheme.pid }}"
        type="{{ allTheme.topNav.type == 3 ? 'card' : 'line' }}" 
        line-class="{{ 'showcase-top-nav-line-class' + allTheme.topNav.type }}" 
        scroll-class="{{ 'showcase-top-nav-scroll-class' + allTheme.topNav.type }}" 
        nav-class="{{ 'showcase-top-nav-nav-class' + allTheme.topNav.type }}"
        wrap-class="{{ 'showcase-top-nav-wrap-class' + allTheme.topNav.type }}"
        tab-class="{{ 'showcase-top-nav-tab-class' + allTheme.topNav.type }}"
        tab-style="{{  allTheme.topNav.style }}"
      >
        <van-tee-tab 
          wx:for="{{ allTheme.topNav.list }}" 
          wx:for-item="tab" 
          wx:for-index="index" 
          wx:key="index" 
          name="{{ 'tab' + index }}" 
          pid="{{ allTheme.pid }}" 
          title="{{ tab.title }}" 
          title-style="{{ allTheme.topNav.selectedId == index ? allTheme.topNav.activeStyle : tab.titleStyle }}"
        >
        </van-tee-tab>
      </van-tee-tabs>
    </view>
  </view>
  <view class="theme-feature" wx:if="{{ allTheme.topNav.featureShow }}">
    <showcase-container id="showcase-container" components="{{ allTheme.feature }}" unique-key="{{ allTheme.uniqueKey }}" components-length="{{ allTheme.componentsLength }}" shop-name="{{ CURRENT_GLOBAL_SHOP.shop_name }}" shop-logo="{{ CURRENT_GLOBAL_SHOP.logo }}" hq-kdt-id="{{  CURRENT_GLOBAL_SHOP.chainStoreInfo.rootKdtId }}" extra="{{ allTheme.extra }}" has-search-sticky="{{ allTheme.topNavFixedNeedDelay }}" page-lifetimes="{{ ['onReachBottom', 'onPullDownRefresh'] }}" bind:buy="showcaseHandleGoodsBuy" />
  </view>
</template>