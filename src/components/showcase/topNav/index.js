/* eslint-disable */
import Toast from '@vant/weapp/dist/toast/toast';
import StickyControl from '@youzan/decorate-tee/native/weapp/common/utils/sticky-control';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import theme from 'shared/common/components/theme-view/theme';
import Event from '@youzan/weapp-utils/lib/event';
import FeatureLoadControl from 'shared/components/showcase-options-home/feature-load/index';
import throttle from '@youzan/weapp-utils/lib/throttle';
/* eslint-enable */

const app = getApp();
const TOP_NAV_HEIGHT = 44;
const STICKY_ITEM_NAME = 'TopNav';

export default {
  fetchTopNavThemeDataSuccess(components = []) {
    this.handleTabScrollFn = throttle(this.handleTabScroll, 50);
    Event.on('onPageScroll' + this.getPageKey(), this.handleTabScrollFn, this);

    // 通过type 替换 下标
    const navsConfig = components.find((item) => item.type === 'top_nav') || {};
    const searchConfig = components.find((item) => item.type === 'search') || {
      showSearchComponent: 0,
      positionType: 0,
    };
    const {
      sub_entry: subEntry = [],
      show_method: showMethod,
      slide_setting: slideSetting,
      count,
      color,
    } = navsConfig;
    if (subEntry && subEntry.length !== 0) {
      // 添加样式属性
      const isScroll = +slideSetting === 1;
      const showCount =
        subEntry.length < 4
          ? subEntry.length
          : Math.min(subEntry.length, count);
      const tabsConfig = {
        scroll: isScroll,
        list: subEntry.map((item, index) => {
          const { alias, title } = item;
          return {
            id: index,
            alias,
            title,
            titleStyle:
              +showMethod === 3
                ? `background-color:#fff;color:${color};margin-top: 8px;border-radius: 4px;`
                : `color:${color}`,
          };
        }),
        count: isScroll ? count : subEntry.length,
        color,
        activeStyle: '',
        type: showMethod,
        selectedId: 0,
        featureShow: false,
        height: TOP_NAV_HEIGHT,
        style: isScroll
          ? `flex: 0 0 ${
              100 / (showCount + (showCount < subEntry.length ? 0.5 : 0))
            }%`
          : '',
      };
      const showSearchComponent = +searchConfig.showSearchComponent === 1;
      const topNavFixedNeedDelay =
        showSearchComponent && +searchConfig.positionType === 1;
      if (topNavFixedNeedDelay) {
        searchConfig.zIndex = 121;
      }

      this.setYZData({
        'theme.topNav': tabsConfig,
        'theme.topNavLoading': true,
        'theme.topNavSearch': searchConfig,
        'theme.showSearchComponent': showSearchComponent,
        'theme.topNavFixed': +searchConfig.show_search_component === 0,
        'theme.topNavFixedTop': 0,
        'theme.topNavZIndex': 120,
        'theme.topNavFixedNeedDelay': topNavFixedNeedDelay,
        'theme.featureComponentsAllLoaded': false,
        'theme.feature': [],
        'theme.pid': `pid${Math.ceil(Math.random() * 1000)}`,
      });

      this.getThemeColor(showMethod);

      StickyControl.setControlSub(this.handelStickyTop, this, STICKY_ITEM_NAME);
      StickyControl.setCheckItem(STICKY_ITEM_NAME, 110);

      StickyControl.setWillStickyComponent({
        name: STICKY_ITEM_NAME,
        type: 'fixed',
        positionTop: 0,
        elementHeight: TOP_NAV_HEIGHT,
        zIndex: this.zIndex || 110,
      });
      this.fetchFeatureDetail(0);
    }
  },

  getThemeColor(showMethod) {
    theme.getThemeColor('general').then((color) => {
      this.setYZData({
        'theme.themeColor': color,
        'theme.topNav.activeStyle':
          +showMethod === 3
            ? `background-color:${color};color:#fff;margin-top: 8px;border-radius: 4px;`
            : `color:${color}`,
      });
    });
  },

  /**
   * 根据`alias`获取微页面配置信息
   * @param {String} alias
   */
  fetchFeatureDetail(index) {
    const { topNav, topNavFixedNeedDelay } = this.data.theme;
    const { alias } = topNav.list[+index];
    this.setYZData({ 'theme.topNavLoading': true }, { immediate: true });
    // node接口 请求微页面配置信息
    return app
      .request({
        path: '/wscdeco/feature-detail.json',
        data: {
          alias,
          check_chainstore: true,
          stage: 16,
          check_multi_store: 1,
          close_chainstore_webview_limit: true,
          check_old_home: 1,
          // async_components: 'goods,ump_limitdiscount',
          // adaptor_components: adaptorComponents.join(','),
        },
      })
      .then((res) => {
        if (res?.videoNumberId) {
          this.getVideoNumberInfo(res.videoNumberId);
        }
        let { components = [] } = res;
        // 处理 config
        if (components[0] && components[0].type === 'config') {
          components = components.slice(1);
        }

        // 处理 联系客服
        const contactUsIndex = Array.isArray(components)
          ? components.findIndex((component) => component.type === 'contact_us')
          : -1;
        if (contactUsIndex > -1) {
          const [contactUsComponent] = components.splice(contactUsIndex, 1);
          components.unshift(contactUsComponent);
        }

        FeatureLoadControl.clearShowcaseComponents(this.uniqueKey);
        this.uniqueKey = alias + makeRandomString(8);
        FeatureLoadControl.setShowcaseComponents(
          components,
          true,
          topNavFixedNeedDelay,
          this.uniqueKey
        );

        this.setYZData(
          {
            'theme.topNavLoading': false,
            'theme.componentsLength': components.length,
            'theme.uniqueKey': this.uniqueKey,
            'theme.topNav.selectedId': index,
            'theme.topNav.featureShow': true,
          },
          {
            immediate: true,
            cb: () => {
              this.scrollToTabScrollTop();
              this._handleZanTabScroll();
            },
          }
        );
      })
      .catch((res) => {
        Toast(res.msg || '微页面详情获取失败～');
      });
  },
  // 获取频号直播信息
  getVideoNumberInfo(finderUserName) {
    wx.getChannelsLiveInfo &&
      wx.getChannelsLiveInfo({
        finderUserName,
        success: (res) => {
          wx.setStorageSync('channelsLiveInfo', JSON.stringify(res));
        },
        fail: (error) => {
          console.log('获取视频号直播信息失败', error);
        },
      });
  },
  /**
   * 滚动操作
   * @param selectedId
   * @param index
   */
  scrollToTabScrollTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    });
  },

  /**
   * 吸顶订阅执行
   * @param top
   * @private
   */
  _handelZanTabScrollTop({ type, stickyTop = 0 }) {
    if (type === 'stickyTop') {
      this.setYZData(
        {
          'theme.topNavFixedTop': stickyTop,
        },
        { immediate: true }
      );
    }
  },

  handleTabScroll(event) {
    this.createSelectorQuery()
      .select(`.showcase-top-nav`)
      .boundingClientRect((rect) => {
        if (!rect) return;
        // // 判断页面滚动方向
        const isToBottom =
          event.scrollTop === this.currentPageY
            ? this.lastDirection === 'bottom'
            : event.scrollTop > this.currentPageY;
        this.lastDirection = isToBottom ? 'bottom' : 'up';
        this.currentPageY = event.scrollTop;

        const itemHeight = rect.height;
        if (itemHeight && !this.originHeight) {
          this.originHeight = itemHeight;
        }
        const hasTop = StickyControl.getHasTop();

        const isStartFixed =
          rect.top <
          (!isToBottom && this.data.theme.topNavFixed
            ? hasTop - this.originHeight - 1
            : hasTop);

        if (isStartFixed !== this.data.theme.topNavFixed) {
          StickyControl.setItem(STICKY_ITEM_NAME, itemHeight, isStartFixed);
          const { positionTop = 0 } =
            StickyControl.getItem(STICKY_ITEM_NAME) || {};
          this.isStartFixed = isStartFixed;
          this.stickyTop = positionTop;
          this.setYZData(
            {
              'theme.topNavFixedTop': positionTop,
              'theme.topNavFixed': isStartFixed,
            },
            { immediate: true }
          );
        }
      })
      .exec();
  },

  /**
   * 初始化订阅、监听
   * @private
   */
  _handleZanTabScroll() {
    StickyControl.resetData();
    StickyControl.setControlSub(this.handelStickyTop, this, STICKY_ITEM_NAME);
    StickyControl.setCheckItem(STICKY_ITEM_NAME, 110);
    StickyControl.setWillStickyComponent({
      name: STICKY_ITEM_NAME,
      type: 'fixed',
      positionTop: 0,
      elementHeight: TOP_NAV_HEIGHT,
      zIndex: this.zIndex || 110,
    });
  },

  handelStickyTop({ type, stickyTop = 0 }) {
    if (type === 'stickyTop') {
      this.setYZData(
        {
          'theme.topNavFixedTop': stickyTop,
        },
        { immediate: true }
      );
    }
  },

  handleTabChange(event) {
    const { index } = event.detail.payload;
    this.fetchFeatureDetail(index);
  },
};
