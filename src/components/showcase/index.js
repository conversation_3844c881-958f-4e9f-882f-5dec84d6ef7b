import Event from '@youzan/weapp-utils/lib/event';
import { featureSkuUtilsPromise } from 'shared/utils/async-base-sku';
import FeatureLoadControl from 'shared/components/showcase-options-home/feature-load/index';
import extendCreator from 'utils/extend-creator';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import navigate from '@/helpers/navigate';
import args from '@youzan/weapp-utils/lib/args';
import config from './config';
import getUniquePageKey from 'shared/utils/unique';
import TeeEvent from '@youzan/tee-event';

const extend = extendCreator({
  life: ['onPageScroll', 'onPullDownRefresh', 'onReachBottom'],
  exclude: [],
});

const app = getApp();

// 底部导航中可能展示为微页面的页面路径
const featureTabPaths = [
  'packages/home/<USER>/index',
  'packages/home/<USER>/one',
  'packages/home/<USER>/two',
  'packages/home/<USER>/three',
];

export default extend({}, {
  showShowcaseComponents(
    components,
    type = 0,
    isRefresh,
    isFromCache = false,
    templatePageNeedRelaunch = false,
    isNotLazyLoad = false,
    otherParams = {},
    preLoadFirstScreenData = false
  ) {
    const { scenes = '', ...restOtherParams } = otherParams;
    otherParams = restOtherParams;
    // 确定微杂志的展示类型
    let typeName = config.themes[type] || '';
    if (
      ['default', 'takeAway', 'topNav', 'ribecaCustom'].indexOf(typeName) < 0
    ) {
      typeName = 'feature';
    }

    switch (typeName) {
      case 'default':
      case 'takeAway':
      case 'ribecaCustom': {
        const query = {
          fromRoute: encodeURIComponent(this.route),
        };
        if (this.data.id) {
          query.id = this.data.id;
        } else if (this.data.alias) {
          query.alias = this.data.alias;
        }
        const pages = getCurrentPages() || [];
        const pageOptions = {
          url: args.add('/packages/showcase-template/index', query),
        };
        if (pages.length > 1 && !templatePageNeedRelaunch) {
          navigate.switchTab(pageOptions, 'redirect');
        } else {
          wx.reLaunch(pageOptions);
        }

        break;
      }
      case 'feature':
      case 'topNav': {
        const isTopNav = typeName === 'topNav';

        // 微页面配置
        let config;
        if (
          Array.isArray(components) &&
          components.length >= 1 &&
          components[0].type === 'config'
        ) {
          [config] = components.splice(0, 1);
          this._setFeaturePageConfigData(config);
        }

        // 处理 联系客服
        const contactUsIndex = Array.isArray(components)
          ? components.findIndex((component) => component.type === 'contact_us')
          : -1;
        if (contactUsIndex > -1) {
          const [contactUsComponent] = components.splice(contactUsIndex, 1);
          components.unshift(contactUsComponent);
          this._setImData({ config, contactUsComponent, otherParams });
        } else {
          this._setImData({ config, otherParams });
        }

        const { chainStoreInfo = {} } = app.getShopInfoSync();
        const { isMultiOnlineShop } = chainStoreInfo;
        const _kdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId();

        const theme = {
          type: typeName,
          'theme.feature': [],
          'theme.extra.kdtId': otherParams.kdtId || app.getKdtId(), // 优先用外层传入的 kdtId
          'theme.extra.appId': app.getAppId(),
          'theme.extra.lsKdtId': _kdtId,
          'theme.extra.hqKdtId': app.getExtKdtId(),
          'theme.extra.buyerId': app.getBuyerId() || '',
          'theme.extra.offlineId': app.getOfflineId(),
        };

        if (isNotLazyLoad) theme['theme.feature'] = components;

        // 顶部导航、微页面、首页、签到、个人中心 都会进到这里； 商品详情页 内部 单独处理
        // 顶部导航 内部自己处理
        if (!isNotLazyLoad && !isTopNav) {
          if (this.uniqueKey) {
            FeatureLoadControl.clearFirstScreenComponents(this.uniqueKey);
            FeatureLoadControl.clearShowcaseComponents(this.uniqueKey);
          }
          this.uniqueKey =
            this.__wxExparserNodeId__ + makeRandomString(8) + `&${scenes}&`;
          // 通过 theme.componentsLength 来分页
          theme['theme.componentsLength'] = components.length;
          // theme.uniqueKey 作为唯一标识别
          theme['theme.uniqueKey'] = this.uniqueKey;
          const hasElevator = components.find(
            (i) => i.type === 'elevator_navigation'
          );
          preLoadFirstScreenData &&
            !hasElevator &&
            FeatureLoadControl.setFirstScreenComponents(
              components.slice(0, 8),
              this.uniqueKey
            );
          FeatureLoadControl.setShowcaseComponents(
            components,
            false,
            false,
            this.uniqueKey
          );
        }

        this.setYZData(theme, {
          immediate: true,
          cb: () => {
            if (isTopNav) {
              this.fetchTopNavThemeDataSuccess(
                components,
                isRefresh,
                isFromCache
              );
            }
          },
        });

        break;
      }
      default:
        break;
    }
  },

  showcaseHandleGoodsBuy(e) {
    const {
      alias,
      activityTypes = null,
      timingSaleHideBuyBtnInSku,
      id = '',
      goodsId = '',
    } = e.detail;
    const options = { activityTypes, timingSaleHideBuyBtnInSku };
    const extra = { goodsIdForBirthday: id || goodsId };

    featureSkuUtilsPromise()
      .then(({ getSkuData }) =>
        getSkuData(alias, Object.assign({}, options, extra))
      )
      .then((skuData) => {
        this.setYZData({
          'theme.featureSkuData': skuData,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  },

  // 联系客服的回调
  onContactBack: navigate.contactBack,

  _setFeaturePageConfigData(config = {}) {
    app.getShopConfigData().then((res) => {
      const result = {
        'theme.extra.hideGift': +res.gift_plug_status === 0,
      };

      result.pageBgColor =
        (+config.is_global_setting === 1
          ? res.background_color
          : config.color) || '';

      this.setYZData({
        ...result,
      });
    });
  },

  // 设置 im 信息
  _setImData({ config, contactUsComponent, otherParams }) {
    const path = this.route;
    const offlineId = app.getOfflineId();
    const contactPromiseArray = [app.getImData()];

    if (offlineId > 0) {
      contactPromiseArray.push(app.getShopInfo());
    }

    // 获取im 信息
    Promise.all(contactPromiseArray)
      .then(([imData, shopInfo]) => {
        const sourceParam = {
          kdt_id: otherParams.kdtId || app.getKdtId(), // kdt_id
          endpoint: imData.endpoint, // 渠道，wap端写死'h5',小程序'mmp'
          source: 'none',
        };

        if (shopInfo) {
          // 多网点
          sourceParam.site_id = offlineId;
          sourceParam.site_name = (shopInfo.store && shopInfo.store.name) || '';
        }

        // 从上面的代码看起来，config可能为 undefined
        if (
          (path === 'packages/home/<USER>/index' ||
            path === 'pages/home/<USER>/index') &&
          config
        ) {
          const logo = contactUsComponent && contactUsComponent.teamLogo;

          sourceParam.source = 'page'; // 写死'page'
          sourceParam.detail = JSON.stringify({
            title: config.title, // 微页面title
            desc: config.description, // 微页面描述信息
            alias: this.data.alias, // 微页面alias
            imgs: logo ? [logo] : [], // 没有 logo 就传个空数组
          });
        }

        // sourceParam 是 im 来源
        // inTabPage 用于调整底部客服组件的位置
        this.setYZData({
          'theme.extra.sourceParam': JSON.stringify(sourceParam),
          'theme.extra.businessId': imData.businessId || '',
          'theme.extra.inTabPage': featureTabPaths.indexOf(path) > -1,
        });
      })
      .catch((e) => {
        console.log(e);
      });
  },

  onPageScroll(e) {
    Event.trigger('onPageScroll' + this.getPageKey(), e);
    TeeEvent.trigger('onPageScroll' + this.getPageKey(), e);
    // Event.trigger('feature:PageScrollChange', e);
    this.trigger('sdk:featurePageScroll' + this.getPageKey(), e);
  },

  onPullDownRefresh(e) {
    Event.trigger('onPullDownRefresh' + this.getPageKey(), e);
  },

  onReachBottom(e) {
    Event.trigger('onReachBottom' + this.getPageKey(), e);
    Event.trigger('float-share:show' + this.getPageKey());
  },

  getPageKey() {
    if (this.__pageEventUniqueKey) {
      return this.__pageEventUniqueKey;
    }
    this.__pageEventUniqueKey = getUniquePageKey();
    return this.__pageEventUniqueKey;
  },

  handleSkuClose() {
    this.setYZData({
      'theme.featureSkuData.showGoodsSku': false,
    });
  },

  onUnload() {
    // 移除挂载的 this.uniqueKey
    // 有两种情况 正常的 this.uniqueKey 存在的 ，微页面、签到、个人中心 正常清除
    // 顶部导航 自己内部自己设置 uniqueKey 赋值到 this 上
    FeatureLoadControl.clearShowcaseComponents(this.uniqueKey || '');
  },
});
