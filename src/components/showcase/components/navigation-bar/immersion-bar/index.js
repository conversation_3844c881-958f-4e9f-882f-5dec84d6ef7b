import { VanxComponent } from 'shared/common/base/wsc-component/index';
import getSystemInfo from 'shared/utils/browser/system-info';
import { mapState } from '@youzan/vanx';
import { getPaddingTop } from 'shared/utils/nav-config';

const { windowWidth } = getSystemInfo();

// 标准
const NAVIGATIONBAR_TYPE_STANDARD = 'standard';

const GLOBAL = 'global';

// 高斯模糊
const GAUSSIAN_BLUR = 'gaussianblur';

const METHOD_SET_OBSERVE_COMPONENT_DATA = 'observeData';
const WHITE = '#fff';
const BLACK = 'black';

const CENTER = 'center';
const LEFT = 'left';

VanxComponent({
  // behaviors: [componentBehavior, loggerBehavior],

  properties: {
    // 字体颜色
    styleColorCustomFontColor: {
      type: String,
      value: BLACK,
    },
    // 背景颜色
    styleColorCustomBackgroundColor: {
      type: String,
      value: WHITE,
    },
    titleSwitch: {
      type: String,
      value: '1', // 标题设置: 0 关 / 1 开
    },
    titleContentType: {
      type: String,
      value: 'text', // 内容形式: text 文字 / image 图片
    },
    titleImageUrl: {
      type: String,
      value: '', // 标题图片
    },
    showLangIcon: Boolean,
    titlePosition: {
      type: String,
      value: CENTER, // 位置选择:  center 居中 / left 居左
    },
    searchSwitch: {
      type: String,
      value: '0', // 0 关 / 1 开
    },
    searchPosition: {
      type: String,
      value: CENTER, // 位置选择:  center 居中 / left 居左
    },
    shortcutSwitch: {
      type: String,
      value: '0', // 快捷入口设置: 0 关 / 1 开
    },
    navigationbarConfigType: {
      type: String,
      value: GLOBAL, // 样式设置: global 跟随全局 / custom 自定义
    },
    shortcutList: {
      type: Array,
      value: [], //  快捷入口列表: { name: 键, title: 名称, checked: 开启, iconUrl: 快捷入口图标 }[]
    },
    defaultShortcutList: {
      type: Array,
      value: [], //  返回列表: { name: 键, title: 名称, checked: 开启, iconUrl: 快捷入口图标 }[]
    },
    styleColorCustomType: {
      type: String,
      value: GAUSSIAN_BLUR, // 颜色类型: gaussianblur 高斯模糊 / purecolor 纯色
    },
    title: {
      type: String,
      value: '',
    },
    backIconStyle: {
      type: String,
      value: '',
    },
    navigationbarType: {
      // 当前是否沉浸式
      type: String,
      value: '', // 风格选择: standard 标准 / immersion 沉浸式
      observer: METHOD_SET_OBSERVE_COMPONENT_DATA,
    },
    styleColorType: {
      type: String,
      value: GLOBAL,
    },
    paddingTop: {
      type: Number,
      value: 0,
      observer: METHOD_SET_OBSERVE_COMPONENT_DATA,
    },
    showMenuMain: Boolean,
    // 沉浸式上划展示模式
    immersionUpShow: {
      type: Boolean,
      value: false,
      observer: METHOD_SET_OBSERVE_COMPONENT_DATA,
    },
    height: {
      type: Number,
      value: 0,
      observer: METHOD_SET_OBSERVE_COMPONENT_DATA,
    },
    switchPageOptions: Object,
    isReservation: Boolean, // 是否订座微页面
    showChainStoreSwitch: {
      value: true,
    },
  },

  data: {
    bgStyle: '',
    isImmersion: false, // 是否选择沉浸式展示
    colorType: BLACK, // 文字的颜色，black 或者 white
    centerWidth: 0,
    centerStyle: '',
    iconImageType: '',
    navPaddingTop: 0,
    // navHeight
    navH: 44,
    // caclComplete
    done: false,
    // mergedShortcutList
    mSL: [],
    // showCustomShortcut
    showCS: false,
  },

  mapData: {
    ...mapState('/', ['shop']),
  },

  methods: {
    onIconClick(e) {
      this.triggerEvent('immersionTap', { key: e.detail.key });
    },

    [METHOD_SET_OBSERVE_COMPONENT_DATA]() {
      const {
        defaultShortcutList,
        immersionUpShow,
        navigationbarConfigType,
        navigationbarType,
        searchPosition,
        searchSwitch,
        shortcutList,
        shortcutSwitch,
        styleColorCustomBackgroundColor,
        styleColorCustomFontColor,
        styleColorCustomType,
        styleColorType,
        titlePosition,
        titleSwitch,
        showLangIcon,
      } = this.data;

      const isImmersion = navigationbarType === 'immersion';
      const isPureColorAndNotImmersion =
        styleColorCustomType === 'purecolor' && !isImmersion;
      const isStandardGlobal =
        navigationbarType === NAVIGATIONBAR_TYPE_STANDARD &&
        styleColorType === GLOBAL;
      const isImmersionUpShow =
        immersionUpShow &&
        navigationbarType !== NAVIGATIONBAR_TYPE_STANDARD &&
        styleColorCustomType !== GAUSSIAN_BLUR;

      let bgStyle = 'transition: all 0.1s ease-out;';
      if (
        isPureColorAndNotImmersion ||
        isStandardGlobal ||
        (isImmersionUpShow && titleSwitch === '1')
      ) {
        bgStyle = `background-color: ${styleColorCustomBackgroundColor};${bgStyle}`;
      }

      // 设置胶囊颜色为文字颜色
      wx.setNavigationBarColor({
        frontColor: styleColorCustomFontColor === BLACK ? '#000' : WHITE,
        backgroundColor: WHITE, // 背景颜色必须要跟上，不然会报错
      });

      // 获取胶囊信息 wx.getMenuButtonBoundingClientRect()
      const menuButton = wx.getMenuButtonBoundingClientRect();
      const menuButtonWidth = windowWidth - menuButton.left;

      // 获取左部分有没有元素信息
      const hasLeft =
        (titleSwitch === '1' && titlePosition === LEFT) ||
        (searchSwitch === '1' && searchPosition === LEFT) ||
        shortcutSwitch === '1';
      let leftWidth = hasLeft ? 120 + 6 : menuButtonWidth;

      const enableCustomBar = shortcutSwitch === '1';
      // 如果存在自定义快捷入口，则拼接上默认的快捷入口（暂时只有返回）
      const mSL = enableCustomBar
        ? [...defaultShortcutList, ...shortcutList]
        : defaultShortcutList;
      const showCS = showLangIcon
        ? immersionUpShow
          ? !(searchSwitch === '1' && searchPosition === 'left')
          : mSL.length > 1
        : navigationbarConfigType !== GLOBAL &&
          (enableCustomBar || defaultShortcutList.length > 0);

      // 如果是沉浸式下的快捷入口，则宽度 + 8
      if (
        isImmersion &&
        !immersionUpShow &&
        styleColorCustomType !== GAUSSIAN_BLUR
      ) {
        leftWidth += 8;
      }

      // 获取可以显示标题的区域
      const centerWidth =
        windowWidth - Math.max(menuButtonWidth, leftWidth) * 2 - 20;
      const centerStyle = `width:${centerWidth}px;left:${leftWidth + 10}px;`;
      // 图标展示的类型
      let iconImageType = NAVIGATIONBAR_TYPE_STANDARD;
      if (navigationbarType === NAVIGATIONBAR_TYPE_STANDARD) {
        iconImageType = NAVIGATIONBAR_TYPE_STANDARD;
      } else if (immersionUpShow) {
        iconImageType = 'after_slide';
      } else {
        iconImageType = 'before_slide';
      }

      const navPaddingTop = getPaddingTop(this.data.height);

      this.setYZData({
        colorType: styleColorCustomFontColor,
        navPaddingTop,
        isImmersion,
        bgStyle,
        centerStyle,
        iconImageType,
        done: true,
        mSL,
        showCS,
      });
    },

    handleShare() {
      this.triggerEvent('share');
    },
  },
});
