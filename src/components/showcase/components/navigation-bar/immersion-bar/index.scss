.cap-nb-warp {
  position: relative;
}

.cap-nb {
  /* 若浏览器支持 backdrop-filter，则透明度为 50%，并启用毛玻璃 */
  @supports (-webkit-backdrop-filter: blur(10px)) or
    (backdrop-filter: blur(10px)) {
    &.gaussianblur {
      background: rgba(255, 255, 255, 0.1);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      color: #eee;
    }
  }

  /* 若浏览器器不支持，则使用 MaterialDesign 的风格 */
  @supports not (
    (-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))
  ) {
    &.gaussianblur {
      background: #fff;
      color: #333;
      box-shadow: 0px 10px 30px rgba(255, 255, 255, 0.18),
        0px 6px 10px rgba(255, 255, 255, 0.22);
    }
  }

  &.immersion {
    background: transparent;
  }

  .warp {
    .confont {
      font-family: 'navigation-iconfont' !important;
      font-size: 36rpx;
      display: block;
      speak: none;
      font-style: normal;
      font-weight: normal;
      font-variant: normal;
      text-transform: none;
      line-height: 1;
      /* Better Font Rendering =========== */
      -webkit-font-smoothing: antialiased;
    }

    display: flex;
    padding: 0 6px 0 6px;
    height: 44px;
    align-items: center;

    .left {
      width: 120px;
    }

    .center {
      min-width: 70px;
      height: 44px;
      position: absolute;
      top: 0;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
    }

    .right {
      width: 88px;
    }

    .home::before {
      content: '\f102';
    }
  }
}
