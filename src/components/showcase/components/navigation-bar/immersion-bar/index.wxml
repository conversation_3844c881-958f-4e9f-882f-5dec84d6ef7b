<view class="cap-nb-warp">
  <view wx:if="{{ done }}"
    class="'cap-nb' {{  styleColorCustomType === 'gaussianblur' && styleColorType !== 'global' ? 'gaussianblur' : '' }} {{ isImmersion ? 'immersion' : '' }}"
    style="padding-top: {{ navPaddingTop }}px;{{bgStyle }}; height: {{ navH }}px;box-sizing: content-box;">
    <view class="warp" style="position: relative;">
      <reservation-share wx:if="{{ isReservation }}" bind:share="handleShare" />
      <block wx:if="{{ !isReservation }}">
        <view class="left">
          <view class="confont home"
            wx:if="{{showMenuMain && (titleSwitch !== '1' && searchSwitch !==  '1' && shortcutSwitch !==  '1') && immersionUpShow}}">
          </view>

          <!-- 左侧搜索 -->
          <cap-navigation-search bind:searchClick="onIconClick"
            wx:elif="{{searchSwitch === '1' && searchPosition === 'left'}}"
            show-immersion="{{ isImmersion  && styleColorCustomType !== 'gaussianblur'}}"></cap-navigation-search>
         
          <!-- 左侧文本 -->
          <cap-title wx:if="{{ titleSwitch === '1' && titleContentType === 'text' && titlePosition === 'left' && (immersionUpShow || !isImmersion) }}"
            title="{{ title }}" title-color="{{ colorType }}" />

          <!-- 左侧图片 -->
          <cap-navigation-image
            wx:elif="{{ titleSwitch === '1' && titleContentType === 'image' && titlePosition === 'left' && (immersionUpShow || !isImmersion) }}"
            title-image-url="{{ titleImageUrl }}" real-height="{{ navH }}" />
          <!-- 左侧快捷入口 -->
          <cap-navigation-icon
            wx:elif="{{ showCS }}"
            shortcut-list="{{ mSL }}"
            back-icon-style="{{ backIconStyle }}"
            show-immersion="{{ isImmersion && styleColorCustomType !== 'gaussianblur' }}"
            image-type="{{ iconImageType }}" bind:iconClick="onIconClick" />
        </view>

        <view class="center" style="{{ centerStyle }}">
          <!-- 中间文本 -->
          <cap-title
            wx:if="{{ titleSwitch === '1' && titleContentType === 'text' && titlePosition === 'center' && (immersionUpShow || !isImmersion) }}"
            title="{{ title }}" title-color="{{ colorType }}" text-align="center" style="width: 100%" />

          <!-- 中间搜索 -->
          <cap-navigation-search bind:searchClick="onIconClick"
            wx:if="{{searchSwitch === '1' && searchPosition === 'center'}}"
            show-immersion="{{ isImmersion && !immersionUpShow && styleColorCustomType !== 'gaussianblur'}}"
            text-align="center" style="width: 100%"></cap-navigation-search>

          <!-- 中间图片 -->
          <cap-navigation-image
            wx:if="{{ titleSwitch === '1' && titleContentType === 'image' && titlePosition === 'center' && (immersionUpShow || !isImmersion) }}"
            title-image-url="{{ titleImageUrl }}" real-height="{{ navH }}" text-align="center"
            style="width: 100%" />
        </view>
      </block>
    </view>
    <chain-store-switch
      wx:if="{{ showChainStoreSwitch !== false && !isReservation && shop.chainStoreInfo.isMultiOnlineShop && isImmersion }}"
      info="{{ shop.chainStoreInfo }}"
      page-options="{{ switchPageOptions }}"
      is-immersion="{{ isImmersion }}"
      immersion-up-show="{{ immersionUpShow }}"
      show-in-navigation="{{ searchSwitch !== '1' && shortcutSwitch !== '1' }}"
    />
  </view>
</view>

