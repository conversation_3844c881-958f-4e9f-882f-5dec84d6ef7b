@font-face {
  font-family: "yzicon";
  src: url("iconfont/yzicon_6e5df2f173c387d65c4569cff617197b.svg#yzicon") format("svg"),
    url("iconfont/yzicon_6e5df2f173c387d65c4569cff617197b.ttf") format("truetype"),
    url("iconfont/yzicon_6e5df2f173c387d65c4569cff617197b.eot?#iefix") format("embedded-opentype"),
    url("iconfont/yzicon_6e5df2f173c387d65c4569cff617197b.woff") format("woff"),
    url("iconfont/yzicon_6e5df2f173c387d65c4569cff617197b.woff2") format("woff2");
}

[class^="nav-icon-"],
[class*="nav-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "yzicon" !important;
  display: block;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
}

.nav-icon-back::before {
  content: "\f101";
}
.nav-icon-home::before {
  content: "\f102";
}
.nav-icon-menu::before {
  content: "\f103";
}
.nav-icon-search::before {
  content: "\f104";
}
