Component({
  // behaviors: [component<PERSON>ehavior, loggerBehavior],

  properties: {
    shortcutList: {
      type: Array,
      value: [], //  快捷入口列表: { name: 键, title: 名称, checked: 开启, iconUrl: 快捷入口图标 }[]
    },
    // 展示沉浸式UI
    showImmersion: {
      type: Boolean,
      value: false,
    },
    backIconStyle: {
      type: String,
      value: '',
    },
    // 图标展示的类型，标准，沉浸式，或者沉浸式上划
    imageType: {
      type: String,
      value: '', // standard  before_slide after_slide
      observer: 'iconUrlList',
    },
  },

  data: {
    iconUrlList: [],
  },

  methods: {
    iconUrlList() {
      const res = this.data.shortcutList.map((i) => {
        return {
          ...i,
          style: `background-image: url(${i.images[this.data.imageType]})`,
        };
      });

      this.setData({
        iconUrlList: res,
      });
    },
    onTap(e) {
      const { key } = e.currentTarget.dataset;
      this.triggerEvent('iconClick', { key });
    },
  },
});
