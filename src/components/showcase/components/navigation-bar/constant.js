import { supportRouteMap, SPECIAL_ROUTE_ARR } from 'shared/utils/nav-config';

export const sysKeyMap = {
  usercenter: 'onUserCenterTap',
  shopcar: 'onShopCarTap',
  search: 'onSearchTap',
  marketingPage: 'onMarketingPageTap',
  language: 'languageTap',
  home: 'onHomeTap',
  back: 'onBackTap',
  allGoods: 'onAllGoodsTap',
};

export const getSupport = (route, arr) => {
  const v = supportRouteMap[route];
  if (v !== undefined) {
    return (arr || []).map((_) => +_).indexOf(v) > -1;
  }
  return false;
};

export const getSpecialSupport = (route) => {
  return SPECIAL_ROUTE_ARR.includes(route);
};

export const initialValue = {
  navColor: '#fff',
  textColor: '#000',
  sysPath: [],
  customPath: [],
  supportPages: ['0', '2', '5'],
  hotKeys: [],
};
