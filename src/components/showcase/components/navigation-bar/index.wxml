<!-- 外层占位 -->
<view
	wx:if="{{ canUseNav && openCustomNav }}"
	style="height: {{ (useImmersion && cd.navigationbar_type !== 'standard') || originImmersion ? 0 : height }}px;"
	class="nb"
>
	<!-- 滚动时查看位置 -->
	<view class="nb-top-line"></view>
	<cap-sticky
		sticky-height="{{ height }}"
		z-index="{{ 10001 }}"
		offset-top="{{ 0 }}"
		sticky-wrap-class="{{ needPointClickNav && !isReservation ? 'nb__sticky-wrap-point' : 'nb__sticky-wrap' }} fixed"
		bind:scroll="handleScroll"
	>
		<view wx:if="{{ useImmersion }}">
			<cap-navigation-bar
				style-color-custom-type="{{ cd.style_color_custom_type }}"
				search-switch="{{ cd.search_switch }}"
				search-position="{{ cd.search_position }}"
				navigationbar-type="{{ cd.navigationbar_type }}"
				style-color-custom-font-color="{{ cd.style_color_custom_font_color }}"
				style-color-custom-background-color="{{ cd.style_color_custom_background_color }}"
				title="{{ titleText }}"
				showMenuMain="{{ showImmersionMenuMain }}"
				title-switch="{{ cd.title_switch }}"
				title-content-type="{{ cd.title_content_type }}"
				title-image-url="{{ cd.title_image_url }}"
				show-lang-icon="{{ cd.show_lang_icon }}"
				shortcut-switch="{{ cd.shortcut_switch }}"
				shortcut-list="{{ cd.shortcut_list }}"
        show-chain-store-switch="{{ cd.show_chain_store_switch }}"
				defaultShortcutList="{{ cd.defaultShortcutList }}"
				title-position="{{ cd.title_position }}"
				navigationbar-config-type="{{ cd.navigationbar_config_type }}"
				style-color-type="{{ cd.style_color_type }}"
				padding-top="{{ paddingTop }}"
				height="{{ height }}"
				immersion-up-show="{{ immersionUpShow }}"
				bind:immersionTap="onImmersionTap"
				back-icon-style="{{ cd.back_icon_style }}"
				switch-page-options="{{ switchPageOptions }}"
				is-reservation="{{ isReservation }}"
				bind:share="handleShare"
			/>
		</view>
		<!-- 内层postion：fixed -->
		<view
			wx:if="{{ !useImmersion && !awaitingNavConfig }}"
			class="nb__main"
			style="height: {{ originImmersion && opacityVal === 0 ? 0 : height }}px;"
		>
			<!-- 导航背景 -->
			<view
				class="nb__bg"
				style="height: {{ originImmersion && opacityVal === 0 ? 0 : height }}px;background: {{ (currentSupport || specialSupport) && configData.navColor }};opacity: {{ originImmersion ? opacityVal : 1 }}"
			/>
			<!-- 非主页情况，只会有返回按钮和主页按钮 -->
			<view
				wx:if="{{ showMenuBack || showMenuMain || showLangIcon && !isChannels }}"
				class="nb__icon {{ showMenuBack && !isChannels && 'nb__icon__split' }}"
				style="{{ customIconStyle }} {{showLangIcon&&showMenuMain&& !isChannels? customIconWidth: '' }}"
			>
				<view wx:if="{{ showLangIcon && !isChannels }}" class="nb__icon-button" bind:tap="languageTap">
					<view class="nb__langicon" style="background-image: url({{ langUrl }})"></view>
				</view>
				<view wx:elif="{{ showMenuBack }}" class="nb__icon-button" bind:tap="onBackTap">
					<view class="nb__iconfont nb__i-back"></view>
				</view>
				<view wx:if="{{ showMenuMain && !isChannels }}" class="nb__icon-button" bind:tap="onMenuTap">
					<view
						class="nb__iconfont nb__i-{{ (configData.customPath.length + configData.sysPath.length > 1) && currentSupport ? 'menu' : 'home' }}"
					></view>
				</view>
			</view>
			<!-- 文字标题 -->
			<view
				class="nb__title-text"
				style="color: {{ (currentSupport || specialSupport) && configData.textColor }}; opacity: {{ originImmersion ? opacityVal : 1 }} ;padding-top: {{ paddingTop }}px; {{ textStyle }}"
			>
				<view class="nb__title-text_t" style="width: {{ textWidth }}px; font-size: {{ fontSizeSetting }}px;">
					{{ titleText }}
				</view>
			</view>
		</view>
	</cap-sticky>
	<nav-bar-open-panel
		is-menu-open="{{ isMenuOpen }}"
		current-support="{{ currentSupport }}"
		contact-extra="{{ contactExtra }}"
		open-menu-style="{{ openMenuStyle }}"
		open-menu-after-style="{{ openMenuAfterStyle }}"
		config-data="{{ configData }}"
		bind:onMenuTap="onMenuTap"
		bind:onMenuCustomPathTap="onMenuCustomPathTap"
		bind:onMenuSysPathTap="onMenuSysPathTap"
	/>
</view>
