@font-face {
  font-family: 'navigation-iconfont';
  src: url('https://b.yzcdn.cn/project/2020/top-nav-v2/yzicon_6e5df2f173c387d65c4569cff617197b.ttf')
      format('truetype'),
    url('https://b.yzcdn.cn/project/2020/top-nav-v2/yzicon_6e5df2f173c387d65c4569cff617197b.woff')
      format('woff'),
    url('https://b.yzcdn.cn/project/2020/top-nav-v2/yzicon_6e5df2f173c387d65c4569cff617197b.woff2')
      format('woff2');
}

.nb-top-line {
  height: 5px;
  position: absolute;
  width: 100%;
}

.nb__langicon{
  width: 65%;
  height: 65%;
  background-size: contain;
  background-repeat: no-repeat;
}

.nb {
  width: 100%;
  height: 64px;
  position: relative;

  &__iconfont {
    font-family: 'navigation-iconfont' !important;
    font-size: 36rpx;
    display: block;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
  }
  &__i-search::before {
    content: '\f104';
  }
  &__i-home::before {
    content: '\f102';
  }
  &__i-menu::before {
    content: '\f103';
  }
  &__i-back::before {
    content: '\f101';
  }
  &__sticky-wrap {
    width: 100%;
    top: 0;
    z-index: 10001;
  }
  // &__sticky-wrap 与 __sticky-wrap-point 除 pointer-events 的样式需要同步修改
  &__sticky-wrap-point {
    width: 100%;
    top: 0;
    z-index: 10001;
    pointer-events: none;
  }
  &__main {
    height: 64px;

    /** z-index 顺序 修改请查看 docs/z-index.md **/
  }

  &__bg {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background: #fff;
  }

  &__icon {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-around;
    border: 1px solid rgba(229, 229, 229, 0.6);
    box-sizing: border-box;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.6);
    z-index: 120;
    &__split::after {
      content: '';
      width: 1px;
      height: 65%;
      position: absolute;
      left: 50%;
      top: 18%;
      background: rgba(229, 229, 229, 0.6);
    }
    &-button {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-search {
      background: rgba(247, 248, 250, 0.8);
      border: none !important;
    }
    &_home {
      display: flex;
      align-items: center;
      &-image {
        width: 100%;
        height: 100%;
      }
    }
  }
  &__search-text {
    font-size: 12px;
    margin-left: 5px;
    color: #969799;
    overflow: hidden;
  }

  &__title-text {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    box-sizing: border-box;
    font-size: 36rpx;
    font-weight: 500;
    padding-top: 20px;
    position: relative;
    &_t {
      width: 161px;
      font-size: 16px;
      display: inline-block;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  &__placeholder {
    position: absolute;
  }
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}