import componentBehavior from 'shared/components/showcase/behaviors/component-behavior';
import loggerBehavior from 'shared/components/showcase/behaviors/logger-behavior';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import navigate from 'shared/utils/navigate';
import { checkIsChannels, checkFromShowcase } from 'shared/utils/channel-mini';
import { getSystemInfoSync } from '@youzan/tee-api';
import {
  MenuButtonBoundingClientRect,
  getHeight,
  getSpecialRule,
  HOME_ROUTE,
  GOODS_DETAIL_TOUTE,
  KQ_GOODS_DETAIL,
  getPaddingTop,
  needUpdateRect,
} from 'shared/utils/nav-config';
// import StickyControl from 'shared/utils/sticky-control';
import {
  sysKeyMap,
  initialValue,
  getSupport,
  getSpecialSupport,
} from './constant';
import { jumpToLink } from 'shared/components/showcase-options-home/jump-to-link';
import { getConfigData } from './api';
import get from '@youzan/weapp-utils/lib/get';
import Event from '@youzan/weapp-utils/lib/event';
import getUniquePageKey from 'shared/utils/unique';
import throttle from '@youzan/weapp-utils/lib/throttle';
import { getPlugins } from '@youzan/ranta-helper-tee';
import tabNavigate from '@youzan/wsc-tab-bar-utils/lib/weapp/navigate';
import TeeEvent from '@youzan/tee-event';

const { dmc } = getPlugins();
const app = getApp();

let isInit = false;
const pu = 'https://img01.yzcdn.cn/upload_files/2024/10/30/';
const LangUrl = {
  white: `${pu}in-white.png`,
  blank: `${pu}in-black.png`,
};

const TOP_NAV_CONFIG = '__TOP_NAV_CONFIG__';
const USER_CENTER = 'Usercenter';
const CHAIN_STORE_KDT_ID_UPDATE_EVENT = 'app:chainstore:kdtid:update';
const ON_NAV_OPACITY_CHANGE = 'onNavOpacityChange';
const ON_SET_IMMERSIVE_READY = 'onSetImmersiveReady';
const GLOBAL = 'global';

Component({
  behaviors: [componentBehavior, loggerBehavior],

  properties: {
    /** 小程序页面标题 */
    titleText: String,
    /** 是否开启自定义导航 */
    openCustomNav: Boolean,
    /** 是否等待页面导航配置(主要是微页面) */
    awaitingNavConfig: Boolean,
    // 微页面导航
    navigationbarConfigData: {
      type: Object,
      value: {},
      observer: 'initImmersionData',
    },
    switchPageOptions: Object,
  },

  data: {
    /** 兼容低版本小程序 */
    canUseNav: true,
    /** 获取默认字体大小，用于图标和导航文字 */
    fontSizeSetting: 16,
    /** 导航高度 */
    height: getHeight(),
    /** 导航上边距 */
    paddingTop: getPaddingTop(),
    /** 文字宽度，也是不影响两边按钮的最大宽度 */
    textWidth: 161,
    /** 连锁是否展示主页按钮 */
    showImmersionMenuMain: false,
    /** 是否展示主页按钮 */
    showMenuMain: false,
    /** 是否展示返回按钮 */
    showMenuBack: false,
    /** 自定义按钮区域icon样式 */
    customIconStyle: '',
    isMenuOpen: false,
    configData: initialValue,
    // 是否是视频号
    isChannels: checkIsChannels(),
    useImmersion: false, // 是否使用沉浸式导航
    immersionUpShow: false, // 沉浸式上划展示模式
    cd: {}, // showComponentData 改名为 cd 缩小模板层体积,
    needPointClickNav: false, // 是否可以穿透点击导航
    contactExtra: {
      sourceParam: '', // 客服组件sessionnFrom
      businessId: '',
    },
    specialSupport: false,
    originImmersion: false, // 是否使用普通导航下的沉浸式效果
    opacityVal: 0, // 导航栏透明度
    isReservation: false,
  },

  attached() {
    this.getConfig();
    this.getImInfo();
    this.initReservationShare();
    this.initOriginImmersion();
    this.BUC = this.updeteConfig.bind(this);
    app.on(CHAIN_STORE_KDT_ID_UPDATE_EVENT, this.BUC);
    // 吸顶逻辑
    // StickyControl.setStickyControlCheckItem(
    //   CUSTOM_NAVIGATION,
    //   9999999999,
    //   true
    // );

    app.isSwitchTab().then((isTabPage) => {
      this.isTabPage = isTabPage;
    });
  },

  ready() {
    this.pageScrollFn = throttle(
      this.onPageScrollIntersectionObserver.bind(this),
      50
    );
  },

  detached() {
    Event.off('onPageScroll' + this.getPageKey(), this.pageScrollFn);
    if (this.opacityFn) {
      TeeEvent.off(ON_NAV_OPACITY_CHANGE, this.opacityFn);
      TeeEvent.off(ON_SET_IMMERSIVE_READY, this.triggerFunc);
    }
    app.off(CHAIN_STORE_KDT_ID_UPDATE_EVENT, this.BUC);
  },
  pageLifetimes: {
    show() {
      if (this._cacheFunc) {
        this._cacheFunc();
        this._cacheFunc = null;
      }
    },
  },

  methods: {
    initReservationShare() {
      const pages = getCurrentPages();
      const { type } = pages[pages.length - 1].options || {};

      this.setData({
        isReservation: type === 'tableBooking',
      });
    },

    handleBackIcon() {
      if (!this.isFirstPage()) {
        const after_slide = `${pu}left-white.png`;
        return [
          {
            key: 'back',
            show: 1,
            title: '返回',
            images: {
              after_slide,
              before_slide: `${pu}left-gray.png`,
              standard: after_slide,
            },
          },
        ];
      }
      return [];
    },

    isHome() {
      const { path } = app.getWxPage();
      return path === HOME_ROUTE;
    },

    async langUtils() {
      if (this.isHome()) {
        return app.LanguageCore && app.LanguageCore.isOpen('home');
      }
    },

    async handleLangIcon(defaultShortcutList, isOpenShort) {
      let showLangIcon = false;
      defaultShortcutList = defaultShortcutList.filter(
        (i) => i.key !== 'language'
      );

      const url = await this.langUtils();
      if (url) {
        showLangIcon = true;
        const afterSlide = isOpenShort ? LangUrl.blank : LangUrl.white;
        defaultShortcutList.unshift({
          key: 'language',
          show: 1,
          title: '语言',
          images: {
            after_slide: afterSlide,
            before_slide: LangUrl.white,
            standard: afterSlide,
          },
        });
      }
      return { showLangIcon, defaultShortcutList };
    },

    async getShowComponentData() {
      const { configData, navigationbarConfigData } = this.data;
      let shortcutList = get(navigationbarConfigData, 'shortcut_list', []);
      const shortcutSwitch = get(
        navigationbarConfigData,
        'shortcut_switch',
        '0'
      );
      const configType = get(
        navigationbarConfigData,
        'navigationbar_config_type',
        ''
      );

      // eslint-disable-next-line prefer-const
      let { defaultShortcutList, showLangIcon } = await this.handleLangIcon(
        this.handleBackIcon(),
        shortcutSwitch === '1' && configType !== GLOBAL
      );

      shortcutList = shortcutList
        .filter((i) => i.show)
        .slice(0, 3 - defaultShortcutList.length);

      if (navigationbarConfigData.style_color_type === GLOBAL && configData) {
        navigationbarConfigData.style_color_custom_background_color =
          configData.navColor;
        navigationbarConfigData.style_color_custom_font_color =
          configData.textColor && configData.textColor.includes('#fff')
            ? 'white'
            : 'black';
      }

      return {
        ...navigationbarConfigData,
        shortcut_list: shortcutList,
        show_lang_icon: showLangIcon,
        defaultShortcutList,
      };
    },

    init() {
      // 在获取MenuButtonBoundingClientRect值之前判定是否需要更新rect
      needUpdateRect();
      const {
        top,
        height: menuButtonHeight,
        right,
        width,
        canUseNav,
      } = MenuButtonBoundingClientRect;
      // 预防一下api取值错误，再来一遍更新缓存和高度
      let height = getHeight(false);
      const systemInfo = getSystemInfoSync();
      let { statusBarHeight } = systemInfo;
      const { windowWidth, fontSizeSetting } = systemInfo;
      const paddingLeft = windowWidth - right;
      let textWidth = windowWidth - width * 2 - paddingLeft * 4;
      // 确定是否展示主页按钮和返回按钮,页面栈不存在上一页且不是主页的情况
      const pages = getCurrentPages();
      const { configData } = this.data;
      let currPageRoute = pages[pages.length - 1].route;
      let showMenuBack = pages.length > 1;
      // 只有一个堆栈且是商详时需要展示回退。
      const isGoodsDetail =
        currPageRoute === GOODS_DETAIL_TOUTE ||
        currPageRoute === KQ_GOODS_DETAIL;
      if (!showMenuBack && isGoodsDetail) {
        showMenuBack = true;
      }
      const isHomePage = HOME_ROUTE === currPageRoute;

      if (isHomePage) {
        showMenuBack = false;
      }
      if (
        currPageRoute.indexOf('pages/goods/') === 0 ||
        currPageRoute.indexOf('packages/goods/') === 0
      ) {
        currPageRoute = 'pages/goods/';
      }
      const currentSupport = getSupport(
        currPageRoute,
        this.data.configData.supportPages
      );
      const specialSupport = getSpecialSupport(currPageRoute);
      const showFoldMenu =
        configData.sysPath.length + configData.customPath.length > 1;
      const extra = getSpecialRule(currPageRoute);
      const { type, open } = extra || {};
      type === 'back' && (showMenuBack = open);
      let showMenuMain =
        // 如果有展开菜单的话，菜单展示
        (showFoldMenu && currentSupport) ||
        // 是否有配置，有的话遵循配置，无配置的话首页不展示，其他页面展示
        (type === 'home' ? open : !isHomePage);
      let showImmersionMenuMain = false;

      // 页面嵌在tab页的场景下，会员页和收费会员不展示顶部菜单
      if (
        this.isTabPage &&
        ['level-center-free', 'level-center-plus'].includes(
          app.globalData.attachedPageId
        )
      ) {
        showMenuMain = false;
      }

      // 连锁特有
      if (app.forceShowHome()) {
        showImmersionMenuMain = true;
        showMenuMain = true;
      }

      // 计算一下图标块的样式
      let customIconStyle = `width: ${
        showMenuBack ? width : menuButtonHeight
      }px;height: ${menuButtonHeight}px;left: ${paddingLeft}px;top: ${top}px;border-radius: ${height}px;`;

      if (checkIsChannels()) {
        // 非橱窗场景,状态栏高度强制设为0(靠API给的statusBarHeight不太稳定)
        if (!checkFromShowcase()) {
          statusBarHeight = 0;
        }
        customIconStyle = `width: 32px;height: 32px;left: 7px;top: ${
          statusBarHeight + 3
        }px;border:none;`;
        height = statusBarHeight + 40;
        textWidth = 173;
      }

      const openMenuStyle = `top: ${height + 13}px;left: ${paddingLeft}px`;
      const openMenuAfterStyle = `left: ${
        (showMenuBack ? (width / 4) * 3 : menuButtonHeight / 2) - 7
      }px`;

      this.initImmersionData();
      // todo 抛出
      this.triggerEvent('set-height', height);

      this.setData({
        showMenuMain,
        customIconWidth: `width:${width}px`,
        showImmersionMenuMain,
        height,
        paddingTop: getPaddingTop(),
        fontSizeSetting,
        canUseNav,
        textWidth,
        showMenuBack,
        customIconStyle,
        openMenuStyle,
        openMenuAfterStyle,
        currentSupport,
        specialSupport,
        isSkyline: this.renderer === 'skyline',
      });
    },

    // 是否一致页面
    isFirstPage() {
      const pages = getCurrentPages();
      // 空白页面不算
      const isBlankPage =
        pages.length === 2 &&
        pages[0] &&
        pages[0].route &&
        pages[0].route.includes('pages/common/blank-page/index');
      const isHome = this.isHome();
      return isHome || isBlankPage || pages.length <= 1;
    },

    async initImmersionData() {
      // 非商详页场景的滚动监听效果
      /* eslint-disable camelcase */
      const { origin_immersion, navigationbar_config_type } =
        this.data.navigationbarConfigData || {};
      const scrollEvent = 'onPageScroll' + this.getPageKey();
      const eventParams = [scrollEvent, this.pageScrollFn];
      if (origin_immersion) {
        TeeEvent.off(...eventParams);
        TeeEvent.on(...eventParams);
        this.setData({
          originImmersion: true,
        });
        return;
      }
      /* eslint-enable camelcase */
      // 这里要加微页面的判断
      // if (!this.isFirstPage()) {
      //   this.setData({
      //     useImmersion: false,
      //   });
      //   return;
      // }

      let useImmersion = false;

      if (Object.keys(this.data.navigationbarConfigData || {}).length) {
        // eslint-disable-next-line camelcase
        if (navigationbar_config_type === GLOBAL) {
          const showLangIcon = await this.langUtils();
          this.setData({
            showLangIcon,
            langUrl: LangUrl.blank,
          });
          return;
        }
        useImmersion = true;

        const cd = await this.getShowComponentData();

        Event.off(...eventParams);
        TeeEvent.off(...eventParams);
        Event.on(...eventParams);
        TeeEvent.on(...eventParams);
        // 代码放在下面会被覆盖

        this.setData({
          cd,
          useImmersion,
          needPointClickNav: this.getNeedPointClickNav(),
        });

        // 此方法在真机上不生效，监听pagescroll
        // wx.nextTick(() => {
        //   this.createIntersectionObserver({
        //     thresholds: [1],
        //   })
        //     .relativeToViewport()
        //     .observe('.navigation-bar-top-line', (res) => {
        //       const { intersectionRatio } = res;
        // this.setData({
        //   immersionUpShow: !(intersectionRatio > 0),
        // });
        //     });
        // });
      }
    },

    getNeedPointClickNav() {
      const { useImmersion, immersionUpShow, navigationbarConfigData } =
        this.data;
      return (
        useImmersion &&
        !immersionUpShow &&
        navigationbarConfigData.style_color_custom_type !== 'gaussianblur' &&
        navigationbarConfigData.navigationbar_type === 'immersion'
      );
    },

    getPageKey() {
      if (!this.__pageEUK) {
        this.__pageEUK = getUniquePageKey();
      }
      return this.__pageEUK;
    },

    onPageScrollIntersectionObserver(e) {
      if (this.data.originImmersion) {
        let opacityVal = 0;
        if (e.scrollTop > 10) {
          opacityVal = 1;
        }
        this.setData({
          opacityVal,
        });
      } else {
        let immersionUpShow = false;
        if (e.scrollTop > 10) {
          immersionUpShow = true;
        }

        if (immersionUpShow !== this.data.immersionUpShow) {
          this.setData({
            immersionUpShow,
          });
        }
        wx.nextTick(() => {
          this.setData({
            needPointClickNav: this.getNeedPointClickNav(),
          });
        });
      }
    },

    handleNavOpacity(opacityVal) {
      if (this.data.originImmersion) {
        this.setData({
          opacityVal,
        });
      }
    },

    getStoreConfig() {
      return wx
        .getStorage(TOP_NAV_CONFIG)
        .then((localData) => {
          const { time = 0, data = initialValue } = localData || {};
          if (new Date().getTime() - time > 10 * 60 * 1000 || !isInit) {
            return getConfigData();
          }
          return { data, cache: true };
        })
        .catch(() => {
          return getConfigData();
        });
    },
    getConfig() {
      const func = app.isHqEnterShop()
        ? Promise.resolve()
        : app.waitForEnterShop();
      func
        .then(() => {
          if (this.isComponentHide) {
            return new Promise((resolve) => {
              this._cacheFunc = () => {
                resolve(this.getStoreConfig());
              };
            });
          }
          return this.getStoreConfig();
        })
        .then((res) => {
          let data;
          const pages = getCurrentPages();
          const currPageRoute = pages[pages.length - 1].route;
          const extra = getSpecialRule(currPageRoute);
          const { type, open, sysPath = [], customPath = [] } = extra || {};
          const hasMenu = type !== 'menu' || open;
          if (res && res.data && !res.cache) {
            try {
              const comList = JSON.parse(res.data);
              const config = comList.find((_) => _.type === 'shop_top_nav');
              config && (data = mapKeysCase.toCamelCase(config));
            } catch (error) {
              data = initialValue;
            }
            if (hasMenu) {
              data.sysPath.push(...sysPath);
              data.customPath.push(...customPath);
            }
            // 处理数据
            const processPath = (paths, filterShow = false) => {
              if (!hasMenu) return [];
              return paths
                .filter(filterShow ? (p) => p.show === 1 : () => true)
                .map((item, index, arr) => ({
                  ...item,
                  _right:
                    (arr.length > 4 && (index + 1) % 4 === 0) ||
                    index === arr.length - 1
                      ? 0
                      : 24,
                }));
            };

            data.sysPath = processPath(data.sysPath, true);
            data.customPath = processPath(data.customPath);

            wx.setStorage(TOP_NAV_CONFIG, {
              data,
              time: new Date().getTime(),
            });
            isInit = true;
          } else {
            data = res.data;
          }
          return data;
        })
        .catch((e) => {
          console.warn(e);
        })
        .then((res) => {
          const { navigationbarConfigData } = this.data;
          /* eslint-disable camelcase */
          const { special_nav_bg_color, special_nav_color } =
            navigationbarConfigData;
          const config = res || { ...initialValue };
          if (special_nav_bg_color) {
            config.navColor = special_nav_bg_color;
          }
          if (special_nav_color) {
            config.textColor = special_nav_color;
          }
          /* eslint-enable camelcase */
          this.setData({ configData: config }, this.init.bind(this));
        });
    },

    onMenuTap() {
      // 埋点 - 导航按钮点击
      app.logger.log({
        et: 'click',
        ei: 'nav_button_weapp',
        en: '导航按钮点击',
        si: app.getKdtId(),
        params: {
          is_show: Number(this.data.isMenuOpen),
        },
      });
      const {
        configData: { sysPath, customPath },
        currentSupport,
      } = this.data;
      if (currentSupport && sysPath.length + customPath.length > 1) {
        this.onOpenMenuStatusChange();
      } else {
        this.onHomeTap();
      }
    },

    onOpenMenuStatusChange() {
      this.setData({
        isMenuOpen: !this.data.isMenuOpen,
      });
    },

    onImmersionTap(e) {
      this[sysKeyMap[e.detail.key]]();
      this.logNavTapEvent({
        event_key: e.detail.key,
      });
    },

    languageTap() {
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateTo({
        url: '/packages/shop/language-setting/index',
      });
    },

    onMenuSysPathTap(e) {
      const { detail } = e;

      this[sysKeyMap[detail]]();
      this.logNavTapEvent({
        event_key: detail,
      });
    },

    /** 主页按钮 */
    onHomeTap() {
      // 防止风险，按场景放开
      const pages = getCurrentPages();
      const path = pages[pages.length - 1].route;
      const params = [{ url: `/${HOME_ROUTE}` }];
      if (['pages-retail/usercenter/dashboard-v2/index'].includes(path)) {
        params.push('navigate', 'switchTab');
      }
      navigate.switchTab(...params);
    },

    onUserCenterTap() {
      // navigate.navigate({ url: '/packages/usercenter/dashboard/index' });
      dmc.switchTab(USER_CENTER).catch(() => {
        dmc.navigate(USER_CENTER);
      });
    },

    onShopCarTap() {
      const commonPath = '/goods/cart/index';
      tabNavigate({
        path: `/pages${commonPath}`,
        fail() {
          navigate.navigate({
            url: `/packages${commonPath}`,
          });
        },
      });
    },

    onAllGoodsTap() {
      dmc.navigate('AllGoodsList');
    },

    onMarketingPageTap() {
      navigate.navigate({ url: '/packages/ext-marketing-page/index' });
    },

    /** 返回按钮 */
    onBackTap() {
      const pages = getCurrentPages();
      if (pages.length === 1) {
        this.onHomeTap();
      } else {
        wx.navigateBack();
      }
    },

    /** 搜索按钮点击直接进入搜索页 */
    onSearchTap() {
      const searchPageRoute = 'packages/shop/search-page/index';
      navigate.navigate({
        url: `/${searchPageRoute}?keepWords=&isFromList=1`,
      });
    },

    onMenuCustomPathTap(e) {
      const { detail } = e;
      // 换了新的jumplink方法，两个参数都从item中读
      jumpToLink(detail, detail);
      this.logNavTapEvent({
        event_key: 'custom_path',
        custom_title: detail.title,
        custom_url: detail.link_url,
      });
    },

    handleScroll() {
      // StickyControl.setStickyControlItem(
      //   CUSTOM_NAVIGATION,
      //   this.data.height,
      //   true
      // );
    },
    getImInfo() {
      app.getDefaultImData().then((data) => {
        this.setData({
          contactExtra: data,
        });
      });
    },
    updeteConfig() {
      isInit = false;
      if (
        this.isComponentHide ||
        app.getEnterCacheConfig('proxyWxJumpPadding')
      ) {
        this._cacheFunc = () => {
          this.getConfig();
        };
      } else {
        this.getConfig();
      }
    },
    openImmersiveStatus() {
      this.opacityFn = throttle(this.handleNavOpacity, 50);
      TeeEvent.on(ON_NAV_OPACITY_CHANGE, this.opacityFn, this);
      this.triggerFunc = () => {
        TeeEvent.trigger('onSetImmersive', {
          status: true,
          navHeight: this.data.height,
        });
      };
      TeeEvent.on(ON_SET_IMMERSIVE_READY, this.triggerFunc, this);
      this.setData({
        originImmersion: true,
      });
    },
    initOriginImmersion() {
      // 判断页面是否是商详页沉浸式效果
      const pages = getCurrentPages();
      const currPageRoute = pages[pages.length - 1].route;
      const isGoodsDetail =
        currPageRoute === GOODS_DETAIL_TOUTE ||
        currPageRoute === KQ_GOODS_DETAIL;
      if (isGoodsDetail) {
        const STYLE_KEY = 'goods_detail_navigation_bar_style';
        const shopConfig = app.getShopConfigDataSync();
        const isConfigEmpty = Object.keys(shopConfig).length === 0;
        if (isConfigEmpty) {
          // 同步方法拿不到配置就走异步方法
          app.getShopConfigData().then((asyncShopconfig) => {
            if (asyncShopconfig[STYLE_KEY] === '1') {
              this.openImmersiveStatus();
            }
          });
        } else if (shopConfig[STYLE_KEY] === '1') {
          this.openImmersiveStatus();
        }
      }
    },
    logNavTapEvent(params) {
      const pages = getCurrentPages();
      const currPageRoute = pages[pages.length - 1].route;
      app.logger.log({
        et: 'click',
        ei: 'shortcut_item_click',
        en: '快捷按钮点击情况',
        si: app.getKdtId(),
        params: {
          component: 'navigation_bar_weapp',
          page_key: currPageRoute,
          ...params,
        },
      });
    },
    handleShare() {
      this.triggerEvent('share');
    },
  },
});
