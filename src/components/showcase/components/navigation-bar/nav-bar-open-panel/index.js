import { VanxComponent } from 'shared/common/base/wsc-component/index';
import navigate from 'shared/utils/navigate';

VanxComponent({
  properties: {
    // 字体颜色
    isMenuOpen: {
      type: Boolean,
      value: false,
    },
    // 背景颜色
    currentSupport: {
      type: Boolean,
      value: false,
    },
    openMenuStyle: {
      type: String,
      value: '',
    },
    openMenuAfterStyle: {
      type: String,
      value: '',
    },
    configData: {
      type: Object,
      value: {},
    },
    contactExtra: {
      type: Object,
      value: {},
    },
  },

  data: {},

  methods: {
    onMenuTap() {
      this.triggerEvent('onMenuTap');
    },

    onMenuCustomPathTap(e) {
      const {
        currentTarget: {
          dataset: { item },
        },
      } = e;
      this.triggerEvent('onMenuCustomPathTap', item);
    },

    onMenuSysPathTap(e) {
      const {
        currentTarget: {
          dataset: { key },
        },
      } = e;
      this.triggerEvent('onMenuSysPathTap', key);
    },

    // 联系客服的回调
    onContactBack: navigate.contactBack,
  },
});
