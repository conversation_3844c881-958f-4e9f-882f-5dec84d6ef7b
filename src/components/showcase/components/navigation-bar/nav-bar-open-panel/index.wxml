<view class="weapp-top-nav" wx:if="{{ isMenuOpen && currentSupport }}" style="display: {{ isMenuOpen && currentSupport ? 'block' : 'none' }}">
  <view class="weapp-top-nav-bg" bind:tap="onMenuTap" catch:touchmove></view>
  <view class="weapp-top-nav-wrapper" style="{{ openMenuStyle }}">
    <view class="weapp-top-nav-wrapper__after" style="{{ openMenuAfterStyle }}"></view>
    <view class="weapp-top-nav__sys-path">
      <block wx:for="{{ configData.sysPath }}" wx:key="key">
        <view class="weapp-top-nav__item" style="margin-right: {{ item._right }}" wx:if="{{ item.key === 'share' }}">
          <button class="weapp-top-nav__item btn" open-type="share" hover-class="none">
            <view class="weapp-top-nav__item-image">
              <image mode="aspectFill" class="weapp-top-nav__item-img" src="{{ item.image }}" />
            </view>
            <view class="weapp-top-nav__item-title">{{ item.title }}</view>
          </button>
        </view>
        <view class="weapp-top-nav__item" style="margin-right: {{ item._right }}" wx:elif="{{ item.key === 'contact' }}">
          <message-contact style="width:{{108 * 1.2}}rpx" business-id="{{contactExtra.businessId}}" session-from="{{contactExtra.sourceParam}}" contact-class="weapp-top-nav__item btn" open-type="contact" bind:contact="onContactBack">
            <view class="weapp-top-nav__item-image">
              <image mode="aspectFill" class="weapp-top-nav__item-img" src="{{ item.image }}" />
            </view>
            <view class="weapp-top-nav__item-title">{{ item.title }}</view>
          </message-contact>
        </view>
        <view wx:else class="weapp-top-nav__item" style="margin-right: {{ item._right }}" data-key="{{ item.key }}" bind:tap="onMenuSysPathTap">
          <view class="weapp-top-nav__item-image">
            <image mode="aspectFill" class="weapp-top-nav__item-img" src="{{ item.image }}" />
          </view>
          <view class="weapp-top-nav__item-title">{{ item.title }}</view>
        </view>
      </block>
    </view>
    <view wx:if="{{ configData.customPath.length > 0 }}" class="weapp-top-nav__custom-path">
      <block wx:for="{{ configData.customPath }}">
        <view class="weapp-top-nav__item" data-item="{{ item }}" bind:tap="onMenuCustomPathTap" style="margin-right: {{ item._right }}rpx">
          <view class="weapp-top-nav__item-image">
            <image mode="aspectFill" class="weapp-top-nav__item-img_custom" wx:if="{{ item.imageUrl }}" src="{{ item.imageUrl }}" />
          </view>
          <view class="weapp-top-nav__item-title">{{ item.title }}</view>
        </view>
      </block>
    </view>
  </view>
</view>