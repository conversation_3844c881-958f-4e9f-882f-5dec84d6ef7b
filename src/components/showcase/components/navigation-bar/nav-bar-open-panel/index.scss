.weapp-top-nav {
  position: fixed;
  z-index: 10000;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  transition: all 0.5s ease-out;

  &-bg {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.2);
  }

  &-wrapper {
    z-index: 1;
    position: absolute;
    top: 32rpx * 1.2;
    left: 16px;
    padding: 12rpx * 1.2 24rpx * 1.2 24rpx * 1.2;
    background: #fbfbfb;
    max-width: 552rpx * 1.2;
    box-sizing: border-box;
    border-radius: 8rpx * 1.2;

    &__after {
      content: '';
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 14rpx * 1.2 16rpx * 1.2 14rpx * 1.2;
      border-color: transparent transparent #fff transparent;
      position: absolute;
      left: 74rpx * 1.2;
      top: -16rpx * 1.2;
    }
  }

  &__sys-path {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  &__custom-path {
    margin-top: 24rpx * 1.2;
    padding-top: 24rpx * 1.2 - 7.5 * 1.2;
    border-top: 1px solid #ebedf0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  &__item {
    display: flex;
    align-items: center;
    flex-direction: column;
    min-width: 108rpx * 1.2;
    width: 108rpx * 1.2;
    height: 108rpx * 1.2;
    margin-right: 24rpx * 1.2;

    &:nth-child(4n) {
      margin-right: 0;
    }

    &-img {
      height: 40rpx * 1.2;
      width: 40rpx * 1.2;
    }

    &-img_custom {
      height: 50rpx * 1.2;
      width: 50rpx * 1.2;
    }

    &-image {
      width: 80rpx * 1.2;
      height: 80rpx * 1.2;
      min-height: 80rpx * 1.2;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &-title {
      height: 28rpx * 1.2;
      font-size: 20rpx * 1.2;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(100, 101, 102, 1);
      line-height: 28rpx * 1.2;
      white-space: nowrap;
    }
  }
  .btn {
    padding: 0;
    border-radius: 0;
    background-color: transparent;
    border-width: 0;
    margin-right: 0;

    &::before {
      border: none;
    }

    &::after {
      border: none;
    }
  }
}
