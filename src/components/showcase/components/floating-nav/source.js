import { PAGE_TYPE_MAP } from './constants';

const app = getApp();
let currentKdtId = app.getKdtId();
let showOnPage = {};
const UPDATE_INTERVAL = 1000 * 60 * 2; // 更新间隔：2 分钟
let lastUpdate = { 9: 0 };
let navData = null;
let cartCount = -1;

// 从网络获取悬浮窗数据
function fetchNavData(pageType) {
  return app
    .requestUseCdn({
      path: '/wscdeco/showcase-api/floating_nav.json',
      data: { from: 'weapp', page_type: pageType, v: 2 },
    })
    .then((data) => {
      return data || {};
    })
    .catch(() => {});
}

// 从网络获取悬浮窗数据
export function getWeixinVideoInfo() {
  return app
    .requestUseCdn({
      path: '/wscdeco/weapp/WeappAccount/getWeixinVideoInfo.json',
    })
    .then((data) => {
      return data || {};
    })
    .catch(() => {});
}

// 从网络获取购物车商品数量
function fetchCartCount() {
  return app
    .request({ path: '/wscshop/trade/cart/count.json' })
    .then(({ count }) => {
      return count || 0;
    })
    .catch(() => {});
}

// 判断页面是否可以下拉刷新
function canPullDown(pageType) {
  if (
    pageType === PAGE_TYPE_MAP.HOME ||
    pageType === PAGE_TYPE_MAP.FEATURE ||
    pageType === PAGE_TYPE_MAP.GOODS_DETAIL
  ) {
    return true;
  }
  return false;
}

// 重置
function reset(kdtId) {
  currentKdtId = kdtId;
  navData = null;
  cartCount = -1;
  showOnPage = {};
  lastUpdate = { 9: 0 };
}

// clone
function clone(obj = {}) {
  return {
    isShareBlack: obj.isShareBlack,
    mainFloatingNav: { ...obj.mainFloatingNav },
    subFloatingNavs: (obj.subFloatingNavs || []).map((item) => ({ ...item })),
  };
}

export default {
  getNavData(pageType, isRefresh = false) {
    const isPullDownPage = canPullDown(pageType);
    const kdtId = app.getKdtId();

    if (kdtId !== currentKdtId) {
      // kdtId 不同
      reset(kdtId);
    }

    return new Promise((resolve) => {
      // 无数据 / 首次进入此类型的页面 / 下拉刷新 / 数据过期
      if (
        !navData ||
        typeof showOnPage[pageType] === 'undefined' ||
        isRefresh ||
        (!isPullDownPage && Date.now() - lastUpdate[pageType] > UPDATE_INTERVAL)
      ) {
        fetchNavData(pageType).then((data) => {
          if (!isPullDownPage) {
            lastUpdate[pageType] = Date.now();
          }
          if (data.value === 0) {
            // 此页面类型不显示悬浮窗
            showOnPage[pageType] = false;
            resolve({});
          } else {
            showOnPage[pageType] = true;
            navData = data;
            resolve(clone(navData));
          }
        });
        return;
      }

      if (showOnPage[pageType] === false) {
        resolve({});
        return;
      }

      resolve(clone(navData));
    });
  },

  getCartCount(isRefresh = false) {
    return new Promise((resolve) => {
      if (cartCount < 0 || isRefresh) {
        fetchCartCount().then((count) => {
          cartCount = count;
          resolve(count);
        });
      } else {
        resolve(cartCount);
      }
    });
  },

  addCartCount(num = 1) {
    cartCount += num;
  },
};
