import { PAGE_TYPE_MAP, EXTRA_ICON_PRIORITY, TYPE_MAP } from '../constants';

const hasGoodsShowcasePageTypes = [
  PAGE_TYPE_MAP.HOME,
  PAGE_TYPE_MAP.GOODS_DETAIL,
  PAGE_TYPE_MAP.GOODS_GROUP,
  PAGE_TYPE_MAP.FEATURE,
];

const app = getApp();

/*
 * 额外的独立子悬浮窗显示管理
 */
export class IconManager {
  constructor() {
    this.iconList = []; // 额外独立子图标列表信息
  }

  /**
   * 获取额外图标的promiseList
   * @param {number} pageType 页面类型
   */
  getExtraIconList(pageType) {
    this.pageType = pageType;
    this.iconList.length = 0; // 切换页面清空
    return [this.getGoodsShowcaseIcon()];
  }

  // 获取商品悬浮窗icon数据
  getGoodsShowcaseIcon() {
    if (hasGoodsShowcasePageTypes.indexOf(this.pageType) > -1) {
      return app
        .requestUseCdn({
          path: '/wscstatcenter/recommend/float-goods.json',
          data: {
            kdtId: app.getKdtId(),
            pageType: this.pageType,
          },
        })
        .then((data) => {
          const { iconData: icon } = data || {};
          icon &&
            this.setIcon({
              priority: EXTRA_ICON_PRIORITY.GOODS_SHOWCASE,
              detail: {
                ...icon,
                independent: true,
                navType: TYPE_MAP.GOODS_SHOWCASE,
                customClass: 'goods-showcase-icon',
              },
            });
          return icon;
        })
        .catch((err) => err);
    }
    return Promise.resolve();
  }

  /**
   * 处理额外的独立子悬浮窗图标数据
   * @param {object} data
   * @param {Array} data.subFloatingNavs
   * @param {object} data.mainFloatingNav
   */
  processNavData(data) {
    let { mainFloatingNav, subFloatingNavs = [] } = data;

    // 视频号直播浮窗逻辑处理
    const liveNav = subFloatingNavs.find(
      (nav) => nav.navType == TYPE_MAP.WECHAT_VIDEO
    );
    
    subFloatingNavs = subFloatingNavs.filter(
      (nav) => nav.navType != TYPE_MAP.WECHAT_VIDEO
    );


    if (this.iconList.length) {
      // 按优先级排序, 优先级高的排前面
      this.iconList.sort((a, b) => b.priority - a.priority);
      const independentIconList = this.iconList.map((it) => it.detail);

      subFloatingNavs.unshift(...independentIconList);
      // 原生悬浮窗未开启，则构造假数据放额外图标
      mainFloatingNav = mainFloatingNav || {
        floatingMode: 1,
        foldedIconUrl:
          '//img01.yzcdn.cn/public_files/2018/08/29/2e21a3be2d6815acc4bc915484733c78.png',
        unfoldedIconUrl:
          '//img01.yzcdn.cn/public_files/2018/08/29/6256e71064c9f5ebe30bdd796957ee7a.png',
      };
    }

    return {
      liveNav, // 视频号直播
      mainFloatingNav,
      subFloatingNavs,
    };
  }

  /**
   * 设置图标
   * @param {Object} icon
   * @param {Number} icon.priority 优先级
   * @param {Number} icon.detail 独立子图标信息
   */
  setIcon(icon = {}) {
    if (this.isValid(icon)) {
      this.iconList.push(icon);
    }
  }

  /**
   * 判断添加的图标信息是否有效
   * @param {Object} icon
   * @param {Number} icon.priority 优先级
   * @param {Number} icon.detail 独立子图标信息
   */
  isValid(icon = {}) {
    if (Object.prototype.toString.call(icon) !== '[object Object]') {
      throw new Error('添加的图标必须为一个对象类型');
    }

    const { priority, detail } = icon;
    if (typeof priority === 'undefined' || typeof detail === 'undefined') {
      throw new Error('添加的图标对象必须有优先级和详细信息');
    }

    return true;
  }
}
