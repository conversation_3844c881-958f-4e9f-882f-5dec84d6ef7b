 <view
  wx:if="{{ subFloatingNavs.length > 0 && !isChannels }}"
  class="custom-class theme-floating-nav {{ active ? 'active' : '' }} {{ hidden ? 'hidden' : '' }} {{ isIphoneX ? 'is-iphone-x' : '' }}"
>
  <!-- 只有一个子窗口 -->
  <block wx:if="{{ subFloatingNavs.length === 1 }}">
    <button
      wx:if="{{ first.navType === typeMap.SHARE}}"
      class="item main {{ first.isReplace ? 'share-animation' : '' }}"
      hover-class="none"
      style="background-image: url({{ first.iconUrl }});"
      open-type="{{ forbidShare ? '' : 'share' }}"
      session-from="{{ im.sourceParam }}"
      business-id="{{ im.businessId }}"
      bindcontact="onContactBack"
      data-nav-type="{{ first.navType }}"
      bindtap="handleNavTap"
    >
      <view wx:if="{{ hasShareActivity }}"  class="share-share-corner" >领</view>
    </button>
    <message-contact
      wx:elif="{{ first.navType === typeMap.CONTACT }}"
      contact-class="item main {{ first.isReplace ? 'share-animation' : '' }}"
      hover-class="none"
      open-type="contact"
      contact-style="background-image: url({{ first.iconUrl }});"
      session-from="{{ im.sourceParam }}"
      business-id="{{ im.businessId }}"
      bindcontact="onContactBack"
      data-nav-type="{{ first.navType }}"
      bindtap="handleNavTap"
    />
    <view
      wx:else
      class="item main {{ first.hasBadge ? 'has-badge' : '' }} {{ first.customClass || '' }}"
      style="background-image: url({{ first.iconUrl }});"
      data-nav-type="{{ first.navType }}"
      bindtap="handleNavTap"
    >
      <view wx:if="{{ first.tag }}" class="tag">{{ first.tag }}</view>
    </view>
  </block>

  <!-- 多于一个子窗口 -->
  <block wx:else>
    <view wx:if="{{ normalList.length > 0 }}" bindtap="handleMainNavTap">
      <view class="item main main-1" style="background-image: url({{ mainFloatingNav.foldedIconUrl }});" >
        <view wx:if="{{ hasShareActivity && shareActivityIcon === 1 }}"  class="share-share-corner" >领</view>
      </view>
      <view class="item main main-2" style="background-image: url({{ mainFloatingNav.unfoldedIconUrl }});" />
    </view>

    <!-- 竖排 -->
    <view class="line {{ normalList.length === 0 ? 'line--only-independent' : '' }}" >
      <block wx:if="{{ isLine }}">
        <block wx:for="{{ normalList }}" wx:key="navType">
          <template is="sub-item" data="{{ hasShareActivity, shareActivityIcon, item, index: active ? index + 1 : 0, forbidShare, typeMap, gap, im }}" />
        </block>
      </block>

      <block wx:for="{{ independentList }}" wx:key="navType">
        <template is="sub-item" data="{{ hasShareActivity, shareActivityIcon, item, index: (active && isLine) ? normalList.length + index + 1 : index + 1, forbidShare, typeMap, gap, im }}" />
      </block>
    </view>

    <!-- 面板 -->
    <block wx:if="{{ !isLine }}">
      <view class="outer-space" bindtouchstart="handleClosePanel" />

      <view class="panel {{ normalList.length > 6 ? 'panel--three-rows' : '' }}">
        <block wx:for="{{ normalList }}" wx:key="navType">
          <button
            wx:if="{{ item.navType === typeMap.SHARE }}"
            class="panel-item"
            hover-class="none"
            open-type="{{ forbidShare ? '' : 'share' }}"
            session-from="{{ im.sourceParam }}"
            business-id="{{ im.businessId }}"
            data-nav-type="{{ item.navType }}"
            bindtap="handleNavTap"
            bindcontact="onContactBack"
          >
            <view
              wx:if="{{ hasShareActivity }}"
              class="share-share-corner"
              style="top: 0;right: 7px;z-index: 1;"
            >领</view>
            <template is="panel-item" data="{{ item, iconTextMap }}" />
          </button>
            <message-contact
              class="panel-item-wrap"
              contact-class="panel-item"
              wx:elif="{{ item.navType === typeMap.CONTACT }}"
              hover-class="none"
              open-type="contact"
              session-from="{{ im.sourceParam }}"
              business-id="{{ im.businessId }}"
              data-nav-type="{{ item.navType }}"
              bindtap="handleNavTap"
              bindcontact="onContactBack"
            >
              <template is="panel-item" data="{{ item, iconTextMap }}" />
            </message-contact>
          <view
            wx:else
            class="panel-item"
            data-nav-type="{{ item.navType }}"
            bindtap="handleNavTap"
          >
            <template is="panel-item" data="{{ item, iconTextMap }}" />
          </view>
        </block>

        <view
          wx:for="{{ skeletonNum }}"
          wx:key="navType"
          class="panel-item"
        />
      </view>
    </block>
  </block>

  <!-- 特殊分享卡片 -->
  <share-feature
    wx:if="{{ shareCard === 'share-feature' }}"
    page-type="{{ shareCardPageType }}"
    show-share-pop="{{ showSharePop }}"
    salesman="{{ salesman }}"
    feature-alias="{{ featureAlias }}"
    bind:finished="hideSharePop"
    bind:update-share-status="updateShareStatus"
  />
</view>

<template name="sub-item">
  <button
    wx:if="{{ item.navType === typeMap.SHARE }}"
    class="item sub {{ item.independent ? 'independent' : '' }}  {{ item.isReplace ? 'share-animation' : '' }}"
    hover-class="none"
    style="background-image: url({{ item.iconUrl }}); transform: translateY(-{{ index * gap }}px);"
    open-type="{{ forbidShare ? '' : 'share' }}"
    session-from="{{ im.sourceParam }}"
    business-id="{{ im.businessId }}"
    data-nav-type="{{ item.navType }}"
    bindtap="handleNavTap"
    bindcontact="onContactBack"
  >
    <view wx:if="{{ hasShareActivity }}"  class="share-share-corner" >领</view>
  </button>
  <message-contact
    wx:elif="{{ item.navType === typeMap.CONTACT }}"
    class="asd item sub {{ item.independent ? 'independent' : '' }}  {{ item.isReplace ? 'share-animation' : '' }}"
    hover-class="none"
    style="background-image: url({{ item.iconUrl }}); transform: translateY(-{{ index * gap }}px);"
    open-type="contact"
    session-from="{{ im.sourceParam }}"
    business-id="{{ im.businessId }}"
    data-nav-type="{{ item.navType }}"
    bindtap="handleNavTap"
    bindcontact="onContactBack"
    opacity-full
  />
  <view
    wx:else
    class="item sub {{ item.independent ? 'independent' : '' }} {{ item.hasBadge ? 'has-badge' : '' }} {{ item.customClass || '' }}"
    style="background-image: url({{ item.iconUrl }}); transform: translateY(-{{ index * gap }}px);"
    data-nav-type="{{ item.navType }}"
    bindtap="handleNavTap"
  >
    <view wx:if="{{ item.tag }}" class="tag">{{ item.tag }}</view>
  </view>
</template>

<template name="panel-item">
  <view
    class="item sub {{ item.hasBadge ? 'has-badge' : '' }}"
    style="background-image: url({{ item.iconUrl }});"
  />
  {{ iconTextMap[item.navType] || item.customeTitle }}
</template>

<!-- 商品橱窗弹层 -->
<goods-showcase
  show="{{ showGoodsShowcase }}"
  bind:close="closeGoodsShowcase"
/>

<!-- 直播挂件 -->
<view hidden="{{ active || isWxvideoLiveShow }}">
  <live-popup />
</view>

<!-- 点单宝堂食挂件 -->
<dine-entry />

<!-- 视频号直播挂件 -->
<view hidden="{{ active }}">
  <channel-live-pop info="{{liveNav}}" bind:update-live-status="updateWxvideoLiveStatus"/>
</view>
