@function parse-rpx($args) {
  @return $args * 2;
}

@keyframes wave {
  0% {
    height: 8px;
  }
  50% {
    height: 18px;
  }
  100% {
    height: 8px;
  }
}

@keyframes wave_low {
  0% {
    height: 8px;
  }
  50% {
    height: 16px;
  }
  100% {
    height: 8px;
  }
}

@keyframes wave_new {
  0% {
    height: parse-rpx(4rpx);
  }

  50% {
    height: parse-rpx(9rpx);
  }

  100% {
    height: parse-rpx(4rpx);
  }
}

@keyframes wave_low_new {
  0% {
    height: parse-rpx(4rpx);
  }
  50% {
    height: parse-rpx(8rpx);
  }
  100% {
    height: parse-rpx(4rpx);
  }
}

@keyframes circle_outside {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(1.1);
  }
}

@keyframes circle_inside {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
  }
}

.channel-live-floating-new {
  position: fixed;
  top: parse-rpx(131rpx);
  right: parse-rpx(5rpx);
  // 查看 code/wsc/docs/z-Index.md
  z-index: 140;

  .top {
    position: relative;
    width: parse-rpx(52rpx);
    height: parse-rpx(52rpx);
    padding: parse-rpx(5.2rpx);
    padding-bottom: 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .circle-outside {
    animation: circle_outside 0.8s cubic-bezier(0, 0, 1, 1) infinite;
  }

  .circle-middle,
  .circle-outside {
    position: absolute;
    border-radius: 50%;
    border: parse-rpx(1.5rpx) solid #ff2281;
    width: parse-rpx(52rpx);
    height: parse-rpx(52rpx);
    box-sizing: border-box;
    top: parse-rpx(5.2rpx);
    right: parse-rpx(5.2rpx);
  }

  .circle-inside {
    border-radius: 50%;
    border: parse-rpx(1rpx) solid #ff2281;
    width: parse-rpx(44rpx);
    height: parse-rpx(44rpx);
    top: parse-rpx(4rpx);
    right: parse-rpx(4rpx);
    overflow: hidden;
    animation: circle_inside 1.6s cubic-bezier(0, 0, 1, 1) infinite;

    image {
      width: parse-rpx(44rpx);
      height: parse-rpx(44rpx);
    }
  }

  .bottom {
    position: absolute;
    width: parse-rpx(48rpx);
    height: parse-rpx(15rpx);
    background-image: linear-gradient(146deg, #ff2282 0%, #e23d49 100%);
    border-radius: parse-rpx(7.5rpx);
    top: parse-rpx(43.2rpx);
    right: parse-rpx(7.2rpx);
    overflow: hidden;

    image {
      width: parse-rpx(48rpx);
      height: parse-rpx(15rpx);
    }
  }

  .live-icon-wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: parse-rpx(8rpx);
    height: parse-rpx(9rpx);
  }

  .default-live-icon-wave {
    position: absolute;
    width: 100%;
    height: 100%;
    top: parse-rpx(4rpx);
    left: parse-rpx(4rpx);
    display: flex;
    box-sizing: border-box;
    justify-content: center;

    .wave-wrap {
      height: parse-rpx(9rpx);
      display: flex;
      align-items: flex-end;
      justify-content: center;
    }

    .wave-item {
      width: parse-rpx(2rpx);
      height: parse-rpx(4rpx);
      border-radius: parse-rpx(1rpx);
      background: #fff;

      + .wave-item {
        margin-left: parse-rpx(1.5rpx);
      }

      &.wave-1 {
        animation: wave_low_new 1s ease-in-out infinite;
      }

      &.wave-2 {
        animation: wave_new 1s ease-in-out 0.4s infinite;
      }

      &.wave-3 {
        animation: wave_low_new 1s ease-in-out 0.8s infinite;
      }
    }
  }
}

.channel-live-floating {
  position: fixed;
  right: 32rpx;
  width: 96rpx;
  height: 96rpx;
  top: 136px;
  z-index: 141;
}

.custom-icon {
  width: 100rpx;
  height: 100rpx;
}

.custom-icon image {
  width: 100%;
  height: 100%;
}

.default-icon {
  position: absolute;
  width: 96rpx;
  height: 104rpx;
}

.default-live-icon {
  width: 100%;
  height: 100%;
}

.default-icon image {
  width: 100%;
  height: 100%;
}

.default-icon .default-live-icon-wave {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  padding: 10px;
  box-sizing: border-box;
  justify-content: center;
}

.default-icon .default-live-icon-wave .wave-wrap {
  height: 18px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.default-icon .default-live-icon-wave .wave-item {
  width: 3px;
  height: 8px;
  border-radius: 1.5px;
  background: #fff;
}

.default-icon .default-live-icon-wave .wave-item + .wave-item {
  margin-left: 3px;
}

.default-icon .default-live-icon-wave .wave-item.wave-1 {
  animation: wave_low 1s ease-in-out infinite;
}

.default-icon .default-live-icon-wave .wave-item.wave-2 {
  animation: wave 1s ease-in-out 0.4s infinite;
}

.default-icon .default-live-icon-wave .wave-item.wave-3 {
  animation: wave_low 1s ease-in-out 0.8s infinite;
}
