import WscComponent from 'pages/common/wsc-component/index';
import { getWeixinVideoInfo } from '../../source';
import { EVENT_MAP } from '../../constants';
import { checkSingleStore } from '@youzan/utils-shop';
import {
  checkIsOpenByLiveGoodsShare,
  checkIsOpenByLiveBag,
} from 'shared/utils/channel';

const app = getApp();

WscComponent({
  data: { isLive: false, finderUserName: '', headUrl: '' },

  properties: {
    info: {
      type: Object,
      value: {},
    },
  },

  methods: {
    linkToLive() {
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'floating_nav_wechat_video_click', // 事件标识
          en: '视频号直播悬浮窗点击', // 事件名称
          params: {
            component: 'floating_nav_wechat_video',
            nickname: this.nickname,
          },
        });
      if (!this.feedId || !this.nonceId) {
        wx.showToast({
          title: '视频号直播id为空',
          icon: 'none',
        });
        return;
      }
      wx.openChannelsLive({
        feedId: this.feedId,
        nonceId: this.nonceId,
        finderUserName: this.data.finderUserName,
      });
    },

    updateLiveStatus() {
      const { finderUserName } = this.data;

      if (finderUserName) {
        wx.getChannelsLiveInfo &&
          wx.getChannelsLiveInfo({
            finderUserName,
            success: (res) => {
              const { status, feedId, nonceId, nickname, headUrl } = res;
              if (+status === 2) {
                this.setData({ isLive: true, headUrl });
                this.feedId = feedId;
                this.nonceId = nonceId;
                this.nickname = nickname;

                this.triggerEvent('update-live-status', true);

                app.logger &&
                  app.logger.log({
                    et: 'view', // 事件类型
                    ei: 'floating_nav_wechat_video_view', // 事件标识
                    en: '视频号直播悬浮窗曝光', // 事件名称
                    params: {
                      component: 'floating_nav_wechat_video',
                      nickname,
                    },
                  });
              } else {
                this.setData({ isLive: false });
              }
            },
            fail: (e) => {
              app.logger &&
                app.logger.log({
                  et: 'view', // 事件类型
                  ei: 'floating_nav_wechat_video_view_fail', // 事件标识
                  en: '视频号直播悬浮窗曝光失败', // 事件名称
                  params: {
                    component: 'floating_nav_wechat_video',
                    errCode: e.err_code,
                    finderUserName,
                    noticeInfo: JSON.stringify(e),
                  },
                });
            },
          });
      }
    },

    getChannelId() {
      return getWeixinVideoInfo().then((res) => {
        const { videoChannelId = '' } = res || {};
        const finderUserName = videoChannelId;
        this.setData({ finderUserName });
      });
    },

    handlePullDownRefresh() {
      this.updateLiveStatus();
    },
  },

  ready() {
    // 监听下拉
    this.on && this.on(EVENT_MAP.PULL_DOWN, this.handlePullDownRefresh);
    // 这里不要用getShopInfoSync，因为sync有坑，某些数据还没有set好，就返回了。
    app.getShopInfo().then((shop) => {
      const { shopMetaInfo = {} } = shop;
      const isSingleStore = checkSingleStore(shopMetaInfo);
      // 非单店判断是否在白名单,不在白名单就不显示
      if (!isSingleStore) {
        const whiteList = [96391935, 90777808, 73366021, 90957577];
        const isWhite = whiteList.indexOf(+this.kdtId) > -1;
        if (!isWhite) return;
      }

      // 直播购物袋中/直播间商品分享 打开不展示
      if (checkIsOpenByLiveGoodsShare() || checkIsOpenByLiveBag()) {
        return;
      }
      this.getChannelId().then(() => {
        this.updateLiveStatus();
      });
    });
  },
});
