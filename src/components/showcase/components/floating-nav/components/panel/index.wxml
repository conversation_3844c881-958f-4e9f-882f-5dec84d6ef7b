<view class="{{ active ? 'active' : '' }}">
  <view class="outer-space" bindtouchstart="handleClosePanel" />

  <view class="panel {{ list.length > 6 ? 'panel--three-rows' : '' }}">
    <block wx:for="{{ list }}" wx:key="{{ item.navType || index }}">
      <button
        wx:if="{{ item.navType === typeMap.SHARE || item.navType === typeMap.CONTACT }}"
        class="panel-item"
        hover-class="none"
        open-type="{{ item.navType === typeMap.CONTACT ? 'contact' : (isGoodsPage ? '' : 'share') }}"
        data-nav-type="{{ item.navType }}"
        bindtap="handleNavTap"
      >
        <template is="panel-item" data="{{ item, iconTextMap }}" />
      </button>

      <view
        wx:else
        class="panel-item"
        data-nav-type="{{ item.navType }}"
        bindtap="handleNavTap"
      >
        <template is="panel-item" data="{{ item, iconTextMap }}" />
      </view>
    </block>

    <view
      wx:for="{{ skeletonNum }}"
      wx:key="skeleton-{{ item }}"
      class="panel-item"
    />
  </view>
</view>

<template name="panel-item">
  <view
    class="item sub {{ item.hasBadge ? 'has-badge' : '' }}"
    style="background-image: url({{ item.iconUrl }});"
  />
  {{ iconTextMap[item.navType] }}
</template>
