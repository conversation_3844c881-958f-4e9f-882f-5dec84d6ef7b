import { TYPE_MAP, ICON_TEXT_MAP } from '../../constants';

Component({
  properties: {
    list: Array,
    skeletonNum: Number,
    isTab: Boolean,
    isGoodsPage: Boolean,
    active: Boolean
  },

  data: {
    typeMap: TYPE_MAP,
    iconTextMap: ICON_TEXT_MAP
  },

  methods: {
    handleNavTap(ev) {
      const { navType, linkUrl } = ev.currentTarget.dataset;
      this.triggerEvent('navtap', { navType, linkUrl });
    },

    handleClosePanel() {
      this.triggerEvent('close');
    }
  }
});
