import { TYPE_MAP } from '../../constants';

Component({
  properties: {
    normalList: Array,
    independentList: Array,
    skeletonNum: Number,
    isGoodsPage: Boolean,
    active: Boolean
  },

  data: {
    typeMap: TYPE_MAP
  },

  methods: {
    handleNavTap(ev) {
      const { navType, linkUrl } = ev.currentTarget.dataset;
      this.triggerEvent('navtap', { navType, linkUrl });
    }
  }
});
