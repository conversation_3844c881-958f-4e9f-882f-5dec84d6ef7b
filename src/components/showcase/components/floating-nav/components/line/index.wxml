<view class="line {{ normalList.length === 0 ? 'line--only-independent' : '' }} {{ active ? 'active': '' }}" >
  <view wx:for="{{skeletonNum}}" wx:key="{{ index }}" class="sub skeleton"/>

  <block wx:for="{{ independentList }}" wx:key="{{ index }}">
    <template is="sub-item" data="{{ item, index, isGoodsPage, typeMap }}" />
  </block>

  <block wx:if="{{ normalList.length <= 3 }}">
    <block wx:for="{{ normalList }}" wx:key="{{ index }}">
      <template is="sub-item" data="{{ item, index, isGoodsPage, typeMap }}" />
    </block>
  </block>
</view>

<template name="sub-item">
  <button
    wx:if="{{ item.navType === typeMap.SHARE || item.navType === typeMap.CONTACT }}"
    class="sub {{ item.independent ? 'independent independent-' + (index + 1) : '' }}"
    hover-class="none"
    style="background-image: url({{ item.iconUrl }});"
    open-type="{{ item.navType === typeMap.CONTACT ? 'contact' : (isGoodsPage ? '' : 'share') }}"
    data-nav-type="{{ item.navType }}"
    bindtap="handleNavTap"
  />
  <view
    wx:else
    class="sub {{ item.independent ? 'independent independent-' + (index + 1) : '' }} {{ item.hasBadge ? 'has-badge' : '' }} {{ item.customClass || '' }}"
    style="background-image: url({{ item.iconUrl }});"
    data-nav-type="{{ item.navType }}"
    bindtap="handleNavTap"
  />
</template>
