import WscComponent from 'pages/common/wsc-component/index';
import Event from '@youzan/weapp-utils/lib/event';
import { getPlugins } from '@youzan/ranta-helper-tee';
import getUniquePageKey from 'shared/utils/unique';

const app = getApp();
const DINE_DATA_KEY = 'retail:dineData';

WscComponent({
  data: {
    dineData: null,
    dineFold: false,
    showDineEntry: false,
    tableNumber: '',
    themeMainColor: '#fff',
  },

  properties: {
    themeColors: {
      type: Object,
      default: () => ({}),
    },
  },

  methods: {
    // 初始化堂食点单桌号悬浮窗
    initDineNavData() {
      const { startTime, lifeTime, data } =
        wx.getStorageSync(DINE_DATA_KEY) || {};

      const currTime = new Date().getTime();

      // 开始时间为空 或 超过生命周期，则重置桌号数据
      if (!startTime || currTime - startTime > lifeTime) {
        this.resetDineNavData();
        return;
      }

      this.handleDineTimer(lifeTime - (currTime - startTime));

      this.setYZData({
        dineData: data,
        tableNumber: data.queryData.tableNumber,
        showDineEntry: true,
      });

      // 页面滚动时隐藏
      Event.once('onPageScroll' + getUniquePageKey(), () => {
        this.setYZData({
          dineFold: true,
        });
      });
    },

    // 距离扫码后一定时间，桌号自动清除
    handleDineTimer(time) {
      if (this.dineTimer) {
        clearTimeout(this.dineTimer);
      }
      this.dineTimer = setTimeout(() => {
        this.resetDineNavData();
      }, time);
    },

    // 重置堂食桌号数据
    resetDineNavData() {
      this.setYZData({
        showDineEntry: false,
        dineData: null,
      });

      wx.setStorage(DINE_DATA_KEY, {});
    },

    linkToShelf() {
      const { dmc } = getPlugins();
      const { route, queryData } = this.data.dineData;

      dmc.navigate(route, queryData);
    },
  },

  attached() {
    app.getShopTheme().then((res) => {
      this.setYZData({
        themeMainColor: res.colors['main-bg'],
      });
    });
    this.initDineNavData();

    // 监听 kdtid变化
    Event.on('retail:update:kdtid', () => {
      wx.setStorage({
        key: 'retail:dineData',
        data: '{}',
      });
    });
  },
  detached() {
    Event.off('retail:update:kdtid');
  },
});
