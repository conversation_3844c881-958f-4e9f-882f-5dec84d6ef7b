@function parse-tvw($args) {
  @return $args * 0.267vw;
}

.dine-floating {
  position: fixed;
  top: parse-tvw(190);
  right: 0;
  z-index: 99;
  width: parse-tvw(99);
  height: parse-tvw(40);
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &--fold {
    right: parse-tvw(-71);
    transition: right 0.3s;
  }

  &__number {
    width: parse-tvw(40);
    height: parse-tvw(40);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 parse-tvw(5);
    box-sizing: border-box;
    position: absolute;
    left: parse-tvw(-20);

    &__text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #fff;
    }
  }

  &__text {
    padding-right: parse-tvw(8);
    color: #333;

    &__arrow {
      color: #c8c9cc;
    }
  }
}
