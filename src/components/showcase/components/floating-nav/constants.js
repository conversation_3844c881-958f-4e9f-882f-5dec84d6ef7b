export const TYPE_MAP = {
  HOME: 1,
  CART: 2,
  WISH: 3,
  CONTACT: 4,
  SHARE: 5,
  USERCENTER: 6,
  BACK_TO_TOP: 7,
  SCAN: 8,
  GUANG_LIVE: 20,
  GOODS_SHOWCASE: 21,
  WECHAT_VIDEO: 22,
  CUSTOME: [101, 102, 103, 104, 105, 106, 107, 108, 109],
};

export const ICON_TEXT_MAP = {
  1: '主页',
  2: '购物车',
  3: '心愿单',
  4: '客服',
  5: '分享',
  6: '个人中心',
  7: '顶部',
  8: '扫一扫',
};

export const LINK_URL_MAP = {
  1: '/pages/home/<USER>/index',
  '1-retail': '/pages-retail/home-shelf/index',
  2: '/packages/goods/cart/index',
  6: '/packages/usercenter/dashboard/index',
  '6-retail': '/packages/retail/usercenter/dashboard-v2/index',
};

export const URL_PATTERN_MAP = {
  1: [
    /^(packages|pages)\/home\/dashboard/,
    /^packages\/(old-home|ext-home)\/dashboard/,
    /^packages\/showcase-template\/index/,
  ],
  3: [
    /^(packages|pages)\/home\/feature/,
    /^packages\/(old-home|ext-home)\/feature/,
    /^(packages|pages)\/home\/tab/,
    /^packages\/(old-home|ext-home)\/tab\/(one|two|three)/,
  ],
  8: [/^packages\/goods(:?-v\d+)?\//, /^pages\/goods\/detail/],
  9: [
    /^packages\/shop\/goods\/all/,
    /^packages\/shop\/goods\/group/,
    /^packages\/shop\/goods\/tag-list\/index/,
    /^packages\/goods-list\/tag\/index/,
    /^packages\/goods-list\/all\/index/,
  ],
};

export const PAGE_TYPE_MAP = {
  HOME: 1,
  FEATURE: 3,
  GOODS_DETAIL: 8,
  GOODS_GROUP: 9,
};

export const EVENT_MAP = {
  ADD_CART: 'component:sku:cart',
  PULL_DOWN: 'home:refresh',
  SHARE: 'share',
};

export const FORBID_SHARE_TYPE = {
  1: true,
  3: true,
  8: true,
  9: false,
};

export const SHARE_CARD_MAP = {
  1: 'share-feature',
  3: 'share-feature',
};

export const SHARE_CARD_TYPE_MAP = {
  1: 'homepage',
  3: 'micropage',
};

// 额外的独立子图标显示优先级，优先级越大，显示越靠上
export const EXTRA_ICON_PRIORITY = {
  GOODS_SHOWCASE: 10, // 商品橱窗
};

export const WAIT_SHOW_TIME = 30;
