import WscComponent from 'pages/common/wsc-component/index';
import throttle from 'utils/throttle';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import StickyControl from 'shared/utils/sticky-control';
import pageScrollBehavior from 'shared/components/showcase/behaviors/page-scroll-control-behavior';
import getSystemInfo from 'shared/utils/browser/system-info';
import navigate from '@/helpers/navigate';
import scanGoodsBarCode from '@/utils/scan-goods-barcode';
import {
  TYPE_MAP,
  ICON_TEXT_MAP,
  LINK_URL_MAP,
  URL_PATTERN_MAP,
  PAGE_TYPE_MAP,
  EVENT_MAP,
  FORBID_SHARE_TYPE,
  SHARE_CARD_MAP,
  SHARE_CARD_TYPE_MAP,
} from './constants';
import source from './source';
import IconController from './icon-controller/index';
import { isNewIphone } from 'shared/utils/browser/device-type';
import { IconManager } from './independent/index';
import getUniquePageKey from 'shared/utils/unique';
import { checkIsChannels } from 'shared/utils/channel';
import { TAB_CONTAINER_RULE } from '@youzan/wsc-tab-bar-utils/lib/constants';
import tabNavigate from '@youzan/wsc-tab-bar-utils/lib/weapp/navigate';
import { jumpToLink } from 'shared/components/showcase-options-home/jump-to-link';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

const app = getApp();
const { system } = getSystemInfo();
const isAndroid = /android/i.test(system);
const isIphoneX = isNewIphone();
const FLOATING_MODE_GET_OUT = 2; // 悬浮模式。上滑隐藏，下滑出现
const CRITICAL_NUM = 3; // 竖排展开和面板展开之间的悬浮窗临界数。<=3，竖排；>3，面板
const GAP = 58; // 竖排相邻悬浮窗的间隔
const iconManager = new IconManager(); // 额外独立子图标显示管理
const SHARE_GIFT_ICON =
  'https://b.yzcdn.cn/public_files/d54cce9404af18381038acb27935dafa.png';
const SHARE_ORIGINAL_ICON =
  '/public_files/2018/08/29/4dde171ddb3d829e147799fb48e2b91d.png';

// 获取当前页面
function getCurPage() {
  const ps = getCurrentPages();
  return ps[ps.length - 1];
}

WscComponent({
  externalClasses: ['custom-class'],

  behaviors: [pageScrollBehavior],

  properties: {
    salesman: {
      type: Object,
      value: {},
    },
    featureAlias: {
      type: String,
    },
    hasShareBenefitActivity: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    active: false,
    hidden: false,
    isWxvideoLiveShow: false,
    typeMap: TYPE_MAP,
    iconTextMap: ICON_TEXT_MAP,
    // 视频号直播悬浮窗
    liveNav: null,
    mainFloatingNav: {},
    subFloatingNavs: [],
    normalList: [],
    independentList: [],
    skeletonNum: 0,
    isIphoneX,
    isTab: false,
    shareCard: '',
    showSharePop: false,
    showGoodsShowcase: false,
    im: {
      sourceParam: '',
      businessId: '',
    },
    hasShareActivity: false,
    // 0无，1展开，2独立图标
    shareActivityIcon: 0,
    // 是否是视频号
    isChannels: checkIsChannels(),
  },

  observers: {
    hasShareBenefitActivity(v) {
      // 商详页分享有礼
      this.setYZData({
        hasShareActivity: v,
      });
    },
  },

  attached() {
    // 视频号不展示悬浮窗
    if (checkIsChannels()) return;

    const page = getCurPage();
    this.pageType = this.getPageType(page.route); // 获取页面类型 pageType

    this.on && this.on(EVENT_MAP.PULL_DOWN, this.handlePullDownRefresh); // 监听下拉刷新

    this.lastScrollTop = 0; // 设置初始 scrollTop
    this.justAttached = true; // 用于防止在自动下拉刷新时，重复请求数据

    this.pageKey = getUniquePageKey();
    this.setYZData({
      componentUniqueKey: this.pageKey,
    });

    // 设置特殊分享卡片
    this.setYZData({
      gap: GAP,
      shareCard: SHARE_CARD_MAP[this.pageType] || '',
    });

    app.waitForEnterShop().then(() => {
      Promise.all([
        source.getNavData(this.pageType),
        app.isSwitchTab(),
        ...iconManager.getExtraIconList(this.pageType),
      ]).then(this.processNavData.bind(this));
    });
  },

  detached() {
    this.delPageScrollControlSubscribe(this.scrollHandler, this, this.pageKey);
    this.hasSetPageScroll = false;
    this.iconController && this.iconController.destroy(this);
  },

  methods: {
    updateShareStatus() {
      this.setData(
        {
          hasShareActivity: true,
        },
        this.checkShareGiftIcon
      );
    },

    updateWxvideoLiveStatus(value) {
      this.setData({
        isWxvideoLiveShow: value?.detail,
      });
    },

    checkShareGiftIcon() {
      const { subFloatingNavs, independentList, normalList, hasShareActivity } =
        this.data;
      const findIterator = (item) => item.navType === TYPE_MAP.SHARE;

      if (subFloatingNavs.find(findIterator) && hasShareActivity) {
        const shareIndependIndex = independentList.findIndex(findIterator);
        const shareNormalIndex = normalList.findIndex(findIterator);
        if (shareIndependIndex > -1) {
          const shareIcon = independentList[shareIndependIndex].iconUrl;
          if (shareIcon.indexOf(SHARE_ORIGINAL_ICON) > -1) {
            this.setYZData({
              [`independentList[${shareIndependIndex}].iconUrl`]:
                SHARE_GIFT_ICON,
            });
            this.setData({
              'first.iconUrl': SHARE_GIFT_ICON,
            });
          }
        } else if (shareNormalIndex > -1) {
          const shareIcon = normalList[shareNormalIndex].iconUrl;
          if (shareIcon.indexOf(SHARE_ORIGINAL_ICON) > -1) {
            this.setYZData({
              [`normalList[${shareNormalIndex}].iconUrl`]: SHARE_GIFT_ICON,
            });
          }
        }
      }
    },

    handleShare() {
      // 如果是主页或微页面，打开分享卡片
      if (
        this.pageType === PAGE_TYPE_MAP.HOME ||
        this.pageType === PAGE_TYPE_MAP.FEATURE
      ) {
        this.openSharePop(SHARE_CARD_TYPE_MAP[this.pageType]);
      }
      this.triggerEvent(EVENT_MAP.SHARE);
    },

    onContactBack: navigate.contactBack,

    // 打开分享卡片
    openSharePop(shareCardPageType) {
      this.setYZData({
        shareCardPageType,
        showSharePop: true,
      });
    },

    // 隐藏分享卡片
    hideSharePop() {
      this.setYZData({
        showSharePop: false,
      });
    },

    // 点击主窗口
    handleMainNavTap() {
      const { active, normalList } = this.data;

      this.changeActive(!active, {}, () => {
        const { independentList } = this.data;
        this.updateSalesManIcon(normalList, independentList);
      });
    },

    // 点击子窗口
    handleNavTap(ev) {
      const { navType } = ev.currentTarget.dataset;

      if (!navType) {
        return;
      }

      const nav = this.data.subFloatingNavs.find(
        (item) => item.navType === navType
      );
      const isRetailApp = app.globalData && app.globalData.isRetailApp;

      switch (navType) {
        case TYPE_MAP.BACK_TO_TOP: // 返回顶部
          StickyControl.releaseStickyControlMap();
          wx.pageScrollTo({
            scrollTop: 0,
            duration: isAndroid ? 0 : 300,
          });
          break;
        case TYPE_MAP.SHARE: // 分享
          this.handleShare();
          break;
        case TYPE_MAP.HOME: // 主页
          navigate.switchTab({
            url: LINK_URL_MAP[navType],
          });
          break;
        case TYPE_MAP.CART: // 购物车
          tabNavigate({
            path: '/pages/goods/cart/index',
            fail() {
              navigate.switchTab({
                url: LINK_URL_MAP[navType],
              });
            },
          });
          break;
        case TYPE_MAP.USERCENTER: // 会员中心
          if (isRetailApp) {
            navigate.switchTab({
              url: LINK_URL_MAP[`${navType}-retail`],
            });
          } else {
            tabNavigate({
              path: '/pages/usercenter/dashboard/index',
              fail() {
                navigate.switchTab({
                  url: LINK_URL_MAP[navType],
                });
              },
            });
          }
          break;
        case TYPE_MAP.GUANG_LIVE: // 爱逛直播
          wx.navigateToMiniProgram({
            appId: nav.appId,
            path: nav.path,
          });
          break;
        case TYPE_MAP.GOODS_SHOWCASE: // 商品橱窗
          this.setYZData({
            showGoodsShowcase: true,
          });
          break;
        case TYPE_MAP.SCAN: // 扫一扫
          scanGoodsBarCode();
          break;
        default:
          // 名字自定义工具
          if (TYPE_MAP.CUSTOME.includes(navType)) {
            // eslint-disable-next-line no-case-declarations
            let link;
            try {
              link = mapKeysCase.toCamelCase(JSON.parse(nav.extraData));
              link &&
                jumpToLink(link, {
                  kdtId: getApp().getKdtId(),
                });
            } catch (error) {
              app.logger.log(error);
            }
          }
          break;
      }

      this.data.active && this.changeActive(false, {}); // 恢复原位
    },

    // 关闭商品橱窗
    closeGoodsShowcase() {
      this.setYZData({
        showGoodsShowcase: false,
      });
    },

    // 关闭弹层
    handleClosePanel() {
      this.changeActive(false, {});
    },

    // 更新：展开 or 收起
    changeActive(active, otherData = {}, callback = () => {}) {
      this.setYZData({ active, ...otherData }, callback);

      if (this.data.isLine && this.iconController) {
        // 只有在子窗口少于 4 个时（即竖排展开时），才更新分销员 icon 的位置
        this.iconController.setAlone(this, active);
      }
    },

    // 处理悬浮窗数据
    processNavData([data, isTab], isRefresh = false) {
      const navData = iconManager.processNavData(data);
      const { liveNav, mainFloatingNav } = navData;
      let { subFloatingNavs = [] } = navData;

      // if (data) this.isShareBlack = data.isShareBlack;

      // 在小程序需要过滤心愿单
      subFloatingNavs = subFloatingNavs.filter(({ navType }) => {
        // 过滤心愿单图标
        if (navType === TYPE_MAP.WISH) {
          return false;
        }

        // 主页过滤主页图标
        if (navType === TYPE_MAP.HOME) {
          return this.pageType !== PAGE_TYPE_MAP.HOME;
        }

        // 商品详情过滤购物车图标
        if (navType === TYPE_MAP.CART) {
          return this.pageType !== PAGE_TYPE_MAP.GOODS_DETAIL;
        }

        if (navType === TYPE_MAP.CONTACT) {
          app.getDefaultImData().then((im) => {
            this.setYZData({ im });
          });
        }

        return true;
      });

      // 子窗口数量为 0（未开启悬浮窗 或 子悬浮窗数组为空）
      if (subFloatingNavs.length === 0) {
        this.changeActive(
          false,
          {
            hidden: false,
            isTab,
            subFloatingNavs: [],
          }
          // this.restartShareObserver
        );

        // 移除滚动监听
        this.delPageScrollControlSubscribe(
          this.scrollHandler,
          this,
          this.pageKey
        );
        this.hasSetPageScroll = false;

        // 设置位置
        this.iconController &&
          this.iconController.setIcon(this, { priority: 100, height: 0 });
        return;
      }

      if (mainFloatingNav.floatingMode === FLOATING_MODE_GET_OUT) {
        // 监听滚动
        if (!this.hasSetPageScroll) {
          this.pageKey = this.setPageScrollControlSubscribe(
            this.scrollHandler,
            this
          );
          this.hasSetPageScroll = true;
        }
      } else if (this.hasSetPageScroll) {
        this.delPageScrollControlSubscribe(
          this.scrollHandler,
          this,
          this.pageKey
        );
        this.hasSetPageScroll = false;
      }

      // 对数据进行处理
      mainFloatingNav.foldedIconUrl = this.getCdnImageUrl(
        mainFloatingNav.foldedIconUrl
      );
      mainFloatingNav.unfoldedIconUrl = this.getCdnImageUrl(
        mainFloatingNav.unfoldedIconUrl
      );
      const { normalList, independentList, isLine } =
        this.getComputedSubNavList(subFloatingNavs);

      // 更新悬浮窗数据
      this.setYZData(
        {
          hidden: false,
          isTab,
          liveNav,
          mainFloatingNav,
          subFloatingNavs,
          first: subFloatingNavs[0],
          normalList,
          independentList,
          isLine,
          skeletonNum: this.getSkeletonNum(normalList, isLine),
          forbidShare: FORBID_SHARE_TYPE[this.pageType] || false,
        },
        this.checkShareGiftIcon
      );

      // 获取购物车数据
      if (this.whereIsCart) {
        // 如果存在购物车悬浮窗
        this.on(EVENT_MAP.ADD_CART, this.handleAddCart); // 监听添加购物车
        source.getCartCount(isRefresh).then((count) => {
          this.refreshCartIcon(+count > 0);
        });
      }

      this.updateSalesManIcon(normalList, independentList);
    },

    updateSalesManIcon(normalList, independentList) {
      // 调整分销员 icon 的位置
      const visibleNum = this.getVisibleNum(normalList, independentList); // 可见悬浮窗的数量
      const opts = {
        priority: 100,
        height: GAP * visibleNum - 10,
      };
      if (this.iconController) {
        this.iconController.setIcon(this, opts);
      } else {
        this.iconController = new IconController()
          // .setBase(this.getBasePosition(isTab, isIphoneX))
          ?.setIcon(this, opts);
      }
    },

    /**
     * 处理购物车数据
     * @param {Number} count 购物车商品数量
     * @param {Object} cartEvent “添加购物车”事件
     */
    handleAddCart(cartEvent = {}) {
      if (cartEvent.type === 'add') {
        source.addCartCount();
        this.refreshCartIcon(true);
      }
    },

    // 刷新购物车
    refreshCartIcon(hasGoods) {
      const [type, index] = this.whereIsCart; // 获取购物车的相关信息：【普通悬浮窗 or 独立悬浮窗】【下标位置】

      if (this.data.subFloatingNavs.length === 1) {
        this.setYZData({
          'first.hasBadge': hasGoods,
          'subFloatingNavs[0].hasBadge': hasGoods,
        });
      } else {
        this.setYZData({
          [`${type}List[${index}].hasBadge`]: hasGoods,
        });
      }
    },

    // 下拉刷新
    handlePullDownRefresh() {
      this.setYZData({
        active: false,
      });
      if (this.justAttached && this.pageType !== PAGE_TYPE_MAP.GOODS_DETAIL) {
        this.justAttached = false;
        return;
      }
      // 如果下拉的不是组件所在的页面，则直接返回
      if (
        !this.iconController ||
        this.iconController.id !== getCurPage().__wxExparserNodeId__
      ) {
        return;
      }

      this.off(EVENT_MAP.ADD_CART, this.handleAddCart); // 移除添加购物车的监听

      Promise.all([
        source.getNavData(this.pageType, true),
        ...iconManager.getExtraIconList(this.pageType),
      ]).then((data) => {
        this.processNavData([data[0], this.data.isTab], true); // 获取新数据
      });
    },

    // 对子窗口处理，便于页面展示
    getComputedSubNavList(subFloatingNavs) {
      const normalList = []; // 普通子窗口
      const independentList = []; // 独立子窗口
      let isLine = false; // 是否竖排展开
      this.whereIsCart = null;

      subFloatingNavs.forEach((nav) => {
        nav.iconUrl = this.getCdnImageUrl(nav.iconUrl); // 压缩图片
        const title = this.getItemTitle(nav);
        nav.customeTitle = title;
        if (nav.independent) {
          independentList.unshift(nav);
        } else {
          normalList.push(nav);
        }
      });

      if (normalList.length <= CRITICAL_NUM) {
        normalList.reverse();
        isLine = true;
      }

      normalList.concat(independentList).some((nav, index) => {
        if (nav.navType === TYPE_MAP.CART) {
          this.whereIsCart = nav.independent
            ? ['independent', index - normalList.length]
            : ['normal', index];
          return true;
        }
        return false;
      });

      return { normalList, independentList, isLine };
    },

    // 获取占位的数量
    getSkeletonNum(normalList, isLine) {
      if (isLine) {
        return 0;
      }

      const num = 3 - (normalList.length % 3);
      return num === 3 ? 0 : num;
    },

    // 获取未展开时页面可见的悬浮窗数量，用于调整分销员 icon 的位置
    getVisibleNum(normalList, independentList) {
      let num = independentList.length;

      if (normalList.length > 0) {
        num++;
      }

      return num;
    },

    getCdnImageUrl(url) {
      return cdnImage(url, '!150x150.png');
    },

    // 获取页面类型
    getPageType(url) {
      let pageType = 0;
      // 当前页面是 TAB 容器
      if (TAB_CONTAINER_RULE.test(url)) {
        // 获取当前tab页挂载页面的真实路径
        url = getCurPage().attachedPage?.is;
      }
      Object.keys(URL_PATTERN_MAP).some((key) => {
        return URL_PATTERN_MAP[key].some((p) => {
          if (p.test(url)) {
            pageType = key;
            return true;
          }
          return false;
        });
      });

      return Number(pageType);
    },

    // 获取基础位置
    getBasePosition(isTab, isIphoneX) {
      // 模拟导航bottom要正常，无需判断
      // if (isTab) {
      //   return 30;
      // }
      if (isIphoneX) {
        return 114;
      }
      return 80;
    },

    scrollHandler(ev) {
      if (!this.__handleScroll) {
        this.__handleScroll = throttle(this.handleScroll, 300);
      }
      this.__handleScroll(ev);
    },

    handleScroll(e) {
      const { scrollTop } = e;
      const { active, hidden } = this.data;
      const offset = scrollTop - this.lastScrollTop;

      if (scrollTop <= 50) {
        this.lastScrollTop = scrollTop;
        hidden && this.setYZData({ hidden: false });
        return;
      }

      if (!hidden) {
        if (offset < 0) {
          this.lastScrollTop = scrollTop;
        } else if (offset > 50) {
          !hidden && this.setYZData({ hidden: true });
          active && this.changeActive(false);
          this.lastScrollTop = scrollTop;
        }
      } else {
        this.lastScrollTop = scrollTop;
        if (offset < 0 && hidden) {
          this.setYZData({ hidden: false });
        }
      }
    },
    getItemTitle(item) {
      try {
        if (TYPE_MAP.CUSTOME.includes(item.navType)) {
          const extraData = JSON.parse(item.extraData);
          const { title } = extraData;
          return title;
        }
      } catch (error) {
        return '';
      }
    },
  },
});
