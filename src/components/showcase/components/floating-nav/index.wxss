.theme-floating-nav {
  position: fixed;
  bottom: 80px;
  right: 10px;
  width: 48px;
  height: 48px;
  z-index: 140; /** z-index 顺序 修改请查看 docs/z-index.md **/
  transition: all 0.24s;
}

.is-iphone-x {
  bottom: 114px;
}

.is-tab {
  bottom: 30px;
}

.hidden {
  transform: translate3d(100px, 0, 0);
}

/*
==== 子窗口
*/
.item {
  position: absolute;
  top: 0;
  left: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.16);
  transition: all 0.24s;
  overflow: visible;
}

.item::after {
  display: none;
}

.main {
  z-index: 1;
}

.main-2 {
  opacity: 0;
  visibility: hidden;
  transform: rotate(-90deg);
}

.sub {
  opacity: 0;
  visibility: hidden;
}

/*
==== 独立子窗口
*/
.independent {
  opacity: 1;
  visibility: visible;
}

.line--only-independent .independent {
  margin-top: 58px;
}

/*
==== 占位符
*/
.skeleton {
  display: none;
}

/*
==== 角标
*/
.has-badge::before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 10px;
  right: 10px;
  width: 8px;
  height: 8px;
  border: 1px solid white;
  border-radius: 50%;
  background-color: #f44;
}

.tag {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  border-radius: 8px;
  background-color: #ff4240;
  color: #fff;
  font-size: 10px;
  line-height: 16px;
  text-align: center;
}

/*
==== 竖排展开
*/
.active .main-1 {
  opacity: 0;
  visibility: hidden;
  transform: rotate(90deg);
}

.active .main-2 {
  opacity: 1;
  visibility: visible;
  transform: none;
}

.active .sub {
  opacity: 1;
  visibility: visible;
}

/*
==== 面板弹窗
*/
.panel {
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  position: fixed;
  bottom: 100px;
  left: 50%;
  margin-left: -208rpx;
  width: 450rpx;
  padding: 20rpx 8rpx 0;
  border-radius: 24px;
  background-color: rgba(0, 0, 0, 0.7);
  opacity: 0;
  visibility: hidden;
  transform: translate3d(36vw, 158rpx, 0) scale3d(0, 0, 1);
  z-index: 3;
  transition: all 0.2s;
}

.panel--three-rows {
  width: 456rpx;
  margin-left: -228rpx;
  padding: 16rpx 8rpx 6rpx;
}

.is-iphone-x .panel {
  bottom: 134px;
}

.is-tab .panel {
  bottom: 50px;
}

.panel-item {
  flex: 1 1 30%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-width: 30%;
  margin-bottom: 20rpx;
  height: 128rpx;
  background: transparent;
  font-size: 12px;
  line-height: 16px;
  color: white;
}

.panel-item-wrap{
  flex: 1 1 30%;
}

.panel-item::after {
  display: none;
}

.panel-item .item {
  position: relative;
  width: 72rpx;
  height: 72rpx;
  margin-bottom: 10rpx;
  box-shadow: none;
}

.panel-item .has-badge::before {
  top: 6px;
  right: 6px;
}

.outer-space {
  position: fixed;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
}

/*
==== 面板展开
*/
.active .panel {
  opacity: 1;
  visibility: visible;
  transform: none;
}

.active .outer-space {
  width: 100%;
  height: 100%;
  z-index: 2;
}

/* 自定义商品橱窗icon样式 */
.goods-showcase-icon {
  background-color: transparent;
  box-shadow: none;
}

.share-share-corner {
  width: 20px;
  height: 16px;
  background: #ee0a24;
  border-radius: 8px;
  position: absolute;
  top: -1px;
  right: 0;
  color: #fff;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
