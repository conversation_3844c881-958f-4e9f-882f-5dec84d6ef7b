.placeholder-class {
  width: 100%;
}
.picker {
  position: fixed;
  bottom: -400px;
  width: 100vw;
  left: 0;
  background: #fff;
  z-index: 999;
  display: flex;
  flex-direction: column;
  height: 0;
  transition: height ease-in 0.3s;
}
.picker-visible {
  height: 236px;
  bottom: 0;
}

.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 236px;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}
.picker .picker-action {
  height: 36px;
  border-bottom: 1rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}
.picker-action view:last-child {
  color: #1aad16;
}
.picker-cols-box {
  background: linear-gradient(to bottom, #efefef, #fff 47%, #efefef 100%);
}
.picker .picker-cols {
  display: flex;
  flex: 1;
  margin: 10px 0;
  padding: 0 5px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  height: 180px;
  background: #fff;
}
.picker-cols .fixed-col {
  color: #999;
  margin-left: 5px;
  height:100%;
  line-height:180px;
}
.picker-cols .col {
  transform-origin: center;
  font-size: 14px;
}
.picker-cols .col .select-item {
  color: #333;
  font-size: 16px;
}
.picker-cols .col {
  flex: 1;
  text-align: right;
  color: #aaa;
  height: 180px;
  position: relative;
  padding-top: 79px;
}
.picker-cols .col .cell {
  height: 36px;
}
.picker-cols::after, .picker-cols::before {
  content: '';
  position: absolute;
  height: 72px;
  width: calc(100% - 10px);
  z-index: 1000;
  pointer-events: none;
}
.picker-cols::before {
  top: 0;
  border-bottom: 1rpx solid #e5e5e5;
}
.picker-cols::after {
  bottom: 0;
  border-top: 1rpx solid #e5e5e5;  
}
