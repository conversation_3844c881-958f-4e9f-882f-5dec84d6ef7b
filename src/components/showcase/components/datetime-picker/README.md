## datetime-picker

使用 picker 组件开发的时间日期组件，弥补小程序 picker 自身对于快速时间选择的不支持

### 示例代码

```
  <date-picker
    bindchange="change"  
    bindcancel="cancel"
    placeholder="请选择一个时间"
    placeholder-class="my-customer-class-name"
    format="你选择了YYYY年MM月DD日HH点mm分ss秒" />
```

> 该组件使用自定义组件开发，怎么用大家懂得

### 属性与事件

| 名称              | 类型      | 是否必须 | 默认                | 描述                                                                                                  |
| ----------------- | --------- | -------- | ------------------- | ----------------------------------------------------------------------------------------------------- |
| placeholder-class | `String`  | `否`     | 无                  | 自定义类，可改变 placeholder 样式，其他类无效                                                         |
| placeholder       | `String`  | `否`     | 请选择时间          | 设置 picker 的 placeholder                                                                            |
| not-use           | `Array`   | `否`     | 无                  | 不需要显示的列 可选择`years`, `months`, `days`, `hours`, `minutes`, `seconds`中的多个                 |
| native            | `Boolean` | `否`     | 无                  | 使用原生 picker，还是自定义的 picker（自定义 picker 滚动不如原生）                                    |
| format            | `String`  | `否`     | YYYY-MM-DD HH:mm:ss | 设置选中的时间显示的格式，支持 _YYYY，yyyy，YY，yy，MM，M，DD，dd，D，d，HH， hh，H，h，mm，m，ss，s_ |
| bindchange        | `String`  | `是`     | 无                  | 用户点击`确认`触发该事件，返回值为按“年，月，日，时，分，秒”顺序的数组，可以通过`detail.value`获取    |
| bindcancel        | `String`  | `否`     | 无                  | 用户点击`取消`触发该事件                                                                              |
