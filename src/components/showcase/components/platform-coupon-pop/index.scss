.cross-store-coupon-dialog {
  max-width: 311px;

  &__content {
    font-size: 14px;
    line-height: 20px;
  }

  &__message {
    max-height: 60vh;
    overflow-y: auto;
    white-space: pre-wrap;
    text-align: center;
    padding: 8px 24px 16px;
    color: #323233;
  }

  &__btn-wrapper {
    position: relative;
    height: auto;
    padding: 0 24px 16px;
    color: #fff;
  }

  &__btn-confirm {
    width: 100%;
    border: none;
    border-radius: 999px;
    height: 36px;
    font-weight: 500;
    background: linear-gradient(to right, #ff6034, #ee0a24);
    text-align: center;
    line-height: 36px;
  }

  &__link {
    color: #646566;
    text-align: center;
    margin-bottom: 16px;
  }

  .van-dialog__footer {
    display: none;
  }
}
.dialog-pop {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 10000;
  background: rgba($color: #000000, $alpha: 0.7);
  display: flex;
  justify-content: center;

  &-title {
    padding-top: 24px;
    height: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    text-align: center;
  }
}
.dialog-pop-content {
  margin-top: 260px;
  width: 311px;
  height: 168px;
  background: #FFFFFF;
  border-radius: 16px;  
}
.mobile-dialog__content{
  &-text {
    height: 20px;
    font-size: 14px;
    color: #7D7E80;
    line-height: 20px;
    margin-top: 8px;
    text-align: center;
  }

  &-button {
    border-top: 1px solid #EBEDF0;
    box-sizing: border-box;
    margin-top: 44px;
    height: 48px;

    &-cancel, &-confirm {
      display: inline-block;
      width: calc((100% - 1px) / 2);
      font-size: 16px;
      line-height: 48px;
      vertical-align: top;
      text-align: center;
    }

    &-split {
      display: inline-block;
      width: 1px;
      height: 48px;
      background: #EBEDF0;
    }

    &-cancel {
      color: #323233;
    }

    &-confirm {
      color: #EE0A24;
    }
  }
}