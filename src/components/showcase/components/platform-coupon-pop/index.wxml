<van-dialog
  class="cross-store-coupon-dialog"
  use-slot
  show="{{ crossDialogShow }}"
  bind:close="onClickDialogConfirm"
  title="{{crossDialogTitle}}"
  show-confirm-button="{{false}}"
  close-on-click-overlay
>
  <view class="cross-store-coupon-dialog__content"> 
    <view class="cross-store-coupon-dialog__message">添加的优惠券可在“个人中心 - 优惠券 / 码”中领取后使用</view>
    <view class="cross-store-coupon-dialog__btn-wrapper">
      <view class="cross-store-coupon-dialog__btn-confirm" bind:tap="onClickDialogConfirm">
        继续逛逛
      </view>
    </view>
    <view class="cross-store-coupon-dialog__link" bind:tap="viewCoupon">查看优惠券</view>
  </view>
</van-dialog>

<view class="dialog-pop" wx:if="{{ authDialogShow }}">
  <view class="dialog-pop-content">
    <view class="dialog-pop-title">授权提示</view>
    <view class="mobile-dialog__content">
      <view class="mobile-dialog__content-text">完成授权后，即可添加优惠券</view>
      <view class="mobile-dialog__content-button">
        <view class="mobile-dialog__content-button-cancel" bind:tap="onAuthClose">暂不授权</view>
        <view class="mobile-dialog__content-button-split"></view>
        <user-authorize
          class="mobile-dialog__content-button-confirm"
          authTypeList="{{ ['mobile'] }}"
          bind:fail="onAuthClose"
          bind:next="onAuthSuccess"
        >
          <view>去授权</view>
        </user-authorize
      </view>
    </view>
  </view>
</view>