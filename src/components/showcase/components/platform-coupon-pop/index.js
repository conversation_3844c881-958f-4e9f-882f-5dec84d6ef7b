import Event from '@youzan/weapp-utils/lib/event';
import getAuthorizedState from 'shared/utils/get-authorized-state';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import args from '@youzan/weapp-utils/lib/args';
import navigate from 'shared/utils/navigate';
import { node as request } from 'shared/utils/request';

Component({
  properties: {},

  data: {
    crossDialogShow: false,
    crossDialogTitle: '已经添加至我的优惠券',
    authDialogShow: false,
    tempAlias: '',
  },

  attached() {
    Event.on('feature-platform-coupon:open', (coupon) => {
      const camelCoupon = mapKeysCase.toCamelCase(coupon);
      const {
        promoterResourceAlias = '',
        linkType = '',
        linkUrl = '',
      } = camelCoupon;
      let alias = '';
      if (linkType === 'platform_coupon') {
        alias = (args.getAll(linkUrl) || {}).promoterResourceAlias;
      } else {
        alias = promoterResourceAlias;
      }
      getAuthorizedState().then((data) => {
        if (data.mobile) {
          this.touchPlatformCoupon(alias);
        } else {
          this.setData({
            authDialogShow: true,
            tempAlias: alias,
          });
        }
      });
    });
  },

  methods: {
    computedDialogTitle(isReceived) {
      this.setData({
        crossDialogTitle: isReceived
          ? '你已添加过该优惠券'
          : '已经添加至我的优惠券',
        crossDialogShow: true,
      });
    },
    onAuthClose() {
      this.setData({
        authDialogShow: false,
      });
    },
    onAuthSuccess() {
      this.setData({
        authDialogShow: false,
      });
      this.touchPlatformCoupon(this.data.tempAlias);
    },
    touchPlatformCoupon(alias) {
      const data = {
        operator: {},
        alias,
      };

      request({
        path: '/wscdeco/ump/platformCoupon/touchCoupon.json',
        method: 'POST',
        data,
      })
        .then((res) => {
          const { code, msg } = res;
          if (!code) {
            // 成功领取
            this.computedDialogTitle(false);
          } else if (code === 349303005) {
            // 已领取
            this.computedDialogTitle(true);
          } else {
            wx.showToast({
              title: msg || '未知错误',
              icon: 'none',
            });
          }
        })
        .catch((err) => {
          wx.showToast({
            title: err || '未知错误',
            icon: 'none',
          });
          this.setData({
            tempAlias: '',
          });
        });
    },
    viewCoupon() {
      const platformCouponlistPageUrl =
        '/packages/user/mediator-coupon/list/index';
      navigate.navigate({
        url: platformCouponlistPageUrl,
      });
      this.onClickDialogConfirm();
    },
    onClickDialogConfirm() {
      this.setData({
        crossDialogShow: false,
        crossDialogTitle: '已经添加至我的优惠券',
        tempAlias: '',
      });
    },
  },
});
