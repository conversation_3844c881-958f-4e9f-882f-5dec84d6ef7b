<!-- 分享遮罩层 -->
<van-popup
  wx:if="{{ showWhitePop }}"
  show="{{ showModal }}"
  custom-style="{{ popStyle }}"
  custom-class="share-image__popup is-white"
>
  <view class="share-image__container is-white" style="{{ containerStyle }}">
    <van-icon 
      name="close" 
      size="24" 
      class="share-image-white__close-btn" 
      bind:click="closeShareImageModal"
    />
    <image
      src="{{ src }}"
      style="{{ posterStyle }}"
      class="share-image--preview is-white"
    />
    <van-button custom-class="share-image-white__btn" color="#fff" bind:click="clickSaveImage">保存海报</van-button>
  </view>
</van-popup>
<van-popup
  wx:else
  show="{{ showModal }}"
  custom-class="share-image__popup"
>
  <view class="share-image__container">
    <view class="share-image__close-btn" bindtap="closeShareImageModal" />
    <image
      src="{{ src }}"
      class="share-image--preview"
    />
    <!-- <view class="share-image__info">保存图片后，可分享到朋友圈</view> -->
    <van-button custom-class="share-image__btn" type="danger" bind:click="clickSaveImage">保存图片</van-button>
  </view>
</van-popup>

<van-action-sheet
  round
  show="{{ sheet.show }}"
  overlay="{{ sheet.overlay }}"
  close-on-click-overlay="{{ sheet.closeOnClickOverlay }}"
  bind:close="closeActionSheet"
  bind:cancel="closeActionSheet" >
  <view>
    <share-activity-sheet-item
      feature-alias="{{ featureAlias }}"
      bind:update-share-tag="updateShareTag"
      bind:update-share-status="updateShareStatus"
    />
    <button
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--bottom"
      open-type="share"
      data-share-tag="{{ shareTag }}"
      data-activity-alias="{{ activityAlias }}"
      bind:tap="onShareCallback"
    >
      发送给朋友
    </button>
    <button
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--bottom"
      bind:tap="onPosterCallback"
    >
      生成海报
    </button>
  </view>
  <view
    class="action-sheet__cancel"
    style="padding-bottom: {{ paddingBottom }}px"
    hover-class="action-sheet__cancel--hover"
    hover-stay-time="70"
    bind:tap="closeActionSheet"
  >
    取消
  </view>
</van-action-sheet>
