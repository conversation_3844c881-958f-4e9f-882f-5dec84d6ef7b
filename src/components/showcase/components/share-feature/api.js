/* eslint-disable */
import { node as request } from 'shared/utils/request';
/* eslint-enable */

const app = getApp();

let shareActivityCache = {};
let shareActivityTag = {};

export const getPoster = (data) => {
  return request({
    path: '/wscshop/poster/feature.json',
    data: { ...data, isV2: 1 },
  });
};

export const getShareTag = (data) => {
  if (
    shareActivityTag.activityAlias === data.activityAlias &&
    shareActivityTag.data
  ) {
    return Promise.resolve(shareActivityTag.data);
  }
  return request({
    path: '/wscdeco/ump/activity/get-share-tag.json',
    method: 'POST',
    data,
  }).then((res) => {
    if (res) {
      shareActivityTag = {
        activityAlias: data.activityAlias,
        data: res,
      };
      return res;
    }
  });
};

export const getShareActivity = (data) => {
  if (shareActivityCache.kdtId === app.getKdtId() && shareActivityCache.data) {
    return Promise.resolve(shareActivityCache.data);
  }
  return request({
    path: '/wscdeco/ump/activity/get-share-activity.json',
    data,
  }).then((res) => {
    if (res) {
      shareActivityCache = {
        kdtId: app.getKdtId(),
        data: res,
      };
      return res;
    }
  });
};

export const joinShowcaseShareActivity = (data) => {
  return request({
    path: '/wscdeco/ump/activity/join-share-activity.json',
    method: 'POST',
    data,
  });
};

// 获取scene字符串
export const getUltraCode = (data) => {
  return request({
    path: '/wscshop/showcase/share/getUltraCode.json',
    data,
  });
};
