@import "helpers/index.wxss";

.share-image__container {
  position: absolute;
  height: 506px;
  width: 267px;
  border-radius: 4px;
  background: transparent;
}

.share-image__popup {
  width: 277px;
  height: 529px;
  overflow: hidden;
  background: transparent !important;
  padding: 11px;
  z-index: 110;
}

.share-image__close-btn {
  position: absolute;
  right: 3px;
  top: -10px;
  height: 26px;
  width: 26px;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, .2);
}

.share-image__close-btn::after,
.share-image__close-btn::before {
  content: '';
  width: 13px;
  height: 1px;
  background: #999;
  position: absolute;
  left: 50%;
  top: 50%;
}

.share-image__close-btn::after {
  transform: translateX(-50%) translateY(-50%) rotateZ(45deg);
}

.share-image__close-btn::before {
  transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
}

.share-image--preview {
  display: block;
  height: 433px;
  width: 257px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, .2);
}

.share-image__info {
  font-size: 12px;
  line-break: 17px;
  margin-bottom: 10px;
  text-align: center;
  color: #999999;
}

.share-image__btn {
  margin: 20px 0;
  width: 257px;
  height: 45px;
}

@media screen and (max-width:360px) {
  .share-image__container {
    transform: scale(.8) translate3d(-50%, -50%, 0);
    left: 40%;
    top: 40%;
  }
}

.action-sheet__cancel, .action-sheet__item {
  height:50px;
  font-size:16px;
  line-height:50px;
  text-align:center;
  background-color:#fff
}
.action-sheet__cancel--hover, .action-sheet__item--hover {
  background-color:#f2f3f5
}
.action-sheet__cancel {
  height:60px
}
.action-sheet__cancel:before {
  display:block;
  height:10px;
  background-color:#f8f8f8;
  content:" "
}

@media screen and (max-height: 800px) {
  .share-image__popup.is-white {
    padding: 150rpx 0 0 !important;
    overflow-y: auto;
  }
}

.share-image__popup.is-white {
  padding: 72rpx 0 0;
  width: 560rpx;
}
.share-image__container.is-white,
.share-image--preview.is-white {
  width: 560rpx;
}

.share-image-white__close-btn {
  position: absolute;
  right: 0;
  top: 0;
  transform: translateY(-150%);
  color: white;
}

.share-image-white__btn {
  margin: 48rpx 0 0 50rpx;
  width: 460rpx;
  height: 96rpx;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 60rpx;
  color: #333 !important;
}
