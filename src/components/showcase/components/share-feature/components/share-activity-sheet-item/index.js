import WscComponent from 'pages/common/wsc-component/index';
import getAuthorizedState from 'shared/utils/get-authorized-state';
import navigate from '@/helpers/navigate';
import { getShareTag, getShareActivity } from '../../api';
import args from '@youzan/weapp-utils/lib/args';

const app = getApp();
WscComponent({
  properties: {
    featureAlias: {
      type: String,
    },
  },
  data: {
    activityInfo: {},
    hasShareActivity: false,
    hasLogin: false,
  },

  attached() {
    getShareActivity().then((res) => {
      if (res && res.activityAlias) {
        this.setData(
          {
            activityInfo: res,
            hasShareActivity: true,
          },
          this.getShareTag
        );
      }
    });
  },
  methods: {
    onLoginClick() {
      const { activityAlias } = this.data.activityInfo;
      this.setData({
        hasLogin: true,
      });
      getShareTag({
        activityAlias,
      }).then((res) => {
        const { alias: shareTag } = res;
        this.triggerEvent('update-share-tag', { shareTag });
      });
    },
    getShareTag() {
      this.triggerEvent('update-share-status', {
        hasShareActivity: !!this.data.activityInfo.activityAlias,
      });
      getAuthorizedState().then((data) => {
        const hasLogin = !!data.mobile;
        if (hasLogin) {
          this.onLoginClick();
        }
        this.setData({
          hasLogin,
        });
      });
    },
    // 跳转活动规则详情页
    goDetailPage() {
      const { activityId } = this.data.activityInfo;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage.route;
      const params = {
        activityId,
        kdt_id: app.getKdtId(),
      };

      const isHome = +/(packages|pages)\/home\/dashboard/.test(currentRoute);
      if (isHome) {
        params.isHome = isHome;
      } else {
        params.alias = this.data.featureAlias;
      }
      const url = args.add('/packages/ump/share-benefit/index', params);
      navigate.navigate({ url });
    },
  },
});
