<view class="share-activity" wx:if="{{ hasShareActivity }}">
  <!-- 分享有礼活动展示 -->
  <view class="share-activity__main">
    <van-icon
      size="18px"
      color="#DE373E"
      name="https://b.yzcdn.cn/public_files/4be833507d5d019bc3fde956b2588392.png"
    />
    <text class="share-activity__main-text">
      分享{{ activityInfo.shareNum }}位好友可得
      <text class="share-activity__text--red">{{ activityInfo.rewardText }}</text>
      {{ activityInfo.rewardName }}
    </text>
  </view>
  <view class="share-activity__action">
    <text
      wx:if="{{ hasLogin }}"
      class="share-activity__action-desc"
      bindtap="goDetailPage"
    >
      {{ activityInfo.rewardTimesLimitText ? activityInfo.rewardTimesLimitText + '，' : '' }}查看详情
    </text>
    <user-authorize
      wx:else
      authTypeList="{{ ['mobile'] }}"
      bind:next="onLoginClick"
    >
      <text class="share-activity__action-login">立即登录</text>
    </user-authorize>
    <van-icon size="14px" color="#969799" name="arrow" />
  </view>
</view>
