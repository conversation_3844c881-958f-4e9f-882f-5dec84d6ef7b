<van-popup
  wx:if="{{ init }}"
  show="{{ showPop }}"
  round
  z-index="{{ 900 }}"
  custom-class="share-activity-pop"
  custom-style="height: {{safeBottom ? 300 : 265}}px;"
  position="bottom"
  bind:close="onClosePop"
>
  <view class="share-activity-pop__header">登录提示</view>
  <view class="share-activity-pop__main">
    你还没有登录，登录后浏览商品好友可获得分享奖励，你也可以分享获得好礼！
  </view>
  <user-authorize
    authTypeList="{{ ['mobile'] }}"
    bind:fail="onClosePop"
    bind:next="joinActivity"
  >
    <theme-view
      custom-class="share-activity-pop__btn"
      block
      bg="main-bg"
      color="main-text"
    >
      立即登录
    </theme-view>
  </user-authorize>
  <view class="share-activity-pop__bottom" bind:tap="onClosePop">
    暂不登录，先逛逛
  </view>
</van-popup>