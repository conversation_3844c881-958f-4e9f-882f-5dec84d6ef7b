import WscComponent from 'pages/common/wsc-component/index';
import getAuthorizedState from 'shared/utils/get-authorized-state';
import { joinShowcaseShareActivity, getShareActivity } from '../../api';
import tabBehavior from '@/custom-tab-bar-v2/tab-behavior';

let hasJoin = false;
const app = getApp();

WscComponent({
  behaviors: [tabBehavior],
  properties: {
    shareTag: {
      type: String,
      obsever(val) {
        val && this.initShareActivity();
      }
    },
  },
  data: {
    init: false,
    showPop: false,
    popHeight: '',
    activityAlias: '',
    activityId: '',
  },
  attached() {
    getShareActivity().then(res => {
      res && this.setData({
        activityAlias: res.activityAlias,
        activityId: res.activityId
      }, this.initShareActivity)
    })
  },
  methods: {
    initShareActivity() {
      const { shareTag } = this.data;
      shareTag && getAuthorizedState().then(data => {
        if (data.mobile) {
          this.joinActivity();
        } else {
          this.setData({
            init: true,
            showPop: true,
          });
        }
      });
    },
    joinActivity() {
      const { shareTag, activityAlias, activityId } = this.data;
      if (!hasJoin) {
        joinShowcaseShareActivity({
          activityAlias,
          shareTag
        }).then(() => {
          hasJoin = true;
          wx.showToast({
            title: '已帮好友获得奖励，分享即得好礼！',
            duration: 5000
          });
        }).catch(() => this.onClosePop);
        app.logger && activityId
          && app.logger.log({
              et: 'custom', // 事件类型
              ei: 'visit_share_activity_showcase', // 事件标识
              en: '被分享人访问微页面', // 事件名称
              params: { 
                activityId,
                shareTag,
              }
            })
      }
      this.onClosePop && this.onClosePop();
    },
    onClosePop() {
      this.setData({
        showPop: false,
      });
    }
  }
});