import { TAB_CONTAINER_RULE } from '@youzan/wsc-tab-bar-utils/lib/constants';
import findTabBarIndex from '@youzan/wsc-tab-bar-utils/lib/weapp/findTabBarIndex';

import WscComponent from 'pages/common/wsc-component/index';
import logv3 from 'utils/log/logv3';
import useWxImageShare from '@youzan/decorate-tee/src/common/utils/use-wx-image-share';
import authorize from '@/helpers/authorize';
import { getPoster, getUltraCode } from './api';
import tabBehavior from '@/custom-tab-bar-v2/tab-behavior';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';

const app = getApp();
const WHITE_POP_IMG_WIDTH = 560;
const WHITE_POP_BTN_HEIGHT = 144; // 按钮高度 + marginTOP;
const WHITE_POP_PADDING_TOP = 72; // 弹框padding 顶部

// 获取当前页面
function getCurPage() {
  const ps = getCurrentPages();
  return ps[ps.length - 1];
}

WscComponent({
  behaviors: [tabBehavior],

  properties: {
    showSharePop: {
      type: Boolean,
      value: false,
      observer(newValue) {
        this.showSheet(newValue);
      },
    },
    pageType: {
      type: String,
      value: '',
    },
    salesman: {
      type: Object,
      value: {},
    },
    featureAlias: {
      type: String,
    },
    noBottom: {
      type: Boolean,
      default: false,
    },
    activity: {
      type: Object,
      default: {},
    },
  },

  data: {
    // 控制预览蒙层是否展示
    showModal: false,
    // 海报图片地址
    src: '',
    sheet: {
      show: false,
      overlay: true,
      cancelText: '取消',
      closeOnClickOverlay: true,
      actions: [
        {
          name: '发送给朋友',
          openType: 'share',
        },
        {
          name: '生成海报',
        },
      ],
    },
    // 分享有礼分享标识
    shareTag: '',
    paddingBottom: 0,
    // 使用微信图片分享功能
    useWxImageShare: false,
    kdtId: undefined,
    pagePath: undefined,
    // 弹层样式
    popStyle: '',
    // 容器样式
    containerStyle: '',
    // 海报图片样式
    posterStyle: '',
    // 是否是白色主题的的弹框
    showWhitePop: false,
  },

  attached() {
    if (!this.data.noBottom) {
      app.isSwitchTab().then((isTabPage) => {
        this.setData({
          paddingBottom: isTabPage ? 49 : 0,
        });
      });
    }

    const { chainStoreInfo = {} } = app.getShopInfoSync();
    const { isMultiOnlineShop } = chainStoreInfo;
    const _kdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId();
    const kdtId = (app.getAppKdtId && app.getAppKdtId()) || _kdtId;
    this.setData({ kdtId });

    useWxImageShare({ kdtId }).then((res = {}) => {
      this.setData({
        useWxImageShare: res,
      });
    });

    // 如果是活动预定
    getApp().globalData.eventBusPoster = this.onPosterCallback.bind(this);
  },

  detached() {
    getApp().globalData.eventBusPoster = null;
  },

  methods: {
    // 控制 sheet 的显示
    showSheet(value) {
      this.setData({
        'sheet.show': value,
        ...(value && { 'sheet.overlay': true }),
      });
    },

    // 关闭 sheet
    closeActionSheet() {
      this.setData({
        'sheet.show': false,
      });
      this.triggerEvent('finished');
    },

    onShareCallback() {
      this.setData({
        'sheet.overlay': false,
      });
      this.triggerEvent('finished');
    },

    onPosterCallback() {
      wx.showLoading({ title: '正在生成' });
      this.doGetPoster(true);
    },

    // 设置活动预定海报相关样式
    setReservePosterStyle({ width, height }) {
      const scale = width / WHITE_POP_IMG_WIDTH;
      const whitePopImgHeight = height / scale;
      const containerHeight = whitePopImgHeight + WHITE_POP_BTN_HEIGHT;
      this.setData({
        popStyle: `height: ${containerHeight + WHITE_POP_PADDING_TOP}rpx`,
        containerStyle: `height: ${containerHeight}rpx`,
        posterStyle: `height: ${whitePopImgHeight}rpx`,
      });
    },

    doGetPoster(needRetry = false, retry = 0) {
      const {
        src,
        pageType,
        featureAlias,
        shareTag,
        kdtId,
        pagePath,
        useWxImageShare,
        activity,
      } = this.data;

      // 已经生成过图片了
      if (src) {
        wx.hideLoading();

        this.showPoster(src, pagePath);
        return;
      }

      const guestKdtId = app.getKdtId();
      const page = getCurPage();
      const alias =
        (pageType === 'micropage' && featureAlias) || page.options.alias || '';

      let path = page.route.split('?')[0];
      const dcPs = logv3.getDCPS();
      const offlineId = app.getOfflineId();

      if (TAB_CONTAINER_RULE.test(path)) {
        // 分享真实导航路径
        const list = getApp().globalData.tabbarOriginList || [];
        const pathConfig = list[findTabBarIndex()];
        const { pagePath } = pathConfig || {};
        path = pagePath || path;
      }
      let scene = {
        page: path,
        guestKdtId,
        kdtId,
        dcPs,
        offlineId,
        alias,
        ...defaultEnterShopOptions, // 添加进店标
      };

      // 分销员逻辑
      const { salesman = {} } = this.data;
      const { seller: sls } = salesman;

      if (sls) {
        scene = {
          ...scene,
          ...getSalesmanParamsObject({ sls }),
        };
      }

      if (activity) {
        scene = { ...scene, ...activity };
      }

      if (shareTag) {
        scene = {
          ...scene,
          shareTag,
        };
      }

      const pageUrl = 'pages/common/blank-page/index';
      const promises = [
        getPoster({
          retry,
          kdtId: guestKdtId,
          alias,
          pageType,
          page: pageUrl,
          scene,
          activity,
        }),
      ];
      if (useWxImageShare) {
        promises.push(
          getUltraCode({
            page: pageUrl,
            scene,
          })
        );
      }

      Promise.all(promises)
        .then(([poster, code]) => {
          wx.hideLoading();
          const { value: src, theme, height, width } = poster;
          const pagePath = code?.scene
            ? `${pageUrl}?scene=${code.scene}`
            : undefined;

          this.setReservePosterStyle({ width, height });
          this.setData({
            src,
            showWhitePop: theme === 'white',
            pagePath,
          });
          this.showPoster(src, pagePath);
        })
        .catch(() => {
          if (needRetry) {
            setTimeout(() => {
              this.doGetPoster(false, 1);
            }, 1000);
          } else {
            wx.showToast({
              title: '生成卡片失败',
              icon: 'none',
              duration: 2000,
            });
            this.closeShareImageModal();
            this.closeActionSheet();
          }
        });
    },

    // 关闭蒙层
    closeShareImageModal() {
      this.setData({
        showModal: false,
      });
    },

    // 点击保存（ 成功后自动关闭弹层，抛出 saved 事件 ）
    clickSaveImage() {
      const { src } = this.data;
      if (!src) return;

      wx.showLoading({ title: '保存中' });
      authorize('scope.writePhotosAlbum')
        .then(() => {
          this.saveShareImage(src)
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000,
              });
              this.closeShareImageModal();
            })
            .catch(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000,
              });
            });
        })
        .catch(() => {
          wx.hideLoading();
          wx.showToast({
            title: '请允许访问相册后重试',
            icon: 'none',
            duration: 1000,
          });
          setTimeout(() => {
            wx.openSetting({
              success: ({ authSetting }) => {
                if (authSetting['scope.writePhotosAlbum']) {
                  this.clickSaveImage();
                }
              },
            });
          }, 1000);
        });
    },

    // 微页面图文卡片保存
    saveShareImage(tempFilePath) {
      return new Promise((resolve, reject) => {
        app.downloadFile({
          url: tempFilePath,
          success(res) {
            if (res.statusCode === 200) {
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: resolve,
                fail: reject,
              });
            } else {
              reject();
            }
          },
          fail: reject,
        });
      });
    },

    updateShareTag({ detail: { shareTag } }) {
      if (!shareTag) {
        return;
      }
      this.setData({
        shareTag,
      });
    },
    updateShareStatus() {
      this.triggerEvent('update-share-status');
    },

    showPosterModal() {
      this.setData(
        {
          showModal: true,
        },
        () => {
          this.triggerEvent('finished');
        }
      );
    },

    showPoster(url, entrancePath) {
      if (!url) return;
      const { useWxImageShare } = this.data;

      if (useWxImageShare) {
        wx.downloadFile({
          url,
          success: (r) => {
            this.closeActionSheet();
            wx.showShareImageMenu({
              path: r.tempFilePath,
              needShowEntrance: true,
              entrancePath,
            });
          },
          fail: () => {
            this.showPosterModal();
          },
        });
      } else {
        this.showPosterModal();
      }
    },
  },
});
