@import '~shared/common/css/_mixins.scss';

.timeline-block {
  position: relative;
  background: #fff;
  box-sizing: border-box;
  height: 100vh;

  &__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    background-color: #fff;
    text-align: center;
    border-radius: 10px;

    &.is-tab {
      margin-top: 20px;
    }
  }

  &__img {
    display: block;
    width: 100%;
    object-fit: contain;
    border-radius: 5px;
    background: #f7f8fa;
  }

  &__title {
    margin: 16px;
    font-size: 16px;
    line-height: 20px;
    margin-top: 16px;
    font-weight: 600;

    @include multi-ellipsis(2);
  }

  &__poptip {
    position: fixed;
    right: 16px;
    bottom: 10px;
    box-sizing: border-box;
    width: 236px;
    height: 58px;
    padding-bottom: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    color: #fff;
    background: url('https://img01.yzcdn.cn/public_files/0e5336ac8895958f32c2e556bbd5d10e.png');
    background-size: 100% 100%;
  }
}

.timeline-snapshotUrl-block {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;

  .logo {
    width: 60px;
    height: 84px;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    flex-shrink: 0;
    padding-top: 24px;
    box-sizing: border-box;

    .logo-img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
    }
  }
  .shop-name {
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    margin-top: 8px;
    color: #333;
    text-align: center;
    flex-shrink: 0;
  }

  .image-block-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .image-block {
    border: 1.5px solid #e0e0e0;
    margin-top: 24px;
    margin: 24px 36px 0 36px;
    position: relative;
    box-sizing: border-box;
    border-radius: 8.22px;
    width: calc(100% - 72px);
    overflow: hidden;
    flex: 1;

    .img {
      width: 100%;
      height: 100%;
    }
  }

  .white-bg {
    position: absolute;
    background: linear-gradient(
      359.93deg,
      #ffffff 35.01%,
      rgba(255, 255, 255, 0) 99.94%
    );
    height: 180px;
    width: 100%;
    bottom: 0;

    .tip {
      font-size: 12px;
      font-weight: 400;
      line-height: 17px;
      position: absolute;
      bottom: 12px;
      right: 12px;
      text-align: right;
      color: #666;
    }
  }

  .triangle-warp {
    width: 100%;
    height: 8px;
    position: relative;
    margin-bottom: 10px;

    .triangle {
      width: 10px;
      height: 10px;
      background-color: #fff;
      border: 1.5px solid #e0e0e0;
      border-top: none;
      border-left: none;
      transform: rotate(45deg);
      position: absolute;
      right: 55px;
      top: -6px;
      border-radius: 1px;
    }
  }
}
