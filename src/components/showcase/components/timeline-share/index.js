import { getTimeLineScreenshot, getTimelineShareData } from './api';

Component({
  properties: {
    alias: String,
    kdtId: String,
    isHomePage: Boolean,
    timelineSnapshotInfo: Object,
  },

  data: {
    title: '',
    image: '',
    snapshotUrl: '',
    shopName: '',
    logo: '',
    showHeight: 700,
  },

  attached() {
    const { alias, kdtId, isHomePage, timelineSnapshotInfo } = this.data;
    const { cacheKey } = timelineSnapshotInfo || {};
    const params = {
      alias,
      kdt_id: kdtId,
      pageType: isHomePage ? 'homepage' : 'micropage',
    };
    wx.showLoading();

    Promise.all([
      getTimelineShareData(params),
      cacheKey
        ? getTimeLineScreenshot({
            ...timelineSnapshotInfo,
            kdt_id: kdtId,
            otherParams: JSON.stringify({
              // operatorId: '22289',
            }),
          })
        : Promise.resolve({}),
    ])
      .then(([res, screenshot]) => {
        const {
          title = '',
          image = '',
          shopName = '',
          logo = '',
          shareInfo,
        } = res || {};
        this.setData({
          title,
          image,
          shopName,
          logo,
          snapshotUrl: shareInfo?.customizeStyle
            ? ''
            : screenshot?.img || image || '',
        });
        wx.hideLoading();
      })
      .catch((e) => {
        // 兜底
        console.log('getTimelineShareData error', e);
        wx.hideLoading();
        this.setData({
          image:
            'https://img01.yzcdn.cn/upload_files/2020/07/09/FhlA5R9G7Yw98g9DlgNclUuRZa3i.png',
        });
      });
  },
});
