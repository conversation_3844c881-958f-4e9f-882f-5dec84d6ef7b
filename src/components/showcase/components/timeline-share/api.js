export const getTimelineShareData = (data) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: 'https://h5.youzan.com/wscshop/weapp/share/feature/time_line_data.json',
      data: {
        ...data,
      },
      success: ({ data: res = {}, statusCode }) => {
        const { code, data = {} } = res;
        if (statusCode !== 200 || code !== 0) {
          reject('err');
        }
        resolve(data);
      },
      fail: () => {
        reject('err');
      },
    });
  });
};

export const getTimeLineScreenshot = (query) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: 'https://h5.youzan.com/wscshop/poster/async-feature-screenshot.json',
      data: {
        ...query,
      },
      success: ({ data: res = {}, statusCode }) => {
        const { code, data = {} } = res;
        if (statusCode !== 200 || code !== 0) {
          reject('err');
        }
        resolve(data);
      },
      fail: () => {
        reject('err');
      },
    });
  });
};
