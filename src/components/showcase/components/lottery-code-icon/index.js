import WscComponent from 'pages/common/wsc-component/index';
import navigate from 'shared/utils/navigate';
import pageScrollBehavior from 'shared/components/showcase/behaviors/page-scroll-control-behavior';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();

WscComponent({
  data: {
    number: 0,
    showArrow: true,
    btnText: '滑动浏览',
  },

  behaviors: [pageScrollBehavior],

  ready() {
    setTimeout(() => {
      this.setPageScrollControlSubscribe(this.handleScroll, this);
    }, 2000);
  },

  detached() {
    clearTimeout(this.timer);
    this.delPageScrollControlSubscribe(this.handleScroll, this);
  },

  methods: {
    handleScroll({ scrollTop }) {
      const pages = getCurrentPages();
      const { options } = pages[pages.length - 1];
      this.options = options;

      if (!this.initScrollTop) {
        this.initScrollTop = scrollTop;
      }

      if (scrollTop !== this.initScrollTop && !this.begin) {
        this.begin = true;
        for (let i = 0; i < 31; i++) {
          this.timer = setTimeout(() => {
            this.setYZData({
              btnText: `浏览 ${30 - i} 秒`,
            });
            if (i === 30) {
              // 参与
              this.joinActivity();
            }
          }, 1000 * i);
        }
      }
    },
    joinActivity() {
      const { alias, codeAlias } = this.options || {};

      const data = {
        kdtId: app.getKdtId(),
        eventType: 3,
        pageAlias: alias,
        alias: codeAlias,
      };
      app
        .request({
          path: '/wscump/lottery-code/join.json',
          data,
        })
        .then((res) => {
          const number = res ? res.tickets.length : 0;
          if (!number) return;

          wx.showToast({
            title: `任务完成，获得${number}个幸运码`,
            icon: 'none',
          });
          this.setYZData({
            btnText: '返回抽奖页',
            number,
            showArrow: false,
          });
        })
        .catch((err) => {
          wx.showToast({
            title: err.msg || '获取抽奖码失败',
            icon: 'none',
          });
          this.setYZData({
            btnText: '返回抽奖页',
            showArrow: false,
          });
        });
    },

    toBack() {
      if (this.data.btnText === '返回抽奖页') {
        const { codeAlias } = this.options || {};
        navigate.redirect({
          url: `/packages/ump/lottery-code/index?alias=${codeAlias}`,
        });
      }
    },
  },
});
