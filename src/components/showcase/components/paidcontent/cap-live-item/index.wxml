<wxs src="./computed.wxs" module="computed" />

<item
  class="cap-live-item"
  theme-class="{{ themeClass }}"
  title="{{ live.title }}"
  icon="https://img01.yzcdn.cn/weapp/wsc/AQjepw.png"
  title-tag="{{ statusText[live.liveStatus] }}"
  title-tag-class="{{ statusColor[live.liveStatus] }}"
  status-list="{{ computed.statusList(live) }}"
  subtitle="{{ live.summary }}"
  thumbnail="{{ live.cover }}"
  bottom-corner-class="{{ live.buyStatus && live.buyStatus.isBought ? '' : 'theme-color' }}"
  bottom-corner-text="{{ computed.priceText(live) }}"
  url="{{ live.url }}">
</item>
