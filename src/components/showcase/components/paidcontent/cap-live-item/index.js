Component({
  properties: {
    liveItem: Object,
    themeClass: String,
    showPriceInfo: Boolean
  },

  data: {
    slots: {
      title: true,
      label: true
    },
    statusText: [
      '已删除',
      '未开始',
      '直播中',
      '回看'
    ],
    statusColor: [
      '',
      'zan-tag--primary',
      'zan-tag--danger',
      ''
    ],
    live: {}
  },

  attached() {
    this.setData({
      live: this.covertLiveItem(this.properties.liveItem)
    });
  },

  methods: {
    covertLiveItem(liveItem) {
      let { buyStatus = {} } = liveItem;

      return this.genPriceFreeTag({
        ...liveItem,
        ...buyStatus,
        price: ((buyStatus.price || liveItem.price) / 100).toFixed(2),
        title: liveItem.title || '',
        url: `/packages/paidcontent/live/index?alias=${liveItem.alias}`
      });
    },

    // https://doc.qima-inc.com/pages/viewpage.action?pageId=46085665
    genPriceFreeTag(item) {
      // ios封杀敏感信息隐藏
      if (!item.showPriceInfo) {
        item.freetext = '';
        item.price = null;
        return item;
      }
      if (item.isBought) {
        item.buytext = '已购买';
      } else if (item.isFreeForVip) {
        item.freetext = '会员免费';
        item.price = null;
      } else if (item.isGroupOn) {
        item.tag = `${item.groupOnNum}人拼团价`;
      } else if (item.isVipDiscount) {
        item.tag = '会员价';
      } else {
        switch (item.sellerType) {
          case 1:
            if (item.price == 0) {
              item.freetext = '免费';
              item.price = null;
            } else if (item.showPriceInfo) {
              item.price = null;
            }
            break;
          case 2:
            if (item.isFree) {
              item.freetext = '免费';
              item.price = null;
            } else {
              item.price = null;
            }
            break;
          case 3:
            if (item.price == 0 || item.isFree) {
              item.freetext = '免费';
              item.price = null;
            }
            break;
          default:
            break;
        }
      }
      return item;
    }
  }
});
