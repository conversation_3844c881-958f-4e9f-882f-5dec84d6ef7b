module.exports = {
  statusList(live) {
    return [live.liveStartAt];
  },
  priceText(live) {
    if (live.showPriceInfo) {
      var price = 0;
      if (live.buyStatus) {
        price = live.buyStatus.price;
      } else {
        price = live.price;
      }

      if (live.buyStatus && live.buyStatus.isBought) return '已购买';
      if (price == 0) return '免费';
      const _price = price / 100;
      return '￥ ' + _price.toFixed(2);
    }
    return '';
  }
};
