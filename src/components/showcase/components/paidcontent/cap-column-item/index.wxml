<wxs src="./computed.wxs" module="computed"/>

<item
  class="cap-column-item"
  theme-class="{{ themeClass }}"
  title="{{ column.title }}"
  subtitle="{{ computed.subtitle(column, isPaid) }}"
  thumbnail="{{ computed.thumbnailUrl(column) }}"
  status-list="{{ computed.statusList(column, isPaid) }}"
  bottom-corner-class="{{ isPaid ? '' : 'theme-color' }}"
  bottom-corner-text="{{ computed.priceText(column, buyStatus) }}"
  bottom-tag-text="{{ computed.priceTagText(column, buyStatus) }}"
  url="{{ computed.columnUrl(column) }}">
</item>
