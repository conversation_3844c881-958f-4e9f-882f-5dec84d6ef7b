const BUY_STATUS = {
  PAID: 0,
  FREE: 1,
  NORMAL: 2,
  VIP_FREE: 3,
  VIP_DISCOUNT: 4,
  GROUPON: 5
};

module.exports = {
  subtitle(column, isPaid) {
    const lastUpdatedInfo = column.lastUpdatedInfo || {};
    const summary = column.summary || '';

    return isPaid && lastUpdatedInfo && lastUpdatedInfo.lastUpdatedTitle
      ? lastUpdatedInfo.publishAt + '更新：' + lastUpdatedInfo.lastUpdatedTitle
      : summary;
  },

  statusList(column, isPaid) {
    const list = [];

    const periodInfo = (column.isUpdate ? '已更新' : '共') + column.contentsCount + '期';
    if (column.contentsCount) list.push(periodInfo);

    const subscriptionsCount = column.subscriptionsCount + '人已购';
    if (!isPaid && column.subscriptionsCount) list.push(subscriptionsCount);

    return list;
  },

  priceText(column, buyStatus) { // 价格文字
    const price = column.price / 100;
    const priceText = '￥' + price.toFixed(2);

    return [
      '已购买',
      '免费',
      priceText,
      '会员免费',
      priceText,
      priceText,
      ''
    ][buyStatus];
  },

  priceTagText(column, buyStatus) { // 价格标签文字
    if (buyStatus === BUY_STATUS.VIP_DISCOUNT) return '会员价';
    if (buyStatus === BUY_STATUS.GROUPON) return column.groupOnNum + '人团购价';

    return '';
  },

  columnUrl(column) {
    return `/packages/paidcontent/column/index?alias=${column.alias}`;
  },

  thumbnailUrl(column) {
    // return UrlHelper.getCdnImageUrl(this.item.cover, '!145x145.jpg');
    const cover = column.cover || '';
    return cover;
  }
};
