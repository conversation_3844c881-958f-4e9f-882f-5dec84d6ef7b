<view class="punch-item {{ mode === 'card' ? 'punch-item--card' : '' }}">
  <item
    external-class="punch-item__item"
    theme-class="{{ themeClass }}"
    title="{{ punch.name }}"
    supports-multi-lines
    thumbnail="{{ punch.coverUrl }}"
    thumbnail-width="{{ 98 }}"
    thumbnail-height="{{ 56 }}"
    status-list="{{ statusList }}"
    url="{{ url }}"
    top-corner-text="{{ price ? '￥' + price / 100 : '' }}"
    slots="{{ slots }}">
    <view
      class="punch-item__tag {{ punch.proceedStatus === 2 && punch.status !== 0 ? 'punch-item__tag--green' : '' }}"
      slot="icon">
      {{ tagText }}
    </view>
  </item>
  <view wx:if="{{ mode === 'card' }}" class="punch-item__footer">
    <view class="punch-item__btn" bindtap="onMyPunchTap">
      <image
        class="punch-item__btn-icon icon-punch" 
        src="https://img01.yzcdn.cn/punch/image/icon-punch.png"
      />
      我的打卡
    </view>
    <button
      class="punch-item__btn" 
      hover-class="none"
      plain
      open-type="share"
      data-punch="1"
      data-alias="{{ punch.alias }}"
      data-title="{{ punch.name }}">
      <image
        class="punch-item__btn-icon icon-invite"
        src="https://img01.yzcdn.cn/punch/image/icon-invite.png"
      />
      邀请好友
    </button>
    <view class="punch-item__btn" bindtap="onIntroTap">
      <image
        class="punch-item__btn-icon icon-intro"
        src="https://img01.yzcdn.cn/punch/image/icon-intro.png"
      />
      打卡介绍
    </view>
  </view>
</view>