module.exports = {
  statusList(count, subCount, isPaid) {
    const list = [];

    const periodInfo = '已更新' + count + '期';
    if (count || count === 0) list.push(periodInfo);

    if (!isPaid && subCount) list.push(subCount + '人已购');

    return list;
  },

  priceText(isPaid, price, showPriceInfo) {
    if (showPriceInfo) {
      if (isPaid) return '已购买';
      if (price === 0) return '免费';
      if (!price) return '';
      const _price = price / 100;
      return '￥ ' + _price.toFixed(2);
    }
    return '';
  }
}
