import WscComponent from 'pages/common/wsc-component/index';

// const app = getApp();

WscComponent({
  properties: {
    hideTag: {
      type: Boolean,
      value: false
    },
    themeClass: String,
    alias: {
      type: String,
      value: ''
    },
    cover: {
      type: String,
      value: ''
    },
    count: {
      type: Number,
      value: 0
    },
    subCount: {
      type: Number,
      value: 0
    },
    summary: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      value: ''
    },
    price: null,
    time: {
      type: String,
      value: ''
    },
    noBorder: {
      type: Boolean,
      value: false
    },
    hasBuy: {
      type: Boolean,
      value: false
    },
    isGroupon: {
      type: Boolean,
      value: false
    },
    grouponNum: {
      type: Number,
      value: 0
    },
    isFree: Number,
    isPaid: Boolean,
    isVip: {
      type: Boolean,
      value: false
    },
    tagName: {
      type: Number,
      value: 0
    },
    showPriceInfo: Boolean
  },

  data: {
  },

  methods: {
    goToMember(e) {
      const { alias } = e.currentTarget.dataset;
      if (!alias) return;

      wx.navigateTo({
        url: `/packages/paidcontent/rights/index?alias=${alias}`
      });
    }
  },
});
