<wxs src="./computed.wxs" module="computed" />

<item
  class="cap-member-item"
  theme-class="{{ themeClass }}"
  title="{{ title }}"
  subtitle="{{ summary || '' }}"
  thumbnail="{{ cover }}"
  status-list="{{ computed.statusList(count, subCount, isPaid) }}"
  bottom-corner-class="{{ isPaid ? '' : 'theme-color' }}"
  bottom-corner-text="{{ computed.priceText(isPaid, price, showPriceInfo) }}"
  url="/packages/paidcontent/rights/index?alias={{ alias }}">
</item>

