<wxs src="./computed.wxs" module="computed"/>

<item
  class="cap-content-item"
  theme-class="{{ themeClass }}"
  title="{{ content.title }}"
  subtitle="{{ computed.subtitle(content, inColumn) }}"
  thumbnail="{{ computed.thumbnailUrl(content) }}"
  icon="{{ computed.icon(content) }}"
  status-list="{{ computed.statusList(content, isPaid, typeSuffix) }}"
  bottom-corner-class="{{ isPaid ? '' : 'theme-color' }}"
  bottom-corner-text="{{ computed.priceText(content, buyStatus, typeSuffix) }}"
  bottom-tag-text="{{ computed.priceTagText(content, buyStatus) }}"
  url="{{ computed.contentUrl(content) }}">
</item>
