const BUY_STATUS = {
  PAID: 0,
  FREE_TRY: 1,
  NORMAL: 2,
  COLUMN_ONLY: 3,
  VIP_FREE: 4,
  VIP_DISCOUNT: 5,
  GROUPON: 6,
  FREE: 7
};

module.exports = {
  subtitle(content, inColumn) {
    const summary = content.summary || '';
    const columnTitle = content.columnTitle || '';
    return inColumn ? columnTitle : summary;
  },

  statusList(content, isPaid, typeSuffix) {
    const list = [content.publishAt];

    const subscriptionsCount = content.subscriptionsCount + '人已' + typeSuffix;
    if (!isPaid && content.subscriptionsCount) list.push(subscriptionsCount);

    return list;
  },

  priceText(content, buyStatus, typeSuffix) { // 价格文字
    const price = content.price / 100;
    const priceText = '￥' + price.toFixed(2);

    return [
      '已购买',
      '免费试' + typeSuffix,
      priceText,
      '',
      '会员免费',
      priceText,
      priceText,
      '免费',
      ''
    ][buyStatus];
  },

  priceTextClass(isPaid) {
    if (isPaid) return '';

    return 'text-red';
  },

  priceTagText(content, buyStatus) { // 价格标签文字
    if (buyStatus === BUY_STATUS.VIP_DISCOUNT) return '会员价';
    if (buyStatus === BUY_STATUS.GROUPON) return content.groupOnNum + '人团购价';

    return '';
  },

  contentUrl(content) {
    return `/packages/paidcontent/content/index?alias=${content.alias}`;
  },

  thumbnailUrl(content) {
    // return UrlHelper.getCdnImageUrl(this.item.cover, '!145x145.jpg');
    const picture = content.picture || {};
    const cover = picture.cover || '';
    return cover;
  },

  icon(content) {
    const mt = content.mediaType || 0;
    return [
      '',
      'https://img01.yzcdn.cn/weapp/paidcontent/doc.png',
      'https://img01.yzcdn.cn/weapp/paidcontent/music.png',
      'https://img01.yzcdn.cn/weapp/paidcontent/video.png',
    ][mt];
  }
};
