import WscComponent from 'pages/common/wsc-component/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

WscComponent({
  externalClasses: ['external-class'],

  options: {
    multipleSlots: true
  },

  properties: {
    themeClass: String,
    slots: {
      type: Object,
      value: {
        thumbnail: false,
        icon: false,
        title: false,
        subtitle: false,
        status: false,
        label: false,
      }
    },
    navigateType: {
      type: String,
      value: 'navigate'
    },
    url: String,
    thumbnail: String,
    title: String,
    titleTag: String,
    titleTagClass: String,
    supportsMultiLines: Boolean,
    subtitle: {
      type: String,
      observer(subtitle) {
        this.setData({
          _subtitle: subtitle.replace(/\n/g, ' ')
        });
      }
    },
    statusList: Array,
    topCornerText: String,
    topTagText: String,
    topCornerClass: String,
    bottomCornerText: String,
    bottomTagText: String,
    bottomCornerClass: String,
    thumbnailHeight: {
      type: Number,
      value: 64
    },
    thumbnailWidth: {
      type: Number,
      value: 110
    },
    icon: String,
    collectFormId: {
      type: <PERSON>olean,
      value: false
    }
  },

  data: {
    _subtitle: ''
  },

  attached() {
    this.setYZData({
      _thumbnail: cdnImage(this.data.thumbnail, '!220x220.jpg')
    });
  },

  methods: {
    goTo() {
      if (!this.data.url) return;

      wx.navigateTo({
        url: this.data.url
      });
    }
  },
});
