<wsc-navigator 
  class="paidcontent-item external-class {{ themeClass }}" 
  type="{{ navigateType }}"
  path="{{ url }}"
  collect-form-id="{{ collectFormId }}"
  hover-class="none" 
  data-url="{{ url }}">
  <!-- 缩略图 -->
  <view class="paidcontent-item__thumbnail-wrapper">
    <slot wx:if="{{ slots.thumbnail }}" name="thumbnail"></slot>
    <image wx:else 
      class="paidcontent-item__thumbnail" 
      style="width: {{ thumbnailWidth }}px; height: {{ thumbnailHeight }}px" 
      src="{{ _thumbnail }}"
      mode="aspectFill">
    </image>
    <view wx:if="{{ statusTag }}" class="paidcontent-item__status-tag">{{ statusTag }}</view>
    <slot wx:if="{{ slots.icon }}" name="icon"></slot>
    <view wx:else class="paidcontent-item__thumbnail-icon">
      <image class="paidcontent-item__thumbnail-icon-image" src="{{ icon }}"></image>
    </view>
  </view>

  <view class="paidcontent-item-detail" style="height: {{ thumbnailHeight }}px">
    <view class="paidcontent-item-row">
      <view class="paidcontent-item-col">
        <!-- 标题 -->
        <view class="paidcontent-item__title-bar">
          <slot wx:if="{{ slots.title }}" name="title"></slot>
          <block wx:else>
            <!-- <text 
              wx:if="{{ titleTag }}" 
              class="paidcontent-item__title-tag zan-tag {{ titleTagClass }}"
            >{{ titleTag }}</text> -->
            <van-tag
              wx:if="{{ titleTag }}"
              class="paidcontent-item__title-tag {{ titleTagClass }}">
              {{ titleTag }}
            </van-tag>
            <text 
              class="paidcontent-item__title {{ supportsMultiLines ? 'paidcontent-item__title--multi' : ''}}"
            >{{ title }}</text>
          </block>
        </view>
  
        <!-- 副标题 -->
        <view wx:if="{{ slots.subtitle || _subtitle }}" class="paidcontent-item__subtitle {{ bottomTagText ? '' : 'paidcontent-item__subtitle--notag' }}">
          <slot wx:if="{{ slots.subtitle }}" name="subtitle"></slot>
          <text wx:else>{{ _subtitle }}</text>
        </view>
      </view>
      <view class="paidcontent-item-col paidcontent-item__corner-top">
        <!-- 右上角 -->
        <slot wx:if="{{ slots.topTag }}" name="topTag"></slot>
        <block wx:else>
          <view class="paidcontent-item__corner-top-text {{ topCornerClass }}">{{ topCornerText }}</view>
          <!-- <view 
            wx:if="{{ topTagText }}" 
            class="paidcontent-item__top-tag zan-tag zan-tag--danger zan-tag--plain paidcontent-item-tag">
            {{ topTagText }}
          </view> -->
          <van-tag
            wx:if="{{ topTagText }}"
            plain
            type="danger"
            class="paidcontent-item__top-tag paidcontent-item-tag">
            {{ topTagText }}
          </van-tag>
        </block>
      </view>
    </view>
  
    <view class="paidcontent-item-row paidcontent-item-detail__footer">
      <view class="paidcontent-item-col paidcontent-item__status-list">
        <!-- 状态信息 -->
        <slot wx:if="{{ slots.status }}" name="status"></slot>
        <block wx:else>
          <text 
            wx:for="{{ statusList }}" 
            wx:key="item" 
            class="paidcontent-item__status"
            style="{{ item.color ? 'color:' + item.color : '' }}"
          >{{ item.text ? item.text : item }}</text>
        </block>
      </view>
      <view class="paidcontent-item-col paidcontent-item__corner-bottom">
        <!-- 右下角标签 -->
        <slot wx:if="{{ slots.bottomTag }}" name="bottomTag"></slot>
        <block wx:else>
          <!-- <view 
            wx:if="{{ bottomTagText }}" 
            class="paidcontent-item__bottom-tag zan-tag zan-tag--danger zan-tag--plain paidcontent-item-tag">
            {{ bottomTagText }}
          </view> -->
          <van-tag
            wx:if="{{ bottomTagText }}"
            plain
            type="danger"
            class="paidcontent-item__top-tag paidcontent-item-tag">
            {{ bottomTagText }}
          </van-tag>
          <view class="paidcontent-item__corner-bottom-text {{ bottomCornerClass }}">{{ bottomCornerText  }}</view>
        </block>
      </view>
    </view>
  </view>
</wsc-navigator>
