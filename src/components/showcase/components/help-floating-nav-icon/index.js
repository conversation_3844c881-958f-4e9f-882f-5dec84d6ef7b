import WscComponent from 'pages/common/wsc-component/index';
import IconController from 'components/showcase/components/floating-nav/icon-controller/index';

WscComponent({
  attached() {
    const app = getApp();
    const pages = getCurrentPages();
    const currentPageIds = pages[pages.length - 1].__wxExparserNodeId__; // page 的 __wxExparserNodeId__ 唯一标志一个实例
    this.iconController = new IconController();
    app.on(`set-wsc-feature-floating-nav-${currentPageIds}`, (data) => {
      this.iconController?.setIcon(this, data);
    });
    app.on(
      `set-wsc-feature-floating-nav-${currentPageIds}-setalone`,
      (data) => {
        this.iconController.setAlone(this, data);
      }
    );
  },
  detached() {
    this.iconController.destroy(this);
  },
  methods: {},
});
