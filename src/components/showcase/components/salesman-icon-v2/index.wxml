<form-view wx:if="{{ shareData.share }}">
  <view
    class="salesman-icon {{ goaway ? 'goaway' : '' }}"
    style="bottom: {{ bottom ? bottom : '' }}; right: {{ bottom ? '9px' : '' }}; transition: {{ bottom ? 'all 0.2s' : '' }};"
    bindtap="handleIconTap"
  >
    <slot wx:if="{{ customIcon }}" name="icon"></slot>
    <image wx:else src="{{ iconUrl }}" />
  </view>
</form-view>

<van-popup
  show="{{ showPopup }}"
  position="bottom"
  z-index="{{ 900 }}"
  custom-style="border-radius: 8px 8px 0 0;bottom: {{ popupBottom }}px"
  overlay="{{ showPopupOverlay }}"
  bind:click-overlay="handleTogglePanel"
>
  <form-view>
    <view class="panel">
      <van-icon
        name="close"
        custom-class="panel-close"
        bindtap="handleTogglePanel"
      />
      <view class="panel-title">
        <block wx:if="{{ shareData.openCustomCpsExt && goodsId }}" >
          <text>商家已开启自定义佣金计算</text>
        </block>
        <block wx:else>
          <text wx:if="{{ commission > 0 }}">分享后预计最高可赚取佣金￥{{ commission }}</text>
          <text wx:else>立即分享给好友</text>
        </block>
      </view>
      <view class="panel-desc">
        朋友通过你分享的页面成功购买后，你可获得对应的佣金。佣金可在“会员中心-{{ shareData.salesmanName }}中心”里查看
      </view>
      <view wx:if="{{ panelIconFlex }}" class="panel-icon-flex">
        <template is="icon-list" data="{{ iconList }}" />
      </view>
      <scroll-view wx:else scroll-x class="panel-icon">
        <template is="icon-list" data="{{ iconList }}" />
      </scroll-view>
      <view
        wx:if="{{ !hideBottom }}"
        class="panel-bottom"
      >
         <van-button
          wx:if="{{ rightsInfo.allowMoments && !hideZoneLink }}"
          round
          plain
          custom-class="panel-bottom--btn"
          bindtap="handleClickMoments"
        >
          我的空间
        </van-button>

        <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="handleLinkCenter">
          <van-button
            round
            plain
            custom-class="{{ rightsInfo.allowMoments ? 'panel-bottom--btn' : 'panel-bottom--btn__block' }}"
          >
            {{ shareData.salesmanName }}中心
          </van-button>
        </user-authorize>
      </view>
    </view>
  </form-view>
</van-popup>

<van-popup
  show="{{ showWeappCode }}"
  z-index="{{ 999 }}"
  custom-style="background-color: transparent;overflow: initial;"
  bind:click-overlay="handleToggleWeappCode"
>
  <view class="weappcode-container">
    <van-icon
      name="close"
      custom-class="weappcode-container--close"
      bindtap="handleToggleWeappCode"
    />
    <view class="weappcode-container__body">
      <image src="{{ weappCode }}" />
      <text class="weappcode-container__body-text">长按识别小程序码</text>
    </view>
    <van-button
      class="weappcode-container__button"
      color="#fff"
      size="large"
      custom-style="background-color: transparent;width: 160px;height: 36px;"
      plain
      round
      bind:tap="handleSaveWeappCode"
    >
      保存图片
    </van-button>
  </view>
</van-popup>

<van-popup
  show="{{ showPoster }}"
  z-index="{{ 999 }}"
  custom-style="background-color: transparent;overflow: initial;"
  bind:click-overlay="handleTogglePoster"
>
  <view class="poster-container">
    <van-icon
      name="close"
      custom-class="poster-container--close"
      bindtap="handleTogglePoster"
    />
    <view class="poster-container__body">
      <image src="{{ poster }}" mode="aspectFill" />
    </view>
    <van-button
      class="poster-container__button"
      color="#fff"
      size="large"
      custom-style="background-color: transparent;width: 160px;height: 36px;"
      plain
      round
      bind:tap="handleSavePoster"
    >
      保存图片
    </van-button>
  </view>
</van-popup>

<van-popup
  show="{{ showTaxSign }}"
  z-index="{{ 999 }}"
  custom-style="background-color: transparent"
  bind:click-overlay="handleToggleTaxSign"
>
  <view class="task-sign">
    <image
      src="https://b.yzcdn.cn/image/salesman/task-banner.png"
      class="task-sign__banner"
    />
    <view class="task-sign__text">
      {{ signText }}
    </view>
    <view
      class="task-sign__btn"
      bindtap="handleCert"
    >
      立即认证
    </view>
    <view
      class="task-sign__next"
      bindtap="handleClose"
    >
      下次再说
    </view>
  </view>
</van-popup>

<template name="icon-list">
  <block
    wx:for="{{ iconList }}"
    wx:for-item="icon"
    wx:key="alias"
  >
    <button
      wx:if="{{ !icon.hidden }}"
      data-alias="{{ icon.alias }}"
      open-type="{{ icon.openType }}"
      class="panel-icon--btn"
      bindtap="handleClickIcon"
    >
      <image src="{{ icon.iconUrl }}" />
      <text>{{ icon.title }}</text>
    </button>
  </block>
</template>

<canvas
  canvas-id="salesman-icon"
  class="poster-canvas"
></canvas>
