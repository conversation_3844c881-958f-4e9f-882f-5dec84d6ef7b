.salesman-icon {
  height: 50px;
  width: 50px;
  position: fixed;
  bottom: 115px;
  right: 6px;
  z-index: 140;

  /** z-index 顺序 修改请查看 docs/z-index.md **/

  image {
    height: 50px;
    width: 50px;
  }
}

.goaway {
  transform: translate3d(100px, 0, 0);
}

.panel {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  padding-top: 16px;

  &-close {
    position: absolute !important;
    top: 6px;
    right: 6px;
    font-size: 24px !important;
    color: #999;
  }

  &-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #333;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &-desc {
    font-size: 12px;
    color: #999;
    line-height: 16px;
    margin: 10px auto 0;
    text-align: center;
    letter-spacing: 0.4px;
    padding: 0 20px;
  }

  &-icon {
    margin-top: 20px;
    width: 100%;
    white-space: nowrap;

    &--btn {
      display: inline-block;
      width: 168rpx;
      height: 168rpx;
      line-height: 0;
      text-align: center;
      background: transparent;
      padding: 0;

      text {
        display: block;
        margin-top: 10px;
        line-height: 1.2;
        font-size: 11px;
        color: #999;
      }

      image {
        width: 52px;
        height: 52px;
        display: block;
        margin: 0 auto;
      }

      &::after {
        display: none;
      }

      &:first-of-type {
        margin-left: 20px;
      }
    }

    &-flex {
      display: flex;
      justify-content: space-around;
      flex-wrap: nowrap;
      margin-top: 20px;
      width: 100%;
    }
  }

  &-bottom {
    width: 100%;
    box-sizing: border-box;
    font-size: 14px;
    color: #333;
    text-align: center;
    padding: 14px 15px;

    &--btn {
      border-color: #dcdee0;
      margin: 0 8px;
      height: 36px !important;
      line-height: 36px !important;
    }

    &--btn__block {
      border-color: #dcdee0;
      margin: 0 auto;
      display: block;
      width: 200px;
    }
  }
}

.weappcode-container,
.poster-container {
  text-align: center;

  &__body {
    position: relative;
    margin-bottom: 24px;
    text-align: center;
    width: 260px;
    padding: 16px;
    border-radius: 8px;
    background-color: #fff;

    &-text {
      margin-top: 12px;
      line-height: 16px;
      font-size: 12px;
      color: #969799;
    }
  }

  &__button {
    margin-top: 24px;
  }

  &--close {
    position: absolute !important;
    top: -17px;
    right: 0;
    font-size: 24px !important;
    color: #fff;
  }
}

.weappcode-container__body {
  image {
    width: 236px;
    height: 236px;
    padding: 17px 12px 12px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(50, 50, 51, 0.1);
  }
}

.poster-container__body {
  padding: 16px;

  image {
    width: 260px;
    height: 396px;
    vertical-align: bottom;
  }
}

.poster-canvas {
  position: fixed;
  top: -10000px;
  width: 300px;
  height: 415px;
}

.task-sign {
  width: 270px;
  background: #fff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;

  &__banner {
    width: 180px;
    height: 100px;
    margin: 24px 0;
  }

  &__text {
    font-size: 13px;
    color: #323233;
    text-align: center;
    line-height: 20px;
    width: 223px;
  }

  &__btn {
    width: 200px;
    height: 36px;
    line-height: 36px;
    border-radius: 18px;
    font-size: 14px;
    color: #fff;
    margin: 24px 0 16px;
    text-align: center;
    background-image: linear-gradient(135deg, #ffaa1e 0%, #ff720d 100%);
    box-shadow: 0 3px 6px 0 rgba(255, 114, 13, 0.2);
  }

  &__next {
    font-size: 13px;
    color: #969799;
    text-align: center;
    margin-bottom: 24px;
  }
}
