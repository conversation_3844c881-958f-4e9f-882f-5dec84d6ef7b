import WscComponent from 'pages/common/wsc-component/index';
import { node as request } from 'shared/utils/request';
import mkc from '@youzan/weapp-utils/lib/map-keys-case';
import tabBehavior from '@/custom-tab-bar-v2/tab-behavior';
import openWebView from 'utils/open-web-view';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import IconController from '../floating-nav/icon-controller/index';
import {
  getCurrentPage,
  genCode,
  navigate,
  authorize,
  loadImage,
  saveImage,
  configAdapter,
  compose,
  fetchPoster,
} from './helpers';
import { monitor, setLogger } from './helpers/log';
import { drawImageCode, createTempPath } from './draw';
import { iconList, SHARE_LOGGER_CONFIG } from './config';
import { subscribeMessage, checkTaxSign } from './tree-service';

const app = getApp();

WscComponent({
  options: {
    multipleSlots: true,
  },

  behaviors: [tabBehavior],

  properties: {
    pageQuery: Object,
    // 获取对应商品的佣金比率
    goodsId: {
      type: Number,
      value: 0,
      observer(id) {
        if (id) {
          this.getShareData();
        }
      },
    },
    // 佣金比率得到预计赚取金额 单位：分
    goodsPrice: {
      type: String,
      value: '',
      observer(price) {
        const { cpsRate } = this.data.shareData;
        const commission = (Math.round(cpsRate * price) / 100).toFixed(2, 10);
        this.setYZData({
          commission,
        });
      },
    },
    // 自定义icon展示
    customIcon: {
      type: Boolean,
      value: false,
    },
    // 调用页面外部海报方法
    usePagePoster: {
      type: Boolean,
      value: false,
    },
    // 隐藏商品海报
    hideGoodsPoster: {
      type: Boolean,
      value: true,
    },
    // 隐藏图文海报
    hideTuwen: {
      type: Boolean,
      value: false,
    },
    // 隐藏空间海报
    hideZonePoster: {
      type: Boolean,
      value: false,
    },
    // 隐藏发布动态
    hideZonePublish: {
      type: Boolean,
      value: true,
    },
    // 隐藏动态链接
    hideZoneLink: {
      type: Boolean,
      value: false,
    },
    // 自定义分享数据，目前只针对生成分销员小程序二维码
    customData: {
      type: Object,
      value: null,
    },
    // 赚字内部区分业务场景，track埋点
    // https://track.qima-inc.com/projects/299
    scenes: {
      type: String,
      value: 'default',
    },
  },

  data: {
    shareData: {},
    iconUrl: '',
    showPopup: false,
    rightsInfo: {},
    commission: 0,
    iconList,
    weappCode: '',
    showWeappCode: false,
    poster: '',
    showPoster: false,
    showPopupOverlay: true,
    panelIconFlex: false,
    popupBottom: 0,
    showTaxSign: false,
    signText: '',
  },

  attached() {
    this.initPopupBottom();
    if (this.data.hideGoodsPoster) {
      // 商品使用信息需要在拿到商品信息后再获取share信息
      this.getShareData();
    }
    this.checkShopRights();
    this.initConfig();
  },

  ready() {
    this.bindRelation();
  },

  methods: {
    initPopupBottom() {
      const TabPaths = [
        'packages/home/<USER>/index',
        'packages/home/<USER>/one',
        'packages/home/<USER>/two',
        'packages/home/<USER>/three',
        'packages/usercenter/dashboard/index',
      ];
      const { route } = getCurrentPage();
      if (TabPaths.indexOf(route) > -1) {
        this.setYZData({
          popupBottom: 49,
        });
      }
    },

    observerIconList(list) {
      const len = list.reduce((prev, item) => {
        return prev + (item.hidden ? 0 : 1);
      }, 0);
      this.setData({
        panelIconFlex: len < 4,
      });
    },

    observerShareData(shareData) {
      // 优先使用自定义图标
      const iconUrl = shareData.iconUrl;
      this.setYZData({
        iconUrl,
      });

      const { cpsRate } = shareData;
      const commission = (
        Math.floor(cpsRate * this.data.goodsPrice) / 100
      ).toFixed(2, 10);
      this.setYZData({
        commission,
      });
    },

    initConfig() {
      // 隐藏不使用的分享图标
      const hideItems = [];
      Object.keys(this.data).forEach((key) => {
        // 商品海报和图文二存一
        if (!this.data.hideGoodsPoster) {
          hideItems.push('tuwen');
        }
        if (key.indexOf('hide') >= 0 && this.data[key]) {
          const upperCaseKey = key.replace('hide', '');
          hideItems.push(
            upperCaseKey.replace(/^.{1}/, upperCaseKey.charAt(0).toLowerCase())
          );
        }
      });
      const list = configAdapter(hideItems, this.data.iconList);
      this.setYZData({
        iconList: list,
      });
      this.observerIconList(list);
    },

    setIcon(shareData) {
      if (shareData.share) {
        this.iconController = new IconController()?.setIcon(this, {
          priority: 90,
          height: 50,
          cb: [
            (bottom) => {
              this.setYZData({ bottom: bottom + 'px' });
            },
            (goaway) => {
              this.setYZData({ goaway });
            },
          ],
        });
      }
    },

    getShareData() {
      const { goodsId } = this.data;
      const query = {
        type: 'NORMAL',
      };
      if (goodsId) {
        query.goodsId = goodsId;
      }

      const { route } = getCurrentPage();
      monitor({
        extra: {
          path: route,
          url: '/wscump/salesman/share.json',
        },
      });

      request({
        path: '/wscump/salesman/share.json',
        query,
        config: {
          cache: true,
          expire: 10,
        },
      }).then((data) => {
        const shareData = mkc.toCamelCase(data);
        this.triggerEvent('set-share', {
          shareData,
        });
        this.setData({
          shareData,
        });
        this.handleLogViewLog(shareData);
        this.observerShareData(shareData);
        this.setIcon(shareData);
      });
    },

    bindCustomer(sellerFrom, fromParams) {
      const { route } = getCurrentPage();
      monitor({
        extra: {
          url: '/wscsalesman/bind-customer.json',
          path: route,
          sellerFrom,
          fromParams,
        },
      });
      return request({
        path: '/wscsalesman/bind-customer.json',
        method: 'POST',
        data: {
          sellerFrom,
          fromParams,
        },
      });
    },

    bindRelation() {
      const { options } = getCurrentPage();
      // 兼容老版小程序为sls
      const { sl, sls, from_params: fromParams } = options;
      const sellerFrom = sl || sls;
      if (sellerFrom) {
        this.bindCustomer(sellerFrom, fromParams).catch(() => {
          // 绑定异常情况，重试一次
          this.bindCustomer(sellerFrom, fromParams);
        });
      }
    },

    /**
     * 获取权限信息
     */
    checkShopRights() {
      request({
        path: '/wscump/salesman/shop/checkShopRights.json',
      }).then((res) => {
        this.setYZData({
          rightsInfo: res,
        });
        // 没有空间权限不显示是空间相关
        if (!res.allowMoments) {
          const list = configAdapter(
            ['zonePoster', 'zonePublish'],
            this.data.iconList
          );
          this.setYZData({
            iconList: list,
          });
          this.observerIconList(list);
        }
      });
    },

    handlers() {
      return compose([
        subscribeMessage.bind(this),
        checkTaxSign.bind(this),
        this.handleTogglePanel.bind(this),
      ]);
    },

    handleIconTap() {
      wx.showLoading();
      this.handlers()();
    },

    handleCert() {
      const { route, options } = getCurrentPage();
      let returnUrl = `/${route}`;
      if (options) {
        returnUrl += '?';
        Object.keys(options).forEach((key, index) => {
          if (index > 0) {
            returnUrl += `&${key}=${options[key]}`;
          } else {
            returnUrl += `${key}=${options[key]}`;
          }
        });
      }
      openWebView('/wscassets/cert/tax/intro', {
        title: '身份证认证',
        query: {
          fromId: 12,
          returnUrl: decodeURIComponent(returnUrl),
        },
      });
    },

    handleClose() {
      this.setYZData(
        {
          showTaxSign: false,
        },
        () => {
          this.handleTogglePanel();
        }
      );
    },

    handleTogglePanel() {
      const showPopup = !this.data.showPopup;
      if (showPopup) {
        this.handleLog(SHARE_LOGGER_CONFIG.clickShare);
      }
      this.setYZData({
        showPopup,
        showPopupOverlay: showPopup || this.data.showPopupOverlay,
      });
    },

    handleLog(logData) {
      const { scenes, shareData } = this.data;
      const { seller } = shareData;
      setLogger(logData, { sl: seller, scenes });
    },

    handleLogViewLog(shareData = {}) {
      const { share } = shareData;
      if (share) {
        this.handleLog(SHARE_LOGGER_CONFIG.salesMoneyView);
      }
    },

    handleClickIcon(e) {
      const { alias } = e.currentTarget.dataset;
      if (alias === 'tuwen' || alias === 'goodsPoster') {
        this.genPoster();
      } else {
        this[alias] && this[alias]();
      }
    },

    handleClickMoments() {
      navigate.navigate({
        url: '/packages/salesman/zone/home/<USER>',
      });
    },

    handleLinkCenter() {
      navigate.navigate({
        url: `/packages/salesman/salesman-center/index?dsKdtId=${this.data.shareData.dsKdtId}`,
      });
    },

    genPageCode() {
      const { weappCode, customData } = this.data;
      return new Promise((resolve) => {
        if (weappCode) {
          return resolve(weappCode);
        }
        let { route, options } = getCurrentPage();
        if (this.data.pageQuery) {
          Object.assign(options, this.data.pageQuery);
        }

        // 自定义二维码生成数据
        if (customData) {
          // 自定义 path
          if (customData.path) {
            route = customData.path;
            delete customData.path;
          }
          // 其他参数
          options = { ...options, ...customData };
        }

        resolve(genCode.call(this, route, options));
      });
    },

    weappCode() {
      const { seller } = this.data.shareData;
      setLogger(SHARE_LOGGER_CONFIG.miniprogram, { sl: seller });
      wx.showToast({
        title: '获取小程序码中',
        icon: 'loading',
      });
      if (this.data.weappCode) {
        wx.hideToast();
        this.handleToggleWeappCode();
        return;
      }
      this.genPageCode().then((weappCode) => {
        this.setYZData({
          weappCode,
        });
        this.handleToggleWeappCode();
      });
    },

    handleToggleWeappCode() {
      const showWeappCode = !this.data.showWeappCode;
      this.setYZData({
        showWeappCode,
      });
    },

    handleSaveWeappCode() {
      const { weappCode } = this.data;
      wx.showLoading({ title: '保存中' });
      loadImage(weappCode).then((src) => {
        if (!src) throw new Error('图片下载失败');
        this.save(src).then(() => {
          this.handleToggleWeappCode();
        });
      });
    },

    save(src) {
      return authorize('scope.writePhotosAlbum')
        .then(saveImage.bind(this, src))
        .then(() => {
          wx.hideLoading();
          wx.showToast({
            title: '保存成功',
            icon: 'success',
          });
        })
        .catch((err) => {
          wx.showToast({
            title: err || '保存失败，请检查保存到相册的权限',
            icon: 'error',
          });
        });
    },

    genPoster() {
      wx.showToast({
        title: '正在生成',
        icon: 'loading',
      });
      const { usePagePoster, shareData } = this.data;
      const { seller } = shareData;
      // 页面调用自由的分享卡片海报
      if (usePagePoster) {
        setLogger(SHARE_LOGGER_CONFIG.poster, {
          sl: seller,
          posterType: 'goodsPoster',
        });
        this.triggerEvent('share-card');
      } else {
        setLogger(SHARE_LOGGER_CONFIG.poster, {
          sl: seller,
          posterType: 'salesmanPost',
        });
        this.showPoster();
      }
    },

    showPoster() {
      if (this.tuwenPoster) {
        this.setYZData(
          {
            poster: this.tuwenPoster,
          },
          () => wx.hideToast()
        );
        this.handleTogglePoster();
        return;
      }
      this.genPageCode().then((weappCode) => {
        drawImageCode
          .call(this, weappCode)
          .then(createTempPath.bind(this))
          .then((src) => {
            this.setYZData(
              {
                poster: src,
              },
              () => wx.hideToast()
            );
            this.handleTogglePoster();
            this.tuwenPoster = src;
          });
      });
    },

    handleTogglePoster() {
      const showPoster = !this.data.showPoster;
      this.setYZData({
        showPoster,
      });
    },

    zonePoster() {
      wx.showToast({
        title: '正在生成',
        icon: 'loading',
      });
      if (this.zonePosterUrl) {
        this.setYZData(
          {
            poster: this.zonePosterUrl,
          },
          () => wx.hideToast()
        );
        this.handleTogglePoster();
        return;
      }
      const kdtId = app.getKdtId();
      const {
        sl,
        salesmanAvatar,
        nickname,
        backgroundUrl,
      } = this.data.rightsInfo;
      fetchPoster({
        type: 'moment',
        page: 'pages/common/blank-page/index',
        scene: JSON.stringify({
          kdtId,
          page: 'packages/salesman/zone/home/<USER>',
          guestKdtId: kdtId,
          ...getSalesmanParamsObject({ sls: sl }),
        }),
        avatar: salesmanAvatar,
        nickname,
        backgroundImg: backgroundUrl,
        width: 292,
        height: 422,
      }).then((res) => {
        loadImage(res.value)
          .then((tempFile) => {
            this.setYZData(
              {
                poster: tempFile,
              },
              () => wx.hideToast()
            );
            this.handleTogglePoster();
            // 缓存海报
            this.zonePosterUrl = tempFile;
          })
          .catch((err) => {
            wx.showToast({
              title: err || '保存失败，请检查保存到相册的权限',
              icon: 'error',
            });
          });
      });
    },

    handleSavePoster() {
      const { poster } = this.data;
      wx.showLoading({ title: '保存中' });
      this.save(poster).then(() => {
        this.handleTogglePoster();
      });
    },

    zonePublish() {
      wx.navigateTo({
        url: `/packages/salesman/zone/editor/index?itemId=${this.data.goodsId}`,
      });
    },

    wx() {
      const { seller } = this.data.shareData;
      setLogger(SHARE_LOGGER_CONFIG.nativeCustom, { sl: seller });
      this.setYZData(
        {
          showPopup: false,
          showPopupOverlay: false,
        },
        {
          immediate: true,
        }
      );
    },
  },
});
