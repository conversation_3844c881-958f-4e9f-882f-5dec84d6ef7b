import <PERSON><PERSON><PERSON><PERSON><PERSON>Logger from '@/bootstrap/skynet-report';

const app = getApp();

const defaultExtra = {
  kdtId: app.getKdtId(),
  type: 'weapp',
  buyerId: app.getBuyerId()
};

const monitor = (config) => {
  try {
    ZanWeappLogger.monitor({
      appName: 'wsc-h5-salesman',
      logIndex: 'wsc_wap_salesman_share_track',
      topic: 'front',
      extra: {
        ...defaultExtra,
        ...(config.extra || {})
      }
    });
  } catch (e) {
    console.log(e);
  }
};

const setLogger = (logData, params = {}) => {
  const kdtId = app.getKdtId();
  const { sl } = params;
  const { params: logParams = {} } = logData;
  const SALESMAN_LOGGER_INDEX = `${kdtId}-${sl}`;
  logData.params = { ...logParams, ...params, mark: SALESMAN_LOGGER_INDEX };
  app.logger.log(logData);
};

export { monitor, setLogger };
