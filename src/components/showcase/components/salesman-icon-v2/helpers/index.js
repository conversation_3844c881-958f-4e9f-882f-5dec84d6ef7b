import navigate from '@/helpers/navigate';
import { fetchUltraCodePost } from '@/helpers/fetch';
import { node as request } from 'shared/utils/request';
import authorize from '@/helpers/authorize';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';

const app = getApp();

function createFetchPosterFactory() {
  let retry = 0;
  return function fetchPoster(url, data) {
    if (!data) {
      data = url;
      url = '/wscsalesman/poster-service/create-poster.json';
    }
    return request({
      path: url,
      query: data
    }).catch((err) => {
      if (retry === 0) {
        retry = 1;
        return fetchPoster(url, {
          ...data,
          retry
        });
      }
      return Promise.reject(err);
    });
  };
}

export const fetchPoster = createFetchPosterFactory();

function loadImage(src) {
  return new Promise((resolve, reject) => {
    app.downloadFile({
      url: src,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (e) => {
        reject(e);
      }
    });
  });
}

function saveImage(tempFilePath) {
  return new Promise((resolve, reject) => {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: resolve,
      fail: reject
    });
  });
}

function getCurrentPage() {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  return currentPage;
}

function genCode(path, query = {}) {
  const newQuery = {
    ...query,
    guestKdtId: app.getKdtId(),
    ...getSalesmanParamsObject({ sl: this.data.shareData.seller })
  };
  return fetchUltraCodePost(path, newQuery).catch((err) => {
    wx.showToast({
      title: err.message || err.msg || '获取二维码失败',
      icon: 'none'
    });
  });
}

function getImageInfo(src) {
  return new Promise((resolve) => {
    wx.getImageInfo({
      src,
      success: (bgData) => {
        resolve(bgData);
      }
    });
  });
}

function configAdapter(hideItems, iconList) {
  const list = JSON.parse(JSON.stringify(iconList));
  hideItems.forEach((hideItem) => {
    list.forEach((icon) => {
      if (icon.alias === hideItem) {
        icon.hidden = true;
      }
    });
  });
  return list;
}

function compose(handlers) {
  if (!Array.isArray(handlers)) throw new TypeError('Handlers stack must be an array!');
  // eslint-disable-next-line no-unused-vars
  for (const fn of handlers) {
    if (typeof fn !== 'function') throw new TypeError('Handler must be composed of functions!');
  }
  return function (next) {
    let index = -1;
    return dispatch(0);
    function dispatch(i) {
      if (i <= index) return Promise.reject(new Error('next() called multiple times'));
      index = i;
      let fn = handlers[i];
      if (i === handlers.length) fn = next;
      if (!fn) return Promise.resolve();
      try {
        return Promise.resolve(
          fn(() => {
            return dispatch(i + 1);
          })
        );
      } catch (err) {
        return Promise.reject(err);
      }
    }
  };
}

export {
  navigate,
  authorize,
  loadImage,
  saveImage,
  getCurrentPage,
  genCode,
  getImageInfo,
  configAdapter,
  compose
};
