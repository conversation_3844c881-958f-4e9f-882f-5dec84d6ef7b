const iconList = [
  {
    alias: 'wx',
    title: '分享到微信',
    openType: 'share',
    hidden: false,
    iconUrl: 'https://img01.yzcdn.cn/image/salesman/zone/wechat.png'
  },
  {
    alias: 'zonePublish',
    title: '发布到动态',
    hidden: false,
    iconUrl: 'https://img01.yzcdn.cn/image/salesman/zone/publish.png'
  },
  {
    alias: 'weappCode',
    title: '生成小程序码',
    hidden: false,
    iconUrl: 'https://img01.yzcdn.cn/image/salesman/zone/weapp_code.png'
  },
  {
    alias: 'goodsPoster',
    title: '生成商品海报',
    hidden: false,
    iconUrl: 'https://img01.yzcdn.cn/image/salesman/zone/goods_post.png'
  },
  {
    alias: 'tuwen',
    title: '生成图文卡片',
    hidden: false,
    iconUrl: 'https://img01.yzcdn.cn/weapp/wsc/NdzsNJ.png'
  },
  {
    alias: 'zonePoster',
    title: '生成空间海报',
    hidden: false,
    iconUrl: 'https://img01.yzcdn.cn/image/salesman/zone/zone_poster.png'
  }
];

const getShareClickLogConfig = (shareCmpt) => {
  return {
    et: 'click', // 事件类型
    ei: 'share', // 事件标识
    en: '分享', // 事件名称
    params: {
      share_type: 'salesman',
      share_cmpt: shareCmpt
    }
  };
};

const SHARE_LOGGER_CONFIG = {
  // 自定义按钮唤起原生分享
  nativeCustom: getShareClickLogConfig('native_custom'),
  // 海报
  poster: getShareClickLogConfig('poster'),
  // 复制
  copylink: getShareClickLogConfig('copylink'),
  // 二维码
  qrCode: getShareClickLogConfig('qr_code'),
  // 小程序码
  miniprogram: getShareClickLogConfig('miniprogram'),
  // 保存图文
  savepictures: getShareClickLogConfig('savepictures'),
  clickShare: {
    et: 'click', // 事件类型
    ei: 'sales_money', // 事件标识
    en: '点赚字', // 事件名称
    params: {
      version: 'v1'
    } // 事件参数
  },
  salesMoneyView: {
    et: 'view', // 事件类型
    ei: 'salesmoney_view', // 事件标识
    en: '赚字曝光', // 事件名称
    params: {
      // scenes: 'str',
      // goods_id: 'str',
      version: 'v1'
    } // 事件参数
  }
};

export { iconList, SHARE_LOGGER_CONFIG };
