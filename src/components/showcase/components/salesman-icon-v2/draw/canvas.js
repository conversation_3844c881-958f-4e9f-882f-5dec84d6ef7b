import {
  loadImage,
  getImageInfo
} from '../helpers';
import {
  CANVAS_BG_IMG,
  CANVAS_ID,
  ZONE_BG_IMG
} from '../constant';

function drawImageCode(weappCode) {
  return new Promise(resolve => {
    const ctx = wx.createCanvasContext(CANVAS_ID, this);
    Promise.all([
      loadImage(CANVAS_BG_IMG),
      loadImage(weappCode)
    ]).then(resp => {
      ctx.drawImage(resp[0], 0, 0, 300, 415);
      ctx.fillStyle = '#333';
      ctx.font = '18px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('发现一些好货，邀你一起看看', 150, 44);
      ctx.drawImage(resp[1], 50, 125, 200, 200);
      ctx.fillStyle = '#666';
      ctx.font = '14px Arial';
      ctx.fillText('长按识别小程序码', 150, 347);
      ctx.draw(false, () => {
        resolve();
      });
    });
  });
}

function drawCircleImage(img, x, y, w, h, r, ctx) {
  ctx.save();
  ctx.beginPath();
  ctx.moveTo(x + r, y);
  ctx.arcTo(x + w, y, x + w, y + h, r);
  ctx.arcTo(x + w, y + h, x, y + h, r);
  ctx.arcTo(x, y + h, x, y, r);
  ctx.arcTo(x, y, x + w, y, r);
  ctx.closePath();
  ctx.fill();
  ctx.clip();
  ctx.drawImage(img, x, y, w, h);
  ctx.restore();
}

function drawZonePoster(weappCode, zoneInfo) {
  const {
    backgroundUrl,
    avatar,
    nickname,
    extText = '长按识别小程序码或保存图片'
  } = zoneInfo;
  return new Promise(resolve => {
    const ctx = wx.createCanvasContext(CANVAS_ID, this);
    Promise.all([
      loadImage(backgroundUrl.replace(/https{0,1}/, 'https')),
      loadImage(ZONE_BG_IMG),
      loadImage(weappCode),
      loadImage(avatar.replace(/https{0,1}/, 'https')),
    ])
      .then(data => {
        const [backgroundImage, zoneBgImg, weappCodeImage, avatarImage] = data;
        getImageInfo(backgroundImage).then(bgData => {
          const { width, height } = bgData;
          const bgHeight = (260 * height) / width;
          ctx.drawImage(backgroundImage, 20, 20, 260, bgHeight);
          ctx.drawImage(zoneBgImg, 0, 0, 300, 431);
          drawCircleImage(avatarImage, 36, 140, 40, 40, 2, ctx);
          ctx.drawImage(weappCodeImage, 95, 264, 110, 110);

          ctx.fillStyle = '#333';
          ctx.font = '14px PingFang bold';
          ctx.fillText(`${nickname}的空间`, 90, 165);

          ctx.fillStyle = '#333';
          ctx.font = '16px PingFang bold';
          ctx.textAlign = 'center';
          ctx.fillText(`${nickname}的空间`, 150, 230);

          ctx.fillStyle = '#969799';
          ctx.font = '12px PingFang';
          ctx.textAlign = 'center';
          ctx.fillText('好货多多，常来逛逛', 150, 250);

          ctx.fillStyle = '#C8C9CC';
          ctx.font = '12px Arial';
          ctx.textAlign = 'center';
          ctx.fillText(extText, 150, 400);

          ctx.draw(false, () => {
            resolve();
          });
        });
      });
  });
}

export {
  drawImageCode,
  drawZonePoster
};
