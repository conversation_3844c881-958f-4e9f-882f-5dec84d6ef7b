import {
  drawImageCode,
  drawZonePoster
} from './canvas';
import { CANVAS_ID } from '../constant';

function createTempPath() {
  return new Promise((resolve, reject) => {
    wx.canvasToTempFilePath(
      {
        canvasId: CANVAS_ID,
        success: res => {
          resolve(res.tempFilePath);
        },
        fail: reject,
        complete: () => {
          wx.hideLoading();
        }
      },
      this
    );
  });
}

export {
  drawImageCode,
  drawZonePoster,
  createTempPath
};
