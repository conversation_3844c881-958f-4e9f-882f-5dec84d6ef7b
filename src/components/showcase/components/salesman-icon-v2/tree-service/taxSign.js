import { node as request } from 'shared/utils/request';

function getTaxSignInfo() {
  return request({
    path: '/wscump/salesman/tax-signed.json',
  });
}

function checkTaxSign(next) {
  getTaxSignInfo()
    .then(data => {
      wx.hideLoading();
      const { taxServiceState, userSigned, authDeadline, authDeadlineOver } = data;
      if (taxServiceState && !userSigned) {
        let signText;
        if (authDeadlineOver) {
          signText = '由于商家开启了个税代缴服务，需要认证升级后才可使用，你还未完成信息认证，商家无法给你发放佣金。';
        } else {
          signText = `由于商家开启了个税代缴服务，需要认证升级后才可使用，请在 ${authDeadline} 前完成认证升级，否则商家无法给你发放佣金。`;
        }
        this.setYZData({
          showTaxSign: true,
          signText,
        });
      } else {
        next();
      }
    }).catch(() => {
      wx.hideLoading();
      next();
    });
}

export default checkTaxSign;
