<view>
  <view wx:if="{{ linkType === 1 }}" class="allarea-link" bind:tap="handleAllAreaLink"></view>
  <block
    wx:if="{{ linkType == 2 && linkConfig.hotAreas && linkConfig.hotAreas.length }}"
  >
    <cap-navigator
      wx:for="{{ linkConfig.hotAreas }}"
      wx:for-item="hotarea"
      wx:key="unique"
      data-hotarea="{{ hotarea }}"
      link-type="{{ hotarea.linkType }}"
      app-id="{{ hotarea.extraData.otherWeappAppid }}"
      path="{{ hotarea.extraData.otherWeappLink }}"
      use-short-link="{{ (hotarea.extraData.useShortLink === '1' || hotarea.extraData.useShortLink === 1) ? 1 : 0 }}"
      short-link="{{ hotarea.extraData.shortLink }}"
      style="position: absolute; left: {{ hotarea.left }}vw; top: {{ hotarea.top }}vh; width: {{ hotarea.width }}vw; height: {{ hotarea.height }}vh;"
      custom-style="position: absolute; left: 0; right: 0; top: 0; bottom: 0;"
      bind:navigate="handleHotareaNavigate"
    />
  </block>
</view>