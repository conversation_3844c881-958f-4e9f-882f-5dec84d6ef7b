import WscComponent from 'pages/common/wsc-component/index';
import jumpLink from 'shared/components/showcase/utils/jumpLink';

WscComponent({
  properties: {
    linkType: {
      type: Number,
      value: 0,
    },
    linkConfig: {
      type: Object,
      value: {},
    },
  },

  methods: {
    handleAllAreaLink() {
      this.triggerEvent('video-ended');
      jumpLink(this.data.linkConfig, true);
    },
    handleHotareaNavigate(event) {
      this.triggerEvent('video-ended');
      const { hotarea = {} } = event.currentTarget.dataset;
      jumpLink(hotarea, true);
    },
  },
});
