.input-number {
  height: 30px;
}
.input-number__icon-minus,
.input-number__icon-add {
  display: inline-block;
  height: 22px;
  width: 22px;
  top: 4px;
  background-size: 22px 22px;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  padding-left: 12px;
}

.input-number__icon-minus {
  background-image: url('https://img01.yzcdn.cn/weapp/wsc/YpG3BGPk.png');
}

.input-number__icon-add {
  background-image: url('https://img01.yzcdn.cn/weapp/wsc/AHGGbQli.png');
}

.input-number__icon-minus--disabled {
  background-image: url('https://img01.yzcdn.cn/weapp/wsc/Tpq7ijUh.png');
}

.input-number__icon-add--disabled {
  background-image: url('https://img01.yzcdn.cn/weapp/wsc/mxGfjOuH.png');
}

.input-number__value {
  display: inline-block;
  line-height: 30px;
  min-width: 14px;
  font-size: 14px;
  color: #333;
  text-align: center;
}
