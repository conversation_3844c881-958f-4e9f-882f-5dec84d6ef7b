import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    step: {
      type: Number,
      value: 1
    },
    min: {
      type: Number,
      value: 1
    },
    max: {
      type: Number,
      value: Number.MAX_VALUE
    },
    value: {
      type: Number,
      value: 1
    },
    isValueShow: {
      type: Boolean,
      value: true
    },
    isAddShow: {
      type: Boolean,
      value: true
    },
    isMinusShow: {
      type: Boolean,
      value: true
    },
    disabled: {
      type: Boolean,
      value: false
    }
  },

  methods: {
    handleNumChange(e, type) {
      const { step, min, max } = this.data;
      let value = this.data.value;

      if (type === 'minus') {
        value -= step;
      } else if (type === 'plus') {
        value += step;
      }

      if (value < min || value > max) {
        this.triggerEvent('overlimit', { value, type });
        return;
      }

      this.triggerEvent('change', { value, type });
    },

    minusNum(e) {
      this.handleNumChange(e, 'minus');
    },

    addNum(e) {
      if (this.data.disabled) {
        return;
      }
      this.handleNumChange(e, 'plus');
    }
  }
});
