<view class="input-number">
  <view
    wx:if="{{ !disabled && isMinusShow }}"
    class="input-number__icon-minus {{ value <= min ? 'input-number__icon-minus--disabled' : '' }}"
    catch:tap="minusNum"
  ></view>
  <view
    wx:if="{{ isValueShow }}"
    class="input-number__value"
  >
    {{ value }}
  </view>
  <view
    wx:if="{{ isAddShow }}"
    class="input-number__icon-add {{ (disabled || value >= max) ? 'input-number__icon-add--disabled' : '' }}"
    catch:tap="addNum"
  ></view>
</view>
