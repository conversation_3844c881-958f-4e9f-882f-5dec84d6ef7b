@import './style/mixins/index.scss';

.salesman-icon {
  height: 48px;
  width: 48px;
  position: fixed;
  bottom: 115px;
  right: 10px;
  z-index: 140;

  /** z-index 顺序 修改请查看 docs/z-index.md **/

  image {
    height: 48px;
    width: 48px;
  }
}

.goaway {
  transform: translate3d(100px, 0, 0);
}

.panel {
  position: relative;
  width: 100%;
  box-sizing: border-box;

  &-close {
    position: absolute !important;
    top: 16px;
    right: 16px;
    font-size: 22px !important;
    color: #999;
    z-index: 1000;
  }

  &-icon {
    width: 100%;
    white-space: nowrap;

    &--btn {
      display: inline-block;
      width: 168rpx;
      height: 168rpx;
      line-height: 0;
      text-align: center;
      background: transparent;
      padding: 0;

      text {
        display: block;
        margin-top: 10px;
        line-height: 1.2;
        font-size: 11px;
        color: #999;
      }

      image {
        width: 52px;
        height: 52px;
        display: block;
        margin: 0 auto;
      }

      &::after {
        display: none;
      }

      &:first-of-type {
        margin-left: 20px;
      }
    }

    &-flex {
      display: flex;
      justify-content: space-around;
      flex-wrap: nowrap;
      margin-top: 20px;
      width: 100%;
    }
  }

  &-bottom {
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
    font-size: 14px;
    color: #333;
    text-align: center;
    margin: 14px 0;

    &--btn {
      border-color: #dcdee0;
      margin: 0 8px;
      height: 36px !important;
      line-height: 36px !important;
    }

    &--btn__block {
      border-color: #dcdee0;
      margin: 0 auto;
      display: block;
      width: 200px;
    }
  }
}

.poster-canvas {
  position: fixed;
  top: -10000px;
  width: 300px;
  height: 415px;
}

.task-sign {
  width: 270px;
  background: #fff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;

  &__banner {
    width: 180px;
    height: 100px;
    margin: 24px 0;
  }

  &__text {
    font-size: 13px;
    color: #323233;
    text-align: center;
    line-height: 20px;
    width: 223px;
  }

  &__btn {
    width: 200px;
    height: 36px;
    line-height: 36px;
    border-radius: 18px;
    font-size: 14px;
    color: #fff;
    margin: 24px 0 16px;
    text-align: center;
    background-image: linear-gradient(135deg, #ffaa1e 0%, #ff720d 100%);
    box-shadow: 0 3px 6px 0 rgba(255, 114, 13, 0.2);
  }

  &__next {
    font-size: 13px;
    color: #969799;
    text-align: center;
    margin-bottom: 24px;
  }
}

.frame-special {
  height: 90vh;
}

.frame {
  position: relative;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &-header {
    position: relative;
    padding: 16px 0 8px;
    text-align: center;
    box-sizing: border-box;

    &__panel-title {
      font-size: 14px;
      line-height: 20px;
      color: #323233;

      > .span {
        color: #ed6a0c;
        font-weight: bolder;
      }
    }

    &__panel-desc {
      font-size: 12px;
      line-height: 16px;
      margin: 4px 0;
      color: #969799;
    }
  }

  &-content {
    flex: 1;
  }
}

.tab {
  &-content {
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: scroll;
  }

  &-promote {
    flex: 1;
    overflow: hidden;
  }
}

.sm-icon-list-row {
  position: relative;
  display: flex;
  padding: 16px 0 16px 8px;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;

  &--border::before {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    top: 0;
    right: 0;
    left: 16px;
    border-top: 1px solid #ebedf0;
    transform: scaleY(0.5);
  }

  &::-webkit-scrollbar {
    height: 0;
  }

  &__option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    user-select: none;
    background: transparent;
    padding: 0;

    &:active {
      opacity: 0.7;
    }

    &::after {
      display: none;
    }
  }

  &__icon {
    width: 48px;
    height: 48px;
    margin: 0 16px;
  }

  &__name {
    max-width: 72px;
    margin-top: 8px;
    padding: 0 4px;
    color: #646566;
    line-height: 12px;
    font-size: 12px;

    @include ellipsis;
  }
}

.tips-wrap {
  display: flex;
  justify-content: center;
  padding: 24px 0 14px;
}

.tips-content {
  display: flex;
  flex-direction: column;
  justify-items: center;
}

.tip-item {
  display: flex;
  align-items: center;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #323233;
  line-height: 20px;
  margin-bottom: 10px;
}

.tip-text {
  margin-left: 8px;
}

.error-text {
  color: #ee0a24;
}

.assistant-pop {
  text-align: center;
  font-size: 14px;

  &__title {
    font-size: 16px;
    font-weight: 500;
    line-height: 48px;
  }

  &__subtext {
    color: #39393a;
    margin: 18px 0 8px;
  }

  &__img {
    width: 200px;
    height: 200px;
  }

  &__action {
    margin: 8px 0 16px;
  }

  &__confirm {
    padding: 8px 16px;
  }
}

.sub-guide {
  width: 213px;
  height: 156px;
}


.wechat-tips {
  &__dialog {
    width: 622rpx;
    padding: 34rpx 0 22rpx;
  }

  &__desc {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 32rpx;
    font-weight: bolder;
    color: #323233;
    line-height: 44rpx;
  }

  &__img {
    display: block;
    width: 548rpx;
    height: 290rpx;
    margin: 40rpx auto 0;
  }
}
