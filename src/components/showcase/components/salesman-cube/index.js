/* eslint-disable @youzan/dmc/wx-check */
import WscComponent from 'pages/common/wsc-component';
import Theme from 'shared/common/components/theme-view/theme';
import { node as request } from 'shared/utils/request';
import mkc from '@youzan/weapp-utils/lib/map-keys-case';
import tabBehavior from '@/custom-tab-bar-v2/tab-behavior';
import openWebView from 'utils/open-web-view';
import { checkIsChannels } from 'shared/utils/channel';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import IconController from '../floating-nav/icon-controller/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import findTabBarIndex from '@youzan/wsc-tab-bar-utils/lib/weapp/findTabBarIndex';
import formatCommission from '@youzan/salesman-cube-core-utils/lib/formatCommission';
import useWxImageShare from '@youzan/decorate-tee/src/common/utils/use-wx-image-share';

import {
  getCurrentPage,
  genCode,
  navigate,
  loadImage,
  compose,
  fetchPoster,
  getTipsData,
  getGuideSourceType,
  guideReplaceCube,
  filterZoneCube,
} from './helpers';
import { monitor, setLogger } from './helpers/log';
import guideTimeLogger from './helpers/guide-time-logger';
import { drawImageCode, createTempPath } from './draw';
import {
  cubeConfig,
  guideCubeConfig,
  getIconMap,
  freeDisableList,
  promoteDisableList,
  SHARE_LOGGER_CONFIG,
  GUIDE_PROMOTE_GOODS_SCENES,
  SPACE_ABILITY_NAME,
} from './config';
import { subscribeMessage, checkTaxSign } from './tree-service';
import { getGuideConfig } from './begin-boarding/getConfig';
import { SalesmanCubeCore } from '@youzan/sales-icon-utils';
import { getGoodsPosterInfoJson, getPosterByYunExtension } from './api';
import {
  TIME_LOG_KEY_MAP,
  DecoratePage,
  UrlPatternMap,
  ShareCardTypeMap,
} from './constant';
import { TAB_CONTAINER_RULE } from '@youzan/wsc-tab-bar-utils/lib/constants';
import Args from '@youzan/weapp-utils/lib/args';

const app = getApp();

const systemInfo = app.getSystemInfoSync();

/** 秒杀 */
const SECKILL_TYPE = 6;

const coreInstance = new SalesmanCubeCore({
  kdtId: app.getKdtId,
  buyerId: app.getBuyerId,
  buildEnv: 'weapp',
  runEnv: 'weapp',
  shareInfo: {},
  goodsInfo: {},
  shareData: {},
  rightsInfo: {},
  nativeAjax: request,
  nativeAjaxUseCdn: app.requestUseCdn,
  nativeMonitor: monitor,
  nativeNavigate: navigate.navigate,
  nativeLogger: (...data) => app.logger.log(data),
  nativeYunExtension: getPosterByYunExtension,
  path: getCurrentPage().route,
  query: getCurrentPage().options || {},
});

WscComponent({
  options: {
    multipleSlots: true,
  },

  behaviors: [tabBehavior],

  properties: {
    pageQuery: Object,
    // 自定义icon展示
    customIcon: {
      type: Boolean,
      value: false,
    },
    // 调用页面外部海报方法
    usePagePoster: {
      type: Boolean,
      value: false,
    },
    // 隐藏商品海报
    hideGoodsPoster: {
      type: Boolean,
      value: true,
    },
    // 自定义分享数据，目前只针对生成分销员小程序二维码
    customData: {
      type: Object,
      value: null,
    },
    cube: {
      type: Array,
      value: [],
    },
    special: {
      type: Boolean,
      value: false,
    },
    // 是否更新当前path为商品信息中的链接
    needUpdatePath: {
      type: Boolean,
      value: false,
    },
    // 佣金是否使用从外部传入的值
    useExternalProfit: {
      type: Boolean,
      value: false,
    },
    // 获取分享信息时机: created 挂载就获取分享信息 | show 打开面板时获取分享信息
    getShareOpportunity: {
      type: String,
      value: 'created',
    },
    goodsInfo: {
      type: Object,
      value: {},
      observer(info, oldInfo) {
        const id = info.id || info.goodsId;
        const goodsSku = info.goodsSkuData || {};
        coreInstance.update('goodsInfo', info);
        const { getShareOpportunity } = this.properties;
        // 本次新增了点击赚字后触发分享信息的请求 created时是原逻辑
        if (getShareOpportunity === 'created') {
          if (id) {
            this.updateCommissionDataById(id, goodsSku.list);
          }
          if (info.id && !oldInfo.id && !checkIsChannels()) {
            this.getShareData();
          }
        }
      },
    },
    // 赚字内部区分业务场景，track埋点 场景值
    scenes: {
      type: String,
      value: 'default',
    },
    // 赚字灰度名称
    grayName: {
      type: String,
      value: '',
    },
    // 是否需要绑定
    needBindRelation: {
      type: Boolean,
      value: true,
    },
  },

  data: {
    cubeVisible: false, // 赚字是否显示
    shareData: {},
    iconUrl: '',
    showPopup: false,
    rightsInfo: {},
    commission: 0,
    commissionRange: [],
    hasCommissionRange: false,
    commissionRangeTextInfo: {},
    iconList: [],
    weappCode: '',
    showWeappCode: false,
    poster: '',
    featurePosterData: '',
    showFeaturePoster: false,
    showPoster: false,
    showPopupOverlay: true,
    popupBottom: 0,
    showTaxSign: false,
    showSaveTip: false,
    signText: '',
    active: 0,
    popupHeight: 0,
    themeMainBgColor: '',
    showGuide: false,
    guideConfig: [],
    showAssistant: false,
    // 是否是视频号
    isChannels: checkIsChannels(),
    // 智能文案内容
    smartTextData: undefined,
    // 页面onshow时是否需要再绑定一次
    hasBind: false,
    // 弹窗是否打开过
    hasOpenPopup: false,
    shortLinkPromoteUrl: '', // 推广的shortLink链接（即小程序链接）
    personalSpaceAbility: false,
    isShowGif: false,
    // 是否使用微信图片分享能力
    isWxShareImg: false,
    // 是否已经获取微信分享能力
    hasWxshareRes: false,
    // 商详海报信息
    goodsPosterInfo: undefined,
    // 是否是企微小程序环境
    isWecomMPEnv: systemInfo.environment === 'wxwork',
    showShareWxTips: false,
  },

  attached() {
    // 视频号不展示悬浮窗
    if (checkIsChannels()) return;
    coreInstance.batchUpdate({
      path: getCurrentPage().route,
      query: getCurrentPage().options,
    });
    const { getShareOpportunity, scenes } = this.properties;
    if (this.data.hideGoodsPoster && getShareOpportunity === 'created') {
      // 商品使用信息需要在拿到商品信息后再获取share信息
      this.getShareData();
    }
    const option = wx.getLaunchOptionsSync();
    // 导购推广商品场景不需要调用
    if (option.scene !== 1154 && scenes !== GUIDE_PROMOTE_GOODS_SCENES) {
      this.checkShopRights();
    }
  },

  pageLifetimes: {
    show() {
      this.bindRelation();
    },
  },

  ready() {
    this.bindRelation();
    this.getPopHeight();
    Theme.getThemeColor('main-bg').then((color) => {
      this.setYZData({
        themeMainBgColor: color,
      });
    });
  },

  methods: {
    getPopHeight() {
      const that = this;
      wx.getSystemInfo({
        success(res) {
          that.setYZData({
            popupHeight: res.windowHeight * 0.9,
          });
        },
      });
    },
    observerShareData(shareData) {
      // 优先使用自定义图标
      const { iconUrl } = shareData;
      this.setYZData({
        iconUrl: cdnImage(iconUrl, '!100x100.jpg'),
      });

      if (this.data.shareData.share) {
        if (!this.data.hasWxshareRes) {
          useWxImageShare({ kdtId: app.getKdtId() }).then((res) => {
            this.setYZData({ isWxShareImg: res, hasWxshareRes: true });
          });
        }

        if (!this.data.goodsPosterInfo) {
          this.getGoodsPosterInfo();
        }
      }
    },

    initConfig(shareData = {}) {
      const kdtId = app.getKdtId();
      return coreInstance
        .getShopAbility(kdtId, SPACE_ABILITY_NAME)
        .then((res) => {
          this.setYZData({
            personalSpaceAbility: res,
          });
        })
        .finally(() => {
          const config = this.formatCubeConfig(shareData, this.data.cube);
          const iconMap = getIconMap({
            shareData,
            kdtId,
            isWecomMPEnv: this.data.isWecomMPEnv,
          });
          const iconOptions = this.getIconOptions(config, iconMap);
          this.setYZData({
            iconList: iconOptions,
          });
        });
    },

    getIconOptions(config, iconMap) {
      return config.map((item) => {
        if (Array.isArray(item)) {
          return this.getIconOptions(item, iconMap);
        }
        return iconMap[item];
      });
    },

    // 获取合并处理后cubeconfig
    formatCubeConfig(shareData, globalCube) {
      let config =
        globalCube.length > 0 ? globalCube : this.getCubeConfigByScenes();

      const { allowMoments, allowGoodsRecommend, isShoppingGuide } = shareData;
      // 免费版本去除不支持的封装功能，为什么不直接获取版本判断
      // 导购身份跳过此过滤
      if (!isShoppingGuide && !allowMoments) {
        config = config.filter((item) => {
          return !(freeDisableList.indexOf(item) > 0);
        });
      }
      // 去除推广页入口
      if (!allowGoodsRecommend) {
        config = config.filter((item) => {
          return !(promoteDisableList.indexOf(item) > 0);
        });
      }
      // 过滤导购不支持cube
      if (isShoppingGuide) {
        config = guideReplaceCube(config);
      }

      if (!this.data.personalSpaceAbility) {
        config = filterZoneCube(config);
      }
      return config;
    },

    // 获取内置scenes config配置
    getCubeConfigByScenes() {
      const { scenes, shareData, isWecomMPEnv } = this.data;
      const scenesConfig = shareData.isShoppingGuide
        ? guideCubeConfig[scenes] || guideCubeConfig.default
        : cubeConfig[scenes] || cubeConfig.default;

      const { weapp, wecom } = scenesConfig;

      // 企微小程序，且有 wxcom 配置，则取 wxcom 配置
      if (isWecomMPEnv && wecom) {
        return wecom;
      }

      return weapp;
    },

    setIcon(shareData) {
      if (shareData.share || shareData.freedomShare) {
        this.iconController = new IconController()?.setIcon(this, {
          priority: 90,
          height: 48,
          cb: [
            (bottom) => {
              this.setYZData({ bottom: bottom + 'px' });
            },
            (goaway) => {
              this.setYZData({ goaway });
            },
          ],
        });
      }
    },

    getShareData() {
      const { goodsInfo, grayName } = this.data;
      const query = {
        type: 'NORMAL',
        grayName,
      };
      if (goodsInfo.id) {
        query.goodsId = goodsInfo.id;
      }

      const { route } = getCurrentPage();
      monitor({
        extra: {
          path: route,
          url: '/wscump/salesman/share.json',
        },
      });

      return request({
        path: '/wscump/salesman/share.json',
        query,
        config: {
          cache: true,
          expire: 10,
        },
      }).then((data) => {
        const shareData = mkc.toCamelCase(data);
        shareData.isShoppingGuide = shareData.salesmanType === 1;
        const isNeedShareMore = data.is_need_share_more !== false;
        if (isNeedShareMore) {
          this.triggerEvent('set-share', {
            shareData,
          });
        }
        coreInstance.update('shareData', shareData);
        const cubeVisible = shareData.share || shareData.freedomShare;
        this.setData({
          shareData,
          cubeVisible,
        });
        this.handleLogViewLog(shareData);
        this.observerShareData(shareData);
        this.setIcon(shareData);
        return this.initConfig(shareData);
      });
    },

    bindCustomer(sellerFrom, fromParams) {
      const { route, options } = getCurrentPage();
      const currentPage = Args.add(route, options);
      const guideBindSourceType = getGuideSourceType(this.data.scenes);
      monitor({
        extra: {
          url: '/wscsalesman/bind-customer.json',
          path: currentPage,
          sellerFrom,
          guideBindSourceType,
          fromParams,
        },
      });
      return request({
        path: '/wscsalesman/bind-customer.json',
        method: 'POST',
        data: {
          sellerFrom,
          guideBindSourceType,
          fromParams,
          sourceUrl: currentPage,
        },
      });
    },

    bindRelation() {
      if (this.data.hasBind) return;
      const { options } = getCurrentPage();
      // 兼容老版小程序为sls
      const { sl, sls, from_params: fromParams } = options;
      const sellerFrom = sl || sls;
      if (sellerFrom && this.data.needBindRelation) {
        this.changeBindStatus();
        this.bindCustomer(sellerFrom, fromParams).catch(() => {
          // 绑定异常情况，重试一次
          this.bindCustomer(sellerFrom, fromParams);
        });
      }
    },
    // 改变hasBind状态
    changeBindStatus() {
      this.setYZData({
        hasBind: true,
      });
    },

    /**
     * 获取权限信息
     */
    checkShopRights() {
      request({
        path: '/wscump/salesman/shop/checkShopRights.json',
      }).then((res) => {
        this.setYZData({
          rightsInfo: res,
        });
        // 没有空间权限不显示是空间相关
        if (!res.allowMoments) {
          // const list = [];
          // this.data.iconList.forEach((index, list) => {
          //   list[index] = configAdapter(['zoom'], this.data.iconList[index]);
          // });
          // this.setYZData({
          //   iconList: list
          // });
        }
      });
    },

    checkIcon(alias) {
      const { iconList } = this.data;
      return iconList?.flat()?.some((item) => item.alias === alias);
    },

    handlers() {
      const { shareData } = this.data;
      // 是否有小程序链接icon: 有 则进行小程序链接的获取
      const hasWeappLinkIcon = this.checkIcon('weappLink');
      // 是否有小程序主页链接icon: 有 则进行小程序主页链接的获取
      const hasWeappHomeLinkIcon = this.checkIcon('weappHomeLink');
      // 是否有微页面链接icon: 有 则进行微页面链接的获取
      const hasWeappFeatureLinkIcon = this.checkIcon('weappFeatureLink');
      const emptyFun = (next) => {
        wx.hideLoading();
        next();
      };
      const fns = [
        subscribeMessage.bind(this),
        // 导购跳过个税认证弹窗
        shareData.isShoppingGuide ? emptyFun : checkTaxSign.bind(this),
        // 获取小程序商详链接
        hasWeappLinkIcon ? this.getWeappLink.bind(this) : emptyFun,
        // 获取小程序主页链接
        hasWeappHomeLinkIcon ? this.getWeappHomeLink.bind(this) : emptyFun,
        // 获取小程序微页面链接
        hasWeappFeatureLinkIcon
          ? this.getWeappFeatureLink.bind(this)
          : emptyFun,
        this.getSmartTextData.bind(this),
        this.getCommissionData.bind(this),
        this.handleTogglePanel.bind(this),
        this.checkBeginBoarding.bind(this),
      ];
      return compose(fns);
    },

    getCommissionData(next) {
      const goodsInfo = this.data.goodsInfo || {};
      const goodsSkuData = goodsInfo.goodsSkuData || {};
      const id = goodsInfo.id || goodsInfo.goodsId;
      if (this.properties.useExternalProfit) {
        // 导购推广商品 佣金从外部传入
        this.setYZData({ commission: goodsInfo.profit / 100 });
        next();
      } else if (id) {
        // 每次都要查，防止导购预计赚不准
        this.updateCommissionDataById(id, goodsSkuData.list)
          .then(() => next())
          .catch(() => next());
      } else {
        next();
      }
    },

    updateCommissionDataById(id, skuList) {
      const goodsInfo = this.data.goodsInfo || {};
      const shareData = this.data.shareData || {};

      const { goodsPrice = {} } = goodsInfo;
      const { minPrice = 0 } = goodsPrice;
      if (shareData.isShoppingGuide) {
        return coreInstance
          .getShareMoney(id, skuList, { minPrice })
          .then((data) => {
            this.setYZData({ commission: data.value / 100 });
          });
      }
      return coreInstance.getShareMoneyRange(id, skuList).then((data) => {
        const { maxCommissionStr, compatibleCommission } =
          formatCommission(data);
        const { profitRange } = data;
        this.setYZData({
          commissionRange: profitRange,
          hasCommissionRange:
            !profitRange.every((o) => +o === 0) &&
            profitRange.length &&
            maxCommissionStr,
          commissionRangeTextInfo:
            profitRange.length === 2
              ? {
                  text: `分享后预计可赚 `,
                  commission: compatibleCommission,
                }
              : {
                  text: `分享后预计最高赚 `,
                  commission: maxCommissionStr,
                },
        });
      });
    },

    checkBeginBoarding() {
      // 检查是否需要显示新人引导，如果需要显示，那就显示新人引导
      request({
        path: '/wscump/salesman/guide/is-success-guide.json',
        data: {
          type: 14,
        },
      }).then((data) => {
        if (!data.value) {
          // 没有弹出过弹框
          this.setYZData({
            guideConfig: getGuideConfig({
              iconList: this.data.iconList,
              special: this.data.special,
            }),
            showGuide: true,
          });
        }
      });
    },

    getShortLink({ next, pageUrlParams = {}, reqParams = {} }) {
      coreInstance
        .getShortLink(pageUrlParams, reqParams)
        .then((res) => {
          this.setYZData({
            shortLinkPromoteUrl: res.miniProgramUrl,
          });
          next();
        })
        .catch(next);
    },

    // 获取小程序商详链接
    getWeappLink(next) {
      const basePath = 'pages/goods/detail/index';
      const { goodsInfo } = this.properties;
      const { activityType, goodsAlias, activityAlias } = goodsInfo || {};
      let queryParams = { alias: goodsAlias };
      // 区分秒杀场景
      if (activityType === SECKILL_TYPE) {
        queryParams = {
          ...queryParams,
          umpType: 'seckill',
          activityType: 'seckill',
          umpAlias: activityAlias,
        };
      }
      coreInstance.update('path', basePath);
      this.getShortLink({
        next,
        pageUrlParams: queryParams,
      });
    },

    /** 获取小程序微页面链接 */
    getWeappFeatureLink(next) {
      this.getShortLink({
        next,
        reqParams: { pageTitle: '微页面' },
      });
    },

    /** 获取小程序店铺主页链接 */
    getWeappHomeLink(next) {
      this.getShortLink({
        next,
        reqParams: { pageTitle: '小程序主页' },
      });
    },

    getSmartTextData(next) {
      const { goodsInfo, isShoppingGuide } = this.data;
      const goodsId = goodsInfo ? goodsInfo.id || goodsInfo.goodsId : '';

      if (goodsId && !isShoppingGuide) {
        coreInstance
          .getSmartText({ goodsId })
          .then((smartTextData) => {
            this.setYZData({
              smartTextData: smartTextData || {},
            });
            next();
          })
          .catch(next);
      } else {
        next();
      }
    },

    // 赚字点击事件
    handleIconTap() {
      guideTimeLogger.reset(TIME_LOG_KEY_MAP.SHOW_PANEL);
      guideTimeLogger.reset(TIME_LOG_KEY_MAP.DRAW_POSTER);
      if (this.properties.getShareOpportunity === 'show') {
        // 点击后才展示面板场景: 此时请求分享信息
        guideTimeLogger.startRecord(TIME_LOG_KEY_MAP.SHOW_PANEL);
        wx.showLoading();
        this.setYZData({
          hasOpenPopup: true,
        });
        this.getShareData().then(() => {
          this.handlers()();
        });
        return;
      }
      if (!this.data.shareData.freedomShare) {
        // 赚字非招募入口-正常流程
        wx.showLoading();
        this.setYZData({
          hasOpenPopup: true,
        });
        this.handlers()();
      } else {
        // 赚字入口的授权成功之后
        wx.navigateTo({
          url: '/packages/salesman/tutorial/index',
        });
      }
    },

    handleCert() {
      const { route, options } = getCurrentPage();
      let returnUrl = `/${route}`;
      if (options) {
        returnUrl += '?';
        Object.keys(options).forEach((key, index) => {
          if (index > 0) {
            returnUrl += `&${key}=${options[key]}`;
          } else {
            returnUrl += `${key}=${options[key]}`;
          }
        });
      }
      openWebView('/wscassets/cert/tax/contract', {
        title: '身份证认证',
        query: {
          fromId: 12,
          returnUrl: decodeURIComponent(returnUrl),
        },
      });
    },

    handleClose() {
      this.setYZData(
        {
          showTaxSign: false,
        },
        () => {
          this.handleTogglePanel();
        }
      );
    },

    handleTogglePanel(next) {
      const showPopup = !this.data.showPopup;
      if (showPopup) {
        this.handleLog(SHARE_LOGGER_CONFIG.clickShare);
        this.showPopupLog(SHARE_LOGGER_CONFIG.showPanel);
      }
      this.setYZData({
        showPopup,
        showPopupOverlay: showPopup || this.data.showPopupOverlay,
      });
      if (typeof next === 'function') {
        next();
      }
    },

    handleLog(logData) {
      const { scenes, shareData } = this.data;
      const { seller } = shareData;
      setLogger(logData, {
        sl: seller,
        scenes,
        salesman_type: shareData.salesmanType,
      });
    },

    showPopupLog(logData) {
      const { shareData } = this.data;
      guideTimeLogger.endRecordAndReportLog(TIME_LOG_KEY_MAP.SHOW_PANEL, {
        logData,
        params: {
          salesman_type: shareData.salesmanType,
        },
      });
    },

    handleLogViewLog(shareData = {}) {
      const { share } = shareData;
      if (share) {
        this.handleLog(SHARE_LOGGER_CONFIG.salesMoneyView);
      }
    },

    // 导购-推广商品-复制小程序链接
    handleCopyWeappLink() {
      // 埋点
      const { weappUrl } = this.data.goodsInfo;
      const { seller, salesmanType } = this.data.shareData;
      setLogger(SHARE_LOGGER_CONFIG.copyWeappLink, {
        sl: seller,
        salesman_type: salesmanType,
        weappUrl,
        shortLinkPromoteUrl: this.data.shortLinkPromoteUrl,
      });
      if (!this.data.shortLinkPromoteUrl) {
        // 接口请求可能失败 此时值是空
        wx.showToast({
          title: '复制失败',
          icon: 'none',
        });
        return;
      }
      wx.setClipboardData({
        data: this.data.shortLinkPromoteUrl,
        success() {
          wx.showToast({
            title: '复制成功',
            icon: 'none',
          });
        },
      });
    },

    // 底部icon点击事件
    handleClickIcon(e) {
      const { alias, config } = e.currentTarget.dataset;
      const { seller, salesmanType } = this.data.shareData;
      this.handleTogglePanel();
      if (['weappLink', 'weappHomeLink', 'weappFeatureLink'].includes(alias)) {
        // 小程序链接复制
        this.handleCopyWeappLink();
      } else if (alias === 'goodsPoster' || alias === 'tuwenPoster') {
        this.genPoster();
      } else if (config.type === 'link') {
        const { goodsActivityInfo: info } = this.data.goodsInfo;
        setLogger(SHARE_LOGGER_CONFIG.clickLinkIcon, {
          sl: seller,
          salesman_type: salesmanType,
          title: config.title,
        });
        const navigateUrl =
          config.alias === 'promote'
            ? `/packages/shop/salesman/promote/index?alias=${
                this.data.goodsInfo?.alias
              }&itemId=${this.data.goodsInfo?.id}&activityAlias=${
                info.activityAlias || ''
              }&activityType=${info.activityType || ''}&activityId=${
                info.activityId || ''
              }&umpType=${info.activityType || ''}`
            : config.weappUrl;
        wx.navigateTo({
          url: navigateUrl,
        });
      } else {
        this[alias] && this[alias]();
      }
    },

    handleClickMoments() {
      navigate.navigate({
        url: '/packages/salesman/zone/home/<USER>',
      });
    },

    handleLinkCenter() {
      navigate.navigate({
        url: `/packages/salesman/salesman-center/index?dsKdtId=${this.data.shareData.dsKdtId}`,
      });
    },

    genPageCode(shareCmpt) {
      const { weappCode, customData, goodsInfo = {} } = this.data;
      return new Promise((resolve) => {
        if (weappCode) {
          return resolve(weappCode);
        }
        let { route, options } = getCurrentPage();
        if (this.data.pageQuery) {
          Object.assign(options, this.data.pageQuery);
        }

        // 自定义二维码生成数据
        if (customData) {
          // 自定义 path
          if (customData.path) {
            route = customData.path;
            delete customData.path;
          }
          // 其他参数
          options = { ...options, ...customData };
        }
        if (shareCmpt) {
          options.share_cmpt = shareCmpt;
        }

        if (goodsInfo.path) {
          route = goodsInfo.path.split('?')[0];
        }

        resolve(genCode.call(this, route, options));
      });
    },

    code() {
      const { seller, salesmanType } = this.data.shareData;
      setLogger(SHARE_LOGGER_CONFIG.miniprogram, {
        sl: seller,
        salesman_type: salesmanType,
      });
      wx.showToast({
        title: '获取小程序码中',
        icon: 'loading',
      });
      if (this.data.weappCode) {
        wx.hideToast();
        this.handleToggleWeappCode();
        return;
      }
      this.genPageCode('miniprogram').then((weappCode) => {
        this.setYZData({
          weappCode: cdnImage(weappCode),
        });
        this.handleToggleWeappCode();
      });
    },

    /**
     * 获取当前微页面的 alias（从tabbar配置获取或者query获取）
     */
    getFeatureAlias() {
      return new Promise((resolve) => {
        const currentRoute = getCurrentPage().route;

        const { list: navList = [] } = app.getNavConfig();
        if (navList.length) {
          resolve(this.getAliasFromNav(navList, currentRoute));
        } else {
          app
            .getNavConfigSync()
            .then((res) => {
              const { list = [] } = res || {};
              // 在没有任何匹配的情况下，默认加载首页
              resolve(this.getAliasFromNav(list, currentRoute));
            })
            .catch((_) => resolve(''));
        }
      });
    },

    /** 匹配navList获取alias */
    getAliasFromNav(list, currentRoute = '') {
      const { alias } = getCurrentPage().options || {};
      let targetAlias = alias;
      const nativeTabBarIndex = findTabBarIndex();
      list.some(({ page_path: pagePath, alias }, index) => {
        // 当前页面路径和底部导航配置中路径一致 || 底部导航配置中替换packages后用主页的pages路径当前页面路径相同 || tabbar顺序和当前路径的索引顺序相同
        // 上诉三个条件有一个为真代表当前路径是被配置为小程序的底部导航,微页面的alias就取配置中的alias
        if (
          currentRoute === pagePath ||
          currentRoute === pagePath.replace(/^pages(-|\/)/, 'packages/') ||
          nativeTabBarIndex === index
        ) {
          this.tabbarIndex = index;
          // id 为老的微页面标志
          // alias 为新的微页面标志
          if (alias) {
            targetAlias = alias;
          }
          return true;
        }
        return false;
      });
      return targetAlias;
    },

    /** 获取微页面类型 */
    getPageType() {
      let pageType = DecoratePage.Home;
      let path = getCurrentPage().route;
      if (TAB_CONTAINER_RULE.test(path)) {
        // 获取当前tab页挂载页面的真实路径
        path = getCurrentPage().attachedPage?.is;
      }
      Object.keys(UrlPatternMap).some((key) => {
        return UrlPatternMap[key].some((p) => {
          if (p.test(path)) {
            pageType = key;
            return true;
          }
          return false;
        });
      });
      return ShareCardTypeMap[pageType];
    },
    /** 增加微页面类型兼容处理 */
    getFeatureParams(alias) {
      let dataParams = {
        pageType: this.getPageType(),
        alias: alias || '',
      };
      // 如果是其他微页面但是未获取到alias降级到首页
      if (
        dataParams.pageType === ShareCardTypeMap[DecoratePage.Other] &&
        !alias
      ) {
        dataParams = {
          pageType: ShareCardTypeMap[DecoratePage.Home],
          alias: '',
        };
      }
      return dataParams;
    },

    featurePoster() {
      const { shareData } = this.data;
      const { seller, salesmanType } = shareData;
      setLogger(SHARE_LOGGER_CONFIG.poster, {
        sl: seller,
        posterType: 'featurePoster',
        salesman_type: salesmanType,
      });
      if (this.data.featurePosterData) {
        this.setYZData({
          featurePosterData: this.data.featurePosterData,
          showFeaturePoster: true,
        });
      } else {
        wx.showLoading();
        this.getFeatureAlias().then((alias) => {
          const dataParams = this.getFeatureParams(alias);
          const sceneParams = {
            alias: dataParams.alias,
            page: getCurrentPage().route,
          };
          if (TAB_CONTAINER_RULE.test(sceneParams.page)) {
            // 获取当前tab页挂载页面的真实路径
            // 分享真实导航路径
            const list = getApp().globalData.tabbarOriginList || [];
            const pathConfig = list[findTabBarIndex()];
            const { pagePath } = pathConfig || {};
            sceneParams.page = pagePath || sceneParams.page;
          }
          coreInstance
            .getFeaturePoster(dataParams, sceneParams)
            .then(({ value }) => {
              wx.hideLoading();
              this.setYZData({
                featurePosterData: value,
                showFeaturePoster: true,
              });
            })
            .catch(() => {
              wx.hideLoading();
              wx.showToast({ title: '生成海报失败', icon: 'error' });
            });
        });
      }
    },

    handleToggleWeappCode() {
      const showWeappCode = !this.data.showWeappCode;
      this.setYZData({
        showWeappCode,
      });
      if (!this.data.showWeappCode) {
        return this.tryOpenAssistantPop();
      }
    },

    handleToggleSaveTip() {
      this.setYZData({
        showSaveTip: !this.data.showSaveTip,
      });
      if (!this.data.showSaveTip) {
        return this.tryOpenAssistantPop();
      }
    },

    handleCompleteGuide() {
      this.setYZData({
        showGuide: false,
      });
      request({
        path: '/wscump/salesman/guide/add-success-guide.json',
        method: 'POST',
        data: {
          type: 14,
        },
      });
    },

    genPoster() {
      wx.showToast({
        title: '正在生成',
        icon: 'loading',
      });
      const { usePagePoster, shareData } = this.data;
      const { seller, salesmanType } = shareData;
      // 页面调用自由的分享卡片海报
      if (usePagePoster) {
        setLogger(SHARE_LOGGER_CONFIG.poster, {
          sl: seller,
          posterType: 'goodsPoster',
          salesman_type: salesmanType,
        });
        this.triggerEvent('share-card');
      } else {
        setLogger(SHARE_LOGGER_CONFIG.poster, {
          sl: seller,
          posterType: 'salesmanPost',
          salesman_type: salesmanType,
        });
        this.showPoster();
      }
    },

    showPoster() {
      if (this.tuwenPoster) {
        this.setYZData(
          {
            poster: this.tuwenPoster,
          },
          () => wx.hideToast()
        );
        this.handleTogglePoster();
        return;
      }
      this.genPageCode('poster').then((weappCode) => {
        drawImageCode
          .call(this, weappCode)
          .then(createTempPath.bind(this))
          .then((src) => {
            this.setYZData(
              {
                poster: src,
              },
              () => wx.hideToast()
            );
            this.handleTogglePoster();
            this.tuwenPoster = src;
          });
      });
    },

    handleTogglePoster() {
      const showPoster = !this.data.showPoster;
      this.setYZData({
        showPoster,
      });
    },

    zonePoster() {
      wx.showToast({
        title: '正在生成',
        icon: 'loading',
      });
      if (this.zonePosterUrl) {
        this.setYZData(
          {
            poster: this.zonePosterUrl,
          },
          () => wx.hideToast()
        );
        this.handleTogglePoster();
        return;
      }
      const kdtId = app.getKdtId();
      const { sl, salesmanAvatar, nickname, backgroundUrl } =
        this.data.rightsInfo;
      fetchPoster({
        type: 'moment',
        page: 'pages/common/blank-page/index',
        scene: JSON.stringify({
          kdtId,
          page: 'packages/salesman/zone/home/<USER>',
          guestKdtId: kdtId,
          ...getSalesmanParamsObject({ sls: sl }),
        }),
        avatar: salesmanAvatar,
        nickname,
        backgroundImg: backgroundUrl,
        width: 292,
        height: 422,
      }).then((res) => {
        loadImage(res.value)
          .then((tempFile) => {
            this.setYZData(
              {
                poster: tempFile,
              },
              () => wx.hideToast()
            );
            this.handleTogglePoster();
            // 缓存海报
            this.zonePosterUrl = tempFile;
          })
          .catch((err) => {
            wx.showToast({
              title: err || '保存失败，请检查保存到相册的权限',
              icon: 'error',
            });
          });
      });
    },

    handleCloseFeaturePoster() {
      this.setYZData({
        showFeaturePoster: false,
      });
    },

    share() {
      const { seller, salesmanType } = this.data.shareData;
      setLogger(SHARE_LOGGER_CONFIG.nativeCustom, {
        sl: seller,
        salesman_type: salesmanType,
      });
      this.setYZData(
        {
          showPopup: false,
          showPopupOverlay: false,
        },
        {
          immediate: true,
        }
      );
    },

    shareInWecom() {
      const { seller, salesmanType } = this.data.shareData;
      setLogger(SHARE_LOGGER_CONFIG.nativeCustom, {
        sl: seller,
        salesman_type: salesmanType,
      });
      this.setYZData(
        {
          showPopup: false,
          showPopupOverlay: false,
          showShareWxTips: true,
        },
        {
          immediate: true,
        }
      );
    },

    closeShareWechartTips() {
      this.setYZData(
        {
          showShareWxTips: false,
        },
        {
          immediate: true,
        }
      );
    },

    publishSuccess(e) {
      const status = getTipsData(e.detail);
      this.share();
      this.setYZData({
        showSaveTip: true,
        status,
      });
    },
    tryOpenAssistantPop() {
      return request({
        path: '/wscsalesman/operation/salesman-aide/isNeedGuide.json',
        data: {
          isForCenter: false,
        },
        method: 'POST',
      }).then((data) => {
        const { needGuide } = data;
        if (needGuide) {
          this.setYZData({
            showAssistant: true,
          });
        }
      });
    },
    closeAssistantPop() {
      this.setYZData({
        showAssistant: false,
      });
    },

    changeTab(tabInfo) {
      const { detail = {} } = tabInfo;
      if (detail.index === 1 && detail.title === '生成海报') {
        guideTimeLogger.startRecord(TIME_LOG_KEY_MAP.DRAW_POSTER);
      }
    },

    /** 获取商详海报信息 */
    getGoodsPosterInfo() {
      getGoodsPosterInfoJson
        .bind(this)()
        .then((res) => {
          this.setYZData({
            goodsPosterInfo: res,
          });
        });
    },
  },
});
