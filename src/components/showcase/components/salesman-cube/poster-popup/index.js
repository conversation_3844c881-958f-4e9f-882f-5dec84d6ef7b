import WscComponent from 'pages/common/wsc-component';
import {
  authorize,
  loadImage,
  saveImage,
} from 'components/showcase/components/salesman-cube/helpers';

WscComponent({
  properties: {
    show: <PERSON><PERSON><PERSON>,
    posterImg: String,
    needDownload: {
      type: Boolean,
      value: false,
    }, // 是否需要先下载图片再保存图片
    csPrefix: {
      type: String,
      value: 'poster',
    },
  },
  methods: {
    close() {
      this.triggerEvent('close');
    },
    save() {
      wx.showLoading({ title: '保存中' });
      if (this.data.needDownload) {
        loadImage(this.data.posterImg)
          .then((src) => {
            if (!src) {
              return wx.showToast({
                title: '图片下载失败',
                icon: 'error',
              });
            }
            this.savePoster(src).then(this.close.bind(this));
          })
          .catch(() => {
            return wx.showToast({
              title: '图片下载失败',
              icon: 'error',
            });
          });
      } else {
        this.savePoster(this.data.posterImg).then(this.close.bind(this));
      }
    },
    savePoster(src) {
      return authorize('scope.writePhotosAlbum')
        .then(saveImage.bind(this, src))
        .then(() => {
          wx.hideLoading();
          wx.showToast({
            title: '保存成功',
            icon: 'success',
          });
        })
        .catch((err) => {
          wx.showToast({
            title: err || '保存失败，请检查保存到相册的权限',
            icon: 'error',
          });
        });
    },
  },
});
