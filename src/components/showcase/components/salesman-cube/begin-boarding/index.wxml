<view class="guide-container" catchtap="handleClick" wx:if="{{guideConfig[step].component === 'downward-guide'}}">
  <view wx:if="{{guideConfig[step]}}" style="display: flex;justify-content: center">
    <view
      class="content-wrapper"
      style="bottom: {{guideConfig[step].bottom}};">
        <view class="content">{{guideConfig[step].content}}</view>
        <view class="guide-button">{{step === guideConfig.length - 1 ? '知道了' : '下一步'}}</view>
        <image class="guide-arrow" src="{{ assertMap[guideConfig[step].arrowType] }}" />
    </view>
  </view>
  <image
    class="guide-img"
    src="{{guideConfig[step].imgUrl}}"
    style=" width: {{guideConfig[step].imgStyle.width}}; height: {{guideConfig[step].imgStyle.height}}; left: {{guideConfig[step].imgStyle.left}}; bottom: {{guideConfig[step].imgStyle.bottom}}; " />
</view>

<view class="guide-container" catchtap="handleClick" wx:if="{{guideConfig[step].component === 'upward-guide'}}">
  <image
    class="guide-img"
    src="{{guideConfig[step].imgUrl}}"
    style=" width: {{guideConfig[step].imgStyle.width}}; height: {{guideConfig[step].imgStyle.height}}; left: {{guideConfig[step].imgStyle.left}}; bottom: {{guideConfig[step].imgStyle.bottom}}; " />
  <view wx:if="{{guideConfig[step]}}" style="display: flex;justify-content: center">
    <view
      class="content-wrapper"
      style="bottom: {{guideConfig[step].bottom}};">
      <image class="guide-arrow" src="{{ assertMap[guideConfig[step].arrowType] }}" />
      <view class="content">{{guideConfig[step].content}}</view>
      <view class="guide-button">{{step === guideConfig.length - 1 ? '知道了' : '下一步'}}</view>
    </view>
  </view>
</view>
