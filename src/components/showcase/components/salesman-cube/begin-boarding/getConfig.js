import { isNewIphone } from '../../../../../../shared/utils/browser/device-type';

const isIphoneX = isNewIphone();

const getIconPlacement = function(list, name) {
  // 获取对应icon的位置索引
  let outerCursor = 0;
  while (outerCursor < list.length) {
    const innerList = list[outerCursor];
    let innerIndex = 0;
    while (innerIndex < innerList.length) {
      if (innerList[innerIndex].alias === name) {
        // 外层是倒着数的，方便下面好计算
        return [list.length - 1 - outerCursor, innerIndex];
      }
      innerIndex++;
    }
    outerCursor++;
  }
  return [-1, -1];
};

const hasTabbar = function() {
  const page = getCurrentPages()[0];
  return page && (page.route === 'pages/home/<USER>/index' || page.route === 'packages/home/<USER>/index');
};

export const getGuideConfig = (options) => {
  const { iconList, special } = options;
  const shareIndexes = getIconPlacement(iconList, 'share');
  const salesmanIndexes = getIconPlacement(iconList, 'salesman');
  const promoterIndexes = getIconPlacement(iconList, 'promote');
  const TabbarHeight = 49; // tabbar的高度
  const iphoneXHeight = 34; // iphonex的兼容高度
  const optionIconHeight = 100; // 赚字面板底部option的高度
  const optionIconWidth = 80; // 赚字面板底部option的宽度
  const arrowMarginToIcon = 104; // 箭头到option的距离
  let extraBottom = hasTabbar() ? TabbarHeight : 0; // 首页需要额外增加底部tab高度
  extraBottom = isIphoneX ? extraBottom + iphoneXHeight : extraBottom;
  const shortGuideConfig = [
    {
      content: '恭喜获得分享赚佣金权益，点击这里可以查看所有可推广商品和佣金',
      component: 'downward-guide',
      imgUrl:
        'https://img01.yzcdn.cn/upload_files/2020/11/24/Fs70rEBwimWwru83H1y-iQfZbOg1.png',
      arrowType:
        salesmanIndexes[1] > 2 ? 'arrowViolentDown' : 'arrowViolentLeftDown',
      bottom:
        salesmanIndexes[1] * optionIconHeight +
        arrowMarginToIcon +
        extraBottom +
        'px',
      imgStyle: {
        left: salesmanIndexes[0] * optionIconWidth + 2 + 'px',
        bottom: salesmanIndexes[1] * optionIconHeight + extraBottom + 6 + 'px',
        height: '90px',
        width: '90px',
      },
    },
  ];
  const longGuideConfig = [
    {
      content: '分享给好友，可赚取佣金',
      component: 'downward-guide',
      imgUrl:
        'https://img01.yzcdn.cn/upload_files/2021/05/26/FpAMSN5m4y26ORqq-JR7MiCg9zN-.png',
      arrowType:
        shareIndexes[1] > 2 ? 'arrowViolentDown' : 'arrowViolentLeftDown',
      bottom:
        shareIndexes[0] * optionIconHeight +
        arrowMarginToIcon +
        extraBottom +
        'px',
      imgStyle: {
        left: shareIndexes[1] * optionIconWidth + 2 + 'px',
        bottom: shareIndexes[0] * optionIconHeight + extraBottom + 6 + 'px',
        height: '90px',
        width: '90px',
      },
    },
    {
      content: '点击这里可以查看所有可推广商品和可提现金额',
      component: 'downward-guide',
      imgUrl:
        'https://img01.yzcdn.cn/upload_files/2020/11/24/Fs70rEBwimWwru83H1y-iQfZbOg1.png',
      arrowType:
        salesmanIndexes[1] > 2 ? 'arrowViolentDown' : 'arrowViolentLeftDown',
      bottom:
        salesmanIndexes[0] * optionIconHeight +
        arrowMarginToIcon +
        extraBottom +
        'px',
      imgStyle: {
        left: salesmanIndexes[1] * optionIconWidth + 2 + 'px',
        bottom: salesmanIndexes[0] * optionIconHeight + extraBottom + 6 + 'px',
        height: '90px',
        width: '90px',
      },
    },
  ];
  if (promoterIndexes[0] >= 0) {
    longGuideConfig.push({
      content: '点击这里可进入销售员专属推广页',
      component: 'downward-guide',
      imgUrl:
        'https://img01.yzcdn.cn/upload_files/2020/11/24/FvwfYwf8CaldPoZNfAOWBvA0yGXj.png',
      arrowType:
        promoterIndexes[1] > 2 ? 'arrowViolentDown' : 'arrowViolentLeftDown',
      bottom:
        promoterIndexes[0] * optionIconHeight +
        arrowMarginToIcon +
        extraBottom +
        'px',
      imgStyle: {
        left: promoterIndexes[1] * optionIconWidth + 2 + 'px',
        bottom: promoterIndexes[0] * optionIconHeight + extraBottom + 6 + 'px',
        height: '90px',
        width: '90px',
      },
    });
  }
  return special ? longGuideConfig : shortGuideConfig;
};
