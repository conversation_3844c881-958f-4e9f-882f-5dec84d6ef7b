import WscComponent from 'pages/common/wsc-component';

WscComponent({
  properties: {
    guideConfig: {
      type: Array,
      value: () => {
        return [];
      },
    },
  },

  data: {
    step: 0,
    assertMap: {
      button:
        'https://img01.yzcdn.cn/upload_files/2020/12/17/FizKPDD0KVrK9CBzRofDO1vBQURh.png',
      arrowUp:
        'https://img01.yzcdn.cn/upload_files/2020/11/24/FvpawPUa7evlstuYiwsFxDWrPrba.png',
      arrowDown:
        'https://img01.yzcdn.cn/images/salesman/common/guide-arrow-down.png',
      arrowViolentUp:
        'https://img01.yzcdn.cn/upload_files/2020/11/24/FvpawPUa7evlstuYiwsFxDWrPrba.png',
      arrowViolentDown:
        'https://img01.yzcdn.cn/upload_files/2020/11/24/FhWjW9qXlbE0w4HDQ6aHlYdX8nRk.png',
      arrowViolentLeftDown:
        'https://img01.yzcdn.cn/upload_files/2020/11/24/Ft-emTUjyW1Zm_f6tEdxnhSiaC36.png',
    },
  },

  methods: {
    handleClick() {
      const { step, guideConfig } = this.data;
      if (step < guideConfig.length - 1) {
        this.setYZData({
          step: this.data.step + 1,
        });
      } else {
        this.triggerEvent('complete');
      }
    },
  },
});
