import { node as request } from 'shared/utils/request';

function getSubMsgId() {
  return request({
    path: '/wscump/salesman/im/getSubMsg.json',
  });
}

function requestSubscribeMessage(tmplIds) {
  return new Promise((resolve) => {
    wx.requestSubscribeMessage({
      tmplIds,
      success(res) {
        if (res.errMsg === 'requestSubscribeMessage:ok') {
          resolve(true);
        } else {
          resolve();
        }
      },
      fail() {
        resolve();
      },
    });
  });
}

function subscribeCallback(tmplIds) {
  request({
    path: '/wscump/salesman/im/subscriptionCallback.json',
    method: 'POST',
    data: {
      scene: 'salesmanOrder',
      templateIdList: tmplIds,
    },
  });
}

// 订阅消息服务，赚字不关心成功与否
function subscribeMessage(next) {
  if (wx.requestSubscribeMessage) {
    // 获取模版id
    getSubMsgId()
      .then((tmplIds) => {
        let showGif = true;
        // 有设置模版id就去请求微信授权
        if (Array.isArray(tmplIds) && tmplIds.length) {
          const that = this;
          wx.getSetting({
            withSubscriptions: true,
            success({ subscriptionsSetting = {} }) {
              const { mainSwitch, itemSettings = {} } = subscriptionsSetting;
              // 订阅消息开关开了 && 订阅消息没做操作 && 没有永久订阅展示引导
              if (
                mainSwitch &&
                showGif &&
                tmplIds.every((id) => !itemSettings[id])
              ) {
                that.setYZData({
                  isShowGif: true,
                });
              }
            },
          });
          requestSubscribeMessage(tmplIds).then((isSuccess) => {
            showGif = false;
            this.setYZData({
              isShowGif: false,
            });
            // 授权成功回调有赞接口
            if (isSuccess) {
              subscribeCallback(tmplIds);
            }
            next();
          });
          return;
        }
        next();
      })
      .catch(next);
  } else {
    next();
  }
}

export default subscribeMessage;
