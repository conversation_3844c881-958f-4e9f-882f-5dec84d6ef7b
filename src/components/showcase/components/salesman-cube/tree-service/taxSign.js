import { node as request } from 'shared/utils/request';

function getTaxSignInfo() {
  return request({
    path: '/wscsalesman/common-api/assets/new-tax/getShopAndUserTaxInfo.json',
  });
}

function checkTaxSign(next) {
  getTaxSignInfo()
    .then((data) => {
      wx.hideLoading();
      const {
        taxServiceState,
        userSigned,
        authDeadline,
        authDeadlineOver,
        shareAuth,
        mode,
      } = data;
      const isFiredOnShare = shareAuth === 1;
      if (taxServiceState && !userSigned && isFiredOnShare) {
        let signText;
        if (mode === 1) {
          signText =
            '根据《电商法》规定，个人收入需要缴纳个税，请先完成个人信息认证，否则获得的佣金或奖励不结算。';
        } else if (authDeadlineOver) {
          signText =
            '由于商家开启了个税代缴服务，需要认证升级后才可使用，你还未完成信息认证，商家无法给你发放佣金或奖励。';
        } else {
          signText = `由于商家开启了个税代缴服务，需要认证升级后才可使用，请在 ${authDeadline} 前完成认证升级，否则商家无法给你发放佣金或奖励。`;
        }

        this.setYZData({
          showTaxSign: true,
          signText,
        });
      } else {
        next();
      }
    })
    .catch(() => {
      wx.hideLoading();
      next();
    });
}

export default checkTaxSign;
