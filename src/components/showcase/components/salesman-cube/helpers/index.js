import navigate from '@/helpers/navigate';
import { fetchUltraCodePost } from '@/helpers/fetch';
import { node as request } from 'shared/utils/request';
import authorize from '@/helpers/authorize';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import { guideDisableList, guideReplaceMap } from '../config';
import { monitor } from 'components/showcase/components/salesman-cube/helpers/log';
import { WX_SHARE_CANCEL } from 'components/showcase/components/salesman-cube/constant';

const app = getApp();

/**
 * 导购绑定关系来源值枚举
 * http://zanapi.qima-inc.com/site/service/view/493343
 */
export const EShoppingGuideBindSourceType = {
  // 暂时前端只使用到这几个值
  /** 浏览微页面 */
  TINY_PAGE: 6,
  /** 领取优惠券 */
  COUPONS: 13,
  /** 浏览商品详情页 */
  BROWSE_GOODS: 21,
  /** 其他 */
  OTHER: 14,
};

/**
 * 场景值与guideSourceType的映射
 */
const scenesGuideSourceMap = {
  item_goods: EShoppingGuideBindSourceType.BROWSE_GOODS,
  feature_page: EShoppingGuideBindSourceType.TINY_PAGE,
  // 店铺主页按微页面算
  home_page: EShoppingGuideBindSourceType.TINY_PAGE,
  // 幸运大转盘按微页面算
  lottery: EShoppingGuideBindSourceType.TINY_PAGE,
  // 权益卡算作商详页，与h5配置相同
  benefit_card: EShoppingGuideBindSourceType.BROWSE_GOODS,
};

/**
 * 获取导购绑定关系来源值
 */
function getGuideSourceType(scene) {
  return scenesGuideSourceMap[scene] || EShoppingGuideBindSourceType.OTHER;
}

/**
 * 过滤不支持的cube
 */
export function filterDisabledArray(array = [], disabledArray = []) {
  const result = [];
  array.forEach((item) => {
    if (Array.isArray(item)) {
      result.push(filterDisabledArray(item, disabledArray));
    } else if (disabledArray.indexOf(item) === -1) {
      result.push(item);
    }
  });
  return result;
}

/**
 * 将传入的 cube 替换为 导购支持的 cube
 */
function guideReplaceCube(cubes) {
  let ret = [];
  cubes.forEach((cube) => {
    if (Array.isArray(cube)) {
      ret.push(guideReplaceCube(cube));
      return;
    }
    if (cube in guideReplaceMap) {
      ret.push(guideReplaceMap[cube]);
      return;
    }
    ret.push(cube);
  });
  ret = filterDisabledArray(ret, guideDisableList);
  return ret;
}

function createFetchPosterFactory() {
  let retry = 0;
  return function fetchPoster(url, data) {
    if (!data) {
      data = url;
      url = '/wscsalesman/poster-service/create-poster.json';
    }
    return request({
      path: url,
      query: data,
    }).catch((err) => {
      if (retry === 0) {
        retry = 1;
        return fetchPoster(url, {
          ...data,
          retry,
        });
      }
      return Promise.reject(err);
    });
  };
}

export const fetchPoster = createFetchPosterFactory();

function loadImage(src) {
  return new Promise((resolve, reject) => {
    wx.downloadFile({
      url: src,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (e) => {
        reject(e);
      },
    });
  });
}

function saveImage(tempFilePath) {
  return new Promise((resolve, reject) => {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: resolve,
      fail: reject,
    });
  });
}

function getCurrentPage() {
  const pages = getCurrentPages() || [];
  const currentPage = pages[pages.length - 1];
  return currentPage || {};
}

function formateParams(query = {}, goodsInfo = {}) {
  const alias = goodsInfo.goodsAlias || goodsInfo.alias;

  // 非商品赚字下处理
  if (alias) {
    query.alias = alias;
  }

  const { goodsActivityInfo } = goodsInfo;
  if (!goodsActivityInfo) return query;
  const activityId = query.activityId || goodsActivityInfo.activityId;
  const activityType = query.type || goodsActivityInfo.activityType;
  const umpAlias = goodsActivityInfo.activityAlias || goodsInfo.activityAlias;
  if (activityId || activityType) {
    // 砍价和秒杀逻辑需要特殊处理
    const type = activityType;
    if (type === 'helpCut' || type === 21) {
      // 砍价活动
      query.activityType = 'helpCut';
      query.umpType = 'helpCut';
      query.activityId = activityId;
    } else if (type === 'seckill' || type === 6) {
      // 秒杀活动 秒杀活动的商品alias还是活动alias 商品海报会生成错误 这次让后端加了真正商品alias字段goodsAlias
      query.umpType = 'seckill';
      query.activityType = 'seckill';
      query.umpAlias = umpAlias;
      // 秒杀活动海报只能使用umpAlias，所以要去除query上可能存在的activityId
      query.activityId = '';
    } else {
      // 其他活动
      query.activityId = activityId;
      query.activityType = activityType;
    }
  }
  return query;
}

function genCode(path, query = {}) {
  const params = formateParams(query, this.data.goodsInfo);
  query.refUrl && delete query.refUrl;
  const newQuery = {
    ...query,
    guestKdtId: app.getKdtId(),
    ...params,
    ...getSalesmanParamsObject({ sl: this.data.shareData.seller }),
  };

  return fetchUltraCodePost(path, newQuery).catch((err) => {
    wx.showToast({
      title: err.message || err.msg || '获取二维码失败',
      icon: 'none',
    });
  });
}

function getImageInfo(src) {
  return new Promise((resolve) => {
    wx.getImageInfo({
      src,
      success: (bgData) => {
        resolve(bgData);
      },
    });
  });
}

function configAdapter(hideItems, iconList) {
  const list = JSON.parse(JSON.stringify(iconList));
  hideItems.forEach((hideItem) => {
    list.forEach((icon) => {
      if (icon.alias === hideItem) {
        icon.hidden = true;
      }
    });
  });
  return list;
}

function compose(handlers) {
  if (!Array.isArray(handlers)) {
    throw new TypeError('Handlers stack must be an array!');
  }
  // eslint-disable-next-line no-unused-vars
  for (const fn of handlers) {
    if (typeof fn !== 'function') {
      throw new TypeError('Handler must be composed of functions!');
    }
  }
  return function (next) {
    let index = -1;
    function dispatch(i) {
      if (i <= index) {
        return Promise.reject(new Error('next() called multiple times'));
      }
      index = i;
      let fn = handlers[i];
      if (i === handlers.length) fn = next;
      if (!fn) return Promise.resolve();
      try {
        return Promise.resolve(
          fn(() => {
            return dispatch(i + 1);
          })
        );
      } catch (err) {
        return Promise.reject(err);
      }
    }
    return dispatch(0);
  };
}

function getTipsData(data = {}) {
  const result = [];
  const textMap = {
    copy_0: '文案/链接复制失败',
    copy_1: '文案/链接已复制',
    material_0: '素材分享到动态失败',
    material_1: '素材已分享到动态',
    image_0: '图片保存到相册失败',
    image_1: '图片已保存到相册',
  };
  Object.keys(data).forEach((key) => {
    console.log(key, data[key]);
    result.push({
      type: key,
      state: data[key],
      text: textMap[`${key}_${data[key]}`],
    });
  });
  return result;
}

function filterZoneCube(cubes) {
  const ret = [];
  cubes.forEach((cube) => {
    if (Array.isArray(cube)) {
      ret.push(filterZoneCube(cube));
    } else if (cube !== 'zoom') {
      ret.push(cube);
    }
  });
  return ret;
}

/** 调用微信图片分享能力 */
const sharePosterByWechatImgShare = (poster, entrancePath, fallback) => {
  app.downloadFile({
    url: poster,
    success: (res) => {
      wx.showShareImageMenu({
        path: res.tempFilePath,
        needShowEntrance: true,
        entrancePath,
        success: () => {
          // 成功，暂不做额外处理
        },
        fail: (err) => {
          monitor({
            extra: {
              msg: '赚字微信图片分享-调用SDK方法失败',
              err,
            },
          });
          if (err && err.errMsg === WX_SHARE_CANCEL) {
            return;
          }
          // 天网日志上报 + 走兜底，旧展示逻辑
          fallback();
        },
      });
    },
    fail: (err) => {
      // 天网日志上报 + 走兜底，旧展示逻辑
      monitor({
        extra: {
          msg: '赚字微信图片分享-图片下载失败',
          err,
        },
      });
      fallback();
    },
  });
};

export {
  getGuideSourceType,
  guideReplaceCube,
  navigate,
  authorize,
  loadImage,
  saveImage,
  getCurrentPage,
  genCode,
  getImageInfo,
  configAdapter,
  compose,
  formateParams,
  getTipsData,
  filterZoneCube,
  sharePosterByWechatImgShare,
};
