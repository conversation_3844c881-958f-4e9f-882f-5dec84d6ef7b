import Zan<PERSON>eappLogger from '@/bootstrap/skynet-report';
import WeappLogSdk from '@youzan/weapp-log-sdk';
import { SALESMAN_TYPE } from '../constant';

const app = getApp();

const defaultExtra = {
  kdtId: app.getKdtId(),
  type: 'weapp',
  buyerId: app.getBuyerId(),
};

// 导购埋点上报对象
const guideLog = new WeappLogSdk({
  plat: {
    yai: 'shop_guide_workbench_b',
    st: 'weapp',
  },
});

const userInfo = app.logger.getGlobal()?.user;
guideLog.setUser(userInfo);
guideLog.setEvent({
  ...app.logger.getGlobal()?.event,
  si: app.getKdtId(),
});

const monitor = (config) => {
  try {
    ZanWeappLogger.monitor({
      appName: 'wsc-h5-salesman',
      logIndex: 'wsc_wap_salesman_share_track',
      topic: 'front',
      extra: {
        ...defaultExtra,
        ...(config.extra || {}),
      },
    });
  } catch (e) {
    console.log(e);
  }
};

const setLogger = (logData, params = {}) => {
  const kdtId = app.getKdtId();
  const { sl, salesman_type } = params;
  const { params: logParams = {} } = logData;
  const SALESMAN_LOGGER_INDEX = `${kdtId}-${sl}`;
  logData.params = { ...logParams, ...params, mark: SALESMAN_LOGGER_INDEX };

  if (salesman_type === SALESMAN_TYPE.GUIDE) {
    if (!guideLog.getLogParams()?.user.buyer_id) {
      guideLog.setUser(app.logger.getGlobal()?.user);
    }
    guideLog.log({
      ...logData,
      durl: app.logger.durl,
    });
  }

  app.logger.log(logData);
};

export { monitor, setLogger };
