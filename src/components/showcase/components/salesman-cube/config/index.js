// cube config
export const cubeConfig = {
  default: {
    weapp: [
      ['share', 'tuwenPoster', 'code'],
      ['salesman', 'material', 'zoom'],
    ],
  },
  // goods-list/goods-group
  feature: {
    weapp: [
      ['share', 'featurePoster', 'code'],
      ['salesman', 'material', 'zoom'],
    ],
  },
  // 微页面
  feature_page: {
    weapp: [
      ['share', 'featurePoster', 'code', 'weappFeatureLink'],
      ['salesman', 'material', 'zoom'],
    ],
  },
  // 首页
  home_page: {
    weapp: [
      ['share', 'featurePoster', 'code', 'weappHomeLink'],
      ['salesman', 'material', 'zoom'],
    ],
  },
  shop_note: {
    weapp: [
      ['share', 'tuwenPoster', 'code'],
      ['salesman', 'material', 'zoom'],
    ],
  },
  benefit_card: {
    weapp: [
      ['share', 'tuwenPoster', 'code'],
      ['salesman', 'material', 'zoom'],
    ],
  },
  salesman_coupon: {
    weapp: [
      ['share', 'code'],
      ['salesman', 'material'],
    ],
  },
  zone: {
    weapp: [
      ['share', 'code', 'zonePoster'],
      ['salesman', 'material'],
    ],
  },
  salesman_goods: {
    weapp: ['code', 'salesman', 'material', 'zoom'],
  },
  lottery: {
    weapp: [['share', 'code']],
  },
};

/** 导购工作台 - 推广商品 - 赚字 */
export const GUIDE_PROMOTE_GOODS_SCENES = 'guide_promote_goods';

/**
 * 导购cube配置
 */
export const guideCubeConfig = {
  default: {
    weapp: [
      ['share', 'tuwenPoster', 'code'],
      ['guide', 'material'],
    ],
    wecom: [
      ['wecomShare', 'share', 'tuwenPoster', 'code'],
      ['guide', 'material'],
    ],
  },
  feature: {
    weapp: [
      ['share', 'featurePoster', 'code'],
      ['guide', 'material'],
    ],
    wecom: [
      ['wecomShare', 'share', 'featurePoster', 'code'],
      ['guide', 'material'],
    ],
  },
  feature_page: {
    weapp: [
      ['share', 'featurePoster', 'weappFeatureLink', 'code'],
      ['guide', 'material'],
    ],
    wecom: [
      ['wecomShare', 'share', 'featurePoster', 'weappFeatureLink', 'code'],
      ['guide', 'material'],
    ],
  },
  home_page: {
    weapp: [
      ['share', 'featurePoster', 'weappHomeLink', 'code'],
      ['guide', 'material'],
    ],
    wecom: [
      ['wecomShare', 'share', 'featurePoster', 'weappHomeLink', 'code'],
      ['guide', 'material'],
    ],
  },
  shop_note: {
    weapp: [
      ['share', 'tuwenPoster', 'code'],
      ['guide', 'material'],
    ],
    wecom: [
      ['wecomShare', 'share', 'tuwenPoster', 'code'],
      ['guide', 'material'],
    ],
  },
  benefit_card: {
    weapp: [
      ['share', 'tuwenPoster', 'code'],
      ['guide', 'material'],
    ],
    wecom: [
      ['wecomShare', 'share', 'tuwenPoster', 'code'],
      ['guide', 'material'],
    ],
  },
  salesman_coupon: {
    weapp: [
      ['share', 'code'],
      ['guide', 'material'],
    ],
    wecom: [
      ['wecomShare', 'share', 'code'],
      ['guide', 'material'],
    ],
  },
  zone: {
    weapp: [
      ['share', 'code', 'zonePoster'],
      ['guide', 'material'],
    ],
    wecom: [
      ['wecomShare', 'share', 'code', 'zonePoster'],
      ['guide', 'material'],
    ],
  },
  salesman_goods: {
    weapp: ['code', 'guide', 'material'],
  },
  [GUIDE_PROMOTE_GOODS_SCENES]: {
    weapp: [['share', 'weappLink', 'code', 'guide', 'guideMaterial']],
    wecom: [
      ['wecomShare', 'share', 'weappLink', 'code', 'guide', 'guideMaterial'],
    ],
  },
  lottery: {
    weapp: [['share', 'code', 'guide', 'material']],
    wecom: [['wecomShare', 'share', 'code', 'guide', 'material']],
  },
};

export const getIconMap = ({ shareData = {}, kdtId, isWecomMPEnv = false }) => {
  const { salesmanName = '销售员', isShoppingGuide } = shareData;
  return {
    wecomShare: {
      alias: 'share',
      title: '企业微信好友',
      openType: 'share',
      hidden: false,
      type: 'wecomShare',
      iconUrl:
        'https://b.yzcdn.cn/public_files/428cedca609cb735488744ea9d57bb4d.png',
    },
    share: {
      // 企微小程序环境下，微信好友分享，需要先给提示弹窗；微信小程序环境下，直接进行分享分享
      alias: isWecomMPEnv ? 'shareInWecom' : 'share',
      title: '微信好友',
      // 企微小程序环境下，微信好友分享，需要先给提示弹窗，需要值为空；微信小程序环境下，直接进行分享分享
      openType: isWecomMPEnv ? '' : 'share',
      hidden: false,
      type: 'share',
      iconUrl: '//b.yzcdn.cn/salesman/cube/share.png',
    },
    code: {
      alias: 'code',
      title: '小程序码',
      hidden: false,
      type: 'poster',
      iconUrl: '//b.yzcdn.cn/salesman/cube/wecode.png',
    },
    tuwenPoster: {
      alias: 'tuwenPoster',
      title: '分享海报',
      hidden: false,
      iconUrl: 'https://img01.yzcdn.cn/weapp/wsc/NdzsNJ.png',
    },
    featurePoster: {
      alias: 'featurePoster',
      title: '分享海报',
      hidden: false,
      iconUrl: 'https://img01.yzcdn.cn/weapp/wsc/NdzsNJ.png',
    },
    goodsPoster: {
      alias: 'goodsPoster',
      title: '分享海报',
      hidden: false,
      type: 'poster',
      iconUrl: '//b.yzcdn.cn/salesman/cube/poster.png',
    },
    zonePoster: {
      alias: 'zonePoster',
      title: '空间海报',
      hidden: false,
      type: 'poster',
      iconUrl: '//b.yzcdn.cn/salesman/cube/poster.png',
    },
    promote: {
      alias: 'promote',
      iconUrl: '//b.yzcdn.cn/salesman/cube/promoteicon.png',
      title: '专属推广页',
      type: 'link',
    },
    salesman: {
      alias: 'salesman',
      iconUrl: '//b.yzcdn.cn/salesman/cube/salesman.png',
      title: `${salesmanName}中心`,
      type: 'link',
      weappUrl: '/packages/salesman/salesman-center/index',
    },
    guide: {
      alias: 'guide',
      iconUrl: '//b.yzcdn.cn/salesman/cube/salesman.png',
      title: '导购工作台',
      type: 'link',
      weappUrl: `/pages/common/webview-page/index?src=${encodeURIComponent(
        `https://h5.youzan.com/guide/center/home?kdt_id=${kdtId}`
      )}`,
    },
    guideMaterial: {
      alias: 'guideMaterial',
      iconUrl: '//b.yzcdn.cn/salesman/cube/meterial.png',
      title: '素材中心',
      type: 'link',
      weappUrl: '/packages/guide/zone/material/index',
    },
    material: {
      alias: 'material',
      iconUrl: '//b.yzcdn.cn/salesman/cube/meterial.png',
      title: '素材中心',
      type: 'link',
      weappUrl: '/packages/salesman/zone/material/index',
    },
    zoom: {
      alias: 'zoom',
      iconUrl: '//b.yzcdn.cn/salesman/cube/zoom.png',
      title: '个人空间',
      type: 'link',
      weappUrl: '/packages/salesman/zone/home/<USER>',
    },
    center: {
      alias: 'center',
      iconUrl: '//b.yzcdn.cn/salesman/cube/zoom.png',
      title: '个人中心',
      type: 'link',
      weappUrl: '/packages/usercenter/dashboard/index',
    },
    weappLink: {
      alias: 'weappLink',
      iconUrl:
        '//img01.yzcdn.cn/upload_files/2022/01/06/FtNIZW_b89uIYk0mwVXzy700WjDO.png',
      title: '小程序链接',
      type: 'weappLink',
    },
    weappHomeLink: {
      alias: 'weappHomeLink',
      iconUrl: '//b.yzcdn.cn/salesman/cube/weappHomeLink.png',
      title: isShoppingGuide ? '小程序链接' : '复制链接',
      type: 'weappHomeLink',
    },
    weappFeatureLink: {
      alias: 'weappFeatureLink',
      iconUrl: '//b.yzcdn.cn/salesman/cube/weappHomeLink.png',
      title: '小程序链接',
      type: 'weappFeatureLink',
    },
  };
};

// 免费版不支持
export const freeDisableList = ['zoom', 'zoomPoster', 'material', 'promote'];

// 推广页不支持
export const promoteDisableList = ['promote'];

/**
 * 导购不支持
 */
export const guideDisableList = ['salesman', 'promote', 'zoom'];

/**
 * 导购需要替换的cube
 */
export const guideReplaceMap = {
  /** 传入的销售员中心替换为导购工作台 */
  salesman: 'guide',
  /** 销售员素材中心替换为导购素材中心 */
  material: 'guideMaterial',
};

const getShareClickLogConfig = (shareCmpt) => {
  return {
    et: 'click', // 事件类型
    ei: 'share', // 事件标识
    en: '分享', // 事件名称
    params: {
      share_type: 'salesman',
      share_cmpt: shareCmpt,
    },
  };
};

// 赚字埋点项目：https://track.qima-inc.com/projects/299
export const SHARE_LOGGER_CONFIG = {
  // 自定义按钮唤起原生分享
  nativeCustom: getShareClickLogConfig('native_custom'),
  // 海报
  poster: getShareClickLogConfig('poster'),
  // 复制
  copylink: getShareClickLogConfig('copylink'),
  // 小程序链接复制
  copyWeappLink: getShareClickLogConfig('copyWeappLink'),
  // 二维码
  qrCode: getShareClickLogConfig('qr_code'),
  // 小程序码
  miniprogram: getShareClickLogConfig('miniprogram'),
  // 保存图文
  savepictures: getShareClickLogConfig('savepictures'),
  // 点击赚字，显示浮层
  showPanel: {
    et: 'view',
    ei: 'show_zhuanzi_panel',
    en: '点赚字，出现浮层',
    params: {
      version: 'v2',
    },
  },
  drawPoster: {
    et: 'view',
    ei: 'draw_poster',
    en: '赚字海报绘制成功曝光',
    params: {
      version: 'v2',
    },
  },
  clickShare: {
    et: 'click', // 事件类型
    ei: 'sales_money', // 事件标识
    en: '点赚字', // 事件名称
    params: {
      version: 'v2',
    }, // 事件参数
  },
  salesMoneyView: {
    et: 'view', // 事件类型
    ei: 'salesmoney_view', // 事件标识
    en: '赚字曝光', // 事件名称
    params: {
      // scenes: 'str',
      // goods_id: 'str',
      version: 'v2',
    }, // 事件参数
  },
  clickPosterTab: {
    et: 'click',
    ei: 'poster_click',
    en: '生成海报tab点击',
  },
  clickLinkIcon: {
    et: 'click',
    ei: 'link_icon_click',
    en: '链接类型icon点击',
  },
};

export const SPACE_ABILITY_NAME = 'salesman_advance_personal_space_ability';
