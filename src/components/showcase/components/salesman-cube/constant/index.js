const CANVAS_BG_IMG = 'https://img01.yzcdn.cn/weapp/wsc/4FKy95.png';
const CANVAS_ID = 'salesman-icon';
const ZONE_BG_IMG = 'https://img01.yzcdn.cn/weapp/wsc/xJSlFGL.png';
/** 微页面类型 */
const DecoratePage = {
  Home: 1,
  Other: 3,
};

// 分销员类型
const SALESMAN_TYPE = {
  // 1: 导购
  GUIDE: 1,
  // 2: 分销员
  SALESMAN: 2,
};

const TIME_LOG_KEY_MAP = {
  SHOW_PANEL: 'SHOW_PANEL', // 点击赚字，复现浮层
  DRAW_POSTER: 'DRAW_POSTER', // 绘制海报
};

/** 微页面匹配正则 */
const UrlPatternMap = {
  [DecoratePage.Home]: [
    /^(packages|pages)\/home\/dashboard/,
    /^packages\/tee-home|ext-home\/dashboard/,
  ],
  [DecoratePage.Other]: [
    /^(packages|pages)\/home\/feature/,
    /^packages\/tee-home|ext-home\/feature/,
    /^(packages|pages)\/home\/tab/,
    /^packages\/showcase-template\/index/,
    /^packages\/ext-home\/tab\/(one|two|three)/,
  ],
};

/** 微信图片分享 cancel 逻辑 */
const WX_SHARE_CANCEL = 'showShareImageMenu:fail cancel';

/** 微页面page-type枚举 */
const ShareCardTypeMap = {
  [DecoratePage.Home]: 'homepage',
  [DecoratePage.Other]: 'micropage',
};
export {
  CANVAS_BG_IMG,
  CANVAS_ID,
  ZONE_BG_IMG,
  SALESMAN_TYPE,
  TIME_LOG_KEY_MAP,
  DecoratePage,
  UrlPatternMap,
  ShareCardTypeMap,
  WX_SHARE_CANCEL,
};
