import { authorize, saveImage, loadImage } from '../helpers';

function save(imageList) {
  return loadImage(imageList.pop()).then((src) => {
    if (!src) throw new Error('图片下载失败');
    return authorize('scope.writePhotosAlbum')
      .then(saveImage.bind(this, src))
      .then(() => {
        if (imageList.length) {
          return save.call(this, imageList);
        }
        return { image: 1 };
      })
      .catch(() => {
        return { image: 0 };
      });
  });
}

export function saveImageList() {
  const { originImgList } = this.data;
  wx.showLoading({ title: '保存中' });
  return new Promise((resolve) => {
    save
      .call(this, originImgList)
      .then((res) => resolve(res))
      .catch((res) => resolve(res));
  });
}
