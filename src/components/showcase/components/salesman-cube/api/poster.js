import args from '@youzan/weapp-utils/lib/args';
import logv3 from 'utils/log/logv3';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import { authorize, saveImage, loadImage, formateParams } from '../helpers';
import guideTimeLogger from '../helpers/guide-time-logger';
import { TIME_LOG_KEY_MAP } from '../constant';
import { SHARE_LOGGER_CONFIG } from '../config';
import getApp from 'shared/utils/get-safe-app';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { guideLogger } from 'utils/guide';

const app = getApp();
const heightMap = {
  598: 465.64,
  557: 449.29,
  545: 424.37,
  505: 407.35,
};

function failedPoster(e) {
  const errMsg = e.msg || '';
  wx.showToast({
    title: errMsg || '生成图片路径失败',
    icon: 'none',
  });
}

export function getPosterByYunExtension(data) {
  const bridge = app?.getYouZanYunSdk?.();
  const beforeGenerateSalesmanGoodsPosterEvent =
    bridge?.page?.events?.beforeGenerateSalesmanGoodsPosterEvent;

  const loggerParams = {
    ecloudMode: ECLOUD_MODE,
    beforeGenerateSalesmanGoodsPosterEvent:
      !!beforeGenerateSalesmanGoodsPosterEvent,
  };

  try {
    guideLogger({
      message: '赚字-生成海报-getPosterByYunExtension调用',
      detail: {
        ...loggerParams,
      },
    });

    if (ECLOUD_MODE && beforeGenerateSalesmanGoodsPosterEvent) {
      // 有定制
      guideLogger({
        message:
          '赚字-生成海报-有beforeGenerateSalesmanGoodsPosterEvent定制：开始调用',
        detail: {
          ...loggerParams,
        },
      });
      return beforeGenerateSalesmanGoodsPosterEvent
        .trigger(data)
        .catch((error) => {
          // 有定制-catch
          guideLogger({
            message:
              '赚字-生成海报-有beforeGenerateSalesmanGoodsPosterEvent定制：catch',
            detail: {
              error,
              ...loggerParams,
            },
          });
          return Promise.resolve([]);
        });
    }
    // 无定制
    return Promise.resolve([]);
  } catch (error) {
    // 兜底-catch
    guideLogger({
      message: '赚字-生成海报-getPosterByYunExtension调用报错',
      detail: {
        error,
        ...loggerParams,
      },
    });
    return Promise.resolve([]);
  }
}

function getPoster(data, needRetry = false, retry = 0) {
  return app
    .request({
      path: '/wscgoods/weapp-poster/goods.json',
      data: {
        retry,
        ...data,
        noAuction: true,
      },
    })
    .catch((e) => {
      if (needRetry) {
        return getPoster(data, false, 1);
      }
      return Promise.reject(e);
    });
}

/** 生成小程序海报scene参数 */
export function generatePosterParams(goodsImgIndex = 0) {
  const dcPs = logv3.getDCPS();
  const offlineId = app.getOfflineId();
  const { chainStoreInfo = {} } = app.getShopInfoSync();
  const { isMultiOnlineShop } = chainStoreInfo;
  const kdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId(); // 连锁需要用总店的店铺ID，才能获取到小程序码

  const { goodsInfo = {} } = this.data;
  const { path } = goodsInfo;
  const purePath = path.split('?')[0];
  let query = args.getAll(path);
  query.refUrl && delete query.refUrl;
  if (this.data.shareData) {
    query = {
      ...query,
      ...getSalesmanParamsObject({ sl: this.data.shareData.seller }),
    };
  }
  query = formateParams(query, goodsInfo);
  // 参照生成QRcode的做法生成入参
  const scene = {
    share_from: 'poster',
    kdtId: String(kdtId),
    page: purePath,
    guestKdtId: String(app.getKdtId()),
    ...query,
    dcPs,
    offlineId,
  };

  const { poster } = this.data;

  // 合并自定义内容
  if (poster && poster.scene) {
    Object.assign(scene, poster.scene);

    // 删除scene
    delete poster.scene;
  }

  const posterParams = {
    ...query,
    kdtId,
    scene: encodeURIComponent(JSON.stringify(scene)),
    page: 'pages/common/blank-page/index',
    sid: app.getSessionId(),
    alias: this.data.goodsInfo.alias,
    // 支持隐藏分享人信息
    isSupportHideUserInfo: true,
    share_cmpt: 'poster',
    autoHeight: 1,
    goodsImgIndex,
  };
  if (offlineId) {
    posterParams.offlineId = offlineId;
  }

  // 合并自定义内容
  if (poster) {
    Object.assign(posterParams, poster);
  }

  return posterParams;
}

function makePoster(...args) {
  const posterParams = generatePosterParams.call(this, ...args);

  return getPosterByYunExtension(posterParams)
    .then((res) => {
      guideLogger({
        message: '赚字-生成海报-getPosterByYunExtension调用结果',
        detail: {
          res,
        },
      });
      if (!res.length) {
        return getPoster(posterParams, true);
      }
      return res[0];
    })
    .then((res) => {
      if (!res.imgUrl) {
        return Promise.reject('生成失败');
      }

      // 绘制海报性能打点上报
      const { shareData } = this.data;
      guideTimeLogger.endRecordAndReportLog(TIME_LOG_KEY_MAP.DRAW_POSTER, {
        logData: SHARE_LOGGER_CONFIG.drawPoster,
        params: {
          salesman_type: shareData.salesmanType,
        },
      });

      const { height } = res;
      this.setYZData({
        posterHeight: heightMap[height] || height,
      });

      return cdnImage(res.imgUrl);
    })
    .catch((e) => {
      failedPoster(e);
    });
}

export function createPoster(...args) {
  return makePoster.call(this, ...args);
}

function save(src) {
  return authorize('scope.writePhotosAlbum')
    .then(saveImage.bind(this, src))
    .then(() => {
      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success',
      });
    })
    .catch(() => {
      wx.hideLoading();
      wx.showModal({
        content: '需要同意将分享图片保存到相册，点击确定后跳转至设置页操作',
        success: (e) => {
          if (e.cancel) return;
          wx.openSetting({
            success: ({ authSetting }) => {
              if (authSetting['scope.writePhotosAlbum']) {
                save(src);
              }
            },
          });
        },
      });
    });
}

export function handleSavePoster(posterImg) {
  wx.showLoading({ title: '保存中' });
  return loadImage(posterImg)
    .then((src) => {
      if (!src) throw new Error('图片下载失败');
      return save(src);
    })
    .catch((err) => {
      wx.hideLoading();
      const { errMsg = '' } = err || {};
      wx.showToast({
        title: errMsg,
        icon: 'none',
      });
    });
}

/** 获取商详海报信息 */
export function getGoodsPosterInfoJson() {
  const { goodsInfo } = this.data;
  const { path } = goodsInfo;
  let query = args.getAll(path);
  query = formateParams(query, goodsInfo);

  return app
    .request({
      path: '/wscgoods/poster/goods-poster-info.json',
      data: query,
    })
    .then((res) => {
      return res;
    });
}
