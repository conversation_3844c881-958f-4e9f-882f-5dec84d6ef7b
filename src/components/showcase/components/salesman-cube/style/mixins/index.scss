@import '../var.scss';
@import './retina.scss';

@mixin border-retina($pos: surround, $border-retina-color: #e5e5e5) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid $border-retina-color;

  @if $pos == surround {
    border-width: 1px;
  }

  @if $pos == top-bottom {
    border-width: 1px 0;
  }

  @if $pos == left-right {
    border-width: 0 1px;
  }

  @if $pos == top {
    border-top-width: 1px;
  }

  @if $pos == right {
    border-right-width: 1px;
  }

  @if $pos == bottom {
    border-bottom-width: 1px;
  }

  @if $pos == left {
    border-left-width: 1px;
  }
}

@mixin hairline($border-retina-color: #e5e5e5) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid $border-retina-color;
}

@mixin multi-ellipsis($lines: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}

@mixin ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

@mixin abs-center {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}

@mixin p-safeArea($height: '0px') {
  padding-bottom: 0;
  padding-bottom: calc($height + constant(safe-area-inset-bottom));
  padding-bottom: calc($height + env(safe-area-inset-bottom));
}

@mixin m-safeArea($height: '0px') {
  margin-bottom: 0;
  margin-bottom: calc($height + constant(safe-area-inset-bottom));
  margin-bottom: calc($height + env(safe-area-inset-bottom));
}
