<view class="{{ posterClass }}">
  <van-loading wx:if="{{ loading }}" />
  <view wx:elif="{{ hasAuth }}" class="poster-box" style="height: {{ height }}px">
    <view class="poster-banner">
      <swiper
        wx:if="{{ posterImgArr.length }}"
        current="{{ current }}"
        class="poster-swiper"
        indicator-color="#ebedf0"
        indicator-active-color="#999999"
        previous-margin="55"
        next-margin="55"
        indicator-dots="{{ posterImgArr.length > 1 }}"
        bind:change="onSwiperChange"
      >
        <block wx:for="{{ posterImgArr }}" wx:for-index="index">
          <swiper-item>
            <view class="poster-banner__img-container {{ current === index ? 'active' : '' }}">
              <image
                src="{{ item.image }}"
                class="poster-banner__img"
                mode="{{ imgMode }}"
              />
              <van-loading
                custom-class="poster-loading"
                vertical
                wx:if="{{ item.loading }}"
              >
                海报生成中
              </van-loading>
              <view
                wx:elif="{{ item.error }}"
                data-index="{{ index }}"
                class="poster-banner__img--error"
                bindtap="reGenPoster"
              >
                海报生成失败，点击重试
              </view>
            </view>
          </swiper-item>
        </block>
      </swiper>
    </view>
    <view class="{{ isWxShareImg ? 'poster-banner__bottom' : 'poster-banner__bottom single-btn' }}">
      <view wx:if="{{ isWxShareImg }}" class="poster-banner__btn" bindtap="shareWechatFriends">
        <image src="//b.yzcdn.cn/salesman/cube/share.png" class="poster-banner__btn--icon" />
        <text class="poster-banner__btn--name">微信好友</text>
      </view>
      <view wx:if="{{ isWxShareImg }}" class="poster-banner__btn" bindtap="savePoster">
        <image src="//b.yzcdn.cn/salesman/cube/NdzsNJ.png" class="poster-banner__btn--icon" />
        <text class="poster-banner__btn--name">保存海报</text>
      </view>
      <view wx:else class="poster-banner__btn" style="background-color: {{ themeMainBgColor }}" bindtap="savePoster">
        保存海报
      </view>
    </view>
  </view>
  <user-authorize wx:else authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="getPoster">
    <view class="poster-authorize" style="height: {{ height - 20 }}px">
      <view class="poster-authorize__body">
        <view>生成海报需要使用微信头像昵称</view>
        <view>授权后可保存海报</view>
        <view class="poster-authorize__btn">点击授权</view>
      </view>
    </view>
  </user-authorize>
</view>
