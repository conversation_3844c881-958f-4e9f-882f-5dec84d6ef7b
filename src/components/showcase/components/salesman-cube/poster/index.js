import WscComponent from 'pages/common/wsc-component';
import Theme from 'shared/common/components/theme-view/theme';
import { monitor, setLogger } from '../helpers/log';
import { SHARE_LOGGER_CONFIG } from '../config';
import * as api from '../api';
import getAuthorizedState from 'shared/utils/get-authorized-state';
import { Hummer } from 'shared/utils/hummer';
import args from '@youzan/weapp-utils/lib/args';
import { generatePosterParams } from '../api';
import { sharePosterByWechatImgShare } from '../helpers';
import getSystemInfo from 'shared/utils/browser/system-info';

WscComponent({
  properties: {
    shareData: {
      type: Object,
      value: () => {
        return {};
      },
    },
    goodsInfo: {
      type: Object,
      value: () => {
        return {};
      },
    },
    goodsPosterInfo: {
      type: Object,
      value: () => {
        return {};
      },
    },
    height: {
      type: Number,
      value: 0,
    },
    isWxShareImg: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    imgMode: 'widthFix',
    imgList: [],
    // 多图下，轮播展示图片索引
    current: 0,
    // 多图轮播图列表
    posterImgArr: [],
    index: 0,
    flag: false,
    loading: true,
    imgWidth: 70,
    bannerStyle: '',
    posterClass: 'poster',
    themeMainBgColor: '',
    hasAuth: true,
  },

  attached() {
    // 埋点
    const { seller, salesmanType } = this.data.shareData;
    setLogger(SHARE_LOGGER_CONFIG.clickPosterTab, {
      sl: seller,
      salesman_type: salesmanType,
    });
    const { windowWidth, windowHeight } = getSystemInfo();
    const scale = windowHeight / windowWidth;
    this.setYZData({
      imgMode: scale > 2.16 || scale < 1.5 ? 'widthFix' : 'heightFix',
    });
  },

  ready() {
    Theme.getThemeColor('main-bg').then((color) => {
      this.setYZData({
        themeMainBgColor: color,
      });
    });
    getAuthorizedState()
      .then((res) => {
        if (res.nicknameAndAvatar) {
          this.getPoster();
          this.setYZData({
            loading: false,
          });
        } else {
          this.setYZData({
            hasAuth: false,
            loading: false,
          });
        }
      })
      .catch((error) => {
        wx.showToast({
          icon: 'none',
          title: '用户授权失败',
        });
        Hummer.capture('分销员：赚字用户授权失败' + error);
        this.setYZData({
          hasAuth: false,
          loading: false,
        });
      });
  },

  methods: {
    /** 轮播改变事件 */
    onSwiperChange({ detail }) {
      this.setYZData({
        current: detail.current,
      });
    },

    /** 重新生成图片 */
    reGenPoster(e) {
      const { index } = e.target.dataset;
      this.genPoster(index);
    },

    /** 更新指定海报图片 */
    updateTargetPoster(item, index) {
      this.setYZData({
        posterImgArr: this.data.posterImgArr.map((img, i) => {
          if (i === index) {
            return item;
          }
          return img;
        }),
      });
    },

    /** 海报绘制，有副作用 */
    genPoster(index) {
      const item = this.data.posterImgArr[index];
      this.updateTargetPoster(
        {
          ...item,
          loading: true,
        },
        index
      );
      api.createPoster
        .call(this, index)
        .then((res) => {
          if (res) {
            this.updateTargetPoster(
              {
                image: res,
                loading: false,
                error: undefined,
              },
              index
            );
          } else {
            this.updateTargetPoster(
              {
                ...item,
                loading: false,
                error: true,
              },
              index
            );
          }
        })
        .catch((e) => {
          this.updateTargetPoster(
            {
              ...item,
              loading: false,
              error: e,
            },
            index
          );
        });
    },

    getPoster(data) {
      const _this = this;
      if (data) {
        _this.setYZData({
          hasAuth: true,
          loading: true,
        });
      }

      if (this.data.posterImgArr.length) {
        this.data.posterImgArr.forEach((item, index) => {
          if (!item.loading && item.error) {
            this.genPoster(index);
          }
        });
      } else {
        const { posterItemModel = {} } = _this.properties.goodsPosterInfo;
        const { pictures = [{}] } = posterItemModel;
        const posterImgArr = pictures.map((item) => ({
          image: item.url,
          loading: true,
          error: undefined,
        }));
        this.setYZData(
          {
            posterImgArr,
          },
          {
            immediate: true,
          }
        );
        posterImgArr.forEach((_, index) => {
          this.genPoster(index);
        });
      }
    },
    savePoster() {
      const { seller, salesmanType } = this.data.shareData;
      setLogger(SHARE_LOGGER_CONFIG.poster, {
        sl: seller,
        posterType: 'savePoster',
        salesman_type: salesmanType,
      });
      const posterImg = this.data.posterImgArr[this.data.current]?.image;
      api.handleSavePoster.call(this, posterImg).then(() => {
        this.triggerEvent('complete');
      });
    },
    /** 调用微信SDK 分享图片 */
    shareWechatFriends() {
      const posterImg = this.data.posterImgArr[this.data.current]?.image;
      const fallback = () => {
        wx.previewImage({
          current: posterImg,
          urls: [posterImg],
        });
      };

      if (this.properties.isWxShareImg) {
        const { seller, salesmanType } = this.data.shareData;
        setLogger(SHARE_LOGGER_CONFIG.poster, {
          sl: seller,
          posterType: 'wechatShareImg',
          salesman_type: salesmanType,
        });
        const { scene: sceneStr } = generatePosterParams.bind(this)();
        try {
          const sceneObj = JSON.parse(decodeURIComponent(sceneStr));
          const { page, ...pageParams } = sceneObj;
          const entrancePath = args.add(page, pageParams);
          sharePosterByWechatImgShare(posterImg, entrancePath, fallback);
        } catch (e) {
          monitor({
            msg: '赚字微信图片分享-JSON.parse失败',
            err: e,
          });
          fallback();
        }
      }
    },
  },
});
