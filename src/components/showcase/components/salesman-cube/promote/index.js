import WscComponent from 'pages/common/wsc-component';
import { node as request } from 'shared/utils/request';
import * as api from '../api';
import fullfillImage from '@youzan/weapp-utils/lib/cdn-image';
import args from '@youzan/utils/url/args';
import buildUrl from '@youzan/utils/url/buildUrl';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import { setLogger } from '../helpers/log';
import { SHARE_LOGGER_CONFIG } from '../config';
import getPromoteText from '@youzan/salesman-cube-core-utils/lib/getPromoteText';
// 用于标识是否正在处理滚动
// const isScroll = false;
const app = getApp();

WscComponent({
  properties: {
    color: {
      type: String,
      value: '',
    },
    // 推广的shortLink链接（即小程序链接）
    shortLinkPromoteUrl: {
      type: String,
      value: '',
    },
    shareData: {
      type: Object,
      value: () => {
        return {};
      },
    },
    shareInfo: {
      type: Object,
      value: () => {
        return {};
      },
    },
    smartTextData: {
      type: Object,
      value: () => ({}),
    },
    goodsInfo: {
      type: Object,
      value: () => {
        return {};
      },
      observer(goodsInfo) {
        const { goodsPrice = {} } = goodsInfo;
        const { maxOriginPrice, minOriginPrice, maxPrice, minPrice } =
          goodsPrice;
        const activityMinPrice = this.getPriceStr(minPrice);
        const realPrice = this.getPriceStr(maxPrice, minPrice);
        const max = maxOriginPrice || maxPrice;
        const min = minOriginPrice || minPrice;
        const originPrice = this.getPriceStr(max, min);

        const promoteText = getPromoteText(goodsInfo, this.data.smartTextData);

        const imgList = goodsInfo.goodsMainPictures.map(
          (item) => item.url || item
        );

        /** 稳定性考虑，不对原来的图片数据进行处理，新增一个图片字段来显示裁剪的图片素材 */
        const showImgList = goodsInfo.goodsMainPictures.map((item) =>
          fullfillImage(item.url || item, '!200x200.q100.jpg')
        );

        const goodsOriginPictures =
          goodsInfo.goodsOriginPictures || goodsInfo.goodsMainPictures;
        const originImgList = goodsOriginPictures.map(
          (item) => item.url || item
        );
        this.setYZData({
          activityMinPrice,
          realPrice,
          originPrice,
          promoteText,
          imgList,
          showImgList,
          originImgList,
        });
      },
    },
    config: {
      type: Object,
      value: () => {
        return {};
      },
    },
    shopRights: {
      type: Object,
      value: () => {
        return {};
      },
      observer(rights) {
        const { barClass } = this.data;
        this.setYZData({
          showPublish: rights.allowMoments,
          ifPublish: rights.allowMoments,
          barClass:
            barClass +
            (rights.allowMoments && this.data.showPersonalSpace
              ? ''
              : ' bar-right'),
          publishIconColor: rights.allowMoments ? this.data.color : '#ccc',
        });
      },
    },
    showPersonalSpace: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    imgWidth: 108,
    rangeStyle: 'left: 0;',
    shortPromoteUrl: '',
    ifPublish: true,
    barClass: 'bar',
    promoteText: '',
    showPublish: false,
    publishIconColor: '',
  },

  attached() {},

  ready() {
    const { shareData, smartTextData } = this.data;
    const kdtId = app.getKdtId();
    const commonParams = {
      ...getSalesmanParamsObject({ sl: shareData.seller || '', kdtId }),
      // KDT_ID为当前店铺 商品页面逻辑为短链带上当前店铺id dsKdtId只为确保全局店铺id不存在情况兜底
      // dsKdtId为当前分销员所在店铺id
      sub_kdt_id: kdtId || shareData.dsKdtId,
    };
    let url = this.getUrl();
    url = args.add(url, {
      ...commonParams,
      share_cmpt: 'savepictures',
    });

    if (smartTextData) {
      const { extra, promoteText } = smartTextData;
      if (extra?.alg) {
        url = args.add(url, { alg: extra.alg });
      }
      if (promoteText) {
        url = args.add(url, { content: 'aiwriting' });
      }
    }

    request({
      path: '/wscshop/ump/salesman/short-url.json',
      data: {
        url,
      },
    })
      .then((res) => {
        this.setYZData({
          shortPromoteUrl: res.value,
        });
      })
      .catch(() => {
        this.setYZData({
          shortPromoteUrl: url,
        });
      });
  },

  methods: {
    onScrollComputedWidth(e) {
      const that = this;
      wx.getSystemInfo({
        success(res) {
          const { imgWidth } = that.data;
          // 图片外壳带有32px的padding
          const percent =
            e.detail.scrollLeft /
            (imgWidth * that.data.imgList.length - res.windowWidth + 32);
          that.setYZData({
            rangeStyle: `left: ${percent * 28}px;`,
          });
        },
      });
    },
    getPriceStr(maxPrice, minPrice) {
      return maxPrice === minPrice || !minPrice
        ? `${(+maxPrice / 100).toFixed(2)}`
        : `${(+minPrice / 100).toFixed(2)}-${(+maxPrice / 100).toFixed(2)}`;
    },
    getCurrentMaterialItem(goodsInfo = {}) {
      const {
        alias,
        itemId,
        id,
        goodsId,
        picture,
        pic,
        title,
        goodsPrice = {},
        goodsAlias,
      } = goodsInfo;
      const { maxPrice, realPrice } = goodsPrice;
      return {
        alias: goodsAlias || alias,
        goodsId: id || itemId || goodsId,
        image: pic || picture,
        title,
        price: +maxPrice || +realPrice * 100,
      };
    },
    changePublishState() {
      this.setYZData({
        ifPublish: !this.data.ifPublish,
        publishIconColor: !this.data.ifPublish ? this.data.color : '#ccc',
      });
    },
    preview(e) {
      const { index } = e.currentTarget.dataset;
      const { imgList } = this.data;
      wx.previewImage({
        urls: imgList,
        current: imgList[index],
      });
    },
    publish() {
      const { ifPublish, goodsInfo, shareData, smartTextData } = this.data;
      const { seller, salesmanType } = shareData;
      const params = { sl: seller, salesman_type: salesmanType };
      if (smartTextData && smartTextData.promoteText) {
        params.content = 'aiwriting';
      }
      setLogger(SHARE_LOGGER_CONFIG.savepictures, params);
      const state = {
        copy: 0,
        image: 1,
      };
      Promise.all([
        this.publishMaterial(ifPublish, goodsInfo),
        this.copyText(),
        api.saveImageList.call(this),
      ]).then((res) => {
        wx.hideLoading();
        const result = {
          ...state,
          ...res[0],
          ...res[1],
          ...res[2],
        };
        this.triggerEvent('publish-success', result);
      });
    },

    publishMaterial(ifPublish, goodsInfo) {
      return new Promise((resolve) => {
        // 发布动态
        if (ifPublish && this.data.showPersonalSpace) {
          request({
            path: '/wscump/salesman/zone/publishMoments.json',
            method: 'POST',
            data: {
              imageList: this.data.imgList.slice(0, 9),
              goods: this.getCurrentMaterialItem(goodsInfo),
              recommendInfo: escape(this.data.promoteText),
            },
          })
            .then(() => {
              resolve({ material: 1 });
            })
            .catch(() => {
              resolve({ material: 0 });
            });
        } else {
          resolve({});
        }
      });
    },

    copyText() {
      return new Promise((resolve) => {
        const { shortLinkPromoteUrl } = this.properties;
        wx.setClipboardData({
          data:
            this.data.promoteText +
            (shortLinkPromoteUrl || this.data.shortPromoteUrl),
          success: () => {
            resolve({ copy: 1 });
          },
        });
      });
    },

    getUrl() {
      const { goodsInfo = {} } = this.data;
      const { alias } = goodsInfo;
      const { activityType, activityId, activityAlias } =
        goodsInfo.goodsActivityInfo;
      const posterAlias = activityAlias || alias;
      let url = buildUrl(`/v2/goods/${posterAlias}`, 'h5', app.getKdtId());
      if (activityType === 'seckill') {
        url = `https://h5.youzan.com/v2/seckill/${posterAlias}`;
      }
      if (activityType === 'helpCut') {
        url += `?activityId=${activityId}&activityType=helpCut`;
      }
      return url;
    },
  },
});
