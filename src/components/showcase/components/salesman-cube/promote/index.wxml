<view class="box">
  <view class="promote__text">
    {{ promoteText }}{{ shortLinkPromoteUrl || shortPromoteUrl }}
  </view>
  <view class="promote">
    <scroll-view id="list" class="promote__scroll" scroll-x bindscroll="onScrollComputedWidth">
      <view class="promote__wrapper">
        <view
          class="promote__list"
          wx:for="{{ showImgList }}"
          wx:key="index"
          data-index="{{ index }}"
          bindtap="preview"
        >
          <image
            data-index="{{ index }}"
            src="{{ item }}"
            class="promote__list-img"
          />
        </view>
      </view>
    </scroll-view>
  </view>
  <view wx:if="{{ imgList.length > 3 }}" class="range">
    <view style="{{ rangeStyle }}" class="range__after" />
  </view>
  <view class="{{ barClass }}">
    <van-icon
      wx:if="{{ showPublish && showPersonalSpace }}"
      bindtap="changePublishState"
      name="checked"
      color="{{ publishIconColor }}"
      size="14px"
    />
    <text class="span" wx:if="{{ showPublish && showPersonalSpace }}">同步分享到空间动态</text>
    <view class="bar-btn" style="background-color: {{ color }}" bindtap="publish">
      <van-icon
        name="//b.yzcdn.cn/salesman/cube/saveicon.png"
        size="14px"
      />
      <text class="span">保存图文</text>
    </view>
  </view>
</view>