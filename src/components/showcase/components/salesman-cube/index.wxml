<form-view wx:if="{{ cubeVisible || getShareOpportunity === 'show' }}">
  <view bindtap="handleIconTap">
    <slot wx:if="{{ customIcon }}" name="icon"></slot>
    <view
      wx:else
      class="salesman-icon {{ goaway ? 'goaway' : '' }}"
      style="bottom: {{ bottom ? bottom : '' }}; right: {{ bottom ? '9px' : '' }}; transition: {{ bottom ? 'all 0.2s' : '' }};"
    >
      <image src="{{ iconUrl }}" />
    </view>
  </view>
</form-view>

<van-popup
  wx:if="{{ hasOpenPopup }}"
  show="{{ showPopup }}"
  position="bottom"
  z-index="{{ 99999 }}"
  custom-style="border-radius: 20px 20px 0 0;"
  overlay="{{ showPopupOverlay }}"
  bind:click-overlay="handleTogglePanel"
>
  <form-view>
    <view id="frame" class="panel frame" style="{{ special ? 'height: 90vh;' : '' }}">
      <van-icon
        name="close"
        size="16"
        custom-class="panel-close"
        bindtap="handleTogglePanel"
      />
      <view class="frame-header" style="{{ commission > 0 || hasCommissionRange ? 'height: 64px;' : 'height: 44px;' }}">
        <view class="frame-header__panel-title" wx:if="{{ commissionRange.length }}">
          {{ hasCommissionRange ? commissionRangeTextInfo.text : '立即分享给好友' }}
          <text class="span" wx:if="{{ hasCommissionRange }}">{{ commissionRangeTextInfo.commission }}</text>
        </view>
        <view class="frame-header__panel-title" wx:else>
          {{ commission > 0 ? '分享后预计可赚' : '立即分享给好友' }}
          <text class="span" wx:if="{{ commission > 0  }}">￥{{ commission }}</text>
        </view>
        <view wx:if="{{ commission > 0 || hasCommissionRange }}" class="frame-header__panel-desc">
          佣金可在“{{ shareData.isShoppingGuide ? '导购工作台' : shareData.salesmanName ? shareData.salesmanName + '中心' : '分销员中心' }}”里查看
        </view>
      </view>
      <view class="frame-content" wx:if="{{ special }}">
        <van-tabs
          wx:if="{{ goodsInfo && showPopup }}"
          active="{{ active }}"
          line-width="{{ 24 }}"
          color="{{ themeMainBgColor }}"
          bind:change="changeTab"
        >
          <van-tab
            title="推广图文"
          >
            <view class="tab-content" style="height: {{ popupHeight - (commission > 0 || hasCommissionRange ? 108 : 88) }}px;">
              <promote
                class="tab-promote"
                goods-info="{{ goodsInfo }}"
                shop-rights="{{ rightsInfo }}"
                share-data="{{ shareData }}"
                short-link-promote-url="{{ shortLinkPromoteUrl }}"
                show-personal-space="{{personalSpaceAbility}}"
                color="{{ themeMainBgColor }}"
                smartTextData="{{ smartTextData }}"
                bind:publish-success="publishSuccess"
              />
              <scroll-view
                scroll-x
                class="panel-icon"
                style="{{'height:' + iconList.length * 100 + 'px'}}"
              >
                <template is="icon-list" data="{{ iconList }}" />
              </scroll-view>
            </view>
          </van-tab>
          <van-tab
            title="生成海报"
          >
            <view class="tab-content" style="height: {{ popupHeight - (commission > 0 || hasCommissionRange ? 108 : 88) }}px;">
              <poster
                bind:complete="tryOpenAssistantPop"
                share-data="{{ shareData }}"
                goods-info="{{ goodsInfo }}"
                height="{{ popupHeight - (commission > 0 || hasCommissionRange ? 108 : 88) }}"
                isWxShareImg="{{ isWxShareImg }}"
                goodsPosterInfo="{{ goodsPosterInfo }}"
              />
            </view>
          </van-tab>
        </van-tabs>
      </view>
      <scroll-view
        wx:else
        scroll-x
        class="panel-icon"
        style="height: {{ iconList.length * 100 }}px;"
      >
        <template is="icon-list" data="{{ iconList }}" />
      </scroll-view>
    </view>
  </form-view>
</van-popup>

<van-popup
  wx:if="{{ hasOpenPopup }}"
  show="{{showGuide}}"
  z-index="{{1000}}"
  custom-style="left: 0;right:0;top:0;bottom:0;background-color: transparent;transform: none;"
>
  <begin-boarding bind:complete="handleCompleteGuide" guideConfig="{{guideConfig}}" />
</van-popup>

<poster-popup wx:if="{{ hasOpenPopup }}" bind:close="handleTogglePoster" show="{{showPoster}}" posterImg="{{ poster }}" />
<poster-popup wx:if="{{ hasOpenPopup }}" needDownload="{{true}}" bind:close="handleCloseFeaturePoster" show="{{showFeaturePoster}}" posterImg="{{ featurePosterData }}" />
<poster-popup wx:if="{{ hasOpenPopup }}" needDownload="{{true}}" bind:close="handleToggleWeappCode" show="{{showWeappCode}}" posterImg="{{weappCode}}" />

<van-popup
  wx:if="{{ hasOpenPopup }}"
  show="{{ showTaxSign }}"
  z-index="{{ 999 }}"
  custom-style="background-color: transparent"
  bind:click-overlay="handleToggleTaxSign"
>
  <view class="task-sign">
    <image
      src="https://b.yzcdn.cn/image/salesman/task-banner.png"
      class="task-sign__banner"
    />
    <view class="task-sign__text">
      {{ signText }}
    </view>
    <view
      class="task-sign__btn"
      bindtap="handleCert"
    >
      立即签约
    </view>
    <view
      class="task-sign__next"
      bindtap="handleClose"
    >
      下次再说
    </view>
  </view>
</van-popup>

<van-popup
  wx:if="{{ hasOpenPopup }}"
  show="{{ showAssistant }}"
  z-index="{{ 1001 }}"
  custom-style="bottom: {{ popupBottom }}px;"
  bind:click-overlay="closeAssistantPop"
  position="bottom"
  round
>
  <view class="assistant-pop">
    <view class="assistant-pop__title">智能助理</view>
    <view class="assistant-pop__subtext">想知道如何快速上手卖货？快来开通智能助理</view>
    <image class="assistant-pop__img" src="https://img01.yzcdn.cn/upload_files/2021/03/09/Fnxvvz23Njr8UTvjpm5Cf4q53PMb.jpg" />
    <view class="assistant-pop__action">长按识别二维码关注</view>
    <view class="assistant-pop__confirm">
      <van-button
        color="linear-gradient(45deg, #ffaa1e, #ff720d)"
        round
        bindtap="closeAssistantPop"
        size="large"
        custom-style="height: 40px;"
      >
        我知道了
      </van-button>
    </view>
  </view>
</van-popup>


<van-dialog
  wx:if="{{ hasOpenPopup }}"
  use-slot
  show="{{ showSaveTip }}"
  bind:close="handleToggleSaveTip"
  width="240px"
  confirmButtonText="我知道了"
  confirmButtonColor="{{ themeMainBgColor }}"
>
  <view class="tips-wrap">
    <view class="tips-content">
      <view wx:for="{{ status }}" wx:for-item="tip" class="tip-item {{ tip.state ? 'comm-text' : 'error-text'}}">
        <van-icon wx:if="{{ tip.state }}" name="passed" />
        <van-icon wx:else name="close" />
        <text class="tip-text">{{ tip.text }}</text>
      </view>
    </view>
  </view>
</van-dialog>

<template name="icon-list">
  <view
    wx:for="{{ iconList }}"
    wx:for-item="rowConfig"
    wx:key="{{ index }}"
    class="{{index > 0 ? 'sm-icon-list-row sm-icon-list-row--border' : 'sm-icon-list-row'}}"
  >
    <view
      name="icon-option"
      wx:for="{{ rowConfig }}"
      wx:for-item="item"
      wx:key="index"
      class="sm-icon-list-row__option"
    >
      <button
        class="sm-icon-list-row__option"
        open-type="{{ item.openType }}"
        data-config="{{ item }}"
        data-alias="{{ item.alias }}"
        bindtap="handleClickIcon"
      >
        <image src="{{ item.iconUrl }}" class="sm-icon-list-row__icon" />
        <text class="sm-icon-list-row__name">{{ item.title }}</text>
      </button>
    </view>
  </view>
</template>

<van-popup wx:if="{{ isShowGif }}" show z-index="{{ 1001 }}" custom-style="top: 228px;border-radius: 8px 8px 0 0;">
  <view>
    <image class="sub-guide" src="https://img.yzcdn.cn/upload_files/2020/03/25/Fq24gCcTJhE3bpk9giOf7LPqC-7H.gif" />
  </view>
</van-popup>
<canvas
  wx:if="{{ hasOpenPopup }}"
  canvas-id="salesman-icon"
  class="poster-canvas"
></canvas>

<van-dialog
  wx:if="{{ isWecomMPEnv }}"
  use-slot
  show="{{ showShareWxTips }}"
  confirmButtonOpenType="share"
  confirmButtonText="去聊天列表"
  confirmButtonColor="#ff720d"
  custom-class="wechat-tips__dialog"
  bind:close="closeShareWechartTips"
>
  <view class="wechat-tips__desc">
    <text>在企业微信聊天列表右上角</text>
    <text>点击「转到微信」</text>
  </view>
  <image class="wechat-tips__img" src="https://img01.yzcdn.cn/upload_files/2024/12/23/Funw4qQh2s40ybhcdJJSYw5jb1ZQ.png" />
</van-dialog>