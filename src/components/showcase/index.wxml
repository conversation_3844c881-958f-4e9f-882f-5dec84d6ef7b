<import src="../showcase/topNav/index.wxml" />

<template name="showcase">
  <!-- 根据类型展示不同模板 -->
  <view class="theme-{{ type }}">
    <showcase-container
      id="showcase-container"
      wx:if="{{ type === 'feature' }}"
      components="{{ theme.feature }}"
      unique-key="{{ theme.uniqueKey }}"
      components-length="{{ theme.componentsLength }}"
      shop-name="{{ CURRENT_GLOBAL_SHOP.shop_name }}"
      shop-logo="{{ CURRENT_GLOBAL_SHOP.logo }}"
      hq-kdt-id="{{  CURRENT_GLOBAL_SHOP.chainStoreInfo.rootKdtId }}"
      extra="{{ theme.extra }}"
      page-lifetimes="{{ ['onReachBottom', 'onPullDownRefresh'] }}"
      bind:buy="showcaseHandleGoodsBuy"
      alias="{{ alias }}"
    />

    <template
      wx:else
      is="theme-{{ type }}"
      data="{{ alias, themeClass, CURRENT_GLOBAL_SHOP, isMultiStore, type, richTextList, banner, tags, goods, systemInfo, scrollIntoView, scrollTop, fixedGoodsTag, theme: theme[type], extra: theme.extra, allTheme: theme, asyncShowcaseComponents, pageCommonData, pageUtils }}"
    />
  </view>

  <!-- 悬浮窗 -->
  <floating-nav wx:if="{{ showFloatingNav }}" salesman="{{ salesman }}" feature-alias="{{ alias }}" />
  <marketing-page-icon wx:if="{{ showFloatingNav }}"/>

  <feature-sku
    wx:if="{{ theme.featureSkuData }}"
    theme-class="{{ themeClass }}"
    feature-sku-data="{{ theme.featureSkuData }}"
    bind:close="handleSkuClose"
  />
</template>
