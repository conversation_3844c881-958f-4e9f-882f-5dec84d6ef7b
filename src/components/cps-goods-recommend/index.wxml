<view class="wrapper wrapper-{{ wrapperType }}" wx:if="{{ visible }}" bindtap="navigateToList">
  <view class="text">
    <view class="name">周边好物</view>
    <view class="desc">
      为你推荐的精选好物
      <van-icon name="arrow" class="desc_icon" />
    </view>
  </view>
  <view class="goods">
    <block wx:for="{{ goods }}" wx:key="id">
      <view class="goods-item">
        <image class="goods-item_img" src="{{ item.imageUrl }}" />
        <view class="goods-item_price">¥ {{ item.price }}</view>
      </view>
    </block>
  </view>
</view>
