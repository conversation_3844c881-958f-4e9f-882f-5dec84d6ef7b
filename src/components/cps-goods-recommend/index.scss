@import '~shared/common/css/_variables.scss';

.wrapper {
  background-image: url('https://b.yzcdn.cn/public_files/2a3cdb2d732d140631282f3a6356fe75.png');
  background-repeat: no-repeat;
  background-size: 100% 105px;
  padding: 16px;
  display: flex;
  justify-content: space-between;

  .text {
    margin: 16px 0 12px 0;

    .name {
      color: $text-color;
      font-size: 18px;
      font-weight: bold;
      line-height: 24px;
      margin-bottom: 2px;
    }

    .desc {
      font-size: 12px;
      line-height: 18px;
      position: relative;
      color: $gray-dark;

      &_icon {
        position: absolute;
        top: 2px;
        font-size: 14px;
      }
    }
  }

  .goods {
    display: flex;

    .goods-item {
      width: 72px;
      height: 72px;
      overflow: hidden;
      border-radius: 8px;
      position: relative;

      &_img {
        width: 100%;
        height: 100%;
      }

      &_price {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 20px;
        background-color: rgba(#000, 0.48);
        font-size: 12px;
        line-height: 20px;
        font-weight: bolder;
        color: #f7f8fa;
        padding-left: 4px;
        white-space: nowrap;
      }
    }

    .goods-item:not(:first-child) {
      margin-left: 12px;
    }
  }
}

.wrapper-flat {
  margin: 8px 0;
}
.wrapper-card {
  margin: 8px 12px;
  border-radius: 8px;
}

@media screen and (min-width: 768px) {
  .wrapper {
    background-image: url('https://b.yzcdn.cn/public_files/3b38b3cf10996481ca3852147ed73645.png');
  }
}
