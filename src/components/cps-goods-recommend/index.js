import WscComponent from 'pages/common/wsc-component/index';
import spm from 'shared/utils/spm';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';

const app = getApp();
const CpsGoodsParams = {
  pageNum: 1,
  pageSize: 2,
  bizName: 'promotion~cps',
};

WscComponent({
  properties: {
    kdtId: {
      type: Number,
      value: app.getKdtId(),
    },
    buyerId: {
      type: Number,
      value: app.getBuyerId(),
    },
    cpsConfigKey: {
      type: String,
      value: '',
    },
    wrapperType: {
      type: String,
      value: 'card',
    },
  },

  data: {
    goods: [],
    visible: false,
  },

  attached() {
    // “周边好物” 组件展示条件过滤
    const { cpsConfigKey } = this.data;
    app
      .request({
        path: '/wscstatcenter/cps/queryCpsConfig.json',
        method: 'GET',
        data: {
          params: JSON.stringify({ configKeys: [cpsConfigKey] }),
        },
      })
      .then((configs) => {
        if (configs[cpsConfigKey]) {
          const { enable } = JSON.parse(configs[cpsConfigKey]);
          if (enable) {
            this.setYZData({ visible: true });
            this.getRecommendResultData();
          }
        }
      })
      .catch((e) => {
        wx.showToast({ title: e.msg || '店铺配置获取失败', icon: 'none' });
      });
  },

  methods: {
    // 获取推荐商品
    getRecommendResultData() {
      app
        .request({
          path: 'wscstatcenter/recommend/getRecommendResultData.json',
          method: 'GET',
          data: {
            params: {
              ...CpsGoodsParams,
              kdtId: this.data.kdtId,
              buyerId: this.data.buyerId,
            },
          },
        })
        .then(({ list = [] }) => {
          this.setYZData({ goods: list });
          app.logger &&
            app.logger.log({
              et: 'view',
              ei: 'component_view',
              en: '周边好物入口展示',
              params: {
                banner_id: this.getBannerId(),
              },
            });
        })
        .catch((e) => {
          this.setYZData({ visible: false });
          wx.showToast({ title: e.msg || '推荐列表获取失败', icon: 'none' });
        });
    },

    getBannerId() {
      return `${spm.getPageSpmTypeId()}~cpsShopRecommend.0~0~${makeRandomString(
        8
      )}`;
    },

    // 打开原生列表页面
    navigateToList() {
      wx.navigateTo({
        url: `/packages/statcenter/cps-goods-list/index?banner_id=${this.getBannerId()}`,
      });
    },
  },
});
