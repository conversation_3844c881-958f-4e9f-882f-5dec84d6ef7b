import WscComponent from 'pages/common/wsc-component/index';
import navigate from 'shared/utils/navigate';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import {
  webviewPagePath,
  defaultShowcaseLayoutConfig,
} from '../../config/constant';

const app = getApp();
const pageRandomNumber = makeRandomString(8);
WscComponent({
  name: 'showcase-item',

  options: {
    multipleSlots: true,
  },

  properties: {
    title: String,
    type: String,
    className: String,
    iconUrl: String,
    bizName: String,
    showCornerMark: Boolean,
  },

  data: {
    ...defaultShowcaseLayoutConfig,
    pageRandomNumber,
  },

  attached() {
    this.initConfig();
  },

  methods: {
    // 初始化配置
    initConfig() {
      const { showCornerMark } = this.data;
      this.setYZData({
        layoutConfig: {
          ...this.data.layoutConfig,
          showCornerMark,
        },
      });
    },

    // 跳转落地页
    jumpPage() {
      const kdtId = app.getKdtId();
      const oid = app.getOfflineId();
      const viewSrc = `/wscstatcenter/recommend-showcase-page/${this.data.type}?kdtId=${kdtId}&oid=${oid}`;
      navigate.navigate({
        url: `${webviewPagePath}?src=${encodeURIComponent(viewSrc)}&title=${
          this.data.title
        }`,
      });
    },

    // 请求成功回调
    handleSuccessFn(event) {
      const { recommendList = [] } = event.detail;
      if (recommendList.length === 0) {
        this.setYZData({
          showItem: false,
        });
      }
      if (recommendList.length >= 10) {
        this.setYZData({
          showLastSelfDefineItem: true,
        });
      }
      this.triggerEvent('render-success', recommendList.length);
    },
  },
});
