  <view
    wx:if="{{ showItem }}"
    class="showcase-item {{ className }}"
  >
    <!-- 橱窗项头部标题 -->
    <view class="showcase-item__header" bindtap="jumpPage">
      <view class="showcase-item__header-main">
        <image
          src="{{ iconUrl }}"
          class="showcase-item__header-icon"
          alt="项目图标"
        />
        <text>{{ title }}</text>
      </view>
      <view class="showcase-item__more">
        查看更多
        <van-icon name="arrow" class="showcase-item__more-icon" />
      </view>
    </view>

    <!-- 橱窗项列表 -->
    <goods-recommend
      biz-name="{{ bizName }}"
      goods-number="{{ goodsNumber }}"
      layout-config="{{ layoutConfig }}"
      page-random-number="{{ pageRandomNumber }}"
      component-index="{{ componentIndex }}"
      custom-recommend-name="{{ recommendName }}"
      generic:goods-item-corner-mark="corner-mark"
      show-last-self-define-item="{{ showLastSelfDefineItem }}"
      bind:afterload="handleSuccessFn"
    >
      <view slot="last-item-in-list">
        <view class="list-more-btn" bindtap="jumpPage">
          <view class="list-more-btn-icon" />
          查看更多
        </view>
      </view>
    </goods-recommend>
  </view>