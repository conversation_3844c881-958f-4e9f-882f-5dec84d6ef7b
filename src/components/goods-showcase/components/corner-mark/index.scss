
.ranking-mark {
  position: absolute;
  left: 4px;
  width: 28px;
  height: 29px;
  line-height: 27px;
  font-size: 13px;
  color: #969799;
  font-weight: 700;
  font-family: 'Avenir Next Condensed';
  text-align: center;
  background-image: url('https://b.yzcdn.cn/wsc-h5-statcenter/recommend-showcase-page/ranking_marks_sprite.png');
  background-size: 27px;
  background-repeat: no-repeat;

  &.rank-1-to-3 {
    top: 0;
  }

  &.rank-above-3 {
    top: 3px;
    height: 27px;
  }

  @for $i from 1 through 3 {
    &--#{$i} {
      background-position: 0 (30px - 31px * $i);
    }
  }

  @for $i from 4 through 10 {
    &--#{$i} {
      background-position: 0 (-65px - 27px * ($i - 3));
    }
  }
}