import WscComponent from 'pages/common/wsc-component/index';
import { showcaseItemList } from './config/constant';

WscComponent({
  name: 'goods-showcase',

  options: {
    pureDataPattern: /^_/
  },

  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        if (val) {
          !this.data.inited &&
            this.setYZData({
              inited: true
            });
        }
      }
    }
  },

  data: {
    showcaseItemList,
    showDefault: false, // 无数据显示默认图
    _emptyListStatus: new Array(3).fill(false) // 空列表数据状态
  },

  methods: {
    // 处理空数据状态
    handleSuccessFn({ currentTarget, detail }) {
      const index = currentTarget.dataset.index;
      if (detail === 0) {
        const emptyListStatus = this.data._emptyListStatus;
        emptyListStatus[index] = true;
        // 橱窗足迹&热榜&推荐列表都为空时显示默认图
        this.setYZData({
          showDefault: emptyListStatus.every((it) => it)
        });
      }
    },

    closePop() {
      this.triggerEvent('close');
    }
  }
});
