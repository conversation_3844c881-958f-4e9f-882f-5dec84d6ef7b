<van-popup
  wx:if="{{ inited }}"
  show="{{ show }}"
  round
  custom-class="showcase-popup"
  safe-area-inset-bottom
  z-index="{{ 299 }}"
  position="bottom"
  bind:close="closePop"
>
  <!-- 橱窗标题栏 -->
  <view class="showcase-popup__header">
    商品橱窗
    <van-icon
      name="cross"
      color="#c8c9cc"
      size="22px"
      class="showcase-popup__close"
      bind:click="closePop"
    />
  </view>
  <!-- 橱窗内容区 -->
  <view class="showcase-popup__container">
    <block wx:for="{{ showcaseItemList }}" wx:key="bizName">
      <showcase-item
        className="{{ item.type }}-showcase-item {{ item.className }}"
        data-index="{{ index }}"
        title="{{ item.title }}"
        type="{{ item.type }}"
        icon-url="{{ item.iconUrl }}"
        biz-name="{{ item.bizName }}"
        show-corner-mark="{{ item.showCornerMark }}"
        bind:render-success="handleSuccessFn"
      />
    </block>

    <!-- 默认状态显示 -->
    <view wx:if="{{ showDefault }}">
      <image
        class="showcase-popup__empty-img"
        src="https://b.yzcdn.cn/public_files/f10a2f03fbcc0353b0c66fdc7be7b1e2.png"
        alt="暂无商品"
      />
      <view class="showcase-popup__empty-text">
        暂无商品
      </view>
    </view>
  </view>
</van-popup>
