export const webviewPagePath = '/pages/common/webview-page/index';

export const showcaseItemList = [
  {
    title: '店铺热榜',
    iconUrl:
      'https://b.yzcdn.cn/public_files/49c98c9a4b9ee7070cda50774423b65e.png',
    showCornerMark: true,
    className: 'ranking-showcase-item',
    bizName: 'g~hotGoods',
    type: 'ranking',
  },
  {
    title: '为你推荐',
    iconUrl:
      'https://b.yzcdn.cn/public_files/991bcb683a19c3cdb26fe11fbc5c3c79.png',
    showCornerMark: false,
    bizName: 'g~mayLike',
    type: 'recommend',
  },
  {
    title: '历史足迹',
    iconUrl:
      'https://b.yzcdn.cn/public_files/4a96aeb556b1b55dd5341b35db8df464.png',
    showCornerMark: false,
    bizName: 'g~history',
    type: 'footmark',
  },
];

export const defaultShowcaseLayoutConfig = {
  goodsNumber: 10,
  componentIndex: 0,
  showItem: true,
  showLastSelfDefineItem: false,
  layoutConfig: {
    layout: 6, // 左右滑动
    sizeType: 2, // 极简样式
    imageRatio: 1, // 1:1
    showTitle: false,
    showSubTitle: false,
    showBuyButton: false,
    showCornerMark: false,
    showUmpTags: true,
    umpTagsMaxCount: 1,
    borderRadiusType: 2, // 圆角
    imageFillStyle: 1, // 留白
    textStyleType: 2, // 加粗体
    goodsMargin: 12,
    pageMargin: 16,
    showPrice: true,
  },
  recommendName: '智能商品悬浮窗',
};
