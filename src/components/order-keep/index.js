import WscComponent from 'pages/common/wsc-component/index';
import Theme from 'shared/common/components/theme-view/theme';

const app = getApp();

const ENUM = {
  coupon: '优惠券',
  points: '积分',
  stock: '库存紧张',
  praise: '好评数',
  evaluationTag: '评价标签',
  sales: '销量',
  activity: '活动',
  discount: '优惠金额',
};
WscComponent({
  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'observerShow',
    },
    displayData: {
      type: Object,
      observer: 'formattedData',
    },
  },

  data: {
    couponValueStyle: '',
    type: '',
    hint: '',
    coupon: {},
    hasCoupon: false,
    tags: [],
    format: {},
  },

  attached() {
    Theme.getThemeColor('main-bg').then((color) => {
      const rgb = Theme.switchHexToRgb(color);
      this.setData({
        tagBgColor: `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, .1)`,
        mainColor: `color: ${color}!important;`,
        mainBg: color,
      });
    });
  },

  ready() {
    this.maxWidth = 80;
    this.maxFontSize = 34;
    this.minFontSize = 16;
  },

  methods: {
    observerShow(show) {
      this.setYZData({
        couponValueStyle: '',
        loading: true,
      });

      if (!show) {
        return null;
      }
      setTimeout(() => {
        this.computeFontSize();
        this.logDialogShow();
      }, 500);
    },

    logDialogShow() {
      const displayData = this.data.displayData || {};
      const { content = {} } = displayData;
      const { trackType } = content;
      app.logger
      && app.logger.log({
        et: 'view',
        ei: 'continuepay_show',
        en: '下单挽留弹窗展示',
        pt: 'trade',
        params: {
          ordernumber: displayData.orderNo || '',
          money: displayData.price || '',
          type: ENUM[trackType],
        },
      });
    },

    async computeFontSize() {
      const {
        content: { type, coupon },
      } = this.data.displayData;

      if (type === 'COUPON') {
        const { valueDesc, unitDesc } = coupon || {};
        const priceWidth = await this.getBoundingClientRectWidth(
          '.order-keep__coupon--price'
        );
        const width = await this.getBoundingClientRectWidth(
          '.order-keep__coupon--unit'
        );
        const unitWidth = width;
        const decorateWidth = unitWidth;
        const scaleRatio = (this.maxWidth - decorateWidth) / priceWidth;
        let finalSize = 0;
        if (scaleRatio < 1) {
          const scaleFontSize = Math.trunc(scaleRatio * this.maxFontSize);
          finalSize = Math.max(scaleFontSize, this.minFontSize);
        } else {
          // 没有单位 是兑换商品类的折扣券
          const isNotVal = unitDesc === undefined || unitDesc === null || unitDesc === '';
          // 只针对兑换商品 没兜住的情况
          if (valueDesc && valueDesc.length > 3 && isNotVal) {
            // 兜底 担心样式破了
            finalSize = 20;
          }
        }
        // 普通场景 一般finalSize会为0 不会走到这里
        if (finalSize) {
          this.setYZData({
            couponValueStyle: `font-size: ${finalSize}px!important;`,
          });
        }
      }
      this.setYZData({
        loading: false,
      });
    },

    getBoundingClientRectWidth(selector) {
      return new Promise((resolve) => {
        const query = this.createSelectorQuery();
        query
          .select(selector)
          .boundingClientRect((res) => {
            const result = res || {};
            resolve(result.width || 0);
          })
          .exec();
      });
    },

    formattedData(value) {
      const { content = {} } = value || {};
      const { type = '', hint = '', trackType } = content;
      let { coupon, tags, format } = content;
      coupon = coupon || {};
      format = format || {};
      tags = tags || [];
      this.trackType = trackType;
      this.setYZData({
        type,
        hint,
        coupon,
        hasCoupon: coupon ? Object.keys(coupon).length > 0 : false,
        tags,
        format,
      });
    },

    onClose() {
      const displayData = this.data.displayData || {};
      app.logger
        && app.logger.log({
          et: 'click',
          ei: 'continuepay_clickcancel',
          en: '下单挽留弹窗点击取消支付',
          pt: 'trade',
          params: {
            ordernumber: displayData.orderNo || '',
            money: displayData.price || '',
            type: ENUM[this.trackType],
          },
        });
      this.triggerEvent('close');
    },

    onConfirm() {
      const displayData = this.data.displayData || {};
      app.logger
        && app.logger.log({
          et: 'click',
          ei: 'continuepay_clickcontinue',
          en: '下单挽留弹窗点击继续支付',
          pt: 'trade',
          params: {
            ordernumber: displayData.orderNo || '',
            money: displayData.price || '',
            type: ENUM[this.trackType],
          },
        });
      this.triggerEvent('confirm');
    },
  },
});
