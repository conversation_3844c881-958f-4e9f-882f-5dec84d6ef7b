.order-keep {
  &__item {
    margin-bottom: 24px;
  }

  &__items {
    margin-top: 8px;
  }

  &__container {
    padding-right: 24px;
    padding-left: 24px;
    padding-bottom: 16px;
  }

  &__container__reset__padding {
    padding-right: 0;
    padding-left: 0;
  }

  &__icon {
    height: 22px;
    width: 22px;
    padding-right: 12px;
  }

  &__text {
    color: #646566;
    font-size: 14px;
    line-height: 20px;
    text-align: center;

    &--important {
      font-size: 14px;
      color: #ee0a24;
      padding-right: 4px;
      padding-left: 4px;
      font-weight: bold;
    }
  }

  &__text--tag {
    color: #646566;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    margin-bottom: 24px;
  }

  &--created {
    text-align: center;
    padding-bottom: 12px;
  }

  &__important {
    color: #ee0a24;
    font-size: 20px;
    font-weight: bold;
    padding-left: 2px;
    padding-right: 2px;
  }

  &__tag {
    display: inline-block !important;
    white-space: nowrap;
    margin-bottom: 12px;
    padding: 4px 8px !important;
    font-size: 12px;
    color: #323233 !important;
    letter-spacing: 0;
    line-height: 16px;
    margin-right: 6px;
    margin-left: 6px;

    &--container {
      margin-top: 8px;
      display: block;
    }
  }

  &__btn--disabled {
    color: #969799;
    font-size: 16px;
    text-align: center;
  }

  &__button--continue {
    height: 36px !important;
    line-height: 34px !important;
    margin-bottom: 16px;
    font-size: 16px !important;
    margin-left: auto;
    margin-right: auto;
    border: none;
    display: block;
    width: 100%;
  }

  &__eval {
    &--block {
      display: flex;
      margin-top: 12px;
      margin-bottom: 12px;
      padding: 0 2px;
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  &__coupon {
    &--left {
      padding: 12px 8px;
      box-sizing: content-box;
      box-sizing: border-box;
      display: inline-block;
      margin-top: auto;
      margin-bottom: auto;

      &--block {
        min-width: 80px;
        box-sizing: border-box;
        text-align: center;
        white-space: nowrap;
      }
    }

    &--right {
      padding: 16px 0;
      width: 175px;
      box-sizing: border-box;
      display: inline-block;
    }

    &--price {
      font-size: 34px;
      line-height: 1;
      // color: #ee0a24;
      height: inherit;
      font-weight: 600;
      display: inline-block;
      white-space: nowrap;
    }

    &--unit {
      font-size: 12px;
      // color: #ee0a24;
      height: inherit;
      padding-left: 2px;
      line-height: 30px;
      display: inline-block;
    }

    &--unit--empty {
      font-size: 12px;
      color: #ee0a24;
      height: inherit;
      display: inline-block;
      line-height: 30px;
    }

    &--name {
      font-size: 14px;
      line-height: 14px;
      padding-bottom: 8px;
      color: #323233;
    }

    &--desc {
      font-size: 12px;
      color: #323233;
      line-height: 12px;
    }

    &--container {
      background: #f7f8fa;
      margin-top: 8px;
      height: 72px;
      display: flex;
      align-items: center;
      border-radius: 4px;
    }

    &--block {
      margin-bottom: 24px;
      position: relative;
    }
  }
}
.coupon-view {
  z-index: 1000;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
}
