<van-dialog
  use-slot
  title="{{ displayData.title }}"
  show="{{ show }}"
  show-cancel-button="{{ false }}"
  show-confirm-button="{{ false }}"
  close-on-click-overlay="{{ false }}"
  bind:close="onClose"
  bind:confirm="onConfirm"
>

<view class="order-keep__container">
  <view class="order-keep__items" >
    <!--已下单-->
    <view class="order-keep__item" wx:if="{{ type=== 'FORMAT' }}">
      <view
        wx:if="{{ type=== 'FORMAT' }}"
        class="order-keep__text"
      >
        <text wx:if="{{ format.prefix }}">{{ format.prefix }}</text>
        <text wx:if="{{ format.value }}" class="order-keep__text--important">{{ format.value }}</text>
        <text wx:if="{{ format.suffix }}">{{ format.suffix }}</text>
      </view>
    </view>
    <!-- 普通文本展示-->
    <view class="order-keep__item" wx:if="{{ type=== 'PLAIN_TEXT' }}">
      <view
        wx:if="{{ hint}}"
        class="order-keep__text"
      >{{ hint }}</view>
    </view>
  </view>
  <!--优惠券-->
  <view
    class="order-keep__coupon--block"
    wx:if="{{ type=== 'COUPON' }}">
    <view
      class="order-keep__text"
      wx:if="{{ hint }}"
    >{{ hint }}</view>
    <view wx:if="{{ loading }}" class="coupon-view">
      <van-loading />
    </view>
    <view class="order-keep__coupon--container" wx:if="{{ hasCoupon }}">
      <view class="order-keep__coupon--left">
        <view class="order-keep__coupon--left--block" style="{{ mainColor }}">
          <view
            class="order-keep__coupon--price"
            style="{{ couponValueStyle }}"
          >{{ coupon.valueDesc }}</view>
          <view class="{{ coupon.unitDesc ? 'order-keep__coupon--unit': 'order-keep__coupon--unit--empty' }}" >{{ coupon.unitDesc || '' }}</view>
        </view>
      </view>
      <view class="order-keep__coupon--right">
          <view class="order-keep__coupon--name">{{ coupon.condition }}</view>
          <view class="order-keep__coupon--desc">{{ coupon.timeDesc }}</view>
      </view>
    </view>
  </view>

  <!--评价-->
  <view wx:if="{{ type=== 'TAG_LIST' }}">
    <view
      wx:if="{{ hint }}"
      class="{{ tags.length ? 'order-keep__text': 'order-keep__text--tag'}}"
    >{{ hint }}</view>
    <view class="order-keep__eval--block" wx:if="{{ tags.length }}">
      <van-tag
        round
        color="{{ tagBgColor }}"
        custom-class="order-keep__tag"
        wx:for="{{ tags }}"
        wx:key="index"
        >{{ item.value }}</van-tag>
    </view>
  </view>

  <van-button round bind:click="onConfirm" type="danger" color="{{ mainBg }}" custom-class="order-keep__button--continue">继续支付</van-button>
  <view bind:tap="onClose" class="order-keep__btn--disabled">暂时放弃</view>
</view>

</van-dialog>