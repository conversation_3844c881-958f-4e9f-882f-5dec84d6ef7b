import args from '@youzan/weapp-utils/lib/args';
import openWebView from 'utils/open-web-view';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import ZanError from './error';
import api from '../api';

class ThirdPartyManager {
  constructor(options) {
    this.fail = options.fail || (() => {});

    // api function
    this.requestCODPay = options.requestCODPay || api.requestCODPay;
    this.paying = false;
  }

  /*
   * 小程序微信支付
   */
  doWxAppletPay(payData = {}, callback) {
    wx.requestPayment({
      timeStamp: payData.timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType,
      paySign: payData.paySign,
      success: res => {
        callback(res);
      },
      fail: res => {
        let type = 'WxPayError';

        if (res.errMsg === 'requestPayment:fail cancel') {
          type = 'cancel';
        }

        this.fail(new ZanError(res.errMsg, type, res));
      }
    });
  }

  doCODPay(payData = {}, callback) {
    this.requestCODPay(payData, callback, e => {
      this.fail(new ZanError(e.msg || '货到付款请求失败', 'cod', e));
    });
  }

  doWebViewPay(payData = {}, callback) {
    const domainIndex = (payData.submitDomain && payData.submitDomain.length) || 0;
    const { submitUrl = '' } = payData;
    openWebView(submitUrl.substr(domainIndex), {
      title: payData.submitTitle || '支付',
      domain: payData.submitDomain,
      query: payData.submitData
    });
    callback(payData, callback);
  }

  doBankCardPay(payData, callback) {
    // 可以处理BANK_CARD, CREDIT_CARD等
    const submitData = Object.assign({}, payData.submitData);
    Object.keys(submitData).forEach(item => {
      submitData[item] = encodeURIComponent(submitData[item]);
    });
    const viewUrl = args.add(payData.submitUrl, submitData);
    const url = args.add('/packages/pay/credit-card/index', {
      title: encodeURIComponent(payData.submitTitle || '支付'),
      viewUrl: encodeURIComponent(viewUrl)
    });
    wx.navigateTo({ url });
    callback(payData, callback);
  }

  doCreditCardPay(payData, callback) {
    wx.navigateTo({
      url:
        '/packages/pay/credit-card/index?payTool=CREDIT_CARD&deepLinkData='
        + encodeURIComponent(JSON.stringify(mapKeysCase.toSnakeCase(payData)))
    });
    callback(payData, callback);
  }

  doDefaultPay(payData = {}, callback) {
    callback && callback(payData);
  }
}

export default ThirdPartyManager;
