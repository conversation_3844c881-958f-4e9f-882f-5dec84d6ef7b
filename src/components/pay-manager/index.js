import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import ThirdPartyManager from './components/third-party-manager';
import ZanError from './components/error';

const app = getApp();

// const TIMEOUT_CODE = 99999;
const LINED_UP_CODE = 50002;
const FRAMEWORK_LINED_UP_CODE = 429;
const RETRY_INTERVAL = {
  1: 3000,
  2: 5000,
};

function noop() {}

function autoLinedUp(mainFn, hook = noop, resolvedHook = noop) {
  let timer;
  let num = 0;
  const linedUpFn = (...args) => {
    num++;
    return mainFn(...args)
      .then(res => {
        num = 0;
        clearTimeout(timer);
        resolvedHook();
        return res;
      })
      .catch(err => {
        const {
          retry = false,
          interval = 3000,
          args: newArgs = args,
          err: newErr = err
        } = hook(err, num, args) || {};
        if (retry) {
          return new Promise(resolve => {
            timer = setTimeout(() => {
              resolve(linedUpFn(...newArgs));
            }, interval);
          });
        }
        num = 0;
        return Promise.reject(newErr);
      });
  };
  linedUpFn.cancel = () => {
    num = 0;
    clearTimeout(timer);
  };
  return linedUpFn;
}

function activeLinedUp(code) {
  const active = code === LINED_UP_CODE || code === FRAMEWORK_LINED_UP_CODE;
  return active;
}

class CashierManager {
  constructor(options = {}) {
    this.cashierRequestUrl = options.cashierRequestUrl;
    this.payRequestUrl = options.payRequestUrl;
    this.fail = options.fail;
    this.thirdPartyManager = new ThirdPartyManager(options);
    this.retryHook = options.retryHook || noop;
    this.resolvedHook = options.resolvedHook || noop;

    this._cashierData = Object.create(null);
    this._payData = Object.create(null);
    this._payWays = [];
    this._isPayProcessing = false;
    this._isFetchedPayWays = false; // 是否已获取支付列表
    this._request = autoLinedUp(app.request, this.onRetry, this.onResolved);
  }

  resetPayProcessingToken() {
    this._isPayProcessing = false;
  }

  onRetry = (err, count) => {
    const code = err && err.code;
    const isRetryCode = activeLinedUp(code) && this.retryHook !== noop;
    this.retryHook({ show: isRetryCode, stage: count });
    return { retry: isRetryCode && count < 3, interval: RETRY_INTERVAL[count] || 5000 };
  };

  onResolved = () => {
    this.resolvedHook();
  };

  getPayWays(cashierData, callback) {
    if (this._isPayProcessing) {
      this.fail(new ZanError('支付进行中...'));
      return false;
    }

    this._cashierData = { ...cashierData };
    this._isPayProcessing = true;

    this
      ._request({
        path: this.cashierRequestUrl,
        data: mapKeysCase.toSnakeCase(this._cashierData),
        method: 'POST'
      })
      .then(res => {
        this._isFetchedPayWays = true;
        this._isPayProcessing = false;
        const result = mapKeysCase.toCamelCase(res || {});
        this._payWays = result.payChannels || [];
        callback(this._payWays, result);
      })
      .catch(err => {
        this._isPayProcessing = false;
        this.fail(err);
      });
  }

  doPayAction(payData = {}, callback) {
    if (this.isPayProcessing) {
      this.fail(new ZanError('支付中...'));
      return;
    }

    const payTool = payData.payTool || payData.payChannel;
    if (payTool === 'CASH_ON_DELIVERY') {
      this._doCODPay(payData).then(
        () => {
          this._doExecutePayAction(payData, callback);
        },
        () => {
          this.fail(new ZanError('', 'cancel'));
        }
      );
    } else {
      this._doExecutePayAction(payData, callback);
    }
  }

  _doExecutePayAction(payData, callback) {
    this._isPayProcessing = true;
    Object.assign(this._payData, this._cashierData, payData);
    this
      ._request({
        path: this.payRequestUrl,
        data: mapKeysCase.toSnakeCase(this._payData || {}),
        method: 'POST'
      })
      .then(res => {
        this._isPayProcessing = false;
        const result = mapKeysCase.toCamelCase(res || {});
        // 如果是改价，需要用户同意的话，就先弹出弹窗
        if (result.operation === 'ADJUST_PRICE') {
          this.fail(new ZanError('adjust_price', 'adjust_price', result));
          return;
        }
        // 渠道支付
        this.doPayWithChannel(this._payData.payTool, result.payData || {}, result, callback);
      })
      .catch(err => {
        this._isPayProcessing = false;
        // console.error(err);
        this.fail(err);
      });
  }

  _doCODPay(payData = {}) {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title: '下单提醒',
        confirmText: '确认',
        content: this._getCODMessage(payData.payChannelName),
        success: res => {
          if (res.confirm) {
            resolve();
          } else {
            reject('cancel');
          }
        }
      });
    });
  }

  _getCODMessage(payName) {
    let message = '';
    switch (payName) {
      case '到店付款':
        message = '您正在选择到店付款，下单后请自行到店领取并付款。';
        break;
      // 货到付款
      default:
        message = '您正在选择货到付款，下单后由商家发货，送货上门并收款。';
    }
    return message;
  }

  doPayWithChannel(payChannel, payData, extra, callback) {
    switch (payChannel) {
      // 微信小程序支付
      case 'WX_APPLET':
        this.thirdPartyManager.doWxAppletPay(payData, callback);
        break;
      case 'ECARD': // E卡
      case 'PREPAID_PAY': // 储值余额
      case 'VALUE_CARD': // 会员余额
      case 'GIFT_CARD': // 礼品卡
      case 'ENCHASHMENT_GIFT_CARD': // 新礼品卡
      case 'CASH_ON_DELIVERY': // 货到付款
        // 内部工具支付在支付接口已经完成支付，直接返回
        callback(payData);
        break;
      // 银行卡支付
      case 'CREDIT_CARD':
      case 'BANK_CARD':
        this.thirdPartyManager.doBankCardPay(extra, callback);
        break;
      // 分期支付
      case 'INSTALMENT':
        this.thirdPartyManager.doBankCardPay(payData, callback);
        break;
      // 默认
      default:
        this.thirdPartyManager.doDefaultPay(payData);
    }
  }
}

export default CashierManager;
