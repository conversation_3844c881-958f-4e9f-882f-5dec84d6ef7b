export default {
  requestCODPay(data, callback, failCallBack) {
    // 货到付款
    var requestData = {
      orderNoList: [data.order_no || data.orderNo],
      payType: 'CASH_ON_DELIVERY'
    };
    getApp().carmen({
      api: 'youzan.trade.core.paytype/3.0.0/decide',
      data: {
        request: JSON.stringify(requestData)
      },
      method: 'POST',
      success: res => {
        callback(res);
      },
      fail: res => {
        failCallBack(res);
      }
    });
  }
};
