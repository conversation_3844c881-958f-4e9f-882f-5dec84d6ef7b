import WscComponent from 'pages/common/wsc-component/index';
import debounce from 'utils/debounce';

const app = getApp();

WscComponent({
  data: {
    showGotoCardIcon: false,
    showListIcon: false,
    y: 20
  },

  pageLifetimes: {
    show() {
      const isFromBiCard = app.globalData.bi_card_sales;
      const y = app.globalData.bi_list_icon_posY || 20;

      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const currentPageUrl = currentPage.route;

      const showListIcon = currentPageUrl === 'packages/retail/extra/sales/bi-card/index';
      const showGotoCardIcon = !showListIcon && !!isFromBiCard && currentPageUrl !== 'packages/retail/chat/index' && currentPageUrl !== 'packages/business-card/auth/index';

      if (showListIcon || showGotoCardIcon) {
        this.setYZData({
          y
        });
      }
      this.setYZData({
        showListIcon,
        showGotoCardIcon
      });
    },

    hide() {
      const { showListIcon, showGotoCardIcon } = this.data;
      if (showListIcon || showGotoCardIcon) {
        this.setYZData({
          showListIcon: false,
          showGotoCardIcon: false
        });
      }
    }
  },

  methods: {
    navigateToSalesPage() {
      const source = app.globalData.bi_card_sales_from;
      const biCardUrl = source === 'new_bi_card' ? `/packages/business-card/detail/index?from_params=${app.globalData.from_params}&from_source=${app.globalData.from_source}`
        : `/packages/retail/extra/sales/bi-card/index?from_params=${app.globalData.from_params}&from_source=${app.globalData.from_source}`;

      wx.navigateTo({
        url: this.data.showListIcon ? '/packages/retail/extra/sales/bi-list/index' : biCardUrl
      });
    },
    onChange: debounce((e) => {
      const { detail: { y } } = e;
      app.globalData.bi_list_icon_posY = y;
    }, 100)
  }
});
