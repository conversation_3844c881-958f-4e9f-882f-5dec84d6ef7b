<view class="count-box">
  <view
    class="viewport"
    wx:for="{{columns}}"
    wx:key="index"
    style="width:{{ (length - 3 === index || (length < 3 && index === 0)) ? _options.cellWidth + 12 : _options.cellWidth }}px;height:{{_options.height}}px;"
  >
    <view class="column-wrap" style="transform: translate3d(0, -{{keys[index] * _options.height}}px, 0);transition-duration:{{_options.during}}s; transition-timing-function:{{_options.ease}}">
      <view 
        wx:for="{{item}}" 
        wx:for-item="row"
        wx:for-index="idx"
        wx:key="idx" 
        class="item"
        style="color:{{_options.color}};height:{{_options.height}}px;line-height: {{_options.height}}px;{{_options.columnStyle}}"
      >
        {{ row }}
      </view>
    </view>
  </view>
</view>