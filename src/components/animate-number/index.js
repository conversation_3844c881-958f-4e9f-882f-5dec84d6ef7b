import WscComponent from 'pages/common/wsc-component';

const CONFIG = {
  during: 1.5, // :number 动画时间
  height: 40, // :number 滚动行高 px
  cellWidth: 24, // 单个数字宽度
  ease: 'cubic-bezier(0.5, 1, 0.89, 1)', // 动画过渡效果
  color: '#FF5837', // 字体颜色
  columnStyle: '', // 字体单元 覆盖样式
};

WscComponent({
  properties: {
    value: {
      type: Number,
      observer(n) {
        this.run(n);
      },
    },
    max: {
      type: Number,
      value: 100,
      observer() {
        this.setRange();
      },
    },
    min: {
      type: Number,
      value: 0,
      observer() {
        this.setRange();
      },
    },
    config: {
      type: Object,
      value: {},
      observer() {
        this.renderStyle();
      },
    },
  },

  data: {
    columns: [],
    keys: [],
    _options: JSON.parse(JSON.stringify(CONFIG)),
    maxData: 0,
    minData: 0,
    length: 0,
  },

  created() {
    this.setRange();
    this.renderStyle();
  },

  /**
   * 组件的方法列表
   */
  methods: {
    setRange() {
      let { max, min } = this.properties;
      max *= 100;
      min *= 100;
      min = Math.max(parseInt(min, 10), 0);
      max = Math.max(parseInt(max, 10), min);

      const columns = this.initColumn(max);

      this.setData({
        columns,
        maxData: max,
        minData: min,
      });

      // 范围调整后，修正当前 value
      if (this.properties.value) {
        this.run(this.properties.value);
      }
    },

    initColumn(n) {
      const digit = String(n).padStart(3, '0').length;
      const arr = [];
      const rows = ['0', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
      const rows1 = rows.map((item) => item + '.');
      for (let i = 0; i < digit; i++) {
        const row = i === 2 ? rows1 : rows;
        if (i) {
          arr.unshift(row);
        } else {
          arr.unshift(row.slice(1));
        }
      }
      return arr;
    },

    run(n) {
      const { maxData, minData } = this.data;
      let value = parseInt(n * 100, 10);
      value = Math.min(maxData, Math.max(minData, value));
      const valueArr = value.toString().split('');
      const lengths = this.data.columns.length;
      const indexs = [];
      this.setYZData({
        length: valueArr.length,
      });

      while (valueArr.length) {
        const figure = valueArr.pop();
        if (indexs.length) {
          indexs.unshift(parseInt(figure, 10) + 1);
        } else {
          indexs.unshift(parseInt(figure, 10));
        }
      }
      while (indexs.length < lengths) {
        indexs.unshift(0);
      }
      this.setYZData({
        keys: indexs,
      });
    },

    renderStyle() {
      const { config = {}, _options } = this.data;
      const val = {};
      Object.keys(_options).forEach((i) => {
        val[i] = config[i] || _options[i];
      });
      this.setYZData({
        _options: val,
      });
    },
  },
});
