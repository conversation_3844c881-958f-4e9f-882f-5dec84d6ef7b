<view class="help__block {{ marginTop ? 'margin-top' : '' }}">
  <view class="help__title">
    <view class="title">{{ title }}</view>
  </view>
  <view class="fission-goods-container">
    <view wx:if="{{ showList.length === 0 }}" class="empty" />
    <view
      wx:for="{{ showList }}"
      data-index="{{ index }}"
      bind:tap="goodsClick"
      class="goods-item {{ list.length === 1 ? 'goods-item-single' : '' }}"
    >
      <view wx:if="{{ item.imageUrl }}">
        <view class="pic">
          <image class="img" mode="aspectFill" src="{{ item.imageUrl }}" />
        </view>
        <view class="goods-context">
          <view>￥{{ item.price }}</view>
        </view>
      </view>
    </view>
  </view>
  <view wx:if="{{ needBtn }}" class="vicebtn" bind:tap="enterShop">
    进店逛逛
  </view>
</view>