import WscComponent from 'pages/common/wsc-component/index';
import Navigate from '@/helpers/navigate';

WscComponent({
  properties: {
    marginTop: {
      type: Boolean,
      value: false,
    },
    needBtn: {
      type: Boolean,
      value: false,
    },
    dataType: {
      type: String,
      value: 'goods',
    },
    title: {
      type: String,
      value: '更多超值好物',
    },
    list: {
      type: Array,
      value: [],
      observer(n) {
        this.getData(n);
      },
    },
  },

  data: {
    showList: [],
  },

  methods: {
    getData(n = []) {
      const list = n.slice(0, 3);
      this.setYZData({
        showList: list,
      });
    },

    goodsClick(e) {
      const { index = 0 } = e.currentTarget.dataset;
      const item = this.data.list[index];
      const { alias: goodsAlias } = item;
      Navigate.navigate({
        url: `/pages/goods/detail/index?alias=${goodsAlias}&shopAutoEnter=1`,
      });
    },

    enterShop() {
      Navigate.switchTab({
        url: '/packages/home/<USER>/index',
      });
    },
  },
});
