import navigate from 'shared/utils/navigate';
import WscComponent from 'pages/common/wsc-component';
import args from '@youzan/weapp-utils/lib/args';
import getApp from 'shared/utils/get-safe-app';
import { followReport, getFansArticle, getQrcode } from './api';
import openWebView from 'utils/open-web-view';

const app = getApp();

const H5_LOGO = 'https://img.yzcdn.cn/weapp/wsc/1fxuP9X.png';
const BASE_SHARE_PATH = '/pages/home/<USER>/index';
const msgImg = 'https://img.yzcdn.cn/weapp/wsc/UJ60enB.png';
const BIZ_CODE = {
  bargain: 1, // 砍价
  wheel: 2, // 幸运大抽奖
  benefit: 3, // 让利涨粉
  lottery: 4, // 抽奖码抽奖
  luckyDrawGroup: 9, // 拼团抽奖
};

const SUB_BIZ_CODE = {
  bargain: 0, // 发起砍价
  helpCut: 1, // 帮砍
};

WscComponent({
  properties: {
    show: {
      type: Boolean,
      value: false,
      observer: 'handleShowPopup',
    },
    title: {
      type: String,
      value: '',
    },
    subTitle: {
      type: String,
      value: '还能了解最新优惠信息',
    },
    btnText: {
      type: String,
      value: '',
    },
    extraData: {
      type: Object,
      value: {},
    },
    zIndex: {
      type: Number,
      value: 100,
    },
  },

  data: {
    showPop: false,
    messagePath: '',
    messageImg: msgImg,
    newRiseFollower: false, // 是否走新的涨粉流程
    webViewUrl: '',
  },

  methods: {
    onContactBack: navigate.contactBack,

    handleShowPopup(show) {
      if (!show) {
        this.setYZData({
          showPop: false,
        });
        return;
      }

      const { extraData } = this.data;
      getFansArticle({
        ...extraData,
        feature: JSON.stringify(extraData.feature),
      })
        .then((res) => {
          // 没有值代表未被灰度/降级/历史活动，走老流程
          if (!res?.value) {
            this.handleCustomServiceMessage();
          } else {
            app.logger
              && app.logger.log({
                et: 'display',
                ei: 'enterpage',
                en: '浏览页面',
                params: {
                  component: 'zhangfen',
                  biz_type: this.getBizType(),
                },
              });
            this.setYZData({
              newRiseFollower: true,
              webViewUrl: res.value,
              showPop: true,
            });
          }
        })
        .catch(() => {
          this.handleCustomServiceMessage();
        });
    },

    togglePopup() {
      this.handleReport(1);
      this.triggerEvent('close');
    },

    getBizType() {
      const { bizCode, bizSubCode } = this.data.extraData;
      // 让利涨粉
      if (bizCode === BIZ_CODE.benefit) {
        return 'benefit';
      }
      // 发起砍价
      if (bizCode === BIZ_CODE.bargain && bizSubCode === SUB_BIZ_CODE.bargain) {
        return 'bargain';
      }
      // 帮砍
      if (bizCode === BIZ_CODE.bargain && bizSubCode === SUB_BIZ_CODE.helpCut) {
        return 'helpcut';
      }

      // 幸运大抽奖
      if (bizCode === BIZ_CODE.wheel) {
        return 'wheel';
      }

      // 抽奖码抽奖
      if (bizCode === BIZ_CODE.lottery) {
        return 'lottery';
      }

      if (bizCode === BIZ_CODE.luckyDrawGroup) {
        return 'luckyDrawGroup';
      }
    },

    // 点击成为粉丝
    onClick() {
      this.handleReport(2);

      if (this.data.newRiseFollower) {
        app.logger
          && app.logger.log({
            et: 'click',
            ei: 'click',
            en: '成为粉丝按钮点击',
            params: {
              component: 'zhangfen',
              biz_type: this.getBizType(this.data.extraData),
            },
          });
        openWebView(this.data.webViewUrl);
      }
    },

    handleReport(followWay) {
      const { extraData } = this.data;
      followReport({
        ...extraData,
        followWay,
      });
    },

    handleCustomServiceMessage() {
      const { extraData } = this.data;
      if (this.qrcode) {
        this.getMessagePath();
      } else {
        getQrcode({
          ...extraData,
          feature: JSON.stringify(extraData.feature),
        })
          .then((res) => {
            this.qrcode = res.value;
            this.getMessagePath();
          })
          .catch(() => {
            this.qrcode = '';
            wx.showToast({
              title: '公众号二维码获取失败，请重试',
              icon: 'none',
            });
          });
      }
    },

    getMessagePath() {
      const h5Link = args.add('https://h5.youzan.com/wscump/weapp-follow', {
        kdtId: app.getKdtId(),
        qrCodeUrl: encodeURIComponent(this.qrcode || ''),
      });
      const urlParams = {
        yz_live_code_link: encodeURIComponent(h5Link),
        yz_live_code_image: encodeURIComponent(H5_LOGO),
        yz_live_code_desc: '关注公众号',
        yz_live_code_title: '关注公众号',
        // TODO - 暂时把直接回复公众号二维码的方式改成回复链接的
        // yz_live_code_component_type: 1,
        // yz_live_code_qrcode: encodeURIComponent(this.qrcode),
      };

      const messagePath = args.add(BASE_SHARE_PATH, urlParams);

      this.setYZData({
        messagePath,
        showPop: true,
        newRiseFollower: false,
      });
    },
  },
});
