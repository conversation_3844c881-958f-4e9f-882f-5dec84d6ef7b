import { getCurrentPage } from 'shared/common/base/wsc-component';
import navigate from 'shared/utils/navigate';

Component({
  externalClasses: ['custom-class'],

  ready() {
    getCurrentPage().getContactInfo
    && getCurrentPage(this).getContactInfo()
      .then(contactData => {
        console.log(contactData);
        this.setData(contactData);
      });
  },

  methods: {
    onContactBack: navigate.contactBack
  }
});
