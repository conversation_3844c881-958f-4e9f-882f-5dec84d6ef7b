import WscComponent from 'pages/common/wsc-component/index';
import navigateHelper from 'shared/utils/navigate';

WscComponent({
  properties: {
    styleSet: Number
  },

  data: {
    inited: false,
    authed: false
  },

  methods: {
    handleClick() {
      // 待微信好物圈功能完善后，才能上线，Allen 说暂时隐藏
      // const isEnteredShoppingList = app.storage.get('app:shopping-list:entered');
      const isEnteredShoppingList = true;
      navigateHelper.navigate({
        url: `/packages/common/shopping-list/${isEnteredShoppingList ? 'entry' : 'open'}/index`
      });
    }
  }
});
