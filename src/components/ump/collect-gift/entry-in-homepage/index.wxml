<block wx:if="{{ state.isShow && !isChannels }}">
  <view data-event="navigateToActivityHomePage" class="entry-container {{ goaway || pGoaway ? 'goaway' : '' }}" style="z-index:{{  zIndex }};{{(bottom || pBottom) ? 'bottom:' + (bottom || pBottom) : ''}}" bindtap="eventHandler">
    <van-icon name="close" data-event="closeContainer" class="entry-container_close-button" catchtap="eventHandler" />
  </view>
</block>