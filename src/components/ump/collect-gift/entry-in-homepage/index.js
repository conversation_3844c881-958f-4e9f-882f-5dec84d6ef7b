import WscComponent from 'pages/common/wsc-component/index';
import IconController from 'components/showcase/components/floating-nav/icon-controller/index';
import { constants } from '../utils/constants';
import isShowCollectGift from '../utils/behaviors/is-show-collect-gift';
import { checkIsChannels } from 'shared/utils/channel';
import { closeCollectGiftShow } from '../utils/api';

const app = getApp();

WscComponent({
  behaviors: [isShowCollectGift],
  data: {
    popupController: {
      isShow: false,
    },
    // 是否是视频号
    isChannels: checkIsChannels(),
  },

  properties: {
    customPosition: {
      type: Boolean,
      default: false,
    },
    pBottom: {
      type: String,
      default: '',
    },
    pGoaway: {
      type: Number,
      default: false,
    },
    zIndex: {
      type: Number,
      default: 140,
    },
  },

  attached() {
    // 视频号不展示悬浮窗
    if (checkIsChannels()) return;
    if (this.data.customPosition) return;

    this.iconController = new IconController().setIcon(this, {
      priority: 80,
      height: 74,
      cb: [
        (bottom) => {
          this.setYZData({ bottom: `${bottom}px` });
        },
        (goaway) => {
          this.setYZData({ goaway });
        },
      ],
    });
  },
  detached() {
    if (this.data.customPosition) return;

    this.iconController.setIcon(this, { height: 0 });
    this.iconController.destroy(this);
  },
  methods: {
    eventHandler({
      target: {
        dataset: { event },
      },
    }) {
      switch (event) {
        case 'closeContainer':
          closeCollectGiftShow().catch(() => {
            console.log('同步关闭状态到远程服务器失败');
          });
          return this.setYZData({ 'state.isShow': false });
        case 'navigateToActivityHomePage':
          app.logger.log({
            et: 'click',
            ei: 'click_collect_gift',
            en: '点击收藏有礼',
          });
          return wx.navigateTo({ url: constants.url.activityHomePage });
        default:
          console.error(new Error(`Unexpected event${event}`));
      }
    },
  },
});
