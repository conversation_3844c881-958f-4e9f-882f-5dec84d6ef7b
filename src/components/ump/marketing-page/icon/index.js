import WscComponent from 'pages/common/wsc-component/index';
import IconController from 'components/showcase/components/floating-nav/icon-controller/index';
import navigate from '@/helpers/navigate';
import { constants } from '../utils/constants';
import isShowMarketingPage from '../utils/behaviors/is-show-marketing-page';

WscComponent({
  behaviors: [isShowMarketingPage],
  data: {
    popupController: {
      isShow: false,
    },
    iconImgStyle: `background-image: url(https://img01.yzcdn.cn/upload_files/2021/10/19/Fod8WsdlHHOZgINop9qK4n0om66y.gif)`,
  },
  properties: {
    customPosition: {
      type: Boolean,
      default: false,
    },
    pBottom: {
      type: String,
      default: '',
    },
    pGoaway: {
      type: Number,
      default: false,
    },
  },
  attached() {
    if (this.data.customPosition) return;
    this.iconController = new IconController().setIcon(this, {
      priority: 60,
      height: 48,
      cb: [
        (bottom) => {
          this.setYZData({ bottom: `${bottom}px;` });
        },
        (goaway) => {
          this.setYZData({ goaway });
        },
      ],
    });
  },
  detached() {
    if (this.data.customPosition) return;
    this.iconController.setIcon(this, { height: 0 });
    this.iconController.destroy(this);
  },
  methods: {
    eventHandler({
      target: {
        dataset: { event },
      },
    }) {
      if (event === 'navigateToMarketingPage') {
        navigate.navigate({ url: constants.url.activityMarketingPage });
      }
    },
  },
});
