import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import spm from 'shared/utils/spm';
import { isShowMarketingPage } from '../api';

const app = getApp();

export default Behavior({
  data: {
    state: {
      isShow: false,
    },
  },
  attached() {
    this.setIsShow();
  },
  methods: {
    async setIsShow() {
      const isShowMarketingPage = await this.isShowMarketingPage();
      if (isShowMarketingPage) {
        this.setYZData({
          'state.isShow': true,
        });
        // 曝光埋点
        app.logger && app.logger.log({
          et: 'view', // 事件类型
          ei: 'component_view', // 事件标识
          en: '组件曝光', // 事件名称
          params: {
            component: 'side_ad',
            banner_id: this.getBannerId(),
          }, // 事件参数
        });
      }
    },
    async isShowMarketingPage() {
      try {
        const marketingPageConfig = await isShowMarketingPage();
        return marketingPageConfig.floating_nav_enable === '1';
      } catch (error) {
        return false;
      }
    },
    getBannerId() {
      return `${spm.getPageSpmTypeId()}~popup_ad.0~0~${makeRandomString(
        8
      )}`;
    },
  },
});
