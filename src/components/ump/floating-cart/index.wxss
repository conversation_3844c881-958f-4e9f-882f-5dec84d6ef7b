.theme-floating-nav {
  position: fixed;
  bottom: 80px;
  right: 10px;
  width: 48px;
  height: 48px;
  z-index: 10;
  transition: all 0.24s;
}

.is-iphone-x {
  bottom: 114px;
}

/*
==== 子窗口
*/
.item {
  position: absolute;
  top: 0;
  left: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,.16);
  transition: all 0.24s;
}

.item::after {
  display: none;
}

.main {
  z-index: 1;
}

/*
==== 角标
*/
.has-badge::before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 10px;
  right: 10px;
  width: 8px;
  height: 8px;
  border: 1px solid white;
  border-radius: 50%;
  background-color: #f44;
}
