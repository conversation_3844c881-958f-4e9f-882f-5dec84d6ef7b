
import navigate from '@/helpers/navigate';
import WscComponent from 'pages/common/wsc-component/index';
import getSystemInfo from 'shared/utils/browser/system-info';
import API from './api';

const { model } = getSystemInfo();
const isIphoneX = /iphone x/i.test(model);

WscComponent({
  properties: {
    iconUrl: {
      type: String,
      value: '//img.yzcdn.cn/weapp/floating-cart/cart-icon.png'
    },
  },

  data: {
    isIphoneX,
    hasBadge: false
  },

  attached() {
    API.getCartCount().then(count => {
      this.refreshCartIcon(+count > 0);
    });
    // 监听添加购物车
    this.on('component:sku:cart', this.handleAddCart);
  },

  detached() {
    this.off('component:sku:cart', this.handleAddCart);
  },

  methods: {
    // 跳转到 path
    handleNavTap() {
      navigate.switchTab({
        url: '/packages/goods/cart/index'
      });
    },

    // 刷新购物车
    refreshCartIcon(hasGoods) {
      this.setYZData({ hasBadge: hasGoods });
    },

    // 处理添加物品事件
    handleAddCart(cartEvent = {}) {
      if (cartEvent.type === 'add') {
        this.refreshCartIcon(true);
      }
    },
  },
});
