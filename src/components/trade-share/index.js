import logv3 from 'utils/log/logv3';
import { getPoster } from './api';
import authorize from '@/helpers/authorize';

const app = getApp();

Component({
  properties: {
    // 是否显示
    visable: {
      type: Boolean,
      value: false
    },
    // 订单id
    orderNo: {
      type: String,
      value: '',
      observer(val) {
        this.triggerEvent('updateShareParam', {
          shareUrl: `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
            `/packages/order/share-page/index?order_no=${val}&kdt_id=${app.getKdtId()}&is_share=1`
          )}` // 讲分享的url改成分享落地页的url
        });
      }
    },
    items: {
      type: Array,
      value: ['sendFriend', 'sharePoster'],
      observer(val) {
        this.setData({
          actions: val
        });
      }
    }
  },
  data: {
    showSheet: true,
    actions: [
      {
        key: 'sendFriend',
        name: '发送给朋友',
        openType: 'share'
      },
      {
        key: 'sharePoster',
        name: '生成海报'
      }
    ],
    showPoster: false, // 是否展示海报
    posterSrc: '' // 分享海报地址
  },
  methods: {
    // 关闭上拉菜单
    closeActionSheet() {
      this.triggerEvent('close');
    },
    // 点击菜单
    handleActionClick(event) {
      const key = event.detail.key;
      switch (key) {
        case 'sendFriend':
          this.closeActionSheet();
          break;
        case 'sharePoster':
          this.closeActionSheet();
          this.doGetPoster(true);
          break;
        default:
          break;
      }
    },
    // 关闭海报
    closePoster() {
      this.setData({ showPoster: false });
    },
    // 获取海报
    doGetPoster(needRetry = false, retry = 0) {
      if (this.data.posterSrc) {
        this.setData({ showPoster: true });
        return;
      }

      wx.showToast({
        icon: 'loading',
        title: '正在生成',
        // 持续时间最长为10s
        duration: 10000
      });

      const { isRetailApp } = app.globalData;
      const guestKdtId = app.getKdtId();
      const _kdtId = isRetailApp ? app.getHQKdtId() : app.getKdtId();
      const kdtId = (app.getAppKdtId && app.getAppKdtId()) || _kdtId;
      const path = '/packages/order/share-page/index';
      const dcPs = logv3.getDCPS();
      const offlineId = app.getOfflineId();

      const scene = {
        page: path,
        guestKdtId,
        kdtId,
        dcPs,
        offlineId,
        order_no: this.data.orderNo
      };

      getPoster({
        retry,
        kdtId: guestKdtId,
        order_no: this.data.orderNo,
        pageType: 'goods',
        page: 'pages/common/blank-page/index',
        scene
      })
        .then((res) => {
          wx.hideToast();
          this.setData({ showPoster: true, posterSrc: res.value });
        })
        .catch(({ msg } = {}) => {
          wx.hideToast();
          if (needRetry) {
            setTimeout(() => {
              this.doGetPoster(false, 1);
            }, 1000);
          } else {
            wx.showModal({
              title: msg || '未知异常',
              content: this.data.orderNo,
              showCancel: false
            });
          }
        });
    },
    // 图片保存到本地
    saveToLocal() {
      const { posterSrc } = this.data;
      if (!posterSrc) return;

      wx.showLoading({ title: '保存中' });
      authorize('scope.writePhotosAlbum')
        .then(() => {
          this.saveShareImage(posterSrc)
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000
              });
              this.closePoster();
            })
            .catch((err) => {
              console.log(err);
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000
              });
            });
        })
        .catch(() => {
          wx.hideLoading();
          wx.showToast({
            title: '请允许访问相册后重试',
            icon: 'none',
            duration: 1000
          });
          setTimeout(() => {
            wx.openSetting({
              success: ({ authSetting }) => {
                if (authSetting['scope.writePhotosAlbum']) {
                  this.saveToLocal();
                }
              }
            });
          }, 1000);
        });
    },
    // 图文卡片保存
    saveShareImage(tempFilePath) {
      return new Promise((resolve, reject) => {
        app.downloadFile({
          url: tempFilePath,
          success(res) {
            if (res.statusCode === 200) {
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: resolve,
                fail: reject
              });
            } else {
              reject();
            }
          },
          fail: reject
        });
      });
    }
  },
  lifetimes: {
    detached() {
      // 在组件实例被从页面节点树移除时执行
      this.triggerEvent('updateShareParam', {
        shareUrl: ''
      });
    }
  }
});
