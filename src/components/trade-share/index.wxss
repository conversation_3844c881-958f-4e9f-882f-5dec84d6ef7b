.trade-share__poster {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  display: flex;
  flex-flow: column center;
  justify-content: center;
  align-items: center;
}

.trade-share__poster_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,.7);
}

.trade-share__poster_img_container {
  position: relative;
}

.trade-share__poster_img {
  border-radius: 10rpx;
  width: 600rpx;
}

.trade-share__poster_close_container {
  position: absolute;
  right: -60rpx;
  top: -60rpx;
  height: 120rpx;
  width: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.trade-share__poster_img_close {
  height: 46rpx;
  width: 46rpx;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.trade-share__poster_img_close::before {
  transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
}

.trade-share__poster_img_close::after {
  transform: translateX(-50%) translateY(-50%) rotateZ(45deg);
}

.trade-share__poster_img_close::before, .trade-share__poster_img_close::after {
  content: '';
  width: 26rpx;
  height: 2rpx;
  background: #999;
  position: absolute;
  left: 50%;
  top: 50%;
}

.trade-share__poster_word {
  font-size: 28rpx;
  color: white;
  margin-top: 20rpx;
  text-align: center;
  position: relative;
}
