# web-view-navigator
支持跳转到特定域名 web-view 的全局组件。

## 参数
- url 需要跳转的 webview 路径。如果是包含域名的URL，域名将会被忽略。建议使用绝对路径的方式，如 `/path/to/web-view-url`
- type 跳转方式。仅支持 `navigate`, `redirect`，对应小程序的 `wx.navigateTo` 和 `wx.redirectTo`，默认 `navigate`。

## 事件
- fail 跳转失败
- success 跳转成功

## 示例
```html
<web-view-navigator
  url="/wscgoods/detail/3f2rt4nyy147m" 
  bind:fail="onFail"
  bind:success="onSuccess"
/>
```

## openWebview(url): Promise
