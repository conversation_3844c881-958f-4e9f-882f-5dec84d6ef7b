import navigate from '@/helpers/navigate';
import args from '@youzan/weapp-utils/lib/args';

const TYPE_SET = ['navigate', 'redirect'];
const YOUZANYUN_WEBVIEW = '/packages/zan-web-view/index';

Component({
  properties: {
    url: String,
    type: {
      type: String,
      value: 'navigate'
    }
  },

  externalClasses: ['custom-class'],

  attached() {
    if (!this.data.path) {
      console.warn('navigator 参数 path 必须');
    }

    if (TYPE_SET.indexOf(this.data.type) === -1) {
      console.log(`不支持的 type 属性 ${this.data.type}`);
    }
  },

  methods: {
    navigateTo() {
      const navigateFn = navigate[this.data.type];

      if (!navigateFn) {
        return this.triggerEvent('fail', { message: 'type 仅支持 navigate, redirect' });
      }
      navigateFn({
        url: args.add(YOUZANYUN_WEBVIEW, { url: encodeURIComponent(this.data.url) }),
        fail: (e) => {
          this.triggerEvent('fail', e);
        },
        success: (e) => {
          this.triggerEvent('success', e);
        }
      });
    }
  }
});
