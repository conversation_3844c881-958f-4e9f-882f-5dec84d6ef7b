import WscComponent from 'pages/common/wsc-component';
import { getCurrentPage } from 'shared/common/base/wsc-component';
import event from '@youzan/tee-event';

const LISTENER_QUEUE = [
  'pageScrollTo',
  'pageToggleRefresh',
  'pageToggleScroll',
];

// 现在都是事件驱动
// 不在混编层做的原因是，每个页面都得去定义基础api比如页面滚动，这个东西其实可以统一。
// 为什么不能用混编层props传入，本质上没什么必要多做一层通信给混编层。
// 这个组件容易后期不好维护，目前定义就是全局事件，页面属性的需要单独处理。

WscComponent({
  properties: {
    refresherEnabled: {
      type: Boolean,
      value: false,
    },
    scrollWithAnimation: {
      type: Boolean,
      value: false,
    },
    enhanced: {
      type: Boolean,
      value: false,
    },
    scollType: {
      type: String,
      value: 'list',
    },
  },
  data: {
    isSkyline: false,
    scrollTop: 0,
    refresherTriggered: false,
    scrollY: true,
  },
  created() {
    this.clearListen = this.initSkylineListen();
    this.setData({
      isSkyline: this.renderer === 'skyline',
    });
  },
  detached() {
    this.clearListen();
  },

  methods: {
    onScroll(e) {
      getCurrentPage().onPageScroll({ scrollTop: e.detail.scrollTop });
    },
    onRefresher() {
      this.pageToggleRefresh(true);
    },
    initSkylineListen() {
      const fns = LISTENER_QUEUE.reduce((fnMap, name) => {
        fnMap[name] = this[name].bind(this);
        return fnMap;
      }, {});
      LISTENER_QUEUE.forEach((name) => event.on(name, fns[name]));
      return () => LISTENER_QUEUE.forEach((name) => event.off(name, fns[name]));
    },
    pageScrollTo({ scrollTop, callback }) {
      // 至少要有1，不然负值会触发下拉刷新。
      const top = Math.max(scrollTop, 1);
      // 两次设置top不能相同所以设置每次需要先设置 -1
      const queue = [top - 1, top];
      queue.forEach((top) => {
        Promise.resolve().then(() => {
          this.setData({
            scrollTop: top,
          });
        });
      });
      callback && Promise.resolve().then(() => callback());
    },
    pageToggleRefresh(state = false) {
      this.setData({
        refresherTriggered: state,
      });
      if (state) {
        getCurrentPage().onPullDownRefresh();
      }
    },
    pageToggleScroll(scrollY = false) {
      this.setData({
        scrollY,
      });
    },
  },
});
