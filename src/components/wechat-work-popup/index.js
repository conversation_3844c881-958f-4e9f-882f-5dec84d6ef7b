import navigate from 'shared/utils/navigate';
import WscComponent from 'pages/common/wsc-component';
import args from '@youzan/weapp-utils/lib/args';
import getApp from 'shared/utils/get-safe-app';
import { followReport } from './api';

const app = getApp();

const H5_LOGO = 'https://b.yzcdn.cn/public_files/63dbb601df217cf035e62efe2bda73db.png';
const BASE_SHARE_PATH = '/pages/home/<USER>/index';
const msgImg = 'https://b.yzcdn.cn/public_files/1b92b6a9af6bf5f94b2b83a6dd17f71b.png';

WscComponent({
  properties: {
    title: {
      type: String,
      value: '添加好友立享福利价',
    },
    subTitle: {
      type: String,
      value: '进入客服消息页面后，点击右下角快捷图片，即可获取添加好友二维码。',
    },
    btnText: {
      type: String,
      value: '去添加',
    },
    contactInfo: {
      type: Object,
      value: {},
    },
    goodAlias: {
      type: String,
      value: '',
    },
    zIndex: {
      type: Number,
      value: 100,
    },
  },

  data: {
    showPop: false,
    messagePath: '',
    messageImg: msgImg,
  },

  methods: {
    onContactBack: navigate.contactBack,

    handleShowPopup() {
      this.setYZData({
        showPop: true,
      });

      this.getMessagePath();

      this.triggerEvent('show');
    },

    togglePopup() {
      this.setYZData({
        showPop: false,
      });

      this.triggerEvent('close');
    },

    getMessagePath() {
      const { contactInfo } = this.data;
      const h5Link = args.add(
        'https://qiwei.youzan.com/wa/wecom-app/marketing/staff-qrcode',
        {
          kdtId: app.getKdtId(),
          qrCodeUrl: encodeURIComponent(contactInfo.qrCodeUrl || ''),
        }
      );
      const urlParams = {
        yz_live_code_link: encodeURIComponent(h5Link),
        yz_live_code_image: encodeURIComponent(H5_LOGO),
        yz_live_code_title: '点击加好友',
        yz_live_code_desc: '点击识别二维码加客服企业微信专属福利享不停',
      };

      const messagePath = args.add(BASE_SHARE_PATH, urlParams);

      this.setYZData({
        messagePath,
      });
    },

    onClick() {
      this.handleReport();
    },

    handleReport() {
      const { goodAlias } = this.data;
      followReport({
        alias: goodAlias,
        kdtId: app.getKdtId(),
      });
    },
  },
});
