<view class="custom-class">
  <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="handleShowPopup">
    <slot></slot>
  </user-authorize>
</view>

<van-popup
  custom-class="follow-popup"
  show="{{ showPop }}"
  z-index="{{ zIndex }}"
  close-on-click-overlay
>
  <view class="follow-popup__content">
    <view class="title">{{ title }}</view>
    <view class="sub-title">{{ subTitle }}</view>
    <image class="activity-img" src="https://b.yzcdn.cn/public_files/5ba5cd58c04d496449cd1c1378bfc9fe.png" alt="活动简略图"></image>
    <button
      class="follow-button"
      open-type="contact"
      send-message-title=" "
      show-message-card="{{ true }}"
      send-message-path="{{ messagePath }}"
      send-message-img="{{ messageImg }}"
      bind:contact="onContactBack"
      bind:tap="onClick"
    >
      {{ btnText }}
    </button>
  </view>
  <view
    bindtap="togglePopup"
    class="follow-popup__close"
  >
    <image src="https://img.yzcdn.cn/public_files/2019/07/09/0fdc360127f973b6eef88ea3d005d52f.png" />
  </view>
</van-popup>
