import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

const DEFAULT_VIDEO_TIME = 3;
const app = getApp();
const { isRetailApp = false } = app.globalData || {};
Component({
  properties: {
    pageHide: {
      type: Boolean,
      default: false,
    },
    isEnterShop: {
      type: Boolean,
      default: false,
    },
  },
  observers: {
    pageHide(val) {
      if (val && this.data.timeNum > 0) {
        app.logger &&
          app.logger.log({
            et: 'view', // 事件类型
            ei: 'cover_ad_hide_weapp', // 事件标识
            en: '启动页-展示时关闭小程序', // 事件名称
            pt: 'retailhome', // 页面类型
          });
      }
    },
    'isEnterShop, timeNum': function (isEnterShopNew, timeNumNew) {
      if (isEnterShopNew && timeNumNew === 0) {
        this.triggerEvent('coverAdHide');
        this.handEnded();
      }
    },
  },
  data: {
    videoMute: true,
    videoSrc: '',
    timeNum: DEFAULT_VIDEO_TIME,
    videoSource: null,
    videoChannelId: '',
    videoId: '',
    imageUrl: '',
    linkType: 0,
    linkConfig: {},
  },

  ready() {
    const { videoAdPromise } = app.globalData;
    if (videoAdPromise) {
      videoAdPromise.then((res) => {
        this.triggerEvent('coverAdShow');
        const {
          playUrl,
          maxWaitTime = DEFAULT_VIDEO_TIME,
          videoSource,
          videoChannelId,
          videoId,
          imageUrl,
          linkType,
          linkConfig,
          videoMuted = true,
        } = res || {};

        this.setData({
          videoSrc: playUrl,
          timeNum: maxWaitTime,
          videoSource,
          videoChannelId,
          videoId,
          imageUrl: this.formatImg(imageUrl),
          linkType,
          linkConfig,
          videoMute: videoMuted,
        });

        // 删除videoAdPromise，防止每次进入首页都触发
        app.globalData.videoAdPromise = null;
        this.handleLogger('view', 'home_ad_show', '启动页-展示', {
          video_source: videoSource,
        });
      });
      setTimeout(() => {
        const timer = setInterval(() => {
          this.setData({
            timeNum: this.data.timeNum - 1,
          });
          if (this.data.timeNum === 0) {
            clearInterval(timer);
            this.handEnded();
          }
        }, 1000);
      }, 1000);
    } else {
      this.triggerEvent('coverAdShow');
      this.triggerEvent('coverAdHide');
    }
  },
  methods: {
    formatImg(url) {
      if (url) {
        const name = url.split('?')[0];
        const postfix = name.split('.').reverse()[0];
        // https://img01.yzcdn.cn/upload_files/2022/10/18/FkTCtuI6mtjmvNk57P1CcrOdb8zO.gif?imageView2/2/w/375/h/0/q/75/format/gif
        return cdnImage(
          name,
          `${isRetailApp ? '!1125x0.' : '!750x0.'}` + postfix
        );
      }
    },
    onMutedClick() {
      this.setData({
        videoMute: !this.data.videoMute,
      });
    },
    handEnded() {
      // 这里兼容一下零售连锁商家，如果没进店就一直展示开屏广告代替白屏
      if (isRetailApp && !this.data.isEnterShop) {
        return;
      }
      const animation = wx
        .createAnimation({
          duration: 150,
          timingFunction: 'linear',
        })
        .opacity(0)
        .step();
      this.setData({
        animationData: animation.export(),
      });
      this.triggerEvent('coverAdHide');
      // 兜底清除
      setTimeout(() => {
        this.setData({
          videoSrc: '',
        });
      }, 200);
    },
    animationend() {
      this.setData({
        videoSource: null,
        videoSrc: '',
      });
    },
    /**
     * 点击图片
     */
    handleImageClick() {
      this.handleLogger('click', 'home_ad_click_img', '点击图片');
    },
    handleSkipClick() {
      this.handleLogger('click', 'home_ad_click_skip', '开屏广告点击跳过', {
        video_source: this.data.videoSource,
      });
      this.handEnded();
    },
    handleLogger(et, ei, en, extraParams = {}) {
      app.logger &&
        app.logger.log({
          et, // 事件类型
          ei, // 事件标识
          en, // 事件名称
          params: {
            component: 'home_video_ad',
            ...extraParams,
          }, // 事件参数
        });
    },
  },
});
