<view
	bind:transitionend="animationend"
	animation="{{ animationData }}"
	class="homepage-ad-video"
	wx:if="{{ videoSource === 0 || videoSource === 1 || videoSource === 2 }}"
>
	<video
		wx:if="{{ videoSource === 0 }}"
		id="homepageVideo"
		object-fit="cover"
		autoplay="{{ true }}"
		show-mute-btn
		muted="{{ videoMute }}"
		controls="{{ false }}"
		show-bottom-progress="{{ false }}"
		bindended="handEnded"
		src="{{ videoSrc }}"
	></video>
	<channel-video
		wx:elif="{{ videoSource === 1 && videoChannelId && videoId }}"
		binderror="handEnded"
		autoplay
		object-fit="contain"
		loop="{{ true }}"
		finder-user-name="{{ videoChannelId }}"
		feed-id="{{ videoId }}"
		class="channel-video"
	></channel-video>

	<image wx:elif="{{ videoSource === 2 && imageUrl }}" class="home-img" mode="aspectFill" src="{{ imageUrl }}" bindtap="handleImageClick" />

	<video-ad-link link-config="{{ linkConfig }}" link-type="{{ linkType }}" bind:video-ended="handEnded"></video-ad-link>

	<view class="control__block">
		<view bindtap="onMutedClick" wx:if="{{ videoSource === 0 }}" class="home-video-ad__voice">
			<image
				src="{{ videoMute ? 'https://img01.yzcdn.cn/upload_files/2022/10/13/Fnw_HxN0eXXCGciI1Oulbwk68aZz.png' : 'https://img01.yzcdn.cn/upload_files/2022/10/13/FkrnH6xtEu4nDpY2KL7Ih4Rbuj9o.png' }}"
			></image>
		</view>
		<view bind:tap="handleSkipClick" i18n-off class="home-video-ad__jump">{{timeNum}}s 跳过</view>
	</view>
</view>