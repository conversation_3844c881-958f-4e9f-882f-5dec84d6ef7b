<van-popup
  custom-class='subscribe-fail-popup'
  round
  position='bottom'
  show='{{ show }}'
  close-on-click-overlay
  bind:close='onClose'
>
  <view class='subscribe-fail-popup__content'>
    <view class='header'>
      <view class='title'>订阅失败</view>
      <view class='sub-title'>重新获取订阅方法</view>
    </view>
    <view class='step-line'>
      1. 点击小程序右上角
      <wsc-icon
        name='icon-collect-gift-share-icon'
        custom-class='desc__icon'
      ></wsc-icon>
    </view>
    <view class='step-line'>
      2. 点击“设置”
      <image
        class='desc__img'
        mode='aspectFit'
        src='https://img.yzcdn.cn/public_files/2019/12/19/f046dfdc7c2f0cdd9290d29266edd838.png'
      />
    </view>
    <view class='step-line'>
      3. 选择订阅消息，并打开“接受订阅消息”开关
      <image
        class='desc__img'
        mode='aspectFit'
        src='{{ openImageSrc }}'
      />
    </view>
    <van-button
      custom-class='confirm-button'
      bind:tap='onClose'
    >我知道了</van-button>
  </view>
</van-popup>
