import navigate from '@/helpers/navigate';
import args from '@youzan/weapp-utils/lib/args';

const TYPE_SET = ['navigate', 'redirect', 'switchTab'];

Component({
  properties: {
    path: String,
    query: Object,
    /**
     * 支持 open - type 支持
     * navigate, redirect, switchTab
     */
    type: {
      type: String,
      value: 'navigate'
    },
    /**
     * 跳转过程中是否收集formid
     */
    collectFormId: {
      type: Boolean,
      value: false
    },
    /**
     * formid bussiness module
     */
    formIdBusinessModule: {
      type: String,
      value: ''
    }
  },

  data: {},

  attached() {
    if (!this.data.path) {
      console.warn('navigator 参数 path 必须');
    }

    if (TYPE_SET.indexOf(this.data.type) === -1) {
      console.log(`不支持的 type 属性 ${this.data.type}`);
    }
  },

  methods: {
    navigateTo() {
      navigate[this.data.type] && navigate[this.data.type]({ url: args.add(this.data.path, this.data.query || {}) });
    }
  }
});
