<view wx:if="{{ show }}" class="loading" style="height:{{windowHeight}}px;z-index:{{zIndex}}">
  <image class="logo" wx:if="{{ globalCustomLoading }}" src="{{ globalCustomLoading }}" mode="aspectFit" style="{{ 'top:' + topOffset }}" />
  <view class="circle" wx:else>
    <view class="circle-c">
      <view class="circle-l circle-it"></view>
      <view class="circle-r circle-it"></view>
    </view>
    <image class="logo-c" wx:if="{{ appLogo }}" src="{{ appLogo }}" mode="aspectFill" />
  </view>
</view>