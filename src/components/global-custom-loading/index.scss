.loading {
  background: #fff;
  width: 100%;
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  top: 0;
}
.logo {
  width: 240rpx;
  height: 240rpx;
  position: relative;
}
.circle {
  position: absolute;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  flex-direction: column;
}
.circle-c {
  background-image: linear-gradient(#ebedf0, #969799);
  position: relative;
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}
.circle-it {
  background: #ebedf0;
  width: 112rpx;
  height: 112rpx;
  position: absolute;
  right: 0;
  top: 0;
  border-radius: 50%;
}
.circle-l {
  clip: rect(0, 56rpx, auto, 0);
}
.circle-r {
  clip: rect(0, auto, auto, 56rpx);
  transform: rotate(118.8deg);
}
.logo-c {
  border-radius: 50%;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background: #f2f4f6;
  width: 100rpx;
  height: 100rpx;
  top: 6rpx;
  border: 2rpx solid #fff;
  box-sizing: border-box;
}