import WscComponent from 'pages/common/wsc-component';

const app = getApp();

const CHANGE_EVENT = 'app:loading:change';

WscComponent({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    topOffset: {
      type: String,
      value: '0',
    },
    top: Boolean, // 是否在最上层
    zIndex: {
      type: Number,
      value: 9999,
    },
  },

  data: {
    globalCustomLoading: '',
    appLogo: __wxConfig?.accountInfo?.icon,
    windowHeight: app.getSystemInfoSync().windowHeight,
  },
  created() {
    app.on(CHANGE_EVENT, this.setCustomLoading);
  },
  detached() {
    app.off(CHANGE_EVENT, this.setCustomLoading);
  },
  attached() {
    this.setCustomLoading();
  },
  methods: {
    setCustomLoading() {
      this.setData({
        globalCustomLoading: app.globalData.globalCustomLoading,
      });
    },
  },
});
