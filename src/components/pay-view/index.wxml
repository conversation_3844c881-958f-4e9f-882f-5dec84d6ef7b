<cap-pay
  custom-class="order-pay-class"
  pay-ways="{{ payWays }}"
  show-pay-ways="{{ showPayWays }}"
  show-password="{{ showPassword }}"
  secure-account="{{ secureAccount }}"
  loading-pay-way="{{ loadingPayWay }}"
  show-adjust-price="{{ showAdjustPrice }}"
  new-price="{{ newPrice }}"
  is-paying="{{ isPaying }}"
  bind:close="doClose"
  bind:pay-way-selected="onPayWaySelected"
  bind:close-password="onClosePassword"
  bind:cancel-adjust-price="cancelAdjustPrice"
></cap-pay>