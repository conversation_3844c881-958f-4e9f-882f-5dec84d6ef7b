import WscComponent from 'pages/common/wsc-component/index';
import PayManager from 'components/pay-manager/index';

const app = getApp();

WscComponent({
  externalClasses: ['order-pay-class'],

  properties: {
    prepay: Boolean,
    prepaySuccess: Boolean,
    prepayParams: {
      type: Object,
      value: {}
    },
    zeroOrder: Boolean,
    orderNo: {
      type: String,
      observer: 'updateOrderNo'
    }
  },

  data: {
    showPayWays: false,
    payWays: [],
    cashierManager: false,
    isFetchedPayWays: false,
    showPassword: false,
    secureAccount: '',
    loadingPayWay: {},
    isPaying: false,
    showAdjustPrice: false,
    newPrice: -1,
    oldOrderNo: ''
  },

  ready() {
    const mobile = app.getMobile();
    this.setYZData({
      secureAccount:
        mobile && mobile.length === 11 ? `${mobile.substr(0, 3)}****${mobile.substr(7)}` : mobile
    });
  },

  methods: {
    initPayManager() {
      if (this.data.cashierManager) {
        return;
      }

      this.data.cashierManager = new PayManager({
        cashierRequestUrl: 'wsctrade/pay/wscweapp/payChannels.json',
        payRequestUrl: 'wsctrade/pay/wscweapp/pay.json',
        fail: this.onPayFail()
      });
    },

    startPay() {
      const { zeroOrder, prepay, prepaySuccess, prepayParams } = this.data;

      // 预下单失败
      if (prepay && !prepaySuccess) {
        wx.showToast({
          title: '支付失败，请稍后再试',
          icon: 'none',
          duration: 2000
        });
        this.triggerEvent('pay-fail', 'prepayError');
        return;
      }

      // 0元订单特殊处理，直接成功
      if (zeroOrder) {
        this.onPaidSuccess();
        this.isPaying = false;
        return;
      }

      if (!prepay) {
        wx.showToast({
          title: '预下单失败，请稍后再试',
          icon: 'none',
          duration: 2000
        });
        this.triggerEvent('pay-fail', 'prepayError');
        return;
      }

      // 支付参数
      this.initPayManager();

      if (this.data.isFetchedPayWays) {
        // 已获取支付方式
        this.setYZData({ showPayWays: true });
        this.triggerEvent('get-pay-ways', {});
      } else {
        let cashierParams = {
          partnerId: prepayParams.partnerId,
          prepayId: prepayParams.prepayId,
          cashierSign: prepayParams.cashierSign,
          cashierSalt: prepayParams.cashierSalt
        };
        if (prepayParams.scene) {
          cashierParams = Object.assign({}, cashierParams, { scene: prepayParams.scene });
        }
        // 获取支付方式
        this.data.cashierManager.getPayWays(cashierParams, payWays => {
          // console.log(payWays);
          this.setYZData({
            isFetchedPayWays: true,
            payWays,
            showPayWays: true
          });
          this.triggerEvent('get-pay-ways', {});
        });
      }
    },

    onPayWaySelected({ detail: { payWay = {}, newPrice = -1, acceptPrice = 0, password = '' } }) {
      this.setYZData(
        { loadingPayWay: { ...payWay, password }, isPaying: true },
        { immediate: true }
      );
      const tokenData = app.getToken() || {};

      // 日志埋点
      app.logger
        && app.logger.log({
          et: 'click',
          ei: 'pay_item',
          en: '选择支付方式',
          params: {
            pay_channel: payWay.payChannel,
            pay_channel_name: payWay.payChannelName
          },
          si: app.getKdtId()
        });

      const { prepayParams } = this.data;
      // 在小程序进行支付时，需要额外传入参数 appId（有赞小程序需要）
      let outBizCtx;
      try {
        const outBizCtxData = JSON.parse(prepayParams.bizExt);
        outBizCtxData.appId = app.getAppId();
        outBizCtx = JSON.stringify(outBizCtxData);
      } catch (e) {
        outBizCtx = prepayParams.bizExt;
      }

      const payParams = {
        password,
        payTool: payWay.payChannel,
        payChannelName: payWay.payChannelName,
        wxSubOpenId: tokenData.openId,
        valueCardNo: payWay.valueCardNo || '',
        acceptPrice,
        newPrice,
        outBizCtx,
        nodeExtra: {
          availableStatus: payWay.availableStatus || '',
          orderNo: this.data.orderNo,
          kdtId: app.getKdtId()
        }
      };

      this.data.cashierManager.doPayAction(payParams, () => {
        this.setYZData({ loadingPayWay: {}, isPaying: false });
        // 有些支付可以直接跳转成功也
        if (['CREDIT_CARD', 'BANK_CARD', 'INSTALMENT'].indexOf(payWay.payChannel) < 0) {
          // WX_APPLET, ECARD
          this.trigger('trade:order:paid', this.data.orderNo);
          this.onPaidSuccess();
        }
      });
    },

    onPayFail() {
      const _this = this;
      return function (err) {
        _this.setYZData({ isPaying: false });
        let errMsg = err.message || err.msg || '支付失败，请稍后再试';
        _this.triggerEvent('pay-fail', errMsg);
        switch (err.name) {
          case 'cancel':
            // 取消微信支付或货到付款
            _this.setYZData({ loadingPayWay: {} });
            break;
          case 'WxPayError':
            _this.setYZData({ loadingPayWay: {} });
            // 微信支付错误日志埋点
            app.logger.appError({
              name: 'wx_pay_error',
              message: errMsg,
              detail: Object.assign({}, err.errorContent, __wxConfig && __wxConfig.appLaunchInfo)
            });
            if (errMsg && errMsg.indexOf('fail jsapi has no permission') > 0) {
              errMsg = '受微信政策限制，请前往店铺公众号或H5进行购买';
            }
            wx.showToast({
              title: errMsg,
              icon: 'none',
              duration: 2000
            });
            break;
          // 订单改价
          case 'adjust_price':
            _this.setYZData({
              showAdjustPrice: true,
              newPrice: err.errorContent.newPrice
            });
            break;
          default:
            switch (err.code) {
              // 需要密码
              case 117700511:
                _this.setYZData({ showPassword: true });
                break;
              // 密码错误
              case 117701503:
                _this.setYZData({ showPassword: true });
                wx.showToast({
                  title: err.message || err.msg || '密码错误',
                  icon: 'none',
                  duration: 2000
                });
                break;
              default:
                if (!_this.data.showPassword) {
                  _this.setYZData({ loadingPayWay: {} });
                }
                wx.showToast({
                  title: err.message || err.msg || '支付失败，请稍后再试',
                  icon: 'none',
                  duration: 2000
                });
                break;
            }
        }
      };
    },

    onPaidSuccess() {
      this.triggerEvent('paid-success');
    },

    onClosePassword() {
      this.setYZData({ loadingPayWay: {}, showPassword: false });
    },

    cancelAdjustPrice() {
      this.setYZData({ showAdjustPrice: false, loadingPayWay: {} });
    },

    doClose() {
      this.setYZData({ showPayWays: false });
      this.triggerEvent('closePayPays');
    },

    updateOrderNo(val) {
      if (this.data.oldOrderNo !== val) {
        this.setYZData({ oldOrderNo: val, isFetchedPayWays: false });
      }
    }
  }
});
