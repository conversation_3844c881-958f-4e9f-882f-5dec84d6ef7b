/**
 * 会员等级升级提示 Dialog
 *
 * props:
 *    eventKey: 事件 key，事件使用 shared/utils/async-event 异步事件 默认值： ''
 *    title: 升级文案，支持 ${level} 表示当前 LvX 仅支持一个 默认值：任务完成，会员升级至${level}
 *    confirmText: 确认文案 默认值： 立即查看
 *    autoFetch: 是否组件 attached 后自动请求是否升级 默认值： false
 *    redirect: 确认事件是否自动 redirect 到等级列表 默认值： true
 *    kdtId: redirectKdtId 如果不传递是，默认使用 app.getKdtId() 的值
 * event:
 *    confirm：点击确认事件 参数: { level: 等级信息 }
 *    close：点击关闭事件 参数: { level: 等级信息 }
 *
 * 使用 Demo:
 *  index.json
 *    ...
 *    "levelup-tip": "components/levelup-tip/index"
 *    ...
 *
 *  index.wxml
 *    <levelup-tip event-key="confirm-mission" bind:confirm="handleConfirm" bind:cancel="handleCancel" bind:close="handleClose" />
 *
 *  index.js
 *  触发
 *  AsyncEvent.triggerAsync('confirm-mission').then((res) => {
 *    // res 可能有多个值
 *    if (res && res[0]) {
 *      console.log(res[0].show, res[0].level);
 *    }
 *  })
 */

import WscComponent from 'pages/common/wsc-component';
import AsyncEvent from 'shared/utils/async-event';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import getApp from 'shared/utils/get-safe-app';
import * as API from './api';

WscComponent({
  properties: {
    eventKey: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      // eslint-disable-next-line
      value: '任务完成，会员升级至${level}',
      observer(newValue) {
        this._setTitle(newValue);
      }
    },
    confirmText: {
      type: String,
      value: '立即查看'
    },
    autoFetch: {
      type: Boolean,
      value: false
    },
    kdtId: {
      type: Number
    },
    redirect: {
      type: Boolean,
      value: true
    }
  },

  data: {
    show: false, // Dialog visible
    level: null, // current level

    _allBenefits: [], // 当前 level 所有权益
    _hasBenfits: false, // 当前 level 是否有权益
    _formatedBenefits: [], // 用于展示的权益列表

    // 标题相关字段
    _titleBefore: '',
    _titleLevel: '',
    _titleAfter: ''
  },

  lifetimes: {
    attached() {
      this.subscribeEvent();
      this._setTitle(this.properties.title);

      if (this.properties.autoFetch) {
        this.fetchLevelAlterInfo();
      }
    },

    detached() {
      this.unsubscribeEvent();
    }
  },

  methods: {
    // #region 事件监听相关内容
    /**
     * 添加 Event 监听，用于后续触发
     */
    subscribeEvent() {
      const eventKey = this.properties.eventKey;
      if (eventKey) {
        AsyncEvent.onAsync(eventKey, this.fetchLevelAlterInfo, this);
      }
    },

    /**
     * 取消 Event 监听
     */
    unsubscribeEvent() {
      const eventKey = this.properties.eventKey;
      if (eventKey) {
        AsyncEvent.offAsync(eventKey, this.fetchLevelAlterInfo, this);
      }
    },
    // #endregion

    /**
     * 获取等级变更信息
     */
    fetchLevelAlterInfo() {
      // 当前已经展示弹窗，不继续请求
      if (this.data.show) {
        return;
      }

      return new Promise((resolve) => {
        API.fetchMark(2)
          .then(({ status, detail }) => {
            const show = status === 1;
            resolve({
              show,
              level: detail
            });
            this._formatBenefits(detail);
            this.setYZData({ show, level: detail });
            if (show) {
              API.removeMark();
            }

            this._setTitle(this.properties.title);
          })
          .catch(() => {
            resolve({
              show: false,
              level: null
            });

            this._resetData();
          });
      });
    },

    /**
     * 格式化等级权益展示相关信息
     * @param {Object} levelDetail 等级详情
     */
    _formatBenefits(levelDetail) {
      let allBenefits = [];
      let formatedBenefits = [];

      // 处理 权益顺序 + 自定义权益
      if (!levelDetail || !levelDetail.levelBenefit) {
        allBenefits = [];
      } else {
        const benefits = levelDetail.levelBenefit || {};
        allBenefits = [
          'coupon',
          'points',
          'present',
          'discount',
          'memberPrice',
          'postage',
          'pointsFeedBack',
          'diyTemplateList'
        ].reduce((res, benefitKey) => {
          const curBenefit = benefits[benefitKey] || [];
          res = res.concat(curBenefit);
          return res;
        }, []);
      }

      // 格式化展示权益
      let benefits = [...allBenefits];
      if (benefits.length) {
        if (benefits.length > 4) {
          benefits = benefits.slice(0, 3);
          benefits.push({
            benefitPluginInfo: {
              showName: '更多权益',
              icon: 'https://b.yzcdn.cn/public_files/a1fb1079aa056c48631a190bc5247dd9.png'
            }
          });
        }
        benefits = benefits.slice(0, 4);
      }
      formatedBenefits = benefits.map((benefit) => {
        const pluginInfo = benefit.benefitPluginInfo || {};
        return {
          key: benefit.benefitTplId,
          showName: pluginInfo.showName,
          icon: cdnImage(pluginInfo.icon),
          count: (benefit.couponList || []).reduce((res, coupon) => res + coupon.number || 0, 0)
        };
      });

      this.setYZData({
        _allBenefits: allBenefits,
        _formatedBenefits: formatedBenefits,
        _hasBenefits: !!allBenefits.length
      });
    },

    /**
     * 解析 title 属性用于展示
     * @param { String } title 标题
     */
    _setTitle(title) {
      const reg = /^(.*?)(\$\{level\})(.*?)$/;
      const matchRes = (title || '').match(reg);
      const levelValue = (this.data.level || {}).levelValue;
      if (matchRes) {
        this.setYZData({
          _titleBefore: matchRes[1] || '',
          _titleLevel: levelValue ? `Lv${levelValue}` : '',
          _titleAfter: matchRes[3] || ''
        });
      } else {
        this.setYZData({
          _titleBefore: title,
          _titleLevel: '',
          _titleAfter: ''
        });
      }
    },

    // #region 事件
    /**
     * 确认事件
     */
    handleConfirm() {
      API.removeMark();
      this.triggerEvent('confirm', { level: this.data.level });
      this.triggerEvent('close', { level: this.data.level });
      this._resetData();

      if (this.properties.redirect) {
        const kdtId = this.properties.kdtId || getApp().getKdtId();
        wx.redirectTo({
          url: `/packages/levelcenter/free/index?kdt_id=${kdtId}`
        });
      }
    },

    /**
     * cancel 事件
     */
    handleClose() {
      API.removeMark();
      this.triggerEvent('close', { level: this.data.level });
      this._resetData();
    },

    /**
     * 重置 Data
     */
    _resetData() {
      this.setYZData({
        show: false,
        level: null,

        _allBenefits: [],
        _hasBenfits: false,
        _formatedBenefits: []
      });
    }
    // #endregion
  }
});
