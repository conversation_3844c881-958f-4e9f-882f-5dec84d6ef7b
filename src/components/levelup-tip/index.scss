.main-content {
  margin-top: 56px;
  padding: 22px 22px 32px;
  border-radius: 20px;
  background: #fff;
}

.levelup-img {
  width: 222px;
  height: 150px;
  background: url(//b.yzcdn.cn/public_files/89e6a53f784f293b23d379a9b915703d.png) no-repeat
    center/cover;
  margin: 0 auto 16px;
}

.levelup-title {
  text-align: center;
  font-size: 18px;
  color: #323233;
  font-weight: bold;

  .levelup-title__before,
  .levelup-title__level,
  .levelup-title__after {
    display: inline-block;
    vertical-align: middle;
  }

  .levelup-title__level {
    color: #ed6a0c;
    margin-left: 0.5em;
  }
}

.benefit-count-tip {
  margin: 8px 0 28px;
  color: #7d7e80;
  text-align: center;
  font-size: 14px;
  line-height: 20px;
}

.all-benefit-count {
  color: #ed6a0c;
  margin-right: 0.2em;
  font-weight: bold;
}

.benefit-items {
  display: flex;
  align-items: center;
  justify-content: center;
}

.benefit-item {
  flex: 1;
  font-size: 12px;
  width: 66px;
  max-width: 25%;
  text-align: center;
  position: relative;
  color: #7d7e80;
}

.benefit-icon {
  width: 40px;
  height: 40px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin: 0 auto 6px;
}

.benefit-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  display: inline-block;
}

.benefit-count {
  position: absolute;
  right: 0;
  top: -6px;
  background-image: linear-gradient(90deg, #ff6034 0%, #ee0a24 100%);
  border-radius: 999px;
  font-size: 12px;
  color: #fff;
  text-align: center;
  padding: 2px 0.5em;
  transform: scale(0.8);
}

.benefit-tip {
  margin: 16px auto 0;
  text-align: center;
  line-height: 1.5;
  color: #88898b;
  font-size: 12px;
}

.confirm-btn {
  margin: 24px auto;
  color: #724804;
  background-image: linear-gradient(90deg, #e2bb7c 0%, #e8c388 100%);
  border-radius: 22px;
  opacity: 0.94;
  line-height: 40px;
  outline: none;
  border: none;
  width: 100%;
  font-size: 16px;
  font-weight: bold;
}

.close-icon-wrap {
  text-align: center;
  font-size: 32px;
}
