<van-dialog
  id="van-dialog"
  use-slot
  show="{{ show }}"
  wx:if="{{ show }}"
  show-confirm-button="{{ false }}"
  custom-style="background: transparent"
>
  <view class="main-content">
    <view class="levelup-img" />
    <view class="levelup-title">
      <view class="levelup-title__before">{{ _titleBefore }}</view>
      <view class="levelup-title__level">{{ _titleLevel }}</view>
      <view class="levelup-title__after">{{ _titleAfter }}</view>
    </view>
    <view wx:if="{{ _hasBenefits }}" class="benefit-count-tip">
      <text class="all-benefit-count">{{ _allBenefits.length }}项</text>
      会员专项权益已激活
    </view>
    <view wx:if="{{ _formatedBenefits && _formatedBenefits.length }}" class="benefit-items">
      <view class="benefit-item" wx:for="{{ _formatedBenefits }}">
        <view wx:if="{{ item.count }}" class="benefit-count">{{ item.count }}张</view>
        <view class="benefit-icon" style="background-image: url({{ item.icon }})" />
        <view class="benefit-name">{{ item.showName }}</view>
      </view>
    </view>
    <view wx:if="{{ _hasBenefits }}" class="benefit-tip">
      礼包仅首次升级时发放
    </view>
  </view>

  <button class="confirm-btn" bindtap="handleConfirm">{{ confirmText }}</button>

  <view class="close-icon-wrap">
    <van-icon name="close" color="#c8c9cc" bindtap="handleClose" />
  </view>
</van-dialog>
