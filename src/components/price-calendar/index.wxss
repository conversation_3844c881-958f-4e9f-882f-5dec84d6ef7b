@import './style.scss';

/* 头部样式 开始 */
.m-calendar-header {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
}
.m-calendar-select {
  position: relative;
  text-align: center;
  width: 70px;
  border-bottom: 1px solid transparent;
}
.u-month-show{
  font-size: 14px;
  color: #333;
  line-height: 20px;
}
.u-price-show{
  font-size: 10px;
  color: #f44;
  height: 14px;
  line-height: 14px;
  margin-bottom: 8px;
}
.u-icon {
  position: absolute;
  display: inline-block;
  width: 14px;
  height: 14px;
  background-size: 9px 14px;
  background-position: center center;
  background-repeat: no-repeat;
}
.u-icon-arrow-left {
  top: 3px;
  left: 0;
  background-image: url('https://b.yzcdn.cn/punch/icon/u-arrow-left.png');
}
.u-icon-arrow-right {
  top: 3px;
  right: 0;
  background-image: url('https://b.yzcdn.cn/punch/icon/u-arrow-right.png');
}
.u-current-month {
  display: inline-block;
  font-size: 14px;
  line-height: 20px;
  color: #333;
}
.u-current-date {
  position: absolute;
  top: 3px;
  right: 10px;
  font-size: 14px;
  line-height: 20px;
  color: #00B389;
}
/* 头部样式 结束 */

/* 星期列表样式 开始 */
.m-calendar-week-list {
  display: flex;
  background-color: #eee;
  padding: 8px 0;
}
.u-week-item {
  flex: 1;
  font-size: 14px;
  text-align: center;
  color: #333333;
}
.u-week-item-week {
  color: #F44;
}
/* 星期列表样式 结束 */

/* 日期列表样式 开始 */
.m-calendar-date-list {
  display: flex;
  flex-wrap:wrap;
}
.u-data-item-box {
  width: calc(100%/7);
}
.u-date-item {
  margin: 14rpx 0;
  height: 50px;
  text-align: center;
  font-size: 10px;
  color: #333;
}
.u-date-item-left{
  color: #F44;
  line-height: 19px;
  height: 19px;
}
.u-date-item-date{
  color: #ccc;
}
.u-date-item-price{
  color: #666;
  line-height: 19px;
  height: 19px;
}
.calendar-active-class {
  color: #fff;
  background-color: #F44;
}

.calendar-active-class .u-date-item-left,.calendar-active-class .u-date-item-price{
  color: #fff;
}


/* 日期列表样式 结束 */
