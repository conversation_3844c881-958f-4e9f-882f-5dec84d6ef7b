import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    currentYear: {
      type: String,
      value: ''
    },
    currentMonth: {
      type: String,
      value: ''
    },
    activeStyle: {
      type: String,
      value: ''
    },
    activeClass: {
      type: String,
      value: ''
    },
    calendarInfoData: {
      // ?? ob obj
      type: Object,
      value: {},
      observer(newValue, oldValue) {
        newValue = newValue || {};
        oldValue = oldValue || {};
        if (JSON.stringify(newValue.ecardPriceCalendarModelMap) != JSON.stringify(oldValue.ecardPriceCalendarModelMap)) {
          this.initDateList();
        }
      }
    },
  },
  data: {
    weekList: ['日', '一', '二', '三', '四', '五', '六'],
    dateList: [],
    activeMonth: 0,
    calendarMonth: []
  },
  attached() {
    // 在组件实例进入页面节点树时执行
    this.initDateList();
  },
  methods: {
    initDateList() {
      // 获取当前月份的天数
      let date = new Date();
      let currentYear = +(date.getFullYear());
      let currentMonth = +(date.getMonth() + 1);
      this.setData({
        currentYear,
        currentMonth
      });
      let calendarMonth = [this.calNextMonth(currentMonth), this.calNextMonth(currentMonth + 1), this.calNextMonth(currentMonth + 2), this.calNextMonth(currentMonth + 3)];
      this.setData({
        calendarMonth
      });
      this.handleDateList();
    },
    // 计算获取指定月份的年月
    calNextMonth(month) {
      let newMonth = month;
      let newYear = this.data.currentYear;
      if (month > 12) {
        // 如果当前已经是12月，到下一年
        newYear++;
        newMonth -= 12;
      }
      // 为了方便获取每月的信息
      let showCurrentMonth = newMonth > 9 ? newMonth : `0${newMonth}`;
      return {
        year: +newYear,
        month: +newMonth,
        paramName: `${newYear}-${showCurrentMonth}`
      };
    },
    // 切换月份
    onChangeMonth(event) {
      this.setData({
        activeMonth: event.currentTarget.dataset.index
      });
      this.handleDateList();
    },
    // 点击单个日期，触发事件，暴露事件钩子
    onDateItemClick(event) {
      let dataset = event.currentTarget.dataset;
      if (dataset.disable) return;
      let year = dataset.year;
      let month = dataset.month;
      let date = dataset.date;
      this.handleDateItem(year, month, date, dataset);
    },
    handleDateItem(year, month, date, dataset) {
      this.setDateItemActive(date);
      this.triggerEvent('dateItemClick', {
        year, month, date, dataset
      });
    },
    // 点击单个日期，该日期为active状态
    setDateItemActive(date) {
      let dateList = this.data.dateList;
      dateList.forEach((item) => {
        if (item.taskDate === date) {
          item.active = true;
        } else {
          item.active = false;
        }
      });

      this.setData({
        dateList
      });
    },
    // 处理dateList，传入currentYear: 年份, currentMonth: 月份
    handleDateList() {
      const currentYear = this.data.calendarMonth[this.data.activeMonth].year;
      let currentMonth = this.data.calendarMonth[this.data.activeMonth].month;
      // maxDay: 当月一共几天
      let maxDay = new Date(currentYear, currentMonth, 0).getDate();
      // 获取当前月份第一天是星期几, 0: 星期天, 6: 星期六
      let firstDay = new Date(`${currentYear}-${currentMonth}-1`.replace(/-/g, '/')).getDay();

      currentMonth = currentMonth > 9 ? currentMonth : `0${currentMonth}`;
      let dateList = [];
      // 前几天要空出来
      for (let i = 0, len = firstDay; i < len; i++) {
        dateList.push({});
      }
      let info = this.data.calendarInfoData ? this.data.calendarInfoData.ecardPriceCalendarModelMap : {};
      // 生成日历列表
      for (let i = 1, len = maxDay + 1; i < len; i++) {
        const infoDate = currentYear + '-' + currentMonth + '-' + (i > 9 ? i : `0${i}`);
        let obj = {
          taskDate: i,
          info: info[infoDate],
          infoDate
        };
        dateList.push(obj);
      }
      this.setData({
        dateList
      });
    }
  }
});
