import {
  checkRetailMinimalistShop,
  checkUnifiedShop,
} from '@youzan/utils-shop';
import request from '@youzan/tee-biz-request';

export const getHqShopSettings = (keys) => {
  return request({
    path: '/wscshop/shop/getHqShopSettings.json',
    data: {
      keys,
    },
  });
};

export const setShowTopBarInfo = ({
  shopMetaInfo,
  showBtn,
  cb,
  setGlobal,
  resCb,
  otherInfo,
}) => {
  let res = {
    showTopBar: false,
    needChangeShowTopBar: false,
    hqTopNavHidden: false,
    ...otherInfo,
  };
  const app = getApp();

  const isRetailMinimalistShop = checkRetailMinimalistShop(shopMetaInfo);
  const isUnifiedShop = checkUnifiedShop(shopMetaInfo);

  if (isRetailMinimalistShop || isUnifiedShop) {
    res.showTopBar = showBtn;
    res.needChangeShowTopBar = true;

    getHqShopSettings('hide_top_bar').then((result) => {
      res.showTopBar = false;
      const hqTopNavHidden = result.hide_top_bar
        ? Boolean(+result.hide_top_bar)
        : true;
      if (showBtn || !hqTopNavHidden) {
        res.showTopBar = true;
      }
      if (setGlobal) {
        app.globalData.showTopBarInfo = res;
      }
      resCb && resCb(res);
      cb && cb(res);
    });
  } else {
    res.showTopBar = true;
    resCb && resCb(res);
    if (setGlobal) {
      app.globalData.showTopBarInfo = res;
    }
  }
};
