export let ENTER_CONST_VAL = {
  // 跳转到其他店铺首页标识, 配置指定店铺和进总部使用
  hqHomeToOtherHome: false,
  // 缓存历史kdtId
  cacheKdtId: {},
  // 跳转底部导航配置缓存
  cacheNavConfig: {},
  // 判断是否执行了代理跳转
  isWxProxyDone: false,
  // 是否代理中更新kdtId
  isProxyUpdateKdtId: false,
  // 代理中更新kdtId缓存函数
  proxyUpdateEmitFunc: [],
  // 最后一次更新的网店kdtId
  lastOnlineKdtId: null,
  // 是否代理跳转锁
  proxyWxJumpPadding: false,
  // 是否Padding, 用于三方模板
  onAppRoutePadding: false,
  // 阻塞全局进店标识
  blockEnterShopType: false,
  // 用于webview跳转到小程序的识别缓存
  isEnterWbViewPage: false,
  // 底部导航变更kdtId标识
  // isTabbarChangeKdtId: false,
  // 切店范围缓存
  switchShopRangeCache: {},
  // 记录进入后台的url
  hideUrl: '',
  // 底部菜单缓存
  tabbarNavCacheMap: {},
  // 激活的tabBar
  activeTabBars: [],
};

export const ENTER_CACHE_FUNC = {
  // 代理跳缓存函数
  proxyDone: [],
};

const deepClone = (obj) => {
  return JSON.parse(JSON.stringify(obj));
};

const COPY_VAL = deepClone(ENTER_CONST_VAL);

export const clearEnterVal = () => {
  const { tabbarNavCacheMap } = ENTER_CONST_VAL;
  ENTER_CONST_VAL = {
    ...deepClone(COPY_VAL),
    tabbarNavCacheMap,
  };
};

// 不想缩写, 但为了体积......
export const URL_MAP = {
  H: '/pages/home/<USER>/index',
  F: '/pages/home/<USER>/index',
  T: '/packages/trade-buy/order/buy/index',
};
