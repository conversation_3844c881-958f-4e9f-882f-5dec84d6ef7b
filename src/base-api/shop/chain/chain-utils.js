import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import {
  checkSingleStore,
  checkEduChainStore,
  checkChainStore,
  checkRetailOnlineBranchStore,
  checkRetailOfflineBranchStore,
} from '@youzan/utils-shop';
import {
  getAsyncApolloConfig,
  ENTER_APOLLO_KEYS,
  localCache,
  accordSkyLogger,
} from 'common-api/multi-shop/multi-shop-redirect';
import { ENTER_CONST_VAL } from './constants';
import { waitForProxyWxJumpDone } from './helper';

// 是否是纯零售线下门店（不具备网店能力）
export function isPureOfflineShop(shopMetaInfo) {
  // fix 多渠道场景门店也有网店能力
  return (
    checkRetailOfflineBranchStore(shopMetaInfo) &&
    !checkRetailOnlineBranchStore(shopMetaInfo)
  );
}

// 是否有门店能力
export function isOfflineShop(shopMetaInfo) {
  return (
    checkRetailOfflineBranchStore(shopMetaInfo) ||
    checkSingleStore(shopMetaInfo)
  );
}

// 连锁店铺
export function isMultiOnlineShop(shopMetaInfo) {
  // 非单店的店铺，都应当属于连锁下的店铺
  return !checkSingleStore(shopMetaInfo);
}

export function isEduChainStore(shopMetaInfo) {
  return checkEduChainStore(shopMetaInfo);
}

export function isChainStore(shopMetaInfo) {
  return checkChainStore(shopMetaInfo);
}

export function isRootShop(shopMetaInfo) {
  return shopMetaInfo.shop_role === 1;
}

export function getCurrentPageOption() {
  const pages = getCurrentPages();

  if (pages.length === 0) {
    // pages为空时表明page未初始化, 需要从Launch options中取path、query参数
    const { path, query } = wx.getLaunchOptionsSync();
    return { path, query };
  }

  const { route: path, options: query } = pages[pages.length - 1];

  // 空白页进店则取globalData存的 path、query
  if (path === 'pages/common/blank-page/index') {
    return (getApp().globalData || {}).enterShopInfo;
  }

  return {
    path,
    query,
  };
}

export function getEnterRoute() {
  const { path = '', query = '' } = getCurrentPageOption() || {};
  const camelQuery = mapKeysCase.toCamelCase(query);
  const route = path.replace(/^pages(-|\/)/, 'packages/');
  return { route, camelQuery, path, query };
}

/** 获取全局进店忽略列表 */
export async function getGlobalEnterIgnoreConfig() {
  const apolloConfig = await getAsyncApolloConfig();
  return apolloConfig[ENTER_APOLLO_KEYS.ignoreConfig] || {};
}

// 检查是否能够进店。有些业务场景需要阻止 app 层面的自动进店
// 1. 连锁店铺
// 2. 处于特定规则忽略内的页面
// 3. 不在零售首页
export async function shouldEnterChainShop(shopMetaInfo = {}, customRoute) {
  if (!customRoute) {
    await waitForProxyWxJumpDone();
  }
  const { route, camelQuery, path, query } = getEnterRoute();

  const ignoreConfig = await getGlobalEnterIgnoreConfig();
  const { globalEnterStoreIgnore = [] } = ignoreConfig;
  const isIgnorePage = globalEnterStoreIgnore.includes(customRoute || route);
  const isUmpPromotionPage = camelQuery.umpAlias && camelQuery.umpType;

  const needEnterShop =
    !isPureOfflineShop(shopMetaInfo) && // 纯零售线下门店不需要进店
    isMultiOnlineShop(shopMetaInfo) &&
    !isIgnorePage && // @金凤 @营销
    !isUmpPromotionPage; // @金凤 @营销

  return {
    needEnterShop,
    path,
    query,
    route,
  };
}

/**
 * 特殊场景阻塞全局进店
 */
export async function blockSpecialEnterShop(route) {
  try {
    const [ignoreConfig, apolloConfig] = await Promise.all([
      getGlobalEnterIgnoreConfig(),
      getAsyncApolloConfig(),
    ]);
    const {
      webviewList = [],
      designEnterStoreIgnore = [],
      onRouteIgnoreCheck = [],
      needHqCheck = [],
    } = ignoreConfig;

    if (apolloConfig[ENTER_APOLLO_KEYS.loadIdCheck]) {
      const routeIgnore =
        onRouteIgnoreCheck.includes(route) ||
        onRouteIgnoreCheck.some((i) => route.startsWith(i));

      const result = { routeIgnore, loadIdCheck: true };
      if (webviewList.includes(route)) {
        return { ...result, type: 'webview' };
      }
      if (designEnterStoreIgnore.includes(route)) {
        return { ...result, type: 'design' };
      }
      if (needHqCheck.includes(route)) {
        return { ...result, type: 'checkHq' };
      }
      return result;
    }
  } catch (error) {}
  return { loadIdCheck: false };
}

// 判断当前店铺装修是否跟随总部
export function isDecorateFollowHq() {
  const app = getApp();
  const { config } = app.getShopInfoSync();
  return +config.subshop_decorate_independent === 0;
}

// 全局进店,检查route==common/webview-page是否需要全局进店并做缓存处理
// 2021年10月22日11:23:11 修复 pages/common/webview-expires/index 下进店问题
export function glEnterShopBlockCheck(route) {
  // 获取apollo配置,异步等待结果返回
  return blockSpecialEnterShop(route).then((opt) => {
    if (!opt.type || ['checkHq'].includes(opt.type)) return false;
    ENTER_CONST_VAL.blockEnterShopType = opt.type;
    accordSkyLogger({
      text: `[wx] 阻塞${opt.type}页面触发全局进店, url:${route}`,
    });
    // 设置首次进店缓存,首次总部进店无缓存时会添加shopAutoEnter=1
    // 这边设置缓存后就消除自动添加shopAutoEnter=1的隐患,导致进店不一致
    opt.type === 'webview' && localCache.set('_is_init_enter_shop_', true);
    return true;
  });
}
