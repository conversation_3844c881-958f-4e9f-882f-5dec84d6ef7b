import { ENTER_CONST_VAL, ENTER_CACHE_FUNC } from './constants';
import args from '@youzan/weapp-utils/lib/args';
import {
  isHqEnterShop,
  isChainStoreSyncInWX,
  isHome,
  getEnterShopCacheByBizType,
  CHAIN_BIZ_TYPE,
} from '@youzan/tee-chain-store';

import { getRootKdtId } from '@youzan/shop-core-tee';
import { getAppPage } from '@youzan/shop-tee-shared';

export { isHqEnterShop } from '@youzan/tee-chain-store';

export const getCacheKdtId = (path) => ENTER_CONST_VAL.cacheKdtId[path];

/** 缓存跳转 */
export const setCacheKdtId = (path, kdtId) => {
  ENTER_CONST_VAL.cacheKdtId[path] = kdtId;
};

/** 获取配置 */
export const getEnterCacheConfig = (key) => {
  return ENTER_CONST_VAL[key];
};

/** 设置配置 */
export const setEnterCacheConfig = (key, arg1, arg2) => {
  if (arg2) {
    ENTER_CONST_VAL[key][arg1] = arg2;
    return;
  }
  arg2 = arg1;
  ENTER_CONST_VAL[key] = arg2;
};

/** 缓存kdtid更新update函数 */
export const cacheProxyUpdateEmit = (emitFunc) => {
  ENTER_CONST_VAL.proxyUpdateEmitFunc.push(emitFunc);
};

/** 代理进行中 */
export function setProxyWxJumpPadding(t) {
  ENTER_CONST_VAL.proxyWxJumpPadding = t;
  if (!t) {
    ENTER_CACHE_FUNC.proxyDone.forEach((i) => i());
    ENTER_CACHE_FUNC.proxyDone = [];
  }
}

/** 代理是否执行跳转完毕 */
export async function waitForProxyWxJumpDone() {
  if (!ENTER_CONST_VAL.proxyWxJumpPadding) {
    return true;
  }
  return new Promise((resolve) => {
    ENTER_CACHE_FUNC.proxyDone.push(resolve);
  });
}

/** 是否被代理跳转执行 */
export function isWxProxyDone() {
  return getEnterCacheConfig('isWxProxyDone');
}

/** 是否被代理中更新kdtId */
export function isProxyUpdateKdtId() {
  return getEnterCacheConfig('isProxyUpdateKdtId');
}

/** 切店范围缓存-设置 */
export function setSwitchShopRangeCache(ext) {
  const { enterConfig, path } = ext;
  const enterShopId = parseInt(Math.random() * Math.random() * 10000000, 10);
  const rPtah = path ? 'tab' + path : '';
  ENTER_CONST_VAL.switchShopRangeCache[rPtah || enterShopId] = enterConfig;
  return enterShopId;
}

/** 切店范围缓存-获取 */
export function getSwitchShopRangeCache(ext) {
  // eslint-disable-next-line prefer-const
  let { url = '', enterShopId } = ext;
  const rangeCache = ENTER_CONST_VAL.switchShopRangeCache;

  if (!Object.keys(rangeCache).length) {
    return {};
  }

  if (!enterShopId) {
    const { enterShopId: uEnterShopId } = args.getAll(url) || {};
    enterShopId = uEnterShopId;
  }

  if (enterShopId && rangeCache[enterShopId]) {
    return rangeCache[enterShopId];
  }

  const path = url.replace(/^\//, '').replace(/\?.*/, '');
  const rPtah = 'tab' + path;
  if (path && rangeCache[rPtah]) {
    return rangeCache[rPtah];
  }
  return {};
}

/** 切店范围缓存-清理 */
export function clearCacheByHome() {
  const keys = Object.keys(ENTER_CONST_VAL.switchShopRangeCache);
  if (keys.length) {
    ENTER_CONST_VAL.switchShopRangeCache = keys.filter((k) =>
      k.startsWith('tab')
    );
  }
}

/**
 * 获取微信页面路径
 * 当前页面: preLength=0
 * 上一个页面: preLength=1
 * @param {*} preLength
 * @returns
 */
export function getWxPage(preLength = 0) {
  return getAppPage(preLength);
}

/**
 * 查找页面是否已激活
 * @param {*} path
 * @returns
 */
export function isActivePath(path) {
  // 获取当前页面栈信息
  const pages = ENTER_CONST_VAL.activeTabBars;
  if (pages.length) {
    return pages.find((i) => i === path);
  }
  return false;
}

/**
 * 强制使用总部底部导航
 * @returns
 */
export function forceUseHqTabbar() {
  return (
    isChainStoreSyncInWX() &&
    isHqEnterShop() &&
    ENTER_CONST_VAL.tabbarNavCacheMap[getRootKdtId()] &&
    !ENTER_CONST_VAL.hqHomeToOtherHome
  );
}

/**
 * 强制显示小房子home
 * @returns
 */
export function forceShowHome() {
  if (isChainStoreSyncInWX()) {
    const { path } = getWxPage();
    if (isHome(path) && ENTER_CONST_VAL.hqHomeToOtherHome) {
      return true;
    }
  }
}

/** 延时 */
export function delay(timeout) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, timeout);
  });
}

export function isHqOrHomeEnter() {
  return (getEnterShopCacheByBizType(CHAIN_BIZ_TYPE.HOME) || {}).kdtId;
}

export function isChainHqShop(kdtId) {
  return isChainStoreSyncInWX() && +kdtId === +getRootKdtId();
}
/**
 * 返回是否能更新kdtId
 * 这种场景 onAppRoute中执行更新KdtId
 * 目前用于首页场景返回阻止onshow中更新kdtId
 */
export function notUpdateKdtIdInChain() {
  return !getEnterCacheConfig('isWxProxyDone');
}

export function addPrefixPath(url) {
  return url.startsWith('/') ? url : '/' + url;
}

export function setCacheActiveTabBar(path, needClear) {
  if (needClear) {
    ENTER_CONST_VAL.activeTabBars = [path];
  }
  const arr = ENTER_CONST_VAL.activeTabBars || [];
  arr.push(path);
  ENTER_CONST_VAL.activeTabBars = arr;
}

export function clearCacheActiveTabBar() {
  ENTER_CONST_VAL.activeTabBars = [];
}
