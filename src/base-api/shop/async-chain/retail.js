/** 零售特有逻辑抽离 * */
import args from '@youzan/weapp-utils/lib/args';

import {
  localCache,
  accordSkyLogger,
  autoEnterShop,
  isChainStoreSyncInWX,
} from 'common-api/multi-shop/multi-shop-redirect';
import { debug } from '@youzan/tee-chain-store';
import { getRootKdtId, getCurrentKdtId } from '@youzan/shop-core-tee';
import {
  shouldEnterChainShop,
  blockSpecialEnterShop,
} from '@/base-api/shop/chain/chain-utils';
// eslint-disable-next-line import/no-cycle
import { EnterShopProxyWxJump, OnAppRoute } from './proxy-jump';
import { ENTER_CONST_VAL, URL_MAP } from '@/base-api/shop/chain/constants';

function enterShopFormRouteCheck(params) {
  const { needEnterShop, query, redirectUrl, path, sourcePath, openType } =
    params;
  // !needEnterShop 就认为页面有自己的进店,无需这边处理
  if (needEnterShop) {
    autoEnterShop(
      {
        ...query,
        redirectUrl,
        logTag: 'w_global_' + openType,
      },
      getRootKdtId()
    ).then((subKdtId) => {
      // 有返回店铺id,做下拉刷新
      if (subKdtId) {
        try {
          const url = args.add(`/${path}`, query);
          // eslint-disable-next-line @youzan/dmc/wx-check
          wx.redirectTo({
            url,
            fail: () => {
              // eslint-disable-next-line @youzan/dmc/wx-check
              wx.switchTab({
                url,
                fail: () => {
                  // 兜底跳转首页, 避免卡在此页面
                  // eslint-disable-next-line @youzan/dmc/wx-check
                  wx.reLaunch({ url });
                },
              });
            },
          });
        } catch (error) {
          accordSkyLogger({
            err: `[wx] enterShopFormRouteCheck跳转失败,url:${sourcePath},id:${getCurrentKdtId()}`,
          });
        }
      }
    });
  }
}

/**
 * 小程序页面切换做店铺kdtId检查
 * packages/common/webview-page/index下已屏蔽了全局进店
 */
function checkKdtIdAndDoAutoEnter(sourcePath, extra) {
  const app = getApp();
  const { shopMetaInfo = {} } = app.getShopInfoSync();
  // 连锁店铺下,未接入进店的页面强制执行总部进店获取用户上次访问过的店铺进店记录(node进店缓存)
  if (
    ENTER_CONST_VAL.isEnterWbViewPage ||
    ((extra.isAutoReLaunch || extra.type === 'checkHq') &&
      getCurrentKdtId() === getRootKdtId())
  ) {
    shouldEnterChainShop(shopMetaInfo).then((res) => {
      const { needEnterShop, path, query } = res;
      const postfix = `url:${path}, openType:${extra.openType}`;
      const redirectUrl = args.add('/' + path, query);
      const params = {
        needEnterShop,
        query,
        redirectUrl,
        path,
        sourcePath,
        openType: extra.openType,
      };

      // webview 页面跳转到原生页
      if (ENTER_CONST_VAL.isEnterWbViewPage) {
        // 防止多次更新
        ENTER_CONST_VAL.isEnterWbViewPage = false;
        // 跳转出来的重新检查进店
        // 这边设置缓存后就消除自动添加shopAutoEnter=1的隐患,导致进店不一致
        localCache.set('_is_init_enter_shop_', true);
        // 上报天网
        accordSkyLogger({
          text: `[wx] ${ENTER_CONST_VAL.blockEnterShopType}跳到原生页面,更新店铺id处理, ${postfix}`,
        });
        enterShopFormRouteCheck(params);
      } else if (
        // 热启动检测当前店铺是总部
        (extra.isAutoReLaunch || extra.type === 'checkHq') &&
        getCurrentKdtId() === getRootKdtId() &&
        needEnterShop
      ) {
        if (URL_MAP.T.includes(path)) {
          accordSkyLogger({
            text: `[wx] 检测下单页在总部,跳转首页 ${postfix}`,
          });
          // eslint-disable-next-line @youzan/dmc/wx-check
          wx.reLaunch({ url: URL_MAP.H });
          return;
        }
        accordSkyLogger({
          text: `[wx] 热启动检测当前停留在总部,主动触发进店, ${postfix}`,
        });
        enterShopFormRouteCheck(params);
      }
    });
  }
}

/**
 * 监听wx的router变化
 */
export function listenChainRouterChange() {
  if (!isChainStoreSyncInWX()) return;

  try {
    new EnterShopProxyWxJump();
    const OnAppRouteFunc = new OnAppRoute();

    wx.onAppRoute(function (res) {
      debug('onAppRoute', res);
      const { openType, path } = res;
      const route = path.replace(/^pages(-|\/)/, 'packages/');
      // 返回更新历史店铺id
      OnAppRouteFunc.init(res);
      const isAutoReLaunch = openType === 'autoReLaunch';
      // 当小程序启动或重新启动时(热启动)
      if (['appLaunch', 'autoReLaunch'].includes(openType)) {
        ENTER_CONST_VAL.isEnterWbViewPage = false;
        const { scene, path, query } = wx.getEnterOptionsSync() || {};
        accordSkyLogger({
          text: `[wx] 小程序初始化路径${openType}: ${JSON.stringify({
            path,
            query,
            scene,
          })}`,
        });
      }
      // apollo灰度开关
      blockSpecialEnterShop(route).then((options) => {
        const { routeIgnore, type, loadIdCheck } = options;
        if (['webview', 'design'].includes(type)) {
          ENTER_CONST_VAL.isEnterWbViewPage = true;
          return;
        }

        if (loadIdCheck && !routeIgnore) {
          // 店铺id检查更新
          checkKdtIdAndDoAutoEnter(path, {
            isAutoReLaunch,
            openType,
            type,
          });
        }
      });
    });
  } catch (error) {
    debug('listenChainRouterChange', error);
    accordSkyLogger({
      err: `[wx] 监听router失败`,
    });
    return {};
  }
}
