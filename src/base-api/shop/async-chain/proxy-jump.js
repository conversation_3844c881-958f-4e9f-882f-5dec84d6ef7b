import { getRootKdtId, getCurrentKdtId } from '@youzan/shop-core-tee';
import {
  shouldEnterChainShop,
  getGlobalEnterIgnoreConfig,
} from '@/base-api/shop/chain/chain-utils';
import args from '@youzan/weapp-utils/lib/args';
import {
  enterShop<PERSON>pollo,
  accordSkyLogger,
  autoEnterShop,
  getEnterShopCacheByBizType,
  CHAIN_BIZ_TYPE,
  isHome as isHomeFunc,
} from 'common-api/multi-shop/multi-shop-redirect';

import getTabBarList from '@youzan/wsc-tab-bar-utils/lib/getTabBarList';
import {
  debug,
  FeatureEnterMode,
  isHqEnterShop,
  enterShopSelect,
  clearEnterShopCache,
  setApplicationScene,
  EXT_SCENE_ID,
} from '@youzan/tee-chain-store';
// eslint-disable-next-line import/no-cycle
import { updateKdtId } from '@/base-api/shop/index';
import {
  ENTER_CONST_VAL,
  clearEnterVal,
  URL_MAP,
} from '@/base-api/shop/chain/constants';
import {
  getCacheKdtId,
  setCacheKdtId,
  setProxyWxJumpPadding,
  setSwitchShopRangeCache,
  clearCacheByHome,
  getWxPage,
  isActivePath,
  delay,
  isHqOrHomeEnter,
  addPrefixPath,
  waitForProxyWxJumpDone,
  setCacheActiveTabBar,
  clearCacheActiveTabBar,
} from '@/base-api/shop/chain/helper';

const enterShopSelectFunc = (redirectUrl, redirectType = 'navigateTo') => {
  return enterShopSelect(
    {
      redirectUrl: encodeURIComponent(redirectUrl),
    },
    {
      redirectType,
    }
  );
};

// 抽离home逻辑
const handleHomeFunc = (query, type) => {
  const { kdtId: cacheKdtId, routeBizType } =
    getEnterShopCacheByBizType(CHAIN_BIZ_TYPE.HOME) || {};
  const urlKdtId =
    query.subKdtId || query.sub_kdt_id || query.kdtId || query.kdt_id;
  // 只有指定进店或总部进店,才会更新, 其他场景都是跟随
  if (cacheKdtId) {
    if (type === 'switchTab' && ENTER_CONST_VAL.hqHomeToOtherHome) {
      return {};
    }

    if (+cacheKdtId !== getCurrentKdtId() && !urlKdtId) {
      const info = `首页回到${
        routeBizType === 'hq' ? '总部进店' : '指定进店'
      }${cacheKdtId}`;

      accordSkyLogger({
        name: 'enterShopProxyWxJump',
        info,
      });
      ENTER_CONST_VAL.hqHomeToOtherHome = false;
      return { routeBizType, cacheKdtId };
    }

    // 缓存和需要进入店铺不一致, 说明要去别的店铺首页了
    if (urlKdtId && +cacheKdtId !== +urlKdtId) {
      ENTER_CONST_VAL.hqHomeToOtherHome = true;
    }
  }

  return {};
};

const getCurrentNavInfo = (path, isSwitchTab) => {
  if (!isSwitchTab) return {};
  const { tabbarOriginList } = getApp().globalData;
  let currentNavInfo = null;

  let tabIndex = getTabBarList().findIndex((i) => i === '/' + path);
  tabIndex > -1 && (currentNavInfo = tabbarOriginList[tabIndex]);
  // 不存在
  if (!currentNavInfo) {
    tabIndex = tabbarOriginList.findIndex(
      (i) => i.pagePath.replace(/\?.*/, '') === path
    );
    tabIndex > -1 && (currentNavInfo = tabbarOriginList[tabIndex]);
  }

  return { currentNavInfo, tabIndex };
};

// 不支持底部导航进店场景
const isNotFeatureMode = (kdtId) => {
  const homeKdtId = kdtId || getCacheKdtId('home');
  const cache = ENTER_CONST_VAL.tabbarNavCacheMap[homeKdtId];
  /**
   * 底部导航只有总部能设置进店跳转, 而且只能设置微页面,下发到跟随网店
   * 非总部进店&底部导航数据是自定义装修(没有底部导航跳转逻辑)
   */
  return !isHqEnterShop() && !cache?.isDecorateFollowHq;
};

// 微页面跳转
export function featureJump(extra = {}) {
  const {
    enterConfig,
    bannerIdOpt,
    alias,
    callback,
    jumpType = 'navigateTo',
  } = extra;
  const { mode, kdtIdList } = enterConfig;
  if (!enterConfig) return false;

  if (mode) {
    // 进店场景值
    setApplicationScene({
      sceneId: EXT_SCENE_ID.FEATURE,
      sceneSource: 'CUSTOM',
    });
  }
  // 进店规则进店
  if (mode === FeatureEnterMode.Enter) {
    const opt = { alias, ...bannerIdOpt };
    opt.shopAutoEnter = 4;
    // 业务场景：微页面配置了总部的微页面，需要加上进店标
    callback && callback();
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx[jumpType]({ url: args.add(URL_MAP.F, opt) });
    return true;
  }
  // 指定进店
  if (
    mode === FeatureEnterMode.Assign &&
    +kdtIdList[0] !== +getCurrentKdtId()
  ) {
    callback && callback();
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx[jumpType]({
      url: args.add(URL_MAP.F, {
        alias,
        kdtId: kdtIdList[0],
        ...bannerIdOpt,
      }),
    });
    // dmc.navigate('Feature', { alias, kdtId: kdtIdList[0], ...bannerIdOpt });
    return true;
  }
  // 店铺列表
  if (mode === FeatureEnterMode.Select) {
    const enterShopId = setSwitchShopRangeCache({ enterConfig });
    const opt = { alias, ...bannerIdOpt, enterShopId };
    callback && callback();
    enterShopSelectFunc(args.add(URL_MAP.F, opt), jumpType);
    return true;
  }
}

// 点单宝跳转
export function retailShelfJump(extra = {}) {
  if (!(extra && Object.keys(extra))) {
    return extra.linkUrl;
  }
  debug('extra', extra);
  const { enterConfig, alias = '', callback, linkUrl } = extra;
  // 单店直接返回
  if (!getApp().isChainStoreSync()) {
    return addPrefixPath(args.add(linkUrl, { alias }));
  }
  // 没有进店配置返回原始链接
  if (!enterConfig) return addPrefixPath(linkUrl);
  // 进店场景值
  // setApplicationScene({
  //   sceneId: EXT_SCENE_ID.FEATURE,
  //   sceneSource: 'CUSTOM',
  // });
  const enterShopId = setSwitchShopRangeCache({ enterConfig });
  const opt = { alias, enterShopId };
  callback && callback(opt, enterConfig);
  const url = args.add(linkUrl, opt);
  return addPrefixPath(url);
}

export class EnterShopProxyWxJump {
  // 微信app
  app = {};

  keys = ['reLaunch', 'switchTab', 'navigateTo', 'navigateBack', 'redirectTo'];

  // 跳转类型
  type = '';

  // 跳转参数
  options = {};

  // 原始函数
  rawFunc = {};

  // 基础信息
  baseInfo = {};

  // 缓存函数
  cacheUpdateFunc = null;

  // 执行次数
  times = 0;

  constructor() {
    this.init();
  }

  // 实例初始化
  async init() {
    const navBackUpdatePreKdtId = await enterShopApollo(
      'navBackUpdatePreKdtId'
    );
    if (!navBackUpdatePreKdtId) return;

    this.app = getApp();
    this.getPagesReplaceRules = this.app.getPagesReplaceRules();
    this.delayTime = await enterShopApollo('blockDelayTime', { raw: true });
    this.keys.forEach((i) => {
      this.rawFunc[i] = wx[i];
      const newFunc = this.proxyRun(i);
      Object.defineProperty(wx, i, {
        get() {
          return newFunc;
        },
      });
    });
  }

  proxyRun(i) {
    return async (options) => {
      try {
        if (!(await this.start(i, options))) return;
        await this[i](options);
      } catch (error) {
        await this.end({ noCheckUrl: true });
        this.rawFunc[i](options);
        this.logger({
          info: '跳转劫持异常',
          error,
        });
      }
    };
  }

  // 前置的校验
  async start(type, options) {
    debug(type, options);
    this.baseInfo = {};
    // navigateBack options和其他不一样
    if (type === 'navigateBack' || !options.url) {
      this.type = type;
      setProxyWxJumpPadding(true);
      await this.baseAction(options);
      return true;
    }

    const ignoreCheck = await this.ignoreCheckPath(options);
    // 等待上一个执行完成
    await waitForProxyWxJumpDone();
    // if (ENTER_CONST_VAL.proxyWxJumpPadding && !ignoreCheck) {
    //   this.times += 1;
    //   if (this.times < 2) {
    //     this.blockFunc = {
    //       type,
    //       options,
    //       time: Date.now(),
    //     };
    //     // 阻塞可能会有问题
    //     this.logger('被阻塞了');
    //     return false;
    //   }
    // }
    // 重置计数器
    // this.times = 0;
    this.type = type;
    this.options = options;

    setProxyWxJumpPadding(true);
    await this.baseAction(options);
    ENTER_CONST_VAL.lastUrl = this.baseInfo.path;
    // 如果是忽略列表, 直接执行原始函数
    if (ignoreCheck) {
      // 缓存tab数据
      this.isSwitchTabChangeShop(options);
      this.runFunc(options);
      return false;
    }

    return true;
  }

  // 结束,清理工作
  async end(ext = {}) {
    const { noCheckUrl, scene } = ext;
    const { path, targetIsSwitchTab } = this.baseInfo;
    ENTER_CONST_VAL.isProxyUpdateKdtId = false;
    const length = ENTER_CONST_VAL.proxyUpdateEmitFunc?.length;
    if (length) {
      if (noCheckUrl || (await this.pageInstantCreated())) {
        const { proxyUpdateEmitFunc } = ENTER_CONST_VAL;
        const { length } = proxyUpdateEmitFunc || [];
        proxyUpdateEmitFunc && proxyUpdateEmitFunc[length - 1]();
        if (this.cacheUpdateFunc) {
          this.cacheUpdateFunc();
        }
      }
    }
    this.cacheUpdateFunc = null;
    ENTER_CONST_VAL.proxyUpdateEmitFunc = [];
    setProxyWxJumpPadding(false);
    if (this._targetIsSwitchTab) {
      setCacheActiveTabBar(path, scene === '100');
    } else if (this.type === 'relaunch') {
      if (targetIsSwitchTab) {
        setCacheActiveTabBar(path, true);
      } else {
        clearCacheActiveTabBar();
      }
    }
  }

  // 判断是否跳转完成, getCurrentPages比较滞后
  async pageInstantCreated(n = 1) {
    const { path } = getWxPage();
    const { path: targetPath } = this.baseInfo;
    if (path === targetPath) {
      return true;
    }
    await delay(5);
    if (n > 300) return true;
    return this.pageInstantCreated(n + 1);
  }

  // 执行函数
  async runFunc(arg) {
    const { targetIsHome } = this.baseInfo;
    if (targetIsHome && this.type !== 'reLaunch') {
      if (getCurrentKdtId() !== getCacheKdtId('home') && isHqOrHomeEnter()) {
        // 这里用reLaunch, 主要是为了情况switchTab的路由栈
        // 同时还可以更新底部导航,
        // 不过会牺牲一下性能体验
        debug('home-reLaunch');
        this.rawFunc.reLaunch({
          url: URL_MAP.H,
        });
        return this.end();
      }
    }
    this.rawFunc[this.type](arg);
    this.end();
  }

  // 前置的基础信息
  async baseAction(options) {
    let targetPath = '';
    if (this.type === 'navigateBack') {
      if (options && options.delta) {
        targetPath = getWxPage(+options.delta)?.path;
      } else {
        targetPath = getWxPage(1).path;
      }
    } else {
      targetPath = (options.url || getWxPage().path)
        .replace(/^\//, '')
        .replace(/\?.*/, '');
    }
    const currentKdtId = getCurrentKdtId();

    const currentPage = getWxPage();
    const { path: currentPath } = currentPage || {};

    const { shopMetaInfo = {} } = this.app.getShopInfoSync();

    // 是否能执行全局进店, 否代表页面单独接入了
    const { needEnterShop } = await shouldEnterChainShop(
      shopMetaInfo,
      targetPath.replace(/^pages(-|\/)/, 'packages/')
    );

    // 当前是tabbar页面
    const currentIsSwitchTab = await this.app.isSwitchTab(
      '/' + currentPath,
      false,
      false
    );

    // 是否tabbar页面
    const targetIsSwitchTab = await this.app.isSwitchTab(
      '/' + targetPath,
      false,
      false
    );

    this.baseInfo = {
      path: targetPath,
      targetPath,
      targetIsSwitchTab,
      isIgnoreCache:
        this.type === 'reLaunch' && targetPath.includes('shopselect'),
      targetIsHome: isHomeFunc(targetPath), // 跳转目标是否首页
      needEnterShop,
      // current代表跳转前
      currentKdtId,
      currentIsSwitchTab,
      currentPath,
      currentIsHome: isHomeFunc(currentPath), // 跳转前的页面是否首页
    };
    ENTER_CONST_VAL.isWxProxyDone = true;
    this.setCacheKdtId();
  }

  // 检查是否忽略劫持
  async ignoreCheckPath(options) {
    const { ignoreProxyJumpCheck = [], needProxyJumpCheck = [] } =
      await getGlobalEnterIgnoreConfig();
    // 重定向列表需要忽略
    const pagesReplaceRules = await this.getPagesReplaceRules.catch(() => ({}));
    const url = options.url.replace(/^\//, '');
    const pckUrl = url.replace(/^pages(-|\/)/, 'packages/');
    const pagUrl = url.split('?')[0];
    // 强制检查
    const needCheck = needProxyJumpCheck.find((i) => pckUrl.includes(i));
    if (needCheck) {
      return false;
    }
    return (
      ignoreProxyJumpCheck.find((i) => pckUrl.includes(i)) ||
      pagesReplaceRules[pagUrl]
    );
  }

  setCacheKdtId() {
    const {
      currentIsSwitchTab,
      currentPath,
      currentKdtId,
      currentIsHome,
      targetIsHome,
      isIgnoreCache,
    } = this.baseInfo;
    // navigateTo缓存跳转前的页面
    if (currentIsHome && !isIgnoreCache) {
      setCacheKdtId('home', currentKdtId);
    }

    if (targetIsHome) {
      // 总部清空切店范围缓存
      clearCacheByHome();
    }
    // tab缓存跳转前的页面
    if (currentIsSwitchTab) {
      return setCacheKdtId(currentPath, currentKdtId);
    }
  }

  // 集合处理, 包含如果是总部会重新进店
  async jumpGather(options) {
    const { needEnterShop } = this.baseInfo;
    const ignoreCheck = await this.ignoreCheckPath(options);
    // 如果当前店铺是总部, needEnterShop,切换底部导航需要重新进店
    // 单独接进店的不用处理
    if (
      isHqEnterShop() &&
      getRootKdtId() === getCurrentKdtId() &&
      needEnterShop &&
      !ignoreCheck
    ) {
      this.logger('总部重新进店');
      const { lastOnlineKdtId } = ENTER_CONST_VAL;
      // 如果有上次进入的网店, 直接更新成最后一次的网店
      if (lastOnlineKdtId) {
        await this.updateKdtId({
          cacheKdtId: lastOnlineKdtId,
          mark: '1001',
        });
      } else {
        // 重新进店
        try {
          // 进店有可能跳转店铺列表
          ENTER_CONST_VAL.isProxyUpdateKdtId = true;
          await autoEnterShop({
            redirectUrl: options.url,
            logTag: 'proxy_global',
            kdtId: getRootKdtId(),
            shopAutoEnter: 1,
          });
        } catch (e) {
          this.end({ noCheckUrl: true });
          return true;
        }
      }
    }

    return this.runFunc(options);
  }

  async updateKdtId(ext) {
    // 主要为了kdtId改变触发emit, 用状态控制下
    ENTER_CONST_VAL.isProxyUpdateKdtId = true;
    const { cacheKdtId, mark } = ext;
    await updateKdtId(cacheKdtId, getRootKdtId() === cacheKdtId, {
      mark,
    });
    this.cacheUpdateFunc = () => this.app.updateChainStoreInfo();
  }

  async switchTab(options) {
    const { path, currentPath, targetIsSwitchTab } = this.baseInfo;

    // 各种中间页无需处理
    if (currentPath.includes('pages/common')) {
      return this.runFunc(options);
    }

    const { currentNavInfo } = getCurrentNavInfo(path, targetIsSwitchTab);
    // 不存在
    if (!currentNavInfo) {
      return this.runFunc(options);
    }

    // 非微页面或非主页
    if (
      !['feature', 'home', 'retail-shelf'].includes(currentNavInfo.attachedId)
    ) {
      // 匹配是switchTab, 先只处理非微页面场景
      this._targetIsSwitchTab = true;
      // 已进店, 跟随当前店铺
      if (getRootKdtId() !== getCurrentKdtId()) {
        const cacheKdtId = getCacheKdtId(path);
        // 这里可能存在刷新问题, switchTab切换默认是不刷新的, 所以如果缓存店铺不一致需要reLaunch下
        // 不过这样处理体验就会下降
        if (
          cacheKdtId &&
          getCurrentKdtId() !== cacheKdtId &&
          isActivePath(path)
        ) {
          await this.end({ noCheckUrl: true, scene: '100' });
          // eslint-disable-next-line @youzan/dmc/wx-check
          return wx.reLaunch(options);
        }
        return this.runFunc(options);
      }
      return this.jumpGather(options);
    }

    // 微页面
    if (['feature', 'retail-shelf'].includes(currentNavInfo.attachedId)) {
      if (isNotFeatureMode()) {
        this.logger('tab-自定义装修');
        return this.runFunc(options);
      }

      const { enterConfig } = currentNavInfo.extraData || {};
      const { mode, kdtIdList = [] } = enterConfig || {};

      // 未设置 或 按进店规则进店, 如果已进店就是跟随上一个店铺
      if (!mode || mode === FeatureEnterMode.Enter) {
        this.logger('tab-跟随进店');
        setCacheKdtId(path, '');
        return this.runFunc(options);
      }

      // 只有配置其他模式才读取缓存
      const cacheKdtId = getCacheKdtId(path);
      // 只有总部进店才走缓存, 优先读取缓存
      if (isHqEnterShop() && cacheKdtId) {
        if (getCurrentKdtId() !== cacheKdtId) {
          this.logger('tab-进入缓存店铺');
          await this.updateKdtId({
            cacheKdtId,
            mark: '1002',
          });
        }
        return this.runFunc(options);
      }

      // 点单宝其他场景在内部执行
      if (currentNavInfo.attachedId === 'retail-shelf') {
        return this.runFunc(options);
      }

      // 指定进店
      if (mode === FeatureEnterMode.Assign) {
        this.logger('tab-指定进店');
        await this.updateKdtId({
          cacheKdtId: kdtIdList[0],
          mark: '1003',
        });
        this.cacheSwitchConfig();
        if (!this.checkIsSameSwitchTab()) return;
        return this.runFunc(options);
      }

      // 店铺列表
      if (mode === FeatureEnterMode.Select) {
        this.logger('tab-店铺列表');
        this.cacheSwitchConfig();
        // 解锁
        setProxyWxJumpPadding(false);
        // 缓存,用于店铺列表切店范围
        setSwitchShopRangeCache({
          enterConfig,
          path,
        });
        await enterShopSelectFunc(options.url);
        return;
      }
    }

    if (currentNavInfo.attachedId === 'home') {
      // const cacheKdtId = getCacheKdtId(path);
      // 只有指定店铺和进总部
      const { kdtId: cacheKdtId } =
        getEnterShopCacheByBizType(CHAIN_BIZ_TYPE.HOME) || {};
      if (
        cacheKdtId &&
        +cacheKdtId !== getCurrentKdtId() &&
        !ENTER_CONST_VAL.hqHomeToOtherHome
      ) {
        if (getCurrentKdtId() !== cacheKdtId) {
          this.logger('tab-home-进入缓存店铺');
          await this.updateKdtId({
            cacheKdtId,
            mark: '1004',
          });
        }
      }
      return this.runFunc(options);
    }

    // 此函数包含了兜底
    return this.jumpGather(options);
  }

  isSwitchTabChangeShop() {
    const { targetPath, currentIsSwitchTab, currentPath, currentIsHome } =
      this.baseInfo;
    if (targetPath.includes('chain-store/shopselect/index') && !currentIsHome) {
      const { currentNavInfo, tabIndex } = getCurrentNavInfo(
        currentPath,
        currentIsSwitchTab
      );
      if (currentNavInfo) {
        ENTER_CONST_VAL.cacheNavConfig[currentPath] = {
          tabIndex,
          currentNavInfo,
        };
      }
    } else if (this.type === 'reLaunch') {
      ENTER_CONST_VAL.cacheNavConfig = {};
    }
  }

  cacheSwitchConfig() {
    const { targetPath, targetIsSwitchTab } = this.baseInfo;
    const { currentNavInfo, tabIndex } = getCurrentNavInfo(
      targetPath,
      targetIsSwitchTab
    );
    if (currentNavInfo) {
      ENTER_CONST_VAL.cacheNavConfig[targetPath] = {
        tabIndex,
        currentNavInfo,
      };
    }
  }

  checkIsSameSwitchTab() {
    const { targetPath, targetIsSwitchTab, targetIsHome } = this.baseInfo;
    // 首页或总部进店不管
    if (targetIsHome || this.app.forceUseHqTabbar()) return true;

    if (targetIsSwitchTab && targetPath.includes('pages/tab/')) {
      const { list } = this.app.getNavConfig() || {};
      const { tabIndex, currentNavInfo } =
        ENTER_CONST_VAL.cacheNavConfig[targetPath] || {};
      ENTER_CONST_VAL.cacheNavConfig[targetPath] = {};
      const { length } = list;
      // tabIndex 从0开始
      if (length - 2 < tabIndex) {
        this.logger('tabIndex 数量对应不上, tabbar重定向跳转');

        if (currentNavInfo.attachedId === 'feature') {
          const { alias } = currentNavInfo;
          const url = `${URL_MAP.F}?alias=${alias}&kdtId=${getCurrentKdtId()}`;
          this.rawFunc.reLaunch({ url });
        } else {
          let url = currentNavInfo.pagePath || URL_MAP.H;
          url = '/' + url.replace(/^\//, '').replace(/\?.*/, '');
          this.rawFunc.reLaunch({
            url,
            fail: () => {
              this.logger('tabbar跳转未找到页面');
              this.rawFunc.reLaunch({ url: URL_MAP.H });
            },
          });
        }
        this.end();
        return false;
      }
    }
    return true;
  }

  async redirectTo(options) {
    if (options.url.startsWith(URL_MAP.F)) {
      const { enterConfig, alias, ...bannerIdOpt } = args.getAll(options.url);
      // webview三方模板，跳转原生微页面会携带enterConfig
      if (enterConfig) {
        try {
          // 如果上一个页面是首页， 缓存下kdtId
          if (isHomeFunc(getWxPage(1).path)) {
            setCacheKdtId('home', getCurrentKdtId());
          }
          if (
            featureJump({
              alias,
              bannerIdOpt,
              enterConfig: JSON.parse(
                decodeURIComponent(decodeURIComponent(enterConfig))
              ),
              callback: this.end.bind(this),
              jumpType: 'redirectTo',
            })
          ) {
            return;
          }
        } catch (err) {}
      }
    }
    return this.jumpGather(options);
  }

  async navigateTo(options) {
    return this.jumpGather(options);
  }

  // 返回值处理首页场景
  async navigateBack(options) {
    const { targetPath } = this.baseInfo;
    if (isHomeFunc(targetPath)) {
      const cacheKdtId = getCacheKdtId('home');
      if (cacheKdtId && cacheKdtId !== getCurrentKdtId()) {
        await this.updateKdtId({ cacheKdtId, mark: '1005' });
      }
    }
    return this.runFunc(options);
  }

  async reLaunch(options) {
    // tab切店检查
    if (!this.checkIsSameSwitchTab(options)) return;
    const { targetIsHome } = this.baseInfo;
    const query = args.getAll(options.url);
    // 总部会回到指定店铺场景
    if (targetIsHome) {
      const { routeBizType, cacheKdtId } = handleHomeFunc(query, 'reLaunch');
      if (routeBizType) {
        await this.updateKdtId({ cacheKdtId, mark: '1006' });
      }
    }
    this.runFunc(options);
  }

  logger(info) {
    const { url } = this.options || {};
    accordSkyLogger({
      name: 'enterShopProxyWxJump',
      type: this.type,
      kdtId: getCurrentKdtId(),
      url,
      info,
    });
  }
}

// 和EnterShopProxyWxJump类强关联的
export class OnAppRoute {
  options = {};

  // 路由监听
  async init(options) {
    this.options = options;
    // openType 跳转方式
    // (appLaunch/autoReLaunch/reLaunch/switchTab/navigateTo/navigateBack/redirectTo)
    const { openType, path, query } = options;
    this.routeLogger(options);
    if (['appLaunch', 'autoReLaunch'].includes(openType)) {
      // 热启动从新扫码会清空缓存
      if (!(isHomeFunc(path) && Object.keys(query).length === 0)) {
        clearEnterVal();
        clearEnterShopCache(CHAIN_BIZ_TYPE.HOME);
      }
      clearEnterShopCache(CHAIN_BIZ_TYPE.RETAIL_SHELF);
    }
    if (!ENTER_CONST_VAL.isWxProxyDone || ['navigateBack'].includes(openType)) {
      ENTER_CONST_VAL.lastUrl = path;
    }
    // 是否开启返回功能
    const navBackUpdatePreKdtId = await enterShopApollo(
      'navBackUpdatePreKdtId'
    );
    if (!navBackUpdatePreKdtId) return;

    if (['navigateBack'].includes(openType)) {
      this.onNavigateBack(options);
    }

    if (['switchTab'].includes(openType)) {
      await this.onSwitchTab(options);
    }

    this.reLaunchRouter(options);
    this.end(options);
  }

  // onAppRoute触发, 这里的主要是触发手势左滑返回
  async onSwitchTab(options) {
    if (ENTER_CONST_VAL.isWxProxyDone) return;
    const { path, query } = options;

    if (isHomeFunc(path)) {
      return this.homeAction({ mark: '1010' });
    }

    const { currentNavInfo, tabIndex } = getCurrentNavInfo(path, true);
    const { attachedId, extraData } = currentNavInfo || {};

    if (attachedId === 'feature') {
      /**
       * 底部导航只有总部能设置进店跳转, 而且只能设置微页面,下发到跟随网店
       * 非总部进店&底部导航数据是自定义装修(没有底部导航跳转逻辑)
       */
      if (isNotFeatureMode(getCurrentKdtId())) {
        this.logger('tab-自定义装修');
        return;
      }

      const { enterConfig } = extraData || {};
      const { mode, kdtIdList = [] } = enterConfig || {};
      ENTER_CONST_VAL.onAppRoutePadding = true;
      // 只有配置其他模式才读取缓存
      const cacheKdtId = getCacheKdtId(path);
      // 只有总部进店才走缓存, 优先读取缓存
      if (cacheKdtId) {
        if (getCurrentKdtId() !== cacheKdtId) {
          this.logger({
            info: 'OR-tab-进入缓存店铺',
          });
          await updateKdtId(cacheKdtId, false, {
            mark: '1007',
          });
          this.refreshPage(path);
        }
        return;
      }

      // 指定进店
      if (mode === FeatureEnterMode.Assign) {
        this.logger({
          info: 'OR-tab-指定进店',
        });
        await updateKdtId(kdtIdList[0], false, {
          mark: '1008',
        });
        this.refreshPage(path);
        return;
      }

      // 店铺列表
      if (mode === FeatureEnterMode.Select) {
        this.logger({
          info: 'OR-tab-店铺列表',
        });
        if (currentNavInfo) {
          ENTER_CONST_VAL.cacheNavConfig[path] = {
            tabIndex,
            currentNavInfo,
          };
        }
        // 缓存,用于店铺列表切店范围
        setSwitchShopRangeCache({
          enterConfig,
          path,
        });
        enterShopSelectFunc(args.add('/' + path, query), 'reLaunch');
      }
    } else {
      const { shopMetaInfo = {} } = getApp().getShopInfoSync();
      const { needEnterShop } = await shouldEnterChainShop(shopMetaInfo);

      // 如果当前店铺是总部, needEnterShop,切换底部导航需要重新进店
      // 单独接进店的不用处理
      if (
        isHqEnterShop() &&
        getRootKdtId() === getCurrentKdtId() &&
        needEnterShop
      ) {
        this.logger({
          info: 'OR-总部重新进店',
        });
        await autoEnterShop({
          redirectUrl: options.url,
          logTag: 'proxy_global',
          kdtId: getRootKdtId(),
          shopAutoEnter: 1,
        });
        this.refreshPage(path);
      }
    }
  }

  refreshPage(url) {
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx.reLaunch({
      url: url.startsWith('/') ? url : '/' + url,
    });
  }

  // onAppRoute触发, 这里的主要是触发手势左滑返回
  async onNavigateBack(options) {
    const { path } = options;
    if (isHomeFunc(path) && !ENTER_CONST_VAL.isWxProxyDone) {
      const cacheKdtId = getCacheKdtId('home');
      if (cacheKdtId && cacheKdtId !== getCurrentKdtId()) {
        await updateKdtId(cacheKdtId, getRootKdtId() === cacheKdtId, {
          mark: 1011,
        });
        getApp().updateChainStoreInfo();
      }
    }
  }

  homeAction({ mark }) {
    const { query, openType } = this.options;
    const { routeBizType, cacheKdtId } = handleHomeFunc(query, openType);
    if (routeBizType) {
      updateKdtId(cacheKdtId, getRootKdtId() === cacheKdtId, {
        mark,
      });
      this.refreshPage(`${URL_MAP.H}?kdt_id=` + cacheKdtId);
    } else {
      setCacheKdtId('home', getCurrentKdtId());
    }
  }

  reLaunchRouter(options) {
    const { openType, path } = options;
    if (
      ['autoReLaunch', 'reLaunch'].includes(openType) &&
      isHomeFunc(path) &&
      !ENTER_CONST_VAL.isWxProxyDone
    ) {
      this.homeAction({ mark: '1009' });
    }
  }

  // 结束,清理
  end() {
    ENTER_CONST_VAL.isWxProxyDone = false;
    setTimeout(() => {
      ENTER_CONST_VAL.onAppRoutePadding = false;
    }, 500);
  }

  // 辅助日志
  async routeLogger(options) {
    const { openType, path } = options;
    if (await enterShopApollo('accordLogger')) {
      accordSkyLogger({
        text: 'onAppRoute',
        msg: {
          openType,
          path,
        },
      });
    }
  }

  // 进店路由跳转日志
  logger(info) {
    const { path, openType } = this.options || {};
    accordSkyLogger({
      name: 'OnAppRoute',
      type: openType,
      kdtId: getCurrentKdtId(),
      path,
      info,
    });
  }
}
