export const defaultShopConfig = {
  // 请求的时候有可能直接调用
  // 写为 undefined 的可以保证请求如果没有，就会默认删除这个参数
  kdtId: 0,
  im: null,
  // nav: mapKeysCase.toSnakeCase(getExtSync('tabBar')),
  nav: {},
  vip: null,
  base: null,
  store: null,
  config: null,
  copyright: {
    isCustomized: false,
    logo: 'https://img.yzcdn.cn/weapp/wsc/jda6QN.png'
  },
  theme: {
    themeClass: 'th0',
    isFantasy: false,
    themeFetched: false,
    type: 0
  },
  isMultiStore: false,
  autoEntryStore: 0,
  soldOutRecommend: false,
  openHideStore: 0,
  multiStoreSwitch: false,
  hideTopBar: 0,
  hasBase: false,
  logo: '',
  shop_name: '',
  shop_type: 0,
  kdt_id: 0,
  service: {},
  security: {},
  wechat_sync_shopping_list: 0,
  isServiceDue: false,
  virtualGoodsCannotWePay: false,
  offlineId: 0,
  chainStoreInfo: {},
  shop_business_isopen: true,
  suspend_message: ''
};
