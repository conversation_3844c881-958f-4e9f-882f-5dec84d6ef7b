import storage from '@youzan/captain-weapp/dist/packages/utils/storage';
import { getExtSync } from 'shared/utils/browser/ext-config';

const kdtIdData = {
  // 微商城小程序和 originKdtId 一致
  current: 0,
  // 小程序店铺 kdtid
  originKdtId: getExtSync('kdtId'),
};
const KDT_ID_KEY = 'app:kdt_id';

let isReadedFromStorage = false;

function ensureReadStorage() {
  return new Promise((resolve) => {
    if (isReadedFromStorage) {
      resolve();
      return;
    }

    const extKdtId = kdtIdData.originKdtId;
    storage
      .getAsync(KDT_ID_KEY)
      .then(({ current = 0 }) => {
        // 没有的话默认用总的
        kdtIdData.current = current || extKdtId;
      })
      .catch(() => {
        kdtIdData.current = extKdtId;
      })
      .finally(() => {
        // 只读取一次
        isReadedFromStorage = true;
        resolve();
      });
  });
}

function setToStorage(kdtId) {
  kdtIdData.current = kdtId;
  storage.setAsync(
    KDT_ID_KEY,
    {
      current: kdtId,
    },
    { expire: 0.02 } // 单位：天。这里是 30 分钟
  );
}

export function setKdtId(kdtId) {
  if (!kdtId) {
    return;
  }
  ensureReadStorage().then(() => setToStorage(kdtId));
}

export function getKdtId() {
  return new Promise((resolve) => {
    ensureReadStorage().then(() => {
      resolve({
        ...kdtIdData,
      });
    });
  });
}

export function getOriginKdtId() {
  return kdtIdData.originKdtId;
}
