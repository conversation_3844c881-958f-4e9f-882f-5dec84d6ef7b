import get from '@youzan/weapp-utils/lib/get';
import Event from '@youzan/weapp-utils/lib/event';
import teeEvent from '@youzan/tee-event';
import * as request from 'shared/utils/request';

import store from '../store';
import { getAppLifeVale, removeAppLifeValue } from '../utils';
/* eslint-disable-next-line */
import { getKdtId, setShopInfo } from './index';
import {
  invokeEnterMultiStore,
  getOfflineId,
  setOfflineId as setCurrentOfflineId,
  readOfflineData,
  saveOfflineIdRecord,
  readMultiStoreLocation,
} from '@youzan/shop-core-tee';

const { state, commit } = store;

const TriggerNameMap = {
  fetchShopInfo: 'app:fetchshopinfo',
  fetchedShopInfo: 'app:fetchedshopinfo',
  initNoStore: 'app:init:nostoreid',
};

const resolveMultiStoreConfig = (res) => {
  const app = getApp();

  app.globalData.fetchedShop = true;
  if (!res) {
    app.trigger(`${TriggerNameMap.fetchShopInfo}:fail`);
    app.trigger(TriggerNameMap.fetchedShopInfo);
    return;
  }

  const {
    auto_entry_store,
    is_multi_store,
    offline_id,
    sold_out_recommend,
    open_hide_store,
  } = res;
  const shopInfo = {
    isMultiStore: is_multi_store,
    soldOutRecommend: sold_out_recommend,
    offlineId: offline_id,
    autoEntryStore: auto_entry_store,
    openHideStore: open_hide_store,
  };

  // 非多网点情况下，把网点ID删除，否则由于缓存会导致（网点改变判断）对比失效
  if (!shopInfo.isMultiStore) {
    delete shopInfo.offlineId;
  }

  setOfflineId(shopInfo.offlineId);
  app.setShopInfo(shopInfo);
  if (shopInfo.isMultiStore && !app.getOfflineId()) {
    app.trigger(TriggerNameMap.initNoStore);
    teeEvent.trigger(TriggerNameMap.initNoStore);
  }

  app.trigger(`${TriggerNameMap.fetchShopInfo}:success`);
  app.trigger(TriggerNameMap.fetchedShopInfo);
};

/**
 * 保存网点
 */
const saveOfflineId = (currentOfflineId) => {
  return saveOfflineIdRecord(getKdtId(), currentOfflineId);
};

/**
 * 数据转换: 将标准的多网点数据格式转为 wsc 内特殊格式
 */
const trasformOfflineDataInWSC = (offlineData) => {
  // 如果已经转换过直接返回
  if (offlineData.is_opening !== undefined) {
    return offlineData;
  }

  const {
    id = 0,
    phone = {},
    name = '',
    images = [],
    address = {},
    businessTimeSettingString = '',
    opening = false,
  } = offlineData;

  const { areaCode = '', localNumber = '' } = phone;
  const {
    city = '',
    district = '',
    lat = 0,
    lng = 0,
    province = '',
  } = address;

  return {
    id,
    phone1: areaCode,
    phone2: localNumber,
    name,
    image: images,
    province,
    city,
    area: district,
    address: (address && address.address) || '',
    lat,
    lng,
    // eslint-disable-next-line camelcase
    business_hours_advanced_text: businessTimeSettingString,
    // eslint-disable-next-line camelcase
    is_opening: opening ? '1' : '0',
  };
};

/**
 * 获取网点信息
 */
export const getStore = () => {
  return readOfflineData(getKdtId(), getOfflineId()).then((res) => {
    // 数据转换
    res = trasformOfflineDataInWSC(res);

    if (Array.isArray(res.image) && res.image[0]) {
      res.image = res.image[0];
    } else {
      res.image = get(state.shop, 'base.logo');
    }
    return res;
  });
};

const initMultiStore = ({ store: config }) => {
  const shareId = getAppLifeVale('current_share_offline_id') || 0; // 通过分享进入页面时带的网点ID，级别最高

  // 分享的情况
  if (shareId) {
    config.offline_id = shareId;
    removeAppLifeValue('current_share_offline_id'); // 移除分享的ID
  }
  // 开始进网点
  invokeEnterMultiStore({ targetOfflineId: +shareId })
    .then((offlineId) => {
      config.offline_id = offlineId;

      if (offlineId) {
        const location = readMultiStoreLocation();
        // 定位成功时, 更新用户信息
        if (location) {
          commit(
            'UPDATE_USER_INFO',
            Object.assign(state.userInfo, {
              poi: {
                location: { lat: location.latitude, lng: location.longitude },
              },
            })
          );
        }
      }

      // 进网点成功
      resolveMultiStoreConfig(config);
    })
    .catch((err) => {
      console.error('多网点模块执行错误', err);
    });
};

export function setOfflineId(offlineId, cb, options = {}) {
  const app = getApp() || {};
  const lastOfflineId = getOfflineId();

  /**
   * 修改请求库中的网点 ID
   */
  request.updateShop({ offlineId });

  if (
    (offlineId || offlineId === 0) &&
    lastOfflineId !== offlineId &&
    +lastOfflineId !== +offlineId
  ) {
    return setCurrentOfflineId(offlineId).then(() => {
      state.shop.offlineId = offlineId;
      app.trigger && app.trigger('app:offlineId:change', cb, options);

      return getStore().then((store) => setShopInfo({ store }));
    });
  }
  // 切换网点未改变
  cb && cb(true);
  return Promise.resolve();
}

export function listenMultiStoreResolved() {
  Event.on('app:multi:store:resolved', initMultiStore);
}

export function getOfflineData() {
  return get(state, 'shop.store', {});
}

export { getOfflineId, saveOfflineId };
