import Event from '@youzan/weapp-utils/lib/event';
import TeeEvent from '@youzan/tee-event';
import get from '@youzan/weapp-utils/lib/get';
import args from '@youzan/weapp-utils/lib/args';
import * as request from 'shared/utils/request';
import {
  checkEduShop,
  checkOnlineBranchStore,
  checkChainStore,
  checkRetailShop,
} from '@youzan/utils-shop';
import {
  enterShopApollo,
  accordSkyLogger,
  autoEnterShop,
  isChainStoreSyncInWX as teeIsChainStoreSyncInWX,
} from 'common-api/multi-shop/multi-shop-redirect';

import store from '../store';
// eslint-disable-next-line import/no-cycle
import { updateKdtId, getKdtId, fetchLaunchJsonCallback } from './index';
import { getOriginKdtId } from './kdt-id';
import { getRootKdtId, getCurrentKdtId } from '@youzan/shop-core-tee';
import {
  isMultiOnlineShop,
  isRootShop,
  shouldEnterChainShop,
  isEduChainStore,
  isChainStore,
  isOfflineShop,
  getEnterRoute,
  glEnterShopBlockCheck,
} from './chain/chain-utils';
import { EVENT_TRIGGER_KEY } from '@youzan/tee-chain-store';
// eslint-disable-next-line import/no-cycle
import { setEnterCacheConfig } from './chain/helper';
// eslint-disable-next-line import/no-cycle
export {
  setSwitchShopRangeCache,
  forceUseHqTabbar,
  forceShowHome,
  getEnterCacheConfig,
  getWxPage,
  setEnterCacheConfig,
  cacheProxyUpdateEmit,
  isHqEnterShop,
  isWxProxyDone,
  waitForProxyWxJumpDone,
  isProxyUpdateKdtId,
  notUpdateKdtIdInChain,
  getSwitchShopRangeCache,
  isChainHqShop,
  isHqOrHomeEnter,
} from './chain/helper';

export { featureJump, retailShelfJump } from './async-chain/proxy-jump';

const { state } = store;

// 标记是否 reLaunch 了
let isReLaunch = false;
const p = 'packages';
// 获取当前页面信息
// 选择店铺之后调回原来页面
const getRedirectUrl = () => {
  let url = `/${p}/home/<USER>/index`;
  const pages = getCurrentPages();

  if (pages.length > 0) {
    const { route, options = {} } = pages[pages.length - 1];
    url = args.add(`/${route}`, options);
  }
  return encodeURIComponent(url);
};

const reLaunch = () => {
  isReLaunch = true;
  // eslint-disable-next-line @youzan/dmc/wx-check
  wx.reLaunch({
    url: `/${p}/shop-select/chain-store/shopselect/index?dbKey=location&from=new&redirectUrl=${getRedirectUrl()}`,
  });
};

export function getCurrentPageOption() {
  const pages = getCurrentPages();

  if (pages.length === 0) {
    // pages为空时表明page未初始化, 需要从Launch options中取path、query参数
    const { path, query } = wx.getLaunchOptionsSync();
    return { path, query };
  }

  const { route: path, options: query } = pages[pages.length - 1];

  // 空白页进店则取globalData存的 path、query
  if (path === 'pages/common/blank-page/index') {
    return (getApp().globalData || {}).enterShopInfo;
  }

  return {
    path,
    query,
  };
}

export function updateChainStoreInfo() {
  const { shopMetaInfo = {} } = state.shop;
  // 连锁网店
  if (isMultiOnlineShop(shopMetaInfo)) {
    if (!isRootShop(shopMetaInfo)) {
      // 页面可监听这个事件进行一些业务处理
      // 这个事件同时会触发云定制事件'ecloud:chain-store:kdtId:update'
      getApp().trigger('app:chainstore:kdtid:update', {
        kdtId: shopMetaInfo.kdt_id,
      });
    }
    // 总部也会触发
    getApp().trigger('app:chainstore:change:update', {
      kdtId: shopMetaInfo.kdt_id,
    });
  }
}

/**
 * 判断当前店铺是否是连锁
 * @returns
 */
export function isChainStoreSync() {
  const { shopMetaInfo = {} } = state.shop;
  // 兼容单店升级连锁过渡场景,后期不考虑此场景可以删除
  if (Object.keys(shopMetaInfo).length) {
    return checkChainStore(shopMetaInfo);
  }
  return teeIsChainStoreSyncInWX();
}

export function setChainStoreInfo({ shopMetaInfo, shopInfo }) {
  /**
   * 店铺类型判断使用
   * app.getShopInfo().then((res={}) => {
   *   let isRetailShop = res.shopType?.isRetailShop
   * })
   */
  const shopType = {
    isRetailShop: checkRetailShop(shopMetaInfo), // 是否是零售店
    isRootShop: isRootShop(shopMetaInfo), // 是否是总部
    isChainStore: isChainStore(shopMetaInfo), // 是否是连锁店
    isMultiOnlineShop: isMultiOnlineShop(shopMetaInfo),
    isEduChainStore: isEduChainStore(shopMetaInfo),
    isOnline: shopMetaInfo.online_shop_open,
    isEduShop: checkEduShop(shopMetaInfo),
    isUnitedHqStore: false, // 无大网店店铺模型,可直接返回false
    isPureOffineShop: isOfflineShop(shopMetaInfo),
  };

  if (shopType.isChainStore && shopType.isOnline) {
    setEnterCacheConfig('lastOnlineKdtId', shopMetaInfo.kdt_id);
  }

  return {
    chainStoreInfo: {
      showBtn: !!shopMetaInfo.show_retail_switch_btn,
      showUserLocation: shopMetaInfo.show_user_location, // 是否展示用户位置
      onlineShopVisitModel: shopMetaInfo.online_shop_visit_model, // 个性化模式
      visitSecondConfirm: shopMetaInfo.visit_second_confirm, // 个性化模式-进店二次确认(店铺列表)
      logo: shopInfo.logo,
      name: shopMetaInfo.shop_name,
      rootKdtId: shopMetaInfo.root_kdt_id,
      ...shopType, // 为了兼容之前的代码
    },
    shopType,
  };
}

// fork 多网点逻辑，实现连锁店的开关逻辑
function initChainStore({ shopMetaInfo, isFromBlankPageOrOnLaunch }) {
  const { route } = getEnterRoute();
  Promise.all([
    shouldEnterChainShop(shopMetaInfo),
    glEnterShopBlockCheck(route),
  ])
    .then((result) => {
      const [shouldEnterRes, ignoreCommonWebview] = result;
      const { needEnterShop, path, query } = shouldEnterRes;
      if (needEnterShop) {
        if (ignoreCommonWebview) return;
        const redirectUrl = args.add('/' + path, query);
        // 仅小程序启动或空白页调用updateKdtId时需要获取url上的参数进店
        autoEnterShop(
          isFromBlankPageOrOnLaunch
            ? {
                ...query,
                redirectUrl,
                logTag: 'global',
              }
            : { redirectUrl, logTag: 'global' }
        ).then((newKdtId) => {
          if (newKdtId) {
            // 这句不知道干啥的，零售那边用的
            getApp().dontUpdateKdtIdByServer();
          }
        });
      } else {
        // 更新店铺信息
        enterShopApollo('updateChainStoreInfo').then((isTrue) => {
          if (isTrue) {
            const { shopMetaInfo = {}, shopInfo } = getApp().getShopInfoSync();
            updateChainStoreInfo(shopMetaInfo, shopInfo);
          }
        });
      }
    })
    .catch(() => {
      accordSkyLogger({
        err: `[wx] 全局进店处理失败`,
      });
    });
}

export function getHQKdtId() {
  const { shop } = state;

  return (
    get(shop, 'chainStoreInfo.rootKdtId') ||
    shop.kdt_id ||
    getRootKdtId() ||
    null
  );
}

// 当前是否总部
export function isShopHq() {
  if (!teeIsChainStoreSyncInWX()) return false;
  return getCurrentKdtId() === getHQKdtId();
}

export function setIsReLaunch(value) {
  isReLaunch = value;
}

// 检测是否需要重定向
// 微信有个坑，如果 reLaunch 过程中有其他的路由方法被调用，如 redirect, navigate，可能会导致 reLaunch 失效
export function checkReLaunchShopSelect() {
  if (isReLaunch) {
    reLaunch();
  }
}

let currentYzUserId = '';
/**
 * 插秧未来大客会员进店临时兼容代码，后续会下线
 * 用户授权手机号后查询当前是否处于会员归属店铺，不在则切换店铺
 */
export function redirectToMemberShopWhenAuthorize(app) {
  const hqKdtId = +getOriginKdtId();
  const kdtIdList = [90957577, 54731005, 42865412];

  if (kdtIdList.indexOf(hqKdtId) === -1) {
    return;
  }

  app.on('app:token:success', (token) => {
    const { yzUserId } = token;

    if (!currentYzUserId) {
      currentYzUserId = yzUserId;
    }

    if (currentYzUserId === yzUserId) {
      return;
    }

    currentYzUserId = yzUserId;

    const currentKdtId = +getKdtId();

    request
      .node({
        path: '/wscshop/weapp/getMemberStore.json',
      })
      .then((res) => {
        const { kdtId, shopMetaInfo } = res;
        if (
          kdtId &&
          +kdtId !== currentKdtId &&
          checkOnlineBranchStore(shopMetaInfo)
        ) {
          let redirectUrl = decodeURIComponent(getRedirectUrl());
          redirectUrl = args.add(redirectUrl, { subKdtId: kdtId });
          redirectUrl = args.remove(redirectUrl, 'shopAutoEnter');

          updateKdtId(kdtId, false, {
            mark: '[chain-store.js]redirectToMemberShopWhenAuthorize',
          });

          // fetch 到了global信息之后再跳转
          Event.once(
            'shop:info:fetch:success',
            () => {
              // eslint-disable-next-line @youzan/dmc/wx-check
              wx.reLaunch({
                url: redirectUrl,
              });
            },
            this
          );
        }
      });
  });
}

const APP_CHAIN_STORE_RESOLVED_KEY = 'app:chain:store:resolved';
// 中台化切换原生注册事件会丢失
export function createChainStoreEvent() {
  Event.off(APP_CHAIN_STORE_RESOLVED_KEY, initChainStore);
  Event.on(APP_CHAIN_STORE_RESOLVED_KEY, initChainStore);
}

// 定制店铺处理
export function checkSpecialShopDZ() {
  // 新光天地特殊处理
  return [19171281].includes(+getCurrentKdtId());
}

// 进店回调
TeeEvent.on(EVENT_TRIGGER_KEY.a_enter_c, updateChainStoreInfo);
// 进店店铺信息回调
TeeEvent.on(EVENT_TRIGGER_KEY.shop_c, fetchLaunchJsonCallback);
