import THEME_CLASS_MAP, { defaultThemeColors } from 'themes/config';
import * as request from 'shared/utils/request';
import { checkSingleStore, checkHqStore } from '@youzan/utils-shop';
import get from '@youzan/weapp-utils/lib/get';
import Event from '@youzan/weapp-utils/lib/event';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import {
  initShopCore,
  updateKdtId as setCurrentKdtId,
  getCurrentKdtId,
  getOfflineId as getCurrentOfflineId,
  getRootKdtId,
} from '@youzan/shop-core-tee';
import store from '../store';
import { defaultShopConfig } from './config';
import { setKdtId } from './kdt-id';
import { getAppLifeVale } from '../utils';
import { enterShopApollo } from 'common-api/multi-shop/multi-shop-redirect';
import { shouldEnterChainShop } from './chain/chain-utils';
// eslint-disable-next-line import/no-cycle
import {
  createChainStoreEvent,
  cacheProxyUpdateEmit,
  getEnterCacheConfig,
  forceUseHqTabbar,
  setChainStoreInfo,
} from './chain-store';
import { isChainStoreSyncInWX } from '@youzan/tee-chain-store';
import { setShowTopBarInfo } from './chain/top-bar-helper';

export { getLocationAuth, toExistPage } from '@youzan/shop-tee-shared';

const { state, commit } = store;

// 处理所有的店铺基础信息
const resolveShopInitData = (res, { isFromBlankPageOrOnLaunch = false }) => {
  createChainStoreEvent();
  const { _fromCache: fromCache } = res;
  const shopState = {};
  /* eslint camelcase: 0 */
  const {
    shop_info = {},
    offline_data = {},
    copyright_data = {},
    mall_shop_config_data = {},
    business_data,
    is_hide_power_by,
    is_stop_we_pay_for_vitual,
    shop_theme_data = {},
    shop_meta_info,
    shop_business_config = {},
    shop_nav_data = {},
    crypto_info = {},
  } = res;

  // 全店风格合并至shopInfo
  // 多网点开关
  shopState.isMultiStore = offline_data.is_multi_store;
  shopState.autoEntryStore = offline_data.auto_entry_store;
  shopState.soldOutRecommend = offline_data.sold_out_recommend;
  shopState.offlineSeparatePrice = offline_data.offline_separate_price;
  shopState.openHideStore = +mall_shop_config_data.open_hide_store;
  shopState.multiStoreSwitch =
    +mall_shop_config_data.open_multi_store_switch === 1;
  shopState.isDecorateFollowHq =
    +mall_shop_config_data.subshop_decorate_independent === 0;
  shopState.hideTopBar = +mall_shop_config_data.hide_top_bar;
  shopState.hasBase = true;

  shopState.nav = shop_nav_data.data || {};

  // 兼容老数据
  Object.assign(shopState, shop_info);

  shopState.base = { ...shop_info };
  shopState.isServiceDue =
    shop_info.service && shop_info.service.status !== 'WEAPP_AVAILABLE';
  shopState.security = {};
  shopState.security.joined = +shop_info.security.joined !== 0;
  shopState.virtualGoodsCannotWePay = is_stop_we_pay_for_vitual;

  Object.assign(state.shop, shopState);
  shopState.copyright = {
    ...(state.shop.copyright || {}),
    ...mapKeysCase.toCamelCase(copyright_data),
    isCustomized: !!copyright_data.is_logo_customized,
    isWeixinSelfHoldPay: res.is_weixin_self_hold_pay,
    hidePoweredBy: copyright_data.hide_powered_by || is_hide_power_by,
  };
  shopState.copyright.isCustomized &&
    (shopState.copyright.logo =
      copyright_data.customized_logo || defaultShopConfig.copyright.logo);

  shopState.config = mall_shop_config_data;

  // 店铺营业时间和营业状态
  shopState.shop_business_isopen = shop_business_config.is_open;
  shopState.suspend_message =
    shop_business_config.next_open_time_note ||
    shop_business_config.suspend_message;

  // business id 实际上只是在公共版使用，按照原来的逻辑，如果后端异常有可能导致 promise 挂起，事件监听挂起
  shopState.im =
    business_data.status < 0
      ? {}
      : {
          businessId: business_data.business_id,
          areaCode: business_data.area_code, // 电话区号
        };

  shopState.vip = {
    isBigShop: is_hide_power_by,
  };

  shopState.cryptoInfo = crypto_info;

  const themeClass = THEME_CLASS_MAP[shop_theme_data.type] || 'th0';
  shopState.theme = {
    themeClass,
    isFantasy: themeClass === 'th11',
    themeFetched: true,
    type: shop_theme_data.type,
    colors: shop_theme_data.colors || defaultThemeColors,
  };

  shopState.shopMetaInfo = shop_meta_info;

  const chainStore = setChainStoreInfo({
    shopMetaInfo: shop_meta_info,
    shopInfo: shop_info,
    isFromBlankPageOrOnLaunch,
  });
  // eslint-disable-next-line no-use-before-define
  setShopInfo({ ...shopState, ...chainStore });

  const chainStoreInfo = get(chainStore, 'chainStoreInfo', {});
  setShowTopBarInfo({
    shopMetaInfo: shop_meta_info,
    showBtn: get(chainStoreInfo, 'showBtn', false),
    cb: () => {},
    setGlobal: true,
    otherInfo: {
      isMultiOnlineShop: get(chainStoreInfo, 'isMultiOnlineShop', false),
      isRootShop: get(chainStoreInfo, 'isRootShop', false),
    },
  });

  function emitFunc() {
    // 多网点/多网店逻辑额外通过监听这个事件处理
    Event.trigger('app:multi:store:resolved', {
      store: offline_data,
      fromCache,
    });
    Event.trigger('app:chain:store:resolved', {
      shopMetaInfo: shop_meta_info,
      shopInfo: shop_info,
      isFromBlankPageOrOnLaunch,
    });

    Event.trigger('shop:copyright:resolved', shopState.copyright);
    Event.trigger('shop:nav:resolved', shopState.nav);
    Event.trigger('shop:config:resolved', shopState.config);
    Event.trigger('shop:im:resolved', shopState.config);
    Event.trigger('shop:vip:resolved', shopState.vip);
    Event.trigger('shop:theme:resolved', shopState.theme);
    Event.trigger('shop:info:change', shopState);
    Event.trigger('shop:info:fetch:success');
  }

  // 进店劫持
  if (getEnterCacheConfig('isProxyUpdateKdtId')) {
    cacheProxyUpdateEmit(emitFunc);
  } else {
    emitFunc();
  }

  return shopState;
};

// launch.json 请求参数提取,进店内部launch.json于进店接口合并,需要用到这些参数
export function getShopInfoParams() {
  return {
    weapp_type: state.config.weappType,
    share_offline_id: getAppLifeVale('current_share_offline_id') || '',
    not_enter_shop: '1', // 不需要通过launch.json接口进店
    global_theme_version: 1, // 全店风格收拢，后端接口会返回颜色变量，接口上需要区分新旧全店风格
  };
}

// launch.json 请求参数提取,进店内部launch.json于进店接口合并,需要用到这些参数
export function fetchLaunchJsonCallback(res = {}) {
  const {
    fromCache,
    cacheData = {},
    enable_http2 = false,
    enable_cdn = false,
    mall_shop_config_data,
  } = res;

  const app = getApp();
  if (app && app.globalData) {
    app.globalData.enableHttp2 = enable_http2;
    app.globalData.enableCdn = enable_cdn;
    app.globalData.mall_shop_config_data = mall_shop_config_data;
  }
  // app.globalData.enableHttp2 = true;

  // 如果响应数据来自缓存，则重新拉取最新的数据更新缓存并重新设置state
  // 这里要注意就是进店的情况，进店会请求两次launch.json，一次总店，一次分店。
  // 所以这里会有个两个launch.json事件，可能会出现分店的launch.json先执行，总店的后执行
  if (fromCache) {
    cacheData.refreshPromise.then((res) => {
      // 如果state已经设置过kdtid
      // && 设置的是分店（证明已经进店）
      // && kdtid和请求返回的kdtid不一致
      // && 请求返回的是总部
      // 就不用再更新state了。。
      const { shop_meta_info = {} } = res;
      const isBranchStore =
        +get(state, 'shop.shopMetaInfo.shop_role', null) === 2;
      if (
        +getKdtId() &&
        isBranchStore &&
        +getKdtId() !== shop_meta_info.kdt_id &&
        shop_meta_info.shop_role === 1
      ) {
        return;
      }
      return res;
    });
  }

  // 将shopState的数据绑到state.shop上
  return {
    ...res,
    _fromCache: fromCache,
  };
}

function fetchInitData() {
  const query = getShopInfoParams();

  const requestConfig = {
    skipShopInfo: true,
    cache: true,
    needRefresh: true,
    expire: 30,
  };

  return fetchLaunchJsonWidthRetry(() => {
    return request.node({
      path: 'wscshop/weapp/launch.json',
      data: { ...query, need_cnd: true },
      config: requestConfig,
    });
  })
    .then((res = {}) => {
      return fetchLaunchJsonCallback(res);
    })
    .catch((res) => {
      Event.trigger('shop:info:fetch:fail');
      return Promise.reject(res);
    });
}

function beforeUpdateKdtId(kdtId, lastKdtId) {
  // 强制转成整数
  kdtId = +kdtId;

  if (!kdtId) {
    return;
  }

  if (kdtId === +lastKdtId) {
    request.updateShop({ offlineId: getOfflineId(), kdtId });
    return;
  }

  setKdtId(kdtId);
  commit('UPDATE_SHOP_DATA', { kdtId });

  request.updateShop({ kdtId });
  Event.trigger('shop:kdt_id:change', kdtId);
}

function fetchLaunchJsonWidthRetry(req, retry = 3) {
  return req().catch((res) => {
    if (retry > 0) {
      return fetchLaunchJsonWidthRetry(req, retry - 1);
    }
    return Promise.reject(res);
  });
}

export function getKdtId() {
  return getCurrentKdtId() || get(state, 'shop.kdtId', null);
}

initShopCore({
  request: fetchInitData,
  pick: (res) => {
    const { type: themeType, colors } = res.shop_theme_data;

    return {
      shop: mapKeysCase.toCamelCase({
        ...res.shop_meta_info,
        logo: get(res, 'shop_info.logo'),
      }),
      multiStore: mapKeysCase.toCamelCase(res.offline_data),
      theme: {
        type: themeType,
        colors: colors || defaultThemeColors,
      },
    };
  },
  onShopInfoChange: (_, origin, launchQuery) =>
    resolveShopInitData(origin, launchQuery),
  onKdtIdChange: (kdtId, lastKdtId) => beforeUpdateKdtId(kdtId, lastKdtId),
});

/**
 * 更新kdtId，中途会触发更新shop，第二个参数为true强制更新kdtId
 * @param {Number} kdtId
 * @param {Boolean} force
 * @param {Object} launchQuery
 * @param {string} launchQuery.mark 用于埋点日志 https://doc.qima-inc.com/pages/editpage.action?pageId=381750125
 * @returns {Promise} 店铺信息Promise
 */
export function updateKdtId(kdtId = 0, force = false, launchQuery = {}) {
  // 连锁首次进店前会锁住更新
  if (!(+kdtId > 0)) {
    return;
  }

  return setCurrentKdtId(kdtId, {
    ...launchQuery,
    force,
  });
}

export function setShopInfo(shopInfo = {}) {
  // const shop = state.shop;
  commit('UPDATE_SHOP_DATA', shopInfo);
  // state.shop = Object.assign({}, shop, shopInfo);

  /**
   * 店铺信息改变要改变页面内用到的地方，后边可以通过 watch 在自己页面内完成
   */
  if (!getEnterCacheConfig('isProxyUpdateKdtId')) {
    Event.trigger('shop:info:change', shopInfo);
  }
  return state.shop;
}

function resolveShopData(key) {
  return () => {
    return new Promise((resolve) => {
      const item = state.shop[key] || {};
      if (!state.shop.hasBase || Object.keys(item).length === 0) {
        Event.once(`shop:${key}:resolved`, () => resolve(state.shop[key]));
      } else {
        resolve(state.shop[key]);
      }
    });
  };
}

// 连锁底部导航数据处理
const chainStoreNavConfig = (nav) => {
  if (!nav) return {};
  if (!isChainStoreSyncInWX()) return nav;

  const cache = getEnterCacheConfig('tabbarNavCacheMap');
  // 进店进总部强制使用,总部导航
  if (forceUseHqTabbar()) {
    nav.list = mapKeysCase
      .toSnakeCase(cache[getRootKdtId()].navList)
      .map((i) => {
        i.page_path = i.page_path.includes('home/feature/index')
          ? i.page_path
          : i.page_path.replace(/^\/|(\?.*)$/g, '');
        return i;
      });
  }
  return nav;
};

export const getShopTheme = resolveShopData('theme');
export const getHiddenPowerBy = resolveShopData('vip');
export const getShopConfigData = resolveShopData('config');
export const getCopyright = resolveShopData('copyright');
export const getNavConfigSync = async () => {
  const nav = await resolveShopData('nav');
  return chainStoreNavConfig(nav);
};

export const getNavConfig = () => {
  const { nav } = state.shop;
  return chainStoreNavConfig(nav);
};

function handleShopMetaInfo(state, shopMetaInfo = {}) {
  return new Promise((resolve) => {
    if (checkSingleStore(shopMetaInfo)) {
      resolve(state.shop);
    } else if (checkHqStore(shopMetaInfo)) {
      const { needEnterShop } = shouldEnterChainShop(shopMetaInfo);
      enterShopApollo('getShopInfo').then((isTrue) => {
        if (isTrue && needEnterShop) {
          // 连锁总部,等待进店结果
          getApp().once('app:chainstore:kdtid:update', () =>
            resolve(state.shop)
          );
        } else {
          resolve(state.shop);
        }
      });
    } else {
      resolve(state.shop);
    }
  });
}

export const getShopInfo = () => {
  return new Promise((resolve) => {
    const { shopMetaInfo, hasBase } = state.shop || {};
    if (hasBase) {
      resolve(handleShopMetaInfo(state, shopMetaInfo));
    } else {
      Event.once('shop:info:fetch:success', () => {
        const { shopMetaInfo } = state.shop || {};
        resolve(handleShopMetaInfo(state, shopMetaInfo));
      });
    }
  });
};

// 等待进店完成。这是 promise 只会触发一次，如果要根据 kdtid 变化做相应的处理，需要监听其他事件
// 1. 单店直接 resolve
// 2. 连锁进店后 resolve
export function waitForEnterShop() {
  return new Promise((resolve) => {
    getShopInfo().then((shopInfo) => {
      if (+get(shopInfo, 'shopMetaInfo.shop_role') !== 1) {
        resolve(shopInfo);
      } else {
        getApp().once('app:chainstore:kdtid:update', () => {
          resolve(state.shop);
        });
      }
    });
  });
}

export function getShopInfoSync() {
  return isEmpty(state.shop) ? { base: {}, security: {}, nav: {} } : state.shop;
}

export function getShopConfigDataSync() {
  return get(state, 'shop.config', {});
}

export function getOfflineId() {
  return getCurrentOfflineId() || get(state, 'shop.offlineId', '');
}

export function getOfflineSeparatePrice() {
  return get(state, 'shop.offlineSeparatePrice', 0);
}

/**
 * 防止调用报错，实际上不应该调用到这里
 * 应该引用 mutil-store，并使用其中的方法，覆盖该方法
 */
export function setOfflineId() {}
export function getHQKdtId() {
  return null;
}

export const getCryptoInfo = () => {
  return state.shop.cryptoInfo || {};
};

/**
 * 多语言场景设置顶部导航标题
 * @param {*} text
 */
export const setLangNavTitle = async (text) => {
  function setTitle(val) {
    wx.setNavigationBarTitle({
      title: val,
    });
  }
  const app = getApp();
  if (!app.LanguageCore || !(await app.LanguageCore.needTranslate(text)))
    return setTitle(text);
  app.LanguageCore.addLang({
    text,
    callback: (val) => {
      setTitle(val);
    },
  });
};
