import * as request from 'shared/utils/request';
import { getExtSync } from 'shared/utils/browser/ext-config';
import adaptorComponents from 'constants/adaptor-components';
import { prefetch } from '@youzan/biz-center';

const URL_MAP = {
  user: '/wscuser/membercenter/',
  other: '',
};

export function fetchFirstScreenData(params) {
  return request.node({
    path: `${URL_MAP.user}init-data.json`,
    method: 'get',
    data: {
      ...params,
      currentKdtId: params.onlineKdtId,
      needConsumptionAboveCoupon: 1,
    },
    query: {
      kdt_id: params.kdtId,
    },
  });
}

export function fetchGlobalData(params) {
  return request.node({
    path: `${URL_MAP.user}global.json`,
    method: 'get',
    data: {
      ...params,
      adaptorComponents: adaptorComponents.join(','),
    },
    query: {
      kdt_id: params.kdtId,
    },
  });
}

export function fetchPromise(params) {
  return Promise.all([fetchGlobalData(params), fetchFirstScreenData(params)]);
}

export function fetchPromiseForWsc(app) {
  const extConfig = getExtSync() || {};
  // 只针对单店
  if (!app.isChainStoreSync() && extConfig.kdtId && extConfig.userVersion) {
    return fetchPromise({
      kdtId: extConfig.kdtId,
      version: extConfig.userVersion,
      onlineKdtId: extConfig.kdtId,
    });
  }
}

export function fetchUserInfo() {
  return request.node({
    path: '/retail/h5/user/memberInfo.json',
    method: 'get',
  });
}

function fetchUcConf(kdtId) {
  return request.node({
    path: `${URL_MAP.user}user-center-config.json`,
    method: 'get',
    data: {
      kdtId,
      platform: 2,
    },
  });
}

function fetchUcContact(kdtId) {
  return request.node({
    path: `${URL_MAP.user}contact.json`,
    method: 'get',
    data: {
      kdt_id: kdtId,
    },
  });
}

function fetchBrandFeatureDetail(kdtId) {
  return request.node({
    path: '/wscdeco/brand-feature-detail.json',
    method: 'get',
    data: {
      stage: 16,
      hadEnterShop: true,
      kdt_id: kdtId,
      labelType: 'usercenter,freeMemberCenter,payMemberCenter',
    },
  });
}

function fetchRtPromise(params) {
  const { kdtId } = params;
  return Promise.all([
    fetchUserInfo(),
    fetchUcConf(kdtId),
    fetchUcContact(kdtId),
    +kdtId,
  ]);
}

function handleRetailTemplate(app, kdtId, type) {
  fetchBrandFeatureDetail(kdtId).then(({ usercenter }) => {
    const uCset = !!usercenter;
    app.globalData.ucTempType = uCset ? 1 : type;
  });
}

export function fetchPromiseForRetail(app, globalKdtId) {
  // 只针对连锁
  if (app.isChainStoreSync()) {
    const hqKdtId = app.getHQKdtId() || globalKdtId;
    prefetch.getMiniappPrefetchData('retailTemplate').then((data) => {
      handleRetailTemplate(app, hqKdtId, data);
    });
    return app.waitForEnterShop().then(() => {
      const kdtId = app.getKdtId();
      return fetchRtPromise({ kdtId });
    });
  }
}
