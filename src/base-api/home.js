import { getExtSync } from 'shared/utils/browser/ext-config';
import { prefetch } from '@youzan/biz-center';
import request from '@youzan/tee-biz-request';

import { isHqEnterShop, debug } from '@youzan/tee-chain-store';
// 缓存scene解析
const SCENE_CODE_CACHE = {};
const CACHE_TAB_BAR_PROMISE = {};

const URL_MAP = {
  h5: 'https://h5.youzan.com/',
  other: '',
};

const showStoreHotModal = () => {
  wx.showModal({
    content: '店铺太火爆啦，请稍后重试',
    confirmText: '刷新',
    cancelText: '关闭',
    success: (res) => {
      res.cancel && wx.exitMiniProgram();
      res.confirm && wx.restartMiniProgram();
    },
  });
};

const getKdtId = () => getExtSync('kdtId');

const prefetchData = (key, options) =>
  prefetch.getMiniappPrefetchData(key, options);

export function toPromise(fn) {
  return function (options) {
    return new Promise((resolve, reject) => {
      const result =
        fn &&
        fn({
          ...(options || {}),
          header: {
            'Extra-Data': JSON.stringify({
              is_weapp: 1,
              client: 'weapp',
              bizEnv: 'wsc',
              skip_sid: 1,
              version: getExtSync('userVersion'),
            }),
          },
          success(res) {
            // 首页装修接口被限流，给出提示，特定状态码 429
            if (
              res.statusCode === 429 &&
              options.url.includes('homepage-detail.json')
            ) {
              return showStoreHotModal();
            }
            if ((res.data || {}).code === 0) {
              resolve(res.data.data);
            } else {
              reject({
                data: res.data,
                statusCode: res.statusCode,
              });
            }
          },
          reject,
        });

      if (result && result.then) {
        result.then(resolve, reject);
      }
    });
  };
}

const wxRequest = toPromise(wx.request);

export function getHomeDetailPromise({ path, kdtId, hadEnterShop }) {
  return wxRequest({
    url: URL_MAP.h5 + 'wscdeco/homepage-detail.json',
    data: {
      stage: 16,
      // tee_route: path, 判断跳转用的，暂不需要
      check_multi_store: 1,
      fetchSepHqStoreDetail: isHqEnterShop() ? 1 : 0,
      close_chainstore_webview_limit: true,
      check_old_home: 1,
      hadEnterShop,
      kdt_id: kdtId || getKdtId(),
      version_control: JSON.stringify({
        use_native_feature_page: 1,
        feature_page_path: path,
      }),
    },
  }).catch((res) => {
    // 首页装修接口被限流，给出提示，特定状态码 429
    res.statusCode === 429 && showStoreHotModal();
  });
}

export function getHomeDetailPromisePreload(res) {
  return prefetchData('homeFeature', {
    //
  })
    .then((data) => {
      const { kdtId, homeDetailInfo } = res;
      // 此详情是进店请求内部包掉的
      if (homeDetailInfo) {
        homeDetailInfo.needEnterShop = false;
        return homeDetailInfo;
      }

      // 命中channel的错误，不存在数据
      if (!data || !data.components?.length) {
        return getHomeDetailPromise(res).catch((tmpErr) => {
          return Promise.reject({
            type: 'homeErr',
            err: tmpErr,
          });
        });
      }

      // 有拉到数据
      if (data.id) {
        data.isPreload = true;
      }
      // 小程序query的kdtId 是总店的情况下，需要重新拉取(连锁场景)
      const { shopMetaInfo: { kdtId: shopKdtId } = {} } = data;
      // 可能得情况如下，其实下面的逻辑就是连锁-预拉取返回网店首页的数据；
      // 1. 总部 总部
      // 2. 网店 总部
      // 3. 单店 单店
      if (+shopKdtId !== +kdtId) {
        return getHomeDetailPromise(res)
          .then((data) => {
            return data;
          })
          .catch((tmpErr) => {
            return Promise.reject({
              type: 'homeErr',
              err: tmpErr,
            });
          });
      }
      // node层处理完，把进店状态修改
      // ext里面的总店id 和 预拉取的数据kdtid 不一致表示进度成功了
      if (+getKdtId() !== +shopKdtId) {
        data.needEnterShop = false;
      }
      // prefetch-home
      debug('pho', data);
      return data;
    })
    .catch((err) => {
      debug('pho', err);
      // 并不是所有的错误都需要重新兜底首页预拉取数据，只有 getMiniappPrefetchData 报错才catch
      // 但像 此方法中 getHomeDetailPromise 正常返回，但接口返回了错误信息，这里会把某些错误吞掉，后续在主页包里拿不到真正的错误信息，是不应该继续兜底的
      if (err && err.type === 'homeErr') {
        return Promise.reject(err.err);
      }
      // 兜底一下首页预拉取
      return getHomeDetailPromise(res);
    });
}

/**
 * 处理scene数据
 * @param {*} kdt_id
 * @param {*} router
 * @param {*} param
 * @returns
 */
export const handleSceneCodeResult = (kdt_id, router, { pageData }) => {
  const { guestKdtId, shopAutoEnter } = pageData || {};

  const kdtId = guestKdtId || kdt_id;
  return { ...router, kdt_id: kdtId, shopAutoEnter };
};

/**
 * scene换取
 * @param {*} data 请求参数
 * @param {*} router 路由参数
 * @returns
 */
export const getWeappSceneCodeJson = (data, router) => {
  const cache = SCENE_CODE_CACHE[data.key];
  if (cache) return Promise.resolve(cache);
  return wxRequest({
    url: URL_MAP.h5 + 'v3/weapp/scene-code-v2.json',
    data,
  }).then(({ pageData }) => {
    SCENE_CODE_CACHE[data.key] = handleSceneCodeResult(data.kdt_id, router, {
      pageData,
    });
    return SCENE_CODE_CACHE[data.key];
  });
};

// 缓存
const AD_CACHE = {
  fetch: null,
  result: null,
};

function getCoverAdPromise() {
  // 此处改成 wxRequest, 暂时不依赖登录态，依赖登录态比较慢
  return wxRequest({
    url: URL_MAP.h5 + 'wscdeco/homepage-video-ad.json',
    data: {
      kdtId: getKdtId(),
    },
  }).then((res) => {
    AD_CACHE.result = res;
    return res;
  });
}

export function getCoverAdPromisePreload() {
  if (AD_CACHE.result) {
    return Promise.resolve(AD_CACHE.result);
  }

  if (AD_CACHE.fetch) {
    return AD_CACHE.fetch;
  }
  AD_CACHE.fetch = prefetchData('homeAd')
    .then((data) => {
      // 命中channel的错误，不存在数据
      if (!data) {
        return getCoverAdPromise().catch((tmpErr) => {
          return Promise.reject(tmpErr);
        });
      }

      AD_CACHE.result = data;
      return data;
    })
    .catch((err) => {
      console.log(err);
      // 兜底一下预拉取
      return getCoverAdPromise();
    });

  return AD_CACHE.fetch;
}

function getGlobalCustomLoading() {
  const key = 'global_custom_loading';
  return wxRequest({
    url: URL_MAP.h5 + 'wscshopcore/extension/configs.json',
    method: 'POST',
    data: {
      keys: [key],
      kdtId: getKdtId(),
    },
  }).then((data) => {
    const globalCustomLoading = data[key] || '{}';
    const { open, resource } = JSON.parse(globalCustomLoading);
    const type = 'image';
    wx.preloadAssets({
      data: [
        {
          type,
          src: resource,
        },
        { type, src: __wxConfig?.accountInfo?.icon },
      ].filter((item) => item.src),
    });
    return open ? resource : '';
  });
}

export function getGlobalCustomLoadingPromisePreload() {
  return new Promise((resolve) => {
    prefetchData('globalCustomLoading')
      .then((data) => {
        if (data === null || data === undefined) {
          resolve(getGlobalCustomLoading());
        } else {
          resolve(data);
        }
      })
      .catch(() => {
        resolve(getGlobalCustomLoading());
      });
  });
}

export function getBrandFeature(kdtId) {
  return request({
    path: '/wscdeco/brand-feature-detail.json',
    data: {
      stage: 16,
      hadEnterShop: true,
      kdt_id: kdtId,
      labelType: 'usercenter,freeMemberCenter,payMemberCenter',
    },
  });
}

// 获取底部导航
export const getWeappCustomNav = (kdtId = getApp().getKdtId(), refresh) => {
  if (CACHE_TAB_BAR_PROMISE[kdtId] && !refresh) {
    return CACHE_TAB_BAR_PROMISE[kdtId];
  }

  return Promise.all([prefetchData('tabBar'), prefetchData('shopStyle')]).then(
    ([tabBar, shopStyle]) => {
      if (tabBar && shopStyle && tabBar.kdtId === kdtId) {
        CACHE_TAB_BAR_PROMISE[kdtId] = {
          ...tabBar,
          themeMainBg: shopStyle.colors['main-bg'],
        };
      } else {
        CACHE_TAB_BAR_PROMISE[kdtId] = request({
          path: '/wscshop/weapp/custom_nav.json',
          data: {
            kdt_id: kdtId,
            useOmniChannel: true, // 优先查询全渠道数据
          },
        });
      }

      return CACHE_TAB_BAR_PROMISE[kdtId];
    }
  );
};

// // 获取底部导航
// export const getHqShopSettings = (refresh, name) => {
//   if (CACHE_HQ_SHOP_SETTINGS && !refresh) {
//     return CACHE_HQ_SHOP_SETTINGS[name];
//   }
//   const keys = ['hide_top_bar'];
//   CACHE_HQ_SHOP_SETTINGS = request({
//     path: '/wscshop/shop/getHqShopSettings.json',
//     data: {
//       keys: keys.join(','),
//     },
//   });
//   return CACHE_HQ_SHOP_SETTINGS[name];
// };
