/**
 * 账号多端 api 和 wsc 的中间层
 * @description 设计方案：https://doc.qima-inc.com/pages/viewpage.action?pageId=*********
 */

import {
  TEE_LOGIN_SILENT_BEFORE_EVENT,
  TEE_LOGIN_PLAT_SUCCESS_EVENT,
  TEE_LOGIN_YZ_BEFORE_EVENT,
  TEE_LOGIN_END_EVENT,
} from '@youzan/passport-tee-shared';
import {
  setAppTokenHooks,
  setCheckLoginHooks,
  setTeeLoginFailHooks,
  setBeforeYouzanLoginHook,
  setYouzanLoginFailHooks,
} from '@youzan/passport-tee-shared/lib/hooks/login';
import * as logger from 'utils/logger';
import { promisifyCall } from 'utils/promisify';
import teeEvent from '@youzan/tee-event';
import appEvent from 'shared/utils/app-event';
import Event from '@youzan/weapp-utils/lib/event';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import * as request from 'shared/utils/request';
import store from './store';
import { storage } from './utils';
import { getKdtId } from './shop';
import { getExtSync } from 'shared/utils/browser/ext-config';
import { getGuideBizDataMap } from 'utils/guide';

import {
  TOKEN_SUCCESS_EVENT,
  BEFORE_LOGIN_EVENT,
  LOGIN_SUCCESS_EVENT,
} from '../constants/event';
import { getLoginParams } from './account';

const { state, commit } = store;

let checkSessionPromise = null;

/**
 * 上报错误
 * @param {String} message
 * @param {Object} detail
 */
function reportSkynet(message, detail) {
  Event.trigger('logger:skynet', {
    message,
    detail,
  });
}

/**
 * 获取 enterOptions，如果失败会降级为 getLaunchOptions
 */
function safeGetEnterOptions() {
  let result = {};
  try {
    const enterOptions = wx?.getEnterOptionsSync?.();
    const launchOptions = wx?.getLaunchOptionsSync?.();
    result = enterOptions || launchOptions;
  } catch (err) {
    reportSkynet('静默登录获取 enterOptions 异常', err);
  }

  if (typeof result === 'object' && result !== null) {
    return result;
  }

  return {};
}

/**
 * 设置登录的 hooks
 * @description 用来处理多端静默登录和 wsc 静默登录的差异
 */
function setLoginHooks() {
  // 设置 app token 的取值 hooks
  setAppTokenHooks((token) => {
    // 兼容多端和 wsc 小程序存储 token 的数据格式，wsc 是 { value: token }，而多端的直接是 token
    // 目前采用“鸭子类型”判断属于哪种格式
    return token.value?.userId ? token.value : token;
  });
  // 设置静默登录的 check hooks
  setCheckLoginHooks(() => isEmpty(state.pluginData));
  // 设置渠道方 （tee.login） 登录失败的 hooks
  setTeeLoginFailHooks((error) => {
    error = typeof error === 'object' ? error.errMsg : '微信登录失败';

    reportSkynet('wx.login fail', { error });
  });
  // 设置有赞登录前的 hooks
  // 【注意】冷启动时会在数据预拉取接口中进行静默登录，如果这里需要加业务参数，请联系「基础业务」
  setBeforeYouzanLoginHook(() => {
    const { config, pluginData } = getLoginParams(state);

    commit('UPDATE_TOKEN', {});

    const { query } = wx?.getLaunchOptionsSync?.();
    const guideBizDataMap = getGuideBizDataMap(query);
    const extraBizData = {
      enterOptions: {
        extKdtId: getExtSync('kdtId'),
        ...safeGetEnterOptions(),
      },
      /** 导购业务信息 */
      guideBizDataMap,
      sceneData: state.sceneData,
    };

    return {
      ...config,
      ...pluginData,
      inWsc: true,
      kdtId: getKdtId() + '',
      extraBizData,
    };
  });
  // 设置有赞登录失败的 hooks
  setYouzanLoginFailHooks((error) => {
    reportSkynet('loginYouzan fail', error);
  });
}

/**
 * 设置事件监听
 * @description 用来磨平多端和 wsc 的事件差异
 */
function setEvent() {
  teeEvent.on(TEE_LOGIN_SILENT_BEFORE_EVENT, () => {
    Event.trigger(BEFORE_LOGIN_EVENT);
    appEvent.trigger(BEFORE_LOGIN_EVENT);
  });
  teeEvent.on(TEE_LOGIN_PLAT_SUCCESS_EVENT, (code) => {
    Event.trigger(LOGIN_SUCCESS_EVENT);
    logger.info(`[${LOGIN_SUCCESS_EVENT}]`, code);
    ECLOUD_MODE && reportSkynet('appWxLoginSuccess', { code });
  });
  teeEvent.on(TEE_LOGIN_YZ_BEFORE_EVENT, () =>
    appEvent.trigger('app:before:token')
  );

  // 监听标准中台化登录成功事件，做数据的特殊处理
  // ？这里会执行超过4次，导致反复更新kdtid的动作被执行
  teeEvent.on(TEE_LOGIN_END_EVENT, loginDataTransform);
}

/**
 * 将 token 内的 number 全部转为 string
 */
function changeNumberToString(token) {
  try {
    const keys = Object.keys(token);

    keys.forEach((key) => {
      if (typeof token[key] === 'number') {
        token[key] += '';
      }
    });
    // eslint-disable-next-line no-empty
  } catch (err) {}

  return token;
}

/**
 * 静默登录数据转换
 * @description wsc 的登录态数据结构和标准的模式不一致，在此做转换并塞入到 storage 里
 * @param data - 多端返回的静默登录数据
 * @return wsc 的数据结构
 */
function loginDataTransform(data) {
  // 注意：只需要在第一次做转换即可，后续的从 storage 里读取的已经是转换过的
  if (!data.cache) {
    data.yzUserId = data.yzUserId || data.userId;
    data.userId = data.platformFansId;
    data.userPrivacyDto = data.userPrivacyDto || {};
    data.userPrivacyDto.kdtId =
      data.userPrivacyDto.kdtId || data.userPrivacyDto.current_kdtid;
  }

  const token = changeNumberToString(mapKeysCase.toCamelCase(data));

  request.updateToken(token);
  commit('UPDATE_TOKEN', token);

  Event.trigger(TOKEN_SUCCESS_EVENT, token);
  appEvent.trigger(TOKEN_SUCCESS_EVENT, token);
  !data.cache &&
    storage.setAsync('app:token', token, {
      expire: 0.8, // sessionId 默认时效为 1 天，sessionKey 时效以天为级别，accessToken 默认时效 5 天
    });

  return token;
}

/**
 * 多端 wsc 版的静默登录 - 直接触发重新登录，不校验缓存
 * @param {Function} callback 成功回调（不要使用，请根据返回的 promise 进行下一步）
 * @param {Number} retry 尝试登录次数
 * @returns {Object} Promise<token>
 */
export function login(callback = () => {}) {
  return getApp()
    .resolveTeeAPI()
    .then((api) => api.forceLogin().then(callback).catch(callback));
}

/**
 * 多端版的 wsc 静默登录
 * 只做 token 是否存在检查，不做 wx.checkSession 检查
 * 原因是 wx.checkSession 比较耗时间。
 * 注：这个方法不能确保用户在 微信侧 是否还有登录态（见下面 checkSessionWithWx 方法）
 * @returns {Object} Promise<token>
 */
export function checkSession() {
  return getApp()
    .resolveTeeAPI()
    .then((api) => api.login());
}

/**
 * 检查登录态
 * 检查卡门 token 与 微信 session_key 是否都有效
 * 这个方法可以确保用户在 微信侧、有赞侧 都是有登录态的
 * 此方法适用于：需要微信 session_key 鉴权的场景，比如 获取微信手机号解密 等场景
 * https://developers.weixin.qq.com/miniprogram/dev/api/open-api/login/wx.checkSession.html
 * @returns {Promise<void | never>}
 */
export function checkSessionWithWx() {
  if (!checkSessionPromise) {
    checkSessionPromise = promisifyCall('wx.checkSession');
  }

  return checkSessionPromise
    .then(() => {
      checkSession();
      checkSessionPromise = null;
    })
    .catch(() => {
      getApp()
        .resolveTeeAPI()
        .then((api) => api.forceLogin());
      checkSessionPromise = null;
    });
}

/**
 * 修改登录态缓存
 */
export function updateLoginStorage(data) {
  const storageCache = wx.getStorageSync('app:token');

  // 如果当前登录态过期了，就不修改了
  if (!storageCache) {
    return;
  }

  wx.setStorage({
    key: 'app:token',
    data: {
      ...storageCache,
      value: {
        ...storageCache.value,
        ...data,
      },
    },
  });
}

setLoginHooks();
setEvent();
