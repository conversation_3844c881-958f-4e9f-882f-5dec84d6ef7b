import {
  LanguageCore,
  initApollo as setLanguageApollo,
} from '@youzan/shop-tee-language';
import { apollo, skyLog } from '@youzan/shop-tee-shared';
import { setCache as setCacheRetail } from '@youzan/retail-tee-component/src/common/global.js';
import { navigateToNextPage } from '@youzan/tee-biz-prefetch';
import request from '@youzan/tee-biz-request';

export { LanguageCore } from '@youzan/shop-tee-language';

function listenRouter() {
  wx.onAppRoute(function () {
    LanguageCore.getPageLanguageCache();
  });
}

export function asyncRetailUtils() {
  return import('@youzan/retail-tee-component/src/utils');
}

/** 一些app级别的初始化 */
export const initAppFunctions = (extra) => {
  const { isRetailApp } = extra;
  setLanguageApollo();
  apollo.initApolloConfig();
  LanguageCore.init(isRetailApp);
  listenRouter();
  setCacheRetail({
    navigateToNextPage,
    request,
    skyLog,
  });
  setTimeout(() => {
    asyncRetailUtils().then(({ initRetailShelfPrefetch }) => {
      initRetailShelfPrefetch();
    });
  }, 8000);
};
