import {
  cache,
  hooks,
  platform,
  onEvent,
  onUserAuthSuccess,
  TEE_LOGIN_END_EVENT,
} from '@youzan/passport-tee-shared';

import * as logger from 'utils/logger';
import { promisifyCall } from 'utils/promisify';

import * as request from 'shared/utils/request';
import AppEvent from 'shared/utils/app-event';

import get from '@youzan/weapp-utils/lib/get';
import WeappEvent from '@youzan/weapp-utils/lib/event';
import throttle from '@youzan/weapp-utils/lib/throttle';
import TeeEvent from '@youzan/tee-event/lib';

import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { setRequestDep } from '@youzan/tee-biz-request';
import { storage } from './utils';
import { updateLoginStorage } from './passport-tee-login';
import { enterShopOnLogin } from '@/helpers/shop';
import { onApiCategoryChange } from '@/helpers/app';
import { Hummer } from 'shared/utils/hummer';

import store from './store';
import {
  TOKEN_SUCCESS_EVENT,
  LOGIN_SUCCESS_EVENT,
  BEFORE_LOGIN_EVENT,
} from '../constants/event';
import {
  resolveProtocol,
  getAuthorizeData,
  syncAuthState,
  getLoginParams,
} from './account';

// 后续逐渐迁移至此
export * from './account';

const { authorizeHook } = hooks;
const { authLogger } = platform;

const { state, commit } = store;
const noop = () => {};

// 需要清理历史token缓存时，升级tokenVersion来进行重登录
const TOKEN_VERSION = '2';

function showAppLoginFailModal() {
  wx.showModal({
    title: '登录失败',
    content: '登录失败可能导致应用无法使用，你可以再试试或退出小程序',
    confirmText: '再试试',
    showCancel: false,
    success: () => {
      const app = getApp();

      app.login(() => {
        app.hasToken() && wx.showToast({ title: '登录成功' });
      });
    },
  });
}

/**
 * 上报错误
 * @param {String} message
 * @param {Object} detail
 */
function reportSkynet(message, detail) {
  WeappEvent.trigger('logger:skynet', {
    message,
    detail,
  });
}

const loginConfig = {
  skipToken: true,
  skipKdtId: true,
  skipShopInfo: true,
};

function loginYouzan(code) {
  const { config, pluginData } = getLoginParams(state, { includeSecret: true });

  commit('UPDATE_TOKEN', {});

  AppEvent.trigger('app:before:token');

  return request.node({
    method: 'POST',
    config: loginConfig,
    path: 'wscshop/weapp/authorize.json',
    data: { ...config, ...pluginData, code },
  });
}

function tokenSuccess(token, isCache = false) {
  token = mapKeysCase.toCamelCase(token);
  // 写入tokenVersion以供后续兼容
  token.tokenVersion = TOKEN_VERSION;

  request.updateToken(token);
  setRequestDep(token);
  commit('UPDATE_TOKEN', token);

  WeappEvent.trigger(TOKEN_SUCCESS_EVENT, token);
  TeeEvent.trigger(TOKEN_SUCCESS_EVENT, token);
  AppEvent.trigger(TOKEN_SUCCESS_EVENT, token);

  !isCache &&
    storage.setAsync('app:token', token, {
      expire: 0.8, // sessionId 默认时效为 1 天，sessionKey 时效以天为级别，accessToken 默认时效 5 天
    });
}

function tokenFail() {
  setTimeout(() => {
    showAppLoginFailModal();
  }, 10);
}

let loginPromise = null;

/**
 * 登录
 * @param {Function} callback 成功回调（不要使用，请根据返回的 promise 进行下一步）
 * @param {Number} retry 尝试登录次数
 * @returns {Object} Promise<token>
 */
export function login(callback = () => {}, retry = 6) {
  WeappEvent.trigger(BEFORE_LOGIN_EVENT);
  AppEvent.trigger(BEFORE_LOGIN_EVENT);

  if (retry <= 0) {
    tokenFail();
    return Promise.reject();
  }

  loginPromise =
    loginPromise ||
    promisifyCall('wx.login')
      .catch((error) => {
        error = typeof error === 'object' ? error.errMsg : '微信登录失败';

        reportSkynet('wx.login fail', { error });

        loginPromise = null;
        throw new Error(error);
      })
      .then(({ code }) => {
        WeappEvent.trigger(LOGIN_SUCCESS_EVENT);
        logger.info(`[${LOGIN_SUCCESS_EVENT}]`, code);

        ECLOUD_MODE && reportSkynet('appWxLoginSuccess', { code });
        return loginYouzan(code)
          .then((token) => {
            token = tokenSuccess(token);
            loginPromise = null;
            callback();
            return token;
          })
          .catch((error) => {
            reportSkynet('loginYouzan fail', error);

            /**
             * 这个回调很危险，如果登录失败，而回调又依赖 token，可能存在循环
             */
            callback();
            loginPromise = null;

            // 获取微信信息失败，尝试重新获取
            if (code === 135000025 || code === 160210092) {
              setTimeout(() => {
                login(() => {}, retry - 1);
              }, 100);
              return Promise.reject();
            }

            // 商家后台已经解绑了小程序
            if (code === 135000028) {
              wx.showModal({
                title: '登录失败',
                content: '获取商家信息失败，请联系商家获取最新小程序',
                showCancel: false,
              });
              return Promise.reject();
            }

            tokenFail();

            throw new Error(error);
          });
      });

  return loginPromise;
}

export function getToken(key) {
  const { token } = state;

  return key ? token[key] : token;
}

export function hasToken() {
  return !isEmpty(state.token);
}

export function getAccessToken() {
  return getToken('accessToken') || null;
}

export function getFansType() {
  return getToken('fansType') || 1343;
}

export function getSessionId() {
  return getToken('sessionId') || null;
}

export function getBuyerId() {
  return getToken('buyerId') || null;
}

export function getMobile() {
  return getToken('mobile') || null;
}

export function getUserInfoSync(key) {
  const { userInfo } = state;
  if (key) {
    return get(userInfo, key);
  }
  return userInfo;
}

export function updateUserInfo(userInfo = {}) {
  commit('UPDATE_USER_INFO', userInfo);
}

// 更新用户信息相关缓存
const updateUserCache = (userInfo) => {
  const res = {
    userInfo: { ...userInfo },
  };
  getApp().globalData.userInfo = res;
  // 更新 app:token
  updateLoginStorage(userInfo);
  updateUserInfo({ ...userInfo });
};

// 根据授权状态，格式化用户信息
const formatUserInfo = (authData) => {
  const { privacyState } = authData;
  const { nickname, avatar, mobile, gender } = authData.userInfo;
  const isNick = privacyState.nicknameAndAvatar;
  const isMobile = privacyState.mobile;
  const n = isNick ? nickname : '';
  const a = isNick ? avatar : '';
  const m = isMobile ? mobile : '';

  return {
    ...authData.userInfo,
    nickname: n,
    nickName: n,
    avatar: a,
    avatarUrl: a,
    gender,
    mobile: m,
  };
};

/**
 * 获取用户数据
 * 实现：直接从有赞账号服务读取数据
 * 重要提示：`wx.getUserProfile` 方式已废弃。
 */
export function getUserInfo(success = noop, fail = noop) {
  getAuthorizeData()
    .then((authData) => {
      const userInfo = formatUserInfo(authData);

      // 头像昵称授权后才表示能够拿到完整的用户信息
      if (!userInfo.nickName) {
        throw new Error('头像昵称未授权');
      }

      updateUserCache(userInfo);
      success({ userInfo });
    })
    .catch((e) => {
      console.error(e);
      const msg = e.message || '头像昵称更新失败';
      const err = {
        errMsg: msg,
        message: msg,
      };

      fail(err);
    });
}

/**
 * 清除协议数据缓存
 */
export function clearProtocolCache() {
  return resolveProtocol().then((res) => res.clearProtocolCache());
}

/**
 * 授权组件授权成功后的处理逻辑
 *
 * @param {object} params
 * @param {string[]} [params.authTypeList] 此次更新的授权列表，可选：protocol, mobile, nicknameAndAvatar
 * @param {boolean} [params.userDeny] 是否拒绝
 */
function handleAuthSuccess(params) {
  const app = getApp();
  const { authTypeList = [], userDeny } = params || {};

  const emitEvent = (userInfo) => {
    if (userDeny) {
      return;
    }

    // 通知：给有赞云
    const triggerCloudEvent = (type, detail) => {
      AppEvent.trigger('app:account:authorized', {
        type,
        detail: detail || {},
      });
    };

    authTypeList.forEach((type) => {
      if (type === 'protocol') {
        triggerCloudEvent('protocol');
        return;
      }
      if (type === 'nicknameAndAvatar') {
        triggerCloudEvent('userInfo', {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          gender: userInfo.gender,
        });
        return;
      }
      if (type === 'mobile') {
        triggerCloudEvent('mobile', {
          mobile: userInfo.mobile,
        });
      }
    });
  };

  return getAuthorizeData({ kdtId: app.getKdtId() })
    .then((authData) => {
      const userInfo = formatUserInfo(authData);
      updateUserCache(userInfo);

      emitEvent(userInfo);
    })
    .catch((e) => {
      // 数据查询失败也要通知更新
      const { userInfo = {} } = app.globalData.userInfo || {}; // 已有数据兜底
      emitEvent(userInfo);

      console.error('getAuthorizeData error:', e);
      throw e;
    });
}

// 监听登录
WeappEvent.on('auth:login', login);

WeappEvent.once('app:after-bootstrap', (app) => {
  // 店铺 kdt_id 更新
  app.on('app:chainstore:kdtid:update', ({ kdtId }) => {
    authLogger.logAll({
      logName: 'chain_auth_sync',
      logData: { kdtId },
    });
    syncAuthState({ kdtId });
    clearProtocolCache(); // 协议数据目前没有根据 kdtId 使用或更新缓存，需手动更新缓存
  });
});

// 授权组件登录后重新进店
// 仅迁移的历史逻辑，是否需要同步执行仍存疑，后续可以把这些逻辑梳理合并下，比如通过事件监听异步处理
authorizeHook.mobileChange.set(() => enterShopOnLogin());

// FID
const POPUP_FID = 'user-auth-popup';

authorizeHook.buttonClickAfter.set(() => {
  Hummer.mark.start(POPUP_FID);
});
authorizeHook.popupShowAfter.set(() => {
  Hummer.mark.end(POPUP_FID);
});

// 监听授权成功事件
onUserAuthSuccess(handleAuthSuccess);
// 小程序模式变更后重新渲染授权组件
onApiCategoryChange(() => syncAuthState());

// 重新登录之后刷新授权数据并通知授权组件重新渲染
onEvent(
  TEE_LOGIN_END_EVENT,
  throttle((res) => {
    if (!res.cache) {
      syncAuthState();

      resolveProtocol().then((protocol) => protocol.clearProtocolCache());
    }
  }, 500)
);

// 原生小程序初始化成功标识，防止重复初始化
cache.set('CACHE_ACCOUNT_INIT', true);
