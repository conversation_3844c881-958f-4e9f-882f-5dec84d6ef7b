import pick from '@youzan/weapp-utils/lib/pick';
import * as logger from 'utils/logger';

export function getLoginParams(state, options) {
  const { includeSecret } = options || {};
  const { config, pluginData } = state;

  const validTip = (key) =>
    `[auth]loginYouzan: ${key} 必须，请在程序启动时设置项目配置信息`;

  logger.assert(config.appId, validTip('appId'));
  logger.assert(config.clientId, validTip('clientId'));
  logger.assert(config.clientSecret, validTip('clientSecret'));

  const configProps = ['appId', 'clientId', 'grantType'];
  includeSecret && configProps.push('clientSecret');

  return {
    config: pick(config, configProps),
    pluginData: pick(pluginData, ['pluginHostAppId', 'pluginHostOpenId']),
  };
}
