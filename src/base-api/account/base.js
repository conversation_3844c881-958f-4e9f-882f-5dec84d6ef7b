let __TEE_API_CACHE;

export function resolveTeeAPI() {
  // resolve 异步 module 有性能损耗，增加缓存
  if (!__TEE_API_CACHE) {
    __TEE_API_CACHE = import('@youzan/passport-tee-api')
      .then((res) => res.default)
      .catch((e) => {
        __TEE_API_CACHE = null;
        throw e;
      });
  }
  return __TEE_API_CACHE;
}

export function resolveProtocol() {
  return import('@youzan/passport-protocol');
}
