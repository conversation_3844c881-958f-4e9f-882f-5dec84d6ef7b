import { platform, emitUserAuthSync } from '@youzan/passport-tee-shared';

import { resolveTeeAPI, resolveProtocol } from './base';

/**
 * 获取授权原始数据
 */
export function getAuthorizeData(options) {
  return resolveTeeAPI().then((api) => {
    const { kdtId = getApp().getKdtId() } = options || {};
    return api.getAuthorizeData({ ...options, kdtId });
  });
}

/**
 * 刷新授权组件数据
 *
 * @param {object} [options]
 * @param {number} [options.kdtId] 店铺 ID
 * @return {Promise<unknown>}
 */
export function refreshUserAuthData(options) {
  return resolveTeeAPI().then((api) => {
    const app = getApp();
    const { kdtId = app.getKdtId() } = options || {};

    console.log('[account] refreshUserAuthData', { ...options, kdtId });
    return api.refreshUserAuthData({ kdtId, appId: app.getAppId() });
  });
}

/**
 * 预请求/加载授权组件数据
 *
 * @param {object} [options]
 * @param {number} [options.kdtId] 店铺 ID
 * @return {Promise<unknown>}
 */
export function prefetchUserAuthData(options) {
  return resolveTeeAPI().then((api) => {
    const app = getApp();
    const { kdtId = app.getKdtId() } = options || {};

    return api.prefetchUserAuthData({
      kdtId,
      appId: app.getAppId(),
    });
  });
}

/**
 * 更新授权数据并触发授权组件重新渲染
 *
 * @param {object} [options]
 * @param {number} [options.kdtId] 店铺 ID
 * @return {Promise<unknown>}
 */
export function syncAuthState(options) {
  console.log('[account] syncAuthState', options);
  return refreshUserAuthData(options).finally(() => {
    emitUserAuthSync();
  });
}

/**
 * 微信签署监听
 */
export function onNeedPrivacyAuth() {
  platform.onNeedPrivacyAuth(({ eventInfo }) => {
    resolveProtocol().then(({ InvokeProtocol }) =>
      new InvokeProtocol().auth({
        bizType: 'weapp-privacy',
        bizData: { eventInfo },
        timeout: 0, // 必备场景不超时，等待协议组件加载完成
      })
    );
  });
}
