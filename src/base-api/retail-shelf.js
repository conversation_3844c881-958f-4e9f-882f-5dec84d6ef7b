export function getRetailShelfQueryParams(path, query) {
  // 点单页
  return path === 'packages/retail-shelf/shelf/index'
    ? {
        ...query,
      }
    : {};
}

export function genOrderPullJumpUrl(url, ext = {}) {
  if (!url) return url;
  const app = getApp();
  const { query = {} } = app.getWxPage() || {};
  const keys = ['enterShopId', 'alias'];
  const formatObj = ext.alias ? ext : query;
  keys.forEach((i) => {
    if (formatObj[i]) {
      const f = url.includes('?') ? '&' : '?';
      url = `${url}${f}${i}=${formatObj[i]}`;
    }
  });
  return url;
}
