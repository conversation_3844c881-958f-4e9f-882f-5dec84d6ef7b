import Store from '@youzan/vanx';
import * as request from 'shared/utils/request';
import pick from '@youzan/weapp-utils/lib/pick';
import { getExtSync } from 'shared/utils/browser/ext-config';

import { defaultShopConfig } from './shop/config';

const store = new Store({
  state: {
    /**
     * 登录前应该准备好，使用 UPDATE_CONFIG 更新数据
     * appId 小程序 appId
     * clientId carmen参数
     * clientSecret carmen参数
     * grantType carmen参数
     * version 小程序版本
     * weappType 小程序类型，如公共版 youzan_weapp，爱逛 ai_guang。用于获取 businessId
     */
    config: {
      grantType: 'yz_union',
      weappType: '',
      version: getExtSync('userVersion')
    },
    /**
     * 登录前应该准备好，使用 UPDATE_PLUGIN_DATA 更新数据
     * pluginHostAppId
     * pluginHostOpenId
     */
    pluginData: {},
    /**
     * accessToken
     * refreshToken
     * userId
     * openId
     * mobile
     * buyerId
     * sessionId
     * viewTrack
     */
    token: {},

    shop: defaultShopConfig,
    /**
     * 保存scene反解的数据
     */
    sceneData: {},
    /**
     * nickname 用户昵称
     * avatar 用户头像
     * userId 粉丝ID
     * viewTrack2 用户ID，加密
     * yzUserId 有赞 userId
     * platformFansId 有赞大号粉丝ID
     * openId 微信 openId
     * buyerId 有赞 buyerId，绑定手机号才有
     * fansType 粉丝类型
     * mobile 用户手机号
     */
    userInfo: {
      poi: null
    }
  },
  mutations: {
    UPDATE_TOKEN(state, token) {
      state.token = token;

      const userInfo = pick(token, [
        'avatar',
        'gender',
        'kdtId',
        'mobile',
        'openId',
        'userId',
        'yzUserId',
        'nickName',
        'fansType',
        'viewTrack2',
        'platformFansId',
        'isDefaultAvatar'
      ]);
      userInfo.nickname = userInfo.nickName;

      delete userInfo.nickName;

      state.userInfo = userInfo;
    },

    UPDATE_CONFIG(state, config) {
      Object.assign(state.config, config);

      state.config.appId &&
        request.setRequestDep({
          appId: state.config.appId,
          version: state.config.version
        });
    },

    UPDATE_PLUGIN_DATA(state, config) {
      const { pluginData } = state;
      pluginData.pluginHostAppId = config.appId;
      pluginData.pluginHostOpenId = config.openId;
    },

    UPDATE_USER_INFO(state, payload) {
      state.userInfo = {
        ...state.userInfo,
        ...payload,
        isDefaultAvatar: false,
      };
    },

    UPDATE_SCENE_DATA(state, payload) {
      state.sceneData = { ...state.sceneData, ...payload };
    },

    UPDATE_SHOP_DATA(state, payload = {}) {
      state.shop = {
        ...state.shop,
        ...payload
      };
    }
  }
});

export default store;
