// 优惠方式
export const PreferentialModeEnum = {
  NUM: 1,
  DISCOUNT: 2,
  EXCHANGE: 3,
  BUY_PRESENT: 4,
  EXPRESS: 5,
};

// 优惠券面额生成规则类型
export const BeneitNumTypeEnum = {
  FIXED: 1,
  ASSIGNED: 2,
  RANDOM: 3,
  NOASSIGNED: 4,
};

export const CouponStatusEnum = {
  Valid: 1, // 生效中-未使用 & 未过期
  Used: 2, // 已使用
  Expired: 3, // 已过期-未使用 & 已过期
  Invalid: 4, // 已失效-已使用 | 已过期
  Locked: 5, // 已锁定
};

/**
 * 券码活动类型[老凭证类型]
 */
export const GroupType = {
  Code: 'code',
  Card: 'card',
  Thirdparty: 'thirdparty',
};

/**
 * 券码活动类型
 */
export const CouponType = {
  Normal: 0,
  Code: 1,
  Thirdparty: 2,
};

// * 场景营销计划类型
export const PlanTypeEnum = {
  Birthday: 1,
  MemberDay: 2,
  Festival: 3,
};

// * 场景营销计划状态
export const PlanStateEnum = {
  NotStart: 0,
  InProgress: 1,
  Terminated: 2,
  ActivePause: 3,
  AbnormalPause: 4,
  Finished: 5,
};

/** 场景营销订阅消息缓存 key */
export const SCENE_MARKET_SUBSCRIBE_STORAGE_KEY = 'scene-market-subscribe';

/** 场景营销订阅消息 - SCENE */
export const SCENE_MARKET_SCENE = 'cimWeMiniProMsg';

/** 积分到期提醒订阅消息缓存 key */
export const POINTS_EXPIRE_SUBSCRIBE_STORAGE_KEY = 'points-expire-subscribe';

/** 积分到期提醒订阅消息 - SCENE */
export const POINTS_EXPIRE_REMINDER_SCENE = 'maCrmMiniProPoint';

// * 来源页面映射
export const FromPageMap = {
  homeOrFeature: 'homeOrFeature',
  memberCenterClick: 'memberCenterClick',
  levelGuide: 'levelGuide',
};
