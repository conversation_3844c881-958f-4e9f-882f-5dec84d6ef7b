import { enterShopAfterLogin } from '@youzan/tee-chain-store';
import omit from '@youzan/utils/object/omit';
import args from '@youzan/utils/url/args';

import { getCurrentPage } from 'shared/common/base/wsc-component';

/**
 * enterShopAfterLogin
 */
export function enterShopOnLogin() {
  const currentPage = getCurrentPage();

  const redirectUrl = `/${encodeURIComponent(
    args.add(currentPage.route, omit(currentPage.options || {}, 'subKdtId'))
  )}`;

  // 登录后重新进店
  return enterShopAfterLogin({ redirectUrl });
}
