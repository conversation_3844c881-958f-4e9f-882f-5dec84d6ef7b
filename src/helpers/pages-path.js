import args from '@youzan/weapp-utils/lib/args';
import {
  PAGE_TYPE,
  NAVIGATE_TYPE,
  navigateToRantaPage,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';

function getPath(path = '', query = {}) {
  const url = path.slice(0, 1) === '/' ? path : '/' + path;
  return args.add(url, query);
}

function redirectTo(path = '', query = {}) {
  wx.redirectTo({ url: getPath(path, query) });
}

function reLaunch(path = '', query = {}) {
  wx.reLaunch({ url: getPath(path, query) });
}

function redirectToBySeckill(path = '', query = {}) {
  const { goodsAlias, alias } = query;
  redirectTo(path, {
    ...query,
    alias: goodsAlias || alias,
    umpType: 'seckill',
    umpAlias: alias,
  });
}

function toRantaPage(pageType) {
  navigateToRantaPage({
    pageType,
    type: NAVIGATE_TYPE.REDIRECT,
  });
}

function toCart() {
  toRantaPage(PAGE_TYPE.CART);
}

function toPaid() {
  toRantaPage(PAGE_TYPE.PAID);
}

/**
 * 新老页面跳转规则列表
 *
 * 如果在这里添加自己的规则，请必须正确的 target 和 handle 方法
 * 在 handle 中你可以做任何处理，当老页面找不到时会传如 target 和进入页面时的参数
 */

const p = 'packages';
const goodsV2Pre = `${p}/goods-v2`;
const goodsV3Pre = `${p}/goods-v3`;
const retailPre = `${p}/retail`;
const homePre = `${p}/home`;
const goodsPath = 'pages/goods/detail/index';
const pageHome = 'pages/home';

let pagesReplaceRulesMap = null;
let hasGetRulesMap = false;

export const getPagesReplaceRules = async () => {
  if (pagesReplaceRulesMap && hasGetRulesMap) return pagesReplaceRulesMap;

  // 页面重定向配置转移至apollo，可根据版本号设置对应的配置
  const redirectPathMap = await getApp()
    .request({
      path: '/wscshop/weapp/redirect_mapping.json',
    })
    .then((res) => {
      hasGetRulesMap = true;
      return res;
    })
    .catch(() => ({}));

  const redirectToRulesMap = {};
  Object.keys(redirectPathMap).forEach((item) => {
    redirectToRulesMap[item] = {
      target: redirectPathMap[item],
      handle: redirectTo,
    };
  });
  pagesReplaceRulesMap = {
    ...redirectToRulesMap,
    [`${goodsV2Pre}/cart/index`]: {
      target: '',
      handle: toCart,
    },
    [`${goodsV3Pre}/cart/index`]: {
      target: '',
      handle: toCart,
    },
    [`${goodsV3Pre}/seckill/index`]: {
      target: goodsPath,
      handle: redirectToBySeckill,
    },
    // 自由购用户中心
    [`${retailPre}/freego-usercenter/index`]: {
      target: `${retailPre}/usercenter/dashboard-v2/index`,
      handle: redirectTo,
      query: {
        isRetail: '1',
      },
    },
    [`${homePre}/dashboard/index`]: {
      target: `${pageHome}/dashboard/index`,
      handle: reLaunch,
    },
    [`${homePre}/feature/index`]: {
      target: `${pageHome}/feature/index`,
      handle: redirectTo,
    },
    [`${homePre}/tab/one`]: {
      target: `${pageHome}/tab/one`,
      handle: redirectTo,
    },
    [`${homePre}/tab/two`]: {
      target: `${pageHome}/tab/two`,
      handle: redirectTo,
    },
    [`${homePre}/tab/three`]: {
      target: `${pageHome}/tab/three`,
      handle: redirectTo,
    },
    'packages/order/paid/index': {
      target: '',
      handle: toPaid,
    },
    'packages/crm/order-detail/index': {
      target: 'packages/trade/crm-order-detail/index',
      handle: redirectTo,
    },
  };
  return pagesReplaceRulesMap;
};
