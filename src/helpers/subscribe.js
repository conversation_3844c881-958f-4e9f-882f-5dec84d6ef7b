import Toast from '@vant/weapp/dist/toast/toast';

import { checkPureWscSingleStore } from '@youzan/utils-shop';

import {
  queryWechatSubscribeResult,
  requestSubscribeMessage,
} from 'utils/subscribe-message';

const app = getApp();

const PREFIX = '/wscump/common/';
// 提升小程序订阅消息渗透率黑名单
const getNotInBlackOfSubscribe = function () {
  return app.request({
    path: '/wscdeco/im/common/is-shop-not-in-black-list.json',
  });
};

const getSceneTemplIdList = function (scene) {
  return app.request({
    path: `${PREFIX}get-template.json`,
    query: {
      scene,
    },
  });
};

// 单独开放getSubscribePreInfo（订阅之前需要查询的前置信息） 和 handleSubscribeClick（点击订阅按钮后）
// 之所以单独开放是这两个方法，是因为微信限制：从点击订阅到调用wx api 中间不能有异步请求。
// https://jira.qima-inc.com/browse/ONLINE-718541
export function handleSubscribeClick({
  templateIdList = [],
  scene = '',
  needAlwaysToast = false,
  successCallBack = null,
  rejectCallBack = null,
  failCallBack = null,
  showCallBack = null,
  closeCallBack = null,
  // 埋点数据
  logParam = {},
}) {
  const canLog = Object.keys(logParam).length > 0;

  if (templateIdList.length > 0) {
    requestSubscribeMessage({
      templates: templateIdList,
      onSuccess: (res) => {
        for (const key in res) {
          if (key !== 'errMsg' && res[key] === 'reject') {
            rejectCallBack && rejectCallBack();
            break;
          }
        }

        // eslint-disable-next-line no-use-before-define
        const { acceptTemplateIdList: list = [] } = handleSubscriptionRes(res);
        if (list.length) {
          // 上传消息后端，记录用户授权信息
          canLog &&
            app.logger.log({
              et: 'click',
              ei: 'allow_msg_subscribe',
              en: '允许订阅小程序消息',
              params: logParam,
            });
          // eslint-disable-next-line no-use-before-define
          return subscriptionCb({
            scene,
            templateIdList: list,
          })
            .then(({ value = false } = {}) => {
              if (value) {
                canLog &&
                  app.logger.log({
                    et: 'click',
                    ei: 'msg_subscribe_success',
                    en: '成功订阅小程序消息',
                    params: {
                      ...logParam,
                      result: JSON.stringify(list),
                    },
                  });
                // 成功后出发成功后的业务逻辑
                successCallBack && successCallBack(list);
              } else {
                Toast('授权失败');
              }
            })
            .catch(() => {
              Toast('授权失败');
            });
        }

        /**
         * 用户全部拒绝，根据用户设置判断是否需要执行失败回调
         * 如果用户设置已经拒绝，执行失败回调
         * 如果用户设置没有拒绝，说明本次操作刚拒绝，不执行失败回调，静默
         */
        // 当用户勾选了订阅面板中的“总是保持以上选择，不再询问”时，模板消息会被添加到用户的小程序设置页，通过 wx.getSetting 接口可获取用户对相关模板消息的订阅状态。
        let result = {};
        wx.getSetting({ withSubscriptions: true })
          .then((res) => {
            result = res.subscriptionsSetting;
            const { mainSwitch, itemSettings } = res.subscriptionsSetting;
            const allItemRejected = templateIdList.every(
              (item) =>
                itemSettings && itemSettings[item.templateId] === 'reject'
            );
            // 主开关关闭或所有项拒绝
            if (!mainSwitch || allItemRejected) {
              failCallBack && failCallBack();
            } else {
              closeCallBack && closeCallBack();
            }
          })
          .catch(() => {
            closeCallBack && closeCallBack();
          });
        canLog &&
          app.logger.log({
            et: 'click',
            ei: 'refuse_msg_subscribe',
            en: '拒绝订阅小程序消息',
            params: {
              ...logParam,
              result: JSON.stringify(result),
            },
          });
      },
      onFail: () => {
        failCallBack && failCallBack();
      },
      onShowTips: () => {
        needAlwaysToast &&
          Toast({
            duration: 3000,
            position: 'top',
            message:
              '勾选“总是保持以上选择”后，点击“允许”，即可长期获取签到提醒。',
          });
        // 添加弹出记录
        canLog &&
          app.logger.log({
            et: 'click',
            ei: 'msg_subscribe_view',
            en: '小程序订阅消息弹窗曝光',
            params: logParam,
          });
        showCallBack && showCallBack();
      },
      onCloseTips: () => {
        closeCallBack && closeCallBack();
      },
    });
  } else {
    // 对于template为空的情况（商家类目配置不正确，参考ONLINE-491807），此处按照close的方式来处理，不阻塞主流程
    closeCallBack && closeCallBack();
  }
}

export function getSubscribePreInfo({ scene = '', pageId = null }) {
  return Promise.all([getNotInBlackOfSubscribe(), app.getShopInfo()]).then(
    ([{ value: canSubscribe }, { shopInfo = {} }]) => {
      if (
        checkPureWscSingleStore(shopInfo) &&
        canSubscribe &&
        pageId !== null
      ) {
        return Promise.all([
          getSceneTemplIdList(scene),
          queryWechatSubscribeResult(pageId),
        ]).then(
          ([
            { templateIdList: sceneTempIdList = [] },
            { templateList: pageIdTempIdList = [] },
          ]) => {
            return [
              ...sceneTempIdList.map((templateId) => ({ templateId })),
              ...pageIdTempIdList,
            ];
          }
        );
      }

      return getSceneTemplIdList(scene).then(
        ({ templateIdList: sceneTempIdList }) => {
          return sceneTempIdList.map((templateId) => ({
            templateId,
          }));
        }
      );
    }
  );
}

// 订阅消息
// scene
// pageId
// needAlwaysToast  是否需要总是保持以上选择相关提示
// successCallBack
// rejectCallBack
// failCallBack
// showCallBack
// closeCallBack
// // 埋点数据
// logParam = {},
export function subscribeMessage(params) {
  params.needAlwaysToast && wx.showLoading();

  getSubscribePreInfo({
    scene: params.scene,
    pageId: params.pageId,
  })
    .then((templateIdList) => {
      wx.hideLoading();
      handleSubscribeClick({
        templateIdList,
        ...params,
      });
    })
    .catch((e) => {
      console.warn('Subscribe err:', e);
      e.msg &&
        wx.showToast({
          title: e.msg,
          icon: 'none',
        });
    });
}

export function isAuth4Push({ scene = '' }) {
  return new Promise((resolve) => {
    app
      .request({
        path: `${PREFIX}is-auth-for-push.json`,
        query: {
          scene,
        },
      })
      .then((res) => {
        res && resolve(res.value);
      })
      .catch((e) => {
        e &&
          wx.showToast({
            title: e,
            icon: 'none',
          });
      });
  });
}

export function isSubscriptionMsgAvailable() {
  return new Promise((resolve) => {
    app
      .request({
        path: `${PREFIX}is-subscription-msg-available.json`,
      })
      .then((res) => {
        res && resolve(res.value);
      })
      .catch((e) => {
        e &&
          wx.showToast({
            title: e,
            icon: 'none',
          });
      });
  });
}

// 上报订阅成功的事件到消息后端
function subscriptionCb({ scene = '', templateIdList = [] }) {
  return app.request({
    path: `${PREFIX}subscription-callback.json`,
    query: {
      scene,
      templateIdList: JSON.stringify(templateIdList),
    },
  });
}

// 处理成功回调里的 templateIds
function handleSubscriptionRes(res = {}) {
  const acceptTemplateIdList = [];
  try {
    // eslint-disable-next-line no-unused-vars
    for (const key in res) {
      if (key !== 'errMsg' && res[key] === 'accept') {
        acceptTemplateIdList.push(key);
      }
    }
  } catch (err) {
    console.err('授权成功数据解析失败：', res);
  }

  return {
    acceptTemplateIdList,
  };
}
