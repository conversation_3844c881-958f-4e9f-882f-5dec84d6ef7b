## 获取小程序码

- fetchQRCode 
  - path `String` 分享出去的路径，不能带参数(参数统一放在query中)
  - query `Object` 分享链接上带的参数，默认会带上网点ID，店铺ID

  ``` javascript
    import { fetchQrCode } from '@/helpers/fetch'

    fetchQrCode(path, query)
  ```
## 获取用户授权
- authorize
  - scope `String` 要获取的权限。函数内会判断用户是否授权，没有会尝试发起授权请求

  ``` javascript
    import authorize from '@/helpers/authorize'

    authorize('scope.writePhotosAlbum')
  ```

## 地图工具
- searchAddress 地址搜索
  - options `Object` http://lbs.qq.com/qqmap_wx_jssdk/index.html

  ``` javascript
    import { searchAddress } from '@/helpers/lbs'

    searchAddress({ keyword: '杭州' })
  ```

- reverseGeocoder GPS 转换为地址
  - options `Object` http://lbs.qq.com/qqmap_wx_jssdk/index.html

  ``` javascript
    import { reverseGeocoder } from '@/helpers/lbs'

    reverseGeocoder({ location: {...} })
  ```

- tryLocation 获取地理位置 gcj02
  - success `Function` 成功回调，返回第一个参数百度坐标，第二个参数是火星坐标（腾讯和高德坐标）
  - fail `Function` 失败回调，如果有第三个参数，会执行第三个参数后才回调
  - callback `Function` 失败确认回调，如果传了这个参数，这个方法会在调用失败后调用，传入 success, fail 参数。主要用在第一次定位失败，继续尝试第二次

- baiduToGcj 百度坐标转火星坐标
  - lng `Number`
  - lat `Number`

- gcjToBaidu 火星坐标转百度坐标
  - lng `Number`
  - lat `Number`

- calcDistance 计算两个坐标距离
  - source `Object`
  - target `Object`

- parseDistance 格式化距离
  - distance `Number` 单位米。大于 10000 米，返回 `> 10km`。小于 100 米，返回 `< 100m`。其他返回带两位小数字符串：12.12m

## 跳转
- switchTab
- navigate
- redirect