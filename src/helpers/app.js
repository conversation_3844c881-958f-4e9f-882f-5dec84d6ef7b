import debounce from '@youzan/weapp-utils/lib/debounce';

function getApiCategory() {
  if (wx.getApiCategory) {
    return wx.getApiCategory();
  }
  if (wx.getEnterOptionsSync) {
    return wx.getEnterOptionsSync().apiCategory;
  }
  return 'default';
}

export function judgeEmbedded() {
  return getApiCategory() === 'embedded';
}

export function onApiCategoryChange(callback) {
  let lastApiCategory = getApiCategory();

  const triggerChange = debounce((newApiCategory) => {
    if (lastApiCategory !== newApiCategory) {
      console.log(
        `[app] apiCategory changed from ${lastApiCategory} to ${newApiCategory}`
      );

      lastApiCategory = newApiCategory;
      callback(newApiCategory);
    }
  }, 500);

  /**
   * 监听多个事件的原因：
   *
   * 1. 部分 API 存在兼容性差异，可能不支持
   * 2. 部分 API 实际可能不会执行，只能做到尽量监听
   */

  if (wx.onApiCategoryChange) {
    wx.onApiCategoryChange((res) => {
      triggerChange(res.apiCategory);
    });
  }

  if (wx.onEmbeddedMiniProgramHeightChange) {
    wx.onEmbeddedMiniProgramHeightChange(() => {
      triggerChange(getApiCategory());
    });
  }

  wx.onAppShow((res) => {
    triggerChange(res.apiCategory || getApiCategory());
  });
}
