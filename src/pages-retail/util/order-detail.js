const BUY_WAY = {
  combined: 202 // 组合支付
};

/**
 * 获取支付金额
 * @param orderData
 * @returns
 */
export function getPayment(orderData = {}) {
  const { state, phasePayDetails = [], buyWay, realPay = 0 } = orderData;
  // 交易成功
  if (state > 3) {
    // 组合支付获取二阶段支付结果
    if (buyWay === BUY_WAY.combined) {
      return (phasePayDetails.find(item => item.phase === 2) || {}).pay || 0;
    }
    return realPay;
  }
  return null;
}
