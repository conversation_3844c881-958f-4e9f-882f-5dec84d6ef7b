import {
  getThemeMainColor,
  getThemeMainRgbColorWithAlpha,
} from 'retail/util/shelf-global';

export const ThemeColorWithComponent = Behavior({
  data: {
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    themeMainColor: getThemeMainColor(),
  },
  attached() {
    this.setData({
      themeMainColor: getThemeMainColor(),
      themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    });
  },
  pageLifetimes: {
    show() {
      this.setData({
        themeMainColor: getThemeMainColor(),
        themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
      });
    },
  },
});
