// 导航栏上胶囊按钮的高度
const CAPSULE_HEIGHT = 32;
// 安卓上，胶囊按钮距离导航栏顶部的距离
const ANDROID_MARGIN = 8;
// iOS上，胶囊按钮距离导航栏顶部的距离
const IOS_MARGIN = 6;

let systemInfoCache = null;

export const CustomNavBehavior = Behavior({
  data: {
    statusBarHeight: 0,
    navHeight: 0,
  },
  attached() {
    if (!systemInfoCache) systemInfoCache = wx.getSystemInfo();
    systemInfoCache.then((systemInfo) => {
      const { statusBarHeight, system } = systemInfo;
      let navHeight = CAPSULE_HEIGHT;
      navHeight += ~system.indexOf('iOS') ? IOS_MARGIN * 2 : ANDROID_MARGIN * 2;
      this.setData({
        statusBarHeight,
        navHeight,
      });
    });
  },
});
