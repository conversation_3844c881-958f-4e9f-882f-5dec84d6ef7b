const colorHash = {
  normal: '#323233',
  red: '#F40F0F'
};

/**
 * 获得多条凑单信息
 * @param groupInfoList
 * @returns {{node: {children: *[], name: string, attrs: {class: string}}[], text: string, promotionText: string}[]}
 */
export function getMultDiscountText(groupInfoList = []) {
  return groupInfoList.map(groupInfo => getDiscountText(groupInfo));
}

/**
 * 获取凑单信息，输入信息 {meetsInfo, recommendInfos, operateInfo} 分别为 左侧已满足的信息，中间推荐的信息，右侧操作信息
 * @returns {{node: [{children: [], name: string, attrs: {class: string}}], text: string, promotionText: string}}
 * @param group
 */
export function getDiscountText(group = {}) {
  const {
    meetInfos,
    recommendInfos,
    operateInfo
  } = group?.briefTips ?? {};
  const res = {
    promotionText: '', // 活动类型
    text: '', // 跳转提示信息
    linkType: '', // 跳转类型，包括换购和凑单
    node: [
      // 外面需要套一层，否则会丢失宽度，导致 flex 布局失效
      {
        name: 'div',
        attrs: {
          class: 'discount-text'
        },
        children: []
      }
    ]
  };
  // 活动标签
  if (group.promotionTypeDesc) {
    res.promotionText = group.promotionTypeDesc;
  }
  // 已满足信息
  if (meetInfos) {
    res.node[0].children.push({
      name: 'span',
      attrs: {
        class: 'meet'
      },
      children: JSON.parse(meetInfos).map(item => getNode(item))
    });
  }
  // 推荐信息
  if (recommendInfos) {
    res.node[0].children.push({
      name: 'span',
      attrs: {
        class: 'recommend'
      },
      children: JSON.parse(recommendInfos).map(item => getNode(item))
    });
  }
  // 操作按钮
  if (operateInfo) {
    const tmp = JSON.parse(operateInfo)[0];
    res.text = tmp.content;
    res.linkType = tmp.ref;
  }
  return res;
}
// 生成节点信息
function getNode(item) {
  return {
    name: 'span',
    attrs: {
      style: `color: ${colorHash[item.color] ?? colorHash.normal}`,
    },
    children: [
      {
        type: 'text',
        text: item.color ? ' ' + item.content + ' ' : item.content // 红色文字左右两侧各有空格
      }
    ]
  };
}
