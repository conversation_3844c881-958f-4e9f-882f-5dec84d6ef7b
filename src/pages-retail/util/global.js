import { getExtSync } from 'shared/utils/browser/ext-config';

const app = getApp();
const getScene = () => app.globalData.scene;

export const isFreeGo = () => getScene() === 2;
export const isScanGo = () => getScene() === 1;
export const getReatilSource = () => {
  let userVersion = getExtSync('userVersion');
  switch (app.globalData.scene) {
    case 1: {
      userVersion = userVersion || '3.8.0';
      return `MINAPP-SCAN-${userVersion}`;
    }
    case 2: {
      userVersion = userVersion || '3.12.0';
      return `MINAPP-FREE-${userVersion}`;
    }
    case 3: {
      userVersion = userVersion || '1.0.0';
      return `MINAPP-SHELF-${userVersion}`;
    }
    default:
      return '';
  }
};

// 101 优惠券
// 1013 商品兑换券
const COUPON_TYPE = ['101', '1013', '1014'];
export const getCouponList = (promotion) => {
  return promotion.filter((item) => COUPON_TYPE.includes(item.type));
};

// 获取顶部导航高度
export const getNavHeight = () => {
  // 导航栏上胶囊按钮的高度
  const CAPSULE_HEIGHT = 32;
  // 安卓上，胶囊按钮距离导航栏顶部的距离
  const ANDROID_MARGIN = 8;
  // iOS上，胶囊按钮距离导航栏顶部的距离
  const IOS_MARGIN = 6;
  // 设置默认值，机型不同，顶部高度不同也会导致抖动，所以选择直接获取，调用耗时极短
  const { statusBarHeight, system } = wx.getSystemInfoSync();
  let navHeight = CAPSULE_HEIGHT;
  navHeight += ~system.indexOf('iOS') ? IOS_MARGIN * 2 : ANDROID_MARGIN * 2;

  return {
    statusBarHeight, // 44
    navHeight, // 44
  };
};
