import { bindPhoneNumber, joinMember } from 'retail/util/bind-mobile';
import { curry } from 'retail/util/base';

const bindPhoneNumberCurried = curry(bindPhoneNumber);

const app = getApp();

const twoDay = 3600 * 24 * 2 * 1000;

export const MEMBER_STORAGE_KEY = `retail:ismember:${app.getKdtId()}`;

export const saveMemberInfoToCache = () => {
  wx.setStorage({
    key: MEMBER_STORAGE_KEY,
    data: { isMember: true, expired: Date.now() + twoDay }
  });
};

export const handleBindMember = (detail, callback) => {
  const { encryptedData, iv } = detail;

  if (!encryptedData) {
    return;
  }

  const become = app.getMobile()
    ? joinMember
    : bindPhoneNumberCurried(iv, encryptedData);

  const sessionId = app.getSessionId();

  app.logger &&
    app.logger.appError({
      name: 'bind_member_session_log',
      message: '日志记录 sessionId',
      detail: {
        currentKdtId: app.getKdtId(),
        sessionId
      }
    });

  app.login(() => {
    become(
      () => {
        saveMemberInfoToCache();
        typeof callback === 'function' && callback();
      },
      {
        member_src_way: 800,
        member_src_channel: 1000,
        need_be_member: true
      }
    );
  });
};
