import { values } from './base';

const getDiscountNode = (groupInfo, { themeMainColor = '#F40F0F' }) => {
  const { isMaximumDiscount, meetType, discountType, willDiscountType } =
    groupInfo;
  let { hadCost = 0, hadDiscount, willCost = 0, willDiscount } = groupInfo;
  let hadDiscountCopy = '';

  if (hadDiscount === 0) {
    return '';
  }

  switch (discountType) {
    case 1:
      hadDiscount /= 100;
      hadDiscount && (hadDiscountCopy = `下单减${hadDiscount}元`);
      break;
    case 2:
      hadDiscount /= 10;
      hadDiscount && (hadDiscountCopy = `下单打${hadDiscount}折`);
      break;
    default:
      return;
  }

  willDiscount =
    willDiscountType === 1 ? willDiscount / 100 : willDiscount / 10;

  switch (meetType) {
    case 1: {
      willCost /= 100;
      hadCost /= 100;
      break;
    }
    case 2: {
      willCost /= 1; // 保持一致
      hadCost /= 1;
      break;
    }
    default:
      break;
  }

  const nextDiscountNode = [
    {
      type: 'text',
      text: '再买',
    },
    {
      name: 'span',
      children: [
        {
          type: 'text',
          text: `${willCost}`,
        },
      ],
      attrs: {
        style: `color: ${themeMainColor}`,
      },
    },
    {
      type: 'text',
      text: meetType === 1 ? '元' : '件',
    },
    {
      type: 'text',
      text: `可${willDiscountType === 1 ? '减' : '打'}`,
    },
    {
      name: 'span',
      children: [
        {
          type: 'text',
          text: `${willDiscount}`,
        },
      ],
      attrs: {
        style: `color: ${themeMainColor}`,
      },
    },
    {
      type: 'text',
      text: willDiscountType === 2 ? '折' : '元',
    },
  ];

  const nodesChildren = [];

  if (isMaximumDiscount) {
    nodesChildren.push({
      type: 'text',
      text: `已满${hadCost}${meetType === 1 ? '元' : '件'}，${hadDiscountCopy}`,
    });
  } else {
    if (hadDiscountCopy.length > 0) {
      nodesChildren.push({
        type: 'text',
        text: `${hadDiscountCopy}，`,
      });
    }

    Array.prototype.push.apply(nodesChildren, nextDiscountNode);
  }

  const nodes = [
    {
      name: 'p',
      attrs: {
        style: 'font-size: 12px',
      },
      children: nodesChildren,
    },
  ];

  return nodes;
};

const getBaleNode = (groupInfo) => {
  const { constantPrice, constantPriceNum } = groupInfo;
  return [
    {
      name: 'p',
      attrs: {
        style: 'font-size: 12px',
      },
      children: [
        {
          type: 'text',
          text: `${constantPrice / 100}元任选${constantPriceNum}件`,
        },
      ],
    },
  ];
};

const getMakeUpBaleNode = (groupInfo, { themeMainColor = '#F40F0F' }) => {
  const { constantPrice, constantPriceNum } = groupInfo;
  return [
    {
      name: 'p',
      attrs: {
        style: 'font-size: 12px',
      },
      children: [
        {
          type: 'text',
          text: '以下商品',
        },
        {
          name: 'span',
          children: [
            {
              type: 'text',
              text: `${constantPrice / 100}`,
            },
          ],
          attrs: {
            style: `color: ${themeMainColor}; padding: 0 3px;`,
          },
        },
        {
          type: 'text',
          text: '元任选',
        },
        {
          name: 'span',
          children: [
            {
              type: 'text',
              text: `${constantPriceNum}`,
            },
          ],
          attrs: {
            style: `color: ${themeMainColor}; padding: 0 3px;`,
          },
        },
        {
          type: 'text',
          text: '件',
        },
      ],
    },
  ];
};

const getHighestDiscountNode = (groupInfo, { themeMainColor = '#F40F0F' }) => {
  const { willCost, meetType } = groupInfo;

  let theHighestDiscountNode = null;

  try {
    const discountList = JSON.parse(groupInfo.discountListInfo);
    const [{ info }] = getDiscountInfo([discountList?.[0]]);

    if (info) {
      theHighestDiscountNode = [
        {
          name: 'p',
          attrs: {
            style: 'font-size: 12px',
          },
          children: [
            {
              type: 'text',
              text: `部分商品购${info}，还差`,
            },
            {
              name: 'span',
              children: [
                {
                  type: 'text',
                  text: `${meetType === 1 ? willCost / 100 : willCost}`,
                },
              ],
              attrs: {
                style: `color: ${themeMainColor}`,
              },
            },
            {
              type: 'text',
              text: meetType === 1 ? '元' : '件',
            },
          ],
        },
      ];
    }
  } catch (error) {
    console.error('Error discount_list_info:', error);
  }

  return theHighestDiscountNode;
};

/**
 * 获取凑单文案
 * @param {[{meetType: 0|1, isMaximumDiscount: boolean, discountType: 0|1, hadCost: number, willCost: number, hadCost: number, willDiscount: number }]} groupInfoList
 */
export function computeDiscountCopy(
  groupInfoList,
  options = { themeMainColor: '#F40F0F' }
) {
  return groupInfoList.map((groupInfo) => {
    if (groupInfo.promotionType === 109) {
      return getMakeUpBaleNode(groupInfo, options);
    }
    return getDiscountNode(groupInfo, options);
  });
}

function handleParseNode(nodeInfo, extraStyle) {
  return nodeInfo.map((i) => {
    let text = i.content;

    const createChildeTextNode = (text) => ({
      type: 'text',
      text,
    });

    if (i.color) {
      text = ` ${i.content} `;
      return {
        type: 'node',
        name: 'span',
        attrs: {
          style: extraStyle,
        },
        children: [createChildeTextNode(text)],
      };
    }
    return createChildeTextNode(text);
  });
}

/** 更具后端返回的数据，生成rich-text需要的数据结构，只有凑单信息 */
/**
 *
 * @param {object} param0 凑单信息
 * @param {*} ellipsis 是否要省略
 * @returns
 */
export function computePrefixInfoNodeByJson(
  { meetInfos, recommendInfos, operateInfo },
  extraStyle = '',
  isText = false
) {
  try {
    operateInfo = JSON.parse(decodeURIComponent(operateInfo));
  } catch (error) {
    operateInfo = [];
  }
  try {
    meetInfos = JSON.parse(decodeURIComponent(meetInfos)).map((i) => ({
      ...i,
      type: 'meetinfo',
    }));
  } catch (error) {
    meetInfos = [];
  }
  try {
    recommendInfos = JSON.parse(decodeURIComponent(recommendInfos)).map(
      (i) => ({
        ...i,
        type: 'recommendinfo',
      })
    );
  } catch (error) {
    recommendInfos = [];
  }
  if (isText) {
    return meetInfos
      .concat(recommendInfos)
      ?.map((i) => {
        if (i.color) {
          return ` ${i.content} `;
        }
        return i.content;
      })
      .join('');
  }
  const info =
    meetInfos.concat(recommendInfos).length > 0
      ? [
          {
            type: 'node',
            name: 'p',
            attrs: {
              style: `max-width: 100%;overflow: hidden;white-space: nowrap;word-break: break-all;font-size: 12px;text-overflow: ellipsis;`,
            },
            children: handleParseNode(
              meetInfos.concat(recommendInfos),
              extraStyle
            ),
          },
        ]
      : [];
  const oper =
    operateInfo.length > 0 ? handleParseNode(operateInfo, extraStyle) : [];
  return { info, oper };
}

/**
 * 获取首个营销活动凑单文案
 */
export function computeHighestDiscount(
  theHighestDiscountGroup,
  options = { themeMainColor: '#F40F0F' }
) {
  if (!theHighestDiscountGroup) {
    return null;
  }

  const { promotionType } = theHighestDiscountGroup;

  if (promotionType === 109) {
    return getBaleNode(theHighestDiscountGroup, options);
  }

  return theHighestDiscountGroup.hadDiscount
    ? getDiscountNode(theHighestDiscountGroup, options)
    : getHighestDiscountNode(theHighestDiscountGroup, options);
}

export function getDiscountInfo(discountList) {
  return discountList.map((discount) => {
    const {
      preferentialType,
      preferentialValue,
      conditionType,
      conditionValue,
    } = discount;
    const type1 = preferentialType === 1 && conditionType === 1; // 满件减元
    const type2 = preferentialType === 1 && conditionType === 2; // 满元减元
    const type3 = preferentialType === 2 && conditionType === 1; // 满件打折
    const type4 = preferentialType === 2 && conditionType === 2; // 满元打折

    if (!preferentialValue) return {};

    if (type1) {
      return {
        info: `满${conditionValue}件减${preferentialValue / 100}元`,
        price: `- ¥${preferentialValue / 100}`,
      };
    }
    if (type2) {
      return {
        info: `满${conditionValue / 100}元减${preferentialValue / 100}元`,
        price: `- ¥${preferentialValue / 100}`,
      };
    }
    if (type3) {
      return {
        info: `满${conditionValue}件打${preferentialValue / 10}折`,
        price: `${preferentialValue / 10}折`,
      };
    }
    if (type4) {
      return {
        info: `满${conditionValue / 100}元打${preferentialValue / 10}折`,
        price: `${preferentialValue / 10}折`,
      };
    }

    return {};
  });
}

export function convertOrderDetailDiscount(activities = []) {
  return activities
    .filter((activity) => activity.type === 'meetReduce')
    .map((activity) => {
      const meetType = +activity.extraInfo.meetType;
      let { promotionCondition, discount, decrease } = activity;
      const isDiscount = typeof discount === 'number';
      if (meetType === 1) {
        promotionCondition /= 100;
      }

      discount /= 10;
      decrease /= 100;

      return {
        info: `已满 ${promotionCondition} ${
          meetType === 1 ? '元' : '件'
        }，下单${isDiscount ? '打' : '减'} ${
          isDiscount ? discount : decrease
        } ${isDiscount ? '折' : '元'}`,
        price: `- ¥${decrease}`,
      };
    });
}

export function convertOrderDetailDiscountNew(activities = []) {
  return activities
    .filter((activity) =>
      [
        'meetReduce',
        'bale',
        'secondHalfDiscount',
        'storedCustomerDiscount',
        '1014',
        'rechargeOrderFree',
        'pointDeduction',
      ].includes(activity.type)
    )
    .map((activity) => {
      const { name, id, decrease, discount } = activity;
      if (activity.type === 'meetReduce') {
        const meetType = +activity.extraInfo.meetType;
        let { promotionCondition } = activity;
        const isDiscount = typeof discount === 'number';
        if (meetType === 1) {
          promotionCondition /= 100;
        }
        return {
          id,
          detail: `已满 ${promotionCondition} ${
            meetType === 1 ? '元' : '件'
          }，下单${isDiscount ? '打' : '减'} ${
            isDiscount ? discount / 10 : decrease / 100
          } ${isDiscount ? '折' : '元'}`,
          typeDesc: name,
          decreaseAmount: decrease,
        };
      }
      return {
        id,
        detail: name,
        typeDesc: name,
        decreaseAmount: decrease,
      };
    });
}

export function convertOrderConfirmActivities(activities = []) {
  return values(
    activities
      .filter(({ isCanUse }) => isCanUse)
      .reduce((ret, activity) => {
        const { id, type, decreaseAmount } = activity;
        // 506 和 storedCustomerDiscount 都表示储值专项优惠，神奇的后端实现
        if (
          !['107', '109', '115', '506', 'storedCustomerDiscount'].includes(type)
        ) {
          return ret;
        }
        if (!ret[id]) {
          ret[id] = { ...activity };
        } else {
          ret[id].decreaseAmount += decreaseAmount;
        }
        return ret;
      }, {})
  ).map(({ id, type, typeDesc, decreaseAmount, ...oth }) => {
    if (type === '107') {
      const { meetValue, meetType, discount, denominations } = oth;
      return {
        id,
        typeDesc,
        detail: `满${
          meetType === 1 ? `${meetValue / 100}元` : `${meetValue}件`
        }，${discount ? `打${discount / 10}折` : `减${denominations / 100}元`}`,
        decreaseAmount,
      };
    }
    if (type === '115') {
      return {
        id,
        typeDesc,
        detail: typeDesc,
        decreaseAmount,
      };
    }
    const { constantsPrice, constantsPriceNum } = oth;
    return {
      id,
      typeDesc,
      detail: `${constantsPrice / 100}元任选${constantsPriceNum}件`,
      decreaseAmount,
    };
  });
}
