// 配置信息合并生成

export function assignMinappConfig({
  isCustomGroups,
  groups,
  selectedGroupId,
  multiplayerCartConfig,
  addMealConfig,
  navigationBarTitle,
  reservationSettingConfig,
  surchargeInfoResponse,
  supportMultiSkuSwitchConfigResponse,
  mealTakingModeResponse = {},
  decoratePictureResponse = {},
  shoppingBagQueryResponse,
  scanVerificationConfigResponse = {},
  queueReminderConfigResponse = {},
  // 自助结账是否切换微信的扫一扫
  switchWechatScanResponse: switchWxScanResponse = { status: 0 },
  ...rest
} = {}) {
  const app = getApp();
  let paymentConfig = 2;
  const ticketConfigKey = 'ticket_stamp_time';
  const isScanFromTable = app.globalData.scanFromTable;

  if (isScanFromTable && rest[ticketConfigKey]) {
    paymentConfig = rest[ticketConfigKey];
  }

  this.setData({
    takeConfig: reservationSettingConfig?.status
      ? reservationSettingConfig
      : null,
    productPictures: decoratePictureResponse.pictureUrls || [],
    currentSwiperIndex: 0,
    mealTakingModeResponse,
    queueReminderConfigResponse,
    switchWxScanResponse
  });
  return Object.assign(app.globalData.retailConfig, {
    isCustomGroups,
    isMultiCart: multiplayerCartConfig,
    // 扫桌码 且 配置 为 true，才是加餐场景
    isSupportAddMeal: isScanFromTable && addMealConfig,
    isSupportAddMealOpen: addMealConfig,
    navigationBarTitle,
    paymentConfig,
    groups,
    selectedGroupId,
    surchargeInfoResponse,
    supportMultiSkuSwitchConfigResponse,
    takeConfig: reservationSettingConfig ?? {},
    shoppingBagQueryResponse,
    scanVerificationConfigResponse,
    mealTakingModeResponse,
    queueReminderConfigResponse,
    scanBuyTangshiConfig: rest?.scanBuyTangshiConfig ?? false,
    switchWxScanResponse
  });
}
