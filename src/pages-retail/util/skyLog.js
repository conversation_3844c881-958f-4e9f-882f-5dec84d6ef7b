import <PERSON>an<PERSON>eappLogger from '@youzan/zan-weapp-logger';

const app = getApp();
const APP_NAME = 'retail-node-h5';

/**
 * @param {*} options 日志数据
 * @param {String} options.message 日志内容，比如 a is not defined
 * @param {String} options.detail 日志详情
 * @param {String} options.level 日志等级，比如 error info
 * @param {String} options.extra 拓展信息
 *
 */
function log(options) {
  try {
    ZanWeappLogger.log({
      appName: APP_NAME,
      ...options,
      message:
        `【buyerId:${app.getBuyerId()},kdtId:${app.getKdtId()}】` +
        options.message,
      name: options.name || 'app-log',
      level: options.level || 'info',
      extra: {
        ...options.extra,
        appId: app.getAppId(),
        appVersion: app.getVersion(),
      },
    });
  } catch (error) {
    console.error('log error', error);
  }
}

export default log;
