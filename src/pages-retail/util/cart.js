// 购物车相关工具函数

/**
 * 根据买家信息拆分购物车内容
 *
 * @param {Array<Object>} cartItems
 * @returns {Object} itemsByBuyerId
 */
export function splitItemsByBuyerIdForRender(items, key = 'productBuyers') {
  const itemsByBuyerId = {};
  let nickNameIndex = 1;

  items.forEach((item, index) => {
    const buyers = item[key] || [];

    if (buyers.length === 0) return [];

    buyers.forEach((buyer) => {
      const key = buyer.buyerId;

      const currentItem = {
        itemIndex: index,
        actualNum: buyer.num,
        ...item,
        unitToShow: `X${buyer.num}`,
      };

      const currentCartItem = itemsByBuyerId[key];

      // 判断 对象中是否已有对应 buyer 的 key
      if (currentCartItem) {
        currentCartItem.items.push(currentItem);
      } else {
        if (!buyer.avatar) {
          buyer.avatar =
            'https://img01.yzcdn.cn/upload_files/2023/06/14/Fhh27rMb8reNr5Zy1cITwZDjYvvJ.png';
        }
        if (!buyer.nickName) {
          buyer.nickName = '微信用户_' + nickNameIndex++;
        }
        Object.assign(itemsByBuyerId, {
          [key]: {
            buyer,
            items: [currentItem],
          },
        });
      }
    });
  });

  return Object.values(itemsByBuyerId).map((item) => ({
    id: item.buyer.buyerId,
    ...item,
  }));
}
