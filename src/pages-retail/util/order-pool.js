/* 拼单通用全局数据 */
const app = getApp();
const orderPool = (app.globalData.orderPool = {});

// 当前拼单id
function setCurrentId(id) {
  orderPool.id = +id;
}

function getCurrentId() {
  return orderPool.id;
}

// 当前拼单方式
function setCurrentMode(mode) {
  orderPool.mode = +mode;
}

function getCurrentMode() {
  return orderPool.mode;
}

// 当前店铺
function setCurrentStore(store) {
  orderPool.store = store;
}

function getCurrentStore() {
  return orderPool.store ?? null;
}

// 当前地址
function setCurrentAddress(address) {
  orderPool.address = address;
}

function getCurrentAddress() {
  return orderPool.address ?? null;
}

// 派仓
function setWarehouse(warehouse) {
  orderPool.warehouse = warehouse;
}

function getWarehouse() {
  return orderPool.warehouse || {};
}

// 清空数据
function clear() {
  setCurrentId(null);
  setCurrentStore(null);
  setCurrentAddress(null);
  setWarehouse(null);
}

export default {
  setCurrentId,
  getCurrentId,
  setCurrentMode,
  getCurrentMode,
  setCurrentStore,
  getCurrentStore,
  setCurrentAddress,
  getCurrentAddress,
  setWarehouse,
  getWarehouse,
  clear
};
