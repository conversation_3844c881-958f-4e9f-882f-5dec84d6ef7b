/**
 * 目前只有储值余额和储值卡，没有礼品卡的概念，储值卡可以开启互斥选项，选择互斥卡时不能使用营销活动优惠
 * @type {{STORED_CARD: number, STORED_VALUE: number}}
 */
const CARD_TYPE = {
  STORED_VALUE: 1001, // 储值余额
  STORED_CARD: 1002, // 储值卡
};

/**
 * 获得默认选中的余额和卡
 * @param cardList
 * @returns {*[]}
 */
export function getSelectedValueCard(cardList = []) {
  if (cardList.length) {
    const card = cardList.filter(({ selected, usable }) => selected && usable);
    return [...card];
  }
  return [];
}

/**
 * 获得全部的余额和卡，区分可用和不可用
 * @param cardList
 * @returns {{usable: {storedCard: [], storedValue: []}, unusable: {storedCard: [], storedValue: []}}|null}
 */

export function getStoreValueAndCard(cardList = []) {
  if (cardList.length) {
    const result = {
      usable: {
        storedValue: [],
        storedCard: [],
      },
      unusable: {
        storedValue: [],
        storedCard: [],
      },
    };
    cardList.forEach((card) => {
      if (card.usable) {
        if (card.cardSubType === CARD_TYPE.STORED_VALUE) {
          result.usable.storedValue.push(card); // 可使用的储值余额
        } else {
          result.usable.storedCard.push(card); // 可使用的储值卡
        }
      } else if (card.cardSubType === CARD_TYPE.STORED_VALUE) {
        result.unusable.storedValue.push(card); // 不可使用的储值余额
      } else {
        result.unusable.storedCard.push(card); // 不可使用的储值卡
      }
    });
    return result;
  }
  return null;
}

/**
 * 获取储值卡 id 列表，和是否有互斥卡
 * @param cards
 * @returns {{isExclusion: boolean, cardIds: string[]}}
 */
export function getIdsAndPromotion(cards = {}) {
  return {
    cardIds: Object.keys(cards),
    isExclusion: Object.values(cards).some((item) => item.isExclusion),
  };
}

export function checkSingleCard(cards = {}, ctx) {
  const { lastChangeCard, isAbandon } = ctx;

  if (isAbandon) {
    return getIdsAndPromotion(cards);
  }

  // 对于未处于放弃优惠状态的用户 检查他上次选的卡里是不是有专享卡 如果有把其它卡去掉
  if (!isAbandon || typeof lastChangeCard !== 'undefined') {
    const { isSinglePayCard = false, selected } = lastChangeCard || {};
    const cardKeys = Object.keys(cards);
    const result = {};

    // 选中【非专享卡】时 检查已选的卡里有没有专享卡 如果有就把专享卡去掉
    if (!selected && !isSinglePayCard) {
      cardKeys.forEach((key) => {
        if (!cards[key].isSinglePayCard) {
          result[key] = cards[key];
        }
      });

      return getIdsAndPromotion(result);
    }
  }

  return getIdsAndPromotion(cards);
}

export function getBenefitCard(cards, ctx, cardIds) {
  const { useBenefit: benefit, isAbandon, rejectMemberBenefit } = ctx;

  if (!cards || typeof cards !== 'object') return '';

  const cardKeys = Object.keys(cards);
  let text = '';

  cardKeys.forEach((key) => {
    if (Array.isArray(cardIds) && !cardIds.includes(key)) return;

    const { benefitCard } = cards[key] || {};

    if (benefitCard) {
      const { balance, summaryCardNo: giftCardNo } = cards[key];
      const { benefitCardNo } = benefitCard;

      let useBenefit = true;

      if (isAbandon || rejectMemberBenefit) {
        useBenefit = false;
      } else if (benefit) {
        const { cardNo } = benefit.extraMap || {};
        useBenefit = benefitCardNo === cardNo;
      }

      text = JSON.stringify({
        benefitCardNo,
        balance,
        giftCardNo,
        useBenefit,
      });
    }
  });

  return text;
}

/**
 * 创建订单时获取预付卡参数
 */
export function getPrepaid({
  selectedValueCard = [],
  cardPricePay = 0,
  storeThenPay = false,
  balanceMkExclusion = false,
  memberBenefit = {},
  isAbandon = false,
}) {
  // 充值并支付 不传 pcl 即不扣卡里的钱
  let pcl = storeThenPay
    ? []
    : selectedValueCard.map((item) => {
        const result = { cardNo: item.summaryCardNo };

        const { benefitCard } = item;
        if (benefitCard) {
          const { benefitCardNo } = benefitCard;

          const { extraMap = {} } = memberBenefit;
          const { cardNo } = extraMap;

          result.benefitCardNo = benefitCardNo;
          result.useBenefit = !isAbandon && benefitCardNo === cardNo;
        }

        return result;
      });

  return JSON.stringify({
    isMutex:
      // 充值并支付时(没有选中的卡) 且互斥时 要取 balanceMkExclusion 来判断是否互斥
      selectedValueCard.some((item) => item.isExclusion) ||
      (storeThenPay && balanceMkExclusion),
    pcl,
    // 如果有选卡&合计价格>0, 传储值支付价格
    prepaidPayAmount: pcl.length ? cardPricePay : 0,
  });
}

/**
 * 计算选择储值卡金额总和
 * @param {*} selectedValueCard
 * @returns
 */
export function getSelectValueCardTotal(selectedValueCard = []) {
  return selectedValueCard.reduce((total, item) => item.balance + total, 0);
}
