import promisify from 'utils/promisify';
import pick from '@youzan/weapp-utils/lib/pick';
import mapKeyCase from '@youzan/weapp-utils/lib/map-keys-case';
// import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
// import usePagination from 'retail/util/use-pagination';
import { getReatilSource, isFreeGo } from 'retail/util/global';

const app = getApp();
const carmen = promisify(app.carmen);
const retailCarmen = promisify(app.retailCarmen);

const getGlobalRetailConfig = () => {
  return app.globalData.retailConfig || {};
};

const getAdminId = () => ({ adminId: app.getBuyerId() });

export const req = (path, method = 'GET', data) => {
  return app.retailRequest({
    path,
    method,
    data: {
      ...data,
      retailSource: getReatilSource(),
    },
  });
};

const COMMON_CHAR = {
  version: '1.0.0',
};
const REQUEST_PREFIX = {
  H5_PREFIX: '/retail/h5/miniprogram/',
  CARMEN_PREFIX: 'youzan.retail.',
  h5Cart: '/retail/h5/cart/',
};

// 格式化套餐数据
const formatComboData = (data) => {
  const { combo_detail: { combo_groups = [] } = {}, combo_mark = {} } = data;
  combo_groups.forEach((item) => {
    item.combo_sub_items = item.combo_sub_items.reduce((pre, subItem) => {
      const { sku_relateds } = subItem;
      // 固定套餐子商品sku平铺
      if (
        combo_mark.combo_type === 0 &&
        sku_relateds &&
        sku_relateds.length > 1
      ) {
        sku_relateds.forEach((sku) => {
          pre.push({
            ...subItem,
            isSoldOut: !sku.stock_num > 0 || !(subItem.is_display === 1),
            sku_relateds: [sku],
          });
        });
      } else {
        subItem.isSoldOut = !sku_relateds.find(
          ({ stock_num }) => stock_num > 0
        );
        pre.push(subItem);
      }
      return pre;
    }, []);
  });
};

// 获取先付后吃就餐人数
export const getDinerNum = () => {
  return (
    (!getGlobalRetailConfig().isSupportAddMealOpen &&
      app.globalData.needPayLaterEatDinerNum) ||
    null
  );
};

// 查询扫码点单 | 自助结账 在总部开启的门店列表
export function queryOpenShops() {
  const kdtId = app.getHQKdtId();

  if (!kdtId) return Promise.resolve({ joinType: 0, subKdtIds: [] });

  return app.request({
    path: `${REQUEST_PREFIX.H5_PREFIX}queryOpenShops.json`,
    data: {
      retailSource: getReatilSource(),
      kdtId,
    },
  });
}

export const querySKU = ({ itemId }) =>
  retailCarmen({
    api: `${REQUEST_PREFIX.CARMEN_PREFIX}product.search.offline.queryone/${COMMON_CHAR.version}/forcustomer`,
    data: {
      item_id: itemId,
      with_display_off: false,
    },
  }).then((res) => {
    const { combo_mark = {} } = res;
    if (combo_mark.is_combo) {
      formatComboData(res);
    }
    return res;
  });

// 获取核销码数据
export const getTakeCodeData = (orderNo) => {
  const kdtId = app.getKdtId();
  return carmen({
    api: `kdt.trade.bill/${COMMON_CHAR.version}/payResult`,
    data: {
      orderParams: {
        kdtId,
        orderNo,
      },
    },
  });
};

/**
 * 降级获取商品ids
 * @param {Number} rootKdtId 总部kdtId
 * @param {Number} childKdtId 要查询的分店kdtId
 * @param {Array[Number]} itemIds 总部的商品itemIds
 */
export const queryChildGoodsByParentGoods = (itemIds) => {
  const rootKdtId = app.getHQKdtId();
  const childKdtId = app.getKdtId();
  return app
    .request({
      path: '/wscshop/weapp/goods/query_item_ids.json',
      data: {
        rootKdtId,
        childKdtId,
        itemIds,
      },
    })
    .then(mapKeyCase.toCamelCase);
};

/**
 * 24小时货架获取点单页或首页配置
 * @param {Number} kdtId 总店kdtId
 * @param {Number} type  1：首页， 2：点单页
 */
export function fetchApplyConfig(kdtId, type) {
  return carmen({
    api: `${REQUEST_PREFIX.CARMEN_PREFIX}trade.misc.shelf/${COMMON_CHAR.version}/getapplyconfig`,
    data: { kdt_id: kdtId, type },
  });
}

export function getUmp(type, ids, additionNum, autoAddition, hideEmptyCoupon) {
  const param = {
    activity_type: type,
    ...(autoAddition
      ? {
          auto_addition: autoAddition,
          addition_num: additionNum,
        }
      : { ids }),
  };
  if (hideEmptyCoupon) {
    param.hide_empty_coupon = !!hideEmptyCoupon;
  }

  return carmen({
    api: `${REQUEST_PREFIX.CARMEN_PREFIX}trade.misc.shelf/${COMMON_CHAR.version}/getactivitydetail`,
    data: param,
  });
}

// export const fetchGoodsByGroup = usePagination(
//   ({ groupIds, name, pageNo = 1, pageSize = 20 }) => {
//     if (!(groupIds || name)) return Promise.resolve();
//     return retailCarmen({
//       api: `${REQUEST_PREFIX.CARMEN_PREFIX}product.search.offline.api.search/1.0.0/forcustomer`,
//       data: {
//         [name ? 'name_or_sku_no' : 'group_ids']: name || groupIds,
//         page_size: pageSize,
//         page_no: pageNo,
//       },
//     }).then(({ items, paginator }) => {
//       return {
//         data: items
//           .filter((goods) => goods.measurement !== 10)
//           .map((item) =>
//             Object.assign(item, {
//               photo_url: cdnImage(item.photo_url, '!100x100+2x.jpg'),
//               price: item.price,
//               groupId: groupIds?.[0],
//             })
//           ),
//         total: paginator.total_count,
//       };
//     });
//   }
// );

const adaptSpu = (data) => {
  const item = data.item_info_by_sku_no_model;
  if (item) {
    const {
      item_sku_models: skuModels = [],
      item_props: goodsProps = [],
      sku_no: skuNo,
    } = item;
    const information = pick(item, 'price', 'title', 'picture');

    return {
      information,
      skuModels,
      skuNo,
      goodsProps,
    };
  }
  return null;
};

export const addShopBagToCart = (
  cartId,
  { uniqueKey, title, price, picUrl, goodsNum = 1 }
) => {
  const data = mapKeyCase.toSnakeCase({
    cartId,
    uniqueKey,
    title,
    price,
    goodsNum,
    pictureUrl: picUrl,
    productType: 3,
  });
  return retailCarmen({
    api: `${REQUEST_PREFIX.CARMEN_PREFIX}trade.customer.cart.nospu/${COMMON_CHAR.version}/add`,
    data,
  }).then(adaptSpu);
};

export const addSurchargeGoodsToCart = (
  cartId,
  {
    uniqueKey,
    title,
    pictureUrl,
    price,
    subTitle,
    goodsNum = 1,
    extraInfo = {},
  }
) => {
  const data = {
    cartId,
    uniqueKey,
    title,
    pictureUrl,
    price,
    goodsNum,
    subTitle,
    productType: 4,
    isSnacks: true,
  };

  !subTitle && delete data.subTitle;

  return req(`${REQUEST_PREFIX.h5Cart}addNoSkuGoods.json`, 'POST', {
    ...data,
    ...extraInfo,
    dinerNum: getDinerNum(),
  }).then(adaptSpu);
};

const newFreeGoAddGoods = (data) => {
  // TODO: remove kdtId param passing
  return req(`${REQUEST_PREFIX.H5_PREFIX}freebuyAdd.json`, 'POST', {
    kdtId: app.getOfflineKdtId(),
    ...data,
  });
};

const newBatchFreeGoAddGoods = (data) => {
  return req(`${REQUEST_PREFIX.H5_PREFIX}batchFreebuyAdd.json`, 'POST', {
    kdtId: app.getOfflineKdtId(),
    data,
  });
};

const newScanGoAddGoods = (data) => {
  return req(`${REQUEST_PREFIX.H5_PREFIX}scanbuyAdd.json`, 'POST', data);
};

export const getUmpActivity = (data) => {
  return req(`${REQUEST_PREFIX.H5_PREFIX}makeUpActivity.json`, 'GET', {
    ...getAdminId(),
    ...data,
  });
};

export const getUmpActivities = (data) => {
  return req(`${REQUEST_PREFIX.H5_PREFIX}makeUpActivities.json`, 'GET', {
    ...getAdminId(),
    ...data,
  });
};

// 批量更新商品数量，增量更新
export const updateBatchGoodsCount = (data) => {
  return req(`${REQUEST_PREFIX.H5_PREFIX}updateBatchGoodsCount.json`, 'GET', {
    ...getAdminId(),
    ...data,
  });
};

const getFreeGoAddParams = (options = {}) => {
  const {
    priceAdjustmentCheck = true,
    goodsGroupId,
    num = 1,
    detailModels,
    cartId,
    skuId,
    goodsId,
    comboInfo = {},
    extraInfo = {},
    isMustHaveItem = false, // 必选品标记
  } = options;
  const goodsData = {
    num,
    priceAdjustmentCheck,
    skuId,
    goodsId,
    goodsProps: detailModels,
    isMustHaveItem,
    ...comboInfo,
  };
  // 只有先付后吃才传用餐人数
  goodsGroupId && (goodsData.goodsGroupId = goodsGroupId);
  return {
    cartId,
    containProp: (detailModels || []).length > 0,
    ...getAdminId(),
    goods: goodsData,
    // 先付后吃传用餐人数
    dinerNum: getDinerNum(),
    ...extraInfo,
  };
};

export const addGoodsToCart = (code, num = 1, options = {}) => {
  // 扫码点单采用新接口
  if (isFreeGo()) {
    return newFreeGoAddGoods(getFreeGoAddParams({ ...options, num }));
  }

  const {
    priceAdjustmentCheck = true,
    detailModels,
    cartId,
    skuId,
    goodsId,
  } = options;

  let params = {
    num,
  };

  if (skuId && goodsId) {
    params = {
      ...params,
      goodsSkuNo: code,
      priceAdjustmentCheck,
      skuId,
      goodsId,
      goodsProps: detailModels,
    };
  } else {
    params.goodsSkuNo = code;
  }

  return newScanGoAddGoods({
    cartId,
    containProp: true, // detailModels?.length > 0,
    ...getAdminId(),
    goods: params,
  });
};

/**
 * 批量添加购物车
 */
export const batchAddGoodsToCart = ({ goods = [], cartId }) => {
  const data = goods.map((item) => {
    return getFreeGoAddParams({ ...item, cartId });
  });
  return newBatchFreeGoAddGoods(data);
};

const injectCartId = (cartId) => cartId && { cart_id: cartId };

export function updateCartData({
  amount: num,
  cartItemId,
  cartId,
  cartUniqueKey,
  buyerId,
}) {
  const goods = {
    cartItemId,
    num,
  };
  if (cartUniqueKey) {
    goods.cartUniqueKey = cartUniqueKey;
  }
  const data = {
    goods,
    retail_source: getReatilSource(),
    ...injectCartId(cartId),
  };
  if (buyerId) data.buyerId = buyerId;
  return req(`${REQUEST_PREFIX.h5Cart}editGoods.json`, 'GET', data);
}

export function deleteCartData(goodsList, cartId, isDeleteAll = false) {
  const list = Array.isArray(goodsList) ? goodsList : [goodsList];
  const goods = list.map((goods) =>
    pick(goods, ['cartItemId', 'cartUniqueKey'])
  );

  return req(`${REQUEST_PREFIX.h5Cart}deleteGoods.json`, 'GET', {
    goods,
    is_delete_all: isDeleteAll,
    retail_source: getReatilSource(),
    ...injectCartId(cartId),
  });
}

// const isSpecialQrCode = (content) => content.indexOf('goods_id') > -1;
// const getGoodsIdFromSpecialQrCode = (content) =>
//   content.slice(content.lastIndexOf('=') + 1);

// TODO: 代码有点长
// export const scan = async (code, config) => {
//   const getCode =
//     typeof code !== 'undefined'
//       ? Promise.resolve({ result: code })
//       : scanCode({ scanType: 'barCode' });

//   try {
//     const { result } = await getCode;
//     const code = isSpecialQrCode(result)
//       ? getGoodsIdFromSpecialQrCode(result)
//       : result;
//     const skuModel = await addGoodsToCart(code, 1, config);

//     const pages = getCurrentPages();
//     const currentPage = pages[pages.length - 1];
//     let cartPath = '/packages/retail/cart/index';

//     if (skuModel) {
//       cartPath += `?skuModel=${JSON.stringify(skuModel)}`;
//     }
//     if (cartPath.indexOf(currentPage.route) === -1) {
//       wx.navigateTo({
//         url: cartPath,
//         success() {
//           wx.showToast({
//             title: '添加商品成功',
//             icon: 'success',
//           });
//         },
//       });
//     }
//     return { skuModel, isSku: !!skuModel };
//   } catch (err) {
//     const hasPluPriceChangeErrorCode = 233501021;
//     const { code, msg, errMsg } = err;
//     if (code !== hasPluPriceChangeErrorCode) {
//       if (msg) {
//         wx.showModal({
//           content: msg,
//           title: '识别失败',
//           showCancel: false,
//         });
//       } else if (errMsg.indexOf('cancel') === -1) {
//         wx.showModal({
//           content: '扫码失败',
//           showCancel: false,
//         });
//       }
//       return { isCancel: true };
//     }

//     const { code: plu, tips } = JSON.parse(msg);

//     const { confirm } = await showModal({
//       content: tips,
//       showCancel: true,
//     });
//     return confirm
//       ? scan(plu, { priceAdjustmentCheck: false })
//       : { isCancel: true };
//   }
// };

export const adapter = (
  { cartItemList = [], unavailableCartItemList: unavailable = [] },
  noPlu = false
) => {
  const a2b = (item) => ({
    productSkuId: item.skuId,
  });

  const goodsList = cartItemList
    .filter((item) => {
      return noPlu ? !item.fromPlu && item.pricingStrategy !== 10 : true;
    })
    .map((item) => {
      // TODO: 需要重构
      const { promotion = {}, amount, pricingStrategy, unit, price } = item;
      const promotionTags = [].concat(promotion.typeDesc ?? []);
      const promotionType = promotion.type;

      const priceInformation = {};

      if (!item.originPrice && price) {
        Object.assign(priceInformation, {
          originPrice: price.originPrice,
          salePrice: price.salePrice,
        });
      }

      let adjustAmount = amount;

      if (pricingStrategy === 10) {
        adjustAmount /= 1000;
      }

      const unitToShow =
        pricingStrategy === 10
          ? `${adjustAmount.toFixed(2)} ${unit}`
          : `x${adjustAmount}`;

      return Object.assign(item, {
        ...a2b(item),
        ...priceInformation,
        unitToShow,
        amount: +adjustAmount,
        promotionTags,
        promotionType,
      });
    });

  const unavailableGoodsList = unavailable.map((item) => {
    let { amount } = item;
    const { price } = item;
    const priceInformation = {};

    if (!item.originPrice && price) {
      Object.assign(priceInformation, {
        originPrice: price.originPrice,
        salePrice: price.salePrice,
      });
    }

    if (item.pricingStrategy === 10) {
      amount /= 1000;
    }
    return Object.assign(item, {
      ...a2b(item),
      ...priceInformation,
      desc: item.invalidReason,
      num: amount,
    });
  });

  return {
    goodsList,
    unavailableGoodsList,
  };
};

const fetchCartData = (business) => (cartId) => {
  if (typeof business !== 'number') {
    throw Error('错误的 business 类型');
  }

  const params = {
    business,
    preOrdered: false,
    addMeal: getGlobalRetailConfig().isSupportAddMeal || false,
    needCalcJoinMemberPromotion: true,
    needRecommendCoupon: true,
    retailSource: getReatilSource(),
  };

  if (cartId) params.cartId = cartId;
  if (business === 1) params.autoAddGift = true;

  // 扫码点单传入履约方式自提(堂食\外带产品认为都是自提)
  if (business === 2) params.supportDeliveryChannelCoupon = true;

  // 替换购物车列表接口
  return req(`${REQUEST_PREFIX.h5Cart}list.json`, 'GET', params).then((res) => {
    const { goodsNo, ...rest } = res;

    return {
      ...rest,
      ...adapter(res, business === 2),
      skuNo: goodsNo,
    };
  });
};

export const fetchScanGoCart = fetchCartData(1);
export const fetchFreeGoCart = fetchCartData(2);

/**
 * 确认订单
 *
 * @param {*} params
 */
export const confirmOrder = ({
  uniqueId,
  cartId,
  cartItems,
  deliveryType,
  couponId = '',
  coupons = [],
  needRecommendCoupon = true,
  showCustomerAllCoupon,
  prepaidCardList = [],
  hasPromotion,
  supportAddMeal = false,
  filterPreOrdered = true,
  needHelpSelectCard = true,
  needRecommendStoredDiscount = true,
  setHasPromotion = false,
  selectedMemberBenefit,
  needRecommendMemberBenefit = true,
  isMutex,
  manuallySelectPointsDeduction,
  selectedGiftCardWithBenefit,
}) => {
  let params = {};
  // couponId 和 coupons 兼容处理，避免出现选择不使用优惠券的场景 couponPromotionIds没传
  if (couponId) {
    params = { couponPromotionId: couponId };
  } else {
    params = { couponPromotionIds: coupons.map((coupon) => coupon.id) };
  }

  params.needRecommendCoupon = needRecommendCoupon;

  if (coupons.length) {
    params.needRecommendCoupon = false;
  }

  if (selectedMemberBenefit) {
    params.selectedMemberBenefit = selectedMemberBenefit;
    params.needRecommendMemberBenefit = false;
  }
  if (!needRecommendMemberBenefit) params.needRecommendMemberBenefit = false;

  const payload = {
    ...(uniqueId ? { uniqueId } : {}),
    cartId,
    deliveryType,
    cartItems,
    supportAddMeal,
    filterPreOrdered,
    business: app.globalData.scene || 2,
    retailSource: getReatilSource(),
    needHelpSelectCard,
    needRecommendStoredDiscount,
    showCustomerAllCoupon,
    isMutex,
    manuallySelectPointsDeduction,
    selectedGiftCardWithBenefit,
    ...params,
  };

  if (Array.isArray(prepaidCardList) && prepaidCardList.length) {
    payload.hasPromotion = hasPromotion;
    payload.prepaidCardList = prepaidCardList;
  } else {
    payload.hasPromotion = !(filterPreOrdered && supportAddMeal);
  }

  if (setHasPromotion) {
    payload.hasPromotion = true;
  }

  // eslint-disable-next-line @youzan/koko/no-async-await
  return app
    .retailRequest({
      path: `${REQUEST_PREFIX.h5Cart}confirm.json`,
      method: 'POST',
      data: {
        ...payload,
        // 传个标 node 层处理这俩参数
        // supportCombinePay: true,
        // supportPieceThresholdCoupon: true,
        freego: true,
        needCalcJoinMemberPromotion: true,
        supportDeliveryChannelCoupon: true,
      },
    })
    .then((res) => {
      const { orderItemList, unavailableOrderItemList, ...rest } = res;

      return {
        ...rest,
        ...adapter({
          cartItemList: orderItemList,
          unavailableCartItemList: unavailableOrderItemList,
        }),
      };
    });
};

export const cancelOrderAPI = ({ orderNo, cartId }) => {
  return retailCarmen({
    api: `${REQUEST_PREFIX.CARMEN_PREFIX}trade.order.minapp/${COMMON_CHAR.version}/cancel`,
    data: {
      order_no: orderNo,
      buyer_cancel: true,
      ...(cartId && { cart_id: cartId }),
    },
    methods: 'POST',
  });
};

export const buyOrderAgain = ({ orderNo, cartId }) => {
  const data = { order_no: orderNo };
  cartId && (data.cart_id = cartId);
  const { tableCode } = getGlobalRetailConfig().tableInfo || {};
  tableCode && (data.table_num = tableCode);

  return retailCarmen({
    api: `${REQUEST_PREFIX.CARMEN_PREFIX}trade.customer.cart/${COMMON_CHAR.version}/createbyorder`,
    data,
  });
};

export function fetchOrderDetail({ orderNo, cartId } = {}) {
  return retailCarmen({
    api: `${REQUEST_PREFIX.CARMEN_PREFIX}trademanager.order.consumer.detail/${COMMON_CHAR.version}/search`,
    data: {
      order_no: orderNo,
      real_time_search: true,
      ...(cartId && { cart_id: cartId }),
    },
  }).then(mapKeyCase.toCamelCase);
}

export const fetchAppConfig = () => {
  return retailCarmen({
    api: `${REQUEST_PREFIX.CARMEN_PREFIX}trade.misc.sourceconfig/${COMMON_CHAR.version}/get`,
  })
    .then(mapKeyCase.toCamelCase)
    .then((res) => {
      const {
        retailSourceConfigResponse,
        groupingSettingResponse,
        surchargeInfoResponse,
        multiplayerCartConfig,
        multiPlayerSnacksConfig,
        ...rest
      } = res;
      const config = (
        (retailSourceConfigResponse || {}).configList || []
      ).reduce((config, currentConfig) => {
        config[currentConfig.configKey] = currentConfig.configValue;
        return config;
      }, {});
      return {
        ...pick(
          rest,
          'reservationSettingConfig',
          'scanBuyTangshiConfig',
          'shoppingBagConfig',
          'decoratePictureResponse',
          'shoppingBagQueryResponse',
          'scanVerificationConfigResponse',
          'supportMultiSkuSwitchConfigResponse',
          'mealTakingModeResponse',
          'queueReminderConfigResponse',
          'switchWechatScanResponse'
        ),
        navigationBarTitle: getShopTitle(groupingSettingResponse),
        ...getCustomGroupList(groupingSettingResponse),
        ...config,
        surchargeInfoResponse,
        multiplayerCartConfig,
        addMealConfig: multiplayerCartConfig && multiPlayerSnacksConfig,
      };
    });

  function hadSetGroups(customShopInfo) {
    return (customShopInfo || {}).showMode === 1;
  }

  function getCustomGroupList(customShopInfo) {
    if (hadSetGroups(customShopInfo)) {
      const { groupId, upperId } = customShopInfo.itemGroupList?.[0] || {};
      return {
        groups: customShopInfo.itemGroupList.map((group) => ({
          ...group,
          id: group.groupId,
          name: group.title,
        })),
        selectedGroupId: upperId || groupId,
        isCustomGroups: true,
      };
    }
    return {
      isCustomGroups: false,
    };
  }

  function getShopTitle(customShopInfo) {
    if (!customShopInfo) return;
    const navigationBarTitle =
      +customShopInfo.pageTitleMode === 1
        ? customShopInfo.pageTitle
        : app.getShopInfoSync().shop_name;

    return hadSetGroups(customShopInfo) ? navigationBarTitle : '扫码点单';
  }
};

export const fetchShopConfig = (keys) => {
  return req(`${REQUEST_PREFIX.H5_PREFIX}shop/getShopConfigs`, 'POST', {
    keys,
  });
};

export const checkWhiteList = () => {
  return req('/retail/h5/misc/checkSkipGetPhoneNumberWhiteList');
};

/**
 * 开桌
 * @param {Object} options
 * @param {number} options.tableId
 * @param {number} options.peopleNum
 */
export function openTable(options) {
  return req(
    `${REQUEST_PREFIX.H5_PREFIX}scan-code/open-table.json`,
    'POST',
    options
  );
}

/**
 * 点单页配置接口
 */
export const getOrderPageConfigs = () => {
  return req(
    `${REQUEST_PREFIX.H5_PREFIX}scan-code/getOrderPageConfigs.json`,
    'POST',
    {
      withItemLabel: true,
      supportFixGroupOptionalCombo: true,
      screenWidth: wx.getSystemInfoSync().screenWidth,
    }
  );
};

/**
 * 获取店铺状态
 */
export const queryShopStatus = () => {
  return req(`${REQUEST_PREFIX.H5_PREFIX}scan-buy/shop/status.json`);
};

/** 查询订单信息，辅助判断评价逻辑 */
export const queryOrderInfoByNo = (orderNo) => {
  return app.request({
    path: '/wsctrade/order/getOrderInfoByTrade.json',
    data: {
      order_no: orderNo,
    },
  });
};
