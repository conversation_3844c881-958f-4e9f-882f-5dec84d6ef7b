import promisify from 'utils/promisify';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { fetchApplyConfig } from './api';
import { setDetailDisplayType } from './shelf-global';

const app = getApp();
const carmen = promisify(app.carmen);

// 查询店铺详细信息
export const queryShopContact = () => {
  return app.request({
    path: '/wscshop/api/showcase-retail/shop/queryShopContact.json',
    method: 'GET'
  });
};

// buyerLat, buyerLng, shopLng, shopLat
// 查询店铺是否支持同城送或者自提
export const checkDeliverySupportWithoutItem = data => {
  return app.request({
    path:
      '/wscshop/api/showcase-retail/shop/checkDeliverySupportWithoutItemJson.json',
    method: 'GET',
    data
  });
};

// 获取外卖下 接单时间
// export const getOrderReceivingSetting = () => {
//   return carmen({
//     api: 'youzan.retail.trade.misc.shelf/1.0.0/getorderreceivingsettingforc'
//   });
// };

// 批量查询店铺营业状态
export const fetchShopStatus = kdtIds => {
  return app.request({
    method: 'GET',
    path: 'wscshop/api/showcase-retail/shop/queryShopBusinessStatuses.json',
    data: {
      kdtIds
    }
  });
};

/**
 * 获取网店列表
 * @param {*} data
 */
const fetchShopList = data => {
  return carmen({
    api: 'youzan.retail.shop.hq.consumer.delivery/1.0.0/search',
    data: {
      ...data,
      store_statuses: ['try', 'valid', 'protect'],
      append_online_business_hours: true,
      retail_source: 'retail'
    }
  }).then(async ({ items = [] } = {}) => {
    items.forEach(shop => {
      shop.isBusinessOpen =
        shop.onlineBusinessHours.currentBusinessStatus.isOpen;
    });
    return items;
  });
};

const doSearchShopList = (data = {}) => {
  const rootKdtId = app.getHQKdtId();
  wx.showLoading();

  const handleShopList = shopList => {
    const formatedShopList = shopList
      // .filter(({ storeKdtId }) => storeKdtId !== app.getKdtId())
      .map(shop => ({
        ...shop,
        unit: 'km'
      }));
    wx.hideLoading();
    return formatedShopList;
  };

  const hasLocation = data.lon && data.lat;

  return fetchShopList({
    kdt_id: rootKdtId,
    ...(hasLocation && {
      sort_lon: data.lon,
      sort_lat: data.lat,
      delivery_method: data.type,
      page_size: 50,
      page_no: 1
    })
  })
    .then(handleShopList)
    .catch(err => {
      wx.showToast({
        title: err?.msg ?? '无法加载门店列表',
        icon: 'none'
      });
      wx.hideLoading();
    });
};

// 自提
export const doSearchShopListWithSelfTake = data => {
  return doSearchShopList({ ...data, type: 2 });
};

// 同城送
export const doSearchShopListWithDelivery = data => {
  return doSearchShopList({ ...data, type: 1 });
};

// 切换店铺
export const handleChooseShop = async shop => {
  const { storeKdtId } = shop;
  if (!storeKdtId) {
    wx.showToast({ icon: 'none', title: '店铺信息不全，请重试' });
    return;
  }
  try {
    wx.showLoading();
    app.updateKdtId(storeKdtId);
    // 更新获取点单页配置信息
    const shelfOrderSettings = await fetchApplyConfig(storeKdtId, 2);
    shelfOrderSettings.components = shelfOrderSettings.components
      ? JSON.parse(shelfOrderSettings.components)
      : [];
    return {
      orderPageSettings: transformOrderPageSettings(shelfOrderSettings)
    };
  } catch (error) {
    wx.showToast({
      icon: 'none',
      title: error?.msg ?? `拉取店铺配置失败, 请重试`
    });
  } finally {
    wx.hideLoading();
  }
};

// 处理点单页配置数据
export const transformOrderPageSettings = settings => {
  let realSettings = {};
  if (settings) {
    const { templateId, components, goodsDetailDisplay } = settings;

    // 将商详展示类型值存储到全局
    setDetailDisplayType(goodsDetailDisplay);

    const orderPageSettingsHandleMap = {
      order_sheet_ad: component => {
        const { style } = component;
        const orderSheetAd = JSON.parse(style);
        return {
          orderSheetAd
        };
      },
      item_search: component => {
        const { style } = component;
        return {
          showSearchComponent: JSON.parse(style)?.show ?? true
        };
      },
      shop_title: component => {
        const { show, style } = component;
        return {
          showUmp: +show,
          shopHeaderConfig: style ? JSON.parse(style) : null
        };
      },
      general_item_group: component => ({ itemGroupData: component }),
      recommend_item_group: component => ({ recommendGroupData: component }),
      shop_activity: component => {
        const { activityComponentType } = component;
        const key = mapKeysCase.toCamelCase(`${activityComponentType}_config`);
        return {
          [key]: component
        };
      },
      // 点单页商品列表排版风格数据
      goods_info: ({ style }) => {
        return {
          goodsListStyle: style ? JSON.parse(style) : null
        };
      }
    };

    realSettings = components.reduce(
      (data, component) => {
        const { type } = component;
        if (!orderPageSettingsHandleMap[type]) {
          return data;
        }
        return { ...data, ...orderPageSettingsHandleMap[type](component) };
      },
      { goodsDetailDisplay, templateId }
    );
  }
  return realSettings;
};

// 当前店铺是否单店
export const isSingleShop = () => {
  return app.getShopInfoSync().shopMetaInfo.shop_role === 0;
};

// 当前店铺是否单店
export const isSingleShopPromise = () => {
  return app.getShopInfo().then(({ shopMetaInfo }) => {
    return shopMetaInfo.shop_role === 0;
  });
};

export const getCurrentShopName = () => {
  return app.getShopInfoSync().shopMetaInfo.shop_name;
};
