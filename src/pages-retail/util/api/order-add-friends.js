const app = getApp();
// const carmen = promisify(app.carmen);

const prefixPath = '/retail/h5/trade/order-add-friends';
/**
 * 查询最新引导加微活动
 * @param {number}} channelType: 适用渠道：1.扫码点单 2.点单宝
 * @param {number}} locationType: 显示位置：1.点单前 2.预结单页 3.支付结果页
 */
export function queryQrCode(data) {
  return app.retailRequest({
    path: `${prefixPath}/queryQrCode.json`,
    method: 'GET',
    data,
  });
}

/**
 * 扫码加标签
 *
 * @param {number}} activityId
 * @param {number}} baseId 微信二维码baseId
 * @param {string}} miniprogramTitle 小程序消息标题
 * @param {string}} miniprogramPage 小程序页面路径
 */
export function report(data) {
  return app.retailRequest({
    path: `${prefixPath}/report.json`,
    method: 'POST',
    data,
  });
}
