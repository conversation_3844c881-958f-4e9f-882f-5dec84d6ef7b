const app = getApp();
const prefix = '/prepaid/recommend/api/card/';
const r = 'recharge/';
const origin = 'cashier';
const method = 'POST';
// 签约开卡
function signAgreement(data) {
  return app.retailRequest({
    path: `${prefix}giftcard/${r}agreement/sign.json`,
    method,
    origin,
    data,
  });
}

// 储值预下单
function getPreOrder(data) {
  return app.retailRequest({
    path: `${prefix}pos/giftcard/${r}pre/order.json`,
    method,
    origin,
    data,
  });
}

// 查询充值的状态
function queryOrderStatus(data) {
  return app.retailRequest({
    path: `${prefix}recommend/${r}status.json`,
    method: 'GET',
    origin,
    data,
  });
}

// 查询规则礼包详情
function getGiftPacks(data) {
  return app.retailRequest({
    path: `${prefix}${r}giftpacks.json`,
    method,
    origin,
    data,
  });
}

export { signAgreement, getPreOrder, queryOrderStatus, getGiftPacks };
