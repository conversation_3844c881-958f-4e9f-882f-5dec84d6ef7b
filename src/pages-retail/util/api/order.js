// import promisify from 'utils/promisify';
import retailCarmen from 'retail/util/retail-carmen';
import { getReatilSource } from 'retail/util/global';

const app = getApp();
// const carmen = promisify(app.carmen);

/**
 * 根据桌号查询预订单信息
 *
 * @param {number}} tableId
 */
export function queryPreOrderStatus(tableId, needGoodsInfo = false) {
  return app.retailRequest({
    path: '/retail/h5/trade/preOrderStatus.json',
    data: {
      tableId,
      needGoodsInfo,
      payStateList: [0, 1],
    },
  });
}

/**
 * 结算页价格计算
 *
 * @param {*} params
 */
export const calculateOrderPrice = ({
  cartItemIds,
  cartId,
  couponId,
  calculateLunchBoxFee = false,
}) => {
  const params = {
    cart_item_ids: cartItemIds,
    calculate_lunch_box_fee: calculateLunchBoxFee,
  };

  if (couponId) params.coupon_id = couponId;
  if (cartId) params.cart_id = cartId;

  return retailCarmen({
    api: 'youzan.retail.trade.customer.cart/1.0.0/calculate',
    data: params,
  });
};
