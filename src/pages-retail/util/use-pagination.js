import { curry } from './base';

function pagination(api, params) {
  async function* paginationIterator() {
    const defaultParams = {
      pageSize: 20,
      pageNo: 1
    };
    const thePaginationData = [];
    const sentParams = { ...defaultParams, ...params };

    do {
      /* eslint-disable no-await-in-loop */
      try {
        const { total, data } = await api(sentParams);

        sentParams.pageNo += 1;
        sentParams.total = total;
        thePaginationData.push(...data);
        yield thePaginationData;
      } catch (err) {
        // 如果在 await 的过程中，出现了异常的话，必须 catch，否则迭代器会直接终止
        yield Error(err.msg);
      }
    } while ((sentParams.pageNo - 1) * sentParams.pageSize < sentParams.total);
  }

  const iterator = paginationIterator(params);

  const fetchPagination = async () => {
    const { done, value } = await iterator.next();
    if (done) return null;

    if (value instanceof Error) {
      throw value;
    }
    return value;
  };

  return fetchPagination;
}

export default curry(pagination);
