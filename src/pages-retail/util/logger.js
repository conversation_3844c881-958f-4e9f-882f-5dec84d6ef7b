/**
 * 事件埋点
 * @param et string 事件类型， 页面，点击，曝光，自定义，规则
 * @param ei string 事件标识
 * @param en string 事件名称
 * @param params object 参数
 */
const app = getApp();

export function loggerEvent(et = 'click', ei = '', en = '', params = {}) {
  try {
    app.logger &&
      app.logger.log({
        et,
        ei,
        en,
        params,
      });
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error(
      'logger 错误',
      `et = ${et}`,
      `ei = ${ei}`,
      `en = ${en}`,
      'params = ',
      params
    );
  }
}
