import promisify from '../../utils/promisify';

// for unit test
const _getStorage = promisify(wx.getStorage);
const _setStorage = promisify(wx.setStorage);

const cacheFetch = (
  api,
  key,
  getStorage = _getStorage,
  setStorage = _setStorage
) => {
  let invokedTimes = 0;
  return (options, ...args) => {
    let onCacheRead;
    let noCache = false;
    let justCacheOnce = false;

    invokedTimes += 1;

    if (typeof options === 'object' && options !== null) {
      ({ onCacheRead, noCache, justCacheOnce } = options);
      if (justCacheOnce && invokedTimes > 1) {
        onCacheRead = () => {};
        noCache = true;
      }
    } else if (typeof options === 'function') {
      onCacheRead = options;
    }
    let readCacheSuccess = false;
    let readCacheFail = false;
    let getResponseSuccess = false;
    let getResponseFailure = false;

    const readCache = getStorage({ key }).then(
      ({ data }) => {
        readCacheSuccess = true;
        return data;
      },
      err => {
        readCacheFail = true;
        // 啥都不管, 直接抛出, 这样就能判断是缓存报错了.
        throw err;
      }
    );

    const fetchRequest = api(...args).then(
      data => {
        getResponseSuccess = true;

        // 缓存写入也可能失败, 但是我们不关心
        try {
          noCache || setStorage({ key, data });
        } catch (e) {
          console.log(e);
        }
        return data;
      },
      err => {
        getResponseFailure = true;
        throw err;
      }
    );

    function handleData(data) {
      // 如果先完成请求，则处理请求返回的数据即可。
      if (getResponseSuccess) {
        return data;
      }

      // 如果先拿到缓存，则先处理缓存数据，并继续等待请求被 resolve
      if (readCacheSuccess) {
        onCacheRead(data);
        // 如果请求又失败了，则使用缓存的数据
        return getResponseFailure ? data : fetchRequest;
      }
    }

    return Promise.race([readCache, fetchRequest]).then(
      handleData,
      // 如果请求先被 reject，则直接使用缓存数据作为替代方案
      err => {
        if (readCacheFail) {
          if (getResponseFailure) {
            throw err;
          } else {
            return fetchRequest;
          }
        } else {
          return readCache.then(handleData);
        }
      }
    );
  };
};

export default cacheFetch;
