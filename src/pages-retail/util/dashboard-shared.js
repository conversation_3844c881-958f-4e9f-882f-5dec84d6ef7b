/* eslint-disable @youzan/koko/no-async-await */
/* eslint-disable @youzan/dmc/wx-check */
import { queryPreOrderStatus } from 'retail/util/api/order';
import args from '@youzan/weapp-utils/lib/args';
import { Hummer } from 'shared/utils/hummer';
import { navigateToNextPage } from '@youzan/tee-biz-prefetch';
import { getOrderPageConfigs, queryShopStatus } from 'retail/util/api';

const app = getApp();
const SCAN_CODE_GOODS_LIST_PATH = '/packages/retailb/goodslist/index';

export function markStart(type) {
  Hummer.mark.start(type);
}

export function markEnd(type) {
  Hummer.mark.end(type);
}

export function markHelp(options = {}, type = 'init') {
  if (typeof options === 'string') {
    options = {
      to: options,
    };
  }
  if (type === 'init') {
    app.globalData.retailMarkHelp = options;
  } else {
    Object.assign(app.globalData.retailMarkHelp, options);
  }
}

export async function getPreOrderInfo(tableId) {
  // 有桌号状态下，查询预订单
  const res = await queryPreOrderStatus(tableId, true);
  if (res && (res.orderDetailResponseList || []).length > 0) {
    // 获取预订单信息
    const preOrderList = res.orderDetailResponseList;
    // eslint-disable-next-line no-restricted-properties
    const preOrderItem = preOrderList.find((item) =>
      [0, 1].includes(item.state)
    );

    const { preOrderNo, orderNo, preOrderItemList = [] } = preOrderItem || {};

    const hasGoodsItem = preOrderItemList.length;

    if (orderNo) throw Error('已生成订单'); // 已生成订单，抛出异常，走 catch 逻辑
    if (!preOrderNo) throw Error('未找到预订单'); // 未找到预订单，抛出异常，走 catch 逻辑
    // 是否有商品,没有就是开桌场景
    if (!hasGoodsItem) throw Error('noGoodsItem'); // 预订单无商品，抛出异常，走 catch 逻辑

    const dbid = app.db.set({ preOrderItem });
    return {
      preOrderNo,
      dbid,
    };
  }
  throw new Error('orderDetailResponseList 为空');
}

// 跳转到点单页
export function gotoGoodsList(
  query = {},
  type = 'redirectTo',
  prefetchOpt = {}
) {
  const { orderPrefetchPromise } = prefetchOpt;
  // 扫码点单
  navigateToNextPage({
    navigatePath: 'free-go',
    navigateCb: (prefetchKey) => {
      setTimeout(() => {
        markStart('scan_order_start_show');
        const url = args.add(SCAN_CODE_GOODS_LIST_PATH, {
          ...query,
          prefetchKey,
        });
        wx[type]({
          url,
        });
      }, 150);
    },
    prefetchCb: () => {
      if (orderPrefetchPromise) return orderPrefetchPromise;
      return getOrderPageConfigs();
    },
  });
}

/**
 * 跳转预订单页面
 * @param preOrderNo
 * @param dbid
 */
export function gotoPreOrder(preOrderNo, dbid) {
  markHelp('order/pre-detail');
  wx.navigateTo({
    url: `/packages/retailb/order/pre-detail/index?no=${preOrderNo}&dbid=${dbid}`,
  });
}

/**
 * 零售首页
 * @param query
 */
export function goRetailHome(query, scene) {
  const url = args.add('/packages/retail/dashboard/index', query);
  // pages-retail onshow场景
  if (scene === 'pages-retail-show') {
    if (+query.s !== 2) {
      markHelp('retail/dashboard');
      wx.redirectTo({
        url,
      });
    }
    return +query.s !== 2;
  }
  markHelp('retail/dashboard');
  // 其他
  wx.redirectTo({
    url,
  });
  return true;
}

// 更据预订单返回信息，跳转到对应页面
export async function withPreOrderToDo(query, ext = {}) {
  const { toGoodsList, dinerNum, orderPrefetchPromise } = ext;

  function handleGoodsList(query, dinerNum) {
    // 选中就餐人数时做一个提示
    if (dinerNum) {
      wx.showToast({
        title: '本桌已选人数,正在加入点单',
        icon: 'none',
      });
    }
    setTimeout(
      () => {
        gotoGoodsList(query, 'redirectTo', { orderPrefetchPromise });
      },
      dinerNum ? 800 : 0
    );
  }


  const { isOpen } = await queryShopStatus();

  if (query.t) {
    return getPreOrderInfo(query.t)
      .then((res) => {
        // 营业中才需要跳转预结单页面
        if (isOpen && !toGoodsList) {
          // 跳转预订单信息页面
          gotoPreOrder(res.preOrderNo, res.dbid);
        } else {
          handleGoodsList(query);
        }
        return true;
      })
      .catch((err) => {
        if (err?.message === 'noGoodsItem') {
          // 已选就餐人数,但还未下单,其他场景不能做跳转
          handleGoodsList(query, dinerNum);
          return true;
        }
      });
  }
}

/** 从门店进入网店首页 */
export function goToOnlineHomePage() {
  const rootKdtId = app.getHQKdtId();
  const param = {
    shopAutoEnter: 1,
    kdt_id: rootKdtId,
  };
  // eslint-disable-next-line @youzan/dmc/wx-check
  wx.reLaunch({
    url: args.add('/pages/home/<USER>/index', param),
  });
}
