const app = getApp();

/**
 * 计算营销活动结果
 *
 * @param {*} promotions
 */
export function getPromotionResult(promotions) {
  return promotions
    .filter(
      (promotion) =>
        promotion.isCanUse &&
        ['107', '109', '115', '506'].includes(promotion.type)
    )
    .reduce((res, cur) => res + cur.decreaseAmount, 0);
}

/**
 * 获取储值专项优惠的优惠
 * @param promotions
 * @returns {*[]}
 */
export function getStoredDiscountPromotion(promotions) {
  return (
    promotions.find(
      (promotion) => promotion.isCanUse && promotion.type === '506'
    ) || {}
  );
}

/**
 * 储值专项折扣是否开启
 * @returns {*}
 * @param storedDiscountInfo
 */
export function getStoredDiscountIsOpen(storedDiscountInfo) {
  return !!(storedDiscountInfo && storedDiscountInfo.activityId);
}

/**
 * 计算专项折扣的值
 * @param storeInfo
 * @returns {string}
 */
export function getStoredDiscount(storeInfo) {
  return (storeInfo.discount / 10).toFixed(1);
}

/**
 * 获取储值专项优惠的优惠的钱
 * @param activities
 * @returns {*[]}
 */
export function getStoredDiscountValue(activities = []) {
  return (
    activities.find((activity) => activity.type === 'storedCustomerDiscount')
      ?.decrease || 0
  );
}

/**
 * TODO 待删除
 * 获取选中的优惠券
 *
 * @param {*} coupons
 */
export function getSelectedCoupon(coupons) {
  return (
    coupons.find((coupon) => !!coupon.isRecommend && !!coupon.isCanUse) ?? null
  );
}

/**
 * 获取选中的优惠券，新版支持多张优惠券
 *
 * @param {*} coupons
 */
export function getSelectedCoupons(coupons) {
  return coupons.filter((coupon) => !!coupon.isRecommend && !!coupon.isCanUse);
}

/**
 * 获取可选的优惠券
 *
 * @param {*} coupons
 */
export function getCanSelectedCoupons(coupons = []) {
  return coupons.filter((coupon) => !!coupon.isCanUse) || [];
}

/**
 * 优惠券选择
 *
 * @param {*} coupons
 */
export function onCouponChange(coupons, selected) {
  const dbid = app.db.set({
    type: 'coupon',
    selected,
    coupon_list: coupons,
  });

  // eslint-disable-next-line @youzan/dmc/wx-check
  wx.navigateTo({
    url: `/packages/retail/buy/coupon/index?dbid=${dbid}`,
  });
}
