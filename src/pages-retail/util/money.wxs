function adjustFixed(num, len) {
  const integer = Math.round(num * Math.pow(10, len)) / Math.pow(10, len);
  return integer.toFixed(len);
}

function toYuan(money) {
  return adjustFixed(parseFloat(money / 100) || 0, 2);
}
function toCent(money) {
  return parseInt(Math.round(money * 100), 10) || 0;
}
function toNumberYuan(money) {
  return +adjustFixed(parseFloat(money / 100) || 0, 2);
}

module.exports = { toYuan, toCent, toNumberYuan };
