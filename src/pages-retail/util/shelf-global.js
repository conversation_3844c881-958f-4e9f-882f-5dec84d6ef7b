import { defaultThemeColors } from 'themes/config';
import hexToRgba from '@youzan/utils/string/hexToRgba';
/**
 * 24小时货架中全局对象的存取方法
 */
const app = getApp();

export const detailDisplayTypeMap = {
  PAGE: 2,
  FULL_POP: 1,
  BOTTOM_POP: 0,
};

// 获取总部的全店风格，并存在全局对象上
export const initGlobalThemeType = async (that) => {
  try {
    const rootShopThemeData = await getShopThemeData(
      app.getHQKdtId() || app.getKdtId()
    );

    if (!app.globalData.hqShopInfo) {
      app.globalData.hqShopInfo = {
        themeType: rootShopThemeData.type,
        themeColors: rootShopThemeData.colors,
      };
    } else {
      app.globalData.hqShopInfo.themeType = rootShopThemeData.type;
      app.globalData.hqShopInfo.themeColors = rootShopThemeData.colors;
    }
    that &&
      that.setData({
        themeMainColor: getThemeMainColor(),
        themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
        themeMainRgbColor5: getThemeMainRgbColorWithAlpha(0.5),
      });
  } catch (error) {
    console.error(error);
  }
};

export const getShopThemeData = (kdtId) => {
  return app.request({
    method: 'GET',
    path: '/wscshop/shop/theme_and_colors.json',
    data: { kdt_id: kdtId, version: 1 }, // version: 1 全店风格收拢，为了区分新旧接口，增加version参数
  });
};

let req = null;
export const initGlobalThemeAfter = () => {
  if (app.globalData?.hqShopInfo?.themeType) {
    return Promise.resolve();
  }
  if (req) {
    return req;
  }
  req = getShopThemeData(app.getHQKdtId() || app.getKdtId()).then(
    (rootShopThemeData) => {
      if (!app.globalData.hqShopInfo) {
        app.globalData.hqShopInfo = {
          themeType: rootShopThemeData.type,
          themeColors: rootShopThemeData.colors,
        };
      } else {
        app.globalData.hqShopInfo.themeType = rootShopThemeData.type;
        app.globalData.hqShopInfo.themeColors = rootShopThemeData.colors;
      }

      req = null;

      return rootShopThemeData;
    }
  );

  return req;
};

// 店铺列表
export const getShelfShopList = () => {
  return app.globalData.shelfShopList || [];
};

export const setShelfShopList = (shelfShopList) => {
  app.globalData.shelfShopList = shelfShopList;
};

// 当前选择的店铺
export const getCurrentSelectShop = () => {
  return getShelfShopList().find((shop) => shop.userSelected);
};

// 外卖中的用户当前地址列表信息
export const getShelfAddress = () => {
  return app.globalData.shelfAddress;
};

export const setShelfAddress = (address) => {
  app.globalData.shelfAddress = address;
};

// 当前店铺是否打烊
export const isSuspendShop = () => {
  return !getCurrentSelectShop()?.isBusinessOpen;
};

/**
 * 获取总部的店铺信息
 */
export const getHqShopData = () => {
  const { hqShopInfo } = app.globalData;
  if (hqShopInfo) {
    return hqShopInfo;
  }
  const data = wx.getStorageSync('shop:hq:info');
  if (data) {
    app.globalData.hqShopInfo = data;
  }
  return app.globalData.hqShopInfo;
};

export const getThemeColors = () => {
  return getHqShopData()?.themeColors;
};
/**
 * 设置总部的店铺信息
 *
 * @param {Object} shopInfo
 */
export const setAndGetHqShopData = (shopInfo) => {
  const { chainStoreInfo, theme } = shopInfo;

  // 判断是否为总部
  if (chainStoreInfo.isRootShop) {
    const hqData = {
      kdtId: chainStoreInfo.rootKdtId,
      logo: chainStoreInfo.logo,
      name: chainStoreInfo.name,
      themeType: theme.type,
      theme,
    };
    app.globalData.hqShopInfo = hqData;
    wx.setStorage({
      key: 'shop:hq:info',
      data: hqData,
    });
  }
  return app.globalData.hqShopInfo;
};

// 获取主题色
export const getThemeMainColor = () => {
  return getThemeColors()?.general ?? defaultThemeColors.general;
};

// 获取主题色 rgba
export const getThemeMainRgbColorWithAlpha = (alpha = 1) => {
  return hexToRGB(getThemeMainColor(), alpha);
};

// 获取主题色
export const getThemeMainColorLinear = () => {
  const linearColor = getThemeColors() ?? defaultThemeColors;
  return `linear-gradient(to right, ${linearColor['start-bg']} 0%, ${linearColor['end-bg']} 100%)`;
};

export function hexToRGB(hex, alpha) {
  return hexToRgba(hex, alpha);
  // if (hex.length === 4) {
  //   hex = hex.replace(
  //     /^#([0-9a-fA-F]\1)([0-9a-fA-F]\2)([0-9a-fA-F]\3)$/g,
  //     '#$1$1$2$2$3$3'
  //   );
  // }
  // const tempHex = hex.replace('#', '0x');
  // const r = tempHex >> 16;
  // const g = (tempHex >> 8) & 0xff;
  // const b = tempHex & 0xff;
  // return `rgb(${r}, ${g}, ${b}, ${alpha})`;
}

export const filterByValidTime = (list) => {
  return list.filter(({ validStartTime, validEndTime }) => {
    const now = new Date();
    if (validStartTime && validStartTime > now) {
      return false;
    }
    if (validEndTime && validEndTime < now) {
      return false;
    }
    return true;
  });
};

export const setCurrentDetailGoods = (goods = {}) => {
  app.globalData.currentDetailGoods = { ...goods };
};

export const getCurrentDetailGoods = () => {
  return app.globalData.currentDetailGoods;
};

export const setDetailDisplayType = (type) => {
  app.globalData.detailDisplayType = type;
};

export const getDetailDisplayType = () => {
  return app.globalData.detailDisplayType;
};
