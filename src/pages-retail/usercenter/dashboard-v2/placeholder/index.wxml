<global-custom-loading wx:if="{{ globalCustomLoading }}" show="{{ true }}" />
<view wx:else class="uc-placeholder-warp">
  <view class="profile-photo-area" style="padding-top: {{ navigationBarHeight }}px">
    <view class="first-line">
      <view class="header-btn"></view>
      <view class="header-btn"></view>
    </view>
    <view class="photo-area">
      <view class="left-photo"></view>
      <view class="right-photo">
        <view class="name"></view>
        <view class="name-desc"></view>
      </view>
    </view>
    <view class="member-area">
      <view class="member-area-left">
        <view class="member-area-left-gray"></view>
      </view>
      <view class="member-area-right"></view>
    </view>
  </view>
  <view class="content">
    <view class="stats-area">
      <view class="stats-item" wx:for="{{ stats }}">
        <view class="stats-item-head"></view>
        <view class="stats-item-body"></view>
      </view>
    </view>
    <view class="order-area">
      <view class="order-head">
        <view class="order-head-left"></view>
        <view class="order-head-right"></view>
      </view>
      <view class="order-content">
        <view class="order-item" wx:for="{{ orders }}">
          <view class="order-item-item-head"></view>
          <view class="order-item-item-body"></view>
        </view>
      </view>
    </view>
    <view class="widgets-area" wx:for="{{ widgets }}">
      <view class="widget" wx:for="{{ item }}">
        <view class="widget-left">
          <view class="widget-left-white"></view>
        </view>
        <view class="widget-right"></view>
        <view class="border-line"></view>
      </view>
    </view>
  </view>
</view>
