.uc-placeholder-warp {
  background: #f7f8fa;
  min-height: 100vh;
}

.profile-photo-area {
  padding: 12px 12px 0 12px;
  background: #fff;
}

.profile-photo-area .first-line {
  height: 24px;
  display: flex;
  justify-content: flex-end;
}

.profile-photo-area .first-line .header-btn {
  margin-left: 8px;
  background: #f7f7f7;
  border-radius: 12px;
  width: 72px;
  height: 24px;
}

.photo-area {
  padding-top: 6px;
  display: flex;
}

.photo-area .left-photo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f7f7f7;
  margin-right: 10px;
}

.right-photo .name {
  width: 160px;
  height: 24px;
  margin-bottom: 10px;
  background: #f7f7f7;
}

.right-photo .name-desc {
  width: 68px;
  height: 24px;
  background: #f7f7f7;
  border-radius: 12px;
}

.member-area {
  margin-top: 27px;
  height: 44px;
  background: #f4f4f4;
  border-radius: 8px 8px 0px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.member-area-left {
  width: 145px;
  height: 16px;
  background: #fff;
}

.member-area-left-gray {
  background: #f4f4f4;
  margin-left: 16px;
  width: 8px;
  height: 16px;
}

.member-area-right {
  width: 65px;
  height: 16px;
  background: #fff;
}

.content {
  padding: 12px;
}

.stats-area {
  background: #ffffff;
  border-radius: 8px;
  padding: 14px 25px;
  display: flex;
  justify-content: space-around;
}

.stats-item-head {
  width: 32px;
  height: 20px;
  background: #f7f7f7;
  margin-bottom: 8px;
  margin-left: auto;
  margin-right: auto;
}

.stats-item-body {
  width: 56px;
  height: 16px;
  background: #f7f7f7;
}

.order-area {
  padding: 14px 16px 16px 16px;
  background: #ffffff;
  margin-top: 12px;
  border-radius: 12px;
}

.order-head {
  height: 24px;
  display: flex;
  justify-content: space-between;
}

.order-head-left {
  width: 60px;
  height: 24px;
  background: #f7f7f7;
}

.order-head-right {
  width: 68px;
  height: 20px;
  margin-top: 2px;
  background: #f7f7f7;
}

.order-content {
  padding: 16px 10px 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.order-item-item-head {
  width: 32px;
  height: 32px;
  background: #f7f7f7;
  margin-bottom: 8px;
  margin-left: auto;
  margin-right: auto;
}

.order-item-item-body {
  width: 40px;
  height: 16px;
  background: #f7f7f7;
}

.widgets-area {
  background: #fff;
  border-radius: 8px;
  margin-top: 12px;
}

.widget {
  padding: 13px 16px 13px 12px;
  display: flex;
  justify-content: space-between;
  position: relative;
}

.widget-left:nth-child(even) {
  width: 130px;
}
.widget-left:nth-child(odd) {
  width: 180px;
}

.widget-left {
  background: #f7f7f7;
  height: 24px;
}

.widget-right {
  width: 24px;
  height: 24px;
  background: #f7f7f7;
}

.widget-left-white {
  width: 8px;
  height: 24px;
  background: #fff;
  margin-left: 24px;
}

.border-line {
  position: absolute;
  height: 1px;
  background: #f7f7f7;
  bottom: 0;
  left: 46px;
  width: calc(100% - 46px);
}

.widget:last-child .border-line {
  display: none;
}
