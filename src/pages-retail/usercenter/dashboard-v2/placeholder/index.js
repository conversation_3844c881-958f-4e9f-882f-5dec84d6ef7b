console.log(`🔥 init: `, getApp().globalData.globalCustomLoading);
const array4 = [...Array(4).keys()];
Component({
  data: {
    stats: [...array4],
    orders: [...Array(5).keys()],
    widgets: [[...array4], [...array4]],
    navigationBarHeight: getApp().globalData.navigationBarHeight,
    globalCustomLoading: true,
  },
  attached() {
    this.setData({
      globalCustomLoading: getApp().globalData.globalCustomLoading,
    });
  },
});
