.auth-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, .7);
  z-index: 1000;


  .auth {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -311rpx;
    margin-top: -168rpx;
    width: 622rpx;
    height: 336rpx;
    background-color: #fff;
    border-radius: 32rpx;

    &__comp {
      display: flex;
      width: 100%;
      height: 100%;
      justify-content: center;
      align-items: center;
    }

    &__info {
      display: flex;
      padding: 0 48rpx;
      height: 240rpx;
      font-size: 28rpx;
      color: #646566;
      align-items: center;
    }
  
    &__btn {
      display: flex;
      height: 96rpx;
      font-size: 32rpx;
      color: #323233;
      border-top: 2rpx solid #ebedf0;
      box-sizing: border-box;
  
      &--btn {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        border-right: 2rpx solid #ebedf0;
      }
  
      &--btn:last-child {
        border-right: none;
      }
  
      &--success {
        display: flex;
        font-size: 32rpx;
        color: #ee0a24;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
