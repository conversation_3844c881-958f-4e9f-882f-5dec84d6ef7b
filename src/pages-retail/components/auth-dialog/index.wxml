<view class="auth-dialog" wx:if="{{show}}">
  <view class="auth">
    <view class="auth__info">为获得更好的会员体验，我们将会获取你的微信信息。</view>
    <view class="auth__btn">
      <view class="auth__btn--btn auth__btn--default" bindtap="onFail">不允许</view>
      <view class="auth__btn--btn auth__btn--success">
        <user-authorize
          customStyle="display: flex; width: 100%; height: 100%; justify-content: center; align-items: center;"
          class="auth__comp"
          authTypeList="{{ ['mobile', 'nicknameAndAvatar'] }}"
          bind:fail="onFail"
          bind:next="onSuccess">
          <view class="auth__btn--success">允许</view>
        </user-authorize>
      </view>
    </view>
  </view>
</view>