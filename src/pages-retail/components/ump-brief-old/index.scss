:host {
  --line-height: 48rpx;
  --title-height: 88rpx;
}

.ump-brief-old {
  display: flex;
  align-items: flex-end;
  font-size: 24rpx;
  background-color: #ffffff;
  margin-bottom: 6px;

  &-tags {
    flex: 1;
    height: 36rpx;
    overflow: hidden;
  }

  &-tag {
    font-size: 24rpx;
    display: inline-block;
    padding: 0 8rpx;
    margin-right: 8rpx;
    border-radius: 4rpx;
    color: var(--theme-main-color);
    background-color: var(--theme-main-rgb-color);
    line-height: 36rpx;
  }

  &-more {
    color: #a0a1a3;
    font-size: 10px;
  }
}

.ump-detail {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 744rpx;
  border-radius: 36rpx 36rpx 0 0;

  &-title {
    flex-shrink: 0;
    font-size: 32rpx;
    font-weight: 500;
    height: var(--title-height);
    line-height: var(--title-height);
    text-align: center;
    color: #323233;
  }

  &-close {
    font-size: 36rpx;
  }

  &-container {
    height: calc(100% - var(--title-height));
    padding: 40rpx 32rpx;
    box-sizing: border-box;
  }

  &-group {
    display: flex;
    align-items: flex-start;
    // padding: 0 16px 14rpx;
  }

  &-label {
    flex-basis: 150rpx;
    flex-shrink: 0;
    text-align: right;
    height: var(--line-height);
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  &-text {
    display: inline-block;
    vertical-align: middle;
    line-height: var(--line-height);
  }

  &-list {
    flex: 1;
    padding-top: 1rpx;
    font-size: 24rpx;
    color: #323233;
  }
}
