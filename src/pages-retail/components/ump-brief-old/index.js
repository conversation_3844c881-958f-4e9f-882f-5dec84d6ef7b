Component({
  properties: {
    discountTypeDesc: String,
    activities: Array,
    show: <PERSON>olean,
    umpData: {
      type: Object,
      observer(data) {
        const { meetReduceBriefList, baleList } = data;
        const briefList = this.computeBriefList(meetReduceBriefList, baleList);
        this.setData({
          meetReduceBriefList,
          baleList,
          briefList,
        });
        // if (briefList.length > 0) {
        this.triggerEvent(
          'umpShow',
          {},
          {
            bubbles: true,
            composed: true,
          }
        );
        // }
      },
    },
  },

  data: {
    expanded: false,
    meetReduceBriefList: [],
    baleList: [],
    briefList: [],
  },

  methods: {
    computeBriefList(meetReduceBriefList = [], baleList = []) {
      const ret = [];
      meetReduceBriefList
        .filter((_, i) => i < 6)
        .forEach((item) => {
          const { firstPreferentialDesc, id } = item;
          ret.push({
            id,
            text: firstPreferentialDesc,
          });
        });
      if (ret.length < 6) {
        baleList
          .filter((_, i) => i < 6 - ret.length)
          .forEach((item) => {
            const { id, balePrice, baleNum } = item;
            ret.push({
              id,
              text: `${balePrice / 100}元任选${baleNum}件`,
            });
          });
      }
      return ret;
    },

    closePopup() {
      this.setData({
        expanded: false,
      });
    },

    showPopup() {
      this.setData({
        expanded: true,
      });
    },
  },
});
