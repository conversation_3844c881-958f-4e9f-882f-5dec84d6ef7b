<popup
  position="bottom"
  show="{{show}}"
  bind:close="closePopup"
  bind:transitionEnd="clear"
  overlay
  custom-class="radius-popup"
  overlay="{{true}}"
  style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
>
  <view class="container">
    <header
      title="{{title}}"
      bind:closePopup="closePopup"
      bind:changeHeaderStatus="changeHeaderStatus"
      specifications="{{specifications}}"
      subtotal="{{subtotal * count}}"
      picture="{{picture}}"
      header-status="{{headerStatus}}"
      source-pictures="{{sourcePictures}}"
      style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
    />
    <scroll-view
      scroll-y="{{contentScrollY}}"
      scroll-into-view="{{toView}}"
      scroll-with-animation
      enhanced
      bounces
      class="detail-content detail-content_{{scene}}"
      bind:scroll="handleContentScroll"
    >
      <view class="body" id="body_{{scene}}">
        <!-- 基本信息 -->
        <goods-info goods="{{goodsInfo}}" wx:if="{{headerStatus !== 'thumbnail'}}" promotion-tags="{{promotionTags}}" />
        <view class="sku-count-row" wx:if="{{utils.showInputNumber(scene,comboType,sku)}}">
          <view>
            <text>购买数量</text>
          </view>
          <input-number
            min="{{1}}"
            max="{{maxStock}}"
            is-input
            value="{{count}}"
            disabled="{{inputNumberDisabled}}"
            bind:change="onNumberChange"
            bind:overlimit="cantAddMore"
            style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
          />
        </view>

        <!-- 普通商品 -->
        <block wx:if="{{scene === 'common'}}">
          <row
            wx:for="{{sku}}"
            wx:key="title"
            bind:select="onSelectSpec"
            nth="{{index}}"
            is-negative="{{isNegativeStock}}"
            title="{{item.title}}"
            models="{{item.rowModels}}"
            selected-spec="{{item.currentSelected}}"
          />
          <row
            wx:for="{{rowsOfProps}}"
            wx:key="title"
            bind:select="onSelectProps"
            nth="{{index}}"
            is-negative="{{isNegativeStock}}"
            title="{{item.title}}"
            models="{{item.rowModels}}"
            selected-props="{{item.currentSelected}}"
            multiple="{{item.multiple}}"
            necessary="{{item.necessary}}"
            is-props
          />
        </block>

        <!-- 必选品 -->
        <block wx:elif="{{scene === 'surcharge'}}">
          <view class="sku-row">
            <sur-row
              bind:select="onSelectSpec"
              models="{{sku}}"
              selected="{{surSelected}}"
              bind:selectSur="onSelectSur"
            />
          </view>
        </block>

        <!-- 套餐 -->
        <block wx:else>
          <!-- 固定套餐 -->
          <block wx:if="{{comboType===0}}">
            <row
              wx:for="{{sku}}"
              wx:key="title"
              bind:select="onSelectSpec"
              nth="{{index}}"
              is-negative="{{isNegativeStock}}"
              title="{{item.title}}"
              models="{{item.rowModels}}"
              selected-spec="{{item.currentSelected}}"
            />
            <specific-row-item group="{{selectedComboItems}}" theme-main-color="{{themeMainColor}}" />
          </block>

          <!-- 自选套餐 -->
          <view wx:else id="combo-wrap">
              <optional-sku-row
                wx:for="{{goodsInfo.combo_detail.combo_groups}}"
                wx:key="index"
                id="optional-combo-sku-{{item.goods_combo_group_id}}"
                list="{{item.combo_sub_items}}"
                name="{{item.name}}"
                rule="{{item.rule}}"
                bind:alterComboGoods="alterComboGoods"
                bind:openSku="openSku"
                group-id="{{item.goods_combo_group_id}}"
                selected-combo-items="{{selectedComboItems}}"
              />
          </view>
        </block>
      </view>
    </scroll-view>

    <block>
      <!-- 自选套餐 -->
      <footer
        wx:if="{{scene === 'combo' && comboType === 1}}"
        is-combo
        disabled="{{disabled}}"
        bind:addSku="addSku"
        btnText="{{disabled?'请选择套餐商品':submitBtnText}}"
        loading="{{loading}}"
        subtotal="{{originPrice}}"
        origin-price="{{subtotal}}"
        themeMainColor="{{themeMainColor}}"
        style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
      />

      <footer
        wx:else
        disabled="{{disabled}}"
        bind:addSku="addSku"
        btnText="{{submitBtnText}}"
        notSelectedProps="{{notSelectedProps}}"
        loading="{{loading}}"
        subtotal="{{subtotal}}"
        themeMainColor="{{themeMainColor}}"
        style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
      />
    </block>

  </view>
</popup>

<wxs module="utils">
  function showInputNumber(scene,comboType,sku) {
  if(scene === 'common' || scene === 'surcharge') {
  return true;
  }

  if(scene === 'combo' && comboType === 0 && sku.length > 0) {
  return true;
  }
  }
  module.exports = { showInputNumber:showInputNumber }
</wxs>


