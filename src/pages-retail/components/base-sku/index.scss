.radius-popup {
  border-radius: 20px 20px 0 0;
  overflow-y: visible !important;
  height: 80vh;
}

.container {
  width: 750rpx;
  height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.sku-count-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  margin: 0 32rpx;
  height: 108rpx;
  // border-bottom: 1rpx solid #ebedf0;
  box-sizing: border-box;
  flex-shrink: 0;
}

.sku-count-row text {
  font-size: 14px;
  color: #323233;
  font-weight: 700;
}

.body {
  background: white;
  flex: 1;
}


.detail-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}