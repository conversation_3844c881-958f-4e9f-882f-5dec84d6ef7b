:host {
  --buttonRadius: 22px;
  --tapRadius: 34px;
  --offset: 5.5px;
  --theme-main-color: red;
  --input-min-width: 20px;
  --input-max-width: 30px;
}

.input-number {
  display: flex;
  align-items: center;
  color: #666666;
  .extend-click-area {
    width: var(--tapRadius);
    height: var(--tapRadius);
    position: absolute;
    z-index: 5;
  }
}

.input-number__icon-minus,
.input-number__icon-add {
  position: relative;
  width: var(--buttonRadius);
  height: var(--buttonRadius);
  border-radius: 50%;
  background-color: #38f;
  color: #fff;
  text-align: center;
}

.input-number__icon-minus:active,
.input-number__icon-add:active {
  opacity: 0.6;
}

.input-number__icon-minus {
  background-color: #fff;
  color: #bbb;
  background-size: var(--buttonRadius);
  background-position: center;
}

.disable-btn,
.disable-minus-btn {
  opacity: 0.3;
}

.stepper-minus-btn,
.stepper-add-btn {
  width: var(--buttonRadius);
  height: var(--buttonRadius);
  border-radius: var(--buttonRadius);
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--theme-main-color);
  position: relative;
  background: #fff;
}

.stepper-minus-btn::after,
.stepper-add-btn::after {
  content: '';
  height: 1px;
  width: 60%;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background: var(--theme-main-color);
}

.stepper-add-btn {
  background: var(--theme-main-color);
}

.stepper-add-btn::after {
  background: #fff;
}

.stepper-add-btn::before {
  content: '';
  height: 60%;
  width: 1px;
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  background: #fff;
}

.input-number__icon-add {
  background-size: var(--buttonRadius);
  background-position: center;
  background-color: #fff;
}

.input-number__value {
  min-width: var(--input-min-width);
  max-width: var(--input-max-width);
  font-size: 16px;
  color: #333;
  text-align: center;
}

.input-number-padding {
  width: var(--tapRadius);
  // 避免在某些 pop 弹起时，点击会被穿透的情况，目前暂时没有定位到原因，这样处理可以解决
  transform: translateX(0);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all cubic-bezier(0, 0, 0.1, 0.1) 0.2s;
  position: relative;
}

.input-number-padding.active {
  transform: translateX(0) rotate(180deg);
}

.input-number-padding.hide {
  transform: translateX(var(--tapRadius)) rotate(180deg);
}

.input-number__badge {
  background: #fff;
  color: var(--theme-main-color);
  border-color: var(--theme-main-color);
  z-index: 1;

  &--hide {
    // 商详页 goodsSku 的计步器样式被 van-badge 覆盖，原因未知
    display: none !important;
  }
}
