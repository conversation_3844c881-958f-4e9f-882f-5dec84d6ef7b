<view class="input-number">
  <view
    class="input-number-padding {{currentNum > 0 ? 'active' : 'hide'}}"
    wx:if="{{!onlyAddMode}}"
  >
    <!-- 扩展点击区域，将点击的热区扩大，采用绝对定位，不增加按钮的高度 -->
    <view class="extend-click-area" catch:tap="onClickMinus" />
    <view class="input-number__icon-minus">
      <view class="stepper-minus-btn {{disableMinus?'disable-minus-btn':''}}" />
    </view>
  </view>

  <view wx:if="{{currentNum > 0 && !onlyAddMode}}" class="input-number__value input-value-class">
    {{ currentNum }}
  </view>

  <view class="input-number-padding" wx:if="{{!onlyMinusMode}}">
    <view class="extend-click-area" catch:tap="onClickPlus" />
    <view class="input-number__icon-add">
        <view class="stepper-add-btn {{disablePlus?'disable-btn':''}}"/>
    </view>
  </view>
</view>


