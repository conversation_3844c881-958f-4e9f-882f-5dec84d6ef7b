/**
 * 防抖处理, 首次点击立即触发，随后触发会在最后一次触发后执行
 */
const debounceClick = (fn, wait = 250) => {
  let timeout;
  let firstTimeout;
  let isFirst = true;
  let clickNum = 1;
  return function () {
    if (isFirst) {
      fn(1); // 触发一次
      isFirst = false;
      // wait 时间后置 true，避免只点击一次后，超过 wait 时间的点击无法触发 first
      firstTimeout = setTimeout(() => {
        isFirst = true;
      }, wait);
    } else {
      clearTimeout(firstTimeout); // 如果进入 debouce 就取消 firstTimeout 的计时器，在执行完成后再设置 first
      if (timeout) {
        clearTimeout(timeout);
        clickNum += 1;
      }
      timeout = setTimeout(() => {
        fn(clickNum);
        timeout = null;
        isFirst = true;
        clickNum = 1;
      }, wait);
    }
  };
};

Component({
  externalClasses: ['input-value-class'],
  properties: {
    currentNum: {
      type: Number,
      value: 0,
    },
    minNum: {
      type: Number,
      value: 0,
    },
    disableMinus: {
      type: Boolean,
      value: false,
    },
    disablePlus: {
      type: Boolean,
      value: false,
    },
    // 加号模式：仅显式一个加号，数量作为角标在加号上放展示
    onlyAddMode: {
      type: Boolean,
      value: false,
    },
    // 是否开启防抖，如果开启的话会延迟触发，速度稍慢
    useDebounce: {
      type: Boolean,
      value: true,
    },
    themeColor: String,
  },
  lifetimes: {
    attached() {
      this.initDebounce();
    },
  },

  methods: {
    initDebounce() {
      this.debouncePlus = debounceClick((clickNum) => {
        this.emitPlus(clickNum);
      });
      this.debounceMinus = debounceClick((clickNum) => {
        this.emitMinus(clickNum);
      });
    },
    onClickPlus() {
      const { disablePlus, useDebounce } = this.data;
      if (disablePlus) return;
      useDebounce ? this.debouncePlus() : this.emitPlus(1);
    },
    onClickMinus() {
      const { currentNum, minNum, disableMinus, useDebounce } = this.data;
      if (currentNum <= minNum && currentNum > 0) {
        wx.showToast({ title: `最少购买${this.minNum}件`, icon: 'none' });
      }
      if (disableMinus) return;
      useDebounce ? this.debounceMinus() : this.emitMinus(1);
    },
    emitPlus(clickNum) {
      const { currentNum } = this.data;

      this.triggerEvent('clickPlus', {
        prevNum: currentNum,
        nextNum: currentNum + clickNum,
        type: 'ADD',
      });
    },
    emitMinus(clickNum) {
      const { currentNum, minNum } = this.data;
      const nextNum = currentNum - clickNum;
      this.triggerEvent('clickMinus', {
        prevNum: currentNum,
        nextNum: nextNum < minNum ? minNum : nextNum, // debounce 时可能出现减少到比最小值小的情况
        type: 'MINUS',
      });
    },
  },
});
