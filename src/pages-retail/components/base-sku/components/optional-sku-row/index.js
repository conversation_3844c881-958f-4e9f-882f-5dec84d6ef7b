import { DefaultShowItemNumber } from 'retail/components/base-sku/helper';

Component({
  data: {
    isUnfold: false,
  },
  properties: {
    list: {
      type: Array,
      value: [],
    },
    name: String,
    rule: {
      type: String,
      value: '',
    },
    groupId: Number,
    selectedComboItems: {
      type: Array,
      value: [],
    },
  },
  observers: {
    list() {
      this.setShowList();
    },
    rule() {
      this.setRuleObj();
    },
    selectedComboItems() {
      this.setSubComboList();
    },
  },
  methods: {
    toggleCollapse() {
      this.setData(
        {
          isUnfold: !this.data.isUnfold,
        },
        () => {
          this.setShowList();
        }
      );
    },
    setShowList() {
      const { list, isUnfold } = this.data;
      const showList = list.slice(
        0,
        isUnfold ? list.length : DefaultShowItemNumber
      );
      this.setData({
        showList,
      });
    },
    setRuleObj() {
      const { rule } = this.data;
      this.setData({
        ruleObj: rule ? JSON.parse(rule) : {},
      });
    },
    // 用于计算选中数量
    setSubComboList() {
      const { selectedComboItems, groupId } = this.data;
      const subComboList =
        selectedComboItems.find(({ id }) => groupId === id)?.subComboList || [];
      this.setData({
        subComboList,
      });
    },
  },
});
