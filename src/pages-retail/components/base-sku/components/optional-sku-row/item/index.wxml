  <view
    class="optional-combo-item__content {{ item.isSoldOut ? 'optional-combo-item__content--sold-out' : '' }}"
    style="{{util.activeStyle(currentNum,themeMainColor)}}"
  >
    <image
      class="item-image"
      inner-class="item-image"
      src="{{picture}}"
      mode="aspectFill"
    />
    <view class="item-title">{{ item.title }}</view>
    <view
      class="item-stepper {{ currentNum > 0 ? 'item-stepper--center' : '' }}"
    >
      <goods-stepper
        wx:if="{{!item.isSoldOut}}"
        input-value-class="input-value-class"
        current-num="{{currentNum}}"
        disable-minus="{{disableMinus}}"
        style="{{util.themeColorStyle(themeMainColor)}}"
        bind:clickPlus="clickPlus"
        bind:clickMinus="clickMinus"
      />
      <view wx:else class="item-sold-out">已售罄</view>
    </view>
  </view>

  <wxs module="util">
  function activeStyle(currentNum,themeMainColor) {
    return currentNum > 0 ? 'border-color:' +themeMainColor  : '';
  }

  function themeColorStyle(themeMainColor) {
    return '--theme-main-color:'+ themeMainColor;
  }


  module.exports = { activeStyle:activeStyle,themeColorStyle:themeColorStyle }
</wxs>