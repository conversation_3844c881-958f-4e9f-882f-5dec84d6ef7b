@mixin text-ellipsis($lines: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $lines;
}

.item-image {
  display: block;
  width: 100%;
  height: 109px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.item-title {
  @include text-ellipsis;
  margin: 8px;
  font-size: 14px;
  font-weight: bold;
  color: #323233;
  height: 40px;
}

.item-stepper {
  display: flex;
  justify-content: flex-end;
  padding: 0 2px;

  &--center {
    justify-content: center;
  }
}

.optional-combo-item {
  &__content {
    border: 1px solid #ebedf0;
    border-radius: 8px;
    padding-bottom: 8px;

    &--sold-out {
      .item-image {
        -webkit-mask: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3));
        mask: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3));
      }

      .item-title {
        color: #c8c9cc;
      }

      .item-stepper {
        .item-sold-out {
          font-size: 12px;
          color: #c8c9cc;
          padding-top: 5px;
          padding-right: 6px;
        }
      }
    }
  }
}

.input-value-class {
  max-width: initial !important;
  flex: 1;
}