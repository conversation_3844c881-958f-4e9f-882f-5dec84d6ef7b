import { getPicture } from 'retail/components/base-sku/helper';
import { ThemeColorWithComponent } from 'retail/util/behaviors/themeBehavior';

Component({
  behaviors: [ThemeColorWithComponent],
  data: {
    currentNum: 0,
  },
  properties: {
    item: Object,
    groupId: Number,
    maxPlusNum: Number,
    supportRepeat: <PERSON>olean,
    subComboList: Array,
  },
  observers: {
    item(val) {
      this.setData({
        picture: getPicture(val.picture),
      });
    },
    subComboList(val) {
      const { item } = this.data;
      const currentNum =
        val.reduce((pre, i) => {
          if (i.goodsId === item.item_id) {
            return pre + i.num;
          }
          return pre;
        }, 0) || 0;
      this.setData({
        currentNum,
        disableMinus:
          (item.sku_relateds?.length > 1 || item.props) && currentNum > 1,
      });
    },
  },
  methods: {
    clickPlus({ detail }) {
      const { maxPlusNum, supportRepeat, currentNum, item, groupId } =
        this.data;
      const { prevNum, nextNum } = detail;
      // {prevNum: 0, nextNum: 1, type: "ADD"}

      if (maxPlusNum === 0) {
        wx.showToast({ title: '已达到最大选购数量', icon: 'none' });
        return;
      }

      if (!supportRepeat && currentNum > 0) {
        wx.showToast({ title: '不能加购相同商品', icon: 'none' });
        return;
      }

      const {
        item_id,
        sku_relateds = [],
        props = [],
        item_properties = [],
      } = item;

      let isMultiProps = false; // 是否多属性
      const propertyIds = [];
      for (const item of props) {
        if (item.text_models.length > 1) {
          isMultiProps = true;
          break;
        }
        propertyIds.push(item.text_models[0].id);
      }

      // 多规格||多属性弹起sku模态框
      if (sku_relateds.length > 1 || isMultiProps) {
        const relatedSku = sku_relateds.map(({ sku_id }) => sku_id);

        this.triggerEvent(
          'openSku',
          {
            goods: {
              goodsId: item_id,
              relatedSku,
              itemProperties: item_properties, // 用于计算价格
              skuProps: props,
            },
            comboGroupId: groupId,
            maxPlusNum: supportRepeat ? maxPlusNum : 1,
          },
          { bubbles: true, composed: true }
        );
        return;
      }

      this.triggerEvent(
        'alterComboGoods',
        {
          goods: {
            goodsId: item_id,
            skuId: sku_relateds[0]?.sku_id,
            propertyIds,
          },
          comboGroupId: groupId,
          prevNum,
          nextNum,
        },
        { bubbles: true, composed: true }
      );
    },
    clickMinus({ detail }) {
      const { prevNum, nextNum } = detail;
      const {
        item: { item_id },
        subComboList,
        groupId,
      } = this.data;
      const subCombo = subComboList.find(({ goodsId }) => goodsId === item_id);

      this.triggerEvent(
        'alterComboGoods',
        {
          goods: {
            goodsId: item_id,
            skuId: subCombo?.skuId,
            propertyIds: subCombo?.propertyIds,
          },
          comboGroupId: groupId,
          prevNum,
          nextNum,
        },
        { bubbles: true, composed: true }
      );
    },
  },
});
