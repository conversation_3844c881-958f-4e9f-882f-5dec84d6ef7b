<collapse
  is-show-collapse="{{util.isShowCollapse(list)}}"
  is-unfold="{{isUnfold}}"
  bind:toggleCollapse="toggleCollapse"
>
  <view class="optional-combo__label">
    <group-label name="{{name}}" rule="{{rule}}" />
  </view>
  <view class="optional-combo__list">
    <view
      class="optional-combo__item"
      wx:for="{{showList}}"
      wx:key="index"
    >
      <optional-combo-item
        item="{{item}}"
        group-id="{{groupId}}"
        max-plus-num="{{util.maxPlusNum(ruleObj,subComboList)}}"
        support-repeat="{{ruleObj.supportRepeatSelect}}"
        sub-combo-list="{{subComboList}}"
      />
    </view>
  </view>
</collapse>

<wxs module="util">
  function maxPlusNum(ruleObj,subComboList) {
  var a= subComboList.reduce(function (pre, item) {
  return pre + item.num;
  }, 0)
  return ruleObj.selectNum - a
  }

  function isShowCollapse(list) {
  return list.length > 6;
  }


  module.exports = { maxPlusNum:maxPlusNum,isShowCollapse:isShowCollapse }
</wxs>


