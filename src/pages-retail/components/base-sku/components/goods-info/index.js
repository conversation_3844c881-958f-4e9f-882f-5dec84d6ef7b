import { ThemeColorWithComponent } from 'retail/util/behaviors/themeBehavior';

Component({
  behaviors: [ThemeColorWithComponent],
  data: {
    showSpread: false,
  },
  properties: {
    goods: {
      type: Object,
      value: {},
    },
    /** 营销标 */
    promotionTags: {
      type: Array,
      value: [],
    },
  },
  lifetimes: {
    ready() {
      this.createSelectorQuery()
        .select(`#sell-point`)
        .boundingClientRect()
        .exec((res) => {
          this.setData({
            showSpread: (res[0]?.height || 0) > 36,
          });
        });
    },
  },
  methods: {
    switchSpread() {
      this.setData({
        showSpread: false,
      });
    },
  },
});
