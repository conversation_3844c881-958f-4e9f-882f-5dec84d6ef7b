<view class="goods-base-info">
  <view class="goods-info">
    <view class="goods-title">{{ goods.title }}</view>
    <!-- 普通标签 -->
    <!-- <view wx:if="labels.length > 0" class="goods-labels">
      <view class="goods-label" v-for="item in labels" :key="item">
        {{ item }}
      </view>
    </view> -->
    <!-- 卖点 -->
    <view id="sell-point" wx:if="{{goods.sell_point}}" class="goods-sell-point {{showSpread?'hidden-sell-point':''}}">
      {{ goods.sell_point }}
      <view class="spread" wx:if="{{showSpread}}" bind:tap="switchSpread">
        <!-- 点击展示全部卖点 -->
        <van-icon custom-class="spread-icon" name="arrow-down" />
      </view>
    </view>
    <view class="goods-tags-sales">
      <ump-brief
        wx:if="{{promotionTags}}"
        tagList="{{promotionTags}}"
        placed-type="{{2}}"
        style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}};width: 100%"
        sales="{{goods.soldNum}}"
      />
    </view>
      
  </view>
</view>


