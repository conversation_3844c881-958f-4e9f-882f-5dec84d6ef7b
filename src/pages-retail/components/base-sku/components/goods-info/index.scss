.goods-base-info {
  background-color: #fff;
  @mixin text-ellipsis($lines: 1) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lines;
  }
  .goods-info {
    padding: 20px 0 24px 0;
    margin: 0 16px;
    border-bottom: 0.5px solid #ebedf0;
    box-sizing: border-box;
    .goods-title {
      color: #323233;
      font-weight: bold;
      line-height: 24px;
      letter-spacing: 0.2;
      font-size: 18px;
      // 超出2行省略
      @include text-ellipsis(2);
    }

    .goods-labels {
      display: flex;
      margin-top: 4px;
      font-size: 12px;
      color: #323233;
      flex-wrap: wrap;

      .goods-label {
        display: flex;
        margin: 4px 4px 0 0;
        padding: 0 4px;
        height: 16px;
        line-height: 17px;
        justify-content: center;
        border-radius: 2px;
        background-color: #f2f3f5;
      }
    }

    .goods-sell-point {
      position: relative;
      margin-top: 8px;
      word-break: break-all;
      color: #333;
      font-size: 12px;
      line-height: 18px;
      letter-spacing: 0.2;

      .spread {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 32px;
        line-height: 18px;
        text-align: right;
        background: linear-gradient(
          to right,
          rgba(255, 255, 255, 0.1) 10%,
          rgba(255, 255, 255, 0.5) 35%,
          rgba(255, 255, 255, 1) 35%
        );
        .spread-icon {
          font-size: 14px;
          height: 18px;
          line-height: 18px;
          vertical-align: middle;
        }
      }
    }

    .hidden-sell-point {
      max-height: 36px;
      overflow-y: hidden;
    }
    // .goods-ump-container {
    //   display: flex;
    //   justify-content: space-between;
    //   .goods-ump-tags {
    //     display: flex;
    //     flex-wrap: wrap;
    //     margin-top: 8px;

    //     .tag {
    //       display: flex;
    //       align-items: center;
    //       font-size: 12px;
    //       height: 16px;
    //       padding: 0 4px;
    //       border-radius: 2px;
    //       box-sizing: border-box;

    //       &.strong,
    //       &.primary {
    //         color: white;
    //         background-color: var(--theme-color, #ee0a24);
    //       }

    //       &.weak {
    //         color: var(--theme-color, #ee0a24);
    //         border: 1px solid var(--theme-rgb-color, #ee0a2499);

    //       }

    //       &:not(:last-child) {
    //         margin-right: 4px;
    //       }
    //     }
    //   }
      
    // }

    .goods-tags-sales {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 24rpx;
    }
    
  }
}