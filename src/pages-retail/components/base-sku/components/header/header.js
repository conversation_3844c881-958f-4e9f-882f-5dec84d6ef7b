import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import {
  HeaderStatus,
  getImageHeight,
  DefaultImageHeight,
} from 'retail/components/base-sku/helper';

const statusClassMap = {
  [HeaderStatus.Init]: 'goods-transition-init',
  [HeaderStatus.Thumbnail]: 'goods-transition-small',
  [HeaderStatus.LargeImage]: 'goods-transition-large',
};

Component({
  data: {
    singleImageMode: true,
    imageHeight: DefaultImageHeight,
    swiperIndex: 0,
    statusClass: '',
    imgUrl: '',
  },
  properties: {
    picture: String,
    title: String,
    specifications: String,
    subtotal: {
      type: Number,
      value: 0,
    },
    sourcePictures: {
      type: Array,
      value: [],
      observer(val) {
        this.initPicture(val);
      },
    },
    headerStatus: {
      type: String,
      value: '',
      observer(val) {
        this.setData({
          statusClass: statusClassMap[val],
        });
      },
    },
  },
  methods: {
    closePopup() {
      this.triggerEvent('closePopup');
    },
    initPicture(val) {
      if (val && val.length) {
        const { height = DefaultImageHeight, url } = val[0];
        this.setData({
          imageHeight: getImageHeight(height) || DefaultImageHeight,
          imgUrl: cdnImage(url, '!730x0.jpg'),
          singleImageMode: val.length === 1,
          pictures: val,
        });
      } else {
        this.setData({
          imgUrl: this.data.picture,
          singleImageMode: true,
        });
      }
    },
    handleHeaderTouchStart(e) {
      // 处理点击事件
      if (this.data.headerStatus === HeaderStatus.Thumbnail) {
        this.triggerEvent('changeHeaderStatus', HeaderStatus.LargeImage);
        return;
      }
      const { pageY } = e.touches[0];
      this._startPosition = pageY;
    },
    handleHeaderTouchMove(e) {
      const { pageY } = e.touches[0];
      if (
        this.data.headerStatus !== HeaderStatus.Thumbnail &&
        this._startPosition - pageY >= 20
      ) {
        this.triggerEvent('changeHeaderStatus', HeaderStatus.Thumbnail);
      }
    },
    handleSwiperChange({ detail }) {
      this.setData({
        imgUrl: cdnImage(this.data.pictures[detail].url, '!730x0.jpg'),
      });
    },
  },
});
