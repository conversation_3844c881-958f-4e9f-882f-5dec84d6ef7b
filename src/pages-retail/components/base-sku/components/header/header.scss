$goods-left-offset: 16px;
$top-radius: 20px 20px 0 0;

.header {
  display: flex;
  flex-direction: column;

  @mixin button {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 14px;
    border: none;
    z-index: 10;

    &::before {
      opacity: 0;
    }
  }

  .close-button {
    @include button;
    right: 16px;
    background: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/goods-sku-close.svg')
      no-repeat center;
  }

  .close-icon {
    position: absolute;
    top: 16px;
    border: none;
    z-index: 10;
    right: 20px;
  }

  .small-content {
    flex-direction: row;
    position: relative;
    display: none;

    &.small-content--thumbnail {
      display: flex;
    }
    
    &.small-content--single-image {
      display: flex;
    }

    @mixin text-ellipsis($lines: 1) {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: $lines;
    }

    .goods-image {
      position: relative;
      flex-shrink: 0;
    }

    .goods-info {
      margin: 24px 36px 8px 16px;
      .goods-title {
        color: #323233;
        font-weight: bold;
        line-height: 24px;
        letter-spacing: 0.2;
        font-size: 18px;
        @include text-ellipsis;
      }

      .specifications {
        margin-top: 8px;
        color: #646566;
        font-size: 14px;
        line-height: 18px;
      }
    }
  }

  .border-line{
    margin: 0 16px;
    border-bottom: 0.5px solid #ebedf0;
  }

  .goods-swiper {
    border-radius: $top-radius;
    overflow: hidden;
  }

  .goods-transition-init {
    width: 100vw;
    border-radius: 20px 20px 0 0;
    height: var(--goods-image-height, 375px);
  }
  .goods-transition-small {
    width: 120px;
    height: 120px;
    top: -24px;
    animation: 0.33s linear image-transition-small;
    left: $goods-left-offset;
    margin-right: $goods-left-offset;
    border-radius: 8px;
    border: 0.5px solid #e0e0e080;
    &.disabledTransition {
      animation: none;
    }
  }
  .goods-transition-large {
    width: 100vw;
    border-radius: $top-radius;
    height: var(--goods-image-height, 375px);
    animation: 0.33s linear image-transition-large;
  }
  @keyframes image-transition-small {
    0% {
      width: 100vw;
      height: var(--goods-image-height, 375px);
      top: 0;
      left: 0;
      margin-right: 0;
    }
    100% {
      width: 120px;
      height: 120px;
      top: -24px;
      left: $goods-left-offset;
      margin-right: $goods-left-offset;
      border-radius: 8px;
    }
  }

  @keyframes image-transition-large {
    from {
      width: 120px;
      height: 120px;
    }
    to {
      width: 100vw;
      height: var(--goods-image-height, 375px);
    }
  }
}