<view class="header" style="--goods-image-height:{{ imageHeight}}px">
  <van-button
    wx:if="{{headerStatus !== 'thumbnail'}}"
    custom-class="close-button"
    plain
    round
    bind:click="closePopup"
  />
  <van-icon
    wx:else
    class="close-icon"
    color="#333"
    size="18"
    name="cross"
    bind:click="closePopup"
  />
  <view class="small-content {{headerStatus === 'thumbnail'?'small-content--thumbnail'  : '' }} {{singleImageMode? 'small-content--single-image' : ''}}">
    <image
      src="{{imgUrl}}"
      mode="aspectFill"
      catch:touchmove="handleHeaderTouchMove"
      catch:touchstart="handleHeaderTouchStart"
      class="goods-image {{statusClass}}"
    />
    <view wx:if="{{headerStatus === 'thumbnail'}}" class="goods-info">
      <view class="goods-title">{{ title }}</view>
      <text class="specifications" wx:if="{{specifications}}">
          {{specifications}}
        </text>
    </view>
  </view>
  <view class="border-line" wx:if="{{headerStatus === 'thumbnail'}}"></view>
    
   <!-- 多张图片采用轮播图 -->
    <goods-image
      class="goods-swiper"
      show="{{headerStatus !== 'thumbnail' && !singleImageMode}}"
      goods-images="{{pictures || []}}"
      image-height="{{imageHeight}}"
      show-right-indicators
      show-indicators="{{false}}"
      catch:touchmove="handleHeaderTouchMove"
      catch:touchstart="handleHeaderTouchStart"
      bind:swiperChange="handleSwiperChange"
    />
</view>
