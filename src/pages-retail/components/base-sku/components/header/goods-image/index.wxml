<view class="goods-image" wx:if="{{renderImg.length > 0 && show}}" style="--goods-image-height: {{imageHeight}}px;">
  <swiper
    class="swiper-container"
    ref="goodsImage"
    interval="3000"
    autoplay
    current="{{currentSwp}}"
    circular
    indicator-dots="{{helper.isShowDots(goodsImages, showIndicators)}}"
    bindchange="onSwpChange"
  >
    <swiper-item wx:for="{{renderImg}}" wx:key="index" item-id="{{index}}">
      <image class="goods-image-swipe__image" mode="aspectFit" src="{{item}}" />
    </swiper-item>
  </swiper>
  <view wx:if="{{helper.isShowCusDots(goodsImages, showRightIndicators)}}" class="goods-image__indicator">
    {{ helper.indicatorText(goodsImages, rightIndicatorIndex) }}
  </view>
</view>

<wxs module="helper" src="./index.wxs" />


