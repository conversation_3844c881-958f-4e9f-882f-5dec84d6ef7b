function isShowDots(goodsImages, showIndicators) {
  return goodsImages.length > 1 && showIndicators;
}

function isShowCusDots(goodsImages, showRightIndicators) {
  return (goodsImages || []).length > 1 && showRightIndicators;
}

function indicatorText(goodsImages, rightIndicatorIndex) {
  goodsImages = goodsImages || [];
  return rightIndicatorIndex + ' / ' + goodsImages.length;
}

module.exports = {
  isShowDots,
  isShowCusDots,
  indicatorText,
};
