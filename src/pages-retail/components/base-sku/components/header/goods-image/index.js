import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { DefaultImageHeight } from 'retail/components/base-sku/helper';

Component({
  data: {
    rightIndicatorIndex: 1,
    currentSwp: 0,
    renderImg: [],
  },
  properties: {
    goodsImages: {
      type: Array,
      value: () => [],
    },
    showIndicators: Boolean,
    showRightIndicators: Boolean,
    imageHeight: {
      type: Number,
      value: DefaultImageHeight,
    },
    show: {
      type: Boolean,
      value: true,
    },
  },
  observers: {
    'goodsImages,show': function (val1 = [], val2) {
      const cdnImages =
        val1?.map((item) => {
          return cdnImage(item.url, '!730x0.jpg');
        }) ?? [];
      const { currentSwp } = this.data;
      // 隐藏时直接切成一张图，就不会再触发 change 事件了
      const renderImg = val2
        ? cdnImages
        : cdnImages.slice(currentSwp, currentSwp + 1);
      this.setData({
        renderImg,
      });
    },
  },
  methods: {
    onSwpChange({ detail }) {
      const { current } = detail;
      this.setData({
        rightIndicatorIndex: current + 1,
        currentSwp: current,
      });
      this.triggerEvent('swiperChange', current);
    },
  },
});
