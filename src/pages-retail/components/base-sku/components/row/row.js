import {
  getThemeMainColor,
  getThemeMainRgbColorWithAlpha,
} from 'retail/util/shelf-global';

Component({
  data: {
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    themeMainColor: getThemeMainColor(),
    themeMainColor15: getThemeMainRgbColorWithAlpha(0.15),
    borderColor: getThemeMainRgbColorWithAlpha(0.5),
  },
  properties: {
    models: Array,
    selectedSpec: Number,
    selectedProps: Array,
    title: String,
    nth: Number,
    isProps: Boolean,
    isNegative: Boolean,
    multiple: Boolean,
    necessary: Boolean,
  },
  lifetimes: {
    attached() {
      this.setData({
        themeMainColor: getThemeMainColor(),
        themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
        themeMainColor15: getThemeMainRgbColorWithAlpha(0.15),
        borderColor: getThemeMainRgbColorWithAlpha(0.5),
      });
    },
  },
  methods: {
    onClick({ currentTarget }) {
      const {
        dataset: { index, disabled },
      } = currentTarget;
      const { nth, multiple, necessary } = this.data;

      if (disabled) return;

      this.triggerEvent('select', { row: nth, index, multiple, necessary });
    },
  },
});
