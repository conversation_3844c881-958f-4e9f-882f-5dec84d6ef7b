<view class="sku-tree-item" style="--theme-main-color:{{themeMainColor}};--theme-main-rgb-color:{{themeMainColor}};--theme-main-rgb15-color: {{themeMainColor15}};--border-color: {{borderColor}};">
  <view class="sku-title">
    <text>
    {{title}} {{necessary && multiple?'（可多选）':''}}
    </text>
  </view>
  <view class="sku-row">
    <view class="sku-item {{util.shouldDisabled(item.stockNum, isProps, isNegative)?'sku-item-hidden':''}}" wx:for="{{models}}" wx:key="index">
      <view
        class="sku-btn {{util.shouldDisabled(item.stockNum, isProps, isNegative, item.textStatus)?'sku-btn-disabled':util.computeButtonClass(index, isProps ? selectedProps : selectedSpec)}}"
        bind:tap="onClick"
        data-disabled="{{util.shouldDisabled(item.stockNum, isProps, isNegative, item.textStatus)}}"
        data-id="{{item.id}}"
        data-index="{{index}}"
      >
        {{ item.v }} 
        <text wx:if="{{item.extPrice&&item.extPrice>0}}" class="ext-price">
           ¥{{util.formatOriginPrice(item.extPrice)}}
        </text>
      </view>
    </view>
  </view>
</view>

<wxs module="util">
  function computeButtonClass(i, selected) {
  var isSelected = false;
  if (selected.length) {
  isSelected = ~selected.indexOf(i)
  } else {
  isSelected = i === selected;
  }
  return isSelected ? 'sku-btn-highlight' : 'sku-btn-normal';
  }

  function formatOriginPrice(originPrice) {
   return parseFloat(originPrice / 100);
  }

  function shouldDisabled(stockNum, isProps, isNegative,textStatus) {
    // 属性
    if (isProps && textStatus === 0) {
      // 判断商品的属性禁用状态
      // 0 为关闭
      return true;
    }

    if (isProps || isNegative) {
      return false;
    }
    return stockNum === 0;
  }

  module.exports = { computeButtonClass: computeButtonClass, shouldDisabled: shouldDisabled,formatOriginPrice:formatOriginPrice }
</wxs>


