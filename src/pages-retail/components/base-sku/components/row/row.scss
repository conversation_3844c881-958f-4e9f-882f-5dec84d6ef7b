:host {
  --theme-main-color: red;
  --theme-main-rgb-color: rgba(25, 137, 250, 0.1);
}

.sku-tree-item {
  background-color: #fff;
  padding: 12px 4px 0 16px;

  .sku-title {
    margin-bottom: 12px;
    color: #333;
    font-size: 14px;
    font-weight: 500;
  }

  .sku-row {
    .sku-btn {
      display: inline-block;
      box-sizing: border-box;
      font-size: 14px;
      text-align: center;
      // (100% 屏幕宽度 - 左右padding - btn 之间的间距(2个)) / 3 就是一排放三个
      min-width: calc((100vw - 32px - 24px) / 3);
      padding: 0 16px;
      margin-bottom: 12px;
      height: 40px;
      line-height: 40px;
      background: #f2f3f5;
      border-radius: 4px;
      border: 0.5px solid transparent;

      .ext-price{
        margin-left: 3px;
        display: inline;
        color: var(--theme-main-color);
      }
    }

    .sku-btn-disabled {
      color: #c8c9cc;
      opacity: 0.8;
      .sku-price {
        color: inherit;
      }
    }

    .sku-btn-normal {
      background-color: rgba(242, 243, 245, 0.5);
      color: #666666;
    }

    .sku-btn-highlight {
      background-color: var(--theme-main-rgb15-color);
      color: var(--theme-main-color);
      border: 0.5px solid var(--border-color);
    }

    .sku-price {
      display: inline;
      color: var(--theme-main-color);
    }
  }

  .sku-row>.sku-item {
    margin-right: 12px;
    display: inline-block;
    text-decoration: inherit;
    &.sku-item-hidden {
      display: none;
      margin:0;
    }
  }
}