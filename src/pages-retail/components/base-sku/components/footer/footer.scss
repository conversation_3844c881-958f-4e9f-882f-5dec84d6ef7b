:host {
  --deactive-color: #2f2f34;
}

$button-height: 56px;

.footer {
  bottom: 0;
  background: white;
  padding: 0 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  border-top: 0.5px solid #ebedf0;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
}

.submit-button {
  margin: 8px 0;
  font-weight: 500;
  height: $button-height !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
  border: none !important;
  &.submit-button-normal {
    background-color: var(--theme-main-color);
  }
  &.submit-button-disabled {
    border-color: var(--theme-main-color);
    background-color: var(--theme-main-color);
    opacity: 0.5;
  }

  .text-container {
    display: flex;
    justify-content: space-between;
    display: flex;
    width: 300px;
    justify-content: space-between;
    line-height: $button-height;

    .price-container {
      display: flex;
      align-items: center;
      .origin-price {
        margin-left: 10px;
        font-size: 16px;
        font-weight: 400;
        font-family: Avenir;
        color:rgba(255, 255, 255, 0.4);
        text-decoration: line-through;
      }
    }
  }
}