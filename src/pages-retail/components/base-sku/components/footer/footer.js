Component({
  properties: {
    disabled: Boolean,
    loading: Boolean,
    notSelectedProps: {
      type: Array,
      value: [],
    },
    btnText: String,
    themeMainColor: String,
    subtotal: Number,
    originPrice: {
      type: Number,
      value: 0,
    },
    isCombo: Boolean,
  },
  methods: {
    addGoods() {
      const { notSelectedProps } = this.data;

      if (!notSelectedProps.length) {
        this.triggerEvent('addSku');
      } else {
        const title = notSelectedProps.map((item) => item.title).join('、');
        wx.showToast({
          title: `请选择${title}`,
          icon: 'none',
        });
      }
    },
  },
});
