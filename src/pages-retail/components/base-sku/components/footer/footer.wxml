<view class="footer">
  <van-button
    size="large"
    type="info"
    custom-class="{{disabled?'submit-button submit-button-disabled':'submit-button submit-button-normal'}}"
    style="--theme-main-color: {{themeMainColor}}"
    text-class="submit-content"
    loading="{{loading}}"
    round="{{true}}"
    block="{{true}}"
    bind:tap="addGoods"
  >
    <view class="text-container">
      <view class="price-container">
        <price style="--price-bottom:0; --price-color: #fff; --price-font-size:16px" price="{{subtotal}}" />
        <view wx:if="{{originPrice > subtotal}}" class="origin-price">
          ￥{{ utils.formatOriginPrice(originPrice) }}
        </view>
      </view>
      <text>
          {{ btnText }}
        </text>
    </view>
  </van-button>
</view>


<wxs module="utils">
  function formatOriginPrice(originPrice) {
   return parseFloat(originPrice / 100);
  }

  module.exports = { formatOriginPrice:formatOriginPrice }
</wxs>