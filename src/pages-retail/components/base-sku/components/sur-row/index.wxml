<view class="sku-tree-item" style="--theme-main-color:{{themeMainColor}};--theme-main-rgb-color:{{themeMainColor}};--theme-main-rgb15-color: {{themeMainColor15}};--border-color: {{borderColor}};">
  <view class="sku-row">
    <view class="sku-item" wx:for="{{models}}" wx:key="index">
      <view
        class="sku-btn {{util.computeButtonClass(index, selected)}}"
        bind:tap="onClick"
        data-id="{{item.subUniqueKey}}"
        data-index="{{index}}"
      >
        {{ item.title }}
      </view>
    </view>
  </view>
</view>

<wxs module="util">
  function computeButtonClass(i, selected) {
  var isSelected = false;
  if (selected.length) {
  isSelected = ~selected.indexOf(i)
  } else {
  isSelected = i === selected;
  }
  return isSelected ? 'sku-btn-highlight' : 'sku-btn-normal';
  }

  module.exports = { computeButtonClass: computeButtonClass}
</wxs>


