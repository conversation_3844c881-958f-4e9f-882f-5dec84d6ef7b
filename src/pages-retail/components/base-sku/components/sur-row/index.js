import {
  getThemeMainColor,
  getThemeMainRgbColorWithAlpha,
} from 'retail/util/shelf-global';

const getTheme = () => {
  return {
    themeMainColor: getThemeMainColor(),
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    themeMainColor15: getThemeMainRgbColorWithAlpha(0.15),
    borderColor: getThemeMainRgbColorWithAlpha(0.5),
  };
};

Component({
  data: {
    ...getTheme(),
  },
  properties: {
    models: Array,
    selected: Number,
    title: String,
  },
  lifetimes: {
    attached() {
      this.setData(getTheme());
    },
  },
  methods: {
    onClick({ currentTarget }) {
      const {
        dataset: { index },
      } = currentTarget;
      this.triggerEvent('selectSur', { index });
    },
  },
});
