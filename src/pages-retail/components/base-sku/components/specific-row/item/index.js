import {
  getPicture,
  DefaultShowItemNumber,
  getSkuTextArr,
  getPropTextArr,
} from 'retail/components/base-sku/helper';

Component({
  data: {
    list: [],
    sourceList: [],
    isUnfold: false,
  },
  properties: {
    group: {
      type: Array,
      value: {},
      observer(newVal = []) {
        if (!newVal.length) return;
        this.formatList(newVal[0].combo_sub_items);
      },
    },
    themeMainColor: String,
  },
  methods: {
    formatList(arr = []) {
      const list = arr
        .slice(0, this.data.isUnfold ? arr.length : DefaultShowItemNumber)
        .map((item) => {
          const prop =
            item.props?.reduce((pre, item) => {
              return [...pre, ...item.text_models];
            }, []) || [];
          return {
            ...item,
            picture: getPicture(item.picture),
            skuDescArr: getSkuTextArr(
              item.sku_relateds[0],
              item.item_properties
            ),
            propDescArr: getPropTextArr(
              prop,
              prop.map(({ id }) => id)
            ),
            total: item.sku_relateds[0]?.combine_num || 1,
          };
        });
      this.setData({
        sourceList: arr,
        list,
      });
    },
    toggleCollapse() {
      this.setData(
        {
          isUnfold: !this.data.isUnfold,
        },
        () => {
          this.formatList(this.data.sourceList);
        }
      );
    },
  },
});
