.specific-combo {
  &__label {
    font-size: 14px;
    color: #323233;
    font-weight: bold;
    padding-bottom: 12px;
  }

  &__item {
    display: flex;
    align-items: center;
    padding-bottom: 8px;

    .item-image {
      width: 64px;
      height: 64px;
      margin-right: 8px;
      border-radius: 8px;
    }

    .item-content {
      flex: 1;
      position: relative;
      height: 64px;

      &__head {
        position: absolute;
        top: 0;
      }

      &__title {
        font-size: 14px;
        color: #323233;
        font-weight: bold;
        padding-bottom: 4px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      &__total {
        font-size: 12px;
        color: #969799;
        position: absolute;
        bottom: 0;
      }
    }

    &--sold-out {
      position: relative;

      .item-image {
        -webkit-mask: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3));
        mask: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3));
      }

      .item-content {
        &__title {
          color: #c8c9cc;
        }

        &__total {
          color: #c8c9cc;
        }
      }

      .item-sold-out {
        position: absolute;
        bottom: 20px;
        right: 16px;
        font-size: 12px;
        color: #c8c9cc;
      }
    }
  }
}