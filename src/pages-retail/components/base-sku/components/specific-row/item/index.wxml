<view class="specific-combo">
  <collapse is-show-collapse="{{util.isShowCollapse(sourceList)}}" is-unfold="{{isUnfold}}" bind:toggleCollapse="toggleCollapse">
    <view class="specific-combo__label">已包含以下商品</view>
    <view wx:for="{{list}}" wx:key="index" class="specific-combo__item {{item.isSoldOut ? 'specific-combo__item--sold-out' : ''}}">
      <image
        class="item-image"
        inner-class="item-image--inner"
        src="{{item.picture}}"
        mode="aspectFit"
      />
      <view class="item-content">
        <view class="item-content__head">
          <view class="item-content__title">{{ item.title }}</view>
          <sku-prop-text
            wx:if="{{item.skuDescArr.length || item.propDescArr.length}}"
            sku-arr="{{item.skuDescArr}}"
            prop-arr="{{item.propDescArr}}"
            color="{{themeMainColor}}"
            sold-out="{{item.isSoldOut}}"
          />
        </view>
        <view class="item-content__total">x{{ item.total }}</view>
      </view>
      <view wx:if="{{item.isSoldOut}}" class="item-sold-out"> 已售罄 </view>
    </view>
  </collapse>
</view>


<wxs module="util">
  function isShowCollapse(list) {
   return list.length > 6;
  }

  module.exports = { isShowCollapse:isShowCollapse }
</wxs>


