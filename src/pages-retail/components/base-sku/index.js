import * as allFunc from './extends';
import { HeaderStatus, Scene } from './helper';
import { ThemeColorWithComponent } from 'retail/util/behaviors/themeBehavior';

let extendFunc = {};
for (const key of Object.keys(allFunc)) {
  extendFunc = { ...extendFunc, ...allFunc[key] };
}

const initialStatus = {
  specifications: '',
  subtotal: 0,
  disabled: false,
  inputNumberDisabled: false,
  count: 1,
  rowsOfProps: [],
  sku: [],
  surSelected: 0,
  // skuModel中第一个元素：商品基本sku信息，用来处理多属性商品获取skuId
  spec: {},
  notSelectedProps: [],
  maxStock: 99, // 必选品默认99
  // add
  headerStatus: HeaderStatus.Thumbnail,
  toView: '',
  contentScrollY: true,
  selectedComboItems: [], // 选中的套餐
};

Component({
  behaviors: [ThemeColorWithComponent],
  options: {
    multipleSlots: true,
  },
  properties: {
    title: String,
    goodsNo: String,
    picture: String,
    sourcePictures: Array,
    originPrice: Number, // 基本价格
    isNegativeStock: Boolean,
    isLite: Boolean, // 是否为 Lite 店铺
    loading: {
      type: Boolean,
      observer(val) {
        this.setData({
          disabled: val,
          inputNumberDisabled: val,
        });
      },
    },
    show: {
      type: Boolean,
      value: false,
      observer(show) {
        if (!show) {
          this.clear();
          return;
        }
        this.imgInit();
      },
    },
    props: {
      type: Array,
      value: [],
      observer(val) {
        this.buildPropsModel(val);
      },
    },
    skuModels: {
      type: Array,
      value: [],
      observer(val) {
        this.buildSkuModel(val);
      },
    },
    // 以下是新增属性
    // 是否立即显示大图
    showLargeImage: {
      type: Boolean,
      value: false,
    },
    // 场景
    scene: {
      type: String,
      value: Scene.Common, // normal 普通, surcharge 必选, combo 套餐
    },
    comboType: Number, // 套餐类型,scene=combo时有效
    // 全部的商品信息
    goodsInfo: {
      type: Object,
      value: {},
    },
    submitBtnText: {
      type: String,
      value: '加入购物车',
    },
    isSubComboSku: Boolean, // 是否是套餐子商品
    /** 营销标 */
    promotionTags: {
      type: Array,
      value: [],
    },
    /** 赠品的最大可选库存 */
    addOnStockNum: {
      type: Number,
      value: -1,
    },
  },

  data: {
    // depClone
    ...JSON.parse(JSON.stringify(initialStatus)),
  },

  methods: {
    ...extendFunc,
    clear() {
      setTimeout(() => {
        this.setData(initialStatus);
        this.skuTree = null;
        this._query = null;
        this.getItemWithIndex = null;
        this._isTransitioning = false;
        this._canScroll = false;
        this._skuMap = null;
      });
    },
    closePopup() {
      this.triggerEvent('closeSku');
    },

    onNumberChange({ detail: { value } }) {
      this.setData({
        count: value,
      });
    },

    cantAddMore({ detail = {} }) {
      if (detail.type === 'plus') {
        wx.showToast({
          icon: 'none',
          title: '该商品已经没有更多库存',
          duration: 600,
        });
      }
    },
  },
});
