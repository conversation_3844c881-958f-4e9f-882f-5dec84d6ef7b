import { HeaderStatus } from '../helper';

// 图片逻辑处理
export const handlesForImg = {
  // 初始化img
  imgInit() {
    // 图片处理,是否立即显示大图
    if (this.data.showLargeImage) {
      this.setData({
        headerStatus: HeaderStatus.Init,
      });
      setTimeout(() => {
        this.getScrollStatus();
      }, 1000);
    }
  },
  // 检查是否能滚动
  getScrollStatus() {
    if (!this._query) {
      this._query = this.createSelectorQuery();
      this._query
        .select(`.detail-content_${this.data.scene}`)
        .boundingClientRect();
      this._query.select(`#body_${this.data.scene}`).boundingClientRect();
    }
    this._query.exec((res) => {
      if (res[0] && res[1]) {
        this._canScroll = res[1].height - res[0].height > 5;
      }
    });
  },
  handleContentScroll({ detail }) {
    this.setData({
      toView: '',
    });
    if (!this._isTransitioning && detail.deltaY < 0) {
      this.changeHeaderStatus({ detail: HeaderStatus.Thumbnail });
    }
  },

  // 图片显示状态处理
  changeHeaderStatus({ detail }) {
    if (
      this.data.headerStatus === detail ||
      this._isTransitioning ||
      (detail === HeaderStatus.Thumbnail && !this._canScroll)
    ) {
      return;
    }
    this._isTransitioning = true; // 防止多次触发
    let contentScrollY = true;
    let toView = '';

    // 转场缩略图时，先禁用掉滚动
    if (detail === HeaderStatus.Thumbnail) {
      contentScrollY = false;
    }

    // 转为大图时要滚动到顶部区域
    if (detail === HeaderStatus.LargeImage) {
      toView = `body_${this.data.scene}`;
    }

    this.setData(
      {
        contentScrollY,
        toView,
        headerStatus: detail,
      },
      () => {
        // 标记正在转场，这个时间要 >= 动画时间，避免转场过程中 scroll 触发
        setTimeout(() => {
          this.getScrollStatus();
          this._isTransitioning = false;
          this.setData({
            contentScrollY: true,
          });
        }, 600);
      }
    );
  },
};
