export const changeSku = {
  // 选属性
  onSelectProps({ detail }) {
    const { row, index, multiple } = detail;

    // 这里直接修改了 this.data，不是最佳实践
    let s = this.data.rowsOfProps[row].currentSelected;
    const hasIndex = s.indexOf(index);

    if (~hasIndex) {
      s.splice(hasIndex, 1);
    } else if (multiple) {
      // 支持多选，需要加入到数组中
      s.push(index);
    } else {
      // 没选中 且 非多选，直接修改数组内容
      s = [index];
    }

    // this.setDisabledStatus();
    // FIXME: hack, 强制同步 rowsOfProps 的值，让 concatSpec 能够计算正确。
    // 但是多规格的不需要，因为它没有改动 data 的引用，而 s = [index] 会改变引用。当然如果你觉得这样不太好的话，也可以把多规格改成一样的形式
    this.setData(
      {
        [`rowsOfProps[${row}].currentSelected`]: s,
      },
      () => {
        this.setData({
          specifications: this.concatSpec(),
          subtotal: this.computeSubTotal(),
        });
        this.setDisabledStatus();
      }
    );
  },
  // 选规格
  onSelectSpec({ detail }) {
    const { row, index } = detail;

    if (this.data.sku[row].currentSelected === index) {
      wx.showToast({
        title: `${this.data.sku[row].title}至少选一个`,
        icon: 'none',
      });
    }
    this.data.sku[row].currentSelected = index;

    this.setData({
      subtotal: this.computeSubTotal(),
      specifications: this.concatSpec(),
      [`sku[${row}].currentSelected`]: index,
    });
    //  新增套餐逻辑
    this.selectComboItem();
    // 更新库存值
    this.setMaxStock();
  },
  // 必选品逻辑
  onSelectSur({ detail }) {
    this.setData({
      surSelected: detail.index,
    });
  },
};
