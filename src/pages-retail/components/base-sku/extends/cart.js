import { COMBO_TYPE, Scene, formatSpecificComboInfoParams } from '../helper';
import { flatMap } from 'retail/util/base';

// 加购逻辑
export const addCarts = {
  // 必选品加购
  addSurSku() {
    const { sku, surSelected, goodsInfo, count, title, picture } = this.data;
    const { uniqueKey, price } = goodsInfo;
    let subTitle = '';
    if (sku?.[surSelected]?.subUniqueKey) {
      subTitle = sku[surSelected].title;
    }
    const payLoad = {
      scene: Scene.Surcharge,
      uniqueKey: sku?.[surSelected]?.subUniqueKey ?? uniqueKey,
      price,
      title,
      pictureUrl: picture,
      goodsNum: count,
      subTitle,
    };

    this.triggerEvent('addGoods', payLoad);
  },
  // 普通商品加购
  addCommonSku() {
    let skuNo;
    try {
      const { barcode, sku_no } = this.getItemWithIndex();
      skuNo = this.data.isLite ? barcode : sku_no;
    } catch {
      skuNo = this.data.goodsNo;
    }

    const detailModels = flatMap(
      this.data.rowsOfProps,
      ({ rowModels, currentSelected }) =>
        currentSelected.map((index) => rowModels[index])
    ).map((prop) => {
      return {
        valId: prop.id,
        id: prop.parentId,
        price: prop.price,
      };
    });

    /**
     * NOTE:
     * this.getItemWithIndex只能多规格商品调用
     * 因为多属性商品（非多规格）无法调用 this.getItemWithIndex 方法
     * 但是呢：新的加车接口需要goodsId和skuId，所以只能取skuModels中第一个元素：商品基本sku信息
     *
     * */
    const { item_id, sku_id } = this.getItemWithIndex
      ? this.getItemWithIndex()
      : this.data.spec;
    const skuId = sku_id;
    const goodsId = item_id;

    let params = {
      scene: Scene.Common,
      skuNo,
      amount: this.data.count,
      detailModels,
      skuId,
      goodsId,
    };

    if (this.data.isSubComboSku) {
      params = {
        goods: {
          goodsId,
          skuId,
          propertyIds: detailModels.map((i) => i.valId),
        },
        prevNum: 0,
        nextNum: this.data.count,
        comboGroupId: this.data.goodsInfo.comboGroupId,
      };
    }

    this.triggerEvent('addGoods', params);
  },
  // 固定套餐加购
  addSpecificSku() {
    const { comboType, selectedComboItems, spec, count } = this.data;
    const { item_id, sku_id, sku_no } = this.getItemWithIndex
      ? this.getItemWithIndex()
      : spec;

    const params = {
      scene: Scene.Combo,
      comboInfo: {
        isCombo: true,
        comboType,
        comboDetail: formatSpecificComboInfoParams(selectedComboItems),
      },
      skuNo: sku_no,
      amount: count,
      detailModels: [],
      skuId: sku_id,
      goodsId: item_id,
    };
    this.triggerEvent('addGoods', params);
  },
  // 自选套餐
  addOptionalSku() {
    const { goodsInfo, selectedComboItems, comboType, count } = this.data;
    const { sku_no, sku_id, item_id } = goodsInfo.item_sku_models[0];
    const comboGroups = selectedComboItems.map((item) => {
      item.comboSubSkus = item.subComboList;
      delete item.subComboList;
      return item;
    });
    const params = {
      scene: Scene.Combo,
      comboInfo: {
        isCombo: true,
        comboType,
        comboDetail: JSON.stringify({ comboGroups }),
      },
      skuNo: sku_no,
      amount: count,
      detailModels: [],
      skuId: sku_id,
      goodsId: item_id,
    };
    this.triggerEvent('addGoods', params);
  },
  // 商品加购
  addSku() {
    const { scene, comboType } = this.data;
    // 自选套餐特殊处理
    if (scene === Scene.Combo && comboType === COMBO_TYPE.Optional) {
      if (this.validateAddCombo()) {
        this.setData({
          loading: true,
        });
        this.addOptionalSku();
      }
      return;
    }

    this.setData({
      loading: true,
    });
    if (scene === Scene.Surcharge) {
      this.addSurSku();
    } else if (scene === Scene.Common) {
      this.addCommonSku();
    } else if (comboType === COMBO_TYPE.Specific) {
      this.addSpecificSku();
    }
  },
};
