import {
  getPicture,
  getExtraSkuPrice,
  getPropPrice,
  Validator,
  updateCart,
  getSkuText,
  deleteCart,
} from '../helper';
import { querySKU } from 'retail/util/api';

export const comboFunc = {
  getComboData(detail) {
    const { goods, comboGroupId } = detail;

    const { goodsInfo } = this.data;
    // 获取当前套餐分组
    const { rule, combo_sub_items, name } =
      goodsInfo.combo_detail.combo_groups.find(
        ({ goods_combo_group_id }) => comboGroupId === goods_combo_group_id
      );
    // 获取套餐分组中的商品
    const originGoodsData = combo_sub_items.find(
      ({ item_id }) => item_id === goods.goodsId
    );
    // 获取当前商品的sku
    const skuData = originGoodsData.sku_relateds.find(
      ({ sku_id }) => sku_id === goods.skuId
    );

    return {
      rule,
      groupName: name,
      originGoodsData,
      skuData,
    };
  },
  handleProps(originGoodsData, propertyIds) {
    const initObj = { propData: [], selectProp: [] };
    return (
      originGoodsData.props?.reduce((pre, item) => {
        const selectProp = item.text_models.find((i) =>
          propertyIds.includes(i.id)
        );
        if (selectProp) {
          pre.selectProp.push({
            id: item.id,
            name: item.prop_name,
            valId: selectProp.id, // 固定套餐属性是唯一的
            valName: selectProp.text_name,
            price: selectProp.price,
          });
        }
        pre.propData = [...pre.propData, ...item.text_models];
        return pre;
      }, initObj) || initObj
    );
  },
  alterComboGoods({ detail }) {
    const { goods, nextNum, comboGroupId, prevNum } = detail;
    const { selectedComboItems } = this.data;
    const { rule, groupName, originGoodsData, skuData } =
      this.getComboData(detail);
    const { propertyIds = [] } = goods;

    // 获取当前商品的属
    const { propData, selectProp } = this.handleProps(
      originGoodsData,
      propertyIds
    );

    const prop = {
      selectedComboList: [...selectedComboItems],
      group: {
        id: comboGroupId,
        name: groupName,
        rule: JSON.parse(rule) || {},
      },
      goods: {
        id: goods.goodsId,
        goodsName: originGoodsData.title,
        num: nextNum,
        skuId: goods.skuId,
        skuName: skuData.sku,
        originPrice: skuData?.price || 0,
        price: getExtraSkuPrice(skuData, originGoodsData.item_properties),
        picture: getPicture(originGoodsData.picture),
        skuDesc: getSkuText(skuData?.sku, originGoodsData.item_properties),
        snapshotkey: originGoodsData.snap_key,
        props: selectProp,
        propertyIds,
        prevNum,
      },
    };
    // 价格 = sku.price+ 额外的sku价格+ 属性价格
    const price =
      ((skuData?.price || 0) +
        getExtraSkuPrice(skuData, originGoodsData.item_properties) +
        getPropPrice(propData, propertyIds)) *
      (nextNum - prevNum);

    const getSpecifications = (selectedComboItems) => {
      return selectedComboItems
        .reduce((pre, curr) => {
          if (curr.subComboList?.length) {
            pre.push(`已选${curr.subComboList?.length}件${curr.name}`);
          }
          return pre;
        }, [])
        .join(';');
    };

    // 修改数量
    if (nextNum > 0) {
      const data = Validator.updateCart(prop);
      if (data.value) {
        const selectedComboItems = updateCart(prop);
        this.setData(
          {
            selectedComboItems,
            subtotal: this.data.subtotal + price,
            specifications: getSpecifications(selectedComboItems),
          },
          () => {
            this.setDisabledStatus();
          }
        );
        return;
      }
      wx.showToast({ title: data.label, icon: 'none' });
      return;
    }

    // 删除
    if (nextNum === 0) {
      this.setData(
        {
          selectedComboItems: deleteCart(prop),
          subtotal: this.data.subtotal + price,
          specifications: getSpecifications(selectedComboItems),
        },
        () => {
          this.setDisabledStatus();
        }
      );
    }
  },
  openSku({ detail }) {
    const {
      goods: { relatedSku, skuProps, goodsId, itemProperties },
      comboGroupId,
      maxPlusNum,
    } = detail;
    // console.log('🚀 ~ file: funs.js:534 ~ openSku ~ maxPlusNum', maxPlusNum);
    // eslint-disable-next-line no-multi-assign
    const skuMap = (this._skuMap = this._skuMap || new Map());
    const itemId = goodsId;

    if (skuMap.has(itemId)) {
      const cache = skuMap.get(itemId);
      cache.skuInformation.maxPlusNum = maxPlusNum;
      this.triggerEvent('openSubSkuPop', cache);
      return;
    }
    // item_sku_models
    querySKU({ itemId }).then((res) => {
      const skuInformation = {
        ...res,
        itemProperties,
        comboGroupId,
        maxPlusNum,
        sourcePictures: JSON.parse(res.picture),
      };
      const willBeSaved = {
        skuInformation,
        skuModels: res.item_sku_models.filter(
          ({ sku_id }, index) => index === 0 || relatedSku.includes(sku_id)
        ),
        skuProps: skuProps ?? [],
        skuGoodsNo: res.goods_no,
      };

      // 缓存最近5个,防止内存溢出
      if (skuMap.size > 5) {
        skuMap.delete(skuMap.keys().next().value);
      }
      skuMap.set(itemId, willBeSaved);
      this.triggerEvent('openSubSkuPop', willBeSaved);
    });
  },
  /**
   * 加购前校验
   * @param {Object} param
   * @param {Boolean} param.onlyCheck 是否只校验,不弹toast
   */
  validateAddCombo(options = {}) {
    const { onlyCheck = false } = options;
    const { goodsInfo, selectedComboItems } = this.data;
    const originComboItems = goodsInfo.combo_detail.combo_groups;

    if (selectedComboItems.length === 0) {
      !onlyCheck && wx.showToast({ title: '请选择套餐商品', icon: 'none' });
      this.scrollToView('combo-wrap');
      return false;
    }

    if (originComboItems.length !== selectedComboItems.length) {
      const selectedIds = selectedComboItems.map(({ id }) => id);
      const arr = originComboItems.filter(
        ({ goods_combo_group_id }) =>
          !selectedIds.includes(goods_combo_group_id)
      );
      const names = arr.map(({ name }) => name);
      if (!onlyCheck) {
        this.scrollToView(
          'optional-combo-sku-' + arr?.[0].goods_combo_group_id
        );
        wx.showToast({
          title: `请选择${[
            names.slice(0, names.length - 1).join('、'),
            names[names.length - 1],
          ]
            .filter((item) => item)
            .join('和')}`,
          icon: 'none',
        });
      }
      return false;
    }
    // 获取当前套餐分组
    const res = selectedComboItems.find(({ rule, subComboList }) => {
      const num = subComboList.reduce((pre, i) => {
        return pre + i.num;
      }, 0);

      return JSON.parse(rule)?.selectNum - num > 0;
    });

    if (res) {
      !onlyCheck &&
        wx.showToast({
          title: `${res.name}需选择${JSON.parse(res.rule)?.selectNum}份商品`,
          icon: 'none',
        });
      return false;
    }

    return true;
  },
};
