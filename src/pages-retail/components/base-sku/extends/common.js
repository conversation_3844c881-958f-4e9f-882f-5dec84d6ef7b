import { flatMap } from 'retail/util/base';
import { COMBO_TYPE, Scene, getExtraSkuPrice } from '../helper';

export const commonFunc = {
  pickSpecifications(list) {
    return flatMap(list, ({ rowModels, currentSelected }) => {
      if (Array.isArray(currentSelected)) {
        return currentSelected.map((selected) => rowModels[selected].v);
      }
      return rowModels[currentSelected].v;
    });
  },

  concatSpec(s = this.data.sku, p = this.data.rowsOfProps) {
    const spec = this.pickSpecifications(s);
    const props = this.pickSpecifications(p);
    return spec.concat(props).join(',');
  },

  computeSubTotal(rowsOfProps = this.data.rowsOfProps) {
    const { goodsInfo, isSubComboSku } = this.data;

    let specPrice =
      this.getItemWithIndex?.()?.price ?? this.data.originPrice ?? 0;
    // 属性也是有价格的
    const subTotalOfProps = flatMap(
      rowsOfProps,
      ({ currentSelected, rowModels }) =>
        currentSelected.map((selected) => rowModels[selected].price)
    ).reduce((subtotal, num) => subtotal + num, 0);
    // 套餐子商品,加额外的价格
    if (isSubComboSku && this.getItemWithIndex) {
      specPrice += getExtraSkuPrice(
        this.getItemWithIndex(),
        goodsInfo.itemProperties
      );
    }

    return subTotalOfProps + specPrice;
  },
  // 计算选中的套餐
  selectComboItem() {
    const {
      scene,
      goodsInfo: { combo_detail } = {},
      sku = [],
      comboType,
    } = this.data;

    // 0 固定套餐 1 自选套餐
    if (scene === Scene.Combo && comboType === COMBO_TYPE.Specific) {
      const { combo_groups = [] } = combo_detail || {};
      // 无规格
      if (!sku.length) {
        this.setData({
          selectedComboItems: [combo_groups[0]],
        });
        return;
      }
      // 单/多规格
      const { sku_id } = this.getItemWithIndex
        ? this.getItemWithIndex()
        : this.data.spec;
      const comboItem = combo_groups.find((item) => {
        return item.sku_id === sku_id;
      });
      this.setData({
        selectedComboItems: [comboItem],
      });
    }
  },

  setMaxStock() {
    const { stock_num: stockNum } = this.getItemWithIndex
      ? this.getItemWithIndex()
      : this.data.spec;
    const { isNegativeStock, isSubComboSku, goodsInfo, addOnStockNum } =
      this.data;
    let maxStock = 99;

    if (addOnStockNum && addOnStockNum !== -1) {
      maxStock = addOnStockNum / 1000;
    } else if (isSubComboSku) {
      // 子套餐库存受套餐控制
      maxStock = goodsInfo.maxPlusNum;
    } else {
      maxStock = isNegativeStock ? 99 : stockNum / 1000;
    }
    this.setData({
      maxStock,
    });
  },
  // 设置disabled状态
  setDisabledStatus() {
    const { scene, comboType } = this.data;
    if (scene === Scene.Combo && comboType === COMBO_TYPE.Optional) {
      this.setData({
        disabled: !this.validateAddCombo({ onlyCheck: true }),
      });
    } else {
      const notSelectedProps = this.data.rowsOfProps.filter(
        (item) => item.necessary && !item.currentSelected.length
      );
      const disabled = notSelectedProps.length > 0;
      this.setData({
        disabled,
        notSelectedProps,
      });
    }
  },

  scrollToView(anchor) {
    this.setData({
      toView: anchor,
    });
    setTimeout(() => {
      this.setData({
        toView: '',
      });
    }, 100);
  },
};
