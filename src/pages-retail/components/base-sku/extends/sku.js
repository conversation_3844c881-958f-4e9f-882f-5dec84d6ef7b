import {
  COMBO_TYPE,
  setModelForEmptyTree,
  buildTree,
  propModelAdapter,
  getSelectedItem,
  findDefaultSelected,
  getTotalSotckViaId,
  Scene,
} from '../helper';

// sku逻辑处理
export const handlesSku = {
  // 必选品sku
  buildSurchargeSku(value) {
    this.setData({
      sku: value,
    });
  },
  // 无规格商品
  buildNoSku(value) {
    const [spec, ...models] = value || [];
    if (!value || !value.length || (spec && !models.length)) {
      this.setData(
        {
          sku: [],
          // 商品基本sku信息：包含skuId和goodsId(item_id)
          spec,
          subtotal: this.computeSubTotal(),
        },
        () => {
          // 固定无规格套餐
          this.selectComboItem();
          this.setMaxStock();
        }
      );
      return true;
    }
  },
  // 子套餐计算规格加价
  getExtPrice({ k_id, id }) {
    const { isSubComboSku, goodsInfo } = this.data;
    let extPrice = 0;
    try {
      if (isSubComboSku) {
        extPrice =
          goodsInfo.itemProperties
            .find((item) => item.dict_id === +k_id)
            .sub_properties.find((item) => item.dict_id === +id)?.spec_property
            ?.price || 0;
      }
    } catch (error) {
      console.log('子商品规格加价计算错误', error);
    }
    return extPrice;
  },
  // 多规格sku
  buildMutipleSku(value) {
    const [spec, ...models] = value || [];
    // 所有的 SKU 信息
    const allSkus = JSON.parse(spec.sku);
    // Note: sku的values字段是Object，经过json.parse后会无序，这里改为通过valuesSorted（数组）来构建sku
    const allIds = allSkus.map(({ valuesSorted = [] }) =>
      valuesSorted.map((item) => Object.keys(item)[0])
    );
    // 构建 UI 上展示的 SKU 信息对象数组
    // Note: sku的values字段是Object，经过json.parse后会无序，这里改为通过valuesSorted（数组）来构建sku
    const sku = allSkus.map(({ k, k_id, valuesSorted = [] }, index) => ({
      title: k,
      currentSelected: 0,
      rowModels: valuesSorted.map((item) => {
        const id = Object.keys(item)[0];

        return {
          id,
          extPrice: this.getExtPrice({ k_id, id }),
          v: item[id],
          stockNum: getTotalSotckViaId(id, models, index),
        };
      }),
    }));

    this.skuTree = setModelForEmptyTree(
      models,
      allIds.length
    )(buildTree(allIds));

    // 生成了一个由 sku id 层层索引的树
    // allIds
    //   |> buildTree
    //   |> setModelForEmptyTree(models, allIds.length)
    //   |> (tree => (this.skuTree = tree));

    this.getItemWithIndex = getSelectedItem(allIds, this.skuTree);

    // eslint-disable-next-line no-unused-vars
    for (const item of sku) {
      // 设置每种规格中默认选择的规格， 如果打开负库存开关，则默认选择第一个，反之找到那个有库存的规格。
      item.currentSelected = findDefaultSelected(
        item,
        this.data.isNegativeStock
      );
    }

    this.data.sku = sku;
    // 套餐
    this.selectComboItem();
    this.setData(
      {
        sku,
        subtotal: this.computeSubTotal(),
        specifications: this.concatSpec(sku),
      },
      () => {
        this.setMaxStock();
      }
    );
  },
  /**
   * 构建 SKU 模型
   *
   * @param {Array} value
   */
  buildSkuModel(value = []) {
    const { scene, comboType } = this.data;
    if (scene === Scene.Surcharge) {
      this.buildSurchargeSku(value);
    } else if (scene === Scene.Combo && comboType === COMBO_TYPE.Optional) {
      this.setDisabledStatus();
    } else {
      if (this.buildNoSku(value)) return;
      this.buildMutipleSku(value);
    }
  },
  buildPropsModel(props = []) {
    const { scene } = this.data;
    // 套餐不存在属性处理
    if (scene === Scene.Combo) return;

    const rowsOfProps = (props || []).map(propModelAdapter);
    this.setData(
      {
        rowsOfProps,
        subtotal: this.computeSubTotal(rowsOfProps),
        specifications: this.concatSpec(undefined, rowsOfProps),
      },
      () => {
        this.setDisabledStatus();
      }
    );
  },
};
