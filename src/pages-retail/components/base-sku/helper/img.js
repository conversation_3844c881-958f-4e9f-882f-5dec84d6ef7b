import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

export function getImageHeight({ height, width, ratio = 1 }) {
  const SYS_WINDOW_WIDTH = wx.getSystemInfoSync().windowWidth;
  if (height && width) {
    ratio = height / width;
  }
  ratio = Math.min(2, ratio);
  ratio = Math.max(1 / 2.5, ratio);
  return Math.floor(SYS_WINDOW_WIDTH * ratio);
}

export const getPicture = (data) => {
  return data ? cdnImage(JSON.parse(data)[0].url, '!400x0.jpg') : '';
};
