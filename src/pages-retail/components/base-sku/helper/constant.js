// 套餐类型
export const COMBO_TYPE = {
  // 固定套餐
  Specific: 0,
  // 自选套餐
  Optional: 1,
};

export const HeaderStatus = {
  Init: 'init', // 初始状态
  Thumbnail: 'thumbnail', // 缩略图
  LargeImage: 'largeImage', // 大图(也就是恢复初始状态，但是有动画)
};

export const DefaultImageHeight = 375;

// sku 组件的场景
export const Scene = {
  Common: 'common', // 普通商品
  Surcharge: 'surcharge', //  必选附加商品
  Combo: 'combo', // 套餐
};

// 用于套餐默认显示最大数量
export const DefaultShowItemNumber = 6;
