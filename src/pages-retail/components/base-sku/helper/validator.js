export const Validator = {
  updateCart({ selectedComboList, group, goods }) {
    const groupData = selectedComboList.find(({ id }) => group.id === id);

    if (goods.num > group.rule.selectNum) {
      return {
        label: '已达到最大选购数量',
        value: false,
      };
    }

    if (!groupData) {
      return { value: true };
    }

    const goodsNum =
      goods.prevNum === 0
        ? goods.num +
          groupData.subComboList.reduce(
            (pre, { goodsId, num }) => (goodsId === goods.id ? pre + num : pre),
            0
          )
        : goods.num;

    const totalGoodsNum =
      groupData.subComboList
        .filter(({ goodsId }) => goods.id !== goodsId)
        .reduce((pre, { num }) => {
          return pre + num;
        }, 0) + goodsNum;

    if (totalGoodsNum > group.rule.selectNum) {
      return {
        label: '已达到最大选购数量',
        value: false,
      };
    }

    if (group.rule.supportRepeatSelect) {
      return {
        value: true,
      };
    }

    const selectedGoods = groupData.subComboList.find(
      ({ goodsId }) => goods.id === goodsId
    );

    return {
      value: !selectedGoods,
      label: selectedGoods ? '不能加购相同商品' : '',
    };
  },
};
