import { set } from '../../../util/base';

// 根据 sku 中的 s1,s2,s3…s5 的值，生成一个连续的路径树。s1.s2.s3 类似
export const setModelForEmptyTree = (models, length) => (tree) => {
  models.forEach((model) => {
    const path = Array.from({ length }, () => 0)
      .map((_, i) => model[`s${i + 1}`])
      .join('.');
    set(path, model, tree);
  });
  return tree;
};

export function buildTree(ids, breads = []) {
  const [first, ...rest] = ids;
  if (!first) return null;

  return first.reduce((result, id) => {
    breads.push(id);
    result[id] = buildTree(rest, breads);
    return result;
  }, {});
}

export const propModelAdapter = (props) => {
  const {
    id,
    prop_name,
    name,
    text_models,
    detail_models,
    is_multiple: multiple,
    is_necessary: necessary,
  } = props;
  let models;
  if (text_models) {
    models = text_models.map((model) => ({
      v: model.text_name,
      id: model.id,
      price: model.price,
      parentId: id,
      textStatus: model.text_status,
    }));
  } else {
    models = detail_models.map((model) => ({
      v: model.val_name,
      id: model.val_id,
      price: model.price,
      parentId: model.id,
      textStatus: model.text_status,
    }));
  }

  const defaultSelectedIndex = models.findIndex(
    (model) => model.textStatus !== 0
  );

  const shouldSelectFirstEnabledProp = models.every(
    (model) => model.price === 0
  );

  return {
    title: prop_name ?? name,
    isProps: true,
    multiple,
    necessary,
    // 属性分组全部不加价默认选中第一个，混合加价默认无选中
    currentSelected:
      shouldSelectFirstEnabledProp && defaultSelectedIndex >= 0
        ? [defaultSelectedIndex]
        : [],
    rowModels: models,
  };
};

export function getSelectedItem(allIds, skuTree) {
  return function () {
    const indexs = this.data.sku.map((item) => item.currentSelected || 0);
    return allIds.reduce((theGoods, ids, i) => {
      return theGoods[ids[indexs[i] || 0]];
    }, skuTree);
  };
}

export const findDefaultSelected = (sku, isNegative) => {
  if (isNegative) return 0;
  // 一般来说，为了让 sku 可售，第一行就肯定会存在能够被选中的规格，所以直接 find 就行了
  // return models.findIndex(sku => sku.stock_num > 0);
  return sku.rowModels.findIndex((model) => model.stockNum > 0);
};

/**
 * 通过 id 数组获取总的库存
 *
 * @param {Array<number>} values
 * @param {Array<Object>} models
 * @param {number} index
 *
 * @returns {number}
 */
export const getTotalSotckViaId = (id, models, index) => {
  const itemsFound = models.filter((item) => item[`s${index + 1}`] === +id);
  if (itemsFound.length === 0) return 0;
  return itemsFound.reduce((prev, acc) => prev + acc.stock_num, 0);
};
