const getFormatPriceObj = (price = 0) => {
  const formatPrice = parseFloat((price / 100).toFixed(2));
  return {
    price: Math.abs(formatPrice),
    minus: formatPrice >= 0 ? '' : '-',
  };
};

export const getPropTextArr = (propData, propIds) => {
  return propData
    .filter(({ id }) => propIds.includes(id))
    .reduce(
      (prev, { text_name, price }) => [
        ...prev,
        { ...getFormatPriceObj(price), text: text_name },
      ],
      []
    );
};

export const getSkuTextArr = (selectedSku, skuTree) => {
  const arr = [];
  for (let index = 0; ; index++) {
    const sId = selectedSku[`s${index + 1}`];
    if (!sId) {
      break;
    }
    const val = skuTree[index].sub_properties?.find(
      (item) => item.dict_id === sId
    );
    arr.push({
      ...getFormatPriceObj(val?.spec_property?.price),
      text: val.text,
    });
  }
  return arr;
};

// 处理规格加价
const getExtPrice = ({ k_id, id }, itemProperties) => {
  if (!itemProperties || itemProperties.length === 0) return '';
  let extPrice = 0;
  try {
    extPrice =
      itemProperties
        .find((item) => item.dict_id === +k_id)
        .sub_properties.find((item) => item.dict_id === +id)?.spec_property
        ?.price || 0;
  } catch (error) {
    console.log('规格加价计算错误', error);
  }
  if (extPrice > 0) return `¥${extPrice / 100}`;

  return '';
};

export const getSkuText = (data, itemProperties = []) => {
  if (!data) return '';
  const parseDate = JSON.parse(data);
  return parseDate
    .map((i) => {
      return `${i.v}${getExtPrice(
        { k_id: i.k_id, id: i.v_id },
        itemProperties
      )}`;
    })
    .join(';');
};
