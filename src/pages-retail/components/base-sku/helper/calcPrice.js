// 规格加价
export const getExtraSkuPrice = (selectedSku, skuTree) => {
  let price = 0;
  for (let index = 0; ; index++) {
    const sId = selectedSku[`s${index + 1}`];
    if (!sId) {
      break;
    }
    price +=
      skuTree[index].sub_properties?.find((item) => item.dict_id === sId)
        ?.spec_property?.price || 0;
  }
  return price;
};

// 获取属性加价
export const getPropPrice = (propData, propIds) => {
  return propData
    .filter(({ id }) => propIds.includes(id))
    .reduce((prev, item) => {
      return prev + (item.price || 0);
    }, 0);
};
