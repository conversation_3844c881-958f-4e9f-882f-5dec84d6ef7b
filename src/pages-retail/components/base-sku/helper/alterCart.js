import pick from '@youzan/weapp-utils/lib/pick';
import { getPicture } from './img';
import { getSkuText } from './combo';

const findSelectedGoods = ({ goodsId, skuId, propertyIds, goods }) => {
  if (goodsId !== goods.id || skuId !== goods.skuId) {
    return false;
  }

  if (!!(propertyIds || []).length !== !!(goods.propertyIds || []).length) {
    return false;
  }

  if (!(propertyIds || []).length) {
    return true;
  }

  if (propertyIds.find((id) => !goods.propertyIds.includes(id))) {
    return false;
  }

  return true;
};

export const updateCart = ({ selectedComboList, group, goods }) => {
  const groupData = selectedComboList.find(({ id }) => group.id === id);

  const newGoodsInfo = {
    goodsId: goods.id,
    ...pick(goods, [
      'goodsName',
      'skuId',
      'skuName',
      'num',
      'price', // 规格加价
      'originPrice', // 原价
      'picture',
      'propertyIds',
      'snapshotkey',
      'skuDesc',
      'props',
    ]),
  };

  if (groupData) {
    const selectedGoods = groupData.subComboList.find(
      ({ goodsId, skuId, propertyIds }) =>
        findSelectedGoods({ goodsId, skuId, propertyIds, goods })
    );

    if (selectedGoods) {
      // goods.prevNum为0是添加数量，如：sku加购场景
      selectedGoods.num =
        goods.prevNum === 0 ? goods.num + selectedGoods.num : goods.num;
      return selectedComboList;
    }

    groupData.subComboList.push(newGoodsInfo);
    return selectedComboList;
  }

  selectedComboList.push({
    subComboList: [newGoodsInfo],
    rule: JSON.stringify(group.rule),
    id: group.id,
    name: group.name,
  });

  return selectedComboList;
};

export const deleteCart = ({ selectedComboList, group, goods }) => {
  const groupData = selectedComboList.find(({ id }) => group.id === id);

  const selectedGoodsIndex = groupData.subComboList.findIndex(
    ({ goodsId, skuId, propertyIds }) =>
      findSelectedGoods({ goodsId, skuId, propertyIds, goods })
  );

  groupData.subComboList.splice(selectedGoodsIndex, 1);

  if (!groupData.subComboList.length) {
    selectedComboList = selectedComboList.filter(({ id }) => group.id !== id);
  }

  return selectedComboList;
};

export const formatSpecificComboInfoParams = (selectedComboItems = []) => {
  const comboGroups = [];
  selectedComboItems.forEach((element) => {
    const combo = {
      id: element.goods_combo_group_id,
      name: element.name,
      comboSubSkus: [],
    };

    element.combo_sub_items.forEach((item) => {
      const common = {
        goodsId: item.item_id,
        goodsName: item.title,
        picture: getPicture(item.picture),
        snapshotKey: item.snap_key,
      };
      // 处理属性
      const props = [];
      (item.props || []).forEach((info) => {
        const textModels = info.text_models[0];
        const prop = {
          id: info.id,
          name: info.prop_name,
          valId: textModels.id, // 固定套餐属性是唯一的
          valName: textModels.text_name,
          price: textModels.price,
        };
        props.push(prop);
      });
      // 处理sku
      (item.sku_relateds || []).forEach((info) => {
        const comboSubSku = {
          skuId: info.sku_id,
          skuName: info.sku,
          num: info.combine_num || 1,
          originPrice: info.price,
          skuDesc: getSkuText(info.sku),
        };
        combo.comboSubSkus.push({
          ...common,
          ...comboSubSku,
          props,
        });
      });
    });

    comboGroups.push(combo);
  });
  return JSON.stringify({ comboGroups });
};
