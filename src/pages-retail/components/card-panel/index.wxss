:host {
  --title-color: #323233;
  --title-weight: 500;
  --title-size: 28rpx;

  --desc-color: #969799;
  --desc-weight: normal;
  --desc-size: 28rpx;

  --border-style: solid;
  --card-padding: 24rpx 32rpx 24rpx 24rpx;

  --header-direction: row;
  --header-align: center;
  --header-justify: center;
}

.card-panel {
  background: white;
  padding: var(--card-padding, 24rpx);
  border-radius: 8rpx;
  box-sizing: border-box;
}

.card-panel .icon {
  background-size: contain;
  width: 40rpx;
  height: 40rpx;
  background-repeat: no-repeat;
  border-radius: 50%;
}

.card-panel .header {
  display: flex;
  height: var(--card-height, initial);
  flex-direction: var(--header-direction, row);
  align-items: var(--header-align, center);
  justify-content: var(--header-justify, center);
}

.card-panel .title {
  display: flex;
  align-items: center;
  line-height: 1.25;
}
.card-panel .name {
  color: var(--title-color);
  font-weight: var(--title-weight);
  font-size: var(--title-size);
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.card-panel .desc {
  color: var(--desc-color);
  font-weight: var(--desc-weight);
  font-size: var(--desc-size);
  line-height: 1.25;
  flex-shrink: 0;
  margin: var(--desc-margin, initial);
  word-break: break-word;
}

.card-panel .body {
  border-top: 1rpx var(--border-style) #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
}
