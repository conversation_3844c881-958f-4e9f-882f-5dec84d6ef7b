:host {
    --title-container-height: 40px;
}

.radius-popup {
  border-radius: 8px 8px 0 0;
}

.title-container {
  position: relative;
  height: var(--title-container-height);
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
}

.title-container .close-btn {
  position: absolute;
  right: 5px;
  top: 0;
  width: var(--title-container-height);
  height: var(--title-container-height);
  display: flex;
  justify-content: center;
  align-items: center;
}

.title-container .close-btn:active {
  opacity: 0.6;
}
