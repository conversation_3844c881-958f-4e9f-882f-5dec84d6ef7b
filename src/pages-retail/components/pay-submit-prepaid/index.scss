:host {
  --button-color: #ffffff;
  --button-border-color: #1989fa;
  --button-bg-color: #1989fa;
  --container-height: 100rpx;
  --text-valign: center;
}

.pay-submit-container {
  box-shadow: 0 -2px 10px rgba(125, 126, 128, 0.16);
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}

.pay-button {
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 192rpx;
  height: 80rpx;
  border-radius: 10em;
  padding: 0 40rpx;
  margin-right: 0;
  overflow: visible !important;
  border: none !important;
  background-color: var(--theme-main-color, --button-border-color) !important;
}

.btn-reverse {
  background-color: white !important;
  color: var(--theme-main-color, --button-border-color) !important;
  border-color: var(--theme-main-color, --button-border-color) !important;
}

.btn-hairline {
  background-color: #fff !important;
  color: #332233 !important;
  border-color: #979797 !important;
}

.pay-submit {
  box-sizing: border-box;
  background: white;
  display: flex;
  align-items: center;
  padding: 8px 15px;
  height: var(--container-height);

  &__text {
    display: flex;
    align-items: var(--text-valign, center);

    &-extra {
      font-size: 20rpx;
      color: #959799;
    }
  }

  &__label {
    font-size: 28rpx;
    color: #323233;
    margin-right: 10rpx;

    &.notice {
      color: #c8c9cc;
    }
  }

  &__action {
    margin-left: auto;
    display: flex;
    align-items: center;
  }
}

.ways-index--cap-pay-item-cell {
  height: 50px;
}
