// import PayService from '@youzan/zan-pay-weapp';

const app = getApp();

const mapRetailSceneToCashierBiz = {
  /** 自助结账 */
  1: 'self_checkout',
  /** 扫码点单 */
  2: 'qr_order',
};

function getCashierBizByRetailScene(scene) {
  return mapRetailSceneToCashierBiz[scene] || 'unknown';
}

Component({
  properties: {
    /** 业务场景 */
    scene: Number,
    orderNo: String,
    themeColor: String,
  },
  data: {
    isPaying: false,
    onPaySuccess: null,
    onCashierClose: null,
  },
  attached() {},
  methods: {
    noop() {},
    /**
     * 在业务组件中动态修改收银台实例的配置
     *
     * @param {*} config 配置(onPaySuccess, onCashierFail, onCashierClose)
     */
    setConfig(config) {
      const { onPaySuccess, onCashierClose } = config;
      this.setData({
        onPaySuccess,
        onCashierClose,
      });
    },
    use(fn) {
      if (typeof fn !== 'function') {
        throw new Error('middleware must be a function');
      }
      this.middleware = this.middleware || [];
      this.middleware.push(fn);
      return this;
    },
    compose(middleware) {
      if (!Array.isArray(middleware)) {
        throw new Error('Middleware stack must be an array!');
      }
      return function (context, next) {
        let index = -1;
        function dispatch(i) {
          if (i <= index) {
            return Promise.reject(new Error('next() called multiple times'));
          }
          index = i;
          let fn = middleware[i];
          if (i === middleware.length) {
            fn = next;
          }
          if (!fn) return Promise.resolve();
          try {
            // 执行第 i 个中间件，并传入第 i + 1 个中间件
            return Promise.resolve(fn(context, () => dispatch(i + 1)));
          } catch (err) {
            return Promise.reject(err);
          }
        }
        return dispatch(0);
      };
    },
    async startPay(ctx) {
      const fn = this.compose([...this.middleware, this.pay.bind(this)]);
      await fn(ctx);
    },

    async pay(ctx, next) {
      if (!ctx.payInfo) {
        throw new TypeError(
          '支付所需的数据需要放入 ctx.payInfo，payInfo 内容参考文档 https://npm.qima-inc.com/package/@youzan/zan-pay-tee'
        );
      }
      /** 业务方需调用下单接口，后端返回支付所需的参数 */
      const {
        prepayId,
        partnerId,
        cashierSign,
        cashierSalt,
        orderNo /** 业务方的订单号，有时也叫做 outBizNo */,
        rechargeNo,
      } = ctx.payInfo;

      const payParams = {
        prepayId,
        partnerId,
        cashierSign,
        cashierSalt,
        orderNo:
          orderNo || rechargeNo /** 业务方的订单号，有时也叫做 outBizNo */,
        /** 是否展示有赞担保 */
        showGuarantee: false,
      };
      console.log('使用推荐充值版收银台');
      this.startPaying(payParams);
      await next();
    },

    startPaying(payParams) {
      if (this.payService) {
        this.payService.startPay({ ...payParams });
      } else {
        wx.showToast({
          title: '收银台初始化失败',
          icon: 'none',
          duration: '1000',
        });
      }
    },
    // 成功
    onPaidSuccess() {
      wx.vibrateShort({ success: () => wx.vibrateShort() });
      this.data.onPaySuccess && this.data.onPaySuccess();
      this.triggerEvent('paid-success');
    },
    // 关闭
    onClose() {
      this.data.onCashierClose && this.data.onCashierClose();
      this.triggerEvent('paid-close');
    },
    // 初始化
    async initPay({ detail }) {
      /** 传了 scene 才需要走收银台 */
      if (!this.data.scene) {
        return;
      }
      const { default: PayService } = await import('@youzan/zan-pay-weapp');
      this.payService = new PayService({
        toast: wx.showToast,
        clear: wx.hideToast,
        request: app.request,
        biz: getCashierBizByRetailScene(this.data.scene),
        quickMode: false,
      });
      const crypto = await import('@youzan/crypto');
      this.payService.init(detail, crypto);
    },

    onCashierNavi({ detail: destination }) {
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${destination.url}&title=${destination.title}`,
      });
    },
  },
});
