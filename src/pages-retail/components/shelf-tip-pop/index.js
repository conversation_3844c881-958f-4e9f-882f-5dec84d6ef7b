// 默认
const NO_SHOP = 0;
// 类似 仅支持蛋糕配送
const ONLY_GOODS = 1;

Component({
  properties: {
    show: Boolean,
    title: String,
    content: String,
    type: {
      type: Number,
      value: NO_SHOP
    }
  },
  data: {
    no_shop: NO_SHOP,
    only_goods: ONLY_GOODS
  },
  methods: {
    closePopup() {
      this.triggerEvent('closePopup');
    },
    toOrder() {
      this.triggerEvent('toOrder');
    },
    toChangeAddress() {
      this.triggerEvent('toChangeAddress');
    }
  }
});
