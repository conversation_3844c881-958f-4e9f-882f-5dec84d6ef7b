:host {
  --theme-main-color: red;
}


$title-container-height: 40px;
$panel-bottom: 50px;



.radius-popup {
  border-radius: 10px 10px 0 0;
}

.main-container {
  height: 194px;

  .title-container {
    position: relative;
    height: $title-container-height;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    font-size: 34rpx;

    .close-btn {
      position: absolute;
      right: 5px;
      top: 0;
      width: $title-container-height;
      height: $title-container-height;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .close-btn:active {
      opacity: 0.6;
    }
  }
  .content {
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;;
    padding: 10px;
    box-sizing: border-box;
    text-align: center;
  }

  .panel-bottom {
    height: $panel-bottom;
    box-sizing: border-box;
    padding: 5px 10px;
    display: flex;
    background: #fff;
    .confirm-btn {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid var(--theme-main-color);
      border-radius: $panel-bottom / 2;
      color: var(--theme-main-color);
    }
    .confirm-btn:active {
      opacity: 0.6;
    }

    .confirm-btn:nth-child(2) {
      margin-left: 10px;
    }
  }
}
