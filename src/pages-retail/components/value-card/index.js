import { joinMember } from 'retail/util/bind-mobile';

const app = getApp();

Component({
  properties: {
    data: Object,
    memberInfo: Object
  },

  observers: {
    memberInfo(data) {
      if (!data) return;
      if (!data.asset_info || !data.customer_info) return;

      const { asset_info = {}, customer_info = {}, user_info = {} } = data;

      const balance = asset_info.stored_balance_value;

      let balanceText = '';

      if (balance === null || balance === undefined) {
        balanceText = '更新中';
      } else {
        balanceText = (balance / 100).toFixed(2);
      }

      this.setData({
        balance: balanceText,
        isMember: customer_info.is_member,
        hasMobile: user_info.mobile_flag || false,
        mobile: customer_info.mobile
      });
    }
  },

  data: {
    balance: '',
    hasMobile: false
  },

  methods: {
    becomeMember() {
      joinMember(
        () => {
          app.logger.log({
            et: 'custom', // 事件类型
            ei: 'become_member_via_value_card', // 事件标识
            en: '通过储值卡组件成为会员', // 事件名称
            pt: 'retailshelfhome', // 页面类型
          });
        },
        {
          kdt_id: app.getKdtId(),
          member_src_way: 800,
          member_src_channel: 1000,
          need_be_member: true
        });
    },
    handleRecharge(e) {
      app.logger.log({
        et: 'click', // 事件类型
        ei: 'go_prepaid', // 事件标识
        en: '点击充值', // 事件名称
        pt: 'retailshelfhome', // 页面类型
      });

      wx.navigateTo({
        url: '/packages/pre-card/home/<USER>'
      });
    }
  }
});
