<view class="value-card">
  <view class="value-card__text">
    <view class="value-card__title">
      余额：{{ balance }}
    </view>
    <view class="value-card__desc">{{ data.text || '充值立享优惠' }}</view>
  </view>
  <button
    class="value-card__btn"
    wx:if="{{ isMember || hasMobile }}"
    catch:tap="handleRecharge"
  >
    马上充值
  </button>
  <view wx:else class="value-card__btn">
    <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="becomeMember">
      立即绑定
    </user-authorize>
  </view>
  <!-- <button
    wx:else
    class="value-card__btn"
    open-type="getPhoneNumber"
    bindgetphonenumber="handleRecharge"
  >
    立即绑定 -->
  </button>
</view>
