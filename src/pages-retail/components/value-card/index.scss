.value-card {
  display: flex;
  margin: 32rpx;
  border-radius: 8rpx;
  background-color: #fff;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
  height: 176rpx;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 20rpx hsla(0,0%,49%,.16);

  &__title {
    font-size: 36rpx;
    font-weight: bold;
    color: #323233;
  }

  &__desc {
    font-size: 28rpx;
    color: #EE0A24;
    margin-top: 10rpx;
  }

  &__btn {
    color: #EE0A24;
    border-radius: 28rpx;
    border: 1px solid #EE0A24;
    height: 56rpx;
    line-height: 56rpx;
    font-size: 28rpx;
    background-color: #fff;
    margin: 0;
  }
}
