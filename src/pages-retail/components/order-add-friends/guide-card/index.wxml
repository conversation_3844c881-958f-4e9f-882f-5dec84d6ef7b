<view wx:if="{{ info.locationStyle === 1 }}" style="{{ bgStyle }} {{ styles }}" class="c-oaf-guide-card">
  <view style="flex: 1; padding: 8rpx 0">
    <block wx:if="{{ !info.backgroundImageUrl }}">
      <view class="c-oaf-guide-card__title">
        {{ info.pageTitle }}
        <image src="https://img01.yzcdn.cn/retail/ci_start/config/2023/11/21/fastigiate-star-icon.svg"
          class="c-oaf-guide-card__icon" />
      </view>
      <view class="c-oaf-guide-card__guide-txt">{{ info.bootstrapText }}</view>
      <view style="background: {{ mainColor }}" class="c-oaf-guide-card__button">
        长按右侧二维码 👉
      </view>
    </block>
  </view>
  <qrcode 
    src="{{ info.liveQrcodeUrl }}"
    info="{{ info }}"
    custom-class="c-oaf-guide-card__qrcode"
    img-class="c-oaf-guide-card__image"
    loading-class="c-oaf-guide-card__image-loading"
    error-class="c-oaf-guide-card__image-error"
  />
</view>
<!-- 中间\底部弹窗样式 -->
<guide-popup 
  wx:elif="{{ info.locationStyle === 2 || info.locationStyle === 3 }}"
  mainColor="{{ mainColor }}"
  info="{{ info }}"
  position="{{ info.locationStyle === 2 ? 'center' : 'bottom' }}"
  contentStyle="{{ bgStyle }}"
/>