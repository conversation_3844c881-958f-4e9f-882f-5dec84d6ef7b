import WscComponent from 'pages/common/wsc-component/index';
import { queryQrCode } from 'retail/util/api/order-add-friends';

WscComponent({
  properties: {
    mainColor: String,
    locationType: Number,
    tableCode: String,
    tableId: String,
    styles: String,
  },
  data: {
    info: {},
    bgStyle: '',
  },
  attached() {
    queryQrCode({
      channelType: 1, // 适用渠道：1：扫码点单
      locationType: this.data.locationType, // 显示位置：1.点单前 2.预结单页 3.支付结果页
      tableId: this.data.tableId,
    }).then(
      (res = {}) => {
        this.setData({
          info: {
            ...res,
            _tableCode: this.data.tableCode,
            _tableId: this.data.tableId,
          },
          bgStyle: res.backgroundImageUrl
            ? `background-image: url('${res.backgroundImageUrl}');`
            : '',
        });
      },
      () => !1
    );
  },
  methods: {
    handleClose() {
      //
    },
  },
});
