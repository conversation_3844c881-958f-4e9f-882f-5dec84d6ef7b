import WscComponent from 'pages/common/wsc-component/index';
import { getThemeMainRgbColorWithAlpha } from 'retail/util/shelf-global';

WscComponent({
  properties: {
    mainColor: String,
    info: {
      type: Object,
      value: {},
      observer(val) {
        this.setData({ isDefault: !val.backgroundImageUrl });
      },
    },
    position: String,
    contentStyle: String,
  },
  data: {
    show: true,
    bgStyle: `background-color: ${getThemeMainRgbColorWithAlpha(0.05)}`,
  },
  methods: {
    // 空事件组织穿透
    handleStopBubble() {},
    handleClose() {
      this.setData({ show: false });
    },
  },
});
