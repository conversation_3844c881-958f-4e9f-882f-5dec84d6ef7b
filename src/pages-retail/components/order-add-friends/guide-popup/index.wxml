<van-popup 
  show="{{ show }}" 
  position="{{ position }}"
  round
  lock-scroll
  closeable="{{ position === 'bottom' }}"
  safe-area-inset-bottom="{{ false }}"
  bind:close="handleClose"
  custom-class="c-oaf-guide-pop is-{{ position }}"
>
  <view style="{{ contentStyle }}" class="c-oaf-guide-pop__content-wrapper"  catchtap="handleStopBubble">
    <view style="{{ bgStyle }}" class="c-oaf-guide-pop__content">
      <block wx:if="{{ isDefault }}">
        <view class="c-oaf-guide-pop__title">
          {{ info.pageTitle }}
          <view class="c-oaf-guide-pop__star-icon">
            <image src="https://img01.yzcdn.cn/retail/ci_start/config/2023/11/21/star-icon.svg" />
          </view>
        </view>
        <view class="c-oaf-guide-pop__guide-txt">{{ info.bootstrapText }}</view>
      </block>
      <qrcode class="c-oaf-guide-pop__qrcode {{ isDefault ? 'is-default' : ''}}" src="{{ info.liveQrcodeUrl }}"
        info="{{ info }}" />
      <view wx:if="{{ isDefault }}" style="background: {{ mainColor }}" class="c-oaf-guide-pop__button">
        👆长按二维码识别
      </view>
    </view>
    <view 
      wx:if="{{ info.locationStyle === 2 }}" 
      class="c-oaf-guide-pop__close" 
      bind:tap="handleClose"
    >
      <van-icon name="close" />
    </view>
  </view>
</van-popup>