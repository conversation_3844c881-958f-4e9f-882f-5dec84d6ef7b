.c-oaf-guide-pop {
  &.is-center {
    width: 604rpx;
    height: 884rpx;
    background-color: transparent !important;
    .c-oaf-guide-pop__content-wrapper {
      border-radius: 16px;
    }
    .c-oaf-guide-pop__content {
      width: 604rpx;
      height: 768rpx;
      padding: 80rpx 0;
    }
    .c-oaf-guide-pop__close {
      bottom: -24px;
      left: 0;
      right: 0;
      font-size: 34px;
      color: white;
      transform: translateY(100%);
    }
  }
  &.is-bottom {
    .c-oaf-guide-pop__content {
      height: 800rpx;
      padding: 72rpx 0 112rpx;
    }
  }
  &__content-wrapper {
    position: relative;
    background-color: #fff;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }
  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
  }
  &__title {
    position: relative;
    color: #111;
    text-align: center;
    font-size: 40rpx;
    font-weight: 500;
    letter-spacing: -1rpx;
    margin-bottom: 16rpx;
    min-width: 336rpx;
  }
  &__guide-txt {
    color: #999;
    text-align: center;
    font-size: 28rpx;
    height: 32rpx;
  }
  &__qrcode {
    margin: 160rpx 0 40rpx 0;
    &.is-default {
      margin-top: 48rpx;
    }
  }
  &__button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 268rpx;
    height: 80rpx;
    border-radius: 200rpx;
    color: #fff;
    font-size: 24rpx;
    letter-spacing: 1rpx;
  }
  &__star-icon {
    position: absolute;
    top: 0rpx;
    right: 0rpx;
    z-index: 1;
    transform: translate3d(100%, -40%, 0);
    image {
      width: 52rpx;
      height: 50rpx;
    }
  }
  &__close {
    position: absolute;
    z-index: 1;
    display: flex;
    justify-content: center;
  }
}