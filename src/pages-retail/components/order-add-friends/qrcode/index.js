import WscComponent from 'pages/common/wsc-component/index';
import { report } from 'retail/util/api/order-add-friends';

WscComponent({
  externalClasses: [
    'custom-class',
    'img-class',
    'loading-class',
    'error-class',
  ],
  properties: {
    src: String,
    info: {
      type: Object,
      value: {},
    },
    loadingSize: {
      type: Number,
      value: 28,
    },
  },
  methods: {
    handleLongPress() {
      const { activityId, baseId, channelType, _tableCode, _tableId } =
        this.data.info;
      report({
        activityId,
        baseId,
        tableId: _tableId,
        tableCode: _tableCode,
        channelType,
      });
    },
  },
});
