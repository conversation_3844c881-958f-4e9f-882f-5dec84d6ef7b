.c-oaf-qrcode-wrapper {
  transform: rotateZ(360deg);
  &.is-border {
    width: 336rpx;
    height: 336rpx;
    background-color: #fff;
    border: 1rpx solid #E0E0E0;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .c-oaf-qrcode {
      width: 304rpx;
      height: 304rpx;
    }
    .c-oaf-qrcode__loading {
      width: 304rpx;
      height: 304rpx;
    }
    .c-oaf-qrcode__error {
      width: 304rpx;
      height: 304rpx;
      image {
        width: 104px;
        height: 104px;
      }
    }
  }
}
.c-oaf-qrcode {
  display: block;
  width: 336rpx;
  height: 336rpx;
  &__loading {
    bottom: 0;
    right: 0;
    background-color: #eee;
    height: 336rpx;
    width: 336rpx;
  }
  &__error {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #666;
    font-size: 28rpx;
    font-weight: 400;
    height: 336rpx;
    width: 336rpx;
    image {
      width: 136px;
      height: 136px;
    }
  }
}