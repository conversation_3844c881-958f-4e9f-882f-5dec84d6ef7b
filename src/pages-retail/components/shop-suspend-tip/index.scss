:host {
  --container-padding: 30rpx;
  --suspend-img-width: 180rpx;
}

.safe-bottom-padding {
    bottom: 0;
    left: 0;
    height: 34px;
    width: 100%;
    background: #fff;
}

.shop-suspend-container {
  position: absolute;
  z-index: 99;
  bottom: 0;
  left: 0;
  height: 200rpx;
  width: 750rpx;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  box-sizing: border-box;
  border-top-right-radius: 20rpx;
  border-top-left-radius: 20rpx;
  box-shadow:  0 -1px 1px 0 #d8d8d8;

  .shop-suspend-message {
    align-self: flex-start;
    width: calc(100% - var(--container-padding) * 2 - var(--suspend-img-width));
    &_title {
      font-size: 32rpx;
      font-weight: bold;
    }

    &_time {
      display: flex;
      font-size: 24rpx;
      height: 60rpx;
      line-height: 60rpx;
      color: #999;
      &_icon {
        margin-right: 10rpx;
        position: relative;
        top: 6rpx;
      }
    }
  }

  .shop-suspend-image {
    background: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/suspend.png);
    background-size: 168rpx 140rpx;
    background-repeat: no-repeat;
    width: var(--suspend-img-width);
    height: var(--suspend-img-width);
  }
}
