<view class="shop-sign">
  <image src="{{bannerImage}}" mode="{{ autoHeight ? 'widthFix' : 'scaleToFill' }}" class="{{'shop-sign__img ' + (autoHeight ? 'auto' : '')}}" />
  <view class="shop-sign__main">
    <view class="{{'shop-sign__mark ' + layout}}">
      <image class="shop-sign__mark-icon" mode="aspectFill" src="{{logo}}" />
    </view>
    <view class="{{'shop-sign__title ' + layout}}">{{shopName}}</view>
    <view wx:if="{{showNotice && notice}}" class="{{'shop-sign__announcement ' + layout}}">公告：{{notice}}</view>
    <slot></slot>
  </view>
</view>
