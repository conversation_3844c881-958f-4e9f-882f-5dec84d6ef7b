Component({
  properties: {
    layout: {
      type: String,
      value: 'right'
    },
    shopName: String,
    logo: String,
    notice: String,
    showNotice: Boolean,
    autoHeight: {
      type: Boolean,
      value: false
    },
    bannerImage: String,
    shopMessage: {
      type: Object,
      value: {
        title: 'ZAN CAFE',
        notice: '这里写公告公告',
        autoHeight: false,
        backgroundImage: '',
        icon: ''
      }
    }
  },
  data: {}
});
