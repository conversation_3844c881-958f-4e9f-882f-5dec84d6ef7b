:host {
  --price-font-size: 36rpx;
  --price-color: #f40f0f;
  --price-weight: 500;
  --price-height: 48rpx;
  --price-tag-color: #bbb;
  --price-family: avenir, -apple-system, blinkmacsystemfont, helvetica neue,
    helvetica, roboto, arial, pingfang sc, hiragino sans gb, microsoft yahei,
    simsun, sans-serif;
  --origin-price-font-size: 28rpx;
  --prefix-margin: 0;
  --price-bottom: -1px;
}

.price-group {
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
}

.price-group__front {
  color: var(--price-color);
  font-size: 24rpx;
  font-weight: var(--price-weight);
  font-family: var(--price-family);
  position: relative;
  bottom: var(--price-bottom, -1px);
  display: flex;
}

.price-group__front--prefix {
  font-size: 24rpx;
  margin-right: var(--prefix-margin);
}

.price-group__front--price {
  font-size: var(--price-font-size, 36rpx);
  font-weight: var(--price-weight);
  line-height: var(--price-height);
}

.price-group__front--suffix {
  font-size: 24rpx;
  font-weight: var(--price-weight);
}

.price-group__tag {
  font-size: 24rpx;
  margin-left: 5rpx;
  color: var(--price-tag-color);
}

.origin-price {
  color: #999;
  font-size: var(--origin-price-font-size);
  text-decoration: line-through;
  padding-left: 16rpx;
}

.origin-price.right {
  width: 100%;
  text-align: right;
}

.mr2px {
  margin-right: 2px;
}

.second-line-mode {
  justify-content: flex-end;
}
