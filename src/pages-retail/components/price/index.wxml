<view class="price-group {{ secondLineMode ?'second-line-mode' : '' }}">
  <slot name="pretext"></slot>
  <view class="price-group__front">
    <!-- <text class="price-group__front--prefix">{{isNegative ? '– ' : ''}}¥</text>
    <text class="price-group__front--price">{{tools.getYuan(price)}}</text>
    <text class="price-group__front--suffix">.{{tools.getJiao(price)}}</text> -->
    <text class="price-group__front--price mr2px">{{isNegative ? '– ' : ''}}¥ </text>
    <text class="price-group__front--price">{{tools.getYuan(price)}}</text>
    <text wx:if="{{!tools.isPricePerfect(price)}}" class="price-group__front--price">{{offZeroJiao}}</text>
  </view>
  <text wx:if="{{isSpu}}" class="price-group__tag">
    起
  </text>
  <text class="origin-price" wx:if="{{!secondLineMode && originPrice !== price && originPrice}}">
    ¥{{util.toPrice(originPrice)}}
  </text>
</view>
<block wx:if="{{secondLineMode}}">
  <view class="origin-price right" wx:if="{{originPrice !== price && originPrice}}">
    ¥{{util.toPrice(originPrice)}}
  </view>
</block>
<wxs src="./splitPrice.wxs" module="tools" />
<wxs module="util">
  function toPrice(price) {
    price /= 100;
    // 不支持 (price / 100).toFixed
    return +price.toFixed(2);
  }
  module.exports = { toPrice: toPrice };
</wxs>