Component({
  properties: {
    isSpu: Boolean,
    isNegative: <PERSON><PERSON><PERSON>,
    secondLineMode: {
      type: Boolean,
      value: false,
    },
    price: {
      type: Number,
      value: 0,
    },
    originPrice: {
      type: Number,
      value: 0,
    },
  },
  options: {
    multipleSlots: true,
  },
  data: {
    offZeroJiao: '',
  },
  observers: {
    price(val) {
      this.setData({
        offZeroJiao: this.getJiaoStr(val),
      });
    },
  },
  methods: {
    padStart(src, targetLength, pad) {
      if (src.length >= targetLength) {
        return src;
      }
      const diff = targetLength - src.length;
      let padStr = '';
      for (let i = 0; i < diff; i += 1) {
        padStr += pad;
      }

      return padStr + src;
    },
    getJiao(price) {
      return this.padStart('' + (price % 100), 2, 0);
    },
    deleteSuffixZero(str) {
      // 挪到这边是因为 wxs 不支持这样的正则写法
      return str.replaceAll(/0+$/g, '');
    },
    getJiaoStr(price) {
      const value = this.getJiao(price);

      const deleteZeroValue = this.deleteSuffixZero(value);
      if (deleteZeroValue === '') return '';
      return '.' + deleteZeroValue;
    },
  },
});
