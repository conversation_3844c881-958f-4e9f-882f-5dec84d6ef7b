/* eslint-disable @youzan/koko/no-async-await */
import Dialog from '@vant/weapp/dist/dialog/dialog';
import { openTable, getOrderPageConfigs } from 'retail/util/api';
import {
  gotoGoodsList,
  withPreOrderToDo,
  markStart,
  goToOnlineHomePage,
} from 'retail/util/dashboard-shared';

const app = getApp();

Component({
  properties: {
    themeMainRgbColor: String,
    themeMainColor: String,
    dinerNumInfo: {
      type: Object,
      value: {},
    },
    tableCode: String,
    shopName: String,
    query: {
      type: Object,
      value: {},
    },
  },

  observers: {
    dinerNumInfo(config) {
      if (config.showChooseDinerNums) {
        this.orderPromise = getOrderPageConfigs();
      }
    },
  },
  methods: {
    // 选择人数
    async handleSelectDinerNum(env) {
      const val = env.detail;
      const { query } = this.data;
      // 先吃后付模式,一定是多人的;会直接开桌,就餐人数缓存在后端
      if (app.globalData.retailConfig.isSupportAddMealOpen) {
        openTable({
          tableId: query.t,
          tableName: this.data.tableCode,
          dinerNum: val,
        })
          .then(() => {
            markStart('scan_order_diner_jump');
            gotoGoodsList(query, 'navigateTo', {
              orderPrefetchPromise: this.orderPromise,
            });
          })
          .catch(async (err) => {
            const { code, msg } = err;
            if (code === 22402030) {
              Dialog.alert({
                message: msg,
                confirmButtonText: '我知道了',
                confirmButtonColor: this.themeMainColor,
              });
            } else {
              if (
                !(await withPreOrderToDo(query, {
                  dinerNum: true,
                  orderPrefetchPromise: this.orderPromise,
                }))
              ) {
                wx.showToast({
                  title: '桌台状态异常，请联系店员',
                  icon: 'none',
                });
              }
            }

            app.logger.skynetLog({
              appName: 'retail-node-h5',
              message: '扫码点单开桌失败',
              level: 'error',
              detail: `${err}`,
            });
          });
      } else {
        // 先付后吃模,缓存下就餐人数,加购是需要用到
        app.globalData.needPayLaterEatDinerNum = val;
        gotoGoodsList(query, 'navigateTo', {
          orderPrefetchPromise: this.orderPromise,
        });
      }
    },
    handleOnlineHome() {
      goToOnlineHomePage();
    },
  },
});
