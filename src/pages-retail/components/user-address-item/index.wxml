<view class="user-address-container">
  <view
    wx:if="{{selected}}"
    class="user-address-selected"
  >
    <van-radio-group value="{{ selected ? '1' : '2' }}">
      <van-radio
        name="1"
        checked-color="{{themeMainColor}}"
      ></van-radio>
    </van-radio-group>
  </view>
  <view
    wx:else
    class="user-address-noselected"
    bind:tap="clickSelectAddress"
  >
    <van-radio-group value="{{ selected ? '1' : '2' }}">
      <van-radio name="1"></van-radio>
    </van-radio-group>
  </view>
  <view
    class="user-address-info"
    bind:tap="clickSelectAddress"
  >
    <view class="user-address-info__first_line">
      <view class="user-address-info-name">{{userName}}</view>
      <view class="user-address-info-phone">{{userPhone}}</view>
      <view
        class="user-address-info-tag"
        wx:if="{{isDefaultAddress}}"
      >默认</view>
    </view>
    <view class="user-address-info__address">{{address}}</view>
  </view>
  <view
    class="user-address-edit"
    bind:tap="clickEditAddress"
  />
</view>
