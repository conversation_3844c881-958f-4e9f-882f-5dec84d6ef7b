Component({
  properties: {
    themeMainColor: String,
    selected: {
      type: Boolean,
      value: false
    },
    addressId: String,
    userName: String,
    userPhone: String,
    isDefaultAddress: {
      type: Boolean,
      value: false
    },
    address: String
  },
  methods: {
    clickEditAddress() {
      const { addressId } = this.data;
      this.triggerEvent('clickEditAddress', { addressId });
    },
    clickSelectAddress() {
      const { addressId } = this.data;
      this.triggerEvent('clickSelectAddress', { addressId });
    }
  }
});
