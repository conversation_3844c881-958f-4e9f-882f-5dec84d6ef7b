$user-address-container-height: 60px;
$user-address-container-icon-width: 45px;

.icon-public {
  flex: 0 0 $user-address-container-icon-width;
  min-height: $user-address-container-height;
  display: flex;
  justify-content: center;
  align-items: center;
  background-position: center;
  background-size: 16px 16px;
  background-repeat: no-repeat;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-address-container {
  min-height: $user-address-container-height;
  width: 100%;
  display: flex;
  justify-content: space-between;
  background: #fff;
  box-sizing: border-box;
  border-radius: 4px;

  .user-address-selected {
    @extend .icon-public;
    // background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/selected-icon.svg);
  }

  .user-address-noselected {
    @extend .icon-public;
  }

  .user-address-edit {
    @extend .icon-public;
    width: $user-address-container-icon-width;
    background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/38_edit.svg);
  }

  .user-address-edit:active {
    opacity: 0.3;
  }

  .user-address-info:active {
    opacity: 0.6;
  }

  .user-address-info {
    flex: 1;

    &__first_line {
      height: 30px;
      display: flex;
      color: #323233;
      flex-direction: row;
      align-items: flex-end;

      .user-address-info-name {
        max-width: 70px;
        @extend .text-ellipsis;
      }

      .user-address-info-phone {
        margin: 0 5px;
        max-width: 110px;
        @extend .text-ellipsis;
      }

      .user-address-info-tag {
        background: #ee0a24;
        color: #fff;
        border-radius: 8px;
        font-size: 12px;
        padding: 1px 4px;
      }
    }

    &__address {
      display: flex;
      align-items: left;
      justify-content: space-around;
      flex-direction: column;
      font-size: 12px;
      padding: 8px 0 12px;
      color: #969799;
    }
  }
}
