const app = getApp();

Component({
  options: {
    multipleSlots: true,
  },
  properties: {
    noSwitch: Boolean,
    shopInfo: Object,
    showProgressBar: {
      type: Boolean,
      value: false,
    },
    showLocation: {
      type: Boolean,
      value: true,
    },
    tableCode: {
      type: String,
      value: '',
    },
    couponList: {
      type: Array,
      value: [],
    },
    myCouponList: {
      type: Array,
      value: [],
    },
    needPhoneAuth: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    authorizationMobile: false,
  },

  // 检测组件所在的 page 的生命周期，来更新店铺信息
  pageLifetimes: {
    show() {
      const { mobile } = app.getUserInfoSync();
      this.setData({
        authorizationMobile: !!mobile,
      });
    },
  },

  methods: {
    onMyCouponList() {
      this.triggerEvent('myCouponList');
    },
    onBindMember() {
      this.setData({
        authorizationMobile: true,
      });
    },
    navigateToShoplist() {
      if (this.data.noSwitch || this.data.shopInfo.isSingle) return;
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateTo({
        url: '/packages/retail/shop-list/index',
      });
    },
  },
});
