<view class="shop-header">
	<view class="main">
		<view wx:if="{{tableCode}}" class="left-view">
			<van-icon name="https://img01.yzcdn.cn/upload_files/2023/02/27/Fq61km5PO2WUTyHb3oIYq10RQ56-.png" class="food" size="24px" />
			<view class="title">{{tableCode}}桌</view>
		</view>
		<view wx:else class="left-view" bind:tap="navigateToShoplist">
			<view class="title">{{ shopInfo.shopName }}</view>
			<van-icon name="arrow" color="#323233" class="link" />
		</view>
		<view class="right-view">
			<view class="coupon-button">
				<van-icon name="coupon" size="18px" class="coupon-icon" />
				<view
					wx:if="{{ needPhoneAuth && !authorizationMobile }}"
					style="color: #333;background-color: #f7f8fa;"
					class="coupon-login">
          <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="onBindMember">
            优惠券
          </user-authorize>
        </view>
				<!-- <van-button
				 wx:if="{{ needPhoneAuth && !authorizationMobile }}"
				 color="#f7f8fa"
				 size="mini"
				 custom-style="color: #333;"
				 custom-class="coupon-login"
				 open-type="getPhoneNumber"
				 bind:getphonenumber="onBindMember"
				>优惠券
				</van-button> -->
				<block wx:else>
					<view class="coupon-num" bind:tap="onMyCouponList">{{ myCouponList.length }}</view>
					<view class="coupon" bind:tap="onMyCouponList">张券</view>
				</block>
			</view>
		</view>
	</view>
	<view class="secondary">
		<progress-banner id="progress-banner" wx:if="{{ showProgressBar }}" banner-container="banner-container" />
		<slot name="ump-brief" />
	</view>
</view>

