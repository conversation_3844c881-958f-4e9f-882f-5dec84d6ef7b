// 导航栏上胶囊按钮的高度
const CAPSULE_HEIGHT = 32;
// 安卓上，胶囊按钮距离导航栏顶部的距离
const ANDROID_MARGIN = 8;
// iOS上，胶囊按钮距离导航栏顶部的距离
const IOS_MARGIN = 6;

Component({
  properties: {
    hideBack: Boolean,
    statusBarStyle: String,
    navBarStyle: String,
  },
  data: {},
  options: {
    multipleSlots: true
  },
  lifetimes: {
    ready() {
      const { statusBarHeight, system } = wx.getSystemInfoSync();
      let navHeight = CAPSULE_HEIGHT;
      if (~system.indexOf('iOS')) {
        navHeight += IOS_MARGIN * 2;
      } else {
        navHeight += ANDROID_MARGIN * 2;
      }
      this.setData({
        statusBarHeight,
        navHeight
      });
    }
  },
  methods: {
    clickBack() {
      this.triggerEvent('clickBack');
    },
    clickCustom() {
      this.triggerEvent('clickCustom');
    }
  }
});
