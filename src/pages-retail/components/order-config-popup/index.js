const app = getApp();

Component({
  properties: {
    show: Boolean,
    takeConfig: Object,
    takeStyle: Object,
    themeMainColor: String
  },

  observers: {
    takeStyle(value) {
      // 在前面的基础上，一旦有一个为false，则认定只设置了一种就餐方式
      const {
        defaultOption,
        diningIn,
        diningInAlias,
        takingOut,
        takingOutAlias
      } = value;
      this.setData({
        defaultOption,
        diningIn,
        diningInAlias,
        takingOut,
        takingOutAlias,
        isOnlyShowOneTakeStyle: diningIn ^ takingOut
      });
    }
  },

  data: {
    radio: '1',
    takeTimeText: '',
    showAppointment: false,
    isOnlyShowOneTakeStyle: false,
    defaultOption: 0,
    diningIn: false,
    diningInAlias: '',
    takingOut: false,
    takingOutAlias: '',
    icons: {
      selected: 'https://b.yzcdn.cn/wsc-minapp/icon/retail/selected.svg',
      unselected: 'https://b.yzcdn.cn/wsc-minapp/icon/retail/no-selected.svg'
    }
  },

  methods: {
    setAppointmentConfig({ detail }) {
      if (!detail) {
        return this.setData({
          showAppointment: false,
          takeTimeText: ''
        });
      }

      this.appointmentConfig = detail;
      const { date, time } = detail;

      this.setData({
        showAppointment: false,
        takeTimeText: `${date.value}${date.comments} ${time}`
      });
    },

    toggleYuyuePicker() {
      this.setData({
        showAppointment: !this.data.showAppointment
      });
    },

    onClose() {
      if (this.data.showAppointment) return;
      this.triggerEvent('close');
    },

    go({ detail, currentTarget }) {
      if (this.isRedirecting) return;

      // this.setData({ isRedirecting: true });

      this.isRedirecting = true;

      const handler = () => {
        wx.vibrateShort();

        const currentWay = +currentTarget.dataset.way;

        app.logger &&
          app.logger.log({
            et: 'click',
            ei: 'type_choose',
            en: '选择就餐方式',
            pt: 'home',
            params: {
              type: currentWay
            }
          });

        this.triggerEvent('submit', {
          deliveryWay: currentWay,
          appointmentConfig: this.appointmentConfig
        });

        // 这个过程很快，所以用一个 setTimeout 来过渡下禁用的操作
        setTimeout(() => {
          this.isRedirecting = false;
          // this.setData({ isRedirecting: false });
        }, 1000);

        this.appointmentConfig = null;
      };
      handler();
      // app.getUserInfo(
      //   () => {
      //   },
      //   () => {
      //     handler();
      //   }
      // );
    },

    onChange({ detail }) {
      wx.vibrateShort();

      if (+detail === 3) {
        this.toggleYuyuePicker();
      }

      this.setData({
        radio: detail
      });
    }
  }
});
