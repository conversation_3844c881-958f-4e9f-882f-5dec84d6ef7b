<popup
  show="{{show}}"
  overlay
  custom-style="{{showAppointment ? 'transform: translateX(-150%);' : ''}}"
  bind:close="onClose"
  position="bottom"
  custom-class="radius-popup"
>
  <view class="header-block">
    <view
      class="header-block-close"
      hover-class="header-block-close__hover"
      hover-hover-stay-time="400"
      bind:tap="onClose"
    >
      <van-icon name="cross" color="#cfcfd2" size="20px"/>
    </view>
  </view>
  <slot></slot>
  <view style="margin: 40rpx 0; padding: 0 32rpx;">
    <van-radio-group
      value="{{radio}}"
      wx:if="{{takeConfig}}"
      bind:change="onChange"
    >
      <van-radio
        label-class="{{radio == 1 ? 'takeway-radio--selected' : 'takeway-radio'}}"
        name="1"
        use-icon-slot
      >
        现在点单，支付成功后店内取单
        <view
          class="{{radio == 1 ? 'radio-selected' : 'radio-unselected'}}"
          slot="icon"
        />
      </van-radio>
      <van-radio
        wx:if="{{takeConfig}}"
        name="3"
        custom-class="just-wantmargin-but-you-need-to-write-class"
        use-icon-slot
        label-class="{{radio == 3 ? 'takeway-radio--selected' : 'takeway-radio'}}"
      >
        <view>
          <view style="display: flex; align-items: center;">
            预约点单，支付成功后到店取单
            <van-icon color="#969799" name="arrow" style="margin-left: auto; height: 16px; line-height: 1;" />
          </view>
          <text wx:if="{{radio == 3}}" class="takeway-text">{{takeTimeText}}</text>
        </view>
        <view
          class="{{radio == 3 ? 'radio-selected' : 'radio-unselected'}}"
          slot="icon"
        />
      </van-radio>
    </van-radio-group>

    <view style="margin-top: 60rpx;" wx:if="{{diningIn || takingOut}}">
      <view style="color: var(--unselected-text-color); font-size: 28rpx;">
        选择就餐方式
      </view>
        <view class="button-group">
          <user-authorize class="button-content" wx:if="{{diningIn}}" authTypeList="{{ ['nicknameAndAvatar'] }}" data-way="{{3}}" bind:next="go" bind:fail="go">
            <button
              class="take-button {{isOnlyShowOneTakeStyle ? 'take-button__active' : ''}}"
              hover-class="focus"
              hover-stay-time="1000"
              hover-stop-propagation
            >
              {{diningInAlias}}
            </button>
          </user-authorize>
          <user-authorize class="button-content" wx:if="{{takingOut}}" authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="go" data-way="{{1}}" bind:fail="go">
            <button
              class="take-button {{isOnlyShowOneTakeStyle ? 'take-button__active' : ''}}"
              hover-class="focus"
              hover-stay-time="1000"
            >
              {{takingOutAlias}}
            </button>
          </user-authorize>
        </view>
    </view>
  </view>
</popup>

<yuyue-picker
  wx:if="{{takeConfig}}"
  overlay="{{false}}"
  style="--theme-main-color: {{themeMainColor}}"
  custom-style="transform: translateX({{showAppointment ? '0' : '100%'}});"
  show-popup
  show-arrow
  take-config="{{takeConfig}}"
  bind:close="closePicker"
  bind:submit="changeTime"
  bind:back="setAppointmentConfig"
/>
