/* 因为找一种方式将所有常用的 css 变量给挂载，这样就能直接用了 */
:host {
  --theme-main-color: #1989fa;
  --secondary-text: #969799;
  --theme-main-rgb-color: #1989fa;
  --blue: #1989fa;
  --unselected-text-color: #969799;
  --radio-width: 36rpx;
  --half-radio-width: 18rpx;
}

.radius-popup {
  border-radius: 8px 8px 0 0;
}

.header-block {
  height: 60rpx;
}

.header-block-close {
  position: absolute;
  top: 20rpx;
  right: 32rpx;
  padding: 5px 0 10px 10px;
}

.header-block-close__hover {
  opacity: 0.6;
}

.just-wantmargin-but-you-need-to-write-class {
  margin-top: 48rpx;
}

/* van 不够灵活，挺难用的 */
.takeway-radio {
  font-size: 32rpx;
  color: var(--unselected-text-color);
  flex: 1;
  line-height: 1.4;
}

.takeway-radio--selected {
  font-size: 32rpx;
  color: var(--theme-main-color);
  line-height: 1.4;
  flex: 1;
}

.takeway-text {
  font-size: 28rpx;
  color: var(--theme-main-color);
  line-height: 1.4;
  margin-top: 8rpx;
}

.button-group {
  display: flex;
  margin: 24rpx -20rpx;
}

.button-content {
  margin: 0 20rpx;
  flex: 1;
}

.take-button {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  /* width: 320rpx; */
  height: 160rpx;
  border-radius: 8rpx;
  background: #f2f3f5;
  color: var(--theme-main-color);
  font-size: 40rpx;
  overflow: visible;
}

.take-button:after {
  border-color: transparent;
}

.take-button.focus,
.take-button__active {
  color: var(--theme-main-color);
  background: var(--theme-main-rgb-color);
}

.take-button.focus:after {
  border-color: var(--theme-main-color);
}

.radio-unselected,
.radio-selected {
  width: var(--radio-width);
  height: var(--radio-width);
  border-radius: var(--radio-width);
  border: 4rpx solid #d8d8d8;
  position: relative;
}

.radio-selected {
  border-color: var(--theme-main-color);
}

.radio-selected::after {
  content: "";
  position: absolute;
  width: var(--half-radio-width);
  height: var(--half-radio-width);
  border-radius: var(--half-radio-width);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--theme-main-color);
}
