$open-setting-dialog-width: 300px;
$open-setting-dialog-height: 180px;
$open-setting-btns-height: 50px;

$secondary-font-color: #aaa;
$secondary-bg-color: #f7f8fa;

.open-setting-container {
  position: fixed;
  width: 100vw;
  height: 100%;
  top: 100%;
  left: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;

  .open-setting-dialog {
    width: $open-setting-dialog-width;
    height: $open-setting-dialog-height;
    background-color: #fff;
    border-radius: 10px;

    .open-setting-info {
      width: $open-setting-dialog-width;
      height: $open-setting-dialog-height - $open-setting-btns-height;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      padding: 10px;
      border-bottom: 1px solid $secondary-bg-color;
      .open-setting-info-title {
        font-size: 16px;
        flex: 3;
        font-weight: 500;
      }
      .open-setting-info-msg {
        flex: 7;
        font-size: 14px;
        color: $secondary-font-color;
        text-align: center;
      }
      .open-setting-info-text {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .open-setting-btns {
      width: $open-setting-dialog-width;
      height: $open-setting-btns-height;
      display: flex;
      flex-direction: row;
      .open-setting-btn {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 15px;
      }
      .open-setting-btn:active {
        opacity: 0.6;
      }
      .open-setting-btns-auth::after {
        border: none;
      }
      .open-setting-btns-auth {
        background-color: transparent;
        border-left: 1px solid $secondary-bg-color;
        color: red;
      }
    }
  }
}
