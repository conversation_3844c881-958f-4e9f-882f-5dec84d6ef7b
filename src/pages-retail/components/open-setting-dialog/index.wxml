<view wx:if="{{showDialog}}" class="open-setting-container" animation="{{containerAnimation}}" catch:tap="noop" catch:touchmove="noop">
    <view class="open-setting-dialog">
        <view class="open-setting-info">
            <view class="open-setting-info-text open-setting-info-title">{{settingTitle}}</view>
            <view class="open-setting-info-text open-setting-info-msg">{{settingMsg}}</view>
        </view>
        <view class="open-setting-btns">
            <view class="open-setting-btn open-setting-btns-cancel" bindtap="clickCancel">{{cancelBtnText}}</view>
            <button 
                class="open-setting-btn open-setting-btns-auth" 
                open-type="openSetting" 
                bindtap="clickAuth"
            >
                {{authBtnText}}
            </button>
        </view>
    </view>
</view>