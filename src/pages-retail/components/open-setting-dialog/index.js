const ANIMATION_TIME = 200;

Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
      observer: 'computeContainerAnimation'
    },
    settingTitle: String,
    settingMsg: String,
    cancelBtnText: {
      type: String,
      value: '取消'
    },
    authBtnText: {
      type: String,
      value: '去授权'
    }
  },
  data: {
    showAnimation: null,
    showDialog: false
  },
  lifetimes: {
    created() {
      this.containerAnimation = wx.createAnimation({
        duration: ANIMATION_TIME
      });
    }
  },
  methods: {
    noop() {},
    clickCancel() {
      this.triggerEvent('clickCancel');
    },

    clickAuth() {
      this.triggerEvent('clickAuth');
    },

    computeContainerAnimation(val) {
      if (val) {
        this.containerAnimation.top(0).step();
        this.setData(
          {
            showDialog: val
          },
          () => {
            setTimeout(() => {
              this.setData({
                containerAnimation: this.containerAnimation.export()
              });
            }, ANIMATION_TIME);
          }
        );
      } else {
        this.containerAnimation.top('100%').step();
        this.setData(
          {
            containerAnimation: this.containerAnimation.export()
          },
          () => {
            setTimeout(() => {
              this.setData({
                showDialog: val
              });
            }, ANIMATION_TIME);
          }
        );
      }
    }
  }
});
