 <view class="goods-combo-collapse">
    <slot />
    <view wx:if="{{isShowCollapse}}" class="goods-combo-collapse__action">
      <btn
        custom-class="goods-combo-collapse__action--toggle"
        size="small"
        bind:click="toggle"
        plain
        round
        >{{ util.getToggleText(isUnfold) }}</btn>
    </view>
  </view>


<wxs module="util">
  function getToggleText(isUnfold) {
     return isUnfold ? '收起' : '展开';
  }

  module.exports = { getToggleText: getToggleText }
</wxs>