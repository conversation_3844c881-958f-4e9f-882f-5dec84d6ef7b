<view class="desc {{soldOut?'sold-out':''}}">
  <block wx:for="{{skuArr}}" wx:key="index" wx:for-item="sku">
    {{ sku.text }}
    <view wx:if="{{!!sku.price}}" class="price" style="{{util.getPriceStyle(soldOut,color)}}">{{ sku.minus }}¥{{ sku.price }}</view>
    {{ skuArr.length === index + 1 ? '' : ';' }}
  </block>
  <block wx:if="{{skuArr.length && propArr.length}}">;</block>
  <block wx:for="{{propArr}}" wx:key="index" wx:for-item="prop">
    {{ prop.text }}
    <view wx:if="{{!!prop.price}}" class="price" style="{{util.getPriceStyle(soldOut,color)}}">{{ prop.minus }}¥{{ prop.price }}</view>
    {{ propArr.length === index + 1 ? '' : ';' }}
  </block>
</view>



<wxs module="util">
  function getPriceStyle(soldOut,color) {
     var color = soldOut ? '#c8c9cc' : color
     return 'color:' + color ;
  }

  module.exports = { getPriceStyle: getPriceStyle }
</wxs>