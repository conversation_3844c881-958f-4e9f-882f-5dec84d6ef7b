:host {
  --theme-main-color: red;
}

$address-panel-bottom: 50px;

.address-panel-container {
  background: #f2f3f5;
  height: 100%;
  display: flex;
  flex-direction: column;

  .address-panel-body {
    box-sizing: border-box;
    padding: 10px;
    flex: 1;
    overflow-y: auto;

    .no-address {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      &_img {
        width: 100px;
        height: 100px;
        background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/no-address.svg);
        background-repeat: no-repeat;
        background-position: center;
      }
      &_title {
        font-size: 14px;
        color: #969799;
      }
    }
  }

  .address-panel-bottom {
    height: $address-panel-bottom;
    box-sizing: border-box;
    padding: 5px 10px;
    display: flex;
    background: #fff;
    .add-address-btn {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      background: var(--theme-main-color);
      border-radius: $address-panel-bottom / 2;
      color: #fff;
    }
    .add-address-btn:active {
      opacity: 0.6;
    }
  }
}
