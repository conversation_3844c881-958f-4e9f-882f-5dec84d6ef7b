<view class="address-panel-container">
    <view class="address-panel-body">
        <!-- <weixin-address-item /> -->
        <block wx:if="{{addresses && addresses.length}}">
            <block wx:for="{{addresses}}" wx:for-item="address" wx:key="id">
                <user-address-item 
                    bind:clickEditAddress="handleEditAddress"
                    bind:clickSelectAddress="handleSelectAddress"
                    isDefaultAddress="{{address.isDefault}}" 
                    selected="{{address.id == isSelectedId}}" 
                    userName="{{address.userName}}" 
                    userPhone="{{address.tel}}" 
                    addressId="{{address.id}}"
                    themeMainColor="{{themeMainColor}}"
                    address="{{address.province + address.city + address.county + address.community + address.addressDetail}}" 
                />
                <template is="placeholder-height" />
            </block>
        </block>
        <block wx:else>
            <view class="no-address">
                <view class="no-address_img" />
                <view class="no-address_title">你还没有收货地址哦</view>
            </view>
        </block>
    </view>
    <view class="address-panel-bottom">
        <view class="add-address-btn" bind:tap="clickNewAddress">+新增地址</view>
    </view>
</view>
<template name="placeholder-height">
    <view style="height: 10px"></view>
</template>