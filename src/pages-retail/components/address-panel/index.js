Component({
  properties: {
    addresses: {
      type: Array,
      value: []
    },
    isSelectedId: Number,
    themeMainColor: String,
    reTrigger: Boolean
  },
  methods: {
    clickNewAddress() {
      this.triggerEvent('clickNewAddress');
    },
    handleEditAddress({ detail }) {
      this.triggerEvent(
        'handleEditAddress',
        this.data.addresses.find(address => address.id == detail.addressId)
      );
    },
    handleSelectAddress({ detail }) {
      if (
        this.data.isSelectedId == detail.addressId &&
        !this.properties.reTrigger
      )
        return;
      this.triggerEvent(
        'handleSelectAddress',
        this.data.addresses.find(address => address.id == detail.addressId)
      );
    }
  }
});
