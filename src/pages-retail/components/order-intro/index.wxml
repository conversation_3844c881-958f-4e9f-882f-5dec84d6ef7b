<view
  class="order-intro"
  style="opacity: {{unavailable ? '0.5' : 1}}"
>
  <image
    class="order-img"
    src="{{ imagePath }}"
    mode="aspectFill"
    lazy-load
    style="height: {{imgWidth}}rpx; width: {{imgWidth}}rpx "
  />
  <view class="order-detail">
    <view wx:if="{{unavailable}}" class="main">
      <view class="content">
        <view class="row primary">
          <text>{{goods.name}}</text>
        </view>
        <view class="row" wx:if="{{goods.promotionTag}}">
          <van-tag custom-class="tag" round text-color="#fff">{{goods.promotionTag}}</van-tag>
        </view>
        <view class="row secondary">
          <view>{{util.join(goods.specifications, goods.propertiesDesc)}}</view>
        </view>
        <view class="row">
          <view class="unavailable-tag">{{goods.unavailableReason || '已失效'}}</view>
        </view>
      </view>
      <view wx:if="{{ goods.amount }}" class="goods-price">
        <price price="{{goods.salePrice || goods.price}}" origin-price="{{goods.originAmount / goods.amount}}" secondLineMode style="--price-color: #323233;--price-font-size: 28rpx;--price-family: avenir-heavy, pingfang sc, helvetica neue, arial, sans-serif;" />
        <view class="goods-item-num">x{{ goods.amount }}</view>
      </view>
    </view>

    <view class="main" wx:else>
      <view class="content">
        <view class="row primary">
          <text>{{goods.name}}</text>
        </view>
        <view class="row secondary" wx:if="{{util.join(goods.specifications, goods.propertiesDesc)}}">
          <view>{{util.join(goods.specifications, goods.propertiesDesc)}}</view>
        </view>
        <view wx:if="{{goods.isCombo}}" class="combo-row {{showAllCombo?'show':''}}">
          <view id="combo-row">
              <block wx:for="{{comboArr}}">
                 <view>{{item.name}} 
                    <text class="combo-row-num">
                      x{{item.num}}
                    </text>
                  </view>
              </block>
          </view>
          <van-icon name="arrow-down" wx:if="{{!showAllCombo&&showDown}}" class="combo-row-down"/>
          <view class="arrow-down-holder" bind:tap="showAllComboRow"></view>
        </view>
      
        <view class="row" wx:if="{{goods.promotionTag}}">
          <van-tag
            custom-class="tag"
            round
            text-color="#fff"
            size="small"
          >{{goods.promotionTag}}</van-tag>
        </view>
      </view>
      <view class="goods-price">
        <price
          price="{{goods.salePrice || goods.price}}"
          origin-price="{{goods.originAmount / goods.amount}}"
          secondLineMode
          style="--price-color: #323233;--price-font-size: 28rpx;--price-family: avenir-heavy, pingfang sc, helvetica neue, arial, sans-serif;"
        />
        <view class="goods-item-num">{{ goods.unitToShow }}</view>
      </view>
    </view>
  </view>
</view>

<wxs
  src="retail/util/money.wxs"
  module="money"
/>
<wxs module="util">
  function join(a = '', b = '') {
    return a ? a : '' + (a && b ? ', ' + b : b);
  }
  module.exports = { join: join };
</wxs>
