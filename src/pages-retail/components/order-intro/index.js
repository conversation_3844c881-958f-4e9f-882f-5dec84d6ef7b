// 想了一下，这个 setData 的操作，应该在 data 层处理。不然会存在大量的 setData 操作，影响性能。小程序真坑……
// import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { getThemeMainColor } from 'retail/util/shelf-global';

Component({
  properties: {
    goods: Object,
    amount: Number,
    imgWidth: {
      type: Number,
      value: 176,
    },
    unavailable: Boolean,
  },
  data: {
    imagePath: String,
    showAllCombo: false,
    showDown: false,
    comboArr: [],
    themeMainColor: getThemeMainColor(),
  },
  observers: {
    goods(goods) {
      // console.log('🚀 ~ file: index.js:40 ~ goods ~ goods', goods);
      const { picUrl, isCombo, comboDetail } = goods;
      const comboArr = [];
      if (isCombo) {
        try {
          comboDetail.comboGroups.forEach((t) => {
            t?.comboSubSkus.forEach((item) => {
              const propsStr =
                item.props?.map((ii) => ii.valName).join(';') || '';

              const str =
                item.skuDesc || propsStr
                  ? ` (${item.skuDesc || ''}${
                      item.skuDesc && propsStr ? ';' : ''
                    }${propsStr})`
                  : '';
              comboArr.push({
                name: `${item.goodsName}${str}`,
                num: item.num,
              });
            });
          });
        } catch (error) {
          console.log('解析套餐数据有误', error);
        }
      }
      this.setData(
        {
          comboArr,
          imagePath: cdnImage(picUrl, '!80x80+2x.jpg'),
        },
        () => {
          setTimeout(() => {
            const query = this.createSelectorQuery();
            query
              .select('#combo-row')
              .boundingClientRect()
              .exec((res) => {
                if (res[0] && res[0].height > 58) {
                  this.setData({
                    showDown: true,
                  });
                }
              });
          }, 1000);
        }
      );
    },
  },
  methods: {
    showAllComboRow() {
      this.setData({
        showAllCombo: true,
      });
    },
  },
});
