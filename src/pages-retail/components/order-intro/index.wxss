.order-intro {
  display: flex;
  min-height: 96rpx;
  background-color: #fff;
  padding: var(--padding, 0);
}

.order-intro view {
  box-sizing: border-box;
}

.order-img {
  width: 62rpx;
  height: 62rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
  margin-right: 16rpx;
}

.row:not(:last-child) {
  margin-bottom: 8rpx;
}

.order-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.order-detail .row {
  display: flex;
  align-items: center;
}

.row .reason {
  font-weight: normal;
}

.row.primary {
  display: -webkit-box;
  -webkit-box-align: start;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
  font-weight: 500;
  font-size: 30rpx;
  margin-bottom: 8rpx;
  line-height: 34rpx;
  color: #323233;
}

.row.secondary {
  color: #969799;
  font-size: 24rpx;
  line-height: 32rpx;
  color: #999;
}

.row .origin-price {
  text-decoration: line-through;
  color: #969799;
  font-size: 24rpx;
  margin-left: 16rpx;
  text-align: right;
}

.tag {
  height: 32rpx;
  line-height: 32rpx;
  font-size: 20rpx;
  border-radius: 4rpx;
  padding: 0 8rpx;
  background-color: var(--theme-main-color);
  color: #fff;
}

/* 计算高度使用px */
.combo-row {
  color: #969799;
  font-size: 12px;
  line-height: 18px;
  position: relative;
  max-height: 50px;
  overflow: hidden;
  margin-bottom: 10px;
}
.combo-row.show {
  max-height: inherit;
  overflow: inherit;
}
.combo-row-num {
  display: inline-block;
  margin-left: 10px;
}
.combo-row-down {
  position: absolute;
  right: 0;
  top: 40px;
  z-index: 3;
  background: #fff;
}
.main {
  display: flex;
  justify-content: space-between;
}
.content {
}

.goods-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  width: 180rpx;
  margin-top: -6rpx;
}
.goods-item-num {
  font-size: 24rpx;
  color: #969799;
}

.unavailable-tag {
  padding: 0 8rpx;
  background-color: #f2f3f5;
  color: #999;
  font-size: 24rpx;
  display: inline-block;
  min-width: 88rpx;
}

.arrow-down-holder {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30px;
  height: 30px;
}
