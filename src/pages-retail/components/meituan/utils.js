import { getDateStrFix2 } from '@youzan/weapp-utils/lib/time-utils';

export function format(coupon) {
  const {
    id,
    startTime,
    endTime,
    denominations,
    name,
    validPeriodCopywriting,
    isRecommend: selected,
    isCanUse: available,
    canNotUseReason: reason,
  } = coupon;
  const valueDesc = '' + denominations / 100;
  let decimal = [valueDesc];
  const showDecimal = valueDesc.includes('.');
  if (showDecimal) {
    decimal = valueDesc.split('.');
  }
  let period = validPeriodCopywriting;

  if (!period) {
    period =
      getDateStrFix2(new Date(startTime)).replace(/-/g, '.') +
      ' - ' +
      (endTime
        ? getDateStrFix2(new Date(endTime)).replace(/-/g, '.')
        : '无限制');
  }
  return {
    id,
    showDecimal,
    price: decimal[0],
    decimal: decimal[1],
    period,
    name,
    selected,
    available,
    reason,
  };
}
