/* eslint-disable prettier/prettier */
/* eslint-disable no-return-assign */
/* eslint-disable @youzan/koko/no-async-await */
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import money from '@youzan/weapp-utils/lib/money';
import { format } from './utils';

const app = getApp();

Component({
  properties: {
    themeColor: String,
    couponList: {
      type: Array,
      value: () => [],
      observer(val) {
        this.calValue(val);
      },
    },
    canChoose: {
      type: Boolean,
      value: true,
    }
  },
  data: {
    show: false,
    showPop: false,
    auth: false,
    coupons: [],
    loading: false,
    emptyImage: cdnImage('vant/coupon-empty.png'),
    couponTxt: '',
    kdtId: '',
  },

  created() {
    this.couponVal = 0;
    this.hasPull = false; // 是否主动拉取过美团券
    this.pullDown = false; // 主动拉取是否完成
    this.isLink = false; // 是否跳转美团小程序场景
    this.extra = {}; // 美团授权额外参数
  },

  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show() {
      this.check();
    },
  },

  attached() {
    this.setData({
      kdtId: app.getKdtId(),
    });
    this.check();
    this.cache = Promise.resolve();
  },

  methods: {
    genCouponTxt() {
      let txt = '选择优惠券';
      if (this.couponVal > 0) txt = `- ¥${+money(this.couponVal).toYuan()}`;
      if (!this.data.auth) txt = '授权获取卡券';
      this.setData({ couponTxt: txt });
    },
    calValue(coupons) {
      this.couponVal = coupons.reduce((price, coupon) => {
        price += +coupon.decreaseAmount;
        return price;
      }, 0);
      this.genCouponTxt();
    },
    check() {
      app
        .request({
          path: '/retail/h5/meituan/info.json',
          data: { source: 3 }
        })
        .then(
          ({ showChannelCoupon: show, userHasAuth: auth, extraDataDTO }) => {
            this.extra = extraDataDTO;
            this.setData({
              auth,
              show,
            });
            this.genCouponTxt();
            // this.auth = true;
            if (auth && this.isLink) {
              // 如果是跳转授权返回
              // 直接更新下单页
              this._isLink = false;
              this.cache = new Promise((resolve) => {
                this.triggerEvent('changeMeituanCoupons', {
                  done: () => {
                    resolve();
                  }
                }, {
                  composed: true,
                  bubbles: true,
                });
              });
            } else if (auth && !this.hasPull) {
              // 如果是已经授权过，且第一次进入
              // 静默缓存拉取美团券
              this.cache = this.onPull({ loading: false });
            }
          }
        )
        .catch(() => ({}));
    },
    async onPull() {
      if (this.hasPull) return;
      this.hasPull = true;
      await app.request({
        path: '/retail/h5/meituan/exchange.json',
        data: {
          source: 3,
        },
      });
      await new Promise((resolve) => {
        this.triggerEvent('changeMeituanCoupons', {
          done: () => {
            resolve();
          }
        }, {
          composed: true,
          bubbles: true,
        });
      });
      this.pullDown = true;
    },
    async onClick() {
      if (!this.data.auth) {
        this.isLink = true;
        wx.navigateToMiniProgram({
          appId: 'wxde8ac0a21135c07d',
          path: '/dynamic-page/index?scene=verify-bulk-comp|openplatform-verify-bulk',
          envVersion: 'release',
          extraData: this.extra,
        });
        return;
      }
      // 静默拉取未结束
      if (!this.pullDown) {
        this.setData({ loading: true });
        // 券更新接口实质上只会执行一次
        await this.cache.catch(() => {});
        this.setData({ loading: false });
        // 如果是跳转外部小程序授权返回，那么不走pull-券更新取流程
        // 所以这里还是要修正一次
        this.pullDown = true;
      }
      this.setData({
        coupons: (this.properties.couponList || []).map(format),
      });
      this.setData({
        showPop: true,
      });
    },
    onCancel() {
      this.triggerEvent('changeMeituanCoupons', {
        ids: [],
      }, {
        composed: true,
        bubbles: true,
      });
      this.setData({
        showPop: false,
      });
    },
    onClose() {
      this.setData({
        showPop: false,
      });
    },
    onChoose({
      currentTarget: {
        dataset: { index, selected, available },
      },
    }) {
      if (available) {
        this.setData({
          [`coupons[${index}].selected`]: !selected,
        });
      }
    },
    onConfirm() {
      this.triggerEvent('changeMeituanCoupons', {
        ids: this.data.coupons.filter((coupon) => coupon.selected),
      }, {
        composed: true,
        bubbles: true,
      });
      this.setData({
        showPop: false,
      });
    },
  },
});
