<view wx:if="{{ show }}">
  <user-authorize
    style="width: 100%"
    type="separate"
    kdt-id="{{ kdtId }}"
    auth-type-list="{{ ['mobile'] }}"
    bind:next="onClick">
    <van-cell
      custom-class="long-cell"
      is-link="{{ auth && canChoose }}"
      border="{{ false }}"
      center="{{ true }}"
    >
      <view slot="title" class="wrap">
        <van-tag class="wrap" color="#ffbd00" text-color="#000">美团</van-tag>
        <text class="title">美团券</text>
      </view>
      <view wx:if="{{ loading }}">
        <van-loading type="spinner" color="#1989fa" />
      </view>
      <view wx:else class="text {{ !canChoose ? 'disable-color' : '' }}">{{ couponTxt }}</view>
    </van-cell>
  </user-authorize>

  <van-popup
    wx:if="{{ auth }}"
    show="{{ showPop }}"
    round="{{ true }}"
    safe-area-inset-bottom="{{ true }}"
    position="bottom"
    class="action-sheet"
  >
    <view class="action-sheet__title">
      <text>请选择美团券</text>
      <van-icon name="cross" class="action-sheet__close" catch:tap="onClose" />
    </view>

    <view class="action-sheet__body">
      <view class="coupon-wrap {{ !item.available ? 'disabled' : '' }}" wx:for="{{ coupons }}" wx:key="{{ id }}" data-index="{{ index }}" data-selected="{{ item.selected }}" data-available="{{ item.available }}" catch:tap="onChoose">
        <view class="coupon">
          <view class="label">美团</view>
          <view class="amount">
            <view class="yuan_unit"> ¥ </view>
            <view class="num">
              <view>{{ item.price }}<text wx:if="{{ item.showDecimal }}" class="decimal">.{{ item.decimal }}</text></view>
            </view>
            <view class="unit">元</view>
          </view>
          <view class="coupon-info">
            <view class="coupon-name">{{ item.name }}</view>
            <view class="coupon-period">
              <view>{{ item.period }}</view>
            </view>
          </view>
          <view class="checkbox">
            <van-checkbox
              wx:if="{{ item.available }}"
              size="{{ 18 }}"
              value="{{ item.selected }}"
              class="t-coupon__corner"
              checked-color="{{ themeColor }}"
            />
          </view>
        </view>
        <view wx:if="{{ !item.available && item.reason }}" class="reason">{{ item.reason }}</view>
      </view>
      <view wx:if="{{ !coupons.length }}" class="empty">
        <image class="empty-img" mode="aspectFit" src="{{ emptyImage }}" />
        <view class="empty-text">暂无优惠券</view>
      </view>
    </view>

    <view class="action-buttons">
      <van-button
        custom-class="action-button action-button__first"
        class="custom-button"
        type="warning"
        size="large"
        catch:tap="onCancel"
      >
        不使用优惠券
      </van-button>
      <van-button
        custom-class="action-button action-button__last"
        class="custom-button"
        type="danger"
        size="large"
        catch:tap="onConfirm"
      >
        确定
      </van-button>
    </view>
  </van-popup>
</view>