.long-cell {
  padding: 10px 0 !important;

  > .t-cell__title {
    flex: none;
  }

  > .t-cell__value {
    color: #323233;
    font-weight: 500;

    .t-field__control::placeholder {
      font-weight: normal;
      text-align: right;
    }
  }

  &:not(:last-child):not(.t-cell--borderless)::after {
    left: 12px;
    right: 12px;
  }
}

.coupon-cell--value {
  color: #323233;
  font-weight: var(--theme-common-price-font-weight, 800) !important;
  font-family: Avenir;
}
.wrap {
  display: flex;
  align-items: center;
}
.title {
  margin-left: 6px;
}
.text {
  color: var(--theme-main-color, #333333);
  font-weight: 500;
}

.action-sheet {
  &__title {
    font-size: 16px;
    font-weight: 500;
    position: relative;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 12px;
    color: #c8c9cc;
    font-size: 22px;
    display: flex;
    align-items: center;
    height: 100%;
  }

  &__body {
    min-height: 250px;
    min-height: 40vh;
    max-height: 400px;
    max-height: 70vh;
    overflow-y: auto;
    background: #f7f8fa;
  }

  &__bottom {
    padding: 5px 16px;
  }

  &__button {
    height: 40px !important;
  }
}

.action-buttons {
  padding: 5px 12px calc(5px + env(safe-area-inset-bottom));
  display: flex;
  background-color: #fff;

  .custom-button {
    width: 50%;
  }

  .action-button {
    flex: 1;
    height: 40px !important;
    font-weight: 500;
    font-size: 14px;
    border: none;
    border-radius: 0;
    line-height: 40px;

    &__first {
      margin-left: 5px;
      border-top-left-radius: 999px !important;
      border-bottom-left-radius: 999px !important;
      background: var(--vice-bg);
      color: var(--vice-text);
    }

    &__last {
      margin-right: 5px;
      border-top-right-radius: 999px !important;
      border-bottom-right-radius: 999px !important;
      background: var(--main-bg);
      color: var(--main-text);
    }
  }
}

.empty {
  padding: 0;
  overflow: hidden;
  text-align: center;

  &-text {
    margin: 16px 0;
    color: #969799;
    font-size: 14px;
    line-height: 20px;
  }

  &-img {
    width: 200px;
    height: 200px;
  }
}

.coupon-wrap {
  margin: 12px 12px 0;
  background-color: #fff;
  border-radius: var(--theme-radius-card, 8px);
  box-sizing: border-box;
}
.coupon {
  position: relative;
  display: flex;
  padding: 12px 0;
  min-height: 84px;
  box-sizing: border-box;
}

.amount {
  display: flex;
  padding: 0 14px;
  flex-shrink: 0;
  font-weight: 500;
  font-size: 24px;
  align-self: center;
  align-items: baseline;
  font-family: Avenir;
  color: var(--ump-icon, #ee0a24);
}

.unit {
  display: inline-block;
  font-size: 40%;
  font-weight: 600;
}

.yuan_unit {
  font-size: 16px;
  font-weight: 600;
  margin-right: 2px;
  font-family: Avenir;
}

.decimal {
  font-size: 14px;
  font-weight: 500;
}
.coupon-info {
  overflow: hidden;
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  justify-content: space-between;
}
.coupon-name {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.coupon-period {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre-wrap;
  font-size: 12px;
  color: #969799;
}

.label {
  position: absolute;
  right: 0;
  top: 0;
  padding: 1px 8px;
  border-radius: var(--theme-radius-tag, 4px) 0 var(--theme-radius-tag, 4px) 0;
  font-size: 10px;
  line-height: 14px;
  background: #ffbd00;
  color: #000;
}

.checkbox {
  display: flex;
  padding: 0 14px;
  align-items: center;
}

.disabled {
  .amount,
  .coupon-period,
  .coupon-name {
    color: #969799;
  }
}

.reason {
  font-size: 12px;
  padding: 5px 12px;
  color: #969799;
}

.disable-color {
  font-size: 24rpx !important;
  font-weight: normal;
  color: var(--cell-value-color, #969799) !important;
}
