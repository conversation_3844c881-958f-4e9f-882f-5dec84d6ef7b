:host {
  --theme-main-color: #1989fa;
}

.picker-container {
  display: flex;
  flex-direction: column;
  padding-bottom: 2rpx;
  align-items: center;
}

.picker-container .center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.picker-container .header {
  position: relative;
  height: 88rpx;
  width: 100%;
  color: #323233;
  font-size: 16px;
  /* border-bottom: 0.1rpx solid #d8d8d8; */
}

.picker-container .header .arrow-container {
  position: absolute;
  left: 24rpx;
  top: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(-50%);
}

.picker-container .header van-icon {
  color: #969799;
  height: 36rpx;
  width: 36rpx;
  font-size: 36rpx;
  line-height: 1;
}

.picker-container .confirm {
  position: absolute;
  right: 24rpx;
}

.picker-column {
  color: #000;
  font-size: 28rpx;
  height: 100rpx;
  box-sizing: border-box;
  background: white;
}

.picker-column.highlight {
  color: var(--theme-main-color);
}

.picker-column.date {
  justify-content: left;
}

.picker-column.time {
  justify-content: space-between;
  padding-right: 40rpx;
}

.pick-view-container {
  height: 658rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.pick-view-date,
.pick-view-time {
  height: 100%;
  display: flex;
  overflow-y: auto;
}

.pick-view-date::-webkit-scrollbar,
.pick-view-time::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.pick-view-date {
  flex: 2;
  background: #f7f8fa;
}

.picker-column.date {
  background: #f7f8fa;
  padding-left: 36rpx;
}

.picker-column.date.highlight {
  background: white;
}

.pick-view-time {
  flex: 3;
  padding-left: 36rpx;
}

