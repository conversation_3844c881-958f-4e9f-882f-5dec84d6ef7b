<popup position="bottom" custom-style="border-radius: 8px 8px 0 0; {{customStyle}}" bind:close="closePopup" show="{{showPopup}}" overlay="{{overlay}}">
  <view class="picker-container">
    <view class="center header">
      <view class="arrow-container" wx:if="{{showArrow}}" bindtap="back">
        <van-icon name="arrow-left" />
      </view>
      <text>选择取单时间</text>
    </view>
    <view class="pick-view-container">
      <view class="pick-view-date">
        <view style="flex: 1;">
          <view class="picker-column date center {{index === currentRow ? 'highlight' : ''}}" wx:for="{{dateRows}}" wx:key="index" data-current-date="{{index}}" bindtap="onPickerDateChange">
            {{item.value + item.comments}}
          </view>
        </view>
      </view>
      <view class="pick-view-time">
        <view style="flex: 1">
          <view wx:if="{{timeRows[currentRow].length === 0}}">
            <view class="picker-column highlight center">已过今天可预约时间</view>
          </view>
          <block wx:else>
            <view class="picker-column time center {{index === currentTime ? 'highlight' : ''}}" wx:for="{{timeRows[currentRow]}}" wx:key="index" data-current-time="{{ index }}" bindtap="onPickerTimeChange">
              {{item}}
              <van-icon name="success" size="18px" color="{{themeMainColor}}" wx:if="{{index === currentTime}}"/>
            </view>
          </block>
        </view>
      </view>
    </view>
  </view>
</popup>