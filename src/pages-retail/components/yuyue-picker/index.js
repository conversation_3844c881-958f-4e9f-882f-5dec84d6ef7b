import { generateDateRange, generateTimeRange } from './generate-time-range';

Component({
  properties: {
    customStyle: String,
    overlay: {
      type: Boolean,
      value: true
    },
    takeConfig: {
      type: Object,
      observer: 'normalizeConfig'
    },
    showPopup: <PERSON><PERSON><PERSON>,
    placeholder: String,
    showArrow: Boolean
  },

  data: {
    currentRow: 0,
    currentTime: 0,
    timeRows: [],
    dateRows: []
  },

  methods: {
    normalizeConfig(config) {
      if (!config) {
        return this.setData({
          currentRow: 0,
          currentTime: 0,
          timeRows: [],
          dateRows: []
        });
      }

      // eslint-disable-next-line prefer-const
      let { index: [currentRow = 0, currentTime = 0] = [] } =
        getApp().getRetailConfig().appointmentConfig || {};

      // 如果有占位支付，time 往后移动一级
      if (this.data.placeholder && currentRow === 0) {
        currentTime += 1;
      }

      this.setData({
        currentRow,
        currentTime,
        timeRows: generateTimeRange(config, this.data.placeholder),
        dateRows: generateDateRange(config.days)
      });
    },

    onPickerChange({ detail }) {
      this.setData({
        currentRow: detail.value[0],
        currentTime: detail.value[1]
      });
    },

    getConfig() {
      const { dateRows, timeRows, currentRow, currentTime } = this.data;

      const date = dateRows[currentRow];
      const time = timeRows[currentRow][currentTime];

      if (!time || time === this.data.placeholder) {
        return null;
      }

      return {
        date,
        time,
        index: [currentRow, currentTime]
      };
    },

    back() {
      if (this.data.showArrow) {
        this.triggerEvent('back', this.getConfig());
      } else {
        this.triggerEvent('close', this.getConfig());
      }
    },

    closePopup() {
      if (this.data.showArrow) {
        this.triggerEvent('close');
      } else {
        this.triggerEvent('close', this.getConfig());
      }
    },

    onPickerDateChange({ currentTarget }) {
      this.setData({
        currentRow: currentTarget.dataset.currentDate
      });
    },

    onPickerTimeChange({ currentTarget }) {
      this.setData({
        currentTime: currentTarget.dataset.currentTime
      });
      this.closePopup();
      this.back();
    }
  }
});
