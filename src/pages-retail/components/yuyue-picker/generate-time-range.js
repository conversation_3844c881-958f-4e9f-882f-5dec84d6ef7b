import { moment } from '@youzan/weapp-utils/lib/time';

const defaultTakeConfig = {
  beginTime: '09:30',
  endTime: '18:00',
  days: 0,
  timeLevel: 60
};

const dayToChinese = date => {
  return '周' + ['日', '一', '二', '三', '四', '五', '六'][date.getDay()];
};

function formatDate(s) {
  const d = new Date(s);
  // TODO: 兼容性问题
  const minute = (d.getMinutes() + '').padStart(2, '0');
  const hours = (d.getHours() + '').padStart(2, '0');
  return `${hours}:${minute}`;
}

function getTimeRange(config, today = true) {
  const { beginTime, endTime, timeLevel } = config;

  const beginDate = new Date();
  beginDate.setHours(beginTime.split(':')[0]);
  beginDate.setMinutes(beginTime.split(':')[1]);
  beginDate.setSeconds(0);

  const endDate = new Date();
  endDate.setHours(endTime.split(':')[0]);
  endDate.setMinutes(endTime.split(':')[1]);

  let startingTime = new Date();
  if (today && Date.now() < +beginDate) {
    startingTime = beginDate;
  }

  if (today && Date.now() > +endDate) {
    return [];
  }

  if (!today) {
    startingTime = beginDate;
  }

  // 这样就能获得分别代表了开始时间和截止时间的 date 对象。

  const steps = [];
  const step = timeLevel * 60 * 1000;

  // 可以获得根据预约时间间隔配置的，所有的时间段
  for (let s = +startingTime; s < +endDate; s += step) {
    steps.push(formatDate(s));
  }
  // 不放在 for 循环是因为，是因为可能最后一个时间段是少于 step 表示的时间段。
  const endStep = formatDate(endDate);
  if (steps && steps.length && steps[steps.length - 1] !== endStep) {
    steps.push(endStep);
  }

  const ranges = [];
  let i = 0;
  while (i < steps.length - 1) {
    ranges.push(`${steps[i]}-${steps[i + 1]}`);
    i += 1;
  }
  return ranges;
}

function generateTimeRange(config = defaultTakeConfig, placeholder) {
  const { days } = config;
  const timeRange = [];

  for (let i = 0; i <= days; i += 1) {
    timeRange.push(getTimeRange(config, !i));
  }

  if (placeholder) {
    timeRange[0].unshift(placeholder);
  }

  return timeRange;
}

function generateDateRange(days) {
  const times = [];
  const ms = 24 * 60 * 60 * 1000;
  let now = new Date(Date.now() - ms);

  const getCommentDate = (i, nextDay) =>
    i < 3 ? ['今天', '明天', '后天'][i] : dayToChinese(nextDay);

  for (let i = 0; i <= days; i += 1) {
    const nextDay = new Date(+now + ms);
    times.push({
      value: moment(nextDay, 'MM-DD'),
      dateIncludesYear: moment(nextDay, 'YYYY-MM-DD'),
      comments: `（${getCommentDate(i, nextDay)}）`
    });
    now = nextDay;
  }
  return times;
}

export { generateDateRange, generateTimeRange };
