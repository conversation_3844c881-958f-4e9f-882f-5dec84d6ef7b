<view class="{{'ump-brief ' + layout}}" bind:tap="showPopup" wx:if="{{briefList.length > 0}}">
  <view class="ump-brief-tags">
    <text class="ump-brief-tag" wx:for="{{briefList}}" wx:for-item="briefItem" wx:key="id">{{briefItem.text}}</text>
  </view>
  <view class="ump-brief-more">
    <text>更多优惠</text>
    <van-icon name="arrow-down" custom-style="padding-left: 3rpx;" />
    <!-- <van-icon wx:if="{{expanded}}" name="arrow-up" custom-style="padding-left: 3rpx;" /> -->
  </view>
</view>

<popup
  position="bottom"
  z-index="{{1024}}"
  show="{{expanded}}"
  custom-style="height: 35%; border-radius: 36rpx 36rpx 0 0;"
  custom-class="ump-detail"
  bind:close="closePopup"
  overlay
>
  <view class="ump-detail-title">优惠活动</view>
  <van-icon
    name="cross"
    color="#969799"
    class="ump-detail-close"
    custom-style="position: absolute; top: 13px; right: 15px;"
    bind:click="closePopup"
  />
  <scroll-view scroll-y="true" style="flex: 1; overflow: auto;">
    <view class="ump-detail-group" wx:if="{{meetReduceList.length > 0}}">
      <view class="ump-detail-label">
        <text class="ump-brief-tag">满减</text>
      </view>
      <view class="ump-detail-list">
        <text
          class="ump-detail-text"
          wx:for="{{meetReduceList}}"
          wx:for-item="meetReduce"
          wx:for-index="meetReduceIndex"
          wx:key="id"
        >
          {{meetReduce.text}}
          <text wx:if="{{meetReduceIndex < meetReduceList.length - 1}}">，</text>
        </text>
      </view>
    </view>
    <view class="ump-detail-group" wx:if="{{baleList.length > 0}}">
      <view class="ump-detail-label">
        <text class="ump-brief-tag">打包一口价</text>
      </view>
      <view class="ump-detail-list">
        <text
          class="ump-detail-text"
          wx:for="{{baleList}}"
          wx:for-item="bale"
          wx:for-index="baleIndex"
          wx:key="id"
        >
          {{bale.text}}
          <text wx:if="{{baleIndex < baleList.length - 1}}">，</text>
        </text>
      </view>
    </view>
    <view class="ump-detail-group" wx:if="{{limitDiscount.length > 0}}">
      <view class="ump-detail-label">
        <text class="ump-brief-tag">限时折扣</text>
      </view>
      <view class="ump-detail-list">
        <text
          class="ump-detail-text"
          wx:for="{{limitDiscount}}"
          wx:for-item="dicount"
          wx:for-index="dicountIndex"
          wx:key="id"
        >
          {{dicount.text}}
          <text wx:if="{{dicountIndex < limitDiscount.length - 1}}">，</text>
        </text>
      </view>
    </view>
  </scroll-view>
</popup>
