Component({
  properties: {
    // meetReduceList: {
    //   type: Array,
    //   observer() {
    //     this.computeBriefList();
    //   }
    // },
    // baleList: {
    //   type: Array,
    //   observer() {
    //     this.computeBriefList();
    //   }
    // },
    // limitDiscount: {
    //   type: Array,
    //   observer() {
    //     this.computeBriefList();
    //   }
    // },
    // show: <PERSON><PERSON><PERSON>,
    layout: String,
    umpData: {
      type: Object,
      observer() {
        this.computeBriefList();
        // if (briefList.length > 0) {
        //   this.triggerEvent(
        //     'umpShow',
        //     {},
        //     {
        //       bubbles: true,
        //       composed: true
        //     }
        //   );
        // }
      }
    }
  },

  data: {
    expanded: false,
    meetReduceList: [],
    baleList: [],
    limitDiscount: [],
    briefList: []
  },

  methods: {
    computeBriefList() {
      const {
        meetReduceList = [],
        baleList = [],
        limitDiscount = []
      } = this.data.umpData;
      const ret = meetReduceList.slice(0, 5);
      if (ret.length < 5) {
        ret.push(...baleList.slice(0, 5 - ret.length));
      }
      if (ret.length < 5) {
        ret.push(...limitDiscount.slice(0, 5 - ret.length));
      }
      this.setData({
        briefList: ret,
        limitDiscount,
        baleList,
        meetReduceList
      });
    },

    closePopup() {
      this.setData({
        expanded: false
      });
    },

    showPopup() {
      this.setData({
        expanded: true
      });
    }
  }
});
