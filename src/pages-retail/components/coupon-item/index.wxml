<view class="coupon {{ isInvalid ? 'invalid' : '' }}" style="--theme-main-color: {{ themeMainColor }}; --theme-main-rgb-color: {{ themeMainRgbColor }}" bind:tap="onClickItem">
	<view class="left-view">
		<!-- 满减券 -->
		<block wx:if="{{ info.type === 'reward' }}">
			<view class="top-view">
				<view class="price {{priceClass}}">{{ info.value }}
					<text class="unit">元</text>
				</view>
			</view>
			<view class="bottom-view">
				<view class="desc">{{ info.value2 }}</view>
			</view>
		</block>
		<!-- 折扣券 -->
		<block wx:elif="{{ info.type === 'discount' }}">
			<view class="top-view">
				<view class="price {{priceClass}}">{{ info.value }}
					<view class="unit">折</view>
				</view>
			</view>
			<view class="bottom-view">
				<view class="desc">{{ info.value2 }}</view>
			</view>
		</block>
		<!-- 随机金额 -->
		<block wx:elif="{{ info.type === 'random' }}">
			<view class="top-view">
				<view class="big-text">随机金额</view>
			</view>
			<view class="bottom-view">
				<view class="desc">{{ info.value2 }}</view>
			</view>
		</block>
		<!-- 商品兑换券 -->
		<block wx:elif="{{ info.type === 'goods' }}">
			<view class="top-view">
				<view class="big-text">商品兑换</view>
			</view>
			<view class="bottom-view">
				<view class="desc">{{ info.value2 }}</view>
			</view>
		</block>

		<block wx:elif="{{ info.type === 'meetGive' }}">
			<view class="top-view">
				<view class="big-text">{{ info.value }}</view>
			</view>
			<view class="bottom-view">
				<view class="desc">{{ info.value2 }}</view>
			</view>
		</block>

		<!-- 新人标 -->
		<view class="new" wx:if="{{ info.isNewcomerVoucher }}">新人</view>
	</view>
	<view class="center-view">
		<view class="coupon-title-box">
			<view class="coupon-title">{{ info.title }}</view>
			<view wx:if="{{ info.count > 1 }}" class="coupon-mul">x{{ info.count }}张</view>
		</view>
		<view class="coupon-validity-period">{{ info.period }}</view>
	</view>
	<view class="right-view">
		<view wx:if="{{ isInvalid }}" class="invalid-text">不可用</view>
		<view wx:elif="{{ showDetail }}" class="detail" bind:tap="onDetail">查看详情</view>
		<view wx:elif="{{ selectMode }}" class="checkbox-area" bind:tap="onChangeSelected">
			<van-checkbox
			 style="margin: 0 auto;"
			 value="{{ isSelected }}"
			 disabled="{{ isDisabled }}"
			 checked-color="{{ themeMainColor }}"
			/>
		</view>
		<!-- 可叠加标 -->
		<view class="can-overlay" wx:if="{{ canOverlayAsset }}">可叠加</view>
	</view>
</view>
<view wx:if="{{ isInvalid }}" class="invalid-reason">
	不可用原因：{{ info.canNotUseReason }}
</view>

