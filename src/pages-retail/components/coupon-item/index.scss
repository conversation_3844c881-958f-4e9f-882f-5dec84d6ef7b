.coupon {
  display: flex;
  width: 100%;
  height: 84px;
  background: white;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;

  &.invalid {
    margin-bottom: 0;

    .left-view {
      .big-text,
      .coupon-title,
      .price,
      .desc {
        color: #c8c9cc;
      }
    }
    .center-view {
      .coupon-title-box .coupon-title {
        color: #c8c9cc;
      }
      .coupon-validity-period {
        color: #c8c9cc;
      }
    }
  }

  .left-view {
    position: relative;
    flex: none;
    width: 112px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .big-text {
      font-weight: 600;
      font-size: 18px;
      line-height: 25px;
      color: var(--theme-main-color);
      margin-top: 5px;
    }

    .price {
      color: var(--theme-main-color);
      font-weight: 600;
      font-size: 30px;
      line-height: 30px;
      word-break: break-all;

      .unit {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        margin-left: 4px;
        display: inline;
      }

      &.size-1 {
        font-size: 24px;
      }
      &.size-2 {
        font-size: 20px;
      }
      &.size-3 {
        font-size: 17px;
      }
    }
    .desc {
      font-size: 12px;
      line-height: 16px;
      color: #999999;
      margin-top: 6px;
    }
    .new {
      position: absolute;
      top: 0;
      left: -38px;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
      color: var(--theme-main-color);
      background-color: var(--theme-main-rgb-color);
      transform: rotate(-45deg);
      width: 100px;
      padding-top: 4px;
      padding-bottom: 2px;
    }
  }
  .center-view {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;

    .coupon-title-box {
      display: inline-flex;
      font-weight: 500;
      font-size: 16px;
      line-height: 18px;
      margin-bottom: 12px;
      margin-top: 6px;
      padding-right: 8px;

      .coupon-title {
        color: #323233;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .coupon-mul {
        flex: none;
        color: #999999;
        margin-left: 4px;
        font-size: 14px;
        font-weight: 400;
      }
    }
    .coupon-validity-period {
      font-size: 12px;
      line-height: 16px;
      color: #999999;
    }
  }
  .right-view {
    position: relative;
    flex: none;
    width: 76px;
    height: 100%;
    display: flex;
    align-items: center;

    .detail {
      display: flex;
      justify-content: center;
      align-items: center;
      background: var(--theme-main-color);
      border-radius: 12px;
      width: 60px;
      height: 24px;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
      color: #ffffff;
    }

    .checkbox-area {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 50px;
    }

    .can-overlay {
      position: absolute;
      top: 5px;
      right: -34px;
      font-size: 12px;
      line-height: 12px;
      text-align: center;
      color: var(--theme-main-color);
      transform: rotate(45deg);
      width: 100px;
      padding-top: 4px;
      padding-bottom: 2px;
      border: 1px solid var(--theme-main-color);
    }
  }
}

.invalid-reason {
  width: 100%;
  height: 32px;
  background: white;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #323233;
  padding: 8px 12px;
  box-sizing: border-box;
  border-top: 1px dashed #ebedf0;
  font-size: 12px;
  line-height: 16px;
  margin-bottom: 10px;
}
.invalid-text {
  width: 100%;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  color: #c8c9cc;
}
