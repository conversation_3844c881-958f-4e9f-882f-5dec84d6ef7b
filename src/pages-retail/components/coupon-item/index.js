import { ThemeColorWithComponent } from 'retail/util/behaviors/themeBehavior';

Component({
  behaviors: [ThemeColorWithComponent],
  properties: {
    info: {
      type: Object,
      value: {},
      observer(info) {
        const price = info.value.toString().length - 4;
        this.setData({
          priceClass: `size-${Math.min(price, 3)}`,
        });
      },
    },
    showDetail: {
      type: Boolean,
      value: false,
    },
    // 选择模式，后面展示选择圆圈
    selectMode: {
      type: Boolean,
      value: false,
    },
    // 是否选中
    isSelected: {
      type: Boolean,
      value: false,
    },
    // 是否不可选
    isDisabled: {
      type: Boolean,
      value: false,
    },
    // 是否失效
    isInvalid: {
      type: Boolean,
      value: false,
    },
    canOverlayAsset: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    priceClass: '',
  },
  methods: {
    onDetail() {
      this.triggerEvent('detail', this.data.info);
    },
    onChangeSelected() {
      if (this.data.isDisabled || this.data.isInvalid) {
        return;
      }
      this.triggerEvent('selected', {
        coupon: this.data.info,
        selected: !this.data.isSelected,
      });
    },
    onClickItem() {
      if (this.data.selectMode || this.data.isDisabled || this.data.isInvalid) {
        return;
      }
      this.triggerEvent('selected', {
        coupon: this.data.info,
        selected: true,
      });
    },
  },
});
