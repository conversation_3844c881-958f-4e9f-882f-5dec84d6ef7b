Component({
  properties: {
    title: {
      type: String,
      value: '',
      observer: 'changeTitle'
    },
    loading: {
      type: Boolean,
      value: false,
      observer: 'changeLoading'
    },
    frontColor: {
      type: String,
      observer: 'changeBarColor',
      value: '#ffffff'
    },
    backgroundColor: {
      type: String,
      observer: 'changeBarColor'
    }
  },
  methods: {
    changeTitle(title) {
      wx.setNavigationBarTitle({
        title
      });
    },
    changeLoading(loading) {
      loading ? wx.showNavigationBarLoading() : wx.hideNavigationBarLoading();
    },
    changeBarColor() {
      const color = this.data.frontColor;
      const frontColor =
        // eslint-disable-next-line no-nested-ternary
        color === 'white' ? '#ffffff' : color === 'black' ? '#000000' : color;
      wx.setNavigationBarColor({
        frontColor,
        backgroundColor: this.data.backgroundColor,
        animation: this.data.animation
      });
    }
  }
});
