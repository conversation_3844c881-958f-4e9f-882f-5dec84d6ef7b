import iPhoneX from 'retail/util/iphonex-behavior';
import { ThemeColorWithComponent } from 'retail/util/behaviors/themeBehavior';

Component({
  behaviors: [iPhoneX, ThemeColorWithComponent],
  properties: {
    activities: Array,
    show: Boolean,
    totalDecrease: Number,
  },
  methods: {
    closeActivityPop() {
      this.triggerEvent(
        'closeActivityPop',
        {},
        { composed: true, bubbles: true }
      );
    },
  },
});
