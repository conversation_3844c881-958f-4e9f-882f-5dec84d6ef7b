.activity-pop {
  position: fixed;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &__title {
    margin-bottom: 20rpx;
    flex-shrink: 0;
    font-size: 32rpx;
    font-weight: 500;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    color: #323233;
  }

  &__content {
    font-size: 32rpx;
    height: 300px;
  }

  &__activities {
    padding: 16px 32rpx;
  }

  &__btn {
    flex-shrink: 0;
    padding-bottom: 10rpx;
  }

  .activity-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-size: 28rpx;
    color: #323233;
    padding-bottom: 24rpx;
  }



  .activity-detail {
    flex: 1;
  }

  .activity-total {
    padding-top: 20px;
    border-top: 1rpx solid #ebedf0;
    text-align: right;
    font-size: 28rpx;
    color: #323233;
  }

  .ok-btn {
    width: 92%;
    background-color: var(--theme-main-color);
    color: #fff;
    border-radius: 44rpx;
  }
}