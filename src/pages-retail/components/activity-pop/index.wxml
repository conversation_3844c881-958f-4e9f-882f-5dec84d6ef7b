<popup
  overlay
  show="{{show}}"
  position="bottom"
  custom-style="border-radius: 36rpx 36rpx 0 0;"
  custom-class="activity-pop"
  z-index="{{1000}}"
  bind:click-overlay="closeActivityPop"
>
  <view class="activity-pop__title">活动优惠</view>
  <scroll-view class="activity-pop__content" scroll-y="true" style="height: 300px;">
    <view class="activity-pop__activities">
      <view
        wx:for="{{activities}}"
        wx:for-item="activity"
        wx:key="id"
      >
        <view class="activity-item">
          <view class="activity-tag"><text class="activity-tag__text">{{activity.typeDesc}}</text></view>
          <view class="activity-decrease">
            <text>{{'-¥ ' + money.toYuan(activity.decreaseAmount)}}</text>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <view class="activity-pop__btn" style="--theme-main-color: {{themeMainColor}}">
    <btn
      block
      type="default"
      custom-class="ok-btn"
      bind:tap="closeActivityPop">
      知道了
    </btn>
  </view>
</popup>

<wxs src="retail/util/money.wxs" module="money" />
