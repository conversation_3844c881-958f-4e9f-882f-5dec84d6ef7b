<view class="pay-submit-container t666" bind:tap="handleTapContainer" style="opacity: {{hidden ? 0 : 1}}; transition: opacity 0.3s ease-in-out; will-change: opacity; pointer-events: {{hidden ? 'none' : 'auto'}} ">
  <slot name="header" />
  <view class="pay-submit space-between">
    <block wx:if="{{payInfo.show}}">
      <view class="pay-submit__left">
        <text class="pay-submit__label">
          {{payInfo.label}}
          <text class="pay-submit__price">¥ {{payInfo.amount}}</text>
        </text>
        <text style="font-size:10px" class="pay-submit__discount">{{payInfo.desc}}</text>
      </view>
      <btn custom-class="pay-button recharge" plain hairline round bind:click="emitRecharge" data-recommend>
        {{payInfo.button}}
      </btn>
      <!-- 这里还是用emitPay 但是要告诉pay是储值推荐触发的 -->
    </block>
    <block wx:else>
      <view class="pay-submit__text" wx:if="{{showPrice}}">
        <view class="pay-submit__total">
          <text class="pay-submit__label">{{payLabel || '合计: '}}</text>
          <!-- 价格没有计算完成状态 -->
          <text wx:if="{{ !canPay }}">¥--</text>
          <block wx:else>
            <text class="pay-submit__label notice" style="color: #c8c9cc" wx:if="{{originPrice === 0 && price === 0 && isEmptyCart}}">
              未选购商品
            </text>
            <view wx:else>
              <price origin-price="{{originPrice}}" price="{{price}}" style="--price-color: #323233;" />
            </view>
          </block>
        </view>
        <view class="pay-submit__discount">
          <text style="margin-right: 10rpx;">已优惠</text>
          <price price="{{totalDecrease}}" style="--price-color: var(--theme-main-color);--price-font-size: 24rpx;--price-weight:400;--price-height:36rpx;" />
        </view>
      </view>
      <view class="pay-submit__action" catch:tap="noop">
        <btn wx:if="{{ showAppend }}" custom-class="pay-button add" plain hairline round type="info" style="margin-right: 12rpx;" bind:click="handleAddMeal">
          加菜
        </btn>
        <btn wx:elif="{{ showCancel }}" custom-class="pay-button cancel" plain hairline round type="info" bind:click="cancelOrder" loading="{{isCancel}}" style="margin-right: 12rpx;">
          取消订单
        </btn>
        <user-authorize wx:if="{{!hasAuthMobile && !hiddenPay}}" scene="click_buy_now" authTypeList="{{ ['mobile'] }}" bind:next="emitPay" bind:fail="emitPay">
          <btn round hairline type="info" custom-class="pay-button {{ showPrice ? '' : 'larger' }} {{ supportAddMeal ? 'long' : '' }}" disabled="{{disabled || !canPay}}" loading="{{isPaying}}" style="--button-default-height: 96rpx;--button-default-font-size:32rpx;--button-border-radius:48rpx;">
            <slot />
          </btn>
        </user-authorize>
        <btn wx:elif="{{!hiddenPay}}" round hairline type="info" custom-class="pay-button {{ showPrice ? '' : 'larger' }} {{ supportAddMeal ? 'long' : '' }}" disabled="{{disabled || !canPay}}" loading="{{isPaying}}" style="--button-default-height: 96rpx;--button-default-font-size:32rpx;--button-border-radius:48rpx;" bind:tap="emitPay">
          <slot />
        </btn>
      </view>
    </block>
  </view>
  <view style="height: 34px; background: white" wx:if="{{isiPhoneX}}" />
</view>
<zan-pay theme-color="{{ themeColor }}" bind:init="initPay" bind:paysuccess="onPaidSuccess" bind:navigate="onCashierNavi" bind:cashierclose="onClose" />