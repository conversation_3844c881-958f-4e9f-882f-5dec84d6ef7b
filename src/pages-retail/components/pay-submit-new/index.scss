:host {
  --button-color: #ffffff;
  --button-border-color: #1989fa;
  --button-bg-color: #1989fa;
  --container-height: 128rpx;
  --text-valign: center;
}

.pay-submit-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}

.pay-button {
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible !important;
  border: none !important;
  background-color: var(--theme-main-color, --button-border-color) !important;
  width: 336rpx;
  height: 96rpx !important;
  font-weight: 500;
  font-size: 32rpx !important;

  &.cancel,
  &.add {
    background-color: #fff !important;
    color: #333333 !important;
    border-color: #e0e0e0 !important;
    font-weight: 500;
    font-size: 32rpx !important;
    width: 266rpx;
  }

  &.add {
    border-color: var(--theme-main-color) !important;
    color: var(--theme-main-color) !important;
  }

  &.recharge {
    color: #fff;
  }

  &.larger {
    width: 420rpx;
  }

  &.long {
    width: 702rpx;
  }

  &.small {
    width: 212rpx;
  }
}

.pay-submit {
  position: relative;
  box-sizing: border-box;
  background: white;
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  height: var(--container-height);

  &.space-between {
    justify-content: space-between;
  }

  &__text {
    display: flex;
    flex-direction: column;
    align-items: var(--text-valign, center);

    &-extra {
      font-size: 20rpx;
      color: #959799;
    }
  }

  &__price {
    margin-left: 8rpx;
    font-size: 32rpx;
  }

  &__discount,
  &__total {
    display: flex;
    align-items: baseline;
  }

  &__discount {
    font-size: 24rpx;
    line-height: 32rpx;
    color: var(--theme-main-color);
  }

  &__label {
    font-size: 28rpx;
    color: #323233;
    margin-right: 10rpx;

    &.notice {
      color: #c8c9cc;
    }
  }

  &__action {
    margin-left: auto;
    display: flex;
    align-items: center;
  }
}

.ways-index--cap-pay-item-cell {
  height: 50px;
}

.left-money {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-weight: 600;
  font-size: 20px;
}
