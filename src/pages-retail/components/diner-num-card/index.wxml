<view
  class="diner-num-container"
  style="--themeMainRgbColor:{{themeMainRgbColor}};--themeMainColor:{{themeMainColor}};background-image:url('{{dinerNumInfo.backgroundImg||defaultImgBgUrl}}'); --title-color:{{dinerNumInfo.backgroundImg?'#fff':'#333'}}"
  wx:if="{{dinerNumInfo.showChooseDinerNums}}"
>
  <view class="mask-view {{dinerNumInfo.backgroundImg?'active':''}}">
    <view class="shop_container">
      <view class="sub_title">欢迎光临</view>
      <view class="shop_name">{{shopName}}</view>
    </view>
    <view class="select__card">
      <view class="select__card_header">
        <view class="header__left">你好，请选择人数</view>
        <view class="header__right" wx:if="{{tableCode}}">
          <view class="diner-icon" />
          {{tableName}}桌
        </view>
      </view>
      <view class="select__card_ul">
        <view
          wx:for="{{ defaultDinerNumsRange }}"
          wx:for-item="index"
          wx:key="index"
          data-index="{{index+1}}"
          bindtap="handleDinerNum"
          class="select__card_li {{activeNum===index+1?'active':''}}"
        >
          {{index+1}}
        </view>
        <view
          class="select__card_li"
          bindtap="handleOther"
          wx:if="{{dinerNumInfo.customDinerNums}}"
        >其他
        </view>
      </view>
      <view class="select__card_btn {{activeNum>0?'actived':''}}" bindtap="handleOpenOrder">开始点单</view>
    </view>
    <view/>

    <diner-number-keyboard
      show="{{showNumberKeyboard}}"
      theme-main-rgb-color="{{themeMainRgbColor}}"
      theme-main-color="{{themeMainColor}}"
      bind:collapse="handleCollapse"
      bind:confirm="handleConfirm"
    />


