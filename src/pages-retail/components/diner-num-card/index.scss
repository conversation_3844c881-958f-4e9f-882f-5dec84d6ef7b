@mixin flex($direction: column, $justify: center, $align: center) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
}

:host {
  --themeMainColor: rgb(174,33,33);
  --themeMainRgbColor: rgb(174,33,33,0.1);
  --title-color: #333;
}

.diner-num-container {
  background: rgb(249, 243, 237);
  background-size: cover;
  background-position: center center;
  height: 100%;
  .mask-view{
    @include flex();
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    &.active{
      background: rgba(0,0,0,0.4);
    }
  }


  .shop_container {
    @include flex();
    width: 100%;
    margin: 182px 0 100px;

    .sub_title {
      font-weight: 400;
      font-size: 20px;
      margin-bottom: 14px;
      color: var(--title-color);
      opacity: 0.6;
    }

    .shop_name {
      width: 80%;
      text-align: center;
      line-height: 36px;
      font-weight: 500;
      font-size: 26px;
      color: var(--title-color);
    }
  }

  .select__card {
    width: 700rpx;
    padding: 16px 16px 20px;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 12px;
    position: absolute;
    bottom: calc(48px + constant(safe-area-inset-bottom));
    bottom: calc(48px + env(safe-area-inset-bottom));

    &_header {
      height: 40px;
      font-size: 14px;
      @include flex(row, space-between, center);
      margin-bottom: 20px;

      .header__left {
        font-weight: bold;
        font-size: 18px;
        text-indent: 4px;
      }

      .header__right {
        @include flex(row);
        font-weight: 500;
        color: #999;

        .diner-icon {
          width: 24px;
          height: 24px;
          background-image: url('https://img01.yzcdn.cn/upload_files/2023/05/18/FsODSs_alrEHmoLeUWqTW-rxnPjj.png');
          background-size: contain;
        }
      }
    }

    &_ul {
      margin-bottom: 22px;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 16px 8px;

      .select__card_li {
        @include flex();
        background: #f7f7f7;
        border-radius: 20px;
        height: 72rpx;
        width: 148rpx;

        &.active {
          background: var(--themeMainColor);
          color: #fff;
        }
      }
    }

    .select__card_btn {
      @include flex();
      height: 48px;
      width: 100%;
      background-color: var(--themeMainRgbColor);
      border-radius: 30px;
      font-size: 16px;
      font-weight: bold;
      color: #fff;
      &.actived{
        background: var(--themeMainColor);
      }
    }
  }
}