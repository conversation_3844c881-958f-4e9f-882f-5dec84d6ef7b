import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

Component({
  properties: {
    themeMainRgbColor: String,
    themeMainColor: String,
    dinerNumInfo: {
      type: Object,
      value: {},
    },
    tableCode: String,
    shopName: String,
  },
  data: {
    activeNum: 0,
    defaultImgBgUrl: cdnImage(
      'upload_files/2023/06/13/FhJMZX_CH1RwSGLkftMDU9i-Rh39.png'
    ),
    showNumberKeyboard: false,
  },
  observers: {
    dinerNumInfo(config) {
      const { showChooseDinerNums, defaultDinerNumsRange, customDinerNums } =
        config || {};
      if (config && showChooseDinerNums) {
        this.setData({
          defaultDinerNumsRange: new Array(
            defaultDinerNumsRange - (customDinerNums ? 1 : 0)
          ),
        });
      }
    },
    tableCode(text) {
      if (text?.length > 10) {
        text = text.slice(0, 4) + '...' + text.slice(-4);
      }
      this.setData({ tableName: text });
    },
  },

  methods: {
    // 选择人数
    handleDinerNum(ev) {
      const { index } = ev.target.dataset;
      this.setData({ activeNum: index });
    },
    // 开始点单
    handleOpenOrder() {
      if (this.data.activeNum < 1) {
        wx.showToast({
          title: '请先选择人数',
          icon: 'none',
        });
        return;
      }
      this.triggerEvent('openOrder', this.data.activeNum);
    },
    // 键盘开始点单
    handleConfirm(en) {
      this.triggerEvent('openOrder', en.detail);
    },
    // 缩起
    handleCollapse() {
      this.setData({ showNumberKeyboard: false });
    },
    // 其他
    handleOther() {
      this.setData({ showNumberKeyboard: true });
    },
  },
});
