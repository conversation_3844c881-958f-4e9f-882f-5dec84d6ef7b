<view  class="combo-suk-container">
  <sku
    id="parentSku"
    bind:addGoods="addGoods"
    bind:closeSku="closeSku"
    wx:if="{{show}}"
    show="{{showPop}}"
    is-lite="{{isLite}}"
    sku-models="{{skuModels}}"
    props="{{props}}"
    goods-no="{{goodsNo}}"
    origin-price="{{originPrice}}"
    loading="{{loading}}"
    title="{{title}}"
    picture="{{picture}}"
    source-pictures="{{sourcePictures}}"
    is-negative-stock="{{isNegativeStock}}"
    goods-info="{{goodsInfo}}"
    scene="{{scene}}"
    combo-type="{{comboType}}"
    bind:openSubSkuPop="openSubSkuPop"
    show-large-image="{{showLargeImage}}"
    promotion-tags="{{promotionTags}}"
    add-on-stock-num="{{addOnStockNum}}"
  >
  </sku>
  <!-- 套餐子商品 -->
  <sku
    bind:addGoods="addSubSkuGoods"
    bind:closeSku="closeSubSku"
    wx:if="{{showComboSubSku}}"
    show
    sku-models="{{comboSubData.skuModels}}"
    props="{{comboSubData.skuProps}}"
    goods-no="{{comboSubData.skuGoodsNo}}"
    origin-price="{{comboSubData.skuInformation.price}}"
    loading="{{comboSubLoading}}"
    title="{{comboSubData.skuInformation.title}}"
    picture="{{comboSubData.skuInformation.picture}}"
    source-pictures="{{comboSubData.skuInformation.sourcePictures}}"
    goods-info="{{comboSubData.skuInformation}}"
    scene="common"
    isSubComboSku
    submitBtnText="加入套餐"
    promotion-tags="{{promotionTags}}"
  >
  </sku>
</view>


