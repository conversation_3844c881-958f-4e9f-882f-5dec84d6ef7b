Component({
  data: {
    showComboSubSku: false,
    comboSubData: {},
    comboSubLoading: false,
    showPop: false,
  },
  properties: {
    title: String,
    goodsNo: String,
    picture: String,
    sourcePictures: Array,
    originPrice: Number, // 基本价格
    isNegativeStock: Boolean,
    isLite: Boolean, // 是否为 Lite 店铺
    comboType: Number, // 套餐类型
    loading: {
      type: Boolean,
    },
    show: {
      type: Boolean,
      value: false,
      observer(val) {
        setTimeout(() => {
          this.setData({
            showPop: val,
          });
        }, 0);
      },
    },
    props: {
      type: Array,
      value: [],
    },
    skuModels: {
      type: Array,
      value: [],
    },
    // 是否立即显示大图
    showLargeImage: {
      type: Boolean,
      value: false,
    },
    // 场景
    scene: {
      type: String,
      value: 'common', // common 普通, surcharge 必选, combo 套餐
    },
    goodsInfo: {
      type: Object,
      value: {},
    },
    /** 营销标 */
    promotionTags: {
      type: Array,
      value: [],
    },
    /** 赠品的最大可选库存 */
    addOnStockNum: {
      type: Number,
      value: -1,
    },
  },
  lifetimes: {
    attached() {},
  },
  methods: {
    closeSku({ detail }) {
      this.triggerEvent('closeSku', detail);
    },
    addGoods({ detail }) {
      this.triggerEvent('addGoods', detail);
    },
    openSubSkuPop({ detail }) {
      console.log('🚀 ~ file: index.js:55 ~ openSubSkuPop ~ detail', detail);
      this.setData({
        showComboSubSku: true,
        comboSubData: detail,
      });
      this;
    },
    closeSubSku() {
      this.setData({
        showComboSubSku: false,
        comboSubData: {},
        comboSubLoading: false,
      });
    },
    addSubSkuGoods({ detail }) {
      const componentInstants = this.selectComponent(`#parentSku`);
      componentInstants.alterComboGoods({ detail });
      this.closeSubSku();
    },
  },
});
