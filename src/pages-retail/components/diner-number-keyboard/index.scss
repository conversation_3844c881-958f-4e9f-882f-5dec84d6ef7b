:host {
  --themeMainColor: rgb(174,33,33);
  --themeMainRgbColor: rgb(174,33,33,0.1);
}

.my-popup{
  background-color: #F2F3F5 !important;
}

.number-keyboard-container {
  background-color: #F2F3F5;
  padding-bottom: 20px;
}

.number-keyboard {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 10px;
  padding: 10px;
  border-radius: 5px;

  .key {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: 48px;
    background-color: #FFF;
    border-radius: 8px;
    font-weight: 400;
    font-size: 28px;
  }

  .delete {
    grid-column-start: 4;
    grid-column-end: 5;
    grid-row-start: 1;
    grid-row-end: 3;
    height: 100%;

    &-icon {
      width: 32px;
      height: 22px;
    }
  }

  .collapse {
    &-icon {
      width: 32px;
      height: 22px;
    }
  }

  .confirm {
    grid-column-start: 4;
    grid-column-end: 5;
    grid-row-start: 3;
    grid-row-end: 5;
    font-size: 16px;
    color: #fff;
    height: 100%;
    font-weight: bold;
    background-color: var(--themeMainRgbColor);
    &.actived{
      background: var(--themeMainColor);
    }
  }

  .zero {
    grid-column-start: 1;
    grid-column-end: 3;
    grid-row-start: 4;
    grid-row-end: 5;
  }
}

.show-number-container {
  height: 116px;
  color: #000;
  padding: 10px;

  .title {
    text-align: center;
    font-weight: 500;
    font-size: 18px;
    margin-top: 3px;
  }

  .select-num {
    margin-top: 30px;
    text-align: center;
    font-weight: 500;
    font-size: 32px;

    .unit {
      font-size: 14px;
      color: #999;
      margin-left: 5px;
    }
  }
}