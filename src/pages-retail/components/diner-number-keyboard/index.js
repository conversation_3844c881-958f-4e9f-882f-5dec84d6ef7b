Component({
  data: {
    activeNum: '0',
  },
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    themeMainRgbColor: String,
    themeMainColor: String,
    dinerNumInfo: {
      type: Object,
      value: {},
    },
  },

  methods: {
    handleTap(e) {
      const { key } = e.target.dataset;
      if (!key) return;
      let { activeNum } = this.data;

      if (key === 'delete') {
        activeNum = activeNum.slice(0, -1);
        if (activeNum === '') activeNum = '0';
      } else if (key === 'confirm') {
        if (activeNum < 1) {
          wx.showToast({
            title: '请先选择人数',
            icon: 'none',
          });
          return;
        }
        this.triggerEvent('confirm', activeNum);
        setTimeout(() => {
          this.triggerEvent('collapse');
        }, 1000);
      } else if (key === 'collapse') {
        this.triggerEvent('collapse');
      } else if (activeNum === '0') {
        activeNum = key;
      } else {
        activeNum += key;
      }
      // 限制最大值
      if (activeNum > 99) {
        activeNum = '99';
      }

      this.setData({
        activeNum,
      });
    },
    onClose() {
      this.triggerEvent('collapse');
    },
  },
});
