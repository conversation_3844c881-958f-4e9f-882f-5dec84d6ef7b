<van-popup
  use-slot
  show="{{ show }}"
  position="bottom"
  round
  closeable
  custom-class="my-popup"
  bind:close="onClose"
  style="--themeMainRgbColor:{{themeMainRgbColor}};--themeMainColor:{{themeMainColor}}"
>
  <view class="number-keyboard-container">
    <view class="show-number-container">
      <view class="title">请选择人数</view>
      <view class="select-num">
        {{activeNum}}
        <text class="unit">位</text>
      </view>
    </view>

    <view class="number-keyboard" bindtap="handleTap">
      <view class="key" data-key="1">1</view>
      <view class="key" data-key="2">2</view>
      <view class="key" data-key="3">3</view>
      <view class="key delete" data-key="delete">
        <image
          data-key="delete"
          mode="aspectFit"
          class="delete-icon"
          src="https://img.yzcdn.cn/public_files/2021/01/18/29270d14b3823734f8b10c52d9f4541c.png"
        />
      </view>
      <view class="key" data-key="4">4</view>
      <view class="key" data-key="5">5</view>
      <view class="key" data-key="6">6</view>
      <view class="key" data-key="7">7</view>
      <view class="key" data-key="8">8</view>
      <view class="key" data-key="9">9</view>
      <view class="key confirm {{activeNum>0?'actived':''}}" data-key="confirm">
        <view data-key="confirm">
          开始
        </view>
        <view data-key="confirm">
          点单
        </view>
      </view>
      <view class="key zero" data-key="0">0</view>
      <view class="key collapse" data-key="collapse">
        <image
          mode="aspectFit"
          data-key="collapse"
          class="collapse-icon"
          src="https://img.yzcdn.cn/public_files/2021/01/18/ee822ccad0925c9da9483da7392f3bb4.png"
        />
      </view>
    </view>
  </view>

</van-popup>


