import { geocoder, gcjToBaidu } from '@/helpers/lbs';

Component({
  properties: {
    title: {
      type: String,
      value: '获取微信收货地址'
    }
  },
  data: {
    showSettingDialog: false
  },
  methods: {
    clickGetWxAddress() {
      wx.getSetting({
        success: res => {
          const isAuthAddress = res.authSetting['scope.address'];
          if (isAuthAddress === undefined) {
            wx.chooseAddress({
              fail: () => {
                this.setData({ showSettingDialog: true });
              },
              success: this.chooseSuccess.bind(this)
            });
          } else if (isAuthAddress === false) {
            this.setData({ showSettingDialog: true });
          } else {
            wx.chooseAddress({
              success: this.chooseSuccess.bind(this)
            });
          }
        }
      });
    },

    chooseSuccess(res) {
      const { provinceName, cityName, countyName, detailInfo } = res;
      geocoder({
        address: provinceName + cityName + countyName + detailInfo
      })
        .then(geoResult => {
          if (geoResult.status === 0) {
            const {
              location,
              address_components: { province, city, district }
            } = geoResult.result;
            const { lng, lat } = gcjToBaidu(location.lng, location.lat);
            const detail = {
              id: 0,
              lon: lng + '',
              lat: lat + '',
              province,
              city,
              county: district,
              addressDetail: detailInfo
            };
            this.triggerEvent('returnWxAddress', detail, { bubbles: true, composed: true });
          } else {
            wx.showToast({ title: '该地址无效，请重新选择!', icon: 'none' });
          }
        })
        .catch(e => {
          wx.showToast({
            title: e.msg || e.message || '该地址无效，请重新选择!',
            icon: 'none'
          });
        });
    },

    handleCancelSettingDialog() {
      this.setData({ showSettingDialog: false });
    },

    handleConfirmSettingDialog() {
      this.setData({ showSettingDialog: false });
    }
  }
});
