$wx-address-container-height: 45px;

.wx-address-container:active {
  opacity: 0.6;
}

.wx-address-container {
  position: relative;
  height: $wx-address-container-height;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  box-sizing: border-box;
  border-radius: 4px;

  .wx-icon {
    width: $wx-address-container-height;
    height: $wx-address-container-height;
    background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/new-wechat.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 50rpx 50rpx;
  }

  .wx-address-title {
    font-size: 14px;
  }

  .wx-address-arrow {
    position: absolute;
    right: 0;
    top: 0;
    width: $wx-address-container-height;
    height: $wx-address-container-height;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .wx-right-arrow {
    position: absolute;
    right: 0;
    top: 0;
    width: $wx-address-container-height;
    height: $wx-address-container-height;
    background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/03_arrow.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 12px 12px;
  }
}
