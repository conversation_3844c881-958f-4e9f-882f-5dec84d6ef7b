<view
  wx:if="{{progressType === 'banner'}}"
  class="banner-progress banner-container"
>
  <block wx:if="{{timeMinutes > 0}}">
    <view class="progress-container">
      <progress-bar
        textColor="{{textColor}}"
        barColor="{{barColor}}"
        text="{{orderNum}}单制作中"
        percentage="{{orderNum}}"
      />
    </view>
    <!-- <text
      class="status-container"
      style="margin-left: 14rpx;"
    >预计等待{{timeMinutes}}分钟</text> -->
  </block>
  <text
    wx:if="{{timeMinutes === 0}}"
    class="status-container"
  >无需等待，可立即取餐</text>
</view>

<view
  wx:elif="{{progressType === 'card'}}"
  class="card-progress banner-container"
>
  <block wx:if="{{timeMinutes > 0}}">
    <view class="card-progress-header">
      <text>前面</text>
      <text
        class="highlight-text"
        style="color: {{barColor}};"
      >{{orderNum}}单</text>
      <text>制作中</text>
      <!-- <text
        class="highlight-text"
        style="color: {{barColor}};"
      >{{timeMinutes}}分钟</text> -->
    </view>
    <progress-bar
      textColor="{{textColor}}"
      barColor="{{barColor}}"
      percentage="{{orderNum}}"
    />
  </block>
  <view
    wx:if="{{timeMinutes === 0}}"
    class="card-progress-header"
  >无需等待，可立即取餐</view>
</view>

<view
  wx:elif="{{progressType === 'text'}}"
  class="card-progress banner-container"
>
  <block wx:if="{{timeMinutes > 0}}">
    <view class="card-progress-header">
      <text>前面</text>
      <text
        class="highlight-text"
        style="color: {{barColor}};"
      >{{orderNum}}单</text>
      <text>制作中</text>
      <!-- <text
        class="highlight-text"
        style="color: {{barColor}};"
      >{{timeMinutes}}分钟</text> -->
    </view>
  </block>
  <block wx:if="{{timeMinutes === 0}}">
    <slot></slot>
  </block>
</view>

<view
  wx:elif="{{progressType === 'order'}}"
  class="banner-progress banner-container"
>
  <block wx:if="{{timeMinutes > 0}}">
    <view class="progress-container-100">
      <progress-bar
        textColor="{{textColor}}"
        barColor="{{barColor}}"
        text="{{orderNum}}单制作中"
        percentage="{{orderNum}}"
      />
    </view>
  </block>
</view>

<view wx:else>progressType 类型有误</view>
