import { queryQueueToday, queryOrderQueueToday } from './api';

Component({
  externalClasses: ['banner-container'],
  properties: {
    progressType: {
      type: String,
      value: 'banner',
    },
    // 订单号
    orderNo: String,
    // 外部传入数据
    sourceDataFromExternal: Object,
  },
  data: {
    barColor: '#5CCDB3',
    textColor: '#323233',
    orderNum: 0,
    timeMinutes: -1, // 默认为-1
  },
  observers: {
    orderNum(num) {
      let barColor = '#5CCDB3';
      let textColor = '#323233';
      if (num <= 30) {
        // 小于30 绿色
        barColor = '#5CCDB3';
        textColor = '#323233';
      } else if (num <= 80) {
        // 小于80 黄色
        barColor = '#FAAB0C';
        textColor = '#000000';
      } else {
        // 大于80 红色
        barColor = '#EE0A24';
        textColor = '#FFFFFF';
      }
      this.setData({
        barColor,
        textColor,
      });
    },
  },
  lifetimes: {
    ready() {
      this.refresh();
    },
  },

  methods: {
    refresh() {
      const { sourceDataFromExternal, orderNo } = this.data;
      if (sourceDataFromExternal) {
        this.handleResponseData(sourceDataFromExternal);
        return;
      }
      if (orderNo) {
        return this.fetchOrderQueueToday(orderNo);
      }
      return this.fetchQueueToday();
    },

    fetchOrderQueueToday(orderNo) {
      return queryOrderQueueToday(orderNo)
        .then(this.handleResponseData.bind(this))
        .catch(this.handleRejectData);
    },

    fetchQueueToday() {
      return queryQueueToday()
        .then(this.handleResponseData.bind(this))
        .catch(this.handleRejectData);
    },

    handleResponseData(data) {
      const { queueOrderSum: orderNum, totalTimeSum } = data;
      this.setData({
        orderNum,
        timeMinutes: Math.round(totalTimeSum / 60),
      });
    },

    handleRejectData(e) {
      wx.showToast({
        title: e.msg || '获取订单排队信息失败, 请重试',
        icon: 'none',
      });
    },
  },
});
