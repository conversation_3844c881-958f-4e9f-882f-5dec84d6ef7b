Component({
  properties: {
    title: String,
    takeCodeImage: String,
    takeCode: {
      type: String,
      observer(val) {
        this.setData({ prettyTakeCode: val.replace(/(\d{4})/g, '$1 ') });
      }
    },
    bottomDesc: String,
    showSaveToPhoneAlbum: {
      type: Boolean,
      value: false
    }
  },
  data: {
    prettyTakeCode: ''
  },
  methods: {
    saveToPhoneAlbum() {
      const fileManager = wx.getFileSystemManager();
      fileManager.writeFile({
        filePath: wx?.env?.USER_DATA_PATH + '/tmp.png',
        data: this.data.takeCodeImage.slice(22),
        encoding: 'base64',
        success: () => {
          wx.saveImageToPhotosAlbum({
            filePath: wx?.env?.USER_DATA_PATH + '/tmp.png',
            success() {
              wx.showToast({
                icon: 'none',
                title: '保存图片到相册成功！'
              });
            },
            fail(err) {
              wx.showToast({
                icon: 'none',
                title: err?.msg ?? '保存失败，请重试！'
              });
            }
          });
        },
        fail: err => {
          wx.showToast({
            icon: 'none',
            title: err?.msg ?? '写入文件失败，请重试！'
          });
        }
      });
    }
  }
});
