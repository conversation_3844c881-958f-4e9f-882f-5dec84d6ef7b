import { VanxComponent } from 'pages/common/wsc-component/index';

VanxComponent({
  properties: {
    valueCard: {
      type: Array,
      value: [],
      observer: 'initState'
    },
    // 充值推荐规则
    recommendDetail: {
      type: Object,
      value: {},
      observer: 'initState'
    },
    product: {
      type: Object,
      value: {},
      observer: 'setGiftPackState'
    }
  },
  data: {
    showRecommend: false,
    giveRecharge: false,
    isEmptyGiftPack: true,
    valueCardRecommendAmount: '',
    prepaidPrefix: '单用途预付卡',
    agreementUrl: '/packages/pre-card/agreement/index'
  },

  created() {
    this.initState();
  },

  methods: {
    initState() {
      const { recommendDetail = {}, valueCard } = this.data;
      const balance = valueCard.length ? valueCard[0] : {};
      const valueCardRecommendAmount = this.formatPrice(
        recommendDetail.amount || 0
      );
      this.setYZData({
        valueCardRecommendAmount,
        showRecommend: this.showRecommend(valueCardRecommendAmount),
        giveRecharge: !!balance.ifGiveRecharge
      });
    },
    setGiftPackState() {
      this.setYZData({
        isEmptyGiftPack: this.data.product?.giftPacks?.length === 0
      });
    },
    showRecommend(valueCardRecommendAmount) {
      // 当存在推荐金额时展示储值推荐
      if (valueCardRecommendAmount === '') return false;
      return valueCardRecommendAmount || valueCardRecommendAmount === 0;
    },
    formatPrice(price) {
      return price % 100 === 0
        ? Math.round(price / 100)
        : (price / 100).toFixed(2);
    },
    onRecharge() {
      this.triggerEvent('onRecharge');
    }
  }
});
