import WscComponent from 'pages/common/wsc-component/index';
import { joinMember } from 'retail/util/bind-mobile';

const app = getApp();

WscComponent({
  properties: {
    showBindPhoneNumber: Boolean,
    mobile: String,
    originKdtId: Number,
    memberSrcWay: Number,
    memberSrcChannel: Number
  },

  data: {
    userInfo: {
      nickName: '',
      avatarUrl: ''
    }
  },

  attached() {
    app.getUserInfo(res => {
      this.setYZData({
        userInfo: res.userInfo
      });
    });
  },

  methods: {
    getPhoneNumber(e) {
      if (this.data.mobile) {
        this.triggerEvent('accountSuccess', {
          mobile: this.data.mobile
        });
        return;
      }
      const { originKdtId, memberSrcWay, memberSrcChannel } = this.data;
      joinMember(() => this.triggerEvent('reload'), {
        kdt_id: originKdtId || app.getKdtId(),
        member_src_way: memberSrcWay || 800,
        member_src_channel: memberSrcChannel || 1000,
        need_be_member: true
      });
    },

    onGetUserInfo(res) {
      const { nickname: nickName, avatar: avatarUrl } = res.detail;
      this.setYZData({
        userInfo: {
          nickName,
          avatarUrl
        }
      });
    }
  }
});
