.user-info {
  height: 108rpx;
  padding: 20rpx 0;
  position: relative;
  margin: 0 20px;
}

.user-info image {
  width: 100%;
  height: 100%;
}

.user-info__avatar {
  width: 108rpx;
  height: 108rpx;
  border-radius: 50%;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
}

.user-info__default {
  background-size: 108rpx 108rpx;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .1);
  vertical-align: middle;
}

.user-info__tip {
  font-size: 40rpx;
  line-height: 56rpx;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.user-info__auth {
  display: inline-block;
}

.user-info__right-region {
  display: inline-block;
  margin-left: 30rpx;
  width: 70%;
}

.user-info__nickname {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 40rpx;
  line-height: 56rpx;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.user-info__mobile {
  font-size: 28rpx;
  line-height: 40rpx;
  height: 40rpx;
  padding-left: 30rpx;
  background-image: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
  background-repeat: no-repeat;
  background-size: 18rpx 26rpx;
  background-position: 0 45%;
}

.user-info__mobile--btn {
  display: inline-block;
  width: 148rpx;
  height: 40rpx;
  background-color: #fff;
  font-size: 24rpx;
  padding: 0;
  line-height: 37rpx;
  border: 1rpx solid #333;
  border-radius: 20rpx;
}

.icon--arrow {
  position: absolute;
  top: 0;
  right: -10px;
  width: 100px;
  height: 81px;
  background-position: 80px center;
  background-repeat: no-repeat;
  background-size: 10px;
  background-image: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
}

.icon {
  width: 22rpx;
  height: 40rpx;
  background-size: 22rpx;
  background-repeat: no-repeat;
  background-position: center;
}

.icon--mobile {
  background-image: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
  display: inline-block;
}
