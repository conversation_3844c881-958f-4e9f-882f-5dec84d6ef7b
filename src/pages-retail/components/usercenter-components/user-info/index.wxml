<view class="user-info">
  <view wx:if="{{ !userInfo.nickName }}">
    <user-authorize class="user-info__auth" authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="onGetUserInfo">
      <view class="user-info__avatar user-info__default">
        <image src="//b.yzcdn.cn/showcase/membercenter/2018/08/06/<EMAIL>" alt="avatar" />
      </view>
    </user-authorize>
    <view class="user-info__right-region">
      <user-authorize class="user-info__auth" authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="onGetUserInfo">
        <view class="user-info__tip">
          点击显示微信头像
        </view>
      </user-authorize>
      <view class="user-info__mobile">
        <view wx:if="{{showBindPhoneNumber}}" class="user-info__mobile--btn">
          <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="getPhoneNumber">
            绑定手机号
          </user-authorize>
        </view>
        <!-- <button
          wx:if="{{showBindPhoneNumber}}"
          open-type="getPhoneNumber"
          bindgetphonenumber="getPhoneNumber"
          class="user-info__mobile--btn"
        >
          绑定手机号
        </button> -->
        <view wx:else>{{mobile || '-'}}</view>
      </view>
    </view>
  </view>
  <view wx:else>
    <navigator
      class="user-info__avatar"
      url="/pages-retail/usercenter/account-setting/index?origin_kdt_id={{originKdtId}}&member_src_way={{memberSrcWay}}&member_src_channel={{memberSrcChannel}}"
      hover-class="none"
    >
      <image src="{{ userInfo.avatarUrl || '//b.yzcdn.cn/showcase/membercenter/2018/08/06/<EMAIL>'}}" />
    </navigator>
    <view class="user-info__right-region">
      <view class="user-info__nickname">
        Hi, {{ userInfo.nickName}}
      </view>
      <view class="user-info__mobile">
        <view wx:if="{{showBindPhoneNumber}}" class="user-info__mobile--btn">
          <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="getPhoneNumber">
            绑定手机号
          </user-authorize>
        </view>
        <!-- <button
          wx:if="{{showBindPhoneNumber}}"
          open-type="getPhoneNumber"
          bindgetphonenumber="getPhoneNumber"
          class="user-info__mobile--btn"
        >
          绑定手机号
        </button> -->
        <view wx:else>{{mobile}}</view>
      </view>
      <navigator hover-class="none" url="/pages-retail/usercenter/account-setting/index?origin_kdt_id={{originKdtId}}&member_src_way={{memberSrcWay}}&member_src_channel={{memberSrcChannel}}" class="icon--arrow">
      </navigator>
    </view>
  </view>
</view>
