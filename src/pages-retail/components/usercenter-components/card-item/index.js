import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import WscComponent from 'pages/common/wsc-component/index';
import { COLOR_MAP } from './constant';

WscComponent({
  properties: {
    name: String,
    desc: String,
    colorCode: String,
    coverUrl: String
  },
  data: {
    coverUrlCdn: '',
    COLOR_MAP
  },
  attached() {
    if (this.data.coverUrl) {
      this.setYZData({
        coverUrlCdn: cdnImage(this.data.coverUrl)
      });
    }
  }
});
