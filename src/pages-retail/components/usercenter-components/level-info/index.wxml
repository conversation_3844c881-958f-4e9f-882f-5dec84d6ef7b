<view class="'level-info' {{ fetching || !isMember || !coverUrl ? 'level-info__empty_level' : '' }}" style="{{ coverUrl ? 'background-image: url(' + coverUrl + ')' : isMember ? 'background:-webkit-linear-gradient(to left top, #C49542, #F1CF64)' : ''}}">
  <view bindtap="handleCardClick" wx:if="{{!fetching}}" class="level-info__card">
    <view class="level-info__inner">
      <block wx:if="{{isMember}}">
        <view class="level-info__header">
          <view class="level-info__header--desc">
            {{ levelName }}
            <text class="level-info__level-icon" />
            <text class="level-info__level-number">{{ level || '' }}</text>
          </view>
          <view class="level-info__mobile">{{ mobile || '' }}</view>
          <view wx:if="{{!!level}}" class="level-info__qrcode" catch:tap="handleQrcodePopup" />
        </view>
        <view class="level-info__member-tip">
          <view>当前成长值 {{ currentGrowth }}</view>
          <view wx:if="{{ isMaxLevel }}" class="level-info__member-right">已是最高等级</view>
          <view wx:else class="level-info__member-right">
            升级到VIP {{ level ? level + 1 : 1 }} 还需 {{ nextLevelGrowthGap }}
          </view>
        </view>
      </block>
      <view wx:else>
        <text class="level-info__become-member">成为店铺会员，尊享会员权益</text>
        <view wx:if="{{ !mobile }}" class="level-info__bind-btn">
          <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="bindMobileAndBecomeMember">
            绑定手机成为会员
          </user-authorize>
        </view>
        <!-- <button
          wx:if="{{ !mobile }}"
          open-type="getPhoneNumber"
          bindgetphonenumber="bindMobileAndBecomeMember"
          class="level-info__bind-btn"
          catch:tap="noop"
        >
          绑定手机成为会员
        </button> -->
        <button wx:else class="level-info__bind-btn" catch:tap="becomeMember">领取权益</button>
      </view>
    </view>
  </view>
</view>
<!-- 一旦关闭 popup 就直接移除，停止轮询 -->
<level-info-popup
  wx:if="{{ showPopup }}"
  show="{{ showPopup }}"
  identity="{{ identityNo }}"
  levelName="{{ levelName }}"
  nickName="{{ nickName }}"
  level="{{ level }}"
  bind:close="handleQrcodePopup"
/>
