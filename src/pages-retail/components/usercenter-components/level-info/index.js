import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'utils/open-web-view';
import { joinMember } from 'retail/util/bind-mobile';
import navigate from '@/helpers/navigate';
import api from './api';

const app = getApp();

WscComponent({
  properties: {
    isMember: Number,
    mobile: String,
    fetching: Boolean,
    levelName: String,
    nickName: String,
    level: Number,
    nextLevelGrowthGap: Number,
    isMaxLevel: Boolean,
    currentGrowth: Number,
    coverUrl: String,
    identityNo: String,
    levelQrcode: String,
    levelBarcode: String,
    showQrcodePopup: Boolean,
    originKdtId: Number,
    memberSrcWay: Number,
    memberSrcChannel: Number
  },

  data: {
    fetching: true,
    showPopup: false
  },

  methods: {
    // 当前用户绑定过手机号, 但是不是会员
    noop() {},

    becomeMember() {
      const { originKdtId, memberSrcWay, memberSrcChannel } = this.data;
      joinMember(() => this.triggerEvent('reload'), {
        kdt_id: originKdtId || app.getKdtId(),
        member_src_way: memberSrcWay || 800,
        member_src_channel: memberSrcChannel || 1000
      });
    },

    gotoMemberCenter() {
      if (!this.data.fetching) {
        const { originKdtId, memberSrcWay, memberSrcChannel } = this.data;
        navigate.navigate({
          url: `/packages/retail/membercenter/index?origin_kdt_id=${originKdtId}&member_src_way=${memberSrcWay}&member_src_channel=${memberSrcChannel}`
        });
      }
    },

    handleCardClick() {
      const { isMember, mobile } = this.data;
      const kdtId = app.getKdtId();
      api
        .checkInwhitelist({
          kdt_id: app.getHQKdtId(),
        })
        .then(
          ({ value: isInWhiteList }) => {
            if (isInWhiteList) {
              if (!mobile) {
                return;
              }
              if (isMember) {
                openWebView(
                  `https://h5.youzan.com/wscuser/memberlevel?kdt_id=${kdtId}`,
                  { title: '会员中心' }
                );
              } else {
                openWebView(
                  `https://h5.youzan.com/wscuser/memberlevel/mobilecheck?kdt_id=${kdtId}&sales_id=0&referee_id=0&referee_scene=0`
                );
              }
            } else {
              navigate.navigate({
                url: '/packages/retail/membercenter/index'
              });
            }
          },
          (err = {}) => {
            wx.showToast({
              title: err.msg || '网络错误',
              icon: 'none'
            });
            navigate.navigate({
              url: '/packages/retail/membercenter/index'
            });
          }
        );
    },

    // 当前用户无手机号, 并且不是会员.
    bindMobileAndBecomeMember(event) {
      const { originKdtId, memberSrcWay, memberSrcChannel } = this.data;

      joinMember(() => this.triggerEvent('reload'), {
        kdt_id: originKdtId || app.getKdtId(),
        member_src_way: memberSrcWay || 800,
        member_src_channel: memberSrcChannel || 1000,
        need_be_member: true
      });
    },

    handleQrcodePopup() {
      this.setData({
        showPopup: !this.data.showPopup
      });
    }
  }
});
