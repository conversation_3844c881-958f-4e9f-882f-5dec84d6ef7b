<!-- 集点卡2.0 -->
<reward-points></reward-points>
<!-- <view class="collect-points" wx:if="{{!!collectPointsResult}}">
  集点卡1.0
  <collect-points id="collectPoints" result="{{collectPointsResult}}"/>
</view> -->
<view class="prepaid-info">
  <view class="prepaid-info__title">储值余额（元）</view>
  <view class="prepaid-info__price">
    <span class="{{ balance  > -1 ? '' : 'update-text' }}">{{ !hasAgreeProtocol ? '-' : balance}}</span>
    <block wx:if="{{!isRetail && !isHideBalanceEntry}}">
      <view
        wx:if="{{ mobile }}"
        class="prepaid-info__recharge"
        catch:tap="handleRechargeClick"
      >
        立即充值
      </view>
      <view wx:else class="prepaid-info__recharge">
        <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="becomeMember">
          立即绑定
        </user-authorize>
      </view>
      <!-- <button
        wx:else
        class="prepaid-info__recharge"
        bind:getphonenumber="handleRechargeClick"
        open-type="getPhoneNumber"
      >
        立即绑定
      </button> -->
    </block>
  </view>
</view>
<view class="point-info">
  <view class="point-info__title">可用{{statsNames.points}}</view>
  <view class="point-info__amount">
    <text class="{{ points  > -1 ? '' : 'update-text' }}">{{ !hasAgreeProtocol ? '-' : points}}</text>
    <navigator
      wx:if="{{hasStore && !isRetail}}"
      hover-class="none"
      url="/packages/ump/integral-store/index"
      class="point-info__store"
    >
      {{statsNames.points}}兑换
    </navigator>
  </view>
</view>

<view class="dashboard-container__scrollx">
  <coupon-info id="couponInfo" bind:subscribeMsg="handleSubscribeMsg" is-retail="{{isRetail}}" is-member="{{isMember}}" has-agree-protocol="{{ hasAgreeProtocol }}" />
</view>
<view class="usercenter-hr" />
<view class="dashboard-container__scrollx">
  <card-info id="cardInfo" pointsName="{{ statsNames.points }}" has-agree-protocol="{{ hasAgreeProtocol }}" />
</view>

<view class="dashboard-container">

  <cell
    wx:if="{{ !isRetail }}"
    title="我的赠品"
    value="查看"
    title-class="cell-bold"
    custom-class="usercenter-cell"
    is-link
    url="/packages/ump/presents/index"
  />
  <cell-group wx:if="{{!isRetail}}">
    <cell
      title="我的返现"
      value="去提现"
      title-class="cell-bold"
      custom-class="usercenter-cell"
      is-link
      url="/packages/user/cashback/list/index"
    />

    <cell
      wx:if="{{isOpenDistribution}}"
      value="查看"
      custom-class="usercenter-cell"
      bind:tap="handleSalesmanClick"
      is-link
    >
      <view slot="title">
        <user-authorize
          authTypeList="{{ ['nicknameAndAvatar'] }}"
          is-once
        >
          <view class="van-cell-text cell-bold">{{ salesman.settingName || '分销员' }}中心</view>
        </user-authorize>
      </view>
    </cell>

    <cell
      wx:if="{{ isSetGroupCenter && !isMultiOnlineShop}}"
      value="查看"
      custom-class="usercenter-cell"
      title-class="cell-bold"
      is-link
      url="/packages/groupbuying/activity/list/index"
    >
      <view slot="title">
        <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" is-once>
          <view class="van-cell-text cell-bold">{{ groupName || '团长' }}中心</view>
        </user-authorize>
      </view>
    </cell>

    <cell
      wx:if="{{!isMultiOnlineShop}}"
      title="我的礼品卡"
      value="查看"
      title-class="cell-bold"
      custom-class="usercenter-cell"
      is-link
      url="/packages/gift/list/index"
    />
    <cell wx:if="{{ fromParams }}" title="我的专属销售员" value="查看" title-class="cell-bold" bind:tap="handleShopperClick" custom-class="usercenter-cell" right-icon-class="usercenter-cell-icon">
      <view slot="right-icon" class="usercenter-cell-icon" />
    </cell>
  </cell-group>
  <cell wx:if="{{ showBlindBoxEntrance }}" title="我的盲盒" title-class="cell-bold" custom-class="usercenter-cell" value="查看" is-link url="{{ blindBoxUrl }}" />

  <cell
    title="账号设置"
    value="去设置"
    title-class="cell-bold"
    custom-class="usercenter-cell"
    is-link
    url="/packages/account/settings/index"
  />
  <cell-group wx:if="{{!isRetail && servicePhoneNumber}}">
    <cell title="联系客服" title-class="cell-bold" bind:tap="handleContactCustomerService" custom-class="usercenter-cell" />
  </cell-group>
  <subscribe-message show="{{showSubscribeMark}}" z-index="{{15}}"></subscribe-message>
</view>
