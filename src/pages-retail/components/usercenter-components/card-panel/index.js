import wxLogin from 'utils/wx-login';
import { joinMember } from 'retail/util/bind-mobile';
import {
  queryWechatSubscribeResult,
  getTemplateByScene,
  requestSubscribeMessage,
} from 'utils/subscribe-message';
import api from './api';

const app = getApp();
// 订购新版储值店铺的 status
const newStoredValueStatus = 9;
// 获取到优惠券过期和优惠券到账的两个模版ID都scene值
const scene = 'coupon_notice_scene';
// 获取到活动通知的模版ID
const pageId = 5;

Component({
  properties: {
    mobile: String,
    balance: String,
    points: String,
    isRetail: Boolean,
    originKdtId: Number,
    memberSrcWay: Number,
    memberSrcChannel: Number,
    isMember: Number,
    hasAgreeProtocol: Boolean,
  },

  data: {
    hasStore: false,
    isSetGroupCenter: false,
    groupName: '',
    salesman: {},
    servicePhoneNumber: '',
    fromParams: '',
    fromSource: '',
    isOpenDistribution: true,
    isHideBalanceEntry: false,
    statsNames: {
      points: '积分',
    },
    collectPointsResult: undefined,
    showBlindBoxEntrance: false,
    blindBoxUrl: `/pages/common/webview-page/index?src=${encodeURIComponent(
      '/wscump/blind-box/list'
    )}&title=${encodeURIComponent('盲盒列表')}`,
    showSubscribeMark: false,
    templateIds: [],
  },

  lifetimes: {
    ready() {
      this.fetchCouponTemplateIds();
    },
  },

  methods: {
    init() {
      this.fetchPointsName();
      this.handleShopMode();
      this.fetchIsOpenStore();
      this.fetchSalesmanData();
      this.fetchCustomerService();
      this.fetchGroupCenterInfo();
      this.fetchShoppersInfo();
      this.fetchShopDistributionInfo();
      this.fetchBalanceWhiteList();
      this.fetchBlindBoxInfo();
      // 集点卡，需要buyerId，来获取用户当前集点活动详情
      // app.getToken('buyerId') && this.fetchCollectPointsActivity();
    },
    // 获取优惠券订阅消息模板
    fetchCouponTemplateIds() {
      const promises = [
        queryWechatSubscribeResult(pageId),
        getTemplateByScene(scene),
      ];
      Promise.all(promises).then(([templateByPageId, templateByScene]) => {
        const { templateList: templateIdList = [] } = templateByPageId || {};
        const { templateIdList: templateSceneIdList = [] } =
          templateByScene || {};
        this.setData({
          templateIds: templateIdList.concat(templateSceneIdList),
        });
      });
    },
    // 获取 我的盲盒 入口信息
    fetchBlindBoxInfo() {
      app
        .request({
          path: '/wscump/blind-box/checkBlindBoxEntrance.json',
          method: 'get',
        })
        .then((res) => {
          this.setData({
            showBlindBoxEntrance: res && res.value,
          });
        })
        .catch(() => {});
    },

    async fetchCollectPointsActivity() {
      try {
        const result = await api.queryCurrentCollectPointsActivity();
        const { activityModel: { costPoints, startAt } = {} } = result;
        if (costPoints && startAt) {
          this.setData({ collectPointsResult: result });
        }
      } catch (error) {
        console.error(error);
      }
    },

    fetchPointsName() {
      app
        .getPointsName()
        .then(({ pointsName = '积分' }) => {
          this.setData({
            'statsNames.points': pointsName,
          });
        })
        .catch(() => {});
    },

    // 多网店模式下隐藏分销员和返现入口
    handleShopMode() {
      app.getShopInfo().then((data) => {
        const { chainStoreInfo = {} } = data;
        this.setData({
          isMultiOnlineShop: !!chainStoreInfo.isMultiOnlineShop,
        });
      });
    },

    fetchSalesmanData() {
      app.carmen({
        api: 'youzan.salesman.wap.account/1.0.0/get',
        success: (salesman) => {
          this.setData({
            salesman,
          });
        },
      });
    },

    fetchBalanceWhiteList() {
      api
        .checkIsInWhiteList({
          type: 12,
        })
        .then((res) => {
          const { is_support_white_config, is_hide } = res;
          if (is_support_white_config && is_hide) {
            this.setData({
              isHideBalanceEntry: true,
            });
          }
        });
    },

    // 获取群团购相关信息
    fetchGroupCenterInfo() {
      app
        .request({
          path: 'wscuser/membercenter/groupCenterInfo.json',
          method: 'GET',
        })
        .then((res = {}) => {
          this.setData({
            isSetGroupCenter: res.isShow,
            groupName: res.name,
          });
        });
    },

    /**
     * 获取专属导购相关信息
     * 1、查询商家是否设置名片模版
     * 2、获取专属导购信息
     */
    fetchShoppersInfo() {
      app.carmen({
        api: 'youzan.retail.sales.vcard.template.valid/1.0.0/get',
        query: {
          retail_source: 'retail',
        },
        success: (data) => {
          if (data?.value) {
            app
              .request({
                path: '/wscsalesman/getSalesmanVcardInfo.json',
                method: 'get',
                data: {},
              })
              .then((sales) => {
                if (sales?.fromParams) {
                  this.setData({
                    fromParams: sales.fromParams,
                    fromSource: sales.fromSource,
                  });
                }
              });
          }
        },
      });
    },

    // 获取店铺是否开启分销员功能
    fetchShopDistributionInfo() {
      app
        .request({
          path: 'wscump/salesman/is_distribution.json',
          method: 'GET',
        })
        .then((res = {}) => {
          const { status } = res;
          this.setData({
            isOpenDistribution: !!status,
          });
        });
    },

    handleShopperClick() {
      const { fromParams, fromSource } = this.data;
      const defineTitle = 'userCenter';
      wx.navigateTo({
        url: `/packages/business-card/detail/index?from_params=${fromParams}&from_source=${fromSource}&kdt_id=${app.getKdtId()}&define_title=${defineTitle}`,
      });
    },

    becomeMember() {
      const { originKdtId, memberSrcWay, memberSrcChannel } = this.data;
      joinMember(() => this.triggerEvent('reload'), {
        kdt_id: originKdtId || app.getKdtId(),
        member_src_way: memberSrcWay || 800,
        member_src_channel: memberSrcChannel || 1000,
        need_be_member: true,
      });
    },

    handleRechargeClick() {
      api
        .checkIsNewStoredValue({
          kdt_id: app.getHQKdtId(),
          type: 1001,
        })
        .then(
          (res) => {
            if (res.status === newStoredValueStatus) {
              wx.navigateTo({
                url: '/packages/pre-card/home/<USER>',
              });
            } else {
              wx.navigateTo({
                url: '/packages/pre-card/home/<USER>',
              });
            }
          },
          () => {
            wx.navigateTo({
              url: '/packages/pre-card/home/<USER>',
            });
          }
        );
    },

    handleSalesmanClick() {
      if (!app.getBuyerId()) {
        // 没有绑定手机号
        return wx.navigateTo({
          url: '/packages/salesman/tutorial/index',
        });
      }

      const { salesman } = this.data;
      // 若不是销售员并且没有被清退过，直接进入招募计划
      if (!salesman.salesman && !salesman.fired) {
        return wx.navigateTo({
          url: '/packages/salesman/tutorial/index',
        });
      }
      // 跳转招募计划或者销售员中心
      wx.navigateTo({
        url: `/packages/salesman/salesman-center/index?dsKdtId=${salesman.dsKdtId}`,
      });
    },

    fetchIsOpenStore() {
      app.request({
        path: 'wscump/integral/has_open.json',
        success: (res) => {
          this.setData({ hasStore: !!res.status });
        },
      });
    },

    fetchCustomerService() {
      app.carmen({
        api: 'weapp.wsc.shop.returnaddress/1.0.0/get',
        success: (res) => {
          let servicePhoneNumber = '';
          if (+res.show_notice_mobile) {
            if (res.notice_phone2) {
              servicePhoneNumber = res.notice_phone2;
              // 如果有电话前缀，就需要拼接
              if (res.notice_phone1) {
                servicePhoneNumber =
                  res.notice_phone1 + '-' + servicePhoneNumber;
              }
            } else if (res.notice_mobile) {
              servicePhoneNumber = res.notice_mobile;
            }
          }
          this.setData({ servicePhoneNumber });
        },
      });
    },

    handleContactCustomerService() {
      const { servicePhoneNumber } = this.data;
      wx.showModal({
        title: servicePhoneNumber,
        confirmText: '呼叫',
        success: (res) => {
          if (res.confirm) {
            wx.makePhoneCall({
              phoneNumber: servicePhoneNumber,
            });
          }
        },
      });
    },
    becomeMember() {
      joinMember(
        () => {
          this.onBindSuccess();
          app.logger.log({
            et: 'custom', // 事件类型
            ei: 'bind_member_success', // 事件标识
            en: '绑定会员成功', // 事件名称
            pt: 'retailshelfhome', // 页面类型
          });
        },
        {
          kdtId: app.getKdtId(),
          member_src_way: 800,
          member_src_channel: 1000,
          need_be_member: true,
        }
      );
    },
    onBindSuccess() {
      this.triggerEvent('onBindSuccess');
    },

    handleSubscribeMsg(e) {
      const { templateIds: templates, isMember } = this.data;
      if (isMember === 0) {
        return wxLogin({
          event: e.detail,
          redirectUrl: false,
          success: () => {
            this.becomeMember();
          },
        });
      }
      const hasSubscribed = app.globalData.personalCoupon;
      if (hasSubscribed) {
        this.navigateToCouponList();
      } else {
        requestSubscribeMessage({
          templates,
          onSuccess: () => {},
          onShowTips: () => {
            this.setData({
              showSubscribeMark: true,
            });
          },
          onCloseTips: () => {
            this.setData({
              showSubscribeMark: false,
            });
          },
          onComplete: () => {
            app.globalData.personalCoupon = true;
            this.navigateToCouponList();
          },
          onSelfLog: {
            subscribePos: '个人中心查看全部优惠券',
            subscribeSource: 'personal',
          },
        });
      }
    },

    navigateToCouponList() {
      wx.navigateTo({
        url: `/packages/user/coupon/list/index${
          this.data.isRetail ? '?isRetailHide=1' : ''
        }`,
      });
    },
  },
});
