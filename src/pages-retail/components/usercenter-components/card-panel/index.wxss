@import "shared/common/css/helper/index.wxss";

.dashboard-container {
  margin: 0 20px;
}

.dashboard-container__scrollx {
  margin-left: 20px;
}

.split {
  background-color: #fafafa;
  height: 10px;
  margin: 20px 0;
}

.prepaid-info,
.point-info {
  margin: 0 20px 20px 20px;
  font-weight: 500;
  border-bottom: 1rpx solid #e5e5e5;
}

.prepaid-info__title,
.point-info__title {
  margin-bottom: 10px;
  font-weight: 500;
  font-size: 16px;
}

.prepaid-info__price,
.point-info__amount {
  font-size: 24px;
  line-height: 30px;
  margin-bottom: 20px;
  color: #333;
}

.prepaid-info__price,
.point-info__amount {
  position: relative;
}

.prepaid-info__recharge,
.point-info__store {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 12px;
  color: #D40000;
  height: 30px;
  line-height: 30px;
  border: 1px solid #D40000;
  border-radius: 15px;
  width: 72px;
  text-align: center;
  box-sizing:border-box;
  background: none;
  padding: 0;
}

.scroll-disable {
  position: fixed;
  top: 0;
  bottom: 0;
}

.usercenter-cell {
  border-top: 1rpx solid #e5e5e5;
  align-items: center;
  padding: 21px 0;
  line-height: 20px;
}

.usercenter-cell::after {
  border-bottom-width: 0px;
}


.usercenter-cell .zan-cell__bd {
  font-weight: 500;
  font-size: 16px;
}

.usercenter-cell button {
  text-align: left;
  line-height: 18px;
  width: 100%;
  display: flex;
  font-size: 12px;
}

.update-text {
  font-size: 14px;
  color: #999;
}

.usercenter-hr {
  margin: 0 20px;
  height: 0;
  border-top: 1rpx solid #e5e5e5;
}

.collect-points {
  margin-left: 20px;
}

.panel-index--cell-bold {
  color: #000;
}

.cell-bold {
  font-weight:500;
  color: #000;
  font-size: 16px;
}
.van-cell__value {
  font-size: 12px;
  color: #333;
}

.usercenter-cell-icon {
  width: 15px;
  font-weight: 400;
}

.cell-index--van-cell__right-icon {
  color: #333;
  font-weight: 400;
}

.usercenter-cell-icon::after {
  position: absolute;
  top: 50%;
  right: 2px;
  content: ' ';
  height: 6px;
  width: 6px;
  border-color: #333;
  border-width: 1px 1px 0 0;
  border-style: solid;
  transform: translateY(-50%) matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
}

.van-cell::after {
  border: none;
}