import { promisifiedCarmen } from 'utils/promisified-api';

const app = getApp();

export default {
  checkIsNewStoredValue(data) {
    return promisifiedCarmen({
      api: 'youzan.cardvoucher.valuecard.subscribe/1.0.0/query',
      data
    });
  },

  // 是否屏蔽余额入口，以便商家可通过微信小程序升级审核
  checkIsInWhiteList(data) {
    return promisifiedCarmen({
      api: 'youzan.cardvoucherbiz.whiteconfig/3.0.0/searchwhiteconfig',
      data
    });
  },
  // 个人中心查询 当前集点活动记录
  queryCurrentCollectPointsActivity() {
    const hqKdtId = app.getHQKdtId();
    // 直接查总部的商品： 出了问题找产品：zhoulin
    const kdtId = app.getHQKdtId();
    const userId = app.getToken('buyerId') || app.getToken('userId');
    return app.request({
      path: '/retail/h5/miniprogram/queryCurrentCollectPointsInfo.json',
      data: {
        hqKdtId,
        kdtId,
        userId
      }
    });
  }
};
