<view class="card-info">
  <view class="card-info__title">
    集点
    <view-all url="/packages/retail/usercenter/collect-points/index" />
  </view>
  <view class="card-info__area" bindtap="toActivities">
    <view class="collect-points">
      <view class="collect-points__header">
        <view class="collect-points__header-title">{{activityName}}</view>
        <view class="collect-points__header-time">{{startTime}}-{{endTime}}</view>
      </view>
      <view class="collect-points__body">
          <image class="collect-points__body-image" src="{{goodsImg}}" />
          <view class="collect-points__body-info">
              <view class="collect-points__body-info__title">
                  {{goodsName}}
              </view>
               <view class="collect-points__body-info__points">
                  <view class="collect-points__body-info__points-count">{{costPoints}}</view>
                  <view class="collect-points__body-info__points-name">集点</view>
    
              </view>
          </view>
      </view>
      <view class="collect-points__footer">
          <view class="collect-points__footer-title">已集点 {{pointsNum}}/{{costPoints}}</view>
          <view class="collect-points__footer-points" wx:if="{{!!pointsNum}}">
            <block wx:for="{{hasPointsArr}}" wx:key="index">
                <view class="collect-points__footer-points__circle collect-points__footer-points__selected" />
            </block>
            <block wx:for="{{unPointsArr}}" wx:key="index">
                <view class="collect-points__footer-points__circle collect-points__footer-points__normal" />
            </block>
          </view>
        
    </view>
  </view>
</view>