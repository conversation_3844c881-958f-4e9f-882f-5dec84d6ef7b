import { moment as formatDate } from 'utils/time';

Component({
  properties: {
    result: Object
  },
  observers: {
    result(data) {
      try {
        const {
          pointsNum,
          activityModel: {
            costPoints,
            name,
            presentGoodsInfo = [],
            startAt,
            endAt
          } = {}
        } = data;
        const unPointsNum = costPoints - pointsNum;
        const firstGoodsInfo = presentGoodsInfo[0] || {};
        this.setData({
          pointsNum,
          costPoints,
          activityName: name,
          startTime: formatDate(startAt, 'YYYY.MM.DD'),
          endTime: formatDate(endAt, 'YYYY.MM.DD'),
          goodsName: firstGoodsInfo.goodsName,
          goodsImg: firstGoodsInfo.picture[0]?.url ?? '',
          unPointsArr: Array(unPointsNum < 0 ? 0 : unPointsNum),
          hasPointsArr: Array(pointsNum > costPoints ? costPoints : pointsNum)
        });
      } catch (error) {
        console.error(error);
      }
    }
  },
  data: {
    // 当前已集点数
    pointsNum: 0,
    // 该活动所需点数
    costPoints: 0,
    // 集点名称
    activityName: '',
    // 活动开始时间
    startTime: '',
    // 活动结束时间
    endTime: '',
    // 商品名称
    goodsName: '',
    // 商品图片
    goodsImg: '',
    // 用于：集点icon数组
    hasPointsArr: [],
    unPointsArr: []
  },
  methods: {
    toActivities() {
      wx.navigateTo({
        url: '/packages/retail/usercenter/collect-points/index'
      });
    }
  }
});
