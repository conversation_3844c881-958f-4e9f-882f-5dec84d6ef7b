.card-info {
    font-weight: 500;
    padding-bottom: 20px;
}

.card-info__title {
    margin: 20px 20px 10px 0;
    font-size: 16px;
    line-height: 20px;
}

.card-info__list--empty {
    font-size: 14px;
    line-height: 26px;
    margin-top: 10px;
    color: #999;
}

.card-info__area {
    margin-top: 20px;
    margin-right: 20px;
    .collect-points {
        min-height: 20px;
        padding: 16px 10px;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.08);
        border-radius: 8px;

        &__header {
            display: flex;
            justify-content: space-between;
            &-title {
                font-size: 14px;
                color: #332233;
                font-weight: bold;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            &-time {
                flex-shrink: 0;
                font-size: 12px;
                color: #969799;
            }
        }

        &__body {
            display: flex;
            height: 72px;
            border-radius: 4px;
            padding: 8px;
            margin: 12px 0 14px 0;
            box-sizing: border-box;
            background-color: #f7f8fa;
            &-image {
                width: 56px;
                height: 56px;
                background-color: #fff;
                flex-shrink: 0;
            }
            &-info {
                flex: 1;
                margin: 5px 0 5px 8px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                overflow: hidden;
                &__title {
                    font-size: 13px;
                    color: #323233;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-weight: normal;
                }
                &__points {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    &-count {
                        color: #ee0a24;
                        font-size: 16px;
                        font-weight: bolder;
                    }
                    &-name {
                        margin-left: 2px;
                        color: #969799;
                        font-size: 13px;
                    }
                }
            }
        }

        &__footer {
            display: flex;
            &-title {
                font-size: 12px;
                color: #332233;
                flex-shrink: 0;
                font-weight: normal;
            }
            &-points {
                display: flex;
                flex-wrap: wrap;
                &__circle {
                    width: 16px;
                    height: 16px;
                    margin-left: 16rpx;
                    margin-bottom: 16rpx;
                    background-size: 16px 16px;
                    background-position: center;
                }
                &__selected {
                    background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/selected-collect-points.svg);
                }

                &__normal {
                    background-image: url(https://su.yzcdn.cn/wsc-minapp/icon/retail/normal-collect-points.svg);
                }
            }
        }
    }
}
