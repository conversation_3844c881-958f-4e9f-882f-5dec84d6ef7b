const app = getApp();

const routeMap = [
  '/packages/trade/order/list/index',
  '/packages/retail/order/list/index'
];
Component({
  properties: {
    isRetail: {
      type: Number,
      value: 0
    },
    hasAgreeProtocol: Boolean,
  },
  data: {
    userInfo: {},
    memberData: {}
  },
  attached() {
    const { isRetail } = this.data;
    const orderData = [
      {
        type: 'topay',
        icon: 'wait-pay',
        desc: '待付款',
        count: 0,
        link: `${routeMap[isRetail]}?type=topay`
      },
      {
        type: 'tosend',
        icon: 'wait-delivery',
        desc: '待发货',
        count: 0,
        link: `${routeMap[isRetail]}?type=tosend`
      },
      {
        type: 'send',
        icon: 'has-delivered',
        desc: '已发货',
        count: 0,
        link: `${routeMap[isRetail]}?type=send`
      },
      {
        type: 'sign',
        icon: 'comment',
        desc: '待评价',
        count: 0,
        link: `${routeMap[isRetail]}?type=toevaluate`
      }
    ];

    const defaultUrl = isRetail
      ? routeMap[1]
      : `${routeMap[0]}?pagetype=single`;
    this.setData({
      orderData,
      defaultUrl
    });
  },
  methods: {
    fetchBuyerCount() {
      app.carmen({
        api: 'kdt.trade.buyer.count/1.0.2/get',
        success: res => {
          var format = num => (num > 99 ? '99+' : num);
          this.setData({
            'orderData[0].count': format(res.topay),
            'orderData[1].count': format(res.tosend),
            'orderData[2].count': format(res.send)
          });
        }
      });
    },
    init() {
      this.fetchBuyerCount();
    }
  }
});
