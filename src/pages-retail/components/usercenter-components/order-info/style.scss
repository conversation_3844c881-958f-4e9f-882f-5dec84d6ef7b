.order-info {
    margin: 0 20px;

    &__navigator {
        background-color: #fff;
        opacity: 1;
        overflow: hidden;
        margin: 20px 0;
        line-height: 30px;
    }

    &__title {
        float: left;
        font-weight: 500;
        font-size: 16px;
    }

    &__state-list {
        display: flex;
        justify-content: space-between;

        &--item {
            width: 20%;
            text-align: center;
        }

        &--icon {
            margin:0 auto;
            height: 24px;
            width: 24px;
            margin-bottom: 8px;

            &__wait-pay {
                background: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>') center no-repeat;
                background-size: 100%;
            }

            &__wait-delivery {
                background: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>') center no-repeat;
                background-size: 100%;
            }

            &__has-delivered {
                background: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>') center no-repeat;
                background-size: 100%;
            }

            &__has-done {
                background: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>') center no-repeat;
                background-size: 100%;
            }

            &__comment {
                margin-top: 2px;
                height: 22px;
                background: url('https://b.yzcdn.cn/retail/img/weapp/<EMAIL>') center no-repeat;
                background-size: 100%;
            }
        }

        &--desc {
            font-size: 14px;
        }
    }
}