<view class="order-info">
  <view hover-class="none" class="order-info__navigator">
    <view class="order-info__title">订单列表</view>
    <view-all url="{{defaultUrl}}" />
  </view>
  <view class="order-info__state-list" wx:if="{{!isRetail}}">
    <view wx:for="{{orderData}}" class="order-info__state-list--item" wx:key="index">
      <navigator url="{{ item.link }}" hover-class="none">
        <view class="order-info__state-list--icon order-info__state-list--icon__{{ item.icon }} zan-badge">
          <view wx:if="{{ hasAgreeProtocol && item.count }}" class="zan-badge__count">{{ item.count }}</view>
        </view>
        <view class="order-info__state-list--desc">{{ item.desc }}</view>
      </navigator>
    </view>
  </view>
</view>
