import api from './api';

// const app = getApp();
const IMG_URLS = {
  CHECKED: '//img01.yzcdn.cn/upload_files/2021/05/12/FrNNzqyyVNRK-QDZyG9CyarM2wlC.png',
  UNCHECKED: '//img01.yzcdn.cn/upload_files/2021/05/12/FvqqUKi11jhjFAwXl1uDZipI4OLB.png'
};

Component({
  
  data: {
    activityId: 0, 
    containerOversize: false,
    contentIconOversize: false,
    visible: true,
    IMG_URLS,
    pointsNum: 0,
    costPoints: 10,
    pointRow: [], // 每行数量，如[10, 8]第一行十个，第二行8个
    rowType: [] // 一行显示几个，一种6个大icon，另一种10个小icon
  },

  attached() {
    this.fetchData();
  },

  methods: {
    async fetchData() {
      // 不传activityId，后端会返回一个存在的进行中活动
      const { pointsNum = -1, activityModel } = await api.getRewardPointsInfo();
      // 活动已经删除的情况
      if (pointsNum === -1 && !activityModel) {
        console.log('活动已删除');
        this.setData({
          visible: false
        });
        return;
      }
      const {
        minCostPointsPresentRule: { costPoints = 10 } = {},
        receivedCollectionPointPic,
        unreceivedCollectionPointPic,
        id,
      } = activityModel || {};

      if (receivedCollectionPointPic && unreceivedCollectionPointPic) {
        IMG_URLS.CHECKED = receivedCollectionPointPic;
        IMG_URLS.UNCHECKED = unreceivedCollectionPointPic;
      }

      this.setData({
        pointsNum,
        costPoints: costPoints > 20 ? 20 : costPoints,
        activityId: id
      });
      this.calcSize(costPoints);
      this.calcPointRow(costPoints > 20 ? 20 : costPoints);
    },
    handleClick() {
      const app = getApp();
      const { activityId } = this.data;
      wx.navigateTo({
        url: `/packages/point/home/<USER>
      });
    },
    calcPointRow(costPoints) {
      const pointRow = [];
      while (costPoints > 0) {
        if (costPoints >= 10) {
          pointRow.push(10);
          costPoints -= 10;
        } else {
          pointRow.push(costPoints);
          costPoints = 0;
        }
      }
      this.setData({
        pointRow
      });
    },
    calcSize(costPoints) {
      let rowType = [];
      let containerOversize = false;
      let contentIconOversize = false;
      if (costPoints > 6) {
        rowType = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      } else {
        rowType = [1, 2, 3, 4, 5, 6];
        contentIconOversize = true;
      }
      if (costPoints > 10) {
        containerOversize = true;
      }
      this.setData({
        rowType,
        containerOversize,
        contentIconOversize
      });
    }
  }
});
