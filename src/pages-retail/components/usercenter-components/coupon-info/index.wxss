.coupon-info {
  font-weight: 500;
  margin-bottom: 20px;
}

.coupon-info__header {
  height: 20px;
  margin-right: 20px;
  line-height: 20px;
}

.coupon-info__title {
  float: left;
  margin-bottom: 10px;
  font-weight: 500;
  font-size: 16px;
}

.coupon-info__list--empty {
  color: #999;
  font-size: 14px;
  line-height: 26px;
  margin-top: 10px;
  margin-bottom: 20px;
}

.coupon-info__area {
  display: flex;
  overflow-x: scroll;
  height: 70px;
  align-items: center;
  margin-bottom: 20px;
  white-space: nowrap;
  width: 100%;
  -webkit-overflow-scrolling: touch;
}

.coupon-info__help {
  font-size: 14px;
  opacity: 0.7;
}

.coupon-info__area::-webkit-scrollbar {
  display: none;
}

.view-all {
  float: right;
  font-size: 12px;
  position: relative;
  padding-right: 26rpx;
  color: #333;
  font-weight: 400;
  background-color: #fff;
  margin-top: -10px;
}

.view-all::after {
  position: absolute;
  top: 44%;
  right: 0;
  left: auto;
  content: ' ';
  height: 15rpx;
  width: 15rpx;
  border-radius: 0;
  border-color: #333;
  border-width: 1px 1px 0 0;
  border-style: solid;
  transform: translateY(-50%) matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
}