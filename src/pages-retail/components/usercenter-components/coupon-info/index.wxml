<view class="coupon-info" wx:if="{{ hasAgreeProtocol }}">
  <view class="coupon-info__header">
    <view class="coupon-info__title">优惠券/码<text class="coupon-info__help" wx:if="{{couponTotal}}">（{{couponTotal}}张可用）</text></view>
     <view wx:if="{{isMember}}" >
      <button class="view-all" bind:tap="viewAll" >查看全部<button>
    </view>
    <view wx:else>
      <view class="view-all">
        <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="viewAll">
          查看全部
        </user-authorize>
      </view>
      <!-- <button class="view-all" bindgetphonenumber="viewAll" open-type="getPhoneNumber">查看全部<button/> -->
    </view>
  </view>
  <view class="coupon-info__list--empty" wx:if="{{couponList && couponList.length === 0}}">
    暂无可用优惠券
  </view>
  <view class="coupon-info__area" wx:else>
    <block wx:for="{{couponList}}" wx:key="index">
      <coupon-item
        value="{{item.value}}"
        title="{{item.thresholdAmountMsg}}"
        date="{{item.availableDateMsg}}"
        type="{{item.groupType}}"
        preferentialType="{{item.preferentialType}}"
        bind:tap="redirectToCouponDetail"
        data-coupon="{{ item }}"
      />
    </block>
  </view>
</view>
