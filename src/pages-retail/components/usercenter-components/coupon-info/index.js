import WscComponent from 'pages/common/wsc-component/index';
import money from '@youzan/weapp-utils/lib/money';
import navigate from '@/helpers/navigate';

const app = getApp();

WscComponent({
  properties: {
    couponList: Array,
    couponTotal: Number,
    isRetail: Boolean,
    isMember: Boolean,
    hasAgreeProtocol: Boolean,
  },

  methods: {
    init() {
      app.request({
        path: '/wscump/coupon/get_buyer_coupons_group.json',
        data: {
          status: 1,
          pageSize: 100,
          pageNo: 1
        }
      }).then(res => {
        const {
          cardsNum = 0,
          codesNum = 0,
          cards = [],
          codes = []
        } = res;
        const couponList = cards.concat(codes).map(item => {
          if (item.preferentialType === 1) {
            item.value = money().adjustFixed(+parseFloat(item.value), 0);
          }
          return item;
        });
        this.setYZData({
          couponTotal: cardsNum + codesNum,
          couponList
        });
      });
    },

    redirectToCouponDetail(data) {
      const {
        currentTarget: {
          dataset: {
            coupon: {
              couponId,
              groupType
            }
          }
        }
      } = data;
      navigate.navigate({
        url: `/packages/user/coupon/detail/index?id=${couponId}&from=list&type=${groupType}${this.data.isRetail ? '&isRetailHide=1' : ''}`
      });
    },

    viewAll(e) {
      this.triggerEvent('subscribeMsg', e);
    }
  }
});
