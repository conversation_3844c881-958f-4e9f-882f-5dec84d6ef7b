import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  properties: {
    cardList: Array,
    hasAgreeProtocol: <PERSON><PERSON>an,
    pointsName: {
      type: String,
      value: '积分'
    }
  },

  methods: {
    init() {
      app.carmen({
        api: 'youzan.retail.scrm.customer.benefit.cards/1.0.0/get',
        success: cardList => {
          cardList.map(card => {
            card.desc = this.handleCardDesc(card);
            return card;
          });
          this.setYZData({
            cardList
          });
        },
        fail: res => {
          wx.showToast({
            title: (res && res.msg) || '网络错误',
            icon: 'none'
          });
        }
      });
    },

    handleCardDesc(card) {
      const BENEFIT_MAP = {
        freePost: '包邮',
        discount: '折',
        pointRate: `倍${this.data.pointsName}回馈`
      };

      const { free_post: freePost, discount, point_rate: pointRate } = card;
      const desc = [];

      if (freePost) {
        desc.push(BENEFIT_MAP.freePost);
      }
      if (discount) {
        desc.push(`${discount / 10}${BENEFIT_MAP.discount}`);
      }
      if (pointRate) {
        desc.push(`${pointRate / 10}${BENEFIT_MAP.pointRate}`);
      }
      return desc.join('、');
    },

    handleCardDetail(event) {
      const { alias } = event.currentTarget.dataset;
      wx.navigateTo({
        url: `/packages/benefit-card/detail/index?alias=${alias}`
      });
    }
  }
});
