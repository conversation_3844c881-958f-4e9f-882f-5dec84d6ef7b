<view class="card-info" wx:if="{{ hasAgreeProtocol }}">
  <view class="card-info__title">
    权益卡
    <text class="card-info__help" wx:if="{{cardList && cardList.length}}">（{{cardList.length}}张可用）</text>
    <view-all url="/packages/benefit-card/list/index" />
  </view>
  <view wx:if="{{cardList && cardList.length === 0}}" class="card-info__list--empty">
    暂无可用权益卡
  </view>
  <view wx:else class="overflow-auto">
    <view class="card-info__area">
      <block wx:for="{{cardList}}" wx:key="index">
          <card-item
            catchtap="handleCardDetail"
            data-alias="{{item.card_alias}}"
            name="{{item.name}}"
            desc="{{item.desc}}"
            color-code="{{item.color_code}}"
            cover-url="{{item.cover_url}}"
          />
      </block>
    </view>
  </view>
</view>
