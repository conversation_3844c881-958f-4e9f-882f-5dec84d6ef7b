import money from '@youzan/weapp-utils/lib/money';
import {
  notifyOfflineMemberSignFailed,
  signSceneCallback
} from 'retail/util/bind-mobile';
import { queryLevelInfo } from './api';

const app = getApp();

Component({
  properties: {
    isRetail: Number,
    isSign: Boolean,
    originKdtId: Number,
    memberSrcWay: Number,
    memberSrcChannel: Number,
    hasAgreeProtocol: Boolean
  },

  data: {
    showQrcodePopup: false,
    isMember: false,
    fetching: true,
    mobile: '',
    showBindPhoneNumber: null,
    balance: '更新中',
    points: '更新中',
    memberInfo: {},
    paidLevelInfo: {}
  },

  attached() {
    this.cardInfo = this.selectComponent('#card-panel >>> #cardInfo');
    this.couponInfo = this.selectComponent('#card-panel >>> #couponInfo');
    this.orderInfo = this.selectComponent('#orderInfo');
    this.cardPanel = this.selectComponent('#card-panel');
    this.hadHide = false;
    this.fetchUserInfo();
  },

  pageLifetimes: {
    show() {
      this.hadHide && this.fetchUserInfo();
    },
    hide() {
      this.hadHide = true;
    }
  },

  methods: {
    refresh() {
      this.fetchUserInfo();
      this.cardInfo.init();
      this.couponInfo.init();
      this.orderInfo.init();
      this.cardPanel.init();
    },

    onBindSuccess({ detail }) {
      this.setData({
        showBindPhoneNumber: false,
        mobile: detail.mobile
      });
    },

    bindSuccessCallback() {
      this.fetchUserInfo();
      this.couponInfo.init();
      this.cardInfo.init();
      this.orderInfo.init();
    },

    fetchUserInfo() {
      const signQrcodeKey = app.globalData.signQrcodeKey;
      const isFirstSign = app.globalData.fristSign;

      const defaultParams = {
        need_query_points: true,
        need_query_balance: true,
        need_query_member_benefits: true,
        need_query_growth_value: true
      };

      if (isFirstSign && signQrcodeKey) {
        defaultParams.qr_code = signQrcodeKey;
        defaultParams.member_src_way = 800;
        defaultParams.member_src_channel = 1000;
        defaultParams.kdt_id = app.globalData.signKdtId || app.getKdtId();
      }

      app.carmen({
        api: 'youzan.retail.scrm.customer.assets/1.0.0/get',
        data: defaultParams,
        success: (memberInfo = {}) => {
          queryLevelInfo().then(res => this.setData({ paidLevelInfo: res }));

          const {
            asset_info: assetInfo = {},
            customer_info: customerInfo = {},
            user_info: userInfo = {},
            // member_level_right: memberLevelRight = {},
            is_login_cash: isLoginCashier
          } = memberInfo;

          let points = assetInfo.current_points;

          if (typeof points === 'undefined') {
            points = '更新中';
          }

          const balance = assetInfo.stored_balance_value;
          const isMember = customerInfo.is_member || 0;
          const mobile = userInfo.mobile || '';
          const nickName = userInfo.nick_name || '';

          if (isLoginCashier === false) {
            notifyOfflineMemberSignFailed();
          }

          const showBindPhoneNumber = !mobile;

          this.setData({
            memberInfo,
            points,
            balance:
              /* eslint no-nested-ternary: 'off' */
              !mobile && balance === undefined
                ? '查看储值余额，请先绑定手机'
                : balance === undefined
                ? '更新中'
                : money(balance).toYuan(),
            showBindPhoneNumber,
            mobile,
            isMember,
            nickName
          });

          wx.hideToast();

          if (isMember && isFirstSign && signQrcodeKey && isLoginCashier) {
            signSceneCallback('cashier');
          }
        },
        fail: res => {
          wx.hideToast();
          wx.showToast({
            title: (res && res.msg) || '网络错误',
            icon: 'none'
          });
        },
        complete: () => {
          this.setData({
            fetching: false
          });
          wx.stopPullDownRefresh();
        }
      });
    }
  }
});
