<view>
	<user-info
	 class="dashboard-container"
	 showBindPhoneNumber="{{showBindPhoneNumber}}"
	 mobile="{{mobile}}"
	 originKdtId="{{originKdtId}}"
	 memberSrcWay="{{memberSrcWay}}"
	 memberSrcChannel="{{memberSrcChannel}}"
	 bind:accountSuccess="onBindSuccess"
	 bind:reload="bindSuccessCallback"
	/>
	<member-card bind:onBindSuccess="fetchUserInfo" memberInfo="{{ hasAgreeProtocol ? memberInfo : null }}" paidInfo="{{ hasAgreeProtocol ? paidLevelInfo : null }}" />
	<order-info
	 wx:if="{{ !isSign }}"
	 id="orderInfo"
	 is-retail="{{isRetail}}"
	 has-agree-protocol="{{ hasAgreeProtocol }}"
	/>
</view>

<view class="split" />

<card-panel
 id="card-panel"
 mobile="{{mobile}}"
 balance="{{balance}}"
 points="{{points}}"
 is-retail="{{isRetail}}"
 has-agree-protocol="{{ hasAgreeProtocol }}"
 bind:reload="bindSuccessCallback"
 bind:onBindSuccess="fetchUserInfo"
 originKdtId="{{originKdtId}}"
 memberSrcWay="{{memberSrcWay}}"
 memberSrcChannel="{{memberSrcChannel}}"
 isMember="{{isMember}}"
/>

