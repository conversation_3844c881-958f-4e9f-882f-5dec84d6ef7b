import accDiv from '@youzan/utils/number/accDiv';
// 优惠方式 1：代金券 2：折扣券 3：兑换券 4：买赠券
const PreferentialModeEnum = {
  Num: 1,
  Discount: 2,
  Exchange: 3,
  Send: 4,
};

// 优惠券面额生成规则类型 1: 固定值 2: 指定值 3: 范围随机值 4: 无指定值
const BenefitNumTypeEnum = {
  Fixed: 1,
  Assigned: 2,
  Random: 3,
  NoAssigned: 4,
};
export function formatCouponText(coupon) {
  if (!coupon) {
    return '优惠券';
  }
  const {
    preferentialMode,
    voucherValueGenerateType,
    value,
    preferentialCopywriting,
    title,
  } = coupon;
  const preferentialType = Number(preferentialMode);
  let tempValue;
  const suffix = '优惠券';
  if (preferentialType === PreferentialModeEnum.Num) {
    if (voucherValueGenerateType === BenefitNumTypeEnum.Random) {
      return `随机金额${suffix}`;
    }
    tempValue = value ? accDiv(value, 100) : '';

    return `${tempValue}元${suffix}`;
  }

  if (preferentialType === PreferentialModeEnum.Discount) {
    tempValue = value ? accDiv(value, 10) : '';

    return `${tempValue}折${suffix}`;
  }

  if (preferentialType === PreferentialModeEnum.Exchange) {
    return `兑换商品${suffix}`;
  }
  if (preferentialType === PreferentialModeEnum.Send) {
    return `${preferentialCopywriting.split('，')[1] || '买赠'}${suffix}`;
  }
  return title;
}
const memberPrefix = '会员';
const newMemberPrefix = '新会员';
export function formatBenefitsTextForBenefitsData(benefitsData = {}) {
  const {
    presentList,
    couponList,
    discount,
    points,
    postage,
    memberPrice,
    pointsFeedBack,
    pointsName,
    diyTemplateList,
  } = benefitsData;
  const presentsNum = presentList?.length || 0;
  const couponsNum = couponList?.length || 0;
  const discountValue = discount?.discount;
  const pointsValue = points?.points;
  const pointsRate = pointsFeedBack?.rate;
  const diyTemplateNum = diyTemplateList?.length;

  // 礼包：优先取赠品
  if (presentsNum > 0) {
    if (presentsNum > 1) {
      return {
        prefix: newMemberPrefix,
        text: `送${presentsNum}种赠品`,
      };
    }
    if (presentsNum === 1) {
      return {
        prefix: newMemberPrefix,
        text: `送${presentList[0].presentName}`,
      };
    }
  }
  // 权益：其次取会员折扣
  if (discountValue) {
    return {
      prefix: memberPrefix,
      text: `享${discountValue / 10}折`,
    };
  }

  // 权益：后取会员价活动
  if (memberPrice) {
    return {
      prefix: memberPrefix,
      text: `享会员价`,
    };
  }

  // 礼包：后取优惠券
  if (couponsNum > 0) {
    if (couponsNum > 1) {
      const total = couponList.reduce(
        (prev, coupon) => prev + (coupon.number || 0),
        0
      );
      return {
        prefix: newMemberPrefix,
        text: `享${total}张优惠券`,
      };
    }
    if (couponsNum === 1) {
      const coupon = couponList[0];
      const couponDisplayText = formatCouponText(coupon);
      if (coupon.number === 1) {
        return {
          prefix: newMemberPrefix,
          text: `享${couponDisplayText}`,
        };
      }

      if (coupon.number > 1) {
        return {
          prefix: newMemberPrefix,
          text: `享${coupon.number}张${couponDisplayText}`,
        };
      }
    }
  }
  // 礼包：后取积分
  if (pointsValue) {
    return {
      prefix: memberPrefix,
      text: `享${pointsValue}${pointsName || '积分'}`,
    };
  }

  // 权益： 取积分倍率
  if (pointsRate) {
    return {
      prefix: memberPrefix,
      text: `享${accDiv(pointsRate, 10)}倍${pointsName || '积分'}`,
    };
  }
  // 权益：取包邮
  if (postage) {
    return {
      prefix: memberPrefix,
      text: `享包邮`,
    };
  }

  // 权益：商家自定义权益
  if (diyTemplateNum) {
    return {
      prefix: memberPrefix,
      text: '享丰富会员权益',
    };
  }
  // 默认文案
  return {
    prefix: '',
    text: '登录查看资产/订单等信息',
    noBenefits: true,
  };
}
