<!--扫码点单储值卡选择弹框-->
<van-popup
  overlay
  position="bottom"
  show="{{ showState }}"
  custom-class="value-card-popup"
  close-on-click-overlay="{{false}}"
  safe-area-inset-bottom="{{false}}"
  bind:close="closePopup"
>
  <view class="value-card-popup__title">
    <van-icon
      wx:if="{{activeIndex === 1 && !toRecharge}}"
      name="arrow-left"
      size="12px"
      custom-class="prepay-card__pop-left"
      bind:tap="onLeft"
    />
    <text class="value-card-popup__title-text">{{ activeIndex === 0 ? '请选择余额或卡' : '选择一个礼包' }}</text>
  </view>
  <view class="recommend-content-swipper">
    <view class="recommend-content {{activeIndex === 0 ? 'content-translateX0' : 'content-translateX100'}}">
      <view class="recommend-content-swipper_li">
        <van-tabs wx:if="{{showTabs}}" active="{{active}}">
          <van-tab title="可用">
            <scroll-view scroll-y class="value-card-popup__content">
              <block wx:if="{{usableStoredValue.length || usableStoredCard.length}}">
                <view wx:if="{{usableStoredValue.length}}">
                  <view>余额</view>
                  <block wx:for="{{usableStoredValue}}" wx:key="summaryCardNo">
                    <stored-value-card
                      card="{{item}}"
                      disabled="{{item.balance==0}}"
                      bind:changeSelected="handleChangeSelected"
                      defaultSelected="{{util.getSelected(defaultSelectCard, item)}}"
                      card-type="storedValue"
                    />
                  </block>
                </view>
                <!-- 充值推荐 -->
                <!-- <recharge-recommend
                  wx:if="{{recommendDetail.desc}}"
                  valueCard="{{usableStoredValue}}"
                  bind:onRecharge="onRecharge"
                  recommend-detail="{{ recommendDetail }}"
                  product="{{product}}"
                /> -->
                <view wx:if="{{usableStoredCard.length}}">
                  <view>卡</view>
                  <block wx:for="{{usableStoredCard}}" wx:key="summaryCardNo">
                    <stored-value-card
                      card="{{item}}"
                      defaultSelected="{{util.getSelected(defaultSelectCard, item)}}"
                      bind:changeSelected="handleChangeSelected"
                      card-type="storedCard"
                    />
                  </block>
                </view>
              </block>

              <block wx:else>
                <template is="empty" />
              </block>

            </scroll-view>
          </van-tab>
          <van-tab title="不可用">
            <block wx:if="{{unusableStoredValue.length || unusableStoredCard.length}}">
              <view wx:if="{{unusableStoredValue.length}}" style="padding:10px">
                余额
                <block wx:for="{{unusableStoredValue}}" wx:key="summaryCardNo">
                  <stored-value-card card="{{item}}" disabled card-type="storedValue" />
                </block>
              </view>
              <view wx:if="{{unusableStoredCard.length}}" style="padding:10px">
                卡
                <block wx:for="{{unusableStoredCard}}" wx:key="summaryCardNo">
                  <stored-value-card card="{{item}}" disabled card-type="storedCard" />
                </block>
              </view>
            </block>
            <block wx:else>
              <template is="empty" />
            </block>
          </van-tab>
        </van-tabs>
        <view class="value-card-popup__bottom">
          <view class="value-card-popup__submit">
            <view class="value-card-popup__submit-text">
              <text>可抵扣：</text>
              <price price="{{ discountPrice }}" style="--price-color:{{ themeMainColor }}" />
            </view>
            <btn
              custom-class="value-card-popup__submit-btn {{submitDisabled || valueCardBtnDisabled ? 'value-card-popup__submit-btn-disabled' : ''}}"
              round
              hairline
              type="info"
              disabled="{{submitDisabled || valueCardBtnDisabled}}"
              loading="{{submitDisabled || valueCardBtnDisabled}}"
              bind:click="handleSubmit"
            >
              完成
            </btn>
          </view>
          <view style="height: 34px; background: white" wx:if="{{isiPhoneX}}" />
        </view>
      </view>
      <!-- 切换礼包 -->
      <!-- <view class="recommend-content-swipper_li">
        <gift-pack
          id="prepaidRecommendGiftPack"
          recommend-detail="{{ recommendDetail }}"
          value-card="{{valueCard}}"
          product="{{ product }}"
          bind:onClose="onClose"
          bind:startCashierPay="startPay"
          themeMainColor="{{themeMainColor}}"
        />
      </view> -->
    </view>
  </view>
</van-popup>
<!-- 储值推荐 -->
<!-- <pay-submit-prepaid themeColor="{{themeMainColor}}" id="payService" scene="{{scene}}" /> -->

<template name="empty">
  <view class="value-card-popup__empty">
    <image class="value-card-popup__empty-img" src="https://img01.yzcdn.cn/upload_files/2021/05/25/Fukq3YsmMep2SuYUEJ5ICMoBePTs.png" />
    <view class="value-card-popup__empty-text">暂无余额或卡</view>
  </view>
</template>

<wxs module="util">
  function getSelected(cards, card) {
  return cards.some(function(item) {
  return item.summaryCardNo === card.summaryCardNo
  });
  }
  module.exports = { getSelected: getSelected };
</wxs>
<wxs src="retail/util/money.wxs" module="money" />


