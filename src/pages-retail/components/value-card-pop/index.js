import { getThemeMainColor } from 'retail/util/shelf-global';
import {
  loggerGoBack,
  loggerOnRecharge,
  loggerStartPay,
  loggerOnClose,
  loggerPaySuccess,
  loggerPayFail,
  loggerClosePayList,
} from 'retail/util/prepaid-recommend-logger/logger';
import { isNewIphone } from 'shared/utils/browser/device-type';
// eslint-disable-next-line import/no-extraneous-dependencies

const app = getApp();

Component({
  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer(value) {
        if (value) this.initData();
        this.setData({ showState: value });
        // 用 wx:if 的方法不行，还是会出现位置错误的问题，暂时用这个方法处理
        setTimeout(() => {
          this.setData({
            showTabs: value,
          });
        });
      },
    },
    storedValueAndCard: {
      type: Object,
      value: {},
      observer(value) {
        if (value && (value.usable || value.unusable)) {
          this.setData({
            usableStoredValue: [...value.usable.storedValue],
            usableStoredCard: [...value.usable.storedCard],
            unusableStoredValue: [...value.unusable.storedValue],
            unusableStoredCard: [...value.unusable.storedCard],
          });
        }
      },
    },
    defaultSelectCard: {
      type: Array,
      value: [],
    },
    totalPrice: {
      type: Number,
    },
    // 是否直接充值
    toRecharge: {
      type: Boolean,
      default: false,
    },
    // 充值推荐规则
    recommendDetail: {
      type: Object,
      value: {},
    },
    /** 储值卡勾选的时候和点完成的时候都会请求confirm接口，点击过快会出现接口时序问题 */
    valueCardBtnDisabled: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    isiPhoneX: isNewIphone(),
    showState: false,
    showTabs: false,
    active: 0,
    themeMainColor: getThemeMainColor(),
    discountPrice: 0,
    submitDisabled: false,
    usableStoredValue: [],
    usableStoredCard: [],
    unusableStoredValue: [],
    unusableStoredCard: [],
    showTips: false,

    activeIndex: 0,
    product: {},
    scene: app.globalData.scene,
  },
  attached() {
    this.setData({
      scene: app.globalData.scene,
    });
  },
  methods: {
    initData() {
      const { defaultSelectCard } = this.data;
      let sum = 0;
      const map = {};
      defaultSelectCard.forEach((item) => {
        sum += item.balance;
        map[item.summaryCardNo] = item;
      });
      this.selectedCard = { ...map };
      this.setData({
        discountPrice: sum,
      });
    },

    handleChangeSelected({ detail }) {
      const { selected, card } = detail;
      let { discountPrice } = this.data;
      // 计算抵扣价格
      if (selected) {
        this.selectedCard[card.summaryCardNo] = card;
        discountPrice += card.balance;
      } else {
        delete this.selectedCard[card.summaryCardNo];
        discountPrice -= card.balance;
      }

      this.setData({
        discountPrice,
        submitDisabled: true,
        showTips: false,
      });
      this.triggerEvent('valueCardChange', {
        cards: { ...this.selectedCard },
        callback: () => {
          this.setData({
            submitDisabled: false,
          });
        },
      });
    },
    handleSubmit(env) {
      if (this.data.submitDisabled) {
        return;
      }
      const prams = {
        cards: { ...this.selectedCard },
      };
      this.triggerEvent('valueCardSubmit', prams);
      this.closePopup(env !== 'pay');
    },
    closePopup(flag) {
      this.setData({
        showState: false,
        discountPrice: 0,
      });
      setTimeout(() => {
        this.onLeft('close');
      }, 1000);
      this.triggerEvent('valueCardClose', flag);
    },
    onLeft(flag) {
      // 点击返回打点
      flag !== 'close' && loggerGoBack();
      this.setData({
        activeIndex: 0,
      });
    },
    // 关闭
    onClose(flag) {
      const { activeIndex } = this.data;
      if (flag !== 'pay' && activeIndex === 1) {
        loggerOnClose();
      }
      // 支付时关闭弹窗
      if (flag === 'pay') {
        this.handleSubmit('pay');
      }
    },
    // toast
    showFail(title, duration = '2000') {
      wx.hideLoading();
      wx.showToast({
        title,
        icon: 'none',
        duration,
      });
    },
  },
});
