.value-card-popup {
  display: flex;
  flex-direction: column;
  border-radius: 40rpx 40rpx 0 0;
  height: 80% !important;
  background-color: #f2f3f5 !important;
  &__title {
    position: relative;
    height: 88rpx;
    line-height: 88rpx;
    padding: 0 32rpx;
    color: #333;
    font-size: 32rpx;
    font-weight: 500;
    text-align: center;
    background-color: #fff;
    &-icon {
      position: absolute !important;
      right: 32rpx;
      top: 20rpx;
    }
  }

  &__content {
    box-sizing: border-box;
    padding: 24rpx 24rpx 140rpx 24rpx;
    height: calc(80vh - 120px);
  }

  &__bottom {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99;
  }

  &__tips {
    height: 80rpx;
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    background-color: #fffbe8;
    &-text {
      font-size: 24rpx;
      color: #ed6a0c;
    }
  }

  &__submit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100rpx;
    padding: 0 32rpx;
    background-color: #fff;
    box-shadow: 0 4rpx 20rpx rgba(125, 125, 125, 0.16);
    &-text {
      display: flex;
    }
    &-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 192rpx;
      height: 80rpx;
      border: none !important;
      font-size: 28rpx;
      color: #fff;
      background: var(--theme-main-color) !important;
    }
    .value-card-popup__submit-btn-disabled {
      background-color: #c8c9cc !important;
      opacity: 1;
    }
  }
}

.value-card-popup__empty {
  display: flex;
  flex-direction: column;
  align-items: center;

  &-img {
    width: 160px;
    height: 160px;
    margin-top: 20px;
  }

  &-text {
    font-size: 14px;
    text-align: center;
    color: #969799;
    margin-top: 16px;
  }
}

.recommend-content-swipper{
  overflow: hidden;
  height: 100%;
}

.recommend-content-swipper_li{
  width: 100%;
  flex: none;
}

.recommend-content {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  transition: transform 0.3s cubic-bezier(0.84, 0.84, 0.32, 0.32);
}

.content-translateX0 {
  transform: translateX(0);
}

.content-translateX100 {
  transform: translateX(-100%);
}

.prepay-card__pop-left {
  position: absolute !important;
  top: 12px;
  left: 0;
  padding: 0 12px;
  color: #969799;
  font-size: 22px !important;
  line-height: 28px;
  text-align: center;
}