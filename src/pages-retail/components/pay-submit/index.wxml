<view class="pay-submit-container" bind:tap="handleTapContainer" style="opacity: {{hidden ? 0 : 1}}; transition: opacity 0.3s ease-in-out; will-change: opacity; pointer-events: {{hidden ? 'none' : 'auto'}} ">
  <slot name="header" />
  <view class="pay-submit space-between">
    <block wx:if="{{payInfo.show}}">
      <view class="pay-submit__left">
        <text class="pay-submit__label">
          {{payInfo.label}}
          <text class="pay-submit__price">¥ {{payInfo.amount}}</text>
        </text>
        <text style="font-size:10px" class="pay-submit__discount">{{payInfo.desc}}</text>
      </view>
      <btn custom-class="pay-button recharge" plain hairline round bind:click="emitPay" data-recommend>
        {{payInfo.button}}
      </btn>
      <!-- 这里还是用emitPay 但是要告诉pay是储值推荐触发的 -->
    </block>
    <block wx:else>
      <view class="pay-submit__text" wx:if="{{ showPrice }}">
        <slot name="left-icon" wx:if="{{ hasIcon }}" />
        <text class="pay-submit__label" wx:else>合计:</text>
        <text class="pay-submit__label notice" style="color: #c8c9cc" wx:if="{{originPrice === 0 && price === 0 && isEmptyCart}}">
          未选购商品
        </text>
        <view wx:else>
          <price origin-price="{{originPrice}}" price="{{price}}" style="--price-color: var(--theme-main-color, #f44)" />
          <text wx:if="{{ supportAddMeal }}" class="pay-submit__text-extra">优惠券/积分/储值请在支付页使用</text>
        </view>
      </view>
      <view class="pay-submit__action" catch:tap="noop">
        <view wx:if="{{ smallPayButton }}" class="left-money">{{ money }}</view>
        <btn wx:if="{{ showAppend }}" custom-class="pay-button add {{ smallPayButton ? 'small' : '' }}" plain hairline round type="info" style="margin-right: 12rpx;" bind:click="handleAddMeal">
          加菜
        </btn>
        <btn wx:elif="{{ showCancel }}" custom-class="pay-button cancel {{ smallPayButton ? 'small' : '' }}" plain hairline round type="info" bind:click="cancelOrder" loading="{{isCancel}}" style="margin-right: 12rpx;">
          取消订单
        </btn>
        <user-authorize wx:if="{{ !hasAuthMobile }}" authTypeList="{{ ['mobile'] }}" scene="click_buy_now" bind:next="emitPay" bind:fail="emitPay">
          <btn wx:if="{{!hiddenPay}}" round hairline type="info" custom-class="pay-button {{ showPrice ? '' : 'larger' }} {{ supportAddMeal ? 'long' : '' }} {{ smallPayButton ? 'small' : '' }}" disabled="{{disabled}}" loading="{{isPaying}}">
            <slot />
          </btn>
        </user-authorize>
        <btn wx:elif="{{!hiddenPay}}" round hairline type="info" custom-class="pay-button {{ showPrice ? '' : 'larger' }} {{ supportAddMeal ? 'long' : '' }} {{ smallPayButton ? 'small' : '' }}" disabled="{{disabled}}" loading="{{isPaying}}" bind:tap="emitPay">
          <slot />
        </btn>
      </view>
    </block>
  </view>
  <view style="height: 34px; background: white" wx:if="{{isiPhoneX}}" />
</view>
<pay-view id="payViewRef" order-no="{{ orderNo }}" prepay="{{ isPrepay }}" prepay-success="{{ isPrepaySuccess }}" prepay-params="{{ realOrderPayResult }}" zero-order="{{ isZeroOrder }}" catch:get-pay-ways="getPaywaysDone" catch:paid-success="onPaidSuccess" catch:pay-fail="onPayFail" bind:closePayPays="onClose" />