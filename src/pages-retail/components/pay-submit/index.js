import promisify from 'utils/promisify';
import {
  MEMBER_STORAGE_KEY,
  saveMemberInfoToCache,
} from 'retail/util/bind-member';
import retailCarmen from 'retail/util/retail-carmen';
import iphonex from 'retail/util/iphonex-behavior';

const orderDetailRoute = '/packages/retail/order/detail/index';
const HAS_AUTH_MOBILE = 'retail_has_auth_mobile_decide';

// eslint-disable-next-line no-unused-vars
const getStorage = promisify(wx.getStorage);
const app = getApp();

Component({
  behaviors: [iphonex],
  options: {
    multipleSlots: true,
  },
  properties: {
    justUI: Boolean,
    isSkipPay: Boolean,
    showCancel: Boolean,
    showAppend: Boolean,
    hasIcon: Boolean,
    disabled: Boolean,
    showPrice: {
      type: Boolean,
      value: true,
    },
    hiddenPay: {
      type: Boolean,
      value: false,
    },
    // 用来支付的储值卡卡号
    valueCardNo: {
      type: Array,
      value: [],
    },
    // 是否是空购物车
    isEmptyCart: {
      type: Boolean,
      value: true,
    },
    price: {
      type: Number,
      value: 0,
    },
    originPrice: {
      type: Number,
      value: 0,
    },
    cardPricePay: {
      // 余额卡支付
      type: Number,
      value: 0,
    },
    orderNo: String,
    orderPayResult: {
      type: Object,
      observer: 'observeOrderStatusChanged',
    },
    hidden: Boolean,
    // 是否支持加餐
    supportAddMeal: Boolean,
    money: String,
    smallPayButton: Boolean,
    // 储值推荐
    payInfo: {
      type: Object,
      value: {},
    },
  },
  data: {
    isPrepay: false,
    isPrepaySuccess: false,
    isZeroOrder: false,
    isPaying: false,
    isCancel: false,
    // 过滤掉储值卡支付
    realOrderPayResult: { scene: 'VALUE_COMMON' },
    hasAuthMobile: false,
  },
  attached() {
    this.isMember = false;
    getStorage({
      key: HAS_AUTH_MOBILE,
    }).then((res) => {
      this.setData({
        hasAuthMobile: res.data,
      });
    });
  },
  methods: {
    noop() {},

    async getMember() {
      try {
        const { data } = await getStorage({ key: MEMBER_STORAGE_KEY });
        if (typeof data === 'object') {
          const { expired = 0, isMember } = data;
          if (expired <= Date.now()) {
            throw Error();
          }
          this.isMember = isMember;
        } else {
          // 历史兼容
          throw Error();
        }
      } catch (err) {
        const {
          customer_info: { is_member: isMember = false },
        } = await retailCarmen({
          api: 'youzan.retail.scrm.customer.assets/1.0.0/get',
        });
        if (isMember) {
          saveMemberInfoToCache();
          this.isMember = true;
        }
      }
    },

    handleTapContainer() {
      this.triggerEvent('touch');
    },

    emitPay(detail) {
      if (detail) {
        const { dataset } = detail.currentTarget || {};
        const { recommend = false } = dataset || {};

        // 储值推荐的主按钮事件统一交给父页面处理
        if (recommend) {
          return this.triggerEvent('store');
        }
      }

      app.logger.skynetLog({
        appName: 'retail-node-h5',
        message: `[retail-buy] 扫码点单下单手机号授权状态: ${
          detail?.type || '不用授权'
        }`,
        level: 'info',
      });

      wx.setStorage({
        key: HAS_AUTH_MOBILE,
        data: true,
      });

      const {
        orderPayResult,
        valueCardNo,
        justUI,
        price,
        cardPricePay,
        disabled,
        isPaying,
      } = this.data;
      if (disabled || isPaying) {
        return;
      }

      if (orderPayResult && !orderPayResult.error) {
        // ** 开单完成 **
        // 支付余额为0, 走储值支付
        if (valueCardNo.length && !price && cardPricePay) {
          // 使用储值卡前置支付
          this.payByValueCard();
          return;
        }

        // 调用中台收银台支付
        return this.startPay();
      }

      if (!justUI) {
        this.setData({
          isPrepay: true,
          isPaying: true,
        });
      }

      // 调用父组件进行开单
      this.triggerEvent('pay');
    },

    async handlePay() {
      await this.getMember();

      if (this.isMember && app.getBuyerId() && app.getMobile()) {
        // 个人授权信息全部完成后 才能进行支付
        return this.emitPay();
      }
    },

    maybeError(error) {
      if (error?.error) {
        this.setData({
          isPrepay: false,
          isPaying: false,
        });
        wx.showToast({
          title: error.error || '订单创建失败，请重试',
          icon: 'none',
          duration: 2000,
        });
        return true;
      }
    },

    // FIXME: maybe we don't need this.
    onClose() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1]._route;
      if ('/' + currentPage !== orderDetailRoute) {
        return wx.redirectTo({
          url: `${orderDetailRoute}?orderNo=${this.data.orderNo}`,
        });
      }
    },

    observeOrderStatusChanged(payResult) {
      const hasError = this.maybeError(payResult);
      if (hasError) {
        return;
      }
      const {
        realOrderPayResult,
        orderNo,
        price,
        isSkipPay,
        valueCardNo,
        cardPricePay,
      } = this.data;

      if (orderNo) {
        this.setData({
          realOrderPayResult: { ...realOrderPayResult, ...payResult },
          isPrepaySuccess: true,
          isZeroOrder: !price,
        });

        if (
          valueCardNo.length &&
          !price &&
          cardPricePay &&
          payResult.isNeedStoreCardPay
        ) {
          // 使用储值卡前置支付
          this.payByValueCard();
          return;
        }

        if (isSkipPay) {
          return wx.redirectTo({
            url: `${orderDetailRoute}?orderNo=${orderNo}`,
          });
        }

        // 调用中台收银台支付
        this.startPay();
      }
    },

    // 通过储值卡支付
    payByValueCard() {
      const { valueCardNo, orderNo, price, cardPricePay } = this.data;

      if (valueCardNo.length && !price && cardPricePay) {
        // 储值卡支付
        return app
          .retailRequest({
            path: '/retail/h5/trade/storeCardPayForNode.json',
            method: 'POST',
            data: {
              useMinKdt: true,
              minKdtId: app.getOfflineKdtId(),
              outBizNo: orderNo,
              buyerId: app.getBuyerId(),
              payAmount: cardPricePay,
              storeCardNos: valueCardNo.map((item) => item.summaryCardNo),
            },
          })
          .then(() => {
            this.onPaidSuccess();
            app.logger &&
              app.logger.log({
                et: 'click', // 事件类型
                ei: 'value_card_pay', // 事件标识
                en: '储值卡前置支付', // 事件名称
                pt: 'retailfreebuy', // 页面类型
                params: {}, // 事件参数
              });
          })
          .catch((e) => {
            this.handlePayFail('value_card_pay', e);
            wx.showToast({
              icon: 'none',
              title: e?.msg ?? '支付发生问题，请重试!',
            });
          });
      }

      return Promise.reject('valueCardNo不能为空');
    },

    async cancelOrder() {
      this.triggerEvent('cancel', { orderNo: this.data.orderNo });
    },

    startPay() {
      const payWaysRef = this.selectComponent('#payViewRef');

      if (payWaysRef) {
        try {
          payWaysRef.startPay();
        } catch (e) {
          this.handlePayFail('startPay', e);
        }
      } else {
        this.handlePayFail('payWaysRef', { message: 'payWaysRef不存在' });
      }
    },

    onPaidSuccess() {
      wx.vibrateShort({ success: () => wx.vibrateShort() });
      this.triggerEvent('paid-success', { orderNo: this.data.orderNo });
    },

    getPaywaysDone() {
      this.setData({ isPaying: false });
    },
    onPayFail(e) {
      this.handlePayFail('payViewFail', { message: e.detail });
    },

    handlePayFail(from, e = {}) {
      this.setData({ isPaying: false });
      app.logger.skynetLog({
        appName: 'retail-node-h5',
        message: `[retail-buy] [pay-submit] 扫码点单下单失败-${from}`,
        level: 'error',
        detail: {
          errMessage: e.message || e.msg || '',
        },
      });
    },

    cancelPay() {
      this.setData({ isPaying: false });
    },

    // 加餐，跳转到商品列表
    handleAddMeal() {
      wx.redirectTo({
        url: '/packages/retailb/goodslist/index',
      });
    },
  },
});
