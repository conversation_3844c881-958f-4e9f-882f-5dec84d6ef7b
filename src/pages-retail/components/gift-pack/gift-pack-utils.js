function priceFn(value, dc, floor = true) {
  if (!value && value !== 0) {
    return '-.--';
  }
  const dvd = +dc || 100;
  const realValue = +value || 0;
  return !floor ? realValue / dvd : Math.floor(realValue / dvd);
}

export function formatPrice(price) {
  return price % 100 === 0 ? Math.round(price / 100) : (price / 100).toFixed(2);
}

export function disabledRecharge(giftPacks, activeIndex) {
  if (giftPacks !== undefined) {
    return giftPacks.length !== 0 && activeIndex === -1;
  }
}
export function balance(value, fiexd = 2) {
  const result = priceFn(value, 100, false);
  if (result === '-.--') {
    return result;
  }
  return result.toFixed(fiexd);
}
