<view class="gitfpack">
  <view calss="sheet__content sheet__content__protocol">
    <block wx:if="{{!!giftPacks.length}}">
      <view class="sheet__title">充值: ¥ {{ formatProductAmount }}</view>
      <!-- 礼包 -->
      <view class="{{!!giftPacks.length ? 'sheet__scroll-content' : ''}}" style="padding-bottom: {{ paddingScrollBottom }}">
        <view class="sheet__scroll-content-wrap">
          <view
            wx:for="{{giftPacks}}"
            wx:for-index="idx"
            wx:for-item="giftPack"
            wx:key="giftPackId"
            class="sheet__card"
            data-idx="{{idx}}"
            bind:tap="cardClick"
          >
            <!-- 标题 -->
            <view class="sheet__card--title">
              <view class="name">{{ giftPack.giftPackName }}</view>
              <view class="check-icon" style="border-color: {{themeMainColor }}">
                <van-icon style="background: {{ themeMainColor }}" class="{{idx === activeIndex ? 'checked' : 'gift-van-icon'}}" name="success" />
              </view>
            </view>
            <!-- 内容 -->
            <view class="sheet__card--content">
              <view class="sheet__rights">
                <rule-rights-detail gift-pack="{{giftPack}}" />
              </view>
            </view>
          </view>
        </view>
        <view class="sheet_protocol--no-card-sign" style="padding-bottom: {{paddingBottom }}">
          查看
          <navigator hover-class="none" class="sheet__protocol-nav" href="{{agreementUrl}}">
            《储值卡消费者服务协议》
          </navigator>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="sheet__card--no-gift">
        <image src="https://b.yzcdn.cn/wsc-h5-assets/card/none-gift-pack.png" alt="" />
        <view class="sheet__card--no-gift-text">
          <view>暂无礼包可赠送</view>
          <view>您可选择继续充值{{ formatProductAmount }}元或放弃</view>
        </view>
      </view>
    </block>
  </view>
  <!-- 底部 -->
  <view class="sheet__footer {{deviceType}}">
    <btn
      safe-area-inset-bottom
      custom-class="van-btn theme-bg-color"
      bind:tap="onRechargeClick"
      type="danger"
      disabled="{{disabledRecharge}}"
      loading="{{rechargeBtnLoading}}"
      color="{{themeMainColor}}"
    >
      立即充值
    </btn>
  </view>
</view>


