import { VanxComponent } from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';
import { isNewIphone } from 'shared/utils/browser/device-type';
import * as formatUtils from './gift-pack-utils.js';

const app = getApp();
VanxComponent({
  properties: {
    product: {
      type: Object,
      value: {},
      observer() {
        this.setFormatProductAmount();
        this.setDisabledRecharge();
        this.setGiftPacks();
      }
    },
    valueCard: {
      type: Object,
      value: {}
    },
    payService: Object,
    themeMainColor: {
      type: String,
      value: ''
    },
    // 充值推荐规则
    recommendDetail: {
      type: Object,
      value: {},
      observer: 'initState'
    }
  },
  data: {
    // disabledRecharge: true,
    rechargeBtnLoading: false,
    isRecharging: false, // 是否是充值中
    // 如果没开过卡 需要保存一下新开的卡号呦
    cardNoSign: '',
    // 如果只有一个礼包默认选中
    activeIndex: -1,
    payWays: {},
    paddingScrollBottom: 0,
    pay: {},
    isIPhoneX: false,
    isProtocolApproval: false,
    deviceType: '',
    agreementUrl: '/packages/pre-card/agreement/index'
  },
  ready() {
    this.initState();
    this.setDeviceType();
  },
  watch: {
    'data.activeIndex': function() {
      this.setDisabledRecharge();
    }
  },
  methods: {
    setFormatProductAmount() {
      const val = formatUtils.balance(this.data.product.amount);
      this.setYZData({
        formatProductAmount: val
      });
    },
    setDisabledRecharge() {
      const val = formatUtils.disabledRecharge(
        this.data.product.giftPacks,
        this.data.activeIndex
      );
      this.setYZData({
        disabledRecharge: val
      });
    },
    setGiftPacks() {
      const val = this.data.product?.giftPacks || [];
      this.setYZData({
        giftPacks: val
      });
    },
    setDeviceType() {
      const isIphoneX = isNewIphone();
      this.setYZData({
        deviceType: isIphoneX ? 'iPhone-X' : '',
        paddingBottom: isIphoneX ? '34px' : 0
      });
    },

    preOrderError(error) {
      wx.showToast({
        title: `预充值失败${error?.msg}`,
        icon: 'none',
        duration: 2000
      });
      this.toggleRecharging(false);
      this.toggleRechargeBtnLoading(false);
    },
    // 设置卡号
    initState() {
      const recommendDetail = this.data.recommendDetail || {};
      const { cardNo } = recommendDetail;
      if (cardNo) {
        this.setYZData({
          cardNoSign: cardNo
        });
      }
    },
    // 点击充值进行校验
    onRechargeClick() {
      if (this.data.disabledRecharge) {
        return;
      }

      this.initState();

      const { isRecharging } = this.data;

      // 充值中...
      if (isRecharging) return;

      this.toggleRecharging(true);
      this.toggleRechargeBtnLoading(true);

      this.startCashierPay(this.preparePrepay());
    },

    // 预下单参数
    preparePrepay() {
      const {
        product: { amount: payAmount },
        cardNoSign = ''
      } = this.data;
      const {
        giftPackName: goodsName = '储值推荐固定金额储值',
        factType: tradeDesc = 'VLCARD_RCHG',
        giftPackId: ruleNo = '0',
        giftPackVersion: ruleVersion = ''
      } = this.activeGiftPack();

      return {
        kdtId: app.getKdtId(),
        cardNo: cardNoSign,
        payAmount,
        goodsName,
        tradeDesc,
        ruleVersion,
        ruleNo,
        source: 30,
        extendsInfo: {
          kdtId: app.getKdtId(),
          ruleNo,
          cardNo: cardNoSign,
          productNo: this.data.productNo,
          amount: payAmount,
          pubKdtId: app.getKdtId(),
          acpKdtId: app.getKdtId(),
          marketChannel: '2',
          weappRedirectUrl: '',
          successRedirect: ''
        }
      };
    },

    // 开始预下单
    startCashierPay(prepayParams) {
      return this.preOrder(prepayParams);
    },

    // 开始充值
    preOrder(prepayParams) {
      this.triggerEvent('startCashierPay', {
        prepayParams
      });
    },

    giftPackStateClean() {
      this.triggerEvent('onClose');
      this.toggleRecharging(false);
      this.toggleRechargeBtnLoading(false);
    },

    // 关闭弹窗
    onClose() {
      this.triggerEvent('onClose');
    },
    // 选择礼包索引
    cardClick(event) {
      const idx = event.currentTarget.dataset.idx;
      const { activeIndex } = this.data;
      this.setYZData({
        activeIndex: activeIndex === idx ? -1 : idx
      });
    },
    // 选中的礼包
    activeGiftPack() {
      return get(this.data, `product.giftPacks[${this.data.activeIndex}]`, {});
    },

    toggleRecharging(bol) {
      this.setYZData({
        isRecharging: bol
      });
    },

    toggleRechargeBtnLoading(bol) {
      this.setYZData({
        rechargeBtnLoading: bol
      });
    },
    // radio
    toggleProtocolCheck() {
      this.setYZData({
        isProtocolApproval: !this.data.isProtocolApproval
      });
    }
  }
});
