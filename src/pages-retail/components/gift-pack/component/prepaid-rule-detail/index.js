import { VanxComponent } from 'pages/common/wsc-component/index';

const formatUtils = {
  formatPrice(price) {
    return price % 100 === 0
      ? Math.round(price / 100)
      : (price / 100).toFixed(2);
  },
  getInvalidClass(isInvalid) {
    return isInvalid ? 'item__disable' : '';
  },
  getInvalidText(preText) {
    return preText + '，充值后无法获赠';
  }
};

VanxComponent({
  properties: {
    giftPack: {
      type: Object,
      default: () => {},
      observer() {
        const funcKeys = [
          'bonusAmountRights',
          'couponRights',
          'growthPointRights',
          'memberCardRights',
          'pointRights',
          'presentRights'
        ];
        funcKeys.forEach(item => {
          const val = this[item]();
          this.setYZData({
            [item]: val
          });
        });
      }
    }
  },
  data: {
    bonusAmountRights: {},
    couponRights: [],
    growthPointRights: {},
    memberCardRights: [],
    pointRights: {},
    presentRights: {}
  },
  computed: {},
  methods: {
    // 赠送金
    bonusAmountRights() {
      const { giftPack } = this.data;
      const bonusAmountRights = giftPack?.ruleRights?.bonusAmountRights || {};
      bonusAmountRights.bonusAmount_FormatePrice = formatUtils.formatPrice(
        bonusAmountRights.bonusAmount
      );
      return bonusAmountRights;
    },
    // 优惠券
    couponRights() {
      const { giftPack } = this.data;
      const couponRights = giftPack?.ruleRights?.couponRights || [];
      couponRights.forEach(coupon => {
        coupon.invalidText_FormatClass = formatUtils.getInvalidClass(
          !!coupon.invalidText
        );
        coupon.invalidText_FormatText = formatUtils.getInvalidText(
          coupon.invalidText
        );
      });
      return couponRights;
    },
    // 成长值
    growthPointRights() {
      const { giftPack } = this.data;
      return giftPack?.ruleRights?.growthPointRights || {};
    },
    // 权益卡
    memberCardRights() {
      const { giftPack } = this.data;
      const memberCardRights = giftPack?.ruleRights?.memberCardRights || [];
      memberCardRights.forEach(member => {
        member.invalidText_FormatClass = formatUtils.getInvalidClass(
          !!member.invalidText
        );
        member.invalidText_FormatText = formatUtils.getInvalidText(
          member.invalidText
        );
      });
      return memberCardRights;
    },
    // 积分
    pointRights() {
      const { giftPack } = this.data;
      return giftPack?.ruleRights?.pointRights || {};
    },
    // 赠品
    presentRights() {
      const { giftPack } = this.data;
      const presentRights = giftPack?.ruleRights?.presentRights || {};
      presentRights.invalidText_FormatText = formatUtils.getInvalidText(
        presentRights.invalidText
      );
      presentRights.invalidText_FormatClass = formatUtils.getInvalidClass(
        !!presentRights.invalidText
      );
      return presentRights;
    }
  }
});
