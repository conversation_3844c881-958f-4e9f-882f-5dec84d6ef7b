<view class="rule-rights-detail">
  <!-- 🍀 赠送金 -->
  <view class="item__right item__bonus" wx:if="{{bonusAmountRights.bonusAmount}}"> 送 {{ bonusAmountRights.bonusAmount_FormatePrice }} 元 储值余额
    <view class="validity" wx:if="{{bonusAmountRights.bonusValidType === 1}}">
      <br bind:_init="_tic" />
      到账{{ bonusAmountRights.bonusValidPeriod }}个月后失效
    </view>
  </view>
  <!-- 🎫 优惠券 -->
  <block bind:_init="_tic" wx:if="{{couponRights.length}}">
    <view
      wx:for="{{couponRights}}"
      wx:for-item="coupon"
      wx:key="couponId"
      class="{{'item__right item__coupon'}} {{coupon.invalidText_FormatClass}}"
    > 送 {{ coupon.count }} 张 {{ coupon.slogan || coupon.couponName }}
      <view class="item__stages" wx:if="{{!!coupon.invalidText}}"> {{ coupon.invalidText_FormatText }} </view>
    </view>
  </block>
  <!-- ⌚ 赠品 -->
  <view wx:if="{{presentRights.presentName}}" class="{{'item__right item__present'}} {{presentRights.invalidText_FormatClass}}"> 送 1 份 {{ presentRights.presentName }}
    <view class="item__stages" wx:if="{{!!presentRights.invalidText}}"> {{ presentRights.invalidText_FormatText }} </view>
  </view>
  <!-- 📈 积分 -->
  <view class="item__right item__point" wx:if="{{pointRights.point}}"> 送 {{ pointRights.point }} {{ pointRights.customPointName || '积分' }} </view>
  <!-- 💳 普通权益卡 -->
  <!-- 💳 储值权益卡 -->
  <block bind:_init="_tic" wx:if="{{memberCardRights.length}}">
    <view
      wx:for="{{memberCardRights}}"
      wx:for-item="member"
      wx:key="cardId"
      class="{{'item__right item__member'}} {{member.invalidText_FormatClass}}"
    > 送 1 张 {{ member.cardName }}
      <view class="item__stages" wx:if="{{!!member.invalidText}}"> {{ member.invalidText_FormatText }} </view>
    </view>
  </block>
  <!-- 🍀 成长值 -->
  <view class="item__right item__growth" wx:if="{{growthPointRights.growthPoint}}"> 送 {{ growthPointRights.growthPoint }} 成长值
  </view>
</view>


