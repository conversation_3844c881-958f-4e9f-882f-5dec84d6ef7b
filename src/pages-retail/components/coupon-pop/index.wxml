<view class="{{'coupon ' + layout}}" catch:tap="togglePopup">
  <view class="coupon-item" wx:for="{{displayCoupons}}" wx:for-item="coupon" wx:key="id">
    <view class="coupon-item__value">
      <text wx:if="{{coupon.discount}}">{{coupon.priceValue}}<text class="price-hint">折</text></text>
      <text wx:else>¥{{coupon.priceValue}}</text>
      <text wx:if="{{coupon.valueRandomTo}}" class="price-hint">起</text>
    </view>
    <view class="coupon-item__type">{{coupon.threshold}}</view>
    <view class="coupon-item__opt">领取</view>
    <view class="before" /><view class="after" />
  </view>
  <!-- <view class="coupon-item">
    <view class="coupon-item__value">¥5</view>
    <view class="coupon-item__type">无门槛</view>
    <view class="coupon-item__opt">领取</view>
  </view> -->
</view>

<van-popup
  position="bottom"
  show="{{show}}"
  custom-style="height: 60%;"
  custom-class="coupon-pop"
  bind:close="togglePopup"
>
    <view class="coupon-pop__header">
      领取优惠券
      <van-icon custom-class="coupon-pop__close" name="cross" bind:tap="togglePopup" />
    </view>
    <scroll-view
      scroll-y
      class="coupon-pop__scroll"
      style="height: calc(100% - 88rpx);"
    >
    <view class="coupon-pop__content">
      <view class="coupon-pop__title">可领优惠券</view>
      <view class="coupon-pop__item" wx:for="{{displayCoupons}}" wx:for-item="coupon" wx:key="id">
        <view class="coupon-pop__item-price">
          <view class="price">
            <text class="value">{{coupon.priceValue}}</text>
            <text wx:if="{{coupon.discount}}">折</text><text wx:else>元</text>
            <text wx:if="{{coupon.valueRandomTo}}">起</text>
          </view>
          <view class="meet">{{coupon.threshold}}</view>
        </view>
        <view class="coupon-pop__item-msg">
          <view class="desc">{{coupon.name}}</view>
          <view class="time-range">{{coupon.timeRange}}</view>
        </view>
        <view class="coupon-pop__item-opt">
          <!-- <button class="obtain">立即领取</button> -->
          <van-button wx:if="{{!coupon.acquired}}" round custom-class="obtain" data-id="{{coupon.id}}" bind:click="takeCoupon" disabled="{{coupon.disabled}}">
            {{coupon.acquired ? '已领取' : '立即领取'}}
          </van-button>
        </view>
        <view class="coupon-got" wx:if="{{coupon.acquired}}">
          <view class="coupon-got__inner">已领</view>
        </view>
      </view>
    </view>
    </scroll-view>
</van-popup>
