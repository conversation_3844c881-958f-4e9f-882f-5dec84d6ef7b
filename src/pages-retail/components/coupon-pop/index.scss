.coupon {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  height: 48rpx;

  &-item {
    display: flex;
    position: relative;
    overflow: hidden;
    align-items: center;
    height: 48rpx;
    width: 286rpx;
    margin-right: 20rpx;
    background-color: #ffe9b7;
    border-radius: 2px;
    color: #7A4A19;
    font-size: 22rpx;
    line-height: 48rpx;
    text-align: center;

    .before,
    .after {
      content: ' ';
      position: absolute;
      width: 6px;
      height: 4px;
      border-radius: 6rpx/4rpx;
      background-color: #fff;
    }

    .before {
      top: -2px;
      right: 64rpx;
    }

    .after {
      bottom: -2px;
      right: 64rpx;
    }

    .price-hint {
      font-size: 20rpx;
    }

    &__value {
      font-size: 32rpx;
      font-weight: 500;
    }

    &__value,
    &__type {
      flex-grow: 1;
    }

    &__opt {
      flex-shrink: 0;
      width: 68rpx;
    }
  }

  &-pop {
    display: flex;
    flex-direction: column;
    border-radius: 36rpx 36rpx 0 0;

    &__header {
      position: relative;
      text-align: center;
      height: 88rpx;
      line-height: 88rpx;
      font-size: 32rpx;
      font-weight: 400;
    }

    &__close {
      position: absolute !important;
      top: 30rpx;
      right: 40rpx;
      color: #969799;
    }

    &__content {
      padding: 32rpx;
    }

    &__title {
      font-size: 26rpx;
    }

    &__item {
      display: flex;
      position: relative;
      overflow: hidden;
      align-items: center;
      background-color: #FEF5F6;
      border: 1rpx solid #FDE6E9;
      border-radius: 10rpx;
      padding: 32rpx;
      margin-top: 20rpx;

      &-price {
        flex-shrink: 0;
        width: 188rpx;

        .price {
          color: #EE0A24;
          font-size: 24rpx;
        }

        .value {
          font-size: 60rpx;
        }

        .meet {
          font-size: 24rpx;
          color: #969799;
          margin-top: 2rpx;
        }
      }

      &-msg {
        flex-grow: 1;

        .desc {
          font-size: 28rpx;
          color: #323233
        }

        .time-range {
          font-size: 24rpx;
          color: #969799;
          margin-top: 18rpx;
        }
      }

      &-opt {
        flex-shrink: 0;

        .obtain {
          width: 125rpx;
          padding: 0;
          border: 0;
          height: 48rpx;
          line-height: 48rpx;
          font-size: 24rpx;
          color: #fff;
          background-color: #EE0A24;
        }
      }
    }

    &__got {
      position: absolute;
      transform: rotate(-30deg);
      right: -12rpx;
      bottom: -12rpx;
      width: 88rpx;
      height: 88rpx;
      border-radius: 44rpx;
      border: 2rpx solid #EB8B8B;
      display: flex;
      align-items: center;
      justify-content: center;

      &__inner {
        width: 72rpx;
        height: 72rpx;
        border-radius: 36rpx;
        border: 1rpx dashed #EB8B8B;
        background-color: rgba(238, 10, 36, 0.06);
        text-align: center;
        line-height: 72rpx;
        font-size: 20rpx;
        color: #EB8B8B;
      }
    }
  }
}
