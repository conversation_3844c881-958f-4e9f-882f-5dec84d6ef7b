import { moment } from '@youzan/weapp-utils/lib/time';

const app = getApp();

Component({
  properties: {
    layout: String,
    coupons: {
      type: Array,
      observer(data) {
        this.getDisplayCoupons(data);
      }
    }
  },
  data: {
    show: false,
    displayCoupons: []
  },
  methods: {
    togglePopup() {
      this.setData({
        show: !this.data.show
      });
    },
    getDisplayCoupons(data) {
      this.setData({
        displayCoupons: data.map(
          ({
            dateType,
            fixedTerm,
            fixedBeginTerm,
            denominations,
            discount,
            validEndTime,
            validStartTime,
            condition,
            ...oth
          }) => {
            const ret = {
              ...oth,
              acquired: false,
              disabled: false,
              threshold: condition ? `满${condition / 100}元使用` : '无门槛',
              priceValue: discount ? discount / 10 : denominations / 100,
              discount,
              denominations
            };
            if (dateType === 1) {
              ret.timeRange = `${moment(validStartTime, 'YYYY.MM.DD')}-${moment(
                validEndTime,
                'YYYY.MM.DD'
              )}`;
            }
            if (dateType === 2) {
              ret.timeRange = `领券${
                fixedBeginTerm ? '次' : '当'
              }日起${fixedTerm}天内可用`;
            }
            return ret;
          }
        )
      });
    },

    takeCoupon(event) {
      const { id } = event.currentTarget.dataset;
      // const { accessToken } = app.getToken();
      const { displayCoupons } = this.data;
      const index = displayCoupons.findIndex(
        ({ id: couponId }) => couponId === id
      );
      const newDisplayCoupons = [...displayCoupons];
      newDisplayCoupons[index].disabled = true;
      this.setData({
        displayCoupons: newDisplayCoupons
      });

      app
        .request({
          path: '/wscshop/ump/coupon/fetchCoupon.json', // '/wscump/coupon/take_coupon_by_token.json',
          method: 'POST',
          data: {
            source: 'mini_program',
            groupId: id
          }
        })
        .then(res => {
          const { discount, value } = res;
          newDisplayCoupons[index].acquired = true;
          newDisplayCoupons[index].disabled = false;
          newDisplayCoupons[index].priceValue = discount
            ? discount / 10
            : value / 100;
          newDisplayCoupons[index].valueRandomTo = 0;
          this.setData({
            displayCoupons: newDisplayCoupons
          });
          wx.showToast({
            icon: 'none',
            title: '领取成功'
          });
        })
        .catch(err => {
          newDisplayCoupons[index].disabled = false;
          this.setData({
            displayCoupons: newDisplayCoupons
          });
          wx.showToast({
            icon: 'none',
            title: err.msg || '领取失败'
          });
        });
    }
  }
});
