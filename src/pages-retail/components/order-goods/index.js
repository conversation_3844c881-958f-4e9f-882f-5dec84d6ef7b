import money from '@youzan/weapp-utils/lib/money';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { splitItemsByBuyerIdForRender } from 'retail/util/cart';
import { getStoredDiscount } from 'retail/util/ump';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import Toast from '@vant/weapp/dist/toast/toast';
import { mapKeysToCamelCase } from '@youzan/utils/string';
import { getThemeMainColor, hexToRGB } from 'retail/util/shelf-global';
import {
  getGiftPacks,
  getPreOrder,
  queryOrderStatus,
  signAgreement,
} from 'retail/util/api/prepaid';
import {
  interval,
  getValueCardWidth,
  getDiscountText,
  processContent,
  processPack,
  rechargePrint,
} from './tools';

const app = getApp();

Component({
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持
  },
  properties: {
    // 原生组件坑很多
    hideInput: Boolean,
    themeColor: String,
    isCreated: Boolean,
    allowPayStatus: Boolean,
    // 储值优惠 隐藏储值卡和优惠券
    isHiddenCard: Boolean,
    totalDecrease: Number,
    totalCurrentPrice: Number,
    scene: Number,
    balanceMkExclusion: {
      type: Boolean,
      value: false,
    },
    // 多人购物车 暂不支持选择放弃优惠
    isStoreVip: {
      type: Boolean,
      value: false,
    },
    goodsList: {
      type: Array,
      observer(val) {
        this.handleGoodsData(val);
      },
    },
    recommend: {
      type: Object,
      observer(val) {
        const { recommendDetaid: detail, newRecommend } = val;

        if (!detail) return;

        this.cancelChoose(true);

        // 区分储值推荐和储值优惠 执行对应的handler
        const { rechargeDiscount, productInfos, cardNo } = detail;

        // 保存下卡号
        this.cardNo = cardNo;

        // 保存充值规则 为了避免重新confirm导致充值规则异常 只有在初始化时保存
        if (this.newRecommend === void 0) {
          // 余额全额支付时可享受会员优惠时强制改为先充后付
          this.newRecommend = newRecommend && !this.data.isStoreVip;
        }

        if (rechargeDiscount) {
          this.isDiscount = true;
          return this.processDiscount(detail);
        }

        if (productInfos) {
          this.isRecommend = true;
          return this.processRecommend(detail);
        }

        // 充值完成还要confirm一下 这时候清空页面上的推荐
        this.setData({ values: [] });
        this.cardNo = '';
        this.newRecommend = void 0;
        this.isDiscount = false;
        this.isRecommend = false;
        this.currentPackId = '';
        this.currentRule = null;
        this.productInfos = null;
        this.discountRules = null;
      },
    },
    extraPriceDTOS: Array,
    shopName: String,
    shopLocation: String,
    valueCard: {
      type: Array,
      observer: 'handleValueCard',
    },
    // 储值卡是否余额不足
    cardNotEnough: Boolean,
    coupons: {
      type: Array,
      observer: 'handleCouponAmount',
    },
    canChooseCoupon: {
      type: Boolean,
      value: false,
      observer: 'handleCouponAmount',
    },
    isDone: {
      type: Boolean,
      value: false,
      observer: 'handleCouponAmount',
    },
    showDiscountDetail: {
      type: Boolean,
      value: false,
    },
    deliveryTypeDesc: String,
    comment: String,
    totalPrice: Number, // 小计
    cardPricePay: {
      type: Number,
      observer: 'handleCardPricePay',
    }, // 储值卡
    realTotalPrice: Number, // 合计=应付价格
    orderInfo: Object,
    promotionValue: Number,
    // 储值余额和卡，包括可使用和不可使用的
    storedValueAndCard: {
      type: Object,
      value: null,
      observer: 'handleCouponAmount',
    },
    storedDiscountValue: Number, // 储值折扣优惠的钱
    storedDiscountIsOpen: {
      type: Boolean,
      observer(val) {
        if (val) {
          this.setData({
            storedDiscount: getStoredDiscount(this.data.storedDiscountInfo), // 获取储值折扣活动的折扣
          });
        }
      },
    },
    storedDiscountInfo: Object,
    unavailableGoodsList: Array,
    showDeliveryTypeDesc: {
      type: Boolean,
      value: true,
    },
    dinerNum: {
      type: Number,
      value: 0,
    },
    showComment: {
      type: Boolean,
      value: true,
    },
    // 提交页，展示
    isSubmitPage: Boolean,
    // 是否加餐流程
    isAddMeal: {
      type: Boolean,
      value: false,
    },
    // 是否是多人购物车
    isMultiCart: {
      type: Boolean,
      value: false,
    },
    // 充值推荐规则
    recommendDetail: {
      type: Object,
      value: {},
    },
    // 订单详情,组合支付展示
    phasePayDetails: {
      type: Array,
      value: [],
    },
    appointmentTime: {
      type: String,
      value: '-',
    },
    valueCardBtnDisabled: {
      type: Boolean,
      value: false,
    },
    canUseMemberBenefit: Boolean,
    memberBenefitDesc: String,
    meituanCoupons: Array,
    mainColor: {
      type: String,
      observer(v) {
        this.setData({ mainColorAlpha: hexToRGB(v, 0.1) });
      },
    },
    themeMainRgbColor: String,
    fns: {
      type: Object,
      value: {},
    },
    canChooseMeituan: {
      type: Boolean,
      value: true,
    },
    goodsWrapperStyle: String,
  },

  data: {
    couponAmount: '',
    isHide: false,
    // 记录当前是否点击过”查看全部“
    isExpand: false,
    lunchBox: undefined,
    cardValueAmount: '',
    canChooseValueCard: false,
    showValueCard: false,
    multiOrderItems: [],
    storedDiscount: '',
    showValueAmountCell: false,
    values: [],
    chosen: null,
    showGiftPop: false,
    product: null,
    themeMainColor: getThemeMainColor(),
    mainColorAlpha: '',
    defaultPack: '',
    // 当前选中的礼包
    selected: '',
    // 抵扣金额
    deduction: 0,
  },

  ready() {
    this.setData({
      logo: cdnImage(getApp().getShopInfoSync().logo, '!64x64+2x.jpg'),
    });
    this.initPayService();
  },

  methods: {
    processDiscount(v) {
      const { rules } = v.rechargeDiscount;
      const width = getValueCardWidth(rules.length) + 'rpx';

      const values = rules.map((rule) => ({
        w: width,
        v: rule.amount / 100,
        t: getDiscountText(rule),
        id: rule.amount,
      }));

      this.discountRules = rules;
      this.setData({ values });
    },
    processRecommend(detail) {
      // 查询礼包
      const { productInfos } = detail;
      const productNoList = productInfos.map((p) => p.productNo);

      const Fn =
        productNoList && productNoList.length
          ? getGiftPacks
          : () => Promise.resolve([]);

      Fn({
        kdtId: app.getKdtId(),
        productNoList,
        recommendChannelType: 1,
        disableFilter: true,
      }).then((res = []) => {
        (productInfos || []).forEach((product) => {
          const { productNo } = product;
          const pack = res.find((item) => item.productNo === productNo);

          // 无礼包
          if (!pack.giftPacks.length) {
            product.bonusAmount = 0;
            product.packs = [];

            return;
          }

          const rights = pack.giftPacks[0].ruleRights;
          const { bonusAmountRights = {} } = rights;

          product.bonusAmount = bonusAmountRights.bonusAmount || 0;
          product.packs = processPack(pack);
        });

        // 不需要响应 仅保存下来 选择档位的时候用
        this.productInfos = productInfos;
        this.processValues(productInfos);
      });
    },
    processValues(products) {
      const width = getValueCardWidth(products.length) + 'rpx';
      const values = products.map((product) => {
        const { packs, amount, productNo, bonusAmount: ba = 0 } = product;
        return {
          ba: ba / 100,
          w: width,
          v: amount / 100,
          g: packs[0] ? '送' + packs[0].freegoSlogan : '',
          id: productNo,
        };
      });
      // 储值推荐展示逻辑 完
      const deduction =
        (this.data.balanceMkExclusion
          ? this.data.totalDecrease + this.data.totalCurrentPrice
          : this.data.totalPrice) / 100;
      this.setData({ values, deduction });
    },

    cancelChoose(onlyUI) {
      this.setData({ chosen: '' });
      this.triggerEvent('chooseValue', {
        show: false,
      });

      if (onlyUI) return;

      /**
       * 优惠互斥 取消勾选档位时重新renderorder 把优惠信息再加上
       */
      if (this.data.balanceMkExclusion) {
        const { fns } = this.data;
        fns.renderOrder({ cards: {} });
      }
    },

    handleChooseDiscount(id) {
      const rule = this.discountRules.find((r) => r.amount === id);

      this.currentRule = rule;
      this.setData({ chosen: id });

      const { totalCurrentPrice = 0, totalDecrease = 0 } = this.data;

      this.triggerEvent('chooseValue', {
        amount: rule.amount / 100,
        label: '储值',
        desc: getDiscountText(rule, totalCurrentPrice, totalDecrease, true),
        show: true,
        button: '充值并支付',
      });
    },

    handleChooseRecommend(id, e) {
      const product = this.productInfos.find((item) => item.productNo === id);
      const { id: packId } = product.packs[0] || {};
      const { deduction } = this.data;

      // 默认选中第一个礼包
      this.currentPackId = packId;
      // 设置选中状态
      this.setData({
        showGiftPop: !!packId,
        chosen: id,
        product,
        selected: packId,
      });

      if (packId) {
        // 隐藏原来的底部bar
        this.triggerEvent('toggleSubmitBar', false);
      }

      // if (e) return;

      const { newRecommend } = this;

      // 更新底部bar
      this.triggerEvent('chooseValue', {
        amount: product.amount / 100,
        label: '储值',
        desc: deduction ? `本单抵扣${deduction}元` : '',
        show: true,
        button: newRecommend ? '充值并支付' : '立即充值',
      });
    },

    chooseValue(id, e) {
      const { isDiscount, isRecommend } = this;

      if (isDiscount) {
        return this.handleChooseDiscount(id);
      }

      if (isRecommend) {
        return this.handleChooseRecommend(id, e);
      }
    },

    handleChooseValue(e) {
      if (e === 'storeVip') {
        // 储值会员 用最接近订单金额的档位出弹窗
        const value = this.data.values[0];
        if (value) {
          this.chooseValue(value.id, e);
        }

        return;
      }

      const { dataset } = e.currentTarget;

      if (this.data.chosen === dataset.id) {
        return this.cancelChoose();
      }

      /**
       * 如果是充值并支付 且 互斥 提醒一下
       * 同时重新renderOrder 取消优惠券
       */
      if (this.newRecommend && this.data.balanceMkExclusion) {
        Dialog.confirm({
          message: '本单储值余额不能和营销同时享受，将以商品原价计算支付金额。',
          confirmButtonText: '去充值',
        })
          .then(() => {
            const { fns } = this.data;
            this.injectCardNo().then((cardNo) => {
              fns
                .renderOrder({
                  cards: { [cardNo]: { isExclusion: true } },
                  isMutex: true,
                })
                .then(() => {
                  this.chooseValue(dataset.id);
                });
            });
          })
          .catch(() => {});
      } else {
        this.chooseValue(dataset.id);
      }
    },

    handleCloseGiftPop() {
      this.setData({
        showGiftPop: false,
      });
      this.triggerEvent('toggleSubmitBar', true);

      // 互斥
      if (this.data.balanceMkExclusion) {
        this.cancelChoose();
      }
    },

    handleChoosePack({ detail }) {
      const { id } = detail;
      this.currentPackId = id;
    },

    getCurrentCard() {
      const { cardNo, isDiscount, isRecommend } = this;
      const { usable } = this.data.storedValueAndCard;
      const { storedValue = [] } = usable;

      return storedValue.find((c) => c.summaryCardNo === cardNo) || {};
    },

    // 自动选卡逻辑
    handleSelectCard(immediate) {
      const { cardNo, isDiscount, isRecommend } = this;
      const card = this.getCurrentCard();

      this.handleValueCardChange({
        cards: { [cardNo]: card },
        isDiscount,
        isRecommend,
        immediate,
      });
      this.triggerEvent('chooseValue', {
        show: false,
      });
      this.triggerEvent('toggleSubmitBar', true);
    },

    /* payService ⬇️ */
    onPaySuccess() {
      wx.showLoading({ title: '充值中...' });

      const { rechargeNo } = this;

      interval({
        fn: () => queryOrderStatus({ rechargeNo }),
        validator: (result) => {
          const { status } = result;

          return status === 2;
        },
      }).then((success) => {
        const title = success ? '充值成功' : '充值失败';

        // 储值成功打印小票
        if (success) {
          rechargePrint(rechargeNo);
        }

        wx.showToast({
          title,
          duration: 1000,
        });

        this.setData({
          showGiftPop: false,
        });

        // 充值优惠只有"先充后付"
        if (this.isDiscount) {
          return this.triggerEvent('rechargeDone');
        }

        this.triggerEvent('toggleSubmitBar', true);

        if (this.newRecommend) {
          // 调页面的paid-success
          this.triggerEvent('paid-success', { orderNo: this.orderNo });
        } else {
          // 先充值再支付 需要刷新下order 并且自动选卡
          // 通过触发valueCardChange来刷新order
          this.handleSelectCard();
        }
      });
    },

    onCashierClose() {
      /**
       * 充值并支付 创单完成但是没有付款
       * 拉起付款前就已经把E单号挂在this上了
       */
      if (this.data.isCreated) {
        const url = `/packages/retail/order/detail/index?orderNo=${this.orderNo}`;

        return wx.redirectTo({ url });
      }

      // 如果礼包弹窗是展示状态 则不显示底部bar
      if (this.data.showGiftPop) return;
      this.triggerEvent('toggleSubmitBar', true);
    },

    handleStore() {
      // 控制权交给页面 因为需要页面创单
      this.triggerEvent('store');
    },

    handlePay({ orderNo } = {}) {
      this.triggerEvent('toggleSubmitBar', false);

      // 保存起来 paid-success的时候要用
      this.orderNo = orderNo;

      const ctx = { orderNo };

      this._payService.startPay(ctx);
    },

    getBaseInfo() {
      // 点击支付时触发 这里其实已经拿到礼包信息和卡号了
      const { recommendDetaid } = this.data.recommend;
      // ! 拿到卡号
      const { cardNo, productInfos, rechargeDiscount } = recommendDetaid;

      // 储值优惠没有pack
      if (this.isDiscount) {
        const { ruleId, ruleVersion } = rechargeDiscount;
        const { currentRule } = this;

        return [
          cardNo,
          {},
          currentRule,
          { ruleId, ruleVersion, orderInfo: currentRule },
        ];
      }

      const product = recommendDetaid.productInfos.find(
        (p) => p.productNo === this.data.chosen
      );
      const pack = product.packs.find((p) => p.id === this.currentPackId) || {};

      return [cardNo, pack.detail || {}, product];
    },

    // ! 准备预下单参数
    makePreOrderParams(context, outBizNo) {
      const [cardNo, pack, product, rule] = context.getBaseInfo();

      return {
        kdtId: app.getKdtId(),
        cardNo,
        outBizNo,
        payAmount: product.amount,
        goodsName: pack.giftPackName || '储值推荐固定金额储值',
        tradeDesc: pack.factType || 'VLCARD_RCHG',
        ruleVersion: pack.giftPackVersion,
        ruleNo: pack.giftPackId,
        source: 30,
        rechargeFreeOrderRule: rule || {},
        extendsInfo: {
          acpKdtId: app.getKdtId(),
          templateNo: context.data.recommend.recommendDetaid.templateNo,
          marketChannel: '2',
        },
      };
    },

    // 创建预接单前如果没有卡 需要先申请一张卡
    injectCardNo() {
      const { cardNo } = this;
      if (cardNo) return Promise.resolve(cardNo);

      const { templateNo } = this.data.recommend.recommendDetaid;

      return signAgreement({ templateNo })
        .then((result) => {
          const { cardNo } = result;
          this.cardNo = cardNo;

          return cardNo;
        })
        .catch(() => {
          Toast.fail('签约开卡失败');
        });
    },

    // 预下单
    async getPreOrderInfo(ctx, next) {
      const { orderNo } = ctx;
      const params = this.makePreOrderParams(this, orderNo);

      if (!params.cardNo) {
        params.cardNo = await this.injectCardNo();
      }

      const info = await getPreOrder(params);

      // R单号 在prepay的响应里叫 outBizNo
      this.rechargeNo = info.recharge_no;

      ctx.preOrderInfo = mapKeysToCamelCase(info);
      ctx.prepayParams = params;

      await next();
    },

    // 整理数据
    async assignPayment(ctx, next) {
      const params = ctx.prepayParams;
      params.scene = 'COMMON';

      ctx.payInfo = {
        ...ctx.preOrderInfo,
        extendsInfo: params.extendsInfo,
        prepay: params.prepay || false,
        prepaySuccess: params.prepay_success,
        extPoint: params.ext_point_pay_result_v_o || '',
      };

      await next();
    },

    initPayService() {
      if (this._payService) return true;
      const payService = this.selectComponent('#payService');
      payService.setConfig({
        onPaySuccess: this.onPaySuccess.bind(this),
        onCashierClose: () => this.onCashierClose(),
      });

      payService.use(this.getPreOrderInfo.bind(this));
      payService.use(this.assignPayment.bind(this));

      this._payService = payService;
    },

    /* payService ⬆️ */

    handleGoodsData(items) {
      const dataToBeSet = {
        lunchBox: items.find((item) => item.uniqueKey === 'lunchBox') || false,
      };

      // 加餐，处理商品数据
      if (this.data.isAddMeal) {
        const itemsForRender = splitItemsByBuyerIdForRender(items);

        this.setData({
          multiOrderItems: itemsForRender,
          ...dataToBeSet,
        });
      } else {
        // 如果点击过展开就不展示”查看全部“了
        this.setData({
          isHide: this.data.isExpand ? false : items.length > 5,
          ...dataToBeSet,
        });
      }
    },

    toggleShopDiscount() {
      this.triggerEvent('showDiscountDialog');
    },

    // 多张优惠券可叠加，目前支持满减券
    handleCouponAmount() {
      const { coupons = [], isDone, canChooseCoupon } = this.data;
      let couponAmount = '';

      coupons.forEach((coupon) => {
        if (coupon) {
          const { decreaseAmount, discount, type } = coupon;
          if (type === '1013') {
            couponAmount = '商品兑换';
          } else if (discount) {
            couponAmount = `${discount / 10} 折`;
          } else if (decreaseAmount) {
            couponAmount = Number(couponAmount) + decreaseAmount;
          } else if (!isDone) {
            // this.data.couponAmount 有值则表示，一定有可用的优惠券可选
            couponAmount = this.data.couponAmount ? '选择优惠券' : '';
          }
        }
      });

      if (typeof couponAmount === 'number') {
        couponAmount = `- ¥${+money(couponAmount).toYuan()}`;
      }

      // 有优惠券时提示
      if (!isDone && canChooseCoupon && !couponAmount) {
        couponAmount = '选择优惠券';
      }

      this.setData({
        couponAmount,
        canChooseCoupons: !isDone && canChooseCoupon,
      });
      // 选完优惠券后 要立即同步储值状态
      this.handleValueCard();
    },

    handleCardPricePay(cardPricePay) {
      this.setData({
        cardValueAmount: cardPricePay
          ? `- ¥${+money(cardPricePay).toYuan()}`
          : '选择余额/卡',
      });
    },

    handleValueCard(v) {
      if (!this.data.storedValueAndCard) return;
      const {
        isDone,
        isAddMeal,
        cardPricePay,
        storedValueAndCard: { usable: { storedCard, storedValue } = {} },
      } = this.data;

      this.setData({
        cardValueAmount: cardPricePay
          ? `- ¥${+money(cardPricePay).toYuan()}`
          : '选择余额/卡',
        // 是否显示余额/卡这一列
        showValueAmountCell:
          // 先吃后付 isAddMeal 为false
          // isDone 默认为false 创单后才会设置为true
          (!isAddMeal && !isDone) || (isDone && !!+cardPricePay),
        canChooseValueCard:
          !isDone && (storedCard?.length || storedValue?.length),
      });
    },

    handleChangeComment(e) {
      if (!e.detail) return;

      this.triggerEvent('change', {
        value: e.detail,
      });
    },

    handleValueCardChange(e) {
      /**
       * handleValueCardChange 只通过两个地方触发
       * 1. pay-submit 的 triggerEvent
       * 2. 充值完借用选卡逻辑
       * 这里把参数整理下 页面就不需要一层层解构了
       */

      const { fns } = this.data;
      fns.renderOrder(e.detail ? e.detail : e).then(() => {
        e.detail.callback();
      });
    },

    handleValueCardSubmit(e) {
      this.triggerEvent('valueCardSubmit', e);
    },

    showValueCardPop() {
      if (!this.data.canChooseValueCard) {
        return;
      }

      this.setData({
        showValueCard: true,
      });
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'select_value_card', // 事件标识
          en: '查看储值卡', // 事件名称
          pt: 'retailfreebuy', // 页面类型
          params: {}, // 事件参数
        });
      this.triggerEvent('toggleSubmitBar', false);
    },

    onChangeMemberBenefit() {
      if (!this.data.canUseMemberBenefit) {
        return;
      }
      this.triggerEvent('changeMemberBenefit');
    },

    onChangeCoupon() {
      if (!this.data.canChooseCoupons) {
        return;
      }
      this.triggerEvent('changeCoupon');
    },

    handleValueCardClose(env) {
      this.setData({
        showValueCard: false,
      });
      this.triggerEvent('toggleSubmitBar', env.detail);
    },

    showMoreGoodsList() {
      this.setData({
        isHide: false,
        isExpand: true,
      });
    },

    showStoredDiscount() {
      Dialog.alert({
        title: '储值专项折扣说明',
        message: '使用条件：使用“余额/卡”全额支付时，可享此折扣。',
        theme: 'round-button',
        messageAlign: 'left',
        confirmButtonText: '我知道了',
      }).then(() => {
        // on close
      });
    },
  },
});
