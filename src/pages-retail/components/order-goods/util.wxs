function computeAmount(goods) {
  return goods.pricingStrategy === 10 ? 1 : goods.amount;
}

function totalGoodsCount(goodsList) {
  return goodsList.reduce(
    (init, goods) => init + (computeAmount(goods) || goods.num),
    0
  );
}

function hideGoodsList(goodsList = [], needToHide, names = []) {
  const list = goodsList.filter(goods => names.indexOf(goods.uniqueKey) === -1);
  return needToHide ? list.slice(0, 5) : list;
}

module.exports = {
  totalGoodsCount,
  hideGoodsList
};
