<view class="shop-goods">
  <view class="order-goods" style="{{ goodsWrapperStyle }}">
    <block wx:if="{{ isAddMeal }}">
      <view
        class="order-goods-multiple"
        wx:for="{{ multiOrderItems }}"
        wx:for-item="item"
        wx:key="id"
      >
        <view class="order-goods-multiple__header">
          <image class="order-goods-multiple__header-avatar" src="{{ item.buyer.avatar }}" />
          <text class="order-goods-multiple__header-name">{{ item.buyer.nickName }}</text>
        </view>
        <order-intro
          style="--padding: 16rpx 0;"
          wx:for="{{ item.items }}"
          wx:for-item="goods"
          wx:key="index"
          goods="{{ goods }}"
          img-width="{{ 112 }}"
        />
      </view>
    </block>
    <block wx:else>
      <order-intro
        style="--padding: 24rpx 0;"
        wx:for="{{ util.hideGoodsList(goodsList, isHide, ['lunchBox']) }}"
        wx:for-item="goods"
        wx:key="index"
        goods="{{ goods }}"
        img-width="{{ 112 }}"
      />
    </block>
    <template is="unavailable-goods-list" wx:if="{{unavailableGoodsList.length}}" data="{{unavailableGoodsList: unavailableGoodsList}}" />
    <view wx:if="{{ isHide }}" class="goods-more" bind:tap="showMoreGoodsList">
      <text>查看全部</text>
    </view>

    <view class="shop-goods__extra {{!isAddMeal?'bdt1px':''}}">
      <cell
        wx:if="{{lunchBox}}"
        title="{{lunchBox.name}}"
        title-class="coupon-title"
        value="¥ {{money.toNumberYuan(lunchBox.salePrice)}}"
        value-class="lunch-box-money"
        border="{{false}}"
        custom-class="coupon-class"
      />
      <view wx:if="{{ extraPriceDTOS && extraPriceDTOS.length > 0 }}">
        <cell
          wx:for="{{extraPriceDTOS}}"
          wx:key="index"
          title="{{item.name}}"
          title-class="coupon-title"
          value="¥{{money.toNumberYuan(item.realPay)}}"
          border="{{false}}"
          custom-class="coupon-class"
          value-class="extra-cell-value"
        />
      </view>
      <block wx:if="{{ !isAddMeal && !isHiddenCard && !isMultiCart }}">
        <cell
          title="会员优惠"
          title-class="coupon-title"
          bind:click="onChangeMemberBenefit"
          value="{{canUseMemberBenefit ? memberBenefitDesc || '有可使用的会员优惠' : '暂无可用' }}"
          is-link="{{canUseMemberBenefit}}"
          border="{{false}}"
          value-class="{{canUseMemberBenefit ? 'coupon-value-red' : 'coupon-value-none'}} {{isDone ? 'color333' : ''}}"
          custom-class="order-ump-item coupon-class"
        >
        </cell>
        <slot name="member-guide"></slot>
    </block>
      <cell
        wx:if="{{ !isAddMeal && !isHiddenCard }}"
        title="优惠券"
        title-class="coupon-title"
        bind:click="onChangeCoupon"
        value="{{couponAmount || '暂无可用'}}"
        is-link="{{canChooseCoupons}}"
        border="{{false}}"
        value-class="{{couponAmount ? 'coupon-value-red' : 'coupon-value-none'}} {{isDone ? 'color333' : ''}}"
        custom-class="order-ump-item coupon-class"
      >
      </cell>
      
      <meituan-entry
        wx:if="{{ !isAddMeal && canChooseMeituan }}"
        canChoose="{{ canChooseMeituan }}"
        theme-color="{{ themeColor }}"
        coupon-list="{{ meituanCoupons }}" />

      <cell
        wx:if="{{promotionValue}}"
        title="活动优惠"
        title-class="coupon-title"
        value="{{'-¥ ' + money.toNumberYuan(promotionValue)}}"
        value-class="coupon-value-red {{isDone ? 'color333' : ''}}"
        custom-class="order-ump-item coupon-class discount-label"
        is-link="{{!isDone || showDiscountDetail}}"
        border="{{false}}"
        bind:click="toggleShopDiscount"
      >
      </cell>

      <van-divider
        wx:if="{{ isAddMeal }}"
        dashed
        customStyle="font-size: 22rpx;"
        content-position="center"
        style="--divider-margin: 8rpx 0;"
      >
        优惠券/积分/储值可在结账时使用
      </van-divider>

      <view class="info {{ isAddMeal ? '' : 'hasBorderTop' }}">
        <text class="info__goods-count">共 {{ util.totalGoodsCount(goodsList) }} 件商品，小计：</text>
        <price price="{{totalPrice}}" style="--price-color: #333;--price-family: avenir-heavy, pingfang sc, helvetica neue, arial, sans-serif;" />
      </view>

    </view>
  </view>
  <slot name="plus-buy"></slot>
  <view class="card__info" wx:if="{{ phasePayDetails && phasePayDetails.length > 0 }}">
    <cell
      wx:for="{{phasePayDetails}}"
      wx:key="index"
      title="{{item.buyWayDesc}}"
      title-class="coupon-title"
      value="-¥{{money.toNumberYuan(item.pay)}}"
      custom-class="coupon-class"
      value-class="coupon-value-red"
    />
  </view>

  <value-gift-pop
    show="{{ showGiftPop }}"
    disable="{{!allowPayStatus}}"
    mainColor="{{mainColor}}"
    product="{{product}}"
    selected="{{selected}}"
    newRecommend="{{recommend.newRecommend}}"
    bindonClose="handleCloseGiftPop"
    bindchooseGiftPack="handleChoosePack"
    bindpay="handleStore"
  />
  <pay-service themeColor="{{themeMainColor}}" id="payService" scene="{{scene}}"/>

  <view class="card-wrapper" style="margin-top:24rpx" wx:if="{{showValueAmountCell && !isHiddenCard}}">
    <view class="tooltips" wx:if="{{!isStoreVip && recommend.newRecommend && values.length}}">
      <image class="tooltips-icon" src="https://img01.yzcdn.cn/upload_files/2023/11/06/FtLl4D04PtNEa8mP_9BBMGoumkAV.png" />
      <!-- <text>储值更划算！</text> -->
      <text>
        <block wx:if="{{values[0].ba}}">充{{values[0].v}}送{{values[0].ba}}</block>
        <block wx:else>充值{{values[0].v}}元</block>
        ，本单可抵扣{{deduction}}元</text>
    </view>
    <view class="card__info" style="padding:0 0 {{ !isStoreVip && values.length ? '32rpx' : '0' }} 0;margin-top:0">
      <view style="padding:0 24rpx">
        <cell
          title="余额/卡"
          title-class="coupon-title bold"
          bind:click="showValueCardPop"
          value="{{cardValueAmount || '无'}}"
          is-link="{{canChooseValueCard}}"
          border="{{false}}"
          value-class="{{(totalPrice && canChooseValueCard && valueCard.length) ? 'coupon-value--red' : ''}}"
          custom-class="card-class"
        />
      </view>
      <block wx:if="{{!isStoreVip && values.length}}">
        <view class="values">
          <view class="value {{ chosen === item.id ? 'active' : '' }} {{ values.length === 1 ? 'flex-start' : ''}}" wx:for="{{ values }}" wx:key="index" data-id="{{ item.id }}" style="width: {{ item.w }}" bindtap="handleChooseValue">
            <view wx:if="{{chosen === item.id}}" class="value-icon" style="background: {{ mainColor }}; width: 40rpx">
              <image class="value-icon-img" src="https://img01.yzcdn.cn/upload_files/2023/11/19/FnPlgPzuenPh92W8rTlXhGszlnRs.png" />
            </view>
            <view wx:elif="{{ !recommend.newRecommend && index === 0 }}" class="value-icon" style="background: {{ mainColorAlpha }}; width: 80rpx">推荐</view>
            <view class="value-title {{item.t?'c111':''}}">充{{ item.v }}元</view>
            <view wx:if="{{item.t}}" class="base-value-text value-discount">{{item.t}}</view>
            <view wx:else class="base-value-text value-gift">{{ item.g }}</view>
            <image wx:if="{{values.length===1}}" class="value-background" src="https://img01.yzcdn.cn/upload_files/2023/11/06/Fq5WJJc1wVqHOa8gKNNWhfxRd9HY.png" />
          </view>
        </view>
        <image wx:if="{{values.length > 2}}" class="rectangle" src="https://img01.yzcdn.cn/upload_files/2023/11/21/Fgekze6BBN9I4pYafjTmtHh_wzBT.png" />
        <view class="protocol">
          储值即同意
          <navigator class="protocol-link" hover-class="none" url="/packages/pre-card/agreement/index" type="switch">
            《储值卡消费者服务协议》
          </navigator>
        </view>
      </block>
      <view wx:if="{{storedDiscountIsOpen && !isDone}}" class="discount-cell">
        <view class="main-info">
          <view class="left-icon" />
          <view class="discount-text text">储值专享折扣</view>
          <van-icon
            name="info-o"
            color="#969799"
            size="16px"
            bindtap="showStoredDiscount"
          />
          <view class="text right-text">{{storedDiscount}}折</view>
        </view>
        <view wx:if="{{cardNotEnough}}" class="tips">储值余额不足，不可享此折扣</view>
      </view>
    </view>
  </view>

  <block wx:if="{{showComment}}">
    <!-- custom-class无效加了这个标签 -->
    <!-- 预结单页没有备注不展示 -->
    <view wx:if="{{ isSubmitPage ? comment !== '无' ? true : false : !isDone }}" class="textarea">
      <van-field
        label="订单备注"
        value="{{ comment }}"
        placeholder="点击填写备注信息，最多20个字"
        border="{{ false }}"
        bind:change="handleChangeComment"
        maxlength="{{ 20 }}"
        label-class="textarea-label"
        title-class="textarea-title"
        readonly="{{isSubmitPage}}"
        input-align="right"
        style="--cell-horizontal-padding: 24rpx;"
      />
    </view>
  </block>

  <view wx:if="{{ orderInfo.orderNo && !isSubmitPage }}" class="order__info">
    <view class="info-title">订单信息</view>
    <view class="info-content">取餐时间：{{ appointmentTime === '-' || !appointmentTime? '立即取餐' : appointmentTime }}</view>
    <block wx:if="{{showComment}}">
      <view wx:if="{{ isDone || isSubmitPage }}" class="info-content">订单备注：{{ comment || '无' }}</view>
    </block>
    <block wx:if="{{showDeliveryTypeDesc}}">
      <view wx:if="{{isDone && deliveryTypeDesc}}" class="info-content">取货方式：{{ deliveryTypeDesc }}</view>
    </block>
    <block wx:if="{{dinerNum}}">
      <view  class="info-content">人数：{{ dinerNum }}位</view>
    </block>
    <view class="info-content">订单编号：{{ orderInfo.orderNo }}</view>
    <view class="info-content">
      创建时间：{{ orderInfo.createTimeStr }}
    </view>
    <!-- 存在没付款的情况 -->
    <view class="info-content" wx:if="{{orderInfo.payTime}}">
      付款时间：{{ orderInfo.payTimeStr }}
    </view>
    <view class="info-content" wx:if="{{orderInfo.buyWayDesc}}">
      支付方式：{{ orderInfo.buyWayDesc }}
    </view>
  </view>

</view>

<value-card-pop
  show="{{showValueCard}}"
  recommend-detail="{{ recommendDetail }}"
  stored-value-and-card="{{storedValueAndCard}}"
  default-select-card="{{valueCard}}"
  total-price="{{totalPrice}}"
  value-card-btn-disabled="{{valueCardBtnDisabled}}"
  bind:valueCardClose="handleValueCardClose"
  bind:valueCardChange="handleValueCardChange"
  bind:valueCardSubmit="handleValueCardSubmit"
/>

<template name="unavailable-goods-list">
  <view class="available-box">
    <view class="unavailable-box__title">
      失效{{ unavailableGoodsList.length }}件商品，不计入支付金额
    </view>
    <order-intro
      wx:for="{{unavailableGoodsList}}"
      wx:key="index"
      goods="{{item}}"
      unavailable
      img-width="{{112}}"
    />
  </view>
</template>

<wxs src="./util.wxs" module="util" />
<wxs src="retail/util/money.wxs" module="money" />


