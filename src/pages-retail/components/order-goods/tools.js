import { getReatilSource } from 'retail/util/global';

const app = getApp();

/**
 * 使用setTimeout模拟setInterval 取代async轮询
 */

const returnTrue = () => true;

export function interval(options) {
  const fn = options.fn || Promise.resolve.bind(Promise);
  const count = options.count || 30;
  const delay = options.delay || 1000;
  const validator = options.validator || returnTrue;

  return new Promise((resolve) => {
    fn()
      .then((result) => {
        const validated = validator(result);
        if (validated) return resolve(validated);

        let i = 0;

        function inner() {
          fn().then((result) => {
            const validated = validator(result);
            if (validated || ++i === count) {
              return resolve(validated);
            } else {
              setTimeout(inner, delay);
            }
          });
        }

        setTimeout(inner, delay);
      })
      .catch(() => resolve(false));
  });
}

export const getValueCardWidth = (len) => {
  if (len === 1) return 654;
  if (len === 2) return 320;
  return 372;
};

export const processPack = (val) => {
  if (!val) return;

  return val.giftPacks.map((pack) => {
    const { amount, giftPackName: name, giftPackId: id, freegoSlogan } = pack;

    return {
      amount,
      name,
      id,
      freegoSlogan,
      detail: pack,
      rules: pack.ruleRights,
    };
  });
};

export const getDiscountText = (rule, price, decrease, suffix = false) => {
  const { rechargeType, discount, reduction } = rule;

  if (rechargeType === 3) {
    let text = `本单减${reduction / 100}元`;
    if (suffix) {
      text += `，仅需¥${(price + decrease - reduction) / 100}`;
    }
    return text;
  }

  if (rechargeType === 1) {
    return '本单免单';
  }

  let text = `本单${discount / 10}折`;

  return price
    ? text + `，仅需${(((price + decrease) * discount) / 1e4).toFixed(2)}元`
    : text;
};

/**
 * 储值小票打印
 * @param {*} params
 * @returns
 */
export function rechargePrint(billNo) {
  return app.retailRequest({
    path: '/retail/h5/trade/rechargePrint',
    method: 'POST',
    data: {
      bizType: 'Prepaid',
      retailSource: getReatilSource(),
      kdtId: app.getKdtId(),
      forceNotCustomizeReceipt: true,
      enableEncode: true,
      adminId: app.getBuyerId(),
      billNo,
    },
  });
}
