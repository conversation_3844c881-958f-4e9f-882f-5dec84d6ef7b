.shop-goods {
  border-radius: 8rpx;

  &__item {
    padding: 8px 12px;
    line-height: 1.6;
    overflow: hidden;
    display: flex;
    align-items: center;
    border-radius: 8rpx;

    .goods-image {
      margin-right: 5px;
    }
  }

  &__item:first {
    margin-top: 5px;
  }

  .textarea {
    border-radius: 16rpx;
    margin-top: 24rpx;
    overflow: hidden;
    &-label {
      font-weight: 500;
      font-size: 28rpx;
      color: #323233;
    }
    &-title {
      display: flex;
      align-items: center;
    }
  }

  .order__info {
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    font-size: 24rpx;
    background: white;
    color: #969799;
    display: flex;
    flex-direction: column;
    margin-top: 24rpx;
    margin-bottom: 24rpx;
  }

  .card__info {
    @extend .order__info;
    padding: 0 24rpx;

    .discount-cell {
      box-sizing: border-box;
      border-radius: 6px;
      margin-bottom: 12px;
      padding: 12px;
      background-color: #f7f8fa;
      .main-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left-icon {
          width: 16px;
          height: 16px;
          background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/stored-discount.png);
          background-size: 100%;
          margin-right: 8px;
        }
        .discount-text {
          margin-right: 4px;
        }
        .right-text {
          font-weight: 700;
          margin-left: auto;
        }
      }
      .tips {
        margin: 4px 0 0 24px;
        font-size: 12px;
        color: #646566;
      }
      .text {
        color: #323233;
        font-size: 14px;
      }
    }
  }

  .info-title {
    font-weight: 500;
    font-size: 32rpx;
    line-height: 40rpx;
    color: #323233;
    margin-bottom: 16rpx;
  }

  .info-content {
    display: block;
    margin: 4px 0;
    line-height: 1.25;
  }

  .extra-cell-value {
    font-weight: 500;
    color: #323233;
  }
}

.goods-more {
  height: 24px;
  font-size: 12px;
  color: #9b9b9b;
  text-align: center;
  margin-top: 15px;
  text:after {
    content: '';
    display: inline-block;
    width: 10rpx;
    height: 10rpx;
    margin-left: 8rpx;
    border-top: 3rpx solid #9b9b9b;
    border-right: 3rpx solid #9b9b9b;
    transform: translateY(-4rpx) rotate(135deg);
  }
}

.info {
  display: flex;
  margin-left: auto;
  align-items: baseline;
  justify-content: flex-end;
  padding-top: 30rpx;

  &.hasBorderTop {
    border-top: 1rpx solid #DCDEE0;
  }

  &__goods-count {
    font-size: 24rpx;
    color: #323233;
  }
}

.coupon, .card {
  &-class {
    padding: 10px 0 !important;
  }
  &-value-red {
    color: var(--theme-main-color) !important;
    font-weight: 500;
    &.color333 {
      color: #333333 !important;
    }
  }
  &-value-none{
    font-size: 24rpx;
  }
  &-title {
    color: #323233 !important;

    &.bold {
      font-weight: 500;
    }
  }
}

.order-goods-multiple {
  &__header {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    background-color: white;

    &-avatar {
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      margin-right: 16rpx;
    }

    &-name {
      color: #999;
      font-size: 24rpx;
      line-height: 32rpx;
    }
  }
}
