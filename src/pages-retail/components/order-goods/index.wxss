@import 'shared/common/css/helper/index.wxss';
@import './app.scss';

.unavailable-box {
  overflow: hidden;
}
.unavailable-box__title {
  color: #969799;
  font-size: 24rpx;
  line-height: 56rpx;
}
.lunch-box-money {
  color: #323233 !important;
  font-weight: 500;
  font-size: 28rpx;
  line-height: 36rpx;
}

.order-goods {
  background: #fff;
  padding: 30rpx 24rpx;
  padding-top: 24rpx;
  border-radius: 16rpx;
}

.order-ump-item {
  display: flex;
  align-items: center;
}
.discount-label {
  align-items: center;
}

.bdt1px {
  border-top: 1rpx solid #DCDEE0;
}
.card-wrapper {
  position: relative;
}

.tooltips {
  display: flex;
  align-items: flex-start;
  height: 70rpx;
  padding: 16rpx 24rpx;
  margin-bottom: -40rpx;
  border-radius: 16rpx;
  color: #fff;
  background: var(--theme-main-color);
}

.tooltips > text {
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 36rpx;
}

.tooltips-icon {
  width: 98rpx;
  height: 98rpx;
  opacity: 0.08;
  position: absolute;
  right: 20rpx;
  top: -6rpx;
  transform: rotate(14deg);
}

.values {
  position: relative;
  display: flex;
  align-items: center;
  gap: 14rpx;
  overflow-x: scroll;
  padding: 0 20rpx 0 0;
}

.values::-webkit-scrollbar {
  display: none;
}

.value.active {
  --main-color: var(--theme-main-color);
  --text-color: #fff;
  --mask-color: #fff;

  border: 2px solid var(--main-color);
}

.rectangle {
  position: absolute;
  right: 0;
  bottom: 38px;
  width: 35px;
  height: 85px;
}

.value {
  box-sizing: border-box;
  position: relative;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-flow: column;
  height: 164rpx;
  padding: 40rpx;
  border-radius: 16rpx;
  background: var(--mask-color, #f5f5f5);
  overflow: hidden;
  border: 1.5px solid #fff;
}

.value.flex-start {
  align-items: flex-start;
}

.value-background {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 238rpx;
  height: 152rpx;
}

.value:nth-child(1) {
  margin-left: 24rpx;
}

.value-icon {
  position: absolute;
  right: 0;
  top: 0;
  height: 36rpx;
  border-radius: 0 0 0 16rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 36rpx;
  text-align: center;
  color: var(--text-color, var(--theme-main-color));
  overflow: hidden;
}

.value-icon-img {
  width: 36rpx;
  height: 100%;
}

.value-title {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 44rpx;

  /* color: var(--main-color, #111); */
  color: #111;
}

.c111 {
  color: #111;
}

.base-value-text {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: PingFang SC;
  line-height: 40rpx;
  text-align: center;
}

.value-gift {
  font-size: 28rpx;
  font-weight: 400;

  /* color: var(--main-color, #999); */
  color: #999;
}

.value-discount {
  font-size: 28rpx;
  font-weight: 400;
  color: var(--theme-main-color);
}

.protocol {
  display: flex;
  margin: 12rpx 0 0 24rpx;
}

.protocol-link {
  color: var(--theme-main-color);
}
