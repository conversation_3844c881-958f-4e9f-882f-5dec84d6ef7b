<view
  class="ump-brief {{placedType === 1 ? 'goods-ump-brief' : placedType === 2 ? 'detail-ump-brief' : ''}}"
  bind:tap="showPopup"
>
  <view class="ump-brief-tags {{tagList.length > 0 ? 'detail-ump-brief-have-tags' : ''}}">
    <text
      wx:if="{{tagList.length > 0}}"
      wx:for="{{tagList}}"
      wx:for-item="tagItem"
      wx:key="index"
      class="ump-brief-tag {{placedType === 1 ? 'ump-brief-tag-small' : ''}} {{tagItem.level === 1 ? 'ump-brief-tag-strong' : ''}}"
    >{{tagItem.tag}}</text>
    <view wx:if="{{sales}}" class="goods-sales">
      <view>销量 {{ sales }}</view>
    </view>
  </view>
  <view class="ump-brief-more" wx:if="{{placedType === 0 && tagList.length > 0}}">
    <text>更多</text>
    <van-icon
      name="arrow-down"
      custom-style="padding-left: 4rpx;"
    />
  </view>
</view>

<popup
  position="bottom"
  z-index="{{1024}}"
  show="{{expanded}}"
  custom-class="ump-detail"
  bind:close="closePopup"
  overlay
  safe-area-inset-bottom="{{false}}"
  wx:if="{{placedType === 0}}"
>
  <view class="ump-detail-title">优惠活动</view>
  <van-icon
    name="cross"
    color="#969799"
    class="ump-detail-close"
    custom-style="position: absolute; top: 13px; right: 15px;"
    bind:click="closePopup"
  />
  <scroll-view
    scroll-y="true"
    class="ump-detail-container"
  >
    <view
      class="ump-detail-group"
      wx:if="{{umpData.length > 0}}"
    >
      <view
        class="ump-detail-label"
        wx:for="{{umpData}}"
        wx:for-item="umpItem"
        wx:key="index"
      >
        <text class="ump-detail-tag">{{umpItem.activityTypeLabel}}</text>
        <view class="ump-detail-list">
          <view
            wx:for="{{umpItem.activityDetails}}"
            wx:for-item="ump"
            wx:key="index"
            class="ump-detail-wrap"
          >
            <text class="ump-detail-text">{{ump.preferentialDesc}}</text>
            <text
              class="ump-detail-btn"
              wx:if="{{ump.extra && ump.extra.storeExchangeSkus}}"
              bind:tap="gotoExchangeGoods"
              data-extra-info="{{ump.extra}}"
            >
              查看
            </text>
          </view>
        </view>
      </view>
      <view style="height: 88rpx"></view>
    </view>
    
  </scroll-view>
</popup>
