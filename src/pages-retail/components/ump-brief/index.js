Component({
  properties: {
    discountTypeDesc: String,
    activities: Array,
    show: Boolean,
    /** 营销活动详细数据，按照活动类型分组 */
    umpData: {
      type: Object,
    },
    /** 营销活动标列表 */
    tagList: {
      type: <PERSON><PERSON>y,
      observer() {
        if (this.data.placedType === 0) {
          this.triggerEvent(
            'umpShow',
            {},
            {
              bubbles: true,
              composed: true,
            }
          );
        }
      },
    },
    /** 0: 活动通栏 1：下单页商品 2: 商品详情页面 */
    placedType: {
      type: Number,
      value: 0,
    },
    /** 销量 */
    sales: {
      type: Number,
    },
  },

  data: {
    expanded: false,
  },

  methods: {
    closePopup() {
      this.setData({
        expanded: false,
      });
    },

    showPopup() {
      if (this.data.placedType === 0) {
        this.setData({
          expanded: true,
        });
      }
    },

    gotoExchangeGoods(e) {
      const extraInfo =
        e.target?.dataset?.extraInfo ?? e.currentTarget?.dataset?.extraInfo;
      const dbid = getApp().db.set({
        pageTitle: extraInfo.pageTitle,
        exchangeGoodsList: extraInfo.storeExchangeSkus,
      });
      wx.navigateTo({
        url: `/packages/retail/exchange-goods/index?dbid=${dbid}`,
      });
    },
  },
});
