:host {
  --line-height: 48rpx;
  --title-height: 88rpx;
}

.ump-brief {
  font-size: 24rpx;
  background-color: #ffffff;
  margin-bottom: 6px;
  position: relative;

  &-tags {
    display: flex;
    align-items: flex-end;
    overflow: hidden;
  }

  .goods-sales {
    color: #969799;
    font-size: 24rpx;
    white-space: nowrap;
    align-self: flex-start;
    flex-grow: 1;
    text-align: right;
  }

  &-tag {
    font-size: 24rpx;
    display: inline-block;
    padding: 0 8rpx;
    margin-right: 8rpx;
    border-radius: 4rpx;
    color: var(--theme-main-color);
    background-color: var(--theme-main-rgb-color);
    line-height: 36rpx;
    flex-shrink: 0;
  }

  &-tag-small {
    font-size: 20rpx;
    height: 32rpx;
    line-height: 32rpx;
  }

  &-tag-strong {
    color: #ffffff;
    background-color: var(--theme-main-color);
  }

  &-more {
    position: absolute;
    right: 0;
    top: 0;
    width: 128rpx;
    height: 36rpx;
    line-height: 36rpx;
    color: #666666;
    font-size: 20rpx;
    background: linear-gradient(270deg, #FFFFFF 64.84%, rgba(255, 255, 255, 0) 100%);
    text-align: right;
  }
}

.ump-detail {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 744rpx;
  border-radius: 36rpx 36rpx 0 0;

  &-title {
    flex-shrink: 0;
    font-size: 32rpx;
    font-weight: 500;
    height: var(--title-height);
    line-height: var(--title-height);
    text-align: center;
    color: #323233;
  }

  &-close {
    font-size: 36rpx;
  }

  &-container {
    height: calc(100% - var(--title-height));
    box-sizing: border-box;
  }

  &-group {
    padding: 0 32rpx;
  }

  &-label {
    display: flex;
    align-items: flex-start;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    & + & {
      margin-top: 16rpx;
    }
  }

  &-tag {
    color: var(--theme-main-color);
    background-color: var(--theme-main-rgb-color);
    width: 88rpx;
    height: 32rpx;
    line-height: 32rpx;
    text-align: center;
    margin-right: 16rpx;
    font-size: 24rpx;
    margin-top: 4rpx;
  }

  &-list {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
  }

  &-wrap {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  &-text {
    flex-basis: 462rpx;
    font-size: 24rpx;
    color: #323233;
    line-height: 40rpx;
  }

  &-btn {
    white-space: nowrap;
    color: var(--theme-main-color);
    line-height: 36rpx;
    font-size: 24rpx;
  }
}

.goods-ump-brief {
  margin: 8rpx 0;

  .ump-brief-tags {
    height: 36rpx;
    flex-wrap: wrap;
  }
}

.detail-ump-brief {
  margin-bottom: 0;

  .detail-ump-brief-have-tags {
    margin-bottom: -16rpx;
  }

  .ump-brief-tags {
    flex-wrap: wrap;
    overflow: visible;
    height: auto;
  }

  .ump-brief-tag {
    margin-bottom: 16rpx;
  }
}
