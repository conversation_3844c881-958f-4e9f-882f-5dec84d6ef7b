<!--储值余额和储值卡组件-->
<view class="value-card {{ disabled ? 'value-card-disabled' : '' }} {{cardType === cardTypeEnum.STORED_VALUE ? 'stored-value' : 'stored-card'}}" style="--theme-main-color-liner: {{themeMainColorLinear}}">
  <view class="value-card__info">
    <view class="value-card__info__content">
      <view class="value-card__info__content-title">{{card.cardName}}</view>
      <price price="{{ card.balance }}" style="--price-color:{{cardType === cardTypeEnum.STORED_VALUE ? '#fff' : '#323233'}};--price-font-size: 30px;--prefix-margin: 2px" />
    </view>

    <view wx:if="{{!disabled}}" class="value-card__status">
      <van-icon
        data-card="{{card}}"
        bind:click="switchSelectedStatus"
        name="{{selected ? 'checked' : 'circle'}}"
        color="{{cardType === cardTypeEnum.STORED_VALUE ? '#fff' : selected ? themeMainColor : '#979797'}}"
        size="40rpx"
      />
    </view>
  </view>
  <view class="value-card__tips" wx:if="{{card.freezingSumDnom && !card.balance && !card.isExclusion && card.usable}}">待付款订单冻结金额{{money.toYuan(card.freezingSumDnom)}}元,取消后冻结余额可释放</view>
  <view class="value-card__tips" wx:if="{{card.isExclusion && card.useSpecification}}">
    <view>{{card.useSpecification}}</view>
  </view>
</view>

<wxs src="retail/util/money.wxs" module="money" />


