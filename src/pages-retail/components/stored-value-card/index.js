import Dialog from '@vant/weapp/dist/dialog/dialog';
import {
  getThemeMainColor,
  getThemeMainColorLinear
} from 'retail/util/shelf-global';

const CARD_TYPE = {
  STORED_VALUE: 'storedValue',
  STORED_CARD: 'storedCard'
}

Component({
  properties: {
    cardType: {
      type: String,
      value: 'storedValue'
    },
    card: Object,
    disabled: {
      type: Boolean,
      value: false
    },
    defaultSelected: {
      type: Boolean,
      value: false,
      observer(value) {
        this.setData({
          selected: value
        });
      }
    }
  },
  data: {
    themeMainColorLinear: getThemeMainColorLinear(),
    themeMainColor: getThemeMainColor(),
    selected: false,
    cardTypeEnum: CARD_TYPE
  },
  methods: {
    switchSelectedStatus({ target }) {
      const {
        dataset: { card }
      } = target;
      const status = !this.data.selected;
      // 选中优惠互斥卡时需要弹出确认框，取消时或其他卡不需要弹出
      if (status && card.isExclusion) {
        Dialog.confirm({
          message:
            '若使用了该类卡，将不能享受店铺活动、优惠券、积分、储值专享折扣等优惠。是否继续使用该卡？'
        })
          .then(() => {
            this.triggerSelected(status, card);
          })
          .catch(() => {});
      } else {
        this.triggerSelected(status, card);

      }
    },
    triggerSelected(status, card) {
      this.setData({
        selected: status
      });
      this.triggerEvent('changeSelected', { selected: status, card });
    }
  }
});
