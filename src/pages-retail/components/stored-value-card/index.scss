.value-card {
  position: relative;
  padding: 24rpx;
  margin: 24rpx 0;
  color: #fff;
  border-radius: 8rpx;
  &__info {
    display: flex;
    flex: 1;
    justify-content: space-between;
    align-items: center;
    &__content {
      font-size: 14px;
      &-title {
        margin-bottom: 8rpx;
      }
    }

  }
  &__tips {
    padding-top: 16rpx;
    border-top: 1px dashed #ebedf0;
    font-size: 24rpx;
    line-height: 32rpx;
    color: #646566;
  }
}

.value-card-disabled::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: #fff;
  opacity: 0.4;
}

.stored-value {
  background: var(--theme-main-color-liner);
}
.stored-card {
  background: #fff;
  color: #323233;
}
