Component({
  properties: {
    isCustomPickUpCode: <PERSON>olean,
    orderCreatedTime: Number,
    isTradeSuccess: Boolean,
    appointmentTime: {
      type: String,
      observer: 'updateTicketTips',
    },
    pickupCode: String,
    tableCode: {
      type: String,
      observer: 'updateTicketTips',
    },
    noBorder: <PERSON><PERSON><PERSON>,
    // 订单号
    orderNo: String,
  },

  data: {
    hidden: true,
    nodes: [],
  },

  methods: {
    updateTicketTips(time) {
      // 默认值处理
      if (time === '-') {
        time = '';
      }

      const fourHourInMillSeconds = 4 * 3600 * 1000;
      const { orderCreatedTime, isTradeSuccess, isCustomPickUpCode } =
        this.data;

      let hidden = false;
      if (isTradeSuccess) {
        if (time) {
          const expire = Number(new Date(time.replace(/\d\d:\d\d-/, '')));
          hidden = Date.now() > expire + fourHourInMillSeconds;
        } else {
          hidden = Date.now() > orderCreatedTime + fourHourInMillSeconds;
        }
      }

      const node = {
        name: 'div',
        attrs: {
          style: 'color: #969799; font-size: 14px',
        },
        children: [],
      };

      if (this.data.tableCode) {
        node.children.push({
          type: 'text',
          text: '正在为您配餐，请不要离开您的座位',
        });
      }
      // else if (time) {
      //   node.children.push(
      //     {
      //       type: 'text',
      //       text: '请在&ensp;',
      //     },
      //     {
      //       type: 'node',
      //       name: 'span',
      //       attrs: {
      //         style: 'color: #323233; font-size: 14px',
      //       },
      //       children: [{ type: 'text', text: time }],
      //     },
      //     {
      //       type: 'text',
      //       text: '&ensp;到店取餐',
      //     }
      //   );
      // }
      else {
        node.children.push({
          type: 'text',
          text: `凭此${isCustomPickUpCode ? '口令' : '取餐号'}去柜台取餐`,
        });
      }
      this.setData({
        nodes: [node],
        hidden,
      });
    },
  },
});
