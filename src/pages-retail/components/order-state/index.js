import CountDown from 'utils/countdown';

Component({
  properties: {
    state: {
      type: Number,
      observer(state) {
        // 支付成功后的重置
        if (this.data.timeStr && state !== 3) {
          this.timeCountDown && this.timeCountDown.stop();
          return this.setData({
            timeStr: '',
          });
        }
      },
    },
    stateDesc: String,
    stateDescTipsV2: String, // 纯文字提示，没有逻辑
    stateDescTips: {
      type: String,
      observer(val) {
        this.timeCountDown && this.timeCountDown.stop();
        if (val && this.data.state === 3) {
          this._initCountDown(val);
        } else {
          this.setData({
            timeStr: val,
          });
        }
      },
    },
    showIcon: Boolean,
    tableCode: String,
    dinerNum: String,
    iconUrl: String,
  },
  data: {
    title: '',
    description: '',
  },
  methods: {
    _initCountDown(sec) {
      this.timeCountDown = new CountDown(+sec, {
        onChange: (data, formatedData) => {
          let timeStr = '请尽快付款，';
          if (data.day) {
            timeStr += `${data.day}天`;
          }
          if (data.hour) {
            timeStr += `${formatedData.hour}时`;
          }
          if (data.minute) {
            timeStr += `${formatedData.minute}分`;
          }
          if (data.second) {
            timeStr += `${formatedData.second}秒`;
          }

          timeStr += '后订单自动关闭';
          if (this.data.state === 3 && timeStr !== this.data.timeStr) {
            this.setData({ timeStr });
          }
        },
        onEnd: () => {
          this.triggerEvent('countdownEnd');
        },
      });
    },
  },

  detached() {
    this.timeCountDown && this.timeCountDown.stop();
  },
});
