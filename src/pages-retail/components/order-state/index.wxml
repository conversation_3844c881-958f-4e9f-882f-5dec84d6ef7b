<view class="order-state">
  <view class="top-box">
    <view class="title">
      <van-icon
        wx:if="{{ showIcon && iconUrl }}"
        name="{{ iconUrl }}"
        class="icon"
        size="24px"
      />
      <text wx:if="{{tableCode}}">{{ tableCode }}桌</text>
      <text wx:else i18n-off>{{ stateDesc }}</text>
    </view>
    <view class="diner-num" wx:if="{{dinerNum}}">
      {{dinerNum}}位
    </view>
  </view>
  <view class="description" wx:if="{{ timeStr }}">{{ timeStr }}</view>
  <view class="description" wx:if="{{ stateDescTipsV2 }}">{{ stateDescTipsV2 }}</view>
</view>


