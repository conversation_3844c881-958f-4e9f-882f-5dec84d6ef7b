.order-state {
  display: flex;
  flex-direction: column;
  padding: 40rpx 32rpx;
  margin-bottom: 12rpx;

  .icon {
    margin-right: 8rpx;
  }

  .top-box{
    display: flex;
    align-items: center;
  }

  .title {
    font-weight: 600;
    font-size: 40rpx;
    line-height: 36rpx;
    display: flex;
    align-items: center;
  }

  .diner-num {
    font-weight: 500;
    font-size: 15px;
    color: #323233;
    padding-left: 9px;
    position: relative;
    margin-left: 9px;
    &::before {
      position: absolute;
      left: 0;
      top: 3px;
      display: block;
      content: '';
      width: 2px;
      height: 78%;
      background-color: #e0e0e0;
    }
  }

  .description {
    font-size: 24rpx;
    line-height: 36rpx;
    color: #666666;
    margin-top: 16rpx;
  }
}
