<view class="wrapper custom-class" wx:if="{{ data.pictures.length }}">
  <block wx:if="{{ data.templateType === 2 }}">
    <view
      wx:for="{{ data.pictures || [] }}"
      wx:for-item="picture"
      wx:key="picture"
      wx:for-index="idx"
      class="picture-block"
    >
      <image
        src="{{ picture }}"
        mode="widthFix"
        lazy-load
        bind:tap="showSwiperGoodsDetail"
        data-goods-id="{{data.images ? data.images[idx].goodsId : 0}}"
      />
    </view>
  </block>
  <block wx:else>
  <swiper
      class="pictures"
      autoplay
      circular
      interval="3000"
      indicator-dots="{{ data.templateType !== 1 && data.indicatorType === 1 && (data.pictures && data.pictures.length > 1) }}"
      bindchange="swiperChange"
      next-margin="{{ data.templateType === 1 ? '16rpx' : '0' }}"
    >
      <swiper-item
        wx:for="{{ data.pictures || [] }}"
        wx:for-item="picture"
        wx:key="picture"
        wx:for-index="idx"
      >
        <image
          class="picture-image {{ data.templateType === 1 ? 'large' : '' }}"
          src="{{ picture }}"
          mode="aspectFill"
          lazy-load
          bind:tap="showSwiperGoodsDetail"
          data-goods-id="{{data.images ? data.images[idx].goodsId : 0}}"
        />
      </swiper-item>
    </swiper>

    <view class="dots-list" wx:if="{{ data.templateType !== 1 && data.indicatorType !== 1 && (data.pictures && data.pictures.length > 1) }}">
      <block wx:for="{{ data.pictures }}" wx:key="picture">
        <view class="dot {{ index === current ? 'active' : '' }}" />
      </block>
    </view>
  </block>
</view>
