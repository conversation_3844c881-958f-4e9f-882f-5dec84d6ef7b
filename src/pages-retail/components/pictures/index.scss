.wrapper {
  position: relative;
  margin: 32rpx;
  background-color: #fff;
}

.pictures {
  height: 280rpx;
  overflow: hidden;
  transform: translateY(0);

  .picture-image {
    box-sizing: border-box;
    height: 280rpx;
    width: 100%;
    border-radius: 16rpx;

    &.large {
      padding-right: 32rpx;
    }
  }
}

.picture-block {
  font-size: 0;
  text-align: center;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 20rpx hsla(0, 0%, 49%, .16);

  &:not(:last-child) {
    margin-bottom: 16rpx;
  }

  image {
    width: 100%;
    border-radius: 16rpx;
  }
}

.dots-list {
  display: flex;
  position: absolute;
  bottom: 16rpx;
  left: 50%;
  transform: translateX(-50%);

  .dot {
    height: 4rpx;
    width: 16rpx;
    background-color: rgba(50, 50, 51, .2);

    &:not(:last-child) {
      margin-right: 8rpx;
    }

    &.active {
      background-color: #fff;
    }
  }
}
