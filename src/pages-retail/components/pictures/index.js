Component({
  externalClasses: ['custom-class'],
  properties: {
    data: Object
  },

  data: {
    current: 0
  },

  methods: {
    swiperChange(e) {
      this.setData({
        current: e.detail.current
      });
    },
    showSwiperGoodsDetail({ currentTarget: { dataset } }) {
      if (!dataset.goodsId) return;
      dataset.images = this.data.data.images;
      this.triggerEvent('showSwiperGoodsDetail', dataset, {
        bubbles: true,
        composed: true
      });
    }
  }
});
