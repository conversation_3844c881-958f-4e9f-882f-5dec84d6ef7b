:host {
  --buttonRadius: 44rpx;
  --tapRadius: 44px;
  --offset: 8rpx;
  --theme-main-color: #1989fa;
}

.input-number {
  display: flex;
  align-items: center;
  color: #666666;
  transform: translate(var(--offset), var(--offset));
}

.input-number__icon-minus,
.input-number__icon-add {
  position: relative;
  width: var(--buttonRadius);
  height: var(--buttonRadius);
  line-height: var(--buttonRadius);
  border-radius: 50%;
  background-color: #38f;
  z-index: 10;
  color: #fff;
  text-align: center;
}

.input-number__icon-minus:active,
.input-number__icon-add:active {
  opacity: 0.6;
}

.input-number__icon-minus {
  background-color: #fff;
  color: #bbb;
  background-size: var(--buttonRadius);
  background-position: center;
}

.disable-minus-btn, .disable-add-btn {
  opacity: 0.5;
}

.stepper-minus-btn,
.stepper-add-btn {
  width: var(--buttonRadius);
  height: var(--buttonRadius);
  border-radius: var(--buttonRadius);
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--theme-main-color);
  position: relative;
  background: #fff;
}

.stepper-minus-btn::after,
.stepper-add-btn::after {
  content: '';
  height: 1px;
  width: 60%;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background: var(--theme-main-color);
}

.stepper-add-btn {
  background: var(--theme-main-color);
}

.stepper-add-btn::after {
  background: #fff;
}

.stepper-add-btn::before {
  content: '';
  height: 60%;
  width: 1px;
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  background: #fff;
}

.input-number__icon-add {
  background-size: var(--buttonRadius);
  background-position: center;
  background-color: #fff;
}

.input-number__value {
  width: 48rpx;
  font-size: 32rpx;
  color: #333;
  text-align: center;
  margin: 0 -2px;
}

.input-number-padding {
  width: var(--tapRadius);
  height: var(--tapRadius);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all cubic-bezier(0, 0, 0.1, 0.1) 0.2s;
}

.input-number-padding.active {
  transform: translateX(0) rotate(180deg);
}
.input-number-padding.hide {
  transform: translateX(var(--tapRadius)) rotate(180deg);
}
