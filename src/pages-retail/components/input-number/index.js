import Toast from '@vant/weapp/toast/toast';

Component({
  properties: {
    externalShowMinus: {
      type: Boolean,
      value: false,
    },
    // 是否禁用掉加号按钮
    externalDisableAdd: {
      type: Boolean,
      value: false,
    },
    external: {
      type: Boolean,
      value: true,
    },
    isInput: Boolean,
    step: {
      type: Number,
      value: 1,
    },
    min: {
      type: Number,
      value: 0,
    },
    max: {
      type: Number,
      value: Number.MAX_VALUE,
    },
    value: {
      type: Number,
      value: 0,
      observer(newVal) {
        const needToShow = newVal >= 1;
        this.setData({
          isMinusShow: needToShow,
          isValueShow: needToShow,
        });
      },
    },
    isAddShow: {
      type: Boolean,
      value: true,
    },
    disabled: {
      type: Boolean,
      value: false,
    },
    /** 1 加价购换购 2满减送赠品, 只用于选换购商品和选赠品弹窗 */
    addOnType: Number,
  },
  externalClasses: ['goods-buy'],
  data: {
    isMinusShow: false,
    isValueShow: false,
  },
  methods: {
    updateInput({ detail }) {
      const { value, max } = this.data;
      const newValue = Math.floor(detail.value);

      if (value === newValue) return;

      const limit = max ?? 99;

      if (newValue > limit || newValue <= 0 || Number.isNaN(newValue)) {
        wx.showToast({
          title: `允许购买数量为1-${limit}件`,
          icon: 'none',
        });
        return this.setData({
          value: value >= max ? max : value,
        });
      }

      const type = newValue > value ? 'plus' : 'minus';
      this.triggerEvent('change', { value: newValue, oldValue: value, type });
    },

    handleNumChange(e, type) {
      const { step, min, max } = this.data;
      let { value } = this.data;
      const oldValue = value;

      if (type === 'minus') {
        value -= step;
      } else if (type === 'plus') {
        if (value < max) {
          value += step;
        } else {
          const title = value > 99 ? '最多只能购买 99 个' : '商品库存不足';
          return wx.showToast({
            title,
            icon: 'none',
          });
        }
      }

      if (
        (type === 'minus' && value < min) ||
        (type === 'plus' && value > max)
      ) {
        this.triggerEvent('overlimit', { value, type });
        return;
      }

      this.triggerEvent('change', {
        value,
        oldValue,
        type,
      });
    },

    minusNum(e) {
      const { value, min } = this.properties;
      if (this.data.disabled || value <= min) return;
      this.handleNumChange(e, 'minus');
    },

    addNum(e) {
      const { max } = this.properties;
      const { disabled, externalDisableAdd, value } = this.data;
      if (disabled || externalDisableAdd || value >= max) {
        if (externalDisableAdd || value >= max) {
          if (this.data.addOnType === 1) {
            Toast({
              message: '已达最大换购数量',
              context: this,
            });
          } else if (this.data.addOnType === 2) {
            Toast({
              message: '已达最大赠品数量',
              context: this,
            });
          }
        }

        return;
      }
      this.handleNumChange(e, 'plus');
    },
  },
});
