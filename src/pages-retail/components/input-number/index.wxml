<view style="{{isInput ? 'transform: translateX(var(--offset));' : 'transform: translate(var(--offset),var(--offset))'}}" class="input-number goods-buy">
  <view class="input-number-padding {{(externalShowMinus || isMinusShow) ? 'active' : 'hide'}}" catch:tap="minusNum">
    <view class="input-number__icon-minus">
      <view class="stepper-minus-btn {{ (value <= min || disabled) ? 'disable-minus-btn' : ''}}" />
    </view>
  </view>
  <block wx:if="{{externalShowMinus || isValueShow}}">
    <input wx:if="{{isInput}}" disabled="{{ disabled }}" value="{{value}}" class="input-number__value" type="number" bind:blur="updateInput" />
    <view class="input-number__value" wx:else>{{value}}</view>
  </block>
  <view class="input-number-padding" catch:tap="addNum">
    <view class="input-number__icon-add">
        <view class="stepper-add-btn {{ disabled || externalDisableAdd || value >= max ? 'disable-add-btn' : ''}}"></view>
    </view>
  </view>
</view>
<van-toast id="van-toast" />
