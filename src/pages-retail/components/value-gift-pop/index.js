import accDiv from '@youzan/utils/number/accDiv';
import { hexToRGB } from '../../util/shelf-global';

const app = getApp();
const formatAmount = (v) => accDiv(v, 100);

const formatPrice = (price) => {
  return price % 100 === 0 ? Math.round(price / 100) : (price / 100).toFixed(2);
};

const getInvalidClass = (isInvalid) => {
  return isInvalid ? 'item__disabled' : '';
};

const getInvalidText = (preText) => {
  return preText + '，充值后无法获赠';
};

const defalutPack = {
  amount: 0,
  giftPacks: [],
  productNo: '',
};

Component({
  properties: {
    mainColor: {
      type: String,
      observer(v) {
        this.setData({ mainColorAlpha: hexToRGB(v, 0.05) });
      },
    },
    newRecommend: <PERSON><PERSON><PERSON>,
    show: {
      type: Boolean,
      value: false,
    },
    disable: {
      type: <PERSON>olean,
      value: false,
    },
    // 默认选中的礼包
    selected: {
      type: String,
      observer(v) {
        this.setData({ chosen: v });
      },
    },
    // 档位相关信息
    product: {
      type: Object,
      value: defalutPack,
      observer(v) {
        if (!v) return;
        this.processProduct(v);
      },
    },
  },
  data: {
    chosen: '',
    amount: 0,
    isMultiple: false,
    title: '',
    single: {},
    multiple: [],
    mainColorAlpha: '',
  },
  ready() {},
  methods: {
    processProduct(v) {
      const { amount: a, packs } = v;
      if (packs.length === 0) return;

      // 多礼包
      const isMultiple = packs.length > 1;

      // 标题总金额
      const amount = formatAmount(a);
      const title = isMultiple ? `充${amount}送1个礼包，请选择` : ``;

      const data = { amount, title, isMultiple };

      if (isMultiple) {
        const multiple = this.processMultipleRights(v);
        data.multiple = multiple;
      } else {
        const single = this.processSingleRights(v);
        data.single = single;
      }

      this.setData({ ...data });
    },

    processMultipleRights(val) {
      return val.packs.map((pack) => {
        const { amount, detail, id } = pack;

        return {
          id,
          amount: formatAmount(amount),
          bonus: this.processBonusAmount(detail),
          point: this.processPoint(detail),
          coupon: this.processCoupon(detail),
          present: this.processPresent(detail),
          growPoint: this.processGrowthPoint(detail),
          memberCard: this.processMemberCard(detail),
        };
      });
    },

    // 单礼包
    processSingleRights(val) {
      const { detail } = val.packs[0];
      const list = [];
      const { ruleRights = {}, ruleCondition = {} } = detail;
      const { stages, monNum, giveGiftType, delayGiveHours } = ruleCondition;

      let periodText = '';

      // 延迟到账
      if (giveGiftType === 1) {
        periodText =
          delayGiveHours <= 23
            ? `${delayGiveHours}小时后到账`
            : `${delayGiveHours / 24}天后到账`;
      }

      const dividePeriodPrefix = stages
        ? `分${stages}个月，每月${monNum}号到账`
        : '';
      const getSuffix = (value, unit) => {
        return dividePeriodPrefix
          ? `${dividePeriodPrefix}${value / stages}${unit}`
          : '';
      };

      // 分期到账
      const {
        bonusAmountRights,
        growthPointRights,
        pointRights,
        memberCardRights,
        presentRights,
        couponRights,
      } = ruleRights;
      if (bonusAmountRights && bonusAmountRights.bonusAmount) {
        let suffix = '';

        if (bonusAmountRights.bonusValidType === 1) {
          suffix = `到账后${bonusAmountRights.bonusValidPeriod}个月内有效`;
        } else {
          suffix = getSuffix(formatAmount(bonusAmountRights.bonusAmount), '元');
        }

        list.push({
          key: '赠送金',
          value: `¥${formatAmount(bonusAmountRights.bonusAmount)}`,
          suffix,
        });
      }
      if (Array.isArray(couponRights)) {
        const couponList = couponRights.map(
          ({
            condition,
            couponName,
            preferentialCopywriting,
            preferentialType,
            denominations,
            discount,
            validTimeCopywriting,
            count,
          }) => {
            const formattedAmount = formatAmount(condition);

            let value = preferentialCopywriting.split('，')[1];
            let unit = '';
            // 1  代金券
            if (preferentialType === 1) {
              value = formatAmount(denominations);
              unit = '元';
            }
            // 2 折扣券
            if (preferentialType === 2) {
              value = accDiv(discount, 10);
              unit = '折';
            }
            // 3 兑换券
            if (preferentialType === 3) {
              value = '兑换商品';
            }

            return {
              value,
              condition: !formattedAmount
                ? '无门槛'
                : `满${formattedAmount}元使用`,
              name: couponName,
              unit,
              validTimeCopywriting,
              count,
            };
          }
        );

        const totalCnt = couponRights.reduce(
          (total, cur) => total + cur.count,
          0
        );

        list.push({
          key: '优惠券',
          value: `${totalCnt}张`,
          list: couponList,
          isCoupon: true,
          suffix: getSuffix(totalCnt, '张券'),
        });
      }
      if (pointRights && pointRights.point) {
        const key = pointRights.customPointName || '积分';
        list.push({
          key,
          value: `x${pointRights.point}`,
          suffix: getSuffix(pointRights.point, key),
        });
      }
      if (Array.isArray(memberCardRights)) {
        list.push({
          key: memberCardRights[0].cardName,
          value: `${memberCardRights.length}张`,
        });
      }
      if (presentRights && presentRights.presentName) {
        list.push({
          key: presentRights.presentName,
          value: `x1`,
        });
      }
      if (growthPointRights && growthPointRights.growthPoint) {
        list.push({
          key: '成长值',
          value: `x${growthPointRights.growthPoint}`,
          suffix: getSuffix(growthPointRights.growthPoint, '成长值'),
        });
      }
      return {
        list,
        periodText,
      };
    },

    // 多礼包 赠送金
    processBonusAmount({ ruleRights, ruleCondition }) {
      const { bonusAmountRights = {} } = ruleRights;
      bonusAmountRights.bonusAmount_FormatePrice = formatPrice(
        bonusAmountRights.bonusAmount
      );
      bonusAmountRights.suffix = this.getDividePeriodSuffix(
        ruleCondition,
        accDiv(bonusAmountRights.bonusAmount, 100),
        '元'
      );
      return bonusAmountRights;
    },

    // 多礼包 优惠券
    processCoupon({ ruleRights, ruleCondition }) {
      const { couponRights = [] } = ruleRights;
      couponRights.forEach((coupon) => {
        coupon.invalidText_FormatClass = getInvalidClass(!!coupon.invalidText);
        coupon.invalidText_FormatText = getInvalidText(coupon.invalidText);
        coupon.suffix = this.getDividePeriodSuffix(
          ruleCondition,
          coupon.count,
          '张'
        );
      });
      return couponRights;
    },

    // 多礼包 成长值
    processGrowthPoint({ ruleRights, ruleCondition }) {
      const { growthPointRights = {} } = ruleRights;

      return {
        ...growthPointRights,
        suffix: this.getDividePeriodSuffix(
          ruleCondition,
          growthPointRights.growthPoint,
          '成长值'
        ),
      };
    },

    // 多礼包 权益卡
    processMemberCard({ ruleRights }) {
      const { memberCardRights = [] } = ruleRights;
      memberCardRights.forEach((member) => {
        member.invalidText_FormatClass = getInvalidClass(!!member.invalidText);
        member.invalidText_FormatText = getInvalidText(member.invalidText);
      });
      return memberCardRights;
    },

    // 多礼包 积分
    processPoint({ ruleRights, ruleCondition }) {
      const { pointRights = {} } = ruleRights;
      return {
        ...pointRights,
        suffix: this.getDividePeriodSuffix(
          ruleCondition,
          pointRights.point,
          pointRights.customPointName || '积分'
        ),
      };
    },

    // 多礼包 赠品
    processPresent({ ruleRights }) {
      const { presentRights = {} } = ruleRights;
      presentRights.invalidText_FormatText = getInvalidText(
        presentRights.invalidText
      );
      presentRights.invalidText_FormatClass = getInvalidClass(
        !!presentRights.invalidText
      );
      return presentRights;
    },

    getDividePeriodSuffix(condition, value, unit) {
      const { stages, monNum } = condition;
      if (!stages) {
        return '';
      }
      const dividePeriodPrefix = stages
        ? `分${stages}个月，每月${monNum}号到账`
        : '';

      return dividePeriodPrefix
        ? `${dividePeriodPrefix}${value / stages}${unit}`
        : '';
    },

    handleClose() {
      this.triggerEvent('onClose');
    },

    handleClick(event) {
      const { dataset } = event.currentTarget;
      const chosen = dataset.name;

      this.triggerEvent('chooseGiftPack', { id: chosen });
      this.setData({ chosen });
    },

    handlePay() {
      const { chosen } = this.data;
      this.triggerEvent('pay', { id: chosen });
    },
  },
});
