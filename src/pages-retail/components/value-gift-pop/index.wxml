<van-popup overlay position="bottom" show="{{ show }}" custom-class="value-gift-popup" close-on-click-overlay="{{false}}" safe-area-inset-bottom="{{false}}">
  <view class="container">
    <view class="container-title" wx:if="{{title}}">{{title}}</view>
    <scroll-view scroll-y enable-passive enhanced show-scrollbar="{{false}}" class="scroll-content">
      <image class="container-close" src="https://img01.yzcdn.cn/upload_files/2023/11/21/FrrC927OXqNDlG4AcZAzP7Usf73m.png" bindtap="handleClose" />
      <!-- 多礼包展示列表 -->
      <view wx:if="{{ isMultiple }}" class="container-list">
        <view class="item" bind:tap="handleClick" wx:for="{{multiple}}" wx:key="index" data-name="{{item.id}}">
          <view style="flex-grow: 1">
            <view class="item-title">
              <image class="item-title__icon" src="https://img01.yzcdn.cn/upload_files/2023/10/12/FjLtiWka9x3K5RiEZP_q4VIltwOP.png" />
              <text class="item-title__text">充值{{ item.amount }}元礼包</text>
            </view>
            <!-- 赠送金 -->
            <view class="item-content" wx:if="{{item.bonus.bonusAmount}}">
              <text class="item-content__text">送{{item.bonus.bonusAmount_FormatePrice}}元</text>
              <text class="validity" wx:if="{{item.bonus.bonusValidType===1}}">
                (到账{{item.bonus.bonusValidPeriod}}个月后失效)
              </text>
              <text class="validity" style="color:#ccc" wx:if="{{item.bonus.suffix}}">
                {{item.bonus.suffix}}
              </text>
            </view>
            <!-- 优惠券 -->
            <block wx:if="{{item.coupon.length}}">
              <view class="{{['item-content',c.invalidText_FormatClass]}}" wx:for="{{item.coupon}}" wx:for-item="c" wx:key="index">
                <text class="item-content__text">
                  送{{ c.count }}张{{ c.slogan || c.couponName }}
                </text>
                <text class="validity" wx:if="{{c.invalidText}}">{{c.invalidText_FormatText}}</text>
                <text class="validity" wx:if="{{c.suffix}}">{{c.suffix}}</text>
              </view>
            </block>
            <!-- 赠品 -->
            <view class="{{['item-content',item.present.invalidText_FormatClass]}}" wx:if="{{item.present.presentName}}">
              <text class="item-content__text">送1份{{item.present.presentName}}</text>
              <text class="validity" wx:if="{{item.present.invalidText}}">
                {{item.present.invalidText_FormatText}}
              </text>
            </view>
            <!-- 积分 -->
            <view class="item-content" wx:if="{{item.point.point}}">
              <text class="item-content__text">
                送{{ item.point.point }} {{ item.point.customPointName || '积分' }}
              </text>
              <text class="validity" wx:if="{{item.point.suffix}}">{{item.present.suffix}}</text>
            </view>
            <!-- 权益卡 -->
            <block wx:if="{{item.memberCard}}">
              <view class="{{['item-content',item.invalidText_FormatClass]}}" wx:for="{{item.memberCard}}" wx:for-item="m" wx:key="index">
                <text class="item-content__text">送1张{{ m.cardName }}</text>
                <text class="validity" wx:if="{{m.invalidText}}">{{m.invalidText_FormatText}}</text>
              </view>
            </block>
            <!-- 成长值 -->
            <view class="item-content" wx:if="{{item.growPoint.growthPoint}}">
              <text class="item-content__text">送{{ item.growPoint.growthPoint }}成长值</text>
              <text class="validity" wx:if="{{item.growPoint.suffix}}">
                {{item.growPoint.suffix}}
              </text>
            </view>
          </view>
          <van-checkbox checked-color="{{mainColor}}" label-class="item-label" value="{{ chosen === item.id }}" />
        </view>
      </view>
      <!-- 单礼包 -->
      <block wx:else>
        <view class="container-content">
          <view class="content-title">
            <text>充值{{amount}}元</text>
          </view>
          <van-divider hairline />
          <view class="content-body">
            <view>
              <text class="base-text fw600 c323233">赠送内容</text>
              <block wx:if="{{single.periodText}}">
                <text class="divider fs24 fw400 c999">|</text>
                <text class="fs24 fw400 c999">{{single.periodText}}</text>
              </block>
            </view>
            <block wx:for="{{single.list}}" wx:key="{{item.key}}">
              <view class="body-item">
                <view>
                  <text class="base-text fw400 c666">{{item.key}}</text>
                  <block wx:if="{{item.suffix}}">
                    <text class="divider fs24 fw400 c999">|</text>
                    <text class="fs24 fw400 c999">{{item.suffix}}</text>
                  </block>
                </view>
                <text class="base-text fw600 c-main">{{item.value}}</text>
              </view>
              <block wx:if="{{item.isCoupon}}">
                <view class="coupon" style="background: {{mainColorAlpha}}" wx:for="{{item.list}}" wx:key="index">
                  <view class="coupon-left">
                    <view class="coupon-value">
                      <text class="{{item.unit ? 'amount' : 'text'}}">{{item.value}}</text>
                      <text class="fw400 fs24" wx:if="{{item.unit}}">{{item.unit}}</text>
                    </view>
                    <text class="coupon-tip c999">{{item.condition}}</text>
                  </view>
                  <view class="coupon-right">
                    <view class="coupon-title c323233">
                      <view class="text">{{item.name}}</view>
                      <text class="count c999" wx:if="{{item.count > 1}}">x{{item.count}}</text>
                    </view>
                    <text class="coupon-tip c999">{{item.validTimeCopywriting}}</text>
                  </view>
                </view>
              </block>
            </block>
          </view>
        </view>
      </block>
    </scroll-view>
    <view class="container-bottom">
      <view class="protocol">
        储值即同意
        <navigator class="protocol_link" hover-class="none" url="/packages/pre-card/agreement/index" type="switch">
          《储值卡消费者服务协议》
        </navigator>
      </view>
      <van-button round hairline custom-class="main-button" type="info" disabled="{{disable}}" bind:click="handlePay">
        {{disable ? '联系服务员充值' : newRecommend ? '充值并支付' : '立即充值'}}
      </van-button>
    </view>
  </view>
</van-popup>