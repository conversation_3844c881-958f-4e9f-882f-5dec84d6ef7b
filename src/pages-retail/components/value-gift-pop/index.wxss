.value-gift-popup {
  display: flex;
  flex-direction: column;
  border-radius: 40rpx 40rpx 0 0;
  height: 1000rpx !important;
  background-color: #f2f3f5 !important;
}

.scroll-content {
  overflow: hidden;
  height: calc(780rpx - env(safe-area-inset-bottom));
  height: calc(780rpx - constant(safe-area-inset-bottom));
}

.container {
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: 100%;

  padding: 0 24rpx;

  background-color: #fff;
}

.container-title {
  width: 100%;
  margin-top: 28rpx;

  font-size: 32rpx;
  font-weight: 500;
  line-height: 44rpx;
  min-height: 44rpx;
  text-align: center;
}

.container-close {
  position: absolute;
  top: 22rpx;
  right: 8rpx;

  width: 44rpx;
  height: 44rpx;
}

.container-list {
  padding: 30rpx 32rpx;
}

.item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
}

.item-title {
  display: flex;
  align-items: center;
  margin-bottom: 28rpx;
}

.item-title__icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 10rpx;
}

.item-title__text {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 40rpx;
  color: #323233;
}

.item-content {
  font-family: PingFang SC;
  font-size: 28rpx;
  line-height: 40rpx;
  color: #666;
}

.item-content .validity {
  font-size: 24rpx;
  color: #999;
}

.item-label {
  display: none;
}

.container-content {
  margin: 0 24rpx;
  padding-bottom: 80rpx;
}

.content-title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  padding: 96rpx 8rpx 0;

  font-family: PingFang SC;
  font-size: 48rpx;
  font-weight: 600;
}

.content-body > view:not(.coupon) {
  padding: 0 8rpx;
}

.content-body > view:nth-child(2) {
  margin-top: 32rpx;
}

.body-item {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
}

.base-text {
  font-family: PingFang SC;
  font-size: 28rpx;
  /* line-height: 40rpx; */
}

.divider {
  margin: 0 12rpx;
}

.fs24 {
  font-size: 24rpx;
}

.fw600 {
  font-weight: 600;
}

.fw500 {
  font-weight: 500;
}

.fw400 {
  font-weight: 400;
}

.c666 {
  color: #666;
}

.c999 {
  color: #999;
}

.c323233 {
  color: #323233;
}

.c-main {
  color: var(--theme-main-color);
}

.coupon {
  display: flex;
  margin-top: 16rpx;
  border-radius: 16rpx;
}

.coupon > view {
  display: flex;
  flex-flow: column;
  gap: 10px;
}

.coupon-left {
  justify-content: center;
  align-items: center;
  min-width: 224rpx;
  height: 168rpx;
  margin-right: 24rpx;
}

.coupon-right {
  flex-grow: 1;
  justify-content: center;
}

.coupon-value {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 48rpx;
  text-align: center;
  color: var(--theme-main-color);
}

.coupon-value .amount {
  font-family: PingFang SC;
  font-size: 60rpx;
  font-weight: 600;
}

.coupon-value .text {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
}

.coupon-title {
  display: flex;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 48rpx;
}

.coupon-title .count {
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: 400;
}

.coupon-title .text {
  max-width: 326rpx;
  margin-right: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.coupon-tip {
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: 400;
  line-height: 32rpx;
}

.container-bottom {
  box-sizing: border-box;
  position: absolute;
  left: 0;
  bottom: 0;

  width: 100%;
  padding: 16rpx 24rpx;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);

  box-shadow: 0px -0.5px 0px 0px #ebedf0;
}

.main-button {
  width: 100%;
  height: 96rpx !important;
  margin-top: 14rpx;
  border: none !important;
  font-size: 16px !important;
  font-weight: 600;
  background: var(--theme-main-color) !important;
}

.protocol {
  display: flex;
  margin: 0 0 0 24rpx;
  font-size: 24rpx;
  color: #969799;
}

.protocol_link {
  color: var(--theme-main-color);
}

.item__disabled {
  order: 2;
  color: #c8c9cc;
}

.item__disabled text {
  color: #c8c9cc;
}
