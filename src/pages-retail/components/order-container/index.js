const WHITE = 'white';
const BLACK = 'black';
const BLUE = '#1989fa';

Component({
  properties: {
    isOrderCreated: Boolean,
    loading: Boolean,
    useBg: Boolean,
    bgColor: {
      type: String,
      value: BLUE,
    },
    navigationStyle: {
      type: String,
    },
  },
  observers: {
    'navigationStyle,bgColor': function (val1, val2) {
      if (val1 === BLACK) {
        this.setData({
          backgroundColor: val2 || WHITE,
          frontColor: BLACK,
        });
      } else {
        this.setData({
          backgroundColor: val2 || BLUE,
          frontColor: WHITE,
        });
      }
    },
  },
  data: {
    backgroundColor: BLUE,
    frontColor: WHITE,
  },
  methods: {
    noop() {},
  },
});
