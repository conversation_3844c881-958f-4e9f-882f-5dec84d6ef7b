.member-card {
  position: relative;
  display: flex;
  margin: 32rpx;
  padding: 32rpx;
  box-sizing: border-box;
  background-color: #D09A45;
  align-items: center;
  justify-content: space-between;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  overflow: hidden;

  &__cover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-position: cover;
    background-image: url('//b.yzcdn.cn/public_files/4f052ce6375fa10abac7ab7eab716ad7.png');
  }

  &.rounded {
    border-radius: 16rpx;
  }

  &__text {
    color: #fff;
  }

  &__title {
    font-size: 40rpx;
  }

  &__btn {
    color: #D09A45;
    height: 56rpx;
    line-height: 56rpx;
    border-radius: 28rpx;
    background-color: #fff;
    font-size: 26rpx;
    margin: 0;
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__content {
    color: #fff;
    width: 100%;
    position: relative;

    &--level {
      display: flex;

      .level-name {
        font-size: 34rpx;
        font-weight: bold;
      }

      .level-icon {
        display: flex;
        align-items: center;

        &__prefix {
          width: 28rpx;
          height: 28rpx;
          background: url(//b.yzcdn.cn/public_files/74c8dd5df409556a7d481f212b36cc38.png) 50%/cover no-repeat;
          border-radius: 50%;
          margin-left: .5em;
          border: 2rpx solid #D09A45;
          overflow: hidden;
          z-index: 1;
        }

        &__content {
          position: relative;
          left: -8rpx;
          font-size: 24rpx;
          font-weight: 400;
          line-height: 1;
          padding: 0 6px;
          background-color: #fff;
          color: #D09A45;
          border-radius: 0 12rpx 12rpx 0;
          transform: scale(.833);
          transform-origin: left center;
        }
      }
    }

    &--progress {
      margin-top: 8rpx;
      height: 4rpx;
      width: 100%;
      background-color: hsla(0, 0, 100%, .2);
      overflow: hidden;
      border-radius: 2rpx;

      .progress {
        height: 100%;
        background-color: #fff;
      }
    }

    &--growth {
      position: relative;
      font-weight: bold;
      margin-top: 30rpx;

      .growth {
        font-size: 68rpx;
      }

      .text {
        font-size: 24rpx;
        margin-left: 16rpx;
      }
    }

    &--desc {
      font-size: 24rpx;
      margin-top: 40rpx;
    }
  }

  &__consume__content {
    color: #fff;
    width: 100%;
    position: relative;
    display: flex;
    justify-content: space-between;

    &__qrcode {
      display: block;
    }

    &__upgrade-paid-btn{
      position: absolute;
      right: 0;
      top: 34px;
      display: flex;
      align-items: center;
      border: 1px solid white;
      border-radius: 24rpx;
      color: white;
      font-size: 28rpx;
      background-color: rgba(255, 255, 255, 0.2);
      line-height: 48rpx;
      height: 48rpx;
      padding: 0 20rpx;
      font-weight: normal;
    }
  }

  .member-card__qrcode {
    display: flex;
    position: relative;
    align-items: center;
    font-size: 28rpx;

    &-icon {
      width: 52rpx;
      height: 52rpx;
    }
  }
}

.upgrade-paid-btn {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  align-items: center;
  border: 1px solid white;
  border-radius: 24rpx;
  color: white;
  font-size: 28rpx;
  background-color: rgba(255, 255, 255, 0.2);
  line-height: 48rpx;
  height: 48rpx;
  padding: 0 20rpx;
  font-weight: normal;

  van-icon {
    display: flex;
    align-items: center;
  }
}

.member-paid-level {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 272rpx;
  width: 100%;

  .member-card__qrcode {
    position: absolute;
    right: 0;
    top: 0;
    color: white;
  }

  .level-header {
    display: flex;
    flex-direction: column;
  }

  .level-name {
    color: white;
    font-size: 34rpx;
    font-weight: bold;
  }

  .level-date {
    font-size: 24rpx;
    color: white;
    margin-top: 16rpx;
  }

  .benefit-btn {
    position: absolute;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 32rpx;
    color: white;
    padding: 12rpx 26rpx;
    font-size: 26rpx;
    border: 1px solid white;
  }

  .benefit-num {
    color: white;
    font-size: 28rpx;
  }
}

.consume-member-card__title {
  font-size: 40rpx;
  margin-bottom: 14rpx;
}