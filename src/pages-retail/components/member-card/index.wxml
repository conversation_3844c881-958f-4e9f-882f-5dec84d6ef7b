<view
  wx:if="{{ loaded }}"
  class="member-card {{ isMember ? 'rounded' : '' }}"
  style="background-image: url({{ coverUrl }});"
>
  <!-- 付费会员优先展示 -->
  <view
    wx:if="{{ supportPaidLevel && paidLevelInfo.level && hasPaidLevel }}"
    class="member-paid-level"
    bindtap="navigateToUserCenter"
  >
    <view class="level-header">
      <text class="level-name">{{ paidLevelInfo.level.name }}</text>
      <text class="level-date" wx:if="{{ expired }}">已于 {{ endDate }} 到期</text>
      <text class="level-date" wx:elif="{{ willExpireFlag }}">将于 {{ endDate }} 到期</text>
      <text class="level-date" wx:else>有效期至 {{ endDate }}</text>
      <view class="member-card__qrcode" catch:tap="navigateToLevelCode">
        <image
          class="member-card__qrcode-icon"
          src="https://b.yzcdn.cn/retail/weapp/icons/<EMAIL>"
          alt="会员码"
          lazy-load
        />
      </view>
    </view>
    <view class="benefit-num">尊享 {{ benefitNum }} 项专享权益</view>
    <view
      class="benefit-btn"
      wx:if="{{ willExpireFlag }}"
      catch:tap="navigateToLevelDetail"
    >立即续费
    </view>
  </view>
  <!-- 免费会员开始 -->
  <block wx:else>
    <view wx:if="{{ hasCoverUrl && !hasPaidLevel }}" class="member-card__cover-overlay" />

    <!-- 消费行为会员开始 -->
    <view
      class="member-card__consume__content"
      wx:if="{{ levelGrowth.isConsume }}"
      bindtap="navigateToLevelCenter"
    >
      <view class="member-card__consume__content__title">
        <view class="consume-member-card__title">{{ levelGrowth.levelValue > 0 ? 'LV.' + levelGrowth.levelValue : '会员中心' }}</view>
        <view>{{ '点击前往会员中心' }}</view>
        <view
          class="member-card__consume__content__upgrade-paid-btn"
          catch:tap="navigateToPaidLevels"
          wx:if="{{ supportPaidLevel }}"
        >
          升级付费会员
          <van-icon name="arrow" />
        </view>
      </view>
      <view
        class="member-card__consume__content__qrcode"
        wx:if="{{ levelGrowth.levelValue > 0 }}"
        catch:tap="handleOpenPopup"
      >
        <image
          class="member-card__qrcode-icon"
          src="https://b.yzcdn.cn/retail/weapp/icons/<EMAIL>"
          alt="会员码"
          lazy-load
        />
      </view>
    </view>
    <!-- 消费行为会员结束 -->
    <!-- 成长值会员开始 -->
    <block wx:else>
      <!-- 已经是会员开始 -->
      <block wx:if="{{ isMember }}">
        <view class="member-card__content" bindtap="navigateToUserCenter">
          <!-- 卡片上部开始 -->
          <view class="member-card-header">
            <view class="member-card__content--level">
              <text class="level-name">{{ level === 0 ? '客户' : levelName }}</text>
              <view class="level-icon">
                <view class="level-icon__prefix" />
                <view class="level-icon__content">
                  Lv.{{ level }}
                </view>
              </view>
            </view>
            <view
              wx:if="{{ level > 0 }}"
              class="member-card__qrcode"
              catch:tap="handleOpenPopup"
            >
              <image
                class="member-card__qrcode-icon"
                src="https://b.yzcdn.cn/retail/weapp/icons/<EMAIL>"
                alt="会员码"
                lazy-load
              />
            </view>
          </view>
          <!-- 卡片上部结束 -->
          <!-- 卡片中部开始 -->
          <view class="member-card__content--growth">
            <text class="growth">{{ currentGrowth }} </text>
            <text class="text">成长值</text>
            <view
              class="upgrade-paid-btn"
              catch:tap="navigateToPaidLevels"
              wx:if="{{ supportPaidLevel }}"
            >
              升级付费会员
              <van-icon name="arrow" />
            </view>
          </view>
          <!-- 卡片中部结束 -->
          <view class="member-card__content--progress">
            <view class="progress" style="width: {{ progress }}%"></view>
          </view>
          <!-- 底部描述信息开始 -->
          <view
            class="member-card__content--desc"
            wx:if="{{ !isRegistryComplete }}"
            catch:tap="navigateToCompletePage"
          >
            完善资料尊享更多会员权益
          </view>
          <view class="member-card__content--desc" wx:elif="{{ isMaxLevel }}">
            已是最高等级
          </view>
          <view class="member-card__content--desc" wx:elif="{{ nextLevelGrowthGap }}">
            再获得 {{ nextLevelGrowthGap }} 成长值成为 VIP {{ level ? level + 1 : 1 }}
          </view>
          <!-- 底部描述信息结束 -->
        </view>
      </block>
      <!-- 尚未成为会员开始 -->
      <block wx:else>
        <view class="member-card__text">
          <view class="member-card__title">{{ data.title || '成为会员' }}</view>
          <view>{{ data.desc || '尽享 18 项会员权益' }}</view>
        </view>
        <view wx:if="{{ !hasMobile }}" class="member-card__btn">
          <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="becomeMember">
            立即授权
          </user-authorize>
        </view>
        <!-- <button
          class="member-card__btn"
          wx:if="{{ !hasMobile }}"
          open-type="getPhoneNumber"
          bindgetphonenumber="bindMobileAndBecomeMember"
          catch:tap="noop"
        >
          立即授权
        </button> -->
        <button
          wx:else
          class="member-card__btn"
          bind:tap="becomeMember"
        >
          领取权益
        </button>
      </block>
    </block>
    <!-- 成长值会员结束 -->
  </block>
  <!-- 免费会员结束 -->
</view>

<level-info-popup
  wx:if="{{ showPopup }}"
  show="{{ showPopup }}"
  identity="{{ identity }}"
  levelName="{{ levelName }}"
  nickName="{{ nickName }}"
  level="{{ level }}"
  bind:close="handleClosePopup"
/>

<level-info-popup
  wx:if="{{ showPaidPopup }}"
  show="{{ showPaidPopup }}"
  identity="{{ paidLevelInfo.identityNo }}"
  levelName="{{ paidLevelInfo.level.name }}"
  nickName="{{ nickName }}"
  bind:close="handleClosePaidPopup"
/>

