/**
 * 会员等级卡片组件
 *
 * - [x] 免费会员等级 (通过属性传入)
 * - [x] 付费会员等级 (组件内部调用接口)
 */

import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { computeDayCount } from '@youzan/weapp-utils/lib/time-utils';

import openWebView from 'utils/open-web-view';
import { joinMember } from 'retail/util/bind-mobile';
import { moment as formatDate } from 'utils/time';

import navigate from '@/helpers/navigate';
import { queryLevel } from './api';

const app = getApp();
const defaultCoverUrl =
  'https://img01.yzcdn.cn/public_files/1ac9bfa34153ccaf5448c02a9ca60e4e.png';

/**
 *  获取付费权益数量
 *
 * @param {Object} benefit
 * @returns {Number} num
 */
function getBenefitNum(benefit) {
  let num = 0;
  const benefitList = benefit.levelBenefit;

  if (benefitList) {
    Object.keys(benefitList).forEach(key => {
      if (key === 'diyTemplateList') {
        num += benefitList[key].length;
      } else {
        num += 1;
      }
    });
  }

  return num;
}

Component({
  properties: {
    memberInfo: Object,
    paidInfo: Object,
    data: Object
  },

  observers: {
    memberInfo(data) {
      this.transformUserInfo(data);
    },
    paidInfo(data) {
      if (!data) return;

      if (!data.enable) {
        this.setData({ loaded: true });
        return;
      }

      this.transformPaidLevelInfo(data);
    }
  },

  data: {
    isMember: false,
    hasMobile: false,
    levelName: '',
    level: 0,
    currentGrowth: '',
    isMaxLevel: false,
    nextLevelGrowthGap: '',
    showPopup: false,
    showPaidPopup: false,
    coverUrl: defaultCoverUrl,
    supportPaidLevel: false,
    paidLevelInfo: {},
    loaded: false,
    isRegistryComplete: true,
    levelGroupAlias: '',
    levelGrowth: {}
  },

  pageLifetimes: {
    show() {
      this.queryLevelInfo();
    }
  },

  attached() {
    this.queryWscLevelInfo();
  },

  methods: {
    noop() {},

    getKdtId() {
      const { chainStoreInfo = {} } = app.getShopInfoSync();
      const { isMultiOnlineShop } = chainStoreInfo;
      const kdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId();
      return kdtId;
    },

    queryLevelInfo() {
      queryLevel().then(res => {
        // 非付费会员才需要处理这两个字段
        if (!res.isPaidMember) {
          this.setData({
            isRegistryComplete: res.isRegistryComplete ?? true,
            levelGroupAlias: res.levelGroupAlias ?? ''
          });
        }
      });
    },

    queryWscLevelInfo() {
      app
        .request({
          path: '/wscuser/membercenter/level-growth.json',
          method: 'get',
          data: {
            kdt_id: this.getKdtId()
          }
        })
        .then(res => {
          this.setData({
            levelGrowth: res
          });
        })
        .catch(() => {
          this.setData({
            levelGrowth: {}
          });
        });
    },

    transformUserInfo(data) {
      if (!data) return;

      const {
        asset_info,
        customer_info,
        user_info,
        member_level_right = {}
      } = data;

      if (!asset_info || !customer_info || !user_info) return;

      // 判断是否为会员
      const isMember = customer_info.is_member !== 0;
      // 是否有手机号
      const hasMobile = user_info.mobile_flag;

      this.setData({
        isMember,
        hasMobile,
        // 会员名称
        nickName: user_info.nick_name,
        // 会员标识
        identity: member_level_right.identity_no ?? '',
        // 会员等级名称
        levelName:
          member_level_right.vip_name ||
          `VIP ${member_level_right.vip_level || 0}`,
        // 会员等级
        level: member_level_right.vip_level,
        // 到下一级需要的经验值
        nextLevelGrowthGap: member_level_right.next_growth_limit_gap,
        // 是否最高等级
        isMaxLevel: member_level_right.is_current_level_to_max || false,
        // 会员卡封面
        coverUrl: cdnImage(member_level_right.cover_url || defaultCoverUrl),
        hasCoverUrl: !!member_level_right.cover_url,
        currentGrowth: asset_info.growth_value,
        progress: member_level_right.is_current_level_to_max
          ? 100
          : (asset_info.growth_value /
              (asset_info.growth_value +
                member_level_right.next_growth_limit_gap)) *
            100
      });
    },

    transformPaidLevelInfo(data) {
      if (Object.keys(data).length) {
        const { enable, info } = data;
        const dayCount = computeDayCount(new Date(), info.termEndAt);
        const levelData = {
          supportPaidLevel: enable,
          paidLevelInfo: info,
          hasPaidLevel: enable && info.level,
          benefitNum: getBenefitNum(data.detail),
          endDate: formatDate(info.termEndAt, 'YYYY-MM-DD'),
          expired: dayCount === 0,
          willExpireFlag: dayCount <= 30,
          loaded: true
        };

        if (enable && info.level) {
          Object.assign(levelData, {
            coverUrl: 'https://b.yzcdn.cn/retail/shelf/paid_level_bg.png'
          });
        }

        this.setData(levelData);
      }
    },

    onBindSuccess() {
      this.triggerEvent('onBindSuccess');
    },

    becomeMember() {
      joinMember(
        () => {
          this.onBindSuccess();
          app.logger.log({
            et: 'custom', // 事件类型
            ei: 'bind_member_success', // 事件标识
            en: '绑定会员成功', // 事件名称
            pt: 'retailshelfhome' // 页面类型
          });
        },
        {
          kdtId: app.getKdtId(),
          member_src_way: 800,
          member_src_channel: 1000,
          need_be_member: true
        }
      );
    },

    /**
     * 重定向到会员中心
     */
    navigateToUserCenter() {
      const { isMember } = this.data;
      const kdtId = app.getKdtId();

      if (isMember) {
        openWebView(
          `https://h5.youzan.com/wscuser/memberlevel?kdt_id=${kdtId}`,
          {
            title: '会员中心'
          }
        );
      } else {
        openWebView(
          `https://h5.youzan.com/wscuser/memberlevel/mobilecheck?kdt_id=${kdtId}&sales_id=0&referee_id=0&referee_scene=0`
        );
      }
    },

    /**
     * 定向到wsc小程序会员中心

     */
    navigateToLevelCenter() {
      if (app.getKdtId() === 47999) return;
      navigate.navigate({ url: '/packages/levelcenter/free/index' });
    },

    /**
     * 跳转到付费等级页面
     */
    navigateToPaidLevels() {
      const url = `https://cashier.youzan.com/pay/wscuser_paylevel?kdt_id=${app.getKdtId()}`;
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(url)}`
        // url: '/packages/levelcenter/plus/index'
      });
    },

    /**
     * 跳转到付费等级详情
     */
    navigateToLevelDetail() {
      const alias = this.data.paidLevelInfo?.level?.levelAlias;
      const url = `https://cashier.youzan.com/pay/wscuser_paylevel?kdt_id=${app.getKdtId()}&alias=${alias}`;
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(url)}`
      });
    },

    /**
     * 会员码页面
     */
    navigateToLevelCode() {
      wx.navigateTo({
        url: `/packages/member-code/index?eT=${Date.now()}`
      });
    },

    /**
     * 跳转到填写信息页面
     */
    navigateToCompletePage() {
      // alias
      openWebView('/wscuser/levelcenter/fill', {
        query: {
          kdt_id: app.getKdtId(),
          alias: this.data.levelGroupAlias,
          eT: Date.now(),
        }
      });
    },

    logShowMemberCode() {
      app.logger.log({
        et: 'click', // 事件类型
        ei: 'show_member_code', // 事件标识
        en: '点击展示会员码', // 事件名称
        pt: 'retailshelfhome' // 页面类型
      });
    },

    /**
     * 打开会员码弹框
     */
    handleOpenPopup() {
      this.logShowMemberCode();
      this.setData({
        showPopup: true
      });
    },

    handleOpenPaidPopup() {
      this.logShowMemberCode();
      this.setData({
        showPaidPopup: true
      });
    },

    /**
     * 关闭会员码弹窗
     */
    handleClosePopup() {
      this.setData({
        showPopup: false
      });
    },

    handleClosePaidPopup() {
      this.setData({
        showPaidPopup: false
      });
    }
  }
});
