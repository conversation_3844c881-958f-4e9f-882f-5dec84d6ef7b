import { shallowCompare } from 'retail/util/base';

Component({
  properties: {
    noSwitch: Boolean,
    shopInfo: Object,
    showProgressBar: {
      type: Boolean,
      value: false,
    },
    showLocation: {
      type: Boolean,
      value: true,
    },
  },

  // 检测组件所在的 page 的生命周期，来更新店铺信息
  pageLifetimes: {
    show() {
      const { shopInfo } = getApp().getRetailConfig();
      if (shopInfo && !shallowCompare(shopInfo, this.data.shopInfo)) {
        // 微信 setData 的问题，必须浅拷贝，否则最后是同一个值
        this.setData({
          shopInfo: { ...shopInfo },
        });
        this.triggerEvent('shopchange', shopInfo);
      }
    },
  },

  methods: {
    navigateToShoplist() {
      if (this.data.noSwitch || this.data.shopInfo.isSingle) return;
      wx.navigateTo({
        url: '/packages/retail/shop-list/index',
      });
    },
  },
});
