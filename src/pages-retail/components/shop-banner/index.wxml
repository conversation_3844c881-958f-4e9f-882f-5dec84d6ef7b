<view
  class="shop-header"
  bind:tap="navigateToShoplist"
>
  <view style="display: flex; flex-direction: column; flex: 1;">
    <text class="shop-header__title">{{shopInfo.shopName}}</text>
    <view class="shop-header__address" wx:if="{{showLocation}}">
      <van-icon
        class="icon"
        name="https://su.yzcdn.cn/wsc-minapp/icon/retail/pin.svg"
      />
      <text class="subtitle">{{shopInfo.location}}</text>
    </view>
    <progress-banner
      id="progress-banner"
      wx:if="{{showProgressBar}}"
      banner-container="banner-container"
    />
  </view>
  <van-icon
    wx:if="{{!(shopInfo.isSingle || noSwitch)}}"
    name="arrow"
    color="#969799"
    class="link"
  />
</view>
