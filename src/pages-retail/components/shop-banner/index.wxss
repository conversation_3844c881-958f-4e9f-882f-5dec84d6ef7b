:host {
  --shadow: 0 2px 16px rgba(125, 126, 128, 0.16);
  --bg: white;
  --radius: 0;
}

.shop-header {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 32rpx;
  min-height: var(--header-height,160rpx);
  background: var(--bg);
  border-radius: var(--radius);
  padding-bottom: var(--header-padding-bottom,32rpx);
}

.shop-header .shop-header__title {
  font-size: 32rpx;
  line-height: 1.4;
  font-size: 500;
  margin-bottom: 16rpx;
  font-family: 'PingFang SC';
}

.shop-header .shop-header__address {
  display: flex;
  align-items: flex-start;
}

.shop-header .icon {
  font-size: 23rpx;
  margin-top: 4rpx;
}

.shop-header .subtitle {
  font-size: 24rpx;
  color: #969799;
  line-height: 1.5;
  margin-left: 10rpx;
}

.shop-header .link {
  margin-left: auto;
  flex-shrink: 0;
}

.banner-container {
  margin-top: 24rpx;
}
