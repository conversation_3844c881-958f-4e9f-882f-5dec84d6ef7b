// 通用
$font-size-list: 12 14 15 16 18 20 22 24 28 32 36 48;
@each $size in $font-size-list {
  .f-#{$size} {
    font-size: #{$size}px
  }
}

@for $color from 1 through 9 {
  .black-#{$color} {
    color: #{111111 * $color};
  }
}

@for $weight from 1 through 9 {
  .w-#{$weight * 100} {
    font-weight: $weight * 100;
  }
}

.text-ellipsis {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}



// flex 相关
@mixin flex {
  display: flex;
}
.d-flex {
  @include flex;
}

.d-flex-column {
  @include flex;
  flex-direction: column;
}


.v-align {
  align-items: center;
}

.bottom-align {
  align-items: flex-end;
}

.d-flex .pull-right {
  margin-left: auto;
}

.hv-align {
  @include flex;
  align-items: center;
  justify-content: center;
}

.no-shrink {
  flex-shrink: 0;
}

.bblue {
  background-color: #38f;
}
.cblue {
  color: #38f;
}
.red {
  color: #F40F0F;
}

/* important 大法 */
.pay-button {
  display: flex !important;
  justify-content: center;
  align-items: center;
  border-color: #1989FA  !important;
  background: #1989FA  !important;
  width: 192rpx !important;
  height: 72rpx  !important;
  color: white !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
}
