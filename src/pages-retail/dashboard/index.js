/* eslint-disable @youzan/koko/no-async-await */
/* eslint-disable @youzan/dmc/wx-check */
// 已经挪到分包
import args from '@youzan/weapp-utils/lib/args';
import { getScanCodeInitConfig } from './api';
import { checkWhiteList, fetchAppConfig } from 'retail/util/api';
import { getExtSync } from 'shared/utils/browser/ext-config';
import { initGlobalThemeType } from 'retail/util/shelf-global';
import {
  goRetailHome,
  gotoGoodsList,
  withPreOrderToDo,
  goToOnlineHomePage,
  markStart,
  markEnd,
  markHelp,
} from 'retail/util/dashboard-shared';
import cacheFetch from 'retail/util/cache-fetch';
import { assignMinappConfig } from 'retail/util/config';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import { constructParamsFromUrl } from 'retail/util/base';
import { queryQrCode } from 'retail/util/api/order-add-friends';

export const CACHE_GLOBAL_KEY = {
  needPayLaterEatDinerNum: null,
  retailConfig: {},
  scanFromTable: false,
  // scanCodeDecoConfig: null,
};

const UPDATE_OFFLINE_KDT_ID_SCENE = 'updateOfflineKdtId';
const PRE_ORDER_STATUS_SCENE = 'preOrderStatus';
const INIT_CONFIG_DATA_SCENE = 'initConfigData';
const TO_ONLINE_HOMEPAGE_SCENE = 'toOnlineHomePage';
const SHOW_DINER_NUM_SCENE = 'showDinerNum';

const ON_LAUNCH_END_SCENES = [
  UPDATE_OFFLINE_KDT_ID_SCENE,
  PRE_ORDER_STATUS_SCENE,
  INIT_CONFIG_DATA_SCENE,
  TO_ONLINE_HOMEPAGE_SCENE,
  SHOW_DINER_NUM_SCENE,
];

const PHONE_DIALOG_KEY = 'has_reject_phone_auth';
const OrderAddFriends = 'order-add-friends';

const app = getApp();

Page({
  data: {
    tableCode: '', // 桌号
    shopName: '',
    dinerNumInfo: {
      // 是否开启点餐人数
      showChooseDinerNums: false,
      // 背景图
      backgroundImg: '',
      // 自定义人数
      customDinerNums: false,
      // 默认范围
      defaultDinerNumsRange: 0,
    },
    query: {},
    globalCustomLoading: true,
    loading: false,
  },
  onLoad({ q, ...query }) {
    // 初始化全局配置
    this.initGlobalConfig();

    const fn = () => {
      this.setData({
        query,
        globalCustomLoading: app.globalData.globalCustomLoading,
      });

      this.isCheckUpgrade = true;
      this.onShow();
    };
    // 处理query
    this.convertQueryToConfig(q, query).then(fn).catch(fn);
    this.firstLoad = true;
  },
  onShow() {
    if (!this.isCheckUpgrade) {
      return;
    }
    markHelp({}, 'init');
    markStart('scan_order_retail_dashboard');

    this.init()
      .then((res) => {
        if (res) {
          // 这些场景都算扫点单首开结束点
          if (ON_LAUNCH_END_SCENES.includes(res)) {
            markEnd('scan_order_on_launch');
          }
          this.skynetLog(`扫码点单初始化,执行逻辑到-${res}`, {
            level: 'info',
          });
        }
      })
      .catch((err) => {
        this.skynetLog('扫码点单初始化失败', {
          level: 'error',
          detail: {
            errMessage: err?.message || '',
          },
        });
        goRetailHome(this.data.query);
      });
  },
  skynetLog(message, payload) {
    app.logger.skynetLog({
      appName: 'retail-node-h5',
      message: `[pages-retail] ${message}`,
      ...payload,
    });
  },
  // 顺序调用,异步数据依赖使用await,中断流程使用return true
  // query目前是共享的,改修时需要考虑清楚
  // init中return的需要标记下,方便后续排查问题
  async init() {
    const { query } = this.data;
    const { from, t } = query;
    markHelp({ from });
    // 非扫码点单直接跳转首页
    if (goRetailHome(query, 'pages-retail-show')) {
      // 自助结账 提前更新offlineKdtId
      query.kdt_id && app.updateOfflineKdtId(query.kdt_id);
      return 'goRetailHome';
    }
    // 是否无效码
    if (this.scanCodeValid(query)) return 'scanCodeValid';
    // // 初始化全局配置
    // this.initGlobalConfig();
    // 更新kdtId
    const updateOfflineKdtId = await this.updateOfflineKdtId(query);
    if (updateOfflineKdtId) return UPDATE_OFFLINE_KDT_ID_SCENE;
    //  扫码点单,判断是不是在 跳过获取手机号白名单
    this.checkWhiteList(query);

    // 扫码点单加好友
    if (![OrderAddFriends, 'home-shelf'].includes(from) && +query.s === 2) {
      const showScanAddFriends = await this.showScanAddFriends(t);
      if (showScanAddFriends) return OrderAddFriends;
    }

    // 批量处理
    const [preOrderStatus, initConfig] = await this.batchProcess({
      query,
      from,
      t,
    });
    // 预订单处理
    if (preOrderStatus) return PRE_ORDER_STATUS_SCENE;
    // 处理返回数据
    if (this.handleInitConfigData(initConfig, query))
      return INIT_CONFIG_DATA_SCENE;

    // 跳转到网店首页
    if (this.handleToOnlineHomePage({ query, initConfig }))
      return TO_ONLINE_HOMEPAGE_SCENE;

    // 就餐人数处理
    if (this.showDinerNumCart(initConfig)) return SHOW_DINER_NUM_SCENE;
    // 兜底跳转点单页
    this.setData({ loading: true });
    // 正常页面离开之后 loading 会销毁，做个兜底 3s 后关闭 loading
    setTimeout(() => {
      this.setData({ loading: false });
    }, 3000);
    gotoGoodsList(query);
  },
  // 批量处理
  batchProcess({ query, from, t }) {
    return Promise.all([
      // 跳转到预订单页面
      this.handleGoToPreOrder(query),
      // 获取扫码点单初始化配置
      getScanCodeInitConfig(from === 'home-shelf' ? '' : t),
    ]);
  },
  convertQueryToConfig(q, query) {
    const sceneStorageKey = 'shop:scene:last';
    // 配置中没有 scene 的时候从 storage 中获取
    const lastScene = wx.getStorageSync(sceneStorageKey) || 1;
    query.kdt_id = +(query.kdt_id || query.kdtId);

    // 存在老的扫码点单桌码单场景 线上例子：pages-retail/dashboard/index?scene=s%3D2%26t%3D526203
    // 存在老的扫码点单店铺码单场景 线上例子：pages-retail/dashboard/index?scene=s%3D2
    // 把scene解析出来赋值给query
    let { scene } = query;
    if (scene && typeof scene === 'string') {
      scene = decodeURIComponent(scene).split('&');
      scene = scene.reduce((res, item) => {
        const query = item.split('=');
        res[query[0]] = query[1];
        return res;
      }, {});

      Object.assign(query, scene);
    }

    // 从首页跳转进来
    const cacheTableId = app.globalData.scanCodeDecoConfig?.tableId;
    if (query.from === 'home-shelf' && cacheTableId) {
      query.t = cacheTableId;
    }

    if (q) {
      /**
       * NOTE: 解析自定义二维码链接中的参数
       * 关联自定义二维码文档：https://developers.weixin.qq.com/miniprogram/introduction/qrcode.html#%E4%BA%8C%E7%BB%B4%E7%A0%81%E8%A7%84%E5%88%99
       * 获取自定义二维码链接的参数, 自定义链接会放在onLoad中的参数`option.q`中，并且是encodeURLComponent后完整路径，
       * 比如：是encodeURLComponent(https://b.yzcdn.cn/retail/2021001185620130?kdt_id=43070480&goods_id=123123123&s=2)
       * 所以需要通过 `decodeURIComponent |> constructParamsFromUrl` 来获取到 `桌码` 和 `场景值` 和店铺 `kdt_id` 的信息
       */
      const {
        kdt_id: kdtId,
        s = 1,
        t,
        ...restParams
      } = constructParamsFromUrl(decodeURIComponent(q));

      const params = {
        s: +s,
        kdt_id: +(kdtId || query.kdt_id),
      };
      if (t) params.t = +t;

      Object.assign(query, restParams, params);
    }

    // 在有 kdtId 和 scene 的情况下，存储到对应的 storage 中
    if (query.kdt_id && +query.s) {
      wx.setStorage({
        key: sceneStorageKey,
        data: +query.s,
      });
    }
    app.globalData.scanFromTable = !!query.t;
    app.globalData.scene = +query.s || lastScene;
    app.globalData.retailConfig = {};
    // 如果有桌号,保存到全局
    if (query.t) {
      Object.assign(app.globalData.retailConfig, {
        tableInfo: {
          tableId: query.t,
        },
      });
    }
    if (+query.upgrade === 1) {
      return Promise.resolve();
    }

    return getApp()
      .request({
        path: '/retail/h5/miniprogram/scan-buy/shop/upgrade-chain.json',
        data: {
          kdtId: query.kdt_id,
          sceneKey: query.sceneKey,
          sceneKdtId: getExtSync().kdtId,
        },
      })
      .then(({ targetOfflineKdtId, hasUpgrade }) => {
        if (hasUpgrade) {
          query.kdt_id = targetOfflineKdtId;
        }
        return !0;
      })
      .catch(() => !1);
  },
  //  扫码点单,判断是不是在 跳过获取手机号白名单
  checkWhiteList(query) {
    if (+query.s !== 2) return;
    return checkWhiteList().then(({ needSkip }) => {
      // 如果在跳过获取手机号白名单店铺里, 存一个storage，可以跳过后续的弹框
      if (needSkip) {
        wx.setStorage({
          key: PHONE_DIALOG_KEY,
          data: needSkip,
        });
      }
    });
  },
  // 返回数据处理
  handleInitConfigData(initConfig, query) {
    const { tableCode = '', tableId, openShops } = initConfig ?? {};
    const { joinType, subKdtIds } = openShops || {};
    // 门店是否开启扫码点单
    if (
      joinType &&
      subKdtIds.length &&
      !~subKdtIds.indexOf(+app.getOfflineKdtId())
    ) {
      Dialog.confirm({
        message: `该门店暂不提供扫码点单服务`,
        confirmButtonText: '选择其他店铺',
        showCancelButton: false,
      }).then(() => {
        this.goToShopListPage();
      });
      return true;
    }
    // 更新门店kdtId
    if (tableId) {
      query.t = tableId;
    }

    this.setData({
      tableCode,
      query,
    });
  },
  goToShopListPage() {
    markHelp('shop-list');
    wx.redirectTo({
      url: '/packages/retail/shop-list/index',
    });
  },
  // 更新门店kdtId
  async updateOfflineKdtId(query) {
    const { kdt_id, from, t, scene } = query;
    const offlineKdtId = app.getOfflineKdtId();

    // 目前还不知道连锁 老的扫码点单桌码单场景 参数是怎么样的
    // 先不处理了。 遇到再解决
    // if (scene && !kdt_id) {
    //   this.goToShopListPage();
    //   return true;
    // }

    if (!offlineKdtId) {
      // 连锁店铺
      /**
       * !(kdt_id && from === 'scancode')
       * 复现步骤
       * 扫桌码点单之后，过了一段时间，想再点单
       * 下拉，点小程序进来，进到了店铺主页
       * 这时候再扫码，就会进店铺列表
       */
      const shouldNavigateToShopList = await this.shouldNavigateToShopList();
      if (!(kdt_id && (from === 'scancode' || t)) && shouldNavigateToShopList) {
        this.goToShopListPage();
        return true;
      }
      query.kdt_id = kdt_id || app.getKdtId() || app.getExtKdtId();
      app.updateOfflineKdtId(query.kdt_id).then((res) => {
        this.setData({
          shopName: res.shop_name,
        });
      });
    } else if (kdt_id && +kdt_id !== +offlineKdtId) {
      app.updateOfflineKdtId(kdt_id).then((res) => {
        this.setData({
          shopName: res.shop_name,
        });
      });
    } else {
      query.kdt_id = offlineKdtId;
      // app.updateKdtId(offlineKdtId, false, {
      //   mark: '804',
      // });
      this.setData({
        shopName: app.getOfflineShopInfo().shop_name,
      });
    }
    // 主题色初始化
    initGlobalThemeType(this);
  },
  // 就餐人数处理
  showDinerNumCart({ dinerNumInfo }) {
    if (dinerNumInfo && dinerNumInfo.showChooseDinerNums && this.data.query.t) {
      this.setData({ dinerNumInfo });
      return true;
    }

    this.setData({
      dinerNumInfo: {
        showChooseDinerNums: false,
      },
    });
  },
  // // 初始化配置
  initGlobalConfig() {
    Object.keys(CACHE_GLOBAL_KEY).forEach((key) => {
      app.globalData[key] = CACHE_GLOBAL_KEY[key];
    });
  },
  // 根据最终的kdtid去请求店铺配置(如自助结账购物袋配置、扫码点单的必选品配置)，并setData
  fetchConfigFromKdtId() {
    const getConfig = cacheFetch(
      fetchAppConfig,
      `appconfig:${app.getOfflineKdtId()}`
    );
    return getConfig(assignMinappConfig.bind(this)).then((res) => {
      assignMinappConfig.bind(this)(res);
      return res;
    });
  },
  // 是否跳转到预订单页面
  async handleGoToPreOrder(query) {
    let status = false;
    // 获取配置信息
    await this.fetchConfigFromKdtId();
    if (query.t && this.firstLoad) {
      Object.assign(app.globalData.retailConfig, {
        deliveryWay: 3,
      });
    }
    // 加餐才去查预订单
    if (app.globalData.retailConfig.isSupportAddMealOpen) {
      // 有桌号状态下，查询预订单, 并跳转到对应页面
      status = await withPreOrderToDo(query, {
        toGoodsList: !this.firstLoad,
      });
    }
    this.firstLoad = false;
    return status;
  },
  // 是否需要跳转到店铺列表
  shouldNavigateToShopList() {
    const offlineShopInfo = app.getOfflineShopInfo() || {};

    if (offlineShopInfo.shopRole === 2) {
      return !offlineShopInfo.offlineShopOpen;
    }
    // 门店，不跳转列表
    // 这里拿到的就是 isPureOffineShop 这个字段……不是拼错了
    // 之前用同步的写法可能拿不到，导致不会跳转到店铺列表
    return app.getShopInfo().then((res) => {
      const { chainStoreInfo = {} } = res || {};
      return chainStoreInfo.isPureOffineShop === false;
    });
  },
  // 跳转到网店首页
  handleToOnlineHomePage({ query = {}, initConfig }) {
    const { hasOnline, isUpgrade, jumpPage } = initConfig || {};
    /** 扫码点单 + 升级店铺新逻辑 */
    if (
      query.from !== 'home-shelf' &&
      isUpgrade &&
      hasOnline &&
      query.t &&
      jumpPage === 'homepage_decorate_page'
    ) {
      goToOnlineHomePage();
      return true;
    }
  },
  // 是否无效码
  scanCodeValid(query) {
    if (query.timeout && Date.now() > query.timeout) {
      Dialog.confirm({
        message: `桌码已过期，请重新扫码下单`,
        confirmButtonText: '我知道了',
        showCancelButton: false,
      }).then(() => {
        wx.exitMiniProgram();
      });
      return true;
    }
  },

  // 是否展示点单加好友活动页
  async showScanAddFriends(t) {
    const payload = {
      channelType: 1, // 适用渠道：1：扫码点单
      locationType: 1, // 显示位置：1.点单前 2.预结单页 3.支付结果页
    };
    t && (payload.tableId = t);
    const dbid = await queryQrCode(payload).then(
      (res) => {
        if (res.activityId) {
          return app.db.set({
            ...res,
            _tableCode: this.data.tableCode,
            _tableId: t,
          });
        }
      },
      () => !1
    );

    if (!dbid) return false;
    const currentPage = getCurrentPages().pop();
    const url = args.add('/packages/retailb/order/add-friends/index', {
      dbid,
      redirectUrl: encodeURIComponent(
        args.add(`/${currentPage.route}`, this.data.query)
      ),
    });
    wx.redirectTo({ url });
    return true;
  },
});
