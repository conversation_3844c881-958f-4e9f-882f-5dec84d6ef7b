import { getReatilSource } from 'retail/util/global';

const app = getApp();

/**
 * 聚合接口
 * 1. 商家维度判断，是否支持首页升级
 * 2. 获取桌号跳转页面配置
 * 3. 是否有网店判断
 * 4. 获取桌号配置
 * 5. 获取就餐人数配置
 * 6. 查询扫码点单 | 自助结账 在总部开启的门店列表
 * @param {*} tableId
 * @returns
 */
export function getScanCodeInitConfig(tableId) {
  return new Promise((resolve) => {
    const cacheTableId = app.globalData.scanCodeDecoConfig?.tableId;
    if (tableId ? tableId === cacheTableId : cacheTableId) {
      return resolve(app.globalData.scanCodeDecoConfig);
    }

    app
      .retailRequest({
        path: '/retail/h5/miniprogram/scan-code/init-config.json',
        data: {
          tableId: tableId || '',
          retailSource: getReatilSource(),
        },
      })
      .then((res) => {
        const {
          dinerNumInfo,
          hasOnline = false,
          isUpgrade = false,
          jumpPage = 'order_sheet_page',
          tableCodeConfig,
          openShops,
        } = res;
        const scanCodeDecoConfig = {
          hasOnline,
          isUpgrade,
          jumpPage,
          tableCodeConfig,
          dinerNumInfo,
          openShops,
        };
        if (tableId) {
          scanCodeDecoConfig.tableId = tableId;
          scanCodeDecoConfig.tableCode = tableCodeConfig.tableCode;
        }
        app.globalData.scanCodeDecoConfig = scanCodeDecoConfig;
        resolve(scanCodeDecoConfig);
      })
      .catch((err) => {
        console.error('扫码点单初始化聚合接口查询失败', err);
        resolve({});
      });
  });
}
