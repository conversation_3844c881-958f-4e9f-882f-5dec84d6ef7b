import getApp from 'shared/utils/get-safe-app';

const app = getApp();

export default Behavior({
  data: {
    popupBottom: 0,
    noSafeBottom: 0,
    authorizePopupCustomStyle: '',
    safeBottom: app.deviceType === 'iPhone-X',
  },

  attached() {
    app.isSwitchTab('', true).then((isTabPage) => {
      if (isTabPage) {
        this.setData({
          popupBottom: 49,
          // 暂时回退，后需修复
          // authorizePopupCustomStyle: isNewIphone()
          //   ? 'padding-bottom: 40px'
          //   : 'padding-bottom: 20px',
          authorizePopupCustomStyle: '',
          noSafeBottom: this.data.safeBottom ? 83 : 49,
        });
      }
    });
  },
});
