export const DEFAULT_ICON_COLOR = {
  ACTIVE: '#f44',
  UNACTIVATED: '#969799',
  RESET_UNACTIVE: '#666',
};

export const COLOR_MODE = {
  SHOP: 'shop',
  CUSTOM: 'custom',
};

export const NAV_STYLE = {
  DEFAULT: 1,
  MAIN_ICON: 2,
  FLOAT: 3,
};

export const USERCENTER_ROUTE = {
  WSC: 'packages/usercenter/dashboard/index',
  RETAIL: 'packages/retail/usercenter/dashboard-v2/index',
};

export const USERCENTER_REG =
  /packages\/(retail\/)?usercenter\/dashboard(-v2)?\/index/;

export const RETAIL_SHELF_PATH = 'packages/retail-shelf/shelf/index';

export const TABBAR_LIST = [
  '/pages/home/<USER>/index',
  '/pages/goods/cart/index',
  '/pages/usercenter/dashboard/index',
];

export const ALL_GOODS_ROUTE = {
  OLD: 'packages/shop/goods/all/index',
  NEW: 'packages/goods-list/all/index',
};

/** 需要走统一路由处理的路径 */
export const IN_DMC_ROUTE = {
  [`/${ALL_GOODS_ROUTE.OLD}`]: 'AllGoodsList',
};
