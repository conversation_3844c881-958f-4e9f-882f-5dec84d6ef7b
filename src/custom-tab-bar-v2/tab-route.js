import {
  PAGE_TYPE,
  ROUTE_CONF,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import {
  FEATURE_TAB_ONE,
  FEATURE_TAB_TWO,
  FEATURE_TAB_THREE,
  FEATURE_HOME,
  USER_CENTER,
} from 'shared/utils/decorate/navigate-to-feature';
import args from '@youzan/weapp-utils/lib/args';
import { USERCENTER_ROUTE, USERCENTER_REG, ALL_GOODS_ROUTE } from './constants';

/**
 * tab 路由配置映射关系
 *
 * 需求场景:
 * 两个 route 都需要使得购物车 tab 为高亮选中态
 *
 * 详细描述:
 * 目前接口下发的自定义导航配置是直接传的 route, 如 packages/goods/cart/index
 * 当 currentPageRoute 与配置下发 route 相等时, 购物车的 tab 即激活高亮
 * 此时关系为 "一对一", 一个 tab 对应一个 route
 *
 * 但当业务发生重构, 如中台化改造, 在上线过程中往往会存在新、老两个页面
 * 而新、老页面都需要关联同一 tab
 * 此时关系为 "一对多", 一个 tab 对应多个 route
 *
 * 这里就是维护 "一对多" 的关系, 以接口下发 route 作为 key 关联多个 route
 * 如遍历下述配置未匹配成功, 仍走原先 "一对一" 的逻辑
 */
const TAB_CONFIG_ROUTE_MAP = {
  [ROUTE_CONF[PAGE_TYPE.CART].TRANS]: {
    subRoutes: [ROUTE_CONF[PAGE_TYPE.CART].NEW, ROUTE_CONF[PAGE_TYPE.CART].OLD],
  },
  [FEATURE_TAB_ONE.TRANS]: {
    subRoutes: [FEATURE_TAB_ONE.TEE, FEATURE_TAB_ONE.EXT, FEATURE_TAB_ONE.OLD],
  },
  [FEATURE_TAB_TWO.TRANS]: {
    subRoutes: [FEATURE_TAB_TWO.TEE, FEATURE_TAB_TWO.EXT, FEATURE_TAB_TWO.OLD],
  },
  [FEATURE_TAB_THREE.TRANS]: {
    subRoutes: [
      FEATURE_TAB_THREE.TEE,
      FEATURE_TAB_THREE.EXT,
      FEATURE_TAB_THREE.OLD,
    ],
  },
  [FEATURE_HOME.TRANS]: {
    subRoutes: [FEATURE_HOME.TEE, FEATURE_HOME.EXT, FEATURE_HOME.OLD],
  },
  [ALL_GOODS_ROUTE.OLD]: {
    subRoutes: [ALL_GOODS_ROUTE.OLD, ALL_GOODS_ROUTE.NEW],
  },
  [USER_CENTER.TRANS]: {
    subRoutes: [USER_CENTER.PAGE],
  },
};

/**
 * 根据当前页面路径获取配置下发的路径
 * @param {*} route 配置接口下发的 tab 路径
 */
export default function getRouteMapKey(route) {
  let key = route;

  if (!TAB_CONFIG_ROUTE_MAP[key]) {
    const keys = Object.keys(TAB_CONFIG_ROUTE_MAP);

    for (let i = 0; i < keys.length; i++) {
      const { subRoutes } = TAB_CONFIG_ROUTE_MAP[keys[i]];

      if (subRoutes && subRoutes.length) {
        for (let j = 0; j < subRoutes.length; j++) {
          if (route === subRoutes[j]) {
            key = keys[i];
            break;
          }
        }
      }

      if (key !== route) break;
    }
  }

  return key;
}

export function getTabInnerPath({ customPage, pagePath }) {
  const app = getApp();
  const { isRetailApp } = app.globalData;
  // 替换老的pages路由数据为 packages，
  // 大客定制有些场景不需要替换，会多传一个customPage字段
  // 自定义链接也不需要替换，会多传一个customPage字段
  let innerPath =
    customPage ||
    pagePath.startsWith('pages/home') ||
    pagePath.startsWith('pages/common/webview-page')
      ? pagePath
      : pagePath.replace(/^pages(-|\/)/, 'packages/');

  // 零售客群小程序，点单页跳转主包
  // eslint-disable-next-line no-undef
  // if (LS_COMPILE_MODE === 'kq') {
  //   innerPath = pagePath.startsWith('packages/retail-shelf/shelf')
  //     ? pagePath.replace(/^packages\//, 'pages/')
  //     : innerPath;
  // }
  if (USERCENTER_REG.test(innerPath)) {
    if (isRetailApp && innerPath === USERCENTER_ROUTE.WSC) {
      innerPath = USERCENTER_ROUTE.RETAIL;
    }
    if (!isRetailApp && innerPath === USERCENTER_ROUTE.RETAIL) {
      innerPath = USERCENTER_ROUTE.WSC;
    }
  }

  return innerPath;
}

export function adaptorTabbarOriginList(list) {
  // eslint-disable-next-line no-undef
  if (LS_COMPILE_MODE === 'kq' || LS_COMPILE_MODE === 'ls') {
    return list.map((item) => {
      const { pagePath } = item;
      if (pagePath.startsWith('packages/retail-shelf/shelf')) {
        return {
          ...item,
          attachedId: 'retail-shelf',
          pagePath: 'packages/retail-shelf/shelf/index',
          extra: args.getAll(pagePath),
          routingType: 'switchTab',
          customPage: true,
        };
      }
      return item;
    });
  }

  return list;
}
