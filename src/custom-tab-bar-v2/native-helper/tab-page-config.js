import getAttachedIdAsync from '@youzan/wsc-tab-bar-utils/lib/weapp/getAttachedIdAsync';
import findTabBarIndex from '@youzan/wsc-tab-bar-utils/lib/weapp/findTabBarIndex';
import getNavConfigAsync from '@youzan/wsc-tab-bar-utils/lib/weapp/getNavConfigAsync';
import getUniquePageKey from '@youzan/decorate-tee/src/common/utils/unique';

import { getTabInnerPath } from '@/custom-tab-bar-v2/tab-route';

const pathPrefix = 'pages/home/<USER>/';
const pageIdMap = {
  'packages/usercenter/dashboard/index': 'usercenter',
};
// 减少体积, 缩减命名
pageIdMap[`${pathPrefix}one`] = 'h-t-1';
pageIdMap[`${pathPrefix}two`] = 'h-t-2';
pageIdMap[`${pathPrefix}three`] = 'h-t-3';
const app = getApp();

const tabPageConfig = {
  data: {
    attachedPageId: '',
    pageLoaded: false,
  },
  onLoad(options) {
    this.containerOptions = options;
    this.setAttachedPageId();
    this.onRantaPageAttached();
    this.triggerRantaTabPageReady();
    this.onPageRendered();
  },
  onShow() {
    if (this.getTabBar()) {
      this.getTabBar().setData({
        selectedIndex: findTabBarIndex(),
      });
    }
    if (this.attachedPage) {
      this.attachedPage.onShow?.();
      console.log('[Async Tab Page onShow Finished]');
    }
  },
  onUnload() {
    if (this.attachedPage) {
      this.attachedPage.onUnload?.();
      console.log('[Async Tab Page onUnload Finished]');
    }
  },
  onHide() {
    if (this.attachedPage) {
      this.attachedPage.onHide?.();
      console.log('[Async Tab Page onHide Finished]');
    }
  },
  onShareAppMessage(e) {
    if (this.attachedPage) {
      return this.attachedPage.onShareAppMessage?.(e);
    }
    return {};
  },
  onReachBottom() {
    if (this.attachedPage) {
      this.attachedPage.onReachBottom?.();
      console.log('[Async Tab Page onReachBottom Finished]');
    }
  },
  onPageScroll(e) {
    if (this.attachedPage) {
      this.attachedPage.onPageScroll?.(e);
      console.log('[Async Tab Page onPageScroll Finished]');
    }
  },
  onPullDownRefresh(e) {
    if (this.attachedPage) {
      this.attachedPage.onPullDownRefresh?.(e);
      console.log('[Async Tab Page onPullDownRefresh Finished]');
    }
  },
  /**
   * 设置需要挂载的 PageId
   */
  setAttachedPageId() {
    // 当一个 tab 页第一次被加载的时候，有三种方式
    // 1. 底部导航切换
    // 2. 其他页面的入口
    // 3. 分享卡片（或者扫码）直接进入
    if (pageIdMap[this.route]) return;
    getAttachedIdAsync()
      .then((attachedPageId) => {
        this.setData({
          attachedPageId,
          pageLoaded: false,
        });
        // 在全局写入tab页中的页面类型
        app.globalData.attachedPageId = attachedPageId;
      })
      .catch((navItem) => {
        delete app.globalData.tabBarParams;
        if (!navItem) {
          // eslint-disable-next-line @youzan/dmc/wx-check
          return wx.reLaunch({
            url: `/pages/home/<USER>/index`,
          });
        }
        const realPagePath = getTabInnerPath(navItem);
        // eslint-disable-next-line @youzan/dmc/wx-check
        wx.reLaunch({
          url: `/${realPagePath}`,
        });
      });
  },

  triggerRantaTabPageReady() {
    if (pageIdMap[this.route]) {
      app.trigger(`ranta-tab-page-ready-${getUniquePageKey()}`);
    }
  },
  /**
   * 监听页面渲染第一次渲染有效信息完成的事件
   */
  onPageRendered() {
    app.on('first-render', (opt) => {
      console.log('page-render-opt: ', opt);
      this.setData({
        pageLoaded: true,
      });
    });
    // 兜底处理，避免接入导航的页面没有触发loading消失事件，导致页面无法渲染
    setTimeout(() => {
      app.trigger('first-render', 'base');
    }, 5000);
  },

  /**
   * 监听 ranta 异步页面挂载完成的事件
   */
  onRantaPageAttached() {
    // 这里 onLoad 不能代表当前页面的异步组件（页面）也挂载成功111
    app.on(`async-page-attached-${getUniquePageKey()}`, (opt) => {
      const { rantaCtx } = opt;
      // const attachedPageId = getAttachedIdByRoute(route);
      let { attachedPageId } = this.data;
      if (!attachedPageId) {
        attachedPageId = pageIdMap[this.route];
      }
      this.attachedPage = this.selectComponent(`#${attachedPageId}`);
      const pageExclusiveData = {
        _route: this.route,
      };
      (((rantaCtx || {}).env || {}).router || {}).updateRoute?.(this.route);
      // 挂载多端属性到实例
      Object.assign(this.attachedPage[Symbol.for('vm')], pageExclusiveData);
      const { tabBarParams = {} } = app.globalData;
      let pageParams = tabBarParams[attachedPageId];
      console.log(
        '[Async Tab Page Attached Page Instance]:',
        this.attachedPage,
        tabBarParams,
        attachedPageId
      );
      // 点单宝独立逻辑
      if (attachedPageId === 'retail-shelf') {
        const { tabbarOriginList = {} } = app.globalData;
        const currentTab = tabbarOriginList.find(
          (item) => item.attachedId === 'retail-shelf'
        );
        const { extra = {} } = currentTab || {};
        const storageKey = 'ExtraShopArgs';
        const enterShopParams = wx.getStorageSync(storageKey) || {};
        wx.clearStorageSync(storageKey);
        pageParams = { ...extra, ...pageParams, ...enterShopParams };
      }
      if (attachedPageId === 'feature') {
        pageParams = { ...this.containerOptions, ...pageParams };
      }
      this.execPageLifeCycle(
        this.attachedPage,
        pageParams || this.containerOptions
      );
      delete app.globalData.tabBarParams;
    });
  },
  /**
   * 监听普通异步页面挂载完成的事件
   */
  onTabPageAttached(e) {
    this.attachedPage = this.selectComponent(`#${e.currentTarget.id}`);

    const pageExclusiveData = {
      route: this.route,
      __route__: this.route,
      ...(this.attachedPage.__freeData__ || {}),
    };
    // 需要把页面的自由Data和页面专属的属性构造出来，绑定到this.attachedPage
    Object.assign(this.attachedPage, pageExclusiveData);
    const { tabBarParams = {} } = app.globalData;
    const pageParams = tabBarParams[e.currentTarget.id];

    console.log(
      '[Async Tab Page Attached Page Instance]:',
      this.attachedPage,
      tabBarParams,
      e.currentTarget.id
    );
    this.execPageLifeCycle(
      this.attachedPage,
      pageParams || this.containerOptions
    );
    // 用完之后清理
    delete app.globalData.tabBarParams;
  },
  /**
   * 执行指定页面的生命周期方法
   * @param {*} page
   * @param {*} options
   */
  execPageLifeCycle(page, options) {
    getNavConfigAsync().then((list) => {
      const app = getApp();
      const pathConfig = list[findTabBarIndex()];
      const { spm } = pathConfig || {};
      if (spm) {
        const pages = getCurrentPages();
        const { route } = pages[pages.length - 1];
        // 这里动态设置一下当前页面 route 和挂载页面 spm 的映射关系
        // 否则会导致挂载页面匹配当前route不一致会埋 fake 日志
        app.logger.spmHelper.setMoreSpm({ [route]: spm });
      }
      page.onLoad?.(options || {});
      page.onShow?.();
      page.onReady?.();
    });
  },
};

export { tabPageConfig };
