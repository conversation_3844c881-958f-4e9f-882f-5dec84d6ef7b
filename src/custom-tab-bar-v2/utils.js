import getTabBarList from '@youzan/wsc-tab-bar-utils/lib/getTabBarList';

export function isTabPageInPageStack() {
  const pageStack = getCurrentPages() || [];
  return pageStack.some(
    ({ route }) => getTabBarList().indexOf(`/${route}`) > -1
  );
}

export function isTabPagePath(path) {
  if (!path) return false;
  const cPath = '/' + path.replace(/^\//, '');
  return getTabBarList().some((tabPage) => cPath.startsWith(tabPage));
}

export function isCurrentTabPage() {
  const pages = getCurrentPages() || [];
  const page = pages[pages.length - 1] || {};
  return isTabPagePath(`/${page.route}`);
}

export function checkShelfPage(path) {
  return path === '/packages/retail-shelf/shelf/index';
}
