import WscPage from 'pages/common/wsc-page/index';

// 热卖小程序appId
const RmAppId = 'wx63d3fc92d8fdeba2';

WscPage({
  data: {
    bizName: 'landing~cps',
    maxGoodsNumber: 10,
    recommendName: '周边好物推荐',
    layoutConfig: {
      sizeType: 7,
      borderRadiusType: 2,
      showBuyButton: true,
      buyButtonType: 3,
      buttonText: '购买',
      textStyleType: 2,
    },
  },
  // 跳转到热卖小程序
  navigateToRmMini({ detail }) {
    wx.navigateToMiniProgram({
      appId: RmAppId,
      path: `/pages/common/webview-page/index?src=${encodeURIComponent(
        detail.h5Url
      )}`,
    });
  },
});
