import args from '@youzan/weapp-utils/lib/args';
import WscPage from 'pages/common/wsc-page/index';

WscPage({
  data: {
    src: '',
    orderNo: 0
  },

  onLoad(query) {
    this.setYZData({
      orderNo: query.order_no,
      src: args.add('https://h5.youzan.com/wsctrade/order/evaluate/review', {
        order_no: query.order_no
      })
    });
  },

  handlePostMessage(event) {
    const { data = [] } = event.detail;
    const isAdded = data.some(item => item['trade:evaluate:add']);

    isAdded && this.trigger('trade:evaluate:add', this.data.orderNo);
  }
});
