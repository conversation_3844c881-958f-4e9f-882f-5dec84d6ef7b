import args from '@youzan/weapp-utils/lib/args';
import WscPage from 'pages/common/wsc-page/index';

WscPage({
  data: {
    src: '',
    orderNo: 0,
  },

  onLoad(query) {
    this.setYZData({
      orderNo: query.order_no,
      src: args.add('https://h5.youzan.com/wsctrade/order/evaluate/create', {
        ...query,
      }),
    });
  },

  handlePostMessage(event) {
    const { data = [] } = event.detail;
    const isCreated = data.some((item) => item['trade:evaluate:create']);

    isCreated && this.trigger('trade:evaluate:create', this.data.orderNo);
  },
});
