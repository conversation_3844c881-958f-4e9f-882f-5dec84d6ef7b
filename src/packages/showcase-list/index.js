import WscPage from 'pages/common/wsc-page/index';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import { showcaseItemConfig } from './config/constant';

const pageRandomNumber = makeRandomString(8);
WscPage({
  data: {
    layoutConfig: {
      layout: 3,
      imageRatio: 1,
      showSubTitle: false,
      showPrice: true,
      showBuyButton: false,
      showCornerMark: false,
      borderRadiusType: 2,
      imageFillStyle: 1,
      textStyleType: 2,
      pageMargin: 12,
      goodsMargin: 12,
    },
    recommendName: '智能商品悬浮窗',
    pageRandomNumber,
    componentIndex: 0,
    bizName: '',
    type: '', // 页面类型 足迹/热榜/推荐
    show: false,
  },

  onLoad(options) {
    const { type } = options;
    const showcaseItem = showcaseItemConfig[type];
    wx.setNavigationBarTitle({
      title: showcaseItem.title,
    });

    this.setYZData({
      type,
      show: true,
      bizName: showcaseItem.bizName,
      layoutConfig: {
        ...this.data.layoutConfig,
        showCornerMark: type === 'ranking',
      },
    });
  },
});
