<page-container
  forbid-copyright="{{ false }}"
>
  <view wx:if="{{ show }}" class="container {{ type === 'ranking' ? 'ranking-container':'' }}">
    <goods-recommend
      biz-name="{{ bizName }}"
      goods-number="{{ 20 }}"
      layout-config="{{ layoutConfig }}"
      page-random-number="{{ pageRandomNumber }}"
      component-index="{{ componentIndex }}"
      custom-recommend-name="{{ recommendName }}"
      generic:goods-item-corner-mark="corner-mark"
    />
  </view>
</page-container>
