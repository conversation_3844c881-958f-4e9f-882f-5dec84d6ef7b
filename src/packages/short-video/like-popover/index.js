import WscComponent from 'shared/common/base/wsc-component/index';

function getRandomArbitrary(min, max) {
  return Math.random() * (max - min) + min;
}

WscComponent({

  properties: {
    top: Number,
    left: Number,
  },

  data: {
    show: true
  },

  attached() {
    this.setYZData({
      rotate: Math.floor(getRandomArbitrary(-35, 35))
    });
  },

  methods: {
    handleAnimationEnd() {
      this.setYZData({ show: false });
    }
  }
});
