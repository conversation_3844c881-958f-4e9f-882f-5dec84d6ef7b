
.van-toast{
  background-color:#333 !important;
}
.swiper {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}

.cover {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
}

.short-video-wrapper {
  padding-bottom: 49px;
  background-color: #000;
}

.salesman-icon {
  bottom: 15px;
}


@keyframes hand {
  0% {
    transform: translateY(56px) rotate(-30deg)
  }

  8.3% {
    transform: translateY(56px) rotate(-30deg)
  }

  50% {
    transform: translateY(-12px) rotate(-30deg)
  }

  58.3% {
    transform: translateY(-12px) rotate(-30deg)
  }

  to {
    transform: translateY(56px) rotate(-30deg)
  }
}

.cap-tiktok__guide {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  text-align: center
}

.cap-tiktok__guide__layout {
  position: relative;
  width: 66px;
  height: 118px;
  margin: 0 auto
}

.cap-tiktok__guide__layout--line {
  position: absolute;
  opacity: 0;
  width: 16px;
  height: 32px;
  border-radius: 8px;
  transform: translateY(70px);
  background: -webkit-linear-gradient(hsla(0,0%,100%,.8),hsla(0,0%,100%,0));
  background: linear-gradient(hsla(0,0%,100%,.8),hsla(0,0%,100%,0));
  animation: line 1.2s ease-out infinite
}

.cap-tiktok__guide__layout--hand {
  position: absolute;
  left: 12px;
  width: 55px;
  height: 66px;
  transform: translateY(56px) rotate(-30deg);
  transform-origin: center;
  animation: hand 1.2s ease-out infinite
}

.cap-tiktok__guide img.guide {
  width: 70px;
  height: 120px
}

.cap-tiktok__guide--fir,
.cap-tiktok__guide--sec {
  text-align: center;
  color: #fff;
  font-size: 18px;
  line-height: 24px;
}

.van-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,.3);
  z-index: 1;
}
