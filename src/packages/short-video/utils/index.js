import { PAGE_RANDOM_KEY, JUMP_UMP_GOODS_DETAIL_MAP } from '../constants';

const app = getApp();

/**
 * 获取bannerId
 * @param { String } list/home/<USER>
 * @param { Number }
 * @return {string}
 */
export const getBannerId = (noteId, index = 0, type) => {
  return `shopnote.${noteId}~${type}~${index}~${PAGE_RANDOM_KEY}`;
};

// 绑定分销员
export const bindSalesMan = (salesmanAlias) => {
  if (!salesmanAlias) return;

  app.carmen({
    api: 'youzan.salesman.wap.customer.relation/1.0.0/add',
    query: {
      sellerFrom: salesmanAlias,
    },
    success: () => {},
  });
};

// 视频列表添加key值

export const getListWithKey = function (list) {
  return list.map((item, index) => {
    item.key = `${item.id}_${index}`;
    return item;
  });
};

// 路由跳转传参增加额外的活动标
export const handleActivityQuery = (good) => {
  let query = {};
  good?.activityInfos?.forEach((info) => {
    const { type, activityAlias, activityId } = info;
    if (type === JUMP_UMP_GOODS_DETAIL_MAP.SECKILL) {
      query = {
        umpType: type,
        umpAlias: activityAlias,
      };
    }
    if (type === JUMP_UMP_GOODS_DETAIL_MAP.HELPCUT) {
      query = {
        activityId,
        type,
      };
    }
  });

  return query;
};
