import Args from '@youzan/weapp-utils/lib/args';
import navigate from 'shared/utils/navigate';
import get from '@youzan/weapp-utils/lib/get';
import loggerBehavior from 'shared/components/showcase/behaviors/logger-behavior';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';
import WscComponent from 'shared/common/base/wsc-component/index';
import { getBannerId, handleActivityQuery } from '../utils';
import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';
import Toast from '@vant/weapp/dist/toast/toast';

function cent2yuan(value) {
  return `¥${Number(value / 100).toFixed(2)}`;
}

function getObjectFit({ videoWidth, videoHeight, windowWidth, windowHeight }) {
  let containWidth;
  let containHeight;
  let videoStyleWidth;
  let videoStyleHeight;

  const videoRatio = videoWidth / videoHeight;
  const windowRatio = windowWidth / windowHeight;

  if (videoRatio > windowRatio) {
    containWidth = windowWidth;
    containHeight = (containWidth * videoHeight) / videoWidth;
    videoStyleWidth = `${windowWidth}px`;
    videoStyleHeight = `calc(100vw * ${containHeight / containWidth})`;
  } else {
    containHeight = windowHeight;
    containWidth = (containHeight * videoWidth) / videoHeight;
    videoStyleHeight = `${windowHeight}px`;
    videoStyleWidth = `calc(100vh * ${containWidth / containHeight})`;
  }

  // contain 模式黑边小于 12 像素的话就使用 cover 模式
  if (windowWidth - containWidth < 40 && windowHeight - containHeight === 0) {
    return {
      objectFit: 'cover',
      videoStyleWidth: `${windowWidth}px`,
      videoStyleHeight: `${windowHeight}px`,
    };
  }

  return {
    objectFit: 'fill',
    videoStyleWidth,
    videoStyleHeight,
  };
}

const { platform } = wx.getSystemInfoSync();
const app = getApp();

WscComponent({
  properties: {
    isCurrent: Boolean,
    liveStreamId: Number,
    shopLogo: String,
    videoSrc: String,
    videoInfo: Object,
    salesmanInfo: Object,
    noteData: Object,
    kdtId: Number,
    appId: String,
    roomId: Number,
    hasLiving: Boolean,
  },

  data: {
    playing: true,
    showInfos: true,
    duration: 0, // 视频时长，向下取整，秒
    currentTime: 0, // 视频当前时间
    platform,
    likePopoverList: [],
    objectFit: 'contain',
    componentData: {},
    videoLoading: true,
  },

  behaviors: [loggerBehavior],

  observers: {
    isCurrent(isCurrent) {
      if (isCurrent !== this._oldIsCurrent) {
        if (isCurrent) {
          if (this.goodsParams) {
            ensureAppLogger(
              getComponentLoggerParams('view', [this.goodsParams])
            );
          }

          this.getVideoInfo();

          const { noteData = {} } = this.properties;
          if (noteData.id) {
            app.logger.log({
              et: 'custom',
              ei: 'shopnote',
              en: '店铺笔记',
              params: {
                note_id: noteData.id,
                note_type: 'short_video',
                note_model_id: 5,
                spm: `shopnote.${noteData.id}`,
              },
            });
          }
        }
      }
      this._oldIsCurrent = isCurrent;
    },
  },

  pageLifetimes: {
    show() {
      if (this.hided) {
        this.hided = false;
        if (this.data.isCurrent) {
          this.getVideoInfo();
          if (this.data.playing) {
            this.videoContext.play();
          }
        }
      }

      this.setYZData({
        swiperCurrent: 0,
      });
    },

    hide() {
      this.hided = true;
      this.videoContext.pause();
    },

    resize(res) {
      if (this.videoLoadedMetaData) {
        const { windowWidth, windowHeight } = res.size;
        this.setVideoObjectFit({
          videoWidth: this.videoWidth,
          videoHeight: this.videoHeight,
          windowWidth,
          windowHeight,
        });
      }
    },
  },

  lifetimes: {
    attached() {
      const { isCurrent, videoSrc } = this.data;

      if (videoSrc) {
        this.setYZData({ initialPlayUri: videoSrc });
      }

      if (!isCurrent) {
        this.getVideoInfo();
      }
    },

    detached() {
      this.logStop();
    },
  },

  methods: {
    handlegetcontext({ detail }) {
      this.videoContext = detail;
    },

    setItemLoggerParams(item, index) {
      const { liveStreamId } = this.data;
      const params = { ...item };
      const bannerId = getBannerId(liveStreamId, index + 1, 'goods');
      const sls = get(this.properties, 'salesmanInfo.seller', '');

      params.loggerParams = {
        goods_id: item.id,
        banner_id: bannerId,
        item_id: item.id,
        item_type: 'goods',
        note_type: 'short_video',
      };

      params.url = Args.add('/pages/goods/detail/index', {
        alias: item.alias,
        banner_id: bannerId,
        ...handleActivityQuery(item),
      });

      if (sls) {
        params.url = addSalesmanParams({ url: params.url, sls });
      }

      return params;
    },

    handleGoodsClick() {
      const { goodsInfo = {} } = this.data;
      const { url } = goodsInfo;

      this.ensureAppLogger('open_goods', goodsInfo.loggerParams);

      navigate.navigate({
        url,
      });
    },

    handleAvataClick() {
      const { hasLiving, roomId } = this.data;
      if (hasLiving) {
        // todo: 跳转至小程序页面
        const livePageUrl = `/packages/weapp-live/room/index?id=${roomId}`;
        // eslint-disable-next-line @youzan/dmc/wx-check
        wx.navigateTo({
          url: livePageUrl,
        });
      } else {
        // eslint-disable-next-line @youzan/dmc/wx-check
        wx.switchTab({
          url: '/pages/home/<USER>/index',
        });
      }
    },

    getVideoInfo() {
      const { liveStreamId, isCurrent } = this.data;

      if (liveStreamId === 0) {
        this.setYZData({ deleted: true });
        return;
      }

      // 若获取过视频信息了，不重新请求接口，直接设置当前视频信息
      if (this.data.playUri) {
        if (isCurrent) {
          const { videoCoverUrl, description, videoLabel, goodsInfo } =
            this.data;
          this.triggerEvent('setcurrentiteminfo', {
            videoCoverUrl,
            description,
            videoLabel,
            goodsInfo,
          });
        }
        return;
      }

      app
        .request({
          path: `/wscshop/shortvideo/queryShopNotePage?noteId=${liveStreamId}`,
          data: {
            kdt_id: app.getExtKdtId(),
            targetId: app.getKdtId(),
          },
        })
        .then((res) => {
          if (res?.requestData?.length === 0) {
            Toast({
              message: '短视频已失效',
              duration: 1000,
            });
            setTimeout(() => {
              // eslint-disable-next-line @youzan/dmc/wx-check
              wx.switchTab({
                url: '/pages/home/<USER>/index',
              });
            }, 1000);
            return;
          }
          const { videoUrl: playUri, thumbsUpCount, videoCoverUrl } = res;
          const { shopNoteTemplate, userThumbsStatus = 0 } = res;

          // 解析短视频数据
          let { componentData } = shopNoteTemplate;
          componentData = JSON.parse(componentData);
          const {
            description = '',
            videoLabel = '',
            videoId = '',
          } = componentData[0];
          this.videoId = videoId;

          // 解析商品数据
          const goodsInfo = res.goodsInfo.length
            ? this.setItemLoggerParams(res.goodsInfo[0], 0, 'goods')
            : {};
          goodsInfo.price = cent2yuan(goodsInfo.price);

          this.goodsParams = {
            banner_id: getBannerId(liveStreamId, 0, 'goods'),
            goods_id: goodsInfo.id,
            item_id: goodsInfo.id,
            item_type: 'goods',
          };

          if (isCurrent) {
            ensureAppLogger(
              getComponentLoggerParams('view', [this.goodsParams])
            );
            this.setYZData({
              playUri,
            });
            // 设置当前视频信息
            this.triggerEvent('setcurrentiteminfo', {
              videoCoverUrl,
              description,
              videoLabel,
              goodsInfo,
            });
          }
          this.setYZData({
            videoCoverUrl,
            description,
            videoLabel,
            goodsInfo,
            statGoodJobCount: thumbsUpCount,
            statGoodJobStatus: !!userThumbsStatus,
          });
        })
        .catch(() => {
          wx.showToast({
            icon: 'none',
            title: '获取视频失败',
          });
        });
    },

    play() {
      // 防止加载慢的情况下在划走后播放
      if (!this.data.isCurrent) return;

      if (this.videoLoadedMetaData) {
        this.videoContext.play();
        this.setYZData({
          playing: true,
        });

        this.played = true;
        this.logPlay();
      } else {
        this.waitForPlay = true;
      }
    },

    pause() {
      this.videoContext.pause();
      this.setYZData({
        playing: false,
      });
    },

    stop() {
      // f**k wechat: 在某些机型上 stop() 不能用，用这样兼容一下
      if (this.videoContext) {
        this.videoContext.stop();
        this.videoContext.pause();

        this.setYZData({
          playing: false,
        });

        this.logStop();
      }
    },

    /**
     * 播放进度变化，触发频率 250ms 一次
     */
    handleTimeUpdate(e) {
      const {
        detail: { currentTime, duration },
      } = e;

      this.setYZData({
        currentTime: Math.floor(currentTime),
        duration: Math.floor(duration),
      });
    },

    handleLoadedMetaData(e) {
      const { duration, width, height } = e.detail;
      const { windowWidth, windowHeight } = wx.getSystemInfoSync();
      this.videoWidth = width;
      this.videoHeight = height;

      this.setVideoObjectFit({
        videoWidth: this.videoWidth,
        videoHeight: this.videoHeight,
        windowWidth,
        windowHeight,
      });

      this.setYZData({
        videoLoading: false,
      });

      this.videoLoadedMetaData = true;
      if (this.waitForPlay) {
        this.play();
      }

      this.setYZData({
        duration: Math.floor(duration),
      });
    },

    setVideoObjectFit({ videoWidth, videoHeight, windowWidth, windowHeight }) {
      const { objectFit, videoStyleWidth, videoStyleHeight } = getObjectFit({
        videoWidth,
        videoHeight,
        windowWidth,
        windowHeight,
      });

      this.setYZData({
        objectFit,
        videoStyle: `width: ${videoStyleWidth}; height: ${videoStyleHeight};`,
      });
    },

    logPlay() {
      this.needLogStop = true;
      // request();
      setTimeout(() => {
        this.ensureAppLogger('logger', {
          et: 'click',
          ei: 'video_valid_played',
          en: '开始播放',
          params: {
            channel: 'showcase_shopnote',
            partner_biz_type: 1,
            partner_biz_id: app.getHQKdtId(),
            video_id: this.videoId,
          },
        });
      }, 3000);
    },

    logStop() {
      if (this.needLogStop) {
        this.needLogStop = false;
      }
    },

    toggleGoodJobStatus() {
      const { statGoodJobStatus, statGoodJobCount } = this.data;
      app
        .request({
          path: '/wscshop/shopnote/setThumbStatus.json',
          method: 'POST',
          data: {
            shopNoteId: this.properties.liveStreamId,
            thumbsStatus: Number(!statGoodJobStatus),
          },
        })
        .then((res) => {
          if (res) {
            this.setYZData({
              statGoodJobStatus: Number(!statGoodJobStatus),
              statGoodJobCount: statGoodJobStatus
                ? statGoodJobCount - 1
                : statGoodJobCount + 1,
            });
          }
        });
    },

    /**
     * 点击屏幕空白处，单击切换控制，双击点赞
     */
    handleScreenTap(e) {
      const { timeStamp, detail } = e;
      clearTimeout(this.doubleTapTimer);

      if (timeStamp - this.lastTapTimeStamp < 300) {
        // doubletap
        const { statGoodJobStatus } = this.data;
        if (!statGoodJobStatus) {
          this.toggleGoodJobStatus();
        }
        const { likePopoverList } = this.data;
        this.setYZData({
          [`likePopoverList[${likePopoverList.length}]`]: {
            top: detail.y,
            left: detail.x,
          },
        });
      } else if (this.data.playing) {
        this.stop();
      } else if (!this.data.playing) {
        this.play();
      }

      this.lastTapTimeStamp = timeStamp;
    },

    togglePlaying() {
      const {
        data: { playing },
        videoContext,
      } = this;

      playing ? videoContext.pause() : videoContext.play();

      this.setYZData({
        playing: !playing,
      });
    },

    /**
     * 拖进度条的时候暂停视频
     * 不然在 iOS 端可能会意外的把视频暂停，并且 handleSliderChange 中的 play() 也无效
     */
    handleSliderChanging(e) {
      const {
        detail: { value },
      } = e;
      const { videoContext } = this;
      videoContext.pause();
      videoContext.seek(value);
    },

    /**
     * 松开进度条开始播放。（如果本身就是 playing 状态的话）
     */
    handleSliderChange(e) {
      const {
        detail: { value },
      } = e;
      const {
        data: { playing },
        videoContext,
      } = this;
      videoContext.seek(value);
      if (playing) {
        videoContext.play();
      }
    },

    noop() {},
  },
});
