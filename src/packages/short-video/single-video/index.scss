@import '~shared/common/css/_mixins.scss';

.container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.van-toast{
  background-color:blue !important;
}
.video-container {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  flex: 1;
  z-index: -10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.video {
  width: 100%;
  height: 100%;
}

/* 安卓视频加载拉伸优化 */
.android .video {
  height: 0;
}

@keyframes loading-sprite {
  0% {
    background-position: 0;
  }

  100% {
    background-position: 100%;
  }
}

.video-loading {
  position: absolute;
  top: 0;
  bottom: 30px;
  left: 0;
  right: 0;
  margin: auto;

  /**
   * 用 gif 的话有的时候展示是接着上次隐藏时的状态播的，很怪。
   * 用 sprite 的方式展示动画，可以保证每次播放都是从头开始，另外图片从 31KB -> 21KB
   * gif 在这 https://img.yzcdn.cn/guang/weappp/20200320/loading.gif
   * 很奇怪，用 160rpx 在 3x 的 iPhone 上显示不出来。
   * loading-sprite.png 的尺寸是 10080 × 240
   */
  width: 170rpx;
  height: 170rpx;
  max-width: 100px;
  max-height: 100px;
  background-image: url('https://b.yzcdn.cn/guang/weappp/20200320/loading-sprite.png');
  background-size: cover;
  background-repeat: no-repeat;
  animation: loading-sprite 2s infinite;
  animation-timing-function: steps(41);
}

.swiper {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.subscribe {
  width: 46px;
  height: 22px;
  min-width: 46px;
  line-height: 22px;
  font-size: 12px;
  text-align: center;
  border-radius: 11px;
  background-image: linear-gradient(to left, #ff6034 0%, #ee0a24 100%);
  margin-left: 9px;
}

/* 单击过渡效果 */
.video-controls,
.infos {
  bottom: 0;
  padding-bottom: calc(constant(safe-area-inset-bottom) - 15px);
  padding-bottom: calc(env(safe-area-inset-bottom) - 15px);
  transition: opacity 0.3s ease-out;
}

.video-controls.show,
.infos.show {
  opacity: 1;
}

.video-controls.hide,
.infos.hide {
  opacity: 0;
  z-index: -1;
}

.main {
  flex: 1;
  position: relative;
}

.tips {
  position: absolute;
  bottom: 50%;
  left: 50%;
  margin: auto;
  padding: 4px 8px;
  color: #fff;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  font-size: 14px;
  transform: translateX(-50%);
}

.infos {
  display: flex;
  align-items: flex-end;
  position: absolute;
  top: 0;
  width: 100%;
  color: white;
}

.infos .left {
  flex: 1;
  font-size: 12px;
  display: flex;
  align-items: center;
  max-width: 100%;
  min-width: 100%;
  white-space: nowrap;
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0) 100%
  );
}

.infos .head {
  width: 100%;
  padding: 58rpx 32rpx 32rpx 32rpx;
}

.infos .left .head .author {
  min-width: 285px;
  display: inline;
  font-weight: bold;
  font-size: 15px;
  line-height: 24px;
  white-space: pre-wrap;
  max-height: 48px;
 
  @include multi-ellipsis(2);
}

.infos .left .head .time {
  color: rgba(237, 237, 237, 0.7);
  margin-left: 10px;
  font-size: 12px;
  font-weight: 500;
}

.infos .left .intro {
  color: #e9e9e9;
  font-weight: 500;
  line-height: 18px;
  min-height: 36px;
}

.infos .right {
  color: #fff;
  font-size: 12px;
  text-align: center;
  margin-bottom: 166px;
  position: absolute;
  right: 16rpx;
  bottom: 50rpx;
}

.infos .right .avatar {
  display: block;
  margin-bottom: 32px;
  line-height: 1;
  border-radius: 50%;
  border: 1px solid #fff;
  overflow: hidden;
}

.infos .right .avatar-df {
  display: block;
  margin-bottom: 32px;
  line-height: 1;
  border-radius: 50%;
}

.infos .right .avatar image {
  border-radius: 50%;
}

.infos .right .right-item {
  display: block;
}

.infos .right .right-item image {
  width: 40px;
  height: 40px;
  vertical-align: bottom;
  opacity: 0.95;
}

.like {
  margin-bottom: 14px;
}

.avatar image {
  width: 48px !important;
  height: 48px !important;
}

.infos .right .right-item .text {
  margin-top: 2px;
  font-weight: 500;
  line-height: 17px;
}

.infos .right .like .wrapper {
  position: relative;
  width: 40px;
  height: 40px;
  margin: auto;

  .show {
    width: 40px;
    height: 40px;
  }
}

.infos .right .like image {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;

  /* 此处的 z-index 必须要有，如果不写，在开发工具上正常，在 iOS 上会有问题。 */

  /* 给 .show 加 z-index: 1 会使点赞按钮在 popup 的蒙层之上 */

  /* 给 .hide 加 z-index: -1 会使点赞按钮在底部的黑色渐变之下，在点击的一瞬间按钮会被阴影盖住 */

  /* 给 .hide 加 z-index: 0 正好 */
}

.infos .right .like image.show {
  transition: transform 0.3s 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.27);
  transform: scale(1);
}

.infos .right .like image.hide {
  transition: transform 0.3s cubic-bezier(0.6, -0.28, 0.74, 0.05);
  transform: scale(0);
  z-index: 0;
}

.infos .right .more image {
  padding: 0 1px;
  box-sizing: border-box;
}

.video-controls {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: absolute;
  width: 100%;
  top: 0;
}

.video-controls .wrapper {
  display: flex;
  padding-left: 8px;
}

.progress-bar {
  display: flex;
  justify-content: space-between;
  flex: 1;
  align-items: center;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  margin-right: 15px;
  margin-left: 8px;
  line-height: 18px;
}

.progress-bar-text {
  min-width: 36px;
}

.progress-bar-slider {
  flex: 1;
  margin: 0 10px;
}

.popup {
  border-radius: 12px 12px 0 0;
}

/* stylelint-disable */
// van-overlay {
//   opacity: 0.4;
// }

.goods-wrapper {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.videolabel {
  margin-bottom: 8px;
  color: #dcdee0;
  font-size: 12px;
  line-height: 16px;
}

.short-video-logo {
  width: 36px;
  height: 42px;
  margin-right: 12px;
}

.short-video-goods {
  display: flex;
  padding: 4px 4px;
  width: 170px;
  border-radius: 4px;
  background-color: rgba(123, 123, 123, 0.8);
  box-sizing: border-box;
}

.short-video-image__img {
  min-width: 38px;
  max-width: 38px;
  min-height: 38px;
  max-height: 38px;
}

.short-video-image__tip {
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: left;
  overflow: hidden;
}

.short-video-image__tip-title,
.short-video-image__tip-price {
  margin-left: 4px;
  color: #fff;
}

.short-video-image__tip-title {
  line-height: 20px;
  font-size: 12px;
  max-width: 116px;

  @include multi-ellipsis(1);
}

.short-video-image__tip-price {
  margin-top: 3px;
  font-size: 14px;
  font-weight: bold;
}

.video-share-button {
  padding: 0 0;
  background: transparent;
  outline: none;
  color: #fff;
}

.video-share-button::after {
  border: none;
}
