<wxs src="./data-formatter.wxs" module="df" />

<view class="container">
  <van-toast id="van-toast" />

  <view class="main" bind:tap="handleScreenTap">
    <view class="video-container {{ platform }}">
      <async-video-simple
        wx:if="{{ kdtId }}"
        app-id="{{ appId }}"
        shop-id="{{ kdtId }}"
        kdt-id="{{ kdtId }}"
        open-id="oDpvq0LNzwhMGfiNRbq-NthY5oUo"
        custom-id="video"
        custom-class="video"
        objectFit="{{ objectFit }}"
        src="{{ initialPlayUri || playUri }}"
        loop
        controls="{{ false }}"
        show-center-play-btn="{{ false }}"
        enable-progress-gesture="{{ false }}"
        custom-style="height:100%;"
        video-style="{{ videoStyle }}"
        bindended="handleVideoEnded"
        bindloadedmetadata="handleLoadedMetaData"
        bindgetcontext="handlegetcontext"
        bindtimeupdate="handleTimeUpdate"
      />
      <view wx:if="{{ videoLoading }}" class="video-loading" />
    </view>

    <block>
      <view class="infos {{ showInfos ? 'show' : 'hide' }}">
        <view class="left">
          <view class="head" catch:tap="noop">
            <view class="goods-wrapper" catch:tap="handleGoodsClick" wx:if="{{goodsInfo.id}}">
              <image class="short-video-logo" src="https://b.yzcdn.cn/public_files/c968f8244533bb7456cac3f3bbaacac1.png"></image>
              <view class="short-video-goods">
                <image class="short-video-image__img" src="{{goodsInfo.imageUrl}}" mode="scaleToFill"></image>
                <view class="short-video-image__tip">
                  <text class="short-video-image__tip-title">{{ goodsInfo.title }}</text>
                  <text class="short-video-image__tip-price">{{ goodsInfo.price }}</text>
                </view>
              </view>
            </view>
            <view class="videolabel" wx:if="{{videoLabel}}">#{{ videoLabel }}</view>
            <view class="author van-ellipsis" catch:tap="handleAuthorTap">{{ description }}</view>
          </view>
        </view>
  
        <view class="right" catch:tap="noop">
        <block>
          <view class="right-item avatar-df" wx:if="{{hasLiving}}" bind:tap="handleAvataClick">
            <live-dynamic-effect>
              <image slot="cus-img" mode="aspectFill" src="{{shopLogo}}"></image>
            </live-dynamic-effect>
          </view>
          <view class="right-item avatar" wx:else bind:tap="handleAvataClick">
            <image mode="aspectFill" src="{{shopLogo}}"></image>
          </view>
        </block>
          
          <view class="right-item like" bind:tap="toggleGoodJobStatus">
            <view class="wrapper">
              <image class="{{ statGoodJobStatus ? 'show' : 'hide' }}" mode="aspectFit" src="https://img.yzcdn.cn/guang/weapp/20200203/detail-like-red.svg" />
              <image class="{{ statGoodJobStatus ? 'hide' : 'show' }}" mode="aspectFit" src="https://img.yzcdn.cn/guang/weapp/20200203/detail-like-white.svg" />
            </view>
            <view class="text">{{ statGoodJobCount ? statGoodJobCount : '点赞' }}</view>
          </view>
          <form-view type="default">
            <button open-type="share" data-desc="{{description}}" catch:tap="shareClick" class="video-share-button">
              <view class="right-item share" catch:tap="handleShareTap">
                <image mode="aspectFit" src="https://img.yzcdn.cn/guang/weapp/20200203/detail-share.svg" />
                <view class="text" style="font-size: 10px;">分享</view>
              </view>
            </button>
          </form-view>
        </view>
      </view>
  
      <view class="video-controls {{ !showInfos ? 'show' : 'hide' }}">
        <view class="wrapper">
          <play-button playing="{{ playing }}" catch:tap="togglePlaying" />
          <!-- max 设置为视频时长，value 设置为当前时间，一秒一个 step -->
          <view class="progress-bar" catch:tap="noop">
            <view class="progress-bar-text">{{ df.secondToTimeString(currentTime) }}</view>
            <slider
              class="progress-bar-slider"
              max="{{ duration }}"
              value="{{ currentTime }}"
              block-size="12"
              activeColor="#fff"
              backgroundColor="#d8d8d8"
              catch:changing="handleSliderChanging"
              catch:change="handleSliderChange"
            />
            <view class="progress-bar-text">{{ df.secondToTimeString(duration) }}</view>
          </view>
        </view>
      </view>
    </block>

    <like-popover
      wx:for="{{ likePopoverList }}"
      wx:key="index"
      top="{{ item.top }}"
      left="{{ item.left }}"
    />
  </view>
</view>
