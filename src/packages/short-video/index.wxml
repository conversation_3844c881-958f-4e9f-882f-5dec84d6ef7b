<view class="short-video-wrapper" wx:if="{{ kdtId }}">
  <van-toast id="van-toast" />
  <swiper 
    class="swiper"
    style="padding-bottom:{{noSafeBottom}}px"
    vertical
    current="{{ swiperCurrent }}"
    duration="{{ swiperDuration }}"
    circular="{{ swiperCircular }}"
    bind:change="handleSwiperChange"
    bind:animationfinish="handleSwiperAnimationfinish"
  >
    <swiper-item
      wx:for="{{ videos }}"
      wx:for-index="videoSwiperIndex"
      wx:key="key"
      class="swiper-item"
    >
      <single-video
        id="singleVideo_{{ item.id }}"
        is-current="{{ swiperCurrent === videoSwiperIndex }}"
        video-src="{{ item.playUri }}"
        video-label="{{item.videoLabel}}"
        shop-logo="{{ shopLogo }}"
        live-stream-id="{{ item.id }}"
        salesman-info="{{ salesman }}"
        noteData="{{item}}"
        kdtId="{{kdtId}}"
        has-living="{{ liveInfo.hasLiving }}"
        room-id="{{ liveInfo.roomId }}"
        bind:setcurrentiteminfo="setCurrentItemInfo"
      />
    </swiper-item>
  </swiper>

  <view
    hidden="{{ !coverVisible }}"
    class="cover"
    catch:touchmove="noop"
  />

  <salesman-cube
    use-page-poster="{{ true }}"
    bind:share-card="onSalesmanShareCard"
    need-bind-relation="{{false}}"
  />

  <inject-protocol noAutoAuth />

  <share-goods
    id="share-goods"
    wx:if="{{true}}"
    goodsImage="{{currentItemInfo.videoCoverUrl}}"
    salesmanAlias="{{salesman.seller}}"
    title="{{currentItemInfo.videoLabel}}"
    desc="{{currentItemInfo.description}}"
    shopName="{{currentItemInfo.videoLabel}}"
    / >
  <view class="van-overlay" style="z-index: 1;" wx:if="{{ guideShow }}" catch:tap="handleGuideClick">
    <view class="cap-tiktok__guide">
      <view class="cap-tiktok__guide__layout">
        <view class="cap-tiktok__guide__layout--line"></view>
        <image src="https://b.yzcdn.cn/cdn/tiktok-guide-hand.png" class="cap-tiktok__guide__layout--hand">
      </view>
      <view class="cap-tiktok__guide--fir"> 上下滑动 </view>
      <view class="cap-tiktok__guide--sec"> 查看更多内容 </view>
      </view>
  </view>
</view>
