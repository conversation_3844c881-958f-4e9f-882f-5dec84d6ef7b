import WscComponent from 'shared/common/base/wsc-component/index';
import getApp from 'shared/utils/get-safe-app';
import get from '@youzan/weapp-utils/lib/get';
import { toHttps } from 'shared/utils/url';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import { fetchUltraCode } from 'shared/utils/qrcode';

const app = getApp();

WscComponent({
  properties: {
    goodsImage: {
      type: String,
      value: '',
    },
    salesmanAlias: String,
    path: String,
    title: String,
    desc: String,
    shopName: String,
  },
  data: {
    canvasId: 'drawerImage',
    // 控制预览蒙层是否展示
    showModal: false,
    // 控制 canvas 是否展示
    showCanvas: false,
    // canvas 绘制的分享图临时路径
    src: '',
    img: '',
    // 小程序码图片路径
    weappCode: '',
    sheet: {
      show: false,
      closeOnClickOverlay: true,
    },
    isSupportOpenBusinessView: !!wx.openBusinessView,
    wrapperClass: '',
  },
  observers: {
    desc(val) {
      if (!!val != !!this._oldDesc) {
        const className = val
          ? 'share-image__container-wrapper'
          : 'share-image__container-desc-wrapper';
        this.setYZData({
          wrapperClass: className,
        });
      }
      this._oldDesc = val;
    },
  },
  attached() {
    const desc = this.properties.desc;
    const className = desc
      ? 'share-image__container-wrapper'
      : 'share-image__container-desc-wrapper';
    this.setYZData({
      wrapperClass: className,
    });
  },
  methods: {
    /**
     * @deprecated 临时方法
     * @description 直接生成销售员海报
     */
    drawSalemanCard() {
      wx.showLoading({ title: '正在生成' });
      this.setYZData(
        {
          showCanvas: true,
        },
        this.draw.bind(this)
      );
    },

    showSheet(value) {
      this.setYZData({
        'sheet.show': value,
      });
    },
    closeActionSheet() {
      this.setYZData({
        'sheet.show': false,
      });
      // this.$commit('HIDE_SHARE_POP');
      this.triggerEvent('finished');
    },

    handlePoster(e) {
      if (e.detail.openType === 'share') return;
      wx.showLoading({ title: '正在生成' });
      this.setYZData(
        {
          showCanvas: true,
        },
        this.draw.bind(this)
      );
    },

    handleRecommend() {
      this.shareGoodsRecommend();
      this.closeActionSheet();
    },

    shareGoodsRecommend() {
      wx.showLoading({
        title: '正在同步',
        mask: true,
      });
      const openBusinessView = () => {
        wx.hideLoading();
        wx.openBusinessView({
          businessType: 'friendGoodsRecommend',
          extraData: {
            product: {
              item_code: this.shareGoodsId || this.data.goodsId,
              title: this.data.title,
              image_list: this.data.goodsImageList.map((it) => it.url),
            },
          },
          fail(e) {
            const { errCode = -1 } = e;
            if (errCode !== 0 && errCode !== -3) {
              wx.showToast({
                title: '推荐失败' + JSON.stringify(e) + e.errCode,
                icon: 'none',
                duration: 2000,
              });
            }
          },
        });
      };
      app
        .request({
          path: '/wscshop/goods/query-mall.json',
          data: {
            alias: this.data.alias,
          },
        })
        .then((res) => {
          this.shareGoodsId = res.goodsId;

          if (res.need_wait) {
            setTimeout(openBusinessView, 1500);
          } else {
            openBusinessView();
          }
        })
        .catch(() => {
          wx.hideLoading();
          wx.showToast({
            title: '同步失败',
            icon: 'none',
            duration: 2000,
          });
        });
    },

    draw() {
      this.loadGoodsImage()
        .then(this.drawQrCode.bind(this))
        .then(this.createTempPath.bind(this))
        .then((src) => {
          this.setYZData(
            {
              showModal: true,
              src,
            },
            () => {
              this.triggerEvent('success');
            }
          );
        })
        .catch((e) => {
          let errMsg = '';
          if (typeof e === 'object') {
            try {
              errMsg = JSON.stringify(e);
            } catch (err) {
              errMsg = String(e);
            }
          } else {
            errMsg = String(e);
          }
          wx.showToast({
            title: errMsg || '生成图片路径失败',
            icon: 'none',
          });
          app.getUserInfo((res) => {
            app.logger &&
              app.logger.appError({
                name: 'draw_goods_poster_error',
                message: '绘制商品海报失败',
                detail: {
                  errMsg,
                  userName: get(res, 'userInfo.nickName'),
                },
              });
          });
          this.triggerEvent('failed', { err: e });
        })
        .then(() => {
          this.setYZData({ showCanvas: false });
          this.closeActionSheet();
          // this.$commit('HIDE_SHARE_POP');
          this.triggerEvent('finished');
        });
    },

    drawQrCode() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      // if (this.data.weappCode) {
      //   return Promise.resolve();
      // }
      if (!pages.length) {
        return Promise.reject();
      }

      const path = currentPage.route;
      let query = currentPage.options;

      if (this.data.salesmanAlias) {
        query = {
          ...query,
          ...getSalesmanParamsObject({ sls: this.data.salesmanAlias }),
        };
      }
      return fetchUltraCode(path, query).then((img) => {
        this.setYZData({
          weappCode: img,
        });
        return this.drawShareImage(img);
      });
    },

    // 生成分享图临时路径
    createTempPath() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          wx.canvasToTempFilePath(
            {
              canvasId: this.data.canvasId,
              success: (res) => {
                resolve(res.tempFilePath);
              },
              fail: reject,
              complete: () => {
                wx.hideLoading();
                this.setYZData({
                  show: false,
                });
              },
            },
            this
          );
        }, 100);
      });
    },

    loadGoodsImage() {
      // if (this.data.img) return Promise.resolve();
      if (!this.data.goodsImage) return Promise.reject();
      return new Promise((resolve, reject) => {
        const url = cdnImage(this.data.goodsImage, '!730x0.jpg');
        const imageUrl = toHttps(url);
        if (!imageUrl) {
          return wx.showToast({
            title: '生成卡片失败:',
            icon: 'none',
            duration: 2000,
          });
        }
        loadImage(imageUrl)
          .then((img) => {
            this.setYZData({ img }, resolve);
          })
          .catch((e) => {
            console.log(e);
            e &&
              wx.showToast({
                title: '生成卡片失败:',
                icon: 'none',
                duration: 2000,
              });
            reject(e);
          });
      });
    },
    // 获取图片宽高
    getImageInfo(image) {
      return new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: image,
          success: (res) => {
            resolve(res);
          },
          error: (err) => reject(err),
        });
      });
    },
    drawTwoLineText(text, color, fontSize, ctx, x, y) {
      let title = text;
      let titleMore = '';
      const charLen = Math.floor(224 / fontSize);
      const lineHeight = fontSize + 5;
      // 一行大约 12 个汉字需要换行
      if (title.length > charLen) {
        titleMore = title.slice(charLen);
        title = title.slice(0, charLen);
      }
      if (titleMore.length >= charLen) {
        titleMore = titleMore.slice(0, charLen - 1) + '...';
      }
      ctx.setFontSize(fontSize);
      ctx.setFillStyle(color);
      ctx.fillText(title, x, y);
      ctx.fillText(titleMore, x, y + lineHeight);
    },
    // 预绘制和实际输出分享图绘制（ 通过有无 qrCodeUrl 判断 ）
    drawShareImage(qrCodeUrl) {
      const { img } = this.data;
      const ctx = wx.createCanvasContext(this.data.canvasId, this);
      const desc = this.data.desc;
      const getHeight = (height) => {
        if (!desc) return height - 30;
        return height;
      };
      ctx.setFillStyle('white');
      ctx.fillRect(0, 0, 260, getHeight(320));
      ctx.drawImage(img, 0, 0, 260, 145, 260, 145);
      // 最多两行的标题……
      ctx.setFontSize(18);
      ctx.setFillStyle('#000000');
      const title = this.data.title;
      this.drawTwoLineText(title, '#000000', 18, ctx, 17, 170);
      desc && this.drawTwoLineText(desc, '#AAAAAA', 9, ctx, 17, 213);
      // 小程序码
      if (qrCodeUrl) {
        ctx.drawImage(qrCodeUrl, 17, getHeight(252), 45, 45);
      }
      ctx.setFontSize(9);
      ctx.setFillStyle('#AAAAAA');
      ctx.fillText('长按小程序阅读全文', 69, getHeight(273));
      ctx.fillText(`分享自${this.data.shopName}`, 69, getHeight(287));
      ctx.draw();
      this.triggerEvent(qrCodeUrl ? 'created' : 'inited', {
        canvasId: this.data.canvasId,
      });
    },

    closeShareImageModal() {
      this.setYZData({
        showModal: false,
      });
    },

    // 点击保存（ 成功后自动关闭弹层，抛出 saved 事件 ）
    clickSaveImage() {
      const { src } = this.data;
      if (!src) return;

      wx.showLoading({ title: '保存中' });

      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'goods_savephoto',
          en: '点击海报保存按钮',
          si: '', // 店铺id，没有则置空
          params: {},
        });

      this.saveShareImage(src)
        .then(() => {
          wx.hideLoading();
          wx.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000,
          });
          this.closeShareImageModal();
          this.triggerEvent('saved');
        })
        .catch(({ errMsg } = {}) => {
          wx.hideLoading();
          if (
            errMsg === 'saveImageToPhotosAlbum:fail auth deny' ||
            errMsg === 'saveImageToPhotosAlbum:fail authorize no response'
          ) {
            this.getSavePhotosRights();
            return;
          }
          wx.showToast({
            title: '保存失败',
            icon: 'none',
            duration: 2000,
          });
        });
    },

    getSavePhotosRights() {
      this.triggerEvent('open-setting');
      if (wx.openSetting) {
        wx.openSetting({
          success: ({ authSetting }) => {
            if (authSetting['scope.writePhotosAlbum']) {
              this.clickSaveImage();
            }
          },
        });
      }
    },

    // 保存图片 api 调用
    saveShareImage(tempFilePath) {
      return new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: tempFilePath,
          success: resolve,
          fail: reject,
        });
      });
    },
  },
});

// 下载图片获得临时路径
function loadImage(src) {
  const app = getApp();
  return new Promise((resolve, reject) => {
    app.downloadFile({
      url: src,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (e) => {
        reject(e);
      },
    });
  });
}
