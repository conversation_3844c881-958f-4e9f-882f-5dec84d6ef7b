import { addSalesmanParams } from 'shared/utils/salesman-params-handler';
import Args from '@youzan/weapp-utils/lib/args';
import WscPage from 'pages/common/wsc-page/index';
import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';
import { bindSalesMan, getBannerId, getListWithKey } from './utils';
import { initSalesman } from 'shared/utils/salesman-share';
import { EShoppingGuideBindSourceType } from 'constants/guide';

/**
 * 根据 swiper 当前 index 获取上一个或下一个的 index
 * swiper 是循环的，
 * 0 的上一个是 2，2 的下一个是 0
 */
function getPrevIndex(index) {
  return [2, 0, 1][index];
}
function getNextIndex(index) {
  return [1, 2, 0][index];
}

function fetchStickNoteList(id) {
  return app
    .request({
      path: '/wscshop/shortvideo/listStickShopNoteIds',
    })
    .then((res) => {
      let index;
      if (id) {
        index = res.indexOf(id);
      }

      const afterShopNoteIds = id
        ? res.slice(index + 1, res.length)
        : res.slice(1, res.length);
      const beforeShopNoteIds = id ? res.slice(0, index) : [];

      return {
        afterShopNoteIds,
        beforeShopNoteIds,
        currentId: id || res[0],
      };
    });
}

function fetchNoteListByPage(id) {
  return app
    .request({
      path: '/wscshop/shortvideo/listBeforeAndAfterShopNotes',
      data: {
        noteId: id,
      },
    })
    .then((res) => {
      return res;
    });
}

function fetchNoteList(id) {
  if (id) {
    return fetchNoteListByPage(id);
  }

  return fetchStickNoteList();
}

function fetchCurrentNoteId(params) {
  return app
    .request({
      path: '/wscshop/shortvideo/fetchCurrentNoteId',
      data: params,
    })
    .then((res) => {
      return res;
    });
}

function fetchLiveInfoApi(kdtId) {
  return app
    .request({
      path: '/wscshop/content-center/fetchLiveInfo.json',
      data: {
        kdtId,
        scene: 4, // 短视频scene = 4;
      },
    })
    .then((res) => {
      return res;
    });
}

const app = getApp();

/**
 * 短视频详情页
 */
WscPage({
  data: {
    videos: [], // 实际渲染的 video 列表
    coverVisible: false, // 使用这个保证每次只滑动一个视频

    // swiperXxx 为 swiper 的属性
    swiperCircular: false,
    swiperCurrent: 0,
    swiperDuration: 300,
    shopLogo: '',
    salesman: {},
    guideShow: false,
    kdtId: 0,
    liveInfo: {
      hasLiving: false,
      roomId: undefined,
    },
    // 当前视口视频信息
    currentItemInfo: {},
  },

  initData(options) {
    // 如果是连锁 获取总店id
    const kdtId = app.getExtKdtId();
    const appId = app.getAppId();
    this.setYZData({
      kdtId,
      appId,
    });

    this.videoPool = []; // 全部视频列表
    this.videoIndex = 0;

    // 获取分销员信息
    this.fetchSalesmanBaseData();

    // 展示引导弹窗
    this.showGuideDialog();

    // 开始轮询直播信息
    this.fetchLiveInfoPoll();

    this.query = options;

    const {
      // eslint-disable-next-line no-unused-vars
      noteId, // id, 必传
      // eslint-disable-next-line no-unused-vars
      playUri, // 强烈推荐，可以省去根据 id 取视频地址的时间，提升首屏视频显示速度
      sl,
    } = this.query;

    if (sl) {
      bindSalesMan(sl);
    }

    // 立即指定一个播放列表
    // 如果预设了 playUri 视频首屏加载会很快，否则会根据 id 获取播放地址
    // this.videoPool = [{ id: noteId, playUri, key: `${noteId}_0` }];

    app.getShopInfo().then((res) => {
      this.setYZData({
        shopLogo: res.logo,
      });
    });

    this.setYZData({
      videos: this.videoPool,
    });
    this.getPlaylist(noteId, 'init');
  },

  onLoad(options) {
    const newOptions = { ...options };
    initSalesman.call(this, {
      sst: 11,
      gst: EShoppingGuideBindSourceType.TINY_PAGE,
    });
    app.waitForEnterShop().then(() => {
      if (newOptions.noteId) {
        const params = {
          kdtId: app.getExtKdtId(),
          targetKdtId: app.getKdtId(),
          noteId: options.noteId,
        };
        fetchCurrentNoteId(params).then((res) => {
          newOptions.noteId = res.noteId;
          this.initData(newOptions);
          this.trigger('shortVideo:loaded', res.noteId);
        });
      } else {
        this.initData(newOptions);
      }
    });
  },

  onUnload() {
    this.fetchLiveInfoPollDisposer();
  },

  showGuideDialog() {
    wx.getStorage({
      key: 'SHORT_VIDEO_GUIDE_KEY',
      success: (res) => {
        if (res.data !== '1') {
          this.setYZData({
            guideShow: true,
          });
        }
      },
    });

    wx.setStorage({
      key: 'SHORT_VIDEO_GUIDE_KEY',
      data: '1',
    });
  },

  // 引导弹窗点击隐藏
  handleGuideClick() {
    this.setYZData({
      guideShow: false,
    });
  },

  // 获取分销员信息
  fetchSalesmanBaseData() {
    app.carmen({
      api: 'youzan.salesman.wap.account/1.0.0/get',
      success: (res) => {
        this.setYZData({
          'salesman.openSalesman': res.openSalesman,
          'salesman.icon': res.settingIcon,
          'salesman.name': res.settingName,
          'salesman.isSalesman': res.salesman,
          'salesman.alias': res.sls,
        });
      },
    });
  },

  // 获取直播状态轮询
  async fetchLiveInfoPoll() {
    if (this.liveInfoTimer) {
      clearTimeout(this.liveInfoTimer);
    }
    try {
      await this.fetchLiveInfo();
      this.liveInfoTimer = setTimeout(() => {
        this.fetchLiveInfoPoll();
      }, 40000);
    } catch (e) {
      console.error(e, 'fetchLiveInfoPoll-error-catch');
      this.fetchLiveInfoPollDisposer();
    }
  },
  // 取消获取直播状态轮询定时器
  fetchLiveInfoPollDisposer() {
    if (this.liveInfoTimer) {
      clearTimeout(this.liveInfoTimer);
      this.liveInfoTimer = null;
    }
  },

  // 获取直播信息
  async fetchLiveInfo() {
    const kdtId = app.getKdtId();
    const result = await fetchLiveInfoApi(kdtId);
    this.setYZData({
      liveInfo: {
        hasLiving: result.hasLiving,
        roomId: result.roomId,
      },
    });
    return result;
  },

  // 设置当前current item的信息
  setCurrentItemInfo({ detail = {} }) {
    this.setYZData({
      currentItemInfo: detail,
    });
  },

  // 获取视频列表
  getPlaylist(id, type = 'init') {
    if (this.loading) return;
    this.loading = true;
    fetchNoteList(id).then((res) => {
      const { afterShopNoteIds = [] } = res;
      const { beforeShopNoteIds = [] } = res;
      const { currentId = '' } = res;

      const prevList = beforeShopNoteIds.map((i) => ({ id: i }));

      const nextList = afterShopNoteIds.map((i) => ({ id: i }));

      if (type === 'init') {
        this.nextListFinished = false;
        this.videoPool = [...prevList, { id: currentId || id }, ...nextList];
        this.videoIndex = id
          ? this.videoPool.findIndex((item) => item.id === id)
          : 0;
      }

      if (type === 'prev') {
        this.videoPool.unshift(...prevList);
        this.videoIndex += prevList.length;
      }
      if (type === 'next') {
        this.videoPool.push(...nextList);
      }

      this.videoPool = getListWithKey(this.videoPool);

      if (type === 'init') {
        if (this.videoIndex === 0) {
          // 第一个
          this.setYZData(
            {
              swiperCircular: false,
              swiperCurrent: 0,
              videos: this.videoPool.slice(0, 3),
            },
            () => {
              const { id = '' } = this.data.videos[this.data.swiperCurrent];
              id && this.getSingleVideo(id).play();
            }
          );
        } else if (this.videoIndex === this.videoPool.length - 1) {
          // 最后一个
          this.setYZData(
            {
              swiperCircular: false,
              swiperCurrent: this.videoPool.length === 2 ? 1 : 2, // 如果只有两个视频，最后一个的 index 就是 1 了
              videos: this.videoPool.slice(-3),
            },
            () => {
              const { id = '' } = this.data.videos[this.data.swiperCurrent];
              id && this.getSingleVideo(id).play();
            }
          );
        } else {
          // 中间的
          this.setYZData(
            {
              swiperCircular: true,
              swiperCurrent: 1,
              videos: this.videoPool.slice(
                this.videoIndex - 1,
                this.videoIndex + 2
              ),
            },
            () => {
              const { id = '' } = this.data.videos[this.data.swiperCurrent];
              id && this.getSingleVideo(id).play();
            }
          );
        }
      }

      if (!prevList.length) {
        this.prevListFinished = true;
      }

      // 没有后面的视频时 请求第一篇视频
      if (!nextList.length) {
        app
          .request({
            path: '/wscshop/shortvideo/listStickShopNoteIds',
          })
          .then((res) => {
            const nextList = res.map((i) => ({ id: i }));
            this.videoPool.push(...nextList);
            this.nextListFinished = false;
          });
      }

      this.loading = false;
    });
  },

  getSingleVideo(index) {
    return this.selectComponent(`#singleVideo_${index}`);
  },

  clickShareLog() {
    const noteId = this.videoPool[this.videoIndex].id;
    // banner_id
    const params = {
      banner_id: getBannerId(noteId, 0, 'share'),
    };
    // 获取埋点格式
    const loggerMsg = getComponentLoggerParams('click_content', params);
    // 上报
    ensureAppLogger(loggerMsg);
  },

  /**
   * change 每次都会触发
   * 如果划得够快 animationfinish 可能多次 change 后才触发
   * 所以用 coverVisible 控制浮层，防止一次滑多个
   */
  handleSwiperChange(e) {
    if (e.detail.source !== 'touch') return;

    const diff = e.detail.current - this.data.swiperCurrent;
    const direction = diff === 1 || diff === -2 ? 'toNext' : 'toPrev';

    if (direction === 'toNext') {
      this.videoIndex += 1;

      // 后面不足 3 个的时候用最后一个的 id 获取后面的列表

      if (
        !this.nextListFinished &&
        this.videoIndex > this.videoPool.length - 4
      ) {
        this.getPlaylist(this.videoPool[this.videoPool.length - 1].id, 'next');
      }
    } else {
      this.videoIndex -= 1;
      // 前面不足 3 个的时候用第一个的 id 获取前面的列表
      if (!this.prevListFinished && this.videoIndex < 3) {
        this.getPlaylist(this.videoPool[0].id, 'prev');
      }
    }

    const prev = this.videoPool[this.videoIndex - 1];
    const current = this.videoPool[this.videoIndex];
    const next = this.videoPool[this.videoIndex + 1];

    const videos = [];

    videos[e.detail.current] = current;
    if (prev) {
      videos[getPrevIndex(e.detail.current)] = prev;
    }
    if (next) {
      videos[getNextIndex(e.detail.current)] = next;
    }

    this.nextVideos = videos;
    this.hasChanged = true;

    if (!this.nextListFinished) {
      this.setYZData({
        coverVisible: true,
        swiperCurrent: e.detail.current,
      });
    }
    this.nextListFinished = false;
  },

  handleSwiperAnimationfinish() {
    if (!this.hasChanged) return;
    this.hasChanged = false;

    const isFirst = this.videoIndex === 0;
    const isLast = this.videoIndex === this.videoPool.length - 1;
    const isFirstOrLast = isFirst || isLast;

    // 视频数量大于 3，并且是开头或结尾需额外处理
    // 比如有 4 个视频，videos[3] 将渲染在 swiperItems[0] 里，后面会有两个空的 swiperItem
    // 所以在这里把 videos[3] 放到 swiperItems[2] 里，只可以回看上一个，而没有下一个。
    // 这就导致回到第一个的时候 videos[0] 会被放在 swiperItems[2] 里，
    // 需使用同样的方法把 videos[0] 放到 swiperItems[0] 里。
    if (this.videoPool.length > 3 && isFirstOrLast) {
      let videos = [];
      let swiperCurrent;

      // 开头取前 3，定位到第一个 swiperItem
      if (isFirst) {
        videos = this.videoPool.slice(0, 3);
        swiperCurrent = 0;
      }

      // 结尾取后 3，定位到最后一个 swiperItem
      if (isLast) {
        videos = this.videoPool.slice(-3);
        swiperCurrent = 2;
      }

      this.setYZData(
        {
          swiperDuration: 0,
          coverVisible: false,
          swiperCircular: !isFirstOrLast, // 开头结尾为 false
        },
        () => {
          this.setYZData(
            {
              videos,
              swiperCurrent,
              swiperDuration: 300,
            },
            () => {
              for (let i = 0; i < this.data.videos.length; i++) {
                const { id } = this.data.videos[i];
                if (!id) break;
                if (this.data.swiperCurrent === i) {
                  this.getSingleVideo(id).play();
                } else {
                  this.getSingleVideo(id).stop();
                }
              }
            }
          );
        }
      );
    } else {
      this.setYZData(
        {
          videos: this.nextVideos,
          coverVisible: false,
          swiperCircular: !isFirstOrLast, // 开头结尾为 false
        },
        () => {
          for (let i = 0; i < this.data.videos.length; i++) {
            if (this.data.videos[i]) {
              const { id = '' } = this.data.videos[i];

              if (this.data.swiperCurrent === i) {
                this.getSingleVideo(id).play();
              } else {
                this.getSingleVideo(id).stop();
              }
            }
          }
        }
      );
    }
  },

  onSalesmanShareCard() {
    try {
      this.selectComponent('#share-goods').drawSalemanCard();
    } catch (e) {
      console.log(e.message);
    }
  },

  onShareAppMessage() {
    const { description, videoCoverUrl } = this.data.currentItemInfo;
    const { id: noteId } = this.videoPool[this.videoIndex];
    const kdtId = app.getKdtId();
    const shareUrl = 'packages/short-video/index?is_share=1';

    let newSharePath = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
      shareUrl
    )}&kdt_id=${kdtId}`;

    if (noteId) {
      newSharePath = Args.add(newSharePath, { noteId });
    }

    if (this.data.salesman && this.data.salesman.seller) {
      newSharePath = addSalesmanParams({
        url: noteId ? Args.add(shareUrl, { noteId }) : shareUrl,
        sl: this.data.salesman.seller,
      });
    }

    this.clickShareLog();

    return {
      title: description,
      path: newSharePath,
      imageUrl: videoCoverUrl,
    };
  },

  noop() {},
});
