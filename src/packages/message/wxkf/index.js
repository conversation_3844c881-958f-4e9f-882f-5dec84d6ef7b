import WscPage from 'pages/common/wsc-page/index';

WscPage({
  data: {
    imData: {
      businessId: '',
      sourceParam: '{}',
    },
    wxkfConfig: {
      kfLink: '',
      corpId: '',
    },
  },

  onLoad() {
    // this.initImData();
    const { kfLink, corpId } = this.__query__;
    this.setYZData({
      wxkfConfig: {
        kfLink: decodeURIComponent(kfLink),
        corpId,
      },
    });
  },

  // initImData() {
  //   getApp()
  //     .getDefaultImData()
  //     .then((data) => {
  //       this.setYZData({
  //         imData: data,
  //       });
  //     });
  // },
});
