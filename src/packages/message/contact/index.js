import args from '@youzan/weapp-utils/lib/args';
import get from '@youzan/weapp-utils/lib/get';
import WscPage from 'pages/common/wsc-page/index';

export const FROM_CHANNEL = {
  /**
   * 小程序webview由小程序跳转客服组件跳转的button
   */
  WEAPP_IM: 'weapp_im',
};

/**
 * 小程序客服Webview页面
 * query 参数解析
 * sessionFrom: https://doc.qima-inc.com/pages/viewpage.action?pageId=69569323
 */

const app = getApp();

const BIZ = {
  GOODS: 'goods',
};

WscPage({
  onLoad(query = {}) {
    const { sessionFrom: sessionFromStrEncode = '' } = query;
    const kdtId = app.getKdtId();
    const baseUrl = 'https://h5.youzan.com/v3/im/index';
    const sessionFromStr = decodeURIComponent(sessionFromStrEncode);
    const sessionFrom = (() => {
      try {
        const sessionFrom = JSON.parse(sessionFromStr);
        if (!sessionFrom.detail) {
          return sessionFrom;
        }
        if (typeof sessionFrom.detail === 'string') {
          sessionFrom.detail = JSON.parse(sessionFrom.detail);
        }
        return sessionFrom;
      } catch (e) {
        console.log('error', e);
        return {};
      }
    })();
    const params = {
      kdt_id: kdtId,
      fs: encodeURIComponent(sessionFromStr),
      biz: sessionFrom.source === BIZ.GOODS ? BIZ.GOODS : undefined,
      type: sessionFrom.source === BIZ.GOODS ? BIZ.GOODS : undefined,
      alias: get(sessionFrom, 'detail.alias', ''),
      fc: FROM_CHANNEL.WEAPP_IM,
    };
    this.setYZData({
      url: args.add(baseUrl, params),
    });
    this.__webviewMsg = {};
  },

  handlePostMessage() {},

  onShareAppMessage() {
    // 有 src 就分享 webview 加 src 参数，否则分享首页
    const { src } = this.options || {};
    const defaultSharePath = src
      ? args.add(`/${this.route}`, { src })
      : '/packages/home/<USER>/index';
    return this.__webviewMsg['ZNB.share'] || { path: defaultSharePath };
  },
});
