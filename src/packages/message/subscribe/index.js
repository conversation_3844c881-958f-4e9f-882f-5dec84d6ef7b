import WscPage from 'pages/common/wsc-page/index';
import get from '@youzan/weapp-utils/lib/get';

const app = getApp();
const shopInfo = app.getShopInfoSync();

WscPage({
  data: {
    templateIdList: [],
    popShow: true,
    shopName: get(shopInfo, 'base.shop_name', ''),
    shopLogo: get(shopInfo, 'base.logo', '')
  },

  onLoad(query) {
    try {
      const params = JSON.parse(query.params);
      if (Array.isArray(params.templateIdList)) {
        this.setYZData({
          templateIdList: params.templateIdList
        });
      }
    } catch (e) {
      // 如果解析错误跳回到原来的页面
      wx.navigateBack();
      console.error(e);
    }
  },
  handleWxSubscribe() {
    wx.navigateBack();
  }
});
