<van-popup show="{{ popShow }}" position="bottom" bind:close="onClose" custom-style="height: 288px;" round>
    <view class="container">
        <view class="top">
            <image class="avatar" src="{{ shopLogo }}"></image>
            <view class="shop-name">{{ shopName }} 申请</view>
        </view>
        <view class="line"></view>
        <view class="content">订阅本条消息提醒，客服回复后我们将第一时间通知你</view>
        <view class="operator">
            <van-button class="cancel" custom-style="color: #1AAD19; font-size: 16px; border-radius: 4px; width: 100%; font-weight: bold;" color="#EDEDED" bind:click="handleWxSubscribe">
                取消
            </van-button>
            <subscription-msg class="sure" id="subscription-msg" template-ids="{{ templateIdList }}" bind:complete="handleWxSubscribe">
                <van-button color="#1AAD19" custom-style="font-size: 16px; border-radius: 4px; width: 100%; font-weight: bold;">
                    去开启
                </van-button>
            </subscription-msg>
        </view>
    </view>
</van-popup>