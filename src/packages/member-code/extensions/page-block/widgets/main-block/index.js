import { RantaWidget } from 'shared/common/base/ranta/widget';
import pageComponentBehavior from '@/custom-tab-bar-v2/native-helper/page-component-behavior';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { Hummer } from 'shared/utils/hummer';
import { getIsCrmShop } from 'common-api/scrm';
import { checkInWxPayWhiteList } from './api';
import theme from 'shared/common/components/theme-view/theme';
import { mapData } from '@youzan/ranta-helper-tee';

const app = getApp();
RantaWidget({
  behaviors: [pageComponentBehavior],
  data: {
    type: null,
    src: '',
    showWxPayBtn: false,
    wxPayIcon: cdnImage(
      'upload_files/2022/03/21/FqEx9rlTjNvy__DD_0zHNJI_bBIm.png'
    ),
    iconMember: cdnImage(
      'upload_files/2024/04/03/Fm4nG8MBhEMInCH6FUJoBRm5rJCm.png'
    ),
    userInfo: {},
    navHeight: 44,
    statusBarHeight: 20,
    isCodeLoadingComplete: false,
    payParams: {},
    pageQuery: {},
  },

  attached() {
    mapData(this, ['pageQuery']);
    wx.setScreenBrightness({ value: 0.8 });

    const { isRetailApp } = app.globalData;
    const { navIndex, eT } = this.data.pageQuery;
    this.getNaviationInfo();
    this.getUserInfo();
    theme.getThemeColor('main-bg').then((color) => {
      this.setYZData({ themeColor: color });
    });

    if (isRetailApp) {
      this.payParams = {};
      this.setYZData({ type: 'native' });
      this.checkInWhiteList();
      this.checkIsCrmShop();
      eT && Hummer.mark.start('member-code', eT);
    } else {
      const baseUrl = 'https://h5.youzan.com/wscuser/scrm/member-code';
      const timestamp = eT || Date.now();
      app.isSwitchTab().then((isTab) => {
        const src = isTab
          ? `${baseUrl}?newTabbarColor=1&customNav=1&eT=${timestamp}&navIndex=${navIndex}${
              app.deviceType === 'iPhone-X' ? '&isNewIphone=1' : ''
            }`
          : `${baseUrl}?eT=${timestamp}`;
        this.setYZData({ src, type: 'webview' });
      });
    }
  },

  detached() {
    wx.setScreenBrightness({ value: 0.4 });
  },

  methods: {
    checkInWhiteList() {
      checkInWxPayWhiteList().then((res) => {
        const { result = false, params } = res;
        this.setYZData({ showWxPayBtn: result, payParams: params });
      });
    },
    checkIsCrmShop() {
      getIsCrmShop().then(({ available }) => {
        this.setYZData({ isCrmShop: available });
      });
    },
    getUserInfo(cb = () => {}) {
      app
        .resolveTeeAPI()
        .then((api) => api.getUserInfo({ kdtId: app.getKdtId(), cache: false }))
        .then((userInfo) => {
          this.setYZData({ userInfo });
          cb?.();
        });
    },
    afterAuthorize() {
      this.getUserInfo(() => {
        this.launchFastJoinSDK();
      });
    },
    getNaviationInfo() {
      const { statusBarHeight, system } = wx.getSystemInfoSync();
      this.setYZData({
        navHeight: system.indexOf('iOS') > -1 ? 44 : 48,
        statusBarHeight,
      });
    },
    launchFastJoinSDK() {
      this.ctx.process.invoke('launchFastJoinSDK', {
        successCallback: () => {
          const memberCodeInfoRef = this.selectComponent('#memberCodeInfo');
          if (memberCodeInfoRef) {
            memberCodeInfoRef.updateLevelView();
          }
        },
      });
    },
    handleToWxPay() {
      wx.openOfflinePayView({
        ...this.payParams,
        package: this.payParams.packageStr,
        success: () => console.log('open offline pay view success'),
        fail: (res) => console.log('open offline pay view failed: ', res),
      });
    },
    codeLoadingComplete(isCodeLoadingComplete) {
      this.setYZData({ isCodeLoadingComplete });
    },
  },
});
