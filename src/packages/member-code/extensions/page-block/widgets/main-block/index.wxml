
<view wx:if="{{ type === 'native' }}" style="padding-top: {{navHeight + statusBarHeight}}px; background-image: linear-gradient(to right,rgba(0, 0, 0, 0.2),rgba(0, 0, 0, 0.2)),linear-gradient(to right, {{themeColor}}, {{themeColor}})">
  <navigation-bar navHeight="{{ navHeight }}" statusBarHeight="{{ statusBarHeight }}" />
  <view class="{{ ['member', !isCodeLoadingComplete && 'hide' ] }}">
    <view class="wrapper">
      <member-code-info id="memberCodeInfo" userInfo="{{ userInfo }}"  bindauth="afterAuthorize" bind:codeLoadingComplete="codeLoadingComplete">
        <van-button
          custom-class="wechat"
          custom-style="display: flex;color: #22AC38;border-color:#22ac38"
          round="{{ true }}"
          wx:if="{{ userInfo && userInfo.mobile && showWxPayBtn }}"
          catchtap="handleToWxPay"
          icon="{{ wxPayIcon }}"
        >
          打开微信付款码
        </van-button>
      </member-code-info>
    </view>
    <coupon-list wx:if="{{ isCrmShop }}" />
  </view>
  <view wx:if="{{ !isCodeLoadingComplete }}" class="loading-content">
     <van-loading type="spinner">加载中</van-loading>
  </view>
</view>
<block wx:elif="{{ type === 'webview' }}">
  <znb-web-view src="{{src}}" />
</block>