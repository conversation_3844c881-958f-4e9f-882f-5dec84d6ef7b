import WscComponent from 'pages/common/wsc-component/index';
import { getPlugins } from '@youzan/ranta-helper-tee';

const { dmc } = getPlugins();

const app = getApp();

WscComponent({
  properties: {
    aList: Array,
    clearDotUrlMap: Object,
  },
  methods: {
    fetchFn(path) {
      return app.request({
        path,
      });
    },
    hdClearDot(targetKey, isShowDot) {
      const p = Promise.resolve();
      if (!isShowDot) return p;
      return this.clearDotUrlMap[targetKey]
        ? this.fetchFn(this.clearDotUrlMap[targetKey])
        : p;
    },
    jumpClick(e) {
      const $data = e.currentTarget.dataset;

      const { jumpPage, jumpLink } = $data.bean || {};
      if (jumpPage) {
        dmc.navigate(jumpPage, {
          kdt_id: app.getKdtId(),
        });
      } else if (jumpLink) {
        dmc.navigate(jumpLink);
      }
    },
  },
});
