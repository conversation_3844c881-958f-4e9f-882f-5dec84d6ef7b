<view class="member-assets" wx:if="{{ aList.length }}">
  <view class="inner">
    <block wx:if="{{ aList.length === 1 }}">
      <view class="single_out" wx:for="{{ aList }}" wx:for-item="it" wx:key="key">
        <view class="single_bl" data-bean="{{ it }}" bindtap="jumpClick">
          <view class="left">
            <view class="{{ ['icon-bx', it.isShowDot && 'dot'] }}"><image class="icon-img" src="{{ it.icon }}" /></view>
            <view class="val">￥{{ it.val }}</view>
          </view>
          <view class="right">
            {{ it.btnText }}
          </view>
        </view>
      </view>
    </block>
    <block wx:else>
      <view 
        class="mult_out"
        wx:for="{{ aList }}"
        wx:for-item="one"
        wx:key="key"
        bindtap="jumpClick"
        data-bean="{{ one }}"
      >
        <view class="{{ ['mult-val', one.isShowDot && 'dot'] }}">{{ one.val }}</view>
        <view class="mult-desc">{{ one.desc }}</view>
      </view>
    </block>
  </view>
</view>