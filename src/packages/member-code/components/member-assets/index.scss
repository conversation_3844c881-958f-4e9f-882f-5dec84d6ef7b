.member-assets {
  padding: 0 4px 0;
  .inner {
    border-top: 1px dashed #e0e0e0;
    padding-top: 20px;
    display: flex;
    .single_out {
      flex: 1 1 auto;
      display: flex;
      justify-content: center;
      .single_bl {
        width: 90%;
        height: 50px;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        background-color: #f1f1f1;
        border-radius: 30px;
        overflow: hidden;
        padding: 10px 24px 10px 20px;
        box-sizing: border-box;
        align-items: center;
        justify-content: space-between;
        .left {
          display: flex;
          align-items: center;
          .icon-bx {
            position: relative;
            &.dot {
              &::after {
                content: '';
                width: 5px;
                height: 5px;
                border-radius: 50%;
                background-color: #d9263a;
                position: absolute;
                top: 2px;
                right: -1px;
              }
            }
            .icon-img {
              width: 32px;
              height: 32px;
              display: block;
            }
          }
          .val {
            padding-left: 4px;
          }
        }
        .right {
          flex: 0 0 60px;
          min-width: 60px;
          text-align: right;
        }
      }
    }
    .mult_out {
      flex: 1;
      text-align: center;
      .mult-val {
        font-size: 18px;
        font-weight: 500;
        padding-top: 4px;
        padding-bottom: 4px;
        position: relative;
        display: inline-block;
        &.dot {
          &::after {
            content: '';
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background-color: #d9263a;
            position: absolute;
            top: -2px;
            right: -6px;
          }
        }
      }
      .mult-desc {
        font-size: 12px;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
