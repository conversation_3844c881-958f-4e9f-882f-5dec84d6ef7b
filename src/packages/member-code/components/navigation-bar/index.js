import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  data: {
    showHomeIcon: true,
  },
  properties: {
    navHeight: Number,
    statusBarHeight: Number,
  },

  attached() {
    const pages = getCurrentPages();
    const showHomeIcon = pages.length <= 1;

    this.setYZData({
      showHomeIcon,
    });
  },

  methods: {
    toBack() {
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateBack();
    },

    toHome() {
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.reLaunch({
        url: '/packages/home/<USER>/index',
      });
    },
  },
});
