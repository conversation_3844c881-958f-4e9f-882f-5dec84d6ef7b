import WscComponent from 'pages/common/wsc-component/index';
import { getLevel, getQRCode, getMemberCodeConf } from './api';
import { Hummer } from 'shared/utils/hummer';

const app = getApp();

WscComponent({
  data: {
    loading: true,
    expired: false,
    barCode: '',
    qrCode: '',
    code: '',
    shopName: '',
    mobile: '',
    levelInfo: {},
    avatar: '',
    nickname: '',
    isCrmShop: false,
    isShowMobile: false,
    showCode: false,
    aList: [],
    expiredTime: 0,
    expiredTip: '',
    clearDotUrlMap: {},
  },

  ready() {
    app.trigger('first-render', 'member-code');
  },
  observers: {
    isCrmShop(isCrmShop) {
      this.setYZData(
        { showCode: isCrmShop || this.data.showCode },
        { immediate: true }
      );
    },
  },

  created() {
    this.timer = null;
    this.getCodeInfo().then(() => {
      Hummer.mark.end('member-code');
    });
    getMemberCodeConf().then((res) => {
      const { avatar, isShowMobile, nickname, mobile } = res.userInfo;
      const {
        aList,
        expiredTime,
        expiredTip,
        clearDotUrlMap,
        shopName,
        isCrmShop,
      } = res;
      this.setYZData({
        aList,
        expiredTime,
        expiredTip,
        clearDotUrlMap,
        shopName,
        isCrmShop,
        avatar,
        isShowMobile,
        nickname: nickname === 'undefined' ? '微信用户' : nickname,
        mobile,
        loading: false,
      });
      this.pollingReq(res.expiredTime);
      this.triggerEvent('codeLoadingComplete', true);
    });
    this.updateLevelView();
  },

  detached() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  methods: {
    updateLevelView() {
      getLevel().then((res) => {
        this.setYZData({ levelInfo: res.level || {} });
      });
    },
    clickRecall() {
      this.getCodeInfo();
      this.pollingReq(this.expireTime);
    },
    pollingReq(val) {
      if (val) {
        clearInterval(this.timer);
        this.timer = setInterval(() => {
          this.getCodeInfo();
        }, val);
      }
    },
    getCodeInfo(res) {
      // 授权手机号场景
      res && this.triggerEvent('auth');
      return getQRCode()
        .then((res) => {
          const { barCode, qrCode, encodeCode: code, showBarCode } = res;
          const data = {
            barCode,
            qrCode,
            code,
            showCode: this.data.isCrmShop || showBarCode,
          };
          if (this.data.expired) {
            data.expired = false;
          }
          this.setYZData(data);
          this.getMemberCodeConfigInfo();
          return res;
        })
        .catch((msg) => {
          this.setYZData({ expired: true });
          wx.showToast({
            title: msg || '获取会员码失败',
            icon: 'none',
          });
        });
    },
    getMemberCodeConfigInfo() {
      getMemberCodeConf().then((res) => {
        const { avatar, nickname, mobile } = res.userInfo;
        this.setYZData({
          avatar,
          nickname: nickname === 'undefined' ? '微信用户' : nickname,
          mobile,
        });
      });
    },
  },
});
