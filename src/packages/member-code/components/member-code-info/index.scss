.member-code-container {
  padding-top: 33px;
  .inner {
    background-color: #fafcff;
    border-radius: 10px;
    padding: 0 20px 20px;
    min-height: 366px;
  }
  .user-info {
    position: relative;
    .avatar-out {
      padding: 6px;
      background-color: #fafcff;
      position: absolute;
      top: -31px;
      left: calc(50% - 31px);
      border-radius: 50%;
      &::before {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        left: -16px;
        top: 12px;
        background: radial-gradient(
          circle at 0 0,
          transparent 20px,
          #fafcff 21px
        );
      }
      &::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        right: -16px;
        top: 12px;
        background: radial-gradient(
          circle at 100% 0,
          transparent 20px,
          #fafcff 21px
        );
      }
      .avatar {
        width: 51px;
        height: 51px;
        border-radius: 50%;
        position: relative;
        z-index: 1;
      }
    }
    .detail {
      width: 100%;
      padding-top: 36px;
      text-align: center;
      .nickname {
        line-height: 22px;
        color: #323233;
        font-weight: bold;
        width: 100%;
        text-overflow: ellipsis;
        word-break: keep-all;
        overflow: hidden;
      }
      .level {
        color: #eeae3e;
        font-size: 12px;
        line-height: 18px;
      }
    }
  }
  .code-loading {
    text-align: center;
    padding: 100px 0;
  }
  .code-out {
    position: relative;
  }
  .code-cont {
    text-align: center;
  }
  .code {
    position: relative;
    min-height: 350px;
    padding: 32px 16px 10px;
    box-sizing: border-box;
    text-align: center;
  }

  .barcode {
    width: 100%;
    max-width: 295px;
    height: 72px;
    text-align: center;
  }

  .number {
    display: flex;
    margin: 8px 0 16px;
    color: #969799;
    font-size: 12px;
    flex-direction: column;
  }

  .qrcode-s {
    width: 160px;
    height: 160px;
  }

  .qrcode-b {
    width: 200px;
    height: 200px;
  }

  .expired {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    z-index: 1;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.96);

    .recall {
      margin-top: 16px;
    }
  }

  .tips {
    font-size: 12px;
    color: #999;
    margin-top: 7px;
  }

  .guide {
    color: #333;
    padding-top: 108px;
    min-height: 284px;
  }

  .hello {
    font-size: 16px;
    line-height: 20px;
    text-align: center;
  }

  .welcome {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    margin-top: 12px;
    display: flex;
    justify-content: center;
  }

  .shop {
    max-width: calc(100% - 120px);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .login {
    width: 138px;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 22px;
    background: #323233;
    color: #fff;
    margin: 44px auto 136px;
  }

  .icon {
    position: absolute;
    width: 240px;
    height: 40px;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }
}
