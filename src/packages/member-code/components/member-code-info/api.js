const app = getApp();

const FREETYPE = 1;

// 获取用户会员码信息
export const getQRCode = () => {
  return app.request({
    path: '/wscuser/scrm/member-code/api/getCode.json',
    data: {
      kdt_id: app.getKdtId(),
    },
  });
};

/** 获取会员资产信息和刷新时间配置 */
export function getMemberCodeConf() {
  return app.request({
    path: '/wscuser/scrm/member-code/api/getMemberCodeConf',
  });
}

// 获取用户会员等级
export const getLevel = () => {
  return app.request({
    path: '/wscuser/levelcenter/api/simpleUserLevelDetail.json',
    data: {
      type: FREETYPE,
    },
  });
};
