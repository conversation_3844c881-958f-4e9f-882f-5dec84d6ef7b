<view class="member-code-container">
  <view class="inner">
    <!-- userInfo -->
    <view class="user-info">
      <view class="avatar-out">
        <image mode="aspectFit" src="{{ avatar }}" class="avatar" />
      </view>
      <view class="detail">
        <text class="nickname" style="font-size: {{ isShowMobile ? 18 : 20 }}px;">{{ nickname }}</text>
        <view class="level" wx:if="{{ levelInfo.levelValue > 0 }}">
          <text>{{ levelInfo.name }}</text>
          <text>LV.{{ levelInfo.levelValue }}</text>
        </view>
      </view>
    </view>
    <!-- code -->
    <view wx:if="{{ mobile }}" class="code-out">
      <view class="code-cont" style="padding: {{ showCode ? '24px 0' : '40px 0' }}" catchtap="clickRecall">
        <block wx:if="{{ showCode }}">
          <image src="{{ barCode }}" class="barcode" />
          <text class="number">{{ code }}</text>
        </block>
        <view>
          <image src="{{ qrCode }}" class="{{ showCode ? 'qrcode-s' : 'qrcode-b' }}" />
        </view>
        <view class="tips">{{ expiredTip }}</view>
      </view>
      <view wx:if="{{ expired && qrCode }}" class="expired">
        <p>会员码已过期</p>
        <van-button class="recall" type="danger" plain catchtap="clickRecall">点击刷新</van-button>
      </view>
      <member-assets wx:if="{{ !isCrmShop }}" aList="{{ aList }}" clearDotUrlMap="{{ clearDotUrlMap }}" />
    </view>
    <view class="guide" wx:if="{{ !mobile && !loading }}">
      <view class="hello">HELLO</view>
      <view class="welcome">
        欢迎加入
        <view class="shop">{{shopName}}</view>
        会员
      </view>
      <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="getCodeInfo">
        <view class="login">登录/注册</view>
      </user-authorize>
      <image class="icon" mode="aspectFit" src="{{iconMember}}" />
    </view>
    <slot />
  </view>
</view>