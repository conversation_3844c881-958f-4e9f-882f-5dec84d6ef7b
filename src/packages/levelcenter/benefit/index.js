import WscPage from 'pages/common/wsc-page/index';
import args from '@youzan/weapp-utils/lib/args';

const app = getApp();

WscPage({
  data: {
    url: '/wscuser/levelcenter',
  },
  onLoad(query = {}) {
    const kdtId = app.getKdtId();
    const { benefit, alias, benefit_format } = query;
    const params = {
      kdt_id: kdtId,
      level_alias: alias,
      benefit_id: benefit,
    };
    if (benefit_format) params.benefit_format = benefit_format;
    const baseUrl = args.add(
      'https://h5.youzan.com/wscuser/levelcenter/benefit',
      params
    );
    this.setYZData({
      url: baseUrl,
    });
  },
});
