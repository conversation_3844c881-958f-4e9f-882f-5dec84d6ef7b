import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    currentFreeLevel: {
      type: Number,
      value: 0,
      observer(nv) {
        if (nv) {
          this.setIconBgStyle({ currentFreeLevel: nv });
        }
      }
    },
    currentPlusLevel: {
      type: Number,
      value: 0,
      observer(nv) {
        if (nv) {
          this.setIconBgStyle({ currentPlusLevel: nv });
        }
      }
    }
  },
  data: {
    iconStyle: ''
  },
  methods: {
    getPlusIconXByLevel(currentPlusLevel) {
      const originXPos = -16;
      // 16 - margin-left, (1-9)width - 54,(10)width - 58, 1:1.2
      let xPos = (currentPlusLevel - 1) * -69 + originXPos;
      if (currentPlusLevel > 9) {
        xPos -= 3;
      }
      return xPos;
    },
    getFreeIconXByLevel(currentFreeLevel) {
      const originXPos = -16;
      // 16 - margin-left, (1-9)width - 22，(10)width - 26 1:1.5
      let xPos = (currentFreeLevel - 1) * -38 + originXPos;
      if (currentFreeLevel > 9) {
        xPos -= 4;
      }
      return xPos;
    },
    getIconBgStyle(params) {
      const freeIconUrl = '//b.yzcdn.cn/public_files/683f97b8cccef830558636e173feb4ac.png';
      const plusIconUrl = '//b.yzcdn.cn/public_files/43812ce7e311029abc53901dcf8c424f.png';

      const originPlusIconStyle = `width: 58px; height: 16px; margin-left: 4px; background-size: 1230% 100%; background-image: url(${plusIconUrl});`;
      const originFreeIconStyle = `width: 27px; height: 16px; margin-left: 4px; background-size: 1500% 100%; background-image: url(${freeIconUrl});`;
      const currentPlusLevel = params.currentPlusLevel || this.properties.currentPlusLevel;
      const currentFreeLevel = params.currentFreeLevel || this.properties.currentFreeLevel;

      const isPlus = currentPlusLevel > 0;
      const isFree = currentFreeLevel > 0;
      if (isPlus) {
        const posX = this.getPlusIconXByLevel(currentPlusLevel);
        return originPlusIconStyle + `background-position-x: ${posX}px`;
      }
      if (isFree) {
        const posX = this.getFreeIconXByLevel(currentFreeLevel);
        return originFreeIconStyle + `background-position-x: ${posX}px`;
      }
      return '';
    },
    setIconBgStyle(params) {
      this.setYZData({
        iconStyle: this.getIconBgStyle(params)
      });
    }
  }
});
