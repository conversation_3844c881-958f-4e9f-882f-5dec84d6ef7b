import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';

const app = getApp();

WscComponent({
  properties: {
    max: {
      type: Number,
      value: 3
    },

    disabledDefaultAction: {
      type: <PERSON><PERSON>an,
      value: false
    },

    hasMobile: {
      type: <PERSON><PERSON><PERSON>,
      value: false
    },
    fetchMissionFinished: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'refreshMissionList'
    },
    upgradeMissionList: {
      type: Array,
      value: []
    }
  },

  data: {
    missions: [],
    attached: false
  },

  methods: {
    fetchMissionList() {
      app
        .request({
          path: '/wscuser/scrm/api/ump/getMissionListV2.json'
        })
        .then((res = []) => {
          this.triggerEvent('after-fetch-missions');
          this.setYZData({ missions: this.formatMissionList(res) });
        })
        .catch((msg) => {
          this.setYZData({ missions: [] });
          this.triggerEvent('after-fetch-missions');
          wx.showToast({
            title: msg,
            icon: 'error'
          });
        });
    },

    handleClickAction({ detail: { index, mission } }) {
      this.triggerEvent('action', { index, mission });
    },

    handleStateChange({ detail: { index, mission } }) {
      const missions = this.data.missions;
      missions[index] = mission;
      this.setYZData({
        missions
      });
    },

    formatMissionList(data) {
      const missions = [];
      if (!data.length) {
        this.triggerEvent('missionsNumber');
      }
      data.forEach((item) => {
        if (item.index === 2) {
          missions.unshift(...item.list);
        } else if (item.index === 3) {
          // 过滤有赞云任务
          missions.push(...get(item, 'list', []).filter((citem) => citem.name === 'DAILYSIGN'));
        } else if (item.index !== 999) {
          missions.push(...item.list);
        }
      });
      const res = missions
        .filter((item) => item.index !== 999 && !item.finished)
        .slice(0, this.properties.max);
      if (!res.length) {
        this.triggerEvent('missionsNumber');
      }
      return res;
    },
    refreshMissionList(nv) {
      if (!nv && this.data.attached) {
        this.fetchMissionList();
      }
    }
  },

  attached() {
    this.setYZData({
      attached: true
    });
    // this.fetchMissionList();
  }
});
