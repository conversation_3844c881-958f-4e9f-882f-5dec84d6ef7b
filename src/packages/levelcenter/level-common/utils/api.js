import adaptorComponents from 'constants/adaptor-components';

const app = getApp();
export function fetchComponentsOfType(type) {
  return app
    .request({
      path: '/wscuser/levelcenter/api/getDecorateComponents.json',
      method: 'get',
      data: {
        adaptorComponents: adaptorComponents.join(','),
        type
      }
    })
    .then((components = []) => {
      components = components.filter(
        item => item.type !== 'image_ad' || (item.type === 'image_ad' && item.sub_entry.length > 0)
      );

      return components;
    });
}

export function getCustomerAuth() {
  return app.request({
    path: '/wscuser/scrm/api/getCustomerAuth.json'
  });
}

export function checkIsSupportGoodsRecommend(data) {
  return app.request({
    path: '/wscuser/level/api/checkSupportRecommendGoods.json',
    data,
  });
}
