import get from '@youzan/weapp-utils/lib/get';
import omit from '@youzan/weapp-utils/lib/omit';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { LevelColorCodeMap, defaultColorCode, LEVEL_MASK } from './constant';

export const flatObject = (object, flatKeys) => {
  let flatKeysArr = [];
  if (!Array.isArray(flatKeys)) {
    flatKeysArr = [flatKeys];
  }
  return flatKeysArr.reduce((value, key) => {
    const keyValue = get(value, key, {});
    const data = {
      ...omit(value, key),
      ...keyValue,
    };
    return data;
  }, object);
};

// 查找下一个未达到的等级
export const findNextLevel = (cards, nowLevel) => {
  return cards.find((item) => item.card.minGrowth > nowLevel.currentGrowth);
};

export const toStringify = (obj) => {
  const str = JSON.stringify(obj);
  return str
    .replace(/{/g, '')
    .replace(/}/g, '')
    .replace(/"/g, '')
    .replace(/,/g, ';');
};

export const getColorCodeAndStyle = (colorCode) => {
  // 如果没有对应颜色使用默认颜色
  if (!colorCode || !LevelColorCodeMap[colorCode]) {
    colorCode = defaultColorCode;
  }

  const config = LevelColorCodeMap[colorCode];

  return {
    background: config,
    backgroundStyle: `linear-gradient(${config.degree}deg, ${config.colors
      .map((color) => `${color.color} ${color.percent}%`)
      .join(',')})`,
  };
};

export const getLevelBgInfo = (config) => {
  const { colorCode, cover, levelType = 'default' } = config;
  if (cover) {
    return {
      background: cover,
      backgroundStyle: `url(${cdnImage(
        cover,
        '!middle.jpg'
      )}) center/cover no-repeat`,
      mask: LEVEL_MASK.default,
      maskBgStyle: `url(${cdnImage(
        LEVEL_MASK.default,
        '!middle.jpg'
      )}) center/cover no-repeat`,
    };
  }

  return {
    ...getColorCodeAndStyle(colorCode),
    mask: LEVEL_MASK[levelType],
    maskBgStyle: `url(${cdnImage(
      LEVEL_MASK[levelType],
      'middle'
    )}) center/cover no-repeat`,
  };
};
