import get from '@youzan/weapp-utils/lib/get';
import pick from '@youzan/weapp-utils/lib/pick';
import omit from '@youzan/weapp-utils/lib/omit';
import { formatBenefits as originFormatBenefits } from '../../../benefit-card/utils';
import {
  consumeMissionMap,
  ConsumeMissionType,
  LevelMode,
  LevelUpgradeConditionType,
} from './constant';

export const formatBenefits = originFormatBenefits;

const termTypeText = {
  31: '天',
  32: '周',
  33: '月',
};

// 场景营销的权益处理
export const planBenefitAdapter = (planBenefit = {}) => {
  const res = [];
  const { birthdayInfoList, festivalInfoList, memberdayInfoList } = planBenefit;

  if (birthdayInfoList && birthdayInfoList.length) {
    const birthdayInfo = birthdayInfoList[0] || {};
    const { termType } = birthdayInfo.effectiveTime || {};
    const benefit = {
      name: 'birthday',
      pluginName: '生日好礼',
    };
    benefit.remark = termTypeText[termType]
      ? `生日当${termTypeText[termType]}享好礼`
      : '生日享好礼';
    res.push(benefit);
  }

  if (festivalInfoList && festivalInfoList.length) {
    res.push({
      name: 'festival',
      pluginName: '节日特权',
      remark: '节日专属优惠',
    });
  }

  if (memberdayInfoList && memberdayInfoList.length) {
    const memberdayInfo = memberdayInfoList[0] || {};
    const { termType } = memberdayInfo.effectiveTime || {};
    const benefit = {
      name: 'memberday',
      pluginName: '专属会员日',
    };
    if (termType === 31) {
      benefit.remark = '每日专享优惠';
    } else if (termType === 32) {
      benefit.remark = '每周专享优惠';
    } else if (termType === 33) {
      benefit.remark = '每月专享优惠';
    } else {
      benefit.remark = '';
    }

    res.push(benefit);
  }

  return res;
};

// 等级权益的处理
const benefitListAdapter = (benefitList, planBenefit) => {
  const list = formatBenefits(benefitList);
  list.push(...planBenefitAdapter(planBenefit));
  return list;
};

// 等级卡面的数据处理
const cardAdapter = (level) => {
  const res = {};
  res.color = level.colorCode;
  res.image = level.coverUrl;
  res.alias = level.levelAlias;
  res.levelGroupAlias = level.levelGroupAlias;
  res.minGrowth = get(level, 'levelGrantConditionList[0].minGrowth', 0);
  res.name = level.name;
  res.levelValue = level.levelValue;
  return res;
};

// * 根据当前等级和条件列表生成升级任务列表
const geneUpgradeMissionList = (
  nowLevel,
  levelGrantConditionList,
  levelName,
  isFullLevel
) => {
  const { currentTotalTradeAmount = 0, currentTotalTradeCount = 0 } = get(
    nowLevel,
    'levels[0].customerConsumeInfo',
    {}
  );
  const consumeUpgrade = levelGrantConditionList.find(
    (condition) => condition.conditionType === LevelUpgradeConditionType.CONSUME
  );
  let list = [];
  if (consumeUpgrade) {
    const behaviorCondition = consumeUpgrade?.behaviorCondition || [];
    list = Object.keys(behaviorCondition)
      .filter((mission) => mission !== 'op')
      .map((condition) => {
        const item = behaviorCondition[condition];
        switch (condition) {
          case 'minTotalTradeAmount':
            return consumeMissionMap.totalTradeAmount(
              currentTotalTradeAmount,
              item,
              levelName,
              isFullLevel
            );
          case 'minTotalTradeCount':
            return consumeMissionMap.totalTradeCount(
              currentTotalTradeCount,
              item,
              levelName,
              isFullLevel
            );
          case 'minTradeAmount':
            return consumeMissionMap.singleTradeAmount(
              item,
              levelName,
              isFullLevel
            );
          default:
            return {};
        }
      });
  }

  return list.sort((a, b) => b.rate - a.rate); // * 避免未获得等级；有消费金额；超过升级条件，显示负数;
};

// * 等级的数据处理
const levelCardsAdapter = (levelList, nowLevel, isLevelGroupEnabled = true) => {
  const list = [];
  // * 先把小于当前等级的和禁用的过滤掉
  levelList = levelList.filter((levelItem) => {
    // 小于当前用户等级的等级不展示
    if (levelItem.levelValue < nowLevel.levelValue) return;
    // 如果当前等级禁用，但没有禁用等级组，不展示这个等级
    if (!levelItem.isEnabled && isLevelGroupEnabled) return;

    return true;
  });
  const isFullLevel = levelList.length === 1;
  const hasLevel = nowLevel.levelValue >= 1;

  levelList.forEach((level, index) => {
    const isConsume = level.mode === LevelMode.CONSUME; // * 是否为消费行为模式
    let upgradeMissionList = [];
    let priorityUpgradeCondition = {};
    if (isConsume) {
      // * 如果是未注册会员，则展示一个单独的不可滑动的upgradeConditionList，以LV1的condition作为计算条件
      // * 当前等级的升级条件，以下一等为依据进行计算
      // * 第一项 && 有等级 && 不是满级 --> 数组的后一项；
      const targetLevel =
        index === 0 && hasLevel && !isFullLevel ? levelList[1] : level;
      upgradeMissionList = geneUpgradeMissionList(
        nowLevel,
        get(targetLevel, 'levelGrantConditionList'),
        get(targetLevel, 'name'),
        isFullLevel
      );

      // * 加入优先升级条件
      // ? 如果满级，优先条件设置为累计购买金额
      if (levelList.length === 1) {
        priorityUpgradeCondition =
          upgradeMissionList.find(
            (mission) => mission.type === ConsumeMissionType.AMOUNT
          ) || upgradeMissionList[0];
      } else {
        priorityUpgradeCondition = upgradeMissionList[0];
      }
    }

    const levelInfo = {
      isEnabled: !!level.isEnabled,
      card: isConsume
        ? { ...cardAdapter(level), priorityUpgradeCondition }
        : cardAdapter(level),
      levelBenefit: get(level, 'levelBenefit', {}),
      planBenefits: planBenefitAdapter(get(level, 'planBenefits', {})),
      benefitList: benefitListAdapter(
        get(level, 'levelBenefit', []),
        get(level, 'planBenefits', {})
      ),
      levelGoods: get(level, 'levelGoods', {}),
      upgradeMissionList,
      isConsume, // * 传入mode为2则表明为消费行为模式
    };

    list.push(levelInfo);
  });

  return list;
};

const SinglelevelCardsAdapter = (
  levelList,
  nowLevel,
  isLevelGroupEnabled = true,
  detailAlias
) => {
  const level =
    levelList.find((level) => detailAlias === level.levelAlias) || levelList[0];

  // 如果当前等级禁用，但没有禁用等级组，不展示这个等级
  if (!level.isEnabled && isLevelGroupEnabled) return [];

  const levelInfo = {
    isEnabled: !!level.isEnabled,
    card: cardAdapter(level),
    levelBenefit: get(level, 'levelBenefit', {}),
    planBenefits: planBenefitAdapter(get(level, 'planBenefits', {})),
    benefitList: benefitListAdapter(
      get(level, 'levelBenefit', []),
      get(level, 'planBenefits', {})
    ),
    levelGoods: get(level, 'levelGoods', {}),
  };

  const list = [levelInfo];
  return list;
};
// 付费等级处理
const paidLevelCardsAdapter = (
  levelList,
  nowLevel,
  isLevelGroupEnabled,
  lowestLevel = {}
) => {
  const list = [];
  let index = 0;
  const indexMap = {};
  levelList.forEach((level) => {
    // 如果当前等级没有招募会员,且不是用户拥有的等级
    const lowestLevelValue = get(lowestLevel, 'level.levelValue', '');
    const isExpired = get(lowestLevel, 'isExpired', true);
    // 禁用 & 非当前等级
    if (!level.isDisplay && level.levelValue !== lowestLevelValue) return;
    // 禁用 & 当前等级 & 非体验 & 已经过期
    if (!level.isDisplay && level.levelValue === lowestLevelValue) {
      if (isExpired) {
        return;
      }
    }
    // 当前等级非体验未过期，则不展示低等级
    if (lowestLevelValue && level.levelValue < lowestLevelValue && !isExpired)
      return;

    const levelInfo = {
      isDisplay: !!level.isDisplay,
      card: cardAdapter(level),
      levelBenefit: get(level, 'levelBenefit', {}),
      benefitList: benefitListAdapter(get(level, 'levelBenefit', [])),
      levelGoods: get(level, 'levelGoods', {}),
    };

    indexMap[level.levelAlias] = index;
    list.push(levelInfo);
    index++;
  });

  return {
    list,
    indexMap,
  };
};

// 成长规则文案
export const ruleAdapter = (rule) => {
  switch (rule.ruleType) {
    case 1:
      return `每消费${(rule.conditionVal / 100).toFixed(
        2
      )}元，交易完成即可获得${rule.rewardGrowth}个成长值；`;
    case 2:
      return `完成${rule.conditionVal}笔订单，交易完成时即可获得${rule.rewardGrowth}个成长值；`;
    default:
      return '';
  }
};

// 获取成长规则
export const growthRuleAdapter = (growthRule) => {
  const res = [];
  let i = 1;
  if (!growthRule || !growthRule.length) return res;
  growthRule.forEach((rule) => {
    const r = ruleAdapter(rule);
    if (r) res.push(`${i++}.${r}`);
  });
  return res;
};

// 处理得到当前等级和成长值信息
export const handleNowLevel = (level, customerGrowth) => {
  let nowLevel = null;
  const levelObj = level;
  const simpleLevel = omit(level, 'level');
  const levels = simpleLevel ? [simpleLevel] : [];
  nowLevel = get(levelObj, 'level', null);
  if (!nowLevel) {
    nowLevel = {
      levelValue: 0,
    };
  } else {
    nowLevel.identityNo = levelObj.identityNo;
  }

  nowLevel.currentGrowth = customerGrowth.currentGrowth || 0;
  nowLevel.totalGrowth = customerGrowth.totalGrowth;
  nowLevel.levels = levels;
  nowLevel.keepLevelProgress = get(levelObj, 'keepLevelProgress', null);

  return nowLevel;
};

export const levelAdapter = (data, detailAlias = undefined, lowestLevel) => {
  let nowLevel = {};

  const levelV2List = get(data, 'levelList[0].levelV2List', []);

  (levelV2List || []).forEach((level) => {
    const levelPlanBenefit = data?.levelPlanBenefits?.find(
      (levelBenefit) => levelBenefit.levelId === level.levelId
    );
    if (levelPlanBenefit) {
      level.planBenefits = pick(levelPlanBenefit, [
        'birthdayInfoList',
        'festivalInfoList',
        'memberdayInfoList',
      ]);
    }
  });

  let cards = null;
  let indexMap = null;
  // 当只需要当个等级的处理时
  if (detailAlias) {
    nowLevel = handleNowLevel(data.levels, data.customerGrowth, 1);
    cards = SinglelevelCardsAdapter(
      levelV2List,
      nowLevel,
      data.isLevelGroupEnabled,
      detailAlias
    );
  } else if (lowestLevel) {
    nowLevel = handleNowLevel(data.levels, data.customerGrowth, 2);
    const res = paidLevelCardsAdapter(
      levelV2List,
      nowLevel,
      data.isLevelGroupEnabled,
      lowestLevel
    );
    cards = res.list;
    indexMap = res.indexMap;
  } else {
    // * 免费会员等级走这个逻辑
    nowLevel = handleNowLevel(data.levels, data.customerGrowth, 1);
    cards = levelCardsAdapter(levelV2List, nowLevel, data.isLevelGroupEnabled);
  }

  return {
    nowLevel,
    growthRule: growthRuleAdapter(data.growthRule),
    cards,
    indexMap,
  };
};

export const toMissionCenter = '/wscuser/scrm/missioncenter';
