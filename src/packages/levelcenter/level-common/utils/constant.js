import accSub from '@youzan/utils/number/accSub';
import format from '@youzan/utils/money/format';

/**
 * * 将分转化为元并保留指定位数小数
 * @param {number} cent
 * @param {number} precision
 * @returns {number}
 */
function moneyFormat(cent) {
  return Number(format(cent, true, false));
}

// 等级类型
export const LevelType = {
  FREE: 1, // 免费
  PAY: 2, // 付费
};

export const ConsumeMissionType = {
  AMOUNT: 1, // * 消费总金额
  COUNT: 2, // * 消费总次数
  SINGLE: 3, // * 单笔最大消费
};

// * 消费模式升级任务列表配置
export const consumeMissionMap = {
  totalTradeAmount(currentAmount, conditionAmount, levelName, isFullLevel) {
    currentAmount = moneyFormat(currentAmount);
    conditionAmount = moneyFormat(conditionAmount);
    const need = accSub(conditionAmount, currentAmount);
    const rate = Number((currentAmount / conditionAmount).toFixed(4));
    return {
      remark: '累计购买金额',
      type: ConsumeMissionType.AMOUNT,
      subTitle: `再购买${need}元商品`,
      infoTitle: `${currentAmount}/${conditionAmount}`,
      rate,
      need,
      unit: '元',
      current: currentAmount,
      condition: conditionAmount,
      desc: isFullLevel
        ? '您已升级为最高等级'
        : `再去购买${need}元商品即可升${levelName}`,
      isFullLevel,
      icon: 'https://b.yzcdn.cn/public_files/e2c3b82c8d7337ed7fcfdead41cee065.png',
    };
  },
  totalTradeCount(currentCount, conditionCount, levelName, isFullLevel) {
    const need = conditionCount - currentCount;
    const rate = Number((currentCount / conditionCount).toFixed(4));
    return {
      remark: '累计购买次数',
      type: ConsumeMissionType.COUNT,
      subTitle: `再去下单${need}次`,
      infoTitle: `${currentCount}/${conditionCount}`,
      rate,
      need,
      unit: '次',
      current: currentCount,
      condition: conditionCount,
      desc: isFullLevel
        ? '您已升级为最高等级'
        : `再去购买${need}次商品即可升${levelName}`,
      isFullLevel,
      icon: 'https://b.yzcdn.cn/public_files/03e79c38e9092d3e2156f1563f727e6f.png',
    };
  },
  singleTradeAmount(conditionSingleAmount, levelName, isFullLevel) {
    conditionSingleAmount = moneyFormat(conditionSingleAmount);
    const need = conditionSingleAmount;
    return {
      remark: '单笔消费最大金额',
      type: ConsumeMissionType.SINGLE,
      subTitle: `单笔消费${need}元`,
      infoTitle: '',
      rate: 0,
      need,
      current: '',
      condition: need,
      desc: isFullLevel
        ? '您已升级为最高等级'
        : `单次购买${need}元立即升级到${levelName}`,
      isFullLevel,
      icon: 'https://b.yzcdn.cn/public_files/04fda855e49f36479fdbb4f49e26e10c.png',
    };
  },
};

/**
 * * 会员等级模式
 */
export const LevelMode = {
  /** 成长值模式 */
  GROWTH: 1,
  /** 消费行为模式 */
  CONSUME: 2,
};

/**
 * * 会员升级条件类型
 */
export const LevelUpgradeConditionType = {
  /** 消费行为模式 */
  GROWTH: 1,
  REGISTER: 2,
  CONSUME: 3,
};

// 等级类型
export const levelType = {
  FREE: 1, // 免费
  PAY: 2, // 付费
};

// shared/common/constant/constant.js  还有一份，由于打包机制的原因，这里单独写一份
export const LevelColorCodeMap = {
  Color010: {
    degree: 30,
    pureColor: '#63B359',
    colors: [
      { color: '#84B67D', percent: 0 },
      { color: '#6DAC64', percent: 100 },
    ],
  },
  Color020: {
    degree: 30,
    pureColor: '#2C9F67',
    colors: [
      { color: '#2c9f67', percent: 0 },
      { color: '#36A46E', percent: 100 },
    ],
  },
  Color030: {
    degree: 30,
    pureColor: '#509FC9',
    colors: [
      { color: '#81C1E2', percent: 0 },
      { color: '#55ADDB', percent: 100 },
    ],
  },
  Color040: {
    degree: 30,
    pureColor: '#5885CF',
    colors: [
      { color: '#7693C4', percent: 0 },
      { color: '#5178B8', percent: 100 },
    ],
  },
  Color050: {
    degree: 30,
    pureColor: '#9062C0',
    colors: [
      { color: '#A680CB', percent: 0 },
      { color: '#905DC4', percent: 100 },
    ],
  },
  Color060: {
    degree: 30,
    pureColor: '#D09A45',
    colors: [
      { color: '#F2C379', percent: 0 },
      { color: '#E2A850', percent: 100 },
    ],
  },
  Color070: {
    degree: 30,
    pureColor: '#E4B138',
    colors: [
      { color: '#FBD16C', percent: 0 },
      { color: '#E7B848', percent: 100 },
    ],
  },
  Color080: {
    degree: 30,
    pureColor: '#EE903C',
    colors: [
      { color: '#F4A646', percent: 0 },
      { color: '#E99429', percent: 100 },
    ],
  },
  Color081: {
    degree: 30,
    pureColor: '#F08500',
    colors: [
      { color: '#F4A646', percent: 0 },
      { color: '#FB9F2D', percent: 100 },
    ],
  },
  Color082: {
    degree: 30,
    pureColor: '#A9D92D',
    colors: [
      { color: '#B9D27B', percent: 0 },
      { color: '#9FBC54', percent: 100 },
    ],
  },
  Color090: {
    degree: 30,
    pureColor: '#DD6549',
    colors: [
      { color: '#E8755C', percent: 0 },
      { color: '#EB6649', percent: 100 },
    ],
  },
  Color100: {
    degree: 30,
    pureColor: '#CC463D',
    colors: [
      { color: '#E3675F', percent: 0 },
      { color: '#DB4D45', percent: 100 },
    ],
  },
  Color101: {
    degree: 30,
    pureColor: '#CF3E36',
    colors: [
      { color: '#E3675F', percent: 0 },
      { color: '#EC362B', percent: 100 },
    ],
  },
  Color102: {
    degree: 30,
    pureColor: '#5E6671',
    colors: [
      { color: '#BABABA', percent: 0 },
      { color: '#7F7F7F', percent: 100 },
    ],
  },
};

export const LEVEL_MASK = {
  [levelType.FREE]:
    '//b.yzcdn.cn/public_files/cd2927863946844a21b8684a0c356b3f.png',
  [levelType.PAY]:
    '//b.yzcdn.cn/public_files/a22b94c2c3b46928e3f2d0f734da7463.png',
  default: '',
};

// 默认 ColorCode 值
export const defaultColorCode = 'Color060';

export const goodsRecommendTitleConfig = {
  title: '更多精选商品',
  showTitleComponent: 1,
  showMethod: 1,
};
