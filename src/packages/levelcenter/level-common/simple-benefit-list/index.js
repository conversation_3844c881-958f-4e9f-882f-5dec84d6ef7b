import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    benefitsList: {
      type: Array,
      value: [],
      observer(nv) {
        this.setShowList(nv);
      }
    },
    unTakeVisible: Boolean,
    subType: Number,
    rowSize: {
      type: Number, // 每一行几个
      value: 4
    },
    maxRow: {
      type: Number, // 最多有几行
      value: 2
    },
    nowLevelAlias: String,
    kdtId: String
  },

  data: {
    showList: [],
    customClass: ''
  },

  methods: {
    toBenefitLink(e) {
      const { rowSize, maxRow, benefitsList } = this.properties;
      const mostIconNum = rowSize * maxRow;
      let index = e.currentTarget.dataset.index;
      if (benefitsList.length > mostIconNum && index === 7) {
        index = 0;
      }
      const { kdtId, nowLevelAlias } = this.properties;
      const weappUrl = `/packages/levelcenter/benefit/index?kdt_id=${kdtId}&alias=${nowLevelAlias}&benefit=${index}`;
      wx.navigateTo({
        url: weappUrl
      });
    },
    setShowList(benefitsList) {
      const { rowSize, maxRow } = this.properties;
      const mostIconNum = rowSize * maxRow;
      const moreIconUrl = '//b.yzcdn.cn/public_files/2186edb4d3976ca13cb66b4c80f63669.png';
      let showList = benefitsList;
      if (benefitsList.length > mostIconNum) {
        showList = benefitsList.slice(0, mostIconNum - 1);
        showList.push({
          appName: '更多权益',
          icon: moreIconUrl
        });
      }
      this.setYZData({
        showList,
        customClass: showList.length > 4 ? 'benefit-list-flex-start' : 'benefit-list-flex-center'
      });
    }
  }
});
