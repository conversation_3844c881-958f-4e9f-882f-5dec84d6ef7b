<view>
  <!-- 权益列表 -->
  <view wx:if="{{ showList.length > 0 }}" class="simple-benefit-list">
    <view
      class="benefit-list {{ customClass }}"
    >
      <view
        wx:for="{{ showList }}"
        wx:key="{{ item.key }}"
        wx:for-index="index"
        class="benefit-item"
      >
        <text wx:if="{{ item.key === 'experienceCard' && unTakeVisible }}" class="badge">待领取</text>
        <text wx:if="{{ item.key === 'coupon' && item.total }}" class="badge coupon-badge">{{ item.total }}张</text>
        <view
          data-index="{{ index }}"
          class="benefit_item-wrapper"
          bindtap="toBenefitLink"
        >
          <image
            wx:if="{{ item.icon }}" 
            src="{{ item.icon }}"
            class="icon-img"
          />
          <image
            wx:else
            src="//img.yzcdn.cn/memberlevel/v2/{{ item.name }}@2x.png"
            class="icon-img"
          />
          <text class="icon-name">{{ item.appName || item.pluginName }}</text>
        </view>
      </view>
    </view>
  </view>
</view>
