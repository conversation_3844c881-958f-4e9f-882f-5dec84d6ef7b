import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'utils/open-web-view';
import args from '@youzan/weapp-utils/lib/args';
import navigate from '@/helpers/navigate';

const missionState = {
  start: 0,
  done: 30
};

const app = getApp();
const kdtId = app.getKdtId();

WscComponent({
  properties: {
    mission: {
      type: Object
    },

    hasBindPhone: {
      type: Boolean
    },

    index: {
      type: Number
    },

    disabledDefaultAction: {
      type: Boolean
    }
  },

  data: {
    showMore: false,
    isOpen: false
  },

  methods: {
    handleClickDoMission() {
      if (this.properties.disabledDefaultAction) {
        this.triggerEvent('missionClick', { index: this.index, mission });
        return;
      }
      const mission = this.properties.mission;
      if (mission.finished || mission.state !== missionState.start) {
        return;
      }
      switch (mission.name) {
        case 'BINDPHONE':
          break;
        case 'IMPROVEID':
          this.handleClickToImproveid();
          break;
        case 'BOOKMARk':
          this.handleClickToCollectGift();
          break;
        default: {
          this.navigateWithUrlParams(mission.urlParams);
        }
      }
    },

    // 绑定手机号任务
    bindGetPhoneNumber() {
      const { reward } = this.properties.mission;
      // 已经绑定了手机号
      if (this.properties.hasBindPhone) {
        this.updateMissionState('BINDPHONE').then(() => {
          reward &&
            wx.showToast({
              title: `已完成手机号绑定任务，成长值+${reward}`,
              icon: 'none',
              duration: 2000
            });
          this.triggerEvent('missionStateChange', {
            index: this.properties.index,
            mission: {
              ...this.properties.mission,
              state: missionState.done,
              finished: true
            }
          });
        });
      } else {
        this.triggerEvent('bindMobile', true);
      }
    },

    // 完善信息任务
    handleClickToImproveid() {
      const { state, reward } = this.properties.mission;
      const url = `https://h5.youzan.com/wscuser/membercenter/setting?is-improveid=true&state=${state}&reward=${reward}&kdt_id=${kdtId}&isWeapp=1`;
      openWebView(url);
    },

    // 收藏有礼
    handleClickToCollectGift() {
      const { reward } = this.properties.mission;
      app
        .request({
          path: '/wscuser/scrm/api/ump/getCollectGift'
        })
        .then((res = {}) => {
          const { auditStatus } = res;
          // auditStatus == 1时审核通过，0是待审核，2 拒绝
          if (auditStatus === 1) {
            reward &&
              this.updateMissionState('BOOKMARK').then(() => {
                wx.showToast({
                  title: `已完成收藏小程序任务，成长值+${reward}`,
                  duration: 2000
                });
              });
          } else {
            this.navigateWithUrlParams(this.properties.mission.urlParams);
          }
        })
        .catch((msg) => {
          wx.showToast({
            title: msg || '网络错误',
            icon: 'error',
            duration: 1000
          });
        });
    },

    navigateWithUrlParams({ url, weappUrl, type }) {
      if (weappUrl) {
        if (type && navigate[type]) {
          navigate[type]({ url: weappUrl });
        } else {
          navigate.navigate({ url: weappUrl });
        }
      } else {
        openWebView(args.add(url, { kdt_id: kdtId }));
      }
    },

    // 更新任务状态
    updateMissionState(name) {
      const formData = {
        missionTplType: name,
        state: missionState.done
      };
      return app.request({
        path: '/wscuser/scrm/api/ump/update',
        method: 'POST',
        data: formData
      });
    }
  }
});
