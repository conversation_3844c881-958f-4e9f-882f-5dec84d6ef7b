@mixin flexCenter {
  display: flex;
  align-items: center;
}

.info-authorize {
  display: flex;
  align-items: center;
  background-color: #252525;

  .info-promot {
    margin-left: 12px;
    color: #fff;
  }
}

.info-header {
  display: flex;
  padding: 12px 0 6px 18px;
  align-items: center;
  background-color: #252525;

  .van-cell {
    background-color: #252525;
  }

  .value-link {
    @include flexCenter;

    font-size: 14px;
    color: #969799;
    justify-content: flex-end;
    margin-right: 16px;
    font-weight: normal;
  }

  .van-icon {
    font-size: 12px;
    color: #969799;
  }

  .person_title {
    color: #fff;
    font-weight: bold;
    font-size: 17px;
    line-height: 21px;
    @include flexCenter;

    justify-content: space-between;
  }

  .person_name {
    @include flexCenter;
  }

  .person_desc {
    font-size: 13px;
    color: #969799;
    @include flexCenter;

    width: 200px;
    line-height: 16px;
    margin-top: 4px;
  }

  .level-icon {
    background: #969799;
    width: 43px;
    line-height: 12px;
    color: #000;
    font-size: 12px;
    border-radius: 10px;
    display: inline-block;
    text-align: center;
    position: relative;
    margin-left: 5px;

    .level-img {
      position: absolute;
      top: -1px;
      left: -6px;
      width: 14px;
      height: 14px;
      border-radius: 7px;
      z-index: 1;
      background: #252525;
      padding-right: 1px;
    }
  }

  .info-avatar {
    height: 48px;
    width: 48px;
    border-radius: 999px;
    border: 1.5px solid #3d3d3d;

    image {
      height: 48px;
      width: 48px;
      border-radius: 999px;
    }
  }

  .person-info {
    padding-left: 12px;
    flex-grow: 1;
  }
}

.desc-icon {
  margin-left: 5px;
  height: 12px;

  img {
    width: 12px;
    height: 12px;
  }
}
