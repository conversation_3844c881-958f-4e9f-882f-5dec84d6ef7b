import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'utils/open-web-view';
import get from '@youzan/weapp-utils/lib/get';
import { moment as formatDate } from 'utils/time';
import { LevelType } from '../utils/constant';

const app = getApp();

WscComponent({
  properties: {
    userLevel: {
      type: Object,
      value: {},
      observer(nv) {
        if (nv.customerSource) {
          this.setNowDesc(nv);
        }
      }
    },
    linkText: {
      type: String,
      value: ''
    },
    link: {
      type: String,
      value: ''
    },
    linkQuery: {
      type: Object
    },
    descLink: {
      type: String,
      value: ''
    }
  },
  data: {
    avatar: '',
    desc: '',
    levelName: '',
    userInfo: {
      avatarUrl: 'https://img.yzcdn.cn/public_files/aa912255b9328b35a53d548b0979086d.png'
    },
    hasAuth: false
  },
  methods: {
    linkTo() {
      const { link, linkQuery } = this.properties;
      openWebView(link, { query: linkQuery });
    },

    setNowDesc(nv) {
      this.setYZData(this.getNowDesc(nv));
    },

    getNowDesc(userLevel) {
      const userLevelType = get(userLevel, 'level.levelType', 0);
      const levelValue = get(userLevel, 'level.levelValue', -1);
      const currentFreeLevel = userLevelType === LevelType.FREE ? levelValue : -1;
      const currentPlusLevel = userLevelType === LevelType.PAY ? levelValue : -1;
      // 免费会员中心逻辑
      const level = get(userLevel, 'level.levelAlias', null);
      if (!level) {
        return {
          desc: '当前未开启会员',
          currentFreeLevel,
          currentPlusLevel
        };
      }
      if (userLevel.termEndAt === 0) {
        return {
          desc: '永久有效',
          currentFreeLevel,
          currentPlusLevel
        };
      }
      return {
        desc: `当前等级有效期 ${formatDate(userLevel.termEndAt, 'YYYY-MM-DD')}`,
        currentFreeLevel,
        currentPlusLevel
      };
    },

    onGetUserInfo(e) {
      const userInfo = get(e, 'detail.userInfo', {});

      if (e && e.detail && e.detail.userInfo.nickName) {
        const kdtId = getApp().getKdtId();
        this.getUserInfoOutOfSensitive(userInfo).then((res) => {
          this.setYZData({
            userInfo: res,
            kdtId,
            hasAuth: e.detail.userInfo || e.detail.isNotValid
          });
          this.triggerEvent('hasAuth', userInfo);
        });
      }
    },

    getUserInfoOutOfSensitive(userInfo) {
      return new Promise((resolve) => {
        wx.getStorage({
          key: 'app:token',
          success: (res) => {
            const token = res.data;
            const phone = get(token, 'mobile', '');
            const nickName = get(userInfo, 'nickName', '');
            if (nickName === phone && nickName.length === 11) {
              userInfo.nickName = `${nickName.substr(0, 3)}****${nickName.substr(7, 10)}`;
            }
            resolve(userInfo);
          }
        });
      });
    }
  },

  attached() {
    app.getUserInfo(res => {
      this.getUserInfoOutOfSensitive(res.userInfo).then((userInfo) => {
        this.setYZData({
          userInfo,
          hasAuth: userInfo || userInfo.nickName
        });
        this.triggerEvent('hasAuth', userInfo);
      });
    });
  }
});
