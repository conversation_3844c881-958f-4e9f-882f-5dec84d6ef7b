<view class="info-header">
  <block wx:if="{{ hasAuth }}">
    <view class="info-avatar">
      <image src="{{ userInfo.avatarUrl }}"></image>
    </view>
    <view class="person-info">
      <view class="person_title">
        <view class="person_name">
          {{ userInfo.nickName }}
          <level-icon current-free-level="{{ currentFreeLevel }}" current-plus-level="{{ currentPlusLevel }}" />
        </view>
        <view wx:if="{{ !!linkText }}" class="value-link">
          <text bindtap="linkTo">{{ linkText }}</text>
          <van-icon name="arrow" />
        </view>
      </view>
      <view class="person_desc" bindtap="descLinkTo">
        {{ desc }}
      </view>
    </view>
  </block>
  
  <block wx:else>
    <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" bindgetuserinfo="onGetUserInfo" data-nav="authorize">
      <view class="info-authorize">
        <view class="info-avatar">
          <image src="{{ userInfo.avatarUrl || 'https://img.yzcdn.cn/public_files/aa912255b9328b35a53d548b0979086d.png' }}" />
        </view>
        <view class="info-promot">
          点击显示微信头像
        </view>
      </view>
    </user-authorize>
  </block>
</view>
