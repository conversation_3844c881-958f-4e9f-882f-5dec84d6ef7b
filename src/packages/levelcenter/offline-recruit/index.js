/**
 * 线下门店拉新：分发拉新入接口的页面
 * 参考common/webview
 */
import args from '@youzan/weapp-utils/lib/args';
import WscPage from 'pages/common/wsc-page/index';
import buildUrl from '@youzan/utils/url/buildUrl';

const app = getApp();
/**
 * todo  common/webview 使用Page
 */
WscPage({
  data: {
    src: '',
  },

  onLoad(query = {}) {
    const weappVersion = app.getVersion();
    const { kdtId } = query;
    if (query.title) {
      wx.setNavigationBarTitle({ title: decodeURIComponent(query.title) });
      delete query.title;
    }
    const recruitLandingPage = buildUrl(
      'https://h5.youzan.com/wscuser/levelcenter/offline-recruit',
      '',
      kdtId
    );
    const recruitLandingPageWithParams = args.add(
      recruitLandingPage,
      {
        ...query,
        weappVersion,
      },
      // 小程序 query 里面拿到的值没有 decode 这边不需要再次 encode
      false
    );

    if (recruitLandingPageWithParams) {
      this.setYZData({
        src: recruitLandingPageWithParams,
      });
    }
  },
});
