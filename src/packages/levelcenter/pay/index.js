// 下单页（载入H5页面）
import Event from '@youzan/weapp-utils/lib/event';
import Args from '@youzan/weapp-utils/lib/args';
import WscPage from 'pages/common/wsc-page/index';
import { getRecruitMemberParams } from 'utils/level';

const app = getApp();

WscPage({
  data: {
    url: '',
  },
  onLoad(options = {}) {
    const { alias } = options;
    this.alias = alias;
    Event.on('buy-h5:pay:call', (detail) => {
      this.orderNo = detail.orderNo;
    });
    const kdtId = app.getKdtId();
    const baseUrl = `https://cashier.youzan.com/pay/wscuser_paylevel?kdt_id=${kdtId}`;
    const urlQuery = getRecruitMemberParams(options);
    if (alias) {
      urlQuery.alias = alias;
    }
    this.setYZData({
      url: Args.add(baseUrl, urlQuery),
    });
  },

  onShow() {
    const { orderNo } = this;
    if (orderNo) {
      app
        .request({
          origin: 'cashier',
          path: '/pay/wsctrade/order/buy/prepare.json',
          method: 'POST',
          data: { order_no: orderNo },
        })
        .then((data) => {
          const redirect = data.redirectConfig || {};

          if (redirect.timeout) {
            // 订单已超时
            wx.redirectTo({
              url: `/packages/levelcenter/plus/index?alias=${this.alias}`,
            });
          } else if (redirect.orderPaid) {
            // 订单已支付 跳转到支付成功
            wx.redirectTo({
              url: `/packages/levelcenter/plus/index?alias=${this.alias}`,
            });
          } else if (redirect.orderCanceled) {
            // 订单已取消
            wx.redirectTo({
              url: `/packages/levelcenter/plus/index?alias=${this.alias}`,
            });
          } else if (redirect.peerpay) {
            const errorId = app.db.set({ text: '小程序不支持找人代付' });
            wx.redirectTo({
              url: '/packages/common/error/index?dbid=' + errorId,
            });
          }
        })
        .catch(() => {
          // do nothing
        });
    }
  },
});
