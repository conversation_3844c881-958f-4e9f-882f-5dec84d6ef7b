.title {
  font-size: 14px;
  color: #333;
  text-align: center;
  margin-bottom: 10px;
}

.swipe-container {
  position: relative;
  height: 174px;
  align-content: center;
  flex-direction: row;
  background-color: #fff;
}

.swipe-wrapper {
  height: 154px;
  padding: 10px;
}

/*卡*/

.card-wrapper{
  width: calc(100% - 20px);
  margin:0 10px;
  position: relative;
  height: 100%;
}

.background {
  position: absolute;
  height: 140px;
  opacity: 0.2;
  background-image: linear-gradient(-90deg, #e3bd68 0%, #d4ae6e 100%);
  width: 100%;
}

.card-region {
  padding: 20px;
  height: 100%;
  color: #fff;
  box-sizing: border-box;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  background-size: cover;
}

.card-back {
  position: absolute;
  top: 0;
  right: 30px;
  width: 128px;
  height: 117px;
}

.card-header {
  height: 35px;
  line-height: 35px;
  font-size: 16px;
  margin-bottom: 10px;
  position: relative;
  overflow: hidden;
}

.shop-name {
  float: left;
  background-size: 20px;
  height: 20px;
  line-height: 20px;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  word-break: break-all;
  font-size: 12px;
}

.shop-logo {
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: top;
  background-size: 30px;
  border-radius: 15px;
  background-position: center;
  background-repeat: no-repeat;
  margin-right: 5px;
}

.member-type {
  font-size: 22px;
  line-height: 24px;
  margin-bottom: 30px;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.card-content {
  font-size: 12px;
  overflow: hidden;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.expiry-date {
  float: left;
}

.card-state {
  float: right;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

/* 权益 */

.benefit {
  background-color: #fff;
  margin-bottom: 10px;
  padding: 0 10px;
}

.benefit-item {
  width: 100%;
  font-size: 14px;
  text-align: center;
  display: inline-block;
}

.item {
  width: 40px;
  height: 40px;
  margin: 10px auto;
}

.text {
  display: block;
  font-size: 14px;
  color: #333;
}

.remark {
  display: block;
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
