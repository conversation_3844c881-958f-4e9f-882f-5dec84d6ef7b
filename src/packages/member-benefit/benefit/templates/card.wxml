<template name="card">
  <view class="card-region" style="{{ coverUrl ? 'background-image:url('+ coverUrl +')':(color ? 'background-color:'+ color +';':'background-image: linear-gradient(90deg, #e3bd68 10%, #ae905c 100%);') }}">
    <image class="card-back" src="{{ backSrc }}"/>
    <view class="card-header">
      <view class="shop-name">
        <image
          class="shop-logo" src="{{ logoSrc }}"/>
        <text>{{ shopName }}</text>
      </view>
    </view>
    <view class="member-type">
      <text>{{ memberType }}</text>
    </view>
    <view class="card-content">
      <view class="expiry-date">
        <text>{{ expiryDate }}</text>
      </view>
      <view class="card-state">{{ cardState }}</view>
    </view>
  </view>
</template>
