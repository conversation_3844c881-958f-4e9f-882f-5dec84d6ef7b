<import src="./icon.wxml"/>

<template name="benefit">
  <view class="benefit">
    <view class="title">
      <text>尊享权益</text>
    </view>
    <block wx:for="{{ rows }}" wx:for-item="row" wx:key="index">
      <van-row>
        <block wx:for="{{ row }}" wx:for-item="item" wx:key="index">
          <van-col span="8">
            <template is="icon" data="{{ ...item }}"/>
          </van-col>
        </block>
      </van-row>
    </block>
  </view>
</template>
