<import src="./card.wxml"/>

<template name="swipe-cards">
  <view class="swipe-container">
    <view class="background"/>
    <swiper
        previous-margin="20px"
        next-margin="20px"
        class="swipe-wrapper"
        bindchange="handleChange">
      <block wx:for="{{ cards }}" wx:key="index">
        <swiper-item item-id="{{ index }}">
          <view class="card-wrapper">
            <template is="card" data="{{ ...item, logoSrc, backSrc, shopName }}"/>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
</template>
