import navigate from '@/helpers/navigate';
import WscPage from '../../../pages/common/wsc-page/index';

const app = getApp();
let map = null;
let globalData = null;
const api = {
  getCards: '/wscuser/memberbenefit/api/getCards'
};

const paddingZero = (num) => {
  return num < 10 ? `0${num}` : num;
};


WscPage({
  data: {
    backSrc: '',
    logoSrc: '',
    shopName: '',
    index: 0,
    benefits: [],
    cards: [],
    showIntroduction: false,
  },
  handleChange(e) {
    this.setYZData({
      index: e.detail.current
    });
  },
  genBenefit(benefit) {
    return Object.assign(benefit, map[benefit.type]);
  },
  genBenefitList(benefitList) {
    return benefitList.map((benefit) => this.genBenefit(benefit));
  },
  genExpiryDate(card) {
    if (!card.endDate) return '';
    const date = new Date(card.endDate);
    const month = date.getMonth() + 1;
    return `${date.getFullYear()}-${paddingZero(month)}-${paddingZero(date.getDate())} 到期`;
  },
  genBenefitRows(benefits) {
    const res = [];
    benefits.forEach(benefit => {
      res.push([]);
      const rows = res[res.length - 1];
      benefit.forEach((icon, i) => {
        if ((i + 1) % 3 === 1) rows.push([]);
        rows[rows.length - 1].push(icon);
      });
    });
    return res;
  },
  sortCards(cards, benefits) {
    let index = -1;
    const now = Date.now();
    cards.forEach((card, i) => {
      if (card.status === -1) {
        card.cardState = '未开通';
        return;
      }
      card.expiryDate = this.genExpiryDate(card);
      if (card.isEnabled && card.status === 1) {
        if (card.endDate <= now) {
          card.cardState = '已过期';
          return;
        }
        if (card.beginDate <= now && card.endDate > now) {
          card.cardState = '生效中';
          index = i;
          return;
        }
      }

      if (card.status === 2) {
        card.cardState = '已冻结';
        return;
      }

      card.cardState = '未生效';
    });
    if (index >= 0) {
      cards.unshift(cards.splice(index, 1)[0]);
      benefits.unshift(benefits.splice(index, 1)[0]);
    }
    this.setYZData({
      cards,
      benefits: this.genBenefitRows(benefits)
    });
  },
  jumpToMember() {
    navigate.navigate({
      url: '/packages/home/<USER>/index?alias=PaVsIC1SOZ'
    });
  },
  jumpToIntroduction() {
    navigate.redirect({
      url: '/packages/home/<USER>/index?alias=NroktuKj5y'
    });
  },
  onLoad() {
    const cdn = 'https://b.yzcdn.cn';
    const systemInfo = app.getSystemInfoSync();

    let devicePixelRatio = systemInfo.pixelRatio ? Math.min(systemInfo.pixelRatio, 3) : 2;
    map = {
      shipping: {
        url: `${cdn}/wscuser/memberbenefit/shipping@${devicePixelRatio}x.png`,
        text: '包邮',
      },
      advance: {
        url: `${cdn}/wscuser/memberbenefit/advance@${devicePixelRatio}x.png`,
        text: '提前购',
      },
      refund: {
        url: `${cdn}/wscuser/memberbenefit/refund@${devicePixelRatio}x.png`,
        text: '极速退款',
      },
      discount: {
        url: `${cdn}/wscuser/memberbenefit/discount@${devicePixelRatio}x.png`,
        text: '专享折扣',
      },
      grayDiscount: {
        url: `${cdn}/wscuser/memberbenefit/discount_gray@${devicePixelRatio}x.png`,
        text: '专享折扣',
      }
    };
    this.setYZData({
      backSrc: `${cdn}/wscuser/memberbenefit/card-background@${devicePixelRatio}x.png`,
      logoSrc: `${cdn}/wscuser/memberbenefit/logo@${devicePixelRatio}x.png`
    });
    app.request({
      path: api.getCards
    }).then((data) => {
      if (!data.cards.length) {
        this.jumpToIntroduction();
      }
      this.setYZData({
        logoSrc: globalData.shopInfo.logo,
        shopName: globalData.shopInfo.shop_name
      });
      const benefits = data.benefits.map((benefitList) => this.genBenefitList(benefitList));
      this.sortCards(data.cards, benefits);
    }).catch((err) => {
      wx.showToast({
        title: err.msg || '获取会员卡信息失败，请重试',
        icon: 'none'
      });
    });
  }
});
