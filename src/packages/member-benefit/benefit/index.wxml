<import src="./templates/benefit.wxml"/>
<import src="./templates/swipe-cards.wxml"/>

<view>
  <block wx:if="{{ cards.length > 0 }}">
    <view>
      <template is="swipe-cards" data="{{ cards, handleChange, backSrc, logoSrc, shopName }}"/>
      <template is="benefit" data="{{ rows:benefits[index] }}"/>
      <van-cell-group>
        <van-cell title="会员介绍" is-link bindtap="jumpToMember"/>
      </van-cell-group>
    </view>
  </block>
</view>
