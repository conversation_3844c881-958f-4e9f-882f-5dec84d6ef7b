import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    src: '',
  },

  onLoad() {
    this.setYZData({
      src: 'https://uic.youzan.com/passport/settings',
    });
    wx.setNavigationBarTitle({
      title: '账号与安全',
    });
  },

  handleMessage(event) {
    /**
     * 消息体结构
     * {
     *    type: string;
     *    mobile?: boolean;
     *    nicknameAndAvatar?: boolean;
     * }
     */
    const { data = [] } = event.detail;
    const authorizeChangeMessages = data
      .filter((i) => i.type === 'user-authorize:authorize-change')
      .map((i) => {
        delete i.type;
        return i;
      });
    if (authorizeChangeMessages.length) {
      app.syncAuthState();
    }
  },
});
