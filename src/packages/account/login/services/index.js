import { loginByWx } from 'shared/components/account/wx-login/service';

const app = getApp();

const COUNTRY_CODE_CN = '+86';

const Post = function (options = {}) {
  // 合并默认选项
  options = {
    config: {
      skipShopInfo: true,
    },
    method: 'POST',
    header: {
      'content-type': 'application/x-www-form-urlencoded',
    },
    origin: 'uic',
    data: {},
    ...options,
  };
  return app.request(options);
};

/**
 * 获取验证码
 * @param {*} data { mobile: number; ticket: string }
 * @param {*} times
 * @param {*} success
 * @param {*} fail
 */
function fetchCode(data, times, success, fail) {
  // POST send
  return Post({
    origin: 'uic',
    pathname: '/passport/weapp/login-page/sms.json',
    path: '/passport/weapp/login-page/sms.json',
    data: {
      ...data,
      countryCode: COUNTRY_CODE_CN,
    },
    success: (data) => {
      success(data);
    },
    fail,
  });
}

/**
 * @description 验证码登录
 * @param params 参数
 */
function loginBySms(data) {
  app.logger.skynetLog({
    name: 'loginBySms',
    message: 'loginBySms',
    detail: data,
  });

  return Post({
    origin: 'uic',
    pathname: '/passport/login.json',
    path: '/passport/login.json',
    data,
  });
}

/**
 * 获取协议列表
 */
function getProtocols() {
  return import('@youzan/passport-protocol').then(({ getProtocolData }) => {
    return getProtocolData().then((res) => {
      return (res.protocolData || []).map((item) => ({
        name: item.agreementTplName,
        url: item.url,
      }));
    });
  });
}

export default {
  fetchCode,
  loginBySms,
  loginByWx,
  getProtocols,
};
