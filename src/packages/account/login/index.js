import { showWxLoginButton } from 'shared/utils/wx-login';
import WscPage from 'pages/common/wsc-page/index';

import methods from './method';

const app = getApp();
const homePage = '/pages/home/<USER>/index';

WscPage({
  data: {
    pageText: {
      tips: '为了你的资产安全请绑定手机号',
    },
    formData: {
      countryCode: '+86',
      mobile: '',
      wxMobile: '',
      captcha: '',
      captchaTime: null,
      isProtocolAgreed: false,
    },
    captcha: {
      text: '获取验证码',
      code: '',
      times: 1,
      countdown: 60,
      textStyle: 'acc-code__btn--enabled',
      btnStyle: '',
      timer: null,
    },
    loginBtn: {
      text: '登录',
      disabled: true,
      wxDisabled: false,
      wxText: '手机号快速登录',
      btnStyle: 'font-size:16px;font-weight:500;',
    },
    redirect: {
      logined: homePage,
      navigateType: 'redirectTo',
    },

    showWxButton: false,
  },

  onLoad(options) {
    let { redirectUrl } = options;
    if (options.encodeRedirectUrl) {
      redirectUrl = decodeURIComponent(options.encodeRedirectUrl);
    }
    if (redirectUrl) {
      this.setYZData({
        'redirect.logined': redirectUrl,
      });
    }

    if (options.navigateType) {
      this.setYZData({
        'redirect.navigateType': options.navigateType,
      });
    }
    // 检查微信用户的登录情况
    wx.setNavigationBarTitle({
      title: '验证码登录',
    });

    app.checkSessionWithWx().catch(() => {});

    this.watchProtocolAgree();

    app.resolveTeeAPI().then((api) => {
      // 获取是否支持原生获取手机号能力
      api.getNativeMobileAbility({ kdtId: this.kdtId }).then((enabled) => {
        this.setYZData(
          {
            showWxButton: enabled && showWxLoginButton(),
          },
          { immediate: true }
        );
      });
    });
  },
  detached() {
    this.detachedProtocol();
  },

  /**
   * @description 登录成功后的跳转
   */
  loginRedirect() {
    const { logined: url, navigateType } = this.data.redirect;
    const navigateActionParams = {};

    // 默认跳转tabbar主页面,需要使用switchTab方法
    // 跳转地址默认为首页，后退时对navigate不做转换
    const navigate =
      url === homePage && navigateType !== 'navigateBack'
        ? 'switchTab'
        : navigateType;

    if (navigate !== 'navigateBack') {
      navigateActionParams.url = url;
    } else {
      navigateActionParams.delta = 1;
    }
    return wx[navigate]({
      ...navigateActionParams,
      success: () => {},
      fail: () => {
        wx.showToast({
          icon: 'none',
          title: '登录跳转失败，请关闭小程序重新登录。',
        });
      },
    });
  },
  ...methods,
});
