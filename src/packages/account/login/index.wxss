.input {
  caret-color: #07c160 !important;
  font-size: 14px !important;
  line-height: 1em !important;
  padding: 4px 0 !important;
  height: 24px !important;
}

.captcha {
  padding: 40px 30px 0 30px;
  display: flex;
  flex-direction: column;
  position: absolute;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: #f8f8f8;
}

.captcha .container {
  flex: 1;
  min-height: 420px;
}

.captcha .container .tips {
  font-size: 14px;
  color: #999999;
}

.captcha .container .section {
  padding: 21px 0 10px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.captcha .container .section .country {
  padding-right: 10px;
  margin-right: 10px;
  color: #333;
  position: relative;
  font-size: 14px;
}

.captcha .container .section .country::after {
  display: block;
  content: ' ';
  width: 1px;
  height: 14px;
  background: #e5e5e5;
  position: absolute;
  top: 50%;
  margin-top: -7px;
  right: 0;
}

.fetch-captcha-btn {
  color: #38f;
  font-size: 14px;
  margin-left: 10px;
}

.fetch-captcha-btn.acc-code--enabled {
  /* background-color: white; */
}

.fetch-captcha-btn .acc-code__btn--enabled {
  color: #06be04;
}

.van-field,
van-field {
  flex: 1 !important;
  background: none !important;
  padding: 0 !important;
  width: 100% !important;
}

.van-field::after {
  display: none !important;
}

.van-cell {
  padding: 0px !important;
  background: #f8f8f8 !important;
}

.login-btn {
  margin-top: 30px;
}

.wx-login-btn {
  margin-top: 20px;
}

.custom-input-class {
  background: red;
}

.countdown {
  font-size: 12px;
  color: #ccc;
}
