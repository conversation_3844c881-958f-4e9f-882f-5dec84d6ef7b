<view class="captcha">
  <view class="container">
    <view class="tips">{{pageText.tips}}</view>
    <view class="section">
      <view class="country" catchtap="selectCountry">中国 {{formData.countryCode}}</view>
      <van-field
        type="number"
        value="{{formData.mobile}}"
        bind:input="bindMobileInput"
        placeholder="请输入手机号"
        maxlength="11"
        clearable
        border="{{ false }}"
      ></van-field>
    </view>
    <view class="section">
      <van-field
        input-class="custom-input-class"
        type="number"
        value="{{formData.captcha}}"
        bind:input="bindCaptchaInput"
        placeholder="请输入6位验证码"
        maxlength="6"
        clearable
        border="{{ false }}"
      ></van-field>

      <user-authorize authTypeList="{{ ['protocol'] }}" bind:next="fetchCaptcha" >
        <view class="fetch-captcha-btn {{captcha.btnStyle}}">
          {{ captcha.text }}
        </view>
      </user-authorize>
    </view>
    <protocol-field id="protocolField" bind:agreementChange="handleAgreementChange" />
    <van-button
      catchtap="login"
      class="button"
      custom-class="login-btn"
      custom-style="color:#323233; {{ loginBtn.btnStyle }}"
      block
      disabled="{{loginBtn.disabled}}"
    >
      {{loginBtn.text}}
    </van-button>

    <block wx:if="{{ showWxButton }}">
      <!-- 未同意协议前先校验 -->
      <van-button
        wx:if="{{ formData.isProtocolAgreed }}"
        open-type="getPhoneNumber"
        class="button"
        custom-class="wx-login-btn"
        color="#155bd4"
        custom-style="{{ loginBtn.btnStyle }}"
        block
        disabled="{{loginBtn.wxDisabled}}"
        bindgetphonenumber="wxLogin"
      >
        {{loginBtn.wxText}}
      </van-button>
      <van-button
        wx:else
        class="button"
        custom-class="wx-login-btn"
        color="#155bd4"
        custom-style="{{ loginBtn.btnStyle }}"
        block
        disabled="{{loginBtn.wxDisabled}}"
        bind:tap="validateForm"
      >
        {{loginBtn.wxText}}
      </van-button>
    </block>


    <van-dialog id="van-dialog" />
  </view>

  <inject-protocol noAutoAuth />
  <behavior-verify id="behavior-verify" autoInit />
</view>
