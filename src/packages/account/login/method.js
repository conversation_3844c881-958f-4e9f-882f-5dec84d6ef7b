import { platform, emitUserAuthSuccess } from '@youzan/passport-tee-shared';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import { getFromParams } from 'shared/utils/guide-share';

import Services from './services/index';

const app = getApp();

/**
 * @description Page构造函数里的方法，独立出单独的文件
 */
export default {
  getProtocol() {
    if (!this.$_protocolPs) {
      // 异步分包
      this.$_protocolPs = import('@youzan/passport-protocol')
        .then(({ InvokeProtocol }) => new InvokeProtocol())
        .catch((e) => {
          this.$_protocolPs = null; // 失败后下次重试
          console.error('Resolve passport-protocol failed', e);
          throw e;
        });
    }
    return this.$_protocolPs;
  },
  detachedProtocol() {
    this.getProtocol().then((protocol) => protocol.off());
  },
  watchProtocolAgree() {
    this.getProtocol().then((protocol) => {
      protocol.on('agree', () => {
        const protocolField = this.selectComponent('#protocolField');
        if (protocolField) {
          // 调用子组件的方法
          protocolField.handleAgreementChange({ detail: true });
        }
      });
      protocol.init();
    });
  },

  /**
   * 基础信息校验
   */
  validateBase() {
    return this._checkProtocol();
  },

  /**
   * @description 账号存在提醒后，点确认登录触发
   */
  confirmLogin(option = {}) {
    const mobile = option.mobile || this.data.formData.mobile;
    const countryCode = option.countryCode || this.data.formData.countryCode;
    const { logined } = this.data.redirect;

    const redirectUrl = `/packages/account/to-bind/index?mobile=${mobile}&countryCode=${encodeURIComponent(
      countryCode
    )}&redirectUrl=${encodeURIComponent(logined)}`;

    return wx.redirectTo({
      url: redirectUrl,
    });
  },

  /**
   * @description 手机或微信已和其它账号绑定时，需要唤起弹层提示是否继续登录
   * @param {*} wxMobile
   * @param {*} auth 是否为授权唤起的弹层对话
   */
  configDialog(auth = false, option = {}) {
    let mobile;
    if (auth) {
      if (!option.mobile) {
        return wx.showToast({
          icon: 'none',
          title: '授权获取手机号码失败，请重启小程序重新授权',
        });
      }
      mobile = option.mobile;
    } else {
      if (!this.data.formData.mobile) {
        return wx.showToast({
          icon: 'none',
          title: '请正确填写手机号码',
        });
      }
      mobile = this.data.formData.mobile;
    }

    const blurredMobile = []; // 手机号码脱敏
    blurredMobile.push(mobile.substring(0, 3));
    blurredMobile.push('****');
    blurredMobile.push(mobile.substring(7));

    const message = `手机号${blurredMobile.join('')}已与其他微信账号绑定`;

    Dialog.confirm({
      message,
      confirmButtonText: '继续登录',
      cancelButtonText: '换个手机号',
    })
      .then(() => {
        this.confirmLogin(option);
      })
      .catch(() => {
        // TODO Cancel hook
      });
  },
  /**
   * @description sms登录
   */
  login() {
    const { disabled } = this.data.loginBtn;
    const { mobile, captcha, countryCode } = this.data.formData;
    if (disabled) {
      return;
    }
    if (!this._checkMobile(mobile)) {
      return;
    }
    if (!this._checkCaptcha(captcha)) {
      return wx.showToast({
        icon: 'none',
        title: '请正确输入收到的六位验证码',
      });
    }

    const isValid = this.validateBase();
    if (!isValid) {
      return;
    }

    const data = {
      countryCode,
      mobile,
      captcha,
    };

    this._beforeLogin();
    Services.loginBySms(data)
      .then(() => this.handleLoginSuccess())
      .catch((error) => {
        this._loginFail();
        if (error.code === 135200018 || error.code === 135200019) {
          return this.configDialog();
        }
        wx.showToast({
          icon: 'none',
          title: error.msg || '服务请求出错，请稍后再试',
        });
      });
  },

  validateForm() {
    if (this.data.loginBtn.wxDisabled) {
      return false;
    }
    return this.validateBase();
  },

  wxLogin(e) {
    platform
      .nativeMobileCallback(e)
      .then(({ detail }) => {
        if (!this.validateForm()) {
          return;
        }

        this.beginWxLogin();

        this.loginLoading();
        const params = {
          code: detail.code,
          encryptedData: detail.encryptedData,
          iv: detail.iv,
          appId: app.getAppId(),
          sessionId: app.getSessionId(),
          bizDataMap: {
            from_params: getFromParams(),
          },
        };

        Services.loginByWx(params)
          .then(() => this.handleLoginSuccess('wxLogin'))
          .catch((error) => {
            this._loginFail(error, 'wxLogin');

            if (error.code === -9999) {
              const errData = (error.res && error.res.data) || {};
              if (
                (errData.code === 135200018 || errData.code === 135200019) &&
                errData.mobile
              ) {
                return this.configDialog(true, {
                  mobile: errData.mobile,
                  countryCode: errData.countryCode,
                });
              }
            }

            let title =
              error.msg ||
              error.message ||
              error.errMsg ||
              '服务请求出错，请稍后再试';

            if (error.code === 135000049) {
              title = '微信授权失败，请重启小程序重新授权';
            }

            wx.showToast({
              icon: 'none',
              title,
            });
          });
      })
      .catch(({ disagree }) => {
        // 非拒绝则为其他异常
        if (!disagree) {
          wx.showToast({
            icon: 'none',
            title: '微信授权失败',
          });
        }

        this.resetWxLogin();
      });
  },

  handleLoginSuccess(loginType = '') {
    return app.login().then(() => {
      emitUserAuthSuccess({
        authTypeList: ['mobile'],
        authPopTypeList: ['mobile'],
      });

      this._loginSuccess(loginType);

      // 不 return，即不在这里处理结果 catch
      this.loginRedirect();
    });
  },

  beginWxLogin() {
    this.setYZData({
      'loginBtn.wxDisabled': true,
    });
  },
  resetWxLogin() {
    this.setYZData({
      'loginBtn.wxDisabled': false,
    });
  },
  // 发送验证码
  fetchCaptcha() {
    const { captcha, formData } = this.data;
    if (captcha.countdown !== 60) {
      return;
    }
    if (!this._checkMobile(formData.mobile)) {
      return;
    }

    if (!this._checkProtocol()) {
      return;
    }

    import('@youzan/behavior-verify/src/miniapp/weapp/main').then(
      ({ default: behaviorVerify }) => {
        behaviorVerify({
          // 短信验证码登录发送短信
          bizType: 7,
          onSuccess: (ticket) => {
            const sendTimes = captcha.times;
            // 发送
            Services.fetchCode(
              { mobile: formData.mobile, ticket },
              sendTimes,
              // Success
              () => {
                this._countDownForCaptchaCode();
                this.setYZData({
                  'captcha.btnStyle': 'countdown',
                  'captcha.textStyle': 'acc-code__btn--disabled',
                  'captcha.times': sendTimes,
                });
              },
              // Fail
              (error) => {
                const title = error.msg || '服务请求失败，请稍后再试';
                wx.showToast({
                  icon: 'none',
                  title,
                });
              }
            );
          },
        });
      }
    );
  },
  // 绑定手机号码输入状态
  bindMobileInput(e) {
    this.setYZData({
      'formData.mobile': e.detail,
      'loginBtn.disabled': !(e.detail && this.data.formData.captcha), // 按钮状态
    });
  },
  // 绑定验证码输入状态
  bindCaptchaInput(e) {
    this.setYZData({
      'formData.captcha': e.detail,
      'loginBtn.disabled': !(e.detail && this.data.formData.mobile), // 按钮状态
    });
  },
  _checkMobile(value) {
    if (!value || value.length !== 11) {
      wx.showToast({
        icon: 'none',
        title: '请输入正确的手机号',
      });

      return false;
    }
    return true;
  },
  _checkCaptcha(value) {
    if (!value || value.length !== 6) {
      return false;
    }
    return true;
  },
  /** 校验-协议 */
  _checkProtocol() {
    const { isProtocolAgreed } = this.data.formData;

    if (isProtocolAgreed) {
      return true;
    }

    wx.showToast({
      icon: 'none',
      title: '请阅读并勾选协议',
    });

    return false;
  },
  /**
   * 验证码输入倒计时读秒
   */
  _countDownForCaptchaCode() {
    let { countdown } = this.data.captcha;
    if (countdown === 0) {
      this.setYZData({
        'captcha.countdown': 60,
        'captcha.text': '重新发送',
        'captcha.btnStyle': '',
        'captcha.textStyle': 'acc-code__btn--enabled',
      });
      return;
    }
    countdown--;

    this.setYZData({
      'captcha.countdown': countdown,
      'captcha.text': '已发送(' + countdown + 's)',
    });

    this.data.captcha.timer = setTimeout(() => {
      this._countDownForCaptchaCode();
    }, 1000);
  },
  /**
   * @description 触发登录前行为
   */
  _beforeLogin() {
    this.loginLoading();
    this.setYZData({
      'loginBtn.disabled': true,
    });
  },
  /**
   * @description 触发登录后行为
   */
  _afterLogin() {},
  /**
   * @description 登录成功后触发的行为
   */
  _loginSuccess(type = null) {
    wx.hideLoading();
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      mask: false,
    });
    type === 'wxLogin' && this.resetWxLogin();
  },
  /**
   * @description 登录失败触发行为
   */
  _loginFail(error, type = null) {
    wx.hideLoading();

    const { mobile, captcha, countryCode, wxMobile } = this.data.formData;
    const loginBtnDisabled = !(mobile && captcha);
    let resetCountryCode = countryCode;
    let restWxMobile = wxMobile;
    if (error) {
      const { res } = error;
      // 后端解密回来的手机号码和国家代码(中国返回86)
      if (res && res.data && res.data.countryCode) {
        resetCountryCode = res.data.countryCode;
        restWxMobile = res.data.mobile;
      }
    }
    this.setYZData({
      'loginBtn.disabled': loginBtnDisabled,
      'formData.countryCode': resetCountryCode,
      'formData.wxMobile': restWxMobile,
    });
    type === 'wxLogin' && this.resetWxLogin();
  },
  handleAgreementChange(val) {
    this.setYZData({
      'formData.isProtocolAgreed': val.detail,
    });
  },

  loginLoading(title = '正在登录...') {
    wx.showLoading({
      title,
      mask: true,
    });
  },
};
