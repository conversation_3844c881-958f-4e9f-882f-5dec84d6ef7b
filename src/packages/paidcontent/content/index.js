import WscPage from 'pages/common/wsc-page/index';
import { getAddLogUrl } from '../utils';

const app = getApp();

WscPage({
  onLoad(options) {
    const kdtId = options.kdt_id || options.kdtId || app.getKdtId();

    const alias = options.alias || '';
    // 直接重定向走
    let redirectUrl = `https://h5.youzan.com/wscvis/knowledge/index?kdt_id=${kdtId}&page=contentshow&alias=${alias}`;
    redirectUrl = getAddLogUrl(redirectUrl, options);
    wx.redirectTo({
      url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(redirectUrl)}`
    });
  }
});

