## 知识付费文档

## 知识付费通用组件

## 目录结构

1. 页面自用的组件请先放在页面下的 components 中，如果别的页面有用到，再根据通用性向上移

### Map

src/components
└── paidcontent
    ├── cap-column-item
    ├── cap-content-item
    ├── cap-live-item
    ├── cap-member-item
    └── item

src/packages/paidcontent
├── behaviors
├── column
│   ├── components
│   │   ├── content-item
│   │   └── live-item
│   └── detail
├── components
│   ├── audio-player
│   ├── comment
│   ├── countdown
│   ├── entry
│   ├── gift
│   │   ├── gift-info-card
│   │   └── rank-list-item
│   ├── groupon
│   ├── home-btn
│   ├── invite-friend
│   │   ├── giving-friend
│   │   ├── invite-card
│   │   ├── pay-for-friend
│   │   ├── pay-for-friend-sku
│   │   └── winning-rank
│   ├── paidcontent-popup
│   ├── price
│   ├── promotion-banner
│   ├── service-btn
│   ├── userinfo
│   └── waterfall
├── content
│   └── components
│       ├── audio-detail
│       ├── buy-button
│       ├── comment-list
│       ├── content-detail
│       ├── text-detail
│       └── video-detail
├── gift
├── groupon
│   ├── components
│   │   └── share-card
│   └── templates
│       ├── avatar-list
│       ├── button
│       └── intro
├── list
│   └── components
│       ├── column-item
│       ├── content-item
│       ├── list
│       ├── live-item
│       └── rights-item
├── live
├── live-room
│   └── components
│       ├── audioBlock
│       ├── bubbleItem
│       ├── countDown
│       ├── discussPanel
│       ├── floatArea
│       ├── inputBar
│       ├── questionPanel
│       └── voiceRecorder
├── pay
│   └── components
│       ├── column-item
│       ├── content-item
│       └── live-item
├── rights
│   ├── components
│   │   ├── column-item
│   │   └── content-item
│   └── detail
├── support
│   ├── component
│   │   └── invite-card
│   └── template
│       ├── button
│       └── intro-pop
├── write-comment
├── utils.js
├── vuefy.js
├── constants.js
└── 坑.md
