import WscPage from '../../../pages/common/wsc-page/index';
import { getAddLogUrl } from '../utils';

const app = getApp();

WscPage({
  onLoad(query) {
    const alias = query.alias || '';
    const kdtId = query.kdt_id || query.kdtId || app.getKdtId();
    // 直接重定向走
    let redirectUrl = `https://h5.youzan.com/wscvis/knowledge/index?kdt_id=${kdtId}&page=columnshow&alias=${alias}`;
    redirectUrl = getAddLogUrl(redirectUrl, query);
    wx.redirectTo({
      url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(redirectUrl)}`
    });
  }
});
