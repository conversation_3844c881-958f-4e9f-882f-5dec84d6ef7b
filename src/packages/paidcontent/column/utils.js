import getApp from 'shared/utils/get-safe-app';

export function createFormId(data, success, fail) {
  const app = getApp();
  const { chainStoreInfo = {} } = app.getShopInfoSync();
  const { isMultiOnlineShop } = chainStoreInfo;

  getApp().carmen({
    api: 'wsc.weapp.formid/1.0.0/add',
    data: {
      ...data,
      kdt_id: isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId()
    },
    method: 'GET',
    success: res => {
      success && success(res);
    },
    fail: res => {
      fail && fail(res.msg, res);
    }
  });
}
