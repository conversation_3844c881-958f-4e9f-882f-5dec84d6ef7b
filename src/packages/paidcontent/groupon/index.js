import WscPage from 'pages/common/wsc-page';
import each from '@youzan/weapp-utils/lib/each';
import money from '@youzan/weapp-utils/lib/money';
import CountDown from 'utils/countdown';
import navigate from '@/helpers/navigate';
import logv3 from 'utils/log/logv3';

import {
  CONTENT_MAP,
  CONTENT_TYPE,
  CONTENT_MEDIA,
  CONTENT_TEXT,
  MEDIA_ICON,
  PROMOTION_STATUS,
  USER_JOIN_PROMOTION_STATUS,
  GROUP_STATUS,
  /* USER_JOIN_GROUP_STATUS, */
  MORE_AVATAR_URL,
  PLACEHOLDER_AVATAR_URL,
  DEFAULT_AVATAR_URL,
  GROUP_ICON
} from './config';

const app = getApp();
const p = 'packages';
let requestNum = 0;

WscPage({
  data: {
    // 页面数据
    showCard: false,
    showCreateCardTip: false,
    isLoading: true,

    // 店铺、订单信息
    kdtId: '',
    orderNo: '',

    // 活动id
    promotionId: '',
    promotionType: '',

    // 商品信息
    isColumn: null,
    alias: '',
    price: '',
    priceStr: '',
    originPrice: '',
    originPriceStr: '',
    title: '',
    summary: '',
    goodsThumbUrl: '',
    goodsIconUrl: '',
    subscriptionsCount: '',
    contentType: '',
    contentTypeStr: '',
    videoDuration: '',
    isPaid: false,

    // 拼团信息
    // 拼团类型，0->普通团,1->老带新拼团
    groupType: 0,
    groupAlias: '',
    groupStatus: '',
    userGroupStatus: '',
    promotionStatus: '',
    userPromotionStatus: '',
    // 用户参加的其他拼团的对应活动alais
    userJoinedGroupAlias: '',
    fromGroupId: 0,
    discountStr: '',
    discountRate: '',
    joinNum: '',
    conditionNum: '',
    remainJoinNum: '',
    isHead: false,
    isMockGroupon: false,
    headerPrice: 0,
    // 用户身份，0->老人，1->新人
    userIdentity: 0,
    avatars: [],
    buttons: [],

    // 团状态图标
    activityIcon: '',
    // 倒计时信息
    countdown: '',
    remainTime: '',

    // 分享卡片信息
    shareCardPath: '',
    shareCardSubTitle: '',
    shareCardPriceStr: '',
    shareCardOriginPriceStr: '',
    shareCardJoinGroupStr: '',

    requestNum: 0,
    activity_type: '',
  },

  onLoad(options) {
    const { alias: groupAlias = '', orderNo = '', goodId = 0, activity_type} = options;
    this.setYZData({
      groupAlias,
      orderNo,
      activity_type 
    });
    this.fetchGoodSimpleInfo(goodId)
      .then(() => {
        this.fetchGrouponData();
      });
  },

  onShareAppMessage() {
    const {
      title, author, goodsThumbUrl, groupAlias, price, originPrice
    } = this.data;
    const titleStr = (author && title) ? `【${author}:${title}】` : `【${author || title}】`;
    const savePercent = ((originPrice - price) / originPrice).toFixed(2) * 100;
    const shareTitle = `优惠${savePercent}%,快来${money(price).toYuan()}元团购${titleStr}`;

    return logv3.processShareData({
      title: shareTitle,
      imageUrl: goodsThumbUrl,
      path: `${p}/paidcontent/groupon/index?alias=${groupAlias}`
    });
  },

  onGetUserInfo(res) {
    this.setYZData({
      isNotValid: res.detail && res.detail.isNotValid
    });

    if (res && res.detail && res.detail.userInfo) {
      const kdtId = getApp().getKdtId();
      this.setYZData({
        userInfo: res.detail.userInfo,
        kdtId
      });
    }
  },

  onButtonClick(evt) {
    const { flag, url } = evt.target.dataset;
    const {
      groupType, userIdentity, userPromotionStatus, userGroupStatus, isPaid
    } = this.data;
    if (flag === 'link') {
      // 如果为老带新团，新客参团，老客开新团，未参加当前活动且未参加促销
      if (!isPaid && groupType === 1 && userIdentity === 0 && userGroupStatus === 0 && userPromotionStatus === 0) {
        wx.showModal({
          content: '你是我们的老客户了，可以开个新团，立享优惠哦',
          cancelText: '知道了',
          confirmText: '开新团',
          success: (res) => {
            if (res.confirm) {
              wx.redirectTo({
                url
              });
            }
          }
        });
      } else {
        wx.redirectTo({
          url
        });
      }
    } else if (flag === 'share') {
      this.onShareAppMessage();
    } else if (flag === 'goHome') {
      navigate.switchTab({
        url: `/${p}/home/<USER>/index`
      });
    }
  },

  onShareCardClick() {
    this.setYZData({
      showCard: true
    });
  },

  initCountdown() {
    const { remainTime } = this.data;
    if (remainTime > 0) {
      new CountDown(remainTime, {
        onChange: (timeData, timeDataStr) => {
          const { hour, minute, second } = timeDataStr;
          const countdown = `${hour}:${minute}:${second}`;
          this.setYZData({
            countdown
          });
        }
      });
    }
  },

  // 查询商品的基本信息，如果为线下课，重定向至线下课的拼团页面；如果是知识付费，留在当前页面
  fetchGoodSimpleInfo(goodId) {
    const { orderNo, activity_type } = this.data;
    return new Promise((resolve) => {
      if (!goodId) {
        resolve();
      } else {
        wx.showLoading({
          title: '数据加载中...'
        });
        app.request({
          path: '/wscvis/findSimpleCourseList.json',
          data: {
            goodsIds: +goodId,
          }
        })
          .then((res = []) => {
            if (res.length > 0) {
              res.forEach(item => {
                if (+item.owlType === 10) {
                  const courseGrouponUrl = `/${p}/edu/webview/index?targetUrl=${encodeURIComponent(`https://h5.youzan.com/wscvis/edu/groupon-detail?order_no=${orderNo}&alias=${item.alias}&kdt_id=${getApp().getKdtId()}&activity_type=${activity_type}`)}`;
                  wx.redirectTo({
                    url: courseGrouponUrl,
                  });
                }
              });
            }
          })
          .then(() => {
            resolve();
          })
          .catch(() => {
            resolve();
          });
      }
    });
  },

  fetchGrouponData() {
    const { groupAlias, orderNo } = this.data;
    wx.showLoading({
      title: '数据加载中...'
    });
    app.request({
      path: 'wscvis/knowledge/getGrouponDetail.json',
      data: {
        groupAlias,
        orderNo
      }
    })
      .then((res = {}) => {
        wx.hideLoading();
        // 拼团详情获取中的状态，这个状态下，等1s后重刷
        if (res.waitFlush) {
          if (requestNum < 3) {
            requestNum++;
            wx.showLoading({
              title: '数据加载中...'
            });
            setTimeout(() => {
              this.fetchGrouponData();
            }, 1000);
          } else {
            wx.showModal({
              content: '获取拼团详情失败',
              showCancel: false,
              confirmText: '去逛逛吧',
              success: () => {
                wx.reLaunch({
                  url: `/${p}/home/<USER>/index`
                });
              }
            });
          }
        } else {
          this.parseGrouponData(res);
          // 知识付费商品信息和活动信息没有包在一个接口
          this.fetchGoodsData();
          const {
            groupStatus, userGroupStatus, fromGroupId, headerPrice, orderNo
          } = this.data;
          // 支付回跳时如果是有团长价超团，则有提示信息
          if (orderNo) {
            this.isBeyondGroup(groupStatus, userGroupStatus, fromGroupId, headerPrice);
          }
        }
      })
      .catch(res => {
        wx.showModal({
          title: '获取拼团详情失败',
          content: res.msg,
          showCancel: false,
          confirmText: '去逛逛吧',
          success: () => {
            wx.reLaunch({
              url: `/${p}/home/<USER>/index`
            });
          }
        });
      });
  },

  fetchGrouponData1() {
    const { groupAlias, orderNo } = this.data;

    wx.showLoading({
      title: '数据加载中...'
    });

    app.carmen({
      api: 'youzan.owl.groupon/1.0.0/groupbyalias',
      query: {
        group_alias: groupAlias,
        order_no: orderNo
      },
      success: (res) => {
        this.parseGrouponData(res);
        // 知识付费商品信息和活动信息没有包在一个接口
        this.fetchGoodsData();
        const {
          groupStatus, userGroupStatus, fromGroupId, headerPrice, orderNo
        } = this.data;
        // 支付回跳时如果是有团长价超团，则有提示信息
        if (orderNo) {
          this.isBeyondGroup(groupStatus, userGroupStatus, fromGroupId, headerPrice);
        }
      },
      fail: (res) => {
        // 50800 是拼团详情获取中的状态
        // 这个状态下，等1s后重刷。最多请求2次
        const isWaiting = res.code === 50800;
        let { requestNum } = this.data;
        console.log('requestNum', requestNum);
        requestNum += 1;
        this.setYZData({
          requestNum
        });
        if (isWaiting && requestNum < 3) {
          wx.showLoading({
            title: '数据加载中...'
          });
          setTimeout(() => {
            this.fetchGrouponData();
          }, 1000);
          return;
        }

        wx.hideLoading();
        wx.showModal({
          title: '获取拼团详情失败',
          content: res.msg,
          showCancel: false,
          confirmText: '去逛逛吧',
          success: () => {
            wx.reLaunch({
              url: `/${p}/home/<USER>/index`
            });
          }
        });
      }
    });
  },

  fetchGoodsData() {
    const { alias, kdtId, contentTypeStr } = this.data;
    const requestUrl = `youzan.owl.${contentTypeStr}.detail/1.0.0/get`;

    app.carmen({
      api: requestUrl,
      query: {
        kdt_id: kdtId,
        alias
      },
      config: {
        skipKdtId: true
      },
      success: (res) => {
        this.parseGoodsData(res);
        this.parseShareCardData();
        this.setYZData({
          isLoading: false
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 有团长价时判断是否超团
  isBeyondGroup(groupStatus, userGroupStatus, fromGroupId, headerPrice) {
    if (groupStatus === 1 && headerPrice > 0 && userGroupStatus === 0 && fromGroupId > 0) {
      wx.showModal({
        title: '',
        content: '抱歉，此团已满，订单关闭并且自动退款，再去新开个团吧',
        showCancel: false
      });
    }
  },

  parseGrouponData(data) {
    const {
      // id: promotionId,
      targetAlias: alias,
      userGroupStatus: userGroup,
      group,
      groupon,
      contentType,
      // promotionType,
      joinGroupRecords,
      userGrouponStatus,
      userIdentity
    } = data;
    const {
      status: groupStatus, promotionId, joinNum, remainJoinNum, remainTime, groupAlias
    } = group;
    const {
      status: promotionStatus, promotionType, kdtId, price, conditionNum, isMockGroupon, headerPrice, groupType
    } = groupon;
    const { status: userGroupStatus, fromGroupId, isHead } = userGroup;
    const { status: userPromotionStatus, groupAlias: userJoinedGroupAlias } = userGrouponStatus;
    const avatars = this.formatJoinMembers(joinGroupRecords, conditionNum, remainJoinNum, userPromotionStatus, isMockGroupon);
    const isColumn = contentType === CONTENT_TYPE.COLUMN;

    this.setYZData({
      contentTypeStr: CONTENT_MAP[contentType],
      priceStr: money(price).toYuan(),
      alias,
      price,
      kdtId,
      avatars,
      isColumn,
      promotionId,
      promotionType,
      contentType,
      conditionNum,
      joinNum,
      remainJoinNum,
      remainTime,
      fromGroupId,
      groupStatus,
      promotionStatus,
      userGroupStatus,
      userPromotionStatus,
      userJoinedGroupAlias,
      isHead,
      headerPrice,
      groupAlias,
      groupType,
      userIdentity
    });
  },

  parseGoodsData(data) {
    const {
      price: originPrice,
      title,
      author,
      summary,
      video,
      picture,
      mediaType,
      contentsCount,
      isSubscription,
      subscriptionsCount
    } = data;
    const { videoDuration, videoCover } = video || {};
    const { cover } = picture;
    const {
      isColumn, price, fromGroupId, isHead, groupStatus, promotionStatus, userGroupStatus, userPromotionStatus
    } = this.data;
    const goodsThumbUrl = cover || videoCover;
    const discount = originPrice - price;
    const discountStr = money(discount).toYuan();
    const discountRate = Math.round((discount * 100) / originPrice);
    const intro = this.formatContentIntro(isColumn, contentsCount, videoDuration);
    const isPaid = isSubscription;
    const {
      showCreateCardTip, buttons, description, activityIcon
    } = this.formatButtonsAndDescription(isSubscription, promotionStatus, userPromotionStatus, groupStatus, userGroupStatus, fromGroupId, isHead, mediaType, isColumn, videoDuration);
    const goodsIconUrl = this.getGoodsIconUrl(isColumn, mediaType);

    this.setYZData({
      originPriceStr: money(originPrice).toYuan(),
      title,
      intro,
      author,
      buttons,
      activityIcon,
      summary,
      description,
      discountStr,
      discountRate,
      originPrice,
      goodsThumbUrl,
      goodsIconUrl,
      videoDuration,
      subscriptionsCount,
      showCreateCardTip,
      isPaid
    });
  },

  parseShareCardData() {
    const {
      groupAlias,
      author,
      intro,
      priceStr,
      originPriceStr,
      conditionNum,
      remainJoinNum,
      discountRate
    } = this.data;

    const shareCardPath = `${p}/paidcontent/groupon/index?alias=${groupAlias}`;
    const shareCardSubTitle = author && intro ? `${author} | ${intro}` : author || intro;
    const shareCardPriceStr = `${conditionNum}人拼团价：¥ ${priceStr}`;
    const shareCardOriginPriceStr = `单买原价：¥ ${originPriceStr}`;
    const shareCardJoinGroupStr = `仅差${remainJoinNum}人，快来凑团，立省${discountRate}%`;

    this.setYZData({
      shareCardPath,
      shareCardSubTitle,
      shareCardPriceStr,
      shareCardOriginPriceStr,
      shareCardJoinGroupStr
    });
  },

  // 参团列表展示处理
  formatJoinMembers(data, total, remainder, userPromotionStatus, isMockGroupon) {
    let avatars = [];
    // const joinedNumber = data.length;
    // let placeholderNum = 0;

    if (total < 10) {
      each(data, (item) => {
        const { avatar, isHead } = item;
        const label = isHead ? '团长' : '';
        avatars.push({
          avatar,
          label
        });
      });
      avatars = avatars.concat(this.pushPlaceholder(remainder, userPromotionStatus, isMockGroupon));
    } else if (remainder > 8) {
      each(data, (item) => {
        const { avatar, isHead } = item;
        const label = isHead ? '团长' : '';
        if (isHead) {
          avatars.push({
            avatar,
            label
          });
        }
      });

      avatars = avatars.concat(this.pushPlaceholder(9, userPromotionStatus, isMockGroupon));
    } else {
      let avatarNum = total - remainder;
      each(data, (item) => {
        if (avatarNum > 0 && avatars.length < 10) {
          const { avatar, isHead } = item;
          const label = isHead ? '团长' : '';
          avatars.push({
            avatar,
            label
          });
          avatarNum--;
        }
      });
      avatars = avatars.concat(this.pushPlaceholder(remainder, userPromotionStatus, isMockGroupon));
    }

    if (total > 10) {
      avatars[1].avatar = MORE_AVATAR_URL;
    }

    return avatars;
  },

  pushPlaceholder(remainder, userPromotionStatus, isMockGroupon) {
    const avatarsTmp = [];
    for (let i = 0; i < remainder; i++) {
      const avatarTmp = {};
      // 虚拟成团
      if (userPromotionStatus === USER_JOIN_PROMOTION_STATUS.SUCCESS && isMockGroupon) {
        avatarTmp.avatar = DEFAULT_AVATAR_URL;
      } else {
        avatarTmp.avatar = PLACEHOLDER_AVATAR_URL;
      }
      avatarTmp.label = '';
      avatarsTmp.push(avatarTmp);
    }

    return avatarsTmp;
  },

  formatContentIntro(isColumn, count, time) {
    if (isColumn) {
      if (count) {
        return `已更新${count}期`;
      }
    } else {
      return time ? this.formatDuration(time) : '';
    }
  },

  // 处理按钮
  formatButtonsAndDescription(isPaid, promotionStatus, userPromotionStatus, groupStatus, userGroupStatus, fromGroupId, isHead, mediaType, isColumn) {
    const {
      alias,
      orderNo,
      groupAlias,
      promotionType,
      promotionId,
      contentTypeStr,
      userJoinedGroupAlias,
      groupType
    } = this.data;
    // 活动是否结束
    let isPromotionEnd = false;
    // 新客一键参团
    let newToJoinGroup = false;
    // 是否要开新团
    let isNeedNewGroup = false;
    // 是否参团
    let isNeedJoinGroup = false;
    // 是否邀请好友参团
    let isNeedInvite = false;
    // 是否参与的团已购买
    let isJoined = false;
    // 用户已参加该活动下的某个团，非当前团
    let isJoinedPromotion = false;
    // 是否展示倒计时
    let showCountdown = false;
    // 是否用户是新团长，上个团已满时的场景
    let showOverTip = false;
    // 是否当前团已满
    let isGroupBeyond = false;
    // 是否展示凑团失败文案
    let isShowFailure = false;
    // 是否显示拼团成功文案
    let isShowSuccess = false;
    // 按钮组
    const buttons = [];
    // 活动及团状态图标
    let activityIcon = '';
    // 展示提示
    let descriptionTip;

    if (isPaid) {
      // 内容已被购买，显示查看内容
      if (userJoinedGroupAlias === groupAlias && groupStatus === GROUP_STATUS.GROUP_SUCCESS) {
        // 拼团成功优先级高，展示团成功图标
        isShowSuccess = true;
        activityIcon = GROUP_ICON.SUCCESS;
      }
      isJoined = true;
    } else {
      // 内容未被购买
      /* eslint-disable */
      if (promotionStatus === PROMOTION_STATUS.ENDED) {
        // 拼团活动已结束，显示进店逛逛，显示活动结束图标
        isPromotionEnd = true;
        activityIcon = GROUP_ICON.END;
      } else if (promotionStatus === PROMOTION_STATUS.STARTED) {
        // 拼团活动进行中
        if (userJoinedGroupAlias === groupAlias) {
          // 用户参加了当前团
          if (groupStatus === GROUP_STATUS.GROUP_SUCCESS) {
            // 当前团成功时，显示拼团成功文案
            isShowSuccess = true;
          } else if (groupStatus === GROUP_STATUS.GROUP_FAILURE) {
            // 当前团失败时，显示拼团失败文案,显示开新团按钮,显示团失效图标
            activityIcon = GROUP_ICON.FAILURE;
            isShowFailure = true;
            isNeedNewGroup = true;
          } else if (groupStatus === GROUP_STATUS.GROUP_WAITING) {
            // 当前团拼团中，显示倒计时，加邀请好友文案，显示邀请好友按钮
            showCountdown = true;
            isNeedInvite = true;
            // 如果是来自于其他团，且为当前团长，展示成为团长提示,仅在支付完成后回跳展示
            if (fromGroupId && isHead && orderNo) {
              showOverTip = true;
            }
          }
        } else if (!!userJoinedGroupAlias && userJoinedGroupAlias !== groupAlias) {
          // 用户参加了活动，但未参加当前团，显示已参加其他团，显示查看团按钮
          isJoinedPromotion = true;
        } else if (!userJoinedGroupAlias) {
          // 用户未参加活动
          if (groupStatus === GROUP_STATUS.GROUP_SUCCESS) {
            // 当前团成功时，显示团已满文案，显示开新团，显示团已满图标
            activityIcon = GROUP_ICON.BEYOND;
            isGroupBeyond = true;
            isNeedNewGroup = true;
          } else if (groupStatus === GROUP_STATUS.GROUP_FAILURE) {
            // 当前团失败时，显示拼团失败文案,显示开新团按钮,显示团失效图标
            activityIcon = GROUP_ICON.FAILURE;
            isShowFailure = true;
            isNeedNewGroup = true;
          } else if (groupStatus === GROUP_STATUS.GROUP_WAITING) {
            // 当前团拼团中，显示倒计时，加邀请好友文案，老带新团显示'新客一键参团'， 普通团显示'我要参团'按钮
            showCountdown = true;
            if (groupType === 1) {
              newToJoinGroup = true;
            } else {
              isNeedJoinGroup = true;
            }
          }
        }
      }
    }

    // 按钮和描述展示处理
    if (isPaid) {
      if (isJoined) {
        buttons.push({
          type: 'link',
          text: this.getViewContentText(isColumn, mediaType),
          url: `/${p}/paidcontent/${contentTypeStr}/index?alias=${alias}`
        });
      }
    } else if (isPromotionEnd) {
      buttons.push({
        type: 'goHome',
        text: '进店逛逛',
        url: `/${p}/home/<USER>/index`
      });
    } else if (isNeedNewGroup) {
      buttons.push({
        type: 'link',
        text: '开个新团',
        url: this.getViewContentUrl(contentTypeStr, alias, '', promotionType, promotionId)
      });
    } else if (isNeedJoinGroup) {
      buttons.push({
        type: 'link',
        text: '我要参团',
        url: this.getViewContentUrl(contentTypeStr, alias, groupAlias, promotionType, promotionId)
      });
    } else if (newToJoinGroup) {
      buttons.push({
        type: 'link',
        text: '新客一键参团',
        url: this.getViewContentUrl(contentTypeStr, alias, groupAlias, promotionType, promotionId)
      });
    } else if (isNeedInvite) {
      buttons.push({
        type: 'share',
        text: '邀请好友参团'
      });
    } else if (isJoinedPromotion) {
      buttons.push({
        type: 'link',
        text: '查看我的团',
        url: this.getGrouponPath(userJoinedGroupAlias, orderNo)
      });
    }

    // 展示处理
    if (showCountdown) {
      this.initCountdown();
    } else {
      descriptionTip = this.getDescriptionTip(isGroupBeyond, isShowFailure, isJoined, isShowSuccess, isJoinedPromotion);
    }

    return {
      showCreateCardTip: isNeedInvite,
      description: {
        showOverTip,
        showCountdown,
        isPromotionEnd,
        descriptionTip
      },
      activityIcon,
      buttons
    };
  },

  formatDuration(time) {
    const leftPad = num => (num > 9 ? num : `0${num}`);
    const hour = leftPad(Math.floor(time / 3600));
    const minute = leftPad(Math.floor(time / 60) % 60);
    const second = leftPad(time % 60);
    return +hour ? `${hour}:${minute}:${second}` : `${minute}:${second}`;
  },

  getViewContentText(isColumn, mediaType) {
    return isColumn ? '查看专栏' : CONTENT_TEXT[mediaType];
  },

  getViewContentUrl(type, alias, groupAlias, promotionType, promotionId) {
    const { groupType, userIdentity } = this.data;
    // 老带新团，老人开新团
    if (groupAlias && groupType === 1 && userIdentity === 0) {
      return `/${p}/paidcontent/pay/index?type=${type}&alias=${alias}&groupAlias=&promotionType=${promotionType}&promotionId=${promotionId}`;
    }
    return `/${p}/paidcontent/pay/index?type=${type}&alias=${alias}&groupAlias=${groupAlias}&promotionType=${promotionType}&promotionId=${promotionId}`;
  },

  getDescriptionTip(isGroupBeyond, isGroupFailed, isJoinedSuccess, isShowSuccess, isJoinedGroup) {
    if (isGroupBeyond) {
      return '此团已满，去开个新团吧';
    } else if (isGroupFailed) {
      return '此团已失效，去开个新团吧';
    } else if (isShowSuccess) {
      return '恭喜你，拼团成功啦';
    } else if (isJoinedSuccess && !isShowSuccess) {
      return '你已拥有该付费知识，不用再买了哦';
    } else if (isJoinedGroup) {
      return '你已有1个进行中的团啦，不能再买了哦';
    }
  },

  getGoodsIconUrl(isColumn, mediaType) {
    if (isColumn) {
      return MEDIA_ICON.COLUMN;
    } else if (mediaType === CONTENT_MEDIA.IMAGE_TEXT) {
      return MEDIA_ICON.IMAGE;
    } else if (mediaType === CONTENT_MEDIA.AUDIO) {
      return MEDIA_ICON.AUDIO;
    } else if (mediaType === CONTENT_MEDIA.VIDEO) {
      return MEDIA_ICON.VIDEO;
    }
  },

  getGrouponPath(alias, orderNo) {
    return `/${p}/paidcontent/groupon/index?alias=${alias}&orderNo=${orderNo}`;
  },

  goToDetail() {
    const type = this.data.isColumn ? 'column' : 'content';
    const url = `/${p}/paidcontent/${type}/index?alias=${this.data.alias}`;
    wx.navigateTo({
      url
    });
  }
});

