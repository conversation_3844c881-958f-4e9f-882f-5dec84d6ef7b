//  专栏、内容map
const CONTENT_MAP = {
  1: 'column',
  2: 'content'
};

const CONTENT_TYPE = {
  COLUMN: 1,
  CONTENT: 2
};

// 内容类型
const CONTENT_MEDIA = {
  IMAGE_TEXT: 1,
  AUDIO: 2,
  VIDEO: 3
};

// 内容文案
const CONTENT_TEXT = {
  1: '查看图文',
  2: '查看音频',
  3: '查看视频'
};

// 拼团活动状态
const PROMOTION_STATUS = {
  STARTED: 1,
  ENDED: 2
};

// 用户参与的该团状态
const GROUP_STATUS = {
  GROUP_WAITING: 0,
  GROUP_SUCCESS: 1,
  GROUP_FAILURE: 2
};

// 用户参团状态
const USER_JOIN_GROUP_STATUS = {
  NOT_JOIN: 0,
  JOINED: 1,
  SUCCESS: 2
};

const USER_JOIN_PROMOTION_STATUS = {
  NOT_JOIN: 0,
  JOINED: 1,
  SUCCESS: 2
};

// 参与省略头像
const MORE_AVATAR_URL = 'https://img.yzcdn.cn/public_files/2017/09/29/7ad1015d2279c58b2d11dfd125a10b98.png';

// 待参与默认头像
const PLACEHOLDER_AVATAR_URL = 'https://img.yzcdn.cn/public_files/2017/09/20/4135b27618a9fb6f520c70dd6a217ec8.png';

// 凑团成功时虚拟头像
const DEFAULT_AVATAR_URL = 'https://img.yzcdn.cn/public_files/2018/04/08/avatar.png';

// 商品类型图片
const MEDIA_ICON = {
  IMAGE: 'https://img.yzcdn.cn/public_files/2018/04/08/icon_image.png',
  VIDEO: 'https://img.yzcdn.cn/public_files/2018/04/08/icon_video.png',
  COLUMN: 'https://img.yzcdn.cn/public_files/2018/04/08/icon_column.png',
  LIVE: 'https://img.yzcdn.cn/public_files/2018/04/08/icon_live.png',
  AUDIO: 'https://img.yzcdn.cn/public_files/2018/04/08/icon_audio.png',
};

// 活动状态及团状态图标
const GROUP_ICON = {
  END: 'https://img.yzcdn.cn/public_files/2018/04/08/end.png',
  FAILURE: 'https://img.yzcdn.cn/public_files/2018/04/08/failure.png',
  SUCCESS: 'https://img.yzcdn.cn/public_files/2018/04/08/success.png',
  BEYOND: 'https://img.yzcdn.cn/public_files/2018/04/08/over.png'
};

export {
  CONTENT_MAP,
  CONTENT_TYPE,
  CONTENT_MEDIA,
  CONTENT_TEXT,
  PROMOTION_STATUS,
  USER_JOIN_PROMOTION_STATUS,
  GROUP_STATUS,
  USER_JOIN_GROUP_STATUS,
  MORE_AVATAR_URL,
  PLACEHOLDER_AVATAR_URL,
  DEFAULT_AVATAR_URL,
  MEDIA_ICON,
  GROUP_ICON
};
