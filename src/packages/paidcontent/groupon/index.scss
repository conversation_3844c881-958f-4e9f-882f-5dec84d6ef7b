@import './templates/avatar-list/index.scss';
@import './templates/button/index.scss';
@import './templates/intro/index.scss';
$white: #fff;
$red: #f44;
$gray: #999;

.page-groupon {
  box-sizing: border-box;
  padding: 30rpx 40rpx 16rpx;
  min-height: 100vh;

  /* 顶部 */
  &__top {
    position: relative;
    width: 100%;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, .09);
    overflow: hidden;

    /* banner图 */
    &-banner {
      position: relative;

      &-image {
        width: 100%;
        height: 320rpx;
        vertical-align: bottom;
      }

      &-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 320rpx;
        background-image: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, .7) 100%);
        z-index: 1;
      }

      &-icon {
        position: absolute;
        top: 20rpx;
        right: 30rpx;
        width: 40rpx;
        height: 40rpx;
        z-index: 2;
      }

      &-logo {
        position: absolute;
        top: 20rpx;
        left: 30rpx;
        padding: 0 10rpx;
        background-color: #f44;
        font-size: 20rpx;
        color: #fff;
        border-radius: 5rpx;
      }
    }

    /* 商品详情 */
    &-detail {
      position: absolute;
      left: 30rpx;
      bottom: 22rpx;
      z-index: 2;
      color: $white;
    }

    &-title {
      font-size: 36rpx;
      font-weight: 700;
    }

    &-intro {
      margin-top: 10rpx;
      font-size: 24rpx;
      line-height: 24rpx;
      overflow: hidden;
      opacity: .8;

      > text:not(:first-child) {
        margin-left: 8rpx;
        padding-left: 8rpx;
        border-left: 1px solid $white;
      }
    }

    &-content {
      padding: 28rpx 30rpx 26rpx;

      .van-tag {
        background-color: #ffecec !important;
        color: $red !important;
        padding: 0 3px !important;
        vertical-align: 15%;
      }
    }
  }

  &__price {
    font-size: 32rpx;
    color: $red;
  }

  &__old-price {
    font-size: 24rpx;
    color: $gray;
  }

  &__activity-icon {
    position: absolute;
    width: 80px;
    height: 80px;
    right: -10px;
    bottom: -10px;
  }

  &__guide {
    margin: 40rpx 0;
    font-size: 24rpx;
    color: $gray;
    text-align: center;

    &-arrow {
      vertical-align: middle;
    }
  }

  &__create-card {
    margin-top: 30rpx;
    font-size: 24rpx;
    color: #3283fa;
    text-align: center;
  }
}