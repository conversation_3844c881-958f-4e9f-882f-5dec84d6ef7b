import WscComponent from 'pages/common/wsc-component';
import authorize from '@/helpers/authorize';
import { fetchUltraCode } from '@/helpers/fetch';
import { getUrlAndParams } from '../../../utils';

const app = getApp();

WscComponent({
  properties: {
    show: {
      type: Boolean,
      value: false,
      observer: 'handleShowChange'
    },
    canvasId: {
      type: String,
      value: 'share-poster'
    },
    path: String,
    title: String,
    subTitle: String,
    description: String,
    imageUrl: String,
    iconUrl: String,
    priceStr: String,
    originPriceStr: String,
    joinGroupStr: String
  },

  data: {
    showModal: false,
    tempImg: '',
    tempShareCard: '',
    weappCode: '',
    iconTmp: ''
  },

  methods: {
    handleShowChange(newVal) {
      if (!newVal) return;
      this.setData(
        {
          showModal: true
        },
        this.draw
      );
    },

    draw() {
      const { tempShareCard } = this.data;
      if (tempShareCard) return;
      wx.showLoading({
        title: '正在生成'
      });
      this.loadGoodsImage()
        .then(this.drawIcon.bind(this))
        .then(this.drawQrcode.bind(this))
        .then(this.drawShareImage.bind(this))
        .then(() => {
          this.setData({
            showModal: true
          });
          this.triggerEvent('success');
        })
        .catch((err) => {
          console.log(err);
          wx.showToast({
            title: '生成图片路径失败',
            icon: 'none'
          });
          this.triggerEvent('failed', { err });
        })
        .then(() => {
          this.triggerEvent('finished');
        });
    },

    drawIcon() {
      const { iconUrl, iconTmp } = this.data;
      const self = this;
      if (iconTmp) {
        return Promise.resolve();
      }
      return new Promise((resolve, reject) => {
        loadImage(iconUrl)
          .then((iconTmp) => {
            self.setData(
              {
                iconTmp
              },
              resolve
            );
          })
          .catch(() => {
            reject();
          });
      });
    },

    drawQrcode() {
      const { weappCode, path } = this.data;
      if (weappCode) {
        return Promise.resolve(weappCode);
      }
      if (!path) {
        return Promise.reject();
      }
      const qrParams = getUrlAndParams(path);
      return (
        fetchUltraCode(qrParams.path, qrParams.params)
          // return fetchQRCode(path)
          // .then(loadImage)
          .then((img) => {
            this.setData({
              weappCode: img
            });
            return weappCode;
          })
      );
    },

    drawShareImage() {
      const {
        canvasId,
        title,
        subTitle,
        description,
        tempImg,
        // iconUrl,
        iconTmp,
        priceStr,
        originPriceStr,
        joinGroupStr,
        weappCode
      } = this.data;
      const ctx = wx.createCanvasContext(canvasId, this);
      // 渐变
      const gradient = ctx.createLinearGradient(20, 20, 20, 360);
      // 距离canvas顶部距离
      const initHeight = 430;
      // title只做一行展示
      const _title = title.length > 20 ? title.substr(0, 18) + '...' : title;
      // 绘制底色
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, 670, 850);
      // 绘制商品图
      ctx.drawImage(tempImg, 20, 40, 630, 340);
      // 绘制商品类型图
      ctx.drawImage(iconTmp, 590, 50, 40, 40);
      // 绘制图片蒙层
      gradient.addColorStop(0, 'rgba(0, 0, 0, 0)');
      gradient.addColorStop(1, 'rgba(0, 0, 0, .5)');
      ctx.setFillStyle(gradient);
      // 外部阴影
      ctx.shadowBlur = 10;
      ctx.shadowColor = 'black';
      ctx.fillRect(20, 40, 630, 340);
      ctx.shadowBlur = 0;
      ctx.shadowColor = '';
      // 绘制商品信息
      ctx.setFillStyle('#ffffff');
      ctx.setFontSize(36);
      ctx.fillText(_title, 45, 310);
      // 作者相关信息
      ctx.setFontSize(24);
      ctx.fillText(subTitle, 45, 350);
      // 简介
      ctx.setFillStyle('#666666');
      ctx.setTextAlign('center');
      // 计算简介换行位置,由于measureText的兼容问题，这里直接写死
      if (description.length > 24) {
        ctx.fillText(description.substring(0, 23), 335, initHeight);
        ctx.fillText(description.substring(23, description.length - 1), 335, initHeight + 44);
      } else {
        ctx.fillText(description, 335, initHeight);
      }

      // 成团信息，活动价
      ctx.setFillStyle('#ff4444');
      ctx.setFontSize(32);
      ctx.fillText(priceStr, 335, 560);
      // 原价
      ctx.setFillStyle('#999999');
      ctx.setFontSize(24);
      ctx.fillText(originPriceStr, 335, 612);
      // 底部
      ctx.setFillStyle('#f8f8f8');
      ctx.fillRect(0, 664, 670, 180);
      ctx.setTextAlign('left');
      // 待成团人数，优惠文案
      ctx.setFillStyle('#333333');
      ctx.setFontSize(24);
      ctx.fillText(joinGroupStr, 45, 746);
      // 提示文案
      ctx.setFillStyle('#666666');
      ctx.setFontSize(24);
      ctx.fillText('扫描识别二维码参与', 45, 800);
      // 二维码
      if (weappCode) {
        ctx.drawImage(weappCode, 510, 704, 120, 120);
      }
      ctx.draw(false, this.createTempPath.bind(this));
    },

    createTempPath() {
      const { canvasId } = this.data;
      return new Promise((resolve, reject) => {
        wx.canvasToTempFilePath(
          {
            canvasId,
            destWidth: 670,
            destHeight: 860,
            success: (res) => {
              this.setData({
                tempShareCard: res.tempFilePath
              });
            },
            fail: reject,
            complete: () => {
              wx.hideLoading();
              this.setData({
                show: false
              });
            }
          },
          this
        );
      });
    },

    closeShareCardModal() {
      this.setData({
        showModal: false,
        show: false
      });
    },

    loadGoodsImage() {
      const self = this;
      const { imageUrl, tempImg } = this.data;
      if (tempImg) return Promise.resolve();
      if (!imageUrl) return Promise.reject();
      return new Promise((resolve, reject) => {
        loadImage(imageUrl)
          .then((tempImg) => {
            self.setData(
              {
                tempImg
              },
              resolve
            );
          })
          .catch(() => {
            wx.showToast({
              title: '生成卡片失败',
              icon: 'none'
            });
            reject();
          });
      });
    },

    handleSaveImage() {
      const { tempShareCard } = this.data;
      if (!tempShareCard) return;
      wx.showLoading({
        title: '保存中'
      });
      authorize('scope.writePhotosAlbum')
        .then(saveImage.bind(null, tempShareCard))
        .then(() => {
          wx.hideLoading();
          wx.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000
          });
          this.closeShareCardModal();
          this.triggerEvent('saved');
        })
        .catch(() => {
          wx.showToast({
            title: '保存失败，请检查保存到相册的权限',
            icon: 'none',
            duration: 2000
          });
        });
    }
  }
});

function loadImage(url) {
  return new Promise((resolve, reject) => {
    app.downloadFile({
      url,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: reject
    });
  });
}

function saveImage(tempFilePath) {
  return new Promise((resolve, reject) => {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: resolve,
      fail: reject
    });
  });
}
