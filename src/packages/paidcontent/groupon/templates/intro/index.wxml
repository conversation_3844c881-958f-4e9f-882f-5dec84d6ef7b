<template name="intro">
  <view class="page-groupon__intro">
    <block wx:if="{{ !isPromotionEnd }}">
      <view wx:if="{{ showOverTip }}" class="page-groupon__intro-tip">上个团已满，你已被选为新团长</view>
      <view class="page-groupon__intro-desc">
        <block wx:if="{{ showCountdown }}">
          还差 <text class="page-groupon__intro-join-num">{{ remainJoinNum }}</text> 人，
          {{ userGroupStatus ? "快喊小伙伴" : "快来" }}一起拼团吧
        </block>
        <block wx:else>{{ descriptionTip }}</block>
      </view>
      <view wx:if="{{ showCountdown }}" class="page-groupon__intro-time">
        剩余 <text class="page-groupon__intro-countdown">{{ countdown }}</text> 结束
      </view>
    </block>
    <view wx:else class="page-groupon__intro-desc">活动已结束，去看看其他商品吧</view>
  </view>
</template>