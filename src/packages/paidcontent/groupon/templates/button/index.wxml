<template name="bottom-button">
  <view class="page-groupon__bottom">
    <block wx:for="{{ buttons }}" wx:key="*this">
      <van-button
        wx:if="{{ item.type !== 'link' }}"
        custom-class="page-groupon__bottom-btn share"
        type="danger"

        block="true"
        open-type="{{ item.type === 'share' ? 'share' : '' }}"
        data-flag="{{ item.type }}"
        data-url="{{ item.url }}"
        style="width: 100%"
        bind:click="onButtonClick">
        {{ item.text }}
      </van-button>
      <user-authorize
        wx:else
        authTypeList="{{ ['nicknameAndAvatar'] }}"
        class="page-groupon__bottom-btn"
        bindgetuserinfo="onGetUserInfo"
        catchtap="onButtonClick"
        data-flag="{{ item.type }}"
        data-url="{{ item.url }}">
        <view class="authorize">
          {{ item.text }}
        </view>
      </user-authorize>
    </block>
  </view>
</template>
