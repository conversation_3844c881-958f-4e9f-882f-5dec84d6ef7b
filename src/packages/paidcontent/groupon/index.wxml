<import src="./templates/button/index.wxml" />
<import src="./templates/avatar-list/index.wxml" />
<import src="./templates/intro/index.wxml" />

<view wx:if="{{ !isLoading }}" class="page-groupon">
  <view class="page-groupon__top">
    <!-- 顶部展示 -->
    <view class="page-groupon__top-banner" bindtap="goToDetail">
      <!-- 蒙层 -->
      <view class="page-groupon__top-banner-mask"></view>
      <!-- banner -->
      <image
        src="{{ goodsThumbUrl }}"
        alt="图片"
        mode="aspectFill"
        class="page-groupon__top-banner-image"
      />
      <view wx:if="{{ groupType === 1 }}" class="page-groupon__top-banner-logo">老带新</view>
      <!-- 内容类型icon -->
      <image
        wx:if="{{ goodsIconUrl }}"
        src="{{ goodsIconUrl }}"
        alt="内容标识"
        class="page-groupon__top-banner-icon"
      />
      <!-- 内容介绍 -->
      <view class="page-groupon__top-detail">
        <view class="page-groupon__top-title">{{ title }}</view>
        <view class="page-groupon__top-intro">
          <text wx:if="{{ author }}">{{ author }}</text>
          <text wx:if="{{ intro }}" class="page-groupon__top-intro-detail">{{ intro }}</text>
        </view>
      </view>
    </view>
    <!-- 拼团信息 -->
    <view class="page-groupon__top-content">
      <view class="page-groupon__price">
        {{ conditionNum }}人拼团价：¥ {{ priceStr }}
        <van-tag plain>省{{ discountStr }}元</van-tag>
      </view>
      <view class="page-groupon__old-price">
        单买原价：¥ {{ originPriceStr }}
      </view>
    </view>
    <!-- 活动状态图标 -->
    <image
      wx:if="{{ activityIcon }}"
      src="{{ activityIcon }}"
      class="page-groupon__activity-icon"
    />
  </view>
  <!-- 拼团信息 -->
  <view class="page-groupon__content">
    <!-- 拼团活动信息 -->
    <template is="intro" data="{{ ...description, remainJoinNum, countdown, isHead, userGroupStatus }}" />

    <!-- 参与人员信息 -->
    <template is="avatar-list" data="{{ avatars }}" />

    <navigator
      url="/packages/ump/pintuan/playing-instruction/index?groupType={{ groupType }}"
      class="page-groupon__guide"
      hover-class="none">
      玩法详情
      <van-icon name="arrow" custom-class="page-groupon__guide-arrow"></van-icon>
    </navigator>
  </view>
  <!-- 底部信息 -->
  <template is="bottom-button" data="{{ buttons }}" />
  <view wx:if="{{ showCreateCardTip }}"  class="page-groupon__create-card" bindtap="onShareCardClick">生成邀请卡片</view>
</view>
<!-- canvas卡片 -->
<share-card
  show="{{ showCard }}"
  path="{{ shareCardPath }}"
  title="{{ title }}"
  sub-title="{{ shareCardSubTitle }}"
  description="{{ summary }}"
  image-url="{{ goodsThumbUrl }}"
  icon-url="{{ goodsIconUrl }}"
  price-str="{{ shareCardPriceStr }}"
  origin-price-str="{{ shareCardOriginPriceStr }}"
  join-group-str="{{ shareCardJoinGroupStr }}"
/>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />