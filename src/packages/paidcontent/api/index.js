const app = getApp();

const API = {
  showSubMsgPopup(sceneName) {
    return new Promise((resolve) => {
      const _this = this;
      this.isMpTemplateMsgAvailable()
        .then((res) => {
          if (res && !res.value) {
            return this.getTemplate(sceneName);
          }
        })
        .then((res) => {
          if (res && res.templateIdList && res.templateIdList.length > 0) {
            wx.requestSubscribeMessage({
              tmplIds: res.templateIdList,
              success() {
                _this.pushSuccessCallback(sceneName, res.templateIdList);
                resolve();
              },
              fail() {
                resolve();
              },
            });
          } else {
            resolve();
          }
        })
        .catch((err) => {
          console.error('subMsg-err:', err);
          resolve();
        });
    });
  },

  isMpTemplateMsgAvailable() {
    return app.request({
      path: '/wscvis/edu/push/isMpTemplateMsgAvailable.json',
      method: 'GET',
    });
  },

  getTemplate(scene) {
    return app.request({
      path: '/wscvis/edu/push/getTemplate.json',
      method: 'GET',
      query: {
        scene,
      },
    });
  },

  showSaveMsgConfigPopup(sceneName, scopeType, goodsId, activityId) {
    const _this = this;
    this.isMpTemplateMsgAvailable()
      .then((res) => {
        if (res && !res.value) {
          return this.shouldPullUpMsgDialog(
            sceneName,
            scopeType,
            goodsId,
            activityId
          );
        }
      })
      .then((res) => {
        if (res && res.needPullUp && res.pullUpItems.length > 0) {
          const { pullUpItems } = res;
          const templateIdList = [];
          pullUpItems.forEach((item) => {
            templateIdList.push(item.templateId);
          });
          const choiceSaveParams = [];

          wx.requestSubscribeMessage({
            tmplIds: templateIdList,
            success(res) {
              for (let i = 0; i < pullUpItems.length; i++) {
                if (res[pullUpItems[i].templateId]) {
                  choiceSaveParams.push({
                    ...pullUpItems[i],
                    choice: res[pullUpItems[i].templateId],
                  });
                }
              }
              return _this.saveMsgConfig(
                sceneName,
                scopeType,
                goodsId,
                activityId,
                choiceSaveParams
              );
            },
            fail(res) {
              console.log('fail:', res);
            },
          });
        }
      })
      .catch((err) => {
        console.error('SaveMsgConfig:', err);
      });
  },

  getTemplateV2(scene) {
    return app.request({
      path: '/wscvis/edu/push/getTemplateV2.json',
      method: 'GET',
      query: {
        scene,
      },
    });
  },

  shouldPullUpMsgDialog(scene, scopeType, goodsId, activityId) {
    return app.request({
      path: '/wscvis/edu/push/shouldPullUpMsgDialog.json',
      method: 'GET',
      query: {
        scene,
        scopeType,
        goodsId,
        activityId,
      },
    });
  },

  saveMsgConfig(scene, scopeType, goodsId, activityId, choiceSaveParams) {
    return app.request({
      path: '/wscvis/edu/push/saveMsgConfig.json',
      method: 'POST',
      data: {
        scene,
        scopeType,
        goodsId,
        activityId,
        choiceSaveParams,
      },
    });
  },

  pushSuccessCallback(scene, templateIdList) {
    return app.request({
      path: '/wscvis/edu/push/subscriptionCallback.json',
      method: 'POST',
      data: {
        scene,
        templateIdList,
      },
    });
  },

  // 包含分销员海报的简版商品信息查询接口
  getDistributorDetail(data) {
    return app.request({
      path: '/wscvis/ump/invite-card/getDistributorDetail.json',
      data,
    });
  },
};

export default API;
