const app = getApp();

function _getAliasPromise({ alias, sourceKdtId, targetKdtId }) {
  // 是连锁多网店
  // 并且当前店铺是连锁店
  // 则调用接口获取alias,即刷新页面
  return app
    .request({
      path: '/wscvis/edu/getCampusProductByCampus.json',
      data: {
        sourceProductAlias: alias,
        sourceKdtId,
        targetKdtId
      }
    })
    .then(data => {
      return data.campusProductAlias;
    })
    .catch(() => {
      return alias;
    });
}

function fetchActualAlias(currentAlias) {
  return new Promise(resolve => {
    app.getShopInfo().then(data => {
      const { chainStoreInfo = {}, kdt_id: kdtId } = data;
      const { isRootShop = false, isMultiOnlineShop = false } = chainStoreInfo;
      const currentKdtId = app.getKdtId();

      const queryData = {
        alias: currentAlias,
        sourceKdtId: app.globalData.prevMultiShopKdtID || currentKdtId || chainStoreInfo.rootKdtId,
        targetKdtId: currentKdtId || kdtId
      };

      // 非多网店(单店)则直接调接口
      if (!isMultiOnlineShop) {
        resolve(currentAlias);
        // 如果是总店则监听进店事件
      } else if (isRootShop) {
        app.once('app:chainstore:kdtid:update', ({ kdtId }) => {
          _getAliasPromise(
            Object.assign({}, queryData, {
              targetKdtId: kdtId
            })
          ).then(alias => {
            resolve(alias);
          });
        });
        // 如果是分店则直接更新alias
      } else {
        _getAliasPromise(queryData).then(alias => {
          resolve(alias);
        });
      }
    });
  });
}

export { fetchActualAlias };
