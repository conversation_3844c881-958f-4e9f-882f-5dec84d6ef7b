import { MEDIA_TYPE, PAGE_TYPE_MAP } from './constants';
import args from '@youzan/weapp-utils/lib/args';
import { getUrlLogParams } from 'utils/log/get-url-log-params';

const app = getApp();

export function parseTime(time, withYaer) {
  if (!time) return '';
  const date = new Date(time);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  let result = `${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
  return withYaer ? `${year}-${result}` : result;
}

export function parsePrice(price) {
  let priceCharList = (String(price) || '').split('');
  // 补充零位
  if (priceCharList.length < 3) {
    priceCharList = (new Array(3 - priceCharList.length)).fill('0').concat(priceCharList);
  }
  priceCharList.splice(-2, 0, '.');
  return priceCharList.join('');
}

// 将秒数转化为“分:秒”的形式
export function secondsToTimeStr(seconds) {
  const minute = parseInt(seconds / 60, 10);
  const second = Math.round(seconds % 60);
  return `${minute}分${second}秒`;
}

// 详情内容文案
export function getContentInfoText({
  isColumn, author, contentsCount, mediaType, videoDuration, audioDuration
}) {
  const authorText = author ? `作者：${author}` : '';
  let descText = '';
  if (isColumn) {
    descText = contentsCount ? ` 已更新${contentsCount}期` : '';
  } else if (mediaType === MEDIA_TYPE.VIDEO) {
    descText = videoDuration ? ` ${secondsToTimeStr(videoDuration)}` : '';
  } else if (mediaType === MEDIA_TYPE.AUDIO) {
    descText = audioDuration ? ` ${secondsToTimeStr(audioDuration)}` : '';
  }
  return authorText && descText ? `${authorText} | ${descText}` : authorText || descText;
}

// 获取当前页面的url不带参数
export function getCurrentPageUrl() {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  return currentPage.route;
}

// 获取当前页面的url带参数
export function getCurrentPageUrlWithParams() {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options;
  const url = currentPage.route;

  let urlWithParams = url + '?';
  Object.keys(options).forEach((key) => {
    urlWithParams += `${key}=${options[key]}&`;
  });
  return urlWithParams.substring(0, urlWithParams.length - 1);
}

// 获取当前页面的参数
export function getCurrentPageParams() {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options;
  return options;
}

// 构建url参数字符串
export function buildUrlParamsStr(params) {
  let paramStr = '';
  Object.keys(params).forEach((key) => {
    if (params[key]) {
      paramStr += `&${key}=${params[key]}`;
    }
  });
  return paramStr.substring(1);
}

export function createFormId(requestData, success, fail) {
  getApp().carmen({
    api: 'wsc.weapp.formid/1.0.0/add',
    data: requestData,
    method: 'GET',
    success: (res) => {
      success && success(res);
    },
    fail: (res) => {
      fail && fail(res.msg, res);
    }
  });
}

export const REFRESH_KEY = '__paid_content_refresh__';

// 需要goodsid埋点的地方调用，否则默认使用kdtid全局埋点
export const makeLog = ({
  id, alias, title, type
}) => {
  new Promise((resolve, reject) => {
    if (id) {
      resolve(id);
      return;
    }
    const requestData = {
      alias,
      type: PAGE_TYPE_MAP[type]
    };
    app.carmen({
      method: 'GET',
      api: 'youzan.owl.goodsid/1.0.0/get',
      query: requestData,
      success: (res) => {
        resolve(res.value);
      },
      fail: (err) => {
        reject(err);
      }
    });
  })
    .then((logId) => {
      app.trigger('goodsDetail:loaded', logId, {
        id: logId,
        title
      });
    })
    .catch(err => {
      console.warn('makeLog', err);
    });
};


export function getPageUrl() {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const { route, options } = currentPage;
  const argList = Object.keys(options).reduce((prev, key) => {
    return prev.concat(key + '=' + options[key]);
  }, []);
  if (!argList.length) return route;
  return route + '?' + argList.join('&');
}

/**
 * 获取url的主体部分和参数
 * @returns {path, params 参数部分}
 * @params url
 */
export function getUrlAndParams(url) {
  const path = url.split('?')[0];
  const paramsUrl = url.split('?')[1] || '';
  const params = {};
  if (paramsUrl) {
    paramsUrl.split('&').forEach((item) => {
      const tempArr = item.split('=');
      // 参数值中有等号，做处理
      params[tempArr[0]] = tempArr.slice(1).join('=');
    });
  }
  return {
    path,
    params
  };
}

// 数字对下取整
export function numberFloor(number) {
  return Math.floor(number);
}

/**
 * 获取店铺小程序在ios系统中的支付能力
 */
export function getShopOrderAbility() {
  return new Promise((resolve) => {
    app.request({
      path: 'wscvis/getWeappConfig.json'
    }).then((res) => {
      resolve(res);
    });
  });
}

export function isIOS() {
  const { platform } = app.getSystemInfoSync();
  return platform === 'ios';
}

export function getAddLogUrl(originUrl, query) {
  return args.add(originUrl, getUrlLogParams(query));
}
