<page-container
  fixed-bottom
  forbid-copyright="{{ !fetched }}"
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view class="gift-receive">
    <view class="header">
      <view class="h2"><text>{{ isShareGift ? contentData.num : '' }}{{ textInfo.title }}</text></view>
      <view class="h4">{{ textInfo.desc }}</view>
    </view>
    <gift-info-card wx:if="{{ contentData }}"
      cover="{{ contentData.cover }}"
      title="{{ contentData.title }}"
      author="{{ contentData.author }}"
      isColumn="{{ isColumn }}"
      avatar="{{ contentData.userHeadPortrait }}"
      userName="{{ contentData.userName }}"
      mediaType="{{ contentData.mediaType }}"
      videoTime="{{ contentData.videoDuration }}"
      receivGiftStatus="{{ contentData.shareStatus }}"
      contentsCount="{{ contentData.contentsCount }}"
    />
    <form 
      wx:if="{{ canSendGift }}"
      bindsubmit="handleBtnClick" 
      report-submit>
      <button class="action" formType="submit">{{ textInfo.btnText }}</button>
      <!-- <view class="action" formType="submit"> {{ textInfo.btnText }}</view> -->
    </form>
    <view wx:else class="action disabled-send-gift">{{ textInfo.btnText }}</view>
    <view wx:if="{{ isShareGift }}" class="tip">超过7天未被领取的礼物，将会自动原路退款至付款账户</view>
    <view wx:if="{{contentData.list.length}}">
      <view class="received">
        <text>已领取</text>
      </view>
      <view class="received-desc">
        <text>仅限{{ contentData.num }}个免费名额，先到先得</text>
      </view>
      <view wx:if="{{ contentData.list.length }}" class="record">
        <block wx:for="{{ contentData.list }}" wx:key="id">
          <rank-list-item
          item="{{ item }}"/>
        </block>
      </view>
    </view>
    <!-- 分享Pop -->
    <view wx:if="{{ isShareGift && contentData.num }}" class="zan-popup {{ popupData.isShowShareGuide ? 'zan-popup--show': ''}}">
      <view class="zan-popup__mask" bindtap="toggleShowShareFriend"></view>
      <view class="zan-popup__container">
        <giving-friend
          isShareGift="{{ isShareGift }}"
          everyContentFriendCount="{{ contentData.num }}"
          bind:close-mask="toggleShowShareFriend"
          bind:share-gift="shareGift"
        />
      </view>
    </view>
  </view>
</page-container>

<van-action-sheet
  show="{{ actionsheet.show }}"
  cancel-text="取消"
  close-on-click-overlay
  actions="{{ actionsheet.actions }}"
  bind:close="closeActionSheet"
  bind:cancel="closeActionSheet"
  bind:select="handleZanActionsheetClick"
/>
