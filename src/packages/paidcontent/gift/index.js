import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import logv3 from 'utils/log/logv3';
import WscPage from '../../../pages/common/wsc-page/index';
import { getCurrentPageUrlWithParams, buildUrlParamsStr, createFormId } from '../utils';
import {
  GIFT_TYPE, GIFT_TEXT_INFO, RECEIVE_GIFT_STATUS, CONTENT_TYPE, CAN_SEND_GIFT_TYPE
} from '../constants';

const app = getApp();

WscPage({
  data: {
    contentData: null,
    showSharedFriend: false,
    isColumn: false,
    popupData: {
      // 分享pop
      isShowShareGuide: false
    },
    actionsheet: {
      show: false,
      actions: [{
        index: 0,
        name: '发送给朋友',
        openType: 'share'
      }]
    },
    fetched: false,
    canSendGift: true
  },
  onLoad({
    alias = '',
    gift_type = '',
    order_alias = '',
    share_alias = '',
    channel_type = '',
    gift_no = '',
    gift_sign = ''
  }) {
    const giftType = parseInt(gift_type, 10);
    this.setYZData({
      alias,
      giftType,
      orderAlias: order_alias,
      isShareGift: giftType === GIFT_TYPE.SHARE_GIFT,
      shareAlias: share_alias,
      channelType: channel_type,
      giftNo: gift_no,
      giftSign: gift_sign
    });
    if (giftType === GIFT_TYPE.SHARE_GIFT) {
      this.getShareGift({
        order_alias
      });
    } else if (giftType === GIFT_TYPE.RECEIVE_GIFT) {
      const receiveParam = this.getReceiveParam();
      this.getReceiveGift(receiveParam).then((result) => {
        this.praseDataForShow(result);
      });
    }
  },

  // 获取领取礼物详情
  getReceiveGift(params) {
    return new Promise((resolve) => {
      app.carmen({
        method: 'GET',
        api: 'youzan.owl.share.sharecontent/1.0.0/get',
        data: params,
        success: (res) => {
          res = mapKeysCase.toCamelCase(res);
          resolve(res);
        },
        fail: () => {
          wx.showToast({
            title: '获取礼物详情失败',
            icon: 'none'
          });
        }
      });
    });
  },
  // 获取领取礼物页参数
  getReceiveParam() {
    return {
      alias: this.data.alias,
      share_alias: this.data.shareAlias,
      channel_type: this.data.channelType,
      order_alias: this.data.orderAlias,
      gift_no: this.data.giftNo,
      gift_sign: this.data.giftSign
    };
  },
  // 获取分享礼物内容
  getShareGift(params) {
    app.carmen({
      method: 'GET',
      api: 'youzan.owl.share.giftdetail/1.0.0/get',
      data: params,
      success: (res) => {
        this.praseDataForShow(res);
      },
      fail: () => {
        wx.showToast({
          title: '获取送礼详情失败',
          icon: 'none'
        });
      }
    });
  },
  praseDataForShow(contentData) {
    const isShareGift = this.data.isShareGift;
    const textInfo = GIFT_TEXT_INFO[isShareGift ? 'SHARE_GIFT' : contentData.shareStatus] || {};
    const isColumn = contentData.owlType === CONTENT_TYPE.COLUMN;
    const canSendGift = isShareGift ? contentData.canSendGift === CAN_SEND_GIFT_TYPE.YES : true;

    this.setYZData({
      contentData,
      fetched: true,
      canSendGift,
      textInfo,
      isColumn
    });
  },
  toggleShow(name) {
    const popupData = this.data.popupData;
    popupData[name] = !popupData[name];
    this.setYZData({ popupData });
  },
  // 展示分享给好友
  toggleShowShareFriend() {
    this.toggleShow('isShowShareGuide');
  },
  // 处理送礼事件
  handleBtnClick(evt) {
    let formId = evt.detail.formId || '';
    if (this.data.isShareGift) {
      createFormId({
        form_id: formId,
        business_module: 'owlGift'
      });
      this.toggleShowShareFriend();
    } else if (this.data.contentData.shareStatus === RECEIVE_GIFT_STATUS.UNRECEIVE) {
      createFormId({
        form_id: formId,
        business_module: 'owlShareReceive'
      });
      const receiveParam = this.getReceiveParam();
      this.getReceiveGift(receiveParam).then((data) => {
        const shareStatus = data.shareStatus;
        if (shareStatus === RECEIVE_GIFT_STATUS.UNRECEIVE) {
          this.getReceiveGiftResult(receiveParam);
          // this.jumpToContent(shareStatus);
        } else {
          this.praseDataForShow(data);
        }
      });
    } else {
      this.jumpToContent();
    }
  },

  getReceiveGiftResult(params) {
    app.carmen({
      method: 'GET',
      api: 'youzan.owl.share.receive/1.0.0/get',
      data: params,
      success: (res) => {
        res = mapKeysCase.toCamelCase(res);
        this.jumpToContent(res.shareStatus);
      },
      fail: (err) => {
        wx.showToast({
          title: err.msg,
          icon: 'none'
        });
      }
    });
  },
  // 导航到内容详情页
  jumpToContent(shareStatus) {
    // 跳转到详情页面
    const type = this.data.isColumn ? 'column' : 'content';
    let url = `/packages/paidcontent/${type}/index?alias=${this.data.alias}`;
    if (shareStatus === RECEIVE_GIFT_STATUS.RECEIVED) {
      wx.showToast({
        title: '领取成功，跳转中...',
        icon: 'none'
      });
      const paramsStr = buildUrlParamsStr({
        is_receive: 1,
        share_alias: this.data.shareAlias,
        channel_type: this.data.channelType,
        order_alias: this.data.orderAlias,
        gift_no: this.data.giftNo,
        gift_sign: this.data.giftSign
      });
      url = `${url}&${paramsStr}`;
    }
    wx.navigateTo({
      url
    });
  },
  onShareAppMessage() {
    const {
      isShareGift,
      contentData,
      alias,
      channelType,
      orderAlias,
      shareAlias
    } = this.data;
    const shareUserName = contentData.userName || '匿名用户';
    if (isShareGift) {
      const { giftNo, giftSign } = contentData;
      const paramStr = buildUrlParamsStr({
        kdt_id: app.getKdtId(),
        alias,
        share_alias: shareAlias,
        channel_type: channelType,
        order_alias: orderAlias,
        gift_no: giftNo,
        gift_sign: giftSign,
        gift_type: 2
      });
      const shareUrl = `packages/paidcontent/gift/index?${paramStr}`;

      return logv3.processShareData({
        title: `${shareUserName}买了份礼物送给你，快来领取吧`,
        path: shareUrl,
        imageUrl: contentData.cover
      });
    }

    const author = contentData.author;
    const authorText = author ? `作者：${author}` : '';
    return logv3.processShareData({
      title: `${authorText} | ${contentData.title}`,
      path: getCurrentPageUrlWithParams(),
      imageUrl: contentData.cover
    });
  },
  // 分享送礼
  shareGift() {
    this.toggleShowShareFriend();
    this.setYZData({
      'actionsheet.show': true
    });
  },
  handleZanActionsheetClick({ index }) {
    this.__yzLog__({
      et: 'click',
      ei: 'share_gift',
      en: '送礼',
      params: {
        share_method: index === 1 ? 'card' : 'wx_share'
      }
    });
  },
  closeActionSheet() {
    this.setYZData({
      'actionsheet.show': false
    });
  }
});
