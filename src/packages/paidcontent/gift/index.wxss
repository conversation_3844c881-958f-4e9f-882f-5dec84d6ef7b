.gift-receive {
  box-sizing: border-box;
  padding: 35px;
  width: 100%;
  min-height: 100%;
  background-image: url(https://img.yzcdn.cn/public_files/2018/05/09/917e3026d6f59864ba15df28dc67ef5c.png);
  background-size: 100%;
}

.header {
  text-align: center;
  color: #c8a06f;
}

.h2 {
  display: flex;
  align-items: center;
  font-size: 36rpx;
  justify-content: center;
}

.h2 text {
  margin: 0 10rpx;
}

.h2::before,
.h2::after {
  display: inline-block;
  content: "";
  width: 130rpx;
  height: 4rpx;
}

.h2::before {
  background: linear-gradient(to left, #c8a06f, #fff);
}

.h2::after {
  background: linear-gradient(to right, #c8a06f, #fff);
}

.h4 {
  margin-top: 5px;
  font-size: 12px;
}

.action {
  line-height: 1;
  padding: 10px 0;
  margin-top: 40px;
  background: #f44;
  font-size: 14px;
  text-align: center;
  color: #fff;
  border-radius: 2px;
}

.disabled-send-gift {
  color: #999;
  background-color: #e5e5e5;
  pointer-events: none;
}

.tip {
  margin-top: 10px;
  font-size: 10px;
  color: #999;
  font-weight: 100;
  text-align: center;
}

.received {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.received text {
  display: inline-block;
  position: relative;
  z-index: 2;
  padding: 0 10px;
  color: #333;
  font-size: 14px;
  background: #fff7ed;
}

.received::after {
  position: absolute;
  z-index: 1;
  display: block;
  width: 100%;
  height: 1px;
  background: #ccc;
  content: "";
}

.received-desc {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10rpx;
  font-size: 12px;
  color: #999;
}

.gift-receive .zan-popup__mask {
  z-index: 11;
}

.gift-receive .zan-popup__container {
  z-index: 12;
  width: 100%;
  background: transparent;
}
