const app = getApp();
const BASE_URL = '/wscvis/exam';

// 获取题目列表相关
export function getQuestionListApi(query) {
  return app.request({
    method: 'GET',
    path: `${BASE_URL}/getQuestionList.json`,
    query
  });
}

// 提交测试答案相关(!!!注意和GET方式字段的区分)
export function submitAnswerApi(data) {
  return app.request({
    method: 'POST',
    path: `${BASE_URL}/submitAnswer.json`,
    data
  });
}

// 获取结果详情
export function getExamRecordApi(query) {
  return app.request({
    method: 'GET',
    path: `${BASE_URL}/getExamRecord.json`,
    query
  });
}

// 获取分享相关
export function getShareApi(query) {
  return app.request({
    method: 'GET',
    path: `${BASE_URL}/getShare.json`,
    query
  });
}

// 获取测试详情
export function getExamDetailApi(query) {
  return app.request({
    method: 'GET',
    path: `${BASE_URL}/getExamDetail.json`,
    query
  });
}

// 参与测试
export function joinExamApi(data) {
  return app.request({
    method: 'POST',
    path: `${BASE_URL}/joinExam.json`,
    data
  });
}
