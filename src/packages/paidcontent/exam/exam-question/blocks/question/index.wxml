<view
  style="height: 100vh; width: 100vw; position: fixed; background: url({{currentQuestion.backgroundPic.url}}) no-repeat top center / cover;"
></view>
<view style="position: absolute; left: 0; width: 100%; top: 0">
  <question-head
    current-index="{{currentIndex}}"
    question-count="{{questionCount}}"
    item="{{currentQuestion}}"/>
  <question-item
    item="{{currentQuestion}}"
    current-index="{{currentIndex}}"
    question-count="{{questionCount}}"
    bind:answer="receiveAnswer"
    bind:question="receiveQuestion"/>
  <question-operate
    loading="{{loading}}"
    item="{{currentQuestion}}"
    current-index="{{currentIndex}}"
    question-count="{{questionCount}}"
    question-type="{{currentQuestion.questionType}}"
    next-question-menu-pic="{{currentQuestion.nextQuestionMenuPic}}"
    bind:answer="receiveAnswer"
    bind:question="receiveQuestion"/>
</view>