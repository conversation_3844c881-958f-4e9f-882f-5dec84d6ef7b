import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  properties: {
    loading: {
      type: Boolean,
      value: false
    },
    currentQuestion: {
      type: Object,
      value: {}
    },
    questionCount: {
      type: Number,
      value: 0
    },
    currentIndex: {
      type: Number,
      value: 0,
      observer() {
        // 设置答题页回到顶部
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 0
        });
        this.setYZData({ scrollTop: 0 });
      }
    }
  },

  data: {
    bgHeight: 0,
    scrollTop: 0
  },

  attached() {
    const windowHeight = app.getSystemInfoSync().windowHeight;
    this.setYZData({ bgHeight: windowHeight });
  },

  methods: {
    // 一级组件通信到页面（记录答案）
    receiveAnswer(e) {
      const ev = e.detail || {};
      this.triggerEvent('answerinfo', ev);
    },
    // 一级组件通信到页面（题目切换）
    receiveQuestion(e) {
      const ev = e.detail || {};
      this.triggerEvent('switchquestion', ev);
    }
  }
});
