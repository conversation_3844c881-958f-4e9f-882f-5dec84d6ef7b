import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  properties: {
    loading: {
      type: Boolean,
      value: false
    },
    item: {
      type: Object,
      value: {},
      // 最后一题勾选后显示查看结果按钮
      observer(newVal) {
        // ?? ob obj
        if (this.data.currentIndex === this.data.questionCount - 1) {
          console.log('lastItem:', newVal.itemList);
          let counter = 0;
          newVal.itemList.forEach(item => {
            if (item.selected) {
              counter++;
            }
          });
          console.log('222:counter', counter);
          counter > 0 ? this.setYZData({ lastItemIsChecked: true }) : this.setYZData({ lastItemIsChecked: false });
          console.log('333:isC', this.data.lastItemIsChecked);
        }
      }
    },
    questionCount: {
      type: Number,
      value: 0
    },
    currentIndex: {
      type: Number,
      value: 0
    },
    questionType: {
      type: Number,
      value: 1
    },
    nextQuestionMenuPic: {
      type: Object,
      value: {},
      observer(newVal) {
        // ?? ob obj
        if (newVal) {
          console.log('nextQuestionMenuPic', newVal);
          this.handlePicForNext(newVal);
        }
      }
    }
  },

  data: {
    lastItemIsChecked: false,
    nextBtnWidth: 0,
    nextBtnHeight: 0
  },

  attached() {
  },

  methods: {
    toPrevQuestion() {
      this.triggerEvent('question', {
        type: 2
      });
    },
    toNextQuestion() {
      this.triggerEvent('question', {
        type: 1
      });
    },
    submitAnswer() {
      this.triggerEvent('answer', {
        type: 6
      });
    },
    // 自定义下一步图片
    handlePicForNext(obj) {
      const { windowWidth } = app.getSystemInfoSync();
      const maxWidth = windowWidth - 20;
      const nextBtnWidth = obj.width > maxWidth ? maxWidth : obj.width;
      const nextBtnHeight = obj.width > maxWidth ? maxWidth / obj.width * obj.height : obj.height;
      console.log('nextBtnWidth:', this.data.nextBtnWidth);
      this.setYZData({
        nextBtnWidth,
        nextBtnHeight
      });
    }
  }
});
