import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  type: 'video',

  properties: {
    currentIndex: {
      type: Number,
      observer(newVal) {
        // 通过监测路由变化解决连续视频播放问题
        console.log('video:currentIndex', newVal);
        this.init();
        this.setYZData({ hidePoster: false });
        console.log('video:hidePoster', this.data.hidePoster);
      }
    },
    src: String,
    poster: String,
    objectFit: {
      type: String,
      value: 'contain'
    }
  },

  data: {
    height: 0,
    hidePoster: false,
    showVideo: false,
  },

  ready() {
    this.init();
  },

  methods: {
    init() {
      this.setData({
        showVideo: false, // 防止连续视频播放时前一个视频播放完后下一个视频也会显示播放最后的暂停
        height: (app.getSystemInfoSync().windowWidth * 9) / 16,
        id: Math.random().toString(36).substr(2, 9)
      });

      this.videoContext = wx.createVideoContext('video-id', this);
    },

    handlePosterClick() {
      this.setData({
        showVideo: true
      }, () => {
        // 设置延迟 隐藏视频模块出现的一刹那的缩放动画
        setTimeout(() => {
          this.setData({
            hidePoster: true,
            showVideo: true
          }, () => {
            this.videoContext.play();
          });
        }, 200);
      });
    },

    handleVideoPlay(e) {
      this.triggerEvent('play', e);
    },

    handleVideoPause(e) {
      this.triggerEvent('pause', e);
    },

    pause() {
      this.videoContext.pause();
    },

    play() {
      this.videoContext.play();
    }
  }
});
