.c-video {
  position: relative;
  width: 100%;
}
.c-video__player {
  display: block;
  width: 100%;
}

.c-video__player-hide {
  display: none;
}

.c-video__poster-wrap {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: #000;
}

.c-video__poster {
  width: 100%;
}

.c-video__button {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12px 0 12px 24px;
  border-color: transparent transparent transparent #fff;
  z-index: 2;
}

.c-video__mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: .6;
}
