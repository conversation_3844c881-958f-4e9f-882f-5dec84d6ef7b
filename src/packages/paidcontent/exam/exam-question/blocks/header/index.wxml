<wxs src="../question/common.wxs" module="quesUtil"></wxs>

<view class="question-head">
  <block wx:if="{{item.mediaType === 1}}">
    <view class="question-head__text question-head__pic">
      <view wx:if="{{item.description}}">
        {{quesUtil.getQuestionText(currentIndex, questionCount, item)}} <text class="question-head__text-title--type">{{quesUtil.getQuestionType(item)}}</text>
      </view>
      <view wx:else>{{quesUtil.getQuestionSeriaNo(currentIndex, questionCount)}}</view>
      <view class="question-head__pic-cover">
        <image
          class="question-head__pic-cover--style"
          mode="widthFix"
          src="{{item.media.descUrl}}">
        </image>
      </view>
    </view>
  </block>

  <block wx:if="{{item.mediaType === 3}}">
    <view class="question-head__text question-head__audio">
      <view class="question-head__text-title">
        <view wx:if="{{item.description}}">
          {{quesUtil.getQuestionText(currentIndex, questionCount, item)}} <text class="question-head__text-title--type">{{quesUtil.getQuestionType(item)}}</text>
        </view>
        <view wx:else>{{quesUtil.getQuestionSeriaNo(currentIndex, questionCount)}}</view>
      </view>

      <audio-content
        title="{{item.media.mediaName}}"
        reload="+0"
        loop="+0"
        src="{{item.media.descUrl}}"/>
    </view>
  </block>

  <block wx:if="{{item.mediaType === 2}}">
    <view class="question-head__text question-head__video">
      <view class="question-head__text-title">
        <view wx:if="{{item.description}}">
          {{quesUtil.getQuestionText(currentIndex, questionCount, item)}} <text class="question-head__text-title--type">{{quesUtil.getQuestionType(item)}}</text>
        </view>
        <view wx:else>{{quesUtil.getQuestionSeriaNo(currentIndex, questionCount)}}</view>
      </view>

      <video-content
        class=""
        current-index="{{currentIndex}}"
        src="{{item.media.video.videoUrl}}"
        poster="{{item.media.video.coverUrl}}"/>
    </view>
  </block>

  <block wx:if="{{item.mediaType === 0}}">
    <view class="question-head__text" wx:if="{{item.description}}">
      {{quesUtil.getQuestionText(currentIndex, questionCount, item)}} <text class="question-head__text-title--type">{{quesUtil.getQuestionType(item)}}</text>
    </view>
  </block>
</view>