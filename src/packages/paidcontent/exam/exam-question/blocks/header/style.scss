$c-place: #f8f8f8;
$c-gray: #bbb;

.text-placeholder {
  background-color: $c-place;
  height: 156px;
  text-align: center;
  line-height: 156px;
  color: $c-gray;
  margin-bottom: 20px;
}

.pic-placeholder {
  padding: 20px;

  &__question {
    height: 64px;
    background-color: $c-place;
    text-align: center;
    line-height: 64px;
    color: $c-gray;
    margin-bottom: 15px;
  }

  &__pic {
    height: 136px;
    background-color: $c-place;
    text-align: center;
    line-height: 136px;
    color: $c-gray;
    margin-bottom: 15px;
  }

  &__audio {
    height: 33px;
    background-color: $c-place;
    border-radius: 16px;
    text-align: center;
    line-height: 33px;
    color: $c-gray;
  }
}

.question-head__text {
  padding: 20px;
  color: #333;
  font-size: 17px;
  line-height: 20px;
  margin-bottom: 20px;

  &-title {
    margin-bottom: 20px;
  }

  &-title--type {
    display: inline-block;
    width: 32px;
    height: 16px;
    font-size: 12px;
    color: #666;
    border: 1px solid #999;
    line-height: 16px;
    text-align: center;
  }
}

.question-head__pic {

  &-cover {
    margin-top: 20px;

    &--style {
      width: 100%;
    }
  }
}

.question-head__audio {

  &-content {
    margin-top: 20px;
  }
}

.question-head__video-wrap {
  position: relative;
}

.question-head__video-content {
  width: 100%;
  height: 100%;
}

.question-head__video {
  margin-bottom: 10px;

  /*
  div {
    margin-top: 20px;

    #myVideo {
      width: 100%;
      height: 100%;
    }
  }
  */
}

.question-head__video-cover {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1000;
}

.question-head__video-cover-start {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  background-image: url(https://b.yzcdn.cn/fix-base64/72f37f85b032522de97fc4caa608f478d9f4dc7d38f07c0f31b2b22901c4605e.png);
  background-size: 60px 60px;
  transform: translate(-50%, -50%);
  z-index: 1001;
}

video::-internal-media-controls-download-button {
  display: none;
}

video::-webkit-media-controls-enclosure {
  overflow: hidden;
}

video::-webkit-media-controls-panel {
  width: calc(100% + 50px);
}