import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    itemList: {
      type: Array,
      value: [],
      observer(newVal) {
        // ?? ob obj
        console.log('picture-radio:', newVal);
        const myItemList = newVal.map(item => {
          if (item.selected) return { ...item, active: true };
          return { ...item, active: false };
        });
        this.setYZData({
          myItemList
        });
      }
    },
    currentIndex: {
      type: Number,
      value: 0
    },
    itemRowNum: {
      type: Number,
      value: 1
    },
    questionCount: {
      type: Number,
      value: 0
    }
  },

  data: {
    myItemList: []
  },

  attached() {
  },

  methods: {
    toNextQuestion(event) {
      const value = event.currentTarget.dataset.value || {};
      // 记录答案
      this.triggerEvent('toanswer', {
        type: 4,
        id: value.id
      });
      // 题目跳转
      if (this.data.currentIndex !== this.data.questionCount - 1) {
        this.triggerEvent('toquestion', {
          type: 1
        });
      }
    }
  }
});
