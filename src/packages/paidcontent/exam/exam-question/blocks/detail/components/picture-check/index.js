import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    itemList: {
      type: Array,
      value: [],
      observer(newVal) {
        // ?? ob obj
        const myItemList = newVal.map(item => {
          if (item.selected) return { ...item, active: true };
          return { ...item, active: false };
        });
        this.setYZData({
          myItemList
        });
      }
    },
    currentIndex: {
      type: Number,
      value: 0
    },
    itemRowNum: {
      type: Number,
      value: 1
    }
  },
  data: {
    myItemList: []
  },
  attached() {
  },
  methods: {
    changeStatus(event) {
      const index = event.currentTarget.dataset.index || 0;
      const value = event.currentTarget.dataset.value || {};
      const curItemActive = `myItemList[${index}].active`;
      this.setYZData({
        [curItemActive]: !value.active
      });
      console.log('picture-check:myItemList:', this.data.myItemList);
      // 提交选项
      this.triggerEvent('toanswer', {
        type: 3,
        id: value.id,
        flag: !value.active
      });
    }
  }
});
