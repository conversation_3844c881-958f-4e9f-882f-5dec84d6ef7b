$c-item: #00b389;
$c-place: #f8f8f8;
$c-gray: #bbb;

.clearfix {
  &::after {
    height: 0;
    clear: both;
    visibility: hidden;
    content: '';
    display: block;
  }
}

.item-list {
  &__pic-two {
    width: calc(50% - 7.5px);
    float: left;
  }

  &__pic-two:nth-child(2n+1) {
    margin-right: 15px;
  }

  &__pic {
    margin-bottom: 15px;
    max-width: 100%;
    box-sizing: border-box;
    position: relative;
    display: inline-block;

    &__content {
      max-width: 100%;
      vertical-align: middle;
    }

    &__content-status {
      position: absolute;
      width: 21px;
      height: 21px;
      top: 0;
      right: 0;

      &-bg {
        border-top: 21px solid $c-item;
        border-left: 21px solid transparent;
        display: inline-block;
      }

      &-icon {
        width: 10px;
        height: 8px;
        position: absolute;
        top: 3px;
        right: 0;
      }
    }
  }

  &__pic-checked {
    border: 1px solid $c-item;
  }
}