import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    item: {
      type: Object,
      value: {},
      observer(newVal) {
        // ?? ob obj
        console.log('newQuestionItem:', newVal);
        this.getSelectedItem(newVal);
      }
    },
    currentIndex: {
      type: Number,
      value: 0
    },
    questionCount: {
      type: Number,
      value: 0
    }
  },

  data: {
    checkSelected: [],
    radioSelected: ''
  },

  methods: {
    // 根据标记获取选中的选项
    getSelectedItem(ques) {
      this.setYZData({
        checkSelected: []
      });
      // this.data.checkSelected = [567, 568];
      const question = ques;
      const items = question.itemList;
      console.log('detail:radio', items);
      if (question.questionType === 1) {
        console.log('detail---------');
        for (let i = 0; i < items.length; i++) {
          if (items[i].selected === true) {
            this.setYZData({ radioSelected: items[i].id });
            break;
          }
        }
        console.log(this.data.radioSelected);
      }
      if (question.questionType === 2) {
        let checkSelected = [];
        for (let i = 0; i < items.length; i++) {
          if (items[i].selected === true) {
            checkSelected.push(items[i].id + '');
          }
        }
        this.setYZData({ checkSelected });
      }
      console.log('checkSelected:', this.data.checkSelected);
    },
    // 单选文字选择答案后直接跳转下一题
    toNextQuestion(event) {
      const value = event.currentTarget.dataset.value || {};
      this.triggerEvent('answer', {
        type: 1, // 单选选项
        id: value.id
      });
      // 非最后一道题跳转到下一题
      if (this.data.currentIndex !== this.data.questionCount - 1) {
        setTimeout(() => {
          this.triggerEvent('question', {
            type: 1
          });
        }, 100);
      }
    },
    // 多选文字记录所选项
    saveAnswer(event) {
      console.log('event:', event);
      this.setYZData({ checkSelected: event.detail });
      this.triggerEvent('answer', {
        type: 2, // 多选文字
        id: this.data.checkSelected
      });
    },
    // 二级组件通信给一级组件（记录答案）
    toAnswer(e) {
      const ev = e.detail || {};
      this.triggerEvent('answer', ev);
    },
    // 二级组件通信给一级组件（题目切换）
    toQuestion(e) {
      const ev = e.detail || {};
      this.triggerEvent('question', ev);
    }
  }
});
