<view class="question-item">
    <block wx:if="{{item.style === 1}}">
      <view wx:if="{{item.questionType === 1}}">
        <van-radio-group value="{{radioSelected}}">
          <view
            wx:for="{{item.itemList}}"
            wx:key="index"
            wx:for-item="value"
            wx:for-index="index">
            <van-radio
              custom-class="question-item__checkbox-wrap"
              icon-class="{{ radioSelected === value.id ? 'question-item__icon--selected' : ''}}"
              name="{{value.id}}"
              data-value="{{value}}"
              bind:tap="toNextQuestion">
              {{value.detail}}
            </van-radio>
          </view>
        </van-radio-group>
      </view>
      <view wx:if="{{item.questionType === 2}}">
        <van-checkbox-group value="{{checkSelected}}" bind:change="saveAnswer">
          <view
            class="question-item__checkbox-wrap"
            wx:for="{{item.itemList}}"
            wx:key="index"
            wx:for-item="value"
            wx:for-index="index">
            <van-checkbox checked-color="#00b389" name="{{value.id}}">{{value.detail}}</van-checkbox>
          </view>
        </van-checkbox-group>
      </view>
    </block>
    <block wx:if="{{item.style === 2}}">
      <picture-radio
        wx:if="{{item.questionType === 1}}"
        item-list="{{item.itemList}}"
        item-row-num="{{item.itemRowNum}}"
        current-index="{{currentIndex}}"
        question-count="{{questionCount}}"
        bind:toanswer="toAnswer"
        bind:toquestion="toQuestion"
      />
      <picture-check
        wx:if="{{item.questionType === 2}}"
        current-index="{{currentIndex}}"
        item-list="{{item.itemList}}"
        item-row-num="{{item.itemRowNum}}"
        serial-no="{{item.serialNo}}"
        bind:toanswer="toAnswer"
      />
    </block>
  </view>