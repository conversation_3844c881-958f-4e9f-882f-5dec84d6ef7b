// 获取题目描述信息
const getQuestionText = (currentIndex, questionCount, item) => {
  return (currentIndex + 1) + '/' + questionCount + '、' + item.description;
};

const getQuestionSeriaNo = (currentIndex, questionCount) => {
  return (currentIndex + 1) + '/' + questionCount;
};

const getQuestionType = (item) => {
  return item.questionType === 1 ? '单选' : '多选';
};

const getImg = (obj = {}) => {
  const { url, width, height } = obj;
  return {
    url,
    width,
    height
  };
};

const getImgUrl = (obj) => {
  return getImg(obj).url;
};

const getimgBoxSize = (obj) => {
  const WIDTH = 750;
  const img = getImg(obj);
  const width = img.width;
  const height = img.height;
  if (width <= 750) {
    return {
      width,
      height,
    };
  }
  const changedHeight = parseInt(height * WIDTH / width, 10);
  return {
    width: WIDTH,
    height: changedHeight
  };
};

module.exports = {
  getQuestionText,
  getQuestionSeriaNo,
  getQuestionType,
  getImg,
  getImgUrl,
  getimgBoxSize
};
