<view wx:if="{{!isLoading}}">
  <exam-question
    wx:if="{{!isEmpty}}"
    loading="{{submitLoading}}"
    current-question="{{questionList[currentIndex]}}"
    question-count="{{questionCount}}"
    current-index="{{currentIndex}}"
    bind:answerinfo="answerInfo"
    bind:switchquestion="switchQuestion"
  />
  <empty wx:else text="暂无测试问题列表数据！" />
</view>
<view class="loading-container" wx:else>
  <van-loading
    type="spinner"
    class="detail-loading"
    color="white"/>
</view>