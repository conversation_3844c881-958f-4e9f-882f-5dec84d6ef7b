import WscPage from 'pages/common/wsc-page/index.js';
import { getQuestionListApi, submitAnswerApi } from '../api';

const currentPage = {
  data: {
    isLoading: true,
    submitLoading: false,
    isEmpty: false, // 页面有无渲染数据
    examId: 0,
    kdtId: 0,
    identity: '',
    currentIndex: 0,
    // currentQuestion: {},
    questionCount: 0,
    questionList: []
  },

  onLoad(query) {
    const examId = +query.examId || 0;
    const currentIndex = +query.currentIndex || 0;
    const kdtId = +query.kdtId || 0;
    const identity = query.identity || '';
    console.log('[question:currentIndex]', currentIndex);
    this.setYZData({
      examId,
      kdtId,
      currentIndex,
      identity
    });
    this.getQuestionList();
  },

  // 获取测试详情
  getQuestionList() {
    getQuestionListApi({
      examId: this.data.examId,
      identity: this.data.identity
    })
      .then(res => {
        this.setYZData({ isLoading: false });

        console.log('res:', res);

        const data = res || {};
        const changedData = {
          questionList: data.questionList,
          // currentQuestion: data.questionList[this.data.currentIndex],
          questionCount: data.questionCount
        };

        console.log('[question:changeData]', changedData);

        this.setYZData(changedData);
      })
      .catch(e => {
        this.setYZData({
          isLoading: false,
          isEmpty: true
        });
        wx.showToast({
          title: e.msg,
          icon: 'none'
        });
      });
  },

  // 页面处理记录答案
  answerInfo(e) {
    console.log('answerInfo:', e.detail);
    const ev = e.detail || {};

    // 文字选项-单选
    if (ev.type === 1) {
      const filterItemList = this.data.questionList[this.data.currentIndex].itemList.map((item) => {
        if (item.id === ev.id) return { ...item, selected: true };
        return { ...item, selected: false };
      });
      this.setYZData({
        [`questionList[${this.data.currentIndex}].itemList`]: filterItemList
      });
    }

    // 文字选项-多选
    if (ev.type === 2) {
      const ids = ev.id;
      if (ids.length > 0) {
        const filterItemList = this.data.questionList[this.data.currentIndex].itemList.map(item => {
          if (ids.indexOf(item.id + '') !== -1) return { ...item, selected: true };
          return { ...item, selected: false };
        });
        console.log('filterItemList:', filterItemList);

        this.setYZData({
          [`questionList[${this.data.currentIndex}].itemList`]: filterItemList
        });
      } else {
        const filterItemList = this.data.questionList[this.data.currentIndex].itemList.map(item => {
          return { ...item, selected: false };
        });

        this.setYZData({
          [`questionList[${this.data.currentIndex}].itemList`]: filterItemList
        });
      }
      console.log('ev:type:', this.data.questionList[this.data.currentIndex]);
    }

    // 图片选项-多选
    if (ev.type === 3) {
      const filterItemList = this.data.questionList[this.data.currentIndex].itemList.map((item) => {
        if (item.id === ev.id) {
          if (ev.flag) return { ...item, selected: true };
          return { ...item, selected: false };
        }
        return item;
      });

      this.setYZData({
        [`questionList[${this.data.currentIndex}].itemList`]: filterItemList
      });

      console.log('图片多选:', this.data.questionList[this.data.currentIndex].itemList);
    }

    // 图片选项-单选
    if (ev.type === 4) {
      const filterItemList = this.data.questionList[this.data.currentIndex].itemList.map((item) => {
        if (item.id === ev.id) return { ...item, selected: true };
        return { ...item, selected: false };
      });

      this.setYZData({
        [`questionList[${this.data.currentIndex}].itemList`]: filterItemList
      });

      console.log('图片单选:', this.data.questionList[this.data.currentIndex].itemList);
    }

    // 6为提交答案
    if (ev.type === 6) {
      const itemList = this.data.questionList[this.data.currentIndex].itemList;
      console.log('提交答案时的itemList：', itemList);
      let count = 0;
      itemList.forEach((item) => {
        if (item.selected) count++;
      });
      if (count > 0) {
        this.submitAnswer();
      } else {
        wx.showToast({ title: '您还未选择答案哦!', icon: 'none' });
      }
    }
  },

  // 页面中处理题目切换
  switchQuestion(e) {
    console.log('switchQuestion:', e.detail);
    const ev = e.detail || {};

    const itemList = this.data.questionList[this.data.currentIndex].itemList;
    let count = 0;
    itemList.forEach((item) => {
      if (item.selected) count++;
    });

    // type === 1 切换到下一题
    if (ev.type === 1) {
      if (count > 0) {
        this.setYZData({
          currentIndex: this.data.currentIndex + 1,
          // currentQuestion: this.data.questionList[this.data.currentIndex + 1]
        });
      } else {
        wx.showToast({ title: '您还未选择答案哦!', icon: 'none' });
      }
    }
    // type === 2 切换到上一题
    if (ev.type === 2) {
      this.setYZData({
        currentIndex: this.data.currentIndex - 1,
        // currentQuestion: this.data.questionList[this.data.currentIndex - 1]
      });
    }
  },

  submitAnswer() {
    // 遍历获取所有问题的id和对应选项的id
    const itemIdList = [];
    const questionIdList = [];
    this.data.questionList.forEach((item) => {
      const questionId = item.id;
      const itemList = item.itemList;
      let count = 0;
      itemList.forEach((item) => {
        if (item.selected) {
          count++;
          itemIdList.push(item.id);
        }
      });
      count > 0 ? questionIdList.push(questionId) : undefined;
    });
    // 问题id数量和问题总数相同时表示所有题答完
    console.log('questionIdList:', questionIdList.length);
    console.log('questionCount:', this.data.questionCount);
    if (questionIdList.length === this.data.questionCount) {
      this.setYZData({ submitLoading: true });

      submitAnswerApi({
        examId: this.data.examId,
        kdtId: this.data.kdtId,
        identity: this.data.identity,
        itemIdList,
        questionIdList
      })
        .then(res => {
          this.setYZData({ submitLoading: false });

          console.log('submitAnswerApi:', res);
          const data = res || {};
          if (data.recordDetail) {
            // navigateTo会保留当前页面，跳转到下一个页面（会导致最后一题为音频时继续播放）
            wx.redirectTo({
              url: `/packages/paidcontent/exam/exam-result/index?examId=${this.data.examId}&identity=${this.data.identity}`
            });
          }
        })
        .catch(e => {
          this.setYZData({ submitLoading: false });

          console.log(e.msg);
          wx.showToast({
            title: e.msg,
            icon: 'none'
          });
        });
    } else {
      wx.showToast({
        title: '您还有题目未选择答案哦!',
        icon: 'none'
      });
    }
  }
};

const pageConfig = Object.assign({}, currentPage);

WscPage(pageConfig);
