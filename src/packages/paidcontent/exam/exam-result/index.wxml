<page-container>
  <block wx:if="{{!isLoading}}">
    <view class="exam-result" wx:if="{{!isEmpty}}">
      <view class="exam-result__rank" wx:if="{{resultDisplay !== 0}}">
        <text class="exam-result__rank-title">答对{{correctNum}}道题，正确率{{correctRate}}%</text>
        <text class="exam-result__rank-desc">超过{{joinRate}}%参与者</text>
      </view>

      <view class="exam-result__desc">
        <text class="exam-result__desc-title">{{resultTitle}}</text>
        <text class="exam-result__desc-desc" wx:if="{{resultType === 1}}">{{resultDesc}}</text>
        <view
          class="exam-result__desc__pic"
          style="background: url({{resultImg}}) center / cover no-repeat"
          wx:if="{{resultType === 2}}"></view>
      </view>

      <view class="exam-result__share">
        <button
          open-type="share"
          class="exam-result__share__btn exam-result__share__btn-friend">
          分享好友
        </button>
        <user-authorize
          authTypeList="{{ ['nicknameAndAvatar'] }}"
          class="exam-result__share__btn exam-result__share__btn-pic"
          bind:next="generateCard">
          生成长图分享
        </user-authorize>
      </view>

      <view wx:if="{{hasCoupon && takeCouponCount}}" class="exam-coupon">
        <text class="exam-coupon__title">恭喜，你已获得{{takeCouponCount}}张优惠券奖励</text>
        <view class="exam-coupon__list">
          <view
            class="exam-coupon__list__item"
            bindtap="toFitGoods"
          >
            <view
              class="exam-coupon__list__item-num"
              wx:if="{{takeCouponCount > 0}}"
            >
              x{{takeCouponCount}}
            </view>
            <view class="exam-coupon__list__item__price" wx:if="{{discount !== 0}}">
              <text class="exam-coupon__list__item__price--main">{{discount / 10}}折</text>
            </view>
            <view class="exam-coupon__list__item__price" wx:elif="{{valueRandomTo !== 0}}">
              <view class="exam-coupon__list__item__price--low">¥{{randomLow}}</view>
              <view class="exam-coupon__list__item__price--line">~</view>
              <view class="exam-coupon__list__item__price--high">¥{{randomHigh}}</view>
            </view>
            <view class="exam-coupon__list__item__price" wx:else>
              ¥ <text class="exam-coupon__list__item__fitst-price">{{mainNum}}</text>.<text>{{miniNum}}</text>
            </view>
            <view class="exam-coupon__list__item__desc">
              <view class="exam-coupon__list__item__desc-type">{{couponTitle}}</view>
              <view class="exam-coupon__list__item__desc-use">{{couponDesc}}</view>
              <view class="exam-coupon__list__item__desc-use">{{couponStartAt}}-{{couponEndAt}}</view>
            </view>
          </view>
        </view>
      </view>

      <van-popup show="{{ cardShareDisplay }}" custom-class="share-card-container-pop">
        <view class="share-card-container">
          <view class="share-card-container__close" bind:tap="handleCloseShareCard">
          </view>
          <image
            wx:if="{{shareCardUrl}}"
            class="share-card-container__card"
            src="{{shareCardUrl}}"
            bind:longpress="onSaveCard" />
          <image
            wx:else
            class="share-card-container__loading"
            src="https://b.yzcdn.cn/v2/image/loader.gif" />
        </view>
      </van-popup>

      <canvas class="canvas-placeholder" canvas-id="exam_share" />

    </view>
    <!-- no-data -->
    <empty wx:else text="暂无测试结果数据！" />
  </block>

  <view class="loading-container" wx:else>
    <van-loading
      type="spinner"
      class="detail-loading"
      color="white"/>
  </view>
</page-container>
