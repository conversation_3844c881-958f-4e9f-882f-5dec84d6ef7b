// import { format } from 'utils/time-utils';
import { format } from '@youzan/weapp-utils/lib/time-utils';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import { fetchUltraCode } from '@/helpers/fetch';
import authorize from '@/helpers/authorize';
import WscPage from 'pages/common/wsc-page/index.js';

import * as canvasUtils from '../../canvas';
import { getExamRecordApi, getShareApi } from '../api';
import { DEFAULT_SHARE_BG, DEFAULT_SHARE_ACATAR, DEFAULT_SHARE_NICKNAME } from './constant';

const app = getApp();

const { chainStoreInfo = {} } = app.getShopInfoSync();
const { isRootShop } = chainStoreInfo;

const currentPage = {
  data: {
    identity: '',
    examId: 0,
    wxShareData: '',
    wxShareDisplay: false,
    cardShareDisplay: false,

    correctNum: 0,
    correctRate: 0,
    joinRate: 0,
    resultDisplay: 1,
    resultTitle: '',
    resultDesc: '',
    resultImg: '',
    resultType: 1,

    coupon: {},
    takeCouponCount: 0,
    discount: 0,
    valueRandomTo: 0,
    randomLow: 0,
    randomHigh: 0,
    mainNum: 0,
    miniNum: 0,
    couponTitle: '',
    couponDesc: '',
    couponStartAt: '',
    couponEndAt: '',
    hasCoupon: false,

    // 分享数据
    shareInfo: {},
    shareType: 2,
    // 生成小程序码所用
    shareUrl: '',
    // 生成公众号码所用（base64）
    sharePublicUrl: '',

    // 结果详情数据
    resultInfo: {},
    // 结果数据状态
    isLoading: true,

    // 绘制分享卡片状态
    showCardDisplay: false,

    // 用户信息
    userInfo: {},

    // 绘制好的分享卡片
    shareCardUrl: '',
  },

  onLoad(query) {
    const examId = +query.examId || 0;
    const identity = query.identity || '';

    const shareUrl = isRootShop && identity ? `packages/paidcontent/exam/exam-detail/index?alias=${examId}&identity=${identity}` : `packages/paidcontent/exam/exam-detail/index?alias=${examId}`;
    const userInfo = app.globalData.userInfo || {};

    this.setYZData({
      shareUrl,
      userInfo,
      examId,
      identity
    });

    getExamRecordApi({
      examId,
      identity
    })
      .then(res => {
        console.log('getExamRecordApi:', res);
        if (res) {
          this.parseResToView(res);
          const { examId } = this.data;

          return getShareApi({
            examId,
            identity,
            sourceType: 1
          });
        }
      })
      .then(res => {
        console.log('getShareApi', res);
        const resData = res;

        if (resData) {
          this.setYZData({
            shareInfo: resData.examDTO || {},
            shareType: resData.shareType || 2,
            sharePublicUrl: resData.shareUrl || ''
          });
        }
      })
      .catch(e => {
        console.log(e.msg);
      });
  },

  toFitGoods() {
    const coupon = this.data.coupon || {};
    const joinGoodsList = coupon.joinGoodsList || [];
    const couponAlias = coupon.alias || '';
    console.log('result:joinGoodsList', joinGoodsList);
    if (joinGoodsList.length > 0) {
      wx.navigateTo({
        url: `/packages/shop/goods/group/index?pageType=coupon&alias=${couponAlias}`,
      });
    } else {
      wx.reLaunch({
        url: '/packages/home/<USER>/index'
      });
    }
  },

  // 将数据解析成 view 所需的结构
  parseResToView(res) {
    const {
      recordDetail = {}, percentRank, result = {}, takeCouponCount
    } = res;

    // 答题情况
    const correctNum = recordDetail.correctNum;
    const correctRate = Math.floor(recordDetail.percentNum * 100);
    const joinRate = Math.floor(percentRank * 100);

    // 结果详情
    const resultDisplay = result.display;
    const resultTitle = result.title;
    const resultDesc = result.description;
    const resultImg = result.descPic.url;

    const resultType = result.style;

    // 优惠券信息
    const coupon = result.coupon || {};
    const discount = coupon.discount || 0;
    const condition = coupon.condition || 0;
    const valueRandomTo = coupon.valueRandomTo || 0;
    const randomLow = (coupon.denominations / 100).toFixed(2) || '';
    const randomHigh = (coupon.valueRandomTo / 100).toFixed(2) || '';
    const mainNum = (coupon.denominations / 100).toFixed(2).split('.')[0] || '';
    const miniNum = (coupon.denominations / 100).toFixed(2).split('.')[1] || '';
    const couponTitle = coupon.title || '';
    const couponDesc = condition !== 0 ? `满${(condition / 100).toFixed(2)}可用` : '无门槛';
    const couponStartAt = format(new Date(coupon.validStartTime), 'yyyy.MM.dd') || '';
    const couponEndAt = format(new Date(coupon.validEndTime), 'yyyy.MM.dd') || '';

    this.setYZData({
      resultInfo: res,
      isLoading: false,
      correctNum,
      correctRate,
      joinRate,
      resultDisplay,
      resultTitle,
      resultDesc,
      resultImg,
      resultType,
      takeCouponCount,
      coupon,
      discount,
      valueRandomTo,
      randomLow,
      randomHigh,
      mainNum,
      miniNum,
      couponTitle,
      couponDesc,
      couponStartAt,
      couponEndAt,
      hasCoupon: !isEmpty(coupon)
    });
  },

  onShareAppMessage() {
    const { shareInfo, shareUrl } = this.data;
    console.log('examDTO:', shareInfo);
    const coverPic = shareInfo.coverPic || {};
    console.log('title:', shareInfo.title);
    return {
      title: `我刚参与了${shareInfo.title}的测试，快来一起测下吧`,
      path: shareUrl,
      imageUrl: coverPic.url || ''
    };
  },

  handleCloseShareCard() {
    this.setYZData({
      cardShareDisplay: false
    });
  },

  generateCard(e) {
    this.setYZData({
      cardShareDisplay: true,
      userInfo: {
        avatarUrl: e.detail.avatar,
        nickName: e.detail.nickname,
      }
    });
    this.initDrawInfo();
  },

  initDrawInfo() {
    const {
      userInfo,
      shareType,
      sharePublicUrl,
      shareInfo,
      correctRate,
      joinRate,
      resultTitle,
      resultType,
      resultDesc,
      resultImg,
      shareCardUrl,
      resultInfo = {}
    } = this.data;

    if (shareCardUrl) {
      return;
    }
    const drawInfos = {
      shareType,
      sharePublicUrl,
      avatarSrc: userInfo.avatarUrl || DEFAULT_SHARE_ACATAR,
      nickName: userInfo.nickName || DEFAULT_SHARE_NICKNAME,
      title: shareInfo.title,
      correctRate,
      joinRate,
      resultTitle,
      resultType,
      resultDesc,
      resultImg,
      bgSrc: DEFAULT_SHARE_BG,
      qrcode: '',
      resultInfo
    };

    return Promise.all([
      this.uploadAvatar(drawInfos.avatarSrc),
      // 1 先关注公众号https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQHE8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyeDFyNVFzMWJmVzExODdWUjFzMWIAAgQHLA5cAwQAjScA
      // 2 小程序码
      drawInfos.shareType === 2 ? this.getQrcode() : this.getPublicCode(drawInfos.sharePublicUrl)
    ])
      .then(([cdnAvatarSrc, qrcode]) => {
        console.log('获取二维码&头像地址', qrcode, cdnAvatarSrc);
        drawInfos.qrcode = qrcode;
        drawInfos.avatarSrc = cdnAvatarSrc;
        return this.drawCard(drawInfos);
      });
  },

  drawCard(drawInfos) {
    const result = drawInfos.resultInfo.result || {};
    const hasResult = !!result.display;
    const resultType = drawInfos.resultType;
    new Promise(() => {
      const imgList = [
        canvasUtils.wxGetImageInfo(drawInfos.bgSrc),
        drawInfos.shareType === 2 ? Promise.resolve(drawInfos.qrcode) : canvasUtils.wxGetImageInfo(drawInfos.qrcode),
        canvasUtils.wxGetImageInfo(drawInfos.avatarSrc)
      ];
      (resultType === 2 && (result.descPic || {}).url) && imgList.push(canvasUtils.wxGetImageInfo(result.descPic.url));
      Promise.all(imgList)
        .then(([bg, qrcode, avatar, resultImg = {}]) => {
          console.log('getImageInfo', bg, qrcode, avatar);
          const ctx = wx.createCanvasContext('exam_share');

          const sizeRulesBg = [ctx, bg.path, 0, 0, 310, 552, 8];
          const sizeRulesAvatar = [ctx, 22, 20, 31, avatar.path];
          const sizeRulesNickName = [ctx, drawInfos.nickName, 90, 62, 16, undefined, '#333', undefined, 22];
          const sizeRulesQecode = [ctx, drawInfos.shareType === 2 ? qrcode : qrcode.path, 22, 470, 60, 60];
          const sizeRulesTitle = [ctx, drawInfos.title, 90, 490, 198, 14, undefined, undefined, 20];
          const sizeRulesScanText = [ctx, '长按保存图片', 90, 532, 13, undefined, '#999', undefined, 20];
          const sizeRules = () => {
            const map = {
              10: {
                bg: sizeRulesBg,
                avatar: sizeRulesAvatar,
                nickName: sizeRulesNickName,
                result: [ctx, `我刚完成了【${drawInfos.title}】，正确率为${drawInfos.correctRate}%，超过了${drawInfos.joinRate}%的参与者，一起来测下吧。`, 24, 110, 264, 13, undefined, undefined, 18],
                resultTitle: [ctx, drawInfos.resultTitle, 22, 162, 17, undefined, '#333', undefined, 25],
                resultImg: [ctx, resultImg.path, 22, 177, 266, 266, 8],
                line: [ctx, 22, 456, 288, 456, 1, undefined],
                qrcode: sizeRulesQecode,
                title: sizeRulesTitle,
                scanText: sizeRulesScanText,
              }, // 展示结果 结果图片
              11: {
                bg: sizeRulesBg,
                avatar: sizeRulesAvatar,
                nickName: sizeRulesNickName,
                result: [ctx, `我刚完成了【${drawInfos.title}】，正确率为${drawInfos.correctRate}%，超过了${drawInfos.joinRate}%的参与者，一起来测下吧。`, 24, 110, 264, 13, undefined, undefined, 18],
                resultTitle: [ctx, drawInfos.resultTitle, 22, 175, 17, undefined, '#333', undefined, 25],
                resultDesc: [ctx, drawInfos.resultDesc, 22, 208, 264, 13, 'serif', '#666', 18],
                line: [ctx, 22, 456, 288, 456, 1, undefined],
                qrcode: sizeRulesQecode,
                title: sizeRulesTitle,
                scanText: sizeRulesScanText,
              }, // 展示结果 结果文字
              '00': {
                bg: sizeRulesBg,
                avatar: sizeRulesAvatar,
                nickName: sizeRulesNickName,
                resultTitle: [ctx, drawInfos.resultTitle, 22, 137, 17, undefined, '#333', undefined, 25],
                resultImg: [ctx, resultImg.path, 22, 152, 266, 266, 8],
                line: [ctx, 22, 456, 288, 456, 1, undefined],
                qrcode: sizeRulesQecode,
                title: sizeRulesTitle,
                scanText: sizeRulesScanText,
              }, // 不展示结果 结果图片
              '01': {
                bg: sizeRulesBg,
                avatar: sizeRulesAvatar,
                nickName: sizeRulesNickName,
                resultTitle: [ctx, drawInfos.resultTitle, 22, 137, 17, undefined, '#333', undefined, 25],
                resultDesc: [ctx, drawInfos.resultDesc, 22, 170, 264, 13, 'serif', '#666', 18],
                line: [ctx, 22, 456, 288, 456, 1, undefined],
                qrcode: sizeRulesQecode,
                title: sizeRulesTitle,
                scanText: sizeRulesScanText,
              }, // 不展示结果 结果文字
            };
            const showResult = hasResult ? 1 : 0; // 是否展示结果
            const resultDesc = resultType === 1 ? 1 : 0; // 有结果详情文字

            return map[`${showResult}${resultDesc}`];
          };
          console.log(sizeRules());
          // 绘制背景图
          canvasUtils.drawImage(...sizeRules().bg);

          // 绘制头像
          canvasUtils.drawAvatarImg2(...sizeRules().avatar);

          // 绘制用户名
          canvasUtils.drawTextLine(...sizeRules().nickName);

          // 绘制测试情况
          hasResult && canvasUtils.drawParagraph(...sizeRules().result);

          // 绘制结果标题
          canvasUtils.drawTextLine(...sizeRules().resultTitle);

          // 绘制结果
          resultType === 1 ? canvasUtils.drawParagraph(...sizeRules().resultDesc) : canvasUtils.drawImage(...sizeRules().resultImg);

          // 绘制底部分界线
          canvasUtils.drawLine(false)(...sizeRules().line);

          // 绘制二维码
          canvasUtils.drawImage(...sizeRules().qrcode);

          // 绘制标题
          canvasUtils.drawParagraph(...sizeRules().title);
          canvasUtils.drawTextLine(...sizeRules().scanText);

          ctx.draw(false, () => {
            console.log('ctx.draw');
            wx.canvasToTempFilePath({
              canvasId: 'exam_share',
              destWidth: 310 * 3,
              destHeight: 552 * 3,
              success: (res) => {
                console.log('绘制好的卡片', res.tempFilePath);
                this.setYZData({
                  shareCardUrl: res.tempFilePath,
                });
              },
              fail(err) {
                console.log('绘制失败', err);
              }
            });
          });
        });
    })
      .catch((err) => {
        console.warn(err);
        wx.showToast({
          title: '生成图片失败，请稍后再试',
          icon: 'none'
        });
      });
  },

  // 上传头像
  uploadAvatar(avatar) {
    return new Promise((resolve) => {
      canvasUtils.uploadMetiarial(avatar).then(res => {
        resolve(res);
      });
    });
  },

  // 长按保存图片到相册
  onSaveCard() {
    const inviteCardSrc = this.data.shareCardUrl;
    if (!inviteCardSrc) return;
    authorize('scope.writePhotosAlbum')
      .then(() => {
        return new Promise((resolve, reject) => {
          wx.saveImageToPhotosAlbum({
            filePath: inviteCardSrc,
            success: resolve,
            fail: reject
          });
        });
      })
      .then(() => {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      })
      .catch(() => {
        wx.showToast({
          title: '保存失败，请检查保存到相册的权限',
          icon: 'none'
        });
      });
  },

  // 获取二维码
  getQrcode() {
    const { shareUrl } = this.data;
    return new Promise((resolve) => {
      fetchUltraCode(shareUrl, {})
        .then(qrSrc => {
          resolve(qrSrc);
        });
    })
      .catch(err => {
        console.log('小程序码获取失败', err);
        wx.showToast({
          title: '小程序码获取失败',
          icon: 'none'
        });
      });
  },

  // 获取公众号二维码(base64 转download File可用的cdn域名)
  getPublicCode(base64Str) {
    console.log('dada-----------', base64Str);
    const app = getApp();
    const logger = app.logger;
    return new Promise((resolve, reject) => {
      app.carmen({
        api: 'youzan.shop.weapp.codeimage/1.0.0/convert',
        method: 'POST',
        data: {
          mimeType: 'image/png',
          base64Str: `base64,${base64Str}`
        },
        success: (res) => {
          let url = res.attachment_url || '';
          if (typeof url === 'string') {
            url = url.replace(/^http:/, 'https:');
          }
          resolve(url);
        },
        fail: (err) => {
          logger.requestError({
            alert: 'warn',
            message: '公众号二维码转换失败',
            response: `err: ${JSON.stringify(err)}`,
            request: {
              options: { base64Str }
            }
          });
          reject('公众号二维码地址获取失败');
        }
      });
    })
      .catch(err => {
        console.log('公众号二维码地址获取失败', err);
        wx.showToast({
          title: '公众号二维码地址获取失败',
          icon: 'none'
        });
      });
  }
};

const pageConfig = Object.assign({}, currentPage);

WscPage(pageConfig);
