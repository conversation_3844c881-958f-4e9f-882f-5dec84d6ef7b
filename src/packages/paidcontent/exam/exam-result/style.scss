@use "sass:math";

$c-black: #333;
$c-white: #fff;
$c-green: #00b389;
$scale: 3;

.loading-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
}

.detail-loading {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.exam-result {
  padding: 25px 20px;
  background-color: $c-white;

  &__rank {
    text-align: center;
  }

  &__rank-title {
    color: $c-black;
    font-size: 18px;
    line-height: 25px;
    margin-bottom: 10px;
    display: block;
  }

  &__rank-desc {
    color: #999;
    font-size: 15px;
    line-height: 19px;
    margin-bottom: 20px;
    display: block;
  }

  &__desc {
    padding: 40rpx 50rpx;
    box-shadow: 0 0 5px 3px #eee;
    margin-bottom: 40rpx;
    border-radius: 8rpx;

    &__pic {
      width: 400rpx;
      height: 400rpx;
      margin: 0 auto;
    }
  }

  &__desc-title {
    color: $c-black;
    font-size: 18px;
    line-height: 25px;
    margin-bottom: 20px;
    text-align: center;
    display: block;
  }

  &__desc-desc {
    color: #666;
    font-size: 16px;
    line-height: 22px;
    display: block;
  }

  &__share {
    width: 560rpx;
    margin: 0 auto;
    height: 74rpx;

    &__btn {
      width: 252rpx;
      height: 74rpx;
      line-height: 74rpx;
      text-align: center;
      font-size: 15px;
      border-radius: 37rpx;
    }

    &__btn-friend {
      float: left;
      color: $c-green;
      border: 1px solid $c-green;
      background-color: #fff;
    }

    &__btn-pic {
      float: right;
      color: $c-white;
      background-color: $c-green;
    }
  }
}

.exam-coupon {
  margin-top: 10px;
  background-color: #fff;
  padding: 25px 0;

  &__title {
    font-size: 18px;
    color: $c-black;
    line-height: 24px;
    margin-bottom: 20px;
    text-align: center;
    display: block;
  }

  &__list {
    &__item {
      background-color: $c-white;
      border-top: 2px solid #e00;
      height: 100px;
      box-shadow: 0 0 5px 3px #eee;
      margin-bottom: 20px;
      position: relative;

      &-num {
        position: absolute;
        top: 5px;
        left: 0;
        width: 31px;
        height: 18px;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        color: #fff;
        font-size: 14px;
        background: #e00;
        border-top-right-radius: 15px;
        border-bottom-right-radius: 15px;
      }

      &__price {
        width: 50%;
        line-height: 102px;
        float: left;
        text-align: center;

        &--main {
          font-size: 24px;
        }

        &--line {
          line-height: 4px;
          margin: 2px 0;
        }

        &--low {
          line-height: 25px;
          font-size: 18px;
          margin-top: 22px;
        }

        &--high {
          line-height: 25px;
          font-size: 18px;
        }
      }

      &__first-price {
        font-size: 24px;
      }

      &__desc {
        width: 50%;
        float: right;
        padding-top: 16px;
        padding-bottom: 14px;

        &-type {
          color: #4a4a4a;
          font-size: 17px;
          line-height: 24px;
          margin-bottom: 11px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        &-use {
          color: #afafaf;
          font-size: 14px;
          line-height: 18px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }
}

.share-card-container-pop {
  background-color: transparent !important;
  overflow-y: initial !important;
}
.share-card-container {
  width: 620rpx;
  min-height: 1000rpx;
  background-color: #fff;
  border-radius: 8px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  &__close {
    width: 24px;
    height: 24px;
    position: absolute;
    right: -12px;
    top: -12px;
    background: url(https://b.yzcdn.cn/public_files/2018/10/25/4ca27320a73730285191c3e577fe0c7e.png);
    background-repeat: no-repeat;
    background-size: 24px;
  }

  &__card {
    width: 620rpx;
    height: 1104rpx;
  }

  &__loading {
    width: 16px;
    height: 16px;
  }
}
.canvas-placeholder {
  position: fixed;
  top: -1000px * $scale;
  width: 310px * $scale;
  height: 552px * $scale;
  transform: scale(math.div(1, $scale));
}

.no-data {
  padding: 85px 0;
  font-size: 14px;
  color: #999;
  text-align: center;
  background: #fff;
}
