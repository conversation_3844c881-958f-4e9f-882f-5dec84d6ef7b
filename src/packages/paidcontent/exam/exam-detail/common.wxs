const getImg = (obj = {}) => {
  const { url, width, height } = obj;
  return {
    url,
    width,
    height
  };
};

const getImgUrl = (obj) => {
  return getImg(obj).url;
};

const getimgBoxSize = (obj) => {
  const WIDTH = 750;
  const img = getImg(obj);
  const width = img.width;
  const height = img.height;
  console.log(width, height);
  if (width <= 750) {
    return {
      width,
      height,
    };
  }
  const changedHeight = parseInt(height * WIDTH / width, 10);
  return {
    width: WIDTH,
    height: changedHeight
  };
};

module.exports = {
  getImg,
  getImgUrl,
  getimgBoxSize
};
