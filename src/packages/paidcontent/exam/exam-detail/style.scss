.exam-detail {
  height: 100%;

  &__division {
    width: 100%;
    height: 20rpx;
    background-color: #f2f2f2;
  }

  &__footer-wrap {
    width: 100%;
    height: 146rpx;
    display: flex;
    flex-direction: row;
    justify-content: center;
    // background: linear-gradient(to top, rgba(255,255,255,1), rgba(255,255,255,0));
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    padding-top: 10rpx;
    box-shadow: 0 0 18px 18px rgba(255, 255, 255, 1);

    .footer-wrap {
      &__button {
        width: 360rpx;
        background-color: #00b389;
        border-color: #00b389;

        &--disable {
          background-color: #e5e5e5;
          border-color: #e5e5e5;
          opacity: 1;
        }
      }
    }
  }
}

.loading-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, .5);
}

.detail-loading {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}