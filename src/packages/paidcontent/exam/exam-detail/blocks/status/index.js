import WscComponent from 'pages/common/wsc-component/index';
import { countDownRemainParse } from '@youzan/weapp-utils/lib/time';
import { getCurrentPageParams } from '../../../../utils';

WscComponent({
  properties: {
    startTime: {
      type: Number,
      value: 0,
    },
    endTime: {
      type: Number,
      value: 0,
    },
    currentTime: {
      type: Number,
      value: 0,
    },
    status: {
      type: Number,
      value: 0,
      observer(newVal) {
        if (+newVal === 2) {
          this.startCount();
        }
        if ([1, 2, 4, 5].indexOf(+newVal) > -1) {
          this.setYZData({
            show: true
          });
        } else {
          this.setYZData({
            show: false
          });
        }
      }
    }
  },
  data: {
    remainTime: '',
    remainTimer: null,
    currentTimeforData: 0,
    show: false,
  },
  attached() {
  },
  methods: {
    startCount() {
      this.setYZData({
        remainTime: countDownRemainParse((this.data.startTime - this.data.currentTime) || 0).toString(),
        currentTimeforData: this.data.currentTime
      });
      this.walkCount();
    },
    walkCount() {
      clearTimeout(this.remainTimer);
      this.remainTimer = setTimeout(() => {
        const diff = this.countFn();
        if (diff > 0) {
          this.walkCount();
        } else {
          this.finishCount();
        }
      }, 1000);
    },
    countFn() {
      this.data.currentTimeforData += 1000;
      const diff = this.data.startTime - this.data.currentTimeforData;
      if (diff >= 0) {
        const diffStr = countDownRemainParse(diff).toString();
        this.setYZData({
          remainTime: diffStr
        });
      }
      return diff;
    },

    finishCount() {
      this.setYZData({
        show: false
      });
      this.triggerEvent('finishcount', {
        isGoing: true
      });
    },

    goToResult() {
      const query = getCurrentPageParams() || {};
      console.log('query:alias--------------', query.alias);
      wx.navigateTo({
        url: `/packages/paidcontent/exam/exam-result/index?examId=${query.alias || 0}`,
      });
    }
  }
});
