<view class="exam-status" wx:if="{{show}}">
  <block>
    <block wx:if="{{status === 2}}">
      <text class="exam-status__text">距开始：{{remainTime}}</text>
    </block>
    <block wx:if="{{status === 4}}">
      <text class="exam-status__text">已结束</text>
    </block>
    <view wx:if="{{status === 1}}" class="exam-status--joined">
      <text class="exam-status__text">你已参与过</text>
      <text bind:tap="goToResult" class="exam-status__text exam-status__result">查看测试结果</text>
    </view>
    <view wx:if="{{status === 5}}">
      <text class="exam-status__text exam-status__result">预览模式</text>
    </view>
  </block>
</view>