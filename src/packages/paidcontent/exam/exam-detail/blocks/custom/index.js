import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  properties: {
    info: {
      type: Object,
      value: {},
    }
  },
  data: {
    custBgWidth: 0,
    customBgHeight: 0,
    custStartBtnWidth: 0,
    custStartBtnHeight: 0
  },
  attached() {
    this.handleBg();
    this.handleStartBtn();
  },
  methods: {
    // 处理自定义背景图
    handleBg() {
      const { windowWidth, windowHeight } = app.getSystemInfoSync();
      this.setYZData({
        custBgWidth: windowWidth,
        customBgHeight: windowHeight
      });
    },

    // 处理自定义开始图片
    handleStartBtn() {
      const { windowWidth } = app.getSystemInfoSync();
      const startBtn = this.data.info.startMenuPic || {};
      const btnWidth = startBtn.width;
      const btnHeight = startBtn.height;
      const maxWidth = windowWidth - 20;
      if (btnWidth > maxWidth) {
        this.setYZData({
          custStartBtnWidth: maxWidth,
          custStartBtnHeight: maxWidth / btnWidth * btnHeight
        });
      } else {
        this.setYZData({
          custStartBtnWidth: btnWidth,
          custStartBtnHeight: btnHeight
        });
      }
    }
  }
});
