import WscPage from 'pages/common/wsc-page/index.js';

import { getExamDetailApi, joinExamApi } from '../api';

const currentPage = {
  data: {
    isLoading: true, // 请求loading
    isEmpty: false, // 页面有无渲染数据
    startLoading: false, // 按钮 loading
    examId: 0,
    identity: '',
    examStatus: 0, // 测试状态
    detailData: {},
    isForbid: true,
    examStyle: 1, // 1固定模板 2自定义
    currentTime: 0,
    startTime: 0,
    endTime: 0
  },

  onLoad(query) {
    const examId = +query.alias || 0;
    const identity = query.identity || '';
    this.setYZData({
      examId,
      identity
    });
    this.getExamDetail();
  },

  // 获取测试详情
  getExamDetail() {
    getExamDetailApi({
      id: this.data.examId,
      identity: this.data.identity
    })
      .then(res => {
        this.setYZData({ isLoading: false });

        const data = res || {};
        const exam = data.exam || {};

        const changedData = {
          detailData: exam,
          examStyle: exam.style,
          currentTime: data.currentTime,
          startTime: exam.startAt,
          endTime: exam.endAt,
          examStatus: data.customerExamStatus
        };

        if (data.customerExamStatus === 3) {
          changedData.isForbid = false;
        }

        // 预览状态
        if (data.customerExamStatus === 5) {
          changedData.isForbid = false;
        }

        this.setYZData(changedData);
      })
      .catch(e => {
        this.setYZData({
          isLoading: false,
          isEmpty: true
        });
        wx.showToast({
          title: e.msg,
          icon: 'none'
        });
      });
  },

  // 倒计时结束
  finishCount(e) {
    console.log('倒计时结束');
    // 刷新状态
    const ev = e.detail || {};
    ev.isGoing ? this.setYZData({ isForbid: false }) : undefined;
  },

  // 开始测试
  startExam() {
    console.log('开始测试');
    const examId = this.data.examId;
    const identity = this.data.identity;
    if (!this.data.isForbid) {
      joinExamApi({
        examId,
        identity
      })
        .then(() => {
          wx.navigateTo({
            url: `/packages/paidcontent/exam/exam-question/index?currentIndex=0&examId=${examId}&identity=${identity}`,
          });
        })
        .catch(e => {
          console.log(e.msg);
          wx.showToast({
            title: e.msg,
            icon: 'none'
          });
        });
    }
  }
};

const pageConfig = Object.assign({}, currentPage);

WscPage(pageConfig);
