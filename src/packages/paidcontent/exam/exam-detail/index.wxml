<view class="exam-detail" wx:if="{{!isLoading}}">
  <block wx:if="{{!isEmpty}}">
    <exam-status start-time="{{startTime}}" end-time="{{endTime}}" current-time="{{currentTime}}" status="{{examStatus}}" bind:finishcount="finishCount"></exam-status>

    <block wx:if="{{examStyle === 1}}">
      <exam-header info="{{detailData}}"></exam-header>

      <view class="exam-detail__division"></view>

      <exam-detail info="{{detailData}}"></exam-detail>

      <view class="exam-detail__footer-wrap">
        <van-button type="primary" disabled="{{isForbid}}" round loading="{{startLoading}}" custom-class="footer-wrap__button {{isForbid ? 'footer-wrap__button--disable' : ''}}" bind:tap="startExam">开始测试</van-button>
      </view>
    </block>
    <exam-custom wx:else info="{{detailData}}" bind:tap="startExam"></exam-custom>
  </block>
  <empty wx:else text="暂无测试信息！" />
</view>
<view class="loading-container" wx:else>
  <van-loading
    type="spinner"
    class="detail-loading"
    color="white"/>
</view>