export default {
  BGLIST: [
    {
      type: 'fix',
      poster: 'https://img.yzcdn.cn/paidcontent/sharecard/bg1.jpg',
      color: 'fff'
    },
    {
      type: 'fix',
      poster: 'https://img.yzcdn.cn/paidcontent/sharecard/bg2.jpg',
      color: 'fff'
    },
    {
      type: 'fix',
      poster: 'https://img.yzcdn.cn/paidcontent/sharecard/bg4.jpg',
      color: '000'
    },
    {
      type: 'fix',
      poster: 'https://img.yzcdn.cn/paidcontent/sharecard/bg3.jpg',
      color: '000'
    }
    // 3 4命名错位
  ],

  BGICONLIST: [
    {
      type: 'fix',
      poster: 'https://img.yzcdn.cn/paidcontent/sharecard/bgicon1.jpg'
    },
    {
      type: 'fix',
      poster: 'https://img.yzcdn.cn/paidcontent/sharecard/bgicon2.jpg'
    },
    {
      type: 'fix',
      poster: 'https://img.yzcdn.cn/paidcontent/sharecard/bgicon3.jpg'
    },
    {
      type: 'fix',
      poster: 'https://img.yzcdn.cn/paidcontent/sharecard/bgicon4.jpg'
    }
  ],

  // 图片内文字颜色定义
  BGCOLORLIST: [
    '#fff',
    '#fff',
    '#000',
    '#000'
  ],

  DISTRIBUTORSTATUS: {
    error: 500,
    checking: -1,
    pass: 1,
    unpass: 2
  }
};
