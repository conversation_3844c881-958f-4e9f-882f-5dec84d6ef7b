<import src="/components/zan-account/bind-phone-number/index.wxml"/>

<view>
  <canvas class="invite-canvas" canvas-id="inviteCard" />
  <view class="invite-img">
    <view class="invite-img__preview">
      <view class="invite-img__image">
        <image
          src="{{ genImageList[currentBgIndex] || bgList[currentBgIndex].poster }}"
          bindtap="onSaveCard"
        />
      </view>
      <view class="invite-img__hint">
        点击上方图片保存
      </view>
    </view>

    <scroll-view wx:if="{{ bgList.length > 4}}" class="invite-img__scroll-area" scroll-x>
      <image-picker
        image-list="{{ bgIconList }}"
        bind:change="onChangeImage"
        customStyle="width: {{ bgList.length * 90}}px"
        >
      </image-picker>
    </scroll-view>
    <view wx:else class="invite-img__action-area">
      <image-picker
        image-list="{{ bgIconList }}"
        bind:change="onChangeImage">
      </image-picker>
    </view>
    <view
      wx:if="{{ isShowNotice }}"
      class="invite-img__notice"
      bind:tap="onClickNotice">
      <i></i>
      <text>邀请卡须知</text>
    </view>
    <view wx:else class="invite-img__notice"></view>
    <view
      class="invite-img__float-area"
      bind:tap="onClickOtherShare">
      <image src="https://b.yzcdn.cn/paidcontent/sharecard/linkIcon.png"/>
      <button class="invite-img__share-btn" open-type="share"></button>
    </view>

  </view>
</view>

<!-- 协议 -->
<inject-protocol noAutoAuth>

<account-login
  wx:if="{{ accountLogin }}"
  show="{{ accountLogin }}"
  bind:loginSuccess="onAccountSuccess"
  bind:closeAccountLogin="onAccountClose"
></account-login>
