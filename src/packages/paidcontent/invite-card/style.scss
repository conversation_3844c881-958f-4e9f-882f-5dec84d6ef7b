@use "sass:math";

$scale: 3;

.invite-img {
  background: #e5e5e5;
  height: 98vh;
  display: flex;
  flex-direction: column;

  &__scroll-area {
    height: 90px;
  }

  &__preview {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  &__hint {
    font-size: 13px;
    color: #333;
    margin-top: 18px;
  }

  &__image {
    image {
      width: 50vw;
      height: 90vw;
    }
  }

  &__notice {
    text-align: center;
    height: 20px;
    background-color: #fff;
    line-height: 20px;

    text {
      font-size: 12px;
      color: #787878;
    }

    i {
      display: inline-block;
      width: 11px;
      height: 11px;
      margin-right: 5px;
      background-size: contain;
      background-repeat: no-repeat;
      background-image: url(https://b.yzcdn.cn/paidcontent/sharecard/noticeIcon.png);
    }
  }

  &__float-area {
    position: fixed;
    right: 0;
    bottom: 120px;
    border: none;
    padding-right: 0;

    image {
      height: 40px;
      width: 52px;
    }
  }

  &__share-btn {
    opacity: 0;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
}

.invite-canvas {
  position: fixed;
  top: -1000px * $scale;
  width: 375px * $scale;
  height: 667px * $scale;
  transform: scale(math.div(1, $scale));
}
