import logv3 from 'utils/log/logv3';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import authorize from '@/helpers/authorize';
import WscPage from '../../../pages/common/wsc-page/index';
import constants from './constants';
import API from '../api';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';

const app = getApp();
const { BGLIST, BGICONLIST, DISTRIBUTORSTATUS } = constants;

WscPage({
  data: {
    bgList: [],
    bgIconList: [],
    currentBgIndex: 0,
    genImageList: [],
    drawInfos: {},
    shareInfo: {},
    fxInfo: {},
    userInfo: {},
    redirectH5: '',
    // 分销标识
    sls: '',
    // 登录弹窗标志位
    accountLogin: false,
  },

  onLoad(options) {
    // 处理webview传递的query
    const fxInfo = {
      distributionMoney: options.distributionMoney,
      // 是否需要注册分销员
      isDistribution: options.isDistribution,
    };
    const userInfo = {
      avatarUrl:
        options.avatarUrl ||
        'https://img.yzcdn.cn/public_files/2017/10/23/1321da81aa84d0539b0d5af73fbbf53b.png',
      nickName: options.nickName,
    };
    const shareInfo = {
      title: options.title,
      author: options.author,
      alias: options.alias,
    };

    // 是否需要注册成为分销员
    const isDistribution = +fxInfo.isDistribution === 1;
    const isShowNotice = isDistribution && +fxInfo.distributionMoney > 0;
    this.setYZData({
      fxInfo,
      userInfo,
      shareInfo,
      redirectH5: options.redirectH5,
      isDistribution,
      isShowNotice,
    });

    /**
     * 账号绑定检查(如果需要注册分销员并且没有绑定过手机号)
     */
    if (isDistribution && !app.getBuyerId()) {
      this.setYZData({
        accountLogin: true,
      });
      return;
    }

    wx.showToast({
      title: '初始化中...',
      duration: 5000,
      icon: 'none',
    });

    /**
     * changelog
     * 2018/9/27 chenzihao:
     * 添加获取海报的逻辑
     */
    this._getPosterUri().then((posters) => {
      const userDefinedPosters = [];
      (posters || []).forEach((item) => {
        userDefinedPosters.push({ type: 'dynamic', poster: item });
      });
      this.setYZData({
        bgList: userDefinedPosters.concat(BGLIST),
        bgIconList: userDefinedPosters.concat(BGICONLIST),
      });
      if (isDistribution) {
        this._prepareRegister();
      } else {
        this._fetchFxShareUrl();
      }
    });
  },

  onShareAppMessage() {
    // 拼接小程序码链接
    const shareUrl = `pages/common/blank-page/index?weappSharePath=packages%2Fedu%2Fwebview%2Findex%3FtargetUrl%3D${encodeURIComponent(
      this.data.redirectH5
    )}`;
    return logv3.processShareData({
      title: this.data.shareInfo.title,
      path: shareUrl,
      imageUrl: this.data.genImageList[this.data.currentBgIndex],
    });
  },

  // 获取商品信息
  _fetchProduct() {
    const prodAlias = this.data.shareInfo.alias;
    if (!prodAlias) return;

    return API.getDistributorDetail({ alias: prodAlias }).then((content) => {
      if (content.type === 6) {
        // 处理线下课
        const { offlineCourse = {} } = content;
        const { minPrice = 0, maxPrice = 0, pictures, title } = offlineCourse;
        let price = '';
        if (minPrice === maxPrice) {
          price = `￥${this._formatPrice(minPrice)}`;
        } else {
          price = `￥${this._formatPrice(minPrice)}起`;
        }
        this.setYZData({
          drawInfos: {
            ...this.data.drawInfos,
            price,
            title,
            cover: pictures[0],
            owlType: 10,
          },
        });
      } else {
        // 处理线上课
        const { onlineCourse = {} } = content;
        const { price = 0, cover, title } = onlineCourse;
        this.setYZData({
          drawInfos: {
            ...this.data.drawInfos,
            price: `￥${this._formatPrice(price)}`,
            title,
            cover,
          },
        });
      }
      this.makeCard();
    });
  },

  // 处理价格信息
  _formatPrice(price) {
    return price % 100 === 0
      ? Math.round(price / 100)
      : (price / 100).toFixed(2);
  },

  _initDrawInfos() {
    const drawInfos = {
      bgSrc: cdnImage(
        this.data.bgList[this.data.currentBgIndex].poster,
        '!large.webp'
      ),
      type: this.data.bgList[this.data.currentBgIndex].type,
      page: '%2Fpackages%2Fedu%2Fwebview%2Findex',
      targetUrl: encodeURIComponent(this.data.redirectH5),
      title: this.data.shareInfo.title || '',
      teacher: this.data.shareInfo.author || '',
      color: this.data.bgList[this.data.currentBgIndex].color || '',
    };

    Object.keys(drawInfos).forEach((key) => {
      if (key === 'title') {
        drawInfos[key] = encodeURIComponent(drawInfos[key]);
      }
    });

    this.setYZData({
      drawInfos,
    });

    this._fetchProduct();
  },

  makeCard(index = 0) {
    if (this.data.genImageList[index]) return;
    wx.showLoading({ title: '正在生成' });

    const drawInfos = {
      ...this.data.drawInfos,
      bgSrc: cdnImage(
        this.data.bgList[this.data.currentBgIndex].poster,
        '!large.webp'
      ),
      type: this.data.bgList[this.data.currentBgIndex].type,
      color: this.data.bgList[this.data.currentBgIndex].color,
    };

    this.setYZData({
      drawInfos,
    });

    app
      .request({
        path: '/wscvis/ump/invite/getInvitePoster.json',
        method: 'GET',
        query: drawInfos,
      })
      .then((res) => {
        app.downloadFile({
          url: res.img,
          success: ({ tempFilePath }) => {
            wx.hideLoading();
            this.data.genImageList[index] = tempFilePath;
            this.setYZData({
              genImageList: this.data.genImageList,
            });
          },
          fail: () => {
            wx.hideLoading();
            this.data.genImageList[index] = res.img;
            this.setYZData({
              genImageList: this.data.genImageList,
            });
          },
        });
      });
  },

  onChangeImage(e) {
    if (this.data.isDistribution && !app.getBuyerId()) {
      this.setYZData({
        accountLogin: true,
      });
      return;
    }
    this.setYZData({
      currentBgIndex: e.detail,
    });
    this.makeCard(e.detail);
  },

  onClickNotice() {
    wx.showModal({
      content: `每邀请1位好友购买成功，将获得￥${(
        this.data.fxInfo.distributionMoney / 100
      ).toFixed(2)}作为奖励。`,
      showCancel: false,
    });
  },

  // 保存图片
  onSaveCard() {
    if (this.data.isDistribution && !app.getBuyerId()) {
      this.setYZData({
        accountLogin: true,
      });
      return;
    }
    const inviteCardSrc = this.data.genImageList[this.data.currentBgIndex];
    if (!inviteCardSrc) return;
    authorize('scope.writePhotosAlbum')
      .then(() => {
        return new Promise((resolve, reject) => {
          if (inviteCardSrc.match(/^wxfile/)) {
            wx.saveImageToPhotosAlbum({
              filePath: inviteCardSrc,
              success: resolve,
              fail: reject,
            });
          } else {
            app.downloadFile({
              url: inviteCardSrc,
              success: ({ statusCode, tempFilePath }) => {
                console.log('statusCode', statusCode);
                if (statusCode === 200) {
                  wx.saveImageToPhotosAlbum({
                    filePath: tempFilePath,
                    success: resolve,
                    fail: reject,
                  });
                } else {
                  reject();
                }
              },
            });
          }
        });
      })
      .then(() => {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
        });
      })
      .catch(() => {
        wx.showToast({
          title: '保存失败，请检查保存到相册的权限',
          icon: 'none',
        });
      });
  },

  onAccountSuccess() {
    this.setYZData({
      accountLogin: false,
    });
    const { fxInfo, userInfo, shareInfo, redirectH5 } = this.data;
    this.onLoad({
      ...fxInfo,
      ...userInfo,
      ...shareInfo,
      redirectH5,
    });
  },

  onAccountClose() {
    this.setYZData({
      accountLogin: false,
    });
  },

  // 静默注册
  _prepareRegister() {
    return new Promise(() => {
      app
        .request({
          path: '/wscvis/knowledge/register.json',
          method: 'POST',
          query: {
            kdtId: app.getKdtId(),
          },
        })
        .then(({ status }) => {
          this._noticeUser(status);
          this._fetchFxShareUrl(status === DISTRIBUTORSTATUS.pass);
        })
        .catch(() => {
          // 注册分销员失败，但是不能影响后续分享
          this._fetchFxShareUrl();
        });
    });
  },

  // 获取分销url
  _fetchFxShareUrl(isDistributor) {
    return new Promise((resolve, reject) => {
      if (!isDistributor) {
        resolve('');
      }
      app.carmen({
        method: 'GET',
        api: 'youzan.salesman.wap.account/1.0.0/get',
        success: (res) => {
          const { sls } = res;
          this.setYZData({
            sls,
          });
          resolve(sls);
        },
        fail: (err) => {
          console.warn('_fetchSalasmanInfo', err);
          reject();
        },
      });
    })
      .then((sls) => {
        let redirectH5 = decodeURIComponent(this.data.redirectH5);

        redirectH5 = sls
          ? addSalesmanParams({ url: redirectH5, sls })
          : redirectH5;
        this.setYZData({
          redirectH5: encodeURIComponent(redirectH5),
        });
        this._initDrawInfos();
      })
      .catch(() => {
        this._initDrawInfos();
      });
  },

  // 根据分销员状态给用户一些提示
  _noticeUser(distributorStatus) {
    let notice = '';
    switch (distributorStatus) {
      case DISTRIBUTORSTATUS.pass:
        return;
      case DISTRIBUTORSTATUS.unpass:
        notice =
          '你没有通过商家分销员审核，在此期间你可以把课程分享给好友但无法获得邀请佣金';
        break;
      case DISTRIBUTORSTATUS.checking:
        notice =
          '商家需要审核你的申请，在此期间你可以把课程分享给好友但无法获得邀请佣金';
        break;
      case DISTRIBUTORSTATUS.error:
        return;
      default:
        return;
    }
    wx.hideToast();
    wx.showModal({
      content: notice,
      showCancel: false,
      confirmText: '我知道了',
    });
  },

  // 获取自定义上传的海报
  _getPosterUri() {
    return new Promise((resolve) => {
      console.log('fetch poster');
      app
        .request({
          path: '/wscvis/knowledge/getFindRelatedImg.json',
          method: 'GET',
          query: {
            alias: this.data.shareInfo.alias,
          },
        })
        .then((data) => resolve(data))
        .catch((err) => {
          // 如果请求出现异常，继续进行下一步
          console.error('[fetch posters]:', err);
          resolve([]);
        });
    });
  },

  // 一次性订阅消息
  onClickOtherShare() {
    // 入参为scene值
    API.showSubMsgPopup('salesmanOrder');
  },
});
