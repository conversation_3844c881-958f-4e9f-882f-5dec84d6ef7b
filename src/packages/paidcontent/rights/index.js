import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  onLoad(query) {
    const alias = query.alias || '';
    const kdtId = query.kdt_id || query.kdtId || app.getKdtId();
    // 直接重定向走
    const redirectUrl = `https://h5.youzan.com/wscvis/knowledge/index?kdt_id=${kdtId}&page=vipbenefit&alias=${alias}`;
    wx.redirectTo({
      url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(redirectUrl)}`
    });
  }
});
