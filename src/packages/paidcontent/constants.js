export const PCT_TYPE = {
  content: 'content',
  column: 'column',
  live: 'live',
  punch: 'punch'
};

// page owlType映射表
export const PAGE_TYPE_MAP = {
  Content: 2,
  Column: 1,
  Live: 4,
  VipBenefit: 3
}

// 内容类型
export const MEDIA_TYPE = {
  IMAGE_TEXT: 1,
  AUDIO: 2,
  VIDEO: 3
};

// 内容类型文案
export const MEDIA_TEXT_MAP = {
  [MEDIA_TYPE.IMAGE_TEXT]: '图文',
  [MEDIA_TYPE.AUDIO]: '音频',
  [MEDIA_TYPE.VIDEO]: '视频'
};

// 邀请卡类型文案
export const INVITE_TEXT_MAP = {
  [MEDIA_TYPE.IMAGE_TEXT]: '邀请你阅读',
  [MEDIA_TYPE.AUDIO]: '邀请你收听',
  [MEDIA_TYPE.VIDEO]: '邀请你观看',
  COLUMN: '邀请你看订阅'
};

// 分享类型文案
export const SHARE_TEXT_MAP = {
  [MEDIA_TYPE.IMAGE_TEXT]: '花钱请你读',
  [MEDIA_TYPE.AUDIO]: '花钱请你听',
  [MEDIA_TYPE.VIDEO]: '花钱请你看',
  COLUMN: '花钱请你看'
};

// 礼物页类型，分享or领取
export const GIFT_TYPE = {
  SHARE_GIFT: 1,
  RECEIVE_GIFT: 2
};

export const RECEIVE_GIFT_STATUS = {
  OVER_CONTENT_COUNT: 1,
  OVER_FRIEND_COUNT: 2,
  PAIED: 3,
  RECEIVED: 4,
  UNRECEIVE: 5,
  RECEIVED_FAIL: 6
};

// 礼物页文案展示
export const GIFT_TEXT_INFO = {
  [RECEIVE_GIFT_STATUS.OVER_CONTENT_COUNT]: {
    title: '不能再领取啦',
    desc: '你已经领取多期TA分享的此专栏内容',
    btnText: '我也要'
  },
  [RECEIVE_GIFT_STATUS.OVER_FRIEND_COUNT]: {
    title: '不能再领取啦',
    desc: '你已经领取多期TA分享的此专栏内容',
    btnText: '我也要'
  },
  [RECEIVE_GIFT_STATUS.PAIED]: {
    title: '你已购买该内容',
    desc: '快去查看吧',
    btnText: '立即查看'
  },
  [RECEIVE_GIFT_STATUS.RECEIVED]: {
    title: '你已领取该内容',
    desc: '快去查看吧',
    btnText: '立即查看'
  },
  [RECEIVE_GIFT_STATUS.UNRECEIVE]: {
    title: '你收到一份礼物',
    desc: '快去领取吧',
    btnText: '免费领取'
  },
  [RECEIVE_GIFT_STATUS.RECEIVED_FAIL]: {
    title: '手慢啦，礼物被抢光了',
    desc: '',
    btnText: '我也要'
  },
  SHARE_GIFT: {
    title: '份礼物已被打包好',
    desc: '快去喊好友来收礼吧',
    btnText: '喊好友来收礼'
  }
};

export const ACTIVITY_TYPE = {
  INVITE_FRIEND: 1,
  PERSENT_GIFT: 3,
  INVITE_CARD: 2,
  COLLECT_ZAN: 4,
  RECOMMEND_POLITE: 6
};

export const RECEIVE_GIFT_RESULT_TEXT = {
  [RECEIVE_GIFT_STATUS.OVER_CONTENT_COUNT]: '领取失败，不能再领取更多TA分享的此专栏内容',
  [RECEIVE_GIFT_STATUS.OVER_FRIEND_COUNT]: '领取失败，不能再领取更多TA分享的此专栏内容',
  [RECEIVE_GIFT_STATUS.PAIED]: '你已购买该内容，可直接观看',
  [RECEIVE_GIFT_STATUS.RECEIVED_FAIL]: '很遗憾，礼物被抢光了'
};

export const DEFAULT_USER_INFO = {
  avatar: 'https://img.yzcdn.cn/public_files/2017/10/23/1321da81aa84d0539b0d5af73fbbf53b.png',
  name: '匿名用户'
};

export const CONTENT_TYPE = {
  COLUMN: 1,
  CONTENT: 2
};

export const CAN_SEND_GIFT_TYPE = {
  YES: 1,
  NO: 0
};

// 与显示顺序一一对应
export const COLLECT_INFO_KEYS = ['contactAddress', 'name', 'mobile', 'weiXin', 'gender'];

// 0元商品领取规则 1：需要领取 2：免领取
export const FREE_GOODS_GET_RULE = {
  NEED_GET: 1,
  NOT_GET: 2
};
