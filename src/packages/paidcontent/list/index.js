// 已购买知识付费专栏和内容列表 & 店铺全部专栏列表 & 店铺全部内容列表  共用
// 当入参有 type 时，为内容列表或专栏列表，否则默认为已购买列表
import logv3 from 'utils/log/logv3';
import WscPage from '../../../pages/common/wsc-page/index';
import listConfig, { getKdtId } from './config';
import { EDU_TAB, LOCAL_MAPPER } from './constant';

const app = getApp();
const TYPE_LIST = ['column', 'content', 'rights', 'live', 'classLive'];
const dangerA = '打';
const dangerB = '卡';

WscPage({
  data: {
    listConfig,
    type: '',
    inited: false,
    fetched: true,
    windowHeight: 0,
    selectedTabIdx: 0,
    tab: EDU_TAB,
    // 账号绑定
    showBindPhoneNumber: false,
    bindTips: '买了课程但没有显示？可能是没有登录',

    loading: false,
    refresh: false,
    noMore: false,
    list: [],

    lists: {
      course: {
        loading: false,
        noMore: false,
        unstartList: [],
        ongoingList: [],
        endList: [],
      },
      column: {
        loading: false,
        noMore: false,
        list: [],
      },
      content: {
        loading: false,
        noMore: false,
        list: [],
      },
      punch: {
        loading: false,
        noMore: false,
        list: [],
      },
      rights: {
        loading: false,
        noMore: false,
        list: [],
      },
      smallClass: {
        loading: false,
        noMore: false,
        list: [],
      },
    },
    themeClass: app.themeClass,
    // 登录弹窗标志位
    accountLogin: false,
    // check是否展示tab
    tabShowStatus: {},
    tabTitle: '',
  },

  onLoad({ type = '' }) {
    // 店铺全部专栏列表 & 全部内容列表 使用 type 字段区分
    let title = '我的课程';
    if (type && TYPE_LIST.indexOf(type) >= 0) {
      this.setYZData({ type });
      switch (type) {
        case 'column':
          title = '全部专栏';
          break;
        case 'content':
          title = '全部内容';
          break;
        case 'rights':
          title = '全部权益';
          break;
        case 'live':
          title = '全部直播';
          break;
        case 'classLive':
          title = '全部班课';
          break;
        default:
          title = '全部权益';
          break;
      }
    } else {
      // 隐藏分享按钮
      wx.hideShareMenu({
        fail(err) {
          console.error(err);
        },
      });

      let showBindPhoneNumber = false;
      if (!app.getBuyerId()) {
        showBindPhoneNumber = true;
      } else {
        showBindPhoneNumber = false;
      }
      this.setYZData({ showBindPhoneNumber });
    }
    wx.setNavigationBarTitle({ title });
    this.setYZData({
      inited: true,
      title,
      tabTitle: dangerA + dangerB,
    });

    this.onCheckTabExist();
  },

  onShow() {
    this.setYZData({
      windowHeight: app.getSystemInfoSync().windowHeight,
    });

    this.getProgresses();
  },

  onShareAppMessage() {
    return logv3.processShareData({
      title: this.data.title,
      path: this.data.shareUrl,
    });
  },

  onPullDownRefresh() {
    this.setYZData({
      refresh: true,
    });
  },

  onReachBottom() {
    if (this.data.type && !this.data.loading && !this.data.noMore) {
      this.loadMore();
    } else if (
      !this.data.type &&
      !this.data.lists[this.data.tab.selectedId].loading &&
      !this.data.lists[this.data.tab.selectedId].noMore
    ) {
      this.loadMore();
    }
  },

  loadMore() {
    const newState = {};
    if (this.data.type) {
      newState.loading = true;
    } else {
      newState[`lists.${this.data.tab.selectedId}.loading`] = true;
    }
    this.setYZData(newState, { immediate: true });
  },

  getProgresses() {
    // 获取 Storage 中存储的进度信息
    const progresses = wx.getStorageSync('paidcontent:progress') || {};
    this.setYZData({
      progresses,
    });
  },

  onAllListLoaded(e) {
    const { list = [], noMore = false } = e.detail;

    this.setYZData({
      list,
      noMore,
      loading: false,
      fetched: true,
      refresh: false,
    });
  },

  onListLoaded(e) {
    const { list = [], noMore = false } = e.detail;

    let unstartList = [];
    let ongoingList = [];
    let endList = [];

    if (e.target.id === 'course') {
      // 根据status（1-已学完、2-未开始、3-进行中）对data进行filter操作
      unstartList = list.filter((value) => {
        return value.eduCourseLittleDTO.eduCourseState === 2;
      });
      ongoingList = list.filter((value) => {
        return value.eduCourseLittleDTO.eduCourseState === 3;
      });
      endList = list.filter((value) => {
        return value.eduCourseLittleDTO.eduCourseState === 1;
      });
    }

    this.setData({
      refresh: false,
      [`lists.${e.target.id}`]: {
        list,
        unstartList,
        ongoingList,
        endList,
        noMore,
        loading: false,
        fetched: true,
      },
    });
  },

  handleZanTabChange({ detail }) {
    this.setYZData({
      selectedTabIdx: detail.index,
      'tab.selectedId': this.data.tab.list[detail.index].id,
    });
  },

  /**
   * 账号绑定
   */
  tapBindZanAccount() {
    this.setYZData({
      accountLogin: true,
    });
  },

  onAccountSuccess() {
    this.setYZData({
      showBindPhoneNumber: false,
      accountLogin: false,
    });
    this.onPullDownRefresh();
  },

  onAccountClose() {
    this.setYZData({
      accountLogin: false,
    });
  },

  onCheckTabExist() {
    app
      .request({
        path: '/wscvis/knowledge/mypay/checkExist.json',
        method: 'GET',
        data: {
          kdt_id: getKdtId(),
        },
      })
      .then((res) => {
        // 重新设置 tab 的 list
        const tabList = this.data.tab.list.filter((o) => {
          return res[LOCAL_MAPPER[o.id]];
        });
        this.setYZData({
          tabShowStatus: res,
          'tab.list': tabList,
          'tab.selectedId': tabList[0].id,
        });
        console.log(
          'tabShowStatus:',
          this.data.tabShowStatus,
          this.data.tab.list
        );
      });
  },
});
