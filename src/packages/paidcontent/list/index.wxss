@import "/components/zan-account/bind-phone-number/index.wxss";

page {
  background: #f9f9f9;
}

.paid-list {
  position: relative;
  margin-bottom: 50px;
  min-height: 80vh;
  min-height: calc(100vh - 140px);
}

#list-threshold {
  position: absolute;
  bottom: 200px;
  width: 100vw;
  height: 1px;
}

.content-part, .column-part {
  background: #fff;
  overflow-x: hidden;
}

.no-item {
  padding: 85px 0;
  font-size: 12px;
  color: #999;
  text-align: center;
  background: #fff;
}

.no-more {
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 12px;
  color: #ccc;
}

.bind-phone-container {
  min-height: 56px;
}

.bind-phone-container .bind__container {
  margin-bottom: 0;
}
.bind__container .bind__title {
  font-size: 12px;
}
.course-item-tab {
  line-height: 17px;
  font-size: 12px;
  color: #969799;
  margin-top: 16px;
  margin-bottom: 10px;
  padding-left: 15px;
}
