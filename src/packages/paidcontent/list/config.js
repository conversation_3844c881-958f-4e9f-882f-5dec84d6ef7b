import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';
import { parseTime, parsePrice } from '../utils';
import { shopCanPayForKnowledge } from '../../../utils/judgement';

const app = getApp();
const dangerA = '打';
const dangerB = '卡';

export const getKdtId = () => {
  const { chainStoreInfo = {} } = app.getShopInfoSync();
  const { isMultiOnlineShop } = chainStoreInfo;
  const kdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId();
  return kdtId;
};

export default {
  allColumn: {
    request: {
      method: 'POST',
      api: 'youzan.owl.column/1.0.1/search',
      data: {
        status: 1,
        buyer_id: app.getBuyerId() || 0,
      },
    },
    failMsg: '获取全部专栏列表失败',
    parse(list) {
      const loggerList = [];

      const _list = list.map((item) => {
        const { buyStatus = {}, picture = {}, lastUpdatedInfo = {} } = item;

        const loggerParams = {
          goods_id: item.goodsId,
          item_id: item.id,
          item_type: 'paid_column',
        };
        loggerList.push(loggerParams);

        lastUpdatedInfo.publishAt = parseTime(lastUpdatedInfo.publishAt);
        return {
          ...item,
          ...buyStatus,
          ...picture,
          lastUpdatedInfo,
          showPriceInfo: shopCanPayForKnowledge(),
        };
      });

      const loggerMsg = getComponentLoggerParams('view', loggerList);
      ensureAppLogger(loggerMsg);

      return _list;
    },
  },

  allContent: {
    request: {
      method: 'POST',
      api: 'youzan.owl.content/1.0.1/search',
      data: {
        user_id: app.getBuyerId() || 0,
        buyer_id: app.getBuyerId() || 0,
        status: 1,
        alias: '',
      },
    },
    failMsg: '获取全部内容列表失败',
    parse(list) {
      const loggerList = [];

      const _list = list.map((item) => {
        const { buyStatus = {}, picture = {} } = item;

        const loggerParams = {
          goods_id: item.goodsId,
          item_id: item.id,
          item_type: 'paid_content',
        };
        loggerList.push(loggerParams);

        return {
          ...item,
          ...buyStatus,
          ...picture,
          publishAt: parseTime(item.publishAt),
          showPriceInfo: shopCanPayForKnowledge(),
        };
      });

      const loggerMsg = getComponentLoggerParams('view', loggerList);
      ensureAppLogger(loggerMsg);

      return _list;
    },
  },

  allRights: {
    request: {
      method: 'POST',
      api: 'youzan.owl.benefitpkg.withcount/1.0.1/search',
      data: {
        buyer_id: app.getBuyerId() || 0,
      },
    },
    failMsg: '获取全部权益列表失败',
    parse(list) {
      return list.map((item) => {
        return {
          ...item,
          showPriceInfo: shopCanPayForKnowledge(),
        };
      });
    },
  },

  allLives: {
    request: {
      method: 'POST',
      api: 'youzan.owl.live.miniapplivelist/1.0.0/get',
      data: {
        sell_status: 1,
        show_in_store: 1,
        sort_by: 'serial_no',
        sub_sort_by: 'publish_at',
        sort_type: 'desc',
      },
    },
    failMsg: '获取全部直播列表失败',
    parse(list) {
      return list.map((item) => {
        return {
          ...item,
          showPriceInfo: shopCanPayForKnowledge(),
        };
      });
    },
  },

  allClassLives: {
    type: 'classLive',
    request: {
      method: 'POST',
      api: 'youzan.edu.class.live.query/1.0.0/miniapp',
      data: {
        kdt_id: getKdtId(),
        page_request: {
          count_enabled: '',
          page_size: 15,
          sort: {
            orders: [
              { direction: 'DESC', property: 'serial_no' },
              { direction: 'DESC', property: 'publish_at' },
            ],
          },
        },
        query: {
          user_id: app.getBuyerId() || 0,
          source: 0,
        },
      },
    },
    failMsg: '获取全部班课列表失败',
    parse(list) {
      return list.map((item) => {
        return {
          ...item,
          showPriceInfo: shopCanPayForKnowledge(),
        };
      });
    },
  },

  course: {
    useHttp: true,
    request: {
      method: 'GET',
      path: '/wscvis/edu/findPageStuSchedule.json',
      data() {
        return {
          pageSize: 10,
          kdt_id: getKdtId(),
        };
      },
    },
    failMsg: '获取线下课列表失败',
  },

  column: {
    request: {
      method: 'POST',
      api: 'youzan.owl.column.sub/1.0.0/search',
      data: {},
    },
    failMsg: '获取已购买专栏列表失败',
    parse(list) {
      return list.map((item) => {
        const { picture = {} } = item;

        return {
          ...item,
          ...picture,
          subCreateTime: parseTime(item.subCreateTime, true),
        };
      });
    },
  },

  content: {
    request: {
      method: 'POST',
      api: 'youzan.owl.live.miniapplivesubs/1.0.0/get',
      data: {
        user_id: app.getToken('buyerId') || 0,
      },
    },
    checkFinishEmpty: true,
    failMsg: '获取已购买内容列表失败',
    parse(list) {
      return list.map((item) => {
        if (item.mediaType === 4) {
          item.boughtTime = parseTime(item.subCreateTime, true);
          return item;
        }
        return {
          kdtId: item.kdtId || 0,
          alias: item.alias || '',
          cover: (item.picture ? item.picture.cover : item.cover) || '',
          mediaType: +item.mediaType,
          title: item.title,
          columnTitle: item.columnTitle || '',
          // 有所属专栏 && 开启免费试读 && 不是已购买内容列表 --> 显示 “免费试X”
          showFree: item.columnAlias && item.isFree,
          summary: item.summary,
          count: item.subscriptionsCount,
          subCount: item.subscriptionsCount,
          time: parseTime(item.publishAt),
          buyTime: parseTime(item.subCreateTime, true),
          price: parsePrice(item.price),
        };
      });
    },
  },

  punch: {
    useHttp: true,
    request: {
      method: 'GET',
      path: '/wscvis/punch/getBoughtList',
      data() {
        return {
          page: 1,
          size: 10,
          kdt_Id: getKdtId(),
        };
      },
    },
    failMsg: `获取已购买群${dangerA + dangerB}列表失败`,
  },

  rights: {
    request: {
      method: 'POST',
      api: 'youzan.owl.benefitpkg.subscription/1.0.0/list',
      data: {},
    },
    failMsg: '获取已购买权益列表失败',
    parse(list) {
      return list.map((item) => {
        item.buyTime = parseTime(item.buyTime, true);
        return item;
      });
    },
  },

  smallClass: {
    useHttp: true,
    request: {
      method: 'GET',
      path: '/wscvis/course/live/small-class/findSubSmallClassPage.json',
      data() {
        return {
          pageSize: 10,
          kdt_id: getKdtId(),
        };
      },
    },
    failMsg: '获取班课列表失败',
  },
};
