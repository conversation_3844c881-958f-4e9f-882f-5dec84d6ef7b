import WscComponent from 'pages/common/wsc-component/index';
import appLogger<PERSON><PERSON>avior from 'shared/components/showcase/behaviors/app-logger-behavior';

const app = getApp();
const PAGE_SIZE = 15;

WscComponent({
  behaviors: [appLogger<PERSON>ehavior],

  properties: {
    loading: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        if (val && this.data.inited) this.fetchList();
      }
    },
    refresh: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        if (val) this.fetchList(true);
      }
    },
    show: {
      type: <PERSON><PERSON><PERSON>,
      value: true,
      observer(val) {
        if (val && !this.data.noMore && !this.data.inited) this.fetchList();
      }
    },
    config: Object
  },

  data: {
    inited: false,
    page: 1,
    pageNumber: 1,
    list: [],
    total: 0,
    noMore: false
  },

  attached() {
    if (this.data.show) this.fetchList();
  },

  methods: {
    fetchList(refresh = false) {
      if (!this.data.inited) wx.showLoading();

      let { page, list, config } = this.data;

      // 不使用 carmen 协议
      if (config.useHttp) {
        this.fetchByHttp(refresh);
        return;
      }

      // 刷新重置
      if (refresh) {
        page = 1;
        list = [];
      }

      const {
        data,
        method,
        api
      } = config.request;

      const options = {
        method,
        api,
        data: {
          page,
          page_size: PAGE_SIZE,
          ...data
        },
        
        success: (res) => {
          // 非翻页
          if (Array.isArray(res)) {
            this.setData({
              noMore: true,
              list: this.parseList(res || []),
              total: res.length
            });
            return;
          }

          if (res.data) res = res.data;
          if (!res.list && res.items) res.list = res.items;

          let noMore = false;

          if (config.checkFinishEmpty) {
            if ((res.list || []).length === 0) {
              noMore = true;
            }
          } else {
            if ((res.list || []).length < PAGE_SIZE) {
              noMore = true;
            }
          }

          this.setData({
            noMore,
            page: ++page,
            list: list.concat(this.parseList(res.list || [])),
            total: res.total || (res.paginator && res.paginator.totalCount)
          });
        },
        fail: () => {
          wx.showToast({
            title: this.data.config.failMsg || '获取列表失败',
            icon: 'none'
          });
        },
        complete: () => {
          if (refresh) {
            wx.stopPullDownRefresh();
          }
          if (!this.data.inited) {
            this.setData({
              inited: true
            });
            wx.hideLoading();
          }
          this.triggerEvent('loaded', {
            list: this.data.list,
            noMore: this.data.noMore
          });
        }
      };

      if (config.type === 'classLive') {
        options.header = {
          'content-type': 'application/json'
        }
        options.data.page_request.page_number = page;
        options.success = (res) => {
          const { content = [], total } = res;
          let noMore = false;
          if (content.length < PAGE_SIZE) {
            noMore = true;
          }

          this.setData({
            noMore,
            page: ++page,
            list: list.concat(this.parseList(content || [])),
            total: total
          });
        }
      };

      app.carmen(options);
    },

    /**
     * 暂时只用于群打卡
     * 有其他使用请自行适配
     */
    fetchByHttp(refresh = false) {
      let { page, pageNumber, list, config } = this.data;
      page = refresh ? 1 : page;
      pageNumber = refresh ? 1 : pageNumber;
      list = refresh ? [] : list;

      const {
        method,
        path,
      } = config.request;

      let { data } = config.request;
      if (data) data = data();
      const params = Object.assign({}, data || {}, {
        page,
        pageNumber
      });

      app
        .request({
          method,
          path,
          data: params
        })
        .then(res => {
          if (res) {
            const newList = list.concat(this.parseList(res.content || []));
            this.setData({
              noMore: newList.length >= res.total,
              page: ++page,
              pageNumber: ++pageNumber,
              list: newList,
              total: res.total
            });

            if (refresh) {
              wx.stopPullDownRefresh();
            }
            if (!this.data.inited) {
              this.setData({
                inited: true
              });
              wx.hideLoading();
            }
            this.triggerEvent('loaded', {
              list: this.data.list,
              noMore: this.data.noMore
            });
          }
        })
        .catch(err => {
          console.error(err);
          wx.showToast({
            title: config.failMsg || '获取列表失败',
            icon: 'none'
          });
        });
    },

    parseList(list) {
      if (!this.data.config.parse) return list;
      return this.data.config.parse(list);
    }
  }
});
