import WscComponent from 'pages/common/wsc-component/index';
import { moment } from '@youzan/weapp-utils/lib/time';

const { platform } = wx.getSystemInfoSync();

WscComponent({
  properties: {
    item: Object,
  },

  attached() {
    this.initState(this.data.item);
  },

  data: {
    statusList: [],
    pageUrl: '',
  },

  methods: {
    getStatusList(item) {
      const subCreateDesc = platform === 'ios' ? '订阅时间：' : '购买时间：';
      return [`${subCreateDesc}${moment(item.createdTime, 'YYYY-MM-DD')}`];
    },

    getPageUrl(item) {
      // const h5Url =
      //   item.url.search('youzan.com') > -1
      //     ? item.url
      //     : `https://h5.youzan.com${item.url}`;
      // let url = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
      //   h5Url
      // )}`;
      return `/packages/paidcontent/live/index?alias=${item.alias}`;
    },

    initState(item) {
      this.setYZData({
        statusList: this.getStatusList(item),
        pageUrl: this.getPageUrl(item),
      });
    },
  },
});
