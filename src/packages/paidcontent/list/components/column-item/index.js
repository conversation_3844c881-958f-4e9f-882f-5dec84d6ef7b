import WscComponent from 'pages/common/wsc-component/index';
import columnItem<PERSON>ehavior from '../../../behaviors/column-item';

const { platform } = wx.getSystemInfoSync();

WscComponent({
  behaviors: [columnItemBehavior],

  methods: {
    getStatusList(column) {
      const subCreateDesc = platform === 'ios' ? '订阅时间：' : '购买时间：';
      return [`${subCreateDesc}${column.subCreateTime}`];
    },

    getSubtitle(column) {
      const isGift = column.channelType === 1 || column.channelType === 3;
      return isGift ? `${column.fromUserName || '匿名用户'}请你看` : `已更新${column.contentsCount}期`;
    }
  }
});
