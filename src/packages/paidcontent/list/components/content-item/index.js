import WscComponent from 'pages/common/wsc-component/index';
import contentItemBehavior from '../../../behaviors/content-item';

const { platform } = wx.getSystemInfoSync();

WscComponent({
  behaviors: [contentItemBehavior],

  properties: {
    progress: {
      type: Object,
      value: {}
    }
  },

  methods: {
    getStatusList(content, typeSuffix) {
      const list = [];
      const desc = `${platform === 'ios' ? `订阅时间：${content.buyTime}` : `购买时间：${content.buyTime}`}`;
      list.push(desc);

      const { progress } = this.data;
      if (progress && progress.percent) {
        list.push({
          text: +progress.percent === 100 ? `已${typeSuffix}完` : `${progress.percent}%`,
          color: '#00bf00'
        });
      }

      return list;
    },

    getSubtitle(content) {
      const isGift = content.channelType === 1 || content.channelType === 3;
      return isGift ? `${content.fromUserName || '匿名用户'}请你看` : '';
    }
  }
});
