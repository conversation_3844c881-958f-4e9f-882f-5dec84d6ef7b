import WscComponent from 'pages/common/wsc-component/index';
import courseItemBehavior from '../../../behaviors/course-item';

WscComponent({
  behaviors: [courseItemBehavior],

  methods: {
    getStatusList(course) {
      // 骚操作：因为学员和之前的购买时间的UI完全一致，那就用原来的购买时间的UI了
      if (course.userAssetDTO && course.userAssetDTO.studentName) {
        return [`学员：${course.userAssetDTO.studentName}`];
      }
      return [''];
    },

    getSubtitle(course) {
      if (course.courseProductDTO.sellType === 1) {
        return `${course.userAssetDTO.total / 100}课时`;
      }
      if (course.courseProductDTO && course.courseProductDTO.skuVal) {
        return course.courseProductDTO.skuVal;
      }
      return '';
    },

    getCourseTypeName(course) {
      // 同h5端逻辑统一，只显示体验课
      if (course.courseProductDTO) {
        const { courseTypeName, courseType } = course.courseProductDTO;
        if (courseType === 0) {
          return courseTypeName;
        }
      }
      return '';
    },
  },
});
