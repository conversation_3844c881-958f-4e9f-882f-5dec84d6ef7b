<import src="/components/zan-account/bind-phone-number/index.wxml"/>

<page-container
  forbid-copyright="{{ !fetched }}"
  class="{{ themeClass }} page-{{ deviceType }}"
>

  <view wx:if="{{ showBindPhoneNumber }}" class="bind-phone-container container {{ themeClass }}">
    <template is="bind_phone_number" data="{{bindTips}}"></template>
  </view>

  <view class="{{ themeClass }} paid-list" wx:if="{{ inited }}">
    <block wx:if="{{ type }}">
      <!-- 全部专栏列表 -->
      <list 
        id="all-column"
        wx:if="{{ type === 'column' }}" 
        loading="{{ loading }}"
        refresh="{{ refresh }}"
        config="{{ listConfig.allColumn }}" 
        bind:loaded="onAllListLoaded">
        <cap-paid-column 
          wx:for="{{ list }}" 
          wx:key="id" 
          column="{{ item }}" 
          theme-class="{{ themeClass }}" 
          hide-columncontentscount="{{ hideColumnContentsCount }}" 
          hide-columnsubscount="{{ hideColumnSubsCount }}"
          show-price-info="{{ item.showPriceInfo }}"
        />
      </list>

      <!-- 全部内容列表 -->
      <list 
        id="all-content"
        wx:if="{{ type === 'content' }}" 
        loading="{{ loading }}"
        refresh="{{ refresh }}"
        config="{{ listConfig.allContent }}" 
        bind:loaded="onAllListLoaded">
        <cap-paid-content 
          wx:for="{{ list }}"
          wx:key="id"
          content="{{ item }}" 
          theme-class="{{ themeClass }}" 
          hide-contentsubscount="{{ hideContentSubsCount }}"
          show-price-info="{{ item.showPriceInfo }}"
        />
      </list>

      <!-- 全部权益列表 -->
      <list 
        id="all-rights"
        wx:if="{{ type === 'rights' }}" 
        loading="{{ loading }}"
        refresh="{{ refresh }}"
        config="{{ listConfig.allRights }}" 
        bind:loaded="onAllListLoaded">
        <cap-member-item
          wx:for="{{ list }}" 
          wx:key="id"
          theme-class="{{ themeClass }}"
          alias="{{ item.alias }}"
          cover="{{ item.cover }}"
          title="{{ item.name }}"
          price="{{ item.buyStatus && item.buyStatus.price }}"
          is-paid="{{ item.buyStatus && item.buyStatus.isBought }}"
          count="{{ item.periodsCount }}"
          sub-count="{{ item.subscriptionsCount }}"
          summary="{{ item.summary }}"
          show-price-info="{{ item.showPriceInfo }}"
        />
      </list>

      <!-- 全部直播列表 -->
      <list
        id="all-live"
        wx:if="{{ type === 'live' }}"
        loading="{{ loading }}"
        refresh="{{ refresh }}"
        config="{{ listConfig.allLives }}"
        bind:loaded="onAllListLoaded">
        <cap-paid-live
          wx:for="{{ list }}"
          wx:key="id"
          liveItem="{{ item }}"
          theme-class="{{ themeClass }}"
          show-price-info="{{ item.showPriceInfo }}"
        />
      </list>

      <!-- 全部班课 -->
      <list
        id="all-class-live"
        wx:if="{{ type === 'classLive' }}"
        loading="{{ loading }}"
        refresh="{{ refresh }}"
        type='classLive'
        config="{{ listConfig.allClassLives }}"
        bind:loaded="onAllListLoaded">
        <cap-paid-live
          wx:for="{{ list }}"
          wx:key="id"
          liveItem="{{ item }}"
          theme-class="{{ themeClass }}"
          show-price-info="{{ item.showPriceInfo }}"
        />
      </list>
    </block>
    <block wx:else>
      <block wx:if="{{ tabShowStatus.course || tabShowStatus.column || tabShowStatus.content || tabShowStatus.benefit || tabShowStatus.gci || tabShowStatus.smallClass }}">
        <van-tabs
          active="{{ selectedTabIdx }}"
          bind:change="handleZanTabChange">

          <!-- 线下课 -->
          <template name="course-item">
            <view wx:if="{{ lists.course.ongoingList.length > 0 }}">
              <view class="course-item-tab">进行中</view>
              <course-item
                wx:for="{{ lists.course.ongoingList }}"
                wx:key="id"
                course="{{ item }}"
              />
            </view>
            <view wx:if="{{ lists.course.unstartList.length > 0 }}">
              <view class="course-item-tab">未开始</view>
              <course-item
                wx:for="{{ lists.course.unstartList }}"
                wx:key="id"
                course="{{ item }}"
              />
            </view>
            <view wx:if="{{ lists.course.endList.length > 0 }}">
              <view class="course-item-tab">已学完</view>
              <course-item
                wx:for="{{ lists.course.endList }}"
                wx:key="id"
                course="{{ item }}"
              />
            </view>
          </template>

          <!-- 专栏 -->
          <template name="column-item">
            <column-item
              wx:for="{{ lists.column.list }}"
              wx:key="id"
              column="{{ item }}"
            />
          </template>

          <!-- 内容：需要区分直播和普通内容商品 -->
          <template name="content-item">
            <block wx:for="{{ lists.content.list }}" wx:key="id">
              <live-item 
                wx:if="{{ item.mediaType === 4 }}"
                themeClass="{{ themeClass }}"
                live-item="{{ item }}"
              />
              <content-item
                wx:else
                content="{{ item }}"
                progress="{{ progresses['c-' + item.alias] }}"
              />
            </block>
          </template>

          <template name="punch-item">
            <punch-item
              wx:for="{{ lists.punch.list }}"
              wx:key="{{ item.alias }}}"
              theme-class="{{ themeClass }}"
              bought
              punch="{{ item }}"
            />
          </template>

          <!-- 会员权益 -->
          <template name="rights-item">
            <rights-item
              wx:for="{{ lists.rights.list }}" 
              wx:key="id"
              theme-class="{{ themeClass }}"
              rights="{{ item }}"
            />
          </template>

          <!-- 班课 -->
          <template name="small-class-item">
            <small-class-item
              wx:for="{{ lists.smallClass.list }}"
              wx:key="{{ item.alias }}"
              item="{{ item }}"
            />
          </template>

          <!-- 通用模板 -->
          <van-tab wx:if="{{  tabShowStatus.course }}" title="线下课">
            <list
              id="course"
              loading="{{ lists.course.loading }}"
              refresh="{{ refresh }}"
              list="{{ lists.course.list }}"
              config="{{ listConfig.course }}" 
              bind:loaded="onListLoaded">
              <template is="course-item" data="{{ lists }}"/>
            </list>
          </van-tab>
          <van-tab wx:if="{{ tabShowStatus.column }}" title="专栏">
            <list
              id="column"
              loading="{{ lists.column.loading }}"
              refresh="{{ refresh }}"
              list="{{ lists.column.list }}"
              config="{{ listConfig.column }}" 
              bind:loaded="onListLoaded">
              <template is="column-item" data="{{ lists }}"/>
            </list>
          </van-tab>
          <van-tab wx:if="{{ tabShowStatus.smallClass }}" title="班课">
            <list
              id="smallClass"
              loading="{{ lists.smallClass.loading }}"
              refresh="{{ refresh }}"
              list="{{ lists.smallClass.list }}"
              config="{{ listConfig.smallClass }}" 
              bind:loaded="onListLoaded">
              <template is="small-class-item" data="{{ lists }}"/>
            </list>
          </van-tab>
          <van-tab wx:if="{{ tabShowStatus.content }}" title="内容">
            <list
              id="content"
              loading="{{ lists.content.loading }}"
              refresh="{{ refresh }}"
              list="{{ lists.content.list }}"
              config="{{ listConfig.content }}" 
              bind:loaded="onListLoaded">
              <template is="content-item" data="{{ lists }}"/>
            </list>
          </van-tab>
          <van-tab wx:if="{{ tabShowStatus.benefit }}" title="会员">
            <list
              id="rights"
              loading="{{ lists.rights.loading }}"
              refresh="{{ refresh }}"
              list="{{ lists.rights.list }}"
              config="{{ listConfig.rights }}" 
              bind:loaded="onListLoaded">
              <template is="rights-item" data="{{ lists }}"/>
            </list>
          </van-tab>
          <!-- 暂时屏蔽，后期插件化 -->
          <!-- <van-tab wx:if="{{ tabShowStatus.gci }}" title="{{ tabTitle }}">
            <list
              id="punch"
              loading="{{ lists.punch.loading }}"
              refresh="{{ refresh }}"
              list="{{ lists.punch.list }}"
              config="{{ listConfig.punch }}" 
              bind:loaded="onListLoaded">
              <template is="punch-item" data="{{ lists }}"/>
            </list>
          </van-tab> -->
        </van-tabs>
      </block>
      <block wx:else>
        <empty-page></empty-page>
      </block>
    </block>
  </view>
</page-container>

<account-login
  wx:if="{{ accountLogin }}"
  show="{{ accountLogin }}"
  bind:loginSuccess="onAccountSuccess"
  bind:closeAccountLogin="onAccountClose"
></account-login>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />