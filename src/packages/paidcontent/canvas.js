import upload from 'utils/upload';

const scale = 3;
const DEFAULT_USER_INFO =
  'https://img.yzcdn.cn/public_files/2017/10/23/1321da81aa84d0539b0d5af73fbbf53b.png';

/**
 * 绘制圆角矩形
 */
export function drawRoundRect(x, y, width, height, radius, cxt) {
  cxt.beginPath();
  cxt.arc(x + radius, y + radius, radius, Math.PI, Math.PI * (3 / 2));
  cxt.lineTo(width - radius + x, y);
  cxt.arc(width - radius + x, radius + y, radius, Math.PI * (3 / 2), Math.PI * 2);
  cxt.lineTo(width + x, height + y);
  cxt.arc(width - radius + x, height - radius + y, radius, 0, Math.PI * (1 / 2));
  cxt.lineTo(x, height + y);
  cxt.arc(radius + x, height - radius + y, radius, Math.PI * (1 / 2), Math.PI);
  cxt.closePath();
}

/**
 * 绘制头像
 */
export function drawAvatarImg(x, y, radius, img, ctx) {
  ctx.save();
  // 画圆
  var cx = x + radius;
  var cy = y + radius;
  ctx.beginPath();
  ctx.arc(cx, cy, radius, 0, 2 * Math.PI);
  ctx.closePath();

  ctx.clip();
  const d = 2 * radius;
  ctx.drawImage(img, x, y, d, d);
  ctx.restore();
}

/**
 * 绘制头像2
 */
export function drawAvatarImg2(ctx, _x, _y, _radius, img) {
  const x = _x * scale;
  const y = _y * scale;
  const radius = _radius * scale;
  ctx.save();
  // 画圆
  var cx = x + radius;
  var cy = y + radius;
  ctx.beginPath();
  ctx.arc(cx, cy, radius, 0, 2 * Math.PI);
  ctx.closePath();

  ctx.clip();
  const d = 2 * radius;
  ctx.drawImage(img, x, y, d, d);
  ctx.restore();
}

/**
 * 绘制图片(方、圆角、圆)
 *
 * @param ctx      画布
 * @param img      load好的img对象
 * @param x        左上角定点 x 轴坐标
 * @param y        左上角定点 y 轴坐标
 * @param w        宽
 * @param h        高
 * @param radius   圆角半径
 * @param bgColor  背景色
 */
export function drawImage(ctx, img, _x, _y, _w, _h, _radius = 0) {
  const x = _x * scale;
  const y = _y * scale;
  const radius = _radius * scale;
  const w = _w * scale;
  const h = _h * scale;
  ctx.save();
  const width = w;
  const height = h || w;
  // 绘制圆角
  if (radius) {
    // ctx.save();
    // 计算四个断点
    const endPoints = [
      { x, y },
      { x: x + width, y },
      { x, y: y + height },
      { x: x + width, y: y + height }
    ];
    // 计算横矩形的四个点
    const horRectPoints = [
      { x, y: y + radius },
      { x: x + width, y: y + radius },
      { x, y: y + height - radius },
      { x: x + width, y: y + height - radius }
    ];
    // 计算垂直矩形的四个点
    const verRectPoints = [
      { x: x + radius, y },
      { x: x + width - radius, y },
      { x: x + radius, y: y + height },
      { x: x + width - radius, y: y + height }
    ];
    ctx.beginPath();
    ctx.moveTo(horRectPoints[0].x, horRectPoints[0].y);
    ctx.arcTo(endPoints[0].x, endPoints[0].y, verRectPoints[0].x, verRectPoints[0].y, radius);
    ctx.lineTo(verRectPoints[1].x, verRectPoints[1].y);
    ctx.arcTo(endPoints[1].x, endPoints[1].y, horRectPoints[1].x, horRectPoints[1].y, radius);
    ctx.lineTo(horRectPoints[3].x, horRectPoints[3].y);
    ctx.arcTo(endPoints[3].x, endPoints[3].y, verRectPoints[3].x, verRectPoints[3].y, radius);
    ctx.lineTo(verRectPoints[2].x, verRectPoints[2].y);
    ctx.arcTo(endPoints[2].x, endPoints[2].y, horRectPoints[2].x, horRectPoints[2].y, radius);
    ctx.closePath();
    ctx.clip();
  }
  ctx.drawImage(img, x, y, width, height);
  ctx.restore();
}

/**
 * 绘制单行文字
 *
 * @param ctx         画布
 * @param content     内容
 * @param x           绘制左下角原点 x 坐标
 * @param y           绘制左下角原点 y 坐标
 * @param fontSize    字体大小
 * @param fontFamily  字体家族
 * @param color       字体颜色
 * @param textAlign   字体排布
 * @param lineHeight  设置行高
 */
export function drawTextLine(
  ctx,
  content,
  _x,
  _y,
  _fontSize = 12,
  fontFamily = 'serif',
  color = '#333',
  textAlign = 'left',
  _lineHeight = _fontSize
) {
  const x = _x * scale;
  const lineHeight = _lineHeight * scale;
  const fontSize = _fontSize * scale;
  let y = _y * scale;
  y -= (lineHeight - fontSize) / 2;

  ctx.textAlign = textAlign;
  ctx.font = `${fontSize}px ${fontFamily}`;
  ctx.fillStyle = color;
  ctx.fillText(content, x, y);
}

/**
 * 绘制多行片段
 *
 * @param ctx         画布
 * @param content     内容
 * @param x           绘制左下角原点 x 坐标
 * @param y           绘制左下角原点 y 坐标
 * @param maxWidth    最大宽度
 * @param fontSize    字体大小
 * @param fontFamily  字体家族
 * @param color       字体颜色
 * @param lineHeight  设置行高
 * @param maxLine     设置最多 n 行， n 行结尾以 ... 结束（-1 不设置）
 */
export function drawParagraph(
  ctx,
  content,
  x,
  y,
  maxWidth,
  fontSize = 12,
  fontFamily = 'serif',
  color = '#333',
  lineHeight = fontSize,
  maxLine = -1
) {
  let line = 0;
  // 修改换行符为空格符
  content = content.replace(/\n|\r/g, ' ');

  // 获取几行文字
  const maxFontLine = calcLineAmount(ctx, content, maxWidth, fontSize, fontFamily);

  const contentArr = Array.from(content);
  const maxFontAmount =
    ((maxWidth * scale) /
      (measureContentLength(ctx, content, fontSize, fontFamily) / contentArr.length)) >>
    0;

  while (contentArr.length) {
    line += 1;
    const drawingText = contentArr.splice(0, maxFontAmount).join('');
    if (line < maxLine || maxLine === -1) {
      // 如果当前行小于指定最大行 或者 不设置最大行
      drawTextLine(ctx, drawingText, x, y, fontSize, fontFamily, color, 'left', lineHeight);
    } else if (line === maxLine) {
      if (maxFontLine > maxLine) {
        drawTextLine(
          ctx,
          `${drawingText.substr(0, drawingText.length - 2)}...`,
          x,
          y,
          fontSize,
          fontFamily,
          color,
          'left',
          lineHeight
        );
      } else {
        drawTextLine(ctx, drawingText, x, y, fontSize, fontFamily, color, 'left', lineHeight);
      }
    }
    y += lineHeight;
  }
}

/**
 * 绘制线条
 * @description 高阶函数 drawLine(true)(ctx, x, y, xx, yy, width, color, pattern, offset);
 * @param isDash      (true)是否是虚线 默认实线
 * @param ctx         画布
 * @param x           绘制原点 x 坐标
 * @param y           绘制原点 y 坐标
 * @param xx          绘制结束点 x 坐标
 * @param yy          绘制结束点 y 坐标
 * @param width       线有多粗
 * @param color       颜色
 * @param pattern     一组描述交替绘制线段和间距（坐标空间单位）长度的数字 [10, 20]
 * @param offset      虚线偏移量

 * @description 高阶函数 drawLine(false)(ctx, x, y, xx, yy, width, color, lineCap);
 * @param isDash      (false)是否是虚线 默认实线
 * @param ctx         画布
 * @param x           绘制原点 x 坐标
 * @param y           绘制原点 y 坐标
 * @param xx          绘制结束点 x 坐标
 * @param yy          绘制结束点 y 坐标
 * @param width       线有多粗
 * @param color       颜色
 * @param lineCap     线条的端点样式
 */
export function drawLine(isDash = false) {
  let returnFn = () => {};
  if (isDash) {
    returnFn = (
      ctx,
      _x,
      _y,
      _xx,
      _yy,
      _width,
      color = '#e5e5e5',
      pattern = [10 * scale, 20 * scale],
      offset = 5 * scale
    ) => {
      const x = _x * scale;
      const y = _y * scale;
      const xx = _xx * scale;
      const yy = _yy * scale;
      const width = _width * scale;

      ctx.setLineDash(pattern, offset);

      ctx.beginPath();
      ctx.setStrokeStyle(color);
      ctx.setLineWidth(width);
      ctx.moveTo(x, y);
      ctx.lineTo(xx, yy);
      ctx.stroke();
    };
  } else {
    returnFn = (ctx, _x, _y, _xx, _yy, _width, color = '#e5e5e5', lineCap) => {
      const x = _x * scale;
      const y = _y * scale;
      const xx = _xx * scale;
      const yy = _yy * scale;
      const width = _width * scale;

      ctx.beginPath();
      ctx.setStrokeStyle(color);
      lineCap && ctx.setLineCap(lineCap);
      ctx.setLineWidth(width);
      ctx.moveTo(x, y);
      ctx.lineTo(xx, yy);
      ctx.stroke();
    };
  }
  return returnFn;
}

/**
 * 计算文字宽度
 * @param ctx         画布
 * @param content     内容
 * @param fontSize    字体大小
 */
export function measureContentLength(ctx, content, _fontSize = 12, fontFamily = 'serif') {
  const fontSize = _fontSize * scale;
  ctx.font = `${fontSize}px ${fontFamily}`;
  if (wx.canIUse('canvasContext.measureText')) {
    return ctx.measureText(content).width;
  }
  return content.length * fontSize;
}

/**
 * 计算文字占用行数';
 *
 * @param ctx         画布
 * @param content     内容
 * @param maxWidth    最大宽度
 * @param fontSize    字体大小
 */
export function calcLineAmount(ctx, content, _maxWidth, _fontSize = 12, fontFamily = 'serif') {
  const maxWidth = _maxWidth * scale;
  const fontSize = _fontSize * scale;
  ctx.font = `${fontSize}px ${fontFamily}`;
  return Math.ceil(measureContentLength(ctx, content, fontSize, fontFamily) / maxWidth);
}

// 下载图片获得临时路径
export function wxGetImageInfo(src) {
  return new Promise((resolve, reject) => {
    if (typeof src === 'string') {
      src = src.replace(/^http:/, 'https:');
    }
    wx.getImageInfo({
      src,
      success: (res) => {
        resolve(res);
      },
      fail: (e) => {
        reject(e);
      }
    });
  });
}

// 上传头像
export function uploadMetiarial(src) {
  return new Promise((resolve) => {
    if (src) {
      wxGetImageInfo(src).then((res) => {
        upload({
          file: res.path,
          success: (res) => {
            resolve(res.attachmentPath);
          },
          fail: (err) => {
            console.warn('上传图片失败', err);
            resolve(DEFAULT_USER_INFO);
          }
        });
      });
    }
  });
}
