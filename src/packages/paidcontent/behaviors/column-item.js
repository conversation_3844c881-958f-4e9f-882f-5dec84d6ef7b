const app = getApp();
const { chainStoreInfo = {} } = app.getShopInfoSync();
const { isMultiOnlineShop } = chainStoreInfo;

export default Behavior({
  properties: {
    themeClass: String,
    column: Object,
    isPaid: Boolean
  },

  attached() {
    this.initState(this.data.column);
  },

  data: {
    subtitle: '',
    statusList: [],
    pageUrl: '',
  },

  methods: {
    getSubtitle() {
      return '';
    },

    getStatusList() {
      return [];
    },

    getPageUrl(column) {
      // 连锁店铺跳转
      if (isMultiOnlineShop) {
        return `/pages/common/blank-page/index?weappSharePath=packages%2Fpaidcontent%2Fcolumn%2Findex%3Fkdt_id%3D${column.kdtId}%26alias%3D${column.alias}`;
      }
      // 单店保持原来跳转
      return `/packages/paidcontent/column/index?alias=${column.alias}`;
    },

    initState(column) {
      this.setData({
        subtitle: this.getSubtitle(column),
        statusList: this.getStatusList(column),
        pageUrl: this.getPageUrl(column)
      });
    }
  }
});
