const app = getApp();
const { chainStoreInfo = {} } = app.getShopInfoSync();
const { isMultiOnlineShop } = chainStoreInfo;

export default Behavior({
  properties: {
    themeClass: String,
    course: Object
  },

  attached() {
    this.initState(this.data.course);
  },

  data: {
    subtitle: '',
    pageUrl: '',
  },

  methods: {
    getSubtitle(course) {
      return course.courseProductDTO.skuVal;
    },

    getStatusList() {
      return [];
    },

    getCourseTypeName() {
      return '';
    },

    getPageUrl(course) {
      let url = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(`https://h5.youzan.com${course.courseDetailUrl}`)}`;
      // 连锁店铺跳转
      if (isMultiOnlineShop) {
        return `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(url)}`;
      }
      // 单店保持原来跳转
      return url;
    },

    initState(course) {
      this.setData({
        subtitle: this.getSubtitle(course),
        statusList: this.getStatusList(course),
        pageUrl: this.getPageUrl(course),
        courseTypeName: this.getCourseTypeName(course),
      });
    }
  }
});
