import paidcontentIconMap from '../common/paidcontent-icon.js';

const app = getApp();
const { chainStoreInfo = {} } = app.getShopInfoSync();
const { isMultiOnlineShop } = chainStoreInfo;

export default Behavior({
  properties: {
    themeClass: String,
    content: Object,
    isPaid: Boolean
  },

  data: {
    typeSuffix: '',
    typeName: '',
    statusList: [],
    freeText: '',
    icon: '',
    subtitle: '',
    pageUrl: ''
  },

  attached() {
    this.initState(this.data.content);
  },

  methods: {
    getTypeName(mediaType) {
      return [
        '',
        'imgtext',
        'audio',
        'video'
      ][+mediaType];
    },

    getTypeSuffix(mediaType) {
      return [
        '',
        '读',
        '听',
        '看',
        '看'
      ][+mediaType];
    },

    getSubtitle() {
      return '';
    },

    getStatusList() {
      return [];
    },

    getFreeText(typeSuffix) {
      if (this.data.content.isFree && !this.data.isPaid) return `免费试${typeSuffix}`;

      return '';
    },

    getIcon(mediaType) {
      const iconUrl = paidcontentIconMap[mediaType];
      return iconUrl;
    },

    getPageUrl(content) {
      // 连锁店铺跳转
      if (isMultiOnlineShop) {
        return `/pages/common/blank-page/index?weappSharePath=packages%2Fpaidcontent%2Fcontent%2Findex%3Falias%3D${content.alias}%26kdt_id%3D${content.kdtId}`;
      }
      // 单店保持原来跳转
      return `/packages/paidcontent/content/index?alias=${content.alias}`;
    },

    initState(content) {
      const {
        mediaType = 0
      } = content;

      const typeSuffix = this.getTypeSuffix(mediaType);

      this.setData({
        typeSuffix,
        typeName: this.getTypeName(mediaType),
        subtitle: this.getSubtitle(content),
        statusList: this.getStatusList(content, typeSuffix),
        freeText: this.getFreeText(typeSuffix),
        icon: this.getIcon(mediaType),
        pageUrl: this.getPageUrl(content)
      });
    }
  }
});
