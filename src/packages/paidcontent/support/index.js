import WscPage from '../../../pages/common/wsc-page/index';

const app = getApp();

WscPage({
  onLoad(query) {
    const kdtId = query.kdt_id || query.kdtId || app.getKdtId();
    const { zanAlias = '', alias = '' } = query;
    // 直接重定向走
    const redirectUrl = `https://h5.youzan.com/wscvis/ump/collect-zan?kdt_id=${kdtId}&alias=${alias}&zanAlias=${zanAlias}`;
    wx.redirectTo({
      url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(redirectUrl)}`
    });
  }
});
