import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';

const app = getApp();

WscComponent({
  properties: {
    kdtId: String,
    pagePath: String,
    title: {
      type: String,
      observer: '_updateSessionFrom'
    },
    cover: {
      type: String,
      observer: '_updateSessionFrom'
    },
    alias: String,
    price: {
      type: Number,
      observer: '_updateSessionFrom'
    },
    webImText: String,
  },

  data: {
    show: false,
    sessionFrom: {},
    endpoint: 'mmp',
  },

  attached() {
    app.getImData()
      .then((res) => {
        this.setYZData({
          show: +res.isSetContact && +res.is_web_im_in_goods,
          'im.businessId': res.businessId || '',
          webImText: this.getWebImText(res.web_im_in_goods_config),
        });
      });

    // app.getImData().then(shopImConfig => {
    //   this.setYZData({
    //     endpoint: shopImConfig.endpoint,
    //   }, () => {
    //     this._updateSessionFrom();
    //   });
    // });

    this._updateSessionFrom();
  },

  methods: {
    onContactBack: navigate.contactBack,
    _updateSessionFrom() {
      this.setYZData({
        sessionFrom: JSON.stringify({
          kdt_id: this.data.kdtId,
          source: 'goods',
          endpoint: this.data.endpoint,
          detail: JSON.stringify({
            name: this.data.title,
            alias: this.data.alias,
            price: +this.data.price,
            imgs: this.data.cover ? [this.data.cover] : [],
          }),
        }),
      });
    },
    getWebImText(webImGoodConfig) {
      try {
        const config = JSON.parse(webImGoodConfig || '{}');
        if (config && 'default' in config) {
          if (config.default === 1) {
            return config.label;
          }
        }
        return '客服';
      } catch (e) {
        return '客服';
      }
    }
  },
});
