<view wx:if="{{ show }}" class="message-container" style="position: relative">
  <van-icon name="chat-o" custom-class="message__icon" />
  <view class="message__text">{{ webImText }}</view>
  <!-- TODO -->
  <button
    class="message__contact-button"
    open-type="contact"
    bindcontact="onContactBack"
    session-from="{{ sessionFrom }}"
    send-message-title="{{ title }}"
    send-message-path="{{ pagePath }}"
    send-message-img="{{ cover }}"
    show-message-card>
  </button>
</view>
