import { format, formatDayWithZero } from 'utils/time';

export default {
  parse(remain) {
    let dateFormateResult = format(remain).data; // 只需要计算出距离开始和结束的倒计时，对于已经结束的(负值)不关注

    let result = [];
    if (dateFormateResult.day) result.push({ value: dateFormateResult.day, unit: '天' });

    result.push({ value: formatDayWithZero(dateFormateResult.hour), unit: ':' });
    result.push({ value: formatDayWithZero(dateFormateResult.minute), unit: ':' });
    result.push({ value: formatDayWithZero(dateFormateResult.second), unit: '' });
    return result;
  }
};
