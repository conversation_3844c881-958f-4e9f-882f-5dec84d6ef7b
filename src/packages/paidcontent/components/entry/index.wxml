<view class="column-entry" bindtap="goTo">
  <view class="column-entry__card">
    <image class="column-entry__thumbnail" src="{{ thumbnail }}" mode="aspectFill" />
    <van-button
      custom-class="column-entry__btn"
      plain
      type="primary"
      size="mini">
      {{ btnText || '查看专栏' }}
    </van-button>
    <view class="column-entry__info">
      <view class="column-entry__info-title">{{ title || ' ' }}</view>
      <view class="column-entry__info-status">{{ status }}</view>
    </view>
  </view>
</view>