@import "themes/base.scss";
@import "shared/common/css/helper/index.wxss";

.column-entry {
  padding: 10px 0;
  background: #fff;
}
.column-entry__card {
  padding: 15px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, .12);
}
.column-entry__thumbnail {
  float: left;
  display: block;
  width: 68px;
  height: 40px;
}
.column-entry__info {
  margin-left: 78px;
  padding-right: 12px;
  overflow: hidden;
}
.column-entry__info-title {
  line-height: 20px;
  color: #333;
  font-size: 14px;
  font-weight: 700;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.column-entry__info-status {
  margin-top: 4px;
  line-height: 16px;
  color: #999;
  font-size: 12px;
  height: 16px;
}
.column-entry__btn {
  float: right;
  top: 10px;
  margin-right: 0;
  color: #0a0 !important;
  border-color: #0a0 !important;
}

