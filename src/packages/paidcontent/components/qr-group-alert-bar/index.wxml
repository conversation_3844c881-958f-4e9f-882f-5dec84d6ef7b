<view 
  wx:if="{{ joinGroupSetting.groupOpen &&  joinGroupSetting.courseDetailPageOpen}}" 
  class="{{ noBorder ? 'qr-cell-wrapper qr-cell-wrapper__no-border' : 'qr-cell-wrapper' }}">
  <view class="{{noBorder ? 'qr-cell qr-cell__no-border' : 'qr-cell'}}">
    <view>
      <image src="https://b.yzcdn.cn/public_files/2019/10/10/db68187e68e42a3b7df1a1a33e45054a.svg" />
      <text>{{ joinGroupSetting.guideCopy }}</text>
    </view>
    <view bindtap="showPop">
      <text>查看</text>
      <van-icon name="arrow" />
    </view>
  </view>
  
  <join-group-popup
    show="{{ showJoinGroup }}"
    join-group-setting="{{ joinGroupSetting }}"
    bind:close="onJoinGroupClose"
  />
</view>