<import src="../countdown/index.wxml" />

  <view class="groupon-section {{ type === 'column' ? 'groupon-column' : '' }}">
    <view class="groupon-section__list" wx:if="{{ promotionData.promotionDetail.isShowJoinGroup && !promotionData.userStatus.status && promotionData.promotionDetail.groupList.length>0 }}">
      <block wx:for="{{ promotionData.promotionDetail.groupList }}" wx:key="{{ index }}">
        <view class="groupon-section__list-item">
          <view class="groupon-section__list-item-thumb">
            <image src="{{ item.avatar }}"></image>
          </view>
          <user-authorize
            scene="paidcontent"
            bind:next="checkWxUnion"
            data-cb="onJoinGroupon"
            class="groupon-section__list-item-go"
            data-group-alias="{{item.groupAlias}}">
            <text>去凑团</text>
          </user-authorize>
          <view class="groupon-section__list-item-detail">
            <view class="groupon-section__list-item-detail-user">{{ item.username }}</view>
            <view class="groupon-section__list-item-detail-des">
              还差
              <text class="groupon-section__list-item-detail-des-num">{{ item.remainJoinNum }}</text>
              人成团，剩余
              <text class="groupon-section__list-item-detail-des-time">{{ ongoingGroup[index] }}</text>
              结束
            </view>
          </view>
        </view>
      </block>
    </view>

    <view class="groupon-section__intro">
      <view class="zan-panel zan-panel--without-border groupon-section__intro-des">
        <view class="zan-cell zan-cell--access">
          <view class="zan-cell__bd">支付开团邀请{{ promotionData.promotionDetail.conditionNum - 1 }}人参团，人数不足自动退款</view>
          <view class="zan-cell__ft" bindtap="gotoDescDetail">玩法详情</view>
        </view>
      </view>

      <view wx:if="{{ promotionData.promotionDetail.groupType === 1 }}" class="zan-panel zan-panel--without-border groupon-section__intro-oldtonew">
        <text class="groupon-section__intro-oldtonew-logo">老带新</text>
        <text class="groupon-section__intro-oldtonew-tip">所有客户都能开团，但店铺新客才能参团哦</text>
      </view>
    </view>
  </view>
