.groupon-section {
  background-color: #fff;
  margin: 10px -15px;

  &.groupon-column {
    margin: 0;
  }

  &__title {
    display: flex;
    margin-bottom: 5px;

    &-wrap {
      display: flex;
      flex: 1;
      &-name {
        height: 40px;
        line-height: 40px;
        padding-left: 15px;
        background-image: linear-gradient(90deg, #ff9898 0%, #ff4343 100%);
        font-size: 16px;
        color: #fff;
        text-align: right;
        z-index: 1;
      }
      &-radius {
        width: 40px;
        height: 40px;
        margin-left: -15px;
        border-top-right-radius: 100%;
        border-bottom-right-radius: 100%;
        background-color: #ff4343;
        z-index: 0;
      }
    }

    &-countdown {
      float: right;
      line-height: 40px;
      padding-right: 15px;

      .count-down__container {
        .countdown-label {
          display: inline-block;
          padding-right: 10px;
          font-size: 12px;
          color: #4a4a4a;
        }
      
        .countdown--num {
          display: inline-block;
          line-height: 12px;
          padding: 1px 2px;
          margin: 0 2px;
          font-size: 10px;
          color: #ff4444;
          background-color: #ffecec;
          border-radius: 2px;
        }
      
        .countdown--split {
          display: inline-block;
          font-size: 10px;
          color: #4a4a4a;
        }
      }
    }
  }

  &__list {
    padding: 0 15px;

    &-item {
      position: relative;
      display: flex;
      margin: 10px 0px 10px 0px;
      background-color: #fff;

      &-thumb {
        width: 34px;
        height: 34px;

        image {
          width: 100%;
          height: 100%;
          border-radius: 100%;
        }
      }

      &-detail {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 20rpx;
        font-size: 12px;

        &-des {
          color: #878787;

          &-num {
            padding: 0 2px;
            color: #fc2a35;
          }
        }
      }

      &-go {
        position: absolute;
        right: 0;
        width: 60px;
        height: 34px;
        line-height: 40px;
        text-align: center;
        font-size: 12px;
        color: #ed5050;
        text {
          padding: 2px 5px;
          border-radius: 2px;
          border: 1px solid #ed5050;
        }
      }
    }
  }

  &__intro{

    &-des {

      .zan-cell {
        font-size: 12px;
        color: #333;

        .zan-cell__ft {
          color: #999;
        }
      } 
    }
    

    &-oldtonew {
      display: flex;
      justify-content: center;
      height: 34px;
      line-height: 34px;
      margin: 0 15px;
      background-color: #f8f8f8;

      &-logo {
        padding-right: 10px;
        font-size: 16px;
        color: #ff413c;
      }

      &-tip {
        font-size: 12px;
        color: #9b9b9b;
      }
    }
  }
}