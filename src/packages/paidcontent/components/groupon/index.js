import WscComponent from 'pages/common/wsc-component/index';
import { format, formatDayWithZero } from 'utils/time';
import { checkWxUnion } from '../../mixins/check-wx-union';
import countdown from '../countdown/index';

const formatCountdown = countdown.parse;
var countdownTimer = null;

WscComponent({
  properties: {
    promotionData: {
      type: Object
    },
    timeSeparator: {
      type: Array,
      default: [':', ':', ':', ':']
    },
    type: String
  },

  data: {
    ongoingGroupRemain: []
  },

  ready() {
    let ongoingGroupRemain = [];
    (this.properties.promotionData.promotionDetail.groupList || []).forEach((item) => {
      ongoingGroupRemain.push(Date.now() + item.remainTime);
    });
    this.setData({
      ongoingGroupRemain
    });
    this.runCountdown();
  },

  detached() {
    clearInterval(countdownTimer);
  },

  methods: {
    checkWxUnion,

    runCountdown() {
      this.countdown();
      countdownTimer = setInterval(() => {
        this.countdown();
      }, 1000);
    },

    countdown() {
      let remain = this.properties.promotionData.endAt - Date.now();

      let ongoingGroup = this.data.ongoingGroupRemain || [];
      if (remain < 0) {
        clearInterval(countdownTimer);
        // return this.refresh();
        return;
      }

      let countdownData = formatCountdown(remain);

      // TODO 需要对该部分改造
      let convertedOngoing = [];
      ongoingGroup.forEach((item) => {
        let remainTime = item - Date.now();
        let groupOnTime = format(remainTime).data;
        convertedOngoing.push(formatDayWithZero(groupOnTime.hour)
          + ':' + formatDayWithZero(groupOnTime.minute)
          + ':' + formatDayWithZero(groupOnTime.second));
      });

      this.setData({
        countdown: countdownData,
        ongoingGroup: convertedOngoing
      });
    },

    onJoinGroupon(evt) {
      let alias = evt.currentTarget.dataset.groupAlias;
      wx.navigateTo({
        url: `/packages/paidcontent/groupon/index?alias=${alias}`
      });
    },

    gotoDescDetail() {
      const groupType = this.data.promotionData.promotionDetail.groupType || 0;

      wx.navigateTo({
        url: `/packages/ump/pintuan/playing-instruction/index?groupType=${groupType}`,
      });
    },
  }
});
