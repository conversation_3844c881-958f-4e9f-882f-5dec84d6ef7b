.invite-btn {
  position: fixed;
  height: 30px;
  right: 0;
  bottom: 100px;
  font-size: 12px;
  display: flex;
  align-items: center;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  box-shadow: 0 0 5px #d0cdcd;
  background-color: #fff;
  padding: 0 10px;
  color: #666;
  z-index: 9;

  &__icon {
    width: 22px;
    height: 22px;
  }

  &__money {
    font-size: 16px;
    color: #333;
  }

  &::after {
    border: none;
    border-radius: 0;
  }
}

button {
  border-radius: 0;
}

button::after {
  border-radius: 0;
}

.pop {
  .invite-pop {
    padding: 0 20px;

    &__title {
      position: relative;
      padding: 15px 0;
      font-size: 16px;
      color: #333;
    }
  
    &__close {
      position: absolute;
      right: 0;
      display: initial;
      font-size: 22px;
      color: #999;
    }
  
    &__item {
      height: 105px;
      margin: 15px auto 20px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center;
      background-image: url(https://b.yzcdn.cn/fix-base64/3a142e08281ac267d5062ef62b408def2da3bc0e9a30b206b547452372c9164b.png);
  
      &-info {
        position: relative;
        display: flex;
        height: 70px;
        line-height: 70px;
        padding: 0 20px;
  
        &-desc {
          margin-right: 16px;
          font-size: 14px;
          color: #333;
  
          span {
            font-size: 20px;
          }
        }
  
        &-select {
          height: 20px;
          width: 20px;
          background-size: contain;
          position: absolute;
          right: 20px;
          top: 50%;
          margin-top: -10px;
          background-image: url(https://b.yzcdn.cn/fix-base64/c8bcd98fe830d386b601a031d8baa728dc18227871d63fa02f8a78ec75dfe471.png);
  
          &.selected {
            background-image: url(https://b.yzcdn.cn/fix-base64/1c411d375a647ba184c161a2c805accf7c4c356be64b973ece8fcdc83efb12ed.png);
          }
        }
      }
  
      &-tip {
        height: 35px;
        line-height: 35px;
        padding: 0 20px;
        font-size: 13px;
        color: #666;
      }
    }
  }
}
