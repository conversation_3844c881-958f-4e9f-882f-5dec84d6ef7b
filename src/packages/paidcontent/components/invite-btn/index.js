import WscComponent from 'pages/common/wsc-component/index';
import { getCurrentPageUrlWithParams, numberFloor } from '../../utils';

const app = getApp();

WscComponent({
  properties: {
    fxInfo: {
      type: Object,
      observer: '_onFxInfoChange'
    },
    shareInfo: Object,
    referralInfo: {
      type: 'Object',
      observer: '_onReferralChange'
    }
  },

  data: {
    isSupportFx: false,
    distributionMoney: 0,
    show: false
  },

  attached() {
    this.computeProfit();
  },

  methods: {
    doSelect(evt) {
      const type = +evt.currentTarget.dataset.index;
      this.setYZData({
        type
      });
      setTimeout(() => {
        type === 0 ? this.toRefferal() : this.toFxInvite();
      }, 0);
    },

    onClick() {
      const {
        isSupportFx,
        isRefferal
      } = this.data;

      if (isSupportFx && isRefferal) {
        this.setYZData({
          show: true
        });
        return;
      }

      if (isRefferal) {
        this.toRefferal();
        return;
      }

      this.toFxInvite();
    },

    doClose() {
      this.setYZData({
        show: false
      });
    },

    toRefferal() {
      app.globalData.PAID_CONTENT_REFERRAL_INFO = {
        path: getCurrentPageUrlWithParams()
      };
      const {
        alias,
        mediaType,
        title,
        originPrice
      } = this.data.shareInfo;
      wx.navigateTo({
        url: `/packages/paidcontent/referral-invite/index?alias=${alias}&mediaType=${mediaType || 0}&title=${title}&price=${originPrice}`
      });
      setTimeout(() => {
        this.doClose();
      }, 500);
    },

    toFxInvite() {
      app.globalData.PAID_CONTENT_FX_INFO = {
        fxInfo: this.data.fxInfo,
        shareInfo: {
          title: this.data.shareInfo.title || '',
          author: this.data.shareInfo.author || '',
          alias: this.data.shareInfo.alias,
        },
        path: getCurrentPageUrlWithParams()
      };

      wx.navigateTo({
        url: '/packages/paidcontent/invite-card/index'
      });
    },

    _onFxInfoChange(newVal) {
      this.setData({
        isSupportFx: newVal.distributionMoney > 0,
        fxProfit: (newVal.distributionMoney / 100).toFixed(2)
      });
    },

    _onReferralChange(newVal) {
      const { isSubscription } = this.data.shareInfo;
      this.setYZData({
        isRefferal: isSubscription && newVal.channelType === 6,
        commissionMoney: numberFloor(newVal.commissionMoney / 100),
        newerSubsidyPrice: numberFloor(newVal.newerSubsidyPrice / 100)
      });
    },

    computeProfit() {
      const {
        isSupportFx,
        isRefferal,
        fxInfo,
        referralInfo
      } = this.data;
      let maxProfit = 0;
      if (isRefferal && isSupportFx) {
        maxProfit = referralInfo.commissionMoney > fxInfo.distributionMoney
          ? numberFloor(referralInfo.commissionMoney / 100) : (fxInfo.distributionMoney / 100).toFixed(2);
      } else if (isRefferal) {
        maxProfit = numberFloor(referralInfo.commissionMoney / 100);
      } else {
        maxProfit = (fxInfo.distributionMoney / 100).toFixed(2);
      }

      this.setYZData({
        distributionMoney: maxProfit
      });
    }
  },
});
