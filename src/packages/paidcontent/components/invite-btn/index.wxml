<user-authorize
  class="invite-btn"
  authTypeList="{{ ['nicknameAndAvatar'] }}"
  bind:next="onClick">

  <view
    wx:if="{{ distributionMoney > 0 }}"
    class="invite-card-text">
    最高赚
    <text class="invite-btn__money">￥{{ distributionMoney }}</text>
  </view>
  <block wx:else>
    <image
      class="invite-btn__icon"
      src="https://img.yzcdn.cn/public_files/2018/05/16/72697e48986dcc76eb2596a65318d92b.png">
    </image>
    <text class="invite-card-text">生成邀请卡</text>
  </block>

</user-authorize>

<van-popup
  show="{{ show }}"
  position="bottom"
  custom-class="pop"
  bind:close="doClose">
  <view class="invite-pop">
    <view class="invite-pop__title van-hairline-bottom">
      请选择优惠方式
      <van-icon class="invite-pop__close" name="close" bindtap="doClose" />
    </view>

    <view class="invite-pop__item" bindtap="doSelect" data-index="0">
      <view class="invite-pop__item-info">
        <view class="invite-pop__item-info-desc">
          好友立减<text>￥{{ newerSubsidyPrice}}</text>
        </view>
        <view class="invite-pop__item-info-desc">
          你亦可得<text>￥{{ commissionMoney }}</text>
        </view>
        <view class="invite-pop__item-info-select {{ type === 0 ? 'selected' : '' }}"></view>
      </view>
      <view class="invite-pop__item-tip">好友首次下单享立减价，你亦可获得分享佣金</view>
    </view>

    <view class="invite-pop__item" bindtap="doSelect" data-index="1">
      <view class="invite-pop__item-info">
        <view class="invite-pop__item-info-desc">
          好友下单，你可得
          <text>￥{{ fxProfit }}</text>
        </view>
        <view class="invite-pop__item-info-select {{ type === 1 ? 'selected' : '' }}"></view>
      </view>
      <view class="invite-pop__item-tip">任何好友原价下单，你可获得分享佣金</view>
    </view>
  </view>
</van-popup>
