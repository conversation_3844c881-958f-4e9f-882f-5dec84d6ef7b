.pay-sku {
  box-sizing: border-box;
  padding: 20px 15px 0;
  width: 100%;
  background: #fff;
  color: #333;
}

.pay-sku__title {
  font-size: 16px;
  font-weight: 900;
}

.pay-sku__info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 20px;
  height: 56px;
}

.pay-sku__info--img {
  width: 100px;
  height: 100%;
  margin-right: 10px;
  border-radius: 2px;
  overflow: hidden;
}

.pay-sku__info--img image {
  width: 100%;
  height: 100%;
}

.pay-sku__info--intro {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 5px 0;
}

.pay-sku__info--intro .title {
  font-size: 14px;
}

.pay-sku__info--intro .author {
  font-size: 12px;
  color: #999;
}

.pay-sku__count {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 10px 0;
  border-bottom: .5px solid #e5e5e5;
  border-top: .5px solid #e5e5e5;
  font-size: 14px;
}

.pay-sku__price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  font-size: 14px;
}

.pay-sku__price .money {
  color: #f44;
}

.pay-sku__action {
  margin: 0 -15px;
  background-color: #f44;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
