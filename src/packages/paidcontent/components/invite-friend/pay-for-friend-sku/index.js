import { getContentInfoText, createFormId } from '../../../utils';

Component({
  properties: {
    payContentInfo: Object,
    author: {
      type: String,
      value: 'wolfdu'
    },
    mediaType: {
      type: Number,
      value: 2
    },
    videoDuration: Number
  },

  data: {
    goods: {
      // 当前 stepper 数字
      currentNum: 1,
      // 最小可到的数字
      min: 1,
      // 最大可到的数字
      max: 100,
      componentId: 'pay-for-friend-sku-stepper'
    },
    contentInfoText: '',
    sum: 0
  },

  attached() {
    this.setData({
      contentInfoText: this.getContentInfoText(),
      sum: this.data.payContentInfo.price
    });
  },

  methods: {
    nextStep(evt) {
      let formId = evt.detail.formId || '';
      createFormId({
        form_id: formId,
        business_module: 'owlGift'
      });
      this.triggerEvent('paycontent', { count: this.data.goods.currentNum });
    },

    handleZanStepperChange({
      detail: stepper
    }) {
      this.data.goods.currentNum = stepper;
      const unitPrice = this.data.payContentInfo.price * 100;
      const totalPrice = stepper * unitPrice;
      this.setData({
        goods: this.data.goods,
        sum: this.parsePrice(totalPrice)
      });
    },

    parsePrice(price) {
      return (price / 100).toFixed(2);
    },

    getContentInfoText() {
      const {
        payContentInfo, mediaType, videoDuration, audioDuration, author
      } = this.data;
      return getContentInfoText({
        isColumn: payContentInfo.isColumn || false,
        contentsCount: payContentInfo.contentsCount || 0,
        mediaType,
        videoDuration,
        audioDuration,
        author
      });
    }
  }
});
