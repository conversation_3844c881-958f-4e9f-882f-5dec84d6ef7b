<view class="pay-sku">
  <view class="pay-sku__title">付费送好友</view>
  <view class="pay-sku__info">
    <view class="pay-sku__info--img">
      <image src="{{ payContentInfo.cover }}" />
    </view>
    <view class="pay-sku__info--intro">
      <view class="title">{{ payContentInfo.title }}</view>
      <view class="author">{{ contentInfoText }}</view>
    </view>
  </view>

  <view class="pay-sku__count">
    <view>购买数量：</view>
    <view>
      <van-stepper
        value="{{ goods.currentNum }}"
        min="{{ goods.min }}"
        max="{{ goods.max }}"
        bind:change="handleZanStepperChange"
      >
      </van-stepper>
      
    </view>
  </view>

  <view wx:if="{{ !payContentInfo.isActivityStarted }}" class="pay-sku__price">
    <view>总金额：</view>
    <view class="money">￥{{ sum }}</view>
  </view>

  <form bindsubmit="nextStep" report-submit>
    <button class="pay-sku__action" formType="submit">下一步</button>
  </form>
  
</view>
