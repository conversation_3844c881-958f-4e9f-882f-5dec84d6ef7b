<view class="winning-list">
  <view class="winning-list__header">快人一步，你是第{{ receiveResult.rank }}个抢到的</view>
  <view class="winning-list__wrapper">
    <view class="winning-list__content">
      <scroll-view class="scroll-view" scroll-y lower-threshold="{{ 100 }}" bindscrolltolower="loadMore">
        <block wx:for="{{ list }}" wx:key="id">
          <view class="list-item">
            <image wx:if="{{ index < 3 }}" class="avatar-img" src="{{ indexImage[index] }}"></image>
            <view wx:else class="index-text">{{ index + 1 }}</view>
            <view class="list-item__logo">
              <image src="{{ item.userHeadPortrait || defaultAvatar }}" />
            </view>
            <view class="list-item__info">
              <view class="list-item__info-name">{{ item.userName || defaultName }}</view>
              <view class="list-item__info-time">{{ item.createdAt }}</view>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
  </view>
  <view class="winning-list__footer" wx:if="{{receiveResult}}">
    <view>已领{{ receiveResult.receivedCount }}个，还剩{{ receiveResult.unreceivedCount }}个名额</view>
  </view>
  <view class="winning-list__close" bindtap="closeMask"></view>
</view>
