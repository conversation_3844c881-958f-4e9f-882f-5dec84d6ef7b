import { formatDateTimeFix2 } from 'utils/time-utils';
import each from '@youzan/weapp-utils/lib/each';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

const app = getApp();

const RANK_INDEX_IMG = [
  'https://b.yzcdn.cn/public_files/2018/05/11/a04b049ca9f39cea43d412d3feea5899.png',
  'https://b.yzcdn.cn/public_files/2018/05/11/16b7c7d9f341b93e812a11e8f7aabc11.png',
  'https://b.yzcdn.cn/public_files/2018/05/11/ad22b8ab7f970305a3f43c22cb7b2da8.png'
];

Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
      observer: 'handleShowChange'
    },
    receiveResult: Object,
    alias: String,
    fromUserId: String,
    channelType: String,
    orderAlias: String,
    shareAlias: String
  },

  data: {
    list: [],
    page: 1,
    pageSize: 10,
    indexImage: RANK_INDEX_IMG,
    isLoading: false,
    finished: false,
    defaultAvatar: 'https://img.yzcdn.cn/public_files/2017/10/23/1321da81aa84d0539b0d5af73fbbf53b.png',
    defaultName: '匿名用户'
  },

  methods: {
    handleShowChange(newVal) {
      if (!newVal) return;
      if (!this.data.list.length) {
        wx.showLoading({ title: '正在查询领取排名' });
        const params = this.getParams();
        this.fetchRankList(params).then((res) => {
          this.setData({
            list: res.items
          });
          wx.hideLoading();
        });
      }
    },
    // 获取排名
    fetchRankList(params) {
      return new Promise((resolve, reject) => {
        app.carmen({
          method: 'GET',
          api: 'youzan.owl.share.sharerank/1.0.0/get',
          data: params,
          success: (res) => {
            res = mapKeysCase.toCamelCase(res);
            this.parseReceiveResult(res);
            resolve(res);
          },
          fail: () => {
            reject([]);
            wx.showToast({
              title: '获取领取礼物排名失败',
              icon: 'none'
            });
          }
        });
      });
    },

    parseReceiveResult(data) {
      each(data.items, item => {
        item.createdAt = formatDateTimeFix2(new Date(item.createdAt));
      });

      return data;
    },
    closeMask() {
      this.triggerEvent('close-mask');
    },
    // 下拉滚动加载更多
    loadMore() {
      if (!this.data.finished) {
        wx.showLoading({ title: '正在查询领取排名' });
        const page = this.data.page++;
        const params = this.getParams(page);
        this.fetchRankList(params).then((res) => {
          const rankList = res.items;
          const oldList = this.data.list;
          const finished = rankList.length < this.pageSize;
          this.setData({
            list: oldList.concat(rankList),
            finished,
            page
          });
          wx.hideLoading();
        });
      }
    },
    // 领取排名参数
    getParams() {
      return {
        alias: this.data.alias,
        from_user_id: this.data.fromUserId,
        channel_type: this.data.channelType,
        order_alias: this.data.orderAlias,
        share_alias: this.data.shareAlias,
        pageSize: this.data.pageSize,
        page: this.data.page
      };
    }
  }
});
