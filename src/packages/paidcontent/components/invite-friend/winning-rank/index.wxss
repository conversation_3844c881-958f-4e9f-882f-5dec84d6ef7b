.winning-list {
  box-sizing: border-box;
  width: 100%;
  padding: 0 60rpx;
  border-radius: 8rpx;
  text-align: center;
}

.winning-list__header {
  width: 100%;
  height: 152rpx;
  background-image: url(https://img.yzcdn.cn/public_files/2018/05/11/530de156d458d69267cade58a2eea311.png);
  background-position: top left;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: #fff;
  font-size: 32rpx;
  line-height: 152rpx;
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
  overflow:hidden
}

.winning-list__wrapper {
  position: relative;
  height: 636rpx;
  overflow-y: scroll;
  background: #fff;
  padding: 40rpx 30rpx;
  text-align: left;
}

.winning-list__wrapper::-webkit-scrollbar {
  display: none;
}

.list-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 44rpx;
  background-position: center left;
  background-repeat: no-repeat;
  background-size: 40rpx 40rpx;
}

.list-item:last-child {
  margin-bottom: 0;
}

.list-item__logo {
  width: 72rpx;
  height: 72rpx;
  overflow: hidden;
  border-radius: 100%;
}

.list-item__logo image {
  width: 100%;
  height: 72rpx;
}

.list-item__info {
  margin-left: 20rpx;
}

.list-item__info-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.list-item__info-time {
  font-size: 24rpx;
  color: #999;
}

.winning-list__footer {
  position: relative;
  top: -4rpx;
  width: 100%;
  padding: 24rpx 0 20rpx 0;
  color: #999;
  font-size: 24rpx;
  background: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, .2);
  border-bottom-left-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}

.winning-list__close {
  margin: 40rpx auto 0;
  width: 70rpx;
  height: 70rpx;
  background-image: url(https://img.yzcdn.cn/public_files/2018/05/16/aa990c106f34a3954d4d25c755fda505.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100%;
}

.avatar-img {
  height: 44rpx;
  width: 44rpx;
  margin-right: 16rpx;
}

.index-text {
  width: 42rpx;
  height: 42rpx;
  margin: 20rpx 10rpx 20rpx 10rpx;
  color: #999;
}

.scroll-view {
  height: 636rpx;
}
