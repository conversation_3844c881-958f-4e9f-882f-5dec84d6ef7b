<view class="giving-friend">
  <view class="giving-friend__content">
    <view class="giving-friend__content-text">
      <view class="h3" wx:if="{{ isReceiveOver }}">免费名额已用完，付费购买送给TA</view>
      <block wx:elif="{{ isShareGift }}">
        <view class="h2">喊好友来收礼</view>
        <view class="h3" >
          <view>前{{ everyContentFriendCount }}名领取的好友可免费获得该内容</view>
        </view>
      </block>
      <block wx:else>
        <view class="h2">免费请好友看</view>
        <view class="h3" >
          <view>前{{ everyContentFriendCount }}名领取的好友可免费获得该内容</view>
          <view>已有<text class="red">{{ receivedCount }}</text>个好友领取</view>
        </view>
      </block>
    </view>
    <form 
      wx:if="{{ isReceiveOver }}"
      bindsubmit="showSku" 
      report-submit>
      <button class="giving-friend__content-action" formType="submit">购买内容送好友</button>
        <!-- <view class="giving-friend__content-action" formType="submit">购买内容送好友</view> -->
    </form>

    <form 
      wx:elif="{{ isShareGift }}"
      bindsubmit="shareGift" 
      report-submit>
      <button class="giving-friend__content-action" formType="submit">喊好友来收礼</button>
      <!-- <view class="giving-friend__content-action" formType="submit">喊好友来收礼</view> -->
    </form>
    
    <form 
      wx:else 
      bindsubmit="showSku" 
      report-submit>
      <button class="giving-friend__content-action" formType="submit">发送给好友</button>
      <!-- <view class="giving-friend__content-action" formType="submit">发送给好友</view> -->
    </form>
    
    <view class="giving-friend__content-tip" wx:if="{{ isFx }}">好友购买专栏赚<text class="red">￥{{ distributionMoney }}</text></view>
  </view>
  <view class="giving-friend__close" bindtap="closeMask"></view>
</view>
