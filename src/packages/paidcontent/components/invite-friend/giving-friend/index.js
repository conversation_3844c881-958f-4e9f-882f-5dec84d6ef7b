import { createFormId } from '../../../utils';

Component({
  properties: {
    // 总数量
    everyContentFriendCount: Number,
    // 已领取数量
    receivedCount: Number,
    // 分销金额
    earnMoney: {
      type: Number,
      observer: 'formatEarnMoney'
    },
    // 分销
    isFx: Boolean,
    isShareGift: Boolean
  },

  data: {
    isReceiveOver: false,
    distributionMoney: 0
  },

  attached() {
    const { receivedCount, everyContentFriendCount } = this.data;
    const isReceiveOver = everyContentFriendCount === receivedCount;
    this.setData({
      isReceiveOver
    });
  },

  methods: {
    closeMask() {
      this.triggerEvent('close-mask');
    },
    showSku(evt) {
      let formId = evt.detail.formId || '';
      createFormId({
        form_id: formId,
        business_module: 'owlGift'
      });
      this.triggerEvent('show-pay-sku');
    },
    shareGift(evt) {
      let formId = evt.detail.formId || '';
      createFormId({
        form_id: formId,
        business_module: 'owlShare'
      });
      this.triggerEvent('share-gift');
    },
    formatEarnMoney(newValue) {
      this.setData({
        distributionMoney: (newValue / 100).toFixed(2)
      });
    }
  }
});
