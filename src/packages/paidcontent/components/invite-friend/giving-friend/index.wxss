.red {
  color: #f44;
}

.giving-friend {
  box-sizing: border-box;
  width: 100%;
  padding: 0 60rpx;
}

.giving-friend__content {
  padding: 60rpx 40rpx;
  background: #fff;
  border-radius: 8rpx;
}

.giving-friend__content-text {
  padding-top: 180rpx;
  background-image: url(https://img.yzcdn.cn/public_files/2018/05/18/6f096e093379fa9609c931a9b42fcf1f.png);
  background-position: center top;
  background-repeat: no-repeat;
  background-size: 100px;
}

.giving-friend__content-text .h2 {
  font-size: 32rpx;
  color: #333;
  font-weight: 900;
  text-align: center;
}

.giving-friend__content-text .h3 {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
  font-weight: 300;
  text-align: center;
}

.giving-friend__content-action {
  line-height: 1;
  padding: 20rpx 0;
  margin-top: 40rpx;
  background: #f44;
  font-size: 24rpx;
  font-weight: 300;
  color: #fff;
  border-radius: 4rpx;
  text-align: center;
}

.giving-friend__content-tip {
  margin-top: 10rpx;
  font-size: 20rpx;
  color: #999;
  font-weight: 100;
  text-align: center;
}

.giving-friend__close {
  margin: 60rpx auto 0;
  width: 70rpx;
  height: 70rpx;
  background-image: url(https://img.yzcdn.cn/public_files/2018/05/16/aa990c106f34a3954d4d25c755fda505.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100%;
}
