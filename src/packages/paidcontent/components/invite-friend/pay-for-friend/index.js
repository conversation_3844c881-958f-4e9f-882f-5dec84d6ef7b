
Component({
  properties: {
    cover: {
      type: String,
      value: ''
    },
    everyContentFriendCount: {
      type: Number,
      value: 10
    },
    receivedCount: {
      type: Number,
      value: 8
    },
    distributionMoney: {
      type: Number,
      value: 200
    }
  },

  data: {
  },

  methods: {
    closeMask() {
      this.triggerEvent('close-mask');
    },
    showSku() {
      this.triggerEvent('show-pay-sku');
    }
  }
});
