.share-friend {
  box-sizing: border-box;
  width: 100%;
  padding: 0 60rpx;
}

.share-friend__card {
  position: relative;
  width: 100%;
  border-radius: 4px;
  background: #fff;
  overflow: hidden;
}

.share-friend__card::after {
  position: absolute;
  top: 20px;
  right: 0;
  padding: 2px 8px;
  content: "";
  display: block;
  background: rgba(0, 0, 0, .8);
  color: #fff;
  font-size: 10px;
  border-top-left-radius: 9px;
  border-bottom-left-radius: 9px;
  font-weight: 100;
}

.share-friend__card.text::after {
  content: "文本";
}

.share-friend__card.audio::after {
  content: "音频";
}

.share-friend__card.video::after {
  content: "视频";
}

.share-friend__cover {
  width: 100%;
  max-height: 320rpx;
  overflow: hidden;
}

.share-friend__cover image {
  width: 100%;
}

.share-friend__content {
  padding: 15px;
}

.share-friend__content-title {
  font-size: 14px;
  padding-bottom: 2px;
}
.share-friend__content-column,
.share-friend__content-author {
  padding: 2px;
  font-size: 10px;
  color: #999;
  font-weight: 100;
}

.share-friend__content-action {
  padding: 10px 0;
  margin-top: 40px;
  background: #f44;
  font-size: 14px;
  color: #fff;
  border-radius: 2px;
  text-align: center;
}

.share-friend__content-tip {
  margin-top: 2px;
  font-size: 10px;
  color: #999;
  font-weight: 100;
  text-align: center;
}

.share-friend__close {
  margin: 30px auto 0;
  width: 35px;
  height: 35px;
  background-image: url(https://img.yzcdn.cn/public_files/2018/05/16/aa990c106f34a3954d4d25c755fda505.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100%;
}
