import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import upload from 'utils/upload';
import authorize from '@/helpers/authorize';
import { fetchUltraCode } from '@/helpers/fetch';
import { INVITE_TEXT_MAP, MEDIA_TEXT_MAP, DEFAULT_USER_INFO } from '../../../constants';
import { getContentInfoText } from '../../../utils';

Component({
  properties: {
    // 触发分享图预览的流程
    observerStatus: {
      type: Boolean,
      value: false,
      observer: 'handleShowChange'
    },
    // 自定义 canvasId
    canvasId: {
      type: String,
      value: 'inviteCardCanvas'
    },
    cover: {
      type: String,
      value: 'https://img.yzcdn.cn/upload_files/2018/05/09/FmM3ByBCIv93hTGFx7fYgoUBJlq-.jpeg'
    },
    isColumn: {
      type: Boolean,
      value: true
    },
    title: {
      type: String,
      value: 'title'
    },
    author: {
      type: String,
      value: 'author'
    },
    contentsCount: {
      type: Number,
      value: 10
    },
    mediaType: Number,
    audioDuration: Number,
    videoDuration: Number,
    userName: {
      type: String,
      value: 'userName'
    },
    avatar: String,
    isDistribution: {
      type: Boolean,
      value: false
    },
    distributionMoney: Number,
    inviteCardQrUrl: String,
    alias: String
  },

  data: {
    // 邀请卡
    inviteCardSrc: '',
    // 控制 canvas 是否展示
    showCanvas: false,
    // 邀请卡二维码src
    inviteCardQrSrc: ''
  },

  methods: {
    handleShowChange(newVal) {
      if (!newVal || this.data.inviteCardQrSrc) return;
      wx.showLoading({ title: '正在生成' });
      this.setData({
        showCanvas: true
      }, this.draw.bind(this));
    },

    draw() {
      this.getQrImgSrc()
        .then(this.uploadAvatar.bind(this))
        .then(this.drawInviteCard.bind(this))
        .then(this.createTempPath.bind(this))
        .then((src) => {
          this.setData({
            inviteCardSrc: src,
            showCanvas: false
          });
          wx.hideLoading();
        });
    },

    uploadAvatar() {
      return new Promise((resolve) => {
        if (this.data.avatar) {
          wxGetImageInfo(this.data.avatar).then((res) => {
            upload({
              file: res.path,
              success: (res) => {
                this.setData({
                  avatar: res.attachmentPath
                });
                resolve();
              },
              fail: (res) => {
                console.warn('上传头像失败', res);
                this.setData({
                  avatar: DEFAULT_USER_INFO.avatar
                });
                resolve();
              }
            });
          });
        } else {
          this.setData({
            avatar: DEFAULT_USER_INFO.avatar
          });
          resolve();
        }
      });
    },

    // 生成唯一的图片key
    generateKey() {
      return makeRandomString(8) + (new Date()).getTime();
    },

    // 获取二维码src
    getQrImgSrc() {
      return new Promise((resolve) => {
        fetchUltraCode(this.data.inviteCardQrUrl, {}).then((qrSrc) => {
          this.setData({
            inviteCardQrSrc: qrSrc
          });
          resolve(qrSrc);
        });
      });
    },

    // 获取二维码地址
    fetchQRCode(path) {
      const app = getApp();
      const kdtId = app.getKdtId();
      return new Promise((resolve, reject) => {
        path = encodeURIComponent(path);
        app.carmen({
          api: 'youzan.shop.weapp/1.0.0/qrcode',
          config: { skipKdtId: true },
          query: {
            path,
            kdt_id: kdtId
          },
          success: (res) => {
            let url = res.attachment_url || '';
            if (typeof url === 'string') {
              url = url.replace(/^http:/, 'https:');
            }
            resolve(url);
          },
          fail: () => {
            reject();
          }
        });
      });
    },

    getContentTitle(title) {
      if (title.length > 15) {
        return `${title.slice(0, 14)}...`;
      }
      return title;
    },

    drawInviteCard() {
      return new Promise((resolve) => {
        const cardBackgroundUrl = 'https://img.yzcdn.cn/public_files/2018/05/07/3b773de852d622ec59a6ca4ff719e2bd.png';
        Promise.all([
          wxGetImageInfo(cardBackgroundUrl),
          wxGetImageInfo(this.data.cover),
          // wxGetImageInfo(this.data.inviteCardQrSrc),
          Promise.resolve(this.data.inviteCardQrSrc),
          wxGetImageInfo(this.data.avatar)
        ]).then((res) => {
          const ctx = wx.createCanvasContext(this.data.canvasId, this);
          console.log(res);

          // 绘制背景图
          ctx.drawImage(res[0].path, 0, 0, 280, 460);

          // 绘制商品图
          this.drawGoodsImage(0, 0, 280, 160, 4, res[1].path, ctx);

          // 绘制右上角内容类型
          this.drawContentType(this.getContentTypeText(), ctx);

          // 内容title
          const contentTitle = this.getContentTitle(this.data.title);
          fillText(contentTitle, 20, 190, '#4A4A4A', '500 16px PingFang SC', 'start', ctx);
          // 内容描述信息
          const contentInfoText = this.getContentInfoText();
          fillText(contentInfoText, 20, 210, '#999999', '12px PingFang SC', 'start', ctx);

          // 绘制二维码
          // 二维码位置
          const beginHeight = 260;
          ctx.drawImage(res[2], 100, beginHeight, 80, 80);
          const tips = '长按扫码查看内容';
          fillText(tips, 140, beginHeight + 100, '#999999', '10px PingFang SC', 'center', ctx);

          // 头像绘制
          this.drawAvatarImage(20, 404, 20, res[3].path, ctx);
          // 头像信息
          const userName = this.getUserName(this.data.userName);
          fillText(userName, 70, 424, '#4A4A4A', '16px PingFang SC', 'start', ctx);
          fillText(this.getInviteText(), 70, 444, '#999999', '12px PingFang SC', 'start', ctx);

          ctx.draw();
          resolve();
        });
      });
    },

    getUserName(name) {
      if (name.length > 20) {
        return `${name.slice(0, 19)}...`;
      }
      return name || DEFAULT_USER_INFO.name;
    },

    getContentTypeText() {
      if (this.data.isColumn) {
        return '专栏';
      }
      return MEDIA_TEXT_MAP[this.data.mediaType];
    },

    drawContentType(contentType, context) {
      const width = 44;
      // 计算完容器宽度
      const beginX = 280 - width;
      const beginY = 20;
      context.save();
      context.fillStyle = 'rgba(0, 0, 0, 0.4)';
      drawLeftRoundRect(beginX, beginY, width, 20, 10, context);
      context.fillStyle = '#fff';
      context.font = '12px Arial';
      context.textAlign = 'center';
      context.fillText(contentType, beginX + (width / 2) + 2, beginY + 14);
      context.restore();
    },

    getInviteText() {
      if (this.data.isColumn) {
        return INVITE_TEXT_MAP.COLUMN;
      }
      return INVITE_TEXT_MAP[this.data.mediaType];
    },

    drawAvatarImage(x, y, radius, img, context) {
      context.save();
      circleRect(context, x, y, radius);
      context.clip();
      const d = 2 * radius;
      context.drawImage(img, x, y, d, d);
      context.restore();
    },

    // 获取商品内容信息
    getContentInfoText() {
      const {
        isColumn,
        contentsCount,
        mediaType,
        videoDuration,
        audioDuration,
        author
      } = this.data;

      return getContentInfoText({
        isColumn,
        contentsCount,
        mediaType,
        videoDuration,
        audioDuration,
        author
      });
    },

    // 绘制商品图片
    drawGoodsImage(x, y, width, height, radius, img, context) {
      context.save();
      drawTopRoundRect(x, y, width, height, radius, context);
      context.clip();
      context.drawImage(img, x, y, width, height);
      context.restore();
    },

    // 生成分享图临时路径
    createTempPath() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          wx.canvasToTempFilePath({
            canvasId: this.data.canvasId,
            success: (res) => {
              resolve(res.tempFilePath);
            },
            fail: reject,
            complete: () => {
              wx.hideLoading();
              this.setData({
                show: false
              });
            }
          }, this);
        }, 500);
      });
    },
    // 关闭遮罩层
    closeMask() {
      this.triggerEvent('close-mask');
    },

    // 保存图片
    saveCard() {
      const { inviteCardSrc } = this.data;
      if (!inviteCardSrc) return;
      wx.showLoading({
        title: '保存中'
      });
      authorize('scope.writePhotosAlbum')
        .then(this.saveImage.bind(null, inviteCardSrc))
        .then(() => {
          wx.hideLoading();
          wx.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000
          });
          this.triggerEvent('saved');
        })
        .catch(() => {
          wx.showToast({
            title: '保存失败，请检查保存到相册的权限',
            icon: 'none',
            duration: 2000
          });
        });
    },

    saveImage(tempFilePath) {
      return new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: tempFilePath,
          success: resolve,
          fail: reject
        });
      });
    },

    // 发送给好友
    sendFriend() {
      this.triggerEvent('share-friend');
    }
  }
});

/**
 * top圆角矩形
 */
function drawTopRoundRect(x, y, width, height, radius, cxt) {
  cxt.beginPath();
  cxt.arc(x + radius, y + radius, radius, Math.PI, Math.PI * (3 / 2));
  cxt.lineTo((width - radius) + x, y);
  cxt.arc((width - radius) + x, radius + y, radius, Math.PI * (3 / 2), Math.PI * 2);
  cxt.lineTo(width + x, (height + y));
  cxt.lineTo(x, height + y);
  cxt.closePath();
}

/**
 * left圆角矩形
 */
function drawLeftRoundRect(x, y, width, height, radius, cxt) {
  cxt.beginPath();
  cxt.arc(x + radius, y + radius, radius, Math.PI, Math.PI * (3 / 2));
  cxt.lineTo(width + x, y);
  cxt.lineTo(width + x, (height + y));
  cxt.lineTo(radius + x, height + y);
  cxt.arc(radius + x, (height - radius) + y, radius, Math.PI * (1 / 2), Math.PI);
  cxt.closePath();
  cxt.fill();
}

/**
 * fill text
 * @param {String} text 内容
 * @param {Number} x 坐标
 * @param {Number} y 坐标
 * @param {*} context ctx
 */
function fillText(text, x, y, fillStyle, font, textAlign, context) {
  context.save();
  setFillTextOptions(fillStyle, font, textAlign, context);
  if (text.length < 22) {
    // 单行显示
    context.fillText(text, x, y);
  } else { // 多行文字处理
    const textTop = text.slice(0, 22);
    let textBottom;
    if (text.length > 44) {
      textBottom = text.slice(22, 42) + '...';
    } else {
      textBottom = text.slice(22, 44);
    }
    context.fillText(textTop, x, y);
    context.fillText(textBottom, x, y + 20);
  }
  context.restore();
}

function setFillTextOptions(fillStyle, font, textAlign, ctx) {
  if (fillStyle) {
    ctx.fillStyle = fillStyle;
  }
  if (font) {
    ctx.font = font;
  }
  if (textAlign) {
    ctx.textAlign = textAlign;
  }
  return ctx;
}

// 画圆形图片
function circleRect(ctx, x, y, r) {
  var cx = x + r;
  var cy = y + r;
  ctx.beginPath();
  ctx.arc(cx, cy, r, 0, 2 * Math.PI);
  ctx.closePath();
}

// 下载图片获得临时路径
function wxGetImageInfo(src) {
  return new Promise((resolve, reject) => {
    if (typeof src === 'string') {
      src = src.replace(/^http:/, 'https:');
    }
    wx.getImageInfo({
      src,
      success: (res) => {
        resolve(res);
      },
      fail: (e) => {
        reject(e);
      }
    });
  });
}
