.wrap {
  background-color: rgba(0, 0, 0, 0);
  display: flex;
  justify-content: center;
}

.wrap .invite-card {
  height: 920rpx;
  width: 560rpx;
}

.wrap .content {
  height: 920rpx;
  width: 560rpx;
  position: relative;
}

.close-card {
  position: absolute;
  top: -26rpx;
  right: -26rpx;
}

.close-card .close-image {
  width: 52rpx;
  height: 52rpx;
}

.press-save {
  position: absolute;
}

.press-save-btn {
  width: 560rpx;
  height: 80rpx;
  position: relative;
  color: #e5e5e5;
  text-align: center;
  margin: 0 auto;
}

.press-save-btn .action-btn {
  display:flex;
  justify-content:space-between;
}

.action-btn .save-btn {
  width:216rpx;
  border:1rpx solid #fff;
  height:80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-btn .send-btn {
  width:316rpx;
  height:80rpx;
  background: #fff;
  color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
}

.desc {
  margin-top: 20rpx;
}

.money {
  color: red;
}

.zan-popup__container {
  background: rgba(0, 0, 0, 0);
}

.share-canvas {
  position: fixed;
  top: -1000px;
}
