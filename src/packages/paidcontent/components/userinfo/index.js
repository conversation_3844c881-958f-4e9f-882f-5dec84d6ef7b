const app = getApp();

Component({
  properties: {
    force: {
      type: Boolean,
      value: true
    },
    immediate: {
      type: Boolean,
      value: true
    },
    show: {
      type: Boolean,
      value: false,
      observer(val) {
        if (val) this.getUserInfo();
      }
    },
    message: {
      type: String,
      value: '请允许获取你的昵称和用户名以使用直播功能'
    }
  },

  data: {
    innerVisible: false
  },

  ready() {
    if (this.data.immediate) this.getUserInfo();
  },

  methods: {
    onGotUserInfo() {
      app.getUserInfo(res => {
        this.triggerEvent('userinfo', res.userInfo);
        this.setData({
          innerVisible: false
        });
      }, () => {
        let innerVisible = false;
        if (this.data.force) {
          innerVisible = true;
        } else {
          this.triggerEvent('cancel');
        }
        this.setData({
          innerVisible
        });
      });
    },

    handleCancel() {
      this.setData({
        innerVisible: false
      });
      this.triggerEvent('cancel');
    }
  }
});
