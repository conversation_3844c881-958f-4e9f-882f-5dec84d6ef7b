.content {
  position: relative;
  box-sizing: border-box;
  margin-top: 30px;
  width: 100%;
  background: #fff;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  box-shadow: 0 0 5px #eee;
}

.content::after {
  position: absolute;
  bottom: -20px;
  left: 0;
  box-sizing: border-box;
  display: block;
  width: 100%;
  content: "";
  border: 10px solid #f4f4f4;
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
}

.content .cover {
  position: relative;
  width: 100%;
  height: 280rpx;
}

.content .cover .present-icon {
  position: absolute;
  width: 150rpx;
  height: 150rpx;
  top: -28rpx;
  left: -28rpx;
}

.content .cover .receive-over-img {
  width: 108rpx;
  height: 108rpx;
  float: right;
  margin-top: -20rpx;
}

.content .coverText {
  position: absolute;
  top: 20px;
  right: 2%;
  padding: 2px 8px;
  content: "";
  display: block;
  background: rgba(0, 0, 0, .8);
  color: #fff;
  font-size: 10px;
  border-top-left-radius: 9px;
  border-bottom-left-radius: 9px;
  font-weight: 100;
}

.content .cover .cover-image {
  width: 96%;
  height: 280rpx;
  margin: 2%;
}

.content .intro {
  padding: 15px;
}

.content .intro .title {
  font-size: 14px;
  padding-bottom: 2px;
}

.content .intro .column,
.content .intro .author {
  padding: 2px;
  font-size: 10px;
  color: #999;
  font-weight: 100;
}

.content .intro .origin {
  margin-top: 45px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.origin .logo {
  width: 48rpx;
  height: 48rpx;
  border-radius: 100%;
  overflow: hidden;
}

.origin .logo image {
  width: 100%;
  height: 48rpx;
}

.origin .name {
  margin-left: 8px;
  font-size: 10px;
  color: #999;
  font-weight: 100;
}

.origin .name text {
  font-weight: 900;
}
