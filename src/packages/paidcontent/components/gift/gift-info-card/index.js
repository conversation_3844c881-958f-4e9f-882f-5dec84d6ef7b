import { RECEIVE_GIFT_STATUS, MEDIA_TEXT_MAP } from '../../../constants';
import { getContentInfoText } from '../../../utils';

Component({
  properties: {
    cover: String,
    title: String,
    author: String,
    isColumn: Boolean,
    mediaType: Number,
    audioDuration: Number,
    videoDuration: Number,
    avatar: String,
    userName: String,
    receivGiftStatus: Number,
    contentsCount: Number
  },

  data: {
    contentInfoText: '',
    contentType: '',
    userAvatar: 'https://img.yzcdn.cn/public_files/2017/10/23/1321da81aa84d0539b0d5af73fbbf53b.png',
    isShowGiftImg: false,
    isShowReceiveOverImg: false
  },

  attached() {
    this.setData({
      contentInfoText: this._getContentInfoText(),
      contentType: this._getContentType(),
      userAvatar: this._getAvatar(),
      isShowGiftImg: this.data.receivGiftStatus !== RECEIVE_GIFT_STATUS.PAIED,
      isShowReceiveOverImg: this.data.receivGiftStatus === RECEIVE_GIFT_STATUS.PRESENT_FAIL
    });
  },

  methods: {
    _getContentInfoText() {
      const {
        isColumn,
        contentsCount,
        mediaType,
        videoDuration,
        audioDuration,
        author
      } = this.data;
      return getContentInfoText({
        isColumn,
        isUpdate: contentsCount,
        mediaType,
        videoDuration,
        audioDuration,
        author
      });
    },
    _getContentType() {
      if (this.data.isColumn) {
        return '专栏';
      }
      return MEDIA_TEXT_MAP[this.data.mediaType] || '';
    },
    _getAvatar() {
      return this.data.avatar || 'https://img.yzcdn.cn/public_files/2017/10/23/1321da81aa84d0539b0d5af73fbbf53b.png';
    }
  }
});
