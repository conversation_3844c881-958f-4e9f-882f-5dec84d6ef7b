.item {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item .person {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.person .logo {
  width: 88rpx;
  height: 88rpx;
  border-radius: 100%;
  overflow: hidden;
}

.person .logo image {
  width: 100%;
  height: 88rpx;
}

.person .info {
  margin-left: 10px;
}

.person .info .name,
.person .info .comment {
  font-size: 14px;
}

.person .info .comment {
  margin-top: 5px;
}

.person .info .time {
  margin-left: 10px;
  font-size: 10px;
  color: #999;
  font-weight: 100;
}

.quantity {
  font-size: 16px;
  color: #f44;
}
