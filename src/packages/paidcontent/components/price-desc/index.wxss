.price-wrapper {
  padding: 32rpx;
}

.price-title-wrapper {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  line-height: 1.29;
  color: #323233;
}
.line {
  flex: 1;
  height: 3rpx;
  background: #ebedf0;
}

.title {
  padding: 0 24rpx;
  display: flex;
  align-items: center;
}
.van-icon-arrow {
  font-size: 24rpx;
  color: #dcdee0;
  transform: rotate(-90deg) scale(0.6333333);
  transition: 0.3s all;
}
.van-icon-arrow.expand {
  transform: rotate(90deg) scale(0.6333333);
}

.price-desc-wrapper {
  border-radius: 8rpx;
  background-color: #f7f8fa;
  margin-top: 40rpx;
  padding: 24rpx 16rpx;
  font-size: 24rpx;
  line-height: 1.33;
  color: #969799;
}
.paragraph {
  margin-bottom: 24rpx;
}
.paragraph:last-child {
  margin-bottom: 0rpx;
}
.em {
  color: #323233;
}
