<view class="price-wrapper">
  <view class="price-title-wrapper">
    <view class="line" />
    <view class="title" bindtap="onExpand">
      价格说明
      <view class="{{isVisible ? 'expand van-icon-arrow': 'van-icon-arrow'}}">
        <van-icon name="arrow" />
      </view>
    </view>
    <view class="line" />
  </view>
  <view wx:if="{{ isVisible }}" class="price-desc-wrapper ">
    <view class="paragraph">
      <text class="em">划线价格：</text>
      划线的价格可能是课程的原价、曾经展示过的销售价等，仅供您参考。
    </view>
    <view class="paragraph">
      <text class="em">未划线价格：</text>
      未划线的价格是课程的销售标价，具体的成交价格可能因会员使用优惠券、积分等发生变化，最终以订单结算价格为准。
    </view>
    <view>*此说明仅当出现价格比较时有效。若课程针对划线价格进行了特殊说明，以特殊说明为准。</view>
  </view>
</view>