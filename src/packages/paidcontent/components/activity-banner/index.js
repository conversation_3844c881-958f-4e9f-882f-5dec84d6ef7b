import WscComponent from 'pages/common/wsc-component/index';
import countdown from '../countdown/index';

const formatCountdown = countdown.parse;

var countdownTimer = null;

WscComponent({
  properties: {
    activityBannerInfo: {
      type: Object,
      value: {},
    },

    originPrice: {
      type: Number,
    }
  },

  data: {
    activePriceWithInt: '',
    activePriceWithDecimal: '',
    originPriceDisplay: '',
  },

  ready() {
    this.formatData();
    this.runCountdown();
  },

  detached() {
    clearInterval(countdownTimer);
  },

  methods: {
    formatData() {
      const { activityPrice } = this.data.activityBannerInfo;
      const activePriceWithInt = (activityPrice / 100).toString().split('.')[0];
      let activePriceWithDecimal = activityPrice % 100;
      const originPriceDisplay = this.data.originPrice / 100;
      if (+activePriceWithDecimal < 10) {
        activePriceWithDecimal = '0' + activePriceWithDecimal;
      } else if (+activePriceWithDecimal % 10 === 0) {
        activePriceWithDecimal = +activePriceWithDecimal / 10;
      }
      this.setData({
        activePriceWithInt,
        activePriceWithDecimal,
        originPriceDisplay,
      });
    },

    runCountdown() {
      this.countdown();
      let _this = this;
      countdownTimer = setInterval(() => {
        _this.countdown();
      }, 1000);
    },

    countdown() {
      const remain = this.data.activityBannerInfo.endAt - Date.now();
      if (remain < 0) {
        clearInterval(countdownTimer);
        // return this.refresh();
        return;
      }

      const countdownData = formatCountdown(remain);

      this.setData({
        countdown: countdownData,
      });
    },
  },

});
