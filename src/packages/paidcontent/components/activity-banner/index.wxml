<import src="../countdown/index.wxml" />

<theme-view
  gradient
  gradient-deg="45"
  color="main-text"
  custom-class="activity-banner"
>

<view class="activity-banner__container">
  <view class="activity-banner__price">
    <view class="activity-banner__price-current">
      <span class="int">
          ￥{{ activePriceWithInt }}
      </span>
      <span w:if="{{activePriceWithDecimal}}" class="decimal">
          .{{activePriceWithDecimal}}
      </span>
    </view>
    <view class="activity-banner__price-origin">
      <view class="activity-banner__price-origin-del">
        原价￥{{ originPriceDisplay }}
      </view>
      <van-tag
        class="activity-banner__price-origin-tag"
        text-color="#fff"
        color="rgba(0, 0, 0, .5)"
      >{{ activityBannerInfo.tagName }}</van-tag>
    </view>
  </view>
  <view class="activity-banner__date">
    <!-- <p class="activity-banner__date-label">
      {{ dateLabel }}
    </p> -->
    <template is="countdown" data="{{ countdown }}" />
  </view>
</view>
</theme-view>