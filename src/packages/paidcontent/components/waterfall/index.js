import WscComponent from 'pages/common/wsc-component/index';

const LOAD_OFFSET = 200;

WscComponent({
  properties: {
    listId: String,
    list: Array,
    total: Number,
    noDataTip: {
      type: String,
      value: ''
    },
    loading: Boolean
  },

  disableLoadCheck() {
    let type = this.data.tab.selectedId;
    if (this.data.type) {
      type = this.data.type;
    }
    return (
      this.$checkRunning
      || this.data[type].noMore
      || this.data[type].noData
      || this.data[type].loading
    );
  },

  onPageScroll() {
    if (this.disableLoadCheck()) return;
    this.$checkRunning = true;
    setTimeout(() => {
      this.checkPagination();
      this.$checkRunning = false;
    }, 400);
  },

  checkPagination() {
    const query = wx.createSelectorQuery().in(this);

    query.select(this.data.listId).boundingClientRect((res) => {
      if (!res) return;
      if (res.bottom < this.data.windowHeight + LOAD_OFFSET) {
        this.loadData();
      }
    }).exec();
  }
});
