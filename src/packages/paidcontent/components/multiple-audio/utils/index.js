export function SafeDate(arg = '') {
  if (!arg) return new Date();
  return typeof arg === 'string' ? new Date(arg.replace(/-/g, '/')) : new Date(arg);
}

export function formatDate(date = '', sep = '-') {
  const dateObj = new SafeDate(date);
  const y = dateObj.getFullYear();
  const m = dateObj.getMonth() + 1;
  const d = dateObj.getDate();
  return `${y}${sep}${m < 10 ? `0${m}` : m}${sep}${d < 10 ? `0${d}` : d}`;
}

/**
 * 时间在1小时内显示分钟（例如12分钟前）
 * 时间在1-24小时内，则显示小时（例如5小时前）
 * 时间在1-7天内，则显示具体星期（星期三）
 * 时间在7天外，则显示具体年月日（2018-02-21）
 *
 * @param {Number} dateTime
 */
export function formatDateTime(dateTime) {
  const timestamp = +new SafeDate(+dateTime);
  const now = Date.now();
  const distance = now - timestamp;

  if (distance < 3600000) {
    // 小于1小时
    const m = parseInt(distance / 60000, 10);
    return m === 0 ? '刚刚' : `${m}分钟前`;
  }
  if (distance < 3600000 * 24) {
    // 小于24小时
    return `${parseInt(distance / 3600000, 10)}小时前`;
  }
  if (distance < 3600000 * 24 * 7) {
    const weekText = ['日', '一', '二', '三', '四', '五', '六'];
    return `星期${weekText[new SafeDate(dateTime).getDay()]}`;
  }

  return new SafeDate(dateTime)
    .toLocaleDateString()
    .replace(/\/(\d)(?!\d)/g, '-0$1')
    .replace(/\//g, '-');
}

export function pad(number) {
  return number < 10 ? `0${number}` : `${number}`;
}

/**
 * seconds -> '0:00'
 *
 * @param {Number} seconds
 */
export function formatTime(seconds) {
  if (!seconds) return '0:00';

  let minute = parseInt(seconds / 60, 10);
  let second = Math.ceil(seconds % 60, 10);
  if (second === 60) {
    minute++;
    second = 0;
  }
  return `${minute}:${pad(second)}`;
}

export function dateRangeInclude(startDate, endDate, date) {
  return (
    +new SafeDate(date) >= +new SafeDate(startDate) && +new SafeDate(date) <= +new SafeDate(endDate)
  );
}

const app = getApp();
/**
 * 下载接口 promise 化
 */
export function downloadFile(url) {
  return app.downloadFile({ url }).then((res) => {
    if (res.statusCode === 200) {
      return res.tempFilePath;
    }
    return Promise.reject();
  });
}

export function createFormId(data, success, fail) {
  getApp().carmen({
    api: 'wsc.weapp.formid/1.0.0/add',
    data,
    method: 'GET',
    success: (res) => {
      success && success(res);
    },
    fail: (res) => {
      fail && fail(res.msg, res);
    }
  });
}

export function convertCodeImage(base64Str, resolve, reject) {
  getApp().carmen({
    api: 'youzan.shop.weapp.codeimage/1.0.0/convert',
    method: 'POST',
    data: {
      mimeType: 'image/png',
      base64Str: `base64,${base64Str}`
    },
    success: (res) => {
      let url = res.attachment_url || '';
      if (typeof url === 'string') {
        url = url.replace(/^http:/, 'https:');
      }
      resolve(url);
    },
    fail: () => {
      reject();
    }
  });
}
