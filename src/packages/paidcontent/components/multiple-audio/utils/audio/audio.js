/**
 * 创建非背景音频
 *
 * API 参考：https://developers.weixin.qq.com/miniprogram/dev/api/media/audio/InnerAudioContext.html
 */

export const PLAY_STATUS = {
  PLAY: 'play',
  PAUSE: 'pause',
  STOP: 'stop',
  ENDED: 'ended',
  FAIL: 'fail'
};

export const BUFFER_STATUS = {
  WAITING: 'waiting',
  CANPLAY: 'canplay'
};

let id = 1;
export default function getInnerAudioContext(src, cbs, { startTime = 0, autoplay = false, loop = false } = {}) {
  if (!src) {
    wx.showToast({
      title: '音频源错误',
      icon: 'none'
    });
    return;
  }

  // 初始化
  const audio = wx.createInnerAudioContext();
  audio.id = id++;
  audio.src = src;
  audio.status = PLAY_STATUS.STOP;
  audio.bufferStatus = BUFFER_STATUS.WAITING;
  audio.obeyMuteSwitch = false;
  // 默认设置
  audio.startTime = startTime;
  audio.autoplay = autoplay;
  audio.loop = loop;

  // 设置监听器
  audio.onCanplay(() => {
    audio.bufferStatus = BUFFER_STATUS.CANPLAY;
    cbs.onBufferStatusChange && cbs.onBufferStatusChange(BUFFER_STATUS.CANPLAY);
  });
  audio.onPlay(() => {
    audio.status = PLAY_STATUS.PLAY;
    cbs.onStatusChange && cbs.onStatusChange(PLAY_STATUS.PLAY);
  });
  audio.onPause(() => {
    audio.status = PLAY_STATUS.PAUSE;
    cbs.onStatusChange && cbs.onStatusChange(PLAY_STATUS.PAUSE);
  });
  audio.onStop(() => {
    audio.status = PLAY_STATUS.STOP;
    cbs.onStatusChange && cbs.onStatusChange(PLAY_STATUS.STOP);
  });
  audio.onEnded(() => {
    audio.status = PLAY_STATUS.ENDED;
    cbs.onStatusChange && cbs.onStatusChange(PLAY_STATUS.ENDED);
  });
  let waitUpdate = false;
  audio.onTimeUpdate(() => {
    if (waitUpdate) return;

    waitUpdate = true;
    setTimeout(() => {
      waitUpdate = false;
      cbs.onTimeUpdate && cbs.onTimeUpdate(audio.currentTime);
    }, 500);
  });
  audio.onError((err) => {
    audio.status = PLAY_STATUS.FAIL;
    cbs.onError && cbs.onError(err);
  });
  audio.onWaiting(() => {
    audio.bufferStatus = BUFFER_STATUS.WAITING;
    cbs.onBufferStatusChange && cbs.onBufferStatusChange(BUFFER_STATUS.WAITING);
  });
  audio.onSeeking(() => {
    audio.pause();
  });
  audio.onSeeked(() => {
    audio.play();
  });

  return audio;
}
