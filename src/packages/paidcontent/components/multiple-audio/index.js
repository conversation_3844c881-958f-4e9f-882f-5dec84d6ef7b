import WscComponent from 'pages/common/wsc-component/index';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import AudioUtils from 'common-api/audio/index';
import { formatTime } from './utils';
import getInnerAudioContext from './utils/audio/audio';
import audioManager from './utils/audio/audio-manager';

WscComponent({
  properties: {
    title: String,
    loop: Number, // 循环播放
    reload: Number, // 暂停后重播
    src: {
      type: String,
      value: '',
      observer(newSrc) {
        // 配合isStop解决音频连续播放状态复用的问题（!!!小测试使用）
        if (!isEmpty(this.data.audio)) {
          this.pause();
          this.setYZData({
            isStop: true,
            audio: {}
          });
        }

        if (newSrc) this.createAudio(newSrc);
      }
    }
  },

  data: {
    audio: {},
    playStatus: 'stop',
    bufferStatus: '',
    percent: 0,
    currentTime: 0,
    duration: 0,
    formattedDuration: '0:00',
    formattedCurrentTime: '0:00'
  },

  detached() {
    this.data.audio.destroy();
  },

  methods: {
    createAudio(src) {
      AudioUtils
        .getAudioInfo(src)
        .then(({ url, duration = 0 }) => {
          const audio = getInnerAudioContext(url, {
            onStatusChange: (newStatus) => {
              if (newStatus === 'ended') {
                setTimeout(() => {
                  this.updateCurrentTime(0);
                }, 1000);
                this.data.audio.stop();
              }

              this.setData({
                playStatus: newStatus
              });
            },
            onBufferStatusChange: (newStatus) => {
              this.setData({
                bufferStatus: newStatus
              });
            },
            onTimeUpdate: (currentTime) => {
              !this.data.isStop ? this.updateCurrentTime(currentTime) : this.updateCurrentTime(0);
              // this.updateCurrentTime(currentTime);
            },
            onError(err) {
              wx.showToast({
                title: err,
                icon: 'none'
              });
              console.error(err);
            }
          }, {
            loop: !!this.data.loop
          });
          audioManager.register(audio);

          this.setData({
            voiceUrl: url,
            duration,
            formattedDuration: formatTime(duration),
            audio
          });

          this.updateCurrentTime(0);
        });
    },

    /**
     * 更新进度
     *
     * @param {Number} current
     */
    updateCurrentTime(currentTime = 0) {
      const percent = parseInt(currentTime / this.data.duration * 100, 10);
      this.setData({
        percent: percent > 100 ? 100 : percent,
        currentTime,
        formattedCurrentTime: formatTime(currentTime)
      });
    },

    pause() {
      this.data.audio.pause();
    },

    onPlayTap() {
      this.data.audio.play();
      this.data.isStop = false;
    },

    onPauseTap() {
      if (this.data.reload) {
        this.data.audio.stop();
      } else {
        this.data.audio.pause();
      }
    },

    onSliderChange(e) {
      this.data.audio.seek(e.detail.value);
    }
  }
});
