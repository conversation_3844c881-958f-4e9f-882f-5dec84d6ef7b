<view class="audio-player">
  <view class="audio-player__control">
    <view
      wx:if="{{ playStatus !== 'play' }}"
      class="audio-player__btn--play"
      bindtap="onPlayTap"
    />
    <view
      wx:else
      class="audio-player__btn--pause"
      bindtap="onPauseTap"
    />
  </view>
  <view class="audio-player__main">
    <view class="audio-player__title">{{ title }}</view>
    <view class="audio-player__slider-container">
      <slider
        class="audio-player__slider"
        min="0"
        max="{{ duration }}"
        value="{{ currentTime }}"
        active-color="#00b389"
        background-color="#f2f2f2"
        block-size="12"
        block-color="#00b389"
        bindchange="onSliderChange"
      />
    </view>
    <view class="audio-player__timestamp">
      <text class="audio-player__current-time">{{ formattedCurrentTime }}</text>
      <text class="audio-player__duration">{{ formattedDuration }}</text>
    </view>
  </view>
</view>