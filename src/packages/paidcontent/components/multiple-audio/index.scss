.audio-player {
  display: flex;
  box-sizing: border-box;
  height: 80px;
  border: 1px solid #e4e4e4;
  border-radius: 6px;
  background: #fff;

  &__control {
    flex: 0 0 auto;
    width: 80px;
    height: 100%;
  }

  &__btn {

    @mixin btn {
      margin: 18px auto 0;
      width: 40px;
      height: 40px;
      // background-size: contain;
    }

    &--play {
      @include btn;

      background: url(https://b.yzcdn.cn/fix-base64/280b04c53f142e0797ed2eb9853a5741c0bd35668b5e42a0accb46e498764ae8.png) no-repeat;
      background-size: contain;
    }

    &--pause {
      @include btn;

      background: url(https://b.yzcdn.cn/fix-base64/b8ce259e5009f88ee7c7dfaee6e76f8c1aa298a3f24d3685e67d53174d9310af.png) no-repeat;
      background-size: contain;
    }
  }

  &__main {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    padding: 16px 10px 16px 5px;
    overflow: hidden;
  }

  &__title {
    flex: 0 0 auto;
    height: 13px;
    overflow: hidden;
    line-height: 13px;
    text-align: left;
    font-size: 13px;
    color: #333;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &__slider-container {
    flex: 1 1 auto;
    padding-top: 4px;
  }

  &__slider {
    margin: 0;
    box-sizing: border-box;
    padding: 0 0 0 1px;
  }

  &__timestamp {
    display: flex;
    justify-content: space-between;
    flex: 0 0 auto;
    height: 10px;
    line-height: 10px;
    font-size: 10px;
    color: #333;
  }
}
