.promotion-banner {
  position: relative;
  display: flex;
  height: 40px;
  align-items: baseline;
  background-color: #fff;
  
  &__tag {
    min-width: 20px;
    height: 14px;
    line-height: 14px;
    margin-right: 10px;
    padding: 1px 5px;
    font-size: 10px;
    color: #f44;
    background-color: #fff;
    border: 1px solid #f44;
    border-radius: 2px;
  }

  &__tip {
    display: -webkit-box;
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;
    color: #333;
    font-size: 12px;
  }

  &__arrow {
    position: absolute;
    top: 14px;
    right: 0;
    width: 6px;
    height: 12px;
    font-weight: bold;
    color: #999;
    background: url(https://b.yzcdn.cn/fix-base64/2d0769086b26d5ca49006efc457aaa94504953fc80369f8c086a5b09e8688f79.png) 6px 12px no-repeat;
    background-position: center;
    background-size: contain;
    transition: .1s linear;
  }

  &__arrow_down {
    transform: rotate(90deg);
  }
}

.promotion-column {
  padding: 0 15px;
  margin: -10px auto 10px;
  .promotion-banner__arrow {
    right: 15px;
  }
}

.promotion-order {
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  margin: 10px 0;

  .promotion-banner {
    &__label {
      font-size: 14px;
      margin-right: 10px;
    }
    
    &__arrow {
      right: 15px;
    }
  }
}

.promotion-info-dialog {
  .zan-popup__container {
    width: 100%;
    left: 0;
  }

  &__title {
    text-align: center;
    font-size: 16px;
    line-height: 16px;
    padding: 14px 0;
  }
  &__item {
    border-top: 1rpx solid #e5e5e5;
    font-size: 14px;
    line-height: 16px;
    padding: 10px 15px;
    display: flex;
    align-items: center;
  }
  
  &__label {
    margin-right: 3px;
  }
  
  &__desc {
    flex: 1;
  }
  
}