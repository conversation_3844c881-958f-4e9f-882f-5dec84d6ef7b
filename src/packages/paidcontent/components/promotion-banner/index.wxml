
  <view id="pr-banner" class="promotion-banner {{ type === 'column' ? 'promotion-column' : '' }} {{ isOrder ? 'promotion-order' : '' }}" bindtap="togglePromotionDialog">
    <block wx:if="{{ !isOrder }}">
      <view wx:for="{{ tags }}" wx:key="*this" class="promotion-banner__tag">{{ item }}</view>
    </block>
    <text wx:else class="promotion-banner__label">店铺活动</text>
    <view class="promotion-banner__tip">{{ meetReduceDes }}</view>
    <view class="promotion-banner__arrow"></view>
  </view>

  <view class="zan-popup zan-popup--bottom {{ showPromotionDialog ? 'zan-popup--show' : '' }} promotion-info-dialog">
    <view class="zan-popup__mask" catchtouchmove="catchTouchMove" bindtap="togglePromotionDialog">
    </view>
    <view class="zan-popup__container">
      <view class="promotion-info-dialog__title">店铺活动</view>
      <view class="promotion-info-dialog__item">
        <theme-view
          wx:for="{{ tags }}"
          wx:for-index="index"
          wx:key="{{ index }}"
          color="main"
          border="main"
          custom-class="promotion-info-dialog__label zan-tag zan-tag--danger zan-tag--plain"
        >
          {{ item }}
        </theme-view>
        <view class="promotion-info-dialog__desc">
          <view> {{ meetReduceDes }} </view>
        </view>
      </view>
      <view class="zan-btn zan-btn--danger zan-btn--large promotion-info__main-btn" bindtap="togglePromotionDialog">
        关闭
      </view>
    </view>
  </view>

