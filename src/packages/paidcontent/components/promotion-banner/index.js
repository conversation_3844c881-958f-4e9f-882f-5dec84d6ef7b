import WscComponent from 'pages/common/wsc-component/index';
import each from '@youzan/weapp-utils/lib/each';

WscComponent({
  properties: {
    meetReduce: Object,
    type: String,
    isOrder: Boolean
  },
  externalClasses: ['is-column'],
  data: {
    tags: [],
    meetReduceDes: [],
    showPromotionDialog: false
  },

  ready() {
    const app = getApp();
    app.getPointsName()
      .then(({ pointsName = '积分' }) => {
        this.pointsName = pointsName;
        this.init();
      })
      .catch(() => {
        this.pointsName = '积分';
        this.init();
      });
  },
  methods: {
    init() {
      const tags = [];
      let meetReduceDes = [];
      if (this.properties.meetReduce.type === 'meetReduce') {
        each(this.properties.meetReduce.activityData, (item) => {
          tags.push('买赠');
          let pointDes = item.score ? `赠${item.score}${this.pointsName}` : '';
          let couponDes = item.couponNum ? `赠${item.couponNum}张${item.couponValue ? `${(item.couponValue / 100).toFixed(2)}元` : `${item.couponDiscount / 10}折`}${item.couponTitle}` : '';
          if (pointDes && couponDes) {
            meetReduceDes.push(`${pointDes};${couponDes}`)
          } else {
            meetReduceDes.push(pointDes || couponDes);
          }
        });
      }

      this.setData({
        tags,
        meetReduceDes
      });
    },
    togglePromotionDialog() {
      const showPromotionDialog = this.data.showPromotionDialog;
      this.setData({
        showPromotionDialog: !showPromotionDialog
      });
    }
  },
});
