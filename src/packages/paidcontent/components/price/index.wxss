@import "themes/base.scss";
@import "shared/common/css/helper/index.wxss";

.paidcontent-price {
  margin: 10px 0 0;
}
.paidcontent-price__symbol {
  line-height: 12px;
  color: #f44;
  font-size: 12px;
}
.paidcontent-price__current {
  margin-left: 2px;
  line-height: 24px;
  color: #f44;
  font-size: 24px;
}
.paidcontent-price__origin {
  margin-left: 6px;
  line-height: 12px;
  color: #666;
  font-size: 12px;
  text-decoration: line-through;
}
.paidcontent-price__tag {
  margin-left: 6px;
  vertical-align: middle;
  padding: 0 3px !important;
}
