const app = getApp();
const { screenHeight, windowHeight } = wx.getSystemInfoSync();

Component({
  data: {
    logo: '',
    height: screenHeight,
    heightGap: screenHeight - windowHeight,
  },
  async attached() {
    const { logo } = app.getShopInfoSync();
    if (logo) {
      this.setData({ logo });
    } else {
      const { logo } = await app.getShopInfo();
      this.setData({ logo });
    }
  },
});
