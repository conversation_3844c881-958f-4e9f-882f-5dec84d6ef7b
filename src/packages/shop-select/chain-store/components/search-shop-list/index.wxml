<navigator-bar
  title="{{ isFocus ? focusTitle : normalTitle }}"
  class="navigator"
>
  <view class="page-container">
    <!-- 定位 -->
    <view hidden="{{isFocus}}">
      <slot />
    </view>
    <block wx:if="{{ isLocalRetailCustom }}">
      <!-- 店铺列表 -->
      <view class="shop-list-container--retail">
        <retail-shop-list
          lon="{{ lon }}"
          lat="{{ lat }}"
          use-cancel="{{ useCancel }}"
          mode="{{ retailMode }}"
          theme-color="{{ themeColor }}"
          theme-bg-color="{{ themeBgColor }}"
          isFocus="{{ isFocus }}"
          shopLists="{{ shopLists }}"
          title="{{ title }}"
          noMore="{{ noMore }}"
          hiddenTitle="{{ hiddenListTitle }}"
          hidden="{{ hiddenShopList }}"
          blank-content="{{blankContent}}"
          is-offline="{{ isOffline }}"
          lastVisitedKdtId="{{ lastVisitedKdtId }}"
          LBS-open="{{ LBSOpen }}"
          bind:choose="handleChooseItem"
          bind:changeAddress="handlerChangeAddress"
          bind:onHiddenMap="onHiddenMap"
          isMapHidden="{{isMapHidden}}"
        />
      </view>
    </block>

    <block wx:else>
      <!-- 店铺列表 -->
      <view class="shop-list-container">
        <shop-list
          shopLists="{{ shopLists }}"
          title="{{ title }}"
          noMore="{{ noMore }}"
          hiddenTitle="{{ hiddenListTitle }}"
          hidden="{{ hiddenShopList }}"
          blank-content="{{ blankContent }}"
          bind:choose="handleChooseItem"
          is-offline="{{ isOffline }}"
        />
      </view>
    </block>
    <view wx:if={{showProtocolMask}} class="protocol-refuse-mask" bindtap="backHome"></view>
  </view>
</navigator-bar>
