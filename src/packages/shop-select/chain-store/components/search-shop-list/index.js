import theme from 'shared/common/components/theme-view/theme';
import { SuitableBizType } from '../../constant';
import Tee from '@youzan/tee';
import { enterShopApollo } from 'common-api/multi-shop/multi-shop-redirect';
import { getPlugins } from '@youzan/ranta-helper-tee';

const PROTOCOL_DENIED_KEY = 'PROTOCOL_DENIED';
Component({
  properties: {
    lon: Number,
    lat: Number,
    shopLists: Array,
    title: String,
    hiddenListTitle: {
      type: Boolean,
      value: false,
    },
    hiddenShopList: {
      type: Boolean,
      value: false,
    },
    redirectUrl: {
      type: String,
    },
    normalTitle: String,
    focusTitle: String,
    blankContent: String,
    noMore: Boolean,
    isOffline: Boolean,
    bizType: {
      type: Number | String,
      observer(data) {
        this.setData({
          isLocalRetailCustom: +data === +SuitableBizType.LocalRetail,
        });
      },
    },
    lastVisitedKdtId: Number,
    LBSOpen: Boolean,
    isMapHidden: Boolean,
    retailMode: Number,
  },

  data: {
    isFocus: false,
    themeColor: '#ee0a24',
    themeBgColor: '',
  },

  async ready() {
    const [themeColor, themeBgColor] = await Promise.all([
      theme.getThemeColor('general'),
      theme.getThemeColor('vice-bg'),
    ]);
    this.setData({
      themeColor,
      themeBgColor,
    });
  },

  // attached() {
  // enterShopApollo('needCheckProtocolAuth').then((isTrue) => {
  //   if (isTrue && Tee.getGlobal(PROTOCOL_DENIED_KEY) === true) {
  //     this.setData({
  //       showProtocolMask: true,
  //     });
  //   }
  // });
  // },

  methods: {
    onHiddenMap() {
      this.triggerEvent('onHiddenMap');
    },
    handleChooseItem({ detail }) {
      const { shop } = detail;
      this.triggerEvent('select', { detailValue: shop });
    },

    handlerChangeAddress({ detail }) {
      this.triggerEvent('changeAddress', detail);
    },

    backHome() {
      const { dmc } = getPlugins();
      dmc.switchTab('Home').catch(() => {
        dmc.navigate('Home');
      });
    },
  },
});
