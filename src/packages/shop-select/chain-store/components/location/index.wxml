<view class="location-wrapper {{ retailMode === 1 ? 'is-delivery':'' }}">
  <!-- 外送只展示搜索框 -->
  <block wx:if="{{ retailMode === 1 || onlyShowSearch }}">
    <view class="search-container w100">
      <search
        bind:focus="handleSearchFocus"
        bind:change="handleInput"
        bind:cancel="handleCancel"
        keyword="{{ keyword }}"
        search-icon-style="{{ isFocus ? '' : 'position:absolute;left:90px;' }}"
        input-style="background: #F2F2F2; border-radius: 16px;height: 32px;"
        inner-input-style="width: 100%;"
        align-left="{{ false }}"
        placeholder="{{ placeholder }}"
        useCancel="{{ useCancel }}"
        searchClass="{{ isFocus ? '' : 'center-placeholder' }}"
        cancelStyle="color: black"
        adjustPosition="{{ false }}"
      />
    </view>
  </block>
  <block wx:else>
    <view class="information" wx:if="{{!isFocus}}" bind:tap="goToEditAddress">
      <van-icon name="location" class="icon-location" />
      <text class="text">{{currentLocation}}</text>
      <van-icon name="arrow" class="icon-arrow" />
    </view>
    <view class="search-container {{ isFocus ? 'w100' : 'icon' }}">
      <image
        wx:if="{{!isFocus}}"
        src="https://img01.yzcdn.cn/upload_files/2023/11/01/Fva56Iv8SNE5ZeOKGN6OibpvrstK.png"
        mode="aspectFit"
        class="location-icon search"
        bind:tap="handleSearchFocus"
      >
      </image>
      <search
        wx:else
        bind:focus="handleSearchFocus"
        bind:change="handleInput"
        bind:cancel="handleCancel"
        keyword="{{ keyword }}"
        input-style="background: #F2F2F2; border-radius: 16px;height: 32px;"
        placeholder="{{ placeholder }}"
        align-left="{{ false }}"
        useCancel="{{ useCancel }}"
        cancelStyle="color: black"
        adjustPosition="{{ false }}"
        focus="{{isFocus}}"
      />
    </view>
      <image
        wx:if="{{!isFocus}}"
        bind:tap="toggleMapShow"
        data-is-map-hidden="{{ isMapHidden }}"
        src="https://img01.yzcdn.cn/upload_files/2023/11/01/FvYg4d8UTkuYg4wsijHmNijoi5Az.png"
        mode="aspectFit"
        class="location-icon map"
      >
  </block>
</view>
