view {
  box-sizing: border-box;
}

@mixin verticalCenter {
  display: flex;
  align-items: center;
}

.location-wrapper {
  padding: 12tpx 20tpx;
  @include verticalCenter();
  justify-content: space-between;
  // box-shadow: 0px 0.5px 0px rgba(0, 0, 0, 0.08);

  &.is-delivery {
    padding: 6tpx 20tpx;
  }

  .information {
    @include verticalCenter();
    max-width: calc(100% - 80px);
    flex-grow: 1;
    padding-right: 16tpx;
    box-sizing: border-box;
    .icon-location, .text {
      color: #333333;
      font-size: 18tpx;
    }
    .text {
      padding: 0tpx 2tpx 0 4tpx;
      text-overflow: ellipsis;
      overflow-x: hidden;
      white-space: nowrap;
      flex-grow: 0;
      max-width: 280tpx;
      box-sizing: border-box;
      font-weight: 500;
      font-size: 16tpx;
      line-height: 22tpx;
      color: #323233;
    }
    .icon-arrow {
      color: #333333;
    }
  }

  .relocation {
    @include verticalCenter();
    .text {
      margin-left: 4tpx;
      color: #323233;
      font-size: 12tpx;
    }

    &.disabled {
      opacity: 0.3;
    }
  }

  .search-container {
    position: relative;
    &.w100 {
      width: 100%;
    }
    &.icon {
      font-size: 0;
      margin-right: 16tpx;
    }
  }

  .location-icon {
    width: 32tpx;
    height: 32tpx;
  }
}
