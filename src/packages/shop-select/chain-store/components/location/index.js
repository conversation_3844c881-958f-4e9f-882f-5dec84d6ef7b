import { getExtOpt } from '../../utils';

const defaultCopy = '重新定位';

Component({
  properties: {
    currentLocation: String,
    dbKey: String,
    redirectUrl: {
      type: String,
      value: '',
      observer(t) {
        this.setData({
          encodeRedirectUrl: t ? encodeURIComponent(t) : '',
        });
      },
    },
    alias: String,
    goodsId: String,
    copy: {
      type: String,
      value: defaultCopy,
      observer(val) {
        this.setData({
          disabled: !!val && val !== defaultCopy,
        });
      },
    },
    isMapHidden: Boolean,
    keyword: String,
    placeholder: String,
    useCancel: Boolean,
    isFocus: Boolean,
    retailMode: Number,
    onlyShowSearch: Boolean,
  },
  methods: {
    reSetLocation() {
      if (this.data.disabled) return;
      this.triggerEvent('reSetLocation');
    },
    toggleMapShow(e) {
      this.triggerEvent('toggleMapShow', e.target);
    },
    handleSearchFocus() {
      this.triggerEvent('handleSearchFocus', true);
    },
    handleInput(e) {
      this.triggerEvent('handleInput', e.detail);
    },
    handleCancel() {
      this.triggerEvent('handleCancel', false);
    },
    goToEditAddress() {
      const { dbKey, encodeRedirectUrl, alias, goodsId, currentLocation } =
        this.data;

      if (['请授权地理位置信息'].includes(currentLocation)) {
        return this.reSetLocation();
      }
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateTo({
        url: `/packages/shop/chain-store/editaddress/index?dbKey=${dbKey}&redirectUrl=${encodeRedirectUrl}&alias=${alias}&goodsId=${goodsId}&extOptJson=${encodeURIComponent(
          JSON.stringify(getExtOpt())
        )}`,
      });
    },
  },
});
