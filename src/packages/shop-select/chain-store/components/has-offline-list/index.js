import Args from '@youzan/weapp-utils/lib/args';

Component({
  properties: {
    useCancel: Boolean,
    lon: Number,
    lat: Number,
    umpAlias: String,
    umpType: String,
    isRedirectDisable: String,
    onlineList: {
      type: Array,
      default: [],
    },
    offlineList: {
      type: Array,
      default: [],
    },
    searchList: {
      type: Array,
      default: [],
    },
    onlineTotal: {
      type: Number,
      observer(onlineTotal) {
        this.setData({
          onlineTitle: `共${onlineTotal}家网店`,
          onlineShowAll: onlineTotal > 3,
          isShowOnline: onlineTotal > 0,
        });
      },
    },
    offlineTotal: {
      type: Number,
      observer(offlineTotal) {
        this.setData({
          offlineTitle: `共${offlineTotal}家门店`,
          offlineShowAll: offlineTotal > 3,
          isShowOffline: offlineTotal > 0,
        });
      },
    },
    hiddenListTitle: {
      type: Boolean,
      value: false,
    },
    hiddenShopList: {
      type: Boolean,
      value: false,
    },
    blankContent: String,
  },

  data: {
    isFocus: false,
  },
  ready() {
    const { onlineTotal, offlineTotal } = this.data;
    this.setData({
      onlineTitle: `共${onlineTotal}家网店`,
      offlineTitle: `共${offlineTotal}家门店`,
      offlineShowAll: offlineTotal > 3,
      onlineShowAll: onlineTotal > 3,
    });
  },
  methods: {
    searchFocus() {
      // 定位组件隐藏
      this.setData({
        useCancel: true,
        hiddenListTitle: true,
        isFocus: true,
      });
      const { isFocus } = this.data;
      this.triggerEvent('focus', { isFocus });
    },

    handleChooseItem({ detail }) {
      const { shop } = detail;
      this.triggerEvent('select', { detailValue: shop });
    },

    handleInput(event) {
      // 用input的value值去调接口
      const { detail } = event;
      const { value } = detail;
      this.setData({
        hiddenListTitle: true,
        hiddenShopList: false,
      });
      if (!value) {
        this.setData({
          hiddenShopList: true,
          searchList: [],
          hiddenListTitle: true,
        });
        return;
      }

      this.triggerEvent('change', { detailValue: value });
    },

    handleCancel() {
      // 点击取消，回到初始页，
      this.setData({
        useCancel: false,
        hiddenShopList: false,
        hiddenListTitle: false,
        isFocus: false,
      });
      const { isFocus } = this.data;
      this.triggerEvent('focus', { isFocus });
    },

    handleGoShopList(event) {
      const {
        detail: { isOffline },
      } = event;
      const {
        lon,
        lat,
        umpAlias,
        umpType = 'coupon',
        isRedirectDisable,
      } = this.data || {};
      const url = Args.add('/packages/shop/chain-store/storeall/index', {
        lon,
        lat,
        umpAlias,
        umpType,
        isOffline: isOffline ? 1 : 0,
        isRedirectDisable,
      });
      wx.redirectTo({ url });
    },
  },
});
