<navigator-bar title="{{isFocus ? focusTitle : normalTitle}}">
  <view class="page-container">
    <!-- 搜索框  -->
    <view class="search-container">
      <search
        bind:focus="searchFocus"
        bind:change="handleInput"
        bind:cancel="handleCancel"
        input-style="background: #f9f9f9; border-radius: 16px;"
        placeholder="{{ placeholder }}"
        align-left
        useCancel="{{ useCancel }}"
        searchStyle="width: 340px; height: 32px;"
        cancelStyle="color: black"
        adjustPosition="{{ false }}"
      />
    </view>
    <!-- 定位 -->
    <view hidden="{{ isFocus }}">
      <slot />
    </view>
    <!-- 店铺列表 -->
    <view wx:if="{{ isFocus }}">
      <shop-list
        shopLists="{{ searchList }}"
        hiddenTitle="{{ true }}"
        ump-type="coupon"
        bind:choose="handleChooseItem"
      >
    </view>
    <view wx:else>
      <view wx:if="{{ isShowOnline }}" class="shop-list-container">
        <shop-list
          title="{{ onlineTitle}}"
          is-online="{{ true }}"
          shopLists="{{ onlineList }}"
          isShowAll="{{ onlineShowAll }}"
          hiddenTitle="{{ hiddenListTitle }}"
          hidden="{{ hiddenShopList }}"
          blank-content="{{blankContent}}"
          bind:choose="handleChooseItem"
          bind:go-list="handleGoShopList"
        />
      </view>
      <view wx:if="{{ isShowOffline }}" class="shop-list-container">
        <shop-list
          title="{{ offlineTitle }}"
          is-offline="{{ true }}"
          shopLists="{{ offlineList }}"
          isShowAll="{{ offlineShowAll }}"
          hiddenTitle="{{ hiddenListTitle }}"
          hidden="{{ hiddenShopList }}"
          blank-content="{{blankContent}}"
          bind:choose="handleChooseItem"
          bind:go-list="handleGoShopList"
        />
      </view>
    </view>
  </view>
</navigator-bar>