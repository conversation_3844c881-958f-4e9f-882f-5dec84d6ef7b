import getApp from 'shared/utils/get-safe-app';
import Event from '@youzan/weapp-utils/lib/event';
import { tryLocation, reverseGeocoder } from '@/helpers/lbs';
import { getShopList } from '../api';
import { injectKeyToData, formatListQuery } from '../utils';

export function redirect() {
  Event.trigger('shop-select:redirect');
}

export function selectSubShop(kdtId, shouldRedirect = true) {
  const app = getApp();
  const currentKdtId = app.getKdtId();

  return app.getShopInfo().then(() => {
    // 更新 kdtid
    return Promise.resolve(
      app.updateKdtId(kdtId, false, {
        mark: '911',
      })
    ).then((res) => {
      res && (app.globalData.prevMultiShopKdtID = currentKdtId);

      if (shouldRedirect) {
        redirect();
      }
    });
  });
}

export function getStoreList(data = {}, options) {
  // 经度字段兼容
  if (data.lng) {
    data.lon = data.lng;
  }

  const query = formatListQuery({ ...injectKeyToData({}, options) });

  return getShopList({
    ...query,
    ...data,
    page: data.page || 1,
    // 选择店铺列表过滤打烊店铺
    isFilterClose: 1,
  });
}

export function getLocation() {
  return new Promise((resolve, rejcet) => {
    tryLocation((location) => resolve(location), rejcet);
  })
    .then(({ lng, lat }) => {
      return reverseGeocoder({
        location: {
          latitude: lat,
          longitude: lng,
        },
        poi_options: 'policy=2',
        coord_type: 3,
      });
    })
    .then(({ result = {} }) => {
      const { lat, lng } = result.location || {};
      const {
        city,
        district,
        nation: country,
        province,
        street,
        street_number: streetNumber,
      } = result.address_component || {};
      const township = (result.address_reference.town || {}).title;
      const formattedAddress = (result.formatted_addresses || {}).recommend;

      return {
        lat,
        lng,
        city,
        district,
        country,
        province,
        street,
        streetNumber,
        township,
        formattedAddress,
      };
    });
}
