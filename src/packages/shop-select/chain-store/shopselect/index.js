import debounce from 'utils/debounce';
import WscPage from 'pages/common/wsc-page/index';
import { Hummer } from 'shared/utils/hummer';
import { gcjToBaidu, reverseGeocoder } from '@/helpers/lbs';
import common from '@/packages/shop/multi-store/common';
import Event from '@youzan/weapp-utils/lib/event';
import { setStorage } from '@youzan/tee-api';
import theme from 'shared/common/components/theme-view/theme';
import { getShopList, getAllStoreList } from '../api';
import {
  parseData,
  injectKeyToData,
  formatListQuery,
  getExtOpt,
} from '../utils';
import { EnterShopSceneMap, DbKeyMap } from '../constant';
import {
  SHOP_OPE_STATUS,
  setApplicationScene,
  EXT_SCENE_ID,
  getAuthStatusLBS,
  LOCATIONSTATUS,
  accordSkyLogger,
  enterShopApollo,
  CHAIN_BIZ_TYPE,
} from 'common-api/multi-shop/multi-shop-redirect';
import { handleSelectItem } from '../helper';
import SaasPageConfig from '../saas/page-config';
import { design } from './design.json';

const { checkScope } = common;
const app = getApp();
const storageKey = 'select-shop-info';

const extendPageConfig = ECLOUD_MODE ? SaasPageConfig : {};
// const PROTOCOL_DENIED_KEY = 'PROTOCOL_DENIED';

WscPage(extendPageConfig, {
  data: {
    design,
    showAll: false,
    useCancel: false,
    redirectUrl: '',
    shopLists: [],
    searchShopList: [],
    onlineList: [],
    offlineList: [],
    onlineTotal: 0,
    offlineTotal: 0,
    isFocus: false,
    lastVisitedKdtId: 0,
    focusTitle: '搜索店铺',
    normalTitle: '',
    placeholder: '搜索',
    title: '全部店铺',
    isRetailLBSSettingOpen: true, // 暂时保留, 点单宝隔离了, 无需依赖通用进店的lbs
    showLoading: true,
    isMapHidden: true, // 地图是否隐藏展示
    keyword: '',
    retailMode: 0,
    initShopList: null,
    hiddenListTitle: false,
    chainBizType: CHAIN_BIZ_TYPE.COMMON,
    isLocalRetailCustom: false,
    globalCustomLoading: true,

    showShopDetail: false,
    themeColor: '#ee0a24',
    itemId: 0,
    shopRangeType: '',
  },

  async onLoad(options) {
    const { poi } = app.storage.get(storageKey) || {};
    this.shopLength = 0;

    // 零售 外卖切换店铺需要带用户选择的地址的经纬度，所以lon，lat默认从getExtOpt中拿，没有则用默认值''
    const {
      location: { lat = '', lng: lon = '' } = {},
      deliveryMethod = 0,
      chainBizType = CHAIN_BIZ_TYPE.COMMON,
      itemId = 0, // 商详传入，配合chainBizType: 'goods_detail_self_fetch'使用
      shopRangeType, // string; 上游页面传入，将会直接透传到接口参数，比如购物车.
      hideReserveOrder = false,
    } = getExtOpt() || {};

    this.setData({
      chainBizType,
      retailMode: +deliveryMethod, // 0：不指定，即通用，1：外送， 2：自提
      placeholder: +deliveryMethod === 1 ? '请输入店铺名称' : '搜索',
      globalCustomLoading: app.globalData.globalCustomLoading,
    });

    const data = {
      currentLocation: poi || '',
      chainBizType,
      lon,
      lat,
      itemId,
      shopRangeType,
      hideReserveOrder,
    };
    Object.assign(this, {
      isLastPage: false,
      isLastSPage: false,
      page: 1,
      searchPage: 0,
    });

    Event.on('shop-select:reFetchListWithLocation', this.handleLocationEvent);

    const { chainStoreInfo } = app.getShopInfoSync();
    const { isEduChainStore = false } = chainStoreInfo || {};
    if (!isEduChainStore) {
      // const { lbsEnable } = await getEnterShopPolicy();
      data.isRetailLBSSettingOpen = true;
      // 这里只是简单粗暴的改了, 没有实际读取后端配置
      data.sceneId = EnterShopSceneMap.LocalRetail;
      this._sceneId = EnterShopSceneMap.LocalRetail;
    } else {
      data.sceneId = EnterShopSceneMap.Normal;
      this._sceneId = EnterShopSceneMap.Normal;
    }

    this.setData(injectKeyToData(data, options));
    getAuthStatusLBS()
      .then((locationAuthStatus) => {
        return enterShopApollo('needCheckProtocolAuth').then((isTrue) => {
          const needProtocolAuth =
            locationAuthStatus === LOCATIONSTATUS.UNKNOWN && isTrue;
          if (locationAuthStatus === LOCATIONSTATUS.DENY || needProtocolAuth) {
            this.setYZData({
              needProtocolAuth,
            });
            const { dbKey, lon } = this.data;
            if (dbKey === 'location' && lon) {
              this.fetchList(DbKeyMap.LOCATION);
              return;
            }
            // 此处主动标识异常信息，为了再次点击授权时弹出引导用户允许授权操作
            this.handleLocationErrorEvent(
              { errMsg: 'getLocation:fail auth deny' },
              this.data.currentLocation || '请授权地理位置信息'
            );
          } else {
            // 加载店铺列表
            const { lon, lat } = this.data;
            if (!lon || !lat) {
              this.reSetLocation();
            } else {
              this.fetchList(DbKeyMap.LOCATION);
            }
          }
        });
      })
      .catch(() => {
        // 新增兜底处理
        this.handleLocationErrorEvent(
          { errMsg: 'getLocation:fail auth deny' },
          '请授权地理位置信息'
        );
      });

    // 校区修改文案
    if (isEduChainStore) {
      this.setYZData({
        title: '全部校区',
        focusTitle: '搜索校区',
        normalTitle: '选择校区',
        placeholder: '请输入校区名称',
      });
      wx.setNavigationBarTitle({
        title: '选择校区',
      });
    }

    Event.once('shop-select:redirect', () => {
      // fix 有赞云定制跳转问题
      const currentKdtId = app.getKdtId();
      handleSelectItem(
        {
          kdtId: currentKdtId,
        },
        this.data
      );
    });

    this.setData({
      themeColor: await theme.getThemeColor('general'),
    });

    app.logger &&
      app.logger.log({
        et: 'display', // 事件类型
        ei: 'enterpage', // 事件标识
        en: '浏览页面', // 事件名称
        pt: 'chainstoreselect', // 页面类型
        params: {
          is_local_retail_mode:
            +this._sceneId === +EnterShopSceneMap.LocalRetail,
        }, // 事件参数
      });
    // // 上报埋点
    Hummer?.mark?.log?.({
      tag: 'shop-select',
      scene: ['appLaunch', 'route'],
    });
  },

  onReady() {
    // 小程序乱七八糟。如果跳转得足够快？，前面页面执行的 wx.startPullDownRefresh 会影响到这个页
    wx.stopPullDownRefresh();
  },

  onReachBottom() {
    const { isFocus, showAll } = this.data;

    // 优惠券场景(同时返回门店、网店时)无需下拉刷新
    if (this.__query__.umpType === 'coupon' && showAll) return;

    // 下拉刷新(加载到最后一页的时候要阻止)
    if ((isFocus && this.isLastSPage) || (!isFocus && this.isLastPage)) return;

    if (isFocus) {
      this.searchPage += 1;
    } else {
      this.page += 1;
    }

    this.fetchList(isFocus ? DbKeyMap.KEYWORD : this.data.dbKey);
  },

  onUnload() {
    Event.off('shop-select:reFetchListWithLocation', this.handleLocationEvent);
  },

  makeListRequest(params) {
    const { umpType, isFocus } = params;
    const result = formatListQuery({
      ...params,
      isRedirectDisable: this.data.isRedirectDisable,
    });

    // 优惠券支持搜索门店
    if (umpType && umpType === 'coupon' && isFocus) {
      result.mode = 'all';
      result.appendShopMetaInfo = 1;
    }

    // 优惠券仅门店/网店可用
    if (this.isCouponOnly) {
      result.mode = this.data.isOffline ? 'offline' : '';
    }
    result.page = isFocus ? this.searchPage : this.page;
    return result;
  },

  // 获取店铺列表（包含门店及网店）
  fetchAllShopList(remount) {
    const { data } = this;
    data.pageSize = 3;
    return getAllStoreList(this.makeListRequest(data))
      .then((res) => {
        const {
          onlineList = {},
          offlineList = {},
          isShowAll = true,
          offlineTotal,
        } = res;
        // 仅有门店/网店则一屏展示
        if (!isShowAll) {
          this.isCouponOnly = true;
          this.setData({
            showAll: false,
            isOffline: offlineTotal > 0,
            locationCopy: '',
          });
          data.pageSize = 20;
          this.fetchShopList(remount);
          return;
        }
        // 展示门店和网店，分别最多3家店铺；
        this.setData(
          {
            showAll: true,
            onlineList: parseData(onlineList),
            offlineList: parseData(offlineList),
            onlineTotal: onlineList.total,
            offlineTotal: offlineList.total,
            locationCopy: '',
          },
          () => {
            setTimeout(() => {
              this.setData({ showLoading: false });
            }, 100);
          }
        );
      })
      .catch((e) => {
        this.setData({
          locationCopy: '',
        });
        throw e;
      });
  },

  // 获取店铺列表（仅包含门店）
  fetchShopList(remount) {
    const { isOffline, dbKey, hideReserveOrder } = this.data;
    const { data } = this;
    data.isNewEnterShop = '1';
    data.isFilterClose = '1';
    // 店铺状态列表，逗号隔开
    data.shopOperationStatusList = `${SHOP_OPE_STATUS.RUNNING}`;
    data.hideReserveOrder = hideReserveOrder;

    const isSearch = dbKey === DbKeyMap.KEYWORD;
    const params = this.makeListRequest(data);

    return getShopList(params)
      .then((res = {}) => {
        if (this.setRetailSceneFlag(params, res, remount)) return;
        const list = parseData(res);
        if (this.triggerAsync) {
          return this.triggerAsync('beforeEnterChainShop', {
            list,
          }).then(() => [res, list]);
        }
        return [res, list];
      })
      .then((result) => {
        if (!result) return;
        const [res, list] = result;
        const isLastPage = !res.items.length;
        // 判断是否要自动选择网店
        // if (
        //   !this.data.needProtocolAuth &&
        //   res.autoSelect &&
        //   res.autoSelectShop
        // ) {
        //   handleSelectItem(res.autoSelectShop, this.data);
        //   return;
        // }
        if (isSearch) {
          this.isLastSPage = isLastPage;
        } else {
          this.isLastPage = isLastPage;
        }

        const updateList = () => {
          const key = isSearch ? 'searchShopList' : 'shopLists';
          if (remount) return { [key]: list };
          // FIXME: 这里有个 bug，每次 onShow 都会 concat，导致列表重复。可能改成 onLoad 就行了？
          return {
            [key]: (this.data[key] || []).concat(list),
          };
        };
        const title = this.isCouponOnly
          ? `共${res.total}家${isOffline ? '门店' : '网店'}`
          : '全部店铺';
        const { lastVisitedKdtId = 0 } = res;
        const params = {
          ...updateList(),
          locationCopy: '',
          title,
          lastVisitedKdtId,
          bizType: res.routeSuitableBizType || '',
          isLocalRetailCustom: +this._sceneId === +EnterShopSceneMap.Normal,
        };
        if (+this._sceneId === +EnterShopSceneMap.LocalRetail) {
          // 零售同城模式下都要进零售的定制页
          params.bizType = '4';
          Object.assign(params, {
            normalTitle: '',
            focusTitle: '搜索门店',
          });
        }
        this.setData(params, () => {
          if (!this.data.initShopList && params?.shopLists?.length > 0) {
            this.setData({
              initShopList: params.shopLists,
            });
          }
          setTimeout(() => {
            this.setData({ showLoading: false });
          }, 100);
        });
      })
      .catch((e) => {
        this.setData({
          locationCopy: '',
        });
        throw e;
      });
  },

  // 2021-06-07 解决同城零售店铺列表兜底问题
  // 设置同城零售场景标识
  // 2021-12-29 继续加上该兜底逻辑
  setRetailSceneFlag(params, res, remount) {
    // 根据后端返回值，判断需要进入兜底
    // ↓ routePageType=2，同城 LBS 场景下，所有店铺都不在配送范围
    // 该逻辑就是在data中加上ignoreRetailScene，如果已经有了，就直接return false
    if (
      !this.data.ignoreRetailScene &&
      +params.sceneId === +EnterShopSceneMap.LocalRetail &&
      // +res.routePageType === 2 // 都不在配送范围
      params.page === 1 && // 第一页就没返回店铺
      res?.items.length === 0 // 没有返回店铺，比如店铺都开启了快递
    ) {
      this.setData(
        {
          ignoreRetailScene: true,
        },
        () => {
          this.fetchShopList(remount);
        }
      );
      return true;
    }
  },

  fetchList(type, remount = false) {
    const { lon, lat, isFocus, from } = this.data;
    if (type === DbKeyMap.LOCATION && (!lon || !lat)) return;
    // 优惠券使用店铺场景，需要展示门店&网店列表
    if (
      !this.isCouponOnly &&
      !isFocus &&
      this.__query__.umpType === 'coupon' &&
      from !== 'new'
    ) {
      this.fetchAllShopList(remount);
      return;
    }
    this.setData({ dbKey: type });
    this.fetchShopList(remount);
  },

  handleInputChange: debounce(function (e) {
    const { detail: { detailValue } = {} } = e;
    // 调根据关键字搜索店铺的接口
    this.setData({
      keyword: detailValue,
    });
    this.searchPage = 1;
    this.isLastSPage = false;
    this.fetchList(DbKeyMap.KEYWORD, true);
  }, 800),

  handleShopDetail(e) {
    const shopInfo = e.detail;

    const {
      customerServiceAreaCode: area,
      customerServicePhoneNumber: phone,
      rawItem: { address: { gcjLat, gcjLng } = {} } = {},
    } = shopInfo;

    this.setData({
      showShopDetail: true,
      shopInfo: {
        ...shopInfo,
        phoneNumber: area ? area + '-' + phone : phone,
      },
      markers: [
        {
          latitude: gcjLat,
          longitude: gcjLng,
          width: 50,
          height: 50,
          iconPath: 'https://b.yzcdn.cn/public_files/2021/08/02/point3.png',
        },
      ],
    });
  },

  handleClose() {
    this.setData({
      showShopDetail: false,
    });
  },

  handlePhoneCall() {
    wx.makePhoneCall({
      phoneNumber: this.data.shopInfo.phoneNumber,
    });
  },

  handleShowMap() {
    const {
      address: { gcjLat, gcjLng },
      shopName,
    } = this.data.shopInfo.rawItem;
    wx.openLocation({
      latitude: gcjLat,
      longitude: gcjLng,
      name: shopName,
    });
  },

  handleOrder() {
    const { shopInfo } = this.data;
    if (shopInfo.isOpen || shopInfo.canReserve) {
      this.handleSelect({ detail: { detailValue: shopInfo } });
    }
  },

  handleSelect(e) {
    const app = getApp();
    const { detail } = e;
    const { detailValue } = detail;

    if (detailValue.kdtId !== app.getKdtId()) {
      Event.trigger('retail:update:kdtid');
    }

    // 手动选择时设置场景值
    setApplicationScene({
      sceneId: EXT_SCENE_ID.SHOPLIST,
      sceneSource: 'CUSTOM',
    });
    handleSelectItem(detailValue, this.data);
    const { redirectUrl } = this.data;
    if (redirectUrl.includes('packages/retail-shelf/shelf/index')) {
      setStorage('retail_shelf_reserve_order', true);
    }

    app.logger &&
      app.logger.log({
        et: 'click', // 事件类型
        ei: 'chain_store_select', // 事件标识
        en: '选择店铺', // 事件名称
        pt: 'chainstoreselect', // 页面类型
        params: {
          is_local_retail_mode:
            +this._sceneId === +EnterShopSceneMap.LocalRetail,
        }, // 事件参数
      });
  },

  handleChangeAddress(e) {
    const {
      detail: { lat, lon },
    } = e;
    this.isLastPage = false;
    this.page = 1;
    this.setData(
      {
        lat,
        lon,
      },
      () => {
        // fixme
        this.fetchShopList(true);
        // this.fetchList(DbKeyMap.LOCATION, true);
      }
    );
  },
  reSetLocation() {
    Event.trigger('shop-select:locate');
    this.tryLocation(
      this.handleLocationEvent,
      (err) => {
        this.handleLocationErrorEvent(err);
      },
      this.tryAgain ? this.reSetLocationAgain : null
    );
  },
  // 新增获取微信定位超时逻辑
  tryLocation(success, fail, cb) {
    let locationResultFlag = false;
    let timeOutFlag = false;
    getAuthStatusLBS().then((locationAuthStatus) => {
      // 同意或拒绝状态下设置超时,
      // 未授权时需要等待用户授权不做超时处理
      if (
        [LOCATIONSTATUS.AGREE, LOCATIONSTATUS.DENY].includes(locationAuthStatus)
      ) {
        setTimeout(() => {
          if (locationResultFlag) return;
          timeOutFlag = true;
          fail({ errMsg: 'getLocation:timeOut' });
          accordSkyLogger({
            text: `[wx] 店铺列表获取定位超时`,
          });
        }, 4000);
      }
    });
    wx.getLocation({
      type: 'gcj02',
      success: ({ latitude, longitude }) => {
        if (timeOutFlag) return;
        locationResultFlag = true;
        const { lng, lat } = gcjToBaidu(longitude, latitude);
        success({ lng, lat }, { latitude, longitude });
      },
      fail: (err) => {
        if (timeOutFlag) return;
        locationResultFlag = true;
        cb ? cb(success, fail) : fail(err);
      },
    });
  },
  handleLocationEvent({ lat, lng }) {
    getAuthStatusLBS(true);
    this.setData({
      locationCopy: '🌍 定位中',
    });
    reverseGeocoder({
      location: {
        latitude: lat,
        longitude: lng,
      },
      poi_options: 'policy=2',
      coord_type: 3,
    })
      .then((res) => {
        const { result = {} } = res;
        const {
          nation: countryName,
          province: provinceName,
          city: cityName,
          district: countyName,
        } = result.address_component || {};

        const { recommend } = result.formatted_addresses || {};

        this.setData({
          lon: lng,
          lat,
          countryName,
          provinceName,
          cityName,
          countyName,
          currentLocation: recommend || result.address || '',
        });
        this.isLastPage = false;
        this.page = 1;
        return this.fetchList(DbKeyMap.LOCATION, true);
      })
      .catch((err) => {
        this.handleLocationErrorEvent(err);
      });
  },

  handleLocationErrorEvent(err, locationText = '') {
    if (this.triggerAsync) {
      this.triggerAsync('beforeEnterChainShop', { list: [], err })
        .then(() => {
          this.handleLocationError(err, locationText);
        })
        .catch((e) => {
          console.log(e);
        });
    } else {
      this.handleLocationError(err, locationText);
    }
  },

  handleLocationError(err, locationText = '') {
    if (/auth deny/.test(err?.errMsg)) {
      this.tryAgain = true;
    }

    this.setData({
      lon: undefined,
      lat: undefined,
      currentLocation: locationText || '获取定位失败',
    });
    this.isLastPage = false;
    this.page = 1;
    this.fetchList('', true);
  },

  reSetLocationAgain(success, fail) {
    const app = getApp();
    checkScope(
      'userLocation',
      // 用户已经授权，说明是接口调用失败
      () => fail,
      // 用户未授权，去授权
      () => {
        wx.showModal({
          content: `"${
            app.getShopInfoSync().base.shop_name
          }"要获取你的地理位置，是否允许？`,
          success: (e) => {
            if (e.cancel) return;
            wx.openSetting({
              success: ({ authSetting }) => {
                getAuthStatusLBS(true);
                authSetting['scope.userLocation'] && this.reSetLocation();
              },
            });
          },
        });
      }
    );
  },
  onHiddenMap() {
    this.setData({
      isMapHidden: true,
    });
  },
  // 隐藏地图
  toggleMapShow({
    detail: {
      dataset: { isMapHidden },
    },
  }) {
    this.setData({
      isMapHidden: !isMapHidden,
    });
  },
  handleSearchFocus() {
    // 定位组件隐藏
    this.setData({
      useCancel: true,
      hiddenListTitle: true,
      isFocus: true,
      placeholder: '请输入店铺名称',
    });
  },
  handleInput: debounce(function (event) {
    // 用input的value值去调接口
    const { detail } = event;
    const { value } = detail;
    if (!value) {
      this.setData({
        hiddenShopList: true,
        shopLists: [],
        hiddenListTitle: true,
      });
      return;
    }

    this.setData({
      hiddenListTitle: true,
      hiddenShopList: false,
      keyword: value,
    });
    // 调根据关键字搜索店铺的接口
    this.searchPage = 1;
    this.isLastSPage = false;
    this.fetchList(DbKeyMap.KEYWORD, true);
  }, 500),
  handleCancel() {
    // 点击取消，回到初始页，
    this.setData({
      useCancel: false,
      hiddenShopList: false,
      shopLists: this.data.initShopList,
      hiddenListTitle: false,
      isFocus: false,
      placeholder: +this.data.retailMode === 1 ? '请输入店铺名称' : '搜索',
      keyword: '',
    });
  },
});
