<import src="./custom-tpl.wxml" />
<global-custom-loading wx:if="{{ globalCustomLoading }}" show="{{ showLoading }}" />
<global-loading wx:elif="{{ showLoading }}"></global-loading>

<block wx:for="{{ design }}" wx:key="type">
    <block wx:if="{{ item.type === 'shop-select-block' }}">
      <has-offline-list
        wx:if="{{ showAll }}"
        online-list="{{ onlineList }}"
        offline-list="{{ offlineList }}"
        online-total="{{ onlineTotal }}"
        offline-total="{{ offlineTotal }}"
        search-list="{{ searchShopList }}"
        lon="{{lon}}"
        lat="{{lat}}"
        ump-type="{{ umpType }}"
        ump-alias="{{ umpAlias }}"
        bind:change="handleInputChange"
        bind:select="handleSelect"
        bind:focus="handleFocus"
        bind:shopDetail="handleShopDetail"
        redirectUrl="{{ redirectUrl }}"
        isRedirectDisable="{{ isRedirectDisable }}"
      >
        <location
          currentLocation="{{ currentLocation }}"
          dbKey="{{ dbKey }}"
          copy="{{locationCopy}}"
          redirectUrl="{{ redirectUrl || '' }}"
          bind:reSetLocation="reSetLocation"
          alias="{{ alias || '' }}"
          goodsId="{{ goodsId }}"
        />
      </has-offline-list>
      <search-shop-list
        wx:else
        shop-lists="{{isFocus ?  searchShopList : shopLists}}"
        bind:change="handleInputChange"
        bind:select="handleSelect"
        bind:changeAddress="handleChangeAddress"
        bind:shopDetail="handleShopDetail"
        placeholder="{{ placeholder }}"
        lon="{{lon}}"
        lat="{{lat}}"
        lastVisitedKdtId="{{ lastVisitedKdtId }}"
        title="{{ title }}"
        normal-title="{{ normalTitle }}"
        focus-title="{{ focusTitle }}"
        redirectUrl="{{ redirectUrl }}"
        noMore="{{ noMore }}"
        is-offline="{{ isOffline }}"
        bizType="{{bizType}}"
        LBS-open="{{isRetailLBSSettingOpen}}"
        isMapHidden="{{ isMapHidden }}"
        retailMode="{{ retailMode }}"
        bind:onHiddenMap="onHiddenMap"
      >
        <location
          onlyShowSearch="{{isLocalRetailCustom}}"
          currentLocation="{{ currentLocation }}"
          dbKey="{{ dbKey }}"
          copy="{{locationCopy}}"
          isMapHidden="{{ isMapHidden }}"
          redirectUrl="{{ redirectUrl || '' }}"
          alias="{{ alias || '' }}"
          goodsId="{{ goodsId }}"
          useCancel="{{ useCancel }}"
          keyword="{{ keyword }}"
          placeholder="{{ placeholder }}"
          isFocus="{{ isFocus }}"
          retailMode="{{ retailMode }}"
          bind:reSetLocation="reSetLocation"
          bind:toggleMapShow="toggleMapShow"
          bind:handleSearchFocus="handleSearchFocus"
          bind:handleInput="handleInput"
          bind:handleCancel="handleCancel"
        />
      </search-shop-list>
    </block>
    <!-- 三方自定义组件 -->
    <block wx:elif="{{ item.custom }}">
      <template is="{{ item.type }}" />
    </block>
</block>

<van-popup
  show="{{ showShopDetail }}"
  round
  position="bottom"
>
  <view class="shop-container">
    <view class="close">
      <van-icon name="cross" size="20" bind:tap="handleClose" color="#999" />
    </view>
    <view class="shop-header">
      <view class="shop-header__title">{{ shopInfo.name }}</view>
      <view class="shop-header__business-status" style="color: {{themeColor}};">{{ shopInfo.isOpen ? '营业中' : '休息中' }}</view>
    </view>
    <view class="divider"></view>
    <view class="list">
      <view class="cell">
        <view class="cell__title">营业时间</view>
        <view class="cell__info">{{ shopInfo.summary === '全天' ? '全天营业' : shopInfo.summary }}</view>
      </view>
      <view class="cell">
        <view class="cell__title">店铺电话</view>
        <view class="cell__info">{{ shopInfo.phoneNumber }}</view>
        <view class="cell__right" bind:tap="handlePhoneCall">
          <van-icon name="phone-o" size="20" />
        </view>
      </view>
      <view class="cell">
        <view class="cell__title">店铺地址</view>
        <view class="cell__info">{{ shopInfo.desc }}</view>
      </view>
      <!-- 地图组件 -->
      <map id="map" class="map"
        longitude="{{ shopInfo.rawItem.address.gcjLng }}"
        latitude="{{ shopInfo.rawItem.address.gcjLat }}"
        markers="{{ markers }}"
        show-location
        bindtap="handleShowMap"
        >
      </map>
    </view>
    <view class="footer">
      <view class="buy" bind:tap="handleOrder" style="background: {{ shopInfo.isOpen || shopInfo.canReserve ? themeColor : '#ddd' }}" >
        {{ shopInfo.isOpen ? '去下单' : shopInfo.canReserve ? '去预定' : '店铺已休息' }}
      </view>
    </view>
  </view>
</van-popup>

<inject-protocol noAutoAuth />