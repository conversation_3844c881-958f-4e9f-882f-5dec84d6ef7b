page {
  min-height: 100vh;
  height: 100vh;
}

.shop-container {
  padding: 16px;
  position: relative;
  background: #fff;

  .close {
    position: absolute;
    top: 3px;
    right: 0;
    width: 68px;
    height: 68px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .shop-header {
    padding: 9px 0 12px;

    &__title {
      font-size: 18px;
      font-weight: 500;
      line-height: 25px;
      color: #111111;
      margin-bottom: 8px;
    }

    &__business-status {
      font-size: 14px;
      color: #7d6a57;
    }
  }

  .divider {
    border-top: 1px solid #f9f9f9;
    margin: 12px 0;
  }

  .list {
    .cell {
      padding: 12px 0;
      position: relative;

      &__title {
        font-size: 12px;
        line-height: 18px;
        color: #999;
        margin-bottom: 4px;
      }
      &__info {
        font-size: 14px;
        color: #111111;
      }
      &__right {
        position: absolute;
        top: -3px;
        right: -17px;
        width: 70px;
        height: 70px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .map {
      width: 100%;
      height: 143px;
      margin-bottom: 131px;
    }
  }

  .footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: constant(safe-area-inset-bottom);
    bottom: env(safe-area-inset-bottom);
    height: 64px;
    padding: 8px 16px;
    box-sizing: border-box;
    border-top: 1px solid #f9f9f9;
    background: #fff;

    .buy {
      width: 100%;
      height: 48px;
      border-radius: 24px;
      color: #fff;
      font-weight: 600;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
