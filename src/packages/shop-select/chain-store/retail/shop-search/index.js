const RETAIL_SHOP_SEARCH_KEY = 'RETAIL_SHOP_SEARCH_KEY';

Component({
  properties: {
    placeholder: String,
    pickUpWay: {
      type: String,
      value: 'selfFetch',
    },
    themeColor: String,
    showHistory: <PERSON><PERSON><PERSON>,
    useCancel: {
      type: <PERSON>olean,
      value: false,
    },
    mode: Number,
    keyword: String,
  },

  data: {
    options: [
      { text: '门店自提', value: 'selfFetch' },
      { text: '外送到家', value: 'delivery' },
    ],
    keywords: [],
  },

  lifetimes: {
    attached() {
      const shopHistoryKey = wx.getStorageSync(RETAIL_SHOP_SEARCH_KEY) || [];
      this.setData({
        keywords: shopHistoryKey,
      });
    },
  },

  methods: {
    onSwitch({
      currentTarget: {
        dataset: { option },
      },
    }) {
      this.triggerEvent('switch', { pickUpWay: option.value });
    },

    onSearch({ detail: { value } = {} }) {
      this.onSaveKeyword(value);
      this.triggerEvent('search', { value });
    },

    onSearchInput(event) {
      // 用input的value值去调接口
      const { detail } = event;
      const { value } = detail;
      this.triggerEvent('change', { value });
    },

    onSearchFocus() {
      // 定位组件隐藏  输入框聚焦
      this.triggerEvent('focus');
    },

    onCancel() {
      this.triggerEvent('cancel');
    },

    onDeleteAllKeyWords() {
      wx.removeStorageSync(RETAIL_SHOP_SEARCH_KEY);
      this.setData({
        keywords: [],
      });
    },

    onSelectKeyword({
      currentTarget: {
        dataset: { keyword },
      },
    }) {
      this.triggerEvent('selectKeyword', { value: keyword });
    },

    onSaveKeyword(keyword) {
      const realKeyword = keyword?.trim() ?? '';
      if (!realKeyword) return;
      const { keywords } = this.data;
      if (~keywords.indexOf(realKeyword)) return;
      keywords.unshift(realKeyword);
      // 超出10条历史记录，则删除
      if (keywords.length > 10) {
        keywords.pop();
      }
      this.setData({
        keywords: [...keywords],
      });
      wx.setStorageSync(RETAIL_SHOP_SEARCH_KEY, keywords);
    },
  },
});
