<view style="--theme-color: {{ themeColor }}" class="bar-wrapper {{ useCancel ? 'focus' : '' }} ">
  <view wx:if="{{ mode === 0 && !useCancel }}" class="bar-opts">
    <view wx:for="{{ options }}" wx:key="value" class="opts {{ pickUpWay === item.value ? 'active' : '' }}" bindtap="onSwitch" data-option="{{ item }}">
      {{ item.text }}
    </view>
  </view>
  <!-- 搜索框  -->
  <search
    class="search-wrapper"
    bind:focus="onSearchFocus"
    bind:search="onSearch"
    bind:cancel="onCancel"
    bind:change="onSearchInput"
    input-style="height: 32px;background: #f9f9f9;border-radius: 16px;"
    placeholder="{{ placeholder }}"
    align-left="{{ false }}"
    keyword="{{ keyword }}"
    useCancel="{{ useCancel }}"
    cancelStyle="color: black"
    adjustPosition="{{ false }}"
  />
</view>

<view
  class="history-container"
  wx:if="{{ useCancel && (showHistory || !keyword) && keywords.length > 0 }}"
>
  <view class="header">
    <text class="title">历史搜索</text>
    <image
      bind:tap="onDeleteAllKeyWords"
      class="delete-img"
      src="https://img.yzcdn.cn/wsc-minapp/icon/retail/clear-cart.png"
    />
  </view>
  <view class="body">
    <view
      wx:for="{{ keywords }}"
      wx:for-item="keyword"
      wx:key="*this"
      class="keyword"
      data-keyword="{{ keyword }}"
      catch:tap="onSelectKeyword"
    >
      <text class="keyword-content">{{ keyword }}</text>
    </view>
  </view>
</view>
