:host {
  --theme-color: #ee0a24;
}

.bar-wrapper {
  width: 100%;
  padding: 0 23px 0 32px;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  &.focus {
    padding: 0 16px;
  }
  .search-wrapper {
    width: 100%;
  }
  .bar-opts {
    display: flex;
    flex-shrink: 0;
    flex-basis: 246px;
    color: #969799;
    font-size: 14px;
    .opts {
      &.active {
        color: #323233;
        position: relative;
        &::after {
          content: '';
          width: 80%;
          position: absolute;
          left: 10%;
          bottom: 0;
          height: 3px;
          background-color: var(--theme-color);
          border-radius: 1.5px;
        }
      }
      &:not(:first-child) {
        margin-left: 25px;
      }
    }
  }
}

.history-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 32rpx;

  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .title {
      font-size: 26rpx;
      color: #646566;
    }

    .delete-img {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .body {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .keyword {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 12rpx 16rpx;
      margin-right: 16rpx;
      margin-bottom: 16rpx;
      background-color: #f2f3f5;
      border-radius: 32rpx;
      flex-grow: 0;
      max-width: 100%;

      &-content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 28rpx;
        color: #323233;
      }
    }
  }
}
