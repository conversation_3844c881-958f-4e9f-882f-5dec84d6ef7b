import WscComponent from 'pages/common/wsc-component/index';
import { parseLocalRetaillData, getExtOpt } from '../../utils';
import getApp from 'shared/utils/get-safe-app';
import { isNewIphone } from 'shared/utils/browser/device-type';
import { getBatchShopWaitingTime, getWarehouse } from './api';
import { CHAIN_BIZ_TYPE } from 'common-api/multi-shop/multi-shop-redirect';
import { getCurrentPage } from 'shared/common/base/wsc-component';

const app = getApp();

WscComponent({
  properties: {
    lon: Number,
    lat: Number,
    hidden: {
      type: Boolean,
      value: false,
    },
    title: String,
    blankContent: String,
    themeColor: String,
    themeBgColor: String,
    shopLists: {
      type: Array,
      value: [],
      observer(shopList = []) {
        this.getShopList(shopList);
      },
    },
    noMore: {
      type: Boolean,
      value: false,
    },
    isFocus: {
      type: Boolean,
      value: false,
    },
    mode: Number,
    lastVisitedKdtId: Number,
    useCancel: {
      type: Boolean,
      value: false,
    },
    LBSOpen: Boolean,
    isMapHidden: Boolean,
  },

  data: {
    mapLat: '',
    mapLon: '',
    mapMarkers: [],
    currentKdtId: '',
    isMapOpen: false, // 地图功能是否开启
    showTwiceConfirmDialog: false,
    // twiceConfirmType: '',
    // twiceConfirmText: '',
    twiceConfirmShop: {},
    // warehouseName: '',
    localRetailShopList: [],
    isIphoneX: isNewIphone(),
    chainBizType: CHAIN_BIZ_TYPE.COMMON,
    showDeliveryTag: false,
    disableLink: false,
    twiceConfirmShopMapMarker: [], // 二次确认门店地图标记点
  },

  created() {
    this.getConfig();
  },

  ready() {
    const { options = {} } = getCurrentPage(this);
    const { address = null, chainBizType = CHAIN_BIZ_TYPE.COMMON } =
      getExtOpt();

    this._address = address;
    const data = {
      currentKdtId: +app.getKdtId(),
      chainBizType,
      showDeliveryTag: chainBizType === CHAIN_BIZ_TYPE.RETAIL_SHELF, // 支持展示配送、自提标签
      disableLink: !!options.isRedirectDisable,
    };
    address && (data.address = address);
    this.setYZData(data);
    this.setMap();
  },

  methods: {
    onHiddenMap() {
      this.triggerEvent('onHiddenMap');
    },
    setMap() {
      this.setData(
        {
          isMapOpen: true,
        },
        () => {
          this.createSelectorQuery()
            .in(this)
            .select('#map')
            .fields({ context: true })
            .exec((res) => {
              this.mapContext = res[0]?.context;
            });
        }
      );
    },
    // 获取店铺同城进店个性化配置
    getConfig() {
      const { chainStoreInfo = {} } = app.getShopInfoSync();
      this.isNeedTwiceConfirm = +chainStoreInfo.visitSecondConfirm === 1; // 是否需要二次确认
    },

    async getShopList(shopList) {
      // 外层做了触底加载，但是不会判断是否有新数据，直接返回一个新的shopLists，没必要，所以做一点优化
      // 若传入的店铺列表和当前店铺列表一致，则不更新
      const { localRetailShopList = [], shopWaitingTimeMap } = this.data;
      const { lastVisitedKdtId } = this.properties;
      if (
        shopList.length !== 0 &&
        shopList.length === localRetailShopList.length &&
        shopList.every((shop, i) => shop.kdtId === localRetailShopList[i].kdtId)
      ) {
        return;
      }

      this.setYZData(
        {
          localRetailShopList: this.setShopWaitingTime(
            parseLocalRetaillData(shopList, {
              deliveryMethod: getExtOpt()?.deliveryMethod,
              lastVisitedKdtId,
            }),
            shopWaitingTimeMap
          ),
        },
        () => {
          this.initMap();
          // 延迟执行防止接口过快导致页面未渲染，实际上这个接口应该会较慢
          setTimeout(() => {
            this.getBatchShopWaitingTime(shopList);
          }, 50);
        }
      );
    },
    // 获取所有店铺制作等待时长
    getBatchShopWaitingTime(shopList) {
      const { lat, lon } = this.properties;
      if (isNaN(lat) || isNaN(lon)) return;
      getBatchShopWaitingTime({
        kdtIdList: shopList.map((item) => item.kdtId),
        lon,
        lat,
      }).then((result) => {
        const shopWaitingTimeMap = result.reduce((map, { kdtId, ...args }) => {
          map[kdtId] = args;
          return map;
        }, {});
        const { localRetailShopList } = this.data;
        this.setYZData({
          localRetailShopList: this.setShopWaitingTime(
            localRetailShopList,
            shopWaitingTimeMap
          ),
          shopWaitingTimeMap, // 缓存下来，如果有值则直接取缓存，下一次再更新，避免页面抖动
        });
      });
    },
    setShopWaitingTime(shopList, shopWaitingTimeMap) {
      if (!shopWaitingTimeMap) {
        return shopList;
      }
      return shopList.map((shop) => {
        const {
          needDisplay = false,
          goodsNum,
          maxGoodsNum,
          orderNum,
        } = shopWaitingTimeMap[shop.kdtId] || {};
        if (!needDisplay) return shop;
        return {
          ...shop,
          showWaitingTime: needDisplay,
          waitingTime: {
            percent: ((goodsNum / maxGoodsNum) * 100).toFixed(2),
            goodsNum,
            orderNum,
          },
        };
      });
    },
    // 初始化地图数据
    async initMap() {
      if (this.hasMapInit) return;
      this.hasMapInit = true;
      const currentKdtId = +app.getKdtId();
      const { shopLists } = this.properties;
      // 初始化map
      if (currentKdtId && shopLists?.length > 0) {
        const { lat, lon, markers } = shopLists.reduce(
          (data, { rawItem }) => {
            const {
              address: { gcjLat: lat, gcjLng: lon } = {},
              kdtId,
              shopName,
            } = rawItem;
            if (kdtId === currentKdtId) {
              data.lat = lat;
              data.lon = lon;
            }
            data.markers.push({
              id: +kdtId,
              latitude: +lat,
              longitude: +lon,
              title: shopName,
              width: 50,
              height: 50,
              customCallout: {
                anchorY: 5,
                anchorX: -2,
                display: kdtId === currentKdtId ? 'ALWAYS' : 'BYCLICK',
              },
              zIndex: 10,
              iconPath: 'https://b.yzcdn.cn/public_files/2021/08/02/point3.png',
            });
            return data;
          },
          {
            lat: '',
            lon: '',
            markers: [],
          }
        );
        this.setYZData({
          mapLat: lat,
          mapLon: lon,
          mapMarkers: markers,
        });
      }
    },
    // 点击标记点
    onClickMarker({ detail: { markerId } }) {
      // 点击标记点将其移动到地图中央
      const { rawItem: { address: { gcjLat: lat, gcjLng: lon } = {} } = {} } =
        this.properties.shopLists.find((shop) => shop.kdtId === markerId);
      this.mapContext
        .moveToLocation({
          longitude: +lon,
          latitude: +lat,
        })
        .catch(() => {});
      const { mapMarkers = [] } = this.data;
      this.setData({
        mapMarkers: mapMarkers.map((marker) => {
          if (marker.id === markerId) {
            marker.zIndex = 10;
            marker.customCallout.display = 'ALWAYS';
          }
          return marker;
        }),
      });
    },
    // 点击标记点对应的气泡
    onClickMapCallout({ detail: { markerId } }) {
      const shop = this.properties.shopLists.find(
        (shop) => shop.kdtId === markerId
      );
      if (!this.isNeedTwiceConfirm) return;
      this.setData({
        currentKdtId: shop.kdtId,
      });
      this.onTwiceConfirm(shop);
    },

    async onTwiceConfirm(shop, pickUpWay = 'selfFetch') {
      this._shop = shop;
      const data = { twiceConfirmShopMapMarker: [] };

      // 点击店铺，地图同步移动当前地铺到中心位置
      const { kdtId: currentKdtId } = shop;

      const { mapMarkers = [] } = this.data;
      mapMarkers.forEach((marker) => {
        if (marker.id === currentKdtId) {
          marker.customCallout.display = 'ALWAYS';
          data.twiceConfirmShopMapMarker = [marker];
        }
      });

      if (pickUpWay === 'delivery') {
        // 外卖
        if (!this.properties.LBSOpen) {
          // lbs关闭
          data.showTwiceConfirmDialog = true;
          data.twiceConfirmShop = shop;
        } else {
          const { isSupportDelivery, warehouseName } = this._address || {};
          if (!isSupportDelivery || !warehouseName) {
            // 外卖下选择的地址超出配送范围，直接让选当前店铺
            // 没派到仓也直接让选当前店铺
            this.onTriggerSelect();
            return;
          }

          data.showTwiceConfirmDialog = true;
          data.twiceConfirmShop = shop;
        }
      } else {
        // 自提
        data.showTwiceConfirmDialog = true;
        data.twiceConfirmShop = shop;
      }

      this.setYZData(data);
    },

    async onSelectShop({ detail: shop }) {
      const { disableClick, canReserve } = shop;
      // 店铺处于休息时间，禁止进入店铺 或 禁用跳转
      if (!canReserve && (disableClick || this.data.disableLink)) return;
      this._shop = shop;
      await this.addWarehouseForAddress();
      if (!this.isNeedTwiceConfirm) {
        this.onTriggerSelect();
        return;
      }

      const needUpdateData = {
        currentKdtId: shop.kdtId,
      };

      if (this.mapContext) {
        // 点击店铺，地图同步移动当前地铺到中心位置
        const {
          kdtId: currentKdtId,
          rawItem: { address: { gcjLat: lat, gcjLng: lon } = {} } = {},
        } = shop;
        this.mapContext
          .moveToLocation({
            longitude: +lon,
            latitude: +lat,
          })
          .catch(() => {});
        const { mapMarkers = [] } = this.data;
        needUpdateData.mapMarkers = mapMarkers.map((marker) => {
          marker.customCallout.display =
            marker.id === currentKdtId ? 'ALWAYS' : 'BYCLICK';

          return marker;
        });
      }

      this.setData(needUpdateData);
      if (this._address === null) {
        this.onTwiceConfirm(shop);
      } else {
        this.onTwiceConfirm(shop, 'delivery');
      }
    },

    onSelectAddress({ detail: address }) {
      this._address = address;
      const { lat, lon } = address;
      this.triggerEvent('changeAddress', { lon, lat });
    },

    async onSelectShopForAddress({ detail: shop }) {
      const { disableClick, canReserve } = shop;
      // 店铺处于休息时间，禁止进入店铺
      if (!canReserve && disableClick) return;
      this._shop = shop;
      await this.addWarehouseForAddress();
      if (!this.isNeedTwiceConfirm) {
        this.onTriggerSelect();
        return;
      }
      this.onTwiceConfirm(this._shop, 'delivery');
    },
    async addWarehouseForAddress() {
      if (!this._address) return;
      const {
        lat,
        lon,
        tel: phone,
        userName: name,
        addressDetail: addressStr,
      } = this._address;
      const result = await getWarehouse({
        kdtId: this._shop.kdtId,
        lat,
        lon,
        phone,
        name,
        addressStr,
        emitLBS: this.properties.LBSOpen ? 1 : 0,
      });
      const { warehouseId, startFee, isSupportLocalDelivery, ...others } =
        result || {};
      let extra;
      if (warehouseId) {
        extra = {
          ...others,
          available: true,
          isSupportDelivery: isSupportLocalDelivery,
          warehouseId,
          startPrice: isSupportLocalDelivery ? startFee : 0,
        };
      } else {
        extra = {
          available: false,
          isSupportDelivery: false,
          reason: 'warehouse error',
          startPrice: 0,
        };
      }
      this._address = {
        ...this._address,
        ...extra,
      };
    },

    onTriggerSelect() {
      const shop = this._shop;
      const address = this._address;
      if (address) {
        const { isOnlineOpen } = shop;
        const dbData = {
          ...address,
          isOpen: isOnlineOpen, // 是否营业
        };
        const dbid = app.db.set(dbData);
        shop.extArgs = { dbid, mode: 1 };
      }
      // 地理位置也带过去
      this.triggerEvent('choose', { shop });
    },

    onCloseTwiceConfirmDialog() {
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'click_cancel', // 事件标识
          en: '点击取消', // 事件名称
          pt: 'chainstoreselect', // 页面类型
        });
      this.setYZData({
        showTwiceConfirmDialog: false,
      });
    },
  },
});
