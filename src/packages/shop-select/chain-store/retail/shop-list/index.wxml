<view hidden="{{ hidden }}" class="container {{ isIphoneX ? 'iphone-x' : '' }}" style="--theme-color: {{ themeColor }}">
  <block wx:if="{{ mode === 0 }}">
    <!-- 通用场景 -->
    <view class="map" wx:if="{{ isMapOpen }}" hidden="{{ isFocus || (localRetailShopList && localRetailShopList.length === 0) }}">
      <map hidden="{{ isMapHidden }}" id="map" class="map__comp" longitude="{{ mapLon }}" latitude="{{ mapLat }}" markers="{{ mapMarkers }}" scale="{{ mapScale }}" show-location="{{ true }}" bindmarkertap="onClickMarker" bindcallouttap="onClickMapCallout">
        <cover-view slot="callout">
          <block wx:for="{{ mapMarkers }}" wx:key="id">
            <cover-view class="custom-callout" marker-id="{{ item.id }}">
              <cover-view class="custom-callout__title">{{ item.title }}</cover-view>
            </cover-view>
          </block>
        </cover-view>
      </map>
      <view class="map__btn" wx:if="{{!isMapHidden}}" bindtap="onHiddenMap">收起地图<van-icon name="arrow-up" /></view>
      <!-- <view class="map__btn" bindtap="onHiddenMap" data-is-map-hidden="{{ isMapHidden }}">
        {{ isMapHidden ? '展开地图' : '收起地图' }}
        <van-icon class="map__btn--icon" name="{{ isMapHidden ? 'arrow-down' : 'arrow-up' }}" color="#969799" />
      </view> -->
    </view>
    <!-- <user-address-list
      theme-color="{{ themeColor }}"
      theme-bg-color="{{ themeBgColor }}"
      bind:selectAddress="onSelectAddress" />
    <view wx:if="{{ !useCancel }}" class="title">附近门店</view> -->
    <view wx:if="{{ localRetailShopList && localRetailShopList.length > 0 }}" class="shop-list-wrap">
      <shop-item
        wx:for="{{ localRetailShopList }}"
        wx:key="name"
        theme-color="{{ themeColor }}"
        theme-bg-color="{{ themeBgColor }}"
        currentKdtId="{{ currentKdtId }}"
        isFocus="{{ isFocus }}"
        shop="{{ item }}"
        isMapOpen="{{ isMapOpen }}"
        showDeliveryTag="{{ showDeliveryTag }}"
        bind:contact="onContact"
        bind:jumpMap="onJumpMap"
        chainBizType="{{ chainBizType }}"
        bind:selectShop="onSelectShop"
      ></shop-item>
    </view>
    <view wx:else class="about-blank">{{ blankContent || '暂无门店' }}</view>
  </block>
  <block wx:elif="{{ mode === 2 }}">
    <!-- 自提场景 -->
    <view class="map" wx:if="{{ isMapOpen }}" hidden="{{ isFocus || (localRetailShopList && localRetailShopList.length === 0) }}">
      <map hidden="{{ isMapHidden }}" id="map" class="map__comp" longitude="{{ mapLon }}" latitude="{{ mapLat }}" markers="{{ mapMarkers }}" scale="{{ mapScale }}" show-location="{{ true }}" bindmarkertap="onClickMarker" bindcallouttap="onClickMapCallout">
        <cover-view slot="callout">
          <block wx:for="{{ mapMarkers }}" wx:key="id">
            <cover-view class="custom-callout" marker-id="{{ item.id }}">
              <cover-view class="custom-callout__title">{{ item.title }}</cover-view>
            </cover-view>
          </block>
        </cover-view>
      </map>
      <view class="map__btn" wx:if="{{!isMapHidden}}" bindtap="onHiddenMap">收起地图<van-icon name="arrow-up" /></view>
      <!-- <view class="map__btn" bindtap="onHiddenMap" data-is-map-hidden="{{ isMapHidden }}">
        {{ isMapHidden ? '展开地图' : '收起地图' }}
        <van-icon class="map__btn--icon" name="{{ isMapHidden ? 'arrow-down' : 'arrow-up' }}" color="#969799" />
      </view> -->
    </view>
    <!-- <view class="title">附近门店</view> -->
    <view wx:if="{{ localRetailShopList && localRetailShopList.length > 0 }}" class="shop-list-wrap">
      <shop-item
        wx:for="{{ localRetailShopList }}"
        wx:key="name"
        theme-color="{{ themeColor }}"
        theme-bg-color="{{ themeBgColor }}"
        currentKdtId="{{ currentKdtId }}"
        isFocus="{{ isFocus }}"
        shop="{{ item }}"
        isMapOpen="{{ isMapOpen }}"
        showDeliveryTag="{{ showDeliveryTag }}"
        bind:contact="onContact"
        bind:jumpMap="onJumpMap"
        bind:selectShop="onSelectShop"
        chainBizType="{{ chainBizType }}"
        address="{{ address }}"
      ></shop-item>
    </view>
    <view wx:else class="about-blank">{{ blankContent || '暂无门店' }}</view>
  </block>
  <block wx:elif="{{ mode === 1 }}">
    <!-- 外送场景 -->
    <view wx:if="{{ address }}" class="address-info">
      <view class="address-info__title">{{address.type === 'userLocation' ? '定位地址' : '收货地址'}}</view>
      <!-- <view class="address-info__user">{{ address.userName }} {{ address.tel }}</view> -->
      <view class="address-info__detail">{{ address.addressDetail }}</view>
    </view>
    <view class="address-title">该地址可配送门店</view>
    <view wx:if="{{ localRetailShopList && localRetailShopList.length > 0 }}" class="shop-list-wrap">
      <shop-item
        wx:for="{{ localRetailShopList }}"
        wx:key="name"
        theme-color="{{ themeColor }}"
        currentKdtId="{{ currentKdtId }}"
        isFocus="{{ isFocus }}"
        shop="{{ item }}"
        isMapOpen="{{ isMapOpen }}"
        showDeliveryTag="{{ showDeliveryTag }}"
        bind:contact="onContact"
        bind:jumpMap="onJumpMap"
        bind:selectShop="onSelectShopForAddress"
        chainBizType="{{ chainBizType }}"
      ></shop-item>
    </view>
    <view wx:else class="about-blank">{{ blankContent || '暂无门店' }}</view>
  </block>
</view>

<!-- <van-dialog use-slot show="{{ showTwiceConfirmDialog }}" show-cancel-button confirm-button-color="{{ themeColor }}" bind:confirm="onTriggerSelect" bind:close="onCloseTwiceConfirmDialog">
  <view wx:if="{{ twiceConfirmType === 'SELF_TAKE' }}" class="twice-confirm-dialog__text">
    <text>{{ twiceConfirmText }}</text>
  </view>
  <view wx:else class="twice-confirm-dialog__text">
    <text>{{ twiceConfirmText }}</text>
    <text style="color: {{ themeColor }}">{{ warehouseName }}</text>
  </view>
</van-dialog> -->

<van-dialog
  use-slot
  show="{{ showTwiceConfirmDialog }}"
  show-cancel-button="{{false}}"
  show-confirm-button="{{false}}"
  close-on-click-overlay
  custom-class="shop-confirm-dialog"
  bind:close="onCloseTwiceConfirmDialog"
>
  <view class="shop-confirm-dialog__map-container">
    <map
      class="shop-confirm-dialog__map-container__comp"
      longitude="{{ twiceConfirmShop.rawItem.address.gcjLng }}"
      latitude="{{ twiceConfirmShop.rawItem.address.gcjLat }}"
      markers="{{ twiceConfirmShopMapMarker }}"
      scale="{{ mapScale }}"
      show-location="{{ true }}"
    >
      <cover-view slot="callout">
        <block wx:for="{{ twiceConfirmShopMapMarker }}" wx:key="id">
          <cover-view class="custom-callout" marker-id="{{ item.id }}">
            <cover-view class="custom-callout__title">{{ item.title }}</cover-view>
          </cover-view>
        </block>
      </cover-view>
    </map>
  </view>
  <view class="twice-confirm-dialog__content">
    <view class="twice-confirm-dialog__title">请您确认门店位置</view>
    <view class="twice-confirm-dialog__shop-info">
      <view class="twice-confirm-dialog__shop-name">{{twiceConfirmShop.name}}</view>
      <view class="twice-confirm-dialog__shop-address">{{twiceConfirmShop.desc}}</view>
      <view wx:if="{{lon}}" class="twice-confirm-dialog__shop-distance"  style="color: {{ themeColor }}">距离你 {{twiceConfirmShop.distanceText}}</view>
    </view>
    <view class="twice-confirm-dialog__buttons">
      <view class="twice-confirm-dialog__button twice-confirm-dialog__button--cancel" bind:tap="onCloseTwiceConfirmDialog">切换门店</view>
      <view class="twice-confirm-dialog__button twice-confirm-dialog__button--confirm"  style="background: {{ themeColor }}" bind:tap="onTriggerSelect">对，就这家</view>
    </view>
  </view>
</van-dialog>