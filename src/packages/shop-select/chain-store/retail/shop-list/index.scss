:host {
  --theme-color: #ee0a24;
}

$itemBottomMargin: 8tpx;

.container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  // background-color: #f2f3f5;

  .shop-list-wrap {
    padding-bottom: 1tpx;
  }

  .about-blank {
    padding-top: 100tpx;
    text-align: center;
    font-size: 14tpx;
    color: #999;
  }
}

.map {
  &__comp {
    width: 100%;
    height: 200tpx;
  }

  &__btn {
    display: flex;
    height: 40tpx;
    justify-content: center;
    align-items: center;
    font-size: 14tpx;
    color: #969799;
    background: #fff;

    &--icon {
      display: flex;
      margin-left: 4tpx;
      align-items: center;
    }
  }
}

// 自定义气泡
.custom-callout {
  padding: 0 12tpx;
  height: 32tpx;
  background: #fff;
  border-radius: 4tpx;
  box-sizing: border-box;

  &__title {
    font-size: 14tpx;
    font-weight: 700;
    line-height: 32tpx;
    text-align: center;
    color: #333;
  }
}

.shop-confirm-dialog {
  &__map-container {
    width: 100%;
    height: 180tpx;

    &__comp {
      width: 100%;
      height: 180tpx;
    }
  }
}
.twice-confirm-dialog {
  &__content {
    display: flex;
    flex-direction: column;
    // width: 320tpx;
    // height: 420tpx;
    // box-sizing: border-box;
    padding: 12tpx 16tpx 20tpx;
  }

  &__title {
    font-size: 16tpx;
    font-weight: 600;

    color: #333;
    text-align: center;
    margin-bottom: 10tpx;
  }

  &__shop-info {
    border: 1px solid #e0e0e0;
    background: #fafafa;
    border-radius: 8tpx;
    padding: 12tpx;
    margin-bottom: 16tpx;
    flex: 1;
  }

  &__shop-name {
    font-size: 14tpx;
    font-weight: 500;
    color: #323233;
    line-height: 22px;
    margin-bottom: 4tpx;
  }

  &__shop-address {
    font-size: 13tpx;
    color: #999;
    line-height: 18tpx;
    margin-bottom: 8tpx;
  }

  &__shop-distance {
    font-size: 13tpx;
    font-weight: 600;
    line-height: 18tpx;
  }

  &__buttons {
    display: flex;
    height: 48tpx;

  }

  &__button {
    flex: 1;
    height: 48tpx;
    line-height: 48tpx;
    font-size: 16tpx;
    text-align: center;
    font-weight: 600;
    width: 138tpx;
    border-radius: 24tpx;

    &--cancel {
      color: #333333;
      border: 1tpx solid #e0e0e0;
      margin-right: 12tpx;
    }

    &--confirm {
      color: #fff;
    }
  }

  &__text {
    display: flex;
    padding: 30tpx 24tpx;
    font-size: 16tpx;
    line-height: 22tpx;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: #323233;
  }
}

.iphone-x {
  padding-bottom: 34tpx;
}

.address-info {
  display: flex;
  flex-direction: column;
  padding: 12tpx 20tpx;
  background: #fff;
  box-shadow: 0 0.5tpx 0 0 rgba(0, 0, 0, 0.08);

  &__title {
    font-size: 14tpx;
    color: #666;
    line-height: 18tpx;
  }

  &__user {
    margin-top: 8tpx;
    font-size: 14tpx;
    color: #323233;
    line-height: 20tpx;
  }

  &__detail {
    margin-top: 8tpx;
    font-size: 16tpx;
    font-weight: 500;
    color: #333;
  }
}

.address-title {
  margin-top: 16tpx;
  padding: 0 20tpx;
  font-size: 14tpx;
  color: #666;
  line-height: 19tpx;
}

.title {
  margin-top: 16tpx;
  padding-left: 12tpx;
  font-size: 13tpx;
  color: #969799;
  line-height: 19tpx;
}
