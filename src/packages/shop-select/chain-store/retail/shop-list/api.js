const app = getApp();

// 批量获取店铺制作等待时长
export const getBatchShopWaitingTime = ({ kdtIdList, lon, lat }) => {
  return app.request({
    path: '/retail/h5/miniprogram/order/getBatchShopWaitingTime',
    method: 'POST',
    data: {
      kdtIdList,
      lon,
      lat,
    },
  });
};

// 获取店铺同城进店个性化配置
export const getPersonalizedShopVisitConfigs = () => {
  return app.request({
    path: '/retail/h5/miniprogram/setting/getPersonalizedShopVisitConfigs',
    method: 'POST',
  });
};

/**
 * 获取供货点信息
 * @param {Object}
 * @returns {Object} 包含供货点id，起送价相关信息
 */
let count = 0;
export const getWarehouse = (data) => {
  return app
    .request({
      path: '/retail/h5/miniprogram/queryWareHouse.json',
      data,
    })
    .then((result) => {
      count = 0;
      return result;
    })
    .catch(() => {
      // 该接口可能犹豫校验配送范围导致派仓失败
      // 额外调用一次，关闭配送范围校验，获取一个履约范围内最近的仓
      if (count === 0) {
        count += 1;
        return getWarehouse({
          ...data,
          emitLBS: 0,
        });
      }
      return {};
    });
};
