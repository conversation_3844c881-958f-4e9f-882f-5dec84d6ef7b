$itemBottomMargin: 8tpx;

:host {
  --theme-color: #ee0a24;
}

.shop-list-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20tpx 20tpx;
  padding: 20tpx;
  font-size: 14tpx;
  background-color: #fff;
  border-radius: 8tpx;
  border: 1tpx solid #fff;
  box-sizing: border-box;
  background: #f5f5f5;
  &.selected {
    position: relative;
    border: 1tpx solid var(--theme-color);
    background: #ffffff;
    .selected-bg {
      position: absolute;
      top: 0;
      right: -1tpx;
      width: 0;
      height: 0;
      border-top-right-radius: 8tpx;
      border: 14tpx solid transparent;
      border-top: 14tpx solid var(--theme-color);
      border-right: 14tpx solid var(--theme-color);
    }
    .selected-icon {
      position: relative;
      top: -14tpx;
      font-size: 12tpx;
    }
  }
  .item-content {
    display: flex;
    width: 100%;
    overflow: hidden;
    &.disabled {
      .shop-name,
      .item-desc {
        color: #c8c9cc;
      }

      .normal-content-right {
        .distance {
          color: #c8c9cc;
        }
        .footer-icon::before {
          background-color: #969799;
          opacity: 0.05;
        }
        .footer-opts {
          margin-top: 5tpx;
        }
        .to-order {
          font-weight: 400;
          color: #ccc;
          margin-bottom: 4tpx;
        }
      }
    }
  }
  .content-left {
    flex: 1;
    position: relative;
  }
  .shop-name {
    font-size: 18tpx;
    font-weight: 600;
    color: #323233;

    &__content {
      max-width: 100%;
    }
  }
  .tag-wrapper {
    display: flex;
    flex-flow: row wrap;
  }
  .tag-item {
    margin-top: 8tpx;
    margin-right: 4tpx;
    padding: 1tpx 4tpx;
    font-size: 14tpx;
  }
  .tag-item:not(:first-child) {
    margin-left: 4tpx;
  }
  .item-desc {
    display: flex;
    color: #999999;
    font-size: 14tpx;
    line-height: 18tpx;
    margin-top: 8tpx;
    &.time {
      margin-top: 0;
    }
  }
  .item-reason {
    margin-top: 8tpx;
    font-weight: 400;
    font-size: 12tpx;
    line-height: 18tpx;
    color: #e00000;
  }
  .item-waiting-time {
    display: flex;
    align-items: center;
    margin-top: 12tpx;
    font-size: 14tpx;
  }
  .process-container {
    display: flex;
    align-items: center;
  }
  .process-text {
    font-weight: 600;
    font-size: 12tpx;
    line-height: 18tpx;
    color: var(--theme-color);
    &.normal {
      color: #666666;
      font-weight: 400;
      margin-left: 5tpx;
    }
  }
  .process-no-waiting-time {
    display: flex;
    font-size: 14tpx;
    line-height: 18tpx;
    color: var(--theme-color);
  }
  .content-right {
    flex-basis: 74tpx;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
  }
  .to-order {
    color: var(--theme-color);
    font-size: 14tpx;
    line-height: 18tpx;
    margin-bottom: 4tpx;
    font-weight: 500;
  }
  .distance {
    font-size: 11tpx;
    white-space: nowrap;
    color: #999999;
    line-height: 18tpx;
  }
  .footer-opts {
    width: 72tpx;
    display: flex;
    justify-content: space-between;
    margin-top: 16tpx;
  }
  .footer-opts.center {
    justify-content: center;
  }
  .footer-icon {
    position: relative;
    width: 30tpx;
    height: 30tpx;
    text-align: center;
    &::before {
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 50%;
      background-color: #f2f2f2;
    }
  }
  .icon-phone,
  .icon-guide {
    color: var(--theme-color);
    font-size: 18tpx;
    margin-top: 7tpx;
  }
}
