<view style="--theme-color: {{ themeColor }}"
  class="shop-list-item {{ (shop.kdtId === currentKdtId && !isFocus) ? 'selected' : '' }}" bindtap="onSelectShop">
  <block wx:if="{{ shop.kdtId === currentKdtId && !isFocus }}">
    <view class="selected-bg">
      <van-icon class="selected-icon" name="success" color="#fff" />
    </view>
  </block>
  <view class="item-content {{ shop.disableClick ? 'disabled' : '' }}">
    <view class="content-left">
      <view class="shop-name" catch:tap="onShopDetail">
        <view class="shop-name__content" i18n-off>{{ shop.name }}
          <van-icon class="arrow" name="arrow" />
        </view>
      </view>
      <!-- 店铺制作等待时间 -->
      <view wx:if="{{ chainBizType==='retail_shelf' && shop.isOpen && shop.showWaitingTime && shop.waitingTime }}"
        class="item-waiting-time">
        <view class="process-container" wx:if="{{ shop.waitingTime.orderNum > 0 }}">
          <view class="process-text">
            {{ shop.waitingTime.orderNum }}单 / 共{{ shop.waitingTime.goodsNum }}杯
          </view>
          <view class="process-text normal">
            制作中
          </view>
        </view>
        <view wx:else class="process-no-waiting-time">
          现在下单，立即制作
        </view>
      </view>
      <!-- 店铺地址 -->
      <view class="item-desc">
        {{ shop.desc }}
      </view>
      <!-- 店铺营业时间 -->
      <view wx:if="{{ shop.summary }}" class="item-desc time">
        {{shop.isOpen ? '营业中' : '休息中'}}<view style="margin: 4px;border-right:1px solid;" />{{ shop.summary === '全天' ? '全天营业' : shop.summary }}
      </view>
      <view class="tag-wrapper" wx:if="{{ shop.isOpen }}">
        <!-- <van-tag class="tag-item" color="{{themeColor}}"
          text-color="{{ shop.disableClick && shop.hideToOrder ? '#CCCCCC' : '#ffffff'}}">
          营业中
        </van-tag> -->
        <van-tag wx:if="{{ showDeliveryTag && !shop.isSupportDelivery }}" custom-class="tag-item" color="{{themeColor}}"
          text-color="#ffffff">
          超出配送范围
        </van-tag>
        <van-tag custom-class="tag-item" wx:if="{{ shop.isCurrent }}" color="{{themeColor}}" text-color="#ffffff">
          最近访问
        </van-tag>
        <van-tag wx:if="{{ showDeliveryTag && shop.isSupportSelfFetch }}" custom-class="tag-item" type="success"
          color="{{ themeColorRgba }}" text-color="{{ themeColor || '#07C160' }}">
          可自提
        </van-tag>
        <van-tag wx:elif="{{ showDeliveryTag && shop.localDelivery }}" custom-class="tag-item" type="success"
          color="{{ themeColorRgba }}" text-color="{{ themeColor || '#07C160' }}">
          可外送
        </van-tag>
      </view>
      <view class="tag-wrapper" wx:elif="{{ shop.canReserve }}">
        <van-tag custom-class="tag-item" type="success" color="{{ themeColorRgba }}" text-color="{{ themeColor || '#07C160' }}">
          可预定
        </van-tag>
      </view>
      <!-- 店铺不能选择原因 -->
      <view wx:if="{{ shop.reason }}" class="item-reason">
        {{ shop.reason }}
      </view>
    </view>
    <view class="{{ shop.hideToOrder ? 'content-right hide-to-order' : 'content-right normal-content-right'}}">
      <view class="to-order" wx:if="{{ !shop.hideToOrder }}">{{ shop.isOpen ? '去下单' : '休息中' }}
        <van-icon wx:if="{{shop.isOpen}}" name="arrow" />
      </view>
      <view class="distance" wx:if="{{ shop.distance }}" style="{{ shop.hideToOrder ? 'font-size:12px;' : '' }}">距离{{
        shop.distanceText }}</view>
      <!-- <view 
        wx:if="{{ shop.customerServicePhoneNumber || isMapOpen }}" 
        class="{{ shop.customerServicePhoneNumber && isMapOpen ? '' : 'center' }} footer-opts"
      >
        <view wx:if="{{ shop.customerServicePhoneNumber }}" class="footer-icon" catch:tap="onContact">
          <van-icon name="phone-o" class="icon-phone" />
        </view>
        <view wx:if="{{ isMapOpen }}" class="footer-icon" catch:tap="onJumpMap" data-shop="{{ item }}">
          <van-icon name="guide-o" class="icon-guide" />
        </view>
      </view> -->
    </view>
  </view>
</view>