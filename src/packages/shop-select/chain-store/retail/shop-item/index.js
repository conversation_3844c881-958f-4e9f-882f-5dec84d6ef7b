import theme from 'shared/common/components/theme-view/theme';

Component({
  properties: {
    shop: Object,
    isFocus: <PERSON>olean,
    currentKdtId: Number,
    isMapOpen: <PERSON>olean,
    themeColor: String,
    showDeliveryTag: Boolean,
    chainBizType: String,
    // themeBgColor: String,
  },
  data: {
    themeColorRgba: '',
  },
  ready() {
    const [r, g, b] = theme.switchHexToRgb(this.properties.themeColor);
    this.setData({
      themeColorRgba: `rgba(${r},${g},${b},0.1)`,
    });
  },
  methods: {
    // 跳转店铺
    onSelectShop() {
      this.triggerEvent('selectShop', this.properties.shop);
    },
    // 跳转地图
    onJumpMap() {
      this.triggerEvent('jumpMap', this.properties.shop);
    },

    // 联系商家
    onContact() {
      this.triggerEvent('contact', this.properties.shop);
    },

    onShopDetail() {
      this.triggerEvent('shopDetail', this.properties.shop, {
        bubbles: true,
        composed: true,
      });
    },
  },
});
