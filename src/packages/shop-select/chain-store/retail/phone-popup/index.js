Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    customerServicePhoneNumber: String,
    customerServiceAreaCode: String,
  },

  data: {
    actions: [
      {
        name: '呼叫',
      },
    ],
  },

  methods: {
    onSelect() {
      const {
        customerServicePhoneNumber,
        customerServiceAreaCode,
      } = this.properties;
      if (customerServicePhoneNumber) {
        wx.makePhoneCall({
          phoneNumber: customerServiceAreaCode + customerServicePhoneNumber,
        });
      }
    },
    onClose() {
      this.triggerEvent('onClosePopup');
    },
  },
});
