<view style="--theme-color: {{ themeColor }}" class="wrapper" >
  <view class="header">
    <view class="title">收货地址</view>
    <view wx:if="{{userAddressList.length}}" class="title-add" bindtap="onAdd">新增收货地址</view>
  </view>

  <!-- <view class="address" bind:tap="onBindWXAddress">
    <view style="height: 44px" class="address__wx">
      <image
        class="address__wx--icon"
        src="https://img01.yzcdn.cn/public_files/2020/05/24/c76f5e98253c7ea13db16112bbfcf953.png"
        mode="aspectFit"
      />
      获取微信收货地址
    </view>
    <view class="address__opt">
      <view class="address__opt--edit">
        <van-icon name="arrow" size="16px" color="#969799"></van-icon>
      </view>
    </view>
  </view> -->
  <scroll-view wx:if="{{userAddressList.length}}" class="address-wrapper" scroll-x enable-flex>
    <view
      wx:for="{{ userAddressList }}"
      wx:key="id"
      class="address {{ item.id === selectedId ? 'address--active' : '' }}"
      data-address="{{ item }}"
      data-index="{{ index }}"
      bind:tap="onSelect"
    >
      <block wx:if="{{ item.id === selectedId }}">
        <view class="selected-bg"> 
          <van-icon class="selected-icon" name="success" color="#fff" />
        </view>
      </block>
      <view class="address__info">
        <view class="address__title">
          <span class="address__title__username" style="margin-right: 5px">{{ item.userName }}</span>
          <span class="address__title__tel">{{ item.tel }}</span>
          <van-tag
            wx:if="{{ item.isDefault }}"
            style="margin-left: 5px"
            class="address__title--icon"
            round
            color="{{ themeColor }}"
            >默认</van-tag>
           <van-tag
            wx:if="{{ item.label }}"
            style="margin-left: 5px"
            class="address__title--icon"
            round
            color="{{ themeBgColor }}"
            text-color="{{ themeColor }}"
            >{{ item.label }}</van-tag>
        </view>
        <view class="address__detail">
          {{ item.addressDetail }}
        </view>
      </view>
      <!-- <view class="address__opt">
        <view class="address__opt--edit" data-address="{{ item }}" data-index="{{ index }}" catch:tap="onEdit">
          <van-icon name="edit" size="16px" color="#969799"></van-icon>
        </view>
      </view> -->
    </view>
  </scroll-view>

  <view wx:else class="add-address" bindtap="onAdd">
    <van-icon name="location-o" />
    <view class="tips">请添加收货地址</view>
    <van-icon name="arrow" color="#969799" />
  <view>
</view>

<!-- <view class="btn-wrapper" style="--theme-color: {{ themeColor }}">
  <view class="btn">
    <user-authorize
      customStyle="display: flex; width: 100%; height: 100%; justify-content: center; align-items: center;"
      class="btn__comp"
      authTypeList="{{ ['mobile'] }}"
      bind:next="onAdd">
    </user-authorize>
    <van-icon name="plus" size="14px" color="#fff"></van-icon>
    新增地址
  </view>
</view> -->