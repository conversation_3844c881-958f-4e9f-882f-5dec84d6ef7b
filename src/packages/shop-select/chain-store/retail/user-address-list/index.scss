:host {
  --theme-color: #ee0a24;
}

.wrapper {
  display: flex;
  flex-direction: column;
  margin-top: 16px;
}

.header {
  display: flex;
  margin-bottom: 12px;
  padding: 0 12px;
  font-size: 13px;
  color: #969799;
  flex-direction: row;
  line-height: 19px;
  justify-content: space-between;

  .title-add {
    color: #155bd4;
  }
}

.address-wrapper {
  display: flex;
  flex-wrap: nowrap;
  height: 88px;
}

.address {
  position: relative;
  display: flex;
  margin-left: 12px;
  padding: 12px;
  flex-basis: 320px;
  min-width: 320px;
  height: 88px;
  justify-content: space-between;
  word-break: break-all;
  background-color: #fff;
  border: 1px solid #fff;
  border-radius: 8px;
  box-sizing: border-box;

  &--active {
    border-color: var(--theme-color);
  }

  .selected-bg {
    position: absolute;
    top: 0;
    right: -1px;
    width: 0;
    height: 0;
    border-top-right-radius: 8px;
    border: 14px solid transparent;
    border-top: 14px solid var(--theme-color);
    border-right: 14px solid var(--theme-color);
  }
  .selected-icon {
    position: relative;
    top: -14px;
    font-size: 12px;
  }

  &__checkbox {
    display: flex;
    margin-right: 12px;
    align-items: center;
  }

  &__info {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    font-size: 14px;
    font-weight: 400;
  }

  &__wx {
    display: flex;
    flex-grow: 1;
    align-items: center;

    &--icon {
      display: flex;
      margin-right: 12px;
      width: 24px;
      height: 24px;
    }
  }

  &__opt {
    display: flex;
    width: 60px;
    flex-shrink: 1;
    justify-content: flex-end;
    align-items: center;

    &--edit {
      display: flex;
      width: 50%;
      height: 100%;
      justify-content: flex-end;
      align-items: center;
    }
  }

  &__title {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
    max-width: 294px;

    &__username {
      overflow: hidden;
      flex-shrink: 1;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &__tel,
    &--icon {
      flex-shrink: 0;
      white-space: nowrap;
    }
  }

  &__detail {
    font-size: 12px;
    color: #969799;
  }
}

.add-address {
  border-radius: 8px;
  background-color: #fff;
  padding: 14px 16px;
  display: flex;
  justify-content: space-between;
  margin: 0 12px;

  .tips {
    flex: 1;
    color: #323233;
    margin-left: 8px;
  }
}

.btn-wrapper {
  position: fixed;
  display: flex;
  left: 0;
  bottom: 0;
  padding: 5px 16px 0;
  width: 100%;
  height: 84px;
  background-color: #fff;

  .btn {
    display: flex;
    position: relative;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 40px;
    font-size: 14px;
    color: #fff;
    font-weight: 700;
    background-color: var(--theme-color);
    border-radius: 20px;

    &__comp {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}
