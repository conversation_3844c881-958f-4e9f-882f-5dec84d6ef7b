// import { callWechatAddress } from '@/packages/order-native/utils/request';
import {
  PAGE_TYPE,
  navigateToRantaPage,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import {
  getUserAddressList as getUserAddressListApi,
  getShopConfigs,
  queryTradeConfig,
} from './api';

const app = getApp();

Component({
  properties: {
    themeColor: {
      type: String,
      default: '#ee0a24',
    },
    themeBgColor: String,
  },

  data: {
    userAddressList: [],
    selectedId: null,
    houseNumberRequired: false,
  },

  pageLifetimes: {
    show() {
      this.getUserAddressList();
    },
  },

  attached() {
    this.getUserAddressList();
    getShopConfigs(['house_number_required']).then((shopConfigs) => {
      const { house_number_required: houseNumberRequired } = shopConfigs;
      const configs = {};
      if (+houseNumberRequired) configs.houseNumberRequired = true;
      this.setData(configs);
    });
    queryTradeConfig('local_delivery_position').then((res) => {
      this._forcePoiSelect = res.config === '1';
    });
  },

  methods: {
    getUserAddressList() {
      if (this._requestProcessing) return;
      this._requestProcessing = true;
      return getUserAddressListApi().then((userAddressList = []) => {
        this.setData({
          userAddressList,
        });
        this._requestProcessing = false;
      });
    },
    // async onBindWXAddress() {
    //   const address = await callWechatAddress();
    //   // 接口要求均为字符串，不想改node，就在小程序里恶心一点兼容下吧
    //   address.lat = '' + address?.lat;
    //   address.lon = '' + address?.lon;
    //   addUserAddress(address)
    //     .then(() => {
    //       wx.showToast({
    //         title: '添加成功！',
    //         icon: 'none',
    //       });
    //       this.getUserAddressList();
    //     })
    //     .catch(() => {
    //       wx.showToast({
    //         title: '添加微信地址失败！',
    //         icon: 'none',
    //         duration: 2000,
    //       });
    //     });
    // },
    onSelect({
      currentTarget: {
        dataset: { address },
      },
    }) {
      if (this.data.selectedId === address.id) return;
      this.setData({
        selectedId: address.id,
      });
      this.triggerEvent('selectAddress', address);
    },
    onAdd() {
      const { houseNumberRequired, userAddressList } = this.data;
      const dbid = app.db.set({
        list: userAddressList,
        id: null,
        forcePoiSelect: this._forcePoiSelect,
        delta: 1,
      });
      navigateToRantaPage({
        url: `/packages/order-native/address-edit/index?dbid=${dbid}&isShowRetailDeliveryAddress=true&houseNumberRequired=${houseNumberRequired}`,
        pageType: PAGE_TYPE.ADDRESS_EDIT,
      });
    },
  },
});
