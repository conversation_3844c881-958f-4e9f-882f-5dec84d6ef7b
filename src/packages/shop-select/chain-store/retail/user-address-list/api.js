const app = getApp();

/**
 * 获取用户地址
 */
export const getUserAddressList = () => {
  return app.request({
    path: '/wscshop/api/showcase-retail/getAddressList.json',
    method: 'GET',
  });
};

/**
 * 新增地址 ｜ 从phaeton CP的
 * @param {Object} data
 * @returns
 */
export const addUserAddress = (data) => {
  return app.request({
    path: '/wscshop/api/showcase-retail/addAddress.json',
    method: 'POST',
    data: {
      userId: app.getToken('buyerId'),
      ...data,
    },
  });
};

/**
 * 获取店铺配置
 * @param {Object} data
 * @returns
 */
export const getShopConfigs = (keys = []) => {
  return app.request({
    path: '/retail/h5/miniprogram/shop/getShopConfigs',
    method: 'POST',
    data: {
      keys,
    },
  });
};

/**
 * 获取交易设置
 * @param {Object} data
 * @returns
 */
export const queryTradeConfig = (key) => {
  return app.request({
    path: '/retail/h5/trade/queryTradeConfig',
    method: 'get',
    data: {
      key,
    },
  });
};
