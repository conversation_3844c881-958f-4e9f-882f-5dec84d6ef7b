import { getGoodsAlias, reportGeoInfo } from './api';
import { redirectPage } from './utils';
import { ExtraShopArgs } from './constant';
import args from '@youzan/weapp-utils/lib/args';
import {
  autoEnterShop,
  CHAIN_BIZ_TYPE,
} from 'common-api/multi-shop/multi-shop-redirect';

const app = getApp();
const storageKey = 'select-shop-info';
const CUSTOMER_LATITUDE_CACHE = '_customer_latitude_cache';

export function handleSelectItem(detail = {}, data = {}) {
  const { desc, kdtId, extArgs } = detail;
  const { goodsId, chainBizType, enterAlias, needProtocolAuth } = data;
  let { redirectUrl, lon, lat } = data;
  // 允许传递额外参数至原页面
  if (extArgs) {
    redirectUrl = args.add(redirectUrl, extArgs);
    wx.setStorageSync(ExtraShopArgs, extArgs);
  }

  const currentKdtId = app.getKdtId();

  if (!lon || !lat) {
    try {
      // 重新定位未获取，则从 storage 中获取
      const customerLatitude = wx.getStorageSync(CUSTOMER_LATITUDE_CACHE);
      if (customerLatitude) {
        let currentLatitude = customerLatitude;
        if (typeof customerLatitude === 'string') {
          currentLatitude = JSON.parse(customerLatitude);
        }
        const { lng: customerLng, lat: customerLat } = currentLatitude;
        lon = customerLng;
        lat = customerLat;
      }
    } catch (e) {
      console.error('解析缓存定位数据失败', e);
    }
  }

  // 上报客户经纬度信息
  reportGeoInfo({
    kdtId,
    lat,
    lng: lon,
  });
  // 提前进店
  autoEnterShop(
    {
      redirectUrl,
      alias: enterAlias,
      chainBizType,
      logTag: 'shop_select',
      useCommonEnterShop: !!(
        chainBizType === CHAIN_BIZ_TYPE.RETAIL_SHELF && enterAlias
      ),
    },
    kdtId
  ).then(() => {
    app.getShopInfo().then(() => {
      // 如果有goodId则调接口拿商品的alias
      if (goodsId) {
        getGoodsAlias({
          kdtId,
          goodsId,
        })
          .then((res) => {
            const { alias } = res;
            redirectUrl += `${
              redirectUrl.indexOf('?') !== -1 ? '&' : '?'
            }alias=${alias}`;
            redirectPage(currentKdtId, kdtId, redirectUrl);
          })
          .catch((err) => {
            console.log(err);
          });
      } else {
        redirectPage(currentKdtId, kdtId, redirectUrl, chainBizType);
      }
    });
  });

  if (!needProtocolAuth) {
    app.storage.set(storageKey, {
      poi: desc,
    });
  }
}
