import promisify from 'utils/promisify';
import { getExtSync } from 'shared/utils/browser/ext-config';
import { getObjWithoutEmptyAttr } from './utils';
import { FeatureEnterMode } from '@youzan/tee-chain-store';

const app = getApp();

const carmen = promisify(app.carmen);

function getList(
  {
    lon,
    lat,
    keyword,
    umpAlias,
    umpType,
    page,
    pageSize = 20,
    kdt_id,
    mode,
    from,
    isNewEnterShop,
    appendShopMetaInfo,
    isFilterClose,
    countryName,
    provinceName,
    cityName,
    countyName,
    ignoreStoreLimit,
    enterShopOptionsJsonString,
    bizListJson,
    deliveryMethod,
    shopOperationStatusList,
    sceneId,
    enterConfig,
    chainBizType,
    itemId = 0,
    shopRangeType: _shopRangeType,
    hideReserveOrder,
  },
  path
) {
  const data = {
    mode,
    page,
    pageSize,
    hqKdtId: getExtSync('kdtId') || app.getHQKdtId(),
    kdt_id,
    storeName: keyword,
    from,
    isNewEnterShop,
    isFilterClose,
    countryName,
    provinceName,
    cityName,
    countyName,
    ignoreStoreLimit,
    enterShopOptionsJsonString,
    bizListJson,
    deliveryMethod,
    shopOperationStatusList,
    sceneId,
    chainBizType,
    itemId,
    hideReserveOrder,
  };

  if (lat && lon) {
    data.lon = lon;
    data.lat = lat;
  }

  if (umpAlias && umpType) {
    data.umpAlias = umpAlias;
    data.umpType = umpType;
  }
  const { shopRangeType, mode: enterMode, kdtIdList = [] } = enterConfig || {};
  // 微页面选店范围, 非指定店铺场景
  if (shopRangeType) {
    data.shopRangeType = shopRangeType;
    if (+enterMode !== FeatureEnterMode.Assign) {
      data.kdtIdListStr = kdtIdList.join(',');
    }
  } else if (_shopRangeType) {
    // 当enterConfig没有时，如果外部传入，则使用外部传入的shopRangeType
    data.shopRangeType = _shopRangeType;
  }
  // 优惠券支持搜索门店
  if (appendShopMetaInfo) {
    data.appendShopMetaInfo = 1;
  }

  return app.request({
    path,
    data: getObjWithoutEmptyAttr(data),
  });
}

// 获取店铺列表
export function getShopList(data = {}) {
  const path = 'wscump/multistore/store/list.json';
  return getList(data, path);
}

// 获取适用店铺列表（包含门店&网店，目前适用于券码场景）
export function getAllStoreList(data = {}) {
  const path = '/wscump/multistore/store/shop-list.json';
  return getList(data, path);
}

// 获取商品alias
export function getGoodsAlias({ goodsId, kdtId }) {
  const data = {
    goods_id: goodsId,
    parent_kdt_id: app.getHQKdtId(),
    sub_kdt_id: kdtId,
  };
  return carmen({
    method: 'GET',
    api: 'youzan.retail.ump.common.subgoods/1.0.0/get',
    data,
  });
}

export function exchangeBookKeyByTargetKdtId(params) {
  return app.request({
    origin: 'cashier',
    path: '/pay/wsctrade/order/buy/cross-store-re-cache-order-creation.json',
    method: 'POST',
    data: params,
  });
}

export function getSwitchableShopList(params) {
  return app.request({
    path: '/wsctrade/order/get-switchable-shopList.json',
    method: 'POST',
    data: params,
  });
}

// 获取进店店铺策略
export function getEnterShopPolicy() {
  return app
    .request({
      path: '/retail/h5/miniprogram/getEnterShopPolicy.json',
      data: {
        rootKdtId: app.getHQKdtId(),
      },
    })
    .catch(() => {
      return {
        lbsEnable: false,
      };
    });
}

/** 上报客户经纬度信息 */
export function reportGeoInfo(data) {
  return app.request({
    data,
    path: '/wscump/customer/reportGeoInfo.json',
  });
}
