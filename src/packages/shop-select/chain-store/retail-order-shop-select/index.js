import WscPage from 'pages/common/wsc-page/index';
import theme from 'shared/common/components/theme-view/theme';
import { getSwitchableShopList, exchangeBookKeyByTargetKdtId } from '../api';
import debounce from 'utils/debounce';
import args from '@youzan/utils/url/args';
import omit from '@youzan/utils/object/omit';
import Event from '@youzan/weapp-utils/lib/event';
import { autoEnterShop, setApplicationScene } from '@youzan/tee-chain-store';

const ERROR_CODE_MAP = {
  REGION_OUT: 1001, // "地址超出配送范围"
  LOCAL_DELIVERY_ITEM_NOT_SUPPORT: 3001, // "不支持同城配送"
  LOCAL_DELIVERY_NOT_REACH_START_AMOUNT: 3003, // "没有达到起送金额"
};

WscPage({
  bookKey: '',
  buyerMsg: {},
  goodsInfo: {},
  address: {},
  orderKdtId: {},
  currentShopInfo: {},
  data: {
    themeColor: '#ee0a24',
    themeBgColor: '',

    showShopSelectInfo: false,
    focusTitle: '搜索店铺',
    normalTitle: '选择店铺',

    isFocus: false,
    useCancel: false,
    currentKdtId: '',

    isLoading: false,
    // 店铺列表相关
    page: 1,
    currentShopList: [],
    isEnd: false,
    // 搜索
    searchPage: 1,
    keyword: '',
    searchShopList: [],
    isSearchEnd: false,
    currentGoodsList: [],
  },

  async onLoad(options) {
    // 主题色
    const [themeColor, themeBgColor] = await Promise.all([
      theme.getThemeColor('general'),
      theme.getThemeColor('vice-bg'),
    ]);
    this.setData({
      themeColor,
      themeBgColor,
    });
    this.initData(options);
    this.getShopList();
  },

  initData(options) {
    const { dbid = '' } = options;

    const initData = getApp().db.get(dbid) || {};
    const { bookKey, buyerMsg, goodsInfo, address, orderKdtId, route, query } =
      initData;
    this.bookKey = bookKey;
    this.buyerMsg = buyerMsg;
    this.goodsInfo = goodsInfo;
    this.address = address;
    this.orderKdtId = orderKdtId;
    this.prevPageRoute = route;
    this.prevPageQuery = query;
  },

  getShopList() {
    const { isFocus, currentShopList, keyword, searchShopList } = this.data;

    // 加载
    this.setData({
      isLoading: true,
    });

    const { lat: buyerLat, lon: buyerLng } = this.address;
    const { list = [], unavailable = [] } = this.goodsInfo;

    const currentList = [...list, ...unavailable];

    this.goodsInfo.list = currentList;
    const itemInfoParamList = currentList.map((item) => {
      const { goodsId, num, skuId } = item;
      return { itemId: goodsId, num, skuId };
    });

    const params = {
      ...(isFocus && keyword ? { shopName: keyword } : {}),
      buyerLat,
      buyerLng,
      itemInfoParamList,
      kdtId: this.orderKdtId,
    };

    getSwitchableShopList(params)
      .then((rs) => {
        const list = rs.map((item) => {
          const {
            shopName,
            isOpen,
            address,
            failMsg,
            distance,
            contactPhone,
            lat,
            lng,
            kdtId,
            failCode,
            isAllItemsFail,
            notSupportItemList,
          } = item;

          const disableClick =
            failCode === ERROR_CODE_MAP.LOCAL_DELIVERY_NOT_REACH_START_AMOUNT ||
            !isOpen ||
            isAllItemsFail;

          return {
            lat,
            lng,
            kdtId,
            name: shopName,
            isOpen,
            desc: address,
            reason: failMsg,
            distance: distance && distance / 1000,
            customerServicePhoneNumber: contactPhone,
            isMapOpen: true,
            isSupportDelivery: true,
            notSupportItemList,
            disableClick,
            hideToOrder: true,
          };
        });

        const defaultItem = list.find((item) => !item.disableClick);
        if (defaultItem) {
          this.onSelectShop({ detail: defaultItem });
        }

        // 默认选中逻辑
        // 无分页
        const isEnd = true;
        this.setData({
          ...(isFocus
            ? {
                isSearchEnd: isEnd,
                searchShopList: [...searchShopList, ...list],
              }
            : {
                isEnd,
                currentShopList: [...currentShopList, ...list],
              }),
          isLoading: false,
        });
      })
      .catch(() => {
        const isEnd = true;
        this.setData({
          ...(isFocus
            ? {
                isSearchEnd: isEnd,
              }
            : {
                isEnd,
              }),
          isLoading: false,
        });
      });
  },

  handleSearchFocus() {
    this.setData({
      isFocus: true,
      useCancel: true,
    });
  },

  handleCancel() {
    // 取消搜索
    this.setData({
      isFocus: false,
      useCancel: false,
      searchShopList: [],
      searchPage: 1,
      searchEnd: false,
      isSearchEnd: false,
    });
  },

  handleInput: debounce(function (e) {
    const { detail: { value } = {} } = e;
    // 调根据关键字搜索店铺的接口
    this.setData({
      searchShopList: [],
      searchPage: 1,
      searchEnd: false,
      isSearchEnd: false,
      keyword: value,
    });
    this.getShopList();
  }, 800),

  // 联系商家
  onContact({ detail: { customerServicePhoneNumber } }) {
    this.setYZData({
      showPhonePopup: true,
      customerServicePhoneNumber,
    });
  },

  onClosePhonePopup() {
    this.setYZData({
      showPhonePopup: false,
    });
  },

  // 跳转地图
  onJumpMap({ detail: shop }) {
    const { name, desc, lat, lng } = shop;
    wx.openLocation({
      latitude: +lat,
      longitude: +lng,
      name,
      scale: 13,
      address: desc,
    });
  },

  onSelectShop({ detail: shop }) {
    if (shop.disableClick) return;
    this.setData({
      currentKdtId: shop.kdtId,
    });
    this.currentShopInfo = shop;
  },

  onConfirmShop() {
    const { notSupportItemList } = this.currentShopInfo;

    if (notSupportItemList.length) {
      this.setData({
        showShopSelectInfo: true,
        goodsList: notSupportItemList.map((item) => {
          const { price, title, itemPicture } = item;

          return {
            price: Math.floor(price / 100),
            cent: `${price}`.slice(-2) > 0 ? `${price}`.slice(-2) : '',
            title,
            image: itemPicture,
          };
        }),
      });

      return;
    }
    this.confirmNext();
  },

  async confirmNext() {
    const { currentKdtId } = this.data;

    exchangeBookKeyByTargetKdtId({
      bookKey: this.bookKey,
      targetKdtId: currentKdtId,
      buyerMsg: this.buyerMsg,
      // 过滤掉赠品和换购商品
      changeItems: this.goodsInfo.list.filter(
        (item) => !item.fromTmpAdded && !item.present
      ),
      extensions: {
        EX_LOCAL_DELIVERY_SWITCH: '1',
      },
    }).then(async (res) => {
      const { bookKey } = res;
      const redirectUrl = this.getRedirectUrl(currentKdtId, bookKey);

      await setApplicationScene({
        sceneId: 9010,
        sceneSource: 'OUTOFDELIVERYSHOP',
      });
      await autoEnterShop({ kdtId: currentKdtId }, { redirectUrl });
      Event.trigger('select-out-of-delivery-shop', { redirectUrl });
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateBack();
    });
  },

  getRedirectUrl(kdtId, bookKey) {
    const { id: addressId } = this.address;
    const url = args.add(this.prevPageRoute, {
      ...omit(this.prevPageQuery, 'prefetchKey'),
      kdt_id: kdtId,
      bookKey,
      book_key: bookKey,
      from_source: 'retailOrderShopSelect',
      addressId,
    });

    return `/${url}`;
  },

  onClose() {
    this.setData({
      showShopSelectInfo: false,
    });
  },
});
