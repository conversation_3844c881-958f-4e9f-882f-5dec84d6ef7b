page {
  background-color: #f2f3f5;
}

.page-container {
  .search-container {
    display: flex;
    justify-content: center;
    padding: 6px 0;
    background-color: #fff;
  }

  .search-bar {
    width: 100%;
  }

  .list-container {

    &--search {

    }
  }

  .empty-container {
    height: 100vh;
    background: #fff;
    padding-top: 80px;
    text-align: center;

    .empty-image {
      width: 160px;
      height: 160px;
    }

    .empty-text {
      margin-top: 16px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      text-align: center;
      color: #969799;
    }
  }

  .loading-container {
    text-align: center;
    padding: 40px;
  }
  
  .btn-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
    background-color: #fff;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}

.goods-container {
  .pop-title {
    height: 48px;
    line-height: 48px;
    font-weight: 500;
    font-size: 16px;
    text-align: center;
    color: #323233;
    background-color: #fff;
  }

  .reason {
    margin: 0 12px;
    font-weight: 400;
    font-size: 13px;
    line-height: 42px;
    color: #323233;
  }

  .scroll-wrap {
    max-height: 300px;
    overflow: hidden;
    overflow-y: auto;
    padding-bottom: 80px;
    box-sizing: border-box;
  }

  .goods-item {
    display: flex;
    margin: 0 12px 12px;
    padding: 10px 16px 10px 12px;
    border-radius: 8px;
    background-color: #fff;
  }

  .goods-image {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
  }

  .goods-msg {
    display: flex;
    margin-left: 10px;
    flex-direction: column;
    justify-content: space-between;
  }

  .goods-title {
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    text-align: justify;
    color: #323233;
  }

  .goods-price {
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
  }

  .goods-cent {
    font-weight: 600;
    font-size: 12px;
    line-height: 20px;
  }
  
  .btn-wrap {
    display: flex;
    justify-content: space-around;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
    background-color: #fff;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

}