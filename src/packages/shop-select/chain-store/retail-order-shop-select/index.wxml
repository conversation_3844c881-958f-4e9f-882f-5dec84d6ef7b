<navigator-bar
  title="{{ isFocus ? focusTitle : normalTitle }}"
  class="navigator"
>
  <view class="page-container">
    <view class="search-container">
      <search
        bind:focus="handleSearchFocus"
        bind:change="handleInput"
        bind:cancel="handleCancel"
        keyword="{{ keyword }}"
        input-style="background: #f9f9f9; border-radius: 16px;height: 32px;"
        placeholder="请输入店铺名称"
        align-left="{{ false }}"
        useCancel="{{ useCancel }}"
        searchStyle="width: 340px;"
        cancelStyle="color: black"
        adjustPosition="{{ false }}"
      />
    </view>

    <view class="list-container" hidden="{{ isFocus }}">
      <shop-item 
        wx:for="{{ currentShopList }}"
        wx:key="kdtId"
        shop="{{ item }}" 
        isMapOpen="{{ true }}"
        theme-color="{{ themeColor }}"
        currentKdtId="{{ currentKdtId }}"
        isFocus="{{ false }}"
        isMapOpen="{{ isMapOpen }}"
        bind:contact="onContact"
        bind:jumpMap="onJumpMap"
        bind:selectShop="onSelectShop"
      />
    </view>

    <view class="list-container list-container--search" hidden="{{ !isFocus }}">
      <shop-item 
        wx:for="{{ searchShopList }}"
        wx:key="kdtId"
        shop="{{ item }}" 
        isMapOpen="{{ true }}"
        theme-color="{{ themeColor }}"
        currentKdtId="{{ currentKdtId }}"
        isFocus="{{ false }}"
        isMapOpen="{{ isMapOpen }}"
        bind:contact="onContact"
        bind:jumpMap="onJumpMap"
        bind:selectShop="onSelectShop"
      />

      <view wx:if="{{ searchShopList.length === 0 }}" class="empty-container">
        <view wx:if="{{ isSearchEnd }}">
          <image mode="aspectFit" class="empty-image" src="https://img01.yzcdn.cn/upload_files/2022/09/27/Fm1f0Ded0TDfhAjjjBKh7i5wlhaD.png" />
          <view class="empty-text">没有搜索到该店铺</view>
        </view>
      </view>
    </view>

    <view wx:if="{{ isLoading }}" class="loading-container">
      <van-loading type="spinner">加载中</van-loading>
    </view>

    <phone-popup show="{{ showPhonePopup }}" customer-service-phone-number="{{ customerServicePhoneNumber }}" customer-service-area-code="{{ customerServiceAreaCode }}" bind:onClosePopup="onClosePhonePopup" />

    <view class="btn-container" hidden="{{ !currentKdtId || (isFocus &&  searchShopList.length === 0) }}">
      <van-button 
        color="{{ themeColor }}" 
        round
        size="large"
        custom-style="height: 44px;" 
        type="primary"
        bind:click="onConfirmShop"
      >确定</van-button>
    </view>
  </view>

</navigator-bar>

<van-popup
  show="{{ showShopSelectInfo }}"
  closeable
  round
  position="bottom"
  custom-style="height: 448px;background: #F5F9FA;"
  bind:close="onClose"
>
  <view class="goods-container">
    <view class="pop-title">以下商品无法下单</view>

    <view class="reason">因库存不足/不支持同城配送</view>

    <view class="scroll-wrap">
      <view class="goods-list">
        <view class="goods-item" wx:for="{{ goodsList }}" wx:key="kdtId">
          <image mode="aspectFit" class="goods-image" src="{{ item.image }}" />
          
          
          <view class="goods-msg">
            <view class="goods-title">{{ item.title }}</view>
            <view class="goods-price" style="{{ 'color: ' + themeColor }}">￥{{ item.price }}<text class="goods-cent" wx:if="{{ item.cent }}">.{{ item.cent }}</text></view>
          </view>
        </view>

      </view>
    </view>

    <view class="btn-wrap">
      <van-button 
        round
        size="large"
        custom-style="height: 40px;width: 165px;" 
        plain
        bind:click="onClose"
      >取消</van-button>

      <van-button 
        color="{{ themeColor }}" 
        round
        size="large"
        custom-style="height: 40px;width: 165px;" 
        type="primary"
        bind:click="confirmNext"
      >确定切店</van-button>
    </view>
  </view>

</van-popup>
<inject-protocol noAutoAuth />