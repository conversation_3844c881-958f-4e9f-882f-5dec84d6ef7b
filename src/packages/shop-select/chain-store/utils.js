import args from '@youzan/weapp-utils/lib/args';
import omit from '@youzan/weapp-utils/lib/omit';
import { setIsReLaunch } from '@/base-api/shop/chain-store.js';
import {
  SuitableBizType,
  EnterShopSceneMap,
  DeliveryMethodMap,
} from './constant';
import { getCurrentPage } from 'shared/common/base/wsc-component';
import { CHAIN_BIZ_TYPE } from 'common-api/multi-shop/multi-shop-redirect';
import { getSwitchShopRangeCache } from '@/base-api/shop/chain/helper';

// import sortBy from 'lodash/sortBy';

const app = getApp();

function isBlankPage(url) {
  return /pages\/common\/blank-page/.test(url);
}

function addLocalRetailBizType(bizListJson) {
  let suitableBizList = [];
  try {
    if (bizListJson) {
      suitableBizList = JSON.parse(decodeURIComponent(bizListJson)) || [];
    }
  } catch (_) {
    //
  }
  suitableBizList.push({
    bizType: SuitableBizType.LocalRetail,
    bizAlias: {
      filterStoreNotSupportDelivery: false,
    },
  });
  return encodeURIComponent(JSON.stringify(suitableBizList));
}

export function formatRedirectUrl(url) {
  let redirectUrl = url || '/packages/home/<USER>/index';
  if (isBlankPage(redirectUrl)) {
    const query = args.getAll(redirectUrl);
    let { weappSharePath } = query;
    if (weappSharePath) {
      weappSharePath = decodeURIComponent(weappSharePath);
      const sharePathQuery = args.getAll(weappSharePath);
      // B端复制总店页面链接进入小程序，手动选完店铺后需要移除kdt_id以免又更新成总店的kdt_id
      const newShareQuery = omit(sharePathQuery, 'kdt_id');
      let [newSharePath] = weappSharePath.split('?');
      newSharePath = args.add(newSharePath, newShareQuery);
      redirectUrl = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
        newSharePath
      )}`;
    }
  }

  return redirectUrl;
}

export function redirectPage(currentKdtId, kdtId, redirectUrl, chainBizType) {
  // 选择网店重定向回去时，先把 relaunch 标志置为 false
  setIsReLaunch(false);

  redirectUrl =
    (redirectUrl.startsWith('/') ? '' : '/') +
    args.add(redirectUrl, { subKdtId: kdtId });
  const { enterShopId } = args.getAll(redirectUrl) || {};
  const jumpType =
    enterShopId && chainBizType !== CHAIN_BIZ_TYPE.RETAIL_SHELF
      ? 'redirectTo'
      : 'reLaunch';
  // 切换店铺时删除强制自动进店标示
  redirectUrl = args.remove(redirectUrl, 'shopAutoEnter');

  // 如果kdtId没变，则直接转跳页面
  if (currentKdtId === kdtId) {
    wx[jumpType]({
      url: redirectUrl || '/packages/home/<USER>/index',
    });
    return;
  }

  const url = formatRedirectUrl(redirectUrl);
  wx[jumpType]({
    url,
  });
}

export function parseData(rawData) {
  return rawData.items.map((item) => {
    const {
      storeName,
      storeKdtId,
      distance = 0,
      storeAddress,
      lat,
      lon,
      shopMetaInfo,
    } = item;

    const { offlineShopOpen, onlineShopOpen } = shopMetaInfo || {};
    return {
      name: storeName,
      kdtId: +storeKdtId,
      isCurrent: storeKdtId === rawData.lastVisitedKdtId,
      distance: distance / 1000,
      distanceText:
        distance / 1000 < 1 ? `${distance}m` : `${distance / 1000}km`,
      desc: storeAddress,
      lat,
      lon,
      offlineShopOpen,
      onlineShopOpen,
      rawItem: item,
    };
  });
}

/**
 * 为零售店铺列表按权重排序写个堆排序类
 * 权重 按照 1.是否营业 2.是否超出配送范围 3.距离 权重排序
 */
function swap(arr, i, j) {
  [arr[i], arr[j]] = [arr[j], arr[i]];
}

class Heap {
  constructor(arr = []) {
    if (!Array.isArray(arr)) {
      throw new Error('array required!');
    }
    this.heap = [];
    arr.forEach(this.push.bind(this));
  }

  get length() {
    return this.heap.length;
  }

  // cur 当前店铺
  // target 待对比的店铺
  _check(cur, target) {
    const curOpen = +!!cur.isOpen;
    const targetOpen = +!!target.isOpen;
    /* 按营业中对比 */
    if (curOpen ^ targetOpen) return curOpen;
    /* 按是否超出配送范围对比 */
    const curOutOfDelivery = +!!cur.isSupportDelivery;
    const targetOutOfDelivery = +!!target.isSupportDelivery;
    if (curOutOfDelivery ^ targetOutOfDelivery) return curOutOfDelivery;
    /* 按距离排序 */
    return cur.distance < target.distance;
  }

  push(o) {
    const { heap } = this;
    heap.push(o);
    let idx = heap.length - 1;
    while (idx) {
      const parent = ~~((idx - 1) / 2);
      if (!this._check(heap[idx], heap[parent])) break;
      swap(heap, idx, parent);
      idx = parent;
    }
  }

  pop() {
    const { heap } = this;
    if (heap.length === 0) return;
    swap(heap, 0, heap.length - 1);
    const ret = heap.pop();
    let idx = 0;
    let temp = idx * 2 + 1;
    while (temp < heap.length) {
      const right = idx * 2 + 2;
      if (right < heap.length && this._check(heap[right], heap[temp]))
        temp = right;
      if (this._check(heap[idx], heap[temp])) break;
      swap(heap, idx, temp);
      idx = temp;
      temp = idx * 2 + 1;
    }
    return ret;
  }
}

/**
 * * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法 * * 将 BD-09 坐标转换成GCJ-02 坐标
 * @param bd_lat
 * @param bd_lon
 * @return array
 */
function baiduToGcj(lng, lat) {
  const delta = (Math.PI * 3000.0) / 180.0;
  const x = lng - 0.0065;
  const y = lat - 0.006;
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * delta);
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * delta);
  lng = z * Math.cos(theta);
  lat = z * Math.sin(theta);
  return { lng, lat };
}

export function parseLocalRetaillData(items, options) {
  const { deliveryMethod = 100, lastVisitedKdtId } = options || {};
  const shopList = items.map((item) => {
    const { rawItem, kdtId } = item;
    const {
      onlineBusinessHours = {},
      supportDelivery = true,
      localDelivery = false,
      // 客服电话区号
      customerServiceAreaCode = '',
      //  客服电话或手机号。输入手机号时区号可为空
      customerServicePhoneNumber = '',
      address: { lat, lng },
      selfFetch,
      canReserve = false,
    } = rawItem;
    if (lat && lng) {
      // 接口的坐标系是百度坐标系，需要转换为腾讯地图使用的火星坐标系
      const { lng: gcjLng, lat: gcjLat } = baiduToGcj(lng, lat);
      item.rawItem.address.gcjLat = gcjLat;
      item.rawItem.address.gcjLng = gcjLng;
    }
    const { summary = '', currentBusinessStatus = {} } = onlineBusinessHours;
    const { isOpen = true } = currentBusinessStatus;

    // 自提模式下不受配送范围限制
    const isSupportDelivery =
      +deliveryMethod === DeliveryMethodMap.SelfFetch || supportDelivery;
    return {
      ...item,
      isSupportSelfFetch: selfFetch,
      // 是否在配送范围内
      isSupportDelivery,
      // 是否可外送
      localDelivery,
      // 营业时间
      summary,
      // 是否在营业时间内
      isOpen,
      // 是否可预定
      canReserve,
      // 不在营业时间内时禁止点击
      disableClick: !isOpen,
      // 最近访问
      isCurrent: kdtId === lastVisitedKdtId,
      customerServiceAreaCode,
      customerServicePhoneNumber,
    };
  });
  const heap = new Heap(shopList);

  const arr = [];
  while (heap.length) {
    arr.push(heap.pop());
  }
  return arr;

  // 老的排序用的lodash的sortBy，如果我写的有问题，可以即时切回来 :)
  // 按照 1.是否超出配送范围 2.是否营业 3.距离 权重排序下。
  // return sortBy(
  //   shopList,
  //   (o) => {
  //     return !o.isOpen;
  //   },
  //   (o) => {
  //     return !o.isSupportDelivery;
  //   },
  //   'distance'
  // );
}

// 微页面进店范围处理
function handleFeatureEnterConfig(result) {
  const obj = {};
  obj.enterConfig = getSwitchShopRangeCache({
    url: result.redirectUrl,
  });
  const { alias = '' } = args.getAll(result.redirectUrl) || {};
  obj.enterAlias = alias;
  return { ...result, ...obj };
}

export function injectKeyToData(data = {}, options) {
  const {
    dbKey = '',
    alias = '',
    umpAlias = '',
    umpType = '',
    redirectUrl,
    goodId: goodsId = '',
    dbid,
    from = '',
    kdt_id = +app.getKdtId(),
    ignoreStoreLimit = '',
    enterShopOptionsJsonString = '',
    isRedirectDisable = '',
    isHideLocation,
    bizIndex,
    bizListJson = '',
  } = options;

  const dbData = app.db.get(dbid);

  const result = {
    ...data,
    dbKey,
    goodsId,
    from,
    kdt_id,
    umpAlias,
    umpType,
    ignoreStoreLimit,
    enterShopOptionsJsonString,
    isRedirectDisable,
    isHideLocation,
    bizIndex,
    bizListJson,
  };

  if (dbKey === 'location' && dbData) {
    Object.assign(result, {
      lon: dbData.lng,
      lat: dbData.lat,
      currentLocation: dbData.name || dbData.poi || dbData.currentLocation,
    });
  }

  if (dbKey === 'alias') {
    result.alias = alias;
  }

  if (umpType === 'coupon') {
    result.umpAlias = alias || umpAlias;
  } else if (
    +data.sceneId === +EnterShopSceneMap.LocalRetail &&
    data.chainBizType === CHAIN_BIZ_TYPE.RETAIL_SHELF &&
    data.isRetailLBSSettingOpen
  ) {
    // 非优惠券定制场景，B端进店规则的进店模式为同城模式时，列表请求添加同城业务参数
    // deliveryMethod传100代表自提/外送店铺都查询
    const { deliveryMethod = 100 } = getExtOpt() || {};
    result.deliveryMethod = deliveryMethod;
    result.bizListJson = addLocalRetailBizType(result.bizListJson);
    result.ignoreStoreLimit = from === 'new' ? '1' : '0'; // 忽略隔离网店配置
  }
  result.redirectUrl = redirectUrl ? decodeURIComponent(redirectUrl) : '';

  return handleFeatureEnterConfig(result);
}

export function getObjWithoutEmptyAttr(obj) {
  return Object.keys(obj).reduce((add, key) => {
    if (obj[key]) {
      add[key] = obj[key];
    }
    return add;
  }, {});
}

export function formatListQuery({
  dbKey: type,
  keyword,
  lat,
  lon,
  alias,
  umpAlias,
  umpType,
  pageSize,
  kdt_id,
  from,
  isNewEnterShop,
  isFilterClose,
  countryName,
  provinceName,
  cityName,
  countyName,
  ignoreStoreLimit,
  enterShopOptionsJsonString,
  bizIndex,
  bizListJson,
  // 同城零售配送方式
  deliveryMethod,
  shopOperationStatusList,
  ignoreRetailScene,
  sceneId,
  chainBizType,
  enterConfig,
  isRedirectDisable,
  itemId,
  shopRangeType,
  hideReserveOrder,
}) {
  const result = {
    from,
    pageSize,
    kdt_id,
    isNewEnterShop,
    isFilterClose,
    countryName,
    provinceName,
    cityName,
    countyName,
    deliveryMethod,
    ignoreStoreLimit,
    enterShopOptionsJsonString,
    bizIndex,
    bizListJson,
    shopOperationStatusList,
    sceneId,
    enterConfig,
    chainBizType,
    itemId,
    shopRangeType,
    hideReserveOrder,
  };

  switch (type) {
    case 'keyword':
      result.keyword = keyword;
      if (sceneId === EnterShopSceneMap.LocalRetail) {
        // 同城模式搜索带上经纬度
        result.lon = lon;
        result.lat = lat;
      }
      break;
    case 'location':
      result.lon = lon;
      result.lat = lat;
      break;
    default:
      result.keyword = '';
  }

  // 优惠券适用店铺列表
  if (alias) {
    result.umpAlias = alias;
    result.umpType = 'coupon';
  }

  // 其他营销活动适配连锁
  if (umpAlias && umpType) {
    result.umpAlias = umpAlias;
    result.umpType = umpType;
  }

  // 忽略零售场景值，删除相关参数
  if (ignoreRetailScene) {
    delete result.bizListJson;
    delete result.deliveryMethod;
    result.isDowngrade = true; // 降级调用
  }

  // 非isRedirectDisable都过滤打烊店铺
  if (!result.isFilterClose && !isRedirectDisable) {
    result.isFilterClose = 1;
  }

  if (hideReserveOrder) {
    result.hideReserveOrder = 1;
  }

  return getObjWithoutEmptyAttr(result);
}

export function getExtOpt() {
  const { options = {} } = getCurrentPage(this);
  const { extOptJson, redirectUrl } = options;
  try {
    const extOpt = JSON.parse(decodeURIComponent(extOptJson)) || {};
    if (
      extOpt.chainBizType === CHAIN_BIZ_TYPE.RETAIL_SHELF &&
      !extOpt.deliveryMethod
    ) {
      const query = args.getAll(decodeURIComponent(redirectUrl));
      if (+query.mode === 1) {
        extOpt.deliveryMethod = 1;
      } else {
        extOpt.deliveryMethod = 2;
      }
    }
    return extOpt;
  } catch (_) {
    return {};
  }
}
