<wxs src="./index.wxs" module="util" />

<view class="edu-widgets--classic">
  <block
    wx:for="{{ widgetGroups }}"
    wx:for-index="index"
    wx:for-item="widgetGroup"
    wx:key="{{ index }}"
  >
    <view
      wx:if="{{ widgetGroup.type === 'widget-group' }}"
      class="widget-group"
    >
      <view
        wx:for="{{ widgetGroup.widgets }}"
        wx:for-index="index"
        wx:for-item="widget"
        wx:key="{{ widget.key + index }}"
        data-key="{{ widget.key }}"
        bindtap="handleWidgetClicked"
        class="widget"
      >
        <block>
            <van-cell
              border="{{ false }}"
              title="{{ widget.title }}"
              is-link
              custom-class="custom-van-cell"
            >
              <image slot="icon" src="{{ widget.icon }}" class="icon" mode="aspectFit" />
              <view wx:if="{{ badges[widget.key].value }}" class="{{ 'badge--' + util.getBadgesType(badges, widget.key) }}">
                {{ util.getBadgesContent(badges, widget.key) }}
              </view>
            </van-cell>
        </block>
      </view>
    </view>
    <view wx:else>
      <block wx:for="{{ widgetGroup.widgets }}" wx:key="{{ widget.key + index }}">
        <view class="whitespace" />
      </block>
    </view>
  </block>
</view>
