.widget {
  position: relative;
  overflow: hidden;
}

.widget:first-child {
  border-top-left-radius: var(--theme-radius-card, 0px);
  border-top-right-radius: var(--theme-radius-card, 0px);
}

.widget:last-child {
  border-bottom-right-radius: var(--theme-radius-card, 0px);
  border-bottom-left-radius: var(--theme-radius-card, 0px);
}

.widget + .widget::after {
  position: absolute;
  top: -1px;
  left: 8px;
  right: 8px;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  transform: scaleY(.5);
  content: "";
}

.van-cell {
  align-items: center;
  color: #111;
}

.custom-van-cell {
  padding: 16px 8px !important;
}

.van-cell:active {
  background: #fff;
}

.van-cell__title span {
  position: relative;
}

.van-cell__left-icon {
  margin-right: 10px;
  color: #333;
}

.van-cell__right-icon {
  color: #999;
  font-size: 12px;
}

.whitespace {
  height: 10px;
}

.badge--red-point {
  float: right;
  width: 8px;
  height: 8px;
  background-color: #fd0600;
  border-radius: 50%;
  margin-top: 8px;
}

.badge--number {
  float: right;
  height: 18px;
  background-color: #fd0600;
  border-radius: 999px;
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 12px;
  padding: 0 5px;
  margin-top: 3px;
}

.icon {
  width: 22px;
  height: 22px;
  line-height: 24px;
  margin: 2px 10px 2px 0;
}

.van-image__img {
  width: 17px;
  height: 17px;
}