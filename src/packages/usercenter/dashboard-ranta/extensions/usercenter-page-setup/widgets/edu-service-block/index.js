import WscComponent from 'shared/common/base/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';

const app = getApp();
const kdtId = app.getHQKdtId() || app.getKdtId();
const currentKdtId = app.getKdtId();

WscComponent({
  properties: {
    isYzEdu: {
      type: Boolean,
      value: false,
    },
    appointments: {
      type: Array,
      value: [],
    },
    noViewNum: {
      type: Number,
      value: 0,
      observer(newVal) {
        this._setNoViewNum(newVal);
      },
    },
    isReadWorkset: {
      type: Boolean,
      value: false,
      observer(newVal) {
        this._setIsReadWorkset(newVal);
      },
    },
    waitRewardCount: {
      type: Number,
      value: 0,
      observer(newVal) {
        this._setWaitRewardCount(newVal);
      },
    },
    canShowMomentsFlag: {
      type: Boolean,
      value: false,
      observer(newVal) {
        this._setCanShowMomentsFlag(newVal);
      },
    },
    reviewToReadNum: {
      type: Number,
      value: 0,
      observer(newVal) {
        this._setReviewToReadNum(newVal);
      },
    },
    services: {
      type: Array,
      value: [],
    },
    mode: {
      // 页面风格：1经典版，2九宫格版
      type: Number,
      default: 2,
    },
    iconMode: {
      // 图标风格（九宫格）：1填色版，2线框版
      type: Number,
      default: 1,
    },
    momentsName: {
      type: String,
      value: '家校圈',
    },
  },

  data: {
    links: {
      // appointment: `https://h5.youzan.com/wscvis/edu/appointment/list?kdt_id=${kdtId}`,
      'student-card': `https://h5.youzan.com/wscvis/edu/student/stu-list?kdt_id=${kdtId}`,
      moments: `https://h5.youzan.com/wscvis/edu/moments/feeds?kdt_id=${kdtId}`,
      'appointment-records': `https://h5.youzan.com/wscvis/edu/appointment/records?kdt_id=${kdtId}`,
      'study-records': `https://h5.youzan.com/wscvis/edu/study-records?kdt_id=${kdtId}`,
      schedule: `https://h5.youzan.com/wscvis/edu/course-schedule?kdt_id=${kdtId}`,
      // owned: `https://h5.youzan.com/wscvis/knowledge/index?p=mypay&kdt_id=${kdtId}`,
      certificate: `https://h5.youzan.com/wscvis/edu/certificate/cert-list?kdt_id=${kdtId}`,
      // reward: `https://h5.youzan.com/wscvis/edu/reward/list/preview?kdt_id=${kdtId}`,
      'family-account': `https://h5.youzan.com/wscvis/edu/family-account/detail?kdt_id=${kdtId}`,
      'recommend-gift': `https://h5.youzan.com/wscvis/ump/referral-invite?fromPage=user-center&kdt_id=${kdtId}`,
      introduction: `https://h5.youzan.com/wscvis/ump/introduction/old-student?kdt_id=${kdtId}&from=usercenter_fixed_entry`,
      exam: `https://h5.youzan.com/wscvis/supv/examination/list?kdt_id=${kdtId}`,
      'activity-entry': `https://h5.youzan.com/wscvis/ump/activity-entry?kdt_id=${kdtId}`,
      homework: `https://h5.youzan.com/wscvis/supv/homework/list?kdt_id=${kdtId}`,
      workset: `https://h5.youzan.com/wscvis/supv/workset/myWorkset?kdt_id=${kdtId}`,
      review: `https://h5.youzan.com/wscvis/supv/review/list?kdt_id=${kdtId}`,
      leave: `https://h5.youzan.com/wscvis/edu/leave/list?kdt_id=${currentKdtId}`,
    },
    badges: {
      appointment: {
        value: false,
      },
      studentCard: {
        value: false,
      },
      moments: {
        value: false,
      },
      'appointment-records': {
        value: false,
      },
      'study-records': {
        value: false,
      },
      schedule: {
        value: false,
      },
      owned: {
        value: false,
      },
      certificate: {
        value: false,
        type: 'number',
        num: 0,
      },
      reward: {
        value: false,
      },
      'family-account': {
        value: false,
      },
      'recommend-gift': {
        value: false,
      },
      introduction: {
        value: true,
        type: 'reward',
      },
      exam: {
        value: false,
      },
      'activity-entry': {
        value: true,
        type: 'reward',
      },
      homework: {
        value: false,
      },
      workset: {
        value: false,
      },
      review: {
        value: false,
        type: 'number',
        num: 0,
      },
      leave: {
        value: false,
      },
    },
  },

  methods: {
    handleWidgetClicked(e) {
      const key = e.detail;
      switch (key) {
        case 'appointment':
          this.handleAppointmentClicked();
          break;
        case 'owned':
          this.handleOwnedClicked();
          break;
        case 'reward':
          this.handleRewardClicked();
          break;
        default:
          wx.navigateTo({
            url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
              this.properties.links[key]
            )}`,
          });
      }
    },
    handleRewardClicked() {
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'weapp_uc_reward_click',
          en: '小程序个人中心奖励模块点击',
          pt: 'uc',
          params: {
            kdt_id: app.getKdtId(),
          },
        });
      wx.navigateTo({
        url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
          `https://h5.youzan.com/wscvis/edu/reward/list/preview?kdt_id=${kdtId}`
        )}`,
      });
    },
    handleAppointmentClicked() {
      if (
        this.data.appointments.length === 1 &&
        !this.data.appointments[0].validity
      ) {
        Toast('你的课程尚未生效，暂时无法预约');
      } else {
        wx.navigateTo({
          url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
            `https://h5.youzan.com/wscvis/edu/appointment/list?kdt_id=${kdtId}`
          )}`,
        });
      }
    },
    handleOwnedClicked() {
      wx.navigateTo({
        url: '/packages/paidcontent/list/index',
      });
    },
    _setNoViewNum(newVal) {
      this.data.badges.certificate.value = newVal > 0;
      this.data.badges.certificate.num = newVal;
      this.setYZData({
        badges: this.data.badges,
      });
    },
    _setIsReadWorkset(newVal) {
      this.data.badges.workset.value = newVal;
      this.setYZData({
        badges: this.data.badges,
      });
    },
    _setWaitRewardCount(newVal) {
      this.data.badges.reward.value = newVal > 0;
      this.data.badges.reward.num = newVal;
      this.setYZData({
        badges: this.data.badges,
      });
    },
    _setCanShowMomentsFlag(newVal) {
      this.data.badges.moments.value = newVal;
      this.setYZData({
        badges: this.data.badges,
      });
    },
    _setReviewToReadNum(newVal) {
      this.data.badges.review.value = newVal > 0;
      this.data.badges.review.num = newVal;
      this.setYZData({
        badges: this.data.badges,
      });
    },
  },
  attached() {
    const rewardConfig = this.properties.services.find(
      (service) => service.title === '奖励'
    );
    if (rewardConfig && rewardConfig.selected) {
      this._observer = wx.createIntersectionObserver(this, { thresholds: [1] });
      this._observer.relativeToViewport().observe('.edu-service', (res) => {
        if (!this._reported && res.intersectionRatio === 1) {
          app.logger &&
            app.logger.log({
              et: 'view',
              ei: 'weapp_uc_reward_display',
              en: '小程序个人中心教育卡片奖励模块曝光',
              pt: 'uc',
              params: {
                kdt_id: app.getKdtId(),
              },
            });
          this._reported = true;
        }
      });
    }
  },
  detached() {
    if (this._observer) {
      this._observer.disconnect();
    }
  },
});
