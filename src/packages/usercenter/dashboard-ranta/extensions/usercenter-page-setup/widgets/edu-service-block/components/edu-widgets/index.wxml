<view class="edu-widgets">
  <classic
    wx:if="{{ mode === 1 }}"
    services="{{ services }}"
    icon-mode="{{ iconMode }}"
    links="{{ links }}"
    badges="{{ badges }}"
    bind:widgetClicked="handleWidgetClicked"
  />
  <cube
    wx:else
    icon-mode="{{ iconMode }}"
    services="{{ services }}"
    icon-mode="{{ iconMode }}"
    links="{{ links }}"
    badges="{{ badges }}"
    bind:widgetClicked="handleWidgetClicked"
  />
</view>
