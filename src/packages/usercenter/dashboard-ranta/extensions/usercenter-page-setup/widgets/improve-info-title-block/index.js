import { RantaWidget } from 'shared/common/base/ranta/widget';
import openWebView from 'utils/open-web-view';
import { getHeight } from 'shared/utils/nav-config';

const getImmersiveStyle = () => {
  const navHeaderHeight = getHeight();
  return `top: ${navHeaderHeight}px;`;
};

RantaWidget({
  properties: {
    levelGrowth: {
      type: Object,
      default: {},
    },
    hiddenInfoCompleted: Boolean,
    kdtId: {
      type: Number,
      value: 0,
    },
    userAgreePrivacy: {
      type: Boolean,
      default: false,
    },
    isImmersive: {
      type: Boolean,
      default: false,
    },
  },

  data: {
    immersiveStyle: getImmersiveStyle(),
  },

  methods: {
    handleCompleteInfoClick() {
      openWebView('/wscuser/levelcenter/fill', {
        query: {
          kdt_id: this.data.kdtId,
          alias: this.data.levelGrowth.freeLevelGroupAlias,
          eT: Date.now(),
        },
      });
    },
  },
});
