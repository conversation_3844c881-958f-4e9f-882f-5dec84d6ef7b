<view
  wx:if="{{ levelGrowth.showInfoCompleted && userAgreePrivacy && hiddenInfoCompleted}}"
  class="member-center__complete-info {{ isImmersive ? 'immersive' : '' }}"
  style="{{ isImmersive ? immersiveStyle : '' }}"
  bindtap="handleCompleteInfoClick"
  i18n-all-ellipsis
>
  <view class="text">
    <van-icon
      custom-class="arrow-custom"
      name="{{ isImmersive ? 'user' : 'https://img01.yzcdn.cn/public_files/f31c2cc64cc43f0ef9e35de9eb29067f.png' }}"
      size="16"
      custom-style="margin-right: 8px; margin-top: {{ isImmersive ? 1 : 3 }}px"
    />
    完善资料尊享更多会员权益
  </view>
  <view>
    <text class="usercenter-level-tip">去完善</text>
    <van-icon
      color="{{ isImmersive ? '#2F2F34' : '#9a640c' }}"
      custom-style="vertical-align: -2px;"
      name="arrow"
      size="{{ isImmersive ? '12' : '14' }}"
      custom-class="arrow-custom"
    />
  </view>
</view>