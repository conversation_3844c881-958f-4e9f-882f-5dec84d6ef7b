.guide-becoming-member {
  &_title {
    margin-top: 24px;
    font-size: 18px;
    line-height: 26px;
    font-weight: 500;
    text-align: center;
  }

  &_subtitle {
    margin-top: 8px;
    font-size: 14px;
    line-height: 20px;
    color: #7d7e80;
    text-align: center;
  }

  &_info {
    position: relative;
    margin-top: 32px;
    margin-bottom: 8px;
    height: 84px;
    background: #fcfaf7;
    border: 0.5px solid #ece3d0;
    box-sizing: border-box;
    border-radius: 4px;
    margin-left: 15%;
    margin-right: 15%;
    display: flex;

    &__left {
      width: 122px;
      display: flex;
      text-align: center;
      color: #b18836;
      align-items: center;
      justify-content: center;

      &_value {
        line-height: 24px;
        font-size: 24px;
        font-weight: 600;
      }

      &_value-unit {
        line-height: 12px;
        font-size: 12px;
        font-weight: 400;
      }

      &_text-value {
        line-height: 20px;
        font-size: 20px;
        font-weight: 500;
      }
    }

    &__img-left {
      width: 84px;
      display: flex;
      text-align: center;
      color: #b18836;
      align-items: center;
      justify-content: center;
    }

    &__right {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-right: 16px;

      &_title {
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
        width: 100%;
      }

      &_subinfo {
        margin-top: 4px;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        width: 100%;
      }

      &.present {
        margin-left: 12px;
      }
    }
  }

  &_coupon-num {
    position: absolute;
    top: -8px;
    right: -16px;
    height: 16px;
    font-size: 12px;
    line-height: 16px;
    color: #fff;
    padding-left: 8px;
    padding-right: 8px;
    background: linear-gradient(
        0deg,
        rgba(255, 255, 255, 0.1),
        rgba(255, 255, 255, 0.1)
      ),
      linear-gradient(90deg, #efc97f 0%, #cc9340 100%);
    border-radius: 22px;
  }

  &_coupon-extra {
    position: absolute;
    border: 0.5px solid #ece3d0;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    height: 4px;
    width: 90%;
    left: 5%;
    bottom: -4.75px;
  }

  &_action {
    margin-top: 28px;
    margin-bottom: 7px;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  &_button {
    border: none;
    border-radius: var(--theme-radius-button, 0px);
    height: 40px;
    width: 91%;
    font-size: 16px;
    line-height: 40px;

    &-text {
      color: #fff;
      text-align: center;
    }
  }

  &_hide-giftbag {
    display: flex;
    align-items: center;
    margin: 24px 20px;
  }

  &_shop-logo {
    margin-right: 8px;
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  &_shop-name {
    font-size: 14px;
    color: #7d7e80;
  }

  &_hint {
    margin-top: 8px;
    margin-left: 20px;
    font-size: 16px;
    line-height: 20px;
    text-align: left;
  }
}

.guide-becoming-member__popup {
  border-radius: var(--theme-radius-card, 0px) !important;
}