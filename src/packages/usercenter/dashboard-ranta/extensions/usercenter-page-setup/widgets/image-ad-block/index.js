import { RantaWidget } from 'shared/common/base/ranta/widget';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

RantaWidget({
  properties: {
    template: Number,

    images: {
      type: Array,
      value: [],
    },
  },
  data: {},
  methods: {
    handleImageClick({ detail }) {
      if (detail.extraData) {
        detail.extra_data = mapKeysCase.toSnakeCase(detail.extraData);
      }
      this.triggerEvent('jumpToLink', detail);
    },
    handleImageChange({ detail }) {
      this.triggerEvent('imageChange', detail.value);
    },
    handleContactBack(e) {
      this.triggerEvent('contactback', e.detail);
    },
  },
});
