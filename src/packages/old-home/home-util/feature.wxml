<!-- 主题信息 -->
<import src="/components/showcase/index.wxml" />

<template name="feature_common">
  <!-- 因为微信暂时无法获取页面 scrollTop 用一个始终置顶的标签的 top 值判断 -->
  <view class="feature-page__top-hook" />
  <salesman-cube
    bind:set-share="handleSalesmanSetShare"
    scenes="feature_page"
  />
  <platform-coupon-pop />

  <flow-entrance-icon biz-name="feature" />

  <!-- page-is-feature 用与在外卖模板控制多网点切换按钮不显示。如果是主页是要显示切换按钮的，feature页面不显示 -->
  <view class="{{pageWindowLock && 'page-locked'}} feature-{{ type }} {{ isFeaturePage ? 'page-is-feature' : ''}}">
    <template wx:if="{{ !fetching }}" is="showcase" data="{{ alias, themeClass, CURRENT_GLOBAL_SHOP, isMultiStore, type, richTextList, banner, tags, goods, systemInfo, scrollIntoView, scrollTop, fixedGoodsTag, theme, showFloatingNav, asyncShowcaseComponents, pageCommonData, pageUtils, salesman }}"/>

    <van-toast id="van-toast" />
    <van-notify id="van-notify" />
  </view>
  <!-- 分享有礼活动，页面加载完成之后，并且有分享标识才展示，展示逻辑在组件内部控制 -->
  <share-activity-pop wx:if="{{ shareTag }}" share-tag="{{ shareTag }}" />
</template>
