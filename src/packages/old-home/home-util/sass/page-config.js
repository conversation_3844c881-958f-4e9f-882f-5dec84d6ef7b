import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { beforeFetchHomepage, featureAliasPromise } from './event';
import { fetchSpecificAlias } from './process';
import { updateFeatureAlias } from './open-state';

export default {
  ...YunPageConfig,

  onLoad() {
    const sdk = this.getYunSdk();
    this.__yunPage = sdk.page;

    // 注册开放事件
    sdk.setPageEvent('beforeFetchHomepage', beforeFetchHomepage);

    // 标准页面的事件-创建订单前
    this.onAsync('beforeFetchHomepage', (args) => {
      // console.log('on native beforeFetchHomepage');
      const res = beforeFetchHomepage.trigger(args);
      if (res && Array.isArray(res)) {
        return res[0];
      }
      return res;
    });

    // 合并
    Promise.all([featureAliasPromise(sdk)]).then(([alias]) => {
      updateFeatureAlias(sdk, alias);
    });

    // 注册开放流程
    sdk.setPageProcess('fetchSpecificAlias', fetchSpecificAlias);
  },

  onUnload() {
    this.offAsync('beforeFetchHomepage');
    this.__yunPage && this.__yunPage.unload();
  }
};
