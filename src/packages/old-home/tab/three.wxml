<timeline-share wx:if="{{ isFromTimeline }}" alias="{{ alias }}" kdt-id="{{ kdtId }}" />

<znb-web-view wx:elif="{{ isWebviewFeature }}" src="{{ webviewFeatureUrl }}" bindmessage="onMessage" />

<block wx:else>
  <import src="../home-util/feature.wxml" />

  <page-container
    show-shop-status
    show-store-switch="{{ type !== 'takeAway' }}"
    switch-page-options="{{ { alias } }}"
    class="page-is-tab-page youzan-{{ appType }} {{ themeClass }} page-{{ deviceType }} {{type === 'ribecaCustom' && 'page-is-tab-page'}}"
    page-bg-color="{{ pageBgColor }}"
    forbid-copyright="{{ type === 'ribecaCustom' }}"
    title-text="{{ pageTitle }}"
    navigationbar-config-data="{{navigationbarConfigData}}"
    extra-clipboard-data="{{ { isNative: true } }}"
    open-custom-nav
    isTabPage
  >
    <template
      is="feature_common"
      data="{{ alias, CURRENT_GLOBAL_SHOP, isMultiStore, themeClass, theme, type, fetching, richTextList, banner, tags, goods, systemInfo, scrollIntoView, scrollTop, fixedGoodsTag, zanTopTips, zanToast, salesman, showFloatingNav, floatingNav, pageCommonData, pageUtils }}"
    ></template>

    <import src="/pages/common/wsc-page/index.wxml" />
    <template is="wsc-page" data="{{ wscpage }}" />
    <custom-tab-bar></custom-tab-bar>
  </page-container>
</block>
