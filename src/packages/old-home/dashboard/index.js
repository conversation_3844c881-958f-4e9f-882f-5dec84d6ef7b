import { updateGpsLocationIfNeeded } from '../utils/gps-location-util';
import WscPage from 'pages/common/wsc-page/index';
import featureUtil from '../home-util/feature';
/* #ifdef BUILD_ENV=youzanyun */
import SaasPageConfig from './sass/page-config';
/* #endif */

const app = getApp();
let extendPageConfig = {};
/* #ifdef BUILD_ENV=youzanyun */
extendPageConfig = SaasPageConfig;
/* #endif */
WscPage(featureUtil, extendPageConfig, {
  onLoad() {
    updateGpsLocationIfNeeded();
    // 延迟加载进店有礼数据，保证首页先展示
    this.on('feature:loaded', ({ templateInPackage }) => {
      // 如果首页模板跳分包，就不要请求进店有礼
      if (!templateInPackage && !this.__shopPopFetched) {
        this.__shopPopFetched = true;
        this.setYZData({
          showShopPopManager: true
        });
      }
    });
  },

  onUnload() {
    this.off('feature:loaded');
  },

  onWxUpgradeBtnClick() {
    app.storage.set('showWxUpgradeTip', true, { expire: 14 });
    this.setYZData({ showWxUpgradeTip: false });
  }
});
