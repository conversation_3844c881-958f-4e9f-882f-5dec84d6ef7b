<timeline-share wx:if="{{ isFromTimeline }}" alias="{{ alias }}" kdt-id="{{ kdtId }}" is-home-page />

<znb-web-view wx:elif="{{ isWebviewFeature }}" src="{{ webviewFeatureUrl }}" bindmessage="onMessage" />

<block wx:else>
  <import src="../home-util/feature.wxml" />

  <page-container
    show-service-due
    show-shop-status
    show-store-switch="{{ type !== 'takeAway' && !isRetailShelfTemplate}}"
    switch-page-options="{{ { alias } }}"
    class="page-is-tab-page youzan-{{ appType }} {{ themeClass }} page-{{ deviceType }} {{type === 'ribecaCustom' && 'page-is-tab-page'}}"
    page-bg-color="{{ pageBgColor }}"
    forbid-copyright="{{ type === 'ribecaCustom' }}"
    isShowStoreInfo="{{true}}"
    buyer-address="{{ buyerAddress }}"
    title-text="{{ pageTitle }}"
    navigationbar-config-data="{{navigationbarConfigData}}"
    extra-clipboard-data="{{ { isNative: true } }}"
    open-custom-nav
    isTabPage
  >
    <template
      is="feature_common"
      data="{{ CURRENT_GLOBAL_SHOP, isMultiStore, themeClass, theme, type, fetching, richTextList, banner, tags, goods, systemInfo, scrollIntoView, scrollTop, fixedGoodsTag, zanTopTips, zanToast, salesman, showFloatingNav, floatingNav, buyerAddress, pageCommonData, pageUtils, shareTag }}"
    />

    <!-- 收藏小程序提示 -->
    <collect-tip />

    <collect-gift wx:if="{{ appType === 'wsc' }}" />

    <!-- 店铺弹出层管理 -->
    <shop-pop-manager wx:if="{{ showShopPopManager }}" source="{{ 1 }}" />

    <van-popup
      show="{{ showWxUpgradeTip }}"
      custom-style="background-color: transparent;bottom: 49px"
      position="bottom"
      overlay="{{ false }}"
      close-on-click-overlay="{{ false }}"
    >
      <view class="wx-upgrade-popup-content van-clearfix">
        <view class="wx-upgrade-text">由于微信版本过低，可能会影响你的浏览体验，建议你升级至微信最新版。</view>
        <van-button
          plain
          round
          block
          size="small"
          type="primary"
          class="wx-upgrade-btn"
          bind:click="onWxUpgradeBtnClick"
        >我知道了</van-button>
      </view>
    </van-popup>
    <custom-tab-bar forceShow></custom-tab-bar>
  </page-container>

  <import src="/pages/common/wsc-page/index.wxml" />
  <template is="wsc-page" data="{{ wscpage, youzanAppHideTabbar }}" />
</block>
