<timeline-share wx:if="{{ isFromTimeline }}" alias="{{ alias }}" kdt-id="{{ kdtId }}" />

<znb-web-view wx:elif="{{ isWebviewFeature }}" src="{{ webviewFeatureUrl }}" bindmessage="onMessage" />

<block wx:else>
  <import src="../home-util/feature.wxml" />

  <page-container
    show-shop-status
    show-store-switch="{{ type !== 'takeAway' }}"
    switch-page-options="{{ { alias } }}"
    class="youzan-{{ appType }} {{ themeClass }} page-{{ deviceType }} {{type === 'ribecaCustom' && 'page-is-tab-page'}}"
    page-bg-color="{{ pageBgColor }}"
    forbid-copyright="{{ type === 'ribecaCustom' }}"
    title-text="{{ pageTitle }}"
    navigationbar-config-data="{{ navigationbarConfigData }}"
    open-custom-nav
    extra-clipboard-data="{{ { isNative: true } }}"
  >
    <template
      is="feature_common"
      data="{{ alias, CURRENT_GLOBAL_SHOP, isMultiStore, themeClass, theme, type, fetching, richTextList, banner, tags, goods, systemInfo, scrollIntoView, scrollTop, fixedGoodsTag, zanTopTips, zanToast, isFeaturePage, salesman, showFloatingNav, floatingNav, pageCommonData, pageUtils, shareTag }}"
    ></template>

    <!-- 抽奖码小icon -->
    <lottery-code-icon wx:if="{{ showLotteryCode }}" />
  </page-container>

  <shop-pop-manager wx:if="{{ showFeatureVisitGift }}" source="{{ 4 }}" />

  <import src="/pages/common/wsc-page/index.wxml" />
  <template is="wsc-page" data="{{ wscpage }}" />
</block>

