const app = getApp();

// 集点卡：详情
export function queryDetailCollectPointsInfo(activityId, alias) {
  const hqKdtId = app.getHQKdtId();
  // 直接查总部的商品： 出了问题找产品：zhoulin
  const kdtId = app.getHQKdtId();
  const userId = app.getToken('buyerId') || app.getToken('userId');

  return app.request({
    path: '/retail/h5/miniprogram/queryDetailCollectPointsInfo.json',
    data: {
      kdtId,
      hqKdtId,
      userId,
      activityId,
      alias
    }
  });
}

// 集点卡：兑换记录
export function queryBenefitOperateRecordInfo(
  activityId,
  alias,
  pageNo = 1,
  pageSize = 20
) {
  const hqKdtId = app.getHQKdtId();
  // 直接查总部的商品： 出了问题找产品：zhoulin
  const kdtId = app.getHQKdtId();
  const userId = app.getToken('buyerId') || app.getToken('userId');

  return app.request({
    path: '/retail/h5/miniprogram/queryBenefitOperateRecordInfo.json',
    data: {
      kdtId,
      hqKdtId,
      userId,
      activityId,
      alias,
      pageNo,
      pageSize
    }
  });
}
