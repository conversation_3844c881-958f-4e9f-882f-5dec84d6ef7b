.collect-points-detail {
    width: 100vw;
    height: 100vh;
    background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>);
    background-repeat: no-repeat;
    background-size: 100vw 600rpx;

    @keyframes easein {
        from {
            height: 0;
        }
        to {
            height: 64rpx;
        }
    }

    .activity-status {
        background-color: #ebedf0;
        width: 100vw;
        height: 64rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #646566;
        font-size: 26rpx;
        animation: easein ease 0.5s;
    }

    .points-detail {
        &-header {
            padding: 48rpx 0 0 32rpx;
            margin-bottom: 48rpx;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            color: #fff;
            &__title {
                font-size: 40rpx;
                font-weight: bolder;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            &__btn {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                font-size: 24rpx;
                padding: 8rpx 22rpx;
                background-color: rgba(0, 0, 0, 0.12);
                border-radius: 76rpx 0 0 76rpx;
            }
        }

        &-points {
            margin: 32rpx;
            padding: 32rpx;
            background-color: #fff;
            border-radius: 16rpx;
            box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.08);

            &__title {
                display: flex;
                flex-direction: row;
                align-items: center;
                &-tip {
                    font-size: 32rpx;
                    color: #323233;
                    font-weight: bold;
                }
                &-points {
                    margin-left: 8rpx;
                    color: #969799;
                    font-size: 24rpx;
                }
            }

            &__body {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                &-item {
                    width: 20%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-top: 32rpx;
                }
            }
        }

        &-exchange {
            margin: 32rpx;
            padding: 32rpx;
            background-color: #fff;
            border-radius: 16rpx;
            box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.08);

            &__title {
                font-size: 32rpx;
                color: #323233;
                font-weight: bold;
            }

            &__goods {
                display: flex;
                flex-direction: row;
                height: 144rpx;
                box-sizing: border-box;
                margin-top: 32rpx;
                border-radius: 16rpx;
                background-color: #f7f8fa;
                padding: 16rpx;

                &-image {
                    width: 112rpx;
                    height: 112rpx;
                    flex-shrink: 0;
                }
                &-info {
                    flex:1;
                    margin: 10rpx 16rpx;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    overflow: hidden;

                    &__header {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;
                        overflow: hidden;
                        &-title {
                            font-size: 26rpx;
                            color: #323233;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        &-tip {
                            color: #DF4545;
                            font-size: 24rpx;
                            font-weight: bolder;
                            flex-shrink: 0;
                        }
                    }

                    &__points {
                        display: flex;
                        flex-direction: row;
                        align-items: center;

                        &-count {
                            color: #ee0a24;
                            font-size: 32rpx;
                            font-weight: bold;
                        }

                        &-tip {
                            margin-left: 4rpx;
                            font-size: 26rpx;
                            color: #969799;
                        }
                    }
                }
            }
        }

        &-info {
            margin: 32rpx;
            border-radius: 16rpx;
            background-color: #fff;
            box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.08);
            padding: 32rpx 24rpx;

            &__title {
                font-size: 32rpx;
                font-weight: bold;
                color: #323233;
                margin-bottom: 32rpx;
            }

            &__item {
                display: flex;
                font-size: 26rpx;
                margin-bottom: 16rpx;

                &-tip {
                    color: #969799;
                    flex-shrink: 0;
                }

                &-msg {
                    color: #646566;
                }
            }
        }
    }
}
