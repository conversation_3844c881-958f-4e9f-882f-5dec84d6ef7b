<view class="collect-points-detail">
    <view class="activity-status" wx:if="{{stage === 1 || stage === 3}}">
        {{stage === 1 ? '活动未开始' : '活动已结束'}}
    </view>
    <view class="points-detail">
        <view class="points-detail-header">
            <view class="points-detail-header__title">{{activityTitle}}</view>
            <view class="points-detail-header__btn" bind:tap="showPointsRecordPopup">
                集点记录
                <van-icon name="arrow" color="#fff"/>
            </view>
        </view>
        <view class="points-detail-points">
            <view class="points-detail-points__title">
                <view class="points-detail-points__title-tip">活动集点</view>
                <view class="points-detail-points__title-points">{{pointsNum}}/{{costPoints}}</view>
            </view>
            <swiper 
                indicator-dots="{{limitNumArr.length > 1}}"
                current="{{currentSwiperNo < limitNum ? currentSwiperNo : limitNum - 1}}"
                style="height: {{(88 + 32) * pointsNumRows + 32 * 2}}rpx"
                indicator-active-color="#EE0A24"
                indicator-color="#F2F3F5"
            >
                <block wx:for="{{limitNumArr}}" wx:key="*this" wx:for-index="indexNo">
                    <swiper-item wx:if="{{indexNo === currentSwiperNo}}">
                        <view class="points-detail-points__body">
                            <view 
                                wx:if="{{!!costPoints}}"
                                wx:for="{{costPointsArr}}" 
                                wx:for-index="idx" 
                                wx:key="idx"
                                class="points-detail-points__body-item"
                            >
                                <cicle-points wx:if="{{idx+1 === costPoints && currentSwiperNoPointsNum >= costPoints}}" type="{{2}}" number="{{idx+1}}" />
                                <cicle-points wx:elif="{{idx+1 <= currentSwiperNoPointsNum}}" type="{{0}}" number="{{idx+1}}" />
                                <cicle-points wx:elif="{{idx+1 > currentSwiperNoPointsNum && idx+1 !== costPoints}}" type="{{1}}" number="{{idx+1}}" />
                                <cicle-points wx:else type="{{3}}" number="{{idx+1}}" />
                            </view>
                        </view>
                    </swiper-item>
                    <swiper-item wx:if="{{indexNo < currentSwiperNo}}">
                        <view class="points-detail-points__body">
                            <view 
                                wx:if="{{!!costPoints}}"
                                wx:for="{{costPointsArr}}" 
                                wx:for-index="idx" 
                                wx:key="idx"
                                class="points-detail-points__body-item"
                            >
                                <cicle-points wx:if="{{idx+1 === costPoints}}" type="{{2}}" number="{{idx+1}}" />
                                <cicle-points wx:if="{{idx+1 < costPoints}}" type="{{0}}" number="{{idx+1}}" />
                            </view>
                        </view>
                    </swiper-item>
                    <swiper-item wx:if="{{indexNo > currentSwiperNo}}">
                        <view class="points-detail-points__body">
                            <view 
                                wx:if="{{!!costPoints}}"
                                wx:for="{{costPointsArr}}" 
                                wx:for-index="idx" 
                                wx:key="idx"
                                class="points-detail-points__body-item"
                            >
                                <cicle-points wx:if="{{idx+1 < costPoints}}" type="{{1}}" number="{{idx+1}}" />
                                <cicle-points wx:if="{{idx+1 === costPoints}}" type="{{3}}" number="{{idx+1}}" />
                            </view>
                        </view>
                    </swiper-item>
                </block>
            </swiper>
        </view>
        <view class="points-detail-exchange">
            <view class="points-detail-exchange__title">兑换权益</view>
            <view class="points-detail-exchange__goods">
                <image src="{{goodsImg}}" class="points-detail-exchange__goods-image"/>
                <view class="points-detail-exchange__goods-info">
                    <view class="points-detail-exchange__goods-info__header">
                        <view class="points-detail-exchange__goods-info__header-title">
                            {{goodsTitle}}
                        </view>
                        <view class="points-detail-exchange__goods-info__header-tip">
                            {{stage === 3 ? '已结束' : (pointsNum >= costPoints) ? '待兑换' : ''}}
                        </view>
                    </view>
                    <view class="points-detail-exchange__goods-info__points">
                        <view class="points-detail-exchange__goods-info__points-count">
                            {{costPoints}}
                        </view>
                        <view class="points-detail-exchange__goods-info__points-tip">
                            集点
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="points-detail-info">
            <view class="points-detail-info__title">活动详情</view>
            <view 
                class="points-detail-info__item" 
                wx:for="{{activityInfoDetailItems}}" 
                wx:for-item="info" 
                wx:key="index"
            >
                <view class="points-detail-info__item-tip">
                    {{info[0]}}
                </view>
                <view class="points-detail-info__item-msg">
                    {{info[1]}}
                </view>
            </view>
        </view>
    </view>
</view>


<points-records 
    show="{{showPointsRecords}}"
    alias="{{alias}}"
    activityId="{{activityId}}"
    bind:closePopup="hidePointsRecordPopup"
/>