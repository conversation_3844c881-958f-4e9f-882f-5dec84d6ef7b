import { moment as formatDate } from 'utils/time';
import { queryBenefitOperateRecordInfo } from '../api';

Component({
  options: {
    pureDataPattern: /^_/
  },
  properties: {
    activityId: Number,
    alias: String,
    show: <PERSON><PERSON><PERSON>,
    recordInfoList: {
      type: Array,
      value: []
    }
  },
  observers: {
    async show(val) {
      if (!val) {
        this.setData({
          _currentPageNo: 1,
          recordInfoList: [],
          hasMore: false
        });
        return;
      }
      const { _currentPageNo, _pointPageSize } = this.data;
      const records = await this.getBenefitOperateRecordInfo(_currentPageNo);
      if (!records) return;
      this.setData({
        hasMore: records.length ? records.length === _pointPageSize : false,
        recordInfoList: records.map(item => {
          item.time = formatDate(item.createAt, 'YYYY-MM-DD HH:mm:ss');
          return item;
        })
      });
    }
  },

  data: {
    _currentPageNo: 1,
    _pointPageSize: 10,
    _isFetching: false,
    hasMore: false
  },

  methods: {
    closePopup() {
      this.triggerEvent('closePopup');
    },

    async handleFetchMore() {
      const {
        hasMore,
        _isFetching,
        _currentPageNo,
        _pointPageSize,
        recordInfoList
      } = this.data;
      if (!hasMore || _isFetching) return;
      this.setData({
        _isFetching: true
      });
      const records = await this.getBenefitOperateRecordInfo(
        _currentPageNo + 1
      );
      if (!records) {
        this.setData({
          _isFetching: false
        });
        return;
      }

      this.setData({
        _isFetching: false,
        _currentPageNo: _currentPageNo + 1,
        hasMore: records.length === _pointPageSize,
        recordInfoList: recordInfoList.concat(
          records.map(item => {
            item.time = formatDate(item.createAt, 'YYYY-MM-DD HH:mm:ss');
            return item;
          })
        )
      });
    },

    async getBenefitOperateRecordInfo(currentPageNo) {
      try {
        wx.showLoading({
          title: '加载中'
        });
        const { activityId, alias, _pointPageSize } = this.data;
        const result = await queryBenefitOperateRecordInfo(
          activityId,
          alias,
          currentPageNo,
          _pointPageSize
        );
        return result.recordInfoList || [];
      } catch (error) {
        wx.showToast({
          icon: 'none',
          title: error.msg || '获取兑换记录失败，请重试！'
        });
        return null;
      } finally {
        wx.hideLoading();
      }
    }
  }
});
