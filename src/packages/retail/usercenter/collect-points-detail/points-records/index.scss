.records-popup {
    display: flex;
    flex-direction: column;
    border-radius: 40rpx 40rpx 0 0;
}

.records-header {
    position: relative;
    height: 96rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    &__title {
        color: #323233;
        font-size: 32rpx;
        font-weight: bold;
    }

    &__btn {
        position: absolute;
        top: 26rpx;
        right: 32rpx;
        color: #c0c2c5;
    }
}

.records-body {
    height: 800rpx;

    .record-empty {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #969799;
        font-size: 28rpx;
    }

    .record-item:not(:first-child) {
        border-top: 1rpx solid #EBEDF0;
    }
    .record-item {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        min-height: 144rpx;
        box-sizing: border-box;
        padding: 28rpx 0;
        margin: 0 32rpx;

        &__left {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
            &-title {
                font-size: 32rpx;
                color: #323233;
            }
            &-time {
                font-size: 24rpx;
                color: #969799;
            }
        }

        &__right {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-end;
            font-size: 32rpx;
            margin-left: 40rpx;
            &-points-asc {
                color: #df4545;
            }
            &-points-desc {
                color: #323233;
            }

            &-shop {
                font-size: 24rpx;
                color: #969799;
                text-align: right;
            }
        }
    }
}

.loading {
    display: flex;
    height: 88rpx;
}
