<popup
  position="bottom"
  z-index="{{1024}}"
  show="{{show}}"
  custom-class="records-popup"
  bind:close="closePopup"
  overlay
>
  <view class="records-header">
    <view class="records-header__title">集点记录</view>
    <van-icon
      size="22px"
      name="cross"
      bind:tap="closePopup"
      custom-class="records-header__btn"
    />
  </view>
  <scroll-view 
      class="records-body"
      scroll-y
      lower-threshold="100"
      bindscrolltolower="handleFetchMore"
    >
    <view 
      class="record-empty"
      wx:if="{{!recordInfoList || !recordInfoList.length}}"
    >
      暂无集点记录
    </view>
    <block wx:else>
      <view class="record-item" wx:for="{{recordInfoList}}" wx:key="createAt" wx:for-item="record">
        <view class="record-item__left">
          <view class="record-item__left-title">{{record.changeTypeDesc}}</view>
          <view class="record-item__left-time">{{record.time}}</view>
        </view>
        <view class="record-item__right">
          <view class="record-item__right-points record-item__right-points-{{record.changeType === 1 ? 'asc' : 'desc'}}">{{record.changeType === 1 ? '+' : '' }}{{record.pointsNum}}</view>
          <view class="record-item__right-shop">{{record.shopName}}</view>
        </view>
      </view>
      <view wx:if="{{hasMore}}" class="loading">
        <loading style="margin: auto" />
      </view>
    </block>
  </scroll-view>

</popup>