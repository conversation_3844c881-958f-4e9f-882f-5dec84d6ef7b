import { moment as formatDate } from 'utils/time';
import { queryDetailCollectPointsInfo } from './api';

/**
 * activityType:
 * 1:满xx,送1个集点 (非循环,范围:全部)
 * 2:每满xx,送1个集点 (循环,范围:全部)
 * 3:购买xx商品时,送1个集点 (非循环,范围:部分)
 *
 * stage:
 * 0: 所有，
 * 1: 未开始，
 * 2: 进行中，
 * 3: 已结束，
 * 4: 未结束
 */
Page({
  data: {
    // 活动ID
    activityId: 0,
    // 活动alias
    alias: '',
    //   活动名称
    activityTitle: '',
    // 活动商品图片
    goodsImg: '',
    // 活动商品名称
    goodsTitle: '',
    // 活动所需点数
    costPoints: 0,
    // 活动所需点数，辅助数组
    costPointsArr: [],
    // 当前集点数
    pointsNum: 0,
    // 当前进行的swiper-item-no中的pointsNum
    currentSwiperNoPointsNum: 0,
    // 当前进行的Swiper-item-No
    currentSwiperNo: 0,
    // 活动状态
    stage: 0,
    // 限购次数, 至少可以兑换一次吧
    limitNum: 1,
    // 限购次数数组, 辅助数组
    limitNumArr: [],
    // 活动详情描述数组
    activityInfoDetailItems: [],
    // 动态计算集点的swiperItem的高度 （坑。swiper组件居然不支持自适应高度）
    pointsNumRowHeight: 0,
    // 是否展示集点记录popup
    showPointsRecords: false
  },

  async onLoad({ activityId, alias }) {
    try {
      wx.showLoading({
        title: '加载中'
      });
      const result = await queryDetailCollectPointsInfo(activityId, alias);
      const { pointsNum = 0, activityModel = {} } = result;
      const {
        name,
        costPoints,
        stage,
        limitNum = 1,
        presentGoodsInfo = []
      } = activityModel;
      this.setData({
        stage,
        alias,
        pointsNum,
        activityId,
        limitNum,
        limitNumArr: Array(limitNum),
        activityTitle: name,
        costPoints,
        costPointsArr: Array(costPoints),
        currentSwiperNo: Math.floor(pointsNum / costPoints),
        currentSwiperNoPointsNum: pointsNum % costPoints,
        // 一行最多5个集点
        pointsNumRows: Math.ceil(costPoints / 5),
        goodsTitle: presentGoodsInfo[0]?.goodsName,
        goodsImg: presentGoodsInfo[0]?.picture[0]?.url ?? '',
        activityInfoDetailItems: this.getActivityDetailInfoItems(activityModel)
      });
    } catch (error) {
      wx.showToast({
        icon: 'none',
        title: error.msg || '加载集点活动详情失败, 请重试'
      });
    } finally {
      wx.hideLoading();
      wx.stopPullDownRefresh();
    }
  },

  onPullDownRefresh() {
    const { activityId, alias } = this.data;
    this.onLoad({ activityId, alias });
  },

  getActivityDetailInfoItems({
    name,
    activityType,
    startAt,
    endAt,
    conditionAmount,
    costPoints,
    description
  }) {
    return [
      ['活动名称：', name],
      [
        '活动时间：',
        `${formatDate(startAt, 'YYYY-MM-DD')} 至 ${formatDate(
          endAt,
          'YYYY-MM-DD'
        )}`
      ],
      ['集点方式：', this.getActivityStyleName(activityType, conditionAmount)],
      ['兑换规则：', `集满${costPoints}点可兑换礼品`],
      ['活动说明：', description || '']
    ];
  },

  getActivityStyleName(activityType, conditionAmount) {
    if (activityType === 1) {
      return `满${this.toPrice(conditionAmount)}元，送1个集点`;
    }
    if (activityType === 2) {
      return `每满${this.toPrice(conditionAmount)}元，送1个集点`;
    }
    if (activityType === 3) {
      return `购买指定商品时，送1个集点 (非循环,范围:部分)`;
    }
  },

  toPrice(price) {
    price /= 100;
    // 不支持 (price / 100).toFixed
    return price.toFixed(2);
  },

  async showPointsRecordPopup() {
    this.setData({
      showPointsRecords: true
    });
  },

  hidePointsRecordPopup() {
    this.setData({
      showPointsRecords: false
    });
  }
});
