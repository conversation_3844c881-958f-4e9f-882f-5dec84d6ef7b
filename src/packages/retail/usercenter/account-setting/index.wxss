.account-setting {
  background-color: #fff;
}

.account-setting__item {
  height: 128rpx;
  line-height: 128rpx;
  border-bottom: 1rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.account-setting__item--last {
  border-bottom: none;
  margin-bottom: 40rpx;
}

.avatar-region {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  float: right;
}

.account-setting__item--bind,
.account-setting__item--gender,
.account-setting__item--birthday {
  color: #999;
  font-size: 32rpx;
  line-height: 40rpx;
  width: 350rpx;
  text-align: right;
  padding: 30rpx 20rpx;
  position: relative;
}

.account-setting__item--arrow,
.account-setting__item--gender::after,
.account-setting__item--birthday::after {
  position: absolute;
  top: 50%;
  right: 4rpx;
  content: ' ';
  height: 12rpx;
  width: 12rpx;
  border-color: #999;
  border-width: 4rpx 4rpx 0 0;
  border-style: solid;
  transform: translateY(-50%) matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
}

.account-setting__item--bind::after {
  border: none;
}

.account-setting__item--bind {
  opacity: 1;
  background-color: #fff;
  margin: 0;
  border: 1px solid #fff;
  border-color: #fff;
  border-radius: 0;
}

.sex-select {
  width: 100vw;
}

.account-setting,
.save-btn {
  padding: 0 60rpx;
}
