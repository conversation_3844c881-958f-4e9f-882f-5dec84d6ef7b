import { moment } from '@youzan/weapp-utils/lib/time';
import {
  joinMember,
  notifyOfflineMemberSignFailed,
  signSceneCallback
} from 'retail/util/bind-mobile';
import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    genderList: [
      {
        value: '1',
        name: '男'
      },
      {
        value: '2',
        name: '女'
      }
    ],
    showSexSelect: false,
    showDatePicker: false,
    hasBirthday: false,
    minDate: new Date(1970, 1, 1).getTime(),
    maxDate: new Date().getTime(),
    currentDate: new Date().getTime(),
    birthday: '',
    gender: '',
    genderText: '',
    loading: true,
    submiting: false,
    mobile: '',
    originKdtId: '',
    memberSrcWay: '',
    memberSrcChannel: ''
  },

  onLoad(options) {
    const {
      origin_kdt_id: originKdtId,
      member_src_way: memberSrcWay,
      member_src_channel: memberSrcChannel
    } = options;

    this.setYZData({
      originKdtId: +originKdtId || app.getKdtId(),
      memberSrcWay: +memberSrcWay || 800,
      memberSrcChannel: +memberSrcChannel || 102
    });
  },

  onPullDownRefresh() {
    this.fetchUserInfo();
  },

  toggleDatePicker() {
    const { showDatePicker, hasBirthday } = this.data;
    if (showDatePicker) {
      this.setData({
        showDatePicker: false
      });
    } else if (hasBirthday) {
      this.showModifyTip();
    } else {
      this.setData({
        showDatePicker: true
      });
    }
  },

  confirmBirthday({ detail }) {
    const currentDate = new Date(detail);
    this.setData({
      birthday: moment(currentDate, 'YYYY-MM-DD'),
      currentDate: +currentDate
    });
    this.toggleDatePicker();
  },

  fetchUserInfo() {
    const { signQrcodeKey, isFirstSign, signKdtId } = app.globalData;

    const data = {};

    if (isFirstSign && signQrcodeKey) {
      data.qr_code = signQrcodeKey;
      data.member_src_way = 800;
      data.member_src_channel = 1000;
      data.kdt_id = signKdtId || app.getKdtId();
    }

    app.carmen({
      api: 'youzan.retail.scrm.customer.assets/1.0.0/get',
      data,
      success: ({
        customer_info: customerInfo = {},
        user_info: userInfo = {},
        is_login_cash: isLoginCashier
      }) => {
        const { birthday, gender, is_member: isMember } = customerInfo;
        const { mobile } = userInfo;

        if (isLoginCashier === false) {
          notifyOfflineMemberSignFailed();
        }

        const data = {
          loading: false
        };

        if (birthday) {
          data.birthday = moment(birthday, 'YYYY-MM-DD');
          data.hasBirthday = true;
        }

        if (gender) {
          data.gender = '' + gender;
          data.genderText = this.getGenderText(gender);
          data.hasGender = true;
        }

        if (mobile) {
          data.mobile = mobile;
        }

        this.setYZData(data);

        if (isMember && isFirstSign && signQrcodeKey && isLoginCashier) {
          signSceneCallback('cashier');
        }
      },
      fail: res => {
        wx.showToast({
          title: (res && res.msg) || '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setYZData({
          fetching: false
        });
        wx.stopPullDownRefresh();
      }
    });
  },

  onShow() {
    this.fetchUserInfo();
  },

  goJoinMember() {
    const { originKdtId, memberSrcWay, memberSrcChannel } = this.data;

    joinMember(() => this.fetchUserInfo(), {
      kdt_id: originKdtId || app.getKdtId(),
      member_src_way: memberSrcWay || 800,
      member_src_channel: memberSrcChannel || 1000,
      need_be_member: true
    });
  },

  // getPhoneNumber(event) {
  //   if (this.data.mobile) {
  //     return;
  //   }

  //   const { originKdtId, memberSrcWay, memberSrcChannel } = this.data;

  //   wxLogin({
  //     event,
  //     redirectUrl: false,
  //     success: () => {
  //       joinMember(() => this.fetchUserInfo(), {
  //         kdt_id: originKdtId || app.getKdtId(),
  //         member_src_way: memberSrcWay || 800,
  //         member_src_channel: memberSrcChannel || 1000,
  //         need_be_member: true
  //       });
  //     }
  //   });
  // },

  toggleSexPopup() {
    this.setYZData({
      showSexSelect: !this.data.showSexSelect
    });
  },

  getGenderText(gender) {
    const selectItem = this.data.genderList.find(item => item.value == gender);
    return (selectItem && selectItem.name) || '';
  },

  handleChangeGender({ detail }) {
    this.setYZData({
      gender: detail.value,
      genderText: this.getGenderText(detail.value)
    });
    this.toggleSexPopup();
  },

  showModifyTip() {
    wx.showToast({
      title: '请联系商家修改',
      icon: 'none'
    });
  },

  handleSubmit() {
    const data = {};
    let hasBirthday = false;
    let hasGender = false;

    const { birthday } = this.data;

    if (!this.data.hasBirthday && birthday) {
      data.birthday = birthday ? moment(birthday, 'YYYY-MM-DD') : '';
      hasBirthday = true;
    }

    if (!this.data.hasGender && this.data.gender) {
      data.gender = Number(this.data.gender);
      hasGender = true;
    }

    if (Object.keys(data).length > 0) {
      this.setYZData({
        submiting: true
      });

      app.carmen({
        api: 'youzan.retail.scrm.customer.baseinfo/1.0.0/update',
        data,
        success: () => {
          const disable = {};
          if (hasBirthday) {
            disable.hasBirthday = true;
          }
          if (hasGender) {
            disable.hasGender = true;
          }
          this.setYZData(disable);
          wx.showToast({
            title: '保存成功',
            icon: 'none'
          });
        },
        fail: res => {
          wx.showToast({
            title: (res && res.msg) || '网络错误',
            icon: 'none'
          });
        },
        complete: () => {
          this.setYZData({
            submiting: false
          });
        }
      });
    }
  }
});
