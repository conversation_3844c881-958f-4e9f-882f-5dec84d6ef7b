<view class="page-container">
  <view class="container">
    <view class="account-setting">
      <view class="account-setting__item">
        <text>头像</text>
        <view class="avatar-region">
          <open-data type="userAvatarUrl" />
        </view>
      </view>
      <view class="account-setting__item">
        <text>昵称</text>
        <open-data type="userNickName" />
      </view>
      <view class="account-setting__item">
        <text>性别</text>
        <view wx:if="{{hasGender}}" catch:tap="showModifyTip">{{genderText}}</view>
        <view
          wx:else
          class="{{ genderText ? '' : 'account-setting__item--gender'}}"
          bind:tap="toggleSexPopup"
        >
          {{genderText || '请选择'}}
        </view>
      </view>
      <view class="account-setting__item" bind:tap="toggleDatePicker">
        <text>生日</text>
        <text wx:if="{{birthday}}">{{birthday}}</text>
      </view>
      <view class="account-setting__item account-setting__item--last">
        <text>手机</text>
        <view wx:if="{{mobile}}">{{mobile}}</view>
        <view wx:else class="account-setting__item--bind">
          <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="goJoinMember">
              去绑定
              <!-- <view class="account-setting__item--arrow"/> -->
          </user-authorize>
        </view>
        <!-- <button
          wx:else
          class="account-setting__item--bind"
          open-type="getPhoneNumber"
          bindgetphonenumber="getPhoneNumber"
        >
          去绑定
          <view class="account-setting__item--arrow"/>
        </button> -->
      </view>
    </view>
    <view style="margin: auto; width: 60%">
      <btn
        size="large"
        wx:if="{{!loading && (!hasBirthday || !hasGender)}}"
        custom-class="save-btn"
        type="primary" 
        disabled="{{submiting}}" 
        bind:click="handleSubmit"
      >
        保存
      </btn>
    </view>
  </view>
</view>
<popup 
  position="bottom" 
  show="{{!hasBirthday && showDatePicker}}"
  bind:close="toggleDatePicker"
>
  <date-picker
    type="date"
    bind:cancel="toggleDatePicker"
    bind:confirm="confirmBirthday"
    min-date="{{minDate}}"
    max-date="{{maxDate}}"
    value="{{currentDate}}"
  />
</popup>
<action-sheet 
  actions="{{genderList}}"
  show="{{showSexSelect}}"
  bind:close="toggleSexPopup"
  bind:select="handleChangeGender"
/>

<van-dialog id="van-dialog" />
<account-wx-login id="account-wx-login" />