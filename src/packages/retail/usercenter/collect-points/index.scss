.collect-cart-container {
    padding: 16px;

    .collect-cart {
        width: 100%;
        min-height: 308rpx;
        margin-bottom: 32rpx;
        border-radius: 16rpx;
        background-color: #fff;
        box-shadow: 0 4rpx 20rpx 0 rgba(100, 101, 102, 0.16);
        box-sizing: border-box;
        overflow: hidden;
        &:active {
            opacity: 0.8;
        }

        &__header-doing {
            // background-image: -webkit-linear-gradient(left,#FF4444, #FF8855);
            background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>);
            background-size: 100%;
        }

        &__header-notstart,
        &__header-end {
            // background-image: -webkit-linear-gradient(left,#979699, #cecece);
            background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>);
            background-size: 100%;
        }

        &__header {
            display: flex;
            justify-content: space-between;
            height: 128rpx;
            padding: 32rpx 28rpx 22rpx;
            box-sizing: border-box;

            &-left {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                overflow: hidden;

                &__title {
                    color: #fff;
                    font-size: 32rpx;
                    font-weight: bold;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                &__time {
                    color: #fff;
                    font-size: 26rpx;
                }
            }

            &-right {
                display: flex;
                flex-direction: row-reverse;
                align-items: center;
                flex-shrink: 0;

                &__btn {
                    font-size: 24rpx;
                    font-weight: bold;
                    padding: 6rpx 16rpx;
                    box-sizing: border-box;
                    border-radius: 22rpx;
                    background-color: #fff;
                }

                &__doing {
                    color: #df4545;
                }

                &__notstart,
                &__end {
                    color: #969799;
                }
            }
        }

        &__body {
            padding: 48rpx 32rpx;
            box-sizing: border-box;

            &-style {
                display: flex;
                margin-bottom: 16rpx;
                &__title {
                    flex-shrink: 0;
                    color: #969799;
                    font-size: 26rpx;
                }

                &__msg {
                    color: #646566;
                    font-size: 26rpx;
                }
            }
        }
    }
}
