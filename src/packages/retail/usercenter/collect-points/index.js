import { moment as formatDate } from 'utils/time';
import { queryActivityListPaged } from './api';

/**
 * activityType:
 * 1:满xx,送1个集点 (非循环,范围:全部)
 * 2:每满xx,送1个集点 (循环,范围:全部)
 * 3:购买xx商品时,送1个集点 (非循环,范围:部分)
 *
 * stage:
 * 0: 所有，
 * 1: 未开始，
 * 2: 进行中，
 * 3: 已结束，
 * 4: 未结束
 */
Page({
  data: {
    activities: []
  },

  toPrice(price) {
    price /= 100;
    // 不支持 (price / 100).toFixed
    return price.toFixed(2);
  },

  getActivityStyleName(activityType, conditionAmount) {
    if (activityType === 1) {
      return `满${this.toPrice(conditionAmount)}元，送1个集点`;
    }
    if (activityType === 2) {
      return `每满${this.toPrice(conditionAmount)}元，送1个集点`;
    }
    if (activityType === 3) {
      return `购买指定商品时，送1个集点 (非循环,范围:部分)`;
    }
  },

  async onLoad() {
    const activities = await this.fetchCollectPointsActivities(
      this.currentPage
    );
    this.setData({
      activities
    });
  },

  toDetail({ currentTarget }) {
    wx.vibrateShort({
      complete: () => {
        const { activityId, alias } = currentTarget.dataset;
        wx.navigateTo({
          url: `/packages/retail/usercenter/collect-points-detail/index?activityId=${activityId}&alias=${alias}`
        });
      }
    });
  },

  async onPullDownRefresh() {
    const activities = await this.fetchCollectPointsActivities(
      this.currentPage
    );
    this.setData({
      activities
    });
  },

  // 本来为了分页而抽取出来的方法，结果接口不支持。。。
  async fetchCollectPointsActivities() {
    try {
      wx.showLoading({
        title: '加载中'
      });
      const result = await queryActivityListPaged();
      const sortResult = [
        ...result.filter(item => item.stage === 2),
        ...result.filter(item => item.stage === 1),
        ...result.filter(item => item.stage === 3)
      ];
      const activities = sortResult.map(
        ({
          id,
          alias,
          name,
          description,
          stage,
          startAt,
          endAt,
          activityType,
          costPoints,
          conditionAmount
        }) => ({
          stage,
          activityName: name,
          startTime: formatDate(startAt, 'YYYY-MM-DD'),
          endTime: formatDate(endAt, 'YYYY-MM-DD'),
          collectPointsStyle: this.getActivityStyleName(
            activityType,
            conditionAmount
          ),
          exChangeRule: `集满${costPoints}点可兑换礼品`,
          activityId: id,
          alias,
          description
        })
      );
      return activities;
    } catch (error) {
      wx.showToast({
        title: error.msg || '加载集点活动失败，请重试！',
        icon: 'none'
      });
      return [];
    } finally {
      wx.hideLoading();
      wx.stopPullDownRefresh();
    }
  }
});
