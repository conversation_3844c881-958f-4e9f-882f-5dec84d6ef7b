<view class="collect-cart-container">
    <view 
        class="collect-cart" 
        wx:for="{{activities}}" 
        wx:key="activityId" 
        wx:for-item="activity"
        data-activity-id="{{activity.activityId}}"
        data-alias="{{activity.alias}}"
        bind:tap="toDetail"
    >
        <view class="collect-cart__header {{activity.stage === 1 ? 'collect-cart__header-notstart' : (activity.stage === 2 ? 'collect-cart__header-doing' : 'collect-cart__header-end')}}" >
            <view class="collect-cart__header-left">
                <view class="collect-cart__header-left__title">{{activity.activityName}}</view>
                <view class="collect-cart__header-left__time">{{activity.startTime}} 至 {{activity.endTime}}</view>
            </view>
            <view class="collect-cart__header-right">
                <view wx:if="{{activity.stage === 1}}" class="collect-cart__header-right__btn collect-cart__header-right__notstart">
                    未开始
                </view>
                <view wx:if="{{activity.stage === 2}}" class="collect-cart__header-right__btn collect-cart__header-right__doing">
                    进行中
                </view>
                <view wx:if="{{activity.stage === 3}}" class="collect-cart__header-right__btn collect-cart__header-right__end">
                    已结束
                </view>
            </view>
        </view>
        <view class="collect-cart__body">
            <view class="collect-cart__body-style">
                <view class="collect-cart__body-style__title">
                    集点方式：
                </view>
                 <view class="collect-cart__body-style__msg">
                    {{activity.collectPointsStyle}}
                </view>
            </view>
            <view class="collect-cart__body-style">
                <view class="collect-cart__body-style__title">
                    兑换规则：
                </view>
                <view class="collect-cart__body-style__msg">
                    {{activity.exChangeRule}}
                </view>
            </view>
        </view>
    </view>
</view>