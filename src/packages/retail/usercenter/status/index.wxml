<page-container pageBgColor="#fff">
  <view wx:if="{{ loading }}" />
  <view wx:else>
    <view class="status-container">
      <view class="status-container__desc">
        <block wx:if="{{ isSuccess }}">
          <image class="logo" src="{{ logo }}"></image> 
          <view>登录成功</view>
          <view class="status-container__vipname">
            欢迎您，尊贵的 <view style="display: inline; color: {{themeMainColor}}">VIP{{vipLevel}}</view> 会员
          </view>
        </block>
        <block wx:else>
          <block wx:if="{{ isMember }}">
            <van-icon name="warning" size="60px" color="#F76260"></van-icon>
            <view>扫码登录失败</view>
          </block>
          <block wx:else>
            欢迎加入{{ shopName }}
          </block>
        </block>
      </view>
      <view wx:if="{{ !isMember }}" class="status-container__tip">
        成为店铺会员，尊享会员权益
      </view>
    </view>
    <view class="status-account">
      <official-account wx:if="{{ canIUseOfficalAccount }}"></official-account>
    </view>
    <view>

      <view class="status-action">
        <block wx:if="{{ !isMember }}">
          <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="handleAuthSuccess" bind:fail="handleAuthFail">
            <van-button custom-class="status-button" round type="primary" custom-style="background: {{themeMainColor}};">
              绑定手机成为会员
            </van-button>
          </user-authorize>
        </block>
        <van-button wx:else round plain type="primary" bind:tap="goBack" custom-class="status-button"
          custom-style="background: {{themeMainColor}};">
          完成
        </van-button>
      </view>
      <account-wx-login id="account-wx-login" />
    </view>
    <mem-registry-threshold visible="{{ showMemThresholdPopup }}" bind:close="{{handleCloseMemThresholdPopup}}"
      threshold-type="{{memThresholdType}}" threshold-value="{{memThresholdValue}}">
      <view class="close__threshold" bindtap="handleCloseMemThresholdPopup" slot="head"></view>
    </mem-registry-threshold>
    <van-dialog id="van-dialog" />
  </view>
</page-container>