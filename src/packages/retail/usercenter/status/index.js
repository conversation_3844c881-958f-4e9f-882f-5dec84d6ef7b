import { promisifiedCarmen } from 'utils/promisified-api';
import {
  notifyOfflineMemberSignFailed,
  joinMember,
} from 'retail/util/bind-mobile';
import {
  initGlobalThemeAfter,
  getThemeMainColor,
} from 'retail/util/shelf-global';

import openWebView from 'utils/open-web-view';
import args from '@youzan/utils/url/args';
import { ThresholdType } from 'components/mem-registry-threshold/constants';

const app = getApp();

const LevelGroupType = {
  Free: 1,
  Pay: 2,
};

function checkMemRegistrationState() {
  return app.request({
    path: '/wscuser/level/api/checkMemRegistrationState.json',
  });
}

Page({
  data: {
    desc: '',
    loading: true,
    canIUseOfficalAccount: false,
    isMember: false,
    isSuccess: false,
    vipLevel: '',
    logo: '',
    shopName: '',
    signQrCodeKey: '',
    memThresholdType: ThresholdType.UNKNOWN,
    memThresholdValue: 0,
    showMemThresholdPopup: false,
    shopInfo: {},
    themeMainColor: getThemeMainColor(),
    themeMainRgbColor: '',
  },

  onLoad({ q: signQrCodeKey, type, kdtId }) {
    initGlobalThemeAfter().then(() => {
      this.setData({
        themeMainColor: getThemeMainColor(),
      });
    });

    this.setData({
      signQrCodeKey,
      canIUseOfficalAccount: wx.canIUse('official-account'),
    });

    // 3.0 店铺的签到 直接展示成功 前置流程已经完成了成为会员
    if (type === 'cashier') {
      this.setData({
        loading: false,
        isSuccess: true,
      });
      return;
    }

    if (signQrCodeKey) {
      Promise.all([
        promisifiedCarmen({
          api: 'youzan.retail.scrm.member.scaninfo/1.0.0/get',
          data: {
            qr_code: signQrCodeKey,
            kdt_id: kdtId,
          },
          methods: 'POST',
        }),
        promisifiedCarmen({
          api: 'youzan.retail.scrm.customer.assets/1.0.0/get',
          methods: 'POST',
          kdt_id: kdtId,
          data: {
            need_query_member_benefits: true,
          },
        }),
      ])
        .then(([scanInfo, customerAssets]) => {
          const { term_end: termEnd } = scanInfo;
          const {
            customer_info: customerInfo,
            member_level_right: memberLevelRight,
          } = customerAssets;

          const shopInfo = getApp().getShopInfoSync() || {};
          this.setData({
            logo: shopInfo.logo,
            shopName: shopInfo.shop_name,
            isMember: !!customerInfo.is_member,
            vipLevel: memberLevelRight?.vip_level || '',
          });

          if (new Date().getTime() > termEnd) {
            return Promise.reject({
              isInvaliDateExpection: true,
            });
          }

          if (customerInfo.is_member) {
            return promisifiedCarmen({
              api: 'youzan.retail.scrm.customer.assets/1.0.0/get',
              data: {
                qr_code: signQrCodeKey,
              },
              methods: 'POST',
            });
          }

          this.setData({
            loading: false,
          });
        })
        .then((data) => {
          if (data && data.is_login_cash) {
            this.setData({
              isSuccess: true,
              loading: false,
            });
          }
          this.setData({
            loading: false,
          });
        })
        .catch((err) => {
          if (err.isInvaliDateExpection || err.code === 240000010) {
            notifyOfflineMemberSignFailed();
          }

          this.setData({
            loading: false,
          });
        });
    }
  },

  handleAuthSuccess() {
    joinMember(
      async () => {
        const tryCashLogin = () =>
          promisifiedCarmen({
            api: 'youzan.retail.scrm.customer.assets/1.0.0/get',
            methods: 'POST',
            data: {
              qr_code: this.data.signQrCodeKey,
            },
          })
            .then((data) => {
              if (data.is_login_cash) {
                this.setData({
                  isSuccess: true,
                  isMember: true,
                });
              }
            })
            .catch((err) => {
              console.log(err?.msg);
            });
        const promisees = [checkMemRegistrationState(), tryCashLogin()];

        Promise.all(promisees).then(
          ([
            {
              needFillInfo,
              conditionType: memThresholdType = ThresholdType.UNKNOWN,
              conditionValue: memThresholdValue = 0,
              hasCondition,
            },
          ]) => {
            const redirectToFill = () => {
              const url = '/wscuser/levelcenter/simplified-fill';
              setTimeout(() => {
                openWebView(
                  args.add(url, {
                    kdt_id: app.getKdtId(),
                    levelType: LevelGroupType.Free,
                    fromScene: 'complete',
                    eT: Date.now(),
                  }),
                  { title: '完善信息' }
                );
              }, 500);
            };
            if (needFillInfo) {
              return redirectToFill();
            }
            if (hasCondition) {
              this.setData({
                memThresholdType,
                memThresholdValue,
                showMemThresholdPopup: true,
              });
              return;
            }
            wx.showToast({
              mask: true,
              title: '欢迎入会\r\n已领取新人礼',
              icon: 'none',
            });
          }
        );
      },
      {
        kdt_id: app.getKdtId(),
        need_be_member: true,
      }
    );
  },

  handleAuthFail() {},

  handleCloseMemThresholdPopup() {
    this.setData({ showMemThresholdPopup: false });
  },

  goBack() {
    // wx.navigateBack();
    // 直接跳转到小程序首页
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx.reLaunch({
      url: '/pages/home/<USER>/index',
    });
  },
});
