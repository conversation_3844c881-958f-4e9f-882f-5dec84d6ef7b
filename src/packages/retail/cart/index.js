import WscPage from 'pages/common/wsc-page/index';
import {
  addGoodsToCart,
  deleteCartData,
  fetchScanGoCart,
  scan,
  updateCartData,
  fetchAppConfig
} from 'retail/util/api';
import rq from 'retail/util/rq';
import debounce from 'utils/debounce';
import { getShoppingSkuNo } from './api';

WscPage({
  data: {
    goodsList: [],
    shopName: '',
    confirmBuySku: false,
    cartId: '',
    unavailableGoodsList: [],
    checked: [],
    information: {},
    showSku: false,
    spec: {},
    models: {},
    hasShoppingBag: false,
    selectedAll: true,
    loading: undefined, // 这里使用 undefined 而不是 false 是为了实现一个中间状态
    isNegativeStock: false
  },

  closeSku() {
    this.setData({
      showSku: false
    });
  },

  showSkeleton() {
    this.isMount && this.setData({ loading: true });
  },
  needSkeleton() {
    this.isMount = true;
  },
  notNeedSkeleton() {
    this.isMount = false;
  },

  onLoad({ skuModel }) {
    fetchAppConfig().then(({ shoppingBagConfig }) => {
      this.setData({
        hasShoppingBag: shoppingBagConfig,
        shopName: getApp().getShopInfoSync().shop_name
      });
    });

    if (skuModel) {
      this.skuModel = JSON.parse(skuModel);
    }

    this.needSkeleton();
    this.fetchScanGoCart = rq(fetchScanGoCart, 400, () => {
      this.showSkeleton();
    });

    this.currentUpdateTrack = 0;
    this.flushCardData = new Map();
  },

  onShow() {
    this.fromScan || this.fetchList();
    this.fromScan = false;
    this.preloadSku();
    this.isUnmount = false;
  },

  onHide() {
    // 如果 debounce 还没生效，就进入下单页，会导致数据显示不正确，需要立刻刷新。
    // FIXME: 可能在 onHide 里用有点粗暴。或者在主动跳转的时候会更好？
    this.flushCardData.forEach(flush => flush());
    this.isUnmount = true;
  },

  preloadSku() {
    if (!this.skuModel) return;
    const { information, skuModels, goodsProps, skuNo } = this.skuModel;

    this.setData({
      skuModels,
      skuNo,
      goodsProps,
      information,
      showSku: true
    });
    this.skuModel = null;
  },

  async onAddShoppingBag() {
    wx.showLoading();
    try {
      const itemId = await getShoppingSkuNo();
      if (!itemId) {
        this.setData({
          hasShoppingBag: false
        });
        wx.showModal({
          title: '添加失败',
          content: '购物袋配置已经被修改',
          showCancel: false
        });
        return;
      }

      const data = await addGoodsToCart(itemId);

      if (data) {
        this.skuModel = data;
        this.preloadSku();
      }
    } catch (err) {
      wx.showToast({
        icon: 'none',
        title: err?.msg ?? '网络出现问题'
      });
    } finally {
      wx.hideLoading();
    }
  },

  async onAddSkuGoods({ detail }) {
    const { skuNo, amount, detailModels } = detail;
    try {
      await addGoodsToCart(skuNo, amount, { detailModels });
      this.setData({
        showSku: false
      });
      this.fetchList();
    } catch (err) {
      wx.showToast({
        icon: 'none',
        title: err.msg || '添加商品失败，请重试～'
      });
    } finally {
      this.setData({
        confirmBuySku: false
      });
    }
  },

  computeCheckedStatus(goodsList) {
    const difference = (a, b) => {
      const set = new Set(a);
      for (const ele of b) {
        set.delete(ele);
      }
      return Array.from(set);
    };

    return difference(
      this.toChecked(goodsList),
      this.toChecked(this.data.goodsList)
    ).concat(this.data.checked);
  },

  async fetchList(track = this.currentUpdateTrack) {
    wx.showNavigationBarLoading();
    try {
      const res = await this.fetchScanGoCart();
      if (track !== this.currentUpdateTrack) return;

      this.notNeedSkeleton();
      const { shop, goodsList, cartId, unavailableGoodsList } = res;

      const nextChecked = this.computeCheckedStatus(goodsList);

      this.setData({
        goodsList,
        cartId,
        unavailableGoodsList,
        isNegativeStock: shop?.isNegativeStock ?? false,
        selectedAll: this.adjustAllSelectedStatus(nextChecked, goodsList),
        checked: nextChecked,
        loading: false
      });
    } catch (err) {
      wx.showToast({
        title: err.msg || '获取商品列表失败',
        icon: 'none'
      });
      this.setData({
        loading: false
      });
    } finally {
      wx.stopPullDownRefresh();
      wx.hideNavigationBarLoading();
    }
  },

  onPullDownRefresh() {
    this.fetchList();
  },

  handleScanCode() {
    this.fromScan = true;
    scan().then(({ isSku, skuModel, isCancel } = {}) => {
      if (isSku) {
        this.skuModel = skuModel;
        this.preloadSku();
      } else if (!isCancel) {
        this.fetchList();
      }
    });
  },

  handleGoodsDelete({ detail: { index } }) {
    const { goodsList } = this.data;
    const beDeleted = goodsList[index];
    this.postDeleteGoods(beDeleted, goodsList.filter((_, i) => i !== index));
  },

  handleDeleteAll() {
    const set = new Set(this.data.checked);
    const deletedGoodsList = this.data.goodsList.filter(item =>
      set.has('' + item.cartItemId)
    );
    const leftGoodsList = this.data.goodsList.filter(
      item => !set.has('' + item.cartItemId)
    );

    if (deletedGoodsList.length) {
      this.postDeleteGoods(deletedGoodsList, leftGoodsList);
    } else {
      wx.showToast({
        title: '请至少选中一个商品',
        icon: 'none'
      });
    }
  },

  async postDeleteGoods(deletedGoodsList, leftGoodsList) {
    try {
      await deleteCartData(deletedGoodsList);
      const checked = this.toChecked(leftGoodsList);
      this.setData({
        goodsList: leftGoodsList,
        checked,
        selectedAll: this.adjustAllSelectedStatus(checked, leftGoodsList)
      });
    } catch (ex) {
      wx.showToast({
        icon: 'none',
        title: '删除失败'
      });
    }
  },

  async handleUnavailableGoodsClear() {
    try {
      await deleteCartData(this.data.unavailableGoodsList);
      this.setData({
        unavailableGoodsList: []
      });
    } catch (err) {
      wx.showToast({
        icon: 'none',
        title: err?.msg ?? '删除失败'
      });
    }
  },

  handleGoodsNumUpdate({ detail: { id: cartItemId, amount, index } }) {
    this.currentUpdateTrack += 1;
    this.hadUpdateToServer = false;
    const oldAmount = this.data.goodsList[index].amount;
    this.setData({
      [`goodsList[${index}].amount`]: amount
    });
    if (!amount) return;

    this.flushCardData.set(cartItemId, () =>
      updateCartData({ cartItemId, amount })
    );
    this.updateCartDataToServer(
      cartItemId,
      amount,
      oldAmount,
      index,
      this.currentUpdateTrack
    );
  },

  updateCartDataToServer: debounce(function(
    cartItemId,
    amount,
    oldAmount,
    index,
    track
  ) {
    this.flushCardData.delete(cartItemId);
    if (this.isUnmount) return;

    updateCartData({
      cartItemId,
      amount
    })
      .then(() => {
        this.fetchList(track);
      })
      .catch(err => {
        if (track !== this.currentUpdateTrack) return;
        this.setData({
          [`goodsList[${index}].amount`]: oldAmount
        });
        wx.showToast({
          title: err?.msg ?? '添加出现问题',
          icon: 'none'
        });
      });
  },
  300),

  handleSelectAll({ detail: { selectedAll } }) {
    this.setData({
      selectedAll,
      checked: selectedAll ? this.toChecked(this.data.goodsList) : []
    });
  },

  handleGoodsChecked({ detail: { checked } }) {
    this.setData({
      checked,
      selectedAll: this.adjustAllSelectedStatus(checked)
    });
  },

  toChecked(goodsList) {
    return goodsList.map(goods => '' + goods.cartItemId);
  },

  adjustAllSelectedStatus(checked, goodsList = this.data.goodsList) {
    // 这里使用 >= 而不是 ==，是为了处理下单后返回购物车，继续扫码加车，从而导致 checked 没清空的情况。
    // 在正常情况下，可能保证 checked 的长度一定小于 goodsList
    return checked.length >= goodsList.length;
  }
});
