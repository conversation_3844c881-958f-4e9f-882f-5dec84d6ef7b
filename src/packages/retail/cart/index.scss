page {
  margin-bottom: 50px;
}

.unavailable-goods {
  display: block;
  margin-top: 10px;
}

.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100vh;
  background-color: #fff;

  &__icon {
    margin-bottom: 15px;
    border-radius: 50%;
    height: 128px;
    width: 128px;
    background-image: url(https://su.yzcdn.cn/wsc-minapp/icon/retail/shopping.svg);
    background-size: 128px 128px;
  }

  &__label {
    margin-bottom: 16px;
    font-size: 12px;
    color: #c8c9cc;
  }

  &__btn {
    border-radius: 3px;
    height: 48px;
    width: 120px;
    line-height: 48px;
    font-size: 14px;
    background-color: #fff;
  }
}

.tip-shopping {
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  background: white;
  height: 100rpx;
  .icon {
    background: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>')
      no-repeat center;
    background-size: 16px 16px;
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  .buy {
    background: #1989fa;
    border-color: #1989fa;
    color: white;
    flex-shrink: 0;
    font-size: 24rpx;
  }
}
