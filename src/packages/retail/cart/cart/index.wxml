<import src="./components/skeleton/index.wxml" />

<view class="cart-container">
  <template is="cart-skeleton" wx:if="{{loading}}" />
  <view wx:else class="cart-goods">
    <block wx:if="{{goodsList.length}}">
      <van-checkbox
        custom-class="goods__cell" 
        label-class="goods__detail" 
        bind:change="handleAllCheckToggle"
        value="{{selectedAll}}"
      >
        <goods-cell is-edit="{{isEdit}}">
          <view slot="body" style="display: flex; align-items: center">
            <van-icon name="shop-o" color="#333" style="margin-top: 10rpx" />
            <view class="van-ellipsis" style="margin-left: 10rpx">{{shopName}}</view>
          </view>
          <view slot="footer" class="cart__edit" bind:tap="handleEditOrFinishClick">
            {{ isEdit ? '完成' : '编辑' }}
          </view>
        </goods-cell>
      </van-checkbox>
      <van-checkbox-group 
        value="{{checked}}" 
        bind:change="handleItemCheckToggle"
      >
        <van-checkbox 
          custom-class="goods__cell" 
          label-class="goods__detail" 
          name="{{item.cartItemId}}"
          wx:for="{{ goodsList }}"
          wx:key="cartItemId"
        >
          <goods-cell
            jump="{{!index && hasNewGoods}}"
            index="{{ index }}"
            is-edit="{{isEdit}}"
            swipe
          >
          <view slot="body">
            <view class="cart-goods__detail">
              <view class="van-ellipsis" style="margin-right: 16rpx;">{{ item.title }}</view>
              <tag
                wx:key="*this"
                wx:for="{{item.promotionTags}}"
                wx:for-item="tagName"
                type="danger"
                plain
              >{{tagName}}</tag>
            </view>
            <view class="cart-goods__sku" wx:if="{{ item.specifications }}">
              {{ item.specifications }}
            </view>
            <price price="{{item.salePrice}}" origin-price="{{item.originPrice}}" />
          </view>

          <view slot="footer">
            <view wx:if="{{item.fromPlu}}" class="plu-specification">
              {{item.unitToShow}}
            </view>
            <stepper
              wx:else
              max="{{ isNegativeStock ? 999 : item.stockNum / 1000 }}"
              data-max="{{ isNegativeStock ? 999 : item.stockNum / 1000 }}"
              value="{{ item.amount }}"
              data-index="{{index}}"
              data-id="{{item.cartItemId}}"
              catch:minus="handleGoodsMinus"
              catch:plus="handleGoodsPlus"
              catch:blur="validateGoodsNum"
              style="flex-shrink: 0"
            />
          </view>
        </goods-cell>
      </van-checkbox>
      </van-checkbox-group>
    </block>
  </view>
</view>

<pay-submit
  hidden="{{showSku}}"
  has-icon
  justUI
  bind:pay="onsubmit"
  price="{{totalPrice}}"
  is-empty-cart="{{!goodsList || !goodsList.length}}"
  disabled="{{!checked.length}}"
  style="{{isEdit ? '--button-color: #F40F0F; --button-border-color: #F40F0F; --button-bg-color: white' : ''}}"
>
  {{isEdit ? '删除' : '去结算'}}
  <view slot="left-icon" style="margin-right: 24rpx;">
    <van-checkbox 
      bind:change="handleAllCheckToggle"
      value="{{selectedAll}}"
      label-class="selected-all-label"
      wx:if="{{isEdit}}" 
    >
      全选
    </van-checkbox>
    <view
      wx:else
      class="operation__scan"
      bind:tap="handleScanCode"
    />
  </view>
</pay-submit>

<wxs src="retail/util/money.wxs" module="money" />
