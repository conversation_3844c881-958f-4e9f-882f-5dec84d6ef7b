:host {
  font-size: 28rpx;
  color: #333;
}

.cart__edit {
  font-size: 28rpx;
  color: #1989fa;
}

.cart-goods__detail {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  font-weight: 500;
}

.cart-goods__sku {
  font-size: 12px;
  color: #666;
}

.cart-goods__price {
  font-size: 28rpx;
  color: #f44;
  font-family: futura, sans-serif;
}

.cart-goods__origin-price {
  height: 12px;
  line-height: 12px;
  font-size: 12px;
  color: #666;
}

.cart-goods__origin-price-num {
  text-decoration: line-through;
}

.goods__cell {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  box-sizing: border-box;
  flex: 1;
  margin-left: 15px;
}

.goods__detail {
  display: flex;
  justify-content: center;
  flex-direction: column;
  flex: 1;
  width: 60%;
}

.goods__detail > view {
  margin: 10rpx 0;
}

.padding-for-iphonex {
  padding-bottom: 34px;
  background: white;
}

.selected-all-label {
  color: #969799;
  font-size: 14px;
}

.operation__scan {
  border: 5px solid #fff;
  border-radius: 50%;
  box-shadow: 0 -2px 1px #ddd;
  width: 50px;
  height: 50px;
  margin-bottom: 24rpx;
  background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/scan.png);
  background-position: center;
  background-size: 24px;
  background-repeat: no-repeat;
  background-color: #1989fa;
}

.plu-specification {
  color: #666;
  font-size: 30rpx;
  @supports (align-self: flex-end) {
    align-self: flex-end;
  }
  @supports not (align-self: flex-end) {
    margin-top: auto;
  }
}
