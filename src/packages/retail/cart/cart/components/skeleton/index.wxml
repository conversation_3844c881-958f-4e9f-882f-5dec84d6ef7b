<template name="cart-skeleton">
  <view class="cart-skeleton">
    <view class="header">
      <view class="circle mr-12" />
      <view class="rectangle" style="width: 300rpx;" />
      <view class="rectangle" style="width: 60rpx; margin-left: auto" />
    </view>
     <block wx:for="123" wx:key="*this">
      <view class="cart-row">
        <view class="circle mr-12" />
        <view class="intro">
          <view class="rectangle" style="width: 280rpx;" />
          <view class="rectangle" style="width: 240rpx; margin-top: 8rpx;" />
          <view class="rectangle" style="width: 160rpx; margin-top: 40rpx;" />
        </view>
        <view class="rectangle" style="width: 200rpx; height:60rpx; margin-left: auto" />
      </view>
    </block>
  </view>
</template>
