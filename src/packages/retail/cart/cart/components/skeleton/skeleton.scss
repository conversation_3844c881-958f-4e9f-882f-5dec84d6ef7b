@mixin gradient {
  background-image: linear-gradient(90deg, #eee 0, #e8e8e8 40px, #eee 80px);
  background-size: 300px;
}

@keyframes shine {
  0% {
    background-position: -100px;
  }
  50%, 100% {
    background-position: 140px;
  }
}

.cart-skeleton {
  border-top: 1rpx #e5e5e5 solid;
  border-bottom: 1rpx #e5e5e5 solid;
  display: flex;
  width: 750rpx;
  flex-direction: column;
  .header {
    display: flex;
    align-items: center;
    height: 110rpx;
    padding: 30rpx;
    box-sizing: border-box;
  }

  .cart-row {
    border-top: 10px solid #f2f3f5;
    height: 240rpx;
    box-sizing: border-box;
    padding: 30rpx;
    display: flex;
    align-items: center;

    .intro {
      display: flex;
      flex-direction: column;
    }
  }

  .circle {
    height: 40rpx;
    width: 40rpx;
    border-radius: 50%;
    background: #eee;
  }

  .rectangle {
    background: #eee;
    height: 40rpx;
    @include gradient();
    animation: shine 1.2s infinite linear;
  }

  .mr-12 {
    margin-right: 24rpx;
  }

  .mr-8 {
    margin-right: 16rpx;
  }
}
