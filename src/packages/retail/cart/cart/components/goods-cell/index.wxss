@import 'shared/common/css/helper/index.wxss';

:host {
  font-size: 14px;
  color: #333;
}

.cart-item-container {
  padding: 15px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

@keyframes jump-in {
  0% {
    background-color: white;
    transform: scale(1);
  }

  100% {
    background-color: rgb(245, 250, 255);
    transform: scale(1.02);
  }
}

@keyframes jump-out {
  0% {
    background-color: rgb(245, 250, 255);
    transform: scale(1.02);
  }

  100% {
    background-color: white;
    transform: scale(1);
  }
}

.jump-cell {
  animation: jump-in 0.3s cubic-bezier(0, 0, 0.1, 0.1) forwards,
    jump-out 0.3s cubic-bezier(0.5, 0, 0.5, 0.1) 0.3s forwards;
}

.cart-goods__delete {
  display: flex;
  width: 80px;
  height: 100%;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 30rpx;
  background-color: #f44;
}
