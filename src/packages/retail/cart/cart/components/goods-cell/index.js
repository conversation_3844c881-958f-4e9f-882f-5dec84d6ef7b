Component({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },

  properties: {
    id: String,
    jump: Boolean,
    swipe: <PERSON><PERSON><PERSON>,
    index: Number,
    isEdit: Boolean
  },

  methods: {
    startVibrate() {
      wx.vibrateShort();
      // NOTE：我们在 1s 后清除这个 jump。之所以不在 end 事件中使用，它会让动画变得很唐突
      setTimeout(() => {
        this.setData({
          jump: false
        });
      }, 1000);
    },

    deleteItem({ detail: { position, instance } }) {
      switch (position) {
        case 'right': {
          this.triggerEvent(
            'delete',
            {
              index: this.data.index
            },
            { composed: true, bubbles: true }
          );
        }
        case 'cell': {
          instance.close();
          break;
        }
        default:
          break;
      }
    }
  }
});
