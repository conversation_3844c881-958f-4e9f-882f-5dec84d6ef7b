const app = getApp();

Component({
  properties: {
    checked: {
      type: <PERSON><PERSON>y,
      observer: 'computeSubtotal'
    },
    loading: <PERSON><PERSON><PERSON>,
    isNegativeStock: <PERSON><PERSON><PERSON>,
    cartId: String,
    showSku: <PERSON><PERSON><PERSON>,
    selectedAll: <PERSON><PERSON><PERSON>,
    goodsList: {
      type: <PERSON><PERSON><PERSON>,
      observer(goodsList) {
        if (!goodsList.length) return;
        // 有商品列表变更时，重置动画
        const isEdit = goodsList.length ? this.data.isEdit : false;
        this.setData({
          isEdit,
          hasNewGoods: goodsList.length > this.goodsCount
        });
        this.goodsCount = goodsList.length;
        this.computeSubtotal(this.data.checked);
      }
    },
    shopName: String
  },

  data: {
    isEdit: false,
    totalPrice: 0,
    hasSelected: false
  },

  created() {
    this.handleGoodsMinus = this.updateGoods(a => a - 1);
    this.handleGoodsPlus = this.updateGoods(a => a + 1);
    this.validateGoodsNum = this.updateGoods();
  },

  methods: {
    handleAllCheckToggle({ detail: selectedAll }) {
      this.triggerEvent('selectedAll', {
        selectedAll
      });
    },

    handleItemCheckToggle({ detail: checked }) {
      this.triggerEvent('changeChecked', {
        checked
      });
    },

    computeSubtotal(checked) {
      const set = new Set(checked);
      const subtotal = this.data.goodsList.reduce(
        (subtotal, { cartItemId, amount, salePrice }) => {
          if (set.has('' + cartItemId)) {
            subtotal += amount * salePrice;
          }
          return subtotal;
        },
        0
      );
      this.setData({
        totalPrice: subtotal
      });
    },

    updateGoods(update) {
      return ({ currentTarget, detail }) => {
        if (detail?.value === '') {
          detail.value = 1;
        }
        const { dataset: { index, id } = {} } = currentTarget;
        this.triggerEvent('changeNum', {
          id,
          index,
          amount: update
            ? update(this.data.goodsList[index].amount)
            : +detail.value
        });
      };
    },

    handleEditOrFinishClick() {
      this.setData({
        isEdit: !this.data.isEdit
      });
    },

    handleScanCode() {
      this.triggerEvent('scancode');
    },

    handlePay() {
      const { checked, cartId } = this.data;
      const dbid = app.db.set({
        cartId,
        cartItems: checked
      });

      wx.navigateTo({
        url: `/packages/retailb/buy/index?dbid=${dbid}`
      });
    },

    onsubmit() {
      if (!this.data.isEdit) {
        this.handlePay();
      } else {
        this.triggerEvent('deleteall');
      }
    }
  }
});
