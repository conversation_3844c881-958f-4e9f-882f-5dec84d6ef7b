<page-container forbid-copyright copyright-bg-color="#f2f3f5">
  <view class="container" style="background: white">
    <view class="empty"
       wx:if="{{loading === false && !goodsList.length && !unavailableGoodsList.length }}">
      <view class="empty__icon" />
      <view class="empty__label">没有可以购买的商品</view>
      <button class="empty__btn" bind:tap="handleScanCode">扫码购物</button>
    </view>
    <block wx:else>
      <block wx:if="{{hasShoppingBag}}">
        <view class="tip-shopping">
          <view class="icon" />
          <view style="color: #323233; font-size: 14px;">
            若商品较多，可根据需要购买购物袋
          </view>
          <btn
            bind:click="onAddShoppingBag"
            round
            custom-class="buy"
            size="mini"
            style="margin-left: auto"
          >购买</btn>
        </view>
        <view style="background:  #f9f9f9; height: 16rpx" />
      </block>

      <cart
        loading="{{loading}}"
        shop-name="{{shopName}}"
        cart-id="{{cartId}}"
        checked="{{checked}}"
        selected-all="{{selectedAll}}"
        goods-list="{{goodsList}}"
        bind:changeChecked="handleGoodsChecked"
        bind:changeNum="handleGoodsNumUpdate"
        bind:delete="handleGoodsDelete"
        bind:selectedAll="handleSelectAll"
        bind:scancode="handleScanCode"
        bind:editclick="handleEditOrFinishClick"
        bind:deleteall="handleDeleteAll"
        is-negative-stock="{{isNegativeStock}}"
      />

      <unavailable-goods
        class="unavailable-goods"
        goods-list="{{ unavailableGoodsList }}"
        bind:clear="handleUnavailableGoodsClear"
      />
    </block>
  </view>
</page-container>

<sku
  show="{{showSku}}"
  bind:addGoods="onAddSkuGoods"
  bind:closeSku="closeSku"
  sku-models="{{skuModels}}"
  props="{{goodsProps}}"
  goods-no="{{skuNo}}"
  title="{{information.title}}"
  origin-price="{{information.price}}"
  picture="{{information.picture}}"
  loading="{{confirmBuySku}}"
  is-negative-stock="{{isNegativeStock}}"
/>
