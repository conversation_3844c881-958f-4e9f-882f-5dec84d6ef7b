<view class="level-info__background" />
<view class="level-info">
  <view class="level-info__header">
    <view class="level-info__title">{{ levelName }}</view>
    <view class="level-info__icon" />
    <view class="level-info_num">{{level}}</view>
  </view>
  <view class="level-info__amount" wx:if="{{false}}">
    <view class="level-info__amount--column">
      <text>¥100</text>
      <text>储值卡</text>
    </view>
    <view class="level-info__amount--column">
      <text>¥100</text>
      <text>礼品卡</text>
    </view>
  </view>
  <view class="level-info__body">
    <block wx:if="{{ barcode }}">
      <text class="level-info__text">向收银员出示会员码完成付款</text>
      <image bind:tap="handleTapCode" class="level-info__barcode" src="{{ barcode }}" />
      <view class="level-info__code">{{ code }}</view>
      <text class="level-info__text"></text>
      <image bind:tap="handleTapCode" class="level-info__qrcode" src="{{ qrcode }}" />
    </block>
    <loading color="#000" size="60px" wx:else />
  </view>
</view>
