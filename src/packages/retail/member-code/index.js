import WscPage from 'pages/common/wsc-page/index';
import carmen from 'retail/util/carmen';
import usePolling from 'retail/util/use-polling';

WscPage({
  data: {
    qrcode: '',
    barcode: '',
    levelName: '',
    level: '',
    identity: ''
  },

  async onLoad({ identity, level, levelName }) {
    this.identity = identity;

    const [setQRCode, stopPolling] = usePolling(this.fetchCode, 10000);
    this.setQRCode = setQRCode;
    this.stopPolling = stopPolling;

    this.setQRCode();
    this.setData({
      level,
      levelName
    });
  },

  onUnload() {
    this.stopPolling();
  },

  handleTapCode() {
    this.setData({
      barcode: '',
      qrcode: ''
    });
    this.stopPolling();
    this.setQRCode();
  },

  async fetchCode() {
    try {
      const { bar_code: barcode, qr_code: qrcode } = await carmen({
        api: 'youzan.retail.scrm.common.gencode/1.0.0/create',
        data: {
          code: this.identity
        }
      });
      this.setData({
        barcode,
        qrcode
      });
    } catch (err) {
      wx.showToast({
        title: err?.msg ?? '获取等级码失效',
        icon: 'none'
      });
    }
  }
});
