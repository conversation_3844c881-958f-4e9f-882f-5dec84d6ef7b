import WscPage from 'pages/common/wsc-page/index';
import promisify from 'utils/promisify';
import {
  addGoodsToCart,
  fetchScanGoCart,
  updateCartData,
  deleteCartData,
  querySKU
} from 'retail/util/api';
import rq from 'retail/util/rq';
import {
  getThemeMainColor,
  getThemeMainRgbColorWithAlpha
} from 'retail/util/shelf-global';

const showModal = promisify(wx.showModal);
// 称重商品
const GOODS_BY_WEIGHT = 10;
// 购物车滑动的边界值
const MAX_DELTA_MOVE = 100;

const PROMOTION_TAG_TO_NAME = {
  MEET_REDUCE_PRESENT: '赠品',
  EXCHANGE_GOODS: '换购'
};

const app = getApp();

WscPage({
  data: {
    themeMainColor: getThemeMainColor(),
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    isFullCartView: false,
    goodsList: [],
    unavailableGoodsList: [],
    // 购物车总金额：原价，折扣价，折扣金额
    payInfo: Object,
    cartId: null,
    scanPlaceHolderBorderWidth: 0,
    // 给scan-placeholder添加border，凸显中间扫码区域
    isInitBorder: false,
    isSetScanContainerTop: false,
    scanContainerTop: 0,
    canScrollY: true,
    showSku: false,
    skuInformation: {},
    confirmBuySku: false,
    activityGoodsMap: {},
    isLiteShop: false,
    uniqueId: '',
  },

  // 是否正在处理扫码结果到购物车
  _isDealingWithScan: false,
  // 是否正在处理购物车商品数量
  _isDealingWithGoodsNum: false,
  currentTop: 0,
  pageYAtStartMove: 0,
  deltaMove: 0,
  supportMultiSku: false,
  // 是否使用为微信自带扫一扫
  switchWxScan: '',
  // 是否直接跳转到扫一扫页面
  directScan: '',

  onLoad({ switchWxScan, directScan }) {
    this.setData({
      switchWxScan: !!switchWxScan,
      directScan: !!directScan
    });
    if (!!switchWxScan && !!directScan) {
      // 直接跳转到扫一扫
      this.handleToScan();
    }
    // 自助结账支持多规格的配置
    const { supportMultiSkuSwitchConfigResponse = {} } = app.globalData.retailConfig || {};
    this.supportMultiSku = !!supportMultiSkuSwitchConfigResponse.status;
    !switchWxScan && this._initScanPlaceHolderBorder();
  },

  onShow() {
    this.setData({
      themeMainColor: getThemeMainColor(),
      themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1)
    });
    this._isDealingWithScan = false;
    // 放在onShow里? 可能从前一个界面push进来，也可能从后面一个界面pop进来，保证每次进来都会请求最新购物车数据
    this.fetchCartData({ showLoading: true });
    this._checkLiteAbility();
  },

  onHide() {
    this._isDealingWithScan = true;
  },

  onBindError(err) {
    // console.log(err);
  },

  // 通过js操作元素样式：动态设置scan-placeholder的宽高
  _initScanPlaceHolderBorder() {
    const that = this;
    const queryScanContainer = wx.createSelectorQuery();
    queryScanContainer.select('#scan-go-scan-placeholder').boundingClientRect();
    queryScanContainer.exec(function(container) {
      const querySquare = wx.createSelectorQuery();
      querySquare.select('#scan-go-scan-square').boundingClientRect();
      querySquare.exec(function(square) {
        const scanPlaceHolderBorderWidth = (container[0].width - square[0].width) / 2;
        that.setData({
          scanPlaceHolderBorderWidth,
          isInitBorder: true
        });
      });
    });
  },

  _playScanDoneNote() {
    const innerAudioContext = wx.createInnerAudioContext();
    innerAudioContext.autoplay = true;
    innerAudioContext.src = 'https://b.yzcdn.cn/wsc-minapp/music/retail/scan-done.mp3';
    innerAudioContext.onError(console.error);
  },

  _playVibration() {
    wx.vibrateShort({
      type: 'heavy'
    });
  },

  _checkLiteAbility() {
    app
      .request({
        path: '/retail/h5/shop/checkLiteAbility.json'
      })
      .then(res => {
        this.setData({
          isLiteShop: res?.value
        });
      })
      .catch(() => {});
  },

  async addSku({ detail }) {
    try {
      const { amount, skuNo, detailModels, skuId, goodsId } = detail;
      const { cartId } = this.data;
      await addGoodsToCart(skuNo, amount, {
        detailModels,
        cartId,
        skuId,
        goodsId
      });
      this.closeSku();
      await this.fetchCartData();
    } catch (error) {
      this.setData({
        confirmBuySku: false
      });
      wx.showToast({
        icon: 'none',
        title: error?.msg ?? '加入购物车失败，请重试'
      });
    }
  },

  onClearCart() {
    const { goodsList = [], unavailableGoodsList = [] } = this.data;
    const totalGoodsList = goodsList.concat(unavailableGoodsList);
    if (!totalGoodsList.length) {
      return;
    }
    wx.showModal({
      content: '确认清空购物车？',
      confirmText: '清空',
      confirmColor: getThemeMainColor() || '#1989FA',
      success: async ({ confirm }) => {
        if (!confirm) return;

        try {
          await deleteCartData(totalGoodsList, this.data.cartId);
          await this.fetchCartData();
        } catch (err) {
          wx.showToast({
            icon: 'none',
            title: '清空购物车失败，请重试'
          });
        }
      }
    });
  },

  closeSku() {
    this.setData({
      showSku: false,
      confirmBuySku: false
    });
    this._isDealingWithScan = false;
  },

  // 切换到微信的扫一扫
  handleToScan() {
    wx.scanCode({
      scanType: 'barCode',
      success: res => {
        this._isDealingWithScan = false;
        this.onScanCode({ detail: res });
      }
    });
  },

  async onScanCode(e, config = {}) {
    if (this._isDealingWithScan) return;
    this._isDealingWithScan = true;
    this._playScanDoneNote();
    this._playVibration();
    const result = e.detail.result;
    const isSpecialQrCode = content => content.indexOf('goods_id') > -1;
    const getGoodsIdFromSpecialQrCode = content =>
      content.slice(content.lastIndexOf('=') + 1);
    const code = isSpecialQrCode(result)
      ? getGoodsIdFromSpecialQrCode(result)
      : result;

    const initScanStatus = () => {
      setTimeout(() => {
        this._isDealingWithScan = false;
      }, 1000);
    };

    const showErrorScanModal = (title = '', msg = '', showCancel = false) => {
      wx.showModal({
        title,
        content: msg,
        showCancel,
        confirmColor: getThemeMainColor() || '#1989FA',
        success(res) {
          res.confirm && initScanStatus();
        }
      });
    };

    try {
      const { cartId } = this.data;
      const { itemInfoBySkuNoModel = null } = await addGoodsToCart(code, 1, {
        ...config,
        cartId
      });

      // 调用成功后增加振动反馈
      wx.vibrateShort();
      if (itemInfoBySkuNoModel) {
        if (!this.supportMultiSku) {
          showErrorScanModal('添加失败', '多属性或多规格商品不能添加入购物车');
        } else {
          const itemId = itemInfoBySkuNoModel.itemSkuModels[0].itemId;
          await this.onOpenSku({ itemId });
        }
      } else {
        // todo 待优化：加购一次就回同步一次购物车，这里存在性能问题，好处：数据一直保持同步、坏处：如果网络差，用户体验会不好
        await this.fetchCartData();
        initScanStatus();
      }
    } catch (error) {
      const hasPluPriceChangeErrorCode = 233501021;
      const { code, msg, errMsg } = error;
      if (code === hasPluPriceChangeErrorCode) {
        try {
          const { code: plu, tips } = JSON.parse(msg);
          const { confirm } = await showModal({
            confirmColor: getThemeMainColor() || '#1989FA',
            content: tips,
            showCancel: true
          });
          this._isDealingWithScan = false;
          if (confirm) {
            this.onScanCode(
              { detail: { result: plu } },
              { priceAdjustmentCheck: false }
            );
          }
        } catch (err) {
          showErrorScanModal('商品已改价，msg字段解析有问题', msg);
        }
      } else if (msg) {
        showErrorScanModal('添加失败', msg);
      } else if (errMsg.indexOf('cancel') === -1) {
        showErrorScanModal('扫码失败');
      } else {
        showErrorScanModal('扫码失败', errMsg);
      }
    }
  },

  handleCartSize() {
    this.setData({
      isFullCartView: !this.data.isFullCartView
    });
  },

  async fetchCartData(options) {
    const { showLoading = false } = options || {};
    showLoading && wx.showLoading();
    try {
      const res = await rq(fetchScanGoCart, 400, () => {})();
      const {
        goodsList = [],
        cartId,
        payInfo = {},
        groupInfoList = [],
        unavailableGoodsList = [],
        uniqueId = '',
      } = res;
      this.setData({
        groupInfoList,
        payInfo: payInfo || {}, // 预防payInfo是null
        goodsList: goodsList.map(this.adaptGoods),
        unavailableGoodsList: unavailableGoodsList.map(this.adaptGoods),
        cartId,
        uniqueId,
      });
      showLoading && wx.hideLoading();
    } catch (error) {
      console.log(error);
      showLoading && wx.hideLoading();
      wx.showToast({
        icon: 'none',
        title: error?.msg ?? '网络出现问题',
      });
    }
  },

  adaptGoods(goods) {
    // 称重商品
    const { fromPlu, pricingStrategy, flagList = [] } = goods;

    if (fromPlu && pricingStrategy === GOODS_BY_WEIGHT) {
      goods.pluGoodsByWeight = true;
    }

    flagList.forEach(({ flag }) => {
      if (PROMOTION_TAG_TO_NAME[flag]) {
        // 增加是否为赠品的标识
        goods.isPresent = flag === 'MEET_REDUCE_PRESENT';
        goods.promotionTagName = PROMOTION_TAG_TO_NAME[flag];
      }
    });

    return goods;
  },

  async onChangeGoodsAmout(event) {
    if (this._isDealingWithGoodsNum) return;
    this._isDealingWithGoodsNum = true;
    const { detail } = event;
    const { goodsList, unavailableGoodsList } = this.data;
    const totalGoodsList = [...goodsList, ...unavailableGoodsList];
    let { value, oldValue } = detail;
    const {
      goods: { cartItemId, goodsId },
      type
    } = detail;
    // 若有cartItemId，则说明是购物车商品，优先使用cartItemId
    // 若无cartItemId，则是凑单商品，暂时只能用goodsId
    const goodsIndex = cartItemId
      ? totalGoodsList.findIndex(item => item.cartItemId === cartItemId)
      : totalGoodsList.findIndex(item => item.goodsId === goodsId);
    /*
    打包一口价在购物车有分包逻辑，在凑单页面显示的未满足的那个包的数量，但是其实是有 goodsId 相同的两个商品
    所以在凑单页面增加和删除时传递的 oldValue 和 value 都会有问题
    感觉是设计上的缺陷，目前只能通过这种特殊处理来规避：
    specialType 标记凑单页面，凑单页面增删时根据 goodsId 找到的第一个商品增删 1
    */
    if (detail.specialType === 'makeOrder') {
      oldValue = totalGoodsList[goodsIndex]?.amount ?? 0;
      value = type === 'minus' ? oldValue - 1 : oldValue + 1;
    }
    try {
      if (type === 'minus' && oldValue === 1) {
        // 删除商品
        const beDeletedGoods = totalGoodsList[goodsIndex];
        await deleteCartData(beDeletedGoods);
      } else if (type === 'plus' && oldValue === 0) {
        // 新增商品
        const { skuNo } = detail.goods;
        await addGoodsToCart(skuNo, 1);
      } else {
        // 变更商品数量
        const { cartItemId } = totalGoodsList[goodsIndex];
        await updateCartData({ cartItemId, amount: value });
      }
      await this.fetchCartData();
    } catch (error) {
      console.log(error);
      wx.showToast({
        icon: 'none',
        title: error?.msg ?? '编辑数量失败, 请重试'
      });
    } finally {
      this._isDealingWithGoodsNum = false;
    }
  },

  async onChangeSkuGoodsAmount(event) {
    const {
      detail: {
        goods: { itemId }
      }
    } = event;
    await this.onOpenSku({ itemId });
  },

  async onOpenSku({ itemId }) {
    const goods = await querySKU({ itemId });

    const {
      picture,
      title,
      price,
      item_sku_models: itemSkuModels = [],
      goods_no: goodsNo,
      barcode,
      prop_models: propModels = []
    } = goods;
    const firstPicture = JSON.parse(picture)[0].url;
    const skuInformation = {
      picture: firstPicture,
      title,
      price,
      skuGoodsNo: this.data.isLiteShop ? barcode : goodsNo,
      skuProps: propModels,
      skuModels: itemSkuModels
    };
    this.setData({
      skuInformation,
      showSku: true
    });
  },

  handleToPay() {
    const { cartId, goodsList, uniqueId } = this.data;
    const cartItems = goodsList.map(item => item.cartItemId).filter(Boolean);
    const dbid = app.db.set({
      cartId,
      cartItems,
      uniqueId,
    });
    wx.navigateTo({
      url: `/packages/retailb/buy/index?dbid=${dbid}`
    });
  },

  initScanContainerTop() {
    const scanContainerQuery = wx.createSelectorQuery();
    scanContainerQuery.select('#scan-container').boundingClientRect();
    scanContainerQuery.exec(container => {
      this.currentTop = container[0].top;
    });
  },

  onTouchStart(event) {
    this.initScanContainerTop();
    this.pageYAtStartMove = event.changedTouches[0]?.pageY ?? 0;
  },

  onTouchMove(event) {
    const currentPageY = event.changedTouches[0]?.pageY ?? 0;
    this.deltaMove = currentPageY - this.pageYAtStartMove;
    if (!this.data.isFullCartView && this.deltaMove >= 0) return;
    if (this.data.isFullCartView && this.deltaMove < 0) return;
    this.setData({
      canScrollY: false,
      isSetScanContainerTop: true,
      scanContainerTop: this.currentTop + this.deltaMove
    });
  },

  onTouchEnd() {
    if (this.data.isSetScanContainerTop) {
      const { isFullCartView } = this.data;
      if (!isFullCartView) {
        this.setData({
          isFullCartView: this.deltaMove < -MAX_DELTA_MOVE
        });
      } else {
        this.setData({
          isFullCartView: this.deltaMove < MAX_DELTA_MOVE
        });
      }
      this.setData({
        isSetScanContainerTop: false,
        canScrollY: true
      });
    }
  }
});
