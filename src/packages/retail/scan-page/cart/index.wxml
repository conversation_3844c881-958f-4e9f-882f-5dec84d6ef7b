<view
  wx:if="{{!goodsList.length && !unavailableGoodsList.length}}"
  class="empty"
>
  <view class="empty__icon" />
  <view class="empty__label">扫描商品条码即可加入购物车</view>
</view>
<block wx:else>
  <scroll-view
    scroll-y="{{canScrollY}}"
    class="cart-content"
    style="{{isIphoneX ? 'padding-bottom: 34px;' : ''}}"
  >
    <view
      wx:if="{{discountNode.node[0] && discountNode.node[0].children.length}}"
      class="discount-bar"
      data-discount-node="{{discountNode}}"
      catch:tap="onOpenMakeOrderPop"
    >
      <rich-text data-discount-node="{{discountNode}}" class="discount-content" nodes="{{discountNode.node}}" />
      <view wx:if="{{discountNode.text !== ''}}" data-discount-node="{{discountNode}}" class="button">{{discountNode.text}}</view>
    </view>
    <!--
      为什么要套一层呢view？
      因为要设置这个伪类，但是这个设置又不能放在自定义组件中
      ```
      .goods-item:not(:last-child) {
          border-bottom: 0.5px solid rgba(204, 204, 204, 0.5);
      }
      ```
    -->
    <block wx:if="{{unavailableGoodsList.length}}">
      <view
        class="goods-item"
        wx:for="{{unavailableGoodsList}}"
        wx:for-item="goods"
        wx:key="cartItemId"
      >
        <goods-item
          unavailable
          goods="{{goods}}"
          bind:changeGoodsAmount="onChangeGoodsAmount"
        />
      </view>
    </block>
    <view
      class="goods-item"
      wx:for="{{goodsList}}"
      wx:for-item="goods"
      wx:key="cartItemId"
    >
      <goods-item
        goods="{{goods}}"
        allowOperate="{{ !goods.isPresent }}"
        bind:changeGoodsAmount="onChangeGoodsAmount"
        style="--card-padding: 32rpx"
      />
    </view>
  </scroll-view>
</block>

<!-- 拦截touchmove，否则会穿透影响父组件cart -->
<make-order-pop
  show="{{showMakeOrderPop}}"
  group-info-list="{{groupInfoList}}"
  bind:openPop="onOpenPop"
  bind:closePop="onClosePop"
  bind:changeGoodsAmount="onChangeGoodsAmount"
  catch:touchmove="noop"
/>

<activity-order-pop
  show="{{showActivityOrderPop}}"
  cart-activity-goods-map="{{cartActivityGoodsMap}}"
  group-info="{{selectedDiscountGroup}}"
  group-info-list="{{groupInfoList}}"
  cart-id="{{cartId}}"
  bind:refreshCart="refreshCart"
  bind:closePop="onClosePop"
  bind:updateGoodsNum="onUpdateGoodsNum"
  catch:touchmove="noop"
/>

<view
  class="cart-footer"
  style="{{isIphoneX ? 'padding-bottom: 34px' : ''}}"
>
  <view
    wx:if="{{switchWxScan}}"
    class="cart-footer-scan-button"
    bind:tap="onJumpToScan"
  ></view>
  <view class="cart-footer-left">
    <text class="cart-footer-left-text">合计：</text>
    <view class="cart-footer-left-price">
    <view class="cart-footer-left-price__real">
      <price
        origin-price="{{payInfo.originAmount}}"
        price="{{payInfo.payment || totalMoney}}"
        style="--price-color: var(--theme-main-color); --origin-price-font-size: 26rpx;"
      />
      </view>
    </view>
  </view>
  <view class="cart-footer-btns">
    <btn
      round
      type="info"
      custom-class="cart-footer-pay-button"
      disabled="{{!goodsList.length}}"
      bind:click="onPay"
    >结算{{goodsList.length ? "(" + totalAmount + ")" : ""}}</btn>
  </view>
</view>
