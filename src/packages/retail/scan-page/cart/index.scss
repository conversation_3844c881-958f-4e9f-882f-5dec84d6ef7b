$footer-height: 100rpx;

.empty {
  position: absolute;
  bottom: $footer-height;
  left: 0;
  right: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #fff;

  &__icon {
    margin-bottom: 15px;
    border-radius: 50%;
    height: 128px;
    width: 128px;
    background-image: url(https://su.yzcdn.cn/wsc-minapp/icon/retail/none-cart-new.png);
    background-size: 128px 128px;
  }

  &__label {
    margin-bottom: 16px;
    font-size: 12px;
    color: #c8c9cc;
  }

  &__btn {
    border-radius: 3px;
    height: 48px;
    width: 120px;
    line-height: 48px;
    font-size: 14px;
    background-color: #fff;
  }
}

.cart-content {
  position: absolute;
  bottom: $footer-height;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  box-sizing: border-box;
  background-color: #fff;
}

.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: $footer-height;
  padding: 0 1em;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 10px rgba(125, 126, 128, 0.16);
  background: #fff;
  z-index: 90;

  &-left {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;

    &-text {
      font-size: 28rpx;
      flex-shrink: 0;
      color: #323233;
    }

    &-price {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: flex-end;
      position: relative;
      top: -4rpx;
    }

  }

  .cart-footer-btns {
    margin-left: auto;
    display: flex;
    flex-direction: row;
    justify-content: right;
    align-items: center;
  }

  .footer-btn {
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 192rpx;
    height: 80rpx;
    font-size: 28rpx;
    font-weight: 500;
    border-radius: 10em;
    line-height: 48px;
    padding: 0;
  }

  &-scan-button {
    height: 100rpx;
    width: 100rpx;
    border-radius: 50%;
    border: 16rpx solid #fff;
    background-color: var(--theme-main-color);
    background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/scan-center-icon.svg);
    background-size: 48rpx;
    background-position: center;
    background-repeat: no-repeat;
    transform: translateY(-20rpx);
    box-shadow: 0 -30px 30px -15px rgba(125,126,128,.16);
  }

  &-pay-button {
    @extend .footer-btn;
    border: none !important;
    background-color: var(--theme-main-color);
  }
}

.discount-bar {
  width: 100%;
  padding: 18rpx 32rpx;
  font-size: 24rpx;
  color: #323233;
  box-sizing: border-box;
  background-color: #fffbe8;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;

  .all {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .amount,
  .button {
    color: #ff4444;
  }

  .button {
    order: 2;
    display: inline-block;
    flex-shrink: 0;
    margin-left: 8rpx;

    &:after {
      content: '';
      display: inline-block;
      width: 15rpx;
      height: 15rpx;
      margin-left: 6rpx;
      margin-top: -4rpx;
      border-top: 3rpx solid #f40f0f;
      border-right: 3rpx solid #f40f0f;
      transform: rotate(45deg);
      vertical-align: middle;
    }
  }
}

.discount-content {
  // 开启 bfc，否则会丢失宽度
  overflow: hidden;
}

.discount-text {
  display: flex;
  justify-content: center;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.meet {
  text-align: center;
  order: 0;
  flex-grow: 1;
  @extend .text-ellipsis
}
.recommend {
  text-align: center;
  order: 1;
  flex-grow: 1;
  @extend .text-ellipsis
}
