<swipe-cell
  async-close
  left-width="{{1}}"
  right-width="{{80}}"
  disabled="{{!allowDelete || !allowOperate}}"
  bind:close="onDeleteGoods"
  clickable
>
  <view
    slot="right"
    class="cart-goods__delete"
  >
    删除
  </view>
  <block wx:if="{{unavailable}}">
    <view
      class="goods-item custom-class goods-unavailable"
      style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
      bindtap="onClick"
    >
      <image
        src="{{ goods.picUrl }}"
        class="goods-image"
        lazy-load
        mode="aspectFill"
      />
    <view class='goods-info-container'>
      <view class="goods-item-info">
        <view class="goods-item-info-name">
          <van-tag
            wx:if="{{goods.promotionTagName}}"
            custom-class="goods-item-info-name__tag"
            round
            color="{{themeMainColor}}"
            text-color="#fff"
          >{{goods.promotionTagName}}</van-tag>
          <text class="goods-item-info-name__text">{{goods.title}}</text>
        </view>
        <view class="goods-item-info-desc">
          <view class="goods-item-info-desc__prop">
            <block wx:if="{{goods.pluGoodsByWeight}}">{{(goods.amount || amount) + ' ' + goods.unit}}</block>
            <block wx:if="{{goods.specifications}}">{{goods.specifications + ' '}}</block>
            <block wx:if="{{goods.propertiesDesc}}">{{goods.propertiesDesc}}</block>
          </view>
        </view>
      </view>
      <view class="goods-item-amount goods-item-amount--fixed">
        <view class="goods-item-info-desc__price">{{goods.invalidReason || '已失效'}}</view>
        <view class="goods-item-num">X{{goods.amount}}</view>
      </view>
    </view>
    </view>
  </block>
  <block wx:else>
    <view
      class="goods-item custom-class"
      style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
      bindtap="onClick"
    >
      <image
        src="{{ goods.picUrl }}"
        class="goods-image"
        lazy-load
        mode="aspectFill"
      />
      <view class="goods-info-container">
        <view class="goods-item-info">
          <view class="goods-item-info-name">
            <text class="goods-item-info-name__text">{{goods.title}}</text>
          </view>
          <view class="goods-item-info-desc" wx:if="{{goods.pluGoodsByWeight || goods.specifications || goods.propertiesDesc}}">
            <view class="goods-item-info-desc__prop">
              <block wx:if="{{goods.pluGoodsByWeight}}">{{(goods.amount || amount) + ' ' + goods.unit}}</block>
              <block wx:if="{{goods.specifications}}">{{goods.specifications + ' '}}</block>
              <block wx:if="{{goods.propertiesDesc}}">{{goods.propertiesDesc}}</block>
            </view>
          </view>
        </view>
        <view class="goods-item-tag"  wx:if="{{goods.promotionTagName}}">
          <van-tag
            custom-class="goods-item-info-name__tag"
            round
            color="#EE0A24"
            text-color="#fff"
          >{{goods.promotionTagName}}</van-tag>
        </view>
        <view class="goods-item-amount {{!allowOperate ? 'goods-item-amount--fixed' : ''}}">
          <price
            price="{{goods.salePrice}}"
            origin-price="{{goods.originPrice}}"
            style="--price-font-size: 36rpx; --price-color: var(--theme-main-color, #f44)"
          >
            <view slot="pretext">
              <slot name="pre-price-text"></slot>
            </view>
          </price>
          <block wx:if="{{allowOperate}}">
            <view
              class="get-button"
              wx:if="{{showGetButton}}"
              bind:tap="getAllGiveaway"
            >领取</view>
            <input-number
              wx:else
              value="{{ goods.pluGoodsByWeight ? 1 : (goods.amount || amount) }}"
              externalDisableAdd="{{goods.pluGoodsByWeight}}"
              min="{{amountLimit}}"
              bind:change="onChangeGoodsAmount"
              max="{{amountMax ? amountMax : (goods.promotionTagName ? goods.amount : 100) }}"
              style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
            />
          </block>
          <view wx:else class="goods-item-num">X{{goods.amount}}</view>
        </view>
      </view>
    </view>
  </block>
</swipe-cell>
