import {
  getThemeMainColor,
  getThemeMainRgbColorWithAlpha
} from 'retail/util/shelf-global';

Component({
  externalClasses: ['custom-class'],
  properties: {
    goods: Object,
    amount: Number, // 商品数量。营销页的商品数量需要从购物车列表处理获取，购物车还是从goods获取
    amountLimit: {
      // 多规格多属性商品不允许删除，固设置最小数量为当前购物车中商品数量
      type: Number,
      value: 0
    },
    amountMax: {
      type: Number,
      value: 100
    },
    allowDelete: {
      type: Boolean,
      value: true
    },
    allowOperate: {
      // 是否允许修改/删除商品数量等操作，用于区分普通、赠品、换购等商品
      type: Boolean,
      value: true
    },
    unavailable: {
      // 是否已失效
      type: Boolean,
      value: false
    },
    showGetButton: {
      // 是否展示领取按钮，赠品页需求
      type: Boolean,
      value: false
    }
  },
  options: {
    multipleSlots: true
  },
  data: {
    themeMainColor: getThemeMainColor(),
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1)
  },
  attached() {
    this.setData({
      themeMainColor: getThemeMainColor(),
      themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1)
    });
  },
  methods: {
    onClick() {
      this.triggerEvent('click');
    },
    onChangeGoodsAmount(event) {
      const { goods } = this.properties;
      this.triggerEvent('changeGoodsAmount', {
        ...event.detail,
        goods
      });
    },
    onDeleteGoods({ detail: { position, instance } }) {
      switch (position) {
        case 'right': {
          const { goods } = this.properties;
          this.triggerEvent('changeGoodsAmount', {
            oldValue: 1,
            value: 0,
            type: 'minus',
            goods
          });
        }
        default:
          instance.close();
      }
    },
    getAllGiveaway(event) {
      const { goods } = this.properties;
      this.triggerEvent('getAllGiveaway', {
        ...event.detail,
        type: 'getAllGiveaway',
        goods
      });
    }
  }
});
