.cart-goods__delete {
  display: flex;
  width: 80px;
  height: 100%;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 30rpx;
  background-color: #f44;
}

.goods-item {
  position: relative;
  min-height: 170rpx;
  max-height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: top;
  padding: 24rpx var(--card-padding, 24rpx);
  box-sizing: border-box;
  background-color: #fff;

  .goods-image {
    width: 144rpx;
    height: 144rpx;
    border-radius: 8rpx;
    flex-shrink: 0;
  }

  .goods-info-container {
    width: 100%;
    padding-left: 16rpx;
    display: flex;
    flex-direction: column;
    .goods-item-info {

      &-name {
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 28rpx;
        margin-bottom: 8rpx;
        &__tag {
          height: 32rpx;
          font-size: 24rpx;
          line-height: 32rpx;
          border-radius: 4rpx;
          padding: 0rpx 8rpx;
        }
      }
      &-desc {
        display: flex;
        margin-bottom: 8rpx;

        &__prop {
          flex: 1;
          color: #969799;
          font-size: 24rpx;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }
    }

    .goods-item-amount {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      position: relative;
      margin-top: auto;

      .get-button {
        height: 48rpx;
        width: 104rpx;
        display: flex;
        font-size: 28rpx;
        justify-content: center;
        align-items: center;
        border-radius: 24rpx;
        color: #fff;
        background-color: var(--theme-main-color);
      }

      .goods-item-info-desc__price {
        display: inline-block;
        color: var(--theme-main-color);
        flex-shrink: 0;
        text-align: right;
        font-size: 24rpx;
        font-weight: 500;
        margin-right: 11px;
        .price-integer {
          font-size: 48rpx;
        }
      }

      .goods-item-info-desc__origin_price {
        @extend .goods-item-info-desc__price;
        font-size: 24rpx;
        text-decoration: line-through;
        color: #c8c9cc;
        .price-integer {
          font-size: 24rpx;
        }
      }

      &--fixed {
        display: flex;
        align-items: center;
        .goods-item-info-desc__price {
          color: #323233;
        }

        .goods-item-num {
          margin-left: auto;
          font-size: 24rpx;
          color: #969799;
          text-align: right;
        }
      }
    }
  }


}

.goods-unavailable {
  opacity: .35;
}

