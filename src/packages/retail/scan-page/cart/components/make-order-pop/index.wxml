<popup
  style="--indicator: {{indicator}}"
  overlay
  show="{{show}}"
  position="bottom"
  custom-class="make-order-pop"
  custom-style="height: 70%"
  bind:click-overlay="onClosePop"
  safe-area-inset-bottom="{{false}}"
  z-index="{{5}}"
>
  <view class="make-order-pop__title">
    <view class="make-order-pop__title--left"></view>
    <view class="make-order-pop__title--text">凑单</view>
    <van-icon bind:click="onClosePop" custom-class="make-order-pop__title--right" color="#323233" name="cross" size="48rpx"></van-icon>
  </view>
  <scroll-view scroll-y="true" class="make-order-pop__content">
    <template is="skeleton" wx:if="{{loading}}" />
    <view wx:elif="{{groupInfoListMir.length === 0}}" class="make-order-pop__null">暂无凑单商品</view>
    <view
      wx:if="{{makeUpGroups[discountGroup.groupId]}}"
      wx:for="{{groupInfoListMir}}"
      wx:for-index="discountIndex"
      wx:for-item="discountGroup"
      wx:key="groupId"
    >
      <view
        wx:if="{{discountInfoNodeList[discountIndex]}}"
        class="discount-bar"
      >
        <view class="promotion-text">{{discountInfoNodeList[discountIndex].promotionText}}</view>
        <rich-text class="node-text" nodes="{{discountInfoNodeList[discountIndex].node}}" />
        <view wx:if="{{discountInfoNodeList[discountIndex].text !== '' && discountInfoNodeList[discountIndex].linkType !== linkType.RECOMMEND}}" class="button" data-discount-group="{{discountGroup}}" catch:tap="onOpenActivityOrderPop">{{discountInfoNodeList[discountIndex].text}}</view>
      </view>
      <template
        data="{{goodslist: makeUpGroups[discountGroup.groupId],goodsAmountMap: goodsAmountOfDiffActivity[discountGroup.groupId]}}"
        is="goodslist"
      />
      <view style="height: 16rpx; "/>
    </view>
  </scroll-view>
</popup>

<template name="goodslist">
  <view
    class="make-order-pop__item {{goodsIndex === goodslist.length - 1 ? 'make-order-pop__item--last' : ''}}"
    wx:for="{{goodslist}}"
    wx:for-item="goods"
    wx:for-index="goodsIndex"
    wx:key="id"
  >
    <goods-item
      unavailable="{{!goods.stock}}"
      custom-class="goods-item"
      allow-delete="{{false}}"
      goods="{{goods}}"
      amount="{{goodsAmountMap[goods.goodsId]}}"
      amount-limit="{{(goods.hasMultiSku || goods.hasMultiProp) ? goodsAmountMap[goods.goodsId] : 0}}"
      bind:changeGoodsAmount="onChangeGoodsAmount"
    />
  </view>
</template>

<template name="skeleton">
  <view class="goods-skeleton" wx:for="1234" wx:key="*this">
    <view class="block square" />
    <view style="margin-left: 16rpx;">
      <view class="block rectangle"></view>
      <view class="block rectangle"></view>
      <view class="block rectangle"></view>
    </view>
  </view>
</template>

