$promotionTextColor: rgb(238, 10, 36);
$promotionTextBgColor: rgba(238, 10, 36, 0.1);
view.make-order-pop {
  display: flex;
  flex-direction: column;
  bottom: 100rpx;
  overflow-y: hidden !important;
  z-index: 9;
  background-color: #f2f3f5;
  border-radius: 48rpx 48rpx 0 0;

  &__title {
    display: flex;
    height: 44px;
    padding: 0 32rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    // border-bottom: 1rpx solid #e5e5e5;
    background-color: #fff;

    &--left,
    &--right {
      display: flex;
      width: 48rpx;
      height: 48rpx;
    }

    &--text {
      display: flex;
      flex-grow: 1;
      justify-content: center;
    }
  }

  .make-order-pop__content {
    flex: 1;
    overflow: auto;
    background-color: #F2F3F5;
    padding-bottom: var(--indicator);
  }

  &__null {
    font-size: 12px;
    color: #c8c9cc;
    text-align: center;
  }

  &__item {
    margin: 24rpx;
    background-color: #fff;
    border-radius: 16rpx;

    &--last {
      border-bottom: none;
    }
  }
}

.goods-item {
  border-radius: 16rpx;
}

.discount-bar {
  padding: 24rpx;
  font-size: 24rpx;
  color: #323233;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  align-items: top;

  .all {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .amount,
  .button {
    color: #ff4444;
  }

  .button {
    display: inline-block;
    flex-shrink: 0;
    margin-left: auto;

    &:after {
      content: '';
      display: inline-block;
      width: 15rpx;
      height: 15rpx;
      margin-left: 6rpx;
      margin-top: -4rpx;
      border-top: 3rpx solid #f40f0f;
      border-right: 3rpx solid #f40f0f;
      transform: rotate(45deg);
      vertical-align: middle;
    }
  }

  .promotion-text {
    flex-shrink: 0;
    background-color: $promotionTextBgColor;
    border-radius: 16rpx;
    padding: 0 8rpx;
    margin-right: 16rpx;
    font-size: 24rpx;
    height: 100%;
    color: $promotionTextColor;
    vertical-align: center;
  }
  .node-text {
    margin-right: 16rpx;
  }
}

.goods-skeleton {
  display: inline-flex;
  animation: blink 1s infinite alternate;
  width: 100%;
  height: 200rpx;
  padding: 24rpx;
  box-sizing: border-box;
  background: white;
  will-change: opacity;
}

.goods-skeleton .block {
  background: #eeeeee;
}
.goods-skeleton .square {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
}
.goods-skeleton .rectangle {
  height: 40rpx;
  width: 240rpx;
}
.goods-skeleton .rectangle:nth-child(2n + 2) {
  width: 180rpx;
  margin: 20rpx 0;
}
