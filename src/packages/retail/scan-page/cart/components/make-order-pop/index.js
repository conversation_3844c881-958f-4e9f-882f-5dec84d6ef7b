import iPhoneX from 'retail/util/iphonex-behavior';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import { getMultDiscountText } from 'retail/util/discount-new';
import { getUmpActivities } from 'retail/util/api';
import { LINK_TYPE } from '../../constant';

const UMP_TYPE_MAP = {
  109: 'bale', // 打包一口价
  107: 'meetReduce', // 满减送
  24: 'plusbuy', // 加价购
  115: 'secondhalfdiscount', // 第二件半价
};

/**
 * 将优惠商品字段映射为现有goodsitem组件支持的字段
 * @param {Array} list
 * @param {String} type
 */
const filterGoods = ({ list }) => {
  return list.map(
    (
      { goodsNo, currentPrice, imageUrl, goodsId, itemProperties, ...args },
      index
    ) => ({
      picUrl: imageUrl,
      salePrice: currentPrice,
      originPrice: currentPrice,
      skuNo: goodsNo, // 单规格商品 goodsNo 就是 skuNo
      goodsId,
      itemId: goodsId,
      goodsIndex: index,
      price: currentPrice,
      hasMultiProp: itemProperties && itemProperties.length > 0,
      itemProperties,
      ...args
    })
  );
};

Component({
  behaviors: [iPhoneX],
  properties: {
    show: Boolean,
    groupInfoList: Array
  },
  observers: {
    show(val) {
      this.weight = { ...this.originWeight };
      if (!val) {
        this.setData({
          makeUpGroups: {}
        });
        return;
      }
      // 打开凑单页重新排序
      const { groupInfoListMir } = this.data;
      groupInfoListMir.sort(
        (a, b) =>
          (this.weight[b.groupId] || -1) - (this.weight[a.groupId] || -1)
      );
      this.setData({
        groupInfoListMir
      });
      this.getActivities();
    },
    groupInfoList(data) {
      const len = data.length;
      // groupInfoList 的长度和 originWeight 长度不一致时，说明在凑单页的操作改变了活动信息，需要重新请求凑单商品信息
      if (this.originWeight && len !== Object.keys(this.originWeight).length) {
        this.getActivities();
      }
      // 当用户未关闭凑单页，凑单页商品相对顺序不变
      // 故设置一个权重map，按照凑单页打开前groupInfoList顺序排权重，方便后续sort
      this.originWeight = data.reduce((weightMap, { groupId }, i) => {
        weightMap[groupId] = len - i;
        return weightMap;
      }, {});
      const list = [...data];
      const { show } = this.properties;
      if (Object.keys(this.weight || {}).length === 0) {
        // 初始化
        this.weight = { ...this.originWeight };
      }
      if (show) {
        // 用户未离开凑单页
        // 按照权重由大到小排序，无权重说明是新增的活动，则依次排队尾
        list.sort(
          (a, b) =>
            (this.weight[b.groupId] || -1) - (this.weight[a.groupId] || -1)
        );
      }
      const tmp = {};
      list.forEach((activity) => {
        tmp[activity.groupId] = tmp[activity.groupId] || {};
        (activity?.joinProductDTOS ?? []).forEach(item => {
          if (tmp[activity.groupId][item.goodsId]) { // 需要把相同 goodsId 的活动商品数量相加
            tmp[activity.groupId][item.goodsId] += item.num;
          } else {
            tmp[activity.groupId][item.goodsId] = item.num;
          }
        });
      });
      this.setData({
        groupInfoListMir: list,
        discountInfoNodeList: getMultDiscountText(list),
        goodsAmountOfDiffActivity: tmp
      });
    }
  },
  data: {
    loading: false,
    makeUpGroups: {},
    discountInfoNodeList: [],
    groupInfoListMir: [], // 产品要求用户不离开凑单页，活动相对顺序保持不变，故凑单页增加一个
    linkType: LINK_TYPE,
    goodsAmountOfDiffActivity: {}
  },
  methods: {
    getActivities() {
      const { groupInfoList } = this.data;
      if (groupInfoList.length === 0) return;
      if (isEmpty(this.data.makeUpGroups)) {
        this.setData({
          loading: true
        });
      }
      const params = {};
      params.typeList = groupInfoList.reduce(
        (list, { promotionType, groupId: activityId }) => {
          const param = {
            activityId,
            activityType: UMP_TYPE_MAP[+promotionType]
          };
          list.push(param);
          return list;
        },
        []
      );
      getUmpActivities(params)
        .then((res = {}) => {
          const makeUpGroups = res.multiTypePromotionList.reduce(
            (list, activities) => {
              const activitiesInfo = activities.activitiesItemInfo;
              Object.keys(activitiesInfo).forEach(key => {
                list[key] = filterGoods({
                  list: activitiesInfo[key],
                  type: activities.promotionType
                });
              });
              return list;
            },
            {}
          );
          this.setData({
            makeUpGroups,
            loading: false
          });
        })
        .catch(err => {
          console.error(err);
          this.setData({
            loading: false
          });
          wx.showToast({
            title: err.msg ?? '获取凑单列表失败',
            icon: 'none'
          });
        });
    },
    onClosePop() {
      this.triggerEvent('closePop', { pops: ['showMakeOrderPop'] });
    },
    onChangeGoodsAmount({ detail }) {
      detail.specialType = 'makeOrder'; // 凑单页面为打包一口价做特殊处理标记
      this.triggerEvent('changeGoodsAmount', detail);
    },
    onOpenActivityOrderPop({
      target: {
        dataset: { discountGroup }
      }
    }) {
      this.triggerEvent('openPop', {
        pops: ['showActivityOrderPop'],
        discountGroup
      });
    }
  }
});
