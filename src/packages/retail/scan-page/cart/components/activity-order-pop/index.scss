$promotionTextColor: rgb(238, 10, 36);
$promotionTextBgColor: rgba(238, 10, 36, 0.1);

view.activity-order-pop {
  display: flex;
  flex-direction: column;
  bottom: 100rpx;
  overflow-y: hidden !important;
  z-index: 9;
  background-color: #f2f3f5;
  border-radius: 48rpx 48rpx 0 0;

	&__title {
    display: flex;
    height: 44px;
    padding: 0 32rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    background-color: #fff;

    &--left,
    &--right {
      display: flex;
      width: 48rpx;
      height: 48rpx;
    }

    &--text {
      display: flex;
      flex-grow: 1;
      justify-content: center;
      align-items: center;
    }
  }

  .discount-bar {
    padding: 24rpx;
    font-size: 24rpx;
    color: #323233;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    align-items: top;

    .promotion-text {
      flex-shrink: 0;
      background-color: $promotionTextBgColor;
      border-radius: 16rpx;
      padding: 0 8rpx;
      margin-right: 16rpx;
      font-size: 24rpx;
      height: 100%;
      color: $promotionTextColor;
      vertical-align: center;
    }
  }

  scroll-view {
    flex: 1;
    overflow: auto;
    background-color: #F2F3F5;
  }

  &__null {
    text-align: center;
    font-size: 24rpx;
    height: 96px;
    line-height: 96px;
    color: #999;
  }

  &__item {
    display: flex;
    align-items: center;
    background-color: #fff;
    margin: 24rpx;
    border-radius: 16rpx;

    &__component {
      flex-grow: 1;

      &--last {
        border-bottom: none;
      }
    }
  }
  .goods-item {
    border-radius: 16rpx;
  }

}
.pre-price-text {
  color: var(--theme-main-color, #f44);
  font-size: 24rpx;
  margin-right: 4rpx;
}

.goods-skeleton {
  display: inline-flex;
  animation: blink 1s infinite alternate;
  width: 100%;
  height: 200rpx;
  padding: 24rpx;
  box-sizing: border-box;
  background: white;
  will-change: opacity;
}

.goods-skeleton .block {
  background: #eeeeee;
}
.goods-skeleton .square {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
}
.goods-skeleton .rectangle {
  height: 40rpx;
  width: 240rpx;
}
.goods-skeleton .rectangle:nth-child(2n + 2) {
  width: 180rpx;
  margin: 20rpx 0;
}
