<!-- 满减赠，换购商品落地页 -->
<popup
  style="--indicator: {{indicator}}"
  overlay
  show="{{show}}"
  position="bottom"
  custom-class="activity-order-pop"
  custom-style="height: 79%"
  safe-area-inset-bottom="{{false}}"
  bind:click-overlay="onClosePops"
  z-index="{{6}}"
>
  <view class="activity-order-pop__title">
    <van-icon bind:click="onClosePop" custom-class="activity-order-pop__title--left" color="#323233" name="arrow-left" size="48rpx"></van-icon>
    <view class="activity-order-pop__title--text">
      <text>{{groupInfo.promotionType === 24 ? '换购商品' : '领取赠品'}}</text>
    </view>
    <van-icon bind:click="onClosePops" custom-class="activity-order-pop__title--right" color="#323233" name="cross" size="48rpx"></van-icon>
  </view>
  <view
    wx:if="{{groupInfo.promotionType === 24 && discountInfo}}"
    class="discount-bar"
  >
    <view class="promotion-text">{{discountInfo.promotionText}}</view>
    <rich-text nodes="{{discountInfo.node}}" />
  </view>
  <scroll-view scroll-y="true" class="activity-order-pop__main">
    <template is="skeleton" wx:if="{{loading}}" />
    <view wx:elif="{{discountGroups.length === 0}}" class="activity-order-pop__null">暂无凑单商品</view>
    <view>
      <template
        data="{{goodslist: discountGroups, themeMainColor, groupInfo}}"
        is="goodslist"
      />
      <view style="height: 16rpx; "/>
    </view>
  </scroll-view>
</popup>

<template name="goodslist">
  <view
    class="activity-order-pop__item"
    wx:for="{{goodslist}}"
    wx:for-item="goods"
    wx:for-index="goodsIndex"
    wx:key="cartItemId"
    data-goods-idx="{{goodsIndex}}"
    data-goods="{{goods}}"
  >
    <view class="activity-order-pop__item__component {{goodsIndex === goodslist.length - 1 ? 'activity-order-pop__item__component--last' : ''}}">
      <goods-item
        style="flex-grow: 1;"
        custom-class="goods-item"
        unavailable="{{ goods.stock === 0 }}"
        goods="{{ goods }}"
        amount-max="{{ goods.maxAmount }}"
        show-get-button="{{ groupInfo.promotionType === 107 && !goods.amount }}"
        data-goods="{{ goods }}"
        data-goods-idx="{{ goodsIndex }}"
        bind:changeGoodsAmount="onChangeGoodsAmount"
        bind:getAllGiveaway="onChangeGoodsAmount"
      >
        <view wx:if="{{groupInfo.promotionType === 24}}" slot="pre-price-text">
          <view class="pre-price-text">
            换购价
          </view>
        </view>
      </goods-item>
    </view>
  </view>
</template>

<template name="skeleton">
  <view class="goods-skeleton" wx:for="1234" wx:key="*this">
    <view class="block square" />
    <view style="margin-left: 16rpx;">
      <view class="block rectangle"></view>
      <view class="block rectangle"></view>
      <view class="block rectangle"></view>
    </view>
  </view>
</template>
