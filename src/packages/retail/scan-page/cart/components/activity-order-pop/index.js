import iPhoneX from 'retail/util/iphonex-behavior';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import mapKeyCase from '@youzan/weapp-utils/lib/map-keys-case';
import { getThemeMainColor, getThemeMainRgbColorWithAlpha } from 'retail/util/shelf-global';
import { getUmpActivity, updateBatchGoodsCount } from 'retail/util/api';
import { getDiscountText } from 'retail/util/discount-new';

const TYPE_TO_TAG = {
  107: '赠品'
};

const UMP_TYPE_MAP = {
  24: 'plusbuy',
  107: 'meetreduce'
};

const PREFERENTIAL_TYPE = {
  24: 'exchange',
  107: 'present'
};

/**
 * 将优惠商品字段映射为现有goodsitem组件支持的字段
 * @param {Array} list
 * @param {String} type
 * @param {Object} groupInfo
 */
const filterGoods = ({ list, type, groupInfo }) => {
  return list.map(
    (
      { goodsNo, currentPrice, costPrice, imageUrl, goodsId, amount, ...args },
      index
    ) => ({
      ...args,
      picUrl: imageUrl,
      salePrice: currentPrice,
      originPrice: costPrice,
      skuNo: goodsNo, // 单规格商品 goodsNo 就是 skuNo
      goodsId,
      promotionTagName: TYPE_TO_TAG[type],
      itemId: goodsId,
      goodsIndex: index,
      price: currentPrice,
      maxAmount:
        (+type === 107 &&
          groupInfo?.canGivingGiftList?.filter(l => l.type === 1)?.[index] // type 1 为赠品，这里只需要获取赠品
            ?.num) ||
        amount ||
        1,
      amount: 0
    })
  );
};

Component({
  behaviors: [iPhoneX],
  properties: {
    show: Boolean,
    groupInfo: Object,
    groupInfoList: Object,
    cartActivityGoodsMap: {
      type: Object,
      value: {}
    },
    cartId: {
      type: String,
      value: ''
    }
  },
  observers: {
    show(val) {
      if (!val) {
        // 强制每次进入页面重新渲染
        // （应该）可以避免一些奇奇怪怪的场景导致scroll-view不能滚动，并每次重置滚动条
        this.setData({
          discountGroups: [],
          discountInfo: {}
        });
        return;
      }
      this.getActivity();
    },
    groupInfo(val) {
      const discountInfo = getDiscountText(val);
      this.setData({ discountInfo });
    },
    // 优惠信息有变时重新计算提示信息
    groupInfoList(val) {
      if (this.data.groupInfo?.groupId) {
        const currentDiscount = val.find(item => item.groupId === this.data.groupInfo?.groupId);
        const discountInfo = getDiscountText(currentDiscount);
        this.setData({ discountInfo });
        this.getActiveGoodsNumber();
      }
    }
  },
  data: {
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    themeMainColor: getThemeMainColor(),
    loading: false,
    noMakeUp: false,
    discountGroups: [],
    discountInfo: {},
    inOperation: false // 由于接口延迟较大，可能出现多次点击添加赠品的情况，以此标识是否正在操作中
  },
  attached() {
    this.setData({
      themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
      themeMainColor: getThemeMainColor()
    });
  },
  methods: {
    noop() {},
    getActivity() {
      const { groupInfo } = this.properties;
      if (isEmpty(this.data.discountGroups)) {
        this.setData({
          loading: true
        });
      }
      const {
        groupId: activityId,
        promotionType,
        availableLevel = 1,
        isCircle = false // 是否为循环满减
      } = groupInfo;
      const data = {
        activityId,
        activityType: UMP_TYPE_MAP[+promotionType],
        preferentialType: PREFERENTIAL_TYPE[+promotionType]
      };
      if (UMP_TYPE_MAP[+promotionType] === 'meetreduce') {
        data.level = availableLevel;
        if (isCircle) data.level = 1; // 循环满减送活动的leve写死传1
      }
      getUmpActivity(data)
        .then(mapKeyCase.toCamelCase)
        .then((list = []) => {
          const data = {};
          data.discountGroups = filterGoods({
            list,
            type: promotionType,
            groupInfo
          });
          data.loading = false;
          this.setData(data);
          this.setOperationStatus(false);
          this.getActiveGoodsNumber();
        })
        .catch(err => {
          console.error(err);
          this.setData({
            loading: false
          });
          this.setOperationStatus(false);
          wx.showToast({
            title: err.msg ?? '获取活动列表失败',
            icon: 'none'
          });
        });
    },
    onClosePop() {
      this.triggerEvent('closePop', { pops: ['showActivityOrderPop'] });
    },
    onClosePops() {
      this.triggerEvent('closePop', {
        pops: ['showActivityOrderPop', 'showMakeOrderPop']
      });
    },
    onChangeGoodsAmount({ detail, target: { dataset } }) {
      // 正在发起增减赠品请求结果未返回时，直接 return
      if (this.data.inOperation) return;
      const { discountGroups } = this.data;
      const {
        groupInfo: { promotionType, supportMultiExchange }
      } = this.properties;
      // 如果换购商品只能选一件时，增加时需要进行检测是否已有选中物品
      if (
        detail.type === 'plus' &&
        UMP_TYPE_MAP[+promotionType] === 'plusbuy' &&
        !supportMultiExchange
      ) {
        const hasSelected = discountGroups.filter(item => item.amount === 1);
        if (hasSelected.length > 0) {
          wx.showToast({
            title: '换购超过数量限制',
            icon: 'none'
          });
          return;
        }
      }
      const goods = dataset.goods;
      if (detail.type === 'plus') {
        goods.amount = 1;
      } else if (detail.type === 'minus') {
        goods.amount = -1;
      } else if (detail.type === 'getAllGiveaway') {
        goods.amount = goods.maxAmount;
      }
      this.onUpdateGoodsNum([goods]);
    },
    getActiveGoodsNumber() {
      const { groupInfo, cartActivityGoodsMap } = this.properties;
      const { discountGroups } = this.data;
      const { groupId: activityId } = groupInfo;
      const activityGoods = cartActivityGoodsMap[activityId] || [];
      if (activityGoods.length > 0) {
        // 检测是否购物车中已存在
        discountGroups.forEach(({ goodsId, skuId }, i) => {
          const currentGoods = activityGoods.find(
            ({ goodsId: cartGoodsId, skuId: cartSkuId }) =>
              goodsId === cartGoodsId && skuId === cartSkuId
          );
          if (currentGoods) {
            // 如果已经存在，换购时需要设置为 1，赠品时需要设置为购物车中的赠品数量
            discountGroups[i].amount = currentGoods.amount || 1;
          } else {
            // 不存在则可能是换购情况下删除了，设置 amount 为 0
            discountGroups[i].amount = 0;
          }
        });
      } else if (discountGroups.length) {
        // 如果没有 activityGoods 可能是赠品情况下删除最后一个赠品
        discountGroups.forEach(item => {
          item.amount = 0;
        });
      }
      this.setData({
        discountGroups: [...discountGroups]
      });
      this.setOperationStatus(false);
    },
    async onUpdateGoodsNum(goodsList) {
      try {
        if (goodsList.length > 0) {
          const { cartId } = this.properties;
          const goods = goodsList.map(
            ({
              goodsId,
              amount,
              skuId,
              skuNo: goodsSkuNo,
              itemProperties: { id, name, propTexts = [] } = {}
            }) => {
              return {
                num: amount,
                goodsId,
                skuId,
                goodsSkuNo,
                itemProperties: propTexts.map(item => {
                  return {
                    id,
                    name,
                    valId: item.id,
                    valValue: item.name,
                    price: item.price
                  };
                })
              };
            }
          );
          this.setOperationStatus(true);
          await updateBatchGoodsCount({
            goods,
            cartId
          });
          this.triggerEvent('refreshCart');
        }
      } catch (error) {
        wx.showToast({
          icon: 'none',
          title: error?.msg ?? '加入购物车失败，请重试'
        });
        this.setOperationStatus(false);
      }
    },
    setOperationStatus(val) {
      this.setData({
        inOperation: val
      });
    }
  }
});
