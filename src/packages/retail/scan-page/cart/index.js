import { isNewIphone } from 'shared/utils/browser/device-type';
import { getDiscountText } from 'retail/util/discount-new';
import { updateBatchGoodsCount } from 'retail/util/api';
import { LINK_TYPE } from './constant';


Component({
  properties: {
    payInfo: Object,
    goodsList: {
      type: Array,
      value: []
    },
    groupInfoList: {
      type: Array,
      value: []
    },
    canScrollY: {
      type: Boolean,
      value: true
    },
    switchWxScan: {
      type: Boolean,
      value: false
    },
    cartId: String,
    unavailableGoodsList: {
      type: Array,
      value: []
    }
  },

  data: {
    totalMoney: 0,
    isNoneCart: true,
    totalAmount: 0,
    isIphoneX: isNewIphone(),
    showMakeOrderPop: false, // 是否弹出凑单页
    showActivityOrderPop: false,
    goodsOfDiffActivity: {}, // 不同活动下的goodsId、skuId、num， key 为 promotionType
    selectedDiscountGroup: {},
    cartActivityGoodsMap: {},
    discountNode: {
      text: '',
      node: []
    },
  },

  observers: {
    goodsList(goodsList) {
      /**
       * 多次遍历合并到一次执行
       * goodsIdToAmountMap以goodsId为key缓存购物车中的商品，提供给营销页使用
       */
      const {
        totalMoney,
        totalAmount,
        cartActivityGoodsMap
      } = goodsList.reduce(
        (
          ret,
          {
            salePrice,
            amount,
            pluGoodsByWeight,
            goodsId,
            flagList = [],
            ...args
          }
        ) => {
          ret.totalMoney += Math.round(salePrice * amount);
          if (pluGoodsByWeight) {
            ret.totalAmount += 1;
          } else {
            ret.totalAmount += amount;
          }
          flagList.forEach(({ flag, id }) => {
            if (flag === 'EXCHANGE_GOODS' || flag === 'MEET_REDUCE_PRESENT') {
              (
                ret.cartActivityGoodsMap[id] ||
                (ret.cartActivityGoodsMap[id] = [])
              ).push({
                goodsId,
                amount,
                ...args
              });
            }
          });
          return ret;
        },
        {
          totalMoney: 0,
          totalAmount: 0,
          cartActivityGoodsMap: {}
        }
      );
      this.setData({
        totalMoney,
        totalAmount,
        cartActivityGoodsMap
      });
    },
    groupInfoList(groupInfoList) {
      this.setData({
        discountNode: getDiscountText(groupInfoList[0])
      });
    }
  },

  methods: {
    noop() {},
    onChangeGoodsAmount({ detail }) {
      const {
        goods: { hasMultiSku, itemProperties = [] }
      } = detail;
      if (hasMultiSku || itemProperties.length > 0) {
        this.triggerEvent('changeSkuGoodsAmount', detail);
      } else {
        this.triggerEvent('changeGoodsAmount', detail);
      }
    },
    onPay() {
      if (!this.data.goodsList) return;
      this.triggerEvent('toPay');
    },
    onJumpToScan() {
      this.triggerEvent('toScan');
    },
    onOpenMakeOrderPop({
      target: {
        dataset: { discountNode }
      }
    }) {
      if (!discountNode?.text) return;
      const linkType = discountNode.linkType;
      const selectedDiscountGroup = this.properties.groupInfoList[0];
      if (linkType === LINK_TYPE.RECOMMEND) {
        this.setData({
          showMakeOrderPop: true
        });
      } else {
        this.setData({
          selectedDiscountGroup,
          showActivityOrderPop: true
        });
      }
    },
    onOpenPop({ detail: { pops, discountGroup } }) {
      this.setData(
        pops.reduce(
          (map, key) => {
            map[key] = true;
            return map;
          },
          {
            selectedDiscountGroup: discountGroup
          }
        )
      );
    },
    onClosePop({ detail: { pops } }) {
      this.setData(
        pops.reduce((map, key) => {
          map[key] = false;
          return map;
        }, {})
      );
    },
    async onUpdateGoodsNum({ detail: { goodsList, pops } }) {
      try {
        if (goodsList.length > 0) {
          const { cartId } = this.properties;
          const goods = goodsList.map(
            ({
              goodsId,
              amount,
              skuId,
              skuNo: goodsSkuNo,
              itemProperties: { id, name, propTexts = [] } = {}
            }) => {
              return {
                num: amount,
                goodsId,
                skuId,
                goodsSkuNo,
                itemProperties: propTexts.map(item => {
                  return {
                    id,
                    name,
                    valId: item.id,
                    valValue: item.name,
                    price: item.price
                  };
                })
              };
            }
          );
          await updateBatchGoodsCount({
            goods,
            cartId
          });
          this.triggerEvent('refreshCart');
        }
      } catch (error) {
        wx.showToast({
          icon: 'none',
          title: error?.msg ?? '加入购物车失败，请重试'
        });
      }
    },
    refreshCart() {
      this.triggerEvent('refreshCart');
    }
  }
});
