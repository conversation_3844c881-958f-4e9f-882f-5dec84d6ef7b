$scan-camera-height: 472rpx; // 需要加上偏移的 20rpx，偏移是为了圆角后面显示相机背景
$page-arrow-height: 88rpx;
$full-cart-top: 100rpx;
$scan-square-width: 590rpx;
$scan-square-height: 320rpx;

.scan-page {
  position: relative;
  height: 100vh;
  .scan-header {
    position: relative;
    height: $scan-camera-height;

    .scan-camera {
      width: 100%;
      height: 100%;
    }

    .scan-placeholder {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      border-color: transparent;
      border-style: solid;
      box-sizing: border-box;
      transition: border .2s ease;
    }

    .scan-border {
      border-top-width: 48rpx;
      border-bottom-width: 104rpx;
    }

    .scan-square {
      position: absolute;
      left: 50%;
      top: 50%;
      width: $scan-square-width;
      height: $scan-square-height;
      transform: translate3d(-$scan-square-width / 2, -$scan-square-height / 2, 0);
      background-color: rgba(0, 0, 0, 0);
      background-size: $scan-square-width $scan-square-height;
      background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/scan-square-new2.png);
    }

    .scan-notice {
      position: absolute;
      bottom: 56rpx;
      text-align: center;
      width: 100%;
      font-size: 24rpx;
      color: #fff;
    }
  }

  .scan-container {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    border-top-left-radius: 48rpx;
    border-top-right-radius: 48rpx;

    .header-container {
      display: flex;
      width: 100%;
      height: $page-arrow-height;
      justify-content: center;
      align-items: center;

      .cart-drag {
        width: 40rpx;
        height: 12rpx;
        position: relative;
        top: -20rpx;
        background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/cart-drag.png);
        background-size: 40rpx 12rpx;
        background-repeat: no-repeat;
      }
    }
    .scan-content {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: $page-arrow-height;
    }
  }

  .cart__clear-wrapper {
    position: absolute;
    display: flex;
    align-items: center;
    right: 30rpx;
    .cart__clear-text {
      font-size: 28rpx;
      color: #636566;
      margin-left: 5px;
    }
  }

  .cart-title {
    position: absolute;
    font-weight: 500;
    left: 30rpx;
    font-size: 32rpx;
  }

  .clear-cart {
    width: 32rpx;
    height: 32rpx;
  }

  .full-cart {
    top: $full-cart-top;
  }

  .normal-cart {
    top: $scan-camera-height - 40rpx;
  }
}
