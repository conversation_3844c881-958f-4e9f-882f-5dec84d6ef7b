<page-container>
  <view class="scan-page">
    <block wx:if="{{switchWxScan === true}}">
      <template
        is="cart"
        data="{{themeMainColor, themeMainRgbColor, cartId, groupInfoList, goodsList, unavailableGoodsList, payInfo, canScrollY, switchWxScan, onChangeGoodsAmout, onChangeSkuGoodsAmount, handleToPay, handleToScan, onSetFullCartView, fetchCartData}}"
      />
    </block>

    <block wx:if="{{switchWxScan === false}}">
      <view class="scan-header">
        <camera
          mode="scanCode"
          resolution="high"
          frame-size="large"
          device-position="back"
          flash="off"
          binderror="onBindError"
          bindscancode="onScanCode"
          class="scan-camera"
        />
        <view
          class="scan-placeholder {{isInitBorder ? 'scan-border' : ''}}"
          id="scan-go-scan-placeholder"
          style="border-left-width: {{scanPlaceHolderBorderWidth}}px; border-right-width: {{scanPlaceHolderBorderWidth}}px;"
        >
          <view
            class="scan-square"
            id="scan-go-scan-square"
          ></view>
        </view>
        <text class="scan-notice">将商品条码放入框内，即可自动扫描</text>
      </view>
      <view
        class="scan-container {{isFullCartView ? 'full-cart':'normal-cart'}}"
        id="scan-container"
        style="{{isSetScanContainerTop ? 'top:' + scanContainerTop + 'px' : 'transition: top 0.2s ease-in'}}"
        bind:touchstart="onTouchStart"
        catch:touchmove="onTouchMove"
        bind:touchend="onTouchEnd"
      >
        <view
          class="header-container"
          bind:tap="handleCartSize"
        >
          <view class="cart-title">购物车</view>
          <view class="cart-drag"></view>
          <view
            wx:if="{{goodsList.length || unavailableGoodsList.length}}"
            class="cart__clear-wrapper"
            catch:tap="onClearCart"
          >
            <image class="clear-cart" src="https://su.yzcdn.cn/wsc-minapp/icon/retail/clear-cart.png"></image>
            <view class="cart__clear-text">清空</view>
          </view>
        </view>
        <template
          is="cart"
          data="{{themeMainColor, themeMainRgbColor, cartId, groupInfoList, goodsList, unavailableGoodsList, payInfo, canScrollY, switchWxScan, onChangeGoodsAmout, onChangeSkuGoodsAmount, handleToPay, handleToScan, onSetFullCartView, fetchCartData}}"
        />
      </view>
    </block>
  </view>
</page-container>

<sku
  class="sku"
  bind:addGoods="addSku"
  bind:closeSku="closeSku"
  show="{{showSku}}"
  sku-models="{{skuInformation.skuModels}}"
  props="{{skuInformation.skuProps}}"
  goods-no="{{skuInformation.skuGoodsNo}}"
  origin-price="{{skuInformation.price}}"
  loading="{{confirmBuySku}}"
  title="{{skuInformation.title}}"
  picture="{{skuInformation.picture}}"
  is-lite="{{ isLiteShop }}"
/>

<template name="cart">
  <cart
    style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
    class="scan-content"
    cart-id="{{cartId}}"
    group-info-list="{{groupInfoList}}"
    goods-list="{{goodsList}}"
    unavailable-goods-list="{{unavailableGoodsList}}"
    pay-info="{{payInfo}}"
    can-scroll-y="{{canScrollY}}"
    switch-wx-scan="{{switchWxScan}}"
    bind:changeGoodsAmount="onChangeGoodsAmout"
    bind:changeSkuGoodsAmount="onChangeSkuGoodsAmount"
    bind:toPay="handleToPay"
    bind:setFullCartView="onSetFullCartView"
    bind:toScan="handleToScan"
    bind:refreshCart="fetchCartData"
  />
</template>
