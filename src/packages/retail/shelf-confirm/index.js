const app = getApp();

Page({
  data: {
    url: ''
  },
  onLoad({ dbid }) {
    const data = getApp().db.get(dbid);
    this.setData({
      url: data.url
    });
  },
  onShow() {
    app.logger &&
      app.logger.log({
        et: 'display', // 事件类型
        ei: 'enterpage', // 事件标识
        en: '浏览页面', // 事件名称
        pt: 'retailshelforderconfirm', // 页面类型
        params: {}
      });
  },
  onHide() {
    app.logger && app.logger.pageHide();
  },
  handleMessage() {}
});
