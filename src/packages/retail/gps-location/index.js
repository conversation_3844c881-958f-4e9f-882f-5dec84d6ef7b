import wscPage from 'pages/common/wsc-page/index';
import areaUtil from 'utils/area';
import { reverseGeocoder, geocoder, gcjToBaidu } from '@/helpers/lbs';

const app = getApp();
let areaMap = null;
const SELECT_CITY = '选择城市';
const SELECT_RETRY = '重新选择';
const POSITION_INACCURATE = '定位不准确';

wscPage({
  data: {
    region: [],
    regionRange: [],
    adcode: null,
    isFromGps: true,
    selectBtnText: SELECT_CITY,
    actionBtnDisabled: false,
    hotCityList: [
      { text: '上海', code: '310100' },
      { text: '昆明', code: '530100' },
      { text: '廊坊', code: '131000' },
      { text: '济南', code: '370100' },
      { text: '鹰潭', code: '360600' },
      { text: '苏州', code: '320500' },
      { text: '西安', code: '610100' },
      { text: '张掖', code: '620700' }
    ]
  },
  onLoad() {
    this.getLocation();
    app.fetchAreaMapData(data => {
      areaMap = data;
      this.setLocationData();
    });
  },
  setLocationData(data = {}) {
    const adcode = data.adcode || this.data.adcode;
    if (areaMap && adcode) {
      const region = [];
      const area = areaUtil.formatAreaData(adcode, areaMap);
      const regionRange = [area.province, area.city];
      const provincePrefix = adcode.slice(0, 2);
      const cityPrefix = adcode.slice(0, 4);
      area.province.some((item, index) => {
        if (item.code && item.code.indexOf(provincePrefix) === 0) {
          region[0] = index;
          return true;
        }
        return false;
      });
      area.city.some((item, index) => {
        if (item.code && item.code.indexOf(cityPrefix) === 0) {
          region[1] = index;
          return true;
        }
        return false;
      });
      this.setYZData({
        ...data,
        regionRange,
        region
      });
      this.setCurrentCity(regionRange, region);
    } else if (areaMap && !adcode) {
      const area = areaUtil.formatAreaData(0, areaMap);
      const regionRange = [area.province, area.city];
      this.setYZData({
        ...data,
        regionRange
      });
    }
  },
  setCurrentCity(regionRange, region) {
    const hotCityList = this.data.hotCityList;
    const currentCity = regionRange[1] && regionRange[1][region[1]]
      ? regionRange[1][region[1]].text
      : '';
    hotCityList.forEach(city => {
      city.isSelected = currentCity === city.text || currentCity.indexOf(city.text) !== -1;
    });
    this.setYZData({
      currentCity,
      hotCityList
    });
  },
  getLocation() {
    wx.authorize({
      scope: 'scope.userLocation',
      success: () => {
        reverseGeocoder({
          coord_type: 3 // baidu
        }).then(res => {
          if (res && res.result && res.result.ad_info) {
            const adcode = res.result.ad_info.adcode;
            this.currentLocation = res.result.ad_info.location;
            this.setLocationData({
              adcode,
              isFromGps: true,
              selectBtnText: POSITION_INACCURATE
            });
          }
        });
      },
      fail: () => {}
    });
  },
  bindRegionChange({ detail }) {
    const valid = this.regionCheck(detail.value);
    if (valid) {
      const region = detail.value;
      this.setYZData({
        region,
        selectBtnText: SELECT_RETRY,
        isFromGps: false
      });
      this.setCurrentCity(this.data.regionRange, region);
    }
  },
  regionCheck(region = []) {
    if (!region.length) {
      wx.showToast({
        title: '请选择城市',
        icon: 'none'
      });
      return false;
    }
    return region.every((value, index) => {
      if (!value) {
        wx.showToast({
          title: `请选择${['省份', '城市'][index]}`,
          icon: 'none'
        });
        return false;
      }
      return true;
    });
  },
  bindMultiPickerColumnChange({ detail }) {
    if (detail.column === 0) {
      const regionRange = this.data.regionRange;
      const area = areaUtil.formatAreaData(
        regionRange[0][detail.value].code,
        areaMap
      );
      this.setYZData({
        regionRange: [area.province, area.city],
        region: [detail.value, 0]
      });
    }
  },
  hotCityClickHandler({ target }) {
    const adcode = target.dataset.city.code;
    if (adcode) {
      this.setLocationData({
        adcode,
        isFromGps: false,
        selectBtnText: SELECT_RETRY
      });
    }
  },
  actionClickHandler() {
    const valid = this.regionCheck(this.data.region);
    if (!valid) {
      return;
    }
    const isFromGps = this.data.isFromGps;
    const baiduLocation = {
      choice: isFromGps ? 'auto' : 'manual',
      lng: null,
      lat: null
    };
    if (isFromGps) {
      Object.assign(baiduLocation, this.currentLocation);
      this.goBack(baiduLocation);
    } else {
      this.setYZData({
        actionBtnDisabled: true
      });
      geocoder({
        address: this.data.currentCity
      })
        .then(res => {
          const { lng, lat } = res.result.location;
          this.currentLocation = gcjToBaidu(lng, lat);
          Object.assign(baiduLocation, this.currentLocation);
        })
        .catch(e => {
          console.log(e);
        })
        .then(() => {
          this.setYZData({
            actionBtnDisabled: false
          });
          this.goBack(baiduLocation);
        });
    }
  },
  goBack(baiduLocation) {
    if (baiduLocation.lng) {
      app.storage.set('gps_location', baiduLocation, {
        expire: 0.04 // 1h
      });
    }
    wx.navigateBack();
  }
});
