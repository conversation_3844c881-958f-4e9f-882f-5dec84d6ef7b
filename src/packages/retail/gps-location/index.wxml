<view class="gps-location">
  <view class="gps-location__content">
    <view class="gps-location__top-wrapper">
      <view class="gps-location__current-text" bind:tap="getLocation">定位当前城市</view>
      <view class="gps-location__current-city" wx:if="currentCity">{{ currentCity }}</view>
      <picker mode="multiSelector" bindchange="bindRegionChange" bindcolumnchange="bindMultiPickerColumnChange"
        value="{{region}}" range="{{regionRange}}" range-key="text">
        <view class="gps-location__status">
          <image
            src="https://img.yzcdn.cn/public_files/2018/04/04/9bb40c28a1d671bea0cd581e7892cf4d.png"
            style="margin-right: 5px; width: 11px; height: 11px;" />
          {{ selectBtnText }}
        </view>
      </picker>
    </view>

    <view
      class="gps-location__hot-city-title">
      热门城市
    </view>
    <view
      class="gps-location__hot-city-list"
      bind:tap="hotCityClickHandler">
      <view
        class="gps-location__hot-city {{city.isSelected ? 'gps-location__hot-city--selected' : ''}}"
        wx:for="{{hotCityList}}"
        wx:key="code"
        data-city="{{city}}"
        wx:for-item="city">
          {{ city.text }}
      </view>
    </view>
    <button class="gps-location__action {{actionBtnDisabled ? 'gps-location__action--disabled' : ''}}" disabled="{{actionBtnDisabled}}" bind:tap="actionClickHandler">去挑选商品</button>
  </view>
</view>
<inject-protocol noAutoAuth />