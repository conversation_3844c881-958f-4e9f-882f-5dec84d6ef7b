.order-btn {
  width: 180px !important;
  height: 36px !important;
  color: var(--theme-main-color) !important;
  border: none !important;
  background-color: var(--theme-main-rgb-color) !important;
  line-height: 36px !important;
}

.paid-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.paid {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  line-height: 1.4;
  text-align: center;
}

.paid:nth-child(1) {
  margin-top: 20px;
}

.paid__icon {
  font-size: 60px;
}

.paid-order__status {
  margin-top: 12px;
  font-size: 16px;
  color: #333333;
}

.paid-shop__name {
  color: var(--theme-main-color);
  font-size: 16px;
}

.paid-order__price {
  margin-top: 15px;
  color: #333333;
  font-size: 32px;
  font-weight: 500;
}

.paid-order__price--prefix {
  font-size: 16px;
  margin-right: 6px;
}

.youzan-copyright {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
}

.home-copyright {
  height: 260rpx;
  background-size: 192rpx 80rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url(https://b.yzcdn.cn/retail/ump/img/<EMAIL>);
}

.wx-subscription_wrap {
  width: 93.6%;
  margin-top: auto;
  position: absolute;
  bottom: 268rpx;
}

.wx-subscription {
  height: 128rpx;
  margin: 0 auto;
  padding: 9px 12px 9px 7px;
  box-sizing: border-box;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  margin-top: 12px;
  border-radius: 4px;
}

.wx-subscription__image {
  width: 104rpx;
  height: 80rpx;
}

.wx-subscription__text {
  flex: 1;
  margin-left: 8px;
}

.wx-subscription__text-main {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
  line-height: 20px;
}

.wx-subscription__text-des {
  font-size: 12px;
  color: #7d7e80;
  font-weight: 400;
  line-height: 16px;
  margin-top: 4px;
}

.wx-subscription__btn {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}
