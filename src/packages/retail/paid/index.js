import WscPage from 'pages/common/wsc-page';
import {
  getTakeCodeData,
  fetchOrderDetail,
  queryOrderInfoByNo,
} from 'retail/util/api';
import {
  getThemeMainColor,
  getThemeMainRgbColorWithAlpha,
} from 'retail/util/shelf-global';
import { getPayment } from 'retail/util/order-detail';
import theme from 'shared/common/components/theme-view/theme';
import { getIsCrmShop } from 'common-api/scrm';

/**  crm店铺升级状态 */
const CRM_STATUS_ENUM = {
  /* scrm项目迁移未开始 */
  Unavailable: 0,
  /** scrm项目迁移中 */
  Upgrading: 1,
  /** scrm项目迁移完成 */
  Available: 2,
};

const app = getApp();

WscPage({
  data: {
    status: '',
    expanse: 0,
    qrCodeBase64: '',
    selfFetchNo: '',
    showTakeCode: false,
    themeMainColor: getThemeMainColor(),
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    isShowSubscription: false, // 是否展示微信订阅消息
    themeGeneral: '',
    templateIds: [],
    hasSubscribe: false, // 是否已经订阅了
  },

  onLoad({ orderNo }) {
    this.orderNo = orderNo;
    // 获取主题色
    theme.getThemeColor('general').then((color) => {
      this.setYZData({ themeGeneral: color });
    });
    this.getTemplateIdList();
  },

  onShow() {
    this.show = true;
    this.fetchOrderDetail();
    this.setData({
      themeMainColor: getThemeMainColor(),
      themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    });
    app.logger && app.logger.pageShow();
  },

  onHide() {
    this.show = false;
    clearTimeout(this.lastRefreshDetailTimeout);
    app.logger && app.logger.pageHide();
  },

  onUnload() {
    this.show = false;
    clearTimeout(this.lastRefreshDetailTimeout);
  },

  onPullDownRefresh() {
    clearTimeout(this.lastRefreshDetailTimeout);
    this.fetchOrderDetail();
  },

  handleOrderDetailClick() {
    const { isSuccess } = this.data;
    if (!this.orderData) {
      wx.showToast({
        title: '订单状态有误，请下拉刷新！',
        icon: 'none',
      });
      return;
    }
    const dbid = getApp().db.set({
      orderData: this.orderData,
      needRefreshTimer: !isSuccess,
    });
    wx.redirectTo({
      url: `/packages/retailb/order/detail/index?dbid=${dbid}`,
    });
  },

  async fetchOrderDetail() {
    wx.showLoading();

    try {
      Promise.all([
        fetchOrderDetail({
          orderNo: this.orderNo,
        }),
        getIsCrmShop(),
        queryOrderInfoByNo(this.orderNo),
      ]).then(([orderData, { status }, [orderInfo]]) => {
        this.orderData = orderData;
        const { state } = orderData;

        const isSuccess = state > 3;
        if (!isSuccess && this.show) {
          this.lastRefreshDetailTimeout = setTimeout(() => {
            this.fetchOrderDetail();
          }, 2000);
        }

        this.setData({
          status: isSuccess ? 'success' : 'waiting',
          orderStatus: state,
          expanse: getPayment(orderData),
          isShowSubscription:
            status === CRM_STATUS_ENUM.Available &&
            ['retail_free_buy', 'retail_scan_buy'].includes(
              orderInfo.source_info?.order_mark
            ) &&
            this.data.templateIds?.length > 0,
        });
      });
    } catch (err) {
      wx.showToast({
        icon: 'none',
        title: err.msg ?? '订单获取失败，请下拉重试',
      });
    } finally {
      wx.hideLoading();
      wx.stopPullDownRefresh();
    }
  },

  async showTakeCode() {
    try {
      wx.showLoading();
      const { data: orderData } = await getTakeCodeData(this.orderNo);
      const { qrCodeBase64, selfFetchNo } = orderData.orderSelfFetchInfo;
      this.setData({
        qrCodeBase64,
        selfFetchNo,
        showTakeCode: true,
      });
    } catch (error) {
      wx.showToast({
        icon: 'none',
        title: error?.msg ?? '获取核销码失败，请重试！',
      });
    } finally {
      wx.hideLoading();
    }
  },

  closeTakeCodePopup() {
    this.setData({ showTakeCode: false });
    // 小操作-提升用户体验：假设用户关掉核销码弹框的行为 =》进行核销过了，重新拉取订单列表来刷新状态。
    this.fetchOrderDetail();
  },

  // 获取模板id
  getTemplateIdList() {
    return app
      .request({
        path: '/wscump/common/get-template.json',
        data: {
          scene: 'crm_order_evaluation_msg',
        },
      })
      .then((data = {}) => {
        this.setData({
          templateIds: data.templateIdList || [],
          isShowSubscription:
            this.data.isShowSubscription && data.templateIdList?.length > 0,
        });
      })
      .catch(() => {});
  },

  handleWxSubscribeSuccess() {
    this.setYZData({ hasSubscribe: true });
  },
});
