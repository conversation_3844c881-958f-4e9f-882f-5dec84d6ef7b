<page-container forbid-copyright page-bg-color="white">
  <view class="paid-container" style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}">
    <view class="paid">
      <!-- <icon wx:if="{{ status === 'success' }}" type="success" color="#2DA641" size="28" /> -->
      <icon wx:if="{{status !== 'success'}}" type="waiting" color="#f90" size="28" />
      <view class="paid-order__status">
        {{ status === 'success' ? '支付成功' : '支付结果等待中' }}
      </view>
    </view>

    <view class="paid">
      <!-- <view class="paid-shop__name">
        {{CURRENT_GLOBAL_SHOP.shop_name}}
      </view> -->
      <view wx:if="{{expanse >= 0}}" class="paid-order__price">
        <text class="paid-order__price--prefix">￥</text>{{ money.toYuan(expanse) }}
      </view>
    </view>

    <btn
      wx:if="{{orderStatus === 5}}"
      type="primary"
      custom-class="order-btn"
      plain
      bind:click="showTakeCode"
      style="margin-top: auto; position: absolute; bottom: {{ isShowSubscription ? 520 : 370 }}rpx"
    >
      查看核销码
    </btn>

    <btn
      custom-class="order-btn"
      type="primary"
      plain
      bind:click="handleOrderDetailClick"
      style="margin-top: auto; position: absolute; bottom: {{ isShowSubscription ? 420 : 268 }}rpx"
    >
      查看订单
    </btn>
      <!-- 微信订阅消息 -->
    <subscription-msg
      wx:if="{{ isShowSubscription}}"
      template-ids="{{ templateIds }}"
      has-subscribe="{{ hasSubscribe }}"
      bind:success="handleWxSubscribeSuccess"
      class="wx-subscription_wrap"
    >
      <view class="wx-subscription">
        <image src="https://b.yzcdn.cn/pay-result/images/weixin2.png" alt="wechat"  class="wx-subscription__image" />
        <view class="wx-subscription__text">
          <view class="wx-subscription__text-main">订阅微信通知</view>
          <view class="wx-subscription__text-des">及时获取订单提醒</view>
        </view>
        <view class="wx-subscription__btn" style="color: {{ themeGeneral }}">
          {{ hasSubscribe ? '已订阅' : '立即订阅' }}
        </view>
      </view>
    </subscription-msg>
  <view class="youzan-copyright">
    <!--  有赞水印 -->
    <view class="home-copyright" />
  </view>
    
</page-container>

<wxs src="retail/util/money.wxs" module="money" />

<take-code-popup 
  showTakeCode="{{showTakeCode}}" 
  qrCodeBase64="{{qrCodeBase64}}" 
  selfFetchNo="{{selfFetchNo}}" 
  bind:closeTakeCodePopup="closeTakeCodePopup" 
/>