import WscPage from 'pages/common/wsc-page/index';
import args from '@youzan/weapp-utils/lib/args';
import { getThemeMainColor } from 'retail/util/shelf-global';

const app = getApp();
// eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
const CONTENT_BG_IMG_PREFIX =
  'https://img01.yzcdn.cn/retail/ci_start/config/2023/11/21';

WscPage({
  data: {
    themeColor: '',
    navStyle: 'background-color: transparent;',
    info: {
      pageStyle: 1,
      pageTitle: '',
    },
    pageStyle: '',
    contentStyle: '',
    logoWrapperStyle: '',
    guideTxtStyle: '',
    buttonStyle: '',
    shopLogo: '',
  },
  onLoad(options) {
    const { dbid, redirectUrl } = options;
    this.redirectUrl = decodeURIComponent(redirectUrl);

    Promise.all([
      getThemeMainColor(),
      app
        .request({
          path: '/wscshop/shop/shop_baseinfo.json',
          data: { kdt_id: getApp().getHQKdtId() },
        })
        .catch(() => ({})),
      this.loadData(dbid),
    ]).then((res) => {
      const [themeColor, shopInfo, info] = res;

      this.setYZData({
        info,
        shopLogo: shopInfo.logo,
      });
      this.setDynamicStyle(themeColor);
    });
  },
  setDynamicStyle(themeColor) {
    const { pageStyle: pageStyleType, backgroundImageUrl } = this.data.info;
    const isDefaultPageStyle = pageStyleType === 1;

    let pageStyle = '';
    const bgCommonStyle =
      'background-size: cover; background-repeat: no-repeat; background-position: center';
    if (isDefaultPageStyle) {
      pageStyle = `background-color: ${themeColor}; background-image: url("${CONTENT_BG_IMG_PREFIX}/bg-texture.png");${bgCommonStyle}`;
    } else if (backgroundImageUrl) {
      pageStyle = `background-image: url("${backgroundImageUrl}");${bgCommonStyle}`;
    } else {
      pageStyle = `background: #717171`;
    }

    let contentStyle = '';
    if (isDefaultPageStyle) {
      contentStyle = `background-image: url('${CONTENT_BG_IMG_PREFIX}/content-white.svg')`;
    } else {
      contentStyle = 'background-color: transparent';
    }

    const buttonStyle = `background: ${themeColor}`;

    this.setYZData({
      themeColor,
      pageStyle,
      contentStyle,
      buttonStyle,
    });
  },
  loadData(dbid) {
    return app.db.get(dbid, false) || {};
  },
  handlePass() {
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx.redirectTo({
      url: args.add(this.redirectUrl, { from: 'order-add-friends' }),
    });
  },
});
