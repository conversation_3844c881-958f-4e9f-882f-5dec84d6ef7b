/* eslint-disable @youzan/koko/no-async-await */
import { moment } from '@youzan/weapp-utils/lib/time';
import WscPage from 'pages/common/wsc-page/index';
import Event from '@youzan/weapp-utils/lib/event';
import Dialog from '@vant/weapp/dialog/dialog';
import { isEmpty as isEmptyObject } from '@youzan/tee-util/lib/helper';
import { getReatilSource, getCouponList } from 'retail/util/global';
import { hasDiscount, getRecommend } from '../../uitls';
import { confirmOrder, fetchOrderDetail } from 'retail/util/api';
import { convertOrderConfirmActivities } from 'retail/util/discount';

import {
  getThemeMainColor,
  getThemeMainRgbColorWithAlpha,
} from 'retail/util/shelf-global';
import {
  getPromotionResult,
  getSelectedCoupons,
  getCanSelectedCoupons,
  getStoredDiscountIsOpen,
  getStoredDiscountPromotion,
  onCouponChange,
} from 'retail/util/ump';
import {
  getSelectedValueCard,
  getStoreValueAndCard,
  getIdsAndPromotion,
  getPrepaid,
} from 'retail/util/value-card';
import {
  queryPreOrderDetail,
  createOrderV2,
  queryPaySetting,
  addRechargeActivity,
} from './api';

const app = getApp();

const VIP_STATUS = {
  SATISFY: 1,
  DISSATISFY: 0,
};

WscPage({
  data: {
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    themeMainColor: getThemeMainColor(),
    isOrderCreated: false,
    tableName: '', // 桌码
    tableId: '', // 桌码id
    valueCardList: [], // 储值卡
    selectedValueCard: [], // 当前选择的储值卡
    storedDiscountIsOpen: false,
    storedDiscountInfo: {},
    promotionValue: 0,
    selectedCoupons: [],
    selectedMeituanCoupons: [],
    meituanCoupons: [],
    canPay: false,
    allowPayStatus: false, // 是否允许线上支付
    /** 储值卡勾选的时候和点完成的时候都会请求confirm接口，点击过快会出现接口时序问题 */
    valueCardBtnDisabled: false,
    // 储值推荐相关数据
    recommend: {},
    payInfo: {},
    isHiddenCard: false,
    totalDecrease: 0,
    totalCurrentPrice: 0,
    // isShowedAllBalancePayModal: false,
    balanceMkExclusion: false,
    fns: {},
    isCreated: false,
    scene: app.globalData.scene,
    memberLaunchArgs: {},
  },

  // isShowedAllBalancePayModal: false,
  needRecommendMemberBenefit: true,

  onLoad(options) {
    const { no, dbid } = options;
    this._useStoredCard = true;
    this._needHelpSelectCard = true; // 是否需要后端默认选中储值卡
    this._changeCouponTips = false; // 操作优惠券标识,如果有操作传true

    if (!no) {
      this.isLoad = true;
      return;
    }

    this.no = no;
    this.dbid = dbid;
    this.fetchPreOrderDetail(no, dbid);
    queryPaySetting().then(({ allowPayStatus = false }) => {
      this.setYZData({ allowPayStatus });
      this.isLoad = true;
    });

    // 优惠券选择 Listener
    this.unregisterCoupon = this.registerCouponChangeListener();

    this.setYZData({
      fns: { renderOrder: this.handleValueCardChange.bind(this) },
      memberLaunchArgs: {
        onSuccess: () => {
          app.globalData.isMemberFromOrder = true;
          this.fetchPreOrderDetail(no, dbid);
        },
      },
    });
  },

  onShow() {
    app.logger && app.logger.pageShow();
  },

  onHide() {
    app.logger && app.logger.pageHide();
  },

  onUnload() {
    this.unregisterCoupon();
    // 好像没什么用,还报错注释了
    // this.unregisterValueCard();
  },

  onPullDownRefresh() {
    if (this.no) {
      // 同步预订单
      this.fetchPreOrderDetail(this.no, this.dbid).finally(() => {
        wx.stopPullDownRefresh();
      });
    }
  },
  setDinerNum(dinerNum) {
    dinerNum && this.setYZData({ dinerNum });
  },

  handleChooseValue(item) {
    // 页面load后再允许点击档位
    const { isLoad } = this;
    if (!isLoad) return;

    // 未开启线上支付 仅展示档位和礼包
    const { allowPayStatus } = this.data;
    if (!allowPayStatus) return;

    const { detail } = item;

    this.isChooseValue = !!detail.amount;
    this.setYZData({
      payInfo: detail,
    });
  },

  // 多人购物车 暂不支持选择放弃优惠
  // checkShowedAllBalancePayModal(bizExtraMap) {
  //   // 订单享有储值会员折扣
  //   // 订单非储值余额全额支付
  //   const { VALUE_CARD_LEVEL_GROUP_TYPE, VALUE_CARD_ONLY_FULL_PAY } =
  //     bizExtraMap;
  //   this.isShowedAllBalancePayModal =
  //     +VALUE_CARD_LEVEL_GROUP_TYPE === VIP_STATUS.SATISFY &&
  //     +VALUE_CARD_ONLY_FULL_PAY === VIP_STATUS.DISSATISFY;
  // },

  /**
   * 获取预订单信息
   *
   * @param {*} no
   * @param {*} dbid
   */
  fetchPreOrderDetail(no, dbid, valueCardList = [], hasPromotion = true) {
    wx.showLoading();
    if (!this.cartId && !this.tableNo) {
      const { preOrderItem, uniqueId } = app.db.get(dbid);
      const { cartId, tableNo, tableId } = preOrderItem;

      this.uniqueId = uniqueId;
      this.cartId = cartId;
      this.tableNo = tableNo;
      this.tableId = tableId;
    }

    // 先获取预订单详情
    return queryPreOrderDetail(no).then((preOrderData) => {
      const currentPreOrderData = preOrderData.orderDetailResponseList?.[0];
      const cartItemIds = currentPreOrderData.preOrderItemList.map(
        (item) => +item.itemId
      );
      this.setDinerNum(currentPreOrderData.dinerNum);
      currentPreOrderData.uniqueId &&
        (this.uniqueId = currentPreOrderData.uniqueId);
      this.cartId = currentPreOrderData.cartId;
      this.tableNo = currentPreOrderData.tableNo;
      this.tableId = currentPreOrderData.tableId;
      this.cartItemIds = cartItemIds;
      let coupons = [];
      if (Array.isArray(this.data.coupons)) {
        coupons = [...this.data.coupons];
      }
      if (this._meituanCoupons) {
        coupons.push(...this._meituanCoupons);
      }
      // 确认订单接口
      return confirmOrder({
        cartId: this.cartId,
        uniqueId: this.uniqueId,
        cartItems: cartItemIds,
        hasPromotion,
        coupons,
        needRecommendCoupon: !this._changeCouponTips,
        showCustomerAllCoupon: this._changeCouponTips, // 展示买家的所有优惠券
        needHelpSelectCard: this._needHelpSelectCard,
        prepaidCardList: valueCardList,
        filterPreOrdered: false,
        supportAddMeal: true,
        needRecommendStoredDiscount: this._useStoredCard,
        needRecommendMemberBenefit: this.needRecommendMemberBenefit,
      })
        .then((confirmResult) => {
          wx.hideLoading();
          const {
            shopInfo,
            goodsList = [],
            unavailableGoodsList = [],
            totalCurrentPrice: realTotalPrice,
            totalPrice, // 合计 = 小计 - 使用的储值余额
            usedCustomerPayCardBalance, // 使用的储值余额
            promotion,
            payCardDTOList = [],
            preOrderInfo,
            storedDiscountInfo,
            dinerNum,
            bizExtraMap,
            thirdPromotion = [], // 三方券。现在只有美团券
            totalDecrease,
            balanceMkExclusion,
            joinMemberPromotionDTO = {}, // 引导入会数据
            message,
          } = confirmResult;
          console.log('joinMemberPromotionDTO: ', joinMemberPromotionDTO);

          if (message) {
            // 上面有hideLoading，延时展示
            setTimeout(() => {
              wx.showToast({
                title: message,
                icon: 'none',
              });
            }, 100);
          }

          // 多人购物车 暂不支持选择放弃优惠
          // if (bizExtraMap) {
          //   this.checkShowedAllBalancePayModal(bizExtraMap);
          // }

          const couponList = getCouponList(promotion);

          this.couponList = couponList;

          const valueCard = getSelectedValueCard(payCardDTOList);
          const storedValueAndCard = getStoreValueAndCard(payCardDTOList);
          this.setDinerNum(dinerNum);
          this.setYZData({
            totalCurrentPrice: realTotalPrice,
            totalDecrease,
            recommend: getRecommend(confirmResult),
            balanceMkExclusion,
            // isShowedAllBalancePayModal: this.isShowedAllBalancePayModal,
            preOrderNo: no,
            preOrderVersion: currentPreOrderData.orderVersion,
            cartId: this.cartId,
            cartItemIds,
            tableName: this.tableNo,
            tableId: this.tableId,
            goodsList: goodsList.map(this.adaptGoods),
            totalPay: realTotalPrice,
            realTotalPrice: totalPrice,
            cardPricePay: usedCustomerPayCardBalance, // 储值卡消费
            valueCardList: payCardDTOList,
            shopName: shopInfo.shopName,
            shopAddress: shopInfo.storiesAddress,
            canPay: true,
            orderActivities: convertOrderConfirmActivities(promotion),
            storedDiscountPromotion: getStoredDiscountPromotion(promotion),
            // 是否开启折扣
            storedDiscountIsOpen: getStoredDiscountIsOpen(storedDiscountInfo),
            storedDiscountInfo,
            storedValueAndCard,
            selectedValueCard: valueCard,
            promotionValue: getPromotionResult(promotion),
            selectedCoupons: getSelectedCoupons(couponList),
            canChooseCoupons: !!getCanSelectedCoupons(couponList).length,
            // 储值卡余额不足
            // cardNotEnough: !price,
            orderInfo: {
              orderNo: preOrderInfo.preOrderNo,
              createTime: preOrderInfo.createTime,
              createTimeStr: moment(
                new Date(preOrderInfo.createTime),
                'YYYY-MM-DD HH:mm:ss'
              ),
            },
            comment: preOrderInfo.remark,
            unavailableGoodsList,
            selectedMeituanCoupons: thirdPromotion.filter(
              (item) => item.isRecommend
            ),
            meituanCoupons: thirdPromotion,
            isHiddenCard: hasDiscount(promotion),
            joinMemberPromotion: joinMemberPromotionDTO || {},
          });
        })
        .catch((err) => {
          wx.hideLoading();

          wx.redirectTo({
            url: '/packages/retailb/goodslist/index',
          });
        });
    });
  },

  adaptGoods(info) {
    const { promotions = [], ...args } = info;
    let promotionTag = '';
    promotions.forEach(({ type }) => {
      if (+type === 8) {
        promotionTag = '赠品';
      } else if (+type === 24) {
        promotionTag = '换购';
      }
    });
    return {
      ...args,
      promotions,
      promotionTag,
    };
  },

  toggleActivityPop() {
    this.setYZData({
      showDiscount: !this.data.showDiscount,
    });
  },

  /**
   * 优惠券选择
   */
  handleCouponChange() {
    onCouponChange(this.couponList, this.data.selectedCoupons);
  },

  handleCreatePayProcess(e, cardNo) {
    const {
      cartId,
      cartItemIds,
      selectedCoupons,
      selectedMeituanCoupons,
      preOrderNo,
      preOrderVersion,
      selectedValueCard,
      storedDiscountPromotion,
      storedDiscountIsOpen,
      cardPricePay,
      realTotalPrice,
      isHiddenCard,
      totalPay,
      recommend,
      balanceMkExclusion,
    } = this.data;

    const storeThenPay = e === true;

    // 多人购物车 暂不支持选择放弃优惠
    // // 前置请求判断 是否开启 余额全额支付时可享受会员优惠
    // if (this.isShowedAllBalancePayModal) {
    //   const { recommendDetaid = {} } = this.data.recommend;
    //   const { productInfos } = recommendDetaid;
    //   const showConfirmButton = !isEmptyObject(productInfos);

    //   const paySubmitNewCom = this.selectComponent('#pay-submit');
    //   Dialog.confirm({
    //     message:
    //       '仅限余额(不含礼品卡)全额支付时,可享受储值会员优惠。如放弃优惠,将重新计算订单价格。',
    //     confirmButtonText: '去充值',
    //     showConfirmButton,
    //     cancelButtonText: '放弃优惠',
    //     context: this,
    //     zIndex: 99999,
    //   })
    //     .then(() => {
    //       paySubmitNewCom.cancelPay();
    //       const orderGoodsCom = this.selectComponent('#order-goods');
    //       orderGoodsCom.handleChooseValue('storeVip');
    //       // orderGoodsCom.showValueCardPop();
    //     })
    //     .catch(() => {
    //       paySubmitNewCom.cancelPay();
    //       // 不使用会员折扣
    //       this.needRecommendMemberBenefit = false;
    //       this.fetchPreOrderDetail(this.no, this.dbid);
    //     });
    //   return;
    // }

    // 组装参数
    const params = {
      preOrderNo,
      preOrderVersion,
      tableId: this.tableId,
      tableNo: this.tableNo,
      deliveryType: 3,
      orderCreateType: 2,
      business: 2,
      promotionIds: [...selectedCoupons, ...selectedMeituanCoupons].map(
        (item) => item.id
      ),
      cartId,
      uniqueId: this.uniqueId,
      cartItems: cartItemIds,
      buyerRemark: this.data.comment,
      // 开启储值折扣且使用储值支付时
      storedDiscountPromotionId:
        storedDiscountIsOpen && this._useStoredCard
          ? storedDiscountPromotion?.id
          : null,
      prepaid: getPrepaid({
        selectedValueCard,
        cardPricePay,
        storeThenPay,
        balanceMkExclusion,
      }),
      receiverPhone: app.getMobile(),
      source: getReatilSource(),
      storeThenPay,
    };

    if (storeThenPay) {
      params.storeAndPayDTO = {
        storeThenPay,
        mutex: balanceMkExclusion,
        prepaidCardList: [{ cardNo }],
      };
    }

    // 有储值优惠商品时要传R单
    if (isHiddenCard) {
      const instance = this.selectComponent('#order-goods');
      const { rechargeNo } = instance;

      params.rechargeNo = rechargeNo;
      params.needRecommendCouponThenTake = false;
    }

    // debugger

    // 创建订单
    return createOrderV2(params).then(
      (result) => {
        const {
          orderNo,
          orderPayResult = {},
          isNeedStoreCardPay = true,
        } = result;

        const payload = {
          isCreated: true,
        };

        // 储值推荐触发的开单不需要setData了 只为复用一下这个方法拿E单
        if (!storeThenPay) {
          Object.assign(payload, {
            orderNo,
            orderPayResult: { ...orderPayResult, isNeedStoreCardPay },
            isOrderCreated: true,
          });
        }

        this.setData({ ...payload });

        return result;
      },
      (error) => {
        // 特殊异常情况处理
        // 224020004 B 端加菜了
        // 224020005 订单已取消
        // 224020008 订单已创建
        if ([224020004, 224020005, 224020008].includes(error.code)) {
          // 再拉一遍预订单详情
          setTimeout(() => {
            this.fetchPreOrderDetail(preOrderNo);
          }, 1000);
        }

        this.setYZData({
          orderPayResult: { error: error.msg },
        });
      }
    );
  },

  handleStore() {
    /**
     * 充值并支付的链路 选择档位后 更新底部bar 点击充值将会触发store事件
     * 1. 创建订单拿到E单号
     * 2. 使用E单号调用prepay拿到R单号
     * 3. 支付传入R单号
     */

    const instance = this.selectComponent('#order-goods');
    const { isRecommend, isDiscount, newRecommend, cardNo } = instance;

    // 没有cardNo就是没开卡 可以调用order-goods的injectCardNo进行开卡
    const checkCardNo = cardNo
      ? Promise.resolve.bind(Promise, cardNo)
      : instance.injectCardNo.bind(instance);

    checkCardNo().then((n) => {
      // 储值推荐
      if (isRecommend) {
        // 充值并支付需要先创建订单并切拿到E单号 先充后付不需要
        const Fn = newRecommend
          ? this.handleCreatePayProcess.bind(this)
          : Promise.resolve.bind(Promise, {});

        Fn(true, cardNo).then((result) => {
          const { orderNo } = result;

          instance.handlePay({ orderNo });
        });
      }

      if (isDiscount) {
        // 储值优惠 拉起充值
        instance.handlePay();
      }

      // 不会走到这里了
    });
  },

  handleRechargeDone() {
    const instance = this.selectComponent('#order-goods');
    const { rechargeNo } = instance;

    return addRechargeActivity({
      rechargeNo,
      cartId: this.cartId,
      source: getReatilSource(),
      adminId: app.getBuyerId(),
    })
      .then((result) => {
        if (result.value) {
          /**
           * 1. 添加折扣商品
           * 2. 通过order-goods储值卡数据变更逻辑重新confirm
           *
           * 先吃后付模式 储值优惠充值完直接将订单确认
           * 解决办法：
           * 1. 通知order-goods卡变更时传个标
           * 2. 重新confirm完成后直接确定订单
           */
          instance.handleSelectCard(true);
        }
      })
      .catch(() => {});
  },

  // TODO: 跟 buy/index 一样了，找个地方抽一下
  handlePaidSuccess({ detail: { orderNo } }) {
    wx.showLoading({ title: '支付结果查询中 ' });

    let isSuccessDone = false;
    const fetchOrderStatusInterval = 300;
    const fetchOrderStatusOvertime = 3000;

    const jumpToPaidPage = () => {
      wx.redirectTo({
        url: `/packages/retail/paid/index?orderNo=${orderNo}`,
        complete: () => {
          wx.hideLoading();
          clearTimeout(this.fetchOrderDetailTimer);
          clearTimeout(this.failFetchOrderDetailTimer);
        },
      });
    };

    const syncOrderStatus = () =>
      fetchOrderDetail({ orderNo })
        .then((orderData) => {
          if (orderData.state === 100) {
            isSuccessDone = true;
            return jumpToPaidPage();
          }
          this.fetchOrderDetailTimer = setTimeout(
            syncOrderStatus,
            fetchOrderStatusInterval
          );
        })
        .catch(() => {
          jumpToPaidPage();
        });

    this.fetchOrderDetailTimer = setTimeout(
      syncOrderStatus,
      fetchOrderStatusInterval
    );
    // 超过最大时间，则跳转到paid页面
    this.failFetchOrderDetailTimer = setTimeout(() => {
      if (!isSuccessDone) {
        jumpToPaidPage();
      }
    }, fetchOrderStatusOvertime);
  },

  // 优惠券选择监听
  // todo: 和 buy/index 的逻辑基本一样，可以抽取到一处
  registerCouponChangeListener() {
    const onOrderCouponChange = (coupons) => {
      this.setYZData({ coupons });
      this._needHelpSelectCard = true;
      const { selectedValueCard } = this.data;
      const map = {};
      selectedValueCard.forEach((item) => {
        map[item.summaryCardNo] = item;
      });
      const { cardIds, isExclusion } = getIdsAndPromotion(map);
      this._changeCouponTips = true;
      this._useStoredCard = cardIds.length > 0;
      this.fetchPreOrderDetail(this.no, this.dbid, cardIds, !isExclusion);
    };

    const EVT_NAME = 'order-coupon-change';

    Event.on(EVT_NAME, onOrderCouponChange);

    return () => {
      Event.off(EVT_NAME, onOrderCouponChange);
    };
  },
  // todo: 和非加菜订单的逻辑一起抽取一下
  handleValueCardChange({
    cards,
    isDiscount,
    isRecommend,
    immediate,
    isMutex,
  }) {
    const { cartId, cartItemIds } = this;
    const { deliveryWay: deliveryType, supportAddMeal } = this.data;
    this._needHelpSelectCard = false;
    this.setYZData({
      valueCardBtnDisabled: true,
    });

    const params = {
      isDiscount,
      cartId,
      cartItems: cartItemIds,
      deliveryType,
      needHelpSelectCard: false,
      supportAddMeal,
      hasPromotion: false,
      filterPreOrdered: false,
      needRecommendStoredDiscount: false,
      isMutex,
    };

    // 储值优惠禁用优惠券 同时不能选储值卡支付
    if (isDiscount) {
      params.needRecommendCoupon = false;
      params.needRecommendMemberBenefit = false;
    } else {
      const { cardIds, isExclusion } = getIdsAndPromotion(cards);
      params.prepaidCardList = cardIds;
      params.hasPromotion = !isExclusion;
      params.needRecommendStoredDiscount = cardIds.length > 0;
    }

    return confirmOrder(params)
      .then((orderData) => {
        const {
          totalCurrentPrice,
          usedCustomerPayCardBalance,
          totalPrice,
          payCardDTOList,
          totalDecrease,
          promotion,
          memberBenefits,
          goodsList = [],
          bizExtraMap,
          message,
        } = orderData;

        if (message) {
          wx.showToast({
            title: message,
            icon: 'none',
          });
        }

        // 多人购物车 暂不支持选择放弃优惠
        // if (bizExtraMap) {
        //   this.checkShowedAllBalancePayModal(bizExtraMap);
        // }

        const couponList = getCouponList(promotion);
        const selectedValueCard =
          supportAddMeal || isDiscount
            ? []
            : getSelectedValueCard(payCardDTOList);

        const storedValueAndCard = getStoreValueAndCard(payCardDTOList);

        const data = {
          recommend: getRecommend(orderData),
          totalDecrease,
          totalPay: totalCurrentPrice,
          realTotalPrice: totalPrice,
          cardPricePay: usedCustomerPayCardBalance,
          selectedCoupons: supportAddMeal ? [] : getSelectedCoupons(couponList),
          selectedValueCard,
          storedValueAndCard,
          isHiddenCard: hasDiscount(promotion),
          memberBenefits,
          canUseMemberBenefit: memberBenefits.length > 0,
          goodsList: goodsList.map(this.adaptGoods),
          canChooseCoupons: !!getCanSelectedCoupons(couponList).length,
        };

        /**
         * 如果互斥点击去充值去支付
         * 第二次renderOrder不一定返回储值档位
         * 所以不第二次不更新推荐信息
         */
        if (isMutex) {
          delete data.recommend;
        }

        this.setYZData({ ...data });
      })
      .finally(() => {
        this.setYZData({
          valueCardBtnDisabled: false,
        });
        if (immediate) {
          const paySubmit = this.selectComponent('#pay-submit');
          paySubmit.emitPay();
        }
      });
  },

  // todo: 和非加菜订单的逻辑一起抽取一下
  handleValueCardSubmit({ detail }) {
    const {
      detail: { cards = {} },
    } = detail;
    const { cardIds, isExclusion } = getIdsAndPromotion(cards);
    this._useStoredCard = cardIds.length > 0;
    this.fetchPreOrderDetail(this.no, this.dbid, cardIds, !isExclusion);
  },

  async handleChangeMeituanCoupons({ detail: { done, refresh = true, ids } }) {
    this._changeCouponTips = true;
    this._meituanCoupons = ids;
    if (refresh) {
      this._needHelpSelectCard = true;
      const { selectedValueCard } = this.data;
      const map = {};
      selectedValueCard.forEach((item) => {
        map[item.summaryCardNo] = item;
      });
      const { cardIds, isExclusion } = getIdsAndPromotion(map);
      this._changeCouponTips = true;
      this._useStoredCard = cardIds.length > 0;
      await this.fetchPreOrderDetail(this.no, this.dbid, cardIds, !isExclusion);
    }
    done && done();
  },
});
