import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
// import carmen from 'retail/util/carmen';
import retailCarmen from 'retail/util/retail-carmen';
import { getReatilSource } from 'retail/util/global';

const app = getApp();

/**
 * 根据预订单号查询预订单详情
 *
 * @param {string} orderNo
 */
export function queryPreOrderDetail(orderNo) {
  return app.retailRequest({
    path: '/retail/h5/trade/preOrderDetail.json',
    data: {
      preOrderNo: orderNo,
      retailSource: getReatilSource(),
    },
  });
}

/**
 * 创建订单
 *
 * @param {*} params
 */
export const createOrder = (params) => {
  return retailCarmen({
    api: 'youzan.retail.trade.customer/1.0.0/createorder',
    method: 'POST',
    data: params,
  }).then(mapKeysCase.toCamelCase);
};

/**
 * 创建订单
 *
 * @param {*} params
 */
export const createOrderV2 = (params) => {
  if (!params.uniqueId) {
    delete params.uniqueId;
  }
  return app.retailRequest({
    path: '/retail/h5/trade/createOrder.json',
    method: 'POST',
    data: params,
  });
};

// 获取支付方式，是否允许C端支付
export function queryPaySetting() {
  return app.retailRequest({
    path: '/retail/h5/trade/queryPaySetting',
  });
}

// 添加减钱商品
export const addRechargeActivity = (data) => {
  return app.retailRequest({
    path: '/retail/h5/miniprogram/addRechargeActivity.json',
    method: 'POST',
    data,
  });
};
