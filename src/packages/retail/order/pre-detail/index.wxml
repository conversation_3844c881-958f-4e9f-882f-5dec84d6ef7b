<page-container
  forbid-copyright
  pageBgColor="#F2F2F2"
  style="--theme-main-color: {{themeMainColor}}"
>
  <!-- 订单状态 -->
  <order-state
    state="{{ 3 }}"
    stateDesc="下单成功，坐等开吃"
  />

  <guide-card 
    wx:if="{{ preOrderNo }}"
    mainColor="{{ themeMainColor }}" 
    locationType="{{ 2 }}"
    tableId="{{ tableId }}"
    tableCode="{{ tableName }}"
    styles="margin: -12rpx 24rpx 24rpx;"
  />

  <order-container
    navigation-style="black"
    bg-color="#F2F2F2"
    style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
  >
    <!-- 桌号信息 -->
    <!-- <picking-code
      appointment-time="-"
      table-code="{{ tableName }}"
    /> -->

    <view class="order-goods-title" wx:if="{{tableName}}">
      <!-- <van-icon name="https://img01.yzcdn.cn/upload_files/2023/02/27/Fq61km5PO2WUTyHb3oIYq10RQ56-.png" class="food" size="24px" /> -->
      <text style="font-size: 32rpx;">{{ tableName }}桌</text>
      <text class="diner-num" wx:if="{{dinerNum}}">{{ dinerNum }}位</text>
    </view>

    <order-goods
      id="order-goods"
      scene="{{scene}}"
      fns="{{fns}}"
      is-done="{{ isOrderCreated }}"
      is-submit-page
      shop-name="{{ shopName }}"
      recommend-detail="{{ recommendDetail }}"
      pay-asset-activity-tag-desc="{{ payAssetActivityTagDesc }}"
      goods-list="{{ goodsList}}"
      order-info="{{ orderInfo }}"
      total-price="{{ totalPay }}"
      real-total-price="{{realTotalPrice}}"
      card-price-pay="{{cardPricePay}}"
      coupons="{{ selectedCoupons }}"
      can-choose-coupon="{{canChooseCoupons}}"
      promotion-value="{{ promotionValue }}"
      card-not-enough="{{cardNotEnough}}"
      unavailable-goods-list="{{ unavailableGoodsList }}"
      value-card="{{ selectedValueCard }}"
      value-card-btn-disabled="{{ valueCardBtnDisabled }}"
      stored-value-and-card="{{storedValueAndCard}}"
      stored-discount-is-open="{{storedDiscountIsOpen}}"
      stored-discount-info="{{storedDiscountInfo}}"
      totalDecrease="{{totalDecrease}}"
      totalCurrentPrice="{{totalCurrentPrice}}"
      balanceMkExclusion="{{balanceMkExclusion}}"
      bind:switchcoupon="handleCouponChange"
      bind:changeCoupon="handleCouponChange"
      bind:switchcoupon="handleCouponChange"
      bind:switchCardList="handleValueCardChange"
      bind:showDiscountDialog="toggleActivityPop"
      bind:valueCardChange="handleValueCardChange"
      bind:valueCardSubmit="handleValueCardSubmit"
      bind:chooseValue="handleChooseValue"
      bind:store="handleStore"
      bind:paid-success="handlePaidSuccess"
      bind:rechargeDone="handleRechargeDone"
      showDeliveryTypeDesc
      showComment
      comment="{{ comment || '无' }}"
      theme-color="{{ themeMainColor }}"
      meituan-coupons="{{ meituanCoupons }}"
      bind:changeMeituanCoupons="handleChangeMeituanCoupons"
      recommend="{{recommend}}"
      isHiddenCard="{{isHiddenCard}}"
      allowPayStatus="{{allowPayStatus}}"
      style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
      goodsWrapperStyle="border-radius: 0 0 16rpx 16rpx"
      mainColor="{{themeMainColor}}"
      isCreated="{{isCreated}}"
    >
      <!-- 引导入会 TODO:这里现在组件用不了，等中台化再加 -->
      <view slot="member-guide">
        <member-entry
          joinMemberPromotion="{{joinMemberPromotion}}"
          launchArgs="{{memberLaunchArgs}}"
          themeColor="{{themeMainColor}}"
          hostShow="{{!isHiddenCard}}"
          user-authorize-type="separate"
          member-scene="retailPreDetail"
          title="现在入会，本单立省"
          hasBgColor
        />
      </view>
    </order-goods>

    <pay-submit
      wx:if="{{ canPay }}"
      id="pay-submit"
      order-no="{{ orderNo }}"
      price="{{realTotalPrice}}"
      card-price-pay="{{cardPricePay}}"
      order-pay-result="{{ orderPayResult }}"
      value-card-no="{{selectedValueCard}}"
      is-empty-cart="{{ !goodsList || !goodsList.length }}"
      show-append
      bind:pay="handleCreatePayProcess"
      bind:paid-success="handlePaidSuccess"
      bind:store="handleStore"
      style="--text-valign: baseline; --theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
      showPrice="{{ false }}"
      disabled="{{ !allowPayStatus }}"
      money="￥{{ money.toYuan(realTotalPrice) }}"
      small-pay-button="{{ allowPayStatus }}"
      payInfo="{{payInfo}}"
    >
      {{allowPayStatus ? '结账' : '请线下联系服务员结账'}}
    </pay-submit>

  </order-container>

  <activity-pop
    show="{{ showDiscount }}"
    activities="{{ orderActivities }}"
    total-decrease="{{ promotionValue }}"
    bind:closeActivityPop="toggleActivityPop"
  />
</page-container>
<van-dialog id="van-dialog" />
<user-authorize-popup />

<wxs src="retail/util/money.wxs" module="money" />
