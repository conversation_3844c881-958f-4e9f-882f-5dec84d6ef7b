@import '../pre-detail/index.scss';

.top-custom-nav {
  background-color: var(--theme-main-color);
  color: #fff;
}

.bottom-bar {
  box-shadow: 0 -2px 10px rgba(125, 126, 128, 0.16);
  background: white;
  display: flex;
  height: 100rpx;
  align-items: center;
  justify-content: flex-end;
  position: fixed;
  left: 0;
  right: 0;
  padding-bottom: var(--indicatorHeight, 0);
  bottom: 0;
  z-index: 99;
}

.pay-button {
  box-sizing: border-box;
  display: flex !important;
  justify-content: center;
  align-items: center;
  width: 192rpx;
  height: 72rpx !important;
  font-size: 28rpx !important;
  font-weight: 500;
  border-radius: 10em !important;
  line-height: 48px !important;
  padding: 0 !important;
  margin-left: auto;
  margin-right: 12px;
  overflow: visible !important;
}

.table-code-shopname{
  max-width:80%;
}

.diner-num {
  font-weight: 500;
  font-size: 15px;
  color: #323233;
  padding-left: 9px;
  position: relative;
  margin-left: 9px;
}
.diner-num::before {
  position: absolute;
  left: 0;
  top: 3px;
  display: block;
  content: '';
  width: 2px;
  height: 78%;
  background-color: #e0e0e0;
}

.card-cell {
  --title-color: #646566;
  --title-size: 30rpx;
  --title-size: 30rpx;
  --header-justify: space-between;
  --card-padding: 32rpx;
  --card-height: 72rpx;
}

.banner-container {
  width: 100%;
}
