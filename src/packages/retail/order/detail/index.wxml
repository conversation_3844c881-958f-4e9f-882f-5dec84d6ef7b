<page-container
  forbid-copyright
  pageBgColor="#F2F2F2"
  style="--theme-main-color: {{themeMainColor}}"
>
  <order-state
    state="{{ state }}"
    stateDesc="{{ stateDesc }}"
    stateDescTips="{{ stateDescTips }}"
    bind:countdownEnd="countdownEnd"
  />
  <order-container
    navigation-style="black"
    bg-color="#F2F2F2"
    style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
  >
    <!-- 桌码不展示该模块 -->
    <block wx:if="{{!tableCode}}">
      <picking-code
        wx:if="{{scene === 2 && (pickUpCode && state !== 99 && state !== 3)}}"
        pickup-code="{{pickUpCode}}"
        table-code="{{tableCode}}"
        appointment-time="{{selfFetchTime}}"
        order-created-time="{{ orderInfo.createTime }}"
        is-trade-success="{{ state === 100 }}"
        is-custom-pick-up-code="{{ isCustomPickUpCode }}"
        style="--theme-main-color: {{themeMainColor}};"
        orderNo="{{orderNo}}"
      />

      <!-- 添加条件判断 -->
      <take-code wx:if="{{scene === 1 && state === 5}}">
        <take-code-ticket
          takeCodeImage="{{qrCodeBase64}}"
          takeCode="{{selfFetchNo}}"
          bottomDesc="请将该核销码出示给收银员"
        />
      </take-code>

      <!-- 扫码点单： 茶饮排队通知进度
        1. 扫码点单
        2. 交易状态成功
        3. 开启茶饮通知
        4. 不是预约订单，selfFetchTime 默认值是 ‘-’
      -->
      <card-panel
        wx:if="{{scene === 2 && state === 100 && !!queueReminderConfigResponse.status && selfFetchTime == '-'}}"
        title="取餐时间"
        class="card-cell"
        style="--card-height: 50rpx; --border-style: none; --header-direction: column; --header-align: flex-start;--title-size: 28rpx; --title-color: #323233;"
        use-body
      >
        <view class="banner-container">
          <progress-banner
            id="progress-banner"
            progressType="card"
            orderNo="{{orderNo}}"
          />
        </view>
      </card-panel>
    </block>

    <!-- 卡片一天只会展示一次，detail页面跳转频繁，所以只有支付成功和交易关闭才展示 -->
    <guide-card 
      wx:if="{{ state === 100 || state === 99 }}"
      mainColor="{{ themeMainColor }}"
      locationType="{{ 3 }}"
      tableCode="{{ tableCode }}"
      tableId="{{ tableId }}"
      styles="margin: 0 0 24rpx;"
    />

    <view wx:if="{{ scene === 2 }}" class="order-goods-title">
      <text class="table-code-shopname" i18n-off>{{ shopName }}</text>
      <text wx:if="{{tableCode}}" class="diner-num">
        {{tableCode}}桌
      </text>
    </view>

    <order-goods
      is-done
      show-discount-detail
      shop-name="{{ shopName }}"
      shop-location="{{ shopAddress }}"
      goods-list="{{ goodsList }}"
      order-info="{{ orderInfo }}"
      coupons="{{ coupons }}"
      total-price="{{ realPay }}"
      phase-pay-details="{{phasePayDetails}}"
      promotion-value="{{ promotionValue }}"
      diner-num="{{dinerNum}}"
      delivery-type-desc="{{ deliveryTypeDesc }}"
      storedDiscountValue="{{storedDiscountValue}}"
      showDeliveryTypeDesc="{{scene === 2}}"
      comment="{{ comment || '无' }}"
      showComment="{{scene === 2}}"
      can-choose-meituan="{{ false }}"
      style="--theme-main-color: {{themeMainColor}}"
      goodsWrapperStyle="border-radius: {{ scene === 2 ? '0 0 16rpx 16rpx' : '16rpx' }}"
      bind:showDiscountDialog="toggleShopDiscount"
      extraPriceDTOS="{{extraPriceDTOS}}"
      appointment-time="{{selfFetchTime}}"
    />
  </order-container>

  <activity-pop
    show="{{showDiscount}}"
    activities="{{orderActivities}}"
    total-decrease="{{promotionValue}}"
    bind:closeActivityPop="toggleShopDiscount"
  />

  <pay-submit-new
    wx:if="{{state < 4}}"
    canPay="{{state < 4 &&  isOrderCreator}}"
    scene="{{scene}}"
    hidden-pay="{{canNotPayForStoredDiscountFreeScanOrder}}"
    show-cancel="{{ showCancel && orderCanCancel }}"
    is-empty-cart="{{!goodsList || !goodsList.length}}"
    order-no="{{orderNo}}"
    order-pay-result="{{orderPayResultInfo}}"
    bind:pay="handlePay"
    bind:paid-success="handlePaidSuccess"
    bind:cancel="handleOrderCancel"
    style="--text-valign: baseline; --theme-main-color: {{themeMainColor}}"
    price="{{ payment }}"
    showPrice="{{ false }}"
  >
    立即支付
  </pay-submit-new>

  <!-- <view
    class="bottom-bar"
    wx:elif="{{canBuyAgain}}"
    style="{{isIPhoneX ? '--indicatorHeight: 34px' : ''}}"
  >
    <btn
      custom-class="pay-button"
      plain
      hairline
      round
      type="info"
      color="{{themeMainColor}}"
      bind:click="buyAgain"
    >再来一单</btn>
  </view> -->
</page-container>
