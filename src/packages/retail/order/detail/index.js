import { moment } from '@youzan/weapp-utils/lib/time';
import WscPage from 'pages/common/wsc-page/index';
import { convertOrderDetailDiscountNew } from 'retail/util/discount';
import {
  cancelOrderAPI,
  buyOrderAgain,
  fetchOrderDetail,
  fetchShopConfig,
} from 'retail/util/api';
import { getStoredDiscountValue } from 'retail/util/ump';
import {
  getThemeMainColor,
  getThemeMainRgbColorWithAlpha,
} from 'retail/util/shelf-global';
import { isNewIphone } from 'shared/utils/browser/device-type';

const app = getApp();

const SCAN_GO_ORDER_TYPE = 107;
const FREE_GO_ORDER_TYPE = 106;
// 等待付款
const WAIT_TO_PAY = 3;

WscPage({
  data: {
    shopName: '',
    deliveryType: 1,
    code: 0,
    payment: 0,
    realPay: 0,
    promotionValue: 0,
    stateDesc: '',
    stateDescTips: '',
    shopAddress: '',
    goodsList: [],
    coupons: [],
    showDiscount: false,
    showCancel: true,
    isIPhoneX: isNewIphone(),
    scene: app.globalData.scene,
    // 扫码点单：是否开启排队通知设置
    queueReminderConfigResponse:
      app.globalData.retailConfig?.queueReminderConfigResponse || {},
    // 自助点单：106， 自助结账：107
    orderType: 0,
    selfFetchNo: '',
    qrCodeBase64: '',
    themeMainColor: getThemeMainColor(),
    //  一些额外费用 比如打包费
    extraPriceDTOS: [],
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    storedDiscountValue: 0,
    // 店铺配置 待付款是否可以取消
    orderCanCancel: false,
    // 当前用户是否为创建订单者
    isOrderCreator: false,
  },

  onLoad({ dbid, orderNo, cartId }) {
    if (dbid) {
      const inf = app.db.get(dbid) || {};
      this.needRefreshTimer = !!inf.needRefreshTimer;
      cartId = inf.cartId;
      if (!inf.orderData) {
        wx.showToast({
          title: '获取订单详情失败, 请重试！',
          icon: 'none',
        });
        return;
      }
      this.renderData(inf.orderData);
      orderNo = inf.orderData.orderNo;
      this.setData({
        orderType: inf.orderData.orderType,
      });
    } else {
      this.setData({
        orderNo,
      });
    }

    this.cartId = cartId;
    // 获取店铺配置 消费者是否可以手动取消订单
    this.getOrderCanCancel();
    this.fetchAddRenderOrderDetail();
  },

  onShow() {
    this.mayBePaid && this.fetchAddRenderOrderDetail();
    this.setData({
      themeMainColor: getThemeMainColor(),
      themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    });
    app.logger && app.logger.pageShow(); // onShow page进入事件
  },

  onHide() {
    this.mayBePaid = true;
    this.clearRefreshTimeout();
    app.logger && app.logger.pageHide();
  },

  onUnload() {
    this.clearRefreshTimeout();
  },

  async onPullDownRefresh() {
    try {
      this.clearRefreshTimeout();
      await this.fetchAddRenderOrderDetail();
      if (
        this.data.queueReminderConfigResponse.status &&
        this.data.selfFetchTime == '-'
      ) {
        const progressBar = this.selectComponent('#progress-banner');
        progressBar && (await progressBar.refresh());
      }
    } catch (error) {
      wx.showToast({
        title: error.msg || '下拉刷新失败，请重试!',
      });
    } finally {
      wx.stopPullDownRefresh();
    }
  },

  fetchAddRenderOrderDetail() {
    return fetchOrderDetail({ orderNo: this.data.orderNo, cartId: this.cartId })
      .then((res) => {
        const currentUserId = +app.getUserInfoSync().yzUserId;
        this.setData({
          isOrderCreator: res.buyerInfo?.buyerId === currentUserId,
        });
        this.renderData(res);
      })
      .catch((ex) => {
        wx.showModal({
          title: '获取订单详情失败',
          content: ex.msg || '',
        });
      });
  },

  adaptGoodsList(goodsList) {
    return goodsList.map((element) => {
      const {
        title: name,
        specifications,
        picUrl,
        imgUrl,
        pricingStrategy,
        unit,
        price,
        unitPrice,
        tagsInfo = '{}',
        comboDetail,
        isCombo,
        goodsActivities = [],
      } = element;

      let num = element.actualNum || element.num;

      num /= 1000;
      // TODO: 这段代码重复的出现在两个地方，考虑重构。
      const unitToShow =
        pricingStrategy === 10 ? `${num.toFixed(2)} ${unit}` : `x${num}`;
      let promotionTag = '';
      try {
        const tags = JSON.parse(tagsInfo);
        if (tags.IS_PRESENT) {
          promotionTag = '赠品';
        } else if (goodsActivities.some((item) => item.type === 'plusBuy')) {
          promotionTag = '换购';
        }
      } catch (err) {
        promotionTag = '';
      }

      if (isCombo) {
        try {
          comboDetail.comboGroups = comboDetail.comboGroupInfos.map((ii) => {
            ii.comboSubSkus = ii.comboSubItemInfos.map((kk) => {
              return {
                ...kk,
                props: kk.pros,
                skuDesc:
                  (kk.skuName || '') +
                  (kk.addPrice ? `¥${kk.addPrice / 100}` : ''),
              };
            });

            return ii;
          });
        } catch (error) {
          comboDetail.comboGroups = [];
        }
      }

      return {
        name,
        unitToShow,
        num,
        comboDetail,
        isCombo,
        specifications,
        pricingStrategy,
        price: price ?? unitPrice,
        picUrl: picUrl ?? imgUrl,
        promotionTag,
      };
    });
  },

  handlePay() {
    this.setData({
      orderPayResultInfo: this.orderPayResultInfo,
    });
  },

  // Copy and paste
  handlePaidSuccess({ detail: { orderNo } }) {
    // wx.showLoading({ title: '支付结果查询中' });
    setTimeout(async () => {
      // wx.hideLoading();
      const orderData = await fetchOrderDetail({ orderNo });

      // 此时订单的状态还不在支付成功
      if (orderData.state !== 100) {
        return wx.redirectTo({
          url: `/packages/retail/paid/index?orderNo=${orderNo}`,
        });
      }

      this.renderData(orderData);
    }, 500);
  },

  async buyAgain() {
    try {
      switch (this.data.orderType) {
        case SCAN_GO_ORDER_TYPE:
          await buyOrderAgain({ orderNo: this.data.orderNo });
          wx.redirectTo({
            url: '/packages/retail/scan-page/index',
          });
          break;
        case FREE_GO_ORDER_TYPE:
          await buyOrderAgain({ orderNo: this.data.orderNo });
          wx.redirectTo({
            url: '/packages/retailb/goodslist/index',
          });
          break;
        default:
          return;
      }
    } catch (err) {
      wx.showToast({
        icon: 'none',
        title: err.msg ?? '再来一单创建失败',
      });
    }
  },

  getOrderCanCancel() {
    const key = 'retail_ump_miniapp_order_can_cancel';
    fetchShopConfig([key])
      .then((res) => {
        const orderCanCancel = res[key] === '1';
        this.setData({
          orderCanCancel,
        });
      })
      .catch(() => {
        this.setData({
          orderCanCancel: true,
        });
      });
  },

  handleOrderCancel({ detail: { orderNo } }) {
    wx.showModal({
      content:
        '商家正在火速准备中，请您耐心等待，如仍需取消订单，建议您优先联系商家协商处理',
      confirmColor: this.data.themeMainColor,
      confirmText: '再等等',
      cancelColor: '#999',
      cancelText: '仍然取消',
      success: async (res) => {
        if (res.confirm) return;

        try {
          await cancelOrderAPI({ orderNo, cartId: this.cartId });
          this.setData({
            isCancel: true,
            showCancel: false,
          });
        } catch (err) {
          wx.showToast({
            icon: 'none',
            title: '取消订单失败，请重试',
          });
        } finally {
          this.setData({
            isCancel: false,
          });
          this.fetchAddRenderOrderDetail();
        }
      },
    });
  },

  /**
   * orderType === 107 是自助结账(扫码购)
   * orderType === 106 是自助点单
   * state, orderType
   * 待付款 3
   * 交易关闭 99
   * 交易成功 100
   * 待发货 5
   * 已发货 6
   */
  canBuyAgain({ state, orderType }) {
    // 扫码购里面暂时不需要再来一单功能
    // if (orderType === SCAN_GO_ORDER_TYPE) {
    //   return state === 100;
    // }
    if (orderType === FREE_GO_ORDER_TYPE) {
      return state === 100 || state === 99;
    }
    return false;
  },

  getShopAddress({ shopInfo: { city = '', area = '', address = '' } = {} }) {
    return city + area + address;
  },

  getCoreOrderInfo({
    orderType,
    deliveryType,
    shopName,
    deliveryTypeDesc,
    createTime,
    payTime,
    orderNo,
    payment,
    realPay,
    buyerRemark: comment = '',
    stateDesc,
    state,
    stateDescTips = '',
    activityPromotionAmount,
    tableCode,
    tableId,
    dinerNum,
    couponPromotionAmount,
    selfFetchTime = '',
    pickUpCode = '',
    selfFetchNo = '',
    qrCodeBase64 = '',
    isCustomPickUpCode = false,
    buyWayDesc,
    canNotPayForStoredDiscountFreeScanOrder = false,
  }) {
    const orderInfo = {
      orderNo,
      createTime,
      createTimeStr: moment(new Date(createTime), 'YYYY-MM-DD HH:mm:ss'),
      payTimeStr: moment(new Date(payTime), 'YYYY-MM-DD HH:mm:ss'),
      payTime,
      buyWayDesc,
    };
    if (state === 5) {
      stateDesc = '待核销';
    }
    if (state === 100) {
      stateDesc = '交易成功，感谢光顾';
    }
    if (state === 6) {
      stateDesc = '交易完成';
    }
    return {
      orderType,
      selfFetchNo,
      qrCodeBase64,
      orderInfo,
      shopName,
      deliveryTypeDesc,
      comment,
      deliveryType,
      orderNo,
      payment,
      realPay,
      state,
      stateDesc,
      stateDescTips,
      pickUpCode,
      isCustomPickUpCode,
      tableCode,
      tableId,
      dinerNum,
      selfFetchTime,
      coupons: [
        {
          decreaseAmount: couponPromotionAmount,
        },
      ],
      promotionValue: activityPromotionAmount ?? 0,
      canNotPayForStoredDiscountFreeScanOrder,
    };
  },

  renderData(orderData) {
    if (!orderData) return;

    const {
      orderPayResultInfo = {},
      orderActivities = [],
      extraPriceDTOS = [],
      phasePayDetails = [], // 组合支付,付款详情
    } = orderData;

    this.orderPayResultInfo = orderPayResultInfo;

    this.setData({
      shopAddress: this.getShopAddress(orderData),
      goodsList: this.adaptGoodsList(orderData.itemInfo),
      canBuyAgain: this.canBuyAgain(orderData),
      orderActivities: convertOrderDetailDiscountNew(orderActivities),
      storedDiscountValue: getStoredDiscountValue(orderActivities),
      ...this.getCoreOrderInfo(orderData),
      phasePayDetails,
      extraPriceDTOS,
    });

    if (!this.refreshTimer && orderData.state === WAIT_TO_PAY) {
      this.startRefreshTimerWithCount(2);
    }
  },

  startRefreshTimerWithCount(tryCount = 3) {
    let refreshCount = 0;
    const timeoutTime = 1000;
    const timerFn = () => {
      wx.showLoading({ title: '同步订单信息中' });

      fetchOrderDetail({ orderNo: this.data.orderNo, cartId: this.cartId })
        .then((res = {}) => {
          const { state } = this.data;
          // 如果当前订单状态存在，且最新请求到的订单状态与当前不同再进行重新渲染, 结束。
          if (res.state !== state) {
            this.renderData(res);
            wx.hideLoading();
            this.clearRefreshTimeout();
            return;
          }
          // 如果订单状态不是待付款或者已经刷新了3次, 结束
          if (res.state !== WAIT_TO_PAY || refreshCount >= tryCount) {
            wx.hideLoading();
            this.clearRefreshTimeout();
            return;
          }
          refreshCount += 1;
          this.refreshTimer = setTimeout(timerFn, timeoutTime);
        })
        .catch((ex) => {
          wx.hideLoading();
          wx.showModal({
            title: '获取订单详情失败',
            content: ex.msg || '',
          });
        });
    };
    this.refreshTimer = setTimeout(timerFn, timeoutTime);
  },
  handleBack() {
    wx.navigateBack();
  },
  toggleShopDiscount() {
    this.setData({
      showDiscount: !this.data.showDiscount,
    });
  },
  countdownEnd() {
    // 倒计时结束重新拉取
    this.onPullDownRefresh();
  },
  clearRefreshTimeout() {
    clearTimeout(this.refreshTimer);
    this.refreshTimer = null;
  },
});
