<page-container
  forbid-copyright
  forbid-banner
  page-bg-color="#f2f3f5"
  style="--theme-main-color: {{themeMainColor}}"
>
  <template
    wx:if="{{isMulticartOrderList}}"
    is="orderlist"
    data="{{list:  multiCartOrderList, loading, cartId, orderCanCancel }}"
  />
  <tabs
    wx:else
    color="{{ themeMainColor }}"
    custom-class="order-tabs"
    swipe-threshold="{{swipe}}"
    swipeable
    sticky
    bind:change="handleTabChange"
    active="{{selectedTab}}"
  >
    <tab wx:for="{{tabs}}" wx:key="index" wx:for-item="title" title="{{title}}">
      <template
        wx:if="{{orders[selectedTab].list.length > 0}}"
        is="orderlist"
        data="{{list: orders[selectedTab].list, loading, cartId, orderCanCancel }}"
      />
      <view wx:else class="order-list__empty">
        <view class="order-list__empty-logo"></view>
        <view class="order-list__empty__text">暂无订单</view>
      </view>
    </tab>
  </tabs>
</page-container>

<template name="orderlist">
  <view
    class="order-list__content">
    <order-item
      wx:for="{{ list }}"
      wx:key="orderNo"
      cart-id="{{cartId}}"
      class="order-item"
      order-can-cancel="{{ orderCanCancel }}"
      bind:cancel-order="cancelOrder"
      order-data="{{ item }}"
      bind:showTakeCode="showTakeCode"
    />
    <view wx:if="{{loading}}" style="display: flex">
      <van-loading style="margin: auto" />
    </view>
  </view>
</template>

<take-code-popup
  showTakeCode="{{showTakeCode}}"
  qrCodeBase64="{{qrCodeBase64}}"
  selfFetchNo="{{selfFetchNo}}"
  bind:closeTakeCodePopup="closeTakeCodePopup"
/>
