.order-item {
  background: #fff;
  border-radius: 8rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
}

.order-item order-intro {
  --padding: 12rpx 0;
}

.order-item__header {
  min-height: 88rpx;
  box-sizing: border-box;
  position: relative;
  display: flex;
  align-items: center;
}

.order-item__shop {
  flex: 1;
  font-size: 24rpx;
  color: #969799;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-item__status {
  flex-shrink: 0;
  margin-left: auto;
  font-size: 28rpx;
}

.order-item__address {
  font-size: 24rpx;
  font-weight: 500;
  color: #323233;
  flex-shrink: 0;
  margin-right: 16rpx;
}

.order-item__info-desc {
  font-size: 12px;
  color: #666;
}

.order-item__summary {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 16rpx 0;
  font-size: 14px;

  .info {
    display: flex;
    margin-left: auto;
    align-items: baseline;

    &__goods-count {
      margin-right: 8px;
      font-size: 12px;
      color: #999;
    }
  }
}

.order-item__takecode{
  width: 100%; 
  display: flex; 
  justify-content: flex-end; 
  margin-bottom: 16rpx;
}

/* 订单列表底部 footer */
.order-item__footer {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  padding: 12rpx 0;
}

.order-action {
  min-width: 156rpx;
  height: 56rpx;
  font-size: 26rpx;
  overflow: visible !important;
}

.btn-hairline {
  color: #332233 !important;
  border-color: #979797 !important;
}
