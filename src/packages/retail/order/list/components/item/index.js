import { cancelOrderAPI, buyOrderAgain } from 'retail/util/api';
import {
  getThemeMainColor,
  getThemeMainRgbColorWithAlpha
} from 'retail/util/shelf-global';

const app = getApp();

Component({
  properties: {
    orderData: Object,
    cartId: String,
    orderCanCancel: Boolean
  },

  data: {
    needShowPayBtn: false,
    totalGoodsCount: 0,
    themeMainColor: getThemeMainColor(),
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1)
  },

  attached() {
    this.setData({
      themeMainColor: getThemeMainColor(),
      themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1)
    });
  },

  methods: {
    noop() { },
    navigateToOrder() {
      const dbid = app.db.set({
        orderData: this.data.orderData,
        cartId: this.data.cartId
      });
      wx.navigateTo({
        url: `/packages/retailb/order/detail/index?dbid=${dbid}`
      });
    },
    cancelOrder() {
      wx.showModal({
        content: '商家正在火速准备中，请您耐心等待，如仍需取消订单，建议您优先联系商家协商处理',
        confirmText: '再等等',
        cancelColor: '#999',
        cancelText: '仍然取消',
        confirmColor: this.data.themeMainColor || '#1989FA',
        success: res => {
          if (res.confirm) return;

          const { orderNo } = this.data.orderData;

          cancelOrderAPI({ orderNo, cartId: this.data.cartId })
            .then(() => {
              this.triggerEvent('cancel-order', { orderNo });
            })
            .catch(err => {
              wx.showToast({
                icon: 'none',
                title: err?.msg ?? '取消订单失败'
              });
            });
        }
      });
    },
    async buyAgain() {
      try {
        await buyOrderAgain({
          orderNo: this.data.orderData.orderNo
        });
        wx.navigateTo({
          url: '/packages/retailb/goodslist/index',
        });
      } catch (err) {
        wx.showToast({
          icon: 'none',
          title: err.msg ?? '再来一单创建失败'
        });
      }
    },

    // 展示核销码 弹框
    showTakeCode() {
      this.triggerEvent('showTakeCode', this.data.orderData);
    }
  }
});
