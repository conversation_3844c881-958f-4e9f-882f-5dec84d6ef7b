<!-- text 最傻逼的问题就是会保留换行 -->
<view
  bind:tap="navigateToOrder"
  class="order-item"
  style="width: calc(750rpx - 48rpx); --theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}">
  <view class="order-item__header">
    <text
      class="order-item__address">{{ orderData.addressDesc }}</text>
    <text class="order-item__shop">{{ orderData.orderCreatedTime }}</text>
    <text
      class="order-item__status"
      style="color: {{themeMainColor}}">{{ orderData.stateDesc }}</text>
  </view>
  <view class="order-item__body">
    <order-intro
      wx:for="{{ orderData.orderList }}"
      wx:key="goodsId"
      goods="{{item}}"
    />
  </view>
  <view class="order-item__footer">
    <view class="order-item__summary">
      <view wx:if="{{ orderData.hasOmit }}" class="omit">···</view>
      <view class="info">
        <text class="info__goods-count">共 {{ orderData.totalGoodsCount }} 件, 合计:</text>
        <price price="{{orderData.payment}}" style="--price-color: #323233" />
      </view>
    </view>
    <view
      catch:tap="noop"
      class="order-item__takecode">
      <btn
        wx:if="{{orderData.state === 5}}"
        round
        bind:click="showTakeCode"
        type="info"
        plain
        hairline
        size="small"
        custom-class="order-action btn-hairline"
      >
        查看核销码
      </btn>
      <block wx:if="{{orderData.needShowPayBtn}}">
        <btn
          wx:if="{{ orderCanCancel }}"
          round
          bind:click="cancelOrder"
          type="info"
          plain
          hairline
          size="small"
          style="margin-right: 32rpx"
          custom-class="order-action btn-hairline"
        >
          取消订单
        </btn>
        <btn
          wx:if="{{!orderData.canNotPayForStoredDiscountFreeScanOrder}}"
          bind:click="navigateToOrder"
          round
          hairline
          type="info"
          size="small"
          color="{{themeMainColor}}"
          custom-class="order-action"
        >
          立即支付
        </btn>
      </block>
      <btn
        wx:elif="{{orderData.canBuyAgain}}"
        round
        bind:click="buyAgain"
        type="info"
        plain
        hairline
        size="small"
        color="{{themeMainColor}}"
        custom-class="order-action"
      >
        再来一单
      </btn>
    </view>
  </view>
</view>
