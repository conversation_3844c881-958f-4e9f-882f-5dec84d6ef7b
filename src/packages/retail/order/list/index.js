import { moment } from '@youzan/weapp-utils/lib/time';
import { isFreeGo, isScanGo } from 'retail/util/global';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import cacheFetch from 'retail/util/cache-fetch';
import { getTakeCodeData, fetchShopConfig } from 'retail/util/api';
import {
  getThemeMainColor,
  getThemeMainRgbColorWithAlpha
} from 'retail/util/shelf-global';
import { fetchList as fetchListWithPagination, fetchOrderList } from './api';

const SCAN_GO_ORDER_TYPE = 107;
const FREE_GO_ORDER_TYPE = 106;
const app = getApp();
const orderStateMap = {
  all: null,
  topay: 3
};

const stateMap = ['all', 'topay'];
const tabs = ['全部', '待付款'];

Page({
  data: {
    selectedTab: 0,
    orders: tabs.map(() => ({
      list: [],
      page: 1,
      finished: false,
      fetching: null
    })),
    tabs,
    swipe: 2,
    isFreeGo: false,
    multiCartOrderList: [],
    showTakeCode: false,
    qrCodeBase64: '',
    selfFetchNo: '',
    themeMainColor: getThemeMainColor(),
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    orderCanCancel: true
  },

  onShow() {
    this.getOrderList(undefined, true);
    this.setData({
      themeMainColor: getThemeMainColor(),
      themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1)
    });
    app.logger && app.logger.pageShow();
  },

  onHide() {
    this.isFirstLoad = false;
    app.logger && app.logger.pageHide();
  },

  onLoad({ type, cartId = '' }) {
    const tabMap = {
      topay: 1
    };
    this.orderListFetch = [];
    this.isFirstLoad = true;
    this.setData({
      isFreeGo: isFreeGo(),
      cartId,
      isMulticartOrderList: !!cartId,
      selectedTab: tabMap[type] || 0
    });
    // 获取店铺配置 消费者是否可以手动取消订单
    this.getOrderCanCancel();
  },

  onPullDownRefresh() {
    const { selectedTab, isMulticartOrderList } = this.data;
    if (!isMulticartOrderList) {
      this.orderListFetch[selectedTab] = null;
    }
    this.getOrderList(selectedTab);
  },
  getOrderCanCancel() {
    const key = 'retail_ump_miniapp_order_can_cancel'
    fetchShopConfig([key]).then(res => {
      const orderCanCancel = res[key] === '1';
      this.setData({
        orderCanCancel
      })
    })
  },
  adaptList(list) {
    return list
      .map(item => {
        const { state, itemInfo, orderType, createTime } = item;

        const orderList = itemInfo.map(this.adaptGoods);
        const totalGoodsCount = orderList.reduce((num, item) => {
          return num + (item.pricingStrategy === 10 ? 1 : item.num);
        }, 0);
        const hasOmit = orderList.length > 3;

        // 原来是 待发货
        if (item.state === 5) {
          item.stateDesc = '待核销';
        }
        // 原来是 已发货
        if (item.state === 6) {
          item.stateDesc = '交易完成';
        }

        return {
          ...item,
          needShowPayBtn: state === 3,
          orderCreatedTime: moment(createTime, 'YYYY-MM-DD HH:mm:ss'),
          canBuyAgain:
            (state === 99 || state === 100) && orderType === 106 && isFreeGo(),
          addressDesc: this.adaptExtraInfo(item),
          totalGoodsCount,
          hasOmit,
          orderList: hasOmit ? orderList.slice(0, 3) : orderList
        };
      })
      .filter(item => {
        if (this.data.isMulticartOrderList) return true;

        if (isFreeGo()) {
          return item.orderType === FREE_GO_ORDER_TYPE;
        }
        if (isScanGo()) {
          return item.orderType === SCAN_GO_ORDER_TYPE;
        }
        return false;
      });
  },

  adaptGoods(info) {
    const {
      title,
      unitPrice: price,
      specifications,
      skuDesc,
      propertiesDesc,
      imgUrl,
      pricingStrategy,
      unit,
      tagsInfo = '{}',
      goodsActivities = []
    } = info;
    let { num } = info;
    num /= 1000;
    const unitToShow =
      pricingStrategy === 10 ? `${num.toFixed(2)} ${unit}` : `x${num}`;

    let promotionTag = '';
    try {
      const tags = JSON.parse(tagsInfo);
      if (tags.IS_PRESENT) {
        promotionTag = '赠品';
      } else if (goodsActivities.some(item => item.type === 'plusBuy')) {
        promotionTag = '换购';
      }
    } catch (err) {
      promotionTag = '';
    }
    return {
      name: title,
      num,
      unitToShow,
      pricingStrategy,
      picUrl: cdnImage(imgUrl, '!100x100+2x.jpg'),
      price,
      propertiesDesc,
      specifications: specifications ?? skuDesc,
      promotionTag
    };
  },

  adaptExtraInfo({ deliveryTypeDesc, deliveryType, orderType }) {
    if (orderType === 107) {
      return '自助结账';
    }
    switch (deliveryType) {
      case 3:
        return '堂食';
      case 1:
        return '外带';
      default:
        return deliveryTypeDesc;
    }
  },

  async fetchOrderList(tab) {
    const fetchPagination = this.orderListFetch[tab];

    this.setData({
      selectedTab: tab,
      loading: true
    });

    wx.showNavigationBarLoading();
    try {
      const renderOrderListWithCache = list => {
        this.setData({
          [`orders[${tab}]`]: {
            list: this.adaptList(list)
          }
        });
      };

      const res = await fetchPagination({
        onCacheRead: renderOrderListWithCache,
        justCacheOnce: true
      });
      if (res === null) {
        return;
      }
      this.setData({
        [`orders[${tab}]`]: {
          list: this.adaptList(res)
        }
      });
    } catch (err) {
      wx.showToast({
        icon: 'none',
        title: err.message ?? '订单加载失败，请下拉重试'
      });
    } finally {
      this.setData({
        loading: false
      });
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
    }
  },

  fetchMultiOrderList(dataToPosted) {
    fetchOrderList({
      ...dataToPosted,
      cartId: this.data.cartId,
      pageSize: 20, // 拉尽量多的待付款列表
      pageNo: 1
    }).then(({ data }) => {
      this.setData({
        multiCartOrderList: this.adaptList(data)
      });
      wx.stopPullDownRefresh();
    });
  },

  getOrderList(tab = this.data.selectedTab, force = false) {
    if (this.orderListFetch[tab] && !force) {
      return this.setData({
        selectedTab: tab
      });
    }

    const headKdtId = app.getHQKdtId();
    const dataToPosted = {
      orderManageSource: 3,
      orderState: orderStateMap[stateMap[tab]],
      pageSize: 10,
      headKdtId: app.getKdtId() === headKdtId ? null : headKdtId
    };

    if (this.data.isMulticartOrderList) {
      this.fetchMultiOrderList(dataToPosted);
    } else {
      const isAllTab = tab === 0;
      this.orderListFetch[tab] = isAllTab
        ? cacheFetch(
          fetchListWithPagination(dataToPosted),
          `retail:orderlist:${headKdtId}`
        )
        : fetchListWithPagination(dataToPosted);
      this.fetchOrderList(tab);
    }
  },

  onReachBottom() {
    if (this.data.isMulticartOrderList) return;

    const { orders, selectedTab } = this.data;
    if (orders[selectedTab].finished) return;
    this.fetchOrderList(selectedTab);
  },

  handleTabChange({ detail: { index } }) {
    this.getOrderList(index);
  },

  cancelOrder({ detail }) {
    const { orderNo } = detail;
    if (this.data.isMulticartOrderList) {
      return this.cancelMulticartOrder(orderNo);
    }
    const allList = this.data.orders[0].list.slice();
    const notPayList = this.data.orders[1].list.slice();

    const findOrder = item => item.orderNo === orderNo;
    const indexInAll = allList.findIndex(findOrder);
    const indexInNotPay = notPayList.findIndex(findOrder);

    allList[indexInAll] = {
      ...allList[indexInAll],
      state: 99,
      needShowPayBtn: false,
      stateDesc: '交易关闭'
    };

    if (~indexInNotPay) {
      notPayList[indexInNotPay] = {
        ...notPayList[indexInNotPay],
        state: 99,
        needShowPayBtn: false,
        stateDesc: '交易关闭'
      };
    }

    this.setData({
      'orders[0].list': allList,
      'orders[1].list': notPayList
    });
  },

  cancelMulticartOrder(orderNo) {
    const allList = this.data.multiCartOrderList.slice();

    const findOrder = item => item.orderNo === orderNo;
    const indexInAll = allList.findIndex(findOrder);

    allList[indexInAll] = {
      ...allList[indexInAll],
      state: 99,
      needShowPayBtn: false,
      stateDesc: '交易关闭'
    };

    this.setData({
      multiCartOrderList: allList
    });
  },

  async showTakeCode({ detail }) {
    try {
      wx.showLoading();
      const { data: orderData } = await getTakeCodeData(detail.orderNo);
      const { qrCodeBase64, selfFetchNo } = orderData.orderSelfFetchInfo;
      this.setData({
        qrCodeBase64,
        selfFetchNo,
        showTakeCode: true
      });
    } catch (error) {
      wx.showToast({
        icon: 'none',
        title: error?.msg ?? '获取核销码失败，请重试！'
      });
    } finally {
      wx.hideLoading();
    }
  },

  closeTakeCodePopup() {
    this.setData({ showTakeCode: false });
    // 小操作-提升用户体验：假设用户关掉核销码弹框的行为 =》进行核销过了，重新拉取订单列表来刷新状态。
    this.onPullDownRefresh();
  }
});
