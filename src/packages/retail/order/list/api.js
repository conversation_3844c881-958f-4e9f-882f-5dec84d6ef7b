import carmen from 'retail/util/retail-carmen';
import usePagination from 'retail/util/use-pagination';

export function fetchOrderList({
  pageSize,
  pageNo,
  orderManageSource,
  headKdtId,
  cartId,
  orderState
}) {
  const data = {
    page_size: pageSize,
    page_no: pageNo,
    order_manage_source: orderManageSource
  };

  headKdtId && (data.head_kdt_id = headKdtId);
  cartId && (data.cart_id = cartId);
  orderState && (data.order_state = orderState);

  return carmen({
    api: 'youzan.retail.trademanager.order.consumer/1.0.0/search',
    data
  });
}

export const fetchList = usePagination(fetchOrderList);
