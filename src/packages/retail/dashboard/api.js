import promisify from 'utils/promisify';
import { gcjToBaidu } from '@/helpers/lbs';
import retailCarmen from 'retail/util/retail-carmen';
import carmen from 'retail/util/carmen';

const getSetting = promisify(wx.getSetting);
const getLocation = promisify(wx.getLocation);

const getShopInfo = ({ lng, lat } = {}) => {
  const data =
    lng && lat
      ? {
          current_lon: lng,
          current_lat: lat
        }
      : {};
  return retailCarmen({
    api: 'youzan.retail.shop.consumer.info/1.0.0/query',
    data
  }).then(res => ({
    ...res,
    currentLocation: {
      lon: data.current_lon,
      lat: data.current_lat
    }
  }));
};

export async function askLocationPermission(askPermission = true) {
  let locationHandle = getLocation;
  if (!askPermission) {
    const { authSetting } = await getSetting();
    const locationScope = 'scope.userLocation';
    if (!authSetting[locationScope]) {
      locationHandle = () => Promise.reject();
    }
  }
  return locationHandle({
    type: 'gcj02'
  });
}

export function fetchShopInfo(askPermission = true) {
  return askLocationPermission(askPermission).then(
    ({ latitude, longitude }) => {
      return getShopInfo(gcjToBaidu(longitude, latitude));
    },
    () => getShopInfo({})
  );
}

export function mirgateKdtId(kdtId) {
  return carmen({
    api: 'youzan.shop.migrate/1.0.0/get',
    data: { kdt_id: kdtId }
  }).then(
    data => {
      if (data?.offlineSubShopKdtId) return data.offlineSubShopKdtId;
      if (data?.kdtId) return data.kdtId;
      return kdtId;
    },
    () => kdtId
  );
}

