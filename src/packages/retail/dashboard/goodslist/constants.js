import * as ShelfUtil from 'retail/util/shelf-global';

export const initialData = {
  groups: [],
  cartId: '',
  allGoods: {},
  badge: {},
  totalPrice: 0,
  originPrice: 0,
  groupInfoList: [],
  messageBubbles: [],
  showAllDiscount: false,
  isMultiCart: false,
  allDiscountInfo: '',
  chosenGoods: [],
  unavailableGoodsList: [],
  selectedGroupId: '',
  paymentConfig: 2, // paymentConfig: 2 表示先支付，1 表示先下单
  loaded: {},
  isSearch: false,
  isNegativeStock: false,
  tableCode: '',
  isCustomGroups: true,
  placeholder: '点击搜索商品',
  umpPopOpen: false,
  selectedGroupIndex: 0,
  makeUpGroupList: {},
  themeMainColor: ShelfUtil.getThemeMainColor(),
  customNavHeight: 0,
  currentScrollGroupId: 0,
  showPreOrder: false,
  preOrderNo: null,
  preOrderDBId: '',
  listLock: false, // 拉取购物车的锁
  groupAnchor: '',
  secondGroupAnchorDefault: {},
  getGoodsListDone: false,
  showItemGroupIconNum: 0, // 展示分组icon的数量
  couponList: [],
  myCouponList: [],
  showMyCouponList: false,
  receiveCouponNeedPhone: false,
  hasOnline: true,
  isUpgrade: false,
  shopInfo: {},
};

// 分组初始化截取的初始位置和长度
export const initGroupsFetchIndex = 0;
export const initFetchGroupLength = 15;

export const needComboEnum = 99;

// sku 组件的场景
export const Scene = {
  Common: 'common', // 初始状态
  Surcharge: 'surcharge', // 缩略图
  Combo: 'combo', // 大图(也就是恢复初始状态，但是有动画)
};

/** 营销活动 */
export const ACTIVITY_TYPE = {
  /** 打包一口价 */
  PACKAGE_BUY: 109,
  /** 满减送 */
  MEET_REDUCE: 107,
  /** 加价购 */
  PLUS_BUY: 24,
  /** 第二件半价 */
  SECOND_HALF: 115,
  /** 会员价-等级卡 */
  VIP_PRICE_LEVEL: 1001011,
  /** 会员价-权益卡 */
  VIP_PRICE_BENEFIT: 1001012,
  /** 会员价-特权卡 */
  VIP_PRICE_SPECIAL: 1001013,
  /** 限时折扣 */
  TIME_LIMITED: 13,
  /** 会员价 */
  VIP_PRICE: 10,
  /** 会员等级折扣 */
  VIP_LEVEL_DISCOUNT: 12,
};

/** 营销操作类型 */
export const ACTIVITY_OPER_TYPE = {
  /** 换购 */
  EXCHANGE: 'exchange',
  /** 凑单 */
  RECOMMEND: 'recommend',
  /** 赠品 */
  PRESENT: 'present',
};

/** 标类型 */
export const FLAG_TYPE = {
  /** 换购 */
  EXCHANGE_GOODS: 'EXCHANGE_GOODS',
  /** 满赠 */
  MEET_REDUCE_PRESENT: 'MEET_REDUCE_PRESENT',
};

/** 标名称 */
export const FLAG_TYPE_NAME = {
  /** 换购 */
  EXCHANGE_GOODS: '换购',
  /** 满赠 */
  MEET_REDUCE_PRESENT: '赠品',
};
