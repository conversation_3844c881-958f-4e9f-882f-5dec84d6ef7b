/* eslint-disable @youzan/koko/no-async-await */
import promisify from 'utils/promisify';
import { gcjToBaidu } from '@/helpers/lbs';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import usePagination from 'retail/util/use-pagination';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import retailCarmen from 'retail/util/retail-carmen';
import { getReatilSource as getRetailSource } from 'retail/util/global';
import { needComboEnum } from './constants';

const app = getApp();

export const fetchGoodsByGroup = usePagination(
  ({ groupIds, name, pageNo = 1, pageSize = 20 }) => {
    if (!(groupIds || name)) return Promise.resolve();

    const commonParam = {
      page_size: pageSize,
      page_no: pageNo,
      attributes: [1],
      need_combo: needComboEnum,
    };
    name && (commonParam.name_or_sku_no = name);
    groupIds && (commonParam.group_ids = groupIds.filter(Number));

    return retailCarmen({
      api: 'youzan.retail.product.search.offline.api.search/1.0.0/forcustomer',
      data: commonParam,
    }).then(({ items, paginator }) => {
      return {
        data: items
          .filter((goods) => goods.measurement !== 10)
          .map((item) =>
            mapKeysCase.toCamelCase({
              ...item,
              picture: cdnImage(item.photo_url, '!100x100+2x.jpg'),
              goodsId: item.item_id,
              price: item.price,
            })
          ),
        total: paginator.total_count,
      };
    });
  }
);

export const fetchGoodsGroup = usePagination(({ pageNo: page = 1 }) => {
  const isShop = 1;
  return retailCarmen({
    api: 'youzan.wx.item.group.manage.upper/3.0.0/search',
    method: 'GET',
    data: {
      channel: isShop,
      page,
    },
  })
    .then((res) => ({
      data: res.items,
      total: res.paginator.total_count,
    }))
    .then((group) => {
      group.data.forEach((item) => {
        item.id = item.group_id;
        item.name = item.title;
      });
      return group;
    });
});

/**
 * 初始化多人购物车
 */
export const initialMultiCart = ({ tableId, tableNum, fansType, fansId }) => {
  return app.retailRequest({
    path: '/retail/h5/cart/initMultiCart.json',
    method: 'POST',
    data: {
      tableId: +tableId,
      tableNum,
      fansType,
      fansId: +fansId,
    },
  });
};

export const fetchUmpList = () => {
  return retailCarmen({
    api: 'youzan.retail.ump.multitype/1.0.0/query',
    data: {
      promotion_types: ['meetReduce', 'bale'],
      query_num: 30,
    },
  }).then((res) => mapKeysCase.toCamelCase(res));
};

/** 活动通栏获取所有营销活动标 */
export const fetchAllUmpList = () => {
  return app.retailRequest({
    path: '/retail/h5/ump/shopActivityLabel.json',
    data: {
      promotion_types: JSON.stringify([
        'bale',
        'meetReduce',
        'mndiscount',
        'plusBuy',
      ]),
      query_num: 100,
      retail_source: getRetailSource(),
    },
  });
};

export const fetchGeneralGroup = (page = 1) => {
  const isShop = 1;
  return retailCarmen({
    api: 'youzan.wx.item.group.manage.upper/3.0.0/search',
    method: 'GET',
    data: {
      channel: isShop,
      page,
      page_size: 10,
    },
  })
    .then((res) => {
      return {
        data: res.items,
        total: res.paginator.total_count,
      };
    })
    .then((group) => {
      group.data.forEach((item) => {
        item.groupId = item.group_id;
        item.id = item.group_id;
        item.name = item.title;
      });

      // 只展示一级分组
      group.data = group.data.filter((item) => item.upper_id === 0);
      return group;
    });
};

export const fetchAllGoodsByGroupIds = ({
  groupIds,
  name,
  pageNo = 1,
  pageSize = 20,
} = {}) => {
  const commonParam = {
    page_size: pageSize,
    page_no: pageNo,
    attributes: [1],
    needCombo: needComboEnum,
    retail_source: getRetailSource(),
  };
  name && (commonParam.name_or_sku_no = name);
  groupIds && (commonParam.group_ids = groupIds.filter(Number));

  return app
    .retailRequest({
      path: '/retail/h5/miniprogram/scan-buy/goods/goodsListByGroupIds.json',
      method: 'POST',
      data: commonParam,
    })
    .then((res) => {
      res.items = res.items.map(
        ({
          currentGroup: firstGroup,
          childrenGroups: secondGroupList = [],
        }) => {
          return {
            firstGroup: {
              id: firstGroup.groupId,
              groupId: firstGroup.groupId,
              name: firstGroup.title,
              groupIcon: firstGroup.itemGroupIcon,
              showSellNumType: firstGroup.showSellNumType,
              currentGroupItems: firstGroup.currentGroupItems.map((item) =>
                mapKeysCase.toCamelCase({
                  ...item,
                  picture: cdnImage(item.photoUrl, '!100x100+2x.jpg'),
                  goodsId: item.itemId,
                  price: item.price,
                  firstGroupId: firstGroup.groupId,
                })
              ),
            },
            secondGroupList: secondGroupList.map((secondGroup) => ({
              id: secondGroup.groupId,
              groupId: secondGroup.groupId,
              name: secondGroup.title,
              showSellNumType: secondGroup.showSellNumType,
              currentGroupItems: secondGroup.currentGroupItems.map((item) =>
                mapKeysCase.toCamelCase({
                  ...item,
                  picture: cdnImage(item.photoUrl, '!100x100+2x.jpg'),
                  goodsId: item.itemId,
                  price: item.price,
                  firstGroupId: firstGroup.groupId,
                  secondGroupId: secondGroup.groupId,
                })
              ),
            })),
          };
        }
      );

      return res;
    });
};

export const getGroupList = () => {
  return app
    .retailRequest({
      path: '/retail/h5/miniprogram/scan-buy/group/groups.json',
      method: 'GET',
    })
    .then((res) => {
      // TODO 销量字段控制
      // 把多个二级分组合并到一个一级分组中
      const groups = [];
      res.forEach((group) => {
        if (group.itemGroupIcon) {
          group.itemGroupIcon = cdnImage(
            group.itemGroupIcon,
            '!100x100+2x.jpg'
          );
        }
        if (group.upperId) {
          const upperGroup = groups.find(
            (item) => item.groupId === group.upperId
          );

          // 二级分组的信息
          const childGroup = {
            groupId: group.groupId,
            name: group.title,
            showSellNumType: group.showSellNumType,
          };
          if (upperGroup) {
            upperGroup.childrenGroups.push(childGroup);
          } else {
            groups.push({
              groupId: group.upperId,
              id: group.upperId,
              title: group.upperTitle,
              name: group.upperTitle,
              childrenGroups: [childGroup],
            });
          }

          return;
        }
        groups.push({
          ...group,
          id: group.groupId,
          name: group.title,
        });
      });

      return groups;
    });
};

/**
 * 查询优惠券列表
 */
export const getCouponList = () => {
  return app.retailRequest({
    path: '/retail/h5/coupon/list.json',
    method: 'GET',
  });
};

/**
 * 查询用户的优惠券列表
 */
export const getMyCouponList = () => {
  return app.retailRequest({
    path: '/retail/h5/coupon/user/list.json',
    method: 'GET',
  });
};

/**
 * 一键领取优惠券
 */
export const receiveAllCoupon = ({ couponIds }) => {
  return app.retailRequest({
    path: '/retail/h5/coupon/receive-all.json',
    method: 'POST',
    data: {
      couponIds,
    },
  });
};
/**
 * 领取单张优惠券
 */
export const receiveOneCoupon = ({ couponId }) => {
  return app.retailRequest({
    path: '/retail/h5/coupon/receive-one.json',
    method: 'POST',
    data: {
      couponId,
    },
  });
};

/**
 * 读取优惠券设置
 */
export const getCouponSetting = () => {
  return app.retailRequest({
    path: '/retail/h5/coupon/setting.json',
    method: 'GET',
  });
};

const getSetting = promisify(wx.getSetting);
const getLocation = promisify(wx.getLocation);

const getShopInfo = ({ lng, lat } = {}) => {
  const data =
    lng && lat
      ? {
          current_lon: lng,
          current_lat: lat,
        }
      : {};
  return retailCarmen({
    api: 'youzan.retail.shop.consumer.info/1.0.0/query',
    data,
  }).then((res) => ({
    ...res,
    currentLocation: {
      lon: data.current_lon,
      lat: data.current_lat,
    },
  }));
};

export async function askLocationPermission(askPermission = true) {
  let locationHandle = getLocation;
  if (!askPermission) {
    const { authSetting } = await getSetting();
    const locationScope = 'scope.userLocation';
    if (!authSetting[locationScope]) {
      locationHandle = () => Promise.reject();
    }
  }
  return locationHandle({
    type: 'gcj02',
  });
}

export function fetchShopInfo(askPermission = true) {
  return askLocationPermission(askPermission).then(
    ({ latitude, longitude }) => {
      return getShopInfo(gcjToBaidu(longitude, latitude));
    },
    () => getShopInfo({})
  );
}
