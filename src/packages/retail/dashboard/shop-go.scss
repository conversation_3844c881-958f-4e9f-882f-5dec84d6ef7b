.container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 80px);
}

.step-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  background-color: white;

  &__introduction {
    background-image: url('https://su.yzcdn.cn/wsc-minapp/icon/retail/intruduction.png');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
    height: 660rpx;
    width: 100%;
  }
}

.tabbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88px;
  position: fixed;
  bottom: var(--indicatorHeight, 0px);
  left: 0;
  right: 0;
  padding: 0 80rpx;

  &--background {
    position: fixed;
    height: 1000px;
    width: 1000px;
    border-radius: 100%;
    bottom: calc(-900px + var(--indicatorHeight, 0px));
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    box-shadow: 0 0 8px 0 #eee;
  }

  &__navigator {
    display: flex;
    flex-direction: column;
    z-index: 1;
    // 清空 button 的默认样式
    background-color: unset;
    margin: initial;
    padding: initial;
    flex: 1;
    align-items: center;

    &::after {
      border: none;
    }

    &__icon {
      width: 22px;
      height: 22px;
      &:nth-child(2) {
        width: 54px;
        height: 54px;
      }
    }

    text {
      margin-top: 5px;
      font-size: 12px;
      color: #333;
      text-align: center;
    }
  }
}

@media screen and (max-width: 330px) {
  .step-container__introduction {
    height: 500rpx;
  }
}
