/* eslint-disable prettier/prettier */
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import args from '@youzan/weapp-utils/lib/args';

import { isNewIphone } from 'shared/utils/browser/device-type';

import WscPage from 'pages/common/wsc-page/index';
import {
  queryOpenShops,
  fetchAppConfig,
  checkWhiteList,
} from 'retail/util/api';
import { queryPreOrderStatus } from 'retail/util/api/order';
import { fetchTableCodeWithTableid } from 'retail/util/api/misc';
import { assignMinappConfig } from 'retail/util/config';
import * as shelfUtil from 'retail/util/shelf-global';
import cacheFetch from 'retail/util/cache-fetch';
import { constructParamsFromUrl } from 'retail/util/base';
import { initGlobalThemeType } from 'retail/util/shelf-global';
import { isFreeGo, isScanGo } from 'retail/util/global';

import { getTabIcons } from './utils';
import { fetchShopInfo, mirgateKdtId } from './api';
import { getGroupList, fetchAllGoodsByGroupIds } from './goodslist/api';
import { formatGroupsParams } from './goodslist/utils';
import {
  initGroupsFetchIndex,
  initFetchGroupLength,
} from './goodslist/constants';

const app = getApp();

const SCAN_PAGE_PATH = '/packages/retail/scan-page/index?switchWxScan=true';
const NEED_CHECKED_URLS = [SCAN_PAGE_PATH];
const PHONE_DIALOG_KEY = 'has_reject_phone_auth';

WscPage({
  data: {
    shopInfo: {
      logo: '',
      name: '',
      location: '',
      distance: 0,
      isHq: false,
      isSingle: true,
    },
    showSkeleton: true,
    themeMainRgbColor: shelfUtil.getThemeMainRgbColorWithAlpha(0.1),
    themeMainColor: shelfUtil.getThemeMainColor(),
    isiPhoneX: isNewIphone(),
    productPictures: [],
    currentSwiperIndex: 0,
    scene: 0, // 这里不用 isScan 这种 boolean 变量，可以减少一次和默认值不同的时候的重绘
    takeConfig: null,
    // 扫码点单：就餐方式
    mealTakingModeResponse: {},
    // 扫码点单：是否开启餐饮排队设置
    queueReminderConfigResponse: {},
    hadSettingAppointment: false,
    showPopup: true,
    shopHeight: 0,
    // tab的路由
    urls: [
      '',
      '/packages/retail/order/list/index',
      '/packages/retail/usercenter/dashboard-v2/index?isRetail=1&needNav=1',
    ],
    tabs: ['首页', '订单', '我的'],
    icons: {
      cart: 'https://su.yzcdn.cn/wsc-minapp/icon/retail/cart.svg',
      tabs: {
        normal: getTabIcons(),
        active: getTabIcons(true),
      },
    },
    routers: [
      {
        title: '扫码点单',
        subtitle: '扫码点单免排队',
        icon: 'https://su.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>',
      },
    ],
    showOrderedButton: false,
    preOrderNo: null,
    preOrderDBId: '',
  },

  onLoad({ q, ...query }) {
    wx.showLoading({
      mask: true,
    });
    const config = this.convertQueryToConfig({ q, query });
    this.config = config;
    this.fetchShopInfo = cacheFetch(fetchShopInfo, `shop:info:${config.kdtId}`);
    // 前置处理了,不需要跳转
    this.firstLoad = false;
    // 主要的进店逻辑
    // this.convertShopInfo();
    // 授权弹框
    const { scene } = config;
    // 扫码点单场景下
    if (+scene === 2) {
      // 判断是不是在 跳过获取手机号白名单
      this.checkWhiteList();
    }
  },

  onShow() {
    // 每次进入页面后重新请求预订单信息，刷新已点按钮的显示状态
    if (app.globalData.retailConfig.isSupportAddMeal) {
      this.refreshOrderedButton();
    }
    wx.hideHomeButton && wx.hideHomeButton();
    app.logger && app.logger.pageShow();
    this.convertShopInfo();
  },

  onHide() {
    app.logger && app.logger.pageHide();
    // 清除定时器
    clearTimeout(this.confirmDelay);
  },
  async prefetchGoodsData() {
    if (app.prefetchGoodsGroupsCache) {
      // 有缓存直接返回
      return;
    }
    const groups = await getGroupList();
    app.prefetchGoodsGroupsCache = groups;
    const groupsCopy = JSON.parse(JSON.stringify(groups));
    // 先拿前十个分组的数据
    const firstTen = groupsCopy.splice(
      initGroupsFetchIndex,
      initFetchGroupLength
    );
    const fetchGroups = formatGroupsParams(firstTen);
    app.prefetchGoodsListPromise = fetchAllGoodsByGroupIds({
      groupIds: fetchGroups.map((item) => item.groupId),
    });
  },
  async checkWhiteList() {
    const { needSkip } = await checkWhiteList();
    // 如果在跳过获取手机号白名单店铺里, 存一个storage，可以跳过后续的弹框
    if (needSkip) {
      wx.setStorage({
        key: PHONE_DIALOG_KEY,
        data: true,
      });
    }
  },

  async onPullDownRefresh() {
    try {
      await Promise.all([
        // 获取该店铺相关配置
        this.fetchConfigFromKdtId(),
        // 获取全店风格配置
        initGlobalThemeType(this),
        // 刷新扫码点单茶饮排队进度
        this.refreshQueueReminderData(),
        // 刷新开启门店列表，以及判断当前门店是否开启
        this.refreshOpenShops(),
      ]);
    } catch (error) {
      wx.showToast({
        title: '下拉刷新失败，请重试!',
        icon: 'none',
      });
    } finally {
      wx.stopPullDownRefresh();
    }
  },

  // 排队通知组件 刷新进度
  async refreshQueueReminderData() {
    if (this.data.queueReminderConfigResponse.status) {
      const progressBar = this.selectComponent(
        '#shop-banner >>> #progress-banner'
      );
      if (progressBar) {
        return progressBar.refresh();
      }
    }
  },

  async refreshOpenShops() {
    return queryOpenShops().then((openedShops = {}) => {
      const { joinType, subKdtIds } = openedShops;
      this.setData({
        openedShops,
        shopClosed:
          joinType &&
          subKdtIds.length &&
          !~subKdtIds.indexOf(+app.getOfflineKdtId()),
      });
    });
  },

  convertQueryToConfig({ q, query }) {
    const config = { from: query.from || 'normal' };
    const sceneStorageKey = 'shop:scene:last';

    // 配置中没有 scene 的时候从 storage 中获取
    const lastScene = wx.getStorageSync(sceneStorageKey) || 1;

    if (q) {
      /**
       * NOTE: 解析自定义二维码链接中的参数
       * 关联自定义二维码文档：https://developers.weixin.qq.com/miniprogram/introduction/qrcode.html#%E4%BA%8C%E7%BB%B4%E7%A0%81%E8%A7%84%E5%88%99
       * 获取自定义二维码链接的参数, 自定义链接会放在onLoad中的参数`option.q`中，并且是encodeURLComponent后完整路径，
       * 比如：是encodeURLComponent(https://b.yzcdn.cn/retail/2021001185620130?kdt_id=43070480&goods_id=123123123&s=2)
       * 所以需要通过 `decodeURIComponent |> constructParamsFromUrl` 来获取到 `桌码` 和 `场景值` 和店铺 `kdt_id` 的信息
       */
      const {
        kdt_id: kdtId,
        s = 1,
        t = 0,
        ...restParams
      } = constructParamsFromUrl(decodeURIComponent(q));

      Object.assign(config, restParams, {
        scene: +s,
        kdtId: +(kdtId || app.getKdtId() || app.getExtKdtId()),
        tableId: +t,
      });
    } else {
      Object.assign(config, {
        scene: +query.s || lastScene,
        kdtId: +(query.kdt_id || app.getKdtId() || app.getExtKdtId()),
        tableId: query.t,
      });
    }

    // 在有 kdtId 和 scene 的情况下，存储到对应的 storage 中
    if (config.kdtId && config.scene) {
      wx.setStorage({
        key: sceneStorageKey,
        data: config.scene,
      });
    }

    app.globalData.scene = config.scene || lastScene;
    app.globalData.retailConfig = {};

    return config;
  },

  async convertShopInfo(config = this.config) {
    const { kdtId: maybeInvalidKdtId, tableId } = config;
    const offlineKdtId = app.getOfflineKdtId();

    /**
     * 不管店铺有没有升级过(比如：单店升级连锁会把原来单店的kdtid变成总部的kdtid)
     * 只要从24小时货架 进 [扫码点单|自助结账]，因为是总部，所以直接进店铺选择页面。
     * 而不要跑下面的兼容店铺升级的逻辑
     */
    if (!offlineKdtId) {
      if (this.config.from !== 'home-shelf') {
        const { kdtId } = await this.complicateKdtIdForFuckSingleShop(
          maybeInvalidKdtId
        );
        config.kdtId = kdtId;
      } else if (this.shouldNavigateToShopList()) {
        return this.goToShopListPage();
        // 获取不到门店 kdtId，跳转店铺列表
        // return this.goToShopListPage();
      } else {
        // 单店才会走到这个逻辑
        app.updateOfflineKdtId(maybeInvalidKdtId || app.getKdtId());
      }
    }

    /**
     * 以下需保证均通过门店 kdtId 操作
     */
    try {
      const [shopConfig, shopInfo, tableCode, openedShops] = await Promise.all([
        this.fetchConfigFromKdtId(),
        this.getShopInfo(),
        this.getTableCode(tableId),
        queryOpenShops(),
        initGlobalThemeType(this),
      ]);

      app.globalData.retailConfig.shopInfo = shopInfo;
      app.globalData.scanFromTable = !!tableId;

      wx.setNavigationBarTitle({
        title: shopInfo.shopName,
      });
      // 有桌码或扫码点单取餐方式只开启一种，则不弹弹窗
      const { diningIn, takingOut } = shopConfig.mealTakingModeResponse || {};
      const showPopup = !(
        app.globalData.scanFromTable ||
        (isFreeGo() && diningIn ^ takingOut)
      );
      // do render!
      this.setData(
        {
          showSkeleton: false,
          showPopup,
          shopInfo,
          tableCode,
          scene: config.scene,
          openedShops,
        },
        () => {
          const query = wx.createSelectorQuery();
          query
            .select('#shopbanner')
            .boundingClientRect()
            .exec((nodes) => {
              const { height = 60 } = nodes[0] || {};
              this.setData({
                shopHeight: height,
              });
            });
        }
      );
      if (this.shouldNavigateToShopList()) {
        return this.goToShopListPage();
      }
      // 预加载点单页商品
      this.prefetchGoodsData();

      const { joinType, subKdtIds } = openedShops || {};

      if (
        joinType &&
        subKdtIds.length &&
        !~subKdtIds.indexOf(+app.getOfflineKdtId())
      ) {
        this.setData({ shopClosed: true });
        return;
      }

      this.setData({ shopClosed: false });

      // 有桌号且第一次加载，需要执行跳转逻辑
      if (this.data.tableCode && this.firstLoad) {
        Object.assign(app.globalData.retailConfig, {
          deliveryWay: 3,
        });

        this.firstLoad = false;

        // 加餐才去查预订单
        if (app.globalData.retailConfig.isSupportAddMealOpen) {
          // 有桌号状态下，查询预订单
          this.getPreOrderInfo(tableId)
            .then((res) => {
              // 跳转预订单信息页面
              this.gotoPreOrder(res.preOrderNo, res.dbid);
            })
            .catch(() => {
              this.gotoGoodsList();
            });
        } else {
          this.gotoGoodsList();
        }
      }
    } catch (err) {
      wx.showToast({
        title: err.msg || err.message || '获取门店信息失败, 请退出下次重新进入',
        icon: 'none',
        duration: 2000,
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 兼容店铺kdtId, 老代码 勿动
  async complicateKdtIdForFuckSingleShop(maybeInvalidKdtId) {
    return mirgateKdtId(maybeInvalidKdtId).then((id) => {
      return app.updateOfflineKdtId(id);
    });
  },

  fetchConfigFromKdtId() {
    // 根据最终的kdtid去请求店铺配置(如自助结账购物袋配置、扫码点单的必选品配置)，并setData
    const getConfig = cacheFetch(
      fetchAppConfig,
      `appconfig:${app.getOfflineKdtId()}`
    );
    return getConfig(assignMinappConfig.bind(this)).then((res) => {
      assignMinappConfig.bind(this)(res);
      if (isScanGo()) {
        this.setCartEntryInPageTabWhileScanGo();
      }
      return res;
    });
  },

  /**
   * 自助结账：切换成微信扫一扫的需求
   */
  setCartEntryInPageTabWhileScanGo() {
    if (!isScanGo()) return;
    const { switchWxScanResponse, tabs, icons, urls } = this.data;
    const newTab = '购物车';
    const hasCartEntryInPageTab = tabs.find((item) => item === newTab);
    const openWxScanSetting = !!switchWxScanResponse.status;

    /**
     * 1.打开了微信扫一扫的配置
     * 2. 没有添加tabs
     * 3. 不是总部
     */
    if (
      openWxScanSetting &&
      !hasCartEntryInPageTab &&
      !this.shouldNavigateToShopList()
    ) {
      urls.splice(1, 0, SCAN_PAGE_PATH);
      tabs.splice(1, 0, newTab);
      icons.tabs.normal.splice(
        1,
        0,
        'https://b.yzcdn.cn/retail/minapp/icons/tab-cart.svg'
      );
      this.setData({
        tabs,
        icons: { ...icons },
        urls,
      });
      return;
    }

    // 如果商家关闭了配置，但是tabs之前已经加了购物车tab，需要手动去掉
    if (!openWxScanSetting && hasCartEntryInPageTab) {
      urls.splice(1, 1);
      tabs.splice(1, 1);
      icons.tabs.normal.splice(1, 1);
      this.setData({
        tabs,
        icons: { ...icons },
        urls,
      });
    }
  },

  getShopInfo() {
    const handleShopInfo = (shopInfo) => {
      const { address, city, county, distance, shopName, shopRole, rootKdtId } =
        shopInfo;

      const location = city + county + address;
      const isHq = shopRole === 1;

      app.setShopInfo({
        shopLocation: location,
        rootKdtId,
      });

      return {
        location,
        distance,
        logo: cdnImage(app.getShopInfoSync().logo, '!64x64+2x.jpg'),
        shopName,
        isHq,
        isSingle: shopRole === 0,
      };
    };
    return this.fetchShopInfo(
      {
        onCacheRead: handleShopInfo,
        justCacheOne: true,
      },
      false
    ).then(handleShopInfo);
  },

  getTableCode(tableId) {
    const notShowTableCode = !tableId;

    if (notShowTableCode) {
      this.defineRetailConfig({});
      return Promise.resolve('');
    }

    // 始终记录 tableId
    this.defineRetailConfig({ tableId });

    const setTableCode = (tableCode) => {
      if (!tableCode) {
        this.defineRetailConfig({});
        throw Error('no tablecode');
      }
      this.defineRetailConfig({
        tableCode,
        tableId,
      });
      return tableCode;
    };

    return fetchTableCodeWithTableid(tableId)
      .then(setTableCode)
      .catch((err) => {
        wx.showToast({
          icon: 'none',
          title: '获取桌码失败，请联系店家更换桌码',
        });

        app.logger.appError({
          name: 'retail_fetch_table_code_erorr',
          message: '获取桌码失败',
          detail: {
            currentOfflineKdtId: app.getOfflineKdtId(),
            tableId,
            err,
          },
        });
      });
  },

  defineRetailConfig(value) {
    Object.defineProperty(app.globalData.retailConfig, 'tableInfo', {
      value,
      configurable: true,
    });
  },

  onChange({ detail }) {
    const { urls } = this.data;
    if (NEED_CHECKED_URLS.indexOf(urls[detail]) > -1) {
      this.handleCheckMember(() => {
        wx.navigateTo({
          url: urls[detail],
        });
      });
      return;
    }
    wx.navigateTo({
      url: urls[detail],
    });
  },

  openScan() {
    if (this.shouldNavigateToShopList()) {
      return this.goToShopListPage();
    }
    this.handleCheckMember(() => {
      const { switchWxScanResponse } = this.data;
      let url = '/packages/retail/scan-page/index';
      if (switchWxScanResponse.status) {
        url = args.add(url, { switchWxScan: true, directScan: true });
      }
      wx.navigateTo({
        url,
      });
    });
  },

  gotoGoodsList() {
    if (this.shouldNavigateToShopList()) {
      return this.goToShopListPage();
    }
    wx.navigateTo({
      url: '/packages/retailb/goodslist/index',
    });
  },

  handleCheckMember(fn) {
    fn();
  },

  wantToTangshi() {
    if (app.globalData.scanFromTable) {
      wx.vibrateShort();
      return this.gotoGoodsList();
    }
    this.setData({
      showPopup: true,
    });
  },

  closePopup() {
    this.setData({ showPopup: false });
  },

  confirmOrderConfig({ detail: { appointmentConfig, deliveryWay } }) {
    Object.assign(app.globalData.retailConfig, {
      deliveryWay,
      appointmentConfig,
    });
    // 增加一个 200ms 的延迟
    this.confirmDelay = setTimeout(() => {
      this.closePopup();
      this.gotoGoodsList();
    }, 200);
  },

  onTapStep({ currentTarget }) {
    if (this.shouldNavigateToShopList()) {
      return this.goToShopListPage();
    }
    if (
      isFreeGo() &&
      Object.keys(this.data.mealTakingModeResponse).reduce(
        (ret, key) => ret ^ this.data.mealTakingModeResponse[key],
        false
      )
    ) {
      this.confirmOrderConfig({
        detail: {
          appointmentConfig: null,
          deliveryWay: this.data.mealTakingModeResponse.takingOut ? 1 : 3,
        },
      });
      return;
    }
    wx.vibrateShort();
    [this.wantToTangshi][currentTarget.dataset.index]();
  },

  shouldNavigateToShopList() {
    const offlineShopInfo = app.getOfflineShopInfo();

    if (offlineShopInfo?.shopRole === 2) {
      return !offlineShopInfo?.offlineShopOpen;
    }

    // 门店，不跳转列表
    // 这里拿到的就是 isPureOffineShop 这个字段……不是拼错了
    return app.getShopInfoSync()?.chainStoreInfo?.isPureOffineShop === false;
  },

  handleCancelSettingDialog() {
    this.setData({ showSettingDialog: false });
  },

  handleConfirmSettingDialog() {
    this.setData({ showSettingDialog: false });
  },

  goToShopListPage() {
    wx.navigateTo({
      url: '/packages/retail/shop-list/index',
    });
  },

  async getPreOrderInfo(tableId = this.config.tableId) {
    // 有桌号状态下，查询预订单
    const res = await queryPreOrderStatus(tableId);
    if (res?.orderDetailResponseList?.length > 0) {
      // 获取预订单信息
      const preOrderList = res.orderDetailResponseList;
      // eslint-disable-next-line no-restricted-properties
      const preOrderItem = preOrderList.find((item) =>
        [0, 1].includes(item.state)
      );

      const preOrderNo = preOrderItem?.preOrderNo;
      const orderNo = preOrderItem?.orderNo;

      if (orderNo) throw Error('已生成订单'); // 已生成订单，抛出异常，走 catch 逻辑
      if (!preOrderNo) throw Error('未找到预订单'); // 未找到预订单，抛出异常，走 catch 逻辑

      const dbid = app.db.set({ preOrderItem });
      return {
        preOrderNo,
        dbid,
      };
    }
    throw new Error('orderDetailResponseList 为空');
  },
  /**
   * 跳转预订单页面
   * @param preOrderNo
   * @param dbid
   */
  gotoPreOrder(preOrderNo, dbid) {
    wx.navigateTo({
      url: `/packages/retailb/order/pre-detail/index?no=${preOrderNo}&dbid=${dbid}`,
    });
  },
  handleClickOrdered() {
    this.gotoPreOrder(this.data.preOrderNo, this.data.preOrderDBId);
  },
  // 刷新已点按钮显示状态
  refreshOrderedButton() {
    this.getPreOrderInfo(this.config.tableId)
      .then((res) => {
        // 跳转预订单信息页面
        this.setData({
          showOrderedButton: true,
          preOrderNo: res.preOrderNo,
          preOrderDBId: res.dbid,
        });
      })
      .catch(() => {
        this.setData({
          showOrderedButton: false,
        });
      });
  },
});
