@import '../../../pages-retail/common.scss';
@import './shop-go.scss';
@import './icons.scss';

:host {
  --theme-main-color: #1989fa;
}

.step-block {
  position: relative;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme-main-color);
  box-shadow: 0 4rpx 32rpx rgba(125, 126, 128, 0.16);
  margin: 32rpx;
  border-radius: 4rpx;
}

.step-block .title {
  font-size: 40rpx;
  color: var(--theme-main-color);
  margin-bottom: 16rpx;
}

.step-block .subtitle {
  font-size: 28rpx;
  color: #969799;
  line-height: 1.5;
}

.step-icon {
  height: 128rpx;
  width: 128rpx;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-200%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--theme-main-rgb-color);
  border-radius: 100%;
}

.scan-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 240px);
  padding-top: 50px;
}

.scan-container .block-center {
  display: flex;
  align-items: center;
  flex-direction: column;
  flex: 1;
}

.scan-container .bottom-bar {
  width: 100%;
  height: 240rpx;
  display: flex;
  justify-content: center;
}

.bottom-bar .scan-step-img {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/scango-steps.svg);
}

.scan-container .button-highlight {
  opacity: 0.6;
  transform: scale(0.95);
}

.scan-container .scan-icon {
  height: 345rpx;
  width: 345rpx;
  box-sizing: border-box;
  transition: all 0.1s ease-in;
  border-radius: 100%;
  background-color: var(--theme-main-rgb-color);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 150rpx;
}

.scan-icon .inner-scan {
  position: relative;
  height: 320rpx;
  width: 320rpx;
  border-radius: 100%;
  background: var(--theme-main-color);
}

.inner-scan .scan-icon_img {
  position: relative;
  top: -20rpx;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/scan-center-icon.svg);
}

.inner-scan .scan-icon_title {
  position: absolute;
  bottom: 40rpx;
  font-size: 28rpx;
  color: #fff;
  left: 50%;
  transform: translateX(-50%);
}

swiper.pictures {
  height: 320rpx;
  margin: 32rpx;
}

.float-cart {
  position: absolute;
  --radius: 40px;
  background: url(https://su.yzcdn.cn/wsc-minapp/icon/retail/cart.svg) no-repeat;
  background-size: var(--radius) var(--radius);
  background-position: 4px center;
  height: 48px;
  width: 72px;
  border-radius: var(--radius) 0 0 var(--radius);
  right: 0;
  top: 60%;
  transition: width 0.3s;
  box-shadow: 0 2px 16px rgba(125, 126, 128, 0.16);
}

.float-cart .badge {
  position: absolute;
  right: 27px;
  top: 4px;
  border: 1px solid white;
  transform: translate(25%, -25%);
  min-width: 9px;
  height: 16px;
  width: 16px;
  line-height: 18px;
  font-size: 12px;
  border-radius: 12px;
  background-color: #ff4444;
  color: white;
  text-align: center;
}

.ordered-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 4rpx 32rpx rgba(125,126,128,.16);
  position: absolute;
  right: 24rpx;
  .ordered-img {
    width: 40rpx;
    height: 40rpx;
  }
  .ordered-text {
    font-size: 20rpx;
    color: #323233;
  }
}

/* 适配 >= iphonex */
.scan-go-tabbar van-tabbar .van-tabbar--fixed {
  padding-bottom: 34px;
}

.skeleton-container {
  display: flex;
  width: 100vw;
  height: 100vh;
  padding: 30rpx;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  box-sizing: border-box;
  overflow: hidden;
}

@keyframes blink {
  from {
    opacity: 1;
  }
  to {
    opacity: 0.2;
  }
}

.skeleton {
  flex-shrink: 0;
  -webkit-animation: blink 1s infinite alternate;
  animation: blink 1s infinite alternate;
  will-change: opacity;
  border-radius: 10rpx;
  margin-top: 32rpx;
  background: linear-gradient(#fbfbfb, #ebedf0);
}

.tip-skeleton {
  @extend .skeleton;
  width: 100%;
  height: 160rpx;
}

.hover {
  opacity: 0.8;
}

.navigation {
  display: flex;
  background-color: white;
  position: relative;
  margin: 10px 10px 0;
  height: 200px;
  border-radius: 5px;
  box-shadow: 0 0 8px 0 #eee;

  &__step {
    flex: 1;
    height: 100%;
    background-position: center;
    background-size: auto 120px;
    background-repeat: no-repeat;
  }

  &__step--1 {
    background-image: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
  }

  &__step--2 {
    background-image: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
  }

  &__step--3 {
    background-image: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
  }
}

.operations {
  position: fixed;
  display: flex;
  bottom: 0;
  height: 122px;
  width: 100%;
  overflow: hidden;

  &__background {
    position: fixed;
    height: 1000px;
    width: 1000px;
    border-radius: 100%;
    bottom: -875px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    box-shadow: 0 0 8px 0px #eee;
  }

  &__btn {
    position: relative;
    flex: 1;
    margin-top: 45px;

    .text {
      margin-top: 5px;
      text-align: center;
      font-size: 12px;
      color: #999;
    }

    .num {
      position: absolute;
      top: -10px;
      right: 25px;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      font-size: 12px;
      color: #fff;
      background: #f44;
    }

    .icon {
      display: block;
      margin: 0 auto;
      width: 24px;
      height: 24px;
      background-size: 24px 24px;

      &--cart {
        background-image: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
      }

      &--account {
        background-image: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
      }

      &--scan {
        display: flex;
        align-items: center;
        border-radius: 50%;
        width: 54px;
        height: 54px;
        background: linear-gradient(0deg, var(--theme-main-color), #62bdff);
      }

      .logo {
        flex: 1;
        width: 44px;
        height: 44px;
        background-image: url(https://b.yzcdn.cn/wsc-minapp/icon/retail/scan.png);
        background-size: 28px 28px;
        background-repeat: no-repeat;
        background-position: center;
      }
    }

    &--scan {
      margin-top: 10px;
    }
  }
}

.shop-close-tip {
  padding: 20px 10px;
  text-align: center;
  font-size: 32rpx;
}

.shop-select-btn {
  padding: 0 20px 10px;
  text-align: center;
  font-size: 28rpx;
  color: var(--theme-main-color);
}
