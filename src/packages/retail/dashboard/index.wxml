<view
  wx:if="{{showSkeleton}}"
  class="skeleton-container"
>
  <template is="little-skeleton" />
</view>

<block wx:else>
  <page-container
    forbid-copyright
    page-bg-color="#ffffff"
  >
    <shop-banner
      id="shop-banner"
      showProgressBar="{{!!queueReminderConfigResponse.status}}"
      shop-info="{{ shopInfo }}"
      no-switch="{{ true }}"
      bind:shopchange="changeNavigationTitle"
    />

    <block wx:if="{{scene === 2}}">
      <swiper
        class="pictures"
        indicator-dots="{{productPictures.length > 1}}"
        autoplay
        circular
        interval="3000"
        indicator-color="#c8c9cc"
        indicator-active-color="#fff"
        wx:if="{{productPictures.length}}"
        current="{{currentSwiperIndex}}"
      >
        <swiper-item
          wx:for="{{productPictures}}"
          wx:for-item="img"
          wx:key="index"
        >
          <image
            src="{{img}}"
            style="height: 320rpx; width: 100%; border-radius: 8rpx;"
            mode="aspectFill"
            show-menu-by-longpress
            lazy-load
          />
        </swiper-item>
      </swiper>
      <view
        class="step-block"
        bind:tap="onTapStep"
        data-index="{{index}}"
        wx:for="{{routers}}"
        wx:key="index"
        style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
      >
        <text
          class="step-icon iconfont-dao icon-saomadiandantubiao"
          style="color: {{themeMainColor}}"
        />
        <view style="display: flex; flex-direction: column;">
          <text class="title">{{item.title}}</text>
          <text class="subtitle">{{item.subtitle}}</text>
        </view>
      </view>
      <view wx:if="{{showOrderedButton}}" class="ordered-button" bind:tap="handleClickOrdered" style="bottom: {{160 + (isiPhoneX ? 68 : 0)}}rpx">
        <image class="ordered-img" src="https://b.yzcdn.cn/wsc-minapp/icon/retail/ordered-float.svg"></image>
        <view class="ordered-text">已点</view>
      </view>
      <order-config-pop
        bind:close="closePopup"
        bind:submit="confirmOrderConfig"
        take-config="{{takeConfig}}"
        take-style="{{mealTakingModeResponse}}"
        show="{{showPopup}}"
        themeMainColor="{{themeMainColor}}"
        style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
      >
        <shop-banner
          style="--shadow: none;"
          shop-info="{{shopInfo}}"
          is-link="{{showChangeShopSwitch}}"
        />
      </order-config-pop>
    </block>
    <view
      wx:else
      class="scan-container"
      style="padding-bottom: calc(50px + {{isiPhoneX ? 34 : 0}}px); --theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
    >
      <view class="block-center">
        <view class="bottom-bar">
          <view class="scan-step-img" />
        </view>
        <view
          bind:tap="openScan"
          class="scan-icon"
          hover-class="button-highlight"
        >
          <view class="inner-scan">
            <view class="scan-icon_img" />
            <text class="scan-icon_title">扫商品条码</text>
          </view>
        </view>
      </view>
    </view>

    <view
      style="position: absolute; left: 0; right: 0; bottom: 0; height: {{50 + (isiPhoneX ? 34 : 0)}}px;"
      class="{{isiPhoneX ? 'scan-go-tabbar' : ''}}"
    >
      <van-tabbar
        bind:change="onChange"
        active="{{0}}"
        active-color="{{themeMainColor}}"
      >
        <van-tabbar-item
          wx:for="{{tabs}}"
          wx:key="index"
        >
          <image
            slot="icon"
            src="{{icons.tabs.normal[index]}}"
            style="height: 50rpx; width: 50rpx;"
          />
          <text
            slot="icon-active"
            style="height: 50rpx; width: 50rpx; color: {{themeMainColor}}"
            class="iconfont-home icon-Homehomepagemenu"
          />
          {{item}}
        </van-tabbar-item>
      </van-tabbar>
    </view>

    <dialog
      use-slot
      show="{{ shopClosed }}"
      show-confirm-button="{{false}}"
    >
      <view class="shop-close-tip">该门店暂不提供{{scene === 1 ? '自助结账' : '扫码点单'}}服务</view>

      <view
        bind:tap="goToShopListPage"
        class="shop-select-btn"
        style="--theme-main-color: {{themeMainColor}};"
      >选择其他店铺</view>
    </dialog>
  </page-container>
</block>

<template name="little-skeleton">
  <block
    wx:for="12345"
    wx:key="*this"
  >
    <view class="tip-skeleton" />
    <view
      class="tip-skeleton"
      style="height: 400rpx"
    />
  </block>
</template>
