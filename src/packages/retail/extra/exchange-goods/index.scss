.activity-goods-page {
  min-height: 100vh;
  background-color: #EDEDED;
  padding: 32rpx;
  box-sizing: border-box;

  &-wrap {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
  }

  &-item {
    width: 212rpx;
    border-radius: 16rpx;
    background-color: #ffffff;
    margin-bottom: 24rpx;
    margin-right: 25rpx;

    &:nth-of-type(3n) {
      margin-right: 0;
    }
  }

  &-image {
    width: 100%;
    height: 212rpx;
    border-top-right-radius: 16rpx;
    border-top-left-radius: 16rpx;
  }

  &-info {
    padding: 16rpx;
  }

  &-title {
    display: block;
    width: 100%;
    margin-bottom: 8rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: 500;
    line-height: 40rpx;
    color: #323233;
    font-size: 28rpx;
  }

  &-price-wrap {
    display: flex;
    align-items: baseline;
  }

  &-label {
    width: 100%;
    line-height: 32rpx;
    font-size: 20rpx;
    color: #BE2C34;
    white-space: nowrap;
  }

  &-price {
    font-size: var(--price-font-size);
    line-height: 48rpx;
    margin-right: 8rpx;
    white-space: nowrap;
    &.is-exchange {
      color: #BE2C34;
    }
  }

  &-origin-price {
    font-size: 24rpx;
    line-height: 36rpx;
    color: #969799;
    text-decoration-line: line-through
  }
}