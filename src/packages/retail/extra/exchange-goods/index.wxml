<view class="activity-goods-page">
	<view class="activity-goods-page-wrap">
		<view
			wx:for="{{goodsList}}"
			wx:for-item="goods"
			wx:for-index="goodsId"
			class="activity-goods-page-item"
		>
			<image
				wx:if="{{goods.imgUrl}}"
				class="activity-goods-page-image"
				src="{{goods.imgUrl}}"
				mode="aspectFill"
			/>
			<view class="activity-goods-page-info">
				<text class="activity-goods-page-title">{{goods.title}}</text>
				<block wx:if="{{ type === 'exchange' }}">
					<view class="activity-goods-page-price-wrap">
						<text class="activity-goods-page-price is-exchange"
							style="--price-font-size: {{goods.priceFontSize}}rpx">￥{{goods.exchangePrice}}</text>
						<text class="activity-goods-page-label">换购价</text>
					</view>
					<text class="activity-goods-page-origin-price">￥{{goods.originalPrice}}</text>
				</block>
				<block wx:else>
					<view class="activity-goods-page-title" style="font-size: 24rpx">{{ goods.skuDesc }}</view>
					<text 
						class="activity-goods-page-price"
						style="--price-font-size: {{goods.priceFontSize}}rpx">￥{{goods.originalPrice}}</text>
				</block>
			</view>
		</view>
	</view>
</view>