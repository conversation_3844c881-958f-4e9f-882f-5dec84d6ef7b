.list {
  padding: 16px 6px;
  background: #ededed;
  min-height: 100vh;
  overflow-y: auto;
  
  .block {
    display: inline-flex;
    width: 33.33%;
    justify-content: center;
    padding: 0 6px;
    box-sizing: border-box;
  }

  .goods {
    width: 100%;
    height: 146px;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    margin-bottom: 12px;

    .poster {
      width: 100%;
      height: 106px;
      background-color: #c4c4c4;
    }

    .title {
      width: 100%;
      height: 40px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 8px 8px 12px;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      box-sizing: border-box;
    }
  }
}
