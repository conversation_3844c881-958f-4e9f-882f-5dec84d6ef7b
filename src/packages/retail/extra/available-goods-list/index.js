import { getAvailableGoodsList } from './api';

const app = getApp();

Page({
  data: {
    list: [],
  },
  onLoad({ dbid }) {
    const { title, goodsIds, onlineGoodsGroupIds, offlineGoodsGroupIds } =
      app.db.get(dbid); // 可用优惠券列表

    wx.setNavigationBarTitle({
      title,
    });

    getAvailableGoodsList({
      goodsIds,
      onlineGoodsGroupIds,
      offlineGoodsGroupIds,
    }).then((list) => {
      this.setData({
        list,
      });
    });
  },
});
