import WscPage from 'pages/common/wsc-page/index';
import api from './api';

WscPage({
  data: {
    list: [],
    logo: ''
  },

  onShow() {
    api.getSalesList().then(res => {
      const list = res.map(item => {
        // eslint-disable-next-line
        item.mobile = item.mobile ? item.mobile.match(/\+86-/) ? `${item.mobile.slice(0, 3)} ${item.mobile.slice(4, 7)} ${item.mobile.slice(7, 10)} ${item.mobile.slice(10, 14)}` : `${item.mobile.slice(0, 3)} ${item.mobile.slice(3, 7)} ${item.mobile.slice(7, 11)}` : '';
        item.tagsList = item.tags || [];
        return item;
      });
      this.setYZData({
        list
      });
    }).catch(err => {
      console.log(err);
    });
  }
});
