import WscComponent from 'pages/common/wsc-component/index';
import api from '../api';

WscComponent({
  properties: {
    sales: Object
  },

  methods: {
    navigateToBiCard({ currentTarget: { dataset: { sales } } }) {
      wx.navigateTo({
        url: `/packages/retail/extra/sales/bi-card/index?from_params=${sales.from_params}&from_source=${sales.from_source}`
      });
    },

    handleTagsClick({ target }) {
      const { item, index } = target.dataset;
      const { sales_id: salesId, kdt_id: storeKdtId } = this.data.sales;

      const clickTagParams = {
        tag_id: item.tag_id,
        sales_id: salesId,
        store_kdt_id: storeKdtId,
      };
      api
        .clickSalesCardTag(clickTagParams)
        .then(() => {
          const { sales } = this.data;
          const { tagsList } = sales;
          tagsList[index] = Object.assign({}, tagsList[index], {
            has_clicked: true,
            likeAnimationTrigger: true,
            count_click: tagsList[index].count_click + 1
          });
          this.setYZData({
            sales
          });
        })
        .catch(err => {
          wx.showToast({
            title: err.msg,
            icon: 'none'
          });
        });
    }
  }
});
