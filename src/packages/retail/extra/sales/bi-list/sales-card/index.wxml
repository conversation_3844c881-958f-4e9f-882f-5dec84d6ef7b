<view class="sales-card {{ sales.tagsList.length > 0 ? 'sales-card__normalHeight' : 'sales-card__shorterHeight' }}">
  <view class="sales-card__href" bind:tap="navigateToBiCard" data-sales="{{ sales }}">
    <image class="sales-card__logo" src="{{ sales.logo }}" />
    <view class="sales-card__company">{{ sales.company || '' }}</view>
    <view class="sales-card__name">{{ sales.name || '' }}</view> 
    <view class="sales-card__title">{{ sales.job_name || '' }}</view>
    <view class="sales-card__phone">{{ sales.mobile }}</view>
  </view>
  <view class="sales-card__tags">
    <block wx:for="{{ sales.tagsList || [] }}" wx:key="tag_id">
      <van-button wx:if="{{ item.is_show }}" plain type="primary" data-item="{{ item }}" data-index="{{ index }}" bind:click="handleTagsClick" custom-class="sales-card__tags-btn {{ item.has_clicked ? 'card-index--sales-card__tags-btn--disabled' : '' }}" disabled="{{ item.has_clicked }}">
        {{ item.tag_name }}({{ item.count_click > 999 ? '999+' : item.count_click }})
        <view class="sales-card__tags-like {{ item.likeAnimationTrigger ? 'sales-card__tags-like--animation' : '' }}">+1</view>
      </van-button>
    </block>
  </view>
</view>