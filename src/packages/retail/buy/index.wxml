<page-container forbid-copyright page-bg-color="#f2f3f5">
  <view style="position: fixed;width:100%;background-color: #f2f3f5;z-index: 99;">
    <view style="height: {{ statusBarHeight }}px" />
    <view class="custom-nav" style="height:{{ navHeight }}px">
      <view class="custom-nav__left">
        <view class="custom-nav__left--icon" catch:tap="handleBack">
          <van-icon
            class="icon-return"
            name="arrow-left"
            size="22px"
          />
        </view>
      </view>
      <view class="custom-nav__title" />
      <view class="custom-nav__right" />
    </view>
  </view>
  <view style="height: {{ statusBarHeight + navHeight }}px" />

  <!-- 顶部通知栏 -->
  <notify id="van-notify" />

  <!-- 店铺信息 -->
  <order-state
    wx:if="{{scene === 2}}"
    stateDesc="{{ tableCode ? tableCode : shopName }}"
    tableCode="{{tableCode}}"
    dinerNum="{{dinerNum}}"
    showIcon
    iconUrl="{{ tableCode ? 'https://img01.yzcdn.cn/upload_files/2023/05/08/FguR6ab0rP_H_9d6928U7gBxpYyy.png' : 'https://img01.yzcdn.cn/upload_files/2023/04/24/FnIyrRw-yCKfE_jPO86Lf7XQM3Iz.png' }}"
  />

  <order-container
    loading="{{!canPay}}"
    is-order-created="{{isOrderCreated}}"
    navigation-style="black"
    bgColor="#F2F2F2"
  >

    <view class="order-info">
      <!-- 购物袋：仅在自助结账场景下有 -->
      <view wx:if="{{ scene === 1 && hasShoppingBag}}" class="tip-shopping">
        <text class="shop-header">购物袋</text>
        <scroll-view class="shop-container" scroll-x="true">
          <view
            class="bag-content"
            wx:for="{{bags}}"
            wx:key="uniqueKey"
          >
            <view class="shop-bag-item">
              <image
                src="{{item.picUrl}}"
                mode="aspectFill"
                lazy-load
                class="icon"
              />
              <view class="shop-bag-info">
                <view class="shop-bag-desc">
                  <view class="shop-name">{{item.name}}</view>
                  <view>¥ {{util.toPrice(item.price)}}</view>
                </view>
                <view class="shop-bag-btn">
                  <input-number
                    class="bag-input-number"
                    externalShowMinus="{{true}}"
                    value="{{ item.count }}"
                    data-name="{{item.name}}"
                    data-price="{{item.price}}"
                    data-pic-url="{{item.picUrl}}"
                    data-unique-key="{{item.uniqueKey}}"
                    bind:change="handleBagGoodsNumUpdate"
                    style="--offset: 0;--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
                  />
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 就餐方式：仅在扫码点单场景下有 -->
      <van-cell
        wx:if="{{ scene === 2 && !supportAddMeal && (mealTakingModeResponse.diningIn || mealTakingModeResponse.takingOut)}}"
        custom-class="order-cell"
        title-class="order-cell-title"
        title="取餐方式"
        border="{{false}}"
        style="--theme-main-color: {{themeMainColor}}; --cell-horizontal-padding: 24rpx;"
      >
        <view class="takeway-radio-container">
          <van-radio
            wx:if="{{mealTakingModeResponse.diningIn}}"
            bind:change="changeDeliveryWay"
            label-class="{{deliveryWay == 3 ? 'takeway-radio--selected' : ''}} takeway-radio"
            name="3"
            use-icon-slot
          >
            {{mealTakingModeResponse.diningInAlias}}
          </van-radio>
          <van-radio
            wx:if="{{mealTakingModeResponse.takingOut}}"
            bind:change="changeDeliveryWay"
            label-class="{{deliveryWay == 1 ? 'takeway-radio--selected' : ''}} takeway-radio"
            name="1"
            use-icon-slot
          >
            {{mealTakingModeResponse.takingOutAlias}}
          </van-radio>
        </view>
      </van-cell>

      <!-- 预约就餐时间选择卡片，仅在扫码点单场景有 -->
      <block wx:if="{{ !tableCode }}">
        <van-cell
          wx:if="{{ scene === 2 && takeConfig.status !== 0 && !supportAddMeal }}"
          title="取餐时间"
          border="{{false}}"
          value="{{appointmentTimeForUI}}"
          custom-class="order-cell"
          title-class="order-cell-title"
          label-class="order-cell-label"
          is-link
          bind:tap="toggleAppointmentPicker"
          style="--cell-horizontal-padding: 24rpx;--cell-value-color: #333;"
        />
        <!-- 茶饮排队通知：仅在扫码点单场景有，这块逻辑有问题 -->
        <view class="order-progress" wx:if="{{scene === 2 && shouldShowQueueCard && !appointmentTime}}">
          <progress-banner progressType="order" />
        </view>
      </block>
    </view>

    <!-- 支持中途加餐 -->
    <block wx:if="{{supportAddMeal}}">
      <view class="order-title">
        订单明细
      </view>
      <view class="solid-line" />
    </block>

    <!-- 商品列表：扫码点单和自助结账都有 -->
    <!-- allowPayStatus 可以给true 因为唯一的作用是透传到支付弹窗 -->
    <order-goods
      id="order-goods"
      fns="{{fns}}"
      scene="{{scene}}"
      is-done="{{ isOrderCreated }}"
      is-add-meal="{{ supportAddMeal }}"
      is-multi-cart="{{ isMultiCart }}"
      hide-input="{{!showSubmitBar}}"
      shop-name="{{ shopName }}"
      goods-list="{{ goodsList }}"
      comment="{{ comment }}"
      recommend-detail="{{ recommendDetail }}"
      pay-asset-activity-tag-desc="{{ payAssetActivityTagDesc }}"
      showDeliveryTypeDesc="{{ scene === 2 }}"
      showComment="{{scene === 2}}"
      coupons="{{selectedCoupons}}"
      can-choose-coupon="{{canChooseCoupons}}"
      stored-value-and-card="{{storedValueAndCard}}"
      value-card="{{selectedValueCard}}"
      card-not-enough="{{cardNotEnough}}"
      total-price="{{ totalPay }}"
      real-total-price="{{realTotalPrice}}"
      card-price-pay="{{cardPricePay}}"
      promotion-value="{{ promotionValue }}"
      unavailable-goods-list="{{unavailableGoodsList}}"
      stored-discount-is-open="{{storedDiscountIsOpen}}"
      stored-discount-info="{{storedDiscountInfo}}"
      extraPriceDTOS="{{extraPriceDTOS}}"
      can-use-member-benefit="{{canUseMemberBenefit}}"
      member-benefit-desc="{{memberBenefitDesc}}"
      meituan-coupons="{{ meituanCoupons }}"
      theme-color="{{ themeMainColor }}"
      recommend="{{recommend}}"
      allowPayStatus="{{true}}"
      bind:change="handleChangeComment"
      bind:changeMemberBenefit="handleChangeMemberBenefit"
      bind:changeCoupon="handleCouponChange"
      bind:showDiscountDialog="toggleShopDiscount"
      bind:valueCardChange="handleValueCardChange"
      bind:valueCardSubmit="handleValueCardSubmit"
      bind:toggleSubmitBar="toggleSubmitBar"
      bind:changeMeituanCoupons="handleChangeMeituanCoupons"
      bind:chooseValue="handleChooseValue"
      bind:store="handleStore"
      bind:paid-success="handlePaidSuccess"
      bind:rechargeDone="handleRechargeDone"
      isCreated="{{isCreated}}"
      isStoreVip="{{isShowedAllBalancePayModal}}"
      mainColor="{{themeMainColor}}"
      themeMainRgbColor="{{themeMainRgbColor}}"
      isHiddenCard="{{isHiddenCard}}"
      totalDecrease="{{totalDecrease}}"
      totalCurrentPrice="{{totalCurrentPrice}}"
      balanceMkExclusion="{{balanceMkExclusion}}"
      style="--theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
      goodsWrapperStyle="border-radius: {{ supportAddMeal ? '0 0 16rpx 16rpx' : '16rpx' }}"
    >
      <!-- 加价购卡片 -->
      <view slot="plus-buy">
        <plus-buy-warp
          id="plusBuyWarp"
          theme-colors="{{themeColors}}"
          extra="{{extra}}"
          config="{{plusBuyConfig}}"
          goods-list="{{goodsList}}"
        />
      </view>
      <!-- 引导入会：跟随下单页原生化上 -->
      <view slot="member-guide">
        <member-entry
          joinMemberPromotion="{{joinMemberPromotion}}"
          launchArgs="{{memberLaunchArgs}}"
          themeColor="{{themeMainColor}}"
          hostShow="{{!supportAddMeal && !isHiddenCard && !isMultiCart}}"
          member-scene="retailBuy"
          user-authorize-type="separate"
          title="现在入会，本单立省"
          hasBgColor
        />
      </view>
    </order-goods>

  </order-container>

  <activity-pop
    show="{{showDiscount}}"
    activities="{{orderActivities}}"
    total-decrease="{{promotionValue}}"
    bind:closeActivityPop="toggleShopDiscount"
  />

  <member-benefit
    show="{{showMemberBenefit}}"
    member-benefits="{{memberBenefits}}"
    bind:onClose="handleCloseMemberBenefit"
    bind:onSelect="handleSelectMemberBenefit"
  />

  <!--时间选择器：仅在扫码点单场景下有 -->
  <yuyue-picker
    wx:if="{{ scene === 2 }}"
    placeholder="立即取餐"
    take-config="{{takeConfig}}"
    bind:close="changeAppointmentConfig"
    show-popup="{{showPicker}}"
    style="--theme-main-color: {{themeMainColor}}"
  />

  <!-- 底部支付tab：扫码点单和自助结账都有 -->
  <pay-submit-new
    id="pay-submit"
    canPay="{{canPay}}"
    scene="{{scene}}"
    hidden="{{!showSubmitBar}}"
    order-no="{{orderNo}}"
    order-pay-result="{{orderPayResult}}"
    price="{{realTotalPrice}}"
    card-price-pay="{{cardPricePay}}"
    value-card-no="{{selectedValueCard}}"
    is-empty-cart="{{!goodsList || !goodsList.length}}"
    is-skip-pay="{{ config === 1 }}"
    bind:pay="createOrder"
    bind:store="handleStore"
    style="--text-valign: baseline; --theme-main-color: {{themeMainColor}}; --theme-main-rgb-color: {{themeMainRgbColor}}"
    bind:paid-success="handlePaidSuccess"
    support-add-meal="{{ supportAddMeal }}"
    show-price="{{ !supportAddMeal }}"
    total-decrease="{{ totalDecrease }}"
    is-refresh="{{ isRefresh }}"
    payInfo="{{payInfo}}"
  >
    <view
      slot="header"
      class="submit-same-notice"
      wx:if="{{ sameNum > 0 && supportAddMeal }}"
    >
      有 {{ sameNum }} 菜品相同，重复下单了吗?
    </view>
    {{ util.getSubmitText(config, supportAddMeal, payAmount) }}
  </pay-submit-new>

  <wxs module="util">
    // 获取提交的文案名称
    function getSubmitText(config, supportAddMeal, payAmount) {
      if (payAmount) return '立即充值';
      if (supportAddMeal) return '立即下单';
      if (config !== 1) return '立即支付';
      return '立即下单';
    }

    function toPrice(price) {
    price /= 100;
    return price.toFixed(2);
    }

    module.exports = { toPrice: toPrice, getSubmitText: getSubmitText };
  </wxs>
</page-container>
<van-dialog id="van-dialog" />
<van-toast id="van-toast" />
<user-authorize-popup />


