/* eslint-disable @youzan/koko/no-async-await */
import Event from '@youzan/weapp-utils/lib/event';
import Notify from '@vant/weapp/dist/notify/notify';
import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/dist/toast/toast';
import WscPage from 'pages/common/wsc-page/index';
import { isEmpty as isEmptyObject } from '@youzan/tee-util/lib/helper';
import rq from 'retail/util/rq';
import {
  fetchFreeGoCart,
  fetchOrderDetail,
  fetchScanGoCart,
  updateCartData,
  deleteCartData,
  addShopBagToCart,
  addGoodsToCart,
  batchAddGoodsToCart,
} from 'retail/util/api';
import { queryPreOrderStatus } from 'retail/util/api/order';
import {
  getPromotionResult,
  getSelectedCoupons,
  onCouponChange,
  getCanSelectedCoupons,
  getStoredDiscountIsOpen,
  getStoredDiscountPromotion,
} from 'retail/util/ump';
import {
  getSelectedValueCard,
  getStoreValueAndCard,
  getIdsAndPromotion,
  getPrepaid,
} from 'retail/util/value-card';
import { fetchTableCodeWithTableid } from 'retail/util/api/misc';
import { convertOrderConfirmActivities } from 'retail/util/discount';
import { getReatilSource, isScanGo, getCouponList } from 'retail/util/global';
import { hasDiscount, getRecommend } from '../uitls';
import {
  getThemeMainColor,
  getThemeColors,
  getThemeMainRgbColorWithAlpha,
} from 'retail/util/shelf-global';
import { CustomNavBehavior } from 'retail/util/behaviors/customNavBehavior';
import {
  createPreOrder,
  confirmOrder,
  batchUpdate,
  createOrder as callCreateOrder,
  subscriptionCallback,
  getTemplate,
  addRechargeActivity,
  getAddOnGoodsList,
} from './api';
// import plusBuyFunc from './plus-buy';

const app = getApp();
// 核销码的deliveryType
const TAKE_CODE_DELIVERY_TYPE = 4;
// 进桌的cartid是否和下单的id是否一直的log key
const CART_ID_MATCH_LOG_KEY = 'retail:cartId_match_log';

const VIP_STATUS = {
  SATISFY: 1,
  DISSATISFY: 0,
};

function adaptGoods(info) {
  const { promotions = [], ...args } = info;
  let promotionTag = '';
  promotions.forEach(({ type }) => {
    if (+type === 8) {
      promotionTag = '赠品';
    } else if (+type === 24) {
      promotionTag = '换购';
    }
  });
  return {
    ...args,
    promotionTag,
  };
}

const initData = () => ({
  // 扫码点单：自定义取餐方式
  mealTakingModeResponse: app.globalData.retailConfig.mealTakingModeResponse,
  // 扫码点单：是否开启餐饮排队设置
  queueReminderConfigResponse:
    app.globalData.retailConfig.queueReminderConfigResponse,
  // 扫码点单：是否支持加餐
  supportAddMeal: app.globalData.retailConfig.isSupportAddMeal,
  tableCode: app.globalData.retailConfig?.tableInfo?.tableCode,
  scene: app.globalData.scene,
  scanBuyTangshiConfig: app.globalData.retailConfig?.scanBuyTangshiConfig,
  shopName: app.globalData.retailConfig?.shopInfo?.shopName,
  takeConfig: app.globalData.retailConfig?.takeConfig,
  isMultiCart: app.globalData.retailConfig.isMultiCart,
});

WscPage({
  behaviors: [CustomNavBehavior],
  data: {
    themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
    themeMainColor: getThemeMainColor(),
    themeColors: getThemeColors(),
    canPay: false,
    hasAppointment: false,
    showPicker: false,
    shopAddress: '',
    appointmentTime: '',
    appointmentTimeForUI: '立即取餐',
    unavailableGoodsList: [],
    selectedCoupons: [],
    // 当前选择的储值卡
    selectedValueCard: [],
    comment: '',
    isOrderCreated: false,
    totalPay: 0,
    showSubmitBar: true,
    orderActivities: [],
    promotionValue: 0,
    storedDiscountIsOpen: false,
    storedDiscountInfo: {},
    orderNo: '',
    orderPayResult: null,
    icons: {
      selected: 'https://b.yzcdn.cn/wsc-minapp/icon/retail/selected.svg',
      unselected: 'https://b.yzcdn.cn/wsc-minapp/icon/retail/no-selected.svg',
    },
    hasShoppingBag: false,
    // 自助结账是否开启核销功能
    isOpenTakeCode: false,
    confirmBuySku: false,
    // 扫码点单：用户所选商品是否在茶饮排队设置的分组中
    shouldShowQueueCard: false,
    // 额外费用  比如打包费
    extraPriceDTOS: [],
    extra: {},
    // 订阅消息模板id
    templateIdList: [],
    templateIdListLoaded: false,
    goodsList: app.globalData?.cartItemsDetailCache || [],
    showMemberBenefit: false,
    memberBenefits: [],
    isRefresh: false,
    plusBuyConfig: {},
    ...initData(),
    fastJoinShow: false,
    memberLaunchArgs: {},
    recommend: {},
    payInfo: {},
    isHiddenCard: false,
    isShowedAllBalancePayModal: false,
    balanceMkExclusion: false,
    fns: {},
    isCreated: false,
  },

  isBuyPage: true,
  _isPaying: false,
  isShowedAllBalancePayModal: false,

  onShow() {
    app.logger && app.logger.pageShow();
    // 就餐人数
    this.handleDinerNum();

    this.setData({
      themeColors: getThemeColors(),
      themeMainColor: getThemeMainColor(),
      themeMainRgbColor: getThemeMainRgbColorWithAlpha(0.1),
      tableCode: getApp().globalData.retailConfig?.tableInfo?.tableCode,
    });

    this.setData(initData());
  },

  onHide() {
    app.logger && app.logger.pageHide();
  },

  onLoad({ config, dbid }) {
    // defaultOption: 1 堂食 2 外带
    // deliveryWay:  1 外带 3 堂食
    // junjie: 默认值从接口取  由于需求迭代后面这个值的意思有变更，如果改的话牵扯到多个接口，故做一下映射
    // chaoge: 大餐饮行业管理，堂食外带开关没有开启的情况下，defaultOption 不返回，默认走堂食，没有打包费。
    let deliveryWayDefault = 3;
    if (
      !this.data.supportAddMeal &&
      app.globalData.retailConfig.mealTakingModeResponse.defaultOption === 2
    ) {
      deliveryWayDefault = 1;
    }

    const { appointmentConfig, deliveryWay = deliveryWayDefault } =
      app.getRetailConfig();
    const { cartId, cartItems, uniqueId, orderCachePromise } = app.db.get(dbid);

    this._orderCachePromise = orderCachePromise;
    this.setData({
      config: Number(config) || 0,
      deliveryWay,
      ...this.pickAppointmentConfig(appointmentConfig),
      fns: { renderOrder: this.handleValueCardChange.bind(this) },
      memberLaunchArgs: {
        onSuccess: () => {
          app.globalData.isMemberFromOrder = true;
          this.fetchOrderDetail(this.cartId, this.cartItems);
        },
      },
    });
    this.cartId = cartId;
    this.uniqueId = uniqueId;
    this.cartItems = cartItems;
    this._useStoredCard = true;
    this._needHelpSelectCard = true; // 是否需要后端默认选中储值卡
    this._changeCouponTips = false; // 操作优惠券标识,如果有操作传true
    this.setPlusBuyConfig(cartId);

    this.fetchOrderDetail(cartId, cartItems);
    this.unregister = this.registerCouponChangeListener();
    // 储值卡选择listener
    // this.unregisterValueCard = this.registerValueCardChangeListener();
    this.unregisterGoodsList = this.registerGoodslistUpdate();

    this.handleUnavailableGoodsClear = this.getFieldFromEvent(
      'unavailableGoodsList',
      []
    );
    this.handleChangeComment = this.getFieldFromEvent('comment');

    this.refreshCart = rq(
      isScanGo() ? fetchScanGoCart : fetchFreeGoCart,
      400,
      () => {}
    );

    this.getTemplateInfo();
  },

  onUnload() {
    this.isUnload = true;
    this.unregister();
    this.unregisterGoodsList();
    // 取消监听储值卡选择
    // this.unregisterValueCard();
    this.handleUnavailableGoodsClear = null;
    this.handleChangeComment = null;
    clearTimeout(this.fetchOrderDetailTimer);
    clearTimeout(this.failFetchOrderDetailTimer);
  },

  handleFastJoinShow() {
    this.setData({
      fastJoinShow: true,
    });
  },
  handleFastJoinHide() {
    this.setData({
      fastJoinShow: false,
    });
  },
  handleChooseValue(item) {
    const { detail } = item;
    this.isChooseValue = !!detail.amount;
    this.setYZData({
      payInfo: detail,
    });
  },

  getTemplateInfo() {
    return getTemplate().then(({ templateIdList }) => {
      this.setData({
        templateIdList,
        templateIdListLoaded: true,
      });
    });
  },
  // 设置换购配置
  setPlusBuyConfig(cartId) {
    this.setData({
      plusBuyConfig: {
        addGoods: addGoodsToCart,
        deleteGoods: deleteCartData,
        batchAddGoods: batchAddGoodsToCart,
        getPulBuyGoods: getAddOnGoodsList,
        refreshOrder: this.refreshCartEvent.bind(this),
        getCartInfo: this.getCartInfo.bind(this),
        skyLog: this.skyLog.bind(this),
        hide: this.data.supportAddMeal,
        cartId,
      },
    });
  },
  // 加价购
  // handlePlusBuy(formatGoodsList, extra) {
  //   const componentInstants = this.selectComponent(`#plusBuyWarp`);
  //   componentInstants.$init(formatGoodsList, extra);
  // },
  setDinerNum(dinerNum) {
    dinerNum && this.setYZData({ dinerNum });
  },
  handleDinerNum() {
    // 获取桌号
    this.queryPreOrderStatus().then((res) => {
      this.setDinerNum(res?.preOrderItem.dinerNum);
    });
  },
  queryPreOrderStatus() {
    const { tableInfo: { tableId = '' } = {} } = app.getRetailConfig();

    if (!tableId) {
      Promise.reject(new Error('桌号不存在'));
    }

    return queryPreOrderStatus(tableId).then((res) => {
      if (res?.orderDetailResponseList?.length > 0) {
        // 跳转预订单详情
        const preOrderList = res.orderDetailResponseList;
        const preOrderItem = preOrderList.find((item) =>
          [0, 1].includes(item.state)
        );
        const preOrderNo = preOrderItem?.preOrderNo;
        if (!preOrderNo) throw Error('未找到预订单');

        const dbid = app.db.set({ preOrderItem, uniqueId: this.uniqueId });

        return { preOrderNo, dbid, preOrderItem };
      }
    });
  },

  getCartInfo() {
    return this.refreshCart(isScanGo() ? '' : this.cartId);
  },

  refreshCartEvent() {
    return this.getCartInfo().then((res) => {
      const { goodsList, cartId } = res;
      this.cartItems = goodsList.map((item) => item.cartItemId).filter(Boolean);
      this.cartId = cartId;
      return this.fetchOrderDetail(this.cartId, this.cartItems);
    });
  },

  pickAppointmentConfig(config) {
    return {
      appointmentTimeForUI: !config
        ? '立即取餐'
        : `${config.date.value} ${config.time}`,
      appointmentTime: !config
        ? ''
        : `${config.date.dateIncludesYear} ${config.time}`,
    };
  },

  toggleAppointmentPicker() {
    this.setData({
      showPicker: !this.data.showPicker,
    });
  },

  changeDeliveryWay({ detail }) {
    if (detail === this.data.deliveryWay) return;
    app.logger &&
      app.logger.log({
        et: 'click',
        ei: 'change_type',
        en: '更换就餐方式',
        pt: 'buy',
        params: {
          type: detail,
        },
      });
    this.setData({
      deliveryWay: detail,
    });
    this.fetchOrderDetail(this.cartId, this.cartItems);
  },

  changeAppointmentConfig({ detail }) {
    this.setData({
      showPicker: false,
      ...this.pickAppointmentConfig(detail),
    });
  },

  checkShowedAllBalancePayModal(bizExtraMap) {
    // 多人购物车 暂不支持选择放弃优惠
    const { isMultiCart } = this.data;
    if (isMultiCart) return;

    // 订单享有储值会员折扣
    // 订单非储值余额全额支付
    const { VALUE_CARD_LEVEL_GROUP_TYPE, VALUE_CARD_ONLY_FULL_PAY } =
      bizExtraMap;
    this.isShowedAllBalancePayModal =
      +VALUE_CARD_LEVEL_GROUP_TYPE === VIP_STATUS.SATISFY &&
      +VALUE_CARD_ONLY_FULL_PAY === VIP_STATUS.DISSATISFY;
  },

  // exclusionCardList: 存放储值卡的信息
  // hasPromotion: 是否使用优惠
  async fetchOrderDetail(
    cartId,
    cartItems,
    prepaidCardList = [],
    hasPromotion
  ) {
    let clearLoadingDelay = 0;
    try {
      this.setData({
        isRefresh: true,
      });
      const { deliveryWay: deliveryType, supportAddMeal } = this.data;
      const { needRecommendMemberBenefit, needRecommendCoupon } = this;
      let coupons = [];
      if (Array.isArray(this.coupons)) {
        coupons = [...this.coupons];
      }
      if (this._meituanCoupons) {
        coupons.push(...this._meituanCoupons);
      }
      let request = this._orderCachePromise;
      if (!request) {
        request = confirmOrder({
          uniqueId: this.uniqueId,
          cartId,
          cartItems,
          deliveryType,
          prepaidCardList,
          // 多人购物车默认 hasPromotion true
          setHasPromotion: supportAddMeal,
          hasPromotion,
          coupons,
          // 多人购物车默认 false
          needRecommendCoupon: supportAddMeal ? false : needRecommendCoupon,
          showCustomerAllCoupon: this._changeCouponTips, // 展示买家的所有优惠券
          needHelpSelectCard: this._needHelpSelectCard,
          supportAddMeal,
          filterPreOrdered: true,
          // 多人购物车默认 false
          needRecommendStoredDiscount: supportAddMeal
            ? false
            : this._useStoredCard,
          needRecommendMemberBenefit: supportAddMeal
            ? false
            : needRecommendMemberBenefit,
          selectedMemberBenefit: this.handleTransferMemberBenefitParam(),
        });
      }
      const orderData = await request;
      this._orderCachePromise = null;

      const {
        shopInfo,
        goodsList = [],
        unavailableGoodsList = [],
        displayTotalCurrentPrice, // 加餐显示价格
        totalCurrentPrice, // 小计
        totalPrice, // 合计 = 小计 - 使用的储值余额
        usedCustomerPayCardBalance, // 使用的储值余额
        promotion,
        storedDiscountInfo,
        payCardDTOList = [],
        extraPriceDTOS = [],
        autoRecommendAndTake = [],
        message,
        totalDecrease = 0,
        dinerNum,
        memberBenefits = [],
        hasOrdered,
        orderNo,
        bizExtraMap,
        balanceMkExclusion,
        extra = {}, // 扩展数据
        thirdPromotion = [], // 三方券。现在只有美团券
        joinMemberPromotionDTO = {}, // 引导入会数据
      } = orderData;

      if (message) {
        wx.showToast({
          title: message,
          icon: 'none',
        });
        clearLoadingDelay = 1500;
      }

      if (bizExtraMap) {
        this.checkShowedAllBalancePayModal(bizExtraMap);
      }

      if (hasOrdered && orderNo) {
        // 当前订单已经创建
        // eslint-disable-next-line @youzan/dmc/wx-check
        wx.redirectTo({
          url: `/packages/retail/order/detail/index?cartId=${cartId}&orderNo=${orderNo}`,
        });
        return;
      }

      if (autoRecommendAndTake.length) {
        wx.showToast({
          title: `自动领取${autoRecommendAndTake.length}张优惠券`,
          icon: 'none',
        });
        clearLoadingDelay = 1500;
      }

      this.cartItems = goodsList.map((item) => item.cartItemId);
      const formatGoodsList = goodsList.map(adaptGoods);

      this.handleSkuError(unavailableGoodsList, cartId);
      const couponList = getCouponList(promotion);
      // 过滤满减送权益
      this.couponList = couponList;
      // 储值卡
      this.valueCardList = payCardDTOList;

      if (isScanGo()) {
        const {
          shoppingBagQueryResponse: { list = [], status },
          scanVerificationConfigResponse,
        } = app.globalData.retailConfig;
        if (status) {
          this.setData({ hasShoppingBag: list.length > 0 });
          this.initShoppingBagData(list, goodsList);
        }
        if (scanVerificationConfigResponse) {
          this.setData({
            isOpenTakeCode: !!scanVerificationConfigResponse.status,
          });
        }
      }
      const valueCard = getSelectedValueCard(payCardDTOList);
      const storedValueAndCard = getStoreValueAndCard(payCardDTOList);
      this.setDinerNum(dinerNum);
      this.coupons = couponList.filter((coupon) => coupon.isRecommend);
      const selectMemberBenefit = memberBenefits.find(
        (benefit) => benefit.isRecommend
      );
      this.memberBenefit = selectMemberBenefit;
      this.setYZData(
        {
          recommend: getRecommend(orderData),
          balanceMkExclusion,
          isShowedAllBalancePayModal: this.isShowedAllBalancePayModal,
          shouldShowQueueCard: this.shouldShowQueueNotifyCard(goodsList),
          sameNum: this.getSameItems(goodsList) || 0,
          goodsList: formatGoodsList,
          totalCurrentPrice,
          totalPay: supportAddMeal
            ? displayTotalCurrentPrice
            : totalCurrentPrice,
          realTotalPrice: supportAddMeal
            ? displayTotalCurrentPrice
            : totalPrice, // 实际支付金额
          cardPricePay: usedCustomerPayCardBalance, // 储值卡消费
          orderActivities: convertOrderConfirmActivities(promotion),
          promotionValue: getPromotionResult(promotion),
          storedDiscountIsOpen: getStoredDiscountIsOpen(storedDiscountInfo),
          storedDiscountInfo,
          storedDiscountPromotion: getStoredDiscountPromotion(promotion),
          selectedCoupons: supportAddMeal ? [] : getSelectedCoupons(couponList),
          canChooseCoupons: supportAddMeal
            ? false
            : !!getCanSelectedCoupons(couponList).length,
          storedValueAndCard: supportAddMeal ? {} : storedValueAndCard,
          // 当前选择的储值卡
          selectedValueCard: supportAddMeal ? [] : valueCard,
          // 储值卡余额不足
          // cardNotEnough: !valueCard.length,
          shopName: shopInfo.shopName,
          shopAddress: shopInfo.storiesAddress,
          unavailableGoodsList,
          canPay: true,
          extraPriceDTOS, // 一些额外费用 比如打包费
          extra,
          totalDecrease,
          memberBenefits,
          canUseMemberBenefit: memberBenefits.length > 0,
          memberBenefitDesc: selectMemberBenefit?.name ?? '',
          meituanCoupons: thirdPromotion,
          isHiddenCard: hasDiscount(promotion),
          joinMemberPromotion: joinMemberPromotionDTO || {},
        },
        () => {
          // 拿到真实数据后清除缓存
          app.globalData.cartItemsDetailCache = [];
        }
      );
    } catch (err) {
      console.clear();
      console.log(err);
      this.skyLog({
        message: `fetchOrderDetail执行失败`,
        detail: {
          errMessage: err?.message || '',
        },
      });
      wx.showModal({
        content: err?.msg || '订单获取失败，请返回重试',
        showCancel: false,
        success() {
          wx.navigateBack();
        },
      });
    } finally {
      wx.stopPullDownRefresh();
      setTimeout(() => {
        wx.hideLoading();
      }, clearLoadingDelay);
      this.setData({
        isRefresh: false,
      });
    }
  },

  skyLog(options) {
    const { message, level = 'error', detail } = options;
    app.logger.skynetLog({
      appName: 'retail-node-h5',
      message: '[retail-buy]' + message,
      level,
      detail,
    });
  },

  /**
   * 获取同一个菜品的添加人超过 1 个的项
   *
   * @param {Array<T>} items
   */
  getSameItems(items) {
    return items.filter((item) => {
      return item.productBuyers?.length > 1;
    }).length;
  },

  async handleSkuError(unavailable, cartId) {
    const { supportAddMeal } = this.data;
    const { params, errorGoodsNames } = unavailable.reduce(
      ({ params, errorGoodsNames }, item) => {
        const { name, cartItemId, stockNum } = item;

        errorGoodsNames.push(name);
        params.push({
          cartItemId,
          num: stockNum / 1000,
        });
        return {
          params,
          errorGoodsNames,
        };
      },
      { params: [], errorGoodsNames: [] }
    );
    unavailable = unavailable.filter((item) => item.invalidKey !== 4); // 过滤掉库存不足的商品

    if (errorGoodsNames.length) {
      // 如果存在库存不足的商品
      try {
        await batchUpdate(params);
        const {
          goodsList,
          promotion,
          payCardDTOList = [],
          usedCustomerPayCardBalance,
          totalCurrentPrice,
          totalPrice,
        } = await confirmOrder({
          cartId,
          cartItems: this.cartItems.concat(
            unavailable.map((item) => item.cartItemId)
          ),
          needRecommendStoredDiscount: supportAddMeal
            ? false
            : this._useStoredCard,
        });

        // 过滤满减券权益
        const couponList = getCouponList(promotion);

        this.couponList = couponList;
        this.valueCardList = payCardDTOList;

        this.setData({
          shouldShowQueueCard: this.shouldShowQueueNotifyCard(goodsList),
          goodsList,
          selectedCoupons: getSelectedCoupons(couponList),
          // 当前选择的储值卡
          selectedValueCard: this.getSelectedValueCard(payCardDTOList),
          totalPay: totalCurrentPrice,
          realTotalPrice: totalPrice,
          cardPricePay: usedCustomerPayCardBalance,
          unavailableGoodsList: unavailable,
          isHiddenCard: hasDiscount(promotion),
        });
        wx.showModal({
          content: `商品“${errorGoodsNames.join(
            '、'
          )}”库存不足，系统已为您更新可购买数量`,
          showCancel: false,
          confirmText: '知道了',
        });
      } catch (err) {
        wx.showToast({
          icon: 'none',
          title: err.msg ?? '数量更新失败',
        });
      }
    }
  },

  registerGoodslistUpdate() {
    const onGoodslistUpdate = (item) => {
      if (item.content) {
        Notify({
          message: item.content,
          duration: 2000,
          selector: '#van-notify',
          background: '#EAF4FE',
          color: '#1989FA',
          safeAreaInsetTop: true,
        });
      }
      fetchFreeGoCart(this.cartId).then(
        ({ goodsList }) => {
          this.cartItems = goodsList.map((item) => item.cartItemId);
          if (this.cartItems.length) {
            this.fetchOrderDetail(this.cartId, this.cartItems);
          } else {
            this.setData({
              goodsList: [],
              totalPay: 0,
              canPay: false,
            });
          }
        },
        (err) => {
          Notify({
            message: err?.msg ?? '更新购物车失败',
            safeAreaInsetTop: true,
          });
        }
      );
    };

    Event.on('SYNC_MULTI_CART', onGoodslistUpdate);
    return () => Event.off('SYNC_MULTI_CART', onGoodslistUpdate);
  },

  registerCouponChangeListener() {
    const onOrderCouponChange = (coupons) => {
      const { selectedValueCard } = this.data;
      const map = {};
      selectedValueCard.forEach((item) => {
        map[item.summaryCardNo] = item;
      });
      const { cardIds, isExclusion } = getIdsAndPromotion(map);
      this._changeCouponTips = true;
      this._needHelpSelectCard = true;
      this.needRecommendCoupon = false;
      this.coupons = coupons;
      this.fetchOrderDetail(this.cartId, this.cartItems, cardIds, !isExclusion);
    };
    const eventName = 'order-coupon-change';
    Event.on(eventName, onOrderCouponChange);
    return () => {
      Event.off(eventName, onOrderCouponChange);
    };
  },

  toggleSubmitBar(event) {
    this.setYZData({
      showSubmitBar: event.detail,
    });
  },

  toggleShopDiscount() {
    this.setData({
      showDiscount: !this.data.showDiscount,
    });
  },

  handleChangeMemberBenefit() {
    this.setData({
      showMemberBenefit: true,
    });
  },

  handleCloseMemberBenefit() {
    this.setData({
      showMemberBenefit: false,
    });
  },

  handleSelectMemberBenefit({ detail }) {
    // 下面这段代码我也不知道干什么的，感觉没有会出问题，从上面抄来的，有问题请去问扫码点单一哥
    const { selectedValueCard } = this.data;
    const map = {};
    selectedValueCard.forEach((item) => {
      map[item.summaryCardNo] = item;
    });
    const { cardIds, isExclusion } = getIdsAndPromotion(map);
    this._changeCouponTips = true;
    this._needHelpSelectCard = true;
    this.memberBenefit = detail;
    this.needRecommendMemberBenefit = !!detail;
    this.setData({
      showMemberBenefit: false,
    });
    this.fetchOrderDetail(this.cartId, this.cartItems, cardIds, !isExclusion);
  },

  handleTransferMemberBenefitParam() {
    const { memberBenefit } = this;
    if (!memberBenefit) return;
    const { type: memberType, extraMap = {} } = memberBenefit;
    return {
      memberType,
      ...extraMap,
    };
  },

  handleCouponChange() {
    onCouponChange(this.couponList, this.data.selectedCoupons);
  },

  getFieldFromEvent(key, defaultValue) {
    return ({ detail: { value = defaultValue } }) => {
      this.setData({ [key]: value });
    };
  },

  adaptUnavailable(list) {
    return list.map(({ name, amount, picUrl, sku, unavailableReason }) => ({
      title: name,
      num: amount,
      imgUrl: picUrl,
      sku,
      desc: unavailableReason,
    }));
  },

  // 扫码点单是否要展示茶饮排队通知卡片
  shouldShowQueueNotifyCard(goodsList = []) {
    const {
      queueReminderConfigResponse: {
        queueReminderConfigDetails = [],
        status = 0,
      } = {},
    } = app.globalData.retailConfig;
    if (status) {
      return goodsList.some(({ goodsGroupId }) =>
        queueReminderConfigDetails.some(
          ({ groupId: queueGroupId }) => queueGroupId == goodsGroupId
        )
      );
    }
    return false;
  },

  // 实际的创建订单逻辑
  async doCreateOrder(storeThenPay, cardNo) {
    const {
      selectedCoupons = [],
      comment,
      scene,
      deliveryWay,
      appointmentTime,
      isOpenTakeCode,
      selectedValueCard,
      storedDiscountPromotion,
      storedDiscountIsOpen,
      cardPricePay,
      totalCurrentPrice,
      isHiddenCard,
      recommend,
      balanceMkExclusion,
    } = this.data;
    const { tableInfo: { tableId = '', tableCode = '' } = {} } =
      app.getRetailConfig();

    wx.getStorage({
      key: CART_ID_MATCH_LOG_KEY,
      success: (res) => {
        if (res.data !== '' && res.data !== this.cartId) {
          this.skyLog({
            message: `下单cartId不一致 enter_cartId: ${res.data}, pay_cartId: ${this.cartId}, is_match: false`,
            level: 'info',
          });
        }
        wx.removeStorage({
          key: CART_ID_MATCH_LOG_KEY,
        });
      },
    });

    const coupons = [...selectedCoupons];
    if (this._meituanCoupons) {
      coupons.push(...this._meituanCoupons);
    }

    const postData = {
      deliveryType: isOpenTakeCode ? TAKE_CODE_DELIVERY_TYPE : deliveryWay,
      orderCreateType: scene,
      business: scene,
      promotionIds: coupons.map((item) => item.id),
      cartId: this.cartId,
      uniqueId: this.uniqueId,
      cartItems: this.cartItems,
      buyerRemark: comment,
      storedDiscountPromotionId:
        storedDiscountIsOpen && this._useStoredCard
          ? storedDiscountPromotion?.id
          : null,
      prepaid: getPrepaid({
        selectedValueCard,
        cardPricePay,
        storeThenPay,
        balanceMkExclusion,
      }),
      receiverPhone: app.getMobile(),
      source: getReatilSource(),
      confirmPayment: totalCurrentPrice,
      storeThenPay,
    };

    if (storeThenPay) {
      postData.storeAndPayDTO = {
        storeThenPay,
        mutex: balanceMkExclusion,
        prepaidCardList: [{ cardNo }],
      };
    }

    if (appointmentTime) {
      Object.assign(postData, {
        userTime: appointmentTime,
        receiverPhone: app.getMobile(),
      });
    }

    if (tableId) {
      Object.assign(postData, {
        consumeAddressId: tableId,
        // 没有 tableCode 的时候再根据 tableId 查询一次
        consumeAddress: tableCode || (await fetchTableCodeWithTableid(tableId)),
      });
    }

    // 有储值优惠商品时不要
    if (isHiddenCard) {
      const instance = this.selectComponent('#order-goods');
      const { rechargeNo } = instance;

      postData.rechargeNo = rechargeNo;
      postData.needRecommendCouponThenTake = false;
    } else {
      postData.selectedMemberBenefit = this.handleTransferMemberBenefitParam();
    }

    return callCreateOrder({ orderData: postData }).then(
      (result) => {
        const {
          hasOrdered,
          orderNo,
          orderPayResult = {},
          isNeedStoreCardPay = true,
          payCardDTOList,
          totalDecrease,
        } = result;

        // hasOrdered = true;
        if (hasOrdered && orderNo) {
          // 当前订单已经创建
          // eslint-disable-next-line @youzan/dmc/wx-check
          wx.redirectTo({
            url: `/packages/retail/order/detail/index?cartId=${this.cartId}&orderNo=${orderNo}`,
          });
          return;
        }
        const payload = {
          isCreated: true,
        };
        // 充值并支付的创单不需要更新这些
        if (!storeThenPay) {
          Object.assign(payload, {
            orderNo,
            orderPayResult: { ...orderPayResult, isNeedStoreCardPay },
            isOrderCreated: true,
            totalDecrease,
            storedValueAndCard: getStoreValueAndCard(payCardDTOList),
          });
        }
        this.setData({ ...payload });
        this._isPaying = false;
        return result;
      },
      (err) => {
        const error = this.formatErrorInfo(err);
        this.setData({
          orderPayResult: { error },
        });
        this._isPaying = false;
      }
    );
  },

  // 创建预订单逻辑
  doCreatePreOrder() {
    const { comment } = this.data;
    createPreOrder(this.cartId, this.cartItems, comment)
      .then(() => {
        // 预订单创建成功后，跳转预订单详情
        return this.queryPreOrderStatus().then((res) => {
          const { preOrderNo, dbid } = res;
          wx.showToast({
            title: '下单成功',
            icon: 'success',
            duration: 3000,
            success: () => {
              // 这个没用
            },
          });

          setTimeout(() => {
            wx.redirectTo({
              url: `/packages/retail/order/pre-detail/index?no=${preOrderNo}&dbid=${dbid}`,
            });
          }, 3000);
        });
      })
      .catch((err) => {
        const msg = this.formatErrorInfo(err);
        Toast(msg || '预订单创建失败，请稍候重试');
        // Notify({
        //   message: msg || '预订单创建失败，请稍候重试',
        //   duration: 2000,
        //   selector: '#van-notify',
        //   background: '#EAF4FE',
        //   color: '#1989FA',
        //   safeAreaInsetTop: true,
        // });
      })
      .finally(() => {
        this._isPaying = false;
      });
  },

  formatErrorInfo(err) {
    let { msg } = err;
    // 起购限售报错文案，需要兼容各端逻辑，后端无法直接报错
    try {
      if (+err.code === 233003024) {
        const errInfoList = JSON.parse(err.msg).errData || [];
        const _len = errInfoList.length;
        const {
          title,
          limitedReasons: [{ reason }],
        } = errInfoList[0] || {};

        msg = `【${title}】${_len > 1 ? '等' + _len + '件商品' : ''}${reason}`;
      }
    } catch (error) {
      //
    }

    return msg;
  },

  onPullDownRefresh() {
    const { cartId, cartItems } = this;
    wx.showLoading();
    this.fetchOrderDetail(cartId, cartItems);
  },

  // 要不要再错误的时候刷新页面？
  // pullDownRefresh() {
  //   wx.startPullDownRefresh();
  // },

  handleStore() {
    /**
     * 充值并支付的链路 选择档位后 更新底部bar 点击充值将会触发store事件
     * 1. 创建订单拿到E单号
     * 2. 使用E单号调用prepay拿到R单号
     * 3. 支付传入R单号
     */
    const instance = this.selectComponent('#order-goods');
    const { isRecommend, isDiscount, newRecommend, cardNo } = instance;

    // 没有cardNo就是没开卡 可以调用order-goods的injectCardNo进行开卡
    const checkCardNo = cardNo
      ? Promise.resolve.bind(Promise, cardNo)
      : instance.injectCardNo.bind(instance);

    checkCardNo().then((n) => {
      // 储值推荐
      if (isRecommend) {
        // 充值并支付需要先创建订单并切拿到E单号 先充后付不需要
        const Fn = newRecommend
          ? this.doCreateOrder.bind(this)
          : Promise.resolve.bind(Promise, {});

        Fn(true, n).then((result) => {
          const { orderNo } = result;

          instance.handlePay({ orderNo });
        });
      }

      /**
       * 储值优惠是通过往购物车里加一个用来打折的商品来实现
       * 流程：充值 => 获取减钱的商品 => 加入购物车
       * 所以应该直接进行充值
       */
      if (isDiscount) {
        // 储值优惠
        instance.handlePay();
      }
    });
  },

  /**
   * 充值完成
   * 1. 获取减商品
   * 2. 添加到购物车
   * 3. 重新计算价格
   */
  handleRechargeDone() {
    const instance = this.selectComponent('#order-goods');
    const { rechargeNo } = instance;

    return addRechargeActivity({
      rechargeNo,
      cartId: this.cartId,
      source: getReatilSource(),
      adminId: app.getBuyerId(),
    })
      .then((result) => {
        if (result.value) {
          /**
           * 1. 添加折扣商品
           * 2. 通过order-goods储值卡数据变更逻辑重新confirm
           *
           * 先吃后付模式 储值优惠充值完直接将订单确认
           * 解决办法：
           * 1. 通知order-goods卡变更时传个标
           * 2. 重新confirm完成后直接确定订单
           */
          instance.handleSelectCard(true);
        }
      })
      .catch(() => {});
  },

  /**
   * 创建订单
   */
  createOrder(e) {
    // 前置请求判断 是否开启 余额全额支付时可享受会员优惠
    if (this.isShowedAllBalancePayModal) {
      const { recommendDetaid = {} } = this.data.recommend;
      const { productInfos } = recommendDetaid;
      const showConfirmButton = !isEmptyObject(productInfos);

      const paySubmitNewCom = this.selectComponent('#pay-submit');
      Dialog.confirm({
        message:
          '仅限余额(不含礼品卡)全额支付时,可享受储值会员优惠。如放弃优惠,将重新计算订单价格。',
        confirmButtonText: '去充值',
        showConfirmButton,
        cancelButtonText: '放弃优惠',
        context: this,
        zIndex: 99999,
      })
        .then(() => {
          paySubmitNewCom.cancelPay();
          const orderGoodsCom = this.selectComponent('#order-goods');
          // orderGoodsCom.showValueCardPop();
          orderGoodsCom.handleChooseValue('storeVip');
        })
        .catch(() => {
          paySubmitNewCom.cancelPay();
          // 不使用会员折扣
          this.memberBenefit = null;
          this.needRecommendMemberBenefit = false;
          this.fetchOrderDetail(this.cartId, this.cartItems);
        });
      return;
    }

    if (this._isPaying) return;
    this._isPaying = true;

    // 支持加餐，创建预订单
    if (this.data.supportAddMeal) {
      return this.doCreatePreOrder();
    }

    // 如果不支持调用通知功能，就直接创建订单
    if (!wx.canIUse('requestSubscribeMessage')) {
      this.doCreateOrder();
      return;
    }

    let templateInfoRequest = Promise.resolve(this.data);

    if (!this.data.templateIdListLoaded) {
      templateInfoRequest = this.getTemplateInfo();
    }
    templateInfoRequest
      .then((res) => {
        const { templateIdList } = res;
        // 如果获取不到通知模板内容，就直接创建订单
        if (!(templateIdList instanceof Array && templateIdList.length)) {
          this.doCreateOrder();
          return;
        }
        // 授权订阅消息弹框
        this.doSubscribeNotify(templateIdList);
      })
      .catch((e) => {
        // 获取模板出错时，直接创建订单
        this.doCreateOrder();
        this.skyLog({
          message: '扫码点单下单获取订阅模板出错',
          detail: {
            errMessage: e?.message || '',
          },
        });
      });
  },

  // 调用wx授权订阅消息弹框
  doSubscribeNotify(templateIdList) {
    const wxReqeustSubscribeMessage = (templateIds = []) =>
      new Promise((resolve, reject) => {
        wx.requestSubscribeMessage({
          tmplIds: templateIds,
          success: resolve,
          fail: reject,
        });
      });
    // 根据模板，调用wx消息通知
    wxReqeustSubscribeMessage(templateIdList)
      .then((successSubscribeRes) => {
        const tmplIds = Object.keys(successSubscribeRes)
          .map((key) => {
            if (successSubscribeRes[key] === 'accept') {
              return key;
            }
            return null;
          })
          .filter(Boolean);
        // 用户不同意接收通知任何消息
        if (!tmplIds.length) {
          return;
        }
        // 订阅回调：将用户同意的模板id，回传给后端（后端推送消息要用）
        subscriptionCallback(tmplIds);
      })
      .catch((err) => {
        let errorMsg = '订阅微信通知失败';
        if (err.errMsg && err.errMsg.indexOf('switched off') > -1) {
          errorMsg = '请在小程序设置中允许订阅消息';
        }
        this.skyLog({
          message: `扫码点单下单订阅模板回调出错${errorMsg}`,
          detail: {
            errMessage: err?.errMsg || '',
          },
        });
        // wx.showToast({ title: errorMsg, icon: 'none' });
      })
      .finally(() => {
        // 不管用户同没同意，最终还是要创建订单的。
        this.doCreateOrder();
      });
  },

  handlePaidSuccess({ detail: { orderNo } }) {
    // wx.showLoading({
    //   title: '支付结果查询中'
    // });
    let isSuccessDone = false;
    const fetchOrderStatusInterval = 300;
    const fetchOrderStatusOvertime = 3000;
    const jumpToPaidPage = () => {
      wx.redirectTo({
        url: `/packages/retail/paid/index?orderNo=${orderNo}`,
        complete: () => {
          wx.hideLoading();
          clearTimeout(this.fetchOrderDetailTimer);
          clearTimeout(this.failFetchOrderDetailTimer);
        },
      });
    };
    const syncOrderStatus = () =>
      fetchOrderDetail({ orderNo })
        .then((orderData) => {
          if (orderData.state === 100) {
            isSuccessDone = true;
            return jumpToPaidPage();
          }
          // unload里面已经clear定时器了，下面的方法还是会走，这里做下是否已经unload的判断
          if (this.isUnload) return;
          this.fetchOrderDetailTimer = setTimeout(
            syncOrderStatus,
            fetchOrderStatusInterval
          );
        })
        .catch(() => {
          jumpToPaidPage();
        });

    this.fetchOrderDetailTimer = setTimeout(
      syncOrderStatus,
      fetchOrderStatusInterval
    );
    // 超过最大时间，则跳转到paid页面
    this.failFetchOrderDetailTimer = setTimeout(() => {
      if (!isSuccessDone) {
        jumpToPaidPage();
      }
    }, fetchOrderStatusOvertime);
  },

  initShoppingBagData(shopBagList, goodsList) {
    const defaultPic =
      'https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>';
    const bags = shopBagList.map(
      ({ shoppingBagName, price, picUrl = defaultPic, uniqueKey }) => {
        const bagItem = goodsList.find((item) => item.uniqueKey === uniqueKey);
        return {
          name: shoppingBagName,
          picUrl,
          price,
          uniqueKey,
          count: bagItem ? bagItem.amount : 0,
        };
      }
    );
    this.setData({ bags });
  },

  _isDealingWithGoodsNum: false,

  handleBagGoodsNumUpdate(event) {
    if (this._isDealingWithGoodsNum) return;
    this._isDealingWithGoodsNum = true;
    const { goodsList, bags } = this.data;
    const { value, oldValue, type } = event.detail;
    const { name, price, picUrl, uniqueKey } = event.currentTarget.dataset;
    const bagItem = bags.find((bag) => bag.uniqueKey === uniqueKey);
    const goodsItem = goodsList.find((item) => item.uniqueKey === uniqueKey);
    let leftCount = value;
    let request = null;
    if (type === 'minus' && oldValue === 1) {
      request = deleteCartData(goodsItem).then(() => {
        leftCount = 0;
      });
    } else if (type === 'plus' && oldValue === 0) {
      request = addShopBagToCart(this.cartId, {
        title: name,
        price,
        uniqueKey,
        picUrl,
      }).then(() => {
        leftCount = 1;
      });
    } else {
      const { cartItemId } = goodsItem;
      request = updateCartData({ cartItemId, amount: value }).then(() => {
        leftCount = value;
      });
    }
    request
      .then(() => {
        bagItem.count = leftCount;
        this.setData({ bags });
      })
      .catch((error) => {
        wx.showToast({
          icon: 'none',
          title: error?.msg ?? '编辑数量失败, 请重试',
        });
      })
      .finally(() => {
        this.refreshCartEvent().then(() => {
          this._isDealingWithGoodsNum = false;
        });
      });
  },

  handleValueCardChange({
    cards,
    isDiscount,
    isRecommend,
    immediate,
    isMutex,
  }) {
    const { cartId, cartItems } = this;

    const { deliveryWay: deliveryType, supportAddMeal } = this.data;
    this._needHelpSelectCard = false;

    const params = {
      cartId,
      cartItems,
      deliveryType,
      needHelpSelectCard: false,
      // 多人购物车默认 hasPromotion true
      setHasPromotion: supportAddMeal,
      hasPromotion: false,
      supportAddMeal,
      filterPreOrdered: true,
      // 多人购物车默认是 false
      needRecommendStoredDiscount: false,
      isMutex,
    };

    // 储值优惠禁用优惠券 同时不能选储值卡支付
    if (isDiscount) {
      params.needRecommendCoupon = false;
      params.needRecommendMemberBenefit = false;
    } else {
      const { cardIds, isExclusion } = getIdsAndPromotion(cards || {});
      params.prepaidCardList = cardIds;
      params.hasPromotion = !isExclusion;
      params.needRecommendStoredDiscount = supportAddMeal
        ? false
        : cardIds.length > 0;
    }

    return confirmOrder(params)
      .then((orderData) => {
        const {
          totalCurrentPrice,
          usedCustomerPayCardBalance,
          totalPrice,
          payCardDTOList,
          totalDecrease,
          promotion,
          memberBenefits,
          goodsList = [],
          bizExtraMap,
        } = orderData;
        if (bizExtraMap) {
          this.checkShowedAllBalancePayModal(bizExtraMap);
        }

        const couponList = getCouponList(promotion);
        const selectedValueCard =
          supportAddMeal || isDiscount
            ? []
            : getSelectedValueCard(payCardDTOList);

        const storedValueAndCard = getStoreValueAndCard(payCardDTOList);

        const data = {
          recommend: getRecommend(orderData),
          totalDecrease,
          totalPay: totalCurrentPrice,
          totalCurrentPrice,
          realTotalPrice: totalPrice,
          cardPricePay: usedCustomerPayCardBalance,
          isHiddenCard: hasDiscount(promotion),
          selectedCoupons: supportAddMeal ? [] : getSelectedCoupons(couponList),
          selectedValueCard,
          storedValueAndCard,
          memberBenefits,
          canUseMemberBenefit: memberBenefits.length > 0,
          goodsList: goodsList.map(adaptGoods),
          canChooseCoupons: supportAddMeal
            ? false
            : !!getCanSelectedCoupons(couponList).length,
        };

        /**
         * 如果互斥点击去充值去支付
         * 第二次renderOrder不一定返回储值档位
         * 所以不第二次不更新推荐信息
         */
        if (isMutex) {
          delete data.recommend;
        }

        this.setData({ ...data });
      })
      .finally(() => {
        if (immediate) {
          const paySubmit = this.selectComponent('#pay-submit');
          paySubmit.emitPay();
        }
      });
  },

  handleValueCardSubmit({ detail }) {
    const {
      detail: { cards = {} },
    } = detail;
    const { cardIds, isExclusion } = getIdsAndPromotion(cards);
    this._useStoredCard = cardIds.length > 0;
    this.fetchOrderDetail(this.cartId, this.cartItems, cardIds, !isExclusion);
  },

  handleBack() {
    wx.navigateBack();
  },

  async handleChangeMeituanCoupons({ detail: { done, refresh = true, ids } }) {
    this._changeCouponTips = true;
    if (Array.isArray(ids)) {
      this._meituanCoupons = ids;
      this.needRecommendCoupon = false;
    }
    if (refresh) {
      await this.fetchOrderDetail(this.cartId, this.cartItems);
    }
    done && done();
  },
});
