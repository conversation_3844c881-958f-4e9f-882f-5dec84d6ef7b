import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
// import carmen from 'retail/util/carmen';
import retailCarmen from 'retail/util/retail-carmen';
import { getReatilSource } from 'retail/util/global';

export { confirmOrder } from 'retail/util/api';

const app = getApp();

// 小程序订阅消息模板
export function getTemplate() {
  return app.retailRequest({
    path: '/retail/h5/miniprogram/getTemplate.json',
  });
}

// 小程序订阅消息的回调函数
export function subscriptionCallback(templateIdList) {
  return app.request({
    path: '/retail/h5/miniprogram/subscriptionCallback.json',
    data: {
      templateIdList,
    },
  });
}

export const batchUpdate = (goods) =>
  retailCarmen({
    api: 'youzan.retail.trade.customer.cart/1.0.0/batchedit',
    data: {
      goods: JSON.stringify(goods),
    },
  });

export const getPrice = ({
  cartItemIds,
  cartId,
  couponId,
  diningWay,
  calculateLunchBoxFee = false,
  calculateStoredDiscount = true,
}) => {
  const params = {
    adminId: app.getBuyerId(),
    source: getReatilSource(),
    cartItemIds,
    calculateLunchBoxFee,
    calculateStoredDiscount,
    dingingWay: diningWay, // 就餐方式
  };
  couponId && (params.couponId = couponId);
  cartId && (params.cartId = cartId);
  return app.request({
    path: '/retail/h5/miniprogram/calculateOrderPrice.json',
    method: 'POST',
    data: params,
  });
};

export const createOrder = ({ orderData }) => {
  if (!orderData.uniqueId) {
    delete orderData.uniqueId;
  }
  return app
    .retailRequest({
      path: '/retail/h5/trade/createOrder.json',
      method: 'POST',
      data: orderData,
    })
    .then(mapKeysCase.toCamelCase);
};

export const getShoppingSkuNo = () => {
  return retailCarmen({
    api: 'youzan.retail.trade.misc.shoppingbag/1.0.0/msearch',
  }).then(({ spu_no: spuNo, has_open: hasOpen }) => {
    return {
      hasOpen,
      spuNo,
    };
  });
};

/**
 * 创建预订单
 *
 * @param {number} cartId
 * @param {number[]} cartItemIds
 * @param {string} comment
 */
export const createPreOrder = (cartId, cartItemIds, comment = '') => {
  return app.retailRequest({
    path: '/retail/h5/trade/createPreOrder.json',
    method: 'POST',
    data: {
      cartId,
      itemIdList: cartItemIds,
      buyerRemark: comment,
      retailSource: getReatilSource(),
    },
  });
};

// 添加减钱商品
export const addRechargeActivity = (data) => {
  return app.retailRequest({
    path: '/retail/h5/miniprogram/addRechargeActivity.json',
    method: 'POST',
    data,
  });
};

/**
 * 获取换购商品
 * @param {*} param0
 * @returns
 */
export function getAddOnGoodsList({
  preferentialType,
  activityType,
  activityId,
  level,
  recommendNum = 500,
}) {
  const params = {
    activity_id: activityId,
    activity_type: activityType,
    recommend_num: recommendNum,
    preferential_type: preferentialType,
    level,
  };
  for (const key in params) {
    if (params[key] === undefined || params[key] === null) {
      delete params[key];
    }
  }
  return retailCarmen({
    api: 'youzan.retail.ump.calculation.activity.recommend/1.0.0/get',
    data: params,
  }).then((res) => mapKeysCase.toCamelCase(res));
}
