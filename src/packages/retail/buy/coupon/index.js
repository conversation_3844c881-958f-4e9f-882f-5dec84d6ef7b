import { getDateStrFix2 } from '@youzan/weapp-utils/lib/time-utils';
import Event from '@youzan/weapp-utils/lib/event';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import WscPage from 'pages/common/wsc-page/index';
import { ThemeColorWithComponent } from 'retail/util/behaviors/themeBehavior';

const app = getApp();

const TYPE_MAP = {
  101: 'reward', // 'discount', 'random',
  1013: 'goods',
  1014: 'meetGive',
};

const COUPON_NUM_LIMIT = 20;

/**
 * 整个函数成立的依据是，所有'选择'的数据都是正确。不存在脏数据，
 * @param {object} c
 * @returns c
 */
function tidyUpDisableAttr(c, extra = {}) {
  const coupons = JSON.parse(JSON.stringify(c));
  let optSelect = {};
  const { coupon, selected } = extra;

  if (selected) {
    optSelect = coupons.find((item) => item.id === coupon.id);
    // 非叠加券
    if (!optSelect.canOverlayAsset) {
      coupons.forEach((item) => {
        if (item.id !== coupon.id) {
          item.selected = false;
        }
      });
    } else {
      // 选了叠加券, 非叠加券都false
      coupons.forEach((item) => {
        if (!item.canOverlayAsset) {
          item.selected = false;
        }
      });
    }
  }

  const selectedCoupons = coupons.filter((item) => item.selected);
  const outLimit = selectedCoupons.length > COUPON_NUM_LIMIT;

  if (outLimit) {
    wx.showToast({
      title: `最多选择${COUPON_NUM_LIMIT}张叠加券`,
      icon: 'none',
    });
    optSelect.selected = false;
  }
  return coupons;
}

function adaptCoupon(coupon) {
  const newCoupon = mapKeysCase.toCamelCase(coupon);
  const {
    type,
    name,
    startTime,
    endTime,
    discount,
    meetValue,
    meetType,
    denominations,
    isRecommend: selected,
    extraMap = {},
  } = newCoupon;
  let value = '';
  let value2 = '';
  let couponType = TYPE_MAP[type];

  if (denominations) {
    value = denominations / 100;
  }
  if (discount) {
    value = discount / 10;
    couponType = 'discount';
  }

  if (meetType === 2) {
    if (meetValue) {
      value2 = `满${meetValue}件`;
    }
  } else if (meetValue) {
    value2 = `满${meetValue / 100}元使用`;
  } else {
    value2 = '无门槛使用';
  }

  if (couponType === 'meetGive') {
    const { umpPreferenceDesc } = extraMap;
    value = umpPreferenceDesc || `买赠券`;
  }

  const priceStr = denominations
    ? (denominations / 100).toFixed(2).split('.')
    : '';
  const startTimeStr = getDateStrFix2(new Date(startTime)).replace(/-/g, '.');

  const endTimeStr = endTime
    ? getDateStrFix2(new Date(endTime)).replace(/-/g, '.')
    : '无限制';

  return {
    ...newCoupon,
    type: couponType,
    value,
    value2,
    title: name,
    isNewcomerVoucher: false,
    priceStr,
    selected,
    period: `${startTimeStr} - ${endTimeStr}`,
  };
}

WscPage({
  behaviors: [ThemeColorWithComponent],
  data: {
    couponList: [],
    unavailableCouponList: [],
  },
  onLoad(options) {
    const { dbid } = options;
    const { coupon_list: originalCouponList, selected = [] } = app.db.get(dbid); // 可用优惠券列表
    const [unavailableCouponList, couponList] = originalCouponList
      .map(adaptCoupon)
      .reduce(
        (result, coupon) => {
          const index = +coupon.isCanUse;
          coupon.disabled = !coupon.isCanUse;
          result[index].push(coupon);
          return result;
        },
        [[], []]
      );

    this.setYZData({
      couponList: this.placementSelectedCouponsToHead(selected, couponList),
      unavailableCouponList,
    });
  },

  placementSelectedCouponsToHead(selected, list) {
    if (selected.length === 0) {
      return list;
    }

    const selectedIds = selected.map((item) => item.id);

    return tidyUpDisableAttr(
      [...selected.map(adaptCoupon)].concat(
        list.filter((item) => !selectedIds.includes(item.id))
      )
    );
  },

  // 选择优惠券
  onSelected({ detail: { coupon, selected } }) {
    if (coupon.isCanUse) {
      const { couponList } = this.data;

      this.setYZData({
        couponList: tidyUpDisableAttr(
          couponList.map((item) => ({
            ...item,
            selected: item.id === coupon.id ? selected : item.selected,
          })),
          {
            coupon,
            selected,
          }
        ),
      });
    }
  },

  // 点击不使用优惠券
  onNotUseTapped() {
    Event.trigger('order-coupon-change', []);
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx.navigateBack();
  },

  onConfirm() {
    Event.trigger(
      'order-coupon-change',
      this.data.couponList.filter((coupon) => coupon.selected)
    );
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx.navigateBack();
  },
});
