.page-content {
  padding: 16px 12px 100px;
  background-color: #f2f2f2;
  min-height: 100vh;
}

.fixed-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  background-color: #fff;
  z-index: 1;
  padding: 0 12px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.not-use-coupon {
  width: calc(50vw - 16px);
  height: 44px;
  font-weight: 500;
  font-size: 14px;
  line-height: 44px;
  text-align: center;
  background-color: #f2f3f5;
  color: var(--theme-main-color);
  border-radius: 30px;
  margin-right: 8px;
}

.confirm {
  width: calc(50vw - 16px);
  height: 44px;
  font-weight: 500;
  font-size: 14px;
  line-height: 44px;
  text-align: center;
  background-color: var(--theme-main-color);
  color: white;
  border-radius: 30px;
}

.empty {
  padding-top: 100px;
  text-align: center;
}

.empty__icon {
  height: 84px;
  background: url('https://img01.yzcdn.cn/public_files/2016/11/30/031ac6541509ccc171e2bf2b9e8da405.png')
    no-repeat center;
  background-size: 80px 84px;
}

.empty__text {
  padding: 20px 0;
  font-size: 14px;
  color: #999;
}

.coupon-title {
  position: relative;
  width: 60%;
  margin: 0 auto;
  font-size: 28rpx;
  text-align: center;
  vertical-align: middle;
  color: #999;
}

.coupon-title:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid #e5e5e5;
  border-top-width: 1rpx;
}
