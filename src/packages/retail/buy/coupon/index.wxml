<view class="page-content" style="--theme-main-color: {{ themeMainColor }}; --theme-main-rgb-color: {{ themeMainRgbColor }}">
	<coupon-item
	 wx:for="{{couponList}}"
	 info="{{item}}"
	 selectMode
	 isSelected="{{item.selected}}"
	 isDisabled="{{item.selectDisabled}}"
	 canOverlayAsset="{{item.canOverlayAsset}}"
	 bind:selected="onSelected"
	/>
	<!--失效优惠码-->
	<coupon-item wx:for="{{unavailableCouponList}}" isInvalid info="{{ item }}" />

	<view wx:if="{{ !couponList.length && !unavailableCouponList.length }}" class="empty">
		<view class="empty__icon" />
		<view class="empty__text">暂无优惠券</view>
	</view>

	<view class="fixed-buttons">
		<view class="not-use-coupon" bind:tap="onNotUseTapped">不使用优惠</view>
		<view class="confirm" bind:tap="onConfirm">确定</view>
	</view>
</view>

<inject-protocol noAutoAuth />