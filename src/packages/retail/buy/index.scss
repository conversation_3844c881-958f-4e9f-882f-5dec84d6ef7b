page {
  background: #f2f3f5;

  --secondary-text: #969799;
  --blue: #1989fa;
  --radio-width: 36rpx;
  --half-radio-width: 18rpx;
}

.order-container {
  background: #F2F3F5;
}

.card-cell {
  --title-color: #646566;
  --title-size: 30rpx;
  --title-size: 30rpx;
  --header-justify: space-between;
  --card-padding: 32rpx;
  --card-height: 72rpx;
}

/* van 不够灵活，挺难用的 */
.takeway-radio-container {
  display: inline-flex;
  align-items: center;
  background: #F2F2F2;
  border-radius: 32rpx;
  height: 64rpx;
}

.takeway-radio {
  color: #666 !important;
  flex: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-weight: 500;
  font-size: 28rpx;
  line-height: 52rpx !important;
  text-align: center;
  padding: 6rpx 30rpx;
  padding-left: 30rpx !important;
  height: 64rpx;
  box-sizing: border-box;
}

.takeway-radio--selected {
  color: #fff !important;
  background: var(--theme-main-color) !important;
  border-radius: 40rpx;
}

.order-cell {
  height: 96rpx;
  display: flex;
  align-items: center;
}

.order-cell-title {
  font-weight: 600;
  font-size: 28rpx;
  line-height: 36rpx;
  color: #323233;
}

.order-cell-label {
  font-size: 28rpx;
  line-height: 36rpx;
  color: #323233;
}

.solid-line {
  background: #fff;
  position: relative;
}
.solid-line::after {
  content: "";
  position: absolute;
  top: 0;
  left: 24rpx;
  right: 24rpx;
  border-top: 1rpx solid #DCDEE0;
}

.tip-shopping {
  padding: 26rpx 32rpx;
  background: white;
  height: 240rpx;
  margin-top: 20rpx;
  border-radius: 8rpx;
}

.shop-header {
  color: #323233;
  font-weight: 500;
  font-size: 28rpx;
}

.shop-container {
  width: 100%;
  height: 100%;
  margin-top: 10rpx;
  white-space: nowrap;
  box-sizing: border-box;
}

.shop-container .bag-content {
  display: inline-block;
  width: 300rpx;
  height: 250rpx;
  margin-top: 20rpx;
}
.shop-container .shop-bag-item {
  display: flex;
  width: 100%;
  height: 100%;
}

.shop-bag-item .icon {
  width: 64rpx;
  height: 64rpx;
}

.shop-bag-info .shop-bag-desc {
  padding-left: 20rpx;
  color: #666;
  font-size: 14px;
}

.shop-bag-desc .shop-name {
  display: inline-block;
  width: 220rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.banner-container {
  width: 100%;
}

.submit-same-notice {
  font-size: 26rpx;
  color: #ed6a0c;
  background-color: #fffbe8;
  padding: 14rpx 30rpx;
}

.order-title {
  height: 96rpx;
  background: #FFF;
  border-radius: 16rpx 16rpx 0 0;
  font-weight: 600;
  font-size: 28rpx;
  line-height: 36rpx;
  color: #323233;
  padding-left: 24rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.order-progress {
  padding: 32rpx;
  background: #fff;
  box-sizing: border-box;
  padding-top: 0;
  margin-top: -4rpx;
}

.custom-nav {
  display: flex;
  align-items: center;

  &__left,
  &__right {
    flex: 1;
  }

  &__left {
    display: flex;
    align-items: center;

    &--icon {
      display: flex;
      height: 100%;
      flex-shrink: 1;
      align-items: center;

      .icon-return {
        display: flex;
        padding: 0 16tpx;
        height: 100%;
        justify-content: center;
        align-items: center;
      }
    }
  }

  &__title {
    font-size: 16tpx;
    font-weight: 700;
    color: #323233;
  }
}

.order-info {
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 13tpx;
}
