import Event from '@youzan/weapp-utils/lib/event';
import WscPage from 'pages/common/wsc-page';

const app = getApp();

WscPage({
  data: {
    couponList: [],
    unavailableCouponList: [],
    selectedCoupon: {}
  },
  onLoad(options) {
    const { dbid } = options;
    const { valueCardList, selected, totalPay } = app.db.get(dbid); // 可用储值卡列表

    this.setData({
      valueCardList: valueCardList.filter(
        card => card.usable && card.balance >= totalPay
      ),
      selectedCard: selected
    });
  },

  // 选卡
  onCardTaped({ currentTarget }) {
    const { card = {} } = currentTarget.dataset;
    const { usable } = card;
    if (usable) {
      Event.trigger('order-value-card-change', card);
      wx.navigateBack();
    }
  },

  // 点击不使用储值卡
  onNotUseTapped() {
    Event.trigger('order-value-card-change', {});
    wx.navigateBack();
  }
});
