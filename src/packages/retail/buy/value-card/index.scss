/* index.wxss */
:host {
  --status-value-width: 100rpx;
}

.coupon-not-use {
  height: 88rpx;
  line-height: 88rpx;
  color: #333;
  font-size: 32rpx;
  text-align: center;
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  background-color: #fff;
  z-index: 1;
}

.empty {
  padding-top: 100px;
  text-align: center;
}

.empty__icon {
  height: 84px;
  background: url('https://img01.yzcdn.cn/public_files/2016/11/30/031ac6541509ccc171e2bf2b9e8da405.png')
    no-repeat center;
  background-size: 80px 84px;
}

.empty__text {
  padding: 20px 0;
  font-size: 14px;
  color: #999;
}

.card-cell {
  border-radius: 4px;
  margin: 30rpx 30rpx;
  overflow: hidden;
  position: relative;
  padding: 14px 14px 12px;
  background: #fff;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  &__info {
    flex: 1;
    flex-direction: column;
    width: calc(100% - var(--status-value-width));

    &-title {
      display: block;
      margin-bottom: 10px;
      height: 18px;
      font-size: 14px;
      line-height: 1.29;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &-money-wrap {
      font-weight: 600;
      &__price {
        height: 28px;
        font-size: 8vw;
        line-height: 0.93;
        color: #323233;
      }
    }
  }

  &__status {
    width: var(--status-value-width);
    display: flex;
    flex-direction: row-reverse;
    flex-shrink: 0;
  }
}
