<page-container forbid-copyright>
  <view style="padding-bottom:100rpx">
    <block
      wx:for="{{valueCardList}}"
      wx:key="summaryCardNo"
    >
      <view
        bind:tap="onCardTaped"
        data-card="{{item}}"
        class="card-cell"
      >
        <view class="card-cell__info">
          <text class="card-cell__info-title">{{item.cardName}}</text>
          <view class="card-cell__info-money-wrap">
            <text>￥</text>
            <text class="card-cell__info-money-wrap__price">{{money.toYuan(item.balance)}}</text>
          </view>
        </view>
        <view class="card-cell__status">
          <van-icon
            wx:if="{{selectedCard.summaryCardNo === item.summaryCardNo}}"
            name="checked"
            color="#ee0a24"
            size="40rpx"
          />
          <van-icon
            wx:else
            name="circle"
            color="#969799"
            size="40rpx"
          />
        </view>
      </view>
    </block>

    <view
      wx:if="{{ !valueCardList.length }}"
      class="empty"
    >
      <view class="empty__icon"></view>
      <view class="empty__text">暂无可用余额/卡</view>
    </view>
    <view
      class="coupon-not-use zan-hairline--top"
      bindtap="onNotUseTapped"
    >不使用余额/卡</view>
  </view>
</page-container>

<wxs
  src="retail/util/money.wxs"
  module="money"
/>
