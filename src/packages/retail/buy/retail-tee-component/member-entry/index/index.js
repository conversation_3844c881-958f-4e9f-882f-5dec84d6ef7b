import _tc from "@youzan-open/tee";import _extends from "@babel/runtime/helpers/esm/extends";
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
import Tee from '@youzan/tee';
import formatMoney from '@youzan/utils/money/format';
import { apiGetFastJoin, apiFastJoin } from '../../fast-join/api';
import Toast from '@youzan-open/vant-tee/native/weapp/toast/toast';

var _Tee$getApp = Tee.getApp(),
    logger = _Tee$getApp.logger;

var app = Tee.getApp();
_tc.component({
  props: {
    isNoticeSeparate: Boolean,
    // 入会结果是否按钮和弹框分离，处理层级问题
    isSeparate: Boolean,
    // 是否按钮和弹框分离，处理层级问题
    type: String,
    hasBorder: Boolean,
    hasBgColor: Boolean,
    discount: Number,
    title: {
      type: String,
      default: '入会立享会员价：'
    },
    launchArgs: Object,
    joinMemberPromotion: Object,
    userAuthorizeType: {
      type: String,
      default: 'normal'
    },
    customStyle: String,
    themeColor: String,
    // 'orderDecorate' | 'cart' | 'cartPop' | 'skuDetail' | 'retailBuy'| 'retailPreDetail'
    // [点单页装修] | [购物车] | [购物车弹框] | [商品详情] | [下单页] | [预结单页]
    memberScene: String,
    hostShow: Boolean // 组件宿主是否展示

  },
  data: function data() {
    return {
      fastJoinShow: false,
      kdtId: app.getKdtId() || app.getExtKdtId(),
      onlyNeedMobile: false,
      customerAttributes: []
    };
  },
  computed: {
    discountInner: function discountInner() {
      var _this$joinMemberPromo;

      return +formatMoney(this.discount || ((_this$joinMemberPromo = this.joinMemberPromotion) == null ? void 0 : _this$joinMemberPromo.totalDecrease));
    },
    isShow: function isShow() {
      var _Object$keys;

      return this.type === 'blank' ? true // 空标签代表走这里的入会流程，由外部控制展示逻辑这里不管
      : this.type === 'text' // 文字模式要有立省价格才展示
      ? !!+this.discountInner : ((_Object$keys = Object.keys(this.joinMemberPromotion)) == null ? void 0 : _Object$keys.length) > 0;
    },
    logShow: function logShow() {
      var isShow = this.isShow,
          hostShow = this.hostShow;
      return {
        isShow: isShow,
        hostShow: hostShow
      };
    }
  },
  watch: {
    // 依赖两个字段展示，容器展示才触发曝光埋点
    logShow: {
      handler: function handler(val) {
        if (val.isShow && val.hostShow) {
          this.logView();
        }
      },
      immediate: true,
      deep: true
    }
  },
  created: function created() {
    this.getMemberInfo();
  },
  methods: {
    logView: function logView() {
      logger && logger.log({
        et: 'view',
        // 事件类型
        ei: 'guide_member_view',
        // 事件标识
        en: '引导入会入口曝光',
        // 事件名称
        pt: 'retailScanGo',
        // 页面类型
        params: {
          entry_source: this.memberScene
        } // 事件参数

      });
    },
    getMemberInfo: function getMemberInfo() {
      var _this = this;

      apiGetFastJoin().then(function (res) {
        var _res$attributeInfo, _this$customerAttribu;

        _this.customerAttributes = (res == null ? void 0 : (_res$attributeInfo = res.attributeInfo) == null ? void 0 : _res$attributeInfo.customerAttributes) || []; // 入会只需要填一个信息且是手机信息

        _this.onlyNeedMobile = ((_this$customerAttribu = _this.customerAttributes) == null ? void 0 : _this$customerAttribu.length) === 1 && _this.customerAttributes[0].attributeId === 3;
      });
    },
    handleBtnClick: function handleBtnClick(params) {
      var _this2 = this;

      logger && logger.log({
        et: 'click',
        // 事件类型
        ei: 'guide_member_entry',
        // 事件标识
        en: '引导入会入口点击',
        // 事件名称
        pt: 'retailScanGo',
        // 页面类型
        params: {
          entry_source: this.memberScene
        } // 事件参数

      }); // 如果只需要手机，直接入会

      if (this.onlyNeedMobile) {
        var mobileInfo = _extends({}, this.customerAttributes[0], {
          value: params == null ? void 0 : params.mobile,
          isEditable: true,
          isChange: true
        });

        Toast('入会中...');
        apiFastJoin({
          attributeList: [mobileInfo],
          clickWay: 0,
          sceneType: 1,
          member_src_way: '900'
        }).then(function (res) {
          if (res) {
            !_this2.isNoticeSeparate && _this2.$refs.joinNotice.launchJoinMemberNotice(_this2.launchArgs);

            _this2.$emit('showJoinNotice');
          } else {
            Toast({
              type: 'text',
              message: '入会失败，请稍后重试',
              zIndex: 6000
            });
          }

          _this2.handleFastJoinHide();
        });
      } else {
        this.fastJoinShow = true; // 完善信息弹框出现

        this.$emit('show');
      }
    },
    handleFastJoinHide: function handleFastJoinHide() {
      this.fastJoinShow = false;
      this.$emit('close');
    }
  },
  ud: true
})