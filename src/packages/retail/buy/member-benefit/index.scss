.wrapper {
  display: flex;
  height: 100vh;
  flex-direction: column;
  background-color: #f2f3f5;
}

.header {
  background-color: #fff;
}

.custom-nav {
  display: flex;
  align-items: center;

  &__left,
  &__right {
    flex: 1;
  }

  &__left {
    display: flex;
    align-items: center;

    &--icon {
      display: flex;
      height: 100%;
      flex-shrink: 1;
      align-items: center;

      .icon-return {
        display: flex;
        padding: 0 16tpx;
        height: 100%;
        justify-content: center;
        align-items: center;
      }
    }
  }

  &__title {
    font-size: 16tpx;
    font-weight: 700;
    color: #323233;
  }
}

.body {
  flex-grow: 2;
  display: flex;
  width: 100%;
  padding: 16px 12px;
  flex-direction: column;
  overflow-y: auto;
  // padding: 0 12tpx 12tpx;
  padding-bottom: 12tpx;
  box-sizing: border-box;
}

.footer {
  position: fixed;
  display: flex;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 5tpx 16tpx;
  background-color: #fff;
  box-sizing: border-box;

  &__button {
    flex: 1;
    display: flex;
    margin-right: 8px;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);
    height: 44px;
    font-size: 14px;
    color: #fff;
    font-weight: 500;
    justify-content: center;
    align-items: center;
    color: var(--theme-color);
    background-color: #f2f3f5;
    border-radius: 30px;
  }

  &__button:last-child {
    margin-right: 0;
  }

  &__button--main {
    color: #fff;
    background-color: var(--theme-color);
    border-radius: 30px;
  }
}

.member-benefit {
  overflow: hidden;
  display: flex;
  margin-bottom: 10px;
  width: 100%;
  height: 84px;
  flex-shrink: 0;
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;

  &--left {
    display: flex;
    padding-left: 20px;
    flex-direction: column;
    flex-grow: 1;
    justify-content: center;
    align-items: flex-start;
    color: var(--theme-color);;
  }

  &--right {
    display: flex;
    width: 76px;
    height: 100%;
    justify-content: center;
    align-items: center;
  }

  &__name {
    margin: 6px 0 12px 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 18px;
    padding-right: 8px;
  }

  &__desc {
    font-size: 12px;
    line-height: 16px;
    color: #999;
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.placeholder {
  display: flex;
  height: 80px;
  flex-shrink: 0;
}