<van-popup
	wx:if="{{ show }}"
	show
	position="right"
	z-index="100"
	custom-style="width: 100%;"
	close-on-click-overlay="{{ false }}"
	safe-area-inset-bottom
	lock-scroll
>
	<view class="wrapper" style="--theme-color: {{ themeColor }}">
		<view class="header">
			<view style="height: {{ statusBarHeight }}px"></view>
			<view class="custom-nav" style="height:{{ navHeight }}px">
				<view class="custom-nav__left">
					<view class="custom-nav__left--icon" catch:tap="onClose">
						<van-icon
							class="icon-return"
							name="arrow-left"
							size="22px"
						></van-icon>
					</view>
				</view>
				<view class="custom-nav__title">会员优惠</view>
				<view class="custom-nav__right"></view>
			</view>
		</view>
		<view class="body" style="height: calc(100vh - {{statusBarHeight + navHeight}}px)">
			<view wx:for="{{ memberBenefits }}" wx:key="{{ index }}" class="member-benefit">
				<view class="member-benefit--left">
					<view class="member-benefit__name ellipsis">{{ item.name }}</view>
					<view class="member-benefit__desc ellipsis">{{ item.benefitCopywriting }}</view>
				</view>
				<view class="member-benefit--right" data-index="{{ index }}" catch:tap="onSelected">
					<van-checkbox
						style="margin: 0 auto;"
						value="{{ selectedIdx === index }}"
						checked-color="{{ themeColor }}"
					/>
				</view>
			</view>
			<view class="placeholder"></view>
		</view>
		<view class="footer">
			<view class="footer__button" catch:tap="onReject">不使用优惠</view>
			<view class="footer__button footer__button--main" catch:tap="onConfirm">确定</view>
		</view>
	</view>
</van-popup>