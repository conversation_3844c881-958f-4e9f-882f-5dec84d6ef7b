import WscComponent from 'pages/common/wsc-component/index';
import { getThemeMainColor } from 'retail/util/shelf-global';
import { CustomNavBehavior } from 'retail/util/behaviors/customNavBehavior';

WscComponent({
  behaviors: [CustomNavBehavior],
  properties: {
    show: <PERSON><PERSON><PERSON>,
    memberBenefits: Array,
  },
  observers: {
    memberBenefits(val) {
      const originSelectedIdx = val.findIndex((benefit) => benefit.isRecommend);
      this.setData({
        selectedIdx: originSelectedIdx,
      });
      this.originSelectedIdx = originSelectedIdx;
    },
  },
  data: {
    themeColor: getThemeMainColor(),
    selectedIdx: -1,
  },
  attached() {
    this.setData({
      themeColor: getThemeMainColor(),
    });
  },
  methods: {
    onClose() {
      this.triggerEvent('onClose');
      this.setData({
        selectedIdx: this.originSelectedIdx,
      });
    },
    onSelected({
      currentTarget: {
        dataset: { index },
      },
    }) {
      if (index !== this.data.selectedIdx) {
        this.setData({
          selectedIdx: index,
        });
      }
    },
    onConfirm() {
      if (this.data.selectedIdx === this.originSelectedIdx) {
        this.onClose();
        return;
      }
      this.triggerEvent(
        'onSelect',
        this.data.memberBenefits[this.data.selectedIdx]
      );
    },
    onReject() {
      this.triggerEvent('onSelect', null);
    },
  },
});
