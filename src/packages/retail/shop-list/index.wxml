<page-container forbid-copyright>
	<global-custom-loading wx:if="{{ globalCustomLoading }}" show="{{ loading }}" />
  <view class="page-container {{ deviceType }} {{ themeClass }}">
    <notice-bar
      scrollable="false"
      text="总店无法下单，请选择门店地址"
      wx:if="{{ showMsg }}"
    />
    <view class="container">
      <search
        custom-class="search-input"
        bind:search="handleSearch"
        bind:change="onChange"
        bind:blur="onBlur"
        shape="round"
        keyword="{{search}}"
        placeholder="搜索"
      />

      <view class="shop-list">
        <panel
          wx:for="{{ shopList }}"
          wx:key="storeKdtId"
          use-header-slot
          bindtap="handleChooseShop"
          data-index="{{ index }}"
        >
          <view
            slot="header"
            class="header"
          >
            <view class="title">
              <text class="ellipsis" i18n-off>{{ item.storeName }}</text>
              <view
                class="distance"
                wx:if="{{ item.distance }}"
              >{{ item.distance }} {{ item.unit }}</view>
            </view>
            <text class="address">{{ item.storeAddress }}</text>
            <progress-banner
              wx:if="{{item.queueData}}"
              banner-container="banner-container"
              sourceDataFromExternal="{{item.queueData}}"
            />
          </view>
        </panel>
      </view>

      <view
        wx:if="{{ search.length > 0 && shopList.length === 0 }}"
        class="empty-container"
      >
        <text class="empty-text">暂无相关门店</text>
      </view>
    </view>
  </view>
</page-container>
