import WscPage from 'pages/common/wsc-page/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { queryOpenShops } from 'retail/util/api';
import { gcjToBaidu } from '@/helpers/lbs';
import { fetchShopList, batchQueryQueueToday } from './api';
import { askLocationPermission } from '../dashboard/api';

const app = getApp();

// 用于分页数据加载的方式
const AddDataType = {
  AppendMore: 'AppendMore',
  ReplaceData: 'ReplaceData',
};

WscPage({
  data: {
    shopList: [],
    currentCityCode: -1,
    search: '',
    showMsg: '',
    loading: true,
    globalCustomLoading: app.globalData.globalCustomLoading,
  },

  currentPageNo: 1,
  totalPageNo: 1,

  onLoad({ showMsg = '' }) {
    app.setLangNavTitle('门店列表');
    this.setData({
      showMsg,
    });

    askLocationPermission()
      .then(
        ({ latitude, longitude }) => {
          // 微信获取的高德坐标系 转 百度坐标系
          const { lng, lat } = gcjToBaidu(longitude, latitude);
          this.geo = { lon: lng, lat };
        },
        () => (this.geo = {})
      )
      .then(() => this._doSearch());
  },

  onReachBottom() {
    if (this.currentPageNo < this.totalPageNo) {
      this.currentPageNo += 1;
      this._doSearch(AddDataType.AppendMore);
    }
  },

  onChange({ detail: value }) {
    this.setData({
      search: value,
    });
    if (!value) {
      this.setData({
        shopList: [],
      });
    }
  },

  onBlur() {
    this.currentPageNo = 1;
    this._doSearch();
  },

  handleSearch({ detail = {} }) {
    const value = detail?.value ?? this.data.search;
    this.setData({
      search: value,
    });

    this.currentPageNo = 1;
    this._doSearch();
  },

  switchShop(shopInfo) {
    const { storeKdtId, distance, storeAddress, logo, storeName } = shopInfo;

    // 切换店铺清除缓存
    app.prefetchGoodsGroupsCache = null;
    app.prefetchGoodsListPromise = null;

    app.logger.appError({
      name: 'retail_offline_shop_switch',
      message: '切换门店',
      detail: {
        switchToKdtId: storeKdtId,
        switchToStoreName: storeName,
      },
    });

    app.updateOfflineKdtId(storeKdtId).then(() => {
      Object.assign(app.getRetailConfig()?.shopInfo ?? {}, {
        logo: cdnImage(logo, '!64x64+2x.jpg'),
        shopName: storeName,
        location: storeAddress,
        distance,
      });

      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.redirectTo({
        url: `/pages-retail/dashboard/index?s=2&kdt_id=${storeKdtId}`,
      });
    });
  },

  handleChooseShop({ currentTarget }) {
    const { dataset = {} } = currentTarget;
    const shopInfo = this.data.shopList[dataset.index];

    const { storeKdtId, distance } = shopInfo;

    if (storeKdtId) {
      // 距离大于 5 km
      if (distance > 5) {
        wx.showModal({
          title: '您当前所在位置距离该店较远，是否继续？',
          success: (res) => {
            if (res.confirm) {
              this.switchShop(shopInfo);
            }
          },
        });
      } else {
        this.switchShop(shopInfo);
      }
    } else {
      wx.showToast({
        icon: '',
        title: '该店铺信息不全，请联系店家',
      });
    }
  },

  _doSearch(status = AddDataType.ReplaceData) {
    const keyword = this.data.search || '';
    const rootKdtId = app.getHQKdtId();
    if (!this.data.loading || !this.data.globalCustomLoading) {
      wx.showLoading();
    }
    const handleShopList = async ({ items: shopList, paginator }) => {
      const { pageSize, totalCount } = paginator;
      this.totalPageNo = Math.ceil(totalCount / pageSize);

      try {
        // 扫码点单&自助结账：门店控制
        const openedShops = await queryOpenShops().catch(() => ({
          joinType: 0,
          subKdtIds: [],
        }));
        const { joinType, subKdtIds = [] } = openedShops;
        let formatedShopList = shopList
          .filter(({ storeKdtId }) => {
            if (joinType && subKdtIds.length) {
              return (
                storeKdtId !== app.getOfflineKdtId() &&
                ~subKdtIds.indexOf(storeKdtId)
              );
            }
            return storeKdtId !== app.getOfflineKdtId();
          })
          .map((shop) => ({
            ...shop,
            unit: 'km',
            distance: shop.distance,
          }));
        // 扫码点单：茶饮排队
        if (formatedShopList.length && app.globalData.scene == 2) {
          try {
            const { queueSituations = [] } = await batchQueryQueueToday(
              formatedShopList.map((item) => item.storeKdtId)
            );
            formatedShopList.forEach((item) => {
              const targetQueueData = queueSituations.find(
                (queue) => queue.kdtId == item.storeKdtId
              );
              if (targetQueueData) {
                item.queueData = targetQueueData;
              }
            });
            // 是否是分页请求的数据
            if (status === AddDataType.AppendMore) {
              formatedShopList = [...this.data.shopList, ...formatedShopList];
            }
          } catch (e) {
            wx.showToast({
              title: e.msg || '获取店铺茶饮排队信息失败',
              icon: 'none',
            });
          }
        }

        // 避免重复渲染
        const dontRender =
          this.data.shopList.length === formatedShopList.length &&
          this.data.shopList.every(
            (shop, i) => shop.storeKdtId === formatedShopList[i].storeKdtId
          );
        if (dontRender) {
          wx.hideLoading();
          return;
        }

        // render data
        this.setData({
          shopList: formatedShopList,
        });
      } catch (error) {
        wx.showToast({
          title: error.msg || error.message || '无法加载门店列表',
          icon: 'none',
        });
      } finally {
        this.setData({
          loading: false,
        });
        wx.hideLoading();
      }
    };

    const { lon, lat } = this.geo || {};
    const hasLocation = lon && lat;

    return fetchShopList({
      store_name: keyword,
      store_statuses: ['try', 'valid', 'protect'],
      kdt_id: rootKdtId,
      ...(hasLocation && {
        sort_lon: lon,
        sort_lat: lat,
        page_size: 40,
        page_no: this.currentPageNo,
        sort_type: 1,
      }),
    })
      .then(handleShopList)
      .catch((err) => {
        this.setData({
          loading: false,
        });
        wx.hideLoading();
        wx.showToast({
          title: err?.msg ?? '无法加载门店列表',
          icon: 'none',
        });
      });
  },
});
