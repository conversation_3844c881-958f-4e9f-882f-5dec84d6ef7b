.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  width: 80%;
  font-weight: 500;
}

.search-input .van-cell {
  border-radius: 16px !important;
}

.header {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  line-height: 1.4;
  padding: 12px 0;
  position: relative;
}

.title {
  color: #323233;
  font-size: 32rpx;
  line-height: 1.3;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.distance {
  font-size: 24rpx;
}

.address {
  font-size: 24rpx;
  color: #969799;
  margin-top: 16rpx;
  line-height: 1.5;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shop-list {
  padding: 0 15px;
  background-color: #fff;
}

.empty-container {
  margin-top: 90rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.banner-container {
  margin: 24rpx 0;
}