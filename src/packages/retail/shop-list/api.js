import promisify from 'utils/promisify';

const app = getApp();
const carmen = promisify(app.carmen);

export const fetchShopList = data => {
  return carmen({
    api: 'youzan.retail.shop.hq.consumer/1.0.0/search',
    data: {
      ...data,
      is_offline_open: true
    }
  });
};

// 查询扫码点单：当前订单茶饮排队情况
export function batchQueryQueueToday(kdtIds = []) {
  return app.request({
    path: '/retail/h5/miniprogram/batchQueryQueueToday.json',
    data: {
      kdtIds
    }
  });
}
