// 515 储值优惠
export const hasDiscount = (promotion) => {
  return promotion.some((item) => +item.type === 515);
};

export const getRecommend = (data) => {
  const { customerCardBaseInfoDTO } = data;
  const { recommendDetaid, ...rest } = data.customerCardBaseInfoDTO || {};

  let detail = {};
  try {
    detail = JSON.parse(recommendDetaid);
  } catch {}

  return {
    ...rest,
    recommendDetaid: detail,
  };
};
