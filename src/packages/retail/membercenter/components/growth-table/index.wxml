<view class="scrm-table">
  <view class="scrm-table__tr">
    <view wx:for="{{ columns }}" wx:key="index" class="scrm-table__th scrm-table__column__{{ index + 1 }}">
      {{ item.title }}
    </view>
  </view>
  <view wx:if="{{ datasets.length === 0 }}" class="scrm-table__empty">
    暂无数据
  </view>
  <view wx:else class="scrm-table__tbody">
    <view wx:for="{{ datasets }}" wx:for-item="data" wx:key="index" class="scrm-table__tr">
      <view wx:for="{{ columnsKeys }}" wx:for-item="key"  wx:key="index" class="scrm-table__td scrm-table__column__{{ index + 1 }}">
        {{ data[key] }}
      </view>
    </view>
  </view>
</view>