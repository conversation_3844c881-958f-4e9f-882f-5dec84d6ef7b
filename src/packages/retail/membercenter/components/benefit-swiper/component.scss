.benefit-popup {

    &__single {
        margin: 0 auto;
    }

    &__swiper {
        height: 410px;
    }

    &__container {
        position: relative;
        width: 300px;
        padding: 10px;
        background: #fff;
        border-radius: 8px;
        transition: transform 0.5s;
    }

    &__name {
        font-size: 20px;
        line-height: 40px;
        font-weight: 500;
    }

    &__header {
        color: #B59258;
        padding: 0 10px;
    }

    &__body {
        background: #FCFBF7;
        padding: 10px;
        margin: 10px;
        height: 310px;
        font-size: 14px;
        overflow-y:scroll;


        &-title {
            font-size: 16px;
            font-weight: 500;
            margin-left: 8px;
        }

        &-desc {
            margin-top: 10px;
        }
    }

    &__btn {
        margin-top: 20px;
        background: linear-gradient(to left top, #C49542, #F1CF64);
        font-size: 14px;
        border-radius: 22px;
        width: 160px;
        height: 44px;
        line-height: 44px;
        font-size: 14px;
    }

    &__icon {
        color: #CAAB65;
        background: #FBF7EC;
        height: 35px;
        width: 35px;
        padding: 3px;
        font-size: 28px;
        border-radius: 50%;
        text-align: center;
        float: right;

        background-repeat: no-repeat;
        background-position: center;
        background-size: 25px;
    }

    &__records {
        display: inline-block;
        width: 11px;
        height: 14px;
        background: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>') no-repeat center;
        background-size: 11px 14px;
    }

    &__block {
        margin-top: 15px;
    }

    &__gift {
        margin-top: 15px;
    }

    &__empty {
        margin-left: 10px;
    }

    &__remark {
        margin-top: 10px;
    }
}

.membercenter-icon {
    &__festival {
        background-image: url("https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>");
    }

    &__member-day {
        background-image: url("https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>");
    }

    &__birthday {
        background-image: url("https://b.yzcdn.cn/retail/img/member/<EMAIL>");
    }
}

.benefit-custom {
    &__item {
        background:rgba(255, 255, 255, 0.9);
        margin: 10px 0px;
        padding: 10px 7px;
        border-radius: 5px;
        color: #666;
    }
}