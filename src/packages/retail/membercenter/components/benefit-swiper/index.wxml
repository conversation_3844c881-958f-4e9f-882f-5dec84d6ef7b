<view class="benefit-popup">
  <view class="benefit-wapper">
  <swiper
    class="benefit-popup__swiper"
    previous-margin="20px"
    next-margin="20px"
    bind:change="handleSwiperChange"
    current="{{ currentBenefitSwiper}}"
  >
    <block wx:for="{{ benefits }}" wx:key="benefitName">
      <swiper-item>
        <view class="benefit-popup__container {{ benefits.length === 1 ? 'benefit-popup__single' : '' }}">
          <view class="benefit-popup__header">
            <text class="benefit-popup__name">{{ item.benefitName }}</text>
            <view wx:if="{{ item.benefitIcon }}" class="benefit-popup__icon">
              <van-icon name="{{ item.benefitIcon }}" custom-style="margin-top: 4px;"  size="28px" />
            </view>
            <view wx:else class="{{'membercenter-icon__' + item.benefitIconStyle }} benefit-popup__icon" />
          </view>
          <view class="benefit-popup__body">
            <view class="benefit-popup__body-header">
              <view class="benefit-popup__records" />
              <text class="benefit-popup__body-title">权益介绍</text>
            </view>
            <view wx:if="{{ !item.specialBenefit }}" class="benefit-popup__body-desc">
              {{ item.benefitContent }}
            </view>
            <view wx:elif="{{ item.benefitName === '节日特权'}}" class="benefit-festival">
              <template wx:for="{{ item.festivalBenefits }}" wx:for-item="data" is="benefit-custom" data="{{ item: data, total: item.festivalBenefits.length }}" wx:key="index"></template>
            </view>
            <view wx:else class="benefit-popup__body-desc">
              <template is="benefit-custom" data="{{ item }}"></template>
            </view>
          </view>
        </view>
      </swiper-item>
    </block>
  </swiper>
  <button class="benefit-popup__btn" bind:tap="handlePopClose">
    了解了
  </button>
  </view>
</view>

<template name="benefit-custom">
  <view class="{{ item.festivalName && total > 1 ? 'benefit-custom__item' : '' }}">
    <view>
      <block wx:if="{{ item.specialName }}">{{ item.specialName }}{{ item.benefitContent.benefitTerm }}</block>
      <block wx:else>{{ item.festivalName ? '【' + item.festivalName +  '】' : '' }}{{ item.benefitContent.benefitTerm }}</block>，专享以下特权：
      <view wx:if="{{ item.benefitContent.benefitBag.length > 0 }}" class="benefit-popup__block">
        <view
          wx:for="{{ item.benefitContent.benefitBag }}"
          wx:for-item="benefit"
          wx:key="index"
        >
          · {{ benefit }}
        </view>
      </view>
      <view wx:else class="benefit-popup__block">
        暂无特权
      </view>
    </view>
    <view class="benefit-popup__gift">
      额外获赠：
      <view wx:if="{{ item.benefitContent.giftBag.length > 0 }}" class="benefit-popup__block">
        <view
          wx:for="{{ item.benefitContent.giftBag }}"
          wx:for-item="gift"
          wx:key="index"
        >
          · {{ gift }}
        </view>
      </view>
      <view wx:else class="benefit-popup__block benefit-popup__empty">
        暂无赠送内容
      </view>
    </view>
    <view class="benefit-popup__remark">{{ item.isFromConfig ? '以实际领取到权益为准' : '' }}</view>
  </view>
</template>