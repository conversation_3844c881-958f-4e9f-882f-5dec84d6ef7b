import WscComponent from 'pages/common/wsc-component/index';
import { joinMember } from 'retail/util/bind-mobile';
import navigate from '@/helpers/navigate';

const app = getApp();

WscComponent({
  properties: {
    levelInfo: Object,
    isMember: Boolean,
    userInfo: Object,
    currentLevel: Number,
    maxLevel: Number,
    mobile: String,
    type: String,
    originKdtId: Number,
    memberSrcWay: Number,
    memberSrcChannel: Number,
    pointsName: {
      type: String,
      value: '积分'
    }
  },

  data: {
    upgradeGiftBagText: '升级礼包：暂无',
    updatedLevelInfo: null,
    style: ''
  },

  ready() {
    const { currentLevel, levelInfo } = this.data;

    const data = {
      logo: app.getShopInfoSync().base.logo
    };

    if (levelInfo) {
      const { cover_url: coverUrl } = levelInfo;
      const style = coverUrl ? `background-image: url(${coverUrl})` : '';
      data.style = style;
    }

    if (levelInfo && currentLevel < levelInfo.vip_level) {
      const {
        member_upgrade_gift_bag: { points, coupon_num: couponNum }
      } = levelInfo;
      let upgradeGiftBagText = '';
      if (points) {
        upgradeGiftBagText += `${points}${this.data.pointsName} `;
      }

      if (couponNum) {
        upgradeGiftBagText += `${couponNum}优惠券`;
      }

      if (upgradeGiftBagText) {
        data.upgradeGiftBagText = `升级礼包：${upgradeGiftBagText}`;
      }
    }
    this.setYZData(data);
  },

  methods: {
    /**
     * 有手机号，不是会员
     */
    joinMemberWithMobile() {
      const { originKdtId, memberSrcWay, memberSrcChannel } = this.data;

      joinMember(() => this.triggerEvent('reload'), {
        kdt_id: originKdtId || app.getKdtId(),
        member_src_way: memberSrcWay || 800,
        member_src_channel: memberSrcChannel || 1000,
        need_be_member: true
      });
    },

    redirectToMemberCenter() {
      const { type } = this.data;
      if (type === 'single') {
        navigate.navigate({
          url: '/packages/retail/membercenter/index'
        });
      }
    },

    handleQrcodePopup() {
      this.triggerEvent('handleQrcodeShow');
    }
  }
});
