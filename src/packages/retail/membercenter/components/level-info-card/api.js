import { promisifiedCarmen } from 'utils/promisified-api';

export default {
  bindMobileWithYzAccountAndJoinMember(data) {
    return promisifiedCarmen({
      api: 'youzan.retail.scrm.customer.mobile/1.0.0/bind',
      data,
      method: 'POST',
    });
  },

  getCustomerLevels(data) {
    return promisifiedCarmen({
      api: 'youzan.retail.scrm.level.shoplist/1.0.0/get',
      data
    });
  },

  joinMemberWithMobile(data) {
    return promisifiedCarmen({
      api: 'youzan.retail.scrm.customer.member/1.0.0/become',
      data
    });
  }
};
