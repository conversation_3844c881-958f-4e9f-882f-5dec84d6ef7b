
.zan-popup__container {
    width: 100%;
}

.level-info {
    position: relative;
    margin: 0 auto;
    width: 100%;
    height: 150px;
    color: #fff;
    border-radius: 8px;
    box-shadow: 0px 4px 10px rgba(0, 0 , 0, 0.1);

    &__cover {
        &-vip1 {
            background: linear-gradient(to left top, #C49542, #F1CF64);
        }

        &-vip2 {
            background: linear-gradient(to left top, #37404B, #8699AC);
        }

        &-vip3 {
            background: linear-gradient(to left top, #BA3D58, #E37A91);
        }

        &-vip4 {
            background: linear-gradient(to left top, #1558A6, #5599CE);
        }

        &-vip5 {
            background: linear-gradient(to left top, #167A5F, #4FB288);
        }

        &-vip6 {
            background: linear-gradient(to left top, #825ADA, #482C9D);
        }

        &-vip6 {
            background: linear-gradient(to left top, #C49542, #F1CF64);
        }

        &-vip7 {
            background: linear-gradient(to left top, #901D92, #B541B2);
        }

        &-vip8 {
            background: linear-gradient(to left top, #A92D2D, #ED5F5F);
        }

        &-vip9 {
            background: linear-gradient(to left top, #D17007, #ECB03B);
        }

        &-vip10 {
            background: linear-gradient(to left top, #000000, #403312);
        }
    }

    &__default {
        background-size: 90%;
        background-repeat: no-repeat;
        background-position: 170% 20%;
        background-color:#5d5f67;
        background-image: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
    }

    &__background {
        background-size: cover;
    }
  
    &__name {
        font-size: 16px;
        display: inline-block;
    }

    &__container {
        padding: 20px;
        height: 110px;
    }

    &__inner {
        width: 100%;
    }

    &__customer {
        margin: 0 auto;

        &-text {
            margin: 0 auto;
            padding-top: 8%;
            text-align: center;
            color: #fff;
            font-size: 16px;
        }

        &-action {
            width: 130px;
            font-size: 12px;
            margin: 12% auto 0 auto;
            background-color: #cfcfcf;
            color: #5e6068;
            border-radius: 24px;
            line-height: 24px;
        }

    }

    &__member {

        &-header {

            &__desc {
                display: inline-block;
                margin-left: 10px;
                font-size: 16px;
                vertical-align: top;
            }
        }

        &-icon {
            display: inline-block;
            width: 16px;
            height: 12px;
            background: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>') no-repeat center;
            background-size: 16px 12px;
        }
        
        &-vip {
            margin-left: 3px;
            display: inline-block;
            font-size: 16px;
        }

        &-content {
            text-align: center;
            font-size: 12px;
            padding: 5px 17px;
            background-color: rgba(0, 0, 0, .1);
            margin: 6.5% auto 0;
            border-radius: 13px;
            width: 200px;
        }

        &-mobile {
            font-size: 12px;
        }

        &-tip {
            width: 100%;
            position: absolute;
            bottom: 20px;
            font-size: 12px;
            view {
                display: inline-block;
            }
        }

        &-growth {
            margin-left: 5px;
        }

        &-right {
            position: absolute;
            right: 12%;
        }
    }

    &__current {
        display: inline-block;
        margin-left: 5px;
        font-size: 16px;
        vertical-align: top;

        &-qrcode {
            float: right;
            width: 20px;
            height: 20px;
            margin-top: 5px;
            background: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>') no-repeat center;
            background-size: 40px;
        }
    }

    &__avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: inline-block;
        overflow: hidden;
    }
}
