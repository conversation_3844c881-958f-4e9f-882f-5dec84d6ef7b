<view class="level-info {{ levelInfo.cover_url ? 'level-info__background' : levelInfo.vip_level ? 'level-info__cover-vip' + levelInfo.vip_level  : 'level-info__default' }}" style="{{ style }}" bind:tap="redirectToMemberCenter">
  <view class="level-info__container">
    <view wx:if="{{ isMember }}">
      <view wx:if="{{ levelInfo.vip_level === currentLevel }}" class="level-info__inner">
        <view class="level-info__member-header">
          <view class="level-info__avatar">
            <open-data type="userAvatarUrl"></open-data>
          </view>
          <view class="level-info__member-header__desc">
            {{ levelInfo.vip_level_name }}
            <view class="level-info__member-icon" />
            <view class="level-info__member-vip">{{ levelInfo.vip_level }}</view>
            <view class="level-info__member-mobile">{{ mobile ? mobile : '' }}</view>
          </view>
          <view class="level-info__current-qrcode" catch:tap="handleQrcodePopup" />
        </view>
      </view>
      <view wx:else>
        <view class="level-info__current">
          <view class="level-info__name">{{ levelInfo.vip_level_name }}</view>
          <view class="level-info__member-icon" />
          <text class="level-info__member-vip">{{ levelInfo.vip_level }}</text>
        </view>
        <view class="level-info__member-content">{{ upgradeGiftBagText }}</view>
      </view>
      <view class="level-info__member-tip">
        <view class="level-info__member-growth">当前成长值 {{ levelInfo.currentGrowth }}</view>
        <view wx:if="{{ levelInfo.vip_level === maxLevel && levelInfo.vip_level === currentLevel }}" class="level-info__member-right">
          已是最高等级
        </view>
        <view wx:else class="level-info__member-right">
          升级到{{ levelInfo.vip_level === currentLevel ? 'VIP' + (levelInfo.vip_level + 1)  : '该等级' }} 还需 {{ levelInfo.vip_level === currentLevel ? levelInfo.nextLevelGrowth - levelInfo.currentGrowth : levelInfo.toThisLevelGrowth }}
        </view>
      </view>
    </view>
    <view wx:else class="level-info__customer">
      <view class="level-info__customer-text">成为店铺会员，尊享会员权益</view>
      <block wx:if="{{ !mobile }}">
        <view class="level-info__customer-action">
          <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="joinMemberWithMobile">
            领取权益
          </user-authorize>
        </view>
      </block>
      <!-- <button wx:if="{{ !mobile }}" class="level-info__customer-action" open-type="getPhoneNumber" catch:tap="noop" bindgetphonenumber="getPhoneNumber">
        绑定手机成为会员
      </button> -->
      <button wx:else class="level-info__customer-action" bind:tap="joinMemberWithMobile">
        领取权益
      </button>
    </view>
  </view>
</view>