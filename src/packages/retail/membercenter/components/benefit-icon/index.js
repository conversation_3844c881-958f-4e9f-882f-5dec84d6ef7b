import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    benefit: Object
  },

  // ready() {
  //   const { benefit: { type, value } } = this.data;
  //   const benefitName = benefitEnums[type];
  //   const benefitDesc = benefitDescTpl[type] ? benefitDescTpl[type](value) : '';
  //   const benefitIcon = benefitIconMap[type];
  //   this.setData({
  //     benefitName,
  //     benefitDesc,
  //     benefitIcon
  //   });
  // },


  // methods: {
  //   handleBenefitIconDesc(type, value) {
  //     console.log('type', type, value);
  //   }
  // }

});
