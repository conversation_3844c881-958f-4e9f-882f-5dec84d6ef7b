<view class="membercenter-icon">
  <view wx:if="{{ benefit.benefitIcon }}" class="membercenter-icon__item">
    <van-icon name="{{ benefit.benefitIcon }}" custom-style="margin-top: 4px;" size="28px" />
  </view>
  <view wx:else class="{{'membercenter-icon__' + benefit.benefitIconStyle }} membercenter-icon__item" />
  <view class="membercenter-icon__name zan-font-14">{{ benefit.benefitIconTitle }}</view>
  <view class="membercenter-icon__desc zan-font-12">{{ benefit.benefitDesc }}</view>
</view>