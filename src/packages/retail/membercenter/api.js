import { promisifiedCarmen } from 'utils/promisified-api';

export default {
  getUserInfo(data) {
    return promisifiedCarmen({
      api: 'youzan.retail.scrm.customer.assets/1.0.0/get',
      data
    });
  },

  getCustomerLevels(data) {
    return promisifiedCarmen({
      api: 'youzan.retail.scrm.level.shoplist/1.0.0/get',
      data
    });
  },

  getExtraBenefits(data) {
    return promisifiedCarmen({
      api: 'youzan.retail.scrm.member.activity.c.privileges/1.0.0/get',
      data
    });
  }
};
