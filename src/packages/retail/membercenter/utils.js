/**
 * 转换权益对应的业务描述（图标、名称、内容）
 */
const STATS_NAME = {
  points: '积分'
};

function setStatsName(names) {
  Object.keys(names).forEach(name => {
    STATS_NAME[name] = names[name];
  });
}

function handleBenefitBizData(type, value) {
  const benefitDescTpl = {
    free_post: () => '免运费',
    discount: value => `立享${value / 10}折`,
    points_rate: value => `${value / 10}倍${STATS_NAME.points}`,
    member_day: () => '会员日钜惠',
    festival: () => '节日钜惠'
  };

  const commonBenefitEnums = {
    free_post: '会员包邮',
    discount: '专享折扣',
    member_day: '会员日特权',
    festival: '节日特权'
  };

  const benefitNameEnums = Object.assign({}, commonBenefitEnums, {
    points_rate: `多倍${STATS_NAME.points}回馈`
  });

  const benefitIconEnums = Object.assign({}, commonBenefitEnums, {
    points_rate: `${STATS_NAME.points}回馈`
  });

  const benefitIconMap = {
    free_post: 'free-postage',
    points_rate: 'points',
    discount: 'discount'
  };

  const benefitIconStyle = {
    member_day: 'member-day',
    festival: 'festival'
  };

  const benefitContentMap = {
    free_post: '会员下单时，享受免运费权益',
    discount:
      '如果商品支持消费者使用折扣，那么会员在下单时，将享受会员等级对应的折扣权益',
    points_rate: `会员在消费时，将根据积分规则和倍率权益，发放多倍${STATS_NAME.points}`
  };

  return {
    benefitName: benefitNameEnums[type],
    benefitDesc: benefitDescTpl[type] ? benefitDescTpl[type](value) : '',
    benefitIcon: benefitIconMap[type],
    benefitIconStyle: benefitIconStyle[type] || '',
    benefitIconTitle: benefitIconEnums[type],
    benefitContent: benefitContentMap[type]
  };
}

const birthdayBenefitDescTpl = {
  free_post: () => '包邮',
  discount: value => `${value / 10}折`,
  points_rate: value => `${value / 10}倍${STATS_NAME.points}回馈`,
  point_rate: value => `${value / 10}倍${STATS_NAME.points}回馈`
};

function handleBirthdayBenefit(benefitBag = {}) {
  const {
    discount,
    free_post: freePost,
    points_rate: pointsRate,
    stock_type: stockType,
    stock
  } = benefitBag;
  const benefitDescArray = [];
  if (freePost) {
    benefitDescArray.push(birthdayBenefitDescTpl.free_post());
  }

  if (discount) {
    benefitDescArray.push(birthdayBenefitDescTpl.discount(discount));
  }

  if (pointsRate) {
    benefitDescArray.push(birthdayBenefitDescTpl.points_rate(pointsRate));
  }

  return [
    benefitDescArray.join('、') || '暂无特权',
    `剩余使用次数：${stockType === 0 ? '无限' : stock}次`
  ];
}

function handleBirthdayGift(giftBag = {}) {
  const {
    points,
    coupon_list: couponList,
    benifit_card_d_t_o: benefitCardDto,
    gift_benefit_card_right_info: giftBenfitCardRightInfo,
    growth_value: growthValue
  } = giftBag;

  const giftDescArray = [];

  if (points) {
    giftDescArray.push(`${points}${STATS_NAME.points}`);
  }

  if (benefitCardDto || giftBenfitCardRightInfo) {
    const cardDesc = Object.keys(giftBenfitCardRightInfo)
      .map(benefit => {
        const value = giftBenfitCardRightInfo[benefit];
        return birthdayBenefitDescTpl[benefit]
          ? birthdayBenefitDescTpl[benefit](value)
          : '';
      })
      .filter(i => i)
      .join('/');

    giftDescArray.push(`1张卡${cardDesc ? `：${cardDesc}` : ''}`);
  }

  if (growthValue) {
    giftDescArray.push(`${growthValue}成长值`);
  }

  if (couponList && Array.isArray(couponList) && couponList.length > 0) {
    let couponNum = 0;
    const couponDesc = couponList
      .map(item => {
        couponNum += item.number;
        return `${item.number}张「${item.coupon_name}」`;
      })
      .join('、');
    giftDescArray.push(`${couponNum}张券：${couponDesc}`);
  }

  return giftDescArray;
}

const termTypeEnum = {
  1: '当天',
  2: '当周',
  3: '当月'
};

const memberdayTerm = {
  1: '全天',
  2: '每周',
  3: '每月'
};

const handleMembedayBenefit = data => {
  const {
    banner,
    benefit_bag: benefitBag,
    gift_bag: giftBag,
    term_type: termType,
    isFromConfig
  } = data;
  const handledBenefitBag = handleBirthdayBenefit(benefitBag);
  const handledGiftBag = handleBirthdayGift(giftBag);
  return {
    benefitName: '会员日特权',
    benefitIconTitle: '会员日钜惠',
    benefitDesc: `${memberdayTerm[termType]}会员日钜惠`,
    benefitIconStyle: 'member-day',
    specialBenefit: true,
    specialName: '会员日',
    isFromConfig,
    benefitContent: {
      benefitBag: handledBenefitBag,
      giftBag: handledGiftBag,
      benefitTerm: banner
    }
  };
};

const handleFestivalBenefit = (data = []) => {
  const festivalBenefits = data.map(benefit => {
    const {
      benefit_bag: benefitBag,
      gift_bag: giftBag,
      festival_name: festivalName,
      activity_alias: activityAlias,
      banner: benefitTerm = '当天',
      isFromConfig
    } = benefit;

    const handledBenefitBag = handleBirthdayBenefit(benefitBag);
    const handledGiftBag = handleBirthdayGift(giftBag);
    return {
      festivalName,
      activityAlias,
      isFromConfig,
      benefitContent: {
        benefitTerm,
        benefitBag: handledBenefitBag,
        giftBag: handledGiftBag
      }
    };
  });
  return {
    benefitName: '节日特权',
    benefitIconTitle: '节日特权',
    benefitDesc: '节日钜惠',
    benefitIconStyle: 'festival',
    specialBenefit: true,
    festivalBenefits
  };
};

const handleBirthdayInfo = (birthdayBenefitBag, birthdayGiftBag, term) => {
  const benefitBag = handleBirthdayBenefit(birthdayBenefitBag);
  const giftBag = handleBirthdayGift(birthdayGiftBag);
  return {
    specialName: '生日',
    specialBenefit: true,
    benefitName: '生日特权',
    benefitIconTitle: '生日特权',
    benefitDesc: `生日${termTypeEnum[term]}钜惠`,
    benefitIconStyle: 'birthday',
    benefitContent: {
      benefitBag,
      giftBag,
      benefitTerm: termTypeEnum[term]
    }
  };
};

const unionFestivalByAlias = (realFestival = [], configFestival = []) => {
  const unionedFestival = [];
  for (let i = 0; i < configFestival.length; i++) {
    const configFestivalItem = configFestival[i];
    if (realFestival.length) {
      for (let j = 0; j < realFestival.length; j++) {
        if (
          configFestivalItem.activity_alias !== realFestival[j].activity_alias
        ) {
          unionedFestival.push(configFestivalItem);
        }
      }
    } else {
      unionedFestival.push(configFestivalItem);
    }
  }
  const sortedFestival = unionedFestival
    .concat(realFestival)
    .sort((prev, next) => prev.festival_start_time - next.festival_start_time);
  return sortedFestival;
};

export {
  handleBenefitBizData,
  handleBirthdayBenefit,
  handleBirthdayGift,
  handleMembedayBenefit,
  handleFestivalBenefit,
  handleBirthdayInfo,
  unionFestivalByAlias,
  setStatsName,
  termTypeEnum
};
