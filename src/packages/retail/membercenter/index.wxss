@import "shared/common/css/helper/index.wxss";

.scroll-disable {
    position: fixed;
    top: 0;
    bottom: 0;
}

.membercenter-background {
    height: 200px;
    background: url('https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>') center no-repeat;
    z-index: -1;
}

.membercenter-cards {
    background: #fff;
}

.membercenter-background__arc {
    margin-top: -55px;
    height: 55px;
    overflow: hidden;
}

.membercenter-swiper {
    width: 90%;
    height: 155px;
    position: relative;
    margin: 0 auto;
    margin-top: -155px;
}

.membercenter-background__arc-image {
    width: 100%;
}

.membercenter-block {
    background: #fff;
    padding: 10px;
}

.membercenter-table {
    padding: 10px 20px;
}

.membercenter-block:last-child {
    margin-bottom: 0;
}

.membercenter-benefit__container {
    display: flex;
}

.membercenter-benefit__container .membercenter-benefit__item {
    display: inline-block;
    width: 25%;
}

.membercenter-benefit__popup {
    width: 100%;
    background: none;
}

.membercenter-swiper__scale {
    transform: translate(100%, 0px) translateZ(0px) scale(0.9) !important;
}

.membercenter-cards {
    transition: transform 0.5s;
}

.membercenter-growth__table {
    margin-bottom: 30px;
}

.membercenter-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);   
}

.membercenter-divider {
    height: 10px;
    width: 100%;
    background: #F8F8F8;
}

.membercenter-newswiper__wrapper {
    height: 155px;
}

.membercenter-newswiper__container {
    position: relative;
    margin-top: -155px;
}

.membercenter-newswiper__item {
    float: left;
}

.membercenter-benefit__dot {
    width: 16px;
    border-radius: 100%;
    margin: 0 auto;
}

.membercenter-benefit__dot-container {
    display: inline-block;
    width: 8px;
    height: 4px;
}

.membercenter-benefit__dot-active {
    background: #CAAB65;
}

.membercenter-benefit__dot-unactive {
    background: #E5E5E5;
}

.membercenter-icon__swiper {
    height: 100px;
}