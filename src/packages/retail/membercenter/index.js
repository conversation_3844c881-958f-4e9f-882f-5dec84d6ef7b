import WscPage from 'pages/common/wsc-page/index';
import {
  notifyOfflineMemberSignFailed,
  signSceneCallback
} from 'retail/util/bind-mobile';
import { questions } from './constants';
import {
  handleBenefitBizData,
  handleBirthdayBenefit,
  handleBirthdayGift,
  handleMembedayBenefit,
  handleFestivalBenefit,
  handleBirthdayInfo,
  termTypeEnum,
  setStatsName,
  unionFestivalByAlias
} from './utils';
import api from './api';

const columns = [
  {
    title: '等级',
    name: 'vip_level_name_column'
  },
  {
    title: '对应成长值',
    name: 'growth_value'
  }
];

const app = getApp();

WscPage({
  data: {
    questions,
    benefits: [],
    birthdayPrivilege: null, // 用户已领的生日特权
    realMemberdayPrivilege: null,
    realFestivalPrivilege: [],
    columns,
    levels: [],
    isMember: false,
    mobile: '',
    current: 0, // swiper当前index
    currentLevel: null, // 用户目前等级
    currentSwiperLevel: 1, // swiper 滑动到的等级
    currentBenefitSwiper: 0,
    currentIconSwiper: 0,
    levelName: '',
    maxLevel: null,
    levelQrcode: '',
    levelBarcode: '',
    showQrcodePopup: false,
    showBenefitPopup: false,
    userInfo: {},
    loading: true,
    winWidth: 0, // 屏幕宽度
    translateDistance: 0,
    animationToLarge: {},
    animationToSmall: {},
    originKdtId: '',
    memberSrcWay: '',
    memberSrcChannel: '',
    statsNames: {
      points: '积分'
    }
  },

  handleQrcodePopup() {
    this.setYZData({
      showQrcodePopup: !this.data.showQrcodePopup
    });
  },

  toggleBenefitPopup({
    currentTarget: {
      dataset: { index }
    }
  }) {
    if (!this.data.showBenefitPopup) {
      this.setYZData({
        currentBenefitSwiper: index
      });
    }
    this.setYZData({
      showBenefitPopup: !this.data.showBenefitPopup
    });
  },

  handleQrcodeShow() {
    const {
      userInfo: { member_level_right: { identity_no: identityNo } = {} }
    } = this.data;

    if (!identityNo) {
      api
        .getUserInfo({
          kdt_id: app.getKdtId(),
          need_query_member_benefits: true,
          need_query_growth_value: true,
          need_query_birthday_privilege: true
        })
        .then(userInfo => {
          this.setYZData({
            userInfo
          });
        })
        .catch(err => {
          wx.showToast({
            title: err.msg || '获取用户信息失败',
            icon: 'none',
            duration: 2000
          });
        });
    } else {
      this.setYZData({
        showQrcodePopup: true,
        identityNo: `${identityNo}`
      });
    }
  },

  fetchPointsName() {
    return app
      .getPointsName()
      .then(({ pointsName = '积分' }) => {
        setStatsName({ points: pointsName });
        this.setData({
          'statsNames.points': pointsName
        });
      })
      .catch(() => {});
  },

  initPage() {
    const kdtId = app.getKdtId();
    const signQrcodeKey = app.globalData.signQrcodeKey;
    const isFirstSign = app.globalData.fristSign;

    const defaultParams = {
      need_query_member_benefits: true,
      need_query_growth_value: true,
      need_query_birthday_privilege: true,
      kdt_id: kdtId
    };

    if (isFirstSign && signQrcodeKey) {
      defaultParams.qr_code = signQrcodeKey;
      defaultParams.member_src_way = 800;
      defaultParams.member_src_channel = 1000;
      defaultParams.kdt_id = app.globalData.signKdtId || app.getKdtId();
    }

    Promise.all([
      api.getUserInfo(defaultParams),
      api.getCustomerLevels({
        kdt_id: kdtId,
        query_birthday_privilege: true,
        query_festival_privilege: true,
        query_member_day_privilege: true
      }),
      api.getExtraBenefits({
        types: [4]
      }),
      this.fetchPointsName()
    ])
      .then(([userInfo, levelList, extraBenefits = []]) => {
        const {
          asset_info: assetInfo,
          customer_info: customerInfo,
          member_level_right: memberLevelRight = {},
          birthday_benefit_bag: birthdayBenefitBag = {},
          birthday_gift_bag: birthdayGiftBag = {},
          is_login_cash: isLoginCashier
        } = userInfo;

        if (isLoginCashier === false) {
          notifyOfflineMemberSignFailed();
        }
        /* eslint no-nested-ternary: off */
        const currentLevel = memberLevelRight.vip_level
          ? memberLevelRight.vip_level
          : customerInfo.mobile && !customerInfo.is_member
          ? 1
          : 0;
        const enabledLevels = Object.keys(levelList).filter(level => {
          return +level >= currentLevel && levelList[level].enabled;
        });

        if (assetInfo) {
          const { growth_value: growthValue } = assetInfo;
          const levelListWithBiz = enabledLevels.map((level, index, arr) => {
            const levelInfo = levelList[level];
            levelInfo.currentGrowth = growthValue;
            levelInfo.nextLevelGrowth =
              index + 1 >= arr.length
                ? null
                : levelList[arr[index + 1]].growth_value;
            levelInfo.toThisLevelGrowth = levelInfo.growth_value - growthValue;
            levelInfo.vip_level_name_column = `VIP ${levelInfo.vip_level}`;
            return levelInfo;
          });
          const maxLevel = +enabledLevels[enabledLevels.length - 1];
          this.setYZData({
            currentLevel,
            levels: levelListWithBiz,
            levelName: currentLevel
              ? levelList[currentLevel].vip_level_name
              : levelList[1].vip_level_name,
            maxLevel
          });
        }

        if (customerInfo) {
          this.setYZData({
            isMember: !!customerInfo.is_member,
            mobile: userInfo.user_info.mobile
          });
        }

        let benefits = [];
        if (Object.keys(enabledLevels).length > 0) {
          // 处理通用权益
          const benefitsObj =
            currentLevel === 0
              ? levelList[1].member_benefit_bag
              : levelList[currentLevel].member_benefit_bag;
          benefits = Object.keys(benefitsObj).map(benefit => {
            return handleBenefitBizData(benefit, benefitsObj[benefit]);
          });

          // 处理生日特权
          if (
            birthdayBenefitBag &&
            birthdayBenefitBag.birthday_privilege_banner
          ) {
            const birthdayBenefitBagHandled = handleBirthdayBenefit(
              birthdayBenefitBag
            );
            const birthdayGiftBagHandled = handleBirthdayGift(birthdayGiftBag);
            const birthdayPrivilege = {
              benefitName: '生日特权',
              benefitIconTitle: '生日特权',
              benefitDesc: birthdayBenefitBag.birthday_privilege_banner,
              benefitIconStyle: 'birthday',
              benefitContent: {
                benefitBag: birthdayBenefitBagHandled,
                giftBag: birthdayGiftBagHandled,
                benefitTerm: termTypeEnum[birthdayBenefitBag.term_type]
              },
              specialName: '生日',
              specialBenefit: true
            };
            benefits.push(birthdayPrivilege);
            this.setYZData({
              isMember: !!customerInfo.is_member,
              mobile: userInfo.user_info.mobile,
              birthdayPrivilege
            });
          }
        }

        const currentLevelInfo =
          currentLevel === 0 ? levelList[1] : levelList[currentLevel];
        const realFestivalPrivilege = [];
        let realMemberdayPrivilege = null;

        if (extraBenefits && extraBenefits.length) {
          extraBenefits.forEach(benefit => {
            if (benefit.type === 3) {
              realFestivalPrivilege.push(benefit);
            }
            if (benefit.type === 2) {
              realMemberdayPrivilege = benefit;
            }
          });
        }

        const memberdayPrivilege = realMemberdayPrivilege
          ? handleMembedayBenefit(realMemberdayPrivilege)
          : null;
        if (memberdayPrivilege) {
          benefits.push(memberdayPrivilege);
        } else if (currentLevelInfo.member_day_info) {
          currentLevelInfo.member_day_info.isFromConfig = true;
          benefits.push(
            handleMembedayBenefit(currentLevelInfo.member_day_info)
          );
        }

        this.setYZData({
          realMemberdayPrivilege: memberdayPrivilege,
          realFestivalPrivilege
        });

        const unionedBenefits = this.getCurrentLevelShowedFestival(0);
        if (unionedBenefits) {
          benefits.push(unionedBenefits);
        }

        this.setYZData({
          userInfo,
          currentSwiperLevel: currentLevel,
          benefits,
          loading: false
        });
        wx.stopPullDownRefresh();

        if (
          customerInfo.is_member &&
          isFirstSign &&
          signQrcodeKey &&
          isLoginCashier
        ) {
          signSceneCallback('cashier');
        }
      })
      .catch(err => {
        wx.stopPullDownRefresh();
        this.setYZData({
          loading: false
        });
        wx.showToast({
          title: err.msg || '获取会员信息失败',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 获取应当展示的节日的特权
  getCurrentLevelShowedFestival(index) {
    const { realFestivalPrivilege, levels } = this.data;

    const { festival_info: currentFestivalInfo = [] } = levels[index];
    currentFestivalInfo.forEach(item => (item.isFromConfig = true));

    if (index === 0) {
      // 与商家配置的进行一次合并操作
      const unionedFestival = unionFestivalByAlias(
        realFestivalPrivilege,
        currentFestivalInfo
      );
      const unionedFestivalBenefits = unionedFestival.length
        ? handleFestivalBenefit(unionedFestival)
        : null;
      return unionedFestivalBenefits;
    }
    return currentFestivalInfo && currentFestivalInfo.length > 0
      ? handleFestivalBenefit(currentFestivalInfo)
      : null;
  },

  onLoad(options) {
    const {
      origin_kdt_id: originKdtId,
      member_src_way: memberSrcWay,
      member_src_channel: memberSrcChannel
    } = options;

    this.setYZData({
      originKdtId: +originKdtId || app.getKdtId(),
      memberSrcWay: +memberSrcWay || 800,
      memberSrcChannel: +memberSrcChannel || 102
    });
    wx.getSystemInfo({
      success: ({ windowWidth }) => {
        this.setYZData({
          winWidth: windowWidth
        });
      }
    });
    this.animation = wx.createAnimation({
      transformOrigin: '50% 50%',
      duration: 500,
      timingFunction: 'ease-out',
      delay: 0
    });
    this.initPage();
  },

  onPullDownRefresh() {
    this.animationToSmall(0);
    this.animationToLarge(0);
    this.setYZData(
      {
        current: 0,
        translateDistance: 0,
        showQrcodePopup: false,
        showBenefitPopup: false
      },
      this.initPage
    );
  },

  handleSwiperChange(current) {
    const {
      levels,
      currentLevel,
      birthdayPrivilege,
      realMemberdayPrivilege
    } = this.data;

    const currentSwiperLevel = currentLevel + current;
    const currentSwiperLevelBenefitsObj = levels[current].member_benefit_bag;
    const currentSwiperLevelBenefits = Object.keys(
      currentSwiperLevelBenefitsObj
    ).map(benefit => {
      return handleBenefitBizData(
        benefit,
        currentSwiperLevelBenefitsObj[benefit]
      );
    });

    const {
      birthday_benefit_bag: currentSwiperBirthdayBenefits,
      birthday_gift_bag: currentSwiperBirthdayGifts,
      birthday_term_type: birthdayTermType,
      member_day_info: currentSwiperMemberdayInfo,
      festival_info: currentFestivalInfo = []
    } = levels[current];

    // @TODO 时间紧脏，后面重构一下，不需要这么多 if..
    if (currentLevel === currentSwiperLevel) {
      // @TODO 几种特权重构为一个数组
      if (birthdayPrivilege) {
        currentSwiperLevelBenefits.push(birthdayPrivilege);
      }

      if (realMemberdayPrivilege) {
        currentSwiperLevelBenefits.push(realMemberdayPrivilege);
      } else if (currentSwiperMemberdayInfo) {
        currentSwiperMemberdayInfo.isFromConfig = true;
        currentSwiperLevelBenefits.push(
          handleMembedayBenefit(currentSwiperMemberdayInfo)
        );
      }
      const currentSwiperFestivalBenefit = this.getCurrentLevelShowedFestival(
        current
      );
      if (currentSwiperFestivalBenefit) {
        currentSwiperLevelBenefits.push(currentSwiperFestivalBenefit);
      }
    } else {
      if (currentSwiperBirthdayBenefits && currentSwiperBirthdayGifts) {
        currentSwiperLevelBenefits.push(
          handleBirthdayInfo(
            currentSwiperBirthdayBenefits,
            currentSwiperBirthdayGifts,
            birthdayTermType
          )
        );
      }

      if (currentSwiperMemberdayInfo) {
        currentSwiperMemberdayInfo.isFromConfig = true;
        currentSwiperLevelBenefits.push(
          handleMembedayBenefit(currentSwiperMemberdayInfo)
        );
      }

      if (currentFestivalInfo && currentFestivalInfo.length) {
        currentFestivalInfo.forEach(item => (item.isFromConfig = true));
        currentSwiperLevelBenefits.push(
          handleFestivalBenefit(
            currentFestivalInfo.sort(
              (prev, next) =>
                prev.festival_start_time - next.festival_start_time
            )
          )
        );
      }
    }

    this.setYZData({
      benefits: [].concat(currentSwiperLevelBenefits),
      currentSwiperLevel,
      current
    });
  },

  swiperTouchstart({ changedTouches, timeStamp }) {
    const startClientX = changedTouches[0].clientX;
    this.setYZData({
      startClientX, // 触摸开始位置
      startTimestamp: timeStamp // 触摸开始时间
    });
  },

  swiperTouchend(e) {
    const {
      startClientX,
      startTimestamp,
      winWidth,
      current,
      levels,
      translateDistance
    } = this.data;
    const times = e.timeStamp - startTimestamp; // 时间间隔
    const distance = e.changedTouches[0].clientX - startClientX; // 距离间隔

    // 滑动手指按下到离开小于500ms且移动距离大于10s时滑动，否则不滑动
    if (times < 500 && Math.abs(distance) > 10) {
      let currentIndex = current;

      let x0 = winWidth * 0.84;
      const x1 = translateDistance;
      let x = 0;

      if (distance > 0) {
        currentIndex--; // 向前滑动
        // 滑到第一个不再滑动
        if (currentIndex < 0) {
          currentIndex = 0;
          x0 = 0;
        }
        x = x1 + x0;
      } else {
        currentIndex++; // 向后滑动
        // 滑到最后一个不再滑动
        if (currentIndex >= levels.length) {
          currentIndex = levels.length - 1;
          x0 = 0;
        }
        x = x1 - x0;
      }

      this.animationToLarge(x);
      this.animationToSmall(x);
      this.handleSwiperChange(currentIndex);
      this.setYZData({
        current: currentIndex,
        translateDistance: x,
        currentIconSwiper: 0
      });
    }
  },

  // 向后滑动变大
  animationToLarge(x) {
    this.animation
      .translateX(x)
      .scale(1)
      .step();
    this.setYZData({
      animationToLarge: this.animation.export()
    });
  },

  // 向前滑动变小
  animationToSmall(x) {
    this.animation
      .translateX(x)
      .scale(0.9)
      .step();
    this.setYZData({
      animationToSmall: this.animation.export()
    });
  },

  handleIconSwiper({ detail: { current } }) {
    this.setYZData({
      currentIconSwiper: current
    });
  }
});
