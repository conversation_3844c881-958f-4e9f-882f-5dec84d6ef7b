<page-container pageBgColor="#fff" class="{{ showBenefitPopup || showQrcodePopup ? 'scroll-disable' : '' }}">
<view>
  <view class="container" wx:if="{{ !loading }}">
    <view class="membercenter-cards">
      <view class="membercenter-background" />
      <view class="membercenter-background__arc">
        <image class="membercenter-background__arc-image" src="https://b.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>" />
      </view>

      <view wx:if="{{ isMember }}" class="membercenter-newswiper__wrapper" style="width: {{ winWidth }}px;">
        <view
          class="membercenter-newswiper__container"
          bindtouchstart="swiperTouchstart"
          bindtouchend="swiperTouchend"
          style='width: {{ winWidth * levels.length }}px; position: relative; left: {{ (winWidth - winWidth * 0.84 ) / 2 }}px;'
        >
          <block wx:for="{{ levels }}" wx:key="index">
            <view
              class="membercenter-newswiper__item"
              data-current="{{ current }}"
              data-index='{{ index }}'
              animation="{{ current == index ? animationToLarge : animationToSmall }}"
              style="width: {{ winWidth * 0.84 }}px; height: {{ winWidth * 0.84 * 0.48 }}px; transform: scale({{ current == index ? 1 : 0.9 }});"
            >
            <level-info
              index="{{ index }}"
              isMember="{{ isMember }}"
              mobile="{{ mobile }}"
              levelInfo="{{ item }}"
              maxLevel="{{ maxLevel }}"
              currentLevel="{{ currentLevel }}"
              userInfo="{{ userInfo }}"
              pointsName="{{statsNames.points}}"
              bind:handleQrcodeShow="handleQrcodeShow"
              bind:reload="initPage"
            />
            </view>
          </block>
        </view>
      </view>
      <view wx:else class="membercenter-swiper">
        <level-info
          isMember="{{ isMember }}"
          mobile="{{ mobile }}"
          currentLevel="{{ currentLevel }}"
          userInfo="{{ userInfo }}"
          levelInfo="{{ levelInfo }}"
          originKdtId="{{originKdtId}}"
          memberSrcWay="{{memberSrcWay}}"
          memberSrcChannel="{{memberSrcChannel}}"
          pointsName="{{statsNames.points}}"
          bind:handleQrcodeShow="handleQrcodeShow"
          bind:reload="initPage"
        />
      </view>
    </view>
    <view class="membercenter-benefit membercenter-block" wx:if="{{ benefits.length > 0 }}">
      <block-title content="VIP{{ currentSwiperLevel || 1 }} 权益" />
      <block wx:if="{{ benefits.length <= 4 }}">
        <view class="membercenter-benefit__container">
        <view wx:for="{{ benefits }}" wx:key="index" class="membercenter-benefit__item">
          <benefit-icon data-index="{{ index }}" bind:tap="toggleBenefitPopup" benefit="{{ item }}" />
        </view>
      </view>
      </block>
      <block wx:else>
        <swiper bind:change="handleIconSwiper" class="membercenter-icon__swiper" current="{{ currentIconSwiper }}">
          <swiper-item>
            <view class="membercenter-benefit__container">
              <view wx:for="{{ benefits }}" wx:key="index" wx:if="{{ index < 4 }}" class="membercenter-benefit__item">
                <benefit-icon data-index="{{ index }}" bind:tap="toggleBenefitPopup" benefit="{{ item }}" />
              </view>
            </view>
          </swiper-item>
          <swiper-item>
            <view class="membercenter-benefit__container">
              <view wx:for="{{ benefits }}" wx:key="index" wx:if="{{ index >= 4 }}" class="membercenter-benefit__item">
                <benefit-icon data-index="{{ index }}" bind:tap="toggleBenefitPopup" benefit="{{ item }}" />
              </view>
            </view>
          </swiper-item>
        </swiper>
        <view class="membercenter-benefit__dot">
          <block wx:for="{{ [0, 1] }}" wx:key="index">
            <view class="membercenter-benefit__dot-container membercenter-benefit__dot-{{index === currentIconSwiper ? 'active' : 'unactive'}}" />
          </block>
        </view>
      </block>
    </view>
    <view class="membercenter-divider" />
    <view class="membercenter-benefit membercenter-block membercenter-table">
      <block-title content="会员成长说明" />
      <view wx:if="{{ levels.length > 0 }}" class="membercenter-growth__table">
        <growth-table columns="{{ columns }}" datasets="{{ levels }}" />
      </view>
      <block-qa content="{{ questions }}" />
    </view>
  </view>
  <view wx:else class="membercenter-loading">
    <van-loading type="spinner" color="black" />
  </view>
  <view class="zan-popup zan-popup--center {{ showBenefitPopup ? 'zan-popup--show' : '' }}">
    <view class="zan-popup__mask" bind:tap="toggleBenefitPopup" />
    <view class="zan-popup__container membercenter-benefit__popup">
      <benefit-swiper benefits="{{ benefits }}" bind:close="toggleBenefitPopup" currentBenefitSwiper="{{ currentBenefitSwiper }}"/>
    </view>
  </view>
</view>
<van-dialog id="van-dialog" />
</page-container>

<level-info-popup
  wx:if="{{ showQrcodePopup }}"
  show="{{ showQrcodePopup }}"
  identity="{{ identityNo }}"
  levelName="{{ userInfo.member_level_right.vip_name }}"
  nickName="{{ userInfo.user_info.nick_name }}"
  level="{{ userInfo.member_level_right.vip_level }}"
  bind:close="handleQrcodePopup"
/>

<account-wx-login id="account-wx-login" />
