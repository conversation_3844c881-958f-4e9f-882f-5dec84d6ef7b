.input-bar {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border-top: solid 1rpx #e5e5e5;    
    padding: 5px;
  
    &__input {
        flex: 1;
        margin: 5px;
        position: relative;
        border-radius: 2px;
        border: solid 1rpx #e5e5e5;
        min-height: 30px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 3px;
      
        textarea {
            width: auto;
            font-size: 14px;
            color: #333;
        }
    }

    &__send {
        width: 48px;
        height: 30px;
        line-height: 30px;
        font-size: 12px;
        background: #1AAD19;
        color: #fff;
        padding: 0 3px;
        border-radius: 2px;
    }

    &__icon {
        background-size: 30px 30px;
        height: 30px;
        width: 30px;
        padding: 0 5px;

        &--face {
            background: transparent url('https://b.yzcdn.cn/wap-im/img/<EMAIL>') center center no-repeat;
            background-size: 30px 30px;
        }

        &--keyboard {
            background: transparent url('https://b.yzcdn.cn/wap-im/img/text_model.png') center center no-repeat;
            background-size: 30px 30px;
        }
    }

    &__plus {
        background: transparent url('https://b.yzcdn.cn/wap-im/img/<EMAIL>') center center no-repeat;
        background-size: 30px 30px;
        height: 30px;
        width: 30px;
        padding: 0 5px;
    }
}