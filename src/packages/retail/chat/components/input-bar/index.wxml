<view class="input-bar">
  <view class="input-bar__input">
    <textarea
      value="{{ inputText }}"
      auto-height
      show-confirm-bar="{{ false }}"
      disabled="{{ false }}"
      cursor-spacing='16'
      bindinput="onInput"
      bindfocus="onInputFocus"
    />
  </view>
  <view wx:if="{{ showEmojiPanel }}" class="input-bar__icon input-bar__icon--keyboard" bind:tap="handleKeyboardClick" />
  <view wx:else class="input-bar__icon input-bar__icon--face" bind:tap="handleFaceClick" />
  <view
    wx:if="{{ inputText.length > 0 }}"
  >
    <!-- <van-button size="mini" custom-class="input-bar__send" bindtap="send">发送</van-button> -->
    <form report-submit="true" bindsubmit="send">
      <button form-type="submit" class="input-bar__send">发送</button>
    </form>
    <!-- <button class="input-bar__send" bindtap="send">发送</button> -->
  </view>
  <view wx:else class="input-bar__plus" bind:tap="handleExtraPanel" />
  
</view>