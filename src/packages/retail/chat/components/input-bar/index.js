
import WscComponent from 'pages/common/wsc-component/index';
import { emojiList } from '../emoji-panel/utils';

const app = getApp();

WscComponent({
  properties: {
    showEmojiPanel: Boolean
  },

  data: {
    inputText: ''
  },

  methods: {
    send(e) {
      if (!this.data.inputText) {
        return;
      }
      if (e && e.detail.formId !== 'the formId is a mock one') {
        app.carmen({
          api: 'wsc.weapp.formid/1.0.0/add',
          data: {
            form_id: e.detail.formId,
            business_module: 'default'
          }
        });
      }
      this.triggerEvent('send', { type: 'text', value: this.data.inputText });
    },

    bakespace() {
      let val = this.data.inputText;
      const rIndex = val.lastIndexOf(']');
      const lIndex = val.lastIndexOf('[');
      let lastFace = '';

      if (rIndex === (val.length - 1) && lIndex > -1) {
        lastFace = val.slice(lIndex, rIndex + 1);
      }

      if (lastFace && emojiList.some(v => v === lastFace)) {
        this.setYZData({
          inputText: val.slice(0, lIndex)
        });
        return;
      }

      this.setYZData({
        inputText: val.slice(0, val.length - 1)
      });
    },

    onInput(e) {
      this.setData({
        inputText: e.detail.value
      });
    },

    onInputFocus() {
      this.triggerEvent('toggle', { emoji: false, extra: false, scroll: false });
    },

    handleFaceClick() {
      this.triggerEvent('toggle', { emoji: true, extra: false });
    },

    handleExtraPanel() {
      this.triggerEvent('toggle', { emoji: false, extra: true });
    },

    handleKeyboardClick() {
      this.triggerEvent('toggle', { emoji: false, extra: false });
    },

    handleInsertValue(data) {
      this.setYZData({
        inputText: `${this.data.inputText}${data}`
      });
    },

    clearInput() {
      // 真机不加 setTimeout 清除不掉
      setTimeout(() => {
        this.setData({
          inputText: ''
        });
      }, 300);
    }
  }
});
