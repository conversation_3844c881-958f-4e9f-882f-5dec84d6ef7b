import Toast from '@vant/weapp/dist/toast/toast';
import WscComponent from 'pages/common/wsc-component/index';
import AudioUtils from 'common-api/audio/index';
import { getRawVoiceSrc } from '../../utils';

WscComponent({
  properties: {
    message: Object
  },

  data: {
    pause: true
  },

  ready() {
    const _this = this;
    this.audio = wx.createInnerAudioContext();
    this.audio.id = this.data.message.msg_id;
    this.audio.onPlay(() => {
      _this.toggleStatus();
    });

    this.audio.onStop(() => {
      if (!_this.data.pause) {
        _this.setYZData({
          pause: true
        });
      }
    });

    this.audio.onEnded(() => {
      if (!_this.data.pause) {
        _this.setYZData({
          pause: true
        });
      }
    });

    this.audio.onError((res) => {
      Toast(res.errMsg || '播放失败');
      if (!_this.data.pause) {
        this.setYZData({
          pause: true
        });
      }
    });
  },

  methods: {
    handleVoiceClick() {
      if (this.data.pause) {
        const rawSrc = getRawVoiceSrc(this.data.message.content);
        AudioUtils.getAudioInfo(rawSrc).then(res => {
          this.audio.src = res.url;
          this.triggerEvent('clearvoice', this.data.message);
          this.audio.play();
        });
      } else {
        this.stopVoice();
      }
    },

    stopVoice() {
      if (!this.data.pause) {
        this.audio.stop();
      }
    },

    toggleStatus() {
      this.setYZData({
        pause: !this.data.pause
      });
    }
  }
});
