<view class="voice-message" bind:tap="handleVoiceClick">
  <view class="voice-message__wrapper" style="width: {{ message.durationWidth }}px">
    <view wx:if="{{ pause }}" class="voice-message__icon voice-message__stop"></view>
    <view wx:else class="voice-message__icon voice-message__play"></view>
  </view>
  <view class="voice-message__duration" style="left: {{ message.durationWidth + 30 }}px">{{ message.duration }}“</view>
</view>