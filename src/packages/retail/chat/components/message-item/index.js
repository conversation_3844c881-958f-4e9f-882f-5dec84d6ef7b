import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    message: {
      type: Object,
      observer: 'onMessageStatus'
    }
  },

  ready() {
    if (this.data.message.msg_status === 'PRE_SEND') {
      this.triggerEvent('send', this.data.message);
    }
  },

  methods: {
    triggerImagePreview(data) {
      this.triggerEvent('preview', data);
    },

    triggerWechatCopy() {
      this.triggerEvent('copy');
    },

    onMessageStatus() {
      console.log('status change');
    }
  }
});
