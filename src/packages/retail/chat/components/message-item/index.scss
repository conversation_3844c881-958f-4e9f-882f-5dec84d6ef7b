.message-item {
    margin: 10px 15px;
    width: 100%;
    min-height: 40px;

    &__time {
        text-align: center;
        font-size: 12px;
        line-height: 14px;
        margin-bottom: 15px;
        color: #999;
        &--value {
            padding: 3px 5px;
            background-color: #dbdbdb;
            display: inline-block;
            border-radius: 3px;
            min-width: 60px;
            color: #fff;
        }
    }

    &__left {
        float: left;
        .message-item {
            &__content {
                float: left;
                margin-left: 12px;
                &::after {
                    border-right-color: #fff;
                    left: -16px;
                }
            }

            &__avatar {
                float: left;
            }
        }
    }

    &__right {
        float: right;

        .message-item {
            &__content {
                float: right;
                margin-right: 12px;
                &::after {
                    border-left-color: #fff;
                    right: -16px;
                }
            }
            
            &__loading {
                float: right;
            }

            &--fail {
                float: right;
            }

            &__avatar {
                float: right;
            }
        }
    }

    &__avatar {
        width: 40px;
        height: 40px;
        vertical-align: middle;
        image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
        }
    }

    &__content {
        background: #fff;
        max-width: 70%;
        padding: 9px 12px;
        font-size: 14px;
        position: relative;
        &:after {
            position: absolute;
            content: '';
            top: 10px;
            border: 10px solid transparent;
        }
    }

    &__status {
        width: 22px;
        height: 22px;
        float: right;
        &--loading {
            height: 15px;
            width: 15px;
            margin-top: 10px;
            background: url('https://img.yzcdn.cn/wap-im/img/loading.png') center center no-repeat;
            background-size: 15px 15px;
            animation: loading 1s linear infinite;
        }

        &--fail {
            width: 22px;
            height: 22px;
            margin-top: 7px;
            margin-left: -10px;
            background: url('https://img.yzcdn.cn/wap-im/img/error.png') center center no-repeat;
            background-size: 22px 22px;
        }
    }
}

@keyframes loading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}