<view class="message-item message-item__{{ message.is_self ? 'right' : 'left' }}">
  <view wx:if="{{ message.needTimeStamp }}" class="message-item__time">
    <view class="message-item__time--value">{{ message.timeStr }}</view>
  </view>
  <view class="message-item__avatar">
    <image src="{{ message.sender_avatar || 'https://img.yzcdn.cn/public_files/2018/04/24/defaultAvatar.png' }}" />
  </view>
  <view class="message-item__content">
    <text-message wx:if="{{ message.msg_type === 'text' }}" message="{{ message }}"/>
    <image-message wx:elif="{{ message.msg_type === 'image'}}" message="{{ message }}" bind:preview="triggerImagePreview"/>
    <interactive-message wx:elif="{{ message.msg_type === 'sales_default' }}" message="{{ message }}" bind:copy="triggerWechatCopy" />
    <view wx:else>不支持的消息类型</view>
  </view>
  <view class="message-item__status">
    <view wx:if="{{ message.msg_status === 'PRE_SEND'}}" class="message-item__status--loading" />
    <view wx:if="{{ message.msg_status === 'FAILED'}}" class="message-item__status--fail" />
  </view>
</view>