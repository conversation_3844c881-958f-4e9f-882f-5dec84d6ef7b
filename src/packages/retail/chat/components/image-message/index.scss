.image-message {
    overflow: hidden;
    border-radius: 3px;
    line-height: 0;
    text-align: center;
    max-width: 70%;
    position: relative;

    &__right {
        left: 35%;
    }

    &__left {
      left: -5%;
    }

    image {
        font-size: 0px;
        max-width: 100%;
        max-height: 100%;
    }

    &__popup {
        width: 100%;
        height: 100%;
        position: absolute;
        background:rgba(0,0,0,.6);
    }

    &__loading {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        height: 15px;
        width: 15px;
        background: url('https://img.yzcdn.cn/wap-im/img/loading.png') center center no-repeat;
        background-size: 15px 15px;
        animation: loading 1s linear infinite;
    }
}

@keyframes loading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
