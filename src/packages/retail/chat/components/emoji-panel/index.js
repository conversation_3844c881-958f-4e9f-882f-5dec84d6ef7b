import WscComponent from 'pages/common/wsc-component/index';
import { groupEmoji } from './utils';

WscComponent({
  data: {
    emojiList: groupEmoji(23),
    currentEmojiPanelIndex: 0
  },

  methods: {
    handleEmojiClick({ currentTarget: { dataset: { item } } }) {
      this.triggerEvent('setEmoji', item);
    },

    handleEmojiPanelChange({ detail: { current } }) {
      this.setYZData({
        currentEmojiPanelIndex: current
      });
    },

    handleSendClick(e) {
      if (e.detail.formId !== 'the formId is a mock one') {
        getApp().carmen({
          api: 'wsc.weapp.formid/1.0.0/add',
          data: {
            form_id: e.detail.formId,
            business_module: 'default'
          }
        });
      }
      this.triggerEvent('send');
    },

    deleteEmojiInput() {
      this.triggerEvent('delete');
    }
  }
});
