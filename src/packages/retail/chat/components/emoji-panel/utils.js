import { emojiList } from '../../constants';

const originColumn = 15;

function groupEmoji(groupSize) {
  const total = emojiList.length;
  const result = [];
  for (let index = 0; index < total; index += groupSize) {
    result.push(emojiList.slice(index, index + groupSize).map((value, i) => {
      const originIndex = i + index;
      return {
        top: Math.floor(originIndex / originColumn) * -29 - 2,
        left: Math.floor(originIndex % originColumn) * -29 - 2,
        value
      };
    }));
  }
  return result;
}

export { groupEmoji, emojiList };
