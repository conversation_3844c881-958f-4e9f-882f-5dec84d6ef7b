<view class="emoji-panel">
  <swiper indicator-dots="{{ false }}" bind:change="handleEmojiPanelChange">
    <block wx:for="{{ emojiList }}" wx:key="index" wx:for-item="list">
      <swiper-item>
        <view class="emoji-panel__block">
          <block wx:for="{{ list }}">
            <view class="emoji-panel__item" bind:tap="handleEmojiClick" data-item="{{ item }}">
               <view class="emoji-panel__face" style='background-position: {{ item.left }}px {{ item.top }}px;'></view>
            </view>
          </block>
          <view class="emoji-panel__delete" bind:tap="deleteEmojiInput" />
        </view>
      </swiper-item>
    </block>
  </swiper>
  <view class="emoji-panel__dots">
    <block wx:for="{{ emojiList }}" wx:key="index">
      <view class="emoji-panel__dots--item emoji-panel__dots--{{ index === currentEmojiPanelIndex ? 'active' : ''}}"></view>
    </block>
  </view>
  <view class="emoji-panel__bar">
    <form report-submit="true" bindsubmit="handleSendClick">
      <button form-type="submit" class="emoji-panel__bar--btn">发送</button>
    </form>
  </view>
</view>