import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    message: Object,
    source: String
  },

  methods: {
    handleCardClick() {
      const sales = {};
      const fromParams = this.data.message.content.from_params;
      fromParams.split('!').forEach(item => {
        const arr = item.split('~');
        if (arr.length > 1) {
          sales[arr[0]] = arr[1];
        }
      });
      if (!sales.sales_role) {
        sales.sales_role = 'saleman';
      }

      const params = Object.keys(sales).map(key => `${key}~${sales[key]}`).join('!');
      wx.navigateTo({
        url: `${this.data.source === 'new' ? '/packages/business-card/detail/index' : '/packages/retail/extra/sales/bi-card/index'}?from_params=${params}&from_source=${this.data.message.content.from_source}`
      });
    }
  }
});
