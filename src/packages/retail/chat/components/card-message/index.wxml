<view class="card-message" bind:tap="handleCardClick">
  <view class="card-message__item">
    <image class="card-message__avatar" src="{{ message.content.avatar || 'https://img.yzcdn.cn/public_files/2018/04/24/defaultAvatar.png' }}" mode="aspectFit" />
  </view>
  <view class="card-message__item card-message__desc">
    <view class="card-message__name">{{ message.content.name }}</view>
    <view class="card-message__company">{{ message.content.company }} 导购</view>
  </view>
  <view class="card-message__item card-message__link">
    查看名片  <van-icon name="arrow" custom-style="transform: scale(.8); color: #666; vertical-align: middle;" />
  </view>
</view>