import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  properties: {
    message: Object
  },

  methods: {
    handleWxClick() {
      this.triggerEvent('copy');
    },

    handleMakeCall() {
      wx.makePhoneCall({
        phoneNumber: this.data.message.content.mobile
      });
      app.logger.log({
        et: 'click',
        ei: 'phone_call_click',
        en: '拨打电话',
        si: app.getKdtId()
      });
    },

    handleShopClick() {
      wx.reLaunch({
        url: '/packages/home/<USER>/index'
      });
    },

    handleCustomMessage() {
      this.triggerEvent('send', { type: 'text', value: '最近有什么优惠吗？' });
    }
  }
});
