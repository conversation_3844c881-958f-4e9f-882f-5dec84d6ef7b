import Toast from '@vant/weapp/dist/toast/toast';
import WscPage from 'pages/common/wsc-page/index';
import { node as request } from 'shared/utils/request';
import upload from 'utils/upload';
import debounce from 'utils/debounce';
import { WsClient } from './ws';
import {
  getVoiceLength,
  getFakeMessage,
  getRequstId,
  formatMessage,
  updateMessageByRequestId,
  REQUEST_TYPES,
  isNeedTimeLabel,
} from './utils';
import { salesDefaultAvatar } from './constants';

const app = getApp();

WscPage({
  data: {
    messageList: [],
    showWechatPopup: false,
    showEmojiPanel: false,
    showExtraPanel: false,
    showAuthPopup: false,
    hasNoneMessages: false,
    firstFetchHistory: false,
    scrollTop: 0,
    scrollIntoView: '',
    sales: {},
  },

  onLoad({
    from_params: fromParams = '',
    from_source: fromSource = '',
    conversation_id: conversationId = null,
    store_kdt_id: storeKdtId = null,
    custom_title: customTitle = '',
    kf_id: kfId = null,
  }) {
    app.getUserInfo(() => {}, () => {
      this.setYZData({
        showAuthPopup: true,
      });
    });

    wx.showLoading({
      title: '连接中',
    });

    const salesObj = {};
    if (decodeURIComponent(fromParams)) {
      fromParams.split('!').forEach((item) => {
        const arr = item.split('~');
        if (arr.length > 1) {
          salesObj[arr[0]] = arr[1];
        }
      });
    }

    // 离线消息
    if (conversationId) {
      salesObj.sales_id = +kfId;
      salesObj.store_kdt_id = +storeKdtId;
    }

    this.data.id = `.${salesObj.sales_id}`;

    request({
      path: '/wscump/salesman/im/getSalesmanVcardByAdminApi.json',
      data: {
        buyerId: app.getBuyerId(),
        kdtId: +salesObj.online_kdt_id || app.getKdtId(),
        retailSource: 'MINIAPP',
        salesmanId: +salesObj.sales_id,
        salesmanKdtId: +salesObj.store_kdt_id || app.getKdtId(),
        userRoleType: 'salesman',
      },
    }).then((res) => {
      // 接口替换，字段修改兼容
      const {
        shopAddress,
        storeName,
        kdtId,
        logoUrl,
        mobilePhone,
        salesmanName,
        salesmanId,
        headUrl,
        wechatNo,
      } = res;
      const transformed = {
        address: shopAddress,
        company: storeName,
        kdt_id: kdtId,
        logo: logoUrl,
        mobile: mobilePhone,
        name: salesmanName,
        sales_id: salesmanId,
        avatar: headUrl,
        wx: wechatNo,
        showName: `${salesmanName}（${storeName}）`,
      };
      const sales = { ...salesObj, ...transformed };
      wx.setNavigationBarTitle({
        title:
          customTitle || `${sales.company}-${decodeURIComponent(sales.name)}`,
      });
      app.globalData.from_source = decodeURIComponent(
        fromSource || sales.from_source
      );
      app.globalData.from_params = decodeURIComponent(
        fromParams || sales.from_params
      );
      this.setYZData({
        sales,
        isFromOffline: !!conversationId,
      });
      this.initEvent();
    });
  },

  initEvent() {
    this.getTokenInfo().then((token) => {
      const wsClient = new WsClient({
        token,
        requestChannel: 'nameCard',
      });

      wsClient.ws.eventEmitter.on('connecting', () => {
        console.log('connecting');
      });

      wsClient.ws.eventEmitter.on('open', () => {
        console.log('open');
      });

      wsClient.ws.eventEmitter.on('reconnect', () => {
        console.log('reconnect');
      });

      wsClient.ws.eventEmitter.on('close', () => {
        console.log('close');
      });

      wsClient.ws.eventEmitter.on('stop', () => {
        console.log('stop');
      });

      wsClient.ws.eventEmitter.on('authFail', () => {
        wx.showToast({
          title: '登录验证失败',
          icon: 'none',
        });
      });

      wsClient.ws.eventEmitter.on('authSuccess', async () => {
        if (!this.data.conversationId) {
          await this.createConversationId();
        }
        wx.hideLoading();
        this.setYZData({
          firstFetchHistory: true,
        });
        await this.fetchHistoryMessage({ fristFetch: true });
        await this.clearUnreadMessageCount();
      });

      wsClient.ws.eventEmitter.on('conversationCreate', (data) => {
        const conversationId = JSON.parse(data.body).conversation_id;
        this.setYZData({
          conversationId,
        });
        wx.hideLoading();
      });

      // 历史消息
      wsClient.ws.eventEmitter.on('history', async (data) => {
        await this.handleHistoryMessage(data);
      });

      // 接收消息
      wsClient.ws.eventEmitter.on('message', (data) => {
        if (data.reqType === REQUEST_TYPES.KICKOFF) {
          Toast('连接已断开');
        }

        if (data.reqType === REQUEST_TYPES.MESSAGE) {
          this.handleReceiveMessage(data);
        }

        if (data.reqType === REQUEST_TYPES.NORMAL) {
          this.handleResponseMessage(data.msg);
        }
      });

      this.wsClient = wsClient;
    });
  },

  onUnload() {
    this.wsClient.close();
    clearTimeout(this.tokenInterval);
  },

  async createConversationId() {
    await this.wsClient.send(
      {
        kdt_id: this.data.sales.store_kdt_id,
        kf_id: this.data.sales.sales_id,
      },
      { type: 13 }
    );
  },

  getTokenInfo() {
    return new Promise((resolve) => {
      const getToken = () => {
        const tokenInfo = app.getToken().accessToken;
        if (!tokenInfo) {
          this.tokenInterval = setTimeout(getToken, 1000);
        } else {
          resolve(tokenInfo);
        }
      };
      getToken();
    });
  },

  handleUploadImageMessage(path) {
    return new Promise((resolve, reject) => {
      upload({
        file: path,
        success: (data) => {
          const url = data.attachment_url;
          resolve(url);
        },
        fail: (res) => {
          Toast(res.msg || '上传图片识别');
          reject();
        },
      });
    });
  },

  // 预发送消息
  async handleSendMessage({ detail }) {
    const waitMessage = getFakeMessage({
      content: detail.value,
      msgType: detail.type,
      kfId: this.data.sales.sales_id,
      reqId: getRequstId(),
      conversationId: this.data.conversationId,
      kdtId: this.data.sales.store_kdt_id,
      msgStatus: 'SEND',
    });

    const _messageList = this.data.messageList;
    _messageList.push(waitMessage);

    if (detail.type === 'text') {
      waitMessage.content_decoded = formatMessage(waitMessage.content);
    }

    this.selectComponent('#inputBar').clearInput();
    this.setYZData(
      {
        messageList: _messageList,
      },
      async () => {
        this.setYZData({
          scrollTop: _messageList.length * 500,
        });

        if (detail.type === 'image') {
          waitMessage.content = await this.handleUploadImageMessage(
            detail.value
          );
        }

        this.handleSendingMessage(waitMessage);
      }
    );
  },

  async clearUnreadMessageCount() {
    await this.wsClient.send(
      { conversation_id: this.data.conversationId },
      { type: REQUEST_TYPES.READ }
    );
  },

  // 发送消息
  async handleSendingMessage(data) {
    try {
      await this.wsClient.send(data, {
        type: REQUEST_TYPES.NORMAL,
        reqId: data.req_id,
      });
    } catch (e) {
      console.log(e);
      this.handleMessageFailed(data);
    }
  },

  async fetchHistoryMessage() {
    const headMessageId = this.data.messageList.length
      ? this.data.messageList[0].msg_id || 0
      : 0;
    await this.wsClient.send(
      {
        limit: 20,
        msg_id: headMessageId,
        conversation_id: this.data.conversationId,
      },
      { type: REQUEST_TYPES.TALK }
    );
  },

  handleHistoryMessage(data) {
    try {
      const historyMessages = JSON.parse(data.body) || [];
      if (
        !this.data.firstFetchHistory &&
        (!historyMessages || historyMessages.length === 0)
      ) {
        this.setYZData({
          hasNoneMessages: true,
          scrollToTop: false,
        });
        return;
      }
      if (this.data.firstFetchHistory) {
        const defaultContent = {
          nickName: this.data.sales.name,
          avatarUrl: this.data.sales.avatar || salesDefaultAvatar,
          reqId: getRequstId(),
          isSelf: false,
          conversationId: this.data.conversationId,
        };

        const cardMessage = getFakeMessage({
          ...defaultContent,
          msgType: 'bi_card',
          content: this.data.sales,
        });

        const defaultMessage = getFakeMessage({
          ...defaultContent,
          ...(this.data.isFromOffline
            ? {
                msgType: 'text',
                content: `您好，我是导购${this.data.sales.name}[${this.data.sales.company}]很高兴为您服务，有什么可以帮到您的吗？您可以在这里和我实时沟通哦~`,
              }
            : {
                msgType: 'sales_default',
                content: this.data.sales,
              }),
        });

        if (this.data.isFromOffline) {
          historyMessages.push(cardMessage);
        }
        historyMessages.push(defaultMessage);
      }

      historyMessages.reduce((accumulator, msg) => {
        isNeedTimeLabel(msg, accumulator);
        if (msg.is_self === false) {
          msg.sender_avatar = this.data.sales.avatar || salesDefaultAvatar;
        }

        if (msg.msg_type === 'text') {
          msg.content_decoded = formatMessage(msg.content);
        }

        if (msg.msg_type === 'voice') {
          const voiceDetail = getVoiceLength(msg.content);
          msg.duration = voiceDetail.duration;
          msg.durationWidth = voiceDetail.durationWidth;
        }
        return msg.create_time;
      }, 0);
      const scrollIntoView = this.data.messageList.length
        ? `message${this.data.messageList[0].msg_id}`
        : '';
      this.setYZData(
        {
          messageList: historyMessages.concat(this.data.messageList),
          scrollToTop: false,
        },
        () => {
          if (this.data.firstFetchHistory) {
            this.setYZData({
              scrollTop: this.data.messageList.length * 500,
              firstFetchHistory: false,
            });
          } else {
            this.setYZData({
              scrollIntoView,
            });
          }
        }
      );
    } catch (e) {
      console.log(e);
    }
  },

  // 发送失败
  handleMessageFailed(data) {
    const updateMessageList = updateMessageByRequestId(
      this.data.messageList,
      data,
      'FAIL'
    );
    this.setYZData({
      messageList: updateMessageList,
      scrollTop: updateMessageList.length * 500,
    });
  },

  handleReceiveMessage(data) {
    const { msg } = data;
    if (msg.is_self === false) {
      msg.sender_avatar = this.data.sales.avatar || salesDefaultAvatar;
    }

    if (msg.msg_type === 'text') {
      msg.content_decoded = formatMessage(msg.content);
    }

    if (msg.msg_type === 'voice') {
      const voiceDetail = getVoiceLength(msg.content);
      msg.duration = voiceDetail.duration;
      msg.durationWidth = voiceDetail.durationWidth;
      msg.unread = true;
    }

    const updatedMessageList = this.data.messageList;
    isNeedTimeLabel(
      msg,
      updatedMessageList[updatedMessageList.length - 1].create_time
    );
    updatedMessageList.push(msg);
    this.setYZData({
      messageList: updatedMessageList,
      scrollTop: updatedMessageList.length * 500,
    });
  },

  handleResponseMessage(data) {
    const msgStatus = data.status === 0 ? 'SUCCESS' : 'FAIL';
    const updatedMessageList = updateMessageByRequestId(
      this.data.messageList,
      data,
      msgStatus
    );
    this.setYZData({
      messageList: updatedMessageList,
      scrollTop: updatedMessageList.length * 500,
    });
  },

  handleDeleteInput() {
    this.selectComponent('#inputBar').bakespace();
  },

  handleEmojiPanelSend() {
    this.selectComponent('#inputBar').send();
  },

  handleScrollToTop: debounce(async function () {
    if (this.data.scrollToTop || this.data.hasNoneMessages) {
      return;
    }
    this.setYZData({
      scrollToTop: true,
    });
    await this.fetchHistoryMessage();
  }, 400),

  toggleWechatCopy() {
    wx.setClipboardData({
      data: this.data.sales.wx,
    });

    this.setYZData({
      showWechatPopup: !this.data.showWechatPopup,
    });

    this.__yzLog__({
      et: 'click',
      ei: 'copy_wechat_click',
      en: '复制微信号',
    });
  },

  togglePanel({ detail }) {
    this.setYZData({
      showEmojiPanel: detail.emoji,
      showExtraPanel: detail.extra,
      scrollTop: this.data.messageList.length * 500,
    });
    if (detail.scroll !== false) {
      this.scrollToBottom();
    }
  },

  setEmoji({ detail }) {
    this.selectComponent('#inputBar').handleInsertValue(detail.value);
  },

  scrollToBottom() {
    wx.createSelectorQuery()
      .select('#page')
      .boundingClientRect((rect) => {
        wx.pageScrollTo({
          scrollTop: rect.bottom,
        });
      })
      .exec();
  },

  bindGetUserInfo(e) {
    if (!this.data.showAuthPopup || !e.detail.nickname) {
      return;
    }
    this.setYZData({
      showAuthPopup: false,
    });
  },

  handleAvatarClick({
    currentTarget: {
      dataset: { message },
    },
  }) {
    if (message.is_self === false) {
      wx.navigateTo({
        url: `/packages/retail/extra/sales/bi-card/index?from_params=${app.globalData.from_params}&from_source=${app.globalData.from_source}`,
      });
    }
  },

  handleScrollEvent: debounce(function () {
    if (this.data.showEmojiPanel || this.data.showExtraPanel) {
      this.setYZData({
        showEmojiPanel: false,
        showExtraPanel: false,
      });
    }
  }, 200),

  clearVoice({ detail }) {
    const messageList = this.data.messageList;
    messageList.forEach((item) => {
      if (item.msg_type === 'voice' && item.msg_id !== detail.msg_id) {
        this.selectComponent(`#message${item.msg_id}-voice`).stopVoice();
      }
    });
  },
});
