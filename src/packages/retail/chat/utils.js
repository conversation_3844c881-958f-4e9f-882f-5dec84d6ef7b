/* eslint import/named:off */
import { moment } from 'utils/time';
import * as util from 'utils/time-utils';
import FaceBot from './face-bot';

const faceBot = new FaceBot();

const REQUEST_TYPES = {
  AUTH: 7,
  DROP: 21,
  PICK: 22,
  INIT: 28,
  WAITING: 23,
  ADMIN: 34,
  HEARTBEAT: 1,
  ADMINONLINE: 43,
  TALK: 2,
  NEWTALK: 45,
  OWN: 33,
  HISTORY: 3,
  NORMAL: 4,
  LOST: 5,
  STATUSMAX: 39,
  UNREAD: 12,
  NEW: 13,
  READ: 14,
  MESSAGE: 11,
  KICKOFF: 9
};

const VERSION = 1;
const TIME_GAP = 5 * 60 * 1000; // 5分钟展示时间戳
const app = getApp();
let fakeIdCount = 0;

function getRequstId() {
  return Math.floor((1 + Math.random()) * 0x10000)
    .toString(16)
    .substring(2) + (+new Date());
}

function getFakeMessage(data) {
  const userInfo = app.globalData.userInfo || {};
  return {
    msg_type: data.msgType,
    sender_nickname: data.nickName || userInfo.nickName || '',
    sender_avatar: data.avatarUrl || userInfo.avatarUrl || '',
    create_time: +(new Date()),
    is_event: false,
    is_self: data.isSelf === undefined ? true : data.isSelf,
    msg_id: `fakeid-${fakeIdCount++}`,
    req_id: data.reqId,
    kf_id: data.kfId,
    kdt_id: data.kdtId || app.getKdtId(),
    conversation_id: data.conversationId,
    content: data.content,
    msg_status: data.msgStatus || 'SUCCESS'
  };
}

function findMessageById(messageList, msgId) {
  let index = -1;
  let target;
  messageList.some((msg, i) => {
    if (+msg.msg_id === +msgId) {
      target = msg;
      index = i;
      return true;
    }
    return false;
  });
  return {
    index,
    msg: target
  };
}

function updateMessageByRequestId(messageList, data, status) {
  const result = messageList;
  for (let i = 0; i < result.length; i++) {
    if (result[i].req_id && (result[i].req_id === data.req_id)) {
      result[i].msg_status = status;
      result[i].msg_id = data.msg_id;
    }
  }
  return result;
}

function msgBuilder(msg, requestChannel = 'nameCard', type = REQUEST_TYPES.NORMAL, reqId) {
  const data = {
    reqType: type
  };
  if (type !== REQUEST_TYPES.HEARTBEAT) {
    data.version = VERSION;
    data.body = JSON.stringify(msg);
    data.reqId = reqId || getRequstId();
    data.requestChannel = requestChannel;
  }
  return data;
}

function formatMessage(data) {
  return faceBot.decode(data);
}

function isNeedTimeLabel(message = {}, lastMessageTime) {
  const messageWithTime = message;
  messageWithTime.needTimeStamp = false;
  if (Math.abs(messageWithTime.create_time - lastMessageTime) >= TIME_GAP) {
    messageWithTime.needTimeStamp = true;
    if (util.isToday(messageWithTime.create_time)) {
      const hour = new Date(messageWithTime.create_time).getHours();
      /* eslint no-nested-ternary:off */
      messageWithTime.timeStr = `${hour > 11 ? hour > 16 ? '晚上' : '下午' : '上午'} ${moment(messageWithTime.create_time, 'h:mm')}`;
    } else {
      // @TODO 昨天
      messageWithTime.timeStr = moment(messageWithTime.create_time, 'YYYY/MM/DD HH:mm');
    }
  }
  return messageWithTime;
}

function getVoiceLength(data) {
  const result = {
    duration: 0,
    durationWidth: 0
  };
  if (data) {
    const hashIndex = data.indexOf('#');
    const duration = decodeURIComponent(data.slice(hashIndex));
    if (duration) {
      const matchInfo = duration.match(/[#&]duration=([^&]*)/);
      if (matchInfo && matchInfo[1]) {
        result.duration = parseInt(matchInfo[1], 10);
        result.durationWidth = Math.max(3.7 * result.duration || 0, 72);
      }
    }
  }
  return result;
}

function getRawVoiceSrc(data) {
  const hashIndex = data.indexOf('#');
  return hashIndex > -1 ? data.slice(0, hashIndex) : data;
}

export {
  getRequstId,
  getVoiceLength,
  msgBuilder,
  formatMessage,
  isNeedTimeLabel,
  getFakeMessage,
  getRawVoiceSrc,
  findMessageById,
  updateMessageByRequestId,
  REQUEST_TYPES,
  VERSION
};
