import { PromiseClient, SocketProvider } from '@youzan/imsdk_core';
import {
  msgBuilder,
  REQUEST_TYPES,
} from './utils';

const SOCKET_URL_MAP = {
  qatest: 'wss://mercury-ws-qa.qima-inc.com/dkf',
  pre: 'wss://mercury-ws.qima-inc.com/dkf',
  online: 'wss://im-mercury-ws.youzan.com/dkf'
};

function msgReceiver(msg) {
  let result = {};
  try {
    result = JSON.parse(msg.body);
  } catch (e) {
    console.log(e);
  }
  result.req_id = msg.reqId;
  result.status = msg.status;
  return result;
}

class WsClient {
  constructor(data) {
    this.token = data.token;
    this.beatInterval = 30000;
    this.beatTimeout = 10000;
    this.maxBeatTimeoutTimes = 5;
    this.beatTimeoutCount = 0;
    this.cacheable = true;
    this.authReady = false;
    this.beatQueue = [];
    this.requestChannel = data.requestChannel;
    this.initWS();
  }

  initWS() {
    this.ws = new PromiseClient({
      url: SOCKET_URL_MAP.online,
      maxRetryAttempts: 3,
      timeout: 5000,
      proto: [],
      mapRequestId: value => value.reqId,
      mapResult: value => value.status === 0,
      socketProvider: SocketProvider.WEAPP
    });
    this.initEvent();
    this.ws.connect();
  }

  initEvent() {
    if (this.ws) {
      this.ws.eventEmitter.on('open', () => {
        this.auth();
        this.heartBeat();
      });

      this.ws.eventEmitter.on('close', () => {
        this.authReady = false;
        this.stopHeartbeat();
      });

      this.ws.eventEmitter.on('packet', (data) => {
        if (data.reqType === REQUEST_TYPES.NEW) {
          this.ws && this.ws.eventEmitter.emit('conversationCreate', data);
        }

        if (data.reqType === REQUEST_TYPES.HEARTBEAT) {
          this.clearHeartbeatTimeout();
        }

        if (data.reqType === REQUEST_TYPES.TALK) {
          this.ws && this.ws.eventEmitter.emit('history', data);
        }

        if (data.reqType === REQUEST_TYPES.MESSAGE || data.reqType === REQUEST_TYPES.NORMAL || data.reqType === REQUEST_TYPES.KICKOFF) {
          this.ws && this.ws.eventEmitter.emit('message', {
            msg: msgReceiver(data),
            reqType: data.reqType
          });
        }
      });
    }
  }

  isReady(options = {}) {
    if (options.noAuth) {
      return !!(this.ws && this.ws.socket && this.ws.socket.readyState === this.ws.socket.OPEN);
    }
    return this.authReady;
  }

  async auth() {
    try {
      await this.send({
        token: this.token,
        endpoint: 'wap',
        endpointDetail: '零售导购小程序名片wap'
      }, { type: REQUEST_TYPES.AUTH, noAuth: true });
      this.authReady = true;
      this.ws && this.ws.eventEmitter.emit('authSuccess');
    } catch (e) {
      this.authReady = false;
      this.ws && this.ws.eventEmitter.emit('authFail');
    }
  }

  send(msg, options) {
    if (this.isReady(options)) {
      msg = msgBuilder(msg, this.requestChannel, options.type, options.reqId);
      return this.ws.promiseSend(msg);
    }

    // if (options.needCache) {
    //   //@TODO
    // }
    return Promise.reject(new Error('Socket is not ready'));
  }

  heartBeat() {
    const timer = setTimeout(() => {
      this.beatTimeoutCount += 1;
      if (this.beatTimeoutCount > this.maxBeatTimeoutTimes) {
        this.stopHeartbeat();
        this.ws && this.ws.close(true);
      }
    }, this.beatTimeout);

    this.beatQueue.push(timer);
    this.send(null, { type: REQUEST_TYPES.HEARTBEAT, noAuth: true });
    this.beatTimer = setTimeout(() => this.heartBeat(), this.beatInterval);
  }

  clearHeartbeatTimeout() {
    this.beatTimeoutCount = 0;
    this.beatQueue.forEach(timer => {
      clearTimeout(timer);
    });
    this.beatQueue = [];
  }

  stopHeartbeat() {
    clearTimeout(this.beatTimer);
    this.clearHeartbeatTimeout();
  }

  close() {
    this.ws.close();
  }
}

export { WsClient, msgBuilder };
