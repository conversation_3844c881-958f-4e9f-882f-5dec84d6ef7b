.page-iPhone-X {
  .chat-list__scrollview {
    height: calc(100vh - 87px);
  }
}

.chat {
  &-container {
    background: #f7f8fa;
  }

  &-list {
    &__scrollview {
      height: calc(100vh - 53px);
      max-height: 100%;
    }

    &__loading {
      text-align: center;
      margin: 10px auto;
    }
  }

  &-panel {
    &__iphonex {
      padding-bottom: 34px;
    }
  }
}

.wechat-popup {
  padding: 20px 40px;
  text-align: center;

  &__value {
    display: inline-block;
    font-weight: 500;
    margin-left: 15px;
  }

  &__btn {
    display: block;
    width: 200px;
    margin: 20px 0 10px 0;
  }

  &__tips {
    font-size: 14px;
    color: #9b9b9b;
  }
}

.message-item {
  margin: 10px 0;
  width: 100%;
  min-height: 40px;

  &__card {
    clear: both;
    margin: 0 auto;
    width: 80%;
  }

  &__name {
    font-size: 12px;
    color: #969799;
    line-height: 18px;
    margin-bottom: 7px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &__time {
    text-align: center;
    font-size: 12px;
    line-height: 14px;
    margin-bottom: 15px;
    color: #999;
    &--value {
      padding: 3px 5px;
      background-color: #dbdbdb;
      display: inline-block;
      border-radius: 3px;
      min-width: 60px;
      color: #fff;
    }
  }

  &__left {
    float: left;

    .message-item {
      &__contentWithName {
        float: left;
        margin-left: 12px;
      }

      &__content {
        float: left;
        &::after {
          border-right-color: #fff;
          left: -16px;
        }
      }

      &__avatar {
        float: left;
        margin-left: 15px;
      }
    }
  }

  &__right {
    float: right;

    .message-item {
      &__contentWithName {
        float: right;
        margin-right: 12px;
      }

      &__content {
        float: right;
        background: #a4e562;

        &::after {
          border-left-color: #a4e562;
          right: -16px;
        }
      }

      &__loading {
        float: right;
      }

      &--fail {
        float: right;
      }

      &__avatar {
        float: right;
        margin-right: 15px;
      }
    }
  }

  &__avatar {
    width: 40px;
    height: 40px;
    vertical-align: middle;
    image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }

  &__contentWithName {
    max-width: 60%;
  }

  &__content {
    width: 100%;
    background: #fff;
    padding: 9px 12px;
    font-size: 14px;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      top: 10px;
      border: 10px solid transparent;
    }
  }

  &__status {
    width: 22px;
    height: 22px;
    float: right;
    &--loading {
      height: 15px;
      width: 15px;
      margin-top: 10px;
      background: url('https://img.yzcdn.cn/wap-im/img/loading.png') center
        center no-repeat;
      background-size: 15px 15px;
      animation: loading 1s linear infinite;
    }

    &--fail {
      width: 22px;
      height: 22px;
      margin-top: 7px;
      margin-left: -10px;
      background: url('https://img.yzcdn.cn/wap-im/img/error.png') center center
        no-repeat;
      background-size: 22px 22px;
    }
  }
}

.message-image {
  background: #f7f8fa !important;
  &.message-item__content::after {
    display: none;
  }
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.bi-popup {
  background-color: transparent !important;
  width: 100%;
  height: 100%;

  &__container {
    position: relative;
    height: 100%;
    width: 100%;
  }

  &__arrow {
    height: 150px;
    width: 98px;
    position: relative;
    left: 60%;
  }

  &__pointer {
    width: 32px;
    height: 42px;
    float: left;
    margin-right: 10px;
  }

  &__button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 10px 40px;
    border-radius: 22px;
    text-align: center;
    width: 90px;
    border: 1px solid #fff;
  }

  &__text {
    font-size: 18px;
    width: 190px;
    margin: 0 auto;
  }

  &__auth {
    position: relative;
    top: 50%;
    background: #fff;
    width: 263px;
    height: 290px;
    margin: -145px auto 0;

    &--image {
      width: 263px;
      height: 131px;
    }

    &--title {
      color: #333;
      text-align: center;
      font-size: 16px;
      margin: 13px auto 8px auto;
      font-weight: 500;
    }

    &--text {
      font-size: 14px;
      color: #666;
      text-align: center;
    }

    &--btn {
      border-top: 1px solid #dcdde0;
      color: #1aad19;
      text-align: center;
      font-size: 16px;
      padding: 5px 0;
      margin-top: 20px;
      background: #fff;
    }
  }
}
