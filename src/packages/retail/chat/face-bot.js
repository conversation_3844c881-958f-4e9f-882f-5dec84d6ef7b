import RegexTrie from './regex-trie';
import { emojiCdnMap } from './constants';

class FactBot {
  constructor() {
    this.trie = new RegexTrie();
    this.addData(emojiCdnMap);
    this.regex = this.trie.regex('g');
  }

  addData(data) {
    this.trie.add(Object.keys(data));
  }

  decode(str) {
    let result = str;
    result = result.replace(this.regex, (word) => {
      word = this.replaceToImage(word);
      return word;
    });
    return result;
  }

  replaceToImage(word) {
    const resource = `${emojiCdnMap[word]}@2x.png`;
    return `<img style="width: 24px; height: 24px; vertical-align: middle;" src="https://b.yzcdn.cn/v2/image/emoticons/${resource}" alt="" />`;
  }
}

export default FactBot;
