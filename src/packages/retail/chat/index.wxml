<page-container id="page" pageBgColor="#FFF" forbidCopyright="{{ true }}" class="page-{{ deviceType }}">
  <view class="chat-container">
    <scroll-view class="chat-list__scrollview" scroll-y="true" scroll-top="{{ scrollTop }}" scroll-into-view="{{ scrollIntoView }}" bindscrolltoupper="handleScrollToTop" bindscroll="handleScrollEvent">
      <view wx:if="{{ scrollToTop }}" class="chat-list__loading">
        <van-loading type="spinner" size="20px" />
      </view>
      <block wx:for="{{ messageList }}" wx:key="index">
        <view wx:if="{{ item.msg_type === 'bi_card' }}" id="message{{item.msg_id}}" class="message-item__card">
          <card-message message="{{ item }}" />
        </view>
        <view wx:else id="message{{item.msg_id}}" class="message-item message-item__{{ item.is_self ? 'right' : 'left' }}">
          <view wx:if="{{ item.needTimeStamp }}" class="message-item__time">
            <view class="message-item__time--value">{{ item.timeStr }}</view>
          </view>
          <view class="message-item__avatar" bind:tap="handleAvatarClick" data-message="{{ item }}">
            <image src="{{ item.sender_avatar || 'https://img.yzcdn.cn/public_files/2018/04/24/defaultAvatar.png' }}" mode="aspectFit" />
          </view>
          <view class="message-item__contentWithName">
            <view class="message-item__name" wx:if="{{ !item.is_self }}">{{ sales.showName || item.sender_nickname }}</view>
            <view class="message-item__content {{ item.msg_type === 'image' ? 'message-image' : '' }}">
              <text-message wx:if="{{ item.msg_type === 'text' }}" message="{{ item }}" />
              <image-message wx:elif="{{ item.msg_type === 'image'}}" message="{{ item }}" bind:preview="toggleFullScreenImage" />
              <voice-message id="message{{item.msg_id}}-voice" wx:elif="{{ item.msg_type === 'voice'}}" message="{{ item }}" bind:clearvoice="clearVoice" />
              <interactive-message wx:elif="{{ item.msg_type === 'sales_default' }}" message="{{ item }}" bind:copy="toggleWechatCopy" bind:send="handleSendMessage" bind:preview="toggleFullScreenImage" />
              <view wx:else>不支持的消息类型</view>
            </view>
          </view>
          <view wx:if="{{ item.msg_type !=='image' }}" class="message-item__status">
            <view wx:if="{{ item.msg_status === 'PRE_SEND' || item.msg_status === 'SEND' }}" class="message-item__status--loading" />
            <view wx:if="{{ item.msg_status === 'FAILED'}}" class="message-item__status--fail" />
          </view>
        </view>
      </block>
    </scroll-view>
    <input-bar id="inputBar" showEmojiPanel="{{ showEmojiPanel }}" bind:toggle="togglePanel" bind:send="handleSendMessage" />
    <!-- <van-transition show="{{ showEmojiPanel || showExtraPanel }}" name="slide-up"> -->
    <view wx:if="{{ showEmojiPanel || showExtraPanel }}" class="{{ deviceType === 'iPhone-X' ? 'chat-panel__iphonex' : ''}}">
      <emoji-panel wx:if="{{ showEmojiPanel }}" bind:toggle="togglePanel" bind:setEmoji="setEmoji" bind:send="handleEmojiPanelSend" bind:delete="handleDeleteInput" />
      <extra-panel wx:if="{{ showExtraPanel }}" bind:send="handleSendMessage" />
    </view>
    <!-- </van-transition> -->
  </view>
</page-container>
<van-popup show="{{ showWechatPopup }}" bind:close="toggleWechatCopy" position="bottom">
  <view class="wechat-popup">
    <van-icon name="wechat" color="#1AAD19" custom-style="vertical-align:middle; display:inline-block" />
    <view class="wechat-popup__value">{{ sales.wx }}</view>
    <view>
      <van-button custom-class="wechat-popup__btn" disabled>已复制</van-button>
    </view>
    <view class="wechat-popup__tips">通讯录右上角-添加朋友-点击输入框粘贴</view>
  </view>
</van-popup>
<van-popup show="{{ showAuthPopup }}" custom-class="bi-popup">
  <view class="bi-popup__container">
    <view class="bi-popup__auth">
      <image class="bi-popup__auth--image" src="https://b.yzcdn.cn/retail/img/member/<EMAIL>" />
      <view class="bi-popup__auth--title">登录并授权</view>
      <view class="bi-popup__auth--text">
        <view>申请获取以下权限</view>
        <view>获得你的公开信息（昵称、头像）</view>
      </view>
      <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="bindGetUserInfo">
        <button class="bi-popup__auth--btn">允许</button>
      </user-authorize>
    </view>
  </view>
</van-popup>
<van-toast id="van-toast" />
