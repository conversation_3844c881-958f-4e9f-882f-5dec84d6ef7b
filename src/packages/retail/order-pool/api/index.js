import { getReatilSource } from 'retail/util/global';
import orderPoolApi from './order-pool';
import orderPoolGoodsApi from './order-pool-goods';
import shopApi from './shop';

const app = getApp();

function isDef(v) {
  return typeof v === 'object' && v !== null;
}

let hideLoadingTimer = null;
// 注入公共参数
function inject(fn) {
  const injectStaticData = {
    // retailSource: getReatilSource()
  };
  return data => {
    if (!isDef(data)) {
      throw new Error('入参必须为对象');
    }
    const injectData = {
      retailSource: getReatilSource() || 'MINAPP-SHELF-3.6.16',
      rootKdtId: app.getHQKdtId()
    };
    const mergeData = {
      ...injectStaticData,
      ...injectData,
      ...data
    };
    return addLoading(fn(mergeData));
  };

  // 简单的实现一个节流fn
  // 后端要求同一时间不要多次触发某些请求
  // 所以利用微信原生loading简单实现一下
  function addLoading(val) {
    wx.showLoading({ mask: true, title: '加载中' });
    return Promise.resolve(val)
      .then(ret => {
        if (hideLoadingTimer) {
          clearTimeout(hideLoadingTimer);
        }
        hideLoadingTimer = setTimeout(() => {
          wx.hideLoading();
        }, 700);
        return ret;
      })
      .catch(err => {
        wx.hideLoading();
        return Promise.reject(err);
      });
  }
}

const needInjectParamsApi = {
  ...orderPoolApi,
  ...orderPoolGoodsApi
};

export default {
  ...Object.keys(needInjectParamsApi).reduce((api, key) => {
    api[key] = inject(needInjectParamsApi[key]);
    return api;
  }, {}),
  ...shopApi
};
