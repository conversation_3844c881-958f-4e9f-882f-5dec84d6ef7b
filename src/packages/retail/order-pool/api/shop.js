import promisify from 'utils/promisify';

const app = getApp();
const carmen = promisify(app.carmen);

// 获取用户地理位置
function getLocation() {
  return new Promise((resolve, reject) => {
    wx.getSetting({
      success(res) {
        if (!res.authSetting['scope.userLocation']) {
          wx.authorize({
            scope: 'scope.userLocation',
            success() {
              wx.getLocation({
                type: 'gcj02',
                fail: reject,
                success: resolve
              });
            },
            fail: reject
          });
        } else {
          wx.getLocation({
            type: 'gcj02',
            fail: reject,
            success: resolve
          });
        }
      }
    });
  });
}

/**
 * 获取店铺信息
 */
async function getShopInfo() {
  return carmen({
    api: 'youzan.retail.shop.consumer.info/1.0.0/query'
  });
}

/**
 * 根据指定id获取用户地址信息
 */
function getUserAddress({ userId, addressId }) {
  return app.request({
    path: '/wscshop/api/showcase-retail/getAddress.json',
    method: 'GET',
    data: {
      userId,
      addressId
    }
  });
}

/**
 * 获取供货点信息
 * @param {Object}
 * @returns {Object} 包含供货点id，起送价相关信息
 */
async function getWarehouse({ kdtId }) {
  const { latitude: lat, longitude: lon } = await getLocation().catch(() => {});
  return app
    .request({
      path: '/retail/h5/miniprogram/queryWareHouse.json',
      method: 'GET',
      data: {
        kdtId,
        lat,
        lon
      }
    })
    .then(result => {
      const { checkStartFee, startFee, warehouseId, ...args } = result;
      // 只要请求派仓，则必然要派到一个仓，否则直接抛异常，让上层处理
      if (!warehouseId) {
        return Promise.reject({
          msg: '该外卖地址不可用'
        });
      }
      return {
        ...args,
        warehouseId,
        startPrice: checkStartFee ? startFee : 0
      };
    });
}

/**
 * 判断店铺是供货还是铺货
 * @param {String} kdtId
 * @returns {Number} 1: 铺货 0：供货 目前默认返回铺货，虽然店铺默认都是供货，但是供货需要额外查询一些接口，而铺货就是走的老的网店逻辑
 */
function getOnlineSupplyMode(kdtId) {
  return app
    .request({
      path: '/wscshop/shop/config.json',
      method: 'GET',
      data: {
        kdtId,
        key: 'online_subshop_supply_mode'
      }
    })
    .then(({ online_subshop_supply_mode: mode = 1 }) => +mode)
    .catch(() => 1);
}

export default {
  getShopInfo,
  getUserAddress,
  getWarehouse,
  getOnlineSupplyMode
};
