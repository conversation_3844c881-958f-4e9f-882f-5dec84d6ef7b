/* 拼单商品相关接口 */
const app = getApp();

/**
 * 修改拼单商品操作
 * @param {Object} data jointId, operateType: 1 确认 2 修改 3 清空
 */
function operateGoods(data) {
  return app.request({
    path: '/retail/h5/miniprogram/orderPool/operateGoods.json',
    method: 'POST',
    data
  });
}

/**
 * 和他点一样
 * @param {Object} data fromBuyerId, jointId, clientId
 */
function syncGoods(data) {
  return app.request({
    path: '/retail/h5/miniprogram/orderPool/syncGoods.json',
    method: 'GET',
    data
  });
}

export default {
  operateGoods,
  syncGoods
};
