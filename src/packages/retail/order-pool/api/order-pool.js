import mapKeyCase from '@youzan/weapp-utils/lib/map-keys-case';

/* 拼单相关接口 */
const app = getApp();

/**
 * 创建拼单
 * @param {Object} data addressId, deliveryType: 1 外送 2 自提
 */
function createOrderPool(data) {
  return app.request({
    path: '/retail/h5/miniprogram/orderPool/create.json',
    method: 'POST',
    data
  });
}

/**
 * 加入拼单
 * @param {Object} data jointId
 */
function joinOrderPool(data) {
  return app.request({
    path: '/retail/h5/miniprogram/orderPool/join.json',
    method: 'POST',
    data
  });
}

/**
 * 查询拼单详情
 * @param {Object} data jointId
 */
function getOrderPoolDetail(data) {
  return app.request({
    path: '/retail/h5/miniprogram/orderPool/getDetail.json',
    method: 'GET',
    data
  });
}

/**
 * 查询总店下是否存在拼单进行中的店铺
 * @param {Object} data rootKdtId
 */
function checkActiveOrderPool(data) {
  return app.request({
    path: '/retail/h5/miniprogram/orderPool/checkActiveOrderPool.json',
    method: 'GET',
    data
  });
}

/**
 * 锁定拼单
 * @param {Object} data jointId clientId
 */
function lockOrderPool(data) {
  return app.request({
    path: '/retail/h5/miniprogram/orderPool/lock.json',
    method: 'POST',
    data: {
      opType: 0,
      ...data
    }
  });
}

/**
 * 取消拼单
 * @param {Object} data jointId
 */
function cancelOrderPool(data) {
  return app.request({
    path: '/retail/h5/miniprogram/orderPool/cancel.json',
    method: 'POST',
    data
  });
}

/**
 * 获取bookKey
 */
function getBookKey({
  goodsList,
  activities = [],
  selfFetch = null,
  deliveryAddress = null,
  warehouseId = null
}) {
  return app.request({
    path: '/wscshop/api/showcase-retail/shop/postCacheOrderCreation.json',
    method: 'POST',
    data: mapKeyCase.toCamelCase({
      goodsList: goodsList.map(({ messages, cartId, ...args }) => {
        const params = { ...args };
        const subComboList = params.combo?.groupList
          ?.map(group => {
            return group.subComboList.map(subCombo => {
              const {
                goodsId,
                groupId,
                num,
                skuId,
                propertyIds = []
              } = subCombo;
              return { goodsId, groupId, num, skuId, propertyIds };
            });
          })
          .flat();
        params.extensions = {
          CART_ID: cartId
        };
        if (params?.combo?.groupList.length > 0) {
          params.combo = {
            comboType: params?.combo?.comboType,
            subComboList
          };
        }
        if (messages) params.itemMessage = messages;
        return params;
      }),
      activities,
      selfFetch,
      deliveryAddress,
      expressType: selfFetch ? 1 : 2,
      dispatcherWarehouseId: warehouseId || ''
    })
  });
}

/**
 *  切换地址和网店
 *  @param {Object} data addressId, jointId
 */
function switchAddress(data) {
  return app.request({
    path: '/retail/h5/miniprogram/orderPool/switch.json',
    method: 'POST',
    data
  });
}

/**
 * 查询店铺线下营业时间设置
 * com.youzan.shopcenter.outer.service.businesshours.ShopBusinessHoursReadOuterService#queryShopBusinessHours
 * @returns
 */
function getShopBusinessHours() {
  return app.request({
    path: '/retail/h5/miniprogram/store/shopBusinessHours.json',
    method: 'GET'
  });
}

export default {
  createOrderPool,
  joinOrderPool,
  getOrderPoolDetail,
  checkActiveOrderPool,
  lockOrderPool,
  cancelOrderPool,
  getBookKey,
  switchAddress,
  getShopBusinessHours
};
