// import deepClone from '@youzan/weapp-utils/lib/clone-deep/index';
import orderPoolGlobal from 'retail/util/order-pool';
import OrderPoolWS from '../order-pool-ws';
import { ORDER_POOL_STATUS, ORDER_POOL_TYPE, URLS } from '../constants';
import api from '../api';

const app = getApp();

const INTERVAL_TIME = 1000;

export default Behavior({
  properties: {},
  options: {
    pureDataPattern: /^_/
  },
  data: {
    participants: [], // 拼单参与人
    completedNum: 0, // 选购完成的人数
    joinedNum: 0, // 加入拼单的人数
    status: -1, // 拼单进度
    promoterBuyId: -1, // 发起人id
    addressId: -1, // 收获地址
    deliveryType: -1, // 配送方式
    jointId: -1, // 拼单id
    totalPayPrice: 0, // 拼单总价
    totalOriginPrice: 0,
    type: -1, // 拼单状态，ws消息返回
    goodsNum: 0,
    _ws: null,
    _participantMap: {}, // 记录participant在列表内的index
    _timer: null, // 定时更新器
    _bufferDiff: true // buffer diff 说明存在更新
  },
  methods: {
    // 启动拼单页ws
    async startUpOrderPoolWS({ jointId, orderPoolDetail }) {
      if (!jointId) {
        // console.warn(`[startUpOrderPoolWS] 需要参数 {jointId}`);
        return;
      }
      this.setData({
        jointId
      });

      /**
       * 先获取拼单详情数据初始化behavior的data
       * 数据初始化之后再建立ws消息
       * 再次获取拼单详情数据，初始化behavior的data
       * 保证接口返回的数据和建立ws之间延迟期间数据的实时性
       */

      let myParticipantInfo = {};
      orderPoolDetail.participants.some(item => {
        if (+item.buyerId === +app.getBuyerId()) {
          myParticipantInfo = item;
          return true;
        }
        return false;
      });
      const data = {
        ...orderPoolDetail,
        // 只保留index0，保证第一位是自己
        participants: [myParticipantInfo]
      };
      this._initWSBehavior(data);
      const ws = await this._initWS();
      const dataNext = await this.fetchOrderPoolDetail({
        jointId
      });
      // 不更新已有项，如果存在已有项，说明是 ws 返回的
      this._initWSBehavior(dataNext, false);
      return { orderPoolDetail: dataNext, ws };
    },
    // 关闭拼单页ws
    closeOrderPoolWS() {
      this.data._ws && this.data._ws.close();
      clearInterval(this.data._timer);
      this.clearOrderPool();
    },
    // 重置拼单页数据
    clearOrderPool() {
      this.setData({
        participants: [], // 拼单参与人
        completedNum: 0, // 选购完成的人数
        joinedNum: 0, // 加入拼单的人数
        status: -1, // 拼单进度
        promoterBuyId: -1, // 发起人id
        addressId: -1, // 收获地址
        deliveryType: -1, // 配送方式
        jointId: -1, // 拼单id
        totalPayPrice: 0, // 拼单总价
        totalOriginPrice: 0,
        type: -1, // 拼单状态，ws消息返回
        goodsNum: 0,
        _ws: null,
        _participantMap: {}, // 记录participant在列表内的index
        _timer: null, // 定时更新器
        _bufferDiff: true // buffer diff 说明存在更新
      });
    },
    // 获取拼单详情
    async fetchOrderPoolDetail({ jointId }) {
      const data = await api.getOrderPoolDetail({ jointId });
      const {
        addressId = -1,
        deliveryType = -1,
        totalPayPrice = 0,
        totalOriginPrice = 0,
        promoterBuyId = -1,
        goodsNum = 0,
        completedNum = 0,
        joinedNum = 0,
        status = -1
      } = data;

      // _updateOrderPool主要处理ws返回的，有一些字段在ws可能没有返回，也就不会得到更新。所以在这里初始化数据
      this.setData({
        addressId, // 收货地址
        deliveryType, // 配送方式
        jointId: data.jointId, // 拼单id
        totalPayPrice, // 拼单总价格
        totalOriginPrice,
        promoterBuyId,
        goodsNum,
        completedNum,
        joinedNum,
        status
      });

      return data;
    },
    // 初始化behavior
    _initWSBehavior(data, includeUpdate) {
      const {
        participants // 拼单参与人
      } = data;

      participants.forEach(participant => {
        this._updateParticipant(participant, includeUpdate);
      });
      this._updateOrderPool(data);
      this._bufferFlush();
    },
    async _initWS() {
      const { userId, accessToken } = app.getToken();
      const ws = new OrderPoolWS({ token: accessToken, userId: '' + userId });

      // 目前并不能做到全部依赖，大部分type依然需要全量拉取拼单详情数据。无奈
      ws.on(ORDER_POOL_TYPE.JOIN, data => {
        this._processData(data);
      });
      ws.on(ORDER_POOL_TYPE.GOODS, data => {
        this._processData(data);
      });
      ws.on(ORDER_POOL_TYPE.ONGOING, data => {
        this._processData(data);
      });
      ws.on(ORDER_POOL_TYPE.CONFIRM_GOODS, () => {
        this._reOrderPoolData(); // 需要重新获取拼单详情
      });
      ws.on(ORDER_POOL_TYPE.EDIT_GOODS, () => {
        this._reOrderPoolData(); // 需要重新获取拼单详情
      });
      ws.on(ORDER_POOL_TYPE.LOCKED, () => {
        this._reOrderPoolData(); // 需要重新获取拼单详情
      });
      ws.on(ORDER_POOL_TYPE.SWITCH, () => {
        // 需要重新reload
        const userInfo = app.getUserInfoSync() || {};
        wx.reLaunch({
          url: app.genOrderPullJumpUrl(`${
            URLS.ORDER_POOL
          }?orderPoolId=${orderPoolGlobal.getCurrentId()}&nickname=${
            userInfo.nickname
          }&kdt_id=${app.getKdtId()}`)
        });
      });
      ws.on(ORDER_POOL_TYPE.CANCEL, () => {
        this._reOrderPoolData(); // 需要重新获取拼单详情
      });
      ws.on(ORDER_POOL_TYPE.CREATE_ORDER, () => {
        this._reOrderPoolData(); // 需要重新获取拼单详情
      });
      ws.on(ORDER_POOL_TYPE.PAY, () => {
        this._reOrderPoolData(); // 需要重新获取拼单详情
      });
      ws.on(ORDER_POOL_TYPE.SWITCH_ADDRESS, () => {
        // 需要重新reload
        const userInfo = app.getUserInfoSync() || {};
        wx.reLaunch({
          url: app.genOrderPullJumpUrl(`${
            URLS.ORDER_POOL
          }?orderPoolId=${orderPoolGlobal.getCurrentId()}&nickname=${
            userInfo.nickname
          }&kdt_id=${app.getKdtId()}`)
        });
      });
      ws.on(ORDER_POOL_TYPE.UNLOCK, () => {
        // 解锁拼单，拼单状态重置
        this.setData({
          status: ORDER_POOL_STATUS.INIT
        });
      });

      try {
        await ws.getClient();

        const timer = setInterval(() => {
          this._bufferFlush();
        }, INTERVAL_TIME);

        this.setData({
          _ws: ws,
          _timer: timer
        });
      } catch (error) {
        // console.log(error);
      }

      return ws;
    },
    // 处理ws过来的消息
    async _processData(data) {
      const { participant } = data;
      if (!participant) return;

      // 用户第一人称操作，立即 render
      if (+participant?.buyerId === +app.getBuyerId()) {
        this._updateImmediate(data);
      } else {
        this._updateParticipant(participant);
        this._updateOrderPool(data);
      }
    },
    // 重新获取拼单详情
    async _reOrderPoolData() {
      const jointId = this.data.jointId;
      const data = await this.fetchOrderPoolDetail({ jointId });
      this._initWSBehavior(data);
    },
    // 修改数据，不触发setData
    _updateOrderPool(data) {
      const { completedNum, joinedNum, type } = data;
      this.data.completedNum = completedNum;
      this.data.joinedNum = joinedNum;

      if (type) {
        this.data.type = type;
      }
      this.setData({
        _participantMap: this.data._participantMap,
        _bufferDiff: true
      });
    },
    // rerender
    _bufferFlush() {
      // 仅有新数据来了，bufferDiff == true
      if (this.data._bufferDiff) {
        const { participants, completedNum, joinedNum, type } = this.data;
        this.setData({
          participants,
          completedNum,
          joinedNum,
          type,
          _bufferDiff: false
        });
      }
    },
    // 立即更新
    _updateImmediate(data) {
      const { participant } = data;
      this._updateParticipant(participant);
      this._updateOrderPool(data);
      this._bufferFlush();
    },
    // 更新参与人信息，修改数据，不触发setData，includeUpdate == true，包括更新已有项，false 不更新已有项，只新增。
    _updateParticipant(participant, includeUpdate = true) {
      const { participants, _participantMap } = this.data;
      const buyerId = participant?.buyerId;
      if (!buyerId) return;
      if (_participantMap[buyerId] === undefined) {
        const length = participants.push(participant);
        _participantMap[buyerId] = length - 1;
        return;
      }
      if (_participantMap[buyerId] !== undefined && includeUpdate) {
        participants[_participantMap[buyerId]] = participant;
      }
    },
    getParticipants() {
      return this.data.participants;
    },
    getWSData() {
      return this.data;
    }
  }
});
