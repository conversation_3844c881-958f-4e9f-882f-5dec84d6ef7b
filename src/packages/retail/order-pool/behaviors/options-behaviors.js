/* 公共操作方法 */
import orderPoolGlobal from 'retail/util/order-pool';
import api from '../api/index';
import { URLS } from '../constants';

const app = getApp();

export default Behavior({
  methods: {
    onBack() {
      const pages = getCurrentPages();
      if (pages.length === 1) {
        wx.reLaunch({
          url: URLS.HOME
        });
        return;
      }
      wx.navigateBack();
    },
    onJumpHome() {
      wx.reLaunch({
        url: URLS.HOME
      });
    },
    onOrder() {
      // 若拼单页跳点单页面，直接关闭所有页面
      // 这样就不用考虑出现点单页点左上角返回按钮又跳回拼单页的场景，此时点单页返回会直接跳微页面首页
      // 省去了一大堆判断用户拼单状态的逻辑，小机灵鬼
      const mode = orderPoolGlobal.getCurrentMode();
      app.globalData.shelfParams = {
        scene: 1,
        isOrder: 1
      };
      wx.reLaunch({
        url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=${mode}&scene=1&isOrder=1`)
      });
    },
    // 和他点一样
    async onSyncGoodsWithSelectedBuyer({ fromBuyerId, jointId, clientId }) {
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'sync_cart', // 事件标识
          en: '和他点一样', // 事件名称
          params: {} // 事件参数
        });
      await api
        .syncGoods({
          fromBuyerId,
          jointId,
          clientId
        })
        .then(result => {
          console.log(result);
        });
    },
    // 修改商品
    async onChangeGoods(jointId) {
      await api.operateGoods({ jointId, operateType: 2 }).then(() => {
        this.onOrder();
      });
    },
    // 清空商品
    async onClearGoods(jointId) {
      await api.operateGoods({ jointId, operateType: 3 });
    }
  }
});
