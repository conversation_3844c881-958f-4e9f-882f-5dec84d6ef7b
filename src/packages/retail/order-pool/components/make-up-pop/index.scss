.make-up-pop {
  border-radius: 8px 8px 0 0;
  height: 70%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &__title {
    flex-shrink: 0;
    height: 88rpx;
    line-height: 88rpx;
    padding: 0 32rpx;
    color: #333;
    font-size: 32rpx;
    font-weight: 500;
  }

  &__content {
    flex: 1;
    // height: 300px;
    overflow-y: auto;
    background-color: #F2F3F5;

    .banner {
      padding: 0 32rpx;
      background-color: #FFFBE8;
      color: #323233;
      font-size: 24rpx;
      height: 72rpx;
      line-height: 72rpx;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .placeholder {
      height: 100rpx;
    }
  }

  &__activity {
    margin-bottom: 20rpx;
  }

  .make-up-pop__item {
    background-color: #fff;
    padding-left: 32rpx;
  }

  .make-up-pop__goods:last-child {
    padding: 24rpx 32rpx;
    padding-left: 0;
    background-color: #fff;
    border-bottom: 0.5rpx solid #DCDEE0;
  }

  .goods-skeleton {
    display: inline-flex;
    animation: blink 1s infinite alternate;
    width: 100%;
    height: 200rpx;
    padding: 24rpx;
    box-sizing: border-box;
    background: white;
    will-change: opacity;
  
    & .block {
      background: #eeeeee;
    }
  
    & .square {
      width: 160rpx;
      height: 160rpx;
      border-radius: 8rpx;
    }
  
    & .rectangle {
      height: 40rpx;
      width: 240rpx;
  
      &:nth-child(2n + 2) {
        width: 180rpx;
        margin: 20rpx 0;
      }
    }
  }

  .error {
    height: 88rpx;
    text-align: center;
    line-height: 88rpx;
    color: #959799;
    font-size: 24rpx;
  }
}
