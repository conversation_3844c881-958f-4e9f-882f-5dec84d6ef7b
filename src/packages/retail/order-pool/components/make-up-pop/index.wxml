<popup
  show="{{ showMakeUp }}"
  overlay
  z-index="{{98}}"
  position="bottom"
  custom-class="make-up-pop"
  bind:close="closeMakeUp"
>
  <view class="make-up-pop__title">凑单</view>
  <scroll-view scroll-y class="make-up-pop__content">
    <template is="skeleton" wx:if="{{loading}}" />
    <view class="error" wx:if="{{!loading && error}}">获取凑单商品失败</view>
    <view
      class="make-up-pop__activity"
      wx:if="{{!loading && !error}}"
      wx:for="{{makeUpList}}"
      wx:for-item="activity"
      wx:key="id"
    >
      <view class="banner">{{activitiesDesc[activity.id] || activity.bannerContent}}</view>
      <view
        class="make-up-pop__item"
        wx:for="{{activity.goodsList}}"
        wx:for-item="onegoods"
        wx:key="alias"
      >
        <goods-item
          custom-class="make-up-pop__goods"
          isSuspendShop="{{isSuspendShop}}"
          goods="{{onegoods}}"
          themeMainColor="{{themeMainColor}}"
          noneSku="{{chosenTotal[onegoods.id] && chosenTotal[onegoods.id].noneSku}}"
          currentNum="{{chosenTotal[onegoods.id] && chosenTotal[onegoods.id].num}}"
          bind:clickShowDetail="onGoodsItemDetail"
          bind:editGoodsNum="onGoodsNumChange"
        />
      </view>
    </view>
    <view class="placeholder" />
  </scroll-view>
</popup>

<template name="skeleton">
  <view
    class="goods-skeleton"
    wx:for="1234"
    wx:key="*this"
  >
    <view class="block square" />
    <view style="margin-left: 16rpx;">
      <view class="block rectangle"></view>
      <view class="block rectangle"></view>
      <view class="block rectangle"></view>
    </view>
  </view>
</template>

