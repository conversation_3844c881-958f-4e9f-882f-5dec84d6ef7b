const app = getApp();

Component({
  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        this.setData({
          showMakeUp: val
        });
        if (val) {
          this.getActivitiesGoods();
        }
      }
    },
    activities: {
      type: <PERSON><PERSON>y,
      observer(activities) {
        const activitiesDesc = activities.reduce((ret, activity) => {
          const { activityId, activityDesc, activityType } = activity;
          if (activityType === 101) {
            ret[activityId] = activityDesc;
          }
          return ret;
        }, {});
        this.setData({ activitiesDesc });
      }
    },
    isSuspendShop: {
      type: Boolean,
      value: false
    },
    chosenTotal: {
      type: Object,
      value: {}
    },
    themeMainColor: String
  },
  data: {
    showMakeUp: false,
    testRet: {},
    makeUpList: [],
    loading: false,
    error: false,
    activitiesDesc: {}
  },
  methods: {
    getActivitiesGoods() {
      const { activities } = this.data;
      const kdtId = app.getKdtId();

      this.setData({
        loading: true
      });

      const makeUpActivities = activities.filter(
        ({ activityType }) => [101, 104].indexOf(activityType) > -1
      );
      app
        .request({
          path: '/wscump/shelf/get-make-up-result.json',
          method: 'POST',
          data: {
            kdtId,
            activities: makeUpActivities.map(
              ({ activityType, activityId, activityAlias }) => {
                return { activityType, activityId, activityAlias };
              }
            )
          }
        })
        .then(res => {
          this.setData({
            makeUpList: res,
            loading: false,
            error: false
          });
        })
        .catch(() => {
          this.setData({
            loading: false,
            error: true
          });
        });
    },
    onGoodsItemDetail(e) {
      this.triggerEvent('clickShowDetail', e.detail);
    },

    onGoodsNumChange(e) {
      this.triggerEvent('editGoodsNum', e.detail);
    },

    closeMakeUp() {
      this.setData({
        showMakeUp: false
      });
      this.triggerEvent('onMakeUpClose');
    }
  }
});
