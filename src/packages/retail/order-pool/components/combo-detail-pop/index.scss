.combo-detail-pop {
  border-radius: 20px 20px 0 0;
  background-color: #f2f3f4;

  &__title {
    height: 44px;
    line-height: 44px;
    color: #333;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
  }

  &__content {
    background-color: #f2f3f4;

    .cart-body {
      max-height: calc(80vh - 95px);
      min-height: 150px;
      padding-bottom: 56px;
      background-color: #f2f3f4;
      box-sizing: border-box;
    
      .group-bigwrap {
        background-color: #f2f3f4;
        padding-top: 14px;
    
        .group-wrap {
          background-color: white;
          margin: 0 12px 12px;
          border-radius: 8px;
          padding: 10px 0;
    
          .head {
            margin-left: 12px;
    
            .title {
              font-size: 14px;
              color: #323233;
              font-weight: 700;
            }
    
            .tips {
              font-size: 12px;
              color: #969799;
            }
          }
        }
      }
    }
  }
}
