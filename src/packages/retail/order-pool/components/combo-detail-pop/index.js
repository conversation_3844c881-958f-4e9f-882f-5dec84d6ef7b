import { mapState, mapMutations } from '@youzan/vanx';
import { GoodsComponent } from '@goods/common/vanx-component';

GoodsComponent({
  properties: {
    themeMainColor: String,
    info: Object
  },
  data: {},

  mapData: {
    ...mapState(['ComboDetailShow', 'ComboDetail'])
  },

  methods: {
    ...mapMutations(['setComboDetailShow']),

    closeComboDetail() {
      this.setComboDetailShow(false);
    }
  }
});
