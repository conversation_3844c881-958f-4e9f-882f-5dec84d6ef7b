<popup
 show="{{ ComboDetailShow }}"
 overlay
 closeable
 position="bottom"
 custom-class="combo-detail-pop"
 bind:close="closeComboDetail"
>
  <view class="combo-detail-pop__title">套餐详情</view>
  <scroll-view scroll-y class="combo-detail-pop__content">
    <view class="cart-body">
      <view class="group-bigwrap">
        <view
         class="group-wrap"
         wx:for="{{ComboDetail.groupList}}"
         wx:for-item="group"
         wx:key="alias"
        >
          <view class="head">
            <text class="title"> {{group.title}} </text>
            <text class="tips" wx:if="{{ComboDetail.comboType === 1}}"> （任选 {{group.rule.selectNum}} 份） </text>
          </view>
          <view
           class="combo-detail-pop__item"
           wx:for="{{group.subComboList}}"
           wx:for-item="subCombo"
           wx:key="alias"
          >
            <goods-item
             hide-combo-detail
             custom-class="combo-detail-pop__goods"
             info="{{subCombo}}"
             themeMainColor="{{themeMainColor}}"
            />
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</popup>

