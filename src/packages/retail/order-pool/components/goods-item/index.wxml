<view style="--theme-main-color: {{themeMainColor}}" class="goods">
  <view class="goods-image">
    <image class="goods-image__img" src="{{info.invalid ? invalidImg : info.imgUrl || info.thumbUrl}}" />
  </view>
  <view class="goods-info">
    <view class="goods-info__title {{info.invalid ? 'invalid' : ''}}">{{info.title}}</view>
    <view class="goods-info__sku {{info.invalid ? 'invalid' : ''}}" wx:if="{{ !hideComboDetail }}">{{info.showText}}</view>
    <view class="goods-info__sku" wx:elif="{{ hideComboDetail }}">
      <view wx:if="{{info.skuName || info.propDesc}}">
        <text wx:for="{{info.skuName}}" wx:key="{{item + index}}">
          {{item.sn}}<text style="color: {{themeMainColor}}">{{item.sp}}</text>
          <text wx:if="{{ info.propDesc || index !== info.skuName.length - 1}}">;</text>
        </text>
        <text wx:for="{{info.propDesc}}" wx:key="{{item + index}}">
          {{item.pn}}<text style="color: {{themeMainColor}}">{{item.pp}}</text>
          <text wx:if="{{index !== info.propDesc.length - 1}}">;</text>
        </text>
      </view>
    </view>

    <view class="goods-info__price {{info.invalid ? 'invalid' : ''}}">
      <!-- 价格组件通过themeMainColor显示全店风格，好友拼单不需要在此项目修复，此为临时方案 -->
      <price wx:if="{{ themeMainColor }}" style="--price-color: {{themeMainColor}}" price="{{info.price}}" />
      <price wx:else style="--price-color: {{info.invalid ? '#c8c9cc' : '#323233'}}" price="{{info.price}}" />
      <view class="goods-info__price--stock {{info.invalid ? 'invalid' : ''}}">x{{info.num}}</view>
    </view>
  </view>
  <!-- class="goods-package"这部分理论要抽成一个组件，但后期会中台化，中台化里有对应组件，这里先这样 -->
  <view class="goods-package" wx:if="{{ !hideComboDetail && songoods.length }}">
    <view class="goods-package__head">
      <view class="goods-package__head--title">套餐包含以下商品</view>
      <view class="goods-package__head--details" bindtap="open">
        <text class="detail">详情</text>
        <van-icon name="arrow" class="icon-arrow" />
      </view>
    </view>
    <scroll-view class="goods-package__content" scroll-x>
      <view class="goods-package__content__bigbox" bindtap="open">
        <view
         class="goods-package__content__bigbox__wrap"
         wx:for="{{songoods}}"
         wx:for-item="item"
         wx:for-index="index"
         wx:key="index"
        >
          <image class="goods-package__content__bigbox__wrap--image" lazy-load src="{{item.thumbUrl}}" />
          <text class="goods-package__content__bigbox__wrap--num">
            x{{ item.num }}
          </text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
