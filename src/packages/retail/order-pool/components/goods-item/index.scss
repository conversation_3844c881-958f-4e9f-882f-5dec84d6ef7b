:host {
  
}

.goods {
  display: flex;
  flex-wrap: wrap;
  padding: 0 24rpx;;
  margin-bottom: 48rpx;
  min-height: 192rpx;
  align-items: center;
}

.invalid {
  color: #c8c9cc !important;
}

.goods-image {
  display: flex;
  margin-right: 16rpx;
  width: 144rpx;
  height: 144rpx;
  flex-shrink: 0;
  border-radius: 4rpx;
  background-color: #ddd;

  &__img {
    display: flex;
    width: 100%;
    height: 100%;
    border-radius: 4rpx;
  }
}

.goods-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  height: 144rpx;

  &__title {
    overflow: hidden;
    display: -webkit-box;
    font-size: 28rpx;
    color: #323233;
    font-weight: 500;
    height: 40rpx;
    line-height: 40rpx;
    text-overflow: -o-ellipsis-lastline;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  &__sku {
    overflow: hidden;
    margin-bottom: 26rpx;
    font-size: 24rpx;
    color: #969799;
    text-overflow:ellipsis;
    white-space: nowrap;
    height: 36rpx;
    line-height: 36rpx;
  }

  &__price {
    display: flex;
    justify-content: space-between;
    align-items: center;
  
    &--stock {
      font-size: 24rpx;
      color: #969799;
    }
  }
}


.goods-package {
  width: 100%;
  box-sizing: border-box;
  height: 192rpx;
  padding-top: 24rpx;
  font-size: 24rpx;

  &__head {
    height: 36rpx;
    line-height: 36rpx;
    display: flex;
    justify-content: space-between;

    &--title {
      color: #323233;
      font-weight: 500;
    }

    &--details {
      color: #969799;

      .detail {
        display: inline-block;
        height: 36rpx;
        line-height: 36rpx;
      }

      .icon-arrow {
        font-size:14px;
        display: inline-block;
        height: 36rpx;
        line-height: 36rpx;
        vertical-align: middle;
      }
    }
  }

  &__content {
    &__bigbox {
      display: flex;

      &__wrap {
        display: flex;
        flex-direction: column;
        width: 40px;
        margin: 4px 4px 0 0;

        &--image {
          width: 40px;
          height: 40px;
          border-radius: 2px;
        }

        &--num {
          color: #969799;
          width: 40px;
          text-align: center;
          height: 18px;
          line-height: 18px;
          margin-top: 4px;
        }
      }
    }
  }
}

.cart-header {
  position: sticky;
  top: 0;
  background-color: white;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding: 10px 15px;
  box-shadow: 0 0.5px 0 0 #DCDEE0;
  z-index: 10;

  .header-title {
    text-align: center;
  }

  .header-back {
    position: absolute;
    left: 15px;
    top: 11px;
    font-size: 20px;
  }
}

.cart-body {
  max-height: calc(80vh - 95px);
  min-height: 150px;
  overflow-x: hidden;
  overflow-y: auto;
  padding-bottom: 56px;
  background-color: #f2f3f4;
  box-sizing: border-box;

  .group-bigwrap {
    background-color: #f2f3f4;
    padding-top: 14px;

    .group-wrap {
      background-color: white;
      margin: 0 12px 12px;
      border-radius: 8px;
      padding: 10px 0;

      .head {
        margin-left: 12px;

        .title {
          font-size: 14px;
          color: #323233;
          font-weight: 700;
        }

        .tips {
          font-size: 12px;
          color: #969799;
        }
      }
    }
  }
}

