import { mapMutations } from '@youzan/vanx';
import { GoodsComponent } from '@goods/common/vanx-component';
import { cloneDeep } from '../../utils';

/** 格式化金额（支持负数） */
const formatPrice = price => {
  if (!price) {
    return '';
  }
  const format = price => {
    let n = 2;
    if (price % 10 === 0) n = 1;
    if (price % 100 === 0) n = 0;
    return `¥${(Math.abs(price) / 100).toFixed(n)}`;
  };
  // 支持负数
  return price > 0 ? `${format(price)}` : `-${format(price)}`;
};

GoodsComponent({
  properties: {
    themeMainColor: String,
    info: Object,
    hideComboDetail: Boolean // 控制套餐详情的展示
  },
  data: {
    invalidImg:
      'https://img01.yzcdn.cn/upload_files/2021/04/22/Fgi0BnmsxBQdb_0vetqwoz33kstQ.png',
    songoods: [],
    combo: undefined
  },
  observers: {
    info(val) {
      // 套餐详情面板使用
      const formatGoods = cloneDeep(val);
      try {
        formatGoods?.combo?.groupList?.forEach(group => {
          group.rule = cloneDeep(JSON.parse(group.rule));
          group?.subComboList?.forEach(subCombo => {
            if (subCombo?.skuDesc) {
              const skuDesc = cloneDeep(JSON.parse(subCombo?.skuDesc));
              const skuDescriptions = skuDesc?.reduce((pre, cur) => {
                pre.push({ sn: cur.v, sp: formatPrice(subCombo?.addPrice) });
                return pre;
              }, []);
              subCombo.skuName = skuDescriptions;
            }
            if (subCombo?.properties) {
              const propDesc = subCombo?.properties
                ?.map(property =>
                  property?.propValueList?.map(propValue => ({
                    pn: propValue?.propValueName,
                    pp: formatPrice(propValue?.price)
                  }))
                )
                .flat(3);
              subCombo.propDesc = propDesc;
            }
          });
        });
      } catch (err) {
        // console.log(err);
      }

      // 套餐包括以下商品使用数据
      const songoods = formatGoods?.combo?.groupList
        ?.map(({ subComboList }) =>
          subComboList.map(({ thumbUrl, num }) => ({ thumbUrl, num }))
        )
        .flat();

      this.setYZData({
        songoods,
        combo: formatGoods?.combo
      });
      console.log(val);
    }
  },

  methods: {
    ...mapMutations(['setComboDetailShow', 'setComboDetail']),

    open() {
      console.log('this.combo', this.data.combo);
      this.setComboDetailShow(true);
      this.setComboDetail(this.data.combo);
    }
  }
});
