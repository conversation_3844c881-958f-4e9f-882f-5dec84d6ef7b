import iphonex from 'retail/util/iphonex-behavior';

Component({
  behaviors: [iphonex],
  properties: {
    priceName: {
      type: String,
      value: '应付：'
    },
    price: {
      type: Number,
      value: 0
    },
    originPrice: {
      type: Number,
      value: 0
    },
    priceTip: {
      type: String,
      value: '以实际支付金额为准'
    },
    payButtonText: {
      type: String,
      value: '结算'
    },
    disabled: Boolean,
    isPaying: Boolean
  },
  data: {},
  methods: {
    async onPay() {
      this.triggerEvent('onOrderPoolPay');
    }
  }
});
