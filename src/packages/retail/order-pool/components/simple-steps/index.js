Component({
  properties: {
    steps: {
      type: Array,
      value: [
        {
          text: '步骤一',
          inactiveText: '1',
          activeText: '1',
          activeIcon: 'checked'
        },
        {
          text: '步骤二',
          inactiveText: '2',
          // activeText: '2'
          activeIcon: 'checked'
        },
        {
          text: '步骤三',
          inactiveText: '3',
          activeIcon: 'checked'
        }
      ],
    },
    themeMainColor: String,
    active: {
      type: Number,
      value: 1
    },
    inactiveColor: {
      type: String,
      value: '#c8c9cc'
    },
    activeIcon: {
      type: String,
      value: 'checked'
    },
    inactiveIcon: String
  },
  data: {},
  methods: {
    status(index, active) {
      if (index < active) {
        return 'finish';
      }
      if (index === active) {
        return 'process';
      }

      return 'inactive';
    }
  }
});
