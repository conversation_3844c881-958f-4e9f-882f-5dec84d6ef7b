<view>
  <view class="step__wrapper">
    <view class="step" wx:for="{{ steps }}" wx:key="index" data-index="{{ index }}">
      <view class="step__circle-container">
        <block wx:if="{{ index !== active }}">
          <view class="step__text-container" wx:if="{{item.inactiveText}}">
            <view class="step__text" style="{{ 'background-color: ' + (index < active ? themeMainColor : inactiveColor) }}">
              {{ item.inactiveText }}
            </view>
          </view>
          <van-icon custom-class="step__icon" wx:if="{{ !item.inactiveText && (item.inactiveIcon || inactiveIcon) }}" color="{{ status(index, active) === 'inactive' ? inactiveColor: themeMainColor }}" name="{{ item.inactiveIcon || inactiveIcon }}" />
          <view wx:if="{{ !item.inactiveText && !item.inactiveIcon && !inactiveIcon }}" class="step__circle" style="{{ 'background-color: ' + (index < active ? themeMainColor : inactiveColor) }}" />
        </block>
        <block wx:else>
          <view class="step__text-container" wx:if="{{item.activeText}}">
            <view class="step__text" style="{{ 'background-color: ' + (themeMainColor) }}">
              {{ item.activeText }}
            </view>
          </view>
          <van-icon custom-class="step__icon" wx:if="{{ !item.activeText && (item.activeIcon || activeIcon) }}" name="{{ item.activeIcon || activeIcon }}" color="{{ themeMainColor }}" />
          <view wx:if="{{ !item.activeText && !item.activeIcon && !activeIcon }}" class="step__circle" style="{{ 'background-color: ' + (themeMainColor) }}" />
        </block>
      </view>
      <view wx:if="{{ index !== steps.length - 1 }}" class="step__line" style="{{ 'background-color: ' + (index < active ? themeMainColor : inactiveColor) }}" />
      <view class="step__title">
        <view>{{ item.text }}</view>
        <view>{{ item.desc }}</view>
      </view>
    </view>
  </view>
</view>