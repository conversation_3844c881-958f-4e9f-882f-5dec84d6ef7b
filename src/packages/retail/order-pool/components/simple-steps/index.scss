.step__line {
  position: absolute;
  // background-color: #07c160;
  transition: background-color 0.3s;
  left: 0;
  width: 100%;
  height: 2px;
  margin-top: -1px;
}

.step__icon {
  display: block;
  width: 20px;
  height: 20px;
  overflow: hidden;
  margin: 0 -1px; // 解决icon边缘有空隙
  font-size: 20px !important;
  transition: color 0.3s;
}

.step__circle {
  display: block;
  width: 5px;
  height: 5px;
  background-color: #969799;
  border-radius: 50%;
}

.step__text-container {
  width: 18px;
  height: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.step__text {
  color: #ffffff;
  width: 18px;
  height: 18px;
  border-radius: 10px;
  text-align: center;
}

.step__circle-container {
  position: absolute;
  z-index: 1;
  display: flex;
  background-color: #fff;
  transform: translate(-50%, -50%);
}

.step__title {
  display: inline-block;
  transform: translateX(-50%);
  margin-top: 16px;
  font-size: 14px;
  color: #323233;
}

.step--horizontal:first-child .step__title {
  margin-left: 0;
  transform: none;
}

.step__wrapper {
  position: relative;
  display: flex;
  background: #ffffff;
  padding: 16px 0;
  width: 100%;
  overflow: hidden;
}

.step {
  position: relative;
  flex: 1;
  color: #969799;
  font-size: 14px;
  transform: translateX(50%);
  margin-top: 10px;
}