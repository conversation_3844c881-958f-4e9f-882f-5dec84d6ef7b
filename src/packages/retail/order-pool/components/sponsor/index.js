import Dialog from '@vant/weapp/dist/dialog/dialog';
import orderPoolGlobal from 'retail/util/order-pool';
import { isSingleShop } from 'retail/util/shelf-shop-util';
import {
  ORDER_POOL_STATUS,
  PARTICIPANT_STATUS,
  URLS,
  ORDER_WAY
} from '../../constants';
import optionsBehavior from '../../behaviors/options-behaviors';
import api from '../../api';

const app = getApp();

Component({
  behaviors: [optionsBehavior],
  properties: {
    themeMainColor: String,
    themeMainRgbColorWithAlpha10: String,
    participantList: Array,
    completedNum: Number,
    joinedNum: Number,
    totalOriginPrice: Number,
    totalPayPrice: Number,
    status: Number,
    type: Number,
    jointId: Number,
    startPrice: Number,
    goodsNum: Number
  },
  observers: {
    status(status) {
      // 根据拼单状态的跳转
      if (status === ORDER_POOL_STATUS.CANCEL) {
        Dialog.alert({
          message: '好友已取消本次拼单',
          confirmButtonText: '我知道了',
          context: this,
          zIndex: 999
        }).then(() => {
          wx.reLaunch({
            url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=0`)
          });
        });
      } else if (status === ORDER_POOL_STATUS.CLOSED) {
        Dialog.alert({
          message: '拼单已关闭',
          confirmButtonText: '我知道了',
          context: this,
          zIndex: 999
        }).then(() => {
          wx.reLaunch({
            url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=0`)
          });
        });
      } else if (status === ORDER_POOL_STATUS.SUCCESS) {
        wx.reLaunch({
          url: app.genOrderPullJumpUrl(`${
            URLS.ORDER_POOL_DETAIL
          }?orderPoolId=${orderPoolGlobal.getCurrentId()}`)
        });
      }
    },
    participantList(participantList) {
      // 计算chosenTotal，凑单组件使用。凑单组件本期已经注释掉
      const chosenTotal = participantList.reduce((cur, item) => {
        const { goodsInfoList = [] } = item;
        goodsInfoList.forEach(goodsInfo => {
          if (cur[goodsInfo.goodsId]) {
            cur[goodsInfo.goodsId].num += 1;
          } else {
            cur[goodsInfo.goodsId] = {
              num: 1,
              noneSku: !goodsInfo.skuId
            };
          }
        });
        return cur;
      }, {});
      this.setData({
        chosenTotal
      });
    }
  },
  data: {
    buyerId: +app.getBuyerId() || -1,
    ORDER_POOL_STATUS,
    chosenTotal: {},
    isLink: false
  },
  lifetimes: {
    attached() {
      let isLink = false;
      const mode = orderPoolGlobal.getCurrentMode();
      if (!isSingleShop() && mode === ORDER_WAY.SELF_TAKE) {
        // 目前拼单页只支持连锁店铺自提模式切换店铺
        isLink = true;
      }
      this.setData({
        buyerId: +app.getBuyerId(),
        isLink
      });
    }
  },
  methods: {
    alertDialog(error, defaultMsg = '') {
      Dialog.alert({
        message: error?.msg || defaultMsg,
        confirmButtonText: '我知道了',
        context: this,
        zIndex: 999
      });
    },
    async onOrderCommon() {
      try {
        await this.onChangeGoods(orderPoolGlobal.getCurrentId());
        this.onOrder();
      } catch (error) {
        this.alertDialog(error, '');
      }
    },
    async onOrderImmediate() {
      try {
        await this.onChangeGoods(orderPoolGlobal.getCurrentId());
        this.onOrder();
      } catch (error) {
        this.alertDialog(error, '');
      }
    },
    async onCancelOrderPool(data) {
      try {
        await api.cancelOrderPool({
          jointId: orderPoolGlobal.getCurrentId() || this.properties.jointId,
          reason: data.detail
        });
        wx.reLaunch({
          url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=0`)
        });
      } catch (error) {
        this.alertDialog(error, '取消拼单失败');
      }
    },
    async clearGoods() {
      await Dialog.confirm({
        message: '清空后不可恢复，确定清空吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        context: this,
        zIndex: 999
      });
      try {
        await this.onClearGoods(orderPoolGlobal.getCurrentId());
      } catch (error) {
        this.alertDialog(error, '清空失败');
      }
    },
    async syncGoods(event) {
      try {
        await this.onSyncGoodsWithSelectedBuyer({
          fromBuyerId: event.target.dataset.buyerId,
          jointId: orderPoolGlobal.getCurrentId(),
          clientId: app.config.clientId
        });
      } catch (error) {
        this.alertDialog(error, '和他点一样失败');
      }
    },
    async onOrderPoolPay() {
      const result = this.countAllParticipantStatus();
      await Dialog.confirm({
        message:
          result.addingCount > 0
            ? '你有好友正在选购中，结算后无法修改商品'
            : '结算后其他好友无法再加入或修改商品',
        title: '是否确认结算？',
        confirmButtonText: '确认结算',
        cancelButtonText: '取消',
        context: this,
        zIndex: 999
      });
      try {
        await api.lockOrderPool({
          jointId: orderPoolGlobal.getCurrentId(),
          clientId: app.config.clientId
        });
        this.triggerEvent('pay');
      } catch (error) {
        this.alertDialog(error, '结算失败');
      }
    },
    onPay() {
      this.triggerEvent('pay');
    },
    countAllParticipantStatus() {
      // 统计参与人的状态
      const { participantList } = this.properties;
      const participantStatus = {
        newCount: 0,
        addingCount: 0,
        addedCount: 0
      };
      participantList.forEach(item => {
        if (item.status === PARTICIPANT_STATUS.PRODUCT_ADDED) {
          participantStatus.addedCount += 1;
        }
        if (item.status === PARTICIPANT_STATUS.PRODUCT_ADDING) {
          participantStatus.addingCount += 1;
        }
        if (item.status === PARTICIPANT_STATUS.NEW) {
          participantStatus.newCount += 1;
        }
      });
      return participantStatus;
    }
  }
});
