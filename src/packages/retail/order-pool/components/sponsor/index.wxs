const sponsorPanelInfo = function(joinedNum, completedNum) {
  return {
    total: joinedNum,
    done: completedNum
  };
};

const greaterThenStartPrice = function(startPrice, totalPrice) {
  return +startPrice <= +totalPrice;
};

const hasProduct = function(participantList) {
  return participantList.some(participant => {
    return participant.goodsInfoList.length > 0;
  });
};

const payButtonDisabled = function({
  startPrice,
  totalPrice,
  participantList
}) {
  return (
    !greaterThenStartPrice(startPrice, totalPrice) ||
    !hasProduct(participantList)
  );
};

const payButtonText = function(startPrice, totalPrice) {
  const stPrice = (startPrice / 100).toFixed(2);
  if (greaterThenStartPrice(startPrice, totalPrice)) {
    return '结算';
  }

  if (+totalPrice === 0) {
    return '满' + stPrice + '元起送';
  }

  if (+totalPrice > 0) {
    const price = ((startPrice - totalPrice) / 100).toFixed(2);
    return '还差' + price + '元起送';
  }

  return '结算';
};

function toPrice(price = 0) {
  price /= 100;
  const priceText = price.toFixed(2);
  return '¥' + priceText;
}

module.exports = {
  sponsorPanelInfo,
  greaterThenStartPrice,
  payButtonText,
  toPrice,
  hasProduct,
  payButtonDisabled
};
