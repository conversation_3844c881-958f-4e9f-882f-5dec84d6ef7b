.goods-options {
  display: flex;
  height: 80rpx;
  flex-direction: row-reverse;
  align-items: center;
}

.footer-btn {
  height: 24px;
  line-height: 24px;
  border-radius: 14px;
  background-color: #ffffff;
  font-size: 12px;
  text-align: center;

  &::after {
    border: none;
  }
}

.sync-goods-btn {
  border: solid 1px #dcdee0;
  color: #323233;
  margin: 0 16px 0 0;
}

.on-order-btn {
  border: solid 1px var(--theme-main-color);
  color: var(--theme-main-color);
  margin: 0 16px 0 0;
  padding: 0 8px;
}

.clear-goods-btn {
  border: solid 1px #dcdee0;
  color: #323233;
  margin: 0 8px 0 0;
  width: 64px;
}

.check-more {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 18px;
  font-size: 14px;
  color: #969799;
  text-align: center;
  margin-bottom: 24px;
}

.order-pool-detail {
  &__cell {
    height: 44px;
    line-height: 44px;
    padding: 0 12px;
    background-color: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #323233;
    border-radius: 8px;

    &__bold {
      font-weight: 900;
    }
  }
}