<view style="padding-top: 24rpx">
  <business-hours-notice-bar />
  <panel>
    <address-panel is-link="{{isLink}}" />
  </panel>
  <panel>
    <sponsor-panel info="{{tools.sponsorPanelInfo(joinedNum, completedNum)}}" themeMainColor="{{themeMainColor}}" bind:sponsorCancel="onCancelOrderPool"></sponsor-panel>
  </panel>
  <view wx:for="{{participantList}}" wx:for-item="participant" wx:key="buyerId">
    <panel>
      <goods-card modeType="sponsor" isSponsor="{{buyerId == participant.buyerId}}" participant="{{participant}}" bind:onOrderImmediate="onOrderImmediate">
        <!-- <discount-bar
          wx:if="{{buyerId == participant.buyerId}}"
          slot="header"
          themeMainRgbColorWithAlpha10="{{themeMainRgbColorWithAlpha10}}"
          themeMainColor="{{themeMainColor}}"
          discountTag="满减"
          discountContent="还差 10 元，立享【满100减10】送还差 10 元，立享【满100减10】送"
        ></discount-bar> -->
        <view wx:for="{{participant.goodsInfoList}}" wx:for-item="goodInfo" wx:key="index" slot="content">
          <goods-item info="{{goodInfo}}"></goods-item>
        </view>
        <view slot="footer" wx:if="{{participant.goodsInfoList}}">
          <view wx:if="{{ORDER_POOL_STATUS.INIT === status && buyerId == participant.buyerId && participant.goodsInfoList.length !== 0}}" class="goods-options">
            <button class="footer-btn on-order-btn" bindtap="onOrderCommon">修改商品</button><button class="footer-btn clear-goods-btn" bindtap="clearGoods">清空</button>
          </view>
          <view wx:if="{{ORDER_POOL_STATUS.INIT === status && buyerId != participant.buyerId && participant.goodsInfoList.length !== 0}}" class="goods-options">
            <button class="footer-btn sync-goods-btn" bindtap="syncGoods" data-buyer-Id="{{participant.buyerId}}">和他点一样</button>
          </view>
        </view>
      </goods-card>
    </panel>
  </view>
  <sponsor-pay-submit
    price="{{totalPayPrice}}"
    origin-price="{{totalOriginPrice}}"
    disabled="{{tools.payButtonDisabled({startPrice, totalPrice: totalPayPrice, participantList})}}"
    bind:onOrderPoolPay="onOrderPoolPay"
    payButtonText="{{tools.payButtonText(startPrice, totalPayPrice)}}"
  ></sponsor-pay-submit>
</view>
<van-dialog id="van-dialog" />

<wxs src="./index.wxs" module="tools" />