Component({
  properties: {
    info: {
      type: Object,
      observer(info) {
        this.setData({
          status: this.properties.infoRender(info)
        });
      },
      value: {
        total: 0,
        done: 0
      }
    },
    infoRender: {
      type: Function,
      value: data => {
        const { total = 0, done = 0 } = data;
        if (total === 0 && done === 0) {
          return '当前没有好友拼单，快去邀请吧！';
        }
        return `${total}人参与拼单, ${done}人已选购完成`;
      }
    },
    share: {
      type: Object,
      value: {
        disabled: false,
        text: '邀请好友'
      }
    },
    cancel: {
      type: Object,
      value: {
        disabled: false,
        text: '取消拼单'
      }
    },
    themeMainColor: String
  },
  data: {
    status: '',
    cancelPopupVisible: false,
    reason: 1,
    cancelReasonList: [
      { value: 1, name: '暂时不需要拼单了' },
      { value: 2, name: '商品/门店选错了，重新选' },
      { value: 3, name: '随便看看' },
      { value: 4, name: '其他' }
    ]
  },
  lifetimes: {
    attached() {
      this.setData({
        status: this.data.infoRender(this.data.info)
      });
    }
  },
  methods: {
    onShare() {
      this.triggerEvent('sponsorShare');
    },
    onCancel() {
      this.triggerEvent('sponsorCancel', this.data.reason);
    },
    onClosePopup() {
      this.setData({
        cancelPopupVisible: false
      });
    },
    onRadio(event) {
      this.setData({
        reason: event.detail
      });
    },
    onClick(event) {
      const { name } = event.currentTarget.dataset;
      this.setData({
        reason: name
      });
    },
    onShowPopUp() {
      this.setData({
        cancelPopupVisible: true
      });
    }
  }
});
