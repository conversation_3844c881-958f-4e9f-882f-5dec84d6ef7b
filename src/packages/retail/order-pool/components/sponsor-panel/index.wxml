<view class="sponsor-panel">
  <view class="sponsor-panel__status">{{ status }}</view>
  <view class="sponsor-panel__action">
    <button class="sponsor-panel__button" disabled="{{cancel.disabled}}" bind:tap="onShowPopUp">
      {{ cancel.text }}
    </button>
    <button class="sponsor-panel__primary-button" disabled="{{share.disabled}}" open-type="share" bind:tap="onShare">
      {{ share.text }}
    </button>
  </view>
  <van-popup
    show="{{ cancelPopupVisible }}"
    round
    position="bottom"
    bind:close="onClosePopup"
  >
    <view class="sponsor-panel__popup">
      <view class="sponsor-panel__popup__title">取消拼单</view>
      <view class="sponsor-panel__popup__desc">选择取消原因，帮助我们持续改进</view>
      <view>
        <van-radio-group value="{{ reason }}" bind:change="onRadio">
          <van-cell-group border="{{ false }}">
            <van-cell
              custom-class="sponsor-panel__popup__cell"
              title-class="sponsor-panel__popup__cell__title"
              wx:for="{{cancelReasonList}}"
              wx:key="value"
              title="{{item.name}}"
              clickable
              data-name="{{item.value}}"
              bind:click="onClick"
            >
              <van-radio
                slot="icon"
                checked-color="{{themeMainColor}}"
                name="{{item.value}}"
                icon-size="18px"
              >
                <!-- <view slot="icon" class="{{reason === item.value ? 'sponsor-panel__popup__cell__icon sponsor-panel__popup__cell__icon--checked' : 'sponsor-panel__popup__cell__icon'}}" /> -->
              </van-radio>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
        <view class="sponsor-panel__popup__bottom">
          <button bind:tap="onClosePopup" class="sponsor-panel__button sponsor-panel__popup__button">
            暂不取消
          </button>
          <button bind:tap="onCancel" class="sponsor-panel__primary-button sponsor-panel__popup__button">
            确认取消
          </button>
        </view>
      </view>
    </view>
  </van-popup>
</view>