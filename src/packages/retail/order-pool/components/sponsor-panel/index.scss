.sponsor-panel {
  height: 100px;
  padding: 12px 0;
  border-radius: 8px;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;

  &__status {
    margin-bottom: 16px;
  }

  &__action {
    display: flex;
  }

  &__button {
    margin-right: 32px;
    width: 108px;
    height: 40px;
    border-radius: 20px;
    text-align: center;
    line-height: 40px;
    color: #323233;
    font-size: 14px;
    border: solid 1px #dcdee0;
    background-color: #ffffff;

    &::after {
      border: none;
    }
  }
  
  &__primary-button {
    background-color: var(--theme-main-color);
    width: 108px;
    height: 40px;
    border-radius: 20px;
    text-align: center;
    line-height: 40px;
    color: #ffffff;
    font-size: 14px;

    &::after {
      border: none;
    }
  }

  &__popup {

    &__cell {
      align-items: center;
      height: 44px;
      line-height: 44px;

      &__title {
        margin-left: 9px;
        font-size: 14px;
      }

      &__icon {
        width: 16px;
        height: 16px;
        border: solid 1px #dcdee0;
        background-color: #ffffff;
        border-radius: 8px;
        box-sizing: border-box;

        &--checked {
          padding: 4px;
          border: solid 1px var(--theme-main-color);;
          background: var(--theme-main-color);
        }
      }
    }

    &__title {
      height: 44px;
      line-height: 44px;
      background-color: #ffffff;
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      color: #323233;
    }

    &__desc {
      font-size: 12px;
      color: #969799;
      margin: 8px 16px;
    }

    &__bottom {
      display: flex;
      margin: 42px 16px 5px;
    }

    &__button {
      width: 165px;
      margin-right: 0;
      
      &::after {
        border: none;
      }
    }
  }
}


