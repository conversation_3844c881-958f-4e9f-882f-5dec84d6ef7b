import Dialog from '@vant/weapp/dist/dialog/dialog';
import orderPoolGlobal from 'retail/util/order-pool';
import { ORDER_POOL_STATUS, URLS } from '../../constants';
import optionsBehavior from '../../behaviors/options-behaviors';

const app = getApp();

Component({
  behaviors: [optionsBehavior],
  properties: {
    themeMainColor: String,
    themeMainRgbColorWithAlpha10: String,
    participantList: Array,
    sponsorNickname: String,
    totalPayPrice: Number,
    status: Number,
    type: Number,
    jointId: Number,
    goodsNum: Number,
    promoterBuyId: Number
  },
  observers: {
    status(status) {
      // 根据拼单status设置按钮状态
      if (status !== ORDER_POOL_STATUS.INIT) {
        this.setData({
          allBtnDiasbled: true
        });
      } else {
        this.setData({
          allBtnDiasbled: false
        });
      }
      // 拼单取消跳转
      if (status === ORDER_POOL_STATUS.CANCEL) {
        Dialog.alert({
          message: '好友已取消本次拼单',
          confirmButtonText: '我知道了',
          context: this,
          zIndex: 999
        }).then(() => {
          wx.reLaunch({
            url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=0`)
          });
        });
      } else if (status === ORDER_POOL_STATUS.CLOSED) {
        Dialog.alert({
          message: '拼单已关闭',
          confirmButtonText: '我知道了',
          context: this,
          zIndex: 999
        }).then(() => {
          wx.reLaunch({
            url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=0`)
          });
        });
      } else if (status === ORDER_POOL_STATUS.SUCCESS) {
        wx.reLaunch({
          url: app.genOrderPullJumpUrl(`${
            URLS.ORDER_POOL_DETAIL
          }?orderPoolId=${orderPoolGlobal.getCurrentId()}`)
        });
      }
    }
  },
  data: {
    ORDER_POOL_STATUS,
    buyerId: +app.getBuyerId() || -1,
    orderBtn: {
      disabled: false,
      text: '立即点单'
    },
    allBtnDiasbled: false,
    orderPoolDetailShowAll: false
  },
  lifetimes: {
    attached() {
      this.setData({
        buyerId: +app.getBuyerId()
      });
    }
  },
  methods: {
    checkMore() {
      this.setData({
        orderPoolDetailShowAll: true
      });
    },
    alertDialog(error, defaultMsg = '') {
      Dialog.alert({
        message: error?.msg || defaultMsg,
        confirmButtonText: '我知道了',
        context: this,
        zIndex: 999
      });
    },
    async onOrderCommon() {
      try {
        const { status } = this.properties;
        if (this.checkOrderAble(status)) {
          await this.onChangeGoods(orderPoolGlobal.getCurrentId());
          this.onOrder();
        }
      } catch (error) {
        this.alertDialog(error, '');
      }
    },
    async onOrderImmediate() {
      try {
        const { status } = this.properties;
        if (this.checkOrderAble(status)) {
          await this.onChangeGoods(orderPoolGlobal.getCurrentId());
          this.onOrder();
        }
      } catch (error) {
        this.alertDialog(error, '');
      }
    },
    async clearGoods() {
      await Dialog.confirm({
        message: '清空后不可恢复，确定清空吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        context: this,
        zIndex: 999
      });
      try {
        await this.onClearGoods(orderPoolGlobal.getCurrentId());
      } catch (error) {
        this.alertDialog(error, '清空失败');
      }
    },
    async syncGoods(event) {
      try {
        await this.onSyncGoodsWithSelectedBuyer({
          fromBuyerId: event.target.dataset.buyerId,
          jointId: orderPoolGlobal.getCurrentId(),
          clientId: app.config.clientId
        });
      } catch (error) {
        this.alertDialog(error, '和他点一样失败');
      }
    },
    checkOrderAble(status) {
      if (status === ORDER_POOL_STATUS.INIT) {
        return true;
      }
      Dialog.confirm({
        message: '发起人已提交订单，无法继续加购',
        confirmButtonText: '我知道了',
        context: this,
        showCancelButton: false,
        zIndex: 999
      });
      return false;
    }
  }
});
