<business-hours-notice-bar />
<view wx:if="{{tools.calcActiveStep(status, participantList, buyerId) !== -1}}">
  <simple-steps steps="{{tools.calcSteps(status)}}" active="{{tools.calcActiveStep(status, participantList, buyerId)}}" themeMainColor="{{themeMainColor}}"></simple-steps>
</view>
<view style="padding-top: 24rpx">
  <view>
    <panel>
      <address-panel is-Link />
    </panel>
    <panel>
      <participant-panel wx:if="{{tools.calcActiveStep(status, participantList, buyerId) === -1}}" text="{{sponsorNickname + '喊你一起拼单啦~'}}" order="{{orderBtn}}" bind:participantHandleOrder="onOrderImmediate" themeMainColor="{{themeMainColor}}"></participant-panel>
    </panel>
    <view wx:for="{{participantList}}" wx:for-item="participant" wx:key="buyerId">
      <panel>
        <goods-card modeType="participant" isSponsor="{{promoterBuyId == participant.buyerId}}" isParticipant="{{buyerId == participant.buyerId}}" participant="{{participant}}">
          <view wx:for="{{participant.goodsInfoList}}" wx:for-item="goodInfo" wx:key="index" slot="content">
            <goods-item info="{{goodInfo}}"></goods-item>
          </view>
          <view wx:if="{{!allBtnDiasbled && participant.goodsInfoList}}" slot="footer">
            <view wx:if="{{ORDER_POOL_STATUS.INIT === status && buyerId == participant.buyerId && participant.goodsInfoList.length !== 0}}" class="goods-options">
              <button class="footer-btn on-order-btn" bindtap="onOrderCommon">修改商品</button><button class="footer-btn clear-goods-btn" bindtap="clearGoods">清空</button>
            </view>
            <view wx:if="{{ORDER_POOL_STATUS.INIT === status && buyerId != participant.buyerId && participant.goodsInfoList.length !== 0}}" class="goods-options">
              <button class="footer-btn sync-goods-btn" bindtap="syncGoods" data-buyer-Id="{{participant.buyerId}}">和他点一样</button>
            </view>
          </view>
        </goods-card>
      </panel>
    </view>
    <participant-price-bar price="{{totalPayPrice}}"></participant-price-bar>
  </view>
</view>
<van-dialog id="van-dialog" />

<wxs src="./index.wxs" module="tools" />