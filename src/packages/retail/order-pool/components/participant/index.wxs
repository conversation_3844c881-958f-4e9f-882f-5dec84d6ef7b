const ORDER_POOL_STATUS = {
  INIT: 0, // 初始化
  LOCKED: 1, // 锁定
  CREATE: 2, // 创建
  CLOSED: 3, // 拼单关闭
  CANCEL: 99, // 取消
  SUCCESS: 100 // 成功
};

const PARTICIPANT_STATUS = {
  NEW: 0, // 未选购
  PRODUCT_ADDING: 1, // 选购中
  PRODUCT_ADDED: 2 // 选购完成
};

const calcActiveStep = function(status, participantList, buyerId) {
  let myParticipantInfo = {};
  participantList.some(item => {
    if (+item.buyerId === +buyerId) {
      myParticipantInfo = item;
      return true;
    }
    return false;
  });
  if (
    status === ORDER_POOL_STATUS.INIT &&
    myParticipantInfo.status === PARTICIPANT_STATUS.PRODUCT_ADDED
  ) {
    return 0;
  }
  if (
    status === ORDER_POOL_STATUS.CREATE ||
    status === ORDER_POOL_STATUS.LOCKED
  ) {
    return 1;
  }
  if (status === ORDER_POOL_STATUS.SUCCESS) {
    return 2;
  }
  return -1;
};

const calcSteps = function(status) {
  if (status === ORDER_POOL_STATUS.INIT) {
    // steps = 1
    return [
      {
        text: '完成选品',
        activeIcon: 'checked'
      },
      {
        text: '提交订单',
        inactiveText: '2'
      },
      {
        text: '完成订单',
        inactiveText: '3'
      }
    ];
  }
  if (status === ORDER_POOL_STATUS.LOCKED) {
    // steps = 2
    return [
      {
        text: '完成选品',
        inactiveIcon: 'checked'
      },
      {
        text: '提交订单',
        activeText: '2'
      },
      {
        text: '完成订单',
        inactiveText: '3'
      }
    ];
  }
  if (status === ORDER_POOL_STATUS.CREATE) {
    // steps = 2
    return [
      {
        text: '完成选品',
        inactiveIcon: 'checked'
      },
      {
        text: '提交订单',
        activeIcon: 'checked'
      },
      {
        text: '完成订单',
        inactiveText: '3'
      }
    ];
  }
  if (status === ORDER_POOL_STATUS.SUCCESS) {
    // steps = 3
    return [
      {
        text: '完成选品',
        inactiveIcon: 'checked'
      },
      {
        text: '提交订单',
        inactiveIcon: 'checked'
      },
      {
        text: '完成订单',
        activeIcon: 'checked'
      }
    ];
  }
  return [
    {
      text: '完成选品',
      activeIcon: 'checked',
      inactiveText: '1'
    },
    {
      text: '提交订单',
      activeIcon: 'checked',
      inactiveText: '2'
    },
    {
      text: '完成订单',
      activeIcon: 'checked',
      inactiveText: '3'
    }
  ];
};

function toPrice(price = 0) {
  price /= 100;
  const priceText = price.toFixed(2);
  return '¥' + priceText;
}

module.exports = { calcActiveStep, toPrice, calcSteps };
