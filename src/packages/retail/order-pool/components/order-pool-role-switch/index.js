import Dialog from '@vant/weapp/dist/dialog/dialog';
import orderPoolGlobal from 'retail/util/order-pool';
import orderPoolWSBehavior from '../../behaviors/order-pool-ws-behavior';
import {
  ORDER_WAY,
  PARTICIPANT_STATUS,
  URLS,
  ORDER_POOL_STATUS
} from '../../constants';
import api from '../../api';
import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';

const app = getApp();

Component({
  behaviors: [orderPoolWSBehavior],
  properties: {
    themeMainColor: String,
    themeMainRgbColorWithAlpha10: String,
    sponsorNickname: String,
    customStyle: {
      type: String,
      value: ''
    }
  },
  data: {
    buyerId: +app.getBuyerId() || 0,
    startPrice: 0
  },
  options: {
    pureDataPattern: /^_/
  },
  lifetimes: {
    attached() {
      this._initFinish = false; // 第一次初始化走attached
      this.init();
    },
    detached() {
      this.uninit();
    }
  },
  pageLifetimes: {
    async show() {
      if (this._initFinish) {
        this.init();
      }
    },
    hide() {
      this.uninit();
    }
  },
  methods: {
    async init() {
      try {
        // https://jira.qima-inc.com/browse/CSWT-82797?filter=-1
        this.setData({
          promoterBuyId: -1
        });
        const { jointId, kdtId } = await this.joinOrderPool();
        if (!jointId) return;
        orderPoolGlobal.setCurrentId(+jointId);
        const detail = await this.getOrderPoolDetail(jointId, kdtId);
        const { participants = [] } = detail;
        if (participants?.length) {
          // 如果在点单页直接杀掉小程序，没有点选好了
          // 之后通过分享进拼单页，那么用户状态还是选购中
          // 直接原路跳回点单页
          const idx = participants.findIndex(
            ({ buyerId }) => +buyerId === +app.getBuyerId()
          );
          if (participants[idx]?.status === PARTICIPANT_STATUS.PRODUCT_ADDING) {
            this.onOrder();
            return;
          }
        }
        await this.startUpOrderPoolWS({
          jointId: orderPoolGlobal.getCurrentId(),
          orderPoolDetail: detail
        });
        this.setData({
          buyerId: +app.getBuyerId(),
          startPrice: detail.startPrice
        });
        this._initFinish = true;
      } catch (error) {
        // console.log(error);
        orderPoolGlobal.clear();
        this.closeOrderPoolWS();
        Dialog.confirm({
          customStyle: 'border-radius: 32rpx',
          message: error?.msg || '拼单找不到了，请稍后再试',
          confirmButtonText: '我知道了',
          showCancelButton: false,
          context: this,
          zIndex: 10000
        }).then(() => {
          this.onJumpOrder();
        });
      }
    },

    async joinOrderPool() {
      const cacheOrderPoolId = orderPoolGlobal.getCurrentId();
      if (cacheOrderPoolId) {
        // 用户加入同一笔拼单永远会返回这笔拼单的id，故通过加单来判断用户是否存在拼单，并获取kdtId
        const { status } = await api.joinOrderPool({
          jointId: cacheOrderPoolId
        });
        if (status === ORDER_POOL_STATUS.SUCCESS) {
          // 拼单已完成，跳转到拼单详情页
          wx.reLaunch({
            url: app.genOrderPullJumpUrl(`${URLS.ORDER_POOL_DETAIL}?orderPoolId=${cacheOrderPoolId}`)
          });
          return {};
        }
        if (status === ORDER_POOL_STATUS.CANCEL) {
          // 拼单已取消，抛异常
          return Promise.reject({
            msg: '好友已取消本次拼单'
          });
        }
      }
      // 现在一定要查当前rootKdt下的拼单信息
      const result = await api.checkActiveOrderPool({});
      const { status, jointId, ownerBuyerId, kdtId } = result;
      if (!jointId) {
        // 如果查不到进行中的拼单
        // 场景：拼单发起人下单后点左上角返回按钮回到了拼单页
        this.onJumpOrder();
        return {};
      }
      if (app.getKdtId() !== kdtId) {
        const { query = {} } = app.getWxPage();
        await autoEnterShop({
          logTag: 'retail_order_pool',
          alias: query.alias || '',
          chainBizType: 'retail_shelf',
          shopAutoEnter: 2
        }, kdtId)
      }
      if (
        +ownerBuyerId === +app.getBuyerId() &&
        status === ORDER_POOL_STATUS.LOCKED
      ) {
        // 当前用户是拼单发起人且这笔拼单已锁定，就要解锁
        // 场景：发起人在拼单确认页未下单，通过其他方式进入拼单页
        await api.lockOrderPool({
          jointId,
          opType: 1
        });
      }
      return result;
    },
    async getOrderPoolDetail(jointId, kdtId) {
      // 查店铺和拼单详情
      const {
        deliveryType,
        addressId,
        promoterBuyId,
        ...args
      } = await Promise.all([
        api.getShopInfo({}),
        api.getOrderPoolDetail({ jointId })
      ]).then(([shop, detail]) => {
        orderPoolGlobal.setCurrentStore({
          ...shop,
          kdtId
        });
        const { participants = [] } = detail;
        participants.forEach((participant) => {
          const { goodsInfoList = [] } = participant;
          goodsInfoList.forEach((goodsInfo) => {
            const { skuName = '', propertyName = '' } = goodsInfo;
            const texts = [...skuName.split(' '), ...propertyName.split(' ')];
            goodsInfo.showText = texts.filter(Boolean).join('、')
          });
        });
        return detail;
      });
      let startPrice = 0;
      // deliveryType 1 外送 2 自提
      if (deliveryType === 1) {
        // 外卖需要额外查地址信息
        const address = await api.getUserAddress({
          userId: promoterBuyId,
          addressId
        });
        if (!address || !address[0]) {
          // 未获取到发起人地址
          if (+promoterBuyId === +app.getBuyerId()) {
            // 当前用户是发起人，直接关闭
            // 场景：发起人创建了一笔外卖拼单，然后把创建时选的地址删了，后续查询不到地址信息
            await api.cancelOrderPool({ jointId });
            return Promise.reject({
              msg: '外卖地址查询异常，已自动关闭'
            });
          }
          return Promise.reject({
            msg: '外卖地址查询异常，请稍后重试'
          });
        }
        // 当前用户是拼单发起人，派仓
        if (+promoterBuyId === +app.getBuyerId()) {
          await Promise.all([
            api.getOnlineSupplyMode(kdtId),
            api.getWarehouse({ kdtId })
          ])
            .then(([supplyMode, warehouseInfo]) => {
              // 主要获取一个起送价
              startPrice = warehouseInfo.startPrice;
              // 0：供货，供货模式店铺才需要真正的派仓
              supplyMode === 0 && orderPoolGlobal.setWarehouse(warehouseInfo);
            })
            .catch(() => {
              // 抛异常先不做处理
            });
        }
        orderPoolGlobal.setCurrentMode(ORDER_WAY.DELIVERY);
        orderPoolGlobal.setCurrentAddress(address[0]);
      } else {
        orderPoolGlobal.setCurrentMode(ORDER_WAY.SELF_TAKE);
      }
      return {
        promoterBuyId,
        startPrice,
        ...args
      };
    },
    async uninit() {
      this.closeOrderPoolWS();
    },
    // 以自提跳转点单页
    onJumpOrder() {
      wx.reLaunch({
        url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=0`)
      });
    },
    // 去点单页点单
    onOrder() {
      const mode = orderPoolGlobal.getCurrentMode();
      app.globalData.shelfParams = {
        scene: 1,
        isOrder: 1
      };
      wx.reLaunch({
        url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=${mode}&scene=1&isOrder=1`)
      });
    },
    // 去订单确认页
    onPay() {
      let selfFetch = null;
      let deliveryAddress = null;
      const mode = orderPoolGlobal.getCurrentMode();
      const shop = orderPoolGlobal.getCurrentStore();
      const address = orderPoolGlobal.getCurrentAddress();
      if (mode === ORDER_WAY.DELIVERY) {
        deliveryAddress = {
          addressId: address.id,
          ...address
        };
      } else {
        if (!shop) return;
        const { shopName: name, kdtId } = shop;
        selfFetch = {
          ...shop,
          kdtId,
          name
        };
      }


      const warehouseId = orderPoolGlobal.getWarehouse()?.id ?? '';
      api
        .getBookKey({
          goodsList: this.setGoodsList(),
          selfFetch,
          deliveryAddress,
          warehouseId
        })
        .then(async res => {
          let pageUrl = `${URLS.ORDER}?bookKey=${res.bookKey
            }&retailOrderScene=24hshelf&jointId=${orderPoolGlobal.getCurrentId()}`;

          if (mode === ORDER_WAY.DELIVERY) {
            if (address) {
              pageUrl += `&addressId=${address.id}`;
            }
            if (warehouseId) {
              pageUrl += `&warehouseId=${warehouseId}`;
            }
          }

          // 不知道干嘛的，先注释
          // if (!isSingleShop()) {
          //   const shopInfo = app.getShopInfoSync();
          //   await shelfUtil.initGlobalThemeType();
          //   shopInfo.theme = {
          //     type: shelfUtil.getHqShopData()?.themeType,
          //     kdt_id: app.getHQKdtId()
          //   };
          //   app.setShopInfo(shopInfo);
          // }

          orderPoolGlobal.clear(); // 下单前清空缓存数据
          wx.navigateTo({
            url: pageUrl
          });
        })
        .catch(err => {
          // console.error(err);
          wx.showToast({
            icon: 'none',
            title: err?.msg ?? '创建订单失败，请稍后再试'
          });
        });
    },
    // 将所有人的商品按照goodsId和skuId和propIds合并
    setGoodsList() {
      const participants = this.getParticipants();
      const goodsList = [];
      const idxMap = {}; // 存放（按照goodsId和skuId归并的）商品在goodsList中的下标
      const kdtId = app.getKdtId();
      participants.forEach(({ goodsInfoList = [] }) => {
        goodsInfoList.forEach(
          ({
            goodsId,
            skuId,
            num,
            message: itemMessage,
            propertyIds,
            ...args
          }) => {
            if (idxMap[goodsId] && idxMap[goodsId][skuId]) {
              // 首先商品和规格一样才需要去判断属性
              const temp = idxMap[goodsId][skuId];
              const currentHasProps = Array.isArray(propertyIds);
              if (typeof temp === 'number' && !currentHasProps) {
                // 无属性商品直接用商品id+规格id判断是否一样
                goodsList[temp].num += num;
                itemMessage && (goodsList[temp].itemMessage = itemMessage);
                return;
              }
              if (typeof temp === 'object' && currentHasProps) {
                // 多属性商品要比较下属性是否一直
                propertyIds.sort((a, b) => a - b);
                const currentTempPropId = propertyIds.join('');
                const tempIdx = temp[currentTempPropId];
                if (tempIdx) {
                  goodsList[tempIdx].num += num;
                  itemMessage && (goodsList[tempIdx].itemMessage = itemMessage);
                  return;
                }
              }
            }
            goodsList.push({
              ...args,
              goodsId,
              skuId,
              kdtId,
              num,
              itemMessage,
              propertyIds
            });
            if (!idxMap[goodsId]) idxMap[goodsId] = {};
            let tempPropId = null;
            if (propertyIds?.length > 0) {
              // 如果有属性，将属性排序后拼成一个字符串作为标示
              propertyIds.sort((a, b) => a - b);
              tempPropId = propertyIds.join('');
            }
            if (tempPropId) {
              (idxMap[goodsId][skuId] = {})[tempPropId] = goodsList.length - 1;
            } else {
              idxMap[goodsId][skuId] = goodsList.length - 1;
            }
          }
        );
      });
      return goodsList;
    }
  }
});
