.container {
  display: flex;
  padding: 54rpx 24rpx;
  justify-content: space-between;
  flex-wrap: nowrap;
  background-color: #fff;
  border-radius: 16rpx;;

  &__main {
  
    &--title {
      display: flex;
      margin-bottom: 16rpx;
      max-width: 560rpx;
      font-size: 34rpx;
      color: #323233;
      font-weight: 500;
      line-height: 44rpx;
      align-items: center;
      flex-wrap: nowrap;

      &--text {
        overflow: hidden;
        white-space: nowrap;
        max-width: 100%;
        text-overflow: ellipsis;
      }
    }

    &--tag--root {
      display: flex;
      flex-shrink: 0;
    }

    &--tag {
      margin-left: 8rpx;
      height: 32rpx;
      line-height: 24rpx;
      box-sizing: border-box;
    }

    &--address {
      font-size: 26rpx;
      color: #323233;
      line-height: 36rpx;
    }
  }

  &__link {
    display: flex;
    align-items: center;
  }
}