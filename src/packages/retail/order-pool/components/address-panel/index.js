import orderPoolGlobal from 'retail/util/order-pool';
import { enterShopSelect } from 'common-api/multi-shop/multi-shop-redirect';
import args from '@youzan/weapp-utils/lib/args';
import navigate from '@/helpers/navigate';
import { ORDER_WAY } from '../../constants';

const app = getApp();

Component({
  properties: {
    isLink: Boolean
  },
  options: {
    pureDataPattern: /^_/
  },
  data: {
    mode: null,
    modeName: null,
    _store: {},
    _address: {},
    title: '',
    subTitle: ''
  },
  attached() {
    const mode = orderPoolGlobal.getCurrentMode();
    const data = { mode };
    +mode === ORDER_WAY.SELF_TAKE
      ? Object.assign(data, this.setStore())
      : Object.assign(data, this.setAddress());
    this.setData(data);
  },
  methods: {
    setStore() {
      const store = orderPoolGlobal.getCurrentStore();
      const {
        shopName,
        address: { province, city, county, address }
      } = store;
      return {
        modeName: '自提',
        _store: store,
        title: shopName,
        subTitle: `${province || store.province}${city || store.city}${county ||
          store.county}${address || store.address}`
      };
    },
    setAddress() {
      const address = orderPoolGlobal.getCurrentAddress();
      const store = orderPoolGlobal.getCurrentStore();
      const { province, city, county, addressDetail } = address;
      return {
        modeName: '外卖',
        _address: address,
        _store: store,
        title: `${province}${city}${county}${addressDetail}`,
        subTitle: `由  ${store.shopName}  送出`
      };
    },
    onChange() {
      if (!this.properties.isLink) return;
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'change_address', // 事件标识
          en: '切换网店/地址', // 事件名称
          params: {} // 事件参数
        });
      const { mode } = this.data;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const url = currentPage.route;
      const options = currentPage.options;
      if (mode === ORDER_WAY.SELF_TAKE) {
        enterShopSelect(
          {
            redirectUrl: encodeURIComponent(
              args.add(`/${url}`, {
                ...options,
                mode: ORDER_WAY.SELF_TAKE,
                isSwitch: 1
              })
            )
          },
          {
            redirectFunc: navigate.navigate,
            extOpt: {
              chainBizType: 'retail_shelf',
              isHideAddress: true
            }
          }
        );
      } else {
        const address = orderPoolGlobal.getCurrentAddress();
        const { lat, lon } = address;
        enterShopSelect(
          {
            redirectUrl: encodeURIComponent(
              args.add(`/${url}`, {
                ...options,
                mode: ORDER_WAY.DELIVERY,
                isSwitch: 1
              })
            ),
            isHideLocation: true
          },
          {
            redirectFunc: navigate.navigate,
            location: {
              lat,
              lng: lon,
              currentLocation: address
            },
            extOpt: {
              addressId: address.id,
              chainBizType: 'retail_shelf',
            }
          }
        );
      }
    }
  }
});
