<view class="container" bindtap="onChange">
  <view class="container__main">
    <view class="container__main--title">
      <text class="container__main--title--text">{{title}}</text>
      <van-tag 
        wx:if="{{modeName}}"
        class="container__main--tag--root"
        custom-class="container__main--tag"
        color="rgba(238, 10, 36, .1)"
        text-color="#ee0a24"
        round
      >{{modeName}}</van-tag>
    </view>
    <view class="container__main--address">{{subTitle}}</view>
  </view>
  <view wx:if="{{isLink}}" class="container__link">
    <van-icon name="arrow" />
  </view>
</view>