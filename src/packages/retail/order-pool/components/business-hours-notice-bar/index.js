import api from '../../api';

// 1 使用停业设置； 2使用营业设置
const MODE = {
  CLOSE: 1,
  OPEN: 2
};

// 1全天，2每天重复，3每周重复
const BUSINESS_HOURS_MODE = {
  WHOLE_DAY: 1,
  REPEAT_EVERY_DAY: 2,
  REPEAT_EVERY_WEEK: 3
};

const THIRTY_MINUTES = 30 * 60 * 1000;

Component({
  properties: {},
  data: {
    text: ''
  },
  observers: {},
  lifetimes: {
    async attached() {
      try {
        const businessHours = await api.getShopBusinessHours();
        this.checkBusinessHours(businessHours);
      } catch (error) {
        console.log(error);
      }
    }
  },
  methods: {
    checkBusinessHours({ businessHoursSetting, mode, suspendSetting, businessStatus }) {
      const currentTime = new Date();
      let text = '';
      // 使用营业设置
      if (mode === MODE.OPEN) {
        const {
          businessHoursMode,
          dailyBusinessHours,
          weeklyBusinessHours
        } = businessHoursSetting;
        // 全天
        if (businessHoursMode === BUSINESS_HOURS_MODE.WHOLE_DAY) {
          text = '';
        }
        // 每天重复
        if (businessHoursMode === BUSINESS_HOURS_MODE.REPEAT_EVERY_DAY) {
          text = this.calcText(dailyBusinessHours, currentTime, businessStatus);
        }
        // 每周重复
        if (businessHoursMode === BUSINESS_HOURS_MODE.REPEAT_EVERY_WEEK) {
          weeklyBusinessHours.some(({ timeRanges, weekdays }) => {
            const dayOfWeek = currentTime.getDay();
            // 判断今天是否在 weekdays 设置内
            if (weekdays.indexOf(dayOfWeek) > 0) {
              text = this.calcText(timeRanges, currentTime, businessStatus);
              return true;
            }
            return false;
          });
        }
      }
      // 使用停业设置
      if (mode === MODE.CLOSE) {
        const { endDatetime } = suspendSetting;
        // 停业结束时间
        const end = new Date(endDatetime);
        // 当前时间大于停业结束时间
        if (currentTime > end) {
          text = '';
        }
      }

      this.setData({
        text
      });
    },
    calcText(timeRanges, currentTime, businessStatus = {}) {
      let text = '';
      const { endTime, thirtyMinutesClose } = this.calcEveryDayBusiness(
        timeRanges,
        currentTime
      );

      const { isOpen = true } = businessStatus;

      if (isOpen && thirtyMinutesClose) {
        text = `门店于${endTime}打烊，将无法再提交订单，请尽快完成`;
      }

      if (!isOpen) {
        text = '门店已经打烊';
      }

      return text;
    },
    calcEveryDayBusiness(timeRanges, currentTime) {
      let business = {};
      timeRanges.some(({ endTime, startTime }) => {
        const [startHour, startMinute] = startTime.split(':');
        const [endHour, endMinute] = endTime.split(':');
        const start = new Date(
          currentTime.getFullYear(),
          currentTime.getMonth(),
          currentTime.getDate(),
          +startHour,
          +startMinute,
          currentTime.getSeconds()
        );
        const end = new Date(
          currentTime.getFullYear(),
          currentTime.getMonth(),
          currentTime.getDate(),
          +endHour,
          +endMinute,
          currentTime.getSeconds()
        );

        // 在营业时间内，停止遍历
        if (currentTime >= start && currentTime <= end) {
          const beforeCloseTime = end - currentTime;
          if (beforeCloseTime < THIRTY_MINUTES) {
            // 距离打烊前时间，小于30分钟提示。当找到符合的提示，返回 true 结束遍历
            business = {
              endTime,
              thirtyMinutesClose: true,
            };
            return true;
          }
          return true;
        }

        return false;
      });
      return business;
    }
  }
});
