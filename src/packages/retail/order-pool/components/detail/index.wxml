<view
  wx:for="{{showMore ? list : [list[0]]}}"
  wx:for-item="participant"
  wx:key="buyerId"
>
  <panel>
    <goods-card
      isSponsor="{{(buyerId == participant.buyerId && isSponsor) || (promoterBuyId == participant.buyerId && isParticipant)}}"
      isParticipant="{{buyerId == participant.buyerId && isParticipant}}"
      participant="{{participant}}"
    >
      <view slot="content">
        <view
          wx:for="{{participant.goodsInfoList}}"
          wx:for-item="goodInfo"
          wx:key="index"
        >
          <goods-item info="{{goodInfo}}"></goods-item>
        </view>
        <view
          wx:if="{{participant.goodsInfoList.length !== 0}}"
          class="order-pool-detail"
        >
          <view class="order-pool-detail__cell" wx:if="{{participant.postage}}">
            <view>配送费</view>
            <view class="order-pool-detail__cell__price">{{tools.toPrice(participant.postage)}}</view>
          </view>
          <view class="order-pool-detail__cell" wx:if="{{participant.customerDiscount}}">
            <view>会员优惠</view>
            <view class="order-pool-detail__cell__price">-{{tools.toPrice(participant.customerDiscount)}}</view>
          </view>
          <view class="order-pool-detail__cell" wx:if="{{participant.activityDiscount}}">
            <view>活动优惠</view>
            <view class="order-pool-detail__cell__price">-{{tools.toPrice(participant.activityDiscount)}}</view>
          </view>
          <view class="order-pool-detail__cell" wx:if="{{participant.coupon}}">
            <view>优惠券</view>
            <view class="order-pool-detail__cell__price">-{{tools.toPrice(participant.coupon)}}</view>
          </view>
          <view class="order-pool-detail__cell">
            <view>小计</view>
            <view class="order-pool-detail__cell__bold order-pool-detail__cell__price">{{tools.toPrice(participant.totalPrice)}}</view>
          </view>
        </view>
      </view>
    </goods-card>
  </panel>
</view>
<view
  wx:if="{{list.length > 1 && !showMore}}"
  class="check-more"
  bind:tap="onShowMore"
>
  查看更多
  <van-icon name="arrow-down" />
</view>
<panel>
  <product-price-count
    goodsNum="{{goodsNum}}"
    totalPayPrice="{{totalPayPrice}}"
  />
</panel>

<wxs src="./index.wxs" module="tools" />

    