const app = getApp();

Component({
  properties: {
    list: Array,
    themeMainColor: String,
    totalPayPrice: Number,
    goodsNum: Number,
    promoterBuyId: Number,
    isSponsor: Boolean,
    isParticipant: Boolean
  },
  data: {
    participantList: [],
    showMore: false,
    buyerId: +app.getBuyerId()
  },
  observers: {
    list() {
      this.setData({
        buyerId: +app.getBuyerId()
      });
    }
  },
  methods: {
    onShowMore() {
      this.setData({
        showMore: true
      });
    }
  }
});
