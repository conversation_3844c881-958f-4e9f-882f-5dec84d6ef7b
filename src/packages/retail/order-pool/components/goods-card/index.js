import { PARTICIPANT_STATUS } from '../../constants';

Component({
  options: {
    multipleSlots: true
  },
  properties: {
    themeMainColor: String,
    isSponsor: <PERSON><PERSON>an,
    isParticipant: <PERSON><PERSON><PERSON>,
    participant: Object,
    modeType: String
  },
  data: {
    participantStatus: PARTICIPANT_STATUS
  },
  methods: {
    onOrderImmediate() {
      this.triggerEvent('onOrderImmediate');
    }
  }
});
