.wrapper {
  border-radius: 16rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.people {
  display: flex;
  padding: 0 24rpx;
  height: 136rpx;
  justify-content: space-between;
  align-items: center;

  &__info {
    display: flex;
    flex: 1;
    overflow: hidden;
    align-items: center;
  
    &--img {
      display: flex;
      margin-right: 16rpx;
      min-width: 72rpx;
      max-width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
      background-color: #ddd;
    }

    &--nickname {
      margin-right: 16rpx;
      font-size: 28rpx;
      color: #323233;
      font-weight: 500;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &--type {
      display: flex;
      margin-right: 16rpx;
      padding: 12rpx;
      height: 34rpx;
      line-height: 34rpx;
      font-size: 24rpx;
      color: #323233;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      border: 1px solid #dcdee0;
      border-radius: 4rpx;
      box-sizing: border-box;
    }
  }

  &__status {
    font-size: 24rpx;
    text-align: right;

    &__ing {
      color: #ed6a0c;
    }
    
    &__done {
      color: #969799;
    }
  }

  &__btn {
    width: 78px;
    height: 32px;
    line-height: 32px;
    border-radius: 16px;
    border: solid 1px var(--theme-main-color);
    font-size: 13px;
    text-align: center;
    color: var(--theme-main-color);

    &::after {
      border: none;
    }
  }
}