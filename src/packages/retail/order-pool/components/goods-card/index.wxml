<view 
  class="wrapper"
>
  <slot name="header"></slot>
  <view class="people">
    <view class="people__info">
      <image class="people__info--img" src="{{participant.imgUrl}}"></image>
      <text class="people__info--nickname">{{participant.nickName}}</text>
      <text wx:if="{{isSponsor}}" class="people__info--type">发起人</text>
      <text wx:if="{{isParticipant}}" class="people__info--type">我</text>
    </view>
    <view class="people__btn" wx:if="{{participantStatus.NEW === participant.status && isSponsor && modeType === 'sponsor' && participant.goodsInfoList.length === 0}}" bindtap="onOrderImmediate">立即点单</view>
    <view class="people__status people__status__done" wx:if="{{participantStatus.NEW === participant.status && (!isSponsor || (isSponsor && modeType === 'participant'))}}">还未选购</view>
    <view class="people__status people__status__ing" wx:if="{{participantStatus.PRODUCT_ADDING === participant.status}}">选购中</view>
    <view class="people__status people__status__done" wx:if="{{participantStatus.PRODUCT_ADDED === participant.status}}">选购完成</view>
  </view>
  <slot name="content"></slot>
  <slot name="footer"></slot>
</view>
