<view class="discount-bar">
  <van-tag round color="{{themeMainRgbColorWithAlpha10}}" text-color="{{themeMainColor}}" custom-class="discount-bar__tag">
    {{ discountTag }}
  </van-tag>
  <text class="discount-bar__text">{{ discountContent }}</text>
  <view style="color: {{themeMainColor}}" class="discount-bar__btn" bind:tap="onChooseAddOnItem">
    <text style="color: {{themeMainColor}}">{{ addOnButton.text }}</text>
    <van-icon name="arrow" />
  </view>
  <make-up-pop
    style="--theme-main-color: {{themeMainColor}}"
    activities="{{ activities }}"
    show="{{makeUpVisible}}"
    isSuspendShop="{{isSuspendShop}}"
    themeMainColor="{{themeMainColor}}"
    chosenTotal="{{chosenTotal}}"
    bind:onMakeUpClose="onMakeUpClose"
    bind:clickShowDetail="onGoodsItemDetail"
    bind:editGoodsNum="onGoodsNumChange"
  />
</view>