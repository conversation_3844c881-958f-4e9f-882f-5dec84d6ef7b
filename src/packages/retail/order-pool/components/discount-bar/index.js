Component({
  properties: {
    discountContent: String,
    discountTag: String,
    addOnButton: {
      type: Object,
      value: {
        disabled: false,
        text: '去凑单'
      }
    },
    themeMainColor: String,
    themeMainRgbColorWithAlpha10: String
  },
  data: {
    makeUpVisible: false
  },
  methods: {
    // 凑单商品
    onChooseAddOnItem() {
      this.setData({
        makeUpVisible: true
      });
      this.triggerEvent('chooseAddOnItem');
    },
    onMakeUpClose() {
      this.setData({
        makeUpVisible: false
      });
    },
    onGoodsItemDetail() {
      this.triggerEvent('goodsItemDetail');
    },
    onGoodsNumChange() {
      this.triggerEvent('goodsNumChange');
    }
  }
});
