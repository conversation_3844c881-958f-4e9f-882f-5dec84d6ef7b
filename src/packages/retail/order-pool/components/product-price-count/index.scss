.product-price-count {
  height: 76px;
  padding: 12px;
  border-radius: 8px;
  background-color: #ffffff;
  box-sizing: border-box;

  &__row-one {
    height: 20px;
    font-size: 14px;
    text-align: right;
    color: #323233;

    &__count {
      color: #969799;
    }

    &__product {
      font-weight: 500;
      display: inline-block;
    }

    &__price {
      color: #ee0a24;
      display: inline-block;

      &__unit {
        font-size: 12px;
      }

      &__int {
        font-size: 18px;
      }

      &__float {
        font-size: 12px;
      }
    }
  }

  &__row-two {
    height: 18px;
    font-size: 13px;
    text-align: right;
    color: #969799;
    margin-top: 14px;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;

    &__text {
      height: 18px;
      font-size: 13px;
      text-align: right;
      color: #969799;
    }
  }

  &__dialog {
    &__title {
      text-align: center;
      height: 44px;
      line-height: 44px;
      font-weight: 500;
      background-color: #ffffff;
    }

    &__p1 {
      height: 88px;
      margin: 16px;
      font-size: 16px;
      color: #323233;
    }

    &__bottom {
      padding: 90px 16px 0;
    }

    &__btn {
      width: 100%;
      height: 40px;
      line-height: 40px;
      border-radius: 20px;
      background-color: #ee0a24;
      color: #ffffff;
      font-size: 14px;
      margin: 5px 0;
      
      &::after {
        border: none;
      }
    }
  }
}