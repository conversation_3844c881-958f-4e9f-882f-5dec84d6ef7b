<view class="product-price-count">
  <view class="product-price-count__row-one">
    <text class="product-price-count__row-one__count">
      共{{goodsNum}}件，
    </text>
    <view class="product-price-count__row-one__product">
      商品小计：<view class="product-price-count__row-one__price">
        <text class="product-price-count__row-one__price__unit">¥</text>
        <text class="product-price-count__row-one__price__int">
          {{tools.toPricePartOfInt(totalPayPrice)}}.
        </text>
        <text class="product-price-count__row-one__price__float">
          {{tools.toPricePartOfFloat(totalPayPrice)}}
        </text>
      </view>
    </view>
  </view>
  <view class="product-price-count__row-two" bind:tap="onOpenDialog">
    <van-icon name="arrow" /><text class="product-price-count__row-two__text">账单计算规则</text>
  </view>
</view>

<van-popup
  show="{{ dialogVisible }}"
  round
  closeable
  position="bottom"
  bind:close="onCloseDialog"
>
  <view class="product-price-count__dialog__title">
    账单计算规则
  </view>
  <view class="product-price-count__dialog__p1">
    <view>优惠规则：</view>
    满减优惠金额、优惠券、新客首减、限时折扣、第2件半价等活动，根据每位参与人选购商品的金额占比，按比例分摊。
  </view>
  <view class="product-price-count__dialog__p1">
    <view>配送费计算规则：</view>
    配送费按照1笔订单收取，按参与人数均摊。
  </view>
  <view class="product-price-count__dialog__bottom">
    <button class="product-price-count__dialog__btn" bind:tap="onCloseDialog">
      我知道了
    </button>
  </view>
</van-popup>

<wxs src="./index.wxs" module="tools" />