// 导航栏上胶囊按钮的高度
export const CAPSULE_HEIGHT = 32;

// 安卓上，胶囊按钮距离导航栏顶部的距离
export const ANDROID_MARGIN = 8;

// iOS上，胶囊按钮距离导航栏顶部的距离
export const IOS_MARGIN = 6;

// 模式定义 Map
export const ORDER_WAY = {
  DELIVERY: 1,
  SELF_TAKE: 0
};

export const ORDER_POOL_STATUS = {
  INIT: 0, // 初始化
  LOCKED: 1, // 锁定
  CREATE: 2, // 创建
  CLOSED: 3, // 拼单关闭
  CANCEL: 99, // 取消
  SUCCESS: 100 // 成功
};

export const PARTICIPANT_STATUS = {
  NEW: 0, // 未选购
  PRODUCT_ADDING: 1, // 选购中
  PRODUCT_ADDED: 2 // 选购完成
};

export const ORDER_POOL_TYPE = {
  JOIN: 1, // "加入拼单")：不需要重新获取拼单详情，只需要将新的参与人加到拼单列表
  GOODS: 2, // "商品变更")：暂时不支持
  ONGOING: 3, // "选购中")：不需要重新获取拼单详情，只需要更新参与人状态
  CONFIRM_GOODS: 4, // "选购完成")：需要重新获取拼单详情
  EDIT_GOODS: 5, // "选购编辑（包含清空、修改、删除）")：需要重新获取拼单详情
  LOCKED: 6, // "拼单锁定")：需要重新获取拼单详情
  SWITCH: 7, // "店铺切换")：需要重新获取拼单详情
  CANCEL: 8, // "拼单取消")：需要重新获取拼单详情
  CREATE_ORDER: 9, // "订单创建")：需要重新获取拼单详情
  PAY: 10, // "支付")：需要重新获取拼单详情
  SWITCH_ADDRESS: 11, // 切换地址 需要重新获取拼单详情
  UNLOCK: 12 // (12, "拼单解锁")
};

export const URLS = {
  ORDER_POOL: '/packages/retail/order-pool/order-pool/index',
  ORDER_POOL_DETAIL: '/packages/retail/order-pool/detail/index',
  ORDER: '/packages/order/index',
  HOME: '/packages/home/<USER>/index',
  // SHELF: '/packages/retail/goods-shelf/index'
  SHELF: '/packages/retail-shelf/shelf/index'
};
