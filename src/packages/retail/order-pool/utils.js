// const app = getApp();

// // 使用试需要在 wxml 插入 <canvas style="position: absolute; top: -1000px; left: -1000px; width: 640px; height: 640px; background: #000;" canvas-id="canvas"></canvas>
// export function scaleImgForShare(imgUrl) {
//   return new Promise(resolve => {
//     wx.getImageInfo({
//       src: imgUrl,
//       success: res => {
//         const ctx = wx.createCanvasContext('canvas');
//         let canvasW = 0;
//         let canvasH = 0;

//         const { height, width } = res;

//         if (height >= width) {
//           canvasH = height;
//           canvasW = (height * 5) / 4; // 宽度加大
//         } else {
//           canvasW = width;
//           canvasH = (width * 4) / 5;
//         }

//         ctx.drawImage(
//           res.path,
//           (canvasW - res.width) / 2, // 转成5:4后，左右多出裁切掉
//           0,
//           width,
//           height,
//           0,
//           0,
//           canvasW,
//           canvasH
//         );
//         ctx.draw(false, () => {
//           // 转成临时文件
//           wx.canvasToTempFilePath({
//             width: canvasW,
//             height: canvasH,
//             destWidth: canvasW,
//             destHeight: canvasH,
//             canvasId: 'canvas',
//             fileType: 'jpg',
//             success: res => {
//               resolve(res.tempFilePath);
//             }
//           });
//         });
//       }
//     });
//   });
// }

// export const genShareImg = (n = 0) => {
//   const { base: { logo = '' } = {} } = app.getShopInfoSync() || {};
//   if (logo) {
//     return scaleImgForShare(logo);
//   }
//   if (n === 0) return Promise.resolve(null);
//   return new Promise(resolve => {
//     setTimeout(() => {
//       resolve(genShareImg(n - 1));
//     }, 600);
//   });
// };

export function cloneDeep(res = {}) {
  if (!res) {
    return res;
  }

  if (Array.isArray(res)) {
    return res.map((item) => {
      return cloneDeep(item);
    });
  }

  if (typeof res === 'object') {
    const result = {};
    Object.keys(res).forEach((key) => {
      result[key] = cloneDeep(res[key]);
    });
    return result;
  }

  return res;
}
