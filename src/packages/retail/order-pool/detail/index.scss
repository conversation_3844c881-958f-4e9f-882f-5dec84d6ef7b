.detail {
  position: relative;
  padding-top: 24rpx;
  min-height: 100vh;
  background-color: #f2f3f5;
}

.share {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;

  &__wrap {
    display: flex;
    height: 100rpx;
    padding: 0 32rpx;
    width: 100%;
    font-size: 28rpx;
    justify-content: flex-end;
    align-items: center;
    box-sizing: border-box;
  }

  &__btn {
    display: flex;
    margin: 0;
    padding: 10rpx 24rpx;
    font-size: 28rpx;
    color: #323233;
    line-height: 40rpx;
    justify-content: center;
    align-items: center;
    border: 2rpx solid #dcdee0;
    box-sizing: border-box;
    border-radius: 32rpx;
    background-color: #fff;
  }

  &__btn::after {
    border: none;
  }
}

.safe-area {
  display: flex;
  height: 72rpx;
}