import {
  getThemeMainColor,
  initGlobalThemeType
} from 'retail/util/shelf-global';
import { isNewIphone } from 'shared/utils/browser/device-type';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import api from '../api/index';
import { URLS } from '../constants';

const app = getApp();

Page({
  data: {
    themeMainColor: getThemeMainColor(),
    list: [],
    totalPayPrice: 0,
    goodsNum: 0,
    show: false,
    status: 0,
    isIphoneX: isNewIphone(),
    id: null,
    isSponsor: false,
    isParticipant: false,
    steps: [
      {
        text: '完成选品',
        inactiveIcon: 'checked'
      },
      {
        text: '提交订单',
        inactiveIcon: 'checked'
      },
      {
        text: '完成订单',
        activeIcon: 'checked'
      }
    ]
  },

  onLoad(query) {
    initGlobalThemeType(this);
    const { orderPoolId } = query;
    this.setData({
      id: orderPoolId
    });
    this.getDetail(orderPoolId);
  },

  getDetail(jointId) {
    return api
      .getOrderPoolDetail({
        jointId
      })
      .then(result => {
        const {
          totalPayPrice,
          goodsNum,
          participants,
          status,
          promoterBuyId
        } = result;
        const buyerId = +app.getBuyerId() || -1;
        this.setData({
          show: true,
          totalPayPrice,
          goodsNum,
          status,
          promoterBuyId: +promoterBuyId,
          list: participants,
          isSponsor:
            buyerId !== -1 && promoterBuyId !== -1 && buyerId === promoterBuyId,
          isParticipant:
            buyerId !== -1 && promoterBuyId !== -1 && buyerId !== promoterBuyId
        });
      })
      .catch(error => {
        Dialog.confirm({
          customStyle: 'border-radius: 32rpx',
          message: error?.msg || '拼单不见了，请稍后再试',
          confirmButtonText: '我知道了',
          showCancelButton: false,
          context: this,
          zIndex: 10000
        }).then(() => {
          wx.reLaunch({
            url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=0`)
          });
        });
      });
  },

  async onShareAppMessage() {
    app.logger &&
      app.logger.log({
        et: 'click', // 事件类型
        ei: 'share_joint_order', // 事件标识
        en: '分享账单', // 事件名称
        params: {} // 事件参数
      });
    const shopInfo = app.getShopInfoSync() || { base: { logo: '' } };
    const imageUrl = shopInfo.base.logo;
    const userInfo = app.getUserInfoSync() || {};
    const { id } = this.data;
    return {
      title: `${userInfo.nickname}给你分享账单啦～`,
      path:  app.genOrderPullJumpUrl(`${URLS.ORDER_POOL_DETAIL}?orderPoolId=${id}&nickname=${
        userInfo.nickname
      }&kdt_id=${app.getKdtId()}`),
      imageUrl
    };
  }
});
