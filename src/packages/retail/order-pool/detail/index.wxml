<view wx:if="{{isParticipant}}">
  <simple-steps steps="{{steps}}" active="{{2}}" themeMainColor="{{themeMainColor}}"></simple-steps>
</view>
<view class="detail" style="padding-bottom: {{isIphoneX ? '168rpx' : '100rpx'}}">
  <detail 
    wx:if="{{show}}"
    list="{{list}}"
    isSponsor="{{isSponsor}}"
    isParticipant="{{isParticipant}}"
    totalPayPrice="{{totalPayPrice}}"
    goodsNum="{{goodsNum}}"
    promoterBuyId="{{promoterBuyId}}">
  </detail>

  <view wx:if="{{isSponsor}}" class="share">
    <view class="share__wrap">
      <button class="share__btn" open-type="share" bindtap="onShareAppMessage">分享账单</button>
    </view>
    <view wx:if="{{isIphoneX}}" class="safe-area"></view>
  </view>
</view>

<van-dialog
  id="van-dialog" />

<canvas style="position: absolute; top: -1000px; left: -1000px; width: 640px; height: 640px; background: #000;" canvas-id="canvas"></canvas>

<wxs src="../index.wxs" module="tools" />
<inject-protocol noAutoAuth />