import Client, { SocketProvider } from '@youzan/imsdk_core';

const app = getApp();

const SOCKET_URL_MAP = {
  online: 'wss://im-mercury-ws.youzan.com',
  qatest: 'wss://mercury-ws-qa.qima-inc.com',
  pre: 'wss://mercury-ws.qima-inc.com'
};

const SOURCE = 'joint';

class WS {
  constructor(options) {
    this.options = options;
    this.events = {};
    this.client = null;
  }

  getClient() {
    const { token, userId } = this.options;
    const events = this.events;
    this.client = new Promise((resolve, reject) => {
      const client = new Client(
        {
          token,
          userId,
          clientId: app.config.clientId,
          endpoint: 'wap',
          role: 'group'
        },
        {
          url: SOCKET_URL_MAP.online,
          socketProvider: SocketProvider.WEAPP,
          beatInterval: 15000
        }
      );

      client.on('authSuccess', () => {
        console.log('>>authSuccess');
        client.socketOpen = true;
        resolve(client);
      });

      client.on('authFail', () => {
        client.socketOpen = false;
        reject('登陆验证失败');
      });

      client.on('reconnect', msg => {
        // console.log('>>> reconnect', msg);
      });

      client.on('stop', msg => {
        console.log('>>> stop', msg);
      });

      client.on('error', msg => {
        wx.showToast({
          title: JSON.stringify(msg.errMsg),
          icon: 'none',
          duration: 2000
        });
        console.log('>>> error', msg);
        try {
          client.close();
        } catch (error) {
          console.log(error);
        }
        reject({
          ...msg,
          msg: msg.errMsg
        });
      });

      client.on('packet', msg => {
        console.log('>>> packet', msg);
        if (msg.args?.content) {
          try {
            const content = JSON.parse(msg.args.content);
            if (events[content.type] && content.source === SOURCE) {
              events[content.type].forEach(event => {
                event({
                  ...content,
                  key: msg.args.createTime
                });
              });
            }
          } catch (e) {
            console.log(e);
          }
        }
      });
      client.connect();
    });

    return this.client;
  }

  checkEvents(type) {
    if (!this.events[type]) {
      this.events[type] = [];
    }
  }

  on(type, fn) {
    this.checkEvents(type);
    if (this.events[type].indexOf(fn) < 0) {
      this.events[type].push(fn);
    }
  }

  emit(type, payload) {
    this.client.then(c => {
      // 不确定后端格式要求，先这么写
      c.send({ type, ...payload });
    });
  }

  close() {
    this.client.then(c => {
      c.close();
    });
  }
}

export default WS;
