<view class="navigation-bar">
  <view style="height: {{statusBarHeight}}px;" />
  <view class="custom-nav" style="height: {{navHeight}}px">
    <view class="custom-nav__icon">
      <view class="custom-nav__icon--button" bindtap="onBack">
        <van-icon name="arrow-left" size="20px"/>
      </view>
    </view>
    <view class="custom-nav__text">拼单</view>
    <view class="custom-nav__block"></view>
  </view>
</view>

<order-pool-role-switch
  wx:if="{{show}}"
  customStyle="padding-top: {{statusBarHeight + navHeight}}px; padding-bottom: {{isIphoneX ? '84px' : '50px'}}; min-height: calc(100vh - {{statusBarHeight + navHeight + (isIphoneX ? 84 : 50)}}px);"
  style="--theme-main-color: {{themeMainColor}}"
  themeMainRgbColorWithAlpha10="{{themeMainRgbColor}}"
  statusBarHeight="{{statusBarHeight}}"
  navHeight="{{navHeight}}"
  themeMainColor="{{themeMainColor}}"
  sponsorNickname="{{sponsorNickname}}"
/>

<van-dialog id="van-dialog" />

<auth-dialog
  show="{{authDialogShow}}"
  bind:success="onAuthConfirm"
  bind:fail="onAuthCancel">
</auth-dialog>

<combo-detail-pop themeMainColor="{{themeMainColor}}" comboDetail="{{ true }}" />

<canvas style="position: absolute; top: -1000px; left: -1000px; width: 640px; height: 640px; background: #000;" canvas-id="canvas"></canvas>
<inject-protocol noAutoAuth />