import {
  getThemeMainColor,
  initGlobalThemeType
} from 'retail/util/shelf-global';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import Store, { mapState } from '@youzan/vanx';
import { VanxPage } from 'pages/common/wsc-page/index';
import orderPoolGlobal from 'retail/util/order-pool';
import { isNewIphone } from 'shared/utils/browser/device-type';
import store from '../store';
import { CAPSULE_HEIGHT, IOS_MARGIN, ANDROID_MARGIN, URLS } from '../constants';
import api from '../api/index';

const app = getApp();

VanxPage({
  data: {
    themeMainColor: getThemeMainColor(),
    statusBarHeight: 0,
    navHeight: 0,
    sponsorNickname: '',
    show: false,
    isIphoneX: isNewIphone(),
    authDialogShow: false
  },

  mapData: {
    ...mapState(['ComboDetailShow'])
  },

  onLoad(query) {
    this.$store = new Store(store);
    initGlobalThemeType(this);
    app.getShopInfo().then(() => {
      this._loadPromise = Promise.resolve(query);
      app.getUserInfo(
        () => {
          this.onInit(query);
        },
        () => {
          this.setData({
            authDialogShow: true
          });
        }
      );
    });
  },

  onShow() {},

  onAuthCancel() {
    this.onBack();
  },

  onAuthConfirm() {
    this.setData({
      authDialogShow: false
    });
    this._loadPromise.then(query => {
      this.onInit(query);
    });
  },

  onShareAppMessage() {
    const shopInfo = app.getShopInfoSync() || { base: { logo: '' } };
    const imageUrl = shopInfo.base.logo;
    const userInfo = app.getUserInfoSync() || {};
    return {
      title: `${userInfo.nickname}喊你一起拼单啦～`,
      path:  app.genOrderPullJumpUrl(`${
        URLS.ORDER_POOL
      }?orderPoolId=${orderPoolGlobal.getCurrentId()}&nickname=${
        userInfo.nickname
      }&kdt_id=${app.getKdtId()}`),
      imageUrl
    };
  },

  async onInit(query) {
    const { statusBarHeight, system } = wx.getSystemInfoSync();
    let navHeight = CAPSULE_HEIGHT;
    if (~system.indexOf('iOS')) {
      navHeight += IOS_MARGIN * 2;
    } else {
      navHeight += ANDROID_MARGIN * 2;
    }

    const { orderPoolId, nickname = '', isSwitch } = query;

    if (+isSwitch === 1) {
      await this.onSwitchLBS(query);
    }

    orderPoolId
      ? orderPoolGlobal.setCurrentId(orderPoolId)
      : orderPoolGlobal.clear();
    this.setData({
      statusBarHeight,
      navHeight,
      sponsorNickname: nickname,
      show: true
    });
  },

  onBack() {
    const pages = getCurrentPages();
    if (pages.length === 1) {
      wx.reLaunch({
        url: URLS.HOME
      });
      return;
    }
    // 返回点单页，默认成自提
    wx.reLaunch({
      url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=0`)
    });
  },

  // 切换网店，可能切换地址
  async onSwitchLBS({ dbid }) {
    const jointId = '' + orderPoolGlobal.getCurrentId(); // 切换lbs回来全局必然缓存了拼单id
    if (!jointId) return Promise.resolve(); // 避免没考虑到到的异常场景
    const params = {
      jointId
    };
    const { id } = app.db.get(dbid) || {};
    if (id) {
      params.addressId = id;
    }
    return api.switchAddress(params).catch(error => {
      Dialog.confirm({
        customStyle: 'border-radius: 32rpx',
        message: error?.msg || '更改失败，请重试',
        confirmButtonText: '我知道了',
        showCancelButton: false,
        context: this,
        zIndex: 10000
      }).then(() => {
        wx.reLaunch({
          url: app.genOrderPullJumpUrl(`${URLS.SHELF}?mode=0`)
        });
      });
    });
  }
});
