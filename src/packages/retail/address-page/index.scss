$address-page-item-border-radius: 4px;
$addres-page-font-size: 14px;
$addres-page-btn-font-size: 14px;

.address-page-box {
  background: #fff;
  border-radius: $address-page-item-border-radius;
  box-sizing: border-box;
  font-size: $addres-page-font-size;
  padding: 10px;
}

.address-page-btn {
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
  font-size: $addres-page-btn-font-size;
  box-sizing: border-box;

  &:active {
    opacity: 0.6;
  }
}

.address-page {
  width: 100vw;
  height: 100vh;
  background: #f2f3f5;
  padding: 10px;
  box-sizing: border-box;

  &__info {
    min-height: 220px;
    @extend .address-page-box;

    &-item {
      min-height: 40px;
      max-height: 55px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-desc {
        width: 70px;
        margin-right: 20px;
      }

      &-content {
        flex: 1;

        .address-page-disable-input {
          width: 100%;
          height: 50px;
          overflow: auto;
        }
      }

      .address-page-textarea {
        height: 50px;
        width: 100%;
        overflow-y: atuo;    
      }
    }

    &-item:nth-child(3),
    &-item:nth-child(4) {
        align-items: flex-start;
        margin-top: 10px;
    }
  }

  &__setting {
    margin-top: 10px;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    @extend .address-page-box;
  }

  &__save-btn {
    margin-top: 30px;
    background: red;
    color: #fff;
    @extend .address-page-btn;
  }

  &__delete-btn {
    margin-top: 15px;
    background: #fff;
    @extend .address-page-btn;
  }
}
