const app = getApp();

export const addAddress = data => {
  return app.request({
    path: '/wscshop/api/showcase-retail/addAddress.json',
    method: 'POST',
    data: {
      userId: app.getToken('buyerId'),
      ...data
    }
  });
};

export const delAddress = addressId => {
  return app.request({
    path: '/wscshop/api/showcase-retail/delAddress.json',
    method: 'POST',
    data: {
      userId: app.getToken('buyerId'),
      addressId
    }
  });
};

export const updateAddress = data => {
  return app.request({
    path: '/wscshop/api/showcase-retail/updateAddress.json',
    method: 'POST',
    data: {
      userId: app.getToken('buyerId'),
      ...data
    }
  });
};

export const setDefaultAddress = (addressId, defaultFlag) => {
  return app.request({
    method: 'POST',
    path: '/wscshop/api/showcase-retail/setDefault.json',
    data: {
      userId: app.getToken('buyerId'),
      addressId,
      defaultFlag
    }
  });
};
