<view class="address-page">

    <view class="address-page__info">
        <view class="address-page__info-item">
            <view class="address-page__info-item-desc">收货人</view>
            <input 
                type="text" 
                placeholder="请填写收货人姓名" 
                class="address-page__info-item-content" 
                bindinput="handleUserNameInput"
                value="{{oldEditAddress.userName}}"
            />
        </view>

        <view class="address-page__info-item">
            <view class="address-page__info-item-desc">手机号码</view>
            <input 
                type="number" 
                placeholder="请填写收货人手机号"
                class="address-page__info-item-content" 
                bindinput="handlePhoneInput"
                value="{{oldEditAddress.tel}}"
            />
        </view>

        <view class="address-page__info-item">
            <view class="address-page__info-item-desc">选择地区</view>
            <view bind:tap="clickChooseLocation" class="address-page__info-item-content">
                <view class="address-page-disable-input">
                    <view wx:if="{{!initAddressRegion}}" style="color: #808080">
                        地区信息
                    </view>
                    <block wx:else>
                        {{initAddressRegion}}
                    </block>
                </view>
            </view>
        </view>

        <view class="address-page__info-item">
            <view class="address-page__info-item-desc" style="margin-top: {{iosTextarea ? '15rpx': '0'}}">详细地址</view>
            <textarea 
                wx:if="{{!showSettingDialog}}" 
                placeholder="街道门牌、楼层房间号等" 
                class="address-page__info-item-content address-page-textarea"
                bindinput="handleAddressDetailTextarea"
                value="{{initAddressDetail}}"
            />
        </view>
    </view>

    <view class="address-page__setting">
        <view class="address-page__setting-title">设为默认收货地址</view>
        <switch bindchange="clickChangeDefaultAddress" checked="{{isDefaultAddress}}"/>
    </view>

    <view class="address-page__save-btn" style="background: {{themeMainColor}}" bind:tap="clickSaveAddress">保存并使用</view>
    <view class="address-page__delete-btn" bind:tap="delAddress">删除</view>

</view>

<dialog 
  class-name="dialog-open-setting"
  title="地理位置未授权"
  message="如需使用该小程序，请打开您手机的定位授权，开启后重新打开小程序"
  confirm-button-open-type="openSetting"
  show-cancel-button
  cancel-button-text="取消"
  confirm-button-text="去授权"
  show="{{showSettingDialog}}"
  bind:openSetting="handleConfirmSettingDialog"
  bind:cancel="handleCancelSettingDialog"
/>
<inject-protocol noAutoAuth />