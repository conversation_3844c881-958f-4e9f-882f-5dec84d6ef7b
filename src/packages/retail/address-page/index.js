import pick from '@youzan/weapp-utils/lib/pick';
import * as shelfUtil from 'retail/util/shelf-global';
import { geocoder, gcjToBaidu } from '@/helpers/lbs';
import {
  addAddress,
  delAddress,
  updateAddress,
  setDefaultAddress
} from './api';

const NEW_ADDRESS = 0;
const EDIT_ADDRESS = 1;

Page({
  data: {
    themeMainColor: shelfUtil.getThemeMainColor(),
    iosTextarea: wx.getSystemInfoSync().system.indexOf('iOS') > -1,
    newAddress: NEW_ADDRESS,
    editAddress: EDIT_ADDRESS,
    showSettingDialog: false,
    initAddressRegion: '',
    initAddressDetail: '',
    isDefaultAddress: false,
    oldEditAddress: {}
  },

  onLoad({ mode = NEW_ADDRESS, dbid }) {
    this.setData({ themeMainColor: shelfUtil.getThemeMainColor() });
    /**
     * province, city, county, addressDetail, lat, lon, isDefault, userName, tel
     */
    this.newAddress = {};
    this.currentMode = mode;
    if (mode == EDIT_ADDRESS) {
      const { addressInfo } = getApp().db.get(dbid);
      wx.setNavigationBarTitle({ title: '编辑收货地址' });
      const { province, city, county } = addressInfo;
      this.setData({
        oldEditAddress: addressInfo,
        initAddressRegion: province + city + county,
        initAddressDetail: addressInfo.addressDetail,
        isDefaultAddress: addressInfo.isDefault
      });
      this.newAddress = pick(addressInfo, [
        'province',
        'city',
        'county',
        'addressDetail',
        'lat',
        'lon',
        'isDefault',
        'userName',
        'tel'
      ]);
      this.newAddress.addressId = addressInfo.id;
    } else {
      wx.setNavigationBarTitle({ title: '新增地址' });
    }
  },

  onShow() {
    wx.getSetting({
      success: res => {
        const isAuthLocation = res.authSetting['scope.userLocation'];
        if (isAuthLocation === undefined) {
          // 第一次请求授权
          wx.getLocation({
            fail: () => {
              // 引导授权
              this.setData({ showSettingDialog: true });
            }
          });
        } else if (isAuthLocation === false) {
          // 已经拒绝过授权，再次进行引导授权
          this.setData({ showSettingDialog: true });
        }
      }
    });
  },

  handleUserNameInput({ detail }) {
    this.newAddress.userName = detail.value;
  },

  handlePhoneInput({ detail }) {
    this.newAddress.tel = detail.value;
  },

  handleAddressDetailTextarea({ detail }) {
    this.newAddress.addressDetail = detail.value;
  },

  clickChangeDefaultAddress({ detail }) {
    this.setData({ isDefaultAddress: detail.value });
    this.newAddress.isDefault = detail.value ? 1 : 0;
    if (this.currentMode == EDIT_ADDRESS) {
      const { id } = this.data.oldEditAddress;
      wx.showLoading();
      setDefaultAddress(id, this.newAddress.isDefault).then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '设置成功'
        });
        wx.hideLoading();
      });
    }
  },

  async clickSaveAddress() {
    const { userName, tel, addressDetail } = this.newAddress;
    const { initAddressRegion } = this.data;
    let errorTip = '';
    if (!this.checkIsValidStr(userName)) {
      errorTip = '请填写收货人姓名';
    } else if (!this.checkIsValidStr(tel)) {
      errorTip = '请填写手机号';
    } else if (!this.checkIsValidPhone(tel)) {
      errorTip = '手机号码有误，请重写填写';
    } else if (!this.checkIsValidStr(initAddressRegion)) {
      errorTip = '请选择地区';
    } else if (!this.checkIsValidStr(addressDetail)) {
      errorTip = '请填写详细地址';
    }

    if (errorTip) {
      wx.showToast({ icon: 'none', title: errorTip });
      return;
    }

    const geoResult = await geocoder({
      address: initAddressRegion + addressDetail
    });

    if (geoResult.status === 0) {
      const {
        location,
        address_components: { province, city, district }
      } = geoResult.result;
      this.newAddress.province = province;
      this.newAddress.city = city;
      this.newAddress.county = district;
      const { lng, lat } = gcjToBaidu(location.lng, location.lat);
      this.newAddress.lat = lat + '';
      this.newAddress.lon = lng + '';
    }
    wx.showLoading();
    if (this.currentMode == EDIT_ADDRESS) {
      updateAddress(this.newAddress)
        .then(() => {
          wx.hideLoading();
          wx.showToast({
            title: '更新成功',
            complete: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 1000);
            }
          });
          wx.hideLoading();
        })
        .catch(error => {
          wx.showToast({
            icon: 'none',
            title: error?.msg ?? '修改地址失败，请重试'
          });
          wx.hideLoading();
        });
    } else {
      addAddress(this.newAddress)
        .then(() => {
          wx.hideLoading();
          wx.showToast({
            title: '添加成功',
            complete: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 1000);
            }
          });
          wx.hideLoading();
        })
        .catch(error => {
          wx.showToast({
            icon: 'none',
            title: error?.msg ?? '添加地址失败，请重试'
          });
          wx.hideLoading();
        });
    }
  },

  delAddress() {
    const { id } = this.data.oldEditAddress;
    if (id) {
      wx.showLoading();
      delAddress(id)
        .then(() => {
          wx.hideLoading();
          wx.showToast({
            title: '删除成功',
            complete: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 1000);
            }
          });
        })
        .catch(error => {
          wx.showToast({
            icon: 'none',
            title: error?.msg ?? '删除地址失败，请重试'
          });
          wx.hideLoading();
        });
    } else {
      wx.navigateBack();
    }
  },

  handleCancelSettingDialog() {
    this.setData({ showSettingDialog: false });
    wx.navigateBack();
  },

  handleConfirmSettingDialog() {
    this.setData({ showSettingDialog: false });
  },

  clickChooseLocation() {
    wx.chooseLocation({
      success: res => {
        const { name: addressName, address } = res;

        geocoder({
          address: address + addressName
        })
          .then(res => {
            if (res.status === 0) {
              // eslint-disable-next-line camelcase
              const {
                location,
                address_components: {
                  province,
                  city,
                  district,
                  street = '',
                  street_number = ''
                },
                ad_info: { adcode }
              } = res.result;
              this.newAddress.province = province;
              this.newAddress.city = city;
              this.newAddress.county = district;
              const { lng, lat } = gcjToBaidu(location.lng, location.lat);
              this.newAddress.lat = lat + '';
              this.newAddress.lon = lng + '';
              this.newAddress.addressDetail = addressName;
              this.newAddress.areaCode = adcode;
              this.setData({
                initAddressDetail: addressName,
                initAddressRegion:
                  province + city + district + street + street_number
              });
            } else {
              wx.showToast({ title: res.message, icon: 'none' });
            }
          })
          .catch(() => {
            wx.showToast({
              title: '请重新选择地区',
              icon: 'none'
            });
          });
      }
    });
  },

  checkIsValidStr(str) {
    if (!str || !str.trim()) {
      return false;
    }
    return true;
  },

  checkIsValidPhone(phone) {
    return /^1[3456789]\d{9}$/.test(phone);
  }
});
