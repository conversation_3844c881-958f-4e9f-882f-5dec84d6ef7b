import WscPage from 'pages/common/wsc-page/index';
import {
  parseTrackData,
  stringifyTrackData
} from 'shared/utils/salesman-params-handler';
import args from '@youzan/weapp-utils/lib/args';

import api from './api';

WscPage({
  onLoad({ from_params: fromParams = '', ...queryRest }) {
    wx.showLoading({ title: '加载中...' });
    const { sales_id, store_kdt_id, ...rest } = parseTrackData(fromParams);
    if (!sales_id) {
      wx.hideLoading();
      wx.showToast({
        title: 'sales_id错误',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    if (!store_kdt_id) {
      wx.hideLoading();
      wx.showToast({
        title: 'store_kdt_id错误',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    api
      .getSalesId({
        targetKdtId: store_kdt_id,
        openId: sales_id,
        kdt_id: store_kdt_id
      })
      .then(res => {
        wx.hideLoading();
        if (!(res && res.value)) {
          wx.showToast({
            title: '获取导购员信息失败',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        wx.redirectTo({
          url: args.add(`/packages/retail/chat/index`, {
            from_params: stringifyTrackData({
              sales_id: res.value,
              store_kdt_id,
              ...rest
            }),
            kdt_id: store_kdt_id,
            source: 'new',
            ...queryRest
          })
        });
      })
      .catch(() => {
        wx.hideLoading();
        wx.showToast({
          title: '网络异常，获取导购员信息失败',
          icon: 'none',
          duration: 2000
        });
      });
  }
});
