<view class="exchange-goods-page">
	<view class="exchange-goods-page-wrap">
		<view
			wx:for="{{exchangeGoodsList}}"
			wx:for-item="exchangeGood"
			wx:for-index="goodsId"
			class="exchange-goods-page-item"
		>
			<image
				wx:if="{{exchangeGood.imgUrl}}"
				class="exchange-goods-page-image"
				src="{{exchangeGood.imgUrl}}"
				mode="aspectFill"
			/>
			<view class="exchange-goods-page-info">
				<text class="exchange-goods-page-title">{{exchangeGood.title}}</text>
				<view class="exchange-goods-page-price-wrap">
					<text class="exchange-goods-page-exchange-price" style="--exchange-price-font-size: {{exchangeGood.priceFontSize}}rpx">￥{{exchangeGood.exchangePrice}}</text>
					<text class="exchange-goods-page-label">换购价</text>
				</view>
				<text class="exchange-goods-page-origin-price">￥{{exchangeGood.originalPrice}}</text>
			</view>
		</view>
	</view>
</view>