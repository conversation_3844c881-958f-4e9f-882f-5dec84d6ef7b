import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

Page({
  data: {
    exchangeGoodsList: [],
  },

  onLoad(query = {}) {
    const { dbid } = query;
    const { pageTitle, exchangeGoodsList } = getApp().db.get(dbid, true);
    let goodsList = [];
    try {
      goodsList = JSON.parse(decodeURIComponent(exchangeGoodsList))?.map(
        (i) => {
          i.imgUrl = JSON.parse(i.imgUrl)?.[0]?.url;
          i.exchangePrice = this.toPrice(i.exchangePrice ?? 0);
          i.originalPrice = this.toPrice(i.originalPrice ?? 0);
          if (i.imgUrl?.endsWith('.gif')) {
            i.imgUrl = cdnImage(i.imgUrl, '!100x100+2x.jpg');
          }
          const str = i.exchangePrice + '';
          if (str.length < 6) {
            i.priceFontSize = 32;
          } else if (str.length === 6) {
            i.priceFontSize = 28;
          } else {
            i.priceFontSize = 24;
          }
          return i;
        }
      );
    } catch (error) {}
    this.setData({
      exchangeGoodsList: goodsList,
    });
    wx.setNavigationBarTitle({
      title: pageTitle ? decodeURIComponent(pageTitle) : '换购商品',
    });
    this.setPrevPageNotRefresh();
  },

  setPrevPageNotRefresh() {
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    if (!prevPage) return;
    prevPage.notRefresh = true;
  },

  toPrice(price) {
    price /= 100;
    // 不支持 (price / 100).toFixed
    return parseFloat(price.toFixed(2));
  },
});
