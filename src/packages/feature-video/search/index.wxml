<page-container
  forbid-copyright
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <!--<template is="searchbar" data="{{isShowClearButton,beginSearch,inputvalue,isfocus,searchbg}}"></template>-->
  <view style="min-height: {{ videos.length > 0 ? '0px' : systemInfo.windowHeight + 'px' }}">
    <block style="display: {{ videos.length > 0 ? 'block' : 'none' }}">
      <scroll-view
        class="search-goods__list"
        scroll-with-animation
        scroll-y
        bindscrolltolower="handleRecycleToLower"
      >
        <van-search
          background="{{ searchbg || '#F2F2F2' }}"
          input-class="feature-video__search__input"
          value="{{ inputValue }}"
          placeholder="搜索专题视频"
          focus="{{ focused }}"
          bind:change="searchChange"
          bind:search="searchDone"
        />
        <view
          class="search-goods__list-item"
          style="width: {{ systemInfo.windowWidth }}px;"
          wx:for="{{ videos }}"
          wx:for-index="index"
          wx:key="alias"
        >
          <showcase-feature-video
            component-data="{{ item }}"
            hq-kdt-id="{{ hqKdtId }}"
          />
        </view>
      </scroll-view>
    </block>
    <block wx:if="{{videos && videos.length == 0}}">
      <view class="search-goods__empty">
        <view class="search-goods__empty-box">
          <view class="search-goods__empty-line"></view>
        </view>
        <text class="search-goods__empty-text">
          没有找到你想要的专题视频
          换个搜索词试试
        </text>
      </view>
    </block>

  </view>

  <import src="/pages/common/wsc-page/index.wxml" />
  <template is="wsc-page" data="{{ wscpage }}" />
</page-container>
