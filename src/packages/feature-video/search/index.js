// 垃圾的代码。应该没有后续的维护了...
import each from '@youzan/weapp-utils/lib/each';
import WscPage from '../../../pages/common/wsc-page/index';

const app = getApp();
const systemInfo = app.getSystemInfoSync();

WscPage({
  /**
   * 页面的初始数据
   */
  data: {
    inputValue: '',
    keyword: '',
    page: 1,
    focused: false,
    loading: false,
    needLoadMore: false,
    showSoldNum: false,
    orderBy: '',
    order: '',
    themeClass: app.themeClass,
    systemInfo,
    videos: [],
    hqKdtId: app.getHQKdtId()
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 从热门搜索跳转进来，直接进行搜索
    if (options.q) {
      this.setYZData({
        keyword: options.q,
        inputValue: options.q
      });

      this.fetchProductList();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  handleRecycleToLower() {
    if (this.data.loading || !this.data.needLoadMore) {
      return;
    }

    this.setYZData({
      page: this.data.page + 1
    });
    this.fetchProductList();
  },

  searchChange(e) {
    this.setYZData(
      {
        inputValue: e.detail || ''
      },
      { immediate: true }
    );
  },

  searchDone(e) {
    const keyword = e.detail;
    this.setYZData({
      keyword,
      page: 1
    });

    this.__yzLog__({
      et: 'click',
      ei: 'search',
      en: '搜索',
      params: {
        words: keyword
      }
    });

    this.fetchProductList(true);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { keyword } = this.data;
    return {
      path: `/packages/feature-video/search/index?q=${keyword}`
    };
  },

  /**
   * 获取商品列表数据
   */
  fetchProductList(needRefresh) {
    const { keyword, page } = this.data;

    if (this.data.loading) {
      return;
    }

    const queryObj = {
      // 组装查询参数
      page_size: 2,
      page,
      keyword: encodeURI(keyword),
      from: 'weapp',
      hqKdtId: this.data.hqKdtId
    };

    wx.showToast({
      title: '加载中',
      icon: 'loading'
    });
    this.setYZData({ loading: true });

    app
      .request({ path: '/wscump/feature-video/list_detail.json', query: queryObj })
      .then(response => {
        response = response || {};
        const { videos } = this.data;
        const productList = needRefresh ? [] : [...videos]; // 如果是请求第一页数据，则置为空数组；否则在原数组上拼接新数据
        each(response.list, item => {
          const _item = { video: item };
          productList.push(_item);
        });
        if (response.page === 1 && response.list.length === 0) {
          this.setYZData({
            loading: false,
            needLoadMore: false,
            videos: []
          });
          return;
        }

        wx.hideToast();
        this.setYZData({
          needLoadMore: response.list.length === 2,
          loading: false,
          videos: productList
        });
      })
      .catch(() => {
        wx.hideToast();
        this.setYZData({ loading: false });
      });
  }
});
