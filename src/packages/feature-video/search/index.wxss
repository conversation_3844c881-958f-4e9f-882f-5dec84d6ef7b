
/* packages/shop/goods/search/index.wxss */
@import "shared/common/css/helper/index.wxss";

.search-goods__list {
  border-bottom: 1rpx solid #e9e9e9;
  background-color: #fff;
  height: 100vh;
}

.search-goods__cell {
  background: white;
  opacity: 1;
  padding: 10px 15px 10px 0;
  border-bottom: 1rpx solid #e9e9e9;
}

.search-goods__list-item {
  margin-bottom: 25px;
  box-sizing: border-box;
}

.search-goods__list-item:last-child {
  margin-bottom: 0;
}

.search-goods__list-item:last-of-type .search-goods__cell {
  border-bottom-color: transparent;
}

.search-goods__thumb {
  height: 196rpx;
  width: 196rpx;
}
.search-goods__detail {
  margin-left: 216rpx;
  height: 196rpx;
}

.search-goods__title {
  font-size: 14px;
  color: #333;
  line-height: 18px;
}

.search-goods__sale-info {
  position: absolute;
  left: 0;
  bottom: 0;
  margin-bottom: 0;
}

.search-goods__price {
  display: inline-block;
  font-size: 16px;
  vertical-align: middle;
}

.search-goods__sold-num {
  display: inline-block;
  margin-left: 15px;
  font-size: 12px;
  color: #9b9b9b;
  vertical-align: middle;
}

.search-goods__cart {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid;
  border-radius: 48rpx;
  text-align: center;
  line-height: 48rpx;
  font-size: 28rpx;
}

.search-goods__empty {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 140px;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.search-goods__empty-box {
  box-sizing: border-box;
  position: relative;
  width: 80px;
  height: 84px;
  border: 4px solid #e5e5e5;
  border-radius: 6px;
}

.search-goods__empty-box::before,
.search-goods__empty-box::after {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 11px;
  width: 10px;
  height: 10px;
  border: 4px solid #e5e5e5;
  border-radius: 50%;
}
.search-goods__empty-box::before {
  left: 9px;
}

.search-goods__empty-box::after {
  right: 9px;
}

.search-goods__empty-line {
  box-sizing: border-box;
  position: absolute;
  top: -3px;
  left: 13px;
  width: 46px;
  height: 46px;
  border: 4px solid #e5e5e5;
  border-top-color: transparent;
  border-left-color: transparent;
  border-radius: 50%;
  transform: rotate(45deg);
}

.search-goods__empty-text {
  font-size: 14px;
  color: #a3a3a3;
  text-align: center;
}
.sc-goods__photo--soldout::after {
  position:absolute;
  top:0;
  left:0;
  right:0;
  bottom:0;
  margin:auto;
  width:100%;
  height:100%;
  background-image:url(https://img.yzcdn.cn/public_files/2017/11/20/fab60eb5be80315a6dd19f91fbd1dca9.png);
  background-color:rgba(0, 0, 0, 0.3);
  background-position:center;
  background-repeat:no-repeat;
  background-size: 60px 60px;
  content: '';
}

.feature-video__search__input {
  border-radius: 8rpx;
}