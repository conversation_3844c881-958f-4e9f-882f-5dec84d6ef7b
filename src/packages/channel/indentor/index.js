import WscPage from 'pages/common/wsc-page';

const app = getApp();

WscPage({
  data: {
    src: ''
  },

  onLoad(options) {
    const { kdtId = app.getKdtId() } = options || {};
    const url = `https://h5.youzan.com/wscsalesman/channel/indentor?kdtId=${kdtId}`;

    if (url.indexOf('https://h5.youzan.com') < 0) {
      app.logger.appError({
        name: 'wsc',
        message: 'check enter shop url',
        detail: {
          appId: app.getAppId(),
          kdtId,
          buyerId: app.getBuyerId(),
          appVersion: app.getVersion(),
          mobile: app.getMobile(),
          url,
          time: new Date().toLocaleString()
        }
      });
    }

    this.setYZData({
      src: url
    });
  }
});
