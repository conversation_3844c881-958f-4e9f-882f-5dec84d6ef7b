import getConcatData from '@youzan/weapp-utils/lib/get-concat-data';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import { formatTeeGoods, getItemTitleOpt } from './decorate';

export function formatNewList(
  response,
  {
    page,
    pageSize,
    currentList,
    isLimitSold,
    limitSoldNum,
    hasMore,
    spmType,
    pageType,
    loggerParam = {},
    isUmpInfoShow = false,
    layout,
    needFormatPrice = true,
    titleConfig,
    soldOutGoodsImg,
    noNeedFormat = false,
  }
) {
  const res = response || [];
  // 当列表中含有榜单或佣金或推荐卡时，要占位
  const showExtra = res.some((el = {}) => {
    const hasRank = el.rankInfoDTO && Object.keys(el.rankInfoDTO).length !== 0;
    const showRebate =
      +el.salesmanRebateMaxPrice || (el.extension || {}).openSalesmanBooth;
    const showGuideCommission =
      +el.guideCommission || (el.extension || {}).openGuideBooth;
    return (
      hasRank || showRebate || el.showRecommendedCard || showGuideCommission
    );
  });
  const list = res.map((goods, index) => {
    const item = formatTeeGoods({
      goods,
      index,
      isUmpInfoShow,
      layout,
      needFormatPrice,
      showExtra,
      soldOutGoodsImg,
      noNeedFormat,
    });

    if (titleConfig) {
      item.titleOpt = getItemTitleOpt(item.titleOpt, titleConfig, index);
    }

    const { totalSoldNum } = item.otherOpt;
    const { rankType, rankNo } = item.rankOpt;
    const { slg = '', alg = '' } = item.extraInfo || {};
    const { id } = item.actionOpt;
    const bannerId = `${spmType}.${id}~${pageType}~${
      index + 1
    }~${makeRandomString(8)}`;
    // 为0时不显示销量信息 组件中 >=0 显示销量
    // 开启限制最低显示销量 && 销量小于限制最低销量
    const hideSoldNum =
      +totalSoldNum === 0 || (isLimitSold && +totalSoldNum < limitSoldNum);
    if (!hideSoldNum) {
      item.soldNumOpt = { totalSoldNum };
    }
    let rankLogger = {};
    if (rankNo && rankType) {
      rankLogger = {
        rank_type: rankType,
        rank_no: rankNo,
      };
    }
    item.actionOpt = {
      ...item.actionOpt,
      loggerParams: {
        goods_id: id,
        banner_id: bannerId,
        item_id: id,
        alg,
        slg,
        ...rankLogger,
        ...loggerParam,
      },
    };
    item.salesmanRebatePrice = goods.salesmanRebatePrice;
    item.salesmanRebateMaxPrice = goods.salesmanRebateMaxPrice;
    item.guideCommission = goods.guideCommission;
    return item;
  });
  const listData =
    +page === 1
      ? { 'goodsOpt.items': list }
      : getConcatData(list, 'goodsOpt.items', currentList.length);

  let needLoadMore = false;

  // 是否需要加载更多
  if (typeof hasMore !== 'undefined' ? hasMore : response.length === pageSize) {
    page++;
    needLoadMore = true;
  }

  // 是否为空
  const isEmpty = page === 1 && list.length === 0;
  return {
    list,
    listData,
    needLoadMore,
    isEmpty,
    page,
  };
}

export function getBarHeight() {
  const TAB_BAR_HEIGHT = 44;
  const systemInfo = wx.getSystemInfoSync();
  // 导航栏高度 = 状态栏高度 + 44
  const navBarHeight = systemInfo.statusBarHeight + TAB_BAR_HEIGHT;
  return {
    navBarHeight,
    statusBarHeight: systemInfo.statusBarHeight,
  };
}
