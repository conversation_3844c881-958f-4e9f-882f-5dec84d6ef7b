import get from '@youzan/weapp-utils/lib/get';
import format from '@youzan/utils/money/format';
import {
  SHOP_RANKING_MAP,
  RECOMMENDED_CARD_TYPE,
  MEMBER_CARD_TYPE_LIST,
  MEMBER_CARD_UNSUPPORT_OTHER_TAG_LIST,
  PRICE_ACTIVITY_LIST,
  ACTIVITY_MAP,
  ORIGIN_LAYOUT_NAME_MAP,
  TITLE_FONT_SIZE_MAP,
  LAYOUT_TYPE,
  BUY_BUTTON_TYPE,
  LAYOUT_MAP,
  SIZE_TYPE,
  TYPE_MAP,
} from './constants';
import formatPrice from '@youzan/utils/money/formatPrice';
import formatCommission from '@youzan/salesman-cube-core-utils/lib/formatCommission';

function getTitleStyle({ layout, sizeType }) {
  const marginTop =
    layout === ORIGIN_LAYOUT_NAME_MAP.SMALL ||
    (sizeType === TYPE_MAP.SAMPLE && layout !== ORIGIN_LAYOUT_NAME_MAP.LIST)
      ? 8
      : 12;
  const height = (layout === ORIGIN_LAYOUT_NAME_MAP.BIG ? 22 : 36) + marginTop;
  return {
    marginTop,
    height,
  };
}

function getTitleOpt(titleOpt, { showTitle, layout, sizeType }) {
  // 特殊逻辑，详细列表必要展示标题。。。。
  if (!showTitle && layout !== ORIGIN_LAYOUT_NAME_MAP.LIST) return null;

  const { height } = getTitleStyle({ sizeType, layout });

  return {
    ...titleOpt,
    titleFontSize: TITLE_FONT_SIZE_MAP[layout],
    titleLines: layout === ORIGIN_LAYOUT_NAME_MAP.BIG ? 1 : 2,
    bgColor: 'transparent',
    height: `${height}px`,
  };
}

// 新老商品卡片布局转化
export function getGoodsLayoutOpt(config, origin, opt = {}) {
  const { titleOpt, priceOpt, imgOpt } = origin;
  let { fill } = imgOpt;
  const { buyBtnType, buyBtn, sizeType, size, showTitle, price } = config;
  // decorate-tee 中  list的表示为  layoutType one  layout horizontal
  const layoutType = size === 3 ? 'one' : LAYOUT_TYPE[size];
  // 商品分组页特殊处理
  const btnOpt = +buyBtn === 1 ? BUY_BUTTON_TYPE[+buyBtnType] : undefined;
  if (opt.isTag && +buyBtnType === 1 && btnOpt) {
    btnOpt.name = 'cart-circle-o';
  }
  const layout = size === 3 ? LAYOUT_MAP.HORIZONTAL : LAYOUT_MAP.VERTICAL;
  const type = SIZE_TYPE[+sizeType];
  const newTitleOpt = getTitleOpt(titleOpt, {
    layout: size,
    showTitle: +showTitle,
    sizeType: type,
  });
  // const newTextAreaOpt = getTextAreaOpt(textAreaOpt, {
  //   sizeType: type,
  //   layout: size,
  // });

  // 瀑布流，图片填充要修改
  if (type === TYPE_MAP.WATERFALL) {
    fill = 'width';
  }
  return {
    'goodsOpt.itemCardOpt.imgOpt.fill': fill,
    'goodsOpt.itemCardOpt.titleOpt': newTitleOpt,
    'goodsOpt.itemCardOpt.priceOpt': +price === 1 ? priceOpt : undefined,
    'goodsOpt.itemCardOpt.btnOpt': btnOpt,
    'goodsOpt.itemCardOpt.type': type,
    'goodsOpt.itemCardOpt.layoutType': layoutType,
    'goodsOpt.itemCardOpt.layout': layout,
    'goodsOpt.itemCardOpt.textAreaOpt': null,
    'goodsOpt.layoutOpt.type': layoutType,
  };
}

export function getItemTitleOpt(titleOpt, config, index) {
  // 不展示标题
  if (!config.showTitle) return null;

  // 一行三个，样式特殊处理
  if (config.layoutType === 'hybrid' && titleOpt) {
    const extStyle = {};
    const { height } = getTitleStyle({
      sizeType: config.sizeType,
      layout:
        index % 3 === 0
          ? ORIGIN_LAYOUT_NAME_MAP.BIG
          : ORIGIN_LAYOUT_NAME_MAP.SMALL,
    });
    if (index % 3 === 0) {
      extStyle.titleFontSize = TITLE_FONT_SIZE_MAP[ORIGIN_LAYOUT_NAME_MAP.BIG];
      extStyle.titleLines = 1;
    }

    Object.assign(titleOpt, {
      ...extStyle,
      height: `${height}px`,
    });
  }
  return titleOpt;
}

/**
 * 商详预加载数据
 * 支持的类型  周期购,
 * @param goods
 * @param type
 * @param otherOpts
 * @returns
 */
export function formatGoodsPreloadOpt(goods, type = 'goods', otherOpts = {}) {
  let goodsPreloadOpt = {};

  let goodsInfo = {};

  if (type === 'groupon') {
    goodsInfo = get(goods, 'goodsInfo', {});
  }

  if (type === 'period_buy') {
    goodsInfo = get(goods, 'itemsInfo', {});
  }

  if (type === 'member_goods') {
    goodsInfo = get(goods, 'goodsItemDTO', {});
  }

  if (type === 'points_goods') {
    goodsInfo = get(goods, 'generalGoodsInfoDTO', {});
  }

  try {
    const preloadPrice =
      goods.activityPrice == null ? goods.price : goods.activityPrice;
    if (type === 'ump_limitdiscount') {
      goodsPreloadOpt = {
        image: {
          url: goods.imageUrl,
          width: 0, // 考虑去dom上获取
          height: 0,
        },
        alias: goods.idAlias,
        price: otherOpts.price,
        title: goods.title,
      };
    } else if (type === 'bargain') {
      goodsPreloadOpt = {
        image: {
          url: goods.thumbUrl,
          width: 0, // 考虑去dom上获取
          height: 0,
        },
        alias: goods.alias,
        price: otherOpts.price,
        title: goods.title,
      };
    } else if (type === 'ump_seckill') {
      goodsPreloadOpt = {
        image: {
          url: goods.thumbUrl,
          width: 0, // 考虑去dom上获取
          height: 0,
        },
        alias: goods.goodsAlias,
        price: otherOpts.price,
        title: goodsInfo.title,
      };
    } else if (type === 'points_goods') {
      goodsPreloadOpt = {
        image: {
          url: goodsInfo.generalGoodsImg,
          width: 0, // 考虑去dom上获取
          height: 0,
        },
        alias: goodsInfo.generalGoodsAlias,
        price: goods.price || otherOpts.price,
        title: goodsInfo.generalGoodsTitle,
      };
    } else if (type === 'member_goods') {
      goodsPreloadOpt = {
        image: {
          url: goodsInfo.goodsPic,
          width: 0, // 考虑去dom上获取
          height: 0,
        },
        alias: goodsInfo.goodsAlias,
        price: goods.price || otherOpts.price,
        title: goodsInfo.goodsTitle,
      };
    } else if (type === 'groupon' || type === 'period_buy') {
      goodsPreloadOpt = {
        image: {
          url: goodsInfo.attachmentUrl,
          width: goodsInfo.width,
          height: goodsInfo.height,
        },
        alias: goodsInfo.alias,
        price: goods.price || otherOpts.price,
        title: goods.title,
      };
    } else if (goods.picture && type === 'goods') {
      let pictureInfo = goods.picture;
      if (typeof goods.picture === 'string') {
        pictureInfo = JSON.parse(goods.picture);
      }
      const curPicture = pictureInfo.find((i) => i.url === goods.imageUrl);
      goodsPreloadOpt = {
        image: curPicture,
        alias: goods.alias,
        price: otherOpts.noNeedFormat
          ? preloadPrice
          : format(preloadPrice, true, false),
        title: goods.title,
      };
    } else if (
      type === 'goods' &&
      goods.imageUrl &&
      goods.width &&
      goods.height
    ) {
      goodsPreloadOpt = {
        image: {
          url: goods.imageUrl,
          width: goods.width,
          height: goods.height,
        },
        alias: goods.alias,
        price: otherOpts.noNeedFormat
          ? preloadPrice
          : format(preloadPrice, true, false),
        title: goods.title,
      };
    }
  } catch (error) {
    console.log(error);
  }

  goodsPreloadOpt._assign = true;

  return goodsPreloadOpt;
}

// 分销员佣金format
export function formatSalesmanOpt(goods) {
  // 是否展示返佣价格
  const { maxCommissionStr } = formatCommission({
    profitRange: [goods.salesmanRebateMaxPrice],
    commissionSendType: goods.salesmanCommissionSendType,
    commissionConfigDTO: goods.salesmanCommissionConfigDTO,
    customPointsName: goods.customPointsName,
  });
  const showRebate = !!maxCommissionStr;
  const showExtra =
    showRebate || (goods.extension && goods.extension.openSalesmanBooth);
  if (showExtra) {
    let title = ' ';
    const titleColor = 'var(--general, #ff720d)';

    if (showRebate) {
      title = `最高赚 ${maxCommissionStr}`;
    }
    return {
      title,
      titleColor,
    };
  }

  return null;
}

export function formatGuideOpt(goods) {
  const showRebate = +goods.salesmanRebatePrice;
  const showGuideCommission = +goods.guideCommission;
  const showExtra =
    (goods.extension && goods.extension.openGuideBooth) || showGuideCommission;
  if (showExtra) {
    let title = ' ';
    const titleColor = '#ff720d';

    if (!showRebate && showGuideCommission) {
      const guideCommissionPrice = formatPrice(Number(goods.guideCommission));
      title = `预计赚${guideCommissionPrice}元`;
    }

    return {
      title,
      titleColor,
    };
  }

  return null;
}

// 店铺榜单
export function formatShopRankingOpt(goods) {
  const { rankNo, rankType } = goods.rankInfoDTO || {
    rankNo: '',
    rankType: '',
  };
  if (rankNo && rankType) {
    return {
      title: `本店${SHOP_RANKING_MAP[rankType]}第${rankNo}`,
      titleColor: 'var(--icon, #323233)',
    };
  }
  return null;
}

function formatMenberDiscountOpt(goods) {
  // 会员价瀑布流问题，兜底用true，不至于瀑布流挂掉
  const showRecommendedCard = get(goods, 'showRecommendedCard', true);
  // 会员价瀑布流问题占位
  if (showRecommendedCard) {
    const title = goods.recommendedCard || ' ';
    const titleColor = 'var(--general, #999999)';
    return {
      title,
      titleColor,
    };
  }

  return null;
}

// 榜单信息和返利信息互斥，榜单信息 > 返利信息 > 推荐卡
function formatExtOpt(goods, showExtra) {
  const extTitle =
    formatShopRankingOpt(goods) ||
    formatSalesmanOpt(goods) ||
    formatGuideOpt(goods) ||
    formatMenberDiscountOpt(goods);

  // 占位，防止布局错乱
  if (extTitle || showExtra) {
    return {
      _assign: true,
      list: [
        {
          title: ' ',
          ...extTitle,
          vMargin: 4,
          hMargin: 0,
          bgColor: 'transparent',
          titleFontSize: 12,
          titleLines: 1,
          titleFontWeight: 'normal',
          titleExtraStyle: {
            'line-height': 1,
          },
        },
      ],
      align: 'left',
    };
  }
  return null;
}

function computedTags(activityInfos) {
  let tags = activityInfos.map((tag) => {
    const { labelThemeType, label, preLabel, type } = tag;

    if (labelThemeType === 'other') {
      tag.color = '#FAAB0C';
    }

    return {
      type: labelThemeType,
      label,
      preLabel,
      labelType: type,
    };
  });
  // 会员价标签处理
  tags = tags.filter(({ labelType }) => labelType !== RECOMMENDED_CARD_TYPE);
  if (
    tags.find(
      ({ labelType }) => labelType && MEMBER_CARD_TYPE_LIST.includes(labelType)
    )
  ) {
    tags = tags.filter(
      ({ labelType }) =>
        labelType && !MEMBER_CARD_UNSUPPORT_OTHER_TAG_LIST.includes(labelType)
    );
  }
  return tags;
}

function computedPostageActivity(list) {
  // postageFreeType 0 包邮 1 件 2 元
  const postageMap = {};
  let filterList = [];
  const preFilterList = []; // 不包邮的活动

  return list
    .map((item, index) => {
      if (
        item.type === ACTIVITY_MAP.MEET_REDUCTION ||
        item.type === ACTIVITY_MAP.POSTAGE_FREE
      ) {
        const { postageFreeType } = item;
        // 已经存在包邮了 直接过滤别的活动
        if (postageMap[0]) {
          filterList.push(index);
          return item;
        }

        // 是包邮活动 把不是之前不是包邮的过滤掉
        if (+postageFreeType === 0) {
          filterList = [...filterList, ...preFilterList];
        }

        // 包邮类型存在 && 不包邮 记录下不包邮的活动
        if (+postageFreeType > 0) {
          preFilterList.push(index);
        }
        // map 里不存在则写入
        if (!postageMap[postageFreeType]) {
          postageMap[postageFreeType] = {
            postageFreeThreshold: +item.postageFreeThreshold,
            index,
          };
        } else if (
          +item.postageFreeThreshold >=
          postageMap[postageFreeType].postageFreeThreshold
        ) {
          filterList.push(index);
        } else {
          filterList.push(postageMap[postageFreeType].index);
          postageMap[postageFreeType] = {
            postageFreeThreshold: +item.postageFreeThreshold,
            index,
          };
        }
      }
      return item;
    })
    .filter((item, index) => filterList.indexOf(index) < 0);
}

function computedPriceActivity(activityInfos) {
  const tags = activityInfos.filter(
    (item) => PRICE_ACTIVITY_LIST.indexOf(item.type) > -1
  );
  // 兼容价格标签
  return tags.map((tag) => ({
    preLabel: tag.preLabel,
    label: tag.priceLabel,
    type: 'primary',
  }));
}

function computedMenberActivity(tags) {
  tags = tags.filter(({ type }) => type !== RECOMMENDED_CARD_TYPE);
  if (tags.find(({ type }) => MEMBER_CARD_TYPE_LIST.includes(type))) {
    tags = tags.filter(
      ({ type }) => !MEMBER_CARD_UNSUPPORT_OTHER_TAG_LIST.includes(type)
    );
  }
  return tags;
}

function formatTags(activityInfos, isUmpInfoShow) {
  if (isUmpInfoShow) {
    const tags = computedTags(activityInfos);
    return tags;
  }
  let tags = [];
  const tagsList = activityInfos.filter(
    (item) => PRICE_ACTIVITY_LIST.indexOf(item.type) < 0
  );

  // 包邮特殊处理
  tags = computedPostageActivity(tagsList);

  // 会员价标签处理
  tags = computedMenberActivity(tagsList);

  // 价格优惠特殊处理
  tags.unshift(...computedPriceActivity(activityInfos));

  return tags;
}

export function formatTeeGoods(config) {
  const {
    goods,
    index = 0,
    isUmpInfoShow,
    layout,
    needFormatPrice = true,
    showExtra = false,
    soldOutGoodsImg,
    noNeedFormat,
  } = config;
  const { imageUrl, labelViewModel = {}, isImgCover } = goods;
  let mask = '';

  if (+goods.soldStatus === 2 && +goods.buyWay !== 0) {
    mask = 'soldout';
  }

  const imgOpt = {
    _assign: true,
    src: imageUrl,
    mask,
    keyLabel: undefined,
    soldOutGoodsImg,
  };

  if (isImgCover) {
    imgOpt.fill = 'cover';
  }

  if (labelViewModel.labelGroupModels) {
    const keyTagObj =
      labelViewModel.labelGroupModels.find((item) => item.type === 1) || {};
    const keyLabel =
      (keyTagObj.labelModels && keyTagObj.labelModels[0].name) || '';
    imgOpt.keyLabel = keyLabel;
  }

  const titleOpt = {
    title: goods.title,
  };

  const extraInfo = goods.extraInfo || {};

  const price =
    goods._originActivityPrice || goods.activityPrice || goods.price;
  const priceOpt = {
    price: needFormatPrice ? format(price, true, false) : price,
  };

  const originPirce = goods._originPrice || goods.price;
  const oPrice =
    goods.origin ||
    (+goods.activityPrice < +originPirce
      ? needFormatPrice
        ? format(originPirce, true, false)
        : originPirce
      : '');
  const oPriceOpt = {
    price: oPrice,
  };

  // 营销氛围和销售员导购信息
  const activityInfos = goods.activityInfos || [];
  const tags = formatTags(activityInfos, isUmpInfoShow);

  let num = 3;
  if (layout !== undefined) {
    // 一行三个、横向滚动 只展示1个标
    if (
      layout === ORIGIN_LAYOUT_NAME_MAP.THREE ||
      layout === ORIGIN_LAYOUT_NAME_MAP.SWIPE
    ) {
      num = 1;
    }
    // 一行两个、列表 只展示2个标
    else if (
      layout === ORIGIN_LAYOUT_NAME_MAP.SMALL ||
      layout === ORIGIN_LAYOUT_NAME_MAP.LIST
    ) {
      num = 2;
    } else if (layout === ORIGIN_LAYOUT_NAME_MAP.HYBRID) {
      num = index % 3 === 0 ? 3 : 2;
    }
  }

  const tagsOpt = {
    _assign: true,
    list: tags.slice(0, num),
  };

  // 预售
  const activityOpt = {
    _assign: true,
    text: '预售',
  };

  // 有秒杀活动的商品，需要先跳转到秒杀详情页
  let goodsActivityAlias = '';
  // 有砍价的商品需要传activityId、umpType参数
  let helpCutParam = {};
  if (activityInfos && activityInfos.length) {
    const { type, activityAlias, activityId } = activityInfos[0] || {};
    if (type === 'seckill') {
      goodsActivityAlias = activityAlias;
    }

    if (type === 'helpCut') {
      helpCutParam = {
        activityId,
        umpType: type,
      };
    }
  }

  const { comboMark = {} } = goods;
  const isOptionalCombo = comboMark.isCombo && comboMark.comboType === 1;

  const actionOpt = {
    _assign: true,
    alias: goods.alias,
    id: goods.id,
    postage: goods.postage,
    isVirtual: goods.isVirtual,
    picture: goods.imageUrl,
    link: {
      type: goodsActivityAlias ? 'seckill' : 'goods',
      alias: goodsActivityAlias || goods.alias,
      ...helpCutParam,
    },
    isOptionalCombo,
    comboMark,
  };

  // 榜单信息和返利信息互斥，榜单信息 > 返利信息 > 推荐卡
  const extOpt = formatExtOpt(goods, showExtra);

  // 店铺榜单信息
  const rankOpt = {
    ...(goods.itemRankDTO || goods.rankInfoDTO || {}),
  };

  // 其它信息
  const otherOpt = {
    sellPoint: goods.sellPoint,
    totalSoldNum: goods.totalSoldNum,
  };
  // 商品按钮加购数量信息
  const btnGoodsNumberOpt = goods.goodsCartDTO
    ? { ...goods.goodsCartDTO, _assign: true }
    : { num: 0, _assign: true };
  return {
    imgOpt,
    titleOpt,
    extraInfo,
    priceOpt,
    oPriceOpt,
    actionOpt,
    tagsOpt,
    goodsPreloadOpt: formatGoodsPreloadOpt(goods, 'goods', {
      noNeedFormat,
    }),
    ...(goods.preSale ? { activityOpt } : {}),
    extOpt,
    rankOpt,
    otherOpt,
    btnGoodsNumberOpt,
  };
}
