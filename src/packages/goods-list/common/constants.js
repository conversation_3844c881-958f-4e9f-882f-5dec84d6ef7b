import Tee from '@youzan/tee';

export const SIZE_TYPE = {
  0: 'card',
  1: 'waterfall',
  2: 'simple',
  3: 'promotion',
  4: 'multi',
  5: 'card2',
  6: 'points',
  7: 'card-shadow',
  8: 'tag-left',
};

export const TYPE_MAP = {
  WATERFALL: 'waterfall',
  SAMPLE: 'simple',
};

// 上下两个是一个东西，使用的地方不同，使用的方式不同
export const ORIGIN_LAYOUT_NAME_MAP = {
  BIG: 0,
  SMALL: 1,
  HYBRID: 2,
  LIST: 3,
  THREE: 5,
  SWIPE: 6,
};

// 图片文字布局
export const LAYOUT_MAP = {
  // 水平布局
  HORIZONTAL: 'horizontal',
  // 垂直布局
  VERTICAL: 'vertical',
};

export const BUY_BUTTON_TYPE = {
  1: {
    name: 'cart-o',
    type: 'icon',
    size: 24,
  },
  2: {
    type: 'icon',
    name: 'add-o',
  },
  3: {
    corner: 'circle',
    name: '马上抢',
    theme: 'primary',
    type: 'btn',
  },
  4: {
    type: 'btn',
    name: '购买',
  },
};

// 会员价标签列表
export const MEMBER_CARD_TYPE_LIST = [
  'customerDiscount',
  'mergedCustomerDiscount',
];

// 会员价互斥标签列表
export const MEMBER_CARD_UNSUPPORT_OTHER_TAG_LIST = [
  'seckill',
  'limitTimeDiscount',
  'groupon',
  'tuan',
  'helpCut',
  'helpDepositExpansion',
  'coupon',
];

const activities = [
  'MEET_REDUCTION',
  'SECKILL',
  'LIMIT_TIME_DISDCOUNT',
  'BALE',
  'DEPOSIT_EXPANSION',
  'HELP_DEPOSIT_EXPANSION',
  'GROUPON',
  'POSTAGE_FREE',
  'SECOND_HALF',
  'TUAN',
  'CUSTOMER_DISCOUNT',
  'MERGED_CUSTOMER_DISCOUNT',
  'HELP_CUT',
  'COUPON',
  'RECOMMENDED_CARD',
];

export const ACTIVITY_MAP = Object.fromEntries(
  activities.map((activity) => [activity, activity])
);

export const PRICE_ACTIVITY_LIST = [
  'SECKILL',
  'GROUPON',
  'LIMIT_TIME_DISDCOUNT',
  'TUAN',
];

export const RECOMMENDED_CARD_TYPE = 'recommendedCard'; // * 非会员下的会员推荐标识

export const TITLE_FONT_SIZE_MAP = {
  [ORIGIN_LAYOUT_NAME_MAP.BIG]: 16,
  [ORIGIN_LAYOUT_NAME_MAP.SMALL]: 13,
  [ORIGIN_LAYOUT_NAME_MAP.HYBRID]: 13,
  [ORIGIN_LAYOUT_NAME_MAP.LIST]: 14,
  [ORIGIN_LAYOUT_NAME_MAP.THREE]: 13,
  [ORIGIN_LAYOUT_NAME_MAP.SWIPE]: 13,
};

export const SKELETON_OPT = (config = {}) => ({
  type: 'card',
  layoutType: 'two',
  layout: LAYOUT_MAP.VERTICAL,
  corner: 'rect',
  imgOpt: {
    useSkeleton: true,
  },
  textAreaOpt: {
    skeletonList: [
      {
        skeletonStyle: `width: 100%;margin: 2px 0 8px 0;--skeleton-row-height: ${
          config.oneHeight || 20
        }px`,
      },
      {
        skeletonStyle: `height: 16px;margin-top: 6px; width: ${
          config.twoWidth || 100
        }px;`,
      },
      {
        skeletonStyle: `width: ${config.threeWidth || 60}px;margin: ${
          config.threeMargin || '8px 0 12px 0'
        };--skeleton-row-height: 20px`,
      },
    ],
    textAreaHeight: config.areaHeight || 98,
    useSkeleton: true,
  },
});

const getIsSkyline = () => {
  const skylineInfo = wx.getSkylineInfoSync();
  return skylineInfo?.isSupported;
};

const getUseContainer = () => {
  const app = Tee.getApp();
  const { use_open_container = false } = app.getShopConfigDataSync();
  return use_open_container;
};

const TITLE_OPT = {
  hMargin: 0,
  height: '44px',
  titleFontSize: 13,
  titleFontWeight: '400',
  titleLines: 2,
  vMargin: 0,
};

const ITEM_CARD_CONFIG = {
  layoutType: 'two',
  align: 'left',
  btnOpt: BUY_BUTTON_TYPE[1],
  layout: LAYOUT_MAP.VERTICAL,
  type: 'card',
  corner: 'circle',
  theme: 'theme',
  imgOpt: {
    fill: 'contain',
    ratio: '1-1',
    showKeyLabel: true,
    needPreload: true,
  },
  priceOpt: {
    fontSize: 18,
    fontWeight: '400',
    tagGap: 2,
  },
  oPriceOpt: {
    color: '#c8c9cc',
    delLine: true,
    fontSize: 12,
    tagGap: 2,
  },
  tagsOpt: {
    textAlign: 'left',
  },
  titleOpt: TITLE_OPT,
  preSaleGoodsLabel: '0',
  isSkyline: getIsSkyline(),
};

export const LAYOUT_TYPE = {
  0: 'one',
  1: 'two',
  2: 'hybrid',
  3: 'list',
  5: 'three',
  6: 'swipe',
};

const LAYOUT_OPT = {
  type: 'two',
  itemMargin: 8,
  layoutMargin: 8,
};

export const BASE_LAYOUT_CONFTG = {
  type: 'goods',
  isSync: true,
  loading: false,
  items: [],
  triggerCreateInit: true,
  isSkyline: getIsSkyline(),
  useContainer: getUseContainer(),
};

// 全部商品配置
export const ALL_GOODS_CONFIG = {
  ...BASE_LAYOUT_CONFTG,
  itemCardOpt: ITEM_CARD_CONFIG,
  layoutOpt: LAYOUT_OPT,
  skeletonOpt: {
    two: SKELETON_OPT(),
  },
  isShowGoodsAddNumber: true,
};

// 商品分组配置
export const TAG_CONFIG = {
  ...BASE_LAYOUT_CONFTG,
  itemCardOpt: ITEM_CARD_CONFIG,
  layoutOpt: LAYOUT_OPT,
  isShowGoodsAddNumber: true,
};

// 搜索结果配置
export const SERACH_CONFIG = {
  ...BASE_LAYOUT_CONFTG,
  itemCardOpt: {
    ...ITEM_CARD_CONFIG,
    titleOpt: {
      ...TITLE_OPT,
      height: '48px',
      titleFontSize: 14,
    },
    layoutType: 'one',
    layout: LAYOUT_MAP.HORIZONTAL,
  },
  layoutOpt: {
    ...LAYOUT_OPT,
    type: 'one',
  },
  skeletonOpt: {
    default: {
      ...SKELETON_OPT({
        oneHeight: 16,
        twoWidth: 16,
        threeWidth: 73,
        threeMargin: '60px 0 12px 0',
        areaHeight: 0,
      }),
      layoutType: 'one',
      layout: LAYOUT_MAP.HORIZONTAL,
    },
  },
};

// 搜索结果中台化配置
export const SERACH_RANTA_CONFIG = {
  ...BASE_LAYOUT_CONFTG,
  itemCardOpt: ITEM_CARD_CONFIG,
  layoutOpt: {
    ...LAYOUT_OPT,
    type: 'two',
  },
  // skeletonOpt: {
  //   default: {
  //     ...SKELETON_OPT({
  //       oneHeight: 16,
  //       twoWidth: 16,
  //       threeWidth: 73,
  //       threeMargin: '60px 0 12px 0',
  //       areaHeight: 0,
  //     }),
  //     layoutType: 'one',
  //     layout: LAYOUT_MAP.HORIZONTAL,
  //   },
  // },
  skeletonOpt: {
    two: SKELETON_OPT(),
  },
  isShowGoodsAddNumber: true,
};

// 店铺榜单标签
export const SHOP_RANKING_MAP = {
  hot_sale: '销量榜',
  popular: '人气榜',
  new_arrival: '新品榜',
};
