{"name": "@wsc-shop/goods-list-all-page-setup", "extensionId": "@wsc-shop/goods-list-all-page-setup", "version": "0.0.1", "bundle": "<builtin>", "lifecycle": ["beforePageCreate", "beforePageMount", "onPageShow", "onPullDownRefresh", "onShareAppMessage", "onReachBottom", "onPageScroll", "pageDestroyed"], "framework": "weapp", "data": {"provide": {"cpsConfigKey": ["r", "w"], "kdtId": ["r", "w"], "shopMetaInfo": ["r", "w"], "shopBaseInfo": ["r", "w"], "shopBlockInfo": ["r", "w"], "showTab": ["r", "w"], "navBarTitle": ["r", "w"], "canShowNavBar": ["r", "w"], "listQuery": ["r", "w"], "totalAlias": ["r", "w"], "filters": ["r", "w"], "themeClass": ["r", "w"], "goodsOpt": ["r", "w"], "uuid": ["r", "w"], "goods_detail": ["r", "w"], "listType": ["r", "w"], "pageBgColor": ["r", "w"], "themeVars": ["r", "w"], "themeTagObj": ["r", "w"], "isSkyline": ["r"], "pageType": ["r"]}, "consume": {"themeCSS": ["r"], "themeTag": ["r"]}}, "process": {"define": ["handleItemClick", "showcaseHandleGoodsBuy", "handleFilterChange", "handleChangeLayoutType", "handleItemImageLoaded"], "invoke": ["autoEnterShop"]}, "event": {"emit": ["featureSku:show"]}, "widget": {"provide": ["GoodsListAllPageSetup"], "consume": ["SearchBar", "FilterBar", "ShareTab", "DcGoods", "ShopStatus", "ShopTopBar", "ShopBlock", "featureSku", "<PERSON>Footer"]}, "platform": ["weapp"]}