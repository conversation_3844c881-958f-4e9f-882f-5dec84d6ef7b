// @ts-nocheck
// import Event from '@youzan/weapp-utils/lib/event';
import { createStore, mapState, mapActions } from '@ranta/store';
import Toast from '@vant/weapp/dist/toast/toast';
import viewLog from '../../../mixins/logs-ranta';
import Salesman from '../../../mixins/salesman-ranta';
import GoodsPageSku from '../../../mixins/sku-ranta';
import CommonMixin from '../../../mixins/common';
import GoodsPageFilterBar from '../../../mixins/filter-bar-ranta';
import StickyControl from 'shared/utils/sticky-control';
// import getUniquePageKey from 'shared/utils/unique';
import { Hummer } from 'shared/utils/hummer';
import getAllGoodsParams from 'shared/utils/goods/all-goods-params';
import { formatNewList } from '../../../common/utils';
import { SIZE_TYPE, ALL_GOODS_CONFIG } from '../../../common/constants';
import set from 'utils/set';
import args from '@youzan/utils/url/args';

const app = getApp();
const defaultOrderBy = 'algPVD30';
// const loadingToast = {
//   duration: 0,
//   forbidClick: true,
//   message: '加载中',
//   loadingType: 'spinner',
// }

// 当前上下文 用于在ranta store里面调用一些mixin的方法
let context = null;

export default class {
  store = createStore({
    state: () => ({
      page: 1,
      themeClass: app.themeClass,
      loading: false,
      needLoadMore: true,
      isEmpty: false,
      // 搜索条的配置
      searchConfig: {
        border_style_color: '#fff',
        border_style_height: 40,
        border_style_method: '0',
        color: '#f9f9f9',
        hot_search_keys: [],
        hot_search_keys_new: [],
        position_show_method: '1',
        position_type: '0',
        show_search_component: '1',
        text_align_method: '0',
        text_color: '#999',
        type: 'search',
        z_index: 121,
      },
      // sku
      featureSkuData: null,
      // filter-bar
      order: '',
      orderBy: defaultOrderBy,
      listQuery: {},
      filterBarFixed: false,
      filterBarFixedTop: 0,
      filters: [
        { title: '综合', value: defaultOrderBy },
        { title: '上新', value: 'createdTime' },
      ],
      isLimitSold: false,
      limitSoldNum: 0,
      salesman: {},
      shareImgUrl: '',
      uuid: '',
      slgStatus: true,
      goodsOpt: ALL_GOODS_CONFIG,
      showTab: false,
      navBarTitle: '',
      canShowNavBar: false,
      totalAlias: [],
      id: '',
      __query__: '',
      route: '',
      firstPageList: null,
    }),
    actions: {
      setVal(obj) {
        Object.entries(obj).forEach(([key, value]) => {
          set(this, key, value);
        });
      },
      fetchProductList() {
        const { loading, orderBy, order, page } = this;

        if (loading) return;

        // Toast.loading(loadingToast)

        this.setVal({
          loading: true,
          'goodsOpt.loading': true,
        });
        const params = getAllGoodsParams({
          extra: {
            page,
            order_by: orderBy,
            ...(order ? { order } : {}),
          },
        });
        const prefetchKey = 'prefetch:all-goods';
        const prefetchApi = app.prefetchCache?.[prefetchKey]?.value;
        const getAllGoods =
          prefetchApi ||
          app.request({
            path: '/wscshop/weapp/goods/all-goods.json',
            query: params,
            method: 'GET',
            cacheConfig: {
              forceUpdate: false,
              expire: 60,
            },
          });
        return getAllGoods
          .then(({ showUmpAmbience, list: rs }) => {
            const currentList = +page === 1 ? [] : this.goodsOpt.items;
            const currentAlias = rs.map((item) => item.alias);
            const totalAlias = this.totalAlias.concat(currentAlias);
            const showTab = totalAlias.length >= 20;
            const { isLimitSold, limitSoldNum } = this;
            const {
              listData,
              page: newPage,
              needLoadMore,
              isEmpty,
              list,
            } = formatNewList(rs, {
              page,
              pageSize: params.pageSize,
              isLimitSold,
              limitSoldNum,
              currentList,
              spmType: 'ag',
              pageType: 'goods',
              layout: 1,
              isUmpInfoShow: !!+showUmpAmbience,
              soldOutGoodsImg: this.soldOutGoodsImg,
            });

            // 区分首次和下拉加载更多返回的数据 返回的数据格式不一样
            let res = this.goodsOpt.items || [];
            if (listData['goodsOpt.items']) {
              // 首次
              this.firstPageList = [...listData['goodsOpt.items']];
              // 优先展示10个 首屏渲染更快,前10个渲染完成后再渲染剩余
              res = this.firstPageList.slice(0, 10);
            } else {
              // 下拉
              res = res.concat(Object.values(listData));
            }

            this.setVal({
              page: newPage,
              needLoadMore,
              isEmpty,
              loading: false,
              'goodsOpt.loading': false,
              'goodsOpt.itemCardOpt.type':
                this.goodsOpt.layoutOpt.type === 'two'
                  ? SIZE_TYPE[+showUmpAmbience]
                  : 'list',
              showTab,
              totalAlias,
              canShowNavBar: true,
              listQuery: params,
              ...listData,
              'goodsOpt.items': res,
            });
            setTimeout(() => {
              Hummer.markRendered('fs');
            }, 0);
            // 获取到slg后，触发enterpage埋点上报：每个商品返回的slg是一致的
            context.triggerLogger(list[0]?.extraInfo?.slg || '0', 'ag');
            context.getSalesmanCard(list);
            context.setGoodsViewLog(list, 'allpro');
          })
          .catch((response) => {
            this.setVal({
              loading: false,
              'goodsOpt.loading': false,
            });
            Toast(response?.msg || '没有找到商品哦～');
            // 处理接口异常时，enterpage也需埋点上报
            context.triggerLogger('0', 'ag');
          })
          .finally(() => {
            // 删除dmc提早请求接口
            if (prefetchApi) {
              delete app.prefetchCache[prefetchKey];
            }
            // 页面白屏延迟消失
            // setTimeout(() => {
            // Toast.clear();
            // }, 500);
          });
      },
      handleItemClick(e) {
        context.handleItemClick(e);
      },
      showcaseHandleGoodsBuy(e) {
        context.showcaseHandleGoodsBuy(e);
      },
      handleFilterChange(e) {
        context.handleFilterChange(e);
      },
      handleItemImageLoaded() {
        app.logger.log({
          et: 'view',
          ei: 'view_list_goods',
          en: '列表商品曝光',
        });

        this.setFirstPageRender();
      },
      handleRootTap(e) {
        context.handleRootTap(e);
      },
      setFirstPageRender() {
        if (this.firstPageList) {
          this.setVal({
            'goodsOpt.items': this.firstPageList,
          });
          this.firstPageList = null;
        }
      },
    },
  });

  constructor({ ctx }) {
    ctx.store = this.store;
    context = this;
    this.ctx = ctx;
    Object.assign(
      this,
      mapState(
        this,
        [
          // 'page',
          'themeClass',
          'loading',
          'needLoadMore',
          'filters',
          'shareImgUrl',
          'uuid',
          'slgStatus',
          'goodsOpt',
          'showTab',
          'listQuery',
          'navBarTitle',
          'canShowNavBar',
          'totalAlias',
          '__query__',
          'route',
          'id',
        ],
        {
          setSelf: true,
        }
      ),
      mapActions(this, ['setVal', 'fetchProductList', 'handleItemImageLoaded']),
      viewLog,
      GoodsPageSku,
      GoodsPageFilterBar,
      Salesman,
      CommonMixin
    );
    this.ctx.data.themeVars =
      'padding-bottom: constant(env(safe-area-inset-bottom) + 50px);z-index: 141;';
    this.ctx.data.shopBlockInfo = {
      showServiceDue: false,
      showStoreSwitch: false,
      showShopStatus: false,
      switchPageOptions: {},
      buyerAddress: '',
      isRetailShelf: false,
      navigationbarConfigData: {},
    };

    this.ctx.data.pageType = 'goods-list';
    this.store.watch('showTab', (val) => {
      this.ctx.data.showTab = val;
    });
    this.ctx.watch('themeTag', (value) => {
      this.ctx.data.themeTagObj = value;
    });

    const skylineInfo = wx.getSkylineInfoSync();
    this.ctx.data.isSkyline = skylineInfo?.isSupported;
  }

  beforePageCreate() {
    this.commonBeforePageCreate({
      slData: {
        sst: 7,
      },
    });
  }

  /**
   * 生命周期函数--监听页面加载
   */
  beforePageMount(options) {
    app.checkReLaunchShopSelect();
    // Toast.loading(loadingToast)
    const query = this.ctx.env.getQuery() || {};
    const opt = {
      redirectUrl: args.add(this.ctx.env.route, query),
      logTag: 'goods-list-all',
    };
    const [promise] = this.ctx.process.invoke('autoEnterShop', query, opt);
    promise.then(() => {
      this.setVal({
        id: app.getKdtId(),
        __query__: options.query,
        route: options.route,
      });
      context.getUUid();
      this.filterBarOnLoad();
      this.skuOnLoad();
      this.fetchProductList();
      this.commonOnLoad();
    });
  }

  onPageShow() {
    this.filterBarSticky();
    this.commondOnShow();
  }

  onPageHide() {
    this.commonOnHide();
  }

  pageDestroyed() {
    this.commonOnUnload();
  }

  filterBarSticky() {
    StickyControl.releaseStickyControl();
    // z-index 顺序 修改请查看 docs/z-index.md
    StickyControl.setStickyControlCheckItem('searchTop', 121);
    StickyControl.setStickyControlSubscribe(
      this.handelScrollTop,
      this,
      'FilterBar'
    );
  }

  handelScrollTop({ type, stickyTop = 0 }) {
    if (type === 'stickyTop') {
      this.setVal({
        filterBarFixedTop: stickyTop,
      });
    }
  }

  handleTabScroll(event) {
    let { isFixed } = event.detail;

    if (isFixed) {
      if (!StickyControl.checkStickyControlItem('FilterBar')) {
        isFixed = false;
      }
    }

    StickyControl.setStickyControlItem('FilterBar', 42, isFixed);

    const { positionTop = 0 } =
      StickyControl.getStickyControlItem('FilterBar') || {};

    this.setVal({
      filterBarFixedTop: positionTop,
      filterBarFixed: isFixed,
    });
  }

  // 增加分享钩子
  onShareAppMessage(e) {
    const sharePath = '/packages/goods-list/all/index';
    const result = {
      path: sharePath,
      title: '全部商品',
    };
    if (this.shareImgUrl) {
      result.imageUrl = this.shareImgUrl;
    }
    return this.getShareData(result, { extra: e });
  }
}
