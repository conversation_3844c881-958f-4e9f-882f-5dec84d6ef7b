<!-- packages/goods-list/all/index -->
<scroll-view type="list" scroll-y scrollTop="{{scrollTop}}" style="height:100vh;" bindscrolltolower="onReachBottom" bindscroll="onPageScroll">
  <view class="goods-list-all__wrap" style="{{ (goodsOpt.layoutOpt.type === 'list' ? ' background: #fff' : 'background: linear-gradient(180deg, #FFF 20vh, #F5F5F5 50vh)') }}" bindtap="handleRootTap">
    <!-- offset-top hack 解决 scroll-view bug -->
    <van-sticky disabled="{{false}}" offset-top="{{1}}">
      <share-tab />
      <search-bar />
      <filter-bar isShowLayoutChange />
    </van-sticky>
    <shop-block />
    <block wx:if="{{ !isEmpty }}">
      <dc-goods />
    </block>
    <block wx:else>
      <view class="all-goods__empty">
        <image class="all-goods__empty-image" src="https://img.yzcdn.cn/v2/image/wap/trade/new_order/<EMAIL>"></image>
        <text class="all-goods__empty-text">没有发现商品，去首页逛逛吧</text>
      </view>
    </block>
    <view wx:if="{{loading}}" style="text-align: center;">
      <van-loading type="spinner" />
    </view>
    <van-toast id="van-toast" />
    <van-notify id="van-notify" />
    <shop-pop-manager source="{{ 5 }}" />
    <custom-tab-bar />
    <!-- 原来page-container的内容 -->
    <bi-icon />
    <protocol z-index="{{ protocolIndex }}" noAutoAuth="{{ noAutoAuth }}" />
    <!-- 原来page-container的内容 -->
    <page-footer wx:if="{{ goodsOpt.items.length }}"/>
  </view>
</scroll-view>