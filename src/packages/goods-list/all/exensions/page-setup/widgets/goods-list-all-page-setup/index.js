import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import Event from '@youzan/weapp-utils/lib/event';
import TeeEvent from '@youzan/tee-event';
import getUniquePageKey from 'shared/utils/unique';
import { getCurrentPage } from 'shared/common/base/wsc-component';
import pageComponentBehavior from '@/custom-tab-bar-v2/native-helper/page-component-behavior';

RantaWidget({
  behaviors: [pageComponentBehavior],
  data: {
    protocolIndex: 10001,
    noAutoAuth: false,
    scrollTop: 0,
  },
  attached() {
    mapState(this, ['needLoadMore', 'loading', 'isEmpty', 'goodsOpt']);
    mapActions(this, ['setVal', 'fetchProductList', 'handleRootTap']);
    TeeEvent.on('pageScrollTo', (options) => {
      if (options.scrollTop === 0) {
        this.setYZData({
          scrollTop: this.data.scrollTop === 0 ? 1 : options.scrollTop,
        });
      }
    });
  },
  detached() {
    TeeEvent.off('pageScrollTo');
  },
  methods: {
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
      console.log('goods-list-all onReachBottom');
      const { needLoadMore, loading } = this.data;
      if (!needLoadMore) {
        Event.trigger('float-share:show' + getUniquePageKey(this));
      }

      if (!needLoadMore && !loading) {
        return;
      }
      this.fetchProductList();
    },
    /**
     * 滚动事件 触发 使用同一个Event 实例
     * @param e
     */
    onPageScroll(e) {
      if (e.scrollTop <= 0) return;
      Event.trigger('onPageScroll' + getUniquePageKey(this), e);
      getCurrentPage().onPageScroll(e.detail);
    },
  },
});
