@import '/components/showcase/index.wxss';

.goods-list-all__wrap {
  min-height: 100vh;
  padding-bottom: 100px;
  overflow: hidden;
}
.showcase-all-goods--fixed .filter-bar-wrap {
  transition: linear top 300ms;
}

.filter-bar-wrap {
  width: 100%;
}

.all-goods__empty {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  width: 100%;
  text-align: center;
}

.all-goods__empty-image {
  width: 80px;
  height: 84px;
  display: block;
  margin: 0 auto 15px;
}

.all-goods__empty-text {
  font-size: 14px;
  line-height: 28px;
  color: #a9a9a9;
}
