// @ts-nocheck
// import Event from '@youzan/weapp-utils/lib/event';
import { createStore, mapState, mapActions } from '@ranta/store';
import Toast from '@vant/weapp/dist/toast/toast';
import viewLog from '../../../mixins/logs-ranta';
import Salesman from '../../../mixins/salesman-ranta';
import appEvent from 'shared/utils/app-event';
import GoodsPageSku from '../../../mixins/sku-ranta';
import GoodsPageFilterBar from '../../../mixins/filter-bar-ranta';
import CommonMixin from '../../../mixins/common';
// import getUniquePageKey from 'shared/utils/unique';
// import SaasPageConfig from "../../saas/page-config";
import { getExhibitionMoreTag, updateExhibitionTag } from '../../saas/utils';
// import { initSalesman } from 'shared/utils/salesman-share';
import { bridge } from '@youzan/ranta-helper';
import { getSearchResultParams } from 'shared/utils/prefetch-params';
import { formatNewList } from '../../../common/utils';
import { SERACH_RANTA_CONFIG } from '../../../common/constants';
// import yunDesign from "../../design.json";
import set from 'utils/set';

const app = getApp();
// let extendPageConfig = {};
/* #ifdef BUILD_ENV=youzanyun */
// extendPageConfig = SaasPageConfig;
/* #endif */

const SEARCH_STATUS = {
  HAS_RESULT: 1,
  NO_RESULT: 0,
};

const SEARCH_HISTORY = 'search-history';

// 当前上下文 用于在ranta store里面调用一些mixin的方法
let context = null;

export default class {
  // getExhibitionMoreTag(params) {
  //   return updateExhibitionTag(params);
  // }

  store = createStore({
    state: () => ({
      page: 1,
      themeClass: app.themeClass,
      loading: false,
      needLoadMore: false,
      exhibitionTag: false,
      inputValue: '',
      keyword: '',
      type: '',
      focused: false,
      isLimitSold: false,
      limitSoldNum: 0,
      filters: [
        { title: '综合', value: '' },
        { title: '上新', value: 'createdTime' },
      ],
      orderBy: '',
      order: '',
      isEmpty: false,
      hotSearchKeys: [],
      /** 新增热词和历史搜索展示，控制展示商品或者展示热词的状态 */
      isShowProducts: true,
      searchHistory: [],
      readyFocused: false,
      fetchListByHotSearch: true,
      isSupportGoodsRecommend: false,
      recommendHasData: true,
      // 是否展示个性化推荐组件，开始不加载
      showRecommend: false,
      shareImgUrl: '',
      uuid: '',
      slgStatus: true,
      slg: '',
      hot: '',
      salesman: {},
      goodsOpt: SERACH_RANTA_CONFIG,
      design: [],
      showShareFloating: false,
      listQuery: {},
      totalAlias: [],
      __query__: '',
      route: '',
    }),
    actions: {
      setVal(obj) {
        Object.entries(obj).forEach(([key, value]) => {
          set(this, key, value);
        });
      },
      fetchProductList() {
        const {
          order,
          loading,
          fetchListByHotSearch,
          hotSearchKeys,
          type,
          orderBy,
          exhibitionTag,
        } = this;

        // eslint-disable-next-line prefer-const
        let { page, keyword } = this;

        if (loading) return;

        // 首次搜索的时候，没有输入并且配置了搜索词
        if (fetchListByHotSearch && !keyword && hotSearchKeys.length) {
          keyword = hotSearchKeys[0];
          this.setVal({
            keyword,
            inputValue: keyword,
            fetchListByHotSearch: false,
          });
        }

        wx.showToast({
          title: '加载中',
          icon: 'loading',
        });

        this.setVal({
          loading: true,
          'goodsOpt.loading': true,
          isEmpty: false,
        });

        const params = getSearchResultParams({
          exhibitionTag,
          type,
          keyword,
          extra: order ? { order } : {},
        });

        app
          .request({
            path: '/wscshop/showcase/goods_search/goods.json',
            method: 'GET',
            query: {
              ...params,
              page,
              order_by: orderBy,
              supportFixGroupOptionalCombo: true,
            },
            cacheConfig: {
              forceUpdate: false,
              expire: 60,
            },
          })
          .then((response) => {
            const currentList = page === 1 ? [] : this.goodsOpt.items;
            const currentAlias = response.map((item) => item.alias);
            const showShareFloating =
              page === 1 ? (response || []).length >= 20 : true;
            const { isLimitSold, limitSoldNum } = this;
            const {
              page: newPage,
              needLoadMore,
              isEmpty,
              list,
            } = formatNewList(response, {
              page,
              pageSize: params.page_size,
              isLimitSold,
              limitSoldNum,
              currentList,
              spmType: 'search',
              pageType: 'search',
              layout: 3,
              isUmpInfoShow: false,
              needFormatPrice: false,
              soldOutGoodsImg: this.soldOutGoodsImg,
            });

            const newTotalAlias = this.totalAlias.concat(currentAlias);
            const firstGoods = list[0] || {};
            const slg = firstGoods.extraInfo?.slg || '0';
            const showRecommend = isEmpty || !needLoadMore;
            // 获取到slg后，触发enterpage埋点上报：每个商品返回的slg是一致的
            context.triggerLogger(slg, 'search');
            context.getSalesmanCard(list);

            wx.hideToast();
            const resList = currentList.concat(list);
            this.setVal({
              loading: false,
              isShowProducts: true,
              needLoadMore,
              isEmpty,
              page: newPage,
              'goodsOpt.loading': false,
              showRecommend,
              slg,
              showShareFloating,
              listQuery: {
                ...params,
                page,
                order_by: orderBy,
              },
              totalAlias: newTotalAlias,
              'goodsOpt.items': resList,
            });

            // 搜索结果日志
            app.logger.log({
              et: 'callback',
              ei: 'search_callback',
              en: '搜索反馈',
              params: {
                status:
                  response.length > 0
                    ? SEARCH_STATUS.HAS_RESULT
                    : SEARCH_STATUS.NO_RESULT,
                words: keyword,
                goods_id: response.map((goods) => goods?.actionOpt?.id),
              },
            });
            context.setGoodsViewLog(list, 'search');
          })
          .catch((response) => {
            console.log('response', response);
            wx.hideToast();
            // 处理接口异常时，enterpage也需埋点上报
            if (this.slgStatus) {
              appEvent.trigger('goodsTag:loaded', {
                slg: '0',
              });
            }
            this.setVal({
              loading: false,
              'goodsOpt.loading': false,
              isShowProducts: true,
              showRecommend: true,
              slgStatus: false,
            });
            Toast(response.msg || '没有找到商品哦～');
          });
      },
      handleItemClick(e) {
        context.handleItemClick(e);
      },
      showcaseHandleGoodsBuy(e) {
        context.showcaseHandleGoodsBuy(e);
      },
      handleFilterChange(e) {
        context.handleFilterChange(e);
      },
      handleItemImageLoaded() {},
      handleRootTap(e) {
        context.handleRootTap(e);
      },
      setGoodsViewLog(res, type) {
        context.setGoodsViewLog(res, type);
      },
    },
  });

  constructor({ ctx }) {
    ctx.store = this.store;
    context = this;
    this.ctx = ctx;
    Object.assign(
      this,
      mapState(
        this,
        [
          'themeClass',
          'needLoadMore',
          'keyword',
          'filters',
          'searchHistory',
          'readyFocused',
          'shareImgUrl',
          'uuid',
          'slgStatus',
          'slg',
          'salesman',
          'goodsOpt',
          'listQuery',
          'totalAlias',
          'listType',
          'hot',
          '__query__',
          'route',
        ],
        {
          setSelf: true,
        }
      ),
      mapActions(this, ['setVal', 'fetchProductList', 'handleItemImageLoaded']),
      viewLog,
      GoodsPageSku,
      GoodsPageFilterBar,
      Salesman,
      CommonMixin
      // extendPageConfig
    );
    this.ctx.data.shopBlockInfo = {
      showServiceDue: false,
      showStoreSwitch: false,
      showShopStatus: false,
      switchPageOptions: {},
      buyerAddress: '',
      isRetailShelf: false,
      navigationbarConfigData: {},
    };
    this.ctx.data.showTab = false;
    this.ctx.data.canShowNavBar = true;
    this.ctx.data.navBarTitle = '商品搜索';
    this.ctx.data.recommendBizName = 'search~rec';
    this.ctx.data.layoutConfig = {
      textStyleType: 2,
      borderRadiusType: 2,
      showCornerMark: false,
    };
    this.store.watch('searchText', (val) => {
      this.ctx.data.searchText = val;
    });
    this.store.watch('slg', (val) => {
      this.ctx.data.slg = val;
    });
    this.ctx.watch('themeTag', (value) => {
      this.ctx.data.themeTagObj = value;
    });
    this.store.watch('showShareFloating', (val) => {
      this.ctx.data.showShareFloating = val;
    });
    const skylineInfo = wx.getSkylineInfoSync();
    this.ctx.data.isSkyline = skylineInfo?.isSupported;
    this.ctx.data.pageType = 'search-result';
  }

  /**
   * getExhibitionMoreTag
   * @desc 是否显示多标签
   */
  @bridge('getExhibitionMoreTag', 'process')
  getExhibitionMoreTag(params) {
    return updateExhibitionTag(params);
  }

  beforePageCreate() {
    this.commonBeforePageCreate({
      slData: {
        sst: 9,
      },
    });
  }

  /**
   * 生命周期函数--监听页面加载
   */
  beforePageMount(options) {
    // this.sassOnLoad();
    this.filterBarOnLoad();
    this.skuOnLoad();
    this.commonOnLoad();
    // 增加云配置 过滤config
    // const design = (yunDesign?.design || []).filter((d) => d.type !== "config");

    this.setVal({
      // design,
      exhibitionTag: getExhibitionMoreTag.type,
      __query__: options.query,
      route: options.route,
    });

    app.checkReLaunchShopSelect();
    // 从热门搜索跳转进来，直接进行搜索
    if (options.query.q) {
      const decodeQuery = decodeURIComponent(options.query.q);
      this.setVal({
        keyword: decodeQuery,
        inputValue: decodeQuery,
        isShowProducts: true,
        type: options.query.type,
        hot: options.query.hot,
      });

      this.fetchProductList(); // options.query.searchType，用于支持扫码购搜索
    } else {
      this.setVal({
        readyFocused: true,
        isShowProducts: false,
      });
    }
    // initSalesman.call(this, { sst: 9 });
    // 所有热词都从外部传入进来
    try {
      if (options.query.hot) {
        const hotSearchKeys = JSON.parse(decodeURIComponent(options.query.hot));
        this.setVal({
          hotSearchKeys,
        });
      }
    } catch (e) {}

    const history = wx.getStorageSync(SEARCH_HISTORY);
    if (history) {
      this.setVal({
        searchHistory: history,
      });
    }
    this.getUUid();

    this.fetchGoodsRecommend();
    if (this.readyFocused) {
      this.setVal({
        focused: true,
      });
    }

    console.log('this', this);
    console.log('app', getApp());
  }

  onPageShow() {
    this.commondOnShow();
  }

  onPageHide() {
    this.commonOnHide();
  }

  pageDestroyed() {
    this.commonOnUnload();
  }

  // /**
  //  * 页面上拉触底事件的处理函数
  //  */
  // onReachBottom() {
  //   console.log('ssssad', this.needLoadMore);
  //   if (!this.needLoadMore) {
  //     Event.trigger('float-share:show' + getUniquePageKey(this));
  //     return;
  //   }
  //   this.fetchProductList();
  // }

  // /**
  //  * 滚动事件 触发 使用同一个Event 实例
  //  * @param e
  //  */
  // onPageScroll(e) {
  //   if (e.scrollTop <= 0) return;
  //   Event.trigger('onPageScroll' + getUniquePageKey(this), e);
  // }

  fetchGoodsRecommend() {
    app
      .request({ path: '/wscshop/showcase/goods_search/recommend.json' })
      .then((res) => {
        const { goods_recommend_search_result: recommend = '0' } = res;
        if (+recommend === 1) {
          this.setVal({
            isSupportGoodsRecommend: true,
          });
        }
      });
  }

  // 用户点击右上角分享
  onShareAppMessage(e) {
    const { keyword, hot } = this;
    console.log(keyword, hot);
    const result = {
      path: `/packages/goods-list/search-result/index?q=${keyword}&hot=${hot}`,
    };
    if (this.shareImgUrl) {
      result.imageUrl = this.shareImgUrl;
    }
    return this.getShareData(result, { extra: e });
  }
}
