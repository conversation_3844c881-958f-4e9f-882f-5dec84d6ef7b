{"name": "@wsc-shop/search-result-page-setup", "extensionId": "@wsc-shop/search-result-page-setup", "version": "0.0.1", "bundle": "<builtin>", "lifecycle": ["beforePageCreate", "beforePageMount", "onPageShow", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "onPageScroll"], "framework": "weapp", "data": {"provide": {"cpsConfigKey": ["r", "w"], "kdtId": ["r", "w"], "shopMetaInfo": ["r", "w"], "shopBaseInfo": ["r", "w"], "shopBlockInfo": ["r", "w"], "showTab": ["r", "w"], "navBarTitle": ["r", "w"], "canShowNavBar": ["r", "w"], "listQuery": ["r", "w"], "totalAlias": ["r", "w"], "showShareFloating": ["r", "w"], "filters": ["r", "w"], "themeClass": ["r", "w"], "goodsOpt": ["r", "w"], "uuid": ["r", "w"], "goods_detail": ["r", "w"], "listType": ["r", "w"], "searchText": ["r", "w"], "slg": ["r", "w"], "pageBgColor": ["r", "w"], "themeTagObj": ["r", "w"], "recommendBizName": ["r", "w"], "layoutConfig": ["r", "w"], "isSkyline": ["r"], "pageType": ["r"]}, "consume": {"themeCSS": ["r"], "themeTag": ["r"]}}, "process": {"define": ["handleItemClick", "showcaseHandleGoodsBuy", "handleFilterChange", "handleChangeLayoutType", "handleItemImageLoaded"]}, "event": {"emit": ["featureSku:show"]}, "widget": {"provide": ["SearchResultTop", "SearchResultBottom", "EmptyBlock", "SearchResultPageSetup"], "consume": ["FilterBar", "DcGoods", "ShopPopManager", "ShopBlock", "featureSku", "ShareTab", "GoodsRecommend"]}, "component": {"consume": ["VanList"]}, "platform": ["weapp"]}