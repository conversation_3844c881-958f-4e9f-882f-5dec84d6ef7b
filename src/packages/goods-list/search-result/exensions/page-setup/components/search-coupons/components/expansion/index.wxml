<view class="search-coupons-expansion">
  <view class="expansion-header">
    <view class="vacant"></view>
    <view class="expansion-title">
      <view>送你1张惊喜券，买“</view>
      <view class="serach-text">{{ searchText }}</view>
      <view>”相关商品可用</view>
    </view>
    <view class="expansion-close" bindtap="putawayHandle">
      <van-icon name="cross" color="#969799" />
    </view>
  </view>
  <view wx:if="{{ couponData.name && couponStatus === 'unaccalimed' }}" class="expansion-content">
    <coupon theme-class="{{ themeClass }}" coupon="{{ couponData }}" btn-icon="{{ false }}">
      <view slot="btnPrefix">
        <user-authorize bind:next="receivedHandle" scene="get_coupon">
          <theme-view custom-class="expansion-btn" bg="var(--ump-icon)" color="main-text">立即领取</theme-view>
        </user-authorize>
      </view>
    </coupon>
  </view>
  <view wx:if="{{ couponData.name && couponStatus === 'received' }}" class="expansion-content">
    <!-- <coupon theme-class="{{ themeClass }}" coupon="{{ couponData }}" btn-icon="{{ false }}" bottom-right-icon="{{ couponStatus === 'received' }}" btn-text="立即使用" bind:click-btn="useHandle"></coupon> -->
    <theme-view custom-class="expansion-coupon" border="var(--ump-border)" bg="var(--ump-coupon-bg)">
      <view class="expansion-coupon__box">
        <view class="expansion-coupon__block expansion-coupon-denomination">
          <view class="coupon-denomination">
            <theme-view custom-class="denomination-value" color="var(--ump-icon)">{{ couponData.value}}</theme-view>
            <theme-view custom-class="denomination-unit" color="var(--ump-icon)">{{ couponData.unit}}</theme-view>
          </view>
        </view>
        <view class="expansion-coupon-content">
          <view class="coupon-content-name">{{ couponData.name }}</view>
          <view class="coupon-content-desc" wx:if="{{ couponData.validTime }}">{{ couponData.validTime}}</view>
          <view class="coupon-content-countdown" wx:else>
            <view class="coupon-countdown-text">距到期仅剩</view>
            <van-count-down
              use-slot
              time="{{ couponData.validEndTime - curTime }}"
              bind:change="onChange"
              bind:finish="onFinished">
              <text class="coupon-countdown-time" wx:if="{{ timeData.days > 0 }}">{{ timeData.days }}</text>
              <text class="coupon-countdown-time time-day" wx:if="{{ timeData.days > 0 }}">天</text>
              <text class="coupon-countdown-time">{{ timeData.hours }}:</text>
              <text class="coupon-countdown-time">{{ timeData.minutes }}:</text>
              <text class="coupon-countdown-time">{{ timeData.seconds }}</text>
            </van-count-down>
          </view>
        </view>
        <view class="expansion-coupon__block expansion-coupon-action">
          <theme-view custom-class="coupon-content-btn" bg="var(--ump-main-bg)" color="var(--ump-main-text)" bindtap="useHandle">立即使用</theme-view>
        </view>
        <view wx:if="{{themeClass}}" class="received-icon">
          <view class="received-icon-out">
            <view class="received-icon-inner">已领</view>
          </view>
        </view>
      </view>
    </theme-view>
  </view>
  <view class="expansion-footer">{{ countDownNum }}秒后自动收起</view>
</view>