<view class="search-coupons {{ iPhoneX && !isClose ? 'iPhone-X' : '' }}">
  <view wx:if="{{ show }}" class="not-open {{ isPutaway ? 'putaway' : '' }} {{ isClose ? 'close' : '' }} {{ isExpansion ? 'expansion' : '' }}">
    <expansion
      theme-class="{{ themeClass }}"
      wx:if="{{ !isPutaway && !isClose && isGetCoupon }}"
      bindonstatus="putawayHandle"
      bindreceived="receivedHandle"
      binduse="useHandle"
      searchText="{{ searchText }}"
      couponData="{{ couponData }}"
      couponStatus="{{ couponStatus }}"
      logParams="{{ logParams }}"
      countDownNum="{{ countDownNum }}"
    ></expansion>
    <putaway
      wx:if="{{ isPutaway && !isClose && isGetCoupon }}"
      bindonstatus="closeHandle"
      bindreceived="receivedHandle"
      binduse="useHandle"
      couponData="{{ couponData }}"
      couponStatus="{{ couponStatus }}"
      logParams="{{ logParams }}"
    ></putaway>
  </view>
  <van-toast id="van-toast" />
</view>