.search-coupons-expansion {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.expansion-header {
  padding: 16px 0 8px;
  height: 20px;
  line-height: 20px;
  display: flex;
  justify-content: center;
  font-size: 14px;
}

.vacant {
  flex: auto;
}

.expansion-title {
  width: 300px;
  display: flex;
  justify-content: center;
}

.serach-text {
  max-width: 74px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--ump-icon)
}

.expansion-close {
  flex: auto;
  line-height: 24px;
  padding-left: 6px;
}

.expansion-content {
  align-items: center;
  width: 343px;
  height: 100px;
  margin: 16px auto 0;
}

.expansion-btn {
  display: flex;
  align-items: center;
  padding: 0 6px;
  height: 20px;
  line-height: 20px;
  justify-content: center;
  border-radius: var(--theme-radius-button, 12px);
}

.expansion-footer {
  text-align: center;
  height: 32px;
  line-height: 32px;
  font-size: 12px;
  color: #969799;
}

.expansion-coupon {
  border-width: 1px;
  border-style: solid;
  border-radius: var(--theme-radius-card, 4px);
  width: inherit;
  margin-bottom: 12px;
  overflow: hidden;
  font-size: 12px;
  transition: height 0.2s linear;
  box-shadow: 0 2px 6px 1px rgba(0, 0, 0, 0.06);
}

.expansion-coupon__box {
  display: flex;
  width: 100%;
  align-items: center;
  position: relative;
}

.expansion-coupon__block {
  position: relative;
  display: flex;
  align-items: center;
}

.coupon-denomination {
  display: flex;
  align-items: baseline;
}

.expansion-coupon-denomination {
  flex: none;
  box-sizing: border-box;
  width: 112px;
  height: inherit;
  padding: 16px 12px;
  overflow: hidden;
}

.expansion-coupon-content {
  flex: 1;
  max-width: 100%;
  padding: 22px 8px 16px 0;
  color: #323233;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.expansion-coupon-action {
  flex: none;
  height: inherit;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: 76px;
  padding-right: 12px;
  box-sizing: border-box;
  justify-content: flex-start;
  position: relative;
}

.denomination-value {
  font-size: 29px;
  font-weight: 600;
}

.denomination-unit {
}

.coupon-content-name {
  font-size: 14px;
  overflow: hidden;
  color: #323233;
  font-weight: bold;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 12px;
  text-align: left;
}

.coupon-content-desc {
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-align: left;
}

.coupon-content-countdown {
  font-size: 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.coupon-countdown-text {
  color: #fa1919;
  font-size: 12px;
  margin-right: 4px;
}
.coupon-countdown-time {
  display: inline-flex;
  color: #fa1919;
  font-size: 12px;
}

.coupon-content-btn {
  flex: 1;
  padding: 0;
  font-size: 12px;
  width: 64px;
  height: 20px;
  line-height: 20px;
  border-radius: var(--theme-radius-button, 12px);
  text-align: center;
  background-color: inherit;
  color: inherit;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  left: 0;
  z-index: 1;
}

.received-icon {
  width: 44px;
  height: 44px;
  position: absolute;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.received-icon-out {
  position: relative;
  left: 5px;
  box-sizing: content-box;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  border: 2px solid;
  opacity: 0.4;
  border-color: var(--ump-icon, #323233);
}
.received-icon-inner {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 0.5px dotted;
  transform: rotate(-45deg);
  font-size: 12px;
  font-weight: bold;
  border-color: var(--ump-icon, #323233);
  background: var(--ump-tag-bg, #f2f2ff);
  color: var(--ump-icon, #323233);
}
