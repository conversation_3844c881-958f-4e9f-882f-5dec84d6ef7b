import WscComponent from 'pages/common/wsc-component/index';
import getAuthorizedState from 'shared/utils/get-authorized-state';
import { moment } from '@youzan/weapp-utils/lib/time';
import { jumpLink } from '@goods/utils';

const app = getApp();
const couponTypes = {
  0: 'card', // 优惠券
  1: 'code', // 优惠码
};
const errorMsg = '领取失败';
WscComponent({
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持
  },
  properties: {
    searchText: String,
    goodsList: Array,
    slg: String,
  },
  data: {
    show: true,
    isPutaway: false,
    isClose: false,
    isExpansion: false,
    isGetCoupon: false,
    themeClass: 'th0',
    isAuthorized: false,
    couponStatus: 'unaccalimed', // 券状态：unaccalimed 未领取，received 已领取
    couponData: {},
    couponId: 0,
    couponType: 0,
    alias: 0,
    logParams: {},
    countDownNum: 10,
    iPhoneX: false,
  },
  async attached() {
    const authorizedState = await getAuthorizedState();
    const isAuthorized =
      authorizedState.mobile && authorizedState.nicknameAndAvatar;
    const shopTheme = await app.getShopTheme();
    this.setYZData({
      themeClass: shopTheme.themeClass,
      isAuthorized,
      iPhoneX: app.deviceType === 'iPhone-X',
    });
    this.showCouponHandle();
  },
  methods: {
    // 埋点
    setLogHandle(log) {
      app.logger && app.logger.log(log);
    },
    // 弹窗曝光log
    getViewLog() {
      return {
        et: 'view', // 事件类型
        ei: 'view', // 事件标识
        en: '搜索发券弹窗曝光', // 事件名称
        params: this.data.logParams,
      };
    },
    // 立即领取按钮点击log
    getReceiveLog() {
      return {
        et: 'click', // 事件类型
        ei: 'click_receive', // 事件标识
        en: '立即领取按钮点击', // 事件名称
        params: this.data.logParams,
      };
    },
    // 去使用按钮点击log
    getUseLog() {
      return {
        et: 'click', // 事件类型
        ei: 'click_use', // 事件标识
        en: '去使用按钮点击', // 事件名称
        params: this.data.logParams,
      };
    },
    putawayHandle() {
      this.setYZData({
        isPutaway: true,
      });
    },
    closeHandle() {
      this.setYZData({
        isClose: true,
      });
    },
    updateCouponData(data, status, show = false) {
      const couponData = {
        value: data.value_copywriting || this.data.couponData.value,
        unit: data.unit_copywriting || this.data.couponData.unit,
        name: data.use_threshold_copywriting || this.data.couponData.name,
      };
      const timeFormat = 'YYYY.MM.DD';
      // 未领取数据源
      if (status === 'unaccalimed') {
        const startTime = data.start_time;
        const endTime = data.end_time;
        couponData.validTime = `${moment(startTime, timeFormat)}-${moment(
          endTime,
          timeFormat
        )}`;
      }

      // 已领取数据源
      if (status === 'received' && data.validEndTime) {
        const startTime = data.validStartTime;
        const endTime = data.validEndTime;
        couponData.validTime = `${moment(startTime, timeFormat)}-${moment(
          endTime,
          timeFormat
        )}`;
        couponData.validEndTime = data.validEndTime;
        if (data.validEndTime - +new Date() <= 48 * 60 * 60 * 1000) {
          delete couponData.validTime;
        }
      }
      this.setYZData({
        couponData,
        couponStatus: status,
      });
      if (show) {
        setTimeout(() => {
          this.setYZData(
            {
              isExpansion: true,
              isGetCoupon: true,
            },
            500
          );
        });
      }
    },
    showCouponHandle() {
      // 无搜索关键字，不请求、不展示优惠券
      if (!this.data.searchText) {
        return;
      }
      this.getCouponData();
    },
    // 获取券信息
    getCouponData() {
      const goodsIdList = [];
      this.data.goodsList.forEach((item) => {
        goodsIdList.push(item?.actionOpt?.loggerParams?.goods_id);
      });
      const rmd = 'search_rcmd';
      const fetchParams = {
        goodsIdList: goodsIdList.slice(0, 20).join(','),
        searchKeywords: this.data.searchText,
        source: rmd,
        bizName: rmd,
      };
      app
        .request({
          path: 'wscshop/ump/coupon/search-coupon.json',
          query: fetchParams,
        })
        .then((res) => {
          // AB不展示券曝光埋点
          if (res && !res.show && res.ab_trace_id) {
            this.setYZData({
              logParams: {
                component: 'search_coupon_popup',
                abTraceId: res.ab_trace_id,
                slg: this.data.slg,
              },
            });
            this.setLogHandle(this.getViewLog());
          }
          if (!res || !res.show) {
            this.setYZData({
              show: false,
            });
            return;
          }
          this.setYZData({
            alias: res.activity_alias,
            logParams: {
              component: 'search_coupon_popup',
              abTraceId: res.ab_trace_id,
              slg: this.data.slg,
            },
          });

          // 已授权的自动领券
          if (this.data.isAuthorized) {
            this.updateCouponData(res, 'unaccalimed', false);
            this.getCoupons();
          } else {
            this.updateCouponData(res, 'unaccalimed', true);
          }
          // 曝光埋点
          this.setLogHandle(this.getViewLog());
        })
        .catch((err) => {
          this.setYZData({
            show: false,
          });
          console.log(err);
        });
    },
    // 立即领取
    receivedHandle(e) {
      if (e && e.detail) {
        this.setYZData({
          countDownNum: +e.detail < 2 ? 3 : e.detail,
        });
      }
      this.getCoupons();
      // 立即领取点击埋点
      this.setLogHandle(this.getReceiveLog());
    },
    // 立即使用
    useHandle() {
      this.useCoupons();
      // 去使用按钮点击埋点
      this.setLogHandle(this.getUseLog());
    },
    // 领券
    getCoupons() {
      const fetchParams = {
        alias: this.data.alias,
        source: 'search_rcmd',
        bizName: 'search_rcmd',
      };
      app
        .request({
          path: 'wscshop/ump/coupon/receive-search-coupon.json',
          data: fetchParams,
          method: 'POST',
        })
        .then((res) => {
          if (!res) {
            wx.showToast({
              title: errorMsg,
              icon: 'none',
            });
            return;
          }
          const extParams = {
            validEndTime: +res.valid_end_time,
            validStartTime: +res.valid_start_time,
          };
          this.setYZData({
            couponId: res.coupon_id,
            couponType: res.coupon_type,
            isGetCoupon: false,
          });
          this.updateCouponData({ ...extParams }, 'received', true);
        })
        .catch((err) => {
          wx.showToast({
            title: err.msg || errorMsg,
            icon: 'none',
          });
          console.log(err);
        });
    },
    // 使用券
    useCoupons() {
      const fetchParams = {
        couponId: this.data.couponId,
        groupType: couponTypes[this.data.couponType],
        searchKeyword: this.data.searchText,
      };
      app
        .request({
          path: 'wscump/coupon/coupon_use_redirect.json',
          query: fetchParams,
        })
        .then((res) => {
          if (!res) return;
          if (res.h5Url.indexOf('showcase/homepage') > -1) {
            // 停留当前页
            this.setYZData({
              isClose: true,
            });
          } else {
            jumpLink(res.weappUrl);
          }
        })
        .catch((err) => {
          wx.showToast({
            title: err.msg || errorMsg,
            icon: 'none',
          });
          console.log(err);
        });
    },
  },
});
