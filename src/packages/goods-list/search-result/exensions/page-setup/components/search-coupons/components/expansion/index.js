import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持
  },
  properties: {
    themeClass: String,
    searchText: String,
    couponStatus: String,
    couponData: Object,
    logParams: Object,
    countDownNum: Number,
  },
  data: {
    timer: null,
    curTime: new Date().getTime(),
    themeImage: '',
  },

  attached() {
    this.countDown();
  },
  detached() {
    clearInterval(this.data.timer);
  },
  methods: {
    // 发券弹窗关闭按钮点击log
    getPopupCloseLog() {
      return {
        et: 'click', // 事件类型
        ei: 'click_Popup_close', // 事件标识
        en: '发券弹窗关闭按钮点击', // 事件名称
        params: this.data.logParams,
      };
    },
    putawayHandle() {
      this.triggerEvent('onstatus');
      app.logger && app.logger.log(this.getPopupCloseLog());
    },
    receivedHandle() {
      this.triggerEvent('received', this.data.countDownNum);
    },
    useHandle() {
      this.triggerEvent('use');
    },
    countDown() {
      let { countDownNum } = this.data;
      this.setYZData({
        timer: setInterval(() => {
          countDownNum--;
          this.setYZData({
            countDownNum,
          });
          if (countDownNum === 0) {
            clearInterval(this.data.timer);
            this.triggerEvent('onstatus');
          }
        }, 1000),
      });
    },
    onChange(e) {
      // 多端编译到原生，要取payload
      const time = e.detail.payload;
      if (time) {
        time.hours = time.hours < 10 ? `0${time.hours}` : time.hours;
        time.minutes = time.minutes < 10 ? `0${time.minutes}` : time.minutes;
        time.seconds = time.seconds < 10 ? `0${time.seconds}` : time.seconds;
      }
      this.setYZData({
        timeData: time,
      });
    },
    onFinished() {
      this.triggerEvent('onstatus');
    },
  },
});
