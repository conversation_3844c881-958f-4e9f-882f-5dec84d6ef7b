import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();
WscComponent({
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持
  },
  properties: {
    title: String,
    couponStatus: String,
    couponData: Object,
    logParams: Object,
  },
  data: {
    curTime: new Date().getTime(),
    timeData: {},
    remindClass: 'putaway-desc',
  },
  ready() {
    this.remindHandle();
  },
  methods: {
    // 发券浮层关闭按钮点击log
    getCloseLog() {
      return {
        et: 'click', // 事件类型
        ei: 'click_close', // 事件标识
        en: '发券浮层关闭按钮点击', // 事件名称
        params: this.data.logParams,
      };
    },
    closeHandle() {
      this.triggerEvent('onstatus');
      app.logger && app.logger.log(this.getCloseLog());
    },
    receivedHandle() {
      this.triggerEvent('received');
    },
    useHandle() {
      this.triggerEvent('use');
    },
    onChange(e) {
      // 多端编译到原生，要取payload
      const time = e.detail.payload;
      if (time) {
        time.hours = time.hours < 10 ? `0${time.hours}` : time.hours;
        time.minutes = time.minutes < 10 ? `0${time.minutes}` : time.minutes;
        time.seconds = time.seconds < 10 ? `0${time.seconds}` : time.seconds;
      }
      this.setYZData({
        timeData: time,
      });
    },
    onFinished() {
      this.triggerEvent('onstatus');
    },
    remindHandle() {
      const isNotRemind =
        this.data.couponData.validEndTime - this.data.curTime > 1000 * 60 * 60;
      if (isNotRemind) {
        this.setYZData({
          remindClass: 'putaway-desc',
        });
      } else {
        this.setYZData({
          remindClass: 'putaway-desc-remind',
        });
      }
    },
  },
});
