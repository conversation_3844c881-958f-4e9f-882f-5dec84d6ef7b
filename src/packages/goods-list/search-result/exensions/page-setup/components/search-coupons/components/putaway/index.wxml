<theme-view custom-class="search-coupons-putaway" bg="var(--ump-coupon-bg)" opacity="0.06">
  <view class="putaway-left">
    <view class="denomination">
      <theme-view color="var(--ump-icon)">{{ couponData.value }}{{ couponData.unit }}</theme-view>
      <view>券</view>
    </view>
    <view class="info">
      <view wx:if="{{ couponStatus === 'unaccalimed' }}" class="putaway-desc-name">{{ couponData.name }}</view>
      <view wx:else class="count-down">
        <van-count-down
          use-slot
          time="{{ couponData.validEndTime - curTime }}"
          bind:change="onChange"
          bind:finish="onFinished">
          <text class="{{ remindClass }}" wx:if="{{ timeData.days > 0 }}">{{ timeData.days }}</text>
          <text class="time-day {{ remindClass }}" wx:if="{{ timeData.days > 0 }}">天</text>
          <text class="{{ remindClass }}">{{ timeData.hours }}:</text>
          <text class="{{ remindClass }}">{{ timeData.minutes }}:</text>
          <text class="{{ remindClass }}">{{ timeData.seconds }}</text>
        </van-count-down>
        <view
          class="time-suffix {{ remindClass }}">
          后失效
        </view>
      </view>
    </view>
  </view>
  <view class="putaway-right">
    <user-authorize wx:if="{{ couponStatus === 'unaccalimed' }}" bind:next="receivedHandle" scene="get_coupon">
      <theme-view custom-class="putaway-btn" bg="var(--ump-main-bg)" color="var(--ump-main-text)">立即领取</theme-view>
    </user-authorize>
    <theme-view wx:else custom-class="putaway-btn" bg="var(--ump-main-bg)" color="var(--ump-main-text)" bindtap="useHandle">
      立即使用
    </theme-view>
    <view class="putaway-close" bindtap="closeHandle">
      <van-icon name="cross" color="#969799" />
    </view>
  </view>
</theme-view>