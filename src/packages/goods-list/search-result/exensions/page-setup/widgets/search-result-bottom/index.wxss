
@import "shared/common/css/helper/index.wxss";

.sc-goods__photo--soldout::after {
  position:absolute;
  top:0;
  left:0;
  right:0;
  bottom:0;
  margin:auto;
  width:100%;
  height:100%;
  background-image:url(https://img.yzcdn.cn/public_files/2017/11/20/fab60eb5be80315a6dd19f91fbd1dca9.png);
  background-color:rgba(0, 0, 0, 0.3);
  background-position:center;
  background-repeat:no-repeat;
  background-size: 60px 60px;
  content: '';
}

.search-goods__media-icon {
  position: absolute;
  bottom: 26px;
  right: 5px;
  width: 20px;
  height: 20px;
}

.recommend-title {
  display: flex;
  justify-content: center;
  box-sizing: border-box;
  text-align: center;
  width: 100%;
  border: transparent 16px;
  border-style: none solid;
  font-size: 14px;
  line-height: 48px;
  position: relative;
}

.recommend-title::before {
  content: '';
  width: 200%;
  height: 1px;
  position: absolute;
  top: 24px;
  left: 0;
  background-color: #d4d6d9;
  transform-origin: 0;
  transform: scale(0.5);
  z-index: 0;
}

.recommend-title__text {
  position: relative;
  z-index: 1;
  width: 120px;
  padding: 0 12px;
  font-weight: 500;
}
