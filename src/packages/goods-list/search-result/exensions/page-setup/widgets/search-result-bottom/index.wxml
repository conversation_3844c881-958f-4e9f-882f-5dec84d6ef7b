<view style="{{ (goodsOpt.layoutOpt.type === 'list' ? '; background: #fff' : '; background: #F5F5F5') }}" bindtap="handleRootTap">
  <view wx:if="{{ showRecommend && isShowProducts && isSupportGoodsRecommend && recommendHasData }}" class="search-goods__recommend">
    <goods-recommend bind:afterload="onAfterload"></goods-recommend>
  </view>
  <van-toast id="van-toast" />
  <van-notify id="van-notify" />
  <search-coupons wx:if="{{ goodsOpt.items.length > 0 }}" searchText="{{ keyword }}" goodsList="{{ goodsOpt.items }}" slg="{{ slg }}"></search-coupons>
  <feature-sku />
  <!-- 原来page-container的内容 -->
  <bi-icon />
  <protocol
    z-index="{{ protocolIndex }}"
    noAutoAuth="{{ noAutoAuth }}"
  />
  <!-- 原来page-container的内容 -->
</view>