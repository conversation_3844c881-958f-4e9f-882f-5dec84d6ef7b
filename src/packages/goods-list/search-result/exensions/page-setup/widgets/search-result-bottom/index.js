import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';

RantaWidget({
  options: {
    styleIsolation: 'shared',
  },
  data: {
    protocolIndex: 10001,
    noAutoAuth: false,
  },

  properties: {},

  observers: {},

  attached() {
    mapState(this, [
      'keyword',
      'isShowProducts',
      'isSupportGoodsRecommend',
      'recommendHasData',
      'showRecommend',
      'slg',
      'goodsOpt',
      'showShareFloating',
      'listQuery',
      'totalAlias',
    ]);
    mapActions(this, ['handleRootTap', 'setGoodsViewLog']);
  },

  methods: {
    // 推荐组件的回调，数据空的时候需要隐藏
    onAfterload({ detail }) {
      const { recommendList = [] } = detail;
      if (recommendList.length <= 0) {
        this.setYZData({
          recommendHasData: false,
        });
      } else {
        this.setGoodsViewLog(recommendList, 'search');
      }
    },
  },
});
