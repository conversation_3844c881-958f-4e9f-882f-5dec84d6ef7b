import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';

const SEARCH_HISTORY = 'search-history';
const app = getApp();

RantaWidget({
  options: {
    styleIsolation: 'shared',
  },
  data: {},

  properties: {},

  observers: {},

  attached() {
    mapState(this, [
      'inputValue',
      'keyword',
      'focused',
      'hotSearchKeys',
      'isShowProducts',
      'searchHistory',
      'goodsOpt',
      'needLoadMore',
      'loading',
    ]);
    mapActions(this, ['setVal', 'fetchProductList', 'handleRootTap']);
    setTimeout(() => {
      this.getRect(this, '.sticky-content').then((rect) => {
        this.setYZData({
          stickyHeight: `height: ${rect.height}px;`,
          fixedTop: `top: ${rect.height}px;`,
        });
      });
    }, 1000);
  },

  methods: {
    getRect(context, selector) {
      return new Promise((resolve) => {
        this.createSelectorQuery()
          .select(selector)
          .boundingClientRect()
          .exec((rect = []) => resolve(rect[0] || []));
      });
    },
    onCloseTap() {
      const { items = [] } = this.data.goodsOpt;
      if (items.length > 0) {
        this.setVal({
          isShowProducts: true,
        });
      }
    },
    onSearchKeyTap({ detail: { text } }) {
      if (text) {
        // 异步，防止onBlur返回上一次的结果
        setTimeout(() => {
          this.setVal({
            keyword: text,
            inputValue: text,
          });
        }, 100);
        this.searchDone({ detail: text });
      }
    },
    searchChange({ detail: inputValue = '' }) {
      this.setVal({
        inputValue,
      });
    },
    searchDone({ detail: keyword = '' }) {
      this.setVal({
        keyword,
        page: 1,
      });

      app.logger.log({
        et: 'click',
        ei: 'search',
        en: '搜索',
        params: {
          words: keyword,
        },
      });
      this.searchSaveHistory(keyword);
      this.fetchProductList();
    },
    /**
     * 搜索输入框聚焦
     */
    searchFocus() {
      this.handleSearchClick();
      this.setVal({
        isShowProducts: false,
        focused: true,
      });
    },
    // 存储搜索历史
    searchSaveHistory(searchValue) {
      if (searchValue) {
        let searchHistory = this.data.searchHistory.filter(
          (item) => item !== searchValue
        );
        searchHistory.unshift(searchValue);
        searchHistory = searchHistory.slice(0, 10);
        this.setVal({
          searchHistory,
        });

        wx.setStorage({
          key: SEARCH_HISTORY,
          data: searchHistory,
        });
      }
    },
    /**
     * 搜索输入框失去焦点
     */
    // onCancelFocus() {
    //   const { items = [] } = this.data.goodsOpt;
    //   if (items.length > 0) {
    //     this.setVal({
    //       isShowProducts: true,
    //       focused: false,
    //     });
    //   } else {
    //     // eslint-disable-next-line @youzan/dmc/wx-check
    //     wx.navigateBack();
    //   }
    // },
    // 清除搜索历史
    searchHandleClearHistory() {
      this.setVal({
        searchHistory: [],
      });
      wx.removeStorage({
        key: SEARCH_HISTORY,
      });
    },
    handleSearchClick() {
      const searchPageRoute = 'packages/shop/search-page/index';
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.redirectTo({
        url: `/${searchPageRoute}?keepWords=${encodeURIComponent(
          this.data.inputValue
        )}&isFromList=1`,
      });
    },
  },
});
