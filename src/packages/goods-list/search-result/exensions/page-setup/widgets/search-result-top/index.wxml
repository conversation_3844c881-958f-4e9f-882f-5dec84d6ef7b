<view
  style="{{ (goodsOpt.layoutOpt.type === 'list' ? '; background: #fff' : '; background: linear-gradient(180deg, #FFF 20vh, #F5F5F5 50vh)') }}"
  bindtap="handleRootTap">
  <view style="{{ stickyHeight }}" class="sticky-wrap">
    <view class="sticky-content">
      <share-tab />
      <view bindtap="handleSearchClick">
        <van-search search-style="height: 100rpx; padding: 0 30rpx; background: {{ searchbg || '#F2F2F2' }};"
          inputStyle="height: 80rpx; border-radius: 4rpx; border-radius: var(--theme-radius-card, 999px) !important"
          keyword="{{ inputValue }}" value="{{ inputValue }}"
          placeholder="{{ hotSearchKeys && hotSearchKeys.length > 0 ? hotSearchKeys[0] : '搜索商品' }}" focus="{{ focused }}"
          shape="round" readonly bind:change="searchChange" bind:search="searchDone" bind:focus="searchFocus"
          :positionShowMethod="0" :positionType="1" />
      </view>
      <view wx:if="{{!isShowProducts}}" class="search__float" bind:tap="onCloseTap" style="{{ fixedTop }}">
        <keys-map wx:if="{{ hotSearchKeys && hotSearchKeys.length > 0 }}" title="热门搜索" show-keys="{{ hotSearchKeys }}"
          bind:click="onSearchKeyTap" />
        <keys-map wx:if="{{ searchHistory && searchHistory.length > 0 }}" title="搜索历史" show-keys="{{ searchHistory }}"
          bind:click="onSearchKeyTap">
          <view slot="km__right-icon" class="search-goods__remove-icon" catch:tap="searchHandleClearHistory">
            <van-icon name="delete" color="#C8C9CC" size="14px" custom-style="display:block" />
            <text>清空</text>
          </view>
        </keys-map>
      </view>
      <filter-bar wx:if="{{ isShowProducts }}" isShowLayoutChange />
    </view>
  </view>
  <view style="{{ stickyHeight }}"></view>
  <shop-block />
  <van-list bind:load="fetchProductList" loading="{{loading}}" finished="{{!needLoadMore}}">
    <dc-goods />
  </van-list>
</view>