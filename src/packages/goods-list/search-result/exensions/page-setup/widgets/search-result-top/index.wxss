
@import "shared/common/css/helper/index.wxss";

.search__float{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  top: 54px;
  background: #F7F8FA;
}

.search-goods__remove-icon{
  height: 100%;
  display: flex;
  align-items: center;
}

.search-goods__remove-icon text{
  font-size: 12px;
  color: #C8C9CC;
  display: inline-block;
  margin-left: 1px;
}

.sc-goods__photo--soldout::after {
  position:absolute;
  top:0;
  left:0;
  right:0;
  bottom:0;
  margin:auto;
  width:100%;
  height:100%;
  background-image:url(https://img.yzcdn.cn/public_files/2017/11/20/fab60eb5be80315a6dd19f91fbd1dca9.png);
  background-color:rgba(0, 0, 0, 0.3);
  background-position:center;
  background-repeat:no-repeat;
  background-size: 60px 60px;
  content: '';
}

.search-goods__media-icon {
  position: absolute;
  bottom: 26px;
  right: 5px;
  width: 20px;
  height: 20px;
}

.van-search__content {
  border-radius: var(--theme-radius-card, 8px) !important
}

.sticky-wrap {
  position:fixed;
  top:0;
  left:0;
  right:0;
  background-color: #fff;
  z-index: 99;
}