import { RantaWidget } from 'shared/common/base/ranta/widget';
import getUniquePageKey from 'shared/utils/unique';
import Event from '@youzan/weapp-utils/lib/event';
import TeeEvent from '@youzan/tee-event';
import { getCurrentPage } from 'shared/common/base/wsc-component';

RantaWidget({
  data: {
    scrollTop: 0,
  },

  properties: {},

  observers: {},

  attached() {
    TeeEvent.on('pageScrollTo', (options) => {
      if (options.scrollTop === 0) {
        this.setYZData({
          scrollTop: this.data.scrollTop === 0 ? 1 : options.scrollTop,
        });
      }
    });
  },
  detached() {
    TeeEvent.off('pageScrollTo');
  },
  methods: {
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
      // console.log('goods-list-search onPageScroll');
      if (!this.needLoadMore) {
        Event.trigger('float-share:show' + getUniquePageKey(this));
      }
    },

    /**
     * 滚动事件 触发 使用同一个Event 实例
     * @param e
     */
    onPageScroll(e) {
      // console.log('goods-list-search onPageScroll');
      if (e.scrollTop <= 0) return;
      Event.trigger('onPageScroll' + getUniquePageKey(this), e);
      getCurrentPage().onPageScroll(e.detail);
    },
  },
});
