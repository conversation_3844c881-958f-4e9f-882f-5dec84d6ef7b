<view style="{{ (goodsOpt.layoutOpt.type === 'list' ? '; background: #fff' : '; background: linear-gradient(180deg, #FFF 20vh, #F5F5F5 50vh)') }}"  wx:if="{{ isShowProducts && isEmpty }}" class="s-g-e" bindtap="handleRootTap">
  <image
    src="https://img.yzcdn.cn/public_files/123c352d876be85e960ca5c78dd910a6.png"
    class="s-g-e-img"
  />
  <view class="s-g-e-text">
    <view>没有找到你想要的商品</view>
    <view>换个搜索词试试</view>
  </view>
</view>