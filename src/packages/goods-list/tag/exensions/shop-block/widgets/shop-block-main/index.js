import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapData } from '@youzan/ranta-helper';

const app = getApp();

RantaWidget({
  data: {
    shop: app.$store.state.shop,
    shopBlockInfo: {},
    stickyHeight: 0,
  },

  properties: {},

  observers: {},

  attached() {
    mapData(this, ['shopBlockInfo', 'stickyHeight']);
    console.log('shopBlockInfo', this.data.shopBlockInfo);
  },

  methods: {},

  // behaviors: [pageComponentBehavior],
});
