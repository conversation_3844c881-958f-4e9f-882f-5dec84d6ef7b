<shop
  is-retail-shelf="{{ shopBlockInfo.isRetailShelf }}"
  show-service-due="{{ shopBlockInfo.showServiceDue }}"
  show-shop-status="{{ shopBlockInfo.showShopStatus }}"
  show-store-switch="{{ shopBlockInfo.showStoreSwitch && stickyHeight }}"
  switch-page-options="{{ shopBlockInfo.switchPageOptions }}"
  buyer-address="{{ shopBlockInfo.buyerAddress }}"
  navigationbar-config-data="{{ shopBlockInfo.navigationbarConfigData }}"
></shop>
