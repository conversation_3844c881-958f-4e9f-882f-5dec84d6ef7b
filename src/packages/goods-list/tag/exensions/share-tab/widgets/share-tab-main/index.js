import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapData } from '@youzan/ranta-helper';

RantaWidget({
  data: {
    showTab: false,
    navBarTitle: '',
    canShowNavBar: false,
    listQuery: {},
    totalAlias: [],
  },

  properties: {
    shareFrom: Number,
  },

  observers: {},

  attached() {
    mapData(this, [
      'showTab',
      'navBarTitle',
      'canShowNavBar',
      'listQuery',
      'totalAlias',
    ]);
  },

  methods: {},

  // behaviors: [pageComponentBehavior],
});
