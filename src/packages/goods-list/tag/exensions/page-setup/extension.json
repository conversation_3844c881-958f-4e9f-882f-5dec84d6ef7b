{"name": "@wsc-shop/goods-list-tag-page-setup", "extensionId": "@wsc-shop/goods-list-tag-page-setup", "version": "0.0.1", "bundle": "<builtin>", "lifecycle": ["beforePageCreate", "onPageShow", "beforePageMount", "onPullDownRefresh", "onShareAppMessage", "onReachBottom", "onPageScroll"], "framework": "weapp", "data": {"provide": {"cpsConfigKey": ["r", "w"], "kdtId": ["r", "w"], "shopMetaInfo": ["r", "w"], "shopBaseInfo": ["r", "w"], "shopBlockInfo": ["r", "w"], "showTab": ["r", "w"], "navBarTitle": ["r", "w"], "canShowNavBar": ["r", "w"], "listQuery": ["r", "w"], "totalAlias": ["r", "w"], "filters": ["r", "w"], "themeClass": ["r", "w"], "goodsOpt": ["r", "w"], "uuid": ["r", "w"], "pageBgColor": ["r", "w"], "themeTagObj": ["r", "w"], "stickyHeight": ["r", "w"], "featureComponents": ["r"], "isSkyline": ["r"], "pageType": ["r"]}, "consume": {"themeCSS": ["r"], "themeTag": ["r"], "themeColors": ["r"]}}, "process": {"define": ["handleItemClick", "showcaseHandleGoodsBuy", "handleFilterChange", "handleItemImageLoaded", "handleChangeLayoutType"]}, "event": {"emit": ["featureSku:show"]}, "widget": {"provide": ["GoodsListTagPageSetup"], "consume": ["CpsRecommendGoods", "SearchBar", "FilterBar", "ShareTab", "DcGoods", "ShopStatus", "ShopTopBar", "ShopBlock", "featureSku", "ShowcaseContainer", "<PERSON>Footer"]}, "component": {"consume": ["VanList"]}, "platform": ["weapp"]}