<scroll-view type="list" scroll-top="{{scrollTop}}" scroll-y scroll-with-animation enhanced style="height: 100vh" bindscrolltolower="onReachBottom" bindscroll="onPageScroll">
  <view class="box" style="{{ (goodsOpt.layoutOpt.type === 'one' ? '; background: #fff' : '; background: linear-gradient(180deg, #FFF 20vh, #F5F5F5 50vh)') }}" bindtap="handleRootTap">
    <!-- offset-top hack 解决 scroll-view bug -->
    <van-sticky wx:if="{{ hasLoaded }}" disabled="{{false}}" offset-top="{{1}}">
      <view style="height: {{ navBarHeight }}px">
        <share-tab />
      </view>
    </van-sticky>
    <van-sticky wx:if="{{ hasLoaded }}" offset-top="{{ navBarHeight + 1 }}" disabled="{{false}}">
      <!-- <shop-top-bar /> -->
      <view style="height: {{ stickyHeight }}px">
        <view wx:if="{{ showSearchBar }}" style="height: 50px">
          <search-bar />
        </view>
        <view class="tab-container" wx:if="{{ groupData.length > 1 }}">
          <van-tabs
            pid="{{ pid }}"
            custom-class="tabs"
            nav-class="custom-nav-class"
            active="{{ activeName }}"
            title-inactive-color="#969799"
            title-active-color="#323233"
            color="var(--general, #2f2f34)"
            line-width="28"
            ellipsis="{{ false }}"
            bind:change="handleTabChange"
          >
            <van-tab
              pid="{{ pid }}"
              wx:for="{{ groupData }}"
              title="{{ item.subtitle || item.title }}"
              name="{{ item.alias }}"
              wx:key="alias"
            ></van-tab>
            <view slot="nav-right">
              <view class="icon-show-more" bindtap="showAllGroup">
                <van-icon name="{{ isAllGroupShow ? 'arrow-up' : 'arrow-down' }}" />
              </view>
            </view>
          </van-tabs>
          <van-dropdown-menu
            custom-class="dropdown-all-tag"
            z-index="{{ 11 }}"
            close-on-click-outside="{{ false }}"
            pid="aliasDropmenu"
          >
            <van-dropdown-item
              pid="aliasDropmenu"
              class="js-dropdown-item"
              bind:close="onCloseDropdown"
            >
              <view class="tag-container" style="{{tagStyle}}">
                <van-tag
                  custom-class="{{ item.alias === activeName ? 'custom-van-tag tag-active' : 'custom-van-tag' }}"
                  wx:for="{{ groupData }}"
                  round
                  wx:key="alias"
                  data-key="{{ item.alias }}"
                  bindtap="handleClickTag"
                  >{{ item.subtitle || item.title }}</van-tag
                >
              </view>
            </van-dropdown-item>
          </van-dropdown-menu>
        </view>
        <filter-bar />
      </view>
    </van-sticky>
    <view wx:if="{{ isEmpty }}" class="tag-list-goods__empty">
      <image
        class="tag-list-goods__empty-img"
        src="https://img.yzcdn.cn/upload_files/2020/12/31/FkdBILcwwEKmXT3IzSyjAyNpB7j9.png"
      />
      <text class="tag-list-goods__empty-text">暂无商品</text>
    </view>
    <block v-else>
      <van-list bind:load="fetchProductList" loading="{{loading}}" finished="{{!needLoadMore}}">
        <dc-goods />
      </van-list>
    </block>
    <showcase-container />
    <cps-recommend-goods />
    <van-toast id="van-toast" />
    <van-notify id="van-notify" />
    <!-- 原来page-container的内容 -->
    <protocol
    z-index="{{ protocolIndex }}"
    noAutoAuth="{{ noAutoAuth }}"
    />
    <bi-icon />
    <!-- 原来page-container的内容 -->
    <feature-sku />
    <shop-pop-manager source="{{ 5 }}" />
    <page-footer wx:if="{{ hasLoaded }}" />
  </view>
</scroll-view>
