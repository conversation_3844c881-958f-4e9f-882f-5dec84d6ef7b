import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import { mapData } from '@youzan/ranta-helper';
import getUniquePageKey from 'shared/utils/unique';
import Event from '@youzan/weapp-utils/lib/event';
import TeeEvent from '@youzan/tee-event';
import { getCurrentPage } from 'shared/common/base/wsc-component';

RantaWidget({
  data: {
    protocolIndex: 10001,
    noAutoAuth: false,
    showShopStatus: true,
    showServiceDue: false,
    isRetailShelf: false,
    themeTag: {},
    themeColors: {},
    tagStyle: '',
    pid: '',
  },

  properties: {},

  observers: {},

  attached() {
    // ONLINE-862976 不同页面设置不同的pid 同一个页面被打开多次 如果用同一个pid 会导致groupData合并
    this.setYZData({
      pid: Date.now(),
    });
    mapData(this, ['themeTag', 'themeColors']);
    mapState(this, [
      'isEmpty',
      'showSearchBar',
      'orderBy',
      'hasLoaded',
      'activeName',
      'groupData',
      'isAllGroupShow',
      'navBarHeight',
      'goodsOpt',
      'stickyHeight',
      'loading',
      'needLoadMore',
    ]);
    // 全点风格van-tag样式
    this.handleTagStyle();
    mapActions(this, ['setVal', 'fetchProductList', 'handleRootTap']);
    TeeEvent.on('pageScrollTo', (options) => {
      if (options.scrollTop === 0) {
        this.setYZData({
          scrollTop: this.data.scrollTop === 0 ? 1 : options.scrollTop,
        });
      }
    });
  },
  detached() {
    TeeEvent.off('pageScrollTo');
  },
  methods: {
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
      const { needLoadMore } = this.data;
      if (!needLoadMore) {
        Event.trigger('float-share:show' + getUniquePageKey(this));
      }
    },

    onPageScroll(e) {
      // console.log('goods-list-tag onPageScroll');
      if (e.scrollTop <= 0) return;
      Event.trigger('onPageScroll' + getUniquePageKey(this), e);

      getCurrentPage().onPageScroll(e.detail);
    },
    showAllGroup() {
      const { isAllGroupShow } = this.data;
      this.setVal({
        isAllGroupShow: !isAllGroupShow,
      });
      this.selectComponent('.js-dropdown-item')[Symbol.for('vm')].vm.toggle(
        !isAllGroupShow
      );
    },
    handleTabChange(e) {
      this.setVal({
        page: 1,
        activeName: e.detail.payload.name,
      });
      setTimeout(() => {
        TeeEvent.trigger('pageScrollTo', { scrollTop: 0, duration: 0 });
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 0,
        });
        this.fetchProductList();
      }, 0);
      // 如果二级分组筛选是展开状态，要收起来
      if (this.data.isAllGroupShow) {
        this.showAllGroup();
      }
    },
    handleClickTag(e) {
      const { key } = e.currentTarget.dataset;
      this.setVal({
        page: 1,
        activeName: key,
      });
      setTimeout(() => {
        TeeEvent.trigger('pageScrollTo', { scrollTop: 0, duration: 0 });
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 0,
        });
        this.fetchProductList();
      }, 0);
      this.showAllGroup();
    },
    handleTagStyle() {
      const { themeTag, themeColors } = this.data;
      const tagActiveBorder = themeTag.plain
        ? themeColors['tag-text']
        : 'transparent';
      const tagBorder = themeTag.plain ? '#DCDEE0' : 'transparent';
      const tagBg = themeTag.plain
        ? '#fff'
        : themeColors['neutral-bg-main'] || '#f7f8fa';
      const tagActiveBg = themeTag.plain
        ? '#fff'
        : themeColors['tag-bg'] || '#f7f8fa';

      this.setYZData({
        tagStyle: `--tag-active-border: ${tagActiveBorder}; --tag-border: ${tagBorder}; --tag-bg: ${tagBg}; --tag-active-bg: ${tagActiveBg}`,
      });
    },
    onCloseDropdown() {
      const { isAllGroupShow } = this.data;
      if (isAllGroupShow) {
        this.setVal('isAllGroupShow', !isAllGroupShow);
      }
    },
  },
});
