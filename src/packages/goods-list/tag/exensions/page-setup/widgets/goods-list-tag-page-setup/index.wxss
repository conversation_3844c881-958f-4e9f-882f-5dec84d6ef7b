@import '/components/showcase/index.wxss';

.box {
  min-height: 100vh;
  overflow: hidden;
}
.showcase-all-goods--fixed .filter-bar-wrap {
  transition: linear top 300ms;
}

.filter-bar-wrap {
  width: 100%;
}

.all-goods__empty {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  width: 100%;
  text-align: center;
}

.all-goods__empty-image {
  width: 80px;
  height: 84px;
  display: block;
  margin: 0 auto 15px;
}

.all-goods__empty-text {
  font-size: 14px;
  line-height: 28px;
  color: #a9a9a9;
}

.tag-list-goods__empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.tag-list-goods__empty-img {
  display: block;
  width: 160px;
  height: 160px;
}

.tag-list-goods__empty-text {
  font-size: 14px;
  color: #969799;
  text-align: center;
}

.tab-container {
  position: relative;
}

.tabs {
  height: 44px;
  border-bottom: 1px solid #f1f1f1;
}

.tabs::after {
  content: ' ';
  display: block;
  min-width: 44px;
  flex: 1;
}

.icon-show-more {
  /* position: absolute;
  top: 0;
  right: 0; */
  width: 45px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background: #fff;
  z-index: 1;
}

.dropdown-all-tag {
  height: 0 !important;
  overflow: hidden;
}

.tag-container {
  padding: 10px;
  background: #fff;
}

.custom-van-tag {
  display: inline-flex !important;
  padding: 6px 12px !important;
  margin: 6px !important;
  color:  var(--neutral-text-main, #323233) !important;
  background: var(--tag-bg) !important;
  border-radius: var(--theme-radius-button, 999px) !important;
  border: 1px solid var(--tag-border, #DCDEE0) !important;
}

.tag-active {
  color: var(--theme-tag-color, #323233) !important;
  border: 1px solid var(--tag-active-border, #DCDEE0) !important;
  background: var(--tag-active-bg) !important;
}

.custom-nav-class {
  height: 44px;
}