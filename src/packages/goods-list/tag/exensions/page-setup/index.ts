// @ts-nocheck
import { createStore, mapState, mapActions } from '@ranta/store';
// import Event from '@youzan/weapp-utils/lib/event';
import Toast from '@vant/weapp/dist/toast/toast';
import { checkReLaunchShopSelect } from '@/base-api/shop/chain-store';
import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';
import appEvent from 'shared/utils/app-event';
import openWebView from 'shared/utils/open-web-view';
import { getCurrentRoute } from 'shared/utils/url';
import GoodsPageSku from '../../../mixins/sku-ranta';
import GoodsPageFilterBar from '../../../mixins/filter-bar-ranta';
import Salesman from '../../../mixins/salesman-ranta';
import CommonMixin from '../../../mixins/common';
// import getUniquePageKey from 'shared/utils/unique';
import args from '@youzan/weapp-utils/lib/args';
import viewLog from '../../../mixins/logs-ranta';
// import { initSalesman } from 'shared/utils/salesman-share';
// import { requestByCache } from '@youzan/tee-biz-request';
import { getTagBaseParams } from 'shared/utils/prefetch-params';
import { formatNewList, getBarHeight } from '../../../common/utils';
import { getGoodsLayoutOpt } from '../../../common/decorate';
import { TAG_CONFIG } from '../../../common/constants';
import set from 'utils/set';

const app = getApp();

// 店铺状态
const SHOP_LOCK_STATUS = {
  // 风控锁定
  LOCK: 1,
};

// 当前上下文 用于在ranta store里面调用一些mixin的方法
let context = null;

export default class {
  store = createStore({
    state: () => ({
      page: 1,
      id: '', // new
      tagParams: {}, // new
      layout: 0,
      showUmpAmbience: false,
      themeClass: app.themeClass,
      loading: false,
      needLoadMore: true,
      showSearchBar: false,
      isEmpty: false, // new
      listQuery: {},
      // 分销员
      salesman: {},
      pageQuery: {},
      // sku
      featureSkuData: null,
      // filter-bar
      order: '',
      orderBy: '',
      hasLoaded: false,
      tagComponents: [],
      filters: [
        { title: '综合', value: '' },
        { title: '上新', value: 'createdTime' },
      ],
      isLimitSold: false,
      limitSoldNum: 0,
      cpsConfigKey: 'cps_goods_recommend_goods_group',
      shareImgUrl: '',
      activeName: '',
      soldOutGoodsImg: '',
      groupData: [],
      isAllGroupShow: false,
      emptyList: false,
      navBarHeight: 0,
      searchConfig: {
        border_style_color: '#fff',
        border_style_height: 40,
        border_style_method: '0',
        color: '#f9f9f9',
        hot_search_keys: [],
        hot_search_keys_new: [],
        position_show_method: '1',
        position_type: '1',
        show_search_component: '1',
        text_align_method: '0',
        text_color: '#999',
        type: 'search',
        z_index: 121,
      },
      slgStatus: true,
      goodsOpt: TAG_CONFIG,
      uuid: '',
      showTab: false,
      navBarTitle: '',
      canShowNavBar: false,
      totalAlias: [],
      stickyHeight: 0,
      shop: app.$store.state.shop,
      __query__: '',
      route: '',
    }),
    actions: {
      setVal(obj) {
        Object.entries(obj).forEach(([key, value]) => {
          set(this, key, value);
        });
      },
      fetchGroupPage(options) {
        this.id = app.getKdtId();
        wx.showToast({
          title: '加载中',
          icon: 'loading',
        });
        this.fetchAlias(options)
          .then((alias) => {
            this.setVal({
              tagAlias: alias,
              page: 1,
              loading: false,
              needLoadMore: true,
            });
            this.getTagBaseInfo(alias);
          })
          .finally(() => {
            // 页面白屏延迟消失
            setTimeout(() => {
              wx.hideToast();
            }, 500);
          });
      },
      fetchAlias(options) {
        return new Promise((resolve) => {
          app.getShopInfo().then((data) => {
            const { chainStoreInfo } = data;
            const { isRootShop = false, isMultiOnlineShop = false } =
              chainStoreInfo;

            // 只有连锁多网店才进行alias映射
            if (!isMultiOnlineShop) {
              resolve(options.query.alias);
              // 如果是总店则执行进店
            } else if (isRootShop) {
              const redirectUrl = args.add(
                '/' + options.route,
                options.query || {}
              );
              autoEnterShop({ ...(options.query || {}), redirectUrl }).then(
                (subKdtId) => {
                  if (subKdtId) {
                    this.getAliasPromise(options.query.alias, subKdtId).then(
                      (alias) => {
                        this.setVal({
                          alias,
                        });
                        // this.setYZData({ alias });
                        resolve(alias);
                      }
                    );
                  } else {
                    resolve(options.query.alias);
                  }
                }
              );
              // 如果是分店则直接更新alias
            } else {
              this.getAliasPromise(
                options.query.alias,
                app.getKdtId() || data.kdt_id
              ).then((alias) => {
                this.setVal({
                  alias,
                });
                // this.setYZData({ alias });
                resolve(alias);
              });
            }
          });
        });
      },
      getComponents(id) {
        app
          .request({
            path: '/wscdeco/biz-component/list.json',
            data: {
              id,
              kdtId: this.id,
              stage: 100,
              bizName: 'itemTag',
              queryKeys: 'id,kdtId',
            },
          })
          .then((res) => {
            this.setVal({
              tagComponents: res.components,
            });
          });
      },
      getTagBaseInfo(alias) {
        this.setVal({
          loading: true,
        });
        this.tagParams = getTagBaseParams({ alias });
        app
          .request({
            path: '/wscshop/goods-list/tag/base.json',
            method: 'GET',
            query: {
              alias,
              supportFixGroupOptionalCombo: true,
              orderType: 'desc',
              orderField: 'score',
              ...(this.tagParams || {}),
            },
            cacheConfig: {
              forceUpdate: false,
              expire: 60,
            },
          })
          .then((res) => {
            const { tagInfo, subTag } = res;
            // 从 tagInfo 取到的标题会精准到具体分组名, 从 parsedComponents 取到的标题是第一级分组名
            const {
              title: curTitle,
              subtitle: curSubtitle,
              parsedComponents,
              groupId,
            } = tagInfo;
            this.getComponents(groupId);
            const {
              buy_btn: buyBtn,
              show_title: showTitle,
              show_ump_ambience: showUmpAmbience = 0,
              show_search_bar: showSearchBar,
              buy_btn_type: buyBtnType,
              size,
              size_type: sizeType,
              price,
              title = '',
              subtitle = '',
            } = parsedComponents[0] || {};

            const navBarTitle =
              curSubtitle || curTitle || subtitle || title || '商品分组';
            // // 设置 NavigationBar title
            // wx.setNavigationBarTitle({
            //   title: navBarTitle,
            // });

            const groupData = [
              {
                alias,
                title: '全部',
              },
              ...subTag,
            ];
            const needShowSearchBar =
              showSearchBar === undefined || +showSearchBar === 1;
            const stickyHeight =
              groupData.length > 1 && needShowSearchBar
                ? 136
                : groupData.length <= 1 && !needShowSearchBar
                ? 40
                : 92;
            this.setVal({
              layout: +size,
              showUmpAmbience: +showUmpAmbience === 1,
            });

            const layoutConfig = getGoodsLayoutOpt(
              {
                size: this.layout,
                buyBtn,
                buyBtnType,
                sizeType,
                price,
                showTitle,
              },
              this.goodsOpt.itemCardOpt,
              {
                isTag: true,
              }
            );

            this.setVal({
              activeName: alias,
              groupData,
              navBarTitle,
              listQuery: {
                alias,
                ...(this.tagParams || {}),
              },
              isAllGroupShow: false,
              pageQuery: {
                alias,
                title,
                subtitle,
              },
              stickyHeight,
              hasLoaded: true,
              showSearchBar: needShowSearchBar,
              loading: false,
              ...layoutConfig,
            });

            // 获取商品失败
            if (!res.fetchGoodsSuccess || !res.tagGoods) {
              this.fetchProductList();
            } else {
              this.setListData(res.tagGoods);
            }
          })
          .catch(() => {
            this.setVal({
              activeName: '',
              groupData: [],
              isAllGroupShow: false,
              loading: false,
            });
          });
      },
      setListData(rs) {
        // 获取到slg后，触发enterpage埋点上报：每个商品返回的slg是一致的
        context.triggerLogger(
          rs.list ? rs.list[0]?.extraInfo?.slg || '0' : '0',
          't'
        );

        if (!rs.list && this.page === 1) {
          this.setVal({
            isEmpty: true,
            loading: false,
            'goodsOpt.items': [],
          });
          return;
        }

        // eslint-disable-next-line prefer-const
        let { page } = this;
        const { isLimitSold, limitSoldNum, goodsOpt = {} } = this;
        const { hasMore } = rs;
        const currentList = +page === 1 ? [] : goodsOpt.items;
        const currentAlias = rs.list.map((item) => item.alias);
        const totalAlias = this.totalAlias.concat(currentAlias);
        const showTab = totalAlias.length >= 20;

        const {
          listData,
          page: newPage,
          needLoadMore,
          isEmpty,
          list,
        } = formatNewList(rs.list || [], {
          page,
          pageSize: this.tagParams.pageSize,
          isLimitSold,
          limitSoldNum,
          spmType: 't',
          pageType: 'goods',
          currentList,
          hasMore,
          loggerParam: {
            item_type: 'goods',
          },
          noNeedFormat: true,
          layout: this.layout,
          isUmpInfoShow: this.showUmpAmbience,
          needFormatPrice: false,
          titleConfig: {
            layoutType: (goodsOpt.itemCardOpt || {}).layoutType,
            showTitle: (goodsOpt.itemCardOpt || {}).titleOpt,
            sizeType: (goodsOpt.itemCardOpt || {}).type,
          },
          soldOutGoodsImg: this.soldOutGoodsImg,
        });

        // 区分首次和下拉加载更多返回的数据 返回的数据格式不一样
        let res = this.goodsOpt.items || [];
        if (listData['goodsOpt.items']) {
          // 首次
          res = [...listData['goodsOpt.items']];
        } else {
          // 下拉
          res = res.concat(Object.values(listData));
        }
        // todp mixin
        context.getSalesmanCard(list);

        this.setVal({
          page: newPage,
          needLoadMore,
          isEmpty,
          loading: false,
          showTab,
          totalAlias,
          canShowNavBar: true,
          'goodsOpt.items': res,
        });

        // todo mixin
        context.setGoodsViewLog(list, 'tags');
      },
      getAliasPromise(currentAlias, kdtId) {
        if (currentAlias) {
          return app
            .request({
              path: '/wscshop/api/showcase-retail/getRetailSubShopTagInfo.json',
              data: {
                source_alias: currentAlias,
                target_kdt_id: kdtId,
              },
            })
            .then((data) => {
              return data.alias;
            })
            .catch((err) => {
              openWebView('/wscshop/common/error/not-exist', {
                title: '分组创建中/不存在',
                method: 'redirectTo',
                query: {
                  kdt_id: app.getKdtId(),
                  type: 'tag',
                  redirect_url: getCurrentRoute({
                    withSlash: true,
                    withQuery: true,
                  }),
                },
              });
              throw err;
            });
        }

        return new Promise((resolve) => {
          resolve(currentAlias);
        });
      },
      fetchProductList() {
        const { loading, page, order, orderBy, activeName, groupData } = this;

        if (loading) return;

        const showUmpAmbience = this.showUmpAmbience || false;

        this.setVal({
          loading: true,
        });
        // this.setYZData({
        //   loading: true,
        // });

        return app
          .request({
            path: '/wscshop/goods/goodsByTagAlias.json',
            data: {
              ...(this.tagParams || {}),
              page,
              needActivity: showUmpAmbience ? 1 : 0,
              needGroupFilter: groupData.length > 1,
              tagAlias: activeName,
              order_by: orderBy,
              supportFixGroupOptionalCombo: true,
              ...(order ? { order } : {}),
            },
          })
          .then((rs) => {
            this.setListData(rs);
          })
          .catch((response) => {
            this.setVal({
              loading: true,
            });
            // this.setYZData({
            //   loading: false,
            // });
            Toast(response.msg || '没有找到商品哦～');
            // 处理接口异常时，enterpage也需埋点上报
            // mixin ??
            context.triggerLogger('0', 't');
          });
      },
      handleItemClick(e) {
        context.handleItemClick(e);
      },
      showcaseHandleGoodsBuy(e) {
        context.showcaseHandleGoodsBuy(e);
      },
      handleFilterChange(e) {
        context.handleFilterChange(e);
      },
      handleItemImageLoaded() {
        app.logger.log({
          et: 'view',
          ei: 'view_list_goods',
          en: '列表商品曝光',
        });
      },
      handleRootTap(e) {
        context.handleRootTap(e);
      },
    },
  });

  constructor({ ctx }) {
    ctx.store = this.store;
    context = this;
    this.ctx = ctx;
    Object.assign(
      this,
      mapState(
        this,
        [
          'id',
          'themeClass',
          'loading',
          'needLoadMore',
          'pageQuery',
          'filters',
          'shareImgUrl',
          'slgStatus',
          'goodsOpt',
          'uuid',
          'listQuery',
          'showTab',
          'navBarTitle',
          'canShowNavBar',
          'totalAlias',
          '__query__',
          'route',
        ],
        {
          setSelf: true,
        }
      ),
      viewLog,
      GoodsPageSku,
      GoodsPageFilterBar,
      Salesman,
      CommonMixin
    );
    mapActions(this, [
      'setVal',
      'fetchGroupPage',
      'fetchAlias',
      'fetchProductList',
      'getTagBaseInfo',
      'setListData',
      'handleItemImageLoaded',
    ]);
    this.ctx.data.cpsConfigKey = 'cps_goods_recommend_goods_group';
    this.ctx.data.pageType = 'goods-group';
    this.ctx.data.shopBlockInfo = {
      showServiceDue: false,
      showStoreSwitch: true,
      showShopStatus: true,
      switchPageOptions: {},
      buyerAddress: '',
      isRetailShelf: false,
      navigationbarConfigData: {},
    };
    this.store.watch('showTab', (val) => {
      this.ctx.data.showTab = val;
    });
    this.store.watch('tagComponents', (val) => {
      this.ctx.data.featureComponents = val;
    });
    this.ctx.watch('themeTag', (value) => {
      this.ctx.data.themeTagObj = value;
    });
    this.store.watch('stickyHeight', (val) => {
      this.ctx.data.stickyHeight = val;
    });

    const skylineInfo = wx.getSkylineInfoSync();
    this.ctx.data.isSkyline = skylineInfo?.isSupported;
  }

  beforePageCreate() {
    this.commonBeforePageCreate({
      slData: {
        sst: 8,
      },
    });
  }

  /**
   * 生命周期函数--监听页面加载
   */
  beforePageMount(options) {
    const { navBarHeight } = getBarHeight();
    this.setVal({
      navBarHeight,
      __query__: options.query,
      route: options.route,
    });
    // 店铺锁定检查
    const { shopMetaInfo = {} } = app.getShopInfoSync() || {};
    this.ctx.data.shopMetaInfo = JSON.parse(JSON.stringify(shopMetaInfo)); // 深拷贝一下 解决控制台报错
    if (shopMetaInfo.lock_status === SHOP_LOCK_STATUS.LOCK) {
      // eslint-disable-next-line @youzan/dmc/wx-check
      return wx.redirectTo({ url: '/packages/common/lock/index' });
    }
    this.showLoading();
    // 连锁重定向
    checkReLaunchShopSelect();
    // mixin的初始化逻辑
    this.filterBarOnLoad();
    this.skuOnLoad();
    this.commonOnLoad();
    // 多网点重新选择网点后要更新页面数据
    appEvent.on(
      'app:offlineId:change',
      () => {
        this.fetchGroupPage(options);
      },
      this
    );
    this.fetchGroupPage(options);
  }

  onPageShow() {
    this.commondOnShow();
  }

  onPageHide() {
    this.commonOnHide();
  }

  pageDestroyed() {
    this.commonOnUnload();
  }

  showLoading() {
    wx.showToast({
      title: '加载中',
      icon: 'loading',
    });
  }

  // /**
  //  * 页面上拉触底事件的处理函数
  //  */
  // onReachBottom() {
  //   console.log('goods-list-tag onReachBottom');
  //   const { needLoadMore, loading } = this;
  //   if (!needLoadMore) {
  //     Event.trigger('float-share:show' + getUniquePageKey(this));
  //   }
  //   if (!needLoadMore && !loading) {
  //     return;
  //   }
  //   this.fetchProductList();
  // }

  // onPageScroll(e) {
  //   console.log('goods-list-tag onPageScroll');
  //   if (e.scrollTop <= 0) return;
  //   Event.trigger('onPageScroll' + getUniquePageKey(this), e);
  // }

  // 增加分享钩子
  onShareAppMessage(e) {
    const { pageQuery = {} } = this;
    const { alias = '', title = '商品分组', subtitle = '商品分组' } = pageQuery;
    const sharePath = args.add('/packages/goods-list/tag/index', {
      alias,
      title,
      subtitle,
    });
    const result = {
      path: sharePath,
      title: subtitle || title || '商品分组',
    };
    if (this.shareImgUrl) {
      result.imageUrl = this.shareImgUrl;
    }
    return this.getShareData(result, { extra: e });
  }
}
