import { RantaWidget } from 'shared/common/base/ranta/widget';
import {
  TAG_CONFIG,
  ALL_GOODS_CONFIG,
  SERACH_RANTA_CONFIG,
} from '../../../../../common/constants';
import { mapData } from '@youzan/ranta-helper';
import { Hummer } from 'shared/utils/hummer';

const app = getApp();

const CONFIG_MAP = {
  TAG_CONFIG,
  ALL_GOODS_CONFIG,
  SERACH_RANTA_CONFIG,
};

RantaWidget({
  data: {
    kdt_id: app.getKdtId(),
    uuid: '',
    goodsOpt: ALL_GOODS_CONFIG,
  },

  properties: {
    pageType: {
      type: String,
      observer: 'pageTypeChange',
    },
  },
  attached() {
    mapData(this, ['uuid', 'goodsOpt']);
    this.ctx.event.listen('enterObserve', this.createObserve.bind(this));
  },
  methods: {
    pageTypeChange(val) {
      this.setYZData({
        goodsOpt: CONFIG_MAP[val],
      });
    },
    handleItemImageLoaded(e = {}) {
      // 这是列表页上的方法
      this.ctx.process.invoke('handleItemImageLoaded', e);
      const { isSkyline, pageType } = this.ctx.data;
      const { index, parentIndex } = e.detail?.payload;
      if (index === 0 && parentIndex === 0) {
        Hummer.mark?.log?.({
          tag: pageType,
          scene: ['route'],
          extra: { isSkyline },
        });
      }
      // 这是 prefetch 上的方法
      this.ctx.process.invoke('imgLoadSign');
    },
    handleItemClick(e) {
      this.ctx.process.invoke('handleItemClick', e);
    },
    showcaseHandleGoodsBuy(e) {
      this.ctx.process.invoke('showcaseHandleGoodsBuy', e);
    },
    createObserve() {
      this.ctx.process.invoke('itemObserve', {
        context: this,
        wrapClass: '.goods-list__observe',
      });
    },
  },
});
