{"name": "@wsc-shop/dc-goods", "extensionId": "@wsc-shop/dc-goods", "version": "0.0.1", "bundle": "<builtin>", "lifecycle": [], "framework": "weapp", "widget": {"provide": ["DcGoodsMain"]}, "data": {"consume": {"goodsOpt": ["r", "w"], "uuid": ["r", "w"], "isSkyline": ["r"], "pageType": ["r"]}}, "process": {"invoke": ["handleItemClick", "showcaseHandleGoodsBuy", "handleChangeLayoutType", "handleItemImageLoaded", "imgLoadSign", "itemObserve"]}, "event": {"listen": ["enterObserve"]}, "component": {"consume": ["GoodsTee"]}, "platform": ["weapp"]}