{"name": "@wsc-shop/filter-bar", "extensionId": "@wsc-shop/filter-bar", "version": "0.0.1", "bundle": "<builtin>", "lifecycle": [], "framework": "weapp", "widget": {"provide": ["FilterBarMain"], "consume": ["ThemeIcon"]}, "data": {"consume": {"filters": ["r", "w"], "themeClass": ["r", "w"], "goodsOpt": ["r", "w"]}}, "process": {"invoke": ["handleFilterChange", "handleChangeLayoutType"]}, "platform": ["weapp"]}