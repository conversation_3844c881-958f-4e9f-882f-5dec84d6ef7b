import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapData } from '@youzan/ranta-helper';
import { SKELETON_OPT } from '../../../../../common/constants';

RantaWidget({
  data: {
    filters: [
      { title: '综合', value: '' },
      { title: '上新', value: 'createdTime' },
    ],
    layoutType: 'two',
    themeClass: '',
  },

  properties: {
    isShowLayoutChange: {
      type: Boolean,
      value: false,
    },
  },

  methods: {
    handleFilterChange(e) {
      this.ctx.process.invoke('handleFilterChange', e);
    },
    handleChangeLayoutType() {
      // 切换展示一行两个/一行一个和加载时的骨架图
      const isTypeTwo = this.data.goodsOpt.layoutOpt.type === 'two';
      if (!this.data.goodsOpt.items.length) return;
      this.setYZData({
        layoutType: isTypeTwo ? 'one' : 'two',
      });
      const ske = {};
      if (!this.data.goodsOpt.isTagPage) {
        if (isTypeTwo) {
          ske.default = {
            ...SKELETON_OPT({
              oneHeight: 16,
              twoWidth: 16,
              threeWidth: 73,
              threeMargin: '60px 0 12px 0',
              areaHeight: 0,
            }),
            layoutType: 'one',
            layout: 'horizontal',
          };
        } else {
          ske.two = SKELETON_OPT();
        }
      }

      const layoutOpt = isTypeTwo
        ? {
            type: 'one',
            itemMargin: 16,
            layoutMargin: 12,
          }
        : {
            type: 'two',
            itemMargin: 8,
            layoutMargin: 8,
          };

      this.ctx.process.invoke('handleChangeLayoutType', {
        'goodsOpt.itemCardOpt.type': isTypeTwo ? 'card' : 'card',
        'goodsOpt.itemCardOpt.layout': isTypeTwo ? 'horizontal' : 'vertical',
        'goodsOpt.itemCardOpt.layoutType': isTypeTwo ? 'one' : 'two',
        'goodsOpt.layoutOpt': { ...this.data.goodsOpt.layoutOpt, ...layoutOpt },
        'goodsOpt.skeletonOpt': ske,
      });
    },
  },

  observers: {},

  attached() {
    this.setYZData({
      commonFilters: this.data.filters.map((filter) => ({ ...filter })),
      currentFilter: parseInt(this.data.defaultFilter, 10),
    });
    mapData(this, ['filters', 'themeClass', 'goodsOpt']);
  },
});
