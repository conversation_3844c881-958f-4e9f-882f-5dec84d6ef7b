import { RantaWidget } from 'shared/common/base/ranta/widget';

RantaWidget({
  data: {
    searchConfig: {
      border_style_color: '#f9f9f9',
      border_style_height: 40,
      border_style_method: '0',
      color: '#fff',
      hot_search_keys: [],
      hot_search_keys_new: [],
      position_show_method: '0',
      position_type: '0',
      show_search_component: '1',
      text_align_method: '0',
      text_color: '#999',
      type: 'search',
      z_index: 121,
    },
  },

  properties: {},

  observers: {},

  attached() {},

  methods: {},

  // behaviors: [pageComponentBehavior],
});
