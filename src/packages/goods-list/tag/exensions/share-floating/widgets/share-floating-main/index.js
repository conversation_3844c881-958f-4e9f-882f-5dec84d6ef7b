import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapData } from '@youzan/ranta-helper';

RantaWidget({
  data: {
    showShareFloating: false,
    listQuery: {},
    totalAlias: [],
    iconUrl: 'https://b.yzcdn.cn/goods_share/share-floating.png',
  },

  properties: {
    cloudConfig: {
      type: Object,
      observer: 'handleSetIconUrl',
    },
  },

  attached() {
    mapData(this, ['showShareFloating', 'listQuery', 'totalAlias']);
  },

  methods: {
    handleSetIconUrl(val) {
      if (val && val.icon) {
        this.setYZData({
          iconUrl: val.icon,
        });
      }
    },
  },
});
