// 一些从wsc-page  摘过来的逻辑
import appEvent from 'shared/utils/app-event';
import EventUtil from '@youzan/weapp-utils/lib/event';
import { logPageEnter } from 'utils/log/logv3';
import { recordGuideAndCustomerRelation } from 'utils/guide';
import args from '@youzan/weapp-utils/lib/args';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';
import { getGuideSecondSharerId } from 'packages/guide/utils';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import { initSalesman } from 'shared/utils/salesman-share';

const app = getApp();

export default {
  commonBeforePageCreate({ slData }) {
    app.waitForEnterShop().then((res) => {
      this.ctx.data.kdtId = res.kdtId;
      this.ctx.data.shopBaseInfo = {
        kdtId: res.kdtId,
      };
      const { shopMetaInfo = {} } = app.getShopInfoSync() || {};
      this.ctx.data.shopMetaInfo = JSON.parse(JSON.stringify(shopMetaInfo));
    });
    this.ctx.logger.setPageAsyncLog();
    this.store.watch('navBarTitle', (val) => {
      this.ctx.data.navBarTitle = val;
    });
    this.store.watch('canShowNavBar', (val) => {
      this.ctx.data.canShowNavBar = val;
    });
    this.store.watch('listQuery', (val) => {
      this.ctx.data.listQuery = val;
    });
    this.store.watch('filters', (val) => {
      this.ctx.data.filters = val;
    });
    this.store.watch('themeClass', (val) => {
      this.ctx.data.themeClass = val;
    });
    this.store.watch('totalAlias', (val) => {
      this.ctx.data.totalAlias = val;
    });
    this.store.watch('uuid', (val) => {
      this.ctx.data.uuid = val;
    });
    this.store.watch(
      'goodsOpt',
      (val) => {
        this.ctx.data.goodsOpt = val;
        this.ctx.data.pageBgColor =
          val.layoutOpt.type === 'list' ? '#fff' : '#f7f8fa';
      },
      { deep: true }
    );
    this.ctx.process.define('handleItemClick', (e) => {
      this.handleItemClick(e);
    });
    this.ctx.process.define('showcaseHandleGoodsBuy', (e) => {
      this.showcaseHandleGoodsBuy(e);
    });
    this.ctx.process.define('handleFilterChange', (e) => {
      this.handleFilterChange(e);
    });
    this.ctx.process.define('handleItemImageLoaded', (e) => {
      this.handleItemImageLoaded(e);
    });
    this.ctx.process.define('handleChangeLayoutType', (val) => {
      this.setVal({
        ...val,
      });
    });
    initSalesman({
      gst: 6,
      getSalesmanData: (err, data) => {
        if (err) return;
        this.ctx.data.salesmanShareData = data;
      },
      ...slData,
    });
  },
  commonOnLoad() {
    /**
     * 维护一个页面栈，用作事件回收
     * 小程序 getCurrentPages 没维护 tab 页的页面栈
     */
    console.log('minxinthis', this);
    if (app._routeStack) {
      app._routeStack.push(this.route);
    } else {
      app._routeStack = [this.route];
    }
  },
  commondOnShow() {
    const pages = getCurrentPages() || [{}];
    EventUtil.trigger('page:lifetimes:show', { route: this.route });
    appEvent.once('goodsTag:loaded', (options, { spmType } = {}) => {
      logPageEnter(...[this.route, this.id, options, spmType, pages]);
    });
    // 记录当前用户和导购
    recordGuideAndCustomerRelation({
      path: this.route,
      query: this.__query__,
    });
  },
  commonOnHide() {
    EventUtil.trigger('page:lifetimes:hide', { route: this.route });
  },
  commonOnUnload() {
    const pageIndex = (app._routeStack || []).findIndex(
      (item) => this && item === this.route
    );
    if (pageIndex !== -1) {
      app._routeStack.splice(pageIndex, 1);
    }

    appEvent.off(null, null);
  },
  getShareData(shareData, { extra = {} }) {
    const app = getApp();
    const loggerParams = shareData.loggerParams || {};

    app.logger.log({
      et: 'click',
      ei: 'share',
      en: '转发',
      params: {
        share_cmpt: 'native_custom',
        ...loggerParams,
      },
    });
    const SHARE_CMPT_MAP = {
      menu: 'native_wechat', // 右上角
      button: 'native_custom', // 按钮
    };

    // 获取分享来源
    const { from, target } = extra;
    const shareCmpt = SHARE_CMPT_MAP[from];
    // 拼装额外分享参数
    let sharePath = shareData.path;
    const allQueryData = args.getAll(sharePath);
    // 增加 ban_default_kdt_id 参数来禁用默认从 app.getKdtId() 中获取 kdt_id 的逻辑
    // 你可能好奇为什么不直接默认从 allQueryData 中获取 kdt_id, 据杨杰强大哥说就是为了修复一个问题才改成从 app.getKdtId()中取的
    const { ban_default_kdt_id: banDefaultKdtId } = allQueryData;
    let queryData = {
      ...allQueryData,
      ...defaultEnterShopOptions,
      is_share: 1,
      share_cmpt: shareCmpt,
    };

    // 导购员分享链接用户二次转发逻辑
    const guideSecondSharerId = getGuideSecondSharerId();
    if (guideSecondSharerId) {
      queryData.guide_second_sharer_id = guideSecondSharerId;
    }

    // 销售员统一处理分享操作
    const { salesmanShareData: slData } = this.ctx.data || {};
    if (slData && slData.share) {
      queryData = {
        ...queryData,
        ...getSalesmanParamsObject({ sl: slData.seller }),
      };
    }
    // 去除原query

    // 增加 kdt_id
    if (banDefaultKdtId === 'true') {
      queryData.kdt_id = allQueryData.kdt_id || app.getKdtId() || '';
    } else {
      queryData.kdt_id = app.getKdtId() || '';
    }

    // 增加 dc_ps 追踪
    // 增加埋点数据
    const logGlobalInfo = app.logger.getGlobal() || {};
    const contextInfo = logGlobalInfo.context || {};
    const userInfo = logGlobalInfo.user || {};

    if (contextInfo.dc_ps) {
      queryData.dc_ps = contextInfo.dc_ps || '';
    }

    if (userInfo.uuid) {
      queryData.from_uuid = userInfo.uuid || '';
    }

    // 门店id
    if (app.getShopInfoSync().isMultiStore) {
      queryData.offlineId = app.getOfflineId();
    }

    // 拼接分享query
    sharePath = args.add(sharePath, queryData);

    // 清除网点id，走自动进店逻辑
    if (shareData.noOfflineId) {
      sharePath = args.remove(sharePath, 'offlineId');
    }

    // 分享页面都通过 blank-page 来进行中转
    const newSharePath = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
      sharePath
    )}`;
    const finalShareData = {
      ...shareData,
      path: newSharePath,
    };

    if (target?.id === 'salesman') {
      finalShareData.promise = new Promise((reslove) => {
        setTimeout(() => {
          reslove(finalShareData);
          // 100毫秒会截到蒙层
        }, 300);
      });
    }
    return finalShareData;
  },
};
