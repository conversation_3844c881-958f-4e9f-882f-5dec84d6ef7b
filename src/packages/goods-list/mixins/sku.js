import { featureSkuUtilsPromise } from 'shared/utils/async-base-sku';
import omit from '@youzan/utils/object/omit';
import { jumpToLink } from 'shared/components/showcase-options-home/jump-to-link.js';

const app = getApp();
export default {
  onLoad() {
    this.on('component:sku:cart', (res) => {
      if (res.type === 'add') {
        // search页面 Toast不出来，先用wx.showToast;
        // Toast('添加购物车成功');
        wx.showToast({
          title: '添加购物车成功',
          icon: 'none',
        });
      }
    });
  },
  showcaseHandleGoodsBuy(e) {
    const {
      alias,
      activityTypes,
      loggerParams = {},
      id,
    } = e?.detail?.payload || {};
    // 购物策按钮点击-为加入购物车和立即购买提前设置 slg 上下文 透传埋点
    const { slg, alg } = loggerParams;
    app.logger.setContext(
      {
        slg: slg || '',
        alg_id: alg || '',
      },
      30
    );
    const extra = {
      goodsIdForBirthday: id,
    };

    featureSkuUtilsPromise()
      .then(({ getSkuData }) => getSkuData(alias, { activityTypes, ...extra }))
      .then((skuData) => {
        this.setYZData({
          featureSkuData: skuData,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  },
  handleSkuClose() {
    this.setYZData({
      'featureSkuData.showGoodsSku': false,
    });
  },
  handleItemClick(e) {
    const { link, loggerParams, goodsPreloadOpt } = e?.detail?.payload || {};
    this.__yzLog__({
      et: 'click',
      ei: 'click_goods_detail',
      en: '点击商品详情',
    });
    const {
      alg,
      slg,
      banner_id: bannerId,
      rank_no: rankNo,
      rank_type: rankType,
    } = loggerParams;
    link.query = omit(link.query, ['bannerId']);
    let rankLogger = {};
    if (rankNo && rankType) {
      rankLogger = {
        rank_type: rankType,
        rank_no: rankNo,
      };
    }

    jumpToLink(link, {
      ...rankLogger,
      banner_id: bannerId,
      alg,
      slg,
      goodsPreloadOpt,
    });
  },
};
