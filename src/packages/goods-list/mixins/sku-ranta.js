// import { featureSkuUtilsPromise } from 'shared/utils/async-base-sku';
import omit from '@youzan/utils/object/omit';
import { jumpToLink } from 'shared/components/showcase-options-home/jump-to-link.js';
import appEvent from 'shared/utils/app-event';
import Tee from '@youzan/tee';

const app = getApp();
export default {
  skuOnLoad() {
    // ? todo
    appEvent.on('component:sku:cart', (res) => {
      if (res && res.type === 'add') {
        // search页面 Toast不出来，先用wx.showToast;
        // Toast('添加购物车成功');
        wx.showToast({
          title: '添加购物车成功',
          icon: 'none',
        });
      }
    });
  },
  showcaseHandleGoodsBuy(e) {
    const {
      alias,
      id,
      activityTypes,
      loggerParams = {},
    } = e?.detail?.payload || {};
    // 购物策按钮点击-为加入购物车和立即购买提前设置 slg 上下文 透传埋点
    const { slg, alg } = loggerParams;
    app.logger.setContext(
      {
        slg: slg || '',
        alg_id: alg || '',
      },
      30
    );
    const extra = {
      goodsIdForBirthday: id,
      birthdayScene: 'WEAAPP_DECORATE_GOODS',
    };

    app.isSwitchTab().then((isTabPage) => {
      console.log('isTabPage', isTabPage);
      const containerStyle = `padding-bottom: ${
        isTabPage
          ? 'calc(env(safe-area-inset-bottom) + 50px)'
          : 'calc(safe-area-inset-bottom)'
      };z-index: 141;`;
      // this.ctx.data.containerStyle = containerStyle;
      this.ctx.data.themeVars = containerStyle;
      this.ctx.event.emit('featureSku:show', {
        ...e.detail.payload,
        alias,
        // containerStyle,
        activityTypes,
        goodsId: id,
        ...extra,
      });
    });
  },
  handleSkuClose() {
    this.setVal({
      'featureSkuData.showGoodsSku': false,
    });
  },
  handleItemClick(e) {
    Tee.setGlobal('goodsItemClick', Date.now());
    // e?.detail?.payload 和 goodsPreloadOpt 数据有重复，取值要注意
    const {
      link,
      loggerParams,
      goodsPreloadOpt,
      realUrl,
      activityTypes,
      isPresale,
    } = e?.detail?.payload || {};
    app.logger.log({
      et: 'click',
      ei: 'click_goods_detail',
      en: '点击商品详情',
    });
    const {
      alg,
      slg,
      banner_id: bannerId,
      rank_no: rankNo,
      rank_type: rankType,
    } = loggerParams;
    link.query = {
      ...omit(link.query, ['bannerId']),
      shopAutoEnter: 5,
    };
    let rankLogger = {};
    if (rankNo && rankType) {
      rankLogger = {
        rank_type: rankType,
        rank_no: rankNo,
      };
    }

    jumpToLink(link, {
      ...rankLogger,
      banner_id: bannerId,
      alg,
      slg,
      goodsPreloadOpt,
      activityTypes,
      isPresale,
      realUrl,
    });
  },
};
