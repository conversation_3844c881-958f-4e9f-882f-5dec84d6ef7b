const SORT_ORDER = {
  ascend: 'asc',
  descend: 'desc',
};

const app = getApp();

export default {
  onLoad() {
    app
      .request({
        path: '/wscshop/shop/config.json',
        data: {
          key: 'goods_detail_sales',
        },
      })
      .then(({ goods_detail_sales: goodsDetailSales = null }) => {
        if (!goodsDetailSales) {
          // 新店铺没有 goodsDetailSales
          app.getShopConfigData().then((shopConfig) => {
            const { buy_record: buyRecord = 0 } = shopConfig;
            this.computedFilters(buyRecord, false, 0);
          });
          return;
        }
        const {
          show = 0,
          limit = false,
          limit_num: limitNum = 0,
        } = JSON.parse(goodsDetailSales);
        this.computedFilters(show, limit, limitNum);
      });

    // 自定义售罄图标
    app.getShopConfigData().then((shopConfig = {}) => {
      this.soldOutGoodsImg = shopConfig.sold_out_goods_flag;
    });
  },

  computedFilters(show, limit, limitNum) {
    const filters = this.data.filters.slice();
    const offlineId = app.getOfflineId();
    const offlineSeparatePrice = app.getOfflineSeparatePrice();
    const filterPrice =
      +offlineId === 0 || (offlineId && +offlineSeparatePrice === 0);

    // 没开启多网点
    if (filterPrice) {
      filters.splice(1, 0, {
        title: '价格',
        value: 'activityPrice',
        type: 'sort',
        sortDirection: 'ascend',
      });
    }
    // 开启了销量
    if (+show === 1) {
      filters.splice(1, 0, { title: '销量', value: 'totalSoldNum' });
    }

    if (+show === 1 || filterPrice) {
      this.setYZData({
        filters,
        isLimitSold: limit,
        limitSoldNum: +limitNum,
        'goodsOpt.itemCardOpt.soldNumOpt': +show ? {} : null,
      });
    }
  },

  handleFilterChange({ detail: { filter } }) {
    // 当筛选参数变更时，需要重置totalAlias
    const resetTotalAlias =
      filter.value !== this.data.orderBy ||
      SORT_ORDER[filter.sortDirection] !== this.data.order;
    this.setYZData({
      page: 1,
      orderBy: filter.value,
      totalAlias: resetTotalAlias ? [] : this.data.totalAlias,
      order: SORT_ORDER[filter.sortDirection] || '',
    });

    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    });
    this.fetchProductList();
  },
};
