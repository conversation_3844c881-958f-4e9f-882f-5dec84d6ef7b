import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';

// 商品曝光埋点参数
const LOG_VIEW_PARAM = {
  // 有赞公益
  search: {
    et: 'view', // 事件类型
    ei: 'search_goods_view', // 事件标识
    en: '商品数据曝光-小程序', // 事件名称
    pt: 'GoodsSearch', // 页面类型
  },
  tags: {
    et: 'view', // 事件类型
    ei: 'tags_goods_view', // 事件标识
    en: '商品数据曝光-小程序', // 事件名称
    pt: 'GoodsTags', // 页面类型
  },
  allpro: {
    et: 'view', // 事件类型
    ei: 'all_goods_view', // 事件标识
    en: '商品数据曝光-小程序', // 事件名称
    pt: 'GoodsAll', // 页面类型
  },
};

const app = getApp();

export default {
  getUUid() {
    const { user: { uuid } = {} } = app.logger.getGlobal() || {};
    this.setYZData({
      uuid: uuid || '',
    });
  },
  setGoodsViewLog(data, type = 'search') {
    if (!data.length) return;
    // 商品列表-商品曝光 slg alg_id 上下文 透传埋点
    const extParams = {
      slg: (data[0].extraInfo && data[0].extraInfo.slg) || '',
      alg_id: (data[0].extraInfo && data[0].extraInfo.alg) || '',
    };
    if (type !== 'search') {
      extParams.words = '';
      extParams.words_type = '';
    }
    app.logger.setContext(extParams, 30);
    // 延迟埋点，保障slg和alg_id更新
    setTimeout(() => {
      this.__yzLog__(
        Object.assign(LOG_VIEW_PARAM[type], {
          params: {
            goods: this.formatData(data),
          },
        })
      );
      // 商品曝光
      ensureAppLogger(
        getComponentLoggerParams('view', this.formatLoggerList(data))
      );
    }, 1000);
  },
  formatData(data) {
    const result = data.map((goods = {}) => ({
      goods_id: (goods.actionOpt || {}).alias,
      goods_name: (goods.titleOpt || {}).title,
    }));
    return JSON.stringify(result);
  },
  formatLoggerList(data) {
    const loggerList = [];
    data.forEach((item = {}) => {
      loggerList.push((item.actionOpt || {}).loggerParams);
    });
    return loggerList;
  },
  triggerLogger(slg, spmType) {
    if (this.data.slgStatus) {
      this.setYZData(
        {
          slgStatus: false,
        },
        () => {
          this.trigger(
            'goodsTag:loaded',
            {
              slg,
            },
            {
              spmType,
            }
          );
        }
      );
    }
  },
};
