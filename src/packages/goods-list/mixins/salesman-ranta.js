const app = getApp();

export default {
  // TODO
  getSalesmanCard(list) {
    console.log('list', list);
    if (
      list.length > 0 &&
      !this.shareImgUrl &&
      list.some(
        (item) => item.salesmanRebatePrice || item.salesmanRebateMaxPrice
      )
    ) {
      const imgList = list.map((item) => item.imgOpt.src).slice(0, 2);
      app
        .request({
          path: '/wscgoodslist/weapp-poster/salesman-card.json',
          method: 'POST',
          data: {
            imgList,
          },
        })
        .then((data) => {
          this.setVal({
            shareImgUrl: data.value,
          });
        });
    }
  },
};
