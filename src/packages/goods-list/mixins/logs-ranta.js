import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';
// import appEvent from 'shared/utils/app-event';
import { checkCustomNav } from 'shared/utils/nav-config';
import { getClickPositionForLog } from 'pages/common/page-container/utils/index';
// 商品曝光埋点参数
const LOG_VIEW_PARAM = {
  // 有赞公益
  search: {
    et: 'view', // 事件类型
    ei: 'search_goods_view', // 事件标识
    en: '商品数据曝光-小程序', // 事件名称
    pt: 'GoodsSearch', // 页面类型
  },
  tags: {
    et: 'view', // 事件类型
    ei: 'tags_goods_view', // 事件标识
    en: '商品数据曝光-小程序', // 事件名称
    pt: 'GoodsTags', // 页面类型
  },
  allpro: {
    et: 'view', // 事件类型
    ei: 'all_goods_view', // 事件标识
    en: '商品数据曝光-小程序', // 事件名称
    pt: 'GoodsAll', // 页面类型
  },
};

const app = getApp();

export default {
  getUUid() {
    const { user: { uuid } = {} } = app.logger.getGlobal() || {};
    this.setVal({
      uuid: uuid || '',
    });
  },
  setGoodsViewLog(data, type = 'search') {
    if (!data.length) return;
    // 商品列表-商品曝光 slg alg_id 上下文 透传埋点
    const extParams = {
      slg: (data[0].extraInfo && data[0].extraInfo.slg) || '',
      alg_id: (data[0].extraInfo && data[0].extraInfo.alg) || '',
    };
    if (type !== 'search') {
      extParams.words = '';
      extParams.words_type = '';
    }
    app.logger.setContext(extParams, 30);
    // 延迟埋点，保障slg和alg_id更新
    // ? todo
    setTimeout(() => {
      app.logger.log(
        Object.assign(LOG_VIEW_PARAM[type], {
          params: {
            goods: this.formatData(data),
          },
        })
      );
      // 商品曝光
      ensureAppLogger(
        getComponentLoggerParams('view', this.formatLoggerList(data))
      );
    }, 1000);
  },
  formatData(data) {
    const result = data.map((goods = {}) => ({
      goods_id: (goods.actionOpt || {}).alias,
      goods_name: (goods.titleOpt || {}).title,
    }));
    return JSON.stringify(result);
  },
  formatLoggerList(data) {
    const loggerList = [];
    data.forEach((item = {}) => {
      loggerList.push((item.actionOpt || {}).loggerParams);
    });
    return loggerList;
  },
  triggerLogger(slg, spmType) {
    console.log('slg', slg);
    console.log('smTYpe', spmType);
    console.log('slgStatus', this.slgStatus);
    if (this.slgStatus) {
      this.setVal({
        slgStatus: false,
      });
      console.log(this.slgStatus);
      setTimeout(() => {
        this.ctx.logger.setPageInitConfig({
          pageType: spmType,
          eventParams: {
            slg,
          },
        });
      }, 0);
    }
  },
  handleRootTap(event) {
    if (!this.route) {
      const pages = getCurrentPages() || [];
      this.route = pages[pages.length - 1].route;
    }
    const supportCustomNav = checkCustomNav(this.route);

    const pos = getClickPositionForLog(event, {
      supportCustomNav,
    });

    if (pos === null) {
      return;
    }

    app.logger.log({
      et: 'custom',
      ei: 'hot_click',
      en: '热力图点击',
      si: app.getKdtId(),
      params: {
        is_fix: 0,
        ...pos,
      },
    });
  },
};
