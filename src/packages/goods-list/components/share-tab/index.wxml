<view
  class="all-goods__nav"
  style="height: {{ navBarHeight }};"
>
  <view
    class="all-goods__nav-tab"
    style="padding-top: {{ navPaddingTop }}"
  >
    <view class="{{hasLastRoute?'all-goods__nav-tab-btn':'all-goods__nav-tab-btn-single'}}" wx:if="{{ navPaddingTop }}">
      <van-icon
        wx:if="{{ hasLastRoute }}"
        bind:tap="toBack" 
        size="21"
        name="https://img01.yzcdn.cn/upload_files/2024/05/22/Fu7bkgyGmlEwCPYRD7Kbs0hhGBr8.png"
        class="all-goods__nav-tab-btn-back" 
      />
      <van-icon 
        bind:tap="toHome" 
        size="25" 
        name="https://b.yzcdn.cn/goods_share/home_black.png" 
        class="all-goods__nav-tab-btn-home" 
      />
    </view>
    <view wx:if="{{ hasLoadAll }}">
      <view wx:if="{{ showTab }}">
        <view class="all-goods__nav-tab-tab all-goods__nav-tab-tab-active">列表<view class="all-goods__nav-tab-tab-active-line"></view> </view>
        <view
          class="all-goods__nav-tab-tab"
          style="margin-left: 20px"
          bind:tap="onGotoVideoList"
        >推荐</view>
      </view>
      <view wx:else class="all-goods__nav-tab-title">
        {{ navBarTitle }}
      </view>
    </view>
  </view>
</view>