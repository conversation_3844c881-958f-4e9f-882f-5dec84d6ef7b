.all-goods__nav {
  width: 100%;
  background-color: #fff;
}

.all-goods__nav-tab {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  height: 44px;
}

.all-goods__nav-tab-btn {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border: 1px solid hsla(0,0%,89.8%,.6);
  box-sizing: border-box;
  background: hsla(0,0%,100%,.6);
  border-radius: 86px;
  width: 87px;
  height: 32px;
  left: 7px;
}
.all-goods__nav-tab-btn::after {
  content: '';
  height: 20px;
  border-left: 1px solid hsla(0,0%,89.8%,.6);
  position: absolute;
  left: 50%;
}
.all-goods__nav-tab-btn-single {
  position: absolute;
  left: 10px;
}

.all-goods__nav-tab-tab {
  display: inline-block;
  color: #969799;
  font-weight: bold;
  left: 7px;
}

.all-goods__nav-tab-tab-active {
  color: #000;
}

.all-goods__nav-tab-tab-active-line {
  border-radius: 1.5px;
  margin-top: 5px;
  background-color: #000;
  width: 30px;
  height: 3px;
  position: absolute;
  bottom: 0;
}

.all-goods__nav-tab-title {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}