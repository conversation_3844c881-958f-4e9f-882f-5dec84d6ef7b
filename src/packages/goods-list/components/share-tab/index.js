import WscComponent from 'pages/common/wsc-component/index';
import { getPlugins } from '@youzan/ranta-helper-tee';
import { checkWscSingleStore } from '@youzan/utils-shop';
import { getBarHeight } from '../../common/utils';

const app = getApp();

WscComponent({
  properties: {
    shareFrom: {
      type: Number,
    },
    show: {
      type: Boolean,
    },
    groupTitle: {
      type: String,
    },
    canShowNavBar: {
      type: Boolean,
    },
    listQuery: {
      type: Object,
    },
    totalAlias: {
      type: Array,
      value: [],
    },
  },

  data: {
    navBarHeight: '',
    navPaddingTop: '',
    navBarTitle: '',
    showTab: false,
    hasLoadAll: false,
    hasLastRoute: false,
  },

  observers: {
    show(val) {
      if (val) {
        this.setShowTab();
      }
    },
    canShowNavBar(val) {
      // 当已加载数据但并不符合加载悬浮窗条件时
      if (val && !this.data.show) {
        this.setYZData({
          hasLoadAll: true,
        });
      }
    },
    groupTitle(val) {
      this.setYZData({
        navBarTitle: val,
      });
    },
  },

  attached() {
    const { navBarHeight, statusBarHeight } = getBarHeight();
    const { shareFrom, groupTitle } = this.data;
    const pages = getCurrentPages();
    this.setYZData({
      hasLastRoute: pages.length > 1,
      navBarHeight: navBarHeight + 'px',
      navPaddingTop: statusBarHeight + 'px',
      navBarTitle: shareFrom === 4 ? '全部商品' : groupTitle,
    });
  },

  methods: {
    toHome() {
      getPlugins().dmc.switchTab('Home', { kdt_id: app.getKdtId() });
    },
    toBack() {
      const { dmc } = getPlugins();
      dmc.back();
    },
    setShowTab() {
      const kdtId = app.getKdtId();
      const { shareFrom } = this.data;

      app
        .request({
          path: '/wscshop/shop/shop-meta-info.json',
          data: { kdt_id: kdtId },
        })
        .then((info) => {
          const isWscSingleStore = checkWscSingleStore(info);
          if (isWscSingleStore) {
            app.logger.log({
              et: 'view',
              ei: 'view_recommend_tab',
              en: '推荐tab曝光',
              params: {
                share_from: shareFrom,
              },
            });
          }
          this.setYZData({
            showTab: isWscSingleStore,
            hasLoadAll: true,
          });
        });
    },
    onGotoVideoList() {
      const { shareFrom, totalAlias, listQuery } = this.data;
      app.logger.log({
        et: 'click',
        ei: 'click_recommend_tab',
        en: '推荐tab点击',
        params: {
          share_from: shareFrom,
        },
      });
      getPlugins().dmc.navigate('Share', {
        shareFrom,
        alias: totalAlias[0],
        listQuery: encodeURIComponent(JSON.stringify(listQuery)),
      });
    },
  },
});
