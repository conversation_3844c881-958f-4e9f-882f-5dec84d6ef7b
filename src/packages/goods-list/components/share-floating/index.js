import WscComponent from 'pages/common/wsc-component';
import IconController from 'components/showcase/components/floating-nav/icon-controller/index';
import { checkWscSingleStore } from '@youzan/utils-shop';
import { getPlugins } from '@youzan/ranta-helper-tee';

const GUIDE_KEY = 'share_floating_guide';
const SHARE_FROM = 5;
const app = getApp();

WscComponent({
  properties: {
    // icon展示
    show: {
      type: Boolean,
      value: false,
    },
    listQuery: {
      type: Object,
    },
    totalAlias: {
      type: Array,
      value: [],
    },
    iconUrl: {
      type: String,
      value: 'https://b.yzcdn.cn/goods_share/share-floating.png',
    },
  },

  data: {
    guideImgUrl: 'https://b.yzcdn.cn/goods_share/share_guide.gif',
    showGuide: false,
    showFloating: false,
  },

  observers: {
    show(show) {
      if (show) {
        this.iconController = new IconController().setIcon(this, {
          priority: 80,
          height: 50,
          cb: [
            (bottom) => {
              this.setYZData({
                bottom: bottom + 'px',
              });
            },
            (goaway) => {
              this.setYZData({ goaway });
            },
          ],
        });

        const canShowGuide = !app.storage.get(GUIDE_KEY);

        app
          .request({
            path: '/wscshop/shop/shop-meta-info.json',
            data: { kdt_id: app.getKdtId() },
          })
          .then((data) => {
            const isWscSingleStore = checkWscSingleStore(data);
            // 需要微商城单店 & 缓存里没有展示记录 才能展示
            const showGuide = isWscSingleStore && canShowGuide;
            if (isWscSingleStore) {
              app.logger.log({
                et: 'view',
                ei: 'view_share_floating',
                en: '悬浮icon曝光',
                params: {
                  share_from: SHARE_FROM,
                },
              });
            }
            this.setYZData({
              showFloating: isWscSingleStore,
            });

            if (showGuide) {
              setTimeout(() => {
                app.storage.set(GUIDE_KEY, true);
                this.setYZData({
                  showGuide,
                });
              }, 200);
            }
          });
      }
    },
  },

  attached() {},

  methods: {
    toVideoList() {
      app.logger.log({
        et: 'view',
        ei: 'click_share_floating',
        en: '悬浮icon点击',
        params: {
          share_from: SHARE_FROM,
        },
      });

      const query = this.data.listQuery || {};
      const keyword = decodeURIComponent(query.keyword || '');
      // 对于order_by默认为'', 此时需要删除order_by, 否则接口会报错
      if (!query.order_by) {
        delete query.order_by;
      }

      getPlugins().dmc.navigate('Share', {
        alias: this.data.totalAlias[0],
        shareFrom: SHARE_FROM,
        listQuery: encodeURIComponent(
          JSON.stringify({
            ...query,
            keyword,
          })
        ),
      });
    },
    onCloseGuide() {
      this.setYZData({
        showGuide: false,
      });
    },
  },
});
