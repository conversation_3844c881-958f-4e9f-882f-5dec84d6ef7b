import WscComponent from 'pages/common/wsc-component/index';

const SORT_ASCEND = 'ascend';
const SORT_DESCEND = 'descend';

WscComponent({
  externalClasses: ['theme-class', 'theme-color', 'theme-border-color'],
  properties: {
    filters: {
      type: Object,
      value: [],
    },
    defaultFilter: {
      type: String,
      value: 0,
    },
    color: {
      type: String,
      value: '#ff4e4d',
    },
    currentLayoutType: {
      type: String,
      value: 'two',
    },
    isShowLayoutChange: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    commonFilters: [],
    currentFilter: 0,
  },

  ready() {
    setTimeout(() => {
      // 延迟400ms，因为van-sticky是异步组件
      getApp().trigger('first-render', 'goods-list');
    }, 400);
  },

  attached() {
    this.setYZData({
      commonFilters: this.data.filters.map((filter) => ({ ...filter })),
      currentFilter: parseInt(this.data.defaultFilter, 10),
    });
  },

  methods: {
    handleClick({
      currentTarget: {
        dataset: { index },
      },
    }) {
      index = parseInt(index, 10);

      const { filters, currentFilter } = this.data;
      const tempFilters = filters.slice();
      const nowFilter = { ...filters[index] };

      if (currentFilter === index && nowFilter.type !== 'sort') {
        // 如果点击的是同一个过滤器，并且不是 sort 类型，则不作处理
        return;
      }

      // 调整排序方向
      if (nowFilter.type === 'sort') {
        if (currentFilter !== index) {
          // 如果是新点击，则重置默认排序
          this.resetSortDirection(nowFilter, index);
        } else if (nowFilter.sortDirection === SORT_DESCEND) {
          // 如果是降序，则改为升序
          nowFilter.sortDirection = SORT_ASCEND;
          tempFilters.splice(index, 1, nowFilter);
          this.setYZData({
            filters: tempFilters,
          });
        } else {
          // 如果是升序，则改为降序
          nowFilter.sortDirection = SORT_DESCEND;
          tempFilters.splice(index, 1, nowFilter);
          this.setYZData({
            filters: tempFilters,
          });
        }
      }

      this.setYZData({
        currentFilter: index,
      });

      this.triggerEvent('change', { filter: nowFilter, index });
    },

    handleChangeLayoutType() {
      this.triggerEvent('handleChangeLayoutType');
    },

    // 重置默认排序方向
    resetSortDirection(filter, index) {
      const tempFilters = this.data.filters.slice();

      filter.sortDirection = filter.defaultDirection || SORT_ASCEND;
      tempFilters.splice(index, 1, filter);
      this.setYZData({
        filters: tempFilters,
      });
    },
  },
});
