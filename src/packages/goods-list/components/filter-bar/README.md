## FilterBar 过滤器

### 代码演示

#### 基础用法

```html
<cap-filter-bar
  filters="{{filters}}"
  default-filter="1"
  color="#a6c"
  bindchange="handleChange"
>

</cap-filter-bar>

<div style="{{ margin: '15px' }}">
  {{ content }}
</div>

<script>
export default {
  data() {
    return {
      content: '富强、民主、文明、和谐'
    }
  },

  methods: {
    handleChange({ detail: { filter, index } }) {
      switch (index) {
        case 0:
          this.content = '富强、民主、文明、和谐';
          break;
        case 1:
          this.content = '自由、平等、公正、法治';
          break;
        case 2:
          if (filter.sortDirection === 'ascend') {
            this.content = '好好学习';
          } else {
            this.content = '天天向上';
          }
          break;
        case 3:
          this.content = '爱国、敬业、诚信、友善';
          break;
      }
    }
  }
}
</script>
```

### API

#### filter-bar

| 参数       | 说明      | 类型       | 默认值       | 可选值      |
|-----------|-----------|-----------|-------------|-------------|
| filters | 过滤器 | `Array`  | `[]` |  |
| default-filter | 默认选中的过滤器 | `String` | `0` |  |
| color | 文字颜色 | `String` | `#ff4e4d` |  |

### 数据格式

#### filters 的数据格式

```js
[
  {
    title: '综合'
  },
  {
    title: '销量'
  },
  {
    title: '价格',
    type: 'sort',
    defaultDirection: 'ascend'     // 非必须。ascend or descend，默认为 ascend
  },
  {
    title: '上新'
  }
]
```

#### change 事件中返回的 filter 数据结构

如果 `type` 是 `sort`，则返回的 `filter` 会有一个 `sortDirection` 属性，用于标记升序 `ascend` 还是降序 `descend`。

```js
{
  title: '价格',
  type: 'sort',
  defaultDirection: 'ascend'
  sortDirection: 'ascend'     // 实际的排序方向
}
```
