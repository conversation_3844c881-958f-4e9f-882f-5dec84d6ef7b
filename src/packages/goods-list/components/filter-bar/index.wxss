.cap-filter-bar {
  display: flex;
  align-items: stretch;
  height: 40px;
  list-style-type: none;
  background-color: white;
  border-left: 1px solid #f1f1f1;
  border-right: 1px solid #f1f1f1;
  font-size: 14px;
  color: #717171;
}

.cap-filter-bar__item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
}

.cap-filter-bar__item--active {
  color: var(--icon, #323233);
}

.cap-filter-bar__sort-icon {
  position: relative;
}

.cap-filter-bar__sort-icon__ascend {
  content: '';
  display: block;
  position: absolute;
  top: -4px;
  left: 5px;
  width: 3px;
  height: 3px;
  border-top: 1px solid #bbb;
  border-left: 1px solid #bbb;
  transform: rotate(45deg);
}

.cap-filter-bar__sort-icon__descend {
  content: '';
  display: block;
  position: absolute;
  bottom: -5px;
  left: 5px;
  width: 3px;
  height: 3px;
  border-bottom: 1px solid #bbb;
  border-right: 1px solid #bbb;
  transform: rotate(45deg);
}

.cap-filter-bar__item--active .cap-filter-bar__sort-icon--ascend .cap-filter-bar__sort-icon__ascend {
  border-color: var(--icon, #323233);
}

.cap-filter-bar__item--active .cap-filter-bar__sort-icon--descend .cap-filter-bar__sort-icon__descend {
  border-color: var(--icon, #323233);
}

.layout-icon {
  width: 48px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
