<view class="cap-filter-bar theme-class">
  <view
    wx:if="{{ filters.length > 0 }}"
    wx:for="{{ filters }}"
    wx:key="{{ index }}"
    wx:for-item="filter"
    class="cap-filter-bar__item {{ filter.type === 'sort' ? 'cap-filter-bar__item--sort' : '' }} {{ index === currentFilter ? 'cap-filter-bar__item--active' : '' }}"
    bindtap="handleClick"
    data-index="{{ index }}"
  >
    {{ filter.title }}
    <view
      wx:if="{{ filter.type === 'sort' }}"
      class="cap-filter-bar__sort-icon cap-filter-bar__sort-icon--{{ filter.sortDirection }}"
    >
      <view
        class="cap-filter-bar__sort-icon__ascend"
      />
      <view
        class="cap-filter-bar__sort-icon__descend"
      />
    </view>
  </view>
  <view class="layout-icon" bindtap="handleChangeLayoutType" wx:if="{{isShowLayoutChange}}">
    <van-icon size="18px" name="{{currentLayoutType === 'two' ? 'list-switching' : 'list-switch'}}" />
  </view>
</view>
