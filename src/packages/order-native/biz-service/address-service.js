import isPhone from '@youzan/weapp-utils/lib/validators/is-phone';
import { queryGeo } from '@/packages/order-native/utils/request';

const app = getApp();

const DEFAULT_CONFIG = {
  skipKdtId: true,
  skipShopInfo: true,
};

export default class AddressService {
  constructor(store) {
    this.$store = store;
    this.resolveSaveAddress = null;
    this.rejectSaveAddress = null;
  }

  /*
   * id: 232500018
   * userName: "1111"
   * tel: "15157124426"
   * province: "北京市"
   * city: "北京市"
   * county: "朝阳区"
   * addressDetail: "1111"
   * areaCode: ""
   * lat: 39.95441389614754
   * lon: 116.60768694592969
   * postalCode: ""
   * country: ""
   * isDefault: false
   * notVerifyTel: false
   */
  saveAddress(address, _addressList) {
    const saveAddressPromise = new Promise((resolve, reject) => {
      this.resolveSaveAddress = resolve;
      this.rejectSaveAddress = reject;
    });

    const items = ['userName', 'tel', 'areaCode', 'addressDetail'];
    const error = {
      errorInfo: {},
      msg: '',
    };
    const isValid = items.every((item) => {
      const msg = this.getErrorMessageByKey(address, item);
      if (msg) {
        error.errorInfo[item] = true;
        error.msg = msg;
      }
      return !msg;
    });

    if (!isValid) {
      this.rejectSaveAddress(error);
      return saveAddressPromise;
    }

    if (this.isOverseaAddress(address)) {
      // 海外地址不需要经纬度信息 直接保存
      this.requestSave(address);
    } else if (address.lat && address.lon) {
      // 有经纬度直接保存
      this.requestSave(address);
    } else {
      // 查询经纬度
      queryGeo(address)
        .then(({ lat, lon } = {}) => {
          address.lon = lon;
          address.lat = lat;
          this.requestSave(address);
        })
        .catch(() => {
          this.requestSave(address);
        });
    }

    return saveAddressPromise;
  }

  // 请求后端接口保存地址信息
  requestSave(address) {
    const method = address.id ? 'update' : 'add';

    return app
      .request({
        origin: 'cashier',
        method: 'POST',
        path: `/wsctrade/uic/address/${method}Address.json`,
        data: address,
        config: DEFAULT_CONFIG,
      })
      .then((res) => {
        this.resolveSaveAddress && this.resolveSaveAddress(res);
        return res;
      })
      .catch((err) => {
        err.msg = err.msg || '地址保存失败';
        this.resolveSaveAddress && this.rejectSaveAddress(err);
        return err;
      });
  }

  getErrorMessageByKey(address, key) {
    const value = String(address[key] || '').trim();

    switch (key) {
      case 'userName':
        if (value && value.length > 15) {
          return '收货姓名不能超过 15 个字';
        }
        return value ? '' : '请填写姓名';
      case 'tel':
        return this.telValidator(address, value) ? '' : '请填写正确的电话';
      case 'areaCode':
        if (!address?.county?.length && value[0] !== '9') {
          return '请补全所在地区信息';
        }
        return value ? '' : '请选择地区';
      case 'addressDetail':
        return value ? '' : '请填写详细地址';
      case 'postalCode':
        return value && !/^\d{6}$/.test(value) ? '邮政编码格式不正确' : '';
      default:
        return '';
    }
  }

  // 境外地址校验
  telValidator(address, phone) {
    // notVerifyTel=true, 不进行手机号校验
    if (address.notVerifyTel) {
      return true;
    }

    // 海外地址单独的校验规则
    if (this.isOverseaAddress(address)) {
      return phone && phone.length <= 20;
    }
    return isPhone(phone);
  }

  // 是否为海外地址
  isOverseaAddress(address) {
    return address && address.areaCode && address.areaCode[0] === '9';
  }
}
