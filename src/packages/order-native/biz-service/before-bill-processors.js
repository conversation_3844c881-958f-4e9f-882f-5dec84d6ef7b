import Dialog from '@vant/weapp/dist/dialog/dialog';
import validate from '@/packages/order-native/common/validator';
import Toast from '@vant/weapp/dist/toast/toast';
import config from '../common/config';
import { getOrderItemsParam } from '../store/params';

let state;
let getters;
let dispatch;
let commit;

const _fn = () => {};

export function initContext({
  state: _state,
  getters: _getters,
  commit: _commit,
  dispatch: _dispatch,
}) {
  state = _state || {};
  getters = _getters || {};
  commit = _commit || _fn;
  dispatch = _dispatch || _fn;
}

/**
 * 不可使用预付卡
 */
export function handleDisablePrepayCard() {
  return new Promise((resolve, reject) => {
    if (!getters.orderCreated && getters.isDisablePrepayCard) {
      // 不可使用预付卡
      Dialog.confirm({
        message: '部分商品不支持 储值卡/礼品卡 与其他支付方式叠加使用',
        confirmButtonText: '知道了',
        showCancelButton: false,
      })
        .then(() => {
          commit('RESET_PREPAY_CARD_CHECK_STATUS');
          commit('SET_ORDER_FORBID_COUPON', false);
          commit('SET_ORDER_FORBID_PREFERENCE', false);
          dispatch('FETCH_SHOW');
          reject(false);
        })
        .catch(() => {
          reject(false);
        });
    } else {
      resolve(true);
    }
  });
}

/**
 * 参数校验
 */
export function paramsValidator() {
  return new Promise((resolve, reject) => {
    validate({ state, getters, dispatch }) ? resolve(true) : reject(false);
  });
}

export function handleContact() {
  const { contact = {}, version } = state;

  if (version === 2 && contact.required && !contact.id) {
    return dispatch('SAVE_CONTACT', {
      userName: contact.userName,
      telephone: contact.telephone,
    });
  }

  return Promise.resolve(true);
}

/**
 * 核销外部券 & 积分
 */
export function handleExternalConpon() {
  const { points } = state;
  const { chosenCoupon } = getters;
  return new Promise((resolve) => {
    if (
      chosenCoupon.outer === 1 ||
      (points.externalPoint && points.totalPoints)
    ) {
      dispatch('USE_EXTERNAL_COUPON').then(resolve).catch(resolve);
    } else {
      resolve(true);
    }
  });
}

/**
 * 海淘身份信息验证
 */
export function handleOverseaBuyRealNameAuth() {
  return new Promise((resolve, reject) => {
    // 实名校验 店铺条数不足 或 尝试次数过多
    Dialog.confirm({
      title: '身份信息不一致',
      message: '为保障海淘商品顺利清关，请确保姓名与身份证号保持一致。',
      confirmButtonText: '去修改',
      cancelButtonText: '继续付款',
      showCancelButton: true,
    })
      .then(() => {
        // this.stopLoading();
        commit('SET_VERIFY_FAIL', false);
        wx.navigateTo({
          url: '/packages/order-native/idcard/index',
        });
        // 跳到验证页面，中断下单操作
        reject(false);
      })
      .catch(() => {
        // 允许继续下单
        resolve(true);
      });
  });
}

/**
 * 实物定金预售确认
 */
export function handlePresaleConfirm() {
  return new Promise((resolve, reject) => {
    const { version } = state;
    if (version === 2) {
      Toast('请了解并同意定金不退协议，否则无法提交订单');
    } else {
      Toast('请同意预售商品不退定金');
    }
    reject('PRESALE_CONFIRM'); // 没有同意预售商品不退定金
  });
}

/**
 * 下单限流
 */
export function handleOrderLimit() {
  return new Promise((resolve, reject) => {
    const { order } = state;
    // 下单限流，直接返回
    if (order.limitCreate) {
      reject(false);
    } else {
      resolve(true);
    }
  });
}

export function handleValueCard() {
  // !(新储值推荐开启 && 存在储值推荐 && 非 0元单) 跳过选择，直接下单
  if (
    !(
      state.display.newRecommend &&
      state.payAssetActivityTagDesc &&
      getters.orderFinalPrice !== 0
    )
  ) {
    return Promise.resolve(true);
  }

  // 不存在默认提交订单栏时（被有赞云定制等情况），不走储值推荐
  if (!state.display.hasSubmit) {
    return Promise.resolve(true);
  }

  // isStoreAndPayProcess 是 boolean 说明已经做过选择，进行下单
  if (typeof state.order.config.isStoreAndPayProcess === 'boolean') {
    return Promise.resolve(true);
  }

  // 否则要阻断下单并进行选择
  getApp().trigger(config.eventKey.showTradeRecharge);
  return Promise.reject(false);
}

export function handleLiveCouponExt() {
  const goods = getOrderItemsParam(state, getters);

  const { source } = state.order;

  let isLiveScene;
  if (Array.isArray(source.itemSources)) {
    isLiveScene = source.itemSources.find((item) => {
      try {
        const bizTracePointExt = JSON.parse(item.bizTracePointExt);
        const { scene } = bizTracePointExt;
        return scene === 1177; // TODO
      } catch (e) {
        return false;
      }
    });
  }

  if (isLiveScene) {
    return dispatch('USE_LIVE_EXT', {
      goodsIds: goods.map(({ goodsId }) => goodsId),
    });
  }

  return Promise.resolve(true);
}
/**
 * 下单页接入行为组件，对于异常行为弹起滑块进行校验，行为组件返回onSuccess才能继续往下走
 */
export function handleBehaviorVerify() {
  const { order = {} } = state;
  const behaviorParam = {
    platform: order.source?.platform || 'weapp',
    buyer_id: order.buyer?.buyerId || 0,
    order_receiver_phone: order.buyer?.buyerPhone || '',
    book_key: order.bookKey || '',
    kdtId: order.seller?.kdtId || 0,
  };
  const bizType = 158;
  Toast.clear();
  return new Promise((resolve, reject) => {
    import('@youzan/behavior-verify/src/miniapp/weapp/main').then(
      ({ default: behaviorVerify }) => {
        behaviorVerify({
          bizType,
          bizData: JSON.stringify(behaviorParam) || '',
          onSuccess: (token) => {
            // 自己的业务逻辑
            this.$commit('SET_BEHAVIOR_INFO', {
              bizType,
              token,
            });
            resolve(true);
          },
          onCancel: () => {
            reject(false);
          },
          onFail: (error) => {
            console.log(error);
            reject(false);
          },
        });
      }
    );
  });
}
