/** 代理 Promise 的 then 方法，使得这个 Promise 在 pending 状态时最多只接收一个 then 回调 */
export function switchMap(promise) {
  let currentOnFulfilled;

  promise.then = function (onfulfilled) {
    return new Promise((resolve) => {
      currentOnFulfilled = onfulfilled;
      Promise.prototype.then.call(promise, (value) => {
        if (currentOnFulfilled === onfulfilled) {
          resolve(onfulfilled(value));
        }
      });
    });
  };

  return promise;
}
