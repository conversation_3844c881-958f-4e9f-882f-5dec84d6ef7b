import getApp from 'shared/utils/get-safe-app';
import asyncEvent from 'shared/utils/async-event';

function triggerAsync(name, args) {
  return asyncEvent.triggerAsync.apply(getApp(), [name].concat(args));
}

/**
 * 回调钩子
 */
export function executeBeforeCreateOrderHooks(params) {
  return new Promise((resolve, reject) => {
    triggerAsync('beforeCreateOrder', params)
      .then(() => {
        resolve(true);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

/**
 * 回调钩子(同步，不关心结果)
 */
export function executeAfterCreateOrderHooks(args) {
  const outArgs = { orderCreated: false, orderNo: '' };
  if (args) {
    const orderNos = args.orderNos || [];
    Object.assign(outArgs, {
      orderCreated: !!(Array.isArray(orderNos) && orderNos.length),
      orderNo: orderNos[0] || args.orderNo || ''
    });
  }

  return new Promise((resolve) => {
    triggerAsync('afterCreateOrder', outArgs)
      .then(() => {
        resolve(true);
      })
      .catch(() => {
        // 不关心 不处理
        resolve(true);
      });
  });
}

/**
 * 回调钩子
 */
export function executeOnPayItemClickHooks(params) {
  return new Promise((resolve, reject) => {
    triggerAsync('onPayItemClick', params)
      .then(() => {
        resolve(true);
      })
      .catch((e) => {
        reject(e);
      });
  });
}
