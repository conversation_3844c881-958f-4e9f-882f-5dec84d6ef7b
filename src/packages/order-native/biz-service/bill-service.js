import promiseIterator from './promise-iterator';
import {
  initContext,
  paramsValidator,
  handleExternalConpon,
  handleDisablePrepayCard,
  handleOverseaBuyRealNameAuth,
  handlePresaleConfirm,
  handleOrderLimit,
  handleValueCard,
  handleContact,
  handleLiveCouponExt,
  handleBehaviorVerify,
} from './before-bill-processors';
import {
  executeBeforeCreateOrderHooks,
  executeAfterCreateOrderHooks,
} from './hooks';

class BillService {
  constructor({ state, getters, commit, dispatch }) {
    this.state = state;
    this.getters = getters;
    this.$commit = commit;
    this.$dispatch = dispatch;
    this.preBillProcessors = [];
    this.postBillProcessors = [];

    // 初始化前置处理器的上下文
    initContext({
      state,
      getters,
      commit,
      dispatch,
    });
  }

  /**
   * 1. 获取购物车中最早加购商品的导购openId
   * 2. 把该导购的fromParams暂存在state上, 供后续方法用于覆盖下单接口参数
   * @returns { Promise<Number> }
   */
  getEarliestGuideOpenId() {
    return this.$dispatch('GET_EARLIEST_GUIDE_OPENID');
  }

  /**
   * 把下单接口参数中所有商品的fromParams字段改为指定字段
   * @returns { void }
   */
  setAllGoodsGuideParams(openId) {
    return this.$dispatch('SET_ALL_GOODS_GUIDE_PARAMS', openId);
  }

  createOrder() {
    // 下单前处理流程准备
    this.prepareCreateOrder();
    return new Promise((resolve, reject) => {
      const {
        order: { asyncOrder },
      } = this.state;
      // 若是异步下单需要先请求 ASYNC_CREATE_ORDER_BOOK_KEY 接口创建 bookKey 再在 ASYNC_CREATE_ORDER_BOOK_KEY 调用 CREATE_ORDER
      const dispatchMethod = asyncOrder
        ? 'ASYNC_CREATE_ORDER_BOOK_KEY'
        : 'CREATE_ORDER';
      promiseIterator(this.preBillProcessors)
        .then(() => {
          this.$dispatch(dispatchMethod)
            .then((orderResult) => {
              // 创建订单后置处理器
              promiseIterator(this.postBillProcessors, orderResult);
              resolve(orderResult);
            })
            .catch((e) => {
              reject(e);
            });
        })
        .catch((error) => {
          reject(error || false);
        });
    });
  }

  /**
   * 下单前处理流程准备
   */
  prepareCreateOrder() {
    // 重置处理器
    this.clearPreBillProcessor();
    this.clearPostBillProcessor();

    const { order, idcard, tradeTag, enableUseOrderBehaviorParam } = this.state;
    // 不可使用预付卡
    this.addPreBillProcessor(handleDisablePrepayCard);

    // 添加参数处理器
    this.addPreBillProcessor(paramsValidator);

    // 自动添加联系人
    this.addPreBillProcessor(handleContact);

    // 添加直播优惠券扩展字段
    this.addPreBillProcessor(handleLiveCouponExt);

    // 核销外部券 & 积分
    this.addPreBillProcessor(handleExternalConpon);

    // 实名校验 店铺条数不足 或 尝试次数过多
    if (order.needRealNameAuth && idcard.verifyFalse) {
      this.addPreBillProcessor(handleOverseaBuyRealNameAuth);
    } else if (
      tradeTag.hasDepositPreSaleGoods &&
      !order.agreeDeposit &&
      order.showDepositAgreementVal
    ) {
      // 定金预售 未确定不退定金
      this.addPreBillProcessor(handlePresaleConfirm);
    }

    // 下单限流，直接返回限流提示
    this.addPreBillProcessor(handleOrderLimit);

    // 选择储值并支付
    this.addPreBillProcessor(handleValueCard);
    //  行为组件验证
    if (enableUseOrderBehaviorParam) {
      this.addPreBillProcessor(handleBehaviorVerify.bind(this));
    }
    // 处理创建订单前钩子池
    this.addPreBillProcessor(executeBeforeCreateOrderHooks);

    // =================== 创建订单后处理器 ==============================
    this.addPostBillProcessor(executeAfterCreateOrderHooks);
  }

  addPreBillProcessor(processor) {
    this.preBillProcessors.push(processor);
  }

  clearPreBillProcessor() {
    this.preBillProcessors = [];
  }

  addPostBillProcessor(processor) {
    this.postBillProcessors.push(processor);
  }

  clearPostBillProcessor() {
    this.postBillProcessors = [];
  }
}

export default BillService;
