/**
 * 迭代promise数组
 */
function fn(proArr, params) {
  function dispatch(prom) {
    // console.log('prom:', prom);
    if (!prom) return Promise.resolve(true);

    return prom(params)
      .then(tem => {
        const arrElem = proArr.shift();
        if (arrElem) {
          return dispatch(arrElem);
        }
        return tem;
      })
      .catch(e => {
        throw e;
      });
  }

  return dispatch(proArr.shift());
}

export default function iterator(proArr, params) {
  return fn(proArr, params);
}
