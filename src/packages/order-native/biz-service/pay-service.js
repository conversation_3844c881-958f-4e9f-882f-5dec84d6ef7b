import ZanPay from '@youzan/zan-pay-weapp';
import PayManager from 'components/pay-manager/index';
import { findZanPayBiz } from '../utils';

import { executeOnPayItemClickHooks } from './hooks';
import { switchMap } from './switch-map';

const _fn = () => {};
const app = getApp();

/**
 * 继承 zanPay 之后, 通过分流配置输出统一的函数供业务方调用
 */
class PayService extends ZanPay {
  constructor(options) {
    super(options);
    const { state } = options;
    this.state = state;
    this.cashierManager = null;
    this.payWays = [];
    this.isFetchedPayWays = false;
    this.isPaying = false;
    this.zanPayBypass = options.zanPayBypass ?? false;
    this.initialization = Promise.resolve();
    /** zan-pay的支付成功是通过回调通知的，使用Promise通知定制代码需要一个trigger桥接 */
    this.successTrigger = _fn;
    this._onPayFail = _fn;
  }

  get tradeBizExt() {
    try {
      return JSON.parse(this.state.pay.prepayParams.bizExt);
    } catch (error) {
      console.error('获取bizExt失败', error);
      return {};
    }
  }

  /** 组件实例的 $store 属性是异步设置的，需要在发起支付时再设置 store */
  setStore(store) {
    this.store = store;
    this.state = store.state;
  }

  updateCashierManager({ store, onPayFail }) {
    this.store = store;
    this.state = store.state;
    this._onPayFail = onPayFail || _fn;
  }

  cloudInit(listeners) {
    const app = getApp();
    this.initialization = switchMap(
      app
        .request({
          origin: 'cashier',
          path: '/assets/api/union/custom-bypass',
        })
        .then(({ value: zanPayBypass }) => {
          this.zanPayBypass = !!zanPayBypass;
          if (zanPayBypass) {
            return app
              .getShopInfo()
              .then(({ shopMetaInfo }) => {
                this.context.biz = findZanPayBiz(shopMetaInfo);
                this.useTypeRouter = false;
                this.updateCashierType('A');
              })
              .catch((e) => {
                console.error(e);
                this.context.biz = 'trade_order';
              })
              .then(async () => {
                const crypto = await import('@youzan/crypto');
                super.init(listeners, crypto);
              })
              .catch(console.error);
          }
        })
        .catch(console.error)
    );

    return this.initialization;
  }

  // 获取支付方式
  startPay(preOrderParams) {
    const { display: { newRecommend } = {}, order = {} } = this.state;
    /** 储值推荐中的充值并支付需要用户交互，无法在初始化时确认 */
    if (newRecommend && order.config?.isStoreAndPayProcess) {
      this.context.biz = 'prepaid_recharge';
    }

    // 无定制, 走新收银台
    if (this.zanPayBypass) {
      const { order = {} } = this.state;
      return super.startPay({ ...preOrderParams, orderNo: order.orderNo });
    }

    // 旧收银台逻辑
    if (this.isPaying) {
      return Promise.reject();
    }
    // 重置
    this.reject = null;

    const { pay } = this.state;
    // 预下单失败
    if (pay.prepay && !pay.prepaySuccess) {
      return Promise.resolve({ payWays: [] });
    }

    // 无须支付，直接跳转到回调地址
    if (pay.showPayResult) {
      return Promise.resolve({ payWays: [] });
    }

    // 初始化支付服务
    this._getPayManager();

    if (this.isFetchedPayWays) {
      // 已获取支付方式
      return Promise.resolve({ payWays: this.payWays });
    }
    const cashierParams = {
      scene: pay.prepayParams.scene,
      partnerId: pay.prepayParams.partnerId,
      prepayId: pay.prepayParams.prepayId,
      cashierSign: pay.prepayParams.cashierSign,
      cashierSalt: pay.prepayParams.cashierSalt,
      orderMark: '',
    };

    // 获取支付方式
    return new Promise((resolve, reject) => {
      this.reject = reject;
      this.cashierManager.getPayWays(cashierParams, (payWays, res = {}) => {
        this.payWays = payWays;
        this.isFetchedPayWays = true;
        resolve({ payWays, res });
      });
    });
  }

  /**
   * 重写zan-pay的支付操作，增加事件拦截
   * @param  {...any} args
   */
  doPay(...args) {
    const payChannel = args[0];
    const payChannelName = this.payChannels?.find(
      (it) => it.payChannel === payChannel
    )?.payChannelName;
    return executeOnPayItemClickHooks({ payChannel, payChannelName })
      .then(() => super.doPay(...args))
      .catch((e) => {
        /**
         * 如果仅定制事件不定制 UI ，支付中断时需要再次绑
         * 定支付监听器，否则将会在首次支付中断后无法支付
         */
        this.bindDoPayOnce();
        if (this.isQuickMode()) {
          this.EE.emit('quick-close');
        }
        throw e;
      });
  }

  /**
   * 支付
   * 使用场景：1. 老支付组件点击支付后调用
   * 2. 第三方定制调用，完成支付
   * @param {*} {
   *  payChannel: 'WX_APPLET',
   *  password: '' // E卡等支付方式需要
   * }
   */
  callPay(...args) {
    const { pay = {} } = this.state;
    // 0元单等直接返回
    if (pay.showPayResult) {
      return Promise.resolve();
    }

    // 无定制, 走新收银台
    if (this.zanPayBypass) {
      this.context.payData.wxOrderInfo = this.state.wxOrderInfo;
      const { payChannel } = args[0];
      /** zan-pay的支付成功是通过回调通知的，使用Promise通知定制代码需要一个trigger桥接 */
      return new Promise((resolve, reject) => {
        this.doPay(payChannel).catch(reject);
        this.successTrigger = resolve;
      });
    }

    if (this.isPaying) {
      return Promise.reject();
    }

    // 以下是旧收银台逻辑
    const {
      payChannel = '',
      newPrice = -1,
      acceptPrice = 0,
      password = '',
    } = args[0];
    this.isPaying = true;

    // 先执行支付前钩子
    return executeOnPayItemClickHooks({ payChannel })
      .then(() => {
        return this._doPay({
          payChannel,
          acceptPrice,
          newPrice,
          password,
        });
      })
      .catch((e) => {
        this.isPaying = false;
        throw e;
      });
  }

  _doPay({ payChannel, acceptPrice, newPrice, password }) {
    // 重置
    this.reject = null;

    // 支付方式数据
    const payWay = this.payWays.find((_payWay) => {
      return _payWay.payChannel === payChannel;
    });

    const tokenData = app.getToken() || {};
    const { pay, order } = this.state;
    const { prepayParams = {} } = pay;
    // 在小程序进行支付时，需要额外传入参数 appId（有赞小程序需要）
    let outBizCtx;
    try {
      const outBizCtxData = JSON.parse(prepayParams.bizExt);
      outBizCtxData.appId = app.getAppId();
      outBizCtx = JSON.stringify(outBizCtxData);
    } catch (e) {
      outBizCtx = prepayParams.bizExt;
    }

    const payParams = {
      password,
      payTool: payWay.payChannel,
      payChannelName: payWay.payChannelName,
      wxSubOpenId: tokenData.openId,
      valueCardNo: payWay.valueCardNo || '',
      acceptPrice,
      newPrice,
      outBizCtx,
      orderMark: '',
    };

    payParams.nodeExtra = {
      availableStatus: payWay.availableStatus || '',
      orderNo: order.orderNo,
      kdtId: app.getKdtId(),
    };
    return new Promise((resolve, reject) => {
      this.reject = reject;
      this.cashierManager.doPayAction(payParams, () => {
        app.trigger('event:pay:success');
        this.isPaying = false;
        app.trigger('event:pay:success');
        resolve();
      });
    });
  }

  /**
   * 使用场景：定制获取支付方式
   */
  getPayWays() {
    if (this.isPaying) {
      return Promise.reject();
    }

    // 重置
    this.reject = null;

    const { pay, order = {} } = this.state;
    // 预下单失败
    if (pay.prepay && !pay.prepaySuccess) {
      return Promise.resolve({ payWays: [] });
    }

    // 无须支付，直接跳转到回调地址
    if (pay.showPayResult) {
      return Promise.resolve({ payWays: [] });
    }

    const cashierParams = {
      scene: pay.prepayParams.scene,
      partnerId: pay.prepayParams.partnerId,
      prepayId: pay.prepayParams.prepayId,
      cashierSign: pay.prepayParams.cashierSign,
      cashierSalt: pay.prepayParams.cashierSalt,
      /** 给老收银台用的 */
      orderMark: '',
      /** 给zan-pay用的 */
      orderNo: order.orderNo,
      wxSubOpenId: app.getToken()?.openId,
      tradeBizExt: { ...this.tradeBizExt, appId: app.getAppId() },
    };

    // 新支付组件
    if (this.zanPayBypass) {
      const { wxOrderInfo } = this.state;
      if (wxOrderInfo) {
        this.context.payData.wxOrderInfo = wxOrderInfo;
        this.updatePayContext(cashierParams);
        this.payChannels = [
          { payChannel: 'WX_APPLET', payChannelName: '微信支付' },
        ];
        return Promise.resolve({
          payWays: this.payChannels,
        });
      }

      return super
        .queryPayChannels(cashierParams)
        .then((channels) => ({ payWays: channels }));
    }

    // 初始化支付服务
    this._getPayManager();

    if (this.isFetchedPayWays) {
      // 已获取支付方式
      return Promise.resolve({ payWays: this.payWays });
    }

    // 获取支付方式
    return new Promise((resolve, reject) => {
      this.reject = reject;
      this.cashierManager.getPayWays(cashierParams, (payWays, res = {}) => {
        this.payWays = payWays;
        this.isFetchedPayWays = true;
        resolve({ payWays, res });
      });
    });
  }

  onPayFailCallback() {
    const that = this;
    return (error) => {
      this.isPaying = false;
      if (that.reject) {
        that.reject();
      }
      that._onPayFail(error);
    };
  }

  _getPayManager() {
    if (!this.cashierManager) {
      this.cashierManager = new PayManager({
        cashierRequestUrl: 'wsctrade/pay/wscweapp/payChannels.json',
        payRequestUrl: 'wsctrade/pay/wscweapp/pay.json',
        fail: this.onPayFailCallback(),
      });
    }
    return this.cashierManager;
  }
}

export default PayService;
