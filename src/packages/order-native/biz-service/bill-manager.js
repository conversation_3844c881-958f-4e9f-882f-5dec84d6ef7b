import Toast from '@vant/weapp/dist/toast/toast';
import AddressService from '@/packages/order-native/biz-service/address-service';

class BillManager {
  constructor(store, ctx) {
    this.$ctx = ctx;
    this.$store = store;
    this.$commit = store.commit;
    this.$toast = Toast;
    this.addressService = new AddressService(store);
  }

  // 选择地址
  selectAddress(addressId) {
    const { state } = this.$store;
    const list = state.address.list || [];
    const address = list.filter((item) => item.id === addressId);
    if (!address || address.length <= 0) {
      this.$toast('无效地址');
      return false;
    }
    this.$commit('SET_ADDRESS_ID', addressId);
    return true;
  }

  /*
   * 选中自提网点 - 只给开发者使用
   */
  async selectSelfFetchAddress(shopId) {
    const { state } = this.$store;
    const originList = state.selfFetch.list || [];
    if (!originList.length) {
      // 我们这边需要自己去拉取店铺列表
      await this.$store.dispatch('FETCH_SHOP_LIST', { getAll: true });
    }
    const allList = state.selfFetch.list || [];
    const shop = allList.find((item) => item.id === Number(shopId)) || null;
    this.$commit('SET_SELF_FETCH_CHOSEN_SHOP', shop);
    this.$commit('RESET_SELF_FETCH_TIME'); // 重置自提时间
    const requests = [this.$store.dispatch('FETCH_SHOW')];

    // 如果需要选择自提时间，则获取自提时间配置
    if (shop && shop.optionalSelfFetchTime) {
      requests.push(this.$store.dispatch('GET_SELF_FETCH_TIME'));
    }

    return Promise.all(requests);
  }

  /*
   * 保存地址 - 只给开发者使用
   * 1. 保存地址到数据库
   * 2. 更新下单页的地址列表
   */
  saveAddress(address) {
    return this.addressService
      .saveAddress(address)
      .then((response) => {
        const commitType = address.id ? 'UPDATE_ADDRESS' : 'ADD_ADDRESS';
        if (!address.id) {
          address.id = response.value;
        }
        this.$commit(commitType, address);
        return address;
      })
      .catch((err) => {
        return Promise.reject(err);
      });
  }
}

export default BillManager;
