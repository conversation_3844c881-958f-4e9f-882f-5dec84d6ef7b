import Theme from 'shared/common/components/theme-view/theme';
/* eslint-disable no-unused-vars */
export const themeStyleTemplate = ([mainColor, mainBg]) => {
  const rgb = Theme.switchHexToRgb(mainBg);

  return `
    --button-danger-background-color: ${mainBg};
    --button-danger-border-color: ${mainBg};
    --sidebar-selected-border-color: ${mainColor};
    --tree-select-item-active-color: ${mainColor};
    --checkbox-checked-icon-color: ${mainColor};
    --radio-checked-icon-color: ${mainColor};
    --tabs-bottom-bar-color: ${mainColor};
    --invoice-tag-active-background-color-alpha-10: ${`rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, .1)`};
    --invoice-tag-active-color: ${mainBg};
  `;
};
