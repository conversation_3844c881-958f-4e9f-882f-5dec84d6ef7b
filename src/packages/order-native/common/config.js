const charMap = {
  address: 'address',
  selfFetch: 'self-fetch',
  show: 'show',
  cityChange: 'city-change',
};

export default {
  cacheKey: {
    addressDowngrade: `${charMap.address}_downgrade`,
  },

  // 页面间通信事件名
  eventKey: {
    selfFetchCity: `${charMap.selfFetch}-${charMap.cityChange}`,
    addressSelect: `${charMap.address}-select`,
    addressDelete: `${charMap.address}-delete`,
    addressSave: `${charMap.address}-save`,
    couponChange: 'order-coupon-change',
    showExpressWay: `${charMap.show}-express-way`,
    addressCity: `${charMap.address}-${charMap.cityChange}`,
    addressMap: `${charMap.address}-map-change`,
    showContact: `${charMap.show}-contact`,
    showSelfFetchShop: `${charMap.show}-${charMap.selfFetch}-shop`,
    showSelfFetchTime: `${charMap.show}-${charMap.selfFetch}-time`,
    showTradeRecharge: `${charMap.show}-trade-recharge`,
    contactError: 'contact-error',
    idcardError: 'idcard-error',
    presaleDisagreeToast: 'presale-disagree-toast',
    showIdcardPopup: `${charMap.show}-idcard-popup`,
  },

  // 多阶段支付状态
  phaseStatus: {
    WAIT_PAY: '待付款',
    WAIT_PAY_START: '未开始',
    PAID: '已付',
    CLOSE: '已关闭',
    SUCCESS: '已完成'
  },

  // 有赞包运费赠送标
  freightInsuranceTag: {
    NONE: '0',
    SELF: '1',
    FREE: '2',
  },

  // 拼团代收方式
  groupon: {
    forbidReceive: 0,
    optionalReceive: 1,
    forceReceive: 2
  },

  activityMap: {
    3: '降价拍',
    4: '拼团',
    6: '秒杀',
    8: '赠品',
    10: '会员折扣',
    11: '限时折扣',
    23: '抽奖团',
    24: '换购',
    26: '拼团' // 阶梯团
  },

  // 自提网点库存状况
  shopStock: {
    default: 0,
    full: 1, // 有货
    none: 2, // 缺货
    part: 3 // 部分缺货
  },

  // 让利涨粉的涨粉渠道
  umpBenefitChannelType: {
    subscription: 118, // 公众号
    wechatWork: 119, // 企微好友
  },

  // 同城配送-预约时间类型
  appointmentType: {
    // 定时达
    regular: 1,
    // 即时达
    instant: 2,
  },
};
