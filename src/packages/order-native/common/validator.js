import isPhone from '@youzan/weapp-utils/lib/validators/is-phone';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import config from '@/packages/order-native/common/config';

const app = getApp();

// 身份证号校验
function isIdcard(number) {
  return /^\d{15}$/gi.test(number) || (/^\d{17}(\d|X)$/gi.test(number) && isCnNewID(number));
}

// 是否为合规的 18 位身份证号
function isCnNewID(idNumber) {
  const IDExp = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; // 加权因子
  const IDValid = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']; // 校验码

  // 对前17位数字与权值乘积求和
  let sum = 0;
  for (let i = 0, l = idNumber.length; i < l - 1; i++) {
    sum += parseInt(idNumber.charAt(i), 10) * IDExp[i];
  }

  // 检验第18位是否与校验码相等
  return IDValid[sum % 11] === idNumber.charAt(17).toUpperCase();
}

// 校验地址
function validateAddress(state, getters) {
  if (!state.display.showAddressTab) {
    return;
  }

  const { selfFetch } = state;
  const { list, id } = state.address;
  const { forcePoiSelect } = getters;
  if (getters.expressType === 'express' && state.display.showExpressTab) {
    // 团长代收，不校验地址
    if (getters.receiveByGroupHeader) {
      return;
    }

    if (!id) {
      setTimeout(() => {
        const dbid = app.db.set({ list, id, forcePoiSelect, delta: 1 });
        wx.navigateTo({
          url: `/packages/order-native/address-edit/index?dbid=${dbid}`
        });
      }, 1000);
      return '请选择收货地址';
    }

    const address = getters.currentAddress;
    if (
      address.province === '选择省份' ||
      address.city === '选择城市' ||
      address.county === '选择地区' ||
      !address.areaCode
    ) {
      setTimeout(() => {
        const dbid = app.db.set({ list, id, forcePoiSelect, delta: 1 });
        wx.navigateTo({
          url: `/packages/order-native/address-edit/index?dbid=${dbid}`
        });
      }, 1000);
      return '省市区不完整，请重新编辑地址';
    }

    if (getters.showPoiPrompt) {
      Dialog.confirm({
        message: '为提高同城配送准确性，请进行地图定位选点',
        confirmButtonText: '去选点'
      })
        .then(() => {
          const dbid = app.db.set({ list, id, forcePoiSelect, delta: 1 });
          wx.navigateTo({
            url: `/packages/order-native/address-edit/index?dbid=${dbid}`
          });
        })
        .catch(() => {
          // on cancel
        });

      return ['为提高同城配送准确性，请进行地图定位选点', false];
    }
  } else if (getters.expressType === 'self-fetch' && state.display.canSelfFetch) {
    if (!getters.currentContact.id) {
      app.trigger(config.eventKey.showContact);
      return '请选择提货人';
    }
    if (!selfFetch.shop) {
      app.trigger(config.eventKey.showSelfFetchShop);
      return '请选择提货地址';
    }
    if (!selfFetch.time && getters.canSelectSelfFetchTime) {
      app.trigger(config.eventKey.showSelfFetchTime);
      return '请选择提货时间';
    }
  }
}

// 校验联系人
function validateContact(state) {
  const { contact } = state;
  if (!contact.required || contact.id) {
    return '';
  }
  if (state.version === 1) {
    return '请选择联系人';
  }
  if (!contact.userName) {
    app.trigger(config.eventKey.contactError, { userName: true });
    return ['请输入正确的姓名', false];
  }
  if (!(contact.telephone && isPhone(contact.telephone))) {
    app.trigger(config.eventKey.contactError, { telephone: true });
    return ['请输入正确的手机号', false];
  }
  app.trigger(config.eventKey.contactError);
  return '';
}

// 校验身份证号
export function validateIdCard(state) {
  const { order, idcard, tradeTag } = state;
  if (tradeTag.hasOverseaGoods) {
    if (!idcard.name) {
      app.trigger(config.eventKey.idcardError, { name: '请输入真实姓名' });
      return '请输入真实姓名';
    }
    if (!idcard.number) {
      app.trigger(config.eventKey.idcardError, { number: '请输入真实身份证号' });
      return '请输入真实身份证号';
    }
    if (!isIdcard(idcard.number)) {
      app.trigger(config.eventKey.idcardError, { number: '请输入真实身份证号' });
      return '身份证号有误';
    }
  }

  if (order.needIdCardPhoto && !(idcard.frontPhoto && idcard.backPhoto)) {
    app.trigger(config.eventKey.idcardError, { message: '请上传身份证照' });
    return '请先填写身份信息';
  }

  app.trigger(config.eventKey.idcardError, {});

  return '';
}

function validateIdCardAndPrompt(state, getters) {
  const message = validateIdCard(state);
  if (message) {
    Dialog.confirm({
      title: '填写身份证信息',
      message: getters.hasHaitaoGoods
        ? '为保障海淘商品顺利清关，请填写身份证信息。'
        : '该商品下单需要填写身份证信息。',
      confirmButtonText: '去填写'
    })
      .then(() => {
        if (state.version === 1) {
          wx.navigateTo({ url: '/packages/order-native/idcard/index' });
        } else {
          app.trigger(config.eventKey.showIdcardPopup, true);
        }
      })
      .catch(() => {
        // on cancel
      });
  }
  return [message, false];
}

// 校验留言
export function validateOrderMessage(state) {
  const { buyerMsg } = state.order;
  return buyerMsg && buyerMsg.length > 250 ? '留言字数不能超过 250 个字' : '';
}

// 校验同城送时间
function validateLocalDelivery(state) {
  const { delivery, display } = state;
  if (display.showLocalDeliveryBucket && !delivery.text) {
    app.trigger(config.eventKey.showExpressWay);
    return ['请选择期望送达时间', false];
  }
  return '';
}

function toastError(validator, state, getters, dispatch) {
  let msg = validator(state, getters);
  let needToast = true;
  if (Array.isArray(msg)) {
    [msg, needToast] = msg;
  }
  if (needToast && msg) {
    dispatch('TOAST', msg);
  }
  return !msg;
}

// 订单校验
const validators = [
  validateAddress,
  validateContact,
  validateIdCardAndPrompt,
  validateOrderMessage,
  validateLocalDelivery
];

export default function validateOrder({ state, getters, dispatch }) {
  return !validators.some((validator) => !toastError(validator, state, getters, dispatch));
}
