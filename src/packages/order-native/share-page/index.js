import WscPage from 'pages/common/wsc-page/index';
import moneyHelper from '@youzan/weapp-utils/lib/money';
import {
  getOrderEnterShopPolicy,
  handleUrlWithShopAutoEnter,
} from 'common-api/multi-shop/multi-shop-redirect';

const app = getApp();

WscPage({
  data: {
    showError: false, // 是否展示错误页面
    errorText: '', // 错误信息
    buyer: {}, // 买家信息
    orders: {}, // 订单信息
    showOriginPrice: false, // 是否暂时零售价格
  },

  query: {}, // 页面参数

  onLoad(query) {
    this.queryOrderInfo(query, 1);
    this.query = query;
    this.checkIsShowOriginPrice();
  },

  onShow() {
    getOrderEnterShopPolicy();
    app.logger &&
      app.logger.log({
        et: 'display',
        ei: 'enterpage',
        en: '浏览页面',
        si: app.getKdtId(),
      });
  },

  gotoGoodDetail(event) {
    app.logger &&
      app.logger.log({
        et: 'click',
        ei: 'click_goods',
        en: '点击商品',
        si: app.getKdtId(),
      });
    setTimeout(() => {
      wx.navigateTo({
        url: handleUrlWithShopAutoEnter(event.currentTarget.dataset.url),
      });
    }, 100);
  },

  // 查询订单信息
  queryOrderInfo(params, needRetry = 0) {
    const { order_no: orderNo } = params;
    if (!orderNo) {
      this.setYZData({ showError: true, errorText: '分享订单不存在' });
      return;
    }

    wx.showToast({
      icon: 'loading',
      // 持续时间最长为10s
      duration: 10000,
    });
    app
      .request({
        path: '/wsctrade/order/share/getOrderInfo.json',
        method: 'POST',
        data: {
          orderNo: params.order_no,
          kdtId: params.kdt_id,
        },
        config: {
          skipShopInfo: true,
          skipKdtId: true,
        },
      })
      .then((res) => {
        wx.hideToast();
        const { buyerInfo = {}, itemInfo = {} } = res;
        this.setYZData({
          buyer: {
            username: buyerInfo.customerName,
            avatar:
              buyerInfo.avatar || 'https://img01.yzcdn.cn/image/_avatar.png',
          },
          orders: itemInfo.map((item) => {
            return {
              ...item,
              imgUrl: `url(${item.imgUrl ||
                'https://img01.yzcdn.cn/public_files/2019/07/26/8ffb4974386434b2c5c57461463c6102.png'})`,
              originImgUrl: item.imgUrl,
              unitPrice: moneyHelper(item.unitPrice).toYuan(),
              originUnitPrice: moneyHelper(item.originUnitPrice).toYuan(),
            };
          }),
        });
      })
      .catch(({ msg } = {}) => {
        wx.hideToast();
        if (needRetry) {
          setTimeout(() => {
            this.queryOrderInfo(params);
          }, 1000);
        } else {
          this.setYZData({ showError: true, errorText: msg || '未知异常' });
        }
      });
  },

  checkIsShowOriginPrice() {
    app
      .request({
        path: `/wsctrade/order/share/showOriginPrice.json?kdt_id=${app.getKdtId()}`,
        method: 'GET',
      })
      .then((res) => {
        this.setYZData({ showOriginPrice: res.showOriginPrice });
      });
  },

  onShareAppMessage() {
    let nameMaxlen = 0;
    let message = '';
    let subTitle = '';
    if (this.data.orders.length > 1) {
      nameMaxlen = 14;
      message = '等，必须推荐给你';
    } else {
      nameMaxlen = 15;
      message = '，必须推荐给你';
    }
    if (this.data.orders[0].title.length > nameMaxlen) {
      subTitle =
        this.data.orders[0].title.substr(0, nameMaxlen) + '...等' || '一些东西';
    } else {
      subTitle = this.data.orders[0].title || '一些东西';
    }
    return {
      title: `我买了${subTitle}${message}`,
      imageUrl: this.data.orders[0] ? this.data.orders[0].originImgUrl : '',
      path: `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
        `/packages/order-native/share-page/index?order_no=${this.query.order_no}&kdt_id=${this.query.kdt_id}`
      )}`,
    };
  },

  onOfficialAccountError(res) {
    console.log('onOfficialAccountError', res);
  },
});
