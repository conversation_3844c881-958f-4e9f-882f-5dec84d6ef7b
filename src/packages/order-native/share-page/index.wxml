<!-- 订单分享落地页面 -->
<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
  page-bg-color="{{ '#f8f8f8' }}"
>
  <!-- 用户信息 -->
  <view class="order-share-page__user">
    <image class="order-share-page__user_avatar" src="{{ buyer.avatar }}" :alt="username"></image>
    <view class="order-share-page__user_name">
      {{ buyer.username }}
    </view>
    <view class="order-share-page__user_meta order-share-page__quote">
      我买了一些好东西，分享给你看看
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-share-page__list">
    <view class="order-share-page__list_meta order-share-page__quote">
      TA已购买的商品
    </view>
    <block wx:if="{{ orders.length > 0 }}">
      <view
        wx:for="{{ orders }}"
        wx:key="{{ item.alias }}"
        data-url="{{ '/pages/goods/detail/index?alias=' + item.alias }}"
        bindtap="gotoGoodDetail"
        class="order-share-page__list_item"
      >
        <view class="order-share-page__list_img" style="background-image: {{ item.imgUrl }}"></view>
        <view class="order-share-page__list_info">
          <view class="order-share-page__list_title">
            {{ item.title }}
          </view>
          <view class="order-share-page__list_price">
            好友购买价：
            <text class="order-share-page__list_price_num">
              ¥{{ showOriginPrice ? item.originUnitPrice : item.unitPrice }}
            </text>
            <icon class="order-share-page__list_gwc"/>
          </view>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="order-share-page__list_empty">
        这里空空如也～
      </view>
    </block>
  </view>
</page-container>
