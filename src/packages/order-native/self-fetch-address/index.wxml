<page-container class="page {{ themeClass }} page-{{ deviceType }}">
  <scroll-view
    class="shop-list"
    scroll-y
    lower-threshold="150"
    bindscrolltolower="getShopList"
  >
    <!-- 地址列表选择 -->
    <van-search
      wx:if="{{ inSearch }}"
      placeholder="请输入自提点名称"
      show-action
      value="{{ keyword }}"
      bind:search="startShowList"
      bind:change="onSearchInput"
      bind:cancel="onCancelSearch"
    />
    <view wx:else class="banner">
      <view bindtap="selectCity" class="city-text van-c-gray-darker van-font-14">{{ selfFetch.cityName || '全部' }}</view>
      <view bindtap="switchShowInStock" class="stock-btn {{ showInStock ? 'van-c-blue blue' : 'van-c-gray-darker' }}">只看有货网点</view>
      <van-icon bind:click="startSearch" name="search" size="16px" class="van-c-gray" />
    </view>

    <block wx:if="{{ showList }}">
      <van-cell
        wx:for="{{ selfFetchList }}"
        wx:key="{{ item.id }}"
        wx:if="{{ !showInStock || item.orderSoldStatus !== shopStock.none && item.orderSoldStatus !== shopStock.part }}"
        data-index="{{ index }}"
        bind:click="handleSelect"
        center
        value-class="value-class"
      >
        <van-icon
          slot="icon"
          class="{{ item.id != selfFetch.shop.id ? 'van-c-gray-dark' : 'theme-color' }}"
          name="{{ item.id != selfFetch.shop.id ? 'circle' : 'checked' }}"
          size="20px"
          style="margin-right: 10px;"
        />
        <view slot="title">
          <view class="van-font-14 van-c-333">
            {{ item.name }}
            <van-tag wx:if="{{ item.shopTag }}" plain type="danger">{{ item.shopTag }}</van-tag>
          </view>
          <view class="van-font-12 van-c-gray-dark" style="margin-top: 2px;">{{ item.detail }}</view>
        </view>
        <view class="van-c-gray-dark van-font-12">
          {{ item.distanceStr }}
        </view>
      </van-cell>
    </block>

    <view wx:elif="{{ selfFetch.finished }}" class="empty-text van-font-14 van-c-gray-dark van-hairline--top">
      {{ inSearch || showInStock ? '没有符合条件的提货点' : '当前城市暂无提货点，请尝试更换城市' }}
    </view>
  </scroll-view>
</page-container>
