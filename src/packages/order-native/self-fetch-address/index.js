import { mapState, mapGetters } from '@youzan/vanx';
import { VanxPage } from 'pages/common/wsc-page/index';
import store from '@/packages/order-native/store/index';
import config from '@/packages/order-native/common/config';

VanxPage({
  store,

  mapData: {
    ...mapState(['location', 'selfFetch']),
    ...mapGetters(['selfFetchList'])
  },

  data: {
    shopStock: config.shopStock, // 店铺缺货状态配置
    showInStock: true, // 只看有货网点
    keyword: '', // 搜索关键字
    inSearch: false, // 搜索状态
    showList: false // 是否显示列表
  },

  onLoad() {
    this.on(config.eventKey.selfFetchCity, () => {
      this.onCancelSearch();
    });

    if (this.data.location.located) {
      this.startShowList();
    } else {
      this.$dispatch('LOCATE_CITY').then(this.startShowList).catch(this.startShowList);
    }
  },

  // 重置列表参数
  resetListConfig() {
    this.$commit('SET_SELF_FETCH_SHOP_CONFIG', {
      page: 1,
      finished: false,
      list: []
    });
    this.setYZData({
      showList: false
    });
  },

  // 开始请求列表
  startShowList() {
    this.resetListConfig();
    this.getShopList();
  },

  // 获取自提列表
  getShopList() {
    const {
      selfFetch: { finished }
    } = this.data;

    if (finished) {
      return;
    }

    this.setYZData({
      loading: true,
      showList: true
    });

    this.$dispatch('FETCH_SHOP_LIST', {
      keyword: this.data.keyword
    })
      .then(() => {
        this.setYZData({
          loading: false
        });
      })
      .catch((err) => {
        this.setYZData({
          loading: false
        });
        this.$dispatch('TOAST', err.msg || '出错了，请稍后重试');
      });
  },

  // 切换只显示有货网点
  switchShowInStock() {
    const {
      selfFetch: { finished }
    } = this.data;
    this.setYZData({
      showInStock: !this.data.showInStock
    });
    if (!finished) {
      this.getShopList();
    }
  },

  // 前往选择城市
  selectCity() {
    const { location } = this.data;
    wx.navigateTo({
      url: `/packages/order-native/self-fetch-address-city/index?city=${location.cityName}&cityCode=${location.cityCode}`
    });
  },

  // 开始搜索
  startSearch() {
    this.setYZData({ inSearch: true });
  },

  // 搜索框输入
  onSearchInput(event) {
    this.setYZData({ keyword: event.detail });
  },

  // 取消搜索
  onCancelSearch() {
    this.setYZData(
      {
        inSearch: false,
        keyword: ''
      },
      this.startShowList
    );
  },

  // 选中网点后返回下单页
  handleSelect(event) {
    const { index } = event.currentTarget.dataset;
    const { selfFetch, selfFetchList } = this.data;
    const shop = selfFetchList[index];

    if (selfFetch.shop == null || shop.id !== selfFetch.shop.id) {
      this.$commit('SET_SELF_FETCH_CHOSEN_SHOP', shop);

      this.$commit('RESET_SELF_FETCH_TIME'); // 重置自提时间

      const requests = [this.$dispatch('FETCH_SHOW')];

      // 如果需要选择自提时间，则获取自提时间配置
      if (shop.optionalSelfFetchTime) {
        requests.push(this.$dispatch('GET_SELF_FETCH_TIME'));
      }

      Promise.all(requests).then(() => {
        this.showSoldOutDialog(shop.orderSoldStatus);
      });
    }
    this.$commit('SET_EXPRESS_WAP_TAB', 1);
    wx.navigateBack();
  },

  // 自提点缺货，弹窗提示
  showSoldOutDialog(status) {
    const { shopStock } = config;
    let message = '';
    if (status === shopStock.part) {
      message = '订单部分商品在当前提货点缺货，此部分商品将不会购买下单';
    } else if (status === shopStock.none) {
      message = '订单所有商品在当前提货点缺货，请重新选择商品下单';
    }

    if (message) {
      wx.showModal({
        content: message,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  }
});
