@import 'shared/common/css/helper/index.wxss';

.page {
  min-height: 100vh;
  background: #f9f9f9;
}

/* 网店列表 */
.shop-list {
  height: 100vh;
  box-sizing: border-box;
}

/* 顶部搜索、城市条 */
.banner {
  position: relative;
  display: flex;
  height: 45px;
  padding: 0 15px;
  background: #f8f8f8;
  box-sizing: border-box;
  flex: 0 0 auto;
  align-items: center;
  justify-content: space-between;
}

.city-text {
  position: relative;
  padding-right: 12px;
}

.city-text::after {
  position: absolute;
  top: 5px;
  right: 0;
  width: 0;
  height: 0;
  border: 3px solid;
  border-color: #666 #666 transparent transparent;
  content: ' ';
  transform: rotate(135deg) scale(0.8);
}

/* 只看缺货网点切换按钮 */
.stock-btn {
  position: absolute;
  top: 50%;
  right: 50px;
  float: right;
  padding: 0 10px;
  margin-top: -12px;
  font-size: 12px;
  line-height: 24px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
}

.stock-btn.blue {
  border-color: #38f;
}

.empty-text {
  padding-top: 180px;
  text-align: center;
}

.value-class {
  flex: none !important;
  padding-left: 10px;
}
