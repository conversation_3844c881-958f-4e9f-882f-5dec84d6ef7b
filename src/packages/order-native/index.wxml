<import src="./custom-tpl.wxml" />

<page-container
  show-service-due
  show-shop-status
  page-container-class="order-container  {{ themeClass }} page-{{ deviceType }}"
  theme-template="{{ theme }}"
>
<view wx:if="{{ dataLoaded }}">
  <!--自定义导航栏-->
  <view
    slot="page-top-hook"
    wx:if="{{ canUseNav }}"
    class="navigation-bar"
    style="height: {{ height }}px; z-index: 998;"
  >
    <view
      style="height: {{ height }}px; z-index: 998;"
      class="navigation-bar__sticky-wrap"
    >
      <view
        class="navigation-bar__main"
        style="height: {{ height }}px;"
      >
        <view
          class="navigation-bar__icon"
          style="{{ customIconStyle }}"
        >
          <view class="navigation-bar__icon-button" bind:tap="onBackTap">
            <van-icon name="arrow-left" size="20px"/>
          </view>
        </view>
        <view
          class="navigation-bar__title-text"
          style="padding-top: {{ paddingTop }}px;"
          >
          <text  wx:if="{{ !showTradeCarousel }}" style="width: {{ textWidth }}px; font-size: {{ fontSizeSetting }}px;">
            {{ titleText }}
          </text>

          <trade-carousel wx:if="{{ showTradeCarousel }} " custom-top="{{ paddingTop }}" show="{{ showTradeCarousel }}" naviBarText="{{ titleText }}"/>
        </view>
      </view>
    </view>
  </view>

  <!-- 显示店铺、网点状态 -->
  <shop-status
    wx:if="{{ shop.closed }}"
    business-time="{{ shop.closedDesc }}"
    is-multi-store="{{ false }}"
  />

  <form-view
    disabled="{{ shop.activityType !== 23 }}"
    bind:report="handleFormId"
  >
    <block>
      <!-- 倒计时 -->
      <countdown />

      <block wx:for="{{ design }}" wx:key="type">
        <!-- 地址 -->
        <address
          wx:if="{{ item.type === 'address' }}"
          theme-border-color="theme-border-color"
          theme-text-color="theme-text-color"
          theme-bg-color="theme-bg-color"
          theme-button="theme-button"
          theme-color="theme-color"
          th_tree-a="th_tree-a"
        />

        <!-- 商品 -->
        <goods
          wx:elif="{{ item.type === 'goods' }}"
          theme-border-color="theme-border-color"
          theme-text-color="theme-text-color"
          theme-bg-color="theme-bg-color"
          theme-button="theme-button"
          theme-color="theme-color"
        />

        <!-- 营销活动 -->
        <ump
          wx:elif="{{ item.type === 'ump' }}"
          theme-border-color="theme-border-color"
          theme-text-color="theme-text-color"
          theme-bg-color="theme-bg-color"
          theme-button="theme-button"
          theme-color="theme-color"
        />

        <!-- 配送等服务 -->
        <service
          wx:elif="{{ item.type === 'service' }}"
          th_btn-p="th_btn-p"
          theme-button-plain="theme-button-plain"
          theme-border-color="theme-border-color"
          theme-text-color="theme-text-color"
          theme-bg-color="theme-bg-color"
          theme-button="theme-button"
          theme-color="theme-color"
          th_tree-a="th_tree-a"
        />

        <!-- 价格 -->
        <price
          wx:elif="{{ item.type === 'price' }}"
          custom-props="{{ item.customProps }}"
          theme-border-color="theme-border-color"
          theme-text-color="theme-text-color"
          theme-bg-color="theme-bg-color"
          theme-button="theme-button"
          theme-color="theme-color"
        />

        <!-- 底部付款条 -->
        <submit
          wx:elif="{{ item.type === 'submit' }}"
          order-pay-class="order-pay-class"
          theme-border-color="theme-border-color"
          theme-bg-color="theme-bg-color"
          theme-button="theme-button"
          theme-color="theme-color"
        />

        <block wx:elif="{{ item.custom }}">
          <template is="{{ item.type }}" />
        </block>
      </block>
    </block>

    <haitao-footer-notice show-haitao-footer-notice="{{ true }}" />

    <!-- 底部有赞担保 -->
    <guarantee-footer
      wx:if="{{ guarantee.yzGuarantee }}"
      services="{{ guarantee.yzGuaranteeInfo.mainSupportContent }}"
      icons="{{ guarantee.yzGuaranteeDocs.icon }}"
      hideYzGuarantee="{{ guarantee.yzGuaranteeInfo.hideYzGuarantee }}"
    />
    <order-keep
      show="{{ showOrderkeep }}"
      display-data="{{ displayData }}"
      bind:close="orderKeepGiveup"
      bind:confirm="orderKeepComfirm"
    />

  </form-view>

</view>

<!-- 骨架图 -->
<image
  wx:else
  class="skeleton-image"
  mode="scaleToFill"
  src="https://img01.yzcdn.cn/public_files/2020/09/21/6274ce7d710084ada330e16f6b6f6157.png"
/>
<zan-pay bind:init="initPay" bind:paysuccess="onPaySuccess" bind:navigate="onCashierNavi" />
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<behavior-verify id="behavior-verify" autoInit="{{ true }}"></behavior-verify>
</page-container>
