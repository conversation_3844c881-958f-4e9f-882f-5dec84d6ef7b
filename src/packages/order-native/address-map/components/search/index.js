import WscComponent from 'pages/common/wsc-component/index';
import { searchAddress, tryLocation } from 'shared/utils/lbs/index';
import config from '@/packages/order-native/common/config';
import { getCity } from '@/packages/order-native/utils/request';
import { authorizeLocation } from '../../utils';
import { logUseSearchAddress } from '../../log';

import asyncEvent from 'shared/utils/async-event';

import { info } from '../../saas/set-policy';

WscComponent({
  properties: {
    windowHeight: Number,
  },

  data: {
    city: '北京市',
    isSearching: false,
    searchList: [],
    isEmpty: false,
  },

  attached() {
    // 城市变更时修改城市
    this.on(config.eventKey.addressCity, (city) => {
      this.setYZData({ city });
    });

    // 获取当前城市
    authorizeLocation()
      .then(
        () => new Promise((resolve, reject) => tryLocation(resolve, reject))
      )
      .then(getCity)
      .then((res) => {
        this.setYZData({ city: res.cityName });
      });
  },

  methods: {
    formateSearchResult(data, keyword) {
      return (
        data
          // 过滤省市区不全的地址
          .filter((item) => item.province && item.city)
          .map((item) => {
            item.titleArray = item.title.split(keyword);
            return item;
          })
      );
    },

    // 选择城市
    onClickCity() {
      wx.navigateTo({
        url: '/packages/order-native/address-city/index',
      });
    },

    // 聚焦搜索框
    onFocus() {
      this.triggerEvent('inSearch', true);
      this.setYZData({ isSearching: true });
    },

    // 取消搜索
    onCancel() {
      this.triggerEvent('inSearch', false);
      this.setYZData({
        isSearching: false,
        searchList: [],
        isEmpty: false,
      });
    },

    onDownGrade() {
      logUseSearchAddress({
        region: this.data.city,
        keyword: this.keyword,
      });

      this.trigger(config.eventKey.addressMap, {
        addressDetail: this.keyword,
        lat: '',
        lon: '',
        type: 1,
        downgrade: true,
      });

      wx.navigateBack();
    },

    onChange(event) {
      this.keyword = event.detail;
    },

    // 搜索地址
    onSearch(event) {
      const keyword = event.detail;
      searchAddress({
        keyword,
        policy: info.policy,
        region: this.data.city,
        page_size: 20,
        page_index: 1,
      }).then(({ data = [] } = {}) => {
        const newData = this.formateSearchResult(data, keyword);
        asyncEvent.triggerAsync
          .apply(getApp(), [
            'afterSearch',
            {
              data: newData,
            },
          ])
          .then((res = []) => {
            const list = res[0] === undefined ? newData : res[0];
            this.setYZData({
              searchList: list,
              isEmpty: list.length === 0,
              keyword,
            });
          });
      });
    },

    onSelectAddress(event) {
      const { index } = event.currentTarget.dataset;
      this.triggerEvent('select', this.data.searchList[index]);
    },
  },
});
