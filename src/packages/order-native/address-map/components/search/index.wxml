<van-search
  class="search__bar"
  placeholder="请输入您的收货地址"
  show-action="{{ isSearching }}"
  shape="round"
  bind:focus="onFocus"
  bind:change="onChange"
  bind:search="onSearch"
  bind:cancel="onCancel"
>
  <view slot="label" class="search__label" bindtap="onClickCity">
    {{ city }}
    <van-icon name="arrow-down" class="search__icon" />
  </view>
</van-search>

<!-- 搜索结果列表 -->
<scroll-view 
  wx:if="{{ isSearching }}" 
  scroll-y 
  class="search__list" 
  style="height: {{ windowHeight - 54 }}px;"
>
  <view wx:if="{{ isEmpty }}" class="search__empty">
    <image
      class="search__empty-icon"
      mode="aspectFit"
      src="https://img01.yzcdn.cn/public_files/2020/02/12/bc6b35812bc41ee0a100415c438989e1.png"
      alt=""
    />
    <view class="search__empty-text">
      暂无搜索结果
    </view>
    <view class="search__empty-text">
      没有搜到你的地址？
    </view>
    <view class="search__empty-button" bindtap="onDownGrade">
      使用当前搜索的地址
    </view>
  </view>
  <van-cell
    wx:for="{{ searchList }}"
    wx:key="{{ item.id }}"
    label="{{ item.address }}"
    data-index="{{ index }}"
    bind:click="onSelectAddress"
  >
    <view slot="title" style="display: flex;">
      <block wx:for="{{ item.titleArray }}" wx:for-item="word" wx:key="{{ item }}">
        <text wx:if="{{ index !== 0 }}" style="color: #38f;">{{ keyword }}</text>{{ word }}
      </block>
    </view>
  </van-cell>
</scroll-view>