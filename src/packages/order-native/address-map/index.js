import WscPage from 'pages/common/wsc-page/index';
import {
  reverseGeocoder,
  tryLocation,
  gcjToBaidu,
  baiduToGcj,
} from 'shared/utils/lbs/index';
import config from '@/packages/order-native/common/config';
import { authorizeLocation } from './utils';
import get from '@youzan/weapp-utils/lib/get';
import { getCity } from '@/packages/order-native/utils/request';
import {
  ADDRESS_SOURCE,
  ADDRESS_TYPE,
  ADDRESS_POI_TYPE,
} from '@/packages/order-native/utils/constants-address';
import { analyzeAreaCode, getAreaList } from '../address-edit/utils';
import Toast from '@vant/weapp/toast/toast';
/* #ifdef BUILD_ENV=youzanyun */
import SaasPageConfig from './saas/page-config';
/* #endif */

let extendPageConfig = {};
/* #ifdef BUILD_ENV=youzanyun */
extendPageConfig = SaasPageConfig;
/* #endif */

WscPage(extendPageConfig, {
  data: {
    city: '',
    pois: [],
    markers: [],
    showAuthDialog: false,
    located: true,
    showLabel: false,
    longitude: '',
    latitude: '',
    showMap: true,
  },

  onLoad() {
    // 初始化样式
    wx.getSystemInfo({
      success: (res) =>
        this.setYZData({
          windowHeight: res.windowHeight,
        }),
    });

    this.setMarker();
    this.init();

    getAreaList().then((areaList) => {
      this.areaList = areaList;
    });
  },

  // 设置当前位置标记
  setMarker() {
    this.getLocation().then((location) => {
      this.setYZData({
        markers: [
          {
            ...location,
            iconPath:
              'https://img01.yzcdn.cn/public_files/2019/05/23/a10e32e4bfeae621727c760f97274654.png',
            width: '22px',
            height: '22px',
            anchor: { x: 0.5, y: 0.5 },
          },
        ],
      });
    });
  },

  // 初始化地图中心标记
  init() {
    // 直接定位到当前位置
    this.resetPosition();
  },

  onReady() {
    this.mapContext = wx.createMapContext('map');
  },

  onShow() {
    // 未开启定位权限后续开启时 重新定位
    const { located, showAuthDialog } = this.data;
    if (!located && showAuthDialog) {
      authorizeLocation(true).then(() => {
        this.setYZData({ located: true });
        this.setMarker();
        this.onCloseAuthDialog();
      });
      this.init();
    }
  },

  // 初始化位置
  resetPosition() {
    this.getLocation()
      .then((location) => {
        this.setYZData({ located: true, ...location });
        this.updatePoi(location);
      })
      .catch(() => {
        this.setYZData({
          located: false,
          pois: [],
        });
      });
  },

  // 获取当前位置
  getLocation() {
    return authorizeLocation().then(
      () =>
        new Promise((resolve, rejcet) => {
          tryLocation((_, location) => resolve(location), rejcet);
        })
    );
  },

  onInSearch(event) {
    this.setYZData({ showMap: !event.detail });
  },

  // 显示授权弹窗
  onShowAuthDialog() {
    this.setYZData({
      showAuthDialog: true,
    });

    setTimeout(() => {
      this.setYZData({
        showMap: false,
      });
    }, 300);
  },

  // 关闭授权弹窗
  onCloseAuthDialog() {
    this.setYZData({
      showAuthDialog: false,
    });

    setTimeout(() => {
      this.setYZData({
        showMap: true,
      });
    }, 300);
  },

  // 结束拖动地图
  regionChange(res) {
    if (res.type === 'begin') {
      this.setYZData({ showLabel: false });
    } else if (res.type === 'end') {
      this.mapContext.getCenterLocation({
        success: (location) => {
          if (!(location && location.longitude && location.latitude)) {
            return;
          }
          const { longitude, latitude } = this.data;
          const isSame =
            longitude === location.longitude && latitude === location.latitude;
          if (!isSame) {
            this.updatePoi(location);
          } else {
            this.setYZData({ showLabel: true });
          }
        },
      });
    }
  },

  // 更新poi列表
  updatePoi(location) {
    this.setYZData({
      showLabel: true,
      markerLabel: '定位中...',
      pois: [],
      loading: true,
    });

    wx.nextTick(() => {
      reverseGeocoder({
        location,
        get_poi: 1,
        poi_options: 'policy=2;radius=500;page_size=20;page_index=1',
      })
        .catch(() => ({}))
        .then((res = {}) => {
          const markerLabel = get(
            res,
            'result.formatted_addresses.recommend',
            ''
          );
          const pois = get(res, 'result.pois', []);
          this.setYZData({ markerLabel, pois, loading: false });

          if (!pois.length) {
            this.setYZData({ showLabel: false });
          }
        });
    });
  },

  selectArea(district) {
    const countyMap = this.areaList.county_list;

    this.setYZData({
      pickerTitle:
        district.province === district.city
          ? district.province
          : `${district.province} ${district.city}`,
      // 没有区 区需要重选
      countyOptions: Object.keys(countyMap)
        .filter((code) => code.slice(0, 4) === district.areaCode.slice(0, 4))
        .map((code) => ({
          text: countyMap[code],
          value: {
            ...district,
            county: countyMap[code],
            areaCode: code,
          },
        })),
      showArea: true,
    });

    return new Promise((resolve, reject) => {
      this.selectAreaResolve = resolve;
      this.selectAreaReject = reject;
    });
  },

  onConfirm(event) {
    this.setYZData({ showArea: true });
    this.selectAreaResolve(event.detail.value.value);
  },

  onCancel() {
    this.setYZData({ showArea: false });
  },

  // 选择附近poi
  onSelectPoi(event) {
    const poi = this.data.pois[event.currentTarget.dataset.index];
    this.onSelectAddress({
      detail: {
        ...poi,
        ...poi.ad_info,
      },
    });
  },

  // 选择地址
  onSelectAddress(event) {
    const poi = event.detail;
    const baiduLocation = gcjToBaidu(poi.location.lng, poi.location.lat);

    Promise.resolve()
      // poi中没有areaCode时需要查一下
      .then(() =>
        poi.adcode
          ? poi.adcode
          : getCity(baiduLocation).then((res) => res.cityCode)
      )
      // areaCode解析为省市区
      .then((adcode) => analyzeAreaCode(adcode))
      // 校验省市区完整性
      .then((district) => {
        if (
          !district.province ||
          !district.city ||
          (district.areaCode[0] !== '9' && !district.county)
        ) {
          Toast('该地址省市区信息不全，请手动选择');
          return this.selectArea(district);
        }

        return district;
      })
      .then((district) => {
        const addressDetail =
          poi.address
            .replace(poi.province, '')
            .replace(poi.city, '')
            .replace(poi.district, '') + poi.title;
        this.trigger(config.eventKey.addressMap, {
          ...district,
          location: poi.title,
          addressDetail,
          poiId: poi.id,
          poiType: ADDRESS_POI_TYPE.QQ, // 1: 腾讯 2: 百度 3: 高德
          source: ADDRESS_SOURCE.YOUZAN, // 重新选点后，source变为有赞地址
          lat: baiduLocation.lat,
          lon: baiduLocation.lng,
          downgrade: false,
          type: ADDRESS_TYPE.POI,
        });

        wx.navigateBack();
      });
  },
});
