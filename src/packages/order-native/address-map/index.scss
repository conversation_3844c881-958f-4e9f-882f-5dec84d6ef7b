.map {
  width: 100%;
  height: 272px;

  &__blank {
    height: 272px;
  }

  &__title {
    font-size: 12px;
    color: #969799;
    background-color: #fff;
    padding: 0 12px;
    height: 40px;
    display: flex;
    align-items: center;
  }

  &__pois {
    box-sizing: border-box;
    background-color: #fff;
    padding-left: 12px;
    padding-right: 12px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &__item {
      position: relative;
      background: #f7f8fa;
      border-radius: 8px;
      padding: 16px 8px 16px 32px;
      margin-bottom: 8px;

      &__icon {
        position: absolute;
        left: 10px;
        top: 11px;
      }

      &__title {
        font-weight: 600;
        font-size: 14px;
        color: #323233;
        line-height: 1;
      }

      &__address {
        margin-top: 8px;
        font-size: 12px;
        color: #969799;
        line-height: 1;
      }
    }
  }

  &__marker {
    position: absolute;
    bottom: 50%;
    left: 0;
    width: 100%;
    text-align: center;

    &__label {
      position: relative;
      margin-bottom: 12px;
      overflow: visible;
      background-color: #fff;
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.08);
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      color: #333;
      line-height: 16px;
      white-space: nowrap;
      display: inline-block;

      &__after {
        position: absolute;
        bottom: 0;
        left: 50%;
        z-index: 1;
        display: block;
        width: 0;
        height: 0;
        border-top: 6px solid #fff;
        border-right: 6px solid transparent;
        border-left: 6px solid transparent;
        content: '';
        transform: translate(-50%, 100%);
      }
    }

    &__icon {
      width: 20px;
      height: 34px;
      margin: 0 auto;
    }
  }

  &__button {
    height: 18px;
    width: 18px;
    background-color: #fff;
    border-radius: 4px;
    padding: 3px;
    position: absolute;
    right: 10px;
    bottom: 10px;

    &__icon {
      width: 100%;
      height: 100%;
    }
  }

  &__empty {
    &-icon {
      height: 56px;
    }

    &-text {
      font-size: 14px;
      color: #999;
      text-align: center;
      margin-top: 16px;
    }
  }
}

.located {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #fff;

  &__icon {
    height: 60px;
  }

  &__text {
    margin: 16px 0 30px;
    font-size: 14px;
    line-height: 18px;
    color: #969799;
    text-align: center;
  }
}
