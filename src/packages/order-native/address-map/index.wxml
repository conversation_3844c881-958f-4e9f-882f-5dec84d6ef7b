<page-container
  page-container-class="{{ themeClass }}"
  forbid-copyright
>
  <!-- 顶部搜索地址 -->
  <search
    window-height="{{ windowHeight }}"
    bind:inSearch="onInSearch"
    bind:select="onSelectAddress"
  />
  
  <!-- 地图 -->
  <map
    hidden="{{ !showMap }}"
    id="map"
    class="map"
    markers="{{ markers }}"
    longitude="{{ longitude }}"
    latitude="{{ latitude }}"
    scale="{{ 15 }}"
    bindregionchange="regionChange"
  >
    <!-- marker -->
    <cover-view class="map__marker">
      <cover-view wx:if="{{ showLabel }}" class="map__marker__label">
        <cover-view>{{ markerLabel }}</cover-view>
        <cover-view class="map__marker__label__after" />
      </cover-view>
      <cover-image
        class="map__marker__icon"
        src="https://img01.yzcdn.cn/public_files/2020/03/04/32a548551986a2c3c22ef3018eb7a9af.png"
      />
    </cover-view>

    <!-- 定位按钮 -->
    <cover-view class="map__button" bindtap="resetPosition">
      <cover-image
        class="map__button__icon"
        src="https://img01.yzcdn.cn/public_files/2020/02/11/346338342922cbc47270d6387e527465.png"
      />
    </cover-view>
  </map>
  <view hidden="{{ showMap }}" class="map__blank"></view>

  <view class="map__title">附近位置</view>

  <view wx:if="{{ loading }}" class="located" style="height: {{ windowHeight - 54 - 272 - 40 }}px;">
    <van-loading color="#1989fa" />
  </view>

  <view wx:elif="{{ located && pois.length === 0 }}" class="located" style="height: {{ windowHeight - 54 - 272 - 40 }}px;">
    <image
      src="https://img01.yzcdn.cn/public_files/2020/02/12/bc6b35812bc41ee0a100415c438989e1.png"
      mode="aspectFit"
      class="map__empty-icon"
    />
    <view class="map__empty-text">
      暂无地址信息，请搜索你的收货地址
    </view>
  </view>

  <!-- 附近poi -->
  <scroll-view
    wx:elif="{{ located || pois.length }}"
    scroll-y
    class="map__pois"
    style="height: {{ windowHeight - 54 - 272 - 40 }}px;"
  >
    <view
      wx:for="{{ pois }}"
      wx:key="id"
      class="map__pois__item"
      data-index="{{ index }}"
      bind:tap="onSelectPoi"
    >
      <view class="map__pois__item__icon">
        <van-icon
          name="location"
          size="16px"
          color="{{ index === 0 ? '#EE0A24' : '#dddee0' }}"
        />
      </view>
      <view class="map__pois__item__title">{{ item.title }}</view>
      <view class="map__pois__item__address">{{ item.address }}</view>
    </view>
  </scroll-view>

  <!-- 用户未授权信息 -->
  <view wx:else class="located" style="height: {{ windowHeight - 54 - 272 - 40 }}px;">
    <image
      class="located__icon"
      mode="aspectFit"
      src="https://img01.yzcdn.cn/public_files/2020/02/12/bc6b35812bc41ee0a100415c438989e1.png"
    />
    <view class="located__text">无法获取到当前位置信息</view>
    <van-button bind:click="onShowAuthDialog" round>重新获取位置</van-button>
  </view>

  <!-- 用户授权弹窗 -->
  <auth-dialog
    show="{{ showAuthDialog }}"
    bind:close="onCloseAuthDialog"
    theme-button="theme-button"
  />
</page-container>

<van-popup
  show="{{ showArea }}"
  position="bottom"
  class="address-map__area"
>
  <van-picker
    show-toolbar
    title="{{ pickerTitle }}"
    columns="{{ countyOptions }}"
    bind:confirm="onConfirm"
    bind:cancel="onCancel"
  />
</van-popup>

<van-toast id="van-toast" />
