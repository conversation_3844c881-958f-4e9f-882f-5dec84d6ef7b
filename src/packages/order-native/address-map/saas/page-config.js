import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { getYunSdk } from '@youzan/weapp-ecloud-sdk';
import injectProcesses from './process';
import { afterSearchEnter } from './event';
import EcloudSpaceBiz from '../ecloud-space';

export default {
  ...YunPageConfig,
  onLoad() {
    const sdk = getYunSdk();

    sdk.setPageEvent('afterSearchEnter', afterSearchEnter);

    this.onAsync('afterSearch', (args) => {
      return afterSearchEnter
        .trigger(args)
        .then((res) => {
          return Promise.resolve(res[0]);
        })
        .catch((e) => {
          throw e;
        });
    });

    injectProcesses({ sdk });

    // 载入定制逻辑
    try {
      EcloudSpaceBiz && EcloudSpaceBiz(sdk);
    } catch (e) {
      //
    }
  },

  onUnload() {
    this.offAsync('afterSearch');
  },
};
