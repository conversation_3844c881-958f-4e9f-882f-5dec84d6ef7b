// import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';
import Toast from '@vant/weapp/dist/toast/toast';
import { queryGiftPacks, queryOrderStatus } from './api/index';
import {
  loggerPrepaidCard,
  loggerEnterPrepaid,
  loggerPrepaidRecommend,
  loggerOnRecharge,
  loggerGoBack,
  loggerPayFail,
  loggerPaySuccess,
  loggerOnClose,
  loggerStartPay,
  loggerClosePayList,
} from './log/logger';

const PAYING_STATUS = {
  FAIL: '充值超时',
  REFUND: '充值转入退款',
  CANCEL: '充值取消',
};

Component({
  properties: {
    show: {
      type: Bo<PERSON><PERSON>,
      observer(show) {
        const { payAssetActivityTagDesc, toRecharge } = this.data;
        if (show) {
          // 有储值推荐第一次下单页进入储值上报事件
          payAssetActivityTagDesc && loggerEnterPrepaid();
          // 储值组件再打开时获取礼包
          this.queryGiftPacks();
          // 直接充值的话就展示 active2
          this.setData({
            activeIndex: toRecharge ? 1 : 0,
          });
        }
      },
    },
    amount: Number,
    valueCard: Object,
    hasExclusionCard: Boolean,
    recommendDetaid: String,
    newRecommend: Boolean,
    payAssetActivityTagDesc: {
      type: String,
      observer(desc) {
        desc && loggerPrepaidRecommend();
      },
    },
    toRecharge: {
      type: Boolean,
      default: false,
    },
    useCustomerDiscount: {
      type: Object,
      default: () => {
        return {
          open: false,
          discount: '',
        };
      },
    },
  },

  data: {
    activeIndex: 0,
    product: {},
    pay: {},
    isGiftFail: false,
  },

  attached() {
    loggerPrepaidCard();
  },

  methods: {
    // 获取礼包内容
    queryGiftPacks() {
      const { recommendDetaid, payAssetActivityTagDesc } = this.data;
      const recommendDetaids = recommendDetaid && JSON.parse(recommendDetaid);
      const productNo = get(recommendDetaids, 'productInfos[0].productNo', '');
      if (payAssetActivityTagDesc) {
        queryGiftPacks({
          productNoList: productNo,
          disableFilter: true // 禁用 “不可用礼包” 过滤（兼容旧版小程序）
        })
          .then((res) => {
            this.setData({
              product: res && res[0],
              isGiftFail: false,
            });
          })
          .catch(() => {
            this.setData({
              isGiftFail: true,
            });
            Toast.fail('获取礼包数据失败');
          });
      }
    },
    onLeft(flag) {
      // 点击返回打点
      flag !== 'close' && loggerGoBack();
      this.setData({
        activeIndex: 0,
      });
    },
    onClose(flag) {
      const { activeIndex } = this.data;
      if (flag !== 'pay' && activeIndex === 1) {
        loggerOnClose();
      }
      this.triggerEvent('onClose', { bubbles: true, composed: true });
      setTimeout(() => {
        this.onLeft('close');
      }, 1000);
    },
    onRefreshData() {
      this.triggerEvent('onRefreshData');
    },
    onRecharge() {
      loggerOnRecharge();
      const {
        product: { giftPacks = [] },
      } = this.data;
      if (giftPacks.length !== 0) {
        if (!this.data.isGiftFail) {
          this.setData({
            activeIndex: 1,
          });
        } else {
          Toast.fail('获取礼包数据失败');
        }
      } else {
        // 如果礼包为空，直接调用 giftPackSheet 的充值能力
        this.selectComponent &&
          this.selectComponent(
            '#prepaid-recommend-gift-pack'
          ).onRechargeClick();
      }
    },
    startCashierPay(data) {
      loggerStartPay();
      const {
        detail: { pay },
      } = data;
      this.onClose('pay');
      this.setData({
        pay,
      })
      if (this.payService) {
        const { prepayId, partnerId, cashierSalt, cashierSign, scene, bizExt, outBizNo } = pay;

        let tradeBizExt = {};
        try {
          tradeBizExt = JSON.parse(bizExt || '{}');
        } catch (error) {
          console.error('预付卡充值业务扩展参数解析失败', e);
        }

        this.payService.startPay({
          prepayId,
          partnerId,
          cashierSalt,
          cashierSign,
          scene,
          tradeBizExt,
          orderNo: outBizNo,
        });
      }
    },
    // 收银台关闭
    onCashierClose(flag) {
      flag !== true && loggerClosePayList();
    },

    // 充值成功
    onPaySuccess() {
      const count = 1;
      Toast.clear();
      Toast.loading({
        mask: true,
        message: '充值中...',
        duration: 0,
      });
      this.rollPayingStatus(count);
    },
    // 查询结果
    rollingOrderStatus() {
      const { outBizNo } = this.data.pay;
      if (!outBizNo) {
        return new Promise(() => {
          const err = new Error('充值单号 丢失');
          err.msg = '充值单号 丢失';
          throw err;
        });
      }
      return queryOrderStatus({ rechargeNo: outBizNo });
    },
    // 充值状态
    rollPayingStatus(count) {
      this.rollingOrderStatus()
        .then(({ status }) => {
          if (status === 2) {
            // 充值成功打点
            loggerPaySuccess();
            this.onClose('pay');
            this.onCashierClose(true);
            Toast.clear();
            Toast.success({
              message: '充值成功',
              duration: 1000,
            });
            setTimeout(() => {
              this.triggerEvent('onRefreshData');
            }, 1000);
            return;
            // return navs.redirect({ url: '/packages/prepaid/success/index' });
          }

          if (status === 3 && status === 4) {
            const err = new Error(`充值状态异常, ${status}`);
            err.msg = `${PAYING_STATUS[status]}`;
            throw err;
          }
          if (count > 10) {
            const err = new Error('充值超时');
            err.msg = '充值超时';
            throw err;
          }
          return setTimeout(() => this.rollPayingStatus(count + 1), 1000);
        })
        .catch((err) => {
          // 充值失败打点
          loggerPayFail();
          Toast.clear();
          this.onClose('pay');
          this.onCashierClose(true);
          Toast.fail(`充值失败, ${err.msg}`);
        });
    },

    async initPayService({ detail }) {
      const { default: PayService } = await import('@youzan/zan-pay-weapp');
      this.payService = new PayService({
        toast: wx.showToast,
        clear: wx.hideToast,
        request: getApp().request,
        biz: 'prepaid_recharge',
        quickMode: true,
        enableMultiplePay: true,
      });
      const crypto = await import('@youzan/crypto');
      this.payService.init(detail, crypto);
    },

    onCashierNavi({ detail: destination }) {
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${destination.url}&title=${destination.title}`
      });
    },
  },
});
