.gitfpack {
  width: 100vw;
  height: calc(80vh - 44px);
  position: relative;
  background: #f7f8fa;
}
.sheet__content {
  flex: 1;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: 15px;
}
.sheet__content__noprotocol {
  height: calc(80vh - 94px);
}
.sheet__content__protocol {
  height: calc(80vh - 140px);
}
.sheet__title {
  font-size: 16px;
  color: #323233;
  font-weight: 500;
  padding: 16px 16px 16px 20px;
}
.sheet__scroll-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  overflow-y: scroll;
  padding: 0 20px;
}
.sheet__footer {
  background: #ffffff;
  box-sizing: border-box;
  position: absolute;
  width: 100%;
  bottom: 0;
}

.footer {
  display: flex;
  padding: 8px 16px 8px;
}


.sheet__footer .van-btn {
  width: 100%;
  flex: 1;
  border: none;
  border-radius: 5px;
  text-align: center;
  font-size: 14px;
  line-height: 2.55555556;
  border-radius: 25px;
  color: #fff;
}
.sheet__footer .recharge__btn {
  border: none;
  height: 44px;
  line-height: 44px;
  border-radius: 22px;
  margin-bottom: 20px;
  box-sizing: border-box;
  width: 100%;
  font-size: 16px;
  color: #fff;
}

.footer__btn {
  flex: 1;
}

.footer__btn button {
  height: 40px;
  font-size: 14px;
  line-height: 40px;
  border-radius: 18px;
}

.sheet__protocol {
  display: flex;
  align-items: center;
  color: #969799;
  font-size: 12px;
  padding: 8px 0;
  margin-left: 16px;
}

.sheet_protocol--no-card-sign {
  display: flex;
  justify-content: center;
  color: #969799;
  font-size: 12px;
  margin-top: 40px;
}

.protocol-desc {
  margin-left: 5px;
}

.protocol-entry {
  font-size: 14px;
  text-align: center;
}

.protocol-nav {
  display: inline-block;
  color: #38f;
}

.sheet__protocol .van-checkbox__icon {
  width: 16px;
  height: 16px;
  position: relative;
  top: 1px;
}
.sheet__protocol .van-checkbox__icon-wrap,
.van-checkbox__label {
  line-height: 18px;
}
.sheet__protocol .van-icon-success {
  height: 14px;
  width: 14px;
  border-radius: 2px;
  border-color: #38f;
}
/* .sheet__protocol .van-checkbox__icon--checked .van-icon-success {
  border-radius: 2px;
  background: #38f;
  border: 1px solid #38f;
  height: 14px;
  width: 14px;
} */
.sheet__protocol .van-checkbox__icon--checked .van-icon-success::before {
  content: '';
  display: block;
  height: 9px;
  background-image: url('https://b.yzcdn.cn/fix-base64/f6338ab60965356cc1abf737a8be19a8003277a2ef81719fc0c50ed389a52ff2.png');
  background-size: 9px 8px;
  background-position: center center;
  background-repeat: no-repeat;
  margin-top: 2px;
}
.sheet__protocol-nav {
  display: inline-block;
  color: #38f;
}
.sheet__card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}
.sheet__card:last-child {
  margin-bottom: 0;
}
.sheet__card--title {
  display: flex;
  height: 32px;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}
.sheet__card--title::before {
  content: '';
  position: absolute;
  left: -50%;
  bottom: 0;
  width: 200%;
  height: 1px;
  background: #ebedf0;
  transform: scale(0.5);
}
.sheet__card--title .name {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}
.sheet__card--title .check-icon {
  display: inline-block;
  vertical-align: middle;
  line-height: 18px;
  height: 18px;
  width: 18px;
  background: #ffffff;
  border: 1px solid #ff4444;
  border-radius: 100%;
  text-align: center;
  line-height: 22px;
}
.sheet__card--title .check-icon .van-icon {
  border-radius: 100%;
  width: 18px;
  height: 18px;
  box-sizing: border-box;
  border: 1px solid #f44;
  color: #fff;
  transition: 0.2s;
  position: relative;
  display: inline-block;
  font: normal normal normal 14px/1 'vant-icon';
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  text-align: center;
  line-height: 22px;
}

.sheet__card--title .check-icon .van-icon::before {
  position: relative;
  left: 1px;
  top: 1px;
}
.sheet__card--title .check-icon .checked {
  background: #f44;
  color: #fff;
  display: inline-block;
  width: 18px;
  height: 18px;
  border-radius: 100%;
}
.sheet__card--title .check-icon .gift-van-icon {
  display: none;
}
.sheet__card--no-gift {
  text-align: center;
}
.sheet__card--no-gift > image {
  width: 160px;
  height: 160px;
  margin: 0 auto;
  margin-top: 90px;
}
.sheet__card--no-gift-text{
  margin-top: 16px;
  font-size: 14px;
  color: #969799;
}
.sheet__sub-title {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  line-height: 22px;
  margin-bottom: 15px;
}
.sheet__rights {
  padding-top: 12px;
  border-radius: 8px;
}
.sheet__right {
  margin-left: 30px;
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 22px;
  position: relative;
}
.sheet__right:last-child {
  margin-bottom: 0;
}
.sheet__right::before {
  top: 0;
  left: -32px;
  width: 22px;
  height: 22px;
  content: ' ';
  position: absolute;
  background-size: cover;
}

.sheet__scroll-content-wrap {
  flex: 1
}

.sheet_protocol--no-card-sign {
  display: flex;
  justify-content: center;
  color: #969799;
  font-size: 12px;
  margin-top: 40px;
}
