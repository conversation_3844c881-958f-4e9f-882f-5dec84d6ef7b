<wxs src="./index.wxs" module="utils" />
<view class="gitfpack">
  <view
    class="sheet__content {{ cardNoSign ? 'sheet__content__noprotocol' : 'sheet__content__protocol' }}"
  >
    <block wx:if="{{!!product.giftPacks.length}}">
      <view class="sheet__title">充值: ¥ {{ utils.balance(product.amount) }}</view>
      <!-- 礼包 -->
      <view 
        class="{{ !!product.giftPacks.length ? 'sheet__scroll-content' : '' }}" 
      >
        <view class="sheet__scroll-content-wrap">
          <view
            wx:for="{{product.giftPacks}}"
            wx:for-item="giftPack"
            wx:key="giftPackId"
            wx:for-index="idx"
            class="sheet__card"
            data-index="{{idx}}"
            bind:tap="cardClick"
          >
            <!-- 标题 -->
            <view class="sheet__card--title">
              <view class="name">{{giftPack.giftPackName}}</view>
              <view class="check-icon" style="border-color: {{ themeMainColor }}">
                <van-icon
                  style="background: {{ themeMainColor }}"
                  class="{{ idx===activeIndex ? 'checked' : 'gift-van-icon' }}"
                  name="success"
                />
              </view>
            </view>
            <!-- 内容 -->
            <view class="sheet__card--content">
              <view class="sheet__rights">
                <rule-rights-detail gift-pack="{{giftPack}}" />
              </view>
            </view>
          </view>
        </view>
        <view
            wx:if="{{cardNoSign}}"
            class="sheet_protocol--no-card-sign"
          >
            查看
            <navigator
              hover-class="none"
              class="sheet__protocol-nav"
              url="/packages/prepaid/agreement/index"
              type="switch"
            >
              《单用途预付卡协议》
            </navigator>
          </view>
      </view>
    </block>
    <block wx:else>
      <view class="sheet__card--no-gift">
        <image src="https://img01.yzcdn.cn/upload_files/2021/05/25/FuAo3tWRwi2u8w5enT3lWCXJbj8r.png" />
        <view class="sheet__card--no-gift-text">
          <view>暂无礼包可赠送</view>
          <view>您可选择继续充值{{ utils.balance(product.amount) }}元或放弃</view>
        </view>
      </view>
    </block>
  </view>
  <!-- 底部 -->
  <view class="sheet__footer">
    <view wx:if="{{!cardNoSign}}" class="sheet__protocol">
      <van-radio-group value="{{isProtocolApproval}}" bind:change="toggleProtocolCheck">
        <van-radio icon-size="16px" name="{{true}}" />
      </van-radio-group>
      <text class="protocol-desc">我已同意</text>
      <navigator
        hover-class="none"
        class="sheet__protocol-nav"
        url="/packages/prepaid/agreement/index"
        type="switch"
      >
        《单用途预付卡协议》
      </navigator>
    </view>
    <view class="footer">
      <van-button
        safe-area-inset-bottom
        custom-class="van-btn theme-bg-color"
        class="footer__btn"
        bind:click="onRechargeClick"
        type="danger"
        disabled="{{utils.disabledRecharge(product.giftPacks,activeIndex)}}"
        loading="{{ rechargeBtnLoading }}"
      >
        立即充值
      </van-button>
    </view>
  </view>
</view>
<van-toast id="van-toast" />
