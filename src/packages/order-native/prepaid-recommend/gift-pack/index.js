import WscComponent from 'pages/common/wsc-component';
import Toast from '@vant/weapp/dist/toast/toast';
import get from '@youzan/weapp-utils/lib/get';
import { signAgreement, getPreOrder } from '../api/index';

const app = getApp();

function assignPayment(prepayParams = {}) {
  prepayParams.scene = 'COMMON';

  return {
    ...prepayParams,
    prepay: prepayParams.prepay || false,
    prepaySuccess: prepayParams.prepay_success,
    extPoint: prepayParams.ext_point_pay_result_v_o || '',
  };
}

WscComponent({
  properties: {
    product: Object,
    valueCard: {
      type: Object,
      observer: 'initState',
    },
    recommendDetaid: {
      type: String,
      value: '',
    },
  },
  data: {
    disabledRecharge: true,
    rechargeBtnLoading: false,
    isRecharging: false, // 是否是充值中
    // 如果没开过卡 需要保存一下新开的卡号呦
    cardNoSign: '',
    // 如果只有一个礼包默认选中
    activeIndex: -1,
    // 收银台相关参数
    showCashier: false,
    payWays: {},
    pay: {},
    isProtocolApproval: false,
  },
  attached() {
    app.getShopTheme().then((res) => {
      this.setYZData({
        themeMainColor: res.colors['main-bg'],
      });
    });
  },
  methods: {
    // 设置卡号
    initState() {
      const recommendDetaid =
        this.data.recommendDetaid && JSON.parse(this.data.recommendDetaid);
      const { cardNo } = recommendDetaid;
      if (cardNo) {
        this.setYZData({
          cardNoSign: cardNo,
        });
      }
    },
    // 点击充值进行校验
    onRechargeClick() {
      const {
        cardNoSign,
        isRecharging,
        isProtocolApproval,
        product,
      } = this.data;
      if (isRecharging) return;

      const giftPacks = product?.giftPacks;
      // 没勾选协议 && 第一次开卡 && 礼包个数 > 0
      if (!isProtocolApproval && !cardNoSign && giftPacks.length !== 0) {
        Toast({ message: '请先勾选协议', position: 'bottom' });
        return;
      }

      this.toggleRecharging(true);
      this.toggleRechargeBtnLoading(true);

      if (!cardNoSign) {
        return this.signPrepaidAgreement().then(() =>
          this.startCashierPay(this.preparePrepay())
        );
      }
      if (cardNoSign) {
        this.startCashierPay(this.preparePrepay());
      }
    },

    // 预下单参数
    preparePrepay() {
      const {
        product: { amount: payAmount },
        cardNoSign = '',
      } = this.data;
      const {
        giftPackName: goodsName = '储值推荐固定金额储值',
        factType: tradeDesc = 'VLCARD_RCHG',
        giftPackId: ruleNo = '0',
        giftPackVersion: ruleVersion = '',
      } = this.activeGiftPack();
      return {
        kdtId: app.getKdtId(),
        cardNo: cardNoSign,
        payAmount,
        goodsName,
        tradeDesc,
        ruleVersion,
        ruleNo,
        /** 2 小程序-下单页充值: wx_app_trade  */
        source: 2,
        extendsInfo: {
          kdtId: app.getKdtId(),
          ruleNo,
          cardNo: cardNoSign,
          productNo: this.productNo,
          amount: payAmount,
          pubKdtId: app.getKdtId(),
          acpKdtId: app.getKdtId(),
          marketChannel: '1',
          weappRedirectUrl: '',
          successRedirect: '',
        },
      };
    },

    // 签约开卡
    signPrepaidAgreement() {
      const { templateNo } = JSON.parse(this.data.recommendDetaid);
      return signAgreement({
        templateNo,
      })
        .then((res) => {
          // 保存开卡
          this.setYZData({
            cardNoSign: res.cardNo,
          });
          return res;
        })
        .catch(() => {
          Toast.fail('签约开卡失败');
          this.toggleRecharging(false);
          this.toggleRechargeBtnLoading(false);
        });
    },

    // 开始预下单
    startCashierPay(prepayParams) {
      return this.preOrder(prepayParams).catch((error) => {
        this.setYZData({
          showCashier: false,
        });
        Toast.fail(`预充值失败${error.msg}`);
        this.toggleRecharging(false);
        this.toggleRechargeBtnLoading(false);
      });
    },

    // 开始充值
    preOrder(prepayParams) {
      return getPreOrder(prepayParams).then((res) => {
        // this.$emit('onClose');
        this.toggleRecharging(false);
        this.toggleRechargeBtnLoading(false);
        this.triggerEvent('startCashierPay', {
          showCashier: true,
          payWays: res.payChannels || [],
          pay: {
            ...assignPayment({ ...res, extendsInfo: prepayParams.extendsInfo }),
          },
        });
      });
    },

    // 关闭弹窗
    onClose() {
      this.triggerEvent('onClose');
    },
    // 选择礼包索引
    cardClick(e) {
      const idx = e.currentTarget.dataset.index;
      const { activeIndex } = this.data;
      this.setYZData({
        activeIndex: activeIndex === idx ? -1 : idx,
      });
    },
    // 选中的礼包
    activeGiftPack() {
      return get(this, `data.product.giftPacks[${this.data.activeIndex}]`, {});
    },
    toggleRecharging(bol) {
      this.setYZData({
        isRecharging: bol,
      });
    },

    toggleRechargeBtnLoading(bol) {
      this.setYZData({
        rechargeBtnLoading: bol,
      });
    },

    // 切换协议
    toggleProtocolCheck() {
      const { isProtocolApproval } = this.data;
      this.setYZData({
        isProtocolApproval: !isProtocolApproval,
      });
    },
  },
});
