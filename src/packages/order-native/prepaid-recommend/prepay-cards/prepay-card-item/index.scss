.gift-card-item-wrapper {
  border-radius: 4px;
  background-color: #fff;
  margin: 12px 15px 0;
}

.gift-card-item__info {
  display: flex;
}

.gift-card-item {
  height: 84px;
  overflow: hidden;
  position: relative;
  background-color: #fff;
  box-sizing: border-box;
  display: flex;

  &__title {
    position: relative;
    margin-bottom: 10px;
    height: 18px;
    font-size: 14px;
    line-height: 1.29;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;

    &--hascover {
      color: #fff;
    }

    &--nocover {
      color: #323233;
    }

    image {
      left: 0;
      width: 22px;
      height: 22px;
      position: absolute;
    }
  }

  &__date {
    height: 28px;
    box-sizing: border-box;
    display: inline-block;
    width: 130px;
    min-width: 130px;
    display: flex;
    align-items: flex-end;
  }

  &--disabled {
    margin-bottom: 0;
    opacity: 0.4;
  }

  &--hascover {
    border-radius: 4px 4px 0 0;
  }
  &--nocover {
    border-radius: 4px;
  }

  &__reason {
    font-size: 12px;
    padding: 8px 12px;
    background-color: #fff;
    border-radius: 0 0 4px 4px;
    position: relative;
    color: #646566;

    &--nocover::before {
      content: '';
      position: absolute;
      top: 0;
      width: calc(100% - 24px);
      height: 1px;
      background-image: repeating-linear-gradient(
        -90deg,
        transparent,
        transparent 3px,
        rgba(235, 237, 240, 1) 3px,
        rgba(235, 237, 240, 1) 10px
      );
    }
  }

  &__back {
    width: 100%;
    height: 55px;
    position: absolute;
    bottom: 0;
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0) 9%, rgba(0, 0, 0, 0.24));
  }
}

.gift-card-item--left {
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 14px 0 14px 12px;
}
.gift-card-item--right {
  display: flex;
  align-items: center;
  font-size: 18px;
  padding: 0 24px;

  &--hascover {
    color: #fff;
  }

  &--nocover {
    color: #969799;
  }
}
.gift-card-item__account {
  height: 28px;
  font-weight: 600;
  line-height: 0.93;
  flex: 1;

  &--hascover {
    color: #fff;
  }

  &--nocover {
    color: #323233;
  }
}
.gift-card-item__unit {
  font-size: 14px;
  display: inline-block;
}
.gift-card-item__int {
  font-weight: 600;
  display: inline-block;
  height: 28px;
  line-height: 28px;
}
.gift-card-item__float {
  font-size: 16px;
  font-weight: 600;
  display: inline-block;
}
.gift-card-item__desc-name {
  font-size: 14px;
  font-weight: 500;
}
.gift-card-item__desc-date {
  font-size: 12px;
  color: #969799;
  width: 100%;
  text-align: right;
}
.gift-card-item__icon--checked--nocover {
  color: #ee0a24;
}
.gift-card-item__icon--checked--hascover {
  color: #fff;
}