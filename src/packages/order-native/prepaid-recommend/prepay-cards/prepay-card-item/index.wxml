<wxs src="./index.wxs" module="utils" />
<view class="gift-card-item-wrapper">
  <view 
    class="gift-card-item {{ disabled ? 'gift-card-item--disabled' : '' }} {{ utils.hasCover(data.cardCover) ? 'gift-card-item--hascover' : 'gift-card-item--nocover' }}"
    style="background-image: url({{data.cardCover}})"
  >
    <view class="gift-card-item--left">
      <view class="gift-card-item__title {{ utils.hasCover(data.cardCover) ? 'gift-card-item__title--hascover' : 'gift-card-item__title--nocover' }}">
        {{ data.cardName }}余额
      </view>
      <view class="gift-card-item__info">
        <view class="gift-card-item__account {{ utils.hasCover(data.cardCover) ? 'gift-card-item__account--hascover' : 'gift-card-item__account--nocover' }}">
          <view class="gift-card-item__unit">￥</view>
          <view class="gift-card-item__int" style="font-size: {{utils.flexPriceIntFontSize(data.balance)}}">
            {{ utils.formatPriceInt(data.balance) }}
          </view>
          <view class="gift-card-item__float">.{{ utils.formatPriceCent(data.balance) }}</view>
        </view>
        <view class="gift-card-item__date">
          <view class="gift-card-item__desc-date" wx:if="{{ data.expireTime }}">有效期至 {{ data.expireTime }}</view>
        </view>
      </view>
    </view>
    <view class="gift-card-item--right">
      <view style="visibility: {{disabled ? 'hidden' : 'visible'}}; height: 18px">
        <van-icon style="color: {{ themeMainColor }}" class="{{ utils.hasCover(data.cardCover) ? 'gift-card-item__icon--checked--hascover' : 'gift-card-item__icon--checked--nocover' }}" name="checked" wx:if="{{checked}}" />
        <van-icon class="{{ utils.hasCover(data.cardCover) ? 'gift-card-item--right--hascover' : 'gift-card-item--right--nocover' }}" name="circle" wx:if="{{!checked}}" />
      </view>
    </view>
    <view wx:if="{{ utils.hasCover(data.cardCover) }}" class="gift-card-item__back" />
  </view>
  <view wx:if="{{ disabled && data.unusableReason }}" class="gift-card-item__reason {{utils.hasCover(data.cardCover) ? 'gift-card-item__reason--hascover' : 'gift-card-item__reason--nocover'}}">
    {{ data.unusableReason }}
  </view>
  <view wx:elif="{{ data.useSpecification }}" class="gift-card-item__reason {{utils.hasCover(data.cardCover) ? 'gift-card-item__reason--hascover' : 'gift-card-item__reason--nocover'}}">
    {{ checked ? '取消使用该卡类，可重新享受店铺活动、优惠券、积分、储值专享折扣等优惠' : '该卡不可与店铺活动、优惠券、积分、储值专享折扣同时使用，限原价购买' }}
  </view>
  <view wx:if="{{showFreeze}}" class="gift-card-item__reason">
    待付款订单冻结金额{{ utils.formatPrice(data.freezingSumDnom) }}元，取消后冻结余额可释放。
  </view>
</view>