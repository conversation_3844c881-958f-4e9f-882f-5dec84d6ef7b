module.exports = {
  formatPriceInt(price) {
    const p = (price / 100).toFixed(2);
    return p.split('.')[0];
  },
  formatPriceCent(price) {
    const p = (price / 100).toFixed(2);
    return p.split('.')[1];
  },
  flexPriceIntFontSize(price) {
    const p = (price / 100).toFixed(2);
    const i = p.split('.')[0];
    if (i.length < 6) {
      return '8vw';
    }
    return (144 / i.length) / 375 * 100 + 'vw';
  },
  hasCover(cover) {
    return !!cover;
  }
};
