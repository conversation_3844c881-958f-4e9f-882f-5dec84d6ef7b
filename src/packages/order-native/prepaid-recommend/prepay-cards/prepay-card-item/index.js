import WscComponent from 'pages/common/wsc-component';
import computed from './computed';

const app = getApp();

WscComponent({
  properties: {
    data: Object,
    checked: Boolean,
    disabled: Boolean,
    cardType: String
  },

  data: {
    noticeText: '',
  },

  methods: {
    initState() {
      const { data, disabled, checked } = this.data;
      this.setYZData({
        showFreeze: computed.showFreeze(disabled, data, checked),
      });
    },
  },

  attached() {
    this.initState();
    app.getShopTheme().then(res => {
      this.setYZData({
        themeMainColor: res.colors['main-bg'],
      });
    });
  }
});
