export default {
  formatPrice(price) {
    return price % 100 === 0 ? Math.round(price / 100) : (price / 100).toFixed(2);
  },
  noticeText(disabled, data, checked) {
    let result = '';
    if (!disabled && data.showUseSpecification) {
      result += checked
        ? '取消使用该卡类，可重新享受店铺活动、优惠券、积分、储值专享折扣等优惠。'
        : '该卡不可与店铺活动、优惠券、积分、储值专享折扣同时使用，限原价购买。';
    }
    if (!disabled && data.freezingSumDnom > 0) {
      result += `待付款订单冻结金额${this.formatPrice(data.freezingSumDnom)}元，取消后冻结余额可释放。`;
    }
    return result;
  },
  showFreeze(disabled, data) {
    return !disabled && data.freezingSumDnom > 0;
  },
};
