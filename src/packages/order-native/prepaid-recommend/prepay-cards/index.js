import { VanxComponent } from 'pages/common/wsc-component/index';
import Dialog from '@vant/weapp/dialog/dialog';

const TAB_LIST = [
  { title: '可用', value: 'list' },
  { title: '不可用', value: 'disabled' },
];

function sum(arr) {
  return arr.reduce((prev, curr) => prev + curr, 0);
}
let HAS_EXCLUSION_CARD = false;
VanxComponent({
  options: {
    styleIsolation: 'shared',
  },
  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer(show) {
        if (show) {
          const { valueCardList, hasExclusionCard } = this.data;
          // 缓存下互斥的卡状态
          HAS_EXCLUSION_CARD = hasExclusionCard;
          this.setYZData(
            {
              valueCard: { ...valueCardList },
            },
            () => {
              this.prepayCardTotalAmount();
            }
          );
          // 暂时解决vant-tab 在component下没选择样式的问题
          setTimeout(() => {
            const vanTabs = this.selectComponent('#vanTabs');
            vanTabs.setActive(0);
          }, 1000 / 30);
        }
      },
    },
    amount: {
      type: Number,
    },
    product: Object,
    valueCardList: Object,
    hasExclusionCard: Boolean,
    recommendDetaid: String,
    payAssetActivityTagDesc: String,
    newRecommend: Boolean,
    useCustomerDiscount: {
      type: Object,
      observer(discountInfo) {
        discountInfo.open &&
          this.setYZData({
            discountText: `${discountInfo.discount / 10}折`,
          });
      },
    },
    that: Object,
  },

  data: {
    TAB_LIST,
    active: 0,
    prepayCardTotalAmount: 0, // 金额
    integerStr: '', // 金额整数位
    decimalStr: '', // 金额小数位
    customerDiscountNotFullPay: false,
    discountText: '',
    valueCard: {
      checked: [],
      disabled: [],
      list: [],
    },
    themeColor: '#ee0a24',
  },

  methods: {
    // 储值卡、礼品卡中是否存在互斥卡
    hasExclusionCard() {
      const { valueCard } = this.data;
      const checkedList = [...valueCard.checked];
      const cardList = [...valueCard.list];
      return checkedList.some((cardNo) => {
        const card =
          cardList.find((item) => item.summaryCardNo === cardNo) || {};
        return card.isExclusion;
      });
    },
    // 自己维护的卡
    checkedCard({ card, cardType, valueCard }) {
      const state = {
        valueCard: { ...valueCard },
      };
      const { checked } = state[cardType];
      const index = checked.indexOf(card.summaryCardNo);
      if (index !== -1) {
        state[cardType].checked = [
          ...checked.filter((cardNo) => cardNo !== card.summaryCardNo),
        ];
      } else {
        state[cardType].checked = [...checked, card.summaryCardNo];
      }

      this.setYZData(
        {
          valueCard: { ...state.valueCard },
        },
        () => {
          this.prepayCardTotalAmount();
        }
      );
    },
    onRefreshData() {
      this.triggerEvent('onRefreshData');
    },
    onCheck(data) {
      const {
        detail: { card, cardType },
      } = data;

      const { valueCard } = this.data;
      this.checkedCard({
        card,
        cardType,
        valueCard,
      });
    },

    // 选中储值卡的总金额
    valueCardTotalAmount() {
      const { valueCard } = this.data;
      const list = valueCard && valueCard.list;
      const checked = (list || []).filter(
        (card) => valueCard.checked.indexOf(card.summaryCardNo) !== -1
      );
      return sum(checked.map((item) => item.balance));
    },

    // 选中预付卡的总金额
    prepayCardTotalAmount() {
      const prepayCardTotalAmount = this.valueCardTotalAmount();
      const priceStrArr =
        typeof prepayCardTotalAmount === 'number' &&
        (prepayCardTotalAmount / 100).toFixed(2).split('.');
      this.setYZData({
        prepayCardTotalAmount,
        integerStr: priceStrArr && priceStrArr[0],
        decimalStr: priceStrArr ? '.' + priceStrArr[1] : '',
        customerDiscountNotFullPay:
          this.data.useCustomerDiscount &&
          this.data.useCustomerDiscount.open &&
          prepayCardTotalAmount < this.data.amount,
      });
    },

    onTabChange(e) {
      this.setYZData({ active: e.detail.index || 0 });
    },

    onConfirm() {
      const { valueCard, valueCardList } = this.data;

      if (this.data.customerDiscountNotFullPay) {
        Dialog.confirm({
          message: `订单使用了“储值专享折扣（${this.data.discountText}）“，修改选卡金额将导致优惠不可用，确定操作吗？`,
        })
          .then(
            () => {
              this.triggerEvent(
                'onConfirm',
                {
                  valueCard,
                  // 当用户反选所有卡并确定时，保持选中信息
                  keepSelect:
                    valueCard.checked.length === 0 &&
                    valueCardList.checked.length !== 0,
                  exclusionCard: this.hasExclusionCard() !== HAS_EXCLUSION_CARD,
                  isCustomerDiscountNotFullPay: true,
                },
                { bubbles: true, composed: true }
              );
              Dialog.close();
            },
            () => {
              const { valueCardList } = this.data;
              this.setYZData(
                {
                  valueCard: { ...valueCardList },
                },
                () => {
                  this.prepayCardTotalAmount();
                }
              );
            }
          )
          .catch(() => {
            Dialog.close();
          });
      } else {
        this.triggerEvent(
          'onConfirm',
          {
            valueCard,
            // 当用户反选所有卡并确定时，保持选中信息
            keepSelect:
              valueCard.checked.length === 0 &&
              valueCardList.checked.length !== 0,
            exclusionCard: this.hasExclusionCard() !== HAS_EXCLUSION_CARD,
            isCustomerDiscountNotFullPay: false,
          },
          { bubbles: true, composed: true }
        );
      }
    },

    onRecharge() {
      this.triggerEvent('onRecharge');
    },
  },

  created() {
    if (getApp) {
      const app = getApp();
      app.getShopTheme().then((res) => {
        const themeColor = res.colors['main-bg'];
        this.setYZData({
          themeColor,
        });
      });
    }
  },
});
