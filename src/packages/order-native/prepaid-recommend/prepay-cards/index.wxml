<wxs src="./index.wxs" module="computed" />

<view class="prepay-card {{ themeClass }} page-{{ deviceType }}">
  <view class="prepay-card__header">
    <van-tabs active="1" bind:change="onTabChange" id="vanTabs" line-width="40" color='{{themeColor}}'>
      <van-tab wx:for="{{ TAB_LIST }}" wx:key="value" title="{{ item.title }}" />
    </van-tabs>
  </view>
  <!-- 可用 -->
  <view hidden="{{ active === 1 }}" class="prepay-card__content">
    <view class="prepay-card__empty" wx:if="{{ computed.emptyCard(valueCard, payAssetActivityTagDesc, newRecommend) }}">
      <image class="prepay-card__empty-img" src="https://img01.yzcdn.cn/upload_files/2021/05/25/Fukq3YsmMep2SuYUEJ5ICMoBePTs.png" />
      <view class="prepay-card__empty-text">
        暂无余额或卡
      </view>
    </view>
    <prepay-card-balance
      product="{{product}}"
      value-card="{{valueCard}}"
      value-card-recommend="{{recommendDetaid}}"
      pay-asset-activity-tag-desc="{{payAssetActivityTagDesc}}"
      new-recommend="{{newRecommend}}"
      bind:onRecharge="onRecharge"
      bind:onCheck="onCheck"
      bind:onRefreshData="onRefreshData"
      that="{{that}}"
      card-type="valueCard"
    ></prepay-card-balance>
    <prepay-card-list
      show="{{show}}"
      value-card="{{valueCard}}"
      has-exclusion-card="{{hasExclusionCard}}"
      card-type="valueCard"
      bind:onCheck="onCheck"
      that="{{that}}"
    />
  </view>
  <!-- 不可用 -->
  <view hidden="{{ active === 0 }}" class="prepay-card__content">
    <view class="prepay-card__empty" wx:if="{{ computed.emptyDisabledCard(valueCard) }}">
      <image class="prepay-card__empty-img" src="https://img01.yzcdn.cn/upload_files/2021/05/25/Fukq3YsmMep2SuYUEJ5ICMoBePTs.png" />
      <view class="prepay-card__empty-text">
        暂无余额或卡
      </view>
    </view>
    <prepay-card-balance
      value-card="{{valueCard}}"
      value-card-recommend="{{recommendDetaid}}"
      pay-asset-activity-tag-desc="{{payAssetActivityTagDesc}}"
      disabled
      card-type="valueCard"
      bind:onCheck="onCheck"
    ></prepay-card-balance>
    <prepay-card-list
      show="{{show}}"
      value-card="{{valueCard}}"
      has-exclusion-card="{{hasExclusionCard}}"
      disabled
      card-type="valueCard"
      bind:onCheck="onCheck"
      that="{{that}}"
    />
  </view>
  <van-notice-bar
    wx:if="{{ computed.customerDiscountNotFullPay(useCustomerDiscount, amount, prepayCardTotalAmount) }}"
    scrollable="{{ false }}"
    text="{{ computed.noticeText(useCustomerDiscount, amount) }}"
  />
  <van-submit-bar
    button-text="完成"
    bind:submit="onConfirm"
  >
    <view class='submit-bar__text'>
      <text class='submit-bar__text-label'>可折扣：</text>
      <view class='submit-bar__text-price' style='color: {{themeColor}};'>
        <text class='submit-bar__text-price-currency'>￥</text>
        <text class='submit-bar__text-price-imteger'>{{integerStr}}</text>
        <text class='submit-bar__text-price-float'>{{decimalStr}}</text>
      </view>
    </view>
  </van-submit-bar>

  <van-dialog id="van-dialog" />
</view>
