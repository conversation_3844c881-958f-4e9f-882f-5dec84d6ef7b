module.exports = {
  formatValueCard(valueCard, cardType, disabled) {
    if (valueCard !== null) {
      const valueList = (valueCard[disabled ? 'disabled' : 'list'] || []).filter(
        card => !(card.cardType === 102 && card.cardSubType === 1001)
      );
      valueCard[disabled ? 'disabled' : 'list'] = valueList;
      return valueCard;
    }
  },
  title(cardType) {
    let title = '';

    switch (cardType) {
      case 'valueCard':
        title = '储值卡'; break;
      default: break;
    }

    return title;
  },

  list(data, cardType, disabled) {
    return data[cardType][disabled ? 'disabled' : 'list'];
  },

  showLimit(data, cardType, disabled) {
    const list = data[cardType][disabled ? 'disabled' : 'list'] || [];
    return list.length === 50;
  },

  showList(data, cardType, disabled) {
    if (!data || data == null || data[cardType] == null) {
      return false;
    }
    return !!data[cardType][disabled ? 'disabled' : 'list'].length;
  },

  isChecked(data, card, cardType, disabled) {
    if (disabled) {
      return false;
    }

    return data[cardType].checked.indexOf(card.summaryCardNo) !== -1;
  }
};
