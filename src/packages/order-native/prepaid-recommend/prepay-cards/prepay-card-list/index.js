import Dialog from '@vant/weapp/dist/dialog/dialog';
import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    show: Boolean,
    cardType: String,
    disabled: Boolean,
    valueCard: Object,
    hasExclusionCard: Boolean,
    that: Object
  },

  methods: {
    ensure(card) {
      const { cardType, that } = this.data;
      const { checked = [] } = this.data[cardType] || {};

      // 选中互斥卡需要提示一下
      if (card.isExclusion) {
        const isChecked = checked.indexOf(card.summaryCardNo) > -1;
        const message = isChecked
          ? '若取消使用该类卡，系统将重新计算所有优惠活动价格'
          : '若使用该类卡，将不能享受店铺活动、优惠券、积分、储值专享折扣等优惠。是否继续使用该卡？';

        return Dialog.confirm({
          message,
          context: that
        });
      }

      return Promise.resolve();
    },

    // 卡选中
    onCheck(event) {
      const { disabled, cardType } = this.data;

      if (disabled) {
        return;
      }

      const { card } = event.target.dataset;
      this.ensure(card)
        .then(() => {
          this.triggerEvent('onCheck', {
            card,
            cardType
          });
        })
        .catch(() => {});
    }
  }
});
