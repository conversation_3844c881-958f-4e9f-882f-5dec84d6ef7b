<wxs src="./index.wxs" module="computed" />

<view
  wx:if="{{ computed.showList({ valueCard: computed.formatValueCard(valueCard, cardType, disabled) }, cardType, disabled) }}"
  class="prepay-card__list"
>
  <view class="prepay-card__list__title">{{ computed.title(cardType) }}</view>
  <prepay-card-item
    wx:for="{{ computed.list({ valueCard: computed.formatValueCard(valueCard, cardType, disabled) }, cardType, disabled) }}"
    wx:key="id"
    data="{{ item }}"
    data-card="{{ item }}"
    checked="{{ computed.isChecked({ valueCard: computed.formatValueCard(valueCard, cardType, disabled) }, item, cardType, disabled) }}"
    disabled="{{ disabled }}"
    card-type="{{ cardType }}"
    bind:tap="onCheck"
  />
  <view wx:if="{{ computed.showLimit({ valueCard: computed.formatValueCard(valueCard, cardType, disabled) }, cardType, disabled) }}" class="prepay-card__limit">
    最多展示50张礼品卡
  </view>
</view>
