<wxs src="./index.wxs" module="utils" />
<view wx:if="{{showBalance}}">
  <view class="prepay-balance__title">余额</view>
  <view class="prepay-balance-item-wrapper">
    <view
      class="prepay-balance-item {{ disabled ? 'prepay-balance-item--disabled' : '' }}"
      style="background-image: {{ themeColorLinear }}"
      bind:tap="onCheck"
    >
      <view class="prepay-balance-item--left">
        <view class="prepay-balance-item__title">
          <view class="title-h2">储值余额</view>
        </view>
        <view class="prepay-balance-item__account">
          <view class="prepay-balance-item__unit">￥</view>
          {{ balance.balanceInt }}
          <view class="prepay-balance-item__float">.{{ balance.balanceCent }}</view>
        </view>
      </view>
      <view class="prepay-balance-item--right" wx:if="{{ !disabled && !balanceZero }}">
        <van-icon name="checked" wx:if="{{checked}}" />
        <van-icon name="circle" wx:if="{{!checked}}" />
      </view>
    </view>
    <view wx:if="{{giveRecharge}}" class="prepay-balance-item__agreement">
      选择使用余额即表示同意
      <navigator
        hover-class="none"
        class="recharge__protocol-nav"
        url="/packages/prepaid/agreement/index"
        type="switch"
      >
        《单用途预付卡协议》
      </navigator>
    </view>
    <view wx:if="{{showReason}}" class="prepay-balance-item__reason">
      {{balance.unusableReason}}
    </view>
    <view wx:if="{{showFreeze}}" class="prepay-balance-item__reason">
      待付款订单冻结金额{{ utils.formatPrice(balance.freezingSumDnom) }}元，取消后冻结余额可释放。
    </view>
  </view>
  <view
    wx:if="{{payAssetActivityTagDesc && showRecommend && !newRecommend && !disabled}}"
    class="prepay-balance-item__giftpack"
    catch:tap="onRecharge"
  >
    <view class="giftpack-slogan">
      <text>充</text><text class="gitftpack-amount">{{valueCardRecommendAmount}}</text><text>元享</text><text class="gitftpack-part">好礼</text>
    </view>
  </view>
  <view wx:if="{{payAssetActivityTagDesc && showRecommend && !newRecommend && !giveRecharge && !disabled && isEmptyGiftPack}}" class="prepay-balance__protocol">
    立即充值即表示同意
    <navigator
      hover-class="none"
      class="prepay-balance__protocol-nav"
      url="/packages/prepaid/agreement/index"
      type="switch"
    >
      《单用途预付卡协议》
    </navigator>
  </view>
</view>
