import WscComponent from 'pages/common/wsc-component';
import Toast from '@vant/weapp/dist/toast/toast';
import computed from './computed';
import { signAgreement } from '../../api/index';
import Dialog from '@vant/weapp/dist/dialog/dialog';

const app = getApp();

WscComponent({
  properties: {
    disabled: Boolean,
    cardType: String,
    valueCard: {
      type: Object,
      observer: 'initState',
    },
    valueCardRecommend: {
      type: String,
      observer: 'initState',
    },
    product: {
      type: Object,
      observer(value) {
        this.setYZData({
          isEmptyGiftPack: value.giftPacks?.length === 0,
        });
      },
    },
    payAssetActivityTagDesc: String,
    newRecommend: Boolean,
    that: Object,
  },

  data: {
    iconUrl: '',
    valueCardRecommendAmount: '',
    hasCardRecommendDesc: '',
    showBalance: false,
    showReason: false,
    checked: false,
    showRecommend: false,
    showFreeze: false,
    balance: {},
    prepaidPrefix: '储值',
    giveRecharge: false,
    balanceZero: false,
    isRecharged: true,
    isEmptyGiftPack: false,
  },

  attached() {
    // this.initState();
    app.getShopTheme().then(({ colors }) => {
      this.setYZData({
        themeColorLinear: `linear-gradient(to right, ${colors['start-bg']} 0%, ${colors['end-bg']} 100%)`,
      });
    });
  },

  methods: {
    initState(ifGiveRecharge) {
      const {
        valueCardRecommend,
        valueCard,
        disabled,
        payAssetActivityTagDesc,
        newRecommend,
      } = this.data;
      const valueCardRecommends =
        valueCardRecommend && JSON.parse(valueCardRecommend);
      const balance = computed.balance({
        valueCard,
        disabled,
        payAssetActivityTagDesc,
        newRecommend,
      });
      const valueCardRecommendAmount =
        computed.valueCardRecommendAmount(valueCardRecommends);
      this.setYZData({
        iconUrl: computed.iconUrl(),
        valueCardRecommendAmount,
        hasCardRecommendDesc:
          computed.hasCardRecommendDesc(valueCardRecommends),
        showBalance: computed.showBalance(balance),
        showReason: computed.showReason(disabled, balance),
        checked: computed.checked(balance, disabled, valueCard),
        showRecommend: computed.showRecommend(valueCardRecommendAmount),
        showFreeze: computed.showFreeze(disabled, balance),
        giveRecharge:
          ifGiveRecharge === false ? ifGiveRecharge : balance.giveRecharge,
        balance,
        balanceZero: Number(balance.balance) === 0,
        isRecharged: !!valueCardRecommends.cardNo,
      });
    },
    ensure() {
      const { that, checked, balance } = this.data;
      // 选中互斥卡需要提示一下
      if (balance.isExclusion) {
        const message = checked
          ? '若取消使用储值余额，系统将重新计算所有优惠活动价格'
          : '若使用储值余额，将不能享受店铺活动、优惠券、积分、储值专享折扣等优惠，是否继续使用？';
        return Dialog.confirm({
          message,
          context: that,
        });
      }

      return Promise.resolve();
    },
    onCheck() {
      const { balance, disabled, cardType, valueCardRecommend, checked } =
        this.data;
      const valueCardRecommends =
        valueCardRecommend && JSON.parse(valueCardRecommend);
      const { cardNo } = valueCardRecommends;
      // 余额 > 0 && 可用列表 && 存在卡号才能被选中
      if (Number(balance.balance) > 0 && !disabled && !!balance.summaryCardNo) {
        if (!checked && !cardNo && balance.giveRecharge) {
          this.signPrepaidAgreement();
        }
        this.ensure()
          .then(() => {
            this.triggerEvent(
              'onCheck',
              {
                card: balance,
                cardType,
              },
              {
                bubbles: true,
                composed: true,
              }
            );
          })
          .catch(() => {});
      }
    },

    // 储值返现签约开卡
    signPrepaidAgreement() {
      return signAgreement()
        .then((res) => {
          res.cardNo && this.triggerEvent('onRefreshData');
          setTimeout(() => {
            this.initState(false);
          }, 1000 / 30);
        })
        .catch(() => {
          Toast.fail('签约开卡失败');
        });
    },

    onRecharge() {
      this.triggerEvent('onRecharge');
    },
  },
});
