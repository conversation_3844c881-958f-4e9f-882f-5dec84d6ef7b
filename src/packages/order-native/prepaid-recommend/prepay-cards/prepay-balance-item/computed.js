import get from '@youzan/weapp-utils/lib/get';

export default {
  formatPrice(price) {
    return price % 100 === 0
      ? Math.round(price / 100)
      : (price / 100).toFixed(2);
  },
  iconUrl() {
    return '//img01.yzcdn.cn/public_files/2019/06/19/5a0cfe14dfa8a842fc23fff4da8b1c51.png';
  },

  valueCardRecommendAmount(valueCardRecommend) {
    const amount = get(valueCardRecommend, 'productInfos[0].amount', 0);
    return this.formatPrice(amount);
  },

  hasCardRecommendDesc(valueCardRecommend) {
    return get(valueCardRecommend, 'desc.length', 0) > 0;
  },

  showBalance(balance) {
    // 当存在存在余额或余额为 0 时, 展示余额卡
    return !!balance.balance || balance.balance === 0;
  },

  showReason(disabled, balance) {
    return (disabled && balance.unusableReason) || false;
  },

  showFreeze(disabled, balance) {
    return !disabled && balance.freezingSumDnom > 0;
  },

  checked(balance, disabled, valueCard) {
    // 在不可用列表 或 没有卡号的卡不能选中
    if (disabled || !balance.summaryCardNo) {
      return false;
    }
    // 当前卡在 选中数组中时才会被选中
    return get(valueCard, 'checked', []).indexOf(balance.summaryCardNo) !== -1;
  },

  showRecommend(valueCardRecommendAmount) {
    // 当存在推荐金额时展示储值推荐
    if (valueCardRecommendAmount === '') return false;
    return valueCardRecommendAmount || valueCardRecommendAmount === 0;
  },

  intPrice(price = 0) {
    return Number(price).toFixed(2).split('.')[0];
  },
  centPrice(price = 0) {
    return Number(price).toFixed(2).split('.')[1];
  },

  balance({ valueCard, disabled, payAssetActivityTagDesc, newRecommend }) {
    if (!valueCard) {
      return {};
    }
    // 储值卡类型 cardSubType === 1001 || 1000 时为余额卡，可充值
    const card = valueCard[disabled ? 'disabled' : 'list'].find(
      (card) => card.cardType === 102 && card.cardSubType === 1001
    );
    // 如果在列表中发现余额卡直接返回
    if (card) {
      const price = this.formatPrice(card.balance);
      return {
        ...card,
        balance: price,
        balanceInt: this.intPrice(price),
        balanceCent: this.centPrice(price),
      };
    }
    // 没有余额卡 =>
    // 非不可用列表 && 存在储值推荐 && 非充值并支付，手动构造一张余额为零的空卡
    return !disabled && payAssetActivityTagDesc && !newRecommend
      ? { balance: 0, balanceInt: '0', balanceCent: '00' }
      : {};
  },
};
