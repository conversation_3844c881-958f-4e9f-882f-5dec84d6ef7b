module.exports = {
  emptyDisabledCard(valueCard) {
    const valueCardDisabled = valueCard.disabled || [];
    return valueCardDisabled.length === 0;
  },
  emptyCard(valueCard, payAssetActivityTagDesc, newRecommend) {
    const valueCardList = valueCard.list || [];
    // 如果没有余额或卡 并且 （没有储值推荐 或者 是充值并支付模式）
    return valueCardList.length === 0 && (!payAssetActivityTagDesc || newRecommend);
  },

  customerDiscountNotFullPay(useCustomerDiscount = {}, amount, prepayCardTotalAmount) {
    return useCustomerDiscount && useCustomerDiscount.open && prepayCardTotalAmount < amount;
  },

  noticeText(useCustomerDiscount = {}, amount) {
    return useCustomerDiscount && '需至少选择' + (Math.abs(amount) / 100).toFixed(2) + '元，才能使用储值专享折扣（' + useCustomerDiscount.discount / 10 + '折）。';
  }
};
