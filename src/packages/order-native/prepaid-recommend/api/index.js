const app = getApp();
const http = app.request;
// 签约开卡
function signAgreement(data) {
  return http({
    path: '/prepaid/recommend/api/card/giftcard/recharge/agreement/sign.json',
    method: 'POST',
    data
  });
}

// 储值预下单
function getPreOrder({ ...data }) {
  return http({
    path: '/prepaid/recommend/api/card/giftcard/recharge/weapp/pre/order.json',
    method: 'POST',
    data
  });
}

// 查询充值的状态
function queryOrderStatus(data) {
  return app.request({
    path: '/prepaid/recommend/api/card/recommend/recharge/status.json',
    method: 'GET',
    data
  });
}

// 查询规则礼包详情
function queryGiftPacks(data) {
  return app.request({
    path: '/prepaid/recommend/api/card/recharge/weapp/giftpacks.json',
    method: 'GET',
    data
  });
}

export { signAgreement, getPreOrder, queryOrderStatus, queryGiftPacks };
