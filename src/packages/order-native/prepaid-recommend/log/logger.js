import { loggerEvent } from './logger-init';

const component = 'prepaid_recommend';

// 显示储值卡支付组件
export function loggerPrepaidCard(params = {}) {
  loggerEvent('view', 'prepaid_card_show', '储值卡组件曝光', {
    ...params,
    component
  });
}

// 显示储值推荐
export function loggerPrepaidRecommend(params = {}) {
  loggerEvent('view', 'prepaid_recommend_show', '储值推荐曝光', {
    ...params,
    component
  });
}

// 下单进入推荐
export function loggerEnterPrepaid(params = {}) {
  loggerEvent('click', 'enter_prepaid', '储值推荐-下单进入推荐', {
    ...params,
    component
  });
}

// 主动关闭收银台
export function loggerClosePayList(params = {}) {
  loggerEvent('click', 'close_pay_list', '储值推荐-主动关闭收银台', {
    ...params,
    component
  });
}

// 储值推荐-充值入口
export function loggerOnRecharge(params = {}) {
  loggerEvent('click', 'on_recharge', '储值推荐-充值入口', {
    ...params,
    component
  });
}

// 储值推荐-返回
export function loggerGoBack(params = {}) {
  loggerEvent('click', 'go_back', '储值推荐-充值返回', {
    ...params,
    component
  });
}

// 储值推荐-关闭
export function loggerOnClose(params = {}) {
  loggerEvent('click', 'on_close', '储值推荐-充值关闭', {
    ...params,
    component
  });
}

// 储值推荐-开始充值
export function loggerStartPay(params = {}) {
  loggerEvent('click', 'start_pay', '储值推荐-开始充值', {
    ...params,
    component
  });
}

// 储值推荐-储值推荐不一致
export function loggerNoPackId(params = {}) {
  loggerEvent('click', 'desc_no', '储值推荐不一致', {
    ...params,
    component
  });
}

// 储值推荐-充值成功
export function loggerPaySuccess(params = {}) {
  loggerEvent('custom', 'cz_pay_success', '储值推荐-充值成功', {
    ...params,
    component
  });
}

// 储值推荐-充值失败
export function loggerPayFail(params = {}) {
  loggerEvent('custom', 'cz_pay_fail', '储值推荐-充值失败', {
    ...params,
    component
  });
}
