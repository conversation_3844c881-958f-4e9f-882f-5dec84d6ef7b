const app = getApp();
const logger = app.logger;

/**
 * 事件埋点
 * @param et string 事件类型， 页面，点击，曝光，自定义，规则
 * @param ei string 事件标识
 * @param en string 事件名称
 * @param params object 参数
 */
export function loggerEvent(et = 'click', ei = '', en = '', params = {}) {
  try {
    // tslint:disable-next-line:no-unused-expression
    logger
      && logger.log({
        et,
        ei,
        en,
        params
      });
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error(
      'logger 错误',
      `et = ${et}`,
      `ei = ${ei}`,
      `en = ${en}`,
      'params = ',
      params
    );
  }
}
