.prepaid-recommend {
  // height: calc(80vh - 44px);
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
  border-radius: 20px 20px 0 0;
}
.recommend-content-swipper {
  overflow: hidden;
}
.recommend-content {
  // width: 100vw;
  flex: 1;
  display: flex;
  transition: transform 0.3s cubic-bezier(0.84, 0.84, 0.32, 0.32);
}
.content-translateX0 {
  transform: translateX(0);
}
.content-translateX100 {
  transform: translateX(-100%);
}
.prepay-card {
  background: #f2f2f2;
  padding: 0 0 60px;
  box-sizing: border-box;
  // position: relative;
  display: flex;
  flex-direction: column;
  &__pop-header {
    font-size: 16px;
    line-height: 44px;
    text-align: center;
    background-color: #fff;
    position: relative;
    font-weight: 500;
  }
  &__pop-left {
    position: absolute !important;
    top: 12px;
    left: 0;
    padding: 0 12px;
    color: #969799;
    font-size: 22px !important;
    line-height: 44px;
    text-align: center;
  }
  &__pop-close {
    position: absolute !important;
    top: 12px;
    right: 0;
    padding: 0 12px;
    color: #969799;
    font-size: 22px !important;
    line-height: 44px;
    text-align: center;
  }
}
