<van-popup
  custom-class="prepaid-recommend"
  show="{{ show }}"
  round
  position="bottom"
  close-on-click-overlay="{{ false }}"
  bind:close="onClose"
>
  <!-- 标题 -->
  <view class="prepay-card__pop-header">
    <van-icon
      wx:if="{{activeIndex === 1 && !toRecharge }}"
      name="arrow-left"
      size="12px"
      custom-class="prepay-card__pop-left"
      bind:tap="onLeft"
    />
    {{activeIndex === 0 ? '请选择余额或卡' : '请选择礼包'}}
    <van-icon wx:if="{{ activeIndex === 0 || toRecharge }}" name="cross" size="12px" custom-class="prepay-card__pop-close" bind:tap="onClose" />
  </view>
  <view class="recommend-content-swipper">
    <view
      class="recommend-content {{ activeIndex === 0 ? 'content-translateX0' : 'content-translateX100' }}"
    >
      <!-- 储值列表 -->
      <prepay-balance
        class="prepay-balance-content"
        bind:onRecharge="onRecharge"
        show="{{show}}"
        amount="{{amount}}"
        product="{{product}}"
        value-card-list="{{valueCard}}"
        prepay-card-total-amount="{{prepayCardTotalAmount}}"
        hasExclusionCard="{{hasExclusionCard}}"
        recommend-detaid="{{recommendDetaid}}"
        pay-asset-activity-tag-desc="{{payAssetActivityTagDesc}}"
        new-recommend="{{newRecommend}}"
        use-customer-discount="{{ useCustomerDiscount }}"
        bind:onConfirm="onClose"
        bind:onRefreshData="onRefreshData"
        that="{{that}}"
      ></prepay-balance>
      <!-- 充值组件 -->
      <gift-pack
        class="gift-pack-content"
        id="prepaid-recommend-gift-pack"
        bind:onClose="onClose"
        value-card="{{valueCard}}"
        recommend-detaid="{{recommendDetaid}}"
        product="{{product}}"
        bind:startCashierPay="startCashierPay"
      ></gift-pack>
    </view>
  </view>
</van-popup>
<van-dialog id="van-dialog" />

<zan-pay bind:init="initPayService" bind:paysuccess="onPaySuccess" bind:cashierclose="onCashierClose" />
