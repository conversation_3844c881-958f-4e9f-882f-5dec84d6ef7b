## prepaid-recommend

`prepaid-recommend`是一个提供预付卡支付和储值推荐的组件

### 使用场景

下单页支付时选择储值卡支付

### 代码演示

:::demo 基础用法

```js
<prepaid-recommend
  show="{{show}}"
  value-card="{{valueCard}}"
  pay-asset-activity-tag-desc="{{payAssetActivityTagDesc}}"
  has-exclusion-card="{{hasExclusionCard}}"
  recommend-detaid="{{recommendDetaid}}"
  bind:onCheck="onCheck"
  bind:onConfirm="onConfirm"
  bind:onClose="onClose"
  bind:onPaySuccess="onPaySuccess"
></prepaid-recommend>
```

:::

## API

| 参数                        | 说明               | 类型    | 默认值                                | 备选值 |
| --------------------------- | ------------------ | ------- | ------------------------------------- | ------ |
| show                        | 组件是否显示       | boolean | false                                 |        |
| valuec-card                 | 储值卡数据         | object  | {disabled: [], list: [], checked: []} |        |
| has-exclusion-card          | 是否有互斥卡       | boolean | false                                 |        |
| pay-asset-activity-tag-desc | 是显示储值推荐     | string  | ''                                    |        |
| recommend-detaid            | 储值推荐的详情信息 | string  |                                       |        |

## Event

| 参数 | 说明 | 参数
| onCheck | 储值卡选中事件 | {card: {}, cardType: "valueCard" }|
| onConfirm | 选中卡提交事件 | true || false 互斥卡是否有变动 |
| onClose | 储值组件关闭 | |
| onRefreshData | 储值卡变化后下单页需要刷新事件 | |
