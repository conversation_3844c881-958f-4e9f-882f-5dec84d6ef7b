<!-- 顶部搜索栏 -->
<van-search
  placeholder="搜索城市"
  show-action="{{ isSearching }}"
  shape="round"
  bind:focus="onFocus"
  bind:search="onSearch"
  bind:cancel="onCancel"
/>

<!-- 搜索列表 -->
<scroll-view
  wx:if="{{ isSearching }}"
  scroll-y
  class="search__list"
>
  <view wx:if="{{ isEmpty }}" class="search__empty">
    暂无相关城市
  </view>
  <van-cell
    wx:for="{{ searchList }}"
    wx:key="{{ item }}"
    title="{{ item.name }}"
    data-value="{{ item.name }}"
    bind:click="onSelectCity"
  />
</scroll-view>

<scroll-view
  scroll-y
  scroll-into-view="{{ toView }}"
  style="height: {{ windowHeight - 54 }}px;"
>
  <!-- GPS定位 -->
  <view class="location" data-value="{{ city }}" bindtap="onSelectCity">
    <view wx:if="{{ locateStatus < 0 }}" class="location__error">当前城市定位失败</view>
    <block wx:else>
      <van-icon name="location" class="location__icon" />
      <view wx:if="{{ locateStatus > 0 }}" class="location__city">
        {{ city }}
        <view class="location__desc">
          (当前定位城市)
        </view>
      </view>
      <view wx:else class="location__city">定位中...</view>
    </block>
  </view>

  <!-- 热门城市 -->
  <view class="title" id="remen">国内热门城市</view>
  <view class="popular">
    <view
      class="popular__row"
      wx:for="{{ popularCity.length / 3 }}"
      wx:for-item="row"
      wx:key="{{ row }}"
    >
      <view
        class="popular__col"
        wx:for="{{ 3 }}"
        wx:for-item="col"
        wx:key="{{ col }}"
        data-value="{{ popularCity[row * 3 + col] }}"
        bindtap="onSelectCity"
      >{{ popularCity[row * 3 + col] }}
      </view>
    </view>
  </view>

  <view class="title">所有城市</view>
  <!-- 城市列表 -->
  <van-cell
    wx:for="{{ cityList }}"
    wx:key="{{ index }}"
    title="{{ item.name }}"
    data-value="{{ item.name }}"
    bind:click="onSelectCity"
  ></van-cell>
</scroll-view>

<van-toast id="van-toast" />
