$alphabetWidth: 15px;

:host {
  background-color: #fff;
}

.title {
  height: 30px;
  display: flex;
  align-items: center;
  background: #f7f8fa;
  font-size: 12px;
  color: #969799;
  padding: 0 15px;
}

/* 搜索列表 */
.search {
  &__list {
    position: fixed;
    top: 54px;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    background-color: #fff;
  }

  &__empty {
    font-size: 14px;
    color: #7d7e80;
    text-align: center;
    line-height: 18px;
    padding-top: 30px;
  }
}

/* GPS定位 */
.location {
  display: flex;
  padding: 0 15px;
  align-items: center;
  height: 70px;

  &__icon {
    height: 16px;
    font-size: 16px;
    color: #323233;
    line-height: 24px;
  }

  &__city {
    margin-left: 5px;
    color: #323233;
    font-weight: bold;
    font-size: 24px;
  }

  &__desc {
    font-size: 12px;
    color: #969799;
    display: inline-block;
    font-weight: normal;
  }

  &__error {
    font-size: 14px;
    color: #969799;
  }
}

/* 热门城市 */
.popular {
  padding: 20px $alphabetWidth 20px 15px;

  &__row {
    display: flex;
    margin-right: -10px;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__col {
    margin-right: 10px;
    font-size: 14px;
    line-height: 40px;
    color: #333;
    text-align: center;
    flex: 1;
    background: #f7f8fa;
    border-radius: 4px;
  }
}

/* 城市列表 */
.list {
  &__title {
    padding: 0 15px;
    font-size: 14px;
    line-height: 26px;
    color: #7d7e80;
    background: #f7f8fa;
  }
}
