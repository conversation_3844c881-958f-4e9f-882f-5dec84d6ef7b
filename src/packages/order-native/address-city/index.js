import WscPage from 'pages/common/wsc-page';
import { reverseGeocoder } from 'shared/utils/lbs/index';
import { fetchCityList } from '@/packages/order-native/utils/request';
import config from '@/packages/order-native/common/config';
import Toast from '@vant/weapp/toast/toast';

WscPage({
  data: {
    city: '',
    locateStatus: 0,
    popularCity: [
      '上海市',
      '北京市',
      '广州市',
      '成都市',
      '苏州市',
      '深圳市',
      '南京市',
      '天津市',
      '重庆市',
      '厦门市',
      '武汉市',
      '西安市'
    ],
    cityList: [],
    searchList: [],
    isSearching: false,
    toView: ''
  },

  onLoad() {
    this.resetPosition();
    this.fetchCity();
    this.initStyle();
  },

  // 初始化样式
  initStyle() {
    wx.getSystemInfo({
      success: res => this.setYZData({
        windowHeight: res.windowHeight
      })
    });
  },

  // 获取城市列表
  fetchCity() {
    Toast.loading();
    fetchCityList().then(cityList => {
      Toast.clear();
      this.setYZData({ cityList });
    }).catch(() => {
      Toast.fail('加载列表失败');
    });
  },

  tryLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: resolve,
        fail: reject
      });
    });
  },

  // 定位
  resetPosition() {
    this.tryLocation()
      .then(location => reverseGeocoder({ location }))
      .catch(err => {
        this.setYZData({ locateStatus: -1 });
        throw err;
      })
      .then(({ result } = {}) => {
        this.setYZData({
          locateStatus: 1,
          city: result.ad_info.city
        });
      });
  },

  // 开始搜索
  onFocus() {
    this.setYZData({ isSearching: true });
  },

  // 取消搜索
  onCancel() {
    this.setYZData({
      searchList: [],
      isSearching: false,
      isEmpty: false
    });
  },

  // 搜索城市
  onSearch(event) {
    const keyword = event.detail;
    const searchList = this.data.cityList
      .filter(city => city.name.indexOf(keyword) > -1);
    this.setYZData({
      searchList,
      isSearching: true,
      isEmpty: searchList.length === 0
    });
  },

  // 选择城市
  onSelectCity(event) {
    const { value } = event.currentTarget.dataset;
    this.trigger(config.eventKey.addressCity, value);
    wx.navigateBack();
  }
});
