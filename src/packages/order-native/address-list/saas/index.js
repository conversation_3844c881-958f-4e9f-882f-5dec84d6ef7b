import config from '@/packages/order-native/common/config';
import { getYunSdk } from '@youzan/weapp-ecloud-sdk';
import EcloudSpaceBiz from '../ecloud-space';

export function initSdk(vm) {
  const sdk = getYunSdk();
  if (sdk) {
    sdk.setPageProcess('showGetWechatAddress', vm.showGetWechatAddress);
    // 载入定制逻辑
    try {
      EcloudSpaceBiz && EcloudSpaceBiz(sdk);
    } catch (e) {
      //
    }
  }

  //  测试
  // setTimeout(function () {
  //   const showGetWechatAddress = sdk.page.getProcess('showGetWechatAddress');
  //   showGetWechatAddress(false);
  // }, 5000);
}

const app = getApp();
export default {
  onLoad({ scene }) {
    const sdk = app.getYouZanYunSdk();

    app.once(config.eventKey.addressSelect, (address) => {
      sdk.app.trigger('ecloud:address:select', [address, scene]);
    });
  },
};
