import { VanxPage } from 'pages/common/wsc-page/index';

import args from '@youzan/weapp-utils/lib/args';
import navigate from '@/helpers/navigate';
import { checkRetailShop } from '@youzan/utils-shop';
import config from '@/packages/order-native/common/config';
import Toast from '@vant/weapp/dist/toast/toast';
import {
  PAGE_TYPE,
  navigateToRantaPage,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import {
  fetchAddressList,
  saveAddress,
  getShopConfigs,
  queryTradeConfig,
} from '@/packages/order-native/utils/request';
import { stringifyAddress } from 'utils/stringify-address';
import SaasConfig, { initSdk } from './saas';
import { logClickAddress, logEditAddress, logKeepAddress } from './log';
import { themeStyleTemplate } from './theme';

const app = getApp();
const extendPageConfig = ECLOUD_MODE ? SaasConfig : {};

VanxPage(extendPageConfig, {
  data: {
    list: [],
    id: '',
    switchable: false,
    finish: false,
    theme: themeStyleTemplate,
    houseNumberRequired: false,
    showGetWechatAddress: true,
    _rantaEdit: false,
  },

  onLoad({ dbid, scene, id, switchable } = {}) {
    // 打开该页面的场景标识，定制场景在用
    scene && this.setYZData({ scene });
    const dbData = app.db.get(dbid) || {};
    const { forcePoiSelect, list } = dbData;
    const idValue = id !== undefined ? +id : dbData.id || '';
    const switchableValue =
      switchable !== undefined ? switchable : dbData.switchable || false;
    this.setYZData({
      id: idValue,
      switchable: switchableValue,
      forcePoiSelect,
    });

    if (Array.isArray(list)) {
      this.setYZData({
        list: list.map((item) => ({
          ...item,
          name: item.userName,
          address: stringifyAddress(item),
        })),
      });
    } else {
      this.getAddressList();
    }

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: switchable ? '选择收货地址' : '收货地址',
    });

    // 删除地址
    this.on(config.eventKey.addressDelete, (id) => {
      this.setYZData({
        list: (this.data.list || []).filter((item) => item.id !== id),
      });
    });

    // 编辑 / 添加地址
    this.on(config.eventKey.addressSave, (newAddress) => {
      const { list = [] } = this.data;
      const editingIndex = list.findIndex((item) => item.id === newAddress.id);
      newAddress.name = newAddress.userName;
      newAddress.address = stringifyAddress(newAddress);
      if (editingIndex === -1) {
        // 添加地址
        list.push(newAddress);
      } else {
        // 编辑地址
        list.splice(editingIndex, 1, newAddress);
      }

      if (newAddress.isDefault) {
        list.forEach((item) => {
          item.isDefault = newAddress.id === item.id ? 1 : 0;
        });
      }

      this.setYZData({ list, id: newAddress.id });
    });

    getShopConfigs(['house_number_required']).then((shopConfigs) => {
      const { house_number_required: houseNumberRequired } = shopConfigs;
      const configs = {};
      if (+houseNumberRequired) configs.houseNumberRequired = true;
      this.setYZData(configs);
    });
    queryTradeConfig('local_delivery_position').then((res) => {
      this._forcePoiSelect = res.config === '1';
    });

    initSdk(this);
  },

  onShow() {
    if (this._rantaEdit) {
      // 中台化的地址编辑页不会执行app.trigger(config.eventKey.addressSave, newAddress);
      // 所以特殊处理下
      this.getAddressList();
      this._rantaEdit = false;
    }
  },

  getAddressList() {
    Toast.loading();
    fetchAddressList()
      .then((res) => {
        const list = res.map((item) => ({
          ...item,
          name: item.userName,
          address: stringifyAddress(item),
        }));
        Toast.clear();
        this.setYZData({ list, finish: true });
      })
      .catch(() => {
        Toast.clear();
        Toast.fail('获取地址列表失败');
      });
  },

  // 选择收货地址
  onSelect(event) {
    if (!this.data.switchable) {
      return;
    }
    app.trigger(config.eventKey.addressSelect, event.detail);
    setTimeout(() => {
      wx.navigateBack();
    }, 300);
  },

  // 编辑收货地址
  onEdit(event) {
    const id = event.detail == null ? null : event.detail.id;
    const { list, switchable, forcePoiSelect, houseNumberRequired } = this.data;
    const isRetail = checkRetailShop(app.getShopInfoSync());
    const dbid = app.db.set({
      id,
      list,
      // 零售场景默认为 true
      forcePoiSelect: isRetail
        ? this._forcePoiSelect || forcePoiSelect
        : forcePoiSelect,
      delta: switchable ? 2 : 1,
    });
    id == null ? logClickAddress() : logEditAddress({ id });

    if (app.ecloudMode) {
      navigate.navigate({
        url: `/packages/order-native/address-edit/index?dbid=${dbid}&scene=${this.data.scene}`,
      });
    } else {
      let query = {
        dbid,
        scene: this.data.scene,
      };
      if (isRetail) {
        query = {
          ...query,
          isShowRetailDeliveryAddress: true,
          houseNumberRequired,
        }
      }
      const url = args.add('/packages/order-native/address-edit/index', query);
      this._rantaEdit = true;
      navigateToRantaPage({ url, pageType: PAGE_TYPE.ADDRESS_EDIT });
    }
  },

  // 保存微信地址
  onWechatSave(event) {
    if (this.isSaving) {
      return;
    }
    this.isSaving = true;

    const address = event.detail;
    Toast.loading();
    saveAddress(address)
      .then((response) => {
        Toast.clear();
        this.isSaving = false;

        const id = address.id || response.value;
        const newAddress = { ...address, id };
        app.trigger(config.eventKey.addressSave, newAddress);
        app.trigger(config.eventKey.addressSelect, newAddress);

        logKeepAddress(newAddress);

        if (this.data.switchable) {
          wx.navigateBack();
        }
      })
      .catch(() => {
        Toast.clear();
        this.isSaving = false;
      });
  },

  showGetWechatAddress(show) {
    this.setYZData({ showGetWechatAddress: show });
  },
});
