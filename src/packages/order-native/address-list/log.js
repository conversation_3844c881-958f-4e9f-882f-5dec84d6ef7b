const app = getApp();

export function logEditAddress(address) {
  app.logger.log({
    et: 'click', // 事件类型
    ei: 'edit_address', // 事件标识
    en: '编辑地址', // 事件名称
    params: address, // 事件参数
    si: app.getKdtId()
  });
}

export function logClickAddress() {
  app.logger.log({
    et: 'click', // 事件类型
    ei: 'click_address', // 事件标识
    en: '新增地址', // 事件名称
    si: app.getKdtId()
  });
}

export function logKeepAddress(address) {
  app.logger.log({
    et: 'custom', // 事件类型
    ei: 'keep_address', // 事件标识
    en: '保存并使用', // 事件名称
    params: address, // 事件参数
    si: app.getKdtId()
  });
}
