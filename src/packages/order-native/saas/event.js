import { AsyncEvent, SyncEvent } from '@youzan/weapp-ecloud-sdk';

// 同步事件
const onOrderLoadedEvent = new SyncEvent();
// 同步事件
const onExpressChangedEvent = new SyncEvent();
// 异步事件
const beforeCreateOrderEvent = new AsyncEvent();
// 同步事件
const afterCreateOrderEvent = new SyncEvent();
// 异步事件
const onPayItemClickEvent = new AsyncEvent();
// 同步事件
const afterOrderPaidEvent = new AsyncEvent();
// 异步事件
const beforeConfirmEvent = new AsyncEvent();

export {
  onOrderLoadedEvent,
  onExpressChangedEvent,
  beforeCreateOrderEvent,
  afterCreateOrderEvent,
  onPayItemClickEvent,
  afterOrderPaidEvent,
  beforeConfirmEvent
};
