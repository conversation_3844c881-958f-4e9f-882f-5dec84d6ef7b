import pick from '@youzan/weapp-utils/lib/pick';
import get from '@youzan/weapp-utils/lib/get';
import payCaculate from './payment-caculate';
import { moment as formatDate } from 'utils/time';

// openState类似于getter的处理，由于bridge需要监听的原因，在业务内部不做getters处理
const openState = {
  // 有赞云对外开放的扩展字段
  cloudOrderExt(state) {
    return state.cloudOrderExt;
  },

  // 下单页 准备下单的数据
  prepare(state) {
    const addressList =
      state.address.list &&
      state.address.list.map((addr) => {
        const address = pick(addr, [
          'id',
          'addressDetail',
          'areaCode',
          'city',
          'community',
          'country',
          'county',
          'lat',
          'lon',
          'province',
          'userName',
          'tel',
        ]);
        return address;
      });

    const contactList =
      (state.contact.list &&
        state.contact.list.map((cont) => ({
          id: cont.id,
          tel: cont.telephone,
          userName: cont.userName,
        }))) ||
      [];
    return {
      addressList,
      contactList,
    };
  },

  order(state, getters) {
    const { orderNos = [], source = {}, pay = {} } = state.order;
    const { prompt = '' } = state.display;

    return {
      orderCreated: !!(Array.isArray(orderNos) && orderNos.length),
      orderNo: orderNos[0] || state.order.orderNo || '',
      bookKey: source.bookKey,
      showPayResult: pay.showPayResult,
      disableSubmit: !!(
        (state.display && state.display.forbidPay) ||
        !getters.hasGoods
      ),
      prompt,
    };
  },

  buyerMsg(state) {
    return state.order.buyerMsg;
  },


  // 发票
  invoice(state) {
    return state.invoice;
  },

  // 物流数据
  delivery(state, getters) {
    const expressType = getters.expressType;
    const { postage: { currentExpressType } = {} } = state;
    let address = {};
    let selfFetch = {};
    let selectedAddressId = 0;
    if (expressType === 'express') {
      const _address = getters.currentAddress;
      selectedAddressId = _address.id;
      address = pick(_address, [
        'addressDetail',
        'areaCode',
        'city',
        'community',
        'country',
        'county',
        'lat',
        'lon',
        'province',
        'userName',
        'tel',
      ]);
      address.recipients = address.userName;
      delete address.userName;
    } else {
      let shop = state.selfFetch.shop || {};
      shop = pick(shop, [
        'addressDetail',
        'area',
        'city',
        'county',
        'lat',
        'lng',
        'name',
        'province',
        'tel',
      ]);
      let contact = getters.currentContact;
      contact = {
        tel: contact.telephone,
        recipients: contact.userName,
      };
      selfFetch = {
        shop,
        contact,
        time: getters.selfFetchTime,
      };
    }

    // 可以慢慢下掉，使用prepare.addressList
    const addressList =
      state.address.list &&
      state.address.list.map((addr) => {
        const address = pick(addr, [
          'id',
          'addressDetail',
          'areaCode',
          'city',
          'community',
          'country',
          'county',
          'lat',
          'lon',
          'province',
          'userName',
          'tel',
        ]);
        if (!selectedAddressId && addr.isDefault) {
          selectedAddressId = addr.id;
        }
        return address;
      });
    return {
      expressType,
      address,
      selfFetch,
      addressList,
      selectedAddressId,
      expressTypeChoice: currentExpressType,
    };
  },

  // 配送方式
  experssWays(state) {
    const { selfFetch, postage } = state;
    const result = [];

    if (selfFetch.isAllow) {
      result.push({ name: 'self-fetch' });
    }

    if (postage.postageItems && postage.postageItems.length > 0) {
      const { postageItems, currentExpressType } = postage;
      const options = [];
      // expressType 0 为 快递
      // 2 为 同城送
      postageItems.forEach((item) => {
        options.push({
          ...item,
          active: item.expressType === currentExpressType,
        });
      });
      result.push({
        name: 'express',
        options,
      });
    }
    return result;
  },

  // 订单金额数据
  payment(state, getters) {
    return payCaculate(state, getters);
  },

  // 订单的商品列表
  goodsList(state) {
    const { list = [] } = state.goods || {};
    return list.map((goods) => {
      const goodsMap = pick(goods, [
        'title',
        'imgUrl',
        'alias',
        'num',
        'payPrice',
        'goodsId',
        'skuId',
        'weight',
        'pointsPrice',
        'goodsType',
        'activityType',
        'activityId',
        'finalTotalPrice',
        'message',
        'pointDeduction',
      ]);
      goodsMap.originPrice = goods.price;
      goodsMap.commodityTax = goods.goodsTax * goods.num || 0;
      goodsMap.freightTax = goods.postageTax || 0;
      return goodsMap;
    });
  },

  unavailableGoodsList(state) {
    return state.goods.unavailable.map((item) => ({
      title: item.title,
      imgUrl: item.imgUrl,
      // alias
      num: item.num,
      payPrice: item.payPrice,
      originPrice: item.price,
      pointsPrice: item.pointsPrice,
      goodsId: item.goodsId,
      skuId: item.skuId,
      goodsType: item.goodsType,
      unavailableDesc: item.unavailableDesc,
    }));
  },

  // 储值卡，礼品卡，储值余额 选中情况
  payCardGroup(state) {
    const usePayAsset = {};
    const { valueCard = {} } = state;
    const { list } = valueCard;
    usePayAsset.valueCardNos = [];
    usePayAsset.valueBalanceNos = [];

    // 储值卡
    if (valueCard.checked && valueCard.checked.length) {
      valueCard.checked.forEach((item) => {
        const cardItem = list.find((element) => element.summaryCardNo === item);
        // 说明是储值余额
        if (cardItem.cardType === 102 && cardItem.cardSubType === 1001) {
          usePayAsset.valueBalanceNos.push(item);
        } else {
          // 说明是储值卡
          usePayAsset.valueCardNos.push(item);
        }
      });
    }

    return usePayAsset;
  },

  // 优惠券选中情况
  coupon(state, getters) {
    const { coupon = {} } = state;
    const couponData = {};
    couponData.id = coupon.externalId || getters.chosenCoupon.id;
    couponData.couponType = getters.chosenCoupon.type;
    couponData.outerCoupon = coupon.externalId ? 1 : 0; // 是否是第三方优惠券
    return couponData;
  },

  // 额外参数
  extra(state) {
    return pick(state.extra || {}, ['pointDeduction']);
  },

  // 会员卡
  customerCards(state) {
    const { list = [] } = state.customerCards || {};
    return list.map((customerCard) => {
      const card = pick(customerCard, ['id', 'name']);
      return card;
    });
  },

  selectedCustomerCard(state) {
    const { list = [], id } = state.customerCards || {};
    const selectedCard = list.find((card) => card.id === id);
    return pick(selectedCard, ['id', 'name']);
  },

  // 余额/礼品卡
  prePayCard(state, getters) {
    const prePayCardData = {};
    const { valueCard } = state || {};
    const { list = [], disabled = [], checked = [] } = valueCard;
    const { hasExclusionCard = false } = getters;
    prePayCardData.list = list; // 卡列表
    prePayCardData.disabled = disabled; // 不可用的卡列表
    prePayCardData.checked = checked; // 选中的卡号列表
    prePayCardData.isExistDiscount = hasExclusionCard; // 是否存在其他优惠
    return prePayCardData;
  },

  preSalePhaseInfo(state, getters) {
    const { isDepositPresale } = getters;
    if (!isDepositPresale) return {};
    const { pay = {}, goods = {} } = state;
    const { phasePayment = {} } = pay;
    const { phaseItems = [], currentPhase } = phasePayment;
    const items = phaseItems.map((item) => {
      return {
        phase: item.phase,
        phaseDesc: item.phase === 1 ? '定金' : '尾款',
        status: item.status,
        payUmpDiscountMoney: item.payUmpDiscountMoney,
        currentPrice: item.currentPrice,
        valuePrice: item.valuePrice,
        price: item.price,
      };
    });

    const { list = [] } = goods;
    const preSaleItem =
      list.find((item) => {
        return item.presale;
      }) || {};
    const {
      longPreSaleTailPayTimeStr,
      presaleBalanceDueStartTime,
      presaleBalanceDueEndTime,
    } = preSaleItem;

    return {
      currentPhase,
      phaseItems: items,
      payTimeLabel:
        longPreSaleTailPayTimeStr ||
        `尾款支付时间${formatDate(
          presaleBalanceDueStartTime,
          'YYYY-MM-DD HH:mm:ss'
        )}-${formatDate(presaleBalanceDueEndTime, 'YYYY-MM-DD HH:mm:ss')}`,
      longPreSaleTailPayTimeStr,
      presaleBalanceDueStartTime,
      presaleBalanceDueEndTime,
    };
  },

  useBeforePayData(state) {
    const { useBeforePayData = {} } = state;
    return {
      ...useBeforePayData,
    }
  },

  // 随心购数据
  enjoyBuyOrderInfo(state) {
    const extensions = get(state, 'order.extensions', {}) || {};
    const enjoyBuyOrderData = JSON.parse(
      extensions.ENJOY_BUY_ORDER_EXTRA || '{}'
    );
    return pick(enjoyBuyOrderData, [
      'deliveryMode',
      'customDeliveryMode',
      'deliveryCycle',
      'deliveryCycleCustomStartDate',
      'deliveryCycleCustomEndDate',
      'deliveryIssueTotal',
      'deliveryAmount',
      'deliveryStartDate',
      'deliveryTimeSection',
      'deliveryLeadTime',
      'deliveryCycleModifyMaxLimit',
    ]);
  },
};

export { openState };
