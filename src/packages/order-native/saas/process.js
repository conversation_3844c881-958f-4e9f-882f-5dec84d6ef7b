import BillService from '../biz-service/bill-service';
import PayService from '../biz-service/pay-service';
import payCaculate from './payment-caculate';
import { getPlatform } from '../utils/format';

let store;
let billService;
/** @type {PayService} */
let payService;
let payServiceListeners;

export function initCashier(listeners) {
  if (payService) {
    payService.cloudInit(listeners);
  } else {
    payServiceListeners = listeners;
  }
}

export function paySuccess() {
  if (payService) {
    payService.successTrigger();
  }
}

export function destroyPayService() {
  payService = undefined;
}

function initContext(_store) {
  store = _store;

  billService = new BillService(store);

  const app = getApp();
  const metadata = {
    kdt_id: app.getKdtId(),
    buyer_id: app.getBuyerId(),
    account: app.getMobile(),
  };
  payService = new PayService({
    // biz放在init里面赋值，因为店铺类型的判断是异步的
    // biz: 'trade_order',
    toast: wx.showToast,
    clear: wx.hideToast,
    request: app.request,
    account: metadata.account,
    metadata,
    ...store,
    state: store.state,
  });

  if (payServiceListeners) {
    payService.cloudInit(payServiceListeners);
    payServiceListeners = undefined;
  }
}

// 设置有赞云开放的扩展字段
function setCloudOrderExt(...args) {
  store.commit('SET_CLOUD_ORDER_EXT', ...args);
}

/**
 * 创建订单
 */
function createOrder() {
  const payment = payCaculate(store.state, store.getters);
  return billService.createOrder().then((orderResult = {}) => {
    return {
      orderNo: orderResult.orderNo || '',
      payment,
    };
  });
}

/**
 * 获取支付方式
 */
export function getPayWays() {
  return payService.getPayWays().then(({ payWays }) => {
    return {
      payWays: payWays
        .filter((itm) => itm.available)
        .map((payWay) => {
          return {
            payChannel: payWay.payChannel,
            payChannelName: payWay.payChannelName,
          };
        }),
    };
  });
}

/**
 * 支付
 */
function startPay({ payChannel = '' }) {
  return payService.callPay({ payChannel });
}

/**
 * 支付后支持跳转页面
 */
function goToPayResult(params) {
  return store.dispatch('GO_TO_PAY_SUCCESS', params).then((path) => {
    if (path && typeof path === 'string') {
      wx.redirectTo({ url: path });
    }
  });
}

/**
 * 刷新页面数据
 */
function fetchShow() {
  const orderNos = store.state?.order?.orderNos || [];
  const orderCreated = !!(Array.isArray(orderNos) && orderNos.length);
  if (orderCreated) {
    return store.dispatch('FETCH_SHOW_PAY', {
      order_no: orderNos,
      orderMark: getPlatform().orderMark,
    });
  }
  return store.dispatch('FETCH_SHOW', {
    fromIsv: true,
  });
}

/**
 * 获取买家自提信息
 */
function fetchSelfFetch() {
  return store.dispatch('INIT_SELF_FETCH');
}

/**
 * 获取自提列表
 */
function fetchSelfFetchList() {
  return store.dispatch('FETCH_SHOP_LIST', { getAll: true });
}

/**
 * 设置默认城市
 */
function setSelfFetchCity(city) {
  // { cityName, cityCode }
  return store.commit('SET_SELF_FETCH_CITY', city);
}

/**
 * 切换配送方式
 */
function switchExpress({ expressType }) {
  store.commit('SET_EXPRESS_WAP_TAB', expressType === 'express' ? 0 : 1);
}

// 设置联系人
function setContact({ id, userName, tel }) {
  const contact = {
    userName,
    telephone: tel,
  };
  return Promise.resolve(
    id
      ? store.commit('SET_CONTACT_OPTION', { id, ...contact })
      : store.dispatch('SAVE_CONTACT', contact)
  );
}

/**
 * 1. 获取购物车中最早加购商品的导购openId
 * 2. 把该导购的fromParams暂存在state上, 供后续方法用于覆盖下单接口参数
 * @returns { Promise<Number> }
 */
function getEarliestGuideOpenId() {
  return billService.getEarliestGuideOpenId();
}

/**
 * 把下单接口参数中所有商品的fromParams字段改为指定字段
 * @returns { void }
 */
function setAllGoodsGuideParams(openId) {
  return billService.setAllGoodsGuideParams(openId);
}

// 商家配送情况下切换配送方式
function setExpressTypeChoice({ expressType }) {
  store.commit('SET_EXPRESS_TYPE_CHOICE', expressType);
}

/**
 * 设置提货时间
 * @param {*} param0
 */
function setSelfFetchTime({ startTime, endTime, timeText }) {
  if (
    !isNaN(new Date(startTime.replace(/-/g, '/'))) &&
    !isNaN(new Date(endTime.replace(/-/g, '/')))
  ) {
    store.commit('SET_SELF_FETCH_TIME', {
      startTime,
      endTime,
      text: timeText || startTime,
    });
    return true;
  }
  return false;
}

/**
 * 根据bookKey 重新生成订单
 */
function fetchShowByBookKey({ bookKey = '' }) {
  return store.dispatch('FETCH_SHOW_BY_BOOK_KEY', { bookKey });
}

// 根据会员卡id选中，id为空字符串时表示取消
function selectCustomerCard(id) {
  const { state, getters } = store;
  const prevId = state.customerCards.id;

  if (getters.orderCreated || prevId === id) {
    return;
  }

  store.commit('SET_CUSTOM_CARD', id);

  // NOTE: 如果有展示卡，强制不选中展示卡
  if (id && getters.hasDisplayCard) {
    store.commit('SET_DISPLAY_CARD', false);
  }

  store.dispatch('FETCH_SHOW', { setCustomerCard: true }).catch(() => {
    store.commit('SET_CUSTOM_CARD', prevId);
  });
}

// 选中余额卡或礼品卡
function updateCheckedPrePayCards({ cards = [] }) {
  const { state } = store;
  const { valueCard } = state || {};
  const checkedList = cards.map((card) => card.summaryCardNo);
  valueCard.checked = checkedList;

  const giftCard = {
    checked: [],
    disabled: [],
    list: [],
  };
  store.commit('SET_PREPAY_CARD_CHECK_STATUS', { valueCard, giftCard });
}

/**
 * 判断当前收货人姓名是否和身份证姓名一致
 */
function checkDeliveryUserName() {
  const { state, getters } = store;
  const { expressType, currentAddress = {}, currentContact = {} } = getters;
  const { idcard = {} } = state;
  let deliveryUserName = '';
  if (expressType === 'express') {
    deliveryUserName = currentAddress.userName;
  } else {
    deliveryUserName = currentContact.userName;
  }
  const idcardName = idcard.name;
  if (!deliveryUserName && !idcardName) {
    return false;
  }
  return deliveryUserName === idcardName;
}

/**
 * 定金协议是否隐藏
 */
function depositAgreementShow(show = true) {
  store.commit('SET_DEPOSIT_AGREEMENT', show);
}

/**
 * 不使用优惠券
 */
function closeCoupon() {
  store.commit('SET_CHOSEN_COUPON_ID', 0);
}

/**
 * 设置同城配送时间
 */
function setLocalDeliveryTime(deliveryTime) {
  const { state } = store;
  const { hasNeedStockUpGoods } = state.tradeTag;

  store.commit('SET_LOCAL_DELIVERY_TIME', deliveryTime);

  if (hasNeedStockUpGoods) {
    store.dispatch('FETCH_SHOW', {
      expressChanged: true,
      changeDeliveryTime: true, // 只有备货商品 修改配送时间时才会传此参数
    });
  }
}
/**
 * 修改买家留言
 */
function modifyMessage(text = '') {
  store.commit('SET_MODIFY_MESSAGE', text);
}

/**
 * 优惠券组件是否隐藏
 */
function setCouponDisplay(display = true) {
  store.commit('SET_COUPON_DISPLAY', display);
}

/**
 * 跳转优惠券列表页
 */
function navigateCouponPage() {
  store.dispatch('NAVIGATE_COUPON_LIST');
}

/**
 * 唤起储值卡弹窗
 */
function openValueCard() {
  store.dispatch('OPEN_VALUE_CARD');
}

/**
 * 隐藏买家留言
 */
function showMessage(display) {
  store.commit('SET_HIDDEN_MESSAGE', display);
}

// 设置积分抵现数量
export function setPointDeductionCost(event) {
  store.commit('SET_POINT_DEDUCTION_COST', event);
}
// 设置是否使用积分
export function usePointDeduction(event) {
  store.commit('USE_POINT_DEDUCTION', event);
}

export default function injectProcesses({ sdk, store, billManager }) {
  initContext(store);

  sdk.setPageProcess('saveAddress', billManager.saveAddress.bind(billManager));
  sdk.setPageProcess(
    'selectAddress',
    billManager.selectAddress.bind(billManager)
  );
  sdk.setPageProcess(
    'selectSelfFetchAddress',
    billManager.selectSelfFetchAddress.bind(billManager)
  );
  sdk.setPageProcess('createOrder', createOrder);
  sdk.setPageProcess('getPayWays', getPayWays);
  sdk.setPageProcess('startPay', startPay);
  sdk.setPageProcess('setCloudOrderExt', setCloudOrderExt);
  // 更新页面数据
  sdk.setPageProcess('fetchShow', fetchShow);
  sdk.setPageProcess('fetchSelfFetch', fetchSelfFetch);
  // 切换配送方式
  sdk.setPageProcess('switchExpress', switchExpress);
  sdk.setPageProcess('setContact', setContact);
  sdk.setPageProcess('getEarliestGuideOpenId', getEarliestGuideOpenId);
  sdk.setPageProcess('setAllGoodsGuideParams', setAllGoodsGuideParams);
  sdk.setPageProcess('setSelfFetchTime', setSelfFetchTime);
  sdk.setPageProcess('fetchShowByBookKey', fetchShowByBookKey);
  // 设置积分抵现数量
  sdk.setPageProcess('setPointDeductionCost', setPointDeductionCost);
  // 设置是否使用积分
  sdk.setPageProcess('usePointDeduction', usePointDeduction);
  // 获取自提列表
  sdk.setPageProcess('getSelfFetchList', fetchSelfFetchList);
  // 切换商家配送方式
  sdk.setPageProcess('setExpressTypeChoice', setExpressTypeChoice);
  // 设置默认自提城市
  sdk.setPageProcess('setSelfFetchCity', setSelfFetchCity);
  // 选择会员卡
  sdk.setPageProcess('selectCustomerCard', selectCustomerCard);
  // 选中余额卡或礼品卡
  sdk.setPageProcess('updateCheckedPrePayCards', updateCheckedPrePayCards);

  // 跳转至对应的支付成功页
  sdk.setPageProcess('goToPayResult', goToPayResult);

  // 校验当前收货人姓名是否和身份证姓名一致
  sdk.setPageProcess('checkDeliveryUserName', checkDeliveryUserName);

  // 定金不退协议是否隐藏
  sdk.setPageProcess('showDepositAgreement', depositAgreementShow);

  // 不使用优惠券
  sdk.setPageProcess('closeCoupon', closeCoupon);

  // 设置同城配送时间
  sdk.setPageProcess('setLocalDeliveryTime', setLocalDeliveryTime);

  // 修改买家留言
  sdk.setPageProcess('modifyMessage', modifyMessage);

  // 隐藏优惠券组件
  sdk.setPageProcess('setCouponDisplay', setCouponDisplay);

  // 跳转优惠券列表页
  sdk.setPageProcess('navigateCouponPage', navigateCouponPage);

  // 唤起储值卡弹窗
  sdk.setPageProcess('openValueCard', openValueCard);

  // 隐藏买家留言
  sdk.setPageProcess('showMessage', showMessage);
}
