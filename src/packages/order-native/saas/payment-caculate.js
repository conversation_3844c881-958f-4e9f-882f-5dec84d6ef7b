export default function payCaculate(state, getters) {
  const {
    pay = {},
    pointDeduction,
    order: { newCouponProcess },
    fansBenefit = {},
    displayCard = {},
  } = state;
  const { costPoints, isUse, rate } = pointDeduction || {};
  const {
    realPay /* 已经包含邮费，积分抵现，改价等 */ = 0,
    postage = 0,
    itemPay = 0,
    promotions = [],
    originPostage = 0, // 订单级邮费
    extraFees = [], // 附加费
  } = pay;
  const { 
    couponDecrease = 0, 
    prepayCardDecrease = 0, 
    orderCreated, 
    totalPrice = {},
  } = getters;

  const { taxPrice, payUmpDiscountMoney } = totalPrice;

  /* newCouponProcess 是 积分抵现和优惠券抵扣顺序互换项目新流程，若是新流程，优惠券的抵扣金额已在后端进行处理，在realPay中已经减过了，这边不需要再减一遍 */
  let leftAmount = 0;
  if (orderCreated) {
    /** 如果已成单，直接返回realPay */
    leftAmount = realPay;
  } else {
    leftAmount = newCouponProcess ? realPay : realPay - couponDecrease;
  }
  // 统一处理储值抵扣的情况
  leftAmount -= prepayCardDecrease;
  if (leftAmount < 0) {
    leftAmount = 0;
  }

  let points = 0;
  let pointsDeductAmount = 0;

  // 是否使用了积分
  if (isUse) {
    points = costPoints || 0;
    pointsDeductAmount = Math.round(points / rate) * 100;
  }

  return {
    goodsPay: itemPay /* 商品金额 */,
    realPay: leftAmount /* 应付/实付金额，扣减各种优惠后 */,
    postage /* 邮费 */,
    points /* 积分 */,
    pointsDeductAmount /* 积分抵扣金额 */,
    couponDecrease /* 优惠券/码抵扣金额 */,
    prepayCardDecrease /* 储值卡/礼品卡抵扣金额 */,
    promotions /* 优惠 */,
    originPostage /* 订单级邮费 */,
    taxPrice /* 进口税 */, 
    extraFees /* 附加费 */,
    fansBenefit /* 粉丝优惠 */,
    payUmpDiscountMoney /* 支付优惠 */,
    displayCard,
  };
}
