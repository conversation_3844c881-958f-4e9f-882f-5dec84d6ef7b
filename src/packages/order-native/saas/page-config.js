import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { getYunSdk } from '@youzan/weapp-ecloud-sdk';
import { openState } from './state';
import store from '@/packages/order-native/store/index';
import BillManager from '@/packages/order-native/biz-service/bill-manager';
import {
  onOrderLoadedEvent,
  onExpressChangedEvent,
  beforeCreateOrderEvent,
  afterCreateOrderEvent,
  onPayItemClickEvent,
  afterOrderPaidEvent,
  beforeConfirmEvent,
} from './event';
import injectProcesses from './process';
import { initUpdateComponent, updateComponent } from './update-component';
import EcloudSpaceBiz from '../ecloud-space';

export default {
  ...YunPageConfig,
  onLoad() {
    const billManager = new BillManager(store(), this);
    const sdk = getYunSdk();
    this.__yunPage = sdk.page;

    // //////////////yunsdk初始//////////////////
    // 注册开放数据
    sdk.setPageData(this, openState);

    // 注册开放事件
    sdk.setPageEvent('onOrderLoaded', onOrderLoadedEvent);
    sdk.setPageEvent('onExpressChanged', onExpressChangedEvent);
    sdk.setPageEvent('beforeCreateOrderAsync', beforeCreateOrderEvent);
    sdk.setPageEvent('afterCreateOrder', afterCreateOrderEvent);
    sdk.setPageEvent('onPayItemClickAsync', onPayItemClickEvent);
    sdk.setPageEvent('afterOrderPaid', afterOrderPaidEvent);
    sdk.setPageEvent('beforeConfirm', beforeConfirmEvent);

    // confirm之前事件
    this.onAsync('beforeConfirm', (args) => {
      return beforeConfirmEvent.trigger(args);
    });

    // 触发订单已加载事件
    this.on('ecloud:order:loaded', () => {
      // console.log('on ecloud:order:loaded');
      onOrderLoadedEvent.trigger();
    });

    // 标准页面的事件-创建订单前
    this.onAsync('beforeCreateOrder', (args) => {
      // console.log('on native beforeCreateOrder');
      return beforeCreateOrderEvent.trigger(args).catch((e) => {
        // 电商云订阅者执行错误
        // TODO
        throw e;
      });
    });
    // 标准页面的事件-创建订单后
    this.onAsync('afterCreateOrder', (args) => {
      // console.log('on native afterCreateOrder');
      return afterCreateOrderEvent.trigger(args);
    });
    // 标准页面的事件-点击支付方式时
    this.onAsync('onPayItemClick', (args) => {
      // console.log('on native onPayItemClick');
      return onPayItemClickEvent.trigger(args).catch((e) => {
        // 电商云订阅者执行错误
        // TODO
        throw e;
      });
    });
    // 标准页面的事件-支付成功后
    this.onAsync('afterOrderPaid', (args) => {
      // console.log('on native afterOrderPaid', args);
      return afterOrderPaidEvent.trigger(args);
    });

    /* afterOrderPaidEvent.on('afterOrderPaidEvent', args => {
      console.log('in custom afterOrderPaidEvent', args);
    }); */

    // 注册开放流程
    injectProcesses({ sdk, store: this.$store, billManager });

    // 注册组件更新接口
    initUpdateComponent(this.$store);
    sdk.page.updateComponent = updateComponent.bind(this);

    // //////////////yunsdk 使用 demo//////////////////
    /* getYunSdk().page.events.beforeCreateOrderAsync.on('beforeCreateOrderAsync', args => {
      console.log('beforeCreateOrderAsync: ', args);
    }); */

    // 载入定制逻辑
    try {
      EcloudSpaceBiz && EcloudSpaceBiz(sdk);
    } catch (e) {
      //
    }
  },

  onUnload() {
    // console.log('unload...');
    this.off('ecloud:order:loaded');
    this.offAsync('beforeCreateOrder');
    this.offAsync('afterCreateOrder');
    this.offAsync('onPayItemClick');
    this.__yunPage && this.__yunPage.unload();
  },
};
