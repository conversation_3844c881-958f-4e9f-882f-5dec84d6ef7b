let commit;
let state;

const CAN_UPDATE_COMPONENTS_MAP = {
  GoodsInfo: 'SET_CLOUD_COMP_GOODS',
  UmpInfo: 'SET_CLOUD_COMP_UMP',
  AddressBlock: 'SET_CLOUD_COMP_ADDRESS',
  CountdownBlock: 'SET_CLOUD_COMP_COUNTDOWN',
  CouponBlock: 'SET_COUPON_COMP_COUPON',
  ValueCardBlock: 'SET_COUPON_COMP_VALUE_CARD',
};

export function initUpdateComponent({ commit: _commit, state: _state }) {
  commit = _commit;
  state = _state;
}

/**
 * 更新组件默认行为
 * @param {*} compName 组件名称
 * @param {*} display 是否隐藏组件
 * @param {*} properties 新属性
 */
export function updateComponent(compName, display, properties) {
  // 不使用优惠券
  if (compName === 'CouponBlock') {
    commit('SET_CHOSEN_COUPON_ID', 0);
  }
  // 不使用储值卡
  if (compName === 'ValueCardBlock') {
    const { valueCard } = state;
    valueCard.checked = [];
    commit('SET_PREPAY_CARD_CHECK_STATUS', { valueCard });
  }
  if (CAN_UPDATE_COMPONENTS_MAP[compName]) {
    commit(CAN_UPDATE_COMPONENTS_MAP[compName], { display, properties });
  }
}
