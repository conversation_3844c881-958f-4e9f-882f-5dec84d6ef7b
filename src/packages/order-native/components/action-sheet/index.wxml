  <van-popup
    show="{{ show }}"
    round
    safe-area-inset-bottom
    position="bottom"
    class="action-sheet"
    class="{{ popupClass }}"
    bind:close="onClose"
  >
    <view class="action-sheet__title">
      <text wx:if="{{ title }}">{{ title }}</text>
      <slot wx:else name="title" />

      <van-icon
        wx:if="{{ showClose }}"
        name="cross"
        class="action-sheet__close"
        bind:click="onClose"
      />
    </view>

    <view
      class="action-sheet__body {{ scroll ? 'action-sheet__body--scroll' : '' }}"
      style="background-color: {{ bodyBackgroundColor }}; {{ bodyStyle }}"
    >
      <slot />
    </view>

    <view wx:if="{{ buttonText || useBottomSlot }}" class="action-sheet__bottom">
      <slot name="bottom" wx:if="{{ useBottomSlot }}"></slot>
      <van-button
        wx:else
        round
        type="danger"
        size="large"
        custom-class="action-sheet__button"
        bind:click="onConfirm"
      >
        {{ buttonText }}
      </van-button>
    </view>
  </van-popup>