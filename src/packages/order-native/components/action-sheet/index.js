import { VanxComponent } from 'pages/common/wsc-component/index';

VanxComponent({
  properties: {
    show: Boolean,
    title: String,
    showClose: Boolean,
    buttonText: String,
    bodyBackgroundColor: {
      type: String,
      value: '#f7f8fa',
    },
    popupClass: String,
    bodyStyle: String,
    scroll: {
      type: Boolean,
      value: true,
    },
    closeOnConfirm: {
      type: Boolean,
      value: true,
    },
    useBottomSlot: Boolean,
  },

  options: {
    multipleSlots: true,
  },

  methods: {
    onClose() {
      this.triggerEvent('close');
    },

    onConfirm() {
      this.triggerEvent('confirm');

      if (this.data.closeOnConfirm) {
        this.onClose();
      }
    },
  },
});
