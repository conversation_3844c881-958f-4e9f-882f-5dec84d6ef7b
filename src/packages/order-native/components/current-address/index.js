import { VanxComponent } from 'pages/common/wsc-component/index';
import { tryLocation, reverseGeocoder } from 'shared/utils/lbs/index';
import authorize from 'shared/utils/authorize';

VanxComponent({
  options: {
    pureDataPattern: /^_/,
  },
  properties: {
    customClass: {
      type: String,
      value: '',
    },
  },
  data: {
    location: '',
    area: '',
    _detail: {},
  },
  attached() {
    authorize('scope.userLocation')
      .then(
        () =>
          new Promise((resolve, reject) =>
            tryLocation((_, location) => resolve(location), reject)
          )
      )
      .then(this.getCurrentLocation)
      .then(({ message, result }) => {
        if (message === 'query ok') {
          const {
            address_component: { province, city, district },
            formatted_addresses: { recommend },
          } = result;
          this.setYZData({
            location: recommend,
            area: `${province}/${city}/${district}`,
            _detail: result,
          });
        }
      })
      .catch(() => {}); // 拦截异常，不展示当前定位地址
  },
  methods: {
    getCurrentLocation({ latitude, longitude }) {
      return reverseGeocoder({
        location: {
          latitude,
          longitude,
        },
      });
    },
    onClick() {
      this.triggerEvent('save', this.data._detail);
    },
  },
});
