.van-coupon-list {
  position: relative;
  height: 100%;
  background-color: #f7f8fa;

  &__field {
    margin: 5px 0 5px 16px;
    background: #f7f8fa;
    border-radius: 17px;
    flex: 1;

    .van-field__clear {
      margin-right: 0;
    }
  }

  &__input {
    height: 34px;
    line-height: 34px;
  }

  &__exchange-bar {
    display: flex;
    align-items: center;
    background-color: #fff;
  }

  &__exchange {
    font-size: 16px !important;
    height: 32px !important;
    line-height: 30px !important;
    border: 0 !important;
  }

  &__list {
    box-sizing: border-box;
    padding-top: 16px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;

    &--with-bottom {
      padding-bottom: 16px + 50px;
    }
  }

  &__bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 999;
    box-sizing: border-box;
    width: 100%;
    padding: 5px 16px;
    font-weight: 500;
    background-color: #fff;
    padding-bottom: env(safe-area-inset-bottom);
  }

  &__close {
    height: 40px;
    line-height: 38px;
  }

  &__empty {
    padding: 0;
    overflow: hidden;
    text-align: center;

    &-text {
      margin: 16px 0;
      color: #969799;
      font-size: 14px;
      line-height: 20px;
    }

    &-img {
      width: 200px;
      height: 200px;
    }
  }
}
