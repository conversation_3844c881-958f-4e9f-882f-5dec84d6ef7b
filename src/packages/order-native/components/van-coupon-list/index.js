import { VanxComponent } from 'pages/common/wsc-component';

VanxComponent({
  externalClasses: ['custom-class', 'list-class'],

  options: {
    addGlobalClass: true
  },

  properties: {
    code: {
      type: String,
      observer(val) {
        this.currentCode = val;
        this.setIsValid();
      }
    },
    closeButtonText: String,
    inputPlaceholder: {
      type: String,
      value: '请输入优惠码'
    },
    enabledTitle: {
      type: String,
      value: '可用'
    },
    disabledTitle: {
      type: String,
      value: '不可用'
    },
    exchangeButtonText: {
      type: String,
      value: '兑换'
    },
    exchangeButtonLoading: Boolean,
    exchangeButtonDisabled: Boolean,
    exchangeMinLength: {
      type: Number,
      value: 1
    },
    chosenCoupon: {
      type: Number,
      value: -1
    },
    coupons: {
      type: Array,
      value: []
    },
    disabledCoupons: {
      type: Array,
      value: []
    },
    displayedCouponIndex: {
      type: Number,
      value: -1
    },
    showExchangeBar: {
      type: Boolean,
      value: true
    },
    showCloseButton: {
      type: Boolean,
      value: true
    },
    showCount: {
      type: Boolean,
      value: true
    },
    currency: {
      type: String,
      value: '¥'
    },
    emptyImage: {
      type: String,
      value: 'https://img01.yzcdn.cn/vant/coupon-empty.png'
    }
  },

  methods: {
    inInputCode({ detail } = {}) {
      this.currentCode = detail;
      this.setIsValid();
    },

    setIsValid() {
      const { currentCode } = this;
      this.setYZData({ isValid: currentCode && currentCode.length >= this.data.exchangeMinLength });
    },

    onClickExchangeButton() {
      this.triggerEvent('exchange', this.currentCode);

      // auto clear currentCode when not use vModel
      if (!this.code) {
        this.currentCode = '';
      }
    },

    onChange(event) {
      this.triggerEvent('change', event.currentTarget.dataset.index);
    },

    setLine() {
      this.selectComponent('#van-tabs').setLine();
    }
  }
});
