<view class="van-coupon-list custom-class">
  <view class="van-coupon-list__exchange-bar">
    <view class="van-coupon-list__field">
      <van-field
        value="{{ code }}"
        clearable
        border="{{ false }}"
        placeholder-style="color: #c8c9cc !important;"
        custom-style="padding: 0 12px; background-color: transparent;"
        input-class="van-coupon-list__input"
        placeholder="{{ inputPlaceholder }}"
        maxlength="{{ 20 }}"
        bind:input="inInputCode"
      />
    </view>
    <van-button
      plain
      type="danger"
      custom-class="van-coupon-list__exchange"
      loading="{{ exchangeButtonLoading }}"
      disabled="{{ !exchangeButtonLoading && (exchangeButtonDisabled || !isValid) }}"
      bind:click="onClickExchangeButton"
    >{{ exchangeButtonText }}</van-button>
  </view>

  <van-tabs id="van-tabs" custom-class="van-coupon-list__tab" border="{{ false }}" nav-class="van-coupon-list__nav">
    <van-tab title="{{ enabledTitle }}{{ showCount ? ' (' + coupons.length + ')' : '' }}">
      <view class="list-class van-coupon-list__list {{ showCloseButton ? 'van-coupon-list__list--with-bottom' : '' }}">
        <coupon
          wx:for="{{ coupons }}"
          wx:key="id"
          coupon="{{ item }}"
          currency="{{ currency }}"
          chosen="{{ index === chosenCoupon }}"
          data-index="{{ index }}"
          bindtap="onChange"
        />

        <view wx:if="{{ !coupons.length }}" class="van-coupon-list__empty">
          <image class="van-coupon-list__empty-img" mode="aspectFit" src="{{ emptyImage }}" />
          <view class="van-coupon-list__empty-text">暂无优惠券</view>
        </view>
      </view>
    </van-tab>

    <van-tab title="{{ disabledTitle }}">
      <view class="list-class van-coupon-list__list {{ showCloseButton ? 'van-coupon-list__list--with-bottom' : '' }}">
        <coupon
          wx:for="{{ disabledCoupons }}"
          wx:key="id"
          disabled
          coupon="{{ item }}"
          currency="{{ currency }}"
        />

        <view wx:if="{{ !disabledCoupons.length }}" class="van-coupon-list__empty">
          <image class="van-coupon-list__empty-img" mode="aspectFit" src="{{ emptyImage }}" />
          <view class="van-coupon-list__empty-text">暂无优惠券</view>
        </view>
        </view>
    </van-tab>
  </van-tabs>

  <view wx:if="{{ showCloseButton }}" class="van-coupon-list__bottom">
    <van-button
      round
      type="danger"
      block
      class="van-coupon-list__close"
      text="{{ closeButtonText || '不使用优惠券' }}"
      data-index="{{ -1 }}"
      bind:click="onChange"
    />
  </view>
</view>