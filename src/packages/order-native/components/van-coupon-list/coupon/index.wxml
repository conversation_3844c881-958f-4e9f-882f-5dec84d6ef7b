<wxs src="./index.wxs" module="computed" />

<view class="van-coupon {{ disabled ? 'van-coupon--disabled' : '' }}">
  <view class="van-coupon__content">
    <view class="van-coupon__head">
      <view class="van-coupon__amount">
        {{ coupon.valueDesc }}
        <view class="van-coupon__amount-unit">{{ coupon.unitDesc || '' }}</view>
      </view>
      <view class="van-coupon__condition">
        {{ coupon.condition }}
      </view>
    </view>
    <view class="van-coupon__body">
      <view class="van-coupon__name">{{ coupon.name }}</view>
      <view class="van-coupon__valid valid-style">{{ computed.validPeriod(coupon) }}</view>
        <van-checkbox
          wx:if="{{ !disabled }}"
          size="{{ 18 }}"
          value="{{ chosen }}"
          class="van-coupon__corner"
        />
    </view>
  </view>
  <view wx:if="{{ disabled && coupon.reason || !disabled && coupon.description }}" class="van-coupon__dash"></view>
  <view wx:if="{{ disabled && coupon.reason }}" class="van-coupon__description">{{ coupon.reason }}</view>
  <view wx:if="{{ !disabled && coupon.description }}" class="van-coupon__description">{{ coupon.description }}</view>
</view>