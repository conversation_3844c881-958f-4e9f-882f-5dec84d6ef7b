.van-coupon {
  margin: 0 12px 12px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 8px;
  position: relative;

  &:active {
    background-color: #f2f3f5;
  }

  &__content {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    min-height: 84px;
    padding: 14px 0;
    color: #323233;
  }

  &__head {
    position: relative;
    min-width: 96px;
    padding: 0 8px;
    color: #ee0a24;
    text-align: center;
  }

  &__amount,
  &__condition,
  &__name,
  &__valid {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &__amount {
    margin-bottom: 6px;
    font-weight: 500;
    font-size: 30px;

    &-unit {
      display: inline-block;
      font-weight: normal;
      font-size: 40%;

      &:not(:empty) {
        margin-left: 2px;
      }
    }
  }

  &__condition {
    font-size: 12px;
    line-height: 16px;
    white-space: pre-wrap;
  }

  &__body {
    position: relative;
    flex: 1;
    border-radius: 0 8px 8px 0;
    .valid-style {
      padding-right: 40px;
      white-space: pre-wrap;
    }
  }

  &__name {
    margin-bottom: 10px;
    font-weight: bold;
    font-size: 14px;
    line-height: 20px;
  }

  &__valid {
    font-size: 12px;
  }

  &__corner {
    position: absolute;
    top: 0;
    right: 16px;
    bottom: 0;
    display: flex;
    align-items: center;
  }

  &__dash {
    border-top: 1px dashed #ebedf0;
    position: absolute;
    left: 12px;
    right: 12px;
    height: 0;
  }

  &__description {
    padding: 8px 16px;
    font-size: 12px;
  }

  &--disabled {
    &:active {
      background-color: #fff;
    }

    .van-coupon-item__content {
      height: 84px - 10px;
    }

    .van-coupon__head {
      color: inherit;
    }
  }
}
