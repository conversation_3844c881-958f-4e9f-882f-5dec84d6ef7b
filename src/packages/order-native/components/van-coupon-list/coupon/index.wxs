/* eslint-disable */
function padZero(num) {
  num = '00' + num;
  return num.slice(-2);
}

function formatDate(timeStamp) {
  var date = getDate(timeStamp * 1000);
  return date.getFullYear() + '.' + padZero(date.getMonth() + 1) + '.' + padZero(date.getDate());
}

function validPeriod(coupon) {
  if (!coupon) {
    return '';
  }
  if (coupon.extraInfo.validPeriodCopywriting) {
    return coupon.extraInfo.validPeriodCopywriting;
  }
  return formatDate(coupon.startAt) + ' - ' + formatDate(coupon.endAt);
}

module.exports = {
  validPeriod: validPeriod
};
