.address-item {
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;

  --radio-label-margin: 12px;
  --radio-checked-icon-color: var(--theme-general, #ee0a24);

  &__cell {
    padding: 0 !important;
  }

  &__value {
    padding-right: 44px;
    text-align: left !important;
    color: #323233 !important;
  }

  &__address {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
  }

  &__inactive {
    color: #c8c9cc;
  }

  &__tag {
    margin-right: 8px;
    height: 18px;
    min-width: 32px;
    box-sizing: border-box;
    position: relative;
    top: -1px;
    justify-content: center;
  }

  &__name {
    font-size: 14px;
    line-height: 18px;
    color: #969799;
  }

  &--disabled {
    .address-item__name,
    .address-item__address {
      color: #c8c9cc;
    }
  }

  &__edit {
    position: absolute !important;
    padding: 10px 0 10px 10px;
    top: 50%;
    right: 0;
    color: #969799;
    font-size: 20px !important;
    transform: translate(0, -50%);
  }

  &__tip {
    font-size: 12px;
    color: #ed6a0c;
    line-height: 16px;
    display: flex;
    align-items: center;
    margin: 8px 15px 0 0;
    position: relative;
    justify-content: space-between;

    &--switchable {
      margin-left: 30px;
    }

    &__icon {
      height: 1em;
    }
  }
}
