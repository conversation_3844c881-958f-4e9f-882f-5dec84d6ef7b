<wxs src="./index.wxs" module="computed" />

<view class="address-item" bindtap="onSelect" >
  <van-cell border="{{ false }}" custom-class="address-item__cell" value-class="address-item__value" i18n-off>
    <van-radio wx:if="{{ switchable }}" name="{{ item.id }}" value="{{ value }}" disabled="{{inactive}}">
      <view class="address-item__address {{inactive ? 'address-item__inactive' : ''}}">
        {{ item.address }}
      </view>
      <view class="address-item__name">
        <van-tag 
          wx:if="{{ item.isDefault }}"
          class="address-item__tag-root"
          custom-class="address-item__tag"
          color="var(--main-bg, #f2f2ff)"
          text-color="#fff"
          round
        >默认</van-tag>
        <van-tag 
          wx:if="{{ item.label }}"
          custom-class="address-item__tag"
          color="var(--tag-bg, #f2f2ff)"
          text-color="var(--tag-text, #323233)"
          round
        >{{ item.label }}</van-tag>
        {{ item.name }} {{ item.tel }}
      </view>
    </van-radio>
    <view wx:else>
      <view class="address-item__address {{inactive ? 'address-item__inactive' : ''}}">
        {{ item.address }}
      </view>
      <view class="address-item__name">
        <van-tag 
          wx:if="{{ item.isDefault }}"
          size="medium"
          custom-class="address-item__tag"
          color="var(--main-bg, #f2f2ff)"
          text-color="#fff"
          round
        >默认</van-tag>
        <van-tag 
          wx:if="{{ item.label }}"
          custom-class="address-item__tag"
          color="var(--tag-bg, #f2f2ff)"
          text-color="var(--tag-text, #323233)"
          round
        >{{ item.label }}</van-tag>
        {{ item.name }} {{ item.tel }}
      </view>
    </view>

    <van-icon
      slot="right-icon"
      name="edit"
      custom-class="address-item__edit"
      bind:click="onEdit"
      catchtap="noop"
    />
  </van-cell>

  <view
    wx:if="{{ computed.showPoiPrompt(forcePoiSelect, item) }}"
    class="address-item__tip {{ switchable ? 'address-item__tip--switchable' : ''  }}"
    catchtap="onEdit"
  >
    为提高同城配送准确性，请进行地图定位选点
    <van-icon class="address-item__tip__icon" name="arrow" />
  </view>
</view>