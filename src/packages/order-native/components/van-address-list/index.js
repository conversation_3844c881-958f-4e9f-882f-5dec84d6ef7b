import { VanxComponent } from 'pages/common/wsc-component/index';

VanxComponent({
  options: {
    multipleSlots: true,
  },

  properties: {
    list: Array,
    inactiveList: {
      type: Array,
      value: [],
    },
    switchable: {
      type: Boolean,
      value: true,
    },
    value: null,
  },

  methods: {
    onEdit(event) {
      this.triggerEvent('edit', event.detail);
    },

    onSelect(event) {
      this.triggerEvent('select', event.detail);
    },

    onAdd() {
      this.triggerEvent('add');
    },
  },
});
