<view class="address-list">
  <slot name="top"></slot>

  <item
    wx:for="{{ list }}"
    wx:key="index"
    item="{{ item }}"
    value="{{ value }}"
    switchable="{{ switchable }}"
    bind:select="onSelect"
    bind:edit="onEdit"
  />

  <block wx:if="{{inactiveList.length > 0}}">
    <view class="address-inactive-list__info">以下地址超出配送范围</view>
    <item
      wx:for="{{ inactiveList }}"
      wx:key="id"
      inactive="{{true}}"
      item="{{ item }}"
      value="{{ value }}"
      switchable="{{ switchable }}"
      bind:edit="onEdit"
    />
  </block>

  <view class="address-list__bottom">
    <van-button
      round
      block
      type="danger"
      custom-class="address-list__add"
      bind:click="onAdd"
    >+新增地址</van-button>
  </view>
</view>