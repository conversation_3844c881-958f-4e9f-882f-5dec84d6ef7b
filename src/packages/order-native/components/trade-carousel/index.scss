.trade-carousel {
  height: 20px;
  // 比 goods-fixed-info高一点
  z-index: 22;
  overflow: hidden;

  &__swipe {
    height: 20px;
    font-size: 12px;

    &-item {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      height: 20px;
      font-size: 14px;
      color: #323233;
      letter-spacing: 0;
      line-height: 20px;
    }

    &-avatar {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      margin-right: 4px;
    }

    &-text {
      flex-shrink: 0;
      margin-right: 1px;
      font-size: 14px !important;
      font-weight: normal !important;
      line-height: normal;

      // @include ellipsis;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  &_nav-text {
    font-size: 14px;
  }
}

.move-enter-active {
  animation: move-in 0.5s;
}

.move-leave-active {
  animation: move-out 0.3s;
  animation-fill-mode: forwards;
}

@keyframes move-in {
  0% {
    transform: translateY(20px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes move-out {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-20px);
  }
}
