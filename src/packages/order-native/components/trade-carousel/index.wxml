<view
  class="trade-carousel"
  wx:if="{{ show }}"
  style="top: {{ topBarHeight }}px"
>
  <view
    class="trade-carousel__swipe"
    wx:if="{{ tradeRecords.length > 0 }}"
  >
    <block wx:for="{{ tradeRecords }}" wx:for-item="tradeRecord" wx:key="payTime">
      <view
        wx:if="{{ tradeRecord.showEnter || tradeRecord.showLeave }}"
        class="trade-carousel__swipe-item {{ tradeRecord.showEnter ? 'move-enter-active': '' }} {{ tradeRecord.showLeave ? 'move-leave-active': '' }} "
      >
        <image
          wx:if="{{ tradeRecord.avatar }}"
          src="{{ tradeRecord.avatar }}"
          class="trade-carousel__swipe-avatar"
        />
        <text class="trade-carousel__swipe-text">{{ tradeRecord.content }}</text>
      </view>
    </block>
  </view>
  <view wx:if="{{ tradeRecords.length === 0 && naviBarText }}">
    <text class="trade-carousel_nav-text">{{ naviBarText }}</text>
  </view>
</view>
