import { VanxComponent } from 'pages/common/wsc-component/index';
import { timerBehaivor } from '@goods/common/behaviors';
import { mapState } from '@youzan/vanx';
import get from '@youzan/weapp-utils/lib/get';
import store from '@/packages/order-native/store/index';

const app = getApp();

// TODO 存在 async，以及内存泄露
VanxComponent({
  behaviors: [timerBehaivor],
  store,

  data: {
    tradeRecords: [],
  },
  // store,

  properties: {
    customTop: {
      type: Number,
      value: 0,
    },
    show: {
      type: Boolean,
    },
    naviBarText: {
      type: String,
    },
  },

  mapData: mapState({
    abConfigInfo: (state) => get(state, 'shopConfig.abConfigInfo', {}),
    topBarHeight() {
      return this.data.customTop;
    },
  }),

  computed: {
    ...mapState(['tradeRecordsV2']),
  },

  watch: {
    tradeRecordsV2(newVal) {
      this.startAnimation(newVal);
    },
  },

  ready() {
    this.$dispatch('FETCH_TRADE_RECORDS');
    const { abTraceId } = this.data.abConfigInfo || {};
    app.logger && app.logger.log({
      et: 'view', // 事件类型
      ei: 'entertaining_diversions_view', // 事件标识
      en: '下单页跑马灯曝光', // 事件名称
      params: { 
        order_no: '',
        abTraceId,
        component: 'entertaining_diversions',
      } // 事件参数
    });
  },

  detached() {
    clearTimeout(this.tid);
  },

  methods: {
    startAnimation(tradeRecordsV2 = []) {
      const { length: tradeRecordLength } = tradeRecordsV2;

      if (!tradeRecordLength) {
        return;
      }

      const start = (index, tradeRecordLength, isInit) => {
        const nextIndex = index === tradeRecordLength - 1 ? 0 : index + 1;

        // 初始化动画参数，必须每次都清除一下
        const tradeRecords = tradeRecordsV2.map((tradeRecord) => {
          tradeRecord.showEnter = false;
          tradeRecord.showLeave = false;
          return tradeRecord;
        });

        // 进入的时候把动画状态都清除掉；vue会自己做；
        this.setData(
          {
            __isAnimation: true,
            tradeRecords,
          },
          () => {
            this.move(tradeRecords, index, isInit, () => {
              start(nextIndex, tradeRecordLength);
            });
          }
        );
      };

      const startIndex = 0;
      start(startIndex, tradeRecordLength, true);
    },

    move(tradeRecords, index, isInit, next) {
      const INIT_INTERVAL = 1000;
      const ENTRY_INTERVAL = 2000;
      const WAIT_INTERVAL = 2000;
      const LEAVE_INTERVAL = 300;
      const entryInterval = isInit ? INIT_INTERVAL : ENTRY_INTERVAL;

      this.timeoutPromise(
        this.moveIn.bind(this, { tradeRecords, index }),
        entryInterval
      )
        .then(() => {
          return this.timeoutPromise(
            this.moveOut.bind(this, { tradeRecords, index }),
            WAIT_INTERVAL
          );
        })
        .then(() => {
          this.timeoutPromise(next, LEAVE_INTERVAL);
        });
    },

    timeoutPromise(fn, interval) {
      return new Promise((resolve) => {
        this.tid = setTimeout(() => {
          fn();
          resolve();
        }, interval);
      });
    },

    moveIn({ tradeRecords, index }) {
      const tradeRecord = tradeRecords[index];
      tradeRecord.showEnter = true;
      tradeRecord.showLeave = false;
      this.setData({
        __isAnimation: true,
        [`tradeRecords[${index}]`]: tradeRecord,
      });
    },

    moveOut({ tradeRecords, index }) {
      const tradeRecord = tradeRecords[index];
      tradeRecord.showEnter = false;
      tradeRecord.showLeave = true;
      this.setData({
        __isAnimation: true,
        [`tradeRecords[${index}]`]: tradeRecord,
        // tradeRecords: tradeRecordsV2
      });
    },
  },
});
