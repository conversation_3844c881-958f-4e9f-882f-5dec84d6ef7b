<wxs src="./index.wxs" module="utils" />
<view class="rule-rights-detail">
  <!-- 🍀 赠送金 -->
  <view wx:if="{{bonusAmountRights.bonusAmount}}" class="item__right item__bonus">
    送 {{ utils.formatPrice(bonusAmountRights.bonusAmount) }} 元 储值余额
    <view wx:if="{{bonusAmountRights.bonusValidType === 1}}" class="validity">
      <!-- 赠送金有效期 -->
      <br />到账{{ bonusAmountRights.bonusValidPeriod }}个月后失效
    </view>
  </view>

  <!-- 🎫 优惠券 -->
  <block wx:if="{{couponRights.length}}">
    <view
      wx:for="{{couponRights}}"
      wx:for-item="coupon"
      wx:key="couponId"
      class="item__right item__coupon {{utils.getInvalidClass(!!coupon.invalidText)}}"
    >
      送 {{ coupon.count }} 张 {{ coupon.slogan || coupon.couponName }}
      <view wx:if="{{!!coupon.invalidText}}" class="item__stages">
        {{utils.getInvalidText(coupon.invalidText)}}
      </view>
    </view>
  </block>

  <!-- ⌚ 赠品 -->
  <view
    wx:if="{{presentRights.presentName}}"
    class="item__right item__present {{utils.getInvalidClass(!!presentRights.invalidText)}}"
  >
    送 1 份 {{ presentRights.presentName }}
    <view wx:if="{{!!presentRights.invalidText}}" class="item__stages">
      {{utils.getInvalidText(presentRights.invalidText)}}
    </view>
  </view>

  <!-- 📈 积分 -->
  <view wx:if="{{pointRights.point}}" class="item__right item__point">
    送 {{ pointRights.point }} {{ pointRights.customPointName || '积分'}}
  </view>

  <!-- 💳 普通权益卡 -->
  <!-- 💳 储值权益卡 -->
  <block wx:if="{{memberCardRights.length}}">
    <view
      wx:for="{{memberCardRights}}"
      wx:key="cardId"
      wx:for-item="member"
      class="item__right item__member {{utils.getInvalidClass(!!member.invalidText)}}"
    >
      送 1 张 {{ member.cardName }}
      <view wx:if="{{!!member.invalidText}}" class="item__stages">
        {{utils.getInvalidText(member.invalidText)}}
      </view>
    </view>
  </block>

  <!-- 🍀 成长值 -->
  <view wx:if="{{growthPointRights.growthPoint}}" class="item__right item__growth">
    送 {{ growthPointRights.growthPoint }} 成长值
  </view>
</view>
