import WscComponent from 'pages/common/wsc-component';

WscComponent({
  data: {
    bonusAmountRights: {}, // 赠送金
    couponRights: [], // 优惠券
    growthPointRights: {}, // 成长值
    memberCardRights: [], // 权益卡
    pointRights: {}, // 积分
    presentRights: {}, // 赠品
  },
  properties: {
    // 当前礼包
    giftPack: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  attached() {
    const {
      ruleRights: {
        bonusAmountRights = {},
        couponRights = [],
        growthPointRights = {},
        memberCardRights = [],
        pointRights = {},
        presentRights = {},
      },
    } = this.data.giftPack;
    this.setData({
      bonusAmountRights,
      couponRights,
      growthPointRights,
      memberCardRights,
      pointRights,
      presentRights,
    });
  },
});
