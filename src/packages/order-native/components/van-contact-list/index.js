import { VanxComponent } from 'pages/common/wsc-component';

VanxComponent({
  externalClasses: ['bottom-class'],

  properties: {
    list: Array,
    defaultTagText: String,
    addText: {
      type: String,
      value: '新建联系人'
    },
    value: null
  },

  methods: {
    onEdit(event) {
      const item = this.data.list[event.currentTarget.dataset.index];
      this.triggerEvent('edit', item);
    },

    noop() {},

    onSelect(event) {
      const item = this.data.list[event.currentTarget.dataset.index];
      this.triggerEvent('select', item);
    },

    onAdd() {
      this.triggerEvent('add');
    },
  }
});
