.van-contact-list {
  box-sizing: border-box;
  height: 100%;
  padding-bottom: 50px;

  &__item {
    padding: 16px !important;
  }

  &__item-value {
    display: flex;
    align-items: center;
    padding-right: 32px;
    padding-left: 8px;
    color: #323233 !important;
    text-align: left !important;
  }

  &__item-tag {
    flex: none;
    margin-left: 8px;
    font-size: 12px !important;
    padding: 0 4px !important;
    line-height: 16px !important;
  }

  &__item-tag-root {
    display: flex;
  }

  &__group {
    box-sizing: border-box;
    height: 100%;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
  }

  &__edit {
    font-size: 16px !important;
    color: #969799 !important;
  }

  &__bottom {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    padding: 5px 16px;
    background-color: #fff;
  }

  &__add {
    height: 40px;
    line-height: 38px;
  }
}
