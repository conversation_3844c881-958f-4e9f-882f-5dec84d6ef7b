<view class="van-contact-list">
  <view class="van-contact-list__group">
    <van-cell
      wx:for="{{ list }}"
      wx:key="id"
      center
      border="{{ false }}"
      custom-class="van-contact-list__item"
      value-class="van-contact-list__item-value"
      data-index="{{ index }}"
      bind:click="onSelect"
    >
      <van-icon
        slot="icon"
        name="edit"
        custom-class="van-contact-list__edit"
        data-index="{{ index }}"
        bind:click="onEdit"
        catch:tap="noop"
      />

      {{ item.name }}，{{ item.tel }}
      <van-tag
        wx:if="{{ item.isDefault && defaultTagText }}" 
        type="danger" 
        round 
        class="van-contact-list__item-tag-root"
        custom-class="van-contact-list__item-tag"
      >
        {{ defaultTagText }}
      </van-tag>

      <van-radio
        slot="right-icon"
        name="{{ item.id }}"
        value="{{ value }}"
        icon-size="16px"
      />
    </van-cell>
  </view>
  <view class="van-contact-list__bottom bottom-class">
    <van-button
      round
      block
      type="danger"
      custom-class="van-contact-list__add"
      text="{{ addText }}"
      bind:click="onAdd"
    />
  </view>
</view>