import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['tradeTag']),
    ...mapGetters(['orderCreated'])
  },

  data: {
    showConsumersNotice: false,
    showImportsNotice: false
  },

  methods: {
    onClickConsumersNotice() {
      this.setYZData({ showConsumersNotice: true });
    },

    onClickImportNotice() {
      this.setYZData({ showImportsNotice: true });
    },

    onConfirmConsumersNotice() {
      this.setYZData({ showConsumersNotice: false });
    },

    onConfirmImportNotice() {
      this.setYZData({ showImportsNotice: false });
    }
  }
});
