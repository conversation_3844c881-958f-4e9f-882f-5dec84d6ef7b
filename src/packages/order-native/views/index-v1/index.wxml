<!-- 倒计时 -->
<countdown />

<block wx:for="{{ design }}" wx:key="type">
  <!-- 地址 -->
  <address
    wx:if="{{ item.type === 'address' }}"
    theme-border-color="theme-border-color"
    theme-text-color="theme-text-color"
    theme-bg-color="theme-bg-color"
    theme-button="theme-button"
    theme-color="theme-color"
    th_tree-a="th_tree-a"
  />

  <!-- 商品 -->
  <goods
    wx:elif="{{ item.type === 'goods' }}"
    theme-border-color="theme-border-color"
    theme-text-color="theme-text-color"
    theme-bg-color="theme-bg-color"
    theme-button="theme-button"
    theme-color="theme-color"
  />

  <!-- 营销活动 -->
  <ump
    wx:elif="{{ item.type === 'ump' }}"
    theme-border-color="theme-border-color"
    theme-text-color="theme-text-color"
    theme-bg-color="theme-bg-color"
    theme-button="theme-button"
    theme-color="theme-color"
  />

  <!-- 配送等服务 -->
  <service
    wx:elif="{{ item.type === 'service' }}"
    th_btn-p="th_btn-p"
    theme-button-plain="theme-button-plain"
    theme-border-color="theme-border-color"
    theme-text-color="theme-text-color"
    theme-bg-color="theme-bg-color"
    theme-button="theme-button"
    theme-color="theme-color"
    th_tree-a="th_tree-a"
  />

  <!-- 价格 -->
  <price
    wx:elif="{{ item.type === 'price' }}"
    theme-border-color="theme-border-color"
    theme-text-color="theme-text-color"
    theme-bg-color="theme-bg-color"
    theme-button="theme-button"
    theme-color="theme-color"
  />

  <!-- 底部付款条 -->
  <submit
    wx:elif="{{ item.type === 'submit' }}"
    order-pay-class="order-pay-class"
    theme-border-color="theme-border-color"
    theme-bg-color="theme-bg-color"
    theme-button="theme-button"
    theme-color="theme-color"
  />

  <block wx:elif="{{ item.custom }}">
    <template is="{{ item.type }}" />
  </block>
</block>