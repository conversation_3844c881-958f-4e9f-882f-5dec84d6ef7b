import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  externalClasses: [
    'order-pay-class',
    'th_btn-p',
    'theme-button-plain',
    'theme-border-color',
    'theme-text-color',
    'theme-bg-color',
    'theme-button',
    'theme-color',
    'th_tree-a'
  ],

  mapData: {
    ...mapState(['design'])
  }
});
