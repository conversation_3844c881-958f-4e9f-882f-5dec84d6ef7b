import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import { format } from '@/packages/order-native/utils/time/date';

// 周期购日期格式化
function formatWeekDate(date) {
  date = new Date(date);
  return format.date(date, 'yyyy年mm月dd日') + ' （' + format.weekday(date) + '）';
}

VanxComponent({
  store,

  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer(value) {
        if (!value) {
          return;
        }
        const { periodBuy } = this;
        this.setYZData({
          dateList: [
            {
              values: periodBuy.options.map(formatWeekDate),
              defaultIndex: periodBuy.chosenIndex
            }
          ]
        });
      }
    }
  },

  data: {
    dateList: []
  },

  mapData: {
    ...mapState(['periodBuy']),
    ...mapGetters(['expressType'])
  },

  computed: {
    ...mapState(['periodBuy'])
  },

  methods: {
    onClose() {
      this.triggerEvent('close');
    },

    onConfirm(event) {
      const index = event.detail.index[0];
      this.$commit('SET_PERIOD_BUY_CHOSEN_INDEX', index);
      this.onClose();
    }
  }
});
