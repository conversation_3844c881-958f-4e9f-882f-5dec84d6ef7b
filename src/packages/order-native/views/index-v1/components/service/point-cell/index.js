import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['points', 'pointsName']),
    ...mapGetters(['orderCreated']),
  },

  data() {
    return {
      showDetail: false
    };
  },

  methods: {
    onShow() {
      this.setYZData({
        showDetail: true
      });
    },

    onHide() {
      this.setYZData({
        showDetail: false
      });
    },

    onBack() {
      wx.navigateBack();
    }
  }
});
