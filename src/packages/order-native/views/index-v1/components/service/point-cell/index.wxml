<view class="point-cell" wx:if="{{ points.usePoints }}">
  <van-cell
    title="抵用{{ pointsName }}"
    value="{{ points.totalPoints }}{{ pointsName }}"
    is-link="{{ !orderCreated }}"
    bind:click="onShow"
  ></van-cell>

  <van-action-sheet
    show="{{ showDetail }}" 
    bind:close="onHide" 
    title="抵用{{ pointsName }}" 
    position="bottom"
  >
    <van-cell border="{{ false }}">
      <view slot="title">
        {{ pointsName }}余额：
        <text class="theme-color">{{ points.externalPoint || points.userPoints }}</text>
      </view>
    </van-cell>
    <view class="point-cell__popup-button">
      <van-button custom-class="van-button theme-button" size="large" type="danger" bind:click="onHide">确定</van-button>
    </view>
  </van-action-sheet>

  <van-popup
    custom-class="point-cell__unavailable"
    show="{{ !points.available }}"
    close-on-click-overlay="{{ false }}"
  >
    <view>{{ pointsName }}不足，请返回重新下单</view>
    <view class="text">当前余额：{{ points.externalPoint || points.userPoints }}</view>
    <van-button custom-class="van-button theme-button" size="large" type="danger" bind:click="onBack">返回</van-button>
  </van-popup>
</view>
