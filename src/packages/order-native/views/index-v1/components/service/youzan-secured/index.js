import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import { data } from './computed';

// 待支付页

VanxComponent({
  store,

  computed: {
    ...mapState(['order', 'shop', 'goods', 'pay', 'display']),
    ...mapState(data),
  },

  mapData: {
    ...mapState(['order', 'shop', 'goods', 'pay', 'display']),
    ...mapState(data),
  },

  methods: {
    setGuarantee(event) {
      this.$commit('SET_YZ_GUARANTEE', event.detail);
    },
    handleInfoChange({ detail: info }) {
      this.$commit('SET_YZ_GUARANTEE_INFO', info);
    },
    handleDocsChange({ detail: docs }) {
      this.$commit('SET_YZ_GUARANTEE_DOCS', docs);
    },
  },
});
