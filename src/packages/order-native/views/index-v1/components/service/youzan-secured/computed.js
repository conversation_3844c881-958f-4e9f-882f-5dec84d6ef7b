import { get } from '@/packages/order-native/store/utils';

export const data = {
  show() {
    return (this.order.orderNos || []).length === 1;
  },
  createTime() {
    let ct = '';
    try {
      const bizExt = JSON.parse(get(this.pay, 'payParams.bizExt', '{}'));

      if (bizExt.ORDER_CREATE_TIME) {
        ct = Number(bizExt.ORDER_CREATE_TIME);
      }
    } catch (err) {
      ct = 0;
    }
    return ct;
  },
  guaranteeOrderInfoCreated() {
    const { order, goods, display, createTime } = this;
    console.log(
      'alias',
      goods,
      get(goods, 'list', [])
        .filter((res) => !res.present)
        .map((res) => res.alias || '')
    );

    return {
      aliases: get(goods, 'list', [])
        .filter((res) => !res.present)
        .map((res) => res.alias || ''), // 商品 alias
      hasYzSecured: display.yzGuarantee, // 是否是有赞担保订单
      orderNo: order.orderNos[0], // E单号
      orderStatus: 10, // 订单状态
      hasFreightInsurance: display.freightInsurance, // 是否打标退货包运费
      freightInsuranceFree: display.freightInsuranceFree, // 退货包运费赠送标
      orderCreateTime: createTime, // 订单创建时间
    };
  },
};
