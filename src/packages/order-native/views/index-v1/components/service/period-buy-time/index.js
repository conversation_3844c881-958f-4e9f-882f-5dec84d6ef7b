import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['periodBuy']),
    ...mapGetters(['orderCreated', 'periodBuyTimeText', 'expressType'])
  },

  data: {
    showPicker: false
  },

  methods: {
    onClick() {
      this.setYZData({ showPicker: true });
    },

    onClose() {
      this.setYZData({ showPicker: false });
    }
  }
});
