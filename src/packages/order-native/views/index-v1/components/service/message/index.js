import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import { validateOrderMessage } from '@/packages/order-native/common/validator';

VanxComponent({
  store,

  mapData: {
    ...mapState(['order']),
    ...mapGetters(['orderCreated', 'hasGoods'])
  },

  computed: {
    ...mapState(['order'])
  },

  watch: {
    'order.buyerMsg': function () {
      const msg = validateOrderMessage(this.$store.state);
      if (msg) {
        this.$dispatch('TOAST', msg);
      }
    }
  },

  methods: {
    // 设置留言
    handleChange(event) {
      this.$commit('SET_ORDER_MESSAGE', event.detail);
    },

    // 聚焦留言框时打点
    handleFocus() {
      this.$dispatch('LOG_FOCUS_MESSAGE');
    }
  }
});
