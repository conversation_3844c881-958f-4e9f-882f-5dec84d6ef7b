<!-- 可购买数量不为为0的时候显示留言 && !(下了订单，且订单里留言为空时，不显示留言) -->
<van-field
  wx:if="{{ hasGoods && !orderCreated && order.showMessage }}"
  value="{{ order.buyerMsg }}"
  label="买家留言"
  placeholder="留言建议提前协商（250字以内）"
  bind:input="handleChange"
  bind:focus="handleFocus"
  disabled="{{ orderCreated }}"
  border="{{ false }}"
  maxlength="{{ 250 }}"
  title-width="75px"
/>
<van-cell
  wx:elif="{{ hasGoods && order.showMessage }}"
  title="买家留言"
  value="{{ order.buyerMsg || '无' }}"
  border="{{ false }}"
  title-width="75px"
/>
