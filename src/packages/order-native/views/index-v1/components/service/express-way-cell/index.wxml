<!-- 可购买商品不为0 并且非虚拟商品 才要显示配送方式 -->
<van-cell
  wx:if="{{ showExpressWay }}"
  is-link="{{ expressWayEditable }}"
  bind:click="onClickCell"
  title="配送方式"
  title-width="75px"
  value-class="value-class"
  center
>
  <view>
    <text>{{ currentPostage.postageTitle }}</text>
    <text wx:if="{{ currentPostage.available }}">{{ helper.formatPostage(currentPostage.postage) }}</text>
  </view>
  <view
    wx:if="{{ showLocalDeliveryTime }}"
    class="delivery-time {{ localDeliveryTime === '请选择期望送达时间' ? 'van-c-gray' : '' }}"
  >{{ localDeliveryTime }}</view>
</van-cell>
<time-picker
  id="time-picker"
  theme-color="theme-color"
  theme-bg-color="theme-bg-color"
  theme-border-color="theme-border-color"
  theme-button="theme-button"
  th_tree-a="th_tree-a"
  titles="{{ ['选择配送方式', '选择同城配送日期'] }}"
  show="{{ showTimePicker }}"
  showTime="{{ hasGoods && display.showLocalDeliveryTime }}"
  value="{{ delivery.text }}"
  type="delivery"
  bind:close="handleCloseTimePicker"
  bind:confirm="handleConfirmTime"
>
  <view class="button-group">
    <view 
      class="button-block"
      wx:for="{{ expressWayList }}"
      wx:key="{{ index }}"
    >
      <van-button
        round
        plain
        size="small"
        custom-class="{{ item.expressType == currentPostage.expressType ? 'th_btn-p' : 'postage-button' }}"
        bind:click="onClickExpressWay"
        disabled="{{ !item.available }}"
        data-index="{{ index }}"
      >
        {{ item.postageTitle }}
        <text wx:if="{{ item.available }}">{{ item.postage }}</text>
      </van-button>
      <view wx:if="{{ item.postageWarnDesc }}" class="button-desc">{{ item.postageWarnDesc }}</view>
    </view>
  </view>
  <van-cell wx:if="{{ hasGoods && display.showLocalDeliveryTime }}" title="预约送达时间" />
  <view
    wx:elif="{{ preSaleDeliveryTime }}"
    class="express-way__delivery-time"
  >{{ preSaleDeliveryTime }}</view>
</time-picker>

<wxs module="helper">
function formatPostage(postage) {
  return postage === 0 ? '免运费' : '¥' + (postage / 100).toFixed(2);
}

module.exports.formatPostage = formatPostage;
</wxs>
