import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import config from '@/packages/order-native/common/config';

VanxComponent({
  store,

  mapData: {
    ...mapState(['postage', 'delivery', 'display', 'tradeTag', 'extra']),
    ...mapGetters([
      'hasGoods',
      'currentPostage',
      'preSaleDeliveryTime',
      'showExpressWay',
      'expressWayEditable',
      'showLocalDeliveryTime',
      'localDeliveryTime',
      'expressWayList'
    ])
  },

  externalClasses: ['th_btn-p', 'th_tree-a'],

  data: {
    showTimePicker: false
  },

  attached() {
    this.on(config.eventKey.showExpressWay, () => {
      this.onClickCell();
    });
  },

  methods: {
    // 选择配送方式
    onClickExpressWay(event) {
      const { index } = event.currentTarget.dataset;
      const { expressWayList } = this.$store.getters;
      const { currentExpressType } = this.$store.state.postage;
      const option = expressWayList[index];

      if (!option.available || option.expressType === currentExpressType) return;

      this.$commit('RESET_LOCAL_DELIVERY_TIME');
      this.$commit('SET_EXPRESS_TYPE_CHOICE', option.expressType);
      this.$dispatch('FETCH_SHOW', {
        expressChanged: true
      });
    },

    // 取消选择配送方式
    handleCloseTimePicker() {
      this.setYZData({
        showTimePicker: false
      });
    },

    // 选择同城送时间
    handleConfirmTime(event) {
      const { hasNeedStockUpGoods } = this.data.tradeTag;

      this.$commit('SET_LOCAL_DELIVERY_TIME', event.detail);
      this.handleCloseTimePicker();

      const isSupportSpecialPeriodCost =
        this.data.extra.ATTR_REFRESH_TIME_BUCKET;
      if (hasNeedStockUpGoods || isSupportSpecialPeriodCost) {
        this.$dispatch('FETCH_SHOW', {
          expressChanged: true,
          changeDeliveryTime: true,
        });
      }
    },

    getTimePicker() {
      if (!this.timePicker) {
        this.timePicker = this.selectComponent('#time-picker');
      }
      return this.timePicker;
    },

    // 显示配送方式
    onClickCell() {
      const { expressWayEditable } = this.$store.getters;
      if (!expressWayEditable) {
        return;
      }

      const timePicker = this.getTimePicker();
      timePicker.init();
      this.setYZData({
        showTimePicker: true
      });
    }
  }
});
