@import 'shared/common/css/helper/index.wxss';
@import "themes/order-theme.scss";


.dialog__title {
  font-size: 16px;
  line-height: 44px;
  text-align: center;
}

.dialog__content {
  width: 750rpx;
  height: 386px;
  background-color: #fff;
}

.checkbox {
  align-self: center;
  margin-right: 10px;
  font-size: 18px;
  line-height: 18px;
}

.disabled {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-top: 5px;
  background-color: #f8f8f8;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
}

.postage {
  text-align: left;
}

.postage .price {
  height: 25px;
  line-height: 25px;
}

.postage .description {
  font-size: 12px;
  line-height: 1.5;
  color: #999;
}

.postage .warn {
  font-size: 12px;
  line-height: 1.5;
  color: #f60;
}

.delivery-time {
  font-size: 10px;
  line-height: 1.5em;
}

.value-class {
  color: #111 !important;
}

.button-group {
  display: flex;
  padding: 15px 7.5px 0;
}

.button-block {
  max-width: 50%;
  margin: 0 7.5px;
  flex: 1;
}

.button-desc {
  padding-top: 2px;
  font-size: 12px;
  line-height: 1.5em;
  color: #999;
  text-align: center;
}

.postage-button {
  width: 100%;
}

.express-way__delivery-time {
  padding: 15px 20px;
  font-size: 12px;
  color: #999;
}
