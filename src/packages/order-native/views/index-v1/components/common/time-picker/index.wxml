<import src="./empty.wxml" />
<van-popup position="bottom" show="{{ show }}" bind:close="onClose">
  <view class="content">
    <view class="track" style="transform: translateX(-{{ 750 * activeTab }}rpx)">
      <view class="tab">
        <van-cell title="{{ titles[0] }}" title-class="left-title">
          <van-icon custom-class="van-action-sheet__close" slot="right-icon" name="close" color="#bbb" bind:click="onClose" />
        </van-cell>
        <slot />
        <van-tree-select
          wx:if="{{ showTime }}"
          items="{{ items }}"
          main-active-index="{{ mainActiveIndex }}"
          class="time-picker__tree-select"
          main-active-class="th_tree-a"
          content-active-class="theme-color"
          active-id="{{ activeId }}"
          height="{{ 44 * maxRow }}"
          bind:click-nav="onNavClick"
          bind:click-item="onClickItem"
        />
      </view>
      <view class="tab">
        <van-cell title="{{ titles[1] }}" title-class="right-title" bind:click="setActiveTab">
          <van-icon slot="icon" name="arrow-left" custom-class="arrow-icon" color="#bbb" size="10px" />
          <van-icon custom-class="van-action-sheet__close" slot="right-icon" name="close" color="#bbb" bind:click="onClose" />
        </van-cell>
        <van-picker
          id="picker"
          visible-item-count="{{ 10 }}"
          active-class="theme-color"
          columns="{{ columns }}"
          bind:change="onChange"
        />
      </view>
    </view>

    <van-button
      size="large"
      custom-class="theme-button"
      bind:click="onConfirm"
    >确定</van-button>
  </view>
</van-popup>
