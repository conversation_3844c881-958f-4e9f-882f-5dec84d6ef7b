import { VanxComponent } from 'pages/common/wsc-component/index';
import store from '@/packages/order-native/store/index';
import {
  getFirstFewDays,
  getRange,
  getAvailableTime,
  getConfig,
  getOptionsWithInstantTimePoint,
} from '@/packages/order-native/utils/time/index';
import { compare } from '@/packages/order-native/utils/time/date';

VanxComponent({
  store,

  options: {
    addGlobalClass: true,
  },

  externalClasses: ['th_tree-a'],

  properties: {
    show: Boolean,
    showTime: {
      type: Boolean,
      value: true,
      observer(newValue, oldValue) {
        if (this.data.show && newValue !== oldValue && newValue) {
          setTimeout(() => {
            this.init();
          }, 100);
        }
      },
    },
    titles: Array,
    type: {
      type: String,
      observer(type) {
        this.setYZData({ maxRow: type === 'selfFetch' ? 10 : 8 });
      },
    },
    value: {
      type: String,
      observer(activeId) {
        this.setYZData({ activeId });
      },
    },
  },

  data: {
    mainActiveIndex: 1,
    activeId: '',
    columns: [[], [], []],
    items: [],
    activeTab: 0,
  },

  methods: {
    // 初始化配置 外部调用
    async init() {
      this.initConfig();
      const months = getConfig(this.config, this.range);
      const days = await getFirstFewDays(this.config, this.range, 7);

      const dayHide = JSON.parse(JSON.stringify(days[0] || {}));
      dayHide.text = '';

      const showMore = days.length > 7;
      this.setYZData({
        items: showMore
          ? [dayHide].concat(days.slice(0, 7)).concat({ text: '更多日期' })
          : [dayHide].concat(days.slice(0, 7)),
      });
      if (Array.isArray(months) && months.length) {
        this.updateColumns(months);
      }
    },

    initConfig() {
      const { type } = this.data;
      const { state } = this.$store;
      this.config = state[type];
      this.range = getRange(this.config, state);
    },
    // 设置数据
    setItemData() {
      this.setYZData({
        items: this.data.items,
      });
    },
    // 是否存在可选时间 外部调用
    getAvailableTime() {
      const { type } = this.data;
      const { state } = this.$store;
      return getAvailableTime(type, state);
    },

    getPicker() {
      if (this.picker != null) {
        return this.picker;
      }
      this.picker = this.selectComponent('#picker');
      return this.picker;
    },

    async updateColumns(months) {
      const { value } = this.data;

      let availableMonth = null;
      let availableDay = null;
      let isFirstDay = !this.value;
      // 已有选中项时计算默认月、天
      if (value) {
        const [, yearStr = new Date().getFullYear(), monthStr, dayStr] =
          /(?:(\d+)年)?(\d+)月(\d+)日/.exec(value) || [];
        const date = new Date(yearStr, monthStr - 1, dayStr);

        months.some((month, monthIndex) =>
          (month.children || []).some((day, dayIndex) => {
            if (compare.sameDay(date, new Date(day.id))) {
              availableMonth = month;
              availableDay = day;
              isFirstDay = monthIndex === 0 && dayIndex === 0;
              return true;
            }
            return false;
          })
        );
      }

      // 否则默认选中最近可选的月、天
      if (availableMonth == null || availableDay == null) {
        availableMonth = months.find((month) => !month.disabled) || {};
        availableDay =
          (availableMonth.children || []).find((day) => !day.disabled) || {};
      }

      availableDay.children =
        (await getOptionsWithInstantTimePoint({
          day: availableDay,
          config: this.config,
          range: this.range,
          isFirstDay,
          checkOrderLimit: true,
        })) || [];

      const indexes = [
        months.indexOf(availableMonth),
        (availableMonth.children || []).indexOf(availableDay),
        availableDay.children.map((day) => day.id).indexOf(value),
      ];

      const columns = [
        {
          values: months,
          defaultIndex: indexes[0],
        },
        {
          values: availableMonth.children || [],
          defaultIndex: indexes[1],
        },
        {
          values: availableDay.children,
          defaultIndex: indexes[2],
        },
      ];
      this.setData({ columns });

      // NOTE: van-picker 初始化 defaultIndex 有问题，修复后可移除这个逻辑
      setTimeout(() => {
        const picker = this.getPicker();
        picker && picker.setIndexes(indexes);
        const availableDayIndex = this.getAvailableDayIndex(
          months,
          indexes,
          true
        );
        // 小于7天才处理默认选中
        if (availableDayIndex < 7) {
          this.onNavClick({ detail: { index: availableDayIndex + 1 } }, false);
        } else {
          this.setYZData({ activeTab: 1 });
        }
      }, 100);
    },

    // 计算选中index
    getAvailableDayIndex(months, indexes, init) {
      try {
        const [monthsIndex, dayIndex] = indexes;
        let index = init ? dayIndex : months[monthsIndex].children.length;
        if (monthsIndex > 0) {
          index += this.getAvailableDayIndex(
            months,
            [monthsIndex - 1, dayIndex],
            false
          );
        }
        return index;
      } catch (error) {
        return 0;
      }
    },
    // picker 选择
    onChange(event) {
      const { picker, value, index } = event.detail;
      if (index === 0) {
        this.updateMonth(picker, value[index]);
      } else if (index === 1) {
        this.updateDay(picker, value[index]);
      } else if (index === 2) {
        this.updateTime(picker, value[index]);
      }
    },
    // picker 选择月份
    updateMonth(picker, month) {
      // 更新日期可选项
      picker.setColumnValues(1, month.children);
      const availableDay = month.children.find((day) => !day.disabled);
      this.updateDay(picker, availableDay);
    },
    // picker 选择日期
    async updateDay(picker, day) {
      // 更新时间可选项
      day.children = await getOptionsWithInstantTimePoint({
        day,
        isFirstDay:
          picker.getColumnIndex(0) === 0 && picker.getColumnIndex(1) === 0,
        config: this.config,
        range: this.range,
        checkOrderLimit: true,
      });
      this.setItemData();

      picker.setColumnValues(2, day.children);
      const availableTime = day.children.find((time) => !time.disabled) || {};
      this.updateTime(picker, availableTime);

      const { items } = this.data;
      let mainActiveIndex = items.findIndex((item) => item.id === day.id);
      mainActiveIndex = mainActiveIndex !== -1 ? mainActiveIndex : 0;
      this.setYZData({ mainActiveIndex });
    },
    // picker 选择时间
    updateTime(_, time = {}) {
      this.setYZData({
        activeId: time.id,
        innerValue: time.value,
      });
    },
    // tree-select 选择时间
    async onNavClick(event, autoSelectTime = true) {
      const { index } = event.detail;
      // 点击更多日期
      if (index === 8) {
        this.setYZData({ activeTab: 1 });
        return;
      }

      // 更新 picker 选中项
      this.setYZData({ mainActiveIndex: index });

      const day = this.data.items[index];
      const picker = this.getPicker();
      const months = picker.getColumnValues(0);
      let month = picker.getColumnValue(0);

      const stack = [];

      if (this.parentIndexOfChild(month, day) === -1) {
        month = months.find((item) => this.parentIndexOfChild(item, day) > -1);

        stack.push(picker.setColumnIndex(0, months.indexOf(month)));
        stack.push(picker.setColumnValues(1, month.children, false));
      }

      const dayIndex = this.parentIndexOfChild(month, day);
      stack.push(picker.setColumnIndex(1, dayIndex));

      day.children = await getOptionsWithInstantTimePoint({
        day,
        isFirstDay:
          picker.getColumnIndex(0) === 0 && picker.getColumnIndex(1) === 0,
        config: this.config,
        range: this.range,
        checkOrderLimit: true,
      });
      this.setItemData();

      stack.push(picker.setColumnValues(2, day.children, false));

      Promise.all(stack).then(() => {
        if (autoSelectTime) {
          const availableTime =
            day.children.find((time) => !time.disabled) || {};
          this.onClickItem({ detail: availableTime });
        }
      });
    },

    async onClickItem(event) {
      if (this.data.mainActiveIndex === 0) {
        await this.onNavClick({ detail: { index: 1 } }, false);
      }
      this.setYZData({
        activeId: event.detail.id,
        innerValue: event.detail.value,
      });

      // 更新 picker 选中项
      const picker = this.getPicker();
      const { items, mainActiveIndex } = this.data;
      const day = items[mainActiveIndex];
      const timeIndex = this.parentIndexOfChild(day, event.detail);
      picker.setColumnIndex(2, timeIndex);
    },

    parentIndexOfChild(parent, child) {
      return parent.children.findIndex((item) => item.id === child.id);
    },

    onConfirm() {
      const { data } = this;
      this.triggerEvent('confirm', data.innerValue);
    },

    onClose(event) {
      this.triggerEvent('close', event.detail);
    },

    setActiveTab() {
      this.setYZData({ activeTab: 0 });
    },
  },
});
