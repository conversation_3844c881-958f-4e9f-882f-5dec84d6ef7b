@import "themes/order-theme.scss";

.content {
  overflow: hidden;
}

.left-title {
  font-weight: bold;
  text-align: center;
}

.right-title {
  font-weight: bold;
}

.track {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.84, 0.84, 0.32, 0.32);
}

.tab {
  flex: none;
  width: 750rpx;
}

.arrow-icon {
  margin-right: 5px;
}

.van-action-sheet__close {
  position: absolute !important;
  top: 0;
  right: 0;
  display: flex !important;
  height: 100%;
  padding: 0 15px;
  font-size: 18px !important;
  color: #999;
  align-items: center;
}
