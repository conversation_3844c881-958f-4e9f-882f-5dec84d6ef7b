Component({
  properties: {
    textList: {
      type: <PERSON><PERSON><PERSON>,
      observer() {
        this.showOpen();
      },
    },
  },

  data: {
    maxHeight: '4.5em',
    show: false,
  },

  ready() {
    this.showOpen();
  },

  methods: {
    open() {
      this.setData({
        show: false,
        maxHeight: '999em',
      });
    },

    showOpen() {
      const query = this.createSelectorQuery();
      query.select('.collapse-wrappe-text').boundingClientRect();
      query.select('.collapse-wrapper').boundingClientRect();
      query.exec((res) => {
        if (res[0].height <= res[1].height) {
          this.setData({
            show: false,
          });
        } else {
          this.setData({
            show: true,
          });
        }
      });
    },
  },
});
