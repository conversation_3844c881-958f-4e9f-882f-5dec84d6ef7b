<van-popup
  z-index="{{ zIndex }}"
  class="lined-up__wrapper"
  custom-style="background-color: rgba(0, 0, 0, 0)"
  close-on-click-overlay="{{ false }}"
  show="{{ show }}"
>
  <view class="lined-up__container lined-up__container--{{stage}}">
    <van-loading custom-class="van-loading" wx:if="{{ !end }}" color="#fff" size="44px"></van-loading>
    <view class="lined-up__image" wx:else></view>
    <view
      wx:if="{{ showText }}"
      class="lined-up__content"
    >
      <block wx:if="{{ !end }}">
        <view>抢购人数太多</view>
        <view>请等待{{ contentText }}</view>
      </block>
      <view wx:else>店铺太火爆啦，请稍后重试</view>
    </view>
    <van-button
      wx:if="{{ end }}"
      plain
      round
      class="lined-up__close-btn"
      bind:click="close"
    >
      我知道了
    </van-button>
  </view>
</van-popup>
