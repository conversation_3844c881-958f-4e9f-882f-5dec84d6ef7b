<view>
  <pay
    custom-class="order-pay-class"
    pay-ways="{{ payWays }}"
    show-pay-ways="{{ showPayWays }}"
    show-password="{{ showPassword }}"
    secure-account="{{ secureAccount }}"
    loading-pay-way="{{ loadingPayWay }}"
    show-adjust-price="{{ showAdjustPrice }}"
    new-price="{{ newPrice }}"
    is-paying="{{ isPaying }}"
    bind:close="doClose"
    bind:pay-way-selected="onPayWaySelected"
    bind:close-password="onClosePassword"
    bind:cancel-adjust-price="cancelAdjustPrice"
  />

  <zan-pay
    bind:init="initPay"
    bind:paysuccess="onPaySuccess"
    bind:navigate="onCashierNavi"
    bind:cashierclose="onCashierClose"
  />
  <lined-up
    z-index="{{ 120 }}"
    show="{{ showLinedUp }}"
    end="{{ linedUpStage === 3 }}"
    show-text="{{ linedUpStage > 1 }}"
    stage="{{ linedUpStage }}"
    bind:close="onLinedUpClose"
  />
  
  <van-overlay mask show="{{ showTips }}" z-index="199" />
  <view class="wrapper" wx:if="{{ showTips }}" bind:tap="closeTips">
    <image src="https://img01.yzcdn.cn/upload_files/2020/03/25/Fq24gCcTJhE3bpk9giOf7LPqC-7H.gif" alt="" />
  </view>
</view>
