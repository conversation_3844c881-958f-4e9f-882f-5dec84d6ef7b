import { mapState } from '@youzan/vanx';
import args from '@youzan/weapp-utils/lib/args';
import money from '@youzan/weapp-utils/lib/money';
import get from '@youzan/weapp-utils/lib/get';
import YunComponent from '@/youzanyun-sdk/yun-component';
import store from '@/packages/order-native/store/index';
import { uglifyMobile, findZanPayBiz } from '@/packages/order-native/utils';
import PayService from '@/packages/order-native/biz-service/pay-service';
import paySuccessNavigateHandle from '@/packages/order-native/event-handle/pay-success-navigate';
import { checkWscSingleStore, checkRetailShop } from '@youzan/utils-shop';
import {
  startWxVideoPay,
  wxVideoPaySuccess,
} from '@/packages/order-native/utils/wxvideo';
import { PAGE_ID, SCENE_ENUM } from 'utils/retail-subscribe-message';
import {
  queryWechatSubscribeResult,
  requestSubscribeMessage,
  getTemplateByScene,
} from 'utils/subscribe-message';
import { checkIsChannelsAsync, checkIsOpenedWxV3 } from 'shared/utils/channel';

const app = getApp();
let wxresolve = null;
let wxreject = null;
const wxPayPromise = new Promise((reso, rej) => {
  wxresolve = reso;
  wxreject = rej;
});

YunComponent({
  store,

  mapData: {
    ...mapState(['pay', 'order', 'groupon', 'goods', 'showWxSubscribe']),
  },

  externalClasses: ['order-pay-class'],

  data: {
    showPayWays: false,
    showLinedUp: false,
    payWays: [],
    cashierManager: false,
    showPassword: false,
    secureAccount: '',
    loadingPayWay: {},
    isPaying: false,
    showAdjustPrice: false,
    newPrice: -1,
    giftRetry: 1,
    outBizNo: '',
    templateIds: [],
    showTips: false,
    changeSubscribe: false,
    isWscSingleStore: false,
    isRetailStore: false, // 零售店铺
    isShelfStore: false, // 24h货架过来的店铺
    isWxShopStore: false, // 网店小程序
    retailTemplateIds: [],
    curScene: '', // 当前订单履约方式
  },

  ready() {
    const mobile = app.getMobile();
    const secureAccount =
      mobile && mobile.length === 11 ? uglifyMobile(mobile) : mobile;
    this.getShopMetaInfo();
    this.setYZData({ secureAccount });
  },

  methods: {
    getShopMetaInfo() {
      app
        .request({
          path: '/wscshop/shop/shop-meta-info.json',
          data: { kdt_id: app.getKdtId() },
        })
        .then((data) => {
          const isWscSingleStore = checkWscSingleStore(data);
          const isRetailStore = checkRetailShop(data);
          const { pay } = this.$store.state;
          const { bizExt } = pay.prepayParams || {};
          let tradeBizExt;
          try {
            tradeBizExt = JSON.parse(bizExt);
          } catch (e) {
            tradeBizExt = {};
          }
          //  24小时货架和网店小程序
          const isShelfStore = tradeBizExt.order_mark === 'retail_minapp_shelf';
          const isWxShopStore = tradeBizExt.order_mark === 'wx_shop';
          if (isWscSingleStore) {
            this.fetchTemplateIds();
          } else if (isRetailStore) {
            this.fetchRetailTemplateIds({ isShelfStore });
          }
          this.setYZData({
            isWscSingleStore,
            isShelfStore,
            isWxShopStore,
            isRetailStore,
          });
        });
    },
    fetchTemplateIds() {
      app
        .request({
          path: '/wscump/common/get-template.json',
          data: { scene: 'afterPaySuccess' },
        })
        .then((res) => {
          this.setYZData({ templateIds: res.templateIdList || [] });
        });
    },
    fetchRetailTemplateIds({ isShelfStore }) {
      const promises = [queryWechatSubscribeResult(PAGE_ID.ORDER_BUY)];
      const {
        currentPostage: { expressType },
      } = this.$store.getters;
      if (isShelfStore && expressType === 0) {
        this.setYZData({ retailTemplateIds: [] });
        return;
      }
      const scene = SCENE_ENUM[expressType];
      promises.push(getTemplateByScene(scene));
      Promise.all(promises).then(([templateRes, templateIdRes]) => {
        const { templateList = [] } = templateRes || {};
        const { templateIdList = [] } = templateIdRes || {};
        const templateId = templateIdList.map((id) => ({ templateId: id }));
        this.setYZData({
          retailTemplateIds: templateId.concat(templateList),
          curScene: scene,
        });
      });
    },
    async initPay({ detail }) {
      const { shopMetaInfo = {} } = app.getShopInfoSync() || {};
      const metadata = {
        kdt_id: app.getKdtId(),
        buyer_id: app.getBuyerId(),
        account: app.getMobile(),
      };
      this.payService = new PayService({
        toast: wx.showToast,
        clear: wx.hideToast,
        request: app.request,
        metadata,
        biz: findZanPayBiz(shopMetaInfo),
        account: metadata.account,
        quickMode: true,
        zanPayBypass: true,
        onPaySuccessSync: () => {
          this.showSubscription().then(() => {
            wxresolve();
          });
        },
        onCashierFail: () => {
          this.onCashierClose();
        },
      });
      const crypto = await import('@youzan/crypto');
      this.payService.init(detail, crypto);
    },

    onPaySuccess({ detail: { payChannel } }) {
      const { order, guarantee, useWxvideoPayFlow } = this.$store.state;

      // 视频号交易组件支付成功打标
      if (useWxvideoPayFlow) {
        wxVideoPaySuccess(order.orderNo);
      }

      const guaranteeOn = get(guarantee, 'yzGuarantee', false);
      this.$dispatch('LOG_PAY_SUCCESS', { guarantee_on: guaranteeOn });
      this.trigger('trade:order:paid', order.orderNo);
      this.triggerAsync &&
        this.triggerAsync('afterOrderPaid', { payChannel }).then((res) => {
          paySuccessNavigateHandle(this.$store.state).then(async (result) => {
            if (!result && res !== false) {
              if (payChannel === 'WX_APPLET') {
                await wxPayPromise;
              } else {
                wxresolve();
              }
              this.navigateSuccess();
            }
          });
        });
    },

    onCashierNavi({ detail: destination }) {
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${destination.url}&title=${destination.title}`,
      });
    },

    onCashierClose() {
      this.triggerEvent('zanpay-close');
    },

    closeTips() {
      this.setYZData({ showTips: false });
    },

    /* 原老收银台排队逻辑
    initPayManager() {
      if (this.data.cashierManager) {
        return;
      }

      this.data.cashierManager = new PayManager({
        cashierRequestUrl: 'wsctrade/pay/wscweapp/payChannels.json',
        payRequestUrl: 'wsctrade/pay/wscweapp/pay.json',
        fail: this.onPayFail(),
        retryHook: (retry) => this.retryHook(retry),
        resolvedHook: () => this.resolvedHook(),
      });
    },

    onLinedUpEnd() {
      this.setYZData({ loadingPayWay: {}, isPaying: false });
      this.data.cashierManager.cancelLinedUp();
    },

    retryHook(retry) {
      this.setYZData({ showLinedUp: retry });
    },

    resolvedHook() {
      this.setYZData({ showLinedUp: false });
    },

    onLinedUpClose() {
      this.data.cashierManager.resetPayProcessingToken();
      this.triggerEvent('pay-fail', 'linedUpError');
      this.setYZData({ showLinedUp: false });
    },
    */

    // 检查预下单
    checkPrepay() {
      const { pay } = this.$store.state;

      // 预下单失败
      if (pay.prepay && !pay.prepaySuccess) {
        this.$dispatch('LOADING');
        return this.$dispatch('CREATE_PREPAY');
      }

      return Promise.resolve();
    },

    startPay() {
      return this.checkPrepay().then(() => {
        this.doStartPay();
      });
    },

    async doStartPay() {
      const {
        pay,
        order,
        display = {},
        wxOrderInfo,
        useWxvideoPayFlow,
      } = this.$store.state;

      this.payService.setStore(this.$store);

      try {
        const kdtId = app.getKdtId();
        app.logger.log({
          et: 'click',
          ei: 'order_startpay',
          en: '订单页启动支付',
          si: kdtId,
          params: {
            order_no: order.orderNo,
            kdt_id: String(kdtId),
          },
        });
      } catch (error) {
        /** IGNORE */
      }

      // 无须支付，直接跳转到回调地址
      if (pay.showPayResult) {
        this.navigateSuccess();
        this.isPaying = false;
        return;
      }

      // 非视频号订单,且已开通交易3.0,视频号场景无法支付
      const isOpenedWxV3 = await checkIsOpenedWxV3();
      const isChannelsAsync = await checkIsChannelsAsync();

      if (!useWxvideoPayFlow && isOpenedWxV3 && isChannelsAsync) {
        wx.showToast({ title: '该订单不支持在视频号场景下支付', icon: 'none' });
        this.triggerEvent('pay-fail', {
          detail: '该订单不支持在视频号场景下支付',
        });
        return;
      }

      const { partnerId, prepayId, cashierSign, cashierSalt, scene, bizExt } =
        pay.prepayParams;
      const { openId: wxSubOpenId } = app.getToken() || {};
      let tradeBizExt;
      try {
        tradeBizExt = JSON.parse(bizExt);
        tradeBizExt.appId = app.getAppId();
      } catch (e) {
        tradeBizExt = {};
      }

      const { selectedInstallmentPeriod = null } = display;
      const recommendPayTools = [];

      const {
        show = false,
        enable = false,
        confirm = '0',
      } = this.$store.state.useBeforePayData || {};
      if (show && enable && confirm === '1') {
        recommendPayTools.push('PRIOR_USE');
      }

      this.genAfterPayPath()
        .then((afterPayUrl) => {
          const prepayData = {
            cashierSalt,
            cashierSign,
            partnerId,
            prepayId,
            wxSubOpenId,
            scene,
            tradeBizExt,
            recommendPayTools,
            wxOrderInfo,
            orderNo: order.orderNo,
            afterPayUrl: { weappUrl: afterPayUrl },
          };

          if (useWxvideoPayFlow) {
            return startWxVideoPay(prepayData, this.payService);
          }

          return selectedInstallmentPeriod
            ? this.payService.quickInstalment({
                ...prepayData,
                selectedInstallmentPeriod,
              })
            : this.payService.startPay(prepayData);
        })
        .then(({ payWays, res = {} } = {}) => {
          // 兼容 zan-pay
          if (!payWays) return;

          // 老收银台逻辑
          this.setYZData({
            payWays,
            outBizNo: res.outBizNo || null,
            showPayWays: true,
          });
          this.triggerEvent('get-pay-ways', {});
        });
    },

    onPayWaySelected({
      detail: { payWay = {}, newPrice = -1, acceptPrice = 0, password = '' },
    }) {
      // 这里不能延迟 加immediate处理
      this.setYZData(
        { loadingPayWay: { ...payWay, password }, isPaying: true },
        { immediate: true }
      );

      // 日志埋点
      this.$dispatch('LOG_CLICK_PAY_TYPE', {
        pay_channel: payWay.payChannel,
        pay_channel_name: payWay.payChannelName,
      });

      const { order } = this.$store.state;

      // 支付
      this.payService
        .callPay({
          payChannel: payWay.payChannel,
          newPrice,
          acceptPrice,
          password,
        })
        .then(() => {
          this.setYZData({ loadingPayWay: {}, isPaying: false });
          // 有些支付可以直接跳转成功页
          if (
            ['CREDIT_CARD', 'BANK_CARD', 'INSTALMENT'].indexOf(
              payWay.payChannel
            ) < 0
          ) {
            // WX_APPLET, ECARD
            this.trigger('trade:order:paid', order.orderNo);
            this.triggerAsync &&
              this.triggerAsync('afterOrderPaid', {
                payChannel: payWay.payChannel,
              }).then((res) => {
                paySuccessNavigateHandle(this.$store.state).then((result) => {
                  if (!result && res !== false) {
                    this.navigateSuccess(payWay);
                  }
                });
              });
          }
        });
    },

    onPayFail() {
      const _this = this;
      return function (err) {
        _this.setYZData({ isPaying: false });
        let errMsg = err.message || err.msg || '支付失败，请稍后再试';
        _this.triggerEvent('pay-fail', errMsg);
        const { pay } = _this.$store && _this.$store.state;
        switch (err.name) {
          case 'cancel':
            // 取消微信支付或货到付款
            _this.setYZData({ loadingPayWay: {} });
            break;
          case 'WxPayError':
            _this.setYZData({ loadingPayWay: {} });
            // 微信支付错误日志埋点
            app.logger.appError({
              name: 'wx_pay_error',
              message: errMsg,
              detail: {
                ...err.errorContent,
                ...(pay && pay.prepayParams),
                ...(__wxConfig && __wxConfig.appLaunchInfo),
              },
            });
            if (errMsg && errMsg.indexOf('fail jsapi has no permission') >= 0) {
              errMsg = '受微信政策限制，请前往店铺公众号或H5进行购买';
            } else if (
              errMsg &&
              errMsg.indexOf('requestPayment:fail parameter error') >= 0
            ) {
              errMsg = '支付失败，请稍后重试';
            }
            _this.$dispatch('TOAST', errMsg);
            break;
          // 订单改价
          case 'adjust_price':
            if (this._showAdjustPrice) {
              _this.setYZData(
                { showAdjustPrice: false, newPrice: err.errorContent.newPrice },
                {
                  immediate: true,
                  cb: () => {
                    _this.setYZData({
                      showAdjustPrice: true,
                      newPrice: err.errorContent.newPrice,
                    });
                  },
                }
              );
            } else {
              this._showAdjustPrice = true;
              _this.setYZData({
                showAdjustPrice: true,
                newPrice: err.errorContent.newPrice,
              });
            }
            break;
          default:
            switch (err.code) {
              // 需要密码
              case 117700511:
                _this.setYZData({ showPassword: true });
                break;
              // 密码错误
              case 117701503:
                _this.setYZData({ showPassword: true });
                _this.$dispatch('TOAST', err.message || err.msg || '密码错误');
                break;
              default:
                if (!_this.data.showPassword) {
                  _this.setYZData({ loadingPayWay: {} });
                }
                _this.$dispatch(
                  'TOAST',
                  err.message || err.msg || '支付失败，请稍后再试'
                );
                break;
            }
        }
      };
    },

    genAfterPayPath(giftRolling = false) {
      const {
        groupon,
        pay,
        tradeTag,
        order,
        env,
        valueCard,
        shop,
        isHandselFirstPhase,
        goods,
      } = this.$store.state;
      const { finalPrice, isEduOrder } = this.$store.getters;
      // show 页面 或者showpay 页面不同，但是都做gift 处理
      const isGift =
        (order.config.gift && order.config.gift.giftOrder) || order.gift;
      if (isHandselFirstPhase) {
        return Promise.resolve(
          `/packages/ump/handsel-expand/index?order_no=${
            order.orderNo
          }&kdt_id=${app.getKdtId()}`
        );
      }

      if (isGift) {
        this.giftRetry = 1;
        return this.fetchGiftId()
          .then((giftId) => {
            return `/packages/gift/share/index?presenter_view=1&gift_id=${giftId}`;
          })
          .catch((err) => {
            if (err.code && this.giftRetry < 3) {
              this.giftRetry += 1;
              setTimeout(() => {
                this.navigateGiftShareDetail();
              }, 1000);
            } else {
              this.$dispatch('TOAST', err.msg || '获取礼盒id 失败');
            }
          });
      }

      if (groupon.isGroupon) {
        const { goodsId } = goods.list[0] || {};
        return isEduOrder
          ? Promise.resolve(
              `/packages/paidcontent/groupon/index?orderNo=${order.orderNo}&goodId=${goodsId}`
            )
          : Promise.resolve(
              `/packages/collage/groupon/detail/index?from=pay&orderNo=${order.orderNo}&type=${groupon.activityType}`
            );
      }

      if (shop.activityType === 23) {
        // 抽奖拼团
        let activityId = 0;
        try {
          activityId = order.ump.activities[0].activityId;
        } catch (e) {
          console.log(e);
        }
        return Promise.resolve(
          `/packages/collage/lottery/detail/index?order_no=${order.orderNo}&activity_id=${activityId}`
        );
      }

      const data = {
        // 有支付号时传支付号 否则传订单号
        order_no: this.data.outBizNo || order.requestNo || order.orderNo,
        // 多笔订单合并时 支付成功页要跳转到订单列表
        isToOrderList: order.orderNos && order.orderNos.length > 1,
        pay_result: 'success',
        pay_money: money(finalPrice).toCent(), // NOTE: 以分为单位
        changeSubscribe: this.data.changeSubscribe, // 下单时是否唤起微信订阅
      };

      // 传递支付阶段
      if (get(pay, 'phasePayment.currentPhase')) {
        data.phasePaymentStage = pay.phasePayment.currentPhase;
      }

      // 储值卡支付设置支付阶段
      if (get(valueCard, 'checked.length')) {
        data.phasePaymentStage = 2;
      }

      // 设置实物定金预售
      data.depositPreSale = tradeTag.hasDepositPreSaleGoods;

      const dbid = app.db.set(data);
      const path = env.successUrl
        ? decodeURIComponent(env.successUrl)
        : '/packages/order/paid/index';
      const defaultPath = args.add(path, { dbid });

      if (isHandselFirstPhase) {
        return Promise.resolve(
          `/packages/ump/handsel-expand/index?order_no=${
            order.orderNo
          }&kdt_id=${app.getKdtId()}`
        );
      }

      if (isGift) {
        this.giftRetry = 1;
        return this.fetchGiftId()
          .then((giftId) => {
            return `/packages/gift/share/index?presenter_view=1&gift_id=${giftId}`;
          })
          .catch((err) => {
            if (err.code && this.giftRetry < 3 && giftRolling) {
              this.giftRetry += 1;
              setTimeout(() => {
                this.navigateGiftShareDetail();
              }, 1000);
            } else if (giftRolling) {
              this.$dispatch('TOAST', err.msg || '获取礼盒id 失败');
            } else {
              // 当 giftId 请求失败, 银行卡支付成功跳转 导向默认成功页
              return defaultPath;
            }
          });
      }

      if (groupon.isGroupon) {
        const { goodsId } = goods.list[0] || {};
        return isEduOrder
          ? Promise.resolve(
              `/packages/paidcontent/groupon/index?orderNo=${order.orderNo}&goodId=${goodsId}`
            )
          : Promise.resolve(
              `/packages/collage/groupon/detail/index?from=pay&orderNo=${order.orderNo}&type=${groupon.activityType}`
            );
      }

      if (shop.activityType === 23) {
        // 抽奖拼团
        let activityId = 0;
        try {
          activityId = order.ump.activities[0].activityId;
        } catch (e) {
          console.log(e);
        }
        return Promise.resolve(
          `/packages/collage/lottery/detail/index?order_no=${order.orderNo}&activity_id=${activityId}`
        );
      }

      return Promise.resolve(defaultPath);
    },

    navigateSuccess() {
      // 激活 giftId 查询失败时的轮询
      this.genAfterPayPath(true).then((path) => {
        app.trigger('event:pay:success');
        if (path && typeof path === 'string') {
          wx.redirectTo({ url: path });
        }
      });
    },

    showSubscription() {
      const { showWxSubscribe } = this.$store.state;
      const {
        templateIds = [],
        isWscSingleStore,
        isShelfStore,
        retailTemplateIds,
        isRetailStore,
        curScene,
      } = this.data;
      // 目前只支持微商城单店及零售， showWxSubscribe开关影响微商城单店
      if (
        (!isWscSingleStore && !isRetailStore) ||
        (isWscSingleStore && !showWxSubscribe) ||
        (isRetailStore && !retailTemplateIds.length)
      )
        return Promise.resolve();

      return new Promise((resolve) => {
        return requestSubscribeMessage({
          templates: isRetailStore ? retailTemplateIds : templateIds,
          onSelfLog: isRetailStore
            ? {
                subscribePos: '待支付完成后回调',
                subscribeSource: isShelfStore ? '24h_shelf' : 'wx_shop',
                deliveryWay: curScene,
              }
            : {},
          onFail: (err) => {
            resolve();
          },
          onSuccess: (res) => {
            if (!isRetailStore) {
              if (JSON.stringify(res).indexOf('accept') > -1) {
                wx.showToast({ title: '订阅通知成功', icon: 'none' });
                app.logger &&
                  app.logger.log({
                    et: 'click',
                    ei: 'allow_click',
                    en: '点击允许',
                    pt: 'paySuspend',
                    params: {},
                  });
              } else {
                app.logger &&
                  app.logger.log({
                    et: 'click',
                    ei: 'cancle_click',
                    en: '点击取消',
                    pt: 'paySuspend',
                    params: {},
                  });
              }
              this.setYZData({ changeSubscribe: true });
            }
            resolve();
          },
          onShowTips: () => {
            this.setYZData({ showTips: true });
          },
          onCloseTips: () => {
            this.setYZData({ showTips: false });
          },
        });
      });
    },

    navigateGiftShareDetail() {
      this.fetchGiftId()
        .then((giftId) => {
          wx.redirectTo({
            url: `/packages/gift/share/index?presenter_view=1&gift_id=${giftId}`,
          });
        })
        .catch((err) => {
          if (err.code && this.giftRetry < 3) {
            this.giftRetry += 1;
            setTimeout(() => {
              this.navigateGiftShareDetail();
            }, 1000);
          } else {
            this.$dispatch('TOAST', err.msg || '获取礼盒id 失败');
          }
        });
    },

    fetchGiftId() {
      const { order } = this.$store.state;

      return app
        .request({
          path: 'wscump/gift/giftid.json',
          data: { orderNo: order.orderNo },
        })
        .then((resp) => {
          if (resp && resp.alias) {
            return resp.alias;
          }
          // 如果没有礼单500ms后重试
          throw new Error('retry');
        });
    },

    onClosePassword() {
      this.setYZData({ loadingPayWay: {}, showPassword: false });
    },

    cancelAdjustPrice() {
      this.setYZData({ showAdjustPrice: false, loadingPayWay: {} });
    },

    doClose() {
      this.setYZData({ showPayWays: false });
    },
  },
});
