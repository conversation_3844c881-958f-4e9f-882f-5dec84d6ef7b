import WscComponent from 'pages/common/wsc-component/index';

function formatDot(count) {
  return '.'.repeat((Math.abs(count) % 3) + 1);
}

WscComponent({
  properties: {
    show: {
      type: Boolean,
      value: false,
      observer: 'onLinedUpToggle',
    },
    end: {
      type: Bo<PERSON>an,
      value: false,
      // observer: 'onLinedUpToggle',
    },
    showText: {
      type: Boolean,
      value: false,
    },
    stage: {
      type: Number,
      value: 1,
      observer: 'onLinedUpStageChange',
    },
    zIndex: Number,
  },

  data: {
    // 第二阶段第二行的尾部文案
    contentText: '（4s）',
    // 第二形态倒计时数字
    count: 4,
    // 整个排队交互的 timer
    timer: null,
  },

  methods: {
    onLinedUpToggle(show) {
      if (!show) {
        this.reset();
      }
    },
    onLinedUpStageChange(stage) {
      if (stage === 2) {
        this.countDown();
      } else if (stage === 3) {
        this.stopCount();
      }
    },
    countDown() {
      const timer = setInterval(() => {
        const { count } = this.data;
        this.setYZData({
          contentText: count > 0 ? `（${count - 1}s）` : formatDot(count),
          count: count - 1,
        });
      }, 1000);
      this.setYZData({ timer });
    },
    stopCount() {
      clearInterval(this.data.timer);
    },
    close() {
      this.triggerEvent('close');
    },
    reset() {
      clearInterval(this.data.timer);
      setTimeout(() => {
        this.setYZData({
          contentText: '（4s）',
          count: 4,
          timer: null,
        });
      }, 200);
    },
  }
});
