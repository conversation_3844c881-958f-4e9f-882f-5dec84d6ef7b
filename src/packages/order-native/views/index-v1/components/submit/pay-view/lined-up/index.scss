$wifi: 'https://b.yzcdn.cn/fix-base64/eb697257db34df959e6f080384a9e98306e9543a7b053eeec953a376584fa471.png';

.lined-up {
  &__wrapper {
    background-color: rgba(0, 0, 0, 0);
    overflow: visible;
  }

  &__container {
    width: 134px;
    background-color: #000;
    border-radius: 16px;
    text-align: center;
    box-sizing: border-box;
    padding: 12px 0;
    position: relative;

    .van-loading {
      display: inline-block;
      margin: 12px 0;
    }

    &--1 {
      padding: 38px 0;
    }

    &--3 {
      background-color: #fff;
      width: 300px;
      height: auto;
      padding: 34px 0;

      .lined-up__content view:first-child {
        color: #39393a;
        margin-bottom: 8px;
      }

      .lined-up__content view:last-child {
        color: #969799;
      }

      .lined-up__close-btn {
        position: relative;
        bottom: 0;
        left: 0;
        transform: translateX(0);
        width: 104px;
        margin-top: 12px;
        border-color: #c8c9cc;
        color: #39393a;
        line-height: 38px;
        height: 40px;
      }

      .lined-up__image {
        background-image: url($wifi);
      }
    }
  }

  &__content {
    font-size: 14px;
    line-height: 20px;
    color: #fff;
    margin-bottom: 12px;
  }

  &__close-btn {
    position: absolute;
    bottom: -48px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0);
    color: #dcdee0;
    line-height: 30px;
    height: 32px;
    border-color: #dcdee0;
    width: 76px;
  }

  &__image {
    width: 140px;
    height: 140px;
    display: inline-block;
    background-size: 140px 140px;
    margin-bottom: 24px;
  }
}
