import Dialog from '@vant/weapp/dialog/dialog';
import config from '@/packages/order-native/common/config';

const app = getApp();

// 身份证号校验
function isIdcard(number) {
  return /^\d{15}$/gi.test(number) || (/^\d{17}(\d|X)$/gi.test(number) && isCnNewID(number));
}

// 是否为合规的 18 位身份证号
function isCnNewID(idNumber) {
  const IDExp = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; // 加权因子
  const IDValid = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']; // 校验码

  // 对前17位数字与权值乘积求和
  let sum = 0;
  for (let i = 0, l = idNumber.length; i < l - 1; i++) {
    sum += parseInt(idNumber.charAt(i), 10) * IDExp[i];
  }

  // 检验第18位是否与校验码相等
  return IDValid[sum % 11] === idNumber.charAt(17).toUpperCase();
}

// 校验地址
function validateAddress(state, getters) {
  if (!state.display.showAddressTab) {
    return;
  }

  const { selfFetch } = state;
  const { list, id } = state.address;
  switch (getters.expressType) {
    case 'express': {
      // 团长代收，不校验地址
      if (getters.receiveByGroupHeader) {
        return;
      }

      if (!id) {
        setTimeout(() => {
          const dbid = app.db.set({ list, id, delta: 1 });
          wx.navigateTo({
            url: `/packages/order-native/address-edit/index?dbid=${dbid}`
          });
        }, 1000);
        return '请选择收货地址';
      }

      const address = getters.currentAddress;
      if (
        address.province === '选择省份'
        || address.city === '选择城市'
        || address.county === '选择地区'
        || !address.areaCode
      ) {
        return '省市区不完整，请重新编辑地址';
      }
      break;
    }
    case 'self-fetch':
      if (!getters.currentContact.id) {
        return '请选择提货人';
      }
      if (!selfFetch.shop) {
        return '请选择提货地址';
      }
      if (!selfFetch.time && getters.canSelectSelfFetchTime) {
        return '请选择提货时间';
      }
      break;
    default:
      break;
  }
}

// 校验联系人
function validateContact(state) {
  return state.contact.required && !state.contact.id ? '请选择联系人' : '';
}

// 校验身份证号
export function validateIdCard(state) {
  const { order, idcard, tradeTag } = state;
  if (tradeTag.hasOverseaGoods) {
    if (!idcard.name) {
      return '请输入真实姓名';
    }
    if (!idcard.number) {
      return '请先填写身份信息';
    }
    if (!isIdcard(idcard.number)) {
      return '身份证号有误';
    }
  }

  if (order.needIdCardPhoto && !(idcard.frontPhoto && idcard.backPhoto)) {
    return '请先填写身份信息';
  }

  return '';
}

function validateIdCardAndPrompt(state, getters) {
  const message = validateIdCard(state);
  if (message) {
    Dialog.confirm({
      title: '填写身份证信息',
      message: getters.hasHaitaoGoods ? '为保障海淘商品顺利清关，请填写身份证信息。' : '该商品下单需要填写身份证信息。',
      confirmButtonText: '去填写'
    })
      .then(() => {
        wx.navigateTo({
          url: '/packages/order-native/idcard/index'
        });
      })
      .catch(() => {
        // on cancel
      });
  }
  return message;
}

// 校验留言
export function validateOrderMessage(state) {
  const { buyerMsg } = state.order;
  return buyerMsg && buyerMsg.length > 250 ? '留言字数不能超过 250 个字' : '';
}

// 校验同城送时间
function validateLocalDelivery(state) {
  const { delivery, display } = state;
  const hasTime = delivery.startTime && delivery.endTime;
  if (display.showLocalDeliveryTime && !hasTime) {
    app.trigger(config.eventKey.showExpressWay);
    return '请选择期望送达时间';
  }
  return '';
}

function toastError(validator, state, getters, dispatch) {
  return (needToast = true) => {
    const msg = validator(state, getters);
    if (needToast && msg) {
      dispatch('TOAST', msg);
    }
    return !msg;
  };
}

// 订单校验
const validators = [
  { validator: validateAddress },
  { validator: validateContact },
  { validator: validateIdCardAndPrompt, needToast: false },
  { validator: validateOrderMessage },
  { validator: validateLocalDelivery, needToast: false }
];

export default function validateOrder({ state, getters, dispatch }) {
  return !validators.some(
    ({ validator, needToast } = {}) => !toastError(validator, state, getters, dispatch)(needToast)
  );
}
