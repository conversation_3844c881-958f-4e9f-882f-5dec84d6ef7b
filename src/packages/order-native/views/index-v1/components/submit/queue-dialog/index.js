import { VanxComponent } from 'pages/common/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';

VanxComponent({
  properties: {
    value: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'handleWaiting'
    },
    status: {
      type: String,
      observer: 'handleWaiting'
    },
    asyncOrder: <PERSON><PERSON><PERSON>
  },

  created() {
    this.timeout = null;
    this.interval = [];
  },

  methods: {
    handleWaiting(newValue, oldValue) {
      if (newValue === oldValue) {
        return;
      }
      const { value, status } = this.data;
      if (value && status === 'wating') {
        this.start();
      } else {
        this.reset();
      }
    },

    onClose() {
      this.triggerEvent('cancel');
    },

    reset() {
      clearTimeout(this.timeout);
      this.timeout = null;

      this.interval.forEach(timer => {
        clearInterval(timer);
      });
      this.interval = [];

      Toast.clear();
    },

    start() {
      const toast = Toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        context: this,
        mask: true
      });

      let second = 5;
      const { asyncOrder } = this.data;
      const message = asyncOrder ? '正在为您结算' : '抢购人数太多';

      this.timeout = setTimeout(() => {
        toast.setData({
          message: `${message}\n请等待(${second - 1}s)`
        });

        const timer = setInterval(() => {
          second--;
          if (second > 0) {
            toast.setData({
              message: `${message}\n请等待(${second - 1}s)`
            });
          } else {
            toast.setData({ message: '抢购人数太多\n请等待...' });
            clearInterval(timer);
          }
        }, 1000);

        this.interval.push(timer);
      }, 3000);
    }
  }
});
