<van-popup wx:if="{{ status === 'fail' }}" show="{{ value }}" close-on-click-overlay="{{ false }}" custom-class="component">
  <image src="https://img01.yzcdn.cn/public_files/2019/09/23/a3d773d6d0275cff828a7bb3745eae23.png" alt="" class="icon" />

  <view class="text">
    {{ asyncOrder ? '提交订单中，请稍候': '店铺太火爆啦，请稍后重试' }}
  </view>

  <van-button custom-class="button" round bind:click="onClose">
    知道了
  </van-button>
</van-popup>

<van-toast id="van-toast" style="text-align: center;"/>
