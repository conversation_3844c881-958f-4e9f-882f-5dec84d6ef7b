import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['createWxvideoOrderIntercept']),
  },

  data: {
    title: '无法提交订单',
  },

  methods: {
    close() {
      this.$commit('SET_CREATE_WXVIDEO_ORDER_INTERCEPT', {
        ...(this.data.createWxvideoOrderIntercept || {}),
        visible: false,
      });
    },
  },
});
