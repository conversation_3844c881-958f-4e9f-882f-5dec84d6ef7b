  <van-dialog
    use-slot
    show="{{ createWxvideoOrderIntercept.visible }}"
    custom-style="background-color: transparent;"
    show-confirm-button="{{ false }}">
    <view class="create-wxvideo-order-error-dialog-main">
      <view class="create-wxvideo-order-error-dialog-title">{{ title }}</view>
      <view class="create-wxvideo-order-error-dialog-content">
        <view
          class="create-wxvideo-order-error-dialog-content__body"
          wx:if="{{ createWxvideoOrderIntercept.umpMsg }}"
        >
          <view class="ump-error-block">{{ createWxvideoOrderIntercept.umpMsg.head }}</view>
          <view class="ump-error-skus">
            <view
              class="ump-error-sku-item"
              wx:for="{{ createWxvideoOrderIntercept.umpMsg.skuNameList }}"
            >{{ item }}</view>
          </view>
          <view class="ump-error-block">{{ createWxvideoOrderIntercept.umpMsg.footer }}</view>
        </view>
        <view class="create-wxvideo-order-error-dialog-content__body" wx:else>
          {{ createWxvideoOrderIntercept.msg }}
        </view>
      </view>

      <view class="create-wxvideo-order-error-dialog-footer">
        <van-button
          bind:click="close"
          color="linear-gradient(270deg, #FF6034 0%, #EE0A24 100%)"
          text-style="opacity: 0.94;font-weight: 400;font-size: 14px; color: #FFFFFF;"
          round
          class="create-wxvideo-order-error-dialog__van-button"
          custom-class="create-wxvideo-order-error-dialog__button--confirm"
          >知道了</van-button
        >
      </view>
    </view>
  </van-dialog>