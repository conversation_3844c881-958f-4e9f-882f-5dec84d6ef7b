import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['agreement'])
  },

  data: {
    showPopup: false,
    text: ''
  },

  methods: {
    onClick(e) {
      const agreement = this.data.agreement;
      const type = e.currentTarget.dataset.type;
      this.setYZData({
        showPopup: true,
        text: agreement[type]
      });
      switch (type) {
        case 'user':
          this.$dispatch('LOG_USER_AGGREMENT');
          break;
        case 'privacy':
          this.$dispatch('LOG_CLICK_PRIVACY');
          break;
        default:
          break;
      }
    },

    onClose() {
      this.setYZData({ showPopup: false });
    }
  }
});
