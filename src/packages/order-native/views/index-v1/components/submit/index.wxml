<wxs module="util">
  module.exports.money = function (value) {
    var yuan = value / 100;
    return yuan.toFixed(2);
  }
</wxs>
<!-- 目前已经全部隐藏用户协议，个别定制代码没有走prepare接口因此失效，前端主动隐藏掉 -->
<!-- <buy-agreement wx:if="{{ showAgreement && !orderCreated}}"/> -->
<view wx:if="{{ !memberCard.renewal }}" class="order-pay {{ deviceType }} order-pay-class">
  <van-notice-bar
    wx:if="{{ isShowRiskWarnShopPrompt }}"
    text="{{ riskWarnShopPrompt }}"
    backgroundColor="#FDE6E9"
    color="#323233"
  />
  <view class="order-warning" wx:elif="{{ display.prompt }}">
    {{ display.prompt }}
    <text
      class="van-c-blue"
      wx:if="{{ display.showAddOnItem && !isRetail }}"
      bindtap="handleContinueBuy"
    >去凑单</text>
    <text
      class="van-c-blue"
      wx:elif="{{ display.matchOffline }}"
      bindtap="handleMatchOffline"
    >切换到附近可送网点</text>
  </view>

  <view
    wx:if="{{ !address.visible && currentAddress.id && !isShowRiskWarnShopPrompt }}"
    class="order-warning"
  >
    收货地址：{{ fullAddress }}
  </view>

  <view class="submit-bar van-hairline--top">
    <!-- 先用后付场景 -->
    <view wx:if="{{ showPriorUseSummary }}" class="submit-bar__content">
      <view class="submit-bar__content__price">
        {{ priceLabel }}
        <text class="theme-color">￥0.00</text>
      </view>
      <view wx:if="{{ valueCardDecrease }}" class="submit-bar__content__value-card">
        收货后满意再付款：¥{{ haitaoZeroOrder ? haitaoFinalPrice : finalNeedPayPrice }}
      </view>
    </view>
    <!-- 标准场景 -->
    <view wx:if="{{ !showPriorUseSummary }}" class="submit-bar__content">
      <view class="submit-bar__content__price">
        {{ priceLabel }}
        <text class="theme-color">￥{{ haitaoZeroOrder ? haitaoFinalPrice : finalNeedPayPrice }}</text>
      </view>

      <view
        wx:if="{{ valueCardDecrease }}"
        class="submit-bar__content__value-card"
      >
        储值抵扣：¥{{ util.money(valueCardDecrease) }}
      </view>
    </view>

    <form report-submit bindsubmit="submitOrder">
      <theme-view
        bg="main-bg"
        color="main-text"
      >
        <button
          form-type="submit"
          class="submit-bar__button {{ display.forbidPay || !hasGoods ? 'disabled' : 'use-theme-color' }}"
          loading="{{ isPayBtnLoading }}"
          disabled="{{ display.forbidPay || !hasGoods }}"
        >
          <view hidden="{{ isPayBtnLoading }}">
            <text wx:if="{{ showPriorUseSummary }}">0元下单</text>
            <text wx:elif="{{ orderCreated }}">去支付</text>
            <text wx:elif="{{ memberCard.renewal && memberCard.days }}">购买延长使用{{ memberCard.days }}天</text>
            <text wx:else>提交订单</text>
          </view>
        </button>
      </theme-view>
    </form>
  </view>
</view>

<view wx:else class="order-pay order-pay-class">
  <van-button
    type="{{ isRetailApp ? 'primary' : 'danger' }}"
    loading="{{ isPayBtnLoading }}"
    square
    size="large"
    disabled="{{ display.forbidPay || !hasGoods }}"
    bind:click="submitOrder"
  >
    购买延长使用{{ memberCard.days }}天
  </van-button>
</view>

<!-- 支付方式 -->
<pay-view
  id="payViewRef"
  order-pay-class="order-pay-class"
  catch:get-pay-ways="stopLoading"
  catch:pay-fail="onPayFail"
  catch:zanpay-close="stopLoading"
/>

<!-- 排队限流 -->
<queue-dialog
  value="{{ order.limitCreate }}"
  async-order="{{ order.asyncOrder }}"
  status="{{ order.limitCreateStatus }}"
  bind:cancel="onCancelLimit"
/>

<!-- 储值并支付 -->
<trade-recharge
  show="{{ showPrePay }}"
  value-card="{{ valueCard }}"
  parsed-recommend="{{ recommendDetaid }}"
  bind:onClose="onClose"
  bind:select="onSelect"
/>

<!-- 拦截创建视频号订单提示 -->
<create-wxvideo-order-intercept-dialog />
