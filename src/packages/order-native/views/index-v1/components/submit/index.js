import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import navigate from '@/helpers/navigate';
import BillService from '@/packages/order-native/biz-service/bill-service';
import { backTopage, isPageInHistory } from './utils';
import Event from '@youzan/weapp-utils/lib/event';
import config from '@/packages/order-native/common/config';

const app = getApp();

VanxComponent({
  store,

  mapData: {
    ...mapState([
      'display',
      'memberCard',
      'address',
      'order',
      'riskWarnShopPrompt',
      'valueCard',
      'recommendDetaid',
      'isRetail',
      'useBeforePayData',
    ]),
    ...mapGetters([
      'orderCreated',
      'hasGoods',
      'finalPrice',
      'finalNeedPayPrice',
      'priceLabel',
      'currentAddress',
      'fullAddress',
      'isDepositPresale',
      'haitaoZeroOrder',
      'haitaoFinalPrice',
      'isShowRiskWarnShopPrompt',
      'valueCardDecrease'
    ]),
    ...mapState({
      showAgreement: (state) => !state.userPrivacy.agreement_signed,
      showPriorUseSummary: (state) => {
        const { show = false, enable = false, confirm = '0' } = state.useBeforePayData || {};
        return show && enable && confirm === '1';
      }
    }),
  },

  computed: {
    label() {
      let label = '合计：';

      const { pay } = this.$store.state;
      if (pay.multiPhase) {
        label = pay.phasePayment.currentPhase === 1 ? '定金：' : '尾款：';
      }
      return label;
    }
  },

  externalClasses: ['order-pay-class'],

  data: {
    isPayBtnLoading: false,
    isRetailApp: app.globalData.isRetailApp,
    showPrePay: false
  },

  attached() {
    this.setYZData({
      deviceType: app.getDeviceType()
    });
  },

  ready() {
    // 下单时如果走储值并支付流程，这里要打开选择弹窗
    this.on(config.eventKey.showTradeRecharge, () => {
      this.setYZData({ showPrePay: true });
    });

    this.$commit('SET_HAS_SUBMIT', true);
  },

  methods: {
    getBillService() {
      if (!this.billService) {
        this.billService = new BillService(this.$store);
      }
      return this.billService;
    },

    onCancelLimit() {
      this.$commit('UNLIMIT_CREATE_ORDER');
    },

    // 自动切换多网点
    handleMatchOffline() {
      this.$dispatch('MATCH_OFFLINE').then((storeId) => {
        if (storeId) {
          this.$dispatch('FETCH_SHOW').then(() => {
            wx.showModal({
              title: '已切换到附近可送门店',
              content: '服务门店变更，请重新确认订单信息',
              showCancel: false
            });
          });
        } else {
          wx.showModal({
            content: '附近暂无可送门店',
            showCancel: false
          });
        }
      });
    },

    // 去凑单
    handleContinueBuy() {
      const { env } = this.$store.state;
      if (env.takeoutAlias) {
        const path = `/packages/home/<USER>/index?alias=${env.takeoutAlias}`;
        if (isPageInHistory(path)) {
          backTopage(path);
        } else {
          navigate.navigate({ url: path });
        }
      } else {
        navigate.switchTab({ url: '/packages/home/<USER>/index' });
      }
    },

    // 提交订单
    submitOrder(event) {
      try {
        const { weAppFormId } = this.$store.state.order.source;
        const { orderCreated } = this.$store.getters;
        if (!orderCreated && !weAppFormId && event.detail.formId) {
          this.$commit('SET_FORM_ID', event.detail.formId);
        }
      } catch (err) {
        // do nothing
      }

      if (this.data.isPayBtnLoading) {
        return;
      }
      this.setYZData({ isPayBtnLoading: true });

      if (this.data.orderCreated) {
        this.startPay();
      } else {
        // 创建订单
        this.getBillService()
          .createOrder()
          .then(() => {
            this.startPay();
          })
          .catch((err) => {
            if (err === 'PRESALE_CONFIRM') {
              Event.trigger('SCROLL_TO_PRE_SALE');
            }
            setTimeout(() => {
              this.stopLoading();
            }, 1500);
          });
      }
    },

    startPay() {
      const payWaysRef = this.selectComponent('#payViewRef');

      if (payWaysRef) {
        try {
          payWaysRef.startPay().catch((e) => {
            this.onPayFail({ detail: e.message });
          });
        } catch (e) {
          this.onPayFail({ detail: e.message });
        }
      } else {
        this.$dispatch('TOAST', '支付请求失败，请稍后再试');
        this.onPayFail({ detail: 'payWaysRef不存在' });
      }
    },

    onPayFail(event = {}) {
      this.stopLoading();

      const { order, pay } = this.$store.state;

      app.logger.appError({
        name: 'order_submit_payview_error',
        message: event.detail,
        detail: {
          orderNo: order.orderNo,
          prepayParams: pay.prepayParams
        }
      });
    },

    // stop loading
    stopLoading() {
      this.setYZData({ isPayBtnLoading: false });
    },

    onClose() {
      this.setYZData({
        showPrePay: false
      });
    },

    onSelect(event) {
      const params = event.detail;

      this.setYZData({ showPrePay: false });
      this.$commit('SET_IS_STORE_AND_PAY', params.isStoreAndPayProcess);
      this.$commit('SET_VALUE_CARD_PAY_PARAMS', {
        valueCardRechargeRuleNo: params.ruleNo,
        valueCardRechargeRuleVersion: params.ruleVersion,
        ...params
      });

      this.submitOrder();
    }
  }
});
