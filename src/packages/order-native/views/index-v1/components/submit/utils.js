import args from '@youzan/weapp-utils/lib/args';

/**
 * 回到指定页面
 * @param   {String} path 页面路径
 */
export function backTopage(url) {
  const pages = getCurrentPages() || [];
  const pagesLength = pages.length;
  const options = args.getAll(url);
  const path = url.split('?')[0];
  const targetPageIndex = pages.findIndex(pageData => {
    const route = pageData.route || '';
    return (route === path || `/${route}` === path) && matchObject(pageData.options, options);
  });

  if (targetPageIndex > -1) {
    wx.navigateBack({ delta: pagesLength - targetPageIndex - 1 });
  }
}

function matchObject(a, b) {
  return Object.keys(b).every(key => a[key] === b[key]);
}

export function isPageInHistory(url) {
  const pages = getCurrentPages() || [];
  const options = args.getAll(url);
  const path = url.split('?')[0];
  const targetPageIndex = pages.findIndex(pageData => {
    const route = pageData.route || '';
    return (route === path || `/${route}` === path) && matchObject(pageData.options, options);
  });
  return targetPageIndex > -1;
}
