@import "shared/common/css/helper/index.wxss";

.order-pay {
  z-index: 2;
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 11;
  background-color: #fff;
}

.order-warning {
  width: 100%;
  padding: 10px 15px;
  font-size: 12px;
  line-height: 15px;
  color: #f60;
  background-color: #fff7cc;
  box-sizing: border-box;
}

.submit-bar {
  display: flex;
  height: 50px;
  overflow: hidden;
}

.submit-bar__content {
  padding-right: 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.submit-bar__content__price {
  font-size: 14px;
  font-weight: bold;
  color: #111;
  text-align: right;
  line-height: 1;
}

.submit-bar__content__value-card {
  font-size: 10px;
  color: #666;
  text-align: right;
  line-height: 1;
}

.submit-bar__button {
  width: 110px;
  height: 50px;
  font-size: 16px;
  line-height: 50px;
  border-radius: 0;
}

.submit-bar__button::after {
  border-color: transparent !important;
}

.disabled.submit-bar__button {
  color: #999 !important;
  background-color: #e5e5e5 !important;
}

.disabled.submit-bar__button::after {
  border-color: #e5e5e5 !important;
}

.order-pay__btn--renewal {
  display: block;
  height: 50px;
  font-size: 16px;
  line-height: 50px;
}

.use-theme-color {
  color: inherit;
  background-color: transparent;
}

.order-pay.iPhone-X {
  padding-bottom: 34px;
}