@import 'shared/common/css/helper/index.wxss';

.value-class {
  display: none;
}

.card {
  background-color: #fff !important;
}

.unavailable__tips {
  padding: 8px 15px !important;
  margin-top: 10px;
}

.unavailable__panel {
  position: relative;
  padding: 10px 15px;
  line-height: 0;
  white-space: nowrap;
  background-color: #fff;
}

.unavailable__image {
  width: 90px;
  height: 90px;
  margin-right: 5px;
}

.unavailable__action {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 80px;
  padding-right: 15px;
  font-size: 14px;
  line-height: 100px;
  color: #666;
  text-align: right;
  background-color: #fff;
}

.unavailable__action::after {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-color: #c8c8c8;
  border-style: solid;
  border-width: 2px 2px 0 0;
  content: '';
  transform: translateY(-20%) matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
}

/* 不可用商品弹窗 */
.unavailable-dialog__list {
  width: 750rpx;
  max-height: 60vh;
  min-height: 175px;
  overflow: scroll;
  background-color: #fff;
}
