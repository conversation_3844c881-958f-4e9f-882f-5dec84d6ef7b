<block wx:if="{{ formattedUnavailableGoods.length }}">
  <van-cell custom-class="unavailable__tips" title="以下商品无法一起购买，点击查看原因" value-class="value-class" />
  <view class="unavailable__panel" catchtap="showUnavailableDetail">
    <image
      wx:for="{{ formattedUnavailableGoods }}"
      wx:key="index"
      wx:item="item"
      mode="aspectFit"
      src="{{ item.url }}"
      class="unavailable__image"
    />
    <view class="unavailable__action">共{{ formattedUnavailableGoods.length }}件</view>
  </view>
</block>

<van-popup position="bottom" show="{{ showPopup }}" bind:close="hideUnavailableDetail">
  <view class="unavailable-dialog__list">
    <van-card
      wx:for="{{ formattedUnavailableGoods }}"
      wx:key="{{ index }}"
      thumb="{{ item.url }}"
      price="{{ item.payPriceStr }}"
      title="{{ item.title }}"
      title-class="van-font-14"
      num="{{ item.num }}"
      custom-class="card"
    >
      <text slot="tags" class="theme-color van-font-12">{{ item.unavailableDesc }}</text>
    </van-card>
  </view>
  <van-button
    type="danger"
    size="large"
    square
    custom-class="theme-bg-color theme-border-color"
    bind:click="hideUnavailableDetail"
  >我知道了</van-button>
</van-popup>
