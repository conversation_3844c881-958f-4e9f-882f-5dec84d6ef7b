import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapGetters(['formattedUnavailableGoods'])
  },

  data: {
    showPopup: false
  },

  methods: {
    showUnavailableDetail() {
      this.setYZData({ showPopup: true });
    },

    hideUnavailableDetail() {
      this.setYZData({ showPopup: false });
    }
  }
});
