<!-- 店铺名称 -->
<van-cell
  title="{{ shopName }}"
  custom-class="shop__name"
  border="{{ false }}"
  icon="shop-o"
/>

<!-- 循环展示商品 -->
<block wx:for="{{ formattedGoods }}">
<!-- 电子卡劵商品商品 -->
  <virtual-good
    wx:if="{{ item.isECard }}"
    wx:key="goodsId"
    good="{{ item }}"
    data-index="{{ index }}"
    bind:message="showGoodsMessage"
    bind:changeNum="onChangeNum"
    theme-color="theme-color"
    theme-border-color="theme-border-color"
  />
  <good
    wx:else
    wx:key="goodsId"
    good="{{ item }}"
    data-index="{{ index }}"
    bind:message="showGoodsMessage"
    theme-color="theme-color"
    theme-border-color="theme-border-color"
  />
</block>

<!-- 商品留言弹窗 -->
<goods-message
  theme-bg-color="theme-bg-color"
  message="{{ currentMessage }}"
  show="{{ showMessage }}"
  bind:close="handleCloseMessage"
/>

<view wx:if="{{ dataLoaded && !hasGoods }}" class="shop__empty-block van-hairline--top">
  <view class="shop__empty-icon"></view>
  <view class="shop__empty-title">没有可以购买的商品</view>
  <view class="shop__empty-desc">快买点东西犒劳自己吧</view>
  <van-button size="normal" bind:click="doReselectGoods">去逛逛</van-button>
</view>
