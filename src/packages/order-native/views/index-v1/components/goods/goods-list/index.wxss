@import 'shared/common/css/helper/index.wxss';

:host {
  display: block;
  margin-top: 10px;
  background-color: #fff;
}

.shop__name {
  padding: 8px 15px !important;
}

/*
** 可以买的商品为空时
** css
*/
.shop__empty-block {
  padding: 20px 0;
  text-align: center;
  background-color: #fff;
}

.shop__empty-icon {
  height: 80px;
  background: url('https://img01.yzcdn.cn/wsc-h5-trade/<EMAIL>') no-repeat center;
  background-size: 80px 80px;
}

.shop__empty-title {
  padding: 15px 0 5px;
  font-size: 14px;
  color: #111;
}

.shop__empty-desc {
  padding: 5px 0 15px;
  font-size: 10px;
  color: #666;
}

.shop__empty-btn {
  height: 35px;
  margin-right: 0;
  line-height: 35px;
}

.goods-presale {
  background-color: #fafafa !important;
}
