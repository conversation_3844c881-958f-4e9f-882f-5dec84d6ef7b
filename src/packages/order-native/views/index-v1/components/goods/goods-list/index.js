import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState({
      dataLoaded: state => state.dataLoaded,
      shopName: state => state.shop.shopName,
    }),
    ...mapGetters(['formattedGoods', 'hasGoods'])
  },

  data: {
    currentMessage: [], // 当前显示的商品留言
    showMessage: false // 显示商品留言
  },

  methods: {
    // 查看商品留言
    showGoodsMessage(e) {
      const { index } = e.currentTarget.dataset;

      const selectedGoodsData = this.data.formattedGoods[index];

      // 如果没有匹配到的商品信息，作为异常信息处理，跳过
      if (!selectedGoodsData) {
        return;
      }

      this.setYZData({
        currentMessage: Object.keys(selectedGoodsData.message).map(key => ({
          name: key,
          value: selectedGoodsData.message[key]
        })),
        showMessage: true
      });
    },

    // 变更商品数量
    onChangeNum(event) {
      const { index } = event.currentTarget.dataset;
      const num = event.detail;
      this.$dispatch('FETCH_SHOW', { index, num });
    },

    // 关闭商品留言
    handleCloseMessage() {
      this.setYZData({
        showMessage: false
      });
    },

    // 重新选择商品
    doReselectGoods() {
      wx.navigateBack();
    }
  }
});
