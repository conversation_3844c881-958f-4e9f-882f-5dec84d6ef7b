<van-card
  custom-class="virtual-good"
  title="{{ good.title }}"
  title-class="virtual-good__title"
  price="{{ good.payPriceStr }}"
  price-class="theme-color"
  origin-price="{{ good.PriceStr }}"
  thumb="{{ good.url }}"
  tag="{{ good.acvitityTag }}"
  num="{{ good.canChangeNum ? null : good.num }}"
>
  <view slot="desc" class="virtual-good__desc">
    <view>{{ good.desc }}</view>
    <view>{{ good.effectiveTime }}</view>
  </view>
  <view slot="tags" wx:if="{{ good.tags.length }}">
    <van-tag
      wx:for="{{ good.tags }}"
      wx:for-item="tag"
      plain
      custom-class="theme-border-color theme-color"
    >{{ tag }}</van-tag>
  </view>
  <view slot="footer" class="virtual-good__footer">
    <van-button
      wx:if="{{ good.message }}"
      size="small"
      bind:click="showGoodsMessage"
      round
    >
      查看留言
    </van-button>
  </view>
</van-card>

<van-cell wx:if="{{ good.canChangeNum }}" title="购买数量" title-width="80px">
  <view
    wx:if="{{ good.stockNum }}"
    class="virtual-good__stock"
  >
    剩余{{ good.stockNum }}件
  </view>
  <view
    wx:if="{{ good.quotaNum }}"
    class="virtual-good__quota"
  >
    每人限购{{ good.quotaNum }}件
  </view>
  <van-stepper
    value="{{ good.num }}"
    max="{{ good.maxNum }}"
    integer
    disable-input
    disabled="{{ orderCreated }}"
    async-change
    bind:change="onChange"
    custom-class="virtual-good__stepper"
    input-class="virtual-good__input"
    plus-class="virtual-good__plus"
    minus-class="virtual-good__minus"
  />
</van-cell>
