import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapGetters(['orderCreated'])
  },

  properties: {
    good: Object
  },

  methods: {
    // 查看商品留言
    showGoodsMessage() {
      this.triggerEvent('message');
    },

    // 更新商品数量
    onChange(event) {
      const { goodsId, skuId } = this.properties.good;
      const newNum = event.detail;
      this.$dispatch('FETCH_SHOW', { goodsId, skuId, num: newNum });
    }
  }
});
