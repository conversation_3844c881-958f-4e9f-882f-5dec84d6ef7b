<van-card
  title="{{ good.title }}"
  price="{{ good.payPriceStr }}"
  price-class="theme-color"
  origin-price="{{ good.PriceStr }}"
  num="{{ good.num }}"
  thumb="{{ good.url }}"
  tag="{{ good.acvitityTag }}"
  custom-class="goods-card-box"
>
  <view slot="tags" wx:if="{{ good.tags.length }}">
    <van-tag
      wx:for="{{ good.tags }}"
      wx:for-item="tag"
      wx:key="{{ tag }}"
      plain
      custom-class="theme-border-color theme-color"
    >{{ tag }}</van-tag>
  </view>

  <view slot="desc">
    <view wx:if="{{ good.desc }}"> {{ good.desc }} </view>
    <view>
      <collapse textList="{{ text }}" />
    </view>
  </view>

  <view class="goods-card__tax" slot="bottom" wx:if="{{  taxTips }}">
    {{ taxTips }}
  </view>
  
  <view slot="footer">
    <van-button
      wx:if="{{ good.message }}"
      size="small"
      bind:click="showGoodsMessage"
      round
    >
      查看留言
    </van-button>
  </view>
</van-card>
