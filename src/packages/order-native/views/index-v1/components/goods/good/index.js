import WscComponent from 'pages/common/wsc-component/index';

/** 格式化金额（支持负数） */
const formatPrice = (price) => {
  if (!price) {
    return '';
  }
  const format = (price) => {
    return `¥${(Math.abs(price) / 100).toFixed(2)}`;
  };
  // 支持负数
  return price > 0 ? `${format(price)}` : `-${format(price)}`;
};

WscComponent({
  externalClasses: [
    'theme-border-color',
    'theme-text-color',
    'theme-bg-color',
    'theme-color',
  ],

  properties: {
    good: {
      type: Object,
      observer(val) {
        console.log('商品套餐信息将在这里出现--未付款', val);
        const { goodsTax = 0, postageTax = 0, showTax, num, haitao = 0 } = val;

        const songoods = val?.combo?.subComboList
          ?.map(({ num, title, sku, properties, addPrice }) => {
            let propsDesc;
            if (properties) {
              propsDesc = properties
                ?.map((prop) => {
                  return prop?.propValueList?.map((propValue) => {
                    return `${propValue?.propValueName}${formatPrice(
                      propValue?.price
                    )}`;
                  });
                })
                .flat()
                .filter((x) => x)
                .join(';');
            }
            if (sku || propsDesc?.length) {
              if (sku && propsDesc?.length) {
                return `${title}（${sku}${formatPrice(
                  addPrice
                )};${propsDesc}）x${num}`;
              }
              if (sku && !propsDesc?.length) {
                return `${title}（${sku}${formatPrice(addPrice)}）x${num}`;
              }
              if (!sku && propsDesc?.length) {
                return `${title}（${propsDesc}）x${num}`;
              }
            }
            return `${title} x${num}`;
          })
          .filter((x) => x);

        this.setYZData({
          text: songoods,
        });

        let msg = '';

        if (+haitao !== 1) {
          msg = '';
        } else if (!showTax) {
          msg = '进口税(含运费税款)：商品已含税';
        } else {
          const taxPrice = goodsTax * num + postageTax;
          msg = `进口税(含运费税款)：¥ ${(taxPrice / 100).toFixed(2)}`;
        }

        this.setYZData({
          taxTips: msg,
        });
      },
    },
  },

  data: {
    taxTips: '',
    text: ['111', '222'],
  },

  methods: {
    // 查看商品留言
    showGoodsMessage() {
      this.triggerEvent('message');
    },
  },
});
