<van-action-sheet show="{{ show }}" title="商品留言" bind:close="handleClose">
  <scroll-view scroll-y class="dialog__content">
    <van-cell-group>
      <van-cell
        wx:for="{{ formatedMessage }}"
        wx:key="{{ index }}"
        title="{{ item.name }}"
        title-width="75px"
      >
        <image
          wx:if="{{ item.type === 'image' }}"
          src="{{ item.preview }}"
          mode="aspectFit"
          catchtap="previewImg"
          data-src="{{ item.value }}"
          style="width: 70px; height: 70px;"
        />
        <text wx:else>{{ item.value || '无' }}</text>
      </van-cell>
    </van-cell-group>
  </scroll-view>
  <van-button bind:click="handleClose" square size="large" custom-class="theme-bg-color">关闭</van-button>
</van-action-sheet>
