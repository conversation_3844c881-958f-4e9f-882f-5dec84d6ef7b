import WscComponent from 'pages/common/wsc-component/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

WscComponent({
  externalClasses: ['theme-border-color', 'theme-text-color', 'theme-bg-color', 'theme-color'],

  properties: {
    message: {
      type: Array,
      value: [],
      observer(value) {
        // ?? ob obj
        this.setYZData({
          formatedMessage: this.formatMessage(value)
        });
      }
    },
    show: Boolean
  },

  data: {
    formatedMessage: [] // 留言列表
  },

  methods: {
    // 关闭弹窗
    handleClose() {
      this.triggerEvent('close');
    },

    // 处理留言列表 区分图片和文本
    formatMessage(messages = []) {
      messages.forEach(item => {
        const value = item.value;
        const type = /^\s*http(s)*:\/\/.+/.test(value) ? 'image' : 'text';
        item.type = type;

        if (type == 'image') {
          item.preview = cdnImage(value, '!200x200.jpg');
        }
      });

      return messages;
    },

    // 预览图片
    previewImg(e) {
      const src = e.currentTarget.dataset.src;

      wx.previewImage({
        current: src,
        urls: [src]
      });
    }
  }
});
