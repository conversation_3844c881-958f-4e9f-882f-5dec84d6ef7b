import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  externalClasses: [
    'theme-border-color',
    'theme-text-color',
    'theme-bg-color',
    'theme-color'
  ],

  store,

  mapData: {
    ...mapState([
      'componentExtUmp',
      'extensionConfig',
      'order',
      'componentExtCoupon',
      'componentExtValueCard',
    ])
  }
});
