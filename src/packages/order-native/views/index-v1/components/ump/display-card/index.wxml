<view wx:if="{{ hasDisplayCard }}" class="goods-membercard">
  <view class="content" bind:tap="onToggle">
    <image class="badge" src="https://b.yzcdn.cn/wsc-h5-trade/vip-badge-1104.png" />
    <view class="title">
        {{ util.vipcardBenefitInfo(displayCard) }}
    </view>
    <view class="desc">
        {{ util.vipcardDescInfo(displayCard) }}
    </view>
    <theme-view wx:if="{{ !orderCreated }}" color="{{ display.openDisplayCard ? 'main-bg' : '' }}" custom-class="check">
      <van-icon name="{{ display.openDisplayCard ? 'checked' : 'circle' }}" />
    </theme-view>
  </view>
</view>

<wxs module="util">
  module.exports.money = function (value) {
    var yuan = value / 100;
    return yuan.toFixed(2);
  }

  module.exports.vipcardBenefitInfo = function (data) {
    if (data.vipcardBenefitInfo) {
      return data.vipcardBenefitInfo;
    }
    var price = data.totalRecommendedDiscountPrice / 100;
    return '现在勾选开卡，立减 ' + price.toFixed(2) + ' 元';
  }

  module.exports.vipcardDescInfo = function (data) {
    if (data.vipcardDescInfo) {
      return data.vipcardDescInfo;
    }
    var formattedPrice = data.price / 100;
    return data.name + '仅需 ' + formattedPrice.toFixed(2) + ' 元';
  }
</wxs>
