import { VanxComponent } from 'pages/common/wsc-component';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store';

VanxComponent({
  store,

  mapData: {
    ...mapState(['displayCard', 'display']),
    ...mapGetters(['orderCreated', 'hasDisplayCard'])
  },

  data: {
    showPopup: false
  },

  methods: {
    onToggle() {
      const { orderCreated, display } = this.data;

      if (!orderCreated) {
        const { openDisplayCard } = display;
        this.$commit('SET_DISPLAY_CARD', !openDisplayCard);

        if (!openDisplayCard === true) {
          // NOTE: 选中展示卡时需要重置会员优惠，由后端计算使用哪个优惠
          this.$commit('SET_CUSTOM_CARD', null);
        }

        this.$dispatch('FETCH_SHOW').catch(() => {
          // confirm 接口失败时重置 openDisplayCard
          this.$commit('SET_DISPLAY_CARD', openDisplayCard);
        });
      }
    }
  }
});
