@import 'helpers/index.wxss';

.goods-membercard {
  position: relative;
  padding: 0 16px 12px;
  background-color: #fff;
}

.content {
  position: relative;
  padding: 12px 12px 12px 48px;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: 4px;
}

.badge {
  position: absolute;
  top: 14px;
  left: 12px;
  width: 28px;
  height: 16px;
}

.title {
  color: #323233;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 4px;
}

.desc {
  color: rgba(50, 50, 51, .6);
  font-size: 12px;
  line-height: 16px;
}

.discount-money {
  display: inline-block;
  margin: 0 2px;
}

.check {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
}
