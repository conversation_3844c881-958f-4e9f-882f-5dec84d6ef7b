.value-class {
  flex: 2;
  display: flex;
  justify-content: flex-end;
  color: #111 !important;
}

.name {
  color: #111;
  font-size: 14px;
  line-height: 20px;
}

.title {
  display: inline-block;
  vertical-align: middle;
}

.info-icon {
  vertical-align: middle;
  padding: 4px;
}

.value {
  display: flex;
  justify-content: flex-end;
  flex: 2;
}

.switch {
  margin: 2px 0 0 8px;
}

.vip-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 4px;
  vertical-align: middle;
}

.tips-dialog {
  width: 310px !important;
  padding: 0 20px;
  box-sizing: border-box;

  &__title {
    font-size: 16px;
    font-weight: 500;
    line-height: 44px;
    margin-bottom: 20px;
    text-align: center;
  }

  &__content {
    font-size: 14px;
    line-height: 20px;
  }

  &__button {
    height: 36px !important;
    line-height: 34px !important;
    margin: 27px 0 19px;
    border: none;
    background: linear-gradient(to right, #ff6034, #ee0a24);
  }
}
