<van-cell
  wx:if="{{ hasCustomerCards || hasDisplayCard || hasUnavailableCustomerCards}}"
  border="{{ false }}"
  value-class="value-class"
  is-link="{{ canLink }}"
  bind:click="onClickCell"
>
  <view slot="title">
    <text class="title">会员优惠</text>
    <van-icon
      name="info-o"
      size="18px"
      color="#969799"
      class="info-icon"
      catch:tap="onShowTips"
    />
  </view>

  <view class="name">
    <image
      wx:if="{{ displayCustomerCard.id }}"
      class="vip-icon"
      src="https://img01.yzcdn.cn/wsc-h5-trade/membership-logo-1104.png"
    />
    <view class="title">{{ membershipCellTitle }}</view>
  </view>

  <van-switch
    wx:if="{{ hasOneCustomerCards  && !hasUnavailableCustomerCards}}"
    checked="{{ membershipSwitchChecked }}"
    class="switch"
    size="18px"
    active-color="#ee0a24"
    bind:change="onChangeSwitch"
  />
</van-cell>

<!-- 店铺活动浮层 -->
<membership-dialog
  theme-color="theme-color"
  theme-button="theme-button"
  show="{{ showMembership }}"
  bind:close="hideDialog"
/>

<display-card />

<van-popup
  show="{{ showTips }}"
  round
  custom-class="tips-dialog"
  bind:close="onHideTips"
>
  <view class="tips-dialog__title">
    会员优惠说明
  </view>
  <view class="tips-dialog__content">
    1、会员优惠价仅对商家设置了会员优惠的商品生效，普通商品不享受；
  </view>
  <view class="tips-dialog__content">
    2、当会员折扣商品参与限时折扣时，系统将选择其中较低的价格下单结算；
  </view>
  <view class="tips-dialog__content">
    3、会员包邮为订单包邮，当订单中有砍价、秒杀、拼团、优惠套餐活动时，会员包邮失效。
  </view>
  <van-button
    round
    block
    type="danger"
    custom-class="tips-dialog__button"
    bind:click="onHideTips"
  >
    知道了
  </van-button>
</van-popup>
