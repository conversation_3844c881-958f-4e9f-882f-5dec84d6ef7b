import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['customerCards', 'unavailableCustomerCards', 'display']),
    ...mapGetters([
      'chosenCustomCard',
      'hasDisplayCard',
      'orderCreated',
      'hasCustomerCards',
      'hasUnavailableCustomerCards',
      'hasOneCustomerCards',
      'displayCustomerCard',
      'membershipCellTitle',
      'membershipSwitchChecked',
    ]),
    ...mapState({
      canLink: (state) => {
        return (
          state.customerCards.list.length
            + state.unavailableCustomerCards.list.length > 1
          || (state.customerCards.list.length
            + state.unavailableCustomerCards.list.length === 1
            && state.unavailableCustomerCards.list.length === 1)
        );
      },
    }),
  },

  data: {
    showTips: false,
    showMembership: false,
  },

  methods: {
    showDialog() {
      this.setYZData({ showMembership: true });
    },

    hideDialog() {
      this.setYZData({ showMembership: false });
    },

    onShowTips() {
      this.setYZData({ showTips: true });
    },

    onHideTips() {
      this.setYZData({ showTips: false });
    },

    onClickCell() {
      if (this.data.canLink) {
        this.showDialog();
      }
    },

    onChangeSwitch(event) {
      if (this.data.orderCreated) {
        return;
      }

      const value = event.detail;
      const id = value ? this.data.customerCards.list[0].id : '';

      this.$commit('SET_CUSTOM_CARD', id);

      // 如果有展示卡，取消选中展示卡
      if (value && this.data.hasDisplayCard) {
        this.$commit('SET_DISPLAY_CARD', false);
      }

      this.$dispatch('FETCH_SHOW', { setCustomerCard: true });
    },
  },
});
