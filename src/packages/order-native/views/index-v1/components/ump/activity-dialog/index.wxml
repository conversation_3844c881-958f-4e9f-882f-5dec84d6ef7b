<van-action-sheet show="{{ show }}" title="店铺活动" bind:close="onClose">
  <view class="activity-dialog__content">
    <van-cell
      wx:for="{{ formatedActivities.list }}"
      wx:key="index"
      title="{{ item.name !== item.title ? item.title : '' }}"
      custom-class="activity-dialog__cell"
      title-class="activity-dialog__cell__title"
    >
      <van-tag
        plain
        type="danger"
        size="small"
        slot="icon"
      >
        {{ item.name }}
      </van-tag>
      <view
        wx:if="{{ item.value }}"
        class="activity-dialog__cell__decrease"
      >
        - ¥ {{ item.priceStr }}
      </view>
    </van-cell>
    <view
      wx:if="{{ formatedActivities.decrease }}"
      class="activity-dialog__price"
    >
      合计：省 <text class="theme-color bold">¥ {{ formatedActivities.decreaseStr }}</text>
    </view>
  </view>
  <van-button
    square
    size="large"
    custom-class="theme-button"
    bind:click="onClose"
  >
    确定
  </van-button>
</van-action-sheet>
