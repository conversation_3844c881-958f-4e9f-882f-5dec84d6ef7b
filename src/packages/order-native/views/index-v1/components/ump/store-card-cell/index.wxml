<view
  class="store-card-wrapper"
  wx:if="{{ storeCardCell }}"
>
  <van-cell
    border="{{ false }}"
    custom-class="store-card-cell"
    value-class="store-card-cell__value"
  >
    <view slot="title">
      <view class="store-card-cell__label">返储值金额<van-icon name="info-o" custom-class="store-card-icon" bind:click="onClick"/></view>
    </view>
    <view>
      <text>最多可返 ¥{{ money }}</text>
    </view>
  </van-cell>

  <van-popup
    show="{{ showTipCell }}"
    round
    custom-class="store-card-popup"
    bind:close="onClose"
  >
    <view class="store-card-popup__title">返储值金说明</view>
    <view class="store-card-popup__content">
      <text
       wx:for="{{ rebateMessageList }}"
       wx:for-item="item"
       wx:for-index="index"
       class="store-card-popup__item"
      >
      {{ index+1 }}. {{item}}
     </text>
    </view>
    <theme-view
      gradient
      gradientDeg="90"
      color="main-text"
      custom-class="store-card-popup__botton"
      bindtap="onClose"
    >
     <text>知道了</text>
    </theme-view>
  </van-popup>
</view>

