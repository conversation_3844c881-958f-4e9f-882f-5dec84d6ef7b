import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapGetters, mapState } from '@youzan/vanx';
import Toast from '@vant/weapp/dist/toast/toast';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,
  mapData: {
    ...mapGetters(['storeCardCell']),
    ...mapState(['rebateMessageList'])
  },
  data: {
    showTipCell: false,
    money: 0,
  },

  ready() {
    const { storeCardCell } = this.data;
    if (storeCardCell) {
      const { value = 100 } = storeCardCell;
      this.setYZData({
        money: (value / 100).toFixed(2),
      });
    }
  },

  methods: {
    onClose() {
      this.setYZData({ showTipCell: false });
    },
    onClick() {
      Toast.loading();
      this.$dispatch('FETCH_REBATE_MESSAGE').then(() => {
        Toast.clear();
        this.setYZData({ showTipCell: true });
      });
    }
  }
});
