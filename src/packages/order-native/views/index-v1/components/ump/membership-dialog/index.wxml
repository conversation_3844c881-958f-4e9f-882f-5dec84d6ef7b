<van-action-sheet show="{{ show }}" title="选择会员优惠" bind:close="onClose">
  <view class="content">
    <view wx:if="{{ display.openDisplayCard }}" class="tips">
      当前已勾选购买付费卡，若选择使用已有会员优惠则将会取消购买付费卡。
    </view>
    <van-radio-group value="{{ customerCards.id }}" bind:change="onChange">
      <van-cell
        wx:for="{{ customerCards.list }}"
        wx:key="id"
        center
        clickable
        title-class="title"
        custom-class="cell"
        data-id="{{ item.id }}"
        bind:click="onSelect"
      >
        <view slot="title">
          {{ item.name }}
          <van-tag wx:if="{{ index === 0 }}" round type="danger" class="tag">推荐</van-tag>
          <view class="label">{{ item.desc }}</view>
        </view>
        <van-radio
          name="{{ item.id }}"
          slot="right-icon"
          icon-class="theme-color"
        />
      </van-cell>
      <view wx:if="{{ unavailableCustomerCards.list.length}}">
        <view class="membership-dialog--disabled__title--tip">以下权益卡本店不可用</view>
        <van-cell
          wx:for="{{ unavailableCustomerCards.list }}"
          wx:key="id"
          center
          title-class="title-disabled"
          custom-class="cell"
          data-id="{{ item.id }}"
        >
          <view slot="title">
            {{ item.name }}
            <view class="label--disabled ">{{ item.desc }}</view>
          </view>
          <van-radio
            slot="right-icon"
            disabled="{{ true }}"
          />
        </van-cell>
      </view>
    </van-radio-group>
  </view>
  <view class="close">
    <van-button
      round
      size="large"
      data-id=""
      custom-class="close-button"
      bind:click="onSelect"
    >
      不使用会员优惠
    </van-button>
  </view>
</van-action-sheet>
