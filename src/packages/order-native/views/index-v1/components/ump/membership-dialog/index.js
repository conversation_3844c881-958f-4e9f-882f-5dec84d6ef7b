import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['display', 'customerCards', 'unavailableCustomerCards']),
  },

  properties: {
    show: Boolean,
  },

  methods: {
    onSelect(event) {
      const { state, getters } = this.$store;
      const prevId = state.customerCards.id;
      const { id } = event.currentTarget.dataset;

      if (getters.orderCreated || prevId === id) {
        this.onClose();
        return;
      }

      this.$commit('SET_CUSTOM_CARD', id);

      // NOTE: 如果有展示卡，强制不选中展示卡
      if (id && getters.hasDisplayCard) {
        this.$commit('SET_DISPLAY_CARD', false);
      }

      this.$dispatch('FETCH_SHOW', { setCustomerCard: true })
        .then(() => {
          this.onClose();
        })
        .catch(() => {
          this.$commit('SET_CUSTOM_CARD', prevId);
        });
    },

    onClose() {
      this.triggerEvent('close');
    },
  },
});
