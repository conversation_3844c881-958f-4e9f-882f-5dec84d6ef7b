@import '~themes/order-theme.scss';

.content {
  width: 100%;
  height: 386px;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #fff;
}

.label {
  color: #969799;
  font-size: 14px;
  line-height: 20px;
  margin-top: 8px;
}

.label--disabled {
  color: #c8c9cc;
  font-size: 14px;
  line-height: 20px;
  margin-top: 8px;
}

.title {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
}

.title-disabled {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #c8c9cc;
}

.tag {
  margin-left: 8px;
}

.cell {
  padding: 16px !important;
}

.tips {
  padding: 8px 16px;
  color: #ed6a0c;
  font-size: 13px;
  line-height: 18px;
  background-color: #fffbe8;
}

.membership-dialog--disabled__title--tip {
  font-size: 14px;
  margin: 8px 12px;
  margin-top: 20px;
  color: #969799;
}

.close {
  padding: 5px 16px;
}

.close-button {
  height: 40px !important;
  line-height: 38px !important;
}
