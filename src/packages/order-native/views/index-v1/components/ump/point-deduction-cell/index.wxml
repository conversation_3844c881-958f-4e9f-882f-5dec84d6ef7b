<van-cell
  wx:if="{{ !!pointDesc && !orderCreated }}"
  title="{{ pointsName }}"
  class="point-deduction"
  value-class="point-deduction__value"
  title-class="point-deduction__title"
>
  {{ pointDesc }}
  <van-icon
    class="point-deduction__question"
    name="question-o"
    size="14px"
    color="#3388FF"
    data-show-prompt="{{ true }}"
    bind:click="setShowPrompt"
  />

  <van-switch
    wx:if="{{ pointDeduction.status === 3 }}"
    class="point-deduction__switch"
    size="20px"
    checked="{{ pointDeduction.isUse }}"
    bind:change="onSwitchChange"
  />

  <van-popup
    show="{{ isShowPrompt }}"
    position="bottom"
    class="point-deduction__popup"
    data-show-prompt="{{ false }}"
    bind:click-overlay="setShowPrompt"
  >
    <view class="point-deduction__popup__content">
      <view class="point-deduction__popup__title">使用条件：</view>
      <view>
        <view wx:for="{{ useConditions }}" wx:key="{{ item }}">
          {{ item }}
        </view>
      </view>
      <view class="point-deduction__popup__title">使用数量：</view>
      <view>
        <view>1、使用{{ pointsName }}数量为{{ pointDeduction.rate }}的整数倍</view>
        <view>2、每{{ pointDeduction.rate }}{{ pointsName }}抵1元</view>
      </view>
    </view>

    <van-button
      size="large"
      square
      type="danger"
      data-show-prompt="{{ false }}"
      bind:click="setShowPrompt"
    >
      我知道了
    </van-button>
  </van-popup>
</van-cell>