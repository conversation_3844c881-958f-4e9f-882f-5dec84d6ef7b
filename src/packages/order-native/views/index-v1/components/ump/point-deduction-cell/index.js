import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['pointDeduction', 'pointsName']),
    ...mapGetters(['orderCreated', 'useConditions', 'pointDesc'])
  },

  data: {
    showActivityDetail: false,
    isShowPrompt: ''
  },

  methods: {
    setShowPrompt({ currentTarget: { dataset: { showPrompt } } }) {
      this.setData({
        isShowPrompt: showPrompt
      });
    },
    onSwitchChange({ detail }) {
      this.$dispatch('USE_POINT_DEDUCTION', !!detail);
    }
  }
});
