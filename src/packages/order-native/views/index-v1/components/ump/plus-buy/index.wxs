function show(display, plusBuy) {
  if (display == null || plusBuy == null) {
    return false;
  }

  const showPlusBuyComponent = display.plusBuyComponent && display.plusBuyComponent.showPlusBuyComponent;
  const hasExchangeGoodsList = plusBuy.exchangeGoodsList && plusBuy.exchangeGoodsList.length;
  return showPlusBuyComponent && hasExchangeGoodsList;
}

function selected(goods, plusBuy) {
  if (goods == null || plusBuy == null) {
    return [];
  }

  return (
    goods.list.filter(item => plusBuy.selected.some(v => item.fromTmpAdded && v.goodsId === item.goodsId)) || []
  );
}

function selectedCount(goods, plusBuy) {
  return selected(goods, plusBuy).length;
}

function payPrice(goods, plusBuy) {
  const selectedItems = selected(goods, plusBuy);
  const price = selectedItems.reduce((prev, item) => prev + item.payPrice, 0) / 100;
  return price.toFixed(2);
}

module.exports = {
  show,
  selectedCount,
  payPrice
};
