<wxs src="./index.wxs" module="utils" />

<view wx:if="{{ utils.show(display, plusBuy) }}" class="plus-buy">
  <van-cell
    title="优惠换购"
    value="{{ plusBuy.supportMulti ? '（可多选）' : '（可选1件）' }}"
    title-class="plus-buy__cell-title-class"
    value-class="plus-buy__cell-value-class"
    border="{{ false }}"
  />
  <view class="plus-buy__card-list">
    <card
      wx:for="{{ plusBuyGoodsList }}"
      wx:key="{{ item.goodsId }}"
      class="plus-buy__card"
      data="{{ item }}"
    />
  </view>

  <view wx:if="{{ utils.selectedCount(goods, plusBuy) }}" class="plus-buy__price">
    <view class="plus-buy__price-count">
      共{{ utils.selectedCount(goods, plusBuy) }}件
    </view>
    <view class="plus-buy__price-prefix">
      小计：
    </view>
    <theme-view color="main-bg">
      ¥ {{ utils.payPrice(goods, plusBuy) }}
    </theme-view>
  </view>
</view>
