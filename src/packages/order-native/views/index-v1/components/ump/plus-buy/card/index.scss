.component {
  position: relative;

  .card {
    width: 220px;
    height: 84px;
    padding: 8px;
    display: flex;
    box-sizing: border-box;
    background: #f7f8fa;
    border-radius: 4px;

    &__thumb {
      width: 64px;
      height: 64px;
      flex: none;
      margin-right: 8px;
    }

    &__content {
      flex: 1;
      width: 0;
      min-height: 0;
      padding-right: 20px;
      display: flex;
      flex-direction: column;
    }

    &__title {
      font-size: 12px;
      color: #323233;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &__desc {
      font-size: 12px;
      color: #ababab;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &__price {
      flex: 1;
      display: flex;
      align-items: flex-end;
      font-size: 14px;
      color: #f44;
      line-height: 20px;
    }

    &__origin-price {
      margin-left: 5px;
      color: #7d7e80;
      font-size: 10px;
      text-decoration: line-through;
    }
  }
}

.checkbox {
  position: absolute;
  top: 8px;
  right: 8px;
}
