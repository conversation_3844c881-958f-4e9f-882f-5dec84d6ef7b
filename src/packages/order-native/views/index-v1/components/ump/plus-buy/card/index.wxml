<view class="component">
  <view class="card" bindtap="onClickCard">
    <image class="card__thumb" src="{{ data.picture }}" />
    <view class="card__content">
      <view class="card__title">{{ data.title }}</view>
      <view wx:if="{{ data.desc }}" class="card__desc">{{ data.desc }}</view>
      <view class="card__price">
        ¥ {{ data.price }}
        <view class="card__origin-price">¥ {{ data.originalPrice }}</view>
      </view>
    </view>
  </view>

  <van-checkbox
    value="{{ checked }}"
    class="checkbox"
    icon-size="18px"
    bind:change="onClickCard"
  />

  <base-sku
    theme-class="{{ themeClass }}"
    goods="{{ { title: data.title, picture: data.picture } }}"
    sku="{{ sku }}"
    properties="{{ goodsProperties }}"
    show="{{ showSku }}"
    buy-text="确定"
    quota="1"
    show-add-cart-btn="{{ false }}"
    reset-stepper-on-hide="{{ true }}"
    generic:sku-header-price="custom-sku-header-price"
    hide-stock="{{ sku && !!sku.hideStock }}"
    extra-data="{{ skuExtraData }}"
    show-add-cart-btn="{{ false }}"
    show-buy-btn
    bind:buy="onBuyClicked"
    bind:sku-close="onSkuClose"
  />
</view>
