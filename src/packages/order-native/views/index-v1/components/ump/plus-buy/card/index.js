import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  properties: {
    data: Object
  },

  mapData: {
    ...mapState(['order', 'plusBuy', 'goods', 'display'])
  },

  watch: {
    'data.plusBuy': function () {
      const { plusBuy = {}, data } = this.data;
      const checked = (plusBuy.selected || []).some((item) => +data.goodsId === +item.goodsId);

      this.setYZData({ checked });
    }
  },

  data: {
    goodsProperties: [],
    skuExtraData: {
      useCustomHeaderPrice: true
    }
  },

  methods: {
    getSku() {
      this.loading = true;
      this.$dispatch('LOADING');

      const { data } = this.data;

      const fetchPromise =
        this.originSku == null
          ? this.$dispatch('FETCH_SKU_DATA', data.goodsAlias)
          : Promise.resolve(this.originSku);

      return fetchPromise
        .then((originSku) => {
          this.originSku = originSku;
          const goodsProperties = originSku.itemSalePropList || [];
          this.loading = false;
          this.$dispatch('HIDE_TOAST');

          const { exchangeSkuList = [], exchangePrice, originalPrice } = this.data.data;
          const sku = {
            ...originSku,
            // 替换原价与换购价
            price: (exchangePrice / 100).toFixed(2),
            origin_price: (originalPrice / 100).toFixed(2),
            // 筛选出可换购的sku 并 替换sku价格
            list: (originSku.list || [])
              .filter((item) => exchangeSkuList.some((sku) => +sku.skuId === +item.id))
              .map((item) => {
                const exchangeSku = exchangeSkuList.find((sku) => +sku.skuId === +item.id);
                item.origin_price = item.price;
                item.price = exchangeSku.exchangePrice;
                return item;
              })
          };

          this.setYZData({ sku, goodsProperties });

          return sku;
        })
        .catch((err) => {
          this.loading = false;
          this.$dispatch('HIDE_TOAST');
          throw err;
        });
    },

    onClickCard() {
      if (this.loading) {
        return;
      }

      if (this.data.checked) {
        this.$commit('SWITCH_PLUS_BUY_GOODS', this.data.data);
        this.$dispatch('FETCH_SHOW').catch(() => {
          this.$commit('SWITCH_PLUS_BUY_GOODS', this.data.data);
        });
        return;
      }

      this.getSku().then((sku) => {
        if (
          sku.noneSku &&
          !(Array.isArray(sku.messages) && sku.messages.length) &&
          !(Array.isArray(sku.itemSalePropList) && sku.itemSalePropList.length)
        ) {
          const data = {
            num: 1,
            skuId: sku.collectionId,
            ...this.getPlusBuyItemData()
          };
          this.$commit('SWITCH_PLUS_BUY_GOODS', data);
          this.$dispatch('FETCH_SHOW').catch(() => {
            this.$commit('SWITCH_PLUS_BUY_GOODS', data);
          });
        } else {
          this.setYZData({ showSku: true });
        }
      });
    },

    getPropertiesIds(properties = []) {
      const propertiesIds = [];
      properties.forEach((property) => {
        (property.v || []).forEach((value) => {
          propertiesIds.push(value.id);
        });
      });

      return propertiesIds;
    },

    onBuyClicked(event) {
      const data = event.detail;
      const sku = this.data.sku;
      console.log('data', data);
      console.log('sku', this.data.sku);
      const { selectedSkuComb } = data;
      this.onSkuClose();

      const messagesValues = Object.keys(data.messages).map((key) => data.messages[key]);
      const messages = {};
      (sku.messages || []).forEach((message, index) => {
        messages[message.name] = messagesValues[index];
      });

      const switchData = {
        num: data.selectedNum,
        skuId: selectedSkuComb.id,
        propertyIds: this.getPropertiesIds(selectedSkuComb.properties),
        itemMessage: JSON.stringify(messages),
        ...this.getPlusBuyItemData()
      };
      this.$commit('SWITCH_PLUS_BUY_GOODS', switchData);
      this.$dispatch('FETCH_SHOW').catch(() => {
        this.$commit('SWITCH_PLUS_BUY_GOODS', switchData);
      });
    },

    onSkuClose() {
      this.setYZData({ showSku: false });
    },

    getPlusBuyItemData() {
      const { data, display, order } = this.data;
      const { seller } = order;
      return {
        goodsId: data.goodsId,
        kdtId: seller.kdtId,
        storeId: seller.storeId,
        activityId: display.plusBuyComponent.activityId,
        activityType: 24,
        extensions: {
          fromTmpAdd: '1'
        }
      };
    }
  }
});
