import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import pick from '@youzan/weapp-utils/lib/pick';
import get from '@youzan/weapp-utils/lib/get';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import config from '@/packages/order-native/common/config';

const app = getApp();

VanxComponent({
  store,

  data: {
    oldChosenCoupon: null // 保存 上一个选中的优惠券，防止选中会员 等活动 导致优惠券失效，上一个选中的券为 {}
  },

  mapData: {
    ...mapState(['coupon', 'display', 'order']),
    ...mapGetters([
      'chosenCoupon',
      'hasGoods',
      'orderCreated',
      'allCoupons',
      'showPrepayCardCell'
    ])
  },

  ready() {
    // 订阅选择优惠券
    this.on(
      config.eventKey.couponChange,
      (coupon = {}) => {
        const { newCouponProcess } = this.data.order;
        coupon = mapKeysCase.toCamelCase(coupon);
        const { allCoupons = [] } = this.$store.getters;
        const oldChosenCoupon = this.data.oldChosenCoupon || this.data.chosenCoupon;
        if ((isEmpty(coupon) && isEmpty(oldChosenCoupon)) || (get(coupon, 'id') === get(oldChosenCoupon, 'id'))) {
          return;
        }

        // 不使用优惠券
        if (isEmpty(coupon) && !isEmpty(oldChosenCoupon)) {
          this.$commit('SET_CHOSEN_COUPON_ID', 0);
          this.data.oldChosenCoupon = null;

          if (newCouponProcess) {
            return this.$dispatch('FETCH_SHOW', {
              isInitCoupon: false
            });
          }
        } else {
          const existed = allCoupons.some(item => item.id === coupon.id);
          // 添加优惠券
          if (!existed) {
            this.$commit('ADD_COUPON', coupon);
          }
          this.$commit('SET_CHOSEN_COUPON_ID', coupon.id);
          this.data.oldChosenCoupon = coupon;
        }

        if (
          (coupon.groupType === 13 || oldChosenCoupon.groupType === 13)
          && coupon.id !== oldChosenCoupon.id
        ) {
          // 商品兑换券切换，请求 confirm
          // 使用 setTimeout 解决在兑换券列表 调用 navigateBack 之后 loading toast 消失
          setTimeout(() => {
            this.$dispatch('FETCH_SHOW', {
              isInitCoupon: false
            });
          }, 500);
        } else if (newCouponProcess && coupon.id !== oldChosenCoupon.id) {
          this.$dispatch('FETCH_SHOW', {
            isInitCoupon: false
          });
        }
      }
    );
  },
  methods: {
    // 点击跳转到优惠券列表
    showCouponList() {
      this.$dispatch('NAVIGATE_COUPON_LIST');
    },
  },
});
