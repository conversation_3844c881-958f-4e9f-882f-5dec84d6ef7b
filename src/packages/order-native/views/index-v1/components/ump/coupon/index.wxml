<!-- 订单页外部展示条 -->
<van-cell
  wx:if="{{ hasGoods && !display.forbidCoupon && !(orderCreated && !chosenCoupon.id) }}"
  is-link="{{ !orderCreated }}"
  bind:click="showCouponList"
  title="优惠"
  border="{{ showPrepayCardCell }}"
  title-width="75px"
  center
>
  <view class="value-class">
    <text wx:if="{{ allCoupons.length === 0 }}">暂无可用</text>
    <text wx:elif="{{ !chosenCoupon.id }}">{{ allCoupons.length }}张可用</text>
    <block wx:elif="{{ chosenCoupon.id }}">
      <view wx:if="{{ order.newCouponProcess }}">-¥{{ toYuan(chosenCoupon.value || chosenCoupon.denominations) }}</view>
      <view wx:else>-¥{{ toYuan(chosenCoupon.denominations || chosenCoupon.value) }}</view>
    </block>
  </view>
</van-cell>

<wxs module="toYuan">
module.exports = function (value) {
  var yuan = value / 100;
  return yuan.toFixed(2);
}
</wxs>
