import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

const TAG_FLAG = 'show_prepay_card_tip';

VanxComponent({
  store,

  mapData: {
    ...mapState([
      'valueCard',
      'recommendDetaid',
      'payAssetActivityTagDesc',
      'display',
      'showValueCard',
    ]),
    ...mapGetters([
      'orderCreated',
      'prepayCardCellValue',
      'showPrepayCardCell',
      'storeCardCell',
      'prepayCardTotalAmount',
      'hasExclusionCard'
    ])
  },

  data: {
    showTipCell: false
  },

  ready() {
    const { showPrepayCardCell } = this.data;

    try {
      if (showPrepayCardCell) {
        wx.getStorage({
          key: TAG_FLAG,
          // 第一次感知到储值卡
          fail: () => {
            // 等待300ms页面其他组件布局完成, 减少定位误差
            setTimeout(() => {
              this.createSelectorQuery()
                .select('#prepayCardCell')
                .boundingClientRect((res) => {
                  const { top } = res;

                  if (top) {
                    wx.pageScrollTo({
                      scrollTop: top,
                      duration: 300,
                      success: () => {
                        this.setYZData({ showTipCell: true });
                      }
                    });
                  }
                })
                .exec();
            }, 300);
          }
        });
      }
    } catch (err) {
      console.log(err);
    }
  },

  methods: {
    hasExclusionCard() {
      // 如果互斥卡存在状态与选卡前不同，重新请求FETCH_SHOW
      this.$commit('SET_ORDER_FORBID_COUPON', this.data.hasExclusionCard);
      this.$commit('SET_ORDER_FORBID_PREFERENCE', this.data.hasExclusionCard);
      this.$dispatch('FETCH_SHOW');
    },

    onConfirm(data) {
      const {
        detail: { valueCard, exclusionCard }
      } = data;
      this.$commit('SET_PREPAY_CARD_CHECK_STATUS', { valueCard });
      this.$commit('SET_SHOW_VALUE_CARD', false);
      if (exclusionCard) {
        this.hasExclusionCard();
      }
    },

    onRefreshData() {
      this.$dispatch('FETCH_SHOW');
    },

    onTipCellClick() {
      if (this.data.showTipCell) {
        this.setYZData({ showTipCell: false });

        wx.setStorage({
          key: TAG_FLAG,
          data: true
        });
      }
    },

    onClick() {
      this.$dispatch('OPEN_VALUE_CARD');
    },

    onClose() {
      this.$commit('SET_SHOW_VALUE_CARD', false);
    },
  },
});
