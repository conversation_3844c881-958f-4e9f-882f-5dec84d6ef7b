<view id="prepayCardCell" class="pre-pay-card-wrapper" wx:if="{{ showPrepayCardCell }}">
  <prepaid-recommend
    show="{{ showValueCard }}"
    value-card="{{ valueCard }}"
    recommend-detaid="{{ recommendDetaid }}"
    has-exclusion-card="{{ hasExclusionCard }}"
    pay-asset-activity-tag-desc="{{payAssetActivityTagDesc}}"
    new-recommend="{{display ? display.newRecommend : false}}"
    bind:onConfirm="onConfirm"
    bind:onClose="onClose"
    bind:onRefreshData="onRefreshData"
  />
  <van-cell
    border="{{ storeCardCell }}"
    center
    value="{{ prepayCardCellValue.text }}"
    custom-class="prepay-card-cell"
    value-class="{{ prepayCardCellValue.class }}"
    is-link="{{ !orderCreated && !!(valueCard.list.length || valueCard.disabled.length) }}"
    bind:click="onClick"
  >
    <view slot="title">
      储值卡/礼品卡
      <view wx:if="{{ payAssetActivityTagDesc && (display ? !display.newRecommend : false) }}" class="pre-pay-card__tag">
        {{ payAssetActivityTagDesc }}
      </view>
    </view>
  </van-cell>
  <van-transition
    show="{{ showTipCell }}"
    name=""
    duration="{{ { enter: 300, leave: 300 } }}"
    enter-class="van-enter-class"
    leave-to-class="van-leave-to-class"
  >
    <van-cell
      title="储值余额已支持与微信支付等一起使用"
      border="{{ false }}"
      custom-class="pre-pay-card-tip-cell"
      title-class="pre-pay-card-tip-cell__title"
      value-class="pre-pay-card-tip-cell__value"
    >
      <van-button
        custom-class="pre-pay-card-tip-cell__btn"
        round
        size="small"
        bind:click="onTipCellClick"
      >
        我知道了
      </van-button>
    </van-cell>
  </van-transition>
</view>
