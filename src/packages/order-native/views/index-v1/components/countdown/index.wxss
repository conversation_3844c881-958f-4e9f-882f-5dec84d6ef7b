@import "shared/common/css/helper/index.wxss";

.no-bottom-padding {
  padding-bottom: 0 !important;
}

.no-top-padding {
  z-index: 0;
  padding-top: 0 !important;
}

.value-class {
  display: none;
}

.top-info__icon {
  width: 40px;
  height: 40px;
  background: url("https://img01.yzcdn.cn/v2/image/wap/trade/result/order_status/<EMAIL>") center no-repeat;
  background-size: 40px 40px;
  margin-right: 10px;
}

.countdown-box {
  padding-top: 16px;
  overflow: hidden;
  text-align: center;
  background: #fff;
}

.countdown-box-title {
  color: #969799;
}
.countdown-box-desc {
  margin: 4px 0 16px;
  font-weight: bold;
  font-size: 16px;
  color: #ee0a24;
}
.countdown-box::after {
  content: '';
  display: block;
  height: 10px;
  background-color: #f7f8fa;
}