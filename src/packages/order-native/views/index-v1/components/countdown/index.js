import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import CountDown from 'utils/countdown';

const app = getApp();
VanxComponent({
  store,

  mapData: {
    ...mapState(['order', 'componentExtCountdown', 'countdownAbTestConfig']),
    ...mapGetters(['countdownTitle', 'countdownLabel', 'waitPhaseTwoStart']),
  },

  properties: {
    hideBorderBottom: Boolean, // 是否隐藏底部边框
  },

  data: {
    countdownStr: '',
    isShowNewVersion: false,
  },

  ready() {
    const { order } = this.$store.state;
    const { waitPhaseTwoStart } = this.$store.getters;
    if (!waitPhaseTwoStart && order.countdownInterval) {
      this.setCountDownProminent();
      this._initCountDown(order.countdownInterval);
    }
  },

  detached() {
    // 注销计时器
    if (this.disposer != null) {
      this.disposer();
      this.disposer = null;
    }
  },

  methods: {
    setCountDownProminent() {
      const abRes = this.data.countdownAbTestConfig;
      if (!abRes.isValid) {
        return;
      }
      const isNew = abRes.configurations?.isNew;
      app.logger?.log({
        et: 'view',
        ei: 'pay_countdown_view',
        en: '待支付订单页倒计时曝光',
        pt: 'paySuspend',
        params: {
          order_no: this.data.order.orderNo,
          abTraceId: abRes.abTraceId,
          isNew: Number(isNew),
        },
      });
      this.setYZData({
        isShowNewVersion: isNew,
      });
    },
    // 初始化倒计时
    _initCountDown(countdownInterval) {
      // 注销计时器
      if (this.disposer != null) {
        this.disposer();
        this.disposer = null;
      }
      const { multiPhase, phasePayment } = this.$store.state.pay;
      // 生成新计时器
      const countdown = new CountDown(countdownInterval, {
        onChange: (timeData, countdown = {}) => {
          const keys = ['day', 'hour', 'minute', 'second'];
          // 通过ab区分新版老板格式；
          if (this.data.isShowNewVersion) {
            const textMap = {
              day: `${+countdown.day}天`,
              hour: `${+countdown.hour}小时`,
              minute: `${+countdown.minute}分钟`,
              second: `${+countdown.second}秒`,
            };
            const countdownStr = keys
              .filter((key) => countdown[key] > 0)
              .map((key) => textMap[key])
              .join('');
            this.setYZData({ countdownStr });
            return;
          }
          const textMap = {
            day: `${countdown.day}天`,
            hour: `${countdown.hour}时`,
            minute: `${countdown.minute}分`,
            second: `${countdown.second}秒`,
          };
          const timeText = keys
            .filter((key) => countdown[key] > 0)
            .map((key) => textMap[key])
            .join('');
          const countdownStr = `${timeText}内付${
            multiPhase && phasePayment.currentPhase === 2 ? '尾' : ''
          }款，超时订单将自动关闭`;
          this.setYZData({ countdownStr });
        },
        onEnd: this.navigateToResult.bind(this),
      });
      this.disposer = countdown.stop;
    },
    // 跳转页面到订单详情页
    navigateToResult() {
      wx.redirectTo({
        url: `/packages/trade/order/result/index?orderNo=${this.data.order.orderNo}`,
      });
    },
  },
});
