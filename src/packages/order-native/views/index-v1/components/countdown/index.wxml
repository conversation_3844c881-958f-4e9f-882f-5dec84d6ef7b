<block wx:if="{{ order.countdown && componentExtCountdown.display }}">
  <block wx:if="{{!isShowNewVersion}}">
    <van-cell
      title="{{ countdownTitle }}"
      label="{{ waitPhaseTwoStart ? countdownLabel : countdownStr }}"
      custom-class="{{ order.steps.length > 0 ? 'no-bottom-padding' : '' }}"
      value-class="value-class"
      border="{{ false }}"
    >
      <view slot="icon" class="top-info__icon"></view>
    </van-cell>

    <van-steps
      wx:if="{{ order.steps.length > 0 }}"
      direction="horizontal"
      steps="{{ order.steps }}"
      custom-class="van-hairline--bottom"
    />
  </block>
  <block wx:else>
   <view v-else class="countdown-box">
      <view class="countdown-box-title">剩余支付时间</view>
      <view class="countdown-box-desc">
        <text class="countdown-box-num">{{countdownStr}}</text>
      </view>
    </view>
  </block>
</block>
