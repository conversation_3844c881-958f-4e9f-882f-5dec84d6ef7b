<van-cell wx:if="{{ hasGoods }}" custom-class="component">
  <view class="row">
    <text class="title">商品金额</text>
    <text class="value">{{ totalPrice.priceLabel }}</text>
  </view>
  <view wx:if="{{ showDisplayCardPrice }}" class="row">
    <text class="title">{{ displayCard.name }}</text>
    <text class="value">+ ¥{{ util.money(displayCard.price) }}</text>
  </view>
  <view class="row" wx:if="{{ currentPostage.available }}">
    <text class="title">运费</text>
    <text class="value">{{ totalPrice.postage }}</text>
  </view>
  <view class="row" wx:if="{{ totalPrice.taxPrice }}">
    <text class="title">进口税（含运费税款）</text>
    <text class="value">{{ totalPrice.taxPrice }}</text>
  </view>
  <view class="row" wx:for="{{ pay.extraFees }}" wx:key="index">
    <text class="title">{{ item.desc }}</text>
    <text class="value">+ ¥{{ util.money(item.realPay) }}</text>
  </view>
  <view class="row" wx:for="{{ pay.promotions }}" wx:key="index">
    <text class="title">{{ util.getPointsName(item, pointsName) }}</text>
    <text class="value">{{ totalPrice.promotions[index] }}</text>
  </view>
  <view class="row" wx:if="{{ couponDecrease !== 0 }}">
    <text class="title">优惠</text>
    <text class="value">{{ totalPrice.couponDecrease }}</text>
  </view>
  <view class="row" wx:if="{{ fissionActivityValue }}">
    <text class="title">内购券</text>
    <text class="value">{{ fissionActivityValue }}</text>
  </view>
  <view class="row" wx:if="{{ payUmpDiscountMoney !== 0 }}">
    <text class="title">支付优惠</text>
    <text class="value">{{ totalPrice.payUmpDiscountMoney }}</text>
  </view>
  <view class="row" wx:if="{{ prepayCardDecrease !== 0 }}">
    <text class="title">储值卡/礼品卡</text>
    <text class="value">{{ totalPrice.prepayCardDecrease }}</text>
  </view>
</van-cell>

<van-cell wx:if="{{ hasGoods }}" custom-class="price" border="{{ false }}">
  <view wx:if="{{ !showPriorUseSummary }}">
    合计：<text class="theme-color">{{ haitaoZeroOrder ? haitaoFinalPrice : finalPrice }}</text>
  </view>
  <view wx:if="{{ showPriorUseSummary }}">
    收货满意后再付款： ￥<text class="theme-color">{{ haitaoZeroOrder ? haitaoFinalPrice : finalPrice }}</text>
  </view>
</van-cell>

<wxs module="util">
  module.exports.money = function (value) {
    var yuan = value / 100;
    return yuan.toFixed(2);
  }
  module.exports.getPointsName = function(promotion, pointsName) {
    if (promotion.promotionTypeId === 256) {
      return promotion.name.replace('积分', pointsName);
    } else if (promotion.promotionTypeId === 115) {
      console.log('promotion.promotionTypeName',promotion.promotionTypeName)
      return promotion.promotionTypeName;
    } else {
      return promotion.name;
    }
  }
</wxs>
