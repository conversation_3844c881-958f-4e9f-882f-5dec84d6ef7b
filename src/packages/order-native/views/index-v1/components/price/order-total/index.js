import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['pay', 'displayCard', 'pointsName', 'fansBenefit', 'useBeforePayData']),
    ...mapGetters([
      'finalPrice',
      'hasGoods',
      'totalPrice',
      'currentPostage',
      'prepayCardDecrease',
      'couponDecrease',
      'payUmpDiscountMoney',
      'showDisplayCardPrice',
      'haitaoZeroOrder',
      'haitaoFinalPrice',
      'fissionActivityValue'
    ]),
    ...mapState({
      showPriorUseSummary: (state) => {
        const { show = false, enable = false, confirm = '0' } = state.useBeforePayData || {};
        return show && enable && confirm === '1';
      }
    })
  }
});
