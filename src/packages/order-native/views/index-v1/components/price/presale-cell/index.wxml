<van-cell wx:if="{{ hasGoods && hasDepositPreSaleGoods }}" custom-class="component">
  <view wx:for="{{ phasePayment.phaseItems }}" wx:key="{{ item.phase }}" class="text-left">
    <view class="dot {{ item.phase === phasePayment.currentPhase ? 'active' : '' }} {{ index === 0 ? 'first' : '' }}"></view>
    <view class="van-pull-right {{ item.phase === phasePayment.currentPhase ? 'theme-color' : '' }}">
      <!-- 支付优惠说明 -->
      <van-icon
        wx:if="{{ item.payUmpDiscountMoney > 0 }}"
        class="info-icon"
        name="info-o"
        data-phase="{{ item.phase }}"
        bind:tap="onShowPayUmpDetail"
      />
      <text
        class="info-text"
        style="min-width: {{ priceSpanWidth }}px"
      >
        {{ phasePaymentExpandLabel[index] }}¥{{ finalPhasePayment[index] }}
      </text>
    </view>

    <view class="status-wrap" wx:if="{{ isDepositPresale }}">
      <text wx:if="{{ item.phase === 1 }}">阶段一：定金</text>
      <text wx:if="{{ item.phase === 2 }}">阶段二：尾款</text>
      <text wx:if="{{ orderCreated && statusMap[item.status] }}" class="van-c-gray-darker status">
        ({{ statusMap[item.status] }})
      </text>

      <van-icon wx:if="{{ item.phase === 1 && orderCreated && showDepositAgreementVal}}" class="icon" name="question" bind:click="onShowAgreement" />
    </view>
  </view>
</van-cell>

<van-cell
  wx:if="{{ hasGoods && hasDepositPreSaleGoods && !orderCreated && showDepositAgreementVal}}"
  center
  value-class="value-class"
  class="pre-sale-confirm-scroll-to"
>
  <view slot="title" class="cell__title">
    我同意预售商品不退定金
    <van-icon class="icon" name="question" bind:click="onShowAgreement" />
  </view>
  <switch
    slot="right-icon"
    disabled="{{ orderCreated }}"
    checked="{{ agreeDeposit ? 'checked' : '' }}"
    bindchange="onChange"
  />
</van-cell>

<van-action-sheet title="定金不退协议" show="{{ showAgreement }}" bind:close="onCloseAgreement">
  <view class="agreement">
    <view wx:for="{{ agreement }}" wx:key="index" wx:for-item="block" class="agreement__paragraph">
      <view class="agreement__title">{{ block.title }}</view>
      <view wx:for="{{ block.words }}" wx:key="index" wx:for-item="line">{{ line }}</view>
    </view>
  </view>
  <van-button
    type="danger"
    size="large"
    custom-class="theme-bg-color theme-button"
    bind:click="onCloseAgreement"
  >我知道了</van-button>
</van-action-sheet>

<!-- 支付优惠详情弹窗 -->
<van-popup
  show="{{ showPayUmpDetail }}"
  bind:close="onClosePayUmpDetail"
  custom-class="pay-ump-detail-dialog"
>
  <view class="pay-ump-detail-dialog__title">
    价格说明
  </view>
  <view class="pay-ump-detail-dialog__main">
    <view class="pay-ump-detail-dialog__item">
      <text class="pay-ump-detail-dialog__left">
        {{ currentPhase.phase === 1 ? '定金' : '尾款' }}应付
      </text>
      <text class="pay-ump-detail-dialog__right">
        ￥{{ currentPhase.currentPrice }}
      </text>
    </view>
    <view class="pay-ump-detail-dialog__item">
      <text class="pay-ump-detail-dialog__left">
        支付优惠
      </text>
      <text class="pay-ump-detail-dialog__right">
        -￥{{ currentPhase.payUmpDiscountMoney }}
      </text>
    </view>
  </view>
  <van-button
    round
    block
    type="danger"
    size="large"
    custom-class="pay-ump-detail-dialog__btn theme-button"
    style="width: 100%"
    bind:tap="onClosePayUmpDetail"
  >
    知道了
  </van-button>
</van-popup>
