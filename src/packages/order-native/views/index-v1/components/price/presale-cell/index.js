import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import money from '@youzan/weapp-utils/lib/money';
import store from '@/packages/order-native/store/index';
import config from '@/packages/order-native/common/config';
import agreement from './agreement';
import Event from '@youzan/weapp-utils/lib/event';
import { isNewIphone } from 'shared/utils/browser/device-type';

VanxComponent({
  store,

  mapData: {
    ...mapState({
      hasDepositPreSaleGoods: state => state.tradeTag.hasDepositPreSaleGoods,
      agreeDeposit: state => state.order.agreeDeposit,
      showDepositAgreementVal: state => state.order.showDepositAgreementVal,
      phasePayment: state => state.pay.phasePayment,
      priceSpanWidth: state => {
        // 计算价格占用宽度
        const { phasePayment: { phaseItems = [] } } = state.pay;
        const phases = phaseItems.slice();
        if (phases.length === 0) return 0;
        return 12 + ((phases.sort((item1, item2) => {
          return item2.buyerRealPay - item1.buyerRealPay;
        })[0].buyerRealPay) / 100).toFixed(2).toString().length * 7;
      }
    }),
    ...mapGetters(['orderCreated', 'hasGoods', 'finalPhasePayment', 'phasePaymentExpandLabel', 'isDepositPresale'])
  },

  data: {
    showAgreement: false,
    showPayUmpDetail: false,
    currentPhase: {},
    statusMap: config.phaseStatus,
    agreement
  },

  methods: {
    onChange(event) {
      this.$commit('SET_AGREE_DEPOSIT', event.detail.value);
    },
    onCloseAgreement() {
      this.setYZData({
        showAgreement: false
      });
    },
    onShowAgreement() {
      this.setYZData({
        showAgreement: true
      });
    },

    onClosePayUmpDetail() {
      this.setYZData({
        showPayUmpDetail: false
      });
    },
    onShowPayUmpDetail(event) {
      const { phase = 1 } = event.detail;
      const { phasePayment: { phaseItems = [] } } = this.data;
      const currentPhase = phaseItems[phase - 1];
      if (currentPhase) {
        this.setYZData({
          showPayUmpDetail: true,
          currentPhase: {
            ...currentPhase,
            phase,
            currentPrice: money(currentPhase.currentPrice).toYuan(),
            payUmpDiscountMoney: money(currentPhase.payUmpDiscountMoney).toYuan()
          }
        });
      }
    }
  },

  lifetimes: {
    attached() {
      Event.on('SCROLL_TO_PRE_SALE', () => {
        const query = wx.createSelectorQuery().in(this);
        query.select('.pre-sale-confirm-scroll-to').boundingClientRect();
        query.selectViewport().scrollOffset();

        query.exec((res) => {
          if (!res[0] || !res[1]) return;
          var SystemInfo = wx.getSystemInfoSync();
          const elementRectBottom = res[0].bottom;
          const currentScrollTop = res[1].scrollTop;
          const SUBMIT_BAR_HEIGHT = 50; // 底部提交栏高度
          const safeAreaHeight = isNewIphone() ? 34 : 0;

          if (elementRectBottom < SystemInfo.windowHeight - SUBMIT_BAR_HEIGHT) return;

          wx.pageScrollTo({
            scrollTop: elementRectBottom - SystemInfo.windowHeight + SUBMIT_BAR_HEIGHT + currentScrollTop + safeAreaHeight,
            duration: 200,
          });
        });
      });
    },
  },
});
