import { VanxComponent } from 'pages/common/wsc-component/index';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  properties: {
    customProps: {
      type: Object,
      default: () => ({ hidePreSale: false }),
    },
  },

  externalClasses: [
    'theme-border-color',
    'theme-text-color',
    'theme-bg-color',
    'theme-color',
  ],

  ready() {
    if (this.data?.customProps?.hidePreSale) {
      this.$commit('SET_AGREE_DEPOSIT', true);
    }
  },
});
