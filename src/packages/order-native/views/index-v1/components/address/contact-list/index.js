import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  properties: {
    show: Boolean,
    textRule: String,
  },

  mapData: {
    ...mapState(['contact']),
  },

  methods: {
    handleSelect(event) {
      const { id } = event.currentTarget.dataset;
      this.$commit('SET_CONTACT_ID', id);
      this.handleClose();
    },
    handleEdit(event) {
      const { index } = event.currentTarget.dataset;

      // const dbid = app.db.set(this.data.contact.list[index]);
      const id =
        this.data.contact.list[index] && this.data.contact.list[index].id;
      let url = `/packages/order-native/contact/index?id=${id}`;
      if (['normal', 'hidden_phone'].indexOf(this.data.textRule) !== -1) {
        url += `&textRule=${this.data.textRule}`;
      }
      wx.navigateTo({ url });
    },
    handleAdd() {
      // const dbid = app.db.set({});
      const id = null;
      let url = `/packages/order-native/contact/index?id=${id}`;
      if (['normal', 'hidden_phone'].indexOf(this.data.textRule) !== -1) {
        url += `&textRule=${this.data.textRule}`;
      }
      wx.navigateTo({ url });
    },
    handleClose() {
      this.triggerEvent('close');
    },
  },
});
