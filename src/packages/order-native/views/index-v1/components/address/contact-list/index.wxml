<van-action-sheet show="{{ show }}" title="选择提货人" bind:close="handleClose">
  <scroll-view scroll-y class="dialog__content">
    <van-cell
      wx:for="{{ contact.list }}"
      wx:key="id"
      custom-class="cell-class"
      data-id="{{ item.id }}"
      bind:click="handleSelect"
      title="{{ item.userName }}，{{ item.telephone }}"
      title-class="title-class"
      value-class="value-class"
      center
    >
      <van-icon wx:if="{{ contact.id === item.id }}" slot="icon" name="checked" class="icon-check theme-color"/>
      <van-icon wx:else slot="icon" name="circle" class="icon-check van-c-gray-dark"/>
      <van-icon slot="right-icon" name="edit" class="icon-edit" data-index="{{ index }}" catchtap="handleEdit"/>
    </van-cell>
  </scroll-view>

  <van-button size="large" bind:click="handleAdd" custom-class="theme-bg-color">新增提货人</van-button>
</van-action-sheet>
