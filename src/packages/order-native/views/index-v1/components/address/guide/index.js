import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

const IMG_PREFIX = 'https://img01.yzcdn.cn/v2/image/wap';
const guideInfo = {
  groupon: [
    {
      logo: '/ump/guide/<EMAIL>',
      title: '1.选择商品开团/参团'
    },
    {
      logo: '/ump/guide/<EMAIL>',
      title: '2.邀请好友参团'
    },
    {
      logo: '/ump/guide/<EMAIL>',
      title: '3.人满成团'
    }
  ],
  hotel: [
    {
      logo: '/trade/new_order/hotel_guide/<EMAIL>',
      title: '1.选择房型并支付'
    },
    {
      logo: '/trade/new_order/hotel_guide/<EMAIL>',
      title: '2.商家接单确认'
    },
    {
      logo: '/trade/new_order/hotel_guide/<EMAIL>',
      title: '3.预订成功'
    }
  ]
};

VanxComponent({
  store,

  mapData: {
    ...mapState(['tradeTag', 'groupon'])
  },

  data: {
    steps: guideInfo.groupon.map(step => {
      step.logo = IMG_PREFIX + step.logo;
      return step;
    })
  }
});
