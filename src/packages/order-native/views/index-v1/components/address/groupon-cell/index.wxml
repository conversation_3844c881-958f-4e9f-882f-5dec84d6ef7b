<view
  wx:if="{{ expressType === 'express' && groupon.showAgencyReceive && !(orderCreated && !groupon.isHeader && !groupon.isChecked) }}"
  class="van-hairline--top"
>
  <!-- 团长 -->
  <van-cell
    wx:if="{{ groupon.isHeader }}"
    title="{{ textMap[groupon.receiveState].header }}"
    border="{{ false }}"
    value-class="value-class"
    center
  >
    <view slot="icon" class="pintuan__img"></view>
  </van-cell>

  <!-- 团员 -->
  <van-cell
    wx:else
    bind:click="onLeaderSelected"
    title="由团长代收包裹（免运费）"
    value="团长：{{ groupon.headerName }}"
    border="{{ false }}"
    center
  >
    <view
      slot="icon"
      wx:if="{{ groupon.receiveState === config.optionalReceive }}"
      class="pintuan__checkbox theme-color {{ groupon.isChecked ? 'pintuan_checkbox--checked': 'pintuan_checkbox--unchecked' }}"
    />
  </van-cell>
</view>
