import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import config from '@/packages/order-native/common/config';

VanxComponent({
  store,

  mapData: {
    ...mapState(['groupon']),
    ...mapGetters(['expressType', 'orderCreated'])
  },

  data: {
    config: config.groupon,
    textMap: {
      [config.groupon.optionalReceive]: {
        header: '若团员选择团长代收，团员商品将一起发货给你哦'
      },
      [config.groupon.forceReceive]: {
        header: '所有团员购买的商品都由团长你代收哦'
      },
    }
  },

  methods: {
    onLeaderSelected() {
      const { groupon, orderCreated } = this.data;
      if (orderCreated) {
        return;
      }
      if (groupon.receiveState === config.groupon.optionalReceive) {
        const checked = !groupon.isChecked;
        this.$commit('SET_GROUPON_IS_CHECKED', checked);
        this.$dispatch('FETCH_SHOW').then(() => {
          if (checked) {
            this.$dispatch('TOAST', '收件人已切换为团长');
          }
        });
      }
    },
  }
});
