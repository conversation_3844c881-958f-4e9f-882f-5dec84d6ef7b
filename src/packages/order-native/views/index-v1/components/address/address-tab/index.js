import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import { data, computed } from './computed';

VanxComponent({
  store,

  computed: {
    ...mapState([
      'address',
    ]),
    ...mapState(computed),
  },

  mapData: {
    ...mapState({
      showAddressTab: (state) => state.display.showAddressTab,
      showExpressTab: (state) => state.display.showExpressTab,
      hideSelfFetch: (state) => state.display.hideSelfFetch,
      allowSelfFetch: (state) => state.selfFetch.isAllow,
      activeTab: (state) => state.address.activeTab,
    }),
    ...mapState(data),
  },

  externalClasses: ['th_tree-a'],

  data: {
    itemClass: {
      selected: 'selected theme-text-color theme-bg-color',
      normal: 'theme-color van-c-gray-darker',
    },
  },

  ready() {
    const { orderCreated, expressType } = this.$store.getters;

    if (expressType === 'self-fetch' && !orderCreated) {
      this.$dispatch('INIT_SELF_FETCH').then(() =>
        this.$dispatch('FETCH_SHOW')
      );
    }
  },

  methods: {
    onSwitchTab(event) {
      const { getters, state } = this.$store;
      if (!getters.addressEditable) {
        this.$dispatch('TOAST', '你不能再修改配送方式');
        return;
      }

      const { index } = event.currentTarget.dataset;
      const tabInfo = this.tabs[index];
      if (index === state.address.activeTab) {
        return;
      }

      this.$commit('SET_EXPRESS_WAP_TAB', index);

      if (tabInfo.value !== 'express') {
        this.$commit('RESET_LOCAL_DELIVERY_TIME');
      }
      if (tabInfo.value === 'self-fetch' && !state.selfFetch.shop) {
        this.$dispatch('INIT_SELF_FETCH').then(() =>
          this.$dispatch('FETCH_SHOW')
        );
      } else {
        this.$dispatch('FETCH_SHOW');
      }
    },
  },
});
