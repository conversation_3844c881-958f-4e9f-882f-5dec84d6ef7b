<block wx:if="{{ showAddressTab && showExpressTab && allowSelfFetch }}">
  <!-- 商家配送 / 到店自提的顶部tab -->
  <view class="container van-hairline--bottom">
    <view class="segmentedControl theme-border-color">
      <text
        wx:for="{{ tabs }}"
        class="{{ activeTab === item.id ? itemClass.selected : itemClass.normal }} theme-border-color"
        data-index="{{ item.id }}"
        bindtap="onSwitchTab"
      >{{ item.text }}</text>
    </view>
  </view>

  <!-- NOTE: address-tab会频繁切换，使用hidden减少渲染消耗 -->
  <!-- 配送地址 -->
  <view hidden="{{ activeTab !== 0 }}">
    <logistics />
  </view>
  <!-- 自提配送 -->
  <view hidden="{{ activeTab !== 1 }}">
    <self-fetch
      theme-color="theme-color"
      theme-button="theme-button"
      theme-bg-color="theme-bg-color"
      th_tree-a="th_tree-a"
    />
  </view>
</block>

<!-- 只支持快递配送 -->
<logistics wx:elif="{{ showAddressTab && showExpressTab }}" />

<!-- 只支持自提配送 -->
<self-fetch
  wx:elif="{{ !hideSelfFetch && showAddressTab && allowSelfFetch }}"
  theme-bg-color="theme-bg-color"
  theme-button="theme-button"
  theme-color="theme-color"
  th_tree-a="th_tree-a"
/>
