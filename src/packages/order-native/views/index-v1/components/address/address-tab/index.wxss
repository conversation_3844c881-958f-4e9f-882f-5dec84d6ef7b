@import 'shared/common/css/helper/index.wxss';
@import "themes/order-theme.scss";

.container {
  padding: 10px 0;
  background-color: #fff;
}

.segmentedControl {
  display: flex;
  width: 690rpx;
  height: 30px;
  margin: 0 auto;
  border: 1px solid #666;
  border-radius: 4rpx;
  box-sizing: border-box;
}

/* 普通文本 */
.segmentedControl text {
  font-size: 12px;
  line-height: 28px;
  text-align: center;
  flex: 1 0 50%;
}

/* 选中状态文本 */
.segmentedControl .selected {
  color: white;
  background-color: #666;
}

.order-address__border {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 2px;
  background: #fff url('https://b.yzcdn.cn/v2/image/wap/address/<EMAIL>') repeat-x;
  background-size: 36px 2px;
}
