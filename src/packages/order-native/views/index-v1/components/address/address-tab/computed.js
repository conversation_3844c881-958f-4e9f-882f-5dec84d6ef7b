export const computed = {
  selfFetchDefaultAB() {
    const { configurations, isValid } = this.address?.abConfigExpressWay || {};
    const { hasSelfFetchDefault } = configurations || {};
    return !!isValid && !!hasSelfFetchDefault;
  },
  tabs() {
    const EXPRESS = { id: 0, value: 'express', text: '商家配送' };
    const SELF_FETCH = { id: 1, value: 'self-fetch', text: '到店自提' };
    if (this.address?.isSelfFetchDefault && this.selfFetchDefaultAB) {
      return [SELF_FETCH, EXPRESS];
    }

    return [EXPRESS, SELF_FETCH];
  },
};

export const data = {
  selfFetchDefaultAB() {
    return this.selfFetchDefaultAB;
  },
  tabs() {
    return this.tabs;
  },
};
