import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import config from '@/packages/order-native/common/config';

VanxComponent({
  store,

  mapData: {
    ...mapState(['user', 'selfFetch', 'tradeTag']),
    ...mapGetters([
      'orderCreated',
      'selfFechContact',
      'selfFetchAddressDetail',
      'selfFetchTime',
      'currentContact',
      'canSelectSelfFetchAddress',
      'canSelectSelfFetchTime',
    ]),
  },

  externalClasses: ['th_tree-a'],

  data: {
    showContactList: false,
    showTimePicker: false,
  },

  ready() {
    // 提交订单校验时可能唤起选择联系人
    this.on(config.eventKey.showContact, this.bindNameInput.bind(this));
    this.on(
      config.eventKey.showSelfFetchShop,
      this.onFetchAddressTap.bind(this)
    );
    this.on(config.eventKey.showSelfFetchTime, this.onFetchTimeTap.bind(this));
  },

  methods: {
    handleCloseContactList() {
      this.setYZData({ showContactList: false });
    },
    // 选择提货人
    bindNameInput() {
      this.setYZData({ showContactList: true });
    },
    // 选择提货地址
    onFetchAddressTap() {
      if (this.data.canSelectSelfFetchAddress) {
        wx.navigateTo({
          url: '/packages/order-native/self-fetch-address/index',
        });
      }
    },
    getTimePicker() {
      if (!this.timePicker) {
        this.timePicker = this.selectComponent('#time-picker');
      }
      return this.timePicker;
    },
    // 选择提货时间
    onFetchTimeTap() {
      const { selfFetch } = this.$store.state;
      const { orderCreated, canSelectSelfFetchTime } = this.$store.getters;
      if (!canSelectSelfFetchTime) {
        if (!selfFetch.shop && !orderCreated) {
          this.$dispatch('TOAST', '请先选择提货地址');
        }
        return;
      }

      const timePicker = this.getTimePicker();
      if (timePicker.getAvailableTime() != null) {
        timePicker.init();
        this.setYZData({
          showTimePicker: true,
        });
      } else {
        this.$dispatch('TOAST', '没有可选的自提时间');
      }
    },
    // 取消选择自提时间
    handleCloseTimePicker() {
      this.setYZData({
        showTimePicker: false,
      });
    },
    // 选择自提时间
    handleConfirmTime(event) {
      const { hasNeedStockUpGoods } = this.data.tradeTag;

      this.$commit('SET_SELF_FETCH_TIME', event.detail);
      this.handleCloseTimePicker();

      if (hasNeedStockUpGoods) {
        this.$dispatch('FETCH_SHOW');
      }
    },
    // 联系提货点
    handleContactSelfFetchService() {
      const { tel } = this.data.user.delivery.selfFetch;
      wx.showModal({
        title: tel,
        confirmText: '呼叫',
        success: (res) => {
          if (res.confirm) {
            wx.makePhoneCall({ phoneNumber: tel });
          }
        },
      });
    },
  },
});
