<block wx:if="{{ orderCreated }}">
  <van-cell
    title="提货人"
    title-width="80px"
    value-class="van-c-black"
    value="{{ selfFechContact }}"
  />
  <van-cell
    title="提货地址"
    title-width="80px"
    value-class="van-c-black"
    value="{{ selfFetchAddressDetail }}"
  />
  <van-cell
    title="提货时间"
    title-width="80px"
    value-class="van-c-black"
    value="{{ selfFetchTime }}"
  />

  <van-cell wx:if="{{ user.delivery.selfFetch.tel }}" value-class="shop__im" bind:click="handleContactSelfFetchService">
    <image class="shop__im-icon" src="https://img01.yzcdn.cn/public_files/2016/12/02/8812ea19818110de677ac215286b3005.png"/>
    <text>联系提货点</text>
  </van-cell>
</block>

<block wx:else>
  <van-cell title="提货人" title-width="80px" is-link bind:click="bindNameInput">
    <text wx:if="{{ currentContact.id }}" class="van-c-black">{{ currentContact.userName }} {{ currentContact.telephone }}</text>
    <text wx:else class="van-c-gray-dark">选择提货人</text>
  </van-cell>
  <van-cell title="提货地址" title-width="80px" is-link="{{ canSelectSelfFetchAddress }}" bind:click="onFetchAddressTap">
    <view class="{{ selfFetchAddressDetail !== '选择提货地址' ? 'van-c-black' : 'van-c-gray-dark' }}">
      {{ selfFetchAddressDetail }}
    </view>
  </van-cell>
  <van-cell title="提货时间" title-width="80px" is-link="{{ canSelectSelfFetchTime }}" bind:click="onFetchTimeTap" border="{{ false }}">
    <view class="{{ selfFetchTime !== '选择提货时间' ? 'van-c-black' : 'van-c-gray-dark' }}">
      {{ selfFetchTime }}
    </view>
  </van-cell>
</block>

<contact-list
  show="{{ showContactList }}"
  bind:close="handleCloseContactList"
  theme-bg-color="theme-bg-color"
  theme-color="theme-color"
  text-rule="hidden_phone"
/>

<time-picker
  id="time-picker"
  theme-color="theme-color"
  theme-bg-color="theme-bg-color"
  theme-button="theme-button"
  theme-border-color="theme-border-color"
  th_tree-a="th_tree-a"
  titles="{{ ['选择自提时间', '选择自提时间'] }}"
  show="{{ showTimePicker }}"
  type="selfFetch"
  value="{{ selfFetch.time }}"
  bind:close="handleCloseTimePicker"
  bind:confirm="handleConfirmTime"
/>
