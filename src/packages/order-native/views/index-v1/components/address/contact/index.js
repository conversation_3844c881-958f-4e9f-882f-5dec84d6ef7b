import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['contact']),
    ...mapGetters(['orderCreated', 'currentContact'])
  },

  data: {
    showContactList: false
  },

  methods: {
    handleTap() {
      if (this.data.orderCreated) {
        return;
      }
      this.setYZData({
        showContactList: true
      });
    },

    handleCloseContactList() {
      this.setYZData({
        showContactList: false
      });
    }
  }
});
