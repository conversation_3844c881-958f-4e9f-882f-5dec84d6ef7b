<block wx:if="{{ contact.required }}">
  <order-address-customer
    wx:if="{{ contact.id || orderCreated }}"
    user-name="{{ currentContact.userName }}"
    tel="{{ currentContact.telephone }}"
    access="{{ !orderCreated }}"
    catchtap="handleTap"
  />

  <van-cell wx:else is-link bind:click="handleTap" title="添加订单联系人信息" border="{{ false }}" center>
    <image slot="icon" class="customer-info__icon" src="https://b.yzcdn.cn/v2/image/wap/address/<EMAIL>" />
  </van-cell>

  <contact-list
    show="{{ showContactList }}"
    bind:close="handleCloseContactList"
    theme-color="theme-color"
    theme-bg-color="theme-bg-color"
  />
</block>
