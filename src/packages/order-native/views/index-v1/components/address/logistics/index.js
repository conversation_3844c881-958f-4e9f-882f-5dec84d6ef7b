import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import config from '@/packages/order-native/common/config';

const app = getApp();

VanxComponent({
  store,

  mapData: {
    ...mapState(['dataLoaded']),
    ...mapGetters([
      'hasAddress',
      'currentAddress',
      'addressEditable',
      'fullAddress',
      'showPoiPrompt'
    ])
  },

  attached() {
    const { eventKey } = config;

    // 选择地址
    this.on(eventKey.addressSelect, address => {
      this.$commit('SET_ADDRESS_ID', address.id);
      this.$dispatch('FETCH_SHOW');
    });
    // 删除地址
    this.on(eventKey.addressDelete, id => {
      this.$commit('DELETE_ADDRESS', id);
    });
    // 编辑 / 添加地址
    this.on(eventKey.addressSave, newAddress => {
      const { list, id } = this.$store.state.address;
      const editingIndex = list.findIndex(item => item.id === newAddress.id);

      if (editingIndex === -1) {
        // 添加地址
        this.$commit('ADD_ADDRESS', newAddress);
      } else {
        // 编辑地址
        this.$commit('UPDATE_ADDRESS', newAddress);
      }

      if (newAddress.id !== id) {
        this.$commit('SET_ADDRESS_ID', newAddress.id);
      }

      this.$dispatch('FETCH_SHOW').then(() => {
        this.$dispatch('TOAST', '地址编辑成功');
      });
    });
  },

  ready() {
    // 页面滚动
    this.createIntersectionObserver()
      .relativeToViewport()
      .observe('#logistics', res => {
        const { address } = this.$store.state;

        if (address.activeTab === 0) {
          this.$commit('SET_ADDRESS_VISIBLE', res.intersectionRatio > 0);
        }
      });
  },

  methods: {
    // 选择收货地址
    onAddressTap() {
      if (!this.data.addressEditable) {
        this.$dispatch('TOAST', '你不能再修改配送方式');
        return;
      }

      this.$dispatch('LOG_CLICK_ADDRESS');

      const { id, list } = this.$store.state.address;
      const { forcePoiSelect } = this.$store.getters;

      // 没有收货地址时 跳转到新增地址 否则跳转到地址列表
      if (list.length === 0) {
        const dbid = app.db.set({ list, forcePoiSelect, delta: 1 });

        this.$dispatch('LOG_ADD_ADDRESS');

        wx.navigateTo({
          url: `/packages/order-native/address-edit/index?dbid=${dbid}`
        });
      } else {
        const dbid = app.db.set({ list, id, forcePoiSelect, switchable: true });
        wx.navigateTo({
          url: `/packages/order-native/address-list/index?dbid=${dbid}&scene=order`
        });
      }
    },

    onEdit() {
      const { id, list } = this.$store.state.address;
      const { forcePoiSelect } = this.$store.getters;
      const dbid = app.db.set({ list, forcePoiSelect, id, delta: 1 });
      wx.navigateTo({
        url: `/packages/order-native/address-edit/index?dbid=${dbid}`
      });
    }
  }
});
