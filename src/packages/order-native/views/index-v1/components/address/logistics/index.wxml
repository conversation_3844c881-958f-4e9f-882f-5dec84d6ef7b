<van-cell
  wx:if="{{ hasAddress }}"
  bind:click="onAddressTap"
  is-link="{{ addressEditable }}"
  value-class="value-class"
  border="{{ false }}"
  id="logistics"
  center
>
  <van-icon slot="icon" name="location-o" class="left-icon-location" />
  <view slot="title" class="content">
    <view class="title">
      <text>收货人：{{ currentAddress.userName }}</text>
      <text>{{ currentAddress.tel }}</text>
    </view>
    <view class="label">收货地址：{{ fullAddress }}</view>
  </view>
</van-cell>

<van-cell wx:elif="{{ dataLoaded }}" id="logistics" title="选择收货地址" bind:click="onAddressTap" is-link center>
  <van-icon slot="icon" name="add-square" class="left-icon-add" />
</van-cell>

<view
  wx:if="{{ showPoiPrompt }}"
  slot="extra"
  class="address__tip"
  catchtap="onEdit"
>
  为提高同城配送准确性，请进行地图定位选点
  <van-icon name="arrow" />
</view>
