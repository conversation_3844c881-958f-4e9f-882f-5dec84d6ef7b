<page-container theme-template="{{ theme }}" class="page {{ themeClass }} page-{{ deviceType }}">
  <view class="van-contact-edit">
    <view class="van-contact-edit__fields">
      <van-field
        label="姓名"
        placeholder="{{ textInfo.userNamePlaceholder }}"
        value="{{ item.userName }}"
        maxlength="{{ 30 }}"
        clearable
        bind:change="onInputName"
        data-key="userName"
        bind:focus="onFocus"
        placeholder-style="color: #c8c9cc !important;"
        error-message="{{ errorInfo.userName }}"
      />

      <van-field
        label="{{ textInfo.phoneLabel }}"
        placeholder="{{ textInfo.phonePlaceholder }}"
        type="number"
        border="{{ false }}"
        clearable
        value="{{ item.telephone }}"
        bind:change="onInputTel"
        data-key="telephone"
        bind:focus="onFocus"
        placeholder-style="color: #c8c9cc !important;"
        error-message="{{ errorInfo.telephone }}"
      />
    </view>

    <van-cell
      title="{{ textInfo.setDefaultTitle }}"
      custom-class="van-contact-edit__switch-cell"
      border="{{ false }}"
    >
      <van-switch
        checked="{{ item.isDefault }}"
        size="24px"
        bind:change="onChangeDefault"
      />
    </van-cell>

    <view class="van-contact-edit__buttons">
      <van-button
        type="danger"
        block
        round
        loading="{{ isSaving }}"
        bind:click="onSave"
      >
        保存并使用
      </van-button>
      <van-button
        wx:if="{{ item.id }}"
        type="default"
        block
        round
        loading="{{ isDeleting }}"
        bind:click="onDelete"
      >
        删除
      </van-button>
    </view>
  </view>
</page-container>
