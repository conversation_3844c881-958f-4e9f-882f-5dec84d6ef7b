import { mapState, mapGetters } from '@youzan/vanx';
import { VanxPage } from 'pages/common/wsc-page/index';
import store from '@/packages/order-native/store/index';
import isPhone from '@youzan/weapp-utils/lib/validators/is-phone';
import Toast from '@vant/weapp/toast/toast';
import { themeStyleTemplate } from './theme';

// 文案显示规则
const TEXT_RULE = {
  HIDDEN_PHONE: 'hidden_phone',
  NORMAL: 'normal',
};

VanxPage({
  store,

  computed: {
    ...mapGetters(['editingContact']),
  },

  mapData: {
    ...mapState(['contact', 'textRule']),
    ...mapGetters(['editingContact', 'textInfo']),
  },

  data: {
    item: {},
    errorInfo: {},
    isSaving: false,
    isDeleting: false,
    theme: themeStyleTemplate,
  },

  watch: {
    editingContact(newValue) {
      this.setYZData({
        item: newValue,
      });
    },
  },

  onLoad({ id, textRule } = { textRule: TEXT_RULE.NORMAL }) {
    this.$commit('SET_TEXT_RULE', textRule);
    const { textInfo } = this.data;
    wx.setNavigationBarTitle({
      title: +id
        ? `编辑${textInfo.contactLabel}`
        : `添加${textInfo.contactLabel}`,
    });
    if (!id) {
      return;
    }
    this.$commit('SET_CONTACT_EDITING_ID', +id);
    if (this.data.contact.list.length === 0) {
      this.$dispatch('FETCH_CONTACT_LIST');
    }
  },

  onUnload() {
    this.$commit('SET_CONTACT_EDITING_ID', -1);
  },

  onInputName(event) {
    this.setYZData({
      'item.userName': event.detail,
    });
  },

  onInputTel(event) {
    this.setYZData({
      'item.telephone': event.detail,
    });
  },

  onChangeDefault(event) {
    this.setYZData({
      'item.isDefault': event.detail,
    });
  },

  onFocus(event) {
    this.setYZData({
      [`errorInfo.${event.currentTarget.dataset.key}`]: '',
    });
  },

  getErrorMessageByKey(key) {
    let value = this.data.item[key];
    value = String(value == null ? '' : value).trim();

    switch (key) {
      case 'userName':
        return value ? '' : '请输入正确的姓名';
      case 'telephone':
        if (this.data.textRule === TEXT_RULE.HIDDEN_PHONE) {
          const isNumber = /^\d+$/;
          if (!isNumber.test(value)) {
            return '请填写提货码，可输入手机号';
          }
          if (value.length > 11) {
            return '提货码最长不超过11位';
          }
        } else {
          return isPhone(value) ? '' : '请输入正确的手机号';
        }
      default:
        return '';
    }
  },

  // 保存提货人
  onSave() {
    if (this.data.isSaving) {
      return;
    }

    const isValid = ['userName', 'telephone'].every((item) => {
      const msg = this.getErrorMessageByKey(item);
      if (msg) {
        this.setYZData({
          [`errorInfo.${item}`]: msg,
        });
        Toast(msg);
      }
      return !msg;
    });

    if (!isValid) {
      return;
    }

    this.setYZData({ isSaving: true });
    this.$dispatch('SAVE_CONTACT', {
      ...this.data.item,
      isDefault: +this.data.item.isDefault,
    })
      .then(() => {
        this.setYZData({ isSaving: false });
        wx.navigateBack();
      })
      .catch(() => {
        this.setYZData({ isSaving: false });
      });
  },

  // 删除提货人
  onDelete() {
    if (this.data.isDeleting) {
      return;
    }
    this.setYZData({ isDeleting: true });
    this.$dispatch('DELETE_CONTACT', this.data.item.id)
      .then(() => {
        this.setYZData({ isDeleting: false });
        wx.navigateBack();
      })
      .catch(() => {
        this.setYZData({ isDeleting: false });
      });
  },
});
