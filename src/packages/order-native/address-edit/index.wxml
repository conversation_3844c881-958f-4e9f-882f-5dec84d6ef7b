<wxs src="./index.wxs" module="helper"></wxs>

<page-container theme-template="{{ theme }}" class="{{ themeClass }} page-{{ deviceType }}" page-container-class="page-container-class">
  <view
    slot="page-top-hook"
    wx:if="{{ canUseNav }}"
    class="navigation-bar"
    style="height: {{ height }}px;"
  >
    <view
      style="height: {{ height }}px;"
      class="navigation-bar__sticky-wrap"
    >
      <view
        class="navigation-bar__main"
        style="height: {{ height }}px;"
      >
        <view
          class="navigation-bar__icon"
          style="{{ customIconStyle }}"
        >
          <view class="navigation-bar__icon-button" bind:tap="onBackTap">
            <van-icon name="arrow-left" size="20px"/>
          </view>
        </view>
        <view class="navigation-bar__title-text"
          style="padding-top: {{ paddingTop }}px;">
          <text style="width: {{ textWidth }}px; font-size: {{ fontSizeSetting }}px;">
            {{ titleText }}
          </text>
        </view>
      </view>
    </view>
  </view>
  <view class="address-edit">
    <current-address
      wx:if="{{ !form.id }}"
      custom-class="address-edit__current"
      bind:save="onCurrentLocationUse"
    />

    <wechat-address
      wx:if="{{ !form.id && showGetWechatAddress }}"
      custom-class="address-edit__wechat"
      bind:save="onWechatSave"
    />

    <view class="address-edit__fields">
      <van-field
        label="收货人"
        placeholder="收货人姓名"
        value="{{ form.userName }}"
        data-type="userName"
        title-width="72px"
        error="{{ errorInfo.userName }}"
        clearable
        bind:focus="onFocus"
        bind:change="handleInputForm"
        bind:blur="handleFormBlur"
      />
      <van-field
        label="手机号码"
        placeholder="收货人手机号"
        type="number"
        maxlength="20"
        data-type="tel"
        title-width="72px"
        value="{{ form.tel }}"
        error="{{ errorInfo.tel }}"
        clearable
        bind:focus="onFocus"
        bind:change="handleInputForm"
        bind:blur="handleFormBlur"
      />
      <van-field
        label="所在地区"
        placeholder="选择省 / 市 / 区"
        value="{{ helper.getAreaValue(form.country, form.province, form.city, form.county) }}"
        readonly
        title-width="72px"
        is-link="{{ !forcePoiSelect || form.downgrade }}"
        bind:tap="handleAreaShow"
        error="{{ errorInfo.areaCode }}"
        error-message="{{ errorInfoMsg.areaCode }}"
      />
      <van-field
        label="详细地址"
        placeholder="请填写详细地址"
        type="textarea"
        autosize
        value="{{ form.addressDetail }}"
        data-type="addressDetail"
        title-width="72px"
        error="{{ errorInfo.addressDetail }}"
        readonly="{{ forcePoiSelect && !form.downgrade }}"
        clearable
        bind:focus="onFocus"
        bind:change="handleInputForm"
        bindtap="onClickDetail"
        bind:blur="onBlur"
      >
        <view
          slot="right-icon"
          class="address-edit__location"
          catchtap="onClickIcon"
        >
          <van-icon class="address-edit__location__icon" name="location" size="12px" />
          定位
        </view>
      </van-field>
      <view wx:if="{{ forcePoiSelect && !form.downgrade && form.type !== 2 }}" class="address-edit__tip">
        点击定位进行地图选点，提高配送准确性
      </view>
      <view hidden="{{ !map.longitude || !map.latitude }}" class="address-edit__piece">
        <map
          id="map"
          class="map"
          markers="{{ map.markers }}"
          longitude="{{ map.longitude }}"
          latitude="{{ map.latitude }}"
          scale="{{ 15 }}"
          enable-zoom="{{ false }}"
          enable-scroll="{{ false }}"
          bindtap="onClickIcon"
        >
          <!-- marker -->
          <cover-view class="map__marker">
            <cover-view wx:if="{{ map.markerLabel && showMarkerLabel }}"  class="map__marker__label">
              <cover-view>{{ map.markerLabel }}</cover-view>
              <cover-view class="map__marker__label__after" />
            </cover-view>
          </cover-view>
        </map>
      </view>
      <van-field
        value="{{ form.houseNumber }}"
        clearable
        border="{{ false }}"
        type="textarea"
        autosize
        placeholder="街道、门牌号等"
        label="门牌号"
        data-type="houseNumber"
        title-width="72px"
        bind:change="handleInputForm"
        bind:blur="handleFormBlur"
      />
    </view>
    <view class="address-edit__fields">
      <van-field
        wx:if="{{ !form.id && !showAnalyzeInput }}"
        label="智能识别"
        placeholder="请粘贴或输入地址，可智能识别"
        readonly
        title-width="72px"
        bind:tap="onShowAnalyze"
      />
      <analyze
        wx:if="{{ showAnalyzeInput }}"
        bind:analyze="onAnalyze"
        bind:close="onCloseAnalyze"
      />
    </view>

    <labels
      custom-class="address-edit__labels"
      value="{{form.label}}"
      bind:changeLabel="onChangeLabel" />

    <van-cell
      center
      title="设为默认收货地址"
      border="{{ false }}"
      custom-class="address-edit__default"
    >
      <van-switch
        size="24px"
        custom-class="address-edit__default__switch"
        checked="{{ form.isDefault }}"
        data-type="isDefault"
        bind:change="handleInputForm"
      />
    </van-cell>

    <view class="address-edit__buttons">
      <van-button
        type="danger"
        custom-class="address-edit__button"
        round
        block
        bind:click="handleSave"
        loading="{{ isSaving }}"
      >保存并使用</van-button>
      <van-button
        wx:if="{{ form.id }}"
        type="default"
        custom-class="address-edit__button"
        round
        block
        bind:click="handleDelete"
        loading="{{ isDeleting }}"
      >删除收货地址</van-button>
    </view>
  </view>

  <van-dialog
    use-slot
    use-title-slot
    show="{{ showBackupDialog }}"
    show-confirm-button="{{ false }}">
    <view slot="title" class="dialog__title">返回将不保存当前信息，是否离开？</view>
    <view class="dialog__btn dialog__btn-red" bindtap="onClickBtnBack" data-action="save">保存并离开</view>
    <view class="dialog__btn" bindtap="onClickBtnBack" data-action="leave">离开</view>
    <view class="dialog__btn" bindtap="onClickBtnBack" data-action="cancel">取消</view>
  </van-dialog>
</page-container>

<van-popup show="{{ showArea }}" position="bottom">
  <select-area
    area-list="{{ areaList }}"
    loading="{{ areaLoading }}"
    bind:confirm="handleAreaConfirm"
    bind:cancel="handleAreaCancel"
    value="{{ form.areaCode }}"
  />
</van-popup>

<van-toast id="van-toast" />
