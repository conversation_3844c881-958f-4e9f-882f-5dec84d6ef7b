$navBarHeight: 64px;

.page-container-class {
  // padding: 12px;
}

.van-cell::after {
  right: 16px !important;
}

.address-edit {
  margin: 12px;

  &__fields {
    overflow: hidden;
    margin: 12px 0;
    border-radius: 8px;
    background-color: #fff;
  }

  &__detail {
    padding: 0 !important;
    text-align: left !important;

    &-title {
      max-width: 90px !important;
    }
  }

  &__location {
    font-size: 12px;
    color: #969799;
    display: flex;
    align-items: center;

    &__icon {
      height: 12px;
      margin-right: 2px;
      line-height: 12px;
    }
  }

  &__tip {
    color: #ed6a0c;
    font-size: 12px;
    background-color: #fff;
    padding-left: 84px;
    margin-top: -2px;
  }

  &__default {
    margin-top: 12px;
    overflow: hidden;
    border-radius: 8px;

    &__switch {
      display: block;
      float: right;
    }
  }

  &__buttons {
    padding: 32px 4px;
  }

  &__button {
    margin-bottom: 12px;
    font-size: 16px !important;
  }

  &__analyze {
    height: 44px;
    font-size: 14px;
    color: #969799;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;

    &__text {
      margin-right: 8px;
    }
  }
}

.address-edit__piece {
  padding: 0 16px;

  .map {
    width: 100%;
    height: 88px;
    border-radius: 8px;

    &__marker {
      position: absolute;
      bottom: 50%;
      right: 35%;
      width: 100%;
      text-align: right;

      &__label {
        position: relative;
        margin-bottom: 6px;
        overflow: visible;
        background-color: #fff;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.08);
        padding: 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        color: #333;
        line-height: 12px;
        white-space: nowrap;
        display: inline-block;

        &__after {
          position: absolute;
          bottom: 0;
          right: 30px;
          z-index: 1;
          display: block;
          width: 0;
          height: 0;
          border-top: 6px solid #fff;
          border-right: 6px solid transparent;
          border-left: 6px solid transparent;
          content: '';
          transform: translate(0, 100%);
        }
      }
    }
  }
}

.navigation-bar {
  width: 100%;
  height: $navBarHeight;

   &__main{
    height: $navBarHeight;
    background: #fff;

    /** z-index 顺序 修改请查看 docs/z-index.md **/
  }

  &__sticky-wrap {
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 998;
  }


   &__icon {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-around;
    box-sizing: border-box;
    overflow: hidden;
    &-button{
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &__main{
    height: 64px;
    background: #fff;
  }

  &__sticky-wrap {
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 10001;
  }

  &__title-text {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    box-sizing: border-box;
    font-size: 36rpx;
    font-weight: 500;
    padding-top: 20px;

    text {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-align: center;
      width: 161px;
      font-size: 16px;
    }
  }
}

.dialog {
  &__title {
    padding: 18px 0 42px;
    font-size: 14px;
    color: #646566;
    font-weight: 400;
    text-align: center;
    border-bottom: 1px solid #ebedf0;
  }

  &__btn {
    padding: 12px 0;
    font-size: 16px;
    font-weight: 400;
    color: #323233;
    text-align: center;
    border-bottom: 1px solid #ebedf0;

    &-red {
      color: #ee0a24;
    }
  }

  &__btn:last-child {
    border: none;
  }
}
