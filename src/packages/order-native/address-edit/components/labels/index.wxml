<van-cell 
  center
  title="地址标签" 
  border="{{ false }}"
  custom-class="labels {{ customClass }}"
  title-class="labels__title"
  value-class="labels__value"
>
  <view class="labels__selection">
    <view
      class="label {{ value === label ? 'label__active' : '' }}"
      wx:for="{{ labels }}"
      wx:for-item="label"
      wx:key="label"
      data-label="{{label}}"
      bind:tap="onSelectLabel"
    >
      {{ label }}
    </view>
  </view>
</van-cell>