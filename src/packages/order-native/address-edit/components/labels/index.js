import { VanxComponent } from 'pages/common/wsc-component/index';

VanxComponent({
  properties: {
    value: {
      type: String,
      value: ''
    },
    customClass: {
      type: String,
      value: ''
    }
  },
  data: {
    labels: ['家', '公司', '学校'], // 这一期暂时前端写死
  },

  methods: {
    onSelectLabel(event) {
      const { label } = event.currentTarget.dataset;
      this.triggerEvent('changeLabel', { label });
    }
  }
});
