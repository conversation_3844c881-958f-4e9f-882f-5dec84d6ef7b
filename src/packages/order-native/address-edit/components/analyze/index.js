import { VanxComponent } from 'pages/common/wsc-component/index';
import Toast from '@vant/weapp/toast/toast';
import { parseAddress } from '@/packages/order-native/utils/request';

VanxComponent({
  data: {
    address: '',
    showClearBtn: false,
  },

  methods: {
    onAnalyze() {
      if (!this.address) {
        this.onAnalyzeFail('请输入地址信息');
        return;
      }

      parseAddress({ address: this.address })
        .then((response) => {
          const { areaCode, addressUpgradeModel = {} } = response || {};
          if (areaCode) {
            if (addressUpgradeModel.newAddress) {
              this.onAnalyzeFail('已按最新行政区域更新地址');
            }
            this.triggerEvent('analyze', response);
            this.onClose();
          } else {
            this.onAnalyzeFail();
          }
        })
        .catch((err) => {
          this.onAnalyzeFail(err.msg);
        });
    },

    // 防止输得快的时候闪烁，就不写入到data里了
    onInput(event) {
      this.address = event.detail;

      if (this.address && !this.data.showClearBtn) {
        this.setData({ showClearBtn: true });
      }

      // clear的时候要写一下data,不然不会清空
      if (!this.address) {
        this.setYZData({ address: '' });
        this.setData({ showClearBtn: false });
      }
    },

    onClose() {
      this.triggerEvent('close');
    },

    onClear() {
      // setYZData 无效，清空之后input中还有数据
      this.address = '';
      this.setData({ address: '', showClearBtn: false });
    },

    onAnalyzeFail(msg = '') {
      Toast({
        context: this,
        message: msg || '无法解析出完整的行政区，请手动选择',
      });
    },
  },
});
