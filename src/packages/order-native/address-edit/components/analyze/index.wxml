<view
  class="analyze-input-wrapper"
>
  <van-field
    value="{{ address }}"
    label="智能识别"
    title-width="72px"
    border="{{ false }}"
    type="textarea"
    autosize="{{ { minHeight: 72 } }}"
    class="analyze-input"
    custom-style="padding: 9px 15px;"
    input-class="analyze-input__input"
    placeholder="请粘贴或输入地址，可自动识别姓名、手机号码和地址，如：浙江省杭州市西湖区黄龙万科中心G25，张三，132****2343"
    bind:input="onInput"
  />
</view>

<view class="analyze-input__bar">
  <view wx:if="{{ showClearBtn }}" class="analyze-input__manual" bindtap="onClear">
    清空
  </view>
  <view class="analyze-input__manual" bindtap="onClose">
    取消
  </view>
  <view class="analyze-input__manual analyze-input__manual--border" bindtap="onAnalyze">
    识别
  </view>
</view>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />