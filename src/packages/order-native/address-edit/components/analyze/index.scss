.analyze-input {
  display: block;
  font-size: 12px;

  &-wrapper {
    position: relative;

    &::before {
      position: absolute;
      box-sizing: border-box;
      transform-origin: center;
      content: '';
      pointer-events: none;
      top: 0;
      left: 16px;
      right: 16px;
      border-top: 1px solid #ebedf0;
      transform: scaleY(0.5);
    }
  }

  &__input {
    line-height: 18px;
    padding-top: -3px;
  }

  &__bar {
    background: #fff;
    padding: 0 12px 14px;
    display: flex;
    justify-content: flex-end;
  }

  &__manual {
    color: #646566;
    font-size: 14px;
    line-height: 26px;
    width: 64px;
    text-align: center;
    border-radius: 12px;
    overflow: hidden;

    &:not(:last-child) {
      width: 50px;
    }

    &:last-child {
      margin-left: 10px;
    }

    &--border {
      border: 1px solid;
      line-height: 24px;
      color: var(--theme-general);
      border-color: var(--theme-general);
    }
  }
}
