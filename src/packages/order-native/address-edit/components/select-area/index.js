import Toast from '@vant/weapp/dist/toast/toast';
import { VanxComponent } from 'pages/common/wsc-component/index';

VanxComponent({
  properties: {
    value: {
      type: String,
      value: '',
    },
    areaList: {
      type: Object,
      value: {},
    },
    loading: {
      type: Boolean,
      value: false,
    },
  },
  methods: {
    handleAreaConfirm({ detail }) {
      let { values } = detail; //eslint-disable-line
      values = values.filter((value) => !!value);

      if (values.some((value) => !value.code)) {
        Toast('请选择地区');
        return;
      }

      const area = this.selectComponent('#area');

      if (area) {
        const detail = area.getDetail();

        this.triggerEvent('confirm', {
          ...detail,
          formvalue: {
            'form.areaCode': detail.code,
            'form.country': detail.country,
            'form.province': detail.province,
            'form.city': detail.city,
            'form.county': detail.county,
          },
        });
      }
    },
    handleAreaCancel() {
      this.triggerEvent('cancel');
    },
  },
});
