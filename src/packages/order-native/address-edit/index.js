import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import { queryGeo, deleteAddress } from '@/packages/order-native/utils/request';
import config from '@/packages/order-native/common/config';
import AddressService from '@/packages/order-native/biz-service/address-service';
import SaasConfig, { initSdk } from './saas/page-config';
import { ADDRESS_TYPE } from '@/packages/order-native/utils/constants-address';
import {
  MenuButtonBoundingClientRect,
  getHeight,
} from 'shared/utils/nav-config';
import getSystemInfo from 'shared/utils/browser/system-info';

import { analyzeAreaCode, getAreaList } from './utils';
import { logKeepAddress, logPosition } from './log';
import { themeStyleTemplate } from './theme';

const app = getApp();
const extendPageConfig = ECLOUD_MODE ? SaasConfig : {};

WscPage(extendPageConfig, {
  data: {
    // 表单数据
    form: {},
    map: {}, // 地图切片数据
    errorInfo: {},
    errorInfoMsg: {},
    showAnalyzeInput: false,
    isSaving: false,
    areaList: {},
    theme: themeStyleTemplate,
    showBackupDialog: false,
    showMarkerLabel: true,
    showGetWechatAddress: true,
  },

  pageShunt(query) {
    try {
      const pages = getCurrentPages() || [];
      const detail = {
        pageStack: pages.map(_ => _.route),
        query,
      }
      /**
       * 用于判断原生地址编辑页是否还有流量存在
       * 目标是全部流量到转到中台化地址编辑页，并且下线原生地址编辑页
       * 何时能下线该页面？
       * 流量 = 0 超过1个月
       * 访问：https://ops.qima-inc.com/v3/skynet/#/main/prod/log/search/all-log?appName=wsc&order=DESC&queryString=%E8%AE%BF%E9%97%AE%E5%8E%9F%E7%94%9F%E5%9C%B0%E5%9D%80%E7%BC%96%E8%BE%91%E9%A1%B5
       * 选择 7天查询
       */
      app.logger.appError({
        name: 'goto-native-address-edit',
        message: '访问原生地址编辑页',
        detail,
      });
    } catch (e) {
      console.warn('nemo', e);
    }

    // const res = await app.request({
    //   path: '/wsctrade/rantashunt/getWeappShuntConfig.json',
    //   data: {
    //     config: {
    //       addressEdit: 'useWeappRantaTeeAddressEdit',
    //     },
    //   },
    //   method: 'POST',
    // });
    //
    // if (res.address_edit) {
    //   wx.redirectTo({
    //     url: args.add(
    //       '/packages/trade-buy-subpage/order/address-edit/index',
    //       query
    //     ),
    //   });
    // }
  },

  async onLoad({ dbid, addressid, isforcepoiselect, ishousenumber } = {}) {
    this.pageShunt({ dbid, addressid, isforcepoiselect, ishousenumber });

    const { id, list = [], delta = 2, forcePoiSelect } = app.db.get(dbid) || {};

    const newIsForcePoiSelect = isforcepoiselect && JSON.parse(isforcepoiselect);
    const newIsHouseNumber = ishousenumber && JSON.parse(ishousenumber);

    const editingAddress = list.find((item) => item.id === id) || {};
    this.setYZData({
      form: {
        ...editingAddress,
        isDefault: Boolean(editingAddress.isDefault),
      },
      forcePoiSelect,
      isforcepoiselect: newIsForcePoiSelect,
      ishousenumber: newIsHouseNumber,
    });
    // wx.setNavigationBarTitle({ title: id ? '编辑地址' : '新增地址' });
    const { canUseNav } = MenuButtonBoundingClientRect;
    if (canUseNav) {
      const title = id ? '编辑地址' : '新增地址';
      this.initNavBar(title);
    }
    this.setYZData({
      canUseNav
    });

    this.on(config.eventKey.addressMap, (address) => {
      const { lat: latitude, lon: longitude, location: markerLabel } = address;
      this.setYZData({
        form: {
          ...this.data.form,
          ...address,
        },
        map: this.genMapData({ latitude, longitude, markerLabel }),
      });
    });

    this.list = list;
    this.delta = delta;

    getAreaList().then((areaList) => {
      this.areaList = areaList;
      this.setYZData({ areaList });
    });

    if (addressid) {
      this.getAddressList({ addressid, newIsForcePoiSelect });
    }
    initSdk(this);
  },

  initNavBar(title) {
    const systemInfo = getSystemInfo();
    const { statusBarHeight, windowWidth, fontSizeSetting } = systemInfo;
    const {
      top,
      height: menuButtonHeight,
      right,
      width,
    } = MenuButtonBoundingClientRect;
    const height = getHeight();
    const paddingLeft = windowWidth - right;
    const textWidth = windowWidth - width * 2 - paddingLeft * 4;
    const customIconStyle = `width: ${menuButtonHeight}px;height: ${menuButtonHeight}px;left: ${paddingLeft}px;top: ${top}px;`;
    this.setYZData({
      titleText: title,
      height,
      paddingTop: statusBarHeight,
      fontSizeSetting,
      textWidth,
      customIconStyle,
    });
  },

  getAddressService() {
    if (this.addressService == null) {
      this.addressService = new AddressService();
    }

    return this.addressService;
  },

  onShowAnalyze() {
    this.setYZData({ showAnalyzeInput: true });
  },

  onCloseAnalyze() {
    this.setYZData({ showAnalyzeInput: false });
  },

  onClickIcon() {
    logPosition();

    const { lon, lat } = this.data.form;
    wx.navigateTo({
      url: `/packages/order-native/address-map/index?lng=${lon || ''}&lat=${
        lat || ''
      }`,
    });
  },

  onClickDetail() {
    const { forcePoiSelect, form, isforcepoiselect } = this.data;

    if(isforcepoiselect) {
      this.onClickIcon();
      return ;
    }

    if (forcePoiSelect && !form.downgrade) {
      this.onClickIcon();
    }
  },

  onAnalyze(event) {
    this.onClear();
    const res = event.detail;
    analyzeAreaCode(res.areaCode).then((district) => {
      this.setYZData({
        form: {
          lon: '',
          lat: '',
          ...res,
          ...district,
          type: 1,
        },
      });

      this.validateAddress();
    });
  },

  onFocus(event) {
    const { type } = event.currentTarget.dataset;
    this.setYZData({ [`errorInfo.${type}`]: false });
  },

  // 更新表单数据
  handleInputForm(event) {
    const { type } = event.currentTarget.dataset;
    const value = event.detail;

    this.setYZData({ [`form.${type}`]: value });

    if (type === 'addressDetail') {
      this.handleDetailChange(value);
    }
  },

  handleFormBlur({ detail, currentTarget }) {
    const { type } = currentTarget.dataset;
    const { value } = detail;
    const { form } = this.data;

    if (form[type] !== value) {
      this.setYZData({ [`form.${type}`]: value });
    }
  },

  // 展示建议地址
  handleDetailChange() {
    // 地址变更时，重置经纬度
    const { form } = this.data;
    form.lat = '';
    form.lon = '';
    form.type = ADDRESS_TYPE.INPUT;
    this.setYZData({ form });
  },

  // 删除收货地址
  handleDelete() {
    this.setYZData({ isDeleting: true });
    deleteAddress(this.data.form.id)
      .then(() => {
        Toast.success('地址删除成功');
        app.trigger(config.eventKey.addressDelete, this.data.form.id);
        this.setYZData({ isDeleting: false });
        wx.navigateBack();
      })
      .catch((err) => {
        Toast.fail(err.msg || '地址删除失败');
        this.setYZData({ isDeleting: false });
      });
  },

  // 取消选择省市区
  handleAreaCancel() {
    this.setYZData({
      showArea: false,
    });
  },

  // 选择省市区
  handleAreaShow() {
    const { forcePoiSelect, form } = this.data;
    if (!forcePoiSelect || form.downgrade) {
      wx.hideKeyboard();
      this.setYZData({ showArea: true });
    } else {
      // 强制选点 且 未降级 的情况下直接进入地图页面
      this.onClickIcon();
    }
  },

  // 确定省市区
  handleAreaConfirm({ detail }) {
    const { formvalue = {} } = detail;

    this.setYZData({
      showArea: false,
      'form.lon': '',
      'form.lat': '',
      'form.type': 1,
      ...formvalue,
    });
    this.validateAddress();
  },

  // 保存收货地址
  handleSave() {
    const {ishousenumber, form} = this.data;
    if (this.data.isSaving) {
      return;
    }
    if(ishousenumber && !form.houseNumber) {
      Toast('请输入门牌号')
      return ;
    }

    const addressService = this.getAddressService();

    Toast.loading();
    this.setYZData({ isSaving: true });

    const { form: address } = this.data;

    addressService
      .saveAddress(address, this.list)
      .then((response) => {
        this.requestOver(address, response);
      })
      .catch((err) => {
        Toast.clear();
        const key = Object.keys(err.errorInfo)[0]
        const errorInfoMsg = {
          [key]: err.msg,
        };
        this.setYZData({
          errorInfo: err.errorInfo,
          errorInfoMsg,
          isSaving: false,
          showMarkerLabel: true
        });
        Toast(err.msg);
      });
  },

  // 请求成功处理
  requestOver(address, response) {
    const { delta = 2 } = this;
    Toast.clear();

    const id = address.id || response.value;
    const newAddress = { ...address, id };

    logKeepAddress(newAddress);

    const downgradeMap =
      wx.getStorageSync(config.cacheKey.addressDowngrade) || {};
    if (address.downgrade) {
      downgradeMap[id] = {
        expired: Date.now() + 30 * 24 * 60 * 60 * 1000,
      };
      wx.setStorage({
        key: config.cacheKey.addressDowngrade,
        data: downgradeMap,
      });
    } else if (downgradeMap[id] != null) {
      delete downgradeMap[id];
      wx.setStorage({
        key: config.cacheKey.addressDowngrade,
        data: downgradeMap,
      });
    }
    app.trigger(config.eventKey.addressSave, newAddress);
    this.setYZData({ isSaving: false, showMarkerLabel: true });
    wx.navigateBack({ delta });
  },

  // 保存微信地址
  onWechatSave(event) {
    const addressService = this.getAddressService();
    const address = event.detail;
    Toast.loading();
    addressService
      .requestSave(address)
      .then((response) => {
        this.requestOver(address, response);
      })
      .catch(() => {
        Toast.clear();
      });
  },

  // 使用当前定位地址
  onCurrentLocationUse(event) {
    const {
      ad_info: { adcode: areaCode },
      address,
      address_component: { province, city, district: county },
      formatted_addresses: { recommend },
      location: { lat: latitude, lng: longitude }
    } = event.detail;
    this.setYZData({
      form: {
        ...this.data.form,
        areaCode,
        addressDetail: `${address}${recommend}`,
        province,
        city,
        county
      },
      map: this.genMapData({ latitude, longitude, markerLabel: recommend })
    });
  },

  // 生成地图数据
  genMapData({
    latitude,
    longitude,
    markerLabel
  }) {
    return {
      latitude,
      longitude,
      markers: [
        {
          latitude,
          longitude,
          iconPath:
            'https://img01.yzcdn.cn/public_files/2019/05/23/a10e32e4bfeae621727c760f97274654.png',
          width: '22px',
          height: '22px',
          anchor: { x: 0, y: 0 },
        },
      ],
      markerLabel
    };
  },

  onBlur(event) {
    const { type } = event.currentTarget.dataset;
    if (type === 'addressDetail') {
      this.onChangeAddressDetail();
    }
  },

  // 变更详细地址重新计算地图切片数据
  onChangeAddressDetail() {
    const { province, city, county, addressDetail } = this.data.form;
    let map = {};
    queryGeo({ province, city, county, addressDetail })
      .then((result) => {
        const {
          lat: latitude,
          lon: longitude,
          formattedAddress: markerLabel,
        } = result;
        map = this.genMapData({ latitude, longitude, markerLabel });
        this.setYZData({ map });
      })
      .catch(() => {
        this.setYZData({ map });
      });
  },

  onChangeLabel(event) {
    const { label } = event.detail;
    this.setYZData({
      'form.label': label
    });
  },

  onBackTap() {
    this.setYZData({
      showBackupDialog: true,
      showMarkerLabel: false
    });
  },

  onClickBtnBack(event) {
    const { action } = event.currentTarget.dataset;
    switch (action) {
      case 'leave':
        wx.navigateBack();
        break;
      case 'save':
        this.handleSave();
      default:
        this.setYZData({
          showBackupDialog: false,
          showMarkerLabel: true
        });
    }
  },

  // 获取收货人地址列表
  getAddressList({ addressid, isforcepoiselect }) {
    app
      .request({
        path: '/wsctrade/uic/address/getAddressList.json',
        method: 'POST',
      })
      .then((addressList) => {
        const editingAddress =
          addressList.find((item) => item.id === +addressid) || {};
        this.setYZData({
          form: {
            ...editingAddress,
            isDefault: Boolean(editingAddress.isDefault),
          },
        });

        if (typeof isforcepoiselect !== 'undefined') {
          this.setYZData({
            forcePoiSelect: isforcepoiselect,
          });
        }

        const { canUseNav } = MenuButtonBoundingClientRect;
        if (canUseNav) {
          const title = addressid ? '编辑地址' : '新增地址';
          this.initNavBar(title);
        }
      });
  },

  // 校验
  validateAddress() {
    const addressService = this.getAddressService();
    const msg = addressService.getErrorMessageByKey(
      this.data.form,
      'areaCode'
    );
    const data = {
      errorInfo: {
        ...this.data.errorInfo,
        areaCode: !!msg.length,
      },
      errorInfoMsg: {
        ...this.data.errorInfoMsg,
        areaCode: msg,
      },
    }

    this.setYZData(data);

    return data;
  },

  // 清空当前地址信息
  onClear() {
    this.setYZData({
      form: {},
    });
  },
  showGetWechatAddress(show) {
    this.setYZData({ showGetWechatAddress: show });
  },
});
