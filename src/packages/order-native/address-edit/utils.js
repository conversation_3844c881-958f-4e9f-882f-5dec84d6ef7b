import { fetchAreaList } from '@/packages/order-native/utils/request';

let orderBuyAreaList = null;

export function getAreaList() {
  if (orderBuyAreaList == null) {
    return fetchAreaList().then(data => {
      orderBuyAreaList = data;
      return data;
    });
  }

  return Promise.resolve(orderBuyAreaList);
}

export function analyzeAreaCode(areaCode) {
  areaCode = String(areaCode);

  return getAreaList().then(areaList => {
    const { county_list, city_list, province_list } = areaList;
    const province = province_list[Math.floor(areaCode / 10000) * 10000] || '';
    const city = city_list[Math.floor(areaCode / 100) * 100] || '';
    const county = county_list[areaCode] || '';
    // 海外地址
    if (areaCode[0] === '9') {
      return {
        country: city,
        province: county,
        areaCode
      };
    }
    return {
      province,
      city,
      county,
      areaCode
    };
  });
}
