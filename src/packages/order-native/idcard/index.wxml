<page-container
  class="page-container {{ themeClass }} page-{{ deviceType }}"
  page-bg-color="#fff"
  forbid-copyright
>
  <van-field
    clearable
    label="姓名："
    border="{{ false }}"
    maxlength="{{ 15 }}"
    readonly="{{ orderCreated }}"
    value="{{ idcard.name }}"
    left-icon="contact"
    placeholder="请输入真实姓名"
    bind:blur="handleInputName"
  />

  <van-field
    clearable
    left-icon="idcard"
    label="身份证号："
    type="idcard"
    value="{{ idcard.number }}"
    readonly="{{ orderCreated }}"
    bind:blur="handleInputIdNumber"
    placeholder="请输入身份证号"
    maxlength="{{ 18 }}"
    border="{{ false }}"
  />

  <view class="idcard-gap" />

  <block wx:if="{{ order.needIdCardPhoto }}">
    <van-cell title="身份证照片" icon="photo-o" border="{{ false }}" />

    <view class="upload-panel">
      <uploader
        data-key="SET_IDCARD_BACK"
        class="flat-btn"
        text="身份证人像面"
        value="{{ idcard.backPhoto }}"
        bind:change="handleChange"
        disabled="{{ orderCreated }}"
      />
      <uploader
        data-key="SET_IDCARD_FRONT"
        class="flat-btn"
        text="身份证国徽面"
        value="{{ idcard.frontPhoto }}"
        bind:change="handleChange"
        disabled="{{ orderCreated }}"
      />
    </view>

    <van-cell title="参考样式" icon="photo-o" border="{{ false }}" />

    <view class="example-panel">
      <image src="https://img01.yzcdn.cn/wsc-h5-trade/<EMAIL>" class="example-image" mode="aspectFit"></image>
    </view>
  </block>

  <view wx:if="{{ !orderCreated }}" class="btn-wrapper">
    <van-button custom-class="theme-button" size="large" bind:click="handleSubmit">保存</van-button>
  </view>

  <view class="reason" bindtap="handleShowReason">为什么需要上传身份信息？</view>

  <!-- 底部有赞担保 -->
  <guarantee-footer
    wx:if="{{ guarantee.yzGuarantee }}"
    services="{{ guarantee.yzGuaranteeInfo.mainSupportContent }}"
    icons="{{ guarantee.yzGuaranteeDocs.icon }}"
  />

  <!-- 上传原因浮层 -->
  <modal value="{{ showReason }}" bind:close="handleCloseReason"></modal>

  <van-dialog id="van-dialog" />
</page-container>
