import { mapState, mapGetters } from '@youzan/vanx';
import { VanxPage } from 'pages/common/wsc-page/index';
import store from '@/packages/order-native/store/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { validateIdCard } from '@/packages/order-native/common/validator';
import { updateAddress } from './fetch';

VanxPage({
  store,

  mapData: {
    ...mapState(['idcard', 'order', 'guarantee']),
    ...mapGetters(['orderCreated']),
  },

  data: {
    showReason: false,
  },

  // 输入姓名，先不录入store，防止输入卡顿
  handleInputName(event) {
    this.idcardName = event.detail.value;
  },

  // 输入身份证，先不录入store，防止输入卡顿
  handleInputIdNumber(event) {
    this.idcardNumber = event.detail.value;
  },

  // 上传图片
  handleChange(event) {
    if (this.data.orderCreated) {
      return;
    }
    const { key } = event.currentTarget.dataset;
    this.$commit(key, cdnImage(event.detail));
  },
  // 显示上传原因
  handleShowReason() {
    this.setYZData({ showReason: true });
  },
  // 关闭上传原因
  handleCloseReason() {
    this.setYZData({ showReason: false });
  },
  // 提交信息
  handleSubmit() {
    setTimeout(() => {
      // idcardName 和 idcardNumber 不为 undefined 说明修改过，录入store
      this.idcardName != null &&
        this.$commit('SET_IDCARD_NAME', this.idcardName);
      this.idcardNumber != null &&
        this.$commit('SET_IDCARD_NUMBER', this.idcardNumber);

      const { state, getters } = this.$store;
      const message = validateIdCard(state);
      if (message) {
        return this.$dispatch('TOAST', message);
      }

      const requests = [];
      // 实名校验身份信息
      if (state.order.needRealNameAuth) {
        const verifyPromise = this.$dispatch('VERIFY_IDCARD').then((res) => {
          const { verifyPassed } = res || {};
          // 实名校验通过后 修改收货人姓名 用团长地址时不更新
          if (
            verifyPassed &&
            !getters.receiveByGroupHeader &&
            !getters.hasHaitaoGoods
          ) {
            const address = {
              ...getters.currentAddress,
              userName: state.idcard.name,
            };
            return updateAddress(address).then(() => {
              this.$commit('UPDATE_ADDRESS', address);
            });
          }
        });
        requests.push(verifyPromise);
      }

      Promise.all(requests).then(() => {
        wx.navigateBack();
      });
    }, 100);
  },
});
