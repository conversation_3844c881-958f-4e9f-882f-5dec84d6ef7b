.preview {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.preview__img {
  width: 100%;
  height: 100%;
}

.preview__fade {
  z-index: 1;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  transition: transform linear 0.1s;
}

.preview__content {
  display: flex;
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #fff;
}

.preview__content__icon {
  font-size: 28px;
}

.preview__content__text {
  font-size: 12px;
}

.flat-btn {
  box-sizing: border-box;
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 1px solid transparent;
  background: #f3f7fb;
}

.flat-btn__icon {
  width: 40px;
  height: 32px;
}

.flat-btn__text {
  margin-top: 10px;
  color: #afb9c3;
  font-size: 12px;
  line-height: 16px;
}

.angle {
  position: absolute;
  width: 20px;
  height: 20px;
}

.angle.top {
  top: -1px;
  border-top: 1px solid #ddd;
}

.angle.left {
  left: -1px;
  border-left: 1px solid #ddd;
}

.angle.bottom {
  bottom: -1px;
  border-bottom: 1px solid #ddd;
}

.angle.right {
  right: -1px;
  border-right: 1px solid #ddd;
}
