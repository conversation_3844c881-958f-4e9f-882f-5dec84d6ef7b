// 从相册、相机获取图片
export function chooseImage() {
  return new Promise((resolve, reject) => {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        console.log(res);
        resolve(res);
      },
      fail: reject
    });
  });
}

// 查询图片信息
export function getImageInfo(src) {
  return new Promise((resolve, reject) => {
    wx.getImageInfo({
      src,
      success: (res) => {
        console.log(res);
        resolve(res);
      },
      fail: reject
    });
  });
}

/**
 * 计算压缩后的图片尺寸
 * @param   {Number} maxHeight 最大高度
 * @param   {Number} maxWidth  最大宽度
 * @param   {Number} height    图片高度
 * @param   {Number} width     图片宽度
 * @returns {Promise}
 */
export function getImageSize(maxHeight, maxWidth, height, width) {
  let contextHeight = height;
  let contextWidth = width;
  if (contextHeight > maxHeight) {
    contextWidth *= maxHeight / contextHeight;
    contextHeight = maxHeight;
  }
  if (contextWidth > maxWidth) {
    contextHeight *= maxWidth / contextWidth;
    contextWidth = maxWidth;
  }
  return { height: contextHeight, width: contextWidth };
}

export function loadImage(src) {
  const app = getApp();
  return new Promise((resolve, reject) => {
    app.downloadFile({
      url: src,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: reject
    });
  });
}
