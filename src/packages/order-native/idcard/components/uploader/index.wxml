<view wx:if="{{ tempValue || value }}" bindtap="handleShowGallary" class="preview">
  <!-- 预览图 -->
  <image src="{{ tempValue || value }}" class="preview__img" id="J_image" mode="aspectFill"></image>
  <!-- 背景 上传进度 -->
  <view wx:if="{{ showFade }}" class="preview__fade" style="transform: translateY({{ 100 - progress }}%)"></view>
  <!-- 上传状态 图标 文字 -->
  <view class="preview__content">
    <van-icon name="{{ hint.icon }}" class="preview__content__icon" />
    <view class="preview__content__text">{{ hint.text }}</view>
  </view>
</view>

<!-- 上传按钮 -->
<view wx:else class="flat-btn" bindtap="handleUpload">
  <image src="https://img01.yzcdn.cn/wsc-h5-trade/<EMAIL>" class="flat-btn__icon" mode="aspectFit"></image>
  <text class="flat-btn__text">{{ text }}</text>
  <view class="angle left top"></view>
  <view class="angle left bottom"></view>
  <view class="angle right top"></view>
  <view class="angle right bottom"></view>
</view>

<!-- 画布 用于合成图片 -->
<canvas
  wx:if="{{ showCanvas }}"
  style="width: {{ canvasSize.width }}px; height: {{ canvasSize.height }}px; position: fixed; left: -{{ canvasSize.width }}px; top: height: {{ canvasSize.height }}px;"
  canvas-id="firstCanvas"
/>

<van-toast id="van-toast" />

<gallary
  show="{{ showGallary }}"
  disabled="{{ disabled }}"
  url="{{ tempValue || value }}"
  bind:submit="handleUpload"
  bind:close="handleCloseGallary"
/>
