import WscComponent from 'pages/common/wsc-component/index';
import upload from 'utils/upload';
import Toast from '@vant/weapp/dist/toast/toast';
import { hintMap, mark } from './config';
import { chooseImage, getImageInfo, getImageSize } from './utils';

WscComponent({
  properties: {
    // 上传按钮文字
    text: String,
    // 图片链接绑定
    value: String,
    // 禁用上传
    disabled: Boolean
  },
  data: {
    // 上传进度
    progress: 0,
    // 是否显示背景
    showFade: false,
    // 是否显示图片预览
    showGallary: false,
    // 预览图连接
    tempValue: '',
    // 上传状态文字、图标
    hint: {
      icon: '',
      text: ''
    },
    showCanvas: false,
    canvasSize: {
      height: 0,
      width: 0
    }
  },
  methods: {
    handleShowGallary() {
      this.setYZData({ showGallary: true });
    },
    handleCloseGallary() {
      this.setYZData({ showGallary: false });
    },
    // 上传图片
    uploadImage(file) {
      return new Promise((resove, reject) => {
        upload({
          file,
          success: resove,
          progress: ({ progress } = {}) => {
            this.setYZData({ progress });
          },
          fail: (err) => {
            reject({
              ...err,
              msg: err.msg || '上传图片失败',
            });
          }
        });
      });
    },

    handleMark(image) {
      return new Promise((resolve, reject) => {
        const { height, width } = getImageSize(500, 500, image.height, image.width);
        setTimeout(() => {
          this.setYZData(
            {
              showCanvas: true,
              canvasSize: {
                height,
                width
              }
            },
            () => {
              const context = wx.createCanvasContext('firstCanvas', this);
              context.drawImage(image.path, 0, 0, width, height);

              const scale = width / 2 / mark.width;
              const markWidth = mark.width * scale;
              const markHeight = mark.height * scale;

              context.drawImage(
                mark.url,
                (width - markWidth) / 2,
                (height - markHeight) / 2,
                markWidth,
                markHeight
              );

              context.draw(false, () => {
                wx.canvasToTempFilePath({
                  canvasId: 'firstCanvas',
                  fileType: 'jpg',
                  quality: '0.7',
                  success: res => {
                    this.setYZData({
                      showCanvas: false,
                      showGallary: false,
                      tempValue: res.tempFilePath,
                      showFade: true,
                      progress: 0,
                      hint: hintMap.progress,
                    }, () => { resolve(res.tempFilePath) });
                  },
                  fail: reject
                }, this);
              });
            }
          );
        }, 0);
      });
    },
    // 点击上传图片
    handleUpload() {
      if (this.data.disabled) {
        return;
      }
      chooseImage()
        .then(res => res.tempFilePaths[0])
        .then(getImageInfo)
        .then(this.handleMark.bind(this))
        .then(this.uploadImage.bind(this))
        .then(res => res.attachment_url)
        .then(url => {
          this.triggerEvent('change', url);
          this.setYZData({ hint: hintMap.success });
        })
        .catch(err => {
          this.setYZData({ hint: hintMap.fail });
          Toast(err.msg || '选择图片失败');
        });
    }
  }
});
