import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    icon: String,
    text: String,
    value: Boolean
  },
  data: {
    shopName: ''
  },
  attached() {
    const app = getApp();
    app.getShopInfo().then(data => {
      this.setYZData({ shopName: data.shop_name });
    });
  },
  methods: {
    handleClose() {
      this.triggerEvent('close');
    }
  }
});
