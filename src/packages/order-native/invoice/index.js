import { VanxPage } from 'pages/common/wsc-page/index';
import store from '@/packages/order-native/store/index';
import { mapState, mapGetters, mapActions } from '@youzan/vanx';
import theme from 'shared/common/components/theme-view/theme';
import Toast from '@vant/weapp/dist/toast/toast';
import { config } from './constants';

VanxPage({
  store,

  mapData: {
    ...mapState(['invoice', 'invoice_design']),
    ...mapGetters([
      'isElectronic',
      'raiseTypeIndex'
    ]),
    ...mapActions(['GET_INVOICE_DESIGN'])
  },

  data: {
    config
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: this.data.isElectronic
        ? '申请电子发票'
        : '填写开票信息'
    });

    theme.getThemeColor('general').then(color => {
      this.$commit('SET_INVOICE_THEME', color);
    });

    this.$dispatch('GET_INVOICE_DESIGN');
  },

  onTabChange(event) {
    this.$commit('ASSIGN_INVOICE_COMMON', {
      raiseType: event.detail.index === 1
        ? config.raiseType.personal
        : config.raiseType.enterprise
    });
  },

  onEmailChange(event) {
    this.$commit('ASSIGN_INVOICE_COMMON', {
      emailList: [event.detail]
    });
  },

  validateSubmitData(data) {
    const {
      raiseType, userName, emailList, taxpayerId
    } = data;
    let msg = null;

    const isEmail = (email) => {
      const reg = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
      return reg.test(email);
    };

    if (!userName) {
      msg = '请输入发票抬头';
    } else if (userName.length > 100) {
      msg = '发票抬头最多100个字符';
    } else if (raiseType === config.raiseType.enterprise && !taxpayerId) {
      msg = '请输入企业税号';
    } else if (raiseType === config.raiseType.enterprise && taxpayerId && taxpayerId.length > 20) {
      msg = '企业税号最多20个字符';
    } else if (!emailList[0]) {
      msg = '请输入收票邮箱';
    } else if (!isEmail(emailList[0])) {
      msg = '请填写正确的邮箱';
    }

    msg && Toast(msg);
    return !msg;
  },

  getInvoiceData() {
    const {
      invoice = {}
    } = this.data;

    const contentData = invoice[invoice.common.raiseType];
    return Object.assign({}, invoice.common, contentData);
  },

  submit() {
    const data = this.getInvoiceData();

    if (!this.validateSubmitData(data)) {
      return;
    }

    this.$commit('SET_INVOICE_SUBMIT', data);
    wx.navigateBack();
  }
});
