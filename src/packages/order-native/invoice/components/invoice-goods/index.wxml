<view
  wx:if="{{ showInvoiceGoodsCell }}"
  class="invoice__goods"
>
  <van-cell class="invoice__goods-cell">
    <view class="invoice__goods-cell__value">
      <view class="invoice__goods-cell__text">下单商品中有{{ invoiceGoods.noCode.length }}种商品无法开票</view>
      <van-icon
        name="info-o"
        class="invoice__goods-cell__icon"
        color="{{ invoice.themeGeneral }}"
        bind:click="triggerDialog"
      />
    </view>
    <view class="invoice__goods-cell__text">此处为申请增值税普通发票（电子发票），如需专用发票请联系商家</view>
  </van-cell>

  <van-dialog
    use-slot
    show="{{ showDialog }}"
    confirmButtonText="我知道了"
    bind:confirm="triggerDialog"
  >
    <view class="invoice__goods-list">
      <!-- 无法开票 -->
      <view
        wx:if="{{ invoiceGoods.noCode.length }}"
        class="invoice__goods-item"
      >
        <view class="invoice__goods-item__title">以下{{ invoiceGoods.noCode.length }}种商品无法开票</view>
        <view
          wx:for="{{ invoiceGoods.noCode }}"
          wx:key="{{ index }}"
          class="invoice__goods-item__name"
        >
          {{ index + 1 }}. {{ item.title }}
        </view>
      </view>

      <!-- 支持开票 -->
      <view
        wx:if="{{ invoiceGoods.hasCode.length }}"
        class="invoice__goods-item"
      >
        <view class="invoice__goods-item__title">以下{{ invoiceGoods.hasCode.length }}种商品支持开票</view>
        <view
          wx:for="{{ invoiceGoods.hasCode }}"
          wx:key="{{ index }}"
          class="invoice__goods-item__name"
        >
          {{ index + 1 }}. {{ item.title }}
        </view>
      </view>
    </view>
  </van-dialog>
</view>