.invoice__goods {
  margin-bottom: 10px;
}

.invoice__goods-cell {
  &__value {
    display: flex;
    align-items: center;
  }

  &__text {
    color: #999;
    text-align: left;
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-left: 10px;
    font-size: 16px;
  }
}

.invoice__goods-list {
  padding: 25px 20px;
  box-sizing: border-box;
  max-height: 235px;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.invoice__goods-item {
  &:not(:nth-last-of-type(1)) {
    margin-bottom: 15px;
  }

  &__title {
    margin-bottom: 13px;
    font-weight: normal;
    font-size: 16px;
    color: #333;
  }

  &__name {
    color: #666;
    line-height: 25px;
  }
}