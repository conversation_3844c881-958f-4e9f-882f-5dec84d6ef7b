import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['invoice']),
    ...mapGetters([
      'showInvoiceGoodsCell',
      'invoiceGoods'
    ])
  },

  data: {
    showDialog: false
  },

  methods: {
    triggerDialog() {
      this.setYZData({
        showDialog: !this.data.showDialog
      });
    }
  }
});
