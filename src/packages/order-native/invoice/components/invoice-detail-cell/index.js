import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import Toast from '@vant/weapp/dist/toast/toast';
import store from '@/packages/order-native/store/index';
import { config } from '../../constants';

const tagList = [
  {
    label: '商品明细',
    value: config.invoiceDetailType.itemDetail,
    desc: '发票内容将显示详细商品名称',
    disabled: false
  },
  {
    label: '商品类别',
    value: config.invoiceDetailType.itemCategory,
    desc: '发票内容将显示商品相关类别名称',
    disabled: false
  }
];

const desc = {
  [config.invoiceDetailType.itemDetail]: '发票内容将显示详细商品名称',
  [config.invoiceDetailType.itemCategory]: '发票内容将显示商品相关类别名称'
};

VanxComponent({
  store,

  mapData: {
    ...mapState(['invoice', 'display']),
    ...mapGetters([
      'detailDisabled'
    ])
  },

  properties: {
    raiseType: String
  },

  data: {
    config,
    tagList,
    desc
  },

  ready() {
    let mutation = '';
    const { invoiceContent } = this.data.display;

    switch (this.data.raiseType) {
      case config.raiseType.enterprise:
        mutation = 'ASSIGN_INVOICE_ENTERPRISE';
        break;
      case config.raiseType.personal:
        mutation = 'ASSIGN_INVOICE_PERSONAL';
        break;
      default: break;
    }

    let invoiceDetailType = this.data.invoice[this.data.raiseType].invoiceDetailType;

    // 开票内容 10：开启商品明细开票；01：开启商品类目开票；11：两种方式均开启。
    switch (invoiceContent) {
      case 10:
        invoiceDetailType = config.invoiceDetailType.itemDetail;
        tagList[0].disabled = false;
        tagList[1].disabled = true;
        break;
      case 1:
        invoiceDetailType = config.invoiceDetailType.itemCategory;
        tagList[0].disabled = true;
        tagList[1].disabled = false;
        break;
      default:
        invoiceDetailType = this.data.invoice[this.data.raiseType].invoiceDetailType;
        tagList[0].disabled = false;
        tagList[1].disabled = false;
        break;
    }

    this.$commit(mutation, {
      invoiceDetailType
    });

    this.setYZData({
      mutation,
      tagList
    });
  },

  methods: {
    onTagClick(e) {
      const invoiceDetailType = e.target.dataset.value;

      if (
        this.data.detailDisabled
        && invoiceDetailType === config.invoiceDetailType.itemDetail
      ) {
        return Toast('订单中商品名称过长，无法按商品明细开具电子发票');
      }

      const {
        invoice,
        raiseType
      } = this.data;

      if (invoiceDetailType !== invoice[raiseType].invoiceDetailType) {
        this.$commit(this.data.mutation, {
          invoiceDetailType
        });
      }
    }
  }
});
