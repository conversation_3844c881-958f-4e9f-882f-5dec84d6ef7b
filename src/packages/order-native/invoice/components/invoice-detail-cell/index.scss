.detail-cell {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 15px;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;

  &__title {
    max-width:90px;
    min-width:90px;
    flex: 1;
    font-size: 14px;
    line-height: 1;
  }

  &__value {
    flex: 1;
    display: flex;
    align-items: center;

    &__tag {
      display: inline-block;
      margin-right: 14px;
      width: 70px;
      height: 30px;
      border: 1px solid #e5e5e5;
      border-radius: 15px;
      box-sizing: border-box;
      line-height: 28px;
      text-align: center;
      color: #666;
      font-size: 12px;
    }
  }
}

.detail-cell-desc {
  padding: 8px 15px;
  font-size: 12px;
  color: #666;
}