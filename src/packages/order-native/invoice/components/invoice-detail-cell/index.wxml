<view class="detail-cell van-hairline--top">
  <view class="detail-cell__title">开票内容</view>
  <view class="detail-cell__value">
    <theme-view
      wx:for="{{ tagList }}"
      wx:key="{{ item.value }}"
      wx:if="{{ !item.disabled }}"
      custom-class="detail-cell__value__tag"
      border="{{ item.value === invoice[raiseType].invoiceDetailType ? 'general' : '' }}"
      color="{{ item.value === invoice[raiseType].invoiceDetailType ? 'general' : '' }}"
      data-value="{{ item.value }}"
      bindtap="onTagClick"
    >
      {{ item.label }}
    </theme-view>
  </view>
</view>

<view class="detail-cell-desc">{{ desc[invoice[raiseType].invoiceDetailType] }}</view>
