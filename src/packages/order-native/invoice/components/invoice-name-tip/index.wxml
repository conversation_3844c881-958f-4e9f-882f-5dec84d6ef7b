<view
  class="tip"
  hidden="{{ !show || !list.length }}"
>
  <view
    class="tip__list"
  >
    <view
      wx:for="{{ list }}"
      wx:key="{{ index }}"
      class="tip__list__item"
      data-name="{{ item.corpName }}"
      data-id="{{ item.companyId }}"
      bindtap="onItemClick"
    >
      <view>{{ item.corpName }}</view>
      <view
        wx:if="{{ (index + 1) !== list.length }}"
        class="tip__line"
      />
    </view>
  </view>

  <view class="tip__line"/>

  <view
    class="tip__close van-hairline--top"
    bindtap="onClose"
  >
    <van-icon
      name="arrow-up"
      color="{{ invoice.themeGeneral }}"
    />
  </view>
</view>