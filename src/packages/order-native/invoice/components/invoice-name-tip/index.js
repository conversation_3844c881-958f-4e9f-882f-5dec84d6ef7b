import { VanxComponent } from 'pages/common/wsc-component/index';
import store from '@/packages/order-native/store/index';
import { mapState } from '@youzan/vanx';

VanxComponent({
  store,

  mapData: {
    ...mapState(['invoice']),
  },

  properties: {
    show: Boolean,
    list: Array
  },

  methods: {
    onItemClick(e) {
      this.triggerEvent('select', e.currentTarget.dataset);
      this.onClose();
    },

    onClose() {
      this.triggerEvent('close');
    },
  }
});
