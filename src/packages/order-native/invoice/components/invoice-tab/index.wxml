<view class="invoice-tab">
  <van-cell-group
    border="{{ false }}"
  >
    <van-field
      value="{{ invoice[raiseType].userName }}"
      label="发票抬头"
      placeholder="{{ userNamePlaceholder }}"
      bind:change="onUserNameChange"
      bind:focus="onUserNameFocus"
    >
      <theme-view
        slot="button"
        color="general"
        bindtap="onWxTitleClick"
      >
        使用微信抬头
      </theme-view>
    </van-field>

    <invoice-name-tip
      show="{{ tipShow }}"
      list="{{ tipList }}"
      bind:select="onUserNameSelect"
      bind:close="onUserNameClose"
    />

    <van-field
      wx:if="{{ raiseType === config.raiseType.enterprise}}"
      value="{{ invoice[raiseType].taxpayerId }}"
      bind:change="onIdChange"
      label="企业税号"
      placeholder="请输入企业税号"
    />

    <invoice-detail-cell
      raise-type="{{ raiseType }}"
    />
  </van-cell-group>
</view>