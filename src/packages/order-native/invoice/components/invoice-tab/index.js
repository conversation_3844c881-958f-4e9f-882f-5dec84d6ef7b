import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';
import debounce from '@youzan/weapp-utils/lib/debounce';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import Toast from '@vant/weapp/dist/toast/toast';
import authorize from '@/helpers/authorize';
import { config } from '../../constants';

const app = getApp();

const INVOICE_STATUS = {
  CANCEL: 'chooseInvoiceTitle:fail cancel',
  OK: 'chooseInvoiceTitle:ok'
};

VanxComponent({
  store,

  mapData: {
    ...mapState(['invoice']),
    ...mapGetters([
    ])
  },

  properties: {
    raiseType: String
  },

  data: {
    config,
    userNamePlaceholder: '',
    mutation: '',
    tipShow: false,
    tipList: []
  },

  ready() {
    let userNamePlaceholder = '';
    let mutation = '';

    switch (this.data.raiseType) {
      case config.raiseType.enterprise:
        userNamePlaceholder = '请输入企业名称';
        mutation = 'ASSIGN_INVOICE_ENTERPRISE';
        break;
      case config.raiseType.personal:
        userNamePlaceholder = '请输入个人名称';
        mutation = 'ASSIGN_INVOICE_PERSONAL';
        break;
      default: break;
    }

    this.setYZData({
      userNamePlaceholder,
      mutation
    });
  },

  methods: {
    onUserNameChange(e) {
      this.$commit(this.data.mutation, {
        userName: e.detail
      });

      if (this.data.raiseType === config.raiseType.enterprise) {
        this.getEnterpriseListByName();
      }
    },

    onUserNameFocus() {
      this.setYZData({ tipShow: true });
    },

    onUserNameClose() {
      this.setYZData({ tipShow: false });
    },

    onUserNameSelect(e) {
      if (e.detail) {
        const {
          id = '',
          name = ''
        } = e.detail || {};

        /**
         * 莫名其妙的坑
         * 如果要在 input blur 的"同时"修改 value
         * 需手动保证输入框先 blur 再修改 value
         * 否则在某些手机会偶尔先"抖"一下又还原成修改前的 value
         * 或偶尔提示 do not have onInput handler ...
         */
        setTimeout(() => {
          this.$commit(this.data.mutation, {
            userName: name
          });

          id && this.getEnterpriseTaxNumById(id);
        }, 300);
      }
    },

    // 根据输入抬头补全企业名以及获取企业 id
    getEnterpriseListByName: debounce(function () {
      const {
        invoice,
        raiseType
      } = this.data;

      const requestError = () => {
        this.setYZData({ tipList: [] });
      };

      if (!invoice[raiseType].userName) {
        return requestError();
      }

      app.request({
        origin: 'cashier',
        method: 'post',
        path: '/wsctrade/order/invoice/queryTaxInfoListByCorpName.json',
        data: {
          corpName: invoice[raiseType].userName
        }
      }).then(res => {
        res = mapKeysCase.toCamelCase(res);

        let tipList = [];

        if (Array.isArray(res)) {
          tipList = res || [];
        }

        this.setYZData({ tipList });
      }).catch(() => {
        requestError();
      });
    }, 200),

    // 根据企业 id 获取税号
    getEnterpriseTaxNumById(companyId) {
      const requestError = () => {
        Toast('获取公司对应税号失败');
        this.$commit(this.data.mutation, {
          taxpayerId: ''
        });
      };

      if (!companyId) {
        return requestError();
      }

      app.request({
        origin: 'cashier',
        method: 'post',
        path: '/wsctrade/order/invoice/queryCompanyDetailTaxInfo.json',
        data: {
          companyId
        }
      }).then(res => {
        res = mapKeysCase.toCamelCase(res);

        if (res.creditCode && res.creditCode !== '- -') { // 脏数据
          this.$commit(this.data.mutation, {
            taxpayerId: res.creditCode
          });
        } else {
          throw res;
        }
      }).catch(() => {
        requestError();
      });
    },

    onIdChange(e) {
      this.$commit(this.data.mutation, {
        taxpayerId: e.detail
      });
    },

    onWxTitleClick() {
      authorize('scope.invoiceTitle').then(() => {
        wx.chooseInvoiceTitle({
          success: (res) => {
            if (res.errMsg === INVOICE_STATUS.OK) {
              let raiseType = '';
              let mutation = '';

              // type 开发者工具是 Number 真机是 String
              switch (res.type) {
                case 0: // 企业
                case '0':
                  raiseType = config.raiseType.enterprise;
                  mutation = 'ASSIGN_INVOICE_ENTERPRISE';
                  break;
                case 1: // 个人
                case '1':
                  raiseType = config.raiseType.personal;
                  mutation = 'ASSIGN_INVOICE_PERSONAL';
                  break;
                default: break;
              }

              const data = {
                userName: res.title || ''
              };

              // == 兼容本地开发
              if (res.type == 0) {
                data.taxpayerId = res.taxNumber || '';
              }

              this.$commit('ASSIGN_INVOICE_COMMON', { raiseType });
              this.$commit(mutation, data);
            } else {
              Toast(res.errMsg || '调用微信抬头失败');
            }
          },
          fail: (err) => {
            if (err.errMsg !== INVOICE_STATUS.CANCEL) {
              Toast(err.errMsg || '调用微信抬头失败');
            }
          }
        });
      }).catch(() => {
        Toast('获取失败，请检查获取发票抬头的权限');
      });
    }
  }
});
