import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapGetters } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapGetters([
      'isElectronic'
    ])
  },

  data: {
    showDialog: false
  },

  methods: {
    triggerDialog() {
      this.setYZData({
        showDialog: !this.data.showDialog
      });
    }
  }
});
