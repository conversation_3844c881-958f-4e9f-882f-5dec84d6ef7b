<import src="./custom-tpl.wxml" />
<page-container class="page-container page-{{ deviceType }}" forbid-copyright>
  <view class="invoice">
    <block wx:for="{{ invoice_design }}" wx:key="type">
      <block wx:if="{{ item.type === 'notice-bar' }}">
        <van-notice-bar 
          wx:if="{{ isElectronic }}" 
          text="电子发票与增值税普通纸质发票具备同等法律效力，支持报销入账。" 
        />
      </block>
      <block wx:elif="{{ item.type === 'invoice-goods' }}">
        <invoice-goods />
      </block>
      <block wx:elif="{{ item.type === 'form-block' }}">
        <van-tabs 
          color="{{ invoice.themeGeneral }}" 
          active="{{ raiseTypeIndex }}" 
          bind:change="onTabChange">
          <van-tab title="企业开票">
            <invoice-tab 
              class="invoice-block" 
              raise-type="{{ config.raiseType.enterprise }}" 
            />
          </van-tab>
          <van-tab title="个人开票">
            <invoice-tab 
              class="invoice-block" 
              raise-type="{{ config.raiseType.personal }}" 
            />
          </van-tab>
        </van-tabs>
        <!-- email -->
        <van-cell-group custom-class="invoice__email" border="{{ false }}">
          <van-field 
            value="{{ invoice.common.emailList[0] }}" 
            bind:change="onEmailChange" label="收票邮箱" 
            placeholder="用于向你发送电子发票" 
          />
        </van-cell-group>
      </block>
      <block wx:elif="{{ item.type === 'submit-block' }}">
        <!-- 等 vant-weapp 升级, 先写个假 button -->
        <theme-view 
          custom-class="invoice__submit-btn" 
          bg="general" bindtap="submit"
        >
          提交
        </theme-view>
      </block>
      <block wx:elif="{{ item.type === 'invoice-notes' }}">
        <invoice-notes />
      </block>
      <block wx:elif="{{ item.custom }}">
        <template is="{{ item.type }}" />
      </block>
    </block>
  </view>
</page-container>
<van-toast id="van-toast" />