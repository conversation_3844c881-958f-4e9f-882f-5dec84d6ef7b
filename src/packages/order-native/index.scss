// 将只用在order 分包里的样式挪入分包
@import '~themes/order-theme.scss';

$navBarHeight: 64px;

.stripe-border {
  &--top,
  &--bottom {
    position: relative;
  }

  &--top::before,
  &--bottom::after {
    position: absolute;
    right: 0;
    left: 0;
    height: 2px;
    background: repeating-linear-gradient(
      -45deg,
      #ff6d6d,
      #ff6d6d 20%,
      transparent 0,
      transparent 25%,
      #3283fa 0,
      #3283fa 45%,
      transparent 0,
      transparent 50%
    );
    background-size: 80px;
    content: '';
  }

  &--top::before {
    top: 0;
  }

  &--bottom::after {
    bottom: 0;
  }
}

.order-container {
  font-size: 14px;
  padding-bottom: 104px;
}


.time-picker__tree-select--v2 {
  .van-tree-select {
    height: 396px !important;
  }

  .van-tree-select__nav {
    background-color: #f2f3f5 !important;
  }

  .van-tree-select__nitem--active {
    font-weight: normal !important;
    color: var(--theme-general) !important;
  }

  .van-tree-select__item {
    font-weight: normal !important;

    &::after {
      display: none !important;
    }
  }

  .van-tree-select__selected {
    display: flex;
    align-items: center;

    .van-icon-checked {
      font-size: 18px !important;
    }
  }
}

.time-picker__tree-select {
  van-sidebar-item {
    &:first-child {
      display: none;
    }
  }
}

.van-tree-select__nitem--active::after {
  background-color: var(--theme-main-bg) !important;
}

.van-tree-select__item--active {
  color: var(--theme-general) !important;
}

.van-coupon__head {
  color: var(--theme-general) !important;
}

.van-coupon--disabled .van-coupon__head {
  color: #323233 !important;
}

.van-goods-action-button--last {
  --button-danger-background-color: var(--theme-main-bg) !important;
  --button-danger-color: var(--theme-main-text);
}

.van-goods-action-button--first {
  --button-warning-color: var(--theme-vice-text);
  --button-warning-background-color: var(--theme-vice-bg) !important;
}

.van-dialog__footer {
  .van-dialog__button:last-child {
    .van-button {
      color: var(--theme-general) !important;
    }
  }
}

.van-coupon-list .van-tabs__wrap {
  box-shadow: 0 6px 12px -12px #969799;
}

.index {
  &-view {
    padding-bottom: 90px;

    .van-icon {
      min-width: 1em;
    }
  }

  &__block {
    margin: 10px 12px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;

    &:empty {
      display: none;
    }
  }
}

.long-cell .van-cell {
  padding: 10px 12px !important;

  > .van-cell__title {
    flex: none;
  }

  > .van-cell__value {
    color: #323233;
    font-weight: 500;

    .van-field__control::placeholder {
      font-weight: normal;
      text-align: right;
    }
  }

  &:not(:last-child):not(.van-cell--borderless)::after {
    left: 12px;
    right: 12px;
  }
}

.skeleton-image {
  position: absolute;
  height: 100vh;
  width: 100vw;
  left: 0;
  top: $navBarHeight;
}


.navigation-bar {
  width: 100%;
  height: $navBarHeight;

   &__main{
    height: $navBarHeight;
    background: #fff;

    /** z-index 顺序 修改请查看 docs/z-index.md **/
  }

  &__sticky-wrap {
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 998;
  }


   &__icon {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-around;
    box-sizing: border-box;
    overflow: hidden;
    &-button{
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &__main{
    height: 64px;
    background: #fff;
  }

  &__sticky-wrap {
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 10001;
  }

  &__title-text {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    box-sizing: border-box;
    font-size: 36rpx;
    font-weight: 500;
    padding-top: 20px;

    text {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-align: center;
     // width: 161px;
      font-size: 16px;
    }
  }
}
