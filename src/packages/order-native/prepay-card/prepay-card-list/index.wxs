module.exports = {
  title(cardType) {
    let title = '';

    switch (cardType) {
      case '':
        title = '礼品卡'; break;
      case 'valueCard':
        title = '储值卡'; break;
      default: break;
    }

    return title;
  },

  list(data, cardType, disabled) {
    return data[cardType][disabled ? 'disabled' : 'list'];
  },

  showList(data, cardType, disabled) {
    if (data == null || data[cardType] == null) {
      return false;
    }
    return !!data[cardType][disabled ? 'disabled' : 'list'].length;
  },

  isChecked(data, card, cardType, disabled) {
    if (disabled) {
      return false;
    }

    return data[cardType].checked.indexOf(card.summaryCardNo) !== -1;
  }
};
