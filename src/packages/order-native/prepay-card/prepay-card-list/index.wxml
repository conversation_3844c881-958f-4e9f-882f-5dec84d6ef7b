<wxs src="./index.wxs" module="computed" />

<view
  wx:if="{{ computed.showList({ valueCard }, cardType, disabled) }}"
  class="prepay-card__list"
>
  <view class="prepay-card__list__title">{{ computed.title(cardType) }}</view>
  <prepay-card-item
    wx:for="{{ computed.list({ valueCard }, cardType, disabled) }}"
    wx:key="item.id"
    data="{{ item }}"
    data-card="{{ item }}"
    checked="{{ computed.isChecked({ valueCard }, item, cardType, disabled) }}"
    disabled="{{ disabled }}"
    card-type="{{ cardType }}"
    bind:tap="onCheck"
  />
</view>

<van-dialog id="van-dialog" />
