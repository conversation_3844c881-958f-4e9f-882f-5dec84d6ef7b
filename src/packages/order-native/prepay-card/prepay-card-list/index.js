import Dialog from '@vant/weapp/dist/dialog/dialog';
import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '@/packages/order-native/store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState(['valueCard'])
  },

  properties: {
    cardType: String,
    disabled: Boolean
  },

  methods: {
    ensure(card) {
      const { cardType } = this.data;
      const { checked = [] } = this.data[cardType] || {};

      // 选中互斥卡需要提示一下
      if (card.isExclusion) {
        const isChecked = checked.indexOf(card.summaryCardNo) > -1;
        const message = isChecked
          ? '若取消使用该类卡，系统将重新计算所有优惠活动价格'
          : '若使用该类卡，将不能享受店铺活动、优惠券、积分等优惠。是否继续使用该卡？';

        return Dialog.confirm({
          message,
          context: this
        });
      }

      return Promise.resolve();
    },

    onCheck(event) {
      const { disabled, cardType } = this.data;

      if (disabled) {
        return;
      }

      const { card } = event.target.dataset;

      this.ensure(card).then(() => {
        this.$commit('SWITCH_PREPAY_CARD_CHECK_STATUS', {
          card,
          cardType
        });
      }).catch(() => {});
    }
  }
});
