<wxs src="./index.wxs" module="utils" />

<view class="gift-card-item {{ disabled ? 'gift-card-item--disabled' : '' }}">
  <view class="gift-card-item__title">
    <image src="{{ utils.iconUrl(cardType) }}" />
    <view>{{ data.cardName }}</view>
    <theme-view
      class="gift-card-item__price"
      color="{{ disabled ? '' : 'general' }}"
    >
      ￥{{ utils.formatPrice(data.balance) }}
    </theme-view>
  </view>
  <view class="gift-card-item__desc">
    <view>卡号：{{ data.summaryCardNo }}</view>
    <view wx:if="{{ data.expireTime }}">有效期至 {{ data.expireTime }}</view>
  </view>
  <theme-view
    wx:if="{{ checked }}"
    color="general"
    custom-class="gift-card-item__corner"
  >
    <van-icon
      name="success"
      size="12px"
      color="white"
      custom-class="gift-card-item__icon"
    />
  </theme-view>
</view>
<view
  wx:if="{{ disabled && data.unusableReason }}"
  class="gift-card-item__reason"
>
  {{ data.unusableReason }}
</view>
<view
  wx:elif="{{ data.useSpecification }}"
  class="gift-card-item__reason"
>
  {{ checked ? '取消使用该卡类，可重新享受店铺活动、优惠券、积分等优惠' : '该卡不可与店铺活动、优惠券、积分同时使用，限原价购买' }}
</view>
