import { mapGetters } from '@youzan/vanx';
import { VanxPage } from 'pages/common/wsc-page/index';
import store from '@/packages/order-native/store/index';

const TAB_LIST = [
  { title: '可用', value: 'list' },
  { title: '不可用', value: 'disabled' }
];

let HAS_EXCLUSION_CARD = false;

VanxPage({
  store,

  mapData: {
    ...mapGetters(['prepayCardTotalAmount'])
  },

  computed: {
    ...mapGetters(['hasExclusionCard'])
  },

  data: {
    TAB_LIST,
    active: 0
  },

  onReady() {
    // 缓存一下选卡之前的互斥卡存在状态
    HAS_EXCLUSION_CARD = this.hasExclusionCard;
  },

  onTabChange(e) {
    this.setYZData({ active: e.detail.index });
  },

  onConfirm() {
    // 如果互斥卡存在状态与选卡前不同，重新请求FETCH_SHOW
    if (HAS_EXCLUSION_CARD !== this.hasExclusionCard) {
      this.$commit('SET_ORDER_FORBID_COUPON', this.hasExclusionCard);
      this.$commit('SET_ORDER_FORBID_PREFERENCE', this.hasExclusionCard);
      this.$dispatch('FETCH_SHOW');
    }
    wx.navigateBack();
  }
});
