<van-popup round custom-class="trade-recharge" show="{{ show }}" position="bottom" bind:close="onClose">
  <!-- 标题 -->
  <view class="trade-recharge__pop-header">
    <van-icon
      wx:if="{{activeIndex === 1}}"
      name="arrow-left"
      size="12px"
      custom-class="trade-recharge__pop-left"
      bind:tap="onLeft"
    />
    {{activeIndex === 0 ? '充值更优惠' : '请选择礼包'}}
    <van-icon wx:if="{{ activeIndex === 0 }}" name="cross" size="12px" custom-class="trade-recharge__pop-close" bind:tap="onClose" />
  </view>
  <view class="trade-recharge-content-swipper">
    <view
      class="trade-recharge-content {{ activeIndex===0 ? 'content-translateX0' : 'content-translateX100' }}"
    >
     <view class="trade-recharge-content-swipper-tab">
        <recharge-info
          parsed-recommend="{{parsedRecommend}}"
          value-card="{{valueCard}}"
          recommend-desc="{{recommendDesc}}"
          bind:select="onSelect"
          bind:amountClick="handleAmountClick"
          amount-checked-map="{{amountCheckedMap}}"
        />
      </view>
      <view class="trade-recharge-content-swipper-tab">
        <gift-pack product="{{checkedProduct}}" activeIndex="{{giftCheckIdx}}" bind:checkGiftPack="handleCheckGiftPack"/>
      </view>
    </view>
  </view>
</van-popup>