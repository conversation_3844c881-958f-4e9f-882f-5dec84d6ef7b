.trade-recharge {
  background: #f2f2f2;
  // padding: 0 0 60px;
  box-sizing: border-box;
  // position: relative;
  display: flex;
  flex-direction: column;

  .trade-recharge-content-swipper {
    overflow: hidden;
  }

  .trade-recharge-content {
    flex: 1;
    display: flex;
    transition: transform 0.3s cubic-bezier(0.84, 0.84, 0.32, 0.32);
  }

  .trade-recharge-content-swipper-tab {
    flex: none;
    width: 100%;
  }

  .content-translateX0 {
    transform: translateX(0);
  }

  .content-translateX100 {
    transform: translateX(-100%);
  }

  &__pop-header {
    font-size: 16px;
    line-height: 44px;
    text-align: center;
    background-color: #fff;
    position: relative;
  }

  &__pop-left,
  &__pop-close {
    position: absolute !important;
    top: 12px;
    padding: 0 12px;
    color: #969799;
    font-size: 22px !important;
    line-height: 44px;
    text-align: center;
  }

  &__pop-left {
    left: 0;
  }
  
  &__pop-close {
    right: 0;
  }
}
