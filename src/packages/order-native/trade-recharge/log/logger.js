const component = 'tradeRecharge';
const { logger } = getApp();

function loggerEvent(et = 'click', ei = '', en = '', params = {}) {
  try {
    logger &&
      logger.log({
        et,
        ei,
        en,
        params
      });
  } catch (e) {
    console.error('logger 错误', `et = ${et}`, `ei = ${ei}`, `en = ${en}`, 'params = ', params);
  }
}

// 显示充值并支付组件
export function loggerTradeRechargeShow(params = {}) {
  loggerEvent('view', 'tradeRecharge_show', '充值并支付组件曝光', {
    ...params,
    component
  });
}

// 关闭充值并支付组件
export function loggerTradeRechargeClose(params = {}) {
  loggerEvent('click', 'tradeRecharge_close', '充值并支付组件关闭', {
    ...params,
    component
  });
}

// 直接支付不储值
export function loggerTradeRechargePay(params = {}) {
  loggerEvent('click', 'tradeRecharge_pay', '充值并支付组件-直接支付', {
    ...params,
    component
  });
}

// 选择充值并支付
export function loggerTradeRecharge(params = {}) {
  loggerEvent('click', 'tradeRecharge_recharge', '充值并支付组件-选择充值并支付', {
    ...params,
    component
  });
}
