module.exports = {
  balanceCard(valueCard) {
    const price = (value, dc, floor = true) => {
      if (!value && value !== 0) {
        return '-.--';
      }
      const dvd = dc || 100;
      const realValue = value || 0;
      return !floor ? realValue / dvd : Math.floor(realValue / dvd);
    };
    const balance = (value, fiexd = 2) => {
      const result = price(value, 100, false);
      if (result === '-.--') {
        return result;
      }
      return result.toFixed(fiexd);
    };

    if (!valueCard) {
      return balance(0);
    }
    // 储值卡类型 cardSubType === 1001 || 1000 时为余额卡，可充值
    const card = valueCard.list.filter(
      card => card.cardType === 102 && card.cardSubType === 1001
    )[0];
    const c = card && card.balance ? card : { balance: 0 };
    return balance(c.balance);
  },
};
