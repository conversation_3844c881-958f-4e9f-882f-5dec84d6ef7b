.recharge-info {
  background-color: #f7f8fa;
}

.recharge-info__content {
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.recharge-info__title {
  font-size: 16px;
  color: #323233;
  font-weight: 500;
  padding: 16px 16px 8px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: flex-end;
}

.recharge-info__title__unit {
  font-size: 12px;
  padding-left: 6px;
  padding-right: 1px;
}

.recharge-info__scroll-content {
  display: flex;
  flex-direction: column;
  height: 40vh;
  overflow-y: scroll;
  padding: 0 16px 10px 16px;
}

.content-loading {
  height: 40vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.recharge-info__list {
  flex: 1;
}

.recharge-info__footer {
  background-color: #fff;

  .footer {
    display: flex;
    padding: 8px 16px 8px;
  }
  .footer__btn {
    flex: 1;
    button {
      height: 40px;
      font-size: 14px;
      line-height: 40px;
      border-radius: 18px;
    }
  }

  .recharge__btn {
    flex: 1;
    margin-left: 12px;
  }
}

.recharge-info__protocol {
  display: flex;
  align-items: center;
  color: #969799;
  font-size: 12px;
  padding: 8px 0;
  margin-left: 16px;
}

.recharge-info__protocol-nav {
  display: inline-block;
  color: #38f;
}

.sheet_protocol--no-card-sign {
  display: flex;
  justify-content: center;
  color: #969799;
  font-size: 12px;
  margin-top: 40px;
}

.protocol-desc {
  margin-left: 5px;
}

.protocol-entry {
  font-size: 14px;
  text-align: center;
}

.protocol-nav {
  display: inline-block;
  color: #38f;
}
