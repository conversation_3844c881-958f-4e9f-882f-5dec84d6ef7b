import { moment as formatDate } from 'utils/time';
import formatMoneyBase from '../money/format';

const formatMoney = cent => formatMoneyBase(cent, 1, !1);

function formatCent(cent, removeTrailing = true) {
  return removeTrailing ? parseFloat(formatMoney(cent), 10) : formatMoney(cent);
}

const RULE_DISABLE_REASON = {
  TAKE_PART: '已经参与',
  NOT_STARTED: '即将开始',
  FINISHED: '已结束',
  FIRST_RECH: '你已有充值记录，不能享受首充优惠'
};

// 已弃用
function formatGiftPack(giftPack, filterInvalidRights = true) {
  const {
    giftPackId,
    giftPackName: name,
    factAmount: amount,
    ruleConditions = {},
    nextStartTime = null,
    ruleRights: rights = {},
    unavailableReason = null,
    ...rest
  } = giftPack;

  // 选择性过滤失效的会员卡 优惠券
  const memberCards = (rights.memberCardRights || []).filter(
    card => !filterInvalidRights || card.cardStatus === 'VALID'
  );
  // 枚举值1表示有效, 2表示无库存, 详情追溯定义
  const coupons = (rights.couponRights || []).filter(
    coupon => !filterInvalidRights || (coupon.status === 1 || coupon.status === 2)
  );

  // 补充默认值并展开
  const { bonusAmount = 0 } = rights.bonusAmountRights || {};
  const { growthPoint = 0 } = rights.growthPointRights || {};
  const { point = 0, customPointName: pointName = '积分' } = rights.pointRights || {};

  // 处理赠品的逻辑
  let { presentRights: present = {} } = rights;
  if (present.presentStatus !== 'VALID') present = {};

  // 规则是否包含权益标记
  const hasRights = Boolean(
    memberCards.length || coupons.length || bonusAmount || growthPoint || point || present.presentName
  );

  // 处理限制条件的逻辑
  const {
    customerCountLimit: customerLimit = 0,
    firstRecharge = false
  } = ruleConditions;

  // 格式化主体金额 (本金)
  const yuanAmount = formatCent(amount);

  // 处理储值权益文案
  const rightSloganList = [];
  if (bonusAmount) {
    rightSloganList.push(`${formatCent(bonusAmount)}元`);
  }

  if (present.presentName) {
    rightSloganList.push(`一份${present.presentName}`);
  }

  if (point) {
    rightSloganList.push(`${point}${pointName}`);
  }
  if (growthPoint) {
    rightSloganList.push(`${growthPoint}成长值`);
  }
  if (memberCards.length) {
    rightSloganList.push(...memberCards.map(({ cardName }) => `1张${cardName}`));
  }
  if (coupons) {
    rightSloganList.push(
      ...coupons.map(({ count, couponName }) => `${count}张${couponName}`)
    );
  }
  const rightSlogan = rightSloganList.length > 0 ? '送' + rightSloganList.join('、') : '充值金额';

  // 处理左上角提示块文案
  // eslint-disable-next-line no-nested-ternary
  let tooltipSlogan = firstRecharge ? '首充优惠' : customerLimit ? `仅限${customerLimit}次` : '';

  if (unavailableReason) {
    tooltipSlogan = RULE_DISABLE_REASON[unavailableReason];
  }

  // 处理活动时间
  let nextTime = null;
  if (nextStartTime) {
    nextTime = formatDate(nextStartTime, 'YYYY-MM-DD HH:mm:ss');
  }

  return {
    ...rest,
    name,
    point,
    amount,
    giftPackId,
    coupons,
    present,
    nextTime,
    pointName,
    yuanAmount,
    bonusAmount,
    growthPoint,
    memberCards,
    rightSlogan,
    customerLimit,
    tooltipSlogan,
    nextStartTime,
    rights: hasRights
  };
}

export function formatAmountList(amountList = []) {
  if (Array.isArray(amountList)) {
    amountList.forEach(product => {
      if (product.giftPacks && product.giftPacks.length > 0) {
        product.giftPacks = product.giftPacks.map(rule => formatGiftPack(rule));
      }
    });
  }
  return amountList;
}
