import WscComponent from 'pages/common/wsc-component';
import get from '@youzan/weapp-utils/lib/get';
import { queryGiftpacks, signAgreement } from '../api';
import Toast from '@vant/weapp/dist/toast/toast';
import { loggerTradeRechargePay, loggerTradeRecharge } from '../log/logger';

WscComponent({
  properties: {
    valueCard: Object,
    parsedRecommend: {
      type: String,
      value: () => {},
      observer(parsedRecommend) {
        if (parsedRecommend) {
          let recommend = {};
          try {
            recommend = JSON.parse(parsedRecommend);
          } catch (error) {
            console.log(error);
          }
          const isRecharged = !!get(recommend, 'cardNo', '');
          this.setYZData({
            isRecharged,
          });
          this.queryGiftPacks();
        }
      },
    },
    recommendDesc: String,
    amountCheckedMap: {
      type: Object,
      value: () => {},
    },
  },
  data: {
    rechargeBtnLoading: false,
    isRecharging: false,
    isRecharged: false,
    // 如果没开过卡 需要保存一下新开的卡号呦
    cardNoSign: '',
    activeIndex: 0,
    selectedProduct: {},
    loading: false,
    amountList: [],
    isProtocolApproval: false,
  },
  attached() {
    this.queryGiftPacks();
  },
  methods: {
    async queryGiftPacks() {
      let recommend = {};
      try {
        recommend = JSON.parse(this.data.parsedRecommend || '{}');
      } catch (error) {
        console.log(error);
      }
      const productInfos = get(recommend, 'productInfos', '[]');
      const productNos = [];
      if (Array.isArray(productInfos) && productInfos.length > 0) {
        productInfos.every(
          (item) => item.productNo && productNos.push(item.productNo)
        );
      }
      if (productNos.length > 0) {
        this.setYZData({
          loading: true,
        });
        try {
          const amountList = await queryGiftpacks({
            productNoList: productNos,
            disableFilter: true, // 禁用 “不可用礼包” 过滤
            useDetailedSlogan: true, // 启用详细礼包slogan（充值并支付使用）
          });
          this.setYZData({
            amountList,
            loading: false,
          });
        } catch (error) {
          console.log(error);
          this.setYZData({
            loading: false,
          });
          Toast({
            message: '获取礼包内容失败',
            duration: 2000,
          });
        }
      }
    },

    async onRechargeClick() {
      const {
        isRecharged,
        amountCheckedMap = {},
        activeIndex,
        amountList,
        isProtocolApproval,
      } = this.data;
      // 解析当前激活的礼包index
      const activeGiftIdx = amountCheckedMap[activeIndex] || 0;
      // 当前选中金额
      const selectedProduct = amountList[activeIndex] || {};
      let recommend = {};
      try {
        recommend = JSON.parse(this.data.parsedRecommend || '{}');
      } catch (error) {
        console.log(error);
      }
      const templateNo = get(recommend, 'templateNo', '');
      let cardNoSign = '';
      // check 是否选中面额
      if (selectedProduct.amount === undefined) {
        wx.showToast({
          title: '请选择一个充值面额',
          icon: 'none',
        });
        return;
      }

      if (!isProtocolApproval && !isRecharged) {
        Toast({ message: '请先勾选协议', position: 'bottom' });
        return;
      }

      // check 签约开卡 & 开卡
      try {
        if (!isRecharged) {
          this.toggleRechargeBtnLoading(true);
          cardNoSign = await this.signPrepaidAgreement();
        }
      } catch (error) {
        wx.showToast({
          title: '签约开卡失败',
          icon: 'none',
        });
        this.toggleRechargeBtnLoading(false);
        return false;
      }

      // recharge
      const cardNo = get(recommend, 'cardNo', '');
      const activeGiftPack = get(
        selectedProduct,
        `giftPacks[${activeGiftIdx}]`,
        {}
      );
      const rechargeParam = {
        goodsName: activeGiftPack.giftPackName || '充值并支付固定金额储值',
        tradeDesc: activeGiftPack.factType || 'VLCARD_RCHG',
        ruleNo: activeGiftPack.giftPackId || '0',
        ruleVersion: activeGiftPack.giftPackVersion || '',
        payAmount: selectedProduct.amount,
        cardNo: cardNo || this.data.cardNoSign || cardNoSign,
        /** 2 小程序-下单页充值: wx_app_trade,  */
        source: 2,
        templateNo,
        productNo: selectedProduct.productNo,
        isStoreAndPayProcess: true,
      };

      loggerTradeRecharge();
      this.triggerEvent('select', rechargeParam);
    },

    simplePay() {
      loggerTradeRechargePay();
      this.triggerEvent('select', { isStoreAndPayProcess: false });
    },

    signPrepaidAgreement() {
      let recommend = {};
      try {
        recommend = JSON.parse(this.data.parsedRecommend || '{}');
      } catch (error) {
        console.log(error);
      }
      const templateNo = get(recommend, 'templateNo', '');
      return signAgreement({
        templateNo,
      }).then((res) => {
        // 保存开卡
        this.setYZData({
          cardNoSign: res.cardNo,
        });
        this.toggleRechargeBtnLoading(false);
        return res.cardNo;
      });
    },

    cardClick(e) {
      const { product, index, isCheck } = e.detail;
      this.setYZData({
        selectedProduct: product,
        activeIndex: index,
      });
      this.triggerEvent('amountClick', {
        product,
        index,
        isCheck,
      });
    },

    toggleRecharging(bol) {
      this.setYZData({
        isRecharging: bol,
      });
    },

    toggleRechargeBtnLoading(bol) {
      this.setYZData({
        rechargeBtnLoading: bol,
      });
    },

    // 切换协议
    toggleProtocolCheck() {
      const { isProtocolApproval } = this.data;
      this.setYZData({
        isProtocolApproval: !isProtocolApproval,
      });
    },
  },
});
