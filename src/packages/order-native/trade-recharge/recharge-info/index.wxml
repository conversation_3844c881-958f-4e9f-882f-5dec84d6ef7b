<wxs src="./index.wxs" module="utils" />
<view class="recharge-info">
  <view class="recharge-info__title">当前余额：<text class="recharge-info__title__unit">¥</text>{{ utils.balanceCard(valueCard) }}</view>
  <view class="recharge-info__scroll-content">
    <view class="content-loading" wx:if="{{loading}}">
      <van-loading />
    </view>
    <view class="recharge-info__list">
      <amount-list
        wx:if="{{!loading}}"
        data="{{amountList}}"
        selected="{{activeIndex}}"
        show-gift-pack-content="{{true}}"
        bind:amountClick="cardClick"
        amount-checked-map="{{amountCheckedMap}}"
      />
    </view>
    <view wx:if="{{isRecharged}}" class="sheet_protocol--no-card-sign">
        查看
        <navigator
          hover-class="none"
          class="recharge-info__protocol-nav"
          url="/packages/prepaid/agreement/index"
          type="switch"
        >
          《单用途预付卡协议》
        </navigator>
      </view>
  </view>
  <view class="recharge-info__footer van-hairline--top">
    <view wx:if="{{!isRecharged}}" class="recharge-info__protocol">
      <van-radio-group value="{{isProtocolApproval}}" bind:change="toggleProtocolCheck">
        <van-radio icon-size="16px" name="{{true}}" />
      </van-radio-group>
      <text class="protocol-desc">我已同意</text>
      <navigator
        hover-class="none"
        class="recharge-info__protocol-nav"
        url="/packages/prepaid/agreement/index"
        type="switch"
      >
        《单用途预付卡协议》
      </navigator>
    </view>
    <view class="footer">
      <van-button
        loading="{{rechargeBtnLoading}}"
        class="footer__btn pay__btn"
        type="danger"
        plain
        size="large"
        bind:click="simplePay"
      >
        直接支付
      </van-button>
      <van-button
        loading="{{rechargeBtnLoading}}"
        class="footer__btn recharge__btn"
        type="danger"
        size="large"
        bind:click="onRechargeClick"
      >
        充值并支付
      </van-button>
    </view>
  </view>
</view>
