import WscComponent from 'pages/common/wsc-component';

const app = getApp();

const canvas = wx.createCanvasContext();

WscComponent({
  properties: {
    slogan: {
      type: String,
      required: true,
      value: '',
      observer(slogan) {
        const sloganFont =
          '12px -apple-system, system-ui, "Helvetica Neue", Helvetica, Roboto, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft Yahei", SimSun, sans-serif';
        canvas.font = sloganFont;

        const metrics = canvas.measureText(slogan);
        const isOverflow = metrics.width > wx.getSystemInfoSync().windowWidth - 84;
        this.setYZData({
          showArrow: isOverflow
        });
      }
    },
    selected: {
      type: Boolean,
      required: true,
      value: false
    }
  },
  data: {
    showArrow: false
  },
  attached() {
    app.getShopTheme().then((res) => {
      this.setYZData({
        themeMainColor: res.colors['main-bg'],
      });
    });
  },
  methods: {
    showAllDesc() {
      this.setYZData({
        showArrow: false
      });
    }
  }
});
