<view bind:tap="showAllDesc" class="{{ selected ? 'selected' : '' }}">
  <text style="color: {{ selected ? themeMainColor : '#969799' }}" class="amount-item__price__desc" wx:if="{{!showArrow}}">
    {{ slogan }}
  </text>
  <view class="amount-item__price__desc-block" wx:else>
    <text style="color: {{ selected ? themeMainColor : '#969799' }}" class="amount-item__price__desc amount-item__price__desc--ellipsis">{{ slogan }}</text>
    <van-icon name="arrow-down" color="#969799" />
  </view>
</view>