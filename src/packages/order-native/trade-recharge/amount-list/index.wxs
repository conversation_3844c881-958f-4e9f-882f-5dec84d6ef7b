module.exports = {
  elementWidth(columnNum) {
    return `
      width: ${100 / columnNum}%;
    `;
  },
  elementClass(idx, selected) {
    return selected === idx ? 'amount-list__selected' : '';
  },
  selected(idx, selected) {
    return selected === idx;
  },
  balance(value, fiexd = 2) {
    function price(value, dc, floor = true) {
      if (!value && value !== 0) {
        return '-.--';
      }
      const dvd = +dc || 100;
      const realValue = +value || 0;
      return !floor ? realValue / dvd : Math.floor(realValue / dvd);
    }
    const result = price(value, 100, false);
    if (result === '-.--') {
      return result;
    }
    return result.toFixed(fiexd);
  },
  slogan(item, checkedGiftIndex = 0) {
    let slogan;
    if (item.giftPacks && item.giftPacks[checkedGiftIndex]) {
      slogan = item.giftPacks[checkedGiftIndex].detailedSlogan;
    }
    return slogan ? '送 ' + slogan : '充值金额';
  },
  hasGiftPack(item) {
    return item.giftPacks && item.giftPacks.length > 0;
  }
};
