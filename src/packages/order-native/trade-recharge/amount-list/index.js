import WscComponent from 'pages/common/wsc-component';
import Theme from 'shared/common/components/theme-view/theme';

const app = getApp();

WscComponent({
  properties: {
    amountClick: {
      type: Function,
      required: false,
      value: () => {}
    },
    data: {
      type: Array,
      required: false,
      value: []
    },
    columnNum: {
      type: Number,
      required: false,
      value: 1
    },
    selected: {
      type: Number,
      required: false
    },
    amountCheckedMap: {
      type: Object,
      required: false,
      value: () => {},
    },
  },
  attached() {
    app.getShopTheme().then((res) => {
      const mainColor = res.colors['main-bg'];
      const rgb = Theme.switchHexToRgb(mainColor);
      this.setYZData({
        themeMainColor: mainColor,
        themeMainColor8Alpha: `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, .08)`
      });
    });
  },
  methods: {
    handleAmountCheckClick(e) {
      const { item, index } = e.currentTarget.dataset;
      this.triggerEvent('amountClick', { index, product: item, isCheck: true });
    },
    handleToGiftsClick(e) {
      const { item, index } = e.currentTarget.dataset;
      this.triggerEvent('amountClick', { index, product: item });
    },
  },
});
