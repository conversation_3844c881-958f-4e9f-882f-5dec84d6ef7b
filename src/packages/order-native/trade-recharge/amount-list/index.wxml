<wxs src="./index.wxs" module="utils" />
<view>
  <view class="amount-list">
    <view
     class="amount-list__price"
      wx:for="{{ data }}"
      wx:key="{{ item.amount }}"
      wx:for-index="index"
      wx:for-item="item"
      style="{{ utils.elementWidth(columnNum) }}"
    >
      <view class="amount-list__price__left">
          <view class="amount-list__price__check">
            <van-icon
              class="van-icon"
              style="border-color: {{ utils.selected(index, selected) ? themeMainColor: '#ebedf0' }}; background-color: {{utils.selected(index, selected) ? themeMainColor: '#fff' }}"
              size="12px"
              name="success"
            />
          </view>
          <view class="amount-list__price__content">
            <view class="amount-list__price__value">
              <text class="amount-list__price__bit">¥</text>{{ utils.balance(item.amount, 2) }}
            </view>
            <view class="amount-list__price__desc"
              >{{utils.slogan(item, amountCheckedMap[index])}}</view
            >
          </view>
        </view>
        <view class="amount-list__price__right">
          <van-icon class="van-icon" name="arrow" wx:if="{{utils.hasGiftPack(item)}}"/>
        </view>
        <!-- 点击热区：选中礼包 -->
        <view
          class="amount-list__price__check__trigger"
          bind:tap="handleAmountCheckClick"
          data-index="{{index}}"
          data-item="{{item}}"
        ></view>
        <!-- 点击热区：前往礼包页 -->
        <view
          class="amount-list__price__right__trigger"
          bind:tap="handleToGiftsClick"
          data-index="{{index}}"
          data-item="{{item}}"
          wx:if="{{utils.hasGiftPack(item)}}"
        ></view>
    </view>
  </view>
</view>