.amount-list {
  display: flex;
  flex-wrap: wrap;
  color: #323233;
  margin-top: 5px;
}
.amount-list__selected {
  color: #f44;
}
.amount-list__selected .amount-list__price {
  border: solid 0.5px #f44;
  background-color: rgba(255, 68, 68, 0.08);
}
.amount-list__price {
  min-height: 68px;
  border-radius: 4px;
  border: solid 0.5px #ebedf0;
  background-color: #fff;
  box-sizing: border-box;
  padding: 10px 12px;
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-bottom: 12px;
}

.amount-list__price__left {
  display: flex;
}

.amount-list__price__check {
  position: relative;
  margin: auto 0;
  height: 20px;
  width: 20px;
}

.amount-list__price__check .van-icon {
  font: normal normal normal 12px/1 'vant-icon';
  border-radius: 100%;
  text-align: center;
  line-height: inherit;
  width: 18px;
  height: 18px;
  box-sizing: border-box;
  border: 1px solid #969799;
  color: #fff;
  transition: 0.2s;
  position: relative;
  display: inline-block;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}
.amount-list__price__right {
  margin: auto 0;
}
.amount-list__price__right .van-icon {
  color: #969799;
  font-size: 16px;
}

.amount-list__price__check .van-icon view::before {
  position: relative;
  left: 1px;
}

.amount-list__price__content {
  margin-left: 12px;
  margin-right: 12px;
}

.amount-list__price__value {
  height: 28px;
  font-size: 24px;
  font-weight: 500;
  color: #323233;
}
.amount-list__price__bit {
  font-size: 14px;
  margin-right: 4px;
}
.amount-list__price__desc {
  line-height: 16px;
  font-size: 12px;
  font-weight: 400;
  color: #969799;
  margin-top: 4px;
}
.amount-list__price__check__trigger,
.amount-list__price__right__trigger {
  position: absolute;
  top: 0;
  height: 100%;
}
.amount-list__price__check__trigger {
  left: 0;
  width: 100px;
}
.amount-list__price__right__trigger {
  right: 0;
  width: 120px;
}
