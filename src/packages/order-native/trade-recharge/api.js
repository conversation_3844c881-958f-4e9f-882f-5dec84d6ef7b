const app = getApp();
const http = app.request;
// 签约开卡
function signAgreement(data) {
  return http({
    path: '/prepaid/recommend/api/card/giftcard/recharge/agreement/sign.json',
    method: 'POST',
    data,
  });
}

// 获取储值推荐内容
function queryGiftpacks(data) {
  return http({
    path: '/prepaid/recommend/api/card/recharge/giftpacks.json',
    method: 'POST',
    data,
  });
}

export { signAgreement, queryGiftpacks };
