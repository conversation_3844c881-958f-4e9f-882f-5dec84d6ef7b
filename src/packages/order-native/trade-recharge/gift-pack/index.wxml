<wxs src="./index.wxs" module="utils" />
<view class="gitfpack">
  <view class="sheet__content">
    <view class="sheet__title">充值: ¥ {{ utils.balance(product.amount) }}</view>
      <!-- 礼包 -->
      <view class="sheet__scroll-content sheet__scroll-content__noprotocol">
        <view
            wx:for="{{product.giftPacks}}"
            wx:for-item="giftPack"
            wx:key="giftPackId"
            wx:for-index="idx"
            class="sheet__card"
            data-index="{{idx}}"
            bind:tap="cardClick"
          >
            <!-- 标题 -->
            <view class="sheet__card--title">
              <view class="name">{{giftPack.giftPackName}}</view>
              <view class="check-icon" style="border-color: {{ themeMainColor }}">
                <van-icon
                  style="background: {{ themeMainColor }}"
                  class="{{ idx===activeIndex ? 'checked' : 'gift-van-icon' }}"
                  name="success"
                />
              </view>
            </view>
            <!-- 内容 -->
            <view class="sheet__card--content">
              <view class="sheet__rights">
                <rule-rights-detail gift-pack="{{giftPack}}" />
              </view>
            </view>
          </view>
      </view>
  </view>
</view>
<van-toast id="van-toast" />
