import WscComponent from 'pages/common/wsc-component';
import get from '@youzan/weapp-utils/lib/get';

const app = getApp();

WscComponent({
  properties: {
    product: Object,
    activeIndex: Number,
  },
  data: {},
  attached() {
    app.getShopTheme().then((res) => {
      this.setYZData({
        themeMainColor: res.colors['main-bg'],
      });
    });
  },
  methods: {
    // 选择礼包索引
    cardClick(e) {
      const idx = get(e, 'currentTarget.dataset.index', 0);
      this.triggerEvent('checkGiftPack', { activeIndex: idx });
    },
  },
});
