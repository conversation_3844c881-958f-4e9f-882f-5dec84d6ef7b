.gitfpack {
  width: 100vw;
  position: relative;
  background: #f7f8fa;  
  height: 100%;
}
.sheet__content {
  flex: 1;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.sheet__title {
  font-size: 16px;
  color: #323233;
  font-weight: 500;
  padding: 16px 16px 16px 20px;
}
.sheet__scroll-content {
  overflow-y: scroll;
}
.sheet__scroll-content__noprotocol {
  height: 40vh;
  padding-left: 16px;
  padding-right: 16px;
}
.sheet__card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}
.sheet__card:last-child {
  margin-bottom: 0;
}
.sheet__card--title {
  display: flex;
  height: 32px;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}
.sheet__card--title::before {
  content: '';
  position: absolute;
  left: -50%;
  bottom: 0;
  width: 200%;
  height: 1px;
  background: #ebedf0;
  transform: scale(0.5);
}
.sheet__card--title .name {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}
.sheet__card--title .check-icon {
  display: inline-block;
  vertical-align: middle;
  line-height: 18px;
  height: 18px;
  width: 18px;
  background: #ffffff;
  border: 1px solid #ff4444;
  border-radius: 100%;
  text-align: center;
  line-height: 22px;
}
.sheet__card--title .check-icon .van-icon {
  border-radius: 100%;
  width: 18px;
  height: 18px;
  box-sizing: border-box;
  border: 1px solid #f44;
  color: #fff;
  transition: 0.2s;
  position: relative;
  display: inline-block;
  font: normal normal normal 14px/1 'vant-icon';
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  text-align: center;
  line-height: 22px;
}

.sheet__card--title .check-icon .van-icon::before {
  position: relative;
  left: 1px;
  top: 1px;
}
.sheet__card--title .check-icon .checked {
  background: #f44;
  color: #fff;
  display: inline-block;
  width: 18px;
  height: 18px;
  border-radius: 100%;
}
.sheet__card--title .check-icon .gift-van-icon {
  display: none;
}
.sheet__card--no-gift {
  text-align: center;
}
.sheet__card--no-gift > image {
  width: 150px;
  height: 155px;
  margin: 0 auto;
  margin-top: 90px;
}
.sheet__card--no-gift-text{
  margin-top: 10px;
  font-size: 14px;
  color: #A3A3A3;
}
.sheet__rights {
  padding-top: 12px;
  border-radius: 8px;
}
.sheet__right {
  margin-left: 30px;
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 22px;
  position: relative;
}
.sheet__right:last-child {
  margin-bottom: 0;
}
.sheet__right::before {
  top: 0;
  left: -32px;
  width: 22px;
  height: 22px;
  content: ' ';
  position: absolute;
  background-size: cover;
}
