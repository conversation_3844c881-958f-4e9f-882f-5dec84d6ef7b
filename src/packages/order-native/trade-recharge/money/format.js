import formatLargeNumber from './format-large-number';

/**
 * 格式化金钱格式
 * @memberof module:money
 * @param {number} value  待格式化的金额数字
 * @param {boolean} cent 是否是分
 * @param {boolean} showComma 是否显示千分位逗号
 * @returns {string} 格式化之后的金额，精度2位
 * @runkit true
 * @example
 * format(1000000, false));
 * //'1,000,000.00'
 * format(1000000);
 * //'10,000.00'
 */
function format(value, cent = true, showComma = true) {
  value = parseFloat(value, 10);
  if (cent) {
    value /= 100;
  }
  value = value.toFixed(2);
  if (showComma) {
    return formatLargeNumber(value);
  }
  return value;
}
export default format;

