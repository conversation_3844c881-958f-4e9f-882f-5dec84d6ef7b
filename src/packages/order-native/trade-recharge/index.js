import { loggerTradeRechargeShow, loggerTradeRechargeClose } from './log/logger';

Component({
  properties: {
    show: {
      type: Boolean
    },
    valueCard: {
      type: Object,
      required: true
    },
    recommendDesc: {
      type: String,
      default: ''
    },
    parsedRecommend: {
      type: String,
      default: ''
    }
  },

  data: {
    activeIndex: 0, // 当前激活tab索引
    checkedProduct: {}, // 当前选中礼包
    amountCheckedMap: { 0: 0 }, // 当前 金额-礼包 index对应关系
    amountCheckIdx: 0, // 当前选中金额index
    giftCheckIdx: 0, // 当前选中礼包index
  },

  attached() {
    loggerTradeRechargeShow();
  },

  methods: {
    onLeft() {
      this.setData({
        activeIndex: 0,
      });
    },
    onClose() {
      loggerTradeRechargeClose();
      this.triggerEvent('onClose');
      setTimeout(() => {
        this.onLeft();
      }, 1000);
    },
    onSelect({ detail: params }) {
      this.triggerEvent('select', params);
    },
    handleAmountClick(e) {
      const { product, index = 0, isCheck } = e.detail;
      const { amountCheckedMap } = this.data;
      const setData = { checkedProduct: product };
      if (!isCheck) {
        // 去礼包选择tab
        setData.activeIndex = 1;
      }
      setData.amountCheckIdx = index;
      // 拿出当前金额的选中礼包的index
      setData.giftCheckIdx = amountCheckedMap[index] || 0;
      this.setData(setData);
    },
    handleCheckGiftPack(e) {
      const giftCheckIdx = e.detail.activeIndex || 0;
      const amountCheckedMap = this.data.amountCheckedMap;
      // 维护 { 金额 & 礼包 } 的对应关系map
      amountCheckedMap[this.data.amountCheckIdx] = giftCheckIdx;
      this.setData({
        activeIndex: 0,
        giftCheckIdx,
        amountCheckedMap,
        checkedProduct: {}
      });
    },
  },
});
