<page-container class="page {{ themeClass }} page-{{ deviceType }}">
  <van-search
    placeholder="请输入提货城市"
    show-action="{{ inSearch }}"
    value="{{ keyword }}"
    bind:focus="onFocus"
    bind:change="onChange"
    bind:search="onSearch"
    bind:cancel="onCancel"
  />

  <view class="head" wx:if="{{ !inSearch && cityCount }}">
    <view class="text">当前定位</view>
    <van-button custom-class="button" bind:click="onClickButton">
      {{ buttonText }}
    </van-button>
    <view class="text">所有支持自提的城市</view>
  </view>

  <block wx:if="{{ cityCount > 10 }}">
    <block wx:for="{{ currentCityMap }}" wx:for-item="list" wx:for-index="key" wx:key="{{ key }}">
      <block wx:if="{{ list.length }}">
        <view class="key">{{ key }}</view>
        <van-cell-group>
          <van-cell
            wx:for="{{ list }}"
            wx:for-item="city"
            wx:key="{{ city.cityName }}"
            title="{{ city.cityName }}"
            data-index="{{ index }}"
            data-key="{{ key }}"
            bind:click="onClickCity"
            value-class="value-class"
          />
        </van-cell-group>
      </block>
    </block>
  </block>

  <van-cell-group wx:elif="{{ cityCount }}" border="{{ false }}">
    <block wx:for="{{ currentCityMap }}" wx:for-item="list" wx:for-index="key" wx:key="{{ key }}">
      <van-cell
        wx:for="{{ list }}"
        wx:for-item="city"
        wx:key="{{ city.cityName }}"
        title="{{ city.cityName }}"
        data-index="{{ index }}"
        data-key="{{ key }}"
        bind:click="onClickCity"
        value-class="value-class"
      />
    </block>
  </van-cell-group>

  <view wx:elif="{{ listLoaded }}" class="empty">
    {{ inSearch ? cityCount === 0 && keyword ? '当前搜索城市没有提货点' : '' : '暂无支持提货的城市' }}
  </view>
</page-container>
