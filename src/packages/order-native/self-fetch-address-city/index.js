import { mapState } from '@youzan/vanx';
import { VanxPage } from 'pages/common/wsc-page/index';
import store from '@/packages/order-native/store/index';
import config from '@/packages/order-native/common/config';

const app = getApp();

VanxPage({
  store,

  mapData: {
    ...mapState(['location'])
  },

  data: {
    keyword: '', // 搜索关键字
    inSearch: false, // 搜索状态
    currentCityMap: {}, // 城市列表
    cityCount: 0, // 城市数量
    listLoaded: false, // 数据是否已加载
  },

  onLoad() {
    this.cityMap = {};

    this.$dispatch('LOADING');
    app
      .request({
        origin: 'cashier',
        method: 'GET',
        path: '/wsctrade/multistore/selfFetchPoint/getCityMap.json'
      })
      .then(res => {
        this.$dispatch('HIDE_TOAST');
        this.cityMap = res;
        this.setYZData({
          listLoaded: true,
          currentCityMap: res,
          cityCount: this.getCityCount(res),
          buttonText: this.getButtonText()
        });
      })
      .catch(() => {
        this.$dispatch('HIDE_TOAST');
        this.setYZData({
          listLoaded: true,
          buttonText: this.getButtonText()
        });
      });
  },

  // 获取当前城市总数
  getCityCount(currentCityMap) {
    return Object.keys(currentCityMap).reduce(
      (prev, key) => prev + currentCityMap[key].length,
      0
    );
  },

  getButtonText() {
    const { cityName, locating } = this.data.location;
    if (cityName) {
      let unsupport = false;
      if (this.data.listLoaded && JSON.stringify(this.cityMap).indexOf(cityName) === -1) {
        unsupport = true;
      }

      return `${cityName}${unsupport ? ' (该城市暂无提货点)' : ''}`;
    }
    if (locating) {
      return '定位中...';
    }
    return '定位失败，点击重试';
  },

  onClickButton() {
    const { location } = this.data;
    if (location.cityName) {
      this.onClickCity(null, location);
    } else if (!location.locating) {
      this.$dispatch('LOCATE_CITY').then(() => {
        this.setYZData({
          buttonText: this.getButtonText()
        });
      });
    }
  },

  // 选择城市
  onClickCity(event, location) {
    let city;
    if (event == null) {
      city = location;
    } else {
      const { index, key } = event.currentTarget.dataset;
      city = this.data.currentCityMap[key][index];
    }
    this.onCancel();
    this.$commit('SET_SELF_FETCH_CITY', city);
    this.trigger(config.eventKey.selfFetchCity);
    wx.navigateBack();
  },

  onFocus() {
    this.setYZData({ inSearch: true });
  },

  onChange(event) {
    this.setYZData({
      keyword: event.detail
    });
  },

  // 进行搜索
  onSearch() {
    const filteredCityMap = {};

    Object.keys(this.cityMap).forEach(key => {
      this.cityMap[key].forEach(city => {
        if (city.cityName.indexOf(this.data.keyword) !== -1) {
          if (filteredCityMap[key]) {
            filteredCityMap[key].push(city);
          } else {
            filteredCityMap[key] = [city];
          }
        }
      });
    });

    this.setYZData({
      currentCityMap: filteredCityMap,
      cityCount: this.getCityCount(filteredCityMap)
    });
  },

  // 取消搜索
  onCancel() {
    this.setYZData({
      keyword: '',
      inSearch: false,
      currentCityMap: this.cityMap,
      cityCount: this.getCityCount(this.cityMap)
    });
  }
});
