import Toast from '@vant/weapp/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import WscPage from 'pages/common/wsc-page/index';
import theme from 'shared/common/components/theme-view/theme';
import { exchangeCoupon } from './request';
import config from '../common/config';
import couponDesign from './design.json';
import { initSdk } from './saas/index';

const app = getApp();

WscPage({
  data: {
    themeGeneralColor: '',
    forbidcode: true, // 禁用优惠码兑换功能
    charge_coupon: [], // 可用优惠券
    unavailable_coupon: [], // 不可用优惠券
    selected_coupon: {}, // 已选中优惠券
    code: '', // 优惠码
    exchangeParams: {}, // 从下单页传过来的参数，不用关心内容，用于兑换优惠码接口
    coupon_design: [],
    couponData: {},
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const dbid = options.dbid || '';
    const forbidcode = !!options.forbidcode;
    const param = app.db.get(dbid) || {};
    const { charge_coupon = [], unavailable_coupon = [] } = param;

    theme.getThemeColor('general').then(color => {
      this.setYZData({
        themeGeneralColor: color
      });
    }).catch(() => {});

    this.setYZData({
      forbidcode,
      charge_coupon: mapKeysCase.toCamelCase(charge_coupon),
      unavailable_coupon: mapKeysCase.toCamelCase(unavailable_coupon),
      selected_coupon: param.selected_coupon || {},
      exchangeParams: param.exchangeParams || {},
    });
    this.getDesign();
    initSdk(this);
  },

  // 选择优惠券
  handleChooseCoupon(event) {
    const coupon = event.detail;
    this.trigger(config.eventKey.couponChange, mapKeysCase.toSnakeCase(coupon));
    wx.navigateBack();
  },

  // 兑换优惠码
  onExchangeTaped() {
    if (this.data.code.length == 0) return;

    const { charge_coupon, exchangeParams, code } = this.data;

    exchangeCoupon({
      ...exchangeParams,
      userCouponCode: code,
      ...(exchangeParams.ladderNum ? { extendScene: exchangeParams.ladderNum.toString() } : {}) // 适配多人拼团pro，加上 ladderNum（开团时候才有）参数
    })
      .then(res => {
        const existed = charge_coupon.some(item => item.id === res.id);

        if (existed) {
          Toast('该优惠码你已经拥有，已为你自动选中');
        } else {
          charge_coupon.unshift(res);

          this.trigger(config.eventKey.couponChange, mapKeysCase.toSnakeCase(res));
          this.setYZData({ code: '' });
        }

        this.setYZData({ charge_coupon, selected_coupon: res });
        initSdk(this);
      })
      .catch((err) => {
        Toast(err.msg || '兑换失败～');
      });
  },
  // 输入优惠码
  onCodeInput(event) {
    this.setYZData({ code: event.detail });
  },
  // 点击不使用优惠券
  onNotUseTapped() {
    this.trigger(config.eventKey.couponChange, {});
    wx.navigateBack();
  },
  // 获取design
  getDesign() {
    const modList = couponDesign.design || [];
    const newDesign = modList[0]?.type === 'config' ? modList.slice(1) : modList;
    this.setYZData({
      coupon_design: newDesign,
    });
  },
});
