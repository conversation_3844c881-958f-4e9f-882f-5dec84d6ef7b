import WscComponent from 'pages/common/wsc-component/index';
import { format } from '@youzan/weapp-utils/lib/time-utils';

WscComponent({
  externalClasses: [
    'theme-color'
  ],
  properties: {
    coupon: {
      type: Object,
      observer(value) {
        // ?? ob obj
        this.setYZData({
          extra: this.handleCoupon(value)
        });
      }
    },
    disabled: <PERSON><PERSON><PERSON>,
    selected: Boolean
  },
  data: {
    showMore: false, // 是否展开不可用条件
    extra: {}, // 用于展示，由优惠券数据产生
  },
  methods: {
    handleCoupon(coupon) {
      const startAt = format(new Date(1000 * coupon.startAt), 'yyyy.MM.dd');
      const endAt = format(new Date(1000 * coupon.endAt), 'yyyy.MM.dd');
      const validPeriod = `有效期：${startAt} - ${endAt}`;
      // 新版优惠券数据
      if (coupon.valueDesc || coupon.unitDesc) {
        return { validPeriod };
      }
      // 旧版减免券
      if (coupon.denominations) {
        return {
          valueDesc: coupon.denominations / 100,
          unitDesc: '元',
          validPeriod
        };
      }
      // 旧版折扣券
      return {
        valueDesc: coupon.discount / 10,
        unitDesc: '折',
        validPeriod
      };
    },
    // 切换不可用原因 展开状态
    handleShowMore() {
      this.setYZData({
        showMore: !this.data.showMore
      });
    },
    // 选中优惠券
    handleClick() {
      this.triggerEvent('click', this.data.coupon);
    }
  }
});
