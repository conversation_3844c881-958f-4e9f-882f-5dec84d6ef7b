function getYunSdk() {
  const app = getApp();
  if (!app.getYouZanYunSdk) {
    return false;
  }
  const sdk = app.getYouZanYunSdk();
  return sdk;
}

export function initSdk(vm) {
  const sdk = getYunSdk();
  vm.setYZData({
    couponData: {
      charge_coupon: vm.data.charge_coupon,
      unavailable_coupon: vm.data.unavailable_coupon,
      selected_coupon: vm.data.selected_coupon,
    },
  });
  if (sdk) {
    // 页面优惠券数据
    sdk.page.__setData('couponData', vm.data.couponData);
    // 输入优惠码
    sdk.setPageProcess('onCodeInput', vm.onCodeInput);
    // 兑换优惠码
    sdk.setPageProcess('onExchangeTaped', vm.onExchangeTaped);
    // 选择优惠券
    sdk.setPageProcess('handleChooseCoupon', vm.handleChooseCoupon);
    // 不使用优惠券
    sdk.setPageProcess('onNotUseTapped', vm.onNotUseTapped);
  }
}
