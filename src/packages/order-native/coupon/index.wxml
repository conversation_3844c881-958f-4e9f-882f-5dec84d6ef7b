<import src="./custom-tpl.wxml" />
<page-container class="page {{ themeClass }} page-{{ deviceType }}" page-container-class="van-coupon-list">
  <!--优惠码兑换-->
  <block wx:for="{{ coupon_design }}" wx:key="type">
    <block wx:if="{{ item.type === 'exchange-button' }}">
      <van-field
        wx:if="{{ !forbidcode }}"
        value="{{ code }}"
        center
        placeholder="请输入优惠码"
        use-button-slot
        maxlength="20"
        border="{{ false }}"
        bind:change="onCodeInput"
        bind:focus="onCodeFocus"
      >
        <van-button
          slot="button"
          size="small"
          custom-class="theme-bg-color"
          disabled="{{ code.length <= 0 }}"
          bind:click="onExchangeTaped"
        >{{ exchanging ? '验证中...' : '兑换' }}</van-button>
      </van-field>
    </block>

    <block wx:elif="{{ item.type === 'coupon-block' }}">
      <van-tabs
        custom-class="th_tabs-l"
        color="{{ themeGeneralColor }}"
      >
        <van-tab title="可使用优惠券（{{ charge_coupon.length }}）">
          <coupon-cell
            wx:for="{{ charge_coupon }}"
            wx:key="{{ item.id }}"
            coupon="{{ item }}"
            selected="{{ selected_coupon.id == item.id }}"
            bind:click="handleChooseCoupon"
            theme-color="theme-color"
          />
          <empty wx:if="{{ !charge_coupon.length }}" />
        </van-tab>
        <van-tab title="不可使用优惠券（{{ unavailable_coupon.length }}）">
          <coupon-cell
            wx:for="{{ unavailable_coupon }}"
            coupon="{{ item }}"
            wx:key="{{ item.id }}"
            disabled
          />
          <empty wx:if="{{ !unavailable_coupon.length }}" />
        </van-tab>
      </van-tabs>
    </block>

    <block wx:elif="{{ item.type === 'not-used-coupon' }}">
      <van-button
        size="large"
        custom-class="van-coupon-list__close"
        bind:click="onNotUseTapped"
      >不使用优惠</van-button>
    </block>
    <block wx:elif="{{ item.custom }}">
        <template is="{{ item.type }}" />
    </block>
  </block>
</page-container>

<van-toast id="van-toast" />
