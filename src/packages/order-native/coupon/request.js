const app = getApp();

export function validCouponCode(params) {
  return new Promise((resolve, reject) => {
    app.carmen({
      method: 'POST',
      api: 'kdt.ump.coupon.take/1.0.0/takecode',
      data: {
        fans_type: app.getFansType(),
        kdt_id: app.getKdtId(),
        ...params
      },
      success: resolve,
      fail: reject
    });
  });
}

export function exchangeCoupon(params) {
  return app
    .request({
      origin: 'cashier',
      path: '/pay/wsctrade/order/buy/exchangeCoupon.json',
      method: 'POST',
      data: params
    });
}
