/**
 * Vuex Store 入口文件
 */
import Store from '@youzan/vanx';
import { state } from './state';
import getters from './getters';
import actions from './actions';
import mutations from './mutations';
import * as countdown from './modules/countdown';
import * as contact from './modules/contact';
import * as log from './modules/log';
import * as shop from './modules/shop';
import * as goods from './modules/goods';
import * as ump from './modules/ump';
import * as delivery from './modules/delivery';
import * as selfFetch from './modules/self-fetch';
import * as address from './modules/address';
import * as order from './modules/order';
import * as price from './modules/price';
import * as invoice from './modules/invoice';
import * as design from './modules/design';
import * as saas from './modules/saas';
import * as retail from './modules/retail';
import * as shoppingComponent from './modules/shopping-component';
import * as tradeCarousel from './modules/trade-carousel/index.js';

const store = {
  state,
  getters,
  actions,
  mutations,
};

function mergeStore(store, key, module) {
  if (key in module) {
    store[key] = { ...store[key], ...module[key] };
  }
}

[
  countdown,
  contact,
  log,
  shop,
  goods,
  ump,
  delivery,
  selfFetch,
  address,
  order,
  price,
  invoice,
  design,
  saas,
  retail,
  shoppingComponent,
  tradeCarousel,
].forEach(module => {
  mergeStore(store, 'state', module);
  mergeStore(store, 'getters', module);
  mergeStore(store, 'actions', module);
  mergeStore(store, 'mutations', module);
});

let instance = new Store(store);

export function resetStore() {
  instance = new Store(store);
}

export default function getStore() {
  return instance;
}
