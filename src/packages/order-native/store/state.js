/**
 * Vuex 初始 state
 * 初始 State 来自 _global 和 localStorage
 */
import config from '@/packages/order-native/common/config';
import { get, isEmptyObject } from './utils';
import event from 'shared/utils/app-event';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { assignOrderCreation } from './order-station';
import { format } from '../utils/time/date';

// 初始 State
export const state = {
  // 页面是否初次渲染完毕
  dataLoaded: false,
  // 是否已经有预付卡信息
  hasPrepayCardData: false,
  // 页面请求参数
  // 助力定金膨胀定金支付阶段
  isHandselFirstPhase: false,

  // 页面请求参数
  pageQuery: {},

  // 下单页版本
  version: 1,

  // 下单挽留
  orderKeepApply: false,
  // 小程序下单是否唤起微信订阅apollo
  showWxSubscribe: false,
  isSHowStayBuyerDialog: true,

  // 环境
  env: {
    url: '',
    isPayPage: false,
    takeoutAlias: '', // 如果来自外卖模板，记录外卖模板的alias
    successUrl: '', // 成功后跳转路径
    serverTime: Date.now(),
  },

  display: {
    showAddressTab: false,
    showExpressTab: false,
    openDisplayCard: false,
    showLocalDeliveryTime: false,
    hasSubmit: false,
  },

  // 会员卡
  memberCard: {},

  // 用户
  user: {
    phone: '',
    delivery: {},
    idNumber: '',
  },

  // 店铺
  shop: {
    closed: false,
    closedDesc: '',
    shopName: '',
    activities: [],
  },

  // 周期购
  periodBuy: {
    chosenIndex: 0,
    options: [],
  },

  // 积分抵现
  pointDeduction: {
    status: 0,
    isUse: true,
  },

  // 支付
  pay: {
    prepay: false,
    prepaySuccess: false,
    multiPhase: false,
    payParams: {},
    prePayParams: {},
    phasePayment: {},
    promotions: [],
    realPay: 0,
    itemPay: 0,
    postage: 0,
  },

  tradeTag: {},

  displayCard: {},

  // 风险店铺提示
  riskWarnShopPrompt: '',

  // 疫情提示
  epidemicSituationTips: {},

  // 内购券
  fissionActivity: {
    fissionName: '',
    fissionTicketNum: 0,
  },

  // 粉丝优惠
  fansBenefit: {
    id: 0,
    name: '',
    title: '',
    type: '',
    typeId: 0,
    value: 0,
  },

  extra: {},

  // 是否是零售订单
  isRetail: false,

  shopStoredDiscountInfo: {}, // 店铺储值专享折扣信息

  useStoredCustomerDiscount: false, // 订单是否享受储值专享折扣

  showPrePayCardRecharge: false, // 打开储值弹窗并转到充值页

  disableStoredDiscount: false, // 是否禁用折扣，页面维护
  // 是否忽略海淘商品身份绑定（白名单内店铺跳过海淘商品认证）
  ignoreIdBinding: false,

  // 有赞担保
  guarantee: {
    hasYzSecured: false, // 是否是有赞担保订单
    hasFreightInsurance: false, // 是否打标退货包运费
    freightInsuranceFree: '0', // 退货包运费赠送标
    yzGuarantee: false, // 下单页是否显示有赞担保 bar
    yzGuaranteeInfo: {}, // 有赞担保商详页展示信息（提交订单时传给交易后端做交易快照）
    yzGuaranteeDocs: {}, // 有赞担保文案信息（配置在 Apollo）
  },

  // 先用后付数据
  useBeforePayData: {
    show: false, // 先用后付是否露出
    enable: true, // 先用后付是否可以点击
    confirm: '0', // 先用后付是否已经选中,
    protocol: false, // 先用后付用户协议是否展示
    range: [null, null], // 先用后付金额限制
    reason: [null, null], // 先用后付金额限制时的文案
  },

  // // 切流完毕后可以下掉
  enableExpressTypeChoiceParam: false,
  // // 切流完毕后可以下掉
  enableUseOrderBehaviorParam: false,
  // 行为组件参数
  behaviorOrderInfo: {
    bizType: 158,
    token: '',
  },
  countdownAbTestConfig: {},

  presetFromParams: '', // 用于覆盖下单接口参数中的FromParams
};

/**
 * 更新订单数据
 * @param {*} state
 * @param {*} data
 * @param {object} options isInitCoupon: 初始化默认选中优惠券，防止从 商品兑换券 切换为不使用调用 confirm 接口后重置
 */
export function assignOrderData(
  state,
  data,
  { isInitCoupon = true, isFetchShow = false, isAddressIdChange = false } = {}
) {
  const trade = get(data, 'tradeConfirmation');
  const display = get(data, 'displayConfig');
  const shop = get(trade, 'shop');
  const items = get(trade, 'orderItems', []);
  const group = get(trade, 'group');
  const postage = get(trade, 'postage');
  const delivery = get(trade, 'delivery');
  const tradeTag = get(trade, 'tradeTag');
  const orderConfig = get(trade, 'orderConfig');
  const activityType = get(trade, 'activityType');
  const idcard = get(orderConfig, 'idCard');
  const localDelivery = get(data, 'shopDelivery');
  const deliveryTimeBucket = get(data, 'deliveryTimeBucket');
  const address = get(data, 'address');
  const contact = get(data, 'contact');
  const pointsName = get(data, 'pointsName');
  const orderPayment = get(trade, 'orderPayment');
  const customerCards = get(trade, 'customerCards', []);
  const unavailableCustomerCards = get(trade, 'unavailableCustomerCards', []);
  const phasePayment = get(trade, 'phasePayment', {});
  const priorUseData = get(data, '@cashier/prior-use', {});
  const extra = get(trade, 'extra', {});
  const umpSendPromotionInfo = get(trade, 'umpSendPromotionInfo', {});
  // 到店自提有礼
  const storeGiftsInfo = get(trade, 'selfUmpPromotionInfo', {});
  const contactId = get(data, 'contact.id', '');
  const currentContact =
    get(data, 'contact.list', []).find((item) => item.id === contactId) || {};
  const orderCreation = get(data, 'orderCreation');

  // 获取店铺是否开启轮播的配置
  const shopConfig = get(data, 'shopConfig', {});

  if (!isEmptyObject(orderCreation)) {
    // 缓存orderCreation数据
    assignOrderCreation(orderCreation);

    // 只有prepare-by-bookkey（即首次请求）能取到
    const orderMark = get(orderCreation, 'source.orderMark', '');
    if (orderMark) {
      // 是否是零售订单
      state.isRetail = orderMark === 'retail_minapp_shelf';
      // 是否是扫码购订单
      state.isScanCodeBuy = orderMark === 'online_scan_buy';
    }
  }

  state.countdownAbTestConfig = get(data, 'countdownAbTestConfig', {});

  // 是否应用优惠券积分抵现流程新逻辑
  const newCouponProcess = get(orderCreation, 'newCouponProcess', '');

  // 助力定金膨胀定金支付阶段
  const activityTypeItemsA = get(orderCreation, 'items', []);
  const activityTypeItemsB = get(data, 'tradeConfirmation.orderItems', []);

  state.ignoreIdBinding = get(data, 'ignoreIdBinding', state.ignoreIdBinding);

  // 下单页版本
  state.version = ECLOUD_MODE ? 1 : get(data, 'version', state.version);

  // 下单挽留项目 apollo
  state.orderKeepApply = get(data, 'orderKeepApply', state.orderKeepApply);
  // 小程序下单是否唤起微信订阅apollo
  state.showWxSubscribe = get(data, 'showWxSubscribe', state.showWxSubscribe);
  const isHandsel =
    (activityTypeItemsA[0] && activityTypeItemsA[0].activityType === 116) ||
    (activityTypeItemsB[0] && activityTypeItemsB[0].activityType === 116);

  if (
    isHandsel &&
    phasePayment.currentPhase &&
    phasePayment.currentPhase === 1
  ) {
    state.isHandselFirstPhase = true;
  }

  const item0 = items[0] || {};

  // 已请求到数据，页面可以进行渲染
  if (!state.dataLoaded) {
    const activeTab = get(trade, 'postage.currentExpressType') === 1 ? 1 : 0;
    state.address.activeTab = activeTab;

    setTimeout(() => {
      state.dataLoaded = true;
    }, 0);
  }

  // NOTE: 额外信息，目前内部没有用到，仅提供有赞云使用
  extra.FREIGHT_INSURANCE_FREE =
    extra.FREIGHT_INSURANCE_FREE || config.freightInsuranceTag.NONE;
  state.extra = {
    ...state.extra,
    ...extra,
  };

  // 联系人、自提
  if (!isEmptyObject(contact)) {
    state.contact = {
      ...state.contact,
      ...contact,
    };
  }
  // 地址
  if (!isEmptyObject(address)) {
    // 收货地址降级缓存 清理一下过期的缓存
    const downgradeMap =
      wx.getStorageSync(config.cacheKey.addressDowngrade) || {};
    const inTime = Object.keys(downgradeMap).every((id) => {
      const item = downgradeMap[id];
      if (Date.now() >= item.expired) {
        delete downgradeMap[id];
        return false;
      }
      return true;
    });
    if (!inTime) {
      wx.setStorage({
        key: config.cacheKey.addressDowngrade,
        data: downgradeMap,
      });
    }

    (address.list || []).forEach((item) => {
      if (item.id in downgradeMap) {
        item.downgrade = true;
      }
    });
    state.address = {
      ...state.address,
      ...address,
      isSelfFetchDefault: display?.openSelfDefaultSwitch || false,
    };
  }

  // 只能自提的话就 改成1，只能配送就是0
  // 都可就保持不变
  const { showExpressTab } = display;
  const canSelfFetch = display.canSelfFetch || 'selfFetch' in delivery;

  if (showExpressTab && !canSelfFetch) {
    state.address.activeTab = 0;
  } else if (!showExpressTab && canSelfFetch) {
    state.address.activeTab = 1;
  }

  const adjustToChineseTimeZone =
    (new Date().getTimezoneOffset() + 8 * 60) * 60 * 1000;

  // 环境
  state.env = {
    ...state.env,
    serverTime: display.serverTime + adjustToChineseTimeZone,
  };

  // 用户
  state.user = {
    ...state.user,
    delivery,
  };

  // 物流
  state.postage = {
    ...state.postage,
    ...postage,
  };

  // 商品类型
  state.tradeTag = {
    ...state.tradeTag,
    ...tradeTag,
  };

  /**
   * 赠品
   * 后端每次会重新计算，直接赋值即可
   * coupons = [], // 赠品优惠券列表
   * couponsCode = [], // 赠送优惠码列表
   * presents = [], // 赠品列表
   * score = 0 // 赠送的积分
   */
  state.umpSendPromotionInfo = umpSendPromotionInfo;
  state.storeGiftsInfo = storeGiftsInfo;
  // 展示相关配置
  state.display = {
    ...state.display,
    ...display,
    newRecommend: trade.newRecommend,
    serverTime: display.serverTime + adjustToChineseTimeZone,
    invoiceContent: +get(display, 'invoiceContent', 11), // 开票内容 10：开启商品明细开票；01：开启商品类目开票；11：两种方式均开启。
    payPrompt: get(display, 'payPrompt', ''),
    prompt: get(display, 'prompt', ''),
    selectedInstallmentPeriod: get(display, 'selectedInstallmentPeriod', 0), // 分期期数
    installmentRate: get(display, 'installmentRate', 0), // 分期费率
  };

  const invoiceContentType = +get(display, 'invoiceContent', 0);
  if (invoiceContentType === 10) {
    if (state.invoice.personal.invoiceDetailType !== 'itemDetail') {
      state.invoice.personal.invoiceDetailType = 'itemDetail';
      state.invoice.enterprise.invoiceDetailType = 'itemDetail';
    }
  }

  state.riskWarnShopPrompt =
    get(display, 'riskWarnShopPrompt', '') || state.riskWarnShopPrompt;

  // 会员卡
  state.memberCard = {
    ...state.memberCard,
    ...get(trade, 'memberCard'),
  };

  // 身份证
  state.idcard = {
    ...state.idcard,
    binding: !!(idcard.binding && !state.ignoreIdBinding),
    name: idcard.idCardName || state.idcard.name || '',
    number: idcard.idCardNumber || state.idcard.number || '',
    backPhoto: idcard.idCardBackPhoto || state.idcard.backPhoto || '',
    frontPhoto: idcard.idCardFrontPhoto || state.idcard.frontPhoto || '',
  };

  // 店铺
  state.shop = {
    ...state.shop,
    ...shop,
    activityType: trade.activityType,
    activities: get(trade, 'activities', []),
    activitiesNames: get(trade, 'activityNames', []),
    cashBackList: get(trade, 'cashBackList', []),
  };

  // 商品
  state.goods = {
    ...state.goods,
    list: items,
    unavailable: get(trade, 'unavailableItems', []),
    prepareTime: get(trade, 'deliveryCheck.prepareTime', 0),
  };
  // 会员展示卡
  state.displayCard = get(trade, 'displayCard', {});

  // 优惠券
  state.coupon = {
    chosenId: 0,
    ...state.coupon,
    list: get(trade, 'coupons', []),
    disabledList: get(trade, 'unavailableCoupons', []),
  };

  // 内购券
  state.fissionActivity = {
    ...state.fissionActivity,
    ...get(trade, 'fissionActivity', {}),
  };

  // 粉丝优惠
  state.fansBenefit = {
    ...state.fansBenefit,
    ...get(trade, 'fansBenefit', {}),
  };
  //  这里可能怕影响老板 可能得针对新旧版分开处理
  // 积分抵现
  // state.pointDeduction = {
  //   ...state.pointDeduction,
  //   ...get(trade, 'pointDeduction')
  // };

  // 积分兑换
  state.pointDeduction = {
    ...{
      status: state.pointDeduction.status,
      isUse: state.pointDeduction.isUse,
      costPoints: state.pointDeduction.costPoints,
    },
    ...get(trade, 'pointDeduction'),
  };

  // 积分
  state.points = {
    ...state.points,
    ...get(data, 'pointsConfig'),
  };

  state.customerCards = {
    ...state.customerCards,
    list: customerCards,
  };

  state.unavailableCustomerCards = {
    ...state.unavailableCustomerCards,
    list: unavailableCustomerCards,
  };
  const chosenCustomCard = customerCards.find((item) => item.chosen);
  if (chosenCustomCard && chosenCustomCard.id) {
    state.customerCards = {
      ...state.customerCards,
      id: chosenCustomCard.id,
    };
  }

  // 自提
  state.selfFetch = {
    ...state.selfFetch,
    time: state.selfFetch.time || display.selfFetchTime || '',
    isAllow: display.canSelfFetch || 'selfFetch' in delivery,
  };

  // 物流信息从这里取
  if (!isEmptyObject(deliveryTimeBucket)) {
    state.selfFetch = {
      ...state.selfFetch,
      timeSpan: deliveryTimeBucket.timeSpan,
      timeBucket: deliveryTimeBucket.timeBuckets,
      deliveryTimeBucket,
    };
  }

  // 周期购
  state.periodBuy = {
    ...state.periodBuy,
    show: !!item0.issue,
    info: item0.issue || '',
    planTime: item0.planExpressTime || '',
    options: item0.deliverOptions || [],
    chosenOption: item0.deliverOption || '',
  };

  // 拼团
  if (!isEmptyObject(group)) {
    if (group.receiveState === config.groupon.forceReceive) {
      state.groupon.isChecked = true;
    }

    state.groupon = {
      ...state.groupon,
      isGroupon: true,
      isHeader: group.header,
      showAgencyReceive: group.displayAgencyReceive,
      receiveState: group.receiveState,
      headerName: group.headerUserName || '',
      activityType,
    };

    if (group.headerUserName) {
      state.groupon = {
        ...state.groupon,
        headerAddress: {
          userName: group.headerUserName,
          tel: group.headerTel,
          province: group.headerProvince,
          city: group.headerCity,
          county: group.headerCounty,
          areaCode: group.headerAreaCode,
          addressDetail: group.headerAddressDetail,
          community: group.headerCommunity,
          idCardNumber: group.idCardNumber,
        },
      };
    }
  }

  // 联系人
  state.contact = {
    ...state.contact,
    required:
      !display.showAddressTab &&
      items.some(({ virtualType }) => virtualType === 3),
    // 当前显示的联系人名称和联系方式，v2用
    userName: currentContact.userName || state.contact.userName,
    telephone: currentContact.telephone || state.contact.telephone,
  };

  // 同城送
  state.delivery = {
    ...state.delivery,
    text: state.delivery.text || postage.deliveryTimeDisplay || '',
  };
  if (!isEmptyObject(localDelivery)) {
    state.delivery = {
      ...state.delivery,
      ...localDelivery,
    };
  }
  // 计算送达时间的逻辑放到后端
  if (!isEmptyObject(deliveryTimeBucket)) {
    const { instantTimePoint, timeBuckets, timeSpan } = deliveryTimeBucket;
    let { textWithWeekday, text } = state.delivery;

    if (instantTimePoint && !display.showLocalDeliveryTime) {
      textWithWeekday = `尽快送达（预计 ${format.time(
        new Date(instantTimePoint)
      )}）`;
      text = format.dateTime(new Date(instantTimePoint));
    } else if (
      isAddressIdChange &&
      get(state, 'postage.currentExpressType') === 2
    ) {
      textWithWeekday = '';
      text = '';
    }

    state.delivery = {
      ...state.delivery,
      timeSpan,
      timeBucket: timeBuckets,
      deliveryTimeBucket,
      // 展示的送达时间文案（下单页送达时间项、时间选择弹窗）
      textWithWeekday,
      // 送达时间的唯一标识，用于时间选择弹窗（client/pages/order/buy/component/time-picker） 匹配 选中项
      text,
      instantTimePoint,
    };
  }

  // 支付
  state.pay = {
    ...state.pay,
    ...orderPayment,
    extraFees: get(orderPayment, 'extraFees', []),
    phasePayment,
    promotions: get(orderPayment, 'promotions', []),
    orderPromotions: get(orderPayment, 'orderPromotions', []).filter(
      (item) => item.promotionType !== 'storedCustomerDiscount'
    ),
    multiPhase: trade.multiPhasePayment,
    payCardsShowUsableNum: trade.payCardsShowUsableNum,
  };

  // 隐藏储值卡模块则取消默认选择储值卡
  if (state.componentExtValueCard.display) {
    // 储值卡的选中信息服务端决定
    Object.assign(state.valueCard, {
      list: get(trade, 'payValueCards', []),
      disabled: get(trade, 'unavailablePayValueCards', []),
      checked: get(trade, 'payValueCards', [])
        .filter((item) => item.selected)
        .map((item) => item.summaryCardNo),
    });
  }

  state.giveRechargeAmount = get(trade, 'giveRechargeAmount', 0); // 储值卡待激活使用的余额

  state.payAssetActivityTagDesc = trade.payAssetActivityTagDesc || '';

  state.recommendDetaid = trade.recommendDetaid;

  if (isInitCoupon) {
    if (!state.order.couponDisplay || !state.componentExtCoupon.display) return;
    // 如果是0元单或积分足额抵扣，优惠券与储值卡/礼品卡不设默认选中
    if (state.pay.realPay === 0) {
      if (newCouponProcess || state.order.newCouponProcess) {
        const chosenCoupon =
          state.coupon.list.find((item) => item.choose) || {};
        event.trigger(
          config.eventKey.couponChange,
          mapKeysCase.toSnakeCase(chosenCoupon || {})
        );
        state.coupon.chosenId = get(chosenCoupon, 'id', 0);
      } else {
        state.coupon.chosenId = 0;
        // 0元单触发 兑换券 取消选中
        event.trigger(
          config.eventKey.couponChange,
          mapKeysCase.toSnakeCase({})
        );
      }
      state.valueCard.checked = [];
    } else if (newCouponProcess || state.order.newCouponProcess) {
      // 如果是新流程 后端这边会告知最新的优惠券
      const chosenCoupon = state.coupon.list.find((item) => item.choose) || {};
      event.trigger(
        config.eventKey.couponChange,
        mapKeysCase.toSnakeCase(chosenCoupon || {})
      );
      state.coupon.chosenId = get(chosenCoupon, 'id', 0);
    } else {
      // 使用会员卡后，兑换券失效，需要重新获取商品分组
      event.trigger(
        config.eventKey.couponChange,
        mapKeysCase.toSnakeCase(state.coupon.list[0] || {})
      );
      // 初始化页面时，config.eventKey.couponChange 事件不存在，默认选中第一张
      const firstCoupon = state.coupon.list[0] || {};
      if (firstCoupon.value) {
        state.coupon.chosenId = get(firstCoupon, 'id', 0);
      }
    }
  }

  // 订单已创建
  state.order = {
    ...state.order,
    ...orderConfig,
    steps: trade.steps || [],
    openDisplayCard: display.openDisplayCard,
    // 是否显示排队浮层并阻止下单
    limitCreate: false,
    limitCreateMessage: '',

    // 是否异步下单
    asyncOrder: display.asyncOrder || false,
    // 异步订单bookKey
    asyncOrderBookKey: '',
    // 扩展字段 目前只有直播优惠券在使用
    extensions: {
      ...get(data, 'extensions', {}),
      ...(state.order?.extensions || {}),
    },
  };

  if (typeof newCouponProcess === 'boolean') {
    state.order.newCouponProcess = newCouponProcess;
  }
  // 积分自定义名称，避免值被覆盖
  if (typeof pointsName === 'string') {
    state.pointsName = pointsName;
  }

  // 用户协议和隐私政策
  state.userPrivacy = get(data, 'userPrivacy', {});
  state.agreement = get(data, 'agreement', {});

  // 店铺储值专享折扣信息
  if (extra.SHOP_STORED_DISCOUNT_INFO) {
    let temp = {};
    try {
      temp = JSON.parse(extra.SHOP_STORED_DISCOUNT_INFO);
    } catch (err) {
      // do nothing
    }
    state.shopStoredDiscountInfo = temp;
  }

  // 订单是否享受储值专享折扣
  state.useStoredCustomerDiscount =
    extra.USE_STORED_CUSTOMER_DISCOUNT === 'true';

  // 是否禁用折扣，页面维护
  if (!isFetchShow) {
    state.disableStoredDiscount =
      state.shopStoredDiscountInfo.openStoredDiscount === false;
  }

  // 初始化先用后付数据
  state.useBeforePayData = {
    ...state.useBeforePayData,
    ...priorUseData,
  };
  // 下单页跑马灯
  state.shopConfig = {
    ...state.shopConfig,
    ...shopConfig,
  };
  // 切流完毕后下掉
  state.enableUseOrderBehaviorParam =
    state.display.enableUseOrderBehaviorParam || false;
}

// 更新支付数据
export function assignPayment(state, data) {
  const prepayParams = data.prePaymentPreparation || {};
  const paymentPreparation = data.paymentPreparation || {};
  prepayParams.bizExt = paymentPreparation.bizExt || '{}';

  if (state.tradeTag.continuousOrder) {
    // 会员等级连续订阅只能用银行卡支付
    // NOTE: 这个场景的下单不在这个页面，兼容支付页场景
    prepayParams.scene = 'MEM_SUB';
  } else {
    prepayParams.scene = 'VALUE_COMMON';
  }

  state.pay = {
    ...state.pay,
    prepayParams,
    prepay: prepayParams.prepay,
    prepaySuccess: prepayParams.prepaySuccess,
    payParams: data.paymentPreparation,
    showPayResult: data.showPayResult,
  };
}
