/**
 * Vuex actions
 * 接口异步请求、通用页面动作
 */
export default {
  // 显示提示
  TOAST(_, ...args) {
    let options = {};
    if (typeof args[0] === 'object') {
      options = args[0];
    } else {
      options = {
        title: args[0],
        icon: args[1],
        duration: args[2],
      };
    }
    wx.showToast({
      ...options,
      title: options.title == null ? '加载中' : options.title,
      icon: options.icon == null ? 'none' : options.icon,
      duration: options.duration == null ? 3000 : options.duration,
    });
  },

  // 隐藏提示
  HIDE_TOAST() {
    wx.hideToast();
  },

  HIDE_LOADING() {
    wx.hideToast();
  },

  LOADING({ dispatch }, payload) {
    dispatch('TOAST', {
      icon: 'loading',
      duration: 100000,
      mask: true,
      ...payload
    });
  }
};
