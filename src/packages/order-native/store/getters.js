export default {
  // 身份证号
  maskedIdNumber(state) {
    const { delivery } = state.user;
    let idNumber = state.idcard.number;
    if (delivery) {
      const { address, selfFetch } = delivery;
      if (address && address.idCardNumber) {
        idNumber = address.idCardNumber;
      }
      if (selfFetch && selfFetch.idCardNumber) {
        idNumber = selfFetch.idCardNumber;
      }
    }

    if (idNumber.length === 15) {
      return idNumber.replace(/(\d{3})\d{8}(\d{4})/, '$1********$2');
    }

    if (idNumber.length === 18) {
      return idNumber.replace(/(\d{3})\d{11}(\d{4})/, '$1***********$2');
    }

    return '';
  },

  showTradeCarousel(state) {
    return (state.shopConfig.goodsTradeMarquee && +state.shopConfig.goodsTradeMarquee.show === 1)
  }
};
