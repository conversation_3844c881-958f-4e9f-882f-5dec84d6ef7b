/**
 * Vuex mutations
 * 所有对 store state 的修改都需要通过 mutation 操作
 */
export default {
  SET_PAGE_QUERY(state, payload) {
    Object.assign(state.pageQuery, payload);
  },

  EXTEND_ENV(state, payload) {
    Object.assign(state.env, payload);
  },

  SET_TAKEOUT_ALIAS(state, takeoutAlias) {
    state.env.takeoutAlias = takeoutAlias;
  },

  EXTEND_GROUP_DATA(state, payload) {
    Object.assign(state.groupon, payload);
  },

  EXTEND_ORDER_DATA(state, payload) {
    Object.assign(state.order, payload);
  },

  // 设置是否存在提交订单栏，用于储值推荐
  SET_HAS_SUBMIT(state, payload) {
    state.display.hasSubmit = payload;
  },

  FILL_PRESET_FROM_PARAMS(state, payload) {
    const { fromParams } = payload;
    state.presetFromParams = fromParams;
  },

  SET_ALL_GOODS_GUIDE_PARAMS(state) {
    const { order, presetFromParams: replaceFromParams } = state;
    const { source = {} } = order;
    const fromParamsObj = {};

    replaceFromParams.split('!').forEach((param) => {
      const [key, value] = param.split('~');
      fromParamsObj[key] = value;
    });

    if (Array.isArray(source.itemSources) && replaceFromParams) {
      // 统一将 bizTracePointExt 的 from_params 字段改为指定值
      source.itemSources.forEach((item) => {
        if (item.bizTracePointExt) {
          try {
            item.bizTracePointExt = JSON.stringify({
              ...JSON.parse(item.bizTracePointExt),
              from_params: replaceFromParams,
              sl: fromParamsObj.sl,
            });
          } catch (e) {
            console.log(e);
          }
        }
      });
    }
  },
};
