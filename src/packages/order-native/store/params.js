/**
 * 接口入参拼接
 * 主要用于 show/bill 这两个复杂且共性较高的接口
 */
import get from '@youzan/weapp-utils/lib/get';
import pick from '@youzan/weapp-utils/lib/pick';
import money from '@youzan/weapp-utils/lib/money';
import CommonConfig from '@/packages/order-native/common/config';
import { isEmptyObject } from './utils';
import { checkRetailShop } from '@youzan/utils-shop';

function formatPhone(value = '') {
  return value.replace(/[^-|\d]/g, '');
}

// show/bill 接口使用的 address 入参
const getAddressParam = (state, getters) => {
  const { delivery } = state;
  const address =
    getters.expressType === 'express' ? { ...getters.currentAddress } : {};

  if (isEmptyObject(address)) {
    return;
  }

  if (address.id) {
    address.addressId = address.id;
  }

  // 合并门牌号到详细地址
  if (address.houseNumber) {
    address.addressDetail += ' ' + address.houseNumber;
    delete address.houseNumber;
  }

  // 经纬度为 string 类型
  if (address.lat && address.lon) {
    address.lat += '';
    address.lon += '';
  }

  if (address.tel) {
    address.tel = formatPhone(address.tel);
  }

  // 同城送
  if (state.display.showLocalDeliveryBucket) {
    const { instantTimePoint, startTime, endTime, timeSpan } = delivery;
    if (!startTime && !endTime && instantTimePoint) {
      address.instantTimePoint = instantTimePoint;
    } else if (state.display.showLocalDeliveryTime) {
      address.deliveryStartTime = startTime;
      address.deliveryEndTime = endTime;
      address.deliveryTimeSpan = timeSpan;
    }
  }

  return address;
};

export function getCostPointsParam(state, isUse = state.pointDeduction.isUse) {
  const { costPoints } = state.pointDeduction;
  return {
    kdtId: state.shop.kdtId,
    usePointDeduction: isUse,
    costPoints: isUse ? costPoints : 0,
  };
}

// 拼装ump参数
export function getUmpParam(
  state,
  getters,
  isUsePointDeduction,
  isSetCustomerCard
) {
  const { order, plusBuy } = state;
  // 优惠券信息
  const coupon = {};
  if (getters.chosenCoupon.id) {
    coupon.id = state.coupon.externalId || getters.chosenCoupon.id;
    coupon.couponType = getters.chosenCoupon.type;
    coupon.outerCoupon = state.coupon.externalId ? 1 : 0;
  }

  // 选择店铺活动
  const useCustomerCardInfo = {
    specified: false,
  };
  if (state.customerCards.id != null) {
    useCustomerCardInfo.customerCardId = state.customerCards.id;
    useCustomerCardInfo.specified = true;
  }

  // 外部积分 id
  if (state.points.externalId) {
    const { activities } = order.ump;
    if (Array.isArray(activities)) {
      activities.forEach((item) => {
        if (item.usePoints) {
          item.externalPointId = state.points.externalId;
        }
      });
    }
  }

  // 加价购 加购商品数据拼装
  let { activities = [] } = order.ump;
  if (Array.isArray(plusBuy.selected) && plusBuy.selected.length > 0) {
    activities = [
      ...activities,
      ...plusBuy.selected.map((item) =>
        pick(item, [
          'activityId',
          'activityType',
          'goodsId',
          'kdtId',
          'skuId',
          'propertyIds',
        ])
      ),
    ];
  }

  const result = {
    ...order.ump,
    activities,
    coupon,
    useCustomerCardInfo,
    costPoints: getCostPointsParam(state, isUsePointDeduction),
  };

  // 这里的处理含义是 如果是针对新流程 且是切换会员卡情况下，则对coupon参数进行覆盖，告诉后端选择最优的优惠券
  if (order.newCouponProcess && isSetCustomerCard) {
    result.coupon = {
      systemChoose: true,
    };
  }
  return result;
}

// 额外的物流参数
const addExtraDeliveryParam = (state, item) => {
  const { groupon, periodBuy } = state;

  // 海淘身份证
  if (state.idcard.number) {
    item.idCardNumber = state.idcard.number;
  }

  // 周期购
  if (periodBuy.options.length && periodBuy.options[periodBuy.chosenIndex]) {
    item.deliverOption = periodBuy.options[periodBuy.chosenIndex];
  }

  // 拼团
  if (groupon.isGroupon) {
    item.groupHeader = groupon.isHeader;
    item.groupHeaderReceive = groupon.isChecked;
    item.receiveState = groupon.receiveState;
  }

  return item;
};

// show/bill 接口使用的 delivery 入参
export const getDeliveryParam = (state, getters, options = {}) => {
  const { expressType, currentContact, isRetailWarehouseMode } = getters;
  const isFormDbId = options.isFormDbId;
  const isSelfFetch = expressType === 'self-fetch';
  const delivery = {
    hasFreightInsurance: state.tradeTag.hasFreightInsurance,
  };

  if (state.display.showAddressTab) {
    const address = getAddressParam(state, getters);
    if (address) {
      addExtraDeliveryParam(state, address);
      delivery.address = address;
      if (
        state.display.showLocalDeliveryBucket &&
        options.isNeedAppointmentType
      ) {
        delivery.appointmentType =
          state.delivery.startTime && state.delivery.endTime
            ? CommonConfig.appointmentType.regular
            : CommonConfig.appointmentType.instant;
      }
    }
    delivery.expressType = expressType;
  }

  // 自提
  if (isSelfFetch) {
    const { selfFetch } = state;

    delivery.selfFetch = {
      ...selfFetch.shop,
      selfFetchStartTime: selfFetch.selfFetchStartTime,
      selfFetchEndTime: selfFetch.selfFetchEndTime,
      appointmentId: currentContact.id,
      appointmentTime: getters.selfFetchTime,
      appointmentTel: currentContact.telephone,
      appointmentPerson: currentContact.userName,
    };

    addExtraDeliveryParam(state, delivery.selfFetch);

    // 经纬度为 string 类型
    delivery.selfFetch.lat =
      delivery.selfFetch.lat == null
        ? delivery.selfFetch.lat
        : delivery.selfFetch.lat + '';
    delivery.selfFetch.lng =
      delivery.selfFetch.lng == null
        ? delivery.selfFetch.lng
        : delivery.selfFetch.lng + '';

    // businessHoursAdvanced 需要为 string 类型
    if (delivery.selfFetch.businessHoursAdvanced) {
      delivery.selfFetch.businessHoursAdvanced = JSON.stringify(
        delivery.selfFetch.businessHoursAdvanced
      );
    }
  }

  // 零售24小时货架信息
  if (state.retailOrderScene === '24hshelf') {
    if (isSelfFetch) {
      delivery.pickUpWay = getters.retailPickUpWayValue;
      const app = getApp();
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'switch_pickup_way',
          en: '自提取货方式',
          params: {
            label: getters.retailPickUpWayName,
            value: getters.retailPickUpWayValue,
          },
        });
    } else if (isRetailWarehouseMode) {
      delivery.dispatcherWarehouseId = state.warehouseId;
    }
  }

  // 联系人
  if (state.contact.required) {
    delivery.contacts = {
      id: currentContact.id,
      recipients: currentContact.userName,
      tel: currentContact.telephone,
      groupHeader: state.groupon.isHeader,
    };
    // 价格日历
    const appointmentTime = get(
      state.order.delivery,
      'contacts.appointmentTime',
      ''
    );
    if (appointmentTime) {
      Object.assign(delivery.contacts, { appointmentTime });
    }
  }

  // 自提时 expressTypeChoice 固定传 1
  const choice = isSelfFetch ? 1 : state.postage.currentExpressType;
  // 自提需要传，非自提但是choice===1（这种场景一般是从自提到商家配送切换时命中）不需要传
  // 下面代码是对(!(!isSelfFetch && choice === 1) || isSelfFetch)的语义简化
  if (!isFormDbId) {
    if (isSelfFetch || choice !== 1) {
      delivery.expressTypeChoice = choice;
    }
  }
  if (!isNaN(options.expressTypeChoice)) {
    delivery.expressType =
      +options.expressTypeChoice === 1 ? 'self-fetch' : 'express';
    delivery.expressTypeChoice = +options.expressTypeChoice;
  }
  return delivery;
};

// 商品参数
export const getOrderItemsParam = (state, getters) => {
  const { order, goods, displayCard, plusBuy } = state;
  const items = order.items || [];
  let itemsCopy = JSON.parse(JSON.stringify(items));
  const goodsList = goods.list || [];
  const { chosenCoupon } = getters;

  // 勾选会员卡的情况下items加入会员卡商品
  if (order.openDisplayCard) {
    const { kdtId, goodsId, skuId } = displayCard;
    itemsCopy = [
      ...itemsCopy,
      {
        kdtId,
        goodsId,
        skuId,
        num: 1,
      },
    ];
  }

  itemsCopy.forEach((item) => {
    if (!item.extensions) item.extensions = {};

    // 使用商品兑换券标，后端根据兑换券标识对商品进行拆分
    //  groupType  7:优惠券 9:一卡一码 10:通用码 11:社区团购券 12:三方券 13:兑换券2.0
    if (chosenCoupon.groupType === 13) {
      // 获取 兑换券itemId 对应的 OUTER_ITEM_ID
      const chosenCouponGoods =
        goodsList.find(
          (goods) =>
            goods.itemId === chosenCoupon.optimalPreferentialOrderItemIds[0]
        ) || {};
      const { outerItemId } = chosenCouponGoods;

      if (outerItemId && +outerItemId === +item.extensions.OUTER_ITEM_ID) {
        item.extensions.USE_GOODS_EXCHANGE_COUPON = '1';
      }
    }
  });

  if (Array.isArray(plusBuy.selected) && plusBuy.selected.length > 0) {
    itemsCopy = [...itemsCopy, ...plusBuy.selected];
  }

  return itemsCopy;
};

// 拼装储值卡 / 礼品卡参数
export const getUsePayAssetParam = (state) => {
  const usePayAsset = {};
  const { valueCard } = state;

  // 储值卡
  if (valueCard.checked.length) {
    usePayAsset.valueCardNos = valueCard.checked;
  }

  // 如果有使用储值卡 / 礼品卡， 设置店铺id
  if (usePayAsset.Nos || usePayAsset.valueCardNos) {
    usePayAsset.kdtId = state.shop.kdtId;
  }

  return usePayAsset;
};

export function getBizTracePointExt() {
  const app = getApp();
  // 增加埋点数据
  const logGlobalInfo = app.logger.getGlobal() || {};

  const userInfo = logGlobalInfo.user || {};
  const contextInfo = logGlobalInfo.context || {};
  const plat = logGlobalInfo.plat || {};

  return JSON.stringify({
    ...contextInfo,
    ...plat,
    platform: 'weapp',
    uuid: userInfo.uuid,
    userId: userInfo.li || '',
  });
}

// 拼装配置信息
export const getConfigParam = (state, payload = {}) => {
  const app = getApp();
  const { order, disableStoredDiscount, guarantee } = state;
  const { keepSelect } = payload;
  const config = {
    ...order.config,
    buyerMsg: order.buyerMsg,
    openDisplayCard: order.openDisplayCard,
    disableStoredDiscount,
    storedDiscountRechargeGuide: true,
    yzGuaranteeInfo: guarantee.yzGuaranteeInfo, // 担保信息用于订单快照
  };

  const buyerGps = app.storage.get('gps_location');
  if (buyerGps) {
    config.buyer_gps = buyerGps;
  }

  // 发票
  if (state.invoice.submit && Object.keys(state.invoice.submit).length) {
    config.invoice = state.invoice.submit;
  }

  // 身份证
  const idCard = {};
  if (state.tradeTag.hasOverseaGoods) {
    idCard.binding = state.ignoreIdBinding ? undefined : !!state.idcard.binding;
    idCard.idCardName = state.idcard.name;
    idCard.idCardNumber = state.idcard.number;

    if (state.order.needIdCardPhoto) {
      idCard.idCardBackPhoto = state.idcard.backPhoto;
      idCard.idCardFrontPhoto = state.idcard.frontPhoto;
    }

    config.idCard = idCard;
  }

  if (keepSelect) {
    config.valueCardsExtContext = JSON.stringify({
      IS_RELOAD: true,
    });
  }

  return config;
};

const getSourceParam = (state) => {
  // 在 items 中加入统计参数
  const { source } = state.order;

  if (Array.isArray(source.itemSources)) {
    source.itemSources.forEach((item) => {
      item.bizTracePointExt = item.bizTracePointExt || getBizTracePointExt();
      // 存在微信视频号分享员openId归因为分享员订单
      if (state.wxvideoSharerOpenId) {
        try {
          const bizTracePointExt = JSON.parse(item.bizTracePointExt || '{}');
          bizTracePointExt.wxvideo_sharer_open_id = state.wxvideoSharerOpenId;
          item.bizTracePointExt = JSON.stringify(bizTracePointExt);
        } catch (e) {}
      }
      // 需要调用交易组件3.0微信支付归因为交易组件3.0订单
      if (state.useWxvideoPayFlow) {
        try {
          const bizTracePointExt = JSON.parse(item.bizTracePointExt || '{}');
          bizTracePointExt.trade_module_order_version = '3.0';
          item.bizTracePointExt = JSON.stringify(bizTracePointExt);
        } catch (e) {}
      }
    });
  }

  return source;
};

// 获取Orderitems
export const getConfirmOrderItems = (data = {}) => {
  const { tradeConfirmation = {} } = data;
  const { orderItems = [] } = tradeConfirmation;
  return orderItems;
};

export const getUsePointDeduction = (state) => {
  if (state.retailJointId) return false; // 零售拼单不能使用
  return state.pointDeduction.isUse;
};

// 所有商品的总税费
export const getTotalTax = (state) => {
  try {
    const RISK_GOODS_TAX_INFOS = state.goods.list.reduce((pre, current) => {
      if (current.showTax) {
        const { goodsTax, num, postageTax } = current;
        const taxPrice = goodsTax * num + postageTax;
        return pre + taxPrice;
      }
      return pre;
    }, 0);
    return RISK_GOODS_TAX_INFOS || 0;
  } catch (error) {
    return 0;
  }
};

// 添加风控透传字段
function addRiskExtension(extensions, state) {
  const RISK_GOODS_TAX_INFOS = getTotalTax(state) + '';
  let biz;
  try {
    biz = JSON.parse(extensions.BIZ_ORDER_ATTRIBUTE || '{}');
  } catch (e) {
    console.error(e);
    return extensions;
  }
  biz = {
    ...biz,
    RISK_GOODS_TAX_INFOS,
  };
  extensions.BIZ_ORDER_ATTRIBUTE = JSON.stringify(biz);
}

function addSpecialPeriodCostExt(extensions, state, getters) {
  if (
    checkRetailShop(getApp().getShopInfoSync()) &&
    getters.expressType === 'express'
  ) {
    const { ATTR_DISPATCHER_WAREHOUSE_IDS: ids } = state.extra || {};
    return {
      ...extensions,
      ATTR_DISPATCHER_WAREHOUSE_IDS: ids,
      ATTR_SUPPORT_TIMESPAN_DELIVERY_FEE: '1',
    };
  }
  const onlyForExpress = [
    'ATTR_DISPATCHER_WAREHOUSE_IDS',
    'ATTR_SUPPORT_TIMESPAN_DELIVERY_FEE',
  ];
  return Object.entries(extensions).reduce((ext, [key, value]) => {
    if (!onlyForExpress.includes(key)) {
      ext[key] = value;
    }
    return ext;
  }, {});
}

// 获取extensions
export const getExtensionsParam = (state, getters) => {
  const extensions = state.order.extensions || {};
  const app = getApp();
  const logGlobalInfo = app.logger.getGlobal() || {};
  const contextInfo = logGlobalInfo.context || {};
  // 风控共建，添加透传字段
  addRiskExtension(extensions, state);
  // 2021.06.23 @子一 @伴月 @洲泛 @赵杰 为了快速定位线上问题，在订单系统增加了APP_VERSION字段，包含微信版本和小程序版本
  const envInfo = logGlobalInfo.env || {};
  const WEAPP_VERSION = contextInfo.weapp_version;
  const APP_VERSION = envInfo.version + '-' + WEAPP_VERSION;

  let bizOrderAttribute;

  try {
    bizOrderAttribute = JSON.parse(extensions.BIZ_ORDER_ATTRIBUTE || '{}');
  } catch (e) {
    console.error(e);
    return extensions;
  }
  bizOrderAttribute = {
    ...bizOrderAttribute,
    APP_VERSION,
  };

  if (state.retailJointId) {
    // 零售的场景会覆盖掉所有的BIZ_ORDER_ATTRIBUTE, 中台化做修复，这里做临时解
    bizOrderAttribute = {
      ...bizOrderAttribute,
      JOINT_ORDER: '1',
      JOINT_ID: '' + state.retailJointId,
    };
  }
  // 交易组件3.0开通流程 直接塞上3.0订单标 保证后端对于特殊物流、商品、活动的屏蔽逻辑可以生效
  if (
    state.useWxvideoPayFlow &&
    bizOrderAttribute?.OPEN_TRADE_MODULE_VERSION === '3.0'
  ) {
    bizOrderAttribute = {
      ...bizOrderAttribute,
      WX_CHANNELS_COMPONENT_CREATE: 'TRADE_COMPONENT_3_0',
    };
  }
  extensions.BIZ_ORDER_ATTRIBUTE = JSON.stringify(bizOrderAttribute);
  extensions.IS_OPTIMAL_SOLUTION = 'true';
  return addSpecialPeriodCostExt(extensions, state, getters);
};

// 获取先用后付参数
export const getPriorUseOrderParams = (state) => {
  const { useBeforePayData = {} } = state;
  if (!useBeforePayData.show || !useBeforePayData.enable) return {};
  return {
    IS_SELECTED_PRIOR_USE_PAY_WAY: useBeforePayData.confirm,
  };
};

// 创建订单接口入参
export const getCreateOrderParam = (state, getters) => {
  // 不可用商品信息
  const unavailableItems = state.goods.unavailable.map((item) => ({
    skuId: item.skuId,
    propertyIds: item.propertyIds || [],
    goodsId: item.goodsId,
    kdtId: state.shop.kdtId,
    activityType: item.activityType,
    activityId: item.activityId,
  }));

  return {
    version: state.version,
    source: getSourceParam(state),
    config: getConfigParam(state),
    usePayAsset: getUsePayAssetParam(state),
    items: getOrderItemsParam(state, getters),
    seller: state.order.seller,
    ump: getUmpParam(state, getters),
    newCouponProcess: state.order.newCouponProcess,
    unavailableItems,
    asyncOrder: state.order.asyncOrder,
    delivery: getDeliveryParam(state, getters, {
      isNeedAppointmentType: true,
    }),
    cloudOrderExt: state.cloudOrderExt,
    bookKeyCloudExtension: state.bookKeyCloudExtension,
    confirmTotalPrice: money(Number(getters.orderFinalPrice)).toCent(),
    extensions: {
      ...state.order.extensions,
      ...getPriorUseOrderParams(state),
      ...getExtensionsParam(state, getters),
    },
    behaviorOrderInfo: state.behaviorOrderInfo,
  };
};
