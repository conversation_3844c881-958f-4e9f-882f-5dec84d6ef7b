/**
 * 工具类
 */

// 是否为对象
function isObj(x) {
  const type = typeof x;
  return x !== null && (type === 'object' || type === 'function');
}

// 是否为空对象
function isEmptyObject(obj) {
  return isObj(obj) && !Object.keys(obj).length;
}

// 是否未定义
function isDef(val) {
  return val !== null && val !== undefined;
}

// Safe get
function get(object, path, defaultValue = {}) {
  if (!object) {
    return defaultValue;
  }

  const keys = path.split('.');
  let result = object;

  keys.forEach(key => {
    result = isDef(result[key]) ? result[key] : {};
  });

  return isEmptyObject(result) ? defaultValue : result;
}

export {
  get,
  isDef,
  isObj,
  isEmptyObject
};
