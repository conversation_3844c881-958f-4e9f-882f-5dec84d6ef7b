const app = getApp();

//
let giftRetry = 1;

function fetchGiftId(orderNo) {
  return app
    .request({
      path: 'wscump/gift/giftid.json',
      data: { orderNo }
    })
    .then((resp) => {
      if (resp && resp.alias) {
        return resp.alias;
      }
      // 如果没有礼单500ms后重试
      throw new Error('retry');
    });
}

function navigateGiftShareDetail(orderNo) {
  fetchGiftId(orderNo)
    .then((giftId) => {
      wx.redirectTo({
        url: `/packages/gift/share/index?presenter_view=1&gift_id=${giftId}`
      });
    })
    .catch((err) => {
      if (err.code && giftRetry < 3) {
        giftRetry += 1;
        setTimeout(() => {
          navigateGiftShareDetail(orderNo);
        }, 1000);
      } else {
        wx.showToast({
          icon: 'none',
          title: err.msg || '获取礼盒id 失败'
        });
      }
    });
}

export {
  navigateGiftShareDetail,
  fetchGiftId,
  giftRetry
};
