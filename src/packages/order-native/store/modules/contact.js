import { reportSkynet } from 'utils/log/logv3';

const app = getApp();

export const state = {
  // 联系人
  contact: {
    id: '',
    list: [],
    editingIndex: -1,
    required: false,
    userName: '',
    telephone: '',
    editingId: null,
  },
  textRule: 'normal',
};

// 文案显示规则
const TEXT_RULE = {
  HIDDEN_PHONE: 'hidden_phone',
  NORMAL: 'normal',
};

export const getters = {
  // 当前使用的联系人
  currentContact(state) {
    const { contacts } = state.user.delivery;
    if (contacts) {
      return {
        id: contacts.id,
        userName: contacts.recipients,
        telephone: contacts.tel,
      };
    }

    return (
      state.contact.list.filter((item) => item.id === state.contact.id)[0] || {}
    );
  },
  // 正在编辑的联系人
  editingContact(state) {
    // return state.contact.list[state.contact.editingIndex] || {};
    const contact = state.contact.list.find(
      (item) => item.id === state.contact.editingId
    );
    return contact
      ? {
          ...contact,
          isDefault: Boolean(contact.isDefault) || false,
        }
      : {};
  },
  textInfo(state) {
    const textInfo = {
      contactLabel: '联系人',
      phoneLabel: '手机号码',
      phonePlaceholder: '请填写联系人手机号',
      userNamePlaceholder: '请填写联系人姓名',
      setDefaultTitle: '设为默认联系人',
    };

    if (state.textRule === TEXT_RULE.HIDDEN_PHONE) {
      return {
        ...textInfo,
        contactLabel: '提货人',
        phoneLabel: '提货码',
        phonePlaceholder: '请填写提货码，可输入手机号',
        userNamePlaceholder: '请填写提货人姓名',
        setDefaultTitle: '设为默认提货人',
      };
    }

    return textInfo;
  },
};

function SET_CONTACT_USER_NAME(state, payload) {
  state.contact.userName = payload;
}

function SET_CONTACT_TEL(state, payload) {
  state.contact.telephone = payload;
}

function SET_CONTACT_ID(state, id) {
  if (id) {
    // 选中联系人时，同步更新下单页联系人表单信息
    const current = state.contact.list.find((item) => item.id === id) || {};
    state.contact.userName = current.userName;
    state.contact.telephone = current.telephone;
  }
  state.contact.id = id;
}

function SET_TEXT_RULE(state, textRule) {
  state.textRule = textRule;
}

export const mutations = {
  SET_CONTACT_USER_NAME,

  SET_CONTACT_TEL,

  SET_CONTACT_EDITING_INDEX(state, index) {
    state.contact.editingIndex = index;
  },

  SET_CONTACT_EDITING_ID(state, id) {
    state.contact.editingId = id;
  },

  SET_CONTACT_LIST(state, list = []) {
    state.contact.list = list;
    state.contact.loaded = true;
  },

  SET_CONTACT_ID,

  SET_TEXT_RULE,

  /**
   * 设置联系人:ID 非空则取对应id有效值，否则取userName和telephone作为新增的元素
   */
  SET_CONTACT_OPTION(state, { id, userName, telephone }) {
    if (id) {
      SET_CONTACT_ID(state, id);
    } else if (userName && telephone) {
      SET_CONTACT_USER_NAME(state, userName);
      SET_CONTACT_TEL(state, telephone);
      SET_CONTACT_ID(state, '');
    }
  },
};

export const actions = {
  // 获取联系人列表
  FETCH_CONTACT_LIST({ commit }) {
    return app
      .request({
        origin: 'cashier',
        path: '/wsctrade/uic/contact/getContactList.json',
        method: 'POST',
      })
      .then((list) => {
        commit('SET_CONTACT_LIST', list);
        return list;
      });
  },
  // 保存联系人
  SAVE_CONTACT({ commit, state, dispatch, getters }, contact) {
    const method = contact.id ? 'update' : 'add';
    const { userName, telephone, id } = contact;
    const { textInfo } = getters;
    // 增加参数校验
    ECLOUD_MODE &&
      reportSkynet(`有赞云定制下单页新增${textInfo.contactLabel}`, {
        userName,
        telephone,
        id,
      });
    // 增加参数校验
    if (method === 'add' && !(userName && telephone)) {
      return Promise.reject('参数缺失');
    }
    if (method === 'update' && !id) {
      return Promise.reject('参数缺失');
    }

    dispatch('LOADING');

    return app
      .request({
        origin: 'cashier',
        method: 'POST',
        path: `/wsctrade/uic/contact/${method}Contact.json`,
        data: contact,
      })
      .then((response) => {
        const id = contact.id || response.value;
        const { list } = state.contact;
        const newList = list.slice();
        const newAddress = { ...contact, id };
        const editingIndex = list.map((item) => item.id).indexOf(contact.id);

        if (method === 'add') {
          newList.push(newAddress);
        } else {
          newList.splice(editingIndex, 1, newAddress);
        }

        // 当前项设为默认项时 需要将列表中其它项设为非默认
        if (contact.isDefault) {
          newList
            .filter((item) => item.id !== id && item.isDefault)
            .forEach((item) => (item.isDefault = false));
        }

        commit('SET_CONTACT_ID', id);
        commit('SET_CONTACT_USER_NAME', contact.userName);
        commit('SET_CONTACT_TEL', contact.telephone);
        commit('SET_CONTACT_LIST', newList);
        dispatch(
          'TOAST',
          method === 'add'
            ? `${textInfo.contactLabel}添加成功`
            : `${textInfo.contactLabel}编辑成功`
        );

        return response;
      })
      .catch((err) => {
        dispatch('TOAST', err.msg || `${textInfo.contactLabel}保存失败`);
        throw err;
      });
  },

  // 把登录用户信息添加为联系人
  CONVERT_LOGIN_INFO_AS_CONTACT({ dispatch }) {
    const { nickname: userName, mobile: telephone } = app.getUserInfoSync();
    if (userName && telephone) {
      return dispatch('SAVE_CONTACT', {
        userName,
        telephone,
      });
    }
  },

  // 删除联系人
  DELETE_CONTACT({ state, dispatch, commit, getters }, id) {
    const { textInfo } = getters;
    dispatch('LOADING');

    return app
      .request({
        origin: 'cashier',
        method: 'POST',
        path: '/wsctrade/uic/contact/deleteContact.json',
        data: { contactId: id },
      })
      .then(() => {
        const { contact } = state;
        const list = contact.list.slice();
        const editingIndex = list.map((item) => item.id).indexOf(id);
        list.splice(editingIndex, 1);
        commit('SET_CONTACT_LIST', list);

        // 与当前选中联系人相同则取消选择 并 清除下单页联系人信息
        if (id === contact.id) {
          commit('SET_CONTACT_ID', '');
          commit('SET_CONTACT_USER_NAME', '');
          commit('SET_CONTACT_TEL', '');
        }

        dispatch('TOAST', `${textInfo.contactLabel}删除成功`);
      })
      .catch((err) => {
        dispatch('TOAST', err.msg || `${textInfo.contactLabel}删除失败`);
        throw err;
      });
  },
};
