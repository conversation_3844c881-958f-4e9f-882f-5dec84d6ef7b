export const state = {
  // 订单扩展点
  cloudOrderExt: {
    extension: {},
  },

  bookKeyCloudExtension: {
    umpExt: '',
  },

  // 外部扩展数据 - 商品信息
  // [{ goodsId: 0, tag: '', payPrice: 0, originPrice: 0 }]
  componentExtGoods: {
    display: true,
    properties: [],
  },

  componentExtUmp: {
    display: true,
    properties: {},
  },

  componentExtAddress: {
    display: true,
    properties: {},
  },

  componentExtCountdown: {
    display: true,
    properties: {},
  },

  componentExtCoupon: {
    display: true,
    properties: {},
  },

  componentExtValueCard: {
    display: true,
    properties: {},
  },
};

export const mutations = {
  // 设置有赞云扩展字段
  SET_CLOUD_ORDER_EXT(state, payload) {
    state.cloudOrderExt = payload;
  },

  SET_BOOKKEY_CLOUD_EXTENSION(state, payload) {
    state.bookKeyCloudExtension = payload;
  },

  SET_CLOUD_COMP_GOODS(state, { display = true, properties }) {
    state.componentExtGoods.display = display;
    if (properties) state.componentExtGoods.properties = properties;
  },

  SET_CLOUD_COMP_UMP(state, { display = true, properties }) {
    state.componentExtUmp.display = display;
    if (properties) state.componentExtUmp.properties = properties;
  },

  SET_CLOUD_COMP_ADDRESS(state, { display = true, properties }) {
    state.componentExtAddress.display = display;
    if (properties) state.componentExtAddress.properties = properties;
  },

  SET_CLOUD_COMP_COUNTDOWN(state, { display = true, properties }) {
    state.componentExtCountdown.display = display;
    if (properties) state.componentExtCountdown.properties = properties;
  },

  SET_COUPON_COMP_COUPON(state, { display = true, properties }) {
    state.componentExtCoupon.display = display;
    if (properties) state.componentExtCoupon.properties = properties;
  },
  SET_COUPON_COMP_VALUE_CARD(state, { display = true, properties }) {
    state.componentExtValueCard.display = display;
    if (properties) state.componentExtValueCard.properties = properties;
  },
};
