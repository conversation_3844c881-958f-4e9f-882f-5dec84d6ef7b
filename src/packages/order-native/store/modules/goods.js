import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { moment as formatDate } from '@youzan/weapp-utils/lib/time';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import config from '../../common/config';
import get from '@youzan/weapp-utils/lib/get';

const app = getApp();

// 预售文案提示
function getPresaleTips(item, pay) {
  const startTime = item.presaleStartTime;
  const endTime = item.presaleEndTime;

  if (!item.presale) {
    return '';
  }

  if (item.presaleTimeType === 0) {
    const now = new Date();
    const presaleDate = new Date(startTime);
    const dateFormat =
      (now.getFullYear() === presaleDate.getFullYear() ? '' : 'YYYY年') + 'MM月DD日';
    return `${formatDate(presaleDate, dateFormat)} 开始发货`;
  }
  if (item.presaleTimeType === 1) {
    const text = pay.multiPhase ? '尾款支付' : '付款';
    return `${text} ${item.presaleStartTimeAfterPay} 天后发货`;
  }
  const startTimeStr = formatDate(startTime, 'MM月DD日');
  const endTimeStr = formatDate(endTime, 'MM月DD日');

  if (startTime && endTime) {
    return (
      '预计 ' + (startTimeStr === endTimeStr ? startTimeStr : startTimeStr + ' ~ ' + endTimeStr)
    );
  }
  if (startTime) {
    return startTimeStr + '开始发货';
  }
  if (endTime) {
    return '最晚' + endTimeStr + '发货';
  }

  return '';
}

// 计算商品价格
function getTotalPrice(prefix, price, points, pointsName) {
  const arr = [];
  if (points) {
    arr.push(points + pointsName);
  }
  if (price || !points) {
    arr.push(prefix + (price / 100).toFixed(2));
  }
  return arr.join(' + ');
}

// 获取商品属性
export function getGoodsPropertiesStr(properties = []) {
  const propertiesArr = [];

  properties.forEach((currentProperty) => {
    const propValueList = get(currentProperty, 'propValueList', []);
    propValueList.forEach((currentValue) => {
      propertiesArr.push(currentValue.propValueName);
    });
  });

  return propertiesArr.join('，');
}

// 处理商品sku信息
export function getGoodDesc(item = {}) {
  // 会员卡续费，显示时间范围
  const startTime = item.memberCardStartTime;
  const endTime = item.memberCardEndTime;
  if (startTime && endTime) {
    return startTime + '-' + endTime;
  }

  let { sku } = item;
  // eslint-disable-next-line
  sku = Array.isArray(sku) ? sku : sku && typeof sku === 'string' ? JSON.parse(sku) : [];

  const skuStr = sku
    .filter((item) => item.v)
    .map((item) => item.v)
    .join(', ');

  const goodsPropertiesStr = getGoodsPropertiesStr(item.properties);

  return [skuStr, goodsPropertiesStr].filter((item) => !!item).join(', ');
}

function findExtGoods(extGoodsList, goodsId) {
  if (extGoodsList && extGoodsList.length > 0) {
    return (
      extGoodsList.find((goods) => {
        return goods.goodsId === goodsId;
      }) || {}
    );
  }
  return {};
}

export const state = {
  // 商品
  goods: {
    messageList: {},
    list: [],
    unavailable: [],
    prepareTime: 0,
    depositPreSale: false
  }
};

export const getters = {
  // 商品数量
  goodsCount(state) {
    return state.goods.list.reduce((prev, item) => prev + item.num, 0);
  },

  // 是否有海淘商品
  hasHaitaoGoods(state) {
    return state.goods.list.some((item) => +item.haitao === 1);
  },

  // 商品列表
  formattedGoods(state, getters) {
    const { goods, memberCard, shop, pointsName, componentExtGoods } = state;
    return goods.list
      .filter((item) => !item.fromTmpAdded)
      .map((item) => {
        // 预售商品
        if (item.presale && getters.expressType === 'express') {
          item.showDeliveryTime = true;
          item.deliveryTime = getPresaleTips(item, state.pay);
        }

        // 会员卡续费商品
        if (memberCard.renewal && memberCard.memberCardStartTime && memberCard.memberCardEndTime) {
          item.memberCardEndTime = formatDate(memberCard.memberCardEndTime, 'YYYY.MM.DD');
          item.memberCardStartTime = formatDate(memberCard.memberCardStartTime, 'YYYY.MM.DD');
        }

        const extGoodsList = componentExtGoods.properties;
        const extGoods = findExtGoods(extGoodsList, item.goodsId);

        if (item.present) {
          item.acvitityTag = '赠品';
        } else if (+item.haitao === 1) {
          item.acvitityTag = '海淘';
        } else if (item.isUseGoodsExchangeCoupon) {
          item.acvitityTag = '兑换券';
        } else if ('activityType' in item) {
          item.acvitityTag = config.activityMap[item.activityType];
        }

        // 电子卡券
        item.isECard = item.virtualType === 3;
        if (item.isECard) {
          const { quotaNum, stockNum, buyedNum = 0 } = item;

          // 没有库存时视为异常状态，不可修改商品数量
          // 商品中有兑换券商品时，所有电子卡券商品都不能修改数量
          item.canChangeNum =
            stockNum != null &&
            stockNum !== '' &&
            goods.list.every((item) => !item.isUseGoodsExchangeCoupon);

          if (item.canChangeNum) {
            item.maxNum = quotaNum > 0 ? Math.min(quotaNum - buyedNum, stockNum) : stockNum;
          } else {
            // 不可修改数量时给 maxNum 设置一个数字，避免 van-stepper 内部报错
            item.maxNum = 2147483647;
          }
        }

        item.tags = [];
        item.presale && item.tags.push('预售');
        item.isFission && item.tags.push('内购价');
        item.quickRefund && item.tags.push('自动退款');
        getters.isPeriodBuy && item.tags.push('周期购');
        shop.activityType === 20 && item.tags.push('F码专享');
        shop.activityType === 21 && item.tags.push('砍价');
        extGoods.tag && item.tags.push(extGoods.tag);

        item.url = cdnImage(item.imgUrl, '!180x180.jpg');
        // 实付价
        item.payPriceStr = getTotalPrice(
          '',
          extGoods.payPrice || item.payPrice,
          item.pointsPrice,
          pointsName
        );

        item.desc = getGoodDesc(item, state.memberCard);

        // 显示原价的逻辑
        if (extGoods.originPrice && extGoods.payPrice && extGoods.originPrice > extGoods.payPrice) {
          item.PriceStr = getTotalPrice('', extGoods.originPrice, null, pointsName);
        } else if (item.price > item.payPrice && item.payPrice) {
          item.PriceStr = getTotalPrice('', item.price, null, pointsName);
        }

        // item.message 为空对象时，将 item.message 置为null
        if (JSON.stringify(item.message) === '{}') {
          item.message = null;
        }

        return item;
      });
  },

  // 不可购买商品列表
  formattedUnavailableGoods(state) {
    const { pointsName } = state;
    return state.goods.unavailable.map((item) => ({
      ...item,
      url: cdnImage(item.imgUrl, '!180x180.jpg'),
      payPriceStr: getTotalPrice('', item.payPrice, item.pointsPrice, pointsName),
      desc: getGoodDesc(item)
    }));
  }
};

export const actions = {
  FETCH_SKU_DATA({ dispatch, state }, alias) {
    return app
      .request({
        origin: 'cashier',
        method: 'GET',
        path: '/wsctrade/fetch-sku.json',
        data: {
          alias,
          offlineId: get(state, 'order.seller.storeId', '')
        }
      })
      .then((sku) => mapKeysCase.toCamelCase(sku))
      .catch((error) => {
        dispatch('TOAST', error.msg || error.message || '获取商品信息失败，请稍后再试');
        throw error;
      });
  }
};
