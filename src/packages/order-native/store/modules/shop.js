import common from '@/packages/shop/multi-store/common/index';
import get from '@youzan/weapp-utils/lib/get';
import { getDeliveryParam } from '../params';

const app = getApp();

const { saveStoreAndJumpBack } = common;

export const getters = {
  // 是否为多网点
  isMultiStore(state) {
    return get(state, 'order.seller.storeId', 0) > 0;
  }
};

export const mutations = {
  SET_SHOP_CLOSED(state, desc) {
    state.shop.closed = true;
    state.shop.closedDesc = desc;
  },

  SET_STORE_ID(state, storeId) {
    state.order.seller.storeId = storeId;
  }
};

export const actions = {
  // 获取附近可送门店
  MATCH_OFFLINE({
    dispatch, commit, state, getters
  }) {
    dispatch('LOADING');

    const params = {
      ...state.order,
      delivery: getDeliveryParam(state, getters)
    };

    return app.request({
      origin: 'cashier',
      path: 'pay/wsctrade/order/buy/matchOffline.json',
      data: params,
      method: 'POST'
    }).then((res) => {
      const storeId = typeof res === 'object' ? res.value : res;
      dispatch('HIDE_TOAST');
      if (storeId) {
        commit('SET_STORE_ID', storeId);
        saveStoreAndJumpBack.call(this, { id: storeId }, 0);
      }
      return storeId;
    }).catch((err) => {
      dispatch('HIDE_TOAST');
      dispatch('TOAST', err.msg || '服务器开小差了');
    });
  }
};
