import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import args from '@youzan/weapp-utils/lib/args';
import money from '@youzan/weapp-utils/lib/money';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import { getPrefetchData } from '@youzan/tee-biz-prefetch';
import openWebView from 'shared/utils/open-web-view';
import { assignOrderData, assignPayment } from '../state';
import { checkRetailShop } from '@youzan/utils-shop';
import {
  getDeliveryParam,
  getCreateOrderParam,
  getOrderItemsParam,
  getUmpParam,
  getUsePointDeduction,
  getUsePayAssetParam,
  getExtensionsParam,
  getConfigParam,
  getConfirmOrderItems,
  getBizTracePointExt,
} from '../params';
import { isEmptyObject, get } from '../utils';
import {
  navigateGiftShareDetail,
  fetchGiftId,
  giftRetry,
} from '../paypath-helper';
import { getPlatform } from '../../utils/format';
import config from '../../common/config';
import asyncEvent from 'shared/utils/async-event';

const app = getApp();

const MAX_DEDUCTION_TYPE = {
  AMOUNT: 1,
  RATE: 2,
};

export const state = {
  // 订单
  order: {
    steps: [],
    agreeDeposit: false,
    newCouponProcess: false,
    bookKey: '',
    ump: {},
    items: [],
    seller: {},
    source: {},
    config: {},
    delivery: {},
    buyerMsg: '',
    countdownInterval: '',
    needIdCardPhoto: false,
    orderNos: [],
    // 是否显示排队浮层并阻止下单
    limitCreate: false,
    // 排队状态 wating | fail
    limitCreateStatus: '',
    // 下单重试次数
    limitTryCount: 0,
    // 定金协议是否隐藏
    showDepositAgreementVal: true,
    // 是否隐藏优惠券组件
    couponDisplay: true,
    // 是否隐藏买家留言
    showMessage: true,
  },
  buyerStayData: {},
  outBizNo: '',
  showRetailOrderErrorInfo: {},
};

export const getters = {
  useConditions(state) {
    const items = [];
    const {
      minAmount,
      rate,
      maxDeductionAmount,
      maxDeductionType,
      maxDeductionRate,
    } = state.pointDeduction;
    let itemIndex = 1;
    items.push(`${itemIndex++}、订单金额大于${minAmount / 100}元`);
    items.push(`${itemIndex++}、${state.pointsName}数量大于${rate}`);
    if (!maxDeductionType || maxDeductionType === MAX_DEDUCTION_TYPE.AMOUNT) {
      maxDeductionAmount > 0 &&
        items.push(
          `${itemIndex++}、每笔订单最多抵${maxDeductionAmount / 100}元`
        );
    }
    items.push(`${itemIndex++}、部分商品不可用`);
    items.push(`${itemIndex++}、${state.pointsName}不可以抵运费`);
    if (maxDeductionType === MAX_DEDUCTION_TYPE.RATE) {
      items.push(`${itemIndex++}、最高可抵订单金额的${maxDeductionRate}%`);
    }
    return items;
  },

  // 订单是否已创建
  orderCreated(state) {
    const { orderNos } = state.order;
    return !!(Array.isArray(orderNos) && orderNos.length);
  },

  // 针对下单挽留项目新增的字段
  // 在展示 商家配送/自提 配送方式胶囊情况下 且
  // 不展示 拼团 海淘提示情况下
  // 新版下单页需要在导航栏增加背景色
  navbarShowBackGround(state) {
    const { tradeTag, display, env, groupon, selfFetch, version, goods } =
      state;
    // 是否有电子卡券商品
    const hasEcard = goods.list.some(({ virtualType }) => virtualType === 3);
    // 是否显示顶部提示
    const showHeadPrompt =
      tradeTag.hasOverseaGoods && display.headPrompt && !env.isPayPage;
    // 是否显示流程提示
    const showGuide = groupon.isGroupon || tradeTag.hasHotelGoods;
    return (
      !showHeadPrompt &&
      !showGuide &&
      display.showAddressTab &&
      display.showExpressTab &&
      selfFetch.isAllow &&
      !hasEcard &&
      version === 2
    );
  },

  // 订单数据
  billData(state) {
    const { order } = state;
    const source = { ...order.source };
    delete source.itemSources;
    return {
      ...source,
      ...order.buyer,
      ...order.config,
      ...order.seller,
      ...(order.ump.activities || [])[0],
    };
  },

  // 是否有可买商品
  hasGoods(state) {
    return !!state.goods.list.length;
  },

  // 是否有展示卡
  hasDisplayCard(state, getters) {
    // 存在展示卡 并且 （订单未创建 或者 订单已创建且选中展示卡）
    return (
      !isEmptyObject(state.displayCard) &&
      (!getters.orderCreated || state.display.openDisplayCard)
    );
  },

  /**
   * 是否不能使用预付卡
   * 不是海淘零元单 && 是分销订单 && 用了预付卡 && 没有足额支付
   * */
  isDisablePrepayCard(state, getters) {
    const { prepayCardDecrease, finalPrice, haitaoZeroOrder } = getters;

    const isFxOrder = state.goods.list.some(
      (item) => item.goodsType == 10 || item.fx
    );
    const isUsePrepayCard = prepayCardDecrease > 0;
    const isPayoff = finalPrice == 0;

    return !haitaoZeroOrder && isFxOrder && isUsePrepayCard && !isPayoff;
  },

  // 是否显示店铺风险提示
  isShowRiskWarnShopPrompt(state, getters) {
    return (
      !!state.riskWarnShopPrompt && !state.display.forbidPay && getters.hasGoods
    );
  },

  // 是否是教育拼团订单
  isEduOrder(state) {
    return state.goods.list[0].goodsType == 31;
  },

  orderKeepDispalyData(state) {
    const { buyerStayData } = state;
    const { attractiveHints } = buyerStayData;
    return {
      title: '确认放弃付款吗？',
      content: attractiveHints && attractiveHints[0],
    };
  },

  freightInsurance(state, getters) {
    const { hasFreightInsurance } = state.tradeTag;
    const { FREIGHT_INSURANCE_FREE } = state.extra;
    const { yzGuarantee } = state.display;
    const merchant = {
      title: '商家赠送',
      desc: '商家为你购买的商品提供退货包运费服务，退货成功后可补贴运费，实际赠送情况以订单详情页为准。',
    };
    const guarantee = {
      title: '有赞担保赠送',
      desc: '有赞担保订单赠送退货包运费，退货成功可补贴运费，实际赠送情况以订单详情页为准。',
    };
    const finalGuarantee = yzGuarantee && !getters.isChoosedCard;
    if (
      [
        config.freightInsuranceTag.SELF,
        config.freightInsuranceTag.FREE,
      ].indexOf(FREIGHT_INSURANCE_FREE) !== -1 &&
      finalGuarantee
    ) {
      return guarantee;
    }
    if (
      (FREIGHT_INSURANCE_FREE === config.freightInsuranceTag.NONE &&
        hasFreightInsurance) ||
      (FREIGHT_INSURANCE_FREE === config.freightInsuranceTag.SELF &&
        hasFreightInsurance &&
        !finalGuarantee)
    ) {
      return merchant;
    }
    return { title: '', desc: '' };
  },
};

export const mutations = {
  // 设置处置并支付参数
  SET_VALUE_CARD_PAY_PARAMS(state, payload) {
    state.pay.valueCardParams = payload;
  },

  // 设置是否是储值并支付流程
  SET_IS_STORE_AND_PAY(state, payload) {
    state.order.config.isStoreAndPayProcess = payload;
  },
  // 设置外部支付号
  SET_OUT_BIZ_NO(state, payload) {
    state.outBizNo = payload;
  },

  SET_POINT_DEDUCTION_USED(state, isUse) {
    state.pointDeduction.isUse = isUse;
  },

  SET_APP_SCHEME(state, appScheme) {
    state.order.source.appScheme = appScheme;
    state.order.source.needAppRedirect = true;
  },

  SET_SHOW_DATA(state, { data, options = {} }) {
    assignOrderData(state, data, options);
  },

  SET_ORDER_DATA(state, data) {
    state.order.orderNo = data.orderNo;
    state.order.orderNos = data.orderNos;
    state.order.requestNo = data.requestNo;
    state.order.mergePrepayOrderNo = data.mergePrepayOrderNo;

    assignPayment(state, data);
  },

  SET_PAY_DATA(state, data) {
    assignPayment(state, data);
  },

  SET_ORDER_MESSAGE(state, message) {
    state.order.buyerMsg = message;
  },

  // 设置是否禁用优惠券
  SET_ORDER_FORBID_COUPON(state, payload) {
    state.order.config.isForbidCoupon = payload;
  },

  // 设置是否禁用优惠活动
  SET_ORDER_FORBID_PREFERENCE(state, payload) {
    state.order.config.isForbidPreference = payload;
  },

  SET_AGREE_DEPOSIT(state, checked) {
    state.order.agreeDeposit = checked;
  },

  SET_HOTEL_PHONE(state, phone) {
    state.hotel.phone = phone;
  },

  SET_HOTEL_RECIPIENTS(state, payload) {
    state.hotel.recipients[payload.index] = payload.recipient;
  },

  SET_FORM_ID(state, payload) {
    state.order.source = {
      ...state.order.source,
      weAppFormId: payload,
    };
  },

  LIMIT_CREATE_ORDER(state) {
    state.order.limitCreate = true;
    state.order.limitCreateStatus = 'wating';
    state.order.limitTryCount += 1;
  },

  UNLIMIT_CREATE_ORDER(state) {
    // 重置创建订单调用次数
    state.order.limitTryCount = 0;
    state.order.limitCreate = false;
  },

  SET_LIMIT_CREATE_STATUS(state, payload) {
    state.order.limitCreateStatus = payload;
    state.order.limitTryCount = 0;
  },

  SET_DISPLAY_CARD(state, payload) {
    state.order.openDisplayCard = payload;
  },

  SET_GOOD_NUM(state, payload) {
    const { goodsId, skuId, num } = payload;
    for (const item of state.order.items) {
      if (item.goodsId === goodsId && item.skuId === skuId) {
        item.num = num;
        break;
      }
    }
  },

  SET_ASYNC_ORDER_BOOK_KEY(state, payload) {
    state.order.asyncOrderBookKey = payload;
  },

  SET_RECHARGE_PAY(state, payload) {
    Object.assign(state.pay.prepayParams, payload);
  },

  SET_BUYER_STAY(state, payload) {
    Object.assign(state.buyerStayData, payload);
  },

  RESET_BUYER_STAY(state) {
    state.buyerStayData = {};
  },

  SET_BUYER_STAY_SHOW(state, payload) {
    state.isSHowStayBuyerDialog = payload;
  },

  SET_EXTERNAL_LIVE(state, extensions) {
    let bizOrderAttribute;

    try {
      bizOrderAttribute = JSON.parse(extensions.BIZ_ORDER_ATTRIBUTE || '{}');
    } catch (e) {
      console.error(e);
      return;
    }

    state.order.extensions.BIZ_ORDER_ATTRIBUTE = JSON.stringify({
      ...bizOrderAttribute,
      WX_VIDEO_LIVE_ID: '' + extensions.id,
    });
  },

  SET_GUARANTEE_DATA(state, payload) {
    state.display.yzGuarantee = payload.isYzGuarantee; // 是否是有赞担保订单
    state.display.freightInsurance = payload.hasFreightInsurance === '1'; // 是否打标退货包运费
    state.display.freightInsuranceFree = payload.freightInsuranceFree; // 退货包运费赠送标
  },

  SET_BIZ_ORDER_ATTRIBUTE(state, { key, value }) {
    if (!state.bizOrderAttribute) {
      state.bizOrderAttribute = {};
    }
    state.bizOrderAttribute[key] = value;
  },

  SET_SHOULD_USEFIRST_DISPLAY(state, payload) {
    state.useBeforePayData.show = payload;
  },

  SET_SHOULD_USEFIRST_ENABLED(state, payload) {
    state.useBeforePayData.enable = payload;
  },

  SET_SHOULD_USEFIRST_CONFIRM(state, payload) {
    state.useBeforePayData.confirm = payload;
  },

  SET_DEPOSIT_AGREEMENT(state, show) {
    state.order.showDepositAgreementVal = show;
  },
  SET_YZ_GUARANTEE(state, payload) {
    state.guarantee.yzGuarantee = payload;
  },
  SET_YZ_GUARANTEE_INFO(state, payload) {
    state.guarantee.yzGuaranteeInfo = payload;
  },
  SET_YZ_GUARANTEE_DOCS(state, payload) {
    state.guarantee.yzGuaranteeDocs = payload;
  },
  // 行为组件信息
  SET_BEHAVIOR_INFO(state, payload) {
    state.behaviorOrderInfo = payload;
  },
  SET_MODIFY_MESSAGE(state, payload) {
    state.order.buyerMsg = payload;
  },
  SET_COUPON_DISPLAY(state, payload) {
    state.order.couponDisplay = payload;
  },
  SET_HIDDEN_MESSAGE(state, payload) {
    state.order.showMessage = payload;
  },
};

export const actions = {
  // 更新 show.json
  FETCH_SHOW({ dispatch }, payload = {}) {
    if (payload.fromIsv) {
      dispatch('FETCH_SHOW_ORIGIN', payload);
    } else {
      asyncEvent.triggerAsync
        .apply(getApp(), ['beforeConfirm'])
        .then(() => {
          dispatch('FETCH_SHOW_ORIGIN', payload);
        })
        .catch(() => {});
    }
  },
  FETCH_SHOW_ORIGIN({ dispatch, commit, state, getters }, payload = {}) {
    if (!payload.disableLoading) {
      dispatch('LOADING');
    }
    const { changeDeliveryTime, keepSelect, isAddressIdChange, num } = payload;

    const { order, goods } = state;
    const { orderNos } = order;
    const { goodsId, skuId } = goods?.list?.[0] || {};
    const orderCreated = !!(Array.isArray(orderNos) && orderNos.length);

    if (orderCreated) {
      return Promise.resolve({});
    }

    const params = {
      items: getOrderItemsParam(state, getters),
      source: order.source,
      seller: order.seller,
      newCouponProcess: order.newCouponProcess,
      ump: getUmpParam(
        state,
        getters,
        payload.isUsePointDeduction,
        payload.setCustomerCard
      ),
      config: getConfigParam(state, { keepSelect }),
      delivery: getDeliveryParam(state, getters, {
        expressTypeChoice: payload.expressTypeChoice,
        isFormDbId: payload.isFormDbId,
      }),
      usePayAsset: getUsePayAssetParam(state),
      first: !state.dataLoaded,
      cloudOrderExt: state.cloudOrderExt,
      bookKeyCloudExtension: state.bookKeyCloudExtension,
      extensions: getExtensionsParam(state, getters),
    };
    // 根据 goodsId 和 skuId 确定修改数量的商品
    if (
      typeof goodsId !== 'undefined' &&
      typeof skuId !== 'undefined' &&
      typeof num === 'number'
    ) {
      for (const item of params.items) {
        if (item.goodsId === goodsId && item.skuId === skuId) {
          item.num = num;
          break;
        }
      }
    }

    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/confirm.json',
        method: 'POST',
        data: mapKeysCase.toCamelCase(params),
      })
      .then((data) => {
        if (!payload.disableLoading) {
          dispatch('HIDE_TOAST');
        }

        const redirect = data.redirectConfig || {};

        if (redirect.orderCreated) {
          dispatch('TOAST', '订单信息已更新，页面刷新中');
          dispatch('FETCH_SHOW_PAY', {
            order_no: data.orderNos,
            orderMark: getPlatform().orderMark,
          });
        } else if (redirect.teamLocked) {
          const msg = '该店铺因存在异常，暂不支持购买，请联系商家';
          const err = new Error(msg);
          err.msg = msg;
          throw err;
        } else if (changeDeliveryTime && !getConfirmOrderItems(data).length) {
          commit('RESET_LOCAL_DELIVERY_TIME');
          dispatch('TOAST', '当前配送时间库存不足，请重新选择');
        } else {
          if (!state.dataLoaded && data.displayConfig.useExternalAsset) {
            dispatch('FETCH_EXTERNAL_ASSET');
          }

          if (
            typeof goodsId !== 'undefined' &&
            typeof skuId !== 'undefined' &&
            typeof num === 'number'
          ) {
            commit('SET_GOOD_NUM', { goodsId, skuId, num });
          }

          commit('SET_SHOW_DATA', {
            data,
            options: {
              isInitCoupon: get(payload, 'isInitCoupon', true),
              isFetchShow: true,
              isAddressIdChange,
            },
          });

          // 有加价购活动，需要请求加价购数据
          if (
            get(
              data,
              'displayConfig.plusBuyComponent.showPlusBuyComponent',
              false
            )
          ) {
            dispatch('FETCH_PLUS_BUY');
          }

          if (params.first) {
            app.trigger('order:loaded');
          }

          dispatch('HANDLE_MUTATE_STATE_AFTER_FETCH_IN_WXVIDEO');
        }
      })
      .catch((err) => {
        dispatch('HIDE_TOAST');

        if (!params.first) {
          dispatch('TOAST', err.msg);
        } else {
          const errorId = app.db.set({
            text: err.msg,
            code: err.code,
            stack: err.stack,
          });
          wx.redirectTo({
            url: '/packages/common/error/index?dbid=' + errorId,
          });
        }
      });
  },

  /**
   * 把下单接口参数中所有商品的fromParams字段改为指定字段
   * @returns { void }
   */
  SET_ALL_GOODS_GUIDE_PARAMS({ commit }, payload) {
    commit('SET_ALL_GOODS_GUIDE_PARAMS', payload);
  },

  /**
   * 1. 获取购物车中最早加购商品的导购openId
   * 2. 把该导购的fromParams暂存在state上, 供后续方法用于覆盖下单接口参数
   * @returns { Promise<Number> }
   */
  GET_EARLIEST_GUIDE_OPENID({ state, commit }) {
    const { order } = state;
    const { source = {} } = order;
    const { itemSources = [] } = source;
    const sortedItemSource = itemSources.concat();
    let fromParams = '';
    let sl = '';

    sortedItemSource.sort((a, b) => a.cartCreateTime - b.cartCreateTime);

    for (let i = 0; i < sortedItemSource.length; i++) {
      try {
        const bizTracePointExt = JSON.parse(
          sortedItemSource[i].bizTracePointExt || getBizTracePointExt()
        );

        fromParams = bizTracePointExt.from_params;
        const fromParamsObj = {};

        fromParams.split('!').forEach((item) => {
          const [key, value] = item.split('~');
          fromParamsObj[key] = value;
        });

        sl = bizTracePointExt.sl || fromParamsObj.sl;

        if (fromParams) break;
      } catch (e) {}
    }

    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/getEarliestGuideOpenId.json',
        method: 'GET',
        data: { sl },
      })
      .then((data) => {
        const { openId } = data;
        commit('FILL_PRESET_FROM_PARAMS', { openId, fromParams });

        return openId;
      });
  },

  FETCH_SHOW_BY_BOOK_KEY(
    { state, dispatch, commit },
    { bookKey, addressId = 0 }
  ) {
    dispatch('LOADING');
    const data = {
      bookKey,
      addressId,
      usePointDeduction: getUsePointDeduction(state),
      useVersion2: true, // 说明支持新版下单页
      useNewCoupon: true,
      isSupportDefaultSelfFetch: true,
      useOrderKeep: true,
      // 是否使用最优优惠计算，始终为true
      isOptimalSolution: true,
      isSupportSpecialPeriodCost: checkRetailShop(getApp().getShopInfoSync()),
    };
    return getPrefetchData({
      prefetchKey: state.pageQuery.prefetchKey || '',
      normalFetchCb: () => {
        return app.request({
          origin: 'cashier',
          path: '/pay/wsctrade/order/buy/prepare-by-book-key.json',
          data,
        });
      },
    })
      .then((data) => {
        commit('SET_PAGE_QUERY', { prefetchKey: '' });
        dispatch('HIDE_TOAST');

        const redirect = data.redirectConfig || {};

        if (redirect.orderCreated) {
          // 订单已创建
          dispatch('TOAST', '订单信息已更新，页面刷新中');
          dispatch('FETCH_SHOW_PAY', {
            order_no: data.orderNos,
            orderMark: getPlatform().orderMark,
          });
        } else if (redirect.teamLocked) {
          const msg = '该店铺因存在异常，暂不支持购买，请联系商家';
          const err = new Error(msg);
          err.msg = msg;
          throw err;
        } else {
          if (data.displayConfig.useExternalAsset) {
            dispatch('FETCH_EXTERNAL_ASSET');
          }

          commit('SET_SHOW_DATA', {
            data,
          });

          // 有加价购活动，需要请求加价购数据
          if (
            get(
              data,
              'displayConfig.plusBuyComponent.showPlusBuyComponent',
              false
            )
          ) {
            setTimeout(() => {
              dispatch('FETCH_PLUS_BUY');
            }, 0);
          }

          // 设置order参数
          const order = get(data, 'orderCreation');
          order.source = {
            ...order.source,
            ...getPlatform(),
            kdtSessionId: app.getSessionId(),
          };
          order.bookKey = bookKey;

          // 有赞云扩展点数据
          if (order.cloudOrderExt) {
            commit('SET_CLOUD_ORDER_EXT', order.cloudOrderExt);
          }

          // 云共建扩展信息，由bookKey开放扩展，推荐这种方式
          if (order.bookKeyCloudExtension) {
            commit('SET_BOOKKEY_CLOUD_EXTENSION', order.bookKeyCloudExtension);
          }

          // 零售取货方式
          commit(
            'SET_PICK_UP_WAY_DATA',
            get(data, 'tradeConfirmation.pickUpWay', {})
          );

          // FIXME: 处理自定义下单成功后跳转路径，临时修复，有串平台的问题，待改进
          const paymentSuccessRedirect = get(
            order,
            'config.paymentSuccessRedirect',
            ''
          );
          // http 开头的认为是来自 h5 的订单，不做处理
          if (
            paymentSuccessRedirect &&
            !paymentSuccessRedirect.startsWith('http')
          ) {
            commit('EXTEND_ENV', {
              successUrl: paymentSuccessRedirect,
            });
            // NOTE: 必须删除原来的 paymentSuccessRedirect，非 http 格式链接下单接口会报错
            delete order.config.paymentSuccessRedirect;
          }

          commit('EXTEND_ORDER_DATA', order);

          // 设置积分信息
          const points = get(data, 'pointsConfig');
          commit('EXTEND_POINTS', points);

          app.trigger('order:loaded');
        }
      })
      .catch((err) => {
        dispatch('HIDE_LOADING');
        const code = +err.code;

        if (code === 429 || code === 101302001 || code === 101302002) {
          const pages = getCurrentPages() || [];
          const containerPageRoute = get(pages[pages.length - 1], 'route');
          const containerPageOption = get(pages[pages.length - 1], 'options');

          const {
            pageQuery: { redirectCount },
          } = state;
          wx.redirectTo({
            url: args.add('/packages/common/limit-page/index', {
              redirectCount,
              callBackUrl: `/${containerPageRoute}`,
              options: JSON.stringify(containerPageOption),
            }),
          });
        }
        const errorId = app.db.set({
          text: err.msg,
          code: err.code,
          stack: err.stack,
        });
        wx.redirectTo({
          url: '/packages/common/error/index?dbid=' + errorId,
        });
      });
  },

  FETCH_SHOW_PAY({ state, dispatch, commit }, payload = {}) {
    dispatch('LOADING');

    const SCENE_CHECK = dispatch('SCENE_CHECK');
    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/prepare.json',
        method: 'POST',
        data: {
          ...payload,
          useOrderKeep: true,
        },
      })
      .then((data) => {
        dispatch('HIDE_TOAST');

        const redirect = data.redirectConfig || {};
        const orderNo = payload.order_no;

        if (redirect.timeout) {
          // 订单已超时 跳转到订单详情
          wx.redirectTo({
            url: `/packages/trade/order/result/index?orderNo=${orderNo}`,
          });
        } else if (redirect.orderPaid) {
          // 订单已支付 跳转到支付成功
          wx.redirectTo({
            url: `/packages/order/paid/index?order_no=${orderNo}`,
          });
        } else if (redirect.orderCanceled) {
          // 订单已取消 跳转到订单详情
          wx.redirectTo({
            url: `/packages/trade/order/result/index?orderNo=${orderNo}`,
          });
        } else if (redirect.peerpay) {
          const errorId = app.db.set({ text: '小程序不支持找人代付' });
          wx.redirectTo({
            url: '/packages/common/error/index?dbid=' + errorId,
          });
        }

        commit('EXTEND_ENV', { isPayPage: true });
        commit('SET_SHOW_DATA', { data });
        commit('SET_ORDER_DATA', { ...data, orderNo });

        // 有赞云扩展点
        const cloudOrderExt = get(data, 'tradeConfirmation.cloudOrderExt');
        if (cloudOrderExt) {
          commit('SET_CLOUD_ORDER_EXT', cloudOrderExt);
        }

        return SCENE_CHECK.then(() =>
          dispatch('CHECK_USE_WXVIDEO_PAY_FLOW')
        ).then(() => dispatch('GET_WX_ORDER_INFO'));
      })
      .catch(({ msg, code, stack } = {}) => {
        dispatch('HIDE_TOAST');
        const errorCode = +code;
        // 仅用于兜底极速下单待支付页跳转，异常码10000是node层在订单状态为极速下单待支付时才会抛出的
        if (errorCode === 10000) {
          wx.redirectTo({
            url: `/packages/order-native/fastbuy/index?orderNo=${payload.order_no}`,
          });
          return;
        }
        if (
          errorCode === 429 ||
          errorCode === 101302001 ||
          errorCode === 101302002
        ) {
          if (state.order.limitTryCount < 3) {
            // 5秒后如果用户没有手动退出排队，则继续重试
            return new Promise((resolve, reject) => {
              setTimeout(() => {
                if (state.order.limitCreate) {
                  dispatch('FETCH_SHOW_PAY').then(resolve).catch(reject);
                }
              }, 5000 * state.order.limitTryCount);
            });
          }
          commit('SET_LIMIT_CREATE_STATUS', 'fail');
          return;
        }
        if (state.dataLoaded) {
          dispatch('TOAST', msg);
        } else {
          const errorId = app.db.set({ text: msg, code, stack });
          wx.redirectTo({
            url: '/packages/common/error/index?dbid=' + errorId,
          });
        }
      });
  },

  // 异步下单创建BookKey 接口
  ASYNC_CREATE_ORDER_BOOK_KEY({ dispatch, state, commit, getters }) {
    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/create-async-book-key.json',
        method: 'POST',
        data: getCreateOrderParam(state, getters),
      })
      .then((response) => {
        const { asyncOrderBookKey } = response;
        commit('SET_ASYNC_ORDER_BOOK_KEY', asyncOrderBookKey);
        return dispatch('CREATE_ORDER', { disableLoading: true });
      })
      .catch((error = {}) => {
        const message = error.msg || error.message || '创建订单失败，请重试';
        // 交易组件3.0订单屏蔽报错，无需toast
        if (error.code !== 101305069 && error.code !== 101305070) {
          dispatch('TOAST', message);
        }
        throw error;
      });
  },

  // 创建订单
  CREATE_ORDER({ dispatch, commit, state, getters }, payload = {}) {
    const {
      order: { asyncOrder, asyncOrderBookKey, config = {} },
    } = state;

    if (!payload.disableLoading) {
      dispatch('LOADING');
    }

    const url = asyncOrder
      ? '/pay/wsctrade/order/buy/postAsyncOrderResult.json'
      : '/pay/wsctrade/order/buy/v2/bill.json';
    const postData = asyncOrder
      ? { asyncOrderBookKey }
      : getCreateOrderParam(state, getters);

    // 同步异步下单走不同的接口
    return app
      .request({
        origin: 'cashier',
        path: url,
        method: 'POST',
        data: postData,
      })
      .then((data) => {
        dispatch('HIDE_TOAST');
        commit('UNLIMIT_CREATE_ORDER');

        commit('SET_ORDER_DATA', data);

        app.trigger('trade:order:create');

        const GET_WX_ORDER_INFO = dispatch('GET_WX_ORDER_INFO');

        // 储值合并支付时，还需要走储值预下单才能获取到收银台参数
        return (
          state.display.newRecommend && config.isStoreAndPayProcess
            ? Promise.all([
                dispatch('RECHARGE_ORDER_PREPAY'),
                GET_WX_ORDER_INFO,
              ])
            : GET_WX_ORDER_INFO
        ).then(() => data);
      })
      .then((data) => {
        dispatch('LOG_CREATE_ORDER');
        return data;
      })
      .catch((error) => {
        const code = +error.code;
        if (code !== 102901001) {
          // 在异步下单情况下 不清除toast
          dispatch('HIDE_TOAST');
        }

        const message =
          error.msg || error.message || '订单生成失败，请刷新重试';

        switch (code) {
          // 门店休息，显示对应提示
          case 101350001:
          case 101350002:
          case 101350003:
            commit('SET_SHOP_CLOSED', message);
            break;
          // 全部或部分商品售罄
          case 101910001:
          case 101910002:
          case 101910003:
            dispatch('FETCH_SHOW', { disableLoading: true }).then(() => {
              dispatch('TOAST', message);
            });
            break;
          case 101304026:
            // 收货人姓名实名校验不过 需要提示重新修改收货人信息
            dispatch('NOTIFY_UPDATE_ADDRESS', message);
            break;
          // 下单过于频繁，进入排队交互
          case 101302001:
          case 101302002:
          case 429:
            commit('LIMIT_CREATE_ORDER');
            if (state.order.limitTryCount < 3) {
              // 5秒后如果用户没有手动退出排队，则继续重试
              return new Promise((resolve, reject) => {
                setTimeout(() => {
                  if (state.order.limitCreate) {
                    dispatch('CREATE_ORDER', { disableLoading: true })
                      .then(resolve)
                      .catch(reject);
                  }
                }, 1000 + 2000 * state.order.limitTryCount);
              });
            }
            commit('SET_LIMIT_CREATE_STATUS', 'fail');
            break;
          // 被风控
          case 101358005:
            Dialog.confirm({
              message,
              showCancelButton: true,
              cancelButtonText: '取消',
              confirmButtonText: '查看帮助',
            })
              .then(() => {
                openWebView(
                  'https://help.youzan.com/displaylist/detail_4_4-2-51920'
                );
              })
              .catch(() => {});
            break;
          case 102901001:
            // 异步下单
            commit('LIMIT_CREATE_ORDER');
            // 异步下单重试次数
            if (state.order.limitTryCount < 7) {
              return new Promise((resolve, reject) => {
                setTimeout(() => {
                  dispatch('CREATE_ORDER', { disableLoading: true })
                    .then(resolve)
                    .catch(reject);
                }, 1000);
              });
            }
            commit('SET_LIMIT_CREATE_STATUS', 'fail');
            break;
          // 交易组件3.0屏蔽
          case 101305069:
          case 101305070:
          case 101305071:
            commit('SET_CREATE_WXVIDEO_ORDER_INTERCEPT', {
              visible: true,
              ...(error || {}),
              code,
              msg: message,
            });
            // 异步下单 需要关闭 queue dialog
            commit('SET_LIMIT_CREATE_STATUS', 'fail');
            break;
          case 101305045:
            dispatch('TOAST', message);
            commit('SET_LIMIT_CREATE_STATUS', 'fail');
            break;
          default:
            dispatch('TOAST', message);
            break;
        }
        throw error;
      });
  },

  // 支付收单
  CREATE_PREPAY({ commit, dispatch, state }) {
    return app
      .request({
        origin: 'cashier',
        method: 'POST',
        path: '/pay/wsctrade/order/buy/prepay.json',
        data: {
          orderNoList: state.order.orderNos,
        },
      })
      .then((data) => {
        dispatch('HIDE_TOAST');
        const prepayParams = data.prePaymentPreparation || {};

        if (data.success === false || !prepayParams.prepaySuccess) {
          throw data;
        } else {
          commit('UNLIMIT_CREATE_ORDER');
          commit('SET_PAY_DATA', data);
          return data;
        }
      })
      .catch((error = {}) => {
        dispatch('HIDE_TOAST');
        commit('LIMIT_CREATE_ORDER');

        // trade-core中catch了错误，未抛出错误码，只要报错一律重试
        if (state.order.limitTryCount < 3) {
          // 5秒后如果用户没有手动退出排队，则继续重试
          return new Promise((resolve, reject) => {
            setTimeout(() => {
              if (state.order.limitCreate) {
                dispatch('CREATE_PREPAY').then(resolve).catch(reject);
              }
            }, 1000 + 2000 * state.order.limitTryCount);
          });
        }
        commit('SET_LIMIT_CREATE_STATUS', 'fail');

        // 标识一下预下单错误
        error.prepay = true;
        throw error;
      });
  },

  // 储值卡收单
  RECHARGE_ORDER_PREPAY({ commit, dispatch, state }) {
    return app
      .request({
        origin: 'cashier',
        method: 'POST',
        path: '/wsctrade/pay/recharge-order-prepay.json',
        data: {
          templateNo: '',
          clientSource: '',
          // 跳转链接
          partnerReturnUrl: get(state, 'pay.payParams.partnerReturnUrl', ''),
          // 外部业务单号，例如交易E单号，充值并支付使用，存储E单和R单关系
          outBizNo:
            get(state, 'order.mergePrepayOrderNo', '') ||
            get(state, 'order.orderNos', [])[0],
          // 业务透传上下文  美业充值需要透传"traderule"实现充增
          outBizContext: {},
          // 商品描述
          tradeDesc: '',
          // 卡号
          cardNo: '',
          // 支付金额
          payAmount: 0,
          // 充值规则模板ID
          valueCardRechargeRuleNo: '',
          // 商品名称
          goodsName: '',
          // 充值规则模板版本号
          valueCardRechargeRuleVersion: '',
          // 卡模板内部sku
          productNo: '',
          ...get(state, 'pay.valueCardParams', {}),
        },
      })
      .then((data) => {
        dispatch('HIDE_TOAST');

        if (data.success === false) {
          throw data;
        } else {
          commit('SET_RECHARGE_PAY', data);
          return data;
        }
      });
  },

  // 下单挽留
  ORDER_KEEP({ commit, state, getters }) {
    commit('RESET_BUYER_STAY');
    const { goods, pointDeduction, coupon, shop, pay: payState } = state;
    const { activities = [] } = shop;
    const { orderCreated } = getters;
    const orderNo =
      get(state, 'order.mergePrepayOrderNo', '') ||
      get(state, 'order.orderNos', [])[0];
    const stayType = orderCreated ? 1 : 0;
    const {
      realPay,
      decrease,
      itemPay,
      originPostage,
      postage,
      pay,
      originItemPrice,
      originDecrease,
    } = payState;
    const { list = [] } = coupon;

    // 数据过多 直接返回
    // 商品数量大于 15 就不展示
    // 订单级的优惠券数量大于 30 就不展示
    // 订单级的活动数量大于 20 就不展示
    if (list.length > 30 || activities.length > 20 || goods.list.length > 15) {
      commit('SET_BUYER_STAY_SHOW', false);
      return Promise.resolve(1);
    }

    const couponsList = list.map((item) => {
      const {
        choose,
        condition,
        discount,
        denominations,
        endAt,
        id,
        name,
        originCondition,
        startAt,
        unitDesc,
        valueDesc,
        value,
      } = item;
      return {
        choose,
        condition,
        discount,
        denominations,
        endAt,
        id,
        name,
        originCondition,
        startAt,
        unitDesc,
        valueDesc,
        value,
      };
    });

    const orderPayment = {
      realPay,
      decrease,
      itemPay,
      originPostage,
      postage,
      pay,
      originItemPrice,
      originDecrease,
    };

    const goodsList = goods.list.map((item) => {
      const { alias, goodsId, kdtId, num, skuId, activities = [] } = item;
      return {
        alias,
        goodsId,
        kdtId,
        num,
        skuId,
        activities,
      };
    });

    const activitiesList = activities.map((item) => {
      const { value, title, type, typeId, startAt, name, id, endAt } = item;
      return {
        value,
        title,
        type,
        typeId,
        startAt,
        name,
        id,
        endAt,
      };
    });
    const { isUse, costPoints } = pointDeduction;
    const groupPointDeduction = {
      ...pointDeduction,
      costPoints: isUse ? costPoints : 0,
    };

    return app
      .request({
        origin: 'cashier',
        method: 'POST',
        path: '/pay/wsctrade/order/buy/fetchOrderKeep.json',
        data: {
          pointDeduction: groupPointDeduction,
          orderNo,
          stayType,
          orderItems: goodsList,
          coupons: couponsList,
          activities: activitiesList,
          orderPayment,
        },
      })
      .then((data) => {
        commit('SET_BUYER_STAY', data);
      })
      .catch((error) => {
        const code = +error.code;
        // 后端判断不展示下单挽留弹框
        if (code === 102570009) {
          commit('SET_BUYER_STAY_SHOW', false);
          return Promise.resolve(1);
        }
        throw error;
      });
  },

  GO_TO_PAY_SUCCESS({ state, getters, dispatch }, payload = {}) {
    const { giftRolling = true } = payload;
    const {
      groupon,
      pay,
      tradeTag,
      order,
      env,
      giftCard,
      valueCard,
      shop,
      isHandselFirstPhase,
      goods,
      outBizNo,
    } = state;
    const { finalPrice, isEduOrder } = getters;
    // show 页面 或者showpay 页面不同，但是都做gift 处理
    const isGift =
      (order.config.gift && order.config.gift.giftOrder) || order.gift;
    if (isHandselFirstPhase) {
      return Promise.resolve(
        `/packages/ump/handsel-expand/index?order_no=${
          order.orderNo
        }&kdt_id=${app.getKdtId()}`
      );
    }

    if (isGift) {
      giftRetry = 1;
      return fetchGiftId(order.orderNo)
        .then((giftId) => {
          return `/packages/gift/share/index?presenter_view=1&gift_id=${giftId}`;
        })
        .catch((err) => {
          if (err.code && giftRetry < 3) {
            giftRetry += 1;
            setTimeout(() => {
              navigateGiftShareDetail(order.orderNo);
            }, 1000);
          } else {
            dispatch('TOAST', err.msg || '获取礼盒id 失败');
          }
        });
    }

    if (groupon.isGroupon) {
      const { goodsId } = goods.list[0] || {};
      return isEduOrder
        ? Promise.resolve(
            `/packages/paidcontent/groupon/index?orderNo=${order.orderNo}&goodId=${goodsId}`
          )
        : Promise.resolve(
            `/packages/collage/groupon/detail/index?from=pay&orderNo=${order.orderNo}&type=${groupon.activityType}`
          );
    }

    if (shop.activityType === 23) {
      // 抽奖拼团
      let activityId = 0;
      try {
        activityId = order.ump.activities[0].activityId;
      } catch (e) {
        console.log(e);
      }
      return Promise.resolve(
        `/packages/collage/lottery/detail/index?order_no=${order.orderNo}&activity_id=${activityId}`
      );
    }

    const data = {
      // 有支付号时传支付号 否则传订单号
      order_no: outBizNo || order.requestNo || order.orderNo,
      // 多笔订单合并时 支付成功页要跳转到订单列表
      isToOrderList: order.orderNos && order.orderNos.length > 1,
      pay_result: 'success',
      pay_money: money(finalPrice).toCent(), // NOTE: 以分为单位
    };

    // 传递支付阶段
    if (get(pay, 'phasePayment.currentPhase')) {
      data.phasePaymentStage = pay.phasePayment.currentPhase;
    }

    // 储值卡 / 礼品卡支付设置支付阶段
    if (get(giftCard, 'checked.length') || get(valueCard, 'checked.length')) {
      data.phasePaymentStage = 2;
    }

    // 设置实物定金预售
    data.depositPreSale = tradeTag.hasDepositPreSaleGoods;

    const dbid = app.db.set(data);
    const path = env.successUrl
      ? decodeURIComponent(env.successUrl)
      : '/packages/order/paid/index';
    const defaultPath = args.add(path, { dbid });

    if (isHandselFirstPhase) {
      return Promise.resolve(
        `/packages/ump/handsel-expand/index?order_no=${
          order.orderNo
        }&kdt_id=${app.getKdtId()}`
      );
    }

    if (isGift) {
      giftRetry = 1;
      return fetchGiftId(order.orderNo)
        .then((giftId) => {
          return `/packages/gift/share/index?presenter_view=1&gift_id=${giftId}`;
        })
        .catch((err) => {
          if (err.code && giftRetry < 3 && giftRolling) {
            giftRetry += 1;
            setTimeout(() => {
              navigateGiftShareDetail(order.orderNo);
            }, 1000);
          } else if (giftRolling) {
            dispatch('TOAST', err.msg || '获取礼盒id 失败');
          } else {
            // 当 giftId 请求失败, 银行卡支付成功跳转 导向默认成功页
            return defaultPath;
          }
        });
    }

    if (groupon.isGroupon) {
      const { goodsId } = goods.list[0] || {};
      return isEduOrder
        ? Promise.resolve(
            `/packages/paidcontent/groupon/index?orderNo=${order.orderNo}&goodId=${goodsId}`
          )
        : Promise.resolve(
            `/packages/collage/groupon/detail/index?from=pay&orderNo=${order.orderNo}&type=${groupon.activityType}`
          );
    }

    if (shop.activityType === 23) {
      // 抽奖拼团
      let activityId = 0;
      try {
        activityId = order.ump.activities[0].activityId;
      } catch (e) {
        console.log(e);
      }
      return Promise.resolve(
        `/packages/collage/lottery/detail/index?order_no=${order.orderNo}&activity_id=${activityId}`
      );
    }

    return Promise.resolve(defaultPath);
  },

  // 直播间订单
  USE_LIVE_EXT({ commit }, payload) {
    return app
      .request({
        origin: 'cashier',
        method: 'POST',
        path: '/pay/wsctrade/order/buy/getLiveActivity.json',
        data: payload,
      })
      .then((res) => {
        commit('SET_EXTERNAL_LIVE', res);
      })
      .catch(() => {
        return Promise.resolve(true);
      });
  },
};
