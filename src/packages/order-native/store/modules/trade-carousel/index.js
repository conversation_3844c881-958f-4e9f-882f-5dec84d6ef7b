import { getRandomAvatar } from './avatars';
import {
  node as request
} from 'shared/utils/request';

export const state = {
  tradeCarousel: {
    fixedTop: 34
  },
  // 浮动交易记录
  tradeRecordsV2: [],
  
  // 成交记录跑马灯
  tradeRecordsCarousel: [],
   // 店铺配置
  shopConfig: {
    // 新的跑马灯开关，支持评价、好评等内容
    goodsTradeMarquee: null,
    abConfigInfo: {},
  },
};

export const mutations = {
  setTradeRecords(state, payload) {
    state.tradeRecordsV2 = payload.map((tradeRecord) => {
      tradeRecord.show = false;
      tradeRecord.avatar = tradeRecord.avatar || getRandomAvatar();
      return tradeRecord;
    });
  },
  setTradeCarouselTop(state, payload) {
    state.tradeCarousel.fixedTop = payload.top;
  }
 
};


// 0-列表模式，1-悬浮滚动
// const TRADE_MODE = {
//   list: 0,
//   float: 1
// };

export const getters = {
  // showBuyRecordListStyle(state, getters) {
  //   // return getters.parsedGoodsDetailBuyRecord.indexOf(TRADE_MODE.list) > -1;
  //   return true;
  // },

  // parsedGoodsDetailBuyRecord(state) {
  //   const { showBuyRecord, goodsDetailBuyRecord = null } = state.shopConfig;

  //   /*
  //     如果没有解析数据就都显示； 0-列表模式，1-悬浮滚动;
  //     默认值根据老的成交记录字段老处理，老的字段是显示的话，那么0，1都显示；
  //   */
  //   let parsedGoodsDetailBuyRecord = showBuyRecord ? [TRADE_MODE.list, TRADE_MODE.float] : [];

  //   if (goodsDetailBuyRecord) {
  //     try {
  //       parsedGoodsDetailBuyRecord = JSON.parse(goodsDetailBuyRecord) || parsedGoodsDetailBuyRecord;
  //     } catch (error) {
  //       // do nothing
  //     }
  //   }

  //   return parsedGoodsDetailBuyRecord;
  // }
};


const SWITCH = {
  on: 1,
  off: 0,
};

function fetchRecords(data) {
  return request({
    path: '/wscgoods/getItemMarqueeRecords.json',
    data,
  });
}



export const actions = {
  FETCH_TRADE_RECORDS({ state, commit }) {
  
    const alias = state.goods.list[0].alias;
    
    const goodsTradeMarquee = state.shopConfig.goodsTradeMarquee;
  
    if (!alias || !goodsTradeMarquee) {
      return;
    }

    let show = false;
    try {
      show = +goodsTradeMarquee.show === SWITCH.on;
    } catch (e) { //
    }

    if (!show) {
      return;
    }

    fetchRecords({
      alias,
    })
      .then(payload => {
        const result = payload.map((tradeRecord) => {
          let formatRecord = tradeRecord.content;
          const content = tradeRecord.content;
          const match = /\s{0,1}(\W{0,1}\w{0,1}(\*)+\W{0,1}\w{0,1})\s{1}/.exec(content);
          if (match) {
            const name = match[0];
            const nameIndex = formatRecord.indexOf(name);
            if (nameIndex > -1) {
              formatRecord = formatRecord.slice(nameIndex).trim();
            }
          }
          if (content.indexOf('购买了该商品') > -1) {
            formatRecord = formatRecord.replace('购买了该商品', '已购买');
          }
          if (content.indexOf('给出了好评') > -1) {
            formatRecord = formatRecord.replace('给出了好评', '给出好评');
          }
          return {
            ...tradeRecord,
            content: formatRecord
            };
        });
        commit('setTradeRecords', result);
      })
      .catch(() => {
        // do nothing
      });
  },
};
