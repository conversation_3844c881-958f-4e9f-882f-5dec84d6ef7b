import money from '@youzan/weapp-utils/lib/money';
import get from '@youzan/weapp-utils/lib/get';
import { add } from '@/packages/order-native/utils/number';

function sum(arr) {
  return arr.reduce((prev, curr) => prev + curr, 0);
}

function formatPrice(price) {
  return money(price).toYuan();
}

// 计算商品价格
function getTotalPrice(prefix, price, points, pointsName) {
  const arr = [];
  if (points) {
    arr.push(points + pointsName);
  }
  if (price || !points) {
    arr.push(prefix + (price / 100).toFixed(2));
  }
  return arr.join(' + ');
}

export const getters = {
  hasDepositPreSaleGoods(state) {
    return state.tradeTag.hasDepositPreSaleGoods;
  },

  // 计价，用于 total-price 组件
  totalPrice(state, getters) {
    const { points, pay, shop, pointsName, displayCard } = state;
    const { showDisplayCardPrice } = getters;

    // 店铺优惠在交易那里用的applo配置的活动列表，这里的优惠金额应该是这几个优惠金额的叠加，不一定是订单级优惠金额
    const activities = (shop && shop.activities) || [];
    let decrease = activities.reduce(
      (prev, activity) => prev + activity.value,
      0
    );
    decrease = decrease
      ? `-￥${(decrease / 100).toFixed(2)}`
      : `- ￥${formatPrice(pay.decrease)}`;

    const taxPrice = pay.goodsTax + pay.postageTax;

    // 商品金额减去展示卡价格
    const itemPay = showDisplayCardPrice
      ? pay.itemPay - displayCard.price
      : pay.itemPay;

    return {
      // 商品金额
      priceLabel: getTotalPrice('¥', itemPay, points.totalPoints, pointsName),
      // 运费
      postage: `+ ¥${formatPrice(pay.postage)}`,
      // 活动优惠
      promotions: (pay.promotions || []).map(
        (item) =>
          `${item.decrease >= 0 ? '-' : '+'} ¥${formatPrice(
            Math.abs(item.decrease)
          )}`
      ),
      // 优惠
      couponDecrease: `- ¥${formatPrice(getters.couponDecrease)}`,
      // 支付优惠
      payUmpDiscountMoney: `- ¥${formatPrice(getters.payUmpDiscountMoney)}`,
      // 预付卡
      prepayCardDecrease: `- ¥${formatPrice(getters.prepayCardDecrease)}`,
      // 进口税
      taxPrice: pay.showTax ? `+ ¥${formatPrice(taxPrice)}` : '',
      decrease,
    };
  },

  // 分期支付信息
  installmentInfo(state) {
    const { selectedInstallmentPeriod, installmentRate } = state.display;

    return {
      periods: selectedInstallmentPeriod,
      rate: installmentRate,
      value:
        selectedInstallmentPeriod === 1
          ? '不分期/无手续费'
          : `分${selectedInstallmentPeriod}期/费率${installmentRate * 100}%`,
    };
  },

  // 分阶段支付 - 金额抵价文案
  phasePaymentExpandLabel(state) {
    const { pay } = state;
    if (!pay.multiPhase) {
      return [];
    }

    return pay.phasePayment.phaseItems.map((item) => {
      if (item.valuePrice && item.valuePrice !== item.price) {
        return `（定金可抵 ¥ ${formatPrice(item.valuePrice)}）`;
      }
      return '';
    });
  },

  priceLabel(state, getters) {
    // 定金预售
    if (getters.isDepositPresale) {
      return state.pay.phasePayment.currentPhase === 1 ? '定金：' : '尾款：';
    }

    return '合计：';
  },

  // 是否为定金预售
  isDepositPresale(state) {
    return (
      state.pay.multiPhase &&
      get(state, 'pay.phasePayment.bizCode', '') === 'DOWN_PAYMENT_PRE'
    );
  },

  // 选中储值卡的总金额
  valueCardTotalAmount(state) {
    const checked = state.valueCard.list.filter(
      (card) => state.valueCard.checked.indexOf(card.summaryCardNo) !== -1
    );
    return sum(checked.map((item) => item.balance));
  },

  // 预付卡可以抵扣的最大金额
  prepayCardAvailable(state) {
    const valueCardAvailable = state.valueCard.list.filter(
      (card) => card.usable
    );
    const valueCardSum = sum(valueCardAvailable.map((item) => item.balance));
    return valueCardSum;
  },

  // 除互斥卡外可用金额
  prepayCardAvailableExclusion(state) {
    const valueCardAvailableExclusion = state.valueCard.list.filter(
      (card) => card.usable && !card.isExclusion
    );
    const valueCardSumExclusion = sum(
      valueCardAvailableExclusion.map((item) => item.balance)
    );
    return valueCardSumExclusion;
  },

  // 选中预付卡的总金额
  prepayCardTotalAmount(_, getters) {
    return getters.valueCardTotalAmount;
  },

  // 优惠券抵扣金额
  couponDecrease(_, getters) {
    return getters.chosenCoupon ? getters.chosenCoupon.value || 0 : 0;
  },

  // 支付优惠
  payUmpDiscountMoney(state) {
    if (state.pay.phasePayment && state.pay.phasePayment.phaseItems) {
      return state.pay.phasePayment.phaseItems.reduce(
        (prev, curr) => prev.payUmpDiscountMoney + curr.payUmpDiscountMoney
      );
    }
    return 0;
  },

  // 储值卡抵扣金额
  valueCardDecrease(state, getters) {
    const { pay } = state;
    const maxDecrease = getters.valueCardTotalAmount;

    // 支付页直接返回金额
    if (state.env.isPayPage) {
      return pay.valueCardPayPrice || 0;
    }

    // 分阶段支付
    if (pay.multiPhase) {
      return Math.min(sum(getters.couponDecreasedPhasePayment), maxDecrease);
    }

    return Math.min(getters.couponDecreasedPrice, maxDecrease);
  },

  /**
   * 预付卡抵扣金额
   */
  prepayCardDecrease(_, getters) {
    return getters.valueCardDecrease;
  },

  newCouponProcess(state) {
    return state.order.newCouponProcess;
  },

  // 优惠券抵扣后的待支付金额
  couponDecreasedPrice(state, getters) {
    if (getters.newCouponProcess) {
      // 新流程中realPay已经减去优惠券的金额了
      return Math.max(0, state.pay.realPay);
    }
    return Math.max(0, state.pay.realPay - getters.couponDecrease);
  },

  // 最终分阶段支付金额
  couponDecreasedPhasePayment(state, getters) {
    const { pay } = state;

    if (!pay.multiPhase) {
      return [];
    }

    // 抵扣尾款
    // 邮费无法抵扣
    const prices = pay.phasePayment.phaseItems.map((item) => item.currentPrice);
    let decrease = getters.couponDecrease;
    if (getters.newCouponProcess) {
      decrease = 0;
    }
    const finalPrices = [];
    finalPrices[1] = Math.max(state.pay.postage, 0, prices[1] - decrease);
    decrease -= prices[1] - finalPrices[1];

    // 抵扣定金
    finalPrices[0] = Math.max(0, prices[0] - decrease);

    return finalPrices;
  },

  // 合计金额
  finalPrice(state, getters) {
    const { pay } = state;

    // 多阶段支付，返回阶段支付金额
    if (pay.multiPhase) {
      if (state.tradeTag.hasDepositPreSaleGoods) {
        // 阶段支付累加各个阶段的和
        return getters.finalPhasePayment
          .reduce((prev, curr) => add(+prev, +curr))
          .toFixed(2);
      }
      // 组合支付取最后一个阶段的支付金额
      return getters.finalPhasePayment[
        pay.phasePayment.currentPhase - 1
      ].toFixed(2);
    }

    // 支付页直接返回金额
    if (state.env.isPayPage) {
      return money(state.pay.realPay).toYuan();
    }

    const price = getters.couponDecreasedPrice - getters.prepayCardDecrease;
    return money(price).toYuan();
  },

  // 订单最终待支付金额
  finalNeedPayPrice(state, getters) {
    const { pay } = state;

    // 多阶段支付，返回阶段支付金额
    if (pay.multiPhase) {
      return getters.finalPhasePayment[pay.phasePayment.currentPhase - 1];
    }

    // 支付页直接返回金额
    if (state.env.isPayPage) {
      return money(state.pay.realPay).toYuan();
    }

    return money(
      getters.couponDecreasedPrice - getters.prepayCardDecrease
    ).toYuan();
  },

  // NOTE: 交易与ump不支持 需要前端计算海淘商品0元逻辑 shit
  haitaoZeroOrder(state, getters) {
    const { pay, env } = state;

    // 支付页直接返回金额
    if (env.isPayPage) {
      return false;
    }

    return (
      getters.hasHaitaoGoods &&
      money(getters.finalPrice).toCent() < getters.goodsCount * 1 + pay.postage
    );
  },

  haitaoFinalPrice(state, getters) {
    const { pay, env } = state;

    // 支付页直接返回金额
    if (env.isPayPage) {
      return money(pay.realPay).toYuan();
    }

    return getters.haitaoZeroOrder
      ? money(getters.goodsCount * 1 + pay.postage).toYuan()
      : getters.finalPrice;
  },

  // 订单的最终金额
  orderFinalPrice(state, getters) {
    return getters.haitaoZeroOrder
      ? getters.haitaoFinalPrice
      : getters.finalNeedPayPrice;
  },

  // 订单最终分阶段支付金额
  finalPhasePayment(state, getters) {
    const { pay } = state;

    if (!pay.multiPhase) {
      return [];
    }

    // 支付页直接返回金额
    if (state.env.isPayPage) {
      return pay.phasePayment.phaseItems
        .map((item) => item.buyerRealPay)
        .map((item) => money(item).toYuan());
    }

    let { prepayCardDecrease } = getters;
    const prices = getters.couponDecreasedPhasePayment;
    const finalPrices = [];

    finalPrices[1] = Math.max(0, prices[1] - prepayCardDecrease);
    prepayCardDecrease -= prices[1] - finalPrices[1];
    finalPrices[0] = Math.max(0, prices[0] - prepayCardDecrease);

    return finalPrices.map((item) => money(item).toYuan());
  },

  // 是否要显示 展示卡 价格
  showDisplayCardPrice(state, getters) {
    // 展示卡存在 且 (已勾选展示卡 或 订单已创建)
    return (
      getters.hasDisplayCard &&
      (state.display.openDisplayCard || getters.orderCreated)
    );
  },
};
