import pick from '@youzan/weapp-utils/lib/pick';

const app = getApp();

export const actions = {
  // 埋点 - 订单创建成功
  LOG_CREATE_ORDER({ state } = {}) {
    const { adData = {} } = app.globalData;
    const { orderNos } = state.order;
    const { prepay, prepaySuccess, prepayId = '' } =
      (state.pay && state.pay.prepayParams) || {};
    if (!Array.isArray(orderNos)) {
      return;
    }

    orderNos.forEach((orderNo) => {
      const params = {
        type: 'normal',
        prepay_id: prepayId,
        order_no: orderNo,
        orders_combine_id: state.order.orderNo,
        guarantee_on: state.display.yzGuarantee,
      };
      if (adData.gdtVid) {
        params.click_id = adData.gdtVid;
      }
      app.logger.log({
        et: 'click',
        ei: 'orderCreate',
        en: '下单',
        params,
      });

      if (prepay) {
        app.logger.log({
          et: 'custom',
          ei: 'prepay',
          en: '预下单',
          params: {
            type: prepaySuccess ? 'success' : 'error',
            prepay_id: prepayId,
          },
        });
      }
    });
  },

  // 埋点 - 聚焦留言框
  LOG_FOCUS_MESSAGE() {
    app.logger.log({
      et: 'click',
      ei: 'order_message',
      en: '订单留言',
    });
  },

  // 埋点 - 点击地址栏
  LOG_CLICK_ADDRESS({ getters } = {}) {
    app.logger.log({
      et: 'click',
      ei: 'address_change',
      en: '更换地址',
      params: {
        address: getters.expressType,
      },
      si: app.getKdtId(),
    });
  },

  // 埋点 - 点击支付方式
  LOG_CLICK_PAY_TYPE({ state }, payType = {}) {
    const { prepayId = '' } = (state.pay && state.pay.prepayParams) || {};
    app.logger.log({
      et: 'click',
      ei: 'pay_item',
      en: '选择支付方式',
      params: {
        type: 'normal',
        prepay_id: prepayId,
        pay_channel: payType.pay_channel,
        pay_channel_name: payType.pay_channel_name,
      },
      si: app.getKdtId(),
    });
  },

  LOG_PAY_SUCCESS(store, params) {
    app.logger.log({
      et: 'click',
      ei: 'pay_success',
      en: '支付成功',
      params,
      si: app.getKdtId(),
    });

    app.logger.log({
      et: 'click',
      ei: 'order_pay_success',
      en: '支付成功',
      params,
      si: app.getKdtId(),
    });
  },

  LOG_EDIT_ADDRESS(store, address) {
    app.logger.log({
      et: 'click', // 事件类型
      ei: 'edit_address', // 事件标识
      en: '编辑地址', // 事件名称
      params: address, // 事件参数
      si: app.getKdtId(),
    });
  },

  LOG_ADD_ADDRESS() {
    app.logger.log({
      et: 'click', // 事件类型
      ei: 'click_address', // 事件标识
      en: '新增地址', // 事件名称
      si: app.getKdtId(),
    });
  },

  // 埋点 - 点击隐私政策
  LOG_CLICK_PRIVACY({ state }) {
    app.logger.log({
      et: 'click', // 事件类型
      ei: 'click_privacy_policyq', // 事件标识
      en: '点击隐私政策', // 事件名称
      params: {
        goods_list: state.order.items.map((item) =>
          pick(item, [
            'skuId',
            'goodsId',
            'goodsType',
            'price',
            'payPrice',
            'num',
            'groupIds',
            'itemBizMarkCode',
          ])
        ),
        order_amount: state.pay.itemPay,
        logistics_price: state.pay.postage,
      }, // 事件参数
    });
  },

  // 埋点 - 点击用户协议
  LOG_USER_AGGREMENT({ state }) {
    app.logger.log({
      et: 'click', // 事件类型
      ei: 'click_user_agreement', // 事件标识
      en: '点击用户协议', // 事件名称
      params: {
        goods_list: state.order.items.map((item) =>
          pick(item, [
            'skuId',
            'goodsId',
            'goodsType',
            'price',
            'payPrice',
            'num',
            'groupIds',
            'itemBizMarkCode',
          ])
        ),
        order_amount: state.pay.itemPay,
        logistics_price: state.pay.postage,
      }, // 事件参数
    });
  },
};
