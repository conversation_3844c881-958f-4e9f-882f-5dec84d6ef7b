import get from '@youzan/weapp-utils/lib/get';
import money from '@youzan/weapp-utils/lib/money';

// 格式化运费
function calcPostage(postage) {
  return postage === 0 ? '免运费' : '¥' + money(postage).toYuan();
}

const app = getApp();

export const state = {
  // 物流信息
  postage: {
    postageItems: [],
    currentExpressType: 0
  },
  // 同城送
  delivery: {
    isShow: false,
    text: '',
    startTime: '',
    endTime: ''
  },
  // 身份证
  idcard: {
    name: '',
    number: '',
    backPhoto: '',
    frontPhoto: '',
    // 实名认证 店铺条数不足 或 尝试次数过多
    verifyFalse: false
  }
};

export const getters = {
  // 是否为周期购
  isPeriodBuy(state) {
    return state.periodBuy.info || state.periodBuy.planTime;
  },

  periodBuyTimeText(state, getters) {
    const { options, chosenIndex, chosenOption } = state.periodBuy;
    const time = options[chosenIndex] || chosenOption;
    const typeText = getters.expressType === 'self-fetch' ? '提货' : '送达';
    return time ? `${time} 首次${typeText}` : '';
  },

  // 配送方式
  expressType(state) {
    if (state.display.showExpressTab && state.selfFetch.isAllow) {
      return state.address.activeTab === 1 ? 'self-fetch' : 'express';
    }
    return state.selfFetch.isAllow ? 'self-fetch' : 'express';
  },

  // 当前配送方式
  currentPostage(state) {
    const { postage } = state;
    const items = get(postage, 'postageItems', []);
    if (Array.isArray(items)) {
      return items.filter((item) => item.expressType === postage.currentExpressType)[0] || {};
    }
    return {};
  },

  // 预售配送时间
  preSaleDeliveryTime(_, getters) {
    const good = getters.formattedGoods.find((good) => good.showDeliveryTime && good.deliveryTime);
    return (good || {}).deliveryTime || '';
  },

  showExpressWay(_, getters) {
    return getters.hasGoods && getters.expressWayList.length > 0;
  },

  expressWayList(state) {
    return get(state.postage, 'postageItems', []).map((item) => ({
      ...item,
      postage: calcPostage(item.postage)
    }));
  },

  expressWayEditable(state, getters) {
    const postageItems = get(state.postage, 'postageItems', []);
    const showLocalDeliveryTime = getters.hasGoods && state.display.showLocalDeliveryTime;
    return !getters.orderCreated && (postageItems.length > 1 || showLocalDeliveryTime);
  },

  // 是否显示同城送时间
  showLocalDeliveryTime(state, getters) {
    const { preSaleDeliveryTime, hasGoods } = getters;
    const { showLocalDeliveryTime } = state.display;
    return hasGoods && (showLocalDeliveryTime || !!preSaleDeliveryTime);
  },

  // 同城送时间
  localDeliveryTime(state, getters) {
    const { delivery } = state;
    const { preSaleDeliveryTime } = getters;
    return delivery.text || preSaleDeliveryTime || '请选择期望送达时间';
  },

  idNumber(state, getters) {
    const { order, idcard } = state;
    const showNumber = !order.needIdCardPhoto || (idcard.frontPhoto && idcard.backPhoto);
    if (getters.orderCreated) {
      return `${idcard.name} ${getters.maskedIdNumber}`;
    }
    return showNumber && (idcard.name || idcard.number) ? `${idcard.name} ${idcard.number}` : '';
  }
};

export const mutations = {
  SET_EXPRESS_TYPE_CHOICE(state, selected) {
    state.postage.currentExpressType = selected;
  },

  SET_PERIOD_BUY_CHOSEN_INDEX(state, index) {
    state.periodBuy.chosenIndex = index;
  },

  SET_IDCARD(state, paylod) {
    state.idcard = { ...paylod };
  },

  SET_IDCARD_NAME(state, name) {
    state.idcard.name = name;
  },

  SET_IDCARD_NUMBER(state, number) {
    state.idcard.number = number;
  },

  SET_IDCARD_FRONT(state, image) {
    state.idcard.frontPhoto = image;
  },

  SET_IDCARD_BACK(state, image) {
    state.idcard.backPhoto = image;
  },

  SET_VERIFY_FAIL(state, verifyFalse) {
    state.idcard.verifyFalse = verifyFalse;
  },

  SET_LOCAL_DELIVERY_TIME(state, time) {
    state.delivery = Object.assign({}, state.delivery, time);
  },
  RESET_LOCAL_DELIVERY_TIME(state) {
    state.delivery = Object.assign({}, state.delivery, {
      endTime: '',
      startTime: '',
      text: '',
      textWithWeekday: ''
    });
  }
};

export const actions = {
  VERIFY_IDCARD({ state = {}, dispatch, commit } = {}, idcard = state.idcard) {
    const { order, ignoreIdBinding } = state;
    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/verifyIdcard.json',
        method: 'POST',
        data: {
          binding: ignoreIdBinding ? undefined : (idcard.binding ? true : false),
          idCardName: idcard.name,
          idCardNumber: idcard.number,
          bookKey: order.bookKey,
          itemInfoDTOs: state.order.items
        }
      })
      .then((response) => {
        const { verifyPassed } = response;
        if (!verifyPassed) {
          throw response;
        }
        return response;
      })
      .catch((error) => {
        const { verifyNotPassedReasonCode, verifyNotPassedSolution } = error || {};
        switch (verifyNotPassedReasonCode) {
          case 1:
            dispatch('TOAST', verifyNotPassedSolution);
            break;
          case 2:
          case 3:
            // 超过最大认证次数 和 店铺没有可用认证条数 时，先保存失败状态，提交订单时提示
            commit('SET_VERIFY_FAIL', true);
            return error;
          default:
            dispatch('TOAST', '实名认证失败，请稍后再试');
        }
        throw error;
      });
  },
  FETCH_IDCARD_LIST() {
    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/getReceiverIdentityList.json',
        method: 'GET',
      })
  }
};
