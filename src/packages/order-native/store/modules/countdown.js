import { format } from '@youzan/weapp-utils/lib/time-utils';

export const getters = {
  // 等待尾款支付开始
  waitPhaseTwoStart(state) {
    const { multiPhase, phasePayment } = state.pay;
    return (
      multiPhase
      && phasePayment.currentPhase === 2
      && phasePayment.phaseItems[1].payStartTime > Date.now()
    ) || false;
  },

  countdownTitle(state, getters) {
    if (getters.isDepositPresale) {
      if (state.pay.phasePayment.currentPhase === 1) {
        return '等待买家付定金';
      }

      return getters.waitPhaseTwoStart ? '等待尾款支付开始' : '等待买家付尾款';
    }
    return '等待买家付款';
  },

  countdownLabel(state, getters) {
    if (!getters.waitPhaseTwoStart) {
      return '';
    }

    let { payStartTime, payEndTime } = state.pay.phasePayment.phaseItems[1];
    payStartTime = new Date(payStartTime);
    payEndTime = new Date(payEndTime);

    const formatTime = time => format(time, 'yyyy.MM.dd hh:mm:ss');
    return `尾款：${formatTime(payStartTime)} - ${formatTime(payEndTime)}`;
  }
};
