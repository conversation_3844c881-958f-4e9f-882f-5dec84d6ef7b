import designConf from '../../design.json';

export const state = {
  // design组件配置
  design: [],
  extensionConfig: {}, // 业务配置，可能从云端获取配置
};

export const mutations = {
  SET_DESIGN(state, payload) {
    state.design = payload;
  },
  SET_EXTENSION_CONFIG(state, payload) {
    state.extensionConfig = {
      ...payload
    };
  }
};

export const actions = {
  // 做成异步是因为后面这个配置获取会放到服务端
  GET_DESIGN({ commit }) {
    const modList = designConf.design || [];
    const config = modList[0] && modList[0].type === 'config' && modList[0];

    return Promise.resolve({
      config,
      design: config ? modList.slice(1) : modList
    }).then(({ config = {}, design }) => {
      commit('SET_EXTENSION_CONFIG', { ...config, ...config.profile });
      commit('SET_DESIGN', design);
    });
  },
};
