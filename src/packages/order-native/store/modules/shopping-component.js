import {
  checkFromChannelsLive,
  getTradeModuleStatus,
  requestSceneCheck,
  checkBeforeAddOrderPromise,
} from 'shared/utils/channel';

const app = getApp();

export const state = {
  sceneCheckResult: {
    sceneCheck: false,
    isOpenWeappUnion: false,
    checkBeforeAddOrderResult: null,
  },
  wxOrderInfo: null,
  wxvideoSharerOpenId: null,
  useWxvideoPayFlow: false,
  createWxvideoOrderIntercept: {
    visible: false,
    code: null,
    msg: null,
  },
};

export const mutations = {
  SET_SCENE_CHECK_RESULT(state, payload) {
    state.sceneCheckResult = payload;
  },
  SET_WX_ORDER_INFO(state, payload) {
    state.wxOrderInfo = payload;
  },
  SET_CHECK_WXVIDEO_SHARE_ORDER_RESULT(state, payload) {
    state.wxvideoSharerOpenId = payload;
  },
  SET_USE_WXVIDEO_PAY_FLOW(state, payload) {
    state.useWxvideoPayFlow = payload;
  },
  SET_CREATE_WXVIDEO_ORDER_INTERCEPT(state, payload) {
    const createWxvideoOrderIntercept = {
      ...(payload || {}),
    };
    // 对预售和其他禁止的营销活动报错特殊处理
    if (
      payload?.msg &&
      (payload?.code === 101305069 || payload?.code === 101305071)
    ) {
      // 与后端定义好的msg返回结构@阿标
      const errorMsgSplitList = payload?.msg.split('$*');
      createWxvideoOrderIntercept.umpMsg = {
        head: errorMsgSplitList[0] || '',
        skuNameList: (errorMsgSplitList[1] || '')
          .split('、')
          .map((item) => `· ${item}`),
        footer: errorMsgSplitList[2] || '',
      };
    }
    state.createWxvideoOrderIntercept = createWxvideoOrderIntercept;
  },
};

// 获取所有同步订单状态
const getAllSyncOrderStatusFromState = (state) => {
  const defaultResult = {
    shouldSyncOrder: false,
    shouldSyncOrderV2: false,
    shouldSyncOrderV3: false,
    shouldSyncOrderWithOpenFlowInTradeModuleV3: false,
  };

  return new Promise((resolve) => {
    const isPayPage = state?.pageQuery?.orderNo;
    const isBuyPage = !isPayPage;
    /** 是否交易组件开通流程 */
    /** 用商详页带过来的extension中TRADE_MODULE_ORDER=TRUE 来标记是交易组件开通流程下的单 */
    let BIZ_ORDER_ATTRIBUTE = {};
    try {
      BIZ_ORDER_ATTRIBUTE = JSON.parse(
        state.order.extensions?.BIZ_ORDER_ATTRIBUTE || '{}'
      );
    } catch (error) {
      app.logger.appError({
        name: '[weapp]-wxvideo',
        message: '交易组件下单--BIZ_ORDER_ATTRIBUTE解析失败',
        detail: error,
      });
      return resolve(defaultResult);
    }
    // 是否交易组件开通流程
    const isTradeModuleOpenFlow =
      BIZ_ORDER_ATTRIBUTE.TRADE_MODULE_ORDER === 'TRUE';
    // 是否交易组件3.0开通流程
    const isTradeModuleV3OpenFlow =
      BIZ_ORDER_ATTRIBUTE.TRADE_MODULE_ORDER === 'TRUE' &&
      BIZ_ORDER_ATTRIBUTE.OPEN_TRADE_MODULE_VERSION === '3.0';
    // 是否待支付页的交易组件3.0订单
    const isTradeModuleV3OrderInPay =
      isPayPage &&
      BIZ_ORDER_ATTRIBUTE.WX_CHANNELS_COMPONENT_CREATE ===
        'TRADE_COMPONENT_3_0';

    // 优先使用基础库下单前置检查接口结果 排除待支付页 排除开通流程 排除两种方式皆可
    if (
      !isPayPage &&
      !isTradeModuleOpenFlow &&
      state.sceneCheckResult?.checkBeforeAddOrderResult?.requireOrder === 1 &&
      state.sceneCheckResult?.checkBeforeAddOrderResult?.requiredFundType !== 2
    ) {
      return resolve({
        shouldSyncOrder: true,
        // requiredFundType: requireOrder = 1时生效，0，非二级商户号订单，1，二级商户号订单，2，两种方式皆可（后续只会存在1）
        shouldSyncOrderV2:
          state.sceneCheckResult?.checkBeforeAddOrderResult
            ?.requiredFundType === 0,
        shouldSyncOrderV3:
          state.sceneCheckResult?.checkBeforeAddOrderResult
            ?.requiredFundType === 1 ||
          state.sceneCheckResult?.checkBeforeAddOrderResult
            ?.requiredFundType === 2,
        shouldSyncOrderWithOpenFlowInTradeModuleV3: false,
      });
    }

    // 处于交易组件开通流程 或 scene/check通过 或 小程序联盟开通 或 待支付页的交易组件3.0订单 条件限制减少请求量
    if (
      isTradeModuleOpenFlow ||
      state.sceneCheckResult.sceneCheck ||
      state.sceneCheckResult.isOpenWeappUnion ||
      isTradeModuleV3OrderInPay
    ) {
      return getTradeModuleStatus()
        .then((weappTradeModuleStatus = {}) => {
          // v2
          // sceneCheck通过 且 交易组件2.0启用
          const shouldSyncOrderWithSceneCheckInTradeModuleV2 =
            state.sceneCheckResult.sceneCheck &&
            weappTradeModuleStatus.WEAPP_TRADE_MODULE_IS_ENABLE;
          // 处于交易组件开通流程 且 交易组件3.0已开通但未启用
          const shouldSyncOrderWithOpenFlowInTradeModuleV2 =
            isTradeModuleOpenFlow &&
            weappTradeModuleStatus.WEAPP_TRADE_MODULE_IS_OPEN &&
            !weappTradeModuleStatus.WEAPP_TRADE_MODULE_IS_ENABLE;
          // 小程序联盟开通 且 交易组件2.0启用
          const shouldSyncOrderV2WithWeappUnion =
            state.sceneCheckResult.isOpenWeappUnion &&
            weappTradeModuleStatus.WEAPP_TRADE_MODULE_IS_ENABLE;
          // 交易组件2.0订单是否同步
          const shouldSyncOrderV2 =
            shouldSyncOrderWithSceneCheckInTradeModuleV2 ||
            shouldSyncOrderWithOpenFlowInTradeModuleV2 ||
            shouldSyncOrderV2WithWeappUnion;

          // v3
          // 订单确认页
          // sceneCheck通过 且 交易组件3.0启用
          const shouldSyncOrderWithSceneCheckInTradeModuleV3 =
            isBuyPage &&
            state.sceneCheckResult.sceneCheck &&
            weappTradeModuleStatus.WEAPP_TRADE_MODULE_V3_IS_ENABLE;
          // 处于交易组件3.0开通流程 且 交易组件3.0已开通但未启用
          const shouldSyncOrderWithOpenFlowInTradeModuleV3 =
            isBuyPage &&
            isTradeModuleV3OpenFlow &&
            weappTradeModuleStatus.WEAPP_TRADE_MODULE_V3_IS_OPEN &&
            !weappTradeModuleStatus.WEAPP_TRADE_MODULE_V3_IS_ENABLE;
          //  交易组件3.0订单 在订单确认页 是否同步
          const shouldSyncOrderV3InBuy =
            shouldSyncOrderWithSceneCheckInTradeModuleV3 ||
            shouldSyncOrderWithOpenFlowInTradeModuleV3;
          // 待支付页
          // 处于交易组件3.0开通流程 且 交易组件3.0已开通但未启用
          const shouldSyncOrderWithOpenFlowInTradeModuleV3InPayPage =
            isPayPage &&
            isTradeModuleV3OpenFlow &&
            weappTradeModuleStatus.WEAPP_TRADE_MODULE_V3_IS_OPEN &&
            !weappTradeModuleStatus.WEAPP_TRADE_MODULE_V3_IS_ENABLE;
          //  交易组件3.0订单 在待支付页 是否同步
          const shouldSyncOrderV3InPay =
            (isTradeModuleV3OrderInPay &&
              weappTradeModuleStatus.WEAPP_TRADE_MODULE_V3_IS_ENABLE) ||
            (isTradeModuleV3OrderInPay &&
              shouldSyncOrderWithOpenFlowInTradeModuleV3InPayPage);

          // 交易组件3.0订单是否同步
          const shouldSyncOrderV3 =
            shouldSyncOrderV3InBuy || shouldSyncOrderV3InPay;

          /**
           * 是否同步订单
           * shouldSyncOrderV2 || shouldSyncOrderV3
           */
          const shouldSyncOrder = shouldSyncOrderV2 || shouldSyncOrderV3;

          const result = {
            shouldSyncOrder,
            shouldSyncOrderV2,
            shouldSyncOrderV3,
            shouldSyncOrderWithOpenFlowInTradeModuleV3,
          };

          return resolve(result);
        })
        .catch((e) => {
          app.logger.appError({
            name: '[weapp]-wxvideo',
            message: '交易组件下单--获取交易组件接入信息失败',
            detail: e,
          });
          return resolve(defaultResult);
        });
    }
    return resolve(defaultResult);
  });
};

export const actions = {
  CHECK_WXVIDEO_SHARER_ORDER({ commit }) {
    // 微信视频号直播间场景
    if (checkFromChannelsLive()) {
      // 需要基础库达到2.22.1
      if (!wx.getChannelsShareKey) {
        return;
      }
      return wx.getChannelsShareKey?.({
        success(res) {
          const { sharerOpenId } = res || {};
          commit('SET_CHECK_WXVIDEO_SHARE_ORDER_RESULT', sharerOpenId);
        },
        fail(error) {
          app.logger.appError({
            name: '[weapp]-wxvideo',
            message:
              '微信视频号分享员订单打标流程--调用wx.getChannelsShareKey失败',
            detail: error,
          });
        },
      });
    }
  },

  SCENE_CHECK({ commit }) {
    const defaultResult = {
      sceneCheck: false,
      checkBeforeAddOrderResult: null,
      isOpenWeappUnion: false,
    };
    return requestSceneCheck()
      .then((response) => {
        return checkBeforeAddOrderPromise()
          .then((res) => {
            const sceneCheckResult = {
              checkBeforeAddOrderResult: res,
              sceneCheck: response?.sceneCheck,
              isOpenWeappUnion: response?.isOpenWeappUnion,
            };
            commit('SET_SCENE_CHECK_RESULT', sceneCheckResult);
            return sceneCheckResult;
          })
          .catch(() => {
            // 调用失败 使用scene/check兜底
            const sceneCheckResult = {
              ...defaultResult,
              sceneCheck: response?.sceneCheck,
              isOpenWeappUnion: response?.isOpenWeappUnion,
            };
            commit('SET_SCENE_CHECK_RESULT', sceneCheckResult);
            return sceneCheckResult;
          });
      })
      .catch((e) => {
        app.logger.appError({
          name: '[weapp]-wxvideo',
          message: '交易组件下单--调用scene/check失败',
          detail: e,
        });
        return checkBeforeAddOrderPromise()
          .then((res) => {
            const sceneCheckResult = {
              checkBeforeAddOrderResult: res,
              sceneCheck: false,
              isOpenWeappUnion: false,
            };
            commit('SET_SCENE_CHECK_RESULT', sceneCheckResult);
            return sceneCheckResult;
          })
          .catch(() => {
            return defaultResult;
          });
      });
  },

  HANDLE_MUTATE_STATE_AFTER_FETCH_IN_WXVIDEO({ state, commit }) {
    if (state.useWxvideoPayFlow) {
      // 交易组件3.0订单隐藏储值卡
      commit('SET_COUPON_COMP_VALUE_CARD', { display: false });
      commit('RESET_PREPAY_CARD_CHECK_STATUS', state);
    }
  },

  CHECK_USE_WXVIDEO_PAY_FLOW({ state, commit }) {
    return getAllSyncOrderStatusFromState(state)
      .then((allSyncOrderStatus) => {
        const { shouldSyncOrderV3 } = allSyncOrderStatus;
        // 交易组件3.0 需要同步订单至微信的 使用视频号的微信支付
        commit('SET_USE_WXVIDEO_PAY_FLOW', shouldSyncOrderV3);
        return shouldSyncOrderV3;
      })
      .catch(() => false);
  },

  GET_WX_ORDER_INFO({ state, commit }) {
    return getAllSyncOrderStatusFromState(state).then((allSyncOrderStatus) => {
      const isSyncOrder = allSyncOrderStatus?.shouldSyncOrder;
      if (isSyncOrder) {
        const orderNo = state.order?.orderNo;
        return app
          .request({
            origin: 'cashier',
            path: '/pay/wsctrade/order/buy/wx-order-info',
            data: { orderNo },
          })
          .then((orderInfo) => {
            commit('SET_WX_ORDER_INFO', orderInfo);
          })
          .catch((e) => {
            console.error('微信自定义购物组件订单信息查询失败', e);
          });
      }
      return Promise.resolve();
    });
  },

  GET_IS_SYNC_ORDER({ state }) {
    return getAllSyncOrderStatusFromState(state)
      .then((allSyncOrderStatus) => {
        return allSyncOrderStatus?.shouldSyncOrder;
      })
      .catch(() => false);
  },
};
