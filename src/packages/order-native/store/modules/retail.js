const app = getApp();

export const state = {
  retailOrderScene: '', // 来源
  warehouseId: null, // 供货点id
  retailJointId: null, // 拼单id
  retailAddress: {
    // 前4个参数与address module同步，不需要零售维护
    id: '', // 选中收货地址的id
    list: [], // 备选收货地址列表
    activeTab: 0, // 当前选中的配送 / 自提方式
    visible: true, // 地址模块是否可见
    activeList: [],
    inactiveList: [], // 失效地址（超出配送范围）
  },
  retailPickUpWayIdx: -1, // 取货模式集合中选中的下标
  retailPickUpWays: [], // 取货模式集合
};

export const getters = {
  // 是否是零售履约单元供货模式
  isRetailWarehouseMode({ retailOrderScene, warehouseId }) {
    return retailOrderScene === '24hshelf' && warehouseId;
  },
  retailPickUpWayName({ retailPickUpWayIdx, retailPickUpWays }) {
    if (retailPickUpWayIdx < 0) return '';
    return retailPickUpWays[retailPickUpWayIdx].name;
  },
  retailPickUpWayValue({ retailPickUpWayIdx, retailPickUpWays }) {
    if (retailPickUpWayIdx < 0) return '';
    return retailPickUpWays[retailPickUpWayIdx].value;
  },
};

export const mutations = {
  SET_RETAIL_ORDER_SCENE(state, scene) {
    state.retailOrderScene = scene;
  },
  SET_WAREHOUSE_ID(state, id) {
    state.warehouseId = id;
  },
  SET_RETAIL_JOINT_ID(state, id) {
    state.retailJointId = id;
    state.order.extensions = {
      BIZ_ORDER_ATTRIBUTE: JSON.stringify({
        JOINT_ORDER: '1',
        JOINT_ID: '' + state.retailJointId
      })
    };
  },
  SET_RETAIL_ADDRESS(state, address) {
    state.retailAddress = address;
  },
  // 设置取货方式相关数据
  SET_PICK_UP_WAY_DATA(
    state,
    { calculatedPickUpWay = -1, supportedPickUpWays = {} }
  ) {
    const retailPickUpWays = Object.keys(supportedPickUpWays).map((key) => {
      return {
        name: supportedPickUpWays[key],
        value: +key,
      };
    });
    state.retailPickUpWayIdx = retailPickUpWays.findIndex(
      (item) => item.value === +calculatedPickUpWay
    );
    state.retailPickUpWays = retailPickUpWays;
  },
  // 设置取货方式
  UPDATE_PICK_UP_WAY(state) {
    const { retailPickUpWayIdx, retailPickUpWays } = state;
    state.retailPickUpWayIdx =
      (retailPickUpWayIdx + 1) % retailPickUpWays.length;
  },
};

export const actions = {
  // 批量校验地址是否超出配送范围
  BATCH_CHECK_ADDRESS_SUPPORT({ state }, data) {
    const supportAddressMap = {};
    // 针对历史数据没有lon/lat的场景过滤
    const addressList = data.addressList.reduce(
      (addressList, item) => {
        const {
          id: addressId,
          lat,
          lon,
          province = '',
          city = '',
          county = '',
          addressDetail = '',
        } = item;
        if (lat && lon) {
          addressList.push({
            addressId,
            lat,
            lon,
            addressStr: `${province}${city}${county}${addressDetail}`,
          });
        } else {
          supportAddressMap[addressId] = false;
        }
        return addressList;
      }, []
    );
    const request = addressList => {
      return app
        .request({
          path: '/retail/h5/miniprogram/checkUserAddressSupport.json',
          data: {
            warehouseId: state.warehouseId,
            addressList,
          },
          method: 'POST',
        }).catch(() => []);
    };
    // 单个地址请求参数会很长，并且最多可以有50个地址，所以做并发处理
    const requestGroup = addressList.reduce((group, address, i) => {
      if (i % 20 === 0) group.push([]);
      group[group.length - 1].push(address);
      return group;
    }, []);
    return Promise.all(requestGroup.map(request))
      .then((resArr) => {
        const arr = [];
        for (let i = 0; i < resArr.length; i += 1) {
          arr.push(...resArr[i]);
        }
        return arr;
      }).then((res) => {
        return res.reduce(
          (map, { addressId, isSupport = false }) => {
            map[addressId] = isSupport;
            return map;
          },
          supportAddressMap
        );
      }).catch(() => {});
  },
  // 获取附近可送门店
  CHECK_USER_ADDRESS_SUPPORT({ dispatch, commit, state }) {
    return dispatch('BATCH_CHECK_ADDRESS_SUPPORT', { addressList: state.address.list })
      .then((supportAddressMap) => {
        const { activeList, inactiveList } = state.address.list.reduce(
          (data, item) => {
            supportAddressMap[item.id] ? data.activeList.push(item) : data.inactiveList.push(item);
            return data;
          },
          {
            activeList: [],
            inactiveList: [],
          }
        );
        commit('SET_RETAIL_ADDRESS', {
          ...state.address,
          activeList,
          inactiveList,
        });
      });
  },
};
