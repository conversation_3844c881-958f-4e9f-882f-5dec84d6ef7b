import { tryLocation } from '@/helpers/lbs';
import { stringifyAddress } from 'utils/stringify-address';
import pick from '@youzan/weapp-utils/lib/pick';
import get from '@youzan/weapp-utils/lib/get';
import config from '@/packages/order-native/common/config';

const app = getApp();

// 格式化地址中的 distance 字段
function formatDistance(address) {
  return address.distance < 0
    ? ''
    : `${
        address.distance > 1000
          ? (address.distance / 1000).toFixed(2) + 'km'
          : address.distance + 'm'
      }`;
}

// 将 switchs 转化为 weekdays
const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
const switchsTransformer = (switchs) => {
  switchs = switchs.split('');
  return weekdays.filter((day, index) => switchs[index] === '1');
};

export const state = {
  // 自提
  selfFetch: {
    name: '',
    tel: '',
    time: '',
    timeWithWeekday: '',
    cityName: '',
    cityCode: '',
    address: {},
    shop: null,
    selected: null,
    aheadMax: {},
    aheadMin: {},
    isAllow: false,
    list: [],
    finished: false,
    page: 1,
  },
  // 设备定位
  location: {
    cityName: '',
    lat: '',
    lng: '',
    locating: false,
    located: false,
  },
};

export const getters = {
  // 自提点信息
  selfFetchShop(state) {
    const { selfFetch } = state.user.delivery;
    if (selfFetch) {
      return selfFetch;
    }

    const { shop } = state.selfFetch;

    if (shop) {
      return shop;
    }

    return {};
  },

  // 自提人
  selfFechContact(state, getters) {
    const { selfFetch } = state.user.delivery;
    if (selfFetch) {
      return `${selfFetch.appointmentPerson} ${selfFetch.appointmentTel}`;
    }

    const contact = getters.currentContact;
    if (contact.id) {
      return `${contact.userName} ${contact.telephone}`;
    }
    return '选择提货人';
  },

  // 自提详细地址
  selfFetchAddressDetail(state) {
    const { selfFetch } = state.user.delivery;
    if (selfFetch) {
      return `${selfFetch.name} ${stringifyAddress(selfFetch)}`;
    }

    const { shop } = state.selfFetch;
    if (!shop) {
      return '选择提货地址';
    }

    const distance = formatDistance(shop);
    return `${shop.name} ${stringifyAddress(shop)} ${
      distance ? '|' : ''
    } ${distance}`;
  },

  // 自提时间
  selfFetchTime(state, getters) {
    const { selfFetch } = state.user.delivery;
    if (selfFetch) {
      return selfFetch.appointmentTime;
    }

    const { time, shop } = state.selfFetch;
    if (time) {
      return time;
    }

    return !getters.canSelectSelfFetchTime && shop
      ? '请按约定时间上门自提'
      : '选择提货时间';
  },

  // FIXME: 自提时间，选完展示用，逻辑跟selfFetchTime有点重复
  selfFetchTimeWithWeekday(state, getters) {
    const { selfFetch } = state.user.delivery;
    if (selfFetch) {
      return selfFetch.appointmentTime;
    }

    const { time, shop, timeWithWeekday } = state.selfFetch;
    if (time && timeWithWeekday) {
      return timeWithWeekday || time;
    }

    return !getters.canSelectSelfFetchTime && shop
      ? '请按约定时间上门自提'
      : '请选择提货时间';
  },

  // 能否选择自提店铺
  canSelectSelfFetchAddress(state, getters) {
    return (
      (state.display.openSelfFetchSwitch || !getters.isMultiStore) &&
      !getters.orderCreated
    );
  },

  // 能否选择自提时间
  canSelectSelfFetchTime(state, getters) {
    const { shop } = state.selfFetch;
    return (
      (shop && shop.optionalSelfFetchTime && !getters.orderCreated) || false
    );
  },

  selfFetchList(state) {
    const { list } = state.selfFetch;
    return list;
  },
};

export const mutations = {
  SET_SELF_FETCH_CHOSEN_SHOP(state, shop) {
    state.selfFetch.shop = shop;
  },

  SET_SELF_FETCH_SHOP_CONFIG(state, payload) {
    Object.assign(state.selfFetch, payload);
  },

  RESET_SELF_FETCH_TIME(state) {
    Object.assign(state.selfFetch, {
      time: '',
      selfFetchStartTime: '',
      selfFetchEndTime: '',
    });
  },

  SET_SELF_FETCH_TIME(state, time = {}) {
    state.selfFetch.time = time.text;
    state.selfFetch.timeWithWeekday = time.textWithWeekday;
    state.selfFetch.selfFetchStartTime = time.startTime || '';
    state.selfFetch.selfFetchEndTime = time.endTime || '';
  },

  SET_SELF_FETCH_CITY(state, city) {
    state.selfFetch.cityName = city.cityName;
    state.selfFetch.cityCode = city.cityCode;
  },

  SET_LOCATE_CITY(state, location) {
    Object.assign(state.location, location);
  },

  SET_LOCATING(state, locating) {
    state.location.locating = locating;
  },

  SET_SELF_FETCH_TIME_CONFIG(state, config) {
    const timeBucket = config.timeBucket.map((item) => {
      return {
        openTime: item.startTime,
        closeTime: item.endTime,
        weekdays: switchsTransformer(item.switchs),
      };
    });

    Object.assign(state.selfFetch, {
      timeBucket,
      timeSpan: config.timeSpan,
      aheadMax: {
        value: config.aheadMax,
        type: config.aheadMaxType,
      },
      aheadMin: {
        value: config.aheadMin,
        type: config.aheadMinType,
      },
    });
  },
};

export const actions = {
  // 获取设备当前城市
  LOCATE_CITY({ dispatch, commit, state } = {}) {
    dispatch('LOADING', { title: '定位中...' });
    commit('SET_LOCATING', true);

    return new Promise((resolve, reject) => tryLocation(resolve, reject))
      .then((GPS) => {
        commit('SET_LOCATE_CITY', GPS);
        return app.request({
          origin: 'cashier',
          path: '/wsctrade/multistore/selfFetchPoint/getCity.json',
          method: 'GET',
          data: { lat: GPS.lat, lon: GPS.lng },
        });
      })
      .then((res) => {
        dispatch('HIDE_TOAST');
        commit('SET_LOCATING', false);
        commit('SET_LOCATE_CITY', { located: true, ...res });

        if (!state.selfFetch.cityCode) {
          commit('SET_SELF_FETCH_CITY', res);
        }
      })
      .catch(() => {
        dispatch('HIDE_TOAST');
        commit('SET_LOCATING', false);
      });
  },

  // 获取自提时间配置
  GET_SELF_FETCH_TIME({ state, commit } = {}) {
    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/v2/getSelfFetchTime.json',
        method: 'POST',
        data: {
          kdtId: state.selfFetch.shop.kdtId,
          offlineId: state.selfFetch.shop.id,
          fromApp: 'weapp',
        },
      })
      .then((data) => {
        commit('SET_SELF_FETCH_TIME_CONFIG', data);
      });
  },

  // 获取推荐自提网点地址
  FETCH_SELFETCH({ state, dispatch, commit } = {}) {
    const goods = [].concat(state.goods.list, state.goods.unavailable);

    dispatch('LOADING', { title: '正在获取提货地址' });

    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/v2/getSelfFetch.json',
        method: 'POST',
        data: {
          lat: +state.location.lat || -256,
          lng: +state.location.lng || -256,
          kdtId: state.shop.kdtId,
          storeId: state.order.seller.storeId,
          items: goods.map((item) => pick(item, ['goodsId', 'skuId', 'num'])),
        },
      })
      .then((shop = null) => {
        dispatch('HIDE_TOAST');
        commit('SET_SELF_FETCH_CHOSEN_SHOP', shop);
        // 如果需要选择自提时间，则获取自提时间配置
        if (shop.optionalSelfFetchTime) {
          dispatch('GET_SELF_FETCH_TIME');
        }
      })
      .catch(() => {
        dispatch('HIDE_TOAST');
      });
  },

  // 获取上次下单的自提点
  GET_DEFAULT_SELF_FETCH({ state, commit, dispatch } = {}) {
    const goods = [].concat(state.goods.list, state.goods.unavailable);
    const cityCode = get(state, 'selfFetch.cityCode');

    const data = {
      lat: +state.location.lat || -256,
      lng: +state.location.lng || -256,
      kdtId: state.shop.kdtId,
      firstOneFill: true, // 只有一个自提点默认选中
      storeId: get(state, 'order.seller.storeId'),
      items: goods.map((item) => pick(item, ['goodsId', 'skuId', 'num'])),
    };

    if (cityCode) {
      data.cityCode = cityCode;
    }

    dispatch('LOADING', { title: '正在获取提货地址' });

    return app
      .request({
        origin: 'cashier',
        method: 'POST',
        path: '/pay/wsctrade/order/buy/getDefaultSelfFetch.json',
        data,
      })
      .then((shop = null) => {
        dispatch('HIDE_TOAST');
        commit('SET_SELF_FETCH_CHOSEN_SHOP', shop);

        // 如果需要选择自提时间，则获取自提时间配置
        if (shop && shop.optionalSelfFetchTime) {
          dispatch('GET_SELF_FETCH_TIME');
        }
      })
      .catch(() => {
        dispatch('HIDE_TOAST');
      });
  },

  /**
   * 一体化完成所在城市定位，获取自提点，获取自提时间表等
   */
  INIT_SELF_FETCH({ dispatch }) {
    return dispatch('LOCATE_CITY')
      .then(() => dispatch('GET_AUTO_OR_DEFAULT_SELF_FETCH'))
      .catch(() => dispatch('GET_AUTO_OR_DEFAULT_SELF_FETCH'));
  },

  FETCH_MOCK_SELF_FETCH({ state, commit }) {
    return app
      .request({
        origin: 'h5',
        method: 'GET',
        path: '/wsctrade/scancodebuy/address/get-mock-self-fetch.json',
        data: {
          kdtId: state.shop.kdtId,
        },
      })
      .then((selfFetch) => {
        selfFetch && commit('SET_SELF_FETCH_CHOSEN_SHOP', selfFetch);
      })
      .catch(() => {
        // do-nothing
      });
  },

  /**
   * 获取默认自提点
   */
  GET_AUTO_OR_DEFAULT_SELF_FETCH({ dispatch, state }) {
    // 开启多网点时 自动选择当前网点
    if (state.order.seller.storeId) {
      return dispatch('FETCH_SELFETCH');
    }
    // 否则选中上次下单的自提点
    return dispatch('GET_DEFAULT_SELF_FETCH');
  },

  // 获取自提点列表
  FETCH_SHOP_LIST({ state, commit, dispatch } = {}, payload = {}) {
    const { goods, selfFetch, location, shop } = state;
    const { cityCode, list: selfFetchList, page } = selfFetch;
    const { lat, lng } = location;
    const items = [].concat(goods.list, goods.unavailable);
    const { keyword = '', pageSize = 20, getAll = false } = payload;
    const params = {
      kdtId: shop.kdtId,
      items: items.map((item) => ({
        goodsId: item.goodsId,
        skuId: item.skuId,
        num: item.num,
      })),
      keyword,
      page,
      pageSize,
    };

    if (cityCode) {
      params.cityCode = cityCode;
    }

    if (lat && lng) {
      params.lat = lat;
      params.lng = lng;
    }
    return app
      .request({
        origin: 'cashier',
        method: 'post',
        path: '/pay/wsctrade/order/buy/v2/getSelfFetchList.json',
        data: params,
      })
      .then(({ list = [] } = {}) => {
        const formattedList = list.map((item) => {
          if (item.distance > 1000) {
            item.distanceStr = (item.distance / 1000).toFixed(1) + 'km';
          } else if (item.distance > 100) {
            item.distanceStr = item.distance + 'm';
          } else if (item.distance < 100 && item.distance > 0) {
            item.distanceStr = '<100m';
          }
          const { shopStock } = config;
          if (shopStock.none === item.orderSoldStatus) {
            item.shopTag = '缺货';
          } else if (shopStock.part === item.orderSoldStatus) {
            item.shopTag = '部分缺货';
          }
          item.detail = item.province + item.city + item.area + item.address;
          return item;
        });
        let finished = false;
        if (list.length < pageSize) {
          finished = true;
        }

        commit('SET_SELF_FETCH_SHOP_CONFIG', {
          page: page + 1,
          finished,
          list: [...selfFetchList, ...formattedList],
        });

        if (getAll && !finished) {
          return dispatch('FETCH_SHOP_LIST', payload);
        }

        if (getAll && finished) {
          return [...selfFetchList, ...formattedList];
        }
      })
      .catch(() => {
        commit('SET_SELF_FETCH_SHOP_CONFIG', {
          finished: true,
        });
      });
  },
};
