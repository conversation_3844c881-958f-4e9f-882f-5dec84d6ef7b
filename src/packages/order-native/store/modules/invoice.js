import { config } from '../../invoice/constants';
import getBytesLength from '../../utils/get-bytes-length';
import invoiceDesign from '../../invoice/design.json';

export const state = {
  invoice: {
    themeGeneral: '',
    submit: {},
    common: {
      raiseType: config.default.raiseType,
      emailList: []
    },
    enterprise: {
      userName: '',
      taxpayerId: '',
      invoiceDetailType: config.default.invoiceDetailType
    },
    personal: {
      userName: '',
      invoiceDetailType: config.default.invoiceDetailType
    }
  },
  invoice_design: [],
};

export const getters = {
  showInvoiceCell(state, getters) {
    return state.tradeTag.showInvoiceCell && !getters.orderCreated;
  },

  invoiceCellValue(state, getters) {
    if (getters.hasHaitaoGoods) {
      return '海淘商品暂不支持开发票';
    }

    const { raiseType, userName } = state.invoice.submit;
    let value = '请填写开票信息';

    if (raiseType && userName) {
      const raiseTypeStr = raiseType === 'personal' ? '个人' : '企业';
      value = `${raiseTypeStr}-${userName}`;
    }
    return value;
  },

  isElectronic(state) {
    return state.tradeTag.useElectronicInvoice;
  },

  invoiceGoods(state) {
    const goods = {
      hasCode: [],
      noCode: []
    };

    state.goods.list.forEach((item) => {
      const type = item.taxClassCode ? 'hasCode' : 'noCode';
      goods[type].push({
        title: item.title
      });
    });
    return goods;
  },

  showInvoiceGoodsCell(state, getters) {
    return !!(getters.isElectronic && getters.invoiceGoods.noCode.length);
  },

  invoiceDetailType(state, getters) {
    return state.invoice[getters.raiseType].invoiceDetailType;
  },

  raiseType(state) {
    return state.invoice.common.raiseType;
  },

  userName(state, getters) {
    return state.invoice[getters.raiseType].userName;
  },

  emailList(state) {
    return state.invoice.common.emailList;
  },

  raiseTypeIndex(state) {
    switch (state.invoice.common.raiseType) {
      case config.raiseType.personal:
        return 1;
      case config.raiseType.enterprise:
      default:
        return 0;
    }
  },

  // 商品名大于 80 字节屏蔽 "商品明细" 选项
  detailDisabled(state, getters) {
    const goods = [...getters.invoiceGoods.hasCode, ...getters.invoiceGoods.noCode];
    return goods.some((item) => getBytesLength(item.title) > 80);
  }
};

export const mutations = {
  SET_INVOICE_THEME(state, color) {
    state.invoice.themeGeneral = color;
  },
  SET_INVOICE_SUBMIT(state, data) {
    state.invoice.submit = data;
  },
  ASSIGN_INVOICE_COMMON(state, data) {
    Object.assign(state.invoice.common, data);
  },
  ASSIGN_INVOICE_ENTERPRISE(state, data) {
    Object.assign(state.invoice.enterprise, data);
  },
  ASSIGN_INVOICE_PERSONAL(state, data) {
    Object.assign(state.invoice.personal, data);
  },
  SET_INVOICE_DESIGN(state, data) {
    state.invoice_design = data;
  },
};

export const actions = {
  GET_INVOICE_DESIGN({ commit }) {
    const modList = invoiceDesign.design;
    const config = modList[0] && modList[0].type === 'config' && modList[0];
    const newDesign = config ? modList.slice(1) : modList;
    commit('SET_INVOICE_DESIGN', newDesign);
  },
};
