import { stringifyAddress } from 'utils/stringify-address';
import { isEmptyObject } from '../utils';

const app = getApp();

export const state = {
  // 地址
  address: {
    id: '', // 选中收货地址的id
    list: [], // 备选收货地址列表
    activeTab: 0, // 当前选中的配送 / 自提方式
    visible: true, // 地址模块是否可见
    isSelfFetchDefault: false, // 是否默认自提
    abConfigExpressWay: {}, // 配送方式ab
  }
};

export const getters = {
  hasAddress(state, getters) {
    return !isEmptyObject(getters.currentAddress);
  },

  // 当前使用的收货地址
  currentAddress(state, getters) {
    const { address } = state.user.delivery;
    if (address) {
      address.userName = address.recipients;
      return address;
    }

    return getters.receiveByGroupHeader
      ? state.groupon.headerAddress
      : state.address.list.filter(item => item.id === state.address.id)[0] || {};
  },

  // 能否编辑地址
  addressEditable(state, getters) {
    return !getters.orderCreated && !getters.receiveByGroupHeader;
  },

  // 全地址
  fullAddress(state, getters) {
    return stringifyAddress(getters.currentAddress);
  },

  // 前地址
  siteTop(state, getters) {
    return stringifyAddress(getters.currentAddress, 4);
  },

  // 后地址
  siteBottom(state, getters) {
    return stringifyAddress(getters.currentAddress, 0, 4, ' ');
  },

  // 同城送 + 开启设置 => 编辑地址时需要强制poi选点
  forcePoiSelect(state, getters) {
    const { display, postage } = state;
    const { expressType } = getters;
    return expressType === 'express' && display.localDeliveryPosition && postage.currentExpressType === 2;
  },

  // 强制poi选点 + 非选点地址 + 未降级 => 需要提示去选点编辑地址
  showPoiPrompt(state, getters) {
    const { currentAddress, forcePoiSelect } = getters;
    return forcePoiSelect && currentAddress.type !== 2 && !currentAddress.downgrade;
  },
};

export const mutations = {
  SET_EXPRESS_WAP_TAB(state, activeTab) {
    state.address.activeTab = activeTab;
  },

  SET_ADDRESS_VISIBLE(state, visible) {
    state.address.visible = visible;
  },

  SET_ADDRESS_ID(state, id) {
    state.address.id = id;
  },

  ADD_ADDRESS(state, address) {
    if (address.id && state.address.list.some(item => item.id === address.id)) {
      return;
    }
    state.address.list.push(address);

    // 当前项设为默认项时 需要将列表中其它项设为非默认
    if (address.isDefault) {
      state.address.list
        .filter((item) => item.id !== address.id && item.isDefault)
        .forEach((item) => (item.isDefault = false));
    }
  },

  DELETE_ADDRESS(state, id) {
    state.address.list = state.address.list.filter(item => item.id !== id);
    if (id === state.address.id) {
      state.address.id = '';
    }
  },

  UPDATE_ADDRESS(state, address) {
    state.address.list.some((item, index) => {
      if (item.id === address.id) {
        state.address.list.splice(index, 1, address);
        return true;
      }
      return false;
    });

    // 当前项设为默认项时 需要将列表中其它项设为非默认
    if (address.isDefault) {
      state.address.list
        .filter((item) => item.id !== address.id && item.isDefault)
        .forEach((item) => (item.isDefault = false));
    }
  },

  SET_ADDRESS_LIST(state, list = []) {
    state.address.list = list;
  }
};

export const actions = {
  // 提示修改收货人信息
  NOTIFY_UPDATE_ADDRESS({ state, getters } = {}, content) {
    const { id, list } = state.address;
    const { forcePoiSelect } = getters;

    wx.showModal({
      title: '请填写收货人真实姓名',
      content,
      confirmText: '去修改',
      success(res) {
        if (res.confirm) {
          const dbid = app.db.set({ id, list, forcePoiSelect, delta: 1 });

          wx.navigateTo({
            url: `/packages/order-native/address-edit/index?dbid=${dbid}`
          });
        }
      }
    });
  }
};
