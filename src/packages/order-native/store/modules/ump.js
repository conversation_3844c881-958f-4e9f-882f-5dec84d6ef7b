import get from '@youzan/weapp-utils/lib/get';
import money from '@youzan/weapp-utils/lib/money';
import config from '@/packages/order-native/common/config';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import pick from '@youzan/weapp-utils/lib/pick';
import { getGoodDesc } from './goods';

const app = getApp();

export const state = {
  // 优惠券
  coupon: {
    chosenIndex: 0,
    list: [],
    disabledList: [],
    externalList: [],
    externalDisabledList: [],
    internalCouponId: '',
  },
  // 积分
  points: {
    usePoints: '',
    userPoints: '',
    totalPoints: '',
    available: '',
    externalPoint: '',
    externalId: '',
  },
  pointsName: '积分',
  // 拼团
  groupon: {
    isGroupon: false,
    isHeader: false,
    isChecked: false,
    receiveState: 1,
  },
  // 储值卡
  valueCard: {
    list: [],
    disabled: [],
    checked: [],
  },
  giveRechargeAmount: 0, // 储值卡待激活使用的余额
  // 储值推荐文案
  payAssetActivityTagDesc: '',
  // 储值推荐内容
  recommendDetaid: {
    desc: '',
    productInfos: [],
    templateNo: '',
  },
  // 会员优惠
  customerCards: {
    list: [],
    id: null,
  },

  unavailableCustomerCards: {
    list: [],
  },

  // 加价购
  plusBuy: {
    // 可换购的商品列表
    exchangeGoodsList: [],
    // 选中的加购商品
    selected: [],
  },
  // 赠品
  umpSendPromotionInfo: {
    // 赠品之优惠券
    coupons: [],
    // 赠品之优惠码
    couponsCode: [],
    // 赠品之赠品……
    presents: [],
    // 赠品之积分
    score: 0,
  },
  // 到店自提有礼
  storeGiftsInfo: {
    // 优惠券
    coupons: [],
    // 优惠码
    couponsCode: [],
    // 赠品
    presents: [],
    // 积分
    score: 0,
  },
  // 返储值金文案
  rebateMessageList: [],

  // 储值卡弹窗是否弹起
  showValueCard: false,
};

export const getters = {
  // 内购券
  fissionActivityValue(state) {
    const value = get(state.fissionActivity, 'fissionTicketNum', 0);
    if (!value) return 0;

    return `${value}张`;
  },
  // 积分抵现
  pointDesc(state) {
    const { pointsName, pointDeduction } = state;

    const { status, userPoint, minAmount, rate, costPoints } =
      pointDeduction || {};
    if (!status || !userPoint || !minAmount || !rate) {
      return '';
    }

    switch (status) {
      case 1:
        return `共${userPoint}${pointsName}，满${rate}${pointsName}可用`;
      case 2:
        return `可以用${pointsName}抵现的商品总额不足${minAmount / 100}元`;
      case 3:
        return costPoints > 0
          ? `使用${pointsName}抵现${Math.round(costPoints / rate)}元`
          : '';
      default:
        return '';
    }
  },

  // 选中的优惠券
  chosenCoupon(state, getters) {
    return (
      getters.allCoupons.find(
        (coupon) => coupon.id === state.coupon.chosenId
      ) || {}
    );
  },

  chosenCouponIndex(state, getters) {
    return getters.allCoupons.findIndex(
      (coupon) => coupon.id === state.coupon.chosenId
    );
  },

  // 是否有选中的礼品卡或储值卡，用来隐藏底部 有赞担保
  isChoosedCard(state) {
    return !!state.valueCard.checked.length;
  },

  // 按金额排序的优惠券列表
  allCoupons(state) {
    return [...state.coupon.list, ...state.coupon.externalList].map(
      (coupon) => {
        if (coupon.condition) {
          coupon.condition = coupon.condition.replace(/[,，]/gi, '\n');
        }
        return coupon;
      }
    );
  },

  // 禁用的优惠券
  disabledCoupons(state) {
    return [
      ...state.coupon.disabledList,
      ...state.coupon.externalDisabledList,
    ].map((coupon) => {
      if (coupon.condition) {
        coupon.condition = coupon.condition.replace(/[,，]/gi, '\n');
      }
      return coupon;
    });
  },

  // 是否由团长代收
  receiveByGroupHeader(state) {
    const { groupon } = state;
    return (
      groupon.isGroupon &&
      !groupon.isHeader &&
      (groupon.receiveState === config.groupon.forceReceive ||
        groupon.isChecked)
    );
  },

  // 可选择是否团长代收
  grouponOptionalReceive(state) {
    return state.groupon.receiveState === config.groupon.optionalReceive;
  },

  showGrouponHeader(state, getter) {
    return getter.showGrouponCell && state.groupon.isHeader;
  },

  // 是否显示团长代收栏
  showGrouponCell(state, getters) {
    return (
      getters.expressType === 'express' &&
      state.groupon.showAgencyReceive &&
      !(
        getters.orderCreated &&
        !state.groupon.isHeader &&
        !state.groupon.isChecked
      )
    );
  },

  // 店铺活动
  formatedActivities(state) {
    let activities = (state.shop && state.shop.activities) || [];

    // 会员包邮的条目已经包含在会员卡描述里了，所以不展示
    activities = activities.filter(
      (item) => item.type !== 'customerPostageFree'
    );

    let decrease = 0;
    activities.forEach((activity) => {
      decrease += activity.value;
      activity.priceStr = money(activity.value).toYuan();
    });
    const names = activities
      .slice(0, 3)
      .map((activity) => activity.title)
      .join('，');

    return {
      list: activities,
      decreaseStr: money(decrease).toYuan(),
      decrease,
      names,
    };
  },

  // 是否展示预付卡
  showPrepayCardCell(state, getters) {
    const noCheckedCards =
      getters.orderCreated &&
      !state.valueCard.checked.length &&
      !state.pay.valueCardPayPrice;

    return state.display.showPrePayCards && !noCheckedCards;
  },

  // 下单页返储值金条目
  storeCardCell(state) {
    const { cashBackList } = state.shop;
    // typeId 为 2 代表团购返现、typeId 为 203 代表订单返现，cashBackType 代表返现类型，为 2 的时候表示返储值金
    return cashBackList.find((item) => item.cashBackType === 2);
  },

  // 预付卡栏展示文案
  prepayCardCellValue(state, getters) {
    let res = {
      text: '',
      class: '',
    };

    const count = state.pay.payCardsShowUsableNum;

    if (getters.prepayCardDecrease) {
      res = {
        text: `-￥${(getters.prepayCardDecrease / 100).toFixed(2)}`,
        class: 'prepay-card-cell__value--red',
      };
    } else if (state.giveRechargeAmount > 0) {
      res = {
        text: `${(state.giveRechargeAmount / 100).toFixed(
          2
        )}元余额可用，立即开通`,
        class: 'prepay-card-cell__value--bold',
      };
    } else if (!count) {
      res = {
        text: '暂无可用',
        class: 'prepay-card-cell__value--black',
      };
    } else {
      res = {
        text: `${count}张可用`,
        class: 'prepay-card-cell__value--red',
      };
      // 存在互斥卡
      if (getters.hasExclusionCard) {
        res.text += '（部分卡不享受优惠价）';
      }
    }

    return res;
  },

  // 储值卡中是否存在互斥卡
  hasExclusionCard(state) {
    const checkedList = [...state.valueCard.checked];
    const cardList = [...state.valueCard.list];
    return checkedList.some((cardNo) => {
      const card = cardList.find((item) => item.summaryCardNo === cardNo) || {};
      return card.isExclusion;
    });
  },

  chosenCustomCard(state) {
    const { list, id } = state.customerCards;
    return list.find((item) => item.id === id);
  },

  hasCustomerCards(state) {
    return !!state.customerCards.list.length;
  },

  hasUnavailableCustomerCards(state) {
    return !!state.unavailableCustomerCards.list.length;
  },

  hasOneCustomerCards(state) {
    return state.customerCards.list.length === 1;
  },

  // 展示在单元格右侧的优惠卡
  // 只有一张卡时默认展示
  // 有多张卡时，选中后展示
  displayCustomerCard(state, getters) {
    if (getters.hasOneCustomerCards) {
      return state.customerCards.list[0];
    }

    return getters.chosenCustomCard || {};
  },

  membershipCellTitle(state, getters) {
    if (getters.displayCustomerCard.name) {
      return getters.displayCustomerCard.name;
    }

    if (state.display.openDisplayCard) {
      return getters.hasCustomerCards ? '不可与付费卡叠加使用' : '';
    }
    if (getters.hasCustomerCards) {
      return '有可使用的会员优惠';
    }
    if (getters.hasUnavailableCustomerCards) {
      return '暂无本店可用';
    }
    return '暂无会员优惠可用';
  },

  membershipSwitchChecked(state) {
    return !!state.customerCards.id;
  },

  plusBuyGoodsList(state) {
    return (state.plusBuy.exchangeGoodsList || []).map((item) => {
      const goods = state.goods.list.find(
        (v) => v.fromTmpAdded && item.goodsId === v.goodsId
      );

      if (!isEmpty(goods)) {
        return {
          ...item,
          desc: getGoodDesc(goods),
          price: (goods.payPrice / 100).toFixed(2),
          originalPrice: (goods.price / 100).toFixed(2),
          picture: cdnImage(goods.imgUrl, '!180x180.jpg'),
        };
      }

      const res = {
        ...item,
        price: (item.exchangePrice / 100).toFixed(2),
        originalPrice: (item.originalPrice / 100).toFixed(2),
      };

      try {
        res.picture = cdnImage(JSON.parse(item.picture)[0].url, '!180x180.jpg');
      } catch (err) {
        res.picture = '';
      }

      return res;
    });
  },

  // 返储值金条目
  storeMoneyCell(state) {
    const { cashBackList } = state.shop;
    // typeId 为 2 代表团购返现、typeId 为 203 代表订单返现
    // cashBackType 代表返现类型，为 2 的时候表示返储值金
    return cashBackList.find((item) => item.cashBackType === 2);
  },

  // 赠品列表
  giveawayList(state, getters) {
    const { coupons = [], couponsCode = [], presents = [], score = 0 } =
      state.umpSendPromotionInfo || {};
    const list = [];
    if (coupons.length) {
      list.push({
        title: '送优惠券',
        desc: coupons.map(
          (item) =>
            item.couponText ||
            `送${item.num}张${item.condition}${
              item.discount > 0 ? '打' : '减'
            }${item.valueDesc}${item.unitDesc}优惠券`
        ),
      });
    }
    if (presents.length) {
      list.push({
        title: '送赠品',
        desc: presents.map((item) => `送${item.title} ${item.num}份`),
        goodsList: presents,
      });
    }
    if (score > 0) {
      list.push({
        title: '送积分',
        desc: [`送${score}积分`],
      });
    }
    if (couponsCode.length) {
      list.push({
        title: '送优惠码',
        desc: couponsCode.map(
          (item) =>
            item.couponText ||
            `送${item.num}个${item.condition}${
              item.discount > 0 ? '打' : '减'
            }${item.valueDesc}${item.unitDesc}优惠码`
        ),
      });
    }
    if (getters.storeMoneyCell) {
      const { typeId } = getters.storeMoneyCell;
      list.push({
        /* eslint-disable */
        title:
          typeId === 2
            ? '团购返储值金'
            : typeId === 203
            ? '订单返储值金'
            : '返储值金', // 区分是团购还是订单
        desc: [
          `该笔订单最多可返现${(getters.storeMoneyCell.value / 100).toFixed(
            2
          )}元`,
        ],
        /* eslint-disable */
      });
    }
    return list;
  },
};

export const mutations = {
  ADD_COUPON(state, coupon) {
    state.coupon.list.push(coupon);
  },

  SET_CHOSEN_COUPON_ID(state, id) {
    state.coupon.chosenId = id;
  },

  SET_GROUPON_IS_CHECKED(state, checked) {
    state.groupon.isChecked = checked;
  },

  SWITCH_PREPAY_CARD_CHECK_STATUS(state, { card, cardType }) {
    const { checked } = state[cardType];
    const index = checked.indexOf(card.summaryCardNo);
    if (index !== -1) {
      // checked.splice(index, 1);
      state[cardType].checked = [
        ...checked.filter((cardNo) => cardNo !== card.summaryCardNo),
      ];
    } else {
      state[cardType].checked = [...checked, card.summaryCardNo];
    }
  },

  SET_PREPAY_CARD_CHECK_STATUS(state, { valueCard }) {
    // 设置 valueCard 的值
    state.valueCard = { ...valueCard };
  },

  RESET_PREPAY_CARD_CHECK_STATUS(state) {
    state.valueCard.checked = [];
  },

  SET_EXTERNAL_ASSET(state, asset) {
    state.coupon.externalList = asset.coupons;
    state.coupon.externalDisabledList = asset.disabledCoupons;
    state.points.externalPoint = asset.pointValue;
  },

  SET_EXTERNAL_ID(state, ids) {
    state.coupon.externalId = ids.couponId;
    state.points.externalId = ids.pointId;
  },

  EXTEND_POINTS(state, payload) {
    Object.assign(state.points, payload);
  },

  SET_CUSTOM_CARD(state, id) {
    state.customerCards.id = id;
  },

  // 设置加价购信息
  SET_PLUS_BUY(state, payload) {
    // NOTE: 过滤不可购买的商品 零售场景后端过滤不掉 shit
    const exchangeGoodsList = (payload.exchangeGoodsList || []).filter(
      (item) =>
        !state.goods.unavailable.some((v) => +v.goodsId === +item.goodsId)
    );
    const selected = (state.plusBuy.selected || []).filter((item) =>
      exchangeGoodsList.some(
        (v) =>
          v.goodsId === item.goodsId &&
          (v.exchangeSkuList || []).some((sku) => sku.skuId === item.skuId)
      )
    );

    state.plusBuy = {
      ...payload,
      exchangeGoodsList,
      selected,
    };
  },

  // 切换加价购商品
  SWITCH_PLUS_BUY_GOODS(state, payload) {
    let { selected = [] } = state.plusBuy;
    const { supportMulti } = state.plusBuy;

    const index = selected.findIndex(
      (item) => +payload.goodsId === +item.goodsId
    );
    if (index < 0) {
      if (!supportMulti) {
        selected = [];
      }

      selected.push(payload);
    } else {
      selected.splice(index, 1);
    }

    state.plusBuy.selected = selected;
  },

  SET_REBATE_MESSAGE_LIST(state, payload) {
    state.rebateMessageList = payload;
  },

  // NOTE: 积分抵现 设置使用积分数，目前仅提供有赞云使用
  SET_POINT_DEDUCTION_COST(state, costPoints) {
    state.pointDeduction.costPoints = costPoints;
  },

  // 唤起储值弹窗并转到充值页
  SET_SHOW_PRE_PAY_CARD_RECHARGE(state, payload) {
    state.showPrePayCardRecharge = payload;
  },

  // 设置是否禁用折扣
  SET_DISABLE_STORED_DISCOUNT(state, payload) {
    state.disableStoredDiscount = payload;
  },

  // 唤起储值卡弹窗
  SET_SHOW_VALUE_CARD(state, payload) {
    state.showValueCard = payload
  },
};

export const actions = {
  EXCHANGE_COUPON({ state, dispatch }, code) {
    // ump 后端需要一堆入参
    const params = {
      goods: state.goods.list.map((item, index) => {
        const result = {
          ...pick(item, [
            'skuId',
            'goodsId',
            'goodsType',
            'price',
            'payPrice',
            'num',
            'groupIds',
            'itemBizMarkCode',
          ]),
          // 适配多人拼团pro，加上 activityAlias（参团时候才有）参数
          ...pick(state.order.ump.activities[index] || {}, [
            'activityId',
            'activityType',
            'activityAlias',
          ]),
        };

        if (Array.isArray(item.deliverOptions) && item.deliverOptions.length) {
          result.deliverTime = item.deliverTimes;
          result.periodNum = item.deliverOptions.length;
        }

        return result;
      }),
      seller: state.order.seller,
      postage: state.pay.postage,
      userCouponCode: code.replace(/\s/g, ''),
      // 适配多人拼团pro，加上 ladderNum（开团时候才有）参数
      extendScene: {
        LADDER_NUM: get(state.order.config, 'ladderNum', 0).toString(),
      },
    };

    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/exchangeCoupon.json',
        method: 'POST',
        data: params,
      })
      .catch((error) => {
        dispatch('TOAST', error.msg || '兑换失败');
        throw error;
      });
  },
  // 获取外部券 & 积分
  FETCH_EXTERNAL_ASSET({ commit, state } = {}) {
    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/getAssetForOrder.json',
        method: 'GET',
        data: {
          bookKey: state.order.bookKey,
        },
      })
      .then((data) => {
        const { coupon, point } = data;
        const coupons = [];
        const disabledCoupons = [];
        const pointValue = (point && point.value) || 0;

        if (coupon && coupon.length) {
          coupon.forEach((item) => {
            item.outer = 1;
            item.startAt /= 1000;
            item.endAt /= 1000;
            (item.available ? coupons : disabledCoupons).push(item);
          });
        }

        commit('SET_EXTERNAL_ASSET', {
          coupons,
          disabledCoupons,
          pointValue,
        });
      });
  },

  // 获取返储值金的文案说明
  FETCH_REBATE_MESSAGE({ state, commit }) {
    const { cashBackList, activityType } = state.shop;
    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/rebateMessage.json',
        method: 'POST',
        data: {
          cashBackList,
          activityType,
        },
      })
      .then((data) => {
        commit('SET_REBATE_MESSAGE_LIST', data);
      });
  },

  // 核销外部券 & 积分
  USE_EXTERNAL_COUPON({ commit, state, getters }) {
    const params = {
      book_key: state.order.bookKey,
      coupon: {
        coupon_id: getters.chosenCoupon.id,
        external_type: getters.chosenCoupon.externalType,
      },
      point: {
        points: state.points.totalPoints,
      },
    };

    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/useAsset.json',
        method: 'POST',
        data: params,
      })
      .then((data) => {
        const { coupon, point } = data;
        commit('SET_EXTERNAL_ID', {
          couponId: get(coupon, 'internalCouponId', ''),
          pointId: get(point, 'externalPointId', ''),
        });
      });
  },

  USE_POINT_DEDUCTION({ commit, dispatch }, isUse) {
    // 先设置积分再请求接口，防止当第一张优惠券为兑换券时，重新请求 confirm 传 usePointDeduction 参数错误
    commit('SET_POINT_DEDUCTION_USED', isUse);
    dispatch('FETCH_SHOW', {
      isUsePointDeduction: isUse,
    });
  },

  // 获取加价购商品信息
  FETCH_PLUS_BUY({ state, commit }) {
    const isSelfFetch = getters.expressType === 'self-fetch';

    return app
      .request({
        origin: 'cashier',
        path: '/pay/wsctrade/order/buy/plusBuy.json',
        method: 'POST',
        data: {
          kdtId: get(state, 'order.seller.kdtId'),
          offlineId: get(state, 'order.seller.storeId'),
          activityId: get(state, 'display.plusBuyComponent.activityId', 0),
          expressTypeChoice: isSelfFetch ? 1 : state.postage.currentExpressType,
        },
      })
      .then((res) => {
        commit('SET_PLUS_BUY', res);
      });
  },

  // 跳转优惠券列表页
  NAVIGATE_COUPON_LIST({ state, getters }) {
    const {
      allCoupons = [],
      disabledCoupons = [],
      chosenCoupon,
      orderCreated,
    } = getters;

    if (orderCreated) {
      return;
    }
    const { pay = {}, order = {}, goods = {} } = state;
    const params = {
      charge_coupon: allCoupons.slice(),
      unavailable_coupon: disabledCoupons.slice(),
      selected_coupon: chosenCoupon,
      exchangeParams: {
        seller: order.seller,
        postage: pay.postage,
        goods: goods.list.map((item, index) => {
          const result = {
            ...pick(item, [
              'skuId',
              'goodsId',
              'goodsType',
              'price',
              'payPrice',
              'num',
              'groupIds',
            ]),
            // 适配多人拼团pro，加上 activityAlias（参团时候才有）参数
            ...pick(order.ump?.activities?.[index] || {}, [
              'activityId',
              'activityType',
              'activityAlias',
            ]),
          };

          if (
            Array.isArray(item.deliverOptions) &&
            item.deliverOptions.length
          ) {
            result.deliverTime = item.deliverTimes;
            result.periodNum = item.deliverOptions.length;
          }

          return result;
        }),
        // 适配多人拼团pro，加上 ladderNum（开团时候才有）参数
        ladderNum: order.config ? order.config.ladderNum : 0,
      },
    };

    const dbid = app.db.set({ ...params });
    wx.navigateTo({
      url: './coupon/index?dbid=' + dbid,
    });
  },

  // 储值卡弹窗开启
  OPEN_VALUE_CARD({ state, getters, commit }) {
    const { valueCard = {}, payAssetActivityTagDesc } = state;
    const { orderCreated } = getters;
    const clickable =
      payAssetActivityTagDesc ||
      (!orderCreated && !!(valueCard.list.length || valueCard.disabled.length));

    if (clickable) {
      commit('SET_SHOW_VALUE_CARD', true);
    }
  },
};
