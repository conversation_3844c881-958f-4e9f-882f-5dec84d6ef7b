import { mapState, mapGetters } from '@youzan/vanx';
import { VanxPage } from 'pages/common/wsc-page/index';
import get from '@youzan/weapp-utils/lib/get';
import store, { resetStore } from '@/packages/order-native/store/index';
import { getOrder, getPoints } from './utils/format';
import SaasPageConfig from './saas/page-config';
import { FROM_ENUM } from './constant';
import { checkIsChannels, checkHasStatusBar } from 'shared/utils/channel';
import getSystemInfo from 'shared/utils/browser/system-info';
import {
  MenuButtonBoundingClientRect,
  getHeight,
} from 'shared/utils/nav-config';

import { themeStyleTemplate } from './common/theme';
import { getThemeStyle } from 'shared/common/components/theme-provider/theme';
import { destroyPayService, initCashier, paySuccess } from './saas/process';
import api from '@goods/utils/api';
import {
  PAGE_TYPE,
  NAVIGATE_TYPE,
  navigateToRantaPage,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';

const app = getApp();
const extendPageConfig = ECLOUD_MODE ? SaasPageConfig : {};

const PRE_PAGE_LIST = [
  'packages/goods/cart/index',
  'packages/goods/detail/index',
  'pages/goods/detail/index',
  'packages/goods-v2/detail/index',
  'packages/goods-v3/detail/index',
  'packages/trade/cart/index',
  'packages/trade/cart-v1/index',
  'packages/trade-cart/cart/index',
];

VanxPage(extendPageConfig, {
  store,

  mapData: {
    ...mapState(['dataLoaded', 'shop', 'display', 'design', 'guarantee']),
    ...mapGetters([
      'isChoosedCard',
      'navbarShowBackGround',
      'orderCreated',
      'showTradeCarousel',
    ]),
  },

  computed: {
    ...mapState([
      'shop',
      'version',
      'display',
      'order',
      'orderKeepApply',
      'isSHowStayBuyerDialog',
      'guarantee',
    ]),
    ...mapGetters([
      'orderCreated',
      'isDisablePrepayCard',
      'chosenCoupon',
      'orderFinalPrice',
      'receiveByGroupHeader',
      'hasGoods',
      'orderKeepDispalyData',
    ]),
  },

  data: {
    theme: themeStyleTemplate,
    titleText: '',
    orderInitData: {},
    showOrderkeep: false,
    payResultSuccess: false,
    isInDrugWhiteList: false,
  },

  watch: {
    'data.navbarShowBackGround': function () {
      const { canUseNav } = MenuButtonBoundingClientRect;
      if (canUseNav) {
        wx.nextTick(() => {
          getThemeStyle().then((config) => {
            // 新下单页才展示主题色
            // 老下单页 展示白底导航栏，样式不一样
            if (this.data.navbarShowBackGround) {
              this.setYZData({
                navBackgroundColor: config[1],
                textColor: '#fff',
              });
            } else {
              this.setYZData({
                navBackgroundColor: '#fff',
              });
            }
          });
        });
      }
    },
    'data.dataLoaded': function (value) {
      if (value) {
        setTimeout(() => {
          // 来自商详页的领取优惠券展示
          // 文案目前是写死的
          wx.getStorage({
            key: 'goods2trade',
            success({ data }) {
              if (data && data.showCouponPop) {
                const { couponText } = data;
                wx.showToast({
                  title: couponText || '',
                  icon: 'none',
                  duration: 3000,
                });
              }
              wx.removeStorageSync('goods2trade');
            },
          });
        }, 200);
      }
    },
  },

  onLoad(query = {}) {
    const { tradeBuyPageType, tradePayPageType } = app.tradePageType || {};
    const isPay = !query.bookKey && query.orderNo;
    const shouldRedirectTradeBuy = tradeBuyPageType === 'RANTA' && !isPay;
    const shouldRedirectTradePay = tradePayPageType === 'RANTA' && isPay;
    if (app.ecloudMode && (shouldRedirectTradeBuy || shouldRedirectTradePay)) {
      try {
        const { log } = console;
        log(
          `%c 重要提醒 %c\n 旧版${
            !isPay ? '下单' : '待支付'
          }页即将失效 请将硬编码path: \n packages/order-native/index \n 调整为: packages/order/index %c`,
          'background:#ee0a24; color: #ffffff; font-size: 24px; padding: 1px; margin-bottom: 1px; border-radius: 3px;',
          'background:#fde6e9; color: #ee0a24; font-size: 24px; padding: 1px; border-radius: 3px;',
          'background:transparent'
        );
        app.logger.appError({
          name: '[weapp]-order-native',
          message: '大客还在跳转原生下单',
          detail: {
            isPay,
            query,
            kdtId: app.getKdtId(),
          },
        });
      } catch (e) {
        console.error(e);
      }
      navigateToRantaPage({
        pageType: isPay ? PAGE_TYPE.PAY : PAGE_TYPE.ORDER,
        type: NAVIGATE_TYPE.REDIRECT,
        query,
      });
      return;
    }

    const {
      dbid,
      orderNo,
      orderFrom = '',
      bookKey,
      addressId,
      prevBookKey = '',
      retailOrderScene = '',
      warehouseId = null,
      jointId = null,
    } = query;

    const { canUseNav } = MenuButtonBoundingClientRect;

    if (!get(query, 'redirectCount')) {
      query.redirectCount = 0;
    }
    this.$commit('SET_PAGE_QUERY', query);

    // 添加场景来源
    if (retailOrderScene) {
      wx.setStorageSync('retailOrderScene', FROM_ENUM[retailOrderScene]);
      this.$commit('SET_RETAIL_ORDER_SCENE', retailOrderScene);
      if (warehouseId) this.$commit('SET_WAREHOUSE_ID', warehouseId);
      if (jointId) this.$commit('SET_RETAIL_JOINT_ID', jointId);
    }

    if (canUseNav) {
      this.initNavBar();
    }

    let data = {};

    if (dbid) {
      data = app.db.get(dbid);
    } else if (bookKey) {
      data = { bookKey };
    } else {
      data = { order_no: orderNo, type: 'order' };
    }

    if (addressId) data.addressId = addressId;

    data.orderFrom = orderFrom;

    this.setYZData({
      orderInitData: {
        orderFrom,
        addressId,
        bookKey,
        prevBookKey,
        orderNo,
      },
      canUseNav,
    });

    this.$dispatch('GET_DESIGN');
    if (data.type === 'order') {
      // 有orderNo是支付页 请求showPay
      if (canUseNav) {
        this.setYZData({
          titleText: '待付款的订单',
        });
      } else {
        wx.setNavigationBarTitle({ title: '待付款的订单' });
      }

      this.$dispatch('FETCH_SHOW_PAY', { order_no: data.order_no }).then(() => {
        app.trigger('ecloud:order:loaded');
      });
    } else {
      if (canUseNav) {
        this.setYZData({
          titleText: '确认订单',
        });
      } else {
        wx.setNavigationBarTitle({ title: '确认订单' });
      }

      this.$dispatch('CHECK_WXVIDEO_SHARER_ORDER');
      this.handleShowInit(data);
    }

    // 初次获得数据后
    this.once('order:loaded', () => {
      // 团长代收再次请求confirm接口，避免物流计算错误
      if (this.receiveByGroupHeader) {
        this.$dispatch('FETCH_SHOW');
      }

      const { orderCreated, isDisablePrepayCard } = this;

      if (!orderCreated && isDisablePrepayCard) {
        this.$commit('RESET_PREPAY_CARD_CHECK_STATUS');
      }
    });

    // 支付成功后，当页面onUnload时就不该在弹框了，危
    this.once('event:pay:success', () => {
      this.setYZData({
        payResultSuccess: true,
      });
    });
  },

  onUnload() {
    destroyPayService();
    // 用户选择暂时放弃
    if (this.giveUp) {
      resetStore();
      return;
    }
    const orderKeepFlag = this.canOrderKeep();
    if (orderKeepFlag && this.orderKeepApply) {
      this.triggerOnUnloadOrderKeep();
    }
    resetStore();
  },

  /** 定制收银台的事件回调 start */
  initPay({ detail: listeners }) {
    initCashier(listeners);
  },
  onCashierNavi({ detail: destination }) {
    wx.navigateTo({
      url: `/pages/common/webview-page/index?src=${destination.url}&title=${destination.title}`,
    });
  },
  onPaySuccess() {
    app.trigger('event:pay:success');
    paySuccess();
  },
  /** 定制收银台的事件回调 end */

  // 下单页初始化逻辑
  handleShowInit(data) {
    // 如果来自外卖模板记录来源地址
    if (data.takeoutAlias) {
      this.$commit('SET_TAKEOUT_ALIAS', data.takeoutAlias);
    }
    // 自定义下单成功后跳转路径
    this.$commit('EXTEND_ENV', {
      successUrl: data.successUrl,
    });

    // 如果有bookKey走prepare，不需要做下面的数据初始化
    if (data.bookKey) {
      this.$dispatch('FETCH_SHOW_BY_BOOK_KEY', {
        bookKey: data.bookKey,
        addressId: data.addressId,
      }).then(() => {
        app.trigger('ecloud:order:loaded');

        // scene/check调用
        this.$dispatch('SCENE_CHECK').then(() => {
          // 检查是否交易组件3.0场景
          this.$dispatch('CHECK_USE_WXVIDEO_PAY_FLOW').then(() => {
            this.$dispatch('HANDLE_MUTATE_STATE_AFTER_FETCH_IN_WXVIDEO');
          });
        });

        // 默认选中商品兑换券时，请求一次 confirm 拆分商品
        if (this.chosenCoupon.groupType === 13) {
          this.$dispatch('FETCH_SHOW', {
            isInitCoupon: false,
          });
        }
      });
      return;
    }

    // 初始化state数据
    this.$commit(
      'EXTEND_ORDER_DATA',
      getOrder({
        goodsList: data.goods_list || [],
        activityAlias: data.activityAlias,
        orderFrom: data.orderFrom,
        giftInfo: data.giftInfo || {},
      })
    );

    getPoints(data.goods_list || []).then((points) => {
      this.$commit('EXTEND_POINTS', points);
    });

    // 初始化拼团相关数据
    this.$commit('EXTEND_GROUP_DATA', {
      isLuckDraw: !!data.isLuckDraw,
      isGroupon: !!data.isGroupon,
      isHeader: !!data.createGroupon,
    });

    const options = {};
    if (!isNaN(data.expressTypeChoice)) {
      const expressTypeChoice = +data.expressTypeChoice;
      options.expressTypeChoice = expressTypeChoice;
      this.$commit('SET_EXPRESS_WAP_TAB', expressTypeChoice === 1 ? 1 : 0);
    }
    // 请求 show 接口
    options.isFormDbId = true;
    this.$dispatch('FETCH_SHOW', options).then(() => {
      app.trigger('ecloud:order:loaded');
    });
  },

  // 初始化导航栏，开始计算位置
  initNavBar() {
    const systemInfo = getSystemInfo(false);
    const { statusBarHeight, windowWidth, fontSizeSetting } = systemInfo;
    const {
      top,
      height: menuButtonHeight,
      right,
      width,
    } = MenuButtonBoundingClientRect;
    let height = getHeight();
    const paddingLeft = windowWidth - right;
    let textWidth = windowWidth - width * 2 - paddingLeft * 4;
    let customIconStyle = `width: ${menuButtonHeight}px;height: ${menuButtonHeight}px;left: ${paddingLeft}px;top: ${top}px;`;

    // 视频号半屏改造适配
    if (checkIsChannels()) {
      if (checkHasStatusBar()) {
        customIconStyle = `width: 32px;height: 32px;left: 7px;top: ${
          statusBarHeight + 3
        }px;`;
        height = 40 + statusBarHeight;
      } else {
        customIconStyle = `width: 32px;height: 32px;left: 7px;top: 3px;`;
        height = 40;
      }
      textWidth = 173;
    }

    this.setYZData({
      height,
      paddingTop: statusBarHeight,
      fontSizeSetting,
      textWidth,
      customIconStyle,
    });
  },

  checkInWhiteList() {
    return api
      .get({
        url: '/wsctrade/drug/isInWhiteList.json',
      })
      .then((res = { value: false }) => {
        if (res.value) {
          this.setYZData({
            isInDrugWhiteList: true,
          });
        }
        return res.value;
      })
      .catch(() => {});
  },

  canOrderKeep() {
    const pages = getCurrentPages();
    const pageLength = pages.length;
    // 获取下单页的上一页
    const prePage = pages[pageLength - 2];
    const router = get(prePage, 'route', '');
    const disableCreateOrder = this.display.forbidPay || !this.hasGoods;
    return (
      PRE_PAGE_LIST.indexOf(router) > -1 &&
      !disableCreateOrder &&
      !this.data.payResultSuccess
    );
  },

  orderKeepComfirm() {
    // 用户自动选择在想想
    // 留在当前页
    this.setYZData({
      showOrderkeep: false,
    });
  },

  orderKeepGiveup() {
    // 用户自动选择在放弃支付
    // 跳转到上一页
    this.giveUp = true;
    this.setYZData({
      showOrderkeep: false,
    });
    setTimeout(() => {
      wx.navigateBack();
    }, 50);
  },

  triggerOnUnloadOrderKeep() {
    const { orderInitData } = this.data;
    // 防止两次请求
    if (this.loadingStayBuy) {
      return;
    }
    const { orderCreated } = this;
    const { bookKey, orderFrom, addressId, prevBookKey } = orderInitData;
    const orderNo = get(this.order, 'orderNos', [])[0];
    this.loadingStayBuy = true;
    this.$dispatch('ORDER_KEEP').then(() => {
      const { orderKeepDispalyData, isSHowStayBuyerDialog } = this;
      if (isSHowStayBuyerDialog) {
        const params = {
          displayData: {
            ...orderKeepDispalyData, // 订单优惠信息
            orderNo: orderNo || '', // 用于埋点
            orderCreated,
            price: this.orderFinalPrice, // 用于埋点
          },
        };
        params.orderData = {
          bookKey: bookKey || prevBookKey,
          addressId,
          orderFrom,
          orderNo,
        };
        app.trigger('order:leave:stop', params);
      }
      this.loadingStayBuy = false;
    });
  },

  triggerOrderKeepDialog() {
    const { orderCreated } = this;
    const orderNo = get(this.order, 'orderNos', [])[0];
    if (this.loadingStayBuy) {
      return;
    }
    this.loadingStayBuy = true;
    this.$dispatch('ORDER_KEEP')
      .then(() => {
        const { orderKeepDispalyData, isSHowStayBuyerDialog } = this;
        if (isSHowStayBuyerDialog) {
          this.setYZData({
            showOrderkeep: true,
            displayData: {
              ...orderKeepDispalyData,
              orderNo,
              orderCreated,
              price: this.orderFinalPrice,
            },
          });
        } else {
          // 不满足挽留规则的话
          // 设为true 是为了onUnload 不在请求挽留接口
          this.giveUp = true;
          wx.navigateBack();
        }
        this.loadingStayBuy = false;
      })
      .catch(() => {
        this.giveUp = true;
        wx.navigateBack();
      });
  },

  onBackTap() {
    const orderKeepFlag = this.canOrderKeep();
    // 满足下单挽留情况下，用户主动选择放弃 则不在展示
    if (orderKeepFlag && this.orderKeepApply) {
      this.triggerOrderKeepDialog();
    } else {
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateBack({
        fail: () => {
          // eslint-disable-next-line
          wx.reLaunch({
            url: '/pages/home/<USER>/index',
          });
        },
      });
    }
  },

  /**
   * @description 设置 formId
   * @param {Event} 上传事件 event
   */
  handleFormId(event) {
    const { activityType } = this.shop;
    const { weAppFormId } = this.$store.state.order.source;
    // 0元抽奖上报formId至公共formId池，不上报至交易
    if (
      !this.orderCreated &&
      !weAppFormId &&
      event.detail.formId &&
      activityType !== 23
    ) {
      this.$commit('SET_FORM_ID', event.detail.formId);
    }
  },
});
