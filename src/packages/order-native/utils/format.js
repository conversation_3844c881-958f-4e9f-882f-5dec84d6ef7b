import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import pick from '@youzan/weapp-utils/lib/pick';
import get from '@youzan/weapp-utils/lib/get';
import { orderCreation } from '../store/order-station';

const app = getApp();

export function getPlatform() {
  const platform = 'weapp';
  const orderMark = get(orderCreation, 'source.orderMark', '') || 'wx_shop';

  const retailOrderScene = wx.getStorageSync('retailOrderScene');
  if (retailOrderScene && +retailOrderScene === 1) {
    return {
      platform,
      orderMark: 'retail_minapp_shelf',
    };
  }

  return {
    platform,
    orderMark,
  };
}

export function getItemSources(itemSources) {
  const { from_source: fromSource, from_params: fromParams } = app.globalData;
  return itemSources.map((item) => {
    let bizTracePointExt = item.bizTracePointExt;
    try {
      if (fromSource || fromParams) {
        bizTracePointExt = JSON.stringify({
          ...JSON.parse(item.bizTracePointExt),
          from_params: fromParams,
          from_source: fromSource,
        });
      }
    } catch (e) {
      console.log(e);
    }

    return {
      bizTracePointExt,
      goodsId: item.goodsId,
      skuId: item.skuId,
      activityId: item.activityId,
      activityType: item.activityType,
      kdtSessionId: app.getSessionId(),
    };
  });
}

export function getPoints(goodsList) {
  // 是否是积分购买
  const usePoints = goodsList.some((good) => +good.activityType === 5);
  // 积分价格
  const totalPoints = goodsList
    .map((good) => good.pointsPrice * good.num)
    .reduce((prev, curr) => prev + curr, 0);

  if (!usePoints) {
    return Promise.resolve({
      usePoints,
      totalPoints,
    });
  }

  return app.getPoints().then((points) => ({
    available: points.current_points >= totalPoints,
    usePoints,
    totalPoints,
    userPoints: points.current_points,
  }));
}

export function getOrder({
  goodsList = [],
  activityAlias,
  orderFrom,
  giftInfo,
} = {}) {
  const kdtSessionId = app.getSessionId();
  const kdtId = app.getKdtId();
  const storeId = app.getOfflineId() || 0;

  const items = goodsList.map((item) => ({
    ...pick(item, [
      'goodsId',
      'skuId',
      'price',
      'num',
      'itemBizMark',
      'activityId',
      'activityType',
      'propertyIds',
    ]),
    deliverTime: item.deliverTime || 0,
    // 购物车过来传对象 : 商品详情过来传数组
    itemMessage: JSON.stringify(
      orderFrom === 'cart'
        ? item.message || {}
        : Object.keys(item.message || {}).map((key) => item.message[key])
    ),
    kdtId,
  }));

  const activities = goodsList.map((item) => ({
    ...pick(item, ['activityId', 'activityType', 'goodsId', 'skuId']),
    activityAlias: item.activityAlias || activityAlias,
    usePoints: +item.activityType === 5,
    kdtId,
  }));

  const bookKey = makeRandomString(10) + Date.now();

  const source = {
    bookKey,
    clientIp: '127.0.0.1',
    fromThirdApp: false,
    itemSources: getItemSources(goodsList),
    kdtSessionId,
    orderFrom,
    ...getPlatform(),
  };

  // NOTE: 兼容老的价格日志字段处理
  const delivery = {};
  if (app.globalData.priceCalendarDate) {
    delivery.contacts = {
      appointmentTime: app.globalData.priceCalendarDate,
    };
  }

  return {
    source,
    bookKey,
    items,
    seller: { kdtId, storeId },
    config: {
      usePoints: goodsList.some((good) => +good.activityType === 5),
      containsUnavailableItems: false,
      gift: giftInfo,
    },
    ump: { activities },
    delivery,
  };
}
