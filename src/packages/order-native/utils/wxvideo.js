import {
  checkBeforeAddOrderPromise,
  checkOrderSyncWechatPromiseWithCache,
} from 'shared/utils/channel';

const app = getApp();

// 创建订单
export const createOrder = (data) =>
  app.request({
    method: 'POST',
    path: '/wscwxvideo/trade-module/createOrder.json',
    data,
  });

// 获取支付参数
export const getPaymentParams = (data) =>
  app.request({
    path: '/wscwxvideo/trade-module/getPaymentParams.json',
    data,
  });

// 支付打标
export const updateOrderMultipleExt = (data) =>
  app.request({
    method: 'POST',
    path: '/wscwxvideo/trade-module/updateOrderMultipleExt.json',
    data,
  });

const formatPayParams = ({ payParams, wxOrderInfo, orderNo }) => {
  const { nonceStr, prepayId, signType, timeStamp, paySign } = payParams;
  return {
    nonceStr,
    package: prepayId,
    signType,
    timeStamp,
    paySign,
    orderNo,
    orderInfo: wxOrderInfo,
  };
};

export async function startWxVideoPay(prepayData, payService) {
  if (!wx.canIUse('requestOrderPayment')) {
    // TODO 文案未确认
    wx.showToast({ title: '请升级微信', icon: 'none' });
    return;
  }

  // 是否待支付页
  const isPayPage = payService?.store?.state?.pageQuery?.orderNo;

  const { orderNo, wxOrderInfo } = prepayData;

  if (isPayPage) {
    try {
      const isOrderSyncWechatInPayPage =
        await checkOrderSyncWechatPromiseWithCache(orderNo);
      // 待支付页 订单未同步过去 则会拉起交易组件3.0微信支付失败
      if (!isOrderSyncWechatInPayPage) {
        wx.showToast({ title: '网络异常，请重新下单', icon: 'none' });
        return;
      }
    } catch (e) {
      e?.msg && wx.showToast({ title: e.msg, icon: 'none' });
      // 日志上报
      app.logger.appError({
        name: 'yun-sdk-getUserInfo',
        level: 'info',
        message: 'unexpected error',
        detail: {
          error: e.toString(),
          orderNo,
        },
      });
      throw e;
    }
  }

  try {
    const { scene } =
      wx && wx.getEnterOptionsSync
        ? wx.getEnterOptionsSync() || {}
        : wx.getLaunchOptionsSync() || {};
    const { openId } = app.getToken() || {};

    // curOrderNo = orderNo;

    const { traceId } = await checkBeforeAddOrderPromise().catch(() => ({}));
    // 生成微信订单
    const { outOrderId } = await createOrder({
      orderNo,
      openId,
      scene: String(scene),
      traceId,
    });

    // 获取支付参数
    const payParams = await getPaymentParams({
      orderNo,
      openId,
      outOrderId: String(outOrderId),
    });

    // 组装payInfo参数
    const wxPayInfo = formatPayParams({ payParams, wxOrderInfo, orderNo });

    payService.requestOrderPayment(wxPayInfo);
  } catch (e) {
    e?.msg && wx.showToast({ title: e.msg, icon: 'none' });
    // 日志上报
    app.logger.appError({
      name: 'yun-sdk-getUserInfo',
      level: 'info',
      message: 'unexpected error',
      detail: {
        error: e.toString(),
        orderNo,
      },
    });
    throw e;
  }
}

export function wxVideoPaySuccess(orderNo) {
  updateOrderMultipleExt({ orderNo });
}
