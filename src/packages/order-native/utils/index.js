import { checkEduShop, checkRetailShop, checkWscShop } from '@youzan/utils-shop';

export function findZanPayBiz(shopMetaInfo) {
  if (checkEduShop(shopMetaInfo)) return 'edu_trade_order';
  if (checkRetailShop(shopMetaInfo)) return 'retail_trade_order';
  if (checkWscShop(shopMetaInfo)) return 'trade_order';
  return 'unknown';
}

export function uglifyMobile(mobile) {
  return `${mobile.substr(0, 3)}****${mobile.substr(7)}`;
}

export function checkZanPaySwitch(kdtId, config = {}, biz = 'trade_order') {
  const { list: whiteList = [] } = config;
  const threshhold = config[biz] || 0;
  return threshhold > (+kdtId % 100) || whiteList.indexOf(kdtId) > -1;
}
