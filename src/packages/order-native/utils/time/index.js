import getRange from './range';
import getMonths from './month';
import getMeals from './meal';
import getTimes from './time';
import getDays, { getDayOptions } from './day';
import { format } from './date';
import { checkTimeSpanOrderLimit } from './orderLimit';

export function getOptions(date, config, range) {
  switch (config.timeSpan) {
    case 'day':
      return getDayOptions(date, config);
    case 'meal':
      return getMeals(date, config, range);
    default:
      return getTimes(date, config, range);
  }
}

/** 初始day天的选项，当前序号为0 且 存在即时达，则加入即时达数据 */
export async function getOptionsWithInstantTimePoint({
  day,
  isFirstDay,
  config,
  range,
  checkOrderLimit = false,
}) {
  const { instantTimePoint } = config;
  const text = format.dateTime(new Date(instantTimePoint));
  const instantTimePointText = `尽快送达（预计 ${format.time(
    new Date(instantTimePoint)
  )}）`;

  // 是否需要检查时段限单
  const needCheckOrderLimit = checkOrderLimit && !day.disabled;
  const timeSpan = getOptions(new Date(day.id), config, range);
  // 检查时段限单
  const orderTimeSpan = await checkTimeSpanOrderLimit({
    timeSpan,
    config,
    needCheckOrderLimit,
  });

  // 即时达 & 第一时间段非disabled & 第一天
  if (
    orderTimeSpan?.length &&
    isFirstDay &&
    !orderTimeSpan[0].disabled &&
    instantTimePoint
  ) {
    return [
      {
        text: instantTimePointText,
        id: text,
        value: {
          text,
          textWithWeekday: instantTimePointText,
          instantTimePoint,
          startTime: null,
          endTime: null,
        },
      },
      ...orderTimeSpan,
    ];
  }

  return orderTimeSpan;
}

// 计算初始7天的选项
export async function getFirstFewDays(config, range, day) {
  if (!range) {
    return [];
  }

  let { min, max } = range;
  const oneDay = 24 * 60 * 60 * 1000;
  if (max.getTime() - min.getTime() > day * oneDay) {
    max = new Date(min.getTime() + day * oneDay);
  }

  const days = getDays(config, { min, max });

  for (let i = 0; i < days.length; i++) {
    const day = days[i];
    // eslint-disable-next-line no-await-in-loop
    day.children = await getOptionsWithInstantTimePoint({
      day,
      isFirstDay: i === 0,
      config,
      range,
      checkOrderLimit: i === 0,
    });
  }

  return days;
}

export function getConfig(config, range) {
  return getMonths(config, range);
}

export function getAvailableTime(type, state) {
  const config = state[type];
  const range = getRange(config, state);
  if (!range || range.max < range.min) {
    return null;
  }

  const days = getDays(config, range);
  const availableDay = (days || []).find((day) => !day.disabled && day.text);
  if (availableDay == null) {
    return null;
  }

  const times = getOptions(new Date(availableDay.id), config, range);

  // const times = await getOptionsWithInstantTimePoint({
  //   day: availableDay,
  //   config,
  //   range,
  //   checkOrderLimit: true,
  // });

  if (!Array.isArray(times) || !times.length) {
    return null;
  }

  return (times || []).find((time) => !time.disabled);
}

// 将 switchs 转化为 weekdays
export const switchsTransformer = (switchs) => {
  const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  switchs = switchs.split('');
  return weekdays.filter((day, index) => switchs[index] === '1');
};

// 填充 weekdays
export const fillWeekdays = (times) => {
  if (!Array.isArray(times)) {
    return times;
  }
  times.forEach((item) => {
    // 无需填充
    if (Array.isArray(item.weekdays)) {
      return item;
    }
    // 根据switchs字段转换
    if (item.switchs) {
      item.weekdays = switchsTransformer(item.switchs);
    }
    return item;
  });
  return times;
};

export { getRange, getMonths, getDays };
