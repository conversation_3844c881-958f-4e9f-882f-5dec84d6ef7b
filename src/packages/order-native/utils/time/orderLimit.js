import store from '@/packages/order-native/store/index';
import api from '@goods/utils/api';
import { format } from './date';
/**
 *
 * @param {*} param0
 * @returns
 */
export const queryTimeSpanOrderLimit = ({
  timeBucketList,
  offlineId,
  bizType,
  dispatchWarehouseId,
}) => {
  return api.post({
    url: '/wsctrade/order/queryOrderLimit',
    data: {
      timeBucketList,
      offlineId,
      bizType,
      dispatchWarehouseId,
    },
  });
};

/**
 * 数据处理
 * @param {*} timeSpan
 * @returns
 */
const beforeRequestFormatTimeSpanData = (
  timeSpan = [],
  config = {},
  activeTab
) => {
  return timeSpan.reduce((arr, item, index) => {
    const { value, disabled } = item;
    if (!disabled) {
      let { startTime, endTime } = value;
      // 特殊逻辑：自提-全天分片，传00:00-23:59
      if (config.timeSpan === 'day' && activeTab === 1) {
        startTime = format.date(new Date(startTime)) + ' 00:00:00';
        endTime = format.date(new Date(endTime)) + ' 23:59:00';
      }
      arr.push({
        startTime,
        endTime,
        index,
      });
    }
    return arr;
  }, []);
};

/**
 * 数据处理
 * @param {*} timeSpan
 * @returns
 */
const afterRequestFormatTimeSpanData = (timeSpan = [], resultTimeSpan = []) => {
  resultTimeSpan.forEach((item, index) => {
    const obj = timeSpan[index];
    obj.disabled = item.full;
    obj.remain = item.remain;
    if (item.full) {
      obj.text += '  已约满';
    }
  });
  return timeSpan;
};

// 时段限单 检查时间分片是否已预约满
export const checkTimeSpanOrderLimit = async ({
  timeSpan,
  config,
  needCheckOrderLimit,
}) => {
  // 无需检查场景
  if (!needCheckOrderLimit || !timeSpan.length) {
    return timeSpan;
  }

  const { address, extra, delivery, selfFetch } = store().state;
  let dispatchWarehouseId = 0;
  let { offlineId } = delivery;
  try {
    // 自提
    if (address.activeTab === 1) {
      dispatchWarehouseId = +selfFetch.shop.kdtId;
      offlineId = +selfFetch.shop.id;
    } else {
      dispatchWarehouseId = JSON.parse(extra.ATTR_DISPATCHER_WAREHOUSE_IDS)
        .response.assignLocalWarehouseIds[0];
    }
  } catch (error) {
    console.log('获取仓库id失败 ~ error', error);
    // wx.showToast({
    //   title: '获取仓库id失败',
    //   icon: 'none',
    //   duration: 2000,
    // });
    return timeSpan;
  }

  return queryTimeSpanOrderLimit({
    offlineId,
    bizType: address.activeTab,
    dispatchWarehouseId,
    timeBucketList: beforeRequestFormatTimeSpanData(
      timeSpan,
      config,
      address.activeTab
    ),
  })
    .then((res) => {
      return afterRequestFormatTimeSpanData(timeSpan, res);
    })
    .catch((err) => {
      console.log('查询时段限单异常', err);
      return timeSpan;
    });
};
