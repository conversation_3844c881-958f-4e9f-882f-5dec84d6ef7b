import { calc, format, compare } from './date';

export default function (date, config, range) {
  const arr = [];
  if (!date) {
    return arr;
  }

  let minTime = '00:00';
  if (range.min && compare.sameDay(range.min, date)) {
    const minDateTime = format.time(range.min);
    if (compare.time(minDateTime, minTime) === 1) {
      minTime = minDateTime;
    }
  }

  const businessTimes = calc.getBusinessTimesByTimeBucket(date, config.timeBucket);
  if (!businessTimes.length) {
    return arr;
  }

  const { timeSpan } = config;
  const hourToMinutes = {
    halfhour: 30,
    hour: 60,
    '2hour': 120,
  };
  const timeInterval = hourToMinutes[timeSpan] ?? hourToMinutes.hour;

  businessTimes.forEach(businessTime => {
    const { openTime, closeTime } = businessTime;
    let selectedTime = openTime;

    if (compare.time(openTime, closeTime) !== -1) {
      return;
    }

    while (compare.time(selectedTime, closeTime) === -1) {
      const beginTime = selectedTime;
      selectedTime = calc.addTime(selectedTime, timeInterval);
      const endTime = compare.time(selectedTime, closeTime) === 1 ? closeTime : selectedTime;
      // compare.time(endTime, minTime) < 0 的改动解决 https://jira.qima-inc.com/browse/CSWT-84570
      const disabled = compare.time(endTime, minTime) < 0 || (range.max && compare.date(date, range.max) === 1);
      const dateStr = format.date(date);

      const weekday = format.advancedWeekday(date);
      let text = `${format.monthDay(date)} ${beginTime}-${endTime}`;
      let textWithWeekday = `${format.monthDay(date, 'yy-dd')} (${weekday}) ${beginTime}-${endTime}`;
      if (!compare.isCurrentYear(date)) {
        text = date.getFullYear() + '年' + text;
        textWithWeekday = date.getFullYear() + '年' + textWithWeekday;
      }

      arr.push({
        id: text,
        text: `${beginTime}-${endTime}`,
        disabled,
        value: {
          text,
          textWithWeekday,
          startTime: dateStr + ' ' + beginTime + ':00',
          endTime: dateStr + ' ' + endTime + ':00'
        }
      });
    }
  });

  return arr.filter(time => !time.disabled);
}
