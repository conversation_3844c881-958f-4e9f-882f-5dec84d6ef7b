import { calc, format, compare } from './date';
import getDays from './day';

export default function getMonths(config, range) {
  const { min, max } = range || {};
  const times = [];

  if (!range || compare.date(min, max) === 1) {
    return times;
  }

  const date = calc.copy(min);
  do {
    const maxDate = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59);

    const children = getDays(config, {
      min: date,
      max: compare.date(max, maxDate) > 0 ? maxDate : max
    });

    times.push({
      text: format.yearMonth(date),
      disabled: children.every(day => day.disabled),
      id: date.getTime(),
      children
    });

    date.setDate(1);
    date.setMonth(date.getMonth() + 1);
    calc.resetTime(date);
  } while (compare.month(date, max) !== 1);

  return times;
}
