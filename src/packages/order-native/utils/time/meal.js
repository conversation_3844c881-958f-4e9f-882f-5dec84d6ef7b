import { calc, format, compare } from './date';

export const CONFIG = {
  morning: {
    text: '上午',
    startTime: '00:00:00',
    endTime: '11:59:00',
  },
  afternoon: {
    text: '下午',
    startTime: '12:00:00',
    endTime: '17:59:00',
  },
  evening: {
    text: '晚上',
    startTime: '18:00:00',
    endTime: '23:59:00',
  },
};

function getMeal(period, date, businessTimes, range) {
  const config = CONFIG[period];

  // 判断是否可选
  // 在营业时间内且当前时间没超过配送时段
  let isOpen = false;
  if (businessTimes.length) {
    businessTimes.forEach((businessTime) => {
      if (
        compare.time(businessTime.openTime, config.endTime) < 0 &&
        compare.time(businessTime.closeTime, config.startTime) > 0 &&
        (compare.date(date, range.min) === 1 ||
          compare.time(format.time(date), config.endTime) < 1)
      ) {
        isOpen = true;
      }
    });
  }

  const dateStr = format.date(date);
  const weekday = format.advancedWeekday(date);
  let text = `${format.monthDay(date)} ${config.text}`;
  let textWithWeekday = `${format.monthDay(date, 'yy-dd')} (${weekday}) ${
    config.text
  }`;
  if (!compare.isCurrentYear(date)) {
    text = date.getFullYear() + '年' + text;
    textWithWeekday = date.getFullYear() + '年' + textWithWeekday;
  }

  return {
    text: config.text,
    id: text,
    value: {
      text,
      textWithWeekday,
      startTime: dateStr + ' ' + config.startTime,
      endTime: dateStr + ' ' + config.endTime,
    },
    disabled: !isOpen,
  };
}

export default function (date, config, range) {
  if (!range) {
    return [];
  }

  const businessTimes = calc.getBusinessTimesByTimeBucket(
    date,
    config.timeBucket
  );

  // 是否有晚上营业的日子
  const hasEveningBusiness = config.timeBucket.some(
    (item) => compare.time(item.closeTime, CONFIG.evening.startTime) > 0
  );

  const children = [
    getMeal('morning', date, businessTimes, range),
    getMeal('afternoon', date, businessTimes, range),
  ];

  if (hasEveningBusiness) {
    children.push(getMeal('evening', date, businessTimes, range));
  }

  return children.filter((meal) => !meal.disabled);
}
