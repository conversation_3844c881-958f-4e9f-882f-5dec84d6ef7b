import { calc, format, compare } from './date';
import { CONFIG } from './meal';

// 当天是否禁用
function isDayDisabled(date, config, range) {
  const businessTime = calc.businessTime(date, config);

  // 不在营业日期直接返回 true
  if (!businessTime) {
    return true;
  }

  const { min, max } = range;

  if (config.timeSpan !== 'meal') {
    // 当天日期 早于min 或 晚于max
    return compare.day(date, min) === -1 || compare.day(date, max) === 1;
  }

  // 是否有晚上营业的日子
  const hasEveningBusiness = config.timeBucket.some(
    item => compare.time(item.closeTime, CONFIG.evening.startTime) > 0
  );

  const startTime = CONFIG.morning.startTime;
  const endTime = hasEveningBusiness ? CONFIG.evening.endTime : CONFIG.afternoon.endTime;

  return !(
    compare.time(businessTime.openTime, endTime) < 0
    && compare.time(businessTime.closeTime, startTime) > 0
    // 如果是可选的第一天，判断 date 是否早于当天最晚的营业时间
    && (compare.date(date, min) === 1 || compare.time(format.time(date), endTime) < 1)
  );
}

export function getDayOptions(date, config) {
  const businessTime = calc.businessTime(date, config) || {};

  const dateStr = format.date(date);
  let text = format.monthDay(date);
  const weekday = format.advancedWeekday(date);
  let textWithWeekday = `${format.monthDay(date, 'yy-dd')} (${weekday})`;
  if (!compare.isCurrentYear(date)) {
    text = date.getFullYear() + '年' + text;
    textWithWeekday = date.getFullYear() + '年' + textWithWeekday;
  }

  return [{
    id: text,
    text: '全天',
    value: {
      text,
      textWithWeekday,
      startTime: dateStr + ' ' + businessTime.openTime + ':00',
      endTime: dateStr + ' ' + businessTime.closeTime + ':00'
    }
  }];
}

export default function getDays(config, range) {
  const { min, max } = range;
  const times = [];

  if (!range || compare.date(min, max) === 1) {
    return times;
  }

  const date = calc.copy(min);
  do {
    const weekday = format.advancedWeekday(date);

    times.push({
      text: `${format.monthDay(date, 'yy-dd')}（${weekday}）`,
      id: date.getTime(),
      disabled: isDayDisabled(date, config, range),
      children: []
    });

    calc.plusDay(date);
    calc.resetTime(date);
  } while (compare.date(date, max) !== 1);

  return times;
}
