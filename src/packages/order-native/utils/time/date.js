import { fillWeekdays } from './index';

/**
 * Date 相关工具类
 */
export default function padZero(num) {
  return (num < 10 ? '0' : '') + num;
}

// 东八区 offset 为 -480
// 若用户处于其他时区，则需要对时间进行修正
const timezoneOffset = (new Date().getTimezoneOffset() + 480) * 60 * 1000;

export const WEEKDAYS = [
  '周日',
  '周一',
  '周二',
  '周三',
  '周四',
  '周五',
  '周六'
];

export const calc = {
  // 获取今天对应的 Date
  today(serverTime = Date.now()) {
    const now = new Date(serverTime);
    return new Date(now.getFullYear(), now.getMonth(), now.getDate());
  },

  // 拷贝 Date 对象
  copy(date) {
    return new Date(date.getTime());
  },

  // 获取当前时间
  time(serverTime) {
    return format.time(new Date(serverTime));
  },

  // 重置时间
  resetTime(date) {
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0);
    return date;
  },

  // 最大化时间
  maximizeDate(date) {
    date.setHours(23);
    date.setMinutes(59);
    date.setSeconds(59);
    return date;
  },

  // 增加时间
  // 10:30 + 30 = 11:00
  addTime(time, diff) {
    const pairs = time.split(':');
    let hour = +pairs[0];
    let minute = +pairs[1] + diff;

    if (minute >= 60) {
      hour += (minute / 60) | 0;
      minute %= 60;
    }
    return `${padZero(hour)}:${padZero(minute)}`;
  },

  // 获取 Date 对应的营业时间对象
  businessTime(date, config) {
    const { timeBucket: businessHours = [], timeSpan = '' } = config;
    const weekday = format.weekday(date);
    const fillBusinessHours = fillWeekdays(businessHours);
    let business;
    fillBusinessHours.forEach(item => {
      if (item.weekdays.indexOf(weekday) !== -1) {
        /**
         * 当时段细分等于"天"
         * 需要取最晚的配送时间传给后端，不一定是最后一个
         */
        if (!business) {
          business = item;
        } else if (timeSpan === 'day') {
          business = compare.time(item.closeTime, business.closeTime) > 0 ? item : business;
        }
      }
    });
    return business;
  },

  // 获取 TimeBucket 获取 Date 这天的营业时间范围
  getBusinessTimesByTimeBucket(date, timeBucket = []) {
    const weekday = format.weekday(date);
    const fillTimeBuckets = fillWeekdays(timeBucket);
    return fillTimeBuckets.filter(item => item.weekdays.indexOf(weekday) !== -1);
  },

  // 增加日期
  plusDay(date, offset = 1) {
    date.setDate(date.getDate() + offset);
    return date;
  },

  // 增加小时
  plusHour(date, offset = 1) {
    date.setHours(date.getHours() + offset);
    return date;
  },

  // 增加分钟
  plusMinute(date, offset = 1) {
    date.setMinutes(date.getMinutes() + offset);
    return date;
  }
};

export const compare = {
  // 判断两个 Date 是否为同一天
  sameDay(date1, date2) {
    const methods = ['getFullYear', 'getMonth', 'getDate'];
    return methods.every(method => date1[method]() === date2[method]());
  },

  // 比较 Date
  date(date1, date2) {
    date1 = +date1;
    date2 = +date2;

    if (date1 === date2) {
      return 0;
    }

    return date1 > date2 ? 1 : -1;
  },

  // 比较时间
  // 比如 10:30 < 12:00
  time(time1, time2) {
    time1 = +time1.slice(0, 5).replace(':', '');
    time2 = +time2.slice(0, 5).replace(':', '');

    if (time1 === time2) {
      return 0;
    }

    return time1 > time2 ? 1 : -1;
  },

  // 比较月份
  month(date1, date2) {
    const year1 = date1.getFullYear();
    const year2 = date2.getFullYear();
    const month1 = date1.getMonth();
    const month2 = date2.getMonth();

    if (year1 === year2) {
      if (month1 === month2) {
        return 0;
      }

      return month1 > month2 ? 1 : -1;
    }

    return year1 > year2 ? 1 : -1;
  },

  // 判断 Date 是否在营业时间内
  inBusiness(date, businessHours = []) {
    const weekday = format.weekday(date);
    return businessHours.some(item => item.weekdays.indexOf(weekday) !== -1);
  },

  // 判断 Date 是否为今天
  isToday(date) {
    return compare.sameDay(calc.today(), date);
  },

  // 判断 Date 是否为今年
  isCurrentYear(date) {
    return calc.today().getFullYear() === date.getFullYear();
  },

  // 返回较晚的时间
  maxDate(date1, date2) {
    return date2 > date1 ? date2 : date1;
  },

  // 比较日期大小
  day(date1, date2) {
    date1 = new Date(format.date(date1));
    date2 = new Date(format.date(date2));
    return compare.date(date1, date2);
  }
};

export const format = {
  padZero,

  // 时间戳转 Date
  timeSpanToDate(timeSpan) {
    return new Date(timeSpan + timezoneOffset);
  },

  // 根据 Date 返回年月日
  date(date, format = 'yyyy-mm-dd') {
    return format
      .replace('yyyy', date.getFullYear())
      .replace('mm', padZero(date.getMonth() + 1))
      .replace('dd', padZero(date.getDate()));
  },

  // 根据 Date 返回月日，如 '01月01日'
  monthDay(date, format = 'yy月dd日') {
    return format
      .replace('yy', padZero(date.getMonth() + 1))
      .replace('dd', padZero(date.getDate()));
  },

  // 根据 Date 返回年月，如 '2018年1月'
  yearMonth(date, format = 'yyyy年mm月') {
    return format
      .replace('yyyy', date.getFullYear())
      .replace('mm', date.getMonth() + 1);
  },

  // 根据 Date 返回时间，如 '10:00'
  time(date, format = 'hh:mm') {
    return format
      .replace('hh', padZero(date.getHours()))
      .replace('mm', padZero(date.getMinutes()))
      .replace('ss', padZero(date.getSeconds()));
  },

  dateTime(date, format = 'yyyy年mm月dd日 hh:mm:ss') {
    return format
      .replace('yyyy', date.getFullYear())
      .replace('mm', padZero(date.getMonth() + 1))
      .replace('dd', padZero(date.getDate()))
      .replace('hh', padZero(date.getHours()))
      .replace('mm', padZero(date.getMinutes()))
      .replace('ss', padZero(date.getSeconds()));
  },

  // 根据 Date 返回周几，如 '周一'
  weekday(date) {
    return WEEKDAYS[date.getDay()];
  },

  // 根据 Date 返回 今天/明天/后天/周几
  advancedWeekday(date) {
    const today = calc.today();
    const { sameDay } = compare;

    if (sameDay(today, date)) {
      return '今天';
    }

    if (sameDay(calc.plusDay(today), date)) {
      return '明天';
    }

    if (sameDay(calc.plusDay(today), date)) {
      return '后天';
    }

    return format.weekday(date);
  }
};
