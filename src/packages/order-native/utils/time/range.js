import { calc, format, compare } from './date';

// eslint-disable-next-line
function formatAheadTime({ type, value }, aheadType, state) {
  value = +value;
  let now = new Date(state.display.serverTime);

  switch (type) {
    case 'minute':
      calc.plusMinute(now, value);
      break;
    case 'hour':
      calc.plusHour(now, value);
      break;
    case 'day':
      // 计算最晚时间时，需要扣除今天
      if (aheadType === 'max' && value >= 1) {
        value--;
        calc.maximizeDate(now);
      }
      if (aheadType === 'min') {
        calc.resetTime(now);
      }
      calc.plusDay(now, value);
      break;
    case 'none':
      if (aheadType === 'max') {
        calc.maximizeDate(now);
      }
      break;
    default:
      break;
  }

  if (aheadType === 'min') {
    // 若商品有备货时间，则延迟最早配送时间
    if (state.goods.prepareTime) {
      now = new Date(now.getTime() + state.goods.prepareTime);
    }

    // 预售商品，最早配送时间不能早于预售时间
    const presaleStartTime = getPresaleStartTime(state);
    if (presaleStartTime) {
      now = compare.maxDate(presaleStartTime, now);
    }
  }

  return now;
}

// 获取所有商品中最晚的预售时间
function getPresaleStartTime(state) {
  const now = new Date(state.display.serverTime);
  let startTime;

  state.goods.list
    .filter(item => item.presale)
    .forEach(item => {
      let time;

      switch (item.presaleTimeType) {
        // 固定的预售发货时间
        case 0:
        default:
          time = new Date(item.presaleStartTime);
          break;
        // 付款 N 天后发货
        case 1:
          if (state.pay.multiPhase) {
            time = new Date(item.presaleBalanceDueStartTime);
          } else {
            time = calc.copy(now);
          }
          calc.plusDay(time, item.presaleStartTimeAfterPay + 1);
          break;
      }

      startTime = startTime ? compare.maxDate(time, startTime) : time;
    });

  return startTime;
}

/**
 * 获取可选的时间范围
 * 之前是前端计算，现在改成后端计算
 * Doc: https://doc.qima-inc.com/pages/viewpage.action?pageId=250686764
 * @良人 2020.11.11
 */
export default function getRange(config) {
  const { deliveryTimeBucket = {}, timeBucket } = config;
  const { startTime, endTime } = deliveryTimeBucket;

  // 后端计算Range，如果没有值就说明没有可选时间段
  const max = endTime && new Date(endTime);
  let min = startTime && new Date(startTime);

  if (!max || !min || !timeBucket) {
    return null;
  }

  // 判断最小日期是否在营业范围内，若不在范围内，则往后推延
  let available = false;
  while (!available && compare.date(min, max) !== 1) {
    const minDateTime = format.time(min);
    const businessTimes = calc.getBusinessTimesByTimeBucket(min, timeBucket);
    const isInBusinessTime = businessTimes.some(item => compare.time(minDateTime, item.closeTime) !== 1);

    if (isInBusinessTime) {
      available = true;
    } else {
      min = calc.plusDay(min);
      calc.resetTime(min);
    }
  }

  return { max, min };
}
