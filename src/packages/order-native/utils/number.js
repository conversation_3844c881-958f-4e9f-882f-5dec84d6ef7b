export function add(num1, num2) {
  if (
    (typeof num1 !== 'number' && typeof num1 !== 'string')
    || (typeof num2 !== 'number' && typeof num2 !== 'string')
  ) {
    return '';
  }

  const num1Digits = (num1.toString().split('.')[1] || '').length;
  const num2Digits = (num2.toString().split('.')[1] || '').length;
  const baseNum = Math.pow(10, Math.max(num1Digits, num2Digits));
  return (num1 * baseNum + num2 * baseNum) / baseNum;
}
