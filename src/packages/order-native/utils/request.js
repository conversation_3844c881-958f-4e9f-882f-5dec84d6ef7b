import { gcjToBaidu } from '@/helpers/lbs';
import authorize from '@/helpers/authorize';
import { ADDRESS_SOURCE } from './constants-address';

const app = getApp();
const DEFAULT_CONFIG = {
  skipKdtId: true,
  skipShopInfo: true,
};

// 查询经纬度
export function queryGeo(address) {
  return new Promise((resolve, reject) => {
    app.carmen({
      api: 'youzan.logistics.geo/1.0.0/get',
      data: {
        city: address.city,
        address: `${address.province}${address.city}${address.county}${address.addressDetail}`,
      },
      config: DEFAULT_CONFIG,
      method: 'GET',
      success(res) {
        const { lat, lng, formattedAddress } = res;
        const transformed = gcjToBaidu(lng, lat);
        // 高德坐标转百度坐标
        resolve({
          lat: transformed.lat,
          lon: transformed.lng,
          formattedAddress,
        });
      },
      fail: reject,
    });
  });
}

export function fetchAreaList() {
  return app.request({
    origin: 'cashier',
    method: 'GET',
    path: '/wsctrade/uic/address/getAllRegion.json',
    data: {
      showOversea: true,
    },
    config: DEFAULT_CONFIG,
  });
}

// 获取收货地址列表
export function fetchAddressList() {
  return app.request({
    origin: 'cashier',
    method: 'POST',
    path: '/wsctrade/uic/address/getAddressList.json',
    config: DEFAULT_CONFIG,
  });
}

export function parseAddress(payload) {
  return app.request({
    origin: 'cashier',
    method: 'POST',
    path: '/wsctrade/uic/address/parseAddress.json',
    data: payload,
    config: DEFAULT_CONFIG,
  });
}

export function deleteAddress(id) {
  return app.request({
    origin: 'cashier',
    method: 'POST',
    path: '/wsctrade/uic/address/deleteAddress.json',
    data: { addressId: id },
    config: DEFAULT_CONFIG,
  });
}

export function saveAddress(address) {
  const method = address.id ? 'update' : 'add';

  return app.request({
    origin: 'cashier',
    method: 'POST',
    path: `/wsctrade/uic/address/${method}Address.json`,
    data: address,
    config: DEFAULT_CONFIG,
  });
}

export function callWechatAddress() {
  return new Promise((resolve, reject) => {
    authorize('scope.address')
      .then(() => {
        // 微信地址报错，使用小程序的地址选择
        wx.chooseAddress({
          success: (res) => {
            const address = {
              addressDetail: String.prototype.trim.call(res.detailInfo || ''),
              areaCode: res.nationalCode,
              city: res.cityName,
              county: res.countyName,
              postalCode: res.postalCode,
              province: res.provinceName,
              tel: res.telNumber,
              userName: res.userName,
              source: ADDRESS_SOURCE.WECHAT,
            };

            queryGeo(address)
              .then((geo) => {
                resolve({ ...address, ...geo });
              })
              .catch(() => {
                resolve(address);
              });
          },
          fail: (res) => {
            // 用户取消地址选择
            if (
              res.errMsg === 'chooseAddress:cancel' ||
              res.errMsg === 'chooseAddress:fail cancel'
            ) {
              return;
            }

            reject(res);
          },
        });
      })
      .catch(reject);
  });
}

// 根据经纬度反查城市名
export function getCity(GPS) {
  return app.request({
    origin: 'cashier',
    path: '/wsctrade/multistore/selfFetchPoint/getCity.json',
    method: 'GET',
    data: { lat: GPS.lat, lon: GPS.lng },
  });
}

export function fetchCityList() {
  return app.request({
    origin: 'cashier',
    path: '/wsctrade/uic/address/getRegionByLevel.json',
    method: 'GET',
    data: { level: 2 },
  });
}

export function updateAddress(address) {
  return app
    .request({
      origin: 'cashier',
      path: '/wsctrade/uic/address/updateAddress.json',
      method: 'POST',
      data: address,
    })
    .then((response) => {
      address.id = address.id || response.value;
      return address;
    });
}

/**
 * 获取店铺配置
 * @param {Object} data
 * @returns
 */
export const getShopConfigs = (keys = []) => {
  return app.request({
    path: '/retail/h5/miniprogram/shop/getShopConfigs',
    method: 'POST',
    data: {
      keys,
    },
  });
};

/**
 * 获取交易设置
 * @param {Object} data
 * @returns
 */
export const queryTradeConfig = (key) => {
  return app.request({
    path: '/retail/h5/trade/queryTradeConfig',
    method: 'get',
    data: {
      key,
    },
  });
};
