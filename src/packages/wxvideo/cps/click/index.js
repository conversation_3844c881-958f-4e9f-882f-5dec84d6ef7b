import args from '@youzan/weapp-utils/lib/args';
import { setGrowthCookie } from '@youzan/cps-growth-params';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

import { decodeQuery } from './utils';
import * as Api from './api';

// cps中间跳转页，主要用于埋点上报
const app = getApp();

Page({
  onLoad(query) {
    const rawOptions = decodeQuery(query);
    const options = mapKeysCase.toCamelCase(rawOptions);

    const { s, yzkEx: rawYzkEx, z, e, pid, ...rest } = options;
    const yzkEx = z || rawYzkEx; // 三方可能将 yzkEx 信息塞到 z 参数中

    const logGlobalInfo = app.logger.getGlobal() || {};
    const userInfo = logGlobalInfo.user || {};
    const atrUuid = userInfo.uuid;

    // 短链接模式
    // 短链接参数格式 s=alias:code
    // alias是商品alias， code是短地址的值
    const l = s.split(':');
    const alias = l[0];
    const code = l[1];

    setGrowthCookie({
      atr_uuid: atrUuid,
      yzk_ex: yzkEx,
    });

    // 跳到原生页面
    Api.saveClickCode({ ...rest, code, alias, atrUuid, yzkEx, pid });
    const params = {
      alias,
      yzkEx,
      code,
      pid,
      fromMass: options.fromMass,
    };
    // 添加接口判断是否使用中间页, 是则继续跳转, 不是则直接跳转原生
    let baseUrl = '/pages/goods/detail/index';
    if (e !== undefined) {
      const version = e.split(':');
      const goToCPSCouponPage = +version[1] === 1;
      if (goToCPSCouponPage) {
        baseUrl = '/packages/wxvideo/cps/coupon/index';
      }
    }
    // 判断跳转中间页还是商详页
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx.redirectTo({
      url: args.add(baseUrl, params),
    });
  },
});
