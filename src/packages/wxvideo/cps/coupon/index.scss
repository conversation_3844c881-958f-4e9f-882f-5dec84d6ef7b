.coupon-container {
  width: 686rpx;
  height: 972rpx;
  background: #fff;
  margin: 32rpx auto 14rpx auto;
  position: relative;
  border-radius: 24rpx;
  .cover {
    width: 100%;
    height: 686rpx;
    position: relative;
    .nav {
      height: 88rpx;
      width: 100%;
      background: #fff;
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      image {
        width: 56rpx;
        height: 56rpx;
        margin: 16rpx 0rpx 16rpx 24rpx;
        border-radius: 50px;
      }
      &-text {
        height: 100%;
        width: 530rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 88rpx;
        margin-left: 16rpx;
        font-size: 16px;
      }
      &-arrow {
        position: relative;
        top: 30rpx;
      }
    }
    image {
      width: 686rpx;
      height: 686rpx;
      // width: 100%;
      // height: 500rpx;
    }
  }
  .content {
    width: 638rpx;
    margin: 0 auto;
    .title {
      width: 100%;
      height: 40rpx;
      font-size: 14px;
      margin-top: 16rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .price {
      // width: 100%;
      height: 50rpx;
      margin-top: 10rpx;
      display: flex;
      line-height: 50rpx;
      margin-bottom: 16rpx;
      // display: table-cell;
      vertical-align: bottom;

      &-gray {
        color: #969799;
        font-size: 12px;
      }
      .coupon-title {
        margin-right: 8rpx;
      }
      .coupon-symbol {
        font-size: 14px;
        color: #f44;
        margin-right: 8rpx;
      }
      .coupon-price {
        font-size: 18px;
        color: #f44;
        margin-right: 24rpx;
        position: relative;
        bottom: 3rpx;
      }
      .original-title {
        margin-right: 2rpx;
      }
      .original-price {
        margin-left: 2rpx;
        text-decoration: line-through;
      }
    }
  }
}
.toast {
  position: fixed;
  top: 332rpx;
  left: 50%;
  transform: translateX(-50%);
  background: #3b3733;
  width: 296rpx;
  height: 246rpx;
  border-radius: 8rpx;
  opacity: 0.88;
  color: #fff;
  text-align: center;
  &-icon {
    margin: 40rpx auto 24rpx auto;
    width: 72rpx;
    height: 72rpx;
    position: relative;
    &-circle {
      width: 100%;
    }
    &-fail {
      width: 8rpx;
      height: 40rpx;
      position: absolute;
      top: 50%;
      left: 40%;
      transform: translate(50%, -50%);
    }
    &-success {
      width: 44rpx;
      height: 30rpx;
      position: absolute;
      top: 50%;
      left: 16rpx;
      transform: translateY(-50%);
    }
  }
  &-main {
    font-size: 16px;
  }
  &-desc {
    font-size: 12px;
    margin-top: 14rpx;
  }
}
.toast-fail {
  width: 352rpx;
}

.recommend-title {
  padding: 13px 0 9px 0;
  font-size: 14px;
  color: #333;
  text-align: center;
}
