<view class="card-container {{classMap[status].container}}" bindtap="couponClick" wx:if="{{isShowCoupon}}">
  <view class="left" wx:if="{{status == 'orange'}}">
    <view class="price {{classMap[status].price}}">
      <view class="price-symbol">¥</view>
      <view class="price-price">{{info.couponAmount}}</view>
    </view>
    <view class="expire {{classMap[status].expire}}">
      <view class="expire-title">有效期:</view>
      <view class="expire-date">{{info.couponEffectiveDate}}</view>
    </view>
  </view>
   <view class="left" wx:else>
     <view class="error-tip">
       优惠券已失效/已抢完
     </view>
  </view>
  <view class="right {{classMap[status].rightText}}">{{rightTextMap[info.couponStatus]}}</view>
  <!-- 半圆*2 -->
  <view class="circle circle-top {{classMap[status].circleTop}}"></view>
  <view class="circle circle-bottom {{classMap[status].circleBottom}}"></view>
  <!-- 竖线 -->
  <view class="dashed {{classMap[status].dashed}}">
    <image src="{{classMap[status].dashedImg}}" mode="widthFix"></image>
  </view>
</view>
