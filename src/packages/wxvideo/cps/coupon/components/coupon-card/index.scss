.card-container-gray {
  border: 5rpx solid #e3e5e7;
  background: #f0f1f4;
}
.card-container {
  width: 638rpx;
  height: 130rpx;
  display: flex;
  border-radius: 20rpx;
  position: relative;
  .left {
    flex: 0 0 438rpx;
    margin-left: 24rpx;
    position: relative;
    // border-top: 1px solid black;
    // border-left: 1px solid black;
    // border-bottom: 1px solid black;

    .price {
      color: #fff;
      display: flex;
      line-height: 56rpx;
      margin-top: 16rpx;
      &-symbol {
        font-size: 16px;
        position: relative;
        top: 8rpx;
        margin-right: 8rpx;
      }
      &-price {
        font-size: 28px;
      }
    }
    .price-orange {
      color: #fff;
    }
    .price-gray {
      color: #c8c9cc;
    }
    .expire {
      display: flex;
      margin-top: 8rpx;
      font-size: 12px;
      &-title {
        margin-right: 12rpx;
      }
    }
    .expire-orange {
      color: #d8d8d8;
    }
    .expire-gray {
      color: #c8c9cc;
    }
    .error-tip {
      line-height: 130rpx;
      color: #c8c9cc;
      font-size: 18px;
      margin-left: 48rpx;
    }
  }

  .right {
    width: 176rpx;
    line-height: 130rpx;
    font-size: 16px;
    text-align: center;
    position: relative;
  }
  .right-orange {
    color: #fff;
  }
  .right-gray {
    color: #969799;
  }
  .circle {
    width: 28rpx;
    height: 16rpx;
    background: #fff;
    // background: lightblue;
    position: absolute;
    left: 448rpx;
    z-index: 10;
  }
  .circle-top {
    border-radius: 0 0 14px 14px;
    top: -2px;
    z-index: 100;
  }
  .circle-top-gray {
    border-bottom: 2px solid #e3e5e7;
    border-left: 2px solid #e3e5e7;
    border-right: 2px solid #e3e5e7;
  }
  .circle-bottom-gray {
    border-top: 2px solid #e3e5e7;
    border-left: 2px solid #e3e5e7;
    border-right: 2px solid #e3e5e7;
  }
  .circle-bottom {
    border-radius: 14px 14px 0 0;
    bottom: -2px;
    z-index: 100;
  }
  .dashed {
    position: absolute;
    top: 14rpx;
    left: 464rpx;

    image {
      width: 4rpx;
      height: 100rpx !important;
    }
  }
  .dashed-orange {
    left: 460rpx;
  }
}

.status-orange {
  background: linear-gradient(90deg, #f98435 0%, #f44 100%);
}

.status-gray {
  background: #f2f3f5;
}
