import WscComponent from 'pages/common/wsc-component';

WscComponent({
  properties: {
    info: {
      type: Object,
      value: {
        status: true,
      },
    },
  },
  data: {
    classMap: {
      orange: {
        circleTop: '',
        circleBottom: '',
        container: 'status-orange',
        expire: 'expire-orange',
        price: '',
        rightText: 'right-orange',
        dashedImg:
          'https://b.yzcdn.cn/public_files/50b2ff1a70258f48661c7d1936a3eebc.png',
        dashed: 'dashed-orange',
      },
      gray: {
        circleTop: 'circle-top-gray',
        circleBottom: 'circle-bottom-gray',
        container: 'card-container-gray',
        expire: 'expire-gray',
        price: 'price-gray',
        rightText: 'right-gray',
        dashedImg:
          'https://b.yzcdn.cn/public_files/d69e56b3e1f48accf26984891e9bd163.png',
        dashed: '',
      },
    },

    status: 'orange',
    isShowCoupon: false,
    rightTextMap: {
      1: '立即领券',
      2: '已领完',
      3: '已失效',
    },
  },
  ready() {
    // 根据状态展示不同样式

    const { couponStatus } = this.data.info;
    // 判断是否有优惠券
    const status = couponStatus === 1 ? 'orange' : 'gray';
    this.setData({ status, isShowCoupon: true });
  },
  methods: {
    couponClick() {
      this.triggerEvent('couponClick');
    },
  },
});
