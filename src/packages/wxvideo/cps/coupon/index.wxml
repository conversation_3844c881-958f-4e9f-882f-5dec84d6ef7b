<view style="background-color: #f2f3f5;padding-top: 1px;">
  <view class="coupon-container">
    <view class="cover">
      <view class="nav" bindtap="jumpToShopClick">
        <image src="{{info.kdtLogo}}" mode="aspectFill"></image>
        <view class="nav-text">{{info.kdtName}}</view>
        <view class="nav-arrow">
          <van-icon name="arrow" color="#D8D8D8" size="18px" />
        </view>
      </view>
      <view class="coupon-container-cover-img" bindtap="jumpToDetailClick">
        <image src="{{info.goodsMainPic}}" mode="aspectFill"></image>
      </view>
      <view></view>
    </view>
    <view class="content">
      <view class="title">{{info.goodsName}}</view>
      <view class="price">
        <view class="coupon-title  price-gray">券后</view>
        <view class="coupon-symbol">¥</view>
        <view class="coupon-price">{{info.goodsDiscountPrice}}</view>
        <view class="original-title  price-gray">价格</view>
        <view class="original-price  price-gray">¥ {{info.goodsPrice}}</view>
      </view>
      <view>
        <coupon-card info="{{info}}" wx:if="{{isShowCoupon}}" bind:couponClick="couponClick" />
      </view>
    </view>
  </view>

  <!--推荐商品-->
  <recommend goods-number="{{ 10 }}" load-more>
    <view class="recommend-title" slot="title">
      更多精选商品
    </view>
  </recommend>

  <view class="toast {{toastStatus == 'fail' ? 'toast-fail' : ''}}" wx:if="{{isShowToast}}">
    <view class="toast-icon">
      <image class="toast-icon-circle" src="{{toastStatusMap[toastStatus].src}}" mode="widthFix"></image>
    </view>
    <view class="toast-main">{{couponToastMsg}}</view>
    <view class="toast-desc">正在跳转到详情页...</view>
  </view>

  <van-toast id="van-toast" />
</view>