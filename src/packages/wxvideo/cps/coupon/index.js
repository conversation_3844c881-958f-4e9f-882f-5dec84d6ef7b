import money from '@youzan/weapp-utils/lib/money';
import args from '@youzan/weapp-utils/lib/args';
import fullfillImage from '@youzan/utils/url/fullfillImage';

import WscPage from 'pages/common/wsc-page/index';
import Api from './api/index.js';

const app = getApp();

WscPage({
  data: {
    info: {},
    isShowCoupon: false,
    isShowToast: false,
    couponToastMsg: '',
    query: {},
    toastStatusMap: {
      success: {
        src: 'https://b.yzcdn.cn/public_files/b2a949c08a4918c4e03c78058c8b4f02.png',
      },
      fail: {
        src: 'https://b.yzcdn.cn/public_files/bf076cc3428e16e570da3b1997a74f43.png',
      },
    },
    toastStatus: 'success',
  },

  onLoad(query) {
    const { code } = query;
    this.setData({ query });
    this.fetchCPSCoupon(code);
  },
  // 通过短链code查询cps优惠券信息
  fetchCPSCoupon(linkId) {
    Api.fetchCpsCouponByShortCode({ linkId }).then((res) => {
      const info = res;
      // 初始化金额
      info.goodsDiscountPrice = money(info.goodsDiscountPrice).toYuan();
      info.goodsPrice = money(info.goodsPrice).toYuan();
      // 设置图片尺寸
      info.goodsMainPic = fullfillImage(info.goodsMainPic, '!646x646.jpg');
      info.kdtLogo = fullfillImage(info.kdtLogo, '!56x56.jpg'); //

      const { couponId } = info;
      if (couponId === undefined) {
        this.setData({
          couponToastMsg: '优惠券已失效/已抢完',
          isShowToast: true,
          toastStatus: 'fail',
        });
      }
      this.setData({ info, isShowCoupon: true });
      if (info.couponStatus !== 1) {
        setTimeout(() => {
          this.jumpToDetail();
        }, 3000);
      }
    });
  },
  jumpToShopClick() {
    const { couponId } = this.data.info;
    if (couponId === undefined) {
      return;
    }
    const { kdtId } = this.data.info;
    app.logger &&
      app.logger.log({
        et: 'click', // 事件类型
        ei: 'cps_coupon_go_to_shop_home', // 事件标识
        en: '跳转店铺页面', // 事件名称
        pt: 'cpsCoupon', // 页面类型

        params: {
          kdtId,
        }, // 事件参数
      });
    // eslint-disable-next-line @youzan/dmc/wx-check
    // wx.reLaunch({
    //   url: '/packages/home/<USER>/index',
    // });
  },
  jumpToDetail() {
    const { query } = this.data;
    const url = args.add('/pages/goods/detail/index', { ...query });
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx.redirectTo({ url });
  },
  jumpToDetailClick() {
    const { couponId } = this.data.info;
    if (couponId === undefined) {
      return;
    }

    this.goodDetailLog();
    this.getCoupon();
  },
  goodDetailLog() {
    const { goodsAlias, couponId } = this.data.info;
    app.logger &&
      app.logger.log({
        et: 'click', // 事件类型
        ei: 'cps_coupon_goods_cover', // 事件标识
        en: '商品主图点击次数', // 事件名称
        pt: 'cpsCoupon', // 页面类型
        params: {
          alias: goodsAlias,
          couponId,
        },
      });
  },

  couponButtonLog() {
    const { couponId, goodsAlias } = this.data.info;
    app.logger &&
      app.logger.log({
        et: 'click', // 事件类型
        ei: 'cps_coupon_button', // 事件标识
        en: '领券按钮点击次数', // 事件名称
        pt: 'cpsCoupon', // 页面类型
        params: {
          couponId,
          alias: goodsAlias,
        }, // 事件参数
      });
  },

  getCoupon() {
    const { couponStatus, kdtId, couponId, goodsAlias, atrPs, promotionId } =
      this.data.info;
    if (couponStatus === 1) {
      Api.collectCpsCoupon({
        atrPs,
        couponId,
        goodsAlias,
        kdtId,
        promotionId,
      })
        .then(() => {
          this.setData({
            couponToastMsg: '领取成功',
            isShowToast: true,
          });

          setTimeout(() => {
            this.jumpToDetail();
          }, 1500);
        })
        .catch((err) => {
          if (+err.code === 100011) {
            this.setData({ couponToastMsg: '领取已达上限', isShowToast: true });
          } else {
            this.setData({
              couponToastMsg: '优惠券领取失败',
              isShowToast: true,
            });
          }
          setTimeout(() => {
            this.jumpToDetail();
          }, 1500);
        });
      // 领取优惠券 跳转
    }

    // 不希望用户再回到这个页面
  },

  couponClick() {
    const { couponId } = this.data.info;
    if (couponId === undefined) {
      return;
    }
    this.couponButtonLog();
    this.getCoupon();
  },
});
