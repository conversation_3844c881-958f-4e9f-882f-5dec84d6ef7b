import { getFooterData } from './api';

const app = getApp();

const SdkBtnTitleMap = {
  xhs: '返回小红书',
};
Page({
  data: {
    showLaunchAppBtn: false,
    type: false,
  },
  onShow() {
    wx.hideHomeButton();
  },
  onLoad(query = {}) {
    const { downgradeType, type, source = '' } = query;
    const { scene } = wx.getLaunchOptionsSync();
    app.logger &&
      app.logger.log({
        et: 'click',
        ei: 'wxh5-downgrade',
        en: '支付兜底页面曝光',
        params: {
          scene,
          downgradeType,
          source,
        },
      });
    this.setData({
      type: type === 'success',
      showLaunchAppBtn: downgradeType === 'sdk',
      sdkBtnTitle: SdkBtnTitleMap[source] || '返回商家',
    });
    const kdtId = app.getKdtId();
    getFooterData(kdtId).then((footerInfo) => {
      this.setData({
        logoUrl:
          footerInfo?.logoUrl ||
          'https://img.yzcdn.cn/public_files/2018/12/19/5941d128d3d3e8a4748c7c9b95bea409.png',
        logoText: footerInfo?.logoUrl ? null : '有赞提供技术支持',
      });
    });
  },
  toPageHome() {
    // 返回小程序主页
    wx.reLaunch({
      url: '/packages/home/<USER>/index',
    });
  },
});
