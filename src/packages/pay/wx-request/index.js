// 微信支付独立页
import Event from '@youzan/weapp-utils/lib/event';
import args from '@youzan/weapp-utils/lib/args';
import openWebView from 'shared/utils/open-web-view';
import API from '../../paidcontent/api';

const app = getApp();

/**
 * payData: {
 *   // 业务扩展参数
 *   orderNo: '',
 *   phase: '', // 阶段单
 *   goToResult: true, // 支付后往下跳
 *   returnUrl: '', // 优先webview打开h5链接
 *   weappReturnUrl: '', // 其次使用传递的小程序路径地址或默认支付结果页地址
 *   // 以下5个是支付参数 必填
 *   timeStamp: '',
 *   nonceStr: '',
 *   package: '',
 *   signType: '',
 *   paySign: ''
 * }
 */
const page = {
  onLoad(query = {}) {
    const payData = { ...query };
    Object.keys(payData).forEach(item => {
      payData[item] = decodeURIComponent(payData[item]);
    });
    const { orderNo, phase = '' } = payData;
    if (orderNo) {
      Event.trigger('buy-h5:pay:call', {
        orderNo: payData.orderNo
      });
    }
    wx.requestPayment({
      timeStamp: payData.timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType,
      paySign: payData.paySign,
      success: () => {
        // callback(res);
        wx.showToast({
          title: '支付成功',
          duration: 2000
        });

        if (payData.goToResult) {
          if (payData.weappUrl) {
            wx.redirectTo({ url: payData.weappUrl });
          } else if (payData.url || payData.returnUrl) {
            openWebView(payData.url || payData.returnUrl, { title: '支付成功', method: 'redirectTo' });
          } else {
            // 支付成功就直接跳转到支付成功页
            const orderPaidUrl = payData.weappReturnUrl
              || args.add('/packages/order/paid/index', {
                order_no: orderNo,
                phase
              });

            if (payData.pageType === 'live') { // 直播支持一次性消息订阅
              API.showSubMsgPopup('21owlLive')
                .then(() => {
                  wx.redirectTo({
                    url: orderPaidUrl
                  });
                });
            } else {
              wx.redirectTo({
                url: orderPaidUrl
              });
            }
          }
        } else {
          // 爱逛这边支付成功是需要返回的
          setTimeout(() => {
            wx.navigateBack({
              delta: 1
            });
          }, 2000);
        }
      },
      fail: res => {
        let errMsg = res.errMsg || '支付失败，请稍后再试';
        const appLaunchInfo = __wxConfig ? __wxConfig.appLaunchInfo || {} : {};

        // 非主动取消行为
        if (errMsg !== 'requestPayment:fail cancel') {
          app.logger.appError({
            name: 'wx_pay_error',
            message: errMsg,
            detail: { ...res, ...appLaunchInfo }
          });

          if (errMsg.indexOf('fail jsapi has no permission') > 0) {
            errMsg = '受微信政策限制，请前往店铺公众号或H5进行购买';
          }

          wx.showToast({
            title: errMsg,
            icon: 'none',
            duration: 2000
          });
          setTimeout(() => {
            wx.navigateBack({
              delta: 1
            });
          }, 2000);
        } else {
          wx.navigateBack({
            delta: 1
          });
        }
      }
    });
  }
};

Page(page);
