import request from '@youzan/tee-biz-request';
import {
  navigateToRantaPage,
  NAVIGATE_TYPE,
  PAGE_TYPE,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import qs from '@youzan/utils/url/queryString';

Page({
  data: {
    initialize: true,
    params: {},
  },
  onShow() {
    const { initialize, params } = this.data;
    const { outBizNo: orderNo, prepayId, cashierSalt, cashierSign } = params;
    if (initialize) {
      return;
    }
    request({
      path: '/wscassets/bank-transfer-large/check-transfer-status',
      method: 'GET',
      data: {
        prepayId,
        cashierSalt,
        cashierSign,
      },
    })
      .then((res) => {
        // 支付成功
        if (['BUYER_PAIED', 'SUCCESS'].includes(res.acquireQueryStatus)) {
          return wx.redirectTo({
            url: `/packages/order/paid/index?orderNo=${orderNo}`,
          });
        }
        // 待支付重定向至支付页
        return navigateToRantaPage({
          pageType: PAGE_TYPE.PAY,
          type: NAVIGATE_TYPE.REDIRECT,
          query: {
            orderNo,
          },
        });
      })
      .catch(() => {
        // 兜底是去订单详情
        wx.redirectTo({
          url: `/packages/trade/order-detail/index?order_no=${orderNo}`,
        });
      });
  },
  async onLoad(query = {}) {
    const { url = '' } = query;

    if (url) {
      try {
        const paramsStr = decodeURIComponent(url).split('?')[1];
        const params = qs.parse(paramsStr);
        this.data.params = params;
        setTimeout(() => {
          this.data.initialize = false;
          wx.navigateTo({
            url: `/pages/common/webview-page/index?src=${url}`,
          });
        }, 200);
      } catch (e) {
        Toast('网络异常，请返回重试');
      }
    } else {
      Toast('网络异常，请返回重试');
    }
  },
});
