import args from '@youzan/weapp-utils/lib/args';
import Dialog from '@vant/weapp/dist/dialog/dialog';

const app = getApp();

Page({
  data: {
    downgradeType: 'url-link',
    orderNo: '',
    source: '',
  },
  onShow() {
    wx.hideHomeButton();
  },
  async onLoad(query = {}) {
    wx.showToast({
      title: '安全支付中',
      icon: 'loading',
    });
    const {
      orderNo,
      prepayId,
      cashierSign,
      cashierSalt,
      partnerId,
      biz = 'sample_order',
      downgradeType = 'url-link',
      source = '',
    } = query;
    this.downgradeType = downgradeType;
    this.orderNo = orderNo;
    this.source = source;
    this.setData({
      downgradeType,
      orderNo,
    });
    const crypto = await import('@youzan/crypto');
    const { default: ZanPay } = await import('@youzan/zan-pay-core');
    const zanPay = new ZanPay({
      toast: wx.showToast,
      clear: wx.hideToast,
      request: app.request,
      // 环境标记
      env: 'weapp',
      // 业务标记
      biz,
      // 收银台类型 直接掉dopay这里不关心
      type: 'B',
    });
    const cashierParams = {
      partnerId,
      prepayId,
      cashierSign,
      cashierSalt,
      /** 给zan-pay用的 */
      orderNo,
    };
    zanPay.init({}, crypto);
    zanPay.updateContext({
      keyName: 'env',
      value: 'weapp',
    });
    zanPay.updatePayContext(cashierParams);
    zanPay.doPay('WX_APPLET').catch(() => {
      // 取消支付去待支付页面
      this.payCallBack('fail');
    });
    zanPay.EE.on('pay-success', () => this.payCallBack('success'));
    zanPay.EE.on('cashier-fail', () => this.payCallBack('fail'));
    zanPay.EE.on('show-dialog', (options) => {
      Dialog.confirm(options)
        .then(() => {
          zanPay.EE.emit('exception-confirm');
        })
        .catch(() => {
          zanPay.EE.emit('exception-cancel');
        });
    });
  },
  payCallBack(type) {
    wx.redirectTo({
      url: args.add('/packages/pay/wxh5-downgrade-result/index', {
        orderNo: this.orderNo,
        downgradeType: this.downgradeType,
        type,
        source: this.source,
      }),
    });
  },
});
