const app = getApp();
let envVersion = 'release';
try {
  envVersion = __wxConfig.envVersion;
} catch {}

function showModal(options) {
  return new Promise((resolve) => {
    wx.showModal({
      ...options,
      success: resolve,
    });
  });
}

function navigateToMiniProgram(options) {
  return new Promise((resolve, reject) => {
    wx.navigateToMiniProgram({
      ...options,
      success: resolve,
      fail: reject,
    });
  });
}

Page({
  async onLoad(query) {
    const appId = query.appId || 'wx7138c43cf7b11573';
    delete query.appId;
    await wx.showModal({
      title: '提示',
      content: '正在前往有赞支付...',
      showCancel: false,
    });
    try {
      await navigateToMiniProgram({
        appId,
        envVersion,
        path: '/pages/cashier/index' + stringify(query),
      });
      /** 成功跳转到有赞支付，开始监听回切 */
      function cb(options = {}) {
        wx.offAppShow(cb);
        const {
          referrerInfo: {
            appId: referrerAppId,
            extraData: { type, partnerReturnUrl } = {},
          },
        } = options;
        if (referrerAppId === appId && type === 'success') {
          wx.navigateTo({
            url: `/pages/common/webview-page/index?src=${encodeURIComponent(
              partnerReturnUrl
            )}`,
          });
        } else {
          wx.navigateBack();
        }
      }
      wx.onAppShow(cb);
    } catch (error) {
      console.error(error);
      wx.navigateBack();
    }
  },
});

function stringify(query) {
  let str = '?';
  for (const [key, value] of Object.entries(query)) {
    const pair = `${key}=${value}`;
    str += (str === '?' ? '' : '&') + pair;
  }
  return str === '?' ? '' : str;
}
