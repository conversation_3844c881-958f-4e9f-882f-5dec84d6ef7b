
.container--unicashier {
  padding: 15px 0;
}

.unicashier-panel {
  margin-top:10px;
}

.unicashier-total-price {
  position: relative;
  margin: 0 15px;
}

.unicashier-total-price__input {
  line-height: 28px;
  padding: 2px 0;
}

.unicashier-total-price .input-placeholder {
  font-size: 14px;
}

/* 是否输入不参与优惠金额 样式 */
.unicashier-discount {
  display: flex;
  align-items: center;
  line-height: 14px;
  padding: 9px 15px;
}

.unicashier-discount__icon {
  float: left;
  width: 15px;
  height: 15px;
  box-sizing: border-box;
  vertical-align: middle;
  border: 1px solid #e5e5e5;
  text-align: center;
  font-size: 0;
}

.unicashier-discount__icon-success {
  height: 10px;
  vertical-align: middle;
}

.unicashier-discount__icon--checked {
  border-color: #f44;
}

.unicashier-discount__text {
  padding-left: 4px;
  vertical-align: middle;
  color: #999;
  font-size: 12px;
}


/* ump 信息条样式 */
.unicashier-ump__title {
  overflow: hidden;
}

.unicashier-ump__title .unicashier-ump__title-content {
  padding-left: 20px;
}

.unicashier-ump__icon {
  float: left;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  line-height: 14px;
  margin: 4px 0;
  border: 1px solid #f44;
  text-align: center;
  font-size: 10px;
  color: #ff8282;
}

.unicashier-ump__content {
  color: #f44;
  font-size: 12px;
}

/* 金额条 */
.unicashier-price__content {
  color: #f44;
  font-size: 24px;
}

.unicashier-price__flag {
  font-size: 14px;
}

/* 提交按钮 */
.unicashier-btns {
  margin: 20px 15px;
  width: auto !important;
}

