// 买单页面
import money from '@youzan/weapp-utils/lib/money';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import Toast from '@vant/weapp/dist/toast/toast';
import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

const numberExpr = /^(￥)?(\d*(\.\d{0,2})?)?$/;

WscPage({
  data: {
    prepayParams: {},
    totalPrice: {
      error: false,
      value: 0,
      // 上次输入的格式化过的值
      previousValue: '',
      config: {
        placeholder: '询问收银员后输入',
        mode: 'wrapped',
        right: true,
        focus: true,
        inputType: 'digit',
        componentId: 'totalPrice'
      }
    },
    excludePrice: {
      error: false,
      value: 0,
      // 上次输入的格式化过的值
      previousValue: '',
      config: {
        placeholder: '询问收银员后输入',
        mode: 'wrapped',
        right: true,
        inputType: 'digit',
        componentId: 'excludePrice'
      }
    },
    ump: {
      // 扫码优惠id
      activityId: 0,
      // 是否展示优惠信息
      show: false,
      // 优惠门槛金额，0表示不限制，单位：元
      start: 0,
      // 赠送的礼物
      present: '',
      // 优惠描述
      desc: '',
      // 优惠减价金额，单位：元
      reduceQuota: 0,
      // 优惠上线金额，0表示无限制，单位：元
      limitQuota: 0,
      // 优惠折扣
      discount: 10,
      // 是否可以重复计算优惠
      canRepeat: false,
      // 计算出来的优惠金额
      reduce: 0
    },
    showExcludePrice: false
  },

  onLoad(query) {
    const { qrcode_id: qrcodeId, from = '' } = query;
    this.setYZData({ qrcodeId });

    // 检查店铺能否使用小程序二维码收款
    if (from === 'cashier') {
      wx.showLoading();
      app.request({
        path: `/wsctrade/cashier/checkCertification.json?kdt_id=${app.getKdtId()}`,
        method: 'GET',
      }).then(res => {
        // 店铺认证不成功则跳转错误页面
        wx.hideLoading();
        if (!res.success) {
          const errorId = app.db.set({ text: '该店铺无法使用收款码收款，如有疑问请联系商家', code: 101507000 });
          wx.redirectTo({
            url: '/packages/common/error/index?dbid=' + errorId
          });
        }
      }).catch(() => {
        wx.hideLoading();
      });
    }

    app.carmen({
      api: 'youzan.ump.scanreduce.byqrcode/1.0.0/get',
      data: {
        qrcode_id: qrcodeId
      },
      success: (res = {}) => {
        res && this.setYZData({
          ump: {
            // 如果起始金额为0，就直接展示
            show: res.meet_quota === 0,
            desc: res.desc,
            activityId: res.activity_id,
            start: res.meet_quota,
            reduceQuota: res.reduce_quota,
            limitQuota: res.limit_quota,
            discount: res.discount,
            present: res.present,
            canRepeat: res.can_repeat
          }
        });

        this.calculateRealPay();
      },
      fail: () => {}
    });

    app.getShopInfo().then(res => {
      wx.setNavigationBarTitle({
        title: `${res.shop_name || ''}正在向你收款`
      });
    });
  },

  handleTotalPrice({ detail: value = '' }) {
    return this.handlePriceChange(value, 'totalPrice');
  },

  handleExcludePrice({ detail: value = '' }) {
    return this.handlePriceChange(value, 'excludePrice');
  },

  switchExcludePrice() {
    const nextState = !this.data.showExcludePrice;
    let excludePrice = this.data.excludePrice.value;
    // 如果是关闭额外价格的话，需要先把额外价格置0
    if (!nextState) {
      excludePrice = 0;
    }

    this.setYZData({
      'excludePrice.value': excludePrice,
      showExcludePrice: nextState
    });

    // 计算实际支付及优惠
    this.calculateRealPay();
  },

  // 提交支付
  sumbitTap() {
    if (!this.data.totalPrice.value) {
      this.setYZData({ 'totalPrice.error': true });
      Toast('请填写消费总额');
      return;
    }

    // 金额输入错误即返回
    if (this.data.totalPrice.error) {
      Toast('请输入正确的消费总额');
      return;
    }

    // 排除金额输入错误即返回
    if (this.data.showExcludePrice && this.data.excludePrice.error) {
      Toast('请输入正确的不参与优惠金额');
      return;
    }

    if (this.isSubmitting) {
      Toast('支付进行中，请稍后再试');
      return;
    }

    this.isSubmitting = true;

    const umpData = this.data.ump || {};
    wx.showToast({
      title: '请求中',
      icon: 'loading',
      duration: 10000
    });
    // 提交数据
    app.carmen({
      api: 'youzan.trade.business.qrcode/3.0.0/create',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: {
        trade_params: JSON.stringify({
          QrShopInfoDTO: {
            kdtId: app.getKdtId()
          },
          qrPaymentInfo: {
            totalPrice: this.data.totalPrice.value,
            canNotUsePromotionAmount: this.data.excludePrice.value
          },
          qrId: this.data.qrcodeId,
          bookKey: makeRandomString(10) + new Date().getTime(),
          activityId: umpData.show ? umpData.activityId : ''
        })
      },
      success: (res = {}) => {
        wx.hideToast();
        const {
          preparePayResultDTO = {},
          orderResultDTOGroup = {},
          newPrePayResult = {},
          zeroOrder = false
        } = res;
        const prepayParams = {
          ...newPrePayResult,
          bizExt: preparePayResultDTO.bizExt || ''
        };

        // 记录订单号
        this.setYZData({
          orderNo: orderResultDTOGroup[0].orderNo,
          zeroOrder,
          prepayParams
        });

        // 唤起支付
        this.startPay();
      },
      fail: ({ msg = '订单创建失败，请稍后再试' }) => {
        wx.hideToast();
        Toast(msg);
        this.isSubmitting = false;
      }
    });
  },

  startPay() {
    if (this.payService) {
      /** 0元单不走支付系统，需要上游自行处理 */
      if (this.data.zeroOrder) {
        this.onPaidSuccess();
        return;
      }

      let tradeBizExt = {};
      try {
        tradeBizExt = JSON.parse(this.data.prepayParams.bizExt);
      } catch (error) {
        console.error('扫码收款业务扩展参数解析失败', error);
      } finally {
        tradeBizExt.appId = app.getAppId();
      }

      const { cashierSalt, cashierSign, partnerId, prepayId } = this.data.prepayParams;
      this.payService.startPay({
        orderNo: this.data.orderNo,
        cashierSalt,
        cashierSign,
        partnerId,
        prepayId,
        tradeBizExt,
      });
    }
  },

  handlePriceChange(value = '', type = 'totalPrice') {
    const validPrice = this.validateAndSetPrice(value, type);

    // 金额判断
    const { value: excludePrice } = this.data.excludePrice || {};
    const { value: totalPrice } = this.data.totalPrice || {};
    this.setYZData(Object.assign({
      'totalPrice.error': false,
      'excludePrice.error': false
    }, {
      [`${type}.error`]: excludePrice > totalPrice
    }));

    // 计算实际支付及优惠
    this.calculateRealPay();

    return validPrice;
  },

  validateAndSetPrice(value, type) {
    // 如果没有值的话，直接返回
    // 如果数据只有个'.'，就返回
    if (!value || value === '.') {
      this.setYZData({
        [`${type}.value`]: 0,
        [`${type}.previousValue`]: '',
        [`${type}.error`]: false
      });
      return '';
    }

    // '￥1.1' => ["￥1.1", "￥", "1.1", ".1", index: 0, input: "￥1.1"]
    const matchPrice = value.match(numberExpr);

    // 如果不符合的话，直接认为输入错误
    if (!matchPrice) {
      return this.data[type].previousValue;
    }

    const originPrice = matchPrice[2];
    const parsedValue = money(originPrice).toCent();
    const formatedPrice = originPrice ? `￥${originPrice}` : '';

    if (parsedValue.toString().length > 10) {
      return this.data[type].previousValue;
    }

    this.setYZData({
      [`${type}.value`]: parsedValue,
      // 如果金额大于0，表示有实际内容，就存储格式化出来的内容
      // 否则，认为都是无用内容，抛弃掉
      [`${type}.previousValue`]: formatedPrice,
      [`${type}.error`]: false
    });

    return formatedPrice;
  },

  // 计算真实支付价格
  calculateRealPay() {
    const totalPrice = this.data.totalPrice.value;
    let umpPrice = totalPrice - this.data.excludePrice.value;

    // 优惠显示
    if (this.isShowUmp(umpPrice)) {
      this.setYZData({ 'ump.show': true });
    } else {
      this.setYZData({ 'ump.show': false });
    }
  },

  showZanToast(message) {
    Toast(message);
  },

  // 是否需要展示优惠信息
  isShowUmp(realPay) {
    const umpData = this.data.ump;
    const umpStart = umpData.start;
    return umpData.activityId > 0 && realPay >= umpStart;
  },

  onPaidSuccess() {
    wx.redirectTo({
      url: '/packages/order/paid/index?order_no=' + this.data.orderNo
    });
  },

  async initPay({ detail }) {
    const { default: PayService } = await import('@youzan/zan-pay-weapp');
    this.payService = new PayService({
      toast: wx.showToast,
      clear: wx.hideToast,
      request: app.request,
      biz: 'qr_pay',
      quickMode: true,
    });
    const crypto = await import('@youzan/crypto');
    this.payService.init(detail, crypto);
  },

  onCashierNavi({ detail: destination }) {
    wx.navigateTo({
      url: `/pages/common/webview-page/index?src=${destination.url}&title=${destination.title}`,
    });
  },

  onCashierClose() {
    this.isSubmitting = false;
  },
});
