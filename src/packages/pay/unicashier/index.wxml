<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view class="container--unicashier">
    <!-- 总消费金额输入框 -->
    <view class="unicashier-total-price van-hairline--surround">
      <van-field
        autosize
        input-align="right"
        title-width="110px"
        border="{{ false }}"
        label="消费总额"
        input-class="unicashier-total-price__input"
        value=""
        placeholder="{{ totalPrice.config.placeholder }}"
        bind:input="handleTotalPrice"
      ></van-field>
    </view>
  
    <!-- 是否需要输入不参与优惠金额 -->
    <view class="unicashier-discount" catchtap="switchExcludePrice">
      <view class="unicashier-discount__icon {{ showExcludePrice ? 'unicashier-discount__icon--checked' : '' }}">
        <icon wx:if="{{ showExcludePrice }}" class="unicashier-discount__icon-success" type="success_no_circle" size="10" color="#f44"></icon>
      </view>
  
      <text class="unicashier-discount__text">输入不参与优惠的金额（如酒水，特价商品）</text>
    </view>
  
    <!-- 不参与优惠金额 输入框 -->
    <view
      wx:if="{{ showExcludePrice }}"
      class="unicashier-total-price van-hairline--surround"
    >
      <van-field
        autosize
        input-align="right"
        title-width="110px"
        border="{{ false }}"
        label="不参与优惠金额"
        input-class="unicashier-total-price__input"
        value=""
        placeholder="{{ excludePrice.config.placeholder }}"
        bind:input="handleExcludePrice"
      ></van-field>
    </view>
  
    <!-- 优惠信息展示 -->
    <van-cell
      wx:if="{{ ump.activityId }}"
      custom-class="unicashier-panel"
      title-class="unicashier-ump__title"
    >
      <view slot="title">
        <view class="unicashier-ump__icon">惠</view>
        <view class="unicashier-ump__title-content">{{ ump.desc }}</view>
      </view>
      <view wx:if="{{ ump.show && totalPrice.value > 0 }}" class="unicashier-ump__content">
        {{ utils.getUMPDescription(totalPrice.value, excludePrice.value, ump) }}
      </view>
    </van-cell>
  
    <!-- 支付金额展示 -->
    <!-- 在总金额为0时不显示 -->
    <van-cell
      wx:if="{{ totalPrice.value !== 0 }}"
      custom-class="unicashier-panel"
    >
      <view slot="title">实付金额</view>
      <view class="unicashier-price__content">
        <text class="unicashier-price__flag">￥</text>{{ utils.getTotalPay(totalPrice.value, excludePrice.value, ump) }}
      </view>
    </van-cell>

    <van-button
      custom-class="unicashier-btns"
      type="danger"
      block
      disabled="{{ totalPrice.value === 0 }}"
      bind:click="sumbitTap"
    >
      确认买单
    </van-button>
  </view>

  <zan-pay
    bind:init="initPay"
    bind:paysuccess="onPaidSuccess"
    bind:navigate="onCashierNavi"
    bind:cashierclose="onCashierClose"
  />
</page-container>

<wxs src="./utils.wxs" module="utils"></wxs>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
