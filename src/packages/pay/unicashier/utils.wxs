/* eslint-disable */
function adjustFixed(num, len) {
  var parsedNum = Math.round(num * Math.pow(10, len)) / Math.pow(10, len);
  return parsedNum.toFixed(len);
}

function centToYuan(money) {
  return adjustFixed(parseFloat(money / 100) || 0, 2);
}

function calculateReduce(umpPay, umpData) {
  var reduce = 0;
  umpData = umpData || {};

  if (!umpData.show) {
    return reduce;
  }

  var umpStart = umpData.start;
  var umpReduceQuota = umpData.reduceQuota;
  var umpReduceLimit = umpData.limitQuota;

  // 折扣
  if (umpData.discount) {
    reduce += (umpPay * (1 - umpData.discount / 100));
  }

  // 根据支付价格计算 每满xxx元减xx元
  if (umpData.canRepeat && umpStart > 0) {
    reduce += (Math.floor(umpPay / umpStart) * umpReduceQuota);
  } else {
    reduce += umpReduceQuota;
  }

  // 优惠上限
  if (umpReduceLimit) {
    reduce = Math.min(reduce, umpReduceLimit);
  }

  return reduce;
}

function getUMPDescription(totalPrice, excludePrice, umpData) {
  // 展示优惠信息并且有礼物信息
  if (umpData.show && umpData.present) {
    return '已享受优惠';
  }

  var reduce = calculateReduce(totalPrice - excludePrice, umpData);

  if (!reduce) {
    return '';
  }

  return '-￥' + centToYuan(reduce);
}

function getTotalPay(totalPrice, excludePrice, umpData) {
  var reduce = calculateReduce(totalPrice - excludePrice, umpData);

  // 最终金额和 0 对比
  return centToYuan(Math.max(totalPrice - reduce, 0));
}

module.exports = {
  centToYuan: centToYuan,
  getUMPDescription: getUMPDescription,
  getTotalPay: getTotalPay
};
