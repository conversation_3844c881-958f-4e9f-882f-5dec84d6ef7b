import WscPage from 'pages/common/wsc-page';
import { wrapOnInit, fetchKdtId } from '../../pre-card/utils';

const app = getApp();

WscPage({
  data: { },

  onLoad(query) {
    const { ...args } = query;
    fetchKdtId(app).then(kdtId => wrapOnInit({ kdtId, }, {
      app,
      title: '集点卡',
      checklist: ['kdtId'],
      currentUrl: '/packages/point/home/<USER>',
      currentH5Url: '/retail/scrm/point/index',
    }, args));
  }
});
