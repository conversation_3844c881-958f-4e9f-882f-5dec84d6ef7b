import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    agreement: '',
  },

  onShow() {
    wx.showLoading({
      title: '努力加载中',
    });
    app
      .request({
        path: '/wscassets/api/prepaid/weapp/customer/agreement',
      })
      .then(({ context: agreement }) => {
        this.setYZData(
          {
            agreement,
          },
          () => {
            wx.hideLoading();
          }
        );
      })
      .catch((e) => {
        console.warn(e);
        wx.showToast({
          title: String(e.msg || '内部错误'),
          icon: 'none',
          duration: 1000,
        });
        wx.hideLoading();
      });
  },
});
