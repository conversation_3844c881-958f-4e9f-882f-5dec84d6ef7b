import WscPage from 'pages/common/wsc-page';
import { wrapOnInit, entryShop } from '../utils';

const app = getApp();

WscPage({
  data: {},

  onShow() {
    if (wx.canIUse('hideHomeButton')) {
      wx.hideHomeButton();
    }
  },

  async onLoad(query) {
    const { entry, mode = 'normal', ...args } = query;

    const forceKdtId = await entryShop(app, query, this.route);

    delete args.kdtId;
    delete args.kdt_id;

    wrapOnInit(
      {
        kdtId: forceKdtId,
        source: entry,
        mode,
      },
      {
        app,
        title: '',
        checklist: ['kdtId'],
        currentUrl: '/packages/pre-card/recharge/index',
        currentH5Url:
          'https://cashier.youzan.com/pay/new_prepaid_balance_recharge',
      },
      args
    );
  },
});
