import WscPage from 'pages/common/wsc-page';
import { wrapOnInit, fetchKdtId } from '../utils';

const app = getApp();

WscPage({
  data: {},

  onLoad(query = {}) {
    const { cardNo, recordId, alias, ...args } = query;
    fetchKdtId(app).then((kdtId) =>
      wrapOnInit(
        {
          kdtId,
          cardNo,
          recordId,
          alias,
        },
        {
          app,
          title: '领取',
          checklist: ['kdtId'],
          currentUrl: '/packages/pre-card/receive/index',
          currentH5Url: '/prepaid/card/giftcard/receive',
        },
        {
          kdtId,
          card_no: cardNo,
          record_id: recordId,
          alias,
          ...args,
        }
      )
    );
  },
});
