<import src="/components/zan-account/bind-phone-number/index.wxml" />

<page-container copyrightBgColor="#fff" page-bg-color="#fff" class="{{ themeClass }} page-{{ deviceType }} prepaid-disabled">
  <view class="prepaid-disabled__container">
    <view class="prepaid-disabled__image"></view>
    <block wx:if="{{ bind }}">
      <view class="prepaid-disabled__text unbind">绑定手机号后才可使用储值卡</view>
    </block>
    <block wx:else>
      <view class="prepaid-disabled__text">{{ desc ? desc : '商家暂无储值卡服务' }}</view>
    </block>
    <block wx:if="{{ bind }}">
       <user-authorize  authTypeList="{{ ['mobile'] }}"  bind:next="onAccountSuccess" >
            <van-button class="prepaid-disabled__login" type="primary" block>
                去绑定
            </van-button>
        </user-authorize>
    </block>
  </view>
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />