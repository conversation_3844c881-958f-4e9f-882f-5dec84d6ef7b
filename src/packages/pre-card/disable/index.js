import WscPage from 'pages/common/wsc-page/index';
import navs from '@/helpers/navigate';
import Args from '@youzan/weapp-utils/lib/args';

const app = getApp();

WscPage({
  data: {
    bind: false,
    redirectUrl: '',
    desc: '',
    // 登录弹窗标志位
    accountLogin: false
  },

  onLoad(query = {}) {
    const { type, redirectUrl, args, desc = '' } = query;
    const bind = type === 'bind';
    const url = Args.add(redirectUrl, JSON.parse(args));
    this.setYZData({ bind, redirectUrl: url, desc });
  },

  tapBindZanAccount() {
    this.setYZData({
      accountLogin: true
    });
  },

  onAccountSuccess() {
    this.setYZData({
      accountLogin: false
    });
    let hasMobile = !!app.getBuyerId();
    const { redirectUrl: url } = this.data;
    if (hasMobile) {
      return navs.redirect({ url });
    }
  },

  onAccountClose() {
    this.setYZData({
      accountLogin: false
    });
  },
});
