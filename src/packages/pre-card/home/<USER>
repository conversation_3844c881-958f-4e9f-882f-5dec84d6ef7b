import WscPage from 'pages/common/wsc-page';
import { getPath } from '../utils';

const app = getApp();

WscPage({
  data: {},
  onShareAppMessage() {
    return (
      this.__webviewMsg['ZNB.share'] || {
        path: '/packages/home/<USER>/index',
      }
    );
  },
  onLoad(query) {
    const { entry, mode = 'normal', ...args } = query;
    const kdtId = app.getKdtId();
    this.__webviewMsg = {};
    this.setYZData({
      src: getPath(
        { kdtId, source: entry, mode },
        {
          app,
          currentH5Url: '/prepaid/card/index',
        },
        args
      ),
    });
  },
});
