import WscPage from 'pages/common/wsc-page';
import buildUrl from '@youzan/utils/url/buildUrl';
import { wrapOnInit, fetchKdtId } from '../utils';
import format from '@youzan/utils/money/format';
import autoLinedUp from '@youzan/utils/compatible/ajax/autoLinedUp';
import { wscSubscribeMessage } from '@/utils/subscribe-message';
import { getPlugins } from '@youzan/ranta-helper-tee';
import Theme from 'shared/common/components/theme-view/theme';

const app = getApp();

const pollingRechargeInfo = ({ rechargeNo, ruleId }) => {
  const MAX_RETRY_TIMES = 1200;
  const INTERVAL = 500;

  return autoLinedUp(
    () =>
      new Promise((resolve, reject) =>
        app
          .request({
            path: '/prepaid/api/card/recharge/info.json',
            data: {
              rechargeNo,
              ruleId,
            },
          })
          .then((data) => {
            return data && data.polling ? reject(data) : resolve(data);
          })
          .catch((err) => reject(err))
      ),
    (err) => ({ retry: err.polling, interval: INTERVAL }),
    () => {},
    MAX_RETRY_TIMES
  );
};

WscPage({
  data: {
    polling: true,
    useH5: false,
    giftText: '',
    showSubscribeMask: false,
    amount: '',
    themeMainColor: '',
  },

  onShow() {
    if (wx.canIUse('hideHomeButton')) {
      wx.hideHomeButton();
    }
  },

  async onLoad(query = {}) {
    this.query = query;
    const { mode = 'normal', entry, ...args } = query;
    const { amount } = query;

    this.setYZData({
      amount: amount ? format(amount) : '',
    });

    this.request = pollingRechargeInfo({
      rechargeNo: query.orderNo || query.checkRecharge,
      ruleId: query.rule_no,
    });

    this.getThemeColor();
    const info = await this.request();

    this.setYZData({
      polling: info.polling,
      useH5: info.useH5,
      giftText: info.giftText,
      giftTextList: info.giftTextList,
    });

    if (info.useH5) {
      fetchKdtId(app).then((kdtId) =>
        wrapOnInit(
          {
            kdtId,
            source: entry,
            mode,
          },
          {
            app,
            title: '充值成功',
            checklist: ['kdtId'],
            currentUrl: '/packages/pre-card/home/<USER>',
            currentH5Url: '/prepaid/card/success/recharge',
            events: {},
          },
          args
        )
      );
      return;
    }

    this.cardNo = info.cardNo;
    this.waterNo = info.waterNo;
  },

  toDetail() {
    const { kdt_id: kdtId } = this.query;
    this.subscribe(() => {
      const h5Url = buildUrl(
        '/prepaid/card/index?from=successRecharge',
        'h5',
        kdtId
      );
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(
          h5Url
        )}`,
      });
    }, '查看详情');
  },

  toFinish() {
    const { kdt_id: kdtId, fromScene } = this.query;
    this.subscribe(() => {
      if (fromScene) {
        const { dmc } = getPlugins();
        if (fromScene === 'back') {
          dmc.back();
          return;
        }
        if (fromScene === 'Home' || fromScene === 'Usercenter') {
          dmc.reLaunch(fromScene);
          return;
        }
        dmc.navigate(fromScene, {
          kdt_id: kdtId,
        });
      } else {
        const h5Url = buildUrl('/prepaid/card/index', 'h5', kdtId);
        // eslint-disable-next-line @youzan/dmc/wx-check
        wx.navigateTo({
          url: `/pages/common/webview-page/index?src=${encodeURIComponent(
            h5Url
          )}`,
        });
      }
    }, '完成');
  },

  subscribe(next, type) {
    wscSubscribeMessage({
      scene: 'balance_notice_scene',
      windowType: 'recharge_success',
      authorizationType: 'balance',
      subscribePage: '充值成功',
      subscribeType: type,
      options: {
        next,
        onShowTips: () => this.toggleSubscribeMask(true),
        onCloseTips: () => this.toggleSubscribeMask(false),
        onComplete: next,
      },
    });
  },

  reload() {
    this.request && this.request.reset();
  },

  toggleSubscribeMask(showSubscribeMask) {
    this.setYZData({
      showSubscribeMask,
    });
  },

  getThemeColor() {
    Theme.getThemeColor('general').then((color) => {
      const rgb = Theme.switchHexToRgb(color);
      const alpha10 = `${`rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, .1)`}`;
      this.setYZData({
        themeMainColor: color,
        themeColorAlpha10: alpha10,
      });
    });
  },

  getGiftTextList() {},
});
