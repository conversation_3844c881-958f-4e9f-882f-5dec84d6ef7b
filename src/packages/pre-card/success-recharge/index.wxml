<page-container copyrightBgColor="#fff" page-bg-color="#fff" class="{{ themeClass }} page-{{ deviceType }} card">
  <view class="success_recharge__loading-wrapper success-recharge__wrapper" wx:if="{{polling}}">
    <view>
      <h3 class="success-recharge__title">充值中</h3>
      <view class="success-recharge__subtitle">预计1分钟后到账</view>
    </view>
    <view class="success-actions">
      <van-button class="success-recharge__btn" bindtap="reload" round color="{{ themeMainColor }}">
        刷新
      </van-button>
    </view>
  </view>
  <view wx:if="{{!polling}}" class="success-recharge__wrapper">
    <view>
      <h3 class="success-recharge__title">充值成功</h3>
      <p class="success-recharge__amount">
        <view class="amount-unit" wx:if="{{amount}}">￥</view>
        {{amount}}
      </p>
      <view class="success-recharge__content">
        <view class="success-recharge__gift-text" wx:for="{{ giftTextList }}" wx:for-item="giftText" wx:for-index="giftIndex" wx:key="giftIndex">
          <view class="label">
            <view class="theme-color label__action" style="background-color: {{ themeColorAlpha10 }}">
              送
            </view>
            {{ giftText[0] }}
          </view>
          <view class="value">{{ giftText[1] }}</view>
        </view>
      </view>
    </view>
    <view class="success-actions">
      <van-button bindtap="toFinish" round class="success-recharge__btn success-action" color="{{ themeMainColor }}">
        完成
      </van-button>
      <van-button bindtap="toDetail" round class="success-recharge__btn success-action view__btn" plain color="{{ themeMainColor }}">
        查看余额
      </van-button>
    </view>
  </view>
  <subscribe-guide show="{{ showSubscribeMask }}" bind:close="toggleSubscribeMask" />
</page-container>
<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />