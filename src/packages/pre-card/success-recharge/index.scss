.success-recharge__title {
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  text-align: center;
  color: #323233;
  display: block;
  margin-top: 40px;
  margin-bottom: 8px;
}

.success-recharge__amount {
  font-weight: 700;
  font-size: 32px;
  line-height: 36px;
  height: 36px;
  color: #323233;
  display: block;
  text-align: center;
  & > span {
    font-size: 16px;
    line-height: 22px;
  }
}

.success-recharge__gift--text {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  color: #323233;
  display: block;
  margin: 0 auto;
  padding: 12px 0;
  width: 236px;
}

.success-recharge__detail {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #969799;
  text-align: center;
  display: block;
}

.success-actions {
  display: flex;
  justify-content: center;
  width: 100vw;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60px;
}

.success-action:nth-child(n + 1) {
  margin-top: 16px;
}

.success-recharge__btn {
  display: block;
  & .van-button {
    height: 44px;
    width: 188px;
  }
}

.success-recharge {
  &__subtitle {
    text-align: center;
    font-size: 12px;
    color: #969799;
  }
  &__content {
    padding: 0 100rpx;
    margin-top: 40px;
  }
  &__gift-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 21px;
    color: #323233;
    .label {
      display: flex;
      align-items: center;
      font-size: 14px;
      &__action {
        display: block;
        width: 16px;
        height: 16px;
        border-radius: 2px;
        font-size: 11px;
        line-height: 16px;
        text-align: center;
        margin-right: 8px;
      }
    }
    .value {
      font-weight: 500;
    }
  }
}

.amount-unit {
  display: inline-block;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #323233;
}

.view__btn {
  .van-button {
    background-color: #f7f7f7;
    border: none;
  }
}

.success-recharge__wrapper {
  min-height: 100vh;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
