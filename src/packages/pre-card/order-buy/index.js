import WscPage from 'pages/common/wsc-page';
import { wrapOnInit, entryShop } from '../utils';

const app = getApp();

WscPage({
  data: {},

  async onLoad(query = {}) {
    const { templateNo, entry, mode = 'normal', ...args } = query;

    const forceKdtId = await entryShop(app, query, this.route);

    delete args.kdtId;
    delete args.kdt_id;
    wrapOnInit(
      { kdtId: forceKdtId, templateNo, source: entry, mode },
      {
        app,
        title: '购买礼品卡',
        checklist: ['kdtId', 'templateNo'],
        currentUrl: '/packages/pre-card/order-buy/index',
        currentH5Url: 'https://cashier.youzan.com/pay/new_prepaid_card_order',
      },
      {
        templateNo,
        ...args,
      }
    );
  },
});
