import WscPage from 'pages/common/wsc-page';
import { wrapOnInit, fetchKdtId } from '../utils';

const app = getApp();

WscPage({
  data: {},

  onLoad(query) {
    const { entry, ...args } = query;
    fetchKdtId(app).then((kdtId) =>
      wrapOnInit(
        { kdtId, source: entry },
        {
          app,
          title: '兑换',
          checklist: ['kdtId'],
          currentUrl: '/packages/pre-card/exchange/index',
          currentH5Url: '/prepaid/card/exchange',
        },
        args
      )
    );
  },
});
