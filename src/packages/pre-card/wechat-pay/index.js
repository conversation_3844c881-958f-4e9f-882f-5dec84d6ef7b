// 新储值卡微信支付独立页

const app = getApp();

const page = {
  data: {
    viewSrc: ''
  },
  onLoad(query = {}) {
    const payData = Object.assign({}, query);
    Object.keys(payData).forEach(item => {
      payData[item] = decodeURIComponent(payData[item]);
    });
    wx.requestPayment({
      timeStamp: payData.timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType,
      paySign: payData.paySign,
      success: () => {
        wx.showToast({
          title: '支付成功',
          duration: 2000
        });

        setTimeout(() => {
          if (payData.weappRedirectUrl) {
            wx.redirectTo({
              url: decodeURIComponent(payData.weappRedirectUrl)
            });
          } else {
            wx.navigateBack({
              delta: 1
            });
          }
        }, 2000);
      },
      fail: res => {
        let errMsg = res.errMsg || '支付失败，请稍后再试';

        // 非主动取消行为
        if (errMsg !== 'requestPayment:fail cancel') {
          app.logger.appError({
            name: 'wx_pay_error',
            message: errMsg,
            detail: Object.assign({}, res, __wxConfig && __wxConfig.appLaunchInfo)
          });

          if (errMsg.indexOf('fail jsapi has no permission') > 0) {
            errMsg = '受微信政策限制，请前往店铺公众号或H5进行购买';
          }

          wx.showToast({
            title: errMsg,
            icon: 'none',
            duration: 2000
          });
          setTimeout(() => {
            wx.navigateBack({
              delta: 1
            });
          }, 2000);
        } else {
          wx.navigateBack({
            delta: 1
          });
        }
      }
    });
  }
};

Page(page);
