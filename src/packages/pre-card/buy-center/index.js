import WscPage from 'pages/common/wsc-page';
import { wrapOnInit, entryShop } from '../utils';

const app = getApp();

WscPage({
  data: {},

  async onLoad(query) {
    const { entry, mode = 'normal', ...args } = query;
    const forceKdtId = await entryShop(app, query, this.route);

    delete args.kdtId;
    delete args.kdt_id;
    wrapOnInit(
      { kdtId: forceKdtId, source: entry, mode },
      {
        app,
        title: '',
        checklist: ['kdtId'],
        currentUrl: '/packages/pre-card/buy-center/index',
        currentH5Url: '/prepaid/card/giftcard/buy/center',
      },
      args
    );
  },
});
