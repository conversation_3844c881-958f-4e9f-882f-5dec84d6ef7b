import Event from '@youzan/weapp-utils/lib/event';
import args from '@youzan/weapp-utils/lib/args';
import openWebView from 'utils/open-web-view';
import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';

export function fetchKdtId(app) {
  return new Promise((resolve) => {
    const kdtId = app.getKdtId();
    if (!kdtId) {
      Event.once('shop:kdt_id:change', (kdtId) => {
        resolve(kdtId);
      });
    } else {
      resolve(kdtId);
    }
  });
}

export function fetchBuyerId(app) {
  return new Promise((resolve) => {
    const buyerId = app.getBuyerId();
    if (!buyerId) {
      Event.once('app:token:success', () => {
        resolve(app.getBuyerId());
      });
    } else {
      resolve(buyerId);
    }
  });
}

export function getPath(payload, { app, currentH5Url }, mrQuery = {}) {
  const { isRetailApp } = app.globalData;

  const query = {
    acp_kdt_id: payload.kdtId,
    kdt_id: payload.kdtId,
    pub_kdt_id: isRetailApp ? app.getHQKdtId() : payload.kdtId,
    mode: payload.mode || 'normal',
    appId: app.getAppId(),
    ...mrQuery,
  };

  if (payload.source) {
    query.entry = payload.source;
  }

  return args.add(currentH5Url, query);
}

export function wrapOnInit(
  payload,
  {
    app,
    title = '',
    checklist = ['kdtId'],
    openMethod = 'redirectTo',
    currentH5Url = '',
    events = {},
  },
  mrQuery = {}
) {
  for (const each of checklist) {
    if (payload[each] === undefined) {
      throw new Error(`Error without ${each}`);
    }
  }

  const { isRetailApp } = app.globalData;
  for (const eachKey in events) {
    if (eachKey) {
      Event.once(eachKey, (...args) => {
        events[eachKey](...args);
      });
    }
  }
  // getHQKdtId 方法异步获取
  app.getShopInfo().then(() => {
    const query = {
      mode: payload.mode || 'normal',
      appId: app.getAppId(),
      ...mrQuery,
      acp_kdt_id: payload.kdtId,
      kdt_id: payload.kdtId,
      pub_kdt_id: isRetailApp ? app.getHQKdtId() : payload.kdtId,
    };
    if (payload.source) {
      query.entry = payload.source;
    }
    return openWebView(currentH5Url, {
      title,
      method: openMethod,
      query,
    });
  });
}

// 进入指定分享 kdtId 的店铺
async function enterPromotionShop(query, route, forceKdtId) {
  const redirectUrl = args.add('/' + route, query);
  return autoEnterShop(
    { ...query, redirectUrl, ignoreCheckShopType: true },
    forceKdtId
  );
}

export async function entryShop(app, query, route) {
  const { mode = 'normal' } = query;
  // 兼容下划线kdtId 和 驼峰 kdtId，驼峰的不再使用，原因是：app.getShopInfo() 使用的是下划线的 kdtId
  const rawQueryKdtId = query.kdt_id || query.kdtId;
  const queryKdtId = rawQueryKdtId ? Number(rawQueryKdtId) : undefined;

  const { chainStoreInfo = {} } = await app.getShopInfo();
  let forceKdtId = await fetchKdtId(app);

  if (chainStoreInfo.isChainStore) {
    // 总部进店场景
    if (queryKdtId == null || queryKdtId === chainStoreInfo.rootKdtId) {
      forceKdtId = enterPromotionShop(query, route);
    } else if (queryKdtId) {
      if (mode === 'retail-offline' || chainStoreInfo.isPureOffineShop) {
        forceKdtId = queryKdtId;
      } else {
        forceKdtId =
          (await enterPromotionShop(query, route, queryKdtId)) || queryKdtId;
      }
    }
  }

  return forceKdtId;
}
