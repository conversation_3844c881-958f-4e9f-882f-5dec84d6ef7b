const app = getApp();
const BASE_URL = '/intro/api/shop';

export function getShopConfig(query) {
  return app.request({
    method: 'GET',
    path: `${BASE_URL}/getShopConfig`,
    query,
  });
}

export function getShopInfo(query) {
  return app.request({
    method: 'GET',
    path: `${BASE_URL}/getShopInfo`,
    query,
  });
}

export function submitClue(query) {
  if (query.extraInfoMap) {
    query.extraInfoMap = JSON.stringify(query.extraInfoMap);
  }

  if (query.customerProfile) {
    query.customerProfile = JSON.stringify(query.customerProfile);
  }

  return app.request({
    method: 'GET',
    path: `${BASE_URL}/clues`,
    query,
  });
}
