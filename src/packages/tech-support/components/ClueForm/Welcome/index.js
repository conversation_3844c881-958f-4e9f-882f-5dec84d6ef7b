Component({
  properties: {
    mobile: String,
    config: Object,
    globalConfig: Object,
    authed: Boolean,
  },
  data: {
    value: '',
    errMsg: '',
  },
  observers: {
    mobile(mobile) {
      this.setData({
        value: mobile,
      });
    },
  },
  methods: {
    handleClose() {
      this.triggerEvent('close');
    },
    handleInput(e) {
      const { value } = e.detail;

      this.setData({
        value,
      });
    },
    validateMobile() {
      const { value } = this.data;

      if (!/^1\d{10}$/.test(value)) {
        this.setData({
          errMsg: '请输入正确的手机号',
        });

        return false;
      }

      this.setData({
        errMsg: '',
      });

      return true;
    },

    async handleGetPhoneNumber(e) {
      const { mobile } = e.detail;

      if (!mobile) {
        this.handleClose();
        return;
      }

      wx.showLoading({
        title: '加载中...',
      });

      this.setData({
        value: mobile,
      });

      wx.hideLoading();

      this.triggerEvent('submit', { value: mobile });
    },
    handleClickLink(e) {
      const { href } = e.detail;

      if (href.includes('tel')) {
        wx.makePhoneCall({
          phoneNumber: href.replace(/^tel:(\s)?/g, ''),
        }).catch(() => {});
      }
    },
    handleSubmit() {
      if (!this.validateMobile()) {
        return;
      }

      this.triggerEvent('submit', { value: this.data.value });
    },
  },
});
