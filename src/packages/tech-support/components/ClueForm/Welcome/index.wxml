<view class="w">
    <view class="close" bind:tap="handleClose" />

    <view class="title">
        <rich-text nodes="{{config.title || globalConfig.title}}"></rich-text>
    </view>

    <view class="desc" wx:if="{{config.description}}">
        <rich-text nodes="{{config.description}}"></rich-text>
    </view>

    <image
        class="img"
        mode="widthFix"
        src="{{globalConfig.componentProps.coverImage}}"
    />

    <input
        wx:if="{{authed}}"
        placeholder="请输入您的手机号码"
        class="input"
        value="{{mobile}}"
        bind:input="handleInput"
    />

    <user-authorize
        auth-type-list="{{ ['mobile'] }}"
        bind:next="handleGetPhoneNumber"
        wx:else
    >
        <button class="input login-btn">
            请输入您的手机号码
        </button>
    </user-authorize>

    <view class="error-msg" wx:if="{{ errMsg }}">
        {{ errMsg }}
    </view>

    <view wx:else style="height: 20px" />

    <view wx:if="{{authed}}" class="btn" bind:tap="handleSubmit">
        {{globalConfig.title}}
    </view>

     <user-authorize
        auth-type-list="{{ ['mobile'] }}"
        bind:next="handleGetPhoneNumber"
        wx:else
    >
        <button class="btn login-btn">
            {{globalConfig.title}}
        </button>
    </user-authorize>
</view>