.w {
  padding: 32px 24px;
  font-size: 16px;
  line-height: 24px;
  display: flex;
  flex-direction: column;
}
.close {
  position: absolute;
  top: 2px;
  right: 0;
  width: 52px;
  height: 52px;
  background-image: url("https://img01.yzcdn.cn/upload_files/2023/05/11/FvLC_WMk__1QzukkHOZDspNiayzy.png");
  background-size: 24px;
  background-repeat: no-repeat;
  background-position: center;
}
.title {
  white-space: pre-wrap;
  font-weight: 500;
}
.desc {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: #646566;
}
.img {
  margin-top: 16px;
  width: 100%;
  display: block;
  border-radius: 16px;
}
.input {
  margin: 32px 0 0;
  padding: 0 24px;
  box-sizing: border-box;
  height: 56px;
  line-height: 56px;
  border-radius: 27px;
  border: 1px solid #BBC8FF;
  color: #323233;
  text-align: left;
  background-color: #FFF;
}
.input.login-btn, .input.placeholder {
  color: #969799;
}
.error-msg {
  position: relative;
  left: 0;
  bottom: 0;
  padding: 0 24px;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  color: #DF3320;
}
.btn {
  margin: 0;
  box-sizing: border-box;
  height: 56px;
  line-height: 56px;
  border-radius: 28px;
  text-align: center;
  color: #fff;
  font-weight: 500;
  background-color: #000;
}
.btn.login-btn {
  font-size: 14px;
}
