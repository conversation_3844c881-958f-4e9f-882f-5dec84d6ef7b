.gc {
  padding: 8px 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

::-webkit-scrollbar {
  display:none;
  width:0;
  height:0;
  color:transparent;
}

.gtt {
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #7D7E80;
  margin-bottom: 18px;
}

.git:first-child {
  margin-bottom: 30px;
}

.gsi {
  position: relative;
  padding: 6px 40px 6px 66px;
  font-size: 14px;
  line-height: 20px;
  color: #323233;
  margin-bottom: 12px;
}

.gsi::before {
  content: "";
  position: absolute;
  z-index: 1;
  left: 22px;
  top: 50%;
  transform: translateY(-50%);
  width: 26px;
  height: 26px;
  background-image: url("https://img01.yzcdn.cn/upload_files/2023/05/31/FhjZuvsJmXSKEMaGUvGj1pKA0SJw.png");
  background-size: 100%;
}

.gsi.a {
  background: linear-gradient(180deg, #5051D0 0%, #6F6ED7 100%), #FFF;
  box-shadow: 0 2px 5px 3px rgba(127, 148, 218, 0.07);
  border-radius: 26px;
  color: #FFF;
}

.gsi.a::before {
  background-image: url("https://img01.yzcdn.cn/upload_files/2023/05/31/FoSteiH99o7yNPIarD4zBvv6msMP.png");
}

.gb {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 52px;
  border-radius: 26px;
  box-sizing: border-box;
  margin-top: 25px;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  background-color: #000;
}
.gbh {
  opacity: 0.8;
}
