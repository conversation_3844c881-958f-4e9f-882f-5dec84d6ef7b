<view class="gc">
    <view class="g-list">
        <view
            wx:for="{{options}}"
            wx:key="value"
            wx:for-item="g"
            class="git"
        >
            <view class="gtt">
                {{g.title}}
            </view>

            <view
                wx:for="{{g.children}}"
                wx:key="title"
                class="gsi {{selected[g.value][item.value || item.title] ? 'a': ''}}"
                data-group="{{g}}"
                data-item="{{item}}"
                data-index="{{index}}"
                bind:tap="handleSelect"
            >
                {{selected[g.value].indexOf(item.value || item.title) > -1 ? 'a': ''}}
                {{ item.title || item.value }}
            </view>
        </view>
    </view>

     <button
        class="gb"
        hover-class="gbh"
        bind:tap="handleNext"
    >
        {{ last ? globalConfig.confirm || '完成' : '下一步' }}
    </button>
</view>
