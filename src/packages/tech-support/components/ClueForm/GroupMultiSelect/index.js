import get from '@youzan/weapp-utils/lib/get';

Component({
  properties: {
    config: Object,
    options: Array,
    last: Boolean,
  },
  data: {
    selected: {},
    needs: [],
  },
  lifetimes: {
    attached() {
      this.initData();
    },
  },
  methods: {
    initData() {
      const selected = this.data.options.reduce((obj, item) => {
        obj[item.value] = item.children.reduce((o, i) => {
          o[i.value || i.title] = 0;

          return o;
        }, {});

        return obj;
      }, {});

      this.setData({
        selected,
      });
    },
    handleSelect(e) {
      const {
        group: { value: groupValue },
        item,
      } = e.currentTarget.dataset;

      const { needs } = this.data;

      const value = item.value || item.title;

      const key = `selected.${groupValue}.${value}`;

      const currentValue = get(this.data, key);

      let currentNeeds = [];

      if (needs.includes(value)) {
        currentNeeds = needs.filter((i) => i !== value);
      } else {
        currentNeeds = [...needs, value];
      }

      this.setData({
        needs: currentNeeds,
        [key]: currentValue ? 0 : 1,
      });
    },
    getValue() {
      const {
        selected,
        config: { groupSeparateToken },
      } = this.data;

      const valueArr = [];
      for (const [k, v] of Object.entries(selected)) {
        const values = Object.values(v);

        if (values.includes(1)) {
          valueArr.push(k);
        }
      }

      if (!valueArr.length) {
        return '';
      }

      return valueArr.join(groupSeparateToken);
    },
    handleNext() {
      const value = this.getValue();

      if (!value) {
        wx.showToast({
          icon: 'none',
          title: '请选择您的销售渠道和需求',
        });

        return;
      }

      const {
        needs,
        config: { key: configKey, groupKey, separateToken },
      } = this.data;

      this.triggerEvent('change', {
        [groupKey]: this.getValue(),
        [configKey]: needs.join(separateToken),
      });

      this.triggerEvent('submit');
    },
  },
});
