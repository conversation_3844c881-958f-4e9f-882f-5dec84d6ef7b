<view class="sw1 {{focus ? 'focus' : ''}}">
    <view class="t {{steps[current].asStepTitle ? 'st' : ''}}">
       <rich-text nodes="{{ steps[current].asStepTitle ? steps[current].title : globalConfig.title || '免费试用'  }}"></rich-text>
   </view>

    <view class="pb">
        <view class="pit" style="width: {{(current + 1) / steps.length * 100}}%" />
    </view>

    <view class="cw">
        <view class="d" style="transform: translate3D({{-100 * current}}%, 0, 0)">
            <scroll-view class="c" wx:for="{{steps}}" wx:key="key" scroll-y>
                <view class="sc">
                    <view class="st" wx:if="{{!steps[current].asStepTitle}}">
                        <rich-text nodes="{{item.title}}"></rich-text>
                    </view>
                    <view class="sm">
                        <select-comp
                            wx:if="{{item.type === 'select'}}"
                            config="{{item}}"
                            options="{{item.options}}"
                            globalConfig="{{globalConfig}}"
                            last="{{index === steps.length - 1}}"
                            data-item="{{item}}"
                            bind:select="handleChange"
                            bind:submit="handleSubmit"
                        />

                        <company-comp
                            wx:if="{{item.type === 'company'}}"
                            config="{{item}}"
                            globalConfig="{{globalConfig}}"
                            last="{{index === steps.length - 1}}"
                            data-item="{{item}}"
                            bind:focus="handleFocus"
                            bind:blur="handleBlur"
                            bind:select="handleChange"
                            bind:submit="handleSubmit"
                        />

                        <group-multi-select-comp
                            wx:if="{{item.type === 'group-multi-select'}}"
                            config="{{item}}"
                            data-item="{{item}}"
                            options="{{item.options}}"
                            last="{{index === steps.length - 1}}"
                            bind:change="handleChange"
                            bind:submit="handleSubmit"
                        />
                    </view>
                    
                    <button
                        class="sp"
                        bind:tap="handlePrev"
                        wx:if="{{ index > 0 }}"
                    >
                        返回上一步
                    </button>
                </view>
            </scroll-view>
        </view>
    </view>
</view>