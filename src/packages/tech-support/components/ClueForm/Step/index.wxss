.sw1 {
  height: 75vh;
  box-sizing: border-box;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.focus {
  height: 95vh;
}
.t {
  width: 100%;
  padding: 16px 0 12px;
  text-align: center;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
}
.t.st {
  padding: 16px 24px 0;
  white-space: pre-line;
  text-align: left;
  line-height: 24px;
  margin-left: 48px;
}
.pb {
  box-sizing: border-box;
  width: calc(100% - 48px);
  display: flex;
  height: 4px;
  margin: 0 24px 16px;
  overflow: hidden;
  background-color: #EBEBEB;
}
.pit {
  position: relative;
  background-color: #3370FF;
  transition: all cubic-bezier(0.4, 0, 0.2, 1) 300ms;
  overflow: hidden;
}
.pit:first-child {
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
}
.pit:last-child {
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
}
.cw {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  overflow: hidden;
}
.d {
  width: 100%;
  min-height: 0;
  display: flex;
  flex: 1;
  transition: all cubic-bezier(0.4, 0, 0.2, 1) 300ms;
}
.c {
  width: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  padding: 0 24px;
  height: 100%;
}
.sc {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.st {
  font-size: 16px;
  font-weight: 500;
  line-height: 32px;
  min-height: 48px;
  margin-bottom: 8px;
}
.sm {
  flex: 1;
}
.sp {
  font-size: 14px;
  height: 34px;
  color: #969799;
  background-color: transparent;
  border: none;
}
.sp::after {
  content: none;
}
