Component({
  properties: {
    inview: <PERSON><PERSON><PERSON>,
    steps: Array,
    globalConfig: Object,
  },
  data: {
    focus: false,
    current: 0,
  },
  methods: {
    handleFocus() {
      this.setData({
        focus: true,
      });
    },
    handleBlur() {
      this.setData({
        focus: false,
      });
    },
    handleChange(e) {
      const { detail } = e;

      this.triggerEvent('change', { data: detail });
    },
    handleSelect(e) {
      const { item } = e.currentTarget.dataset;

      const { current, steps } = this.data;

      const data = {
        [item.key]: e.detail.value,
      };

      if (current < steps.length - 1) {
        this.setData({
          current: ++this.data.current,
        });
      }

      this.triggerEvent('change', { data });
    },
    handlePrev() {
      const { current } = this.data;

      if (current === 0) {
        this.triggerEvent('prev');
        return;
      }

      this.setData({
        current: --this.data.current,
      });
    },
    handleSubmit() {
      this.handleNext();

      this.triggerEvent('submit');
    },
    handleNext() {
      const { current, steps } = this.data;

      if (current < steps.length - 1) {
        this.setData({
          current: ++this.data.current,
        });
      } else {
        this.triggerEvent('next');
      }
    },
  },
});
