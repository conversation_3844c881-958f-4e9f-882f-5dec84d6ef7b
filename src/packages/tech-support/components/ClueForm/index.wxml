<van-popup 
    round 
    show="{{visible}}"
    custom-style="width:100vw;overflow:hidden;border-radius:10px;padding-bottom:0;"
    position="bottom"
>
    <view
        class="container"
        style="background-color: {{ config.componentProps.backgroundColor || '#fff' }}"
    >
        <view
            wx:if="{{ config.componentProps.backgroundImage }}"
            class="bg"
            style="background-image: url('{{ config.componentProps.backgroundImage }}')"
        />

        <view class="content" style="transform: translate3D({{-100 * step}}%, 0, 0)">
            <view class="step">
                <welcome
                    mobile="{{mobile}}"
                    authed="{{authed}}"
                    config="{{config.welcome}}"
                    globalConfig="{{config}}"
                    bind:submit="handleSubmitMobile"
                    bind:close="handleClose"
                />
            </view>
            <view class="step">
                <step
                    inview="{{step === 1}}"
                    steps="{{config.steps}}"
                    globalConfig="{{config}}"
                    bind:prev="handlePrev"
                    bind:change="handleChange"
                    bind:next="handleNext"
                    bind:submit="handleSubmit"
                />
            </view>
            <view class="step">
                <result
                    config="{{config.result}}"
                    globalConfig="{{config}}"
                    bind:close="handleClose"
                />
            </view>
        </view>
    </view>
</van-popup>