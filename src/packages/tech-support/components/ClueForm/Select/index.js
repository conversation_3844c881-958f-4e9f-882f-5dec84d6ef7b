Component({
  properties: {
    config: Object,
    options: Array,
    last: Number,
  },
  data: {
    current: undefined,
  },
  methods: {
    handleClick(e) {
      const {
        config: { key },
      } = this.data;

      const { item, index } = e.currentTarget.dataset;

      this.setData({
        current: index,
      });

      this.triggerEvent('select', {
        [key]: item.value,
      });

      this.triggerEvent('submit');
    },
  },
});
