.select-wrap {
  display: grid;
  row-gap: 20px;
  flex-wrap: wrap;
  justify-content: space-between;
  column-gap: 16px;
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.select-wrap.compact {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.select-item {
  width: 100%;
  height: 54px;
  border: none;
  border-radius: 27px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 14px;
  color: #323233;
  box-shadow: 0 2px 5px 3px rgba(127, 148, 218, 0.08);
  background-color: #fff;
}
.select-item::after {
  content: none;
}
.select-item.active {
  color: #fff;
  background: linear-gradient(180deg, #5051D0 0%, #6F6ED7 100%), #FFF;
}
.select-hover-item {
  color: #fff;
  background-color: #3370FF;
}
