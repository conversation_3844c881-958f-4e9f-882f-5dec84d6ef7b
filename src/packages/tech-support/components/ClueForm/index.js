import { submitClue } from '../../api';
import { logReportClue } from '../../logger';
import pick from '@youzan/weapp-utils/lib/pick';
import omit from '@youzan/weapp-utils/lib/omit';
import set from 'utils/set';
import defaultsDeep from 'utils/defaultsDeep';

const app = getApp();

const clue = {};

const supportTypes = ['select', 'company', 'group-multi-select'];

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false,
    },
    cluesSource: Number,
    fromSource: String,
    // 展示结果从外面传入
    result: Object,
  },
  data: {
    mobile: '',
    authed: false,
    innerVisible: false,
    mergedVisible: false,
    config: {},
    step: 0,
  },
  observers: {
    visible(visible) {
      if (visible) {
        this.initial();
      }
    },
  },
  lifetimes: {
    attached() {
      this.getConfig();
    },
  },
  methods: {
    async initial() {
      const tokenData = app.getToken() || {};

      const { mobile } = tokenData;

      if (mobile) {
        set(clue, 'mobile', mobile);
      }

      this.setData({
        authed: !!mobile,
        mobile,
      });
    },
    getConfig() {
      app
        .request({
          method: 'GET',
          path: `/intro/api/shop/config/c_weapp`,
        })
        .then((data) => {
          const { weapp, sm, ...extra } = data;

          const { welcome, steps, ...reset } = defaultsDeep(
            {},
            weapp,
            sm,
            omit(extra, ['sm', 'md', 'lg', 'xl', '2xl', 'weapp', 'swan'])
          );

          let { result } = reset;

          if (this.data.result) {
            result = {
              ...result,
              ...pick(this.data.result, ['title', 'qrcode']),
            };
          }

          this.setData({
            config: {
              ...reset,
              welcome,
              result,
              steps: steps.filter((s) => supportTypes.includes(s.type)),
            },
          });
        })
        .catch((e) => {
          console.log('error =>', e);
        });
    },
    handleNext() {
      this.setData({
        step: ++this.data.step,
      });
    },
    handleSubmitMobile(e) {
      const { value } = e.detail;

      this.setData({
        mobile: value,
        authed: true,
      });

      set(clue, 'mobile', value);

      logReportClue({
        medium: 'c_support',
        mobile: value,
        component: 'login',
      });

      this.handleSubmit();

      this.handleNext();
    },
    handleClose() {
      this.triggerEvent('close');
    },
    handlePrev() {
      this.setData({
        step: --this.data.step,
      });
    },
    async handleChange(e) {
      const { data } = e.detail;

      for (const [k, v] of Object.entries(data)) {
        set(clue, k, v);
      }
    },
    async handleSubmit() {
      logReportClue({
        medium: 'c_support',
        mobile: clue.mobile,
        title: '线索提交',
        component: 'login',
      });

      submitClue({
        ...clue,
        from_source: this.data.fromSource,
        cluesSource: this.data.cluesSource,
      });
    },
  },
});
