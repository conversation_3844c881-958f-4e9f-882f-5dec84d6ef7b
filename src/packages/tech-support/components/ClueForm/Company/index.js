import debounce from '@youzan/weapp-utils/lib/debounce';

Component({
  properties: {
    config: Object,
    globalConfig: Object,
    last: Number,
  },
  data: {
    value: '',
    active: false,
    companys: [],
    errMsg: '',
  },
  lifetimes: {
    created() {
      this.qc = debounce(this.uc, 300);
    },
  },
  methods: {
    handleInput(e) {
      const { value } = e.detail;

      this.setData({
        value,
        errMsg: '',
      });

      this.qc(value);
    },
    uc(keyword) {
      getApp()
        .request({
          method: 'GET',
          path: '/intro/api/shop/company/fuzzy',
          query: {
            keyword,
          },
        })
        .then((companys) => {
          this.setData({
            companys,
          });
        });
    },
    qc: null,
    handleFocus() {
      this.setData({ active: true });

      setTimeout(() => {
        this.triggerEvent('focus');
      }, 50);
    },
    handleBlur() {
      this.setData({ active: false });

      this.triggerEvent('blur');
    },
    handleSelectCompany(e) {
      const { item } = e.currentTarget.dataset;

      this.setData({
        value: item,
      });
    },
    validateForm() {
      const { config, value } = this.data;

      if (!config.validate) {
        return '';
      }

      const { required } = config.validate;

      if (required && !value.trim()) {
        return required;
      }

      return '';
    },
    handleNext() {
      const errMsg = this.validateForm();

      if (errMsg) {
        this.setData({
          errMsg,
        });

        return;
      }

      const {
        config: { key },
        value,
      } = this.data;

      this.triggerEvent('select', { [key]: value });

      this.triggerEvent('submit');
    },
  },
});
