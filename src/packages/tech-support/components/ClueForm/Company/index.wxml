<view>
    <view class="cc">
        <input 
            class="ci"
            placeholder="{{config.props.placeholder}}"
            placeholder-class="cip"
            value="{{value}}"
            bind:input="handleInput"
            bind:focus="handleFocus"
            bind:blur="handleBlur"
        />

        <view class="cl {{companys.length && active ? '' : 'hidden'}}">
            <view
                class="cit"
                wx:for="{{companys}}"
                wx:key="*this"
                hover-class="cith"
                data-item="{{item}}"
                bind:tap="handleSelectCompany"
                confirm-type="{{ last ? 'done' : 'next' }}"
            >
                {{ item }}
            </view>
        </view>

        <view class="ce">
            {{ errMsg }}
        </view>
    </view>

    <button
        class="cb"
        hover-class="cbh"
        bind:tap="handleNext"
    >
        {{ last ? globalConfig.confirm || '完成' : '下一步' }}
    </button>
</view>