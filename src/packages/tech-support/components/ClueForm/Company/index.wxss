.cc {
  position: relative;
  z-index: 1;
  height: 54px;
  overflow: visible;
}
.ci {
  box-sizing: border-box;
  width: 100%;
  padding: 0 24px;
  height: 54px;
  border-radius: 27px;
  border: 1px solid #BBC8FF;
  font-size: 16px;
  line-height: 24px;
  background: #fff;
}
.cip {
  color: #969799;
}
.ce {
  position: relative;
  left: 0;
  bottom: 0;
  padding: 0 24px;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  color: #DF3320;
}
.cl {
  width: 100%;
  background: #fff;
  border-radius: 4px;
  margin-top: 4px;
  transition: all cubic-bezier(0.4, 0, 0.2, 1) 150ms;
  padding: 6px 0;
}
.cl.hidden {
  padding: 0;
  max-height: 0;
  overflow: hidden;
}
.cit {
  width: 100%;
  box-sizing: border-box;
  padding: 8px 24px;
  font-size: 14px;
  line-height: 20px;
  color: #000;
  word-break: break-all;
}
.cith {
  background: #E8EFFA;
}
.cb {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 52px;
  border-radius: 26px;
  box-sizing: border-box;
  margin-top: 20px;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  background: #000;
}
.cbh {
  opacity: 0.8;
}
