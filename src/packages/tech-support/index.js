import * as API from './api';
import openWebView from 'shared/utils/open-web-view';

const app = getApp();

const defaultLogParams = {
  et: 'click',
  pt: 'support',
};

Page({
  data: {
    shopInfo: {},
    shopConfig: {},
    guaranteeInfo: {},
    isYZGuarantee: false,
    isJoinConsumeSafeguard: false,
    showFooter: true,
    visible: false,
    imagePrefix: 'https://img01.yzcdn.cn/upload_files',
    safeguard: [],
  },
  handleClose() {
    this.setData({
      visible: false,
    });
  },
  handleSubmit(event) {
    const { mobile } = event.detail;

    this.sendClickLog({
      ei: 'receive_now',
      en: '点击领取按钮',
      params: { mobile },
    });

    this.setData({
      visible: true,
    });
  },
  sendClickLog(logParams) {
    app.logger &&
      app.logger.log({
        et: 'click',
        pt: 'support',
        ...defaultLogParams,
        ...logParams,
      });
  },
  submitClue(mobile) {
    return API.submitClue({
      name: mobile,
      mobile,
      intentionProductType: 'wsc_dd',
      clues_source: 42,
      agent_type: 'weapp',
      leads_get_type: 8,
    }).catch((e) => {
      console.log(e);
    });
  },
  openRule() {
    openWebView(
      'https://www.youzan.com/intro/rule/detail?alias=11el5hp9j&pageType=rules'
    );
  },
  backShop() {
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx.navigateBack();
  },
  onLoad() {
    app.logger &&
      app.logger.log({
        et: 'view',
        ei: 'enter',
        en: '浏览页面',
        pt: 'support',
      });

    const kdtId = app.getKdtId();

    if (kdtId) {
      API.getShopConfig({
        kdtId,
        shopConfig: JSON.stringify([
          'shop_operate_duration_tag_switch',
          'shop_operate_duration_years',
          'principal_cert_type',
          'is_brand_customization',
          'brand_cert_type',
        ]),
      }).then((data) => {
        if (data) {
          const oldShopYear = +data.configs.shop_operate_duration_years;
          this.setData({
            shopConfig: {
              isOldShop: data.configs.shop_operate_duration_tag_switch === '1',
              oldShopYear,
              oldShopYearImg: (data.oldShopMap || {})[oldShopYear],
              // 1 2 4 5分别代表旗舰店，专卖店，专营店，卖场  需要显示品牌标识
              isBrand: [1, 2, 4, 5].indexOf(+data.configs.brand_cert_type) > -1,
              // 2 5 分别代表企业认证和政府及事业单位   需要显示企业认证标识
              principalCert:
                [2, 5].indexOf(+data.configs.principal_cert_type) > -1,
            },
          });
        }
      });

      API.getShopInfo({
        kdtId,
      }).then((data) => {
        const { safeguard = [], ...res } = data;

        return this.setData({
          shopInfo: res,
          safeguard,
        });
      });
    }
  },
  onPageScroll({ scrollTop }) {
    this.setData({
      showFooter: scrollTop <= 1930,
    });
  },
});
