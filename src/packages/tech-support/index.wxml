<page-container>
  <view class="a">
    <view class="b">
        <image mode="aspectFill" class="bb" src="{{imagePrefix}}/2020/08/18/FrFs8KqdD5I0yQbpoTh_CrGM4Wr0.png" mode="aspectFit|aspectFill|widthFix" />
          <view class="bc f_m"
            :style="isGetShopInfo ? 'opacity: 1' : 'opacity: 0'">
              <image class="bd" src="{{shopInfo.logo}}" />
              <view class="be">
                  <text class="bcf f_m">{{ shopInfo.shopName }}</text>
                  <view class="bct">
                      <image wx:if="{{shopConfig.isBrand}}" class="bct_b"
                          src="{{imagePrefix}}/2020/08/27/FtMSeob4veu9DFLd5JcOAoizCulL.png" />
                      <image wx:if="{{shopConfig.principalCert}}"
                          class="bct_p_c"
                          src="{{imagePrefix}}/2020/08/27/FkLq8WHHrYpO17ZAH-YbnHIPyJCu.png" />
                      <image wx:if="{{shopConfig.oldShopYearImg && shopConfig.isOldShop}}"
                          class="bcta" src="{{shopConfig.oldShopYearImg}}" />
                  </view>
              </view>
          </view>
          <text class="bcb f_b">有赞提供技术支持</text>
          <text class="bcp">打造安全无忧的购物体验</text>
    </view>
    <view class="si">
        <view class="c" wx:if="{{shopConfig.isOldShop}}">
            <image class="c_icon"
                src="{{imagePrefix}}/2020/08/18/FrqsKyHYK1U40sQEGjdoGRpQBjq6.png" />
            <view class="cdd">
                <image class="old" src="{{shopConfig.oldShopYearImg}}" />
                <text class="cd">本店已诚信经营{{ shopConfig.oldShopYear }}年以上，深受消费者喜爱
                </text>
            </view>
        </view>

        <view class="c" wx:for="{{safeguard}}">
            <image class="c_icon"
                src="{{item.image}}" />
            <view class="cdd">
                <text class="c_c_t f_b" style="color: {{item.color}}">{{item.title}}</text>
                <text class="cd">{{item.description}}</text>
            </view>
        </view>
    </view>

    <view class="f">
        <text class="f_title f_b">开店用有赞</text>
        <text class="f_text">
            有赞帮助商家在社交渠道获取流量、裂变营销带来销量、沉淀会员不断复购。8年技术沉淀，600万商家都在用，想开店，用有赞！
        </text>
    </view>
    <user-authorize
        auth-type-list="{{ ['mobile'] }}"
        bind:next="handleSubmit"
    >
        <button class="e">领取开店礼包</button>
    </user-authorize>
    <image class="j" src="{{imagePrefix}}/2023/08/03/FkelW2Lx3OZKOaphf3zDJdd78DIE.jpg" mode="widthFix"></image>
    <user-authorize
        auth-type-list="{{ ['mobile'] }}"
        bind:next="handleSubmit"
        class="f_m"
    >
        <button class="spsb f_m">立即领取</button>
    </user-authorize>
    <view class="h">
        点击立即领取即表示阅读并同意<view bindtap="openRule" class="sl_law_rule">《个人信息保护法律声明》</view>
    </view>
    <view bindtap="backShop" class="fbs f_m">返回店铺</view>

    <view wx:if="{{showFooter}}" class="q">
        <view class="w w_back f_m"
              bindtap="backShop">返回店铺</view>
        <user-authorize
            auth-type-list="{{ ['mobile'] }}"
            bind:next="handleSubmit"
            class="w r"
        >
            我要开店
        </user-authorize>
    </view>

    <clue-form closable cluesSource="{{ 42 }}" visible="{{visible}}" bind:close="handleClose" />
  </view>
</page-container>
