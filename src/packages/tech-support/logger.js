export const logReportClue = async ({
  mobile,
  title = '',
  component = '',
  medium = '',
}) => {
  const app = getApp();
  const { logger } = app;
  const tokenData = app.getToken() || {};

  const { openId = '' } = tokenData;

  logger.log({
    et: 'click', // 事件类型
    ei: 'submit_clue', // 事件标识
    en: '提交线索', // 事件名称
    params: {
      medium,
      mobile,
      title,
      openId,
      component,
    }, // 事件参数
  });
};
