import { VanxPage } from 'pages/common/wsc-page/index';
import { mapState, mapGetters, mapMutations } from '@youzan/vanx';
import goodsPageInits from '../common/goods-page';
import goodsPageSaas from '../common/goods-page-saas';
import store from './store';

const extendPageConfig = ECLOUD_MODE ? goodsPageSaas : {};

VanxPage(goodsPageInits, extendPageConfig, {
  currentPageType: 'help-cut',

  store(query) {
    const pageTime = (new Date()).getTime();
    store.name = `help-cut-${query.alias}-${pageTime}`;
    return store;
  },

  mapData: {
    ...mapState({
      showFollowPopup: (state) => state.showFollowPopup,
    }),
    ...mapGetters(['followExtraData'])
  },

  ...mapMutations([
    'togglePopupShow',
  ]),

  onGoodsInited() {
    console.log('oninited', this.$store.state);
  }
});
