import get from '@youzan/weapp-utils/lib/get';

export default {
  updateHelpCutBottom(state, data = {}) {
    const { type = '', sponsorId } = data;
    const activity = get(state, 'marketActivity.helpCut', {});

    switch (type) {
      case 'showLoading':
        activity.loading = true;
        break;
      case 'hideLoading':
        activity.loading = false;
        break;
      case 'updateStatus':
        activity.status = 3;
        activity.sponsorId = sponsorId;
        break;
      default:
        break;
    }
  },
  togglePopupShow(state) {
    state.showFollowPopup = !state.showFollowPopup;
  }
};
