import get from '@youzan/weapp-utils/lib/get';
import args from '@youzan/weapp-utils/lib/args';
import navigate from 'shared/utils/navigate';

const app = getApp();

export default {
  // 砍价购买
  startCut({ state, commit }) {
    const activity = get(state, 'marketActivity.helpCut', {});
    const activityId = activity.activityId;
    if (!activityId) {
      wx.showToast({
        title: '缺少必要参数',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    commit('updateHelpCutBottom', {
      type: 'showLoading',
    });

    app.request({
      method: 'POST',
      path: 'wscump/bargain-purchase/create.json',
      query: {
        activityId,
        type: 1
      }
    }).then((res) => {
      const query = args.getAll(state.pageParams.path) || {};
      const umpAlias = query.umpAlias || '';

      const url = args.add('/packages/ump/bargain-purchase/home/<USER>', {
        umpAlias,
        sponsorId: res.sponsorId,
        umpActivityId: activityId,
      });
      // 这里写得不是很好，时间来不及了，goodsBottom应该用state，而不是getters
      commit('updateHelpCutBottom', {
        type: 'updateStatus',
        sponsorId: res.sponsorId
      });
      navigate.navigate({
        url
      });
    }).catch(err => {
      // 需关注后帮砍
      if (err.code === 160540352) {
        commit('togglePopupShow');
        return;
      }
      wx.showToast({
        title: err.msg,
        icon: 'none',
        duration: 2000
      });
    }).then(() => {
      commit('updateHelpCutBottom', {
        type: 'hideLoading',
      });
    });
  },
};
