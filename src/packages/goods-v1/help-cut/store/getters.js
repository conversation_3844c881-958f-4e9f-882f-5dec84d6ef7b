import get from '@youzan/weapp-utils/lib/get';
import args from '@youzan/weapp-utils/lib/args';
import { splitPrice } from 'shared/packages/goods/store/utils/helpers';
import { SKU_SCENE, ACCOUNT_UNION_SCENE } from 'shared/packages/goods/common/constant';
import {
  getBottomViewConfig,
} from 'shared/packages/goods/containers/goods-bottom/store/common';

export default {
  activityTitle() {
    return '砍价';
  },

  activityLabel() {
    return '砍价享';
  },

  /* ============ activity-banner ========== */
  showActivityBanner() {
    return true;
  },

  countdown(state) {
    const activity = get(state, 'marketActivity.helpCut', {});
    const { startRemainTime, endRemainTime } = activity;
    const now = +new Date();
    let desc;
    let end;
    if (startRemainTime > 0) {
      desc = '距开始';
      end = now + startRemainTime;
    } else if (endRemainTime > 0) {
      desc = '距结束仅剩';
      end = now + endRemainTime;
    } else {
      desc = '活动已结束';
      end = now;
    }

    return {
      desc,
      end,
      start: now
    };
  },

  /* ============ goods-info ============== */

  goodsPrice(state) {
    const activity = get(state, 'marketActivity.helpCut', {});
    const { priceDateRange, goods } = state;
    const {
      price: currentPrice,
      minPrice,
      maxPrice,
    } = activity.sku || {};
    const {
      price: originPrice,
    } = state.sku || {};
    const {
      isVirtualTicket,
    } = goods;
    const isRange = minPrice !== maxPrice;

    // 电子卡券加上价格日历特殊逻辑
    if (isVirtualTicket && priceDateRange) {
      return {
        currentPrice: priceDateRange
      };
    }

    return {
      isRange,
      currentPrice,
      minPriceData: splitPrice(minPrice),
      maxPriceData: splitPrice(maxPrice),
      originPrice,
    };
  },

  // 商品信息价格标签
  goodsPriceTag() {
    return '砍价享';
  },

  // 库存
  goodsStockNum(state) {
    return get(state, 'marketActivity.helpCut.sku.stockNum', 0);
  },

  /* ============ goods-info-tip =========== */

  /* ============ goods-bottom ============= */

  // 零元抽奖拼团主按钮展示
  goodsBottomView(state, getters) {
    const activity = get(state, 'marketActivity.helpCut', {});
    const currentTime = new Date().getTime();

    const maxPrice = get(activity, 'sku.maxPrice');
    const minPrice = get(activity, 'sku.minPrice');
    // 售价
    const price = maxPrice > minPrice ? splitPrice(minPrice, false).desc : get(activity, 'sku.price');
    // 零售价
    const oldPrice = get(activity, 'sku.oldPrice');
    // 库存
    const skuStockNum = get(activity, 'sku.stockNum', 0);

    const btnMain = {
      show: true,
      loading: activity.loading || false,
      text: `¥${price}砍价拿`,
      skuScene: SKU_SCENE.START_CUT,
      accountUnionScene: ACCOUNT_UNION_SCENE.GROUP_BUY,
    };
    const btnVice = {
      show: true,
      text: `¥${oldPrice}购买`,
      skuScene: SKU_SCENE.SEL_SKU,
      accountUnionScene: ACCOUNT_UNION_SCENE.ADD_CART,
    };

    if (!skuStockNum) {
      return {
        statusBar: {
          text: '该商品售罄啦，看看其他商品吧！',
        }
      };
    }

    if (activity.endTime < currentTime) {
      return {
        statusBar: {
          text: '砍价活动已结束，去看看其他商品吧',
        }
      };
    }

    const btnInfo = getBottomViewConfig(state, getters);
    if (btnInfo) {
      return btnInfo;
    }

    // 活动未开始或者库存为0，则不显示砍价按钮
    if (activity.startTime > currentTime || !skuStockNum) {
      btnMain.show = false;
    }

    if (+activity.status === 2) {
      btnMain.disabled = true;
      btnMain.text = '已砍价购买';
      btnMain.skuScene = SKU_SCENE.NORMAL_BUY;
    } else if (+activity.status === 3) {
      const query = args.getAll(state.pageParams.path) || {};
      const umpAlias = query.umpAlias || '';
      btnMain.text = '正在砍价';
      btnMain.navigate = true;
      btnMain.path = args.add('/packages/ump/bargain-purchase/home/<USER>', {
        umpAlias,
        sponsorId: activity.sponsorId,
        umpActivityId: activity.activityId,
      });
    } else {
      btnMain.subscribeScene = 'helpcut';
    }

    return {
      main: btnMain,
      vice: btnVice,
    };
  },

  /* ============ goods-sku ============= */
  // 用于重写的sku数据
  skuData(state) {
    const activity = get(state, 'marketActivity.helpCut', {});
    return state.skuConfig.skuScene === SKU_SCENE.ACT_BUY ? activity.sku : state.sku;
  },

  activitySkuData(state) {
    return get(state, 'marketActivity.helpCut.sku', {});
  },

  skuPriceTag(state) {
    return state.skuConfig.skuScene === SKU_SCENE.ACT_BUY ? '砍价享' : '';
  },

  followExtraData(state) {
    const activity = get(state, 'marketActivity.helpCut', {});
    const activityId = activity.activityId;
    const feature = args.getAll(state.pageParams.path) || {};

    return {
      bizCode: 1,
      bizSubCode: 0,
      activityKey: activityId,
      feature,
    };
  },

  activityInfoForPrice(state) {
    const query = args.getAll(state.pageParams.path) || {};
    const activityId = query.activityId || '';
    return {
      type: 'helpCut',
      identification: activityId,
    };
  },

};
