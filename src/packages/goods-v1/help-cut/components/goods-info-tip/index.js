import get from '@youzan/weapp-utils/lib/get';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState } from '@youzan/vanx';

VanxComponent({
  properties: {
    themeClass: String
  },

  mapData: {
    ...mapState({
      goodsTitle: (state) => state.goods.title,
      shopName: (state) => state.shop.name,
      minPrice: (state) => (+get(state, 'marketActivity.helpCut.sku.minPrice', 0) / 100).toFixed(2, 10),
    }),
  },

  data: {
    isPopupShown: false,
  },

  methods: {
    showPopup() {
      this.setYZData({
        isPopupShown: true
      });
    },

    closePopup() {
      this.setYZData({
        isPopupShown: false
      });
    }
  }
});
