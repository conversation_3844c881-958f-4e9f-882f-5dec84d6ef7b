import get from '@youzan/weapp-utils/lib/get';
import { VanxPage } from 'pages/common/wsc-page/index';
import { mapState, mapMutations, mapGetters } from '@youzan/vanx';
import goodsPageInits from '../common/goods-page';
import goodsPageSaas from '../common/goods-page-saas';
import store from './store';

const app = getApp();
const extendPageConfig = ECLOUD_MODE ? goodsPageSaas : {};

VanxPage(goodsPageInits, extendPageConfig, {
  currentPageType: 'seckill',

  data: {
    // 登录弹窗标志位
    accountLogin: false
  },

  store(query) {
    const pageTime = (new Date()).getTime();
    store.name = `seckill-${query.alias}-${pageTime}`;
    return store;
  },

  mapData: {
    ...mapState({
      activity: (state) => get(state, 'marketActivity.seckill', {}),
      showBindPhoneNumber: (state) => state.seckillAccount.showBindPhoneNumber,
      bindTips: (state) => state.seckillAccount.bindTips,
    }),
  },

  computed: {
    ...mapGetters(['goodsStockNum', 'isStarted'])
  },

  ...mapMutations([
    'setSkuStockNum',
    'updateSeckillAccount',
  ]),

  // 绑定手机号
  tapBindZanAccount() {
    this.setYZData({
      accountLogin: true
    });
  },

  // 完成绑定回调
  onAccountSuccess() {
    this.updateSeckillAccount({
      showBindPhoneNumber: false,
      bindTips: ''
    });
    this.setYZData({
      accountLogin: false
    });
  },

  onAccountClose() {
    this.setYZData({
      accountLogin: false
    });
  },

  onGoodsInited() {
    this.isStarted && this.setSkuStockNum(this.goodsStockNum);
    console.log('oninited', this.$store.state);
    // const { activity, alias } = this.data;
    // if (activity.endAt < +new Date()) {
    //   // 活动结束需要重定向
    //   wx.redirectTo({
    //     url: `/packages/goods/seckill/end?alias=${alias}`,
    //   });
    // }
  },
});
