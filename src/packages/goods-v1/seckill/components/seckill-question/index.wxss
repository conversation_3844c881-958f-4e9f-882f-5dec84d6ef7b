/* .question-pop-up {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, .5);
  z-index: 100;
} */

.question-dialog {
  width: 620rpx;
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.question-dialog .icon-close {
  position: absolute;
  right: 40rpx;
  top: 40rpx;
  line-height: 0;
}

.dialog-title-wp {
  background: linear-gradient(30deg, #f44, #fb7344);
  height: 120rpx;
  width: 100%;
}

.question-dialog .dialog-title {
  position: relative;
  width: 100%;
  height: 120rpx;
  background-image: url("https://img.yzcdn.cn/weapp/seckill/head.png");
  background-size: 620rpx 120rpx;
  text-align: center;
  line-height: 120rpx;
  color: white;
  font-size: 16px;
}

.question-dialog .dialog-desc-wp {
  padding: 40rpx 30rpx;
}

.question-dialog .question-name {
  margin-bottom: 40rpx;
}

.question-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  height: 68rpx;
  border-radius: 34rpx;
  padding: 0 30rpx;
  line-height: 68rpx;
  background: #f2f2f2;
  color: #666;
  font-size: 14px;
  margin-bottom: 30rpx;
}

.question-item .item-check-icon {
  color: #44ba00;
}

.question-item .item-wrong-icon {
  width: 12px;
  height: 12px;
  background: url("https://img.yzcdn.cn/weapp/seckill/wrong-icon.png") no-repeat;
  background-position: center;
  background-size: 12px 12px;
}

.question-item.active .item-check-icon {
  visibility: visible;
}

.question-item.wrong {
  background: rgba(255, 68, 68, .1);
  color: #f44;
}

.question-item.wrong .item-wrong-icon {
  visibility: visible;
}

.dialog-click-btn {
  height: 90rpx;
  widows: 550rpx;
  margin: 0 10rpx;
  border-radius: 45rpx;
  text-align: center;
  line-height: 90rpx;
  font-size: 16px;
  margin-top: 40rpx;
}

.wrong-face {
  width: 52rpx;
  height: 48rpx;
  vertical-align: middle;
}
