import Toast from '@vant/weapp/dist/toast/toast';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapMutations, mapActions } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapState({
      isPopupShow: (state) => state.seckillQuestion.isPopupShow,
      seckillQuestion: (state) => state.seckillQuestion || {},
    }),
  },

  methods: {
    ...mapMutations([
      'updateSeckillQuestion',
      'hideSeckillQuestion',
    ]),
    ...mapActions([
      'bookSeckill',
    ]),

    closePopup() {
      this.hideSeckillQuestion();
    },

    // 选择某项
    chooseAnswer(e) {
      if (!e || !e.currentTarget) return;
      let dataset = e.currentTarget.dataset || {};
      this.updateSeckillQuestion({
        selectedKey: dataset.key,
        wrongKey: ''
      });
    },

    confirmAnswer() {
      const {
        selectedKey,
        answerKey
      } = this.data.seckillQuestion;

      if (!selectedKey) {
        return Toast({
          message: '请选择答案',
          context: this
        });
      }
      if (selectedKey !== answerKey) {
        return this.updateSeckillQuestion({
          wrongKey: selectedKey,
          selectedKey: ''
        });
      }
      // 回答正确则提交答案预约秒杀
      this.bookSeckill();
    }
  }
});
