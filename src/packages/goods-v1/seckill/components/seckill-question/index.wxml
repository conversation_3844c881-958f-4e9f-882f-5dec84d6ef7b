  <van-toast id="van-toast" />
  <van-popup
    z-index="{{ 2 }}"
    show="{{ isPopupShow }}"
    class="question-pop-up" 
    bind:click-overlay="closePopup" 
    close-on-click-overlay >
    <view class="question-dialog">
      <view class="dialog-title-wp">
        <view class="dialog-title">
          <image
            class="wrong-face"
            wx:if="{{ seckillQuestion.wrongKey !== '' }}"
            src="https://b.yzcdn.cn/weapp/seckill/wrong-face.png" />
          {{ seckillQuestion.wrongKey === '' ? '答对问题，完成预约' : '哎哟，答案不对哦' }}
          <icon class="icon-close" color="#fff" type="cancel" size="20" bind:tap="closePopup" />
        </view>
      </view>
      <view class="dialog-desc-wp">
        <view class="question-name">请听题，{{ seckillQuestion.title }}</view>
        <view
          wx:for="{{ seckillQuestion.ItemList }}"
          wx:key="{{ item.key }}"
          class="question-item {{ seckillQuestion.selectedKey === item.key ? 'active' : '' }} {{ seckillQuestion.wrongKey === item.key ? 'wrong' : '' }}"
          bind:tap="chooseAnswer"
          data-key="{{ item.key }}" >
          <text>{{ item.key }}. {{ item.content }}</text>
          <view
            wx:if="{{ seckillQuestion.wrongKey === item.key }}"
            class="item-wrong-icon"></view>
          <van-icon
            name="success"
            class="item-check-icon"
            wx:if="{{ seckillQuestion.selectedKey === item.key }}" />
        </view>
        <van-button block loading="{{  seckillQuestion.isBookLoading }}" type="danger" bind:tap="confirmAnswer">
          提交答案
        </van-button>
      </view>
    </view>
  </van-popup>