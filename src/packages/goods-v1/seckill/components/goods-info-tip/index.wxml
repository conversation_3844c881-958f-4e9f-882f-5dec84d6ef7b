<goods-info-tip-view
  is-link="{{ goodsTipInfo.isLink }}"
  title="秒杀"
  link-tip="玩法详情"
  bind:tap:cell="showPopup"
  desc="{{ goodsTipInfo.desc }}"
/>
<goods-popup
  show="{{ isPopupShown }}"
  title="秒杀规则"
  theme-class="{{ themeClass }}"
  bind:tap:btn="closePopup"
>
  <view class="seckill-tip__container">
    <view 
      wx:for="{{ goodsTipInfo.rules }}"
      wx:key="{{ index }}">
      {{ index + 1 }}. {{ item }}
    </view>
  </view>
</goods-popup>