import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapGetters } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapGetters([
      'goodsTipInfo',
    ]),
  },

  data: {
    isPopupShown: false,
  },

  methods: {
    showPopup() {
      const { goodsTipInfo = {} } = this.data;
      if (goodsTipInfo.isLink) {
        this.setYZData({
          isPopupShown: true
        });
      }
    },

    closePopup() {
      this.setYZData({
        isPopupShown: false
      });
    }
  }
});
