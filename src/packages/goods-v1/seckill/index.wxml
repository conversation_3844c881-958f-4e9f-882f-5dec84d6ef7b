<import src="/components/zan-account/bind-phone-number/index.wxml"/>

<view wx:if="{{ showBindPhoneNumber }}" class="bind-phone-container" style="min-height: 0">
  <template is="bind_phone_number" data="{{ bindTips }}"></template>
</view>

<detail-base
  showGoods="{{showGoods}}"
  class="youzan-{{ appType }} {{ themeClass }} page-{{ deviceType }}"
>
  <goods-info-tip slot="goods-info-tip" />
  <view slot="act-custom-block">
    <seckill-question/>
  </view>

</detail-base>

<account-login
  wx:if="{{ accountLogin }}"
  show="{{ accountLogin }}"
  bind:loginSuccess="onAccountSuccess"
  bind:closeAccountLogin="onAccountClose"
></account-login>
