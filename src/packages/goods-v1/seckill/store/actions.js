
import get from '@youzan/weapp-utils/lib/get';
import { SKU_SCENE } from 'shared/packages/goods/common/constant';

const app = getApp();

export default {
  // 预约秒杀
  makeReservation({ commit, state, dispatch }) {
    const activity = get(state, 'marketActivity.seckill', {});
    const questionId = activity.questionId || '';
    const useFollow = activity.useFollow || '';
    const useQuestion = activity.useQuestion || '';

    if (!app.getBuyerId()) {
      commit('updateSeckillAccount', {
        showBindPhoneNumber: true,
        bindTips: '绑定手机号方可预约'
      });
      return;
    }

    if (useQuestion) {
      commit('showSeckillQuestion');
      app.carmen({
        api: 'youzan.ump.seckill.question/1.0.0/get',
        query: {
          question_id: questionId,
        },
        success: (res) => {
          commit('updateSeckillQuestion', {
            wrongKey: '',
            title: res.title,
            ItemList: res.options || [],
            answerKey: res.answer_key
          });
        },
        fail: () => {
          wx.showToast({
            title: '获取问题失败',
            icon: 'none',
            duration: 2000
          });
          commit('hideSeckillQuestion');
        }
      });
      return;
    }

    if (useFollow) {
      wx.showToast({
        title: '小程序暂不支持关注公众号后预约秒杀',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 既不需要回答问题，又不需要分享，直接预约
    dispatch('bookSeckill');
  },
  // 提交答案预约秒杀
  bookSeckill({ commit, state }) {
    const activity = get(state, 'marketActivity.seckill', {});
    const seckillId = activity.activityId || '';

    commit('updateSeckillQuestion', {
      isBookLoading: true
    });

    wx.showLoading({ title: '数据提交中...' });
    app.carmen({
      api: 'youzan.ump.seckill.book/1.0.0/create',
      query: {
        seckill_id: seckillId,
      },
      success: (res) => {
        if (res) {
          wx.showToast({
            title: '预约成功',
            icon: 'none',
            duration: 2000,
          });
          setTimeout(() => {
            wx.startPullDownRefresh();
          }, 500);
        }
      },
      fail: () => {
        wx.showToast({
          title: '预约失败',
          icon: 'none',
          duration: 2000,
        });
      },
      complete: () => {
        wx.hideLoading();
        commit('updateSeckillQuestion', {
          isBookLoading: false,
          isPopupShow: false,
        });
      }
    });
  },

  // 显示SKU浮窗
  showSkuPop({
    getters, commit, dispatch,
  }, event = null) {
    // 如果不是原价购买
    if (event && event.skuScene !== SKU_SCENE.NORMAL_BUY && event.skuScene !== SKU_SCENE.SEL_SKU) {
      // 需要判断活动是否开始，是否有预约权限等
      if (getters.needOrderButNot || getters.isActivityNotStart) {
        return;
      }
    }

    if (!event || !event.skuScene) {
      event = {
        ...event,
        skuScene: SKU_SCENE.ACT_BUY,
      };
    }
    if (event) {
      // 这里有先后顺序，extraData会依赖config中的参数，判断skuData
      dispatch('updateSkuConfig', event);
      dispatch('updateSkuExtraData', event);
    }
    // 存在特定场景才显示sku
    commit('showSkuPop');
  },
};
