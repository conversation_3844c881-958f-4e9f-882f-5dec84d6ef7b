import get from '@youzan/weapp-utils/lib/get';
import { splitPrice } from 'shared/packages/goods/store/utils/helpers';
import { SKU_SCENE, ACCOUNT_UNION_SCENE } from 'shared/packages/goods/common/constant';
import {
  getBottomViewConfig,
} from 'shared/packages/goods/containers/goods-bottom/store/common';

export default {
  isSeckill(state) {
    return !!get(state, 'marketActivity.seckill', null);
  },

  activityTitle(state) {
    return get(state, 'marketActivity.seckill.tag', '秒杀');
  },

  isStarted(state) {
    const startTime = get(state, 'marketActivity.seckill.startAt', 0);
    return Date.now() - startTime >= 0;
  },

  // 秒杀库存
  goodsStockNum(state) {
    return get(state, 'marketActivity.seckill.sku.stockNum', 0);
  },

  /* ============ activity-banner ========== */
  showActivityBanner() {
    return true;
  },

  countdown(state, getters) {
    const activity = get(state, 'marketActivity.seckill', {});
    const { startAt, endAt } = activity;
    const now = getters.nowDate;
    let desc;
    let end;
    if (now < startAt) {
      desc = '距开始';
      end = startAt;
    } else if (now < endAt) {
      desc = '距结束仅剩';
      end = endAt;
    } else {
      desc = '活动已结束';
      end = now;
    }

    return {
      desc,
      end,
      start: now
    };
  },

  /* ============ goods-info ============== */

  goodsPrice(state) {
    const activity = get(state, 'marketActivity.seckill', {});
    const { priceDateRange, goods } = state;
    const {
      price: currentPrice,
      minPrice,
      maxPrice,
    } = activity.sku || {};
    const {
      price: originPrice,
    } = state.sku || {};
    const {
      isVirtualTicket,
    } = goods;
    const isRange = minPrice !== maxPrice;

    // 电子卡券加上价格日历特殊逻辑
    if (isVirtualTicket && priceDateRange) {
      return {
        currentPrice: priceDateRange
      };
    }

    return {
      isRange,
      currentPrice,
      minPriceData: splitPrice(minPrice),
      maxPriceData: splitPrice(maxPrice),
      originPrice,
    };
  },

  /* ============ goods-info-tip =========== */

  goodsTipInfo(state, getters) {
    const activity = get(state, 'marketActivity.seckill', {});
    const quota = get(activity, 'limit.num', 0);
    const {
      // ?? 后端还没给
      cancelTime,
      bookingNumText = 0,
    } = activity;
    const { needOrderButNot } = getters;

    let desc = '';
    const rules = [];

    desc += quota ? `每人限购${quota}件。` : '';
    desc += cancelTime ? `订单${cancelTime}分钟内未支付将被取消。` : '';

    desc && rules.push(desc);
    needOrderButNot && rules.push(`只有预约成功后才有资格参加秒杀，已有${bookingNumText}人预约成功。`);

    return {
      isLink: rules.length > 0,
      desc,
      rules,
    };
  },

  /* ============ goods-bottom ============= */
  // 秒杀主按钮展示
  goodsBottomView(state, getters) {
    // useFollow 使用公众号完成预约
    // useQuestion 使用答题完成预约

    const activity = get(state, 'marketActivity.seckill', {});
    // isCheckRight 开启秒杀预约
    // isPassCheck 已经预约秒杀
    const {
      isCheckRight, startAt, endAt
    } = activity;
    const now = getters.nowDate;

    let canSeckill = false;
    if (now > startAt && now < endAt) {
      // 活动已进行
      canSeckill = true;
    }

    if (now > endAt) {
      // 活动已结束
      return {
        statusBar: {
          text: '秒杀活动已结束，去看看其他商品吧',
        },
      };
    }

    const btnInfo = getBottomViewConfig(state, getters);
    if (btnInfo) {
      return btnInfo;
    }

    // 需要预约且未预约
    const needOrderButNot = getters.needOrderButNot;

    const btnMain = {
      show: true,
      text: '零售价购买',
      skuScene: SKU_SCENE.NORMAL_BUY,
      accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
    };

    const btnVice = {
      show: !canSeckill && needOrderButNot,
      text: '零售价购买',
      skuScene: SKU_SCENE.SEL_SKU,
      accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
    };

    const statusBar = {};

    if (needOrderButNot && canSeckill) {
      btnMain.text = '零售价购买';
      btnMain.skuScene = SKU_SCENE.NORMAL_BUY;
      btnMain.accountUnionScene = ACCOUNT_UNION_SCENE.NORMAL_BUY;
      statusBar.canBuy = true;
      statusBar.text = '你未预约此活动，无法参加秒杀，下次记得预约哦';
    } else if (needOrderButNot) {
      // needOrderButNot and not canSeckill
      btnMain.text = '立即预约';
      btnMain.skuScene = SKU_SCENE.RESERVATION;
      btnMain.accountUnionScene = ACCOUNT_UNION_SCENE.NORMAL_BUY;
    } else if (canSeckill) {
      btnMain.text = '立即拿下';
      btnMain.skuScene = SKU_SCENE.ACT_BUY;
      btnMain.accountUnionScene = ACCOUNT_UNION_SCENE.NORMAL_BUY;
      btnVice.show = false;
    } else if (isCheckRight) {
      // not canSeckill and isCheckRight and isPassCheck
      statusBar.canBuy = true;
      statusBar.text = '您已成功预约，活动暂未开始，土豪也可零售价购买哦';
    } else {
      statusBar.canBuy = true;
      statusBar.text = '活动尚未开始';
    }

    return {
      main: btnMain,
      vice: btnVice,
      statusBar,
    };
  },


  isSeckillBuy(state) {
    return state.skuConfig.skuScene === SKU_SCENE.ACT_BUY;
  },

  /* ============ goods-sku ============= */
  // 用于重写的sku数据，秒杀
  skuData(state, getters) {
    const activity = get(state, 'marketActivity.seckill', {});
    return getters.isSeckillBuy ? activity.sku : state.sku;
  },

  activitySkuData(state) {
    return get(state, 'marketActivity.seckill.sku', {});
  },

  /* ============ 限购 =============== */
  commonQuota(state, getters) {
    const activity = get(state, 'marketActivity.seckill', {});
    return getters.isSeckillBuy ? activity.limit.num : state.limit.num;
  },

  quotaUsed(state, getters) {
    const activity = get(state, 'marketActivity.seckill', {});
    return getters.isSeckillBuy ? activity.limit.used : state.limit.used;
  },

  quotaCycle(state, getters) {
    return getters.isSeckillBuy ? 0 : getters.commonQuotaPeriod;
  },

  // 联系客服卡片额外参数，表示卡片来源，这里是秒杀
  imFrom() {
    return 'SECKILLGOODS';
  },

  isActivityNotStart(state, getters) {
    const activity = get(state, 'marketActivity.seckill', {});
    const { startAt } = activity;
    const now = getters.nowDate;

    return now < startAt;
  },

  // 需要预约但未预约
  needOrderButNot(state) {
    const activity = get(state, 'marketActivity.seckill', {});
    const {
      isCheckRight,
      isPassCheck,
    } = activity;

    return isCheckRight && !isPassCheck;
  },

  activityInfoForPrice(state) {
    return {
      type: 'seckill',
      identification: state.pageParams.alias,
    };
  },
};
