import { VanxPage } from 'pages/common/wsc-page/index';
import goodsPageInits from '../common/goods-page';
import goodsPageSaas from '../common/goods-page-saas';
import store from './store';

const extendPageConfig = ECLOUD_MODE ? goodsPageSaas : {};

VanxPage(goodsPageInits, extendPageConfig, {
  currentPageType: 'present',

  store(query) {
    const pageTime = (new Date()).getTime();
    store.name = `present-${query.alias}-${pageTime}`;
    return store;
  },

  onGoodsInited() {
    console.log('oninited', this.$store.state);
  }
});
