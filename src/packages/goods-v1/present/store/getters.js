import get from '@youzan/weapp-utils/lib/get';
import { splitPrice } from 'shared/packages/goods/store/utils/helpers';
import { SKU_SCENE, ACCOUNT_UNION_SCENE } from 'shared/packages/goods/common/constant';

export default {
  /* ============ activity-banner ========== */
  showActivityBanner() {
    return false;
  },

  /* ============ goods-info ============== */
  goodsPrice() {
    const minPrice = 0;
    const maxPrice = 0;

    return {
      isRange: false,
      currentPrice: '0.00',
      minPriceData: splitPrice(minPrice),
      maxPriceData: splitPrice(maxPrice),
      originPrice: '',
    };
  },

  // 是否显示运费
  showPostage() {
    return false;
  },

  /* ============ goods-bottom ============= */
  // 首页，店铺
  homeBtnView() {
    return {
      show: false, // isSetHome 是否显示店铺首页
    };
  },
  // 客服
  imBtnView() {
    return {
      show: false, // isSetContact 是否显示客服
    };
  },
  // 购物车
  miniCartBtnView() {
    return {
      show: false,
    };
  },
  giftBtnView() {
    return {
      show: false,
    };
  },
  // 零元抽奖拼团主按钮展示
  goodsBottomView() {
    return {
      main: {
        text: '领取赠品',
        show: true,
        loading: false,
        skuScene: SKU_SCENE.PRESENT_BUY,
        accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
      },
      vice: {},
    };
  },

  /* ============ goods-sku ============= */
  // 购买按钮文案
  skuBuyBtnText() {
    return '下一步';
  },

  // 用于重写的sku数据
  skuData(state) {
    const sku = state.sku || {};
    const presentExchange = get(state, 'marketActivity.presentExchange', {});
    const skuId = get(presentExchange, 'present.skuId');
    return {
      ...sku,
      list: sku.list instanceof Array ? sku.list.map(item => {
        if (item.id !== skuId) {
          item.stockNum = 0;
        } else {
          item.stockNum = Math.min(item.stockNum, 1);
        }
        item.price = item.discountPrice;
        return item;
      }) : [],
      price: '0.00',
      hideStock: true,
    };
  },

  /* ============ 限购 =============== */
  quota() {
    return 1;
  },

  startSaleNum() {
    return 1;
  }

};
