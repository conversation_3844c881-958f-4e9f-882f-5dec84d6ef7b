import args from '@youzan/weapp-utils/lib/args';
import get from '@youzan/weapp-utils/lib/get';
import {
  getBottomViewConfig,
} from 'shared/packages/goods/containers/goods-bottom/store/common';
import { SKU_SCENE, ACCOUNT_UNION_SCENE } from 'shared/packages/goods/common/constant';

export default {
  /* ============ activity-banner ========== */
  showActivityBanner() {
    return true;
  },

  activityTitle() {
    return '0元抽奖团';
  },

  isActivityNotStart(state) {
    const activity = get(state, 'marketActivity.luckyDrawGroup', {});
    return activity.buttonType === 3;
  },

  isActivityEnded(state) {
    const activity = get(state, 'marketActivity.luckyDrawGroup', {});
    return activity.buttonType === 4;
  },

  countdown(state, getters) {
    const activity = get(state, 'marketActivity.luckyDrawGroup', {});
    const { startTime, endTime, ownGroup } = activity;
    const { isActivityNotStart, isActivityEnded } = getters;
    const now = +new Date();
    let desc;
    let end;
    // ?? 活动已结束，buttonType为1
    if (isActivityNotStart || now < startTime) {
      desc = '距活动开始';
      end = startTime;
    } else if (isActivityEnded || now > endTime) {
      desc = '活动已结束';
      end = now;
    } else if (ownGroup) {
      desc = '距开奖仅剩';
      end = endTime;
    } else {
      desc = '距结束仅剩';
      end = endTime;
    }

    return {
      desc,
      end,
      start: now
    };
  },

  // 库存
  goodsStockNum(state) {
    return get(state, 'marketActivity.luckyDrawGroup.sku.stockNum', 0);
  },

  /* ============ goods-info-tip =========== */
  // 参团人数
  activityJoinNum(state) {
    const activity = get(state, 'marketActivity.luckyDrawGroup', {});
    return activity.joinNum || activity.participantNum || 0;
  },

  // 代金券名称
  couponName(state) {
    const activity = get(state, 'marketActivity.luckyDrawGroup', {});
    return activity.couponName || '';
  },

  /* ============ goods-bottom ============= */
  // 零元抽奖拼团主按钮展示
  goodsBottomView(state, getters) {
    const { luckyDrawGroup: activity } = state.marketActivity;

    const currentTime = new Date().getTime();
    // 活动已结束
    if (activity.endTime < currentTime) {
      return {
        statusBar: {
          text: '查看中奖结果',
          link: `/packages/collage/lottery/result/index?activity_id=${activity.activityId}`,
        },
      };
    }

    // 活动即将开始
    if (activity.startTime > currentTime) {
      return {
        statusBar: {
          text: '活动即将开始',
        },
      };
    }

    // 拼团里btnInfo如果活动已结束，不能覆盖上面的查看中奖结果
    const btnInfo = getBottomViewConfig(state, getters);
    if (btnInfo) {
      return btnInfo;
    }

    const btnMain = {
      show: true,
      text: '立即0元开团',
      buy: true,
      isUmp: true,
      loading: false,
      skuScene: SKU_SCENE.ACT_BUY,
      accountUnionScene: ACCOUNT_UNION_SCENE.GROUP_BUY,
    };

    // ?? 这段代码测一下
    if (activity.ownGroup) {
      btnMain.text = '查看我的团';
      btnMain.navigate = true;
      btnMain.path = args.add(
        '/packages/collage/lottery/detail/index',
        {
          group_alias: activity.ownGroup.alias,
          activity_id: activity.activityId
        }
      );
    } else {
      btnMain.subscribeScene = 'lottery';
    }
    return { main: btnMain };
  },

  /* ============ goods-sku ============= */
  // 用于重写的sku数据
  skuData(state) {
    const activity = get(state, 'marketActivity.luckyDrawGroup', {});
    return activity.sku;
  },

  activityInfoForPrice(state) {
    const query = args.getAll(state.pageParams.path) || {};
    const activityId = query.activityId || '';
    return {
      type: 'luckyDrawGroup',
      identification: activityId,
    };
  },
};
