import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapGetters, mapState } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapGetters([
      'activityJoinNum',
      'couponName',
    ]),
    ...mapState({
      goodsTitle: (state) => state.goods.title,
      shopName: (state) => state.shop.name,
      shopPhone: (state) => state.shop.phone,
    }),
  },

  data: {
    isPopupShown: false,
  },

  methods: {
    showPopup() {
      this.setYZData({
        isPopupShown: true
      });
    },

    closePopup() {
      this.setYZData({
        isPopupShown: false
      });
    }
  }
});
