import args from '@youzan/weapp-utils/lib/args';
import Event from '@youzan/weapp-utils/lib/event';
import openWebView from 'utils/open-web-view';
import getUniquePageKey from 'shared/utils/unique';

const p = 'packages';
const pageRedirectMap = {
  // 零元拼团
  luckyDrawGroup: {
    type: 'lucky-draw-group',
    path: `/${p}/goods/lucky-draw-group/index`
  },
  helpCut: {
    type: 'help-cut',
    path: `/${p}/goods/help-cut/index`
  },
  groupOn: {
    type: 'groupon',
    path: `/${p}/goods/groupon/index`
  },

  ladderGroupOn: {
    type: 'groupon',
    path: `/${p}/goods/groupon/index`
  },
  tuan: {
    type: 'tuan',
    path: `/${p}/goods/tuan/index`
  },
  presentExchange: {
    type: 'present',
    path: `/${p}/goods/present/index`
  },
  pointsExchange: {
    type: 'points',
    path: `/${p}/goods/points/index`
  },
  gifts: {
    type: 'gifts',
    path: `/${p}/goods/points/index`
  },
  seckill: {
    type: 'seckill',
    path: `/${p}/goods/seckill/index`
  },
  normal: {
    type: 'normal',
    path: '/pages/goods/detail/index'
  },
  fastbuy: {
    type: 'fastbuy',
    path: `/${p}/independent/goods/fastbuy/index`
  }
};

// 0 使用店铺设置， 1：使用商品设置
const GOODS_MODEL_SWITCH = {
  byShop: 0,
  byGoods: 1,
};

// 0 普通，1 极速 2 分步极速
const PAGE_MODE = {
  normal: 0,
  fastbuy: 1,
  splitFastbuy: 2
};

/**
 * itemPageMode, paymentSettingOrderMode 店铺配置下单模式 // 0 标准，1 极速 2 分步极速
*/
function parseFastBuy(goodsData) {
  const { template = {}, shop } = goodsData;
  const { modelSwitchConfig, itemPageMode = PAGE_MODE.normal } = template;
  const { paymentSettingOrderMode = PAGE_MODE.normal } = shop.config;

  let isFastBuy = false;
  let isFastBuySplit = false;

  // 非实物商品的话，后端会不传这个字段
  if (modelSwitchConfig === undefined
    || (!modelSwitchConfig && +modelSwitchConfig !== 0)
  ) {
    return {
      isFastBuy,
      isFastBuySplit
    };
  }

  if (+modelSwitchConfig === GOODS_MODEL_SWITCH.byGoods) {
    if (+itemPageMode === PAGE_MODE.fastbuy) {
      isFastBuy = true;
    } else if (+itemPageMode === PAGE_MODE.splitFastbuy) {
      isFastBuy = true;
      isFastBuySplit = true;
    }
  } else if (+paymentSettingOrderMode === PAGE_MODE.fastbuy) {
    isFastBuy = true;
  } else if (+paymentSettingOrderMode === PAGE_MODE.splitFastbuy) {
    isFastBuy = true;
    isFastBuySplit = true;
  }

  return {
    isFastBuy,
    isFastBuySplit
  };
}

// 判断是否是新酒店商品（bizCode是00000035， ability_mark_code_list 是 10013），新酒店商品需要跳转到酒店详情页
function parseNewHotel(goodsData) {
  const { goods = {} } = goodsData;
  const { abilityMarkCodeList = [], bizCode = '' } = goods;
  if (bizCode === '000000000035' && abilityMarkCodeList.indexOf(10013) > -1) {
    return [true, `/wscindustry/hotel/goods?kdtId=${goods.kdtId}&itemId=${goods.id}`];
  }
  return [false];
}

export default {
  checkGoodsRedirect({ parsedActivities = {}, goods = {} }, pagetype) {
    // 判断如果是新酒店商品，则直接跳转
    const [isNewHotel, path] = parseNewHotel(goods);
    if (isNewHotel) {
      openWebView(path, {
        method: 'redirectTo',
      });
      return true;
    }

    const { isFastBuy } = parseFastBuy(goods);
    let pageKey = '';

    // 极速下单跳转;
    if (isFastBuy) {
      pageKey = 'fastbuy';
    } else {
      const pageTypeKeys = Object.keys(pageRedirectMap);
      pageKey = pageTypeKeys.find((activityType) => {
        if (parsedActivities[activityType]) {
          return true;
        }
        return false;
      }) || 'normal';
    }

    // ?? 待添加 送礼商品屏蔽拼团商品
    const pageItem = pageRedirectMap[pageKey];
    const pageType = pageItem.type;

    if (pagetype === pageType) {
      return false;
    }
    console.log('start redirect', args.add(pageItem.path, this.options));
    // return;
    // 先关掉跳转，方便调试
    wx.redirectTo({
      url: args.add(pageItem.path, this.options)
    });
    return true;
  },

  onPageScroll(e) {
    Event.trigger('onPageScroll', e);
    Event.trigger('onPageScroll' + getUniquePageKey(this), e);
  },

  // 下拉刷新
  onPullDownRefresh(e) {
    this.trigger('home:refresh');
    Event.trigger('onPullDownRefresh', e);
    Event.trigger('onPullDownRefresh' + getUniquePageKey(this), e);
    let pageType = this.currentPageType;
    const { type } = this.options;
    if (type === 'gift') {
      pageType = type;
    }
    this.fetchGoodsDetail({
      pageType,
      forceRefresh: true
    });
    wx.stopPullDownRefresh();
  },

  onReachBottom(e) {
    Event.trigger('onReachBottom', e);
    Event.trigger('onReachBottom' + getUniquePageKey(this), e);
  }
};
