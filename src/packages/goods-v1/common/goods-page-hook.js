
// 将所有回调包装
function promiseAll(list, data) {
  return Promise.all((list || []).map(it => it(data)));
}

// 将sku的留言部分格式化为 { 留言名称: 留言值 } 数组形式
function makeSkuMessageToMap1(sku = {}, postMessages = {}) {
  const { messages = [] } = sku;
  const result = {};
  messages.forEach((it, index) => {
    result[it.name] = postMessages[index] || '';
  });
  return result;
}

// 将sku的留言部分格式化为 { 留言名称: 留言值 } 对象形式
function makeSkuMessageToMap2(sku = {}, info = {}) {
  const { messages = [] } = sku;
  const result = {};
  messages.forEach((it, index) => {
    result[it.name] = info[`message_${index}`] || '';
  });
  return result;
}

export default {
  _beforeCartSubmitEventArray: [],
  _afterBuyEventArray: [],
  _beforeBuyEventArray: [],
  addBeforeCartSubmitEvent(promiseFunc) {
    this._beforeCartSubmitEventArray.push(promiseFunc);
  },
  addAfterBuyEvent(promiseFunc) {
    this._afterBuyEventArray.push(promiseFunc);
  },
  addBeforeBuyEvent(promiseFunc) {
    this._beforeBuyEventArray.push(promiseFunc);
  },
  beforeCartSubmitEvent(data) {
    const { skuData = {}, goods = {} } = this.data;
    const { num, skuId, cartMessages } = data;
    const infoData = {
      num,
      skuId,
      messages: makeSkuMessageToMap1(skuData, cartMessages),
      goodsAlias: goods.alias,
    };
    return promiseAll(this._beforeCartSubmitEventArray, infoData);
  },
  afterBuyEvent(data) {
    const { url, bookKey, goodsList = [] } = data;
    const { skuData = {}, goods = {} } = this.data;
    const sku = goodsList[0] || {};
    const infoData = {
      buyUrl: url,
      bookKey,
      goodsAlias: goods.alias,
      skuId: sku.sku_id,
      num: sku.num || 1,
      messages: makeSkuMessageToMap2(skuData, sku),
    };
    return promiseAll(this._afterBuyEventArray, infoData);
  },
  beforeBuyEvent(data) {
    const { skuData = {}, goods = {} } = this.data;
    const { num, skuId } = data;
    const infoData = {
      num,
      skuId,
      messages: makeSkuMessageToMap2(skuData, data),
      goodsAlias: goods.alias,
    };
    return promiseAll(this._beforeBuyEventArray, infoData);
  }
};
