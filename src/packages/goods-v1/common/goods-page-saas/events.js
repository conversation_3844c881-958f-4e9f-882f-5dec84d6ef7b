import { AsyncEvent } from '@youzan/weapp-ecloud-sdk';

export default function injectEvents(bridge, ctx) {
  const beforeCartSubmitEvent = new AsyncEvent();
  const afterBuyEvent = new AsyncEvent();
  const beforeBuyEvent = new AsyncEvent();

  ctx.addBeforeCartSubmitEvent && ctx.addBeforeCartSubmitEvent(data => beforeCartSubmitEvent.trigger(data));
  ctx.addAfterBuyEvent && ctx.addAfterBuyEvent(data => afterBuyEvent.trigger(data));
  ctx.addBeforeBuyEvent && ctx.addBeforeBuyEvent(data => beforeBuyEvent.trigger(data));
  bridge.setPageEvent('beforeCartSubmit', beforeCartSubmitEvent);
  bridge.setPageEvent('beforeBuy', beforeBuyEvent);
  bridge.setPageEvent('afterBuy', afterBuyEvent);
}
