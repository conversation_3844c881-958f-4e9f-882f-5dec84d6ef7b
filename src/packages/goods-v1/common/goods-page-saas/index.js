import { mapActions, mapMutations } from '@youzan/vanx';
import getApp from 'shared/utils/get-safe-app';
import injectPageData from './page-data';
import injectEvents from './events';
import injectProcess from './process';

export default {
  ...mapActions(['showSkuPop']),
  ...mapMutations(['saasUpdateComponent', 'saasSkuCache', 'setShareParams']),
  getYunSdk() {
    return getApp().getYouZanYunSdk();
  },
  onLoad() {
    this.initSaasData && this.initSaasData();
  },
  initSaasData() {
    const yunSDK = this.getYunSdk();
    injectPageData(yunSDK, this);
    injectEvents(yunSDK, this);
    injectProcess(yunSDK, this);

    yunSDK.page.updateComponent = (name, display, properties) => {
      this.saasUpdateComponent({ name, display, properties });
    };
  }
};
