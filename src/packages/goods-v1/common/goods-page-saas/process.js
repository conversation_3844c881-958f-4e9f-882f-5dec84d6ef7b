import { SKU_SCENE } from 'shared/goods/common/constant';

const app = getApp();
const APPID = app.getAppId();
const showSKUExchange = {
  selectSku: SKU_SCENE.SEL_SKU,
  addCart: SKU_SCENE.ADD_CART,
  buy: SKU_SCENE.NORMAL_BUY,
  gift: SKU_SCENE.GIFT_BUY,
  point: SKU_SCENE.POINTS_BUY,
  present: SKU_SCENE.PRESENT_BUY,
  actBuy: SKU_SCENE.ACT_BUY,
  saas: SKU_SCENE.SAAS
};

const injectProcess = function (bridge, ctx) {
  // 弹出sku流程
  bridge.setPageProcess('showSKU', (type, more = {}) => {
    if (showSKUExchange[type]) {
      // 云定制情况下，需要传特殊的sku信息
      if (type === 'saas') {
        if (!more.sku || !more.cloudOrderExt) {
          return Promise.reject('云定制时，需要传特殊sku信息以及cloudOrderExt标');
        }
        ctx.saasSkuCache(more);
      }

      // 云测试店铺P5rBu191214200114，appid:wx85c2d982288c170a;
      // 乐乐茶百货公司, appid:wxe3ff1accd18d7c29
      // 心仪画舫 wxf11c5910cb729a82
      // 特殊性支持大客，
      if (
        type === 'addCart' &&
        ['wx85c2d982288c170a', 'wxe3ff1accd18d7c29', 'wxf11c5910cb729a82'].indexOf(APPID) !== -1
      ) {
        if (!more.sku) {
          return Promise.reject('云定制时，需要传特殊sku信息以及cloudOrderExt标');
        }
        ctx.saasSkuCache({ ...more, useSassAddCart: true });
      }

      return ctx.showSkuPop({
        skuScene: showSKUExchange[type]
      });
    }
    return Promise.reject('不支持展示该类型SKU');
  });

  bridge.setPageProcess('getISVExt', () => {
    return ctx.options.isv_ext;
  });

  bridge.setPageProcess('setPageShareParams', (args = {}) => {
    const { isvExt } = args;
    return ctx.setShareParams({ isv_ext: isvExt });
  });

  // 加入购物车流程
  bridge.setPageProcess('addCart', (infos = {}) => {
    const { goods = {} } = ctx.state;
    const { skuData = {} } = ctx.getters;
    const data = {
      goodsId: goods.id,
      messages: {},
      selectedNum: infos.skuNum || 1,
      selectedSkuComb: {},
      noToast: !!infos.noToast
    };
    (infos.messages || []).forEach((it, ind) => {
      data.messages[`message_${ind}`] = it || '';
    });
    if (skuData.none_sku) {
      data.selectedSkuComb.id = skuData.collection_id;
      data.selectedSkuComb.price = skuData.collection_price;
      data.selectedSkuComb.stock_num = skuData.stock_num;
    } else if (+infos.skuId) {
      const sku = (skuData.list || []).find((it) => it.id === +infos.skuId);
      if (!sku) {
        return Promise.reject('未找到对应SKU信息');
      }
      Object.assign(data.selectedSkuComb, sku);
    } else {
      return Promise.reject('未传skuId');
    }
    return ctx.dispatch('submitAddCart', data);
  });
};

export default injectProcess;
