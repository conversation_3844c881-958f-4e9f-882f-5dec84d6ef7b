import get from '@youzan/weapp-utils/lib/get';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

const defineProperty = function (obj, key, value) {
  obj[mapKeysCase.toCamelCase(key)] = value;
};

const getInfoFromObj = function (obj, infoKey, keys) {
  const info = (infoKey ? get(obj, infoKey) : obj) || {};
  const result = {};
  keys.forEach(it => defineProperty(result, it, info[it]));
  return result;
};

// 商品基本信息
const getGoodsItem = function (state) {
  const result = getInfoFromObj(state, 'goods', [
    'alias', 'title', 'picture', 'sellPoint', 'origin', 'isVirtual', 'isVirtualTicket', 'isDisplay', 'limitBuy',
    'waitToSoldTime', 'buyWay', 'buyUrl', 'forbidBuyReason', 'isSupportFCode', 'isInstallment'
  ]);
  // 小程序不支持
  defineProperty(result, 'isGoodsCollected', false);
  defineProperty(result, 'risk', getInfoFromObj(state, 'goods.risk', ['match', 'note']));
  return result;
};

// 获取退款相关配置
const getRefund = function (state) {
  return getInfoFromObj(state, 'refund', ['isSupport', 'type', 'interval']);
};

// 获取多网点信息
const getMultistore = function (state) {
  return getInfoFromObj(state, 'multistore', ['name']);
};

// 获取店铺信息
const getShop = function (state) {
  const result = getInfoFromObj(state, 'shop', ['logo', 'name', 'url', 'certType']);
  // 小程序不需要url
  defineProperty(result, 'url', '');
  return result;
};

// 获取店铺配置信息
const getShopConfig = function (state) {
  const result = getInfoFromObj(state, 'shopConfig', [
    'isSecuredTransactions', 'showRecommendGoods', 'showBuyRecord', 'showCustomerReviews',
    'supportFreightInsurance', 'hideShoppingCart', 'hasPhysicalStore'
  ]);
  // 小程序字段名字不一样
  const showBuyBtn = get(state, 'shopConfig.showBuyBtn', false);
  defineProperty(result, 'isShowBuyBtn', showBuyBtn ? 1 : 0);
  return result;
};

// 获取店铺担保配置
const getGuarantee = function (state) {
  return getInfoFromObj(state, 'guarantee', ['on', 'style']);
};

// 获取配送信息
const getDistribution = function (state) {
  return getInfoFromObj(state, 'distribution', [
    'postage', 'supportExpress', 'supportSelfFetch', 'supportLocalDelivery', 'expressFee', 'localDeliveryFee'
  ]);
};

const getSku = function (state) {
  const getKeys = (it) => Object.keys(it || {});
  const result = getInfoFromObj(state, 'sku', getKeys(state.sku));
  const listData = get(state, 'sku.list', []);
  const list = listData.map(it => {
    return getInfoFromObj(it, null, Object.keys(it || {}));
  });
  defineProperty(result, 'list', list);
  const messagesData = get(state, 'sku.messages', []);
  const messages = messagesData.map(it => {
    return getInfoFromObj(it, null, getKeys(it));
  });
  defineProperty(result, 'messages', messages);
  const treeData = get(state, 'sku.tree', []);
  const tree = treeData.map(it => {
    const vData = get(it, 'v', []);
    const v = vData.map(vit => {
      return getInfoFromObj(vit, null, getKeys(vit));
    });
    const treeItem = getInfoFromObj(it, null, getKeys(it));
    defineProperty(treeItem, 'v', v);
    return treeItem;
  });
  defineProperty(result, 'tree', tree);
  return result;
};

const injectPageData = function (bridge, vm) {
  bridge.setPageData(vm, {
    goodsItem: getGoodsItem,
    refund: getRefund,
    multistore: getMultistore,
    shop: getShop,
    shopConfig: getShopConfig,
    guarantee: getGuarantee,
    distribution: getDistribution,
    sku: getSku,
  });
};

export default injectPageData;
