import args from '@youzan/weapp-utils/lib/args';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import {
  autoEnterShop,
  checkShopStatausAfterES,
} from 'common-api/multi-shop/multi-shop-redirect';
import { checkReLaunchShopSelect } from '@/base-api/shop/chain-store';
import goodsPageMethods from './goods-page-methods';
import goodsPageStore from './goods-page-store';
import goodsPageHook from './goods-page-hook';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';

const app = getApp();

export default {
  ...goodsPageHook,
  ...goodsPageStore,
  ...goodsPageMethods,

  onLoad(options = {}) {
    // 零售店铺额外逻辑
    const camelCaseOptions = mapKeysCase.toCamelCase(options);
    const { umpType = '', umpAlias = '' } = camelCaseOptions;
    // 若传入了umpAlias(ump_alias) 和 umpType(ump_type) 则走
    if (umpAlias && umpType) {
      const redirectUrl = args.add('/' + this.route, options);
      autoEnterShop({ ...options, umpAlias, umpType, redirectUrl }).then(() => {
        checkShopStatausAfterES();
      });
    } else {
      checkReLaunchShopSelect();
    }

    // 初始化数据，包括商品、分销员等
    // store action
    this.initData(options);
  },

  onShow() {},

  // 触发分享按钮
  onShareAppMessage() {
    const { goods = {}, salesman = {}, shareParams } = this.data || {};
    const shareData = {
      title: goods.title,
      desc: goods.subTitle,
      path: goods.path,
    };
    if (salesman) {
      shareData.path = addSalesmanParams({
        url: shareData.path,
        sl: salesman.alias,
      });
    }

    shareData.path = args.add(shareData.path, shareParams);

    // 尝试取商品第一张图作为分享图
    const image = goods.picture;
    if (image) {
      shareData.imageUrl = image.url;
    }

    app.logger &&
      app.logger.log({
        et: 'click',
        ei: 'goods_share_result',
        en: '分享结果回调',
        si: '', // 店铺id，没有则置空
        params: {},
      });
    return shareData;
  },

  /**
   * 增加hooks，当fetchGoodsDetail成功以后，触发onGoodsInited
   */
  // onGoodsInited() {},
};
