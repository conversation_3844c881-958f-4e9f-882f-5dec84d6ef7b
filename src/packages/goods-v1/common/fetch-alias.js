const app = getApp();

function _getAliasPromise({ alias, sourceKdtId, targetKdtId }) {
  // 是连锁多网店
  // 并且当前店铺是连锁店
  // 则调用接口获取alias,即刷新页面
  return app
    .request({
      path: '/wscshop/api/showcase-retail/getRetailSubShopGoodsInfo.json',
      data: {
        source_alias: alias,
        source_kdt_id: sourceKdtId,
        target_kdt_id: targetKdtId
      }
    })
    .then(data => {
      return data.goodsAlias;
    })
    .catch(() => {
      return alias;
    });
}

// 考虑到零售逻辑
function fetchActualAlias(currentAlias) {
  return new Promise((resolve) => {
    app.getShopInfo().then((data) => {
      const { chainStoreInfo = {}, kdt_id: kdtId } = data;
      const { isRootShop = false, isMultiOnlineShop = false } = chainStoreInfo;

      const queryData = {
        alias: currentAlias,
        sourceKdtId: chainStoreInfo.rootKdtId,
        targetKdtId: app.getKdtId() || kdtId,
      };

      // 非多网店则直接调接口
      if (!isMultiOnlineShop) {
        resolve(currentAlias);
        // 如果是总店则监听进店事件
      } else if (isRootShop) {
        app.once('app:chainstore:kdtid:update', ({ kdtId }) => {
          _getAliasPromise(
            Object.assign({}, queryData, {
              targetKdtId: kdtId
            })
          ).then(alias => {
            resolve(alias);
          });
        });
        // 如果是分店则直接更新alias
      } else {
        _getAliasPromise(queryData).then(alias => {
          resolve(alias);
        });
      }
    });
  });
}

export { fetchActualAlias };
