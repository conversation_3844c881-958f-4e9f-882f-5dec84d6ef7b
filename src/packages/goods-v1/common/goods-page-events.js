import { mapMutations, mapActions } from '@youzan/vanx';

const app = getApp();

export default {
  ...mapActions(['setSalesmanData']),
  ...mapMutations(['showSharePop']),
  // 点击分享按钮
  handleShareClick() {
    this.showSharePop();
    app.logger.log({
      et: 'click',
      ei: 'share_goods',
      en: '分享商品',
      params: {
        alias: this.data.alias,
        share_method: 'wx_share',
      }
    });
  },

  // 分销员信息设置
  onSalesmanSetShare(e) {
    const { shareData = {} } = e.detail;
    this.setSalesmanData(shareData);
  },

  // 触发分销员的分享海报
  onSalesmanShareCard() {
    try {
      this.selectComponent('#share-goods').drawSalemanCard();

      app.logger.log({
        et: 'click',
        ei: 'share_goods',
        en: '分享商品',
        params: {
          share_method: 'card'
        }
      });
    } catch (e) {
      console.log(e.message);
    }
  },
};
