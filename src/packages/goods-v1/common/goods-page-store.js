import { mapState, mapGetters, mapMutations, mapActions } from '@youzan/vanx';
import args from '@youzan/weapp-utils/lib/args';
import get from '@youzan/weapp-utils/lib/get';

const app = getApp();

export default {
  // store 在主页面定义
  data: {
    // 这些数据会用于分享
    goods: {},
    showGoods: false,
    themeClass: app.themeClass,
    deviceType: app.deviceType || '',
    appType: 'wsc'
  },

  mapData: {
    ...mapState('/', {
      shop: (state) => {
        return {
          isMultiStore: state.shop.isMultiStore,
          offlineId: state.shop.offlineId
        };
      }
    }),
    ...mapState({
      salesman: (state) => state.salesman,
      shareParams: (state) => state.shareParams
    }),
    ...mapGetters(['skuData'])
  },

  computed: {
    ...mapState('/', {
      offlineId: (state) => state.shop.offlineId
    }),
    ...mapState(['goods', 'sku', 'picture', 'multistore']),
    ...mapGetters(['goodsPrice'])
  },

  ...mapMutations(['setInitData']),
  ...mapActions(['fetchGoods', 'setUmpLogger']),

  watch: {
    offlineId(newVal, oldVal) {
      if (!oldVal && !newVal) {
        return;
      }

      if (oldVal === newVal) {
        return;
      }

      // 秒杀情况下，需要额外传页面类型
      this.fetchGoodsDetail({
        pageType: this.currentPageType
      });
    }
  },

  _waitReadys: [],

  onPageReady(cb) {
    if (this._ready) cb.call(this);
    this._waitReadys.push(cb);
  },

  pageReady() {
    this._ready = true;
    this._waitReadys.forEach((cb) => cb.call(this));
    this._waitReadys = [];
  },

  initData(options) {
    const {
      alias = '',
      activityId = '',
      type = '',
      activityType = '' // ?? 兼容，有的地方是type，有的地方是activityType
      // 埋点相关参数
      // aid = '',
      // traceid = ''
    } = options;
    const { route = '' } = this;
    wx.showLoading({
      title: '加载中...'
    });

    this.setInitData({
      ...options,
      alias,
      activityId,
      activityType: type || activityType, // ?? 0元购已开始用得是activityType
      path: args.add(route, options)
    });

    let pageType = this.currentPageType;
    if (type === 'gift') {
      pageType = type;
    }

    const startKdtId = app.getKdtId();
    return this.fetchGoodsDetail({
      pageType
    })
      .then((res = {}) => {
        // 如果零售店铺需要Update KdtId
        // if (app.globalData.isRetailApp) {
        //   app.updateKdtId(res.kdt_id);
        // }

        // 进入页面埋点数据
        const goods = get(res, 'goods.goods', {});
        this.trigger('goodsDetail:loaded', goods.id, {
          id: goods.id,
          aid: '',
          traceid: '',
          title: goods.title
        });

        this.setYZData({
          showGoods: true,
          goods: {
            title: goods.title,
            subTitle: goods.subTitle,
            picture: get(goods, 'pictures[0]'),
            path: args.add(this.route, this.options),
            alias: goods.alias
          }
        });

        // 设置UMP数据埋点
        this.setUmpLogger();

        wx.hideLoading();
        if (this.onGoodsInited instanceof Function) {
          this.onGoodsInited();
        }
        this.pageReady();
      })
      .catch((err) => {
        if (err === 'chainstore need enter shop') {
          // 如果 kdtId 已经变得不一样，说明已经完成进店操作了
          if (+startKdtId !== +app.getKdtId()) {
            this.initData(options);
            return;
          }
          app.once('app:chainstore:kdtid:update', () => {
            this.initData(options);
          });
          return;
        }
        wx.hideLoading();
        console.warn('[goods] init data err:', err);
      });
  },

  fetchGoodsDetail(options = {}) {
    wx.showNavigationBarLoading();
    // vanx actions
    const { pageType, forceRefresh = false } = options;
    return this.fetchGoods({
      pageType,
      skipShopInfo: !forceRefresh,
      beforeSetGoodsCheck: (goodsData) => {
        if (pageType !== 'gift') {
          // 礼品不需要重定向
          if (this.checkGoodsRedirect(goodsData, this.currentPageType)) {
            return false;
          }
        }

        return true;
      }
    })
      .then((goodsData) => {
        wx.hideNavigationBarLoading();
        return Promise.resolve(goodsData);
      })
      .catch(({ code } = {}) => {
        if (code === 'chainstore need enter shop') {
          return Promise.reject('chainstore need enter shop');
        }
        wx.hideNavigationBarLoading();
        return Promise.reject('need redirect ...');
      });
  },

  getContactInfo() {
    return new Promise((resolve) => {
      this.onPageReady(() => {
        const { sku = {}, goods = {}, picture = {}, multistore = {}, goodsPrice = {} } = this;
        const sourceParam = {
          kdt_id: app.getKdtId(),
          source: 'goods', // 写死'goods'
          endpoint: 'wx', // 渠道，wap端写死'h5'
          detail: JSON.stringify({
            alias: goods.alias,
            name: goods.title, // 商品name
            price: sku.price, // 商品价格，已原价格为主
            imgs: (picture.pictures || []).map((item) => item.url).slice(0, 2)
          })
        };

        if (multistore.id) {
          sourceParam.site_id = multistore.id; // 网点id
        }

        const imPath = args.add(goods.path || this.route, {
          imFrom: 'GOODS',
          alias: goods.alias,
          price: goodsPrice.currentPrice || sku.price || '',
          originPrice: goodsPrice.originPrice || ''
        });

        resolve({
          sourceParam: JSON.stringify(sourceParam),
          messageTitle: goods.title,
          messagePath: `/${imPath}`,
          messageImg: goods.picture,
          messageCard: true
        });
      });
    });
  }
};
