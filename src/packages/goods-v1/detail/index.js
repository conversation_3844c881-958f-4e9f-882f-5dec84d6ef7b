import { VanxPage } from 'pages/common/wsc-page/index';
import { store } from 'shared/packages/goods/store/index';
import goodsPageInits from '../common/goods-page';
import goodsPageSaas from '../common/goods-page-saas';
import { initSalesman } from 'shared/utils/salesman-share';

const extendPageConfig = ECLOUD_MODE ? goodsPageSaas : {};

VanxPage(goodsPageInits, extendPageConfig, {
  currentPageType: 'normal',

  store(query) {
    const pageTime = (new Date()).getTime();
    store.name = `goods-${query.alias}-${pageTime}`;
    return store;
  },

  onGoodsInited() {
    console.log('oninited', this.$store.state);
  },
  onLoad() {
    initSalesman.call(this, {
      scene: 'item_goods',
      sst: 2,
      setSalesmanData: this.$store.actions.setSalesmanData,
    });
  },
});
