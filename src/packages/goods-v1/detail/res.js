let res = {
  nearlyFourMonthsPriceRange: '30.00-50.00',
  cardPriceCalendarModelList: [],
  nearlyFourMonthMinPriceMap: {
    '2018-12': 3000,
    '2019-01': 1000,
    '2019-02': 2000,
    '2019-03': 4000
  },
  nearlyFourDayMarketableMap: {
    '2018-12-11': 8000,
    '2019-01-11': 8000,
    '2019-02-11': 8000,
    '2019-03-11': 8000
  }
};

const dec = {
  originPrice: 3000,
  isEnable: 0,
  dateSkuId: 11665693,
  stockNum: 12,
  itemId: 79988747,
  stockDate: '2018-12-01',
  itemSkuId: 91880780,
  activityPrice: 1000
};

const jun = {
  originPrice: 6000,
  isEnable: 1,
  dateSkuId: 11665693,
  stockNum: 13,
  itemId: 79988747,
  itemSkuId: 91880780,
  activityPrice: 1000,
  stockDate: '2018-01-01'
};

const feb = {
  originPrice: 5000,
  isEnable: 1,
  dateSkuId: 11665693,
  stockNum: 11,
  itemId: 79988747,
  itemSkuId: 91880780,
  activityPrice: 1000,
  stockDate: '2018-02-01'
};

const mar = {
  originPrice: 1000,
  isEnable: 1,
  dateSkuId: 11665693,
  stockNum: 111,
  itemId: 79988747,
  itemSkuId: 91880780,
  activityPrice: 1000,
  stockDate: '2018-03-01'
};
for (let index = 0; index < 31; index++) {
  res.cardPriceCalendarModelList.push(dec);
}
for (let index = 0; index < 31; index++) {
  res.cardPriceCalendarModelList.push(jun);
}
for (let index = 0; index < 28; index++) {
  res.cardPriceCalendarModelList.push(feb);
}
for (let index = 0; index < 31; index++) {
  res.cardPriceCalendarModelList.push(mar);
}

exports.calendarInfoData = res;
