<view class="group-on__container">
  <van-cell
    wx:if="{{  groupOn.ongoingGroup.length }}"
    is-link
    title-class="group-on__desc"
    title="以下小伙伴正在发起拼团、你可以直接参与"
    url="/packages/collage/groupon/join-groups/index?activityId={{ groupOn.activityId }}"
  />
  <van-cell
    center
    value-class="cell-value__class"
    border="{{ index + 1 !== groupOn.ongoingGroup.length }}"
    wx:key="{{ index }}" wx:for="{{ groupOn.ongoingGroup }}">
    <image slot="icon" src="{{ item.fansPicture }}" class="group-on-member__head"></image>
    <view class="group-on-item__view">
      <view style="color: #111;margin-bottom: 4px;font-size: 14px;">{{ item.fansNickname }}</view>
      <view class="van-c-gray-darker group-on-item__desc">
        <text>还差</text>
        <theme-view color="general" class="font-bold" custom-class="theme-inline"> {{ item.remainJoinNum }} </theme-view>
        <text>人成团，剩余{{ remainTimeStrs[index] }}</text>
      </view>
    </view>
    <navigator
      slot="extra"
      hover-class="none"
      url="/packages/collage/groupon/detail/index?groupAlias={{ item.groupAlias }}&type={{ activityType }}"
      class="theme-border-color van-hairline--surround theme-color van-font-12 van-center group-on-action__button">
      <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" is-once >
        去凑团
      </user-authorize>
    </navigator>
  </van-cell>
</view>
