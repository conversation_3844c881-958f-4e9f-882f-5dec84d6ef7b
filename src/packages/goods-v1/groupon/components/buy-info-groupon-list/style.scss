
.group-on {
  &__container {
    margin-top: 10px;
    box-sizing: border-box;
    background-color: #fff;
    color: #333;
  }

  &__desc {
    font-size: 28rpx;
  }

  &__tip {
    font-size: 12px;
    color: #333;
  }

  &-member__head {
    width: 45px;
    height: 45px;
    border-radius: 45px;
  }

  &-item__view {
    height: 48px;
    padding: 0 10px;
    font-size: 12px;
    line-height: 14px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &-action__button {
    width: 108rpx;
    height: 58rpx;
    line-height: 58rpx;
    font-size: 12px;
    &::after {
      border-radius: 58rpx;
    }
  }
}

.font-bold {
  font-weight: bold;
}

.theme-inline {
  display: inline;
}
