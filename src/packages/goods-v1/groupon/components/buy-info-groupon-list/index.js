/**
 * 商品拼团凑团区域
 */

import { countDownRemainParse } from '@youzan/weapp-utils/lib/time';
import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  created() {
    this.clear();
  },
  ready() {
    // ?? 测一下异常条件?
    this.on('goods:detail:show', () => this.updateGrouponRemainTime());
    this.on('goods:detail:hide', () => this.clear());
    this.updateGrouponRemainTime();
  },
  detached() {
    this.clear();
  },
  data: {
    remainTimeStrs: [],
  },
  mapData: {
    ...mapState({
      groupOn: (state) => state.marketActivity.groupOn,
      activityType: (state) => state.activityInfo.activityType
    }),
  },
  methods: {
    updateGrouponRemainTime() {
      const { groupOn = {} } = this.data;
      const { ongoingGroup } = groupOn;
      if (!groupOn || !ongoingGroup) {
        this.clear();
        this.timer = setTimeout(() => this.updateGrouponRemainTime(), 1000);
        return;
      }
      const remainTimes = ongoingGroup.map(item => item.remainTime + new Date().getTime());
      // ?? 有问题，一起调试
      // if (remainTimes.length === 0) {
      //   remainTimes.push(groupOn.endRemainTime + new Date());
      // }
      this.runCountDown(remainTimes);
    },

    clear() {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = null;
    },

    runCountDown(remainTimes = []) {
      this.clear();

      const remainTimeStrs = remainTimes.map((remainTime) => {
        return countDownRemainParse(remainTime - new Date()).toString();
      });
      this.setYZData({ remainTimeStrs });
      this.timer = setTimeout(() => this.runCountDown(remainTimes), 1000);
    }
  }
});
