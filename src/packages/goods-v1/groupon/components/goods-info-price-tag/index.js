import theme from 'shared/common/components/theme-view/theme';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapGetters } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapGetters([
      'activityPriceTag',
    ]),
  },

  data: {
    themeGeneral: ''
  },

  ready() {
    theme.getThemeColor('general')
      .then(color => {
        this.setData({
          themeGeneral: color
        });
      });
  },
});
