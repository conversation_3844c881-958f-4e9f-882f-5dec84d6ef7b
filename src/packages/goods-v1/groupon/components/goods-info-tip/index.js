import get from '@youzan/weapp-utils/lib/get';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapGetters, mapState } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapGetters([
      'activityJoinNum',
      'grouponActivityIntro',
      'groupOnActivityTitle',
      'grouponIntroUrl'
    ]),
    ...mapState({
      groupType: (state) => get(state, 'marketActivity.groupOn.groupType', 1),
    }),
  },
});
