import get from '@youzan/weapp-utils/lib/get';
import args from '@youzan/weapp-utils/lib/args';
import money from '@youzan/weapp-utils/lib/money';
import { splitPrice } from 'shared/packages/goods/store/utils/helpers';
import { SKU_SCENE, ACCOUNT_UNION_SCENE } from 'shared/packages/goods/common/constant';
import { numberToPrice } from 'shared/packages/goods/common';
import {
  getBottomViewConfig,
} from 'shared/packages/goods/containers/goods-bottom/store/common';

export default {
  activityTitle() {
    return '拼团';
  },

  activityPriceTag(state, getters) {
    const activity = get(state, 'marketActivity.groupOn', {});

    if (getters.isLadderGroupOn) {
      return '人多更优惠';
    }

    if (activity.joinNum) {
      return `${activity.joinNum}人拼团价`;
    }
    return '';
  },

  groupOnActivityTitle(state, getters) {
    const activity = get(state, 'marketActivity.groupOn', {});
    if (getters.isLadderGroupOn) {
      return '拼团玩法-阶梯拼团';
    }

    if (activity.groupType === 1) {
      return '拼团玩法-老带新拼团';
    }

    return '拼团玩法';
  },

  grouponActivityIntro(state, getters) {
    const activity = get(state, 'marketActivity.groupOn', {});
    let activityIntro = `支付开团邀请${activity.joinNum - 1}人参团，人数不足自动退款。`;
    if (activity.groupType === 1) {
      // 老带新拼团
      activityIntro = `支付开团邀请${activity.joinNum - 1}名新用户参团，人数不足自动退款。`;
    }

    if (getters.isLadderGroupOn && activity.ladderList) {
      const ladder = activity.ladderList[0];
      const ladderIntro = ladder.reduce((intro, item) => {
        intro.push(`${item.scale}人团￥${money(item.skuPrice).toYuan()}起`);
        return intro;
      }, []);

      ladderIntro.push(
        '先选择参团人数，支付后开团邀请好友参团，人数不足自动退款。'
      );
      activityIntro = ladderIntro.join('，');
    }
    return activityIntro;
  },

  grouponIntroUrl(state, getters) {
    const activity = get(state, 'marketActivity.groupOn', {});

    if (!activity) return;

    const url = args.add(
      '/wscump/groupon/guide',
      {
        kdt_id: getters.kdtId,
        groupType: activity.groupType,
        activityType: state.activityInfo.activityType || '',
      }
    );
    return args.add(
      '/pages/common/webview-page/index',
      {
        src: encodeURIComponent(url)
      }
    );
  },

  activityLabel(state, getters) {
    if (getters.isLadderGroupOn) {
      return '拼团价';
    }

    return getters.activityPriceTag;
  },

  // 参团人数
  activityJoinNum(state) {
    const activity = get(state, 'marketActivity.groupOn', {});
    return activity.joinNum || activity.participantNum || 0;
  },

  /* ============ activity-banner ========== */
  showActivityBanner() {
    return true;
  },

  countdown(state) {
    const activity = get(state, 'marketActivity.groupOn', {});
    const { startRemainTime, endRemainTime } = activity;
    const now = +new Date();
    let desc;
    let end;
    if (startRemainTime > 0) {
      desc = '距开始';
      end = now + startRemainTime;
    } else if (endRemainTime > 0) {
      desc = '距结束仅剩';
      end = now + endRemainTime;
    } else {
      desc = '活动已结束';
      end = now;
    }

    return {
      desc,
      end,
      start: now
    };
  },

  // TODO 处理 mook 数据
  isLadderGroupOn(state) {
    return state.activityInfo.activityType === 26;
  },

  ladderGrouponSkuExtraList(state, getters) {
    const skuId = getters.skuId || 0;
    const activity = get(state, 'marketActivity.groupOn', {});

    return activity.ladderList[skuId].map(item => {
      return {
        ...item,
        price: money(item.skuPrice).toYuan() + (!skuId ? '起' : ''),
      };
    });
  },

  /* ============ goods-info ============== */
  goodsPrice(state, getters) {
    const activity = get(state, 'marketActivity.groupOn', {});
    const { priceDateRange, goods } = state;
    const {
      price: currentPrice,
      minPrice,
      maxPrice,
    } = activity.sku || {};
    const {
      price: originPrice,
    } = state.sku || {};
    const {
      isVirtualTicket,
    } = goods;
    const isRange = minPrice !== maxPrice;

    // 电子卡券加上价格日历特殊逻辑
    if (isVirtualTicket && priceDateRange) {
      return {
        currentPrice: priceDateRange
      };
    }

    if (getters.isLadderGroupOn) {
      return {
        isRange: true,
        currentPrice,
        minPriceData: splitPrice(activity.ladderMinPrice),
        maxPriceData: splitPrice(activity.ladderMaxPrice),
        originPrice
      };
    }
    return {
      isRange,
      currentPrice,
      minPriceData: splitPrice(minPrice),
      maxPriceData: splitPrice(maxPrice),
      originPrice,
    };
  },

  /* ============ goods-info-tip =========== */

  // 代金券名称
  couponName(state) {
    const activity = get(state, 'marketActivity.groupOn', {});
    return activity.couponName || '';
  },

  /* ============ goods-bottom ============= */
  // 零元抽奖拼团主按钮展示
  goodsBottomView(state, getters) {
    const btnInfo = getBottomViewConfig(state, getters);
    if (btnInfo) {
      return btnInfo;
    }
    const activity = get(state, 'marketActivity.groupOn', {});
    if (activity.startRemainTime > 0) {
      return getters.goodsCommonBottomView;
    }
    // 原来的售价
    let originPrice = get(state, 'sku.price');
    const originMinPrice = get(state, 'sku.minPrice');
    if (originMinPrice) {
      originPrice = numberToPrice(originMinPrice);
    }
    // 团购价
    let price = get(activity, 'sku.price');
    const minPrice = get(activity, 'sku.minPrice');
    if (minPrice) {
      price = numberToPrice(minPrice);
    }
    // 预售不支持加入购物车
    const presale = state.goodsActivity.presale;

    // 拼团商品支持零售价购买
    const btnVice = {
      buy: true,
      show: true,
      text: '单独购买',
      sub: `${originPrice}`,
      skuScene: presale ? SKU_SCENE.NORMAL_BUY : SKU_SCENE.SEL_SKU,
      accountUnionScene: ACCOUNT_UNION_SCENE.ADD_CART,
    };

    const btnMain = {
      show: true,
      loading: false,
      skuScene: SKU_SCENE.ACT_BUY,
      accountUnionScene: ACCOUNT_UNION_SCENE.GROUP_BUY,
    };

    // ?? 这一段逻辑是这样的么
    if (activity.groupAlias) {
      btnMain.text = '查看我的团';
      btnMain.path = args.add('/packages/collage/groupon/detail/index', {
        groupAlias: activity.groupAlias,
        type: state.activityInfo.activityType || ''
      });
      btnMain.navigate = true;
    } else {
      if (getters.isLadderGroupOn) {
        const { ladderMinPrice = 0, ladderMaxPrice = 0 } = activity || {};
        price = `${numberToPrice(ladderMinPrice)}${ladderMinPrice !== ladderMaxPrice ? '起' : ''}`
      }
      btnMain.subscribeScene = 'groupon';
      btnMain.text = '我要开团';
      btnMain.sub = `${price}`;
      btnMain.buy = true;
    }

    return {
      main: btnMain,
      vice: btnVice,
    };
  },

  goodsBottomTip(state) {
    const activity = get(state, 'marketActivity.groupOn', {});
    if (activity.startRemainTime > 0) {
      return '活动即将开始';
    }
    return '';
  },

  isGroupOnBuy(state) {
    return state.skuConfig.skuScene === SKU_SCENE.ACT_BUY;
  },

  /* ============ goods-sku ============= */
  // 购买按钮文案
  skuBuyBtnText(state, getters) {
    return getters.isGroupOnBuy ? '确定' : getters.skuBuyBtnCommonText;
  },

  // 用于重写的sku数据
  skuData(state, getters) {
    const activity = get(state, 'marketActivity.groupOn', {});
    return getters.isGroupOnBuy ? activity.sku : state.sku;
  },

  activitySkuData(state) {
    return get(state, 'marketActivity.groupOn.sku', {});
  },

  /* ============ 限购 =============== */
  commonQuota(state) {
    const { limit = {} } = get(state, 'marketActivity.groupOn', {});

    const quota = limit.singleQuota && limit.num
      ? Math.min(limit.singleQuota, limit.num)
      : (limit.singleQuota || limit.num);

    return state.skuConfig.skuScene === SKU_SCENE.ACT_BUY ? quota : state.limit.num;
  },

  quotaUsed(state, getters) {
    const activity = get(state, 'marketActivity.groupOn', {});
    return getters.isGroupOnBuy ? activity.limit.used : state.limit.used;
  },

  quotaText(state, getters) {
    const { commonQuotaText } = getters;
    const { limit } = get(state, 'marketActivity.groupOn', {});

    const texts = []

    if (limit.num) {
      texts.push(`每人限购${limit.num}件`)
    }

    if (limit.singleQuota) {
      texts.push(`每次限购${limit.singleQuota}件`)
    }

    const quotaText = texts.length ? `拼团${texts.join('，')}` : '';
    return state.skuConfig.skuScene === SKU_SCENE.ACT_BUY ? quotaText : commonQuotaText;
  },

  quotaCycle(state, getters) {
    return getters.isGroupOnBuy ? 0 : getters.commonQuotaPeriod;
  },
};
