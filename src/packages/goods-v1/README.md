# 商品详情

需要补充：
1. 初始化流程
2. 新增生命周期钩子 - onGoodsInited
3. store层级结构 - base(goods) -> container -> activity
4. 模板继承使用 slot选择
5. 数据结构

## 初始化流程/生命周期
1. 分包加载
2. 初始化kdtId等小程序基础信息
3. HTTP fetch goodsDetail => store信息初始化
4. 触发生命周期钩子：onGoodsInited
5. 判断页面类型，普通则继续下一步流程，特殊活动则跳转相应页面
  * 因为request做了cache处理，过期时间5-10秒，跳转页面后不需要HTTP fetch
6. 加载页面组件信息

## 页面组件层级关系
* 入口页面： src/goods/
  * detail 普通商品
  * lucky-draw-group 0元抽奖团
  * help-cut 砍价
  * group-on 拼团
  * sec-kill 秒杀
  * points 积分商品
  * gift 送礼
  * present 赠品
  * tuan 团购
  * **特殊商品页有独立store和slot组件，用于重写特殊数据**

* 入口页面继承于基础商品详情页: src/goods/components
  * detail-base 商品详情页核心模板
  * 核心模板可通过slot插槽在特殊商品页重写
  * 商品详情页已开启自定义导航，新加的页面json需要添加新配置 navigationStyle: custom

* 基础商品详情页模块划分：shared/packages/goods/container
  * goods-image 主图
  * activity-banner 主图上活动banner，如倒计时
  * goods-info 商品标题，价格
    * 商品价格
    * 商品标题
    * 分享按钮
    * 价格标签
    * 运费等
    * goods-info-tip **插槽**
      * 各种商品活动说明，如拼团
    * 安全认证
  * recommend-store [多网点]推荐网点
  * order-preference 优惠信息
  * 商品订单级别活动特殊显示
    * discount-packages 优惠套餐推荐区域
    * goods-bundle 打包一口价
    * plus-buy 加价购
  * activity 其他营销活动 **插槽**
  * buy-info 商品购买信息区域
  * buy-info-extra 商品购买区域补充信息 **插槽**
  * shop-info 店铺信息
  * goods-review 商品评论
  * buyer-show 买家秀
  * goods-detail 商品详情
  * goods-bottom 底部按钮 **插槽**
  * share-goods 分享按钮
  * salesman-icon 分销员按钮
  * floating-nav 悬浮窗
  * goods-sku 商品规格 **插槽**

* 复用商品组件 shared/packages/goods/components
  * goods-popup 底部弹窗

## Store层级关系
* base-store shared/packages/goods/store
  * 主要包括goods内部状态变更，详见相应state
  * 仅仅保持后端基础数据及各模块复用信息
* container-store share/packages/goods/containers/**/store
  * 依据模块逻辑，模块自身store
* activity/page-store src/packages/goods/**/store
  * 依据营销活动重写模块逻辑

## 数据结构
``` JSON
  {
    "skuShowPrice":"34.00",
    "url":{

    },
    "env":{

    },
    "pageParams":{
        "alias":"2ok5dbg0riez3",
        "path":"packages/goods/lucky-draw-group/index?alias=2ok5dbg0riez3&type=luckyDrawGroup&activityId=8591363608",
        "activityType":"luckyDrawGroup",
        "activityId":"8591363608",
        "sls":""
    },
    "picture":{
        "pictures":[
            {
                "height":280,
                "id":4007044,
                "url":"https://img.yzcdn.cn/upload_files/2019/01/29/FmIOFjfOsWETn0HKEh3FTxblzbk9.png",
                "width":280
            }
        ],
        "height":280
    },
    "video":{
        "width":null,
        "height":null,
        "coverUrl":"",
        "videoUrl":"",
        "countPlayedUrl":""
    },
    "goods":{
        "id":364568264,
        "type":"normal",
        "alias":"2ok5dbg0riez3",
        "title":"SW抽奖拼团111",
        "path":"packages/goods/lucky-draw-group/index?alias=2ok5dbg0riez3&type=luckyDrawGroup&activityId=8591363608",
        "picture":"https://img.yzcdn.cn/upload_files/2019/01/29/FmIOFjfOsWETn0HKEh3FTxblzbk9.png",
        "sellPoint":"",
        "origin":"",
        "isVirtual":false,
        "isVirtualTicket":false,
        "isDisplay":1,
        "limitBuy":false,
        "waitToSoldTime":0,
        "buyWay":1,
        "buyUrl":"",
        "forbidBuyReason":"活动未开始",
        "isSupportFCode":false
    },
    "supplier":{
        "kdtId":""
    },
    "salesman":{
        "name":"分销员",
        "icon":"https://img.yzcdn.cn/public_files/2017/7/18/2bcf631430ab9320109c2a85fdffd679.png",
        "qrCode":"",
        "goodsCanShare":false,
        "cpsRate":0,
        "commission":"",
        "shareLink":"packages/goods/lucky-draw-group/index?alias=2ok5dbg0riez3&type=luckyDrawGroup&activityId=8591363608&sls=undefined"
    },
    "refund":{
        "isSupport":1
    },
    "limit":{
        "num":0,
        "used":0
    },
    "shop":{
        "kdtId":491391,
        "logo":"https://img.yzcdn.cn/upload_files/2018/12/27/FmurQcHpSj3NylXCRZTX8622UcZJ.png",
        "name":"心怡画坊qa",
        "phone":"029-82401111",
        "shopCertType":2,
        "shopType":0
    },
    "cartInfo":{
        "count":4
    },
    "multistore":{
        "address":"",
        "salableStores":[

        ],
        "openMultiStoreSwitch":""
    },
    "shopConfig":{
        "isShowBuyBtn":false,
        "isSecuredTransactions":1,
        "showRecommendGoods":1,
        "showBuyRecord":0,
        "showCustomerReviews":1,
        "supportFreightInsurance":0,
        "hideShoppingCart":0,
        "hasPhysicalStore":1
    },
    "guarantee":{
        "on":1,
        "style":0
    },
    "retail":{
        "show":false,
        "address":""
    },
    "distribution":{
        "postage":"¥ 0.00~5.00",
        "supportExpress":true,
        "supportSelfFetch":true,
        "supportLocalDelivery":true,
        "expressFee":"免运费",
        "localDeliveryFee":"最低0.00元起送，运费 ¥3.00 ~ 5.00"
    },
    "goodsDetail":{
        "defaultType":"goods",
        "tplStyle":0,
        "richText":"",
        "showcaseComponents":[
            {
                "type":"rich_text",
                "content":"",
                "key":"0.6480-rich_text"
            },
            {
                "action_text":"公共广告",
                "type":"store",
                "key":"0.1782-store"
            },
            {
                "color":"#f9f9f9",
                "fullscreen":0,
                "type":"rich_text",
                "content":"<p>公共广告组件</p><p><img></img></p><p><b>这个是一个标签</b></p><p><a class="js-open-follow" href="###">关注</a><br/></p><p><img data-origin-width="1185" data-origin-height="689" src="https://img.yzcdn.cn/upload_files/2018/03/20/FvP4xe9uxlbB9EceusSOHLDgA6CS.jpg!730x0.jpg"/></p><table><tbody><tr class="firstRow"><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td></tr><tr><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td></tr><tr><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td></tr><tr><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td><td width="32" valign="top"><br/></td></tr></tbody></table><p>af ga gaeg的 v 时光安抚啊<span style="color: rgb(255, 0, 0);"></span><br/></p><p><span style="color: rgb(255, 0, 0);">大法官啊<strong>噶热 </strong></span></p>",
                "key":"0.5158-rich_text"
            },
            {
                "goods_from":"0",
                "display_scale":"1",
                "image_fill_style":"2",
                "goods":[
                    {
                        "alias":"360bdez31up5r",
                        "biz_mark_code":"000000000000",
                        "buy_url":"",
                        "buy_way":1,
                        "goods_type":0,
                        "height":"357",
                        "id":365626719,
                        "image_url":"https://img.yzcdn.cn/upload_files/2019/03/27/3c44144064de44385fab9598b8b47781.png",
                        "is_virtual":0,
                        "origin":"",
                        "picture":[
                            {
                                "height":357,
                                "id":4717918,
                                "url":"https://img.yzcdn.cn/upload_files/2019/03/27/3c44144064de44385fab9598b8b47781.png",
                                "width":722
                            }
                        ],
                        "postage":0,
                        "pre_sale":false,
                        "price":"100.00",
                        "sold_status":1,
                        "start_sold_time":0,
                        "sub_title":"",
                        "title":"木易测试留言2",
                        "total_sold_num":0,
                        "width":"722",
                        "url":"https://shop683559.youzan.com/v2/goods/360bdez31up5r"
                    }
                ],
                "title":"1",
                "corner_mark_type":"0",
                "type":"goods",
                "text_align_type":"left",
                "show_corner_mark":"0",
                "default_image_url":"",
                "goods_list":[

                ],
                "text_style_type":"1",
                "price":"1",
                "buy_btn_type":"1",
                "page_margin":15,
                "size_type":"0",
                "border_radius_type":"1",
                "goods_margin":10,
                "buy_btn_express":"0",
                "goods_number_v2":"6",
                "size":"0",
                "show_sub_title":"1",
                "corner_mark_image":"",
                "button_text":"",
                "buy_btn":"1",
                "key":"0.7989-goods"
            },
            {
                "page_margin":15,
                "size_type":"8",
                "image_fill_style":"2",
                "type":"tag_list_left",
                "title":"1",
                "goods_margin":20,
                "border_radius_type":"1",
                "default_image_url":"",
                "buy_btn_express":"0",
                "size":"3",
                "text_style_type":"2",
                "price":"1",
                "buy_btn_type":"1",
                "sub_entry":[
                    {
                        "is_show_all":false,
                        "goods_number":6,
                        "tag_name":"殷凤-商品分组-C端装修核心内容埋点",
                        "alias":"uc1v17w4",
                        "id":"106097332",
                        "title":"殷凤-商品分组-C端装修核心内容埋点",
                        "type":"tag",
                        "url":"https://shop683559.youzan.com/wscshop/tag/uc1v17w4",
                        "timestamp":1553780795024,
                        "goods_num_display":1
                    },
                    {
                        "is_show_all":false,
                        "goods_number":6,
                        "tag_name":"零一多场景商品",
                        "alias":"dbo1qq0h",
                        "id":"106042820",
                        "title":"零一多场景商品",
                        "type":"tag",
                        "url":"https://shop683559.youzan.com/wscshop/tag/dbo1qq0h",
                        "timestamp":1553780808045,
                        "goods_num_display":6
                    },
                    {
                        "is_show_all":false,
                        "goods_number":6,
                        "tag_name":"sw",
                        "alias":"qy61lkeg1",
                        "id":"105899487",
                        "title":"sw",
                        "type":"tag",
                        "url":"https://shop683559.youzan.com/wscshop/tag/qy61lkeg1",
                        "timestamp":1553780814568,
                        "goods_num_display":6
                    }
                ],
                "show_sub_title":"1",
                "buy_btn":"1",
                "timestamp":1553780799252,
                "key":"0.5217-tag_list_left"
            },
            {
                "indicator":"1",
                "border_width":0,
                "show_method":"7",
                "page_margin":0,
                "corner_type":"2",
                "image_fill_style":"2",
                "count":"6",
                "sub_entry":[
                    {
                        "link_type":"educourse",
                        "image_height":60,
                        "image_url":"https://img.yzcdn.cn/upload_files/2019/02/19/FlGiHC_g6IOlm0AQupa5l_WUD3th.png",
                        "link_url":"https://j.youzan.com/Nq32jh",
                        "alias":"35z3wgxxltnzj",
                        "link_title":"免费商品",
                        "image_width":60,
                        "type":"image_ad_selection",
                        "title":"图片广告组件 - 全部课程",
                        "image_id":"4325217",
                        "image_thumb_url":"upload_files/2019/02/19/FlGiHC_g6IOlm0AQupa5l_WUD3th.png!100x100.jpg",
                        "link_id":361890100
                    },
                    {
                        "link_type":"paidlive",
                        "image_height":426,
                        "image_url":"https://img.yzcdn.cn/upload_files/2017/12/28/f085d898ac0de51d92745ba8323f490f.jpg",
                        "link_url":"https://h5.youzan.com/v2/ump/paidcontent?kdt_id=491391&sg=live&page=livedetail&alias=2onw29hs0a9tp",
                        "alias":"2onw29hs0a9tp",
                        "link_title":"直播测试-ciel-060101",
                        "image_width":604,
                        "type":"image_ad_selection",
                        "title":"图片广告组件 - 全部直播",
                        "image_id":"31377",
                        "image_thumb_url":"upload_files/2017/12/28/f085d898ac0de51d92745ba8323f490f.jpg!100x100.jpg",
                        "link_id":254
                    }
                ],
                "image_style":"2",
                "type":"image_ad",
                "key":"0.3371-image_ad"
            },
            {
                "border_width":0,
                "layout_width":2,
                "layout_height":1,
                "show_method":0,
                "width":2,
                "sub_entry":[
                    {
                        "image_url":"https://img.yzcdn.cn/upload_files/2019/02/14/FnXUG8yjq_IOFGMBCtgxg9VRt0yl.jpeg",
                        "link_title":"2342342",
                        "image_width":1067,
                        "type":"cube_selection",
                        "title":"",
                        "image_thumb_url":"upload_files/2019/02/14/FnXUG8yjq_IOFGMBCtgxg9VRt0yl.jpeg!100x100.jpg",
                        "link_id":977,
                        "link_type":"paidcontent",
                        "image_height":1052,
                        "x":0,
                        "width":1,
                        "y":0,
                        "link_url":"https://h5.youzan.com/v2/ump/paidcontent/index?kdt_id=491391&page=contentshow&alias=273xt2l91hqu7&qr=paidcontent_273xt2l91hqu7",
                        "alias":"273xt2l91hqu7",
                        "image_id":"4228838",
                        "height":1
                    },
                    {
                        "image_url":"https://img.yzcdn.cn/upload_files/2018/05/08/FjIiAP0Ls_SaQzGgJrJIM1F9RVZI.png",
                        "link_title":"米奇-知识-1",
                        "image_width":64,
                        "type":"cube_selection",
                        "title":"",
                        "image_thumb_url":"upload_files/2018/05/08/FjIiAP0Ls_SaQzGgJrJIM1F9RVZI.png!100x100.jpg",
                        "link_id":363590440,
                        "link_type":"paidcolumn",
                        "image_height":64,
                        "x":1,
                        "width":1,
                        "y":0,
                        "link_url":"https://h5.youzan.com/v2/ump/paidcontent/index?kdt_id=491391&page=columnshow&alias=3nqf97v7vlbbj&qr=paidcolumn_3nqf97v7vlbbj",
                        "alias":"3nqf97v7vlbbj",
                        "image_id":"538201",
                        "height":1
                    }
                ],
                "type":"cube_v3",
                "height":1,
                "key":"0.4793-cube_v3"
            },
            {
                "border_width":0,
                "show_method":"9",
                "background_color":"#fff",
                "color":"#000",
                "count":4,
                "image_fill_style":"2",
                "sub_entry":[
                    {
                        "link_type":"pointsstore",
                        "image_height":357,
                        "image_url":"https://img.yzcdn.cn/upload_files/2019/03/27/3c44144064de44385fab9598b8b47781.png",
                        "link_url":"https://h5.youzan.com/v2/ump/pointsstore?kdt_id=491391",
                        "alias":"",
                        "link_title":"积分商城",
                        "image_width":722,
                        "type":"image_ad_selection",
                        "title":"导航一",
                        "image_id":"4717918",
                        "image_thumb_url":"upload_files/2019/03/27/3c44144064de44385fab9598b8b47781.png!100x100.jpg",
                        "link_id":""
                    },
                    {
                        "link_type":"cart",
                        "image_height":331,
                        "image_url":"https://img.yzcdn.cn/upload_files/2019/03/27/090eede0ec9ce88cee8b2f4f8282026c.png",
                        "link_url":"https://h5.youzan.com/wsctrade/cart?kdt_id=491391",
                        "alias":"",
                        "link_title":"购物车",
                        "image_width":331,
                        "type":"image_ad_selection",
                        "title":"导航二",
                        "image_id":"4717916",
                        "image_thumb_url":"upload_files/2019/03/27/090eede0ec9ce88cee8b2f4f8282026c.png!100x100.jpg",
                        "link_id":""
                    },
                    {
                        "link_type":"allgoods",
                        "image_height":980,
                        "image_url":"https://img.yzcdn.cn/upload_files/2019/03/20/FntXETbvH-L7DReQgZjleGSE8RoU.jpg",
                        "link_url":"https://h5.youzan.com/wscshop/feature/goods/all?kdt_id=491391",
                        "alias":"",
                        "link_title":"全部商品",
                        "image_width":980,
                        "type":"image_ad_selection",
                        "title":"导航三",
                        "image_id":"4679976",
                        "image_thumb_url":"upload_files/2019/03/20/FntXETbvH-L7DReQgZjleGSE8RoU.jpg!100x100.jpg",
                        "link_id":""
                    },
                    {
                        "link_type":"seckill",
                        "image_height":1015,
                        "image_url":"https://img.yzcdn.cn/upload_files/2019/03/18/FvI2IsVOqYQUDXxEHC7cydSB83Bs.jpg",
                        "link_url":"https://h5.youzan.com/v2/seckill/8iyjhd4r",
                        "alias":"8iyjhd4r",
                        "link_title":"第三次秒杀测试",
                        "image_width":650,
                        "type":"image_ad_selection",
                        "title":"导航四",
                        "image_id":"4675756",
                        "image_thumb_url":"upload_files/2019/03/18/FvI2IsVOqYQUDXxEHC7cydSB83Bs.jpg!100x100.jpg",
                        "link_id":167553
                    }
                ],
                "slide_setting":"0",
                "type":"image_text_nav",
                "key":"0.7636-image_text_nav"
            },
            {
                "link_type":"history",
                "bg_color":"#ffffff",
                "show_method":"0",
                "color":"#000000",
                "font_size":14,
                "link_url":"https://www.youzan.com/v1/s/history",
                "alias":"",
                "link_title":"历史信息",
                "text":"文本组件",
                "type":"text",
                "show_split_line":"0",
                "link_id":"",
                "key":"0.6906-text"
            },
            {
                "show_method":"0",
                "sub_entry":[
                    {
                        "number":"3",
                        "source_type":"tag",
                        "source_id":"5366618",
                        "type":"source_selection",
                        "source_url":"https://h5.youzan.com/v2/showcase/tag?alias=s13zza7p",
                        "source_title":"女巫测试商品分组"
                    }
                ],
                "type":"link",
                "link_arr":[
                    {
                        "alias":"3exsvlcw6pdnj",
                        "link_id":365913404,
                        "link_type":"goods",
                        "link_title":"女巫商品复制",
                        "link_url":"https://shop683559.youzan.com/v2/goods/3exsvlcw6pdnj"
                    },
                    {
                        "alias":"2fnv002rxccn3",
                        "link_id":365913403,
                        "link_type":"goods",
                        "link_title":"女巫商品复制",
                        "link_url":"https://shop683559.youzan.com/v2/goods/2fnv002rxccn3"
                    },
                    {
                        "alias":"35z2ut7xjhk6n",
                        "link_id":365750705,
                        "link_type":"goods",
                        "link_title":"女巫水印13",
                        "link_url":"https://shop683559.youzan.com/v2/goods/35z2ut7xjhk6n"
                    }
                ],
                "key":"0.9388-link"
            },
            {
                "wx_link":{
                    "link_type":"goods",
                    "alias":"2osq7ug8lsd8v",
                    "link_url":"https://h5.youzan.com/v2/showcase/goods?alias=2osq7ug8lsd8v",
                    "link_title":"有赞精选自营测试商品，运费按重",
                    "link_id":"365624873"
                },
                "wx_title_date":"2019-03-28",
                "sub_title":"",
                "show_method":"1",
                "color":"#f9f9f9",
                "wx_title_author":"",
                "wx_title_link":"跳转到商品",
                "wx_title_link_type":"1",
                "title":"标题组件",
                "type":"title",
                "title_template":"1",
                "sub_entry":[

                ],
                "wx_link_url":"https://h5.youzan.com/v2/showcase/goods?alias=2osq7ug8lsd8v",
                "key":"0.2442-title"
            },
            {
                "action_text":"进入店铺组件",
                "type":"store",
                "key":"0.4666-store"
            },
            {
                "position_show_method":"0",
                "border_style_color":"#fff",
                "color":"#f9f9f9",
                "border_style_height":40,
                "hot_search_keys":[

                ],
                "hot_search_keys_new":[

                ],
                "text_align_method":"0",
                "text_color":"#999",
                "border_style_method":"0",
                "show_search_component":"1",
                "type":"search",
                "position_type":"0",
                "key":"0.5030-search"
            },
            {
                "bg_color":"#b88822",
                "color":"#badcff",
                "type":"notice",
                "content":"公告组件",
                "key":"0.1036-notice"
            },
            {
                "link_type":"history",
                "bg_color":"#ffffff",
                "show_method":"0",
                "color":"#000000",
                "font_size":14,
                "link_url":"https://www.youzan.com/v1/s/history",
                "alias":"",
                "link_title":"历史信息",
                "text":"历史消息",
                "type":"text",
                "show_split_line":"0",
                "link_id":"",
                "key":"0.7149-text"
            }
        ]
    },
    "tradeRecords":{
        "list":[

        ],
        "page":1,
        "loading":false,
        "finished":false
    },
    "skuConfig":{
        "type":"",
        "isMultiBtn":false,
        "isAddCart":false,
        "isGift":false,
        "isPoints":false,
        "isAjaxing":false,
        "ajaxType":"",
        "priceLabel":"",
        "skuComb":null,
        "selectedSku":null
    },
    "sku":{
        "collectionId":1063545,
        "collectionPrice":3400,
        "hideStock":false,
        "list":[

        ],
        "maxPrice":3400,
        "messages":[

        ],
        "minPrice":3400,
        "noneSku":true,
        "price":"34.00",
        "soldNum":0,
        "stockNum":78,
        "tree":[

        ]
    },
    "recommendGoods":{
        "list":[

        ],
        "page":1,
        "loading":false,
        "finished":false
    },
    "orderPromotion":{

    },
    "activityInfo":{
        "activityAlias":"2ok5dbg0riez3",
        "activityId":0,
        "activityType":0,
        "activityName":"",
        "activityTypeName":""
    },
    "pointsData":{
        "pointsName":"积分"
    },
    "postData":{
        "kdtId":491391,
        "goodsId":"",
        "skuId":"",
        "price":"",
        "pointsPrice":"",
        "postage":0,
        "num":0,
        "messages":{

        },
        "storeId":0,
        "activityAlias":"2ok5dbg0riez3",
        "activityId":0,
        "activityType":0,
        "useWxpay":1,
        "hotelGoods":"",
        "orderFrom":""
    },
    "display":{
        "showPlusButtons":false,
        "showCashBackPopup":false,
        "showTuanPopup":false,
        "showLoginPopup":false,
        "showDatePicker":false,
        "showBuyBtn":true,
        "showCartBtn":true,
        "showPointsBtn":false,
        "showForbidBuyBtn":true,
        "showGiftBtn":false,
        "showMiniShopBtn":true,
        "showMiniCartBtn":true,
        "showMiniPointsBtn":false,
        "showShareBtn":false,
        "showImBtn":true,
        "showDeliveryBar":true
    },
    "displayPop":{
        "sharePopShow":false,
        "skuPopShow":false
    },
    "priceCalendarData":{
        "nearlyFourMonthsPriceRange":{

        },
        "nearlyFourMonthMinPriceMap":{

        },
        "nearlyFourDayMarketableMap":{

        },
        "ecardPriceCalendarModelMap":{

        },
        "skuId":0,
        "monthArr":[

        ],
        "priceBarList":[

        ],
        "priceDateRange":"",
        "submitPriceDate":[

        ]
    },
    "marketActivity":{
        "luckyDrawGroup":{
            "activityId":8591363608,
            "activityName":"0元抽奖未开始",
            "level":"GOODS",
            "type":"luckyDrawGroup",
            "buttonType":3,
            "couponName":"",
            "beforeStartTime":2279527584,
            "endRemainTime":2797927584,
            "startTime":1557936000000,
            "endTime":1558454400000,
            "goodsNum":1,
            "participantNum":2,
            "shopId":491391,
            "solved":false,
            "sku":{
                "collectionId":1063545,
                "collectionPrice":3400,
                "hideStock":false,
                "list":[

                ],
                "maxPrice":0,
                "messages":[

                ],
                "minPrice":0,
                "noneSku":true,
                "oldPrice":"34.00",
                "price":"0.00",
                "soldNum":0,
                "stockNum":1,
                "tree":[

                ]
            }
        }
    },
    "orderActivity":{

    },
    "goodsActivity":{

    },
    "haitao":{
        "isHaitao":false,
        "haiTaoItemExtra":null,
        "haiTaoTradeMode":0
    },
    "goodsTag":""
  }
```
