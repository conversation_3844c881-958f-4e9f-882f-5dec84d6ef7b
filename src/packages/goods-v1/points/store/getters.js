import get from '@youzan/weapp-utils/lib/get';
import { SKU_SCENE, ACCOUNT_UNION_SCENE } from 'shared/packages/goods/common/constant';
import {
  getBottomViewConfig,
} from 'shared/packages/goods/containers/goods-bottom/store/common';

export default {
  /* ============ activity-banner ========== */
  showActivityBanner() {
    return false;
  },

  /* ============ goods-info ============== */
  // 商品信息价格标签
  goodsPriceTag() {
    return '';
  },

  goodsPrice(state) {
    const activity = get(state, 'marketActivity.pointsExchange', {});
    const { priceDateRange, goods } = state;
    const {
      pointsPrice: currentPrice,
      minPrice,
      maxPrice,
      minPointsPrice,
      maxPointsPrice,
    } = activity.sku || {};
    const {
      price: originPrice,
    } = state.sku || {};
    const {
      isVirtualTicket,
    } = goods;
    const isRange = minPointsPrice !== maxPointsPrice;

    // 电子卡券加上价格日历特殊逻辑
    if (isVirtualTicket && priceDateRange) {
      return {
        currentPrice: priceDateRange
      };
    }

    return {
      isRange,
      currentPrice,
      minPriceData: {
        yuan: minPrice,
        points: minPointsPrice,
      },
      maxPriceData: {
        yuan: maxPrice,
        points: maxPointsPrice,
      },
      originPrice,
    };
  },

  /* ============ goods-info-tip =========== */

  /* ============ goods-bottom ============= */
  // 积分商品主按钮展示
  goodsBottomView(state, getters) {
    const btnInfo = getBottomViewConfig(state, getters);
    if (btnInfo) {
      return btnInfo;
    }
    const activity = get(state, 'marketActivity.pointsExchange', {});
    const limitBuy = get(state, 'goods.limitBuy', false);
    const showBuyBtn = get(state, 'shopConfig.showBuyBtn', false) && get(state, 'display.showBuyBtn', false);

    const btnMain = {
      text: `${activity.points}兑换`,
      points: true,
      buy: false,
      show: true,
      isUmp: true,
      loading: false,
      skuScene: SKU_SCENE.POINTS_BUY,
      accountUnionScene: ACCOUNT_UNION_SCENE.POINTS_BUY,
    };
    const btnVice = {
      show: !limitBuy && showBuyBtn,
      text: '零售价购买',
      skuScene: SKU_SCENE.SEL_SKU,
      accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
    };
    return {
      main: btnMain,
      vice: btnVice,
    };
  },

  isPointsBuy(state) {
    return state.skuConfig.skuScene === SKU_SCENE.POINTS_BUY;
  },

  /* ============ goods-sku ============= */
  // 用于重写的sku数据
  skuData(state, getters) {
    const activity = get(state, 'marketActivity.pointsExchange', {});
    return getters.isPointsBuy ? activity.sku : state.sku;
  },

  activitySkuData(state) {
    return get(state, 'marketActivity.pointsExchange.sku', {});
  },

  skuPriceTag(state, getters) {
    return getters.isPointsBuy ? '积分兑换' : getters.skuCommonPriceTag;
  },

  /* ============= 限购 ================== */
  commonQuota(state, getters) {
    const activity = get(state, 'marketActivity.pointsExchange', {});
    return getters.isPointsBuy ? activity.limit.num : state.limit.num;
  },

  quotaUsed(state, getters) {
    const activity = get(state, 'marketActivity.pointsExchange', {});
    return getters.isPointsBuy ? activity.limit.used : state.limit.used;
  },

  quotaCycle(state, getters) {
    return getters.isPointsBuy ? 0 : getters.commonQuotaPeriod;
  },

  startSaleNum(state, getters) {
    return getters.isPointsBuy ? 1 : state.limit.startSaleNum;
  },
};
