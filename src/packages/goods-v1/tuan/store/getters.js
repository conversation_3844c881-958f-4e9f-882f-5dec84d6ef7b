import get from '@youzan/weapp-utils/lib/get';
import { splitPrice } from 'shared/packages/goods/store/utils/helpers';
import { SKU_SCENE, ACCOUNT_UNION_SCENE } from 'shared/packages/goods/common/constant';
import {
  getBottomViewConfig,
} from 'shared/packages/goods/containers/goods-bottom/store/common';

export default {
  activityTitle() {
    return '团购';
  },

  /* ============ activity-banner ========== */
  showActivityBanner() {
    return true;
  },

  countdown(state) {
    const activity = get(state, 'marketActivity.tuan', {});
    const { startRemainTime, endRemainTime } = activity;
    const now = +new Date();
    let desc;
    let end;
    if (startRemainTime > 0) {
      desc = '距开始';
      end = now + startRemainTime;
    } else if (endRemainTime > 0) {
      desc = '距结束仅剩';
      end = now + endRemainTime;
    } else {
      desc = '活动已结束';
      end = now;
    }

    return {
      desc,
      end,
      start: now
    };
  },

  /* ============ goods-info ============== */

  goodsPrice(state) {
    const activity = get(state, 'marketActivity.tuan', {});
    const { priceDateRange, goods } = state;
    const {
      price: currentPrice,
      minPrice,
      maxPrice,
      oldPrice: originPrice,
    } = activity.sku || {};
    const {
      isVirtualTicket,
    } = goods;
    const isRange = minPrice !== maxPrice;

    // 电子卡券加上价格日历特殊逻辑
    if (isVirtualTicket && priceDateRange) {
      return {
        currentPrice: priceDateRange
      };
    }

    return {
      isRange,
      currentPrice,
      minPriceData: splitPrice(minPrice),
      maxPriceData: splitPrice(maxPrice),
      originPrice,
    };
  },

  /* ============ goods-info-tip =========== */

  // 返现文案
  goodsTipCashBackDesc(state) {
    const activity = get(state, 'marketActivity.tuan', {});
    const { soldCount = 0 } = activity;
    let { rules = [] } = activity;
    rules = rules.filter(item => item.min > 0);
    if (rules.length === 0) {
      return '';
    }
    const minStage = rules[0];
    const maxStage = rules[rules.length - 1];
    let cashBackDesc = '';
    if (soldCount < minStage.min) {
      cashBackDesc = `满${minStage.min}件返${minStage.money}元`;
    } else {
      const currentStage = rules.findIndex(item => item.min > soldCount);
      cashBackDesc = currentStage === -1 ? `下单立返${maxStage.money}元` : `下单立返${rules[currentStage - 1].money}元`;
    }
    return cashBackDesc;
  },

  // 设置进度条比例
  goodsTipPercentage(state) {
    const activity = get(state, 'marketActivity.tuan', {});
    const { soldCount = 0 } = activity;
    let { rules = [] } = activity;
    rules = rules.filter(item => item.min > 0);
    if (rules.length === 0) {
      return 0;
    }
    const maxStage = rules[rules.length - 1];
    return soldCount / maxStage.min * 100;
  },

  /* ============ goods-bottom ============= */

  // 团购主按钮展示
  goodsBottomView(state, getters) {
    const btnInfo = getBottomViewConfig(state, getters);
    if (btnInfo) {
      return btnInfo;
    }
    const activity = get(state, 'marketActivity.tuan', {});
    const noStart = activity.startRemainTime > 0;
    const btnMain = {
      text: noStart ? '即将开始' : '立即参团',
      show: true,
      isUmp: true,
      buy: true,
      loading: false,
      disabled: noStart,
      skuScene: SKU_SCENE.ACT_BUY,
      accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
    };
    const btnVice = {
      text: '邀请下单返现',
      show: activity.isCash, // 团购有无返现
      skuScene: SKU_SCENE.SHARE,
      disabled: !activity.isCash
    };
    return {
      main: btnMain,
      vice: btnVice,
    };
  },

  /* ============ goods-sku ============= */
  // 用于重写的sku数据
  skuData(state) {
    const activity = get(state, 'marketActivity.tuan', {});
    return activity.sku || state.sku;
  },

  activitySkuData(state) {
    return get(state, 'marketActivity.tuan.sku', {});
  },

  skuPriceTag(state, getters) {
    if (state.skuConfig.skuScene === SKU_SCENE.ACT_BUY) {
      return '团购';
    }
    return getters.skuCommonPriceTag;
  },

  tuanIntroRulesDetail(state) {
    const activity = get(state, 'marketActivity.tuan', {});
    // 普通团购返现
    if (activity.isCash === 1) {
      return [
        '1、通过【微信支付】付款，返现金额将通过 【微信退款】发放，请注意查收；',
        '2、通过【银行卡】付款，返现金额将在3天内，原路发放至银行卡账户；',
        '3、最高返现金额不超过你的现金支付金额；',
      ];
    }
    if (activity.isCash === 2) {
      const limitText = (activity.limitedDays === 0 || !activity.limitedDays) ? '' : (activity.limitedDays + '天');
      // 团购返储值金
      return [
        `1、订单支付完成${limitText}后，且未发起退款才会收到返现金额；`,
        '2、返现金额将发放至储值卡余额，请前往个人中心查看',
      ];
    }
    return [];
  },

  tuanIntroBarTitle(state) {
    const activity = get(state, 'marketActivity.tuan', {});
    if (activity.isCash === 1) {
      return '团购返现说明';
    }
    if (activity.isCash === 2) {
      // 团购返储值金
      return '返储值金说明';
    }
    return '';
  },

};
