<goods-info-tip-view
    is-link
    title="{{tuanIntroBarTitle}}"
    link-tip="玩法详情"
    bind:tap:cell="showPopup"
>
  <view>
    <view class="current">
      <view class="current__text">已售{{soldCount}}件</view>
      <view class="current__text">{{goodsTipCashBackDesc}}</view>
    </view>
    <view class="rules">
      <van-progress 
        class="rules__graph" 
        percentage="{{goodsTipPercentage > 100 ? 100 : goodsTipPercentage}}"
        show-pivot="{{false}}"
        color="#f3594b" />
      <view class="rules__text">
        <block wx:for="{{rules}}" wx:key="amount">
          <text>满{{ item.min }}件返￥{{ item.money }}元;</text>
        </block>
      </view>
    </view>
  </view>
</goods-info-tip-view>

<goods-popup
  show="{{ isPopupShown }}"
  bind:tap:btn="closePopup">
  <view class="cashback-tip__container">
    <view class="container__text">在 {{startTime}} 至 {{endTime}} 期间，购买团购商品，可获得累计返现:</view>
    <block wx:for="{{allRules}}" wx:key="amount">
      <view class="container__text">* 售出达{{ item.min }}件，每件返现￥{{ item.money }}元{{ index + 1 === allRules.length ? '。' : ';' }}</view>
    </block>
    <block wx:for="{{tuanIntroRulesDetail}}" wx:key="item" class="container__body">
      <view class="container__text">{{ item }}</view>
    </block>
  </view>
</goods-popup>