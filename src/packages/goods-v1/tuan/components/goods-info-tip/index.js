import get from '@youzan/weapp-utils/lib/get';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';

VanxComponent({

  mapData: {
    ...mapState({
      tuan: (state) => get(state, 'marketActivity.tuan', {}),
      rules: (state) => get(state, 'marketActivity.tuan.rules', []).filter(item => item.min > 0),
      allRules: (state) => get(state, 'marketActivity.tuan.allRules', []).filter(item => item.min > 0),
      soldCount: (state) => get(state, 'marketActivity.tuan.soldCount', 0),
      startTime: (state) => get(state, 'marketActivity.tuan.startTime', ''),
      endTime: (state) => get(state, 'marketActivity.tuan.endTime', ''),
    }),
    ...mapGetters([
      'goodsTipCashBackDesc',
      'goodsTipPercentage',
      'tuanIntroBarTitle',
      'tuanIntroRulesDetail',
    ]),
  },
  properties: {
    title: String,
    themeClass: String,
  },

  data: {
    isPopupShown: false,
  },

  methods: {
    showPopup() {
      this.setYZData({
        isPopupShown: true
      });
    },

    closePopup() {
      this.setYZData({
        isPopupShown: false
      });
    },
  }
});
