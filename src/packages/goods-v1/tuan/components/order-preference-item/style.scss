@import '~shared/common/css/_mixins.scss';

  .order-preference__item {
    position: relative;
    display: flex;
    line-height: 24px;
    padding: 15px 15px 15px 0;
    width: calc(100vw - 15px);
    box-sizing: border-box;

    &:first-child {
      padding-top: 0;
    }

    .item-view {
      margin: 1px 0 0 10px;
      color: #111;
      font-size: 12px;
    }

    & .link-item {
      margin-right: 15px;

      &::after {
        @include customer-arrow;
        position: absolute;
        top: 26px;
        right: 15px;
        left: auto;
      }
    }
  }

  .color-38f {
    color: #3388FF;
  }

  .text {
    display: inline;
  }
