import get from '@youzan/weapp-utils/lib/get';
import theme from 'shared/common/components/theme-view/theme';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapState({
      tuan: (state) => state.marketActivity.tuan || {},
      rules: (state) => get(state, 'marketActivity.tuan.rules', []).filter(item => item.min > 0),
    }),
  },

  data: {
    themeGeneral: '',
  },

  ready() {
    theme.getThemeColor('general')
      .then(color => {
        this.setYZData({
          themeGeneral: color
        });
      });
  },
});
