<page-container
  fixed-bottom
  show-service-due
  show-shop-status
  show-store-switch
  forbid-copyright="{{ !showGoods }}"
  title-text="{{ title }}"
  open-custom-nav
>
  <view class="feature-page__top-hook" />

  <block wx:for="{{ beforeLoadComponent }}" wx:key="type">
    <view wx:if="{{ item.type === 'goods-image' }}" class="image-container">
      <!-- 主图 -->
      <goods-image
        brief-goods="{{ briefData.goods }}"
      />
    </view>
    
    <!-- 活动倒计时 -->
    <activity-banner
      wx:if="{{ showGoods && item.type == 'activity-banner' }}"
    />

    <block wx:if="{{ item.type === 'goods-info' }}">
      <!-- 商品标题，价格 -->
      <goods-info bind:share="handleShareClick">
        <slot name="goods-info-price-tag" slot="goods-info-price-tag"/>
        <slot name="goods-info-tip" slot="goods-info-tip"/>
      </goods-info>
      <!-- 成交记录轮播 -->
      <goods-trade-carousel wx:if="{{ showGoods }}" />
    </block>

    <custom-component wx:if="{{ item.custom }}" component="{{ item.type }}" />
  </block>


  <block wx:if="{{ showGoods }}">
    <block wx:for="{{ afterLoadComponent }}" wx:key="name">
        <!-- 推荐网点 -->
      <recommend-store wx:if="{{ item.type == 'recommend-store' }}" />

      <!-- 优惠信息、促销 -->
      <order-preference wx:if="{{ item.type == 'order-preference' }}">
        <!-- popup里的活动信息 -->
        <slot name="order-preference-item" slot="order-preference-item"/>
      </order-preference>

      <!--优惠套餐推荐区域-->
      <discount-packages wx:if="{{ item.type == 'discount-packages' }}"/>

      <!-- 打包一口价 -->
      <goods-bundle wx:if="{{ item.type == 'goods-bundle' }}"/>

      <!-- 加价购 -->
      <plus-buy wx:if="{{ item.type == 'plus-buy' }}"/>

      <block wx:if="{{ item.type == 'activity' }}">
        <!-- 诸如groupon-presale -->
        <slot name="activity"></slot>
      </block>

      <!-- 商品购买信息区域 -->
      <buy-info wx:if="{{ item.type == 'buy-info' }}"/>

      <block wx:if="{{ item.type == 'buy-info-extra' }}">
        <!-- 商品购买额外信息，诸如凑团 -->
        <slot name="buy-info-extra"></slot>
      </block>

      <!-- 店铺信息 -->
      <shop-info wx:if="{{ item.type == 'shop-info' }}"/>

      <!-- 评价区域 -->
      <goods-review wx:if="{{ item.type == 'goods-review' }}" />

      <!-- 买家秀区域 -->
      <buyer-show wx:if="{{ item.type == 'buyer-show' }}" />

      <!-- 商品详情 -->
      <goods-detail wx:if="{{ item.type == 'goods-detail' }}" />

      <!-- 底部按钮 -->
      <goods-bottom
        wx:if="{{ item.type == 'goods-bottom' }}"
        bind:share="handleShareClick"
      />

      <!-- 分享按钮 -->
      <share-goods
        wx:if="{{ item.type == 'share-goods' }}"
        id="share-goods"
      ></share-goods>

      <!-- 分销员按钮 -->
      <salesman-icon
        wx:if="{{ item.type == 'salesman-icon' }}"
        bind:set-share="onSalesmanSetShare"
        bind:share="onSalesmanShareCard"
      />

      <!-- 悬浮窗 -->
      <floating-nav
        wx:if="{{ item.type == 'floating-nav' }}"
        bind:share="handleShareClick"
      />

      <block wx:if="{{ item.type == 'act-custom-block' }}">
        <!-- 活动自定义模块 -->
        <slot name="act-custom-block"/>
      </block>

      <!-- 新版sku -->
      <goods-sku wx:if="{{ item.type == 'goods-sku' }}"/>
      
      <custom-component wx:if="{{ item.custom }}" component="{{ item.type }}" />
    </block>
  </block>
</page-container>

<shop-pop-manager wx:if="{{ showShopPopManager }}" source="{{ 2 }}" />
