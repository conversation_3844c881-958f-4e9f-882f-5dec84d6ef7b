import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState } from '@youzan/vanx';
import goodsPageEvents from '../../common/goods-page-events';
import { design } from './design.json';

VanxComponent({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  properties: {
    showGoods: Boolean
  },

  data: {
    pageWindowLock: false
  },

  mapData: {
    ...mapState({
      alias: (state) => state.goods.alias,
      title: (state) => state.goods.title
    })
  },

  methods: {
    ...goodsPageEvents
  },

  ready() {
    const beforeLoadComponent = [];
    const afterLoadComponent = [];
    // eslint-disable-next-line no-unused-vars
    for (const component of design) {
      if (component.needAllData == 'no') {
        beforeLoadComponent.push(component);
      } else {
        afterLoadComponent.push(component);
      }
    }

    this.setYZData({
      beforeLoadComponent,
      afterLoadComponent
    });
    setTimeout(() => {
      this.setYZData({
        showShopPopManager: true
      });
    }, 2000);
  }
});
