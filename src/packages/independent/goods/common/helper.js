import Storage from './storage';
import { promisifyCall } from './promisify';

const storage = Storage();
const config = {
  appId: 'wxf11c5910cb729a82',
  clientId: '4d65249d377b2c3ed8',
  clientSecret: '1cdc05151d64f3a4a6ebd0e9de64422a',
  grantType: 'yz_union',
};

// 需要清理历史token缓存时，升级tokenVersion来进行重登录
const TOKEN_VERSION = '1';

function weakCheckToken() {
  return storage
    .getAsync('app:token')
    .then((token) => {
      // 插件跳过来的需重新请求token，涉及到账号打通
      if (token.tokenVersion !== TOKEN_VERSION || !token.accessToken) {
        return Promise.reject('no token');
      }

      // 直接当作token合法
      return Promise.resolve(token);
    })
    .catch(() => {
      return Promise.reject('no token');
    });
}

function loginYouzan(code) {
  return promisifyCall('wx.request', {
    method: 'POST',
    url:
      'https://h5.youzan.com/wscshop/weapp/authorize.json?app_id=wxf11c5910cb729a82',
    data: { code, ...config },
    header: {
      'Extra-Data': '{"is_weapp":1,"sid":"","version":"","client":"weapp"}',
    },
  }).then((res) => {
    const { data = {} } = res;
    if (data.code === 0) {
      return Promise.resolve(data.data);
    }
    throw new Error(data.msg || '未知错误');
  });
}

let loginPromise = null;
/**
 * 登录
 * @param {Function} callback 成功回调（不要使用，请根据返回的 promise 进行下一步）
 * @param {Number} retry 尝试登录次数
 * @returns {Object} Promise<token>
 */
function login(callback = () => {}, retry = 6) {
  if (retry <= 0) {
    return Promise.reject();
  }

  loginPromise =
    loginPromise ||
    promisifyCall('wx.login')
      .catch((error) => {
        error = typeof error === 'object' ? error.errMsg : '微信登录失败';
        console.info('[app:wx:login:fail]', error);

        loginPromise = null;
        throw new Error(error);
      })
      .then(({ code }) => {
        console.info('[app:wx:login:success]', code);

        return loginYouzan(code).catch((error) => {
          /**
           * 这个回调很危险，如果登录失败，而回调又依赖 token，可能存在循环
           */
          callback();
          loginPromise = null;

          // 获取微信信息失败，尝试重新获取
          if (code === 135000025 || code === 160210092) {
            setTimeout(() => {
              login(() => {}, retry - 1);
            }, 100);
            return Promise.reject();
          }

          throw new Error(error);
        });
      });

  return loginPromise;
}

export function checkSession(options = {}) {
  // 覆盖配置
  Object.keys(config).forEach((it) => {
    if (options[it]) {
      config[it] = options[it];
    }
  });
  return weakCheckToken().catch(() => login());
}

export function getOfflineId(kdtId) {
  return storage.getAsync(`offline_id_${kdtId || 0}`);
}
