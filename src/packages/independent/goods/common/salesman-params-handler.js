/**
 * 急速下单独立分包 修复 支持链接处理sl标识内容
 */
const FROM_PARAMS = 'from_params';
const DEFAULT_KDT_ID = 0;
const SHARE_NUM = 1;

export const stringifyTrackData = (params) => {
  return Object.keys(params).reduce((accStr, curKey) => {
    const element = params[curKey];
    const withDelimiter = accStr ? `${accStr}!` : accStr;
    return `${withDelimiter}${curKey}~${element}`;
  }, '');
};

/**
 * 获得 form_params 参数
 */
export const getFromParams = (params) => {
  const { sl, sls, kdtId = DEFAULT_KDT_ID, oldFromParamsData } = params;
  const slOrSls = sl || sls;
  const withSlData = slOrSls ? { sl: slOrSls, online_kdt_id: `${kdtId}` } : {};
  const withOldFromParamsData = oldFromParamsData
    ? { ...oldFromParamsData, ...withSlData }
    : withSlData;
  return `${stringifyTrackData(withOldFromParamsData)}`;
};

export const getSalesmanParamsObject = (params) => {
  const {
    sl,
    sls,
    kdtId = DEFAULT_KDT_ID,
    oldFromParamsData,
    shareParams = {},
  } = params;
  const propertiesNeedToAdd = ['sl', 'sls'];
  const someProperties = propertiesNeedToAdd.reduce((acc, cur) => {
    const value = params[cur];
    return value ? { ...acc, [cur]: value } : acc;
  }, {});

  const commonShareInfo = {
    is_share: SHARE_NUM,
  };
  return sl || sls
    ? {
        ...commonShareInfo,
        ...shareParams,
        ...someProperties,
        [FROM_PARAMS]: getFromParams({
          sl,
          sls,
          kdtId,
          oldFromParamsData,
        }),
      }
    : {
        ...commonShareInfo,
        ...shareParams,
        ...someProperties,
      };
};
