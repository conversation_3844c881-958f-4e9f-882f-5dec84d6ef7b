function each(obj, iterator, context) {
  if (obj == null) return;
  if (obj.length === +obj.length) {
    for (let i = 0, length = obj.length; i < length; i++) {
      iterator.call(context, obj[i], i, obj);
    }
  } else {
    const keys = Object.keys(obj);
    for (let i = 0, length = keys.length; i < length; i++) {
      iterator.call(context, obj[keys[i]], keys[i], obj);
    }
  }
}

function _strToSnakeCase(str) {
  // 不转数字
  if (/^\d+$/.test(str)) {
    return str;
  }

  return str.replace(/[A-Z]/g, (item) => {
    return '_' + item.toLowerCase();
  });
}

function _toCamelCase(str) {
  // 不转数字
  if (/^\d+$/.test(str)) {
    return str;
  }

  return str.replace(/_[a-z]/g, (item) => {
    return item[1].toUpperCase();
  });
}

function toUnderscoreCase(obj) {
  if (typeof obj === 'string') {
    return _strToSnakeCase(obj);
  }
  if (typeof obj === 'object') {
    var newObj = {};
    if (obj instanceof Array) {
      newObj = [];
    }
    each(obj, (value, key) => {
      if (typeof value === 'object') {
        newObj[_strToSnakeCase(key)] = toUnderscoreCase(value);
      } else {
        newObj[_strToSnakeCase(key)] = value;
      }
    });
    return newObj;
  }
  return obj;
}

function toCamelCase(obj) {
  if (typeof obj === 'string') {
    return _toCamelCase(obj);
  }
  if (typeof obj === 'object') {
    var newObj = {};
    if (obj instanceof Array) {
      newObj = [];
    }
    each(obj, (value, key) => {
      if (typeof value === 'object') {
        newObj[_toCamelCase(key)] = toCamelCase(value);
      } else {
        newObj[_toCamelCase(key)] = value;
      }
    });
    return newObj;
  }
  return obj;
}

export default {
  toSnakeCase: toUnderscoreCase,
  toCamelCase
};
