export default function (app = {}) {
  return {
    set: function set(key, value, options) {
      options = options || {};
      var expire = options.expire || 7;
      try {
        wx.setStorageSync(key, {
          value,
          version: app.VERSION || '',
          expire: Date.now() + expire * 24 * 3600 * 1000
        });
      } catch (e) {
        console.error(e);
      }
    },
    setAsync: function setAsync(key, value, options = {}) {
      var _options$expire = options.expire;
      var expire = _options$expire === undefined ? 7 : _options$expire;

      return new Promise((resolve, reject) => {
        wx.setStorage({
          key,
          data: {
            value,
            version: app.VERSION || '',
            expire: Date.now() + expire * 24 * 3600 * 1000
          },
          success: resolve,
          fail: reject
        });
      });
    },
    get: function get(key) {
      try {
        var data = wx.getStorageSync(key);
        if (data.expire > Date.now()) {
          return data.value;
        }
        wx.removeStorage({ key });
      } catch (e) {
        console.error(e);
      }
    },
    getAsync: function getAsync(key) {
      return new Promise((resolve, reject) => {
        wx.getStorage({
          key,
          success: function success(res) {
            var _res$data = res.data;
            var data = _res$data === undefined ? {} : _res$data;
            var _data$expire = data.expire;
            var expire = _data$expire === undefined ? '' : _data$expire;
            var _data$value = data.value;
            var value = _data$value === undefined ? '' : _data$value;


            if (expire > Date.now()) {
              resolve(value);
            } else {
              resolve('');
              wx.removeStorage({ key });
            }
          },

          fail: reject
        });
      });
    },
    remove: function remove(key) {
      try {
        wx.removeStorageSync(key);
      } catch (e) {
        console.error(e);
      }
    },
    removeAsync: function removeAsync(key) {
      return new Promise((resolve, reject) => {
        wx.removeStorage({
          key,
          success: resolve,
          fail: reject
        });
      });
    },
    clear: function clear() {
      try {
        wx.clearStorageSync();
      } catch (e) {
        console.error(e);
      }
    },
    clearAsync: function clearAsync() {
      return new Promise((resolve, reject) => {
        wx.clearStorage({
          success: resolve,
          fail: reject
        });
      });
    }
  };
}
