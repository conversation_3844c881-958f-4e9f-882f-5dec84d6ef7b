export default function promisify(func) {
  return function promised(args) {
    return new Promise((resolve, reject) => {
      func.call(func, {
        ...args,
        success: resolve,
        fail: reject
      });
    });
  };
}
export const promisifyWxStyle = api => (option, self = {}) => new Promise(
  (res, rej) => api({
    ...option,
    success: res,
    fail: rej
  }, self)
);

export function promisifyCall(weappApi, params = {}) {
  if (!/^wx\./.test(weappApi)) throw new Error('promisifyCall 仅支持 wx 的接口调用')

  let api = weappApi.substr(3)

  if (!wx[api]) throw new Error(`不存在 ${weappApi} API`)

  return new Promise((resolve, reject) => {
    wx[api]({
      ...params,
      success(res) {
        params.success && params.success(res)
        params.complete && params.complete(res)
        resolve(res)
      },
      fail(err) {
        params.fail && params.fail(err)
        params.complete && params.complete(err)
        reject(err)
      }
    })
  })
}
