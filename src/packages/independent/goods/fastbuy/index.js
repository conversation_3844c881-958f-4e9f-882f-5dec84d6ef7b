import { checkSession, getOfflineId } from '../common/helper';
import { getUrlLogParams } from '../common/get-url-log-params';
import { getSalesmanParamsObject } from '../common/salesman-params-handler';
import Event from '@youzan/weapp-utils/lib/event';

/* eslint-disable */
Page({
  data: {
    url: '',
  },
  onLoad(options) {
    Event.on('buy-h5:pay:call', detail => {
      if (detail.orderNo) {
        const baseUrl = `https://cashier.youzan.com/pay/wsctrade_pay?order_no=${detail.orderNo}`;
        this.baseUrl = baseUrl;
      }
    });
    this.config = wx.getExtConfigSync() || {};
    const { alias, sl } = options;

    const fail = (e) => {
      e && console.log(e);
      wx.reLaunch({
        url: '/packages/home/<USER>/index',
      });
    };
    if (
      alias &&
      alias.length > 0 &&
      this.config.appId &&
      this.config.appId.length > 0
    ) {
      checkSession(this.config)
        .then((token) => {
          const sid = token.sessionId || token.session_id;
          const accessToken = token.accessToken || token.access_token;
          const kdt_id = token.kdtId || token.kdt_id;
          const params = {
            alias,
            accessToken,
            kdt_id,
            sid,
            isWeapp: 1,
            weappType: 'share',
            isFastBuy: 1,
            mpVersion: this.config.userVersion || '',
            bizEnv: BUILD_TARGET || 'wsc',
            ...getSalesmanParamsObject({ sl, kdtId: kdt_id }),
            ...getUrlLogParams(options),
          };

          let baseUrl = 'https://tuicashier.youzan.com/pay/wscgoods_detail';
          Object.keys(params).forEach((key, index) => {
            const symb = index === 0 ? '?' : '&';

            baseUrl += `${symb}${key}=${params[key]}`;
          });

          this.kdtId = kdt_id;
          this.baseUrl = baseUrl;
          this.setWithOfflineId();
        })
        .catch((e) => {
          console.log(e);
        });
    } else {
      fail();
    }
  },
  onShow() {
    this.setWithOfflineId();
  },
  setWithOfflineId() {
    if (this.baseUrl && this.kdtId) {
      getOfflineId(this.kdtId)
        .then((data) => {
          if (data && data > 0) {
            this.setData({
              url: `${this.baseUrl}&oid=${data}`,
            });
          } else {
            this.setData({
              url: this.baseUrl,
            });
          }
        })
        .catch((e) => {
          console.log(e);
          this.setData({
            url: this.baseUrl,
          });
        });
    }
  },
  onWebLoad(event) {
    console.log('load', event);
  },

  onError(event) {
    console.log('error', event);
  },

  onPostMessage(event) {
    console.log('message', event);
  },
});
