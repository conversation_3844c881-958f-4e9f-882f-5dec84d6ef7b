import { PAGE_TYPE, NAVIGATE_TYPE, navigateToRantaPage } from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';

Page({
  onLoad(query = {}) {
    const { bookKey = '', orderNo = '' } = mapKeysToCamelCase(query);

    navigateToRantaPage({
      pageType: !bookKey && orderNo ? PAGE_TYPE.PAY : PAGE_TYPE.ORDER,
      type: NAVIGATE_TYPE.REDIRECT,
      query,
    });
  },
});
