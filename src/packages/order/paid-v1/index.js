import { VanxPage } from 'pages/common/wsc-page/index';
import { mapState } from '@youzan/vanx';
import moneyHelper from '@youzan/weapp-utils/lib/money';
import get from '@youzan/weapp-utils/lib/get';
import each from '@youzan/weapp-utils/lib/each';
import args from '@youzan/weapp-utils/lib/args';
import { generateBuyGoodsList } from 'shared/components/cart/buy';
import {
  showChangePriceDialog,
  getWaitBuyList,
} from 'shared/packages/order/pay-result/common/service';
import theme from 'shared/common/components/theme-view/theme';
import navigate from '@/helpers/navigate';
import EventUtil from '@youzan/weapp-utils/lib/event';
import store from './store';
import { addSalesmanParams } from '@youzan/salesman-params-handler';
import buildUrl from '@youzan/utils/url/buildUrl';
import { getShareParams } from 'shared/utils/share-params';

/* #ifdef BUILD_ENV=youzanyun */
import SaasPageConfig from './saas';
/* #endif */

let extendPageConfig = {};
/* #ifdef BUILD_ENV=youzanyun */
extendPageConfig = SaasPageConfig;
/* #endif */

// 埋点参数
const shareParams = getShareParams('native_custom');
const app = getApp();
const { windowHeight } = app.getSystemInfoSync();
// 小程序上暂时支持的支付有礼活动类型
const suportedActivity = [
  'tradeincard',
  'promocode',
  'couponpackage',
  'seller',
  'seniorseller',
  'feature',
  'present',
];

const ACTION_BUTTON_MAP = {
  SUBSCRIPTION: 'openSubscription', // 订阅物流
  PICK_UP_CODE: 'openPickUpCode', // 查看提货码
  CARD_VOUCHER: 'openVoucherCard', // 查看电子卡券凭证
  PREV_WEAPP: 'gotoPreWeapp', // 返回上一个小程序
  // BUY_AGAIN: '', // 再来一单
  // GIVE_AWAY: '', // 立即赠送
  DETAIL: 'gotoDetail', // 查看详情
  SHARE: 'openShareSheet', // 分享一下
};

/**
 * 后端返回key，对应前端模板value
 */
const PAY_RESULT_TEMPLATE = {
  NORMAL: 'success',
  NEWHOPE: 'success',
  WAIT: 'wait',
  PAY_ERROR: 'error',
  OVERSALE_ERROR: 'oversale-error',
  /**
   * 降价拍
   */
  AUCTION: 'auction',
  AUCTION_WAIT: 'wait',
};

VanxPage(extendPageConfig, {
  store,
  data: {
    coupon: {
      show: false,
      quantity: 0, // 优惠券数量
      customShareDesc: '',
      shareLinkImgUrl: '',
    },
    waitBuyDialog: {
      show: false,
      goodsList: [],
    },
    showOfficalAccount: false,
    isRefreshing: false,
    shareItems: [
      {
        key: 'sendFriend',
        name: '发送给朋友',
        openType: 'share',
      },
      {
        key: 'sharePoster',
        name: '生成海报',
      },
    ],
    windowHeight,
    showWxSubscribe: false, // 是否展示微信订阅消息
    // 内购券活动
    inSourcingfission: {
      activityId: '',
      umpAlias: '',
    },
    hasSubscribe: false, // 是否已经订阅了
    themeGeneral: '',
    showRecommendBlock: false,
    showCpsRecommendBlock: false,
    weappImg: '', // 内购支付成功页图片
    solitaireShareData: {},
    isGroupBuy: false,
    groupbuyShareData: {}, // 社区团购订单支付成功分享参数
    shopAd: {}, // 是否展示店铺广告
  },

  mapData: {
    ...mapState([
      'design',
      'orderNo',
      'fetching',
      'payResult',
      'phasePaymentStage',
      'showSubscription',
      'showVoucherCard',
      'voucherCard',
      'showSelfFetch',
      'selfFetch',
      'showShare',
      'extensionConfig',
      'showSolitaire',
      'showGroupbuyShare',
      'showGroupbuySelfFetch',
      'componentSubscribe',
      'hidePickUpCodeBtn',
      'groupbuyBuyAgainPath',
    ]),
  },

  onLoad(query) {
    let orderNo;
    let orderData = {};

    if (query.order_no || query.orderNo) {
      orderNo = query.order_no || query.orderNo;
    } else {
      orderData = app.db.get(query.dbid) || {};
      orderNo = orderData.order_no;
    }

    this.$commit('SET_ORDER_NO', orderNo);
    this.$commit(
      'SET_PAYMENT_STAGE',
      orderData.phasePaymentStage || query.phase
    );

    this.bootstrap();

    wx.hideShareMenu();

    app.getShopConfigData().then((data = {}) => {
      const showOfficalAccount = wx.canIUse('official-account') && +data.show_follow_mp_after_pay === 1;
      this.setYZData({ showOfficalAccount });
    });

    // 获取主题色
    theme.getThemeColor('general').then((color) => {
      this.setYZData({ themeGeneral: color });
    });
  },

  bootstrap() {
    wx.showToast({
      icon: 'loading',
      // 持续时间最长为10s
      duration: 10000,
    });

    this.$dispatch('GET_DESIGN').then(({ config = {}, design }) => {
      this.$commit('SET_EXTENSION_CONFIG', { ...config, ...config.profile });
      this.$commit('SET_DESIGN', design);
      // 推荐商品组件展示走配置
      const isShowRecommendBlock = design.find((item) => item.type === 'recommend-block');
      const isShowCpsRecommendBlock = design.find((item) => item.type === 'cps-goods-recommend');
      if (isShowRecommendBlock) {
        this.setYZData({
          showRecommendBlock: !!isShowRecommendBlock,
        });
      }
      if (isShowCpsRecommendBlock) {
        this.setYZData({
          showCpsRecommendBlock: !!isShowCpsRecommendBlock,
        });
      }
    });

    this.fetchPayResultShopAD().then(this.fetchPayResult);
  },

  fetchPayResult() {
    wx.showToast({
      icon: 'loading',
      // 持续时间最长为10s
      duration: 10000,
    });

    this.$dispatch('FETCH_PAY_RESULT', this.requestQuery())
      .then((payResult) => {
        wx.hideToast();
        this.payResultSuccessHandler(payResult);
      })
      .catch(({ msg } = {}) => {
        wx.hideToast();
        wx.showModal({
          title: msg || '未知异常',
          content: this.data.orderNo,
          showCancel: false,
        });
      });
  },

  payResultSuccessHandler(payResult = {}) {
    const { payState, dialog, shopCoupons } = payResult;
    // 支付成功后获取优惠券信息
    if (payState === 'ALL_PAID') {
      this.handleCoupon(shopCoupons);
    }

    this.setPayResult(payResult);
    if (dialog) {
      showChangePriceDialog({
        dialog,
        jumpHandler: () => {
          this.gotoDetail();
        },
      });
    }
  },

  setPayResult(payResult = {}) {
    const {
      selfFetch,
      buttonGroup = {},
      shareMessage,
      orderItems,
      shopCoupons,
      isShowRecommendGoods,
      isGroupBuy,
      weappImg,
    } = payResult;
    const showGoPreWeapp = get(
      this.data,
      'wscpage.jumpConfig.replaceOrderDetailBtnWithReturnBtn'
    );

    // 处理模版
    payResult.pageTemplate = PAY_RESULT_TEMPLATE[payResult.pageTemplate]
      || PAY_RESULT_TEMPLATE.PAY_ERROR;

    // 处理金额
    payResult.realPayAmount = typeof payResult.realPayAmount === 'number'
      ? moneyHelper(payResult.realPayAmount).toYuan()
      : null;

    payResult.buttonList = [];
    if (showGoPreWeapp) {
      // 插入“返回上一个小程序”按钮
      buttonGroup.PREV_WEAPP = {
        type: 'PREV_WEAPP',
        defaultText: '返回上一个小程序',
      };
    }
    each(ACTION_BUTTON_MAP, (v, key) => {
      const item = buttonGroup[key];
      if (!item) {
        return;
      }
      const { type } = item;
      item.buttonType = ['CARD_VOUCHER', 'GIVE_AWAY'].indexOf(type) !== -1
        || (type === 'SHARE' && !buttonGroup.CARD_VOUCHER)
        ? 'primary'
        : 'default';
      // 修改分享一下按钮的逻辑，不直接打开分享而是打开分享菜单
      if (type === 'SHARE') item.openShareSheet = true;
      payResult.buttonList.push(item);
    });

    // 处理自提数据
    if (selfFetch) {
      selfFetch.wholeAddress = [
        selfFetch.province,
        selfFetch.city,
        selfFetch.county,
        selfFetch.addressDetail,
      ].join('');
    }

    if (payResult.orderTags && payResult.orderTags.SOLITAIRE_BUY) {
      this.openSolitaireDialog();
    }
    if (payResult.isGroupBuy) {
      this.openGroupbuyShareDialog();
    }
    // 确定是否需要展示简版支付成功页
    const hasActivity = payResult.paidPromotion
      && suportedActivity.indexOf(payResult.paidPromotion.promotionType) > -1;
    const hasCoupon = shopCoupons && shopCoupons.quantity > 0;
    const showWxSubscribe = payResult.wxSubscribeScenesList
      && payResult.wxSubscribeScenesList.length > 0
      && wx.canIUse('requestSubscribeMessage');

    if (
      !hasActivity
      && !hasCoupon
      && !isShowRecommendGoods
      && !showWxSubscribe
    ) {
      payResult.simpleStyle = true;
    }

    // 分享
    if (shareMessage) {
      wx.showShareMenu();
    }

    const firstGoods = (orderItems && orderItems[0]) || {};
    this.$commit('SET_FIRST_GOODS', firstGoods);

    const isNewHotelGood = +(firstGoods.newHotelGood || '');

    if (isNewHotelGood) {
      this.setYZData({
        shareItems: [
          {
            key: 'sendFriend',
            name: '发送给朋友',
            openType: 'share',
          },
        ],
      });
    }

    // 设置内购券活动
    this.setInSourcingfission(payResult);

    this.$commit('SET_PAY_RESULT', payResult);
    this.$commit('SET_FETCHING', false);
    this.setYZData({
      isNewHotelGood,
      isGroupBuy,
      showWxSubscribe,
      weappImg:
      weappImg
      || 'https://b.yzcdn.cn/public_files/53007f232f165a057088c0cfe1ccd606.png',
    });
  },

  // 设置内购券活动
  setInSourcingfission(payResult = {}) {
    const { activityShareMessageDTOS = [] } = payResult;

    const inSourcingfissionInfo = activityShareMessageDTOS[0] || {};
    const isDisplay = inSourcingfissionInfo.isDisplay || false;
    const desc = inSourcingfissionInfo.desc || '';
    const imgUrl = inSourcingfissionInfo.imgUrl || '';
    const query = args.getAll(inSourcingfissionInfo.activityUrl);

    this.setYZData({
      inSourcingfission: {
        isDisplay,
        desc,
        imgUrl,
        activityId: query.activityId,
        umpAlias: query.umpAlias,
        inviterVoucherAlias: query.inviterVoucherAlias,
      },
    });

    EventUtil.trigger('PayResultDataReady', payResult);
  },

  actionHandler({ detail: { type, loading = true, first = false } }) {
    // 社群团购订单不默认打开自提码弹窗
    const doNotAutoOpenSelfFetch = type === 'PICK_UP_CODE' && (!this.data.extensionConfig.autoPopupSelfFetchDialog || this.data.isGroupBuy);
    const doNotAutoOpenVoucher = type === 'CARD_VOUCHER'
      && !this.data.extensionConfig.autoPopupVirtualTicketDialog;
    if (first && (doNotAutoOpenSelfFetch || doNotAutoOpenVoucher)) return;
    const fn = this[ACTION_BUTTON_MAP[type]];
    fn && fn.call(this, loading);
  },

  groupbuyHandler() {
    this.$dispatch('GROUPBUY_BUY_AGAIN');
  },

  requestQuery() {
    const { orderNo: requestNo, phasePaymentStage: phase } = this.data;
    const query = { requestNo };
    if (phase) {
      query.phase = phase;
    }
    return query;
  },

  checkPay() {
    const query = this.requestQuery();

    this.setYZData({
      isRefreshing: true,
    });
    app
      .request({
        path: '/wsctrade/order/payresult/checkPay.json',
        query,
      })
      .then((data) => {
        if (data && data.allOrderPaid) {
          this.fetchPayResult();
        }
      })
      .catch()
      .then(() => {
        this.setYZData({
          isRefreshing: false,
        });
      });
  },

  // 展示优惠券信息
  handleCoupon(coupon = {}) {
    const { quantity = 0, customShareDesc, shareLinkImgUrl } = coupon;
    // 判断是否创建成功裂变券活动
    if (quantity > 0) {
      this.setYZData({
        coupon: {
          show: true,
          quantity,
          customShareDesc,
          shareLinkImgUrl,
        },
      });
    } else {
      this.fetchWaitBuyList();
    }
  },

  fetchWaitBuyList() {
    const waitBuyGoodsIds = app.storage.get('waitBuyGoodsIds');
    if (!waitBuyGoodsIds) {
      return;
    }

    getWaitBuyList({
      waitBuyGoodsIds,
    })
      .then(({ goodsList, expressType }) => {
        if (goodsList && goodsList.length) {
          this.setYZData({
            waitBuyDialog: {
              show: true,
              goodsList,
              expressType,
            },
          });
        }
      })
      .catch(() => {});
  },
  updateSolitaireShareData(data) {
    this.setYZData({ solitaireShareData: data.detail });
  },
  updateGroupbuyShareData(data) {
    this.setYZData({ groupbuyShareData: data.detail });
  },
  updateGroupbuyBuyAgainPath(data) {
    this.$commit('GROUPBUY_BUY_AGAIN_PATH', data.detail);
  },
  onWaitBuyDialogConfirm() {
    const { goodsList, expressType } = this.data.waitBuyDialog;

    const dbid = app.db.set({
      type: 'goods',
      goods_list: generateBuyGoodsList(goodsList),
      expressTypeChoice: expressType,
    });

    navigate.navigate({
      url: '/packages/order/index?orderFrom=cart&dbid=' + dbid,
    });
  },

  gotoDetail() {
    this.$dispatch('NAVIGATE_ORDER_DETAIL');
  },

  gotoGoodsDetail() {
    const url = get(this.data.payResult, 'shareMessage.goodsUrl');
    wx.redirectTo({
      url: `/pages/goods/detail/index?alias=${args.getAll(url).alias}`,
    });
  },

  gotoMemberCard() {
    const { memberCard } = this.data.payResult;
    const alias = memberCard.cardAlias || args.getAll(memberCard.url).card_alias;
    wx.redirectTo({
      url: `/packages/card/detail/index?alias=${alias}`,
    });
  },

  gotoPreWeapp() {
    wx.navigateBackMiniProgram({
      extraData: {
        order_no: this.data.orderNo,
      },
    });
  },

  gotoHomePage() {
    navigate.switchTab({
      url: '/packages/home/<USER>/index',
    });
  },

  // 打开自提码弹窗
  openPickUpCode(loading = true) {
    if (this.data.isGroupBuy) {
      this.openGroupbuyPickUpCodeDialog();
    } else {
      this.$dispatch('OPEN_PICK_UP_CODE', loading);
    }
  },

  closePickUpCode() {
    this.$dispatch('CLOSE_PICK_UP_CODE');
  },

  // 打开电子卡券弹窗
  openVoucherCard(loading = true) {
    this.$dispatch('OPEN_VOUCHER_CARD', loading);
  },

  closeVoucherCard() {
    this.$dispatch('CLOSE_VOUCHER_CARD');
  },

  openSolitaireDialog() {
    this.$commit('SET_SOLITAIRE_DIALOG', true);
  },

  closeSolitaireDialog() {
    this.$commit('SET_SOLITAIRE_DIALOG', false);
  },

  openGroupbuyPickUpCodeDialog() {
    this.$commit('TOGGLE_SHOW_GROUPBUY_PICKUP', true);
  },

  closeGroupbuyPickUpCodeDialog() {
    this.$commit('TOGGLE_SHOW_GROUPBUY_PICKUP', false);
  },

  openGroupbuyShareDialog() {
    this.$commit('TOGGLE_SHOW_GROUPBUY_SHARE', true);
  },

  closeGroupbuyShareDialog() {
    this.$commit('TOGGLE_SHOW_GROUPBUY_SHARE', false);
  },

  // 打开订阅物流弹窗
  openSubscription() {
    this.$commit('TOGGLE_SHOW_SUBSCRIPTION', true);
  },

  closeSubscription() {
    this.$commit('TOGGLE_SHOW_SUBSCRIPTION', false);
  },

  openShareSheet() {
    if (this.data.payResult.orderTags && this.data.payResult.orderTags.SOLITAIRE_BUY) {
      this.openSolitaireDialog();
    } else if (this.data.isGroupBuy) {
      this.openGroupbuyShareDialog();
    } else {
      this.$dispatch('OPEN_SHARE_SHEET');
    }
  },

  closeShareSheet() {
    this.$dispatch('CLOSE_SHARE_SHEET');
  },

  closeCouponPopup() {
    this.setYZData({
      'coupon.show': false,
    });
  },

  updateShareParam(event) {
    const { shareMessage = {} } = this.data.payResult || {};
    // 更新分享信息
    this.$commit('SET_PAY_RESULT', {
      shareMessage: { ...shareMessage, ...event.detail },
    });
  },

  fissionShare() {
    const { coupon, payResult } = this.data;
    const customShareDesc = get(coupon, 'customShareDesc', '');
    const fissionId = get(coupon, 'id', '');
    const shareLinkImgUrl = get(coupon, 'shareLinkImgUrl')
      || 'https://img.yzcdn.cn/public_files/2019/09/11/ac05a514d574f7ded4d70dff8f67c62c.png';
    let path = args.add('/packages/ump/fission/index', {
      shareback: 1,
      order_no: payResult.fissionCouponReceiveOrderNo,
      ...shareParams,
    });
    if (fissionId) {
      path = args.add(path, { fissionId });
    }

    return {
      title: customShareDesc,
      imageUrl: shareLinkImgUrl,
      path,
      loggerParams: {
        activity_names: ['fission'],
      },
    };
  },

  inSourcingCouponShare() {
    const { inSourcingfission = {} } = this.data;
    const {
      activityId,
      umpAlias,
      inviterVoucherAlias,
      desc,
      imgUrl,
    } = inSourcingfission;

    let path = 'packages/ump/in-sourcing-fission/index';
    path = args.add(path, {
      activityId,
      umpAlias,
      inviterVoucherAlias,
    });

    return {
      path,
      imageUrl: imgUrl,
      title: desc,
    };
  },

  solitaireShare() {
    const shareTitle = this.data.solitaireShareData.activityName;
    const shareImg = this.data.solitaireShareData.shareImg;
    const shareUrl = args.add(
      'https://h5.youzan.com/wscindustry/solitaire/buy',
      {
        kdtId: app.getKdtId(),
        sub_kdt_id: app.getKdtId(),
        alias: this.data.solitaireShareData.activityAlias,
        inviteUserId: app.getBuyerId(),
        sl: this.data.solitaireShareData.sls,
        shareFrom: 'tradePopup',
      }
    );
    const kdtId = app.getKdtId();
    const link = addSalesmanParams({
      url: buildUrl(shareUrl, 'h5', kdtId),
      kdtId,
      sl: this.data.solitaireShareData.sls,
    });
    return {
      title: shareTitle,
      path: `pages/common/webview-page/index?src=${encodeURIComponent(link)}`,
      imageUrl: shareImg,
    };
  },
  onShareAppMessage({ target }) {
    const { dataset } = target;
    const { type = '' } = dataset;

    if (type === 'fission') {
      // 裂变优惠券
      return this.fissionShare();
    }
    if (type === 'inSourcingCoupon') {
      // 内购券分享
      return this.inSourcingCouponShare();
    }
    if (this.data.showSolitaire) {
      // 社群接龙分享
      return this.solitaireShare();
    }

    if (this.data.isGroupBuy) {
      // 社群团购分享
      return this.data.groupbuyShareData;
    }

    const {
      title,
      desc,
      imgUrl: imageUrl,
      goodsUrl,
      shareUrl, // 分享url，如果粗在则直接使用shareUrl
    } = this.data.payResult.shareMessage;

    const { isNewHotelGood } = this.data;
    const { orderItems = [] } = this.data.payResult;

    let nameMaxlen = 0;
    let message = '';
    let subTitle = '';
    if (orderItems.length > 1) {
      nameMaxlen = 14;
      message = '等，必须推荐给你';
    } else {
      nameMaxlen = 15;
      message = '，必须推荐给你';
    }

    if (orderItems[0].title.length > nameMaxlen) {
      subTitle = orderItems[0].title.substr(0, nameMaxlen) + '...等' || '一些东西';
    } else {
      subTitle = orderItems[0].title || '一些东西';
    }

    let path = shareUrl
      || `/pages/goods/detail/index?alias=${args.getAll(goodsUrl).alias}`;

    if (isNewHotelGood) {
      const newGoodsUrl = goodsUrl.replace('&show_share_guide=1', '');
      path = `/pages/common/webview-page/index?src=${encodeURIComponent(
        newGoodsUrl
      )}`;
    }

    return {
      title: `我买了${subTitle}${message}` || `${title}，${desc}`,
      imageUrl,
      path,
    };
  },

  fetchSelfFetchMessage() {
    const query = this.requestQuery();
    let index = 0;

    const fetchSelfFetchMessage = () => {
      app
        .request({
          path: '/wsctrade/order/payresult/selfFetchMessage.json',
          query,
        })
        .then((data) => {
          if (data && data.selfFetchNo) {
            this.$commit('SET_PAY_RESULT', { selfFetch: data });
          } else {
            delayContinue();
          }
        })
        .catch(delayContinue);
    };

    fetchSelfFetchMessage();

    function delayContinue() {
      // 轮询接口，响应后每秒一次，最多10次
      if (++index < 10) {
        setTimeout(fetchSelfFetchMessage, 1000);
      }
    }
  },

  afterGetRecommends(event) {
    const {
      paidPromotion = {},
      shopCoupons = {},
      wxSubscribeScenesList,
    } = this.data.payResult;
    const hasActivity = paidPromotion
      && suportedActivity.indexOf(paidPromotion.promotionType) > -1;
    const hasCoupon = shopCoupons && shopCoupons.quantity > 0;
    const hasWxSubscribe = wxSubscribeScenesList
      && wxSubscribeScenesList.length > 0
      && wx.canIUse('requestSubscribeMessage');
    this.$commit('SET_PAY_RESULT', {
      simpleStyle:
        !hasActivity && !hasCoupon && !event.detail && !hasWxSubscribe,
    });
  },

  onOfficialAccountError(res) {
    console.log('onOfficialAccountError', res);
  },

  handleWxSubscribeSuccess() {
    this.setYZData({ hasSubscribe: true });
  },

  fetchPayResultShopAD() {
    return app
      .request({
        path: '/wsctrade/order/payresult/shopad.json',
      })
      .then((shopAd) => {
        this.setYZData({ shopAd });
        if (shopAd && shopAd.weappNavigateTo) {
          app.logger.log({
            et: 'view',
            ei: 'view_pay_result_ad',
            en: '支付成功页-广告曝光',
            si: app.getKdtId(),
          });
        }
      })
      .catch(() => {});
  },

  shopAdClick() {
    const shopAd = this.data.shopAd;
    if (shopAd && shopAd.weappNavigateTo) {
      const { appId, weappNavigateTo } = shopAd;
      app.logger.log({
        et: 'click',
        ei: 'click_pay_result_ad',
        en: '支付成功页-广告点击',
        si: app.getKdtId(),
      });
      wx.navigateToMiniProgram({
        appId,
        path: weappNavigateTo,
        envVersion: 'trial',
      });
    }
  },
});
