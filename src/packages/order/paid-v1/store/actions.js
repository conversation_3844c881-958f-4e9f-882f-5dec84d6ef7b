import getApp from 'shared/utils/get-safe-app';
import GOODS_TYPE from '@/constants/goods-type';
import designMod from '../design.json';
import EventUtil from '@youzan/weapp-utils/lib/event';
import { isBlindBox } from 'utils/judge-blind-box';

const app = getApp();

/**
 * Vuex actions
 * 接口异步请求、通用页面动作
 */
export default {
  // 显示提示
  TOAST(_, ...args) {
    let options = {};
    if (typeof args[0] === 'object') {
      options = args[0];
    } else {
      options = {
        title: args[0],
        icon: args[1],
        duration: args[2],
      };
    }
    wx.showToast({
      title: options.title == null ? '加载中' : options.title,
      icon: options.icon == null ? 'none' : options.icon,
      duration: options.duration == null ? 3000 : options.duration,
    });
  },

  // 隐藏提示
  HIDE_TOAST() {
    wx.hideToast();
  },

  LOADING({ dispatch }, payload) {
    dispatch('TOAST', {
      icon: 'loading',
      duration: 100000,
      ...payload,
    });
  },

  /**
   * 关闭电子卡券弹框
   */
  CLOSE_VOUCHER_CARD({ commit }) {
    commit('TOGGLE_SHOW_VOUCHER_CARD', false);
  },

  /**
   * 打开电子卡券弹框
   */
  OPEN_VOUCHER_CARD({ commit, dispatch }, loading) {
    dispatch('FETCH_TICKET_DETAIL', loading)
      .then(() => {
        commit('TOGGLE_SHOW_VOUCHER_CARD', true);
      })
      .catch((msg) => {
        loading && wx.hideLoading();
        wx.showToast({ title: msg || '获取电子卡券信息失败', icon: 'none' });
      });
  },

  /**
   * 获取电子卡券信息
   */
  FETCH_TICKET_DETAIL({ state, commit }, loading) {
    return new Promise((resolve, reject) => {
      const { voucherCard } = state;
      if (voucherCard && voucherCard.qrcode) {
        resolve(voucherCard);
      } else {
        loading && wx.showLoading();
        app
          .request({
            method: 'GET',
            path: '/wsctrade/order/payresult/ticket-detail.json',
            query: {
              orderNo: state.orderNo,
              source: 'weapp',
            },
          })
          .then((res) => {
            loading && wx.hideLoading();
            if (res && res.qrCode) {
              const { orderItems = [] } = state.payResult;
              const defaultImage =
                'https://img01.yzcdn.cn/public_files/2019/08/06/3f099aa967abd3821d610344d7ba3729.jpg';
              const items = orderItems.map((item) => {
                return {
                  title: item.title, // 商品标题
                  num: item.num, // 商品数量
                  sku:
                    item.skuItems
                      .map((sku) => `${sku.k}：${sku.v}`)
                      .join('；') || '默认规格', // sku信息, 如黄色：250;
                  imgUrl: item.imgUrl || defaultImage, // 商品图片地址
                  validity:
                    res.validity.indexOf(' - ') > -1
                      ? `有效期至${res.validity.split(' - ')[1]}`
                      : res.validity,
                };
              });

              const voucherCard = {
                items,
                cardnums:
                  res.tickets instanceof Array
                    ? res.tickets.map((item) => item.ticketCode)
                    : [],
                barcode: res.barCode,
                qrcode: res.qrCode,
              };
              commit('SET_VOUCHER_CARD', voucherCard);
              resolve(voucherCard);
            } else {
              reject('');
            }
          })
          .catch(({ msg } = {}) => {
            reject(msg);
          });
      }
    });
  },

  // 打开自提码弹窗
  OPEN_PICK_UP_CODE({ state, commit }, loading = true) {
    const { selfFetch, orderNo, phasePaymentStage, payResult } = state;
    if (selfFetch && selfFetch.selfFetchQRCode) {
      commit('TOGGLE_SHOW_SELF_FETCH', true);
    } else {
      let index = 0;
      const requestErrorHandler = () => {
        // 轮询接口，响应后每秒一次，最多3次
        if (++index < 3) {
          setTimeout(doRequest, 1500);
        } else if (loading) {
          commit('TOGGLE_SHOW_SELF_FETCH', true);
          commit('SET_SELF_FETCH', { hasError: true, loading: false });
        }
      };
      const doRequest = () => {
        if (loading) {
          commit('TOGGLE_SHOW_SELF_FETCH', true);
          commit('SET_SELF_FETCH', { loading: true, hasError: false });
        }
        app
          .request({
            method: 'GET',
            path: '/wsctrade/order/payresult/selfFetchMessage.json',
            query: {
              requestNo: orderNo,
              phase: phasePaymentStage,
            },
          })
          .then((res) => {
            if (res && res.selfFetchQRCode) {
              const { orderItems = [] } = payResult;
              const defaultImage =
                'https://img01.yzcdn.cn/public_files/2019/08/06/3f099aa967abd3821d610344d7ba3729.jpg';
              const items = orderItems.map((item) => {
                return {
                  title: item.title, // 商品标题
                  num: item.num, // 商品数量
                  sku:
                    item.skuItems
                      .map((sku) => `${sku.k}：${sku.v}`)
                      .join('；') || '默认规格', // sku信息, 如黄色：250;
                  imgUrl: item.imgUrl || defaultImage, // 商品图片地址
                };
              });
              commit('TOGGLE_SHOW_SELF_FETCH', true);
              commit('SET_SELF_FETCH', {
                ...res,
                hasError: false,
                loading: false,
                items,
              });
            } else {
              requestErrorHandler();
            }
          })
          .catch(requestErrorHandler);
      };
      doRequest();
    }
  },

  CLOSE_PICK_UP_CODE({ commit }) {
    commit('TOGGLE_SHOW_SELF_FETCH', false);
  },

  /**
   * 跳转订单详情
   */
  NAVIGATE_ORDER_DETAIL({ state }) {
    const { isVOrder, isGroupBuy, activityType, buttonGroup } = state.payResult;
    const { orderNo } = state;
    const { goodsType, goodsId } = state.firstGoods;

    let url = `/packages/trade/order/result/index?orderNo=${orderNo}`;

    if (goodsType == GOODS_TYPE.MRMBER_CARD) {
      url = `/packages/card/detail/index?goods_id=${goodsId}&orderNo=${orderNo}`;
    } else if (isVOrder) {
      // 至少2件商品 && 至少有一件为分销商品
      url = '/packages/trade/order/list/index';
    } else if (goodsType == 206) {
      // 206为买单订单
      url = `/packages/trade/order/unicashier-result/index?order_no=${orderNo}`;
    } else if (isGroupBuy) {
      url = `/packages/groupbuying/buyer-trade/detail/index?orderNo=${orderNo}&isPay=true`;
    } else if (isBlindBox(activityType)) {
      const h5Url = buttonGroup.DETAIL.url || '';
      url = `/pages/common/webview-page/index?src=${encodeURIComponent(h5Url)}`;
    }
    wx.redirectTo({ url });
  },

  OPEN_SHARE_SHEET({ commit }) {
    app.logger.log({
      et: 'click',
      ei: 'share_order',
      en: '点击分享一下',
      si: app.getKdtId(),
    });
    commit('TOGGLE_SHOW_SHARE', true);
  },

  CLOSE_SHARE_SHEET({ commit }) {
    commit('TOGGLE_SHOW_SHARE', false);
  },

  GET_DESIGN() {
    const modList = designMod.design || [];
    const config = modList[0] && modList[0].type === 'config' && modList[0];
    return Promise.resolve({
      config,
      design: config ? modList.slice(1) : modList,
    });
  },

  TO_BE_SALESMAN({ commit, state: { orderNo } }) {
    const kdtId = app.getKdtId();
    app
      .request({
        method: 'POST',
        path: '/wscsalesman/tutorial/auto-join/afterPayAutoJoin.json',
        data: {
          orderNo,
        },
      })
      .then((data) => {
        if (data && data.content) {
          data = {
            ...data,
            url: `/packages/salesman/salesman-center/index?kdtId=${kdtId}`,
          };
          commit('SET_BE_SALESMAN_INFO', data);
          app.logger.log({
            et: 'view',
            ei: 'show_salesman',
            en: '成为销售员入口曝光',
            pt: 'paySuccess',
            si: app.getKdtId(),
          });
        }
      });
  },

  // 查看支付结果数据是否好了
  CHECK_PAY_RESULT_READY({ state }) {
    return new Promise((resolve) => {
      if (
        state.payResult &&
        Object.keys(state.payResult).length &&
        state.isAllPaid
      ) {
        resolve(state.payResult);
      } else {
        EventUtil.on('PayResultDataReady', (payResult) => {
          resolve(payResult);
        });
      }
    });
  },

  FETCH_PAY_RESULT(store, query) {
    return new Promise((resolve, reject) => {
      app
        .request({
          path: '/wsctrade/order/payresult.json',
          query: { ...query, source: 'weapp' },
          config: {
            skipShopInfo: true,
            skipKdtId: true,
          },
        })
        .then(resolve)
        .catch((e) => {
          reject(e);
        });
    });
  },

  GROUPBUY_BUY_AGAIN({ state }) {
    wx.redirectTo({
      url: state.groupbuyBuyAgainPath,
    });
  },
};
