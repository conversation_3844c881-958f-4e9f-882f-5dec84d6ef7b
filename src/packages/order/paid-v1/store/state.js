export const state = {
  design: [],
  extensionConfig: {}, // 业务配置，可能从云端获取配置
  orderNo: '',
  phasePaymentStage: '',
  fetching: true,
  isAllPaid: false,
  payResult: {},
  beSalesmanInfo: {}, // 订单完成后 是否成为销售员
  showSubscription: false, // 是否展示订阅物流弹窗
  showVoucherCard: false, // 是否显示电子卡券海报
  voucherCard: {}, // 电子卡券信息
  showSelfFetch: false, // 是否显示自提订单海报
  selfFetch: {}, // 自提信息
  firstGoods: {},
  showShare: false, // 是否展示分享
  showSolitaire: false, // 是否显示社群接龙弹窗
  showGroupbuySelfFetch: false, // 是否显示社群团购自提码弹窗
  showGroupbuyShare: false, // 是否显示社群团购分享弹窗
  componentSubscribe: {
    // 是否显示订阅通知组件
    display: true,
  },
  hidePickUpCodeBtn: false, // 是否显示和隐藏查看提货码按钮
  groupbuyBuyAgainPath: '/packages/groupbuying/buyer-trade/buying/index', // 社区团购继续购买path
};
