/**
 * Vuex mutations
 */
export default {
  SET_ORDER_NO(state, payload) {
    state.orderNo = payload;
  },

  SET_PAYMENT_STAGE(state, payload) {
    state.phasePaymentStage = payload;
  },

  SET_FETCHING(state, payload) {
    state.fetching = payload;
  },

  SET_VOUCHER_CARD(state, payload) {
    state.voucherCard = {
      ...payload,
    };
  },

  TOGGLE_SHOW_SUBSCRIPTION(state, payload) {
    state.showSubscription = payload;
  },

  TOGGLE_SHOW_VOUCHER_CARD(state, payload) {
    state.showVoucherCard = payload;
  },

  TOGGLE_SHOW_SELF_FETCH(state, payload) {
    state.showSelfFetch = payload;
  },

  SET_SOLITAIRE_DIALOG(state, payload) {
    state.showSolitaire = payload;
  },

  TOGGLE_SHOW_GROUPBUY_PICKUP(state, payload) {
    state.showGroupbuySelfFetch = payload;
  },

  TOGGLE_SHOW_GROUPBUY_SHARE(state, payload) {
    state.showGroupbuyShare = payload;
  },

  SET_SELF_FETCH(state, payload) {
    state.selfFetch = {
      ...state.selfFetch,
      ...payload,
    };
  },

  SET_PAY_RESULT(state, payload) {
    state.payResult = {
      ...state.payResult,
      ...payload,
    };

    if (payload.payState) {
      state.isAllPaid = payload.payState === 'ALL_PAID';
    }
  },

  SET_BE_SALESMAN_INFO(state, payload) {
    state.beSalesmanInfo = payload;
  },

  SET_FIRST_GOODS(state, payload) {
    state.firstGoods = {
      ...payload,
    };
  },

  TOGGLE_SHOW_SHARE(state, payload) {
    state.showShare = payload;
  },

  SET_DESIGN(state, payload) {
    state.design = payload;
  },

  SET_EXTENSION_CONFIG(state, payload) {
    state.extensionConfig = {
      ...payload,
    };
  },

  SET_CLOUD_COMP_NOTICE(state, display) {
    state.componentSubscribe.display = display;
  },

  SHOW_HIDE_PICK_UP_CODE(state, payload) {
    state.hidePickUpCodeBtn = payload;
  },

  GROUPBUY_BUY_AGAIN_PATH(state, payload) {
    state.groupbuyBuyAgainPath = payload;
  },
};
