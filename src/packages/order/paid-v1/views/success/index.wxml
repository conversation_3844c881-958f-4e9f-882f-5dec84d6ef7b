<import src="../../custom-tpl.wxml" />

<template name="success">
  <block wx:for="{{ design }}" wx:key="type">
    <!-- 支付金额信息 -->
    <msg
      wx:if="{{ item.type === 'msg-block' }}"
      payResult="{{ payResult }}"
    />

    <!-- 支付奖励信息 -->
    <cell
      wx:elif="{{ item.type === 'award-block' }}"
      payResult="{{ payResult }}"
      beSalesmanInfo="{{ beSalesmanInfo }}"
      bind:member-card-click="gotoMemberCard"
    />

    <!-- 按钮类 -->
    <block wx:elif="{{ item.type === 'action-block' }}">
      <action
        payResult="{{ payResult }}"
        bind:click="actionHandler"
        bind:groupbuyContinue="groupbuyHandler"
        hidePickUpCodeBtn= "{{hidePickUpCodeBtn}}"
      />

      <official-account
        wx:if="{{ showOfficalAccount }}"
        binderror="onOfficialAccountError"
      />
    </block>

    <!-- 裂变优惠券 -->
    <block wx:elif="{{ item.type === 'coupon-block' }}">

      <!-- 活动banner -->
      <flow-entrance-banner biz-name="pay_success" />

      <!-- 【流量交换活动】在目标店铺的支付成功页展示图文链接 -->
      <view wx:if="{{ shopAd && shopAd.imageUrl }}" class="exchange-activity" bindtap="shopAdClick">
        <image src="{{ shopAd.imageUrl }}" mode="widthFix" />
      </view>

      <!-- 内购券 -->
      <in-sourcing-coupon
        wx:if="{{ inSourcingfission.isDisplay }}"
        weappImg="{{ weappImg }}"
      />

      <!-- 裂变优惠券 -->
      <fission-coupon
        wx:if="{{ coupon.show }}"
        coupon="{{ coupon }}"
        bind:open="fissionShare"
      />
    </block>

    <!-- 支付有礼 -->
    <block wx:elif="{{ item.type === 'activity-block' }}">
      <activity
        wx:if="{{ payResult.paidPromotion && payResult.paidPromotion.detailUrl }}"
        payResult="{{ payResult }}"
      />
    </block>

    <!-- 三方自定义组件 -->
    <block wx:elif="{{ item.custom }}">
      <template is="{{ item.type }}" />
    </block>
  </block>
</template>
