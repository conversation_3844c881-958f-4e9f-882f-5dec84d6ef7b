<import src="./views/success/index.wxml" />
<import src="./views/wait/index.wxml" />
<import src="./views/auction/index.wxml" />
<import src="./views/error/index.wxml" />
<import src="./views/oversale-error/index.wxml" />

<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
  page-bg-color="{{ payResult.simpleStyle ? '#fff' : '#f9f9f9' }}"
  copyright-bg-color="{{ payResult.simpleStyle ? '#fff' : '#f9f9f9' }}"
  forbid-copyright="{{ payResult.simpleStyle }}"
>
  <!-- 骨架图 -->
  <image
    wx:if="{{ fetching }}"
    class="skeleton-image"
    mode="scaleToFill"
    src="https://img.yzcdn.cn/public_files/2020/10/12/f3ec90527138510188ff8eee64ab0840.png"
  />
  <view wx:if="{{ !fetching }}" class="paid" style="min-height: {{ payResult.simpleStyle ? ((windowHeight - 87) + 'px') : 'auto' }}">
    <template
      wx:if="{{ payResult.pageTemplate && design.length }}"
      is="{{ payResult.pageTemplate }}"
      data="{{ payResult, design, showOfficalAccount, auctionBuyer: payResult.auctionBuyer, isRefreshing, coupon, showSubscription, showVoucherCard, showSelfFetch, subscription, selfFetch, voucherCard, inSourcingfission, weappImg, shopAd, hidePickUpCodeBtn }}"
    />

    <!-- 微信订阅消息 -->
    <subscription-msg
      wx:if="{{ showWxSubscribe && componentSubscribe.display }}"
      template-ids="{{ payResult.wxSubscribeScenesList }}"
      has-subscribe="{{ hasSubscribe }}"
      bind:success="handleWxSubscribeSuccess"
    >
      <view class="wx-subscription">
        <image src="https://b.yzcdn.cn/pay-result/images/weixin2.png" alt="wechat" />
        <view class="wx-subscription__text">
          <view class="wx-subscription__text-main">订阅微信通知</view>
          <view class="wx-subscription__text-des">及时获取订单进度提醒</view>
        </view>
        <view class="wx-subscription__btn" style="color: {{ themeGeneral }}">
          {{ hasSubscribe ? '已订阅' : '立即订阅' }}
        </view>
      </view>
    </subscription-msg>

    <cps-goods-recommend wx:if="{{ showCpsRecommendBlock }}" cpsConfigKey="cps_goods_recommend_pay_success" />

    <recommend-goods
      wx:if="{{ showRecommendBlock }}"
      biz-name="pay_success"
      bindafterload="afterGetRecommends"
    />
  </view>

  <!-- 简版支付结果页自定义copyright -->
  <copyright
    wx:if="{{ payResult.simpleStyle }}"
    page-bg-color="#ffffff"
  />
</page-container>

<wait-buy-dialog
  show="{{ waitBuyDialog.show }}"
  goods-list="{{ waitBuyDialog.goodsList }}"
  bind:confirm="onWaitBuyDialogConfirm"
/>
<!-- 社群接龙弹窗-->
<solitaire-dialog
  orderNo="{{ orderNo }}"
  wx:if="{{ showSolitaire }}"
  visable="{{ showSolitaire }}"
  bind:close="closeSolitaireDialog"
  bind:setShareData="updateSolitaireShareData"
/>

<!-- 社区团购分享弹窗-->
<groupbuy-share-dialog
  orderNo="{{ orderNo }}"
  wx:if="{{ isGroupBuy }}"
  visible="{{ showGroupbuyShare }}"
  bind:close="closeGroupbuyShareDialog"
  bind:setShareData="updateGroupbuyShareData"
  bind:setBuyAgainPath="updateGroupbuyBuyAgainPath"
/>

<!-- 社区团购提货码弹窗-->
<groupbuy-self-fetch-dialog
  orderNo="{{ orderNo }}"
  wx:if="{{ isGroupBuy }}"
  visible="{{ showGroupbuySelfFetch }}"
  bind:close="closeGroupbuyPickUpCodeDialog"
/>

<!-- 订阅物流弹窗 -->
<subscription-dialog
  visable="{{ showSubscription }}"
  bind:close="closeSubscription"
/>

<!-- 查看提货码弹窗 -->
<self-fetch-dialog
  visable="{{ showSelfFetch }}"
  data="{{ selfFetch }}"
  bind:close="closePickUpCode"
/>

<!-- 电子卡券弹窗 -->
<voucher-card-dialog
  visable="{{ showVoucherCard }}"
  data="{{ voucherCard }}"
  bind:close="closeVoucherCard"
/>

<!-- 订单分享 -->
<trade-share
  items="{{ shareItems }}"
  visable="{{ showShare }}"
  orderNo="{{ orderNo }}"
  bind:updateShareParam="updateShareParam"
  bind:close="closeShareSheet"
/>
