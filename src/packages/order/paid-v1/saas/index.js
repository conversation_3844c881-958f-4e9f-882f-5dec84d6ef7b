import { getYunSdk } from '@youzan/weapp-ecloud-sdk';
import { openState } from './state';
import { initUpdateComponent, updateComponent } from './update-component';
import {
  handleOrderDetail,
  handleSubscription,
  handleSelfFetchCode,
  handleVirtualTicket,
  handleOpenShare,
  hidePickUpCodeBtn,
  handleGroupBuyingBuyAgain,
} from './process';
import { beforeGoToUrlAsync } from './event';

export default {
  onLoad() {
    const sdk = getYunSdk();
    this.__yunPage = sdk.page;

    sdk.setPageData(this, openState);

    sdk.setPageEvent('beforeGoToUrl', beforeGoToUrlAsync);

    sdk.setPageProcess('handleOrderDetail', handleOrderDetail);
    sdk.setPageProcess('handleSubscription', handleSubscription);
    sdk.setPageProcess('handleSelfFetchCode', handleSelfFetchCode);
    sdk.setPageProcess('handleVirtualTicket', handleVirtualTicket);
    sdk.setPageProcess('handleOpenShare', handleOpenShare);
    sdk.setPageProcess('hidePickUpCodeBtn', hidePickUpCodeBtn);
    sdk.setPageProcess('handleGroupBuyingBuyAgain', handleGroupBuyingBuyAgain);

    this.on('beforeGoToUrl', (args) => {
      return beforeGoToUrlAsync.trigger(args);
    });

    initUpdateComponent(this.$store);
    sdk.page.updateComponent = updateComponent.bind(this);
  },

  onUnload() {
    this.__yunPage && this.__yunPage.unload();
  },
};
