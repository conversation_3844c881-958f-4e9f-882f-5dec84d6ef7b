let commit;

const CAN_UPDATE_COMPONENTS_MAP = {
  SubscribeNotice: 'SET_CLOUD_COMP_NOTICE',
};

export function initUpdateComponent({ commit: _commit }) {
  commit = _commit;
}

/**
 * 更新组件默认行为
 * @param {*} compName 组件名称
 * @param {*} display 是否隐藏组件
 */
export function updateComponent(compName, display) {
  if (CAN_UPDATE_COMPONENTS_MAP[compName]) {
    commit(CAN_UPDATE_COMPONENTS_MAP[compName], display);
  }
}
