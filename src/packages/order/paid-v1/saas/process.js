import { theStore } from '../store';

/**
 * 处理"查看详情"
 */
export function handleOrderDetail() {
  theStore.dispatch('NAVIGATE_ORDER_DETAIL');
}

/**
 * 处理"订阅物流"
 */
export function handleSubscription() {
  theStore.commit('TOGGLE_SHOW_SUBSCRIPTION', true);
}

/**
 * 处理“查看提货码”
 */
export function handleSelfFetchCode() {
  theStore.dispatch('CHECK_PAY_RESULT_READY').then(() => {
    return theStore.dispatch('OPEN_PICK_UP_CODE', true);
  });
}

/**
 * 处理“查看电子卡券凭证”
 */
export function handleVirtualTicket() {
  theStore.dispatch('CHECK_PAY_RESULT_READY').then(() => {
    return theStore.dispatch('OPEN_VOUCHER_CARD', true);
  });
}

/**
 * 处理“分享一下”
 */
export function handleOpenShare() {
  theStore.dispatch('OPEN_SHARE_SHEET');
}

/**
 * 处理“社区团购再次购买”
 */
export function handleGroupBuyingBuyAgain() {
  theStore.dispatch('GROUPBUY_BUY_AGAIN');
}

/**
 * 显示或隐藏“查看提货码按钮”
 */
export function hidePickUpCodeBtn(display = false) {
  theStore.commit('SHOW_HIDE_PICK_UP_CODE', display);
}
