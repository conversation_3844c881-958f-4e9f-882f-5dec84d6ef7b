.paid__tips {
  color: #999;
  margin: 15px 0;
  padding: 0 15px;
  font-size: 12px;
  line-height: 16px;
}

.container {
  min-height: auto !important;
}

.paid {
  display: flex;
  flex-flow: column nowrap;
}

.exchange-activity {
  display: flex;
  margin-top: 12px;
  padding: 0 12px;

  & > image {
    width: 100%;
    border-radius: 4px;
  }
}

// 微信订阅弹窗样式
.wx-subscription {
  width: 93.6%;
  height: 64px;
  margin: 0 auto;
  padding: 9px 12px 9px 7px;
  box-sizing: border-box;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  margin-top: 12px;
  border-radius: 4px;

  & > image {
    width: 52px;
    height: 40px;
  }

  &__text {
    flex: 1;
    margin-left: 8px;

    &-main {
      font-size: 14px;
      color: #323233;
      font-weight: 500;
      line-height: 20px;
    }

    &-des {
      font-size: 12px;
      color: #7d7e80;
      font-weight: 400;
      line-height: 16px;
      margin-top: 4px;
    }
  }

  &__btn {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
  }
}

.skeleton-image {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
