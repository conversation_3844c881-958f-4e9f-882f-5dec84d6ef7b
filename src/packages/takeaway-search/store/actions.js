import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { formatSkuTree } from 'shared/common/components/base-sku/common/sku-format-tree';

const app = getApp();
const http = app.request;
const NONE_SKU_SUFFIX = 'empty';
const SHOP_TYPE = {
  WSC: 0,
  RETAIL: 7,
};
const shopType = app.globalData.isRetailApp ? SHOP_TYPE.RETAIL : SHOP_TYPE.WSC;

export default {
  // 显示提示
  showToast(...args) {
    let options = {};
    if (args.length === 1 && typeof args[0] === 'object') {
      options = args[0];
    } else {
      options = {
        title: args[0],
        icon: args[1],
        duration: args[2],
      };
    }

    wx.showToast({
      title: options.title == null ? '加载中' : options.title,
      icon: options.icon == null ? 'none' : options.icon,
      duration: options.duration == null ? 3000 : options.duration,
    });
  },

  // 隐藏提示
  hideToast() {
    wx.hideToast();
  },

  hideLoading() {
    wx.hideToast();
  },

  showLoading(payload) {
    this.showToast({
      icon: 'loading',
      duration: 100000,
      ...payload,
    });
  },

  fetchGoodsList(keyword, reset) {
    this.showLoading();
    const { tagIds, goodsList } = this.data;
    const { page, pageSize } = this;
    http({
      path: '/wscshop/showcase/takeaway/search.json',
      method: 'POST',
      data: {
        page,
        pageSize,
        keyword,
        tagIds,
        shopType,
      },
    })
      .then((list) => {
        const currentGoodsList = list.map((item) => {
          const { goods, marketing, spu, processProcedure } = item;
          if (!goods.imageUrl) {
            goods.imageUrl = '/upload_files/no_pic.png';
          }
          // 商品列表图片
          goods.picture = goods.picUrl = cdnImage(
            goods.imageUrl,
            '!300x300.jpg'
          );
          // 商品预览大图
          goods.bigPicUrl = cdnImage(goods.imageUrl, '!750x0.jpg');

          // 如果有活动spu就是用活spu，没有就使用商品级spu
          if (
            marketing &&
            marketing.activities[0] &&
            marketing.activities[0].spu
          ) {
            goods.sku = formatSkuTree(marketing.activities[0].spu);
            // 限时折扣或者会员折扣
            goods.activity = marketing.activities[0];
          } else {
            goods.sku = formatSkuTree(spu);
          }
          goods.price = goods.sku.minPrice;
          goods.processProcedure = processProcedure;
          return item;
        });
        let payloadList = [];
        if (reset) {
          payloadList = currentGoodsList;
          this.UPDATE_FINISHED(false);
        } else {
          payloadList = goodsList.concat(currentGoodsList);
        }
        if (currentGoodsList.length < 10) {
          this.UPDATE_FINISHED(true);
        }
        this.UPDATE_GOODS_LIST({ goodsList: payloadList });
        this.hideLoading();
      })
      .catch((error) => {
        this.showToast(error.msg);
      });
  },

  updateCurrentActiveGoods(params) {
    const { goods, showPreview } = params;
    this.UPDATE_CURRENT_ACTIVE_GOODS({ goods });
    // 点击input-number不显示商品详情
    if (showPreview) this.UPDATE_PREVIEW_GOODS(true);
  },

  updateSkuData(goods) {
    const { alias, num } = goods;
    const { currentActiveGoods } = this.data;

    const { sku } = currentActiveGoods;
    const noShowSku = sku.noneSku && (!sku.messages || !sku.messages.length);

    // 更新商品sku
    currentActiveGoods.noneSku = sku.noneSku;
    currentActiveGoods.stockNum = sku.stockNum;

    // 无sku且不需要留言，直接更新购物车
    if (noShowSku) {
      // 单sku设置skuId
      currentActiveGoods.num = num;
      currentActiveGoods.skuId = NONE_SKU_SUFFIX;
      this.UPDATE_CURRENT_ACTIVE_GOODS({ goods: currentActiveGoods });

      this.UPDATE_GOODS_TO_CART({
        alias,
        skuId: NONE_SKU_SUFFIX,
        num,
      });
    } else {
      this.UPDATE_CURRENT_ACTIVE_GOODS({ goods: currentActiveGoods });
      this.UPDATE_SHOW_SKU_STATUS(true);
    }

    // 更新选择商品 多sku在组件回调处更新
    if (noShowSku) this.UPDATE_CHOOSED_GOODS({ alias });
  },

  multiSkuGoodsToCart(params) {
    const { skuId, num } = params;
    let { currentActiveGoods } = this.data;
    const alias = currentActiveGoods.alias;

    currentActiveGoods = { ...currentActiveGoods, ...params };

    this.UPDATE_CURRENT_ACTIVE_GOODS({ goods: currentActiveGoods });
    this.UPDATE_GOODS_TO_CART({
      alias,
      skuId,
      num,
      hasSku: true,
    });
    this.UPDATE_CHOOSED_GOODS({ alias, skuId });
    this.UPDATE_SHOW_SKU_STATUS(false);
  },
};
