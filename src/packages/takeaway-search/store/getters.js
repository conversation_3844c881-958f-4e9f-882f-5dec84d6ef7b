export default {
  // 当前操作的商品sku
  currentActiveSkuGetter() {
    const { currentActiveGoods } = this.data;
    let sku = {};

    if (currentActiveGoods.goods) {
      sku = currentActiveGoods.goods.sku || {};
    }

    this.setYZData({
      currentActiveSku: sku
    });
  },

  // 添加到购物车的商品数量
  allChoosedNumGetter() {
    const { cartGoods } = this.data;
    const num = cartGoods.reduce((pre, goods) => {
      return pre + goods.num;
    }, 0);
    this.setYZData({
      allChoosedNum: num
    });
  },

  // 添加到购物车的商品总价
  allChoosedPriceGetter() {
    const { cartGoods } = this.data;
    const price = cartGoods.reduce((pre, goods) => {
      return pre + (goods.price * goods.num);
    }, 0);
    this.setYZData({
      allChoosedPrice: price
    });
  }
};
