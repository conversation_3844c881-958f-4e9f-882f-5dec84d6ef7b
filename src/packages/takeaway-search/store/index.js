import actions from './actions';
import mutations from './mutations';
import getters from './getters';

const state = {
  // 搜索商品列表
  goodsList: [],

  // 搜索标志
  searched: false,
  finished: false,

  // 起送费
  startfee: '',

  // 商品分组ids
  tagIds: [],

  // 当前操作的商品
  currentActiveGoods: {},

  // 购物车商品 skuId纬度区分商品
  cartGoods: [],

  // 搜索页新加入的商品
  newCartGoods: [],

  // 选择的商品 alias维度区分商品
  choosedGoods: {},

  // 显示商品详情弹窗
  showGoodsPreview: false,

  // 显示购物车抽屉
  showCart: false,

  // 显示sku
  showTakeAwaySku: false,

  // 当前操作的商品sku
  currentActiveSku: {},

  // 添加到购物车的商品数量
  allChoosedNum: 0,

  // 添加到购物车的商品总价
  allChoosedPrice: 0
};

export {
  state,
  actions,
  mutations,
  getters
};
