export default {
  INIT_STARTFEE(payload) {
    this.setYZData({
      startfee: payload.startfee
    });
  },

  INIT_TAGLIST_IDS(payload) {
    this.setYZData({
      tagIds: payload.ids
    });
  },

  MERGE_GOODS_TO_CART(payload) {
    this.setYZData({
      cartGoods: payload.goodsList
    });

    this.allChoosedNumGetter();
    this.allChoosedPriceGetter();
  },

  UPDATE_FINISHED(payload) {
    this.setYZData({
      finished: payload
    });
  },

  UPDATE_GOODS_LIST(payload) {
    this.setYZData({
      goodsList: payload.goodsList,
      searched: true
    });
  },

  UPDATE_CURRENT_ACTIVE_GOODS(payload) {
    this.setYZData({
      currentActiveGoods: payload.goods
    });
    this.currentActiveSkuGetter();
  },

  UPDATE_PREVIEW_GOODS(payload) {
    this.setYZData({
      showGoodsPreview: payload
    });
  },

  UPDATE_GOODS_TO_CART(payload) {
    const {
      cartGoods, newCartGoods, currentActiveGoods
    } = this.data;
    const {
      skuId, alias, num, hasSku
    } = payload;

    const _cartGoods = JSON.parse(JSON.stringify(cartGoods));
    const _newCartGoods = JSON.parse(JSON.stringify(newCartGoods));

    // 购物车已有此商品
    const findIndex = _cartGoods.findIndex(goods => (goods.skuId === skuId && goods.alias === alias));
    if (findIndex !== -1) {
      // 多sku累加
      if (hasSku) {
        _cartGoods[findIndex].num += num;
        // 判断当前规格的商品数量是否已经大于库存
        let currentSkuStockNum;
        const skuList = _cartGoods[findIndex].sku.list;
        for (let i = 0; i < skuList.length; i++) {
          if (skuId === skuList[i].id) {
            currentSkuStockNum = skuList[i].stockNum;
          }
        }
        if (_cartGoods[findIndex].num > currentSkuStockNum) {
          return this.showToast('数量超出范围');
        }
      } else {
        _cartGoods[findIndex].num = num;
      }
    } else {
      // 新加商品
      _cartGoods.push(JSON.parse(JSON.stringify(currentActiveGoods)));
    }

    // 搜索页新加入的商品
    const findIndexNewCart = _newCartGoods.findIndex(goods => (goods.skuId === skuId && goods.alias === alias));
    if (findIndexNewCart !== -1) {
      // 多sku累加
      if (hasSku) {
        _newCartGoods[findIndexNewCart].num += num;
      } else {
        _newCartGoods[findIndexNewCart].num = num;
      }
    } else {
      _newCartGoods.push(JSON.parse(JSON.stringify(currentActiveGoods)));
    }

    this.setYZData({
      cartGoods: _cartGoods,
      newCartGoods: _newCartGoods
    });
  },

  UPDATE_CHOOSED_GOODS(payload) {
    const { alias, skuId, isCart } = payload;
    const { currentActiveGoods, cartGoods } = this.data;
    let aliasChoosedGoods = this.data.choosedGoods[alias];

    // 购物篮修改商品数量
    if (isCart) {
      aliasChoosedGoods.num = cartGoods.reduce((pre, goods) => {
        if (goods.alias === alias) {
          return pre + goods.num;
        }
        return pre;
      }, 0);
    } else if (aliasChoosedGoods && !aliasChoosedGoods.noneSku) {
      const skuGoods = cartGoods.find(goods => (goods.skuId === skuId && goods.alias === alias));
      // 判断当前规格的商品数量是否已经大于库存
      let currentSkuStockNum;
      const skuList = skuGoods.sku.list;
      for (let i = 0; i < skuList.length; i++) {
        if (skuId === skuList[i].id) {
          currentSkuStockNum = skuList[i].stockNum;
        }
      }
      if (skuGoods.num === currentSkuStockNum) return;
      // 多sku数量需要累加
      aliasChoosedGoods = Object.assign({}, currentActiveGoods, {
        num: aliasChoosedGoods.num + currentActiveGoods.num
      });
    } else {
      aliasChoosedGoods = currentActiveGoods;
    }

    this.setYZData({
      choosedGoods: Object.assign({}, this.data.choosedGoods, {
        [alias]: JSON.parse(JSON.stringify(aliasChoosedGoods))
      })
    });

    this.allChoosedNumGetter();
    this.allChoosedPriceGetter();
  },

  UPDATE_SHOW_SKU_STATUS(payload) {
    this.setYZData({
      showTakeAwaySku: payload
    });
  },

  UPDATE_SHOW_CART_STATUS(payload) {
    this.setYZData({
      showCart: payload
    });
  },

  CLEAR_CART_GOODS() {
    const { choosedGoods } = this.data;
    // 将所有选择的商品数量设为0
    Object.keys(choosedGoods).forEach(alias => {
      choosedGoods[alias].num = 0;
    });

    this.setYZData({
      cartGoods: [],
      showCart: false,
      choosedGoods
    });

    this.allChoosedNumGetter();
    this.allChoosedPriceGetter();
  },
};
