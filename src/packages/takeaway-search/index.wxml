<view class="takeaway-search">
  <van-search
    show-action
    placeholder="搜索商品"
    bind:search="takeAwayThemeSearchHandler"
  />
  <view class="takeaway-search__scroll">
    <!-- 商品列表 -->
    <view 
      wx:for="{{ goodsList }}"
      wx:key="{{ item.goods.id }}"
      class="takeaway-search__goods"
    >
      <takeaway-goods
        goods-info="{{ item.goods }}"
        choosed-goods="{{ choosedGoods }}"
        class="takeaway-search__goods"
        bind:preview="takeAwayThemePreviewGoodsHandler"
        bind:change="takeAwayThemeGoodsChangeHandler"
        bind:overlimit="takeAwayThemeGoodsOverlimitHandler"
      />
    </view>
  </view>
  <view 
    wx:if="{{ searched && !goodsList.length }}"
    class="takeaway-search__empty">没有搜到相关商品</view>

  <!-- 商品详情弹窗 -->
  <takeaway-detail
    show="{{ showGoodsPreview }}"
    goods-info="{{ currentActiveGoods }}"
    choosed-goods="{{ choosedGoods }}"
    bind:close-preview="takeAwayThemeClosePreviewHandler"
    bind:change="takeAwayThemeGoodsChangeHandler"
    bind:overlimit="takeAwayThemeGoodsOverlimitHandler"
  />

  <!-- 底部购物篮 在多网点不营业的情况下，不展示这个条 -->
  <takeaway-bottom
    alias="{{ alias }}"
    iphonex-class="bottom-bar-container"
    start-fee="{{ startfee }}"
    takeout-cart-data="{{ cartGoods }}"
    all-choosed-num="{{ allChoosedNum }}"
    all-choosed-price="{{ allChoosedPrice }}"
    class="takeaway-search__bottom"
    bind:toggleCart="takeAwayThemeToggleCartHandler"
    bind:order="takeAwayThemeOrderHanlder"
  />

  <!-- 购物抽屉 -->
  <takeaway-cart
    iphonex-class="bottom-bar-container"
    takeout-cart-data="{{ cartGoods }}"
    show-takeout-cart="{{ showCart }}"
    bind:toggle="takeAwayThemeToggleCartHandler"
    bind:clear="takeAwayThemeClearCartHandler"
    bind:change="takeAwayThemeCartGoodsChangeHandler"
  />

  <!-- sku弹层 -->
  <base-sku
    show="{{ showTakeAwaySku }}"
    goods="{{ currentActiveGoods }}"
    sku="{{ currentActiveGoods.sku }}"
    buy-text="加入购物车"
    show-add-cart-btn="{{ false }}"
    hide-stock="{{ currentActiveGoods.sku.hideStock }}"
    reset-stepper-on-hide
    theme-class="{{ themeClass }}"
    bind:buy="takeAwayThemeSkuClickHandler"
    bind:sku-close="takeAwayThemeSkuCloseHandler"
  />
</view>
