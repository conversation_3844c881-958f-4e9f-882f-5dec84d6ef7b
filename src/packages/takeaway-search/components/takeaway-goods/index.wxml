<view class="goods-item" bind:tap="onTap">
  <view class="goods-item__image">
    <image
      src="{{ goodsInfo.picUrl }}"
      mode="aspectFit"
      lazy-load
    />
  </view>
  <view class="goods-item__detail">
    <text class="goods-item__detail--title">{{  goodsInfo.title }}</text>
    <text class="goods-item__detail--desc">{{  goodsInfo.sellPoint }}</text>
    <view wx:if="{{ goodsInfo.activity }}" class="goods-item__detail--tags">
      <block wx:if="{{ goodsInfo.activity.discountType }}">
        <van-tag plain type="danger" class="goods-item__detail--tags-tag">
          限时折扣
        </van-tag>
      </block>
      <block wx:else>
        <van-tag plain type="danger" class="goods-item__detail--tags-tag">会员折扣</van-tag>
      </block>
      <van-tag wx:if="{{ goodsInfo.activity.quota }}" plain type="danger" class="goods-item__detail--tags-tag">{{ '限购' + goodsInfo.activity.quota + '件' }}</van-tag>
    </view>
    <view 
      wx:if="{{ goodsInfo.processProcedure && goodsInfo.processProcedure.surplusInfo }}"
      class="goods-item__detail--surplus-info"
    >{{ goodsInfo.processProcedure.surplusInfo }}</view>
    <view class="goods-item__detail--price">
      <text class="goods-item__detail--price-current">¥<text>{{ utils.cent2yuan(goodsInfo.price) + (goodsInfo.sku.noneSku ? '' : '起') }}</text></text>
      <text wx:if="{{ goodsInfo.sku.oldPrice }}" class="goods-item__detail--price-origin">{{ goodsInfo.sku.oldPrice }}</text>
    </view>
    <view 
      wx:if="{{ goodsInfo.processProcedure &&  goodsInfo.processProcedure.procedureList }}"
      class="goods-item__detail--procedure-list"
    >
      <text
        wx:for="{{ goodsInfo.processProcedure.procedureList }}"
        wx:for-item="procedure"
        wx:key="{{ procedure.name }}"
        class="{{ procedure.isCurrent ? 'goods-item__detail--procedure-list--current' : '' }}"
      >
        {{ (index === 0) ? procedure.name : '-' + procedure.name }}
      </text>
    </view>
    <view 
      wx:if="{{ goodsInfo.processProcedure && goodsInfo.processProcedure.processInfo }}"
      class="goods-item__detail--process-info"
    >{{ goodsInfo.processProcedure.processInfo }}</view>
  </view>
  <view class="goods-item__opt">
    <input-number
      class="goods-item__opt--input"
      min="{{ choosedGoods[goodsInfo.alias].noneSku ? 0 : choosedGoods[goodsInfo.alias].num }}"
      max="{{ goodsInfo.activity.quota ? goodsInfo.activity.quota : choosedGoods[goodsInfo.alias].noneSku ? choosedGoods[goodsInfo.alias].stockNum : 99999 }}"
      value="{{ choosedGoods[goodsInfo.alias].num }}"
      is-value-show="{{ choosedGoods[goodsInfo.alias].num > 0 }}"
      is-minus-show="{{ choosedGoods[goodsInfo.alias].num > 0}}"
      disabled="{{ goodsInfo.sku.soldStatus === 2 }}"
      bind:change="onChange"
      bind:overlimit="onOverlimit"
    />
  </view>
</view>

<wxs src="./index.wxs" module="utils" />
