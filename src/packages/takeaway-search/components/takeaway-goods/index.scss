@mixin ellipsis($line) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;
}

.goods-item {
  position: relative;
  display: flex;
  width: 100%;

  &__image {
    width: 75px;
    height: 75px;
    line-height: 75px;
    margin-right: 10px;
    text-align: center;

    image {
      max-width: 100%;
      max-height: 100%;
      vertical-align: middle;
    }
  }

  &__detail {
    width: calc(100% - 85px);

    &--title {
      font-size: 14px;
      color: #333;
      line-height: 20px;

      @include ellipsis(2);
    }

    &--desc {
      font-size: 10px;
      color: #999;
      line-height: 14px;
      
      @include ellipsis(2);
    }

    &--tags {
      &-tag {
        margin-right: 5px;
      }
    }

    &--surplus-info {
      font-size: 10px;
      color: #faab0c;
      margin-top: 8px;
    }

    &--price {
      &-current {
        font-weight: 500;
        color: #f44;
        font-size: 10px;

        text {
          font-size: 14px;
        }
      }

      &-origin {
        margin-left: 5px;
        font-size: 10px;
        color: #999;
        text-decoration: line-through;
      }
    }

    &--procedure-list {
      color: #ccc;
      font-size: 10px;
  
      &--current {
        color: #333;
      }
    }
  
    &--process-info {
      font-size: 10px;
      color: #999;
    }
  }

  &__opt {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #fff;

    &--sku {
      background-color: #f44;
      width: 45px;
      height: 22px;
      border-radius: 11px;
      font-size: 10px;
      color: #fff;
    }
  }
}