import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    tagAlias: {
      type: String
    },

    tagId: {
      type: Number
    },

    goodsInfo: {
      type: Object
    },

    choosedGoods: {
      type: Object,
      value: {}
    }
  },

  methods: {
    onTap() {
      const { goodsInfo: goods, tagAlias, tagId } = this.data;
      this.triggerEvent('preview', {
        goods,
        tagAlias,
        tagId
      });
    },

    onChange(e) {
      const {
        goodsInfo: goods, tagAlias, tagId
      } = this.data;
      this.triggerEvent('change', {
        ...e.detail,
        goods,
        tagAlias,
        tagId
      });
    },

    onOverlimit(e) {
      const {
        goodsInfo: goods, tagAlias, tagId
      } = this.data;
      this.triggerEvent('overlimit', {
        ...e.detail,
        goods,
        tagAlias,
        tagId
      });
    }
  }
});
