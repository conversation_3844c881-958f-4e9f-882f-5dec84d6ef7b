import WscPage from 'pages/common/wsc-page/index';
import get from '@youzan/weapp-utils/lib/get';
import { parseMessages } from '../showcase-template/new-take-away/helpers';

import {
  state, actions, mutations, getters
} from './store';

const app = getApp();

WscPage({
  page: 1,

  pageSize: 10,

  keyword: '',

  data: state,

  onLoad(param) {
    this.init(param);
  },

  onReady() {
    const { title } = app.globalData.featurePageConfig;
    wx.setNavigationBarTitle({
      title
    });
  },

  onUnload() {
    // 设置到全局，外卖页可查看
    app.globalData.takeaway = {
      ...app.globalData.takeaway,
      cartGoods: this.data.newCartGoods
    };
  },

  onReachBottom() {
    if (!this.data.finished) {
      this.page++;
      this.fetchGoodsList(this.keyword);
    }
  },

  init(param) {
    const { startfee } = param;
    // 设置起送费
    this.INIT_STARTFEE({ startfee });
    // 合并外卖页购物车
    if (app.globalData.takeaway) {
      app.globalData.takeaway.needMerge = true;
      // 设置分组ids
      const ids = (app.globalData.takeaway.tagList || []).map(tag => tag.id);
      this.INIT_TAGLIST_IDS({ ids });
      this.MERGE_GOODS_TO_CART({
        goodsList: app.globalData.takeaway.cartGoods || []
      });
    }
  },

  takeAwayThemeSearchHandler(e) {
    this.page = 1;
    this.keyword = e.detail;
    this.fetchGoodsList(e.detail, true);
  },

  takeAwayThemePreviewGoodsHandler(e) {
    const { goods, tagAlias } = e.detail;
    this.updateCurrentActiveGoods({
      goods,
      tagAlias,
      showPreview: true
    });
  },

  takeAwayThemeGoodsChangeHandler(e) {
    const {
      goods, type, value
    } = e.detail;

    this.updateCurrentActiveGoods({ goods });
    this.updateSkuData({
      alias: goods.alias,
      type,
      num: value
    });
  },

  takeAwayThemeGoodsOverlimitHandler(e) {
    const {
      goods, type
    } = e.detail;
    const { choosedGoods } = this.data;

    this.updateCurrentActiveGoods({ goods });

    if (type === 'minus' && choosedGoods[goods.alias] && choosedGoods[goods.alias].noneSku === false) {
      return this.showToast('多规格商品请到购物车中删除');
    }
  },

  takeAwayThemeClosePreviewHandler() {
    this.UPDATE_PREVIEW_GOODS(false);
  },

  takeAwayThemeToggleCartHandler(e) {
    const show = e.detail;
    this.UPDATE_SHOW_CART_STATUS(show);
  },

  takeAwayThemeClearCartHandler() {
    this.CLEAR_CART_GOODS();
  },

  takeAwayThemeCartGoodsChangeHandler(e) {
    const { num, alias, skuId } = e.detail;

    this.UPDATE_GOODS_TO_CART({
      num,
      alias,
      skuId
    });

    this.UPDATE_CHOOSED_GOODS({
      alias,
      num,
      isCart: true
    });
  },

  takeAwayThemeSkuClickHandler(e) {
    const {
      messages,
      selectedSkuValues: skuNameList,
      selectedNum: num,
      selectedSkuComb: { id: skuId, stockNum, price }
    } = e.detail;
    const { messages: config = [] } = get(this.data, 'currentActiveGoods.sku', {});

    this.multiSkuGoodsToCart({
      skuId,
      stockNum,
      price,
      num,
      skuName: skuNameList.join(' '),
      message: parseMessages(messages, config)
    });
  },

  takeAwayThemeOrderHanlder() {
    this.CLEAR_CART_GOODS();
  },

  takeAwayThemeSkuCloseHandler() {
    this.UPDATE_SHOW_SKU_STATUS(false);
  },

  ...actions,

  ...mutations,

  ...getters
});
