// @ts-nocheck
import { mapState, mapActions } from '@ranta/store';
import Args from '@youzan/weapp-utils/lib/args';
import { cloud, useAsHook } from '@youzan/ranta-helper';
import store from './store/index';
import { BARGAIN_PATH } from './common/constants';
import { getShareParams } from 'shared/utils/share-params';
import { umpEnterShop } from '../../../../common/ump-enter-shop'; // eslint-disable-line
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';
import { fetchGrey } from './common/api';
import { vanxToRantaStore } from 'shared/common/base/ranta/store';
import logv3 from 'utils/log/logv3';
import type { HelpcutBeforeHelpcutRuleRender, HelpcutInfo } from '@youzan-cloud/cloud-biz-types';

const app = getApp();

export default class {
  ctx;

  @cloud('helpcutInfo', 'data')
  helpcutInfo: HelpcutInfo;

  @cloud('beforeHelpcutRuleRender', 'hook', { allowMultiple: false })
  beforeHelpcutRuleRender = useAsHook<HelpcutBeforeHelpcutRuleRender>();

  store = vanxToRantaStore(store);

  constructor({ ctx }) {
    ctx.store = this.store;

    this.ctx = ctx;

    const res = mapState(this, [
      'sponsorFetched',
      'queryOptions',
      'recommendGoods',
      'skuInfo',
      'showSkuModal',
      'goodsDetail',
      'bargainDetail',
      'showFollowPopup',
      'followExtraData',
      'isNewHelpCut',
      'hasLogin',
      'needFollow',
      'isCreator',
      'hasAuthorized',
      'newActionExtra',
      'activityStatus',
      'helpcutInfo',
    ]);

    Object.assign(this, res);

    mapActions(this, [
      'SET_QUERY_OPTIONS',
      'SET_SKU_CLOSE',
      'SET_SHOW_FOLLOW_POP',
      'SET_GREY',
      'SET_LAST_RECORD',
      'SET_AUTHORIZED',
      'SET_HELPCUT_INFO',
    ]);

    mapActions(this, [
      'HANDLE_BUY',
      'FETCH_SHOP_NAME',
      'FETCH_BARGAIN_DETAIL',
      'FETCH_BARGAIN_RECORDS',
    ]);

    this.store.watch('bargainDetail', (value) => {
      const {
        totalNum = 0,
        cutedNum = 0,
        joinNum = 0,
        minPrice = 0,
        startNum = 0,
      } = value;
      const helpcutParam = {
        /** 总共帮砍次数  */
        totalNum,
        /** 已帮砍次数  */
        cutedNum,
        /** 发起砍价次数 */
        startNum,
        /** 帮砍次数(X次/人) */
        joinNum,
        /** 砍价最低价(元) */
        minPrice,
      };
      this.SET_HELPCUT_INFO(helpcutParam);
      this.helpcutInfo = helpcutParam;
    });
  }

  onPullDownRefresh() {
    const { queryOptions } = this;
    this.load(queryOptions);
    // eslint-disable-next-line @youzan-open/tee/no-platform-field
    wx.stopPullDownRefresh();
  }

  beforePageMount({ query = {}, route }) {
    this.route = route;
    this.load(query);
  }

  load(query = {}) {
    const { umpAlias = '' } = query;
    const redirectUrl = Args.add('/' + this.route, query);

    umpEnterShop(query, umpAlias, 'helpCut', redirectUrl, {
      name: 'helpCut_without_ump_alias',
      message: '砍价缺少必要参数',
    })
      .then(() => {
        this.getGrey(query);
      })
      .catch(() => {
        this.getGrey(query);
      });
  }

  // 获取走哪个版本砍价
  getGrey(query) {
    const { umpActivityId } = query;
    fetchGrey(umpActivityId)
      .then(async (res) => {
        this.SET_GREY(res.value);
        await this.initAuthorizeStatus(res.value);
        this.init(query);
      })
      .catch(() => {
        this.SET_GREY(false);
        this.init(query);
      });
  }

  // 初始化数据
  init(query) {
    this.SET_QUERY_OPTIONS(query);
    this.SET_LAST_RECORD();
    this.FETCH_SHOP_NAME();
    this.FETCH_BARGAIN_DETAIL();
    this.FETCH_BARGAIN_RECORDS();

    app.logger &&
      app.logger.log({
        et: 'display', // 事件类型
        ei: 'enterpage', // 事件标识
        en: '浏览页面', // 事件名称
        pt: 'helpCut', // 页面类型
        params: {
          status: this.activityStatus,
          spm: 'helpCut',
        },
      });
  }

  // 获取渲染组件时的授权状态
  initAuthorizeStatus() {
    return app
      .resolveTeeAPI()
      .then((api) => api.getUserPrivacy())
      .then((authState) => {
        this.SET_AUTHORIZED(authState.nicknameAndAvatar && authState.mobile);
      });
  }

  // 设置页面分享
  onShareAppMessage() {
    const { sponsorId } = this.queryOptions || {};
    const { umpActivityId, activityId, minPrice, umpAlias } =
      this.bargainDetail || {};
    const { title, originCover } = this.goodsDetail || {};
    const sharePath = Args.add(
      this.isNewHelpCut ? 'packages/ump/helpcut/index' : BARGAIN_PATH,
      {
        ...defaultEnterShopOptions,
        sponsorId,
        umpAlias,
        umpActivityId: this.isNewHelpCut ? activityId : umpActivityId,
        ...getShareParams('native_custom'),
      }
    );
    return logv3.processShareData({
      title: `我想要 ${minPrice} 元拿走 ${
        title.length >= 10 ? title.slice(0, 10) + '...' : title
      } ,帮我砍一下吧！`,
      path: sharePath,
      imageUrl: originCover,
    });
  }
}
