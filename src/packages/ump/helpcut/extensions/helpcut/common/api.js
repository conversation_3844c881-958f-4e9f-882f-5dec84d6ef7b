import parseData from 'utils/parse-data';

const app = getApp();

// 获取新老版本砍价灰度接口
export const fetchGrey = (activityId) => {
  return app.request({
    path: '/wscump/helpcut/v2/is-in-white-list',
    query: {
      activityId,
    },
  });
};

// 获取砍价记录新版
export const fetchBargainRecordsNew = (params) =>
  app.request({
    path: '/wscump/helpcut/v2/get-bargain-records.json',
    query: params,
  });

// 获取砍价记录
export const fetchBargainRecords = (params) =>
  app.request({
    path: '/wscump/helpcut/get-bargain-records.json',
    query: params,
  });

// 获取砍价详情新版
export const getSponsorNew = (params) =>
  app.request({
    path: '/wscump/helpcut/v2/get-sponsor',
    query: params,
  });

// 获取砍价详情
export const getSponsor = (params) =>
  app.request({
    path: '/wscump/helpcut/get-sponsor',
    query: params,
  });

// 获取推荐商品--搜索推荐接口
export const getRecommend = (data) => {
  return app.request({
    method: 'POST',
    path: '/wscump/helpcut/recommend-response.json',
    data,
  });
};

// 发放下单权益
export const giveBuyRights = (data) => {
  return app.request({
    data,
    path: '/wscump/helpcut/give-buy-rights.json',
  });
};

// 帮砍流程处理
export const helpcutVerify = (data) => {
  return app.request({
    data,
    path: '/wscump/helpcut/helpcut-verify.json',
  });
};

// sku数据处理
export const parseSkuDetail = (data) =>
  parseData(
    {
      sku: {
        isNoSku: (data) => data.sku.none_sku,
        id: (data) => data.sku.collection_id,
        tree: (data) => data.sku.tree,
      },
      skuForSkuComponent: (data) => data.sku,
    },
    [],
    data
  );

// 获取分享海报
export const fetchPoster = (data) =>
  app.request({
    method: 'POST',
    path: '/wscump/cardsnap/helpcut-post-v2.json',
    data,
  });

// 生成海报
export const generatePoster = (data) =>
  app.request({
    data,
    method: 'POST',
    path: '/wscump/helpcut/get-poster.json',
  });

// 获取当前所有砍价活动列表
export const getHelpcutList = (data) =>
  app.request({
    path: '/wscump/helpcut/v2/get-helpcut-list',
    data,
  });

// 获取使用优惠券跳转链接
export const getCouponRedirectPath = (data) =>
  app.request({
    path: '/wscump/coupon/coupon_use_redirect.json',
    data,
  });

// 发起订阅消息同步业务方
export const setMsgPush = (data) =>
  app.request({
    path: '/wscump/helpcut/set-msg-push.json',
    method: 'POST',
    data,
  });
