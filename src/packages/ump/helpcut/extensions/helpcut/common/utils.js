import parseData from 'utils/parse-data';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import spm from 'shared/utils/spm';
import { logPageEnter } from 'utils/log/logv3';

const app = getApp();

export const parseSkuDetail = (data) =>
  parseData(
    {
      sku: {
        isNoSku: (data) => data.sku.none_sku,
        id: (data) => data.sku.collection_id,
        tree: (data) => data.sku.tree,
      },
      skuForSkuComponent: (data) => data.sku,
    },
    [],
    data
  );

const generateMobileNum = () => {
  const basicList = [0, 1, 2, 3, 5, 6, 7, 8, 9];
  const numberPrefix = [13, 15, 18];
  const mobilePrefixList = numberPrefix.map((item) =>
    basicList.map((i) => `${item}${i}`)
  );
  const fn = (num) => Math.floor(num * Math.random());
  const firstIndex = fn(numberPrefix.length);
  const secondIndex = fn(basicList.length);
  const randomMobilePrefix = mobilePrefixList[firstIndex][secondIndex];
  let mobileTailNum = fn(10000);
  mobileTailNum = mobileTailNum ? mobileTailNum + 1000 : mobileTailNum;
  return `${randomMobilePrefix}****${mobileTailNum}`;
};

// 获取砍价模拟用户跑马灯数据，返回价格介于最低价和当前价格之间
export const generateUserList = (
  price = 0,
  lowestPrice = 0,
  needMinPriceBuy
) => {
  let moneyOne = lowestPrice;
  let moneyTwo = lowestPrice;

  if (isNaN(+lowestPrice)) {
    lowestPrice = 0;
  }

  if (isNaN(+price) || +price <= 0) {
    price = 100;
  } else if (price - lowestPrice > 0) {
    price -= lowestPrice;
  }

  const randomMobileList = [];
  for (let i = 0; i < 10; i++) {
    // 设置了最低价可购买，只展示最低价
    if (!needMinPriceBuy) {
      moneyOne = (+lowestPrice + Math.random() * price).toFixed(2);
      moneyTwo = (+lowestPrice + Math.random() * price).toFixed(2);
    }

    const name = generateMobileNum();
    const randowNum = parseInt(Math.random() * 3 + '', 10);
    const msgList = [
      `${name}发起了砍价`,
      `${name}大刀一挥帮好友砍掉了 ${moneyOne} 元`,
      `${name}砍价成功了，${moneyTwo} 元购得商品`,
    ];
    randomMobileList.push({
      isShort: randowNum < 1,
      message: msgList[randowNum],
      mobile: randomMobileList.concat(generateMobileNum()),
    });
  }
  return randomMobileList;
};

export const getBannerId = (component, index) => {
  return `${spm.getPageSpmTypeId()}~${component}~${index}~${makeRandomString(
    8
  )}`;
};

// 埋点
export const sendLogger = ({
  et = 'view',
  ei = 'view',
  en,
  pt = 'helpCut',
  params = {},
}) => {
  app.logger &&
    app.logger.log({
      et, // 事件类型
      ei, // 事件标识
      en, // 事件名称
      pt,
      params: {
        spm: 'helpCut',
        ...params,
      },
    });
};

// 中台化改造后埋点改造
export const umpActivityLog = (pageId, options) => {
  const pages = getCurrentPages() || [];
  const currentPage = pages[pages.length - 1];
  const url = currentPage?.route;
  logPageEnter(url, pageId, options, 'helpCut', pages);
};
