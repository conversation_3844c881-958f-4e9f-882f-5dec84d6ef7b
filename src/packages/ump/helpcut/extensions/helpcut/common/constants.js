const p = 'packages';
export const DEFAULT_NICKNAME = '神秘好友';
export const BARGAIN_PATH = `${p}/ump/bargain-purchase/home/<USER>
export const DEFAULT_AVATAR =
  'https://img01.yzcdn.cn/wsc-minapp/bargain-detail/home/<USER>/profile.png';
export const CREATOR_STAMP = '「发现一个好货，邀请好友砍价拿走」';
export const SPONSOR_STAMP = '「朋友一生一起走，帮砍一刀有没有」';

// 砍价活动状态，与下列常量一一对应
export const ACTION_STATUS_ENUM = {
  creatorOngoing: 1, // 发起者正在进行中（未到底价 & 可现价购买）
  creatorCutMinPrice: 2, // 发起者已经帮砍到底价
  creatorNeedButNoMinBuy: 3, // 发起者需要最底价购买但是未到最底价
  creatorHasPurchased: 4, // 发起者已砍价购买
  creatorCutExpired: 5, // 发起者当前砍价已过期
  creatorActivityFinished: 6, // 发起者当前活动已结束
  creatorSoldOut: 7, // 发起者商品已售罄
  creatorReserve: 15, // 发起者当前砍价未开始去预约
  creatorNeedLogin: 16, // 发起者需要登录或授权
  sponsorCanCut: 8, // 帮砍者当前可以帮忙砍价
  sponsorHasMinPrice: 9, // 帮砍者打开但已经砍到最底价了
  sponsorActivityNoChance: 10, // 帮砍者已经砍价过这个商品（活动纬度）
  sponsorStoreNoChance: 11, // 帮砍者已经没有砍价机会了（店铺纬度）
  sponsorCutFinished: 12, // 帮砍者当前砍价已结束
  sponsorActivityFinished: 13, // 帮砍者当前活动已结束
  sponsorSoldOut: 14, // 帮砍者当前商品已抢光
  sponsorNeedLogin: 17, // 帮砍者需要登录或授权
};

// 发起者场景, 对应ACTION_STATUS_ENUM
export const CREATOR_ACTION_LIST = [1, 2, 3, 4, 5, 6, 7, 15];

// 成功场景标识, 对应ACTION_STATUS_ENUM
export const SUCCESS_ACTION_LIST = [1, 2, 3, 8, 9, 10, 11];

// 新版在活动页面能展示的场景
export const NEW_SHOW_ACTION_LIST = [1, 2, 3, 4, 5, 6, 7, 15];

// 新版在活动页面需要展示弹窗的场景
export const NEW_SHOW_POPUP_LIST = [2, 5, 6, 7];

// 新版在活动页面需要展示帮砍弹窗的场景
export const NEW_SHOW_HELPCUT_POPUP_LIST = [1, 3];

// 失败场景文案, 对应ACTION_STATUS_ENUM
export const FAIL_ACTION_COPYRIGHTING = {
  4: '已砍价购买',
  5: '砍价已过期',
  6: '活动已结束',
  7: '商品已售罄',
  12: '砍价已结束',
  13: '活动已结束',
  14: '商品已砍光',
};

// 成功场景, 对应ACTION_STATUS_ENUM
export const SUCCESS_ACTION_MAP = {
  1: {
    // creatorOngoing
    getTip: ({ currentPrice }) => `恭喜你!已砍至${currentPrice}元啦!`,
    getBtnGroup: () => [
      {
        content: '现价购买',
        event: 'onPurchase',
        class: 'prime yellow-background',
      },
      {
        content: '邀请好友帮砍',
        event: 'onInviteFriend',
        class: 'prime gradient-red-background',
      },
    ],
  },
  2: {
    // creatorCutMinPrice
    getTip: ({ currentPrice }) => `恭喜你!已砍至${currentPrice}元啦!`,
    getBtnGroup: ({ currentPrice }) => [
      {
        content: `${currentPrice}元购买`,
        event: 'onPurchase',
        class: 'prime gradient-red-background',
      },
    ],
  },
  3: {
    // creatorNeedButNoMinBuy
    getTip: ({ totalNum, cutedNum, minPrice }) => {
      const remainNum = totalNum - cutedNum;
      return `还差<span class='strong'> ${remainNum} </span>人即可<span class='strong'> ${+minPrice} </span>元拿走`;
    },
    getBtnGroup: ({ currentPrice }) => [
      {
        content: ['现价购买', `¥${currentPrice}`],
        event: 'onPurchase',
        class: 'prime yellow-background price',
      },
      {
        content: ['邀请好友帮砍'],
        event: 'onInviteFriend',
        class: 'prime gradient-red-background',
      },
    ],
  },
  8: {
    // sponsorCanCut
    getTip: ({ currentPrice, originPrice }) => {
      const countPrice = (Number(originPrice) - Number(currentPrice)).toFixed(
        2
      );
      return `已砍${countPrice}元`;
    },
    getBtnGroup: () => [
      {
        content: '我也要砍价拿',
        event: 'onCreateNewBargain',
        class: 'minor yellow-background',
      },
      {
        content: '帮好友砍一刀',
        class: 'prime gradient-red-background',
        isAuthorizeRequired: true,
      },
    ],
  },
  9: {
    // sponsorHasMinPrice
    getTip: () => '已砍至最低价',
    getBtnGroup: () => [
      {
        content: '我也要砍价拿',
        event: 'onCreateNewBargain',
        class: 'prime yellow-background',
      },
      {
        content: '已砍至最低价',
        class: 'minor gray-background',
      },
    ],
  },
  10: {
    // sponsorActivityNoChance
    getTip: () => '已砍过此商品',
    getBtnGroup: () => [
      {
        content: '我也要砍价拿',
        event: 'onCreateNewBargain',
        class: 'prime yellow-background ',
      },
      {
        content: '已砍过此商品',
        class: 'minor gray-background',
      },
    ],
  },
  11: {
    // sponsorStoreNoChance
    getTip: () => '砍价机会已用光',
    getBtnGroup: () => [
      {
        content: '我也要砍价拿',
        event: 'onCreateNewBargain',
        class: 'prime yellow-background',
      },
      {
        content: '砍价机会已用光',
        class: 'minor gray-background',
      },
    ],
  },
};

// 失败场景, 对应ACTION_STATUS_ENUM
export const FAIL_ACTION_MAP = {
  4: {
    // creatorHasPurchased
    getBtnGroup: ({ orderNo }) => [
      {
        content: '查看订单',
        navigateTo: `/${p}/trade/order/result/index?orderNo=${orderNo}`,
      },
      {
        content: '进入店铺',
        navigateTo: `/${p}/home/<USER>/index`,
        openType: 'switchTab',
      },
    ],
  },
  5: {
    // creatorCutExpired
    getBtnGroup: ({ alias, activityId }) => [
      {
        content: '重新发起砍价',
        navigateTo: `/pages/goods/detail/index?alias=${alias}&type=helpcut&activityId=${activityId}`,
      },
    ],
  },
  6: {
    // creatorActivityFinished
    getBtnGroup: () => [
      {
        content: '进入店铺',
        openType: 'switchTab',
        navigateTo: `/${p}/home/<USER>/index`,
      },
    ],
  },
  7: {
    // creatorSoldOut
    getBtnGroup: () => [
      {
        content: '进入店铺',
        openType: 'switchTab',
        navigateTo: `/${p}/home/<USER>/index`,
      },
    ],
  },
  12: {
    // sponsorCutFinished
    getBtnGroup: ({ alias, activityId }) => [
      {
        content: '我也要砍价拿',
        navigateTo: `/pages/goods/detail/index?alias=${alias}&type=helpcut&activityId=${activityId}`,
      },
    ],
  },
  13: {
    // sponsorActivityFinished
    getBtnGroup: () => [
      {
        content: '查看店铺商品',
        navigateTo: `/${p}/home/<USER>/index`,
        openType: 'switchTab',
      },
    ],
  },
  14: {
    // sponsorSoldOut
    getBtnGroup: () => [
      {
        content: '查看店铺商品',
        openType: 'switchTab',
        navigateTo: `/${p}/home/<USER>/index`,
      },
    ],
  },
};

const goingAction = {
  // creatorOngoing
  getTip: ({ cutedPrice, leastPrice }) => {
    return {
      status: '',
      tip: [
        {
          type: 'normal',
          content: '已砍',
        },
        {
          type: 'price',
          content: cutedPrice,
        },
        {
          type: 'normal',
          content: '元，仅差',
        },
        {
          type: 'priceBig',
          content: leastPrice,
        },
        {
          type: 'normal',
          content: '元',
        },
      ],
    };
  },
  getBtn: ({ leastCutNum, minPrice }) => {
    return {
      content: `再邀请${leastCutNum}人帮砍，可${+minPrice}元拿`,
      event: 'invite',
    };
  },
  needProcess: true,
  needCutDown: true,
  cutDownText: '后过期',
  needCurrentBuy: (currentCanBuy) => {
    return currentCanBuy;
  },
  needPopup: (hasNewCut) => {
    return hasNewCut;
  },
};

const cutExpiredAction = {
  // creatorCutExpired
  getTip: ({ leastPrice }) => {
    return {
      status: '',
      tip: [
        {
          type: 'normal',
          content: '砍价结束，仅差',
          class: 'title-normal',
        },
        {
          type: 'price',
          content: leastPrice,
          class: `title-price price-${leastPrice.length}`,
        },
        {
          type: 'normal',
          content: '元砍成',
          class: 'title-normal',
        },
      ],
    };
  },
  getBtn: () => {
    return {
      content: '重新发起砍价',
      event: 'restart',
    };
  },
  needStartBtn: () => {
    return false;
  },
  needProcess: true,
  needPopup: () => {
    return true;
  },
};

const cutMinPriceAction = {
  // creatorCutMinPrice
  getTip: ({ originalPrice }) => {
    return {
      status: '恭喜你！',
      tip: [
        {
          type: 'normal',
          content: '已砍成价值',
          class: 'title-normal',
        },
        {
          type: 'priceBig',
          content: originalPrice,
          class: `title-price price-${originalPrice.length}`,
        },
        {
          type: 'normal',
          content: '元的商品',
          class: 'title-normal',
        },
      ],
    };
  },
  getBtn: ({ minPrice }) => {
    return {
      content: `点击${+minPrice}元拿`,
      event: 'buy',
    };
  },
  needPopup: () => {
    return true;
  },
};

// 新版本活动页面发起者情况对应的按钮和文案
export const CREATOR_ACTION_MAP = {
  1: goingAction,
  2: cutMinPriceAction,
  3: goingAction,
  4: {
    // creatorHasPurchased
    getTip: ({ originalPrice }) => {
      return {
        status: '恭喜你！',
        tip: [
          {
            type: 'normal',
            content: '已砍成价值',
          },
          {
            type: 'priceBig',
            content: originalPrice,
          },
          {
            type: 'normal',
            content: '元的商品',
          },
        ],
      };
    },
    getBtn: ({ canRestartBargain }) => {
      return {
        content: canRestartBargain
          ? '重新发起砍价'
          : '你已领取该商品，不能重复砍价',
        event: canRestartBargain ? 'restart' : '',
        disable: !canRestartBargain,
      };
    },
  },
  5: cutExpiredAction,
  6: {
    // creatorActivityFinished
    getTip: () => {
      return {
        status: '活动已结束，很遗憾未砍成',
      };
    },
    showRecommend: true,
    needPopup: () => {
      return true;
    },
  },
  7: {
    // creatorSoldOut
    getTip: () => {
      return {
        status: '库存不足，活动已结束',
      };
    },
    showRecommend: true,
    needPopup: () => {
      return true;
    },
  },
  15: {
    // creatorReserve
    getTip: ({ originalPrice, minPrice }) => {
      return {
        status: '',
        needWrap: true,
        tip: [
          {
            type: 'normal',
            content: '价值',
          },
          {
            type: 'priceBig',
            content: originalPrice,
          },
          {
            type: 'normal',
            content: '元商品',
          },
          {
            type: 'normal',
            content: minPrice,
          },
          {
            type: 'normal',
            content: '元拿',
          },
        ],
      };
    },
    needCutDown: true,
    cutDownText: '后开始',
    getBtn: ({ finishBook }) => {
      return {
        content: finishBook ? '已预约' : '立即预约',
        event: 'reserve',
      };
    },
  },
};

export const POPUP_MAP = {
  1: {
    type: 'frameless',
    showRedPacket: true,
  },
  2: {
    type: 'frame',
    needGoodsImg: true,
    content: cutMinPriceAction.getTip,
    btn: cutMinPriceAction.getBtn,
  },
  3: {
    type: 'frameless',
    showRedPacket: true,
  },
  5: {
    type: 'frame',
    needGoodsImg: true,
    content: cutExpiredAction.getTip,
    btn: cutExpiredAction.getBtn,
    extraBtn: () => {
      return {
        need: true,
        content: '进店逛逛',
        event: 'shop',
      };
    },
    needProcess: true,
    showRecommendBottom: () => {
      return true;
    },
  },
  6: {
    type: 'frame',
    content: () => {
      return {
        status: '活动已结束，很遗憾未砍成',
      };
    },
    viceBtn: () => {
      return {
        content: '进店逛逛',
        event: 'shop',
      };
    },
    showRecommendTop: true,
  },
  7: {
    type: 'frame',
    content: () => {
      return {
        status: '库存不足，活动已结束',
      };
    },
    viceBtn: () => {
      return {
        content: '进店逛逛',
        event: 'shop',
      };
    },
    showRecommendTop: true,
  },
};
