const app = getApp();
// 订阅消息
export function subscribeMessage({
  scene = '',
  successCallBack = null,
  failCallBack = null,
}) {
  wx.showLoading();
  app
    .request({
      path: '/wscump/common/get-template.json',
      query: { scene },
    })
    .then((res) => {
      wx.hideLoading();
      if (res && res.templateIdList) {
        wx.requestSubscribeMessage({
          tmplIds: res.templateIdList,
          success: () => {
            // 成功后出发成功后的业务逻辑
            successCallBack && successCallBack();
          },
          fail: (err) => {
            failCallBack && failCallBack(err);
          },
        });
      } else {
        // 降级处理, 商家模板消息满了之后 没有模板返回，不发起微信订阅消息，只发起业务接口
        successCallBack && successCallBack();
      }
    })
    .catch((err) => {
      wx.hideLoading();
      err &&
        wx.showToast({
          title: err,
          icon: 'none',
        });
    });
}
