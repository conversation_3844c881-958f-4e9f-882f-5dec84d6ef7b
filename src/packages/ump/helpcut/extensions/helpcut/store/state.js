import { DEFAULT_AVATAR } from '../common/constants';

const state = {
  isNewHelpCut: false, // 是否走新版本砍价

  hasLogin: true, // 是否登录

  needFollow: false, // 是否还需要关注公众号（后台设置需要关注公众号并且当前用户不为公众号粉丝）

  hasAuthorized: false, // 是否已经授权

  isCreator: true, // 是否为发起人

  lastId: null, // 最后一个帮砍人id

  userCutPrice: '', // 当前用户砍价金额

  allHelpcutListNumber: 0, // 当前返回砍价活动个数

  allHelpcutList: [], // 当前所有砍价活动列表

  userCutDetail: {}, // 帮砍详情

  skuInfo: {}, // 砍价sku信息

  userInfo: {}, // 当前用户信息

  shopInfo: {}, // 店铺信息

  goodsDetail: {}, // 商品信息

  queryOptions: {}, // 路径参数

  initiatorInfo: {}, // 帮砍者信息

  bargainDetail: {}, // 砍价活动信息

  couponResult: {}, // 当前优惠券信息

  couponGoodsList: [], // 优惠券可用商品列表

  followExtraData: {}, // 关注弹窗依赖数据

  recommendGoods: [], // 推荐商品
  recommendGoodsThree: [],

  bargainUser: [], // 当前跑马灯用户信息

  bargainRecords: [], // 砍价帮砍记录

  sponsorFetched: false, // 是否展示页面

  fetchRecordIng: true, // 砍价帮砍记录获取中

  fetchRecordFail: false, // 砍价帮砍记录获取失败

  showSkuModal: false, // 展示sku弹层

  showActionSheet: false, // 展示邀请好友弹层

  showCouponPop: false, // 是否展示优惠券弹层

  showCutSuccess: false, // 是否展示帮砍成功弹层

  showFollowPopup: false, // 是否展示关注公众号弹层

  finishBook: false, // 是否已完成预约

  defaultAvatar: DEFAULT_AVATAR, // 默认头像

  currentStock: 0, // 当前库存

  helpcutInfo: {}, // 砍价信息

  hasBehaviorReady: false, // 行为组件是否准备好
};

export default state;
