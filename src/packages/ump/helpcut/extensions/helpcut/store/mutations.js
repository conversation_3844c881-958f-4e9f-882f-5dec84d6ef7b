import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import Args from '@youzan/weapp-utils/lib/args';

const mutations = {
  // 设置页面路径参数
  SET_QUERY_OPTIONS(state, payload) {
    state.queryOptions = payload;
  },

  // 设置缓存的最大帮砍记录的id
  SET_LAST_RECORD(state) {
    state.lastId = wx.getStorageSync('last_helpcut_id');
  },

  // 设置是否进入新版砍价流程
  SET_GREY(state, payload) {
    state.isNewHelpCut = payload;
  },

  // 设置是否已授权
  SET_AUTHORIZED(state, payload) {
    state.hasAuthorized = !!payload;
  },

  // 设置是否完成订阅
  SET_FINISH_BOOK(state) {
    state.finishBook = true;
  },

  // 设置是否登录
  SET_HAS_LOGIN(state, payload) {
    state.hasLogin = payload;
  },

  // 设置是否还需要关注公众号
  SET_NEED_FOLLOW(state, payload) {
    state.needFollow = payload;
  },

  // 设置是否为发起人
  SET_CREATOR(state, payload) {
    const { isCreator, hasLogin } = payload;
    const { is_share } = state.queryOptions;
    state.isCreator = isCreator || (!hasLogin && is_share);
  },

  // 获取店铺手机号
  SET_SHOP_PHONE(state, payload) {
    state.shopInfo = {
      name: state.shopInfo.name,
      phoneNumber: payload,
    };
  },

  // 设置店铺名称
  SET_SHOP_NAME(state, payload) {
    state.shopInfo.name = payload;
  },

  // 设置发起者用户信息
  SET_INITIATOR_INFO(state, payload) {
    state.initiatorInfo = {
      avatar: payload.avatar,
      nickName: payload.nickName,
    };
  },

  // 设置当前用户信息
  SET_USER_INFO(state, payload) {
    state.userInfo = payload;
  },

  // 设置帮砍记录信息
  SET_BARGAIN_RECORDS(state, payload) {
    state.bargainRecords = payload;
    state.fetchRecordIng = false;
    if (payload?.lastId) {
      wx.setStorageSync('last_helpcut_id', payload.lastId);
    }
  },

  // 设置跑马灯用户信息
  SET_BARGAIN_USER_LIST(state, payload) {
    state.bargainUser = payload;
  },

  // 设置帮砍记录获取失败标识
  SET_RECORDS_FAIL(state) {
    state.fetchRecordFail = true;
  },

  // 获取推荐商品列表
  SET_RECOMMEND_GOODS(state, payload) {
    state.recommendGoods = payload;
    state.recommendGoodsThree = payload.slice(0, 3);
  },

  // 设置获取砍价信息接口成功
  SET_SPONSOR_SUCCESS(state) {
    state.sponsorFetched = true;
  },

  // 更新砍价活动信息
  UPDATE_BARGAIN_DETAIL(state, payload) {
    state.bargainDetail = payload;
  },

  // 设置帮砍详情
  SET_USER_CUT_DETAIL(state, payload) {
    state.userCutDetail = payload;
  },

  // 设置帮砍用户当前砍价金额
  SET_USER_CUT_PRICE(state, payload) {
    state.userCutPrice = payload;
  },

  // 设置是否展示关注弹层
  SET_SHOW_FOLLOW_POP(state, payload) {
    state.showFollowPopup = payload;
  },

  // 设置优惠券弹层信息
  SET_COUPON_RESULT(state, payload) {
    state.couponResult = payload;
  },

  // 设置是否展示优惠券弹层
  SET_SHOW_COUPON_POP(state, payload) {
    state.showCouponPop = payload;
  },

  // 设置优惠券可用商品列表
  SET_COUPON_GOODS(state, payload) {
    state.couponGoodsList = payload;
  },

  // 设置是否展示帮砍成功弹层
  SET_SHOW_CUT_SUCESS(state, payload) {
    state.showCutSuccess = payload;
  },

  // 设置是否展示底部邀请卡片弹层
  SET_SHOW_ACTION_SHEET(state, payload) {
    state.showActionSheet = payload;
  },

  // 关闭sku弹层
  SET_SKU_CLOSE(state) {
    state.showSkuModal = false;
  },

  SET_HELPCUT_LIST_NUMBER(state, payload) {
    state.allHelpcutListNumber = payload;
  },

  // 设置当前砍价活动列表
  SET_HELPCUT_LIST(state, payload) {
    state.allHelpcutList = payload;
  },

  // 设置砍价部分展示信息
  SET_HELPCUT_INFO(state, payload) {
    state.helpcutInfo = payload;
  },

  SET_BEHAVIOR_READY(state, payload) {
    state.hasBehaviorReady = payload;
  },

  // 设置关注公众号信息
  SET_EXTRA_DATA(state, payload) {
    const { isNewHelpCut, isCreator } = state;
    const { sponsorId } = state.queryOptions;
    const feature = state.queryOptions || {};
    !isCreator && (feature.sponsorId = sponsorId.toString());
    state.followExtraData = {
      bizCode: 1,
      bizSubCode: isNewHelpCut && isCreator ? 0 : 1,
      activityKey: payload,
      feature,
      targetUrl: Args.add('packages/ump/helpcut/index', feature),
    };
  },

  // 设置商品信息
  SET_GOODS_DETAIL(state, payload) {
    const {
      goodsAlias,
      goodsId,
      goodsName,
      originalPrice,
      thumbUrl,
      yzUid,
      activity = {},
    } = payload || {};
    state.goodsDetail = {
      alias: goodsAlias,
      id: goodsId,
      originPrice: (originalPrice / 100).toFixed(2),
      cover: cdnImage(thumbUrl, '!200x0.jpg'),
      title: goodsName,
      originCover: cdnImage(thumbUrl, '!800x0.jpg'),
      yzUid,
    };
    state.currentStock = activity.currentStock;
  },

  SET_CURRENT_STOCK(state, payload) {
    const { activity = {} } = payload || {};
    state.currentStock = activity.currentStock;
  },

  // 设置sku信息
  SET_SKU_INFO(state, skuData) {
    const { minPrice, currentPrice } = state.bargainDetail || {};
    const { id, alias, title, cover } = state.goodsDetail || {};
    const { skuForSkuComponent, sku } = skuData;
    // 项目需求为 多sku弹起sku面板 单sku直接下单 目前为了快速解决这个问题 直接改成都弹起sku面板
    state.showSkuModal = true;
    state.skuInfo = {
      extraData: {
        currentPrice,
        lowestPrice: minPrice,
        hideSkuStepper: true,
        useCustomHeaderPrice: true,
      },
      sku: skuForSkuComponent,
      goodsDetail: {
        id,
        alias,
        title,
        picture: cover,
        skuId: sku.id,
      },
    };
  },

  // 设置砍价活动信息
  SET_BARGAIN_DETAIL(state, payload) {
    const { defaultAvatar } = state;
    const { isFirstCut: queryFirstCut } = state.queryOptions;
    // 去掉isFirstCut
    delete state.queryOptions.isFirstCut;
    const {
      alias,
      status,
      orderNo,
      minPrice,
      goodsId,
      isCreator,
      avatar, // 发起者头像
      canJoinCut,
      isJoinCut,
      currentCanBuy,
      currentPrice,
      goodsStock,
      activity = {},
      totalNum,
      cutedNum,
      cutedPrice,
      leastPrice,
      originalPrice,
      firstCut = 0,
      leastCutNum,
      expiredAt = new Date().getTime(),
      needFollow,
      isFirstCut,
      hasNewCut,
      helpCutLimit,
    } = payload;
    const {
      id,
      joinNum = 0,
      startNum = 0,
      currentStock,
      umpActivityId,
      isMinPriceBuy,
      endAt = new Date().getTime(),
      startAt,
    } = activity;
    state.bargainDetail = {
      goodsId,
      orderNo,
      joinNum,
      startNum,
      totalNum,
      cutedNum,
      cutedPrice: (cutedPrice / 100).toFixed(2),
      leastPrice: (leastPrice / 100).toFixed(2),
      originalPrice: (originalPrice / 100).toFixed(2),
      firstCut: (firstCut / 100).toFixed(2),
      leastCutNum,
      isCreator,
      avatar: avatar || defaultAvatar,
      canJoinCut,
      currentCanBuy,
      umpAlias: alias,
      umpActivityId,
      activityId: id,
      needMinPriceBuy: isMinPriceBuy,
      isPurchased: status === 1,
      isWholeStoreLimit: isJoinCut,
      activityEndTime: endAt,
      activityStartTime: startAt,
      bargainExpireTime: expiredAt,
      minPrice: (minPrice / 100).toFixed(2),
      currentPrice: (currentPrice / 100).toFixed(2),
      activityStock: Math.min(currentStock, goodsStock),
      bargainDetailSeted: true, // 用于标记是否已经成功设置砍价活动信息
      needFollow,
      showFirstCut: (isFirstCut || queryFirstCut) && firstCut,
      hasNewCut,
      helpCutLimit,
    };
  },
};

export default mutations;
