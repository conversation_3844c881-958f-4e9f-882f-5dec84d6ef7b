import { reportSkynet } from 'utils/log/logv3';
import { formatSkuTree } from 'shared/common/components/base-sku/common/sku-format-tree';
import Event from '@youzan/weapp-utils/lib/event';
import {
  getSponsor,
  giveBuyRights,
  helpcutVerify,
  fetchBargainRecords,
  getSponsorNew,
  fetchBargainRecordsNew,
  getRecommend,
  getHelpcutList,
  getCouponRedirectPath,
} from '../common/api';
import {
  generateUserList,
  parseSkuDetail,
  getBannerId,
  sendLogger,
  umpActivityLog,
} from '../common/utils';
import { DEFAULT_NICKNAME, DEFAULT_AVATAR } from '../common/constants';
// import skuApi from 'common-api/sku/index';
import Navigate from '@/helpers/navigate';
import Args from '@youzan/weapp-utils/lib/args';
import {
  skuTradeUtilsPromise,
  skuApiPromise,
} from 'shared/utils/async-base-sku';

const app = getApp();

const actions = {
  // 初始化店铺名
  FETCH_SHOP_NAME({ commit }) {
    const shopInfo = app.getShopInfoSync();
    commit('SET_SHOP_NAME', shopInfo.shopName || shopInfo.shop_name);
  },

  // 初始化帮砍用户跑马灯
  FETCH_BARGAIN_USER_LIST({ commit, state }) {
    const { minPrice, currentPrice, needMinPriceBuy } = state.bargainDetail;
    commit(
      'SET_BARGAIN_USER_LIST',
      generateUserList(currentPrice, minPrice, needMinPriceBuy)
    );
  },

  // 初始化推荐商品
  FETCH_RECOMMEND_GOODS({ commit, state }, activity) {
    const { isCustomGoods, goodsIds = [] } = activity || {};
    // 自定义推荐商品参数
    const isCustomGoodsParams = {
      isCustomGoods,
      goodsIds,
    };
    const { id } = state.goodsDetail || {};
    getRecommend({
      ...(isCustomGoods ? isCustomGoodsParams : {}),
      scene: 'wsc~helpcut~rec',
      extraParam: {
        goodsIds: [id],
      },
    })
      .then((recommendGoods = []) => {
        commit('SET_RECOMMEND_GOODS', recommendGoods);
        (recommendGoods || []).forEach((item, i) => {
          sendLogger({
            et: 'view', // 事件类型
            ei: 'view', // 事件标识
            en: '商品曝光', // 事件名称
            params: {
              component: 'recommend',
              page_type: 'helpcut',
              alg: item.algs,
              banenr_id: getBannerId('recommend', i + 1),
              goods_id: item.id,
              item_type: 'goods',
              item_id: item.id,
            },
          });
        });
      })
      .catch(() => {
        wx.showToast({
          title: '获取推荐商品失败',
          icon: 'none',
        });
      });
  },

  // 初始化砍价帮砍记录
  FETCH_BARGAIN_RECORDS({ commit, state }) {
    commit('SET_LAST_RECORD');
    const { sponsorId } = state.queryOptions || {};
    if (!sponsorId || sponsorId === 'undefined') return;
    const { isNewHelpCut, lastId } = state;
    const api = isNewHelpCut ? fetchBargainRecordsNew : fetchBargainRecords;
    const params = isNewHelpCut
      ? {
          sponsorId,
          recordId: lastId,
        }
      : {
          sponsorId,
        };
    api(params)
      .then((records) => {
        commit('SET_BARGAIN_RECORDS', records);
      })
      .catch(() => {
        commit('SET_RECORDS_FAIL');
        wx.showToast({
          title: '获取砍价记录失败',
          icon: 'none',
        });
      });
  },

  FETCH_INIT_DETAIL({ commit, state }) {
    const { umpActivityId } = state.queryOptions || {};
    getSponsorNew({ activityId: umpActivityId }).then((sponsorDetail = {}) => {
      const { activity = {} } = sponsorDetail;
      commit('SET_GOODS_DETAIL', sponsorDetail);
      commit('SET_CREATOR', sponsorDetail);
      commit('SET_NEED_FOLLOW', sponsorDetail.needFollow);
      commit('SET_QUERY_OPTIONS', {
        ...state.queryOptions,
        sponsorId: sponsorDetail.id,
        alias: sponsorDetail.goodsAlias,
      });
      commit('SET_BARGAIN_DETAIL', sponsorDetail);
      commit('SET_EXTRA_DATA', activity.id);
    });
  },

  FETCH_CURRENT_STOCK({ commit, state }) {
    const { umpActivityId } = state.queryOptions || {};
    return getSponsorNew({ activityId: umpActivityId })
      .then((sponsorDetail) => {
        commit('SET_CURRENT_STOCK', sponsorDetail);
      })
      .catch((err) => {
        throw err;
      });
  },

  //  初始化砍价详情
  FETCH_BARGAIN_DETAIL({ commit, state, dispatch }) {
    const { sponsorId, umpActivityId } = state.queryOptions || {};
    const { isNewHelpCut, lastId, hasAuthorized } = state;
    commit('SET_LAST_RECORD');
    const api = isNewHelpCut ? getSponsorNew : getSponsor;
    const params = isNewHelpCut
      ? {
          sponsorId,
          activityId: umpActivityId,
          recordId: lastId,
        }
      : {
          sponsorId,
        };
    api(params)
      .then((sponsorDetail = {}) => {
        const {
          kdtPhoneNumber,
          initiatorInfo = {},
          activity = {},
        } = sponsorDetail;

        // 砍价页面埋点
        umpActivityLog(activity.umpActivityId, {
          act_type: '21',
          act_id: activity.umpActivityId,
        });

        commit('SET_GOODS_DETAIL', sponsorDetail);
        if (!isNewHelpCut) {
          commit('SET_SHOP_PHONE', kdtPhoneNumber);
          commit('SET_INITIATOR_INFO', initiatorInfo);
        } else {
          commit('SET_HAS_LOGIN', sponsorDetail.hasLogin);
          commit('SET_NEED_FOLLOW', sponsorDetail.needFollow);
          commit('SET_CREATOR', sponsorDetail);
          // 更新关键参数
          commit('SET_QUERY_OPTIONS', {
            ...state.queryOptions,
            sponsorId: sponsorDetail.id,
            alias: sponsorDetail.goodsAlias,
          });
          if (!state.isCreator && !sponsorDetail.needFollow && hasAuthorized) {
            dispatch('GET_HELPCUT_LIST');
            // 帮砍模式下进入帮砍流程
            dispatch('HANDLE_HELP_CUT');
          }
          // 推广页面无开团id 在node层处理帮砍列表数据
          sponsorDetail.bargainRecords &&
            commit('SET_BARGAIN_RECORDS', sponsorDetail.bargainRecords);
          // 设置已预约状态
          if (sponsorDetail.hadRemind) {
            commit('SET_FINISH_BOOK');
          }
        }
        commit('SET_BARGAIN_DETAIL', sponsorDetail);
        commit('SET_EXTRA_DATA', activity.id);
        commit('SET_SPONSOR_SUCCESS');
        dispatch('FETCH_RECOMMEND_GOODS', activity);
        dispatch('FETCH_BARGAIN_USER_LIST');
      })
      .catch(({ msg }) => {
        wx.showToast({
          duration: 3000,
          title: msg || '获取砍价详情数据失败',
          icon: 'none',
        });
      });
  },

  // 处理发起者购买流程
  HANDLE_PURCHASE({ state, commit }) {
    const { alias } = state.goodsDetail || {};
    skuApiPromise()
      .then(({ getSkuData }) => {
        return getSkuData(alias);
      })
      .then(parseSkuDetail)
      .then((skuData = {}) => {
        skuData = formatSkuTree(skuData);
        // 项目需求为 多sku弹起sku面板 单sku直接下单 目前为了快速解决这个问题 直接改成都弹起sku面板
        commit('SET_SKU_INFO', skuData);
      })
      .catch((err) => {
        // 有过jira，记录下
        app.logger.appError({
          name: 'set-sku-error',
          message: '获取商品详情失败',
          detail: err,
        });
        wx.showToast({
          title: '获取商品详情失败',
          icon: 'none',
        });
      });
  },

  // 处理下单操作
  HANDLE_BUY({ state }, event) {
    const { sponsorId } = state.queryOptions || {};
    const { id } = state.goodsDetail || {};
    const { umpActivityId, currentPrice, currentCanBuy } =
      state.bargainDetail || {};
    let messages = {};
    let skuId = 1;
    if (event) {
      messages = event.detail.messages;
      skuId = event.detail.selectedSkuComb.id;
    } else {
      skuId = state?.skuInfo?.goodsDetail?.skuId;
    }
    giveBuyRights({ sponsorId })
      .then((resp = {}) => {
        if (!resp.sponsorId) {
          wx.showToast({
            title: '发放优惠权益失败，请重试',
            icon: 'none',
          });
          return;
        }
        // 处理原价购买
        // 活动价购买参数
        const umpParams = {
          activityType: 21,
          activityId: umpActivityId,
          activityAlias: String(sponsorId),
        };
        skuTradeUtilsPromise().then(({ goToBuySingle }) => {
          goToBuySingle({
            ...((!state.isNewHelpCut || currentCanBuy) && umpParams),
            num: 1,
            price: Math.floor(parseFloat(currentPrice) * 100),
            messages,
            skuId,
            goodsId: id,
          });
        });
      })
      .catch((err) => {
        wx.showToast({
          title: err.msg || '发放优惠权益失败，请重试',
          icon: 'none',
        });
      });
  },

  // 帮助好友砍价
  HANDLE_HELP_CUT({ state, dispatch }, userInfo) {
    return new Promise((resolve) => {
      if (state.hasBehaviorReady) {
        resolve();
      } else {
        Event.on('behavior:ready', resolve);
      }
    })
      .then(() => {
        const kdtId = app.getKdtId();
        const { sponsorId } = state.queryOptions || {};
        const fansId = app.getToken('userId') || '';
        const buyerId = app.getBuyerId() || '';
        const bizData = JSON.stringify({
          kdtId,
          fansId,
          buyerId,
          sponsorId,
        });

        if (!fansId && !buyerId) {
          reportSkynet('帮砍获取 fansId、buyerId 失败', app.getToken());
        }

        import('@youzan/behavior-verify/src/miniapp/weapp/main').then(
          ({ default: behaviorVerify }) => {
            behaviorVerify({
              bizType: 2,
              bizData,
              onSuccess: (token) => {
                dispatch('HELPCUT_VERIFY', {
                  token,
                  sponsorId,
                  userInfo,
                });
              },
            });
          }
        );
      })
      .catch((err) => {
        wx.showToast({
          duration: 3000,
          title: err.msg || '获取行为组件失败',
          icon: 'none',
        });
      });
  },

  // 帮砍流程处理
  HELPCUT_VERIFY({ state, commit }, payload) {
    const { token, sponsorId, userInfo = {} } = payload || {};
    const { currentPrice } = state.bargainDetail || {};
    helpcutVerify({ sponsorId, token })
      .then((res) => {
        const { couponResult, cutPrice, rcmdItems = [] } = res;
        const countPrice = (Number(currentPrice) - Number(cutPrice)).toFixed(2);
        commit('SET_USER_CUT_PRICE', cutPrice);
        if (state.isNewHelpCut) {
          commit('SET_USER_CUT_DETAIL', res);
          commit('SET_SHOW_COUPON_POP', !!couponResult);
          commit('SET_COUPON_RESULT', couponResult);
          commit('SET_COUPON_GOODS', rcmdItems.slice(0, 3));
          return;
        }
        state.bargainRecords.push({
          avatar: userInfo.avatarUrl || DEFAULT_AVATAR,
          nickName: userInfo.nickName || DEFAULT_NICKNAME,
          cutPrice,
        });
        commit('SET_BARGAIN_RECORDS', { ...state.bargainRecords });
        commit('UPDATE_BARGAIN_DETAIL', {
          ...state.bargainDetail,
          canJoinCut: false,
          currentPrice: countPrice,
          isWholeStoreLimit: false,
        });
        if (couponResult) {
          commit('SET_SHOW_COUPON_POP', true);
          commit('SET_COUPON_RESULT', couponResult);
        } else {
          commit('SET_SHOW_CUT_SUCESS', true);
        }
      })
      .catch((err = {}) => {
        if (state.isNewHelpCut) {
          commit('SET_USER_CUT_DETAIL', err);
          return;
        }
        // 需关注后帮砍
        if (err.code === 160540353) {
          commit('SET_SHOW_FOLLOW_POP', true);
          return;
        }
        wx.showToast({
          title: err.msg || '助力砍价失败, 请刷新或重试',
        });
      });
  },

  GET_HELPCUT_LIST({ state, commit }) {
    const { umpActivityId } = state.queryOptions || {};
    getHelpcutList({ pageSize: 3, activityId: umpActivityId }).then((res) => {
      commit('SET_HELPCUT_LIST_NUMBER', res.list.length);
      commit('SET_HELPCUT_LIST', res.list);
    });
  },

  // 重新发起砍价
  RESTART_BARGAIN({ state }, payload) {
    const { queryOptions = {} } = state;
    const sponsor = payload ? { sponsorId: payload } : {};
    const helpcutUrl = Args.add('/packages/ump/helpcut/index', {
      kdtId: queryOptions.kdtId || queryOptions.kdt_id,
      umpAlias: queryOptions.umpAlias,
      umpActivityId: queryOptions.umpActivityId,
      shopAutoEnter: 1,
      ...sponsor,
    });
    Navigate.navigate({ url: helpcutUrl });
  },

  // 进店逛逛
  ENTER_SHOP() {
    Navigate.switchTab({
      url: '/packages/home/<USER>/index',
    });
  },

  // 进对应商详
  ENTER_GOODS_DETAIL({ state }, payload) {
    const { alias: goodsAlias, umpActivityId, umpAlias } = payload;
    const { queryOptions = {} } = state;
    // umpAlias取值有问题，当前接口未返回umpAlias，取了queryOptions中的umpAlias，即之前参与砍价活动的umpAlias
    const alias = umpAlias || queryOptions.umpAlias;
    if (umpActivityId) {
      Navigate.navigate({
        url: `/pages/goods/detail/index?type=helpCut&alias=${goodsAlias}&activityId=${umpActivityId}&umpAlias=${alias}&umpType=helpCut&shopAutoEnter=1`,
      });
    } else {
      Navigate.navigate({
        url: `/pages/goods/detail/index?alias=${goodsAlias}`,
      });
    }
  },

  // 优惠券跳转
  USE_COUPON({ state }, payload) {
    const { couponId, groupType } = payload;
    getCouponRedirectPath({
      couponId,
      groupType,
    }).then((res) => {
      const url = res.weappUrl;
      const isHome = res.isSwitchTab;
      if (isHome) {
        Navigate.switchTab({
          url,
        });
      } else {
        Navigate.navigate({
          url,
        });
      }
    });
  },
};

export default actions;
