import { moment as formatDate } from 'utils/time';

import {
  CREATOR_STAMP,
  SPONSOR_STAMP,
  FAIL_ACTION_MAP,
  ACTION_STATUS_ENUM,
  SUCCESS_ACTION_LIST,
  CREATOR_ACTION_LIST,
  SUCCESS_ACTION_MAP,
  FAIL_ACTION_COPYRIGHTING,
  CREATOR_ACTION_MAP,
  NEW_SHOW_ACTION_LIST,
  NEW_SHOW_POPUP_LIST,
  NEW_SHOW_HELPCUT_POPUP_LIST,
  POPUP_MAP,
} from '../common/constants';

const getters = {
  // 获取最底价金额，以分为单位
  minPriceYuan(state) {
    const { minPrice = '' } = state.bargainDetail || {};
    return minPrice.split('.')[0];
  },

  // 获取最底价金额，以元为单位
  minPriceFen(state) {
    const { minPrice = '' } = state.bargainDetail || {};
    return minPrice.split('.')[1];
  },

  // 获取砍价过期时间 or 开始时间
  expireTime(state, getters) {
    const { bargainExpireTime = '', activityStartTime = '' } =
      state.bargainDetail || {};
    if (state.isNewHelpCut && getters.activityStatus === 15) {
      return activityStartTime;
    }
    return bargainExpireTime;
  },

  // 获取当前砍价状态是否成功场景
  isActionSuccess(state, getters) {
    if (getters.activityStatus < 0) return false;
    return SUCCESS_ACTION_LIST.indexOf(getters.activityStatus) > -1;
  },

  // 获取砍价失败场景提示文案
  stampCopywriting(state, getters) {
    return FAIL_ACTION_COPYRIGHTING[getters.activityStatus] || '';
  },

  // 获取砍价发起/助力场景的文案
  sloganCopywriting(state, getters) {
    const isCreator = CREATOR_ACTION_LIST.indexOf(getters.activityStatus) > -1;
    return isCreator ? CREATOR_STAMP : SPONSOR_STAMP;
  },

  // 获取砍价帮砍进度
  bargainProgress(state) {
    const { originPrice } = state.goodsDetail || {};
    const { minPrice = '', currentPrice = '' } = state.bargainDetail || {};
    const bargainCount = Number(originPrice) - Number(currentPrice);
    return bargainCount / (Number(originPrice) - Number(minPrice));
  },

  // 获取最新帮砍信息
  newBargainInfo(state) {
    const { bargainRecords = {}, defaultAvatar } = state;
    const hasNewCut = bargainRecords.newCutPrice;
    const { newRecords = [] } = bargainRecords;
    const newShowList = newRecords.slice(0, 3);
    const newShowNicknameList = [];
    let newShowAvatarList = [];
    newShowList.forEach((item, i) => {
      newShowList[i].nickName = item.nickName || '神秘用户';
      newShowList[i].avatar = item.avatar || defaultAvatar;
      newShowNicknameList.push(newShowList[i].nickName);
      newShowAvatarList.push(newShowList[i].avatar);
    });
    let newShowNickname = newShowNicknameList.join('、');
    newShowAvatarList = newShowAvatarList.slice(0, 3);
    if (newRecords.length > 2) {
      newShowNickname =
        newShowNickname.slice(0, 9) + `…等${newRecords.length}名好友`;
    }

    if (newRecords.length > 3) {
      newShowAvatarList.push(
        'https://b.yzcdn.cn/public_files/bbffbe97f0aea2460466dd7177481bf7.png'
      );
    }
    // 头像折叠，第一个头像在上方，需要反转数组反向渲染DOM
    if (newRecords.length > 2) {
      newShowAvatarList.reverse();
    }

    return {
      hasNewCut,
      newCutNum: newRecords.length,
      newShowList,
      newShowNickname,
      newShowAvatarList,
    };
  },

  // 获取砍价当前状态提示
  actionTip(state, getters) {
    const { originPrice } = state.goodsDetail || {};
    const { currentPrice, totalNum, cutedNum, minPrice } =
      state.bargainDetail || {};
    if (getters.activityStatus < 0) return '';

    if (getters.isActionSuccess) {
      const actionInfo = SUCCESS_ACTION_MAP[getters.activityStatus] || {};
      return actionInfo.getTip({
        totalNum,
        cutedNum,
        minPrice,
        originPrice,
        currentPrice,
      });
    }
    return '';
  },

  // 获取砍价成功/失败场景按钮组
  actionBtnGroup(state, getters) {
    const { alias } = state.goodsDetail || {};
    const { orderNo, activityId, currentPrice } = state.bargainDetail || {};
    if (getters.activityStatus < 0) return [];

    if (getters.isActionSuccess) {
      const actionInfo = SUCCESS_ACTION_MAP[getters.activityStatus] || {};
      return actionInfo.getBtnGroup({ currentPrice });
    }
    const actionInfo = FAIL_ACTION_MAP[getters.activityStatus] || {};
    return actionInfo.getBtnGroup({ orderNo, alias, activityId });
  },

  // 计算砍价状态
  activityStatus(state) {
    const {
      activityStartTime,
      activityEndTime,
      bargainExpireTime,
      isCreator,
      activityStock,
      canJoinCut,
      minPrice,
      currentPrice,
      isPurchased,
      needMinPriceBuy,
      isWholeStoreLimit,
      bargainDetailSeted,
      needFollow,
    } = state.bargainDetail || {};

    const { hasLogin, isCreator: creator } = state;

    if (!bargainDetailSeted) return -1;

    const currentDate = new Date().getTime();

    if (!hasLogin || needFollow) {
      if (creator) {
        return ACTION_STATUS_ENUM.creatorNeedLogin;
      }
      return ACTION_STATUS_ENUM.sponsorNeedLogin;
    }
    if (isCreator) {
      // 砍价发起者状态
      if (isPurchased) {
        return ACTION_STATUS_ENUM.creatorHasPurchased;
      }
      if (activityStock <= 0) {
        return ACTION_STATUS_ENUM.creatorSoldOut;
      }
      if (activityStartTime > currentDate) {
        return ACTION_STATUS_ENUM.creatorReserve;
      }
      if (bargainExpireTime <= currentDate) {
        // 砍价进程过期与砍价活动结束同时过期, 优先显示砍价活动结束(因为砍价活动过期不可以"重新发起砍价")
        if (activityEndTime <= currentDate) {
          return ACTION_STATUS_ENUM.creatorActivityFinished;
        }
        return ACTION_STATUS_ENUM.creatorCutExpired;
      }
      if (currentPrice === minPrice) {
        return ACTION_STATUS_ENUM.creatorCutMinPrice;
      }
      if (!needMinPriceBuy) {
        return ACTION_STATUS_ENUM.creatorOngoing;
      }
      return ACTION_STATUS_ENUM.creatorNeedButNoMinBuy;
    }
    if (activityStock <= 0) {
      // 砍价帮砍者状态
      return ACTION_STATUS_ENUM.sponsorSoldOut;
    }
    if (isPurchased || bargainExpireTime <= currentDate) {
      if (activityEndTime <= currentDate) {
        // 砍价进程过期与砍价活动结束同时过期, 优先显示砍价活动结束(因为砍价活动过期不可以"重新发起砍价")
        return ACTION_STATUS_ENUM.sponsorActivityFinished;
      }
      return ACTION_STATUS_ENUM.sponsorCutFinished;
    }
    if (canJoinCut) {
      return ACTION_STATUS_ENUM.sponsorCanCut;
    }
    if (currentPrice === minPrice) {
      return ACTION_STATUS_ENUM.sponsorHasMinPrice;
    }
    if (isWholeStoreLimit) {
      return ACTION_STATUS_ENUM.sponsorActivityNoChance;
    }
    return ACTION_STATUS_ENUM.sponsorStoreNoChance;
  },

  rules(state) {
    const { shopInfo = {}, bargainDetail = {} } = state;

    return [
      `1.活动期间消费者邀请好友帮忙砍价，邀请${bargainDetail.totalNum}名好友砍到底价￥${bargainDetail.minPrice}后，即可按￥${bargainDetail.minPrice}购买商品；`,
      `2.好友帮忙砍到最低价¥${bargainDetail.minPrice}后，消费者须在砍价有效期内购买砍价商品，逾期商品将恢复原价；`,
      `3.同一商品，同一用户仅可享受${
        bargainDetail.startNum || '一'
      }次优惠价格;`,
      `4.同一商品，同一用户仅能帮好友砍价${bargainDetail.joinNum || '一'}次；`,
      '5.砍价商品数量有限。商品售罄后，商品将无法购买，即使你已经发起砍价；',
      `6.若系统判断用户为恶意刷单，${shopInfo.name}针对已下单的商品有权不予发货；对于刷单金额较大或行为较为恶劣者，${shopInfo.name}有权追究用户的法律责任。`,
    ];
  },

  // 新版获取能展示在活动页面上的场景
  isShowAction(state, getters) {
    if (getters.activityStatus < 0) return false;
    return NEW_SHOW_ACTION_LIST.indexOf(getters.activityStatus) > -1;
  },

  newBargainRecords(state) {
    const { records = [] } = state.bargainRecords;
    records.forEach((item) => {
      item.extra =
        state.goodsDetail.yzUid === item.yzUid
          ? '使用狂砍一刀道具'
          : formatDate(item.createdAt, 'YYYY-MM-DD HH:mm:ss');
    });
    return {
      ...state.bargainRecords,
      records,
    };
  },

  // 新版获取砍价当前状态提示
  newActionTip(state, getters) {
    const {
      cutedPrice,
      leastPrice,
      cutedNum,
      minPrice,
      currentCanBuy,
      originalPrice,
    } = state.bargainDetail || {};
    if (getters.activityStatus < 0) return '';

    if (getters.isShowAction) {
      const actionInfo = CREATOR_ACTION_MAP[getters.activityStatus] || {};
      return actionInfo.getTip({
        cutedPrice,
        cutedNum,
        minPrice,
        leastPrice,
        currentCanBuy,
        originalPrice,
      });
    }
    return {};
  },

  // 新版获取砍价成功/失败场景按钮组
  newActionBtn(state, getters) {
    const { currentCanBuy, minPrice, currentPrice, leastCutNum, helpCutLimit } =
      state.bargainDetail || {};
    if (getters.activityStatus < 0) return {};
    const actionInfo = CREATOR_ACTION_MAP[getters.activityStatus] || {};

    if (getters.isShowAction) {
      if (!actionInfo.getBtn) return null;
      return actionInfo.getBtn({
        currentCanBuy,
        minPrice,
        currentPrice,
        leastCutNum,
        finishBook: state.finishBook,
        canRestartBargain: !helpCutLimit,
      });
    }
    return {};
  },

  // 新版获取活动展示页面extra信息
  newActionExtra(state, getters) {
    const { currentCanBuy, hasNewCut: hasNewCutInDetail } =
      state.bargainDetail || {};
    const { hasNewCut: hasNewCutInRecords } = state.bargainRecords || {};
    const hasNewCut = hasNewCutInDetail || hasNewCutInRecords;
    if (getters.activityStatus < 0) return [];

    if (getters.isShowAction) {
      const actionInfo = CREATOR_ACTION_MAP[getters.activityStatus] || {};
      const extraInfo = {
        needProcess: actionInfo.needProcess || false,
        needCutDown: actionInfo.needCutDown || false,
        cutDownText: actionInfo.cutDownText || '',
        showRecommend: actionInfo.showRecommend || false,
        needPopup: actionInfo.needPopup && actionInfo.needPopup(hasNewCut),
        needStartBtn:
          actionInfo.needStartBtn && actionInfo.needStartBtn(currentCanBuy),
        needCurrentBuy:
          actionInfo.needCurrentBuy && actionInfo.needCurrentBuy(currentCanBuy),
      };
      return extraInfo;
    }
    return {};
  },

  // 新版获取展示发起者弹框的场景
  isShowPopup(state, getters) {
    if (getters.activityStatus < 0) return false;
    return NEW_SHOW_POPUP_LIST.indexOf(getters.activityStatus) > -1;
  },

  // 新版获取能展示帮砍红包弹框的场景
  isShowHelpcutPopup(state, getters) {
    if (getters.activityStatus < 0) return false;
    return NEW_SHOW_HELPCUT_POPUP_LIST.indexOf(getters.activityStatus) > -1;
  },

  popupOptions(state, getters) {
    const { hasLogin, needFollow, isCreator, hasAuthorized } = state;
    if (!hasLogin || needFollow || !hasAuthorized) {
      const { minPrice, originalPrice } = state.bargainDetail || {};
      return {
        type: 'initPopup',
        hasTitle: !isCreator,
        title: '求求你帮我砍一刀',
        showInitTitle: true,
        initTitle: isCreator ? '' : `这个商品我想${+minPrice}元拿`,
        originalPrice: +originalPrice,
        minPrice: +minPrice,
        showInitGoods: true,
        showInitBtn: true,
        initBtnText: isCreator ? `点击${minPrice}元拿` : '帮他砍一刀',
      };
    }
    if (getters.activityStatus < 0) return {};
    const popupInfo = POPUP_MAP[getters.activityStatus] || {};
    if (getters.isShowHelpcutPopup) {
      return {
        type: popupInfo.type,
        showRedPacket: popupInfo.showRedPacket,
      };
    }
    if (getters.isShowPopup) {
      const {
        cutedPrice,
        leastPrice,
        // cutedNum,
        minPrice,
        currentCanBuy,
        currentPrice,
        originalPrice,
      } = state.bargainDetail || {};
      const content =
        popupInfo.content &&
        popupInfo.content({
          currentCanBuy,
          cutedPrice,
          leastPrice,
          originalPrice,
        });
      const btn =
        popupInfo.btn &&
        popupInfo.btn({
          currentCanBuy,
          currentPrice,
          minPrice,
        });
      const extraBtn =
        popupInfo.extraBtn &&
        popupInfo.extraBtn({
          currentCanBuy,
        });
      const viceBtn = popupInfo.viceBtn && popupInfo.viceBtn();
      const showRecommendBottom =
        popupInfo.showRecommendBottom &&
        popupInfo.showRecommendBottom({ currentCanBuy });
      return {
        type: popupInfo.type,
        needGoodsImg: popupInfo.needGoodsImg,
        status: (content && content.status) || '',
        tip: (content && content.tip) || null,
        btn: btn || null,
        extraBtn: extraBtn || null,
        viceBtn: viceBtn || null,
        needProcess: popupInfo.needProcess,
        showRecommendBottom: showRecommendBottom || false,
        showRecommendTop: popupInfo.showRecommendTop || false,
      };
    }
    return {};
  },

  helpPopupOptions(state) {
    const {
      isCreator,
      allHelpcutListNumber,
      allHelpcutList,
      showCouponPop,
      couponResult,
      userCutPrice,
      userCutDetail,
      hasAuthorized,
    } = state;
    // 无帮砍数据 -- 安全校验滑块出现的时候场景
    const isNoCutData = JSON.stringify(userCutDetail) === '{}';
    if (isCreator || !hasAuthorized || isNoCutData) {
      return {};
    }
    const hasCoupon = showCouponPop;
    const isFrameless = allHelpcutListNumber || hasCoupon;
    const type = isFrameless ? 'frameless' : 'frame';
    const cutPrice = userCutPrice && Number(userCutPrice).toFixed(2);
    const title = cutPrice
      ? `谢谢你帮我砍了${cutPrice}元！`
      : userCutDetail.msg || '来晚了，助力活动已结束';
    let cutTitle = '更多好物低价抢！';
    let cutBtn = '选我';
    if (allHelpcutListNumber === 1) {
      const goodsDetail = allHelpcutList[0];
      const minPrice = Number(goodsDetail.minPrice / 100 || 0).toFixed(2);
      cutTitle = `限时商品${minPrice}元拿！`;
      cutBtn = `立即${minPrice}元拿`;
    }
    const showHelpcutRecommend = !hasCoupon;

    const params = {
      type,
      showRecommend: true,
      showHelpcutRecommend,
      hasTitle: isFrameless,
      hasCoupon,
      title,
      cutTitle,
      allHelpcutList,
      couponResult,
      cutBtn,
    };
    if (!isFrameless) {
      params.viceBtn = {
        content: '进店逛逛',
        event: 'shop',
      };
    }
    return params;
  },
};

export default getters;
