.activity-records {
  font-size:14px;
  margin: 20px 15px;

  .loading {
    width: 24px;
    margin: 0 auto;
    display: block;
  }

  .title {
    color:#f72a3e;
    text-align: center;
    margin-bottom: 20px;
  }

  .tab {
    background: linear-gradient(to bottom ,#bf2b2a,#bf412a);
    text-align: center;
    height: 16px;
    border-radius: 8px;
  }

  .content{
    display: flex;
    flex-direction: column;
    padding:20px 15px 15px 15px;
    margin: -8px 10px 0 10px;
    background: #fff;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
  }

  .record{
    display: flex;
    height: 30px;
    line-height: 30px;
    margin-bottom: 9px;
    flex-shrink: 0;
  }

  .profile-image{
    width: 30px;
    flex-shrink: 0;
    height: 30px;
    background: gray;
    border-radius: 15px;
  }

  .sponsor-name{
    margin-left: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 20px;
  }

  .bargain-count{
    flex-grow: 1;
    flex-shrink: 0;
    text-align: right;
  }

  .show-all-button{
    color:#f72a3e;
    text-align: center;
  }

  .records-container{
    display: flex;
    flex-direction: column;
    overflow:hidden;
    max-height: 99999px;
    transition: max-height 0.25s;
  }

  .collapse{
    max-height: 115px;
  }

  .empty-records{
    font-size: 12px;
    text-align: center;
    color:#999;
  }

  .icon-arrow{
    display: inline-block;
    height: 12px;
    width: 12px;
    background-image: url("https://img01.yzcdn.cn/wsc/20180823/images/arrow.png");
    background-size: contain;
    background-repeat: no-repeat;
    transition: 0.25s;
    background-position: center;
  }

  .icon-arrow-up{
    transform: rotate(180deg);
  }

  .cut-price {
    color: #f72a3e;
  }
}