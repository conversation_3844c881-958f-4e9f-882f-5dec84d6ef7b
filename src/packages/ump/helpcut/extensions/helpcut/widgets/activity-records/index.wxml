<view wx:if="{{!fetchRecordFail}}" class="activity-records" >
  <van-loading wx:if="{{ fetchRecordIng }}" size="24px" class="loading" />
  <block wx:else>
    <view class="tab" />
    <view class="content">
      <view class="title">| 砍价记录 |</view>
      <view class="records-container {{ isCollapse ? 'collapse' : '' }}">
        <view wx:if="{{ bargainRecords.length === 0 }}" class="empty-records">
          暂无砍价记录
        </view>
        <block wx:else>
          <view
            wx:for="{{ bargainRecords }}"
            wx:for-item="record"
            wx:key="{{ record.nickName }}"
            class="record"
          >
            <image
              src="{{ record.avatar || defaultProfileSrc }}"
              class="profile-image"
              mode="aspect"
            >
            </image>
            <text class="sponsor-name">{{ record.nickName || defaultUserName }}</text>
            <text class="bargain-count">
              帮砍 <text class="cut-price">¥{{ record.cutPrice }}</text>
            </text>
          </view>
        </block>
      </view>
      <view
        wx:if="{{ bargainRecords.length > 3 }}"
        class="show-all-button"
        bindtap="toggleCollapse"
      >
        {{ isCollapse ? '查看全部' : '收起'}}
        <text class="icon-arrow {{ isCollapse ? '' : 'icon-arrow-up' }}"></text>
      </view>
    </view>
  </block>
</view>
