/* eslint-disable @youzan-open/tee/no-platform-field */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import { fetchUltraCodePost } from 'shared/utils/qrcode';
import authorize from 'shared/utils/authorize';
import { fetchPoster } from '../../common/api';
import { BARGAIN_PATH } from '../../common/constants';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';

RantaWidget({
  data: {
    showPoster: false,
    posterUrl: '',
    actions: [
      { name: '邀请好友帮砍', type: 'native', openType: 'share' },
      { name: '生成邀请海报', type: 'poster' },
    ],
  },

  attached() {
    mapState(this, [
      'showActionSheet',
      'bargainDetail',
      'initiatorInfo',
      'queryOptions',
      'goodsDetail',
      'isNewHelpCut',
    ]);
    mapActions(this, ['SET_SHOW_ACTION_SHEET']);
  },

  methods: {
    // 选择菜单
    onSelect(e) {
      const { type } = e.detail;
      if (type === 'native') return;

      this.generateQrCode();
    },

    // 关闭邀请菜单弹窗
    onCancel() {
      this.SET_SHOW_ACTION_SHEET(false);
    },

    // 关闭海报
    onClose() {
      this.setYZData({
        showPoster: false,
      });
    },

    // 设置生成海报
    generateQrCode() {
      if (this.data.posterUrl) {
        this.setYZData({
          showPoster: true,
        });
        this.onCancel();
        return;
      }
      const { sponsorId } = this.data.queryOptions || {};
      const { umpActivityId, minPrice, umpAlias } =
        this.data.bargainDetail || {};
      const { avatar, nickName } = this.data.initiatorInfo || {};
      wx.showLoading({ title: '海报生成中...' });

      fetchUltraCodePost(BARGAIN_PATH, {
        ...defaultEnterShopOptions,
        sponsorId,
        umpAlias,
        umpActivityId,
      })
        .then((qrcode) => {
          const data = {
            qrcode,
            avatar,
            nickName,
            minPrice,
            goodsDetail: this.data.goodsDetail,
          };
          fetchPoster(data)
            .then((res) => {
              wx.hideLoading();
              this.setYZData({
                posterUrl: res.img,
                showPoster: true,
              });
              this.onCancel();
            })
            .catch(() => {
              wx.hideLoading();
              wx.showToast({
                title: '生成海报失败',
                icon: 'none',
              });
            });
        })
        .catch(() => {
          wx.hideLoading();
          wx.showToast({
            title: '生成海报失败',
            icon: 'none',
          });
        });
    },

    // 保存海报
    savePoster() {
      const { posterUrl } = this.data;
      if (!posterUrl) {
        return;
      }

      wx.showLoading({ title: '保存中' });
      authorize('scope.writePhotosAlbum')
        .then(() => {
          this.saveShareImage(posterUrl)
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000,
              });
              this.onClose();
            })
            .catch((err) => {
              console.log(err);
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000,
              });
            });
        })
        .catch(() => {
          wx.hideLoading();
          wx.showModal({
            content: '需要同意将分享图片保存到相册，点击确定后跳转至设置页操作',
            success: (e) => {
              if (e.cancel) return;
              wx.openSetting({
                success: ({ authSetting }) => {
                  if (authSetting['scope.writePhotosAlbum']) {
                    this.savePoster();
                  }
                },
              });
            },
          });
        });
    },

    // 保存图片 api 调用 saveImageToPhotosAlbum只支持本地文件或微信临时路径
    saveShareImage(url) {
      const app = getApp();
      return new Promise((resolve, reject) => {
        app.downloadFile({
          url,
          success(res) {
            if (res.statusCode === 200) {
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: resolve,
                fail: reject,
              });
            }
          },
          fail: reject,
        });
      });
    },
  },
});
