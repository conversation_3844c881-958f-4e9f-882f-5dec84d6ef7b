<view>
  <van-action-sheet
    show="{{ showActionSheet }}"
    actions="{{ actions }}"
    cancel-text="取消"
    bind:cancel="onCancel"
    bind:click-overlay="onCancel"
    bind:select="onSelect"
  />
  <!-- 海报弹层 -->

  <van-popup
    show="{{ showPoster }}"
    custom-class="invite-pop"
  >
    <view
      bindtap="onClose"
      class="invite-pop-close"
    >
      <van-icon name="close" slot="close-icon"></van-icon>
    </view>
    <view class="invite-pop-content">
      <image
        class="invite-pop-content__img"
        src="{{ posterUrl }}"
      >
      </image>
    </view>
    <view
      class="invite-pop-btn--bottom"
      bind:tap="savePoster"
    >
      保存图片
    </view>
  </van-popup>
</view>
