<view class="helpcut-block">
  <view
    class="activity-status"
    style="padding-top: {{ newActionExtra.needCutDown ? '46' : '24' }}px"
  >
    <!-- 倒计时 -->
    <view wx:if="{{ newActionExtra.needCutDown }}" class="cutdown">
      <text>{{remainTime.before}}</text>
      <text class="seconds {{ remainTime.hasMilliseconds ? 'hasMilliseconds' : '' }}">{{remainTime.second}}</text>
      <text class="cutdown-after" >{{remainTime.after}}</text>
    </view>
    <view wx:if="{{ newActionTip.status }}" class="status">
      {{ newActionTip.status }}
    </view>
    <view wx:if="{{ newActionTip.tip }}">
      <view wx:if="{{ !newActionTip.needWrap }}" class="tip">
        <view wx:for="{{ newActionTip.tip }}" class="{{ item.type }}">{{ item.content }}</view>
      </view>
      <view wx:else>
        <view class="tip">
          <view wx:for="{{ util.itemsFilter(newActionTip.tip,0,3) }}" class="{{ item.type }}">
            {{ item.content }}
          </view>
        </view>
        <view class="tip">
          <view wx:for="{{ util.itemsFilter(newActionTip.tip,3,5) }}" class="{{ item.type }}">
            {{ item.content }}
          </view>
        </view>
      </view>
    </view>
    <!-- 进度条 -->
    <view wx:if="{{ newActionExtra.needProcess }}" class="process">
      <view id="process-bar" class="block">
        <view class="bar" style="width: {{ bargainProgressWidth * 100 }}%" />
      </view>
    </view>
    <!-- 推荐商品 -->
    <goods-recommend wx:if="{{ newActionExtra.showRecommend }}" margin-top need-btn list="{{ recommendGoodsThree }}" />
    <!-- 帮砍按钮 -->
    <view
      wx:if="{{ newActionBtn }}"
      class="helpcut-button {{ (finishBook || newActionBtn.disable) ? 'not-click' : ''}}"
      bind:tap="handleClickBtn"
    >
      {{ newActionBtn.content }}
    </view>
    <view wx:if="{{ newActionExtra.needStartBtn }}" class="restart-button" bind:tap="handleRestartBargain">
      重新发起砍价 >
    </view>
    <view wx:if="{{ newActionExtra.needCurrentBuy }}" class="restart-button" bind:tap="handleCurrentBuy">
      不砍了，现价 <text class="">{{ bargainDetail.currentPrice }}</text> 元购买 <van-icon class="restart-button-icon" name="arrow" size="14px"/>
    </view>
  </view>
</view>


<wxs module="util">
  module.exports.itemsFilter = function(list,start,end) {
    return list.slice(start, end);
  }
</wxs>