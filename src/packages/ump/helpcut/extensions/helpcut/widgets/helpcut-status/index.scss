@import '~shared/common/css/animation.scss';

.helpcut-block {
  padding-bottom: 16px;
}

.activity-status {
  position: relative;
  background-color: #fff;
  border-radius: 16px;
  margin: 0 16px;
  padding: 32px 26px 24px;
  text-align: center;
  font-size: 18px;
  // font-weight: bolder;

  .cutdown {
    position: absolute;
    top: 0;
    left: 50%;
    margin-left: -65px;
    min-width: 130px;
    height: 30px;
    padding: 0 6px;
    box-sizing: border-box;
    line-height: 30px;
    background-color: #ffefdb;
    color: #ff1c00;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    font-size: 12px;
    font-weight: bolder;
    .seconds {
      width: 18px;
      display: inline-block;
      text-align: left;
    }
    .hasMilliseconds {
      width: 28px;
    }
    .cutdown-after {
      font-weight: normal;
    }
  }

  .status {
    line-height: 24px;
    text-align: center;
    font-weight: bolder;
    margin-bottom: 10px;
  }

  .tip {
    display: flex;
    justify-content: center;
    align-items: baseline;
    line-height: 30px;
    font-weight: bolder;
    .price {
      color: #ff1c00;
    }

    .priceBig {
      font-size: 24px;
      color: #ff1c00;
    }
  }

  .process {
    position: relative;
    padding: 12px 16px 0;

    .block {
      width: 100%;
      height: 12px;
      background-color: rgba($color: #ff1c00, $alpha: 0.1);
      border-radius: 6px;
    }

    .bar {
      width: 0;
      will-change: width;
      height: 12px;
      border-radius: 6px;
      background-image: linear-gradient(90deg, #ff8947 1%, #f7351d 99%);
      transition: width 1s ease;
    }
  }
}

.helpcut-button {
  position: relative;
  margin: auto;
  margin-top: 32px;
  width: 290px;
  height: 44px;
  line-height: 44px;
  background-image: linear-gradient(
    90deg,
    rgba(255, 137, 71, 1) 1%,
    #f7351d 99%
  );
  border-radius: 22px;
  font-family: PingFangSC-Semibold;
  font-size: 16px;
  color: #fff;
  text-align: center;
  font-weight: bolder;
}
.not-click {
  opacity: 0.5;
  pointer-events: none;
}

.restart-button {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #ff1c00;
  text-align: center;
  line-height: 32px;
  margin-top: 20px;
  font-weight: bolder;

  > text {
    font-size: 18px;
  }
}
