/* eslint-disable @youzan-open/tee/no-platform-field */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapActions, mapState } from '@ranta/store';
import CountDown from 'utils/countdown';
import { subscribeMessage } from '../../common/subscribe';
import { ACTION_STATUS_ENUM } from '../../common/constants';
import { setMsgPush } from '../../common/api';
import { sendLogger } from '../../common/utils';

RantaWidget({
  data: {
    bargainProgressWidth: 0,
  },

  attached() {
    mapState(this, [
      'bargainDetail',
      'goodsDetail',
      'finishBook',
      'recommendGoodsThree',
      'queryOptions',
      'currentStock',
    ]);
    mapState(this, [
      'isShowAction',
      'bargainProgress',
      'newActionTip',
      'newActionBtn',
      'newActionExtra',
      'expireTime',
      'activityStatus',
    ]);
    mapActions(this, ['SET_SHOW_ACTION_SHEET', 'SET_FINISH_BOOK']);
    mapActions(this, [
      'HANDLE_PURCHASE',
      'HANDLE_HELP_CUT',
      'RESTART_BARGAIN',
      'FETCH_CURRENT_STOCK',
    ]);
  },

  observers: {
    expireTime(newTime) {
      if (newTime && newTime - Date.now().valueOf() > 0) {
        this.setCountDown(newTime);
      }
    },
  },

  ready() {
    const { expireTime, newActionBtn = {} } = this.data;
    if (expireTime && expireTime - Date.now().valueOf() > 0) {
      if (this.data.expireTime - Date.now().valueOf() > 0) {
        this.setCountDown(this.data.expireTime);
      }
    }
    setTimeout(() => {
      this.setYZData({
        bargainProgressWidth: this.data.bargainProgress,
      });
    }, 1000);
    const { event } = newActionBtn;
    if (event) {
      let params = {};
      switch (event) {
        case 'restart':
          params = {
            et: 'view',
            ei: 'view_cut',
            en: '发起砍价按钮曝光',
          };
          break;
        case 'reserve':
          params = {
            et: 'view',
            ei: 'view_order',
            en: '立即预约按钮曝光',
          };
          break;
        case 'invite':
          params = {
            et: 'view',
            ei: 'view_join',
            en: '邀请好友按钮曝光',
          };
          break;
        case 'buy':
          params = {
            et: 'view',
            ei: 'view_purchase',
            en: '购买下单按钮曝光',
          };
          break;
      }
      sendLogger(params);
    }
  },

  methods: {
    // 设置倒计时
    setCountDown(time) {
      const { newActionExtra = {} } = this.data;
      const countdownInstance = new CountDown(time - Date.now().valueOf(), {
        onChange: (timeData) => {
          const { day, hour, minute, second, hundredMilliseconds } = timeData;
          let remainTime = {};
          if (this?.activityStatus === ACTION_STATUS_ENUM.creatorReserve) {
            // 未开始状态--展示开始倒计时
            remainTime = {
              before: `${day ? day + '天' : ''}${hour}:${minute}:`,
              second,
              after: newActionExtra.cutDownText,
            };
          } else {
            // 开始状态--展示结束倒计时
            remainTime = {
              before: `帮砍${hour + day * 24}:${minute}:`,
              second: `${second}.${hundredMilliseconds}`,
              after: newActionExtra.cutDownText,
              hasMilliseconds: true,
            };
          }
          this.setYZData({ remainTime });
        },
        onEnd: () => {
          // 活动倒计时结束，更新状态 重新进入活动页面
          const { sponsorId } = this.queryOptions || {};
          this.RESTART_BARGAIN(sponsorId);
        },
        timeout: 100,
      });
      this.countdownInstance = countdownInstance;
    },
    handleBtnFn() {
      const type = this.data.newActionBtn?.event;
      const btnFnMap = {
        // 预约提醒
        reserve: () => {
          sendLogger({
            et: 'click',
            ei: 'click_order',
            en: '点击立即预约按钮',
          });
          this.bookHelpcut();
        },
        // 分享
        invite: () => {
          sendLogger({
            et: 'click',
            ei: 'click_join',
            en: '点击邀请好友按钮',
          });
          this.SET_SHOW_ACTION_SHEET(true);
        },
        // 下单
        buy: () => {
          sendLogger({
            et: 'click',
            ei: 'click_purchase',
            en: '点击购买下单按钮',
          });
          this.HANDLE_PURCHASE();
        },
        // 重新发起砍价
        restart: () => {
          sendLogger({
            et: 'click',
            ei: 'click_cut',
            en: '点击发起砍价按钮',
          });
          this.RESTART_BARGAIN();
        },
      };
      btnFnMap[type]?.();
    },
    // 砍价按钮行为处理
    handleClickBtn() {
      this.FETCH_CURRENT_STOCK()
        .then(() => {
          const { currentStock } = this;
          // 剩余库存不足时 中断砍价流程 告知用户
          if (currentStock === 0) {
            return wx.showToast({
              title: '亲，活动太火爆，库存已耗完，请联系商家补库存',
              icon: 'none',
            });
          }
          this.handleBtnFn();
        })
        .catch(() => {
          this.handleBtnFn();
        });
    },
    // 重新发起砍价
    handleRestartBargain() {
      this.RESTART_BARGAIN();
    },
    // 现价购买
    handleCurrentBuy() {
      this.HANDLE_PURCHASE();
    },
    // 预约提醒
    bookHelpcut() {
      subscribeMessage({
        scene: 'umpHelpCutRemind',
        successCallBack: () => {
          const setMsgPushparams = {
            activityId: this.data.bargainDetail?.activityId,
            bizId: 6, // 业务标识
            subBizId: 0, // 业务子标识
            isOpen: 1,
          };
          setMsgPush(setMsgPushparams)
            .then(({ value = false } = {}) => {
              if (value) {
                wx.showToast({
                  title: '订阅消息授权成功',
                  icon: 'none',
                });
                // 订阅成功后，更新按钮状态
                this.SET_FINISH_BOOK();
              } else {
                wx.showToast({
                  title: '订阅消息授权失败，请重试',
                  icon: 'none',
                });
              }
            })
            .catch(() => {
              wx.showToast({
                title: '订阅消息授权失败，请重试',
                icon: 'none',
              });
            });
        },
        failCallBack: () => {
          wx.showToast({
            title: '订阅消息授权失败，请重试',
            icon: 'none',
          });
        },
      });
    },
  },

  // 关闭倒计时
  detached() {
    this.countdownInstance && this.countdownInstance.stop();
  },
});
