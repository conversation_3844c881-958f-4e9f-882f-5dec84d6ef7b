import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';

RantaWidget({
  attached() {
    mapState(this, [
      'showCutSuccess',
      'bargainDetail',
      'goodsDetail',
      'userCutPrice',
    ]);
    mapActions(this, ['SET_SHOW_CUT_SUCESS']);
  },

  methods: {
    onCreateNewBargain() {
      const { alias } = this.data.goodsDetail || {};
      const { activityId } = this.data.bargainDetail || {};
      // eslint-disable-next-line @youzan/dmc/wx-check, @youzan-open/tee/no-platform-field
      wx.redirectTo({
        url: `/pages/goods/detail/index?alias=${alias}&type=helpcut&activityId=${activityId}`,
      });
    },

    closeCutPop() {
      this.SET_SHOW_CUT_SUCESS();
    },
  },
});
