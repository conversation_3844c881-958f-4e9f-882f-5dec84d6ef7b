.cut-success-pop {
  background-color: transparent !important;

  .cut-success-wrapper {
    display: flex;
    flex-direction: column;

    .coupon-wrapper {
      width: 80vw;
      background: linear-gradient(to left bottom, #F65A1C, #F72A3E);
      font-size: 14px;
      padding: 36px 20px 30px 20px;
      display: flex;
      flex-direction: column;
      text-align: center;
      border-radius: 5px;
      box-shadow: inset 0 0 30px #9C0A0A, 0 2px 4px #000;

      .gift-image {
        background-image: url("https://b.yzcdn.cn/wsc-minapp/bargain-detail/home/<USER>/gift.png");
        width: 196px;
        height: 90px;
        background-size: contain;
        background-repeat: no-repeat;
        margin-bottom: 20px;
        align-self: center;
      }

      .coupon-tip {
          color: #fff;

        .coupon-price {
          color: #fff;
          font-size: 24px;
          font-weight: 600;
        }
      }

      .gift-tip {
        color: #fff;
        margin-top: 15px;
      }

      .crete-cut-btn {
        font-size: 14px;
        color: #911414;
        border-radius: 20px;
        margin: 30px 0 0 0;
        background: #FBC31B;
        height: 40px;
        line-height: 40px;
      }
    }

    .cut-close {
      margin-top: 14px;
      color: #FFF;
      font-size: 24px;
      align-self: center;
    }
  }
}