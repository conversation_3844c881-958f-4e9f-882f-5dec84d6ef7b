<view>
  <van-popup
    show="{{ showCutSuccess }}"
    bind:close="closeCutPop"
    custom-class="cut-success-pop"
  >
    <view class="cut-success-wrapper" >
      <view class="coupon-wrapper" >
        <image class="gift-image" />
        <view class="coupon-tip" >老铁稳，一刀帮我砍了
          <text class="coupon-price">{{ userCutPrice }}</text>元
        </view>
        <view class="gift-tip" >好货不容错过，喜欢就去发起砍价吧</view >
        <button
          class="crete-cut-btn"
          bindtap="onCreateNewBargain"
        >
          我也要{{ bargainDetail.minPrice }}元拿
        </button >
      </view >
      <van-icon
        bindclick="closeCutPop"
        name="close"
        class="cut-close"
      />
    </view >
  </van-popup>
</view>