import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';

RantaWidget({
  data: {
    show: false,
  },

  attached() {
    mapState(this, ['goodsDetail', 'queryOptions']);
    mapActions(this, ['ENTER_GOODS_DETAIL']);
  },

  methods: {
    gotoGoodsDetail() {
      this.ENTER_GOODS_DETAIL({
        ...this.data.goodsDetail,
        umpActivityId: this.data.queryOptions.umpActivityId,
      });
    },
  },
});
