<!-- 砍价成功状态 -->
<view
  wx:if="{{ isActionSuccess }}"
  class="activity-status-success"
>
  <view  class="success-container">
    <view class="tentacle-left" />
    <view class="tentacle-right" />
    <rich-text class="{{activityStatus===2?'action-tip':'action-tip-nosuccess'}}" nodes="{{actionTip}}"></rich-text>
    <!-- 进度条 -->
    <view class="total-wrapper">
      <view class="total-bar">
        <view class="solid-bar" style="width: {{ bargainProgress * 100 }}%" />
        <view class="end-point {{ bargainProgress > 0 ? 'patch-negate-1px' : '' }}" />
      </view>
    </view>
    <!-- 倒计时 -->
    <view class="remain-time">
      砍价还剩
      <text class="time-wrapper" >
        <text class="time-block" >{{remainTime.hour}}</text>
        <text> : </text>
        <text class="time-block" >{{remainTime.minute}} </text>
        <text> : </text>
        <text class="time-block fat" >{{remainTime.second}}</text>
      </text>
      结束
    </view>
    <!-- 砍价按钮区域 -->
    <view class="buttons-group">
      <form-view
        wx:for="{{ actionBtnGroup }}"
        wx:for-item="button"
        wx:key="{{ button.content }}"
        type="help_cut"
        class="{{ button.class }} {{ index === 1 ? 'margin-patch' : '' }}"
      >
        <user-authorize
          wx:if="{{ button.isAuthorizeRequired }}"
          bind:next="onHelpCut"
          btn-class="bargain-userinfo"
          authTypeList="{{ ['nicknameAndAvatar'] }}"
        >
          <button class="{{button.class}}">
            {{button.content}}
          </button>
        </user-authorize>
        <button
          wx:else
          bindtap="handleSuccessEvent"
          data-event="{{ button.event }}"
           class="{{ button.class }}"
        >
          <view class='price-button' wx:if={{activityStatus===3}}>
            <view 
              wx:for="{{button.content}}" 
              data-event="{{ button.event }}" 
              wx:for-index="idx" class="{{index===0&&idx===1?'price-now':''}} {{index===0&&idx===0?'price-button-content':''}}"> 
              {{item}} 
            </view>
          </view>
          <view wx:else data-event="{{ button.event }}" >{{button.content}}</view>
        </button>
      </form-view>
    </view>
  </view>
</view>
<!-- 砍价失败状态 -->
<view
  wx:else
  class="activity-status-fail"
>
  <button
    wx:for="{{ actionBtnGroup }}"
    wx:for-item="button"
    wx:key="{{ button.content }}"
    bindtap="handleFailEvent"
    data-navigateto="{{ button.navigateTo }}"
    data-opentype="{{ button.openType || 'navigate' }}"
    hover-class="none"
    class="button {{ index ===1 ? 'second-button' : '' }}"
  >
    {{button.content}}
  </button>
</view>
