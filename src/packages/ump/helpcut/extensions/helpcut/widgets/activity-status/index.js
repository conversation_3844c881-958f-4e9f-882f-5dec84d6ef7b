import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import Args from '@youzan/weapp-utils/lib/args';
import CountDown from 'utils/countdown';
import Navigate from '@/helpers/navigate';

const app = getApp();

RantaWidget({
  observers: {
    expireTime(newTime) {
      if (newTime) {
        this.setCountDown(newTime);
      }
    },
  },

  attached() {
    mapState(this, ['bargainDetail', 'goodsDetail']);
    mapState(this, [
      'isActionSuccess',
      'bargainProgress',
      'actionBtnGroup',
      'actionTip',
      'expireTime',
      'activityStatus',
    ]);
    mapActions(this, ['SET_SHOW_ACTION_SHEET']);
    mapActions(this, ['HANDLE_PURCHASE', 'HANDLE_HELP_CUT']);
  },

  ready() {
    if (this.data.expireTime) {
      this.setCountDown(this.data.expireTime);
    }
  },

  methods: {
    // 用户帮砍行为处理
    onHelpCut({ detail = {} }) {
      const { data: { userInfo = {} } = {} } = detail;

      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'click_help', // 事件标识
          en: '帮好友点一刀', // 事件名称
        });

      this.HANDLE_HELP_CUT(userInfo);
    },

    // 砍价成功行为处理
    handleSuccessEvent({
      target: {
        dataset: { event },
      },
    }) {
      const { alias } = this.data.goodsDetail || {};
      const { umpAlias, activityId } = this.data.bargainDetail || {};
      switch (event) {
        case 'onInviteFriend':
          app.logger &&
            app.logger.log({
              et: 'click', // 事件类型
              ei: 'click_join', // 事件标识
              en: '邀请好友帮砍', // 事件名称
            });
          this.SET_SHOW_ACTION_SHEET(true);
          return;
        case 'onPurchase':
          this.HANDLE_PURCHASE();
          return;
        case 'onCreateNewBargain':
          // eslint-disable-next-line @youzan/dmc/wx-check, @youzan-open/tee/no-platform-field
          wx.redirectTo({
            url: Args.add(`/pages/goods/detail/index?alias=${alias}`, {
              umpAlias,
              activityId,
              type: 'helpcut',
              umpType: 'helpCut',
              alias,
            }),
          });
          return;
        default:
          console.error(new Error('should never happen'));
      }
    },

    // 砍价失败行为处理
    handleFailEvent({
      target: {
        dataset: { navigateto, opentype },
      },
    }) {
      if (opentype === 'switchTab') {
        return Navigate.switchTab({
          url: navigateto,
        });
      }
      return Navigate.navigate({
        url: navigateto,
      });
    },

    // 设置倒计时
    setCountDown(time) {
      const countdownInstance = new CountDown(time - Date.now().valueOf(), {
        onChange: (timeData, timeDataStr = {}) => {
          this.setYZData({
            remainTime: {
              hour: timeData.hour + timeData.day * 24,
              minute: timeDataStr.minute,
              second: `${timeData.second}.${timeData.hundredMilliseconds}`,
            },
          });
        },
        timeout: 100,
      });
      this.countdownInstance = countdownInstance;
    },
  },

  // 关闭倒计时
  detached() {
    this.countdownInstance && this.countdownInstance.stop();
  },
});
