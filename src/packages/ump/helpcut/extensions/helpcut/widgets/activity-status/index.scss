@mixin buttons-group_button {
  border-radius: 20px;
  line-height: 40px;
  font-size: 3.5vw;
}

@mixin tentacle {
  position: absolute;
  top: -30px;
  height: 45px;
  background: url('https://img01.yzcdn.cn/wsc-minapp/bargain-detail/home/<USER>/tentacle.png')
    no-repeat;
  background-size: contain;
  width: 8px;
}

@keyframes customscale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
  100% {
    transform: scale(1);
  }
}

.activity-status-fail {
  display: flex;
  margin: 0 15px;

  .button {
    height: 40px;
    text-align: center;
    border-radius: 20px;
    background: #fbc31b;
    padding: 0 12px 0 12px;
    line-height: 40px;
    flex-grow: 1;
    color: #911414;
    font-size: 14px;
  }

  .second-button {
    flex-grow: 3;
    margin-left: 12px;
  }
}

.activity-status-success {
  display: flex;
  text-align: center;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  margin: 0 15px 20px 15px;

  .success-container {
    background: #fff;
    display: flex;
    flex-direction: column;
    text-align: center;
    font-size: 14px;
    border-radius: 5px;
    padding: 15px;
    position: relative;

    .tentacle-right {
      @include tentacle;

      left: 15px;
    }

    .tentacle-left {
      @include tentacle;

      right: 15px;
    }

    .action-tip {
      padding: 0 25px 0 25px;
      background: #fff0f2;
      height: 28px;
      line-height: 28px;
      align-self: center;
      border-radius: 14px;
      color: #f72a3e;
    }

    .action-tip-nosuccess {
      color: #646566;
      .strong {
        color: #d00800;
      }
    }

    .total-wrapper {
      position: relative;
      margin-top: 20px;
      background: #fff0f2;
      height: 4px;
      border-radius: 8px;

      .total-bar {
        position: absolute;
        left: 0;
        right: 23px;
        display: flex;
        align-items: center;
        height: 4px;

        .solid-bar {
          height: 4px;
          flex-shrink: 0;
          background: #f72a3e;
          border-top-left-radius: 2px;
          border-bottom-left-radius: 2px;
          display: inline-block;
        }

        .end-point {
          display: inline-block;
          height: 23px;
          flex-shrink: 0;
          position: relative;
          z-index: 1;
          width: 23px;
          border-radius: 15px;
          background: #f72a3e
            url('https://img01.yzcdn.cn/wsc-minapp/bargain-detail/home/<USER>/cut.png')
            no-repeat center;
          background-size: 70%;

          .patch-negate-1px {
            left: -1px;
          }
        }
      }
    }

    .remain-time {
      color: #f44;
      width: 100%;
      margin-top: 20px;
      display: inline-block;
      line-height: 20px;
      height: 20px;

      .time-wrapper {
        margin: 0 8px 0 8px;
      }

      .time-block {
        background: #f72a3e;
        color: #fff;
        display: inline-block;
        text-align: center;
        min-width: 18px;
        padding: 0 2px 0 2px;
        border-radius: 3px;

        &.fat {
          min-width: 36px;
        }
      }
    }

    .buttons-group {
      margin: 20px 0 0 0;
      height: 40px;
      display: flex;

      button {
        overflow: visible !important;
      }

      .gradient-red-background {
        color: #fff;
        background-image: linear-gradient(270deg, #f72a3e 0%, #f65a1c 100%);
        box-shadow: 0 2px 8px 0 rgba(156, 0, 0, 0.2),
          inset 0 -5px 10px 0 #f75123, inset 0 5px 10px 0 #ff7070;
        position: relative;

        // 红色按钮高光部分
        &::before {
          content: '';
          position: absolute;
          display: block;
          top: 4px;
          left: 22px;
          opacity: 0.8;
          background: #fff;
          width: 12px;
          height: 3px;
          border-radius: 2.5px;
        }

        &::after {
          content: '';
          position: absolute;
          display: block;
          top: 4px;
          left: 37px;
          opacity: 0.8;
          width: 74px;
          height: 3px;
          border-radius: 2.5px;
          transform: scale(1);
          border: none;
          background-image: linear-gradient(
            270deg,
            rgba(255, 255, 255, 0),
            rgba(255, 255, 255, 0.8)
          );
        }

        .bargain-userinfo {
          animation: customscale 1s infinite;
        }
      }

      .yellow-background {
        color: #911414;
        border: none;
        background-image: linear-gradient(90deg, #ffc71c 0%, #ffda29 100%);
        box-shadow: 0 2px 8px 0 rgba(156, 113, 0, 0.2),
          inset 0 -5px 10px 0 #ffd84c, inset 0 5px 10px 0 #ffdf55;

        // 按钮高光部分
        &::before {
          content: '';
          position: absolute;
          display: block;
          top: 4px;
          left: 16px;
          opacity: 0.8;
          background: #fff;
          width: 6px;
          height: 3px;
          border-radius: 2.5px;
        }

        &::after {
          content: '';
          position: absolute;
          display: block;
          top: 4px;
          left: 25px;
          opacity: 0.8;
          width: 50px;
          height: 3px;
          border-radius: 2.5px;
          transform: scale(1);
          border: none;
          background-image: linear-gradient(
            270deg,
            rgba(255, 255, 255, 0),
            rgba(255, 255, 255, 0.8)
          );
        }
      }

      .price {
        color: #fff;

        .price-button {
          height: 40px;

          .price-button-content {
            line-height: 16px;
            padding-top: 7px;
          }
          .price-now {
            font-size: 10px;
            line-height: 12px;
          }
        }
      }

      .gray-background {
        background: #bbb;
        color: #fff;

        &::after,
        &::before {
          display: none;
        }
      }

      .prime {
        @include buttons-group_button;

        flex-grow: 1;
        z-index: 2;
      }

      .margin-patch {
        margin: 0 0 0 10px;
      }

      .minor {
        @include buttons-group_button;
      }
    }
  }
}
