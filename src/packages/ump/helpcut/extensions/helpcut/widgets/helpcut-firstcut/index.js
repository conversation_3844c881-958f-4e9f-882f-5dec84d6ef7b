import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';

RantaWidget({
  data: {
    show: true,
    firstCut: 0,
    options: {
      during: 1.5,
      height: 60,
      cellWidth: 38,
      ease: 'cubic-bezier(0, 1, 0, 1)',
      color: '#ffeea4',
      columnStyle: `font-size: 60px;background-image: linear-gradient(to bottom,#fff, #FEFF7C);-webkit-background-clip: text;-webkit-text-fill-color: transparen`,
    },
  },

  attached() {
    mapState(this, ['bargainDetail']);
  },

  ready() {
    setTimeout(() => {
      this.setYZData({
        firstCut: this.data.bargainDetail?.firstCut || false,
      });
    }, 800);
    setTimeout(() => {
      this.setYZData({
        show: false,
      });
    }, 3000);
  },
});
