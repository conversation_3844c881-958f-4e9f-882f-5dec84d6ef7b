@import '~shared/common/css/animation.scss';

.block-frameless {
  width: 100%;
  background-color: transparent !important;
}

.block {
  position: relative;
  background: url('https://img01.yzcdn.cn/upload_files/2021/11/08/FuqgbxJ5qzAoeyt45aLmeI1N0ay1.png');
  background-size: contain;
  background-position-x: center;
  background-repeat: no-repeat;
  width: 320px;
  margin: auto;
  padding: 24px 0 45px;
  font-weight: bolder;

  > .title {
    font-size: 20px;
    color: #fff;
    text-align: center;
    line-height: 32px;
    margin-bottom: 20px;
  }

  > .detail {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    color: #ffeea4;

    .price {
      font-size: 60px;
      height: 60px;
      overflow: hidden;
      background-image: linear-gradient(to bottom, #fff, #feff7c);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .unit {
      font-size: 30px;
      margin-left: 10px;
    }
  }
}
