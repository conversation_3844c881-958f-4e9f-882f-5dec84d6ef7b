import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';

RantaWidget({
  data: {
    rulesVisible: false,
  },

  attached() {
    mapState(this, ['helpcutInfo', 'rules']);
    mapState(this, {
      showRules(state) {
        const { helpcutInfo = {}, rules } = state;
        return helpcutInfo.rules || rules;
      },
      showRuleImage(state) {
        const { helpcutInfo = {} } = state;
        return (
          helpcutInfo.ruleImage ||
          'https://img01.yzcdn.cn/upload_files/2021/10/08/Fr7W60cP3v5wPHBLyvC7R4vZ7ONn.png'
        );
      },
    });
  },
});
