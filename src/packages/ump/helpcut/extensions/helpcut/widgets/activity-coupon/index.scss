.coupon-pop {
  background-color: transparent !important;

  .coupon-wrapper {
    display: flex;
    flex-direction: column;
  
  
    .coupon-content {
      width: 80vw;
      background: linear-gradient(to left bottom, #F65A1C, #F72A3E);
      font-size: 14px;
      padding: 36px 20px 30px 20px;
      display: flex;
      flex-direction: column;
      text-align: center;
      border-radius: 5px;
      box-shadow: inset 0 0 30px #9C0A0A, 0 2px 4px #000;

      .cut-tip {
        color: #fff;

        .coupon-price {
          color: #fff;
          font-size: 24px;
          font-weight: 600;
        }
      }

      .coupon-tip {
        color: #FFF;
        margin-top: 15px;
        margin-bottom: 15px;
      }

      .tap {
        background: linear-gradient(to bottom, #BF2B2A, #BF412A);
        text-align: center;
        height: 16px;
        border-radius: 8px;
      }
  
      .coupon-detail {
        padding: 25px 10px 25px 10px;
        display: flex;
        flex-direction: column;
        margin: -8px 10px 0 10px;
        background: #FFF;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 50);

        .detail-content {
          box-shadow: 0 5px 10px rgba(247, 42, 62, 0.3);
          display: flex;
          height: 64px;

          .left-block {
            background: linear-gradient(to left bottom, #FA893D, #ED3862);
            flex-grow: 1;
            display: flex;
            font-weight: 600;
            padding: 7px;
            align-items: center;
            justify-content: center;
            flex-direction: column;
  
            .coupon-value {
              color: #FFF;
              font-size: 24px;
            }
            
            .coupon-limit {
              color: #FFF;
              font-size: 10px;
            }
          }
  
          .right-block {
            flex-grow: 3;
            text-align: left;
            padding: 12px 10px 12px 10px;
            display: flex;
            flex-direction: column;
  
            .coupon-name {
              font-size: 14px;
              color: #333;
              flex-grow: 1;
            }
            
            .coupon-time {
              font-size: 10px;
              color: #999;
            }
          }
        }
      }
  
      .use-button {
        font-size: 14px;
        color: #911414;
        border-radius: 20px;
        margin: 35px 0 0 0;
        background: #FBC31B;
        height: 40px;
        line-height: 40px;
      }
    }

    .coupon-close {
      margin-top: 14px;
      color: #FFF;
      font-size: 24px;
      align-self: center;
    }
  }
}