import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';

RantaWidget({
  attached() {
    mapState(this, ['showCouponPop', 'couponResult', 'userCutPrice']);
    mapActions(this, ['SET_SHOW_COUPON_POP']);
  },

  methods: {
    closeCouponPop() {
      this.SET_SHOW_COUPON_POP(false);
    },
    useCoupon() {
      // eslint-disable-next-line @youzan/dmc/wx-check, @youzan-open/tee/no-platform-field
      wx.navigateTo({
        url: '/packages/user/coupon/list/index?type=promocard&title=我的优惠券',
      });
    },
  },
});
