@import '~shared/common/css/animation.scss';

.block-frameless {
  width: 100%;
  background-color: transparent !important;
}

.block-frame {
  width: 310px;
  padding: 32px 16px 24px;
  text-align: center;

  > .goods {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    margin-bottom: 32px;
  }

  > .cut-status {
    font-size: 20px;
    font-weight: bolder;
    line-height: 28px;
    padding-bottom: 10px;
  }

  > .helpcut-status {
    font-size: 18px;
    font-weight: bolder;
    line-height: 28px;
    padding: 12px 0;
  }

  > .title {
    position: relative;
    align-items: baseline;

    .title-normal {
      font-size: 18px;
      font-weight: bolder;
    }

    .title-price {
      font-size: 24px;
      font-weight: bolder;
      color: #ff1c00;
    }

    .price-7,
    .price-8 {
      font-size: 22px;
    }

    .price-9,
    .price-10,
    .price-11,
    .price-12 {
      font-size: 18px;
    }
  }

  > .process {
    margin: 16px 9px 0;

    .block {
      width: 100%;
      height: 12px;
      background: rgba($color: #ff1c00, $alpha: 0.1);
      border-radius: 6px;
    }

    .bar {
      height: 12px;
      border-radius: 6px;
      background-image: linear-gradient(90deg, #ff8947 1%, #f7351d 99%);
    }
  }

  > .btn {
    width: 240px;
    height: 44px;
    line-height: 44px;
    background-image: linear-gradient(
      90deg,
      rgba(255, 137, 71, 1) 1%,
      #f7351d 99%
    );
    border-radius: 22px;
    color: #fff;
    font-size: 16px;
    font-weight: bolder;
    margin: auto;
    margin-top: 32px;
  }

  > .vicebtn {
    width: 240px;
    font-size: 14px;
    color: #ff1c00;
    line-height: 36px;
    border: 1px solid #ff1c00;
    border-radius: 20px;
    margin: auto;
    margin-top: 24px;
  }

  > .extrabtn {
    margin-top: 20px;
    font-size: 14px;
    font-weight: bolder;
    color: #ff1c00;
    text-align: center;
    line-height: 32px;
  }
}

.popup-frameless {
  position: relative;
  width: 100vw;
  text-align: center;
  overflow: hidden;

  > .tip {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #fff;
    margin-bottom: 22px;

    .avatar {
      width: 24px;
      height: 24px;
      border-radius: 24px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      margin-right: 4px;
    }
  }

  > .title {
    position: relative;
    background: url('https://img01.yzcdn.cn/upload_files/2021/11/08/FpxBecnJz1BF37hiA342M7USVWCd.png');
    background-size: contain;
    background-position-x: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .title-normal {
      font-size: 20px;
      color: #fff;
    }

    .title-sp {
      font-size: 30px;
      font-weight: bolder;
      color: #dab71e;
      margin: 0 2px;
    }

    .title-gold {
      font-size: 20px;
      font-weight: bolder;
      // color: #dab71e;
      background-image: linear-gradient(to bottom, #fff, #feff7c);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .title-gold-creator {
      font-size: 20px;
      color: #fff;
      line-height: 32px;
      font-weight: bolder;
      display: flex;
      align-items: center;

      .title-original-price {
        display: inline-block;
        padding: 0 4px;
        font-size: 30px;
        color: #ffeea4;
        line-height: 38px;
        height: 40px;
      }
    }
  }
  > .help-title {
    background: url('https://img01.yzcdn.cn/upload_files/2021/11/05/Fp_k6rexBR_hRE1zSB0X5pBDNlgh.png');
    background-size: contain;
    background-position-x: center;
    background-repeat: no-repeat;
  }

  > .goods {
    width: 200px;
    height: 200px;
    border-radius: 4px;
    border: 6px solid #fdda80;
    margin: 36px 0 40px;
  }
  > .big-goods {
    margin: 26px 0 30px;
    border-radius: 8px;
    border: 1px solid #fdda80;
    box-shadow: 0 0 0 9px #fdda80;
  }

  > .recommend {
    display: flex;
    justify-content: center;
    margin: 20px 0 40px;

    .item {
      margin: 0 4px;
    }

    .block {
      border-radius: 4px;
      border: 1px solid #fdda80;
      background: #fff;
      margin-bottom: 14px;

      .block-border {
        padding: 5px;
        box-shadow: 0 0 0 5px #fdda80;
        border-radius: 4px;
      }
      .goods {
        width: 96px;
        height: 96px;
      }

      .name {
        margin: 10px 0 6px;
        line-height: 20px;
        font-size: 13px;
        width: 96px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .btn {
      display: flex;
      justify-content: center;
      width: 100px;
      line-height: 30px;
      background-image: linear-gradient(270deg, #ffec56 1%, #fff1a5 99%);
      border-radius: 20px;
      font-size: 14px;
      font-weight: bolder;
      color: #ee0a24;
      text-align: center;
      margin: auto;
    }
  }

  .frameless-btn {
    width: 240px;
    height: 44px;
    line-height: 44px;
    background-image: linear-gradient(270deg, #ffec56 1%, #fff1a5 99%);
    border-radius: 22px;
    color: #ee0a24;
    font-size: 18px;
    font-weight: bolder;
    margin: auto;
  }

  > .entrance {
    display: flex;
    justify-content: center;
    align-items: baseline;
    font-size: 16px;
    color: #fff;
    margin-top: 40px;
  }

  > .red {
    position: relative;
    background: url('https://b.yzcdn.cn/public_files/45f8676528b4004f829703a477f64986.png');
    background-size: cover;
    width: 318px;
    height: 328px;
    text-align: center;
    margin: auto;
    margin-top: 45px;

    .avatar-block {
      display: flex;
      justify-content: center;
    }
    .reverse-avatar-block {
      flex-direction: row-reverse;
    }

    .avatar {
      margin: 44px 0 10px;
      width: 48px;
      height: 48px;
      border-radius: 48px;
      border: 2px solid rgba(255, 255, 255, 0.5);
    }
    .fold-avatar:not(:first-child) {
      margin-right: -20px;
    }

    .nickname {
      font-family: PingFangSC-Semibold;
      font-size: 14px;
      color: #fffbb7;
      text-align: center;
    }

    .btn {
      position: absolute;
      width: 110px;
      height: 110px;
      left: 104px;
      bottom: 32px;
    }
  }

  > .red-detail {
    position: relative;
    background: url('https://b.yzcdn.cn/public_files/72bad2a378d207f2e432fd2e29e8ecc9.png');
    background-size: cover;
    width: 318px;
    height: 328px;
    text-align: center;
    margin: auto;
    margin-top: 45px;

    .title {
      font-size: 16px;
      font-weight: bolder;
      color: #323233;
      text-align: center;
      line-height: 22px;
      padding-top: 41px;

      > text {
        color: #d44c00;
      }
    }

    .tip {
      font-size: 14px;
      line-height: 20px;
      color: #fffbb7;
      text-align: center;
      margin-top: 70px;
    }

    .number {
      font-family: Avenir-Heavy;
      font-size: 44px;
      color: #fffea4;
      text-align: center;
      line-height: 44px;
      margin: 12px 0;
      background-image: linear-gradient(to bottom, #fff, #feff7c);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .process {
      position: relative;
      width: 200px;
      margin: auto;

      .block {
        width: 100%;
        height: 12px;
        background: #c85d00;
        border-radius: 6px;
      }

      .bar {
        width: 0;
        height: 12px;
        border-radius: 6px;
        background-image: linear-gradient(270deg, #fbe941 0%, #fffbde 100%);
        transition: width 1s ease;
      }
    }

    .btn {
      position: relative;
      width: 200px;
      line-height: 40px;
      background: linear-gradient(179deg, #fff7cd 2%, #ffec56 100%);
      border-radius: 20px;
      font-size: 14px;
      font-weight: bolder;
      color: #ee0a24;
      text-align: center;
      margin: auto;
      margin-top: 25px;
    }
  }
}

.popup-frameless::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: -1;
  background: no-repeat center/100%
    url('https://img01.yzcdn.cn/upload_files/2021/11/08/FsgSWBCIcG5dEm8_fULeVxwctDz5.png');
  animation: rotate 10s linear infinite;
}

.coupon {
  position: relative;
  width: 310px;
  margin: auto;
  background: url('https://b.yzcdn.cn/public_files/ecceeac6bd15d13467fa1dcb7718a833.png');
  background-size: cover;
  padding: 62px 12px 12px;
  border-radius: 16px;
  margin-bottom: 24px;

  > .coupon-block {
    position: relative;
    padding: 12px;
    background-color: #fff;
    border-radius: 8px;
  }

  .coupon-detail {
    position: relative;
    background-image: linear-gradient(
      90deg,
      rgba(255, 137, 71, 1) 1%,
      #f7351d 99%
    );
    width: 100%;
    height: 80px;
    border-radius: 4px;
    display: flex;
    align-items: center;

    > .left {
      width: 104px;
      display: flex;
      justify-content: center;
      align-items: baseline;

      > .value {
        font-size: 30px;
        color: #fff;
        text-align: center;
        font-weight: bolder;
      }

      > .unit {
        font-size: 12px;
        color: #fff;
        text-align: center;
      }
    }

    > .right {
      flex: 1;
      padding: 17px 0;
      text-align: left;

      > .tip {
        font-size: 14px;
        color: #fff;
        line-height: 20px;
        margin-bottom: 10px;
      }

      > .date {
        opacity: 0.8;
        font-size: 12px;
        color: #fff;
        line-height: 16px;
      }
    }
  }
}
