<!-- 授权弹框 -->
<simple-popup
  wx:if="{{ popupOptions.type === 'initPopup' }}"
  show="{{ true }}"
  class="block-frameless helpcut-simple-popup"
  custom-popup-style="z-index: 16;"
  custom-overlay-style="background-color: rgba(0, 0, 0, .8);"
  custom-container-style="background-color: transparent;"
>
  <view name=solt class="popup-frameless">
    <!-- 初始化状态标题 -->
    <!-- 帮砍标题 -->
    <view wx:if="{{ popupOptions.showInitTitle }}" class="title">
      <view wx:if="{{ popupOptions.hasTitle }}" class="title-gold">{{ popupOptions.initTitle }}</view>
      <view wx:else class="title-gold-creator">
        价值
        <view class="title-original-price">{{popupOptions.originalPrice}}</view>
        元商品{{popupOptions.minPrice}}元拿
      </view>
    </view>
  <!-- 商品 -->
    <image
      wx:if="{{ popupOptions.showInitGoods }}"
      class="goods big-goods"
      src="{{ goodsDetail.cover }}"
      mode="aspectFill"
    />
    <!-- 主按钮 -->
    <user-authorize
      wx:if="{{ popupOptions.showInitBtn }}"
      bind:next="toInit"
      btn-class="frameless-btn a-breath"
      authTypeList="{{ ['mobile', 'nicknameAndAvatar'] }}"
    >
      {{ popupOptions.initBtnText }}
    </user-authorize>
  </view>
</simple-popup>

<view wx:else>
  <!-- 无边框类型弹框 -->
  <van-popup
    show="{{ framelessShow }}"
    lock-scroll
    bind:click-overlay="clickFramelessOverlay"
    custom-class="block-frameless"
    safe-area-inset-bottom
    overlay-style="background-color: var(--overlay-background-color,rgba(0,0,0,0.8))"
  >
    <slot />
    <view class="popup-frameless">
      <!-- 头部tip -->
      <view wx:if="{{ helpPopupOptions.hasTitle || popupOptions.hasTitle }}" class="tip">
        <image
          class="avatar"
          src="{{ bargainDetail.avatar }}"
          mode="aspectFill"
        />
        <view>{{ popupOptions.title || helpPopupOptions.title }}</view>
      </view>
      <!-- 标题 -->
      <view wx:if="{{ helpPopupOptions.showHelpcutRecommend }}" class="title">
        <view class="title-gold">{{ helpPopupOptions.cutTitle }}</view>
      </view>
      <!-- 帮砍标题 -->
      <view wx:if="{{ popupOptions.showRedPacket && !showRedDetail }}" class="title help-title" />
      <!-- 帮砍红包壳 -->
      <view
        wx:if="{{ popupOptions.showRedPacket && !showRedDetail }}"
        class="red a-fade-show {{ !showRedPacket && showRedDetail ? 'a-fade-hide' : '' }}"
      >
        <view class="{{ newBargainInfo.newShowAvatarList.length > 2 ? 'reverse-avatar-block avatar-block' : 'avatar-block' }}">
          <image
            wx:for="{{ newBargainInfo.newShowAvatarList }}"
            class="{{ newBargainInfo.newShowAvatarList.length > 2 ? 'fold-avatar avatar' : 'avatar' }}"
            src="{{ item }}"
            mode="aspectFill"
          />
        </view>
        <view class="nickname">
          {{ newBargainInfo.newShowNickname }}
        </view>
        <image
          class="btn a-breath"
          src="https://b.yzcdn.cn/public_files/9258d766ef79fc67d2cc130e2be509cd.png"
          mode="aspectFill"
          bind:tap="openRedPacket"
        />
      </view>
      <!-- 帮砍红包内容 -->
      <view
        wx:if="{{ showRedDetail && !showRedPacket}}"
        class="red-detail a-fade-show"
      >
        <view class="title">
          {{ newBargainInfo.newCutNum }}名好友又砍掉<text>{{ bargainRecords.newCutPrice }}</text>元
        </view>
        <view class="tip">
          砍价进度已达
        </view>
        <view class="number">
          {{ bargainRecords.cutRate }}%
        </view>
        <!-- 进度条 -->
        <view class="process">
          <view class="block">
            <view class="bar" style="width: {{ bargainProgressWidth }}%" />
          </view>
        </view>
        <view class="btn" bind:tap="handleShowShare">
          继续邀请好友帮砍
        </view>
      </view>
      <!-- 砍价赠送优惠券 -->
      <view class="coupon" wx:if="{{ helpPopupOptions.hasCoupon }}">
        <view class="coupon-block">
          <view class="coupon-detail">
            <view class="left">
              <text class="value">
                {{ helpPopupOptions.couponResult.value }}
              </text>
              <text class="unit">
                {{ helpPopupOptions.couponResult.unit }}
              </text>
            </view>
            <view class="right">
              <text class="tip">
                {{ helpPopupOptions.couponResult.thresholdAmountMsg }}
              </text>
              <text class="date">
                {{ helpPopupOptions.couponResult.availableDateMsg }}
              </text>
            </view>
          </view>
          <goods-recommend
            margin-top
            data-type="coupon"
            title="购买以下商品可用"
            list="{{ couponGoodsList }}"
          />
        </view>
      </view>
      <!-- 主按钮 -->
      <view wx:if="{{ helpPopupOptions.hasCoupon }}" class="frameless-btn a-breath" bind:tap="useCoupon">
        立即使用
      </view>
      <!-- 推荐砍价活动商品 -->
      <view wx:if="{{ helpPopupOptions.showHelpcutRecommend }}" class="recommend">
        <view wx:for="{{ helpPopupOptions.allHelpcutList }}" class="item ">
          <view class="block {{ index === 1 ? 'a-goods-breath-reverse' : 'a-goods-breath' }}">
            <view class="block-border">
              <image
                class="goods"
                src="{{ item.thumbUrl }}"
                mode="aspectFill"
              />
              <view class="name">
                {{ item.goodsTitle }}
              </view>
            </view>
          </view>
          <view class="btn" data-index="{{ index }}" bind:tap="newBargin">
            {{ helpPopupOptions.cutBtn }}
          </view>
        </view>
      </view>
      <!-- 进店入口 -->
      <view wx:if="{{ helpPopupOptions.showHelpcutRecommend }}" class="entrance" bind:tap="shop">
        进店逛逛
        <van-icon class="extrabtn-icon" name="arrow" size="14px"/>
      </view>
    </view>
  </van-popup>

  <!-- 有边框类型弹框 -->
  <van-popup
    show="{{ show }}"
    closeable="{{ true }}"
    round
    custom-class="block-frame"
    bind:close="onClose"
  >
    <slot />
    <!-- 商品 -->
    <image
      wx:if="{{ popupOptions.needGoodsImg }}"
      class="goods"
      src="{{ goodsDetail.cover }}"
      mode="aspectFill"
    />
    <!-- 状态文案 -->
    <view wx:if="{{ popupOptions.status}}" class="cut-status">{{ popupOptions.status }}</view>
    <view wx:if="{{ helpPopupOptions.title }}" class="helpcut-status">{{ helpPopupOptions.title }}</view>
    <!-- 标题 -->
    <view wx:if="{{ popupOptions.tip }}" class="title">
      <text
        wx:for="{{ popupOptions.tip }}"
        class="{{ item.class }}"
      >
        {{ item.content }}
      </text>
    </view>
    <!-- 进度条 -->
    <view wx:if="{{ popupOptions.needProcess }}" class="process">
      <view class="block">
        <view class="bar" style="width: {{ bargainProgress * 100 }}%" />
      </view>
    </view>
    <goods-recommend wx:if="{{ helpPopupOptions.showRecommend || popupOptions.showRecommendTop }}" margin-top list="{{ recommendGoodsThree }}" />
    <!-- 主按钮 -->
    <view wx:if="{{ popupOptions.btn }}" class="btn" bind:tap="{{ popupOptions.btn.event }}">
      {{ popupOptions.btn.content }}
    </view>
    <!-- 空心主按钮 -->
    <view wx:if="{{ popupOptions.viceBtn }}" class="vicebtn" bind:tap="{{ popupOptions.viceBtn.event }}">
      {{ popupOptions.viceBtn.content }}
    </view>
    <!-- 帮砍空心主按钮 -->
    <view wx:if="{{ helpPopupOptions.viceBtn }}" class="vicebtn" bind:tap="{{ helpPopupOptions.viceBtn.event }}">
      {{ helpPopupOptions.viceBtn.content }}
    </view>
    <goods-recommend wx:if="{{ popupOptions.showRecommendBottom }}" margin-top list="{{ recommendGoodsThree }}" />
    <!-- 额外按钮 -->
    <view
      wx:if="{{ popupOptions.extraBtn && popupOptions.extraBtn.need }}"
      class="extrabtn"
      bind:tap="{{ popupOptions.extraBtn.event }}"
    >
      {{ popupOptions.extraBtn.content }}
      <van-icon class="extrabtn-icon" name="arrow" size="14px"/>
    </view>
  </van-popup>
</view>