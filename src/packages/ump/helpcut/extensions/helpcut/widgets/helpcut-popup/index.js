import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import Navigate from '@/helpers/navigate';
import Args from '@youzan/weapp-utils/lib/args';
import { sendLogger } from '../../common/utils';

RantaWidget({
  data: {
    show: false,
    framelessShow: false,
    showRedDetail: false,
    showRedPacket: true,
    clickOverlayClose: false,
    bargainProgressWidth: 0,
  },

  attached() {
    mapState(this, [
      'needFollow',
      'goodsDetail',
      'bargainDetail',
      'bargainRecords',
      'allHelpcutListNumber',
      'allHelpcutList',
      'recommendGoodsThree',
      'couponGoodsList',
      'queryOptions',
      'hasAuthorized',
    ]);
    mapState(this, [
      'popupOptions',
      'bargainProgress',
      'newBargainInfo',
      'helpPopupOptions',
    ]);
    mapActions(this, ['SET_SHOW_ACTION_SHEET', 'SET_SHOW_FOLLOW_POP']);
    mapActions(this, [
      'HANDLE_PURCHASE',
      'ENTER_SHOP',
      'ENTER_GOODS_DETAIL',
      'USE_COUPON',
      'FETCH_INIT_DETAIL',
      'RESTART_BARGAIN',
    ]);
  },

  observers: {
    'popupOptions.**': function (val) {
      const { type, hasTitle } = val || {};
      const { helpPopupOptions } = this.data;
      this.setYZData(
        {
          show: type === 'frame' || helpPopupOptions.type === 'frame',
          framelessShow:
            type === 'frameless' || helpPopupOptions.type === 'frameless',
        },
        { immediate: true }
      );
      if (type === 'initPopup') {
        sendLogger({
          et: 'view',
          ei: hasTitle ? 'view_helpcut' : 'view_cut',
          en: hasTitle ? '助力按钮曝光' : '发起砍价按钮曝光',
        });
      }
    },
    'helpPopupOptions.**': function (val) {
      const { type, hasCoupon, showRecommend, allHelpcutList } = val || {};
      const { popupOptions } = this.data;
      this.setYZData(
        {
          show: type === 'frame' || popupOptions.type === 'frame',
          framelessShow:
            type === 'frameless' || popupOptions.type === 'frameless',
        },
        { immediate: true }
      );
      hasCoupon &&
        sendLogger({
          et: 'view',
          ei: 'view_coupon',
          en: '优惠券曝光',
        });
      showRecommend &&
        sendLogger({
          et: 'view',
          ei: 'view_recommend_goods',
          en: '砍价推荐商品曝光',
          params: {
            goodsList: allHelpcutList,
          },
        });
    },
  },

  ready() {
    setTimeout(() => {
      const { hasNewCut } = this.data.bargainRecords;
      hasNewCut &&
        sendLogger({
          et: 'view',
          ei: 'view_red_packet_popup',
          en: '助力红包弹窗曝光',
          params: {
            component: 'helpcut_redpacket_popup',
          },
        });
      this.setYZData({
        showRedPacket: hasNewCut,
      });
    }, 800);
  },

  methods: {
    onClose() {
      this.setYZData({ show: false });
    },

    onFramelessClose() {
      this.setYZData({ framelessShow: false });
    },

    clickFramelessOverlay() {
      if (this.data.clickOverlayClose) {
        this.onFramelessClose();
      }
    },

    handleShowShare() {
      sendLogger({
        et: 'click',
        ei: 'click_cut_share',
        en: '分享按钮点击',
        params: {
          component: 'helpcut_redpacket_popup',
        },
      });
      this.onFramelessClose();
      this.SET_SHOW_ACTION_SHEET(true);
    },

    openRedPacket() {
      sendLogger({
        et: 'click',
        ei: 'click_open',
        en: '”开“按钮点击',
        params: {
          component: 'helpcut_redpacket_popup',
        },
      });
      this.setYZData({
        showRedPacket: false,
      });
      const { bargainRecords = {} } = this.data;
      setTimeout(() => {
        this.setYZData({
          showRedDetail: true,
          clickOverlayClose: true,
        });
      }, 550);
      setTimeout(() => {
        this.setYZData({
          bargainProgressWidth: bargainRecords.cutRate,
        });
      }, 650);
    },

    // 现价购买
    buy() {
      this.HANDLE_PURCHASE();
    },

    // 重新发起砍价
    restart() {
      this.RESTART_BARGAIN();
    },

    // 进店逛逛
    shop() {
      this.ENTER_SHOP();
    },

    newBargin(e) {
      const { index = 0 } = e.currentTarget.dataset;
      const item = this.data.allHelpcutList[index];
      sendLogger({
        et: 'click',
        ei: 'click_recommend_goods_cut',
        en: '点击推荐商品砍价按钮',
        params: {
          goods: item,
        },
      });
      if (item.supportCutWhenStart) {
        Navigate.navigate({
          url: `/packages/ump/helpcut/index?kdtId=${item.kdtId}&umpAlias=${item.umpAlias}&umpActivityId=${item.id}&shopAutoEnter=1`,
        });
      } else {
        this.ENTER_GOODS_DETAIL({ ...item, umpActivityId: item.id });
      }
    },

    // 使用优惠券
    useCoupon() {
      sendLogger({
        et: 'click',
        ei: 'click_coupon',
        en: '点立即使用优惠券按钮',
      });
      const { couponResult } = this.data.helpPopupOptions;
      this.USE_COUPON(couponResult);
    },

    // 关注公众号
    toInit() {
      const { needFollow, queryOptions, popupOptions } = this.data;
      sendLogger({
        et: 'click',
        ei: popupOptions.hasTitle ? 'click_helpcut' : 'click_cut',
        en: popupOptions.hasTitle ? '点击助力按钮' : '点击发起砍价按钮',
      });
      this.FETCH_INIT_DETAIL();
      if (needFollow) {
        return this.SET_SHOW_FOLLOW_POP(true);
      }
      Navigate.navigate({
        url: Args.add('/packages/ump/helpcut/index', queryOptions || {}),
      });
    },
  },
});
