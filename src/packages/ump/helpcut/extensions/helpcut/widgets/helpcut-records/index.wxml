<view wx:if="{{ newBargainRecords.records && newBargainRecords.records.length > 0}}" class="helpcut-block">
  <view class="activity-records">
    <view class="title">
      砍价记录
    </view>
    <view class="detail {{ isCollapse ? 'collapse' : '' }}">
      <view wx:for="{{ newBargainRecords.records }}" class="item van-hairline--bottom">
        <image class="avator" src="{{ item.avatar || defaultAvatar }}" mode="aspectFill" />
        <view class="info">
          <view class="nickname">
            {{ item.nickName || '神秘用户' }}
          </view>
          <view class="extra">
            {{ item.extra }}
          </view>
        </view>
        <view class="price">
          砍掉{{ item.cutPrice }}元
        </view>
      </view>
    </view>
    <view
      wx:if="{{ newBargainRecords.records.length > 3 }}"
      class="show-all-button"
      bindtap="toggleCollapse"
    >
      {{ isCollapse ? '查看全部记录' : '收起'}}
      <van-icon name="{{ isCollapse ? 'arrow-down' : 'arrow-up'}}" />
    </view>
  </view>
</view>