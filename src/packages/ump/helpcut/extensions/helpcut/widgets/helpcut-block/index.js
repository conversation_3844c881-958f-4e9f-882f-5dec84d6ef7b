// @ts-nocheck
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import Event from '@youzan/weapp-utils/lib/event';

RantaWidget({
  attached() {
    mapState(this, [
      'sponsorFetched',
      'queryOptions',
      'recommendGoods',
      'skuInfo',
      'showSkuModal',
      'goodsDetail',
      'bargainDetail',
      'showFollowPopup',
      'followExtraData',
      'isNewHelpCut',
      'hasLogin',
      'needFollow',
      'isCreator',
      'hasAuthorized',
      'newActionExtra',
      'activityStatus',
      'helpcutInfo',
      'rules',
    ]);

    mapActions(this, [
      'SET_SKU_CLOSE',
      'SET_SHOW_FOLLOW_POP',
      'HANDLE_BUY',
      'SET_HELPCUT_INFO',
      'SET_BEHAVIOR_READY',
    ]);
  },

  ready() {
    const { rules, helpcutInfo } = this.data;
    this.ctx.cloud
      .invoke('beforeFormatHelpcut', {
        rules,
        ruleImage:
          'https://img01.yzcdn.cn/upload_files/2021/10/08/Fr7W60cP3v5wPHBLyvC7R4vZ7ONn.png',
      })
      .then(({ rules, ruleImage }) => {
        this.SET_HELPCUT_INFO({
          ...helpcutInfo,
          rules,
          ruleImage,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  },

  methods: {
    // 处理下单流程
    handleBuy(event) {
      this.HANDLE_BUY(event);
    },

    // 关闭sku弹层
    handleSkuClose() {
      this.SET_SKU_CLOSE();
    },

    // 关闭关注弹层
    togglePopupShow() {
      this.SET_SHOW_FOLLOW_POP(false);
    },

    behaviorComponentReady() {
      import('@youzan/behavior-verify/src/miniapp/weapp/main').then(
        ({ default: behaviorVerify }) => {
          behaviorVerify.init(this);
          this.SET_BEHAVIOR_READY(true);
          Event.trigger('behavior:ready');
        }
      );
    },
  },
});
