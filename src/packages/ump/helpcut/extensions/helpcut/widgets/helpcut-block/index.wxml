<page-container>
  <behavior-verify id="behavior-verify" bind:behaviorComponentReady="behaviorComponentReady">
    <view
      wx:if="{{ sponsorFetched }}"
      class="{{isNewHelpCut?'helpcut':'bargain-purchase'}}"
    >
      <view wx:if="{{ isNewHelpCut }}">
        <!-- 登录并不需要再关注公众号状态并已授权 -->
        <view wx:if="{{ hasLogin && !needFollow && hasAuthorized }}">
          <!-- 发起人 -->
          <view wx:if="{{ isCreator }}">
            <view class="goodsBg {{recommendGoods.length > 0 ? '' : 'demote-goodsBg'}}">
              <!-- 新版头部展示 -->
              <helpcut-goods />
              <!-- 活动信息 -->
              <helpcut-status />
              <!-- 帮砍记录 -->
              <helpcut-records />
            </view>
            <!-- 推荐商品 -->
            <products-featured goodsList="{{ recommendGoods }}" />
            <!-- 自砍弹窗 -->
            <helpcut-firstcut wx:if="{{ bargainDetail.showFirstCut }}" />
            <!-- 状态提示框 -->
            <helpcut-popup wx:if="{{ newActionExtra.needPopup }}" />
            <!-- 新版海报 -->
            <helpcut-invite-post />
          </view>
          <!-- 帮砍人 -->
          <view wx:else>
            <!-- 状态提示框 -->
            <helpcut-popup />
          </view>
        </view>
        <!-- 未登录或者需要关注公众号状态 -->
        <view wx:else>
          <helpcut-popup />
        </view>
      </view>
      <view wx:else>
        <!-- 头部展示 -->
        <activity-header />
        <!-- 活动展示 -->
        <activity-status />
        <!-- 帮砍记录 -->
        <activity-records />
         <!-- 推荐商品 -->
        <products-featured goodsList="{{ recommendGoods }}" />
        <!-- 邀请海报 -->
        <invite-post />
      </view>
      <!-- 助力弹层 -->
      <activity-coupon wx:if="{{ !isNewHelpCut }}" />
      <!-- 优惠弹层 -->
      <helpcut-success wx:if="{{ !isNewHelpCut }}" />
      <!-- 关注弹层 -->
      <follow-popup
        show="{{ showFollowPopup }}"
        extra-data="{{ followExtraData }}"
        title="成为店铺粉丝才能为我助力"
        btn-text="成为粉丝"
        bind:close="togglePopupShow"
      />
      <!-- sku弹层 -->
      <base-sku
        show="{{ showSkuModal }}"
        goods="{{ skuInfo.goodsDetail }}"
        sku="{{ skuInfo.sku }}"
        extra-data="{{ skuInfo.extraData }}"
        bind:buy="handleBuy"
        bind:sku-close="handleSkuClose"
        theme-class="{{ themeClass }}"
        buy-text="立即下单"
        show-add-cart-btn="{{ false }}"
        hide-stock
        reset-stepper-on-hide="{{ false }}"
        generic:sku-header-price="sku-header-price"
      >
       <!-- 砍价优化-增加sku流程图 -->
       <image  wx:if="{{ isNewHelpCut }}" slot="sku-actions-top" src='https://img01.yzcdn.cn/upload_files/2021/10/08/FmkPofZymEWunWSELiQXbXIFPibQ.png' class='activity-img' />
      </base-sku>
      <van-toast id="van-toast" />
    </view>
  </behavior-verify>
</page-container>
