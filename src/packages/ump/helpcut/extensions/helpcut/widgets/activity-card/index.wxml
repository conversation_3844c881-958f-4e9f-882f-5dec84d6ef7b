<view class="activity-card {{ isActionSuccess ? '' : 'fail-status' }}">
	<initiator-info/>
	<view class="bargain-slogan">
		{{ sloganCopywriting }}
	</view>
	<view class="bargain-goods">
		<view wx:if="{{ stampCopywriting }}" class="stamp-copywriting">
			{{ stampCopywriting }}
		</view>
		<image
		 class="goods-image"
		 src="{{ goodsDetail.cover }}"
		 mode="aspectFill"
		 bindtap="gotoGoodsDetail"
		/>
		<view class="goods-info">
			<view class="goods-title">{{ goodsDetail.title }}</view>
			<view>
				<text class="goods-stock"> 仅剩:{{ bargainDetail.activityStock }}件</text>
				<view>
					<view class="goods-price">
						<text class="price-lower">最低价 ¥</text>
						<text class="price-yuan">{{ minPriceYuan }}</text>
						<text class="price-fen">.{{ minPriceFen }}</text>
						<text class="origin-price"> ¥{{ goodsDetail.originPrice }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</view>
