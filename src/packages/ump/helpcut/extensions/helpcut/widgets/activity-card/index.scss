.activity-card {
  position: relative;
  display: flex;
  text-align: center;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;

  .bargain-slogan {
    color: #8d3737;
    font-size: 14px;
    margin-top: 6px;
    font-weight: 500;
  }

  .bargain-goods {
    display: flex;
    padding: 25px 15px 24px 15px;

    .stamp-copywriting {
      color: #f72a3e;
      position: absolute;
      display: inline-block;
      transform: rotate(-20deg);
      bottom: 20%;
      right: 2%;
      border: solid 2px;
      padding: 0 8px 0 8px;
      z-index: 1;
    }

    .goods-image {
      width: 100px;
      height: 100px;
      border-radius: 3px;
    }

    .goods-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-left: 10px;
      text-align: left;

      .goods-stock {
        padding: 2px 6px;
        margin-top: 8px;
        color: #fff;
        font-size: 12px;
        text-align: center;
        background-color: #f44;
        border-radius: 10px;
        box-sizing: border-box;
      }

      .goods-title {
        font-size: 14px;
        overflow: hidden;
        color: #333;
        text-overflow: ellipsis;
      }

      .goods-price {
        font-size: 12px;
        margin-top: 20px;

        .price-lower,
        .price-fen {
          color: #f44;
          font-weight: 500;
        }

        .price-yuan {
          color: #f44;
          font-size: 18px;
          font-weight: 500;
        }

        .origin-price {
          color: #666;
          margin-left: 4px;
          text-decoration: line-through;
        }
      }
    }
  }

  &.fail-status {
    .bargain-slogan {
      color: #ccc;
    }

    .bargain-goods {
      .goods-info {
        .goods-stock {
          background-color: #ccc;
        }

        .goods-title {
          color: #ccc;
        }

        .goods-price {
          .price-lower,
          .price-fen,
          .price-yuan,
          .origin-price {
            color: #ccc;
          }
        }
      }
    }
  }
}
