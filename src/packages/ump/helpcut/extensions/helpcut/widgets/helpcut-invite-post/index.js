/* eslint-disable @youzan-open/tee/no-platform-field */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import authorize from 'shared/utils/authorize';
import { generatePoster } from '../../common/api';
import { getShareParams } from 'shared/utils/share-params';
import { sendLogger } from '../../common/utils';

const shareParams = getShareParams('poster');

const app = getApp();
const loadImage = (src) =>
  app.downloadFile({ url: src }).then((result) => result.tempFilePath);

RantaWidget({
  data: {
    showPoster: false,
    posterUrl: '',
    actions: [
      {
        name: '微信好友',
        icon: 'wechat',
        type: 'native',
        openType: 'share',
      },
      {
        name: '分享海报',
        // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
        icon: 'https://img01.yzcdn.cn/upload_files/2021/10/21/FqByUQkq0hmPFrrQcliaTwE7N9Mn.png',
        type: 'poster',
      },
    ],
  },

  attached() {
    mapState(this, [
      'showActionSheet',
      'bargainDetail',
      'initiatorInfo',
      'queryOptions',
      'goodsDetail',
      'isNewHelpCut',
      'shopInfo',
    ]);
    mapActions(this, ['SET_SHOW_ACTION_SHEET']);
  },

  observers: {
    showActionSheet(val) {
      if (val) {
        sendLogger({
          et: 'view', // 事件类型
          ei: 'sharecard', // 事件标识
          en: '分享组件曝光', // 事件名称
          params: {
            page_type: 'helpcut',
            component: 'sharecard',
          }, // 事件参数
        });
      }
    },
  },

  methods: {
    // 选择菜单
    onSelect(e) {
      const { type } = e.detail;
      if (type === 'native') {
        sendLogger({
          et: 'click', // 事件类型
          ei: 'share_miniprogram', // 事件标识
          en: '微信好友点击', // 事件名称
          params: {
            page_type: 'helpcut',
          },
        });
        return;
      }

      if (type === 'poster') {
        // 海报
        sendLogger({
          et: 'click', // 事件类型
          ei: 'share_poster', // 事件标识
          en: '分享', // 事件名称
          params: {
            share_cmpt: 'poster',
            page_type: 'helpcut',
          },
        });
      }

      this.getPoster();
    },

    // 关闭邀请菜单弹窗
    onCancel() {
      this.SET_SHOW_ACTION_SHEET(false);
    },

    // 关闭海报
    onClose() {
      this.setYZData({
        showPoster: false,
      });
    },

    getPoster() {
      const page = 'packages/ump/helpcut/index';
      const pageQuery = { ...this.data.queryOptions, ...shareParams };
      const { name } = this?.shopInfo || {};
      const { cover, title, alias } = this.data.goodsDetail;
      const { minPrice, originalPrice } = this.data.bargainDetail;
      const data = {
        alias,
        page,
        query: pageQuery,
        shopName: name,
        goods: {
          originalPrice,
          minPrice,
          title,
          cover,
        },
      };

      wx.showLoading({ title: '正在生成' });
      generatePoster(data)
        .then((res) => {
          if (res.img) return loadImage(res.img);
          return Promise.reject();
        })
        .then((url) => {
          wx.hideLoading();
          this.setYZData({
            posterUrl: url,
            showPoster: true,
          });
          this.onCancel();
        })
        .catch(() => {
          wx.hideLoading();
          wx.showToast({
            title: '生成海报失败',
            icon: 'none',
          });
        });
    },

    // 保存海报
    savePoster() {
      const { posterUrl } = this.data;
      if (!posterUrl) {
        return;
      }

      wx.showLoading({ title: '保存中' });
      authorize('scope.writePhotosAlbum')
        .then(() => {
          this.saveShareImage(posterUrl)
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000,
              });
              this.onClose();
            })
            .catch((err) => {
              console.log(err);
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000,
              });
            });
        })
        .catch(() => {
          wx.hideLoading();
          wx.showModal({
            content: '需要同意将分享图片保存到相册，点击确定后跳转至设置页操作',
            success: (e) => {
              if (e.cancel) return;
              wx.openSetting({
                success: ({ authSetting }) => {
                  if (authSetting['scope.writePhotosAlbum']) {
                    this.savePoster();
                  }
                },
              });
            },
          });
        });
    },

    // 保存图片
    saveShareImage(url) {
      return new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: url,
          success: resolve,
          fail: reject,
        });
      });
    },
  },
});
