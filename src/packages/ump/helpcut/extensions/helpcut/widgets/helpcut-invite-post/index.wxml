<view>
  <van-share-sheet
    wx:if="{{isNewHelpCut}}"
    show="{{ showActionSheet }}"
    options="{{ actions }}"
    cancel-text="取消"
    bind:cancel="onCancel"
    bind:click-overlay="onCancel"
    bind:select="onSelect"
  >
    <view slot="description">
      <image src='https://img01.yzcdn.cn/upload_files/2021/10/08/FguhvUXohWFi3QM2uKs4IhMp6vQv.png' class='activity-step-img' />
    </view>
  </van-share-sheet>
  <!-- 海报弹层 -->

  <van-popup
    show="{{ showPoster }}"
    custom-class="invite-pop"
  >
    <view
      bindtap="onClose"
      class="invite-pop-close"
    >
      <van-icon name="close" slot="close-icon"></van-icon>
    </view>
    <view class="invite-pop-content">
      <image
        class="invite-pop-content__img"
        src="{{ posterUrl }}"
      >
      </image>
    </view>
    <view
      class="invite-pop-btn--bottom"
      bind:tap="savePoster"
    >
      保存图片
    </view>
  </van-popup>
</view>
