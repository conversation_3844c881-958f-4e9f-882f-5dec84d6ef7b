.helpcut-carousel {
  position: absolute;
  top: 7px;
  left: 10px;
  min-width: 210px;
  height: 32px;
  z-index: 22;
  overflow: hidden;
  color: #fff;
}
.swipe {
  height: 32px;
  color: #fff;
  font-size: 12px;
  line-height: 16px;
}


.item {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  height: 32px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 16px;
  padding: 0 5px;
}

.avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
}

.text {
  flex: 1;
  margin-right: 1px;
  line-height: normal;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.move-enter-active {
  animation: move-in .5s;
}

.move-leave-active {
  animation: move-out .3s;
  animation-fill-mode: forwards;
}

@keyframes move-in {
  0% {
    transform: translateY(32px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes move-out {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-32px);
  }
}
