import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';

RantaWidget({
  data: {
    helpcutRecords: [],
    topBarHeight: 0,
  },

  attached() {
    mapState(this, ['bargainUser']);
  },

  ready() {
    this.startAnimation(this.data.bargainUser);
  },

  methods: {
    async startAnimation(recordsCarousel = []) {
      const recordLen = recordsCarousel.length;

      if (!recordLen) {
        return;
      }

      const start = (index, recordLen, isInit) => {
        const nextIndex = index === recordLen - 1 ? 0 : index + 1;

        // 初始化动画参数，必须每次都清除一下
        const helpcutRecords = recordsCarousel.map((helpcutRecord) => {
          helpcutRecord.showEnter = false;
          helpcutRecord.showLeave = false;
          return helpcutRecord;
        });

        this.setYZData({
          helpcutRecords,
        });
        this.move(helpcutRecords, index, isInit, () => {
          start(nextIndex, recordLen);
        });
      };

      const startIndex = 0;
      start(startIndex, recordLen, true);
    },

    async move(helpcutRecords, index, isInit, next) {
      const INIT_INTERVAL = 1000;
      const ENTRY_INTERVAL = 2000;
      const WAIT_INTERVAL = 2000;
      const LEAVE_INTERVAL = 300;
      const entryInterval = isInit ? INIT_INTERVAL : ENTRY_INTERVAL;

      await this.timeoutPromise(
        this.moveIn.bind(this, { helpcutRecords, index }),
        entryInterval
      );
      await this.timeoutPromise(
        this.moveOut.bind(this, { helpcutRecords, index }),
        WAIT_INTERVAL
      );
      await this.timeoutPromise(next, LEAVE_INTERVAL);
    },

    timeoutPromise(fn, interval) {
      return new Promise((resolve) => {
        setTimeout(() => {
          fn();
          resolve();
        }, interval);
      });
    },

    moveIn({ helpcutRecords, index }) {
      const helpcutRecord = helpcutRecords[index];
      helpcutRecord.showEnter = true;
      helpcutRecord.showLeave = false;
      this.setYZData({
        [`helpcutRecords[${index}]`]: helpcutRecord,
      });
    },

    moveOut({ helpcutRecords, index }) {
      const helpcutRecord = helpcutRecords[index];
      helpcutRecord.showEnter = false;
      helpcutRecord.showLeave = true;
      this.setYZData({
        [`helpcutRecords[${index}]`]: helpcutRecord,
      });
    },
  },
});
