<view
  class="helpcut-carousel"
>
  <view
    class="swipe"
  >
    <block wx:for="{{ helpcutRecords }}" wx:for-item="helpCutUser" wx:key="index">
      <view
        wx:if="{{ helpCutUser.showEnter || helpCutUser.showLeave }}"
        class="item {{ helpCutUser.showEnter ? 'move-enter-active': '' }} {{ helpCutUser.showLeave ? 'move-leave-active': '' }} "
      >
        <image
          class="avatar"
          src="https://img01.yzcdn.cn/public_files/2019/03/13/36455f825857a9a44e13f4c147731e1c.png"
        />
        <text class="text">{{ helpCutUser.message }}</text>
      </view>
    </block>
  </view>
</view>
