import WscPage from 'pages/common/wsc-page/index';
const app = getApp();

WscPage({
  data: {
    src: '',
  },

  onShareAppMessage(options) {
    return this.shareConfig;
  },

  handleMessage({ detail }) {
    const { data } = detail;
    const shareList = data.filter((i) => i.type === 'ZNB.share');
    const { config } = shareList[shareList.length - 1];

    this.shareConfig = config;
  },

  makeQuerys(data) {
    return Object.entries(data)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
  },

  onLoad(options) {
    const kdtId = app.getKdtId();
    const { from, shopId, type } = options;
    let src = 'https://h5.youzan.com/wscshop/booking';
    if (from === 'usercenter') {
      src += `/list?kdt_id=${kdtId}&type=${type}`;
    } else {
      const isFromInvite = from === 'invite';
      const querys = this.makeQuerys({
        ...options,
        kdt_id: isFromInvite ? shopId : kdtId,
      });

      src += isFromInvite ? `/share?${querys}` : `/confirm?${querys}`;
    }

    this.setYZData({
      src,
    });
  },
});
