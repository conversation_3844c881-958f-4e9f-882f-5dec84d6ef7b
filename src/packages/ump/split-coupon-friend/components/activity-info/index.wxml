<view class="split-coupon-friend-activity-info">
	<view class="split-coupon-friend-activity-info__container {{ activityStatus.button ? '' : 'split-coupon-friend-activity-info__container_no-button' }}">
		<block wx:if="{{ !isFinishedBeforeJoin }}">
			<view class="split-coupon-friend-activity-info__tip">{{ activityStatus.tipText }}</view>
			<!-- 优惠券信息 -->
			<view class="split-coupon-friend-activity-info__coupon">
				<coupon
				 wx:if="{{ hasResult }}"
				 coupon="{{ couponInfo }}"
				 auto-format-value="{{ true }}"
				 custom-coupon-box-action-class="split-coupon-friend-activity-info__coupon_action"
				 display-type="flat"
				 bottom-right-icon="{{ activityStatus.couponShowBottomRightIcon }}"
				 hide-btn-wrap="{{ !activityStatus.couponInvalid && !activityStatus.couponShowBottomRightIcon }}"
				 disabled="{{ activityStatus.couponInvalid }}"
				/>
			</view>
			<!-- 立即使用 -->
			<van-button
			 wx:if="{{ activityStatus.couponButton.text }}"
			 custom-class="split-coupon-friend-activity-info__btn split-coupon-friend-activity-info__btn_use"
			 round
			 type="danger"
			 size="large"
			 data-button="{{ activityStatus.couponButton}}"
			 bindtap="handleClickBtn"
			>
				{{ activityStatus.couponButton.text }}
			</van-button>
			<view class="split-coupon-friend-activity-info__friend-tip">
				<view class="split-coupon-friend-activity-info__friend-get">
					{{ activityStatus.helpGetTip }}
				</view>
				<view wx:if="{{ activityStatus.helpGetTip }}" class="split-coupon-friend-activity-info__coupon-tag">
					<text class="coupon-tag-text">券</text>
					<text class="coupon-tag-text">{{ helpGroupCouponInfo }}</text>
				</view>
			</view>
			<!-- 用户头像 -->
			<friend-avatar wx:if="{{ activityStatus.isShowAvatar }}" disabled="{{ activityStatus.isShowFailTip }}" bind:clickAvatar="handleClickAvatar" />
			<!-- 活动开始倒计时 -->
			<start-time wx:if="{{ activityStatus.isShowStartCountDown }}" time="{{ startDistanceTime }}" />
			<!-- 助力倒计时 -->
			<start-time wx:if="{{ activityStatus.isShowHelpEndCountDown }}" time="{{ helpDistanceTime }}" friend="{{ true }}" />
			<view wx:if="{{ activityStatus.failTip }}" class="split-coupon-friend-activity-info__fail">
				{{ activityStatus.failTip }}
			</view>
		</block>
		<!-- 未参与活动前就结束了 -->
		<view class="finished-before-join" wx:else>
			<image class="finished-before-join__img" src="https://b.yzcdn.cn/public_files/cee60aacb7f1215b07511fd6cc81b8ef.png" alt="" />
			<view class="finished-before-join__text">来晚了，活动已结束</view>
		</view>
		<user-authorize bind:next="join" scene="get_coupon" wx:if="{{ activityStatus.button.type === 'JOIN' || activityStatus.button.type === 'START_NEW'}}">
			<van-button
			 wx:if="{{ activityStatus.button.text }}"
			 custom-class="split-coupon-friend-activity-info__btn {{activityStatus.button.isPlain ? 'split-coupon-friend-activity-info__isPlain':''}}"
			 round
			 plain="{{ activityStatus.button.isPlain }}"
			 type="danger"
			 size="large"
			>
				{{ activityStatus.button.text }}
			</van-button>
		</user-authorize>
		<van-button
		 wx:elif="{{ activityStatus.button.text }}"
		 custom-class="split-coupon-friend-activity-info__btn {{activityStatus.button.isPlain ? 'split-coupon-friend-activity-info__isPlain':''}}"
		 round
		 plain="{{ activityStatus.button.isPlain }}"
		 type="danger"
		 size="large"
		 data-button="{{ activityStatus.button}}"
		 bind:tap="handleClickBtn"
		>
			{{ activityStatus.button.text }}
		</van-button>
	</view>
</view>

