.split-coupon-friend-activity-info {
  padding: 0 16px;
  margin-bottom: 16px;

  &__container {
    background: #fff;
    padding: 18px 16px 16px;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.02), 0 8px 8px 0 rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: hidden;

    &_no-button {
      padding-bottom: 0;
    }
  }

  &__tip {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    margin-bottom: 12px;
  }

  &__coupon {
    margin-bottom: 12px;
    &_action {
      min-width: 0 !important;
    }
  }

  &__friend-tip {
    margin-bottom: 32px;
    display: flex;
    align-items: center;
  }

  &__friend-get {
    font-size: 14px;
    font-weight: 500;
  }

  &__coupon-tag {
    display: flex;
    margin-left: 8px;
    border: 1px solid #f00;
    border-radius: 2px;
    font-size: 0;
    color: #f00;
    letter-spacing: 0;
    padding: 0 4px;
    align-items: center;

    .coupon-tag-text {
      font-size: 12px;
    }

    .coupon-tag-text:first-child {
      padding-right: 1.5px;
      border-right: 1px dashed #f00;
    }

    .coupon-tag-text:last-child {
      padding-left: 1.5px;
    }
  }

  &__fail {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #969799;
    margin-bottom: 16px;
    white-space: nowrap;

    &::before,
    &::after {
      content: '';
      display: block;
      width: 55px;
      height: 1px;
      background: linear-gradient(
        to right,
        rgb(255, 255, 255) 0%,
        rgb(245, 149, 140) 100%
      );
      opacity: 0.3;
      margin-right: 8px;
      flex-grow: 1;
    }

    &::after {
      transform: rotate(180deg);
      margin-right: 0;
      margin-left: 8px;
    }
  }

  @keyframes breathe {
    from {
      transform: scale(1);
    }

    to {
      transform: scale(0.95);
    }
  }

  &__btn {
    height: 48px !important;
    background: linear-gradient(270deg, #ff572f 0%, #ff0438 100%) !important;
    font-size: 18px !important;
    font-weight: 500;

    &_animation {
      animation: breathe 0.4s linear infinite alternate;
    }
  }

  &__isPlain {
    background: #fff !important;
  }

  &__btn_use {
    margin-bottom: 32px;
  }
}

.finished-before-join {
  text-align: center;
  margin-bottom: 24px;

  &__img {
    width: 120px;
    height: auto;
    margin-bottom: 15.6px;
  }

  &__text {
    font-size: 16px;
    color: #969799;
  }
}
