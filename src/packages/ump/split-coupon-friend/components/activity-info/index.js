/* eslint-disable @youzan/dmc/wx-check */
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapGetters, mapMutations } from '@youzan/vanx';
import { useCoupon } from '../../common/help';
import { startGroup } from '../../common/api';

const app = getApp();

VanxComponent({
  mapData: {
    ...mapState([
      'activityInfo',
      'groupEndTime',
      'groupInfo',
      'voucherAlias',
      'hasResult',
    ]),

    ...mapGetters([
      'activityStatusType',
      'isFinishedBeforeJoin',
      'shareCopyText',
      'helpedNum',
      'activityStatus',
      'couponInfo',
      'helpGroupCouponInfo',
      'startDistanceTime',
      'helpDistanceTime',
    ]),
  },

  methods: {
    ...mapMutations([
      'SET_GROUP_END_TIME',
      'SET_VOUCHER_ALIAS',
      'SET_SHEET_SHOW',
    ]),

    goHome() {
      wx.reLaunch({ url: '/packages/home/<USER>/index' });
    },

    handleClickBtn(e) {
      console.log(this.data.couponInfo);
      const btn = e.currentTarget.dataset.button;
      const { type } = btn;
      switch (type) {
        case 'GO_HOME':
          this.goHome();
          break;
        case 'INVITE':
          this.invite();
          break;
        case 'USE_COUPON':
          this.useCoupon();
          break;
        default:
          break;
      }
    },

    useCoupon() {
      const couponId =
        this.data.groupInfo.groupVoucherDTO?.userCouponDTO?.fetchId ?? 0;
      useCoupon(couponId);
    },

    startNew() {
      wx.navigateTo({
        url: `/packages/ump/split-coupon-friend/index?alias=${this.data.activityInfo.alias}`,
      });
    },

    join() {
      return startGroup({
        alias: this.data.activityInfo.alias,
      })
        .then(async (data = {}) => {
          if (
            this.data.voucherAlias &&
            this.data.voucherAlias !== data.voucherAlias
          ) {
            // 当前团结束，并且发起了新的团，需要刷新当前页面，更新状态
            this.startNew();
            return;
          }

          this.SET_GROUP_END_TIME(data.groupEndTime);
          this.SET_VOUCHER_ALIAS(data.voucherAlias);

          this.invite();
        })
        .catch((err = {}) => {
          if (err.code === 100003029) {
            wx.showToast({
              title: '已经超过\n发起次数限制',
              icon: 'none',
            });
            return;
          }

          // 已有未完成的团，跳转到团详情
          if (err.code === 1000030404) {
            this.startNew();
            return;
          }
          wx.showToast({
            title: err.msg || '发起失败',
            icon: 'none',
          });
        });
    },

    handleClickAvatar() {
      const type = this.data.activityStatus.button?.type ?? '';
      if (type === 'INVITE') {
        this.invite();
      }
      if (type === 'JOIN') {
        this.join();
      }
    },

    invite() {
      app.yzlogInstance &&
        app.yzlogInstance.log({
          et: 'click', // 事件类型
          ei: 'click_sharecoupon', // 事件标识
          en: '点击邀请好友', // 事件名称
        });

      this.SET_SHEET_SHOW(true);
    },
  },
});
