<view>
	<van-share-sheet
	 show="{{ sheetShow }}"
	 options="{{ actions }}"
	 cancel-text="取消"
	 bind:cancel="onCancel"
	 bind:click-overlay="onCancel"
	 bind:select="onSelect"
	/>

	<!-- 海报弹层 -->
	<van-popup custom-class="share-popup" show="{{ showPoster }}" bind:click-overlay="onClose">
		<view class="share-popup__close">
			<van-icon
			 name="close"
			 color="#fff"
			 size="21px"
			 bind:tap="onClose"
			/>
		</view>

		<view class="share-popup__content">
			<image class="share-popup__content_img" src="{{ posterUrl }}" />
		</view>

		<van-button round type="danger" custom-class="share-popup__tip" bind:tap="savePoster">
			保存海报，发送给好友
		</van-button>
	</van-popup>
</view>

