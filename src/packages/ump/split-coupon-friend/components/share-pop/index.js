import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapGetters, mapMutations, mapState } from '@youzan/vanx';
import authorize from 'shared/utils/authorize';
import { logShare } from 'packages/ump/utils/ump-logger';
import { fetchPoster } from '../../common/api';
import format from '@youzan/utils/money/format';

VanxComponent({
  data: {
    showPoster: false,
    posterUrl: '',
    actions: [
      {
        name: '微信',
        type: 'native',
        openType: 'share',
        shareType: 'native_custom',
        icon: 'wechat',
        url: '',
      },
      { name: '分享海报', type: 'poster', shareType: 'poster', icon: 'poster' },
    ],
  },

  mapData: {
    ...mapState(['sheetShow', 'shopName', 'activityInfo']),
    ...mapGetters(['weappSharePath']),
  },

  methods: {
    ...mapMutations(['SET_SHEET_SHOW']),

    generateQrCode() {
      if (this.data.posterUrl) {
        this.setYZData({
          showPoster: true,
        });
        this.onCancel();
        return;
      }

      wx.showLoading({ title: '海报生成中...' });
      fetchPoster({
        shopName: this.data.shopName,
        page: this.data.weappSharePath,
        coupon: {
          value: +format(
            this.data.activityInfo.startGroupCoupon?.amount,
            true,
            false
          ),
          unit: '元',
        },
      })
        .then(({ img }) => {
          wx.hideLoading();
          this.setYZData({
            showPoster: true,
            posterUrl: img,
          });
        })
        .catch((err) => {
          wx.hideLoading();
          wx.showToast({
            title: err,
            icon: 'none',
          });
        });
    },

    onSelect(e) {
      const { type, shareType } = e.detail;
      logShare(shareType);
      if (type === 'poster') {
        this.SET_SHEET_SHOW(false);
        this.generateQrCode();
      }
    },

    // 关闭邀请菜单弹窗
    onCancel() {
      this.SET_SHEET_SHOW(false);
    },

    // 关闭海报
    onClose() {
      this.setYZData({
        showPoster: false,
      });
    },

    // 保存海报
    savePoster() {
      const { posterUrl } = this.data;
      if (!posterUrl) {
        return;
      }

      wx.showLoading({ title: '保存中' });
      authorize('scope.writePhotosAlbum')
        .then(() => {
          this.saveShareImage(posterUrl)
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000,
              });
              this.onClose();
            })
            .catch(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000,
              });
            });
        })
        .catch(() => {
          wx.hideLoading();
          wx.showModal({
            content: '需要同意将分享图片保存到相册，点击确定后跳转至设置页操作',
            success: (e) => {
              if (e.cancel) return;
              wx.openSetting({
                success: ({ authSetting }) => {
                  if (authSetting['scope.writePhotosAlbum']) {
                    this.savePoster();
                  }
                },
              });
            },
          });
        });
    },

    // 保存图片 api 调用 saveImageToPhotosAlbum只支持本地文件或微信临时路径
    saveShareImage(url) {
      return new Promise((resolve, reject) => {
        wx.downloadFile({
          url,
          success(res) {
            if (res.statusCode === 200) {
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: resolve,
                fail: reject,
              });
            }
          },
          fail: reject,
        });
      });
    },
  },
});
