<view wx:if="{{ formatRecommendGoods && formatRecommendGoods.length }}" class="recommend">
  <view class="recommend-title">推荐更多商品</view>
  <view class="recommend__list">
    <block wx:for="{{ formatRecommendGoods }}" wx:key="index">
      <view class="recommend-goods" bindtap="gotoGoodsDetail" data-item="{{ item }}" data-index="{{ index }}">
        <view class="recommend-goods__img">
          <image class="goods-image" src="{{item.imgUrl}}" lazy-load="true" mode="aspectFit"></image>
        </view>
        <view class="recommend-goods__title recommend-goods__info">{{ item.title }}</view>
        <view class="recommend-goods__desc recommend-goods__info">
          <view class="price">¥{{ item.price }}</view>
          <view wx:if="{{item.showCouponTag}}" class="tag">可用券</view>
        </view>
      </view>
    </block>
  </view>
</view>