/* eslint-disable @youzan/dmc/wx-check */
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapGetters } from '@youzan/vanx';
import { getBannerId } from '../../common/utils';
import args from '@youzan/weapp-utils/lib/args';

VanxComponent({
  mapData: {
    ...mapGetters(['formatRecommendGoods']),
  },
  methods: {
    gotoGoodsDetail(e) {
      const { item, index } = e.currentTarget.dataset;
      const { alias, algs } = item;
      const bannerId = getBannerId(index);
      const goodsUrl = args.add('/packages/goods/detail/index', {
        alias,
        banner_id: bannerId,
        alg: algs,
      });
      wx.navigateTo({
        url: goodsUrl,
      });
    },
  },
});
