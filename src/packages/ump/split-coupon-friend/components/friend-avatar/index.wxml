<view class="friend-avatar {{ disabled ? 'friend-avatar_disabled' : '' }}">
	<view class="friend-avatar__container">
		<view
		 class="friend-avatar__item-wrap {{ 'friend-avatar__item-wrap_' + avatarStyle.type }}"
		 wx:for="{{ avatarStyle.num }}"
		 wx:key="index"
		 bindtap="handleClickAvatar"
		 data-index="{{ index }}"
		>
			<view wx:if="{{ (index + 1) === avatarStyle.num && avatarStyle.type === 3 }}" class="friend-avatar__ellipsis">
				<view wx:for="{{ [0, 1, 2] }}" wx:key="i" class="friend-avatar__ellipsis-item" />
			</view>
			<view class="friend-avatar__item {{ helpUsers[index] && helpUsers[index].userPicture ? 'hasUser' : '' }}" style="zIndex:{{ avatarStyle.num - index }}">
				<block wx:if="{{ !helpUsers[index] || !helpUsers[index].userPicture }}">
					<van-icon name="plus" size="12px" />
				</block>
				<block wx:else>
					<image class="friend-avatar__img" src="{{ helpUsers[index].userPicture }}" />
					<van-tag custom-class="friend-avatar__nickname" round>{{ helpUsers[index].userName }}</van-tag>
				</block>
			</view>
		</view>
	</view>
</view>

