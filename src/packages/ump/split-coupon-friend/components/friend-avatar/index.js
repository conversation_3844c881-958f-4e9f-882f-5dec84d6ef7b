import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapGetters, mapState } from '@youzan/vanx';

VanxComponent({
  properties: {
    disabled: Boolean,
  },

  data: {},

  mapData: {
    ...mapState(['groupInfo', 'activityInfo']),
    ...mapGetters(['totalNum', 'avatarStyle', 'helpUsers']),
  },

  methods: {
    handleClickAvatar(event) {
      const { index } = event.currentTarget.dataset;
      if (
        this.data.helpUsers[index] &&
        this.data.helpUsers[index].userPicture
      ) {
        return;
      }

      this.triggerEvent('clickAvatar');
    },
  },
});
