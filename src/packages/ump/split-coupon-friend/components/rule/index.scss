.rules-content {
  height: 22px;
}

.rules-tag {
  position: absolute;
  right: 16px;
  top: 17px;
  width: 40px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  font-size: 12px;
  font-weight: 800;
  color: #fff0db;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 11px;
}

.rules-popup {
  border-radius: 12px 12px 0 0;
  padding: 0 16px 7px;
  box-sizing: border-box;

  .popup-title {
    position: relative;
    height: 44px;
    text-align: center;
    font-size: 16px;
    line-height: 44px;
    font-family: PingFangSC-Medium;
  }

  .rules {
    padding-top: 12px;
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    color: #323233;
    .rule:not(:last-child) {
      margin-bottom: 15px;
    }
  }

  .rules-btn {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    margin-top: 46px;
    background-image: linear-gradient(90deg, #ff6034 0%, #ee0a24 100%);
    border-radius: 100px;
    color: #fff;
    text-align: center;
    margin-bottom: 8px;
  }
}
