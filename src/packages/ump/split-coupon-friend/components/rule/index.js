import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapGetters, mapState } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapState(['activityInfo', 'shopName']),
    ...mapGetters(['rules']),
  },
  data: {
    rulesVisible: false,
  },

  methods: {
    toggleRules() {
      this.setYZData({
        rulesVisible: !this.data.rulesVisible,
      });
    },
  },
});
