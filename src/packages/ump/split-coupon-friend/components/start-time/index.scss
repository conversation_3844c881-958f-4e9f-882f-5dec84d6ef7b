.start-time {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #323233;
  letter-spacing: 0;
  margin-bottom: 16px;

  &__label {
    white-space: nowrap;
  }

  &__time {
    margin-left: 4px;
    white-space: nowrap;
    min-width: 65px;
    .text {
      color: #ff0f01;
      font-weight: 500;
    }

    &_more-one-day {
      min-width: 100px;
    }
  }

  &::before,
  &::after {
    content: '';
    display: block;
    width: 55px;
    height: 1px;
    background: linear-gradient(
      to right,
      rgb(255, 255, 255) 0%,
      rgb(245, 149, 140) 100%
    );
    opacity: 0.3;
    margin-right: 8px;
    flex-grow: 1;
  }

  &::after {
    transform: rotate(180deg);
    margin-right: 0;
    margin-left: 8px;
  }
}

.split-coupon-friend-activity-info__help-tips {
  white-space: nowrap;
}

.split-coupon-friend-activity-info__help-num {
  color: #f00;
  font-weight: 500;
}
