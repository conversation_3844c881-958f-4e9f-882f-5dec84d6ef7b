/* eslint-disable @youzan/dmc/wx-check */
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { ONE_DAY } from '../../common/constants';
import { getCurrentPageUrlWithParams } from '../../common/utils';
import { mapGetters } from '@youzan/vanx';

VanxComponent({
  properties: {
    time: {
      type: Number,
      value: 0,
    },
    friend: {
      type: <PERSON><PERSON><PERSON>,
    },
  },

  mapData: {
    ...mapGetters(['needFriend']),
  },

  data: {
    timeData: {},
    ONE_DAY,
  },

  methods: {
    onChange(e) {
      const time = e.detail;
      if (time) {
        time.hours = time.hours < 10 ? `0${time.hours}` : time.hours;
        time.minutes = time.minutes < 10 ? `0${time.minutes}` : time.minutes;
        time.seconds = time.seconds < 10 ? `0${time.seconds}` : time.seconds;
      }
      this.setYZData({
        timeData: time,
      });
    },
    onCountDownFinish() {
      const url = getCurrentPageUrlWithParams();
      wx.redirectTo({
        url: '/' + url,
      });
    },
  },
});
