<van-popup custom-class="friend-popup" show="{{ friendShow }}" bind:click-overlay="closePop">
	<view class="friend-popup__close">
		<van-icon
		 name="close"
		 color="#fff"
		 size="21px"
		 bind:tap="closePop"
		/>
	</view>
	<view class="friend-popup__main">
		<!-- 顶部图片 -->
		<image class="friend-popup__top" src="{{ statusInfo.topImg }}" />
		<!-- 可以助力 -->
		<view wx:if="{{ statusInfo.mainText.length }}" class="friend-popup__text">
			<block wx:for="{{ statusInfo.mainText }}" wx:key="item">
				<text wx:decode="{{ true }}" wx:key="item" bindtap="handleMainTextClick" data-index="{{ index }}">
          {{ item }}
        </text>
			</block>
		</view>
		<!-- 助力完成，显示优惠券信息 -->
		<view class="friend-popup__coupon">
			<coupon
			 wx:if="{{ statusInfo.isShowCoupon && helpResult.winCoupon }}"
			 coupon="{{ coupon }}"
			 auto-format-value="true"
			 display-type="flat"
			 btn-text="去使用"
			 btn-icon="{{ false }}"
			 bind:click-btn="handleClickCoupon"
			/>
		</view>
		<view class="friend-popup__btn-wrap" style="{{ statusInfo.buttonWrapStyle || '' }}">
			<user-authorize bind:next="handleHelp" scene="get_coupon" wx:if="{{ statusInfo.button.type === 'help' }}">
				<van-button
				 custom-class="friend-popup__btn"
				 round
				 type="danger"
				 size="large"
				>
					{{ statusInfo.button.text }}
				</van-button>
			</user-authorize>
			<van-button
			 wx:else
			 custom-class="friend-popup__btn"
			 round
			 type="danger"
			 size="large"
			 bind:tap="hendleBtnClick"
			 data-button="{{ statusInfo.button }}"
			>
				{{ statusInfo.button.text }}
			</van-button>
		</view>
		<view
		 wx:if="{{ statusInfo.subButton }}"
		 class="friend-popup__gohome"
		 bind:tap="handleSubButtonClick"
		 data-button="{{ statusInfo.subButton }}"
		>
			{{ statusInfo.subButton.text }}
		</view>
		</viw>
</van-popup>

