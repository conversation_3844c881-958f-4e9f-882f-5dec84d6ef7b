/* eslint-disable @youzan/dmc/wx-check */
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapMutations, mapGetters } from '@youzan/vanx';
import { useCoupon } from '../../common/help';
import { helpGroup } from '../../common/api';

const app = getApp();

VanxComponent({
  properties: {
    disabled: Boolean,
  },

  data: {
    show: true,
  },

  mapData: {
    ...mapState(['activityInfo', 'voucherAlias', 'friendShow', 'helpResult']),
    ...mapGetters(['statusInfo', 'helpResultStatus', 'coupon']),
  },

  methods: {
    ...mapMutations(['SET_HELP_RESULT', 'SET_FERIEND_SHOW']),

    handleClickCoupon() {
      const couponId = this.data.helpResult?.winCoupon?.fetchId ?? 0;
      useCoupon(couponId);
    },

    // 立即助力
    handleHelp() {
      app.yzlogInstance &&
        app.yzlogInstance.log({
          et: 'click', // 事件类型
          ei: 'click_help', // 事件标识
          en: '点击助力', // 事件名称
        });

      helpGroup({
        voucherAlias: this.data.voucherAlias,
        alias: this.data.activityInfo.alias,
      })
        .then((data) => {
          this.SET_HELP_RESULT(data);
        })
        .catch((msg) => {
          wx.showToast({
            icon: 'none',
            title: msg || '助力失败，稍后重试',
          });
        });
    },

    goHome() {
      wx.reLaunch({ url: '/packages/home/<USER>/index' });
    },

    closePop() {
      this.SET_FERIEND_SHOW(false);
    },

    hendleBtnClick(e) {
      const btn = e.currentTarget.dataset.button;
      const { type } = btn;
      switch (type) {
        case 'goHome':
          this.goHome();
          break;
        case 'startNew':
          this.startNew();
          break;
        default:
          break;
      }
    },

    handleSubButtonClick(e) {
      const btn = e.currentTarget.dataset.button;
      const { type } = btn;

      switch (type) {
        case 'GO_HOME':
          this.goHome();
          break;
        default:
          break;
      }
    },

    startNew() {
      wx.navigateTo({
        url: `/packages/ump/split-coupon-friend/index?alias=${this.data.activityInfo.alias}`,
      });
    },
  },
});
