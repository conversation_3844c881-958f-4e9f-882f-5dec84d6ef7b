const mutations = {
  // 设置页面路径参数
  SET_QUERY_OPTIONS(state, payload) {
    state.queryOptions = payload;
  },

  // 参团后设置团结束时间
  SET_GROUP_END_TIME(state, payload) {
    state.groupEndTime = payload;
  },

  // 参团后设置团凭证
  SET_VOUCHER_ALIAS(state, payload) {
    state.voucherAlias = payload;
  },

  SET_SHOP_NAME(state, payload) {
    state.shopName = payload;
  },

  SET_ACTIVITY_INFO(state, payload) {
    state.activityInfo = payload;
  },

  SET_GROUP_INFO(state, payload) {
    state.groupInfo = payload;
  },

  // 是否是团发起者
  SET_IS_CREATOR(state, payload) {
    state.isCreator = payload;
  },

  SET_RECOMMEND_GOODS(state, payload) {
    state.recommendGoods = payload;
  },

  SET_SHEET_SHOW(state, payload) {
    state.sheetShow = payload;
  },

  SET_FERIEND_SHOW(state, payload) {
    state.friendShow = payload;
  },

  SET_HELP_RESULT(state, payload) {
    state.helpResult = payload;
  },

  SET_HAS_RESULT(state, payload) {
    state.hasResult = payload;
  },

  SET_SHARE_IMAGE(state, payload) {
    state.shareImage = payload;
  },
};

export default mutations;
