import {
  getDetailByAlias,
  getRecommendGoods,
  checkRecommendGoodsCanUseCoupon,
  fetchWeappShareImg,
} from '../common/api';
import format from '@youzan/utils/money/format';
import { userTypeMap, WEAPP_DEFAULT_SHARE_IMG } from '../common/constants';

const app = getApp();

const actions = {
  // 初始化店铺名
  FETCH_SHOP_NAME({ commit }) {
    const shopInfo = app.getShopInfoSync();
    commit('SET_SHOP_NAME', shopInfo.shopName || shopInfo.shop_name);
  },

  // 初始活动详情
  FETCH_ACTIVITY_INFO({ commit, state }) {
    const { alias, voucherAlias } = state.queryOptions || {};
    const hasVoucherAlias = voucherAlias ? { voucherAlias } : {};

    getDetailByAlias({ alias, ...hasVoucherAlias })
      .then(({ activityInfo = {}, groupInfo, userType }) => {
        commit('SET_ACTIVITY_INFO', activityInfo || {});
        commit('SET_GROUP_INFO', groupInfo || {});
        commit('SET_IS_CREATOR', userType === userTypeMap.CREATOR);
        commit('SET_FERIEND_SHOW', userType !== userTypeMap.CREATOR);
        // 团凭证
        const groupVoucherAlias =
          groupInfo?.groupVoucherDTO?.voucherAlias ?? '';
        // 团结束时间
        const groupEndTime = groupInfo?.groupVoucherDTO?.endTime ?? 0;
        commit('SET_VOUCHER_ALIAS', groupVoucherAlias || voucherAlias);
        commit('SET_GROUP_END_TIME', groupEndTime);
        commit('SET_HAS_RESULT', true);

        const recommendGoodsType = activityInfo.recommendGoodsType || 0;
        const recommendGoodsIds = activityInfo.recommendGoodsIds || [];
        const couponId = activityInfo?.startGroupCoupon?.couponId || 0;
        const coupon = activityInfo?.helpGroupCoupon || {};

        fetchWeappShareImg({ couponValue: +format(coupon.amount, true, false) })
          .then((res) => {
            commit('shareImage', res?.img ?? WEAPP_DEFAULT_SHARE_IMG);
          })
          .catch(() => {});

        // 如果是自定义商品，并且自定义商品列表为空
        if (recommendGoodsType === 1 && recommendGoodsIds.length === 0) return;
        getRecommendGoods({
          type: recommendGoodsType,
          goodsIds: recommendGoodsIds,
          couponIds: [couponId],
          offlineId: app.getOfflineId() || 0,
        }).then((data = []) => {
          const recommendGoods = data.splice(0, 8);
          const goodsIdList = recommendGoods.map((item) => item.id);

          if (!goodsIdList.length) return;

          checkRecommendGoodsCanUseCoupon({
            goodsIdList,
            activityId: activityInfo?.startGroupCoupon?.couponId,
          })
            .then((data) => {
              const formatRecommendGoods = recommendGoods.map((goods) => {
                if (data.includes(goods.id)) {
                  goods.showCouponTag = true;
                }
                return goods;
              });

              commit('SET_RECOMMEND_GOODS', formatRecommendGoods);
            })
            .catch(() => {});
        });
      })
      .catch(({ msg }) => {
        wx.showToast({
          duration: 3000,
          title: msg || '获取详情数据失败',
          icon: 'none',
        });
      });
  },
};

export default actions;
