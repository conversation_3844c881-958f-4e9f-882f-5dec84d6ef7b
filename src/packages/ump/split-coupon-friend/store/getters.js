import {
  couponStatusMap,
  getActivityStatusMap,
  DEFAULT_AVATAR,
  friendPopupStatus,
  failStatusMap,
} from '../common/constants';
import fullfillImage from '@youzan/weapp-utils/lib/cdn-image';
import format from '@youzan/utils/money/format';
import {
  formatCoupon,
  formatCouponValidTime,
  secToTime,
} from '../common/utils';
import args from '@youzan/utils/url/args';

const app = getApp();

const getters = {
  // 获取活动当前状态类型
  activityStatusType(state) {
    const {
      activityInfo = {},
      groupInfo = {},
      voucherAlias,
      isCreator,
      groupEndTime,
    } = state;
    const alreadyHelpNum = groupInfo.groupVoucherDTO?.alreadyHelpNum ?? 0;
    const needHelpNum = activityInfo.groupCapacity ?? 0;

    // 是否助力者已满
    const isHelpFull = !!alreadyHelpNum && alreadyHelpNum === needHelpNum;

    if (activityInfo.startAt > Date.now()) {
      // 活动未开始
      return 'BEFORE_START';
    }

    if (!isCreator) {
      // 如果是助力者，直接返回未参与
      return 'BEFORE_JOIN';
    }

    if (activityInfo.endAt > Date.now() && !voucherAlias) {
      // 活动已开始，参与活动前
      return 'BEFORE_JOIN';
    }

    if (
      activityInfo.endAt > Date.now() &&
      voucherAlias &&
      !isHelpFull &&
      groupEndTime > Date.now()
    ) {
      // 活动已开始，参与活动后
      return 'AFTER_JOIN';
    }

    // 助力失败，活动未结束
    if (
      activityInfo.endAt > Date.now() &&
      voucherAlias &&
      !isHelpFull &&
      groupEndTime < Date.now()
    ) {
      return 'HELP_FAIL_BEFORE_ACT_END';
    }

    // 助力完成，活动未结束
    if (activityInfo.endAt > Date.now() && isHelpFull) {
      return 'HELP_END_AND_ACT_NOT_END';
    }

    // 助力完成，活动结束
    if (activityInfo.endAt < Date.now() && isHelpFull) {
      // 优惠券可使用时间
      const couponStatus =
        groupInfo.groupVoucherDTO?.userCouponDTO?.couponStatus ?? 0;

      // 优惠券失效
      if (couponStatus === couponStatusMap.UNAVAILABLE) {
        return 'HELP_END_AND_ACT_END_AND_COUPON_INVALID';
      }

      return 'HELP_END_AND_ACT_END';
    }

    // 助力失败，活动未结束
    if (activityInfo.endAt < Date.now() && !isHelpFull) {
      return 'HELP_FAIL_AFTER_ACT_END';
    }
  },

  // 是否未参与前就已经结束
  isFinishedBeforeJoin(state) {
    const { activityInfo = {}, voucherAlias } = state;

    // voucherAlias 存在说明已经参与过活动了
    // 这个状态需要 用户在参与之前就已经结束
    return activityInfo.endAt < Date.now() && !voucherAlias;
  },

  formatRecommendGoods(state) {
    const { recommendGoods } = state;
    return recommendGoods.map((good) => {
      const { algs, title, price, alias, showCouponTag, imageUrl, goodsId } =
        good;
      const imgUrl = fullfillImage(imageUrl || '', '!200x0.jpg');
      return {
        algs,
        title,
        alias,
        imgUrl,
        id: goodsId,
        showCouponTag,
        price: format(price, true, false),
      };
    });
  },

  helpedNum(state) {
    const { groupInfo } = state;
    return groupInfo?.groupVoucherDTO?.alreadyHelpNum ?? 0;
  },

  activityStatus(state, getters) {
    const { activityInfo, groupInfo } = state;
    const { activityStatusType, helpedNum } = getters;
    const { groupCapacity = 0 } = activityInfo;
    const { startGroupStatus } = groupInfo;
    const activityStatus = getActivityStatusMap({
      startGroupStatus,
      needNum: groupCapacity,
      helpedNum,
    })[activityStatusType];
    return activityStatus;
  },

  // 还需要人数
  needFriend(state, getters) {
    const { activityInfo } = state;
    const { helpedNum } = getters;
    const totalNeedNum = activityInfo?.groupCapacity ?? 0;

    return totalNeedNum - helpedNum;
  },

  couponInfo(state, getters) {
    const { activityInfo, groupInfo } = state;
    const { activityStatus } = getters;
    const { startGroupCoupon = {}, title } = activityInfo;
    const userCouponDTO = groupInfo?.groupVoucherDTO?.userCouponDTO ?? {};
    return formatCoupon({
      canUseStartTime: userCouponDTO.startTime || '',
      canUseEndTime: userCouponDTO.endTime || '',
      name: title,
      ...startGroupCoupon,
      ...(activityStatus.couponExt || {}),
    });
  },

  helpGroupCouponInfo(state) {
    const { activityInfo } = state;

    const { helpGroupCoupon = {} } = activityInfo;

    const value = format(helpGroupCoupon.amount, true, false);
    const rule = format(helpGroupCoupon.useRule, true, false);

    return `满${+rule}减${+value}`;
  },
  startDistanceTime(state) {
    const { activityInfo } = state;
    const { startAt } = activityInfo;

    return Math.max(startAt - Date.now(), 0);
  },

  helpDistanceTime(state) {
    const { groupEndTime } = state;
    return Math.max(groupEndTime - Date.now(), 0);
  },

  weappSharePath(state) {
    const { activityInfo = {}, voucherAlias } = state;
    const kdtId = app.getKdtId();
    const { alias } = activityInfo;
    const weappSharePath = args.add('/packages/ump/split-coupon-friend/index', {
      alias,
      voucherAlias,
      kdt_id: kdtId,
    });
    return weappSharePath;
  },

  totalNum(state) {
    const { activityInfo = {} } = state;
    return activityInfo.groupCapacity ?? 4;
  },

  avatarStyle(state, getters) {
    const { totalNum } = getters;
    if (totalNum <= 4) {
      return {
        type: 1,
        num: totalNum,
      };
    }

    if (totalNum === 5) {
      return {
        type: 2,
        num: totalNum,
      };
    }

    return {
      type: 3,
      num: 4,
    };
  },

  helpUsers(state) {
    const { groupInfo } = state;
    const users = groupInfo?.groupVoucherDTO?.helpUserInfos ?? [];
    return users.map((user) => ({
      ...user,
      userPicture: user.userPicture || DEFAULT_AVATAR,
    }));
  },

  statusInfo(state, getters) {
    const { activityInfo } = state;
    const { helpResultStatus } = getters;
    const startGroupCouponValue = activityInfo?.startGroupCoupon?.amount ?? 0;

    const helpGroupCouponValue = activityInfo?.helpGroupCoupon?.amount ?? 0;

    const statusInfo =
      friendPopupStatus({
        mainCouponValue: +format(startGroupCouponValue, true, false),
        helperCouponValue: +format(helpGroupCouponValue, true, false),
      })[helpResultStatus] || {};

    return statusInfo;
  },

  helpResultStatus(state) {
    const { helpResult } = state;
    const { success, canStartGroup, failStatus } = helpResult;

    // 进入页面，还没有助力
    if (!Object.keys(helpResult).length) {
      return 'BEFORE_HELP';
    }

    // 状态码参考  http://zanapi.qima-inc.com/site/service/view/980218
    // 点击 立即助力 之后，助力成功
    if (success && canStartGroup) {
      return 'AFTER_HELP';
    }

    // 点击 立即助力 之后，并且 活动发起次数已达上限
    if (success && !canStartGroup) {
      return 'AFTER_HELP_AND_ACT_LIMIT';
    }

    // 助力失败，已经领券，并且可以开团
    if (failStatus === failStatusMap.HELPED && canStartGroup) {
      return 'HAS_HELPED';
    }

    // 助力失败，已经领券，并且活动发起次数已达上限
    if (failStatus === failStatusMap.HELPED && !canStartGroup) {
      return 'HAS_HELPED_AND_ACT_LIMIT';
    }

    // 助力已满
    if (failStatus === failStatusMap.FULL && canStartGroup) {
      return 'HELP_FULL';
    }

    // 助力已满，并且活动 次数已达上限
    if (failStatus === failStatusMap.FULL && !canStartGroup) {
      return 'HELP_FULL_AND_ACT_LIMIT';
    }

    // 助力有效期过期，并且能发起新的活动
    if (failStatus === failStatusMap.END && canStartGroup) {
      return 'HELP_EXPIRED';
    }

    // 助力有效期过期，并且不能发起新的活动
    if (failStatus === failStatusMap.END && !canStartGroup) {
      return 'HELP_EXPIRED_AND_ACT_LIMIT';
    }

    // 助力次数已达上限，并且可以发起活动
    if (failStatus === failStatusMap.HELP_LIMIT && canStartGroup) {
      return 'HELP_LIMIT';
    }

    // 助力次数已达上限，并且不能发起发起活动
    if (failStatus === failStatusMap.HELP_LIMIT && !canStartGroup) {
      return 'HELP_LIMIT_AND_ACT_LIMIT';
    }
  },

  coupon(state) {
    const { helpResult, activityInfo } = state;
    const winCoupon = helpResult?.winCoupon ?? {};
    return formatCoupon({
      canUseStartTime: winCoupon.startTime || '',
      canUseEndTime: winCoupon.endTime || '',
      ...activityInfo.helpGroupCoupon,
      name: activityInfo.title,
    });
  },

  rules(state) {
    const { activityInfo } = state;
    const {
      groupCapacity,
      startGroupLimitTimes = 0,
      helpGroupLimitTimes = 0,
      groupValidity,
      startGroupCoupon = {},
    } = activityInfo;
    const rules = [
      `发起者发起助力活动，在活动有效期内邀请${groupCapacity}名好友，好友助力成功后，即可领取专属优惠券；`,
    ];

    if (startGroupLimitTimes) {
      rules.push(
        `每个用户同一时间最多可发起一个分享活动，每人允许发起${startGroupLimitTimes}次，超出后则不可再次发起；`
      );
    } else {
      rules.push(`每个用户同一时间最多可发起一个分享活动；`);
    }

    if (helpGroupLimitTimes) {
      rules.push(
        `每个用户最多可助力${helpGroupLimitTimes}次，超出后则不可再次助力；`
      );
    }

    const validTime = formatCouponValidTime(startGroupCoupon);

    return [
      ...rules,
      `每个分享活动发起后${secToTime(
        groupValidity,
        'dd天hh时mm分'
      )}内未集齐好友则活动失败，无法获得奖励；`,
      `优惠券${validTime}有效，过期后失效，无法使用。`,
    ];
  },

  shareConfig(state, getters) {
    const { shareImage } = state;
    const { weappSharePath } = getters;

    return {
      imageUrl: shareImage,
      title: '点一下帮我助力，你也能领优惠券',
      path: weappSharePath,
    };
  },
};

export default getters;
