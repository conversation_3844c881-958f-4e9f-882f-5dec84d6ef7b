import { getActivityCard } from '../common/helpers';

const FAIL_AVATAR = 'https://b.yzcdn.cn/public_files/60dd74517db353390175e516238f2a2a.png';
const ADD_ICON = 'https://img01.yzcdn.cn/public_files/2018/11/13/67444bebc957bd7e7470995b92c3b03a.png';
const defaultAvatar = 'https://b.yzcdn.cn/public_files/2018/04/25/4d6c6efc8173fb2758323e33ac2d6f67.png';

const getters = {
  // 页面卡片展示
  activityCardInfo(state, getters) {
    const { remainNum } = getters;
    return getActivityCard(state.activity, remainNum);
  },

  // 剩余人数
  remainNum(state) {
    const { joinPersonNum } = state.activity;
    return joinPersonNum - state.users.length;
  },
  // 头像列表
  avatarList(state, getters) {
    const { hordeType } = state.activity;
    const { remainNum, activityCardInfo: { isFail } } = getters;
    const list = [];
    state.users.forEach(v => {
      list.push({
        avatar: v.avatar || defaultAvatar,
        isFail,
      });
    });

    if (remainNum > 0 && hordeType !== 2) {
      const avatar = isFail ? FAIL_AVATAR : ADD_ICON;
      const isShare = !isFail;
      for (let i = 0; i < remainNum; i++) {
        list.push({
          avatar,
          isShare,
        });
      }
    }
    return list;
  },

  // 店铺联系方式
  shopConcat(state) {
    const { areaCode, shopConcat = '' } = state.activity;
    const preCode = areaCode ? `${areaCode}-` : '';

    return `${preCode}${shopConcat}`;
  },

  // 参与弹窗信息
  joinPopInfo(state) {
    const { joinStatus, activity, totalMoney } = state;
    const index = joinStatus;
    const STATUS_MAP = [
      '',
      {
        headerTitle: '你已成功开启1个瓜分团',
        showNumTitle: true,
        title: '人即可瓜分优惠礼包',
        money: activity.totalMoney,
        btnType: 'SHARE',
        btnText: '邀请微信好友一起瓜分',
      },
      {
        preTitle: '你已加入',
        headerTitle: '的瓜分团',
        showNumTitle: true,
        title: '人即可瓜分优惠礼包',
        money: activity.totalMoney,
        btnType: 'SHARE',
        btnText: '邀请微信好友一起瓜分',
      },
      {
        headerTitle: '你已达到活动参与次数上限',
        title: '累计获得优惠券',
        money: totalMoney,
        btnType: 'LIMIT',
        btnText: '立即使用优惠券',
      },
      {
        headerTitle: '你已加入一个瓜分团\n瓜分成功后可加入新团',
        title: '',
        money: 0,
        showFail: true,
        btnType: 'JOINED',
        btnText: '前往我的瓜分团',
      },
      {
        headerTitle: '好友已集齐，立即瓜分吧',
        title: '瓜分优惠礼包',
        money: activity.totalMoney,
        btnType: 'FETCH',
        btnText: '立即领取',
      },
    ];

    return STATUS_MAP[index] || '';
  },
};

export default getters;
