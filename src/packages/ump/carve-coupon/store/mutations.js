import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import money from '@youzan/weapp-utils/lib/money';
import { getRules, getNickName, getJoinUsers } from '../common/helpers';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';

const randomString = makeRandomString(8);
const getBannerId = (activityId, index) =>
  `carve.${activityId}~recommend_fixed~${index + 1}~${randomString}`;

const defaultAvatar =
  'https://b.yzcdn.cn/public_files/2018/04/25/4d6c6efc8173fb2758323e33ac2d6f67.png';
const defaultBg =
  'https://img01.yzcdn.cn/public_files/d1b2beffd9ec29bfef55500ec47b6968.png';

const mutations = {
  SET_QUERY_OPTIONS(state, payload) {
    state.queryOptions = payload;
  },

  INIT_DATA(state, payload) {
    const { goods, totalMoney, users, customConfig } = payload;

    const activity = {
      ...payload,
      totalMoney: totalMoney / 100,
      goods: null,
      users: null,
      customConfig: null,
    };

    const goodsList = goods.map((v, index) => {
      const { title, price, alias, showCouponTag, picture } = v;
      const imgUrl = cdnImage(picture[0].url || '', '!200x0.jpg');

      return {
        title,
        alias,
        imgUrl,
        showCouponTag,
        bannerId: getBannerId(activity.activityId, index),
        price: money(price, true, false).toYuan(),
      };
    });

    const { themeType, themeImage } = customConfig;
    const bgImg = themeType === 2 ? themeImage : defaultBg;
    const backgroundStyle = `background-image: url(${bgImg})`;

    state.nickName = getNickName(users, activity);
    state.rules = getRules(activity, state.shopName);
    state.users = getJoinUsers(payload);
    state.goodsList = goodsList;
    state.activity = activity;
    state.customConfig = customConfig || {};
    state.backgroundStyle = backgroundStyle;
  },

  // 设置参与活动用户
  SET_USER_LIST(state, payload) {
    state.activityUser = payload.map((v) => {
      return {
        ...v,
        divideMoney: v.divideMoney / 100,
        avatar: v.avatar || defaultAvatar,
      };
    });
  },

  // 设置本包信息
  SET_JOIN_RESULT(state, payload) {
    state.activity = { ...state.activity, ...payload, isJoined: true };
  },

  // 设置其他团信息
  SET_OTHER_HORDDE_ID(state, payload) {
    state.otherHordeId = payload;
  },

  SET_POP_VISIBLE(state, payload) {
    state.joinStatus = payload || 0;
    state.showPop = !state.showPop;
  },

  // 设置瓜分结果
  SET_OPEN_RESULT(state, payload) {
    const moneyMap = {};
    const {
      totalMoney,
      curUserId,
      joinPersonNum,
      grantType,
      joinNum,
    } = state.activity;
    let currentUserMoney = 0;
    let carved = 0;
    payload.userHordeInfos.forEach((v) => {
      carved += v.money;
      if (v.yzUid === curUserId) {
        currentUserMoney = v.money / 100;
      }
      moneyMap[v.yzUid] = v.money;
    });

    const aveMoney = (totalMoney * 100 - carved) / (joinPersonNum - joinNum);
    let maxIndex = 0;
    let maxMoney = 0;
    const currentJoinedList = state.users.map((user, index) => {
      const realMoney = !user.isMock ? moneyMap[user.userId] : aveMoney;
      if (realMoney > maxMoney) {
        maxMoney = realMoney;
        maxIndex = index;
      }
      return {
        ...user,
        money: (realMoney / 100).toFixed(2),
      };
    });
    // 随机瓜分
    if (grantType === 2) {
      currentJoinedList[maxIndex].hasMaxMoney = true;
    }

    state.currentUserMoney = currentUserMoney;
    state.activity.isOpened = true;
    state.currentJoinedList = currentJoinedList;
  },

  // 邀请弹层
  SET_SHARE_VISIBLE(state, payload) {
    state.showShare = payload;
  },

  SET_SHOP_NAME(state, payload) {
    state.shopName = payload;
  },

  SET_NICK_NAME(state, payload) {
    state.nickName = payload;
  },

  // 设置领取限制，获得的金额
  SET_TOTAL_MONEY(state, payload) {
    state.totalMoney = payload / 100;
  },
};

export default mutations;
