const app = getApp();

// 获取活动信息
export const getActivityInfo = (query) =>
  app.request({
    query,
    path: '/wscump/coupon/carve_coupon_activity_info.json',
  });

// 获取参与的用户信息
export const getUserList = (query) =>
  app.request({
    query,
    options: {
      useCdn: true,
    },
    path: '/wscump/split-coupon/success-join-records.json',
  });

// 领取瓜分包
export const fetchPackage = (data) =>
  app.request({
    data,
    method: 'POST',
    path: '/wscump/coupon/carve_coupon.json',
  });

// 打开瓜分包
export const openPackage = (query) =>
  app.request({
    query,
    path: '/wscump/coupon/carve_coupon_result.json',
  });

// 优惠券立即使用跳转逻辑
// query: { alias, groupType }

export const couponUseRedirect = (query) =>
  app.request({
    path: '/wscump/coupon/coupon_use_redirect.json',
    query,
  });
