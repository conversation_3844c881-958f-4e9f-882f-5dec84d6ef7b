import { moment as formatDate } from 'utils/time';
import navigate from 'shared/utils/navigate';
import { couponUseRedirect } from './api';

const breakValidTime = (time) => {
  if (!time) {
    return '';
  }
  const t = time / 60 / 1000;
  const day = Math.floor(t / (60 * 24));
  const hour = Math.floor((t - day * 60 * 24) / 60);
  const minute = Math.floor(t - day * 60 * 24 - hour * 60);
  return `${day}日${hour}时${minute}分`;
};

// 获取活动规则
export const getRules = (activity) => {
  const {
    joinPersonNum,
    grantType,
    totalMoney,
    ticket = {},
    hordeValidTime,
    endAt,
    startAt,
  } = activity;
  const { validEndTime, validStartTime } = ticket;
  const activityTime = `${formatDate(
    startAt,
    'YYYY.MM.DD HH:mm:ss'
  )}-${formatDate(endAt, 'YYYY.MM.DD HH:mm:ss')}`;
  const { dateType, canUseDate, limit, allGoodsCanUse, payMsg } =
    ticket.rules || {};
  const validTimeText = breakValidTime(hordeValidTime);
  const canUseDateText =
    dateType === 1
      ? `领取后在${formatDate(
          validStartTime,
          'YYYY.MM.DD HH:mm:ss'
        )}-${formatDate(validEndTime, 'YYYY.MM.DD HH:mm:ss')}内有效`
      : `瓜分成功后${canUseDate.slice(3)}`;
  const goodsLimt = allGoodsCanUse ? '全部商品可用' : '部分商品可用';
  const limitText =
    limit === '无限'
      ? '每人不限瓜分次数'
      : `每人允许瓜分${limit}次，超出后则不可再次参与瓜分`;
  const GRANT_TYPE_MAP = {
    1: '平均',
    2: '随机',
  };

  return [
    `1. 用户可邀请好友瓜分优惠礼包，满${joinPersonNum}人则${GRANT_TYPE_MAP[grantType]}瓜分总金额为${totalMoney}元的优惠礼包；`,
    `2. 每个用户同一时间最多瓜分一个优惠礼包，${limitText}；`,
    `3. 活动时间为${activityTime}，在活动时间内，每个优惠礼包发起后${validTimeText}内未集齐好友则瓜分失败，无法获得奖励；`,
    `4. 优惠礼包${canUseDateText}，过期后失效，无法使用；`,
    `5.优惠礼包${goodsLimt}，${payMsg}。`,
  ];
};

// 优惠券立即使用
export const useCoupon = (activity, queryOptions) => {
  const { ticket = {} } = activity;
  const { alias } = ticket;
  // 券码类型，收藏有礼目前只支持优惠券: card
  couponUseRedirect({
    ...queryOptions,
    alias,
    groupType: 'card',
  })
    .then((res) => {
      const {
        isSwitchTab = true,
        weappUrl = '/packages/home/<USER>/index',
      } = res || {};
      isSwitchTab
        ? navigate.switchTab({ url: weappUrl })
        : navigate.navigate({ url: weappUrl });
    })
    .catch(({ msg }) => {
      wx.showToast({
        title: msg,
        icon: 'none',
      });
    });
};

export const getActivityCard = (activity, remainNum) => {
  let showMoney = false;
  let showTime = false;
  let isFail = false;
  let showNumTitle = false;
  let title = '';
  let desc = '';
  let avatarSlot = '';
  let cardClass = '';
  let btns = [];
  const {
    endAt,
    hordeRemainNum,
    remainTime,
    isInvalid,
    shopConcat,
    isJoined,
    hordeId,
    isOpened,
    hordeType,
    joinPersonNum,
    yzUids,
  } = activity;

  // 请求有部分用户查不到头像昵称，但是应该算有效用户
  const reallyRemainNum = joinPersonNum.length - yzUids.length;

  // 待领取
  if (!hordeId) {
    title = '点击 [领取] 瓜分优惠礼包';
    showMoney = true;
    avatarSlot = '';
    cardClass = 'fetch-card';
    btns = [
      {
        key: 0,
        text: '领取',
        type: 'FETCH_NEW',
        btnClass: 'btn__red',
      },
    ];
  }

  // 活动进行中
  if (hordeId && !isOpened) {
    showNumTitle = true;
    showTime = true;
    cardClass = '';
    avatarSlot = 'top';
    btns = isJoined
      ? [
          {
            key: 1,
            text: '邀请微信好友一起瓜分',
            type: 'SHARE',
            btnClass: 'btn__red btn__wechat',
          },
        ]
      : [
          {
            key: 2,
            text: '立即瓜分',
            type: 'FETCH_OLD',
            btnClass: 'btn__red btn__top',
          },
          {
            key: 3,
            text: '瓜分新礼包',
            type: 'FETCH_NEW',
            btnClass: 'btn__yellow btn__bottom',
          },
        ];
  }

  // 瓜分成功
  if (
    isJoined &&
    (remainNum === 0 || hordeType === 2 || reallyRemainNum === 0) &&
    isOpened
  ) {
    const { payMsg, allGoodsCanUse } = activity.ticket.rules;
    const preDesc = allGoodsCanUse ? '全部商品' : '部分商品';
    const nextText = payMsg === '无使用门槛' ? '满任意金额可用' : payMsg;

    cardClass = 'success-card';
    title = '恭喜你，成功获得优惠券';
    showNumTitle = false;
    showMoney = true;
    showTime = false;
    desc = `${preDesc}${nextText}`;
    btns = [
      {
        key: 4,
        text: '立即使用',
        type: 'USE',
        btnClass: 'btn__red btn__top',
      },
      {
        key: 5,
        text: '瓜分新礼包',
        type: 'FETCH_NEW',
        btnClass: 'btn__yellow btn__bottom',
      },
    ];
  }

  // 已过期
  if (remainTime <= 0 && remainNum > 0 && hordeType !== 2) {
    title = '很遗憾，瓜分时间已过期';
    avatarSlot = 'bottom';
    cardClass = 'fail-card';
    showNumTitle = false;
    showMoney = false;
    showTime = false;
    isFail = true;
    btns = [
      {
        key: 6,
        text: '瓜分新礼包',
        type: 'FETCH_NEW',
        btnClass: 'btn__yellow',
      },
    ];
  }

  // 已满员
  if (remainNum === 0 && !isJoined) {
    title = '很遗憾，礼包已满员';
    avatarSlot = 'bottom';
    cardClass = 'fail-card';
    showNumTitle = false;
    showMoney = false;
    showTime = false;
    isFail = true;
    btns = [
      {
        key: 7,
        text: '瓜分新礼包',
        type: 'FETCH_NEW',
        btnClass: 'btn__yellow',
      },
    ];
  }

  // 活动结束
  if (Date.now() - endAt >= 0) {
    title = '哎呀，来晚啦，活动已结束';
    avatarSlot = '';
    cardClass = 'fail-card';
    showNumTitle = false;
    showMoney = false;
    showTime = false;
    isFail = true;
    btns = [];
  }

  // 礼包派发完了
  if (hordeRemainNum <= 0 && !isJoined && !hordeId) {
    title = '哎呀，来晚啦，礼包派发完啦';
    avatarSlot = '';
    showNumTitle = false;
    showMoney = false;
    showTime = false;
    isFail = true;
    cardClass = 'fail-card';
    btns = [];
  }

  // 商家提前停止活动
  if (isInvalid && !hordeId) {
    showNumTitle = false;
    showMoney = false;
    showTime = false;
    isFail = true;
    title = '活动已提前停止';
    avatarSlot = '';
    cardClass = 'fail-card';
    desc = `如有问题，请拨打电话联系客服\n${shopConcat}`;
    btns = [];
  }

  return {
    title,
    avatarSlot,
    showNumTitle,
    showMoney,
    showTime,
    isFail,
    desc,
    btns,
    cardClass,
  };
};

// 临时修复分享时title的nickName会不出现的情况
export const getNickName = (users, activity) => {
  let result = '你的好友';
  const { curUserId } = activity;
  users.forEach((item) => {
    if (item.userId === curUserId) {
      result = item.nickName;
    }
  });
  return result;
};

export const getJoinUsers = (activity) => {
  const { users, hordeType } = activity;

  if (hordeType !== 2) {
    return users;
  }

  // 模拟成团
  const finalUser = [...users];
  const restCoupon = activity.joinPersonNum - (activity.joinNum || 0);
  for (let index = 0; index < restCoupon; index++) {
    finalUser.push({
      nickName: '匿名用户',
      isMock: true,
      avatar:
        'https://b.yzcdn.cn/public_files/2018/04/25/4d6c6efc8173fb2758323e33ac2d6f67.png',
    });
  }
  return finalUser;
};
