<view class="avatar-list {{ activityCardInfo.avatarSlot === 'bottom' ? 'avtart-list__top': '' }} {{ avatarList.length === 4 ? 'avatar-list__len' : '' }}">
  <view
    wx:for="{{ avatarList }}"
    wx:key="{{ index }}"
    class="avatar {{item.isFail ? 'avatar__opacity' : '' }}"
  >
    <image
      wx:if="{{ !item.isShare }}"
      src="{{ item.avatar }}"
    />
    <button
      wx:else
      open-type="share"
      class="default-avatar"
    />
  </view>
</view>