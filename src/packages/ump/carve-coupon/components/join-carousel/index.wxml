<view
  class="trade-carousel"
  style="top: 15px"
>
  <view
    class="trade-carousel__swipe"
  >
    <block wx:for="{{ tradeRecords }}" wx:for-item="tradeRecord" wx:key="{{ index }}">
      <view
        wx:if="{{ tradeRecord.showEnter || tradeRecord.showLeave }}"
        class="trade-carousel__swipe-item {{ tradeRecord.showEnter ? 'move-enter-active': '' }} {{ tradeRecord.showLeave ? 'move-leave-active': '' }} "
      >
        <view class="trade-carousel__swipe-avatar">
          <image src="{{ tradeRecord.avatar }}" />
        </view>
        <text class="trade-carousel__swipe-text">
          {{ tradeRecord.nickname }}同学 获得<text class="money">{{ tradeRecord.divideMoney }}</text>元优惠券
        </text>
      </view>
    </block>
  </view>
</view>
