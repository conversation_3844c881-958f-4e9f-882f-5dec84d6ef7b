.trade-carousel {
  position: fixed;
  top: 15px;
  left: 10px;
  min-width: 210px;
  height: 32px;
  // 比 goods-fixed-info高一点
  z-index: 22;
  overflow: hidden;
  color: #fff;

  &__swipe {
    height: 32px;
    color: #fff;
    font-size: 12px;
    line-height: 16px;

    &-item {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 16px;
      padding-right: 8px;
      height: 24px;
      line-height: 24px;
      border-radius: 12px;
    }

    &-avatar {
      margin-right: 9px;
      width: 22px;
      height: 22px;
      border-radius: 50%;
      border: 1px solid #fff;
      overflow: hidden;
      box-sizing: border-box;

      image {
        width: 100%;
        height: 100%;
      }
    }

    &-text {
      flex: 1;
      margin-right: 1px;
      line-height: normal;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  
    .money {
      color: #ffcb00;
    }
  }
}

.move-enter-active {
  animation: move-in .5s;
}

.move-leave-active {
  animation: move-out .3s;
  animation-fill-mode: forwards;
}

@keyframes move-in {
  0% {
    transform: translateY(32px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes move-out {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-32px);
  }
}
