import { VanxComponent } from 'shared/common/base/wsc-component';
import { mapState } from '@youzan/vanx';

VanxComponent({
  data: {
    tradeRecords: [],
    topBarHeight: 0,
  },

  computed: {
    ...mapState([
      'activityUser'
    ])
  },

  watch: {
    activityUser(newVal) {
      this.startAnimation(newVal);
    }
  },

  methods: {
    async startAnimation(tradeRecordsCarousel = []) {
      const {
        length: tradeRecordLength
      } = tradeRecordsCarousel;

      if (!tradeRecordLength) {
        return;
      }

      const start = (index, tradeRecordLength, isInit) => {
        const nextIndex = index === (tradeRecordLength - 1) ? 0 : (index + 1);

        // 初始化动画参数，必须每次都清除一下
        const tradeRecords = tradeRecordsCarousel.map(tradeRecord => {
          tradeRecord.showEnter = false;
          tradeRecord.showLeave = false;
          return tradeRecord;
        });

        // 进入的时候把动画状态都清除掉；vue会自己做；
        this.setData({
          __isAnimation: true,
          tradeRecords,
        }, () => {
          this.move(tradeRecords, index, isInit, () => {
            start(nextIndex, tradeRecordLength);
          });
        });
      };

      const startIndex = 0;
      start(startIndex, tradeRecordLength, true);
    },

    async move(tradeRecords, index, isInit, next) {
      const INIT_INTERVAL = 1000;
      const ENTRY_INTERVAL = 2000;
      const WAIT_INTERVAL = 2000;
      const LEAVE_INTERVAL = 300;
      const entryInterval = isInit ? INIT_INTERVAL : ENTRY_INTERVAL;

      await this.timeoutPromise(this.moveIn.bind(this, { tradeRecords, index }), entryInterval);
      await this.timeoutPromise(this.moveOut.bind(this, { tradeRecords, index }), WAIT_INTERVAL);
      await this.timeoutPromise(next, LEAVE_INTERVAL);
    },

    timeoutPromise(fn, interval) {
      return new Promise(resolve => {
        setTimeout(() => {
          fn();
          resolve();
        }, interval);
      });
    },

    moveIn({ tradeRecords, index }) {
      const tradeRecord = tradeRecords[index];
      tradeRecord.showEnter = true;
      tradeRecord.showLeave = false;
      this.setData({
        __isAnimation: true,
        [`tradeRecords[${index}]`]: tradeRecord,
      });
    },

    moveOut({ tradeRecords, index }) {
      const tradeRecord = tradeRecords[index];
      tradeRecord.showEnter = false;
      tradeRecord.showLeave = true;
      this.setData({
        __isAnimation: true,
        [`tradeRecords[${index}]`]: tradeRecord,
      });
    },
  }
});
