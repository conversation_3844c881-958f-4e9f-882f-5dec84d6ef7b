import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapMutations, mapGetters, mapActions } from '@youzan/vanx';
import { useCoupon } from '../../common/helpers';
import navigate from 'shared/utils/navigate';

const successPopIcon =
  'https://b.yzcdn.cn/public_files/fcb296d410297415735e09e7f8bcf01c.png';
const failPopIcon =
  'https://b.yzcdn.cn/public_files/de09db40195e509b7a4e63c7f0876e07.png';

VanxComponent({
  options: {
    multipleSlots: true,
  },

  mapData: {
    ...mapState([
      'activity',
      'otherHordeId',
      'kdtId',
      'showPop',
      'ownerNickName',
      'queryOptions',
    ]),
    ...mapGetters(['joinPopInfo', 'remainNum']),
  },

  data: {
    show: false,
    successPopIcon,
    failPopIcon,
  },
  methods: {
    ...mapActions(['OPEN_PACKAGE']),
    ...mapMutations(['SET_POP_VISIBLE', 'SET_SHARE_VISIBLE']),

    handleClick() {
      const { btnType } = this.data.joinPopInfo;

      const ACTION_MAP = {
        LIMIT: 'useCoupon',
        JOINED: 'toSelfCoupon',
        FETCH: 'OPEN_PACKAGE',
      };
      const actionName = ACTION_MAP[btnType];
      actionName && this[actionName](true);
      this.SET_POP_VISIBLE();
    },

    // 立即使用
    useCoupon() {
      const { activity = {}, queryOptions = {} } = this.data;
      useCoupon(activity, queryOptions);
    },

    // 前往我的瓜分团
    toSelfCoupon() {
      const { activityId } = this.data.activity;
      const url = `/packages/ump/carve-coupon/index?activityId=${activityId}&hordeId=${this.data.otherHordeId}`;
      navigate.navigate({ url });
    },
  },
});
