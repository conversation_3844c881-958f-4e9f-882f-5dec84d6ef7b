
<van-popup
  wx:if="{{ joinPopInfo }}"
  show="{{ showPop }}"
  custom-class="pop"
  bind:close="SET_POP_VISIBLE"
>
  <view class="pop-header">
    <image
      src="{{ joinPopInfo.showFail ? failPopIcon : successPopIcon }}"
      class="header-img {{ joinPopInfo.showFail ? 'header-img__fail' : '' }}"
    />
    <van-icon
      name="cross"
      class="header-close"
      bind:tap="SET_POP_VISIBLE"
    />
    <!-- 头部标题 -->
    <view class="header-title">
      <block wx:if="{{ joinPopInfo.preTitle }}">{{ joinPopInfo.preTitle }} <text class="strong">{{ ownerNickName }}</text></block>{{ joinPopInfo.headerTitle }}
    </view>
  </view>
  <view class="pop-content">
    <!-- 弹窗标题 -->
    <view
      wx:if="{{ joinPopInfo.title }}"
      class="activity-title"
    >
      <block wx:if="{{ joinPopInfo.showNumTitle }}">仅差<text class="strong"> {{ remainNum }} </text></block>{{ joinPopInfo.title }} 
    </view>
    <!-- 金额展示 -->
    <view
      wx:if="{{ joinPopInfo.money }}"
      class="activity-money"
    >
      <text class="strong">{{ joinPopInfo.money }}</text>元
    </view>
    <!-- 弹窗按钮 -->
    <button
      class="btn btn__red pop-btn {{ joinPopInfo.btnType === 'SHARE' ? 'btn__wechat' : '' }}"
      open-type="{{ joinPopInfo.btnType === 'SHARE' ? 'share' : '' }}"
      bind:tap="handleClick"
    >
      {{ joinPopInfo.btnText }}
    </button>
    <view
      wx:if="{{ joinPopInfo.btnType === 'LIMIT' }}"
      class="invite"
      bind:tap="SET_SHARE_VISIBLE"
    >
      喊好友一起参与 >
    </view>
  </view>
</van-popup>