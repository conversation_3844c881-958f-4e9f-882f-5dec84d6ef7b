@import '../../index.scss';

.pop {
  text-align: center;
  background-color: transparent !important;
  padding-top: 32px;
  border-radius: 8px;
  width: 270px;

  &-header {
    position: relative;
    padding-top: 51px;
    box-sizing: border-box;
    border-radius: 8px 8px 0 0;
    background-image: url(https://b.yzcdn.cn/public_files/1196094dbb6ab9d2ef4c5435f38e1ced.png);
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: bottom center;
    background-color: #fff;

    .header-img {
      width: 143px;
      height: 75px;
      position: absolute;
      top: -32px;
      left: 0;
      right: 0;
      margin: auto;
    }

    .header-img__fail {
      width: 102px;
    }

    .header-title {
      color: #fff;
      font-size: 16px;
      line-height: 21px;
      padding-bottom: 20px;
      font-family: PingFangSC-Medium;
      white-space: pre-wrap;

      .strong {
        color: #fff0b3;
      }
    }

    .header-close {
      position: absolute;
      padding: 7px;
      top: 2px;
      right: 2px;
      color: #fff;
      font-size: 16px;
    }
  }

  &-content {
    position: relative;
    padding: 20px 0 24px;
    background-color: #fff;
    margin-top: -1px;
  }

  &-btn {
    width: 230px;
  }

  .invite {
    margin-top: 16px;
    color: #969799;
    font-size: 14px;
  }
}

.pop-content .activity-money {
  margin: 12px 0 20px;
}
