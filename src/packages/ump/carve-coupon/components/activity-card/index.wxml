<view
  class="activity-card {{ activityCardInfo.cardClass }}"
>
  <text class="money-icon" />

  <!-- 失败图标 -->
  <image
    wx:if="{{ activityCardInfo.isFail }}"
    src="{{ failIcon }}"
    class="fail-icon"
  />

  <slot name="top"></slot>

  <!-- 标题 -->
  <view
    wx:if="{{ !activityCardInfo.showNumTitle }}"
    class="activity-title"
  >
    {{ activityCardInfo.title }}
  </view>
  <view
    wx:else
    class="activity-title"
  >
    仅差<text class="strong">{{ remainNum }}</text>人，即可瓜分<text class="strong">{{ activity.totalMoney }}</text>元优惠红包
  </view>

  <!-- 瓜分总额 -->
  <view
    wx:if="{{ activityCardInfo.showMoney }}"
    class="activity-money"
  >
    <text class="strong">{{ currentUserMoney || activity.totalMoney }}</text>元
  </view>

  <!-- 说明 -->
  <view
    wx:if="{{ activityCardInfo.desc }}"
    class="activity-desc"
  >{{ activityCardInfo.desc }}</view>

  <!-- 倒计时 -->
  <view
    wx:if="{{ countDown && activityCardInfo.showTime }}"
    class="time"
  >
    距结束 {{ countDown }}
  </view>

  <slot name="bottom"></slot>

  <!-- 底部按钮 -->
  <block wx:if="{{ activityCardInfo.btns.length }}">
    <view
      wx:for="{{ activityCardInfo.btns }}"
      wx:for-item="btn"
      wx:key="{{ btn.key }}"
    >
      <subscribe-message
        wx:if="{{ btn.type === 'FETCH_NEW' || btn.type === 'FETCH_OLD' }}"
        btn-class="btn {{ btn.btnClass }}"
        subscribe-scene="splitcoupon"
        scene="share_ticket"
        data-item="{{ btn }}"
        bind:next="handleBtnClick"
      >
        {{ btn.text }}
      </subscribe-message>
      <button
        wx:else
        class="btn-text btn {{ btn.btnClass }}"
        data-item="{{ btn }}"
        bind:tap="handleBtnClick"
        open-type="{{ btn.type === 'SHARE' ? 'share' : '' }}"
      >
        {{ btn.text }}
      </button>
    </view>
  </block>
</view>
