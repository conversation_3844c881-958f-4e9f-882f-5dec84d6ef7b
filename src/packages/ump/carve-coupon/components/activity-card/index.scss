@import '../../index.scss';

.activity-card {
  position: relative;
  margin: 50.67vw 15px 0;
  background: #fff;
  border-radius: 8px;
  padding: 24px 0;
  text-align: center;
  min-height: 150px;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(50, 50, 51, .08);

  .money-icon {
    position: absolute;
    width: 48px;
    height: 43px;
    top: -20px;
    left: -20px;
    background: url(https://b.yzcdn.cn/public_files/11580c72b762f6584e56a28da433f1b3.png) no-repeat center;
    background-size: 80% 80%;
  }
}

.time {
  margin: 12px 0 16px;
  color: #f60;
  font-size: 14px;

  .time-remain {
    display: inline-block;
    color: #f60;
  }

  &::before,
  &::after {
    margin-bottom: 5px;
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-bottom: 1px solid #dcdee0;
  }

  &::before {
    margin-right: 8px;
    border-left: 23px solid transparent;
  }

  &::after {
    margin-left: 8px;
    border-right: 23px solid transparent;
  }
}

.fail-icon {
  display: block;
  width: 70px;
  height: 64px;
  margin: 0 auto;
}

.fail-title {
  margin: 16px 0 8px;
}

.activity-desc {
  font-size: 14px;
  color: #969799;
  white-space: pre-wrap;
  line-height: 20px;
}

.activity-time {
  margin: 16px 0;
  white-space: normal;
}

.success-card {
  .activity-title {
    font-size: 14px;
    color: #ee0a24;
  }

  .activity-desc {
    margin: 0 0 16px;
  }

  .activity-money {
    margin: 12px 0;
  }
}

.fail-card {
  .activity-title {
    margin: 16px 0 8px;
  }
}

.fetch-card {
  background-image: url(https://b.yzcdn.cn/public_files/61972d8c41da23018c0a9f91aa70cba5.png);
  background-size: 110% 110%;
  background-position: center;
  background-repeat: no-repeat;
}

.btn-text {
  font-size: 16px;
}
