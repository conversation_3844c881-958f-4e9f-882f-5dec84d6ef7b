import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapGetters, mapMutations, mapActions } from '@youzan/vanx';
import { useCoupon } from '../../common/helpers';
import Countdown from '@youzan/weapp-utils/lib/countdown';

const app = getApp();
const FAIL_ICON =
  'https://img01.yzcdn.cn/public_files/2018/04/03/023c9dda27509f01cb20d3af89a41f25.png';

VanxComponent({
  options: {
    multipleSlots: true,
  },

  data: {
    failIcon: FAIL_ICON,
    countDown: '',
  },

  mapData: {
    ...mapState(['activity', 'currentUserMoney', 'queryOptions']),
    ...mapGetters(['avatarList', 'remainNum', 'activityCardInfo']),
  },

  computed: {
    ...mapState(['activity']),
    ...mapGetters(['activityCardInfo']),
  },

  watch: {
    activity(newVal) {
      const { remainTime, hordeId, isOpened } = newVal;
      if (hordeId && !isOpened && remainTime > 0) {
        this.initCountDown(remainTime);
      }
    },
  },

  methods: {
    ...mapActions(['FETCH_PACKAGE']),
    ...mapMutations(['SET_SHARE_VISIBLE', 'SET_NICK_NAME']),

    handleBtnClick(e) {
      const { type } = e.target.dataset.item;
      console.log('type', type);
      const ACTION_MAP = {
        FETCH_NEW: 'fetchNewPackage',
        FETCH_OLD: 'joinOldPackage',
        USE: 'useCoupon',
      };

      const actionName = ACTION_MAP[type];
      actionName && this[actionName]();

      this.logger(e.target.dataset.item);
    },

    logger({ type = '', text = '' }) {
      const { activityId } = this.data.activity;

      const LOGGER_MAP = {
        FETCH_NEW: {
          ei: 'click_carveup', // 事件标识
          en: '参与点击', // 事件名称
        },
        FETCH_OLD: {
          ei: 'click_help', // 事件标识
          en: '立即瓜分点击', // 事件名称
        },
        SHARE: {
          ei: 'click_invite', // 事件标识
          en: '邀请好友点击', // 事件名称
        },
      };

      app.logger &&
        app.logger.log({
          et: 'click',
          en: `点击${text}按钮`,
          params: {
            act_id: activityId,
            act_type: '1006',
          },
          ...LOGGER_MAP[type],
        });
    },

    // 更新用户信息
    updateUser({ userInfo = {} }) {
      this.SET_NICK_NAME(userInfo.nickName);
    },

    // 瓜分新包
    fetchNewPackage() {
      this.FETCH_PACKAGE(1);
    },

    // 参与旧包
    joinOldPackage() {
      this.FETCH_PACKAGE(0);
    },

    // 立即使用
    useCoupon() {
      const { activity = {}, queryOptions = {} } = this.data;
      useCoupon(activity, queryOptions);
    },

    initCountDown(remainTime) {
      new Countdown(remainTime, {
        onChange: (timeData, timeDataStr) => {
          const { day, hour, minute, second } = timeDataStr;
          const dayText = +day ? `${day}天 ` : '';
          const totalHour = +hour > 10 ? Number(hour) : '0' + Number(hour);
          this.setYZData({
            countDown: `${dayText}${totalHour}:${minute}:${second}`,
          });
        },
        onEnd: () => {
          // 如果是活动开始倒计时结束，刷新页面
          setTimeout(() => {
            wx.startPullDownRefresh();
          }, 1000);
        },
      });
    },
  },
});
