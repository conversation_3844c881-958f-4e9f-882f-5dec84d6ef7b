import WscPage from 'pages/common/wsc-page';
import args from '@youzan/weapp-utils/lib/args';

WscPage({
  data: {
    src: '',
    alias: '',
    title: '',
    imageUrl: '',
  },

  onLoad(query) {
    const { alias = '', kdtId, questionId } = query;

    this.setYZData({
      src: args.add('https://h5.youzan.com/wscump/apps/crazy/help', {
        kdtId,
        alias,
        questionId,
      }),
    });
    this.alias = alias;
    this.kdtId = kdtId;
    this.questionId = questionId;
  },

  onShareAppMessage() {
    const { alias, imageUrl, title, kdtId, questionId } = this;
    const path = `/packages/ump/apps/crazy/help/index?alias=${alias}&kdtId=${kdtId}&questionId=${questionId}`;
    return {
      path,
      title,
      imageUrl,
    };
  },

  onPostMessage(event) {
    const list = (event.detail || {}).data || [];

    list.length
      && list.forEach(({ config = {}, type }) => {
        if (type === 'CRAZY.help' || type === 'CRAZY.share') {
          const { title = '', imgUrl = '' } = config;
          this.title = title;
          this.imageUrl = imgUrl;
        }
      });
  },
});
