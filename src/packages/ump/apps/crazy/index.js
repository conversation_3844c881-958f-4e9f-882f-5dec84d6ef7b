import WscPage from 'pages/common/wsc-page';
import args from '@youzan/weapp-utils/lib/args';
import getApp from 'shared/utils/get-safe-app';
import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';

const app = getApp();
WscPage({
  data: {
    src: '',
    alias: '',
    title: '',
    imageUrl: '',
    weappUrl: '',
  },

  onLoad(query) {
    const { alias = '' } = query;
    const redirectUrl = args.add('/' + this.__route__, query);
    autoEnterShop({
      ...query,
      umpAlias: alias,
      umpType: 'crazyguess',
      redirectUrl,
    }).then(() => {
      this.setYZData({
        src: args.add('https://h5.youzan.com/wscump/apps/crazy/game', {
          kdtId: app.getKdtId(),
          alias,
        }),
      });
      this.alias = alias;
    });
  },

  onShareAppMessage() {
    const { alias, imageUrl, title, weappUrl } = this;
    const path = weappUrl || `/packages/ump/apps/crazy/index?alias=${alias}`;
    return {
      path,
      title,
      imageUrl,
    };
  },

  onPostMessage(event) {
    const list = (event.detail || {}).data || [];

    list.length
      && list.forEach(({ config = {}, type }) => {
        if (type === 'CRAZY.help' || type === 'CRAZY.share') {
          const { title = '', imgUrl = '', weappUrl } = config;
          this.title = title;
          this.imageUrl = imgUrl;
          this.weappUrl = weappUrl;
        }
      });
  },
});
