import WscPage from 'pages/common/wsc-page';
import args from '@youzan/weapp-utils/lib/args';
import getApp from 'shared/utils/get-safe-app';
import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';

const app = getApp();
WscPage({
  data: {
    src: '',
    alias: '',
    title: '',
    imageUrl: '',
  },

  onLoad(query) {
    const { alias = '' } = query;
    const redirectUrl = args.add('/' + this.__route__, query);
    autoEnterShop({
      ...query,
      umpAlias: alias,
      umpType: 'shake',
      redirectUrl,
    }).then(() => {
      this.setYZData({
        src: args.add('https://h5.youzan.com/wscump/apps/shake', {
          kdtId: app.getKdtId(),
          alias,
        }),
      });
      this.alias = alias;
    });
  },

  onShareAppMessage() {
    const { alias, imageUrl, title } = this;
    const path = `/packages/ump/apps/shake/index?alias=${alias}`;
    return {
      path,
      title,
      imageUrl,
    };
  },

  onPostMessage(event) {
    const list = (event.detail || {}).data || [];

    list.length &&
      list.forEach(({ config = {} }) => {
        const { title = '' } = config;
        this.title = title;
      });
  },
});
