import WscPage from 'pages/common/wsc-page/index';
import args from '@youzan/weapp-utils/lib/args';
import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';
import { getSlByShare } from '@/packages/ump/utils/getSlByShare';

const app = getApp();

WscPage({
  data: {
    pageUrl: '',
    sl: '',
  },

  onLoad(query) {
    const alias = query.activityAlias || query.alias;
    const { sl } = query;
    const redirectUrl = args.add('/' + this.__route__, query);
    this.path = redirectUrl;
    autoEnterShop({
      ...query,
      ump_alias: alias,
      ump_type: 'bale',
      redirectUrl,
    }).then(() => {
      this.setYZData({
        pageUrl: addSalesmanParams({
          url: args.add('/wscump/bale/recommend', {
            alias,
          }),
          sl,
        }),
      });
    });
  },

  onShow() {
    app.trigger('component:sku:cart', {
      type: 'add',
    });
  },

  onMessage(event) {
    const list = (event.detail || {}).data || [];
    this.sl = getSlByShare(list);
    list.length &&
      list.forEach(({ type, config = {} }) => {
        if (type === 'BALE.share') {
          const { imageUrl = '', title = '' } = config;
          this.shareConfig = {
            title,
            imageUrl,
          };
        }
      });
  },

  onShareAppMessage() {
    const { title, imageUrl } = this.shareConfig;
    let { path } = this;
    if (this.sl) {
      path = addSalesmanParams({
        url: path,
        sl: this.sl,
      });
    }

    return {
      title,
      imageUrl,
      path,
    };
  },
});
