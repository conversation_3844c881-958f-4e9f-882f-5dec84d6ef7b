import args from '@youzan/weapp-utils/lib/args';
import WscPage from 'pages/common/wsc-page/index';
import resolveMsg from 'pages/common/wsc-page/utils/resolve-msg';
import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';

const app = getApp();

WscPage({
  data: {
    viewSrc: '',
  },

  onLoad(options) {
    const { umpType = '', umpAlias = '', src, title } = options;
    if (title) {
      wx.setNavigationBarTitle({ title: decodeURIComponent(title) });
    }

    this.__webviewMsg = {};

    if (umpAlias && umpType) {
      const redirectUrl = args.add('/' + this.route, options);
      // 营销活动自动进店，在当前店铺不适用该活动的时候自动查到一个或者多个，多个情况会跳转到选择店铺页面
      // 一个情况，相当于自动进入该店
      autoEnterShop({ ...options, umpAlias, umpType, redirectUrl }).then(() => {
        const newSrc = args.add(decodeURIComponent(src), {
          kdt_id: app.getKdtId(),
        });
        this.setData({ viewSrc: newSrc });
      });
      return;
    }

    if (src) {
      this.setData({ viewSrc: decodeURIComponent(src) });
    }
  },

  handlePostMessage(ev) {
    resolveMsg.call(this, ev.detail.data || []);
  },

  onShareAppMessage() {
    // 有 src 就分享 webview 加 src 参数，否则分享首页
    const { src } = this.options || {};
    const defaultSharePath = src
      ? args.add(`/${this.route}`, { src })
      : '/packages/home/<USER>/index';
    return this.__webviewMsg['ZNB.share'] || { path: defaultSharePath };
  },
});
