// 默认背景色
export const DEFAULT_BG_COLOR = '#FFA15E';
// 默认主题色
export const DEFAULT_THEME_COLOR = '#F34900';
// 中奖名单显示人数
export const WINNERNUMS = 3;

// 文字滚动动画每帧间隔时间
export const SCROLLSTEP = 0.7;

// 轮盘总坑位数目
export const TOTALPOSITIONS = 9;

// 轮盘周围坑位数目
export const SURROUNDPOSITIONS = 8;

/*
 * 轮盘位置对应
 * 原位置             实际位置
 * 0  1  2           0  1  2
 * 3  4  5           7  8  3
 * 6  7  8           6  5  4
 */
export const MAPPOSITIONS = {
  0: 0,
  1: 1,
  2: 2,
  3: 7,
  4: 8,
  5: 3,
  6: 6,
  7: 5,
  8: 4,
};

// 按钮状态
export const STATUSENUM = {
  UNSTART: 1, // 未开始
  ACTIVE: 2, // 可参加
  END: 3, // 已结束
  INLOTTERY: 4, // 抽奖中
  TOMORROW: 5, // 明天再来
  JOINED: 6, // 已抽奖
  SHARE: 7, // 分享可获得抽奖机会
};

// 部分按钮状态展示消耗积分文案，其余不展示
export const SHOWPOINTLIST = {
  2: true,
  4: true,
};

// 轮盘滚动动画间隔时间
export const INTERVALTIME = {
  NORMAL: 150,
  FAST: 50,
};

// 轮盘滚动动画圈数(至少3圈， 浮动0~2)
export const CIRCLNUM = {
  RANGE: 2,
  MIN: 3,
};

// 快慢动画分割点（前8个格子慢， 中间快，最后慢）
export const SEPARATEPOSITION = 6;

// ‘谢谢参与’奖设置id为0， 方便后面匹配
export const UNWINNINGAWARD = {
  id: 0,
};

// 抽奖结果弹窗背景图
export const BACKGROUNDPNG = {
  LUCKY:
    'https://img01.yzcdn.cn/upload_files/2021/11/15/Fkl0shMv5jyjZOB-D0gEPgbKGnbE.png',
  UNLUCKY: 'https://img01.yzcdn.cn/wsc/ump/lottery/unlucky-new-bg.png',
};

// 跳转链接
export const URLMAP = {
  1: '/packages/user/coupon/list/index?type=promocard&title=我的优惠券', // 优惠券,
  2: '/packages/user/coupon/list/index?type=promocode&title=我的优惠码', // 优惠码
  3: '/packages/ump/presents/index', // 赠品
  4: '/packages/user/integral/index', // 积分
  31: '/packages/user/coupon/list/index?type=promocard&title=我的优惠券', // 优惠券礼包
};

export const RECORDURL = '/packages/ump/new-lottery/list/index';

// 默认图片
export const DEFAULT_IMAGE = {
  0: 'https://img01.yzcdn.cn/upload_files/2021/11/15/Fv4PiBcpNqAtsOm_1IyNr1Ss2PGn.png',
  1: 'https://img01.yzcdn.cn/upload_files/2021/11/15/FlksqPY_sDB50FqfTqWdLnqcJ8rb.png',
  2: 'https://img01.yzcdn.cn/upload_files/2021/11/15/FlksqPY_sDB50FqfTqWdLnqcJ8rb.png',
  3: 'https://img01.yzcdn.cn/upload_files/2021/11/15/Fp5eG1gHRsFz_953Zcvfey9VP1xm.png',
  4: 'https://img01.yzcdn.cn/upload_files/2021/11/15/FlAug0d3HXVrkSEy6LAJkI768Ht7.png',
  31: 'https://img01.yzcdn.cn/upload_files/2021/11/15/FlksqPY_sDB50FqfTqWdLnqcJ8rb.png',
};

// 默认描述
export const DEFAULT_WORDS = {
  0: '谢谢参与',
  1: '优惠券',
  2: '优惠码',
  3: '赠品',
  4: '积分',
  31: '优惠券礼包',
};

// 分享图
export const SHARE_IMAGE =
  'https://img01.yzcdn.cn/public_files/2019/11/06/4e8635e3c0ec1dedef67a6341c0d2318.png';

export const SHARE_TITLE = '幸运大抽奖';

// 背景图默认
export const DEFAULT_BG_URL =
  'https://img01.yzcdn.cn/upload_files/2021/11/10/Fq2pApTgpP4EsBJ-hZ4RvFAgXFBM.png';

export const GUIDE_TYPE = {
  FREE_LEVEL: 1,
  PAID_LEVEL: 2,
  FREE_CARD: 3,
  PAID_CARD: 4,
};

export const AWARD_TYPE = {
  // 0: '谢谢参与',
  COUPON: 1,
  CODE: 2,
  PRESENT: 3,
  POINTS: 4,
  COUPON_PACKAGE: 31,
  THIRD: 30,
};

export const RESULT_BUTTON_TEXT = {
  [AWARD_TYPE.COUPON]: '立即使用',
  [AWARD_TYPE.CODE]: '立即使用',
  [AWARD_TYPE.PRESENT]: '0元领取',
  [AWARD_TYPE.POINTS]: '立即查看',
  [AWARD_TYPE.COUPON_PACKAGE]: '立即领取',
  [AWARD_TYPE.THIRD]: '立即查看',
};

export const RETAIL_RESULT_BUTTON_TEXT = {
  [AWARD_TYPE.COUPON]: '立即查看',
  [AWARD_TYPE.CODE]: '立即查看',
  [AWARD_TYPE.PRESENT]: '0元领取',
  [AWARD_TYPE.POINTS]: '立即查看',
  [AWARD_TYPE.COUPON_PACKAGE]: '立即查看',
  [AWARD_TYPE.THIRD]: '立即查看',
};

export const TASK_TYPE = {
  ORDER: '1',
  SHARE: '2',
  POINTS: '3',
};

export const TASK_BUTTON_TEXT = {
  [TASK_TYPE.ORDER]: '去下单',
  [TASK_TYPE.SHARE]: '去分享',
  [TASK_TYPE.POINTS]: '前往',
};

export const TASK_IMG_URL = {
  [TASK_TYPE.ORDER]: `https://img01.yzcdn.cn/upload_files/2021/11/15/FuR839zglLPIt3VcBun6kH9tK8vN.png`,
  [TASK_TYPE.SHARE]: `https://img01.yzcdn.cn/upload_files/2021/11/15/FiLVPGy3tvaRmCjAd5aXgJM5iJJK.png`,
  [TASK_TYPE.POINTS]: `https://img01.yzcdn.cn/upload_files/2021/11/15/FujGiSdFA8evdUYet_GHpvC15Me-.png`,
};

export const FAIL_POPUP_TYPE = {
  OTHER_TASKS: '0',
  END: '1',
  LACK_OF_POINTS: '2',
};

export const DEFAULT_ROULETTE_COUNT_TIME = 5;

export const TASK_VIEW_LOG_MAP = {
  [TASK_TYPE.ORDER]: {
    name: '下单任务曝光',
    ei: 'view_shoptask',
  },
  [TASK_TYPE.SHARE]: {
    name: '分享任务曝光',
    ei: 'view_sharetask',
  },
  [TASK_TYPE.POINTS]: {
    name: '获取积分曝光',
    ei: 'view_pointstask',
  },
};

export const TASK_CLICK_LOG_MAP = {
  [TASK_TYPE.ORDER]: {
    name: '下单任务点击',
    ei: 'click_shoptask',
  },
  [TASK_TYPE.SHARE]: {
    name: '分享任务点击',
    ei: 'click_sharetask',
  },
  [TASK_TYPE.POINTS]: {
    name: '获取积分点击',
    ei: 'click_pointstask',
  },
};

export const LUCKY_DRAW_TYPE = {
  COMMON_DRAW: 1,
  COMMUNITY_DRAW: 2,
};
