import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import {
  MAPPOSITIONS,
  DEFAULT_WORDS,
  DEFAULT_IMAGE,
  TOTALPOSITIONS,
  UNWINNINGAWARD,
  SURROUNDPOSITIONS,
} from './constant';

// 添加默认图片, 文字之类
const filterAwardInfo = (list) =>
  list.map((item) => {
    const { awardType = 0, imageUrl = '', awardName = '' } = item;
    item.imageUrl = imageUrl || DEFAULT_IMAGE[awardType];
    item.imageUrl.replace('http:', 'https:');
    item.imageUrl = cdnImage(item.imageUrl, '!120x120.jpg');
    item.awardName = awardName || DEFAULT_WORDS[awardType];
    return item;
  });

// 设置“谢谢参与”奖
const setUnWinningAward = (
  unWinningName,
  unWinningWords,
  unWinningImageUrl,
  unWinningJumpUrl
) => ({
  awardName: unWinningName,
  awardWords: unWinningWords,
  imageUrl: unWinningImageUrl || DEFAULT_IMAGE[0],
  jumpUrl: unWinningJumpUrl || {},
  ...UNWINNINGAWARD,
});

/*
 * 设置转盘奖品数据、未中奖数据，确保奖品数量正确
 */
const setAnimationList = (
  awardList,
  unWinningName,
  unWinningWords,
  unWinningImageUrl,
  unWinningJumpUrl
) => {
  awardList.sort((a, b) => a.awardSort - b.awardSort); // 先把奖项按一等奖/二等奖/三等奖 顺序重新排列
  const unWinningAward = setUnWinningAward(
    unWinningName,
    unWinningWords,
    unWinningImageUrl,
    unWinningJumpUrl
  );

  const animationList = [];
  const { length } = awardList;
  awardList.map((award) => animationList.push({ ...award, show: true }));
  animationList.length = TOTALPOSITIONS - 1;
  if (length < SURROUNDPOSITIONS) {
    animationList.fill(
      { ...unWinningAward, show: true },
      length,
      TOTALPOSITIONS - 1
    );
  }

  animationList.push({ show: false });
  return {
    animationList,
    unWinningAward,
  };
};

/*
 * 调整礼品顺序
 */
const reorderAnimationList = (animationList) =>
  animationList.map((elem, index, arr) => {
    console.log('reorderAnimationList');
    const actualIndex = MAPPOSITIONS[index];
    return { index: actualIndex, ...arr[actualIndex] };
  });

/*
 * 产生随机数
 */
const genRandom = (range, base) => {
  return Math.floor(Math.random() * range + base);
};

// 处理开放数据
const handleCloudAwardInfo = (list) =>
  list.map((item) => {
    const {
      awardName = '',
      awardType = '',
      id,
      imageUrl,
      pointsValue,
      position,
    } = item;
    return {
      /** 奖品id */
      id,
      /** 奖品图片 */
      image: imageUrl,
      /** 奖品类型 */
      type: awardType,
      /** 奖品名称 */
      name: awardName,
      point: pointsValue,
      /** 转盘位置，从左上角开始，按顺时针方向排序 */
      position,
    };
  });

export default {
  genRandom,
  filterAwardInfo,
  setAnimationList,
  reorderAnimationList,
  handleCloudAwardInfo,
};
