const app = getApp();

const fetch = function (method, path, data) {
  return new Promise((resolve, reject) => {
    app
      .request({
        method,
        path,
        data,
        success(res) {
          resolve(res);
        },
        fail(err) {
          reject(err);
        },
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const prefix = '/wscump/lottery/';

// 获取抽奖活动中奖名单滚屏信息
export const getActivityWinners = (data) => {
  const path = prefix + 'get-activity-winners.json';
  return fetch('get', path, data);
};

// 获取抽奖活动信息
export const getActivityByAlias = (data) => {
  const path = prefix + 'get-activity-info.json';
  return fetch('get', path, data);
};

// 获取用户积分
export const getUserPoints = (data) => {
  const path = prefix + 'get-user-points.json';
  return fetch('get', path, data);
};

// 检查用户是否可抽奖
export const checkStatus = (data) => {
  const path = prefix + 'check-status.json';
  return fetch('get', path, data);
};

// 用户参与抽奖
export const joinLottery = (data) => {
  const path = prefix + 'join-lottery.json';
  return fetch('get', path, data);
};

// 用户分享活动增加抽奖次数
export const shareActivity = (data) => {
  const path = prefix + 'share-activity.json';
  return fetch('get', path, data);
};

// 查询是否开启引导（Apollo配置&店铺类型）
export const checkOpenGuide = () => {
  const path = prefix + 'check-open-guide.json';
  return fetch('get', path, {});
};

export const getRecommend = (data) => {
  const path = '/wscump/helpcut/recommend-response.json';
  return fetch('post', path, data);
};

export const listJoinLogs = (data) => {
  const path = prefix + 'list-join-logs.json';
  return fetch('get', path, data);
};

export const getShareSetting = (data) => {
  const path = prefix + 'get-share-setting.json';
  return fetch('get', path, data);
};
export const getCouponRedirectPath = (data) => {
  return app.request({
    method: 'get',
    path: '/wscump/coupon/coupon_use_redirect.json',
    data,
  });
};
