import WscPage from 'pages/common/wsc-page/index.js';
import Toast from '@vant/weapp/dist/toast/toast';
import Args from '@youzan/weapp-utils/lib/args';
import navigate from 'shared/utils/navigate';

const app = getApp();

WscPage({
  onLoad(options) {
    const { sponsorId } = options;

    if (!sponsorId || sponsorId === 'undefined') {
      Toast('错误的参数');
      app.logger.appError({
        message: `错误的参数：${sponsorId}`,
      });
      return;
    }

    // 跳转新版砍价页
    const url = Args.add('/packages/ump/helpcut/index', options);
    navigate.redirect({ url });
  },
});
