import args from '@youzan/weapp-utils/lib/args';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';
import { getGuideSecondSharerId } from 'packages/guide/utils';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';

export function resolveShareDataFunc(shareData) {
  const app = getApp();
  const loggerParams = shareData.loggerParams || {};
  app.logger.log({
    et: 'click',
    ei: 'share',
    en: '转发',
    params: {
      share_cmpt: 'native_custom',
      ...loggerParams,
    },
    si: app.getKdtId(),
  });

  // 拼装额外分享参数
  let sharePath = shareData.path || '/packages/home/<USER>/index';
  const allQueryData = args.getAll(sharePath);
  // 增加 ban_default_kdt_id 参数来禁用默认从 app.getKdtId() 中获取 kdt_id 的逻辑
  // 你可能好奇为什么不直接默认从 allQueryData 中获取 kdt_id, 据杨杰强大哥说就是为了修复一个问题才改成从 app.getKdtId()中取的
  const { ban_default_kdt_id: banDefaultKdtId } = allQueryData;
  let queryData = {
    ...allQueryData,
    ...defaultEnterShopOptions,
    is_share: 1,
  };

  // 导购员分享链接用户二次转发逻辑
  const guideSecondSharerId = getGuideSecondSharerId();
  if (guideSecondSharerId) {
    queryData.guide_second_sharer_id = guideSecondSharerId;
  }

  // 销售员统一处理分享操作
  if (shareData.salesman && shareData.salesman.share) {
    queryData = {
      ...queryData,
      ...getSalesmanParamsObject({ sl: shareData.salesman.seller }),
    };
  }

  // 增加 kdt_id
  if (banDefaultKdtId === 'true') {
    queryData.kdt_id = allQueryData.kdt_id || app.getKdtId() || '';
  } else {
    queryData.kdt_id = app.getKdtId() || '';
  }

  // 增加 dc_ps 追踪
  // 增加埋点数据
  const logGlobalInfo = app.logger.getGlobal() || {};
  const contextInfo = logGlobalInfo.context || {};
  const userInfo = logGlobalInfo.user || {};

  if (contextInfo.dc_ps) {
    queryData.dc_ps = contextInfo.dc_ps || '';
  }

  if (userInfo.uuid) {
    queryData.from_uuid = userInfo.uuid || '';
  }

  // 门店id
  if (app.getShopInfoSync().isMultiStore) {
    queryData.offlineId = app.getOfflineId();
  }

  // 拼接分享query
  sharePath = args.add(sharePath, queryData);

  // 清除网点id，走自动进店逻辑
  if (shareData.noOfflineId) {
    sharePath = args.remove(sharePath, 'offlineId');
  }

  // 分享页面都通过 blank-page 来进行中转
  const newSharePath =
    shareData && shareData.jumpBlankPage
      ? sharePath
      : `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
          sharePath
        )}`;
  delete shareData.jumpBlankPage;

  const finalShareData = {
    ...shareData,
    path: newSharePath,
  };
  return finalShareData;
}
