.level-description {
  padding: 15px 10px;
}
.level-header {
  background-color: #F7F7F7;
  height: 40px;
  font-size: 13px;
  line-height: 40px;
  border-top: #E6E6E6 1px solid;
}
.row {
  display: flex;
}

.first-col {
  width: 66px;
  flex-shrink: 0;
  border-left: #E6E6E6 1px solid;
  border-bottom: #E6E6E6 1px solid;
}
.second-col {
  width: 150px;
  flex-shrink: 0;
  border-left: #E6E6E6 1px solid;
  border-bottom: #E6E6E6 1px solid;
}
.third-col {
  flex-grow: 1;
  border-left: #E6E6E6 1px solid;
  border-bottom: #E6E6E6 1px solid;
  border-right: #E6E6E6 1px solid;
}
.col {
  text-align: center;
  vertical-align: middle;
  box-sizing: border-box;
}
.level-row {
  align-items: stretch;
  font-size: 12px;
}
.content-col {
  text-align: left;
  padding: 12px 0px 12px 2px;
  display: flex;
  align-items: center;
}
.content-col.first-col{
  padding-left: 0;
}
.first-col .text-contianer {
  width: 100%;
  text-align: center;
}
.second-content-col {
  padding-left: 10px;
  padding-right: 10px;
}
.third-content-col {
  padding-left: 10px;
  padding-right: 10px;
}
