// import WscPage from 'pages/common/wsc-page/index';
import { isValidFromParams } from 'utils/guide';
import { getSearch, delurlquery } from './utils';
import Args from '@youzan/weapp-utils/lib/args';

const app = getApp();
Page({
  onAddToFavorites() {
    return {
      query: `targetUrl=${encodeURIComponent(this.data.src)}`,
    };
  },
  data: {
    src: '',
  },
  onLoad(query) {
    let { targetUrl = '' } = query;
    const { from_params: fromParamsOfQuery } = query;
    const fromParams = isValidFromParams(fromParamsOfQuery)
      ? fromParamsOfQuery
      : '';
    this.setData({
      fromParams,
    });
    const appId = app.getAppId();
    const tokenData = app.getToken() || {};
    /** 背景：https://b.yzcdn.cn/shopping-guide/20230727-102703.jpeg */
    app?.logger?.updateSession?.({
      query,
    });

    app.getUserInfo((info) => {
      const { userId } = info?.userInfo || {};
      app?.logger?.setBizInfo({
        userId,
      });
    });

    const jump = (openId) => {
      const kdtId = app.getKdtId();
      targetUrl = decodeURIComponent(targetUrl);
      const params = Args.getAll(targetUrl);
      const { kdt_id: urlKdtId = null, kdtId: urlCamelKdtId = null } = params;
      const fromParamsStr = fromParams ? `&from_params=${fromParams}` : '';

      // eslint-disable-next-line prettier/prettier
      if (/\?/gi.test(targetUrl)) {
        targetUrl = `${targetUrl}&appId=${appId}&openId=${openId}${fromParamsStr}`;
      } else {
        targetUrl = `${targetUrl}?appId=${appId}&openId=${openId}${fromParamsStr}`;
      }
      if (+kdtId > 0 && !urlKdtId && !urlCamelKdtId) {
        targetUrl = `${targetUrl}&kdtId=${kdtId}`;
      }
      this.setData({
        src: targetUrl,
      });
    };
    if (tokenData.openId) {
      jump(tokenData.openId);
    } else {
      app.once('app:token:success', (token = {}) => {
        token.openId && jump(token.openId);
      });
    }
  },
  onMessage(event) {
    const messages = event.detail.data;
    let currentShareMessage = {};
    if (messages && messages.length > 0) {
      currentShareMessage = messages[messages.length - 1];
      if (currentShareMessage.type === 'wxsdk.notShare') {
        this.shareConfig = currentShareMessage;
      } else {
        this.shareConfig = currentShareMessage.config || {};
      }
    }
  },
  parseShareConfig() {
    const { title, imageUrl, path } = this.shareConfig;
    const targetUrl = getSearch('src', path);
    const pathUrl = this.getPath(targetUrl);

    return {
      title,
      imageUrl,
      path: pathUrl,
    };
  },
  getPath(targetUrl) {
    const { fromParams } = this.data;
    const fromParamsStr = fromParams ? `from_params=${fromParams}&` : '';
    const weappSharePath = `packages/edu/webview/index?${fromParamsStr}targetUrl=`;
    const packageUrl = `pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
      weappSharePath
    )}`;
    const parsedTargetUrl = this.parseTargetUrl(targetUrl);
    // eslint-disable-next-line prettier/prettier
    const path = `${packageUrl}${encodeURIComponent(
      encodeURIComponent(parsedTargetUrl)
    )}`;
    return path;
  },
  parseTargetUrl(targetUrl) {
    const delSidUrl = delurlquery(targetUrl, 'sid');
    const delAccessTokenUrl = delurlquery(delSidUrl, 'accessToken');
    const delAppIdUrl = delurlquery(delAccessTokenUrl, 'appId');
    const delOpenIdUrl = delurlquery(delAppIdUrl, 'openId');

    return delOpenIdUrl;
  },
  onShareAppMessage() {
    // 需要屏蔽的页面改为分享首页
    if (!this.shareConfig || this.shareConfig.type === 'wxsdk.notShare') {
      const parsedShareConfig = {
        path: Args.add('/packages/home/<USER>/index', {
          from_params: this.data.fromParams,
        }),
      };
      return parsedShareConfig;
    }
    const parsedShareConfig = this.parseShareConfig();
    return parsedShareConfig;
  },
});
