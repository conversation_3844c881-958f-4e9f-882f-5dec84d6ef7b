const getSearch = (name, url) => {
  // eslint-disable-next-line
  name = name.replace(/[[]/, '\\[').replace(/[]]/, '\\]');
  if (!url) return;
  url = '?' + url.split('#')[0].split('?')[1];
  const results = RegExp('[?&]' + name + '=([^&#]*)').exec(url);
  return results ? decodeURIComponent(results[1].replace(/\+/g, ' ')) : '';
};

const escapereg = (str) => {
  return str.replace(new RegExp('([.*+?^=!:\x24{}()|[\\]/\\\\])', 'g'), '\\\x241');
};

const delurlquery = (url, key) => {
  key = escapereg(key);
  var reg = new RegExp(`((\\?)(${key}=[^&]*&)+(?!${key}=))|(((\\?|&)${key}=[^&]*)+$)|(&${key}=[^&]*)`, 'g');
  return url.replace(reg, '\x241');
};

export {
  getSearch,
  delurlquery,
};
