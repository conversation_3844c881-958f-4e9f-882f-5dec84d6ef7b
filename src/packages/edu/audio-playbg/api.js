import { node as request } from 'shared/utils/request';

export const fetchCourseDetail = (data) => {
  return request({
    path: '/wscvis/course/detail/goods.json',
    data,
  });
};

export const getAssetState = (data) => {
  return request({
    path: '/wscvis/course/detail/getAssetState.json',
    data,
  });
};

export const getNextOwl = (alias, sortType, columnAlias) => {
  return request({
    path: '/wscvis/course/column/getNextEduProductInfo.json',
    data: {
      alias,
      sortType,
      columnAlias,
    },
  });
};

// 上报播放进度
export const reportViewProcess = (data) => {
  return request({
    path: '/wscvis/course/detail/reportViewProcess.json',
    data: { ...data },
    loading: false,
  });
};

// 获取播放进度
export const getContentLiveProgress = (data) => {
  return request({
    path: '/wscvis/knowledge/getContentLiveProgress.json',
    data: { ...data },
  });
};
