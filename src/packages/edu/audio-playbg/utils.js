const app = getApp();

export function getPageUrl() {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const { route, options } = currentPage;
  const argList = Object.keys(options).reduce((prev, key) => {
    return prev.concat(key + '=' + options[key]);
  }, []);
  if (!argList.length) return route;
  return route + '?' + argList.join('&');
}

export function getTime(seconds) {
  if (!seconds) return '0:00';
  let minute = parseInt(seconds / 60, 10) || 0;
  let second = Math.round(seconds % 60, 10) || 0;
  if (second === 60) {
    minute++;
    second = 0;
  }
  second = second < 10 ? `0${second}` : `${second}`;
  return `${minute}:${second}`;
}

export function redirectCourseDetail(alias) {
  const kdtId = app.getKdtId();
  const targetUrl = `https://h5.youzan.com/wscvis/knowledge/index?kdt_id=${kdtId}&page=contentshow&alias=${alias}`;
  wx.navigateBack({
    delta: 1,
    success() {
      wx.redirectTo({
        url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
          targetUrl
        )}`,
      });
    },
  });
}
