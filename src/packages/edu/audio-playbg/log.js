const app = getApp();

export function enterPageLog() {
  console.log('进入背景播放页面埋点 :>> ');
  app &&
    app.logger.log({
      et: 'display', // 事件类型
      ei: 'enterpage', // 事件标识
      en: '浏览页面', // 事件名称
      // pt: 'audioPlaybg', // 页面类型
    });
}

export function learnTimesLog({
  alias,
  parentAlias,
  hasPermission,
  hasParentPermission,
}) {
  console.log('------学习次数埋点+1------ :>> ');
  app &&
    app.logger.log({
      et: 'custom', // 事件类型
      ei: 'learn', // 事件标识
      en: '学习次数', // 事件名称
      pt: 'audioPlaybg', // 页面类型
      params: {
        scene: 'weapp_learn',
        media_type: 2,
        start_learn: 1,
        good_alias: alias,
        kdt_id: app.getKdtId(),
        parent_alias: parent<PERSON><PERSON><PERSON>,
        has_permission: hasPermission,
        has_parent_permission: hasParentPermission,
      }, // 事件参数
    });
}

export function learnDurationLog({
  alias,
  parentAlias,
  hasPermission,
  hasParentPermission,
}) {
  console.log('------每十秒上报一次学习时长------ :>> ');
  app &
    app.logger.log({
      et: 'custom', // 事件类型
      ei: 'learn_duration', // 事件标识
      en: '学习时长', // 事件名称
      params: {
        scene: 'weapp_learn',
        media_type: 2,
        duration: 10000,
        good_alias: alias,
        kdt_id: app.getKdtId(),
        parent_alias: parentAlias,
        has_permission: hasPermission,
        log_time: new Date().getTime(),
        has_parent_permission: hasParentPermission,
      },
    });
}

export function learnProgressLog({
  alias,
  progress,
  parentAlias,
  hasPermission,
  hasParentPermission,
}) {
  console.log('每两秒上报一次学习进度 :>> ');
  app &
    app.logger.log({
      et: 'view', // 事件类型
      ei: 'learn_progress_media', // 事件标识
      en: '学习进度', // 事件名称
      params: {
        scene: 'weapp_learn',
        progress,
        media_type: 2,
        good_alias: alias,
        parent_alias: parentAlias,
        kdt_id: app.getKdtId(),
        has_permission: hasPermission,
        log_time: new Date().getTime(),
        has_parent_permission: hasParentPermission,
      },
    });
}
