import WscPage from 'pages/common/wsc-page/index';

import {
  fetchCourseDetail,
  getAssetState,
  getNextOwl,
  reportViewProcess,
} from './api';
import {
  enterPageLog,
  learnTimesLog,
  learnDurationLog,
  learnProgressLog,
} from './log';
import { redirectCourseDetail } from './utils';

const { platform } = wx.getSystemInfoSync();
const iosCancelTip = platform === 'ios' ? '' : '，去购买并收听完整音频 > ';

const PROGRESS_INTERVAL = 2000; // 学习进度梅2s上报一次
const LEARN_TIME_INTERVAL = 10 * 1000; // 学习时长埋点每10s上报一次

// 首次进入展示提示
let stickTipTimes = 1;
// 保存计时器id
const pageTimer = {};

WscPage({
  data: {
    src: '',

    // 提示：购买or播放即将结束
    prefixTip: '',
    // 取消
    cancelTip: '',
    // 默认不展示【购买or播放即将结束】提示
    isCancelTip: true,
    // 任意【点击返回商详页】的提示消失
    showStickTip: false,

    // 控制埋点上报相关字段
    // 学习次数埋点每次播放仅上报一次
    learnLogTimes: false,

    // 音频相关数据
    cover: '',
    title: '',
    duration: 0,
    currentTime: 0,
    progress: 0,
    playing: false,
    // 专栏alias
    parentAlias: '',

    // 是否停止循环播放
    loopStop: false,
  },
  async onLoad(options) {
    // 进入页面埋点
    enterPageLog();
    // 初始提示storage数据
    this.initStickTip();
    // 获取页面跳转携带的数据
    try {
      const {
        alias,
        duration,
        currentTime,
        playing,
        isLog,
        sortType,
        currentColumnAlias = '',
        columnAliasList = '',
      } = options;

      this.setYZData({
        alias,
        duration,
        sortType,
        currentTime,
        playing,
        parentAlias: currentColumnAlias,
        isLog: JSON.parse(isLog),
        columnAliasList: decodeURIComponent(columnAliasList),
      });
      // 获取商详音频数据
      await this.getCourseDetail(alias);
    } catch (error) {
      console.log(error);
    }
  },

  initStickTip() {
    const showStickTip = wx.getStorageSync('showStickTip');
    if (showStickTip !== false) {
      wx.setStorageSync('showStickTip', true);
      this.setYZData({
        showStickTip: true,
      });
      stickTipTimes--;
    }
  },
  hideStickTip() {
    if (stickTipTimes === 0) {
      this.setYZData({
        showStickTip: false,
      });
      wx.setStorageSync('showStickTip', false);
    }
  },

  backCourseDetail() {
    redirectCourseDetail(this.data.alias);
  },
  // 取消【购买or播放即将结束】提示
  onCancelTip() {
    this.setYZData({
      prefixTip: '',
      cancelTip: '',
      isCancelTip: true,
      hasChangeOrder: false,
    });
  },

  // 封装获取商详音频接口
  getCourseDetail(alias) {
    fetchCourseDetail({ alias }).then(({ goodsData, goodsType }) => {
      const { isOwnAsset } = goodsData;
      let content;
      if (goodsType === 4) {
        content = goodsData.sellerContent || {};
      }
      if (goodsType === 3) {
        content = goodsData.content || {};
      }
      const { title, cover, audioContentDTO = {} } = content;
      // const parentAlias = get(column, 'alias', '');
      this.getNextOwlAudio({
        alias,
        parentAlias: this.data.parentAlias,
        init: true,
      });
      this.setYZData({
        title,
        cover,
        alias,
        hasPermission: isOwnAsset === true ? 1 : 0,
        // parentAlias,
        src:
          isOwnAsset === false
            ? audioContentDTO.preview || ''
            : audioContentDTO.content || '',
      });
    });
  },
  // 封装上报服务端播放进度接口
  reportPlaybgProgress({ alias, parentAlias, duration, currentTime }) {
    currentTime &&
      duration !== 0 &&
      reportViewProcess({
        bizNo: alias,
        progress: Math.round((currentTime / duration) * 100) * 100,
        type: 2,
        subType: 2,
        columnAlias: parentAlias || '',
      })
        .then(() => {
          // console.log('result :>> ', result);
        })
        .catch((err) => {
          console.log('err :>> ', err);
        });
  },
  // 封装获取专栏下一篇内容接口
  getNextOwlAudio({ alias, parentAlias, init = false }) {
    parentAlias &&
      getNextOwl(alias, this.data.sortType || 'asc', parentAlias).then(
        (res) => {
          // 初始化
          if (!this.data.nextOwlInfo || init === true) {
            this.setYZData({
              nextOwlInfo: res,
              hasChangeOrder: false,
            });
            return;
          }
          const { nextOwlInfo, currentTime } = this.data;
          if (nextOwlInfo.alias !== res?.alias) {
            this.setYZData({
              nextOwlInfo: res,
            });
            currentTime !== 0 &&
              this.setYZData({
                prefixTip: `老师调整课程顺序，即将播放：${this.data.nextOwlInfo.title}，`,
                cancelTip: '取消',
                hasChangeOrder: true,
              });
          }
        }
      );
  },

  // 播放组件点击播放触发&上报埋点相关
  onAudioPlay() {
    this.setYZData({
      playing: true,
    });
    const { alias, isLog, learnLogTimes, hasPermission, parentAlias } =
      this.data;

    if (parentAlias) {
      getAssetState({ alias: parentAlias }).then((res) => {
        const hasParentPermission = res.isOwnAsset === true ? 1 : 0;
        this.setYZData({
          hasParentPermission,
        });
      });
    }

    const logParams = {
      alias,
      parentAlias,
      hasPermission,
      hasParentPermission: this.data.hasParentPermission,
    };

    // h5端未点击播放，则需小程序点击播放时上报学习次数埋点
    if (hasPermission === 1 && learnLogTimes === false) {
      try {
        if (isLog === false) {
          setTimeout(() => {
            learnTimesLog({ ...logParams });
          }, 1000);
        }
        pageTimer.duration = setInterval(() => {
          if (this.data.playing === true) {
            learnDurationLog({ ...logParams });
          }
        }, LEARN_TIME_INTERVAL);
        pageTimer.progress = setInterval(() => {
          let progress = Math.round(
            (this.data.currentTime / this.data.duration) * 100
          );
          if (this.data.progress === 99) {
            progress = 100;
          }
          if (this.data.playing === true && progress > this.data.progress) {
            learnProgressLog({ ...logParams, progress });
            this.setYZData({
              progress,
            });
          }
        }, PROGRESS_INTERVAL);
        // 记录对应计时器创建的id便于销毁
        this.setYZData({
          learnLogTimes: true,
        });
      } catch (error) {
        console.log(error);
      }
    }
  },

  onAudioPause() {
    this.setYZData({
      playing: false,
    });
  },

  async onTimeUpdate(e) {
    const { duration, currentTime, playing } = e.detail;
    const { alias, parentAlias } = this.data;
    this.setYZData({
      currentTime,
      duration,
      playing,
    });

    // 播放离结束只剩下5秒的时候，提示即将播放下一曲
    if (currentTime < duration - 5) {
      this.setYZData({
        isCancelTip: false,
        prefixTip: '',
        cancelTip: '',
      });
    }
    const { hasPermission, nextOwlInfo } = this.data;
    if (hasPermission === 0) {
      if (duration !== currentTime) {
        this.setYZData({
          prefixTip: '正在免费试听部分内容',
          cancelTip: iosCancelTip,
        });
      }
      if (currentTime >= duration - 2 && duration !== 0) {
        this.setYZData({
          prefixTip: '试听结束',
          cancelTip: iosCancelTip,
        });
      }
    } else if (hasPermission === 1) {
      if (
        nextOwlInfo?.mediaType === 2 &&
        currentTime >= duration - 5 &&
        this.data.isCancelTip === false &&
        currentTime !== 0 &&
        duration !== 0
      ) {
        const time = parseInt(duration, 10) - parseInt(currentTime, 10);
        (time === 5 || (currentTime >= duration - 1 && time === 1)) &&
          (await this.getNextOwlAudio({ alias, parentAlias }));
        !this.data.hasChangeOrder &&
          this.setYZData({
            prefixTip: `即将播放：${this.data.nextOwlInfo.title}，`,
            cancelTip: '取消',
          });
      }
    }
  },

  async onAudioEnd() {
    const { hasPermission, nextOwlInfo, alias } = this.data;
    let nextAlias;
    if (
      nextOwlInfo?.mediaType === 2 &&
      hasPermission === 1 &&
      this.data.isCancelTip === false
    ) {
      nextAlias = nextOwlInfo.alias;
    }
    if (
      !nextOwlInfo ||
      nextOwlInfo.mediaType !== 2 ||
      this.data.isCancelTip === true
    ) {
      this.setYZData({
        src: '',
      });
      nextAlias = alias;
      this.setYZData({
        loopStop: true,
        playing: true,
      });
    }
    this.clearInterval();
    this.getCourseDetail(nextAlias);
    this.onAudioPlay();
    console.log('更新下一篇');
    // 播放下一篇更新相关数据
    setTimeout(() => {
      this.onCancelTip();
      this.setYZData({
        isLog: false, // 下一篇上报学习次数
        playing: true, // 下一篇自动播放
        alias: nextAlias || alias,
        currentTime: 0,
        learnLogTimes: false,
        progress: 0,
      });
    });
  },

  onAudioStop(e) {
    const { controlStop, playing } = e.detail;
    if (controlStop) {
      this.onCancelTip();
      this.setYZData({
        learnLogTimes: false,
      });
    }
    this.setYZData({
      playing,
    });
  },
  clearInterval() {
    const { duration, progress } = pageTimer;
    if (duration && progress) {
      clearInterval(duration);
      clearInterval(progress);
    }
  },
});
