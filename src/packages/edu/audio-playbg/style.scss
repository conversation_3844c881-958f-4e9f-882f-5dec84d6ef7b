.audio-img {
  position: fixed;
  width: 100%;
  height: 100%;
  filter: blur(44rpx);
  border-radius: 16rpx;
  -webkit-filter: blur(44rpx);
}

.audio-mask {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(#323233, 0.6);
}

.audio {
  &-content {
    text-align: center;
    position: absolute;
    top: 18%;
    margin: 0 32rpx;

    // &__cover {
    //   box-shadow: -2rpx -10rpx 14rpx rgba(#323233, 0.6);
    //   border-radius: 16rpx;
    // }

    &__title {
      color: #fff;
      font-size: 36rpx;
      text-align: left;
      margin-top: 12px;
    }

    &__player {
      position: relative;
      top: 320rpx;
    }
  }
}
