import WscComponent from 'pages/common/wsc-component/index';
import { redirectCourseDetail } from '../../utils';

const app = getApp();

WscComponent({
  properties: {
    text: {
      type: String,
      value: 'Wechat',
    },
    alias: String,
  },
  data: {
    statusBarHeight: app.globalData.statusBarHeight + 'px',
    navigationBarHeight: app.globalData.statusBarHeight + 'px',
  },

  methods: {
    back() {
      redirectCourseDetail(this.properties.alias);
    },
  },
});
