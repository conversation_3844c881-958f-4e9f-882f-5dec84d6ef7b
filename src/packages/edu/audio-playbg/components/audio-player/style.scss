.cap-audio {
  position: relative;

  &__time {
    font-size: 20rpx;
    color: #fff;
    padding: 0 5rpx;
    margin-top: -10rpx;
  }

  &__played {
    background-color: #00b389;
    height: 2px;
    border-radius: 10px;
  }

  &__slider {
    display: flex;
    flex-direction: row;
    justify-content: center;

    &-bar {
      position: relative;
      height: 2px;
      width: 100%;
      background: #cacaca;
      border-radius: 10px;
    }
  }

  &__pivot {
    position: absolute;
    top: -6px;
    z-index: 10;
    box-sizing: border-box;
    width: 15px;
    height: 15px;
    text-align: center;
    white-space: nowrap;
    border-radius: 50%;
    background-color: #00b389;
    -webkit-user-select: none;
    user-select: none;
    border: 4px solid #fff;
  }

  &__ctrl {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding-top: 24rpx;
    box-sizing: border-box;
    height: 168rpx;
    bottom: -60rpx;
  }

  &__switch {
    display: block;
    width: 80rpx;
    height: 80rpx;
    margin: 0 80rpx;

    &--pause {
      background: url('https://b.yzcdn.cn/20210104/pause.png') no-repeat;
      background-size: 100% 100%;
    }

    &--play {
      background: url('https://b.yzcdn.cn/20210104/play.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  &__forward,
  &__backward {
    position: relative;
    display: block;
    width: 56rpx;
    height: 56rpx;
    overflow: visible;
  }

  &__forward {
    margin-right: 70rpx;
  }

  &__backward {
    margin-left: 70rpx;
  }

  &__replay {
    position: absolute;
    top: 80rpx;
    left: 40rpx;
    width: 40rpx;
    height: 40rpx;
    overflow: visible;
  }

  &__forward::after,
  &__backward::after,
  &__replay::after {
    content: '';
    position: absolute;
    top: -20rpx;
    bottom: -20rpx;
    left: -20rpx;
    right: -20rpx;
    background: transparent;
  }

  &__stick-tip {
    position: absolute;
    bottom: 112rpx;
    left: 16rpx;
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    color: #fff;
    background: rgba(50, 50, 51, 0.8);
    border-radius: 30rpx;
    z-index: 999;

    &::after {
      position: absolute;
      bottom: -24rpx;
      left: 28rpx;
      border-color: transparent;
      border-top-color: rgba(50, 50, 51, 0.8);
      border-style: solid;
      border-width: 12rpx 10rpx;
      content: '';
    }
  }
}
