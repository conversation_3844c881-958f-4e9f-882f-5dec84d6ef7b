import WscComponent from 'pages/common/wsc-component/index';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import get from '@youzan/weapp-utils/lib/get';

import { getContentLiveProgress, reportViewProcess } from '../../api';
import { getPageUrl, getTime, redirectCourseDetail } from '../../utils';

const STATUS = {
  STOP: 'stop',
  PAUSE: 'pause',
  PLAY: 'play',
};

const app = getApp();

let startX;
let startValue;
let newValue;
let targetAudio = null;
let isDragging = false;

WscComponent({
  properties: {
    // 新增属性，同步h5和小程序的播放进度和状态
    currentTime: {
      type: Number,
      value: 0,
      observer(val) {
        this.setYZData({
          formatedCurrentTime: getTime(val),
        });
      },
    },
    duration: {
      type: Number,
      value: 0,
      observer(val) {
        this.setYZData({
          formatedDuration: getTime(val),
          duration: val,
        });
      },
    },
    playing: {
      type: Boolean,
      value: false,
      observer(val) {
        this.setYZData({
          autoPlay: val,
        });
      },
    },

    alias: {
      type: String,
      value: '',
      observer(val) {
        this.setYZData({
          alias: val,
        });
        this.getContentProgress(val);
      },
    },
    columnAlias: {
      type: String,
      value: '',
      observer(val) {
        this.setYZData({
          columnAlias: val,
        });
      },
    },
    columnAliasList: {
      type: String,
      value: '',
      observer(val) {
        this.setYZData({
          columnAliasList: val,
        });
      },
    },
    showStickTip: Boolean,

    src: {
      type: String,
      value: '',
      observer(val) {
        if (val) {
          this.createAudioContext(val);
        }
      },
    },
    title: {
      type: String,
      value: '',
    },
    coverUrl: {
      type: String,
      value: '',
    },
    // 是否循环播放
    loop: {
      value: false,
      type: Boolean,
    },

    needCheckNetType: {
      type: Boolean,
      observer(newV) {
        this.setYZData({
          _needCheckNetType: newV,
        });
      },
    },
    // 清除埋点计时器
    pageTimer: {
      type: Object,
      observer(newV) {
        this.setYZData({
          _pageTimer: newV,
        });
      },
    },
    autoPlay: Boolean,
    loopStop: Boolean,
  },

  data: {
    // 新增，控制播放状态下拖动进度条
    dragState: false,
    controlStop: false,
    playbgStop: false,

    status: STATUS.STOP,
    canPlay: false,
    isLoading: true,
    percent: 0,
    sliderWidth: 0,
    formatedCurrentTime: '0:00',
    formatedDuration: '0:00',
    audio: null,
    _needCheckNetType: false,
    _pageTimer: {},
  },

  methods: {
    // 封装上报服务端播放进度接口
    reportPlaybgProgress() {
      const {
        alias,
        columnAlias = '',
        percent,
        currentTime,
        duration,
        columnAliasList = '',
      } = this.data;
      const calculateProgress =
        duration && Math.round((currentTime / duration).toFixed(4) * 10000);
      reportViewProcess({
        bizNo: alias,
        progress: Math.round(percent * 100) || calculateProgress || 0,
        type: 2,
        subType: 2,
        studyRelatedAlias: columnAlias,
        columnAliasList: decodeURIComponent(columnAliasList),
      })
        .then(() => {
          // console.log('result :>> ', result);
        })
        .catch((err) => {
          console.log('err :>> ', err);
        });
    },
    getContentProgress(alias) {
      getContentLiveProgress({
        alias,
        type: 2,
      }).then((res) => {
        this.setYZData({
          percent: res?.progress / 100,
        });
      });
    },
    showTip() {
      console.log('showTip');
    },
    onTouchStart(e) {
      isDragging = true;
      const touch = e.touches[0];
      startX = touch.clientX;
      startValue = this.data.percent / 100;
    },
    onTouchMove(e) {
      const touch = e.touches[0];
      const diff = (touch.clientX - startX) / this.data.sliderWidth;
      newValue = Math.max(0, Math.min(startValue + diff, 1));
      const { duration } = this.data.audio;
      this._updateAudioTime(newValue * duration, duration);
      if (this.data.status === STATUS.PLAY) {
        this._audioPause();
        this.setYZData({
          dragState: true,
        });
      }
    },
    onTouchEnd() {
      // 处理连续触发滑动
      if (!isDragging) return;
      setTimeout(() => {
        isDragging = false;
        const { duration } = this.data.audio;
        this._updateAudioTime(newValue * duration, duration);
        this.setYZData({
          percent: newValue.toFixed(4) * 100,
        });
        targetAudio.seek(newValue * duration);
        targetAudio.startTime = newValue * duration;
        if (this.data.dragState === true) {
          this._audioStart();
          this.setYZData({
            dragState: false,
            isDragging: false,
          });
        }
      }, 0);
    },
    async createAudioContext(src) {
      await this.getContentProgress(this.data.alias);
      const { duration: _duration, percent: _percent } = this.data;
      console.log('初始化音频');
      targetAudio = wx.getBackgroundAudioManager();
      if (targetAudio) {
        targetAudio.src = src;
        targetAudio.title = this.data.title || '';
        targetAudio.coverImgUrl = this.data.coverUrl || '';
      }
      // 同步webview的播放进度
      const _currentTime = _duration * (_percent / 100) || 0;
      targetAudio.seek(_currentTime);
      targetAudio.startTime = _currentTime;
      const onPlay = () => {
        if (!targetAudio) return;
        const { currentTime, duration } = targetAudio;
        console.log('开始播放');
        if (this._checkBgAudioEqualCurrent()) {
          this.setYZData({
            status: STATUS.PLAY,
          });
          this.reportPlaybgProgress();
        }
        this.triggerEvent('audioplay', {
          currentTime,
          duration,
          playing: true,
        });
      };
      const onPause = () => {
        if (!targetAudio) return;
        console.log('暂停播放');
        if (this._checkBgAudioEqualCurrent()) {
          this.setYZData({
            status: STATUS.PAUSE,
          });
          this.reportPlaybgProgress();
          this.triggerEvent('audiopause', {
            playing: false,
          });
        }
      };
      const onStop = () => {
        if (!targetAudio) return;
        console.log('停止播放');
        if (this._checkBgAudioEqualCurrent()) {
          this.setYZData({
            status: STATUS.STOP,
          });
        }
        // 背景播放页面悬浮窗叉掉之后onStop监听到，需要再次初始化音频
        if (
          getPageUrl().startsWith('packages/edu/audio-playbg/index') &&
          this.data.controlStop !== true
        ) {
          this.createAudioContext(this.data.src);
          this.setYZData({
            playbgStop: true,
          });
        }
        this.reportPlaybgProgress();
        this.triggerEvent('audiostop', {
          controlStop: this.data.controlStop,
          playing: false,
        });
        if (this.data._pageTimer) {
          const { duration, progress } = this.data._pageTimer;
          if (duration && progress) {
            clearInterval(duration);
            clearInterval(progress);
          }
        }
        console.log('播放停止, 清空计时器');
      };
      const onEnded = () => {
        console.log('播放结束');
        // 结束时记录进度
        const { duration } = this.data;
        this.triggerEvent('timeupdate', {
          currentTime: duration,
          duration,
          playing: false,
        });
        this.triggerEvent('audioend');

        this._resetProgress();
        this.data.audio.startTime = 0;
        this.data.audio.seek(0);
        this.reportPlaybgProgress();
        if (this.data._pageTimer) {
          const { duration, progress } = this.data._pageTimer;
          if (duration && progress) {
            clearInterval(duration);
            clearInterval(progress);
          }
        }
        console.log('播放结束, 清空计时器');
      };

      const __callbacks = {};
      __callbacks.onPlay = onPlay;
      __callbacks.onPause = onPause;
      __callbacks.onStop = onStop;
      __callbacks.onEnded = onEnded;
      targetAudio.__callbacks = __callbacks;

      targetAudio.onPlay(onPlay);
      targetAudio.onPause(onPause);
      targetAudio.onStop(onStop);
      targetAudio.onEnded(onEnded);

      targetAudio.onError((err) => {
        console.error('音频播放错误：', err);
      });

      targetAudio.onTimeUpdate(() => {
        console.log('播放时间更新');
        if (getPageUrl().startsWith('packages/edu/webview/index')) {
          targetAudio.stop();
          // 背景播放音频进入商详播放停止
          this.setYZData({
            controlStop: true,
          });
        }
        // 没有下一曲或者背景播放页面叉掉悬浮窗会导致音频销毁，初始化之后默认播放，需要暂停
        if (
          (getPageUrl().startsWith('packages/edu/audio-playbg/index') &&
            this.data.playbgStop) ||
          this.data.loopStop
        ) {
          targetAudio.pause();
          this.setYZData({
            playbgStop: false,
            loopStop: false,
          });
        }
        let { currentTime } = targetAudio;
        const { duration } = targetAudio;
        currentTime = currentTime > duration ? duration : currentTime;
        this.triggerEvent('timeupdate', {
          currentTime,
          duration,
          playing: this.data.playing === true,
        });
        this.setYZData({
          percent: Math.min((currentTime / duration).toFixed(4) * 100, 100),
          formatedCurrentTime: getTime(
            currentTime > duration ? duration : currentTime
          ),
        });
      });

      targetAudio.onCanplay(() => {
        console.log('可以播放');
        if (this._checkBgAudioEqualCurrent()) {
          this.setYZData({
            canPlay: true,
            isLoading: false,
          });
          // 播放状态下做相关调整进度的操作不触发播放时间更新
          const { currentTime, duration } = targetAudio;
          this.triggerEvent('canplay', {
            currentTime,
            duration,
          });
        }
      });

      targetAudio.onWaiting(() => {
        console.log('等待');
        if (this._checkBgAudioEqualCurrent()) {
          this.setYZData({
            isLoading: true,
          });
        }
      });

      this.setData({
        audio: targetAudio,
      });

      // 自动播放
      if (this.data.autoPlay) {
        this._audioStart();
      }
    },

    _resetProgress(status) {
      this.setYZData({
        formatedCurrentTime: '0:00',
        percent: 0,
        status: status || STATUS.STOP,
      });
    },

    _updateAudioTime(currentTime, duration, isSeek, resetDragging) {
      if (
        getPageUrl().startsWith('packages/edu/audio-playbg') &&
        !this._checkBgAudioEqualCurrent()
      ) {
        return;
      }
      if (currentTime > duration) currentTime = duration;
      const percent = Math.min((currentTime / duration).toFixed(4) * 100, 100);
      this.setYZData(
        {
          formatedCurrentTime: getTime(currentTime),
          isLoading: false,
          isLoaded: true,
          percent,
        },
        () => {
          if (
            isSeek &&
            this.data.audio &&
            typeof this.data.audio.seek === 'function' &&
            this.data.audio.src === this.data.src
          ) {
            this.data.audio.seek(currentTime);
          }
          resetDragging && (isDragging = false);
        }
      );
    },
    _audioStart() {
      if (!this.data.src) {
        return this.triggerEvent('audio-empty');
      }
      if (!this.data.audio) return;

      const play = () => {
        this.data.audio.play();
        this.triggerEvent('audio-start');
      };

      if (this.data._needCheckNetType) {
        this.checkNetType(play);
      } else {
        play();
      }
    },
    _audioPause() {
      if (this.data.audio && this.data.status !== STATUS.PAUSE) {
        this.data.audio.pause();
      }
    },
    _audioFastForward() {
      if (!this.data.audio) return;
      if (!this._checkBgAudioEqualCurrent()) return;
      const { currentTime, duration } = this.data.audio;
      // 修复 ios 不能直接跳转到末尾的问题
      const targetPosition = Math.min(currentTime + 15, duration - 0.1);
      this._updateAudioTime(targetPosition, duration, true, true);
    },
    _audioRewind() {
      if (!this.data.audio) return;
      if (!this._checkBgAudioEqualCurrent()) return;
      const { currentTime, duration } = this.data.audio;
      const targetPosition = Math.max(currentTime - 15, 0);
      this._updateAudioTime(targetPosition, duration, true, true);
    },
    _audioReset() {
      if (!get(this.data, 'audio.src')) return;
      if (!this._checkBgAudioEqualCurrent()) return;
      const { duration } = this.data.audio;
      this._updateAudioTime(0, duration, true, true);
    },
    _audioBack() {
      redirectCourseDetail(this.properties.alias);
    },
    // 判断当前播放的音频跟进入的页面音频是否一致
    _checkBgAudioEqualCurrent() {
      return this.data.audio.src === this.data.src;
    },

    checkNetType(cb) {
      wx.getNetworkType({
        success: (res) => {
          const { networkType } = res;
          if (networkType !== 'wifi') {
            // 这东西强制页面必须有 dialog 节点
            Dialog.confirm({
              message: '即将消耗手机流量，是否继续播放？',
              showCancelButton: true,
              context: this,
            }).then(() => {
              this.setYZData({ _needCheckNetType: false });
              cb && cb();
            });
          } else {
            cb && cb();
          }
        },
        fail: () => {
          cb && cb();
        },
      });
    },
  },

  ready() {
    const query = this.createSelectorQuery();
    query
      .select('.cap-audio__slider-bar')
      .boundingClientRect((res) => {
        res &&
          this.setYZData({
            sliderWidth: res.width,
          });
      })
      .exec();

    // 获取平台信息
    this.setYZData({
      platform: app.getSystemInfoSync().platform,
    });
  },

  created() {
    if (this.data.src) {
      this.createAudioContext(this.data.src);
    }
  },
});
