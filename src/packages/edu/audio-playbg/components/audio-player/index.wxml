<view class="cap-audio">
  <!-- slider -->
  <view class="cap-audio__slider">
    <view class="cap-audio__time" style="margin-right: 6px">{{ formatedCurrentTime }}</view>
    <view
      class="cap-audio__slider-bar"
      bind:touchstart="onTouchStart"
      bind:touchmove="onTouchMove"
      bind:touchend="onTouchEnd"
      bind:touchcancel="onTouchEnd"
    >
      <view
        class="cap-audio__pivot"
        style="left: {{ percent > 97 ? 97 : percent }}%;"
      >
      </view>
      <view class="cap-audio__played" style="width: {{ percent * sliderWidth * 0.01 + 2 }}px;"></view>
    </view>
    <view class="cap-audio__time" style="margin-left: 6px">{{ formatedDuration }}</view>
  </view>
  <!-- controller -->
  <view class="cap-audio__ctrl">
    <!-- rewind -->
    <image bindtap="_audioRewind" class="cap-audio__backward" src="https://b.yzcdn.cn/20201222/fastforward15s.png" />
    <!-- play / pause -->
    <view wx:if="{{ status === 'play' }}" class="cap-audio__switch cap-audio__switch--pause" bindtap="_audioPause" />
    <view wx:else class="cap-audio__switch cap-audio__switch--play" bindtap="_audioStart" />
    <!-- fast forward -->
    <image bindtap="_audioFastForward" class="cap-audio__forward" src="https://b.yzcdn.cn/20201222/rewind15s.png" />
    <!-- replay -->
    <image class="cap-audio__replay" bindtap="_audioBack" src="https://b.yzcdn.cn/20201222/switch.png" />
    <view class="cap-audio__stick-tip" wx:if="{{ showStickTip }}" catchTap="showTip">
      点击可返回音频详情页，查看音频内容介绍
    </view>
  </view>
</view>
<van-dialog id="van-dialog" />