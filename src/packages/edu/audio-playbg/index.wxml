<image class="audio-img" src="{{ cover }}" />
<view class="audio-mask" bindtap="hideStickTip"></view>
<view class="audio">
  <!-- 自定义导航栏 -->
  <audio-navigation
    text="{{ title }}"
    alias="{{ alias }}"
  />
  <view class="audio-content">
    <view style="width: 686rpx; height: 400rpx; margin-top: 40rpx;">
      <image class="audio-content__cover" src="{{ cover }}" mode="aspectFit"/>
      <view class="audio-content__title">{{ title }}</view>
    </view>

    <audio-tips
      tip1="{{ prefixTip }}"
      tip2="{{ cancelTip }}"
      bind:tap="{{ hasPermission === 0 ? 'backCourseDetail' : 'onCancelTip' }}"
    />
    <audio-player
      class="audio-content__player"
      bind:timeupdate="onTimeUpdate"
      bind:audioplay="onAudioPlay"
      bind:audiopause="onAudioPause"
      bind:audiostop="onAudioStop"
      bind:audioend="onAudioEnd"
      src="{{ src }}"
      title="{{ title }}"
      alias="{{ alias }}"
      columnAlias="{{ parentAlias }}"
      columnAliasList="{{ columnAliasList }}"
      loopStop="{{ loopStop }}"
      coverUrl="{{ cover }}"
      pageTimer="{{ pageTimer }}"
      playing="{{ playing }}"
      initPlaying="{{ initPlaying }}"
      duration="{{ duration }}"
      showStickTip="{{ showStickTip }}"
      currentTime="{{ currentTime }}"
    />
  </view>
</view>