import WscComponent from 'pages/common/wsc-component';

WscComponent({
  properties: {
    appId: {
      type: String,
      value: '',
    },
    kdtId: {
      type: String,
      value: '',
    },
    objectFit: {
      type: String,
    },
    src: {
      type: String,
    },
    videoStyle: {
      type: String,
    },
  },
  data: {},
  methods: {
    handleVideoEnded(e) {
      this.triggerEvent('ended', e.detail);
    },

    handleLoadedMetaData(e) {
      this.triggerEvent('loadedmetadata', e.detail);
    },

    handlegetcontext(e) {
      this.triggerEvent('getcontext', e.detail);
    },

    handleTimeUpdate(e) {
      this.triggerEvent('timeupdate', e.detail);
    },
  },
});
