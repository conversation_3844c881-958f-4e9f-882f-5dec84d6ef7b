import WscComponent from 'pages/common/wsc-component';

WscComponent({
  properties: {
    opt: {
      type: Object,
      value: {},
    },
    coverUrl: {
      type: String,
      value: '',
    },
    appId: {
      type: String,
      value: '',
    },
    kdtId: {
      type: String,
      value: '',
    },
    rootKdtId: {
      type: String,
      value: '',
    },
    compatibleOpt: {
      type: Object,
      value: {},
    },
    updateState: {
      type: String,
      value: '',
    },
    multiSize: {
      type: String,
      value: '',
    },
  },
  data: {},
  methods: {
    handlePlay(e) {
      this.triggerEvent('play', e.detail);
    },

    handleVideoContext(e) {
      this.triggerEvent('videoContext', e.detail);
    },

    handlePause(e) {
      this.triggerEvent('pause', e.detail);
    },

    handleEnded(e) {
      this.triggerEvent('ended', e.detail);
    },

    handleTimeupdate(e) {
      this.triggerEvent('timeupdate', e.detail);
    },

    handleMuted(e) {
      this.triggerEvent('muted', e.detail);
    },
  },
});
