  <plugin-video
    v-if="opt.video"
    class="plugin-video + {{ opt.uuid }}"
    open-id="oDpvq0LNzwhMGfiNRbq-NthY5oUo"
    app-id="{{ appId }}"
    kdt-id="{{ kdtId }}"
    source-from="{{ opt.sourceFrom }}"
    video-id="{{ opt.video.video_id }}"
    video-kdt-id="{{ opt.video.video_kdt_id || rootKdtId }}"
    custom-url="{{ opt.customUrl }}"
    surface-plot="{{ opt.surfacePlot }}"
    cover-url="{{ coverUrl }}"
    progress-bar="{{ compatibleOpt.progressBar }}"
    auto-play="{{ compatibleOpt.autoPlay }}"
    video-chamfer="{{ compatibleOpt.videoChamfer }}"
    page-padding="{{ compatibleOpt.pagePadding }}"
    muted="{{ compatibleOpt.muted }}"
    update-state="{{ updateState }}"
    multi-size="{{ multiSize }}"
    uuid="{{ opt.uuid }}"
    voice-position="{{ opt.voicePosition }}"
    click-to-video-number="{{ opt.clickToVideoNumber }}"
    video-number-id="{{ opt.videoNumberId }}"
    video-number-video-id="{{ opt.videoNumberVideoId }}"
    disable-object-fit="{{ opt.disableObjectFit }}"
    bind:play="handlePlay"
    bind:pause="handlePause"
    bind:ended="handleEnded"
    bind:timeupdate="handleTimeupdate"
    bind:videoContext="handleVideoContext"
    bind:handleMuted="handleMuted"
    style="width: 100%"
  />
