import WscComponent from 'pages/common/wsc-component';

WscComponent({
  properties: {
    appId: {
      type: String,
      value: '',
    },
    kdtId: {
      type: String,
      value: '',
    },
    sourceFrom: {
      type: Number,
    },
    videoId: {
      type: Number,
    },
    videoKdtId: {
      type: Number,
    },
    customUrl: {
      type: String,
      value: '',
    },
    coverUrl: {
      type: String,
      value: '',
    },
    progressBar: {
      type: Number,
    },
    videoChamfer: {
      type: Number,
    },
    pagePadding: {
      type: Number,
    },
  },
  data: {},
  methods: {
    handlePlay(e) {
      this.triggerEvent('play', e.detail);
    },

    handlePause(e) {
      this.triggerEvent('pause', e.detail);
    },

    handleEnded(e) {
      this.triggerEvent('ended', e.detail);
    },

    handleUpdateTime(e) {
      this.triggerEvent('timeupdate', e.detail);
    },
  },
});
