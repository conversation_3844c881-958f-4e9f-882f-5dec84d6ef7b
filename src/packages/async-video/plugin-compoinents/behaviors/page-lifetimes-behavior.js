import Event from '@youzan/weapp-utils/lib/event';

const _f = () => {};

export default Behavior({
  properties: {
    pageLifetimes: {
      type: Array,
      value: []
    }
  },

  attached() {
    this.data.pageLifetimes.forEach(item => {
      Event.on(item, this[item] || _f, this);
    });
  },

  detached() {
    this.data.pageLifetimes.forEach(item => {
      Event.off(item, this[item] || _f, this);
    });
  }
});
