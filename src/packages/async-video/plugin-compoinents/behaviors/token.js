const app = getApp();

export default Behavior({
  properties: {
    appId: String,

    openId: {
      type: String,
      observer() {
        this.fetchTokenAndSetData();
      },
    },

    shopId: Number,

    kdtId: Number,

    /**
     * 商品布局方式( 0:大图 1:小图 2:一大两小 3:详情列表)
     */
    layout: {
      type: Number,
      value: 0,
    },

    /**
     * 商品展示模式(0:无边白底 1:瀑布流 2:无边透明底 3:促销 4:多图(不可用，被微小店占用) 5:描边白底 7:卡片投影
     */
    sizeType: {
      type: Number,
      value: 0,
    },

    imageFillStyle: {
      type: String,
      value: 2,
    },

    /**
     * 是否展示购买按钮
     */
    showBuyButton: {
      type: Boolean,
      value: true,
    },

    /**
     * 购买按钮的样式(0,1,2,3 四种可选)
     */
    buyButtonType: {
      type: Number,
      value: 1,
    },

    /**
     * 自定义按钮文字
     */
    buttonText: String,

    extraData: {
      type: Object,
      value: {},
    },

    /**
     * 是否展示倒计时
     */
    showCountDown: {
      type: Boolean,
      value: true,
    },

    /**
     * 是否展示库存
     */
    showStockNum: {
      type: Boolean,
      value: true,
    },

    pageMargin: {
      type: Number,
      value: 15,
    },

    goodsMargin: {
      type: Number,
      value: 10,
    },

    borderRadiusType: {
      type: Number,
      value: 1,
    },

    textAlignType: {
      type: String,
      value: 'left',
    },

    textStyleType: {
      type: Number,
      value: 2,
    },

    redirectType: {
      type: Number,
      value: 0,
    },
  },

  data: {
    loading: true,
    youzanAppId: null,
    componentData: {},
  },

  attached() {
    this.fetchTokenAndSetData();
  },

  methods: {
    fetchTokenAndSetData(callBack) {
      // 从插件移出之后，只用播放视频就可以了，这些逻辑不需要，暂时注释
      const { extraData } = this.data;
      this.setData(
        {
          kdtId: app.getKdtId(),
          loading: false,
          extraData,
          // todo 这两个参数不需要
          // youzanAppId,
          componentData: {
            ...this.computeComponentData(),
          },
        },
        () => {
          if (callBack) {
            callBack();
          }
        }
      );
      // const { extraData, shopId, openId, appId, kdtId } = this.data;

      // if (!openId || this.isFetching) return;

      // const accountInfo = wx.getAccountInfoSync && wx.getAccountInfoSync();
      // const hostAppId =
      //   accountInfo && accountInfo.miniProgram.appId
      //     ? accountInfo.miniProgram.appId
      //     : appId;

      // this.isFetching = true;
      // this.fetchToken({
      //   pluginId: PLUGIN_ID,
      //   appId: hostAppId,
      //   shopId,
      //   openId,
      //   kdtId,
      // }).then((res = {}) => {
      //   const { appId: youzanAppId, kdtId, sessionId, accessToken } = res;

      //   // 更新requesttoken和店铺信息
      //   request.updateToken({
      //     sessionId,
      //     accessToken,
      //   });
      //   request.updateShop({
      //     kdtId,
      //   });

      //   if (!kdtId || !appId) return;

      //   extraData.pluginData = {
      //     appId: hostAppId,
      //     openId,
      //   };

      //   this.isFetching = false;
      // this.setData(
      //   {
      //     kdtId,
      //     loading: false,
      //     extraData,
      //     youzanAppId,
      //     componentData: {
      //       ...this.computeComponentData(),
      //     },
      //   },
      //     () => {
      //       events.trigger('app:token:success', res);
      //       if (callBack) {
      //         callBack();
      //       }
      //     }
      //   );
      // });
    },

    setComponentDataByKey(key, data) {
      this.setData({
        [`componentData.${key}`]: data,
      });
    },

    // fetchToken(options = {}) {
    //   setRequestTokenParams(options);

    //   return fetchToken();
    // },
  },
});
