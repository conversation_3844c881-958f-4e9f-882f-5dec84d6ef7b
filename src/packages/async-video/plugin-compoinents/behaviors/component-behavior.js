import lifetimesBehavior from './page-lifetimes-behavior'

export default Behavior({
  behaviors: [lifetimesBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
    },

    kdtId: Number,

    buyerId: Number,

    appId: String,

    offlineId: Number,

    pageRandomNumber: String,

    extraData: {
      type: Object,
      value: {}
    },

    isPlugin: Boolean
  }
});
