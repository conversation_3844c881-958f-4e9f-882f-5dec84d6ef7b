
<cap-video
  wx:if="{{ inited }}"
  class="capVideo"
  src="{{ src }}"
  poster="{{ poster }}"
  hide-video="{{ hideVideo }}"
  source-from="{{ sourceFrom }}"
  video-width="{{ videoWidth }}"
  video-height="{{ videoHeight }}"
  progress-bar="{{ progressBar }}"
  auto-play="{{ autoPlay }}"
  video-chamfer="{{ videoChamfer }}"
  page-padding="{{ pagePadding }}"
  multiSize="{{ multiSize }}"
  muted="{{ muted }}"
  update-state="{{ videoUpdateState }}"
  voice-position="{{ voicePosition }}"
  click-to-video-number="{{ clickToVideoNumber }}"
  video-number-id="{{ videoNumberId }}"
  video-number-video-id="{{ videoNumberVideoId }}"
  disable-object-fit="{{ disableObjectFit }}"
  uuid="{{ uuid }}"
  bindplay="handlePlay"
  bindpause="handlePause"
  bindended="handleEnded"
  bindtimeupdate="handleUpdateTime"
  bindmuted="handleMuted"
  bindvideoContext="handleVideoContext"
/>
