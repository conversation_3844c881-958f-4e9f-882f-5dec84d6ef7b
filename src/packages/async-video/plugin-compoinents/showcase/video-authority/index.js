import Events from '@youzan/weapp-utils/lib/event';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import componentBehavior from '../../behaviors/component-behavior';
import { countPlay, getPlayInfo, getPluginAuthority } from './api';
import { VOICE_POSITION_MAP } from '../../constants/index';

Component({
  behaviors: [componentBehavior],
  properties: {
    videoKdtId: Number,
    updateState: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'observerDate',
    },
    muted: {
      type: Boolean,
      value: true,
    },
    voicePosition: {
      type: Number,
      default: VOICE_POSITION_MAP.topRight,
    },
  },
  data: {
    inited: false, // 是否初始化
    paid: false, // 播放付费标识
    src: '',
    poster: '',
    countUrl: '',
    videoHeight: 0,
    videoWidth: 0,
    progressBar: 2,
    autoPlay: 2,
    videoChamfer: 1,
    pagePadding: 0,
    sourceFrom: 1,
    multiSize: false,
    uuid: '',
    isUpdate: false,
    videoUpdateState: false,
    hasPermission: true,
    permissionImg:
      'https://b.yzcdn.cn/public_files/2019/04/03/ce1dedf3762a1f8042c51bc7d7de17c4.png',
    clickToVideoNumber: 0,
    videoNumberId: '',
    videoNumberVideoId: '',
    disableObjectFit: false,
  },

  attached() {
    this.pluginAuthority();
  },

  methods: {
    observerDate() {
      this.setData(
        {
          isUpdate: true,
        },
        () => {
          this.pluginAuthority();
        }
      );
    },

    pluginAuthority() {
      const { componentData } = this.data;
      getPluginAuthority()
        .then((result) => {
          const { isInBlackList, hasPluginAuthority } =
            mapKeysCase.toCamelCase(result);

          if (hasPluginAuthority && !isInBlackList) {
            this.initVideo(componentData);
          } else {
            this.setVideoDisabled();
          }
        })
        .catch(() => {
          this.setVideoDisabled();
        });
    },
    initVideo(itemData) {
      const {
        video = {},
        sourceFrom = 1,
        customUrl = '',
        surfacePlot = 1,
        surfacePlotImage = '',
      } = itemData;

      const {
        cover_url: coverUrl = '',
        video_id: videoId = 0,
        video_kdt_id: videoKdtId,
      } = video;

      const baseParams = Object.assign(
        {
          inited: true,
          progressBar: 2,
          autoPlay: 2,
          videoChamfer: 1,
          pagePadding: 0,
          sourceFrom,
        },
        itemData
      );

      const setVideoData = (customParams) => {
        this.handleSetData({ ...baseParams, ...customParams });
      };

      if (sourceFrom === 1) {
        getPlayInfo({
          videoId,
          kdt_id: videoKdtId || this.data.kdtId,
          v_kdt_id: this.data.videoKdtId,
        }).then((result) => {
          if (!result) return;

          const {
            video_url: src = '',
            count_played_url: countUrl = '',
            tencent_video_d_t_o: {
              cover_width: videoWidth = 0,
              cover_height: videoHeight = 0,
            } = {},
          } = result;

          setVideoData({
            src,
            countUrl,
            poster: surfacePlot === 2 ? surfacePlotImage : coverUrl,
            videoHeight,
            videoWidth,
          });
        });
      } else {
        setVideoData({
          src: customUrl,
          poster: surfacePlotImage,
          videoHeight: 0,
          videoWidth: 0,
        });
      }

      this.initEvents();
    },

    handleSetData(data = {}) {
      this.setData(data, () => {
        if (this.data.isUpdate) {
          this.setData({
            isUpdate: false,
            videoUpdateState: this.data.updateState,
          });
        }
      });
    },

    initEvents() {
      Events.on('pauseOtherVideo', (context) => {
        if (this !== context) {
          let video = this.selectComponent('.capVideo');
          video.pause();
        }
      });
    },

    handlePlay({ detail }) {
      Events.trigger('pauseOtherVideo', this);

      if (!this.data.paid && this.data.countUrl) {
        this.data.paid = true;
        countPlay(this.data.countUrl || '');
      }

      this.triggerEvent('play', detail);
    },

    handlePause({ detail }) {
      this.triggerEvent('pause', detail);
    },

    handleEnded({ detail }) {
      this.triggerEvent('ended', detail);
    },

    handleUpdateTime({ detail }) {
      this.triggerEvent('timeupdate', detail);
    },

    handleVideoContext({ detail }) {
      this.triggerEvent('videoContext', detail);
    },

    handleMuted({ detail }) {
      this.triggerEvent('muted', detail);
    },

    setVideoDisabled() {
      this.setData({
        poster: this.data.permissionImg,
        hideVideo: true,
        src: 'placeholder.mp4',
        inited: true,
      });
    },
  },
});
