import { node as request } from 'shared/utils/request';

const app = getApp();
function getPlayInfo(data) {
  return app.requestUseCdn({
    path: '/wscshop/video/playinfo.json',
    data,
  });
}

function getPluginAuthority(data) {
  return request({
    path: '/wscshop/video/pluginAuthority.json',
    data,
  });
}

function countPlay(url) {
  return request({
    path: url,
  });
}
export { getPlayInfo, countPlay, getPluginAuthority };
