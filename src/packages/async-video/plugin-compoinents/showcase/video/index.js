import getSystemInfo from 'shared/utils/browser/system-info';
import { VOICE_POSITION_MAP } from '../../constants/index';

Component({
  type: 'video',

  properties: {
    src: String,
    poster: String,
    hideVideo: Boolean,
    sourceFrom: Number,
    videoWidth: Number,
    videoHeight: Number,
    progressBar: Number,
    autoPlay: Number,
    videoChamfer: Number,
    pagePadding: Number,
    multiSize: Boolean,
    uuid: String,
    disableObjectFit: Boolean,
    objectFit: {
      type: String,
      value: 'contain',
    },
    updateState: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'observerDate',
    },
    muted: {
      type: Boolean,
      value: true,
    },
    voicePosition: {
      type: Number,
      default: VOICE_POSITION_MAP.topRight,
    },
    clickToVideoNumber: Number,
    videoNumberId: String,
    videoNumberVideoId: String,
  },

  data: {
    width: 0,
    height: 0,
    voicePositionStyle: '',
    hidePoster: false,
    showVideo: false,
    mutedStatus: true,
    playStatus: false,
    videoId: '',
    isMutedClick: true,
    isPlayClick: true,
    videoContext: '',
  },

  ready() {
    this.init();
  },

  methods: {
    observerDate() {
      const { muted } = this.properties;
      this.setData(
        {
          hidePoster: false,
          showVideo: false,
          mutedStatus: muted,
          playStatus: false,
          isMutedClick: true,
          isPlayClick: true,
        },
        () => {
          this.init();
        }
      );
    },

    init() {
      const {
        sourceFrom,
        videoHeight,
        videoWidth,
        pagePadding,
        multiSize,
        uuid,
        poster,
        muted,
      } = this.properties;
      this.setData({
        mutedStatus: muted,
      });
      const videoId = !uuid ? 'video-id' : uuid;
      const videoContext = wx.createVideoContext(videoId, this);
      this.handleVideoContext(videoContext);
      let height = 0;
      const voicePositionStyle = this.getVoicePositionStyle();
      const windowWidth = getSystemInfo().windowWidth - pagePadding * 2;
      if ((!multiSize && sourceFrom === 1) || sourceFrom === 2) {
        height = (windowWidth * 9) / 16;
        this.setData({
          width: windowWidth,
          height,
          hidePoster: !poster,
          videoContext,
          voicePositionStyle,
        });
      } else {
        const proportion = videoWidth / videoHeight;
        if (proportion >= 0.6 && proportion < 1) {
          // 3:4
          height = (windowWidth * 4) / 3;
        } else if (proportion >= 0.4 && proportion < 0.6) {
          // 9:16
          height = (windowWidth * 16) / 9;
        } else if (proportion > 1 && proportion <= 1.5) {
          // 4:3
          height = (windowWidth * 3) / 4;
        } else {
          // 兜底 16:9
          height = (windowWidth * 9) / 16;
        }
        this.setData({
          width: windowWidth,
          height,
          hidePoster: !poster,
          videoContext,
          voicePositionStyle,
          objectFit: 'cover',
        });
      }
    },
    getVoicePositionStyle() {
      const { voicePosition, pagePadding } = this.properties;
      const value = 8 + pagePadding;
      if (voicePosition === VOICE_POSITION_MAP.topLeft) {
        return `top:8px;left:${value}px;`;
      }
      if (voicePosition === VOICE_POSITION_MAP.topRight) {
        return `top:8px;right:${value}px;`;
      }
      if (voicePosition === VOICE_POSITION_MAP.bottomLeft) {
        return `bottom:8px;left:${value}px;`;
      }
      if (voicePosition === VOICE_POSITION_MAP.bottomRight) {
        return `bottom:8px;right:${value}px;`;
      }
      return `top:8px;right:${value}px;`;
    },
    handleBindFull(e) {
      // 当视频来源为素材中心的时候做处理
      if (this.properties.sourceFrom === 1) {
        if (
          e.detail &&
          e.detail.fullScreen &&
          e.detail.direction === 'horizontal'
        ) {
          this.setData({
            objectFit: 'contain',
          });
        } else {
          this.setData({
            objectFit: 'cover',
          });
        }
      }
    },

    handleNavigateClick() {
      const { videoNumberId, videoNumberVideoId } = this.data;
      wx.openChannelsActivity({
        finderUserName: videoNumberId,
        feedId: videoNumberVideoId,
      });
    },

    handlePosterClick() {
      if (!this.data.isPlayClick) return;
      this.setData(
        {
          isPlayClick: false,
        },
        () => {
          // 设置延迟 隐藏视频模块出现的一刹那的缩放动画
          setTimeout(() => {
            this.setData(
              {
                hidePoster: true,
                isPlayClick: true,
              },
              () => {
                this.play();
              }
            );
          }, 200);
        }
      );
    },

    handleMutedClick() {
      if (!this.data.isMutedClick) return;
      this.setData(
        {
          isMutedClick: false,
        },
        () => {
          const mutedStatus = !this.data.mutedStatus;
          this.setData(
            {
              mutedStatus,
              isMutedClick: true,
            },
            () => {
              this.triggerEvent('muted', { mutedStatus });
              this.isMutedClick = true;
            }
          );
        }
      );
    },

    handlePlayControl() {
      const { playStatus } = this.data;
      if (playStatus) {
        this.pause();
        return;
      }
      this.play();
    },

    handleVideoPlay(e) {
      this.changePlayStatus(true);
      const { hidePoster } = this.properties;
      if (!hidePoster) {
        this.setData({
          hidePoster: true,
        });
      }
      this.triggerEvent('play', e);
    },

    handleVideoPause(e) {
      this.changePlayStatus(false);
      this.triggerEvent('pause', e);
    },

    handleVideoEnded(e) {
      const { autoPlay } = this.properties;
      if (autoPlay === 2) {
        this.changePlayStatus(false);
      }
      this.triggerEvent('ended', e);
    },

    handleUpdateTime(e) {
      this.triggerEvent('timeupdate', e);
    },

    changePlayStatus(playStatus) {
      this.setData({
        playStatus,
      });
    },

    handleVideoContext(videoContext) {
      const { uuid } = this.properties;
      const detail = {
        uuid,
        videoContext,
      };
      this.triggerEvent('videoContext', detail);
    },

    pause() {
      this.data.videoContext &&
        this.data.videoContext.pause &&
        this.data.videoContext.pause();
    },

    play() {
      this.data.videoContext &&
        this.data.videoContext.play &&
        this.data.videoContext.play();
    },
  },
});
