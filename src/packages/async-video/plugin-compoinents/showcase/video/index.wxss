.c-video {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}
.c-video__player {
  display: block;
  width: 100%;
}

.c-video__player-hide {
  display: none;
}

.c-video__poster-wrap {
  position: absolute;
  left: 50%;
  top: 0;
  width: 100%;
  height: 100%;
  transform: translateX(-50%);
  z-index: 11;
  background-color: #000;
  overflow: hidden;
}

.c-video__poster {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.c-video__button {
  width: 52px;
  height: 68px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: url('https://img01.yzcdn.cn/upload_files/2021/08/25/FnAy-LubCjgQ6k2xLeonWbtcGooD.png')
    no-repeat;
  background-size: 52px auto;
  z-index: 2;
}

.c-video__muted_button {
  width: 22px;
  height: 22px;
  position: absolute;
  z-index: 4;
}

.open_muted {
  background: url('https://img01.yzcdn.cn/upload_files/2021/08/26/FoGgrN0bGLl_Rm9cdoj4xSKOQodg.png')
    no-repeat;
  background-size: 22px auto;
}

.close_muted {
  background: url('https://img01.yzcdn.cn/upload_files/2021/08/26/Fp5Ei1zECCSndaWHGkRWy3IvvDpd.png')
    no-repeat;
  background-size: 22px auto;
}

.c-video__mask,
.c-video__paly_mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.c-video__paly_mask {
  background-color: transparent;
}
