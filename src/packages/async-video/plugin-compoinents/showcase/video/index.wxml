<view class="c-video" wx:if="{{src}}" style="height: {{ height }}px;padding:0 {{pagePadding}}px;">
  <view wx:if="{{ clickToVideoNumber === 1 && videoNumberId && videoNumberVideoId }}" class="c-video__paly_mask" style="z-index:12;" bind:tap="handleNavigateClick"></view>
  <view class="c-video__poster-wrap" style="width:{{width}}px;border-radius:{{videoChamfer === 2 ? 8 : 0}}px;" wx:if="{{ !hidePoster }}">
    <image class="c-video__poster" src="{{ poster }}" mode="aspectFit" />
    <view wx:if="{{ playStatus || !hideVideo }}" class="c-video__button" bind:tap="handlePosterClick"></view>
    <view class="c-video__mask" wx:if="{{ !hideVideo }}"></view>
  </view>
  <view wx:if="{{ progressBar == 1 }}" style="position: absolute;top: 0;left: 0;width: 100%;height: 100%;z-index: 10;">
    <view wx:if="{{ !playStatus && hidePoster }}" class="c-video__button" bind:tap="handlePosterClick"></view>
    <view class="c-video__muted_button {{ !mutedStatus ? 'open_muted' : 'close_muted' }}" style="{{voicePositionStyle}}" bind:tap="handleMutedClick"></view>
    <view class="c-video__paly_mask" wx:if="{{ hidePoster }}" bind:tap="handlePlayControl"></view>
  </view>
  <video id="{{ !uuid ? 'video-id' : uuid }}" wx:if="{{ !hideVideo }}" class="c-video__player" show-center-play-btn="{{ false }}" objectFit="{{ disableObjectFit ? 'contain' : objectFit }}" src="{{ src }}" loop="{{ autoPlay === 1 }}" controls="{{ progressBar !== 1 }}" muted="{{ mutedStatus }}" show-mute-btn="{{ progressBar !== 1 }}" style="width:{{width}}px;height:{{ height }}px;border-radius:{{videoChamfer === 2 ? 8 : 0}}px;" bindplay="handleVideoPlay" bindpause="handleVideoPause" bindended="handleVideoEnded" bindtimeupdate="handleUpdateTime" bindfullscreenchange="handleBindFull" />
</view>
