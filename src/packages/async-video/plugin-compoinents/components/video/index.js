/**
 * 原版视频插件
 * 使用了老版的插件逻辑，支持素材中心视频id字段；
 * 也支持微页面配置能力，需要进行一层数据转化有点麻烦
 * <AUTHOR>
 */
import TokenBehavior from '../../behaviors/token';
import { VOICE_POSITION_MAP } from '../../constants/index';

Component({
  behaviors: [TokenBehavior],

  properties: {
    appId: String,

    openId: String,

    shopId: Number,

    kdtId: Number,

    // 视频所属的店铺 kdtid
    videoKdtId: Number,

    // 多尺寸 支持 9:16 16:9 3:4展示
    multiSize: Boolean,

    // 视频id
    videoId: {
      type: String,
    },

    // 封面图
    coverUrl: {
      type: String,
    },

    // 视频来源 1：视频id videoId; 2. 自定义链接地址; mp4结尾
    sourceFrom: {
      type: Number,
    },
    // 封面图 (1：原视频封面，2：自定义封面)
    surfacePlot: {
      type: Number,
    },

    // 自定义视频地址
    customUrl: {
      type: String,
    },

    // 进度条 (1: 隐藏， 2：显示)
    progressBar: {
      type: Number,
    },

    // 自动播放（1: 开启，2：关闭）
    autoPlay: {
      type: Number,
    },

    // 视频倒角 (1: 直角，2：圆角)
    videoChamfer: {
      type: Number,
    },

    // 页边距
    pagePadding: {
      type: Number,
    },

    // 唯一标识
    uuid: {
      type: String,
    },

    // 更新视频状态
    updateState: {
      type: Boolean,
      observer: 'observerDate',
    },

    // 自动播放时是否静音
    muted: {
      type: Boolean,
      value: true,
    },

    // 声音开关按钮的位置
    voicePosition: {
      type: Number,
      default: VOICE_POSITION_MAP.topRight,
    },

    clickToVideoNumber: Number,

    videoNumberId: String,

    videoNumberVideoId: String,

    disableObjectFit: Boolean,
  },

  data: {
    compUpdateState: false,
  },

  methods: {
    observerDate() {
      this.fetchTokenAndSetData(() => {
        this.setData({
          compUpdateState: this.data.updateState,
        });
      });
    },

    computeComponentData() {
      const { videoId, videoKdtId, coverUrl } = this.data;
      return Object.assign(
        {
          coverUrl: '',
          sourceFrom: 2,
          surfacePlot: 1,
          progressBar: 2,
          autoPlay: 2,
          videoChamfer: 1,
          pagePadding: 0,
        },
        this.data,
        {
          video: {
            video_id: videoId,
            video_kdt_id: videoKdtId,
            cover_url: coverUrl,
          },
          surfacePlotImage: coverUrl,
        }
      );
    },

    handlePlay({ detail }) {
      this.triggerEvent('play', detail);
    },

    handlePause({ detail }) {
      this.triggerEvent('pause', detail);
    },

    handleEnded({ detail }) {
      this.triggerEvent('ended', detail);
    },

    handleUpdateTime({ detail }) {
      this.triggerEvent('timeupdate', detail);
    },

    handleVideoContext({ detail }) {
      this.triggerEvent('videoContext', detail);
    },

    handleMuted({ detail }) {
      this.triggerEvent('muted', detail);
    },
  },
});
