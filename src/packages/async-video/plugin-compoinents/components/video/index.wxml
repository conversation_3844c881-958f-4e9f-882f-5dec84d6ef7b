<showcase-video-authority
  wx:if="{{ !loading }}"
  class="cap-showcase-video"
  component-data="{{ componentData }}"
  update-state="{{ compUpdateState }}"
  kdt-id="{{ kdtId }}"
  video-kdt-id="{{ videoKdtId }}"
  app-id="{{ youzanAppId }}"
  voice-position="{{ voicePosition }}"
  bindplay="handlePlay"
  bindpause="handlePause"
  bindended="handleEnded"
  bindtimeupdate="handleUpdateTime"
  bindmuted="handleMuted"
  bindvideoContext="handleVideoContext"
  muted="{{muted}}"
/>
