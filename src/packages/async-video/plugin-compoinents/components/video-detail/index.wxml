<view class="goods-image-panel">
  <view class="goods-image-tag">
    {{ previewIndex }}/{{ images.length }}
  </view>
    <!-- swiper 高度要低于图片高度一点，有缝隙 -->
    <!-- 小程序模拟器没有自动轮播 ?? -->
  <swiper
    circular
    indicator-dots="{{ false }}"
    autoplay="{{ images.length > 1 }}"
    style="width: {{ width }}px; height: {{ height - 2 }}px;"
    bindtap="handleSwiperImageTap"
    bindchange="handleSwiperChange"
  >
    <swiper-item wx:for="{{ images }}" wx:key="unique">
      <image
        src="{{ item.url }}"
        mode="aspectFit"
        class="goods-image"
        data-index="{{ index }}"
        style="width: {{ width }}px; height: {{ height }}px;"
      />
      <!-- 播放按钮 -->
      <view
        wx:if="{{ index === 0 && video && video.coverUrl && authorityInited }}"
        class="goods-video-play"
        catch:tap="handlePosterClicked"
      />
    </swiper-item>
  </swiper>

  <!-- 主图视频 -->
  <view wx:if="{{ video && authorityInited }}" hidden="{{ !showVideo }}" class="goods-image__video">
    <video
      id="goods-video-id"
      class="goods-video"
      objectFit="contain"
      src="{{ video.videoUrl }}"
      style="height:{{ picHeight + 'px'}}"
      bindplay="handleVideoPlay"
      bindpause="handleVideoPause"
      bindended="handleVideoEnded"
      bindfullscreenchange="handleFullscreenChange"
    >
      <cover-view
        hidden="{{ !showCloseVideoBtn }}"
        class="goods-video__close-btn {{ isVideoFullscreen ? 'goods-video__close-btn--fullscreen' : '' }}"
        bind:tap="handleVideoClose"
      >
        ×
      </cover-view>
    </video>
  </view>
</view>
