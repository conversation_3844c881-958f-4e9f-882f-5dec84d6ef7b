import getSystemInfo from 'shared/utils/browser/system-info';
import TokenBehavior from '../../behaviors/token';

Component({
  behaviors: [TokenBehavior],

  properties: {
    images: Array,
    video: Object
  },

  data: {
    authorityInited: false,
    goods: {},
    width: 0,
    height: 0,
    previewIndex: 1,
    _previewIndex: 0,
    _videoLoaded: false,
    isVideoFullscreen: false
  },

  attached() {
    this.init();
  },

  methods: {

    computeComponentData() {
      // 有权限之后进行初始化
      this.setData({
        authorityInited: true
      });

      return {};
    },

    init() {
      const { images = [] } = this.data;

      this.setData({
        ...this.getLayoutSize(images)
      });
    },

    // 提取一段老的代码
    getLayoutSize(images) {
      const winWidth = getSystemInfo().windowWidth;
      let pictureRatio = 0;

      images.forEach(item => {
        const { height = 0, width = 1 } = item;

        pictureRatio = Math.min(
          1.5,
          Math.max(
            pictureRatio,
            Math.max(0.56, +height / +width)
          )
        );
      });

      const picHeight = Math.ceil(winWidth * pictureRatio);

      return {
        width: winWidth,
        height: picHeight,
        picHeight,
      };
    },

    handleSwiperImageTap() {
      const images = this.data.images || [];
      const _previewIndex = this.data._previewIndex || 0;

      wx.previewImage({
        current: images[_previewIndex].url,
        urls: images.map(image => image.url)
      });
    },


    handleSwiperChange(e) {
      this.data._previewIndex = e.detail.current;
      this.setData({
        previewIndex: this._previewIndex + 1
      });
    },

    handlePosterClicked() {
      if (!this.goodsVideo) {
        this.goodsVideo = wx.createVideoContext('goods-video-id', this);
      }

      this.setData({
        showVideo: true
      }, () => {
        this.goodsVideo.exitFullScreen();
        this.goodsVideo.play();
      });
    },

    /*
      小程序播放器无法监听原生组件点击事件，采用妥协方案
      1. 播放时隐藏关闭按钮
      2. 暂停时显示关闭按钮
      3. 播放结束隐藏播放器
    */
    clearVideoTimer(reset) {
      clearTimeout(this.videoControllTimer);
      // 5s后隐藏关闭按钮
      if (reset) {
        this.videoControllTimer = setTimeout(() => {
          this.setData({
            'mainVideo.showCloseVideoBtn': false
          });
        }, 5000);
      }
    },

    handleVideoPlay() {
      if (!this._videoLoaded) this._videoLoaded = true;
      if (this.data.isVideoFullscreen) return;
      this.setData({
        showCloseVideoBtn: false
      });
    },

    // 不知为啥加载时会触发pause事件
    handleVideoPause() {
      if (!this._videoLoaded) return;
      this.setData({
        showCloseVideoBtn: true
      });
    },

    handleVideoEnded() {
      this._videoLoaded = false;
      this.setData({
        showVideo: false,
        showCloseVideoBtn: false
      });
    },

    // 目前无法监听到原生视频内的点击
    handleVideoTap() {
    },

    handleVideoClose() {
      const goodsVideo = this.goodsVideo;
      if (!goodsVideo) return;

      // 重置视频
      goodsVideo.seek(0);
      goodsVideo.pause();
      // 执行播放结束流程
      this.handleVideoEnded();
    },

    handleFullscreenChange(e) {
      const fullScreen = e.detail.fullScreen;

      // 全屏时一直显示关闭按钮
      this.setData({
        isVideoFullscreen: fullScreen,
        showCloseVideoBtn: fullScreen
      });
    },

  }
});
