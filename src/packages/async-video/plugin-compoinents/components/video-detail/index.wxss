.goods-image-panel {
  position: relative;
}

.goods-image-tag {
  font-family: PingFangSC-Regular;
  font-size: 10px;
  color: #FFFFFF;
  opacity: 0.3;
  background: #000000;
  border-radius: 18px;
  padding: 2px 5px;
  position: absolute;
  right: 15px;
  bottom: 10px;
  z-index: 1;
}

.goods-video-play {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 55px;
  height: 55px;
  background-image: url(https://img.yzcdn.cn/public_files/2019/03/05/e60e0173ebce201fa56135e0b4394e98.png);
  background-size: 55px 55px;
  transform: translate(-50%, -50%);
  text-indent: -999px;
}

.goods-image__video {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.goods-video {
  flex: 1;
  width: 100vw;
  display: block;
}
.goods-video__mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.goods-video__close-btn {
  position: absolute;
  top: 8px;
  left: 5px;
  width: 40px;
  height: 40px;
  font-size: 40px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  z-index: 1;
}

.goods-video__close-btn--fullscreen {
  top: 25px;
}
