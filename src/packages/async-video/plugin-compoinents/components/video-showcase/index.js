/**
 * 微页面视频插件
 * 完全替换微页面视频
 * <AUTHOR>
 */
import TokenBehavior from '../../behaviors/token';

Component({
  behaviors: [TokenBehavior],

  properties: {
    componentData: Object
  },

  methods: {
    computeComponentData() {
      return this.data.componentData;
    },

    handlePlay({ detail }) {
      this.triggerEvent('play', detail);
    },

    handlePause({ detail }) {
      this.triggerEvent('pause', detail);
    },

    handleEnded({ detail }) {
      this.triggerEvent('ended', detail);
    },

    handleUpdateTime({ detail }) {
      this.triggerEvent('timeupdate', detail);
    },
  }
});
