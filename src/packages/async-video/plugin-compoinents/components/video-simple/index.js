/**
 * 精简版视频
 * 目的是让外界原来使用原生组件的地方能直接用这个插件
 * 实现方式：在原生video组件的基础上，加一层权限校验，尽量支持所有原生属性；
 * <AUTHOR>
 */
import getSystemInfo from 'shared/utils/browser/system-info';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import TokenBehavior from '../../behaviors/token';
import { getPluginAuthority } from './api';

Component({
  behaviors: [TokenBehavior],

  properties: {
    customId: String,

    customClass: String,

    src: String,

    customStyle: String,

    objectFit: String,

    controls: Boolean,

    showCenterPlayBtn: Boolean,

    enableProgressGesture: Boolean,

    loop: Boolean,
  },

  data: {
    showVideo: false,
    showFail: false,
    permissionImg: 'https://b.yzcdn.cn/public_files/2019/04/03/ce1dedf3762a1f8042c51bc7d7de17c4.png'
  },

  attached() {
    getPluginAuthority().then(result => {
      const { isInBlackList, hasPluginAuthority } = mapKeysCase.toCamelCase(result);

      if (hasPluginAuthority && !isInBlackList) {
        this.initVideo();
      } else {
        this.setVideoDisabled();
      }
    }).catch(() => {
      this.setVideoDisabled();
    });
  },

  methods: {
    computeComponentData() {
      return {};
    },

    initVideo() {
      const id = Math.random().toString(36).substr(2, 9);
      const { customId } = this.properties;

      this.setData({
        showVideo: true,
        height: (getSystemInfo().windowWidth * 9) / 16,
        id
      });

      this.videoContext = wx.createVideoContext(customId, this);
      this.triggerEvent('getcontext', this.videoContext);
    },

    setVideoDisabled() {
      this.setData({
        showFail: true
      });
    },

    handlePlay({ detail }) {
      this.triggerEvent('play', detail);
    },

    handlePause({ detail }) {
      this.triggerEvent('pause', detail);
    },

    handleEnded({ detail }) {
      this.triggerEvent('ended', detail);
    },

    handleUpdateTime({ detail }) {
      this.triggerEvent('timeupdate', detail);
    },

    handleCatchTap({ detail }) {
      this.triggerEvent('catchtap', detail);
    },

    handleFullScreenChange({ detail }) {
      this.triggerEvent('fullscreenchange', detail);
    },

    handleLoadedMetaData({ detail }) {
      this.triggerEvent('loadedmetadata', detail);
    },

    pause() {
      this.videoContext.pause();
    },

    play() {
      this.videoContext.play();
    }
  }
});
