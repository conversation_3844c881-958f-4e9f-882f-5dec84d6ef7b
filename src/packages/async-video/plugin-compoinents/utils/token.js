// Event.on('auth:login', () => refreshToken(true));

export default function refreshToken(refresh) {
  // const tokenParams = storage.get('app:token:params');
  // if (!tokenParams) Promise.reject();
  // return fetchToken(
  //   {
  //     path: 'wscuser/wx/getPluginAuthSessionKey.json',
  //     method: 'post',
  //     data: tokenParams,
  //   },
  //   refresh
  // )
  //   .then((res) => {
  //     updateSharedToken(res);
  //     return res;
  //   })
  //   .catch((res) => {
  //     const { code } = res;
  //     if (+code === -1) {
  //       // wx.showToast({
  //       //   title: 'AppId错误'
  //       // });
  //     }
  //     return Promise.reject({
  //       ...res,
  //       msg: res.msg || '登录出错，请稍后重试',
  //     });
  //   });
}
