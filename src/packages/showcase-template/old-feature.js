import Toast from '@vant/weapp/dist/toast/toast';
import { resolveFeatureData } from './helper';

const app = getApp();

/**
 * 获取老版本微页面的方法
 * @param {number} featureId 老版本微页面的id
 * @param {object} promiseHandler 外层 promise 处理方法
 * @param {object} options 额外参数 cb 成功后的回调函数，isRefresh 是否是下拉刷新产生的微页面数据获取
 */
export default function requestOldWeappPage(
  featureId,
  { resolve = () => {}, reject = () => {} },
  { cb = () => {}, isRefresh = false }
) {
  const oldFeatureQueryOption = {
    api: 'weapp.wsc.feature/1.0.0/get',
    query: {
      id: featureId,
    },
  };
  app.logger.appError({
    name: '旧版微页面',
    message: '旧版微页面',
    alert: 'warn',
    detail: {
      data: featureId,
      global: app.globalData,
    },
  });
  app.carmen({
    ...oldFeatureQueryOption,
    success: (response) => {
      resolveFeatureData.call(this, {
        response,
        cb,
        resolve,
        isRefresh,
      });
    },
    fail: (res) => {
      wx.hideLoading();

      if (res.code === 60501) {
        Toast('小程序和有赞店铺信息不匹配');
        app.storage.remove('app:token');
      } else {
        Toast('获取信息失败');
      }
      reject();
    },
    complete: () => {
      if (isRefresh) {
        wx.stopPullDownRefresh();
      }
    },
  });
}
