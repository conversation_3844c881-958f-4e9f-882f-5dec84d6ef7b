import Toast from '@vant/weapp/dist/toast/toast';
import navigate from '@/helpers/navigate';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import omit from '@youzan/weapp-utils/lib/omit';
import get from '@youzan/weapp-utils/lib/get';
import throttle from '@youzan/weapp-utils/lib/throttle';
// import skuApi from 'common-api/sku/index';
import { skuApiPromise } from 'shared/utils/async-base-sku';

var app = getApp();
var TAGTITLE_HEIGHT = 27;
var GOODS_HEIGHT = 76;
var EMPTYGOODS_HEIGHT = 54;
var PAGE_SIZE = 50;
var SCROLL_OFFSET = 300;

const NONE_SKU_SUFFIX = 'empty';

export default {
  __takeAwayCurrentScrollOffset: 0,
  __takeAwayTagTaped: false,

  fetchTakeAwayThemeDataSuccess(components, isRefresh) {
    // 去除背景色等配置
    if (
      components[0] &&
      components[0].type === 'config' &&
      components[0].is_global_setting
    ) {
      components.shift();
    }

    // 后端来源数据处理
    var config = components[0];
    // banner数据 包括优惠逻辑计算
    var bannerData = config[0];
    // 优惠&满减信息判空
    var reduceData =
      ((bannerData.meet_reduce && bannerData.meet_reduce.reward_detail) || {})
        .content || [];
    var reduceStr = '';
    if (reduceData.length > 0) {
      reduceStr = '在线支付';
      var allReduceData = [];
      reduceData.forEach((reduceItem) => {
        reduceItem.forEach((item) => {
          allReduceData.push(item.title);
        });
      });
      reduceStr += allReduceData.join('，');
    }
    const shop = this.data.CURRENT_GLOBAL_SHOP || {};

    const {
      team_logo: teamLogo,
      team_name: teamName,
      shop_style: shopStyle,
      show_homepage_url: showHomepageUrl,
      show_record_url: showRecordUrl,
    } = bannerData;
    var banner = {
      logo: cdnImage(shop.image || teamLogo, '!730x0.jpg'),
      title: shop.name || teamName || '',
      preference: reduceStr,
      shopStyle: shopStyle || 'center',
      showHomepageUrl,
      showRecordUrl,
    };
    var noticeData = config[1] || {};
    banner.notice = noticeData.content || '';

    // 商品分组切换数据
    var tagDatas = config[2];
    var tagList = tagDatas.sub_entry || [];
    tagList = tagList.filter((item) => {
      return item.alias && item.title;
    });
    var choosedNum = [];
    tagList.forEach(() => {
      choosedNum.push(0);
    });
    var tags = {
      list: tagList,
      scroll: tagList.length > 3,
      choosedNum,
    };
    var selectedId = this.data.tags.selectedId;

    var currentTag = tags.list.find((item) => {
      return item.id == selectedId;
    });

    if (currentTag) {
      tags.selectedId = currentTag.id;
    } else if (tags.list.length) {
      tags.selectedId = tags.list[0].id;
    }

    // 获取起送价
    this.fetchTakeAwayStartFee();
    if (isRefresh) {
      this.setYZData({
        banner,
        tags,
      });
      tagList.forEach((tag, i) => this.fetchTakeAwayThemeGoods(true, i));
    } else {
      this.setYZData({
        banner,
        tags,
        goods: {},
      });
      tagList.forEach((tag, i) => this.fetchTakeAwayThemeGoods(false, i));
    }

    // 左侧默认高亮处理
    var tagId = (tagList[0] || {}).id || '';
    this.setYZData({
      'theme.takeAway.selectedTagId': tagId,
      'theme.takeAway.fetchToTargetIndex': 0,
      'theme.takeAway.goodsListIntoView': '',
      'theme.takeAway.showAnimation': false,
      // 购物车相关数据
      'theme.takeAway.MAX_VALUE': Number.MAX_VALUE,
      'theme.takeAway.takeoutCartData': [],
      'theme.takeAway.choosedGoods': {},
      'theme.takeAway.showTakeoutCart': false,
      'theme.takeAway.allChoosedPrice': 0,
      'theme.takeAway.allChoosedNum': 0,
    });
  },

  fetchTakeAwayStartFee() {
    app
      .request({
        path: 'v2/showcase/tplStartFee/startfee.json',
        query: {
          offline_id: app.getOfflineId() || 0,
        },
      })
      .then((data) => {
        this.setYZData({
          'theme.takeAway.takeAwayStartFee': data.value,
        });
      });
  },

  fetchTakeAwayThemeGoods(isRefresh, currentTagIndex = 0, options = {}) {
    var goods = this.data.goods;
    var tagData = this.data.tags.list[currentTagIndex] || {};
    var tagId = tagData.id;
    var tagAlias = tagData.alias;

    // 如果刷新，先清空数据
    if (isRefresh) {
      goods = {};
    }

    // 初始化分组数据
    if (!goods[tagId]) {
      goods[tagId] = {
        list: isRefresh ? (this.data.goods[tagId] || {}).list || [] : [],
        p: 0,
        loading: false,
        nomore: false,
        nodata: false,
      };
    }

    var tag = goods[tagId];

    if (tag.nomore || tag.nodata || tag.loading) {
      // setTimeout(() => {
      //   this.__takeAwayTagTaped = false;
      // }, 300);
      return;
    }
    tag.loading = true;
    this.setYZData({
      [`goods.${tagId}`]: tag,
    });

    // 只取一页，一页100个
    this.fetchTakeawayGoodsByTagAlias(tagAlias, 1, {
      page_size: PAGE_SIZE,
      success: (response) => {
        const items = (response.list || []).filter((item) => {
          // 虚拟，分销，酒店，会员购买商品, 预售商品自动过滤
          return (
            +item.is_virtual === 0 &&
            +item.goods_type !== 10 &&
            +item.goods_type !== 35 &&
            !item.purchase_right &&
            +item.pre_sale !== 1
          );
        });

        if (isRefresh) {
          tag.list = items;
        } else {
          tag.list = tag.list.concat(items || []);
        }

        if (items.length === 0) {
          tag.nodata = true;
        }
        // 去除分页
        // else if (items.length < PAGE_SIZE) {
        tag.nomore = true;
        // }

        tag.loading = false;

        // 初始化choosedGoods
        const choosedGoods = this.data.theme.takeAway.choosedGoods;
        const newGoods = {};
        items.forEach((goods) => {
          if (choosedGoods[goods.alias]) return;

          newGoods[`theme.takeAway.choosedGoods.${goods.alias}`] = {
            noneSku: false,
            stockNum: 0,
            num: 0,
            added: false,
          };
        });
        this.setYZData({
          ...newGoods,
          [`goods.${tagId}`]: tag,
        });

        // 如果已经被点击了相应的tag，那就每次都会定位到那个位置
        // if (this.__takeAwayTagTaped) {
        //   var selectedTagId = this.data.theme.takeAway.selectedTagId;
        //   this.setYZData({
        //     'theme.takeAway.goodsListIntoView': `tag-goods-${selectedTagId}`
        //   });
        // }

        if (options.success) {
          options.success(items, tag);
        } else {
          this.ThemeTakeAwayFetchNextPage();
        }
      },
      fail: () => {
        tag.nodata = true;
        tag.loading = false;
        this.setYZData({
          goods,
        });

        this.ThemeTakeAwayFetchNextPage();
      },
    });
  },

  fetchTakeawayGoodsByTagAlias(tagAlias, pageNo = 1, options) {
    app
      .request({
        path: '/wscshop/goods/goodsByTagAlias.json',
        data: {
          alias: tagAlias,
          page: pageNo,
          pageSize: options.page_size,
          isShowPeriod: 1,
          supportCombo: true,
          excludedComboSubType: '["optional_combo"]',
        },
      })
      .then((res) => {
        if (res && res.list && res.list.length) {
          res.list = res.list.map((item) => {
            if (!item.image_url) {
              item.image_url = '/upload_files/no_pic.png';
            }
            item.pic_url = cdnImage(item.image_url, '!300x300.jpg');

            return omit(item, [
              'image_url',
              'picture',
              'origin',
              'buy_url',
              'width',
              'height',
            ]);
          });
        }
        options.success && options.success(res);
      })
      .catch((res) => {
        options.fail && options.fail(res);
      });
  },

  handleThemeTakeAwayTagTap(e) {
    var selectedTagId = e.currentTarget.dataset.tagId;
    // var selectedTagIndex = e.currentTarget.dataset.tagIndex;
    const { selectedTagId: id } = this.data.theme.takeAway;

    if (selectedTagId === id) {
      return;
    }

    this.setYZData({
      'theme.takeAway.selectedTagId': selectedTagId,
      'theme.takeAway.goodsListIntoView': `tag-goods-${selectedTagId}`,
    });
    this.__takeAwayTagTaped = true;
  },

  handleThemeTakeAwayGoodsListScroll(e) {
    if (!this.__takeAwayScroll) {
      this.__takeAwayScroll = throttle((e) => {
        this.__takeAwayCurrentScrollOffset = e.detail.scrollTop;
        this.ThemeTakeAwayCalculateActiveTag(e.detail.scrollTop);
        this.ThemeTakeAwayFetchNextPage();
      }, 200);
    }
    this.__takeAwayScroll(e);
  },

  handleThemeTakeAwayGoodsListTouchStart() {
    if (this.__takeAwayTagTaped) {
      this.__takeAwayTagTaped = false;
    }
  },

  handleThemeTakeAwayGoodsNumChange(e) {
    const { num, key, type } = e.detail;
    const { takeoutCartData, choosedGoods } = this.data.theme.takeAway;
    const alias = key.split('-')[0];
    const choose = choosedGoods[alias];
    const goodsIndex = takeoutCartData.findIndex((goods) => goods.key === key);
    const goodsNum = type === 'minus' ? choose.num - 1 : choose.num + 1;
    this.updateTakeAwayChoosedGoods(alias, goodsNum);
    this.updateTakeAwayCartData(goodsIndex, key, num);
  },

  handleThemeTakeAwayBuyClicked(e) {
    const {
      activeSku: { none_sku: noneSku },
      activeGoods: { alias, title: goodsName },
    } = this.data.theme.takeAway;
    const {
      goodsId,
      messages: message,
      selectedSkuValues: skuName,
      selectedNum: num,
      selectedSkuComb: { id: skuId, stock_num: stockNum, price },
    } = e.detail;

    this.ThemeTakeAwayhandleSkuSelected({
      goodsId,
      goodsName,
      alias,
      num,
      noneSku,
      skuName,
      skuId,
      stockNum,
      message,
      price: price * 100,
    });

    this.setYZData({
      'theme.takeAway.showTakeawaySku': false,
    });
  },

  toggleThemeTakeAwayBannerDialog() {
    this.setYZData({
      'banner.showDialog': !this.data.banner.showDialog,
    });
  },

  toggleThemeTakeAwayCart(e) {
    let show = !this.data.theme.takeAway.showTakeoutCart;
    if (e.detail !== undefined) {
      show = e.detail;
    }
    this.setYZData({
      'theme.takeAway.showTakeoutCart': show,
    });
  },

  clearThemeTakeAwayCart() {
    const { choosedGoods } = this.data.theme.takeAway;
    const goodsNeedUpdate = {};
    /* eslint-disable */
    for (var key in choosedGoods) {
      if (Object.prototype.hasOwnProperty.call(choosedGoods, key)) {
        goodsNeedUpdate[`theme.takeAway.choosedGoods.${key}`] = {
          num: 0,
          stockNum: 0,
          noneSku: false,
          added: false,
        };
      }
    }
    this.setYZData({
      ...goodsNeedUpdate,
      'theme.takeAway.takeoutCartData': [],
      'theme.takeAway.allChoosedPrice': 0,
      'theme.takeAway.allChoosedNum': 0,
    });

    this.updateTakeAwayTagChoosed();
  },

  ThemeTakeAwayFetchNextPage() {
    var themeData = this.data.theme.takeAway;
    var currentScrollOffset = this.__takeAwayCurrentScrollOffset || 0;
    if (
      this.ThemeTakeAwayGetAllGoodsHeight() -
        currentScrollOffset -
        app.getSystemInfoSync().windowHeight >
      SCROLL_OFFSET
    ) {
      // setTimeout(() => {
      //   this.__takeAwayTagTaped = false;
      // }, 300);
      return;
    }

    var { currentTagIndex } = this.ThemeTakeAwayGetNextTag();
    if (currentTagIndex < 0) {
      return;
    }
  },

  ThemeTakeAwayGetAllGoodsHeight() {
    var tagList = this.data.tags.list;
    var goodsDatas = this.data.goods;
    var totalHeight = 0;

    for (var index = 0; index < tagList.length; index++) {
      var item = tagList[index];
      var tagId = item.id;
      var goodsData = goodsDatas[tagId] || {};

      if (goodsData.nodata) {
        totalHeight += EMPTYGOODS_HEIGHT + TAGTITLE_HEIGHT;
        break;
      }

      var goodsCount = (goodsData.list || []).length;
      totalHeight += GOODS_HEIGHT * goodsCount;

      if (goodsData.nomore) {
        // totalHeight += TAGTITLE_HEIGHT;
      } else {
        break;
      }
    }

    return totalHeight;
  },

  ThemeTakeAwayGetNextTag() {
    var tagList = this.data.tags.list;
    var goodsData = this.data.goods;
    var currentTagIndex = tagList.findIndex((tag) => {
      var tagId = tag.id;
      var currentGoodsData = goodsData[tagId];

      // 如果还是没有的商品数据的话，那就认为这个是需要加载的
      if (!currentGoodsData) {
        return true;
      }

      // 如果已经结束了或者已经确定没有数据了，就选择下一项
      if (currentGoodsData.nomore || currentGoodsData.nodata) {
        return false;
      }

      return true;
    });

    return {
      currentTagIndex,
      tag: tagList[currentTagIndex],
    };
  },

  ThemeTakeAwayCalculateActiveTag(scrollTop) {
    if (this.__takeAwayTagTaped) {
      return;
    }
    var tagList = this.data.tags.list || [];
    var goodsDatas = this.data.goods || {};
    var allGoodsHeight = 0;
    var index = 0;

    while (allGoodsHeight < scrollTop + 10 && index < tagList.length) {
      var tagData = tagList[index];
      var tagId = tagData.id;
      var goodsData = goodsDatas[tagId] || { list: [] };
      allGoodsHeight +=
        TAGTITLE_HEIGHT +
        (goodsData.nodata
          ? EMPTYGOODS_HEIGHT
          : GOODS_HEIGHT * goodsData.list.length);
      index++;
    }

    this.setYZData({
      'theme.takeAway.selectedTagId': tagList[index - 1].id,
    });
  },

  ThemeTakeAwayHandleShowGoodsDetail(e) {
    var alias = e.currentTarget.dataset.alias;
    navigate.navigate({
      url: `/pages/goods/detail/index?alias=${alias}`,
    });
  },

  ThemeTakeAwayhandleGoodsBuy(e) {
    const { value } = e.detail;
    const { alias, id, title, picture } = e.currentTarget.dataset;
    const { choosedGoods, takeoutCartData } = this.data.theme.takeAway;
    const choose = choosedGoods[alias];

    // 无sku商品支持快速增减
    if (choose && choose.added && choose.noneSku) {
      const key = `${alias}-${NONE_SKU_SUFFIX}`;
      const goodsIndex = takeoutCartData.findIndex(
        (goods) => goods.key === key
      );
      this.updateTakeAwayChoosedGoods(alias, value);
      this.updateTakeAwayCartData(goodsIndex, key, value);
      return;
    }

    wx.showToast({
      title: '加载中',
      mask: true,
      icon: 'loading',
    });

    skuApiPromise()
      .then(({ getSkuDataIron }) => {
        return getSkuDataIron(alias);
      })
      .then((sku) => {
        // 格式化价格
        sku.list.forEach((item) => {
          item.price = (item.price / 100).toFixed(2);
        });
        const activeGoods = {
          id,
          alias,
          title,
          picture: cdnImage(picture, '!300x300.jpg'),
        };

        // 无sku商品且不需要留言，直接添加到购物车
        if (sku.none_sku && (!sku.messages || sku.messages.length === 0)) {
          this.ThemeTakeAwayhandleSkuSelected({
            goodsId: id,
            goodsName: title,
            alias,
            num: 1,
            noneSku: true,
            skuName: [],
            skuId: sku.collection_id,
            stockNum: sku.stock_num,
            message: {},
            price: sku.price * 100,
          });
          return;
        }

        this.setYZData({
          'theme.takeAway.activeSku': sku,
          'theme.takeAway.activeGoods': activeGoods,
          'theme.takeAway.showTakeawaySku': true,
        });
      })
      .catch((msg) => {
        // 会员卡商品临时处理，需要后端处理接口
        if (msg === '不支持会员卡商品') {
          return Toast('会员卡商品无法加入购物车');
        }
        Toast(msg);
      })
      .then(() => {
        wx.hideToast();
      });
  },

  ThemeTakeAwayhandleGoodsOverlimit(e) {
    const { type } = e.detail;
    if (type === 'minus') {
      wx.showToast({
        title: '多规格商品请到购物车中删除',
        icon: 'none',
      });
    }
  },

  ThemeTakeAwayGetSkuKey(goods) {
    const { alias, skuId, noneSku = false } = goods;
    const suffix = noneSku ? NONE_SKU_SUFFIX : skuId;
    return `${alias}-${suffix}`;
  },

  ThemeTakeAwayhandleSkuSelected(goods) {
    const { alias, num } = goods;
    // sku维度更新商品（商品添加购物车时需要按sku维度，统计总数时需要按商品alias维度）
    const key = this.ThemeTakeAwayGetSkuKey(goods);
    const { takeoutCartData } = this.data.theme.takeAway;

    // 购物车中商品不存在时添加商品，已存在时更新商品数量
    const goodsIndex = takeoutCartData.findIndex(
      (cartGoods) => cartGoods.key === key
    );
    if (goodsIndex < 0) {
      this.addTakeAwayCartData(goods);
    } else {
      const cartGoods = takeoutCartData[goodsIndex];
      const newSkuNum = cartGoods.num + num;
      if (cartGoods.stockNum >= newSkuNum) {
        this.updateTakeAwayCartData(goodsIndex, key, newSkuNum);
      } else {
        // 库存不足
        wx.showToast({
          title: '库存不足',
          icon: 'none',
        });
        return;
      }
    }

    // 商品alias维度更新商品
    const choosedGoods = this.data.theme.takeAway.choosedGoods;

    let choose = choosedGoods[alias];
    if (choose && choose.added) {
      const newGoodsNum = choose.num + num;
      this.updateTakeAwayChoosedGoods(alias, newGoodsNum);
    } else {
      this.addTakeAwayChoosedGoods(goods);
    }
  },

  updateTakeAwayChoosedGoods(alias, num) {
    const { choosedGoods } = this.data.theme.takeAway;

    if (num === 0) {
      const choose = {
        num: 0,
        stockNum: 0,
        noneSku: false,
        added: false,
      };
      this.setYZData({
        [`theme.takeAway.choosedGoods.${alias}`]: choose,
      });
    } else {
      this.setYZData({
        [`theme.takeAway.choosedGoods.${alias}`]: {
          ...choosedGoods[alias],
          num,
        },
      });
    }
  },

  addTakeAwayChoosedGoods(goods) {
    const { num, noneSku = false, alias, stockNum } = goods;
    const choose = {
      num,
      noneSku,
      stockNum,
      added: true,
    };
    this.setYZData({
      [`theme.takeAway.choosedGoods.${alias}`]: choose,
    });
  },

  updateTakeAwayCartData(goodsIndex, key, num) {
    const { takeoutCartData } = this.data.theme.takeAway;

    if (num === 0) {
      takeoutCartData.splice(goodsIndex, 1);
      this.setYZData(
        {
          'theme.takeAway.takeoutCartData': takeoutCartData,
        },
        this.updateTakeAwayAllChoosed
      );
    } else {
      this.setYZData(
        {
          [`theme.takeAway.takeoutCartData[${goodsIndex}]`]: {
            ...takeoutCartData[goodsIndex],
            num,
          },
        },
        this.updateTakeAwayAllChoosed
      );
    }
  },

  addTakeAwayCartData(goods) {
    const { takeoutCartData } = this.data.theme.takeAway;

    goods.key = this.ThemeTakeAwayGetSkuKey(goods);
    goods.skuName = goods.skuName.length ? goods.skuName.join(' ') : '';
    takeoutCartData.push(goods);
    this.setYZData(
      {
        'theme.takeAway.takeoutCartData': takeoutCartData,
      },
      this.updateTakeAwayAllChoosed
    );
  },

  updateTakeAwayAllChoosed() {
    const { takeoutCartData } = this.data.theme.takeAway;
    let allChoosedNum = 0;
    let allChoosedPrice = 0;

    takeoutCartData.forEach((goods) => {
      allChoosedNum += goods.num;
      allChoosedPrice += goods.price * goods.num;
    });

    this.setYZData({
      'theme.takeAway.allChoosedNum': allChoosedNum,
      'theme.takeAway.allChoosedPrice': allChoosedPrice,
    });

    this.updateTakeAwayTagChoosed();
  },

  // 更新左侧分组的角标
  updateTakeAwayTagChoosed() {
    const {
      tags,
      goods,
      theme: {
        takeAway: { choosedGoods },
      },
    } = this.data;
    let choosedNum = [];

    tags.list.forEach(({ id }) => {
      if (!goods[id]) {
        choosedNum.push(0);
        return;
      }

      let count = 0;

      goods[id].list.forEach(({ alias }) => {
        count += get(choosedGoods, `${alias}.num`) || 0;
      });
      choosedNum.push(count);
    });

    this.setYZData({
      tags: {
        ...tags,
        choosedNum,
      },
    });
  },
};
