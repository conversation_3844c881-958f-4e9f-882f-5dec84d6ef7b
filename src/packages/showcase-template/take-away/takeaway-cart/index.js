import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    showTakeoutCart: {
      type: Boolean
    },
    takeoutCartData: {
      type: <PERSON><PERSON><PERSON>,
      observer(newVal) {
        // ?? ob obj
        if (newVal && newVal.length === 0) {
          this.toggleTakeoutCart(false);
        }
      }
    }
  },

  externalClasses: ['iphonex-class'],

  methods: {
    toggleTakeoutCart(show) {
      this.triggerEvent('toggleCart', show);
    },
    onTakeoutCartMaskClick() {
      this.toggleTakeoutCart();
    },
    onClearCart() {
      this.triggerEvent('clearCart');
    },
    handleGoodsNumChange(e) {
      const { value: num, type } = e.detail;
      const { key } = e.currentTarget.dataset;
      this.triggerEvent('goodsNumChange', {
        num,
        type,
        key
      });
    }
  }
});
