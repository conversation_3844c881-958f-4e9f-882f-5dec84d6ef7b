@import "shared/common/css/helper/index.wxss";

.zan-popup__mask {
  bottom: 44px;
}

.takeoutcart {
  margin-bottom: 44px;
  width: 100%;
  z-index: 10;
}
.takeoutcart .takeoutcart__header {
  height: 36px;
  position: relative;
}
.takeoutcart .takeoutcart__header .takeoutcart__title {
  line-height: 36px;
  font-size: 14px;
  margin-left: 15px;
  color: #333;
}
.takeoutcart .takeoutcart__header .takeoutcart__clear-icon {
  width: 14px;
  height: 14px;
  display: inline-block;
  background-size: 14px 14px;
  position: absolute;
  top: 11px;
  right: 48px;
  background-image: url('https://b.yzcdn.cn/fix-base64/be254bdf4d7129e792c7b6d08615fd955e16ef0bc5553460d62b77e56bd43876.png');
}
.takeoutcart .takeoutcart__header .takeoutcart__clear-text {
  font-size: 14px;
  line-height: 36px;
  position: absolute;
  right: 15px;
  color: #999;
}
.takeoutcart .takeoutcart__header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border-bottom: 1px solid #e5e5e5;
}
.takeoutcart .takeoutcart__header::before {
  content: '';
  position: absolute;
  height: 36px;
  width: 4px;
  background-color: #f44;
}
.takeoutcart__clear-wrapper {
  position: absolute;
  height: 36px;
  width: 70px;
  right: 0;
  top: 0;
}
.takoutcart__body {
  max-height: 265px;
  overflow: scroll;
  padding-bottom: 20px;
}
.takoutcart__body::-webkit-scrollbar {
  display: none;
}
/* color variables */
/* default colors */
/* button */
.cartrow {
  position: relative;
  margin-left: 15px;
  color: #333;
}
.cartrow .cartrow__goods-title {
  font-size: 14px;
  line-height: 50px;
  height: 50px;
  word-break: keep-all;
  display: inline-block;
  width: 170px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.cartrow .cartrow__sku-title {
  position: absolute;
  height: 14px;
  line-height: 14px;
  font-size: 12px;
  color: #999;
  top: 39px;
  left: 0;
  max-width: 170px;
  overflow: hidden;
  word-break: keep-all;
  text-overflow: ellipsis;
}
.cartrow .cartrow__goods-price {
  position: absolute;
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 5px;
}
.cartrow .cart-input {
  height: 22px;
  display: inline-block;
  position: absolute;
  right: 15px;
  top: 10px;
}
.cartrow::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border-bottom: 1px solid #e5e5e5;
}
.cartrow:last-child::after {
  display: none;
}
.cartrow_has-sku {
  height: 60px;
}
.cartrow_has-sku .cartrow__goods-title {
  font-size: 14px;
  line-height: 44px;
}
.cartrow_has-sku .cartrow__goods-price {
  height: 60px;
  line-height: 60px;
  font-size: 14px;
}
.cartrow_has-sku .cart-input {
  top: 16px;
}
@media screen and (max-width: 320px) {
  .cartrow .cartrow__goods-title {
    width: 130px;
  }
  .cartrow .cart-input {
    right: 10px;
  }
  .cartrow .cartrow__sku-title {
    width: 130px;
  }
}
