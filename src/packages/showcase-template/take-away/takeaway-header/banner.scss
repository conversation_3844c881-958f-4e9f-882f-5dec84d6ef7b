.cap-shop-banner-takeout {
  padding-top: 86px;
  position: relative;
  overflow: hidden;
}

.cap-shop-banner-takeout__logo {
  position: absolute;
  top: 20px;
  z-index: 2;
  box-shadow: 0 2px 6px rgba(0, 0, 0, .2);

  > image {
    display: block;
    width: 80px;
    height: 80px;
    border-radius: 5px;
  }
}

.cap-shop-banner-takeout--left {
  .cap-shop-banner-takeout__logo {
    left: 20px;
  }

  .cap-shop-banner-takeout__title-wrapper,
  .cap-shop-banner-takeout__notice {
    position: absolute;
    left: 115px;
    color: #fff;
  }

  .cap-shop-banner-takeout__title-wrapper {
    top: -60px;
  }

  .cap-shop-banner-takeout__title,
  .cap-shop-banner-takeout__switch-shop {
    color: #fff;
  }

  .cap-shop-banner-takeout__notice  {
    top: -30px;
    width: 100%;
    box-sizing: border-box;
    padding-right: 125px;
  }

  .cap-shop-banner-takeout__promotion {
    padding: 0 20px;
  }
}

.cap-shop-banner-takeout--center {
  text-align: center;

  .cap-shop-banner-takeout__logo {
    left: 50%;
    transform: translate(-50%, 0);
  }

  .cap-shop-banner-takeout__title {
    color: #000;
  }

  .cap-shop-banner-takeout__notice {
    color: #999;
    margin-top: 5px;
    padding: 0 30px;
  }

  .cap-shop-banner-takeout__promotion {
    padding: 0 30px;
    margin-top: 5px;
  }

  .cap-shop-banner-takeout__links {
    margin-top: 5px;
  }
}

.cap-shop-banner-takeout__content {
  background: #fff;
  border-radius: 12px 12px 0 0;
  padding: 20px 0 0;
  position: relative;
  z-index: 1;

  &.cap-shop-banner-takeout__content--nolinks {
    padding-bottom: 15px;
  }
}

.cap-shop-banner-takeout__title-wrapper {
  font-size: 16px;
  font-weight: bold;

  > navigator,
  .cap-shop-banner-takeout__title {
    display: inline;
  }
}

.cap-shop-banner-takeout__notice {
  font-size: 12px;
  height: 16px;
  line-height: 16px;
}

.cap-shop-banner-takeout__promotion-desc {
  color: #333;
  font-size: 12px;
  margin-left: 5px;
}

.cap-shop-banner-takeout__links {
  overflow: hidden;

  .zan-cell__bd {
    text-align: left;
  }
}

.cap-shop-banner-takeout__link-item {
  float: left;
  width: 50%;
  text-align: center;
  color: #333;
  font-size: 14px;
  line-height: 44px;
}

.cap-shop-banner-takeout__switch-shop {
  font-size: 12px;
  color: #999;
}

.cap-shop-banner-takeout__background {
  position: absolute;
  top: 0;
  width: 100%;
  height: 160px;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  filter: blur(15px);
  background-color: #ccc;
}

.cap-shop-banner-takeout__mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 160px;
  background:rgba(0, 0, 0, 0.1);
}

.banner-popup__container {
  width: 100%;
  text-align: left;
}

.banner-popup__item {
  margin: 20px 15px;
}

.banner-popup__item-hd {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}

.banner-popup__item-bd {
  font-size: 12px;
  color: #999;
  line-height: 18px;
}
