<view class="cap-shop-banner-takeout cap-shop-banner-takeout--{{ shopStyle }} van-hairline--bottom">
    <view class="cap-shop-banner-takeout__logo">
      <image class="banner__logo" src="{{ logo }}" />
    </view>
    <view class="cap-shop-banner-takeout__background" style="background-image: url({{ backgroundImage || logo }})" />
    <view class="cap-shop-banner-takeout__mask" />
    <view class="cap-shop-banner-takeout__content {{ !showHomepageUrl && !showRecordUrl ? 'cap-shop-banner-takeout__content--nolinks' : '' }}">
      <view class="cap-shop-banner-takeout__title-wrapper">
        <view class="cap-shop-banner-takeout__title" bindtap="handleTitleClick">
          {{ title }}
        </view>
        <navigator
          wx:if="{{ showSwitchOfflineShop }}"
          class="cap-shop-banner-takeout__switch-shop"
          url="/packages/shop/multi-store/index/index"
        >
          [切换]
        </navigator>
      </view>
      <view
        class="cap-shop-banner-takeout__notice van-ellipsis"
        wx:if="{{ noticeContent }}"
        bindtap="toggleBannerDialog"
      >
        公告：{{ noticeContent }}
      </view>
      <view
        wx:if="{{ promotionStr }}"
        class="cap-shop-banner-takeout__promotion van-ellipsis"
        bindtap="toggleBannerDialog"
      >
        <theme-view
          border="main"
          bg="main"
          custom-class="meet-reduce-tag zan-tag zan-tag--danger"
        >
          满减
        </theme-view>
        <text class="cap-shop-banner-takeout__promotion-desc">{{ promotionStr }}</text>
      </view>

      <view class="cap-shop-banner-takeout__links" wx:if="{{ showHomepageUrl || showRecordUrl }}">
        <view wx:if="{{ showHomepageUrl && showRecordUrl }}">
          <view class="cap-shop-banner-takeout__link-item" bindtap="handleRecordClick">我的记录</view>
          <view class="cap-shop-banner-takeout__link-item" bindtap="handleTitleClick">店铺首页</view>
        </view>

        <view wx:else>
          <view class="zan-cell zan-cell--access">
            <view class="zan-cell__bd" bindtap="handleSingleLinkClick" data-type="{{ showHomepageUrl ? 'homepage' : 'record' }}">
              <view
                class="zan-cell__text"
              >
                {{ showHomepageUrl ? '店铺首页' : '我的记录' }}
            </view>
            </view>
            <view class="zan-cell__ft"></view>
          </view>
        </view>
      </view>
    </view>

    <view class="zan-popup zan-popup--bottom {{ showDialog ? 'zan-popup--show' : '' }}">
      <view class="zan-popup__mask" bindtap="toggleBannerDialog" />
      <view class="zan-popup__container banner-popup__container" style="z-index: 13;">
        <view wx:if="{{ promotionStr }}" class="banner-popup__item">
          <view class="banner-popup__item-hd">满减</view>
          <view class="banner-popup__item-bd">{{ promotionStr }}</view>
        </view>
        <view wx:if="{{ noticeContent }}" class="banner-popup__item">
          <view class="banner-popup__item-hd">公告</view>
          <view class="banner-popup__item-bd">{{ noticeContent }}</view>
        </view>
        <theme-view
          class="zan-btn zan-btn--large zan-btn--danger"
          bg="main"
          color="main-text"
          bindtap="toggleBannerDialog"
        >
          完成
        </view>
      </view>
    </view>
  </view>

