<template name="theme-takeAway">
  <takeaway-header
    logo="{{ CURRENT_GLOBAL_SHOP.image || CURRENT_GLOBAL_SHOP.logo }}"
    title="{{ CURRENT_GLOBAL_SHOP.name || CURRENT_GLOBAL_SHOP.shop_name }}"
    show-switch-offline-shop="{{ isMultiStore }}"
    notice-content="{{ banner.notice }}"
    promotion-str="{{ banner.preference }}"
    shop-style="{{ banner.shopStyle }}"
    show-homepage-url="{{ banner.showHomepageUrl }}"
    show-record-url="{{ banner.showRecordUrl }}"
  />

  <view class="main-container van-hairline--top">
    <scroll-view scroll-y class="tag-list" scrollIntoView="{{ tagItemIntoView }}">
      <view
        wx:for="{{ tags.list }}"
        wx:for-item="item"
        wx:for-index="index"
        wx:key="tagId"
        id="tag-item-{{ item.id }}"
        class="tag-item {{ item.id === theme.selectedTagId ? 'tag-item--active' : '' }}"
        style="{{ (tags.list.length - 1) == index ? 'border-bottom: 0' : ''  }}"
        data-tag-id="{{ item.id }}"
        data-tag-index="{{ index }}"
        bindtap="handleThemeTakeAwayTagTap"
      >
        <view class="van-multi-ellipsis--l3" style="line-height: 18px; max-height: 54px;">
          {{ item.title }}
        </view>
        <view
          wx:if="{{ tags.choosedNum[index] > 0 }}"
          class="tag-list__quantity"
        >
          {{ tags.choosedNum[index] }}
        </view>
      </view>
      <view style="height: 120px"></view>
    </scroll-view>
    <scroll-view
      scroll-y
      scroll-into-view="{{ theme.goodsListIntoView }}"
      bindscroll="handleThemeTakeAwayGoodsListScroll"
      bindtouchstart="handleThemeTakeAwayGoodsListTouchStart"
      class="tag-goods">
      <view
        wx:for="{{ tags.list }}"
        wx:for-item="item"
        wx:for-index="index"
        wx:key="id"
        class="takeaway-group"
      >
        <view class="tag-goods__hd" id="tag-goods-{{ item.id }}">{{ item.title }}</view>
        <view wx:if="{{ goods[item.id] && goods[item.id].list && goods[item.id].list.length }}" class="tag-goods_bd">
          <view
            wx:for="{{ goods[item.id].list }}"
            wx:for-item="goods"
            wx:for-index="goodsIndex"
            wx:key="id"
            class="goods-item"
            catchtap="ThemeTakeAwayHandleShowGoodsDetail"
            data-alias="{{ goods.alias }}"
          >
            <image
              src="{{ goods.pic_url }}"
              class="goods-image"
              data-tag-index="{{ index }}"
              data-goods-index="{{ goodsIndex }}"
            />
            <view class="goods-container">
              <view class="goods-title van-ellipsis">{{ goods.title }}</view>
              <view class="goods-price">¥ {{ goods.price }}</view>
            </view>
            <input-number
              class="takeout-input"
              data-alias="{{ goods.alias }}"
              data-id="{{ goods.id }}"
              data-title="{{ goods.title }}"
              data-picture="{{ goods.pic_url }}"
              min="{{ theme.choosedGoods[goods.alias].noneSku ? 0 : theme.choosedGoods[goods.alias].num }}"
              max="{{ theme.choosedGoods[goods.alias].noneSku ? theme.choosedGoods[goods.alias].stockNum : theme.MAX_VALUE }}"
              value="{{ theme.choosedGoods[goods.alias].num }}"
              is-value-show="{{ theme.choosedGoods[goods.alias].num > 0 }}"
              is-minus-show="{{ theme.choosedGoods[goods.alias].num > 0 }}"
              disabled="{{ goods.sold_status == 2 }}"
              bind:change="ThemeTakeAwayhandleGoodsBuy"
              bind:overlimit="ThemeTakeAwayhandleGoodsOverlimit"
            ></input-number>
          </view>
        </view>
        <view wx:elif="{{ goods[item.id].nodata }}" class="tag-goods__empty">
          暂无商品哦
        </view>
        <view wx:else class="tag-goods__empty">
          商品加载中...
        </view>
      </view>
    </scroll-view>

    <!-- 在多网点不营业的情况下，不展示这个条 -->
    <takeaway-bottom
      wx:if="{{ !CURRENT_GLOBAL_SHOP.isMultiStore || CURRENT_GLOBAL_SHOP.is_opening }}"
      alias="{{ alias }}"
      iphonex-class="bottom-bar-container"
      class="takeaway-bottom"
      start-fee="{{ theme.takeAwayStartFee }}"
      takeout-cart-data="{{ theme.takeoutCartData }}"
      all-choosed-num="{{ theme.allChoosedNum }}"
      all-choosed-price="{{ theme.allChoosedPrice }}"
      bind:toggleCart="toggleThemeTakeAwayCart"
    >
    </takeaway-bottom>
    <takeaway-cart
      iphonex-class="bottom-bar-container"
      takeout-cart-data="{{ theme.takeoutCartData }}"
      showTakeoutCart="{{ theme.showTakeoutCart }}"
      bind:toggleCart="toggleThemeTakeAwayCart"
      bind:clearCart="clearThemeTakeAwayCart"
      bind:goodsNumChange="handleThemeTakeAwayGoodsNumChange"
    >
    </takeaway-cart>

    <base-sku
      wx:if="{{ theme.activeSku }}"
      theme-class="{{ themeClass }}"
      goods="{{ theme.activeGoods }}"
      sku="{{ theme.activeSku }}"
      show="{{ theme.showTakeawaySku }}"
      buy-text="下一步"
      show-add-cart-btn="{{ false }}"
      hide-stock="{{ theme.activeSku.hide_stock }}"
      bind:buy="handleThemeTakeAwayBuyClicked"
      reset-stepper-on-hide
    >
    </base-sku>
  </view>
</template>

<template name="theme-takeAway-banner">
  <view class="banner {{ isMultiStore && 'is-multi-store' }}">
    <view class="banner__filter-blur" style="background-image: url({{ CURRENT_GLOBAL_SHOP.image || CURRENT_GLOBAL_SHOP.logo }})" />
    <view class="banner__mask" />
    <view class="banner__container">
      <image class="banner__logo" src="{{ CURRENT_GLOBAL_SHOP.image || CURRENT_GLOBAL_SHOP.logo }}" />
      <view class="banner__bd">
        <view class="banner__title" >
          {{ CURRENT_GLOBAL_SHOP.name || CURRENT_GLOBAL_SHOP.shop_name }}
          <navigator class="multi-store--selector" url="/packages/shop/multi-store/index/index" >[切换]</navigator>
        </view>
        <view wx:if="{{ banner.notice }}" class="banner__notice van-ellipsis" bindtap="toggleThemeTakeAwayBannerDialog">
          <text class="banner__notice-text">公告：{{ banner.notice }}</text>
        </view>
        <view wx:if="{{ banner.preference }}" class="banner__preference van-ellipsis" bindtap="toggleThemeTakeAwayBannerDialog">
          <theme-view
            custom-class="banner__preference-tag zan-tag zan-tag--danger"
            bg="main"
            border="main"
          >
            满减
          </theme-view>
          <text class="banner__preference-text">{{ banner.preference }}</text>
        </view>
      </view>
    </view>
  </view>

  <view class="zan-popup zan-popup--bottom {{ banner.showDialog ? 'zan-popup--show' : '' }}">
    <view class="zan-popup__mask" bindtap="toggleThemeTakeAwayBannerDialog" />
    <view class="zan-popup__container banner-popup__container" style="z-index: 13;">
      <view wx:if="{{ banner.preference }}" class="banner-popup__item">
        <view class="banner-popup__item-hd">满减</view>
        <view class="banner-popup__item-bd">{{ banner.preference }}</view>
      </view>
      <view wx:if="{{ banner.notice }}" class="banner-popup__item">
        <view class="banner-popup__item-hd">公告</view>
        <view class="banner-popup__item-bd">{{ banner.notice }}</view>
      </view>
      <theme-view
        custom-class="zan-btn zan-btn--large zan-btn--danger"
        bg="main"
        color="main-text"
        bindtap="toggleThemeTakeAwayBannerDialog"
      >
        完成
      </theme-view>
    </view>
  </view>
</template>
