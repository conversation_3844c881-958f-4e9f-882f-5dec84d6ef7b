.bottomcart {
  width: 100%;
  height: 44px;
  background-color: #fff;
  box-shadow: 0 -0.5px 0 0 rgba(211, 211, 211, .5), 0 -2px 4px 0 rgba(0, 0, 0, .1);
}

.bottomcart::after {
  content: '';
  display: block;
  visibility: hidden;
  clear: both;
}

.bottomcart__cart-icon {
  float: left;
}

.bottomcart__icon-wrapper {
  position: absolute;
  top: -16px;
  left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #f44;
  box-shadow: 0 2px 6px 0 rgba(255, 68, 68, .4);
  z-index: 1;
}

.bottomcart__icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url('https://b.yzcdn.cn/fix-base64/dac37dedb88393a4f97efb0212ba5b71b21c6ef0d17ea2908cf07b9c8211738c.png');
  background-size: 20px 20px;
}

.bottomcart__choosed-num {
  position: absolute;
  top: 0px;
  left: 34px;
  height: 14px;
  min-width: 14px;
  border: 1px solid #f44;
  border-radius: 8px;
  background: white;
  font-size: 10px;
  line-height: 14px;
  text-align: center;
  color: #f44;
}

.bottomcart__choosed-num--big {
  padding: 0 2px;
}

.bottomcart__choosed-price-wrapper {
  padding-left: 76px;
  line-height: 44px;
  font-size: 12px;
  color: #a3a3a3;
}

.bottomcart__choosed-price {
  font-weight: 600;
  color: #0f0f0f;
}

/**
 * 购物车为空
 */
.bottomcart__cart-icon--zero .bottomcart__choosed-num {
  opacity: 0;
}

.bottomcart__cart-icon--zero .bottomcart__choosed-price {
  font-weight: 400;
  color: #a3a3a3;
}

/**
 * 不能去结算（购物车为空，或者还没到起送金额）
 */
.bottomcart__cart-icon--disabled .bottomcart__icon-wrapper {
  background: #f2f2f2;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, .2);
}

.bottomcart__cart-icon--disabled .bottomcart__choosed-num {
  border-color: #f2f2f2;
  color: #666;
}

.bottomcart__cart-icon--disabled .bottomcart__choosed-price {
  color: #999;
}

.bottomcart__cart-icon--disabled .bottomcart__icon {
  background-image: url('https://b.yzcdn.cn/fix-base64/1d972f743de7f6aef0b414a9049d337bf58164ffa811fd021ffdece10645953c.png');
}

/**
 * 右下角的结算按钮
 */
.bottomcart__buy-button {
  float: right;
  padding: 0 31px;
  background: #f44;
  line-height: 44px;
  font-size: 16px;
  color: #fff;
}

.bottomcart__buy-button--disabled {
  background: #efefef;
  color: #a3a3a3;
}

@media (max-width: 320px) {
  .bottomcart__buy-button--disabled {
    padding: 0 15px;
  }
}
