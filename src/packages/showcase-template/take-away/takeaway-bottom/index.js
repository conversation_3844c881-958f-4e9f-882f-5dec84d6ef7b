import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  properties: {
    alias: String,
    startFee: {
      type: Number,
      value: 0
    },
    allChoosedNum: {
      type: Number,
      value: 0
    },
    allChoosedPrice: {
      type: Number,
      value: 0
    },
    takeoutCartData: {
      type: Array,
      value: []
    }
  },

  externalClasses: ['iphonex-class'],

  methods: {
    showTakeoutCartAction() {
      if (!this.data.takeoutCartData.length) return;
      this.triggerEvent('toggleCart');
    },
    goToPay() {
      const order = this.data.takeoutCartData.map((goods) => {
        return {
          activityAlias: '',
          activityId: 0,
          activityType: 0,
          message: goods.message,
          num: goods.num,
          price: goods.price,
          skuId: goods.skuId,
          goodsId: goods.goodsId,
          kdtId: app.getKdtId(),
          bizTracePointExt: ''
        };
      });

      const dbData = {
        type: 'goods',
        goods_list: order
      };
      // 非首页和tab页情况下，需要带上页面alias
      const alias = this.data.alias;
      if (alias) {
        dbData.takeoutAlias = alias;
      }

      const dbid = app.db.set(dbData);

      wx.navigateTo({
        url: '/packages/order/index?orderFrom=cart&dbid=' + dbid
      });
    }
  }
});
