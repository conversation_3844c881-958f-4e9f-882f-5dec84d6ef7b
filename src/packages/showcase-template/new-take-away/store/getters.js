export default {
  // tag Id 集合
  tagIdsGetter() {
    const { tagList } = this.data;
    const ids = tagList.map(tag => {
      return String(tag.id);
    }) || [];
    this.setYZData({
      tagIds: ids
    });
  },

  // 处于选中的tag
  activeTagGetter() {
    const { tagList } = this.data;
    this.setYZData({
      activeTag: tagList.find(tag => tag.active === true)
    });
  },

  // 添加到购物车的商品数量
  allChoosedNumGetter() {
    const { cartGoods } = this.data;
    const num = cartGoods.reduce((pre, goods) => {
      return pre + goods.num;
    }, 0);
    this.setYZData({
      allChoosedNum: num
    });
  },

  // 添加到购物车的商品总价
  allChoosedPriceGetter() {
    const { cartGoods } = this.data;
    const price = cartGoods.reduce((pre, goods) => {
      return pre + (goods.price * goods.num);
    }, 0);
    this.setYZData({
      allChoosedPrice: price
    });
  }
};
