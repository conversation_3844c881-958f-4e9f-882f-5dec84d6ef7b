import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { parseGoods } from '../helpers';
import { NONE_SKU_SUFFIX } from '../constant';
import { getGoodsId, getGoodsById, getStartfee, getCouponList, takeCoupon } from '../api';

const app = getApp();

export default {
  // 显示提示
  showToast(...args) {
    let options = {};
    if (args.length === 1 && typeof args[0] === 'object') {
      options = args[0];
    } else {
      options = {
        title: args[0],
        icon: args[1],
        duration: args[2]
      };
    }

    wx.showToast({
      title: options.title == null ? '加载中' : options.title,
      icon: options.icon == null ? 'none' : options.icon,
      duration: options.duration == null ? 3000 : options.duration
    });
  },

  // 隐藏提示
  hideToast() {
    wx.hideToast();
  },

  hideLoading() {
    wx.hideToast();
  },

  showLoading(payload) {
    this.showToast({
      icon: 'loading',
      duration: 100000,
      ...payload
    });
  },

  initShopInfo(data) {
    const { shop, config } = data;

    // banners数据 包括优惠逻辑计算
    const bannerData = config[0];

    // 优惠&满减信息判空
    const reduceData =
      ((bannerData.meet_reduce && bannerData.meet_reduce.reward_detail) || {}).content || [];
    let reduceStr = '';
    if (reduceData.length > 0) {
      reduceStr = '在线支付';
      const allReduceData = [];
      reduceData.forEach((reduceItem) => {
        reduceItem.forEach((item) => {
          allReduceData.push(item.title);
        });
      });
      reduceStr += allReduceData.join('，');
    }

    const {
      team_logo: teamLogo,
      team_name: teamName,
      shop_style: shopStyle,
      show_homepage_url: showHomepageUrl,
      show_record_url: showRecordUrl,
      show_sold_num: showSoldNum,
    } = bannerData;
    const banner = {
      logo: cdnImage(shop.image || teamLogo, '!730x0.jpg'),
      title: shop.name || shop.shop_name || teamName || '',
      preference: reduceStr,
      shopStyle: shopStyle || 'center',
      showHomepageUrl,
      showRecordUrl,
      showSoldNum
    };

    // 公告
    const noticeData = config[1] || {};
    banner.notice = noticeData.content || '';

    this.INIT_SHOP_INFO({ banner });

    // 店铺优惠券
    const coupons = (config[3] && config[3].coupons) || [];
    this.initCoupons(coupons);
  },

  initCoupons(coupons) {
    const idsArr = coupons.map((coupon) => coupon.id);
    if (!idsArr.length) return;
    getCouponList({
      ids: idsArr.join(','),
      pageNo: 1,
      pageSize: 10
    }).then((list) => {
      this.INIT_COUPONS({ coupons: list });
    });
  },

  initTagAlias(data) {
    const { config } = data;

    const tagData = config[2];
    let tagList = tagData.sub_entry || [];

    // 容错过滤处理
    tagList = tagList.filter((item) => item.alias && item.title);

    // 设置分组选择数量字段、选择状态
    tagList.forEach((item, index) => {
      item.choosedNum = 0;
      // fix scrollIntoView id不能以数字开头的bug
      item.alias = 'tag-' + item.alias;
      // 默认选中第一项
      if (index === 0) {
        item.active = true;
      } else {
        item.active = false;
      }
    });

    // 设置tagIds 搜索页需要
    app.globalData.takeaway = {
      ...app.globalData.takeaway,
      tagList
    };
    this.INIT_TAG_ALIAS({ tagList });
  },

  // 初始化商品数量及id
  initGoodsId() {
    const { tagIds } = this.data;
    getGoodsId({
      tagIds: ['discount', ...tagIds]
    }).then((data) => {
      if (data.discount.length) {
        // 在分组顶部加一项优惠分组
        this.ADD_UMP_TAG({
          tag: {
            alias: 'discount',
            id: 'discount',
            title: '优惠',
            choosedNumber: 0
          }
        });
      }
      this.UPDATE_FETCH_STATUS();
      this.UPDATE_ACTIVE_TAG('umpTag');
      this.INIT_GOODS_IDS({ ids: data });
      this.UPDATE_FETCH_STATUS();
    });
  },

  initStartFee() {
    getStartfee().then((res) => {
      this.INIT_STARTFEE({
        startfee: res.value
      });
    });
  },

  getGoodsInfo(ids) {
    const param = {
      goodsIds: ids
    };
    getGoodsById(param).then((data) => {
      Object.keys(data).forEach((key) => {
        data[key] = parseGoods(data[key]);
      });
      this.UPDATE_GOODS_INFO(data);
    });
  },

  updateCurrentActiveGoods(params) {
    const { goods, tagId, showPreview } = params;
    this.UPDATE_CURRENT_ACTIVE_GOODS({
      goods,
      tagId
    });
    // 点击input-number不显示商品详情
    if (showPreview) this.UPDATE_PREVIEW_GOODS(true);
  },

  updateSkuData(goods) {
    const { alias, num } = goods;
    const { currentActiveGoods } = this.data;
    const { sku } = currentActiveGoods;
    const noShowSku = sku.noneSku && (!sku.messages || !sku.messages.length);

    // 更新商品sku
    currentActiveGoods.noneSku = sku.noneSku;
    currentActiveGoods.stockNum = sku.stockNum;

    // 无sku且不需要留言，直接更新购物车
    if (noShowSku) {
      // 单sku设置skuId
      currentActiveGoods.num = num;
      currentActiveGoods.skuId = NONE_SKU_SUFFIX;
      this.UPDATE_CURRENT_ACTIVE_GOODS({ goods: currentActiveGoods });

      this.UPDATE_GOODS_TO_CART({
        alias,
        skuId: NONE_SKU_SUFFIX,
        num
      });
    } else {
      this.UPDATE_CURRENT_ACTIVE_GOODS({ goods: currentActiveGoods });
      this.UPDATE_SHOW_SKU_STATUS(true);
    }

    // 更新选择商品 多sku在组件回调处更新
    if (noShowSku) this.UPDATE_CHOOSED_GOODS({ alias });
  },

  multiSkuGoodsToCart(params) {
    const { skuId, num } = params;
    let { currentActiveGoods } = this.data;
    const alias = currentActiveGoods.alias;

    currentActiveGoods = { ...currentActiveGoods, ...params };

    this.UPDATE_CURRENT_ACTIVE_GOODS({ goods: currentActiveGoods });
    this.UPDATE_GOODS_TO_CART({
      alias,
      skuId,
      num,
      hasSku: true
    });
    this.UPDATE_CHOOSED_GOODS({ alias, skuId });
    this.UPDATE_SHOW_SKU_STATUS(false);
  },

  takeCoupon(params) {
    takeCoupon({
      groupId: params.id
    })
      .then(() => {
        this.showToast('领取成功');
      })
      .catch((err) => {
        this.showToast(err.msg);
      });
  }
};
