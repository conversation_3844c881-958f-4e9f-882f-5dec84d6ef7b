export default {
  INIT_SHOP_INFO(payload) {
    this.setYZData({
      banner: payload.banner
    });
  },

  INIT_COUPONS(payload) {
    this.setYZData({
      coupons: payload.coupons
    });
  },

  INIT_TAG_ALIAS(payload) {
    this.setYZData({
      tagList: payload.tagList
    });
    this.tagIdsGetter();
  },

  INIT_UPDATE_DATE() {
    this.setYZData({
      banner: {},
      tagList: [],
      tagIds: [],
      goodsIdsMap: {},
      goodsInfo: {},
      activeTag: {},
      isFetching: true,
      startfee: '',
      coupons: [],
    });
  },

  INIT_GOODS_IDS(payload) {
    this.setYZData({
      goodsIdsMap: payload.ids
    });
  },

  INIT_STARTFEE(payload) {
    this.setYZData({
      startfee: payload.startfee
    });
  },

  ADD_UMP_TAG(payload) {
    const { tagList } = this.data;
    tagList.unshift(payload.tag);
    this.setYZData({
      tagList
    });
  },

  UPDATE_GOODS_INFO(payload) {
    this.setYZData({
      goodsInfo: {
        ...this.data.goodsInfo,
        ...payload
      }
    });
  },

  UPDATE_FETCH_STATUS() {
    this.setYZData({
      isFetching: false
    });
  },

  UPDATE_ACTIVE_TAG(payload) {
    const { tagList } = this.data;
    const alias = payload;

    tagList.forEach(tag => {
      if (tag.alias === alias) {
        tag.active = true;
      } else {
        tag.active = false;
      }
    });

    this.setYZData({
      tagList
    });

    this.activeTagGetter();
  },

  UPDATE_IS_TOUCH_GOODS_LIST(payload) {
    this.setYZData({
      isTouchGoodsList: payload
    });
  },

  UPDATE_IS_UP_SCROLL(payload) {
    this.setYZData({
      isUpScroll: payload
    });
  },

  UPDATE_SHOW_CART_STATUS(payload) {
    this.setYZData({
      showCart: payload
    });
  },

  UPDATE_CURRENT_ACTIVE_GOODS(payload) {
    this.setYZData({
      currentActiveGoods: payload.goods
    });
    // 商品预览独立加个tagId
    if (payload.tagId !== undefined) {
      this.data.currentActiveGoods.tagId = payload.tagId;
      this.setYZData({
        currentActiveGoods: this.data.currentActiveGoods
      });
    }
  },

  UPDATE_PREVIEW_GOODS(payload) {
    this.setYZData({
      showGoodsPreview: payload
    });
  },

  UPDATE_SHOW_SKU_STATUS(payload) {
    this.setYZData({
      showTakeAwaySku: payload
    });
  },

  UPDATE_GOODS_TO_CART(payload) {
    const {
      cartGoods, currentActiveGoods, tagList
    } = this.data;
    const {
      skuId, alias, num, hasSku
    } = payload;
    // 当前商品的差值
    let diff = 0;

    const _cartGoods = JSON.parse(JSON.stringify(cartGoods));
    const _tagList = tagList;

    const findIndex = _cartGoods.findIndex(goods => (goods.skuId === skuId && goods.alias === alias));
    // 购物车已有此商品
    if (findIndex !== -1) {
      diff = num;
      // 多sku累加
      if (hasSku) {
        _cartGoods[findIndex].num += num;
        diff = num;
        // 判断当前规格的商品数量是否已经大于库存
        let currentSkuStockNum;
        const skuList = _cartGoods[findIndex].sku.list;
        for (let i = 0; i < skuList.length; i++) {
          if (skuId === skuList[i].id) {
            currentSkuStockNum = skuList[i].stockNum;
          }
        }
        if (_cartGoods[findIndex].num > currentSkuStockNum) {
          return this.showToast('库存不足');
        }
      } else {
        diff = num - _cartGoods[findIndex].num;
        _cartGoods[findIndex].num = num;
      }
    } else {
      diff = currentActiveGoods.num;
      // 新加商品
      _cartGoods.push(JSON.parse(JSON.stringify(currentActiveGoods)));
    }

    // 设置对应分组选择数量
    _tagList.forEach(tag => {
      if (+tag.id === currentActiveGoods.tagId) {
        tag.choosedNum += diff;
      }
    });

    this.setYZData({
      cartGoods: _cartGoods,
      tagList: _tagList
    });
  },

  UPDATE_CHOOSED_GOODS(payload) {
    const { alias, skuId, isCart } = payload;
    const { currentActiveGoods, cartGoods } = this.data;
    let aliasChoosedGoods = this.data.choosedGoods[alias];

    // 购物篮修改商品数量
    if (isCart) {
      aliasChoosedGoods.num = cartGoods.reduce((pre, goods) => {
        if (goods.alias === alias) {
          return pre + goods.num;
        }
        return pre;
      }, 0);
    } else if (aliasChoosedGoods && !aliasChoosedGoods.noneSku) {
      const skuGoods = cartGoods.find(goods => (goods.skuId === skuId && goods.alias === alias));
      // 判断当前规格的商品数量是否已经大于库存
      let currentSkuStockNum;
      const skuList = skuGoods.sku.list;
      for (let i = 0; i < skuList.length; i++) {
        if (skuId === skuList[i].id) {
          currentSkuStockNum = skuList[i].stock_num;
        }
      }
      if (skuGoods.num === currentSkuStockNum) return;
      // 多sku数量需要累加
      aliasChoosedGoods = Object.assign({}, currentActiveGoods, {
        num: aliasChoosedGoods.num + currentActiveGoods.num
      });
    } else {
      aliasChoosedGoods = currentActiveGoods;
    }

    this.setYZData({
      choosedGoods: Object.assign({}, this.data.choosedGoods, {
        [alias]: JSON.parse(JSON.stringify(aliasChoosedGoods))
      })
    });

    this.allChoosedNumGetter();
    this.allChoosedPriceGetter();
  },

  CLEAR_CART_GOODS() {
    // 将所有选择的商品数量设为0
    const { choosedGoods, tagList } = this.data;
    Object.keys(choosedGoods).forEach(alias => {
      choosedGoods[alias].num = 0;
    });
    // 清空tag数量
    tagList.forEach(tag => tag.choosedNum = 0);

    this.setYZData({
      cartGoods: [],
      showCart: false,
      choosedGoods,
      tagList
    });

    this.allChoosedNumGetter();
    this.allChoosedPriceGetter();
  },

  UPDATE_SHOW_UMP_DIALOG(payload) {
    this.setYZData({
      showUmpDialog: payload
    });
  },

  MERGE_GOODS_TO_CART(payload) {
    const { goodsList } = payload;
    const { cartGoods } = this.data;

    for (let i = 0; i < goodsList.length; i++) {
      const findIndex = cartGoods.findIndex(goods => (goods.skuId === goodsList[i].skuId && goods.alias === goodsList[i].alias));
      // 相同商品修改数量
      if (findIndex !== -1) {
        cartGoods[findIndex].num += goodsList[i].num;
      } else {
        cartGoods.push(goodsList[i]);
      }
    }

    this.setYZData({
      cartGoods
    });

    this.allChoosedNumGetter();
    this.allChoosedPriceGetter();
  },
};
