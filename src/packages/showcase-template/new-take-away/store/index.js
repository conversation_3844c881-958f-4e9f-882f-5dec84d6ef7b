import actions from './actions';
import mutations from './mutations';
import getters from './getters';

const state = {
  // 头部banner数据
  banner: {},

  // 侧栏分组
  tagList: [],

  // tag Id 集合
  tagIds: [],

  // 商品ids tagId => [goodsId]
  goodsIdsMap: {},

  // 商品信息 id => info
  goodsInfo: {},

  // 处于选中的tag
  activeTag: {},

  // 获取商品
  isFetching: true,

  // 起送费
  startfee: '',

  // 当前操作的商品
  currentActiveGoods: {},

  // 当前操作的商品sku
  currentActiveSku: {},

  // 购物车商品 skuId纬度区分商品
  cartGoods: [],

  // 添加到购物车的商品数量
  allChoosedNum: 0,

  // 添加到购物车的商品总价
  allChoosedPrice: 0,

  // 选择的商品 alias维度区分商品
  choosedGoods: {},

  // 区分是否为商品区滑动
  isTouchGoodsList: false,

  // 区分上滑还是下滑
  isUpScroll: false,

  // 显示商品预览弹窗
  showGoodsPreview: false,

  // 显示购物车抽屉
  showCart: false,

  // 显示sku
  showTakeAwaySku: false,

  // 显示活动弹窗
  showUmpDialog: false,

  // 优惠券
  coupons: [],

  // tagId dbid
  dbid: '',

  // 设置share数据，避免分享脚本异常
  share: {
    title: '',
    imageUrl: ''
  }
};

export { state, getters, actions, mutations };
