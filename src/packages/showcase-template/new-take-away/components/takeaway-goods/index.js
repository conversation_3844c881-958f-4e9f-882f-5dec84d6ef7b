import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    tagAlias: {
      type: String,
    },

    tagId: {
      type: Number,
    },

    goodsId: {
      type: Number,
    },

    goodsInfo: {
      type: Object,
      value: {},
    },

    choosedGoods: {
      type: Object,
      value: {},
    },
    showSoldNum: {
      type: Boolean,
      default: true,
    },
  },

  data: {
    loading: true,
    goods: {},
  },

  observers: {
    goodsInfo(val) {
      if (val) {
        this.setYZData({
          loading: false,
          goods: val,
        });
      }
    },
  },

  ready() {
    const { goodsId } = this.data;
    this.ob = this.createIntersectionObserver();
    this.ob
      .relativeToViewport({
        bottom: -10,
      })
      .observe(`.goods-item-${goodsId}`, () => {
        this.triggerEvent('relative-to-viewport', {
          id: goodsId,
        });
        this.ob.disconnect();
      });
  },

  methods: {
    onTap() {
      const { goods, tagAlias, tagId } = this.data;
      this.triggerEvent('preview', {
        goods,
        tagAlias,
        tagId,
      });
    },

    onChange(e) {
      const { goods, tagAlias, tagId } = this.data;
      this.triggerEvent('change', {
        ...e.detail,
        goods,
        tagAlias,
        tagId,
      });
    },

    onOverlimit(e) {
      const { goods, tagAlias, tagId } = this.data;
      this.triggerEvent('overlimit', {
        ...e.detail,
        goods,
        tagAlias,
        tagId,
      });
    },
  },
});
