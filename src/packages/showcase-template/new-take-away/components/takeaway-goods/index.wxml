<import src="../../template/index.wxml"></import>

<view class="goods-item goods-item-{{goodsId}}" bind:tap="onTap">
  <!-- 加载占位模版 -->
  <template
    wx:if="{{ loading }}"
    is="goods-skeleton"
  />
  <block wx:else>
    <view class="goods-item__image">
      <image
        src="{{ goods.picUrl }}"
        mode="aspectFit"
        lazy-load
      />
    </view>
    <view class="goods-item__detail">
      <text class="goods-item__detail--title">{{  goods.title }}</text>
      <text class="goods-item__detail--desc">{{  goods.sellPoint }}</text>
      <view wx:if="{{ goods.activity }}" class="goods-item__detail--tags">
        <block wx:if="{{ goods.activity.discountType }}">
          <van-tag plain type="danger" class="goods-item__detail--tags-tag">
            限时折扣
          </van-tag>
        </block>
        <block wx:else>
          <van-tag plain type="danger" class="goods-item__detail--tags-tag">会员折扣</van-tag>
        </block>
        <van-tag wx:if="{{ goods.activity.quota }}" plain type="danger" class="goods-item__detail--tags-tag">{{ '限购' + goods.activity.quota + '件' }}</van-tag>
      </view>
      <view 
        wx:if="{{ goodsInfo.processProcedure &&  goodsInfo.processProcedure.surplusInfo }}"
        class="goods-item__detail--surplus-info"
      >{{ goodsInfo.processProcedure.surplusInfo }}</view>
      <view wx:if="{{ showSoldNum }}" class="goods-item__sales">销量 {{ goods.sku.soldNum }}</view>
      <view wx:else style="height: 18px"></view>
      <view class="goods-item__detail--price">
        <text class="goods-item__detail--price-current">¥<text>{{ utils.cent2yuan(goods.price) + (goods.sku.noneSku ? '' : '起') }}</text></text>
        <text wx:if="{{ goods.sku.oldPrice }}" class="goods-item__detail--price-origin">{{ goods.sku.oldPrice }}</text>
      </view>
      <view 
        wx:if="{{ goodsInfo.processProcedure && goodsInfo.processProcedure.procedureList }}"
        class="goods-item__detail--procedure-list"
      >
        <text
          wx:for="{{ goodsInfo.processProcedure.procedureList }}"
          wx:for-item="procedure"
          wx:key="{{ procedure.name }}"
          class="{{ procedure.isCurrent ? 'goods-item__detail--procedure-list--current' : '' }}"
        >
          {{ (index === 0) ? procedure.name : '一' + procedure.name }}
        </text>
      </view>
      <view 
        wx:if="{{ goodsInfo.processProcedure && goodsInfo.processProcedure.processInfo }}"
        class="goods-item__detail--process-info"
      >{{ goodsInfo.processProcedure.processInfo }}</view>
    </view>
    <view class="goods-item__opt">
      <input-number
        class="goods-item__opt--input"
        min="{{ choosedGoods[goods.alias].noneSku ? 0 : choosedGoods[goods.alias].num }}"
        max="{{ goods.activity.quota ? goods.activity.quota : choosedGoods[goods.alias].noneSku ? choosedGoods[goods.alias].stockNum : 99999 }}"
        value="{{ choosedGoods[goods.alias].num }}"
        is-value-show="{{ choosedGoods[goods.alias].num > 0 }}"
        is-minus-show="{{ choosedGoods[goods.alias].num > 0}}"
        disabled="{{ goods.sku.soldStatus === 2 }}"
        bind:change="onChange"
        bind:overlimit="onOverlimit"
      />
    </view>
  </block>
</view>

<wxs src="./index.wxs" module="utils" />
