<view class="takeaway-coupon {{ !coupon.stock_qty ? 'no_more' : '' }}">
  <view class="takeaway-coupon__main">
    <view class="takeaway-coupon__main--amount">
      <block wx:if="{{ coupon.denominations }}">¥<text>{{ coupon.denominations / 100 }}</text></block>
      <block wx:else><text>{{ coupon.discount / 10 }}折</text></block>
    </view>
    <text class="takeaway-coupon__main--limit">
      <block wx:if="{{ coupon.condition }}">满{{ coupon.condition / 100 }}元可用</block>
      <block wx:else>无门槛</block>
    </text>
  </view>
  <user-authorize
    scene="get_coupon"
    authTypeList="{{ ['nicknameAndAvatar'] }}"
    btn-class="takeaway-coupon__btn"
    bind:next="onTake"
  >
    <view>{{ coupon.stock_qty ? '领取' : '已领'}}</view>
  </user-authorize>
</view>
