.takeaway-coupon {
  display: inline-block;
  padding: 0 10px;
  position: relative;
  background-color: #ffe9b7;
  color: #f39343;
  height: 40px;
  line-height: 40px;
  margin-right: 10px;

  &::before,
  &::after {
    content: "";
    position: absolute;
    top: 0;
    background: url('https://b.yzcdn.cn/fix-base64/1f60dd4cea894e2a8a1300d810788015ee0dfb4b17628f7720679e7280044e73.png') no-repeat;
    height: 100%;
    width: 6px;
  }

  &::before {
    left: 0;
  }

  &::after {
    right: 0;
    transform: rotateY(180deg)
  }

  &__main {
    display: inline-block;
    margin-right: 14px;

    &--amount {
      display: inline;
      font-size: 12px;
      margin-right: 8px;
      vertical-align: middle;

      text {
        font-size: 16px;
        font-weight: bold;
      }
    }

    &--limit {
      font-size: 10px;
      vertical-align: middle;
    }
  }

  &__btn {
    display: inline-block;
    font-size: 12px;
    font-weight: 500;
    border-left: 1px dashed #f39343;
    height: 20px;
    padding-left: 8px;
    line-height: 20px;
    vertical-align: middle;
  }

  &.no_more {
    background-color: #cfcfcf;
    color: #fff;

    &::before,
    &::after {
      background: url('https://b.yzcdn.cn/fix-base64/be4ebeb270e22c1907ef64843cbb1d2d4d79672eb3e9aae644969a248ed45cbf.png') no-repeat;
    }

    .takeaway-coupon__btn {
      border-left: 1px dashed #fff;
    }
  }
}