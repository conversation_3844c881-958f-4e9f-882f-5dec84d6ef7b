<view class="takeout-header {{ shopStyle }}">
  <view class="takeout-header__detail">
    <!-- 店铺logo -->
    <image class="takeout-header__detail--logo" src="{{ logo }}" />
    <view class="takeout-header__detail--info">
      <view class="takeout-header__detail--info-wrap">
        <!-- 店铺名称 -->
        <text class="takeout-header__detail--info-title" bindtap="handleTitleClick">{{ title }}</text>
        <navigator
          wx:if="{{ showSwitchOfflineShop }}"
          class="takeout-header__detail--info-switch"
          url="/packages/shop/multi-store/index/index" 
        >
          [切换]
        </navigator>
      </view>
      <view wx:if="{{ noticeContent || promotionStr }}" class="takeout-header__detail--info-wrap">
        <!-- 没有公告信息时，显示当前活动   两个都没有此列不显示 -->
        <block wx:if="{{ noticeContent }}">
          <!-- 公告栏 -->
          <view
            class="takeout-header__detail--info-notice van-ellipsis"
          >
            公告：{{ noticeContent }}
          </view>
        </block>
        <block wx:else>
          <view
            class="takeout-header__detail--info-activity van-ellipsis"
            bindtap="showAcitvityDialogHandler"
          >
            <van-tag plain type="danger" class="takeout-header__detail--info-activity__tag">满减</van-tag>{{ promotionStr }}
          </view>
        </block>
        <!-- 优惠活动 -->
        <view 
          class="takeout-header__detail--info-cell"
          bindtap="showActivityDialogHandler"
        >
          <text class="takeout-header__detail--info-cell__text">更多优惠</text>
          <van-icon name="arrow" class="takeout-header__detail--info-cell__arrow" />
        </view>
      </view>
    </view>
  </view>
  <!-- 我的记录 店铺首页 link 栏 -->
  <view
    wx:if="{{ showHomepageUrl || showRecordUrl }}"
    class="takeout-header__links"
  >
    <block wx:if="{{ showHomepageUrl && showRecordUrl }}">
      <view class="takeout-header__links--item" bindtap="handleRecordClick">我的记录</view>
      <view class="takeout-header__links--item" bindtap="handleHomeClick">店铺首页</view>
    </block>
    <block wx:else>
      <view class="takeout-header__links--cell" bindtap="handleSingleLinkClick">
        <text class="takeout-header__links--cell-text">{{ showHomepageUrl ? '店铺首页' : '我的记录'}}</text>
        <van-icon name="arrow" class="takeout-header__links--cell-arrow" />
      </view>
    </block>
  </view>
  <van-icon 
    name="search"
    class="takeout-header__search"
    bindtap="navigateToSearchHandler"
  />
</view>