.takeout-header {
  position: relative;
  z-index: 10;
  width: calc(100% - 20px);
  background-color: #fff;
  box-shadow: rgba(0, 0, 0, .1) 0 2px 12px 0;
  padding: 5px 10px 10px 10px;

  &__detail {
    margin-bottom: 15px;

    &--logo {
      flex-shrink: 0;
      width: 48px;
      height: 48px;
      box-shadow: rgba(0, 0, 0, .2) 0 2px 4px 0;
    }

    &--info {
      &-wrap {
        display: flex;
      }

      &-title {
        font-size: 16px;
        color: #111;
        font-weight: bold;
      }

      &-switch {
        font-size: 12px;
        color: #666;
        margin-left: 5px;
        line-height:22px;
      }

      &-notice,
      &-activity {
        width: 216px;
        font-size: 12px;
        color: #999;
        text-overflow: ellipsis;
        overflow:hidden;
        white-space:nowrap;

        &__tag {
          margin-right: 5px;
        }
      }

      &-cell {
        display: flex;
        align-items: center;
        width: 64px;
        line-height: 12px;
        margin-left: 10px;

        &__text {
          text-align: left;
          flex: 4;
          font-size: 12px;
          color: #999; 
        }
  
        &__arrow {
          text-align: right;
          flex: 1;
          font-size: 12px;
          color: #999;
        }
      }
    }
  }

  &__links {
    display: flex;

    &--item {
      flex: 1;
      text-align: center;
      font-size: 12px;
      color: #666;
    }

    &--cell {
      width: 100%;
      display: flex;
      justify-content: space-between;

      &-text {
        text-align: left;
        flex: 1;
        font-size: 12px;
        color: #666; 
      }

      &-arrow {
        text-align: right;
        flex: 1;
        font-size: 12px;
        color: #999;
      }
    }
  }

  &.left {
    .takeout-header__detail {
      display: flex;

      &--logo {
        margin-right: 15px;
      }

      &--info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }

  &.center {
    .takeout-header__detail {
      display: flex;
      flex-direction: column;
      align-items: center;

      &--info-wrap {
        justify-content: center;

        &:first-child {
          margin-top: 10px;
        }

        &:last-child {
          margin-top: 5px;
        }
      }

      &--info-notice {
        width: 256px;
        padding-left: 24px;
      }
    }
  }

  &__search {
    position: absolute;
    top: 2px;
    right: 16px;
    font-size: 20px;
    color: #666;
  }
}