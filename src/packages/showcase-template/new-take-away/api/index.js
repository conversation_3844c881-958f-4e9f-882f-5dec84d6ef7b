import { SHOP_TYPE } from '../constant';

const app = getApp();
const http = app.request;
const shopType = app.globalData.isRetailApp ? SHOP_TYPE.RETAIL : SHOP_TYPE.WSC;

// 获取优惠分组对应商品
export function getUmpGoods(data) {
  return http({
    path: '/wscshop/goods/goodsByUmp.json',
    data
  });
}

// 获取起送价
export function getStartfee() {
  return http({
    path: 'v2/showcase/tplStartFee/startfee.json',
    query: {
      offline_id: app.getOfflineId() || 0
    }
  });
}

// 查询优惠券
export function getCouponList(data) {
  return http({
    path: '/wscshop/ump/coupon/couponList.json',
    data
  });
}

// 获取优惠券
export function takeCoupon(data) {
  return http({
    path: '/wscshop/ump/coupon/fetchCoupon.json',
    method: 'POST',
    data
  });
}

// 获取商品分组的所有商品Id
export function getGoodsId(data) {
  return http({
    path: '/wscshop/showcase/takeout/getGoodsId.json',
    method: 'POST',
    data: {
      ...data,
      shopType
    }
  });
}

// 根据id获取商品信息
export function getGoodsById(data) {
  return http({
    path: '/wscshop/showcase/takeout/getGoodsById.json',
    method: 'POST',
    data: {
      ...data,
      shopType
    }
  });
}
