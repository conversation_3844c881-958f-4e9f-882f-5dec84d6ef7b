@import './template/skeleton.scss';

$gary-dark: #666;
$white: #fff;

.theme-new-takeAway {
  &--body {
    display: flex;

    .takeAway-body__sidebar {
      width: 28vw;
      height: 100vh;
      background-color: #f8f8f8;

      &--block {
        color: $gary-dark;
        padding: 15px;
        line-height: 20px;
        position: relative;
        text-align: center;

        &-num {
          position: absolute;
          right: 0;
          top: 2px;
          width: 20px;
          height: 15px;
          line-height: 15px;
          border-radius: 7px;
          font-size: 10px;
          color: $white;
          background-color: red;
        }

        &-title {
          font-size: 14px;
          word-break: break-all;
          text-align: left;

          image {
            width: 16px;
            height: 16px;
            vertical-align: middle;
            margin-right: 5px;
          }

          text {
            vertical-align: middle;
          }
        }

        &.active {
          background-color: $white;
          color: #111;
        }
      }
    }

    .takeAway-body__goods {
      position: relative;
      width: 72vw;
      height: 100vh;
      padding: 0 0 40px 10px;
      background-color: $white;
      margin-top: 0px;

      &--title {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 8;
        padding-left: 10px;
        font-size: 12px;
        color: $gary-dark;
        height: 30px;
        line-height: 30px;
        width: 78vw;
        background-color: $white;
      }

      &-coupon {
        width: 72vw;
        display: flex;
        white-space: nowrap;
        padding: 10px 0;
        overflow: auto;
        transition: margin 0.1s linear;
        margin-top: 0;
      }

      &--scroll {
        height: inherit;

        &-container {
          &-title {
            height: 30px;
            font-size: 12px;
            color: $gary-dark;
            line-height: 30px;
          }

          &-wrap {
            margin-bottom: 20px;
            padding-right: 15px;
          }

          &-tpl {
            padding-right: 15px;
          }

          &-empty {
            font-size: 14px;
            height: 54px;
            line-height: 54px;
            color: #999;
            text-align: center;
          }
        }

        &-hack {
          width: 100%;
          height: 50vh;
        }
      }
    }

    // 通过父容器上移来达到隐藏优惠券区域的效果
    // 使用transform会影响内部的fixed
    .hidden-coupon {
      margin-top: -60px;
    }

    .coupon-skeleton {
      height: 60px;
      transition: height 0.1s linear;
    }

    .hidden-skeleton {
      height: 0;
    }
  }

  &--bottom {
    z-index: 11;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
