<import src="./template/index.wxml"></import>

<template name="theme-new-takeAway">
  <!-- 店铺信息 -->
  <takeaway-header
    logo="{{ CURRENT_GLOBAL_SHOP.image || CURRENT_GLOBAL_SHOP.logo }}"
    title="{{ CURRENT_GLOBAL_SHOP.name || CURRENT_GLOBAL_SHOP.shop_name }}"
    show-switch-offline-shop="{{ isMultiStore && CURRENT_GLOBAL_SHOP.multiStoreSwitch }}"
    notice-content="{{ banner.notice }}"
    promotion-str="{{ banner.preference }}"
    shop-style="{{ banner.shopStyle }}"
    show-homepage-url="{{ banner.showHomepageUrl }}"
    show-record-url="{{ banner.showRecordUrl }}"
    bind:search="themeTakeAwayToSearchPageHandler"
    bind:show="themeTakeAwayDialogShowHandler"
    class="takeAway-header"
  />

  <view class="theme-new-takeAway--body">
    <!-- 左侧tag区 -->
    <scroll-view
      class="takeAway-body__sidebar"
      scroll-y
      scroll-into-view="{{ activeTag.alias }}"
    >
      <view
        wx:for="{{ tagList }}"
        wx:for-item="tag"
        id="{{ tag.alias }}"
        wx:key="{{ tag.alias }}"
        class="{{ ['takeAway-body__sidebar--block', tag.active ? 'active' : ''] }}"
        bind:tap="takeAwayThemeSidebarTapHandler"
      >
        <!-- tag对应选择商品数量 -->
        <view 
          wx:if="{{ tag.choosedNum }}"
          class="takeAway-body__sidebar--block-num"
        >{{ tag.choosedNum }}</view>

        <view class="takeAway-body__sidebar--block-title">
         <image wx:if="{{ tag.id === 'discount' }}" src="https://img.yzcdn.cn/image/ump_icon.png" />
         <text>{{ tag.title }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 右侧商品区 -->
    <view class="takeAway-body__goods">
      <!-- 固定sticky商品title栏 -->
      <view wx:if="{{ false }}" class="takeAway-body__goods--title">{{ activeTag.title }}</view>
      <!-- 优惠券 -->
      <view class="takeAway-body__goods-coupon {{ isUpScroll && coupons.length > 0 ? 'hidden-coupon' : '' }}">
        <block wx:for="{{ coupons }}" wx:for-item="coupon" wx:key="{{ coupon.id }}">
          <takeaway-coupon wx:if="{{ coupon.total_qty > 0 && coupon.status === 1 }}" coupon="{{ coupon }}" bind:take="takeAwayThemeTakeCouponHandler" />
        </block>
      </view>
      <!-- 商品列表 -->
      <scroll-view
        class="takeAway-body__goods--scroll"
        scroll-y
        scroll-into-view="{{ isTouchGoodsList ? '' : activeTag.alias }}"
        bind:scroll="takeAwayThemeScrollHandler"
        bind:touchstart="takeAwayThemeTouchStartHandler"
      >
        <!-- 优惠券区域收起时的占位节点 -->
        <view wx:if="{{ coupons.length > 0 }}" class="coupon-skeleton {{ isUpScroll ? '' : 'hidden-skeleton' }}"></view>
        <!-- tags -->
        <view
          wx:for="{{ tagList }}"
          wx:for-item="tag"
          wx:key="{{ tag.alias }}"
          class="takeAway-body__goods--scroll-container"
        >
          <view 
            id="{{ tag.alias }}" 
            class="takeAway-body__goods--scroll-container-title"
          >{{ tag.title }}</view>
          <!-- tag alias 作为key对应的商品map->value -->
          <block wx:if="{{ goodsIdsMap[tag.id].length }}">
            <view
              wx:for="{{ goodsIdsMap[tag.id] }}"
              wx:for-item="id"
              wx:key="{{ id }}"
              class="takeAway-body__goods--scroll-container-wrap"
            > 
              <takeaway-goods
                tag-alias="{{ tag.alias }}"
                tag-id="{{ tag.id }}"
                goods-id="{{ id }}"
                goods-info="{{ goodsInfo[id].goods }}"
                choosed-goods="{{ choosedGoods }}"
                show-sold-num="{{ banner.showSoldNum !== undefined ? banner.showSoldNum : true }}"
                bind:preview="takeAwayThemePreviewGoodsHandler"
                bind:change="takeAwayThemeGoodsChangeHandler"
                bind:overlimit="takeAwayThemeGoodsOverlimitHandler"
                bind:relative-to-viewport="takeAwayThemeGoodsRelativeToViewportHandler"
              />
            </view>
          </block>
          <block wx:else>
            <view class="takeAway-body__goods--scroll-container-tpl">
              <!-- 加载占位模版 -->
              <template wx:if="{{ isFetching }}" is="goods-skeleton" />
              <!-- 无商品占位模版 -->
              <template wx:else is="no-goods" />
            </view>
          </block>
        </view>
        <view class="takeAway-body__goods--scroll-hack"></view>
      </scroll-view>
    </view>
  </view>

  <!-- 商品详情弹窗 -->
  <takeaway-detail
    show="{{ showGoodsPreview }}"
    tag-alias="{{ currentActiveGoods.tagAlias }}"
    tag-id="{{ currentActiveGoods.tagId }}"
    goods-info="{{ currentActiveGoods }}"
    choosed-goods="{{ choosedGoods }}"
    bind:close-preview="takeAwayThemeClosePreviewHandler"
    bind:change="takeAwayThemeGoodsChangeHandler"
    bind:overlimit="takeAwayThemeGoodsOverlimitHandler"
  />

  <!-- 底部购物篮 在多网点不营业的情况下，不展示这个条 -->
  <takeaway-bottom
    wx:if="{{ !isMultiStore || CURRENT_GLOBAL_SHOP.is_opening }}"
    alias="{{ alias }}"
    iphonex-class="bottom-bar-container"
    start-fee="{{ startfee }}"
    takeout-cart-data="{{ cartGoods }}"
    all-choosed-num="{{ allChoosedNum }}"
    all-choosed-price="{{ allChoosedPrice }}"
    class="theme-new-takeAway--bottom"
    bind:toggleCart="takeAwayThemeToggleCartHandler"
    bind:order="takeAwayThemeOrderHanlder"
  />

  <!-- 购物抽屉 -->
  <takeaway-cart
    iphonex-class="bottom-bar-container"
    takeout-cart-data="{{ cartGoods }}"
    show-takeout-cart="{{ showCart }}"
    bind:toggle="takeAwayThemeToggleCartHandler"
    bind:clear="takeAwayThemeClearCartHandler"
    bind:change="takeAwayThemeCartGoodsChangeHandler"
  />

  <!-- 优惠活动弹层 -->
  <takeaway-dialog
    show="{{ showUmpDialog }}"
    notice="{{ banner.notice }}"
    promotion-str="{{ banner.preference }}"
    bind:close="takeAwayThemeDialogCloseHandler"
  />

  <!-- sku弹层 -->
  <base-sku
    show="{{ showTakeAwaySku }}"
    goods="{{ currentActiveGoods }}"
    sku="{{ currentActiveGoods.sku }}"
    buy-text="加入购物车"
    show-add-cart-btn="{{ false }}"
    hide-stock="{{ currentActiveGoods.hideStock === 1 || currentActiveGoods.sku.hideStock === 1 }}"
    reset-stepper-on-hide
    theme-class="{{ themeClass }}"
    bind:buy="takeAwayThemeSkuClickHandler"
    bind:sku-close="takeAwayThemeSkuCloseHandler"
  />
</template>
