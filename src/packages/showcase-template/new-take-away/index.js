import throttle from '@youzan/weapp-utils/lib/throttle';
import get from '@youzan/weapp-utils/lib/get';
import navigation from '@/helpers/navigate';
import { HEADER_HEIGHT } from './constant';
import {
  state, getters, actions, mutations
} from './store';
import { parseMessages } from './helpers';

const app = getApp();
// 商品id
let ids = [];

export default {
  data: state,

  // 节流函数
  _takeAwayThrottle: null,

  onShow() {
    // 设置needMerge防止小程序返回桌面再进来触发onShow
    if (app.globalData.takeaway && app.globalData.takeaway.needMerge) {
      this.MERGE_GOODS_TO_CART({
        goodsList: app.globalData.takeaway.cartGoods
      });
      app.globalData.takeaway.needMerge = false;
    }
  },

  onHide() {
    // 设置到全局，搜索页可查看
    app.globalData.takeaway = {
      ...app.globalData.takeaway,
      cartGoods: this.data.cartGoods
    };
  },

  fetchTakeAwayThemeDataSuccess(components, isRefresh) {
    if (isRefresh) {
      this.INIT_UPDATE_DATE();
    }

    // 去除背景色等配置
    if (components[0] && components[0].type === 'config' && components[0].is_global_setting) {
      components.shift();
    }

    const shop = this.data.CURRENT_GLOBAL_SHOP || {};

    // 设置店铺信息
    this.initShopInfo({
      shop,
      config: components[0]
    });

    // 设置商品分组数据
    this.initTagAlias({ config: components[0] });

    // 设置起送价
    this.initStartFee();

    // 设置分组id
    this.initGoodsId();
  },

  takeAwayThemeScrollHandler(e) {
    if (!this._takeAwayThrottle) {
      this._takeAwayThrottle = throttle(() => {
        // 计算当前滑动到哪个分组
        this._takeAwayCalcActiveTag();
      }, 300);
    }
    this._takeAwayCouponScroll(e);
    this._takeAwayThrottle();
  },

  _takeAwayCouponScroll(e) {
    const { coupons, isTouchGoodsList, isUpScroll } = this.data;
    const { detail } = e;

    if (coupons.length <= 0) return;

    // 上滑操作 && 未收起优惠券区域 && 上滑距离大于20px 收起优惠券区域
    if (detail.deltaY < 0 && !isUpScroll && detail.scrollTop > 20) {
      this.UPDATE_IS_UP_SCROLL(true);
    } else if (detail.deltaY > 0 && isTouchGoodsList && detail.scrollTop <= 30) {
      // 下拉操作 && 非左侧tag点击 && 滚动下拉距离至30px
      // 展开优惠券
      this.UPDATE_IS_UP_SCROLL(false);
    }
  },

  _takeAwayCalcActiveTag() {
    const { tagList, isTouchGoodsList } = this.data;
    if (!isTouchGoodsList) return;
    const query = wx.createSelectorQuery();
    query.selectAll('.takeAway-body__goods--scroll-container').boundingClientRect(rects => {
      let currentIndex = 0;
      rects.forEach((rect, index) => {
        // 当前滚动条在那个分组位置
        if (rect.top - HEADER_HEIGHT <= 0) {
          currentIndex = index;
        }
      });
      this.UPDATE_ACTIVE_TAG(tagList[currentIndex].alias);
    }).exec();
  },

  takeAwayThemeSidebarTapHandler(e) {
    if (this.headerHeight) {
      wx.pageScrollTo({
        scrollTop: this.headerHeight
      });
    } else {
      const query = wx.createSelectorQuery();
      query.select('.takeAway-header').boundingClientRect(rect => {
        this.headerHeight = rect.height;
        wx.pageScrollTo({
          scrollTop: this.headerHeight
        });
      }).exec();
    }

    const currentTagAlias = e.currentTarget.id;
    this.UPDATE_IS_TOUCH_GOODS_LIST(false);
    this.UPDATE_ACTIVE_TAG(currentTagAlias);
    this.UPDATE_IS_UP_SCROLL(true);
  },

  takeAwayThemeTouchStartHandler() {
    this.UPDATE_IS_TOUCH_GOODS_LIST(true);
  },

  takeAwayThemeToggleCartHandler(e) {
    const show = e.detail;
    this.UPDATE_SHOW_CART_STATUS(show);
  },

  takeAwayThemePreviewGoodsHandler(e) {
    const { goods, tagId } = e.detail;
    this.updateCurrentActiveGoods({
      goods,
      tagId,
      showPreview: true
    });
  },

  takeAwayThemeClosePreviewHandler() {
    this.UPDATE_PREVIEW_GOODS(false);
  },

  takeAwayThemeGoodsChangeHandler(e) {
    const {
      goods, tagId, type, value
    } = e.detail;

    this.updateCurrentActiveGoods({ goods, tagId });
    this.updateSkuData({
      alias: goods.alias,
      type,
      num: value
    });
  },

  takeAwayThemeGoodsOverlimitHandler(e) {
    const {
      goods, tagId, type
    } = e.detail;
    const { choosedGoods } = this.data;

    this.updateCurrentActiveGoods({ goods, tagId });

    if (type === 'minus' && choosedGoods[goods.alias] && choosedGoods[goods.alias].noneSku === false) {
      return this.showToast('多规格商品请到购物车中删除');
    }
  },

  takeAwayThemeClearCartHandler() {
    this.CLEAR_CART_GOODS();
  },

  takeAwayThemeCartGoodsChangeHandler(e) {
    const { num, alias, skuId } = e.detail;

    this.UPDATE_GOODS_TO_CART({
      num,
      alias,
      skuId
    });

    this.UPDATE_CHOOSED_GOODS({
      alias,
      num,
      isCart: true
    });
  },

  takeAwayThemeSkuClickHandler(e) {
    const {
      messages,
      selectedSkuValues: skuNameList,
      selectedNum: num,
      selectedSkuComb: { id: skuId, stockNum, price }
    } = e.detail;
    const { messages: config = [] } = get(this.data, 'currentActiveGoods.sku', {});

    this.multiSkuGoodsToCart({
      skuId,
      stockNum,
      price,
      num,
      skuName: skuNameList.join(' '),
      message: parseMessages(messages, config),
    });
  },

  themeTakeAwayDialogShowHandler() {
    this.UPDATE_SHOW_UMP_DIALOG(true);
  },

  takeAwayThemeDialogCloseHandler() {
    this.UPDATE_SHOW_UMP_DIALOG(false);
  },

  takeAwayThemeTakeCouponHandler({ detail }) {
    this.takeCoupon(detail);
  },

  themeTakeAwayToSearchPageHandler() {
    navigation.navigate({
      url: `/packages/takeaway-search/index?startfee=${this.data.startfee}`
    });
  },

  takeAwayThemeOrderHanlder() {
    this.CLEAR_CART_GOODS();
  },

  takeAwayThemeSkuCloseHandler() {
    this.UPDATE_SHOW_SKU_STATUS(false);
  },

  takeAwayThemeGoodsRelativeToViewportHandler({ detail }) {
    const { id } = detail;
    ids.push(id);
    if (ids.length >= 5) {
      this.getGoodsInfo(ids);
      ids = [];
    } else {
      setTimeout(() => {
        if (ids.length > 0) {
          this.getGoodsInfo(ids);
          ids = [];
        }
      }, 500);
    }
  },

  ...actions,

  ...mutations,

  ...getters
};
