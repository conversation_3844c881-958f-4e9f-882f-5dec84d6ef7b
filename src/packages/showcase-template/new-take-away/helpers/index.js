import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { formatSkuTree } from 'shared/common/components/base-sku/common/sku-format-tree';

export function parseGoods(item) {
  const { marketing, spu, processProcedure } = item;
  let { goods } = item;
  if (!goods.imageUrl) {
    goods.imageUrl = '/upload_files/no_pic.png';
  }
  // 商品列表图片
  // eslint-disable-next-line
  goods.picture = goods.picUrl = cdnImage(goods.imageUrl, '!300x300.jpg');
  // 商品预览大图
  goods.bigPicUrl = cdnImage(goods.imageUrl, '!750x0.jpg');

  // 如果有活动spu就是用活spu，没有就使用商品级spu
  if (marketing && marketing.activities[0] && marketing.activities[0].spu) {
    goods.sku = formatSkuTree(marketing.activities[0].spu);
    // 限时折扣或者会员折扣
    goods.activity = marketing.activities[0];
  } else {
    goods.sku = formatSkuTree(spu);
  }
  goods.price = goods.sku.minPrice;

  // 只取需要用到数据，防止触发小程序渲染数据的限制
  goods = {
    alias: goods.alias,
    id: goods.id,
    title: goods.title,
    picture: goods.picture,
    sellPoint: goods.sellPoint,
    totalStock: goods.totalStock,
    picUrl: goods.picUrl,
    sku: goods.sku,
    activity: goods.activity,
    price: goods.price,
    bigPicUrl: goods.bigPicUrl,
    processProcedure,
    hideStock: goods.hideStock,
  };

  return { goods };
}

export function parseMessages(messages, config) {
  const parsed = {};
  Object.keys(messages).forEach((_e, i) => {
    const { name } = config[i];
    const value = messages[`message_${i}`];
    if (name) {
      parsed[name] = value;
    }
  });
  return parsed;
}
