@keyframes blink {
  0% {
    opacity: .4;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: .4;
  }
}

.skeleton {
  position: relative;
  display: flex;
  width: 100%;
  animation: blink 2s infinite;

  image,
  text,
  &-opt {
    display: block;
    background-color: #eee !important;
    color: transparent !important;
  }

  image {
    width: 75px;
    height: 75px;
    margin-right: 10px;
  }

  &-view {
    width: calc(100% - 85px);
  
    &__title {
      width: 100%;
      height: 28px;;
    }

    &__price {
      width: 60%;
      height: 28px;
      margin-top: 19px;
    }
  }

  &-opt {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 28px;
    height: 28px;
    border-radius: 50%;
  }
}
