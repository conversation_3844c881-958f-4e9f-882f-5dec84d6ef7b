import Toast from '@vant/weapp/dist/toast/toast';
import get from '@youzan/weapp-utils/lib/get';
import adaptor from '@youzan/feature-adaptor/es/take-away/index';
import {
  FEATURE_TEMPLATE,
  SHARE_PAGE_TYPES,
  HIDE_FLOATING_NAV_TEMPLATE,
} from './constants';

const { wap2weapp } = adaptor;
const app = getApp();

export function setTabBar(hideTabBar) {
  this.__hideTabBar = hideTabBar;
  try {
    if (hideTabBar) {
      // 隐藏导航栏
      this.setYZData({
        youzanAppHideTabbar: true,
      });
    } else {
      this.setYZData({
        youzanAppHideTabbar: false,
      });
    }
  } catch (err) {
    console.warn(err);
  }
}

export function isTemplateInPackage(response) {
  const config = get(response, 'data.0', {});
  const { templateId } = response;
  // 外卖模板，黎贝卡模板在分包中
  return (
    isTakeAwayTemplate(config, templateId) ||
    templateId === FEATURE_TEMPLATE.REBICA_MAGAZINE
  );
}

export function isCommunitybuy(templateId) {
  if (templateId === FEATURE_TEMPLATE.COMMUNITYBUY) {
    wx.redirectTo({
      url: '/packages/groupbuying/buyer-trade/buying/index',
    });
    return true;
  }
  return false;
}

// 同城零售 - 24小时货架
export function isRetailShelf(templateId, alias) {
  if (templateId === FEATURE_TEMPLATE.RETAIL_SHELF) {
    wx.redirectTo({
      url: `/packages/retail/shelf-home/index${alias ? `?alias=${alias}` : ''}`,
    });
    return true;
  }
  return false;
}

export function isTakeAwayTemplate(config, templateId) {
  if (!config || typeof config !== 'object') {
    Toast('页面配置不存在，请重新关联该页面');
    return false;
  }
  return (
    (config.type &&
      config.type === 'config' &&
      config.template &&
      config.template === 'take_away') ||
    +templateId === FEATURE_TEMPLATE.NEW_TAKE_AWAY
  );
}

export function isShowFloatingNav(templateId) {
  return HIDE_FLOATING_NAV_TEMPLATE.indexOf(templateId) === -1;
}

export function checkNeedUpdateShowcase(response) {
  const originDataStr = this.__showcaseOriginDataStr || '';
  try {
    const responseStr = JSON.stringify({
      title: response.title,
      templateId: response.templateId,
      components: response.data,
      offlineId: app.getOfflineId(),
    });
    if (originDataStr !== responseStr) {
      return true;
    }
  } catch (e) {
    return true;
  }

  return false;
}

export function saveShowcaseOriginData(response) {
  try {
    this.__showcaseOriginDataStr = JSON.stringify({
      title: response.title,
      templateId: response.templateId,
      components: response.data,
      offlineId: app.getOfflineId(),
    });
  } catch (e) {
    console.log(e);
  }
}

/**
 * 根据配置生成微页面标题
 * @param {string} featureTitle 微页面原始标题
 * @param {*} options 额外参数，可以支持去除额外的后缀
 */
export function resolveFeatureTitle(featureTitle, options = {}) {
  const {
    // 去除后缀
    withoutSuffix = false,
  } = options;

  const shopConfig = app.getShopConfigDataSync();
  const {
    feature_title: pageTitleOptions,
    page_title_suffix: pageTitleSuffix,
    is_page_title_suffixed: isPageTitleSuffixDisplayed,
  } = shopConfig;
  const shopName = app.getShopInfoSync().base.shop_name || '';

  let title = featureTitle;
  if (+pageTitleOptions === 1 && shopName) {
    title = shopName;
  }

  // 方法参数中传入无需后缀，强制跳过
  if (!withoutSuffix && +isPageTitleSuffixDisplayed === 1 && pageTitleSuffix) {
    title += ` - ${pageTitleSuffix}`;
  }
  return title;
}

// 处理微页面相关数据，初始化微页面
export function resolveFeatureData(options = {}) {
  const { cb = () => {}, resolve, isRefresh } = options;
  let { response, needRefresh = false } = options;
  const showFloatingNav = isShowFloatingNav(response.templateId);
  // 多网点切换，上一个模板是否和这次的模板是同一类
  const templateInPackage = isTemplateInPackage(response);
  const backToSamePage = templateInPackage === this.previousTemplateInPackage;
  this.previousTemplateInPackage = templateInPackage;

  let pageType;
  // let templateId;

  wx.hideLoading();

  if (this.data.isHomePage) {
    pageType = SHARE_PAGE_TYPES.HOME;
  } else {
    pageType = SHARE_PAGE_TYPES.MICRO;
  }

  // 首页微页面数据和普通微页面数据返回数据格式不同，获取pageId的方式也不同，
  // 首页数据要取 featureId
  // 普通微页面取 id
  // 这里优先取 featureId，如果没有就取 id， 保证id能对上
  const pageId = response.featureId || response.id;

  const { templateId } = response;

  // 做微页面是否要刷新检查，防止同样的数据多次渲染
  // 检查这次数据返回以后，是否需要更新微页面
  if (!checkNeedUpdateShowcase.call(this, response)) {
    this.trigger('feature:notRefresh');

    // 获取分享设置
    if (!this.__isNewFeatureData) {
      this.__isNewFeatureData = true;
      this.fetchShareSetting(pageType, pageId);
    }

    // 页面内容、templateId一样，如果是切换多网点则回退一页就行
    cb && cb(true);
    resolve();
    return;
  }
  saveShowcaseOriginData.call(this, response);

  this.__isNewFeatureData = true;

  if (response.fromCache || needRefresh) {
    // 如果从缓存来的，就开始下拉刷新，请求新的数据
    setTimeout(() => {
      // 如果初始化下拉,就设置不走进店
      this.__needEnterShop = false;

      wx.startPullDownRefresh();
    }, 500);

    // 如果下拉，则把获取分享设置的标志设为 false
    this.__isNewFeatureData = false;
  }

  if (this.__isNewFeatureData) {
    this.fetchShareSetting(pageType, pageId);

    // 两次数据不一致
    // 在刷新之后做社区团购的跳转，保证微页面数据得到更新
    // 是否是社区团购首页
    // 2020.11.19 社区团购微页面在小程序下走内嵌h5
    // const communitybuyFlag = isCommunitybuy(templateId);

    // 是否为同城零售24小时货架
    // 2020.12.23 24小时货架微页面不走单独页面
    // const retailShelfFlag = isRetailShelf(templateId, response.featureAlias || response.alias);

    // if (retailShelfFlag) {
    //   resolve();
    //   return;
    // }
  }

  app.globalData.featurePageConfig = {
    alias: response.alias || response.homepageAlias || response.idAlias,
    title: response.title,
    featureAlias: response.featureAlias,
  };
  this.__featureTitle = get(response || {}, 'data.0.title', '');
  this.updatePageTitle();

  // 兼容 h5 外卖模板数据
  if (isTakeAwayTemplate.call(this, response.data[0], templateId)) {
    response = wap2weapp(response);
    setTabBar.call(this, true);
  } else if (templateId === FEATURE_TEMPLATE.TOP_NAV) {
    // 顶部导航模板
    response.type = 4;
    setTabBar.call(this, false);
  } else if (templateId === FEATURE_TEMPLATE.REBICA_MAGAZINE) {
    // 黎贝卡定制模板
    response.type = 5;
    setTabBar.call(this, true);
  } else if (
    templateId === FEATURE_TEMPLATE.CATEGORY ||
    templateId === FEATURE_TEMPLATE.DOUBLE_11
  ) {
    // 分类模板和双十一模板
    response.type = 6;
    setTabBar.call(this, true);
  } else if (this.data.alias || response.type === undefined) {
    // 新数据只支持 type === 3 的 feature
    response.type = 3;
    setTabBar.call(this, false);
  }
  const templatePageNeedRelaunch = backToSamePage;
  const otherParams = {
    // 微页面组件内使用的 kdtId 应始终跟着组件数据来，
    // 即组件数据是哪个店铺的，对应的 kdtId 也要是该店铺的，防止出现不一致的情况
    kdtId: response.kdtId,
  };
  this.showShowcaseComponents(
    response.data,
    response.type,
    isRefresh,
    response.fromCache,
    templatePageNeedRelaunch,
    undefined,
    otherParams
  );

  this.setYZData(
    {
      fetching: false,
      featureId: pageId,
      templateId,
      showFloatingNav,
      'theme.extra.featureId': pageId,
      'theme.extra.templateId': templateId,
      'theme.extra.isHomePage': this.data.isHomePage,
      isRetailShelfTemplate: templateId === FEATURE_TEMPLATE.RETAIL_SHELF,
    },
    () => {
      // 异步获取数据成功后发信号 埋点
      this.trigger('feature:loaded', {
        pageId: this.data.featureId,
        title: response.title,
        logParams: {
          template_id: templateId,
          is_home: Number(this.data.isHomePage),
        },
        templateInPackage,
      });
    }
  );

  cb && cb(backToSamePage);
  resolve();
}
