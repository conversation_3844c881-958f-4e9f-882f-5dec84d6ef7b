// import { Hummer } from '@youzan/hummer-mp';
import each from '@youzan/weapp-utils/lib/each';
import event from 'shared/utils/app-event';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import ExtendCreator from 'utils/extend-creator';
import { SDKVersionLessThen } from 'utils/compare-version';
import Theme from 'components/showcase/index';
import Toast from '@vant/weapp/dist/toast/toast';
import getSystemInfo from 'shared/utils/browser/system-info';
import adaptorComponents from 'constants/adaptor-components';
import Event from '@youzan/weapp-utils/lib/event';
import navigate from '@/helpers/navigate';
import { checkReLaunchShopSelect } from '@/base-api/shop/chain-store';
import {
  autoEnterShop,
  checkShopStatausAfterES,
  defaultEnterShopOptions,
} from 'common-api/multi-shop/multi-shop-redirect';
import store from '@/base-api/store';
import Args from '@youzan/weapp-utils/lib/args';

import { resolveFeatureTitle, resolveFeatureData } from './helper';
import requestOldWeappPage from './old-feature';
import { initSalesman } from 'shared/utils/salesman-share';

const SHARE_TIME_LINE_SCENE = 1154;

const app = getApp();
const { state } = store;
const extend = ExtendCreator();
const systemInfo = getSystemInfo();
const showWxUpgradeTip =
  SDKVersionLessThen('2.1.3') && app.storage.get('showWxUpgradeTip') !== true;
let offlineChangeCb;
// 微页面是否曾经获取过数据
// __featureFirstLoad 有问题，语义有点怪
let __featureFetched = false;

const extendPageConfig = {};

const data = {
  data: {
    id: 0,
    alias: '',
    themeClass: app.themeClass,
    fetching: true,
    type: 'default',
    // 微页面: 零售24小时货架模板
    isRetailShelfTemplate: false,
    banner: {
      logo: '',
      title: '',
    },
    tags: {
      list: [],
      selectedId: 0,
      scroll: false,
      choosedNum: [],
    },
    pageBgColor: '',
    goods: {},
    systemInfo: {},
    scrollIntoView: '',
    scrollTop: 0,
    success: true,
    showSelfFetchPopup: true,
    visitGift: {},
    youzanAppHideTabbar: false,
    buyerAddress: '',
    asyncShowcaseComponents: [],
    showFeatureVisitGift: false,
    showWxUpgradeTip,
    showLotteryCode: false,
    share: {
      title: '',
      imageUrl: '',
    },
    salesman: {},
    pageTitle: '',
    shareTag: '',
    navigationbarConfigData: {},
    homepageGray: false,
  },
};

const lifeCycle = {
  __featureTitle: '',

  __featureFirstLoad: true,

  __hideTabBar: false,

  __isNewFeatureData: true,

  __needUpdateHomepage: false,

  __needEnterShop: true, // 标记是否需要进店

  onLoad(options) {
    const { scene } = wx.getLaunchOptionsSync();
    if (scene === SHARE_TIME_LINE_SCENE) {
      this.isFromTimeline = true;
      this.setYZData({
        isFromTimeline: true,
        alias: options.alias,
        kdtId: options.kdt_id,
      });
      return;
    }

    checkReLaunchShopSelect();
    initSalesman.call(this, { sst: 1, scene: 'feature_page' });
    // 记录下来，在分享的时候需要
    this.options = options;
    this.__fetchPromise = new Promise(() => {});

    const pagePath = this.route;

    // 乱七八糟的判断是否是店铺主页什么的，alias 代表是新编辑器数据
    if (
      pagePath == 'packages/home/<USER>/one' ||
      pagePath == 'packages/home/<USER>/two' ||
      pagePath == 'packages/home/<USER>/three' ||
      pagePath == 'pages/home/<USER>/one' ||
      pagePath == 'pages/home/<USER>/two' ||
      pagePath == 'pages/home/<USER>/three'
    ) {
      // 更新导航数据
      // 根据当前页面路径和 ext.json 中导航数据，确定加载的微页面内容
      const { list: navList = [] } = app.getNavConfig();
      if (navList.length) {
        this.processNavFeature(navList);
      } else {
        app.getNavConfigSync().then((res) => {
          const { list = [] } = res || {};
          // 在没有任何匹配的情况下，默认加载首页
          this.processNavFeature(list);
        });
      }
    } else if (options.id || options.alias) {
      this.setYZData({
        isHomePage: false,
        id: options.id || 0,
        alias: options.alias || '',
        title: options.title || '',
        showLotteryCode: !!options.codeAlias,
      });
    } else {
      this.setYZData({
        isHomePage: true,
      });
    }

    this.setYZData({
      systemInfo,
    });

    // 首屏打点
    Event.off('feature:first_screen_cover');
    Event.once('feature:first_screen_cover', () => {
      // Hummer?.markRendered('fs');
    });

    // 修复点击首页relaunch后，切换网点不是最新页面实例导致页面不刷新
    offlineChangeCb = this.offlineChangeCallBack;
    // 多网点重新选择网点后要更新页面数据
    this.off('app:offlineId:change', offlineChangeCb);
    this.on('app:offlineId:change', offlineChangeCb);
    // 处理 “连锁店铺A -> 个人中心 -> 订单列表 -> 店铺B商品/课程” 这种场景下再回到首页还是店铺A首页的问题
    if (this.data.isHomePage) {
      this.off('app:chainstore:kdtid:update', this.handleKdtIdChange);
      this.on('app:chainstore:kdtid:update', this.handleKdtIdChange, this);
    }

    this.on('component:sku:cart', (res) => {
      if (res.type == 'add') {
        Toast('添加购物车成功');
      }
    });
    if (options.shareTag) {
      this.setYZData({
        shareTag: options.shareTag,
      });
    }
  },

  // 处理导航页的微页面初始数据
  processNavFeature(list) {
    let updateTabData = { isHomePage: true };
    list.some(({ page_path: pagePath, id, alias }, index) => {
      if (
        this.route === pagePath ||
        this.route === pagePath.replace(/^pages(-|\/)/, 'packages/')
      ) {
        this.tabbarIndex = index;
        // id 为老的微页面标志
        // alias 为新的微页面标志
        if (id > 0 || alias) {
          updateTabData = {
            // 确定有导航信息以后，不加载首页内容，加载对应微页面内容
            isHomePage: false,
            isTabPage: true,
            id,
            alias,
          };
        }
        return true;
      }
      return false;
    });

    this.setYZData(updateTabData);
  },

  getHomePage() {
    // 这里的逻辑主要是针对，用户之前打开了线下小程序，如果微信后台被杀了，
    // 就可能会自动进入网店小程序。这个时候根据 kdtid 的逻辑，可能用的还是之前的门店 kdtid，就会导致微页面的配置拉取失败。主要针对大网店和多网店的场景
    if (app.globalData.isRetailApp) {
      return app.getShopInfo().then(({ chainStoreInfo }) => {
        if (!chainStoreInfo.isRootShop) {
          // 不是多网店
          if (!chainStoreInfo.isMultiOnlineShop) {
            app.updateKdtId(chainStoreInfo.rootKdtId, false, {
              mark: '912',
            });
            // 没有网店能力的多网店
          } else if (!chainStoreInfo.isOnline) {
            return wx.reLaunch({
              url: '/packages/shop-select/chain-store/shopselect/index?dbKey=location',
            });
          }
        }
        return this.fetchHomepage();
      });
    }
    return this.fetchHomepage();
  },

  onShow() {
    if (this.isFromTimeline) {
      return;
    }
    if (!app.getAppId()) {
      wx.showModal({
        title: '提示',
        content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试',
      });
    }
    if (this.__featureFirstLoad) {
      this.__fetchPromise = this.getHomePage().then(() => {
        this.updatePageTitle();
      });
    } else {
      // 这里需要再做一次，因为多网点切换时候，会在网点页面直接调用刷新店铺首页
      // 但是他的更新标题是在多网点切换页面生效的，页面切回来以后会自动失效
      // 所以需要在当前页面再做一次
      this.updatePageTitle();
    }

    this.setYZData({
      buyerAddress: app.getUserInfoSync('poi.title') || '',
    });

    if (this.__hideTabBar) {
      // 隐藏导航栏
      this.setYZData({
        youzanAppHideTabbar: true,
      });
    }

    if (this.__needUpdateHomepage) {
      this.__needUpdateHomepage = false;
      this.updateHomepage();
    }
  },

  onReady() {
    this.__featureFirstLoad = false;
    // 对界面的设置如wx.setNavigationBarTitle请在onReady之后设置
    // https://developers.weixin.qq.com/miniprogram/dev/framework/app-service/page.html
    // 店铺主页和微杂志页标题
    this.updatePageTitle();
    setTimeout(() => {
      this.setYZData({
        showFeatureVisitGift: true,
      });
    }, 2000);
  },

  onUnload() {
    this.off('app:chainstore:kdtid:update', this.handleKdtIdChange);
  },

  onPullDownRefresh(cb) {
    this.__fetchPromise = this.fetchHomepage(
      true,
      typeof cb === 'function' ? cb : ''
    );
    this.trigger('home:refresh');
  },

  handleSalesmanSetShare(e) {
    const { shareData } = e.detail;
    this.setYZData({
      salesman: shareData,
    });
  },

  onMessage(event) {
    const messages = event.detail.data;
    const shareMessages = messages.filter(
      (message) => message.type === 'ZNB.share'
    );
    if (shareMessages.length) {
      this.webviewShareConfig = shareMessages.pop().config;
    }
  },

  onShareAppMessage(e) {
    console.log('e ius ', e, this.data.share, this.webviewShareConfig);
    if (this.webviewShareConfig) {
      return this.webviewShareConfig;
    }

    const { from, target = {} } = e || {};
    const { dataset = {} } = target;
    const { punch, shareTag } = dataset;
    // 教育培训-群打卡-邀请好友
    if (from === 'button' && punch) {
      return {
        title: `邀请你参与${e.target.dataset.title}`,
        path: `/packages/new-punch/introduction/index?alias=${e.target.dataset.alias}`,
        imageUrl: 'https://img01.yzcdn.cn/punch/image/<EMAIL>',
      };
    }

    const { title, imageUrl } = this.data.share;
    const pagePath = this.route;
    const requestData = [];

    const requestOptions = {
      ...(this.options || {}),
      ...defaultEnterShopOptions,
    };

    if (this.data.isTabPage && this.data.alias) {
      requestOptions.alias = this.data.alias;
    }
    // 拼接请求参数
    each(requestOptions, (value, key) => {
      requestData.push(`${key}=${value}`);
    });

    const shareConfig = {
      title:
        title ||
        resolveFeatureTitle(this.__featureTitle, {
          withoutSuffix: true,
        }),
      imageUrl,
    };

    if (from === 'button' && shareTag) {
      requestData.push(`shareTag=${shareTag}`);
    }

    shareConfig.path = `${pagePath}?${requestData.join('&')}`;
    console.log('shareConfig is ', shareConfig);
    return shareConfig;
  },

  onShareTimeline() {
    const shareData = [];
    const originOption = this.options || {};
    const requestOptions = {
      kdt_id: app.getKdtId(),
      alias: this.data.alias,
      ...defaultEnterShopOptions,
      ...originOption,
    };
    // 拼接请求参数
    each(requestOptions, (value, key) => {
      shareData.push(`${key}=${value}`);
    });

    return {
      query: `${shareData.join('&')}`,
    };
  },

  handleKdtIdChange() {
    if (this.__pageKdtId === app.getKdtId()) {
      return;
    }
    const pages = getCurrentPages() || [];
    const { route } = pages[pages.length - 1] || {};

    // 如果 kdtId 变化后，页面还是当前显示的页面，则刷新首页数据,
    // 否则设置标志，等后续 onShow 再刷新数据
    if (this.route === route) {
      this.updateHomepage();
    } else {
      this.__needUpdateHomepage = true;
    }
  },

  updateHomepage() {
    this.__fetchPromise = this.fetchHomepage(true);
  },

  updateNavigationConfig() {
    let data = this.navigationbarConfigData;
    try {
      const jsonData = JSON.parse(this.__showcaseOriginDataStr);
      data = (jsonData.components || []).find(
        (i) => i.type === 'navigationbar_config'
      );
      if (data) {
        this.setYZData({
          navigationbarConfigData: data,
        });
      }
    } catch (error) {}
  },

  updatePageTitle() {
    Promise.all([app.getShopConfigData(), this.__fetchPromise])
      .then(() => {
        const title = resolveFeatureTitle(this.__featureTitle);
        this.updateNavigationConfig();
        this.setYZData({
          pageTitle: title,
        });
        wx.setNavigationBarTitle({
          title,
        });
      })
      .catch((err) => console.error(err));
  },

  closeVisitGift() {
    this.setYZData({
      'visitGift.show': false,
    });
  },
};

const featureRequest = {
  getAliasPromise({ alias, targetKdtId }) {
    return app
      .request({
        path: '/wscshop/api/showcase-retail/getRetailSubShopFeatureInfo.json',
        data: {
          source_alias: alias,
          target_kdt_id: targetKdtId,
        },
      })
      .then((data) => {
        return data.alias;
      })
      .catch(() => {
        return alias;
      });
  },
  // 进店函数
  autoEnterShopFunc(options) {
    if (this.__needEnterShop) {
      return autoEnterShop(options);
    }
    // 这个标识只针对初始化下拉
    this.__needEnterShop = true;
    return Promise.resolve();
  },
  requestFeature({ resolve, reject, cb, isRefresh }) {
    if (this.__isRequesting) {
      return;
    }
    this.__isRequesting = true;
    let { alias } = this.data;
    if (/(packages|pages)\/home\/<USER>\//.test(this.route) && !alias) {
      alias = this.options.alias;
    }
    let featureQueryOption = {};
    const baseParams = {
      show_ad: true,
      check_multistore: true,
      async_components: 'goods,ump_limitdiscount',
      adaptor_components: adaptorComponents.join(','),
      weapp_route: this.route,
      // 跨店券支持
      support_platform_coupon: 1,
      ...(this.options.fromTee
        ? {}
        : {
            check_tee_stage: 0,
            check_ext_stage: 16,
            check_ext_shelf: 1,
            weapp_route: this.route,
          }),
      close_chainstore_webview_limit: true,
    };
    const { isHomePage } = this.data;
    // 首页 和 微页面采用node接口
    if (alias || isHomePage || !this.data.id) {
      const redirectUrl = Args.add('/' + this.route, this.options);
      this.autoEnterShopFunc({ ...this.options, redirectUrl }).then(
        (subKdtId) => {
          checkShopStatausAfterES();
          if (subKdtId) {
            baseParams.kdt_id = app.getKdtId();
            // 进店4期，支持微页面单独进店
            baseParams.support_feature_enter_shop = 1;
          }
          // 首页
          if (alias) {
            featureQueryOption = {
              path: '/wscshop/weapp/feature_detail.json',
              data: {
                ...baseParams,
                alias,
                check_chainstore: true,
              },
            };
          } else {
            featureQueryOption = {
              path: '/wscshop/weapp/homepage_detail.json',
              data: {
                ...baseParams,
                platform: 2,
              },
            };
          }
          // 如果是下拉刷新，就直接使用服务端数据
          const forceUseServerData = isRefresh;
          // 在初始加载时，先不等待 init.json, 把数据拉回来
          const isUseShopInfo = app.globalData.fetchedShop || isRefresh;

          const needRefresh = false;
          let skipToken = false;

          // 下拉刷新强制跳过这个
          if (!isRefresh) {
            skipToken = true;
          }

          const startKdtId = app.getKdtId();
          app
            .request({
              ...featureQueryOption,
              config: {
                // 店铺主页和老的微页面还是需要 kdtid 来获取主页
                skipToken,
                cache: true,
                priority: 'high',
                forceRefreshCache: forceUseServerData,
                skipShopInfo: !isUseShopInfo,
              },
            })
            .then((res) => {
              const {
                alias = '',
                title = '',
                isLock = 0,
                featureAlias,
                videoNumberId,
                isWebviewFeature = false,
                kdtId: enterShopKdtId = 0,
                shopConfig = {},
              } = res;

              // 页面锁定，跳转锁定页面
              if (isLock === 1) {
                return wx.redirectTo({ url: '/packages/common/lock/index' });
              }

              this.setYZData({
                homepageGray: !!shopConfig?.openHomeGray,
              });

              app.globalData.featurePageConfig = {
                alias,
                title,
                featureAlias,
              };

              const response = res;
              // 从接口返回的新数据，不是从缓存返回的数据
              // 字段转换
              if (!response.data) {
                response.data = response.components;
                delete response.components;
                if (
                  (response.data && response.templateId === 91) ||
                  response.templateId === 17
                ) {
                  const subEntry =
                    response.data[1] && response.data[1].sub_entry;
                  if (response.data[1].type === 'tpl_new_take_away') {
                    subEntry[0].show_sold_num = response.data[1].showSoldNum;
                  }
                  response.data[1] = subEntry;
                }
              }

              if (isRefresh) {
                wx.stopPullDownRefresh();
              }

              if (videoNumberId) {
                this.getVideoNumberInfo(videoNumberId);
              }

              // 是三方模板 且在白名单内
              if (isWebviewFeature) {
                const queryOptions = this.options || {};
                // 首页或者路径符合 packages/home/<USER>
                const isTabPage = isHomePage || this.data.isTabPage;
                // 对于主页优先取 featureAlias，如果没有（多网点自定义首页的情况）则取 alias
                const finalAlias = isHomePage ? featureAlias || alias : alias;

                const baseUrl = `/wscshop/feature/${finalAlias}`;

                const { isChainStore } =
                  app.getShopInfoSync().chainStoreInfo || {};

                // 连锁店铺微页面扩展参数
                const chainStoreExtParams = isChainStore
                  ? {
                      // webview中kdtId是根据alias获取的，所以需要透传当前网店的alias
                      alias: finalAlias,
                    }
                  : {};

                const params = {
                  ...queryOptions,
                  ...chainStoreExtParams,
                  kdt_id: startKdtId,
                  ...(app.globalData.isRetailApp ? { is_retail_app: 1 } : {}),
                  ...(isTabPage
                    ? {
                        has_weapp_tabbar: 1,
                        weapp_tabbar_index: this.tabbarIndex || 0,
                      }
                    : {}),
                  ...(isHomePage ? { homepageWebview: 1 } : {}),
                  oid: state.shop.offlineId,
                };
                const webviewFeatureUrl = Args.add(baseUrl, params);

                this.setYZData({
                  isWebviewFeature,
                  webviewFeatureUrl,
                });
                wx.hideLoading();
              }

              resolveFeatureData.call(this, {
                response,
                cb,
                resolve,
                isRefresh,
                needRefresh,
              });
              this.__pageKdtId = enterShopKdtId;
              this.__isRequesting = false;
              // 单独进店后更新global的kdtid
              enterShopKdtId &&
                app.updateKdtId(enterShopKdtId, false, {
                  mark: 913,
                });
              this.updatePageTitle();

              // 获取过数据，直接重置为 true
              __featureFetched = true;
              Event.trigger('FeatureAliasLoaded', alias);
            })
            .catch((res) => {
              wx.hideLoading();
              if (res.code === 'multistore need user info') {
                wx.startPullDownRefresh();
                return;
              }

              if (res.code === 'chainstore need select shop') {
                // 如果 kdtId 已经变得不一样，说明已经完成进店操作了
                if (+startKdtId !== +app.getKdtId()) {
                  wx.startPullDownRefresh();
                  return;
                }
                app.once('app:chainstore:kdtid:update', () => {
                  wx.startPullDownRefresh();
                });
                return;
              }

              // 查询连锁 alias 映射关系失败，跳转回首页
              // 2020/04/02：回退首页 改为 跳转到空状态页。后续可能需要换个 code，现在的 code 有点迷惑
              if (
                res.code === 'chainstore need enter homepage' ||
                res.code === 'chainstore check relation fail'
              ) {
                app.toExistPage();
                // openWebView('/wscshop/common/error/not-exist', {
                //   title: '页面创建中/不存在',
                //   method: 'redirectTo',
                //   query: {
                //     kdt_id: app.getKdtId(),
                //     type: 'feature',
                //     redirect_url: getCurrentRoute({
                //       withSlash: true,
                //       withQuery: true,
                //     }),
                //   },
                // });
                return;
              }

              // 处理重定向逻辑
              if (res && res.code === 302) {
                this.__isRequesting = false;
                __featureFetched = true;
                const {
                  res: { data },
                } = res;
                let { location = '' } = data || {};
                if (location && /^(\/?)pages|packages/.test(location)) {
                  if (location.slice(0, 1) !== '/') {
                    location = '/' + location;
                  }
                  location = Args.add(location, this.options);
                  return wx.redirectTo({ url: location });
                }
              }

              // 获取数据失败，也重置为 true
              __featureFetched = true;

              app.logger.appError({
                name: '微页面请求失败',
                message: '微页面请求失败',
                alert: 'warn',
                detail: {
                  global: app.globalData,
                  data: this.data,
                  res,
                },
              });

              if (isRefresh) {
                wx.stopPullDownRefresh();
              }

              if (res.code === 60501) {
                Toast('小程序和有赞店铺信息不匹配');
                app.storage.remove('app:token');
              } else {
                wx.showModal({
                  title: '获取信息失败',
                  content: '是否重新加载页面',
                  success: (res) => {
                    if (res.confirm) {
                      wx.reLaunch({
                        url: `/${this.route}`,
                      });
                    }
                  },
                });
              }
              reject();
            });
        }
      );
    } else {
      // 旧微页面
      requestOldWeappPage.call(
        this,
        this.data.id,
        { resolve, reject },
        { cb, isRefresh }
      );
      __featureFetched = true;
    }
  },

  // 获取频号直播信息
  getVideoNumberInfo(finderUserName) {
    wx.getChannelsLiveInfo &&
      wx.getChannelsLiveInfo({
        finderUserName,
        success: (res) => {
          wx.setStorageSync('channelsLiveInfo', JSON.stringify(res));
        },
        fail: (error) => {
          console.log('获取视频号直播信息失败', error);
        },
      });
  },

  fetchHomepage(isRefresh, cb = () => {}) {
    return new Promise((resolve, reject) => {
      // 没有appId就不运行了
      if (!app.getAppId()) {
        return reject();
      }

      if (!isRefresh) {
        wx.showLoading({
          title: '加载中',
        });
      }
      if (!this.___beforeFetchHomepageTiggered && this.triggerAsync) {
        this.triggerAsync('beforeFetchHomepage', {
          alias: this.data.alias,
        }).then((res = []) => {
          this.___beforeFetchHomepageTiggered = true;
          // 同步事件返回stop: true, 说明外部监听到时间并暂停获取主页流程
          if (res && res[0] && res[0].stop) {
            Event.on('home:fetchedSpecAlias', (e) => {
              // 获取到alias, 重新走一下获取微页面的逻辑
              const { alias } = e || {};
              this.setYZData({ alias });
              // this.__fetchedSpecAlias = true;
              this.updateHomepage();
            });
            reject();
          } else {
            // 继续获取alias
            this.requestFeature({ resolve, reject, cb, isRefresh });
          }
        });
      } else {
        this.requestFeature({ resolve, reject, cb, isRefresh });
      }
    });
  },

  // 获取分享设置
  fetchShareSetting(pageType, pageId) {
    app
      .request({
        path: '/wscshop/showcase/share/setting.json',
        data: {
          kdtId: app.getKdtId(),
          pageType,
          pageId,
        },
      })
      .then((res = {}) => {
        const { shareTitle = '', bannerImageUrl = '' } = res;
        // 安卓分享不能显示 gif，所以转成 jpg
        const imageUrl = /android/i.test(systemInfo.system)
          ? cdnImage(bannerImageUrl, '!730x0.jpg').replace(/gif$/, 'jpg')
          : bannerImageUrl;

        if (shareTitle) {
          this.setYZData({
            share: {
              title: shareTitle,
              imageUrl,
            },
          });
        }
      })
      .catch((err) => {
        console.log(err || '获取分享设置失败');
      });
  },

  offlineChangeCallBack(cb, options) {
    // 如果还没有获取到第一次微页面数据，并且有多网点数据改变事件触发，就直接忽略
    if (!__featureFetched) {
      return;
    }

    // 不执行当前页面绑定cb，针对多次切换微页面的场景需要options带上回退页面的wxExparserNodeId
    if (
      options &&
      options.wxExparserNodeId &&
      this.__wxExparserNodeId__ !== options.wxExparserNodeId
    ) {
      return;
    }

    // https://jira.qima-inc.com/browse/ONLINE-215573
    // 绿姿大客定制，现在的微页面会监听多网点变化，并且满足某个历史页面是首页/微页面的情况，跳回对应页面或者首页
    // src/packages/shop/multi-store/common/index.js，在多网点里面逻辑写了，如果直接回跳会存在首页数据未加载
    // 完就刷新，导致和网点首页模板展示不一致的情况。所以是首页相关监听网点变化，再做实际的小程序页面栈跳转
    if (options && options.forbidJump) {
      return;
    }

    const { delta = 1 } = options;
    // 使用 pullDown 是因为部分组件根据这个事件来处理定时器
    this.onPullDownRefresh((backToSamePage) => {
      // 跳分包的微页面不要再执行回调了，回调里会navigateBack
      // 切换网店后的几种场景（依据切换后网店对应的页面模板是否在分包而定）：
      // 1. 首页 -> 分包
      // 2. 首页 -> 首页
      // 3. 分包 -> 首页
      // 4. 分包 -> 分包
      // 对应 2和4 两种情况
      const pages = getCurrentPages() || [];
      const { route } = pages[pages.length - 1 - delta] || {};
      const isFeaturePage =
        /((packages|pages)\/home\/<USER>\/showcase-template/.test(
          route
        );

      if (isFeaturePage) {
        if (!backToSamePage) {
          navigate.switchTab({ url: '/packages/home/<USER>/index' });
        }
      }

      cb && typeof cb === 'function' && cb(true);
    });
  },
};

export default extend(
  {},
  data,
  event,
  featureRequest,
  lifeCycle,
  Theme,
  extendPageConfig
);
