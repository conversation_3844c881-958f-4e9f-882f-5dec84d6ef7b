import config from 'components/showcase/config';
import WscPage from 'pages/common/wsc-page/index';
import featureUtil from './feature';
import navigate from '@/helpers/navigate';
import omit from '@youzan/weapp-utils/lib/omit';
import args from '@youzan/weapp-utils/lib/args';
import ribecaCustomTheme from './ribeca/index';
import newTakeAwayTheme from './new-take-away/index';

WscPage(featureUtil, ribecaCustomTheme, newTakeAwayTheme, {
  onLoad() {
    // 延迟加载进店有礼数据，保证首页先展示
    setTimeout(() => {
      const pages = getCurrentPages() || [];
      const { showcaseTplfromRoute = '' } = pages[0]?.options || {};
      const source = /packages\/(ext-)?home\/dashboard\/index/.test(
        decodeURIComponent(showcaseTplfromRoute)
      )
        ? 1
        : 4;

      this.setYZData({
        showShopPopManager: true,
        source,
      });
    }, 2000);
  },

  showShowcaseComponents(components, type = 0, isRefresh, isFromCache = false) {
    // 确定微杂志的展示类型
    const typeName = config.themes[type] || '';
    console.error(components, config, type);

    this.setYZData({
      type: typeName,
    });

    switch (typeName) {
      case 'takeAway':
        this.fetchTakeAwayThemeDataSuccess(components, isRefresh, isFromCache);
        break;
      // 黎贝卡定制模板
      case 'ribecaCustom':
        this.fetchRibecaThemeDataSuccess(components, isRefresh, isFromCache);
        break;
      // 微页面无法在template
      case 'feature':
        if (this.options.fromRoute) {
          const url = args.add(
            `/${decodeURIComponent(this.options.fromRoute)}`,
            omit({ ...this.options }, ['fromRoute'])
          );
          navigate.redirect({
            url,
          });
          break;
        }
      // eslint-disable-next-line no-fallthrough
      default:
        break;
    }
  },

  // 联系客服的回调
  onContactBack: navigate.contactBack,
});
