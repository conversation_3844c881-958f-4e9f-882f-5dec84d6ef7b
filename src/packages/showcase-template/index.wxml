<import src="./new-take-away/index.wxml" />
<import src="./ribeca/index.wxml" />

<view wx:if="{{ homepageGray }}" id="home-gray" />

<page-container
  show-service-due
  show-shop-status
  open-custom-nav
  title-text="{{ pageTitle }}"
  show-store-switch="{{ type !== 'takeAway' && !isFeaturePage }}"
  fixed-bottom="{{ type==='takeAway' }}"
  page-bg-color="{{ pageBgColor }}"
  forbid-copyright="{{ type === 'ribecaCustom' }}"
  class="page-is-tab-page youzan-{{ appType }} {{ themeClass }} page-{{ deviceType }} {{type === 'ribecaCustom' && 'page-is-tab-page'}} page-has-floating-nav"
>
  <!-- 因为微信暂时无法获取页面 scrollTop 用一个始终置顶的标签的 top 值判断 -->
  <view class="feature-page__top-hook" />
  <salesman-cube
    bind:set-share="handleSalesmanSetShare"
    scenes="feature_page"
    need-bind-relation="{{false}}"
  />
  <!-- page-is-feature 用与在外卖模板控制多网点切换按钮不显示。如果是主页是要显示切换按钮的，feature页面不显示 -->
  <view class="{{pageWindowLock && 'page-locked'}} feature-{{ type }} {{ isFeaturePage ? 'page-is-feature' : ''}}">
    <block wx:if="{{ !fetching }}">
      <!-- 根据类型展示不同模板 -->
      <view class="theme-{{ type }}">
        <template wx:if="{{ type === 'category-double11' }}" is="theme-category-double11" data="{{ alias, themeClass, CURRENT_GLOBAL_SHOP, isMultiStore, type, richTextList, banner, tags, goods, systemInfo, scrollIntoView, scrollTop, fixedGoodsTag, theme: theme[type], extra: theme.extra, allTheme: theme, asyncShowcaseComponents, curComponents, showFloatingNav }}" />
        <template
          wx:elif="{{ type === 'takeAway' }}"
          is="theme-new-takeAway"
          data="{{ alias, themeClass, isMultiStore, CURRENT_GLOBAL_SHOP, banner, coupons, tagList, goodsIdsMap, goodsInfo, startfee, isFetching, goodsMap, activeTag, isTouchGoodsList, isUpScroll, cartGoods, allChoosedNum, allChoosedPrice, showGoodsPreview, showCart, currentActiveGoods, currentActiveSku, showTakeAwaySku, choosedGoods, showUmpDialog }}"
        />
        <template wx:else is="theme-{{ type }}" data="{{ alias, themeClass, CURRENT_GLOBAL_SHOP, isMultiStore, type, richTextList, banner, tags, goods, systemInfo, scrollIntoView, scrollTop, fixedGoodsTag, theme: theme[type], extra: theme.extra, allTheme: theme, asyncShowcaseComponents }}" />
      </view>
      <!-- 悬浮窗 -->
      <floating-nav wx:if="{{ showFloatingNav }}" />
    </block>
    <van-toast id="van-toast" />
    <van-notify id="van-notify" />
  </view>

  <shop-pop-manager wx:if="{{ showShopPopManager }}" source="{{ source }}" feature-id="{{ featureId }}" />
  <collect-gift wx:if="{{ appType === 'wsc' }}" />
   <!-- 营销活动会场悬浮窗 -->
   <marketing-page-icon wx:if="{{ appType === 'wsc' }}"/>
   <custom-tab-bar wx:if="{{ showTabbar }}"></custom-tab-bar>
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage, youzanAppHideTabbar }}" />
