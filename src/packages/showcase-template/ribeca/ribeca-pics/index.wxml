<view class="ribrcapics__wrapper" >
  <view
    wx:for="{{ picList }}"
    wx:for-item="item"
    wx:for-index="index"
    wx:key="title"
    class="ribrcapics__pic-item"
    bindtouchmove="handlePicTouchmove"
    bindtouchstart="handlePicTouchstart"
    bindtouchend="handlePicTouchend"
    bindtap="handlePicTap"
    style="z-index:{{index+1}};transform: translateY({{index > cur ? -700*(index-cur-1)-900*cur : -900*cur}}rpx);"
    data-index="{{index}}"
  >
    <view class="ribrcapics__pic" style="background-image: url({{item.image_url}});background-position-y: {{index > cur  ? -200 : 0}}rpx"></view>

    <view class="ribrcapics__pic-title {{cur === index ? 'ribrcapics__pic-title-current' : ''}}" style="transform: translate(-50%, {{index > cur ? -600 : 0}}rpx)">
      {{ item.title }}
    </view>
    <view class="ribrcapics__pic-desc" style="transform: translate(-50%, 0rpx)">
        {{ item.desc }}
    </view>
  </view>
  <view class="ribrcapics__item-wrapper" style="z-index:11;transform: translateY({{- cur*900 - (picList.length-1-cur)*700}}rpx);"
  >
    <view class="ribrcapics__text-item" wx:if="{{showMemberCenter || showContactCustomerService}}">
      <view class="ribrcapics__service" wx:if="{{showContactCustomerService}}" bindtap="contactService">
        <image
        class="ribrcapics__customer-icon"
        src="https://img.yzcdn.cn/src/components/showcase/ribecaCustom/assets/01.png/service.png"/>
        <text class="ribrcapics__service-text">联系客服</text>
        <button
          class="ribrcapics__transparent-btn"
          bindcontact="onContactBack"
          open-type="contact"></button>
      </view>
      <view wx:if="{{showMemberCenter}}" class="ribrcapics__member {{!!showMemberCenter && !!showContactCustomerService ? 'ribrcapics__service-border' : ''}}" bindtap="gotoMemberCenter">
        <image
        class="ribrcapics__member-icon"
        src="https://img.yzcdn.cn/src/components/showcase/ribecaCustom/assets/01.png/vip.png"/>
        <text class="ribrcapics__member-text">个人中心</text>
      </view>
    </view>
  </view>
  <view class="ribrcapics__bottom-wrapper {{bottomFixed ?'ribrcapics__bottom-fixed' : ''}}" wx:if="{{ showBottomIcon }}">
    <image class="ribrcapics__bottom-img" src="https://img.yzcdn.cn/public_files/2018/06/05/b662e1c0f1a729b252ac2de0969ffb78.png"/>
    <view class="ribrcapics__bottom-text" bindtap="linkToStoreInfo">店铺信息</view>
  </view>
</view>

