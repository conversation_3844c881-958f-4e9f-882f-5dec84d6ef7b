
.ribrcapics__wrapper {
  width: 100%;
}
.ribrcapics__pic-item {
  position: relative;
  transition-duration: .6s;
  transition-timing-function: liner;
  width: 100%;
  height: 900rpx;
  position: relative;
  transform: translateY(0px);
}
.ribrcapics__pic {
  background-repeat: no-repeat;
  position: relative;
  background-size:cover;
  height: 100%;
  width: 100%;
  background-position-x: center;
  background-position-y: 0rpx;
  transition-duration: .6s;
  transition-timing-function: liner;
  box-shadow: 0px 4rpx 16rpx rgba(0,0,0,0.3) inset;
}
.ribrcapics__pic-title, .ribrcapics__pic-desc {
  position: absolute;
  color: #FFFFFF;
  text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.3);
  text-align: center;
  left:50%;
  font-weight: 500;
  transform: translateX(-50%);
  transition-duration: .6s;
  transition-timing-function: liner;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width:100%;
}
.ribrcapics__pic-title {
  bottom: 190rpx;
  font-size: 28rpx;
  line-height: 28rpx;
}

.ribrcapics__pic-title-current {
  bottom: 176rpx;
}

.ribrcapics__pic-desc {
  bottom: 80rpx;
  line-height: 66rpx;
  font-size: 48rpx;
}
.ribrcapics__item-wrapper {
  transition-duration: .6s;
  transition-timing-function: liner;
  width: 100%;
  height: 900rpx;
  position: relative;
  background-image: url(https://img.yzcdn.cn/public_files/2018/06/05/6e2c27fe21ceea465eda0ff137817454.png);
  background-size: cover;
  color: #fff;
  font-size: 28rpx;
}
.ribrcapics__text-item {
  padding: 70rpx 0;
  text-align: center;
  background: #3D3D43;
  box-shadow: 0px 4rpx 16rpx #181818;
}
.ribrcapics__bottom-wrapper {
  text-align: center;
  z-index: 12;
  transition-duration: .6s;
  transition-timing-function: liner;
  opacity: 0;
  transition-property: opacity;
}
.ribrcapics__bottom-fixed {
  position: fixed;
  bottom: 0;
  left:50%;
  margin-left:-50%;
  width:100%;
  opacity: 1;
}
.ribrcapics__bottom-img {
  width: 44rpx;
  height: 44rpx;
  vertical-align: middle;
}
.ribrcapics__bottom-text {
  padding: 0rpx 0 30rpx;
  opacity: 0.5;
  font-size: 20rpx;
  color: #FFFFFF;
}
.ribrcapics__service {
  display: inline-block;
  position: relative;
}
.ribrcapics__transparent-btn {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
  border-radius: none;
  background: transparent;
  background-color: transparent;
  border-color: transparent;
  border: none;
  outline: none;
}
.ribrcapics__service-border {
  border-left: 1rpx solid rgba(256, 256, 256, 0.3);
  padding-left: 30rpx;
  margin-left: 30rpx;
}
.ribrcapics__member {
  display: inline-block;
}
.ribrcapics__customer-icon, .ribrcapics__member-icon {
  width: 40rpx;
  height: 40rpx;
  vertical-align: middle;
  margin-right: 20rpx;
}
.ribrcapics__service-text, .ribrcapics__member-text {
  line-height: 28rpx;
  vertical-align: middle;
}





