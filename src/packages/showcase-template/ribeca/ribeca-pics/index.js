import jumpToLink from 'shared/components/showcase/utils/jumpToLink';
import navigate from '@/helpers/navigate';


const app = getApp();

Component({
  properties: {
    picList: {
      type: Array
    },
    showMemberCenter: {
      type: Number
    },
    showContactCustomerService: {
      type: Number
    },
    showMenuList: {
      type: <PERSON>olean
    },
  },

  attached() {
    // 短屏幕手机 不显示 底部icon
    let showBottomIcon = true;
    const windowHeight = app.getSystemInfoSync().windowHeight;
    const windowWidth = app.getSystemInfoSync().windowWidth;

    // 屏幕高宽比太小，不显示 底部icon
    if (windowHeight / windowWidth < 1.4) {
      showBottomIcon = false;
    }
    this.setData({
      cur: 0,
      lastY: 0,
      showBottomIcon
    });
  },
  methods: {
    linkToStoreInfo() {
      const kdtId = app.getKdtId();
      const title = '店铺信息';
      const src = `https://h5.youzan.com/v2/showcase/cert?no_footer=1&no_btn=1&kdt_id=${kdtId}`;
      navigate.navigate({ url: `/pages/common/webview-page/index?title=${title}&src=${encodeURIComponent(src)}` });
    },
    handlePicTap(event) {
      if (this.data.showMenuList) {
        // 如果menu显示，先隐藏menu
        this.triggerEvent('toggleMenuList');
        return;
      }
      const { index } = event.currentTarget.dataset;
      if (index === this.data.cur) {
        // 跳转链接
        const item = this.data.picList[index];
        const linkType = item.link_type;
        jumpToLink(linkType, item);
      } else {
        this.setData({
          cur: index,
        });
      }
      // 如果是滑动到最后一张图片
      let { length } = this.data.picList;
      if (this.data.cur === length - 1) {
        this.setData({
          bottomFixed: true
        });
      } else {
        this.setData({
          bottomFixed: false
        });
      }
    },
    handlePicTouchmove(event) {
      if (this.data.flag !== 0) {
        return;
      }
      if (this.data.showMenuList) {
        // 如果menu显示，先隐藏menu
        this.triggerEvent('toggleMenuList');
        return;
      }
      let currentY = event.touches[0].pageY;
      let ty = currentY - this.data.lastY;
      let { length } = this.data.picList;
      // 向上滑动
      if (ty < 0) {
        if (this.data.cur < length - 1) {
          this.setData({
            cur: this.data.cur + 1
          });
        }
      } else if (this.data.cur > 0) {
        // 向下滑动
        this.setData({
          cur: this.data.cur - 1
        });
      }
      // 如果是滑动到最后一张图片
      if (this.data.cur === length - 1) {
        this.setData({
          bottomFixed: true
        });
      } else {
        this.setData({
          bottomFixed: false
        });
      }
      this.data.lastY = currentY;
      this.data.flag = 1;
    },
    handlePicTouchstart(event) {
      this.data.flag = 0;
      this.data.lastY = event.touches[0].pageY;
    },
    handlePicTouchend() {
      this.data.flag = 0;
    },
    gotoMemberCenter() {
      // 跳转购物车
      jumpToLink('usercenter', {});
    },
    onContactBack: navigate.contactBack
  }
});
