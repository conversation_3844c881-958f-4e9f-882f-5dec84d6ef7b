import jumpToLink from 'shared/components/showcase/utils/jumpToLink';

Component({
  properties: {
    menuList: {
      type: Array
    },
    showMenuList: {
      type: Boolean
    },
    menuIcon: {
      type: String
    },
    showMenu: {
      type: Boolean
    },
    menuIconTop: {
      type: Number,
      value: 16,
    }
  },

  methods: {
    showMenuList() {
      this.triggerEvent('toggleMenuList');
    },
    handleMenuItemTap(event) {
      const { index } = event.currentTarget.dataset;
      const item = this.data.menuList[index];
      const linkType = item.link_type;
      jumpToLink(linkType, item);
    }
  }
});
