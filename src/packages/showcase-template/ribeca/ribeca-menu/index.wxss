.ribrcamenu__menu-icon {
  z-index: 11;
  display: inline-block;
  position: fixed;
  top: 32rpx;
  left: 32rpx;
  width: 48rpx;
  height: 48rpx;
  background-size: cover;
}
.ribrcamenu__menu-content-wrapper{
  width: 100%;
  background: #000;
  position: fixed;
  left: 0;
  top: 100%;
  z-index: 99;
  color: #fff;
  font-size: 24rpx;
  min-height: 280rpx;
  text-align: center;
  padding: 60rpx 0;
  transition: 0.5s;
  opacity: 0;
  background: #3D3D43;
  box-shadow: 0 -4rpx 8rpx 0 rgba(0,0,0,0.20);
  display: flex;
  align-items: center;
  justify-content: center;
}
.ribrcamenu__menu-content {
  min-width: 300rpx;
}
.ribrcamenu__single-menu{
  display: flex;
  justify-content: center;
  align-items: center;
}
.ribrcamenu__menu-item {
  line-height:34rpx;
  padding-bottom:50rpx;
}
.ribrcamenu__last-item {
  padding-bottom: 0rpx;
}

.ribrcamenu__menu-rise {
  transform: translate3d(0, -100%, 0);
  opacity: 1;
}
