<view class="ribrcamenu__menu-wrapper" wx:if="{{showMenu}}">
  <view 
    class="ribrcamenu__menu-icon"
    bindtap="showMenuList"
    style="background-image:url({{menuIcon}});top: {{ menuIconTop }}px"
  >
  </view>
  <template
    is="menu-list"
    data="{{menuList, showMenuList}}"
  >
  </template>
</view>

<template name="menu-list">
  <view class="ribrcamenu__menu-content-wrapper {{showMenuList ? 'ribrcamenu__menu-rise' : ''}} {{menuList.length === 1 ? 'ribrcamenu__single-menu': ''}}">
      <view class="ribrcamenu__menu-content">
          <view
            wx:for="{{ menuList }}"
            wx:for-item="item"
            wx:for-index="index"
            wx:key="title"
            class="ribrcamenu__menu-item {{index === menuList.length -1 ? 'ribrcamenu__last-item': ''}}"
            bindtap="handleMenuItemTap"
            data-index="{{index}}"
          >
            <view class="ribrcamenu__menu-text">
              {{ item.title }}
            </view>
          </view>
      </view>
  </view>
</template>
