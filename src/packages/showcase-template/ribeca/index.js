import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import getRedirectData from 'shared/components/showcase/utils/getRedirectData';
import jumpToLink from 'shared/components/showcase/utils/jumpToLink';
import { getHeight } from 'shared/utils/nav-config';

export default {
  fetchRibecaThemeDataSuccess(components) {
    // 去除背景色等配置
    if (components[0] && components[0].type === 'config' && components[0].is_global_setting) {
      components.shift();
    }

    // 编辑组件数据
    let ribecaComData = components[0];
    let {
      menuList, imageAdList, showMemberCenter, showContactCustomerService, menuIcon, shopCartIcon, showShopCart, showMenu
    } = ribecaComData;

    // 图片处理
    imageAdList.forEach(item => {
      item.image_url = cdnImage(item.image_url, '!750x0.jpg');
    });

    // 处理 menuList中不支持的链接
    let tempMenuList = [];
    menuList.forEach((item) => {
      let { url } = getRedirectData(item.link_type, item);
      if (url) {
        tempMenuList.push(item);
      }
    });
    menuList = tempMenuList;
    // 当所有菜单项均配置了不支持的内容，则隐藏“菜单”按钮
    if (menuList.length === 0) {
      showMenu = 0;
    }

    const menuIconTop = getHeight() + 16;

    this.setYZData({
      'theme.ribecaCustom.menuList': menuList,
      'theme.ribecaCustom.imageAdList': imageAdList,
      'theme.ribecaCustom.showMemberCenter': showMemberCenter,
      'theme.ribecaCustom.showContactCustomerService': showContactCustomerService,
      'theme.ribecaCustom.menuIcon': menuIcon,
      'theme.ribecaCustom.menuIconTop': menuIconTop,
      'theme.ribecaCustom.shopCartIcon': shopCartIcon,
      'theme.ribecaCustom.showShopCart': showShopCart,
      'theme.ribecaCustom.showMenu': showMenu,
      type: 'ribecaCustom'
    });
  },
  ribecaToggleMenuList() {
    let show = !this.data.theme.ribecaCustom.showMenuList;
    // 设置 浮层状态
    this.setYZData({
      'theme.ribecaCustom.showMenuList': show
    });
  },
  ribecaGotoCart() {
    // 跳转购物车
    jumpToLink('cart', {});
  }
};
