<template name="theme-ribecaCustom">
  <scroll-view class="scroll-view_H">
    <view class="cart-icon" bindtap="ribecaGotoCart" style="background-image:url({{theme.shopCartIcon}});top: {{theme.menuIconTop}}px" wx:if="{{theme.showShopCart}}"></view>
    <ribeca-menu
      menu-list="{{theme.menuList}}"
      bind:toggleMenuList="ribecaToggleMenuList"
      showMenuList="{{theme.showMenuList}}"
      menuIcon="{{theme.menuIcon}}"
      showMenu="{{theme.showMenu}}"
      menuIconTop="{{ theme.menuIconTop }}"
    >
    </ribeca-menu>
    <ribeca-pics
      pic-list="{{theme.imageAdList}}"
      showContactCustomerService="{{theme.showContactCustomerService}}"
      showMemberCenter="{{theme.showMemberCenter}}"
      showMenuList="{{theme.showMenuList}}"
      bind:toggleMenuList="ribecaToggleMenuList"
    >
    </ribeca-pics>
  </scroll-view>
</template>
