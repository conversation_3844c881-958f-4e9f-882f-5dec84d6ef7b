import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { getHeaderStateApi } from '../activity/api';

export function canFetch(state, verifyAt) {
  return (state === 100 || state === 60) && verifyAt === 0;
}

export function canRefund(state, settleState, isBuyer) {
  return (state === 100 || state === 60) && settleState === 1 && isBuyer;
}

export function needPay(state) {
  return state === 3;
}

export function getSkuName(sku = []) {
  if (typeof sku === 'string') {
    try {
      sku = JSON.parse(sku);
    } catch (e) {
      sku = [];
    }
  }
  return sku.map((one) => one.v).join('；');
}

export function checkIfHeader() {
  return new Promise((resolve, reject) => {
    getHeaderStateApi()
      .then((res) => {
        const data = mapKeysCase.toCamelCase(res);
        // firState 0: 已清退 1: 正常
        // reviewState 0: 已通过 1: 审核中 2: 审核失败
        if (
          data.fireState === undefined ||
          (data && (data.fireState === 0 || data.reviewState !== 0))
        ) {
          reject();
        } else {
          resolve();
        }
      })
      .catch(() => {
        reject();
      });
  });
}

const orderStateMap = [
  {
    // 退款中
    test: (state, detail) => detail.feedback === 201,
    stateText: '退款中',
    showSelffetch: false,
    showRefund: true
  },
  {
    // 待支付
    test: (state) => state === 3,
    stateText: '待支付',
    showSelffetch: false,
    showRefund: false
  },
  {
    // 已支付
    test: (state) => state === 5,
    stateText: '已支付',
    showSelffetch: false,
    showRefund: false
  },
  {
    // 已关闭
    test: (state) => state === 99,
    stateText: '已关闭',
    showSelffetch: false,
    showRefund: false
  },
  {
    // 已完成
    test: (state, detail) => state === 100 && detail.verifyAt,
    stateText: '已完成',
    showSelffetch: false,
    showRefund: (state, detail) => detail.settleState === 1
  },
  {
    // 待提货
    test: (state, detail) => canFetch(state, detail.verifyAt),
    stateText: '待提货',
    showSelffetch: true,
    showRefund: (state, detail) => detail.settleState === 1
  },
  {
    test: () => true,
    stateText: '订单异常',
    showSelffetch: false,
    showRefund: false
  }
];

const getOrderStateMap = (detail) => {
  const { state } = detail;
  let showRefundDisplay;
  const { stateText, showSelffetch, showRefund } = orderStateMap.find((one) =>
    one.test(state, detail)
  );
  showRefundDisplay = showRefund;
  if (typeof showRefundDisplay !== 'boolean') {
    showRefundDisplay = showRefund(state, detail);
  }
  return {
    stateText,
    showSelffetch,
    showRefund: showRefundDisplay
  };
};

const calcTotalNum = (list) => {
  const { length } = list;
  let num = 0;
  for (let i = 0; i < length; i++) {
    num += list[i].num;
  }
  return num;
};

/**
 * 获取退款相关信息
 * @param orderNo
 */
const fetchSafeInfo = ({ orderNo }) => {
  return new Promise((resolve, reject) => {
    getApp().carmen({
      api: 'youzan.trade.weapp.refunds/1.0.0/get',
      data: {
        order_no: orderNo
      },
      success(res = {}) {
        resolve(mapKeysCase.toCamelCase(res));
      },
      fail(err) {
        reject(err);
      }
    });
  });
};

function transformToBaidu(lon, lat) {
  lon = +lon;
  lat = +lat;
  if (lon === 0 && lat === 0) {
    return {
      lon: 0,
      lat: 0
    };
  }
  const x = lon;
  const y = lat;
  const z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * Math.PI);
  const theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * Math.PI);
  lon = z * Math.cos(theta) + 0.0065;
  lat = z * Math.sin(theta) + 0.006;

  return {
    lon,
    lat
  };
}

function baiduToGcj(lon, lat) {
  lon = +lon;
  lat = +lat;
  if (lon === 0 && lat === 0) {
    return {
      lon: 0,
      lat: 0
    };
  }
  const x = lon - 0.0065;
  const y = lat - 0.006;
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * Math.PI);
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * Math.PI);
  lon = z * Math.cos(theta);
  lat = z * Math.sin(theta);
  return {
    lon,
    lat
  };
}

const padStart = (str = '', len, char) => {
  const strLen = str.length;
  if (strLen >= len) return str;
  for (let i = 0; i < len - strLen; i++) {
    str = char + str;
  }
  return str;
};

const padEnd = (str = '', len, char) => {
  const strLen = str.length;
  if (strLen >= len) return str;
  for (let i = 0; i < len - strLen; i++) {
    str += char;
  }
  return str;
};

const getBuyUrl = ({ headerBuyerId, alias, activityId }) => {
  return `/packages/groupbuying/buyer-trade/buying/index?headerBuyerId=${headerBuyerId}&alias=${alias}&activityId=${activityId}`;
};

const fetchUmp = (path, query={}, method='GET') => {
  return getApp().request({
    ...query,
    path: `/wscump/groupbuying/${path}`,
    method
  })
}

const fetchTradeUmp = (path, query={}, method='GET') => {
  return getApp().request({
    ...query,
    path: `/wsctrade/groupbuying/${path}`,
    method
  })
}

const fetchUmpBuy = (path, query={}, method='GET') => {
  return getApp().request({
    ...query,
    path: `/wscump/communitybuy/${path}`,
    method
  })
}

const fetchIndustryBuy = (path, query={}, method='GET') => {
  return getApp().request({
    ...query,
    path: `/wscindustry/communitybuy/${path}`,
    method,
  })
}


export {
  orderStateMap,
  getOrderStateMap,
  calcTotalNum,
  fetchSafeInfo,
  transformToBaidu,
  baiduToGcj,
  padStart,
  padEnd,
  getBuyUrl,
  fetchUmp,
  fetchUmpBuy,
  fetchIndustryBuy,
  fetchTradeUmp
};
