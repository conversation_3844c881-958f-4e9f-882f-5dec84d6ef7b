import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import money from '@youzan/weapp-utils/lib/money';
import { getOrderStateMap, getSkuName, needPay } from '../../common';

export function handleData(list) {
  list = mapKeysCase.toCamelCase(list);
  list.forEach(one => {
    const { stateText, showSelffetch } = getOrderStateMap(one);
    // 商品总数
    let total = 0;
    one.itemsLen = one.items.length;
    one.items.forEach(item => {
      item.skuName = getSkuName(item.sku);
      item.priceFormat = money(item.price).toYuan();
      // 每个商品购买数相加
      total += item.num;
    });
    one.items = one.items.slice(0, 3);
    // 商品总数
    one.totalLen = total;
    one.canFetch = showSelffetch;
    one.needPay = needPay(one.state);
    one.orderStateText = stateText;
    one.paymentFormat = money(one.payment).toYuan();
  });
  return list;
}
