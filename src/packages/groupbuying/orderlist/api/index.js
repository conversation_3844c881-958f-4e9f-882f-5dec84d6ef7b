const app = getApp();
const http = app.request;

function getHeaderOrderApi(query) {
  return http({
    path: 'wsctrade/groupbuying/headerOrder/getHeaderOrder.json',
    query
  });
}

function getBuyerOrderApi(query) {
  return http({
    path: 'wsctrade/groupbuying/headerOrder/getBuyerOrder.json',
    query
  });
}

function getActivityDetail(query) {
  return http({
    path: '/wscump/communitybuy/order/getActivityDetail.json',
    query
  });
}

function sendMessage(data) {
  return http({
    path: '/wscump/groupbuying/activity/sendMessage.json',
    method: 'POST',
    data
  });
}

function verifyOrder(data) {
  return http({
    path: '/wsctrade/groupbuying/headerOrder/verify.json',
    method: 'POST',
    data
  });
}

export {
  getHeaderOrderApi, getBuyerOrderApi, getActivityDetail, sendMessage, verifyOrder
};
