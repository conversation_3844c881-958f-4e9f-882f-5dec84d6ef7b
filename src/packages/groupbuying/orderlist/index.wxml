<view class="order">
  <view
    wx:if="{{ activityDetail }}"
    class="notice"
  >
    <view class="text">
      已筛选<text>{{ activityDetail.name }}</text>的订单
    </view>
    <van-button
      wx:if="{{ !hasSend && activityDetail.state === 2 }}"
      type="primary"
      round
      size="small"
      bind:click="sendNotice"
    >
      通知提货
    </van-button>
  </view>
  <van-search
    placeholder="{{ isFromBuyer ? '请输入团长手机号' : '请输入手机号' }}"
    shape="round"
    bindsearch="onSearch"
  />
  <van-tabs
    active="{{ orderTabsActive }}"
    line-width="40"
    sticky="{{ true }}"
    color="#07c160"
    bindchange="onTabChange"
  >
    <van-tab
      wx:for="{{ orderTabsName }}"
      title="{{ item }}"
      wx:key="{{ item }}"
    >
    </van-tab>
  </van-tabs>
  <scroll-view
    class="list"
    scroll-y
    bindscrolltolower="onLoad"
    lower-threshold="100"
    style="{{ listStyle }}"
  >
    <!-- 没有团购订单 -->
    <empty wx:if="{{ showEmptyPage && !loading }}" />
    <!-- 展示团购订单 -->
    <block wx:else>
      <order-item
        wx:for="{{ orderListData }}"
        class="order-item"
        is-from-buyer="{{ isFromBuyer }}"
        wx:key="{{ item.orderNo }}"
        order-detail="{{ item }}"
      />
    </block>
    <view class="loading" hidden="{{ !loading }}">
      <zan-loadmore type="loading" />
    </view>
  </scroll-view>
</view>