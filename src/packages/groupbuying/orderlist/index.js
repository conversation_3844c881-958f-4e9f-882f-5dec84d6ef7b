import WscComponent from 'pages/common/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import isPhone from '@youzan/weapp-utils/lib/validators/is-phone';
import { orderTabsName, orderListParams } from './constant';
import { handleData } from './common';
import {
  getHeaderOrderApi, getBuyerOrderApi, getActivityDetail, sendMessage
} from './api';

WscComponent({
  properties: {
    isFromBuyer: {
      type: Boolean,
      value: true
    },
    listStyle: {
      type: String,
      value: 'height: calc(100vh - 153px)'
    },
    state: {
      type: Number,
      value: 0
    }
  },

  data: {
    // 订单类型
    orderTabsActive: 0,
    // 手机号
    mobile: '',
    // 订单列表参数
    orderListParamsData: {
      page: 1,
      pageSize: 10
    },
    // 活动相关信息
    activityId: '',
    activityDetail: null,
    // 订单列表数据
    orderListData: [],
    showEmptyPage: false,
    loading: false,
    finished: false,
    // tab & 参数
    orderTabsName,
    orderListParams,
    listClass: '',
    // 是否发送过通知
    hasSend: false
  },

  ready() {
    const { isFromBuyer, state } = this.data;
    const { activityId } = this.getOptions();
    this.ajaxCount = 0;
    this.getOrder = !isFromBuyer ? getHeaderOrderApi : getBuyerOrderApi;
    if (activityId && !isFromBuyer) {
      this.setYZData(
        {
          listClass: 'list--act',
          activityId,
          orderTabsActive: +state === 60 ? 3 : 0
        },
        () => {
          this
            .fetchAct()
            .then(() => {
              this.onLoad();
            })
            .catch(e => {
              Toast(e.msg || e || '当前团购信息获取失败');
            });
        }
      );
    } else if (isFromBuyer) {
      this.setYZData({
        listClass: 'list--header'
      });
      this.onLoad();
    } else {
      this.onLoad();
    }
  },

  methods: {
    getOptions() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      return currentPage.options;
    },

    onSearch(e) {
      const value = e.detail;
      if (value && !isPhone(value)) {
        return Toast('请输入有效手机号');
      }
      this.setYZData(
        {
          mobile: value
        },
        () => {
          this.reload();
        }
      );
    },

    onTabChange(e) {
      const orderTabsActive = e.detail.index;
      this.setYZData(
        {
          orderTabsActive
        },
        () => {
          this.reload();
        }
      );
    },

    onLoad() {
      const {
        orderListParamsData,
        mobile,
        orderTabsActive,
        orderListData,
        finished,
        loading,
        activityDetail
      } = this.data;
      const { id: activityId } = activityDetail || {};
      const { page, pageSize } = orderListParamsData;
      const { states = null, verifyState = null } = orderListParams[orderTabsActive];
      const params = {
        page,
        pageSize,
        states,
        mobile
      };
      if (finished || loading) return;
      if (states !== null) {
        params.states = states;
      }
      if (verifyState !== null) {
        params.verifyState = verifyState;
      }
      if (activityId) {
        params.activityId = activityId;
      }
      const currentCount = ++this.ajaxCount;
      this.setYZData({
        loading: true
      });
      this.getOrder(params).then(res => {
        if (currentCount !== this.ajaxCount) return;
        const { list: preList } = res;
        const list = handleData(preList);
        const nextListData = page === 1 ? list : orderListData.concat(list);
        if (list.length < pageSize) {
          this.setYZData({
            finished: true
          });
        }
        this.setYZData({
          orderListData: nextListData,
          loading: false,
          showEmptyPage: nextListData.length === 0,
          'orderListParamsData.page': page + 1
        });
      });
    },

    reload() {
      this.setYZData(
        {
          'orderListParamsData.page': 1,
          finished: false,
          orderListData: []
        },
        () => {
          this.onLoad();
        }
      );
    },

    fetchAct() {
      const { activityId } = this.data;
      return new Promise((resolve, reject) => {
        getActivityDetail({
          id: +activityId
        })
          .then(activityDetail => {
            this.setYZData({
              activityDetail
            }, () => {
              resolve();
            });
          })
          .catch(e => {
            reject(e.msg || e || '当前团购信息获取失败');
          });
      });
    },

    sendNotice() {
      const { state } = this.data.activityDetail;
      if (state !== 2) {
        Dialog.alert({
          message: '活动结束之后才可以发送到货通知',
          className: 'fetch-notice'
        });
        return;
      }

      Dialog.confirm({
        title: '确认通知提货？',
        message: '通知待提货团员提货，每个团员每天仅可通知一次'
      })
        .then(() => {
          this.sendMessage();
        })
        .catch(() => {
          // just handle reject
        });
    },

    sendMessage() {
      const { id } = this.data.activityDetail;
      sendMessage({
        activityId: id
      })
        .then(data => {
          if (data) {
            // 已通知过的不需要通知
            Toast.success('提货通知成功');
            this.setYZData({
              hasSend: true
            });
          } else {
            Toast('发送失败');
          }
        })
        .catch(() => {
          Dialog.alert({
            title: '今日已通知',
            message: '每个团员每天仅可通知一次',
            messageAlign: 'center',
            confirmButtonText: '知道了'
          });
        });
    }
  }
});
