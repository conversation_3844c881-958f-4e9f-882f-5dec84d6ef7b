page {
  background-color: #f8f8f8;
}

.list {
  padding-top: 10px;
  box-sizing: border-box;
}

.notice {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 16px;
  background: #fffbe8;
  color: #323233;
  font-size: 14px;

  .text {
    display: flex;

    > text {
      margin: 0 5px;
      font-weight: 400;
      max-width: 130px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.order-item {
  margin-top: 10px;
}

.van-tab {
  color: #999;

  &--active {
    color: #333;
  }
}

.loading {
  padding-bottom: 10px;
}