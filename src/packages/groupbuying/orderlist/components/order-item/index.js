import Toast from '@vant/weapp/dist/toast/toast';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import WscComponent from 'pages/common/wsc-component/index';
import { verifyOrder } from '../../api';

WscComponent({
  properties: {
    orderDetail: {
      type: Object,
      value: {}
    },
    isFromBuyer: {
      type: Boolean,
      value: true
    }
  },

  data: {
    stateDesc: '',
    canFetch: true,
  },

  methods: {
    fetchSuccess() {
      this.setYZData({
        stateDesc: '已完成',
        canFetch: false
      });
      Toast('提货成功');
    },

    onFetch() {
      Dialog.confirm({
        message: '请确认货物，确认之后不可撤销',
        className: 'groupbuy-verify-order',
        confirmButtonText: '确认提货',
        cancelButtonText: '我再想想'
      }).then(() => {
        const { orderNo, headerBuyerId } = this.data.orderDetail;
        verifyOrder({
          orderNo,
          headerBuyerId
        }).then(() => {
          this.fetchSuccess();
        }).catch((e = {}) => {
          Toast(e.msg || e || '提货失败');
        });
      }).catch(() => {
        // just handle reject bu do nothing
      });
    },
    onOrderItemClick() {
      const { isFromBuyer, orderDetail } = this.data;
      const { orderNo, state } = orderDetail;
      let url = `/packages/groupbuying/buyer-trade/detail/index?orderNo=${orderNo}`;
      if (!isFromBuyer) {
        url += '&isHeader=1';
      }
      if (state === 3 && isFromBuyer) {
        url += '&fromBuy=1';
      }
      wx.navigateTo({
        url
      });
    },
    onActNameClick() {
      const { activityId, activityAlias, headerBuyerId } = this.data.orderDetail;
      let url = `/packages/groupbuying/buyer-trade/buying/index?activityId=${activityId}&alias=${activityAlias}&headerBuyerId=${headerBuyerId}`;
      wx.navigateTo({
        url
      });
    }
  },
});
