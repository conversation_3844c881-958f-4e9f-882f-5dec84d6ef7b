<view class="item-wrap">
  <view
    class="item"
    catchtap="onOrderItemClick"
  >
    <view class="no"># {{ orderDetail.payNo }}</view>
    <view class="info">
      <view
        wx:if="{{ !isFromBuyer }}"
        class="buyer"
      >
        <image src="{{ orderDetail.customerAvatar }}" />
        <view class="nickname">{{ orderDetail.customerNickname || '匿名' }}</view>
        <view>{{ orderDetail.customerMobile }}</view>
      </view>
      <view
        wx:else
        class="act"
        catchtap="onActNameClick"
      >
        <text>{{ orderDetail.actName }}</text>
        <van-icon name="arrow" custom-style="top: 2px;" />
      </view>
      <!-- 已支付和待提货展示为绿色 -->
      <view class="state {{ orderDetail.state === 5 || (orderDetail.state === 100 && orderDetail.verifyState === 0) ? 'state--green' : '' }}">
        {{ stateDesc || orderDetail.orderStateText }}
      </view>
    </view>
    <view wx:if="{{ orderDetail.buyerMsg && !isFromBuyer }}" class="buyer-msg">
      订单备注：{{ orderDetail.buyerMsg }}
    </view>
    <view class="list">
      <goods
        wx:for="{{ orderDetail.items }}"
        wx:key="{{ item.tradeItemId }}"
        goods-data="{{ item }}"
      />
      <view
        wx:if="{{ orderDetail.itemsLen > 3 }}"
        class="more"
      >
        查看全部
        <van-icon name="arrow" />
      </view>
    </view>
    <view class="total">
      <text>共{{ orderDetail.totalLen }}件商品，合计：</text>
      <price
        price="{{ orderDetail.paymentFormat }}"
        price-style="color: #323233"
        big-price-style="font-weight: 400;font-size: 18px"
      />
    </view>
    <view
      wx:if="{{ canFetch && orderDetail.canFetch }}"
      class="opt"
    >
      <van-button
        plain
        type="primary"
        size="small"
        round
        catchtap="onFetch"
      >
        确认提货
      </van-button>
    </view>
    <view
      wx:elif="{{ orderDetail.needPay && isFromBuyer }}"
      class="opt"
    >
      <van-button
        plain
        type="primary"
        size="small"
        round
      >
        立即支付
      </van-button>
    </view>
  </view>
</view>