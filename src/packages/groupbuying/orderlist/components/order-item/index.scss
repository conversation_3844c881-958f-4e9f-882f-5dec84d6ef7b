@import '~mixins/index.scss';

.item-wrap {
  padding: 12px;
  padding-top: 0;
}

.item {
  display: flex;
  flex-direction: column;
  padding: 0 12px;
  border-radius: 4px;
  background: #fff;

  .no {
    margin-top: 8px;
    line-height: 22px;
    font-size: 18px;
    color: #07c160;
    font-weight: bold;
  }

  .info {
    display: flex;
    height: 40px;
    align-items: center;
    justify-content: space-between;
    color: #333;

    .buyer {
      display: flex;
      align-items: center;

      > image {
        height: 24px;
        width: 24px;
        border-radius: 50%;
      }

      > view {
        margin-left: 8px;
        font-size: 14px;
      }

      .nickname {
        max-width: 130px;
        @include multi-ellipsis(1);
      }
    }

    .act {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #333;

      > text {
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .state {
      font-weight: 400;
      font-size: 14px;

      &--green {
        color: #07c160;
      }
    }
  }

  .buyer-msg {
    margin: 8px 0;
    padding: 8px;
    line-height: 18px;
    font-size: 13px;
    color: #323233;
    background: #f7f8fa;
    border-radius: 4px;
  }

  .list {
    .more {
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #969799;
      font-size: 12px;
    }
  }

  .total {
    display: flex;
    height: 36px;
    justify-content: flex-end;
    align-items: center;
    font-size: 12px;
    color: #323233;
  }

  .opt {
    display: flex;
    justify-content: flex-end;
    padding: 8px 0 16px;
  }
}
