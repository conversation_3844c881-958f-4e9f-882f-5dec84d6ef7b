.goods {
  display: flex;
  padding: 8px 0;

  .cover {
    height: 72px;
    width: 72px;
    flex-basis: 72px;
    border-radius: 4px;
  }

  .content {
    flex: 1;
    margin-left: 8px;

    .row-1 {
      display: flex;
      justify-content: space-between;
      color: #4a4a4a;

      .title {
        flex: 1;
        line-height: 18px;
        max-height: 36px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 13px;
      }

      .price {
        margin-left: 10px;
        flex: auto 0 0;
        line-height: 20px;
        font-size: 14px;
      }
    }

    .row-2 {
      margin-top: 8px;
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #969799;
      line-height: 16px;

      .sku {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .count {
        margin-left: 10px;
        flex: auto 0 0;
      }
    }
  }
}