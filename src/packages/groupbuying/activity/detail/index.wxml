<page-container class="{{ themeClass }} page-{{ deviceType }}">
  <view class="detail" style="min-height: {{ state !==0 ? 'calc(100vh - 55px)' : '100vh' }}">
    <notice-bar
      noticeText="{{ noticeText }}"
    ></notice-bar>
    <sus-btn
      wx:if="{{ activityMsg }}"
      bindaction="onClickAction"
    ></sus-btn>
    <view class="list-title">社区团购商品</view>
    <view class="goods-list">
      <view wx:if="{{ state === 1 }}" class="goods-list__bar">
        <view>商品</view>
        <view>是否参加</view>
      </view>
      <!-- 社区团购商品列表 -->
      <goods-item
        wx:for="{{ activityGoodsList }}"
        wx:key="index"
        wx:for-item="goods"
        goods-id="{{ goods.itemId }}"
        pictures="{{ goods.picture }}"
        title="{{ goods.title }}"
        profit="{{ goods.profit }}"
        stock-num="{{ goods.stockNum }}"
        sale-num="{{ goods.salesNum }}"
        pay-price="{{ goods.price }}"
        joined="{{ goods.join }}"
        show-switch="{{ state === 1 }}"
        hide-hairline="{{ index === activityGoodsList.length - 1 }}"
        bindswitch="onSwitch"
        bindimgAction="onImgAction"
      >
      </goods-item>
    </view>

    <!-- 限时折扣商品列表 -->
    <view wx:if="{{limitDiscountGoodsList.length > 0}}" class="list-title">限时折扣商品</view>
    <view wx:if="{{limitDiscountGoodsList.length > 0}}" class="goods-list">
      <view wx:if="{{ state === 1 }}" class="goods-list__bar">
        <view>商品</view>
      </view>
      <goods-item
        wx:for="{{ limitDiscountGoodsList }}"
        wx:key="index"
        wx:for-item="goods"
        goods-id="{{ goods.itemId }}"
        pictures="{{ goods.picture }}"
        profit="{{ goods.skuPrices[0].profit }}"
        title="{{ goods.title }}"
        stock-num="{{ goods.stockNum }}"
        show-switch="{{false}}"
        sale-num="{{ goods.salesNum }}"
        pay-price="{{ goods.skuPrices[0].promotionPrice }}"
        hide-hairline="{{ index === activityGoodsList.length - 1 }}"
        bindimgAction="onImgAction"
      >
      </goods-item>
    </view>
    <view wx:if="{{ state !== 0 }}" class="btn-container" style="height: {{ height }}px">
      <view class="btn-box">
        <view class="tabbar">
          <view class="tabbar-item" catchtap="linkToActOrder">
            <image class="tabbar-item-icon" src="https://img.yzcdn.cn/public_files/2019/08/19/6a28f4b2f6142432565365481ffb32d5.png"></image>
            <view class="tabbar-item-text">本团订单</view>
          </view>
          <view class="tabbar-item" catchtap="getStatistics">
            <image class="tabbar-item-icon" src="https://img.yzcdn.cn/public_files/2018/11/22/d6fee20f890e2fffc96067d3a8fef9d6.png"></image>
            <view class="tabbar-item-text">数据</view>
          </view>
          <view class="tabbar-item" catchtap="generateLoong">
            <image class="tabbar-item-icon" src="https://img.yzcdn.cn/public_files/2018/11/22/4a21ac1a00753b793390ad54c72be83f.png"></image>
            <view class="tabbar-item-text">生成接龙</view>
          </view>
        </view>
        <button
          class="btn"
          catchtap="spreadOrGenerateOrder"
          open-type="{{ state === 2 ? '' : 'share' }}"
        >
          {{ btnText }}
        </button>
      </view>
    </view>
    <navigator url="/packages/groupbuying/activity/list/index">
      <view class="return-home">
        <van-icon class="arrow-icon" name="arrow" custom-style="vertical-align: middle;" /><view>首页</view>
      </view>
    </navigator>
    <canvas canvas-id="myCanvas" style="position: fixed; left: 2000px; top: 0; width: 600px; height: {{ canvasHeight }}px;"/>
    <van-toast id="van-toast" />
    <van-dialog id="van-dialog" />
  </view>
</page-container>
