.detail {
  background: #f8f8f8;
  padding-bottom: 55px;
  overflow: hidden;

  .list-title {
    font-size: 14px;
    color: #666;
    margin: 5px 12px;
  }

  .goods-list {
    // min-height: 78vh;

    &__bar {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #999;
      background: #fff;
      padding: 10px 15px;
      position: relative;
      margin-bottom: 1px;

      &:after {
        content: '';
        transform: scale(0.5);
        position: absolute;
        border-bottom: 1px solid #e5e5e5;
        top: -50%;
        right: -50%;
        bottom: -50%;
        left: -50%;
      }
    }
  }

  .btn-container {
    position: fixed;
    width: 100%;
    z-index: 10;
    bottom: 0px;
    background: #fff;
    .btn-box {
      position: absolute;
      width: calc(100% - 32px);
      z-index: 10;
      top: 0px;
      left: 16px;
      display: flex;
      justify-content: space-around;
      background: #fff;
      .tabbar {
        display: flex;
        flex: 1;
        height: 44px;
        margin: 6px auto;
        .tabbar-item {
          display: flex;
          flex: 1;
          margin-right: 16px;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          &-icon {
            height: 18px;
            width: 18px;
          }

          &-text {
            margin-top: 5px;
            font-size: 10px;
            color: #7d7e80;
          }
        }
      }

      .btn {
        flex: 1;
        height: 44px;
        line-height: 44px;
        border-radius: 22px;
        margin: 6px auto;
        padding: 0;
        display: block;
        background: #4b0;
        color: #fff;
        font-size: 16px;
        text-align: center;
        z-index: 1;
      }

      .btn:first-child {
        color: #4b0;
        background: #fff;
      }
    }
  }

  .return-home {
    display: flex;
    align-items: center;
    width: 60px;
    height: 28px;
    line-height: 28px;
    position: fixed;
    text-indent: 3px;
    left: 0;
    bottom: 120px;
    z-index: 99;
    border-radius: 0 14px 14px 0;
    box-shadow: rgba(213, 213, 213, 0.6) 0 2px 10px 0;
    background-color: #fff;
    opacity: 0.9;
    color: #666;
    font-size: 12px;

    .arrow-icon {
      transform: rotate(180deg);
      position: relative;
      left: 6px;
    }
  }

  @media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
    .btn-container {
      background: #fff;
      padding-bottom: 34px;
    }
  }

  button::after {
    border: none;
  }
}

.copyright-container {
  margin-bottom: 70px;
}
