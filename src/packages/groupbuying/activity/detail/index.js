import WscPage from 'pages/common/wsc-page/index';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import Countdown from 'utils/countdown';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import Toast from '@vant/weapp/dist/toast/toast';
import openWebView from 'utils/open-web-view';
import { logPageEnter } from 'utils/log/logv3';
import { isNewIphone } from 'shared/utils/browser/device-type';
import {
  queryActSet4Header,
  getDeliveryMemuApi,
  saveHeaderActSetApi,
  getHeaderStateApi,
  sendMessageApi,
  getDelivery,
  getByKeys,
  getLimitDiscountDetail,
} from '../api';

const app = getApp();
// 接龙需要的序号emoji
const emoji = [
  '0⃣️',
  '1⃣️',
  '2⃣️',
  '3⃣️',
  '4⃣️',
  '5⃣️',
  '6⃣️',
  '7⃣️',
  '8⃣️',
  '9⃣️',
];
const getImage = (keys) =>
  new Promise((resolve, reject) => {
    let i = 5;
    const check = () => {
      setTimeout(() => {
        getByKeys({
          keys,
        })
          .then((data) => {
            if (data.img) {
              resolve(data);
            } else if (i > 0 && data.type === 'pending') {
              i--;
              check();
            } else {
              throw new Error('社区团购提货单生成失败，请重试');
            }
          })
          .catch((e) => {
            reject(e);
          });
      }, 1000);
    };
    check();
  });

WscPage({
  logConfig: {
    isAsync: true,
  },

  data: {
    activityGoodsList: [],
    limitDiscountGoodsList: [],
    // 当前活动的状态
    state: 0,
    // 倒计时
    countdownTime: 0,
    noticeText: '',
    // 活动结束显示'生成提货确认单'
    btnText: '',
    activityInfo: {},
    copyLoongText: '',
    // 活动名称
    actName: '',
    canvasHeight: 0,
    orderListImg: [],
    // 订货确认单
    orderList: [],
    // 活动内容
    activityMsg: '',
    active: -1,
    height: 56,
  },

  onLoad(options) {
    const { kdtId, guestKdtId } = options;
    if (kdtId !== guestKdtId && guestKdtId.trim() !== '') {
      app.updateKdtId(guestKdtId, false, {
        mark: '904',
      });
    }
    if (isNewIphone()) {
      this.setYZData({
        height: 90,
      });
    }
    this.setYZData({
      activityInfo: options,
    });
    this.getHeaderState();
    this.getActivityDetail(options).then(() => {
      this.getDeliveryMemu();
    });
    // 日志埋点
    logPageEnter(
      'packages/groupbuying/activity/detail/index',
      this.data.activityInfo.activityId,
      {
        act_type: 10002,
        act_id: this.data.activityInfo.activityId,
        agent_id: app.getBuyerId(),
      },
      'cgroupondt'
    );
  },

  getHeaderState() {
    getHeaderStateApi()
      .then((res) => {
        const data = mapKeysCase.toCamelCase(res);
        // firState 0: 已清退 1: 正常
        // reviewState 0: 已通过 1: 审核中 2: 审核失败
        if (
          data.fireState === undefined ||
          (data && (data.fireState === 0 || data.reviewState !== 0))
        ) {
          this.goRecruitPage();
        }
      })
      .catch((e) => {
        if (e.code === 10000) return this.goRecruitPage();
        Toast(e.msg);
      });
  },

  onClickAction() {
    Dialog.alert({
      title: '团购活动说明',
      message: this.data.activityMsg,
      confirmButtonText: '我知道了',
    });
  },

  getActivityDetail(query) {
    // 请求限时折扣活动列表
    getLimitDiscountDetail({
      groupBuyActivityId: this.data.activityInfo.activityId,
      withProfit: true,
    })
      .then((res) => {
        let result = [];
        res.forEach((item) => {
          result = result.concat(item.goodsList);
        });
        this.setYZData({
          limitDiscountGoodsList: result,
        });
      })
      .catch((err) => {});
    return queryActSet4Header(query)
      .then((res) => {
        wx.stopPullDownRefresh();
        const data = mapKeysCase.toCamelCase(res);
        this.responseData = data;
        // 传给倒计时组件的时间戳，活动没开始传startTime, 活动进行中endTime
        let time;
        if (data.activityDetail.state === 0) {
          time = data.activityDetail.startTime;
        } else if (data.activityDetail.state === 1) {
          // 是否显示购物Stepper、手机号field和付款按钮，活动未开始和活动结束不显示
          this.setYZData({
            showStepper: true,
          });
          time = data.activityDetail.endTime;
        } else {
          time = 0;
        }
        this.setYZData({
          countdownTime: time,
          activityDetail: data.activityDetail,
          activityGoodsList: data.items,
          activityMsg: data.activityDetail.notice,
          state: data.activityDetail.state,
          actName: data.activityDetail.name,
          btnText:
            data.activityDetail.state === 2 ? '生成提货确认单' : '推广到群聊',
        });
        this.initNoticeText();
        this.checkArea();
      })
      .catch((e) => {
        if (e.code === 10000) return this.goRecruitPage();
        Toast(e.msg);
      });
  },

  checkArea() {
    const {
      activityDetail: { validArea = [] },
      groupHeaderDetail: { addressCode },
    } = this.responseData;
    // 没有选定区域 或者 选定区域中有当前团长的地区code
    if (
      validArea.length === 0 ||
      (addressCode && validArea.indexOf(addressCode) > -1)
    )
      return false;
    Dialog.alert({
      message: '不在指定团长范围内，请联系商家。',
      confirmButtonText: '返回首页',
    }).then(() => {
      wx.redirectTo({
        url: '/packages/groupbuying/activity/list/index',
      });
    });
    return true;
  },

  initNoticeText() {
    let text;
    switch (this.data.state) {
      case 0:
        text = '距离本次团购活动开始还有';
        break;
      case 1:
        text = '距离本次团购活动结束还有';
        break;
      case 2:
        text = '本次活动已结束';
        break;
      default:
        text = '距离本次团购活动结束还有';
        break;
    }
    if (this.data.state === 2) {
      this.setYZData({
        noticeText: text,
      });
      return;
    }
    const start = new Date().getTime();
    const elapsed = this.data.countdownTime - start;
    new Countdown(elapsed, {
      onChange: (timeData) => {
        let timeStr;
        if (timeData.day > 0) {
          timeStr = ` ${timeData.day} 天 ${timeData.hour} 时 ${timeData.minute} 分 ${timeData.second} 秒`;
        } else {
          timeStr = ` ${timeData.hour} 时 ${timeData.minute} 分 ${timeData.second} 秒`;
        }
        this.setYZData({
          noticeText: text + timeStr,
        });
      },
      onEnd: () => {
        // 如果是活动开始倒计时结束，刷新页面
        if (this.data.state === 0) {
          wx.startPullDownRefresh();
          return;
        }
        this.setYZData({
          state: 2,
        });
      },
    });
  },

  getDeliveryMemu() {
    const query = {
      activityId: this.data.activityInfo.activityId,
    };
    getDeliveryMemuApi(query)
      .then((res) => {
        const data = mapKeysCase.toCamelCase(res);
        const orderList = [
          {
            index: '提货序号',
            name: '昵称',
            mobile: '手机号',
            goodsInfo: '商品',
          },
          ...data.map((item, index) => {
            const goodsInfo = item.goodsInfo
              .map((goods) => {
                const sku = JSON.parse(goods.sku);
                if (sku.length > 0) {
                  const skuStr = sku.map((one) => one.v).join('/');
                  return `${goods.title}(${skuStr})*${goods.num}`;
                }
                return `${goods.title}*${goods.num}`;
              })
              .join(' ');
            return {
              index: index + 1,
              name: item.customerNickname || '匿名',
              mobile: item.customerMobile,
              goodsInfo,
            };
          }),
        ];
        this.setYZData({
          copyLoongText: this.parseMemu(data, true),
          orderList,
        });
      })
      .catch((e) => {
        Toast(e.msg);
      });
  },

  parseMemu(data, isLoong) {
    const content = data
      .map((item, index) => {
        const nickName = item.customerNickname
          ? item.customerNickname
          : item.customerMobile;
        const mobile = item.customerMobile;
        const state = item.orderState;
        let stateText;
        if (state === 3) {
          stateText = '未付';
        } else if (state === 5 || state === 100) {
          stateText = '已付';
        } else {
          stateText = '已关闭';
        }
        const goodsInfo = item.goodsInfo
          .map((goods) => {
            const sku = JSON.parse(goods.sku);
            if (sku.length > 0) {
              const skuStr = sku.map((one) => one.v).join('/');
              return `${goods.title}(${skuStr})*${goods.num}`;
            }
            return `${goods.title}*${goods.num}`;
          })
          .join(' ');
        let text;
        if (isLoong) {
          // 接龙序号计算 001 010 110 目前三种情况
          let _index;
          const num = index + 1;
          if (num < 100) {
            if (num < 10) {
              _index = `${emoji[0]} ${emoji[0]} ${emoji[num]}`;
            } else {
              // 个位
              const g = num % 10;
              // 十位
              const s = Math.floor((num / 10) % 10);
              _index = `${emoji[0]} ${emoji[s]} ${emoji[g]}`;
            }
          } else {
            // 个位
            const g = num % 10;
            // 十位
            const s = Math.floor((num / 10) % 10);
            // 百位
            const c = Math.floor((num / 100) % 10);
            _index = `${emoji[c]} ${emoji[s]} ${emoji[g]}`;
          }
          text = `${_index} ${nickName} ${stateText} ${goodsInfo}`;
        } else {
          text = `${
            index + 1
          }. ${nickName} ${mobile} ${stateText} ${goodsInfo}`;
        }
        return text;
      })
      .join('\n');
    if (content) {
      // 接龙加上团购名称
      return isLoong ? this.data.actName + '\n' + content : content;
    }
    return '';
  },

  spreadOrGenerateOrder() {
    const isDisabled = this.checkArea();
    if (isDisabled) return;
    if (this.data.state === 2) {
      this.generateOrder();
      // 日志埋点
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'confirm_checklist',
          en: '提货确认单',
          params: {
            agent_id: app.getBuyerId(),
            act_type: 10002,
            act_id: this.data.activityInfo.activityId,
          },
          si: app.getKdtId(),
        });
    } else {
      this.onShareAppMessage();
      // 日志埋点
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'promote_cgroupon',
          en: '推广到群聊',
          params: {
            agent_id: app.getBuyerId(),
            act_type: 10002,
            act_id: this.data.activityInfo.activityId,
          },
          si: app.getKdtId(),
        });
    }
  },

  generateOrder() {
    const { orderListImg } = this.data;
    if (this.data.orderList.length === 1) {
      Toast('尚未有订单，请稍后再试');
    } else if (orderListImg.length > 0) {
      this.openImagePreview();
    } else {
      Toast.loading();
      getDelivery({
        activityId: this.data.activityInfo.activityId,
      }).then((data) => {
        setTimeout(() => {
          getImage(data).then((data) => {
            const { img } = data;
            this.setYZData(
              {
                orderListImg: img,
              },
              () => {
                this.openImagePreview();
              }
            );
            Toast.clear();
          });
        }, 1000);
      });
    }
  },

  openImagePreview() {
    const { orderListImg } = this.data;
    wx.previewImage({
      urls: orderListImg,
    });
  },

  generateLoong() {
    if (!this.data.copyLoongText) {
      Toast('尚未有订单，请稍后再试');
    } else {
      wx.setClipboardData({
        data: this.data.copyLoongText,
      });
      // 日志埋点
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'puzzle_cgroupon',
          en: '生成接龙',
          params: {
            agent_id: app.getBuyerId(),
            act_type: 10002,
            act_id: this.data.activityInfo.activityId,
          },
          si: app.getKdtId(),
        });
    }
  },

  getStatistics() {
    openWebView('/wscump/groupbuying/statistics/activity', {
      title: '数据详情',
      query: {
        kdtId: app.getKdtId(),
        id: this.data.activityInfo.activityId,
      },
    });
  },

  sendNotice() {
    if (this.data.state !== 2) {
      Dialog.alert({
        message: '活动结束之后才可以发送到货通知',
        messageAlign: 'center',
      });
      return;
    }

    if (this.data.orderList.length === 1) {
      Toast('暂无订单');
      return;
    }

    sendMessageApi({
      activityId: this.data.activityInfo.activityId,
    })
      .then((data) => {
        if (data) {
          // 已通知过的不需要通知
          Dialog.alert({
            message: '已通知买家取货，买家将收到到货通知',
            messageAlign: 'center',
          });
        } else {
          Toast('发送失败');
        }
      })
      .catch((e) => {
        Toast(e.msg);
      });
  },

  onShareAppMessage() {
    const title = this.data.actName;
    const path = `packages/groupbuying/buyer-trade/buying/index?headerBuyerId=${app.getBuyerId()}&alias=${
      this.data.activityInfo.alias
    }&activityId=${this.data.activityInfo.activityId}&is_share=1&forbidBack=1`;
    return {
      imageUrl: this.getCoverImage(),
      title,
      path,
    };
  },

  getCoverImage() {
    const { activityDetail = {}, activityGoodsList } = this.data;
    const { imgUrl } = activityDetail;
    const firstImg = activityGoodsList[0].picture[0].url;
    const cover = imgUrl || firstImg || 'https://img.yzcdn.cn/image/share.png';
    return cover;
  },

  // 设置参加团购活动的商品
  onSwitch({ detail }) {
    // cb为goods-item组件修改switch值的回调
    const { goodsId, join, cb } = detail;
    const postData = {
      goodsId,
      join,
      activityId: this.data.activityInfo.activityId,
    };
    Toast.loading();
    saveHeaderActSetApi(postData)
      .then(() => {
        Toast.clear();
        cb();
      })
      .catch((e) => {
        Toast(e.msg);
      });
  },

  // 图片预览
  onImgAction(e) {
    const imgSet = e.detail.map((item) => item.url);
    wx.previewImage({
      urls: imgSet,
    });
  },

  goRecruitPage() {
    wx.redirectTo({
      url: '/packages/groupbuying/header-recruit/home/<USER>',
    });
  },

  onPullDownRefresh() {
    this.getHeaderState();
    this.getActivityDetail(this.data.activityInfo).then(() => {
      this.getDeliveryMemu();
    });
  },

  linkToActOrder() {
    const { activityId } = this.data.activityInfo;
    const url = `/packages/groupbuying/header-order/index?activityId=${activityId}`;
    wx.navigateTo({
      url,
    });
  },
});
