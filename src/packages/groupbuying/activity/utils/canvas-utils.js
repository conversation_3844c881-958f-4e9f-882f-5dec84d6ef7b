function CanvasUtils(actName, orderList, appCtx) {
  this.ctx = wx.createCanvasContext('myCanvas');
  this.actName = actName;
  this.orderList = orderList;
  this.appCtx = appCtx;
  this.init();
}
// 初始化数据
CanvasUtils.prototype.init = function () {
  // 画布的宽度
  this.width = 600;
  // 表格宽度
  this.tableWidth = this.width - 20;

  // 求出每条数据的最大高度
  this.ctx.save();
  this.ctx.font = 'normal 14px Arial';
  this.orderList.forEach(orderItem => {
    this.getMaxHeight(orderItem);
  });
  this.ctx.restore();

  // 根据每一行的高度得到canvas画布的高度和表格的高度
  this.tableHeight = this.orderList.reduce((pre, item) => {
    return pre + item.height + 10;
  }, 0);
  this.appCtx.setYZData({
    canvasHeight: this.tableHeight + 80
  });

  this.drawFormHeader();
};

// 得到一条数据最大换行最大的高度
CanvasUtils.prototype.getMaxHeight = function (orderItem) {
  let maxHeight = 0;
  Object.keys(orderItem).forEach(key => {
    let maxWidth;
    // 换行一次高度改变一次
    let textHeight = 20;
    // 序号列的宽度
    const indexWidth = 80;
    // 昵称和手机的宽度
    const otherWidth = 130;
    if (key === 'index') {
      this.indexWidth = maxWidth = indexWidth;
    } else if (key === 'name' || key === 'mobile') {
      this.otherWidth = maxWidth = otherWidth;
    } else {
      // 商品宽度为剩余部分减去表
      maxWidth = this.width - 100 - this.indexWidth - this.otherWidth * 2;
    }
    this.textSplit('' + orderItem[key], 0, 0, maxWidth - 10, 20, () => {
      textHeight += 20;
    });
    maxHeight = Math.max(maxHeight, textHeight);
  });
  // 增加height字段表示本行的高度
  orderItem.height = maxHeight;
};

// 文本换行绘制
CanvasUtils.prototype.fillWrapText = function (text, x, y, maxWidth, lineHeight) {
  const ctx = this.ctx;
  this.textSplit(
    '' + text,
    x,
    y,
    maxWidth,
    lineHeight,
    (line, x, y) => {
      ctx.fillText(line, x, y);
    },
    (line, x, y) => {
      ctx.fillText(line, x, y);
    }
  );
};

// 文本分割处理
CanvasUtils.prototype.textSplit = function (text, x, y, maxWidth, lineHeight, cb, cbb) {
  const ctx = this.ctx;

  // 字符分隔为数组
  const arrText = text.split('');
  let line = '';

  for (let n = 0; n < arrText.length; n++) {
    const testLine = line + arrText[n];
    const metrics = ctx.measureText(testLine);
    const testWidth = metrics.width;
    if (testWidth > maxWidth && n > 0) {
      cb(line, x, y);
      line = arrText[n];
      y += lineHeight;
    } else {
      line = testLine;
    }
  }
  if (typeof cbb === 'function') cbb(line, x, y);
};

// 绘制表格和标题
CanvasUtils.prototype.drawFormHeader = function () {
  const ctx = this.ctx;

  // 将画布背景置白
  ctx.setFillStyle('#fff');
  ctx.fillRect(0, 0, this.width, this.appCtx.data.canvasHeight);

  // 表格标题
  ctx.font = 'bold 20px Arial';
  ctx.setFillStyle('#333');
  ctx.textAlign = 'center';
  ctx.fillText(this.actName, this.width / 2, 30);

  // 表格框架
  ctx.setStrokeStyle('#dcdcdc');
  ctx.rect(10, 60, this.width - 20, this.tableHeight);
  ctx.stroke();

  // 表格头部
  ctx.beginPath();
  ctx.moveTo(10, 90);
  ctx.lineTo(10 + (this.width - 20), 90);
  ctx.lineTo(10 + (this.width - 20), 60);
  ctx.lineTo(10, 60);
  ctx.closePath();
  ctx.stroke();
  ctx.setFillStyle('#c9e8ea');
  ctx.fill();

  // 绘制竖线
  let _x = 90;
  for (let i = 0; i < 3; i++) {
    ctx.moveTo(_x, 60);
    ctx.lineTo(_x, this.tableHeight + 60);
    ctx.stroke();
    _x += 130;
  }

  this.drawTableData();

  ctx.draw();
};

// 绘制表格数据
CanvasUtils.prototype.drawTableData = function () {
  const ctx = this.ctx;
  const orderList = this.orderList;

  // 初始化画笔
  ctx.setFillStyle('#333');
  ctx.moveTo(20, 0);
  ctx.setTextAlign('left');

  // 初始的横线的y坐标和字的y坐标
  let initLineY = 90;
  let initFontY = 80;
  // 文本的基础X
  const fontX = 20;
  orderList.forEach((item, index) => {
    const height = item.height;
    ctx.font = 'normal 14px Arial';
    if (index === 0) {
      // 头部字体加粗
      ctx.font = 'bold 14px Arial';
    }

    this.fillWrapText(item.payNo || item.index, fontX, initFontY, this.indexWidth, 20);
    this.fillWrapText(item.name, fontX + this.indexWidth, initFontY, this.otherWidth, 20);
    this.fillWrapText(
      item.mobile,
      fontX + this.indexWidth + this.otherWidth,
      initFontY,
      this.otherWidth,
      20
    );
    this.fillWrapText(
      item.goodsInfo,
      fontX + this.indexWidth + this.otherWidth * 2,
      initFontY,
      this.width - 50 - this.indexWidth - this.otherWidth * 2,
      20
    );

    // 根据每行高度计算横线的y坐标和下一行文字的y坐标, 头部行和末尾行不画横线，从第二行开始, 10是文本与边线的距离
    initFontY = initFontY + height + 10;
    if (index !== 0 && index !== orderList.length - 1) {
      initLineY = initLineY + height + 10;
      ctx.moveTo(10, initLineY);
      ctx.lineTo(this.width - 10, initLineY);
      ctx.stroke();
    }
  });
};

CanvasUtils.prototype.toImagePath = function () {
  return new Promise((resolve, reject) => {
    wx.canvasToTempFilePath(
      {
        x: 0,
        y: 0,
        width: this.width,
        height: this.appCtx.data.canvasHeight,
        canvasId: 'myCanvas',
        success(res) {
          resolve(res.tempFilePath);
        },
        fail(e) {
          reject(e);
        }
      },
      this.appCtx
    );
  });
};

export default CanvasUtils;
