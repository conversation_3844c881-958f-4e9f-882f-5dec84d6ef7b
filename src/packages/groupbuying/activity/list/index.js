import WscPage from 'pages/common/wsc-page/index';
// eslint-disable-next-line
import { moment as formatDate } from 'utils/time';
import Toast from '@vant/weapp/dist/toast/toast';
import { logPageEnter } from 'utils/log/logv3';
import { getActivityListV2Api } from '../api';
import { checkIfHeader } from '../../common';

const app = getApp();
const getLowestMap = function (item) {
  const {
    lowestCustomerNum, lowestOrderNum, lowestSalesNum, lowestMemberNum, paidOrderNum, paidSalesNum, paidCustomerNum,
  } = item;
  if (lowestMemberNum || lowestOrderNum) {
    return {
      unit: '单',
      num: lowestMemberNum || lowestOrderNum,
      paidNum: paidOrderNum,
    };
  }
  if (lowestCustomerNum) {
    return {
      unit: '人',
      num: lowestCustomerNum,
      paidNum: paidCustomerNum,
    };
  }
  if (lowestSalesNum) {
    return {
      unit: '件',
      num: lowestSalesNum,
      paidNum: paidSalesNum,
    };
  }
  return {
    unit: '单',
    num: 0,
    paidNum: paidOrderNum,
  };
};

let ajaxCount = 0;

function formatList(list) {
  list.forEach(one => {
    one.startTime = formatDate(one.startTime, 'MM-DD HH:mm');
    one.endTime = formatDate(one.endTime, 'MM-DD HH:mm');
    one.covers = one.items.slice(0, 3).map(item => {
      if (item && item.picture && item.picture[0]) {
        return item.picture[0].url;
      }
      return null;
    }).filter(one => !!one);
    one.lowest = getLowestMap(one);
  });
  return list;
}

WscPage({
  logConfig: {
    isAsync: true
  },

  data: {
    tabs: [
      {
        name: '全部',
        state: null
      },
      {
        name: '进行中',
        state: 1,
      },
      {
        name: '未开始',
        state: 0,
      },
      {
        name: '已结束',
        state: 2,
      },
    ],
    list: [],
    loading: false,
    finished: false,
    active: 0,
    page: 1,
    pageSize: 20,
    lock: false,
    shareVisible: false,
    alias: '',
    activityId: 0,
    headerBuyerId: 0,
    shareConfig: {
      title: '福利团购',
      imageUrl: 'https://img.yzcdn.cn/image/share.png',
      path: ''
    },
    isOut: false
  },

  onShareAppMessage() {
    return this.data.shareConfig;
  },

  onLoad() {
    // 日志埋点
    checkIfHeader().then(() => {
      const headerBuyerId = app.getBuyerId();
      const { screenWidth } = wx.getSystemInfoSync();
      this.setYZData({
        headerBuyerId,
        isSmallScreen: screenWidth < 375,
        'shareConfig.path': `packages/groupbuying/buyer-trade/buying/index?headerBuyerId=${headerBuyerId}`
      });
      this.getActivityList();
    }).catch(() => {
      this.setYZData({
        isOut: true
      });
    });
    logPageEnter(
      'packages/groupbuying/activity/list/index',
      '',
      {
        act_type: 10002,
        agent_id: app.getBuyerId()
      },
      'cgrouponlist'
    );
  },

  onShareClick(e) {
    const { alias, activityId, shareConfig } = e.detail;
    this.setYZData({
      shareVisible: true,
      alias,
      activityId,
      shareConfig
    }, () => {
      this.onShareAppMessage();
    });
  },

  onShareClose() {
    this.setYZData({
      shareVisible: false,
    });
  },

  getActivityList() {
    if (this.data.lock) return;
    const {
      page, pageSize, active, tabs
    } = this.data;
    const currentCount = ++ajaxCount;
    this.setYZData({
      lock: true,
      loading: true,
    }, () => {
      const params = {
        page,
        pageSize,
        withUmpActivityInfo: true
      };
      const state = tabs[active].state;
      if (state !== null) {
        params.state = state;
      }
      getActivityListV2Api(params).then(res => {
        let { list } = res;
        if (currentCount !== ajaxCount) return;
        if (list.length < pageSize) {
          this.setYZData({
            finished: true
          });
        }
        list = formatList(list);
        this.setYZData({
          list: this.data.list.concat(list),
          page: this.data.page + 1,
          loading: false,
          lock: false
        });
      }).catch(e => {
        Toast(e.msg || e);
      });
    });
  },

  onListLoad() {
    if (this.data.page !== 1 && !this.data.finished) {
      this.getActivityList();
    }
  },

  onTabChange(e) {
    this.setYZData({
      page: 1,
      active: e.detail.index,
      list: [],
      lock: false,
      finished: false
    }, () => {
      this.getActivityList();
    });
  }
});
