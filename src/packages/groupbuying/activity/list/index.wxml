<wxs module="computed" src="./computed.wxs" />
<view class="activity">
  <van-tabs
    active="{{ active }}"
    line-width="{{ 40 }}"
    color="#07c160"
    sticky
    bind:change="onTabChange"
  >
    <van-tab
      wx:for="{{ tabs }}"
      wx:key="{{ item.state }}"
      title="{{ item.name }}"
    />
  </van-tabs>
  <view
    wx:if="{{ isOut }}"
    class="empty"
  >
    <view class="icon" />
    <view class="content">你已被商家清退，无法推广活动</view>
  </view>
  <scroll-view
    wx:else
    scroll-y
    style="height: calc(100vh - 94px)"
    bindscrolltolower="onListLoad"
  >
    <view
      class="empty"
      wx:if="{{ computed.empty(loading, finished, list) }}"
    >
      <view class="icon" />
      <view class="content">没有{{ computed.emptyText(tabs, active) }}团购活动</view>
    </view>
    <view
      class="activity-list"
      wx:else
    >
      <act
        wx:for="{{ list }}"
        wx:key="{{ item.id }}"
        detail="{{ item }}"
        is-small-screen="{{ isSmallScreen }}"
        bind:share="onShareClick"
      />
    </view>
    <view hidden="{{ !loading }}" class="loading">
      <zan-loadmore type="loading" />
    </view>
  </scroll-view>
</view>

<share
  show="{{ shareVisible }}"
  alias="{{ alias }}"
  activity-id="{{ activityId }}"
  header-buyer-id="{{ headerBuyerId }}"
  bind:close="onShareClose"
/>

<nav active="{{ 0 }}" />

<van-toast id="van-toast" />
<inject-protocol noAutoAuth />
