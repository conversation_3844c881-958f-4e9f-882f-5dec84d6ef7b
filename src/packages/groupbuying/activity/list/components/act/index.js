import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  properties: {
    detail: {
      type: Object
    },
    isSmallScreen: {
      type: Boolean,
      value: false
    }
  },
  methods: {
    onItemClick() {
      const { detail } = this.data;
      wx.navigateTo({
        url: `/packages/groupbuying/activity/detail/index?activityId=${detail.id}&alias=${detail.alias}`
      });
    },
    onOrderClick() {
      const { detail } = this.data;
      wx.navigateTo({
        url: `/packages/groupbuying/header-order/index?activityId=${detail.id}`
      });
    },
    onOrderFetchClick() {
      const { detail } = this.data;
      wx.navigateTo({
        url: `/packages/groupbuying/header-order/index?activityId=${detail.id}&state=60`
      });
    },
    onShareClick() {
      const { id: activityId, alias, name: title } = this.data.detail;
      const headerBuyerId = app.getBuyerId();
      const path = `packages/groupbuying/buyer-trade/buying/index?headerBuyerId=${headerBuyerId}&alias=${
        alias
      }&activityId=${activityId}&is_share=1`;
      this.triggerEvent('share', {
        activityId,
        alias,
        shareConfig: {
          title,
          imageUrl: this.getCoverImage(),
          path
        }
      });
    },
    getCoverImage() {
      const { detail } = this.data;
      const { imgUrl, items } = detail;
      const firstImg = items[0].picture[0].url;
      let cover = imgUrl || firstImg || 'https://img.yzcdn.cn/image/share.png';
      return cover;
    },
  },
});
