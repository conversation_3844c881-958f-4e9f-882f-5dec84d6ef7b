<wxs module="computed" src="./computed.wxs" />
<view
  wx:if="{{ detail }}"
  class="act-item"
  catchtap="onItemClick"
>
  <view class="act-header">
    <view class="act-title">
      <view
        hidden="{{ detail.state !== 1 }}"
        class="tag"
      >
        进行中
      </view>
      <view class="act-title-text">{{ detail.name }}</view>
    </view>
    <view
      class="act-time act-time--0"
      wx:if="{{ detail.state === 0 }}"
    >
      {{ detail.startTime }} 开始
    </view>
    <view
      class="act-time act-time--1"
      wx:elif="{{ detail.state === 1 }}"
    >
      {{ detail.endTime }} 结束
    </view>
    <view
      class="act-time act-time--2"
      wx:else
    >
      已结束
    </view>
  </view>
  <view
    wx:if="{{ detail.lowest.num !== 0 || detail.lowest.paidNum !== 0 }}"
    class="act-desc"
  >
    <text wx:if="{{ detail.lowest.num > 0 }}">满 {{ detail.lowest.num }} {{ detail.lowest.unit }}成团，</text>已团 {{ detail.lowest.paidNum }} {{ detail.lowest.unit }}
  </view>
  <view class="act-body">
    <view  class="act-image {{ isSmallScreen ? 'act-image--small' : '' }}">
      <image
        wx:for="{{ detail.covers }}"
        wx:key="{{ index }}"
        src="{{ item }}"
      />
    </view>
    <view class="act-total">
      共 {{ detail.items.length + (detail.umpRelatedItemNum || 0) }} 件商品
      <van-icon custom-class="icon" name="arrow" />
    </view>
  </view>
  <view class="act-footer">
    <van-button
      wx:if="{{ computed.showGroupOrder(detail) }}"
      custom-class="btn"
      type="primary"
      size="small"
      plain
      round
      catchtap="onOrderClick"
    >
      本团订单
    </van-button>
    <van-button
      wx:if="{{ computed.showShare(detail) }}"
      custom-class="btn"
      type="primary"
      size="small"
      plain
      round
      catchtap="onShareClick"
    >
      分享
    </van-button>
    <view
      wx:if="{{ computed.showVerifyInfo(detail) }}"
      class="act-verify-info"
      catchtap="onOrderFetchClick"
    >
      {{ detail.toTakeOrderNum }} 笔待提货订单
      <van-icon custom-class="icon" name="arrow" />
    </view>
  </view>
</view>