import { fetchUmp, fetchUmpBuy, fetchIndustryBuy } from '../../common';

function getActivityListApi(query) {
  return fetchUmp('/activity/getActivityListV2.json', { query });
}

// 获取限时折扣活动数据
function getLimitDiscountDetail(query) {
  return fetchIndustryBuy('buy/queryTldActivityRichList.json', { query });
}

function getActivityListV2Api(query) {
  return fetchUmpBuy('activity/queryActivitiesForHeader.json', { query });
}

function getHeaderStateApi() {
  return fetchUmp('recruit/getHeaderState.json');
}

function getActvityDetailApi(query) {
  return fetchUmp('activity/getActivityDetail.json', { query });
}

function queryActSet4Header(query) {
  return fetchUmp('queryActSet4Header.json', { query });
}

function getDeliveryMemuApi(query) {
  return fetchUmp('activity/getDeliveryMemu.json', { query });
}

function saveHeaderActSetApi(data) {
  return fetchUmp('activity/saveHeaderActSet.json', { data }, 'POST');
}

function sendMessageApi(data) {
  return fetchUmp('activity/sendMessage.json', { data }, 'POST');
}

function getDelivery(data) {
  return fetchUmpBuy('poster/delivery.json', { data });
}

function getByKeys(data) {
  return fetchUmpBuy('poster/getByKeys.json', { data }, 'POST');
}

export {
  getHeaderStateApi,
  getLimitDiscountDetail,
  getActivityListApi,
  getActivityListV2Api,
  getActvityDetailApi,
  queryActSet4Header,
  getDeliveryMemuApi,
  saveHeaderActSetApi,
  sendMessageApi,
  getDelivery,
  getByKeys
};
