import WscComponent from 'pages/common/wsc-component/index';
import { handleChangeStore } from '../../../buyer-trade/utils/chain';

const app = getApp();

WscComponent({
  properties: {
    headerid: Number,
    detail: {
      type: Object,
      value: {}
    }
  },

  methods: {
    toBuyPage(e) {
      const { headerid: headerBuyerId, kdtId } = e.currentTarget.dataset;
      const currentKdtId = app.getKdtId();
      const url = `/packages/groupbuying/buyer-trade/buying/index?headerBuyerId=${headerBuyerId}`;

      if (currentKdtId !== kdtId) {
        // 连锁情况，要切换店铺
        handleChangeStore({
          redirectUrl: url,
          kdtId,
        });
      } else {
        wx.reLaunch({
          url,
        });
      }
    },
  }
});
