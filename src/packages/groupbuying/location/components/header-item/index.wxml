<view class="header-item" bindtap="toBuyPage" data-headerid="{{ detail.headerBuyerId }}" data-kdt-id="{{ detail.kdtId }}">
  <image class="pic" mode="aspectFit" src="{{ detail.headerAvatar }}" />
  <view class="detail">
    <view class="detail-head">
      <view class="user">
        {{ detail.headerName }}
      </view>
      <view class="tag" wx:if="{{ headerid === detail.headerBuyerId }}">
        当前
      </view>
    </view>
    <view class="name">
      {{ detail.subdistrict }}
    </view>
    <view class="loc">
      提货地址：{{ detail.fetchAddress }}
    </view>
  </view>
  <view class="arrow">
    <view class="tips">
      距你{{ detail.distance }}
    </view>
    <van-icon name="arrow" size="14px" color="#969799" />
  </view>
</view>