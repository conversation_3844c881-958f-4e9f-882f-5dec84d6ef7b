
<page-container class="{{ themeClass }} page-{{ deviceType }}">
  <view class="communitybuy-location">
    <view class="communitybuy-location-head">
      <view class="content">
        <view class="title">
          当前位置
        </view>
        <view class="location" wx:if="{{ hasLocation }}">
          <van-icon name="location" size="16px" color="#07C160" />
          <text>{{ chooseLocation.name }}</text>
        </view>
        <view class="location" wx:else>
          <van-icon name="location" size="16px" color="#C8C9CC" />
          <text class="grey">未获取到定位</text>
        </view>
      </view>
      <view class="btn-change" bindtap="getSetting">
        修改位置
      </view>
    </view>
    <view wx:if="{{ !loading }}">
      <!-- 可获取地址 -->
      <view wx:if="{{ hasLocation }}" class="commnitybuy-location-content">
        <!-- 附近有团长 -->
        <view wx:if="{{ headerList.length !== 0 }}">
          <view class="title">
            你附近有{{ headerList.length }}个团长
          </view>
          <block wx:for="{{headerList}}">
            <header-item
              headerid="{{ lastHeaderBuyerId }}"
              detail="{{ item }}"
            />
          </block>
          <view wx:if="{{!isHqStore}}" class="join" bindtap="applyHeader">
            申请加入团长
          </view>
        </view>
        <!-- 附近暂无团长 -->
        <view wx:else>
          <view class="empty">
            <view class="text">
              附近暂无团长，你可以申请加入团长
            </view>
            <view wx:if="{{!isHqStore}}" class="btn" bindtap="applyHeader">  
              申请加入团长
            </view>
          </view>
        </view>
      </view>
      <!-- 不可获取地址 -->
      <view wx:else>
        <view class="empty">
          <view class="text">
            未获取到定位，请点击重试或手动定位地址
          </view>
          <view class="btn" bindtap="regetLocation">
            重新获取
          </view>
        </view>
        <view wx:if="{{!isHqStore}}" class="join-bottom" bindtap="applyHeader">
          申请加入团长
        </view>
      </view>
    </view>
    <van-loading wx:else size="24px" custom-class="loading-class" />
    <van-popup
      custom-class="popup"
      show="{{ popupShow }}"
      overlay="{{ true }}"
      bind:close="onClose"
    >
      <image class="popupImg" mode="scaleToFill" src="https://img.yzcdn.cn/public_files/2019/03/28/e831606dcb8bd4bff865a9aac299608f.png" />
      <view class="popupText">
        开启位置信息
      </view>
      <van-button round type="primary" size="small" bind:click="openSetting">去开启</van-button>
    </van-popup>
  </view>
</page-container>
