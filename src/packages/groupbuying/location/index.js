import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import navigate from 'shared/utils/navigate';
import { getHeaderInfoApi, getRadiusHeadersApi } from './api/index';
import { transformToBaidu } from '../common';
import { checkHqStore } from '@youzan/utils-shop';

const app = getApp();

WscPage({
  data: {
    lastHeaderBuyerId: '',
    hasLocation: false,
    subdistrict: '',
    chooseLocation: {
      lat: 0,
      lon: 0,
      name: '',
      address: ''
    },
    headerList: [],
    loading: true,
    popupShow: false,
    isHqStore: true
  },

  onLoad(query) {
    const { headerBuyerId = '' } = query;
    this.setYZData({
      lastHeaderBuyerId: headerBuyerId
    });
    this.getLocation(headerBuyerId);
    this.judgeHqStore();
  },

  judgeHqStore() {
    app.getShopInfo().then(({ shopMetaInfo = {} }) => {
      const isHqStore = checkHqStore(shopMetaInfo);
      this.setYZData({
        isHqStore
      });
    });
  },

  // 获取附近团长
  getLocation(headerBuyerId) {
    // 如果带上团长id 去获取团长信息
    if (headerBuyerId) {
      getHeaderInfoApi({ headerBuyerId }).then(res => {
        const data = mapKeysCase.toCamelCase(res);
        this.setYZData({
          chooseLocation: {
            lat: data.lat,
            lon: data.lon,
            name: data.subdistrict
          },
          hasLocation: true
        });
        // 该团长设置了经纬度
        if (res.lon !== 0) {
          getRadiusHeadersApi({
            kdtId: app.getKdtId(),
            count: 200,
            radius: 100,
            lon: data.lon,
            lat: data.lat
          }).then(headerList => {
            const headerListData = mapKeysCase.toCamelCase(headerList);
            this.setYZData({
              headerList: this.sortList(headerListData),
              loading: false
            });
          });
        } else {
          this.setYZData({
            hasLocation: false,
            loading: false
          });
        }
      }).catch(e => {
        Toast(e.msg);
      });
    } else {
      this.setYZData({
        loading: false
      });
    }
  },

  // 重新获取地址
  regetLocation() {
    this.getSetting();
  },

  // 获取用户授权信息
  getSetting() {
    const that = this;
    wx.getSetting({
      complete(res) {
        if (res.authSetting && res.authSetting['scope.userLocation']) {
          that.openLocation();
        } else {
          wx.authorize({
            scope: 'scope.userLocation',
            success() {
              that.openLocation();
            },
            fail() {
              that.setYZData({
                popupShow: true
              });
            }
          });
        }
      }
    });
  },

  openSetting() {
    const that = this;
    wx.openSetting({
      success(res) {
        if (res.authSetting['scope.userLocation']) {
          that.setYZData({
            popupShow: false
          });
        }
      }
    });
  },

  // 打开地图
  openLocation() {
    wx.chooseLocation({
      success: (res) => {
        const lonlat = transformToBaidu(res.longitude, res.latitude);
        this.setYZData({
          loading: true
        });
        this.setYZData({
          chooseLocation: {
            ...lonlat,
            name: res.name,
            address: res.address
          },
          hasLocation: true
        });
        getRadiusHeadersApi({
          kdtId: app.getKdtId(),
          count: 200,
          radius: 100,
          ...lonlat
        }).then(headerList => {
          const headerListData = mapKeysCase.toCamelCase(headerList);
          this.setYZData({
            headerList: this.sortList(headerListData),
            loading: false
          });
        });
      }
    });
  },

  onClose() {
    this.setYZData({ popupShow: false });
  },

  sortList(list) {
    const nowIndex = list.findIndex(n => n.headerBuyerId == this.data.lastHeaderBuyerId);
    if (nowIndex > 0) {
      list.unshift(list[nowIndex]);
      list.splice(nowIndex + 1, 1);
    }
    return list.map(item => {
      return this.formate(item);
    });
  },

  formate(data) {
    const { distance, headerAvatar } = data;
    if (distance < 1) {
      data.distance = (distance * 1000).toFixed(0) + 'm';
    } else {
      data.distance = distance.toFixed(1) + 'km';
    }
    data.headerAvatar = headerAvatar || 'https://img.yzcdn.cn/upload_files/avatar.png';
    return data;
  },

  applyHeader() {
    navigate.navigate({
      url: '/packages/groupbuying/header-recruit/home/<USER>'
    });
  }
});
