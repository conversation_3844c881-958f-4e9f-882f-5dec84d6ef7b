.communitybuy-location{
  color: #323233;
  font-size: 12px;
  box-sizing: border-box;
}

.communitybuy-location-head{
  display: flex;
  height: 64px;
  background-color: #fff;
  padding: 12px 16px;
  box-sizing: border-box;
  
  >.content{
    flex: 1;
    .title{
      color: #969799;
      line-height: 16px;
    }
    .location{
      display: flex;
      align-items: center;
      margin-top: 6px;
      line-height: 16px;
      height: 16px;
      font-size: 14px;
      font-weight: bolder;
      
      >.grey{
        color: #C8C9CC;
      }
    }
  }
  
  >.btn-change{
    width: 64px;
    height: 24px;
    line-height: 24px;
    color: #07C160;
    text-align: center;
    border: 1px solid #07C160;
    border-radius: 12px;
    margin-top: 8px;
  }
}

.commnitybuy-location-content{
  box-sizing: border-box;
  padding: 0 16px;

  .title{
    line-height: 50px;
    font-size: 14px;
    color: #969799;
  }

  .join{
    position: relative;
    color: #07C160;
    font-size: 14px;
    text-align: center;
    margin-top: 15px;
  }
}

.empty{
  position: relative;
  margin-top: 160px;
  text-align: center;
  font-size: 14px;

  >.text{
    color: #999;
    line-height: 60px;
  }

  >.btn{
    width: 100px;
    height: 36px;
    line-height: 36px;
    color: #07C160;
    border: 1px solid #07C160;
    border-radius: 18px;
    margin: auto;
  }
}

.join-bottom{
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: 100px;
  color: #07C160;
  font-size: 14px;
}

.loading-class{
  margin-top:160px;
  left:50%;
  transform:translate(-50%);
}

.popup{
  text-align: center;
  font-size: #333;
  line-height: 22px;
  padding: 20px 40px 16px;

  .popupImg{
    width: 200px;
    height: 136px;
  }

  .popupText{
    line-height: 32px;
    font-weight: bolder;
  }
}