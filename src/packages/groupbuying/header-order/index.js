import WscPage from 'pages/common/wsc-page/index';

WscPage({
  data: {
    show: false,
    loading: true,
    state: 0,
    listStyle: 'height: calc(100vh - 153px)'
  },

  onLoad(options) {
    const { activityId, state = 0 } = options;
    if (!activityId) {
      this.setYZData({
        show: true,
        loading: false,
      });
    } else {
      this.setYZData({
        listStyle: 'height: calc(100vh - 148px)',
        state: +state,
        loading: false,
      });
    }
  }
});
