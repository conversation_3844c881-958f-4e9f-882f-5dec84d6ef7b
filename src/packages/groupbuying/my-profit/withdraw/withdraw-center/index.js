import money from '@youzan/weapp-utils/lib/money';
import WscPage from 'pages/common/wsc-page/index.js';
import openWebView from 'utils/open-web-view';
import { getHeaderAssetsApi } from '../../api';

const app = getApp();

const currentPage = {
  data: {
    profitSum: 0,
  },

  onShow() {
    this.requestAvaliable();
  },

  handleWithdrawListClick() {
    wx.navigateTo({
      url: '/packages/groupbuying/my-profit/withdraw/withdraw-record/index'
    });
  },

  handleWithdrawClick() {
    openWebView('/wscassets/withdraw', {
      title: '提现',
      query: {
        kdtId: app.getKdtId()
      }
    });
  },

  handleWithdrawIncomeClick() {
    wx.navigateTo({
      url: '/packages/groupbuying/my-profit/withdraw/income-detail/index'
    });
  },

  requestAvaliable() {
    wx.showToast({
      title: '加载中',
      icon: 'loading'
    });
    getHeaderAssetsApi().then(res => {
      wx.hideToast();
      // 设置顶部的数据，一些基本信息
      this.setYZData({
        profitSum: money(res.available).toYuan()
      });
    }).catch(err => {
      wx.showToast({
        title: err.message || err.msg || '获取提现信息失败，请重试',
        icon: 'none'
      });
    });
  }
};

const pageConfig = { ...currentPage };

WscPage(pageConfig);
