<view>
  <view class="account-panel">
    <van-cell-group>
      <van-cell>
        <view slot="title" class="title-cell">
          <view class="title-label">银行</view>
          <picker
            range="{{ bankList }}"
            range-key="name"
            catchchange="bindPickerChange"
          >
            <view>{{bankList[pickerIndex].name || '请选择银行'}}</view>
          </picker>
        </view>
      </van-cell>
      <van-cell>
        <view slot="title" class="title-cell">
          <view class="title-label">银行卡卡号</view>
          <input
            value="{{ bankData.cardNo }}"
            type="text"
            placeholder="请填入银行卡卡号"
            bindinput="handleFieldChange"
            data-name="bankcard-number"
          />
        </view>
      </van-cell>
      <van-cell>
        <view slot="title" class="title-cell">
          <view class="title-label">姓名</view>
          <input
            value="{{ bankData.cardholder }}"
            type="text"
            placeholder="填入姓名"
            bindinput="handleFieldChange"
            data-name="name"
          />
        </view>
      </van-cell>
    </van-cell-group>
  </view>

  <view class="action-wrapper">
    <van-button
      type="primary"
      size="large"
      bind:click="bindBankCard"
    >
      完成
    </van-button>
  </view>
</view>

<van-toast id="van-toast" />
<inject-protocol noAutoAuth />