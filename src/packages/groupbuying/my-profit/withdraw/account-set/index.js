import WscPage from 'pages/common/wsc-page/index.js';
import Toast from '@vant/weapp/dist/toast/toast';

const app = getApp();

const currentPage = {
  data: {
    bankList: [],
    pickerIndex: '',
    bankData: {}
  },

  onLoad() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    this.fetchBankList()
      .then(() => {
        return this.requestBindCard();
      })
      .catch(e => {
        console.error('set account error', e);
      })
      .then(() => {
        wx.hideLoading();
      });
  },

  handleFieldChange(e) {
    const { detail, target } = e;
    const { name } = target.dataset;
    switch (name) {
      case 'bankcard-number':
        this.setYZData(
          {
            'bankData.cardNo': detail.value
          },
          {
            immediate: true
          }
        );
        break;
      case 'name':
        this.setYZData(
          {
            'bankData.cardholder': detail.value
          },
          {
            immediate: true
          }
        );
        break;
      default:
        break;
    }
  },

  // 选择银行
  bindPickerChange({ detail }) {
    this.setYZData({
      pickerIndex: detail.value,
      'bankData.bankName': this.data.bankList[detail.value].name,
      'bankData.bankCode': this.data.bankList[detail.value].code
    });
  },

  fetchBankList() {
    return new Promise((resolve, reject) => {
      app.carmen({
        api: 'youzan.salesman.wap.withdraw.banks/1.0.0/list',
        success: resp => {
          this.setYZData({
            bankList: resp
          });
          resolve();
        },
        fail: err => {
          wx.showToast({
            title: err.messge || err.msg || '获取支持的银行列表失败',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  },

  bindBankCard() {
    const { cardNo, cardholder, bankName, bankCode, ...other } = this.data.bankData;
    if (!cardNo) {
      Toast('银行卡号必须填写');
      return;
    }
    if (!cardholder) {
      Toast('银行卡持有者姓名必须填写');
      return;
    }
    if (!bankName || !bankCode) {
      Toast('必须选择一个银行');
      return;
    }
    app.request({
      path: '/wscump/salesman/bindCard.json',
      method: 'POST',
      data: {
        ...other,
        cardNo,
        cardholder,
        bankName,
        bankCode,
      }
    }).then(res => {
      if (res.success) {
        Toast('绑定成功');
        // 根据进入来源回退
        const url = this.__query__.from === 'choosen'
          ? '/packages/groupbuying/my-profit/withdraw/account-choose/index'
          : '/packages/groupbuying/my-profit/withdraw/withdraw-assets/index';
        wx.redirectTo({
          url
        });
      }
    }).catch(e => {
      Toast(e.message || e.msg || '绑定银行卡失败，请重试');
    });
  },

  // 一个人只能绑一张卡，如果已有
  requestBindCard() {
    let resolve;
    const promise = new Promise(r => (resolve = r));
    app.carmen({
      api: 'youzan.salesman.wap.withdraw.wallet/1.0.0/get',
      success: res => {
        if (!res || !res.bank) {
          return;
        }
        if (Object.keys(res.bank).length) {
          // 写入默认选中一家银行
          let pickerIndex = '';
          this.data.bankList.forEach((bank, index) => {
            if (bank.name === res.bank.bankName) {
              pickerIndex = index;
            }
          });
          // 写入默认数据
          // 因为隐私原因，银行卡号只返回后面四位，所以不回写
          this.setYZData({
            'bankData.cardholder': res.bank.name,
            // 'bankData.cardNo': res.bank.cardNo,
            'bankData.bankName': res.bank.bankName,
            'bankData.cardBindId': res.bank.bindId,
            'bankData.bankCode': this.data.bankList[pickerIndex].code,
            'fieldConfig.name.value': res.bank.name,
            pickerIndex
          });
        }
      },
      fail: err => {
        wx.showToast({
          title: err.message || err.msg || '获取绑定信息失败，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideToast();
        resolve();
      }
    });
    return promise;
  }
};

const pageConfig = Object.assign({}, currentPage);

WscPage(pageConfig);
