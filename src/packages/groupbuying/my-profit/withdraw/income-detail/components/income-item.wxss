.income-list {
  padding-left: 10px;
  background: #fff;
  position: relative;
  border-bottom: 1rpx solid #e5e5e5;
}

.income-desc {
  font-size: 14px;
  color: #333;
  display: inline-block;
  margin-bottom: 4px;
  line-height: 24px;
  width: 100%;
}

.income-time {
  color: #999;
  font-size: 12px;
  line-height: 18px;
}

.income-number {
  float: right;
  display: inline-block;
  color: #06bf04;
  font-size: 14px;
}

.income-item {
  padding: 5px 10px 5px 0;
  position: relative;
  background: #fff;
  border-top: 1rpx solid #e5e5e5;
}

.negative-num {
  color: #ed5050 !important;
}

.income-detail {
  font-size: 14px;
  color: #999;
}

.header {
  height: 24px;
  padding: 10px 10px 10px 0;
  position: relative;
  line-height: 24px;
  border-bottom: 1rpx solid #e5e5e5;
}


.detail-body {
  padding-right: 10px;
}

.detail-body .clearfix {
  line-height: 30px;
  display: block;
  width: 100%;
}

.left-side {
  float: left;
}

.right-side {
  float: right;
}

.clearfix::after {
  height: 0;
  clear: both;
  visibility: hidden;
  content: '';
  display: block;
}