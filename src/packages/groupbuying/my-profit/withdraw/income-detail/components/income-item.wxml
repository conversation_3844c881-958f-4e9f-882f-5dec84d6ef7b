<template name="income-item">
  <view class="income-item" bindtap="handleIncomeItemClick" data-index="{{dataIndex}}" data-water-no="{{item.waterNo}}">
    <view class="income-desc">
      {{item.type}}
      <view class="{{item.profit < 0 ? 'income-number negative-num' : 'income-number'}}">
        {{item.profit}}
      </view>
    </view>
    <view class="income-time">{{item.time}}</view>
  </view>
  <view hidden="{{!item.show}}" class="income-detail">
    <view class="detail-body">
      <view class="clearfix">
        <view class="left-side">类型</view>
        <view class="right-side">{{item.detail.type}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">备注</view>
        <view class="right-side">{{item.detail.remark}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">时间</view>
        <view class="right-side">{{item.detail.time}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">流水号</view>
        <view class="right-side">{{item.detail.waterNo}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">订单号</view>
        <view class="right-side">{{item.detail.orderNo}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">支付渠道</view>
        <view class="right-side">{{item.detail.channel}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">余额</view>
        <view class="right-side">{{item.detail.balance}}</view>
      </view>
    </view>
  </view>
</template>