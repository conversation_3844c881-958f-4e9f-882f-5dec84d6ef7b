import money from '@youzan/weapp-utils/lib/money';
import WscPage from 'pages/common/wsc-page/index.js';

const app = getApp();

const currentPage = {
  data: {
    incomeList: [],
    page: 0
  },

  onLoad() {
    this.requestIncomeList();
  },

  handleIncomeItemClick(e) {
    const index = e.currentTarget.dataset.index;
    const waterNo = e.currentTarget.dataset.waterNo;
    const incomeList = this.data.incomeList;
    if (!incomeList[index].detail) {
      this.requestIncomeDetail(waterNo).then((resp) => {
        resp.balance = money(resp.balance).toYuan();
        incomeList[index].detail = resp;
        incomeList[index].show = !incomeList[index].show;
        this.setYZData({
          incomeList
        });
      }).catch(() => {
        wx.showToast({
          title: '加载详情数据失败，请重试',
          icon: 'none'
        });
      })
    } else {
      incomeList[index].show = !incomeList[index].show;
      this.setYZData({
        incomeList
      });
    }
  },

  requestIncomeList() {
    wx.showToast({
      title: '加载中',
      icon: 'loading'
    });
    const pageNo = this.data.page + 1;
    if (this.data.nomore) return;
    app.carmen({
      api: 'youzan.salesman.wap.inout/1.0.0/search',
      query: {
        pageSize: 15,
        pageNo
      },
      success: (res) => {
        if (!res) return;
        if (res.length === 0 && this.data.page !== 0) {
          this.setYZData({
            nomore: true
          });
          wx.showToast({
            title: '没有更多了',
            icon: 'none'
          });
        }
        let incomeList = this.data.incomeList;
        res.forEach((item) => {
          item.profit = money(item.profit).toYuan();
        });
        incomeList = incomeList.concat(res);
        this.setYZData({
          incomeList,
          page: pageNo
        });
      },
      fail: (err) => {
        wx.showToast({
          title: err.message || err.msg || '获取销售员基础失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  requestIncomeDetail(waterNo) {
    return new Promise((resolve, reject) => {
      app.carmen({
        api: 'youzan.salesman.wap.inout/1.0.0/get',
        query: {
          waterNo
        },
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  onReachBottom() {
    this.requestIncomeList();
  }
};

const pageConfig = Object.assign({}, currentPage);

WscPage(pageConfig);
