<template name="withdraw-item">
  <view
    bindtap="handleWithdrawItemClick"
    data-index="{{dataIndex}}"
    data-water-no="{{item.outBizNo}}"
    data-type="{{item.dataType}}"
    class="income-item"
  >
    <view class="income-desc">
      {{item.state}}
      <view class="income-number">{{item.withdrawMoney}}</view></view>
    <view class="income-time">{{item.applyTime}}</view>
  </view>
  <view class="income-detail" hidden="{{!item.show}}">
    <view class="detail-body">
      <view class="clearfix">
        <view class="left-side">提现金额</view>
        <view class="right-side">{{item.detail.withdrawAmount}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">手续费</view>
        <view class="right-side">0</view>
      </view>
      <view class="clearfix">
        <view class="left-side">费率</view>
        <view class="right-side">0</view>
      </view>
      <view class="clearfix">
        <view class="left-side">流水号</view>
        <view class="right-side">{{item.detail.outBizNo}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">申请时间</view>
        <view class="right-side">{{item.detail.applyTime}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">收款账户</view>
        <view class="right-side">{{item.detail.withdrawAccount}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">提现状态</view>
        <view class="right-side">{{item.detail.withdrawStatus}}</view>
      </view>
      <view wx:if="{{item.detail.failDesc}}" class="clearfix">
        <view class="left-side">失败原因</view>
        <view class="right-side">{{item.detail.failDesc}}</view>
      </view>
      <view class="clearfix">
        <view class="left-side">到账时间</view>
        <view class="right-side">{{item.detail.finishTime}}</view>
      </view>
    </view>
  </view>
</template>