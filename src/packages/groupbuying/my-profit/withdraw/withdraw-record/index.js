import WscPage from 'pages/common/wsc-page/index.js';
import money from '@youzan/weapp-utils/lib/money';

const app = getApp();

const currentPage = {
  data: {
    withdrawList: [],
    page: 0
  },

  onLoad() {
    this.requestWithdrawList();
  },

  handleWithdrawItemClick(e) {
    const index = e.currentTarget.dataset.index;
    const dataType = e.currentTarget.dataset.type;
    const waterNo = e.currentTarget.dataset.waterNo;
    const withdrawList = this.data.withdrawList;
    if (!this.data.withdrawList[index].detail) {
      this.requestWithdrawDetail(dataType, waterNo).then((resp) => {
        resp.withdrawAmount = money(resp.withdrawAmount).toYuan();
        withdrawList[index].detail = resp;
        withdrawList[index].show = !withdrawList[index].show;
        this.setYZData({
          withdrawList
        });
      }).catch(() => {
        wx.showToast({
          title: '加载详情数据失败，请重试',
          icon: 'none'
        });
      });
    } else {
      withdrawList[index].show = !withdrawList[index].show;
      this.setYZData({
        withdrawList
      });
    }
  },

  requestWithdrawList() {
    wx.showToast({
      title: '加载中',
      icon: 'loading'
    });
    const pageNo = this.data.page + 1;
    if (this.data.nomore) return;
    app.carmen({
      api: 'youzan.salesman.wap.withdraw/1.0.0/search',
      query: {
        pageSize: 15,
        pageNo
      },
      success: (res) => {
        if (!res) return;
        if (res.length === 0 && this.data.page !== 0) {
          this.setYZData({
            nomore: true
          });
          wx.showToast({
            title: '没有更多了',
            icon: 'none'
          });
        }
        let withdrawList = this.data.withdrawList;
        res.forEach((item) => {
          item.withdrawMoney = money(item.withdrawMoney).toYuan();
        });
        withdrawList = withdrawList.concat(res);
        this.setYZData({
          withdrawList,
          page: pageNo
        });
      },
      fail: (err) => {
        wx.showToast({
          title: err.message || err.msg || '获取提现记录列表失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  requestWithdrawDetail(dataType, waterNo) {
    return new Promise((resolve, reject) => {
      app.carmen({
        api: 'youzan.salesman.wap.withdraw/1.0.0/get',
        query: {
          waterNo,
          dataType
        },
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  onReachBottom() {
    this.requestWithdrawList();
  }
};

const PageConfig = Object.assign({}, currentPage);

WscPage(PageConfig);
