<page-container class="{{ themeClass }} page-{{ deviceType }}">
  <view class="withdraw-assets">

    <van-cell-group custom-class="flex-cell">
      <van-cell title="可提现金额" value="{{'￥' + moneyCanWithdraw}}" />
      <van-cell title="本次提现">
        <input
          type="text"
          placeholder="银行卡单笔最高提现5万"
          maxlength="8"
          bindinput="handleInputChange" />
      </van-cell>
    </van-cell-group>

    <van-cell-group custom-class="flex-cell">
      <van-cell
        title="提现账户"
        value="{{withdrawAccountName || '去设置'}}"
        is-link
        bind:click="setAccount"
      />
    </van-cell-group>

    <view class="withdraw-button-wrapper">
      <van-button type="primary" size="large" bindtap="onWithdrawClicked">提现</van-button>
    </view>

    <view class="withdraw-tips">
      <view class="content">• 金额低于1元时不可提现</view>
      <view class="content">• 预计三个工作日可到账</view>
    </view>

    <van-dialog
      use-slot
      title="请确认"
      show="{{ showWithdrawPop }}"
      show-cancel-button
      bind:cancel="toggleWithdrawPop"
      bind:confirm="onSureWithdrawClicked"
    >
      <view class="dialog-content">
        <view class="item">
          提现金额:
        </view>
        <view class="item">￥{{withdrawValue}}</view>
        <view class="item">
          提现账号:
        </view>
        <view class="item">{{withdrawAccountName}}</view>
      </view>
    </van-dialog>
  </view>
  <van-toast id="van-toast" />
</page-container>

<van-action-sheet
  show="{{ actionsheet.show }}"
  actions="{{ actionsheet.actions }}"
  cancel-text="取消"
  close-on-click-overlay
  bind:close="handleZanActionsheetCancel"
  bind:cancel="handleZanActionsheetCancel"
  bind:select="handleZanActionsheetClick"
/>
