import money from '@youzan/weapp-utils/lib/money';
import Toast from '@vant/weapp/dist/toast/toast';
import WscPage from 'pages/common/wsc-page/index.js';
import { getHeaderAssetsApi } from '../../api';

const app = getApp();

const currentPage = {
  data: {
    moneyCanWithdraw: 0,
    withdrawAccountName: '',
    showWithdrawPop: false,
    withdrawValue: '',
    supportWxWallet: false,
    bindBankCard: false,
    actionsheet: {
      show: false,
      actions: [{
        name: '修改账户',
        loading: false
      }]
    }
  },

  onLoad() {
    this.requestSupportWxWallet().then(() => {
      this.requestWithdrawAccount();
    });
    this.requestAvaliable();
  },

  onWithdrawClicked() {
    // 校验输入金额格式
    const result = this.validateWithdrawValue();
    if (result.valid) {
      this.toggleWithdrawPop();
    } else {
      wx.showToast({
        title: result.errMsg,
        icon: 'none'
      });
    }
  },

  validateWithdrawValue() {
    // 校验提现金额
    const withdrawValue = this.data.withdrawValue;
    const moneyCanWithdraw = this.data.moneyCanWithdraw;
    const result = {
      valid: true,
      errMsg: ''
    };
    if (!this.data.withdrawAccountName) {
      result.valid = false;
      result.errMsg = '请先设置提现账户';
      return result;
    }

    if (/\.\d{3,}$/.test(withdrawValue) || /[a-z]/.test(withdrawValue) || isNaN(+withdrawValue)) {
      result.valid = false;
      result.errMsg = '金额输入格式不正确';
      return result;
    }

    if (!withdrawValue) {
      result.valid = false;
      result.errMsg = '请输入提现金额';
      return result;
    }
    if (+moneyCanWithdraw < 1) {
      result.valid = false;
      result.errMsg = '可提现金额不可低于1元';
      return result;
    }

    if (+withdrawValue < 1) {
      result.valid = false;
      result.errMsg = '提现金额不可低于1元';
      return result;
    }

    if (this.__query__.choose === 'wx' && +withdrawValue > 20000) {
      result.valid = false;
      result.errMsg = '单笔最高可提现两万';
      return result;
    }

    if (+withdrawValue > 50000) {
      result.valid = false;
      result.errMsg = '银行卡单笔最高可提现五万';
      return result;
    }


    if (+withdrawValue > +moneyCanWithdraw) {
      result.valid = false;
      result.errMsg = '提现金额不可超过可提现金额';
      return result;
    }

    return result;
  },

  toggleWithdrawPop() {
    const showWithdrawPop = !this.data.showWithdrawPop;
    this.setYZData({
      showWithdrawPop
    });
  },

  onSureWithdrawClicked() {
    const acctType = this.__query__.choose === 'wx' ? 'WEIXIN' : 'BANK';
    const query = {
      money: money(this.data.withdrawValue).toCent(),
      acctType
    };
    if (acctType === 'BANK') {
      Object.assign(query, {
        bindCardId: this.data.bindCardId
      });
    }

    app.carmen({
      api: 'youzan.salesman.wap.withdraw/1.0.0/apply',
      query,
      success: () => {
        Toast('提现申请成功');
        this.requestAvaliable();
      },

      fail: (err) => {
        Toast(err.message || err.msg || '设置提现账户失败，请重试');
      },

      complete: () => {
        this.toggleWithdrawPop();
        wx.hideToast();
      }
    });
  },

  requestWithdrawAccount() {
    app.carmen({
      api: 'youzan.salesman.wap.withdraw.wallet/1.0.0/get',
      success: (res) => {
        let bindBankCard = false;
        if (Object.keys(res.bank).length) {
          bindBankCard = true;
          this.setYZData({
            bindCardId: res.bank.bindId
          });
          if (!this.data.supportWxWallet) {
            // 绑定了银行卡，且不支持微信钱包，直接显示银行卡
            this.setYZData({
              withdrawAccountName: res.bank.bankName + '尾号' + res.bank.cardNo + res.bank.name
            });
          }
        }
        // 是否绑定银行卡
        this.setYZData({
          bindBankCard
        });
        // 已经选择了提现方式
        if (this.__query__.choose) {
          const withdrawAccountName = this.__query__.choose === 'bank'
            ? res.bank.bankName + '尾号' + res.bank.cardNo + res.bank.name : res.wallet.nickName;
          this.setYZData({
            withdrawAccountName
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: err.message || err.msg || '获取钱包信息失败，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideToast();
      }
    });
  },

  handleInputChange(e) {
    this.setYZData({
      withdrawValue: e.detail.value
    });
  },

  setAccount() {
    if (this.__query__.choose) {
      return wx.redirectTo({
        url: '/packages/groupbuying/my-profit/withdraw/account-choose/index'
      });
    }

    const supportWxWallet = this.data.supportWxWallet;
    const bindBankCard = this.data.bindBankCard;
    if (bindBankCard && !supportWxWallet) {
      // 如果不支持微信钱包 且 绑定了银行卡，actionsheet show
      this.setYZData({
        'actionsheet.show': true
      });
    } else {
      wx.redirectTo({
        url: '/packages/groupbuying/my-profit/withdraw/account-choose/index'
      });
    }
  },

  requestSupportWxWallet() {
    return new Promise((resolve, reject) => {
      app.carmen({
        api: 'youzan.salesman.wap.withdraw.wallet/1.0.0/valid',
        success: (resp) => {
          this.setYZData({
            supportWxWallet: resp.success
          });
          resolve();
        },
        fail: (err) => {
          wx.showToast({
            title: err.message || err.msg || '获取微信钱包接口失败',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  },

  handleZanActionsheetCancel() {
    this.setYZData({
      'actionsheet.show': false
    });
  },

  handleZanActionsheetClick() {
    wx.navigateTo({
      url: '/packages/groupbuying/my-profit/withdraw/account-set/index'
    });
  },

  requestAvaliable() {
    wx.showToast({
      title: '加载中',
      icon: 'loading'
    });
    getHeaderAssetsApi().then(res => {
      wx.hideToast();
      // 设置顶部的数据，一些基本信息
      this.setYZData({
        moneyCanWithdraw: money(res.available).toYuan()
      });
    }).catch(err => {
      wx.showToast({
        title: err.message || err.msg || '获取提现信息失败，请重试',
        icon: 'none'
      });
    });
  }
};

const pageConfig = Object.assign({}, currentPage);

WscPage(pageConfig);
