import WscPage from 'pages/common/wsc-page/index.js';

const app = getApp();

const currentPage = {
  data: {
    // 银行卡账户名称
    bankAccountName: '',
    // 微信账户名称
    wxAccountName: '',
    // 允许微信提现
    supportWxWallet: false,
    actionsheet: {},
    // 当前选择的提现类型
    currentChoosen: 0
  },

  onShow() {
    this.requestSupportWxWallet().then(() => {
      this.requestWithdrawAccount();
    });
  },

  setAccount(e) {
    const actionsheet = {
      show: false,
      actions: [
        {
          name: '',
          className: 'account-name',
          index: 0,
          loading: false
        },
        {
          name: '提现到该账户',
          className: 'withdraw-action',
          index: 1,
          loading: false
        },
        {
          name: '修改账户',
          index: 2,
          loading: false
        }
      ],
      cancelText: '取消'
    };
    const accountType = e.currentTarget.dataset.account;
    const currentChoosen = accountType === 'bank' ? 0 : 1;
    actionsheet.show = true;
    // 提现到银行卡
    if (accountType === 'bank') {
      // 若之前未绑定，直接跳
      if (!this.data.bankAccountName) {
        return wx.redirectTo({
          url: '/packages/groupbuying/my-profit/withdraw/account-set/index?from=choose'
        });
      }
      actionsheet.actions[0].name = this.data.bankAccountName;
      // 提现到微信
    } else {
      if (!this.data.wxAccountName) {
        return this.bindWxAccount();
      }
      actionsheet.actions[0].name = this.data.wxAccountName;
      actionsheet.actions.splice(2, 1);
    }
    this.setYZData({
      actionsheet,
      currentChoosen
    });
  },

  onClose() {
    this.setYZData({
      'actionsheet.show': false
    });
  },

  onSelect({ detail }) {
    const index = detail.index;
    // 跳转至银行卡设置页面
    if (index > 1) {
      wx.navigateTo({
        url: '/packages/groupbuying/my-profit/withdraw/account-set/index'
      });
    } else {
      // 根据currentChosen 来设置提现账户
      const type = this.data.currentChoosen === 0 ? 'bank' : 'wx';
      wx.redirectTo({
        url: '/packages/groupbuying/my-profit/withdraw/withdraw-assets/index?choose=' + type
      });
    }
  },

  requestWithdrawAccount() {
    app.carmen({
      api: 'youzan.salesman.wap.withdraw.wallet/1.0.0/get',
      success: res => {
        // 将已经设置的账户信息设置在这里
        let bankAccountName = '';
        if (Object.keys(res.bank).length) {
          bankAccountName = res.bank.bankName + '尾号' + res.bank.cardNo + res.bank.name;
        }
        this.setYZData({
          bankAccountName,
          wxAccountName: res.wallet.nickName
        });
      },
      fail: err => {
        wx.showToast({
          title: err.message || err.msg || '获取绑定信息失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  bindWxAccount() {
    // 绑定微信钱包
    app.getUserInfo(() => {
      const nickName = app.globalData.userInfo.userInfo.nickName;
      wx.showLoading();
      app.carmen({
        api: 'youzan.salesman.wap.withdraw.wallet/1.0.0/create',
        query: {
          nickName
        },
        success: resp => {
          if (resp.success) {
            wx.showToast({
              title: '绑定成功'
            });
          }
          this.setYZData({
            wxAccountName: nickName
          });
        },
        fail: err => {
          wx.showToast({
            title: err.message || err.msg || '绑定微信钱包失败'
          });
        },
        complete: () => {
          wx.hideLoading();
        }
      });
    });
  },

  requestSupportWxWallet() {
    return new Promise((resolve, reject) => {
      app.carmen({
        api: 'youzan.salesman.wap.withdraw.wallet/1.0.0/valid',
        success: resp => {
          this.setYZData({
            supportWxWallet: resp.success
          });
          resolve();
        },
        fail: err => {
          wx.showToast({
            title: err.message || err.msg || '获取微信钱包接口失败',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  }
};

const pageConfig = Object.assign({}, currentPage);

WscPage(pageConfig);
