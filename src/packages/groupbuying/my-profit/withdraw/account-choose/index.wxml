<view class="account-set">
  <van-cell-group>
    <van-cell
      title-class="flex-cell"
      title="银行卡"
      is-link
      bind:click="setAccount"
      data-account="bank"
    >
      {{ bankAccountName || '去设置' }}
    </van-cell>
  </van-cell-group>

  <van-cell-group custom-class="flex-cell-group" wx:if="{{supportWxWallet}}">
    <van-cell
      title-class="flex-cell"
      title="微信钱包"
      is-link
      bind:click="setAccount"
      data-account="wx"
    >
      {{ wxAccountName || '去设置' }}
    </van-cell>
  </van-cell-group>

  <view class="bottom-tips" wx:if="{{supportWxWallet}}">
    未实名认证的微信用户，将无法提现到微信钱包
  </view>

  <van-action-sheet
    show="{{ actionsheet.show }}"
    actions="{{ actionsheet.actions }}"
    cancel-text="{{ actionsheet.cancelText }}"
    bind:close="onClose"
    bind:select="onSelect"
    bind:cancel="onClose"
  />
</view>
<inject-protocol noAutoAuth />