import { fetchTradeUmp } from '../../common';

// 获取团长信息
function getHeaderInfoApi(query) {
  return fetchTradeUmp('myprofit/getHeaderInfo.json', { query });
}

// 获取团长利润
function getHeaderSettleApi(query) {
  return fetchTradeUmp('myprofit/getHeaderSettle.json', { query });
}

// 获取收益列表
function getProfitListApi(query) {
  return fetchTradeUmp('myprofit/getProfitList.json', { query });
}

// 获取团长可提现余额
function getHeaderAssetsApi() {
  return fetchTradeUmp('myprofit/getLeaderForward.json');
}

// 根据提货码查订单
function getVerifyOrdeByCoderApi(query) {
  return fetchTradeUmp('headerOrder/getVerifyOrderByCode.json', { query });
}

// 根据手机号查订单
function getVerifyOrderByMobileApi(query) {
  return fetchTradeUmp('headerOrder/getVerifyOrderByMobile.json', { query });
}

// 核销
function verifyFetchApi(data) {
  return fetchTradeUmp('headerOrder/verify.json', { data }, 'POST');
}

// 是否升级为零钱通
function querySalemanUpgradeResult() {
  return fetchTradeUmp('myprofit/querySalemanUpgradeResult.json');
}

export {
  getHeaderInfoApi,
  getHeaderSettleApi,
  getProfitListApi,
  getHeaderAssetsApi,
  getVerifyOrdeByCoderApi,
  getVerifyOrderByMobileApi,
  verifyFetchApi,
  querySalemanUpgradeResult,
};
