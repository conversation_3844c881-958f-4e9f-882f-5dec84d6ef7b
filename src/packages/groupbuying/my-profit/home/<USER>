import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import money from '@youzan/weapp-utils/lib/money';
import openWebView from 'utils/open-web-view';
import { getHeaderInfoApi, getHeaderSettleApi, querySalemanUpgradeResult } from '../api';

const initSet = ['HeaderInfo', 'HeaderSettle'];
const app = getApp();

WscPage({
  data: {
    nickName: '',
    avatar: '',
    leaderInfo: '',
    monthSettled: 0,
    totalSettled: 0,
    monthWaiteSettle: 0,
    totalWaitSettle: 0,
    joinTime: '',
    communityHeaderName: '',
    lon: 0,
    lat: 0,
    loading: true,
  },

  onLoad() {
    initSet.forEach(item => {
      this[`get${item}`]();
    });
  },

  onShow() {
    // 每次onShow的时候去拉一次经纬度信息
    getHeaderInfoApi().then(res => {
      Toast.clear();
      const data = mapKeysCase.toCamelCase(res);
      this.setYZData({
        lon: data.lon,
        lat: data.lat,
      });
    }).catch(e => {
      Toast(e.msg);
    });
  },

  getHeaderSettle() {
    Toast.loading();
    getHeaderSettleApi().then(res => {
      Toast.clear();
      const data = mapKeysCase.toCamelCase(res);
      this.setYZData({
        totalSettled: money(data.totalSettled + data.totalWaitSettle).toYuan(),
        totalWaitSettle: money(data.totalWaitSettle).toYuan(),
        monthSettled: money(data.monthSettled + data.monthWaitSettle).toYuan(),
        monthWaiteSettle: money(data.monthWaitSettle).toYuan()
      });
    }).catch(e => {
      Toast(e.msg);
    });
  },

  getHeaderInfo() {
    Toast.loading();
    getHeaderInfoApi().then(res => {
      Toast.clear();
      const data = mapKeysCase.toCamelCase(res);
      this.setYZData({
        joinTime: data.joinTime,
        avatar: data.headerAvatar || 'https://img.yzcdn.cn/image/_avatar.png',
        nickName: data.headerName,
        communityHeaderName: data.communityHeaderName,
        lon: data.lon,
        lat: data.lat,
        loading: false
      });
    }).catch(e => {
      Toast(e.msg);
    });
  },

  onNavigateToWithDraw() {
    querySalemanUpgradeResult().then(data => {
      const { upgraded } = data;
      if (upgraded) {
        openWebView('/wscassets/change/profile', {
          title: '有赞钱包',
          query: {
            kdtId: app.getKdtId()
          }
        });
        return;
      }
      wx.navigateTo({
        url: '/packages/groupbuying/my-profit/withdraw/withdraw-center/index'
      });
    }).catch(err => {
      Toast(err.message || err.msg || '获取提现信息失败，请重试');
    });
  },

  onNavigateToVerify() {
    wx.navigateTo({
      url: '/packages/groupbuying/my-profit/verify/index'
    });
  },

  onNavigateToEdit() {
    wx.navigateTo({
      url: '/packages/groupbuying/header-recruit/message/index?edit=1'
    });
  },

  onProfitClick() {
    const kdtId = app.getKdtId();
    openWebView(`/wsctrade/groupbuying/myprofit?kdt_id=${kdtId}#/profit`, {
      title: '我的利润'
    });
  }
});
