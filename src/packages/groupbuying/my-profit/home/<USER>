page {
  background: #f8f8f8;
}

.my-panel {
  position: relative;

  .personal-info {
    height: 172px;
    display: flex;
    justify-content: flex-start;
    padding: 24px 24px 0;
    background: #07c160;
    box-sizing: border-box;

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 8px;
      box-shadow: 0 0 0px 2px #fff;
    }

    &__desc {
      color: #fff;

      .intro {
        display: flex;
        align-items: center;
        height: 22px;

        .name {
          font-size: 18px;
          font-weight: 500;
        }

        .tag {
          margin-left: 8px;
          display: flex;
          align-items: center;
          padding: 0 4px 0 2px;
          height: 16px;
          background: #242321;
          border-radius: 8px;

          &-icon {
            height: 12px;
            width: 12px;
          }

          &-text {
            margin-left: 2px;
            color: #f2e3c4;
            font-size: 10px;
          }
        }
      }

      .time {
        margin-top: 4px;
        font-size: 13px;
        line-height: 18px;
        color: #fff;
      }
    }
  }

  .profit-panel {
    margin: -84px 16px 0;
    padding: 16px 0;
    background: #fff;
    border-radius: 4px;

    .col {
      border-right: 1px solid #dcdee0;
    }

    .van-col {
      padding-left: 16px;
    }

    .label {
      line-height: 18px;
      font-size: 13px;
      color: #969799;
    }

    .content {
      margin-top: 4px;
      line-height: 24px;
      font-size: 18px;
      font-weight: bold;
    }

    .profit-detail {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 16px;
      margin-top: 8px;
      color: #07c160;
      font-size: 12px;

      .icon {
        top: 1px;
        margin-left: 4px;
        font-size: 10px;
      }
    }
  }

  .van-tabbar-item--active {
    color: #4b0;
  }

  .copyright-container {
    margin-top: 38vh;
  }
}

.address-icon {
  float: right;
  position: relative;
  top: 9px;
  border-radius: 50%;
  width: 6px;
  height: 6px;
  background: #f44;
}

.address-cell {
  margin-top: 12px;
}

.cell-group {
  margin: 12px 16px 0;
}
