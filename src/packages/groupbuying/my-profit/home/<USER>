
<page-container class="{{ themeClass }} page-{{ deviceType }}">
  <view class="my-panel">
    <view class="personal-info">
      <image class="avatar" src="{{ avatar }}"></image>
      <view class="personal-info__desc">
        <view class="intro">
          <text class="name">{{ nickName }}</text>
          <view class="tag">
            <image class="tag-icon" src="https://img.yzcdn.cn/public_files/2019/09/11/1d8eff16296024b47d7b80bf28d28c3b.png" />
            <view class="tag-text">{{ communityHeaderName }}</view>
          </view>
        </view>
        <view class="time">
          加入时间：{{ joinTime }}
        </view>
      </view>
    </view>
    <view class="profit-panel" catchtap="onProfitClick">
      <van-row>
        <van-col custom-class="van-hairline--right" span="12">
          <view class="label">累计利润</view>
          <view class="content">{{ totalSettled }}</view>
        </van-col>
        <van-col span="12">
          <view class="label">待结算</view>
          <view class="content">{{ totalWaitSettle }}</view>
        </van-col>
      </van-row>
      <view class="profit-detail">
        查看利润明细
        <van-icon custom-class="icon" name="arrow" />
      </view>
    </view>
    <view class="cell-group">
      <van-cell
        title="收益提现"
        is-link
        bindtap="onNavigateToWithDraw"
      />
      <van-cell
        title="提货核销"
        is-link
        bindtap="onNavigateToVerify"
      />
      <van-cell
        custom-class="address-cell"
        title="我的地址"
        is-link
        bindtap="onNavigateToEdit"
      >
        <view wx:if="{{ !loading && !lon && !lat }}" class="address-icon" />
      </van-cell>
    </view>
  </view>
</page-container>

<nav active="{{ 2 }}" />

<van-toast id="van-toast" />
