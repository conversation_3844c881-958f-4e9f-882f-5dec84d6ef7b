<scroll-view
  scroll-y
  style="height: 100vh"
  bindscrolltolower="loadMore"
>
  <block>
    <view wx:for="{{ orderList }}"
      wx:for-item="order"
      class="order-item"
      wx:key="{{ index }}"
    >
      <order-item
        state="{{ order.state }}"
        order-time="{{ order.bookTime }}"
        order-no="{{ order.orderNo }}"
        goods-list="{{ order.items }}"
        profit="{{ order.profit }}"
        price="{{ order.payment }}"
        detail="{{ order }}"
        is-buyer="{{ false }}"
        is-verify
      />
    </view>
  </block>
  <zan-loadmore wx:if="{{ loadMore.loading }}" type="loading" />
</scroll-view>
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<inject-protocol noAutoAuth />