import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import money from '@youzan/weapp-utils/lib/money';
import { getVerifyOrdeByCoderApi, getVerifyOrderByMobileApi } from '../api';

WscPage({
  data: {
    orderList: [],
    loadMore: {
      loading: false
    },
    finished: false,
    page: 1
  },

  onLoad(options) {
    const { mobile, verifyCode } = options;
    this.setYZData({
      type: mobile ? 'mobile' : 'verifyCode',
      value: mobile || verifyCode
    });
  },

  onReady() {
    this.loadData();
  },

  getVerifyOrderApi() {
    const { type, value, page } = this.data;
    if (type === 'verifyCode') {
      return getVerifyOrdeByCoderApi({
        verifyCode: value,
        page
      });
    }
    return getVerifyOrderByMobileApi({
      mobile: value,
      page
    });
  },

  loadMore() {
    const { page, loadMore, finished } = this.data;
    if (page === 1 || loadMore.loading || finished) return;
    this.loadData();
  },

  loadData() {
    const { page, orderList } = this.data;
    this.setYZData({
      loadMore: {
        loading: true
      }
    });
    this.getVerifyOrderApi()
      .then(data => {
        let { list } = data;
        list.forEach(order => {
          order.items.forEach(one => {
            const { price = 0, refund = 0 } = one;
            one.priceYuan = money(price).toYuan();
            one.refundYuan = money(refund).toYuan();
          });
        });
        if (list.length < 20) {
          this.setYZData({
            finished: true
          });
        }
        this.setYZData({
          orderList: orderList.concat(mapKeysCase.toCamelCase(list)),
          page: page + 1,
          loadMore: {
            loading: false
          }
        });
      })
      .catch(e => {
        Toast(e.msg);
      });
  }
});
