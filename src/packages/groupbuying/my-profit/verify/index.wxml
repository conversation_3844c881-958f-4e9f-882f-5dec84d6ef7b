<view class="verify-container verify-container--vant-tab"
>
  <van-tabs
    class="verify-type"
    custom-class="groupbuy-verify"
    type="card"
    active="{{ active }}"
    bind:change="onTabChange"
  >
    <van-tab title="提货码" />
    <van-tab title="手机号" />
  </van-tabs>
  <view class="verify-input">
    <van-cell-group>
      <van-field
        value="{{ value }}"
        placeholder="{{ text.placeholder }}"
        bind:change="onChange"
      />
    </van-cell-group>
  </view>
  <view class="confirm-btn">
    <van-button
      type="primary"
      loading="{{ orderLoading }}"
      block="{{ true }}"
      catchtap="getVerifyOrder"
    >
      确认
    </van-button>
  </view>
  <van-toast id="van-toast" />
</view>