import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import { getVerifyOrdeByCoderApi, getVerifyOrderByMobileApi } from '../api';

const text = [
  {
    placeholder: '请输入提货码',
    notFoundText: '此提货码不存在',
    notNullText: '提货码不能为空'
  },
  {
    placeholder: '请输入手机号',
    notFoundText: '此手机号未查到订单',
    notNullText: '手机号不能为空'
  }
];

WscPage({
  data: {
    active: 0,
    value: '',
    text: {},
    orderLoading: false
  },

  onLoad() {
    const { active } = this.data;
    this.setYZData({
      text: text[active]
    });
  },

  onChange(e) {
    this.setYZData({
      value: e.detail
    });
  },

  onTabChange(e) {
    const { index: active } = e.detail;
    this.setYZData({
      active,
      value: '',
      text: text[active]
    });
  },

  checkNotNull() {
    const { text, value } = this.data;
    if (value === '') {
      Toast(text.notNullText);
      return false;
    }
    return true;
  },

  getVerifyOrder() {
    this.getVerifyOrderApi()
      .then(this.resolveVerifyOrder)
      .catch(e => {
        Toast(e.msg);
        this.setYZData({
          orderLoading: false
        });
      });
  },

  getVerifyOrderApi() {
    const { active, value } = this.data;
    if (!this.checkNotNull()) return;
    this.setYZData({
      orderLoading: true
    });
    if (active === 0) {
      return getVerifyOrdeByCoderApi({
        verifyCode: value
      });
    }
    return getVerifyOrderByMobileApi({
      mobile: value
    });
  },
  resolveVerifyOrder(data) {
    const { text, active, value } = this.data;
    const { list = [] } = data;
    this.setYZData({
      orderLoading: false
    });
    if (list.length > 0) {
      let query = `verifyCode=${value}`;
      if (active === 1) {
        query = `mobile=${value}`;
      }
      wx.navigateTo({
        url: `/packages/groupbuying/my-profit/verify-list/index?${query}`
      });
    } else {
      Toast(text.notFoundText);
    }
  }
});
