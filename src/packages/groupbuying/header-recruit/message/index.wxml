<page-container class="{{ themeClass }} page-{{ deviceType }}">
  <!-- 没有经纬度信息&已经加载过团长信息&编辑状态下 -->
  <notice bind:click="onCommunityTap" wx:if="{{ isEdit && loadAddress && !lon && !lat }}" />
  <view class="leader-msg">
    <view class="msg-container">
      <view class="leader-msg__title">
        {{ title }}
      </view>
      <van-cell-group class="leader-msg__field">
        <van-field
          value="{{ proxyName }}"
          label="团长名称"
          placeholder="请填写团长姓名或昵称"
          bindchange="onProxyNameChange"
        />
        <view class="area">
          <van-field
            value="{{ region }}"
            label="所在地区"
            placeholder="请选择所在地区"
            readonly
            is-link
            catchtap="showRegionSelect"
          />
        </view>
        <view catchtap="onCommunityTap">
          <van-field
            value="{{ community }}"
            label="小区名称"
            placeholder="请填写小区名称"
            readonly
            is-link
          />
        </view>
        <van-field
          value="{{ fetchAddress }}"
          label="提货地址"
          placeholder="请填写提货地址"
          bindchange="onFetchAddressChange"
        />
      </van-cell-group>
      <view class="leader-msg__btn">
        <van-button
          type="primary"
          block
          bind:click="submit"
          disabled="{{!isShowBtn}}"
        >{{ isEdit ? '保存' : '下一步' }}</van-button>
      </view>
    </view>
    <van-popup
      show="{{ showArea }}"
      position="bottom"
      bind:click-overlay="hideRegionSelect"
    >
      <van-area
        code="{{ areaCode }}"
        area-list="{{ areaList }}"
        bind:confirm="onAreaConfirm"
        bind:cancel="hideRegionSelect"
      />
    </van-popup>
    <van-toast id="van-toast" />
  </view>
</page-container>
