import WscPage from 'pages/common/wsc-page/index';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import Toast from '@vant/weapp/dist/toast/toast';
import { subscribeMessage } from 'helpers/subscribe';
import { transformToBaidu, baiduToGcj } from '../../common';
import {
  getDefaultAddressApi,
  postApplyMessageApi,
  getSettingApi,
  updateHeaderInfoApi,
  getAllRegion,
} from '../api';

WscPage({
  data: {
    title: '团长信息(用于商品配送、管理等，请填写真实信息)',
    proxyName: '',
    region: [],
    province: '',
    city: '',
    county: '',
    community: '',
    areaCode: '',
    fetchAddress: '',
    // 经纬度
    lon: 0,
    lat: 0,
    joinFee: 0,
    // 是否是编辑状态
    isEdit: false,
    loadAddress: false,
    // 地区数据
    showArea: false,
    isShowBtn: false,
    areaList: {},
  },

  onLoad(query) {
    const { edit } = query;
    if (+edit === 1) {
      this.setYZData({
        isEdit: true
      });
    }
    this.getDefaultAddress();
    this.getSetting();
    this.fetchRegion();
    this.initBtn();
  },

  initBtn() {
    const { proxyName = '', community = '', fetchAddress = '', lon, lat, region } = this.data;
    if (proxyName.trim() && fetchAddress.trim() && community && lon && lat && region) {
      this.setYZData({
        isShowBtn: true
      });
    } else {
      this.setYZData({
        isShowBtn: false
      });
    }
  },

  onAreaConfirm(e) {
    this.setYZData({
      ...e.detail,
      showArea: false,
    });
    this.initBtn();
  },

  fetchRegion() {
    getAllRegion().then((data) => {
      this.setYZData({
        areaList: data,
      });
    });
  },

  showRegionSelect() {
    this.setYZData({
      showArea: true
    });
  },

  hideRegionSelect() {
    this.setYZData({
      showArea: false
    });
    this.initBtn();
  },

  getDefaultAddress() {
    Toast.loading();
    getDefaultAddressApi()
      .then((res) => {
        Toast.clear();
        if (!res.province) return;
        this.initAddress(mapKeysCase.toCamelCase(res));
      })
      .catch((e) => {
        Toast(e.msg);
      });
  },

  getSetting() {
    getSettingApi()
      .then((data) => {
        const { joinFee } = data;
        if (joinFee > 0) {
          this.setYZData({
            joinFee
          });
        }
      })
      .catch((e) => {
        Toast(e.msg);
      });
  },

  initAddress(data) {
    const hasLonLat = data.lon !== '' || data.lat !== '';
    const setDataWithLonLatCheck = (data) => (hasLonLat ? data : '');
    this.setYZData({
      ...baiduToGcj(data.lon, data.lat),
      proxyName: data.proxyName,
      community: setDataWithLonLatCheck(data.community),
      areaCode: data.areaCode,
      fetchAddress: setDataWithLonLatCheck(data.addressDetail),
      region: setDataWithLonLatCheck([data.province, data.city, data.county]),
      province: data.province,
      city: data.city,
      county: data.county,
      loadAddress: true
    });
  },

  onProxyNameChange(e) {
    this.setYZData({
      proxyName: e.detail
    });
    this.initBtn();
  },

  onCommunityChange(e) {
    this.setYZData({
      community: e.detail
    });
  },

  onFetchAddressChange(e) {
    this.setYZData({
      fetchAddress: e.detail
    });
    this.initBtn();
  },

  onCommunityTap() {
    wx.chooseLocation({
      success: (data) => {
        const { name, latitude: lat, longitude: lon } = data;
        this.setYZData({
          lon,
          lat,
          community: name
        });
        this.initBtn();
      },
      fail() {
        Toast('位置信息获取失败');
      }
    });
  },

  validate() {
    const { proxyName = '', community = '', fetchAddress = '', lon, lat } = this.data;
    if (proxyName == null || proxyName.trim() === '') {
      Toast('请填写团长名称');
      return false;
    }
    if (proxyName.length > 15) {
      Toast('团长名称不能超过15个字');
      return false;
    }

    if (!community || !lat || !lon) {
      Toast('需要从地图上选择小区');
      return false;
    }

    if (fetchAddress == null || fetchAddress.trim() === '') {
      Toast('请填写提货地址');
      return false;
    }

    if (fetchAddress.length > 200) {
      Toast('收货地址不能超过200个字');
      return false;
    }

    return true;
  },

  subscribeMessage() {
    subscribeMessage({
      scene: 'groupbuyHeaderApply',
      successCallBack: () => {
        Toast('订阅成功');
      }
    });
  },

  submit() {
    if (this.isShowBtn) return;
    if (!this.validate()) return;

    const postData = {
      ...transformToBaidu(this.data.lon, this.data.lat),
      name: this.data.proxyName,
      community: this.data.community,
      province: this.data.province,
      city: this.data.city,
      county: this.data.county,
      areaCode: this.data.areaCode,
      fetchAddress: this.data.fetchAddress
    };

    if (this.data.isEdit) {
      Toast.loading();
      // 团长更新信息
      postData.addressDetail = postData.fetchAddress;
      delete postData.fetchAddress;
      updateHeaderInfoApi(postData)
        .then((res) => {
          if (res) {
            wx.navigateBack({
              delta: 1
            });
            return;
          }
          Toast('团长信息更新失败');
        })
        .catch((e) => {
          Toast(e.msg);
        });
      return;
    }

    this.subscribeMessage();

    // 团长申请，先询问是否要订阅消息，再申请
    postApplyMessageApi(postData)
      .then(() => {
        Toast.clear();
        const { joinFee } = this.data;
        wx.navigateTo({
          url:
            joinFee > 0
              ? '/packages/groupbuying/header-recruit/join-fee/index'
              : '/packages/groupbuying/header-recruit/result/index'
        });
      })
      .catch((e) => {
        if (e.code === 100008) {
          wx.navigateTo({
            url: '/packages/groupbuying/header-recruit/join-fee/index'
          });
          return;
        }
        Toast(e.msg);
      });
  }
});
