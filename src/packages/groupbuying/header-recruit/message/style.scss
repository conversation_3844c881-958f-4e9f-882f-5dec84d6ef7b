.leader-msg {
  min-height: 100vh;
  background: #f8f8f8;

  .msg-container {
    min-height: 85vh;
  }

  &__title {
    font-size: 12px;
    color: #999;
    padding: 15px 0 10px 15px;
  }


  .area {
    position: relative;

    .icon {
      position: absolute;
      top: 50%;
      right: 15px;
      transform: translateY(-50%);
      font-size: 14px;
    }

    .picker {
      width: 80%;
      height: 100%;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 10;
    }
  }

  &__btn {
    margin: 20px 20px 0;
  }

  .field-disable {
    color: #333;
  }
}
