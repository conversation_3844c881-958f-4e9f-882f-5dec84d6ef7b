import WscPage from 'pages/common/wsc-page/index';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import Toast from '@vant/weapp/dist/toast/toast';
import { getHeaderStateApi, getAfterSaleContactApi } from '../api';

const initSet = ['HeaderState', 'ContactMobile'];

WscPage({
  data: {
    isJoined: false,
    tel: ''
  },

  onLoad() {
    initSet.forEach(item => {
      this[`init${item}`]();
    });
  },

  initContactMobile() {
    getAfterSaleContactApi().then(res => {
      if (!res.phoneNumber) return;
      const phoneNumber = res.areaCode + res.phoneNumber;
      this.setYZData({
        tel: phoneNumber
      });
    });
  },

  // 获取团长审核是否设置
  initHeaderState() {
    Toast.loading();
    getHeaderStateApi().then(res => {
      Toast.clear();
      wx.stopPullDownRefresh();
      const data = mapKeysCase.toCamelCase(res);
      // 被清退的重定向到招募首页
      if (data.fireState === 0) {
        return wx.redirectTo({
          url: '/packages/groupbuying/header-recruit/home/<USER>'
        });
      }
      this.setYZData({
        isJoined: data.reviewState === 0
      });
    }).catch(e => {
      Toast(e.msg);
    });
  },

  callPhone() {
    wx.makePhoneCall({
      phoneNumber: this.data.tel
    });
  },

  goHeaderHome() {
    wx.navigateTo({
      url: '/packages/groupbuying/activity/list/index'
    });
  },

  onPullDownRefresh() {
    initSet.forEach(item => {
      this[`init${item}`]();
    });
  }
});
