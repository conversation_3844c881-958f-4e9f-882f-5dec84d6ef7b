<page-container forbid-copyright="{{ !isJoined }}" class="{{ themeClass }} page-{{ deviceType }}">
  <view class="joined-container">
    <view class="joined-container__result, {{ !isJoined ? 'isWait' : ''}}">
      <block wx:if="{{ isJoined }}">
        <image src="https://img.yzcdn.cn/image/icon_done1.png"></image>
        <view class="toast">恭喜你，已成为团长</view>
      </block>
      <block wx:else>
        <image src="https://img.yzcdn.cn/image/icon_ing1.png"></image>
        <view class="text">提交成功，请耐心等待</view>
        <view class="message">审核通过后会有短信通知</view>
        <view class="contact-bussiness">
          如有疑问，请直接 <text class="call" bindtap="callPhone">联系商家</text>
        </view>
      </block>
    </view>
    <vm-button
      wx:if="{{ isJoined }}"
      class="joined-container__btn"
      bindtap:vm="goHeaderHome"
    >
      进入团长首页
    </vm-button>
    <van-toast id="van-toast" />
  </view>
</page-container>
