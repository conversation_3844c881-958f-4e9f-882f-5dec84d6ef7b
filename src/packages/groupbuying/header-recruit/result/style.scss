.joined-container {
  background: #f8f8f8;
  min-height: 100vh;

  &__result {
    width: 100%;
    height: 151px;
    color: #333;
    font-size: 18px;
    background: #fff;
    margin-bottom: 20px;
    text-align: center;

    image {
      width: 60px;
      height: 60px;
      margin: 30px 0 15px;
    }
  }

  &__btn {
    width: 92vw;
    margin: 0 auto 50vh;
    display: block;
  }

  .isWait {
    position: absolute;
    height: 100vh;
    margin: 0;
    z-index: 10;

    .message {
      color: #999;
      font-size: 12px;
      margin-top: 10px;
    }

    .contact-bussiness {
      color: #999;
      font-size: 14px;
      text-align: center;
      position: absolute;
      width: 100%;
      bottom: 30px;

      .call {
        color: #38f;
      }
    }
  }
}
