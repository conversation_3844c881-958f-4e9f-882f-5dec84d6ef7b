import { fetchUmp } from '../../common';

const app = getApp();
const http = app.request;
const tradeBase = '/pay/wsctrade/groupbuying';

let areaData = null;

// 获取团长审核状态
function getHeaderStateApi() {
  return fetchUmp('recruit/getHeaderState.json');
}

// 获取招募文案
function getCopywritingApi() {
  return fetchUmp('recruit/getCopywriting.json');
}

// 获取团长默认地址
function getDefaultAddressApi() {
  return fetchUmp('recruit/getDefaultAddress.json');
}

// 提交团长信息
function postApplyMessageApi(data) {
  return fetchUmp('recruit/postApplyMessage.json', { data }, 'POST');
}

// 获取商家客服电话
function getAfterSaleContactApi() {
  return fetchUmp('recruit/getAfterSaleContact.json');
}

// 门槛缴费设置获取
function getSettingApi() {
  return fetchUmp('recruit/setting.json');
}

// 门槛缴费
function payFeeApi(data) {
  return http({
    path: `${tradeBase}/joinFee/payFee.json`,
    origin: 'cashier',
    method: 'POST',
    data
  });
}

// 更细团长信息
function updateHeaderInfoApi(data) {
  return fetchUmp('recruit/updateHeaderInfo.json', { data }, 'POST');
}

function applyCheck() {
  return fetchUmp('recruit/applyCheck.json');
}

// 获取所有的省市区
function getAllRegion() {
  let resolve = null;
  if (areaData) {
    return Promise.resolve(areaData);
  }
  const p = new Promise((r) => (resolve = r));
  http({
    path: '/wsctrade/uic/address/getAllRegion.json',
    config: {
      skipKdtId: true,
      skipShopInfo: true,
    },
  }).then((data) => {
    areaData = data;
    resolve(areaData);
  });
  return p;
}

export {
  getHeaderStateApi,
  getCopywritingApi,
  getDefaultAddressApi,
  postApplyMessageApi,
  getAfterSaleContactApi,
  getSettingApi,
  payFeeApi,
  updateHeaderInfoApi,
  applyCheck,
  getAllRegion,
};
