<import src="../../components/pay/cashier/index.wxml"/>
<template is="$cashier" data="{{ $cashier }}" />
<view hidden="{{ loading }}">
  <view class="fee-display">
    <view class="header">
      <image
        class="icon"
        src="https://img.yzcdn.cn/public_files/2018/12/18/667157517ae38df2e53a4630179e035e.png"
      />
      <view class="title">{{ feeName }}</view>
      <view class="desc">商家设置了加入门槛，你需要支付相应费用</view>
    </view>
    <view class="fee">
      <text class="fee-icon">¥</text>
      <text class="fee-num">{{ joinFee }}</text>
    </view>
  </view>
  <view class="confirm">
    <van-button
      custom-class="confirm-btn"
      type="primary"
      size="large"
      block="{{ true }}"
      bind:click="payJoinFee"
    >
      确认支付 ¥ {{ joinFee }}
    </van-button>
  </view>
  <van-toast id="van-toast" />
  <zan-pay bind:init="initPay" bind:paysuccess="redirectToSuccess" bind:navigate="onCashierNavi" bind:cashierclose="cashierCancelOrFail" />
</view>
<inject-protocol noAutoAuth />