import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import money from '@youzan/weapp-utils/lib/money';
import { getSettingApi, payFeeApi } from '../api';

const app = getApp();

WscPage({
  data: {
    feeName: '',
    joinFee: '',
    loading: true,
    // 唤起收银台map数据
    $cashier: {
      actions: [],
      show: false,
      componentId: 'cashier'
    },
    payParams: {},
    orderNo: ''
  },

  onLoad() {
    Toast.loading();
    // 获取设置的门槛
    getSettingApi()
      .then(data => {
        Toast.clear();
        let { feeName, joinFee } = data;
        joinFee = money(joinFee).toYuan();
        this.setYZData({
          feeName,
          joinFee,
          loading: false
        });
      })
      .catch(e => {
        Toast(e.msg);
      });
  },

  payJoinFee() {
    // 支付门槛费用
    payFeeApi().then(data => {
      this.setYZData({
        payParams: data
      });
      if (this.payService) {
        const { orderNo, cashierPayResult } = this.data.payParams;
        const { cashierSalt, cashierSign, partnerId, prepayId, bizExt } = cashierPayResult;

        let tradeBizExt = {};
        try {
          tradeBizExt = JSON.parse(bizExt || '{}');
        } catch (error) {
          console.error('团长招募业务扩展参数解析失败', error);
        } finally {
          tradeBizExt.appId = app.getAppId();
        }

        this.payService.startPay({
          orderNo,
          cashierSalt,
          cashierSign,
          partnerId,
          prepayId,
          tradeBizExt,
        });
      }
    }).catch(e => {
      Toast(e.msg);
    });
  },

  cashierCancelOrFail() {
    this.setYZData({
      loading: false
    });
  },

  // 支付完成跳转
  redirectToSuccess() {
    wx.redirectTo({
      url: '/packages/groupbuying/header-recruit/result/index'
    });
  },

  async initPay({ detail }) {
    const { default: PayService } = await import('@youzan/zan-pay-weapp');
    this.payService = new PayService({
      toast: wx.showToast,
      clear: wx.hideToast,
      request: app.request,
      biz: 'group_leader_join_fee',
      quickMode: true,
    });
    const crypto = await import('@youzan/crypto');
    this.payService.init(detail, crypto);
  },

  onCashierNavi({ detail: destination }) {
    wx.navigateTo({
      url: `/pages/common/webview-page/index?src=${destination.url}&title=${destination.title}`,
    });
  },
});
