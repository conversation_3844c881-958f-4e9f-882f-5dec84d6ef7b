import WscPage from 'pages/common/wsc-page/index';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import Toast from '@vant/weapp/dist/toast/toast';
import { getCopywritingApi, getHeaderStateApi, applyCheck } from '../api';
import { isNewIphone } from 'shared/utils/browser/device-type';

const app = getApp();
const initSet = ['HeaderState', 'Copywritting'];

WscPage({
  data: {
    content: '',
    margin: 0,
    backgroundColor: '#f8f8f8',
    isJoined: false,
    showAccount: false,
    height: 56,
    bottom: 8,
  },

  onLoad(options) {
    const { kdtId, guestKdtId } = options;
    if (kdtId !== guestKdtId && guestKdtId.trim() !== '') {
      app.updateKdtId(guestKdtId, false, {
        mark: '907',
      });
    }

    initSet.forEach((item) => {
      this[`init${item}`]();
    });
    this.setYZData({
      isBindYouzan: !!app.getBuyerId(),
    });

    if (isNewIphone()) {
      this.setYZData({
        height: 90,
        bottom: 42,
      });
    }
  },

  initHeaderState() {
    Toast.loading();
    Promise.all([getHeaderStateApi(), applyCheck()])
      .then(([header, apply]) => {
        Toast.clear();
        header = mapKeysCase.toCamelCase(header);
        const { checkCode, reason } = apply;
        if (
          ((!header.id && !header.value) || header.reviewState === 1) &&
          checkCode !== 0
        ) {
          this.goChainPage(reason);
          return;
        }
        if (
          header &&
          header.fireState === 1 &&
          (header.reviewState === 0 || header.reviewState === 1)
        ) {
          this.setYZData({
            isJoined: true,
          });
          this.goResultPage();
        }
      })
      .catch((e) => {
        Toast(e.msg);
      });
  },

  initCopywritting() {
    Toast.loading();
    getCopywritingApi()
      .then((res) => {
        Toast.clear();
        const { content, margin, backgroundColor } =
          mapKeysCase.toCamelCase(res);
        this.setYZData({
          content,
          margin,
          backgroundColor,
        });
        wx.setNavigationBarTitle({
          title: res.title,
        });
      })
      .catch((e) => {
        Toast(e.msg);
      });
  },

  onTap() {
    const isBindYouzan = Boolean(app.getBuyerId());
    if (!isBindYouzan) {
      // 没有绑定有赞手机号
      this.setYZData({ showAccount: true });
    } else if (this.data.isJoined) {
      this.goResultPage();
    } else {
      this.goMsgPage();
    }
  },

  goMsgPage() {
    wx.navigateTo({
      url: '/packages/groupbuying/header-recruit/message/index',
    });
  },

  goResultPage() {
    wx.navigateTo({
      url: '/packages/groupbuying/header-recruit/result/index',
    });
  },

  goChainPage(reason) {
    wx.redirectTo({
      url: `/packages/groupbuying/header-recruit/chain/index?reason=${reason}`,
    });
  },

  onZanAccountBinded() {
    wx.redirectTo({
      url: '/packages/groupbuying/header-recruit/home/<USER>',
    });
  },

  loginSuccess() {
    initSet.forEach((item) => {
      this[`init${item}`]();
    });
    this.setYZData({
      isBindYouzan: !!app.getBuyerId(),
      showAccount: false,
    });
  },

  closeAccountLogin() {
    this.setYZData({ showAccount: false });
  },
});
