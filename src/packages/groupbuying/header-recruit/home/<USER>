<page-container
  page-bg-color="{{ backgroundColor }}"
  fixed-bottom
>
  <view class="recruit-index">
    <view class="recruit-content" style="padding: {{ margin }}px">
      <yz-rich-text html="{{ content }}"></yz-rich-text>
    </view>
    <view wx:if="{{ showAccount}}">
      <account-login
        bind:closeAccountLogin="closeAccountLogin"
        bind:loginSuccess="loginSuccess"
        show="{{ showAccount }}"
      ></account-login>
    </view>
    <view class="button-box" style="height: {{ height }}px">
      <view
        class="button"
        bindtap="onTap"
        style="bottom: {{ bottom }}px"
      >
        成为团长
      </view>
    </view>
    
    <van-toast id="van-toast" />
  </view>
</page-container>
