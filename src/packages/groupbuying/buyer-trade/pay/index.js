import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import isPhone from '@youzan/weapp-utils/lib/validators/is-phone';
import money from '@youzan/weapp-utils/lib/money';
import get from '@youzan/weapp-utils/lib/get';
import { isNewIphone } from 'shared/utils/browser/device-type';
import { getSkuName } from '../../common';
import {
  prepare,
  createTradeApi,
  getHeaderById,
  getPhone,
  getGroupbuySettings,
} from '../api';

const app = getApp();

WscPage({
  data: {
    mobile: '',
    buyerMsg: '',
    payParams: {},
    shopName: '',
    headerInfo: {},
    displayItems: [],
    realPay: 0,
    disabled: false,
    loading: true,
    error: false,
    isIphoneX: false,
    // 优惠券相关
    coupon: {},
    showCoupons: false,
    enableCoupons: [],
    disableCoupons: [],
    groupSettings: {},
    errorMessage: {
      buyerMsg: '',
    },
  },

  onMobileChange(e) {
    this.setYZData({
      mobile: e.detail,
    });
  },

  onBuyerMsgChange(e) {
    this.setYZData({
      buyerMsg: e.detail,
    });
    this.updateValid();
  },
  updateValid() {
    let buyerMsgErr = '';
    const { buyerMsg, errorMessage, groupSettings } = this.data;
    // 没有配置或者是false，则不执行
    if (
      typeof groupSettings.buyerCommentsRequired !== 'boolean' ||
      !groupSettings.buyerCommentsRequired
    ) {
      return;
    }
    if (!buyerMsg.trim()) {
      buyerMsgErr = '请输入备注';
    }
    this.setYZData({
      errorMessage: {
        ...errorMessage,
        buyerMsg: buyerMsgErr,
      },
    });
  },
  checkFormValid() {
    const { buyerMsg, errorMessage, groupSettings } = this.data;
    // 没有配置或者是false，则不校验
    if (
      typeof groupSettings.buyerCommentsRequired !== 'boolean' ||
      !groupSettings.buyerCommentsRequired
    ) {
      return true;
    }
    if (!buyerMsg.trim()) {
      this.setYZData({
        errorMessage: {
          ...errorMessage,
          buyerMsg: '请输入备注',
        },
      });
      return false;
    }
    return true;
  },

  fetchGroupSettings() {
    getGroupbuySettings().then((res) => {
      this.setYZData({
        groupSettings: res || {},
      });
    });
  },

  onLoad(options) {
    const isIphoneX = isNewIphone();
    if (isIphoneX) {
      this.setYZData({
        isIphoneX: true,
      });
    }

    /* eslint-disable */
    const { bookKey, activityId, alias, headerBuyerId } = options;
    Toast.loading();

    // 记录参数
    this.activityId = activityId;
    this.alias = alias;
    this.headerBuyerId = headerBuyerId;

    this.fetchDefaultMobile();
    this.fetchGroupSettings();

    Promise.all([
      this.fetchOrder(bookKey),
      this.fetchShopName(),
      this.fetchHeaderInfo(headerBuyerId),
    ])
      .then(([orderParams, shopName, headerInfo]) => {
        const { forbidPay, realPay, disableCoupons, enableCoupons } =
          orderParams;
        let finalPrice = realPay;
        this.items = get(orderParams, 'orderCreateParams.items', []);

        // 处理优惠券
        let coupon = {};
        let enableCouponsLen = enableCoupons.length;
        let disableCouponsLen = disableCoupons.length;
        let showCoupons = enableCouponsLen || disableCouponsLen;

        if (enableCouponsLen > 0) {
          // 第一张优惠券是优惠金额最大的
          coupon = enableCoupons[0];
          finalPrice = realPay - coupon.value;
        }

        Toast.clear();

        if (!this.items || this.items.length === 0 || forbidPay) {
          this.setYZData({
            error: true,
          });
          return;
        }

        const displayItems = get(orderParams, 'displayItems', []);
        displayItems.forEach((one) => {
          const { goodsId, skuId = '' } = one;
          one.id = `${goodsId}${skuId}`;
          one.cdnImage = cdnImage(one.imgUrl, '!80x80.png');
          one.priceForYuan =
            one.activityId !== 0
              ? money(one.payPrice).toYuan()
              : money(one.price).toYuan();
          one.skuName = getSkuName(one.sku);
        });
        this.setYZData({
          shopName,
          headerInfo: mapKeysCase.toCamelCase(headerInfo),
          displayItems,
          // 用来存储原价（未优惠之前的价格）
          realPay: money(realPay).toYuan(),
          loading: false,
          enableCoupons,
          disableCoupons,
          coupon,
          showCoupons: Boolean(showCoupons),
          // 最终付款的钱（可能会有优惠券的钱）
          finalPrice: money(finalPrice).toYuan(),
        });
      })
      .catch(() => {
        Toast('订单信息获取失败');
      });
  },

  fetchOrder(bookKey) {
    return prepare({
      bookKey,
    }).then((data = {}) => {
      const { orderCreateDTO = {} } = data;
      const enableCoupons = get(data, 'coupons', []);
      const disableCoupons = get(data, 'unavailableCoupons', []);
      return {
        orderCreateParams: orderCreateDTO,
        forbidPay: get(data, 'displayConfig.forbidPay', true),
        realPay: get(data, 'orderPayment.realPay', 0),
        displayItems: get(data, 'shopItem.orderItems', []),
        enableCoupons,
        disableCoupons,
      };
    });
  },

  fetchShopName() {
    return app.getShopInfo().then((info) => {
      return get(info, 'shop_name', '');
    });
  },

  fetchHeaderInfo(headerBuyerId) {
    return getHeaderById(headerBuyerId);
  },

  fetchDefaultMobile() {
    getPhone().then((mobile) => {
      this.setYZData({
        mobile: mobile.value,
      });
    });
  },

  pay(e) {
    const formId = get(e, 'detail.formId');
    const { buyerMsg, mobile, coupon } = this.data;
    if (!mobile || !isPhone(mobile)) {
      Toast('请填写有效手机号');
      return;
    }
    if (buyerMsg.length > 200) {
      Toast('备注不能超过200字');
      return;
    }
    if (!this.checkFormValid()) {
      return;
    }
    Toast.loading();
    const params = {
      alias: this.alias,
      activityId: this.activityId,
      headerBuyerId: this.headerBuyerId,
      items: this.items,
      mobile,
      buyerMsg,
      orderMark: 'wx_shop',
    };
    if (coupon.id) {
      params.ump = {
        coupon: {
          couponType: coupon.type,
          id: coupon.id,
          kdtId: app.getKdtId(),
        },
      };
    }
    if (formId) {
      params.formId = formId;
    }
    createTradeApi(params)
      .then((data) => {
        data = mapKeysCase.toCamelCase(data);
        const payAmount = get(data, 'paymentPreparation.payAmount');
        const { zeroOrder } = data;
        if (!this.orderNo) {
          this.orderNo = data.orderNo;
        }
        if (payAmount === 0 || zeroOrder) {
          wx.redirectTo({
            url: `/packages/order/paid/index?order_no=${this.orderNo}`,
          });
        } else {
          this.navigateToOrderDetail(this.orderNo);
        }
      })
      .catch((e) => {
        Toast(e.msg);
      });
    this.logTrade();
  },

  onCouponSelect(e) {
    const { realPay } = this.data;
    const coupon = e.detail;
    const couponPrice = coupon.value || 0;
    const finalPrice = money(realPay).toCent() - couponPrice;
    this.setYZData({
      coupon,
      finalPrice: money(finalPrice).toYuan(),
    });
  },

  navigateToOrderDetail(orderNo) {
    wx.redirectTo({
      url: `/packages/groupbuying/buyer-trade/detail/index?orderNo=${orderNo}&fromBuy=1`,
    });
  },

  logTrade() {
    setTimeout(() => {
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'cgroupon_order',
          en: '确认下单',
          params: {
            act_type: 10002,
            act_id: `${this.activityId}${this.headerBuyerId}`,
            agent_id: this.headerBuyerId,
            groupbuy_act_id: this.activityId,
          },
          si: app.getKdtId(),
        });
    }, 0);
  },
});
