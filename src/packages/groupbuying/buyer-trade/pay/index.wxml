<wxs module="computed" src="./computed.wxs" />
<view class="pay-page {{ isIphoneX ? 'iphone-x' : '' }}">
  <view hidden="{{ loading }}">
    <view class="header">
      <image
        class="avatar"
        src="{{ headerInfo.headerAvatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}"
      />
      <view class="info">
        <view class="name">团长: {{ headerInfo.headerName }}</view>
        <view class="address">提货地址: {{ headerInfo.fetchAddress }}</view>
      </view>
      <view class="border" />
    </view>
    <view class="detail">
      <view hidden="{{ !shopName }}" class="shop">
        <van-icon custom-class="icon" name="shop-o" />
        {{ shopName }}
      </view>
      <view class="list">
        <view wx:for="{{ displayItems }}" wx:key="index" class="item">
          <item detail="{{ item }}" />
        </view>
      </view>
      <view class="total">
        <view class="label">
          <view>合计: </view>
          <price price="{{ realPay }}" />
        </view>
        <view class="num"></view>
      </view>
    </view>
    <view hidden="{{ !showCoupons }}" class="ump">
      <coupon-cell
        coupon="{{ coupon }}"
        enable-coupons="{{ enableCoupons }}"
        disable-coupons="{{ disableCoupons }}"
        bind:select="onCouponSelect"
      />
    </view>
    <view class="other">
      <van-field
        label="手机号"
        placeholder="提货时需要用到"
        value="{{ mobile }}"
        bind:change="onMobileChange"
      />
      <van-field
        label="备注"
        placeholder="{{groupSettings.buyerCommentsTip || '建议和团长协商后填写'}}"
        value="{{ buyerMsg }}"
        bind:change="onBuyerMsgChange"
        error-message="{{errorMessage.buyerMsg}}"
      />
    </view>
    <view class="result">
      <view class="result-item">
        <view class="result-item-title">商品金额</view>
        <view class="result-item-value">¥ {{ realPay }}</view>
      </view>
      <view class="result-item">
        <view class="result-item-title">优惠券</view>
        <view class="result-item-value">-¥ {{ computed.toYuan(coupon.value) }}</view>
      </view>
    </view>
    <view class="result-total">
      <view class="result-total-text">
        <view>订单合计：</view>
        <price price="{{ finalPrice }}" />
      </view>
    </view>
    <view class="submit {{ isIphoneX ? 'submit-iphonex' : '' }}">
      <view class="total">
        <view class="label">
          <view>合计: </view>
          <price price="{{ finalPrice }}" />
        </view>
      </view>
      <form
        bindsubmit="pay" 
        report-submit="true"
      >
        <button class="pay-form-btn" form-type="submit">
          <van-button
            custom-class="btn"
            type="primary"
            round
          >
            提交订单
          </van-button>
        </button>
      </form>
    </view>
  </view>
  <van-toast id="van-toast" />
</view>
<inject-protocol noAutoAuth />