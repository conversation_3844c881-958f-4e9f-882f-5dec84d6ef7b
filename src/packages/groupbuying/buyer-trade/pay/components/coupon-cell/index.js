import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();
const eventKey = 'communitybuy-coupon-change';

WscComponent({
  properties: {
    coupon: {
      type: Object
    },
    enableCoupons: {
      type: Array
    },
    disableCoupons: {
      type: Array
    }
  },
  ready() {
    // 订阅选择优惠券
    this.on(
      eventKey,
      (coupon = {}) => {
        coupon = mapKeysCase.toCamelCase(coupon);
        this.triggerEvent('select', coupon);
      }
    );
  },
  methods: {
    // 点击跳转到优惠券列表
    showCouponList() {
      const {
        enableCoupons,
        disableCoupons,
        coupon
      } = this.data;

      const params = {
        charge_coupon: enableCoupons.slice(),
        unavailable_coupon: disableCoupons.slice(),
        selected_coupon: coupon,
        exchangeParams: {}
      };

      const dbid = app.db.set({ ...params });
      wx.navigateTo({
        url: '/packages/groupbuying/buyer-trade/coupon/index?dbid=' + dbid
      });
    }
  }
});
