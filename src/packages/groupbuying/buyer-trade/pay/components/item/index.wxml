<view class="item">
  <image src="{{ detail.cdnImage }}" />
  <view class="info">
    <view class="title">{{ detail.title }}</view>
    <view class="sku-name">{{ detail.skuName }}</view>
    <view class="other">
      <view class="price">
        <price
          price="{{ detail.priceForYuan }}"
          originPrice="{{detail.payPrice == detail.price ? null : detail.price ? detail.price / 100 : null}}"
          bigPriceStyle="font-size: 14px"
          priceStype="font-size: 14px"
        />
      </view>
      <view class="">x {{ detail.num }}</view>
    </view>
  </view>
</view>