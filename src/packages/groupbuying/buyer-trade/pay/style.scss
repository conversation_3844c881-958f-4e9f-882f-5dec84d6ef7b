page {
  background: #f7f8fa;
}

.pay-page {
  padding: 8px 12px 58px;

  &.iphone-x {
    padding-bottom: 99px;
  }
}

.header {
  display: flex;
  position: relative;
  padding: 16px 12px;
  border-radius: 4px;
  background: #fff;
  overflow: hidden;

  .avatar {
    height: 44px;
    width: 44px;
    border-radius: 50%;
  }

  .info {
    margin-left: 8px;

    .name {
      line-height: 22px;
      color: #111;
      font-size: 16px;
    }

    .address {
      margin-top: 8px;
      line-height: 18px;
      color: #666;
      font-size: 12px;
    }
  }

  .border {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 2px;
    background-image: url('https://img.yzcdn.cn/public_files/2019/04/08/ac7eb922666c9fa88edda5944eb730a1.png');
    background-position: center center;
    background-repeat: repeat-x;
  }
}

.detail {
  display: flex;
  flex-direction: column;
  margin-top: 8px;
  padding: 0 12px;
  border-radius: 4px;
  background: #fff;

  .shop {
    font-size: 14px;
    color: #323233;
    height: 40px;
    line-height: 40px;

    .icon {
      position: relative;
      top: 3px;
      margin-right: 4px;
      font-size: 16px;
    }
  }

  .total {
    display: flex;
    height: 44px;
    line-height: 44px;
    justify-content: flex-end;
    box-sizing: content-box;
    border-top: 1px solid #ebedf0;

    .label {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #323233;

      > view {
        margin-right: 5px;
      }
    }
  }
}

.ump {
  margin-top: 8px;
}

.other {
  margin-top: 8px;
  border-radius: 4px;
  overflow: hidden;
}

.result {
  margin-top: 10px;
  padding: 13px 15px;
  background: #fff;

  &-item {
    margin-top: 8px;
    display: flex;

    &-title,
    &-value {
      line-height: 18px;
      color: #323233;
      font-size: 14px;
    }

    &-value {
      flex: 1;
      text-align: right;
    }

    &:first-child {
      margin-top: 0;
    }
  }
}

.result-total {
  text-align: right;
  background: #fff;

  &-text {
    display: flex;
    justify-content: flex-end;
    margin-left: 15px;
    padding-right: 15px;
    border-top: 1px solid #ebedf0;
    height: 44px;
    line-height: 44px;
    font-size: 14px;
    color: #323233;
  }
}

.submit {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 50px;
  line-height: 50px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  background: #fff;
  box-shadow: 0 -2px 10px 0 rgba(125, 126, 128, 0.16);
  z-index: 10;

  &.submit-iphonex{
    height:91px;
    padding-bottom:41px;
  }

  .total {
    color: #323233;
    font-size: 14px;

    .label {
      display: flex;
      align-items: center;

      > view {
        margin-right: 5px;
      }
    }
  }

  .btn {
    height: 36px;
    line-height: 36px;
    border-radius: 18px;
  }
}

.pay-form-btn {
  margin: 0;
  padding: 0;
  background: #fff;
  border: none;

  &::after {
    border: 0;
  }
}
