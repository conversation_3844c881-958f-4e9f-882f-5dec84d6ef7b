import { transformToBaidu, fetchUmp, fetchUmpBuy } from '../../common';

const app = getApp();
const http = app.request;
const basePath = '/pay/wsctrade/groupbuying/buyerTrade';
const industryPath = '/wscindustry/communitybuy/buy';

// 获取活动所有数据
function getAllInfoApi(query) {
  return http({
    path: `${basePath}/getActivityDetail.json`,
    origin: 'cashier',
    query,
  });
}


// 获取「营销活动」中的商品详情数据
function getUmpItemDetail(query) {
  return http({
    path: `${industryPath}/queryItemDetailById.json`,
    query
  })
}

// 获取订单详情数据
function getOrderDetail(query) {
  return http({
    path: `${basePath}/getOrderDetail.json`,
    origin: 'cashier',
    query,
  });
}

// 缓存下单数据
function cache(data) {
  return http({
    path: `${basePath}/cache.json`,
    origin: 'cashier',
    method: 'POST',
    data,
  });
}

// 预下单
function preTradeApi(data) {
  return fetchUmpBuy('buy/preTrade.json', { data }, 'POST')
}

// 下单
function createTradeApi(data) {
  return fetchUmpBuy('buy/createTrade.json', { data }, 'POST')
}

// 预下单
function prepare(data) {
  return http({
    path: `${basePath}/prepare.json`,
    origin: 'cashier',
    method: 'POST',
    data,
  });
}

// 订单详情页去支付
function paymentApi(data) {
  return http({
    path: `${basePath}/pay.json`,
    origin: 'cashier',
    method: 'POST',
    data,
  });
}

// 订单列表
function getRollOrders(data) {
  return http({
    path: `${basePath}/rollOrders.json`,
    origin: 'cashier',
    data,
  });
}

// 订单详情
function getItemDetail(data) {
  return http({
    path: `${basePath}/itemDetail.json`,
    origin: 'cashier',
    data,
  });
}

// 根据经纬度尝试查询团长信息
function getLastVisit() {
  return fetchUmp('getLastVisitHeader.json');
}

// 根据经纬度尝试查询团长信息
function tryToFindDetailWithLastVisit(data) {
  return fetchUmp('getActivitySetWithLastVisit.json', { data });
}

// 根据经纬度尝试查询团长信息
function tryToFindDetailWithLocation(data) {
  return fetchUmp('getActivitySetWithLocation.json', { data });
}

// 根据团长id尝试查询活动
function tryToFindDetailWithHeader(data) {
  return fetchUmp('getActivitySetWithHeader.json', { data });
}

// 查询默认团长
function getDefaultAct(data) {
  return fetchUmp('getDefaultAct.json', { data });
}

// 记录最近的团长
function accessHeader(data) {
  return fetchUmp('headerAccess.json', { data });
}

// 获取手机号
function getPhone() {
  return fetchUmpBuy('buy/phone.json');
}

// 门槛缴费设置获取
function getSettingApi() {
  return fetchUmp('recruit/settingV2.json');
}

// 社区团购配置获取
function getGroupbuySettings() {
  return http({
    path: `/wsctrade/groupbuying/settings.json`,
    origin: 'cashier',
    method: 'GET',
  });
}

// 获取位置 promise
function getLocation() {
  let resolve = null;
  const p = new Promise((r) => (resolve = r));
  wx.getLocation({
    type: 'gcj02',
    success(res) {
      const { longitude: lon, latitude: lat } = res;
      resolve(transformToBaidu(lon, lat));
    },
    fail() {
      resolve();
    },
  });
  return p;
}

function getHeaderById(headerBuyerId) {
  return http({
    path: '/wsctrade/groupbuying/myprofit/getHeaderInfo.json',
    data: {
      headerBuyerId,
    },
  });
}

function getCurrentHeader() {
  return fetchUmpBuy('buy/checkHeader.json');
}

function queryGoodsCustomers(data) {
  return fetchUmpBuy('buy/queryGoodsCustomers.json', { data }, 'POST');
}

function fetchCouponList(data) {
  return fetchUmpBuy('coupon/list.json', { data });
}

function receiveCoupon(data) {
  return fetchUmpBuy('coupon/receive.json', { data });
}

function getPoster(data) {
  let url = '/wscump/communitybuy/poster/activity.json';
  let height = 1040;
  const width = 600;
  if (data.itemId) {
    url = '/wscump/communitybuy/poster/item.json';
    height = 960;
  }
  return http({
    path: url,
    data,
  }).then((res) => {
    return {
      ...res,
      height,
      width,
    };
  });
}

function getByKey(data) {
  return fetchUmpBuy('poster/getByKey.json', { data });
}

function getPosterWay() {
  return fetchUmpBuy('poster/posterWay.json');
}

function queryAnotherActivity(data) {
  return fetchUmpBuy('buy/anotherActivity.json', { data });
}

function subscribe(data) {
  data.from = 2;
  return fetchUmpBuy('subscribe.json', { data }, 'POST');
}

function checkSubscribe(data) {
  data.from = 2;
  return fetchUmpBuy('checkSubscribe.json', { data });
}

function getCategories(data) {
  return fetchUmpBuy('activity/getCategories.json', { data });
}

function getRadiusRecommendHeader(data) {
  return fetchUmpBuy('getRadiusRecommendHeader.json', { data });
}

function getLastAccessHeaderV2(data) {
  return fetchUmpBuy('getLastAccessHeaderV2.json', { data });
}

// 获取购物车商品
function getCart(data) {
  return fetchUmpBuy('cart/get.json', { data });
}

// 操作购物车
function operateCart(data) {
  return fetchUmpBuy('cart/operate.json', { data }, 'POST');
}

// 清除购物车
function clearCart(data) {
  return fetchUmpBuy('cart/clear.json', { data }, 'POST');
}

// 活动分享卡片
function fetchShareCard(data) {
  return http({
    method: 'POST',
    path: '/wscindustry/groupbuying/poster/shareCard.json',
    data,
  });
}

// 商品分享卡片
function fetchGoodsShareCard(data) {
  return http({
    method: 'POST',
    path: '/wscindustry/groupbuying/poster/goodsShareCard.json',
    data,
  });
}

export {
  getAllInfoApi,
  getOrderDetail,
  preTradeApi,
  createTradeApi,
  paymentApi,
  getRollOrders,
  getItemDetail,
  getUmpItemDetail,
  getLastVisit,
  tryToFindDetailWithLastVisit,
  tryToFindDetailWithLocation,
  tryToFindDetailWithHeader,
  getPhone,
  getLocation,
  getDefaultAct,
  accessHeader,
  getSettingApi,
  cache,
  prepare,
  getHeaderById,
  getCurrentHeader,
  queryGoodsCustomers,
  fetchCouponList,
  fetchShareCard,
  fetchGoodsShareCard,
  receiveCoupon,
  getPoster,
  getByKey,
  getPosterWay,
  queryAnotherActivity,
  subscribe,
  checkSubscribe,
  getCategories,
  getRadiusRecommendHeader,
  getLastAccessHeaderV2,
  getCart,
  operateCart,
  clearCart,
  getGroupbuySettings,
};
