<page-container class="page {{ themeClass }} page-{{ deviceType }}" page-container-class="van-coupon-list">
  <!--优惠码兑换-->
  <van-field
    wx:if="{{ !forbidcode }}"
    value="{{ code }}"
    center
    placeholder="请输入优惠码"
    use-button-slot
    maxlength="20"
    border="{{ false }}"
    bind:change="onCodeInput"
    bind:focus="onCodeFocus"
  >
    <van-button
      slot="button"
      size="small"
      custom-class="theme-bg-color"
      disabled="{{ code.length <= 0 }}"
      bind:click="onExchangeTaped"
    >{{ exchanging ? '验证中...' : '兑换' }}</van-button>
  </van-field>

  <van-tabs custom-class="th_tabs-l">
    <van-tab title="{{ enableText }} ">
      <coupon-cell
        wx:for="{{ charge_coupon }}"
        wx:key="{{ item.id }}"
        coupon="{{ item }}"
        selected="{{ selected_coupon.id == item.id }}"
        bind:click="handleChooseCoupon"
        theme-color="theme-color"
      />
      <empty wx:if="{{ !charge_coupon.length }}" />
    </van-tab>
    <van-tab title="{{ disableText }}">
      <coupon-cell
        wx:for="{{ unavailable_coupon }}"
        coupon="{{ item }}"
        wx:key="{{ item.id }}"
        disabled
      />
      <empty wx:if="{{ !unavailable_coupon.length }}" />
    </van-tab>
  </van-tabs>

  <van-button
    size="large"
    custom-class="van-coupon-list__close"
    bind:click="onNotUseTapped"
  >不使用优惠</van-button>
</page-container>

<van-toast id="van-toast" />
