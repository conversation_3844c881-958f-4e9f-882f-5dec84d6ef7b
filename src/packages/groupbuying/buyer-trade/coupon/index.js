import Toast from '@vant/weapp/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import WscPage from 'pages/common/wsc-page/index';

const app = getApp();
const eventKey = 'communitybuy-coupon-change';

WscPage({
  data: {
    forbidcode: true, // 禁用优惠码兑换功能
    charge_coupon: [], // 可用优惠券
    unavailable_coupon: [], // 不可用优惠券
    selected_coupon: {}, // 已选中优惠券
    code: '', // 优惠码
    exchangeParams: {}, // 从下单页传过来的参数，不用关心内容，用于兑换优惠码接口,
    enableText: '可使用优惠券（0）',
    disableText: '不可使用优惠券（0）',
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const dbid = options.dbid || '';
    const forbidcode = !!options.forbidcode;
    const param = app.db.get(dbid) || {};
    const { charge_coupon = [], unavailable_coupon = [] } = param;

    this.setYZData({
      forbidcode,
      charge_coupon: mapKeysCase.toCamelCase(charge_coupon),
      unavailable_coupon: mapKeysCase.toCamelCase(unavailable_coupon),
      selected_coupon: param.selected_coupon || {},
      exchangeParams: param.exchangeParams || {},
      enableText: `可使用优惠券（${charge_coupon.length}）`,
      disableText: `不可使用优惠券（${unavailable_coupon.length}）`,
    });
  },

  // 选择优惠券
  handleChooseCoupon(event) {
    const coupon = event.detail;
    this.trigger(eventKey, coupon);
    wx.navigateBack();
  },

  // 兑换优惠码
  onExchangeTaped() {
    Toast('社区团购暂时还不支持优惠码');
  },
  // 输入优惠码
  onCodeInput(event) {
    this.setYZData({ code: event.detail });
  },
  // 点击不使用优惠券
  onNotUseTapped() {
    this.trigger(eventKey, {});
    wx.navigateBack();
  }
});
