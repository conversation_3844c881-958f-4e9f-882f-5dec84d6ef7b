.coupon {
  margin: 10px 15px;
  border-radius: 4px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);

  &__content {
    display: flex;
    height: 100px;
    padding: 0 15px;
    background-color: #fff;
    border-radius: 4px;
    align-items: center;
  }

  &__head {
    flex-shrink: 0;
    min-width: 95px;
    padding-right: 10px;

    &__value {
      height: 34px;
      font-size: 24px;
      font-weight: 500;
      line-height: 34px;
    }

    &__unit {
      margin-left: 2px;
      font-size: 50%;
    }
  }

  &__body {
    flex: 1;

    &__title {
      font-size: 16px;
      font-weight: bold;
      color: #111;
    }

    &__valid {
      margin-top: 10px;
      font-size: 12px;
      color: #999;
    }
  }

  .condition {
    font-size: 12px;
    color: #999;
    white-space: pre-wrap;
  }

  &--disabled {
    .coupon__head__value,
    .coupon__head__unit,
    .coupon__body__title {
      color: #969799;
    }
  }

  &__description {
    position: relative;
    padding: 6px 15px;
    margin-top: -10px;
    font-size: 12px;
    color: #999;
    background-color: #fbfbfb;
    border-radius: 4px;

    &::after {
      border-top-style: dashed;
    }
  }

  &__dot {
    position: absolute;
    top: 0;
    z-index: 2;
    width: 10px;
    height: 10px;
    background-color: #f9f9f9;
    border-radius: 50%;

    &::after {
      position: absolute;
      top: 0;
      left: 0;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      content: '';
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1) inset;
    }

    &--left {
      left: 0;
      transform: translate(-50%, -50%);

      &::after {
        clip: rect(0, 10px, 10px, 5px);
      }
    }

    &--right {
      right: 0;
      transform: translate(50%, -50%);

      &::after {
        clip: rect(0, 5px, 10px, 0);
      }
    }
  }

  &__checkbox {
    flex-shrink: 0;
    font-size: 20px;
  }
}
