<view class="coupon {{ disabled ? 'coupon--disabled' : '' }}" bindtap="handleClick">
  <view class="coupon__content">
    <view class="coupon__head">
      <theme-view color="general">
        <text class="coupon__head__value">{{ coupon.valueDesc || extra.valueDesc }}</text>
        <text class="coupon__head__unit">{{ coupon.unitDesc || extra.unitDesc }}</text>
      </theme-view>
      <view class="condition">{{ coupon.condition }}</view>
    </view>
    <view class="coupon__body">
      <view class="coupon__body__title">{{ coupon.name }}</view>
      <view class="coupon__body__valid">{{ extra.validPeriod }}</view>
    </view>
    <van-icon wx:if="{{ selected }}" name="checked" class="coupon__checkbox theme-color" />
  </view>

  <view
    wx:if="{{ disabled && coupon.reason }}"
    class="coupon__description van-hairline--top"
    catchtap="handleShowMore"
  >
    <view class="{{ showMore ? '' : 'van-ellipsis' }}">{{ coupon.reason }}</view>
    <view class="coupon__dot coupon__dot--left"></view>
    <view class="coupon__dot coupon__dot--right"></view>
  </view>
</view>
