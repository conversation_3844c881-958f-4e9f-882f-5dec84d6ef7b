import Toast from '@vant/weapp/dist/toast/toast';

const PayUtils = {
  // data 接口返回参数
  integrateParams(data) {
    const { prePaymentPreparation, paymentPreparation } = data;
    const newPayResult = prePaymentPreparation.prepay
      ? prePaymentPreparation
      : paymentPreparation;
    // 记录订单号和支付map
    this.setYZData({
      orderNo: data.orderNo || this.data.orderNo,
      payParams: newPayResult,
    });
    this.pay.setPrepayMode(prePaymentPreparation.prepay);
    // 如果是0元订单，到支付成功页
    if (data.zeroOrder || newPayResult.payAmount === 0) {
      wx.redirectTo({
        url: `/packages/order/paid/index?order_no=${this.data.orderNo}`,
      });
      return;
    }
    // 唤起支付
    this.doPay(newPayResult);
  },

  doPay(payParams) {
    Toast.loading();
    this.pay.createCashierOrder(payParams).then(() => {
      this.openCashier();
    });
  },

  // 拉起收银台
  openCashier() {
    const {
      cashierSalt,
      cashierSign,
      partnerId,
      prepayId,
    } = this.data.payParams;
    this.pay
      .getPayWays({
        partner_id: partnerId,
        prepay_id: prepayId,
        cashier_sign: cashierSign,
        cashier_salt: cashierSalt,
      })
      .then((payWays) => {
        Toast.clear();
        if (!payWays || payWays.length === 0) {
          console.warn('支付方式列表为空');
          Toast('暂无可用支付方式，请联系商家开启更多支付方式');
          return;
        }
        if (payWays.length === 1) {
          const payWayData = payWays[0] || {};
          if (!payWayData.available) {
            // 如果只有一种支付方式且不可用，则显示支付列表
            this.setYZData({
              '$cashier.actions': payWays,
              '$cashier.show': true,
              '$cashier.loaded': true,
            });
          } else {
            this.$cashierClick({
              pay_channel: payWayData.pay_channel,
              pay_channel_name: payWayData.pay_channel_name,
            });
          }
        } else {
          // 群团购屏蔽货到付款和找人代付
          const _payWays = [];
          payWays.forEach((item) => {
            if (
              item.pay_channel_name !== '货到付款' &&
              item.pay_channel_name !== '找人代付'
            ) {
              _payWays.push(item);
            }
          });
          this.setYZData({
            '$cashier.actions': _payWays,
            '$cashier.show': true,
            '$cashier.loaded': true,
          });
        }
      })
      .catch((e) => {
        Toast.clear();
        console.warn('获取收银台支付列表失败：', e);
        Toast(e.msg || '网络抖了下，再点下试试');
      });
  },

  $cashierClick(payChannelData, payData) {
    // 支付完成或者失败后取消支付状态锁定
    this.setYZData({
      isDisabled: false,
    });
    this.pay
      .doPayAction(payChannelData, payData)
      .then((payRes) => {
        // 支付完成后续操作
        if (payChannelData.pay_channel === 'CREDIT_CARD') {
          const deepLinkData = (payRes && payRes.deep_link_info) || {};
          Object.assign(deepLinkData, {
            partner_return_url: payRes.partner_return_url,
          });
          wx.navigateTo({
            url:
              '/pages/pay/credit-card/index?deepLinkData=' +
              encodeURIComponent(JSON.stringify(deepLinkData)),
          });
          return;
        }
        this.redirectToSuccess();
      })
      .catch(({ msg, type }) => {
        if (type === 'need_password') {
          this.setYZData({
            '$cashier.showPassword': true,
          });
        } else if (type !== 'cancel' && type !== 'adjust_price') {
          // 如果不是（卖家取消付款 || 不同意改价），就给个提示语
          Toast(msg || '网络抖了下，再点下试试');
        }
      });
  },

  $cashierCancel() {
    this.setYZData({
      isDisabled: false,
    });
    this.setYZData({
      '$cashier.actions': [],
      '$cashier.show': false,
    });
  },

  // 支付完成跳转
  redirectToSuccess() {
    wx.redirectTo({
      url: `/packages/groupbuying/buyer-trade/detail/index?orderNo=${this.data.orderNo}&isPay=true`,
    });
  },
};

export default PayUtils;
