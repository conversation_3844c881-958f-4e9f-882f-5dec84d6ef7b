import Event from '@youzan/weapp-utils/lib/event';
import args from '@youzan/weapp-utils/lib/args';
import omit from '@youzan/weapp-utils/lib/omit';
import { setIsReLaunch } from '@/base-api/shop/chain-store.js';

const app = getApp();

function isBlankPage(url) {
  return /pages\/common\/blank-page/.test(url);
}

export function formatRedirectUrl(url) {
  let redirectUrl = url || '/packages/home/<USER>/index';
  if (isBlankPage(redirectUrl)) {
    const query = args.getAll(redirectUrl);
    let { weappSharePath } = query;
    if (weappSharePath) {
      weappSharePath = decodeURIComponent(weappSharePath);
      const sharePathQuery = args.getAll(weappSharePath);
      // B端复制总店页面链接进入小程序，手动选完店铺后需要移除kdt_id以免又更新成总店的kdt_id
      const newShareQuery = omit(sharePathQuery, 'kdt_id');
      let [newSharePath] = weappSharePath.split('?');
      newSharePath = args.add(newSharePath, newShareQuery);
      redirectUrl = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
        newSharePath
      )}`;
    }
  }

  return redirectUrl;
}

function redirectPage(currentKdtId, kdtId, redirectUrl) {
  // 选择网店重定向回去时，先把 relaunch 标志置为 false
  setIsReLaunch(false);

  // 如果kdtId没变，则直接转跳页面
  if (currentKdtId === kdtId) {
    wx.reLaunch({
      url: redirectUrl || '/packages/home/<USER>/index',
    });
    return;
  }
  // fetch 到了global信息之后再跳转
  Event.once(
    'shop:info:fetch:success',
    () => {
      app.globalData.prevMultiShopKdtID = currentKdtId;
      const url = formatRedirectUrl(redirectUrl);
      wx.reLaunch({
        url,
      });
    },
    this
  );
}

export function handleChangeStore({ redirectUrl, kdtId }) {
  const currentKdtId = app.getKdtId();

  app.getShopInfo().then(() => {
    redirectPage(currentKdtId, kdtId, redirectUrl);
  });

  app.updateKdtId(kdtId, false, {
    mark: '906',
  });
}

export function resolveChainStore() {
  return new Promise((resolve) => {
    app.getShopInfo().then((data) => {
      const { chainStoreInfo } = data;
      const { isRootShop = false, isMultiOnlineShop = false } = chainStoreInfo;

      if (isMultiOnlineShop && isRootShop) {
        app.once('app:chainstore:kdtid:update', () => {
          resolve();
        });
        return;
      }
      resolve();
    });
  });
}
