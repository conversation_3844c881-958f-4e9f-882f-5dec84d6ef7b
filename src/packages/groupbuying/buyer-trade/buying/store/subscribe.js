import Toast from '@vant/weapp/dist/toast/toast';
import { subscribeMessage } from 'helpers/subscribe';
import { subscribe, checkSubscribe } from '../../api';

export const actions = {
  // 订阅
  subscribe({ commit, state }) {
    const { activityId, headerBuyerId } = state;
    subscribeMessage({
      scene: 'groupbuyActivityBegin',
      successCallBack: () => {
        // 记录订阅状态
        subscribe({
          activityId,
          headerBuyerId,
        })
          .then(res => {
            Toast.clear();
            if (res.value) {
              Toast('订阅成功');
              commit('setSubscribe', 1);
            } else {
              Toast('订阅失败');
            }
          })
          .catch(e => {
            Toast(e.msg || e || '订阅失败');
          });
      },
      failCallBack: () => {
        // 失败则弹出失败弹窗
        Toast('订阅失败');
      }
    });
  },

  checkSubscribe({ state, commit }) {
    const {
      activityId, headerBuyerId, templateModel, actDetail
    } = state;
    // 老模版没有订阅
    if (templateModel === 0 || actDetail.state !== 0) return;
    checkSubscribe({
      activityId,
      headerBuyerId,
    })
      .then(res => {
        commit('setSubscribe', res.value ? 1 : 2);
      });
  }
};
