import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import money from '@youzan/weapp-utils/lib/money';
import get from '@youzan/weapp-utils/lib/get';
import { isNewIphone } from 'shared/utils/browser/device-type';
import Toast from '@vant/weapp/dist/toast/toast';
import navs from '@/helpers/navigate';
import {
  getItemDetail,
  getUmpItemDetail,
  queryGoodsCustomers,
  receiveCoupon,
  fetchGoodsShareCard,
} from '../../api';
import { actions as globalActions } from './global';
import { actions as tradeActions } from './trade';
import { actions as cartActions } from './cart';
import { actions as subscribeActions } from './subscribe';
import { padStart } from '../../../common';

const { navigate, switchTab } = navs;

const getReason = (stockNum, left, quotaUsed) => {
  if (left < stockNum) {
    return quotaUsed > 0
      ? `之前已购买过${quotaUsed}件，已超出限购`
      : '已达到限购数量';
  }
  return '已经没有库存了';
};

const getPriceRange = (list) => {
  let min = list[0].promotionPrice;
  let max = list[0].promotionPrice;
  list.forEach((item) => {
    min = item.promotionPrice < min ? item.promotionPrice : min;
    max = item.promotionPrice > max ? item.promotionPrice : max;
  });
  return min === max ? money(min).toYuan() : `${money(min).toYuan()} - ${money(max).toYuan()}`;
};

let goodsStore = {};
const getInitalStore = () => ({
  isIphoneX: isNewIphone(),

  // loading状态
  loading: false,

  // 活动详情
  actDetail: null,

  // 团长详情
  headerDetail: null,

  // 商品列表
  goodslist: [],

  // 当前sku商品
  skuData: null,

  // sku显隐
  skuVisible: false,

  // 订单列表
  orderlist: [],

  // 原始购物车列表
  originlist: [],

  cartVisible: false,

  // 当前商品详情
  detailInfo: null,

  // 详情页visible
  detailVisible: false,

  // 团长buyerId
  headerBuyerId: null,

  // 活动别名
  alias: null,

  // 活动Id
  activityId: null,

  // 限时折扣活动数据
  limitDiscountDetail: null,

  // 支付状态 0 禁止支付 1 计算中 2 可以支付
  payStatus: 0,

  // 支付金额
  realPay: 0,

  // 电话
  mobile: '',

  // 备注
  buyerMsg: '',

  // 是否显示为空
  empty: false,

  // 是否能切换
  showSwitch: false,

  // 是否显示near
  showNear: false,

  // 是否显示手机号
  showMobile: false,

  // 模版类型
  // 0 简单模版 1 新模版
  templateModel: 0,

  // 微页面 alias，只有当templateModel为2时才生效
  featureAlias: '',

  // 当前优惠券
  coupon: {
    value: 0,
  },

  // 可用优惠券
  enableCoupons: [],

  // 不可用优惠券
  disableCoupons: [],

  // 下单可用优惠券显隐
  orderCouponListVisible: false,

  // 优惠券列表
  couponList: [],

  // 优惠券列表显隐
  couponListVisible: false,

  // 当前用户是否是团长
  isHeader: false,

  // roll-order是否吸顶
  fixedRollOrder: false,

  // 订阅状态 0 = loading, 1 = true, 2 = false
  subscribe: 0,

  // 下一个活动
  nextAct: null,

  // 下一个活动显隐
  nextActVisible: false,

  // 分享的显隐
  shareVisible: false,

  // 是否显示分类
  showItemCategory: false,

  // 分类数组
  categoryList: [],

  // 限时折扣活动列表
  limitDiscountList: [],

  // 限时折扣当前选中tab的index
  limitDiscountIndex: 0,

  // 商品分享卡片
  shareGoodsImgs: {},

  // 活动分享卡片
  shareActivityImg:
    'https://img.yzcdn.cn/upload_files/2020/10/23/Fkej2fvopyXHm8fnHenPI5vJ_bpH.png',

  // 商品详情图片
  goodsPictures: []
});

let storeId = 0;

const state = getInitalStore();

const getters = {
  isBuying(state) {
    const { actDetail } = state;
    if (!actDetail) return false;
    return actDetail.state === 1;
  },

  currentPrice(state) {
    const { detailInfo } = state;
    const price = get(detailInfo, 'spu.minPrice', 0);
    return money(price).toYuan();
  },

  getDetailInfo(state) {
    return state.detailInfo;
  },

  buyinglist(state) {
    const { goodslist, originlist } = state;
    return goodslist.map((one) => {
      const { itemId, stockNum } = one;
      const info = goodsStore[itemId] || {};
      const { quota = 0, quotaUsed = 0 } = info;
      const filter = originlist.filter(
        (cartItem) => cartItem.goodsId === itemId
      );
      const left = quota > 0 ? quota - quotaUsed : stockNum;
      let num = 0;
      for (let i = 0; i < filter.length; i++) {
        num += filter[i].num;
      }
      return {
        ...one,
        orderInfo: {
          num,
          max: Math.min(left, stockNum),
          min: 0,
          reason: getReason(stockNum, left, quotaUsed),
        },
      };
    });
  },

  limitDiscountListWithOrder(state) {
    const { limitDiscountList, originlist } = state;
    return limitDiscountList.map((limitDiscount) => {
      limitDiscount.goodsList = limitDiscount.goodsList.map((item) => {
        const { itemId, stockNum } = item;
        const info = goodsStore[itemId] || {};
        const { quota = 0, quotaUsed = 0 } = info;
        const filter = originlist.filter(
          (cartItem) => cartItem.goodsId === itemId
        );
        const left = quota > 0 ? quota - quotaUsed : stockNum;
        let num = 0;
        for (let i = 0; i < filter.length; i++) {
          num += filter[i].num;
        }
        item.umpActivityId = limitDiscount.id;
        return {
          ...item,
          orderInfo: {
            umpActivityId: limitDiscount.id,
            num,
            max: Math.min(left, stockNum),
            min: 0,
            reason: getReason(stockNum, left, quotaUsed),
          },
        };
      });
      return limitDiscount;
    });
  },

  cartlist(state) {
    const { originlist, limitDiscountList } = state;
    return originlist.map((one) => {
      const { goodsId, skuId, num } = one;
      const info = goodsStore[goodsId] ? goodsStore[goodsId] : one.info;
      let skuName = '';
      const { spu, quota, quotaUsed, skuPrices } = info;
      let { price, stockNum, list } = spu;
      let left = stockNum;

      if (one.info && one.info.isLimitDiscount) {
        one.isLimitDiscount = true;
        // one.umpActivityId = one.info.umpActivityId
        info.umpActivityId = one.umpActivityId;
        info.isLimitDiscount = true;
        // info.spu.price = one.info.spu.price
      }

      // 进房计算cart不走这里
      if (info.skuPrices) {
        one.umpActivityId = info.umpActivityId;
        one.isLimitDiscount = true;
        const sku = info.skuPrices.find((one) => one.skuId === skuId);
        price = money(sku.promotionPrice).toYuan();
        // info.spu.price = getPriceRange(info.skuPrices);
      }

      // 只要是限时折扣就会走这里
      if (info.isLimitDiscount || info.umpActivityId) {
        // 单sku
        if (list.length === 0) {
          price = spu.price;
        } else {
          // 多sku
          const sku = list.find((one) => one.id === skuId);
          const skuItem = JSON.parse(sku.sku);
          // 从存储的限时折扣活动信息查询对应的折扣价
          const limitdiscount = limitDiscountList.find(
            (item) => item.id === one.umpActivityId
          );
          const good = limitdiscount.goodsList.find(
            (item) => item.itemId === one.goodsId
          );
          const skuPrice = good
            ? good.skuPrices.find((one) => one.skuId === skuId)
            : null;
          // const skuPrice =  info.skuPrices ? info.skuPrices.find(one => one.skuId === skuId) : null
          // price = skuPrice ? money(skuPrice.promotionPrice).toYuan() : money(sku.price).toYuan();

          stockNum = sku.stockNum;
          price = skuPrice
            ? money(skuPrice.promotionPrice).toYuan()
            : money(sku.price).toYuan();
          skuName = skuItem.map((one) => one.v).join(' ');
        }
      } else if (skuId && !skuPrices) {
        // 多sku商品的价格和库存会不一样
        const sku = list.find((one) => one.id === skuId);
        const skuItem = JSON.parse(sku.sku);
        stockNum = sku.stockNum;
        price = money(sku.price).toYuan();
        skuName = skuItem.map((one) => one.v).join(' ');
      } else if (skuId) {
        const sku = list.find((one) => one.id === skuId);
        const skuItem = JSON.parse(sku.sku);
        skuName = skuItem.map((one) => one.v).join(' ');
        stockNum = sku.stockNum;
      }

      // 处理限购
      if (quota) {
        left = quota - quotaUsed;
        let total = 0;
        if (skuId) {
          const sameIds = originlist.filter((one) => one.goodsId === goodsId);
          for (let i = 0; i < sameIds.length; i++) {
            total += sameIds[i].num;
          }
          left = quota - quotaUsed - total + num;
        }
      }
      return {
        ...info,
        skuId,
        skuName,
        price,
        // price,
        orderInfo: {
          num,
          max: Math.min(stockNum, left),
          min: 0,
          reason: getReason(stockNum, left, quotaUsed),
        },
      };
    });
  },

  totalItem(state) {
    const { originlist } = state;
    let total = 0;
    for (let i = 0; i < originlist.length; i++) {
      total += originlist[i].num;
    }
    return total;
  },

  realMoney(state, getters) {
    let totalMoney = 0;
    const { cartlist } = getters;
    if (!cartlist) return;
    cartlist.forEach((i) => {
      // totalMoney += i.isLimitDiscount ? i.spu.price * i.orderInfo.num : i.price * i.orderInfo.num;
      totalMoney += i.price * i.orderInfo.num;
    });
    return totalMoney.toFixed(2);
  },

  finalPrice(state) {
    const final = money(+state.realPay).toCent() - state.coupon.value;
    return money(final).toYuan();
  },

  subscribeTime(state) {
    const { actDetail } = state;
    if (actDetail && actDetail.startTime) {
      const time = new Date(actDetail.startTime);
      return {
        month: time.getMonth() + 1,
        date: time.getDate(),
        hour: padStart(`${time.getHours()}`, 2, '0'),
        minute: padStart(`${time.getMinutes()}`, 2, '0'),
        second: padStart(`${time.getSeconds()}`, 2, '0'),
      };
    }
    return null;
  },
};

const mutations = {
  // 清除store
  clearStore(state) {
    const newStore = getInitalStore();
    Object.keys(newStore).forEach((key) => {
      const val = newStore[key];
      state[key] = val;
    });
  },

  // 清除goodsStore
  clearGoodsStore() {
    goodsStore = Object.create(null);
  },

  // 设置手机号
  setMobile(state, mobile) {
    state.mobile = mobile;
  },

  // 设置备注
  setBuyerMsg(state, buyerMsg) {
    state.buyerMsg = buyerMsg;
  },

  // 设置当前详情页
  setDetail(state, detail) {
    const { id } = detail;
    const {
      goodslist,
      limitDiscountList,
      limitDiscountIndex,
      originlist,
    } = state;
    let found;
    if (goodslist.find((one) => one.itemId === id)) {
      found = goodslist.find((one) => one.itemId === id);
    } else {
      found = limitDiscountList[limitDiscountIndex].goodsList.find(
        (one) => one.itemId === id
      );
      found.isLimitDiscount = true;
    }
    const { stockNum } = found;
    const filter = originlist.filter((cartItem) => cartItem.goodsId === id);
    const { quota, quotaUsed } = detail;
    const left = quota > 0 ? quota - quotaUsed : stockNum;
    let num = 0;
    for (let i = 0; i < filter.length; i++) {
      num += filter[i].num;
    }
    detail = {
      ...found,
      orderInfo: {
        num,
        max: Math.min(stockNum, left),
        min: 0,
        reason: getReason(stockNum, left, quotaUsed),
      },
      ...detail,
    };
    if (detail.isLimitDiscount) {
      detail.spu.minPrice = detail.skuPrices[0].promotionPrice;
      // detail.spu.price = money(detail.skuPrices[0].promotionPrice).toYuan();
      detail.spu.price = getPriceRange(detail.skuPrices);
      detail.spu.collectionPrice = detail.skuPrices[0].promotionPrice;
    }
    state.detailInfo = detail;
    // commit('setDetailInfo', detail)
  },

  setDetailInfo(state, data) {
    state.detailInfo = data;
  },

  // 设置商品详情图片
  setGoodsPictures(state, goodsPictures) {
    state.goodsPictures = goodsPictures;
  },

  setShareGoodsImg(state, { alias, url }) {
    state.shareGoodsImgs[alias] = url;
  },

  // 设置当前活动
  setActDetail(state, data) {
    const { items, activityDetail, groupHeaderDetail } = data;
    if (items) {
      const goodslist = items.filter((one) => one.join);
      if (goodslist.length > 0) {
        storeId = goodslist[0].storeId || 0;
      }
      state.goodslist = goodslist;
    } else {
      state.goodslist = [];
    }

    if (activityDetail) {
      state.actDetail = activityDetail;
    } else {
      state.actDetail = null;
    }

    if (groupHeaderDetail) {
      state.headerDetail = groupHeaderDetail;
    }
  },

  // 设置商品列表
  setGoodslist(state, goodslist) {
    state.goodslist = goodslist;
  },

  // 设置当前活动id等参数
  setQuery(state, payload) {
    state.headerBuyerId = payload.headerBuyerId;
    state.alias = payload.alias;
    state.activityId = payload.activityId;
  },

  // 设置详情页 Visible
  setDetailVisible(state, visible) {
    if (visible !== state.detailVisible) {
      state.detailVisible = visible;
    }
  },

  // 设置购物车 Visible
  setCartVisible(state, visible) {
    const { originlist } = state;
    const filter = originlist.filter((one) => one.num > 0);
    if (visible && filter.length === 0) {
      Toast('还没有选购商品');
      return;
    }
    state.cartVisible = visible;
  },

  // sku Visible
  setSkuVisible(state, visible) {
    state.skuVisible = visible;
  },

  // sku data
  setSkuData(state, data) {
    state.skuData = data;
  },

  // 设置原始订购列表
  setOriginlist(state, changeData) {
    const { goodsId, delta, num, skuId } = changeData;
    const found = state.originlist.find((one) => {
      if (skuId) {
        return one.goodsId === goodsId && one.skuId === skuId;
      }
      return one.goodsId === goodsId;
    });
    if (!found) {
      if (delta <= 0 || num === 0) {
        return;
      }
      state.originlist.push({
        goodsId,
        skuId,
        num: num || delta,
      });
    } else {
      if (typeof delta !== 'undefined') {
        found.num += delta;
      } else {
        found.num = num;
      }
      if (found.num === 0) {
        state.originlist = state.originlist.filter((one) => one.num > 0);
      }
    }
  },

  // 从stroage恢复cart数据
  restoreOriginlist(state, originlist) {
    state.originlist = originlist;
  },

  // 设置支付状态
  setPayStatus(state, status) {
    state.payStatus = status;
  },

  // 设置支付
  setRealPay(state, realPay) {
    state.realPay = realPay;
  },

  // 是否展示空
  setEmpty(state) {
    state.empty = true;
  },

  // 是否展示swtich
  setSwitch(state, showSwitch) {
    state.showSwitch = showSwitch;
  },

  // 是否展示附近
  setNear(state, showNear) {
    state.showNear = showNear;
  },

  // 是否显示手机号
  setMobileShow(state, showMobile) {
    state.showMobile = showMobile;
  },

  // 设置模版类型
  setTemplateModel(state, templateModel) {
    state.templateModel = templateModel;
  },

  // 模板为自定义模板时，设置微页面alias - featureAlias
  setFeatureAlias(state, featureAlias) {
    state.featureAlias = featureAlias;
  },

  // 设置优惠券列表显隐
  setCouponListVisible(state, visible) {
    state.couponListVisible = visible;
  },

  // 设置优惠券列表
  setCouponList(state, list) {
    state.couponList = list;
  },

  // 把优惠券置为已使用
  /* eslint-disable-next-line */
  setCouponReceived(state, id) {
    const { couponList } = state;
    const found = couponList.find((one) => one.id === id);
    // 把优惠券置为已使用
    if (found) {
      found.isReceived = true;
    }
  },

  // 设置下单优惠券列表显隐
  setOrderCouponListVisible(state, visible) {
    state.orderCouponListVisible = visible;
  },

  // 优惠券设置
  setCoupon(state, coupon) {
    // 因为之后任何改动都会导致重新算价格，所以在这个时候计算价格
    if (coupon.value === undefined) {
      coupon.value = 0;
    }
    state.coupon = coupon;
  },

  // 设置下单优惠券列表
  setOrderCouponList(state, { disableCoupons, enableCoupons }) {
    state.enableCoupons = enableCoupons;
    state.disableCoupons = disableCoupons;
  },

  // 设置当前是否是团长
  setIsHeader(state) {
    state.isHeader = true;
  },

  clearOriginlist(state) {
    state.originlist = [];
    state.cartVisible = false;
  },

  // 设置浮动条是否吸顶
  setFixedRollOrder(state, data) {
    state.fixedRollOrder = data;
  },

  // 设置订阅状态
  setSubscribe(state, status) {
    state.subscribe = status;
  },

  // 设置下一个活动
  setNextAct(state, nextAct) {
    state.nextAct = nextAct;
  },

  // 设置微信二维码显隐
  setNextActVisible(state, nextActVisible) {
    state.nextActVisible = nextActVisible;
  },

  // 设置分享的显隐
  setShareVisible(state, shareVisible) {
    state.shareVisible = shareVisible;
  },

  // 设置是否显示分类
  setShowItemCategory(state, nextValue) {
    state.showItemCategory = nextValue;
  },

  // 设置当前的categoryList
  setCategoryList(state, nextList) {
    state.categoryList = nextList;
  },

  // 设置当前的限时折扣列表
  setLimitDiscountList(state, nextList) {
    state.limitDiscountList = nextList;
  },

  // 设置当前限时折扣tab的index
  setLimitDiscountIndex(state, nextValue) {
    state.limitDiscountIndex = nextValue;
  },

  // 设置活动分享卡片
  setAcitivityShareCard(state, url) {
    state.shareActivityImg = url;
  },
};

const actions = {
  // 增加商品
  plus({ dispatch, commit }, { goodsId, umpActivityId, skuId }) {
    dispatch('getItemDetail', { goodsId, umpActivityId }).then((data) => {
      const { hasSku, id } = data;
      if (hasSku) {
        commit('setSkuData', data);
        commit('setSkuVisible', true);
      } else {
        dispatch('changeOrder', {
          goodsId: id,
          delta: 1,
          umpActivityId,
          skuId,
        });
      }
    });
  },

  // 减少商品
  minus({ dispatch }, { goodsId, umpActivityId, skuId }) {
    dispatch('getItemDetail', { goodsId, umpActivityId }).then((data) => {
      if (data.hasSku) {
        Toast('多规格商品只能在购物车删减哦');
        return;
      }
      dispatch('changeOrder', {
        goodsId,
        delta: -1,
        umpActivityId,
        skuId,
      });
    });
  },

  // 修改数量
  change({ dispatch }, { itemId: goodsId, num, umpActivityId, skuId }) {
    dispatch('getItemDetail', { goodsId, umpActivityId }).then((data) => {
      const { hasSku, id } = data;
      if (!hasSku) {
        dispatch('changeOrder', {
          goodsId: id,
          num,
          umpActivityId,
          skuId,
        });
      }
    });
  },

  changeOrder({ commit, dispatch }, changeData) {
    dispatch('saveCart', changeData)
      .then((res) => {
        commit('setOriginlist', changeData);
        commit('setDetailVisible', false);
        dispatch('checkCartVisible');
        dispatch('preTrade');
      })
      .catch((err) => {
        Toast(err.msg);
      });
  },

  clearOriginlist({ commit, dispatch }) {
    commit('clearOriginlist');
    dispatch('clearCart');
    dispatch('preTrade');
  },

  checkCartVisible({ state, commit }) {
    const { originlist } = state;
    if (originlist.length === 0) {
      commit('setCartVisible', false);
    }
  },

  setLimitDiscountDetail({ state }, limitDiscountDetail) {
    state.limitDiscountDetail = limitDiscountDetail;
  },

  // 点击item显示详情
  showDetail(
    { commit, dispatch },
    { goodsId, umpActivityId, limitDiscountDetail }
  ) {
    if (limitDiscountDetail) {
      dispatch('setLimitDiscountDetail', limitDiscountDetail);
    }
    dispatch('getItemDetail', { goodsId, umpActivityId }).then((data) => {
      const picArr = [...data.pictures];
      if (data?.video?.videoId) {
        picArr.unshift({ url: data.video.coverUrl });
      }
      commit('setGoodsPictures', picArr);
      commit('setDetail', data);
      commit('setDetailVisible', true);
    });
  },

  // 跳转买家订单列表
  navigateToOrder() {
    navigate({
      url: '/packages/groupbuying/buyer-order/index',
    });
  },

  // 跳转团长中心
  navigateToHeaderCenter() {
    navigate({
      url: '/packages/groupbuying/activity/list/index',
    });
  },

  // 跳转个人中心
  navigateToPersonalCenter() {
    switchTab({
      url: '/packages/usercenter/dashboard/index',
    });
  },

  // 获取详情
  getItemDetail({ dispatch }, { goodsId, umpActivityId }) {
    let promiseResolve = null;
    let promiseReject = null;
    const promise = new Promise((resolve, reject) => {
      promiseResolve = resolve;
      promiseReject = reject;
    });
    const found = goodsStore[goodsId];
    if (found) {
      promiseResolve(found);
      const { spu } = mapKeysCase.toCamelCase(found);
      dispatch('fetchGoodsShareCard', {
        price: `￥${(get(spu, 'minPrice') / 100).toFixed(2)}`,
        imgUrl: get(found, 'pictures[0].url'),
        markPrice: get(found, 'origin'),
        alias: get(found, 'alias'),
      });
    } else {
      if (umpActivityId) {
        // 说明是要查看 营销活动商品
        getUmpItemDetail({
          itemId: goodsId,
          storeId,
          umpActivityId,
          umpType: 11,
        })
          .then((data) => {
            const { goods, spu, skuPrices } = mapKeysCase.toCamelCase(data);
            const { id, pictures, video = {} } = goods;
            const { tree } = spu;

            if (skuPrices.length > 1) {
              spu.price = getPriceRange(skuPrices);
            } else {
              spu.price = money(skuPrices[0].promotionPrice).toYuan();
            }
            // 处理sku
            for (let i = 0; i < tree.length; i++) {
              const treeItem = tree[i];
              const ks = treeItem.k_s;
              if (ks === 's1' && treeItem.v) {
                treeItem.v.forEach((one) => {
                  if (one.img_url) {
                    one.imgUrl = one.img_url;
                  }
                });
                break;
              }
            }
            // 处理视频
            if (video.video_id) {
              pictures.unshift(video.cover_url);
            }
            goods.umpActivityId = umpActivityId;
            data = {
              ...goods,
              spu,
              skuPrices,
              hasSku: tree.length > 0,
            };
            // 缓存数据
            goodsStore[id] = data;
            promiseResolve(data);

            dispatch('fetchGoodsShareCard', {
              price: `￥${(get(skuPrices[0], 'promotionPrice') / 100).toFixed(
                2
              )}`,
              imgUrl: get(goods, 'pictures[0].url'),
              markPrice: get(goods, 'origin'),
              alias: get(goods, 'alias'),
            });
          })
          .catch((e) => {
            promiseReject(e.msg);
          });
      } else {
        getItemDetail({
          itemId: goodsId,
          storeId,
        })
          .then((data) => {
            const { goods, spu } = mapKeysCase.toCamelCase(data);
            const { id, pictures, video = {} } = goods;
            const { tree } = spu;
            // 处理sku
            for (let i = 0; i < tree.length; i++) {
              const treeItem = tree[i];
              const ks = treeItem.k_s;
              if (ks === 's1' && treeItem.v) {
                treeItem.v.forEach((one) => {
                  if (one.img_url) {
                    one.imgUrl = one.img_url;
                  }
                });
                break;
              }
            }
            // 处理视频
            if (video.video_id) {
              pictures.unshift(video.cover_url);
            }
            data = {
              ...goods,
              spu,
              hasSku: tree.length > 0,
            };
            // 缓存数据
            goodsStore[id] = data;
            promiseResolve(data);
            dispatch('fetchGoodsShareCard', {
              price: `￥${(get(spu, 'minPrice') / 100).toFixed(2)}`,
              imgUrl: get(goods, 'pictures[0].url'),
              markPrice: get(goods, 'origin'),
              alias: get(goods, 'alias'),
            });
          })
          .catch((e) => {
            promiseReject(e.msg);
          });
      }
    }
    return promise;
  },

  // 获取活动分享卡片
  fetchGoodsShareCard({ state, commit }, detail) {
    const { imgUrl, price = '', markPrice, alias } = detail;
    const { state: activityState } = state;
    const [yuan, cent] = price.replace('￥', '').split('.');

    return fetchGoodsShareCard({
      imgUrl,
      yuan,
      cent,
      markPrice: markPrice ? `￥${markPrice}` : '',
      title: activityState === 1 ? '立即参团' : '立即查看',
    }).then((res) => {
      commit('setShareGoodsImg', {
        alias,
        url: res.value,
      });
    });
  },

  // 买家相关信息
  getGoodsCustomers({ state, commit }) {
    const { goodslist, activityId, headerBuyerId } = state;
    const goodsIds = goodslist.map((one) => one.itemId);
    if (activityId && headerBuyerId && goodsIds && goodsIds.length > 0) {
      queryGoodsCustomers({
        goodsIds,
        activityId,
        headerBuyerId,
      }).then((data) => {
        const goodslistWithConstomers = goodslist.map((one) => {
          const { itemId } = one;
          const customer = data[itemId];
          if (customer) {
            return {
              ...one,
              customerCount: customer.count,
              customerAvatar: customer.customers,
            };
          }
          return {
            ...one,
            customerCount: 0,
            customerAvatar: [],
          };
        });
        commit('setGoodslist', goodslistWithConstomers);
      });
    }
  },

  // 领取优惠券
  receiveCoupon({ commit }, id) {
    return receiveCoupon({
      activityId: id,
    }).then(() => {
      commit('setCouponReceived', id);
    });
  },
  ...globalActions,
  ...tradeActions,
  ...cartActions,
  ...subscribeActions,
};

export const store = {
  state,
  getters,
  mutations,
  actions,
};
