import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import fullfillImage from '@youzan/weapp-utils/lib/cdn-image';
import get from '@youzan/weapp-utils/lib/get';
import Toast from '@vant/weapp/dist/toast/toast';
import { checkHqStore } from '@youzan/utils-shop';
import { getBuyUrl } from '../../../common';
import { handleChangeStore } from '../../utils/chain';
import {
  getAllInfoApi,
  getLocation,
  accessHeader,
  getSettingApi,
  getCurrentHeader,
  fetchCouponList,
  queryAnotherActivity,
  getCategories,
  getLastAccessHeaderV2,
  getRadiusRecommendHeader,
  fetchShareCard,
} from '../../api';
import {
  getLimitDiscountDetail,
} from '../../../activity/api';

const app = getApp();

// 进店逻辑
export const actions = {
  // 尝试获取活动详情，根据获取结果设置状态展示
  init({ dispatch, commit }, payload) {
    return dispatch('initActDetail', payload).then((detail) => {
      let showItemCategoryFlag = false;
      if (!detail.groupHeaderDetail) {
        throw new Error('无团长信息');
      }
      if (detail && detail.activityDetail) {
        const {
          activityDetail: { alias, id: activityId, showItemCategory },
          groupHeaderDetail: { headerBuyerId },
        } = detail;
        commit('setQuery', {
          headerBuyerId,
          alias,
          activityId,
        });
        showItemCategoryFlag = showItemCategory;
        commit('setShowItemCategory', showItemCategory);
        dispatch('fetchCouponList');
      } else {
        commit('setEmpty');
      }
      commit('setActDetail', detail);
      dispatch('accessHeader', detail.groupHeaderDetail.headerBuyerId);
      if (showItemCategoryFlag) {
        dispatch('fetchCategoryInfo');
      }
      dispatch('fetchLimitDiscountInfo');
      const imgUrl = get(detail, 'activityDetail.imgUrl');
      if (imgUrl) {
        commit('setAcitivityShareCard', imgUrl);
      } else {
        dispatch('fetchActivityShareCard', detail)
          .then((res) => {
            commit('setAcitivityShareCard', res.value);
          })
          .catch(() => {});
      }

      const {
        activityDetail: { id: activityId },
      } = detail;
      return getLimitDiscountDetail({
        groupBuyActivityId: activityId,
      }).then((data) => {
        // const
        commit('setLimitDiscountList', data);
        if (payload.umpActivityId) {
          const currentLimitDiscount = data.find(item => item.id === Number(payload.umpActivityId))
          dispatch('setLimitDiscountDetail', {
            startTime: currentLimitDiscount.startTime,
            endTime: currentLimitDiscount.endTime,
            status: currentLimitDiscount.status
          })
        }
        return detail;
      });
    });
  },

  // 获取活动详情
  initActDetail({ dispatch, commit }, payload) {
    const { headerBuyerId, alias, activityId } = payload;

    if (headerBuyerId && alias && activityId) {
      return dispatch('getActDetail', payload);
    }
    commit('setSwitch', true);
    // 有团长标示 或 什么都没有
    return dispatch('getWithHeaderOrLastVisit', headerBuyerId);
  },

  getWithHeaderOrLastVisit({ dispatch }, headerBuyerId) {
    // 先看看是不是之前有没有访问过团长
    const params = {};
    if (headerBuyerId) {
      params.headerBuyerId = headerBuyerId;
    }
    return getLastAccessHeaderV2(params)
      .then(
        ({
          recommendHeader: groupHeaderDetail,
          recommendHeaderActivity: activityDetail,
        }) => {
          if (groupHeaderDetail || activityDetail) {
            return {
              groupHeaderDetail,
              activityDetail,
            };
          }
          return dispatch('tryToFindDetailWithLocation');
        }
      )
      .then(({ groupHeaderDetail, activityDetail }) => {
        return app.getShopInfo().then(({ shopMetaInfo = {} }) => {
          let needRedirect = false;
          // 总部情况下，找到不是自己的活动/团长，做跳转
          const isHqStore = checkHqStore(shopMetaInfo);
          const kdtId = app.getKdtId();
          if (isHqStore) {
            const {
              headerBuyerId: subHeaderBuyerId,
              kdtId: subKdtId,
            } = groupHeaderDetail;
            if (subKdtId !== kdtId) {
              needRedirect = true;
              // 换店relaunch
              handleChangeStore({
                redirectUrl: `/packages/groupbuying/buyer-trade/buying/index?headerBuyerId=${subHeaderBuyerId}`,
                kdtId: subKdtId,
              });
              Toast.loading();
              // 等待重定向
              // 可能对内存不友好
              return new Promise(() => {});
            }
          }
          // 无论如何，这种查询最终要么有团长，要么有团长有活动alias
          if (activityDetail && activityDetail.alias) {
            return dispatch('getActDetail', {
              headerBuyerId: groupHeaderDetail.headerBuyerId,
              alias: activityDetail.alias,
              activityId: activityDetail.id,
            });
          }
          if (!needRedirect) {
            return {
              groupHeaderDetail,
            };
          }
        });
      });
  },

  // 没有带任何参数的情况下，先判断之前是否访问过团购
  // 访问过，根据上一次团长进店
  // 没有访问过，根据经纬度找活动
  tryToFindDetailWithLocation({ dispatch }) {
    return getLocation()
      .then((data) => {
        if (data && data.lon && data.lat) {
          const { lon, lat } = data;
          return getRadiusRecommendHeader({
            lon,
            lat,
          });
        }
        throw new Error('无法获取定位');
      })
      .then(
        ({
          recommendHeader: groupHeaderDetail,
          recommendHeaderActivity: activityDetail,
        }) => {
          Toast.clear();
          dispatch('showNear');
          return {
            groupHeaderDetail,
            activityDetail,
          };
        }
      );
  },

  // 获取活动设置
  getActDetail(store, payload) {
    const { headerBuyerId, alias, activityId, umpActivityId, umpType } = payload;
    return getAllInfoApi({
      headerBuyerId,
      alias,
      activityId,
      umpActivityId,
      umpType
    }).then((data) => {
      data = mapKeysCase.toCamelCase(data);
      return data;
    });
  },

  // 获取活动分享卡片
  fetchActivityShareCard(store, detail) {
    const { activityDetail, items } = detail;
    const { state } = activityDetail;
    let showItems = items;
    if (items.length > 3) {
      showItems = items.slice(0, 3);
    }

    return fetchShareCard({
      items: showItems.map((item) => {
        return {
          price: `￥${(item.price / 100).toFixed(2)}`,
          imgUrl: fullfillImage(get(item, 'picture[0].url'), '!180x0.jpg'),
        };
      }),
      title: state === 1 ? '立即参团' : '立即查看',
      goodsNum: showItems.length,
    });
  },

  // 获取店铺设置
  fetchSetting({ commit }) {
    let resolve = null;
    const p = new Promise((r) => (resolve = r));
    Promise.all([getSettingApi(), getCurrentHeader()])
      .then(([setting, headerInfo]) => {
        const {
          isSwitchOn,
          isDisplayMobile = true,
          templateModel = 0,
          featureAlias = '',
        } = setting;
        isSwitchOn && commit('setSwitch', true);
        isDisplayMobile && commit('setMobileShow', true);
        headerInfo && headerInfo.id && commit('setIsHeader');
        commit('setTemplateModel', templateModel);
        commit('setFeatureAlias', featureAlias);
        resolve();
      })
      .catch(() => {
        resolve();
      });
    return p;
  },

  // 获取优惠券列表
  fetchCouponList({ state, commit }) {
    const { activityId } = state;
    fetchCouponList({
      activityId,
    }).then((data) => {
      const list = data.list.map((one) => ({
        id: one.id,
        alias: one.alias,
        title: one.title,
        use: one.thresholdAmount,
        value: one.value,
        valid: one.validityDesc,
        isReceived: false,
      }));
      commit('setCouponList', list);
    });
  },

  // 显示附近
  showNear({ commit }) {
    // 3秒后自动消失
    setTimeout(() => {
      commit('setNear', true);
      setTimeout(() => {
        commit('setNear', false);
      }, 3000);
    }, 500);
  },

  // 记录进店团长
  accessHeader(store, headerBuyerId) {
    if (headerBuyerId) {
      accessHeader({
        headerBuyerId,
      });
    }
  },

  // 获取分享出去的商品
  showShareDetail({ state, dispatch }, alias) {
    const { goodslist = [], limitDiscountList = [] } = state;
    const allGoods = limitDiscountList
      .map((item) => {
        const { goodsList } = item;
        return goodsList;
      })
      .reduce((prev, curr) => prev.concat(curr), [])
      .concat(goodslist);
    const find = allGoods.find((one) => one.alias === alias);
    if (find) {
      dispatch('showDetail', { goodsId: find.itemId });
    }
  },

  fetchNextAct({ state, commit }) {
    const { actDetail, headerBuyerId } = state;
    const { state: actState } = actDetail;
    // 老模版没有新团提示
    if (actState === 2) {
      queryAnotherActivity({
        headerBuyerId,
      }).then((res) => {
        if (res && res.items) {
          commit('setNextAct', res);
          commit('setNextActVisible', true);
        }
      });
    }
  },

  enterNextAct({ state }) {
    const { nextAct, headerBuyerId } = state;
    const { alias, id } = nextAct;
    const url = getBuyUrl({
      headerBuyerId,
      alias,
      activityId: id,
    });
    wx.redirectTo({
      url,
    });
  },

  fetchCategoryInfo({ state, commit }) {
    const { activityId } = state;
    getCategories({
      activityId,
    }).then((res) => {
      const { list } = res;
      const nextList = list.filter((one) => one.itemNum > 0);
      const all = [
        {
          categoryId: -1,
          categoryName: '全部',
        },
      ];
      if (nextList.length > 0) {
        commit('setCategoryList', all.concat(nextList));
      }
    });
  },

  // 获取限时折扣活动信息
  fetchLimitDiscountInfo({ state, commit }) {
    const { activityId } = state;
    getLimitDiscountDetail({
      groupBuyActivityId: activityId,
    }).then((data) => {
      // const
      commit('setLimitDiscountList', data);
      if (data.length) {
        app.logger && app.logger.log({
          et: 'view', // 事件类型
          ei: 'limited_discount', // 事件标识
          en: '限时折扣活动', // 事件名称
          pt: 'cgroupon', // 页面类型

          params: {
            is_limited_discount: 1
          } // 事件参数
        });
      }
    });
  },
};
