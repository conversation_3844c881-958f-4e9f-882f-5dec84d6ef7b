import Toast from '@vant/weapp/dist/toast/toast';
import get from '@youzan/weapp-utils/lib/get';
import money from '@youzan/weapp-utils/lib/money';
import { preTradeApi, createTradeApi, cache } from '../../api';

const app = getApp();
let payLock = false;

export const actions = {
  // 预下单
  preTrade({ state, dispatch }) {
    const { templateModel } = state;
    if (templateModel === 0) {
      dispatch('template-1-preTrade');
    }
  },

  // 老模版预下单
  'template-1-preTrade': function ({ state, commit }) {
    const {
      alias, activityId, headerBuyerId, originlist
    } = state;
    commit('setPayStatus', 1);
    preTradeApi({
      alias,
      activityId,
      headerBuyerId,
      items: originlist
    })
      .then(data => {
        const realPay = get(data, 'order_payment.real_pay', 0);
        const forbidPay = get(data, 'display_config.forbid_pay', true);
        let status = forbidPay ? 0 : 2;
        const items = get(data, 'shop_item.unavailable_items', []);
        const enableCoupons = get(data, 'coupons', []);
        const disableCoupons = get(data, 'unavailable_coupons', []);

        if (items[0]) {
          // 失败
          status = 0;
          const item = items[0];
          const { unavailable_desc: desc, title } = item;
          Toast(`商品${title}：${desc}`);
        }
        commit('setRealPay', money(realPay).toYuan());
        commit('setPayStatus', status);
        // 处理优惠券
        commit('setOrderCouponList', { enableCoupons, disableCoupons });
        // 重置优惠券
        commit('setCoupon', {});
      })
      .catch(err => {
        commit('setPayStatus', 0);
        Toast(err.msg);
      });
  },

  // 下单
  createTrade({ state }) {
    let resolve;
    let reject;
    const p = new Promise((r1, r2) => {
      resolve = r1;
      reject = r2;
    });
    const {
      alias, activityId, headerBuyerId, originlist, buyerMsg, mobile, coupon
    } = state;
    const params = {
      alias,
      activityId,
      headerBuyerId,
      items: originlist,
      mobile,
      buyerMsg,
      orderMark: 'wx_shop',
    };
    if (coupon.id) {
      params.ump = {
        coupon: {
          couponType: coupon.type,
          id: coupon.id,
          kdtId: app.getKdtId()
        }
      };
    }
    createTradeApi(params).then(data => {
      resolve(data);
    }).catch(e => {
      reject(e);
    });
    return p;
  },

  // 缓存下单参数
  cache({ state }) {
    if (payLock) return;
    payLock = true;
    const {
      alias, activityId, headerBuyerId, originlist
    } = state;
    cache({
      alias,
      activityId,
      headerBuyerId,
      items: originlist,
      orderMark: 'wx_shop'
    })
      .then((res) => {
        const { bookKey } = res
        payLock = false;
        wx.navigateTo({
          url: `/packages/groupbuying/buyer-trade/pay/index?bookKey=${bookKey}&alias=${alias}&activityId=${activityId}&headerBuyerId=${headerBuyerId}`
        });
      })
      .catch(e => {
        payLock = false;
        Toast(e.msg || e);
      });
  }
};
