import money from '@youzan/weapp-utils/lib/money';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { getCart, operateCart, clearCart } from '../../api';

export const actions = {
  // 保存本次商品数据
  saveCart({ state }, { skuId, goodsId, num, delta = 0, umpActivityId }) {
    const { templateModel, originlist, activityId } = state;
    // 仅对新模版生效
    if (templateModel === 0) return;
    const found = originlist.find((one) => {
      if (skuId) {
        return skuId === one.skuId && goodsId === one.goodsId;
      }
      return goodsId === one.goodsId;
    });
    let promiseResolve = null;
    let promiseReject = null;
    const promise = new Promise((resolve, reject) => {
      promiseResolve = resolve;
      promiseReject = reject;
    });
    operateCart({
      activityId,
      cartItem: {
        itemId: goodsId,
        num: found ? found.num + delta : num ? num : 0 + delta,
        skuId: skuId || 0,
        umpActivityId,
      },
    })
      .then((res) => {
        promiseResolve(res);
      })
      .catch((err) => {
        promiseReject(err);
      });
    return promise;
  },

  // 还原
  restoreCart({ state, dispatch, commit }) {
    const { templateModel, activityId } = state;
    // 仅对新模版生效
    if (templateModel === 0) return;
    getCart({
      activityId,
    }).then((data) => {
      const { cartItems = [] } = data || {};
      const next = cartItems
        .filter((one) => {
          const {
            cartItemDetail: { stocks: list = [] } = {},
            skuId,
            umpActivityId,
          } = one;
          if (list.length > 0 && skuId) {
            // 有规格
            return list.some((listSku) => listSku.id === skuId);
          }
          // 无规格情况
          return umpActivityId
            ? list.length === 0
            : list.length === 0 && !skuId;
        })
        .map((one) => {
          const { cartItemDetail, itemId: id } = one;
          const info = {
            id,
            title: cartItemDetail.title,
            spu: {
              price: one.promotionPrice
                ? money(one.promotionPrice).toYuan()
                : money(cartItemDetail.price).toYuan(),
              stockNum: cartItemDetail.totalStock,
              list: mapKeysCase.toCamelCase(cartItemDetail.stocks),
            },
            isLimitDiscount: one.promotionPrice ? true : false,
            quota: cartItemDetail.quota,
            quotaUsed: cartItemDetail.usedQuota,
          };
          return {
            goodsId: one.itemId,
            num: one.num || 0,
            skuId: one.skuId || null,
            umpActivityId: one.umpActivityId || null,
            info,
          };
        });
      commit('restoreOriginlist', next);
    });
  },

  // 清空购物车
  clearCart({ state }) {
    const { templateModel, activityId } = state;
    // 仅对新模版生效
    if (templateModel === 0) return;
    clearCart({
      activityId,
    });
  },
};
