<page-container class="{{ themeClass }} page-{{ deviceType }}" fixed-bottom="{{ templateModel === 1 }}">
  <view wx:if="{{ actDetail || headerDetail }}" class="buying">
    <template-1 wx:if="{{ templateModel === 0 }}" bind:trade="payToHeader" />
    <template-2 wx:else bind:trade="_cache" />
  </view>
  <!-- 没有团购 -->
  <empty />
  <van-toast z-index="{{ 10000 }}" id="van-toast" />
  <van-dialog id="van-dialog" />
  <!-- 分享 -->
  <share-wrap />
  <shop-pop-manager source="{{ 3 }}" />
</page-container>
