import args from '@youzan/weapp-utils/lib/args';
import Event from '@youzan/weapp-utils/lib/event';
import throttle from '@youzan/weapp-utils/lib/throttle';
import { mapState, mapMutations, mapActions } from '@youzan/vanx';
import { VanxPage } from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import get from '@youzan/weapp-utils/lib/get';
import isPhone from '@youzan/weapp-utils/lib/validators/is-phone';
import { logPageEnter } from 'utils/log/logv3';
import { store } from '@/packages/groupbuying/buyer-trade/buying/store/index';
import { getPhone } from '../api';
import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';

const app = getApp();
let i = 1;

Toast.setDefaultOptions({ zIndex: 10000 });

VanxPage({
  store() {
    this.name = `communitybuy-buy-${i}`;
    i++;
    store.name = this.name;
    return store;
  },

  mapData: {
    ...mapState([
      'actDetail',
      'headerDetail',
      'templateModel',
      'fixedRollOrder',
      'featureAlias',
    ]),
  },

  ...mapMutations([
    'setFixedRollOrder',
    'setMobile',
    'setBuyerMsg',
    'setTemplateModel',
    'clearStore',
    'clearGoodsStore',
  ]),

  ...mapActions([
    'init',
    'fetchSetting',
    'getGoodsCustomers',
    'restoreCart',
    'checkSubscribe',
    'fetchNextAct',
    'showShareDetail',
    'cache',
    'createTrade',
  ]),

  logConfig: {
    isAsync: true,
  },

  onLoad(options) {
    const { kdtId, guestKdtId } = options;
    if (guestKdtId && kdtId !== guestKdtId && guestKdtId.trim() !== '') {
      app.updateKdtId(guestKdtId, false, {
        mark: '905',
      });
    }

    this.options = options;
    this.query = wx.createSelectorQuery();

    this.query
      .select('.roll-order-new')
      .boundingClientRect()
      .selectViewport()
      .scrollOffset();
    this.query
      .select('.goods-list')
      .boundingClientRect()
      .selectViewport()
      .scrollOffset();

    this.query.select('.buying >>> .order-list').boundingClientRect();
    this.query.select('.buying >>> .pay').boundingClientRect();
  },

  onShow() {
    if (wx.canIUse('hideHomeButton')) {
      wx.hideHomeButton();
    }
    const redirectUrl = args.add('/' + this.route, this.options);
    // 连锁等待进店完成
    autoEnterShop({ ...this.options, redirectUrl, shopAutoEnter: 2, logTag: 'groupbuying' }).then((res) => {
      this.initData(this.options);
    })
  },

  onReachBottom() {
    // Event.trigger('buying:onListReachBottom');
  },

  getStoreState() {
    return this._module.state;
  },

  onPageScroll: throttle(function () {
    this.query.exec((res) => {
      const { windowHeight } = wx.getSystemInfoSync();
      const orderListHeight = res[4].height;

      if (res[1].scrollTop >= 130) {
        !this.data.fixedRollOrder && this.setFixedRollOrder(true);
      } else {
        this.data.fixedRollOrder && this.setFixedRollOrder(false);
      }

      if (
        res[1].scrollTop + windowHeight >=
        res[1].scrollHeight - orderListHeight - 136 * 6 // 离可视区域6个商品时（单个商品模块高136）开始加载，让用户无感知。
      ) {
        Event.trigger('buying:onListReachBottom');
      }
    });
  }, 100), // 改为100ms，更灵敏防止加载过慢

  initData(options) {
    const {
      headerBuyerId,
      alias,
      activityId,
      itemAlias,
      shareUid,
      umpActivityId,
    } = options;

    // 先获取设置
    this.fetchSetting().then(() => {
      // 是微页面模板，并且带 featureAlias 时跳微页面
      if (this.data.templateModel === 2) {
        if (this.data.featureAlias) {
          this.redirectToFeature(headerBuyerId, activityId);
          return;
        }
        // 没有微页面alias时（可能微页面被删除），降级到电商模版
        this.setTemplateModel(1);
      }

      Toast.loading();

      // 获取手机号逻辑变更
      this.fetchDefaultPhone();

      const params = {
        activityId,
        alias,
        headerBuyerId,
        umpActivityId,
      };
      if (umpActivityId) {
        params.umpActivityId = umpActivityId;
        params.umpType = 11;
      }
      // 由于vanx mapActions找不到对应action时会对对应action的执行结果返回undefined，导致此处报错，所以调用使用?.
      // 复现方式：fetchSetting中的请求pending时，点击小程序后退，此页面store被销毁
      this.init(params)
        ?.then((detail) => {
          if (detail && detail.activityDetail) {
            this.handleInitSuccess(itemAlias, shareUid);
          }
        })
        .catch(this.handleInitFail);
    });
  },

  redirectToFeature(headerBuyerId, activityId) {
    let redirectPath = `/packages/home/<USER>/index?alias=${this.data.featureAlias}`;
    if (headerBuyerId && activityId) {
      redirectPath = args.add(redirectPath, {
        headerBuyerId,
        activityId,
      });
    }
    wx.redirectTo({
      url: redirectPath,
    });
  },

  handleInitSuccess(itemAlias, shareUid) {
    Toast.clear();
    wx.setNavigationBarTitle({
      title: this.data.actDetail.name,
    });
    wx.stopPullDownRefresh();
    // 日志埋点
    logPageEnter(
      'packages/groupbuying/buyer-trade/buying/index',
      this.data.actDetail.id,
      {
        act_type: 10002,
        act_id: `${this.data.actDetail.id}${this.data.headerDetail.headerBuyerId}`,
        agent_id: this.data.headerDetail.headerBuyerId,
        groupbuy_act_id: this.data.actDetail.id,
        is_share: shareUid ? 1 : 0,
        share_uid: shareUid,
      },
      'cgroupon'
    );
    this.getGoodsCustomers();
    this.restoreCart();
    this.checkSubscribe();
    this.fetchNextAct();
    if (itemAlias) {
      this.showShareDetail(itemAlias);
    }
  },

  handleInitFail() {
    wx.navigateTo({
      url: '/packages/groupbuying/location/index',
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    const { detailVisible, cartVisible, couponListVisible } =
      this.getStoreState();
    if (detailVisible || cartVisible || couponListVisible) {
      wx.stopPullDownRefresh();
      return;
    }
    this.clearStore();
    this.clearGoodsStore();
    this.initData(this.options);
  },

  _cache() {
    const { originlist } = this.getStoreState();
    if (!originlist.length) {
      Toast('还没有选购商品');
      return;
    }
    Toast.loading();
    this.cache();
  },

  payToHeader() {
    const {
      originlist,
      mobile,
      buyerMsg = '',
      payStatus,
    } = this.getStoreState();
    if (!originlist.length) {
      Toast('还没有选购商品');
      return;
    }
    if (!mobile || !isPhone(mobile)) {
      Toast('请填写有效手机号');
      return;
    }
    if (buyerMsg.length > 200) {
      Toast('备注不能超过200字');
      return;
    }
    if (payStatus !== 2) return;
    Toast.loading();
    this.createTrade()
      .then((data) => {
        // 日志埋点
        this.logTrade();
        this.navigateToOrderDetail(data.order_no);
      })
      .catch((e) => {
        Toast(e.msg || e);
      });
  },

  onShareAppMessage() {
    const { actDetail, headerDetail } = this.data;
    const { detailVisible, detailInfo, shareActivityImg, shareGoodsImgs } =
      this.getStoreState();
    let { name: title } = actDetail;
    const { alias, id: activityId } = actDetail;
    const { headerBuyerId } = headerDetail;
    const kdt_id = app.getKdtId();
    let itemAlias = '';
    let umpActivityId = '';
    let imageUrl = shareActivityImg;

    if (detailVisible) {
      title = get(detailInfo, 'title', '');
      itemAlias = get(detailInfo, 'alias', '');
      umpActivityId = get(detailInfo, 'umpActivityId', '');
      imageUrl =
        shareGoodsImgs[itemAlias] ||
        'https://img.yzcdn.cn/upload_files/2020/10/23/Fnt7Tza0g_qf7GFtm3gpersG-S6j.png';
    }

    const path = `packages/groupbuying/buyer-trade/buying/index?kdt_id=${kdt_id}&headerBuyerId=${headerBuyerId}&alias=${alias}&activityId=${activityId}&itemAlias=${itemAlias}&umpActivityId=${umpActivityId}&forbidBack=1`;
    return {
      imageUrl,
      title,
      path,
    };
  },

  onShareTimeline() {
    const { actDetail, headerDetail } = this.data;
    const { detailVisible, detailInfo, shareActivityImg, shareGoodsImgs } =
      this.getStoreState();
    let { name: title } = actDetail;
    const { alias, id: activityId } = actDetail;
    const { headerBuyerId } = headerDetail;
    const kdt_id = app.getKdtId();

    let itemAlias = '';
    let umpActivityId = '';
    let imageUrl = shareActivityImg;

    if (detailVisible) {
      title = get(detailInfo, 'title', '');
      itemAlias = get(detailInfo, 'alias', '');
      umpActivityId = get(detailInfo, 'umpActivityId', '');
      imageUrl =
        shareGoodsImgs[itemAlias] ||
        'https://img.yzcdn.cn/upload_files/2020/10/23/Fnt7Tza0g_qf7GFtm3gpersG-S6j.png';
    }

    return {
      imageUrl,
      title,
      query: `kdt_id=${kdt_id}&headerBuyerId=${headerBuyerId}&alias=${alias}&activityId=${activityId}&itemAlias=${itemAlias}&umpActivityId=${umpActivityId}&forbidBack=1`,
    };
  },

  getCoverImage() {
    const { actDetail = {} } = this.data;
    const { imgUrl = '' } = actDetail;
    return imgUrl;
  },

  onPhoneChange(e) {
    this.setMobile(e.detail);
  },

  onBuyerMsgChange(e) {
    this.setBuyerMsg(e.detail);
  },

  fetchDefaultPhone() {
    getPhone().then((phone) => {
      this.setMobile(phone.value);
    });
  },

  navigateToOrderDetail(orderNo) {
    wx.navigateTo({
      url: `/packages/groupbuying/buyer-trade/detail/index?orderNo=${orderNo}&fromBuy=1`,
    });
  },

  logTrade() {
    setTimeout(() => {
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'cgroupon_order',
          en: '确认下单',
          params: {
            act_type: 10002,
            act_id: `${this.data.actDetail.id}${this.data.headerDetail.headerBuyerId}`,
            agent_id: this.data.headerDetail.headerBuyerId,
            groupbuy_act_id: this.data.actDetail.id,
          },
          si: app.getKdtId(),
        });
    }, 0);
  },
});
