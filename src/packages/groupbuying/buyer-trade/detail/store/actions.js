import Toast from '@vant/weapp/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
/* eslint-disable-next-line */
import { moment as formatDate } from 'utils/time';
import { verifyOrder, pay, queryOrder } from '../api';
import defaultDesign from '../default-design.json';
import customDesign from '../design.json';

export default {
  initOrderDetail(
    { commit, dispatch },
    { orderNo, isHeaderQuery, fromBuy, isPay, kdtId }
  ) {
    commit('setBaseData', {
      orderNo,
      isHeaderQuery,
      fromBuy,
      isPay,
      kdtId,
    });
    dispatch('fetchOrder');
  },

  fetchOrder({ state, commit, dispatch }) {
    const { orderNo, fromBuy, kdtId } = state;
    queryOrder({
      orderNo,
      kdt_id: kdtId,
    })
      .then((res) => {
        commit('setIsHeader', res.isHeader);
        commit('setOrderDetail', res.detailData);
        commit('setQrCode', res.qrCode);
        commit('setStateKv');
        commit('setLoading', false);
        if (fromBuy && res.detailData.state === 3) {
          if (res.detailData.payment === 0) {
            // 零元单的情况，需要更改订单状态
            commit('setState', 5);
          } else {
            dispatch('pay');
          }
        }
        dispatch('getDesign');
      })
      .catch((e) => {
        commit('setDisable', e.msg);
        commit('setLoading', false);
      });
  },

  // 确认收货
  confirmFetch({ state, commit }) {
    const { orderNo, headerBuyerId } = state;
    verifyOrder({
      orderNo,
      headerBuyerId,
    })
      .then((data) => {
        if (data) {
          commit('setVerifyAt', formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'));
          commit('setStateKv');
          return;
        }
        Toast('提货失败');
      })
      .catch((e) => {
        Toast(e.msg || e);
      });
  },

  // 支付
  pay({ state, commit }) {
    const { orderNo } = state;
    Toast.loading();
    pay({
      orderNo,
    })
      .then((res) => {
        Toast.clear();
        const data = mapKeysCase.toCamelCase(res);
        commit('setPayParams', data);
        setTimeout(() => {
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          const payWaysRef = currentPage.selectComponent('#zanPayWrapperRef');
          payWaysRef.startPay();
        }, 300);
      })
      .catch((e) => {
        Toast(e.msg || e);
      });
  },

  // 获取design
  getDesign({ state, commit }) {
    const { isHeader } = state;
    const defaultConfig = isHeader
      ? defaultDesign.headerComponents
      : defaultDesign.buyerComponents;
    const modList = customDesign.design || defaultConfig;
    const config = modList[0] && modList[0].type === 'config' && modList[0];
    const newDesign = config ? modList.slice(1) : modList;

    commit('setDesign', newDesign);
  },
};
