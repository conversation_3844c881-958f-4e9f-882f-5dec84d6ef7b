import actions from './actions';
import { formatDetail, getStateKv } from '../common';
import { canRefund } from '../../../common';

const state = {
  // 支付数据
  payParams: {},
  loading: true,
  stateKv: null,
  isHeader: false,
  verifyAt: 0,
  orderNo: '',
  disable: false,
  design: [],
  payNo: 0,
  verifyCode: '',
  headerAvatar: '',
  headerName: '',
  headerFetchAddress: '',
  headerMobile: '',
  stateDesc: '',
  createdAt: '',
  payTime: '',
  buyerMsg: '',
  decrease: 0,
  originPrice: '',
  payment: '',
  items: [],
};

const getters = {};

const mutations = {
  setLoading(state, isLoading) {
    state.loading = isLoading;
  },

  setVerifyAt(state, time) {
    state.verifyAt = time;
    state.state = 100;
  },

  setState(state, nextState) {
    state.state = nextState;
  },

  setBaseData(state, { orderNo, isHeaderQuery, fromBuy, isPay, kdtId }) {
    state.orderNo = orderNo;
    state.isHeaderQuery = isHeaderQuery;
    state.fromBuy = fromBuy;
    state.isPay = isPay;
    state.kdtId = kdtId;
  },

  setOrderDetail(state, orderDetail) {
    const { isHeader } = state;
    const { settleState, state: activityState } = orderDetail;
    const showRefund = canRefund(activityState, settleState, !isHeader);
    orderDetail = formatDetail(orderDetail, showRefund);
    Object.keys(orderDetail).forEach((one) => {
      state[one] = orderDetail[one];
    });
  },

  setQrCode(state, qrCode) {
    state.qrCode = qrCode;
  },

  setStateKv(state) {
    state.stateKv = getStateKv(state);
  },

  setIsHeader(state, isHeaderData) {
    const { isHeaderQuery } = state;
    state.isHeader = isHeaderQuery && isHeaderData;
  },

  setPayParams(state, payParams) {
    state.payParams = payParams;
  },

  setDisable(state, disableText) {
    state.disable = disableText;
  },
  setDesign(state, payload) {
    state.design = payload;
  },
};

export const store = {
  state,
  mutations,
  getters,
  actions
};
