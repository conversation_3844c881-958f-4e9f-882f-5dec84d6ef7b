/* eslint-disable-next-line */
import { moment as formatDate } from 'utils/time';
import { canFetch } from '../../../common';

const headerStateMap = [
  {
    test(detail) {
      return detail.feedback === 201;
    },
    result: {
      stateText: '退款中'
    }
  },
  {
    test(detail) {
      return detail.state === 99;
    },
    result: {
      isFinished: true,
      stateText: '订单已关闭',
      stateDesc(detail) {
        return detail.closeReason;
      }
    }
  },
  {
    test(detail, state) {
      return detail.state === 3 && state.isPay;
    },
    result: {
      stateText: '订单处理中'
    }
  },
  {
    test(detail, state) {
      return detail.state === 3 && !state.isPay;
    },
    result: {
      stateText: '等待买家付款',
      stateDesc: '若未在[time]内付款，订单将自动关闭'
    }
  },
  {
    test(detail) {
      return detail.state === 5;
    },
    result: {
      stateText: '支付成功'
    }
  },
  {
    test(detail) {
      return canFetch(detail.state, detail.verifyAt);
    },
    result: {
      stateText: '待提货',
      stateDesc: '请及时通知用户前往提货地址提货'
    }
  },
  {
    test(detail) {
      return detail.state === 100 && detail.verifyAt;
    },
    result: {
      isFinished: true,
      stateText: '已提货',
      stateDesc(detail) {
        return `提货时间：${detail.verifyAt}`;
      }
    }
  }
];

const buyerStateMap = [
  {
    test(detail) {
      return detail.feedback === 201;
    },
    result: {
      stateText: '退款中'
    }
  },
  {
    test(detail) {
      return detail.state === 99;
    },
    result: {
      isFinished: true,
      stateText: '订单已关闭',
      stateDesc(detail) {
        return detail.closeReason;
      }
    }
  },
  {
    test(detail, state) {
      return detail.state === 3 && state.isPay;
    },
    result: {
      stateText: '订单处理中'
    }
  },
  {
    test(detail, state) {
      return detail.state === 3 && !state.isPay;
    },
    result: {
      stateText: '等待买家付款',
      stateDesc: '若未在[time]内付款，订单将自动关闭'
    }
  },
  {
    test(detail) {
      return detail.state === 5;
    },
    result: {
      stateText: '支付成功，等待商家发货',
      stateDesc: '请在收到团长通知后，前往提货地址提货'
    }
  },
  {
    test(detail) {
      return canFetch(detail.state, detail.verifyAt);
    },
    result: {
      stateText: '待提货',
      stateDesc: '请在收到团长通知后，前往提货地址提货'
    }
  },
  {
    test(detail) {
      return detail.state === 100 && detail.verifyAt;
    },
    result: {
      isFinished: true,
      stateText: '已提货',
      stateDesc(detail) {
        return `提货时间：${detail.verifyAt}`;
      }
    }
  }
];

export { headerStateMap, buyerStateMap };
