@import "../../style/_pannel.scss";

.item-list {
  .title {
    display: flex;
    align-items: center;
    height: 40px;

    image {
      height: 13px;
      width: 15px;
    }

    text {
      margin-left: 8px;
      line-height: 20px;
      font-size: 14px;
      color: #323233;
    }
  }

  .result {
    padding: 12px 0;
    font-size: 14px;
    border-bottom: 1px solid #ebedf0;

    &-item {
      display: flex;
      justify-content: space-between;
      margin-top: 12px;
      height: 20px;
      line-height: 20px;

      &-title {
        color: #969799;
      }

      &-value {
        color: #323233;
      }

      &:first-child {
        margin-top: 0;
      }
    }
  }

  .result-total {
    display: flex;
    align-items: center;
    height: 44px;
    line-height: 44px;
    justify-content: flex-end;

    > text {
      font-size: 14px;
      color: #323233;
    }
  }
}
