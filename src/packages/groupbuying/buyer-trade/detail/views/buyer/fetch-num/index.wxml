<wxs src="./computed.wxs" module="computed" />

<view
  wx:if="{{ verifyCode }}"
  class="order-detail-pannel fetch-num {{ computed.isDisabled(verifyAt, state, payTime) ? 'fetch-num--disabled' : '' }}"
>
  <view class="left">
    <view class="label">提货序号{{ computed.isShow(verifyCode) }}</view>
    <view class="value">{{ payNo }}</view>
  </view>
  <view class="right">
    <image
      wx:if="{{ computed.isDisabled(verifyAt, state, payTime) }}"
      src="https://img.yzcdn.cn/public_files/2019/07/04/11a751d1dd385763b045320e7922d6de.png"
    />
    <image
      wx:else
      src="https://img.yzcdn.cn/public_files/2019/07/04/f0761cef146ce56d6a0313240279e09e.png"
      catchtap="showCode"
    />
    <view class="num">
      提货码：{{ verifyCode }}
    </view>
  </view>
</view>

<van-popup
  show="{{ show }}"
  overlay-class="qrcode-overlay"
  class="qrcode-popup"
>
  <view class="content">
    <image src="{{ qrCode }}" />
    <text class="code">{{ verifyCode }}</text>
    <text class="desc">提货码</text>
    <text class="desc">仅团长 {{ headerName }} 可以扫码核销</text>
    <van-icon
      class="close-btn"
      name="cross"
      color="#616161"
      size="28px"
      bind:click="close"
    />
  </view>
</van-popup>