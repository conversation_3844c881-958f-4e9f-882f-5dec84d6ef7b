@import "../../../style/_pannel.scss";

.fetch-num {
  display: flex;
  justify-content: space-between;
  padding: 16px 12px 0;
  height: 70px;

  .left {
    display: flex;
    flex-direction: column;

    .label {
      font-size: 12px;
      line-height: 16px;
      color: #969799;
    }

    .value {
      margin-top: 8px;
      font-size: 32px;
      line-height: 38px;
      color: #323233;
      font-weight: bold;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    image {
      height: 24px;
      width: 24px;
    }

    .num {
      margin-top: 12px;
      font-size: 14px;
      line-height: 18px;
    }
  }

  &--disabled {
    color: #c8c9cc;

    .right {
      .num {
        color: #c8c9cc;
      }
    }

    .left {
      .label,
      .value {
        color: #c8c9cc;
      }
    }
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;

  image {
    height: 214px;
    width: 214px;
  }

  .code {
    margin-top: 20px;
    line-height: 30px;
    font-size: 24px;
    font-weight: bold;
  }

  .desc {
    margin-top: 8px;
    line-height: 20px;
    font-size: 14px;
    color: #969799;
  }

  .close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
  }
}

.qrcode {
  &-popup {
    background: transparent;
  }

  &-overlay {
    background: rgba(255, 255, 255, 0.6);
  }
}
