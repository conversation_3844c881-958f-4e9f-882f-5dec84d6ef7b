import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  data: {
    show: false,
  },

  mapData: {
    ...mapState([
      'payNo',
      'verifyAt',
      'verifyCode',
      'payTime',
      'state',
      'qrCode',
      'headerName'
    ])
  },

  methods: {
    close() {
      this.setYZData({
        show: false
      });
    },

    showCode() {
      this.setYZData({
        show: true
      });
    }
  },
});
