@import "../../../style/_pannel.scss";

.header-info {
  position: relative;
  display: flex;
  align-items: center;
  padding: 24px 12px;

  .avatar {
    flex-shrink: 0;
    flex-grow: 0;
    flex-basis: 44px;
    height: 44px;
    width: 44px;
    border-radius: 50%;
  }

  .content {
    flex: 1;
    margin: 0 32px 0 8px;

    .title {
      line-height: 22px;
      font-size: 16px;
      color: #333;
      font-weight: 500;

      .label {
        margin-left: 8px;
        height: 16px;
        width: 32px;
      }
    }
  }

  .desc {
    margin-top: 4px;
    font-size: 13px;
    line-height: 18px;

    &-label {
      color: #969799;
    }

    &-content {
      color: #333;
    }
  }

  .phone {
    position: absolute;
    top: 24px;
    right: 12px;
    height: 24px;
    width: 24px;
  }
}