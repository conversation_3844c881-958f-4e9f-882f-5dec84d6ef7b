import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';

VanxComponent({
  mapData: {
    ...mapState([
      'headerAvatar',
      'headerName',
      'headerMobile',
      'headerFetchAddress'
    ])
  },

  methods: {
    call() {
      wx.makePhoneCall({
        phoneNumber: this.data.headerMobile,
        fail() {
          Toast('拨打失败');
        }
      });
    }
  },
});
