import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  data: {
    stateDesc: ''
  },

  mapData: {
    ...mapState(['state', 'verifyAt', 'feedback', 'orderExpireTime', 'stateKv'])
  },

  ready() {
    setTimeout(() => {
      this.startCountdown();
    }, 10000);
  },

  methods: {
    getStateText() {
      const { stateKv, orderExpireTime } = this.data;
      let remain = orderExpireTime - this.count * 5000;
      if (orderExpireTime === 0 || remain <= 0) {
        return '超时订单将自动关闭';
      }
      remain = Math.ceil(remain / 60000);
      return stateKv.originStateDesc.replace('[time]', `${remain}分钟`);
    },
    startCountdown() {
      const { stateKv = {} } = this.data || {};
      const { originStateDesc } = stateKv;
      if (originStateDesc && originStateDesc.indexOf('[time]') > -1 && !this.countdown) {
        this.count = 0;
        this.setYZData({
          stateDesc: this.getStateText()
        });
        this.countdown = setInterval(() => {
          this.count++;
          this.setYZData({
            stateDesc: this.getStateText()
          });
        }, 5000);
      }
    }
  }
});
