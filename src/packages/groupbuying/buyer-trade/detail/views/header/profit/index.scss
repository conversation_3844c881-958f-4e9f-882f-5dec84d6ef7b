@import "../../../style/_pannel.scss";

.profit {
  padding: 13px 12px;

  .content {
    display: flex;
    align-items: center;
    height: 20px;

    image {
      height: 18px;
      width: 18px;
    }

    .text {
      margin-left: 4px;
      font-size: 14px;
      color: #323233;
      font-weight: 500;
    }

    .price {
      margin-left: 8px;
      font-size: 14px;
      color: #faab0c;
      font-weight: bold;
      line-height: 20px;
    }

    .desc {
      margin-left: 8px;
      font-size: 14px;
      color: #969799;
      letter-spacing: 0;
    }

    .tip-icon {
      top: 2px;
      margin-left: 8px;
      color: #969799;
    }

    .state-text {
      flex: auto 1 0;
      font-size: 14px;
      color: #323233;
      letter-spacing: 0;
      text-align: right;
    }
  }
}