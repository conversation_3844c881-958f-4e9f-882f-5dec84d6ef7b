import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  data: {
    show: false
  },

  mapData: {
    ...mapState([
      'profit',
      'noProfitReason',
      'settleStateDesc'
    ]),
  },

  methods: {
    tip() {
      this.setYZData({
        show: true
      });
    },
    onConfirm() {
      this.setYZData({
        show: false
      });
    }
  },
});
