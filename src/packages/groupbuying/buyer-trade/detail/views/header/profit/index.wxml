<wxs module="computed" src="./computed.wxs" />
<view class="order-detail-pannel profit">
  <view class="content">
    <image src="https://img.yzcdn.cn/public_files/2019/07/04/720d1fca2c8c243330955ce609729da0.png" />
    <view class="text">利润</view>
    <view wx:if="{{ computed.showProfit(profit) }}" class="price">¥{{ profit }}</view>
    <view wx:else class="desc">{{ noProfitReason }}</view>
    <van-icon name="warning-o" custom-class="tip-icon" bind:click="tip" />
    <view wx:if="{{ computed.showProfit(profit) }}" class="state-text">{{ settleStateDesc }}</view>
  </view>
  <tip
    show="{{ show }}"
    title="利润说明"
    msg="团员使用优惠券或退款时，利润将会按比例扣除"
    bind:confirm="onConfirm"
  />
</view>
