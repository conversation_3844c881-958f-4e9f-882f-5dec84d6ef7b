@import "../../../style/_pannel.scss";

.buyer-info {
  padding: 0 12px;

  .header {
    display: flex;
    align-items: center;
    height: 56px;
    border-bottom: 1px solid #ebedf0;

    .phone,
    .avatar {
      height: 24px;
      width: 24px;
      flex-shrink: 0;
    }

    .avatar {
      border-radius: 50%;
    }

    text {
      margin-left: 8px;
      flex: 1;
      font-weight: 500;
    }
  }

  .buyer-msg {
    display: flex;
    padding: 13px 0;
    line-height: 18px;
    font-size: 14px;

    .label {
      flex-grow: 0;
      flex-shrink: 0;
      flex-basis: 60px;
      width: 60px;
      color: #969799;
    }

    .value {
      flex: 1;
      margin-left: 16px;
      color: #323233;
    }
  }
}