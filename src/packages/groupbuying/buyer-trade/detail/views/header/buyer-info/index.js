import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';

VanxComponent({
  mapData: {
    ...mapState([
      'customerAvatar',
      'customerNickname',
      'customerMobile',
      'buyerMsg'
    ])
  },

  methods: {
    call() {
      wx.makePhoneCall({
        phoneNumber: this.data.customerMobile,
        fail() {
          Toast('拨打失败');
        }
      });
    }
  },
});
