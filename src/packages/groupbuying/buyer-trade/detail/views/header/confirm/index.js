import { mapState, mapActions } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Dialog from '@vant/weapp/dist/dialog/dialog';

VanxComponent({
  mapData: {
    ...mapState([
      'state',
      'verifyAt'
    ]),
  },

  methods: {
    confirm() {
      Dialog.confirm({
        message: '请确认货物，确认之后不可撤销',
        confirmButtonText: '确认提货',
        cancelButtonText: '我再想想'
      }).then(() => {
        this.confirmFetch();
      }).catch(() => {
        // just handle reject do nothing
      });
    },

    ...mapActions([
      'confirmFetch'
    ])
  },
});
