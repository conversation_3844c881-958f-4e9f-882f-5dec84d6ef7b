import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';

VanxComponent({
  externalClasses: ['custom-class', 'custom-label-class', 'custom-value-class'],

  properties: {
    label: {
      type: String
    },
    value: {
      type: String
    },
    needCopy: {
      type: Boolean,
      value: false
    }
  },

  methods: {
    copy() {
      wx.setClipboardData({
        data: this.data.value,
        fail() {
          Toast('当前环境不支持');
        }
      });
    }
  },
});
