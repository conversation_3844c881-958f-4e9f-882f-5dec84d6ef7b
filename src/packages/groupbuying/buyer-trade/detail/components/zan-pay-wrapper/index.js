import { VanxComponent } from 'shared/common/base/wsc-component/index';

const app = getApp();

VanxComponent({
  properties: {
    zeroOrder: <PERSON><PERSON><PERSON>,
    orderNo: String,
    prepayParams: {
      type: Object,
      value: {},
    },
  },

  methods: {
    startPay() {
      if (this.data.zeroOrder) {
        this.onPaySuccess();
        return;
      }

      if (this.payService) {
        const {
          orderNo,
          prepayParams: {
            cashierSalt,
            cashierSign,
            partnerId,
            prepayId,
            bizExt,
          },
        } = this.data;

        let tradeBizExt = {};
        try {
          tradeBizExt = JSON.parse(bizExt || '{}');
        } catch (error) {
          console.error('社区团购业务扩展参数解析失败', error);
        } finally {
          tradeBizExt.appId = app.getAppId();
        }

        this.payService.startPay({
          orderNo,
          cashierSalt,
          cashierSign,
          partnerId,
          prepayId,
          tradeBizExt,
        });
      }
    },

    async initPay({ detail }) {
      const { default: PayService } = await import('@youzan/zan-pay-weapp');
      this.payService = new PayService({
        toast: wx.showToast,
        clear: wx.hideToast,
        request: app.request,
        biz: 'group_buying',
        quickMode: true,
      });
      const crypto = await import('@youzan/crypto');
      this.payService.init(detail, crypto);
    },

    onPaySuccess() {
      wx.redirectTo({
        url: `/packages/order/paid/index?order_no=${this.data.orderNo}`,
      });
    },

    onCashierNavi({ detail: destination }) {
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${destination.url}&title=${destination.title}`,
      });
    },
  },
});
