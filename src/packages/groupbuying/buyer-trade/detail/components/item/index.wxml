<view class="item">
  <view class="goods">
    <image src="{{ detail.imgUrl }}" />
    <view class="info">
      <view>
        <view class="title">
          {{ detail.title }}
        </view>
        <view class="sku-name">
          {{ detail.skuName }}
        </view>
      </view>
      <view class="other">
        <view class="price">
          ¥ {{ detail.price }}
          <view class="markPrice" wx:if="{{detail.computedMarkPrice && detail.computedMarkPrice !== detail.price}}">¥ {{detail.computedMarkPrice}}</view>
        </view>
        <view>
          x {{ detail.num }}
        </view>
      </view>
    </view>
  </view>
  <view
    wx:if="{{ detail.showRefund || detail.refund !== 0 }}"
    class="refund"
  >
    <view class="refund-text">
      <text wx:if="{{ detail.refund !== 0 }}">已退款 ¥{{ detail.refund }}</text>
    </view>
    <van-button
      wx:if="{{ detail.showRefund }}"
      custom-class="refund-btn"
      type="default"
      size="small"
      plain
      round
      bind:click="applyRefund"
    >
      {{ detail.refundText }}
    </van-button>
  </view>
</view>