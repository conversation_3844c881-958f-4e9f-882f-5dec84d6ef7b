.item {
  .goods {
    display: flex;
    padding: 8px 0;

    image {
      height: 80px;
      width: 80px;
      border-radius: 4px;
    }

    .info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 88px;
      flex: 1;
      margin-left: 8px;

      .title {
        line-height: 20px;
        max-height: 36px;
        font-size: 13px;
        color: #323233;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .sku-name {
        margin-top: 8px;
        line-height: 16px;
        height: 16px;
        font-size: 12px;
        color: #969799;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      .other {
        display: flex;
        justify-content: space-between;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #999;

        .price {
          color: #f24724;
          font-weight: 500;
          font-size: 14px;

          .markPrice {
            display: inline;
            font-size: 10px;
            text-decoration: line-through;
            color: #999;
            margin-left: 2px;
          }
        }
      }
    }
  }

  .refund {
    height: 24px;
    display: flex;
    padding-left: 88px;
    align-items: center;
    justify-content: space-between;

    .refund-btn {
      padding: 0;
      height: 24px;
      width: 64px;
      line-height: 24px;
      border-color: #dcdee0;
    }

    &-text {
      font-size: 12px;
      color: #07c160;
      line-height: 16px;
    }
  }
}
