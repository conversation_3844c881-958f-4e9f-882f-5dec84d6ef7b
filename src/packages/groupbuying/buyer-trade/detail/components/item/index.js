import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { fetchSafeInfo } from '../../../../common';
import money from '@youzan/weapp-utils/lib/money';

const app = getApp();

VanxComponent({
  properties: {
    detail: {
      type: Object,
      observer(val) {
        if (val.markPrice) {
          val.computedMarkPrice = money(val.markPrice).toYuan()
        }
        this.setYZData({
          detail: val
        })
      }
    }
  },

  methods: {
    applyRefund() {
      const { detail } = this.data;
      const { tradeItemId, feedback, orderNo } = detail;
      fetchSafeInfo({
        orderNo
      }).then((res = {}) => {
        const refundInfo = res[tradeItemId] || {};
        const dbid = app.db.set({
          item_id: tradeItemId,
          safe_no: refundInfo.refundId || '',
          order_no: orderNo
        });
        let url;
        if (feedback === 0 || feedback === 249) {
          url = '/packages/trade/order/safe/apply/index';
        } else {
          url = '/packages/trade/order/safe/info/index';
        }
        wx.navigateTo({
          url: `${url}?dbid=${dbid}`
        });
      });
    }
  }
});
