import pick from '@youzan/weapp-utils/lib/pick';

const openState = {
  // 提货信息
  goodsInfo(state) {
    const goodsInfo = pick(state, ['payNo', 'verifyCode']);
    return goodsInfo;
  },
  // 团长信息
  headerInfo(state) {
    const headerInfo = pick(state, [
      'headerAvatar',
      'headerName',
      'headerFetchAddress',
      'headerMobile',
    ]);
    return headerInfo;
  },
  // 订单信息
  orderInfo(state) {
    const orderInfo = pick(state, [
      'orderNo',
      'stateDesc',
      'createdAt',
      'payTime',
      'buyerMsg',
      'decrease',
      'originPrice',
      'payment',
    ]);
    return orderInfo;
  },
  // 订单商品信息
  goodsList(state) {
    const goodsList = state.items.map((item) => {
      return pick(item, ['title', 'itemId', 'price', 'num']);
    });
    return goodsList;
  },
  // 进入详情页的状态
  isHeader(state){
    const { isHeader = false } = state;
    return isHeader;
  }
};

export { openState };
