<import src="./custom-tpl.wxml" />
 
<view class="order-detail">
  <view class="loading" wx:if="{{ loading }}">
    <van-loading />
  </view>
  <block wx:elif="{{ disable }}">
    <view class="disable">
      <image src="https://b.yzcdn.cn/v2/image/wap/common_page/<EMAIL>" />
      <view class="text">{{ disable }}</view>
    </view>
  </block>
  <block wx:else>
    <block wx:for="{{ design }}" wx:key="type">
      <state wx:if="{{ item.type === 'state' }}" />
      <header-fetch-num wx:elif="{{ item.type === 'header-fetch-num' }}" />
      <buyer-info wx:elif="{{ item.type === 'buyer-info' }}" />
      <item-list wx:elif="{{ item.type === 'item-list' }}" />
      <profit wx:elif="{{ item.type === 'profit' }}" />
      <order-no wx:elif="{{ item.type === 'order-no' }}" />
      <confirm wx:elif="{{ item.type === 'confirm' }}" />
      <buyer-fetch-num wx:elif="{{ item.type === 'buyer-fetch-num' }}" />
      <header-info wx:elif="{{ item.type === 'header-info' }}" />
      <buyer-msg wx:elif="{{ item.type === 'buyer-msg' }}" />
      <order-no wx:elif="{{ item.type === 'order-no' }}" />
      <pay wx:elif="{{ item.type === 'pay' }}" />
      <block wx:else>
        <template is="{{ item.type }}" />
      </block>
    </block>
  </block>
</view>
<van-dialog id="van-dialog" />
<van-toast id="van-toast" />
<zan-pay-wrapper
  id="zanPayWrapperRef"
  orderNo="{{ orderNo }}"
  zeroOrder="{{ payParams.zeroOrder }}"
  prepayParams="{{ payParams.prePaymentPreparation }}"
/>
<inject-protocol noAutoAuth />