const app = getApp();
const http = app.request;

export function verifyOrder(data) {
  return http({
    path: '/pay/wsctrade/groupbuying/verify.json',
    method: 'POST',
    origin: 'cashier',
    data,
  });
}

export function pay(data) {
  return http({
    path: '/pay/wsctrade/groupbuying/buyerTrade/pay.json',
    method: 'POST',
    origin: 'cashier',
    data,
  });
}

export function getRefundState(data) {
  return http({
    path: '/wsctrade/refund/getRefundState.json',
    data,
  });
}

export function queryOrder(data) {
  return http({
    path: '/wsctrade/groupbuying/getOrderDetailV2.json',
    data,
  });
}
