import { mapState, mapActions } from '@youzan/vanx';
import { VanxPage } from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import { store } from './store';
import SaasPageConfig from './saas/page-config';

let i = 1;

const extendPageConfig = ECLOUD_MODE ? SaasPageConfig : {};

VanxPage(extendPageConfig, {
  store() {
    this.name = `communitybuy-order-detail-${i}`;
    i++;
    store.name = this.name;
    return store;
  },

  mapData: {
    ...mapState([
      'orderNo',
      'loading',
      'isHeader',
      'payParams',
      'disable',
      'design',
    ]),
  },

  ...mapActions(['initOrderDetail', 'getDesign']),

  onLoad() {
    const { orderNo, isPay, fromBuy, isHeader, kdtId } = this.options;
    this.initOrderDetail({
      orderNo,
      isPay: +isPay === 1,
      fromBuy: +fromBuy === 1,
      isHeaderQuery: +isHeader === 1,
      kdtId: kdtId || getApp().getKdtId(),
    });
  }
});
