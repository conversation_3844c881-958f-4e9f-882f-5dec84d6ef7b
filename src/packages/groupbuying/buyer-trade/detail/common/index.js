/* eslint-disable-next-line */
import { moment as formatDate } from 'utils/time';
import money from '@youzan/weapp-utils/lib/money';
import { buyerStateMap, headerStateMap } from '../config';
import { getSkuName } from '../../../common';

export function getRefundState(detail) {
  const { feedback } = detail;
  if (feedback === 0 || feedback == 249) {
    return {
      refundType: 0,
      refundText: '申请退款'
    };
  }
  if (feedback === 250) {
    return {
      refundType: 2,
      refundText: '已退款'
    };
  }
  return {
    refundType: 1,
    refundText: '退款中'
  };
}

export function formatDetail(detail, showRefund) {
  const {
    createdAt, deliveryTime, payTime, successTime, verifyAt, profit, payment, originPrice, items, orderNo, decrease, ...other
  } = detail;
  return {
    createdAt: formatDate(createdAt, 'YYYY-MM-DD HH:mm:ss'),
    deliveryTime: deliveryTime > 0 ? formatDate(deliveryTime, 'YYYY-MM-DD HH:mm:ss') : deliveryTime,
    payTime: payTime > 0 ? formatDate(payTime, 'YYYY-MM-DD HH:mm:ss') : payTime,
    successTime: successTime > 0 ? formatDate(successTime, 'YYYY-MM-DD HH:mm:ss') : successTime,
    verifyAt: verifyAt > 0 ? formatDate(verifyAt * 1000, 'YYYY-MM-DD HH:mm:ss') : verifyAt,
    profit: profit > 0 ? money(profit).toYuan() : profit,
    payment: payment > 0 ? money(payment).toYuan() : payment,
    originPrice: originPrice > 0 ? money(originPrice).toYuan() : originPrice,
    decrease: decrease > 0 ? money(decrease).toYuan() : decrease,
    items: items.map(one => {
      const {
        price, refund = 0, sku = [], picture = [], ...itemProperty
      } = one;
      return {
        price: money(price).toYuan(),
        refund: refund > 0 ? money(refund).toYuan() : refund,
        skuName: getSkuName(sku),
        imgUrl: picture.length > 0 ? picture[0].url : '',
        showRefund,
        orderNo,
        ...itemProperty,
        ...getRefundState(one),
      };
    }),
    ...other
  };
}

export function getStateKv(storeState) {
  const {
    state, verifyAt, feedback, isHeader, orderExpireTime
  } = storeState;
  const detail = {
    state,
    verifyAt,
    feedback
  };
  const stateMap = isHeader ? headerStateMap : buyerStateMap;
  const map = stateMap.find(one => one.test(detail, storeState));
  if (map) {
    const {
      result: {
        stateText, stateDesc, countdown, isFinished
      }
    } = map;
    let computedStateDesc = stateDesc;
    if (typeof stateDesc === 'function') {
      computedStateDesc = stateDesc(storeState);
    }
    if (computedStateDesc && computedStateDesc.indexOf('[time]') > -1) {
      computedStateDesc = computedStateDesc.replace('[time]', `${Math.ceil(orderExpireTime / 60000)}分钟`);
    }
    return {
      isFinished: !!isFinished,
      countdown: !!countdown,
      stateText,
      stateDesc: computedStateDesc,
      originStateDesc: stateDesc
    };
  }
  return {
    stateText: '订单异常'
  };
}
