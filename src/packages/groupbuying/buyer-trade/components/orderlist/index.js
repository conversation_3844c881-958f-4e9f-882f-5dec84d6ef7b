import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
/* eslint-disable-next-line */
import { moment as formatDate } from 'utils/time';
import { getRollOrders } from '../../api';

VanxComponent({
  mapData: {
    ...mapState([
      'actDetail',
    ])
  },
  data: {
    list: [],
    total: 0,
    pageId: null,
    finished: false,
    loading: false,
    imgSize: '!40x40.jpg'
  },
  ready() {
    this.fetch();
  },
  methods: {
    fetch() {
      const {
        pageId, total, list: originList, actDetail
      } = this.data;

      if (!actDetail || !actDetail.id) {
        this.setYZData({
          finished: true
        });
        return;
      }

      const { id: activityId } = actDetail;

      const params = {
        activityId,
        pageSize: 20
      };
      if (pageId) {
        params.pageId = pageId;
      }
      this.setYZData({
        loading: true
      });
      getRollOrders(params).then(data => {
        const {
          list = [], pageId = '', totalItems, imgSize
        } = data;
        list.forEach(one => {
          one.customerAvatar = cdnImage(one.customerAvatar, imgSize);
          one.payTime = formatDate(one.payTime, 'YYYY-MM-DD HH:mm:ss');
        });
        if (total === 0) {
          this.setYZData({
            total: totalItems
          });
        }
        if (list.length < 20) {
          this.setYZData({
            finished: true
          });
        }
        this.setYZData({
          list: originList.concat(list),
          loading: false,
          pageId
        });
      });
    },
    onLoad() {
      const { pageId, loading, finished } = this.data;
      if (pageId && !loading && !finished) {
        this.fetch();
      }
    }
  }
});
