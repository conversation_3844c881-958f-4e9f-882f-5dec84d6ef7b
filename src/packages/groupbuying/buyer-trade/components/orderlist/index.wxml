<view class="order-list" hidden="{{ list.length === 0 }}">
  <view class="order-list__title order-list__black">
    <image
      class="order-list__left"
      src="https://img.yzcdn.cn/public_files/2018/11/22/997ee482b14180b636b0086ca4eae7f3.png"
    />
    他们都在买
    <image
      class="order-list__right"
      src="https://img.yzcdn.cn/public_files/2018/11/22/997ee482b14180b636b0086ca4eae7f3.png"
    />
  </view>
  <view>
    <view
      wx:for="{{ list }}"
      wx:for-item="item"
      wx:for-index="i"
      wx:key="{{ item.id }}"
      class="order-list__item"
    >
      <view class="order-list__item__floor">{{ total - i }}F</view>
      <view class="order-list__item__content">
        <view class="order-list__item__img">
          <image src="{{ item.customerAvatar }}" />
        </view>
        <view class="order-list__item__info">
          <view class="order-list__item__info-base">
            <view class="order-list__item__info-name">{{ item.customerNickname || '匿名用户' }}</view>
            <view class="order-list__item__info-time">{{ item.payTime }}</view>
          </view>
          <view class="order-list__item__info-detail">
            {{ item.itemTitle }} * {{ item.itemNum }}
          </view>
        </view>
      </view>
    </view>
  </view>
  <zan-loadmore wx:if="{{ loading }}" type="loading" />
  <view class="load-more" wx:if="{{ !finished && !loading }}" catchtap="onLoad">查看更多</view>
</view>