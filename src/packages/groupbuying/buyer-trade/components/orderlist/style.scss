$font-color: #333;
$gray: #999;

@mixin fontColor($fontSize, $color) {
  font-size: $fontSize;
  color: $color;
}

.order-list {
  background-color: #fff;
  margin-top: 20px;

  .load-more {
    padding: 10px 0;
    text-align: center;
    font-size: 12px;
    color: #666;
  }

  &__title {
    font-weight: 500;
    padding-top: 15px;
    margin-bottom: 5px;
    text-align: center;
  }

  &__item {
    display: flex;
    position: relative;
    align-items: center;

    &:last-child {
      .order-list__item__content {
        border-bottom: none;
      }
    }


    &__floor {
      margin-left: 15px;
      width: 20px;
      font-size: 14px;
      color: #dadada;
    }

    &__content {
      display: flex;
      flex: 1;
      padding: 15px 15px 15px 0;
      margin-left: 18px;
      border-bottom: 1px solid #e5e5e5;
    }

    &__img {
      height: 44px;

      > image {
        height: 44px;
        width: 44px;
        border-radius: 50%;
      }
    }

    &__info {
      margin-left: 12px;
      display: flex;
      flex-direction: column;
      flex: 1;

      &-base {
        display: flex;
        margin-top: 7px;
        line-height: 14px;
      }

      &-name {
        font-size: 14px;
        color: #333;
      }

      &-time {
        margin-left: 5px;
        font-size: 12px;
        color: #999;
      }

      &-detail {
        margin-top: 5px;
        font-size: 12px;
        color: #999;
        line-height: 16px;
      }
    }
  }

  &__black {
    @include fontColor(14px, $font-color);

    padding-right: 5px;
  }

  &__left,
  &__right {
    width: 13px;
    height: 11px;
    padding-right: 5px;
  }

  &__right {
    transform: scaleX(-1);
  }

  .van-loading {
    margin: 30px auto;
  }
}