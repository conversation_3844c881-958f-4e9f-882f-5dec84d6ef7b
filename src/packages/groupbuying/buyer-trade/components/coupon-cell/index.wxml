<!-- 订单页外部展示条 -->
<van-cell
  is-link
  bind:click="showCouponList"
  title="优惠"
  title-width="75px"
  center
>
  <view class="value-class">
    <text wx:if="{{ enableCoupons.length === 0 }}">暂无可用</text>
    <text wx:elif="{{ !coupon.id }}">{{ enableCoupons.length }}张可用</text>
    <block wx:else>
      <view>-¥{{ coupon.valueDesc }}</view>
    </block>
  </view>
</van-cell>

<wxs module="toYuan">
module.exports = function (value) {
  var yuan = value / 100;
  return yuan.toFixed(2);
}
</wxs>
