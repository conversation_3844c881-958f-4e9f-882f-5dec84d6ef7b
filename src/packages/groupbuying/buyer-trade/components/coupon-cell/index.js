import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { mapState, mapMutations } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

const app = getApp();
const eventKey = 'communitybuy-coupon-change';

VanxComponent({
  mapData: {
    ...mapState([
      'coupon', 'enableCoupons', 'disableCoupons'
    ])
  },

  ready() {
    // 订阅选择优惠券
    this.on(
      eventKey,
      (coupon = {}) => {
        coupon = mapKeysCase.toCamelCase(coupon);
        this.setCoupon(coupon);
      }
    );
  },
  methods: {
    ...mapMutations([
      'setCoupon'
    ]),
    // 点击跳转到优惠券列表
    showCouponList() {
      const {
        enableCoupons,
        disableCoupons,
        coupon
      } = this.data;

      const params = {
        charge_coupon: enableCoupons.slice(),
        unavailable_coupon: disableCoupons.slice(),
        selected_coupon: coupon,
        exchangeParams: {}
      };

      const dbid = app.db.set({ ...params });
      wx.navigateTo({
        url: '/packages/groupbuying/buyer-trade/coupon/index?dbid=' + dbid
      });
    }
  }
});
