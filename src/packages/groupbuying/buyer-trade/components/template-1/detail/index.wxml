<van-popup
  show="{{ detailVisible }}"
  position="bottom"
  bind:click-overlay="closePopup"
>
  <view class="goods-detail">
    <view class="preview">
      <swiper
        wx:if="{{ detailVisible }}"
        class="van-swipe-item"
        autoplay="{{ autoplayTime }}"
        show-indicators="{{ showIndicators }}"
        circular
      >
        <swiper-item
          wx:for="{{ detailInfo.picture }}"
          wx:for-index="idx"
          wx:for-item="picItem"
          wx:key="idx"
        >
          <image src="{{ picItem.url }}" />
        </swiper-item>
      </swiper>
    </view>
    <view class="base-info">
      <view class="name">{{ detailInfo.title }}</view>
      <view
        wx:if="{{ detailInfo.isDisplaySales === 1 || detailInfo.isDisplayStock === 1 }}"
        class="stat-num-wrap"
      >
        <stat-num item="{{ detailInfo }}"></stat-num>
      </view>
      <view class="sell">
        <view class="price">
          <view class="price-pay">
            <text class="price-unit">¥</text>
            <text class="price-num">{{ detailInfo.spu.price }}</text>
          </view>
          <view
            class="price-origin"
            wx:if="{{ detailInfo.origin }}"
          >
            ¥ {{ detailInfo.origin }}
          </view>
        </view>
        <stepper
          wx:if="{{ isBuying && detailInfo.stockNum > 0 }}"
          bind:plus="onPlus"
          bind:minus="onMinus"
          detail="{{ detailInfo }}"
        />
        <view class="buying-item-none" wx:if="{{ detailInfo.stockNum === 0 }}">卖完啦</view>
      </view>
    </view>
    <view class="line" />
    <view
      class="goods-content"
    >
      <view class="title">商品详情</view>
      <view class="wrap">
        <cap-rich-text wx:if="{{ detailInfo.content }}" html="{{ detailInfo.content }}" />
        <view wx:else class="content">暂无详情</view>
      </view>
    </view>
  </view>
</van-popup>

<!-- 关闭按钮 -->
<view
  class="close-btn"
  hidden="{{ !detailVisible }}"
>
  <view
    class="tap-area"
    catchtap="closePopup"
  ></view>
</view>
