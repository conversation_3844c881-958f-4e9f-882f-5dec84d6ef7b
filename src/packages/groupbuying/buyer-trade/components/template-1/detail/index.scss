.goods-detail {
  height: 100vh;

  .preview {
    height: 100vw;
    background: #f8f8f8;

    .van-swipe-item {
      height: 100vw;
      line-height: 100vw;
      text-align: center;
      overflow: hidden;

      image {
        display: inline;
        max-width: 100vw;
        max-height: 100vw;
        vertical-align: middle;
      }
    }
  }

  .base-info {
    padding: 15px;

    .name {
      font-size: 14px;
      line-height: 20px;
      color: #111;
    }

    .remainder {
      margin-top: 4px;
    }

    .stat-num-wrap {
      margin-top: 4px;
    }

    .sell {
      margin-top: 12px;
      display: flex;

      .price {
        display: flex;
        align-items: baseline;
        flex: 1;

        &-pay {
          font-weight: 500;
          display: inline-block;
          color: #f44;
          font-size: 14px;
          margin-right: 5px;

          text {
            font-size: 18px;
          }

          .price-start {
            font-size: 12px;
            margin-left: 1px;
          }
        }

        &-origin {
          display: inline-block;
          color: #999;
          font-size: 10px;
          text-decoration: line-through;
        }
      }
    }
  }

  .line {
    height: 10px;
    background: #f8f8f8;
  }

  .goods-content {
    .title {
      height: 46px;
      line-height: 46px;
      text-align: center;
      font-size: 14px;
      border-bottom: 1px solid #f2f2f2;
      color: #111;
    }

    .wrap {
      padding: 10px;
    }

    .content {
      font-size: 16px;
    }
  }

  .buying-item-none {
    position: absolute;
    right: 15px;
    color: #333;
    font-size: 14px;
    line-height: 24px;
  }
}

.close-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  height: 28px;
  width: 28px;
  background-image: url("https://img.yzcdn.cn/public_files/2018/12/17/330e2cd3890b6ee46a97fd1f7b2896fd.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center center;
  z-index: 99999;

  .tap-area {
    position: absolute;
    height: 42px;
    width: 42px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}