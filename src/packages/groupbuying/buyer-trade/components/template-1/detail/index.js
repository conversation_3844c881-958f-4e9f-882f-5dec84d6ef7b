import { mapState, mapGetters, mapMutations, mapActions } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState([
      'detailVisible', 'detailInfo'
    ]),
    ...mapGetters([
      'isBuying'
    ])
  },

  data: {
    autoplayTime: 3000,
    showIndicators: false
  },

  methods: {
    ...mapMutations([
      'setDetailVisible'
    ]),
    ...mapActions([
      'plus',
      'minus'
    ]),
    closePopup() {
      this.setDetailVisible(false);
    },
    onPlus() {
      const { detailInfo } = this.data;
      this.plus({goodsId: detailInfo.id});
    },
    onMinus() {
      const { detailInfo } = this.data;
      this.minus(detailInfo.id);
    }
  }
});
