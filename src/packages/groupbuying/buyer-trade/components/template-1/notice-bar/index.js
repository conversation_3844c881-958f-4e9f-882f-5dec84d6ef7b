import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Countdown from 'utils/countdown';

VanxComponent({
  mapData: {
    ...mapState([
      'actDetail',
    ])
  },

  data: {
    leftIcon:
      'https://b.yzcdn.cn/fix-base64/4d95914fb5cd5fa4b885756a1ecdc8b247bea5b994d4ddbf85942a5a456429d0.png',
    noticeText: ''
  },

  ready() {
    const { actDetail } = this.data;
    if (actDetail) {
      this.initNoticeText();
    } else {
      this.setYZData({
        noticeText: '团长暂未开团'
      });
    }
  },

  methods: {
    initNoticeText() {
      let text;
      let time;
      const { state, startTime, endTime } = this.data.actDetail;
      switch (state) {
        case 0:
          text = '距离本次团购活动开始还有';
          time = startTime;
          break;
        case 1:
          text = '距离本次团购活动结束还有';
          time = endTime;
          break;
        case 2:
          text = '本次团购活动已结束，下次记得早点来呦';
          break;
        default:
          text = '距离本次团购活动结束还有';
          break;
      }
      if (state === 2) {
        this.setYZData({
          noticeText: text
        });
        return;
      }
      const start = new Date().getTime();
      const elapsed = time - start;
      new Countdown(elapsed, {
        onChange: timeData => {
          let timeStr;
          if (timeData.day > 0) {
            timeStr = ` ${timeData.day} 天 ${timeData.hour} 时 ${timeData.minute} 分 ${
              timeData.second
            } 秒`;
          } else {
            timeStr = ` ${timeData.hour} 时 ${timeData.minute} 分 ${timeData.second} 秒`;
          }
          this.setYZData({
            noticeText: text + timeStr
          });
        },
        onEnd: () => {
          // 如果是活动开始倒计时结束，刷新页面
          wx.startPullDownRefresh();
        }
      });
    }
  }
});
