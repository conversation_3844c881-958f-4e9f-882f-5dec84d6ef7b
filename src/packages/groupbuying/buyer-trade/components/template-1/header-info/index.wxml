<view
  hidden="{{ !headerDetail }}"
  class="header-info"
>
  <view class="default-info">
    <image
      class="avatar"
      src="{{ headerDetail.headerAvatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}"
    ></image>
    <view class="default-info__name">
      <view class="detail">{{ headerDetail.headerName }} 团长</view>
      <view class="location" catchtap="switch">
        <image src="https://img.yzcdn.cn/wsc-h5-ump/groupbuying/p-green.png"></image>
        <view class="text">{{ headerDetail.subdistrict }}</view>
        <van-icon wx:if="{{ showSwitch }}" custom-class="icon" name="arrow" />
        <view hidden="{{ !showNear }}" class="tip" catchtap="onTap">
          这是离你最近的团长，点击可查看其它团长
          <view class="tip--arrow" />
        </view>
      </view>
    </view>
  </view>
  <view class="contact-info">
    <view wx:if="{{ showMobile }}" class="contact-info__phone">
      <view class="contact">手机号码</view>
      <view class="contact">{{ headerDetail.mobile }}</view>
    </view>
    <view class="contact-info__address">
      <view class="contact">提货地址</view>
      <view class="contact">{{ headerDetail.fetchAddress}}</view>
    </view>
  </view>
  <coupon-pick custom-class="coupon-pick-wrapper" />
</view>
