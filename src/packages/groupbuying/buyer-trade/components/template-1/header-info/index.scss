$font-color: #333;
$gray: #999;

@mixin fontColor($fontSize, $color) {
  font-size: $fontSize;
  color: $color;
}

.header-info {
  background: #fff;

  .default-info {
    padding: 16px 15px;
    display: flex;
    align-items: center;

    .avatar {
      width: 44px;
      height: 44px;
      border-radius: 50%;
    }

    &__name {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-left: 12px;

      .detail {
        line-height: 16px;
        @include fontColor(16px, $font-color);
      }

      .location {
        position: relative;
        display: flex;
        margin-top: 8px;
        align-items: center;
        line-height: 16px;
        font-size: 12px;
        color: #969799;

        image {
          width: 9px;
          height: 12px;
        }

        .text {
          margin-left: 4px;
          max-width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .icon {
          display: block;
          margin-left: 2px;
        }

        .tip {
          position: absolute;
          bottom: -34px;
          left: 13px;
          padding: 0 8px;
          height: 26px;
          line-height: 26px;
          background: rgba(0, 0, 0, 0.7);
          font-size: 10px;
          color: #fff;
          border-radius: 4px;
          white-space: nowrap;

          &--arrow {
            position: absolute;
            top: -8px;
            left: 10px;
            border: 4px transparent solid;
            border-bottom-color: rgba(0, 0, 0, 0.7);
          }
        }
      }
    }
  }

  .contact-info {
    padding: 0 15px 16px 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    &__phone {
      margin-right: 10px;
    }

    &__phone,
    &__address {
      .contact:first-child {
        @include fontColor(12px, $gray);
      }

      .contact:last-child {
        @include fontColor(14px, $font-color);
      }
    }
  }

  .coupon-pick-wrapper {
    padding: 0 16px 15px;
  }
}
