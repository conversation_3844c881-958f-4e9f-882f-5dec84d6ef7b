<view class="pay">
  <view
    wx:if="{{ isBuying }}"
    class="total-pay"
  >
    共{{ totalItem }}件 合计：
    <view class="total-pay__price">
      ¥ <text>{{ payStatus === 1 ? '计算中' : finalPrice }}</text>
    </view>
  </view>
  <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="payToHeader">
    <vm-button
      wx:if="{{ isBuying }}"
      disabled="{{ payStatus !== 2 }}"
    >确认下单</vm-button>
  </user-authorize>
</view>
