import { mapState, mapGetters, mapMutations, mapActions } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState(['cartVisible']),
    ...mapGetters(['totalItem', 'cartlist'])
  },
  methods: {
    ...mapMutations(['setCartVisible']),
    ...mapActions(['clearOriginlist', 'changeOrder']),
    closePopup() {
      this.setCartVisible(false);
    },
    _clearOriginlist() {
      this.clearOriginlist();
    },
    onPlus(e) {
      const { id, skuId } = e.detail;
      this.changeOrder({
        goodsId: id,
        skuId,
        delta: 1
      });
    },
    onMinus(e) {
      const { id, skuId } = e.detail;
      this.changeOrder({
        goodsId: id,
        skuId,
        delta: -1
      });
    },
    onChange(e) {
      const { id, skuId, num } = e.detail;
      this.changeOrder({
        goodsId: id,
        skuId,
        num
      });
    },
    onTrade() {
      this.setCartVisible(false);
      this.triggerEvent('trade');
    }
  }
});
