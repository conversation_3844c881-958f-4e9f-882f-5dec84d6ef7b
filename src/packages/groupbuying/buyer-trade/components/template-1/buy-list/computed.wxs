module.exports = {
  displayList(buyinglist, active, categoryList) {
    if (!categoryList) {
      categoryList = [];
    }
    const currentCategory = categoryList.length > active ? categoryList[active] : null;
    const currentId = currentCategory ? currentCategory.categoryId : -1;
    if (categoryList.length === 0 || currentId === -1) return buyinglist;
    return buyinglist.filter(one => one.categoryIds.indexOf(currentId) > -1);
  }
};
