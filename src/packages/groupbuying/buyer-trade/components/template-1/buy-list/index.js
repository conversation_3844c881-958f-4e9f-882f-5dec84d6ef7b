import Event from '@youzan/weapp-utils/lib/event';
import { mapState, mapGetters } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

const LIST_LOAD_NUM = 20; // 列表一次新增渲染商品数量

VanxComponent({
  mapData: {
    ...mapState(['categoryList', 'skuVisible', 'skuData']),
    ...mapGetters(['buyinglist', 'isBuying']),
  },

  data: {
    active: 0,
    isFixed: false,
    listNum: LIST_LOAD_NUM, // 默认进房0商品，触发第一次新增渲染商品展示一次新增渲染商品的数量
    _buyingList: [],
  },

  ready() {
    Event.on('buying:onListReachBottom', () => {
      this.listLoadMore();
    });
  },
  
  detached() {
    Event.off('buying:onListReachBottom')
  },

  observers: {
    /**
     * getter取到的值会自动更新，需要监听取到最新数据做渲染
     * 小程序observers这里不是深度监听，要加上通配符监听下面的变动
     */
    'buyinglist.**': function (buyinglist) {
      this.setData({
        _buyingList: buyinglist.slice(0, this.data.listNum),
      });
    },
  },

  methods: {
    listLoadMore() {
      const listNum = this.data.listNum + LIST_LOAD_NUM;
      this.setYZData({
        listNum,
        _buyingList: this.data.buyinglist.slice(0, listNum),
      });
    },
    onChange(e) {
      const active = e.detail.index;
      this.setYZData({
        active,
      });
    },

    onScroll(e) {
      const { isFixed } = e.detail;
      this.setYZData({
        isFixed,
      });
    },
  },
});
