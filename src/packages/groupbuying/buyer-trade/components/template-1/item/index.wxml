<wxs src="./computed.wxs" module="computed" />
<view class="buying-item" catchtap="onShowDetail">
  <image class="buying-item__img" src="{{ item.picture[0].url }}" />
  <view class="buying-item__desc">
    <view class="buying-item__desc__title">{{ item.title }}</view>
    <stat-num item="{{ item }}"></stat-num>
    <view class="buying-item__desc__price">
      <view class="buying-item__desc__price-buy">¥ <text class="buying-item__desc__price-buy-num">{{ computed.toYuan(item.price) }}</text></view>
      <view class="buying-item__desc__price-origin" wx:if="{{ item.markPrice > 0 }}">¥ {{ item.markPrice }}</view>
    </view>
    <stepper
      wx:if="{{ isBuying && item.stockNum > 0 && !hideStepper }}"
      detail="{{ item }}"
      bind:plus="onPlus"
      bind:minus="onMinus"
      bind:change="onChange"
      bind:focus="onFocus"
      custom-class="buying-item-stepper"
    ></stepper>
    <view class="buying-item-none" wx:if="{{ item.stockNum === 0 }}">卖完啦</view>
  </view>
</view>
