import { mapState, mapGetters, mapMutations } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState([
      'mobile', 'buyerMsg'
    ]),
    ...mapGetters([
      'isBuying'
    ])
  },
  methods: {
    ...mapMutations([
      'setMobile',
      'setBuyerMsg'
    ]),
    payToHeader() {
      this.triggerEvent('trade');
    },
    onPhoneChange(e) {
      this.setMobile(e.detail);
    },
    onBuyerMsgChange(e) {
      this.setBuyerMsg(e.detail);
    }
  }
});
