import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Dialog from '@vant/weapp/dist/dialog/dialog';

VanxComponent({
  data: {
    icon:
      'https://b.yzcdn.cn/fix-base64/9dc40f9b9654c45b34eecf4e961fd0ef9f4d0217e66b3029c945f4c2125f6511.png',
    text: '活动说明'
  },
  mapData: {
    ...mapState([
      'actDetail'
    ])
  },
  methods: {
    showNotice() {
      const { notice } = this.data.actDetail;
      Dialog.alert({
        title: '团购活动说明',
        message: notice,
        confirmButtonText: '我知道了'
      });
    }
  }
});
