import { mapState, mapMutations } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';

VanxComponent({
  externalClasses: [
    'custom-class'
  ],
  mapData: {
    ...mapState([
      'couponList', 'actDetail'
    ])
  },
  methods: {
    ...mapMutations([
      'setCouponListVisible'
    ]),
    onCouponTap() {
      const { state } = this.data.actDetail || {};
      // 活动未开始
      if (state === 0) {
        Toast('活动开始后可领取优惠券');
        return;
      }
      this.setCouponListVisible(true);
    }
  }
});
