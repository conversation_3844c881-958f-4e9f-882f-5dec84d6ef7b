import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';
import { mapActions } from '@youzan/vanx';

VanxComponent({
  properties: {
    item: {
      type: Object
    }
  },

  data: {
    loading: false,
    smallCase: false
  },

  ready() {
    const { item } = this.data;
    const price = (item.value / 100).toFixed(2);
    const len = `${+price}`.length;
    // 显示字符大于5直接缩小字体
    if (len > 5) {
      this.setYZData({
        smallCase: true
      });
    }
  },

  methods: {
    ...mapActions([
      'receiveCoupon'
    ]),
    receive() {
      if (this.data.loading) return;
      this.setYZData({
        loading: true
      });
      this.receiveCoupon(this.data.item.id)
        .then(() => {
          this.setYZData({
            loading: false
          });
          Toast('领取成功');
        })
        .catch(e => {
          this.setYZData({
            loading: false
          });
          Toast(e.msg || e || '领取失败');
        });
    }
  },
});
