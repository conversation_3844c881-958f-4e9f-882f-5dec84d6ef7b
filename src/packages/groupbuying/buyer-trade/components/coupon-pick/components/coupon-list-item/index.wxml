<wxs module="price" src="../../price.wxs" />
<view class="item">
  <view class="price">
    <view class="price-num">
      <text class="price-num-text {{ smallCase ? 'price-num-text--small' : '' }}">{{ price.toYuan(item.value) }}</text>
      <text class="price-num-unit">元</text>
    </view>
    <view wx:if="{{ item.use > 0 }}" class="price-demand">满{{ price.toYuan(item.use) }}元可用</view>
    <view wx:else class="price-demand">无使用门槛</view>
  </view>
  <view class="content">
    <view class="content-title">{{ item.title }}</view>
    <view class="content-time">{{ item.valid }}</view>
  </view>
  <view class="status">
    <van-button
      custom-class="pick-btn"
      size="mini"
      type="danger"
      round
      catchtap="receive"
    >
      {{ loading ? '领取中' : '立即领取' }}
    </van-button>
    <image
      hidden="{{ !item.isReceived }}"
      class="picked"
      src="https://img.yzcdn.cn/public_files/2019/05/13/e13ea7daf026f5de31190ddecc7ff510.png"
    />
  </view>
</view>