.item {
  display: flex;
  height: 84px;
  background: rgba(238, 10, 36, 0.04);
  border-radius: 4px;
  color: #ff5164;

  .price {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100px;

    &-num {
      line-height: 30px;
      height: 30px;

      &-text {
        font-weight: 600;
        font-size: 30px;

        &--small {
          font-size: 16px;
        }
      }

      &-unit {
        font-size: 12px;
      }
    }

    &-demand {
      margin-top: 6px;
      font-size: 12px;
    }
  }
  .content {
    display: flex;
    padding: 0 10px;
    flex: 1;
    flex-direction: column;
    justify-content: center;

    &-title {
      font-size: 14px;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;
    }

    &-time {
      margin-top: 8px;
      font-size: 12px;
      line-height: 16px;
    }
  }
  .status {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 18px 0 10px;
    overflow: hidden;

    .pick-btn {
      z-index: 1;
    }

    .picked {
      position: absolute;
      bottom: -8px;
      right: -4px;
      height: 44px;
      width: 44px;
      transform: rotateZ(-30deg);
      transform-origin: center center;
    }
  }
}
