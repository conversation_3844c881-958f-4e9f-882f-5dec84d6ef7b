.item {
  display: flex;
  height: 22px;
  line-height: 22px;
  background: #ff5164;
  border-radius: 2px;
  box-sizing: border-box;
  color: #fff;
  overflow: hidden;

  &--disabled {
    background: #dcdee0;
  }

  .price {
    display: flex;
    align-items: center;
    padding: 0 2px 0 4px;
    box-sizing: border-box;
    line-height: 20px;

    text {
      vertical-align: middle;
    }

    .unit {
      font-size: 10px;
    }

    .num {
      margin-left: 2px;
      font-size: 12px;
    }
  }

  .split {
    position: relative;
    width: 6px;

    &-line {
      height: 22px;
      width: 2px;
      border-right: 2px dotted rgba(255, 255, 255, 0.5);
    }

    .circle {
      height: 4px;
      width: 4px;
      border-radius: 50%;
      background: #fff;
    }

    .circle-top {
      position: absolute;
      top: -2px;
      left: 50%;
      transform: translate(-50%, 0);
    }

    .circle-bottom {
      position: absolute;
      bottom: -2px;
      left: 50%;
      transform: translate(-50%, 0);
    }
  }

  .pick {
    padding: 0 4px 0 2px;
    font-size: 10px;
    box-sizing: border-box;
  }
}
