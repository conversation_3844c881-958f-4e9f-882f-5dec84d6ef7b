.coupon-list {
  display: flex;
  flex-direction: column;
  height: 80vh;

  .title {
    height: 44px;
    line-height: 44px;
    text-align: center;
    color: #323233;
    font-size: 16px;
  }

  .header {
    margin: 0 16px;
    display: flex;
    align-items: center;

    .avatar {
      height: 28px;
      width: 28px;
      border-radius: 50%;
    }

    .desc {
      margin-left: 8px;
      font-size: 14px;
      color: #333333;

      text {
        color: #ff5164;
      }
    }
  }

  .list {
    margin-top: 12px;
    padding: 0 16px;
    flex: 1;
    overflow: auto;

    .item {
      margin-top: 12px;

      &:first-child {
        margin-top: 0;
      }
    }
  }

  .opt {
    padding: 7px 16px;
    background: #fff;

    .btn {
      height: 36px;
      line-height: 34px;
    }
  }
}
