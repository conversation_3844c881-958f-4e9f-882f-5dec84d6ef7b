import { mapState, mapMutations } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState([
      'couponList', 'couponListVisible', 'headerDetail'
    ])
  },

  methods: {
    ...mapMutations([
      'setCouponListVisible'
    ]),
    onClose() {
      if (this.data.couponListVisible) {
        this.setCouponListVisible(false);
      }
    }
  }
});
