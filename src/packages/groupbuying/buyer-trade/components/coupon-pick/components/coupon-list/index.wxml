<wxs module="computed" src="./computed.wxs" />
<van-popup
  show="{{ couponListVisible }}"
  z-index="9991"
  position="bottom"
  bind:click-overlay="onClose"
>
  <view class="coupon-list">
    <view class="title">优惠券</view>
    <view class="header">
      <image class="avatar" src="{{ headerDetail.headerAvatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}" />
      <view class="desc">
        {{ headerDetail.headerName }}送你{{ couponList.length }}张共<text>{{ computed.totalValue(couponList) }}</text>元优惠券
      </view>
    </view>
    <view class="list">
      <view
        class="item"
        wx:for="{{ couponList }}"
        wx:key="{{ item.id }}"
      >
        <coupon-list-item item="{{ item }}" />
      </view>
    </view>
    <view class="opt">
      <van-button
        custom-class="btn"
        size="large"
        type="primary"
        round
        catchtap="onClose"
      >
        完成
      </van-button>
    </view>
  </view>
</van-popup>
