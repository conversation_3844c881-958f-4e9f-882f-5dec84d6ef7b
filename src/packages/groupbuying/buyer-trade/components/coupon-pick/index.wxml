<view
  wx:if="{{ couponList.length > 0 && actDetail && actDetail.state !== 2 }}"
  class="coupon custom-class"
  catchtap="onCouponTap"
>
  <view class="coupon-display">
    <view
      class="coupon-display-item"
      wx:for="{{ couponList }}"
      :key="{{ item.id }}"
    >
      <coupon-item item="{{ item }}" disabled="{{ actDetail.state === 0 }}" />
    </view>
  </view>
  <view class="coupon-summary">
    <text>{{ couponList.length }}张优惠券</text>
    <van-icon
      custom-class="icon"
      name="arrow-down"
    />
  </view>
</view>
