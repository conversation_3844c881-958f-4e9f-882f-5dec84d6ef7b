.float-nav {
  z-index: 2;
  position: fixed;
  bottom: 92px;
  left: 15px;

  &.iphone-x {
    bottom: 126px;
  }

  &--lower {
    bottom: 30px;

    &.iphone-x {
      bottom: 64px;
    }
  }

  &-icon {
    height: 48px;
    width: 48px;
    border-radius: 24px;
    border: 0;
    background: #fff;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.16);

    > .van-icon {
      width: 18px;
      height: 18px;
      top: 2px;
      font-size: 18px;
    }

    &--cart {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10px;

      image {
        width: 17px;
        height: 16px;
      }

      &--has {
        &::after {
          content: "";
          position: absolute;
          top: 10px;
          right: 10px;
          height: 8px;
          width: 8px;
          border-radius: 50%;
          background: #f44;
        }
      }
    }

    &--share {
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .text {
        margin-top: -2px;
        text-align: center;
        line-height: 16px;
        font-size: 10px;
        color: #323233;
      }
    }

    &--quick {
      position: relative;
      display: flex;
      flex-direction: column;
      transition: height 0.3s;
      overflow: hidden;

      .quick-btn {
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 48px;
        width: 48px;
        opacity: 1;
        transition: opacity 0.15s;
        z-index: 10;

        .text {
          font-size: 10px;
          line-height: 14px;
        }
      }

      .float-nav-list {
        opacity: 0;
      }

      &--show {
        .text {
          opacity: 0;
          z-index: 0;
        }

        .float-nav-list {
          opacity: 1;
          z-index: 10;
        }
      }
    }
  }
}

.float-nav-list {
  display: flex;
  flex-direction: column;
  opacity: 0;
  transition: opacity 0.15s;

  .item {
    display: flex;
    height: 48px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    border-top: 1px solid #ebedf0;

    &-text {
      margin-top: -2px;
      font-size: 10px;
      line-height: 14px;
    }

    &:first-child {
      border-top: 0;
    }
  }
}
