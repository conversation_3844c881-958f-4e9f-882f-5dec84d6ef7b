<view wx:if="{{ ready }}" class="float-nav {{ floatNavClassName }} {{ isIphoneX ? 'iphone-x' : '' }}">
  <view
    class="float-nav-icon float-nav-icon--quick {{ className }}"
    style="{{ styleHeight }}"
  >
    <view
      class="quick-btn"
      catchtap="onTrigger"
    >
      <view class="text">快捷</view>
      <view class="text">导航</view>
    </view>
    <view class="float-nav-list">
      <view
        class="item"
        wx:key="{{ item.name }}"
        wx:for="{{ nav }}"
        data-name="{{ item.name }}"
        catchtap="eventHandler"
      >
        <van-icon
          size="18px"
          name="{{ item.icon }}"
        />
        <view
          wx:if="{{ item.name !== 'arrow-down' }}"
          class="item-text"
        >
          {{ item.name }}
        </view>
      </view>
    </view>
  </view>
  <view
    class="float-nav-icon float-nav-icon--share"
    catchtap="share"
  >
    <van-icon
      name="share"
    />
    <view class="text">分享</view>
  </view>
  <view
    wx:if="{{ templateModel === 0 }}"
    class="{{ totalItem > 0 ? 'float-nav-icon float-nav-icon--cart float-nav-icon--cart--has' : 'float-nav-icon float-nav-icon--cart' }}"
    catchtap="showCart"
  >
    <image src="https://img.yzcdn.cn/public_files/2019/01/08/c64d39cb7e613ca7bfdcb00ca2d3e203.png" />
  </view>
</view>