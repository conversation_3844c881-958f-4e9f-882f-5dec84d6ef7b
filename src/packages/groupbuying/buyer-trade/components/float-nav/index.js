import { mapState, mapGetters, mapMutations, mapActions } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState([
      'isHeader', 'templateModel', 'isIphoneX'
    ]),
    ...mapGetters([
      'totalItem'
    ])
  },
  data: {
    ready: false,
    show: false,
    floatNavClassName: '',
    className: '',
    styleHeight: '',
    maxHeight: 144,
    nav: []
  },
  ready() {
    const { isHeader, templateModel } = this.data;
    this.navList = [
      {
        name: '订单',
        icon: 'orders-o',
        event: () => {
          this.navigateToOrder();
        }
      },
      {
        name: '团长中心',
        icon: 'user-o',
        event: () => {
          this.navigateToHeaderCenter();
        }
      },
      {
        name: '个人中心',
        icon: 'manager-o',
        event: () => {
          this.navigateToPersonalCenter();
        }
      },
      {
        name: 'arrow-down',
        icon: 'arrow-down',
        event: () => {
          this.onTrigger();
        }
      }
    ];
    if (isHeader) {
      this.setYZData({
        maxHeight: 192,
      });
    } else {
      this.navList.splice(1, 1);
    }
    this.setYZData({
      nav: this.navList.map(one => ({
        name: one.name,
        icon: one.icon
      })),
      ready: true,
      floatNavClassName: templateModel === 0 ? 'float-nav--lower' : ''
    });
  },
  methods: {
    ...mapMutations([
      'setShareVisible',
      'setCartVisible'
    ]),
    ...mapActions([
      'navigateToOrder',
      'navigateToHeaderCenter',
      'navigateToPersonalCenter'
    ]),
    share() {
      this.setShareVisible(true);
    },
    showCart() {
      this.setCartVisible(true);
    },
    eventHandler(e) {
      const name = e.currentTarget.dataset.name;
      const found = this.navList.find(one => one.name === name);
      if (found) {
        found.event();
      }
    },
    onTrigger() {
      const { show, maxHeight } = this.data;
      const nextShow = !show;
      const className = nextShow ? 'float-nav-icon--quick--show' : '';
      const styleHeight = nextShow ? `height: ${maxHeight}px` : '';
      this.setYZData({
        show: !this.data.show,
        className,
        styleHeight
      });
    }
  }
});
