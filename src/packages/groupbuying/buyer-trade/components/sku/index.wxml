<wxs src="./computed.wxs" module="computed" />
<van-popup
  z-index="9993"
  show="{{ skuVisible }}"
  custom-class="van-sku-pop-up"
  position="bottom"
  bind:click-overlay="closePopup"
>
  <view class="van-sku van-sku-container">
    <view class="van-hairline--bottom van-sku-header">
      <view class="van-sku-header__img-wrap" catchtap="onImgAction">
        <image wx:if="{{ defaultImage }}" height="80" width="80" src="{{ skuImage || defaultImage }}" />
      </view>
      <view class="van-sku-header__goods-info">
        <view class="van-sku__goods-name van-ellipsis">{{ skuData.title }}</view>
        <view class="van-sku__goods-price">
          <view class="van-sku__price-symbol">￥</view>
          <view class="van-sku__price-num">{{ selectedData ? computed.toYuan(selectedData.price) : skuData.spu.price }}</view>
          <view wx:if="{{(skuData.isLimitDiscount || skuData.umpActivityId) && selectedData}}" class="van-sku__origin-price-num">{{ computed.toYuan(selectedData.originPrice) }}</view>
        </view>
        <van-icon custom-class="van-sku__close-icon" name="close" catchtap="closePopup" />
      </view>
    </view>
    <view class="van-sku-body" style="max-height: 368px;">
      <view class="van-sku-group-container van-hairline--bottom">
        <view
          wx:for="{{ skuData.spu.tree }}"
          wx:for-index="skuIndex"
          wx:for-item="sku"
          wx:key="{{ sku.kId }}"
          class="van-sku-row"
        >
          <view class="van-sku-row__title">{{ sku.k }}：</view>
          <view
            wx:for="{{ sku.v }}"
            wx:for-index="skuValueIndex"
            wx:for-item="skuValue"
            wx:key="{{ skuValue.id }}"
            class="{{ selected[skuIndex] && selected[skuIndex].id === skuValue.id ? 'van-sku-row__item van-sku-row__item--active' : 'van-sku-row__item' }}"
            catchtap="chooseSku"
            data-sku-index="{{ skuIndex }}"
            data-sku-value-index="{{ skuValueIndex }}"
            data-sku-value-id="{{ skuValue.id }}"
            data-sku-value-image="{{ skuValue.imgUrl }}"
          >{{ skuValue.name }}</view>
        </view>
      </view>
      <view class="van-sku-stepper-stock">
        <view class="van-sku-stepper-container">
          <view class="van-sku__stepper-title">购买数量：</view>
          <view class="van-sku__stepper van-stepper">
            <view class="{{ minusDisable ? 'van-stepper__minus van-stepper__minus--disabled' : 'van-stepper__minus' }}" catchtap="minus"></view>
            <input
              wx:if="{{ !disableInput }}"
              class="van-stepper__input"
              type="tel"
              value="{{ num }}"
              bindchange="change"
              bindblur="blur"
              catchtap
            />
            <view class="{{ plusDisable ? 'van-stepper__plus van-stepper__plus--disabled' : 'van-stepper__plus' }}" catchtap="plus"></view>
          </view>
        </view>
        <view class="van-sku__stock">剩余{{ selectedData ? selectedData.stockNum : skuData.spu.stockNum }}件</view>
        <view wx:if="{{ skuData.quota > 0 }}" class="van-sku__quota">每人限购{{ skuData.quota }}件</view>
      </view>
    </view>
    <view class="van-sku-actions">
      <view class="van-sku-actions-btn" catchtap="confirm">选好了</view>
    </view>
  </view>
</van-popup>