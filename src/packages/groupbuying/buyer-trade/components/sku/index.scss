@import '~themes/abstracts/_mixins.scss';

.van-sku-pop-up {
  overflow: visible !important;
}

.van-sku {
  .van-hairline--bottom {
    border-bottom: 1px solid #eee;
  }

  &-container {
    font-size: 14px;
    color: #333;
  }

  &-body {
    max-height: 350px;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  /* sku header */
  &-header {
    position: relative;
    margin-left: 15px;
    background: #fff;

    &__img-wrap {
      position: relative;
      float: left;
      margin-top: -10px;
      width: 80px;
      height: 80px;
      background: #f8f8f8;
      border-radius: 2px;

      image {
        position: absolute;
        margin: auto;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        max-width: 100%;
        max-height: 100%;
      }
    }

    &__goods-info {
      padding: 10px 60px 10px 10px;
      min-height: 82px;
      overflow: hidden;
      box-sizing: border-box;
    }
  }

  &__goods-name {
    font-size: 12px;
  }

  &__price-symbol {
    vertical-align: middle;
  }

  &__price-num {
    font-size: 16px;
    vertical-align: middle;
  }

  &__origin-price-num {
    font-size: 10px;
    text-decoration: line-through;
    color: #999;
    margin-left: 2px;
  }

  &__goods-price {
    display: flex;
    color: #f44;
    margin-top: 10px;
    vertical-align: middle;
    align-items: baseline;
  }

  &__close-icon {
    top: 10px;
    right: 15px;
    font-size: 20px !important;
    color: #999;
    position: absolute !important;
    text-align: center;
  }

  &-group-container {
    margin-left: 15px;
    padding: 12px 0 2px;

    &--hide-soldout {
      .van-sku-row__item--disabled {
        display: none;
      }
    }
  }

  /* sku row */
  &-row {
    margin: 0 15px 10px 0;

    &:last-child {
      margin-bottom: 0;
    }

    &__title {
      padding-bottom: 10px;
    }

    &__item {
      display: inline-block;
      padding: 5px 9px;
      margin: 0 10px 10px 0;
      height: 28px;
      min-width: 52px;
      line-height: 18px;
      font-size: 12px;
      color: #333;
      background: #f6f6f6;
      text-align: center;
      border: 0;
      border-radius: 2px;
      box-sizing: border-box;

      &--active {
        color: #07c160;
        background: rgba(68, 187, 0, .1);
      }
    }
  }

  /* sku stepper */
  &-stepper {
    &-stock {
      padding: 12px 0;
      margin-left: 15px;
    }

    &-container {
      height: 30px;
      margin-right: 20px;
    }
  }

  &__stepper {
    float: right;
    display: flex;

    &-title {
      float: left;
      line-height: 30px;
    }
  }

  .van-stepper {
    &__minus, &__plus {
      position: relative;
      padding: 5px;
      width: 40px;
      height: 30px;
      line-height: 18px;
      box-sizing: border-box;
      background-color: #fff;
      border: 1px solid #eee;
      vertical-align: middle;
      text-align: center;

      &--disabled {
        background-color: #f8f8f8;
      }

      &::after {
        content: "";
        position: absolute;
        margin: auto;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #666;
        width: 1px;
        height: 9px;
      }

      &::before {
        content: "";
        position: absolute;
        margin: auto;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #666;
        width: 9px;
        height: 1px;
      }
    }

    &__minus::after {
      display: none;
    }

    &__input {
      width: 33px;
      height: 26px;
      line-height: 26px;
      padding: 1px;
      border: 1px solid #eee;
      border-width: 1px 0;
      border-radius: 0;
      box-sizing: content-box;
      color: #333;
      font-size: 14px;
      vertical-align: middle;
      text-align: center;
      -webkit-appearance: none;
      background: #fff;
    }
  }

  &__stock {
    display: inline-block;
    margin-right: 10px;
    color: #999;
    font-size: 12px;
  }

  &__quota {
    display: inline-block;
    color: #f44;
    font-size: 12px;
  }

  /* sku actions */
  &-actions {
    display: flex;

    &-btn {
      flex: 1;
      background: #07c160;
      color: #fff;
      height: 50px;
      line-height: 50px;
      font-size: 16px;
      text-align: center;
    }
  }
}
