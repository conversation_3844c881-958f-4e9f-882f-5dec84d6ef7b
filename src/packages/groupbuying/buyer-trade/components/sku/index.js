import { mapState, mapMutations, mapActions, mapGetters } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';
import { formatNumber } from '../../../common/format';
import { compose } from 'retail/util/base';
// import formatMoney from '@youzan/utils/money/format';

const getInitialData = () => {
  return {
    selected: [],
    selectedData: null,
    num: 1,
    minusDisable: true,
    plusDisable: false,
    maxNum: Infinity,
    skuImage: '',
    defaultImage: '',
  };
};

VanxComponent({
  properties: {
    skuData: {
      type: Object,
      observer(next, prev) {
        if (next && next.spu && next.spu.list && next.skuPrices) {
          next.spu = this.makeSkuIntersected(next)
          this.setData({
            skuData: next
          })
        }
      }
    },
    skuVisible: {
      type: <PERSON><PERSON><PERSON>,
      observer(nextVisible, prevVisible) {
        if (!prevVisible && prevVisible !== nextVisible) {
          this.resetSkuSelect();
        }
      },
    },
  },

  mapData: {
    ...mapState(['loading']),
  },

  data: getInitialData(),

  methods: {
    ...mapMutations(['setSkuVisible']),
    ...mapActions(['changeOrder']),
    closePopup() {
      this.setSkuVisible(false);
    },
    // 限时折扣商品, 可以展示的规格与 限时折扣活动配置有关, 需要做交集处理
    makeSkuIntersected(data) {
      // 1. 挑选出配置在 skuPrices 的 list 作为 Spu 返回
      // 2. spu.list 中的 price, 与 originPrice, 要以 skuPrices 里的为准
      const skuMap = new Map(data.spu.list.map((sku) => [sku.id, sku]))
      const list = data.skuPrices.map((skuPrice) => {
        const tmp = skuMap.get(skuPrice.skuId);
        tmp.price = skuPrice.promotionPrice;
        tmp.originPrice = skuPrice.price;
        return tmp;
      });
      let skuDataMap = {}
      list.forEach(item => {
        const skuArr = JSON.parse(item.sku)
        skuArr.forEach(sku => {
          if (!skuDataMap[sku.k]) {
            skuDataMap[sku.k] = []
          }
          skuDataMap[sku.k].push(sku.v)
        })
      })
      let tree = []
      data.spu.tree.forEach(item => {
          if (skuDataMap[item.k]) {
            let result = []
            item.v.forEach(sku => {
              if (skuDataMap[item.k].includes(sku.name)) {
                result.push(sku)
              }
            })
            item.v = result
            tree.push(item)
          }
      })

      tree = tree.map(item => {
        return {
          ...item,
          count: item.v.length
        }
      })
      return {
        ...data.spu,
        list,
        tree
      };
    },
    resetSkuSelect() {
      if (!this.data.skuData) return
      const { quota, quotaUsed, spu, pictures } = this.data.skuData;
      const { stockNum } = spu;
      const nextData = getInitialData();
      // 有限购，并且可购买数量小于库存
      if (quota > 0 && quota - quotaUsed < stockNum) {
        const left = quota - quotaUsed;
        const maxNum = Math.min(left, stockNum);
        // 一开始maxNum就是小于等于1的
        if (maxNum < 2) {
          nextData.plusDisable = false;
        }
      }
      if (pictures.length) {
        nextData.defaultImage = pictures[0].url;
      }
      this.setYZData(nextData);
    },
    onImgAction() {
      const { skuImage, defaultImage } = this.data;
      const imgSet = skuImage ? [skuImage] : [defaultImage];
      wx.previewImage({
        urls: imgSet,
      });
    },
    chooseSku(e) {
      const { spu } = this.data.skuData;
      const { tree, list } = spu;
      const treeLength = tree.length;
      const {
        skuIndex,
        skuValueIndex,
        skuValueId,
        skuValueImage = '',
      } = e.target.dataset;
      const { selected } = this.data;
      const nextSelected = selected.slice();
      nextSelected[skuIndex] = {
        id: skuValueId,
        skuValueIndex,
      };
      if (this.filterNotNull(nextSelected).length === treeLength) {
        const found = list.find((one) => {
          let flag = true;
          for (let i = 0; i < treeLength; i++) {
            if (nextSelected[i].id !== one[`s${i + 1}`]) {
              flag = false;
              break;
            }
          }
          return flag;
        });
        if (found) {
          this.setYZData({
            selectedData: found,
          });
        }
      }
      this.setYZData({
        selected: nextSelected,
        skuImage: skuValueImage,
      });
    },
    filterNotNull(list) {
      return list.filter((one) => one);
    },
    plus() {
      const { num } = this.data;
      this.setNum(num + 1);
    },
    minus() {
      const { num } = this.data;
      this.setNum(num - 1);
    },
    change(e) {
      const num = this.format(e.detail.value);
      this.setNum(num);
    },
    blur(e) {
      const value = e.detail.value;
      const num = this.format(value);
      if (value !== this.data.currentValue) {
        this.setNum(num);
      }
    },
    format(value) {
      value = formatNumber(String(value));
      // format range
      value = isNaN(value) || value === '' ? 0 : +value;

      return value;
    },
    setNum(num) {
      const { skuData, selectedData } = this.data;
      const { quota, quotaUsed, spu } = skuData;
      const stockNum = selectedData ? selectedData.stockNum : spu.stockNum;
      let maxNum = stockNum;
      if (quota > 0) {
        const left = quota - quotaUsed;
        maxNum = Math.min(maxNum, left);
        if (num > left && num < stockNum) {
          Toast('已经超出限购了');
          return;
        }
      }
      if (num > stockNum) {
        Toast('已经没有库存了');
        return;
      }
      // 数量低于最小
      if (num < 1) {
        Toast('最少选择一件');
        return;
      }
      // 和最小相等
      // 和最大相等
      this.setYZData({
        minusDisable: num <= 1,
        plusDisable: num >= maxNum,
      });
      // 设置数量
      this.setYZData({
        num,
      });
    },
    confirm() {
      const { selectedData, num } = this.data;
      if (!selectedData) {
        Toast('请先选择商品规格');
        return;
      }
      const { itemId, id } = selectedData;
      this.changeOrder({
        goodsId: itemId,
        delta: num,
        skuId: id,
        umpActivityId: this.data.skuData.umpActivityId
      });
      this.closePopup();
    },
  },
});
