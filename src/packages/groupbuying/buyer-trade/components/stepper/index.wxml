<view class="stepper custom-class">
  <view
    class="stepper__minus {{ detail.orderInfo.num <= detail.orderInfo.min ? 'stepper__minus--disabled' : '' }}"
    data-disabled="{{ detail.orderInfo.num <= detail.orderInfo.min }}"
  >
    <view class="tap-area" catchtap="minus"></view>
  </view>
  <!-- <input
    wx:if="{{ !disableInput }}"
    class="stepper__input {{ detail.orderInfo.min === detail.orderInfo.max ? 'stepper__input--disabled' : '' }}"
    type="tel"
    value="{{ currentValue }}"
    bindchange="change"
    bindblur="blur"
    bindfocus="focus"
    catchtap="tap"
  /> -->
  <view class="stepper__text">{{ detail.orderInfo.num || 0 }}</view>
  <view
    class="stepper__plus {{ detail.orderInfo.num >= detail.orderInfo.max ? 'stepper__plus--disabled' : '' }}"
    data-disabled="{{ detail.orderInfo.num >= detail.orderInfo.max }}"
    catchtap
  >
    <view class="tap-area" bindtap="plus"></view>
  </view>
</view>
