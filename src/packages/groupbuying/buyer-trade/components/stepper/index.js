import WscComponent from 'pages/common/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';
import { formatNumber } from '../../../common/format';

WscComponent({
  externalClasses: ['custom-class'],

  properties: {
    detail: Object,
    disableInput: <PERSON><PERSON>an,
  },

  data: {
    currentValue: 0,
  },

  observers: {
    'detail.**': function (val) {
      const { num } = val.orderInfo;
      if (num !== this.data.currentValue) {
        this.setYZData({
          currentValue: num,
        });
      }
    }
  },

  methods: {
    plus() {
      const { detail } = this.data;
      const { orderInfo } = detail;
      const { max, num, reason } = orderInfo;
      if (num >= max) {
        Toast(reason);
        return;
      }
      this.triggerEvent('plus', this.data.detail);
    },
    minus() {
      const { detail } = this.data;
      const { orderInfo } = detail;
      const { num } = orderInfo;
      if (num === 0) {
        return;
      }
      this.triggerEvent('minus', this.data.detail);
    },
    change(e) {
      const num = this.format(e.detail.value);
      this.setYZData({ currentValue: num });
      this.triggerEvent('change', { ...this.data.detail, num });
    },
    blur(e) {
      const num = this.format(e.detail.value);
      this.setYZData({ currentValue: num });
      this.triggerEvent('change', { ...this.data.detail, num });
    },
    tap() {},
    focus() {
      this.triggerEvent('focus', this.data.detail);
    },
    formatNumber(value) {
      return formatNumber(String(value));
    },
    format(value) {
      const { max, reason } = this.data.detail.orderInfo;
      const maxValue = 9999;
      value = this.formatNumber(value);

      // format range
      value = isNaN(value) || value === '' ? 0 : +value;
      if (value >= max) {
        Toast(reason);
        value = max;
      } else if (value > maxValue) {
        Toast('数量超出范围');
        value = maxValue;
      } else if (value === 0) {
        Toast('最少选择一件');
      }

      return value;
    },
  },
});
