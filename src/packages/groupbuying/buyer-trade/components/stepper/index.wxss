.stepper {
  color: #666;
  display: flex;
}

.stepper .stepper__minus, .stepper .stepper__plus {
  position: relative;
  width: 24px;
  height: 24px;
  padding: 0;
  border: none;
  outline: none;
}

.stepper .tap-area {
  position: absolute;
  height: 36px;
  width: 36px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.stepper .stepper__minus {
  background: url("https://b.yzcdn.cn/fix-base64/75a50621d2e83c81abfa04f989ce86352cdf35be4dc7e038c2569b117d5022c3.png") no-repeat center center;
  background-size: 100% 100%;
}

.stepper__minus--disabled {
  background: url("https://b.yzcdn.cn/fix-base64/3ea79204563e2e750d2edba4080f4b30ec3d2dc0dac55781492e70944dd689b3.png") no-repeat center center !important;
  background-size: 100% 100% !important;
}

.stepper__plus--disabled {
  background: url("https://b.yzcdn.cn/fix-base64/b73a7a1c466431418e16f45d706dbc3117a9d381ccac740966184b17f101d7e0.png") no-repeat center center !important;
  background-size: 100% 100% !important;
}

.stepper .stepper__input {
  display: inline-block;
  font-family: PingFangSC-Regular;
  text-align: center;
  vertical-align: middle;
  height: 22px;
  line-height: 22px;
  font-size: 14px;
  color: #333;
  width: 40px;
  padding: 0;
  border: 1px solid #dcdee0;
  border-radius: 2px;
  margin: 0 6px;
  outline: none;
  -webkit-appearance: none;
}

.stepper .stepper__text {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  height: 24px;
  line-height: 24px;
  font-size: 16px;
  color: #333;
  width: 33px;
  text-align: center;
}

.stepper .stepper__plus {
  background: url("https://b.yzcdn.cn/fix-base64/fc820d5222202972f13e13539ffbb49458c1d3b1a11de9481748a772879da7d5.png") no-repeat center center;
  background-size: 100% 100%;
}

.stepper .stepper--disabled {
  background: #f8f8f8;
  color: #bbb;
  border-color: #e8e8e8
}
