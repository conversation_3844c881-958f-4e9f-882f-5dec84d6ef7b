import { mapState, mapMutations } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState([
      'headerBuyerId',
      'activityId',
      'alias',
      'detailInfo',
      'detailVisible',
      'shareVisible'
    ])
  },

  methods: {
    ...mapMutations([
      'setShareVisible'
    ]),
    onClose() {
      this.setShareVisible(false);
    }
  }
});
