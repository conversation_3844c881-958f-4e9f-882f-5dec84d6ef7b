.stat-info {
  display: flex;
  height: 16px;
  line-height: 16px;
  box-sizing: border-box;

  &-num {
    display: inline-block;
    padding: 0 5px;
    border: 1px solid #ffe3e3;
    color: #f44;
    border-radius: 8px;
    font-size: 10px;
    height: 16px;
    line-height: 14px;
    box-sizing: border-box;

    &--sales {
      background: rgba(255, 68, 68, .1);
    }

    &--stock {
      background: #fff;
    }
  }

  &--both {
    .stat-info-num--sales {
      padding-right: 2px;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .stat-info-num--stock {
      padding-left: 2px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}
