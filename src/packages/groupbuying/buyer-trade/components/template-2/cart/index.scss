.groubuy-cart {
  display: flex;
  flex-direction: column;
  color: #333;

  .title {
    padding: 0 15px;
    height: 44px;
    line-height: 44px;

    .text {
      display: flex;
      float: left;

      .desc {
        font-size: 14px;
        margin-left: -2px;
        color: #999;
      }
    }

    .action {
      display: flex;
      float: right;
      color: #969799;
      font-size: 12px;

      .icon {
        top: 1px;
        margin-right: 5px;
      }
    }
  }

  .list {
    max-height: 275px;
    overflow-y: auto;
    border-top: 1px solid #e5e5e5;

    .item {
      display: flex;
      min-height: 68px;
      margin-left: 16px;
      padding-right: 16px;
      border-top: 1px solid #e5e5e5;

      &:first-child {
        border-top: 0;
      }

      .info {
        display: flex;
        justify-content: center;
        flex-direction: column;
        flex: 1;
        margin-right: 15px;

        .name {
          font-size: 14px;
          line-height: 18px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          word-break: break-all;
        }

        .sku {
          margin-top: 8px;
          font-size: 12px;
          line-height: 14px;
          color: #999;
        }
      }

      .sale {
        display: flex;
        width: 190px;
        align-items: center;

        .price {
          display: flex;
          flex: 1;
          color: #f44;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          align-items: center;
          justify-content: flex-end;
          margin-right: 10px;

          .num {
            margin-left: 3px;
            font-size: 18px;
          }
        }
      }
    }
  }

  .submit {
    height: 50px;
    box-sizing: border-box;
    box-shadow: 0 -2px 10px 0 rgba(125, 126, 128, 0.16);
  }
}