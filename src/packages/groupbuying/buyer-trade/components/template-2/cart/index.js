import { mapState, mapGetters, mapMutations, mapActions } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState(['cartVisible']),
    ...mapGetters(['totalItem', 'cartlist'])
  },
  methods: {
    ...mapMutations(['setCartVisible']),
    ...mapActions(['clearOriginlist', 'changeOrder']),
    closePopup() {
      this.setCartVisible(false);
    },
    _clearOriginlist() {
      this.clearOriginlist();
    },
    onPlus(e) {
      const { id, skuId, umpActivityId } = e.detail;
      this.changeOrder({
        goodsId: id,
        skuId,
        delta: 1,
        umpActivityId
      });
    },
    onMinus(e) {
      const { id, skuId,umpActivityId } = e.detail;
      this.changeOrder({
        goodsId: id,
        skuId,
        delta: -1,
        umpActivityId
      });
    },
    onChange(e) {
      const { id, skuId, num, umpActivityId } = e.detail;
      this.changeOrder({
        goodsId: id,
        skuId,
        num,
        umpActivityId
      });
    },
    onTrade() {
      this.setCartVisible(false);
      this.triggerEvent('trade');
    }
  }
});
