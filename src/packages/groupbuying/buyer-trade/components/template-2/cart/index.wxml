<van-popup
  show="{{ cartVisible }}"
  position="bottom"
  bind:click-overlay="closePopup"
>
  <view class="groubuy-cart">
    <view class="title clearfix">
      <view class="text">
        <view class="label">已选商品</view>
        <view class="desc">（共{{ totalItem }}件）</view>
      </view>
      <view
        class="action"
        catchtap="_clearOriginlist"
      >
        <van-icon custom-class="icon" name="delete" />
        <view>清空商品</view>
      </view>
    </view>
    <view class="list">
      <view
        class="item"
        wx:for="{{ cartlist }}"
        wx:for-item="item"
        wx:key="{{ item.id + item.skuId }}"
      >
        <view class="info">
          <view class="name">{{ item.title }}</view>
          <view
            wx:if="{{ item.skuName }}"
            class="sku"
          >
            已选：{{ item.skuName }}
          </view>
        </view>
        <view class="sale">
          <view class="price">
            <view class="unit">¥</view>
            <view class="num">{{ item.price }}</view>
          </view>
          <stepper
            detail="{{ item }}"
            bind:plus="onPlus"
            bind:minus="onMinus"
            bind:change="onChange"
          />
        </view>
      </view>
    </view>
    <view class="submit" />
  </view>
</van-popup>