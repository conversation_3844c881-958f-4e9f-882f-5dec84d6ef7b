.roll-order {
  position: absolute;
  top: 8px;
  left: 15px;
  z-index: 2;
  pointer-events: none;

  &.sticky {
    position: fixed;
    top: 50px;
  }

  &__item {
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, .5);
    border-radius: 14.5px;
    padding-right: 10px;
    transform: translateX(15px);
    opacity: 0;
    height: 30px;
    max-width: 200px;

    image {
      width: 32px;
      height: 28px;
      border-radius: 50%;
      margin:0  5px 0  1px;
    }

    text {
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      font-size: 12px;
      line-height: 30px;
      color: #fff;
    }

    &.active {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .enter {
    transition: all .1s ease;
  }

  .leave {
    transition: all .8s cubic-bezier(1, .5, .8, 1);
  }
}
