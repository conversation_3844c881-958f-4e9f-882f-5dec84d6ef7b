import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { getRollOrders } from '../../../api';

VanxComponent({
  mapData: {
    ...mapState(['activityId', 'fixedRollOrder'])
  },

  data: {
    currentIndex: 0,
    showRoll: false,
    avatar: '',
    customerName: '',
    goodsName: '',
    goodsNum: '',
    rollOrderList: []
  },

  ready() {
    const params = {
      activityId: this.data.activityId,
      pageSize: 30
    };
    getRollOrders(params).then(data => {
      const {
        list = []
      } = data;
      list.forEach(one => {
        one.customerAvatar = cdnImage(one.customerAvatar, '!30x30.png');
      });
      if (list.length > 0) {
        this.setYZData({
          rollOrderList: list
        }, () => {
          this._init();
        });
      }
    });
  },

  methods: {
    // 初始化data数据
    _initData() {
      this.setYZData({
        avatar: this.data.rollOrderList[this.data.currentIndex].customerAvatar ? this.data.rollOrderList[this.data.currentIndex].customerAvatar : '',
        customerName: this.data.rollOrderList[this.data.currentIndex].customerNickname,
        goodsName: this.data.rollOrderList[this.data.currentIndex].itemTitle,
        goodsNum: this.data.rollOrderList[this.data.currentIndex].itemNum
      });
    },

    // 初始化组件
    _init() {
      this.setYZData({
        showRoll: false
      });
      setTimeout(() => {
        this.setYZData({
          showRoll: true
        });
        let index = this.data.currentIndex + 1;
        // 已经到列表尾部
        if (index === this.data.rollOrderList.length) {
          index = 0;
        }
        this.setYZData({
          currentIndex: index
        });
        this._initData();
        setTimeout(() => {
          this._init();
        }, 3000);
      }, 1000);
    }
  }
});
