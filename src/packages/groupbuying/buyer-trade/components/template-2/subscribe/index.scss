.bar {
  display: flex;
  position: fixed;
  bottom: 0;
  padding: 0 16px;
  width: 100%;
  height: 50px;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  background: #fff;
  box-shadow: 0 -2px 10px 0 rgba(125, 126, 128, 0.16);
  z-index: 10;

  &.iphone-x {
    padding-bottom: 34px;
    height: 84px;
  }

  .text {
    font-size: 12px;
    font-weight: bold;

    .orange {
      color: #ff7b1c;
    }
  }

  .btn {
    height: 36px;
    line-height: 34px;
    padding: 0 26px;
  }

  .btn-disabled {
    height: 36px;
    line-height: 34px;
    padding: 0 26px;
    background: #c8c9cc !important;
    border-color: #c8c9cc !important;
  }
}
