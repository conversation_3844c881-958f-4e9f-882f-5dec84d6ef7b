import { mapState, mapGetters, mapActions } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';

VanxComponent({
  mapData: {
    ...mapState(['actDetail', 'subscribe', 'isIphoneX']),
    ...mapGetters(['subscribeTime'])
  },

  methods: {
    ...mapActions([
      'subscribe'
    ]),
    onSubscribe() {
      if (this.data.subscribe === 1) {
        Toast('已经订阅过了');
        return;
      }
      this.subscribe();
    }
  },
});
