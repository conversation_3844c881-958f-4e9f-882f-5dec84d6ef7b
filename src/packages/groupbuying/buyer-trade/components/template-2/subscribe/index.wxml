<!-- 活动是未开始状态 && 活动loading中 -->
<view
  wx:if="{{ actDetail.state === 0 && subscribe !== 0 }}"
  class="bar {{ isIphoneX ? 'iphone-x' : '' }}"
>
  <view class="text">
    <text wx:if="subscribeTime">团购将于<text class="orange">{{ subscribeTime.month }}</text>月<text class="orange">{{ subscribeTime.date }}</text>日<text class="orange">{{ subscribeTime.hour }}</text>:<text class="orange">{{ subscribeTime.minute }}</text>:<text class="orange">{{ subscribeTime.second }}</text>开始</text>
  </view>
  <van-button
    wx:if="{{ subscribe === 1 }}"
    custom-class="btn-disabled"
    type="primary"
    round
  >
    已订阅
  </van-button>
  <van-button
    wx:else
    custom-class="btn"
    type="primary"
    round
    bind:click="onSubscribe"
  >
    提醒我
  </van-button>
</view>
