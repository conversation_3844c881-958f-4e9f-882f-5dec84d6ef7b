<view class="groupbuy-goods-list-title">团购商品</view>
<van-tabs
  wx:if="{{ categoryList.length > 0 }}"
  active="{{ active }}"
  border="{{ false }}"
  line-width="40"
  sticky="{{ true }}"
  color="#07c160"
  bind:change="onChange"
  bind:scroll="onScroll"
  custom-class="{{ isFixed ? 'category-fixed' : '' }}"
>
  <van-tab
    wx:for="{{ categoryList }}"
    wx:key="{{ item.categoryId }}"
    title="{{ item.categoryName }}"
  >
    <view wx:if="{{ index === 0 || index === active  }}" class="goods-list">
      <view
        class="goods-list-item"
        wx:for="{{_buyingList}}"
        wx:for-item="goods"
        wx:if="{{ goods.join }}"
        wx:key="{{ goods.itemId }}"
      >
        <item
          item="{{ goods }}"
          is-buying="{{ isBuying }}"
          sku-visible="{{ skuVisible }}"
          sku-data="{{ skuData }}"
        />
      </view>
    </view>
  </van-tab>
</van-tabs>
<view wx:else class="goods-list">
  <view
    class="goods-list-item"
    wx:for="{{ _buyingList }}"
    wx:for-item="goods"
    wx:if="{{ goods.join }}"
    wx:key="{{ goods.itemId }}"
  >
    <item
      item="{{ goods }}"
      is-buying="{{ isBuying }}"
    />
  </view>
</view>