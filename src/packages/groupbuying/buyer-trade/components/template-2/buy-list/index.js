import Event from '@youzan/weapp-utils/lib/event';
import { mapGetters, mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

const LIST_LOAD_NUM = 20; // 列表一次新增渲染商品数量

VanxComponent({
  mapData: {
    ...mapState(['categoryList', 'skuVisible', 'skuData']),
    ...mapGetters(['buyinglist', 'isBuying']),
  },

  data: {
    active: 0,
    isFixed: false,
    listNum: LIST_LOAD_NUM, // 默认进房0商品，触发第一次新增渲染商品展示一次新增渲染商品的数量
    _buyingList: [],
  },

  ready() {
    Event.on('buying:onListReachBottom', () => {
      this.listLoadMore();
    });
  },

  detached() {
    Event.off('buying:onListReachBottom');
  },

  observers: {
    /**
     * getter取到的值会自动更新，需要监听取到最新数据做渲染
     * 小程序observers这里不是深度监听，要加上通配符监听下面的变动
     */
    'buyinglist.**': function (buyinglist) {
      const { active, categoryList } = this.data;
      const buyingListComplete = this.displayList(buyinglist, active, categoryList)
      this.setData({
        _buyingList: buyingListComplete.slice(0, this.data.listNum)
        // _buyingList: buyingListComplete
      })
    },
  },

  methods: {
    displayList(buyinglist, active, categoryList = []) {
      const currentCategory = categoryList.length > active ? categoryList[active] : null;
      const currentId = currentCategory ? currentCategory.categoryId : -1;
      if (categoryList.length === 0 || currentId === -1) return buyinglist;
      return buyinglist.filter(
        (one) => one.categoryIds && one.categoryIds.indexOf(currentId) > -1
      );
    },

    listLoadMore() {
      const listNum = this.data.listNum + LIST_LOAD_NUM;
      const { active, buyinglist, categoryList } = this.data;
      const buyingListComplete =
        active === 0 ? buyinglist : this.displayList(buyinglist, active, categoryList);
      this.setYZData({
        listNum,
        _buyingList: buyingListComplete.slice(0, listNum),
      });
    },

    onChange(e) {
      const active = e.detail.index;
      this.setYZData(
        {
          active,
          listNum: 0, // 商品数量置0，重新渲染20个商品
        },
        () => {
          this.listLoadMore();
        }
      );
    },

    onScroll(e) {
      const { isFixed } = e.detail;
      this.setYZData({
        isFixed,
      });
    },
  },
});
