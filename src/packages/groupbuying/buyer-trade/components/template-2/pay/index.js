import { mapState, mapGetters, mapMutations } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState([
      'cartVisible', 'isIphoneX'
    ]),
    ...mapGetters([
      'isBuying', 'totalItem', 'realMoney'
    ])
  },
  methods: {
    ...mapMutations([
      'setCartVisible'
    ]),
    payToHeader() {
      this.triggerEvent('trade');
    },
    showCart() {
      this.setCartVisible(!this.data.cartVisible);
    }
  }
});
