.total-pay {
  margin: 24px 0 10px 0;
  text-align: center;
  color: #666;
  font-size: 14px;
  height: 24px;
  line-height: 24px;

  &__price {
    display: inline-block;
    color: #f44;
    font-size: 12px;

    text {
      font-size: 18px;
    }
  }
}

.pay {
  position: fixed;
  bottom: 0;
  display: flex;
  width: 100%;
  height: 50px;
  padding: 7px 16px 7px 72px;
  z-index: 9990;
  background-color: #fff;
  box-sizing: border-box;
  box-shadow: 0 -2px 10px 0 rgba(125, 126, 128, 0.16);

  &.iphone-x {
    height: 84px;
    padding-bottom: 41px;
  }

  .price{
    display:flex;
    align-items:center;
    flex: 1;
    color: #f44;
  }

  .buttom-pay{
    width: 96px;
    height: 36px;
    line-height: 36px;
  }

  .icon-cart{
    position: absolute;
    left: 16px;
    top: -5px;
    width: 45px;
    height: 45px;
    line-height: 52px;
    text-align: center;
    border-radius: 50%;
    background-color: #07C160;
    box-shadow: 0px 0px .1px 3px #fff;
  }
}
