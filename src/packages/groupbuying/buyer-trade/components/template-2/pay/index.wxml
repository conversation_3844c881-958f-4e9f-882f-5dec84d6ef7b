<view class="pay {{ isIphoneX ? 'iphone-x' : '' }}" wx:if="{{ isBuying }}">
  <view class="icon-cart" bindtap="showCart">
    <van-icon wx:if="{{ totalItem === 0 }}" color="#fff" size="24px" name="shopping-cart" />
    <van-icon wx:else color="#fff" size="24px" info="{{ totalItem }}" name="shopping-cart" />
  </view>
  <view class="price" wx:if="{{ isBuying }}">
    <price wx:if="{{ realMoney !== '0.00' }}" price="{{ realMoney }}" />
  </view>
  <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="payToHeader" wx:if="{{ isBuying }}">
    <van-button
      round
      custom-class="buttom-pay"
      type="primary"
      wx:if="{{ isBuying }}"
      disabled="{{ realMoney === '0.00' }}"
    >
      去结算
    </van-button>
  </user-authorize>
</view>
