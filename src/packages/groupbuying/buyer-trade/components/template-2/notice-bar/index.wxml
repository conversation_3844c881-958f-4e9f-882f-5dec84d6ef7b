<view wx:if="{{ !loading }}" class="count-down-notice">
  <image class="icon" src="{{ leftIcon }}" mode="aspectFit|aspectFill|widthFix" />
  <view wx:if="{{ empty }}" class="text">团长暂未开团</view>
  <view wx:else class="text">
    <view>
      {{ noticeText }}
    </view>
    <view class="text" wx:if="{{ !isEnd }}">
      <view class="text" wx:if="{{ day !== -1 }}">
        <view class="number">{{ day }}</view>天 
      </view>
      <view class="text" wx:if="{{ hour !== -1 }}">
        <view class="number">{{ hour }}</view>时 
      </view>
      <view class="text" wx:if="{{ min !== -1 }}">
        <view class="number">{{ min }}</view>分 
      </view>
      <view class="text" wx:if="{{ sec !== -1 }}">
        <view class="number">{{ sec }}</view>秒 
      </view>
    </view>
  </view>
</view>
<view class="notice-bottom" />
