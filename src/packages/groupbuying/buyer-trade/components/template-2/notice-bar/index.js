import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import Countdown from 'utils/countdown';

VanxComponent({
  mapData: {
    ...mapState([
      'actDetail', 'empty'
    ])
  },

  data: {
    leftIcon:
      'https://b.yzcdn.cn/fix-base64/cb974128e7231f3077f519bf1287b521bff9da61ebd97ec0303be86326a4f649.png',
    noticeText: '',
    day: 0,
    hour: 0,
    min: 0,
    sec: 0,
    isEnd: false,
    loading: true
  },

  ready() {
    const { actDetail } = this.data;
    if (actDetail) {
      this.initNoticeText();
    } else {
      this.setYZData({
        loading: false
      });
    }
  },

  methods: {
    initNoticeText() {
      let text;
      let time;
      const { state, startTime, endTime } = this.data.actDetail;
      switch (state) {
        case 0:
          text = '距离团购活动开始还有 ';
          time = startTime;
          break;
        case 1:
          text = '距离团购活动结束还有 ';
          time = endTime;
          break;
        case 2:
          text = '团购已结束';
          this.setYZData({
            isEnd: true
          });
          break;
        default:
          text = '距离团购活动结束还有 ';
          break;
      }
      this.setYZData({
        loading: false
      });
      if (state === 2) {
        this.setYZData({
          noticeText: text
        });
        return;
      }
      const start = new Date().getTime();
      const elapsed = time - start;
      new Countdown(elapsed, {
        onChange: timeData => {
          if (timeData.day > 1) {
            timeData.hour = timeData.minute = timeData.second = -1;
          } else if (timeData.day > 0) {
            timeData.minute = timeData.second = -1;
          } else {
            timeData.day = -1;
          }
          this.setYZData({
            noticeText: text,
            day: timeData.day,
            hour: timeData.hour,
            min: timeData.minute,
            sec: timeData.second
          });
        },
        onEnd: () => {
          // 如果是活动开始倒计时结束，刷新页面
          wx.startPullDownRefresh();
        }
      });
    }
  }
});
