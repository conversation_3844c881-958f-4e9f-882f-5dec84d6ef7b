import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapActions, mapMutations, mapState } from '@youzan/vanx';
import CountDown from '@youzan/weapp-utils/lib/countdown';
import {padStart} from '../../../../common'

VanxComponent({
  properties: {
    item: Object,
    status: String,
    startTime: Number,
    endTime: Number,
    skuVisible: {
      type: <PERSON><PERSON><PERSON>,
      observer(newVal) {
        this.setYZData({
          hideStepper: newVal && this.data.skuData && this.data.item && this.data.skuData.id === this.data.item.itemId,
        });
      },
    },
    isBuying: {
      type: Boolean,
      value: true,
    },
  },
  mapData: {
    ...mapState(['limitDiscountList', 'limitDiscountIndex']),
  },
  data: {
    hideStepper: false,
    time: {
      d: 0,
      h: 0,
      m: 0,
      s: 0,
    },
  },

  ready() {
    const { startTime, endTime, status } = this.data;
    const start = +Date.now();
    const delta = status === '未开始' ? startTime - start : endTime - start;
    if (delta > 0 && status === '未开始') {
      new CountDown(delta, {
        onChange: (timeData) => {
          const { day: d, hour: h, minute: m, second: s } = timeData;
          this.setYZData({
            'time.d': padStart(String(d), 2, '0'),
            'time.h': padStart(String(h), 2, '0'),
            'time.m': padStart(String(m), 2, '0'),
            'time.s': padStart(String(s), 2, '0'),
          });
        },
        onEnd: () => {
          this.setEnd();
        },
      });
    }
  },

  methods: {
    ...mapActions(['showDetail', 'plus', 'minus', 'change', 'getItemDetail', 'fetchLimitDiscountInfo']),
    ...mapMutations(['setSkuData', 'setSkuVisible']),
    onShowDetail() {
      const { itemId, umpActivityId } = this.data.item;
      this.showDetail({ goodsId: itemId, umpActivityId, limitDiscountDetail: {
        startTime: this.data.startTime,
        endTime: this.data.endTime,
        status: this.data.status
      } });
    },
    setEnd() {
      this.fetchLimitDiscountInfo()
    },
    onPlus() {
      const { itemId, umpActivityId, skuPrices } = this.data.item;
      const skuId = skuPrices[0].skuId;
      this.plus({ goodsId: itemId, umpActivityId, skuId });
    },
    onMinus() {
      const { itemId, umpActivityId, skuPrices } = this.data.item;
      const skuId = skuPrices[0].skuId;
      this.minus({ goodsId: itemId, umpActivityId, skuId });
    },
    onChange(e) {
      const { itemId, num } = e.detail;
      const { umpActivityId, skuPrices } = this.data.item;
      const skuId = skuPrices[0].skuId;
      this.change({
        itemId,
        num,
        umpActivityId,
        skuId,
      });
    },
    onFocus() {
      const { itemId } = this.data.item;

      // stepper输入框focus
      this.getItemDetail(itemId).then((data) => {
        const { hasSku } = data;
        if (hasSku) {
          this.setSkuData(data);
          this.setSkuVisible(true);
          this.setYZData({ hideStepper: true });
        }
      });
    },
  },
});
