<wxs src="./computed.wxs" module="computed" />
<view class="buying-item" catchtap="onShowDetail">
  <view class="limitdiscount-img">
    <view class="limitdiscount-tag">{{limitDiscountList[limitDiscountIndex].description || '限时折扣'}}</view>
    <image class="buying-item__img" src="{{ item.picture[0].url }}" />
    <view class="limitdiscount-time" wx:if="{{status === '未开始'}}">
      <span>距开始</span>
      <view class="time">
          <view class="time-box" wx:if="{{time.d !== '00'}}">{{ time.d }}天</view>
          <view class="time-box">{{ time.h }}</view>
          <view class="time-comma">:</view>
          <view class="time-box">{{ time.m }}</view>
          <view class="time-comma">:</view>
          <view class="time-box">{{ time.s }}</view>
        </view>
    </view>
  </view>
  <view class="buying-item__desc">
      <!-- <view class="limit-discount__discount-price">减{{computed.toYuan(item.skuPrices[0].price - item.skuPrices[0].promotionPrice)}}元起</view> -->
    <view class="buying-item__desc__title">{{ item.title }}</view>
    <stat-num item="{{ item }}"></stat-num>
    <view class="buying-item__group" wx:if="{{ item.customerCount > 3 }}">
      <image
        wx:for="{{ item.customerAvatar }}"
        wx:for-item="customer"
        wx:key="{{ customer.userId }}"
        src="{{ customer.avatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}"
      />
      <text>...等{{ item.customerCount }}人已团</text>
    </view>
    <view class="buying-item__group" wx:elif="{{ item.customerCount > 0 }}">
      <image
        wx:for="{{ item.customerAvatar }}"
        wx:for-item="customer"
        wx:key="{{ customer.userId }}"
        src="{{ customer.avatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}"
      />
      <text>{{ item.customerCount }}人已团</text>
    </view>
    <view class="buying-item__desc__price">
      <price price="{{ computed.toYuan(item.skuPrices[0].promotionPrice) }} " originPrice="{{ computed.toYuan(item.skuPrices[0].price) }}" />
    </view>
    <stepper
      wx:if="{{ isBuying && item.stockNum > 0 && !hideStepper && status === '进行中' }}"
      detail="{{ item }}"
      bind:plus="onPlus"
      bind:minus="onMinus"
      bind:change="onChange"
      bind:focus="onFocus"
      custom-class="buying-item-stepper"
    ></stepper>
    <view class="buying-item-none" wx:if="{{ item.stockNum === 0 }}">卖完啦</view>
  </view>
</view>
