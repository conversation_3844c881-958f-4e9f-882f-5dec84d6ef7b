.buying-item {
  display: flex;

  &-none {
    position: absolute;
    right: 0;
    bottom: -7px;
    color: #333;
    font-size: 14px;
    line-height: 24px;
  }

  &__group {
    display: flex;
    margin-top: 8px;
    align-items: center;
    flex: 1;
    line-height: 16px;

    image {
      margin-left: -5px;
      height: 16px;
      width: 16px;
      border-radius: 50%;
      border: 1px solid #fff;

      &:first-child {
        margin-left: 0;
        z-index: 2;
      }

      &:nth-child(2) {
        z-index: 1;
      }
    }

    text {
      margin-left: 8px;
      font-size: 12px;
      color: #4a4a4a;
    }
  }

  &-stepper {
    position: absolute;
    right: 0;
    bottom: 0;
    background: #fff;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -45px;
      width: 45px;
      height: 100%;
      background-image: linear-gradient(
        to right,
        rgba(255, 255, 255, 0),
        rgba(255, 255, 255, 1)
      );
    }
  }

  &__img {
    width: 120px;
    height: 120px;
    border-radius: 4px;
    margin-right: 8px;
  }

  &__desc {
    display: inline-block;
    position: relative;
    flex: 1;

    &__title {
      margin-bottom: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      line-height: 20px;
      color: #333;
      font-size: 14px;
      font-weight: bolder;
      max-height: 40px;
      word-break: break-all;
    }

    &__price {
      display: flex;
      align-items: baseline;
      position: absolute;
      bottom: 0;

      &-buy {
        font-weight: 500;
        display: inline-block;
        color: #f44;
        font-size: 12px;
        margin-right: 5px;

        &-num {
          font-size: 16px;
        }
      }

      &-origin {
        display: inline-block;
        color: #999;
        font-size: 10px;
        text-decoration: line-through;
      }
    }
  }
}
