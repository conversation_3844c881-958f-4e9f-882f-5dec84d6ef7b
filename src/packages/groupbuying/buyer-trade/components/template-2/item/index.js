import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapActions, mapMutations } from '@youzan/vanx';

VanxComponent({
  properties: {
    item: Object,
    skuVisible: {
      type: <PERSON><PERSON><PERSON>,
      observer(newVal) {
        this.setYZData({
          hideStepper: newVal && this.data.skuData && this.data.item && this.data.skuData.id === this.data.item.itemId,
        });
      },
    },
    isBuying: {
      type: Boolean,
      value: true,
    },
  },
  data: {
    hideStepper: false,
  },

  methods: {
    ...mapActions(['showDetail', 'plus', 'minus', 'change', 'getItemDetail']),
    ...mapMutations(['setSkuData', 'setSkuVisible']),
    onShowDetail() {
      const { itemId } = this.data.item;
      this.showDetail({goodsId: itemId});
    },
    onPlus() {
      const { itemId } = this.data.item;
      this.plus({goodsId: itemId});
    },
    onMinus() {
      const { itemId } = this.data.item;
      this.minus({goodsId: itemId});
    },
    onChange(e) {
      const { itemId, num } = e.detail;
      this.change({
        itemId,
        num,
      });
    },
    onFocus() {
      const { itemId } = this.data.item;

      // stepper输入框focus
      this.getItemDetail(itemId).then((data) => {
        const { hasSku } = data;
        if (hasSku) {
          this.setSkuData(data);
          this.setSkuVisible(true);
          this.setYZData({ hideStepper: true });
        }
      });
    },
  },
});
