<wxs src="./computed.wxs" module="computed" />
<view class="buying-item" catchtap="onShowDetail">
  <image class="buying-item__img" src="{{ item.picture[0].url }}" />
  <view class="buying-item__desc">
  <p>{{item.umpType}}</p>
    <view wx:if="{{item.umpType == 11}}">
      减{{computed.toYuan(item.skuPrices[0].price - item.skuPrices[0].promotionPrice)}}元起
    </view>
    <view class="buying-item__desc__title">{{ item.title }}</view>
    <stat-num item="{{ item }}"></stat-num>
    <view class="buying-item__group" wx:if="{{ item.customerCount > 3 }}">
      <image
        wx:for="{{ item.customerAvatar }}"
        wx:for-item="customer"
        wx:key="{{ customer.userId }}"
        src="{{ customer.avatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}"
      />
      <text>...等{{ item.customerCount }}人已团</text>
    </view>
    <view class="buying-item__group" wx:elif="{{ item.customerCount > 0 }}">
      <image
        wx:for="{{ item.customerAvatar }}"
        wx:for-item="customer"
        wx:key="{{ customer.userId }}"
        src="{{ customer.avatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}"
      />
      <text>{{ item.customerCount }}人已团</text>
    </view>
    <view class="buying-item__desc__price">
      <price wx:if="{{item.umpType == 11}}" price="{{ computed.toYuan(item.skuPrices[0].promotionPrice) }} " originPrice="{{ originPrice(item.skuPrices[0].price) }}" />
      <price wx-else price="{{ computed.toYuan(item.price) }} " originPrice="{{ item.markPrice }}" />
    </view>
    <stepper
      wx:if="{{ isBuying && item.stockNum > 0 && !hideStepper }}"
      detail="{{ item }}"
      bind:plus="onPlus"
      bind:minus="onMinus"
      bind:change="onChange"
      bind:focus="onFocus"
      custom-class="buying-item-stepper"
    ></stepper>
    <view class="buying-item-none" wx:if="{{ item.stockNum === 0 }}">卖完啦</view>
  </view>
</view>
