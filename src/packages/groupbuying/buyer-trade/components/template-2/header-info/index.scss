$font-color: #333;
$gray: #999;

@mixin fontColor($fontSize, $color) {
  font-size: $fontSize;
  color: $color;
}

.header-info {
  position: relative;
  margin: -40px 12px 0;
  padding: 16px 15px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 2px 10px 0 rgba(125, 126, 128, .15);
  z-index: 10;

  .default-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 44px;
      height: 44px;
      border-radius: 50%;
    }

    &__name {
      margin-left: 12px;
      flex: 1;

      .detail {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        line-height: 22px;
        margin-bottom: 4px;
        @include fontColor(16px, $font-color);

        .header-tag {
          width: 32px;
          line-height: 16px;
          text-align: center;
          @include fontColor(10px, #5d5134);
          background-image: linear-gradient(
            -134deg,
            #f9e1aa 0%,
            #ffefca 44%,
            #fff0cf 52%,
            #e1c78e 100%
          );
          border-top-right-radius: 10px;
          border-bottom-left-radius: 10px;
          margin: 0 8px;
        }

        &.address, &.phoneNum {
          line-height: 18px;
          @include fontColor(13px, $font-color);

          .contact {
            @include fontColor(13px, $gray);
          }
        }
      }

      .location {
        position: relative;
        display: flex;
        align-items: center;
        line-height: 22px;
        font-size: 12px;
        color: #969799;

        image {
          width: 9px;
          height: 12px;
        }

        .text {
          margin-left: 4px;
          max-width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .icon {
          display: block;
          margin-left: 2px;
        }

        .tip {
          position: absolute;
          bottom: -34px;
          left: 13px;
          padding: 0 8px;
          height: 26px;
          line-height: 26px;
          background: rgba(0, 0, 0, 0.7);
          font-size: 10px;
          color: #fff;
          border-radius: 4px;
          white-space: nowrap;

          &--arrow {
            position: absolute;
            top: -8px;
            left: 10px;
            border: 4px transparent solid;
            border-bottom-color: rgba(0, 0, 0, 0.7);
          }
        }
      }
    }
  }

  .coupon-pick-wrapper {
    margin: 8px 0 0 56px;
  }
}
