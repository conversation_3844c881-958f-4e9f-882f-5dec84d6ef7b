<view
  hidden="{{ !headerDetail }}"
  class="header-info"
>
  <view class="default-info">
    <image
      class="avatar"
      src="{{ headerDetail.headerAvatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}"
    ></image>
    <view class="default-info__name">
      <view class="detail">
        <view>
          {{ headerDetail.headerName }}
        </view>
        <view class="header-tag">
          团长
        </view>
        <view class="location" catchtap="switch">
          <image src="https://img.yzcdn.cn/wsc-h5-ump/groupbuying/p-green.png"></image>
          <view class="text">{{ headerDetail.subdistrict }}</view>
          <van-icon wx:if="{{ showSwitch }}" custom-class="icon" name="arrow" />
          <view hidden="{{ !showNear }}" class="tip" catchtap="onTap">
            这是离你最近的团长，点击可查看其它团长
            <view class="tip--arrow" />
          </view>
        </view>
      </view>
      <view class="detail address">
        <view class="contact">提货地址：</view>
        <view>{{ headerDetail.fetchAddress}}</view>
      </view>
      <view class="detail phoneNum" wx:if="{{showMobile}}">
        <view class="contact">手机号码：</view>
        <view>{{ headerDetail.mobile}}</view>
      </view>
    </view>
  </view>
  <coupon-pick custom-class="coupon-pick-wrapper" />
</view>
