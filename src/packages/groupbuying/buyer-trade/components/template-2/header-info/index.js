import { mapState, mapMutations } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState(['headerDetail', 'showNear', 'showSwitch', 'showMobile'])
  },
  methods: {
    ...mapMutations([
      'setNear'
    ]),
    switch() {
      const { showSwitch, headerDetail } = this.data;
      const { headerBuyerId } = headerDetail;
      if (!showSwitch) return;
      wx.navigateTo({
        url: `/packages/groupbuying/location/index?headerBuyerId=${headerBuyerId}`
      });
    },

    onTap() {
      this.setNear(false);
    }
  }
});
