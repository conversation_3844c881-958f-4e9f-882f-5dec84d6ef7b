<wxs src="./computed.wxs" module="computed" />
<view class="limit-discount" style="position: relative;" wx:if="{{ limitDiscountListWithOrder.length > 0 }}">
    <view class="limit-discount-logo">
        <image src="https://b.yzcdn.cn/path/to/cdn/dir/limitdiscountlogo.png" />
    </view>
    <van-tabs swipe-threshold="{{2}}" active="{{ active }}" border="{{ false }}" line-width="40" sticky="{{ false }}" color="#07c160" bind:change="onChange" bind:scroll="onScroll" custom-class="{{ isFixed ? 'category-fixed' : '' }}">
        <van-tab wx:for="{{ limitDiscountListWithOrder }}" wx:for-item="item" wx:key="{{ item.id }}" title="{{ computed.getStatus(item.startTime, item.endTime) + ' ' +   computed.getTime(item.startTime) }}">
            <view class="goods-list">
                <view class="goods-list-item" wx:for="{{item.goodsList}}" wx:for-item="good" wx:key="{{ good.itemId }}">
                    <limit-discount-item item="{{ good }}" startTime="{{item.startTime}}" endTime="{{item.endTime}}" status="{{computed.getStatus(item.startTime, item.endTime)}}" is-buying="{{ isBuying }}" sku-visible="{{ skuVisible }}" sku-data="{{ skuData }}" />
                </view>
            </view>
        </van-tab>
    </van-tabs>
</view>