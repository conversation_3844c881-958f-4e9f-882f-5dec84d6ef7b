function getTime (time) {
    const date = getDate(time).getDate()
    const hour = getDate(time).getHours()
    const nowDate = getDate().getDate()
    if (nowDate === date) {
        const minute = getDate(time).getMinutes()
        // 当天的活动，显示小时+分钟
        return handlerTime(hour) + ':' + handlerTime(minute)
    }else {
        return date + '日' + handlerTime(hour) + '点'
    }
}

function handlerTime (time) {
  return time >= 10 ? time : '0' + time
}

function getStatus(startTime, endTime) {
  const nowDate = getDate().getTime()
  if (nowDate < startTime) {
    return '未开始'
  } else if ( nowDate > endTime) {
    return '已结束'
  } else {
    return '进行中'
  }
}
  
  module.exports = {
    getTime,
    getStatus
  };
  