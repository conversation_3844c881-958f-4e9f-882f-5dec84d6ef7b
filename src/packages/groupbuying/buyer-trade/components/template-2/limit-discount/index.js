import { mapGetters, mapState, mapMutations } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  options: {
    styleIsolation: 'shared'
  },
  mapData: {
    ...mapState(['limitDiscountList', 'skuVisible', 'skuData']),
    ...mapGetters(['limitDiscountListWithOrder','buyinglist', 'isBuying']),
  },
  data: {
    active: 0,
    isFixed: false,
  },
  methods: {
    ...mapMutations([
      'setLimitDiscountIndex'
    ]),
    onChange(e) {
      const index = e.detail.index
      this.setLimitDiscountIndex(index)
      this.setYZData({
        active: index
      })
    },
    onScroll(e) {
      const { isFixed } = e.detail;
      this.setYZData({
        isFixed,
      });
    },
  },
});
