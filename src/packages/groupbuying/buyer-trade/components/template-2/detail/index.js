import { mapState, mapGetters, mapMutations, mapActions } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import CountDown from '@youzan/weapp-utils/lib/countdown';
import { padStart } from '../../../../common';

VanxComponent({
  properties: {
    detailVisible: {
      type: Boolean,
      value: false,
      observer(val, prev) {
        if (val && val !== prev) {
          this.setCountTime();
        } else {
          this.countDown && this.countDown.stop && this.countDown.stop();
        }
        setTimeout(() => {
          if (
            val &&
            this.data.detailInfo.video &&
            this.data.detailInfo.video.videoId
          ) {
            this.setYZData(
              {
                videoState: 0,
              },
              () => {
                this.videoContext = wx.createVideoContext('myVideo', this);
              }
            );
          } else {
            this.videoContext = null;
          }
        }, 100);
      },
    },
  },

  mapData: {
    ...mapState([
      'detailInfo',
      'headerDetail',
      'actDetail',
      'limitDiscountDetail',
      'limitDiscountList',
      'limitDiscountIndex',
      'goodsPictures'
    ]),
    ...mapGetters(['isBuying', 'currentPrice', 'getDetailInfo']),
  },

  data: {
    autoplayTime: 3000,
    showIndicators: false,
    time: {
      text: '距离结束仅剩',
      showTime: true,
      h: 0,
      m: 0,
      s: 0,
    },
    videoState: 0,
    countDown: null,
  },

  ready() {
    this.setCountTime();
  },

  methods: {
    ...mapMutations(['setDetailVisible', 'setShareVisible']),
    ...mapActions(['plus', 'minus', 'changeOrder']),

    setCountTime() {
      const { detailInfo, actDetail, limitDiscountDetail } = this.data;
      if (detailInfo && detailInfo.isLimitDiscount) {
        const { startTime, endTime, status } = limitDiscountDetail;
        const start = +Date.now();
        const delta = status === '未开始' ? startTime - start : endTime - start;
        if (delta > 0) {
          this.countDown = new CountDown(delta, {
            onChange: (timeData) => {
              const { day: d, hour: h, minute: m, second: s } = timeData;
              this.setYZData({
                'time.d': padStart(String(d), 2, '0'),
                'time.h': padStart(String(h), 2, '0'),
                'time.m': padStart(String(m), 2, '0'),
                'time.s': padStart(String(s), 2, '0'),
              });
            },
          });
        }
      } else if (actDetail) {
        const { startTime, endTime, state } = actDetail;
        const start = +Date.now();
        const delta = state === 0 ? startTime - start : endTime - start;
        if (delta > 0 && state !== 2) {
          this.countDown = new CountDown(delta, {
            onChange: (timeData) => {
              const { day, hour, minute: m, second: s } = timeData;
              const h = day * 24 + hour;
              this.setYZData({
                'time.h': padStart(String(h), 2, '0'),
                'time.m': padStart(String(m), 2, '0'),
                'time.s': padStart(String(s), 2, '0'),
              });
            },
            onEnd: () => {
              this.setEnd();
            },
          });
        } else {
          this.setEnd();
        }
      }
    },
    closePopup() {
      // 如果有视频，关闭视频
      const { videoState } = this.data;
      if (videoState === 1) {
        this.setYZData({
          videoState: 2,
        });
        this.pauseVideo();
        return;
      }
      this.setDetailVisible(false);
    },
    onPlus() {
      const { detailInfo } = this.data;
      const {skuPrices = []} = detailInfo
      const skuId = skuPrices ? skuPrices[0]?.skuId : null;
      this.plus({ goodsId: detailInfo.id, umpActivityId:  detailInfo.umpActivityId, skuId});
    },
    onMinus() {
      const { detailInfo } = this.data;
      const {skuPrices = []} = detailInfo
      const skuId = skuPrices ? skuPrices[0]?.skuId : null;
      this.minus({ goodsId: detailInfo.id, umpActivityId:  detailInfo.umpActivityId, skuId});
    },
    onChange(detail, num) {
      const { id, skuId } = detail;
      const { detailInfo } = this.data;
      this.changeOrder({
        goodsId: id,
        skuId,
        num,
        umpActivityId: detailInfo.umpActivityId
      });
    },
    share() {
      this.setShareVisible(true);
    },
    setEnd() {
      this.setYZData({
        'time.showTime': false,
      });
    },
    onSwiperChange(e) {
      const next = e.detail.current;
      if (next !== 0 && this.videoContext) {
        this.pauseVideo();
      }
    },
    playVideo() {
      this.setYZData(
        {
          videoState: 1,
        },
        () => {
          this.videoContext.play();
        }
      );
    },
    pauseVideo() {
      this.videoContext.pause();
    },
    onVideoPlay() {
      this.setYZData({
        videoState: 1,
      });
    },
    onVideoEnded() {
      this.setYZData({
        videoState: 2,
      });
    },
  },
});
