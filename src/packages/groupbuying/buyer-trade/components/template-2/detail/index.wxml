<wxs module="computed" src="./computed.wxs" />
<van-popup
  z-index="9991"
  show="{{ detailVisible }}"
  position="bottom"
  bind:click-overlay="closePopup"
  custom-class="goods-detail-popup"
  custom-style="height: 90%"
>
  <view class="goods-detail">
    <view class="preview">
      <swiper
        wx:if="{{ detailVisible }}"
        class="van-swipe-item"
        autoplay="{{ computed.autoplay(videoState) }}"
        interval="{{ autoplayTime }}"
        show-indicators="{{ showIndicators }}"
        bindchange="onSwiperChange"
        circular
      >
        <swiper-item
          wx:for="{{ goodsPictures }}"
          wx:for-index="idx"
          wx:for-item="picItem"
          wx:key="idx"
        >
          <view
            class="video-container"
            wx:if="{{ idx === 0 && computed.hasVideo(detailInfo) }}"
          >
            <view
              hidden="{{ videoState === 1 }}"
              class="video-cover"
            >
              <image
                src="{{ detailInfo.video.coverUrl }}"
              />
              <view
                class="play-btn"
                catchtap="playVideo"
              />
            </view>
          </view>
          <image wx:else src="{{ picItem.url }}" />
        </swiper-item>
      </swiper>

      <video
        id="myVideo"
        wx:if="{{ computed.hasVideo(detailInfo) }}"
        hidden="{{ videoState !== 1 }}"
        controls
        src="{{ detailInfo.video.videoUrl }}"
        bindplay="onVideoPlay"
        bindended="onVideoEnded"
      />
    </view>
    <view class="sell-info">
      <view wx:if="{{detailInfo.isLimitDiscount}}" class="limitDiscount-title">
        {{limitDiscountList[limitDiscountIndex].description || '限时折扣'}}
      </view>
      <view class="sell-info-sales">
        <view class="sell-info-sales__price">
          <price
            price="{{ currentPrice }}"
            originPrice="{{ detailInfo.isLimitDiscount ? detailInfo.skuPrices[0].price / 100 : detailInfo.origin }}"
            priceStyle="color: #fff;font-size: 16px"
            bigPriceStyle="font-size: 28px"
            originPriceStyle="color: #fff;font-size: 14px"
          />
        </view>
        <view class="sell-info-sales__num">
          <text hidden="{{ detailInfo.isDisplaySales !== 1 }}" class="sales-num">已抢{{ detailInfo.salesNum }}件</text>
          <text hidden="{{ detailInfo.isDisplaySales !== 1 || detailInfo.isDisplayStock !== 1 }}"> | </text>
          <text hidden="{{ detailInfo.isDisplayStock !== 1 }}" class="stock-num">剩{{ detailInfo.stockNum }}件</text>
        </view>
      </view>
      <view class="sell-info-line" />
      <view class="sell-info-time" wx:if="{{ time.showTime }}">
        <view wx:if="{{detailInfo.isLimitDiscount}}" class="title">距离{{ limitDiscountDetail.status === '未开始' ? '开始' : '结束'}}仅剩</view>
        <view wx:else class="title">距离{{ actDetail.state === 0 ? '开始' : '结束' }}仅剩</view>
        <view class="time">
          <view wx:if="{{detailInfo.isLimitDiscount}}" class="time-box">{{ time.d }}</view>
          <view wx:if="{{detailInfo.isLimitDiscount}}" class="time-comma">天</view>
          <view class="time-box">{{ time.h }}</view>
          <view class="time-comma">:</view>
          <view class="time-box">{{ time.m }}</view>
          <view class="time-comma">:</view>
          <view class="time-box">{{ time.s }}</view>
        </view>
      </view>
      <view class="sell-info-time sell-info-time--end" wx:else>活动已结束</view>
    </view>
    <view class="base-info">
      <view class="base-info-content">
        <view class="base-info-content__title">
          {{ detailInfo.title }}
        </view>
        <view wx:if="{{ time.showTime }}" class="base-info-content__share" catchtap="share">
          <van-icon name="share" />
          <view class="base-info-content__share-text">分享</view>
        </view>
      </view>
      <view
        wx:if="{{ detailInfo.sellPoint }}"
        class="base-info-sell-point"
      >
        {{ detailInfo.sellPoint }}
      </view>
      <view class="base-info-opt">
        <view class="base-info-opt__group" wx:if="{{ detailInfo.customerCount > 3 }}">
          <image
            wx:for="{{ detailInfo.customerAvatar }}"
            wx:for-item="customer"
            wx:key="{{ customer.userId }}"
            src="{{ customer.avatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}"
          />
          <text>...等{{ detailInfo.customerCount }}人已团</text>
        </view>
        <view class="base-info-opt__group" wx:elif="{{ detailInfo.customerCount > 0 }}">
          <image
            wx:for="{{ detailInfo.customerAvatar }}"
            wx:for-item="customer"
            wx:key="{{ customer.userId }}"
            src="{{ customer.avatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}"
          />
          <text>{{ detailInfo.customerCount }}人已团</text>
        </view>
        <view class="base-info-opt__group" wx:else />
        <stepper
          wx:if="{{ isBuying && detailInfo.stockNum > 0 && (detailInfo.isLimitDiscount ? limitDiscountDetail.status !== '未开始' : true)  }}"
          bind:plus="onPlus"
          bind:minus="onMinus"
          bind:change="onChange"
          detail="{{ detailInfo }}"
        />
      </view>
    </view>
    <view class="line" />
    <view class="header">
      <image class="header-avatar" src="{{ headerDetail.headerAvatar || 'https://img.yzcdn.cn/upload_files/avatar.png' }}" />
      <view class="header-content">
        <view class="header-content-name">
          <text>{{ headerDetail.headerName }}</text>
          <image src="https://img.yzcdn.cn/public_files/2019/04/03/552c2c5303c8ac89c992fa5cfcfb90f1.png" />
        </view>
        <view class="header-content-address">
          <text class="label">提货地址：</text>
          <text class="detail">{{ headerDetail.fetchAddress }}</text>
        </view>
      </view>
    </view>
    <view class="line" />
    <view
      class="goods-content"
    >
      <view class="title">— 商品详情 —</view>
      <view class="wrap">
        <cap-rich-text wx:if="{{ detailInfo.content }}" html="{{ detailInfo.content }}" />
        <view wx:else class="content">暂无详情</view>
      </view>
    </view>
  </view>
</van-popup>

<!-- 关闭按钮 -->
<view
  hidden="{{ !detailVisible }}"
  class="close-btn"
>
  <view
    class="tap-area"
    catchtap="closePopup"
  ></view>
</view>
