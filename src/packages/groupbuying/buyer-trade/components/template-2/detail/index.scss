.goods-detail {
  height: 100vh;

  .preview {
    position: relative;
    height: 100vw;
    background: #f8f8f8;

    .van-swipe-item {
      height: 100vw;
      line-height: 100vw;
      text-align: center;
      overflow: hidden;

      image {
        display: inline;
        max-width: 100vw;
        max-height: 100vw;
        vertical-align: middle;
      }

      .video-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vw;
        width: 100vw;

        .video-cover {
          height: 100%;
          width: 100%;

          .play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            height: 55px;
            width: 55px;
            background-size: 100%;
            background-image: url(https://b.yzcdn.cn/fix-base64/72f37f85b032522de97fc4caa608f478d9f4dc7d38f07c0f31b2b22901c4605e.png);
          }
        }
      }
    }

    video {
      position: absolute;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vw;
    }
  }

  .sell-info {
    display: flex;
    align-items: center;
    padding-left: 16px;
    height: 72px;
    background: #07c160;
    background-image: url('https://img.yzcdn.cn/public_files/2019/04/08/444f1e65d6b4ffb98de484be8eb74ce0.png');
    background-position: top center;
    position: relative;

    .limitDiscount-title {
      width: 80px;
      height: 26px;
      background: #07c160;
      border-top-right-radius: 7px;
      font-size: 12px;
      font-weight: normal;
      color: #ffffff;

      position: absolute;
      left:0;
      top: -26px;
      line-height: 26px;
      text-align: center;
    }

    &-sales {
      display: flex;
      flex-direction: column;
      margin-top: 3px;

      &__price {
        height: 24px;
        line-height: 24px;
      }

      &__num {
        margin-top: 6px;
        color: #fff;
        line-height: 18px;
        font-size: 12px;
      }
    }

    &-line {
      flex: 1;
      height: 40px;
      border-right: 1px solid rgba(255,255,255,0.8);
    }

    &-time {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 16px;

      &--end {
        color: #fff;
        font-size: 14px;
      }

      .title {
        font-size: 14px;
        color: #f8ff26;
        line-height: 20px;
      }

      .time {
        display: flex;
        margin-top: 8px;

        .time-box {
          min-width: 18px;
          padding: 3px 5px;
          background: #f8ff26;
          color: #724e06;
          font-size: 12px;
          text-align: center;
          line-height: 20px;
          border-radius: 4px;
        }

        .time-comma {
          margin: 0 2px;
          font-size: 12px;
          color: #724E06;
          line-height: 26px;
        }
        .time-comma:first-of-type {
          margin-right: 4px;
        }
      }
    }
  }

  .base-info {
    display: flex;
    flex-direction: column;
    padding: 12px 16px;

    &-content {
      display: flex;
      flex: 1;

      &__title {
        flex: 1;
        height: 44px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 22px;
        font-size: 16px;
        color: #4a4a4a;
      }

      &__share {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 40px;

        &-text {
          margin-top: 2px;
          line-height: 16px;
          font-size: 12px;
          color: #323233;
        }
      }
    }

    &-sell-point {
      max-height: 36px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      font-family: PingFangSC-Regular;
      font-size: 13px;
      color: #969799;
      line-height: 18px;
    }

    &-opt {
      display: flex;
      align-items: center;
      margin-top: 8px;
      padding-right: 8px;

      &__group {
        display: flex;
        align-items: center;
        flex: 1;
        line-height: 16px;

        image {
          margin-left: -4px;
          height: 16px;
          width: 16px;
          border-radius: 50%;

          &:first-child {
            margin-left: 0;
          }
        }

        text {
          margin-left: 8px;
          font-size: 12px;
          color: #4a4a4a;
        }
      }
    }
  }

  .header {
    display: flex;
    padding: 16px 12px;
    align-items: center;

    &-avatar {
      height: 44px;
      width: 44px;
      border-radius: 50%;
    }

    &-content {
      display: flex;
      flex-direction: column;
      flex: 1;
      justify-content: center;
      padding-left: 8px;

      &-name {
        display: flex;
        align-items: center;

        text {
          font-size: 16px;
          line-height: 22px;
          color: #333;
        }

        image {
          margin-left: 8px;
          width: 32px;
          height: 16px;
        }
      }

      &-address {
        margin-top: 4px;
        line-height: 18px;
        font-size: 12px;

        .label {
          color: #969799;
        }

        .detail {
          color: #333;
        }
      }
    }
  }

  .line {
    height: 10px;
    background: #f8f8f8;
  }

  .goods-content {
    .title {
      height: 46px;
      line-height: 46px;
      text-align: center;
      font-size: 14px;
      border-bottom: 1px solid #f2f2f2;
      color: #111;
    }

    .wrap {
      padding: 10px;
    }

    .content {
      font-size: 16px;
    }
  }

  .buying-item-none {
    position: absolute;
    right: 15px;
    color: #333;
    font-size: 14px;
    line-height: 24px;
  }
}

.close-btn {
  position: fixed;
  margin-top: 20px;
  top: 10%;
  right: 20px;
  height: 28px;
  width: 28px;
  background-image: url('https://img.yzcdn.cn/public_files/2018/12/17/330e2cd3890b6ee46a97fd1f7b2896fd.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center center;
  z-index: 99999;

  .tap-area {
    position: absolute;
    height: 42px;
    width: 42px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
