import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { padEnd } from '../../../../common';

VanxComponent({
  externalClasses: ['custom-class'],

  properties: {
    price: {
      type: Number,
      observer(nextVisible, prevVisible) {
        if (prevVisible !== nextVisible) {
          this.getPirceValue(nextVisible);
        }
      }
    },
    originPrice: {
      type: String,
      observer(next, prev) {
        if (next !== prev) {
          this.getOriginPrice(next)
        }
      }
    },
    bigPriceStyle: {
      type: String
    },
    priceStyle: {
      type: String
    },
    originPriceStyle: {
      type: String
    }
  },
  data: {
    priceShow: []
  },
  ready() {
    const { price, originPrice } = this.data;
    this.getPirceValue(price);
    if (originPrice) {
      this.getOriginPrice(originPrice);
    }
  },
  methods: {
    getPirceValue(price) {
      let priceShow;
      priceShow = price.toString().indexOf('.') === -1 ? (price + '.00') : price.toString();
      priceShow = priceShow.split('.');
      priceShow[1] = padEnd(priceShow[1], 2, '0');
      this.setYZData({
        priceShow
      });
    },

    getOriginPrice(originPrice) {
      if (!originPrice) {
        this.setYZData({
          originPrice: null
        })
        return 
      }
      let priceShow;
      priceShow = originPrice.toString().indexOf('.') === -1 ? (originPrice + '.00') : originPrice.toString();
      this.setYZData({
        originPrice: priceShow
      });
    }
  }
});
