module.exports = {
  stateText(nextAct) {
    let state = 0;
    if (nextAct) {
      state = nextAct.state;
    }
    return state === 0 ? '即将开团' : '正在团购';
  },

  totalItems(nextAct) {
    if (nextAct && nextAct.items) {
      return nextAct.items.length;
    }
    return 0;
  },

  items(nextAct) {
    if (nextAct && nextAct.items) {
      return nextAct.items.slice(0, 3);
    }
    return [];
  }
};
