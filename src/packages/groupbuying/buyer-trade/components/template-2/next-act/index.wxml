<wxs module="computed" src="./computed.wxs" />
<van-popup
  costom-class="next-act-popup"
  show="{{ nextActVisible }}"
  position="bottom"
  close-on-click-overlay="{{ false }}"
  bind:click-overlay="onClose"
>
  <view class="next-act">
    <view class="title">当前团购已结束</view>
    <view class="tip">- 为你推荐 -</view>
    <view class="desc">以下 {{ computed.totalItems(nextAct) }} 个商品{{ computed.stateText(nextAct) }}</view>
    <view class="preview">
      <view
        class="item"
        wx:for="{{ computed.items(nextAct) }}"
        wx:key="{{ item.itemId }}"
      >
        <image src="{{ item.picture[0].url }}" />
      </view>
    </view>
    <van-button
      custom-class="btn"
      type="primary"
      size="large"
      round
      bind:click="onEnterNextAct"
    >
      去看看
    </van-button>
    <image
      class="close-btn"
      src="https://img.yzcdn.cn/public_files/2019/06/19/3bea424db670cd14914c910a3eaf664e.png"
      catchtap="onClose"
    />
  </view>
</van-popup>
