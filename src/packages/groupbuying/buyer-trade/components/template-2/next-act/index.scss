.next-act {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 28px 16px 7px;
  color: #323233;
  text-align: center;

  &-popup {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }

  .title {
    font-size: 18px;
    line-height: 24px;
    font-weight: bold;
  }

  .tip {
    margin-top: 16px;
    font-size: 14px;
    color: #969799;
    line-height: 18px;
  }

  .desc {
    margin-top: 16px;
    font-size: 14px;
    line-height: 18x;
  }

  .preview {
    margin-top: 16px;
    display: flex;
    line-height: 0;

    .item {
      margin-left: 16px;

      &:first-child {
        margin-left: 0;
      }

      image {
        width: calc((100vw - 64px) / 3);
        height: calc((100vw - 64px) / 3);
        border-radius: 4px;
      }
    }
  }

  .btn {
    margin-top: 15px;
    height: 36px;
    line-height: 34px;
  }

  .close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    height: 22px;
    width: 22px;
  }
}
