import { mapState, mapMutations, mapActions } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState(['nextAct', 'nextActVisible'])
  },

  methods: {
    ...mapMutations([
      'setNextActVisible'
    ]),
    ...mapActions([
      'enterNextAct'
    ]),
    onClose() {
      this.setNextActVisible(false);
    },
    onEnterNextAct() {
      this.enterNextAct();
    }
  },
});
