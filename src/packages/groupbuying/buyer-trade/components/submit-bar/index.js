import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    price: {
      type: Number,
      value: -1,
      observer: '_observerPrice'
    },

    buttonText: {
      type: String,
      value: '去支付'
    }
  },

  data: {
    _price: 0
  },

  methods: {
    _observerPrice(newVal) {
      this.setYZData({
        _price: newVal
      });
    },

    submit() {
      this.triggerEvent('submit');
    }
  }
});
