.submit-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  width: 100%;
  background: #fff;

  &__tip,
  &__btn {
    height: 100rpx;
    line-height: 100rpx;
  }

  &__tip {
    flex: 1;
    padding-right: 10px;
    font-size: 16px;
    color: #666;
    text-align: right;
    position: relative;

    text {
      color: #f44;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 200%;
      height: 200%;
      transform: scale(.5);
      transform-origin: 0 0;
      pointer-events: none;
      box-sizing: border-box;
      border-top: 1px solid #e5e5e5;
    }
  }

  &__btn {
    width: 220rpx;
    text-align: center;
    background: #4b0;
    color: #fff;
    font-size: 14px;
  }

  @media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
    & {
      padding-bottom: 34px;
    }
  }
}
