const VERY_LARGE_NUMBER = 2147483647;

Component({
  properties: {
    size: {
      type: String,
      value: 'middle'
    },
    stepper: {
      type: Number,
      value: 1
    },
    min: {
      type: Number,
      value: 1
    },
    max: {
      type: Number,
      value: VERY_LARGE_NUMBER
    },
    step: {
      type: Number,
      value: 1
    }
  },

  methods: {
    handleZanStepperChange: function handleZanStepperChange(e, type) {
      const _e$currentTarget$data = e.currentTarget.dataset;
      const dataset = _e$currentTarget$data === undefined ? {} : _e$currentTarget$data;
      const disabled = dataset.disabled;
      const step = this.data.step;
      let stepper = this.data.stepper;


      if (disabled) return null;

      if (type === 'minus') {
        stepper -= step;
      } else if (type === 'plus') {
        stepper += step;
      }

      if (stepper < this.data.min || stepper > this.data.max) return null;

      this.triggerEvent('change', stepper);
      this.triggerEvent(type);
    },
    handleZanStepperMinus: function handleZanStepperMinus(e) {
      this.handleZanStepperChange(e, 'minus');
    },
    handleZanStepperPlus: function handleZanStepperPlus(e) {
      this.handleZanStepperChange(e, 'plus');
    },
    handleZanStepperBlur: function handleZanStepperBlur(e) {
      let value = e.detail.value;
      const _data = this.data;
      const min = _data.min;
      const max = _data.max;


      if (!value) {
        setTimeout(() => {
          this.triggerEvent('change', min);
        }, 16);
        return;
      }

      value = +value;
      if (value > max) {
        value = max;
      } else if (value < min) {
        value = min;
      }

      this.triggerEvent('change', value);
    }
  }
});
