<view class="vm-stepper vm-stepper--{{ size }}">
  <view
    class="vm-stepper__minus {{ stepper <= min ? 'vm-stepper__minus--disabled' : '' }}"
    data-disabled="{{ stepper <= min }}"
    bindtap="handleZanStepperMinus"
  >
  </view>
  <input
    class="vm-stepper__text"
    type="number"
    value="{{ stepper }}"
    disabled
    bindblur="handleZanStepperBlur"
  />
  <view
    class="vm-stepper__plus {{ stepper >= max ? 'vm-stepper__plus--disabled' : '' }}"
    data-disabled="{{ stepper >= max }}"
    bindtap="handleZanStepperPlus"
  >
  </view>
</view>
