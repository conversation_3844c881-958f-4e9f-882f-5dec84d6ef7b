.vm-stepper {
  color: #666
}

.vm-stepper view {
  display: inline-block;
  padding: 5px 0;
  text-align: center;
  box-sizing: border-box;
  vertical-align: middle;
  font-size: 12px;
}

.vm-stepper .vm-stepper__minus, .vm-stepper .vm-stepper__plus {
  width: 24px;
  height: 24px;
  padding: 0;
  border: none;
  outline: none;
}

.vm-stepper .vm-stepper__minus {
  background: url("https://b.yzcdn.cn/fix-base64/db9e3fbd0bbc02e79b682450ad9ad926aa81a2cd4f9a102b1d6b6d7d934096ca.png") no-repeat center center;
  background-size: 100% 100%;
}

.vm-stepper__minus--disabled {
  background: url("https://b.yzcdn.cn/fix-base64/3ea79204563e2e750d2edba4080f4b30ec3d2dc0dac55781492e70944dd689b3.png") no-repeat center center !important;
  background-size: 100% 100% !important;
}

.vm-stepper__plus--disabled {
  background: url("https://b.yzcdn.cn/fix-base64/b73a7a1c466431418e16f45d706dbc3117a9d381ccac740966184b17f101d7e0.png") no-repeat center center !important;
  background-size: 100% 100% !important;
}

.vm-stepper .vm-stepper__text {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  min-height: auto;
  font-size: 12px
}

.vm-stepper .vm-stepper__plus {
  background: url("https://b.yzcdn.cn/fix-base64/ef6c0a51218282ed231a93b09fb2bde28378f45ae43ef05e0189f838a1b02a88.png") no-repeat center center;
  background-size: 100% 100%;
}

.vm-stepper .vm-stepper--disabled {
  background: #f8f8f8;
  color: #bbb;
  border-color: #e8e8e8
}

.vm-stepper--small .vm-stepper__text {
  font-size: 16px;
  color: #333;
  width: 33px;
  text-align: center;
  border: none;
  padding: 0;
}

.vm-stepper--middle .vm-stepper__text {
  font-size: 16px;
  color: #333;
  width: 33px;
  text-align: center;
  border: none;
  padding: 0;
}

.vm-stepper--large .vm-stepper__text {
  font-size: 16px;
  color: #333;
  width: 33px;
  text-align: center;
  border: none;
  padding: 0;
}
