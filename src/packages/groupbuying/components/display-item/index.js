import WscComponent from 'pages/common/wsc-component/index';
import { fetchSafeInfo } from '../../common';

const app = getApp();

WscComponent({
  properties: {
    detail: Object,
    showRefund: {
      value: false,
      type: Boolean
    },
    orderNo: String
  },

  methods: {
    refund() {
      const { detail, orderNo } = this.data;
      const { tradeItemId, feedback } = detail;
      fetchSafeInfo({
        orderNo
      }).then((res = {}) => {
        const refundInfo = res[tradeItemId] || {};
        const dbid = app.db.set({
          item_id: tradeItemId,
          safe_no: refundInfo.refundId || '',
          order_no: orderNo
        });
        let url;
        if (feedback === 0 || feedback === 249) {
          url = '/packages/trade/order/safe/apply/index';
        } else {
          url = '/packages/trade/order/safe/info/index';
        }
        wx.navigateTo({
          url: `${url}?dbid=${dbid}`
        });
      });
    }
  }
});
