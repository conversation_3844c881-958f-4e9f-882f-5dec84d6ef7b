<view class="display-item">
  <image class="display-item__image" src="{{ detail.picture[0].url }}" />
  <view class="display-item__content">
    <view class="display-item__left">
      <view class="display-item__name">{{ detail.title }}</view>
      <view class="display-item__sku">{{ detail.skuName }}</view>
      <view class="display-item__refund" wx:if="{{ detail.refundYuan > 0 }}">已退款 ¥ {{ detail.refundYuan }}</view>
    </view>
    <view class="display-item__right">
      <view class="display-item__price">¥ {{ detail.priceYuan }}</view>
      <view class="display-item__num">X {{ detail.num }}</view>
      <!-- feedback不为0和249的，都是退款中状态 -->
      <van-button
        wx:if="{{ showRefund && !detail.refund }}"
        custom-class="refund-btn"
        type="default"
        size="small"
        round
        catchtap="refund"
      >
        {{ detail.feedback === 0 || detail.feedback === 249 ? '退款' : '退款中' }}
      </van-button>
    </view>
  </view>
</view>