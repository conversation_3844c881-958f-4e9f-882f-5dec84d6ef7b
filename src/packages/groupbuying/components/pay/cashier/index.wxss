.cashier-wx-pay {
  color: #44ba00;
}

.cashier-actionsheet__btn.cashier-actionsheet__btn--disabled .cashier-wx-pay {
  color: inherit;
}

.zan-actionsheet {
  background-color: #f8f8f8
}

.zan-actionsheet__mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: rgba(0, 0, 0, .7);
  display: none
}

.zan-actionsheet__container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f8f8f8;
  transform: translate3d(0, 50%, 0);
  transform-origin: center;
  transition: all .2s ease;
  z-index: 11;
  opacity: 0;
  visibility: hidden
}

.zan-actionsheet__btn.zan-btn {
  height: 50px;
  line-height: 50px;
  margin-bottom: 0
}

.zan-actionsheet__btn.zan-btn::after {
  border-width: 0;
  border-bottom-width: 1px
}

.zan-actionsheet__btn.zan-btn:last-child::after {
  border-bottom-width: 0
}

.zan-actionsheet__remain {
  margin-left: 10px;
  font-size: 12px;
}

.zan-actionsheet__footer {
  margin-top: 10px
}

.zan-actionsheet__btn.zan-btn--loading .zan-actionsheet__remain {
  color: transparent
}

.zan-actionsheet--show .zan-actionsheet__container {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  visibility: visible
}

.zan-actionsheet--show .zan-actionsheet__mask {
  display: block
}

.zan-cashier__password__mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 12;
  background: rgba(0, 0, 0, .7);
  display: none
}

.zan-cashier__password--show .zan-cashier__password__mask {
  display: block
}

.zan-cashier__password {
  position: fixed;
  background: #f8f8f8;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  transform-origin: center;
  transition: all .2s ease;
  z-index: 13;
  opacity: 0;
  visibility: hidden;
  box-sizing: border-box;
  min-width: 280px;
  padding: 20px 15px;
  background-color: #FFF;
}

.zan-cashier__password--show .zan-cashier__password {
  opacity: 1;
  visibility: visible
}

.zan-cashier__password .zan-cashier__password__header {
  font-size: 16px;
  line-height: 22px;
}

.zan-cashier__password .zan-cashier__password__header .zan-icon {
  float: right;
}

.zan-cashier__password .zan-cashier__password__input-container {
  padding: 15px 0 10px;
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

.zan-cashier__password .zan-cashier__password__input-container .zan-cell:last-child::after {
  display: block;
}

.zan-cashier__password .zan-cashier__password__input-container .zan-field {
  margin: 10px 0 0;
}

.zan-cashier__password .zan-cashier__password__action-container .zan-btn {
  border-radius: 4px;
}
