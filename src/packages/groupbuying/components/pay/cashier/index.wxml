<template name="$cashier">
  <view class="zan-cashier zan-actionsheet {{ $cashier.show ? 'zan-actionsheet--show' : '' }}">
    <view
      class="zan-actionsheet__mask"
      catchtap="$cashierMaskClick"
      data-close-on-click-overlay="{{ $cashier.closeOnClickOverlay || true }}"
      data-component-id="{{ $cashier.componentId }}"></view>
    <view class="zan-actionsheet__container">
      <!-- 实际按钮显示 disable样式不好看先去掉，在js里处理disable的情况 -->
      <button
        wx:for="{{ $cashier.actions }}"
        wx:for-index="index"
        wx:for-item="item"
        wx:key="{{ index }}-{{ item.pay_channel_name }}"
        catchtap="$cashierBtnClick"
        data-component-id="{{ $cashier.componentId }}"
        data-index="{{ index }}"
        data-pay-channel="{{ item.pay_channel }}"
        data-pay-channel-name="{{ item.pay_channel_name }}"
        data-value-card-no="{{ item.value_card_no }}"
        disabled="{{ !!!item.available }}"
        data-enable="{{ item.available }}"
        open-type="{{ item.openType }}"
        class="cashier-actionsheet__btn zan-btn zan-actionsheet__btn {{ item.loading ? 'zan-btn--loading' : '' }} {{ item.className }} {{ !item.available ? 'cashier-actionsheet__btn--disabled' : '' }}"
      >
        <!-- 微信支付 其他支付方式（ 储值卡 / 礼品卡 / E卡等 ） -->
        <block>
          <text class="{{ item.pay_channel === 'WX_APPLET' ? 'cashier-wx-pay' : '' }}">{{ item.pay_channel_name }}</text>
          <text
            wx:if="{{ item.available_desc }}"
            class="zan-actionsheet__remain">( {{ item.available_desc }} )</text>
        </block>
      </button>
    </view>
    <!-- E卡密码框 -->
    <view class="{{ $cashier.showPassword ? 'zan-cashier__password--show' : '' }}">
      <view
        class="zan-cashier__password__mask"
      />
      <view
        wx:if="{{ $cashier.showPassword }}"
        class="zan-cashier__password"
      >
        <view class="zan-cashier__password__header">
          安全验证
          <view class="zan-icon zan-icon-close van-c-gray van-font-22" catchtap="$cashierClosePassword" />
        </view>
        <view class="zan-cashier__password__input-container">
          <view>为了保障你的账户安全，请输入手机账号{{ secureAccount }}的登录密码</view>
          <view
            class="zan-cell zan-field zan-field--wrapped">
            <input
              class="zan-field__input zan-cashier__password__input-container__input"
              type="password" 
              placeholder="请输入登录密码"
              bindinput="onPasswordInputBlur"
              value="{{ $cashier.password }}"
            />
          </view>
        </view>
        <view class="zan-cashier__password__action-container">
          <button
            class="zan-btn zan-btn--primary"
            catchtap="$cashierECardPasswordPay"
          >
            付款
          </button>
        </view>
      </view>
    </view>
  </view>
</template>