/* 收银台使用说明
 * 需要在 data 上挂载对象 $cashier: { actions, componentId, show, closeOnClickOverlay }
 * actions 格式 [{
 *   name,
 *   enable,
 *   isWxPay,
 *   remain
 * }]
 * [ 暴露方法 ]
 * $cashierClick(payChanenl, payData) 选择支付方式回调函数
 * $cashierCancel({ componentId }) 关闭收银台弹层
 */
const methods = {
  $cashierMaskClick({ currentTarget = {} }) {
    const dataset = currentTarget.dataset || {};
    const { componentId, closeOnClickOverlay } = dataset;

    // 判断是否在点击背景时需要关闭弹层
    if (!closeOnClickOverlay) {
      return;
    }

    this.resolveCancelClick.call(this, { componentId });
  },

  $cashierCancelBtnClick(e) {
    const {
      dataset: { componentId },
    } = e.currentTarget || {};
    this.resolveCancelClick.call(this, { componentId });
  },

  $cashierBtnClick({ currentTarget = {} }) {
    const dataset = currentTarget.dataset || {};
    const { enable, payChannel, payChannelName, valueCardNo } = dataset;
    if (!enable) {
      return;
    }
    if (typeof this.$cashierClick === 'function') {
      this.$cashierClick(
        { pay_channel: payChannel, pay_channel_name: payChannelName },
        { value_card_no: valueCardNo }
      );
    } else {
      console.warn('页面缺少 $cashierClick({ componentId, index }) 回调函数');
    }
  },

  resolveCancelClick({ componentId }) {
    console.info('[cashier:actionsheet:cancel]');
    if (typeof this.$cashierCancel === 'function') {
      this.$cashierCancel({ componentId });
    } else {
      console.warn('页面缺少 $cashierCancel({ componentId }) 回调函数');
    }
  },

  /**
   * 关闭密码框
   */
  $cashierClosePassword() {
    this.setYZData({ '$cashier.showPassword': false });
  },

  async $cashierECardPasswordPay() {
    const crypto = await import('@youzan/crypto');
    const password = crypto.aes.legacyEncrypt(this.data.$cashier.password);
    if (typeof this.$cashierClick === 'function') {
      this.$cashierClick({ pay_channel: 'ECARD' }, { password });
    } else {
      console.warn('页面缺少 $cashierClick({ }) 回调函数');
    }
  },

  onPasswordInputBlur(e) {
    const password = e.detail.value;
    this.setYZData({ '$cashier.password': password });
  },
};

export default methods;
