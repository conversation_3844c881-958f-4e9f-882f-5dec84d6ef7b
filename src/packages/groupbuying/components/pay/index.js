import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import payManager from './components/pay-manager';

const app = getApp();

const DEFAULT_PAY_WX = {
  available_status: 'AVALIABLE',
  pay_channel_name: '微信支付',
  balance: '',
  should_wrap: false,
  value_card_no: '',
  available: true,
  pay_channel: 'WX_APPLET',
  available_desc: '',
  channel_type: 'THIRD_PAY'
};

export default class Pay {
  constructor({ cashierData, acquireNo } = {}) {
    this.payData = {};
    this.isCashierCreating = false;
    this.isOrderCreated = false;
    this.isPayProcessing = false;
    this.isFetchedPayWays = false;
    this.payWays = [];
    this.isPrepay = true; // 预下单模式

    // 初始进来的都直接初始化一遍 cashierData
    this.setCashierData(cashierData, acquireNo);
  }

  /**
   * 支付收单接口处理
   * @param cashierData
   * @return promise
   */
  createCashierOrder(cashierData = {}) {
    return new Promise((resolve, reject) => {
      const formatedCashierData = mapKeysCase.toSnakeCase(cashierData) || {};
      if (this.isPrepay) {
        this.payData.cashierData = formatedCashierData;
        this.isOrderCreated = true;
        this.isFetchedPayWays = false;
        resolve({});
        return;
      }

      if (this.isCashierCreating) {
        reject({ msg: '收单已在创建中...' });
        return;
      }
      this.isCashierCreating = true;

      // 展示 loading
      wx.showToast({
        title: '支付提交中',
        icon: 'loading',
        // 持续时间最长为10s
        duration: 10000
      });

      this.payData.cashierData = Object.assign({}, formatedCashierData, {
        // 无法直接转下划线的数据
        currency_code: formatedCashierData.currency_codel
      });

      app.carmen({
        api: 'youzan.pay.unified/1.0.0/create',
        data: formatedCashierData,
        method: 'POST',
        success: (res) => {
          let acquireNo = res.acquire_no || '';
          if (!acquireNo) {
            reject({ msg: '收单失败' });
            return;
          }

          // 内部记录，方便外部调用支付接口
          this.payData.acquireNo = acquireNo;
          this.isOrderCreated = true;
          resolve(res);
        },
        fail: res => {
          reject(res);
        },
        complete: () => {
          this.isCashierCreating = false;
          wx.hideToast();
        }
      });
    });
  }

  /**
   * 异步获取支付方式列表
   * @param cashierData
   * @return promise
   */
  getPayWays(data) {
    return new Promise((resolve, reject) => {
      if (this.isFetchedPayWays) {
        resolve(this.payWays);
      } else if (this.isPrepay) {
        app
          .request({
            path: 'wsctrade/order/buy/payChannels.json',
            data,
            method: 'POST'
          })
          .then((res) => {
            this.isFetchedPayWays = true;
            const payChannels = res.pay_channels || [];
            if (payChannels && payChannels.length === 0) {
              // this.payWays = [DEFAULT_PAY_WX];
              this.payWays = [];
              resolve(this.payWays);
            } else {
              this.payWays = payChannels;
            }
            resolve(this.payWays);
          })
          .catch((err) => {
            console.error(err);
            this.isFetchedPayWays = false;
            reject(err);
          });
      } else {
        this.payWays = [DEFAULT_PAY_WX];
        resolve(this.payWays);
      }
    });
  }

  /**
   * 异步获取支付方式列表
   * @param payChannelData 选择的支付方式名称
   * @param data { payWay 使用的支付方式 password 支付需要的密码，选传 }
   * @return promise
   */
  doPayAction(payChannelData = { pay_channel: 'WX_APPLET' }, data = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isOrderCreated) return reject({ msg: '未完成收单无法支付' });
      if (this.isPayProcessing) return reject({ msg: '支付进行中，请稍候' });
      this.isPayProcessing = true;
      // 展示 loading
      wx.showToast({
        title: '支付提交中',
        icon: 'loading',
        // 持续时间最长为10s
        duration: 10000
      });

      const app = getApp();
      const tokenData = app.getToken() || {};
      const cashierData = this.payData.cashierData || {};

      // 在小程序进行支付时，需要额外传入参数 appId（有赞小程序需要）
      let outBizCtx;
      try {
        const outBizCtxData = JSON.parse(cashierData.biz_ext);
        outBizCtxData.appId = app.getAppId();
        outBizCtx = JSON.stringify(outBizCtxData);
      } catch (e) {
        outBizCtx = cashierData.biz_ext;
      }

      const payCallback = () => {
        wx.hideToast();
        this.isPayProcessing = false;
      };

      if (cashierData.prepay_id) {
        // 预下单模式
        const formatedPayData = Object.assign(
          {},
          {
            partner_id: cashierData.partner_id,
            prepay_id: cashierData.prepay_id,
            cashier_salt: cashierData.cashier_salt,
            cashier_sign: cashierData.cashier_sign,
            pay_tool: payChannelData.pay_channel,
            wx_sub_open_id: tokenData.openId,
            pay_token: data.password,
            accept_price: data.accept_price || 0,
            new_price: data.new_price || -1,
            value_card_no: data.value_card_no,
            out_biz_ctx: outBizCtx
          },
          data
        );
        this._doPreorderPay(formatedPayData, resolve, reject, payCallback);
      } else {
        const formatedPayData = Object.assign(
          {},
          cashierData,
          {
            // 支付组和交易组约定不同步，导致无法直接用原来的数据格式的情况
            goods_desc: cashierData.trade_desc,
            out_biz_ctx: outBizCtx,
            // 额外数据
            wx_sub_open_id: tokenData.openId,
            acquire_no: this.payData.acquireNo,
            pay_tool: payChannelData.pay_channel
          },
          data
        );
        this._doPay(formatedPayData, resolve, reject, payCallback);
      }
    });
  }

  _doPay(formatedPayData, resolve, reject, callback) {
    app.carmen({
      api: 'youzan.pay.unified/1.0.0/multipay',
      data: formatedPayData,
      method: 'POST',
      success: ({ pay_detail_result: payDetailResult }) => {
        let deepLinkInfo = payDetailResult[0].deep_link_info || {};
        payDetailResult[0].deep_link_info = mapKeysCase.toCamelCase(deepLinkInfo);
        payManager
          .doPay(formatedPayData.pay_tool, payDetailResult[0])
          .then(resolve)
          .catch(reject);
      },
      fail: (res) => {
        reject({ msg: res.msg || '支付请求失败，请稍后再试' });
      },
      complete: () => {
        callback && callback();
      }
    });
  }

  /**
   * 预下单支付
   * @param {*} formatedPayData
   */
  _doPreorderPay(formatedPayData, resolve, reject, callback) {
    app
      .request({
        path: 'wsctrade/order/buy/preorderPay.json',
        data: formatedPayData,
        method: 'POST'
      })
      .then((payResult) => {
        //  订单改价
        if (payResult.operation === 'ADJUST_PRICE' && payResult.new_price) {
          wx.showModal({
            title: '改价提醒',
            content: `商家已将交易金额修改为${(payResult.new_price / 100).toFixed(2)}元，是否继续支付？`,
            confirmText: '确认',
            cancelText: '取消',
            success: (res) => {
              // 同意改价
              if (res.confirm) {
                this._doPreorderPay(Object.assign(formatedPayData, { accept_price: 1, new_price: payResult.new_price }), resolve, reject, callback);
              } else {
                reject({ msg: '不同意改价', type: 'adjust_price' });
              }
            }
          });
          return;
        }
        // 微信支付
        let deepLinkInfo = payResult.deep_link_info || '';
        if (deepLinkInfo) {
          try {
            payResult.deep_link_info = JSON.parse(deepLinkInfo);
          } catch (parseErr) {
            console.error(parseErr);
          }
        }
        payManager
          .doPay(formatedPayData.pay_tool, payResult)
          .then(() => {
            resolve(payResult);
          })
          .catch(reject);
      })
      .catch((res) => {
        let type = '';
        if (res.code === 117700511) {
          type = 'need_password';
        }
        reject({ type, msg: res.msg || '支付请求失败，请稍后再试' });
      })
      .then(() => {
        callback && callback();
      });
  }

  doCODPay({ pay_channel_name = '' }) {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title: '下单提醒',
        confirmText: '确认',
        content: this._getCODMessage(pay_channel_name),
        success: (res) => {
          if (res.confirm) {
            resolve();
          } else if (res.cancel) {
            reject();
          }
        },
        fail: () => {
          // 异常情况
          reject();
        }
      });
    });
  }

  _getCODMessage(payName) {
    let message = '';
    switch (payName) {
      case '到店付款':
        message = '你正在选择到店付款，下单后请自行到店领取并付款。';
        break;
      // 货到付款
      default:
        message = '你正在选择货到付款，下单后由商家发货，送货上门并收款。';
    }
    return message;
  }

  /**
   * 设置收单信息
   * @param cashierData 收单需要的数据
   * @param acquireNo 收单号
   * @return promise
   */
  setCashierData(cashierData = {}, acquireNo = '', isOrderCreated) {
    const formatedCashierData = mapKeysCase.toSnakeCase(cashierData) || {};
    this.payData.cashierData = formatedCashierData;

    // 如果传入了收单号，就认为已经收单初始化完成
    if (acquireNo) {
      this.isOrderCreated = true;
      this.payData.acquireNo = acquireNo;
    }

    // 当不走 createCashierOrder 逻辑时可以手动设置订单创建成果（ 如知识付费 ）
    if (isOrderCreated) {
      this.isOrderCreated = true;
    }
  }

  // 设置收银台工作模式
  setPrepayMode(prepay) {
    this.isPrepay = prepay;
  }
}
