/**
 * 处理支付操作
 */

export default {
  doPay(payTool, payData) {
    switch (payTool) {
      case 'WX_APPLET':
        return this.doWXPay(payTool, payData);
      case 'ECARD': // E卡
      case 'PREPAID_PAY': // 储值余额
      case 'VALUE_CARD': // 会员余额
      case 'GIFT_CARD': // 礼品卡
      case 'ENCHASHMENT_GIFT_CARD': // 新礼品卡
        return this._defaultPay(payTool, payData);
      case 'CREDIT_CARD': // 信用卡
        return this.doCreditCardPay();
      default:
        return Promise.reject({ msg: '暂不支持该支付方式' });
    }
  },

  doWXPay(payTool, payData = {}) {
    return new Promise((resolve, reject) => {
      if (!payData.deep_link_info || Object.keys(payData.deep_link_info).length === 0) {
        reject({ msg: '支付失败，请稍后重试!' });
        return;
      }
      const deepLinkData = payData.deep_link_info || {};
      wx.requestPayment({
        timeStamp: deepLinkData.timeStamp,
        nonceStr: deepLinkData.nonceStr,
        package: deepLinkData.package,
        signType: deepLinkData.signType,
        paySign: deepLinkData.paySign,
        success: res => {
          resolve({ res });
        },
        fail: res => {
          let type = 'fail';

          if (res.errMsg === 'requestPayment:fail cancel') {
            type = 'cancel';
          }

          reject({ res, type, msg: res.errMsg });
        }
      });
    });
  },

  doECardPay() {
    return new Promise((resolve) => {
      resolve({});
    });
  },

  doCreditCardPay() {
    return new Promise((resolve) => {
      resolve({});
    });
  },

  _defaultPay() {
    return new Promise((resolve) => {
      resolve({});
    });
  }
};
