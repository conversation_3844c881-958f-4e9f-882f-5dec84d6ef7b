<view class="order-item" catchtap="goDetail">
  <view class="{{ isVerify ? 'order-title has-time' : 'order-title' }}">
    <view class="buyer">{{ isBuyer ? '团长' : '下单人' }}：{{ nickname }} <text>({{ mobile }})</text></view>
    <view class="status">{{ stateText }}</view>
  </view>
  <view wx:if="{{ isVerify }}" class="order-time">
    下单时间：{{ orderTime }}
  </view>
  <view class="order-detail">
    <view class="order-detail__goods">
      <view
        wx:for="{{ goodsList }}"
        wx:for-item="goods"
        wx:if="{{ index < (showAllGoods ? goodsList.length : 2) }}"
        wx:key="{{ goods.itemId }}"
        class="goods-item__wrap"
      >
        <display-item
          order-no="{{ orderNo }}"
          detail="{{ goods }}"
          show-refund="{{ isBuyer && showRefund }}"
        ></display-item>
      </view>
      <view wx:if="{{ moreNum }}" class="more-goods" catchtap="onShowAllGoods">{{ moreText }}</view>
    </view>
  </view>
  <view wx:if="{{ detail.buyerMsg }}" class="order-remark">
    <view class="order-remark-content">备注：{{ detail.buyerMsg }}</view>
  </view>
  <view class="order-total">
    共{{ totalNum }}件 合计：¥ {{ _price }} <text wx:if="{{ !isBuyer && _profit > 0 }}">(预计利润: <text>¥ {{ _profit }}</text>)</text>
  </view>
  <view
    wx:if="{{ showSelffetch }}"
    class="order-item__button"
  >
    <van-button
      custom-class="confirm-btn"
      type="primary"
      size="small"
      round
      plain
      catchtap="onSelffetch"
    >
      确认提货
    </van-button>
  </view>
</view>
