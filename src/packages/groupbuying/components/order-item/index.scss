$font-color: #333;
$gray: #999;

.order-item {
  background-color: #fff;

  .order-title {
    display: flex;
    justify-content: space-between;
    padding: 14px 15px 14px 0;
    margin-left: 15px;
    color: $font-color;
    position: relative;
    font-size: 14px;

    .buyer {
      flex: 1;

      text {
        font-size: 12px;
        color: $gray;
      }
    }

    .status {
      color: #f60;
    }

    &.has-time {
      padding-bottom: 0;
    }
  }

  .order-time {
    margin: 5px 0 5px 15px;
    font-size: 12px;
    line-height: 16px;
    color: #999;
  }

  .order-detail {

    &__goods {
      .goods-item__wrap {
        margin-top: 5px;

        &:first-child {
          margin-top: 0;
        }
      }

      .more-goods {
        margin-top: 5px;
        padding: 15px 0;
        color: #38f;
        font-size: 14px;
        text-align: center;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 200%;
          height: 200%;
          transform: scale(.5);
          transform-origin: 0 0;
          pointer-events: none;
          box-sizing: border-box;
          border-bottom: 1px solid #e5e5e5
        }
      }
    }
  }

  .order-remark {
    overflow: hidden;
    font-size: 12px;
    color: #666;
    line-height: 14px;
    padding: 18px 15px 13px;

    &-content {
      float: right;
      word-break: break-all;
    }
  }

  .order-total {
    position: relative;
    color: #333;
    font-size: 14px;
    padding: 12px 15px 12px 0;
    text-align: right;

    text {
      color: #f60;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 200%;
      height: 200%;
      transform: scale(.5);
      transform-origin: 0 0;
      pointer-events: none;
      box-sizing: border-box;
      border-top: 1px solid #e5e5e5;
    }
  }

  &__button {
    text-align: right;
    padding: 0 15px 15px;
  }

  .confirm-btn {
    width: 78px;
  }
}
