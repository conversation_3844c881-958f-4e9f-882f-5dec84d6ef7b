import WscComponent from 'pages/common/wsc-component/index';
import money from '@youzan/weapp-utils/lib/money';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import Toast from '@vant/weapp/dist/toast/toast';
import { getOrderStateMap, calcTotalNum } from '../../common';
import { verifyFetchApi } from '../../my-profit/api';

WscComponent({
  properties: {
    orderTime: String,
    orderNo: String,
    goodsList: Array,
    profit: Number,
    price: Number,
    state: Number,
    detail: Object,
    isBuyer: Boolean,
    isVerify: {
      value: false,
      type: Boolean
    }
  },

  data: {
    nickname: '',
    mobile: '',
    showAllGoods: false,
    totalNum: 0,
    moreNum: false,
    moreText: '',
    stateText: '',
    showSelffetch: false,
    showRefund: false,
    _profit: 0,
    _price: 0,
    verifyAt: false
  },

  ready() {
    this.init();
  },

  methods: {
    init() {
      let {
        detail, verifyAt, goodsList, price, profit
      } = this.data;

      if (verifyAt) {
        detail = {
          ...detail,
          verifyAt
        };
      }

      const _totalNum = calcTotalNum(goodsList);
      const { stateText, showSelffetch, showRefund } = getOrderStateMap(detail);

      let _moreNum;
      if (goodsList.length > 2) {
        _moreNum = goodsList.length - 2;
      } else {
        _moreNum = 0;
      }

      const _moreText = `查看剩余${_moreNum}件商品`;

      this.setYZData({
        totalNum: _totalNum,
        moreNum: _moreNum,
        moreText: _moreText,
        stateText,
        showSelffetch,
        showRefund,
        nickname: this.getNickName(),
        mobile: this.getMobile(),
        _price: money(price).toYuan(),
        _profit: money(profit).toYuan()
      });
    },

    getNickName() {
      if (this.data.isBuyer) {
        return this.data.detail.headerName;
      }
      return this.data.detail.customerNickname || '匿名';
    },

    getMobile() {
      if (this.data.isBuyer) {
        return this.data.detail.headerMobile;
      }
      return this.data.detail.customerMobile;
    },

    goDetail() {
      const { orderNo, isBuyer } = this.data;
      const isHeader = isBuyer ? 0 : 1;
      wx.navigateTo({
        url: `/packages/groupbuying/buyer-trade/detail/index?orderNo=${orderNo}&isHeader=${isHeader}`
      });
    },

    onShowAllGoods() {
      this.setYZData({
        showAllGoods: !this.data.showAllGoods,
        moreText: this.data.moreText === '收起' ? `查看剩余${this.data.moreNum}件商品` : '收起'
      });
    },

    onSelffetch() {
      Dialog.confirm({
        message: '请确认货物，确认之后不可撤销',
        confirmButtonText: '确认提货',
        cancelButtonText: '我再想想'
      })
        .then(() => {
          this.verifyOrder();
        })
        .catch(() => {
          // just handle reject bu do nothing
        });
    },

    verifyOrder() {
      const { detail } = this.data;
      const { orderNo, headerBuyerId } = detail;
      verifyFetchApi({
        orderNo,
        headerBuyerId
      })
        .then(data => {
          if (data) {
            this.setYZData({
              verifyAt: +Date.now()
            });
            this.init();
          }
          Toast('提货成功');
        })
        .catch(e => {
          Toast(e.msg);
        });
    }
  }
});
