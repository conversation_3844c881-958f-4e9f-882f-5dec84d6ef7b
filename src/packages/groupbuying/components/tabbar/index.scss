.tabbar {
  width: 100%;
  height: 50px;
  display: flex;
  background-color: #fff;
  justify-content: space-around;
  align-items: center;
  padding-top: 5px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.fixed {
  left: 0;
  bottom: 0;
  position: fixed;
}

.hairline--bottom::after {
  content: '';
  transform: scale(0.5);
  position: absolute;
  border-top: 1px solid #e5e5e5;
  top: -50%;
  right: -50%;
  bottom: -50%;
  left: -50%;
}
