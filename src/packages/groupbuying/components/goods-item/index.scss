$font-color: #333;
$gray: #999;

.activity-goods {
  background: #fff;
  display: flex;
  padding: 15px;
  position: relative;

  &__img {
    width: 66px;
    height: 66px;
    margin-right: 10px;
    z-index: 1;
    position: relative;
    border-radius: 2px;
  }

  &__desc {
    display: inline-block;
    flex: 1;

    .title {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      line-height: 20px;
      color: $font-color;
      font-size: 14px;
    }

    .ump {
      margin: 10px 0;
      color: #f44;
      font-size: 10px;

      text {
        padding: 0 5px;
        border: 1px #ffecec solid;
      }

      text:first-child {
        background: #ffecec;
        border-radius: 8px 0 0 8px;
      }

      text:last-child {
        border-radius: 0 8px 8px 0;
      }
    }

    .price {
      &-pay {
        display: inline-block;
        color: #f44;
        font-size: 12px;
        margin-right: 5px;

        text {
          font-size: 16px;
        }
      }

      &-origin {
        display: inline-block;
        color: $gray;
        font-size: 10px;
        text-decoration: line-through;
      }

      &-sale {
        display: inline-block;
        color: $gray;
        font-size: 10px;
        vertical-align: middle;
      }

      &-refund {
        display: inline-block;
        color: #f60;
        font-size: 12px;
      }
    }
  }

  .stepper,
  .num-span,
  .goods-over {
    position: absolute;
    right: 15px;
    bottom: 14px;
    z-index: 8;
  }

  .stepper {
    right: 15px;
  }

  .num-span {
    color: $gray;
    font-size: 12px;
  }

  .goods-over {
    color: $font-color;
    font-size: 14px;
  }

  .switch {
    position: absolute;
    right: 15px;
    bottom: 14px;
  }

  &.hairline--bottom::after {
    content: "";
    transform: scale(.5);
    position: absolute;
    border-bottom: 1px solid #e5e5e5;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: calc(-50% + 18px);
  }

  &.gray-background {
    background: #f8f8f8;
  }
}
