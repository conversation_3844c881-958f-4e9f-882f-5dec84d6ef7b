<view class="activity-goods {{ showBuyNum ? 'gray-background' : !hideHairline ? 'hairline--bottom' : '' }}">
  <image
    wx:if="{{ images[0].cdnUrl }}"
    src="{{ images[0].cdnUrl }}"
    class="activity-goods__img"
    bindtap="imgAction"
  ></image>
  <view class="activity-goods__desc">
    <view class="title" style="margin-bottom: {{ _profit ? '' : '28px' }}">
      {{ title }}
    </view>
    <view wx:if="{{ _profit && stockNum !== -999  }}" class="ump">
      <text>预计利润¥{{ _profit }}</text>
      <text>仅剩{{ stockNum }}件</text>
    </view>
    <view class="price">
      <view class="price-pay" style="color: {{ showBuyNum ? '#333': '#f44' }}">¥ <text>{{ _payPrice || '0.00' }}</text></view>
      <view wx:if="{{ originPrice }}" class="price-origin">¥ {{ originPrice }}</view>
      <view wx:if="{{ _refund }}" class="price-refund">已退款 ¥ {{ _refund }}</view>
      <view wx:if="{{ saleNum !== -999 }}" class="price-sale">我已售{{ saleNum }}件</view>
    </view>
  </view>
  <block wx:if="{{ showStepper && !showGoodsOver }}">
    <vm-stepper
      class="stepper"
      min="0"
      max="{{ stockNum }}"
      stepper="{{ stepper }}"
      bindchange="onChange"
    ></vm-stepper>
  </block>
  <block wx:if="{{ showBuyNum }}">
    <text class="num-span">X{{ num }}</text>
  </block>
  <block wx:if="{{ showGoodsOver }}">
    <text class="goods-over">卖完啦</text>
  </block>
  <block wx:if="{{ showSwitch }}">
    <van-switch
      class="switch"
      checked="{{ checked }}"
      activeColor="#4b0"
      bind:change="onSwitchChange"
    />
  </block>
</view>
