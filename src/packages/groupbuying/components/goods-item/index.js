import WscComponent from 'pages/common/wsc-component/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import money from '@youzan/weapp-utils/lib/money';

WscComponent({
  properties: {
    goodsId: Number,
    pictures: Array,
    stockNum: {
      type: Number,
      value: -999,
      observer: '_stockNumChange'
    },
    title: String,
    payPrice: {
      type: Number,
      value: -999,
      observer: '_payPriceChange'
    },
    saleNum: {
      type: Number,
      value: -999
    },
    profit: {
      type: Number,
      value: -999,
      observer: '_profitChange'
    },
    originPrice: Number,
    joined: {
      type: [Bo<PERSON><PERSON>, Number],
      value: -999,
      observer: '_joinedChange'
    },
    showStepper: Boolean,
    showBuyNum: Boolean,
    showSwitch: Boolean,
    showGoodsOver: Boolean,
    refund: {
      type: Number,
      value: -999,
      observer: '_refundChange'
    },
    num: Number,
    stepper: {
      type: Number,
      value: 0
    },
    hideHairline: <PERSON>olean
  },

  data: {
    images: [],
    checked: false,
    _payPrice: 0,
    _originPrice: 0,
    _refund: 0,
    _profit: 0
  },

  ready() {
    // cdn image
    const _pictures = this.data.pictures.map((pic, index) => {
      if (index === 0) {
        pic.cdnUrl = cdnImage(pic.url, '!100x100.jpg');
      }
      return pic;
    });
    this.setYZData({
      images: _pictures
    });
  },

  methods: {
    imgAction() {
      this.triggerEvent('imgAction', this.data.pictures);
    },

    onChange(e) {
      this.setYZData({
        stepper: e.detail
      });
      this.triggerEvent('numChange', {
        goodsId: this.data.goodsId,
        price: this.data.payPrice,
        num: e.detail
      });
    },

    onSwitchChange({ detail }) {
      this.triggerEvent('switch', {
        goodsId: this.data.goodsId,
        join: detail,
        cb: () => {
          this.setYZData({
            checked: detail
          });
        }
      });
    },

    _payPriceChange(newValue) {
      this.setYZData({
        _payPrice: newValue ? money(newValue).toYuan() : ''
      });
    },

    _refundChange(newValue) {
      this.setYZData({
        _refund: newValue ? money(newValue).toYuan() : ''
      });
    },

    _profitChange(newValue) {
      this.setYZData({
        _profit: newValue ? money(newValue).toYuan() : ''
      });
    },
    _joinedChange(newValue) {
      this.setYZData({
        checked: newValue
      });
    },
  }
});
