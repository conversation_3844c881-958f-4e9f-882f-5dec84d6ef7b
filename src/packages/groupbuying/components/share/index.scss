.spread-popup {
  background: #f4f4f4 !important;

  .spread-panel {
    width: 100%;
    background: #fff;
    height: 134px;
    display: flex;
    justify-content: space-around;

    .wechat,
    .poster {
      flex: 1;
      text-align: center;
      padding: 0;
      background: transparent;
      border: 0;
      line-height: 1;
      box-sizing: border-box;
      border-radius: 0;

      &::after {
        display: none;
      }

      image {
        width: 44px;
        height: 44px;
        margin-top: 31px;
      }

      .text {
        margin-top: 10px;
        font-size: 12px;
        color: #999;
      }
    }
  }

  .cancel {
    width: 100%;
    background: #fff;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 16px;
    color: #333;
    margin-top: 5px;
  }
}

.poster-popup {
  background: transparent !important;
  overflow: visible !important;
}

.poster-wrap {
  position: relative;

  .content-wrap {
    border-radius: 12px;
    overflow: hidden;

    .content {
      display: block;
    }
  }

  .icon {
    position: absolute;
    height: 28px;
    width: 28px;
    top: -48px;
    right: 0;
  }

  .text {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 50%;
    bottom: -48px;
    height: 36px;
    width: 96px;
    border-radius: 18px;
    border: 1px solid #fff;
    font-size: 14px;
    color: #fff;
    background: transparent;
    box-sizing:  border-box;
    transform: translate(-50%);
  }
}
