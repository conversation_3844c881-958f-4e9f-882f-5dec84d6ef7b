<van-popup
  z-index="9999"
  show="{{ show }}"
  position="bottom"
  custom-class="spread-popup"
  overlay-style="background-color: rgba(0, 0, 0, 0.1);"
  bind:close="cancel"
>
  <view class="spread-panel">
    <button
      class="wechat"
      open-type="share"
    >
      <image src="https://img.yzcdn.cn/public_files/2017/08/29/b2e228542c67919b211c00ca8dbfed97.png" />
      <view class="text">微信好友</view>
    </button>
    <button
      class="poster"
      catchtap="showPoster"
    >
      <image src="https://img.yzcdn.cn/public_files/2019/04/08/c67c27b4137669f0ca57e95cf80eea39.png" />
      <view class="text">朋友圈海报</view>
    </button>
  </view>
  <view
    class="cancel"
    catchtap="cancel"
  >
    取消
  </view>
</van-popup>

<van-popup
  custom-class="poster-popup"
  show="{{ posterVisible }}"
  close-on-click-overlay="{{ false }}"
  z-index="10001"
>
  <view class="poster-wrap">
    <view class="content-wrap">
      <image
        class="content"
        style="height: {{ poster.height }}px;width: {{ poster.width }}px"
        src="{{ poster.url }}"
      />
    </view>
    <image
      class="icon"
      src="https://img.yzcdn.cn/public_files/2019/05/29/eda540b7681eab0fc730936c083db49f.png"
      catchtap="onClose"
    />
    <view class="text" catchtap="savePoster">保存图片</view>
  </view>
</van-popup>
