import WscComponent from 'pages/common/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';
import { getByKey, getPoster } from '../../buyer-trade/api';

let windowHeight = null;
let windowWidth = null;
const app = getApp();

const getWindowSize = () => {
  if (windowHeight === null) {
    const { windowHeight: height, windowWidth: width } = wx.getSystemInfoSync();
    windowHeight = height;
    windowWidth = width;
  }
  return {
    height: windowHeight,
    width: windowWidth
  };
};

const checkPoster = (key, width, height) => {
  return new Promise((resolve, reject) => {
    let i = 5;
    const check = () => {
      setTimeout(() => {
        getByKey({
          key
        })
          .then((res) => {
            if (res.img) {
              res.width = width;
              res.height = height;
              resolve(res);
            } else if (i > 0 && res.type === 'pending') {
              i--;
              check();
            } else {
              throw new Error('社区团购商品海报生成失败，请重试');
            }
          })
          .catch((e) => {
            reject(e);
          });
      }, 1000);
    };
    check();
  });
};

WscComponent({
  properties: {
    itemId: {
      type: [String, Number],
      value: ''
    },

    itemAlias: {
      type: String,
      value: ''
    },

    alias: {
      type: String
    },

    activityId: {
      type: [String, Number]
    },

    headerBuyerId: {
      type: [String, Number]
    },

    show: {
      type: Boolean,
      value: false
    }
  },

  data: {
    posterVisible: false,
    poster: {
      height: 0,
      width: 0,
      url: ''
    }
  },

  methods: {
    cancel() {
      this.triggerEvent('close');
    },

    // 显示海报
    showPoster() {
      const { headerBuyerId, alias, activityId, itemId, itemAlias } = this.data;
      const params = {
        headerBuyerId,
        alias,
        activityId,
        itemId,
        itemAlias,
        isWeapp: 1
      };
      Toast.loading({
        duration: 0,
        message: '海报绘制中...'
      });
      getPoster(params)
        .then((res) => {
          if (res.redisKey) {
            return checkPoster(res.redisKey, res.width, res.height);
          }
          const reason = '社区团购商品海报生成失败，请重试';
          return Promise.reject(reason);
        })
        .then((res) => {
          let { img, width, height } = res;
          const { height: innerHeight } = getWindowSize();
          // 屏幕太小的时候，缩小1/4
          if (innerHeight < 620) {
            width = (width * 2) / 5;
            height = (height * 2) / 5;
          } else {
            width /= 2;
            height /= 2;
          }
          const imgObj = {
            width,
            height,
            url: img
          };
          Toast.clear();
          this.setYZData({
            poster: imgObj,
            posterVisible: true
          });
        })
        .catch((e) => {
          Toast.clear();
          Toast(e.msg || e);
        });
    },

    onClose() {
      this.setYZData({
        posterVisible: false
      });
    },

    savePoster() {
      const { url } = this.data.poster;
      Toast.loading({
        message: '图片下载中',
        zIndex: 10010
      });
      this.downloadPoster(url)
        .then((res) => {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success() {
              Toast.success({
                message: '图片保存成功',
                zIndex: 10010
              });
            },
            fail() {
              Toast.fail({
                message: '图片保存失败',
                zIndex: 10010
              });
            }
          });
        })
        .catch(() => {
          Toast.fail({
            message: '图片保存失败',
            zIndex: 10010
          });
        });
    },

    downloadPoster(url) {
      return new Promise((resolve, reject) => {
        app.downloadFile({
          url,
          success: resolve,
          fail: reject
        });
      });
    },

    getContext() {
      const pages = getCurrentPages();
      return pages[pages.length - 1];
    }
  }
});
