import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    code: {
      type: String,
      value: '',
    },
    areaList: {
      type: Object,
      value: {},
    },
  },

  methods: {
    onAreaConfirm() {
      const area = this.selectComponent('#area');

      if (area) {
        const detail = area.getDetail();
        const { code: areaCode, province, city, county } = detail;
        const region = `${province},${city},${county}`;
        this.triggerEvent('confirm', {
          areaCode,
          province,
          city,
          county,
          region,
        });
      }
    },

    onCancel() {
      this.triggerEvent('cancel');
    },
  },
});
