// @ts-nocheck
import { mapState, mapActions } from '@ranta/store';
import { bridge, mapData, useAsHook } from '@youzan/ranta-helper';
import args from '@youzan/weapp-utils/lib/args';
import logv3 from 'utils/log/logv3';
import { vanxToRantaStore } from 'shared/common/base/ranta/store';
import { initSalesman } from 'shared/utils/salesman-share';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import { umpEnterShop } from '../../../../../common/ump-enter-shop'; // eslint-disable-line
import { getMessageActivityId } from './common/api';
import { generateShareTitle } from './common/membercard';
// eslint-disable-next-line
import store from './store';
import { resolveShareDataFunc } from '@/packages/ump/utils/resolve-share';
import { EShoppingGuideBindSourceType } from 'constants/guide';

export default class {
  @bridge('getCollageDate', 'data')
  getCollageDate: { goodsId: string; gapNum: number };

  @bridge('onShareClickEvent', 'event')
  onShareClickEvent = useAsHook<() => any[]>();

  ctx;

  store = vanxToRantaStore(store);

  constructor({ ctx }) {
    ctx.store = this.store;

    this.ctx = ctx;

    mapData(this, ['getCollageDate']);

    const res = mapState(
      this,
      [
        'goods',
        'groupInfo',
        'rightList',
        'isMemberCard',
        'originPicture',
        'shareQuery',
        'shareTitle',
        'isOngoing',
        'isJoinedGroup',
        'from',
      ],
      { setSelf: true }
    );

    Object.assign(this, res);

    mapActions(this, [
      'fetchGrouponDetail',
      'INIT_PAGE_QUERY',
      'SET_SHARE_QUERY',
      'TOGGLE_POPUP',
    ]);

    this.store.watch(
      'from',
      (val) => {
        this.ctx.data.openCustomNav = val === 'pay';
        this.ctx.data.title = val === 'pay' ? '付款详情' : '';
      },
      { immediate: true }
    );
    this.ctx.event.listen('ready', this.getActivityId);
  }

  beforePageMount({ query, route }) {
    const { umpAlias = '' } = query;
    const redirectUrl = args.add('/' + route, query);

    umpEnterShop(query, umpAlias, 'groupOn', redirectUrl, {
      name: 'groupOn_without_ump_alias',
      message: '多人拼团缺少必要参数',
    })
      .then(() => {
        this.initData(query);
      })
      .catch(() => {
        this.initData(query);
      });

    initSalesman.call(this, {
      sst: 13,
      gst: EShoppingGuideBindSourceType.TINY_PAGE,
      getSalesmanData: this.setSalesmanData,
    });
  }

  initData = (query) => {
    this.INIT_PAGE_QUERY(query);
    this.fetchGrouponDetail();
  };

  setSalesmanData = (err, data) => {
    if (err) return;
    const isNeedshowMore = data.isNeedShareMore !== false;
    if (isNeedshowMore) {
      this.handleSalesmanSetShare(data);
    }
  };

  handleSalesmanSetShare = (shareData) => {
    if (shareData && shareData.seller) {
      this.SET_SHARE_QUERY(getSalesmanParamsObject({ sl: shareData.seller }));
    }
  };

  /**
   * 分享设置
   */
  onShareAppMessage() {
    const {
      goods,
      groupInfo,
      rightList,
      isMemberCard,
      originPicture,
      shareQuery,
      shareTitle,
    } = this;
    const loggerParams = {
      spm: `groupon.${groupInfo.activityId}`,
    };
    const buyPrice = goods.skuPrice && goods.skuPrice.replace('起', '');
    const imageUrl = originPicture;
    const path = args.add('/packages/collage/groupon/detail/index', shareQuery);
    let title =
      shareTitle ||
      `【仅剩${groupInfo.gapNum}人】${buyPrice}元拼团购买${goods.title}`;

    if (groupInfo.isJoinedGroup) {
      title = isMemberCard
        ? generateShareTitle(buyPrice, goods.title, rightList)
        : title;
    }

    this.updateShareMenu();

    setTimeout(() => {
      this.TOGGLE_POPUP({
        name: 'showInvitePopup',
        data: true,
      });
    }, 2000);

    return this.doShare({
      title,
      path,
      imageUrl,
      loggerParams,
    }).then((shareData) =>
      resolveShareDataFunc(logv3.processShareData(shareData))
    );
  }

  doShare = (data) => {
    return this.ctx.cloud.invoke('onShareClickEvent').then((res) => {
      if (res?.[0] !== undefined) {
        return { ...data, ...res[0] };
      }
      return data;
    });
  };

  getActivityId = () => {
    const { isJoinedGroup, isOngoing } = this;
    const { groupId, endTime, activityType } = this.groupInfo;
    // 状态进行中且是团员才获取
    if (!isOngoing || !isJoinedGroup) return;

    getMessageActivityId({
      activityId: `${groupId}`,
      activityType,
      expirationTime: endTime / 1000,
    })
      .then((res) => {
        this.wxActivityId = res.wxActivityId;
      })
      .catch((err) => {
        console.error(err);
      });
  };

  updateShareMenu = () => {
    const activityId = this.wxActivityId;
    const { endTime, joinNum, gapNum } = this.groupInfo;
    // 团剩余时间小于24小时可发动态消息
    const isTimeValid = endTime - Date.now() < 24 * 60 * 60 * 1000;
    if (!activityId || !isTimeValid) return;
    // eslint-disable-next-line
    wx.updateShareMenu({
      // eslint-disable-line
      withShareTicket: true,
      isUpdatableMessage: true,
      activityId,
      targetState: 0,
      templateInfo: {
        parameterList: [
          {
            name: 'member_count',
            value: `${joinNum}`,
          },
          {
            name: 'room_limit',
            value: `${joinNum + gapNum}`,
          },
        ],
      },
    });
  };

  onPullDownRefresh() {
    this.fetchGrouponDetail();
  }
}
