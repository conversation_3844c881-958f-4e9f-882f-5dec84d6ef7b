import state from './state';
import getters from './getters';
import actions from './actions';
import mutations from './mutations';
import tradeCarousel from '@goods/containers/trade-carousel/store';

export const mergeStore = (srcStore = {}, destStore = {}) => {
  return {
    state: {
      ...srcStore.state,
      ...destStore.state,
    },
    getters: {
      ...srcStore.getters,
      ...destStore.getters,
    },
    actions: {
      ...srcStore.actions,
      ...destStore.actions,
    },
    mutations: {
      ...srcStore.mutations,
      ...destStore.mutations,
    },
  };
};

const rootStore = [
  {
    state,
    getters,
    actions,
    mutations,
  },
  tradeCarousel,
].reduce((a, b) => mergeStore(a, b), {});

export default rootStore;
