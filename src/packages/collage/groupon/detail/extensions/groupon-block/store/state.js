const state = {
  showSkuModal: false, // 是否展示sku弹层
  sku: {},

  showMemberList: false, // 是否展示团成员弹层
  memberListFrom: null, // 团成员弹层触发来源
  participants: [], // 团成员列表

  createGroupon: false, // 是否开团

  isCardTaken: false, // 是否领取过会员卡
  isMemberCard: false, // 是否会员卡拼团
  rightList: {}, // 会员卡权益
  card: {},

  isMillionGroup: false, // 是否万人团
  joinOther: false, // 凑别人的团
  joinOtherAlias: '', // 凑别人的团
  orderNo: '', // 订单号
  groupAlias: '', // 团别名

  posterImages: {}, // 海报图片
  shareQuery: '', // 分享链接参数
  showInvitePopup: false, // 是否展示上拉弹层

  shopName: '', // 店铺名
  shopLogo: '', // 店铺logo
  originPicture: '', // 商品原图
  shareTitle: '', // 分享标题
  userInfo: {}, // 用户信息

  goods: {}, // 商品信息
  groupInfo: {}, // 团信息

  extraData: {
    useCustomHeaderPrice: true,
    totalNum: 0,
    skuPrice: '',
    skuCouponPrice: '',
  },

  shopConfig: {}, // 店铺标识
  ongoingGroup: [], // 凑团列表
  // 来源
  from: '',

  ready: false,
  tradeRecordsV2: [],

  isSkuFetched: false,
};

export default state;
