import {
  SOLDSTATUS,
  ACTIVITY_TYPE_MAP,
  GROUP_TYPE_MAP,
  logger,
} from '../common/constants';

export default {
  isSoldOut(state) {
    const { soldStatus } = state.goods;
    return soldStatus === SOLDSTATUS.SOLDOUT;
  },
  isEnd(state) {
    const { soldStatus } = state.goods;
    const { isEnd } = state.groupInfo;
    return isEnd || soldStatus === SOLDSTATUS.SOLDOUT;
  },
  hasJoinedGroup(state) {
    const { ongoingGroup } = state;
    return ongoingGroup.length > 0;
  },
  isAllowOpenGroup(state, getters) {
    const { isAllowOpenGroup } = state.groupInfo;
    return isAllowOpenGroup && !getters.isEnd;
  },
  isOngoing(state, getters) {
    const { isGroupOnSuccess, isGroupOnFailed } = state.groupInfo;
    return !isGroupOnSuccess && !isGroupOnFailed && !getters.isEnd;
  },
  isJoinedGroup(state) {
    return state.groupInfo?.isJoinedGroup;
  },
  isOld2New(state) {
    const {
      groupInfo: { groupType },
    } = state;
    return +groupType === GROUP_TYPE_MAP.OLD_TO_NEW;
  },
  isLadder(state) {
    const {
      groupInfo: { activityType },
    } = state;
    return +activityType === ACTIVITY_TYPE_MAP.LADDER;
  },
  // group_status	: 0进行中、1已成团、2拼团失败、3活动结束
  grouponStatus(state, getters) {
    const { isGroupOnSuccess, isGroupOnFailed } = state.groupInfo;
    return logger.grouponStatus({
      isGroupOnSuccess,
      isGroupOnFailed,
      isEnd: getters.isEnd,
    });
  },

  weComFriendDecrease(state) {
    return (state.groupInfo.drainageInfo?.weComFriendDecrease / 100).toFixed(2);
  },
};
