import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { getShareParams } from 'shared/utils/share-params';
import getApp from 'shared/utils/get-safe-app';
import { ACTIVITY_TYPE_MAP } from '../common/constants';

const shareParams = getShareParams('native_custom');
const app = getApp();

const mutations = {
  // 初始化页面参数
  INIT_PAGE_QUERY(state, payload) {
    const {
      orderNo = '',
      groupAlias = '',
      shareUserId = '',
      shareGroupId = '',
      from = '',
    } = payload;
    state.orderNo = orderNo;
    state.from = from;
    state.groupAlias = groupAlias;
    state.pageQuery = payload;
    state.shareUserId = shareUserId;
    state.shareGroupId = shareGroupId;
    state.tradeCarousel.fixedTop = 15;
  },

  // 初始化团详情信息
  SET_GROUPON_DETAIL(state, payload) {
    const { goodsInfo, groupInfo } = payload;
    const isMemberCard = goodsInfo.goodsType === 20;
    let shareQuery = Object.assign(payload.shareQuery, shareParams);
    // 阶梯拼团去商详不展示拼团
    if (
      groupInfo.isJoinedGroup &&
      groupInfo.activityType !== ACTIVITY_TYPE_MAP.LADDER
    ) {
      shareQuery = Object.assign(payload.shareQuery, {
        shareUserId: app.getBuyerId(),
        shareGroupId: payload.groupInfo.groupId,
      });
    }
    // 图片压缩
    state.originPicture = cdnImage(goodsInfo.picture);
    goodsInfo.picture = cdnImage(goodsInfo.picture, '!220x0.jpg');
    goodsInfo.url = `/packages/goods/groupon/index?alias=${goodsInfo.alias}`;

    state.goods = goodsInfo;
    state.groupInfo = groupInfo;
    state.participants = payload.joinRecords;
    state.isMillionGroup = !groupInfo.isAllowOpenGroup;
    state.groupAlias = groupInfo.groupAlias;
    state.orderNo = state.orderNo || groupInfo.orderNo;
    state.isMemberCard = isMemberCard;
    state.shareQuery = shareQuery;

    state.shopConfig = payload.shopConfig;
    state.ongoingGroup = payload.ongoingGroup;
    state.ready = true;
  },

  // 初始化会员卡信息
  INIT_MEMBER_CARD_DETAIL(state, payload) {
    state.card = payload.card;
    state.rightList = payload.card.rightList;
    state.isCardTaken = payload.isTaken;
  },

  // 设置sku信息
  SET_SKU_DATA(state, payload) {
    const { perOrderLimit, buyNum } = state.groupInfo;
    const { limit } = state.goods;
    const quota = perOrderLimit || limit;

    let quotaUsed = buyNum;

    // 如果每单限购存在，看活动是否限购，活动限购取差，否则取0
    if (perOrderLimit > 0) {
      const canBuy = limit - buyNum;
      const lastBuy = perOrderLimit - canBuy;
      quotaUsed = limit && lastBuy > 0 ? lastBuy : 0;
    }

    state.sku = payload;
    state.sku.quota = quota;
    state.sku.quotaUsed = quotaUsed;
  },

  // 是否开团
  SET_IS_CREATE_GROUPON(state, payload) {
    state.createGroupon = payload;
  },

  // 设置是否凑团
  SET_IS_JOIN_OTHER_GROUPON(state, { joinOther, alias }) {
    state.joinOther = joinOther;
    state.joinOtherAlias = alias;
  },

  // sku扩展
  SET_SKU_EXTRA_DATA(state, payload) {
    state.extraData[payload.name] = payload.data;
  },

  // 海报图片
  SET_POSTER_IMAGE(state, payload) {
    state.posterImages = payload;
  },

  // 用户信息
  SET_USER_INFO(state, payload = {}) {
    state.userInfo = payload;
  },

  // // 店铺信息
  // SET_SHOP_DATA(state, payload = {}) {
  //   state.shopName = payload.shopName;
  //   state.shopLogo = payload.shopLogo;
  // },

  // 弹窗展示隐藏
  TOGGLE_POPUP(state, payload) {
    const { name, data } = payload;
    state[name] = data;
  },

  // 设置分享链接参数
  SET_SHARE_QUERY(state, payload) {
    const { shareQuery } = state;
    state.shareQuery = {
      ...shareQuery,
      ...payload,
      ...shareParams,
    };
  },
  SET_MEMBER_LIST_FROM(state, payload) {
    const { from } = payload;
    state.memberListFrom = from;
  },
  SET_SHARE_INFO(state, payload) {
    const { originPicture, shareTitle } = payload;
    state.originPicture = originPicture;
    state.shareTitle = shareTitle;
  },
  SET_SKU_FETCHED(state, payload) {
    state.isSkuFetched = payload;
  },
};

export default mutations;
