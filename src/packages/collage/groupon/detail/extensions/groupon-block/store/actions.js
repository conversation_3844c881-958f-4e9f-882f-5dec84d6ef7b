import Toast from '@vant/weapp/dist/toast/toast';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import omit from '@youzan/weapp-utils/lib/omit';
import getApp from 'shared/utils/get-safe-app';
import { formatSkuTree } from 'shared/common/components/base-sku/common/sku-format-tree';
import navigate from 'shared/utils/navigate';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';
import { ACTIVITY_TYPE_MAP, logger } from '../common/constants';

import {
  fetchCardByGoodsId,
  fetchGrouponDetail,
  fetchIsUserNewCustomer,
  fetchSkuDetail,
  getWeappCard,
} from '../common/api';

const app = getApp();
let requestCount = 0;
let isLogger = false;

const actions = {
  // 初始化团详情数据
  fetchGrouponDetail({ commit, dispatch, state }) {
    wx.showLoading();
    const { pageQuery } = state;

    // 参数去除 kdt_id，避免干扰进店逻辑
    const newQuery = omit(pageQuery, ['kdt_id']);

    fetchGrouponDetail(newQuery)
      .then((res) => {
        wx.hideLoading();
        // 拼团详情获取中的状态，这个状态下，等1s后重刷
        if (res.waitFlush) {
          if (requestCount < 3) {
            requestCount++;
            wx.showLoading({
              title: '数据加载中...',
            });
            setTimeout(() => {
              dispatch('fetchGrouponDetail', pageQuery);
            }, 1000);
          } else {
            wx.showModal({
              content: '获取拼团详情失败',
              showCancel: false,
              confirmText: '返回',
              success: () => {
                wx.navigateBack();
              },
            });
          }
          return;
        }

        if (res.alertType) {
          wx.showToast({
            title: res.alertTitle,
            icon: 'none',
          });
        }
        wx.stopPullDownRefresh();

        if (res.goodsInfo.goodsType === 20) {
          dispatch('fetchMembercardDetail', res.goodsInfo.goodsId);
        }

        // 分享链接参数
        const atrPs = pageQuery.atr_ps;
        const oldShareQuery = state.shareQuery || {};
        const atrPsObj = atrPs ? { atr_ps: atrPs } : {};
        const shareQuery = {
          ...atrPsObj,
          ...oldShareQuery,
          ...defaultEnterShopOptions,
          type: res.groupInfo.activityType,
          groupAlias: res.groupInfo.groupAlias,
          umpAlias: res.groupInfo.activityAlias,
        };

        res.shareQuery = shareQuery;

        commit('SET_GROUPON_DETAIL', res);
        commit('SET_SKU_EXTRA_DATA', {
          name: 'skuPrice',
          data: res.goodsInfo.skuPrice,
        });
        commit('SET_SKU_EXTRA_DATA', {
          name: 'totalNum',
          data: res.groupInfo.totalNum,
        });
        commit('SET_SKU_EXTRA_DATA', {
          name: 'skuCouponPrice',
          data: res.goodsInfo.couponPrice,
        });
        dispatch('getWeappCard', res.goodsInfo);
        dispatch('loggerInit');
      })
      .catch((err) => {
        wx.hideLoading();
        wx.showModal({
          title: '获取拼团详情失败',
          content: err.msg,
          showCancel: false,
          confirmText: '返回',
          success: () => {
            wx.navigateBack();
          },
        });
      });
  },
  loggerInit({ state, getters }) {
    // 营销埋点
    if (isLogger) {
      return;
    }
    const { from } = state;
    const status = getters.grouponStatus;
    app.logger &&
      app.logger.log({
        et: 'display', // 事件类型
        ei: 'enterpage', // 事件标识
        en: '浏览页面', // 事件名称
        pt: 'groupon', // 页面类型
        params: {
          group_status: status,
          spm: logger.spm,
          page_type: logger.pt(from),
        },
      });
    app.logger &&
      app.logger.log({
        et: 'custom',
        ei: 'view_activity',
        params: {
          act_id: state.groupInfo.activityId,
          act_type: '4',
          goods_id: state.goodsInfo.goodsId,
          spm: `groupon.${state.groupInfo.activityId}`,
        },
        si: app.getKdtId(),
      });
    isLogger = true;
  },

  // 初始化会员卡信息
  fetchMembercardDetail({ commit }, payload = 0) {
    fetchCardByGoodsId(payload).then((res) => {
      commit('INIT_MEMBER_CARD_DETAIL', res);
    });
  },

  // 处理老带新拼团
  resolveNewCustomerGroupon({ state, commit }, payload) {
    const { goods, groupInfo } = state;

    wx.showLoading();
    fetchIsUserNewCustomer()
      .then((isNewCustomer) => {
        wx.hideLoading();
        if (!isNewCustomer) {
          const { isMillionGroup } = state;
          const content = isMillionGroup
            ? '仅新用户可以参加此团'
            : '仅新用户可以参加此团，你可以开个新团立享优惠哦';
          const confirmButtonText = isMillionGroup ? '知道了' : '开新团';
          Dialog({
            message: content,
            showCancelButton: !isMillionGroup,
            cancelButtonText: '知道了',
            confirmButtonText,
          }).then(() => {
            if (isMillionGroup) {
              return;
            }
            if (groupInfo.activityType === ACTIVITY_TYPE_MAP.LADDER) {
              return navigate.navigate({
                url: `/packages/goods/groupon/index?alias=${goods.alias}`,
              });
            }
            commit('SET_IS_CREATE_GROUPON', true);
            commit('SET_IS_JOIN_OTHER_GROUPON', {
              joinOther: false,
              alias: null,
            });
            payload.callback();
          });
        } else {
          payload.callback();
        }
      })
      .catch(() =>
        wx.showModal({
          title: '提示',
          content: '获取参团信息失败',
        })
      );
  },

  // 获取sku数据
  getSkuData({ state, commit }) {
    const { goodsId } = state.goods;

    wx.showToast({
      title: '加载中',
      icon: 'loading',
    });

    fetchSkuDetail({
      goodsId,
    })
      .then((res) => {
        wx.hideToast();
        commit('SET_SKU_FETCHED', true);
        const sku = formatSkuTree(res.sku);

        commit('SET_SKU_DATA', sku);
        commit('TOGGLE_POPUP', {
          name: 'showSkuModal',
          data: true,
        });
      })
      .catch(() => {
        wx.hideToast();
        Toast('获取商品信息失败');
      });
  },
  getWeappCard({ commit }, payload) {
    const params = {
      shareType: 'groupon',
    };
    const offlineId = app.getOfflineId();
    params.alias = payload.alias;

    params.timestamp = +new Date();

    if (offlineId) {
      params.offlineId = offlineId;
    }
    getWeappCard(params).then((res) => {
      const { imgUrl, shareTitle } = res;
      commit('SET_SHARE_INFO', {
        originPicture: imgUrl,
        shareTitle,
      });
    });
  },
};
export default actions;
