.root {
  position: relative;
  overflow: hidden;
}

.good-box {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1;
  padding-bottom: 16px;
}

.goods-wrap {
  position: relative;
  padding: 12px 12px 16px;
  display: flex;
  align-items: flex-start;

  .goods-wrap__img {
    position: relative;
    margin-right: 12px;
    border-radius: 5px;
    overflow: hidden;
    background: #fdfbfd;
  }

  .goods-wrap__detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 2px 0;
    box-sizing: border-box;
    width: calc(100% - 120px - 12px);
    justify-content: space-between;
    min-height: 120px;

    .detail__title {
      position: relative;
      line-height: 20px;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      font-weight: 500;
      font-size: 15px;
      color: #323233;
      letter-spacing: 0;
    }
    .detail__good-tags {
      margin-top: 8px;
    }
    .detail__good-tag {
      background: #fdf6e7;
      border-radius: 2px;
      font-size: 12px;
      font-weight: normal;
      color: #faab0c;
      line-height: 16px;
      margin-right: 4px;
      padding: 1px 4px;

      &-red {
        background: #fde6e9;
        color: #f00;
      }
    }

    .detail-price {
      white-space: nowrap;
      bottom: 16px;
      display: flex;
      align-items: flex-end;
      margin-top: 8px;
      flex-wrap: wrap;
      height: 24px;

      &__current {
        font-weight: 500;
        color: #f44;
        display: flex;
        flex-direction: row;
        align-items: flex-end;

        &-tags {
          margin-top: 8px;
        }
        &-tag {
          font-size: 12px;
          font-weight: normal;
          color: #ee0a24;
        }
        &-price {
          font-weight: 600;
          font-size: 22px;
          color: #ee0a24;
          line-height: 22px;
          margin-right: 8px;
          display: flex;
          align-items: flex-end;
          &-unit {
            font-weight: 500;
            font-size: 14px;
            font-size: 16px;
            line-height: 17px;
          }

          &-text {
            margin-left: 2px;
            font-size: 12px;
            font-weight: normal;
            color: #ee0a24;
            line-height: 17px;
          }
        }
      }

      &__original {
        text-decoration: line-through;
        font-size: 12px;
        font-weight: normal;
        color: #c1c1c1;
      }

      &__gray {
        color: #999;
      }
    }
  }
}

.shop-tags {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-bottom: 8px;
  &__tag-official {
    width: 54px;
    height: 16px;
    background: url('https://img01.yzcdn.cn/public_files/cf75189501624ce4bf364525bfc9ab64.png')
      no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-right: 6px;
    vertical-align: middle;
  }

  &__tag-brand {
    width: 30px;
    height: 16px;
    background: url('https://img01.yzcdn.cn/public_files/39924d40a8fc9bd0e94ffa646fec1793.png')
      no-repeat;
    background-size: 100% 100%;
    margin-right: 6px;
  }
  &__old-shop {
    display: block;
    height: 16px;
    margin-right: 6px;
    width: 76px;
  }
}
