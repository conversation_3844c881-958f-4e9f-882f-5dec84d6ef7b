.root {
  background-color: #f8f8f8;
  padding: 8px 16px;
}

.tip {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #999;
  letter-spacing: 0;
  text-align: center;
  position: relative;
  line-height: 44px;
}

.tip::after,
.tip::before {
  flex: 1;
  content: '';
  display: inline-block;
  height: 1px;
  position: relative;
}

.tip::before {
  border-top-left-radius: 50%;
  border-bottom-left-radius: 50%;
  margin-right: 24px;
  background: #ebedf0;
}

.tip::after {
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
  margin-left: 24px;
  background: #ebedf0;
}

.goods-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-left: -9px;
  margin-top: -8px;
}

.content-block {
  padding: 0 8px;
}

.goods-block {
  width: calc((100vw - 41px) / 2);
  display: inline-block;
  margin-left: 9px;
  margin-top: 8px;
  padding-bottom: 8px;
  border-radius: 8px;
  background-color: #fff;
}

.goods-image {
  margin-bottom: 8px;
  width: 100%;
  height: calc((100vw - 41px) / 2);
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  position: relative;
  background: #f2f2f2;
  background-size: cover;

  image {
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
  }
}

.goods-title {
  height: 36px;
  line-height: 18px;
  margin-top: 8px;
  color: #323233;
  font-size: 13px;
  font-weight: 700;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-price {
  font-size: 16px;
  align-items: center;
  margin-top: 4px;
  color: #ee0a24;
  font-family: Avenir-Heavy;
  letter-spacing: 0;
  line-height: 20px;
}
