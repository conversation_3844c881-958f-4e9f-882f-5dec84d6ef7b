.base-cell {
  &::after {
    display: none;
  }
  &::before {
    border: 1px;
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 16px;
    top: 0;
    left: 16px;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(0.5);
  }
  .van-cell__right-icon-wrap {
    color: #c8c9cc;
  }
}
.base-block__label {
  font-size: 13px;
  font-weight: normal;
  color: #323233;
  line-height: 18px;
  margin-right: 8px;
}
.base-block__title {
  font-size: 13px;
  font-weight: normal;
  color: #969799;
  line-height: 16px;
}
