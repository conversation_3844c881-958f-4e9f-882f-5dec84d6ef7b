import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';
import { sendRecordUser } from '../../common/api';
import {
  getCurrentPageUrlWithParams,
  baseImgUrl,
} from '../../common/constants';

const CLOSE_STORAGE_KEY = 'close-storage';

const getCloseStorage = () => {
  return wx.getStorageSync(CLOSE_STORAGE_KEY); // eslint-disable-line
};

RantaWidget({
  properties: {
    // 商品图片
    picture: {
      type: String,
      value: '',
    },
    // 商品价格
    price: {
      type: String,
      value: '',
    },
  },

  data: {
    avatarPlaceHolder: `${baseImgUrl}2023/06/20/Ftb-ExXTx6rtnG2ZGI1PtlhpAwR-.png`,
    closeStorage: getCloseStorage(),
    showPop: false,
  },

  attached() {
    mapState(this, ['groupInfo'], { setSelf: true });
    mapState(this, ['weComFriendDecrease']);
    mapState(this, {
      drainageInfo(store) {
        return store.groupInfo.drainageInfo;
      },

      preferentialPrice: (store) => {
        const priceArr = this.data.price.split('起');
        const calculation =
          Number(priceArr[0]) - Number(store.weComFriendDecrease) > 0
            ? Number(priceArr[0]) - Number(store.weComFriendDecrease)
            : 0;
        const preferentialPrice = calculation.toFixed(2);
        if (priceArr.length > 1) {
          return `${preferentialPrice}起`;
        }
        return preferentialPrice;
      },
    });
  },

  methods: {
    openDialog() {
      const { isDrainage } = this.data.drainageInfo;
      if (isDrainage) {
        this.open();
        sendRecordUser({
          channel: 2,
          goodsPictureUrl: this.data.picture,
          backUrl: getCurrentPageUrlWithParams(),
        });
      }
    },
    setCloseStorage() {
      wx.setStorageSync(CLOSE_STORAGE_KEY, true); // eslint-disable-line
      this.setYZData({
        closeStorage: true,
      });
    },

    open() {
      this.setYZData({
        showPop: true,
      });
    },

    close() {
      this.setYZData({
        showPop: false,
      });
    },
  },
});
