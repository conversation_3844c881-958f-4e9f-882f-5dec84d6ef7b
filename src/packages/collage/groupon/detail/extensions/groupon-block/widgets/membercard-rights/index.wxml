<view wx:if="{{ isMemberCard && !groupInfo.isJoinedGroup }}" class="rights">
  <swiper
    circular
    bindchange="onChange"
    class="rights-swipe"
  >
    <swiper-item
      wx:for="{{ rights }}"
      wx:for-item="right"
      wx:key="index"
      item-id="{{ index }}"
      class="rights-swipe-item"
    >
      <view>
        <view
          class="rights-item"
          wx:for="{{ right }}"
          wx:for-index="{{ key }}"
          wx:key="key"
        >
          <image src="{{ item.icon }}" class="rights-icon" />
          <text class="rights-text">
            {{ item.text }}
          </text>
          <view class="rights-desc">
            {{ item.desc }}
          </view>
        </view>
      </view>
    </swiper-item>
  </swiper>
  <view wx:if="{{ rights.length > 1 }}" class="indicator">
    <view class="indicator-item {{ isSwipe ? 'indicator-item__right' : 'indicator-item__left' }}"></view>
  </view>
  <van-divider custom-style="margin: 20px 16px 0 16px;transform: scaleY(.5);" />
</view>

