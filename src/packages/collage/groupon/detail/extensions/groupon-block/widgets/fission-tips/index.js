import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';
import { fetchFissionDetail } from '../../common/api';

RantaWidget({
  data: {
    couponNum: 0,
    showFission: false,
  },
  attached() {
    mapState(this, ['groupInfo', 'orderNo'], { setSelf: true });
  },
  ready() {
    if (this.orderNo) {
      this.tryCreateAndGetFission();
    }
  },
  methods: {
    handleClick() {
      /* eslint-disable */
      wx.navigateTo({
        url: `/packages/ump/fission/index?sharer=true&order_no=${this.groupInfo.orderNo}`,
      });
      /* eslint-enable */
    },
    tryCreateAndGetFission() {
      fetchFissionDetail(this.orderNo).then((res) => {
        this.setYZData({
          showFission: !!(res && res.id > 0),
          couponNum: res ? res.quantity : 0,
        });
      });
    },
  },
});
