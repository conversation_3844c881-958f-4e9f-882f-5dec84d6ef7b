<van-cell is-link bind:click="togglePopup" class="base-cell">
  <view slot="title">
    <text class="base-block__label">玩法</text>
    <text class="base-block__title">多人拼单 · 人满发货 · 人不满退款</text>
  </view>
</van-cell>
<van-popup show="{{ popShow }}" z-index="{{ 200 }}" position="bottom" custom-class="rule-popup" closeable bind:close="togglePopup">
  <view class="rule-container">
    <view class="rule-header">玩法详情</view>
    <view class="rule-info">
      <qa-block content="{{ qaContent }}" />
      <image src="{{ qaImage }}" class="rule-image" />
    </view>
    <van-button custom-class="rule-btn" bind:click="togglePopup">知道了</van-button>
  </view>
</van-popup>
