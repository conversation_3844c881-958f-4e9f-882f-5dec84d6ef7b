<van-cell is-link bind:click="togglePopup" wx:if="{{ enable }}" class="base-cell">
  <view slot="title">
    <text class="base-block__label">代收</text>
    <text class="base-block__title">{{ title }}</text>
  </view>
</van-cell>
<van-popup show="{{ showPopup }}" z-index="{{ 200 }}" position="bottom" custom-class="header-popup" closeable bind:close="togglePopup">
  <view class="rule-container">
    <view class="rule-header">团长代收</view>
    <view class="rule-info">
      <view class="rule-info__section">
        <van-icon size="18" class="rule-info__icon" name="https://img01.yzcdn.cn/upload_files/2021/07/05/FmWbpNf6-BMTZH8jzPGt_qEtst4a.png" />
        <view>
          <view class="popup-title">统一发货 免运费</view>
          <view class="popup-content">该拼团商品成团后统一发货给团长，免除团员的邮费。</view>
        </view>
      </view>
      <view class="rule-info__section">
        <van-icon size="18" class="rule-info__icon" name="https://img01.yzcdn.cn/upload_files/2021/07/05/Fuxmbpv72vNNOfPCmJO2Ni2HHY2r.png" />
        <view>
          <view class="popup-title">团长代收 领取方便</view>
          <view class="popup-content">商品到货后由团长分发给团员，团员也可以直接联系团长领取；</view>
          <view class="popup-content">团长的收货信息将会展示给团员，以便领取。</view>
        </view>
      </view>
    </view>
    <van-button custom-class="rule-btn" bind:click="togglePopup">知道了</van-button>
  </view>
</van-popup>