 <base-sku
  wx:if="{{ showSkuModal }}"
  show="{{ showSkuModal }}"
  goods="{{ goods }}"
  sku="{{ sku }}"
  quota="{{ sku.quota }}"
  quota-used="{{ sku.quotaUsed }}"
  show-add-cart-btn="{{ false }}"
  extra-data="{{ extraData }}"
  start-sale-num="{{ sku.startSaleNum }}"
  buy-text="确认"
  properties="{{ sku.itemSalePropList }}"
  sku-properties="{{ sku.skuSalePropModelList }}"
  generic:sku-header-price="sku-header-price"
  bind:buy="onBuy"
  bind:sku-close="onSkuClose"
  bind:sku-selected="onSkuSelected"
  class="groupon-sku"
/>