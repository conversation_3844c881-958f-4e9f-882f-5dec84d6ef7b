.trade-carousel {
  // 调整定位方式 https://jira.qima-inc.com/browse/ONLINE-836058
  position: fixed;
  top: 14px;
  left: 12px;
  height: 32px;
  // 比 goods-fixed-info高一点
  z-index: 22;
  overflow: hidden;
  .trade-carousel-backup {
    width: 10em;
    line-height: 32px;
    font-size: 16px;
  }
  &__swipe {
    height: 32px;
    color: #fff;
    font-size: 12px;
    line-height: 16px;

    &-item {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      height: 32px;
      background-image: linear-gradient(-20deg, #ee0a24 0%, #ff6034 100%);
      border: 1px solid #fe765e;
      border-radius: 16px;
      padding: 0 8px 0 4px;
    }

    &-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      margin-right: 8px;
    }

    &-text {
      flex: 1;
      margin-right: 1px;
      line-height: normal;

      // @include ellipsis;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.move-enter-active {
  animation: move-in 0.5s;
}

.move-leave-active {
  animation: move-out 0.3s;
  animation-fill-mode: forwards;
}

@keyframes move-in {
  0% {
    transform: translateY(32px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes move-out {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-32px);
  }
}
