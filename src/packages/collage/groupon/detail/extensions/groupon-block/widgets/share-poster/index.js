/* eslint-disable @youzan-open/tee/no-platform-field */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import authorize from '@/helpers/authorize';
import { promisifyWxStyle } from 'utils/promisify';
import { getShareParams } from 'shared/utils/share-params';
import { generatePoster } from '../../common/api';
import { logger } from '../../common/constants';
import useWxImageShare from '@youzan/decorate-tee/src/common/utils/use-wx-image-share';
import args from '@youzan/utils/url/args';

const app = getApp();
const loadImage = (src) =>
  app.downloadFile({ url: src }).then((result) => result.tempFilePath);

RantaWidget({
  data: {
    shareTime: 0,
    isShowInvitePost: false,
    shareImgSrc: '',
    actions: [
      { name: '微信', icon: 'wechat', type: 'share', openType: 'share' },
      { name: '分享海报', icon: 'poster', type: 'poster' },
    ],
    // 是否知道微信微信能力结果
    hasWxShareRes: false,
    // 是否有微信分享能力
    hasWxShareAbility: false,
  },

  observers: {
    showInvitePopup(val) {
      if (val) {
        app.logger &&
          app.logger.log({
            et: 'view', // 事件类型
            ei: 'sharecard', // 事件标识
            en: '分享组件曝光', // 事件名称
            params: {
              page_type: logger.pt(this.from),
              component: 'sharecard',
            }, // 事件参数
          });
      }
    },
  },

  attached() {
    mapState(this, ['showInvitePopup', 'isMemberCard', 'groupInfo']);
    mapState(
      this,
      ['card', 'shopLogo', 'shopName', 'goods', 'shareQuery', 'from'],
      { setSelf: true }
    );

    this.shareParams = getShareParams('poster');

    mapActions(this, ['TOGGLE_POPUP']);
  },

  methods: {
    showWxShareImg(imgUrl, data) {
      wx.downloadFile({
        url: imgUrl,
        success: (res) => {
          const sharePath = args.add(data.page, data.query);
          wx.showShareImageMenu({
            path: res.tempFilePath,
            needShowEntrance: true,
            entrancePath: sharePath,
            fail: (e) => {
              if (e.errMsg.includes('auth deny')) {
                this.showAuthModal();
              }
            },
            complete: this.closePop,
          });
        },
        complete() {
          wx.hideLoading();
        },
      });
    },

    showAuthModal() {
      wx.showModal({
        content: '需要同意将分享图片保存到相册，点击确定后跳转至设置页操作',
        success: (e) => {
          if (e.cancel) return;
          wx.openSetting({
            success: ({ authSetting }) => {
              if (authSetting['scope.writePhotosAlbum']) {
                this.clickSaveImage();
              }
            },
          });
        },
      });
    },

    getPoster() {
      // 先获取微信分享图片能力
      if (!this.data.hasWxShareRes) {
        this.setYZData({
          hasWxShareRes: true,
        });
        useWxImageShare({ kdtId: this.kdtId }).then((res = false) => {
          this.setYZData({
            hasWxShareAbility: res,
          });
        });
      }
      const page = 'packages/collage/groupon/detail/index';
      const { isMemberCard } = this.data;
      const { shopName, shopLogo, goods, card = {} } = this;
      const { price, originPrice, picture, title, alias } = goods;
      const pageQuery = { ...this.shareQuery, ...this.shareParams };
      const priceInFen = +price[1][1] ? price[1] : price[1][0]; // 分价格做去0处理 '20' -> '2'

      const data = {
        alias,
        page,
        isMemberCard,
        query: pageQuery,
        shopLogo,
        shopName,
        cardName: card.name || '',
        goods: {
          priceInYuan: price[0],
          priceInFen,
          originPrice,
          title,
          picture,
        },
      };

      wx.showLoading({ title: '正在生成' });
      generatePoster(data)
        .then((res) => {
          if (res.img)
            return this.data.hasWxShareAbility ? res.img : loadImage(res.img);
          return Promise.reject();
        })
        .then((url) => {
          wx.hideLoading();
          this.setYZData({
            shareImgSrc: url,
            isShowInvitePost: !this.data.hasWxShareAbility,
          });
          this.data.hasWxShareAbility && this.showWxShareImg(url, data);
          this.onClose();
        })
        .catch(() => {
          wx.hideLoading();
          wx.showToast({
            title: '生成海报失败',
            icon: 'none',
          });
        });
    },

    // 保存海报
    clickSaveImage() {
      const { shareImgSrc } = this.data;
      if (!shareImgSrc) return;

      wx.showLoading({ title: '保存中' });
      authorize('scope.writePhotosAlbum')
        .then(() => {
          promisifyWxStyle(wx.saveImageToPhotosAlbum)({ filePath: shareImgSrc })
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000,
              });
              this.closeInvitePostModal();
            })
            .catch(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000,
              });
            });
        })
        .catch(() => {
          wx.hideLoading();
          this.showAuthModal();
        });
    },

    closeInvitePostModal() {
      this.setYZData({
        isShowInvitePost: false,
      });
    },

    onSelect(e) {
      this.onClose();
      const { type } = e.detail;

      if (type === 'poster') {
        // 海报
        app.logger &&
          app.logger.log({
            et: 'click', // 事件类型
            ei: 'share', // 事件标识
            en: '分享', // 事件名称
            params: {
              share_cmpt: 'poster',
            },
          });
        app.logger &&
          app.logger.log({
            et: 'click', // 事件类型
            ei: 'bill_click', // 事件标识
            en: '海报点击', // 事件名称
            params: {
              component: 'sharecard',
              is_first_share: this.data.shareTime > 0,
              page_type: logger.pt(this.from),
            }, // 事件参数
          });
      }

      if (type === 'share') {
        app.logger &&
          app.logger.log({
            et: 'click', // 事件类型
            ei: 'wx_click', // 事件标识
            en: '微信好友点击', // 事件名称
            params: {
              component: 'sharecard',
              is_first_share: this.data.shareTime > 0,
              page_type: logger.pt(this.from),
            },
          });
        this.setYZData({
          shareTime: 1,
        });
        return;
      }
      this.getPoster();
    },

    onClose() {
      this.TOGGLE_POPUP({
        name: 'showInvitePopup',
        data: false,
      });
    },
  },
});
