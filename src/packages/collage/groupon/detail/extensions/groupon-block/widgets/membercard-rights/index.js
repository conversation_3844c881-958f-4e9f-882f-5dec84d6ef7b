import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';
import each from '@youzan/weapp-utils/lib/each';
import { DEFAULT_RIGHTS } from '../../common/membercard';

RantaWidget({
  data: {
    isSwipe: false,
    rights: {},
    groupInfo: {},
  },

  observers: {
    rightList(newVal) {
      if (Object.keys(newVal).length > 0) {
        const rightLists = this.generateRightList(newVal);
        const rights = this.sliceArr(rightLists, 4);
        this.setYZData({
          rights,
        });
      }
    },
  },

  attached() {
    mapState(this, ['isMemberCard', 'groupInfo', 'rightList']);
  },

  methods: {
    generateRightList(rightList) {
      const result = [];
      const defaultRights = DEFAULT_RIGHTS;
      each(rightList, (item, key) => {
        // 没有该权益
        if (!item.get) {
          delete defaultRights[key];
        } else {
          if (!item.detail) return true;
          // 历史bug处理
          if (defaultRights[key]) {
            defaultRights[key].desc = defaultRights[key].desc.replace(
              /#detail#/g,
              item.detail
            );
          }
        }
      });

      each(defaultRights, (v) => {
        result.push(v);
      });

      return result;
    },

    sliceArr(array, size) {
      const result = [];
      for (let i = 0; i < Math.ceil(array.length / size); i++) {
        const start = i * size;
        const end = start + size;
        result.push(array.slice(start, end));
      }
      return result;
    },

    onChange() {
      this.setYZData({
        isSwipe: !this.data.isSwipe,
      });
    },
  },
});
