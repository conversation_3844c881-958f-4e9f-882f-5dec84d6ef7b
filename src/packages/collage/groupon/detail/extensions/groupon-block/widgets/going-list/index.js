import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';
import { logger } from '../../common/constants.js';

const app = getApp();
RantaWidget({
  attached() {
    mapState(this, ['from'], { setSelf: true });
    mapState(this, ['ongoingGroup']);
    mapState(this, {
      show(store) {
        const { ongoingGroup, isSoldOut } = store;
        return !isSoldOut && ongoingGroup && ongoingGroup.length > 0;
      },
    });
  },
  ready() {
    if (this.data.show) {
      app.logger &&
        app.logger.log({
          et: 'view', // 事件类型
          ei: 'coutuan', // 事件标识
          en: '凑团组件曝光', // 事件名称
          params: {
            page_type: logger.pt(this.from),
            component: 'coutuan',
          }, // 事件参数
        });
    }
  },
  methods: {
    handleClick(e) {
      this.triggerEvent('next', e.target.dataset);
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'coutuan_click', // 事件标识
          en: '凑团点击', // 事件名称
          params: {
            component: 'coutuan',
            page_type: logger.pt(this.from),
          },
        });
    },
  },
});
