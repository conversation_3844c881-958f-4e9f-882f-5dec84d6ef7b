.group-bargin-tip {
  background: #fff1ee;
  border-radius: 5px;
  margin: -8px 12px 0;
  position: relative;
  border: 1px solid #ffd7d7;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px 0 12px;

  &::before {
    content: '';
    position: absolute;
    border-left: 1px solid #ffd7d7;
    border-top: 1px solid #ffd7d7;
    width: 8px;
    height: 7px;
    left: 195px;
    top: -5px;
    background: #fff1ee;
    transform: rotate(45deg);
  }

  &__title {
    display: flex;
    justify-items: center;
    align-items: center;
    flex: none;

    &-icon {
      width: 22px;
      height: 22px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 8px;
      background: linear-gradient(141.55deg, #ff2727 44.48%, #ff2171 84.35%);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &-img {
      width: 16.5px;
      height: 16.5px;
    }

    &-desc {
      font-size: 13px;
      line-height: 30px;
      color: #333;
      flex: 1;
      text-overflow: ellipsis;
      font-weight: 400;
      display: flex;
      align-items: center;

      &-num {
        color: #e00000;
        font-weight: 600;
      }
    }
  }

  &__value {
    height: 22px;
    background: #ff000b;
    border-radius: 12px;
    font-size: 12px;
    line-height: 22px;
    text-align: center;
    color: #fefdff;
    padding: 0 8px;
  }

  &__icon {
    color: #999;
  }
}

.qr-popup-wrap {
  border-radius: 24px 24px 0 0;
}

.qr-popup {
  height: 417px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 16px;
  position: relative;

  &__title {
    margin-top: 32px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #323233;

    &-num {
      color: #e00000;
      font-weight: 500;
      .price-bold {
        font-size: 20px;
      }
    }
  }

  &__desc {
    margin-top: 8px;
    color: #999;
    font-size: 14px;
    line-height: 20px;
  }

  &__image_container {
    border: 1px solid #ffc8ce;
    border-radius: 10px;
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #fef8f7;
    height: 264px;
    width: 100%;
  }

  &__image {
    margin-top: 32px;
    width: 176px;
    height: 176px;
    border-radius: 4px;
  }

  &__tap {
    margin-top: 12px;
    color: #999;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }

  .close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 20001;
    color: #7d7e80;
    width: 22px;
    height: 22px;
  }
}
