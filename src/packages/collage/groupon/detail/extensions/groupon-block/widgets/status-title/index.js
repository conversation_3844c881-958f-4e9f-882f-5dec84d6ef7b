import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';

RantaWidget({
  attached() {
    mapState(this, ['groupInfo', 'isOngoing']);
    mapState(this, {
      title(store) {
        const { isGroupOnSuccess, isGroupOnFailed, isJoinedGroup } =
          store.groupInfo;
        if (isGroupOnSuccess) {
          if (isJoinedGroup) {
            return '恭喜你，拼团成功';
          }
          return '拼团已满';
        }
        if (store.isEnd) {
          return '活动已结束';
        }
        if (isGroupOnFailed) {
          return '人数不足，拼团失败';
        }
      },
    });
  },
});
