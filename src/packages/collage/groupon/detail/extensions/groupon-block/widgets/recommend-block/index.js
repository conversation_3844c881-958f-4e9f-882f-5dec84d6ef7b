import { mapState } from '@ranta/store';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import money from '@youzan/weapp-utils/lib/money';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { RantaWidget } from 'shared/common/base/ranta/widget';
import spm from 'shared/utils/spm';
import { fetchRecommendGoods } from '../../common/api';
import { logger } from '../../common/constants';

const app = getApp();

RantaWidget({
  data: {
    recommendGoods: [],
  },
  observers: {
    'groupInfo.recommendGoodsInfo': function (val) {
      if (val) {
        this.getList();
      }
    },
  },
  attached() {
    mapState(this, ['from'], { setSelf: true });
    mapState(this, ['ready', 'groupInfo']);
  },
  methods: {
    getBannerId(i) {
      return `${spm.getPageSpmTypeId()}~grouponRecommend~${i}~${makeRandomString(
        8
      )}`;
    },
    getList() {
      const { type = 0, goodsIdList = [] } =
        this.data.groupInfo.recommendGoodsInfo || {};
      if (type === 1 && !goodsIdList.length) return;
      const data = {
        type,
        ids: JSON.stringify(goodsIdList),
        scene: this.data.from === 'pay' ? 'wsc~group~ps' : 'wsc~group~ad',
      };
      fetchRecommendGoods(data).then((res = []) => {
        const recommendGoods = res
          .filter((v) => !v.isVirtual)
          .map((v) => {
            return {
              ...v,
              price: money(v.price).toYuan(),
              imageUrl: cdnImage(v.imageUrl, '!340x0.jpg'),
            };
          });
        this.setYZData({
          recommendGoods,
        });
        if (type === 0) {
          (recommendGoods || []).forEach((item, i) => {
            app.logger &&
              app.logger.log({
                et: 'view', // 事件类型
                ei: 'view', // 事件标识
                en: '商品曝光', // 事件名称
                params: {
                  component: 'recommend',
                  page_type: logger.pt(this.from),
                  alg: item.algs,
                  banenr_id: this.getBannerId(i + 1),
                  goods_id: item.id,
                  item_type: 'goods',
                  item_id: item.id,
                },
              });
          });
        }
      });
    },
  },
});
