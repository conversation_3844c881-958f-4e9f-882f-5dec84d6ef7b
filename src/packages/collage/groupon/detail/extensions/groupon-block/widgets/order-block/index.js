import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';
import { memberCardGoodsType } from '../../common/constants';

RantaWidget({
  data: {
    groupInfo: {},
    goods: {},
  },

  attached() {
    mapState(this, ['groupInfo', 'goods']);
    mapState(this, {
      url(store) {
        if (store.goods.goodsType === memberCardGoodsType) {
          `/packages/card/detail/index?alias=${store.card.alias}`;
        }
        return `/packages/trade/order/result/index?orderNo=${store.groupInfo.orderNo}`;
      },
    });
  },
});
