.member-list {
  height: 460px;
  border-radius: 20px 20px 0 0;
  .rule-container {
    height: 100%;
    box-sizing: border-box;
    color: #323233;
    overflow: hidden;
  }

  .rule-header {
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    text-align: center;
    position: relative;
    font-weight: 500;
  }
  .member-tip {
    font-weight: 500;
    font-size: 14px;
    color: #323233;
    line-height: 20px;
    margin: 12px 17px;
  }
  .member-scroll {
    height:calc(100% - 86px);
    height:calc(100% - 86px - constant(safe-area-inset-bottom));
    height:calc(100% - 86px - env(safe-area-inset-bottom));
    margin-bottom: 40px;
  }

  .rule-info {
    padding: 12px 16px;
    font-size: 15px;
    line-height: 24px;
    &__section {
      display: flex;
      flex-direction: row;
      margin-bottom: 22px;
    }
    &__icon {
      width: 18px;
      height: 18px;
      margin-right: 10px;
      line-height: 22px;
    }
    .popup-title {
      color: #323233;
      font-size: 16px;
    }
    .popup-content {
      color: #646566;
      font-size: 14px;
    }
  }

  .rule-btn {
    position: fixed;
    left: 10px;
    right: 10px;
    bottom: 8px;
    bottom: calc(8px + constant(safe-area-inset-bottom));
    bottom: calc(8px + env(safe-area-inset-bottom));
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    background-image: linear-gradient(90deg, #ff6034 0%, #ee0a24 100%);
    border-radius: 100px;
    color: #fff;
    text-align: center;
  }

  .lists {
    background: #fff;

    .list {
      display: flex;

      &-img {
        position: relative;
        margin-right: 8px;
        display: flex;
        align-items: center;
        .img {
          border-radius: 50%;
          width: 40px;
          height: 40px;
        }
      }
      .custom-main {
        display: flex;
        align-items: center;
        flex-direction: row;
        line-height: 20px;
      }
      .tag {
        background-image: linear-gradient(135deg, #ff5644 0%, #fb1c60 100%);
        border: 0;
        border-radius: 16px;
        font-size: 10px;
        font-weight: normal;
        color: #fff;
        text-align: right;
        line-height: 16px;
        padding: 0 4px;
        margin-left: 4px;
      }
      .tag-receiver {
        font-size: 12px;
        font-weight: normal;
        color: #f44;
        line-height: 16px;
        background: #ffecec;
        border-radius: 16px;
        padding: 0 4px;
        margin-left: 4px;
      }
      .tag-icon {
        position: absolute;
        right: 16px;
        top: 18px;
        border: 0;
        border-radius: 50%;
        width: 24px;
        height: 24px;
      }
      .custom-label {
        color: #969799;
        line-height: 20px;
      }
      .nickname {
        font-size: 14px;
        color: #333;
        line-height: 18px;
        overflow: hidden;
      }

      .time {
        margin-top: 8px;
        font-size: 12px;
        color: #969799;
        line-height: 16px;
      }
    }
  }
}
