import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import { BUTTONS_DATA, logger } from '../../common/constants';
import { goBuy } from '../../common/membercard';

const app = getApp();

const loadImage = (src) =>
  app.downloadFile({ url: src }).then((result) => result.tempFilePath);

RantaWidget({
  attached() {
    mapState(
      this,
      [
        'groupInfo',
        'isOngoing',
        'card',
        'goods',
        'isCardTaken',
        'isMemberCard',
        'from',
        'isFetchedSku',
      ],
      { setSelf: true }
    );
    mapState(this, {
      btns(store) {
        const {
          groupInfo = {},
          goods = {},
          isOngoing,
          hasJoinedGroup,
          isEnd,
          isAllowOpenGroup,
          isLadder,
          isOld2New,
        } = store;
        const btnList = [
          BUTTONS_DATA.invite,
          BUTTONS_DATA.join,
          BUTTONS_DATA.joinOther,
          BUTTONS_DATA.reopening,
          BUTTONS_DATA.buy,
        ].filter((btnData) => {
          if (
            btnData.checkEnable(
              {
                ...groupInfo,
                isOngoing,
                hasJoinedGroup,
                isEnd,
                isAllowOpenGroup,
                isLadder,
                isOld2New,
              },
              goods
            )
          ) {
            return true;
          }
          return false;
        });
        if (btnList.find((b) => b.type === 'buy') || btnList.length === 0) {
          btnList.push(BUTTONS_DATA.toShop);
        }
        return btnList.map((b) => ({
          ...b,
          action: b.action.bind(this),
        }));
      },
    });

    mapActions(this, [
      'SET_MEMBER_LIST_FROM',
      'TOGGLE_POPUP',
      'SET_IS_CREATE_GROUPON',
      'SET_USER_INFO',
      'SET_IS_JOIN_OTHER_GROUPON',
      'resolveNewCustomerGroupon',
      'getSkuData',
    ]);
  },
  ready() {
    const { isOngoing, groupInfo } = this;
    if (groupInfo.isJoinedGroup && isOngoing) {
      this.TOGGLE_POPUP({
        name: 'showInvitePopup',
        data: true,
      });
    }
    if (isOngoing && !groupInfo.isJoinedGroup) {
      app.logger &&
        app.logger.log({
          et: 'view', // 事件类型
          ei: 'join_view', // 事件标识
          en: '立即参团按钮曝光', // 事件名称
          pt: 'groupon', // 页面类型
          pi: groupInfo.activityId, // 页面业务Id
          params: {
            page_type: logger.pt(this.from), // 页面类型
          },
        });
    }
  },
  methods: {
    handleClick(e) {
      const { type: datasetType } = { ...e.target.dataset };
      const { type: detailType } = { ...e.detail };
      const content = BUTTONS_DATA[detailType] || BUTTONS_DATA[datasetType];
      const action = content.action.bind(this);
      action(e);
    },
    fetchGoodsDetail() {
      const { card, goods, isCardTaken, isMemberCard, groupInfo } = this;
      if (goods.alias === '') {
        return;
      }

      if (isMemberCard) {
        // eslint-disable-next-line
        wx.showToast({
          title: '加载中',
          icon: 'loading',
        });
        // 区分参团、开个新团
        const cardSku = card.sku[0];
        const { createGroupon } = this;
        const activity = {
          id: groupInfo.activityId,
          price: goods.price[0] * 100 + goods.price[1],
          alias: groupInfo.groupAlias,
          activityType: groupInfo.activityType,
        };

        if (isCardTaken) {
          // eslint-disable-next-line
          wx.showToast({
            title: '你已持有该会员卡，无法参与拼团',
            icon: 'none',
          });
        } else {
          goBuy(cardSku, createGroupon, activity);
        }

        return;
      }
      // 请求过sku信息
      if (this.isFetchedSku) {
        this.TOGGLE_POPUP({
          name: 'showSkuModal',
          data: true,
        });

        return;
      }

      this.getSkuData();
    },
    onGetUserInfo(e) {
      const defaultUserAvatar =
        'https://img.yzcdn.cn/public_files/2018/09/07/af3aa756277cb619b9eb6062ec46cf57.png'; // eslint-disable-line
      const { detail } = e;
      let nickName = '神秘用户';
      let avatarUrl = defaultUserAvatar;

      if (detail.userInfo) {
        detail.userInfo.nickName && (nickName = detail.userInfo.nickName);
        detail.userInfo.avatarUrl && (avatarUrl = detail.userInfo.avatarUrl);
      }

      loadImage(avatarUrl).then((loadedAvatarUrl) => {
        this.SET_USER_INFO({
          nickName,
          avatarUrl: loadedAvatarUrl,
        });
      });
    },
  },
});
