<view wx:if="{{ isOngoing && !groupInfo.isJoinedGroup }}" class="status-buy">
  仅剩
  <text class="status-buy-strong">{{ groupInfo.gapNum }}</text>
  个名额，
  <count-down
    cls="status-buy-countdown"
    font-width="18"
    time="{{ groupInfo.remindTime * 1000 }}"
  />
  后结束
</view>
<view wx:elif="{{ isOngoing && groupInfo.isJoinedGroup }}">
  <view class="status-title">
    还差
    <text class="status-title-strong">{{ groupInfo.gapNum }}</text>
    人成团，快邀请好友一起拼
  </view>
  <view class="status-countdown">
    仅剩
    <count-down
      cls="status-countdown-text"
      font-width="16"
      time="{{ groupInfo.remindTime * 1000 }}"
    />
  </view>
</view>
<view class="status-title" wx:else>{{ title }}</view>

