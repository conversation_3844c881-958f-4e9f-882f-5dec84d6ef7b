import { RantaWidget } from 'shared/common/base/ranta/widget';
import args from '@youzan/weapp-utils/lib/args';
import { mapState } from '@ranta/store';
import {
  formatPrice,
  ACTIVITY_TYPE_MAP,
  GROUP_BARGAIN_TYPE,
  BRAND_CERT_TYPE,
  OLD_SHOP_YEARS,
} from '../../common/constants';

RantaWidget({
  externalClasses: ['custom-class', 'goods-class'],

  properties: {
    // 商品信息
    goods: {
      type: Object,
      value: {},
    },
  },

  data: {
    showOfficialTag: false,
    showBrandTag: false,
    oldShopTagImg: '',
  },

  attached() {
    mapState(this, ['shareUserId', 'shareGroupId', 'groupInfo', 'shopConfig'], {
      setSelf: true,
    });
    mapState(this, {
      url(store) {
        const { shareUserId, shareGroupId, groupInfo = {}, goods } = store;
        const { activityType } = groupInfo;
        if (activityType === ACTIVITY_TYPE_MAP.LADDER) {
          return goods.url;
        }
        return args.add(goods.url, { shareUserId, shareGroupId });
      },
      showPrice(store) {
        const { couponPrice, price } = store.goods;
        const strArr = (couponPrice || price.join('.')).split('起');
        return `${+strArr[0]}`
          .split('.')
          .concat(strArr.length > 1 ? ['起'] : []);
      },

      goodsPrice(store) {
        const { couponPrice, price } = store.goods;
        return formatPrice(couponPrice || price.join('.'));
      },
      showOriginPrice(store) {
        return formatPrice(store.goods.originPrice);
      },

      showGroupBargin(store) {
        const { groupInfo } = store;
        // 社群拼团且有数据展示
        const { groupId, groupType, drainageInfo } = groupInfo;
        return groupId && groupType === GROUP_BARGAIN_TYPE && drainageInfo;
      },

      showOfficialTag(store) {
        const { principalCertType } = store.shopConfig;
        return +principalCertType === 2 || +principalCertType === 5;
      },

      showBrandTag(store) {
        const { FLAGSHIP, EXCLUSIVE, BOUTIQUE, SHOPPING, CHAIN } =
          BRAND_CERT_TYPE;
        return [FLAGSHIP, EXCLUSIVE, BOUTIQUE, SHOPPING, CHAIN].includes(
          +store.shopConfig.brandCertType
        );
      },

      oldShopTagImg(store) {
        const { showOfficialTag, showBrandTag } = store;
        const { shopOperateDurationTagSwitch, shopOperateDurationYears } =
          store.shopConfig;
        return showOfficialTag && showBrandTag
          ? ''
          : +shopOperateDurationTagSwitch && +shopOperateDurationYears
          ? OLD_SHOP_YEARS[Math.min(+shopOperateDurationYears, 20)]
          : '';
      },
    });
  },
});
