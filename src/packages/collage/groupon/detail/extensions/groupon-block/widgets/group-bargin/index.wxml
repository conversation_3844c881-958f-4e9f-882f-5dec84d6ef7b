<view>
  <view bindtap="openDialog" class="goods-buttom-groupon-container">
    <view class="group-bargin-tip" wx:if="{{!drainageInfo.isPromotionEffect && drainageInfo.isDrainage}}">
      <view class="group-bargin-tip__title">
        <view class="group-bargin-tip__title-icon">
          <image src="{{avatarPlaceHolder}}" class="group-bargin-tip__title-img" />
        </view>
        <view class="group-bargin-tip__title-desc">
          <text>加好友领优惠，</text>
          <text class="group-bargin-tip__title-desc-num">额外再减{{ weComFriendDecrease }}元</text>
        </view>
      </view>
      <text class="group-bargin-tip__value">领取优惠</text>
    </view>
    <view class="group-bargin-tip" wx:if="{{!closeStorage && drainageInfo.isPromotionEffect}}">
      <view slot="title" class="group-bargin-tip__title">
        <view class="group-bargin-tip__title-icon">
          <image src="{{avatarPlaceHolder}}" class="group-bargin-tip__title-img" />
        </view>
        <view class="group-bargin-tip__title-desc">获得加好友优惠，可减{{ weComFriendDecrease }}元</view>
      </view>
      <view bindtap="setCloseStorage" class="group-bargin-tip__icon">
        <van-icon name="cross"></van-icon>
      </view>
    </view>
  </view>
  <van-popup
    position="bottom"
    show="{{showPop}}"
    class="qr-popup-wrap"
    round
  >
    <view class="qr-popup">
      <view class="qr-popup__title">
        <text>加好友领优惠，</text>
        <text class="qr-popup__title-num">
          额外再减
          <text class="price-bold">{{ weComFriendDecrease }}</text>
        元
        </text>
      </view>
      <view class="qr-popup__desc">优惠后拼团价¥{{ preferentialPrice }}</view>
      <view class="qr-popup__image_container">
        <image
          class="qr-popup__image"
          src="{{drainageInfo.weComDrainageQrCode}}"
          show-menu-by-longpress
        />
        <view class="qr-popup__tap">长按识别二维码</view>
      </view>
      <view bindtap="close" class='close-btn'>
        <van-icon name="cross"></van-icon>
      </view>
    </view>
  </van-popup>
</view>

