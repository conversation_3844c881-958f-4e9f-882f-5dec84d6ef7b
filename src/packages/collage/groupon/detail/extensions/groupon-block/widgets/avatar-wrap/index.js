import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import { logger } from '../../common/constants';

const app = getApp();

RantaWidget({
  attached() {
    mapState(this, ['from'], { setSelf: true });
    mapState(this, ['groupInfo']);
    mapState(this, {
      head(store) {
        const { participants, groupInfo } = store;
        return groupInfo.totalNum > 5 ? participants.slice(0, 3) : participants;
      },
      foot(store) {
        const { participants, groupInfo } = store;
        return groupInfo.totalNum > 5 ? participants.slice(-2) : [];
      },
    });

    mapActions(this, ['SET_MEMBER_LIST_FROM', 'TOGGLE_POPUP']);
  },
  methods: {
    handleClick() {
      if (!this.data.groupInfo.isJoinedGroup) {
        return;
      }
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'member_click', // 事件标识
          en: '团成员头像点击', // 事件名称
          params: {
            component: 'member',
            page_type: logger.pt(this.from),
          },
        });

      this.SET_MEMBER_LIST_FROM({ from: 'normal' });
      this.TOGGLE_POPUP({
        name: 'showMemberList',
        data: true,
      });
    },
  },
});
