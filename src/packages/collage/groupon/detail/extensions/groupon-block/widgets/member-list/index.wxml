<van-popup
  z-index="{{ 200 }}"
  show="{{ showMemberList }}"
  round
  position="bottom"
  class="member-list"
  closeable
  bind:close="togglePopup"
  custom-style="height: 460px;"
>
  <view class="rule-container">
    <view class="rule-header">{{ memberListFrom === 'member' ? '团长代收' : '查看我的团' }}</view>
    <view class="member-tip" wx:if="{{ memberListFrom === 'member' }}">你的包裹由团长代收</view>
    <scroll-view
      scroll-y
      class="member-scroll"
      bindscrolltolower="getList"
    >
      <block>
        <van-cell-group class="lists" border="{{ false }}">
          <van-cell
            wx:for="{{ displayList }}"
            wx:key="index"
            class="list"
            title-class="nickname"
            label-class="time"
          >
            <view slot="icon" class="list-img">
              <image class="img" src="{{ item.fansPicture }}" />
            </view>
            <view slot="label" class="custom-title">
              <text>{{item.payTime}} {{item.isHead ? '开' : '参'}}团</text>
            </view>
            <view slot="title">
              <view class="custom-main">
                <text class="custom-title">{{ item.fansNickName }}</text>
                <text wx:if="{{ item.isHead }}" class="tag">团长</text>
                <text wx:if="{{ !item.isHead && item.isAgencyReceive }}" class="tag-receiver">
                  团长代收
                </text>
              </view>
              <view class="custom-label">
                <text>{{item.payTime}} {{item.isHead ? '开' : '参'}}团</text>
              </view>
            </view>
            <view slot="right-icon" wx:if="{{ (item.isHead && !groupInfo.isHead && groupInfo.isChooseAgencyReceive) || (groupInfo.isHead && !item.isHead && item.isAgencyReceive)}}">
              <image
                wx:if="item.mobile"
                data-mobile="{{item.mobile}}"
                bindtap="call"
                class="tag-icon"
                src="https://img01.yzcdn.cn/upload_files/2021/08/13/Fs8a0eQn2hs1APaBAOo83Grwrzz5.png"
              />
              <image
                wx:else
                class="tag-icon disabled"
                src="https://img01.yzcdn.cn/upload_files/2021/08/13/FredPntL81R0qH8bV7Rt6tf49K3T.png"
              />
            </view>
          </van-cell>
        </van-cell-group>
      </block>
      <zan-loadmore wx:if="{{ loading }}" type="loading" />
    </scroll-view>
    <van-button custom-class="rule-btn" bind:click="togglePopup">知道了</van-button>
  </view>
</van-popup>

