<view wx:if="{{ show }}" class="groupon-going-list">
  <view class="groupon-going-list__header">以下团可直接参与</view>
  <view class="groupon-going-list__content">
    <view
      wx:for="{{ ongoingGroup }}"
      wx:for-item="group"
      wx:key="group.groupAlias"
      class="groupon-going-item"
    >
      <image src="{{ group.avatar }}" class="groupon-going-item__img" />
      <view class="groupon-going-item__content">
        <view class="groupon-going-item__title">{{ group.nickName }}</view>
        <view class="groupon-going-item__desc">
          仅剩
          <text class="groupon-going-item__gap-num">{{ group.remainJoinNum }}</text>
          个名额，
          <count-down
            cls="groupon-going-item-countdown"
            font-width="14"
            time="{{ group.remainTime * 1000 }}"
          />
          后结束
        </view>
      </view>
      <subscribe-message
        scene="together_group_purchase"
        subscribe-scene="groupon"
        data-alias="{{  group.groupAlias }}"
        bind:next="handleClick"
        data-type="joinOther"
        btn-class="groupon-going-item__button"
      >
        去参团
      </subscribe-message>
    </view>
  </view>
</view>

