import { RantaWidget } from 'shared/common/base/ranta/widget';
import { getHeight } from 'shared/utils/nav-config';
import { timerBehaivor } from '@goods/common/behaviors';
import { mapState, mapActions } from '@ranta/store';
import { checkIsChannels } from 'shared/utils/channel';

const topBarHeight = getHeight() || 0;
// TODO 存在 async，以及内存泄露
RantaWidget({
  behaviors: [timerBehaivor],

  data: {
    tradeRecords: [],
    // 是否是视频号场景
    isChannels: checkIsChannels(),
  },

  observers: {
    tradeRecordsV2(newVal) {
      // 绕过重复快速赋值导致 setYZData 逻辑bug
      setTimeout(() => {
        this.startAnimation(newVal);
      }, 20);
    },
  },

  attached() {
    mapState(this, ['tradeRecordsV2']);
    mapState(this, {
      topBarHeight(store) {
        return store.tradeCarousel.fixedTop + topBarHeight;
      },
      visible: (store) => store.tradeCarousel.visible,
    });

    mapActions(this, ['fetchTradeRecords']);
  },

  ready() {
    this.fetchTradeRecords && this.fetchTradeRecords();
  },

  detached() {
    clearTimeout(this.tid);
  },

  methods: {
    startAnimation(tradeRecordsV2 = []) {
      const { length: tradeRecordLength } = tradeRecordsV2;

      if (!tradeRecordLength) {
        return;
      }

      const start = (index, tradeRecordLength, isInit) => {
        const nextIndex = index === tradeRecordLength - 1 ? 0 : index + 1;

        // 初始化动画参数，必须每次都清除一下
        const tradeRecords = tradeRecordsV2.map((tradeRecord) => {
          tradeRecord.showEnter = false;
          tradeRecord.showLeave = false;
          return tradeRecord;
        });

        // 进入的时候把动画状态都清除掉；vue会自己做；
        this.setYZData(
          {
            __isAnimation: true,
            tradeRecords,
          },
          () => {
            this.move(tradeRecords, index, isInit, () => {
              start(nextIndex, tradeRecordLength);
            });
          }
        );
      };

      const startIndex = 0;
      start(startIndex, tradeRecordLength, true);
    },

    move(tradeRecords, index, isInit, next) {
      const INIT_INTERVAL = 0;
      const ENTRY_INTERVAL = 500;
      const WAIT_INTERVAL = 1000;
      const LEAVE_INTERVAL = 500;
      const entryInterval = isInit ? INIT_INTERVAL : ENTRY_INTERVAL;

      this.timeoutPromise(
        this.moveIn.bind(this, { tradeRecords, index }),
        entryInterval
      )
        .then(() => {
          return this.timeoutPromise(
            this.moveOut.bind(this, { tradeRecords, index }),
            WAIT_INTERVAL
          );
        })
        .then(() => {
          this.timeoutPromise(next, LEAVE_INTERVAL);
        });
    },

    timeoutPromise(fn, interval) {
      return new Promise((resolve) => {
        this.tid = setTimeout(() => {
          fn();
          resolve();
        }, interval);
      });
    },

    moveIn({ tradeRecords, index }) {
      const tradeRecord = tradeRecords[index];
      tradeRecord.showEnter = true;
      tradeRecord.showLeave = false;
      this.setYZData({
        __isAnimation: true,
        [`tradeRecords[${index}]`]: tradeRecord,
      });
    },

    moveOut({ tradeRecords, index }) {
      const tradeRecord = tradeRecords[index];
      tradeRecord.showEnter = false;
      tradeRecord.showLeave = true;
      this.setYZData({
        __isAnimation: true,
        [`tradeRecords[${index}]`]: tradeRecord,
        // tradeRecords: tradeRecordsV2
      });
    },
  },
});
