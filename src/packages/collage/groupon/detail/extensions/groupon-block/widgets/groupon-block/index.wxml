<view class="background {{ groupInfo.isJoinedGroup ? 'short-background' : ''}} {{ from === 'pay' ? 'pay-background' : ''}}" />

<theme-provider style="position: relative">
  <view
    wx:if="{{ ready }}"
    class="container"
    style="{{ topBarStyle }}"
  >
    <trade-carousel wx:if="{{ from !== 'pay' }}" />

    <!-- 商品信息 -->
    <goods-wrap wx:if="{{ !groupInfo.isJoinedGroup }}" goods="{{ goods }}" />
    <van-divider wx:if="{{ !groupInfo.isJoinedGroup }}" custom-style="margin: 0 16px;transform: scaleY(.5);" />
    <!-- 会员权益 -->
    <membercard-rights />
    <!-- 主信息 -->
    <view class="status-wrap">
      <view wx:for="{{ curComps }}">
        <status-title wx:if="{{ item === 'status-title' }}" />
        <btns-wrap wx:if="{{ item === 'btns-wrap' }}" />
        <avatar-wrap wx:if="{{ item === 'avatar-wrap' }}" />
      </view>
    </view>
    <van-divider custom-style="margin: 0 16px;transform: scaleY(.5);" />
    <order-block/>
    <header-receive-block/>
    <rules-block/>
  </view>
  <!-- 裂变券 -->
  <fission-tips wx:if="{{ from ==='pay' }}" />

  <van-dialog id="van-dialog" />
  <!-- sku -->
  <groupon-sku/>
  <!-- 分销员 -->
  <salesman-cube page-query="{{ shareQuery }}" need-bind-relation="{{false}}" />
  <!-- 邀请好友 -->
  <share-poster/>
  <!-- 参团成员 -->
  <member-list/>
</theme-provider>

