<view class="root">
  <!-- 商品信息 -->
  <view class="good-box">
    <navigator
      url="{{ url }}"
      hover-class="none"
      class="goods-wrap"
    >
      <!-- 图片 -->
      <van-image
        src="{{ goods.picture }}"
        width="120"
        height="120"
        fit="contain"
        class="goods-wrap__img"
      />
      <!-- 标题 -->
      <view class="goods-wrap__detail">
        <view>
          <view class="shop-tags">
            <icon wx:if="{{ showBrandTag }}" class="shop-tags__tag-brand" />
            <!-- 企业标 -->
            <icon wx:if="{{ showOfficialTag }}" class="shop-tags__tag-official" />
            <image
              wx:if="{{ oldShopTagImg }}"
              src="{{ oldShopTagImg }}"
              class="shop-tags__old-shop"
            />
          </view>
          <view class="detail__title van-multi-ellipsis--l2">{{ goods.title }}</view>
          <view wx:if="{{goods.tagList}}" class="detail__good-tags">
            <text class="detail__good-tag {{ item === '包邮' ? 'detail__good-tag-red': ''}}" wx:for="{{ goods.tagList }}" wx:key="item">
              {{ item }}
            </text>
          </view>
        </view>
        <!-- 价格 -->
        <view class="detail-price">
          <view class="detail-price__current">
            <text class="detail-price__current-tag">拼团{{ goods.couponPrice ? '券后' : '' }}价</text>
            <view class="detail-price__current-price">
              <text class="detail-price__current-price-unit">￥</text>
              <text class="detail-price__current-price-number">{{ showPrice[0] }}</text>
              <text class="detail-price__current-price-number" wx:if="{{ showPrice[1] && showPrice[1] !== '起' }}">
                .{{ showPrice[1] }}
              </text>
              <text class="detail-price__current-price-text" wx:if="{{ showPrice[showPrice.length - 1] === '起' }}">
                起
              </text>
            </view>
          </view>
          <view class="detail-price__original">￥{{ showOriginPrice }}</view>
        </view>
      </view>
    </navigator>
    <group-bargin
      wx:if="{{ showGroupBargin }}"
      picture="{{ goods.picture }}"
      price="{{ goodsPrice }}"
    />
  </view>
</view>

