<view class="btn-wrap">
  <view
    wx:for="{{ btns }}"
    wx:for-item="btn"
    wx:for-index="index"
    wx:key="type"
    type="ump-groupon"
  >
    <user-authorize
      wx:if="{{ btn.type === 'invite' }}"
      authTypeList="{{ ['nicknameAndAvatar'] }}"
      is-once
      data-type="{{ btn.type }}"
      bindgetuserinfo="onGetUserInfo"
      bindtap="handleClick"
      btn-class="{{ index === 0 ? 'btn-wrap__top' : 'btn-wrap__bottom' }}"
    >
      {{ btn.text }}
    </user-authorize>
    <subscribe-message
      wx:key="index"
      wx:elif="{{ btn.type === 'join' || btn.type === 'reopening' }}"
      scene="together_group_purchase"
      subscribe-scene="groupon"
      data-type="{{ btn.type }}"
      bind:next="handleClick"
      btn-class="{{ index === 0 ? 'btn-wrap__top' : 'btn-wrap__bottom' }}"
    >
      {{ btn.text }}
    </subscribe-message>
    <view wx:elif="{{ btn.type === 'joinOther' }}">
      <going-list bind:next="handleClick" />
    </view>
    <van-button
      wx:else
      wx:key="index"
      hover-class="none"
      data-type="{{ btn.type }}"
      plain="{{ index === 1 }}"
      custom-class="{{ index === 0 ? 'btn-wrap__top' : 'btn-wrap__bottom' }}"
      bind:click="handleClick"
    >
      {{ btn.text }}
    </van-button>
  </view>
</view>

