import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';
import { baseImgUrl } from '../../common/constants';

RantaWidget({
  data: {
    popShow: false,
    qaContent: [],
  },
  attached() {
    mapState(this, {
      qaContent(store) {
        const qaContent = [
          {
            q: '什么是多人拼团？',
            a: '多人拼团（以下简称：拼团）是指由多人一起拼单购买的团购活动，通过拼团买家可以享受比一般网购更低的折扣。',
          },
          {
            q: '怎样算拼团成功？',
            a: '在拼团有效期内找到满足人数的用户参加拼团，即可算拼团成功。',
          },
          {
            q: '拼团失败，怎样退款？',
            a: '若拼团有效期内没有凑齐人数，即算作拼团失败。系统会自动将所支付的货款原路退回，具体到账时间以各银行为准。',
          },
        ];
        // 阶梯团
        if (store.isLadder) {
          qaContent[0] = {
            q: '什么是阶梯拼团？',
            a: '阶梯拼团（以下简称：拼团）是指在团长发起拼团时，选择指定的参团人数，邀请对应数量好友拼单购买的团购活动，参团人数越多，价格折扣越低。',
          };
        }

        // 老带新拼团，增加一条规则
        if (store.isOld2New) {
          qaContent.push({
            q: '怎么算作新用户？',
            a: '若用户未在店铺内有进行中的订单或已成交的订单则视为新用户。',
          });
        }
        return qaContent;
      },
      qaImage(store) {
        return store.isOld2New
          ? `${baseImgUrl}2021/07/05/FrjRmg-OkxVIVRAwecx4aAatY_cG.png` // eslint-disable-line
          : `${baseImgUrl}2021/07/05/FhhyamtrA1brzM8wdbW3Eu1SfF9D.png`; // eslint-disable-line
      },
    });
  },
  methods: {
    togglePopup() {
      this.setYZData({
        popShow: !this.data.popShow,
      });
    },
  },
});
