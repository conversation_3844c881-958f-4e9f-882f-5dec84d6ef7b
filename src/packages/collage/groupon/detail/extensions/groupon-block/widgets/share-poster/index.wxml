<van-popup
  custom-class="share-popup"
  z-index="{{ 200 }}"
  show="{{ isShowInvitePost }}"
  bind:click-overlay="closeInvitePostModal"
>
  <view
    wx:if="{{ !isMemberCard }}"
    bindtap="closeInvitePostModal"
    class="share-popup-close"
  >
    <van-icon name="close"></van-icon>
  </view>
  <view class="share-popup-content {{ isMemberCard ? 'share-popup-content__long' : '' }}">
    <image class="share-popup-content__img" src="{{ shareImgSrc }}"></image>
  </view>
  <van-button
    wx:if="{{ isMemberCard }}"
    type="danger"
    custom-class="share-btn"
    bindtap="clickSaveImage"
  >保存图片
  </van-button>
  <view
    wx:else
    class="share-btn--bottom"
    bind:tap="clickSaveImage"
  >保存图片
  </view>
</van-popup>
<van-share-sheet
  z-index="{{ 200 }}"
  show="{{ showInvitePopup }}"
  options="{{ actions }}"
  bind:select="onSelect"
  bind:close="onClose"
>
  <view slot="title">
    <text wx:if="{{ shareTime === 0 }}" class="share-title">
        还差 <text class="title-strong">{{ groupInfo.gapNum }}</text>
    人成团，快邀请好友一起拼
    </text>
    <text class="share-title" wx:else>分享到微信群，成团率更高</text>
  </view>
  <view slot="description">
    <view class="description-countdown">仅剩
      <count-down
        cls="description-countdown-text"
        font-width="16"
        time="{{ groupInfo.remindTime * 1000 }}"
      />
    </view>
  </view>
</van-share-sheet>

