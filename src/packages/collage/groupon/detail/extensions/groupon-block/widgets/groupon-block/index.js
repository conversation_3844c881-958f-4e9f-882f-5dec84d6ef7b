import { mapState } from '@ranta/store';
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { getHeight } from 'shared/utils/nav-config';

const topBarHeight = getHeight() || 0;

RantaWidget({
  data: {
    curComps: [],
  },

  attached() {
    mapState(this, [
      'ready',
      'goods',
      'groupInfo',
      'from',
      'isOngoing',
      'isJoinedGroup',
      'shareQuery',
    ]);
    mapState(this, {
      topBarStyle(store) {
        const { from } = store;
        return `margin-top: ${from === 'pay' ? 12 : topBarHeight + 56}px`;
      },
    });

    const { goods, groupInfo } = this;

    this.setCollageDate(goods, groupInfo);
  },

  observers: {
    'goods, groupInfo': function (goods, groupInfo) {
      this.setCollageDate(goods, groupInfo);
    },

    ready(val) {
      if (val) {
        this.setCurComps();
        this.ctx.event.emit('ready');
      }
    },
  },

  methods: {
    setCollageDate(goods = {}, groupInfo = {}) {
      const { goodsId = '' } = goods;
      const { gapNum = '' } = groupInfo;
      if (this.ctx) {
        this.ctx.data.getCollageDate = {
          goodsId,
          gapNum,
        };
      }
    },

    setCurComps() {
      const { groupInfo, isOngoing } = this.data;
      const { isJoinedGroup } = groupInfo;
      let curComps = [];
      if (!isJoinedGroup) {
        curComps = ['avatar-wrap', 'status-title', 'btns-wrap'];
      } else if (isOngoing && isJoinedGroup) {
        curComps = ['status-title', 'btns-wrap', 'avatar-wrap'];
      } else {
        curComps = ['status-title', 'avatar-wrap', 'btns-wrap'];
      }

      this.setData({
        curComps,
      });
    },
  },
});
