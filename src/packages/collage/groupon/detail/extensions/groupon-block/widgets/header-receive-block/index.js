import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';

RantaWidget({
  data: {
    showPopup: false,
  },

  attached() {
    mapState(this, ['groupInfo', 'isOngoing'], { setSelf: true });
    mapState(this, {
      title(store) {
        // receiveStatus 团代收状态。0、不代收；1、可选代收；2、强制代收
        const { groupInfo } = store;
        const { receiveStatus, isHead } = groupInfo;
        if (isHead) {
          return receiveStatus === 1
            ? '若团员选择团长代收，团员商品将发货给你'
            : '团员购买的商品由团长代收';
        }
        return '你的包裹由团长代收';
      },
      enable(store) {
        const { groupInfo } = store;
        const { receiveStatus, isJoinedGroup, isChooseAgencyReceive, isHead } =
          groupInfo;
        return (
          receiveStatus && isJoinedGroup && (isChooseAgencyReceive || isHead)
        );
      },
    });

    mapActions(this, ['SET_MEMBER_LIST_FROM', 'TOGGLE_POPUP']);
  },

  methods: {
    togglePopup() {
      const { isHead, isChooseAgencyReceive } = this.groupInfo;

      if (this.isOngoing && isHead) {
        this.setYZData({
          showPopup: !this.data.showPopup,
        });
      } else {
        this.SET_MEMBER_LIST_FROM({
          from: !isHead && isChooseAgencyReceive ? 'member' : 'normal',
        });
        this.TOGGLE_POPUP({
          name: 'showMemberList',
          data: true,
        });
      }
    },
  },
});
