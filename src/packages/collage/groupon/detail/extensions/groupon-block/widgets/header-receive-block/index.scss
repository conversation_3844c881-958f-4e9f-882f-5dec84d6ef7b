.header-popup {
  border-radius: 20px 20px 0 0;
  .rule-container {
    height: 100%;
    box-sizing: border-box;
    color: #323233;
  }

  .rule-header {
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    text-align: center;
    position: relative;
    font-weight: 500;
  }

  .rule-info {
    padding: 12px 16px;
    font-size: 15px;
    line-height: 24px;
    min-height: 50vh;
    max-height: calc(80vh - 68px);
    overflow: scroll;
    &__section {
      display: flex;
      flex-direction: row;
      margin-bottom: 22px;
    }
    &__icon {
      width: 18px;
      height: 18px;
      margin-right: 10px;
      line-height: 22px;
      margin-top: 3px;
    }
    .popup-title {
      color: #323233;
      font-size: 16px;
    }
    .popup-content {
      color: #646566;
      font-size: 14px;
      font-weight: normal;
    }
  }

  .rule-btn {
    position: fixed;
    left: 16px;
    right: 16px;
    bottom: 8px;
    bottom: calc(8px + constant(safe-area-inset-bottom));
    bottom: calc(8px + env(safe-area-inset-bottom));
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    background-image: linear-gradient(90deg, #ff6034 0%, #ee0a24 100%);
    border-radius: 100px;
    color: #fff;
    text-align: center;
  }
}

.base-cell {
  &::after {
    display: none;
  }
  &::before {
    border: 1px;
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 16px;
    top: 0;
    left: 16px;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(0.5);
  }
  .van-cell__right-icon-wrap {
    color: #c8c9cc;
  }
}
.base-block__label {
  font-size: 13px;
  font-weight: normal;
  color: #323233;
  line-height: 18px;
  margin-right: 8px;
}
.base-block__title {
  font-size: 13px;
  font-weight: normal;
  color: #969799;
  line-height: 16px;
}
