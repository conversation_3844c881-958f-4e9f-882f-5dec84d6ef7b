@import '~shared/common/css/_variables.scss';

.groupon-going-list {
  background: #f7f7f7;
  margin: 0 12px;
  padding: 16px 10px 24px;
  border-radius: 8px;
  text-align: left;
  &__header {
    box-sizing: border-box;
    font-size: 14px;
    margin-bottom: 24px;
  }
  .groupon-going-item {
    display: flex;
    align-items: center;

    &::after {
      display: none;
    }

    &__desc {
      font-size: 12px;
      font-weight: normal;
      color: #505052;
      letter-spacing: 0;
      text-align: center;
      line-height: 20px;
      display: flex;
      align-items: center;
    }

    &__img {
      margin-right: 8px;
      width: 34px;
      height: 34px;
      border-radius: 50%;
    }

    &__content {
      flex-grow: 1;
      font-size: 12px;
      color: $gray-dark;
    }

    &__gap-num {
      color: #f44;
    }

    &__title {
      line-height: 16px;
      color: #969799;
    }

    &__button {
      width: 64px;
      height: 24px;
      background: #f01626;
      border-radius: 15px;
      font-size: 12px;
      color: #fff;
      text-align: center;
      line-height: 24px;
      padding: 0;
      border: 0;
    }
  }
}

@media screen and (max-width: 320px) {
  .groupon-going-item {
    &__desc {
      font-size: 10px;
    }

    .cap-countdown {
      &__day,
      &__hour,
      &__minute,
      &__second,
      &__time-text {
        font-size: 10px;
      }
    }
  }
}
