.container {
  margin: 0 12px;
  background: #fff;
  border-radius: 8px;
  font-weight: 500;
  overflow: hidden;
  min-height: auto;
}

.status-wrap {
  display: flex;
  flex-direction: column;
  padding-top: 24px;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: #f8f8f8 url(https://img01.yzcdn.cn/upload_files/2021/08/10/FlWu4zn6hVhWgpuxZ0xa0N9y2_sv.png) 0 0/contain no-repeat;

  &.pay-background {
    background: #f8f8f8;
    background-image: none !important;
    // padding-top: 12px;
  }

  &.short-background {
    background-image: url(https://img01.yzcdn.cn/upload_files/2021/06/30/FowVR_aFXDqDAP51W09ULCB2FRcw.png);
  }
}

.divide {
  border-top: 1px solid #dcdee0;
}