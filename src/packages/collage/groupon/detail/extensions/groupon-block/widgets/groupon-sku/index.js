import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import getApp from 'shared/utils/get-safe-app';
import { ACTIVITY_TYPE_MAP } from '../../common/constants';
import { receiveCoupon } from '../../common/api';
import { skuTradeUtilsPromise } from 'shared/utils/async-base-sku';

const app = getApp();

RantaWidget({
  attached() {
    mapState(this, [
      'sku',
      'goods',
      'groupInfo',
      'showSkuModal',
      'createGroupon',
      'extraData',
    ]);
    mapState(this, ['joinOther', 'joinOtherAlias'], { setSelf: true });

    mapActions(this, ['TOGGLE_POPUP', 'SET_SKU_EXTRA_DATA']);
  },
  methods: {
    // 选择sku
    onSkuSelected(e) {
      const { goods } = this.data;
      const { selectedSkuComb } = e.detail;
      let { actPrice, actCouponPrice } = this.data.extraData;
      if (selectedSkuComb) {
        actPrice = goods.skuInfo[selectedSkuComb.id] / 100;
        actPrice = actPrice.toFixed(2);
        const skuPrice = goods.skuPrices[selectedSkuComb.id];
        actCouponPrice = skuPrice.couponActivityId
          ? (skuPrice.unitPromotionPrice / 100).toFixed(2)
          : null;
      } else {
        actPrice = goods.skuPrice;
        actCouponPrice = goods.couponPrice;
      }

      this.SET_SKU_EXTRA_DATA({
        name: 'skuPrice',
        data: actPrice,
      });
      this.SET_SKU_EXTRA_DATA({
        name: 'skuCouponPrice',
        data: actCouponPrice,
      });
    },

    onSkuClose() {
      this.TOGGLE_POPUP({
        name: 'showSkuModal',
        data: false,
      });
    },

    onBuy(e) {
      const kdtId = app.getKdtId();
      const {
        messages: message,
        selectedNum: num,
        selectedSkuValues: skuName,
        selectedSkuComb: { id: skuId, price, properties = [] },
      } = e.detail;
      const { createGroupon, goods, groupInfo } = this.data;
      const { goodsId } = goods;
      const { activityId, groupAlias, activityType, totalNum } = groupInfo;
      const activityAlias = this.joinOther ? this.joinOtherAlias : groupAlias;

      const goodsList = [
        {
          kdtId,
          goodsId,
          skuId,
          num,
          price: Number(price),
          ...message,
          skuName,
          bizTracePointExt: '',
          activityAlias: createGroupon ? '' : activityAlias,
          activityType,
          activityId: Number(activityId),
        },
      ];

      // 处理商品属性和商品加料的数据
      if (properties.length) {
        const propertyIds = properties.reduce((propertyIds, propModel) => {
          const { v = [] } = propModel;
          v.forEach((item) => {
            propertyIds.push(item.id);
          });
          return propertyIds;
        }, []);
        goodsList[0].propertyIds = propertyIds;
      }

      const extraData = {
        kdtId,
        isGroupon: true,
        activityAlias: createGroupon ? '' : activityAlias,
        createGroupon,
        activityType,
        activityId: Number(activityId),
        umpStepPerson: totalNum,
      };

      // 阶梯团增加团类型参数
      if (activityType === ACTIVITY_TYPE_MAP.LADDER) {
        extraData.umpStepPerson = totalNum;
      }
      return new Promise((resolve) => {
        const { skuPrices } = goods;
        const couponId = skuPrices[skuId]
          ? skuPrices[skuId].couponActivityId || ''
          : '';

        if (couponId) {
          receiveCoupon({ couponId })
            .then(() => {
              extraData.preToastDesc = '你已领取推荐优惠券，下单享优惠';
              return resolve();
            })
            .catch((err = { msg: '领取失败' }) => {
              const { msg } = err;
              extraData.preToastDesc = msg;
              return resolve();
            });
        } else {
          return resolve();
        }
      }).then(() => {
        skuTradeUtilsPromise().then(({ goToBuy }) => {
          goToBuy({
            goodsList,
            extraData,
          });
        });
      });
    },
  },
});
