import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import { getGroupJoinedRecord } from '../../common/api';
import fullfillImage from '@youzan/weapp-utils/lib/cdn-image';
import { logger, baseImgUrl } from '../../common/constants';

const app = getApp();
const defaultAvatar = `${baseImgUrl}2018/09/07/af3aa756277cb619b9eb6062ec46cf57.png`; // eslint-disable-line

const initData = {
  loading: false,
  finished: false,
  lists: [],
  displayList: [],
  pagination: {
    page: 1,
    pageSize: 10,
    totalCount: 0,
  },
};

RantaWidget({
  data: { ...initData },

  observers: {
    showMemberList(value) {
      if (value) {
        this.setYZData(
          {
            ...initData,
          },
          () => {
            this.getList();
          }
        );
        app.logger &&
          app.logger.log({
            et: 'view', // 事件类型
            ei: 'member', // 事件标识
            en: '团成员组件曝光', // 事件名称
            params: {
              page_type: logger.pt(this.from),
              component: 'member',
            }, // 事件参数
          });
      }
    },
    'lists, memberListFrom': function () {
      this.setDisplayList();
    },
  },

  attached() {
    mapState(this, ['groupInfo', 'memberListFrom', 'showMemberList']);
    mapState(this, ['from'], { setSelf: true });

    mapActions(this, ['TOGGLE_POPUP']);
  },

  ready() {
    this.getList();
  },

  methods: {
    getList() {
      const { page, pageSize } = this.data.pagination;
      const { activityType, groupId, groupAlias } = this.data.groupInfo;
      getGroupJoinedRecord({
        pageNo: page,
        pageSize,
        groupId,
        kdtId: app.getKdtId(),
        umpType: activityType,
        groupAlias,
      })
        .then((res) => {
          const data = {
            pagination: res.paginator,
          };
          if (res && Array.isArray(res.items)) {
            res.items.forEach((item) => {
              item.fansPicture = fullfillImage(
                item.fansPicture || defaultAvatar,
                '!200x0.jpg'
              );
            });
            data.lists = this.data.lists.concat(res.items);
          }
          // 全部加载完
          if (
            data.lists.length >= data.pagination.totalCount ||
            !res.items.length
          ) {
            data.finished = true;
          } else {
            data.pagination.page += 1;
          }

          data.loading = false;
          this.setYZData({
            ...data,
          });
        })
        .catch((err) => {
          // eslint-disable-next-line
          return wx.showToast({
            icon: 'none',
            title: err,
          });
        });
    },
    setDisplayList() {
      const { lists, memberListFrom } = this.data;
      if (memberListFrom === 'member' && lists && lists.length > 0) {
        this.setYZData(
          {
            displayList: [lists[0]],
          },
          {
            immediate: true,
          }
        );
      } else {
        this.setYZData(
          {
            displayList: lists,
          },
          {
            immediate: true,
          }
        );
      }
    },
    togglePopup() {
      this.TOGGLE_POPUP({
        name: 'showMemberList',
        data: false,
      });
    },
    call(e) {
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'call_click', // 事件标识
          en: '团成员打电话按钮点击', // 事件名称
          params: {
            component: 'member',
            page_type: logger.pt(this.from),
          },
        });

      const {
        target: {
          dataset: { mobile },
        },
      } = e;
      // eslint-disable-next-line
      wx.makePhoneCall({
        phoneNumber: mobile,
      });
    },
  },
});
