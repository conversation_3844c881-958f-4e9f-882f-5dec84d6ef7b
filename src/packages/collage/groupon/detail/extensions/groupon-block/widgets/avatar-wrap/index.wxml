<van-cell
  bind:click="handleClick"
  center
  border="{{ false }}"
  custom-style="padding: 0"
>
  <view class="avatar-list-wrap">
    <view
      wx:for="{{ head }}"
      wx:for-item="item"
      wx:key="index"
      class="avatar-list-wrap-item {{ groupInfo.totalNum > 5 ? 'avatar-list-wrap-item-compact' : '' }}"
      style="zIndex: {{ 6 - index }}"
    >
      <image class="item__img" src="{{ item.profilePicture }}" />
      <view wx:if="{{ item.head }}" class="item__tag">团长</view>
    </view>
    <view wx:if="{{ groupInfo.totalNum > 5 }}" class="avatar-list-wrap-item-ellipsis">...</view>
    <view
      wx:for="{{ foot }}"
      wx:for-item="item"
      wx:key="index"
      class="avatar-list-wrap-item {{ groupInfo.totalNum > 5 ? 'avatar-list-wrap-item-compact' : '' }}"
      style="zIndex: {{ 6 - index }}"
    >
      <image class="item__img" src="{{ item.profilePicture }}" />
      <view wx:if="{{ item.head }}" class="item__tag">团长</view>
    </view>
  </view>
</van-cell>

