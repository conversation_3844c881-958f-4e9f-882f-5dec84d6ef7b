/* eslint-disable @youzan-open/tee/no-platform-field */
import navigate from 'shared/utils/navigate';
import spm from 'utils/log/spm';

const app = getApp();

export const memberCardGoodsType = 20;

export const logger = {
  // group_status	: 0进行中、1已成团、2拼团失败、3活动结束
  grouponStatus({ isGroupOnSuccess, isGroupOnFailed, isEnd }) {
    if (isGroupOnSuccess) {
      return 1;
    }

    if (isGroupOnFailed) {
      return 2;
    }

    if (isEnd) {
      return 3;
    }

    return 0;
  },
  pt(from) {
    return from === 'pay' ? 'grouponpaysuccess' : 'grouponactivity';
  },
  spm: spm.getSpm(),
};

// 拼团类型
export const ACTIVITY_TYPE_MAP = {
  COMMON: 4, // 普通拼团
  LADDER: 26, // 阶梯团
  MUTLI_GOODS: 27, // 伙拼团
};

export const GROUP_TYPE_MAP = {
  COMMON: 0,
  // 老带新
  OLD_TO_NEW: 1,
};

// 品牌认证类型 0 初始化状态 1 旗舰店 2专卖店 3直营店 4 专营店 5卖场 99 认证被驳回
export const BRAND_CERT_TYPE = {
  // 初始化
  INIT: 0,
  // 旗舰店
  FLAGSHIP: 1,
  // 专卖店
  EXCLUSIVE: 2,
  // 直营店
  CHAIN: 3,
  // 专营店
  BOUTIQUE: 4,
  // 卖场
  SHOPPING: 5,
  // 认证被驳回
  REJECT: 99,
};

export const baseImgUrl = 'https://img01.yzcdn.cn/upload_files/'; // eslint-disable-line

const imgUrl1 = `${baseImgUrl}2020/08/25/`;
const imgUrl2 = `${baseImgUrl}2020/08/27/`; // eslint-disable-line

// 有赞老店图标
export const OLD_SHOP_YEARS = {
  1: `${imgUrl1}FqGqJTRVrZ_t-DIjkN103t_GYWXf.png`,
  2: `${imgUrl1}FmDJUzJkUuKB3UwC0be5Xpj1ledS.png`,
  3: `${imgUrl2}FpsAH_9LW0pRKNVBRYx6Y-o6QhEM.png`,
  4: `${imgUrl2}Fo8sZ6LghvQMB0fmQ8Yr2iLIBL1p.png`,
  5: `${imgUrl2}FvnxLlzcnAIOcvgYXWiu5-TUftWX.png`,
  6: `${imgUrl2}FgqZf8rRXaomgQPenEuOlAPyxvv5.png`,
  7: `${imgUrl2}Fm0I9Fc1I9NhlYQjT1lXfZNtcMNj.png`,
  8: `${imgUrl2}Fl-yluz33ycUrvCLyEJ6ASshMFDh.png`,
  9: `${imgUrl2}Fne4AL_YwZVqwOQ9-X6M6I07xjsF.png`,
  10: `${imgUrl2}FmQIOCBjSHAYufz1L9eqkJ_tlpNC.png`,
  11: `${imgUrl2}Fph7JfAuZ9A3lsmVdXz2nOvBJDQM.png`,
  12: `${imgUrl2}Fl7NnqZ7SMKEix_zRfhEGRZQnGNm.png`,
  13: `${imgUrl2}FhOdpvMHvxA0RvsTuHOIp6gVDiQ8.png`,
  14: `${imgUrl2}FrulqKThR2U3CdVHe2DUPDyEFNPC.png`,
  15: `${imgUrl2}Fr2rO6GNL8uTIl40A6Yd08vFDHcl.png`,
  16: `${imgUrl2}FtAGe37MzCyS1GtpyuBhRX4g7vRp.png`,
  17: `${imgUrl2}FvMedsxOQycRmqC32egg9kUwhAXD.png`,
  18: `${imgUrl2}FqLp8e1W0viAaqk8SuPUurKqCavP.png`,
  19: `${imgUrl2}FpQV9M0xOv_aROZ8EemqW1CNTqxs.png`,
  20: `${imgUrl2}FlkGwdaGTLhlg_mzcRQllOMMvBL0.png`,
};

export const SOLDSTATUS = {
  SELLING: 1,
  SOLDOUT: 2,
  PARTSOLDOUT: 3,
};

export const SWITCH = {
  on: 1,
  off: 0,
};

export const COUPON_TAKE_BIZNAME = 'goods_details_auto_take';

/**
 * BUTTONS_DATA 活动页按钮枚举
 * @param type
 * @param text
 * @param checkEnable 判断按钮是否渲染
 * @param action 点击按钮后的事件, this指向btns-wrap 可使用btns-wrap变量与函数
 *
 */

export const BUTTONS_DATA = {
  buy: {
    type: 'buy',
    text: '单独购买',
    checkEnable: (groupInfo, goods) => {
      const {
        isEnd,
        isGroupOnSuccess,
        isGroupOnFailed,
        isJoinedGroup,
        hasJoinedGroup,
        isAllowOpenGroup,
      } = groupInfo;
      const { soldStatus } = goods;
      if (isGroupOnSuccess && isJoinedGroup) {
        return false;
      }
      if (isEnd || (isGroupOnSuccess && !isJoinedGroup) || isGroupOnFailed) {
        if (
          soldStatus !== SOLDSTATUS.SOLDOUT &&
          !hasJoinedGroup &&
          !isAllowOpenGroup
        ) {
          return true;
        }
      }
      return false;
    },
    action() {
      navigate.navigate({
        url: `/packages/goods/groupon/index?alias=${this.goods.alias}`,
      });
    },
  },
  invite: {
    type: 'invite',
    text: '邀请好友拼团',
    checkEnable: (groupInfo) => {
      const { isOngoing, isJoinedGroup } = groupInfo;
      if (isOngoing && isJoinedGroup) {
        return true;
      }
      return false;
    },
    action() {
      const { groupInfo } = this;
      this.TOGGLE_POPUP({
        name: 'showInvitePopup',
        data: true,
      });
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'click_share', // 事件标识
          en: '点击邀请好友', // 事件名称
          pt: 'groupon', // 页面类型
          params: {
            act_id: groupInfo.activityId,
            page_type: logger.pt(this.from), // 页面类型
          },
        });
    },
  },
  join: {
    type: 'join',
    text: '立即参团',
    checkEnable: (groupInfo) => {
      const { isOngoing, isJoinedGroup } = groupInfo;
      if (isOngoing && !isJoinedGroup) {
        return true;
      }
      return false;
    },
    action() {
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'join_click', // 事件标识
          en: '立即参团按钮点击', // 事件名称
          pt: 'groupon', // 页面类型
          params: {
            page_type: logger.pt(this.from),
          },
        });
      this.createGroupon = false;
      this.SET_IS_CREATE_GROUPON(false);
      this.SET_IS_JOIN_OTHER_GROUPON({ joinOther: false, alias: null });

      // 如果是 老带新 拼团类型
      if (this.isOld2New) {
        this.resolveNewCustomerGroupon({
          callback: () => this.fetchGoodsDetail(),
        });
      } else {
        this.fetchGoodsDetail();
      }
    },
  },
  joinOther: {
    type: 'joinOther',
    text: '',
    checkEnable: (groupInfo) => {
      const {
        isEnd,
        isGroupOnSuccess,
        isGroupOnFailed,
        isJoinedGroup,
        hasJoinedGroup,
      } = groupInfo;

      if (
        !isEnd &&
        ((isGroupOnSuccess && !isJoinedGroup) || isGroupOnFailed) &&
        hasJoinedGroup
      ) {
        return true;
      }
      return false;
    },
    action(e) {
      const { alias } = e.detail;
      this.SET_IS_CREATE_GROUPON(false);
      this.SET_IS_JOIN_OTHER_GROUPON({ joinOther: true, alias });

      if (this.goods.goodsType === memberCardGoodsType && this.isCardTaken) {
        wx.showToast({
          icon: 'none',
          title: '你已持有该会员卡，无法参与拼团',
        });
        return;
      }

      if (this.isOld2New) {
        // 老带新拼团
        this.resolveNewCustomerGroupon({
          callback: () => this.fetchGoodsDetail(),
        });
      } else {
        // 其他拼团
        this.getSkuData();
      }
    },
  },
  reopening: {
    type: 'reopening',
    text: '重新开团',
    checkEnable: (groupInfo) => {
      const {
        isEnd,
        isGroupOnSuccess,
        isGroupOnFailed,
        isJoinedGroup,
        hasJoinedGroup,
        isAllowOpenGroup,
      } = groupInfo;
      if (
        !isEnd &&
        ((isGroupOnSuccess && !isJoinedGroup) || isGroupOnFailed) &&
        !hasJoinedGroup &&
        isAllowOpenGroup
      ) {
        return true;
      }
      return false;
    },
    action() {
      if (this.isLadder) {
        return navigate.navigate({
          url: `/packages/goods/groupon/index?alias=${this.goods.alias}`,
        });
      }
      this.createGroupon = true;
      this.SET_IS_CREATE_GROUPON(true);
      this.SET_IS_JOIN_OTHER_GROUPON({ joinOther: false, alias: null });
      this.fetchGoodsDetail();
    },
  },
  toShop: {
    type: 'toShop',
    text: '进店逛逛',
    action() {
      navigate.switchTab({
        url: '/packages/home/<USER>/index',
      });
    },
  },
};

export const formatPrice = (str) => {
  const strArr = str.split('起');
  return strArr.length > 1 ? `${+strArr[0]}起` : `${+strArr[0]}`;
};

// 获取当前页面的url带参数
export function getCurrentPageUrlWithParams() {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const { options } = currentPage;
  const url = currentPage.route;

  let urlWithParams = url + '?';
  Object.keys(options).forEach((key) => {
    urlWithParams += `${key}=${options[key]}&`;
  });
  return urlWithParams.substring(0, urlWithParams.length - 1);
}

// 社群拼团类型为2
export const GROUP_BARGAIN_TYPE = 2;
