/* eslint-disable @youzan-open/tee/no-platform-field, @youzan/dmc/wx-check */
import getApp from 'shared/utils/get-safe-app';
import { baseImgUrl } from './constants';

const app = getApp();

const imgUrl1 = `${baseImgUrl}2018/12/13/`;
const imgUrl2 = `${baseImgUrl}2028/12/14/`;

// 普通商品凑团开团所需的信息太多，会员卡商品信息不够，前端单独组装
const DEFAULT_RIGHTS = {
  postage: {
    icon: `${imgUrl1}cfaeed64cc3fa690ff03511312fdc990.png`,
    text: '会员包邮',
    desc: '免运费',
  },
  discount: {
    icon: `${imgUrl1}f39293ef058813802ef8ad8a935c1909.png`,
    text: '专享折扣',
    desc: '立享#detail#折',
  },
  point: {
    icon: `${imgUrl2}930ae6ff44be40ed899416b1a67364ed.png`,
    text: '积分赠送',
    desc: '#detail#积分',
  },
  coupon: {
    icon: `${imgUrl2}a8932998aca23eb6e8864440ce6a3c52.png`,
    text: '优惠券',
    desc: '#detail#张',
  },
  pointfeedback: {
    icon: `${imgUrl1}def8c4c521616d58eed4fad99be65bd4.png`,
    text: '积分回馈',
    desc: '#detail#倍积分',
  },
  growth: {
    icon: `${imgUrl2}768d71514657af3f63c076dd2218579c.png`,
    text: '成长值',
    desc: '助力升级#detail#点',
  },
  paidcontent: {
    icon: `${imgUrl2}8aaf19a8c62f7f4572569b6141d1367a.png`,
    text: '知识付费',
    desc: '#detail#',
  },
};

const generateShareTitle = (buyPrice, cardName, rightList) => {
  let title = `超值！只要${buyPrice}元，团购 “${cardName}”，`;
  const rightDesc = [];

  if (rightList.postage.get) {
    rightDesc.push('包邮');
  }
  if (rightList.coupon.get) {
    rightDesc.push('超值优惠券');
  }
  if (rightList.discount.get) {
    rightDesc.push(`专享${rightList.discount.detail}折`);
  }
  if (rightList.paidcontent.get) {
    rightDesc.push('知识付费');
  }
  if (rightList.growth.get) {
    rightDesc.push(`${rightList.growth.detail}点成长值`);
  }
  if (rightList.pointfeedback.get) {
    rightDesc.push(`${rightList.pointfeedback.detail}倍积分`);
  }
  if (rightList.point.get) {
    rightDesc.push(`${rightList.point.detail}积分`);
  }

  title += `${rightDesc.join('，')}。`;
  return title;
};

// TODO：替换购买实现，使用通用方法
function goBuy(cardSku, createGroupon, activity) {
  // 如果自己已经拥有此卡，提示用户已经有卡不能参团
  const logGlobalInfo = app.logger.getGlobal() || {};

  const userInfo = logGlobalInfo.user || {};
  const contextInfo = logGlobalInfo.context || {};

  const bizTraceObject = {
    ...contextInfo,
    platform: 'weapp',
    uuid: userInfo.uuid,
    userId: userInfo.li || '',
  };

  const cacheData = {
    type: 'goods',
    // cardNo: card_no,
    goods_list: [
      {
        activityId: +activity.id,
        activityType: +activity.activityType || 4,
        message: {},
        num: 1,
        price: activity.price,
        skuId: cardSku.goodsSkuId,
        goodsId: cardSku.goodsId,
        kdtId: app.getKdtId(),
        bizTracePointExt: JSON.stringify(bizTraceObject),
      },
    ],
    createGroupon,
    isGroupon: true,
    activityAlias: createGroupon ? '' : activity.alias,
  };

  const dbid = app.db.set(cacheData);
  wx.navigateTo({
    url: '/packages/order/index?orderFrom=membercard&dbid=' + dbid,
  });
}

export { goBuy, generateShareTitle, DEFAULT_RIGHTS };
