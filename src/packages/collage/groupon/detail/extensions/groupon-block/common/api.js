import { COUPON_TAKE_BIZNAME } from './constants';

const app = getApp();

// 判断是否是新用户
const fetchIsUserNewCustomer = () =>
  app
    .request({
      path: '/wscump/groupon/is_user_new_customer.json',
    })
    .then(({ data }) => !data);

// 多人拼团获取团详情信息
const fetchGrouponDetail = (params) => {
  return app.request({
    path: '/wscump/groupon/detail-v2.json',
    data: params,
  });
};

// 获取裂变券信息
const fetchFissionDetail = (parmas) => {
  return app.request({
    path: '/wscump/targeted-marketing/fission/detail-get.json',
    data: {
      order_no: parmas,
    },
  });
};

// 获取会员卡拼团信息
const fetchCardByGoodsId = (params) => {
  return app.request({
    path: '/wscuser/membercard/groupon/detail.json',
    data: {
      goodsId: params,
    },
  });
};

// 获取商品sku信息
const fetchSkuDetail = (params) => {
  return app.request({
    path: '/wscump/collage/goods_sku_detail.json',
    data: params,
  });
};

// 生成海报
const generatePoster = (data) =>
  app.request({
    data,
    method: 'POST',
    path: '/wscump/groupon/poster.json',
  });
const getMessageActivityId = (data) => {
  return app.request({
    path: '/wscump/common/get-message-activity-id.json',
    data,
    type: 'GET',
  });
};

const fetchRecommendGoods = (data) =>
  app.request({
    data,
    method: 'GET',
    path: '/wscump/groupon/recommend-goods.json',
  });
// 获取全部团员信息
const getGroupJoinedRecord = (params) =>
  app.request({
    path: '/wscump/groupon/joined_record.json',
    data: params,
    method: 'GET',
  });
// 领取券
export function receiveCoupon({ couponId }) {
  const requestId = `${couponId}-${new Date().getTime()}`;
  const bizName = COUPON_TAKE_BIZNAME;
  return app.request({
    path: '/wscump/groupon/receive-coupon.json',
    data: { activityId: couponId, bizName, requestId },
    method: 'GET',
  });
}

const getWeappCard = (params) => {
  return app.request({
    path: '/wscgoods/poster/card/goods-v2',
    data: params,
    method: 'GET',
  });
};
const sendRecordUser = (params) => {
  return app.request({
    path: '/wscump/groupon/record_user.json',
    data: params,
    method: 'POST',
  });
};

export {
  fetchIsUserNewCustomer,
  fetchGrouponDetail,
  fetchFissionDetail,
  fetchCardByGoodsId,
  fetchSkuDetail,
  generatePoster,
  fetchRecommendGoods,
  getGroupJoinedRecord,
  getWeappCard,
  getMessageActivityId,
  sendRecordUser,
};
