/* eslint-disable @youzan-open/ranta-tee/valid-ranta-io */
import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  options: {
    multipleSlots: true, // 启用多slot支持
  },
  data: {
    timeData: {},
  },
  properties: {
    time: {
      type: Number,
    },
    cls: {
      type: String,
    },
    fontWidth: {
      type: Number,
    },
  },
  methods: {
    onChange(e) {
      const { days, hours, minutes, milliseconds } = this.data.timeData;
      if (
        e.detail.days !== days ||
        e.detail.hours !== hours ||
        e.detail.minutes !== minutes ||
        Math.floor(e.detail.milliseconds / 100) !==
          Math.floor(milliseconds / 100)
      ) {
        this.setYZData({
          timeData: {
            ...e.detail,
            milliseconds: Math.floor(e.detail.milliseconds / 100),
          },
        });
      }
    },
  },
});
