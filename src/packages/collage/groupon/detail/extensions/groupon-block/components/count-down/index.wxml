<van-count-down
  millisecond
  use-slot
  time="{{ time }}"
  bind:change="onChange"
>
  <text wx:if="{{ timeData.days > 0 }}" class="text-block {{cls}}">{{ timeData.days }}天</text>
  <text class="text-block {{cls}}" style="width: {{fontWidth}}px">{{ timeData.hours >= 10 ? timeData.hours : '0' + timeData.hours }}</text>
  <text class="colon {{cls}}">:</text>
  <text class="text-block {{cls}}" style="width: {{fontWidth}}px">{{ timeData.minutes >= 10 ? timeData.minutes : '0' + timeData.minutes }}</text>
  <text class="colon {{cls}}">:</text>
  <text class="text-block {{cls}}" style="width: {{fontWidth}}px">{{ timeData.seconds >= 10 ? timeData.seconds : '0' + timeData.seconds }}</text>
  <text class="colon {{cls}}">.</text>
  <text class="text-block {{cls}}" style="width: {{fontWidth / 2}}px">{{ timeData.milliseconds }}</text>
</van-count-down>

