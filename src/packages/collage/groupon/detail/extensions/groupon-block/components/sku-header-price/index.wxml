<view class="groupon-sku">
  <theme-view color="general" custom-class="groupon-sku-price">
    ￥{{ extraData.skuPrice }}
  </theme-view>
  <theme-view color="general" bg="main-bg" opacity="0.1" custom-class="groupon-sku-text">
    拼团价
  </theme-view>
</view>
<theme-view bg="main-bg" wx:if="{{ extraData.skuCouponPrice }}" custom-class="groupon-sku-coupon">
  拼团券后￥
  <text class="groupon-sku-coupon-strong">{{extraData.skuCouponPrice}}</text>
</theme-view>