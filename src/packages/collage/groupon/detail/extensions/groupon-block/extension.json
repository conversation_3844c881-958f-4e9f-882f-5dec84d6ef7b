{"name": "@wsc-tee-ump/groupon-detail-groupon-block", "extensionId": "@wsc-tee-ump/groupon-detail-groupon-block", "version": "0.0.1", "bundle": "<builtin>", "lifecycle": ["beforePageMount", "onPullDownRefresh", "onShareAppMessage"], "framework": "weapp", "platform": ["weapp"], "widget": {"provide": ["GrouponBlock", "RecommendBlock", "FissionTips", "HeaderReceive<PERSON><PERSON>", "OrderBlock", "RulesBlock", "TradeCarousel", "AvatarWrap", "BtnsWrap", "GoingList", "GoodsWrap", "GroupBargin", "StatusTitle", "GrouponSku", "MemberList", "MembercardRights", "SharePoster"], "consume": ["GrouponBlock", "RecommendBlock", "FissionTips", "HeaderReceive<PERSON><PERSON>", "OrderBlock", "RulesBlock", "TradeCarousel", "AvatarWrap", "BtnsWrap", "GoingList", "GoodsWrap", "GroupBargin", "StatusTitle", "GrouponSku", "MemberList", "MembercardRights", "SharePoster"]}, "data": {"provide": {"getCollageDate": ["r", "w"], "title": ["r"], "openCustomNav": ["r"]}, "consume": {"getCollageDate": ["r", "w"]}}, "event": {"emit": ["ready"], "listen": ["ready"]}}