<canvas
  canvas-id="invitePostCanvasId"
  class="share-canvas"
/>

<van-popup
  custom-class="share-popup"
  show="{{ isShowInvitePost }}"
>
  <view
    bindtap="closeInvitePostModal"
    class="share-popup-close"
  >
    <van-icon name="close" slot="close-icon"></van-icon>
  </view>
  <view class="share-popup-content">
    <image
      class="share-popup-content__img"
      src="{{ shareImgSrc }}"
    >
    </image>
  </view>
  <view
    class="share-btn--bottom"
    bind:tap="clickSaveImage"
  >
    保存图片
  </view>
</van-popup>

<!-- 上拉弹窗 -->
<van-actionsheet
  show="{{ showPopup }}"
  z-index="{{ 99 }}"
>
  <view class="invite-popup">
    <button
      wx:for="{{ actions }}"
      wx:key="index"
      class="invite-btn"
      hover-class="none"
      data-type="{{ item.type }}"
      open-type="{{ item.type === 'share' ? 'share' : '' }}"
      bindtap="onSelect">
      {{ item.name }}
    </button>
    <view class="invite-btn invite-btn--cancel" bindtap="onClose">取消</view>
  </view>
</van-actionsheet>
