import WscComponent from 'pages/common/wsc-component/index';
import authorize from '@/helpers/authorize';
import { promisifyWxStyle } from 'utils/promisify';
import { generatePoster } from '../../common/api';
import { getShareParams } from 'shared/utils/share-params';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';

const shareParams = getShareParams('poster');

const app = getApp();

const loadImage = (src) => app.downloadFile({ url: src }).then((result) => result.tempFilePath);

WscComponent({
  options: {
    multipleSlots: true, // 启用多slot支持
  },
  properties: {
    // 页面路由
    pageUrl: {
      type: String,
      value: '',
    },
    shopLogo: {
      type: String,
      value: '',
    },
    shopName: {
      type: String,
      value: '',
    },
    // 商品信息
    goods: {
      type: Object,
      value: {},
    },
    // 页面参数
    pageQuery: {
      type: Object,
      value: {},
    },
    // 用户信息
    userInfo: {
      type: Object,
      value: {},
    },
    // 展示上拉菜单
    showPopup: {
      type: Boolean,
      value: false,
    },
    // 进店参数标识
    umpAlias: {
      type: String,
      value: '',
    },
  },
  data: {
    isShowInvitePost: false,
    shareImgSrc: '',
    loadedThumbUrl: '',
    loadedQrCodeUrl: '',
    actions: [
      {
        name: '邀请好友参团',
        type: 'share',
      },
      {
        name: '生成邀请海报',
        type: 'poster',
      },
    ],
  },
  methods: {
    closeInvitePostModal() {
      this.setYZData({ isShowInvitePost: false });
    },

    // 保存海报
    clickSaveImage() {
      const {
        data: { shareImgSrc },
      } = this;
      if (!shareImgSrc) return;

      wx.showLoading({ title: '保存中' });
      authorize('scope.writePhotosAlbum')
        .then(() => {
          promisifyWxStyle(wx.saveImageToPhotosAlbum)({ filePath: shareImgSrc })
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000,
              });
              this.closeInvitePostModal();
            })
            .catch(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000,
              });
            });
        })
        .catch(() => {
          wx.hideLoading();
          wx.showModal({
            content: '需要同意将分享图片保存到相册，点击确定后跳转至设置页操作',
            success: (e) => {
              if (e.cancel) return;
              wx.openSetting({
                success: ({ authSetting }) => {
                  if (authSetting['scope.writePhotosAlbum']) {
                    this.clickSaveImage();
                  }
                },
              });
            },
          });
        });
    },
    // 绘制海报
    drawShareImage() {
      const { goods, userInfo, loadedQrCodeUrl, loadedThumbUrl } = this.data;
      const ctx = wx.createCanvasContext('invitePostCanvasId', this);

      const params = {
        ctx,
        goods,
        userInfo,
        loadedThumbUrl,
        loadedQrCodeUrl,
        loadedBgUrl: this.loadedBgUrl,
        loadedGrouponIcon: this.loadedGrouponIcon,
      };
      this.drawNormalPostal(params);
    },

    getPoster() {
      const { goods, pageUrl, pageQuery, umpAlias } = this.data;
      const { originPrice, picture, title } = goods;

      const data = {
        page: pageUrl,
        query: {
          ...pageQuery,
          ...shareParams,
          ...defaultEnterShopOptions,
          umpAlias,
          fromShare: 1,
        },
        goods: {
          originPrice,
          title,
          picture,
        },
      };

      wx.showLoading({ title: '正在生成' });
      generatePoster(data)
        .then((res) => {
          return loadImage(res.img);
        })
        .then((url) => {
          wx.hideLoading();
          this.setYZData({
            shareImgSrc: url,
            isShowInvitePost: true,
          });
        })
        .catch(() => {
          wx.hideLoading();
          wx.showToast({
            title: '生成海报失败',
            icon: 'none',
          });
        });
    },

    onSelect(e) {
      this.onClose();
      const { type } = e.currentTarget.dataset;

      if (type === 'poster') {
        // 海报
        app.logger
          && app.logger.log({
            et: 'click', // 事件类型
            ei: 'share', // 事件标识
            en: '分享', // 事件名称
            params: {
              share_cmpt: 'poster',
            },
          });
      }

      if (type === 'share') {
        return;
      }
      this.getPoster();
    },

    onClose() {
      this.triggerEvent('close', false);
    },
  },
});
