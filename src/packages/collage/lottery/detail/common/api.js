const app = getApp();

// 获取抽奖拼团结果
export const getMessageActivityId = (data) => {
  return app.request({
    path: '/wscump/common/get-message-activity-id.json',
    data,
    type: 'GET'
  });
};

// 生成海报
export const generatePoster = (data) => {
  return app.request({
    data,
    method: 'POST',
    path: '/wscump/lottery/poster.json'
  });
};

// 是否为粉丝
export function checkNeedFollow(data) {
  return app.request({
    path: '/wscump/common/check-need-follow.json',
    data,
  });
}
