<page-container page-container-class="{{ showRules ? 'no-scroll' : '' }}">
  <!-- 商品信息 -->
  <goods-wrap
   goods="{{ goods }}"
   countdown="{{ activityCountdown }}"
   isGrouponFailed="{{ status === 2 }}"
  >
    <text class="goods-wrap__tag {{status === 2 ? 'goods-wrap__tag--gray' : ''}}" slot="title-icon">抽奖团</text>
    <text slot="title-icon" class="tag-void"></text>
    <van-cell is-link border="{{ false }}" custom-class="collage-tips" bind:click="toggleRulesVisible">
      <image class="collage-tips-avatar" slot="icon" src="{{ CURRENT_GLOBAL_SHOP.logo }}" />
      <view class="collage-tips-tips van-ellipsis">抽奖规则：成团获得抽奖资格，重复成团中奖率提升…</view>
    </van-cell>
  </goods-wrap>

  <!-- 拼团信息 -->
  <group-wrap
    is-joined-group="{{ isJoinedGroup }}"
    is-grouping="{{ status === 0 }}"
    collage-title="{{ collageTitle }}"
    status-string="{{ statusString }}"
    scene="free_lottery"
    subscribe-scene="lottery"
    business-module="lucky_draw_group"
    main-desc="{{ grouponTip }}"
    sub-desc="{{ advertTip }}"
    remain-num="{{ groupItem.remainNum }}"
    countdown="{{ groupCountdown }}"
    participants="{{ participants }}"
    btns="{{ operationBtns }}"
    bindgetuserinfo="onGetUserInfo"
    bindclick="handleClick"
  />

  <!-- 猜你喜欢 -->
  <product-feature recommendGoods="{{recommendGoods}}"></product-feature>

  <!-- sku -->
  <base-sku
    show="{{ showSkuModal }}"
    goods="{{ goods }}"
    is-price-calendar="{{ isPriceCalendar }}"
    is-virtual-ticket= "{{ isVirtualTicket }}"
    price-calendar-data="{{ priceCalendarData }}"
    bind:sku-close="handleSkuClose"
    sku="{{ sku }}"
    properties="{{ sku.itemSalePropList }}"
    sku-properties="{{ sku.skuSalePropModelList }}"
    quota="{{ 1 }}"
    quota-used="{{ 0 }}"
    bind:buy="onBuy"
    extra-data="{{ extraData }}"
    buy-text="确认"
    hide-stock
    reset-stepper-on-hide
    show-add-cart-btn="{{ false }}"
    generic:sku-header-price="sku-header-price"
  />

  <!-- 开奖弹层 -->
  <view class="detail-popup lottery-guide {{ !lotteryVisible ? 'popup-hide' : '' }}" bindtap="toggleLotteryVisible" catchtouchmove="true">
    <view class="content">
      <view class="close-icon">
        <van-icon name="close"></van-icon>
      </view>
      <image class="lottery-guide__img" src="https://b.yzcdn.cn/wsc/20180828/imgs/lottery_img.png" />
      <view class="lottery-guide__tips">你参加的0元抽奖团开奖啦</view>
      <form-view
        type="lucky_draw_group"
        class="lottery-guide__btn"
        hover-class="none"
        bindtap="showResult">
        查看开奖结果
      </form-view>
    </view>
  </view>

  <van-toast id="van-toast" />
</page-container>

<!-- 活动规则 -->
<van-popup
  show="{{ showRules }}"
  round
  position="bottom"
  custom-class="rules-popup"
>
  <view class="popup-title">
    玩法详情
  </view>
  <view class="rules">
    <view class="rules-detail">开奖时间：活动结束后立即开奖。</view>
    <view class="rules-detail"> 1、活动结束后将从拼团成功的用户中，随机抽取中奖者。</view>
    <view class="rules-detail"> 
      2、拼团未成功不具备抽奖资格，拼团成功后可获得抽奖资格，一等奖为【{{ goods.title }}】 
      <block wx:if="{{ activity.goodsNum }}">，{{ activity.goodsNum }}人可中奖</block>
      <block wx:if="{{ couponName }}">
        ，二等奖为【{{ couponName }}】
        <block wx:if="{{ activity.couponNum }}">，{{ activity.couponNum }}人可中奖</block>
      </block>。
    </view>
    <view class="rules-detail">3、同一商品，同一用户仅限作为团员成团一次，但可以作为团长无限发起拼团，成团后，中奖概率叠加。</view>
    <view class="rules-detail">
      4、<block wx:if="{{ couponName }}">【{{ couponName }}】在抽奖结束后立即发放，</block>【{{ goods.title }}】在抽奖结束后72小时内发货。
    </view>
  </view>
  <view class="contact" bindtap="customerService">
    <block wx:if="{{ phone }}">
      如有问题，请拨打电话联系客服\n{{ phone || '' }}
    </block>
  </view>
  <button
    class="rules-btn"
    bind:tap="toggleRulesVisible"
  >
    知道了
  </button>
</van-popup>

<!-- 邀请好友 -->
<share-poster
  show-popup="{{ showInvitePopup }}"
  shop-logo="{{ shopLogo }}"
  shop-name="{{ shopName }}"
  goods="{{ goods }}"
  ump-alias="{{ activity.activityAlias }}"
  page-query="{{ pageQuery }}"
  user-info="{{ userInfo }}"
  page-url="packages/collage/lottery/detail/index"
  bindclose="togglePopupShow"
/>

<follow-popup
  show="{{ showFollowPopup }}"
  extra-data="{{ followExtraData }}"
  title="成为店铺粉丝才能参与抽奖"
  btn-text="成为粉丝"
  bind:close="toggleFollowPopupShow"
/>