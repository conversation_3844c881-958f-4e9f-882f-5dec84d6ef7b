.detail-popup {
  z-index: 2000;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  text-align: center;
  background-repeat: no-repeat;
  transition: 0.2s all ease-in;
}

.lottery-guide {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;

  .content {
    position: relative;
    padding: 24px;
  }

  .close-icon {
    position: absolute;
    top: 0;
    right: 0;
    padding: 2px;
    text-align: right;
    font-size: 22px;
  }

  &__img {
    max-width: 245px;
    height: 452rpx;
  }

  &__tips {
    margin: 8px 0 20px;
    font-size: 16px;
    line-height: 22px;
  }

  &__btn {
    display: block;
    margin: 0 auto;
    background-image: linear-gradient(135deg, #ff5644 0%, #fb1c60 100%);
    border-radius: 22px;
    width: 160px;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    color: #fff;
  }
}

.collage-tips {
  align-items: center;
  padding: 10px 0 !important;

  &-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  &-tips {
    overflow: hidden;
    margin: 0 8px 0 5px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.popup-hide {
  display: none;
}

.rules-popup {
  border-radius: 12px 12px 0 0;
  padding: 0 16px;
  box-sizing: border-box;
  max-height: 95vh !important;

  .popup-title {
    position: relative;
    height: 44px;
    text-align: center;
    font-size: 16px;
    line-height: 44px;
    font-family: PingFangSC-Medium;

    .close-icon {
      position: absolute;
      top: 11px;
      right: 0;
      content: '';
      width: 22px;
      height: 22px;
      background: url(https://b.yzcdn.cn/public_files/d542190bf6bb53eb8c71b9e6b953967c.png)
        no-repeat center;
      background-size: 100% 100%;
    }
  }

  .rules {
    padding-top: 12px;
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    border-bottom: 1px dashed #c8c9cc;

    &-detail {
      margin-bottom: 15px;
    }
  }

  .rules-btn {
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    background-image: linear-gradient(90deg, #ff6034 0%, #ee0a24 100%);
    border-radius: 100px;
    color: #fff;
    text-align: center;
    margin-bottom: 7px;
  }

  .contact {
    margin: 20px 0 40px;
    text-align: center;
    line-height: 20px;
    font-size: 14px;
    white-space: pre-wrap;
  }
}

.goods-wrap__tag {
  position: absolute;
  left: 0;
  top: 3px;
  display: inline-block;
  height: 32px;
  line-height: 32px;
  padding: 0 5px;
  background-image: linear-gradient(135deg, #ff5644 0%, #fb1c60 100%);
  border-radius: 24px;
  text-align: center;
  color: #fff;
  font-size: 20px;
  transform: scale(0.5);
  transform-origin: 0% 0%;
  vertical-align: bottom;
}

.goods-wrap__tag--gray {
  background-image: linear-gradient(135deg, #bcbab9 0%, #9c9497 100%);
}

.tag-void {
  width: 38px;
  display: inline-block;
}

.no-scroll {
  height: 100vh;
  overflow: hidden;
}
