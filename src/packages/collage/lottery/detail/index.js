import WscPage from 'pages/common/wsc-page/index';
import * as Time from 'utils/time';
import args from '@youzan/weapp-utils/lib/args';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import Toast from '@vant/weapp/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import navigate from '@/helpers/navigate';
import { umpEnterShop } from '../../../common/ump-enter-shop/index';
import { getShareParams } from 'shared/utils/share-params';
import {
  getLotteryCollageDetail,
  getRecommendGoods,
  judgePayOrder,
} from '../../api';
import { getMessageActivityId, checkNeedFollow } from './common/api';
import money from '@youzan/weapp-utils/lib/money';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';
import { skuTradeUtilsPromise } from 'shared/utils/async-base-sku';

const app = getApp();
const loadImage = (src) =>
  app.downloadFile({ url: src }).then((result) => result.tempFilePath);

// 分享参数
const shareParams = getShareParams('native_custom');
const p = '/packages';

const defaultUserAvatar =
  'https://img.yzcdn.cn/public_files/2018/09/07/af3aa756277cb619b9eb6062ec46cf57.png';
// 活动类型
const activityType = '23';
let ajaxCount = 0;

WscPage({
  data: {
    id: null, // 活动id
    status: null, //  0 待成团; 1 已成团; 2 未成团; 3 模拟成团

    showRules: false, // 是否展示活动规则
    showInvitePopup: false, // 是否展示邀请弹层
    showSkuModal: false, // 是否展示sku弹层
    shareVisible: false, // 是否展示分享引导
    lotteryVisible: false, // 是否展示开奖弹层
    isJoinedGroup: false, // 是否加入团

    statusString: '',
    couponName: '', // 优惠券名字
    grouponTip: '', // 成团提示
    advertTip: '', // 广告提示
    loadedBgUrl: '', // 海报背景图
    phone: '', // 客服电话
    prizeIcon: '',
    collageTitle: '拼团剩余时间',

    userInfo: {}, // 用户信息
    messageConfig: {},
    activityCountdown: {
      // 活动倒计时
      day: '0',
      hour: '00',
      minute: '00',
      second: '00',
    },
    groupCountdown: {
      // 团倒计时
      hour: '00',
      minute: '00',
      second: '00',
    },
    sku: {}, // 商品sku
    extraData: {
      useCustomHeaderPrice: true,
    },
    pageQuery: {},
    activity: {}, // 活动信息
    groupItem: {}, // 团信息
    goods: {}, // 商品信息

    operationBtns: [], // 操作按钮
    participants: [], // 团员信息
    recommendGoods: [], // 猜你喜欢商品
    actions: [
      {
        name: '邀请好友参团',
        type: 'share',
      },
      {
        name: '生成邀请海报到相册',
        type: 'poster',
      },
    ],
    isPriceCalendar: false, // 判断是否是价格日历场景
    priceCalendarData: {
      priceBarList: [],
      // 当前 sku 日历选中项，在切换 sku 的时候需要清除
      selected: null,
      // 当前展示的 skuId
      activeSkuId: null,
    },
    showFollowPopup: false,
    followExtraData: {},
  },

  onLoad(ctx) {
    const {
      umpAlias = '',
      order_no: orderNo = '',
      group_alias: groupAlias = '',
      activity_id: activityId = '',
    } = ctx;

    this.$log = {
      act_type: activityType,
      act_id: activityId,
    };

    this.setYZData({
      id: activityId,
      pageQuery: ctx,
    });
    this.groupAlias = groupAlias;
    this.orderNo = orderNo;

    const redirectUrl = args.add('/' + this.__route__, ctx);
    umpEnterShop(ctx, umpAlias, 'luckyDrawGroup', redirectUrl, {
      name: 'luckyDrawGroup_without_ump_alias',
      message: '0元抽奖活动页缺少必要参数',
    })
      .then(() => {
        this.initData();
      })
      .catch(() => {
        this.initData();
      });
  },

  onShow() {
    const { status } = this.data;

    // 设置导航栏颜色
    if (status === 2) {
      wx.setNavigationBarColor({
        frontColor: '#ffffff',
        backgroundColor: '#bcbab9',
      });
    }
  },

  onHide() {
    clearInterval(this.activityCountdownTimer);
    clearInterval(this.groupCountdownTimer);
  },

  onUnload() {
    clearInterval(this.activityCountdownTimer);
    clearInterval(this.groupCountdownTimer);
  },

  // 分享
  onShareAppMessage() {
    const { pageQuery = {}, goods, userInfo, activity = {} } = this.data;
    const nickName = userInfo.nickName || '';
    const umpAlias = activity.activityAlias || '';
    pageQuery.fromShare = 1;
    const path = args.add(`${p}/collage/lottery/detail/index`, {
      ...shareParams,
      ...pageQuery,
      ...defaultEnterShopOptions,
      umpAlias,
    });
    const title = `${nickName}邀请你一起免费抽${goods.title}，快来试下手气吧！`;
    const imageUrl = this.originPicture;

    this.updateShareMenu();

    return {
      title,
      path,
      imageUrl,
      success() {
        Toast({
          message: '已成功发送邀请，分享到3个微信群，拼团成功率高达98%',
          selector: '#van-toast',
        });
      },
    };
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.initData();
  },

  initData() {
    const { groupAlias } = this;
    const { orderNo } = this;
    Toast.loading('数据加载中');
    getLotteryCollageDetail({
      groupAlias,
      orderNo,
    })
      .then((res) => {
        Toast.clear();
        this.actionAfterInitData(res);
      })
      .catch((err) => {
        // 下单后ump信息延迟再发请求
        if (Number(err.code) === 160701301 && ajaxCount < 3) {
          ajaxCount++;
          setTimeout(() => {
            this.initData();
          }, 500);
        } else {
          wx.showToast({
            title: err.msg,
            icon: 'none',
            duration: 1000,
          });
          setTimeout(() => {
            wx.stopPullDownRefresh();
          }, 500);
        }
      });
  },

  actionAfterInitData(res) {
    const { orderNo } = this;
    const { goodsItem = {}, activity } = res;
    // 设置导航栏颜色
    if (res.groupItem.status === 2) {
      wx.setNavigationBarColor({
        frontColor: '#ffffff',
        backgroundColor: '#bcbab9',
      });
    }
    // 图片压缩
    this.originPicture = cdnImage(goodsItem.picture);

    res.goodsItem = {
      ...goodsItem,
      price: ['0', '00'],
      originPrice: goodsItem.price,
      picture: cdnImage(goodsItem.picture, '!220x0.jpg'),
      url: `${p}/goods/lucky-draw-group/index?alias=${goodsItem.alias}&activityId=${activity.id}&type=luckyDrawGroup`,
    };

    res.groupParticipationList.forEach((v) => {
      v.profilePicture = cdnImage(v.profilePicture, '!120x120.jpg');
    });

    if (res.sku && res.sku.stock_num < 1) {
      res.sku.stock_num = 1;
    }

    const goodsInfo = res.goodsItem;
    const isPriceCalendar =
      goodsInfo.ecardExtra && goodsInfo.ecardExtra.validityType == 3;

    const { pageQuery } = this.data;
    const followExtraData = {
      bizCode: 9,
      bizSubCode: 0,
      activityKey: activity.activityAlias,
      feature: {
        ...pageQuery,
        groupAlias: this.groupAlias,
      },
      targetImg: this.originPicture,
      targetUrl: args.add('/' + this.__route__, pageQuery),
    };
    this.setYZData({
      phone: res.hotline,
      isJoinedGroup: res.isJoinedGroup,
      activity,
      goods: goodsInfo,
      isPriceCalendar,
      ownParticipation: res.ownParticipation || {},
      participants: res.groupParticipationList,
      groupItem: res.groupItem,
      couponName: res.couponItem ? res.couponItem.title : '',
      lotteryVisible:
        !!res.activity.solved &&
        (res.groupItem.status === 1 || res.groupItem.status === 3) &&
        res.isJoinedGroup,
      sku: mapKeysCase.toCamelCase(res.sku),
      collageTitle: res.collageTitle,
      statusString: res.statusString,
      status: res.groupItem.status,
      advertTip: res.advertTip,
      grouponTip: res.grouponTip,
      operationBtns: res.operationBtns,
      orderNo: res.ownParticipation ? res.ownParticipation.orderNo : orderNo,
      followExtraData,
    });
    // 活动倒计时
    this.activityCountdownTimer = setInterval(() => {
      const activityCountdown = this.coutdown(
        this.data.activity.endTime,
        'activity'
      );
      this.setYZData({
        activityCountdown,
      });
    }, 1000);

    // 团倒计时
    this.groupCountdownTimer = setInterval(() => {
      const groupCountdown = this.coutdown(
        this.data.groupItem.endTime,
        'group'
      );
      this.setYZData({
        groupCountdown,
      });
    }, 1000);

    // 获取猜你喜欢商品列表
    this.fetchFeatureGoods(activity.recommendGoodsInfo);
    // 获取动态消息id
    this.getActivityId(res.isJoinedGroup, res.groupItem.status);

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 500);
    if (activity?.threshold) {
      this.checkShowFollow();
    }
    this.getCalendarInfo();
  },

  // 获取动态消息id
  getActivityId(isJoined, status) {
    // 状态进行中且是团员才获取
    if (status !== 0 || !isJoined) return;

    const { id, endTime } = this.data.groupItem;

    getMessageActivityId({
      activityId: id,
      activityType,
      expirationTime: endTime / 1000,
    })
      .then((res) => {
        this.wxActivityId = res.wxActivityId;
      })
      .catch((err) => {
        console.error(err);
      });
  },

  updateShareMenu() {
    const { remainNum, participantNum, endTime } = this.data.groupItem || {};
    const activityId = this.wxActivityId;

    // 团剩余时间小于24小时可发动态消息
    const isTimeValid = endTime - Date.now() < 24 * 60 * 60 * 1000;

    if (!activityId || !isTimeValid) return;

    wx.updateShareMenu({
      withShareTicket: true,
      isUpdatableMessage: true,
      activityId,
      targetState: 0,
      templateInfo: {
        parameterList: [
          {
            name: 'member_count',
            value: `${participantNum}`,
          },
          {
            name: 'room_limit',
            value: `${participantNum + remainNum}`,
          },
        ],
      },
    });
  },

  // 获取价格日历信息
  getCalendarInfo(virtualSku) {
    // 如果不是卡券或者不支持卡券 直接返回
    if (this.data.goods.ecardExtra?.validityType != 3) return;
    const date = new Date();
    const currentYear = date.getFullYear();
    const currentMonth = date.getMonth() + 1;
    return new Promise((resolve) => {
      app
        .request({
          // method: 'POST',
          path: 'wscshop/goods/list-date.json',
          query: {
            beginDate: this.getParamDate(currentYear, currentMonth, true),
            endDate: this.getParamDate(
              this.calEndYear(currentYear, currentMonth, 3),
              this.calEndMonth(currentMonth, 3),
              false
            ),
            from: 'wsc',
            itemId: +this.data.goods.id,
            itemSkuId: virtualSku || this.getVirtualSku(),
            kdtId: app.getKdtId(),
            activityType:
              (this.data.goods.activities &&
              this.data.goods.activities.type === 'GROUP_ON_DRAW'
                ? 23
                : 0) || 0,
            itemPreOrderTime: this.data.goods.ecardExtra.itemPreOrderTime,
          },
        })
        .then((res) => {
          this.setYZData({
            priceCalendarData: res,
          });
          resolve(res);
        })
        .catch((err) => {
          Toast(err.msg);
        });
    });
  },

  getVirtualSku() {
    if (this.data.sku.none_sku) {
      return this.data.sku.collection_id;
    }
    const skuTree = this.data.sku.tree;
    const skyTreeValue = {
      s1: 0,
      s2: 0,
      s3: 0,
    };
    skuTree.forEach((n, i) => {
      skyTreeValue[`s${i + 1}`] = n.v[0].id;
    });
    const virtualSku = this.data.sku.list.filter(
      (n) =>
        n.s1 == skyTreeValue.s1 &&
        n.s2 == skyTreeValue.s2 &&
        n.s3 == skyTreeValue.s3
    );
    const setSelectedSKU = {
      s1: virtualSku[0].s1,
      s2: virtualSku[0].s2,
      s3: virtualSku[0].s3,
      price: virtualSku[0].price,
      stockNum: virtualSku[0].stock_num,
      skuId: virtualSku[0].id,
      points: null,
    };
    this.setYZData({
      pcDefaultSku: setSelectedSKU,
    });
    return virtualSku[0].id;
  },

  // 拼接接口需要的日期格式
  getParamDate(currentYear, currentMonth, isBegin) {
    const paramCurrentMonth =
      currentMonth > 9 ? currentMonth : `0${currentMonth}`;
    const paramCurrentDate = isBegin
      ? '01'
      : new Date(currentYear, currentMonth, 0).getDate();
    return `${currentYear}-${paramCurrentMonth}-${paramCurrentDate}`;
  },

  // 计算目标年份
  calEndYear(year, month, count) {
    let newYear;
    // 目标月份超过12
    if (month > 12 - count) {
      newYear = year + 1;
    } else {
      newYear = year;
    }
    return newYear;
  },

  // 计算目标月份
  calEndMonth(month, count) {
    let newMonth;
    // 目标月份超过12
    if (month > 12 - count) {
      newMonth = month + count - 12;
    } else {
      newMonth = month + count;
    }
    return newMonth;
  },

  // 拆分价钱元和分
  splitPrice(num) {
    return Number(num / 100)
      .toFixed(2)
      .toString()
      .split('.');
  },

  // 获取猜你喜欢商品
  fetchFeatureGoods(recommendGoodsInfo = {}) {
    const { type = 0, goodsIdList = [] } = recommendGoodsInfo;
    getRecommendGoods({
      type,
      ids: JSON.stringify(goodsIdList),
    }).then((res) => {
      const recommendGoods = res
        .filter((v) => !v.isVirtual)
        .map((v) => {
          return {
            ...v,
            price: money(v.price).toYuan(),
            imageUrl: cdnImage(v.imageUrl, '!340x0.jpg'),
          };
        });
      this.setYZData({
        recommendGoods,
      });
    });
  },

  // 倒计时数据处理
  coutdown(time, type) {
    const remain = time - Date.now();

    if (remain <= 0) {
      const timerName = `${type}CountdownTimer`;
      clearInterval(this[timerName]);
    }
    return Time.format(remain).strData;
  },

  // 重新开团
  reopening() {
    this.createGroupon = true;
    this.setYZData({
      showSkuModal: true,
    });
  },

  // 参团
  joinGroup() {
    this.createGroupon = false;
    const threshold = this.data?.activity?.threshold;
    if (threshold && this.needFollow) {
      this.toggleFollowPopupShow();
    } else {
      this.setYZData({
        showSkuModal: true,
      });
    }
  },

  checkShowFollow() {
    checkNeedFollow({
      bizCode: 9,
      bizSubCode: 0,
      channel: 1, // 小程序
      activityKey: this.data?.activity?.activityAlias,
    }).then((res) => {
      this.needFollow = res?.value;
    });
  },

  onBuy(e) {
    const kdtId = app.getKdtId();
    const price = 0; // 0元抽奖支付价格一定为0
    const {
      goodsId,
      messages: message,
      selectedNum: num,
      selectedSkuComb: { id: skuId, properties = [] },
    } = e.detail;
    const { activity, groupItem, goods } = this.data;

    app.logger &&
      app.logger.log({
        et: 'click',
        ei: 'click_choujiang',
        en: '拼团详情页_0元参团抽奖按钮_点击',
        si: app.getKdtId(),
        params: {
          ...(this.$log || {}),
        },
      });

    const goodsListObj = {
      activityAlias: this.createGroupon ? '' : groupItem.alias,
      goodsId,
      num,
      price: Number(price),
      pay: Number(price),
      skuId,
      type: goods.goodsType,
    };

    // 处理商品属性和商品加料的数据
    if (properties.length) {
      const propertyIds = properties.reduce((propertyIds, propModel) => {
        const { v = [] } = propModel;
        v.forEach((item) => {
          propertyIds.push(item.id);
        });
        return propertyIds;
      }, []);
      goodsListObj.propertyIds = propertyIds;
    }

    const judgeData = {
      kdtId,
      activityId: activity.id,
      activityType: 23,
      orderNo: this.orderNo,
      postage: 0,
      goodsList: JSON.stringify([goodsListObj]),
    };

    judgePayOrder(judgeData)
      .then((res) => {
        if (res) {
          const goodsList = [
            {
              kdtId,
              goodsId,
              skuId,
              num,
              price: Number(price),
              ...message,
              bizTracePointExt: '',
              activityType,
              activityId: activity.id,
            },
          ];
          if (properties.length) {
            goodsList[0].propertyIds = goodsListObj.propertyIds;
          }
          const extraData = {
            kdtId,
            activityAlias: this.createGroupon ? '' : groupItem.alias,
            isLuckDraw: true,
            createGroupon: this.createGroupon,
            activityType,
            activityId: activity.id,
          };

          skuTradeUtilsPromise().then(({ goToBuy }) => {
            goToBuy({
              goodsList,
              extraData,
            });
          });
        }
      })
      .catch((err) => {
        wx.showModal({
          showCancel: false,
          content: err.msg,
          success: () => {
            this.setYZData({
              showSkuModal: false,
            });
          },
        });
      });
  },

  // 查看订单详情
  showOrder() {
    wx.navigateTo({
      url: `${p}/trade/order/result/index?orderNo=${this.data.orderNo}`,
    });
  },

  // 查看中奖结果
  showResult() {
    navigate.navigate({
      url: `${p}/collage/lottery/result/index?activity_id=${this.data.activity.id}`,
    });
  },

  // 进店逛逛
  toShop() {
    navigate.switchTab({
      url: `${p}/home/<USER>/index`,
    });
  },

  handleClick(e) {
    const { type } = e.detail.dataset;
    const actionsMap = {
      REOPENING: 'reopening',
      SHOWORDER: 'showOrder',
      SHOWRESULT: 'showResult',
      INVITE: 'togglePopupShow',
      TOSHOP: 'toShop',
      JOIN: 'joinGroup',
    };
    const actionName = actionsMap[type];

    if (actionName) {
      this[actionName]();
    }
  },
  // 分享弹层
  toggleRulesVisible() {
    const showRules = !this.data.showRules;
    this.setYZData({
      showRules,
    });
  },

  // 开奖弹层
  toggleLotteryVisible() {
    const lotteryVisible = !this.data.lotteryVisible;
    this.setYZData({
      lotteryVisible,
    });
  },
  // 邀请好友弹层
  togglePopupShow() {
    app.logger &&
      app.logger.log({
        et: 'click',
        ei: 'click_invite',
        en: '拼团详情页_发起拼团_邀请好友参团_点击',
      });

    const showInvitePopup = !this.data.showInvitePopup;
    this.setYZData({
      showInvitePopup,
    });
  },
  // 打电话
  customerService() {
    const phoneNumber = this.data.phone;
    wx.makePhoneCall({
      phoneNumber,
    });
  },

  onGetUserInfo(e) {
    const { detail } = e;
    let nickName = '神秘用户';
    let avatarUrl = defaultUserAvatar;

    this.setYZData({
      isNotValid: e.detail && e.detail.isNotValid,
    });
    if (detail.userInfo) {
      detail.userInfo.nickName && (nickName = detail.userInfo.nickName);
      detail.userInfo.avatarUrl && (avatarUrl = detail.userInfo.avatarUrl);
    }

    loadImage(avatarUrl).then((loadedAvatarUrl) => {
      this.setYZData({
        'userInfo.nickName': nickName,
        'userInfo.avatarUrl': loadedAvatarUrl,
      });
    });
  },

  handleSkuClose() {
    this.setYZData({ showSkuModal: false });
  },

  toggleFollowPopupShow() {
    this.setYZData({ showFollowPopup: !this.data.showFollowPopup });
  },
});
