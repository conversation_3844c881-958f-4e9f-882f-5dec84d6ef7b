import WscPage from 'pages/common/wsc-page/index';
import navigate from '@/helpers/navigate';
import args from '@youzan/weapp-utils/lib/args';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import money from '@youzan/weapp-utils/lib/money';
import {
  getLotteryCollageResult,
  getRecommendGoods,
  getWinners,
} from '../../api';
import { umpEnterShop } from '../../../common/ump-enter-shop/index';

// 活动类型
const activityType = '23';

WscPage({
  data: {
    page: 2, // 分页页数
    pageSize: 20, // 每页多少个,
    total: 0, // 一等奖总个数
    id: null, // 活动id
    showCoupon: false,
    hasMore: false,
    title: '已开奖商品',
    coupon: {},
    goods: {},
    winners: [],
    recommendGoods: [],
  },
  onLoad(ctx) {
    const { activity_id: activityId, umpAlias } = ctx;
    const redirectUrl = args.add('/' + this.__route__, ctx);

    umpEnterShop(ctx, umpAlias, 'luckyDrawGroup', redirectUrl, {
      name: 'luckyDrawGroup_without_ump_alias',
      message: '抽奖结果页缺少必要参数',
    })
      .then(() => {
        this.initData(activityId);
      })
      .catch(() => {
        this.initData(activityId);
      });
  },
  initData(activityId) {
    this.$log = {
      act_type: activityType,
      act_id: activityId,
    };
    this.setYZData({
      id: activityId,
    });
    getLotteryCollageResult(activityId)
      .then((res) => {
        const coupon = res.isShowCoupon ? res.ownParticipation.couponItem : {};
        const { goodsItem = {} } = res;
        const hasMore = res.count > res.winners.length;

        this.fetchFeatureGoods(res.recommendGoodsInfo);

        res.winners.forEach((v) => {
          v.profilePicture = cdnImage(v.profilePicture, '!60x60.jpg');
        });

        // 处理商品信息
        res.goodsItem = {
          ...goodsItem,
          price: ['0', '00'],
          originPrice: goodsItem.price,
          picture: cdnImage(goodsItem.picture, '!220x0.jpg'),
          url: `/packages/goods/lucky-draw-group/index?alias=${goodsItem.alias}&activityId=${activityId}&type=luckyDrawGroup`,
        };

        this.setYZData({
          coupon,
          hasMore,
          total: res.count,
          goods: res.goodsItem,
          winners: res.winners,
          showCoupon: res.isShowCoupon,
        });
      })
      .catch((err) => {
        wx.showToast({
          title: err.msg,
          icon: 'none',
          duration: 1000,
        });
      });
  },
  // 获取猜你喜欢商品
  fetchFeatureGoods(recommendGoodsInfo = {}) {
    const { type = 0, goodsIdList = [] } = recommendGoodsInfo;
    getRecommendGoods({
      type,
      ids: JSON.stringify(goodsIdList),
    })
      .then((res) => {
        const recommendGoods = res
          .filter((v) => !v.isVirtual)
          .map((v) => {
            return {
              ...v,
              price: money(v.price).toYuan(),
              imageUrl: cdnImage(v.imageUrl, '!340x0.jpg'),
            };
          });

        this.setYZData({
          recommendGoods,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  },
  onClose() {
    this.setYZData({
      showCoupon: false,
    });
  },

  // 查看更多
  showMore() {
    if (!this.data.hasMore) {
      return;
    }

    let { id, page, pageSize, winners } = this.data;

    getWinners({
      activityId: id,
      page,
      pageSize,
    })
      .then((res) => {
        res.list.forEach((v) => {
          v.profilePicture = cdnImage(v.profilePicture, '!60x60.jpg');
        });

        winners = this.data.winners.concat(res.list);

        this.setYZData({
          winners,
          total: res.count,
          page: page + 1,
          hasMore: res.count > winners.length,
        });
      })
      .catch((err) => {
        err &&
          wx.showToast({
            title: err,
            icon: 'none',
            duration: 1000,
          });
      });
  },
  // 进店逛逛
  handleGoHomePage() {
    navigate.switchTab({
      url: '/packages/home/<USER>/index',
    });
  },
});
