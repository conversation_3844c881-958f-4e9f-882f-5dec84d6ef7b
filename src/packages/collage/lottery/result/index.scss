.result-header {
  padding-bottom: 20px !important;
}

.result-goods-wrap {
  background-image: url(https://img.yzcdn.cn/public_files/2018/11/08/3633eccdc38fefcf72fb495b2f0ad140.png);
  background-position: 67vw 19.4vw;;
  background-size: 67px auto;
  background-repeat: no-repeat;
}

.winners {
  margin-top: 10px;
  padding: 15px 20px 20px;
  background: #fff;

  &-header {
    display: flex;
    justify-content: center;
    align-items: center;

    .line {
      background: #fd3852;
      border-radius: 6px;
      width: 40px;
      height: 2px;
    }

    .dot {
      transform:rotate(45deg);
      background: #fd3852;
      border-radius: 2px;
      width: 6px;
      height: 6px;
      margin: 0 2px;
    }

    .title {
      margin: 0 10px;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #fd3852;
      text-align: center;
    }
  }

  &-lists {
    margin: 15px 0 20px;
    padding: 20px;
    max-height: 230px;
    overflow: scroll;
    background:#fef0f0;
    border: 1rpx solid #FF5644;
    border-radius:8px;

    .winners-list {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #333;

      &__img {
        border: 2px solid #fd3852;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        box-sizing: border-box;
      }

      &__name {
        width: 80px;
        text-align: center;
      }

      &__tel {
        width: 100px;
        text-align: right;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &-more {
    padding-top: 6px;
    text-align: center;
    font-size: 12px;
    color: #1989FA;
  }

  &__btn {
    background-image: linear-gradient(135deg, #ff5644 0%, #fb1c60 100%);
    box-shadow: 0px 2px 10px rgba(253, 56, 82, 0.3);
    border-radius: 22px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    color: #fff;
    font-size: 16px;
  }
}

.coupon {
  margin: 0 auto;
  padding: 42px 10px;
  background-color: transparent !important;
  width: 315px;
  box-sizing: border-box;
}

.coupon__icon--close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 2px;
  color: #fff;
}

.coupon-popup {
  position: relative;
  width: 295px;
  margin: 0 auto;
  padding: 20px 0 20px;
  background: url(https://img.yzcdn.cn/public_files/2018/08/29/988d6857c3bcdd309637820b63a2f988.png) no-repeat top;
  background-size: auto 100%;
  text-align: center;

  &__amount {
    font-family:PingFangSC-Semibold;
    display: flex;
    align-items: center;
    justify-content: center;

    .number {
      color:#a27524;
      font-size:54px;
      line-height: 60px;
    }

    .unit {
      margin-left: 10px;
      background:#a27524;
      width:30px;
      height:30px;
      font-size: 18px;
      line-height: 30px;
      border-radius: 50%;
      color: #fff;
    }
  }

  &__condition {
    margin-top: 2px;
    font-size: 14px;
    color: #ab9771;
  }

  &__name {
    margin: 42px 0 10px;
    font-size: 16px;
    color:#ab9771;
  }

  &__time {
    margin-bottom: 51px;
    font-size: 12px;
    color:#ab9771;
  }

  &__desc {
    font-size: 14px;
    color: #fff;
    line-height: 20px;
  }

  &__btn {
    margin: 20px auto 0;
    display: block;
    background-image:linear-gradient(90deg, #ffd812 1%, #fbd728 100%);
    border-radius: 43px;
    width: 95%;
    max-width: 255px;
    height: 40px;
    line-height: 40px;
    font-size:14px;
    color:#911414;
  }
}

.goods-wrap__tag {
  position: absolute;
  left: 0;
  top: 3px;
  display: inline-block;
  height: 32px;
  line-height: 32px;
  padding: 0 5px;
  background-image: linear-gradient(135deg, #ff5644 0%, #fb1c60 100%);
  border-radius: 24px;
  text-align: center;
  color: #fff;
  font-size: 20px;
  transform: scale(0.5);
  transform-origin: 0% 0%;
  vertical-align: bottom;
}

.goods-wrap__tag--gray {
  background-image:linear-gradient(135deg, #bcbab9 0%, #9c9497 100%);
}

.tag-void {
  width: 38px;
  display: inline-block;
}