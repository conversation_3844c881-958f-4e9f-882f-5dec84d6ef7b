const app = getApp();

// 获取抽奖拼团结果
export const getLotteryCollageResult = activityId => {
  return app.request({
    path: '/wscump/collage/lottery_result.json',
    data: {
      activityId
    },
    type: 'GET',
    withCredentials: true
  });
};

// 获取抽奖拼团详情
export const getLotteryCollageDetail = params => {
  return app.request({
    path: '/wscump/collage/lottery_detail.json',
    data: params
  });
};

// 判断是否能下单
export const judgePayOrder = params => {
  return app.request({
    path: '/wscump/collage/judge_pay_order.json',
    data: params
  });
};

export const getWinners = params => {
  return app.request({
    path: '/wscump/collage/lottery-winners.json',
    data: params
  });
};

// 获取推荐商品
export const getRecommendGoods = data => {
  return app.request({
    path: '/wscump/lottery-groupon/recommend-goods.json',
    data,
    type: 'GET',
  });
};
