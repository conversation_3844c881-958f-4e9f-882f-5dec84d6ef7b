import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  options: {
    multipleSlots: true // 启用多slot支持
  },
  externalClasses: ['custom-class'],

  properties: {
    // 账户打通场景
    scene: {
      type: String,
      value: ''
    },
    // 订阅消息模板名
    subscribeScene: {
      type: String,
      value: ''
    },
    // 标题
    collageTitle: {
      type: String,
      value: ''
    },
    // 拼团状态
    statusString: {
      type: String,
      value: ''
    },
    mainDesc: {
      type: String,
      value: ''
    },
    subDesc: {
      type: String,
      value: ''
    },
    businessModule: {
      type: String,
      value: ''
    },
    // 剩余人数
    remainNum: {
      type: Number,
      value: 0
    },
    // 团是否在进行中
    isGrouping: {
      type: Boolean,
      value: true
    },
    // 是否为团员
    isJoinedGroup: {
      type: Boolean,
      value: false
    },
    // 团员
    participants: {
      type: Array,
      value: []
    },
    btns: {
      type: Array,
      value: []
    },
    // 倒计时
    countdown: {
      type: Object,
      value: {
        hour: '00',
        minute: '00',
        second: '00'
      },
      observer(val) {
        // ?? ob obj
        if (val.day && Number(val.day)) {
          this.setYZData({
            showDay: true
          });
        }
      }
    }
  },

  data: {
    showDay: false
  },

  methods: {
    handleClick(e) {
      this.triggerEvent('click', e.target);
    },

    onGetUserInfo(e) {
      this.triggerEvent('getuserinfo', e.detail);
    }
  }
});
