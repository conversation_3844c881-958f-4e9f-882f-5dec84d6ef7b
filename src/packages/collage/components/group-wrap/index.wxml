<view class="group">
  <view class="group-header group-header--{{ statusString }}">
    <block wx:if="{{ !statusString }}">
      <view class="line"></view><view class="dot"></view>
        <text class="title">{{collageTitle}}</text>
        <view class="dot"></view><view class="line"></view>
    </block>
    <block wx:else>
      {{ collageTitle }}
    </block>
  </view>

  <!-- 拼团倒计时 -->
  <view class="group__time" wx:if="{{ !statusString }}">
    <block wx:if="{{ showDay }}">
      <text class="left-time">{{ countdown.day }}</text> ：
    </block>
    <text class="left-time">{{ countdown.hour }}</text> ：
    <text class="left-time">{{ countdown.minute }}</text> ：
    <text class="left-time">{{ countdown.second }}</text>
  </view>

  <!-- 团成员 -->
  <avatar-wrap
    wx:if="{{ participants.length }}"
    list="{{ participants }}"
  >
  </avatar-wrap>

  <slot></slot>

  <view class="group__number" wx:if="{{ isGrouping }}">
    {{ isJoinedGroup ? '还差' : '仅剩' }}
    <text class="number">{{ remainNum }}</text>
    人，{{ mainDesc }}
  </view>

  <view class="group__desc" wx:if="subDesc">
    {{ subDesc }}
  </view>

  <view class="btn-wrap">
    <form-view
      wx:for="{{ btns }}"
      wx:for-item="btn"
      wx:key="{{ item.type }}"
      type="{{ businessModule }}">
      <user-authorize
        wx:if="{{ btn.type === 'INVITE' }}"
        authTypeList="{{ ['nicknameAndAvatar'] }}"
        is-once
        data-type="INVITE"
        bindgetuserinfo="onGetUserInfo"
        bindtap="handleClick"
        btn-class="{{ index === 0 ? 'btn-wrap__top' : 'btn-wrap__bottom' }}"
      >
        {{ btn.text }}
      </user-authorize>
      <subscribe-message
        wx:elif="{{ btn.type === 'JOIN' ||  btn.type === 'REOPENING' }}"
        scene="{{ scene }}"
        subscribe-scene="{{ subscribeScene }}"
        data-type="{{ btn.type }}"
        bind:next="handleClick"
        btn-class="{{ index === 0 ? 'btn-wrap__top' : 'btn-wrap__bottom' }}"
      >
        {{ btn.text }}
      </subscribe-message>
      <van-button
        wx:else
        wx:key="index"
        plain="{{ index === 1 }}"
        data-type="{{ btn.type }}"
        bind:click="handleClick"
        hover-class="none"
        custom-class="{{ index === 0 ? 'btn-wrap__top' : 'btn-wrap__bottom' }}"
      >
        {{ btn.text }}
      </van-button>
    </form-view>
  </view>
</view>
