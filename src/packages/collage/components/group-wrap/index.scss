.group {
  margin-top: 10px;
  padding: 15px 20px 20px;
  background: #fff;

  &-header {
    margin: 0 auto 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;

    .line {
      background: #fd3852;
      border-radius: 6px;
      width: 40px;
      height: 2px;
    }

    .dot {
      transform: rotate(45deg);
      background: #fd3852;
      border-radius: 2px;
      width: 6px;
      height: 6px;
      margin: 0 2px;
    }

    .title {
      margin: 0 10px;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #fd3852;
      text-align: center;
    }
  }

  &-header--success {
    align-items: flex-start;
    width: 226px;
    height: 36px;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #fff;
    line-height: 32px;
    background: url(https://b.yzcdn.cn/wsc/20180828/imgs/collage_header_img.png) no-repeat center;
    background-size: 100%;
  }

  &-header--fail {
    font-family: PingFangSC-Semibold;
    font-size: 20px;
    color: #999;
    line-height: 26px;
    margin: 0 auto 20px;
  }

  &-header--lottery {
    align-items: center;
    color: #fff;
    font-size: 14px;
    font-family: PingFangSC-Semibold;
    width: 138px;
    height: 36px;
    line-height: 32px;
    background: url(https://img.yzcdn.cn/public_files/2018/08/28/a05edb8a3102271dc693800f85d700a9.png)
      no-repeat center;
    background-size: 100%;
  }

  &__time {
    margin-bottom: 20px;
    text-align: center;
    font-family: PingFangSC-Medium;
    color: #fd3852;
    font-size: 14px;

    .left-time {
      display: inline-block;
      background: #fd3852;
      border-radius: 6px;
      width: 22px;
      height: 22px;
      line-height: 22px;
      color: #fff;
      text-align: center;
    }
  }

  &__number {
    margin: 0 0 10px;
    font-size: 16px;
    color: #333;
    text-align: center;
    line-height: 22px;

    .number {
      color: #fd3852;
      font-family: PinfFangSC-Semibold;
    }
  }

  &__desc {
    font-size: 12px;
    color: #fd3852;
    text-align: center;
    line-height: 16px;
  }

  .btn-wrap {
    margin-top: 20px;
    font-size: 16px;
    text-align: center;

    &__top {
      background-image: linear-gradient(135deg, #ff5644 0%, #fb1c60 100%);
      box-shadow: 0 2px 10px rgba(253, 56, 82, 0.3);
      color: #fff;
      border: none;
      width: 100%;
      border-radius: 22px;
      height: 44px;
      line-height: 44px;
      box-sizing: border-box;
    }

    &__bottom {
      font-size: 16px;
      margin-top: 10px;
      border: 1px solid #fd3852;
      color: #fd3852;
      width: 100%;
      border-radius: 22px;
      height: 44px;
      line-height: 44px;
      box-sizing: border-box;
    }

    &__item {
      width: 100%;
      border-radius: 22px;
      height: 44px;
      line-height: 44px;
      box-sizing: border-box;
    }

    &__top::after,
    &__bottom::after {
      background-color: none !important;
      border-color: none !important;
    }

    &__top:active::after,
    &__bottom:active::after {
      opacity: 0;
    }
  }
}
