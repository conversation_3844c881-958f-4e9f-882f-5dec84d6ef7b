.root {
  position: relative;
  padding: 20px 20px 0;
  background-color: #fff;
  overflow: hidden;

  &__bg {
    position: absolute;
    top: 0;
    left: -5%;
    right: 0;
    margin: 0 auto;
    width: 110%;
    height: 116px;
    border-radius: 0 0 60% 60%;
    background-image: linear-gradient(180deg, #ff5644 0%, #fb1c60 100%);
    z-index: 0;
  }

  &__bg--gray {
    background-image:linear-gradient(180deg, #bcbab9 0%, #9c9497 100%);
  }

  &-title {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 20px;
    font-family:PingFangSC-Medium;
    font-size:16px;
    color: #fff;
    text-align: center;
    line-height: 24px;
    z-index: 1;

    .unit {
      font-size: 12px;
      color: #fff;
      text-align: center;
      line-height: 16px;
    }
  }

  &__time {
    margin: 0 5px;
    background: #fff;
    border-radius: 6px;
    width:22px;
    height:22px;
    line-height: 22px;
    font-size: 14px;
    color: #333333;
    text-align: center;
  }

  &-title--18 {
    font-size: 18px;
  }
}

.goods-wrap {
  position: relative;
  padding: 10px;
  display: flex;
  align-items: flex-start;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  z-index: 1;

  &__img {
    margin-right: 10px;
    width: 220rpx;
    height: 220rpx;
    border-radius: 4px;
    overflow: hidden;

    image {
      width: 100%;
      height:100%;
      border-radius: 4px;
    }
  }

  &-detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 220rpx;

    .detail__title {
      position: relative;
      font-size: 14px;
      line-height: 20px;
      color: #333;
      text-overflow: ellipsis;
      overflow : hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .detail__desc {
      margin: 6px 0 22px;
      display: inline-block;
      font-size: 12px;
      line-height: 16px;
      color: #999;
    }

    .detail-price {
      margin-bottom: 2px;
      font-size: 12px;

      &__current {
        font-family: PingFangSC-Medium;
        color: #f44;
        .bold {
          font-size: 18px;
        }
      }

      &__original {
        margin-left: 5px;
        color: #666;
        text-decoration: line-through;
      }

      &__gray {
        color: #999;
      }
    }
  }
}
