<view class="root custom-class">
  <!-- 背景 -->
  <view class="root__bg {{ isGrouponFailed ? 'root__bg--gray' : '' }}"></view>

  <!-- 活动倒计时 -->
  <view class="root-title" wx:if="{{ countdown }}">抽奖倒计时 
    <text class="root__time">{{ countdown.day }}</text><text class="unit">天</text>
    <text class="root__time">{{ countdown.hour }}</text><text class="unit">时</text>
    <text class="root__time">{{ countdown.minute }}</text><text class="unit">分</text>
    <text class="root__time">{{ countdown.second }}</text><text class="unit">秒</text>
  </view>

  <view
    class="root-title root-title--18"
    wx:elif="{{ title }}"
  >
    {{ title }}
  </view>

  <!-- 商品信息 -->
  <navigator
    url="{{ goods.url }}"
    class="goods-wrap goods-class"
    hover-class="none"
  >
  
    <!-- 图片 -->
    <view class="goods-wrap__img">
      <image mode="aspectFill" src="{{goods.picture}}" />
    </view>

    <!-- 标题 -->
    <view class="goods-wrap-detail">
      <view class="detail__title van-multi-ellipsis--l2">
        <slot name="title-icon"></slot>
        {{ goods.title }}
      </view>

      <!-- sku信息 -->
      <text
        class="detail__desc"
        wx:if="{{ goods.skuStr }}"
      >
        {{ goods.skuStr}}
      </text>

      <!-- 价格 -->
      <view class="detail-price">
        <text class="detail-price__current {{ isGrouponFailed ? 'detail-price__gray' : '' }}">
          ￥<text class="bold">{{ goods.price[0] }}</text>
          <block wx:if="{{ goods.price[1] }}">.{{ goods.price[1] }}</block>
        </text>
        <text class="detail-price__original">￥{{ goods.originPrice }}</text>
      </view>
    </view>
  </navigator>

  <slot></slot>
</view>