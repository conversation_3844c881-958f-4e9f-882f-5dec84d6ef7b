.avatar-wrap {
  margin: 0 auto 26px;
  display: flex;
  justify-content: center;
  align-items: center;

  &-item {
    position: relative;
    width: 40px;
    height: 60px;
    border-radius: 50%;
    box-sizing: border-box;

    .item__img {
      width: 60px;
      height: 100%;
      border: 2px solid #fd3852;
      border-radius: 50%;
      box-sizing: border-box;
    }

    .item__img--noborder {
      border: none;
    }

    .item__img--more {
      width: 50px;
      height: 50px;
      margin: 5px 0;
    }

    .item__tag {
      position: absolute;
      left: 0;
      bottom: -9px;
      margin: 0 auto;
      font-size: 20px;
      width: 60px;
      height: 28px;
      line-height: 28px;
      transform: scale(0.5);
      text-align: center;
      background-image: linear-gradient(135deg, #ff5644 0%, #fb1c60 100%);
      border-radius:16px;
      color: #fff;
    }

    &:last-child {
      margin-right: 20px;
    }
  }

  &-item--more {
    width: 70px;
    text-align: right;
  }
}