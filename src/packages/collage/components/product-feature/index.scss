.root{
  padding: 20px 15px;
}

.tip {
  display: flex;
  align-items: center;
  font-size:14px;
  color:#999;
  letter-spacing:0;
  text-align:center;
  position: relative;
}

.tip::after, .tip::before {
  flex: 1;
  content: '';
  display: inline-block;
  height: 2px;
  position: relative;
  opacity: 0.5;
}

.tip::before {
  border-top-left-radius: 50%;
  border-bottom-left-radius: 50%;
  margin-right: 24px;
  background: #E5E5E5;

}

.tip::after {
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
  margin-left: 24px;
  background: #E5E5E5;
}

.content-block{
  padding: 5px 10px;
}

.goods-block{
  width: calc(50% - 2px);
  margin-bottom:4px;
  background: #FFF;
}

.goods-image{
  width: 100%;
  height: 170px;
  overflow: hidden;
  position: relative;

  image {
    max-height: 100%;
    max-width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }
}

.goods-title {
  margin-bottom: 5px;
  height: 34px;
  line-height: 17px;
  font-size: 12px;
  color: #333;
  text-overflow: ellipsis;
  overflow : hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-price{
  color:#FF423D;
  font-size: 14px;
}

.goods-container{
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}