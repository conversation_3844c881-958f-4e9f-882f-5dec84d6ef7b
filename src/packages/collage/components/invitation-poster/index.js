import WscComponent from 'pages/common/wsc-component/index';
import authorize from '@/helpers/authorize';
import { fetchQRCode } from '@/helpers/fetch';
import { promisifyWxStyle } from 'utils/promisify';

const app = getApp();
const loadImage = src => app.downloadFile({ url: src })
  .then(result => result.tempFilePath);

/**
 * 通信事件1：close  关闭上拉菜单
 * 通信事件2：drawposter 绘制海报
 */
WscComponent({
  externalClasses: ['canvas-class', 'close-class', 'custom-class', 'poster-class'],
  options: {
    multipleSlots: true // 启用多slot支持
  },
  properties: {
    // 商品图片
    picture: {
      type: String,
      value: ''
    },
    // 页面路由
    pageUrl: {
      type: String,
      value: ''
    },
    // 页面参数
    pageQuery: {
      type: Object,
      value: {}
    },
    // 展示上拉菜单
    showPopup: {
      type: Boolean,
      value: false
    },
    // 是否展示吸底按钮
    showBottomBtn: {
      type: Boolean,
      value: false
    },
    // 上拉菜单数据
    actions: {
      type: Array,
      value: []
    }
  },
  data: {
    isShowInvitePost: false,
    shareImgSrc: '',
    loadedThumbUrl: '',
    loadedQrCodeUrl: ''
  },
  methods: {
    // 生成海报
    generateInvitePost() {
      this.isDrawing = true;
      const {
        picture, pageUrl, pageQuery
      } = this.data;
      const pictureUrl = picture.replace(/^http(?!s)/, 'https');

      wx.showLoading({ title: '正在生成' });
      let fetchQRCodeUrl = fetchQRCode(pageUrl, pageQuery);
      Promise.all([
        loadImage(pictureUrl),
        fetchQRCodeUrl.then(loadImage)
      ])
        .then(([loadedThumbUrl, loadedQrCodeUrl]) => this.setYZData({ loadedThumbUrl, loadedQrCodeUrl }))
        .then(() => this.drawShareImage())
        .then(() => promisifyWxStyle(wx.canvasToTempFilePath)({ canvasId: 'invitePostCanvasId' }, this))
        .then(res => {
          this.setYZData({
            shareImgSrc: res.tempFilePath
          });
        })
        .then(() => {
          wx.hideLoading();
          this.setYZData({
            isShowInvitePost: true
          });
          this.triggerEvent('enddraw', 'success');
        })
        .catch(() => {
          wx.hideLoading();
          wx.showToast({
            title: '生成卡片失败:',
            icon: 'none',
            duration: 2000
          });
          this.triggerEvent('enddraw', 'fail');
        });
    },
    closeInvitePostModal() {
      this.setYZData({ isShowInvitePost: false });
    },
    // 保存海报
    clickSaveImage() {
      const { data: { shareImgSrc } } = this;
      if (!shareImgSrc) return;

      wx.showLoading({ title: '保存中' });
      authorize('scope.writePhotosAlbum')
        .then(() => {
          promisifyWxStyle(wx.saveImageToPhotosAlbum)({ filePath: shareImgSrc })
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000
              });
              this.closeInvitePostModal();
            }).catch(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000
              });
            });
        })
        .catch((e) => {
          console.error(e);
          wx.hideLoading();
          wx.showModal({
            content: '需要同意将分享图片保存到相册，点击确定后跳转至设置页操作',
            success: (e) => {
              if (e.cancel) return;
              wx.openSetting({
                success: ({ authSetting }) => {
                  if (authSetting['scope.writePhotosAlbum']) {
                    this.clickSaveImage();
                  }
                }
              });
            }
          });
        }).catch((err) => {
          console.log(err);
        });
    },
    // 绘制海报
    drawShareImage() {
      const {
        loadedThumbUrl,
        loadedQrCodeUrl,
      } = this.data;
      const ctx = wx.createCanvasContext('invitePostCanvasId', this);
      this.triggerEvent('drawposter', {
        ctx,
        loadedQrCodeUrl,
        loadedThumbUrl
      });
      return new Promise(res => {
        setTimeout(() => {
          ctx.draw(true, res);
        }, 1000);
      });
    },
    onSelect(e) {
      this.onClose();
      const { type } = e.currentTarget.dataset;

      if (type === 'share') {
        return;
      }
      this.triggerEvent('begindraw');
      this.generateInvitePost();
    },
    onClose() {
      this.triggerEvent('close', false);
    }
  }
});
