.invite-post-modal-wrapper {
  position: fixed;
  height: 100%;
  width: 100%;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
}

.invite-post-modal-wrapper__invite-post-modal {
  position: relative;
  width: 263px;
  top:-6px;
  left:6px;
}

.invite-post-modal-wrapper__content{
  display: flex;
  flex-direction: column;
  margin: 12px 12px 0 0;
  align-items: center;
  justify-content: flex-start;
}

.invite-post-modal-wrapper__cancel-button{
  position: absolute;
  right: 0;
  height: 25px;
  width: 25px;
  background-size:cover;
  background-image: url("data:image/svg+xml, %3Csvg width='29' height='29' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Ccircle id='b' cx='12.5' cy='12.5' r='12.5'/%3E%3Cfilter x='-14%25' width='128%25' height='128%25' filterUnits='objectBoundingBox' id='a'%3E%3CfeOffset dy='1' in='SourceAlpha' result='shadowOffsetOuter1'/%3E%3CfeGaussianBlur stdDeviation='1' in='shadowOffsetOuter1' result='shadowBlurOuter1'/%3E%3CfeColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.197973279 0' in='shadowBlurOuter1'/%3E%3C/filter%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg transform='translate(2 1)'%3E%3Cuse fill='%23000' filter='url(%23a)' xlink:href='%23b'/%3E%3Cuse fill='%23FFF' xlink:href='%23b'/%3E%3C/g%3E%3Cpath d='M14.5 12.793L10.257 8.55l-.707.707 4.243 4.243-4.243 4.243.707.707 4.243-4.243 4.243 4.243.707-.707-4.243-4.243 4.243-4.243-.707-.707-4.243 4.243z' fill='%23999'/%3E%3C/g%3E%3C/svg%3E");
}

.invite-post{
  width: 375px;
  height: 568px;
  transform: scale(0.65);
  position: absolute;
  left: -9999px;
}

.invite-post-btn {
  width: 244px;
}

.invite-post__save-button{
  margin: 24px 0 0 0;
  width: 100%;
}

.invite-post__save-button--bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: #fff;
  color: #333;
  font-size: 16px;

}

.share-image{
  width: 244px;
  height: 370px;
  background:#f5f5f5;
}

.share-image image {
  width: 100%;
  height: 100%;
}

.invite-popup {
  font-size: 16px;
  color: #333;
  text-align: center;
  background: #F2F2F2;
}

.invite-btn {
  height: 50px;
  line-height: 50px;
  border: none;
  border-radius: 0;
  background: #fff;
  font-size: 16px;
}

.invite-btn::after {
  border: 0;
}

.invite-btn:first-child {
  border-bottom: 1px solid #e5e5e5;
}

.invite-btn--cancel {
  margin-top: 10px;
}