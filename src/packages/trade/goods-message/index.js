import WscPage from 'pages/common/wsc-page/index';

import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

const app = getApp();

WscPage({
  data: {
    goods: {}
  },

  onLoad(query) {
    var goodsKey = query.goods;
    var goods = app.db.get(goodsKey);

    goods.message = this.formatMessage(goods.message);

    this.setYZData({
      goods
    });
  },
  // 对留言的类型做个区分
  formatMessage(messages = []) {
    messages.forEach((item) => {
      var value = item.value;
      var type = /^\s*http(s)*:\/\/.+/.test(value) ? 'image' : 'text';
      item.type = type;

      if (type == 'image') {
        item.preview = cdnImage(value, '!200x200.jpg');
      }
    });

    return messages;
  },

  previewImg(e) {
    var src = e.currentTarget.dataset.src;

    wx.previewImage({
      current: src,
      urls: [src]
    });
  },

  navigateBack() {
    wx.navigateBack();
  }
});
