<page-container
  forbid-copyright
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view class="message zan-panel van-c-black">
    <view class="goods-card zan-card goods-card--first">
      <view class="zan-card__thumb">
        <image class="zan-card__img" src="{{ goods.imgUrl }}" mode="aspectFit"></image>
      </view>
      <view class="zan-card__detail">
        <view class="zan-card__detail-row">
          <view class="zan-card__right-col">￥{{ goods.payPriceStr }}</view>
          <view class="zan-card__left-col van-multi-ellipsis--l2">
            {{ goods.title }}
          </view>
        </view>

        <view class="zan-card__detail-row van-c-gray-darker">
          <view class="zan-card__right-col">x{{ goods.num }}</view>
          <view class="zan-card__left-col goods-card__detail-sku">
            {{ goods.skuStr }}
          </view>
        </view>

      </view>
    </view>

    <view class="message__form">
      <block wx:for="{{ goods.message }}" wx:key="index" wx:item="item">
        <view class="zan-cell zan-field">
          <view class="zan-field__title">{{ item.name }}</view>
          <view class="zan-cell__bd zan-field__input van-c-gray-darker">
            <block wx:if="{{ item.type == 'image'}}">
              <image
                src="{{ item.preview }}"
                mode="scaleToFill"
                catchtap="previewImg"
                data-src="{{ item.value }}"
                style="width: 70px;height: 70px;"
              />
            </block>
            <block wx:else>
              {{ item.value ? item.value : '无' }}
            </block>
          </view>
        </view>
      </block>
    </view>

  </view>

  <!-- 行动按钮 -->
  <view class="zan-btns">
    <button class="zan-btn" catchtap="navigateBack">查看订单详情</button>
  </view>
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
