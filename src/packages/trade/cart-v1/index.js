import WscPage from 'pages/common/wsc-page/index';
import pageCreator from 'shared/components/cart/creator';
import Theme from 'shared/common/components/theme-view/theme';
import args from '@youzan/weapp-utils/lib/args';
import initSAASProcess, {
  initCartProcess,
} from 'shared/components/cart/saas/index';
import navigate from '@/helpers/navigate';

const extendSAASProcess = ECLOUD_MODE ? initSAASProcess : initCartProcess; //eslint-disable-line

const basePage = pageCreator();

WscPage(basePage, extendSAASProcess, {
  onLoad(options = {}) {
    this.emptyJumpUrl = options.back;
    this.isDirty = true;

    Theme.getThemeColor('main-bg').then((color) => {
      const rgb = Theme.switchHexToRgb(color);

      this.setData({
        themeMainBgColor: color,
        themeMainBgAlpha10Color: `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, .1)`,
      });
    });

    this.on(
      'order:leave:stop',
      (data) => {
        const { displayData, orderData } = data || {};
        const { bookKey } = orderData || {};
        if (bookKey === this.data.bookKey) {
          // 说明对应一个页面
          this.setYZData({
            orderKeepData: orderData, // 订单状态
            displayOrderKeepData: displayData,
            reOrder: true, // 再次进入下单页
            showOrderkeep: true,
          });
        }
      },
      this
    );
    this.on('trade:order:create', this.onCartChange, this);
    this.on('component:sku:cart', this.onCartChange, this);
  },

  onUnload() {
    // app.off('order:leave:stop', null, this);
  },

  onShow() {
    if (this.isDirty) {
      this.cartProcess.refreshCartData();
      this.isDirty = false;
    }
  },

  refreshCartData() {
    this.cartProcess.refreshCartData();
  },

  onPullDownRefresh() {
    const recommendGoods = this.selectComponent('#recommend-goods');
    recommendGoods && recommendGoods.refresh && recommendGoods.refresh();
  },

  onCartChange() {
    this.isDirty = true;
  },

  goHome() {
    const { emptyJumpUrl } = this;
    wx.reLaunch({
      url: emptyJumpUrl
        ? `/${emptyJumpUrl}`.replace('//', '/')
        : '/packages/home/<USER>/index',
    });
  },

  goGoodsDetail({ detail: { alias } }) {
    navigate.navigate({
      url: `/pages/goods/detail/index?alias=${alias}`,
    });
  },

  handleCloseSeparateBuyPopup() {
    this.setYZData({
      'separateBuy.show': false,
    });
  },

  orderKeepClose() {
    this.setYZData({
      showOrderkeep: false,
    });
  },

  orderKeepConfirm() {
    this.setYZData({
      showOrderkeep: false,
    });
    const { orderKeepData, reOrder } = this.data;
    const { bookKey, addressId, orderFrom, orderNo } = orderKeepData;
    let params;
    if (orderNo) {
      // 是否已生单
      params = {
        addressId,
        orderFrom,
        orderNo,
        reOrder,
      };
    } else {
      params = {
        addressId,
        orderFrom,
        bookKey,
        reOrder,
      };
    }
    navigate.navigate({
      url: args.add('/packages/order/index', params),
    });
  },
});
