<import src="./custom-tpl.wxml" />

<page-container fixed-bottom isTabPage>
  <view class="{{ isFetching ? 'skeleton' : '' }}">
    <block wx:for="{{ design }}" wx:key="type">
      <block wx:if="{{ shopList.length || unAvailableGoodsList.length }}">
        <shop-header
          wx:if="{{ item.type === 'shop-header' }}"
          show-edit="{{ editShopIndex === -1 || editShopIndex === 0 }}"
          edit-mode="{{ editShopIndex === 0 }}"
          shop-name="{{ shopList[0].shopName }}"
          checked-all="{{ shopList[0].checkedAll }}"
          data-shop-index="{{ 0 }}"
          bind:edit-mode-change="handleEditModeChange"
          bind:shop-check-all="handleShopCheckAll"
        />

        <valid-block
          wx:if="{{ item.type === 'valid-block' }}"
          design-children="{{ item.children }}"
          class="valid-block"
          wx:for="{{ shopList }}"
          wx:for-item="shop"
          wx:key="kdtId"
          data-shop-index="{{ index }}"
          shop="{{ shop }}"
          un-available-goods-list="{{ unAvailableGoodsList }}"
          edit-mode="{{ editShopIndex === index }}"
          show-edit="{{ editShopIndex === -1 || editShopIndex === index }}"
          theme-main-bg-color="{{ themeMainBgColor }}"
          theme-main-bg-alpha-10-color="{{ themeMainBgAlpha10Color }}"
          bind:edit-mode-change="handleEditModeChange"
          bind:shop-check-all="handleShopCheck"
          bind:change-item-checked="handleItemChecked"
          bind:change-batch-checked="handleBatchChecked"
          bind:change-item-num="handleItemNumChange"
          bind:change-item-delete="handleItemDelete"
          bind:goods-img-click="goGoodsDetail"
          bind:refresh-cart="refreshCartData"
          bind:change-goods-sku="fetchGoodsSku"
          bind:handle-show-popup="handleShowPopup"
          bind:activity-btn-click="activityBtnClick"
        />

        <invalid-block
          wx:if="{{ item.type === 'invalid-block' }}"
          custom-class="{{ shopList.length ? '' : 'no-valid-goods' }}"
          goods-list="{{ unAvailableGoodsList }}"
          theme-main-bg-color="{{ themeMainBgColor }}"
          data-shop-index="{{ -1 }}"
          bind:goods-img-click="goGoodsDetail"
          bind:clear="handleUnavailableClear"
          bind:change-item-delete="handleItemDelete"
        />

        <submit-block
          wx:if="{{ item.type === 'submit-block' }}"
          edit-mode="{{ editShopIndex !== -1 }}"
          total-price="{{ totalCheckedPrice }}"
          cart-tips="{{  cartTips }}"
          total-num="{{ totalCheckedNum }}"
          checked-all="{{ checkedAll }}"
          is-cross-shop="{{ shopList.length > 1 }}"
          has-haitao-goods="{{ hasHaitaoGoods }}"
          loading="{{ submitLoading }}"
          bind:check-all-goods="handleAllShopCheck"
          bind:pay-checked-goods="handlePayCheckedGoods"
          bind:batch-delete-goods="handleBatchDelete"
        />
      </block>

      <block wx:if="{{ item.type === 'empty-block' }}">
        <empty-block
          wx:if="{{ !shopList.length && !unAvailableGoodsList.length }}"
          bind:jump-click="goHome"
        />
      </block>

      <block wx:if="{{ item.custom }}">
        <template is="{{ item.type }}" />
      </block>
    </block>

    <ump-info-popup
      activity="{{ activityInfo }}"
      show="{{ showPopup }}"
      theme-main-bg-color="{{ themeMainBgColor }}"
      theme-main-bg-alpha-10-color="{{ themeMainBgAlpha10Color }}"
      bind:close="closePopup"
    />

    <exchange-modal
      disabled="{{ false }}"
      kdtId="{{ shopList[0].kdtId }}"
      activity-type="24"
      has-theme="{{ true }}"
      is-show="{{ showExchangeModal }}"
      activity-id="{{ activityInfo.activityId }}"
      activity-desc="{{ exchangeModalDesc }}"
      plus-buy-goods="{{ plusBuyGoods }}"
      bind:exchange:close-modal="closeExchange"
      bind:exchange:add-succeed="refreshCartData"
    />

  </view>

  <shopping-list wx:if="{{ !isFetching && CURRENT_GLOBAL_SHOP.wechat_sync_shopping_list }}" style-set="{{ 1 }}"/>

  <cps-goods-recommend cpsConfigKey="cps_goods_recommend_shopping_cart" />
  <flow-entrance-banner biz-name="cart" />

  <recommend-goods
    wx:if="{{ !isCrossShop && allGoodsIds }}"
    id="recommend-goods"
    biz-name="cart"
  />
  <custom-tab-bar></custom-tab-bar>
</page-container>

<separate-buy-popup
  separate-buy="{{ separateBuy }}"
  bind:buy="handleSeparateBuy"
  bind:close-separate-buy-popup="handleCloseSeparateBuyPopup"
/>

<van-dialog id="van-dialog" show-cancel-button />
<van-toast id="van-toast" />
<order-keep show="{{ showOrderkeep }}" display-data="{{ displayOrderKeepData }}" bind:close="orderKeepClose" bind:confirm="orderKeepConfirm"/>

<base-sku
  theme-class="{{ themeClass }}"
  goods="{{ skuDataGoods }}"
  sku="{{ skuData }}"
  properties="{{ goodsProperties }}"
  show="{{ isShowSkuPopup }}"
  initial-sku="{{ initialSku }}"
  message-config="{{ skuMessageConfig }}"
  extra-data="{{ skuExtraData }}"
  generic:sku-header-price="custom-sku-header-price"
  buy-text="确定"
  quota="1"
  show-add-cart-btn="{{ false }}"
  reset-stepper-on-hide="{{ true }}"
  show-add-cart-btn="{{ false }}"
  show-buy-btn
  bind:buy="onConfirmSku"
  bind:sku-close="onSkuClose"
  bind:sku-prop-selected="onAsyncPropPrice"
  bind:sku-selected="onAsyncPropPrice"
  hide-stock="{{ skuData.hideStock }}"
/>
