import { VanxPage } from 'pages/common/wsc-page/index';
import args from '@youzan/weapp-utils/lib/args';
import pageComponentBehavior from '@/custom-tab-bar-v2/native-helper/page-component-behavior';

const app = getApp();

VanxPage({
  behaviors: [pageComponentBehavior],
  async onLoad(query) {
    wx.redirectTo({
      url: args.add('/packages/trade/order/list-native/index', query),
    });
  },
});
