import { VanxPage } from 'pages/common/wsc-page/index';
import { mapState, mapGetters } from '@youzan/vanx';
import navigate from '@/helpers/navigate';
import * as processUtils from './common/process';
import Toast from '@vant/weapp/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { PAGE_TYPE, couponCodeMap, CRM_OFFLINE_TYPE } from './common/constant';
import store, { resetStore } from './store';
import api from './common/api';
import { setNavigationBarTitle } from '@youzan/tee-api';
import args from '@youzan/weapp-utils/lib/args';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import spm from 'shared/utils/spm';
import { getHeight } from 'shared/utils/nav-config';
import pageComponentBehavior from '@/custom-tab-bar-v2/native-helper/page-component-behavior';
/* #ifdef BUILD_ENV=youzanyun */
import SaasPageConfig from './saas/page-config';
/* #endif */
import { enterShopApollo, accordSkyLogger } from '@youzan/tee-chain-store';
import { isNewIphone } from 'shared/utils/browser/device-type';

const getIsShowTabbar = (tabbarList) => {
  return tabbarList.some(item => item.pagePath === 'packages/trade/order/list/index');
}
const app = getApp();
let extendPageConfig = {};
/* #ifdef BUILD_ENV=youzanyun */
extendPageConfig = SaasPageConfig;
/* #endif */

VanxPage(extendPageConfig, {
  behaviors: [pageComponentBehavior],
  store,

  mapData: {
    ...mapState([
      'dataLoaded',
      'tab',
      'list',
      'pageType',
      'design',
      'isDrug',
      'isRetailWeappScene',
    ]),
    ...mapGetters([
      'selectedTabId',
      'showEmpty',
      'showNoMore',
      'showRecommend',
      'hasPhoneBlock',
      'hasSearchBlock',
    ]),
  },

  data: {
    themeClass: app.themeClass,
    themeGeneralColor: '',
    bindPhone: {
      show: false,
      tips: '找不到订单？绑定手机号试试',
    },
    showSelfFetchPopup: false,
    selfFetchInfo: false,
    isTabPage: false,
    tabClass: '',
    keyword: '',
    kdtId: '',
    navigateType: '',
    welikeEntryShow: false,
    globalCustomLoading: true,
    isNewIphone: isNewIphone(),
    isShowTabbar: false,
  },

  async onLoad(query) {
    this.isOnLoad = true;
    this.setYZData({
      navHeight: this.data.design?.length ? 0 : getHeight(),
      globalCustomLoading: app.globalData.globalCustomLoading,
    });
    const {
      pagetype = PAGE_TYPE.SINGLE,
      scene,
      refresh = false,
      isDrug = false,
      keyword,
      kdt_id,
      navigateType,
    } = query;
    let { type = 'all' } = query;
    const { selectedTabId } = this.data;
    // 扫码购设置过滤规则
    if (scene === 'onlineScanBuy') {
      this.$commit('SET_CALLER', 'online_scan_buy');
    }
    if (app.getShopInfoSync()?.shop_type === 7) {
      const isRetailUsercenterTemplate =
        getApp().getShopConfigDataSync()?.retail_weapp_usercenter_template ===
        '0';
      if (isRetailUsercenterTemplate) {
        type = 'current';
        wx.setNavigationBarTitle({
          title: '',
        });
        this.$commit('UPDATE_IS_RETAIL_WEAPP_SCENE', true);
      }
    }
    this.$dispatch('GET_USER_GRAY').then(() => {});
    this.$dispatch('GET_DESIGN').then(() => {
      const { list } = this.data.tab;
      const activeIndex = list && list.findIndex((item) => item.id === type);
      const data = {
        selectedId: type,
        activeIndex,
        show: activeIndex !== -1,
      };

      this.$commit('UPDTATE_TAB', data);
      this.$commit('UPDATE_PAGE_TYPE', pagetype);
      this.$commit('UPDATE_IS_DRUG', isDrug);
      this.setListPaddingTop();

      app.isSwitchTab().then((isTabPage) => {
        this.setYZData({
          isTabPage,
        });
      });
      this.setTabClass();
    });

    this.$dispatch('FETCH_DIRECT_BUY_AGAIN_BTN_CONFIG');

    if (isDrug) {
      setNavigationBarTitle('需求清单');
    }
    this.setYZData({
      themeGeneralColor: 'var(--main-bg, #323233)',
    });

    // 从其他页面跳订单列表 订单列表第一页不会刷新的原因是因为page第一次加载的时候为1，第二次从2开始
    if (refresh === 'true') {
      this.$commit('RESET_TAB', selectedTabId);
    }
    // 搜索页新增
    if (keyword) {
      const searchCondition = {
        value: keyword,
      };
      this.$commit('UPDATE_SEARCH_CONDITION', searchCondition);
      this.setYZData({
        keyword,
      });
    }
    // 搜索页跳转 为了解决性能问题 需要不同的跳转方式
    if (navigateType) {
      this.setYZData({
        navigateType,
      });
    }
    if (kdt_id) {
      this.setYZData({
        kdtId: kdt_id,
      });
    }
    app
      .getPointsName()
      .then(({ pointsName = '积分' }) => {
        this.$commit('UPDATE_POINT_NAME', pointsName);
        this.fetchMore();
      })
      .catch(() => {
        this.$commit('UPDATE_POINT_NAME', '积分');
        this.fetchMore();
      });

    app.once('custom-tab-bar:nav:resolved', (tabbarList) => {
      this.setYZData({
        isShowTabbar: getIsShowTabbar(tabbarList),
      });

    });
    if (app?.globalData?.tabbarOriginList) {
      this.setYZData({
        isShowTabbar: getIsShowTabbar(app?.globalData?.tabbarOriginList),
      });
    }
  },

  onShow() {
    this.setYZData({
      'bindPhone.show': !app.getBuyerId(),
    });
    // 返回场景更新店铺id
    this.updateKdtId(this._cacheKdtId, false, {
      mark: '923',
      type: 'back',
    });
    this.setListPaddingTop();
    this.setTabClass();
    // 判断是否是中台化搜索项目back，为了解决列表页返回列表页不刷新问题
    this.orderSearchBack();
    // 从订单详情back回来需要刷新
    app.once('ranta-detail-page-back', () => {
      this.onPullDownRefresh();
    });
    this.off('trade:order:paid', null);
    this.off('trade:evaluate:create', null);
    this.off('trade:evaluate:add', null);
    app
      .resolveTeeAPI()
      .then((api) => api.getUserInfo({ kdtId: app.getKdtId(), cache: false }))
      .then((res) => {
        // 未授权手机号的场景才需要监听，不然会导致每次商详返回都会重新拉接口
        if (!res?.state?.mobile) {
          this.on('app:token:success', () => {
            // 跳转切店不刷新页面
            if (this._cacheKdtId) return;
            this.onPullDownRefresh();

            this.setYZData({
              'bindPhone.show': !app.getBuyerId(),
            });

            this.setListPaddingTop();
            this.setTabClass();
          });
        }
      });
  },

  onUnload() {
    resetStore();
  },

  onPullDownRefresh() {
    this.refreshList();
  },

  onReachBottom() {
    this.fetchMore();
  },

  orderSearchBack() {
    const cbFun = (useCacheOrderListData = false) => {
      if (!this.isOnLoad && !useCacheOrderListData) {
        const searchCondition = {
          value: '',
        };
        this.$commit('UPDATE_SEARCH_CONDITION', searchCondition);
        const { selectedTabId } = this.data;
        this.$commit('RESET_TAB', selectedTabId);
        this.fetchMore(true, true);
      } else {
        this.isOnLoad = false;
      }
    };

    api
      .getUseCacheOrderListData()
      .then(({ value }) => {
        cbFun(value);
      })
      .catch(() => {
        cbFun();
      });
  },
  // 根据顶部动态设置 tab 的 class
  setTabClass() {
    const { bindPhone, hasPhoneBlock, hasSearchBlock, isRetailWeappScene } =
      this.data;
    let tabClass = 'page-header-tab';
    if (isRetailWeappScene) {
      tabClass = 'page-retail-header';
    } else if (bindPhone.show && hasPhoneBlock) {
      if (hasSearchBlock) {
        tabClass = 'page-header-tab';
      } else {
        tabClass = 'page-search';
      }
    } else if (hasSearchBlock) {
      tabClass = 'page-header';
    } else {
      tabClass = 'page-phone';
    }
    this.setYZData({
      tabClass,
    });
  },

  // 根据顶部动态设置 list 的 padding-top
  setListPaddingTop() {
    const { bindPhone, tab, design, hasPhoneBlock, hasSearchBlock } = this.data;
    const showBindPhone = bindPhone.show && hasPhoneBlock;
    const hasTab =
      design && design.findIndex((item) => item.type === 'list-tabs');
    const showTab = hasTab > -1 && tab.show && !this.data.isDrug;

    const COMP_HEIGHT = {
      PHONE: 46,
      SEARCH: 54,
      TAB: 44,
      DEFAULT: 0,
    };

    let paddingTop = COMP_HEIGHT.DEFAULT;
    if (showBindPhone) {
      if (hasSearchBlock) {
        if (showTab) {
          paddingTop = COMP_HEIGHT.PHONE + COMP_HEIGHT.SEARCH + COMP_HEIGHT.TAB;
        } else {
          paddingTop = COMP_HEIGHT.PHONE + COMP_HEIGHT.SEARCH;
        }
      } else if (showTab) {
        paddingTop = COMP_HEIGHT.PHONE + COMP_HEIGHT.TAB;
      } else {
        paddingTop = COMP_HEIGHT.PHONE;
      }
    } else if (hasSearchBlock) {
      if (showTab) {
        paddingTop = COMP_HEIGHT.SEARCH + COMP_HEIGHT.TAB;
      } else {
        paddingTop = COMP_HEIGHT.SEARCH;
      }
    } else if (showTab) {
      paddingTop = COMP_HEIGHT.TAB;
    } else {
      paddingTop = COMP_HEIGHT.DEFAULT;
    }

    this.setYZData({
      listPaddingTop: paddingTop + 'px',
    });
  },

  // tab 切换
  handleTabChange(event) {
    const activeIndex = event.detail.index;
    const newSelectedId = this.data.tab.list[activeIndex].id;
    const oldSelectedId = this.data.selectedTabId;
    if (oldSelectedId === newSelectedId) {
      return;
    }

    this.$commit('UPDTATE_TAB', {
      selectedId: newSelectedId,
    });

    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    });

    this.refreshList();
  },

  // 刷新列表
  refreshList() {
    const { selectedTabId } = this.data;
    this.$commit('RESET_TAB', selectedTabId);
    this.fetchMore(true);
  },

  /**
   * 列表中某项状态改变
   * 1. "全部"列表中更新
   * 2. 非"全部"列表中删除
   */
  onListItemStatusChange(args) {
    return processUtils.refreshListItem(args);
  },

  // 列表分页加载
  fetchMore(isRefresh, clearStorage) {
    return this.$dispatch('FETCH_ORDER_LIST')
      .then(() => {
        this.$commit('SET_DATA_LOADED', true);
        isRefresh && wx.stopPullDownRefresh();
        clearStorage &&
          wx.setStorage({
            key: 'isOrderList',
            data: false,
          });
      })
      .catch(() => {
        this.$commit('SET_DATA_LOADED', true);
        isRefresh && wx.stopPullDownRefresh();
        clearStorage &&
          wx.setStorage({
            key: 'isOrderList',
            data: false,
          });
      });
  },

  // 更新 kdtId, 主要用于公共版小程序
  updateKdtId(kdtId, noChange = false, ext = {}) {
    const { mark = '919', type } = ext;
    if (kdtId && +kdtId !== +app.getKdtId() && !noChange) {
      // 保存当前店铺
      this._cacheKdtId = type === 'back' ? '' : app.getKdtId();
      accordSkyLogger({
        info: `店铺列表更新kdtId,mark:${mark}`,
      });
      this.trigger('update:youzan:kdtId', kdtId, {
        mark,
      });
    }
  },
  // 不需要更新店铺场景
  async notUpdateKdtId(listItem) {
    const isCrmOffline = listItem.crmOfflineType === CRM_OFFLINE_TYPE.OFFLINE;
    if (!app.isChainStoreSync()) {
      return isCrmOffline;
    }
    // eslint-disable-next-line @youzan/koko/no-async-await
    const isTrue = await enterShopApollo('breakOrderListUpKdtId');
    const isOfflineOrder =
      listItem.orderList.length && listItem.orderList[0].isOfflineOrder;

    return (isOfflineOrder && isTrue) || isCrmOffline;
  },

  // 订单点击回调
  async handleOrderClick(e) {
    const { listType, listIndex, orderIndex } = e.detail;

    const listItem = this.data.list[listType]?.list[listIndex];

    if (!listItem) {
      return;
    }
    // eslint-disable-next-line @youzan/koko/no-async-await
    const noChange = await this.notUpdateKdtId(listItem);
    // 点击非CRM线下门店订单，更新kdtId
    this.updateKdtId(listItem.kdtId, noChange);

    return processUtils.handleOrderClick({
      listType,
      listIndex,
      orderIndex,
      listItem,
    });
  },

  // listItem 点击
  async handleBtnClick(e) {
    const { listType, listIndex, btn, extraData = {} } = e.detail;
    const listItem = this.data.list[listType]?.list[listIndex];

    const orderItem = listItem?.orderList?.[0] || {};

    // eslint-disable-next-line @youzan/koko/no-async-await
    const noChange = await this.notUpdateKdtId(listItem);
    // 点击非CRM线下门店订单，更新kdtId
    this.updateKdtId(listItem.kdtId, noChange);

    const args = {
      listType,
      listIndex,
      listItem,
      orderItem,
      extraData,
    };

    switch (btn) {
      case 'topay':
      case 'topayDeposit':
      case 'topayRetainage':
      case 'inviteHelp':
      case 'peerpay':
        this.onTopayBtnClick(args);
        break;
      case 'cancel':
        this.onCancelBtnClick(args);
        break;
      case 'cancelPayOnDelivery':
        this.onCancelPayOnDeliveryBtnClick();
        break;
      case 'confirmReceive':
      case 'confirmReceiveHotel':
        this.onConfirmReceiveBtnClick(args);
        break;
      case 'laterReceive':
        this.onLaterReceiveBtnClick(args);
        break;
      case 'transport':
        this.onTransportBtnClick(args);
        break;
      case 'viewCardDetail':
        this.onViewCardDetailBtnClick(args);
        break;
      case 'grouponDetail':
        this.onGrouponDetailBtnClick(args);
        break;
      case 'lotteryResult':
        this.onLotteryResultBtnClick(args);
        break;
      case 'evaluate':
        this.onEvaluateBtnClick(args);
        break;
      case 'selfFetchCode':
        this.onPopSelfFetchCode(args);
        break;
      case 'paidPromotion':
        this.onPaidPromotionBtnClick(args);
        break;
      case 'coupon':
        this.logger({
          ei: 'click_marketing_paidpro', // 事件标识
          en: '支付有礼按钮点击', // 事件名称
          params: {
            type: 'coupon',
            orderNo: listItem.orderNo,
          }, // 事件参数
        });
        this.onPaidPromotionBtnClick(args);
        break;
      case 'liveQrCode':
        this.logger({
          ei: 'click_marketing_paidpro', // 事件标识
          en: '支付有礼按钮点击', // 事件名称
          params: {
            type: 'coupon',
            orderNo: listItem.orderNo,
          }, // 事件参数
        });
        this.onPaidPromotionBtnClick(args);
        break;
      case 'buyAgain': // 再来一单
        this.onBuyAgainBtnClick(args);
        break;
      case 'directBuyAgain': // 再来一单，直接跳转下单页
        this.onDirectBuyAgainBtnClick(args);
        break;
      case 'fissionCoupon': // 领优惠券
        this.logger({
          ei: 'click_divide_coupon', // 事件标识
          en: '点击领裂变券按钮', // 事件名称
        });
        this.onFissionCouponBtnClick(args);
        break;
      case 'deleteOrder': // 再来一单
        this.onDeleteOrderBtnClick(args);
        break;
      default:
        break;
    }
  },

  // 评价
  onEvaluateBtnClick(args) {
    return processUtils.evaluate(args);
  },

  // 抽奖结果
  onLotteryResultBtnClick(args) {
    return processUtils.lotteryResult(args);
  },

  // 取消(货到付款)
  onCancelPayOnDeliveryBtnClick() {
    return processUtils.cancelPayOnDelivery();
  },

  // 拼团详情
  onGrouponDetailBtnClick(args) {
    return processUtils.grouponDetail(args);
  },

  // 查看卡券
  onViewCardDetailBtnClick(args) {
    return processUtils.viewCardDetail(args);
  },

  // 确认付款、支付定金、支付尾款
  onTopayBtnClick(args) {
    return processUtils.toPay(args);
  },

  // 查看物流
  onTransportBtnClick(args) {
    return processUtils.transport(args);
  },

  // 取消订单
  onCancelBtnClick(args) {
    return processUtils.cancleOrder({ ...args, context: this }).then(() => {
      this.refreshList();
      const orderNo =
        listItem?.orderList.length > 1 ? listItem.orderNo : orderItem.orderNo;
      this.logger({
        ei: 'cancel_order', // 事件标识
        en: '取消订单', // 事件名称
        params: {
          order_no: orderNo,
        },
      });
    });
  },
  // 删除订单
  onDeleteOrderBtnClick(args) {
    const { orderItem = {}, listItem } = args;
    const orderNo =
      listItem?.orderList.length > 1 ? listItem.orderNo : orderItem.orderNo;
    this.logger({
      ei: 'order_delete_click', // 事件标识
      en: '删除订单按钮点击', // 事件名称
      params: {
        order_no: orderNo,
      },
    });
    return processUtils.deleteOrder(args).then(() => {});
  },
  // 确认收货
  onConfirmReceiveBtnClick(args) {
    const { extra = {} } = args?.orderItem || {};
    const { isHotel } = extra;

    return processUtils.confirmReceive(args).then(() => {
      if (isHotel) {
        this.logger({
          ei: 'click_checkin', // 事件标识
          en: '点击入住', // 事件名称
          si: app.getKdtId(),
        });
      }
    });
  },

  // 延长收货
  onLaterReceiveBtnClick(args) {
    return processUtils.laterReceive(args);
  },

  onPopSelfFetchCode({ listItem, orderItem }) {
    const { kdtId } = listItem;
    const { orderNo } = orderItem;
    if (this.loadingSelfFetch) return;
    this.loadingSelfFetch = true;
    if (this.data.selfFetchInfo) {
      this.popupSelfFetchCode();
      this.loadingSelfFetch = false;
    } else {
      api
        .fetchSelfFetchDetail({ orderNo, kdtId })
        .then((res = {}) => {
          this.setYZData({
            selfFetchInfo: mapKeysCase.toCamelCase(res).selfFetchVoucher || {},
          });
          this.popupSelfFetchCode();
          this.loadingSelfFetch = false;
        })
        .catch((e) => {
          Toast(e.msg || e.message || '获取自提信息失败');
          this.loadingSelfFetch = false;
        });
    }
  },

  popupSelfFetchCode() {
    this.setYZData({ showSelfFetchPopup: true });
  },

  onCloseSelfFetch() {
    this.setYZData({ showSelfFetchPopup: false });
  },

  // 支付有礼按钮
  onPaidPromotionBtnClick({ listItem, orderItem }) {
    const orderNo =
      listItem?.orderList.length > 1 ? listItem.orderNo : orderItem.orderNo;
    const { kdtId } = listItem;
    const h5Url = `/wscump/paid-promotion/fetch?kdtId=${kdtId}&orderNo=${orderNo}&source=order_list`;
    navigate.navigate({
      url: `/pages/common/webview-page/index?src=${encodeURIComponent(h5Url)}`,
    });
  },

  // 再来一单
  onBuyAgainBtnClick(args) {
    return processUtils.buyAgain(args);
  },

  getBannerId(index = 0) {
    const pageRandomNumber = makeRandomString(8);
    const loggerSpm = spm.getPageSpmTypeId();
    return `${loggerSpm}~new_buy_again_click~${index}~${pageRandomNumber}`;
  },

  // 再来一单，直接跳转下单页
  onDirectBuyAgainBtnClick(data) {
    const { listItem = {}, extraData, orderItem = {} } = data;
    const { kdtId, orderNo } = listItem;
    const { goodsList = [] } = orderItem;
    const goodsIds = [];
    goodsList.forEach((item) => {
      goodsIds.push(item.goodsId);
    });
    const bannerId = this.getBannerId();
    const params = {
      banner_id: bannerId,
      buyer_id: app.getBuyerId(),
      goods_id: goodsIds,
      order_no: orderNo,
      is_new: true,
    };
    this.logger({
      ei: 'new_buy_again_click',
      en: '新再来一单点击',
      params,
    });

    const { repurchaseCoupon = {} } = extraData;
    const {
      code,
      valueCopywriting = '',
      unitCopywriting = '',
    } = repurchaseCoupon;
    let preToastDesc = '';
    if (
      code !== undefined &&
      +code === 0 &&
      valueCopywriting &&
      unitCopywriting
    ) {
      preToastDesc = `已为你领取${valueCopywriting}${unitCopywriting}优惠券，下单享优惠`;
    } else if (couponCodeMap[code]) {
      preToastDesc = couponCodeMap[code];
    }
    api
      .directBuyAgain({ kdtId, orderNo, preToastDesc })
      .then((res) => {
        // 跳转商详页
        if (+res?.destination === 2) {
          const { alias } = res;
          const params = {
            alias,
          };
          navigate.navigate({
            url: args.add('/pages/goods/detail/index', params),
          });
          return;
        }
        const { book_key: bookKey } = res;
        const params = {
          bookKey,
          orderNo,
          banner_id: bannerId,
        };
        navigate.navigate({
          url: args.add('/packages/order/index', params),
          // url: args.add('/packages/trade-buy/order/buy/index', params),
        });
      })
      .catch((err) => {
        Toast(err.msg || '再来一单请求失败。');
      });
  },

  // 领优惠券
  onFissionCouponBtnClick({ listItem, orderItem }) {
    const orderNo =
      listItem?.orderList.length > 1 ? listItem.orderNo : orderItem.orderNo;
    navigate.navigate({
      url: `/packages/ump/fission/index?sharer=1&order_no=${orderNo}`,
    });
  },

  onWelikeEntryShow() {
    this.setYZData({ welikeEntryShow: true });
  },
  logger({ et = 'click', ei, en, pt = 'ol', params = {} }) {
    app.logger &&
      app.logger.log({
        et, // 事件类型
        ei, // 事件标识
        en, // 事件名称
        pt, // 页面类型
        params,
      });
  },
  handlePlaceholderClick() {
    const { kdtId, navigateType } = this.data;
    this.logger({
      ei: 'search_order_click', // 事件标识
      en: '订单搜索入口点击', // 事件名称
    });
    const queryStr = kdtId ? `?kdt_id=${kdtId}` : '';
    const url = `/packages/trade/order-search/index${queryStr}`;
    if (navigateType === 'redirectTo') {
      wx.redirectTo({
        url,
      });
    } else {
      wx.navigateTo({
        url,
      });
    }
  },
});
