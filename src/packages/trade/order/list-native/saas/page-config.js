import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { openState } from './open-state';
import { beforeObtainBtnTypeEvent } from './event';
import store from '../store/index';
import {
  initProcessContext,
  btnClickHandle,
  updateConfirmReceiveText,
  setGlobalBtnsDisplay,
  addButtonList,
} from './process';

export default {
  ...YunPageConfig,
  onLoad() {
    const sdk = this.getYunSdk();
    this.$store = store();

    sdk.setPageData(this, openState);
    sdk.setPageEvent('beforeOrderBtnClickAsync', beforeObtainBtnTypeEvent);

    this.on('beforeOrderBtnClickAsync', (arg) => {
      return beforeObtainBtnTypeEvent.trigger(arg);
    });
    initProcessContext(this.$store);

    sdk.setPageProcess('btnClickHandle', btnClickHandle);
    sdk.setPageProcess('updateConfirmReceiveText', updateConfirmReceiveText);
    sdk.setPageProcess('setGlobalBtnsDisplay', setGlobalBtnsDisplay);
    sdk.setPageProcess('addButtonList', addButtonList);
  },

  onUnload() {
    //
  }
};
