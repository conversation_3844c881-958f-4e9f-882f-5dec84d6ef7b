import * as processUtils from '../common/process';

let state;
let getters;
let commit;
let dispatch;

const _fn = () => {};

export function initProcessContext({
  state: _state,
  getters: _getters,
  commit: _commit,
  dispatch: _dispatch
}) {
  state = _state || {};
  getters = _getters || {};
  commit = _commit || _fn;
  dispatch = _dispatch || _fn;
}

// 设置有赞云开放的扩展字段
export function setCloudOrderExt(...args) {
  commit && commit('SET_EXTENSION_CONFIG', ...args);
}

// 延长收货
export function laterReceive(payload) {
  return processUtils.laterReceive(payload);
}

// 确认收货
export function confirmReceive(payload) {
  return processUtils.confirmReceive(payload);
}

// 查看物流 orderNo
export function transport(payload) {
  return processUtils.transport(payload);
}

// 取消订单 orderNo,kdtId
export function cancleOrder(payload) {
  return processUtils.cancleOrder(payload);
}

// 查看卡券
export function viewCardDetail(payload) {
  return processUtils.viewCardDetail(payload);
}

// 拼团详情
export function grouponDetail(payload) {
  return processUtils.grouponDetail(payload);
}

// 取消(货到付款)
export function cancelPayOnDelivery(payload) {
  return processUtils.cancelPayOnDelivery(payload);
}

// 抽奖结果
export function lotteryResult(payload) {
  return processUtils.lotteryResult(payload);
}

//  更新列表中某项
export function refreshListItem(payload) {
  return processUtils.refreshListItem(payload);
}

// 评价
export function evaluate(payload) {
  return processUtils.evaluate(payload);
}

// 群拼团订单 付款按钮
export function toPay(payload) {
  return processUtils.toPay(payload);
}

// 点击订单
export function orderClick(payload) {
  return processUtils.handleOrderClick(payload);
}

// 统一处理按钮跳转
export function btnClickHandle(payload) {
  const { btnType } = payload;
  return processUtils[btnType](payload);
}

/**
 * 收货自定义文案
 */
export function updateConfirmReceiveText(_content) {
  return processUtils.updateConfirmReceiveText(_content);
}

/**
 * 隐藏底部按钮
 */
export function setGlobalBtnsDisplay(payload) {
  return processUtils.setGlobalBtnsDisplay(payload);
}

/**
 * 添加定制操作按钮
 */
export function addButtonList(payload) {
  return processUtils.addButtonList(payload);
}
