import api from './api';
import AsyncEvent from 'shared/utils/async-event';
import navigate from '@/helpers/navigate';
import event from 'shared/utils/app-event';
import { formatListItem } from './format';
import { CRM_OFFLINE_TYPE, ORDER_STATUS } from './constant';
import getStore from '../store';
import buildUrl from '@youzan/utils/url/buildUrl';
import args from '@youzan/weapp-utils/lib/args';
import Toast from '@vant/weapp/dist/toast/toast';
import { isBlindBox } from 'utils/judge-blind-box';
import CancelOrderPopup from '../components/cancel-order-popup/cancel-order-popup';
import get from '@youzan/weapp-utils/lib/get';
import openWebView from 'utils/open-web-view';
import {
  PAGE_TYPE,
  navigateToRantaPage,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';

const app = getApp();
const p = 'packages';

// 延长收货
export function laterReceive(payload) {
  const { state } = getStore();
  const { pageType } = state;
  const { orderItem = {}, listItem = {} } = payload;
  const orderNo = listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo;
  const { kdtId } = listItem;
  const params = {
    orderNo,
    pageType,
    kdtId,
  };
  return new Promise((resolve) => {
    wx.showModal({
      title: '延长收货时间',
      content: '每笔订单只能延长一次收货时间，如需多次延长请联系商家。',
      confirmText: '取消',
      cancelText: '确定延长',
      success({ confirm }) {
        if (confirm) {
          return;
        }

        wx.showLoading();
        api
          .laterReceive(params)
          .then(() => {
            wx.hideLoading();
            wx.showToast({
              title: '延长收货成功',
              icon: 'success',
              duration: 1000,
            });
            resolve();
          })
          .catch((err) => {
            wx.hideLoading();
            wx.showToast({
              title: err.msg || '延长收货失败，请稍后重试',
              icon: 'none',
            });
          });
      },
    });
  });
}

// 再来一单
export function buyAgain(payload) {
  const { orderItem = {}, listItem = {} } = payload;
  let { extraData = {} } = payload;
  const orderNo = listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo;

  const { goodsList = [] } = orderItem;
  const { kdtId } = listItem;
  const goodsIds = [];
  goodsList.forEach((item) => {
    goodsIds.push(item.goodsId);
  });
  const params = {
    last_order_no: orderNo,
    order_no: orderNo,
    goods_id: goodsIds,
    is_new: false,
    has_buyagain_coupon: !!listItem?.repurchaseCoupon?.id,
  };
  if (listItem.repurchaseCoupon.id) {
    api
      .afterPurchaseVouchers(listItem.repurchaseCoupon.id)
      .then((res = {}) => {
        const repurchaseCouponInfo = listItem.repurchaseCoupon;
        extraData = {
          repurchaseCoupon: {
            ...res,
            code: 0,
            valueCopywriting: repurchaseCouponInfo.valueCopywriting,
            unitCopywriting: repurchaseCouponInfo.unitCopywriting,
          },
        };
      })
      .catch((err) => {
        extraData = {
          repurchaseCoupon: err,
        };
      });
  }
  app.logger &&
    app.logger.log({
      et: 'click',
      ei: 'new_buy_again_click',
      en: '新再来一单点击',
      pt: 'ol',
      params,
    });
  api
    .buyAgain({ kdtId, orderNo })
    .then((res) => {
      if (res.value) {
        const { repurchaseCoupon = {} } = extraData;
        const {
          code,
          valueCopywriting = '',
          unitCopywriting = '',
        } = repurchaseCoupon;
        const cartQuery = {
          repurchaseCouponStatus: code,
          couponValue: valueCopywriting,
          couponUnit: unitCopywriting,
        };
        app.trigger('component:sku:cart', {
          type: 'add',
        });
        navigateToRantaPage({
          pageType: PAGE_TYPE.CART,
          query: cartQuery,
        });
      } else {
        Toast('再来一单请求失败。');
      }
    })
    .catch(() => {
      Toast('再来一单请求失败。');
    });
}

// 确认收货
export function confirmReceive(payload) {
  const { state } = getStore();
  const { listType, listIndex = 0, orderItem = {}, listItem = {} } = payload;
  const orderNo = listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo;

  const { extra = {}, buyWay } = orderItem;
  const { isHotel } = extra;
  const params = {
    orderNo,
  };

  const app = getApp();
  app.logger &&
    app.logger.log({
      et: 'click',
      ei: 'confirm_goods',
      en: '确认收货',
      params: {
        order_no: orderNo,
      },
    });

  let content =
    buyWay === 1
      ? '请确保已收到商品并检查无误。该订单货款已直接给商家，售后问题需要联系商家解决。'
      : '请确保已收到商品并检查无误。确认收货后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。';
  let cancelText = '确认收货';
  let title = '确认收货';
  if (state.receivingContent !== '') {
    content = state.receivingContent;
  }
  if (isHotel) {
    content =
      buyWay === 1
        ? '请确保已入住，该订单货款已直接给商家，售后问题需要联系商家解决。'
        : '请确保已入住，确认入住后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。';
    cancelText = '确认入住';
    title = '确认入住';
  }
  return new Promise((resolve) => {
    wx.showModal({
      title,
      content,
      confirmText: '取消',
      cancelText,
      success: ({ confirm }) => {
        if (confirm) {
          return;
        }

        wx.showLoading();
        api
          .confirmReceive(params)
          .then((res) => {
            const afterConfirmed = (tMsg, cb) => {
              wx.hideLoading();
              tMsg &&
                wx.showToast({
                  title: `${tMsg}成功`,
                  icon: 'success',
                  complete: () => {
                    if (cb) cb();
                  },
                });
              resolve();
            };

            // 自动跳转发布评价页
            if (res?.isAutoGoEvaluate) {
              afterConfirmed(title, () => {
                navigate.navigate({
                  url: `/${p}/evaluation/order/create/index?order_no=${orderNo}&from=confirm_goods`,
                });
              });
              return;
            }

            AsyncEvent.triggerAsync('confirm-mission')
              .then(([res = {}] = []) => {
                afterConfirmed(res.show ? '' : title);
              })
              .catch(() => {
                afterConfirmed(title);
              });
          })
          .catch((err) => {
            wx.hideLoading();
            wx.showToast({
              title: err.msg || '确认收货失败，请稍后重试',
              icon: 'none',
            });
          });
      },
    });
  }).then(() => {
    refreshListItem({ listType, listIndex });
  });
}

// 查看物流 orderNo
export function transport(payload) {
  const { orderItem = {}, listItem = {} } = payload;
  const orderNo =
    listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo;

  return new Promise((resolve) => {
    navigate.navigate({
      url: `/${p}/trade/order/express/index?orderNo=${orderNo}`,
    });

    resolve();
  });
}

// 取消订单 orderNo,kdtId
export function cancleOrder(payload) {
  const { context, orderItem = {}, listItem = {} } = payload;

  const params = {
    orderNo:
      listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo,
    kdtId: listItem.kdtId,
  };
  const goodsIds = (listItem?.orderList || []).map((order) => {
    return (order?.goodsList || []).map((good) => {
      return good.goodsId;
    });
  });

  return new Promise((resolve) => {
    api.fetchCancelReasonList(params).then((reasonInfo) => {
      if (reasonInfo.isShowItemPutInCartButton) {
        app.logger &&
          app.logger.log({
            et: 'click', // 事件类型
            ei: 'bulk_add_cart', // 事件标识
            en: '批量加入购物车', // 事件名称
            pt: 'ol', // 页面类型

            params: {
              goods_id_list: goodsIds,
              order_no: params.orderNo,
            }, // 事件参数
          });
      }
      const popup = CancelOrderPopup({
        reasonInfo,
        context,
        orderNo: params.orderNo,
        onConfirm: (data) => {
          const { reason, isBuyAgain } = data;
          wx.showLoading();
          api
            .cancelOrder({
              ...params,
              reason: reason.toString(),
              isBuyAgain: isBuyAgain ? 1 : 0,
            })
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '取消订单成功',
                icon: 'none',
                duration: 1000,
              });
              popup.close();

              app.logger &&
                app.logger.log({
                  et: 'click',
                  ei: 'select_reason',
                  en: '选择原因',
                  pt: 'ol',
                  params: {
                    order_no: params.orderNo,
                    reason,
                  },
                });

              app.logger &&
                app.logger.log({
                  et: 'click', // 事件类型
                  ei: 'cancle_order', // 事件标识
                  en: '确认取消订单', // 事件名称
                  pt: 'ol', // 页面类型

                  params: {
                    order_no: params.orderNo,
                    state: isBuyAgain ? 'open' : 'close',
                  }, // 事件参数
                });

              resolve();
            })
            .catch((err) => {
              wx.hideLoading();
              wx.showToast({
                title: err.msg || '网络抖了下，请稍候再试',
                icon: 'none',
              });
              popup.close();
            });
        },
      });
    });
  });
}
// 删除订单 orderNo
export function deleteOrder(payload) {
  const { listType, listIndex = 0, orderItem = {}, listItem = {} } = payload;

  const params = {
    orderNo:
      listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo,
  };

  return new Promise((resolve) => {
    wx.showModal({
      title: '确定删除订单？',
      content: '删除订单后无法恢复，无法处理您的售后问题，请慎重考虑。',
      confirmText: '删除',
      cancelText: '取消',
      confirmColor: '#f23d47',
      success: ({ confirm }) => {
        if (!confirm) {
          return;
        }
        wx.showLoading();
        api
          .deleteOrder(params)
          .then(() => {
            wx.hideLoading();
            wx.showToast({
              title: '订单已删除',
              icon: 'success',
              duration: 1000,
            });
            resolve();
          })
          .catch(() => {
            wx.hideLoading();
            wx.showToast({
              title: '订单删除失败',
              icon: 'none',
            });
          });
      },
    });
  }).then(() => {
    refreshListItem({ listType, listIndex, isDelete: true });
  });
}
// 查看卡券
export function viewCardDetail(payload = {}) {
  const { orderItem = {}, listItem = {} } = payload;
  const orderNo =
    listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo;

  return new Promise((resolve) => {
    navigate.navigate({
      url: `/${p}/trade/cert/verify-ticket/index?order_no=${orderNo}`,
    });
    resolve();
  });
}

// 普通拼团
export function grouponDetail(payload) {
  const { orderItem = {}, listItem = {} } = payload;
  const orderNo =
    listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo;

  const goodsItem = orderItem.goodsList[0];
  const {
    extra: { isLotteryGroup },
  } = orderItem;
  const { goodsId, activityId, alias, activityType } = goodsItem;

  let url = '';
  if (goodsItem.goodsType == 31) {
    url = `/${p}/paidcontent/groupon/index?orderNo=${orderNo}&goodId=${goodsId}&activity_type=${activityType}`;
  } else if (isLotteryGroup) {
    url = `/${p}/collage/lottery/detail/index?order_no=${orderNo}&activity_id=${activityId}`;
  } else {
    url = `/${p}/collage/groupon/detail/index?orderNo=${orderNo}&groupAlias=${alias}`;
  }

  return new Promise((resolve) => {
    navigate.navigate({ url });
    resolve();
  });
}

// 取消(货到付款)
export function cancelPayOnDelivery() {
  return new Promise((resolve) => {
    wx.showModal({
      title: '提示',
      content: '商家可能已经接单配货，你需联系客服申请取消订单哟',
      confirmText: '我知道了',
      showCancel: false,
    });
    resolve();
  });
}

// 抽奖结果
export function lotteryResult(payload) {
  const { orderItem = {} } = payload;
  const { activityId } = orderItem.goodsList[0];

  return new Promise((resolve) => {
    navigate.navigate({
      url: `/${p}/collage/lottery/result/index?activity_id=${activityId}`,
    });
    resolve();
  });
}

//  更新列表中某项
export function refreshListItem(payload) {
  try {
    const { state } = getStore();
    const { list, pageType, canUseTradeUmpV1 } = state;
    const { orderItem = {}, listItem = {}, listIndex, listType, isDelete = false } = payload;
    const orderNo =
      listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo;

    // 当为删除订单的时候  需要直接删掉显示，不管是在哪个tab下
    if (listType === 'all' && !isDelete) {
      return api
        .fetchListItem({
          orderNo,
          pageType,
        })
        .then((res) => {
          const resList = res.list || [];
          if (resList.length) {
            const listItem = formatListItem(resList[0], '', canUseTradeUmpV1);
            list[listType].list[listIndex] = listItem;
            const { list: typeList } = list[listType];
            getStore().commit('UPDATE_LIST', {
              [listType]: {
                list: typeList,
              },
            });
          }
        })
        .catch((e) => {
          console.log('更新列表中某项', e);
        });
    }
    const { list: typeList } = list[listType];
    typeList.splice(listIndex, 1);
    getStore().commit('UPDATE_LIST', {
      [listType]: {
        list: typeList,
      },
    });
    return Promise.resolve();
    // eslint-disable-next-line no-empty
  } catch (error) {}
}

// 评价
export function evaluate(payload) {
  const { orderItem = {}, listItem = {}, listIndex, listType } = payload;
  const orderNo =
    listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo;

  bindCreateEvaluateListener({ listType, listIndex, orderItem });

  return new Promise((resolve) => {
    navigate.navigate({
      url: `/${p}/evaluation/order/create/index?order_no=${orderNo}`,
    });
    resolve();
  });
}

// 付款按钮
export async function toPay(payload) {
  const { orderItem = {}, listItem = {}, listType, listIndex } = payload;
  const orderNo =
    listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo;

  const goodsItem = orderItem.goodsList[0];

  const { activityType } = goodsItem;
  const {
    extra: { isMallGroupBuy, isFinalPayment },
  } = orderItem;
  const { kdtId } = listItem;

  const orderFormatednNo =
    listItem.orderList?.length > 1 ? listItem.orderNo : orderNo;
  let url = '';

  bindOrderPaidListener({ listType, listIndex });
  // 待支付的快速下单订单跳转专门的支付页
  if (orderItem.extra.isFastAd && orderItem.statusCode === ORDER_STATUS.TOPAY) {
    url = `/${p}/order-native/fastbuy/index?orderNo=${orderItem.orderNo}`;
  } else if (activityType == 116 && isFinalPayment) {
    // 助力定金膨胀且是支付尾款
    url = `/${p}/ump/handsel-expand/index?order_no=${orderNo}&kdt_id=${kdtId}`;
  } else if (isMallGroupBuy) {
    // 群拼团订单支付按钮
    url = `/${p}/groupbuying/buyer-trade/detail/index?orderNo=${orderNo}`;
  } else {
    await handleGoodsLevel(listItem);

    navigateToRantaPage({
      pageType: PAGE_TYPE.PAY,
      query: {
        orderNo: orderFormatednNo,
      },
    });

    return Promise.resolve();
  }

  return new Promise((resolve) => {
    navigate.navigate({ url });
    resolve();
  });
}

// 付费商品连续包月 跳转会员支付页
export async function handleGoodsLevel(listItem, cb) {
  const payGradeCard = get(
    listItem,
    'orderList[0].goodsList[0].payGradeCard',
    false
  );
  if (!payGradeCard) {
    cb && cb();
    return;
  }
  const kdtId = get(listItem, 'kdtId', '');
  const goodsId = get(listItem, 'orderList[0].goodsList[0].goodsId', '');
  const skuVid = get(listItem, 'orderList[0].goodsList[0].skuVid', '');
  return api.fetchGoodsLevel({ kdtId, goodsId }).then((res = {}) => {
    const { autoRenew, levelAlias, levelGoods = {} } = res;
    const { skuList } = levelGoods;
    const sku = skuList.find((s) => s.skuVId == skuVid);
    const { goodsSkuType } = sku || [];
    // 1 付费规则 2.自动续费规则
    if (autoRenew && goodsSkuType == 2) {
      wx.navigateTo({
        url:  args.add('/packages/shop/levelcenter/plus/index', {
          kdt_id: kdtId,
          alias: levelAlias,
        }),
      });
    } else {
      return '';
    }
  });
}

function bindCreateEvaluateListener({ listType, listIndex, orderItem }) {
  event.on('trade:evaluate:create', (changedOrderNo) => {
    if (changedOrderNo === orderItem.orderNo) {
      refreshListItem({ listType, listIndex });
    }
  });
}

// 绑定追加评价监听
function bindAddEvaluateListener({ listType, listIndex, orderItem }) {
  event.on('trade:evaluate:add', (changedOrderNo) => {
    if (changedOrderNo === orderItem.orderNo) {
      refreshListItem({ listType, listIndex });
    }
  });
}

// 监听支付完成
function bindOrderPaidListener({ listType, listIndex, orderIndex = 0 }) {
  try {
    const { state } = getStore();
    const { list } = state;
    const listItem = list[listType]?.list[listIndex] || {};

    const { orderNo } = listItem.orderList[orderIndex];

    event.on('trade:order:paid', (changedOrderNo) => {
      if (changedOrderNo === orderNo) {
        refreshListItem({ listType, listIndex });
      }
    });
  } catch (e) {
    console.log('监听支付完成', e);
  }
}

export async function handleOrderClick(payload) {
  const { listType, listIndex = 0, orderIndex = 0, listItem = {} } = payload;

  if (!listItem) {
    return;
  }
  const orderItem = listItem.orderList[orderIndex];
  const goodsItem = orderItem.goodsList[0];
  const cardGoodsId = goodsItem.goodsType == 20 ? goodsItem.goodsId : '';
  let url = '';
  let toPayParams = null;

  // 待支付的快速下单订单跳转专门的支付页
  if (orderItem.extra.isFastAd && orderItem.statusCode === ORDER_STATUS.TOPAY) {
    url = `/${p}/order-native/fastbuy/index?orderNo=${orderItem.orderNo}`;
  } else if (orderItem.extra.isMallGroupBuy) {
    url = `/${p}/groupbuying/buyer-trade/detail/index?orderNo=${orderItem.orderNo}`;
  } else if (goodsItem.goodsType == 206) {
    url = `/${p}/trade/order/unicashier-result/index?order_no=${orderItem.orderNo}`;
  } else if (goodsItem.activityType == 116 && orderItem.extra.isFinalPayment) {
    url = `/${p}/ump/handsel-expand/index?order_no=${orderItem.orderNo}&kdt_id=${listItem.kdtId}`;
  } else if (orderItem.statusCode === ORDER_STATUS.TOPAY) {
    // 判断 连续包月付费商品
    await handleGoodsLevel(listItem);

    toPayParams = {
      orderNo:
        listItem.orderList?.length > 1 ? listItem.orderNo : orderItem.orderNo,
    };
  } else if (goodsItem.payGradeCard) {
    url = `/${p}/levelcenter/pay/index?alias=${goodsItem.alias}`;
  } else if (cardGoodsId) {
    url = `/${p}/card/detail/index?goods_id=${cardGoodsId}`;
  } else if (
    isBlindBox(goodsItem.activityType) &&
    orderItem.statusCode !== 'toPay'
  ) {
    if (orderItem.statusCode === 'cancel') {
      wx.showToast({
        title: '订单已关闭',
        icon: 'none',
      });
      return;
    }
    const h5Url = buildUrl(
      args.add('/wscump/blind-box/detail', {
        kdtId: listItem.kdtId,
        orderNo: orderItem.orderNo,
      }),
      'h5',
      listItem.kdtId
    );
    wx.navigateTo({
      url: `/pages/common/webview-page/index?src=${encodeURIComponent(h5Url)}`,
    });
    return;
  } else if (listItem.crmOfflineType === CRM_OFFLINE_TYPE.OFFLINE) {
    // CRM线下门店订单
    url = `/${p}/trade/crm-order-detail/index?order_no=${orderItem.orderNo}&kdtId=${listItem.kdtId}`;
    app.logger &&
      app.logger.log({
        et: 'click',
        ei: 'click_crm_offline_order',
        en: '点击CRM线下门店订单',
      });
  } else {
    url = `/${p}/trade/order/result/index?order_no=${orderItem.orderNo}`;
  }

  bindCreateEvaluateListener({ listType, listIndex, orderItem });
  bindAddEvaluateListener({ listType, listIndex, orderItem });
  bindOrderPaidListener({ listType, listIndex, orderIndex });

  return new Promise((resolve) => {
    if (toPayParams) {
      navigateToRantaPage({
        pageType: PAGE_TYPE.PAY,
        query: toPayParams,
      });
    } else {
      navigate.navigate({ url });
    }
    resolve();
  });
}

/**
 * 收货自定义文案
 */
export function updateConfirmReceiveText(_content) {
  getStore().commit('UPDATE_RECEIVING_CONTENT', _content);
}

/**
 * 隐藏底部按钮
 */
export function setGlobalBtnsDisplay(payload) {
  const { state, commit } = getStore();
  const { tab, list } = state;
  const { selectedId } = tab;
  const selectedList = list[selectedId] || {};

  if (
    (payload.confirmReceiveHotel ?? '') !== '' &&
    !payload.confirmReceiveHotel
  ) {
    commit('UPDATE_GLOBAL_BTNS_DISPLAY', payload);
    if (payload.type) {
      payload.type.push('confirmReceiveHotel');
    } else {
      payload.type = [];
      payload.type.push('confirmReceiveHotel');
    }
  }
  commit('HIDDEN_BOTTOM_BTNS', payload);

  const formatList = selectedList.list.map((item) => {
    return {
      ...item,
      btnList: item.btnList.filter((btn) => {
        return !payload.type.includes(btn.value);
      }),
    };
  });

  commit('UPDATE_LIST', {
    [selectedId]: {
      list: formatList,
    },
  });
}

/**
 * 添加定制操作按钮
 */
export function addButtonList(payload = {}) {
  const { state, commit } = getStore();
  const { tab, list } = state;
  const { selectedId } = tab;
  const selectedList = list[selectedId] || {};
  const {
    condition: { filterOrderStatus = [], activityTypes = [] },
    cloudBtnList = [],
  } = payload;

  const getList = (orderItem, btnList, extraBtnList) => {
    const { goodsList = [] } = orderItem;
    if (!filterOrderStatus.find((v) => v === orderItem.status)) {
      if (
        !activityTypes.length ||
        goodsList.find((v) => activityTypes.includes(+v.activityType))
      ) {
        return [...btnList, ...extraBtnList];
      }
    }
    return btnList;
  };

  commit('ADD_BUTTON_LIST', payload);
  const formatList = selectedList.list.map((item) => {
    const orderItem = item.orderList[0] || {};
    return {
      ...item,
      btnList: getList(orderItem, item.btnList, cloudBtnList),
    };
  });

  commit('UPDATE_LIST', {
    [selectedId]: {
      list: formatList,
    },
  });
}
