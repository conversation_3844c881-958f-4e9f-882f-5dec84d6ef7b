import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import get from '@youzan/weapp-utils/lib/get';
import { DEFAULT_BTN_CONF, TAG_CONF, RETAIL_BTN_CONF } from './config';
import { ORDER_STATUS } from './constant';
import getStore from '../store';

const app = getApp();

// 格式化金额
function formatPrice(price = 0, operator) {
  const symb = operator ? `${operator} ¥` : '¥';
  return `${symb}${(Math.abs(price) / 100).toFixed(2)}`;
}

// 格式化总金额(积分 + 金额)
function formatTotalPrice(points = 0, price = 0, pointsName = '积分') {
  const totalArr = [];
  points && totalArr.push(`${points}${pointsName}`);
  (price || !points) && totalArr.push(formatPrice(price));
  return totalArr.join(' + ');
}

// 获取 list-item 底部的 btns
function getListItemBtnList(data, canUseTradeUmpV1) {
  const orderItem = data.orderItems[0];
  const permission = data.orderPermission;
  const extra = orderItem.orderExtra;
  let btnList = [];
  const { statusCode, buyWay, status, items = [], orderNo = '' } = orderItem;
  const { directBuyAgainBtnConfig = {} } = data;
  const { state } = getStore();
  const {
    condition: { filterOrderStatus = [], activityTypes = [] },
    cloudBtnList = [],
  } = state.cloudBtnListCondition;

  let BTN_CONF = { ...DEFAULT_BTN_CONF };
  // 零售场景覆盖配置
  if (state.isRetailWeappScene) {
    BTN_CONF = { ...BTN_CONF, ...RETAIL_BTN_CONF };
  }

  // 评价
  if (permission.isShowEvaluate) {
    btnList.push(BTN_CONF.evaluate);
  }

  // 取消
  if (permission.isShowCancelOrder) {
    btnList.push(BTN_CONF.cancel);
  }

  // 取消(待发货的货到付款订单)
  if (buyWay === 9 && statusCode === ORDER_STATUS.TOSEND) {
    btnList.push(BTN_CONF.cancelPayOnDelivery);
  }

  // 付款
  if (permission.isShowTopay) {
    btnList.push(BTN_CONF.topay);
  }

  // 支付定金
  if (permission.isShowTopayPresaleDownpayment) {
    btnList.push(BTN_CONF.topayDeposit);
  }

  // 支付尾款(不可点击)
  if (permission.isShowBeforePresaleFinalpayment) {
    btnList.push(BTN_CONF.topayRetainageDisabled);
  }

  // 支付尾款(可点击)
  if (permission.isShowTopayPresaleFinalpayment) {
    btnList.push(BTN_CONF.topayRetainage);
  }

  // 邀请助力
  if (permission.isShowInviteHelp) {
    btnList.push(BTN_CONF.inviteHelp);
  }

  // 代付
  if (buyWay === 7 && status !== 99) {
    btnList.push(BTN_CONF.peerpay);
  }
  // 删除订单
  if (permission.allowShowDeleteOrder) {
    btnList.push(BTN_CONF.deleteOrder);
  }

  // 延长收货
  if (permission.isAllowLaterReceive) {
    btnList.push(BTN_CONF.laterReceive);
  }

  // 查看物流
  if (permission.allowViewLogistics) {
    btnList.push(BTN_CONF.transport);
  }

  // 确认收货
  if (permission.isAllowConfirmReceive) {
    if (extra.isHotel) {
      // 酒店商品按钮
      state.globalBtnsDisplay.confirmReceiveHotel &&
        btnList.push(BTN_CONF.confirmReceiveHotel);
    } else {
      btnList.push(BTN_CONF.confirmReceive);
    }
  }

  // 拼团详情
  if (permission.allowPintuanDetail) {
    btnList.push(BTN_CONF.grouponDetail);
  }

  const goodsIds = [];
  items.forEach((item) => {
    goodsIds.push(item.goodsId);
  });
  const logParams = {
    et: 'view',
    ei: 'new_buy_again_view',
    en: '新再来一单曝光',
    pt: 'ol',
    params: {
      order_no: orderNo,
      goods_id: goodsIds,
    },
  };
  // 再来一单
  if (permission.isAllowDirectBuyAgain && directBuyAgainBtnConfig.show) {
    // 点击直接进下单页
    btnList.push(BTN_CONF.directBuyAgain);
    // 埋点
    logParams.params.is_new = true;
    logParams.params.abTraceId = directBuyAgainBtnConfig?.ab_trace_id || null;
    app.logger && app.logger.log(logParams);
  } else if (permission.isAllowBuyAgain && !extra.isMallGroupBuy) {
    // 再来一单，社区团购订单不展示再来一单
    btnList.push(BTN_CONF.buyAgain);
    // 埋点
    logParams.params.is_new = false;
    // 新再来一单按钮支持，但由于AB结果，走老按钮逻辑，需要添加abTraceId
    if (permission.isAllowDirectBuyAgain) {
      logParams.params.abTraceId = directBuyAgainBtnConfig?.ab_trace_id || null;
    }
    app.logger && app.logger.log(logParams);
  }

  // 抽奖结果
  if (permission.allowViewLotteryResult) {
    btnList.push(BTN_CONF.lotteryResult);
  }

  // 查看卡券
  if (permission.allowViewCardDetail) {
    btnList.push(BTN_CONF.viewCardDetail);
  }

  // 核销码（扫码购自提码）
  if (permission.showSelfFetchScancode) {
    btnList.push(BTN_CONF.selfFetchCode);
  }

  let hasNewPaidPromotion = false;
  const { paidPromotion = {} } = data;
  const { paidPromotionType = '' } = paidPromotion;
  // 支付有礼跳转按钮
  if (canUseTradeUmpV1) {
    hasNewPaidPromotion = !!paidPromotionType;
  }
  if (hasNewPaidPromotion) {
    const { paidPromotionValue } = paidPromotion;
    const type = `${paidPromotionType}${
      paidPromotionType === 'liveQrCode' ? paidPromotionValue.qrCodeType : ''
    }`;
    btnList.push(BTN_CONF[type]);
  } else {
    const { paidPromotion, hasRefund } = extra;
    if (paidPromotion && !hasRefund) {
      btnList.push(BTN_CONF.paidPromotion);
    }
  }

  // 领优惠券
  if (permission.isAllowFissionCoupon) {
    btnList.push(BTN_CONF.fissionCoupon);
  }

  // 根据特定条件添加定制操作按钮
  if (!filterOrderStatus.find((v) => v === orderItem.status)) {
    if (
      !activityTypes.length ||
      items.find((v) => activityTypes.includes(+v.activityType))
    ) {
      btnList = [...btnList, ...cloudBtnList];
    }
  }
  // yunSdk 处理底部按钮操作
  if (state.globalBtnsDisplay.type.length !== 0) {
    const newBtnList = btnList.filter((item) => {
      return !state.globalBtnsDisplay.type.includes(item.value);
    });
    btnList = newBtnList;
  }

  return btnList;
}

// 获取商品属性
export function getGoodsPropertiesStr(properties = []) {
  const propertiesArr = [];

  properties.forEach((currentProperty) => {
    const propValueList = get(currentProperty, 'propValueList', []);
    propValueList.forEach((currentValue) => {
      propertiesArr.push(currentValue.propValueName);
    });
  });

  return propertiesArr.join('；');
}

// 获取订单的商品列表
function getGoodsList(orderItem, pointsName) {
  const { orderExtra: extra, items } = orderItem;

  const isPrescriptionDrugGoods = items.some(
    (item) => item.isPrescriptionDrugGoods
  );

  return orderItem.items.map((goods) => {
    // sku
    const skuStr = goods.skuStr
      ? goods.skuStr
      : (goods.sku || [])
          .filter((item) => item.v)
          .map((item) => item.v)
          .join('；');

    // 获取商品属性
    const skuVid = get(goods, 'sku[0].vId', '');
    const propertiesStr = getGoodsPropertiesStr(goods.properties);
    const sku = [skuStr, propertiesStr].filter((item) => !!item).join('；');

    // tagList
    const tagList = [];
    extra.isFcode && tagList.push(TAG_CONF.fcode);
    extra.isEnjoyBuy && tagList.push(TAG_CONF.enjoyBuy);
    extra.isKnowledgeGift && tagList.push(TAG_CONF.knowledgeGift);
    (extra.isPresaleOrder || extra.isPresale) &&
      goods.preSale === '1' &&
      tagList.push(TAG_CONF.presale);
    extra.isPeriod && tagList.push(TAG_CONF.period);
    extra.isGroup && tagList.push(TAG_CONF.group);
    extra.isSelfFetch && tagList.push(TAG_CONF.selfFetch);
    extra.isPeerpay && tagList.push(TAG_CONF.peerpay);
    extra.isLotteryGroup && tagList.push(TAG_CONF.lotteryGroup);
    goods.isTimeLimitSeckill && tagList.push(TAG_CONF.limitSeckill); // 限时秒杀
    // 替换"积分"为自定义名称
    (TAG_CONF.points || {}).text = `${pointsName || '积分'}订单`;
    extra.isPoints && tagList.push(TAG_CONF.points);

    let price = '';
    if (extra.isPoints) {
      price = formatTotalPrice(goods.pointsPrice, goods.price, pointsName);
    } else {
      price = formatPrice(goods.price);
    }

    return {
      sku,
      tagList,
      price,
      isShipped: !!goods.isShipped,
      activityId: goods.activityId || '',
      activityType: goods.activityType || '',
      alias: goods.alias,
      goodsId: goods.goodsId,
      goodsType: goods.goodsType,
      itemId: goods.itemId,
      thumb: cdnImage(goods.image, '!180x180.jpg'),
      title: goods.title,
      num: goods.num,
      goodsImgTag: extra.isCrossBorder ? '海淘' : '',
      isPrescriptionDrugGoods,
      payGradeCard: goods.payGradeCard,
      skuVid,
      preSaleExpressTimeDesc: goods.preSaleExpressTimeDesc || '',
    };
  });
}

// 获取酒店订单信息
function getHotelInfo(orderItem) {
  const hotel = {
    show: !!(orderItem.hotel && orderItem.orderType === 35),
    num: orderItem.items.length,
  };

  return Object.assign(hotel, orderItem.hotel || {});
}

// 获取列表每项的订单列表
function getOrderList(listItem, pointsName) {
  const showPayInfo = listItem.orderPermission.isShowTotalPrice;

  return listItem.orderItems.map((item) => {
    return {
      showPayInfo,
      orderNo: item.orderNo,
      outBizNo: item.outBizNo,
      goodsList: getGoodsList(item, pointsName),
      hotel: getHotelInfo(item),
      payDesc: item.payInfo.amountDesc,
      payLast: item.payInfo.last,
      payPrice: formatTotalPrice(
        item.realPointPay,
        item.payInfo.payAmount,
        pointsName
      ),
      status: item.status,
      statusCode: item.statusCode,
      extra: item.orderExtra,
      isOfflineOrder: item.orderExtra?.isOfflineOrder,
      buyWay: item.buyWay,
      pickUpCode: item.orderDesc?.pickUpCode,
      expressType: item.expressType,
    };
  });
}

// 格式化列表 item
export function formatListItem(listItem, pointsName, canUseTradeUmpV1) {
  return {
    shopName: listItem.shopName,
    orderStateStr: listItem.orderStateStr,
    orderNo: listItem.orderNo,
    outBizNo: listItem.outBizNo,
    kdtId: listItem.kdtId,
    orderList: getOrderList(listItem, pointsName),
    btnList: getListItemBtnList(listItem, canUseTradeUmpV1),
    repurchaseCoupon: listItem.repurchaseCoupon || {},
    crmOfflineType: listItem.crmOfflineType,
    retailPickUpCode: getRetailPickUpCode(listItem),
  };
}

// 格式化订单列表
export function formatList(orderList, pointsName, canUseTradeUmpV1) {
  return orderList.map((item) =>
    formatListItem(item, pointsName, canUseTradeUmpV1)
  );
}

function getRetailPickUpCode(listItem) {
  return listItem.orderItems[0]?.orderExtra.pickUpCode || '';
}

// 格式化制作时长处理
// 从订单详情页cp过来的，有问题那不是我的问题
export function formatWaitingTime(order = {}) {
  const { productTime = 0 } = order;
  if (productTime < 60) {
    return `${productTime}秒`;
  }
  if (productTime < 60 * 60) {
    return `${(productTime / 60).toFixed()}分钟`;
  }
  const hour = Math.floor(productTime / 3600);
  const minute = ((productTime % 3600) / 60).toFixed() || '';
  return `${hour}小时${minute}${minute ? '分钟' : ''}`;
}
