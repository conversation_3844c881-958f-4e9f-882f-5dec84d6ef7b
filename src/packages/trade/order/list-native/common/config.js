import { EVALUATION } from 'shared/common/sensetive-words';

// 按钮配置
export const DEFAULT_BTN_CONF = {
  cancel: { text: '取消', value: 'cancel', type: 'default', plain: true },
  cancelPayOnDelivery: {
    text: '取消',
    value: 'cancelPayOnDelivery',
    type: 'default',
    plain: true,
  },
  topay: { text: '付款', value: 'topay', type: 'danger' },
  peerpay: { text: '代付', value: 'peerpay', type: 'danger' },
  transport: {
    text: '查看物流',
    value: 'transport',
    type: 'default',
    plain: true,
  },
  topayDeposit: { text: '支付定金', value: 'topayDeposit', type: 'danger' },
  topayRetainage: { text: '支付尾款', value: 'topayRetainage', type: 'danger' },
  topayRetainageDisabled: {
    text: '支付尾款',
    value: 'topayRetainageDisabled',
    type: 'danger',
    disabled: true,
  },
  inviteHelp: { text: '邀请助力', value: 'inviteHelp', type: 'danger' },
  grouponDetail: {
    text: '拼团详情',
    value: 'grouponDetail',
    type: 'default',
    plain: true,
  },
  lotteryResult: {
    text: '抽奖结果',
    value: 'lotteryResult',
    type: 'default',
    plain: true,
  },
  viewCardDetail: {
    text: '查看卡券',
    value: 'viewCardDetail',
    type: 'danger',
    plain: true,
  },
  confirmReceive: {
    text: '确认收货',
    value: 'confirmReceive',
    type: 'danger',
    plain: true,
  },
  confirmReceiveHotel: {
    text: '确认入住',
    value: 'confirmReceiveHotel',
    type: 'danger',
    plain: true,
  },
  laterReceive: {
    text: '延长收货',
    value: 'laterReceive',
    type: 'default',
    plain: true,
  },
  buyAgain: {
    text: '再来一单',
    value: 'buyAgain',
    type: 'default',
    plain: true,
    showCoupon: true,
  },
  evaluate: {
    text: EVALUATION,
    value: 'evaluate',
    type: 'default',
    plain: true,
  },
  selfFetchCode: {
    text: '核销码',
    value: 'selfFetchCode',
    type: 'danger',
    plain: true,
  },
  deleteOrder: {
    text: '删除订单',
    value: 'deleteOrder',
    type: 'default',
    plain: true,
  },
  paidPromotion: {
    text: '支付有礼',
    value: 'paidPromotion',
    type: 'danger',
    plain: true,
  },
  liveQrCode1: {
    text: '专属客服',
    value: 'liveQrCode',
    type: 'default',
    plain: true,
    icon: 'https://b.yzcdn.cn/public_files/8c404f5a2465baa843f1a847832772de.png',
  },
  liveQrCode2: {
    text: '加粉丝群',
    value: 'liveQrCode',
    type: 'default',
    plain: true,
    icon: 'https://b.yzcdn.cn/public_files/8c404f5a2465baa843f1a847832772de.png',
  },
  coupon: {
    text: '领优惠券',
    value: 'coupon',
    type: 'default',
    plain: true,
    icon: 'https://b.yzcdn.cn/public_files/e49f5b413529e624ab6a538642bf4eeb.png',
  },
  fissionCoupon: {
    text: '抢优惠券',
    value: 'fissionCoupon',
    type: 'default',
    plain: true,
    icon: 'https://img01.yzcdn.cn/public_files/01ceebc40fa09f1902d679c1519012a4.png',
  },
  directBuyAgain: {
    text: '再来一单',
    value: 'directBuyAgain',
    type: 'default',
    plain: true,
    showCoupon: true,
  },
};

// 零售场景覆盖按钮配置
export const RETAIL_BTN_CONF = {
  buyAgain: {
    text: '再来一单',
    value: 'buyAgain',
    type: 'danger',
    plain: false,
    showCoupon: true,
  },
  directBuyAgain: {
    text: '再来一单',
    value: 'directBuyAgain',
    type: 'danger',
    plain: false,
    showCoupon: false,
  },
};

// 标签配置
export const TAG_CONF = {
  presale: { text: '预售', type: 'success' },
  fcode: { text: 'F码专享', type: 'danger' },
  knowledgeGift: { text: '知识付费送礼', type: 'danger' },
  period: { text: '周期购', type: 'danger' },
  enjoyBuy: { text: '随心订', type: 'danger' },
  group: { text: '拼团', type: 'danger' },
  lotteryGroup: { text: '抽奖团', type: 'danger' },
  selfFetch: { text: '自提', type: 'danger' },
  peerpay: { text: '代付', type: 'danger' },
  points: { text: '积分订单', type: 'danger' },
  limitSeckill: { text: '限时秒杀', type: 'danger' },
};
