import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { PAGE_TYPE } from './constant';

const app = getApp();

export default {
  // 获取列表
  fetchList({
    page = 1,
    pageId = 'wsc',
    pageSize = 10,
    type = 'all',
    pageType = PAGE_TYPE.SINGLE,
    keyword = '',
    receivertel = '',
    caller = '',
  }) {
    return new Promise((resolve, reject) => {
      app
        .request({
          path: 'wsctrade/order/list.json',
          method: 'GET',
          config: {
            noStoreId: true,
            skipKdtId: pageType === PAGE_TYPE.ALL,
            skipShopInfo: pageType === PAGE_TYPE.ALL,
          },
          data: {
            page,
            type,
            page_id: pageId,
            page_size: pageSize,
            page_type: pageType,
            keyword,
            receivertel,
            caller,
            haveOfflineOrder: 2, // 表示可以获取线下门店订单，数字前后端约定好的，表示该版本小程序支持展示
          },
        })
        .then((res) => {
          resolve(mapKeysCase.toCamelCase(res));
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  // 获取 list-item
  fetchListItem({ orderNo = '', pageType = PAGE_TYPE.SINGLE }) {
    return new Promise((resolve, reject) => {
      app
        .request({
          path: 'wsctrade/order/oneOrder.json',
          method: 'GET',
          config: {
            noStoreId: true,
            skipKdtId: pageType === PAGE_TYPE.ALL,
            skipShopInfo: pageType === PAGE_TYPE.ALL,
          },
          data: {
            order_no: orderNo,
          },
        })
        .then((res) => {
          resolve(mapKeysCase.toCamelCase(res));
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  // 取消订单
  cancelOrder({ orderNo, kdtId, reason, isBuyAgain }) {
    return new Promise((resolve, reject) => {
      app
        .request({
          path: 'wsctrade/order/cancelOrder.json',
          method: 'POST',
          data: {
            order_no: orderNo,
            kdt_id: kdtId,
            reason,
            isBuyAgain,
          },
        })
        .then((res = {}) => {
          if (res.value) {
            resolve();
          } else {
            reject();
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  // 取消订单
  deleteOrder({ orderNo }) {
    return new Promise((resolve, reject) => {
      app
        .request({
          path: 'wsctrade/order/deleteOrder.json',
          method: 'POST',
          data: {
            order_no: orderNo,
          },
        })
        .then((res = {}) => {
          if (res.value) {
            resolve();
          } else {
            reject();
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  // 确认收货
  confirmReceive({ orderNo }) {
    return new Promise((resolve, reject) => {
      app.request({
        path: 'wsctrade/order/confirmReceiveV2.json',
        method: 'POST',
        data: {
          order_no: orderNo,
        },
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  // 延长收货
  laterReceive({ orderNo, kdtId }) {
    return new Promise((resolve, reject) => {
      app.request({
        path: 'wsctrade/order/delayReceive.json',
        method: 'POST',
        data: {
          order_no: orderNo,
          kdt_id: kdtId,
        },
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  fetchSelfFetchDetail({ orderNo, kdtId }) {
    return new Promise((resolve, reject) => {
      app.request({
        path: '/wsctrade/order/selffetch/detail.json',
        method: 'GET',
        data: {
          orderNo,
          kdtId,
        },
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  // 再来一单
  buyAgain({ orderNo, kdtId }) {
    return new Promise((resolve, reject) => {
      app.request({
        path: '/wsctrade/order/buyAgain.json',
        method: 'POST',
        data: {
          order_no: orderNo,
          kdt_id: kdtId,
        },
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  // 获取取消订单理由列表
  fetchCancelReasonList({ orderNo, kdtId }) {
    return new Promise((resolve, reject) => {
      app.request({
        path: '/wsctrade/order/cancel-order/reason-list.json',
        method: 'GET',
        data: {
          order_no: orderNo,
          kdt_id: kdtId,
        },
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  // 获取是否开启支付营销优化
  getUseUmpGray() {
    return new Promise((resolve, reject) => {
      app.request({
        path: 'wscump/trade/use-trade-ump-v1.json',
        method: 'GET',
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  // 查询商家等级信息
  fetchGoodsLevel({ kdtId, goodsId }) {
    return new Promise((resolve, reject) => {
      app.request({
        path: '/wsctrade/scrm/level.json',
        method: 'GET',
        data: {
          goodsId,
          kdtId,
        },
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  // 获取再来一单[直接进入下单页]配置
  fetchDirectBuyAgainBtnConfig() {
    return new Promise((resolve, reject) => {
      app.request({
        path: '/wsctrade/order/getDirectBuyAgainBtnConfig.json',
        method: 'GET',
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  // 再来一单，直接进入下单页
  directBuyAgain({ orderNo, kdtId, preToastDesc }) {
    return new Promise((resolve, reject) => {
      app.request({
        path: '/wsctrade/order/directBuyAgain.json',
        method: 'POST',
        data: {
          order_no: orderNo,
          kdt_id: kdtId,
          preToastDesc,
        },
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  // 复购券接口
  afterPurchaseVouchers(repurchaseCouponId) {
    return new Promise((resolve, reject) => {
      app.request({
        path: '/wsctrade/order/detail/getVoucher.json',
        method: 'POST',
        data: {
          activityId: repurchaseCouponId,
          /** 业务名称 */
          bizName: 'order_list',
          /** 来源, 可以与业务名称一致 */
          source: 'order_list',
          /** 无约定，保证唯一即可, 每次请求不同 */
          // requestId: 'order_detail_fission_coupon',
        },
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  // 批量获取等待时常
  getBatchShopWaitingTime(orderNos) {
    return new Promise((resolve, reject) => {
      app.request({
        path: '/retail/h5/miniprogram/order/batchOrderWaitingProgress.json',
        method: 'POST',
        data: {
          orderNos,
        },
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },

  // 获取灰度切流，是否返回订单列表时不刷新
  getUseCacheOrderListData() {
    return new Promise((resolve, reject) => {
      app.request({
        path: '/wsctrade/apollo-gray.json',
        method: 'GET',
        data: {
          namespace: 'wsc-h5-trade.gray-release',
          key: 'useCacheOrderListData',
        },
        success: resolve,
        fail(err) {
          reject(err);
        },
      });
    });
  },
}
