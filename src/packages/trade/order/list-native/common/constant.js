// 页面类型
export const PAGE_TYPE = {
  SINGLE: 'single', // 专享版
  ALL: 'all', // 公共版
};

// 订单状态
export const ORDER_STATUS = {
  TOPAY: 'topay',
  SEND: 'send',
  SIGN: 'sign',
  TOSEND: 'tosend',
};

// 券报错信息
export const couponCodeMap = {
  161201035: '优惠券活动已失效，无法再领取',
  161201033: '优惠券库存不足，无法再领取',
  161201406: '领取规则调整，该券不可再领',
  161201050: '当前领取人数太多，请稍后再试',
};

// CRM 线下订单展示枚举
export const CRM_OFFLINE_TYPE = {
  NONE: 0, // 不展示
  ONLINE: 1, // 线上订单
  OFFLINE: 2, // 线下订单
};

export const CRM_OFFLINE_TEXT = {
  [CRM_OFFLINE_TYPE.NONE]: '',
  [CRM_OFFLINE_TYPE.ONLINE]: '网店订单',
  [CRM_OFFLINE_TYPE.OFFLINE]: '门店订单',
};
