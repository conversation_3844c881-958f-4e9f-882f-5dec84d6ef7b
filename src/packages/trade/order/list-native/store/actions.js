import designMod from '../design.json';
import api from '../common/api';
import { formatList } from '../common/format';
import chinaMobile from '@youzan/utils/validate/chinaMobile';

export default {
  GET_USER_GRAY({ commit }) {
    return api
      .getUseUmpGray()
      .then((res) => {
        commit('SET_UMP_GRAY', res.value);
        console.log('res', res);
      })
      .catch(() => {
        commit('SET_UMP_GRAY', false);
      });
  },

  GET_DESIGN({ commit, state }) {
    const { isRetailWeappScene } = state;
    let modList = designMod.design || [];
    if (isRetailWeappScene) {
      modList = modList.map((data) => {
        if (data.type === 'list-tabs') {
          data.props.list = [
            {
              id: 'current',
              title: '当前订单',
            },
            {
              id: 'history',
              title: '历史订单',
            },
          ];
          return data;
        }
        return data;
      });
    }
    const config = modList[0] && modList[0].type === 'config' && modList[0];
    const newDesign = config ? modList.slice(1) : modList;
    const tabComp =
      newDesign.find(
        (item) => item.type === 'list-tabs' || item.type === 'custom-list-tabs'
      ) || {};

    commit('SET_DESIGN', newDesign);
    if (tabComp.props) {
      const { list } = tabComp.props;
      commit('UPDTATE_TAB', {
        list,
      });
    }
    commit('SET_EXTENSION_CONFIG', { ...(config.profile || {}) });

    return Promise.resolve({
      config,
      design: newDesign,
    });
  },

  // 获取订单列表数据
  FETCH_ORDER_LIST({ state, commit, dispatch }) {
    const {
      tab,
      list,
      pointsName,
      pageType,
      searchCondition,
      caller,
      canUseTradeUmpV1,
    } = state;
    const selectedId = tab.selectedId;
    const selectedList = list[selectedId] || {};
    const {
      loading = false,
      finished = false,
      list: selectedListDataList = [],
    } = selectedList;
    if (loading || finished) {
      return;
    }
    const listPageId = selectedList.pageId;
    const formattedPageId =
      selectedId === 'all' || selectedId === 'topay' ? 'wsc' : '1';
    const params = {
      type: state.isDrug ? 'totuan' : selectedId,
      page: selectedList.page || 1,
      pageId: listPageId || formattedPageId,
      pageType,
      caller,
    };
    const searchValue = searchCondition.value || '';

    if (chinaMobile(searchValue)) {
      params.receivertel = searchValue;
    } else if (searchValue) {
      params.keyword = searchValue;
    }

    commit('UPDATE_LIST', {
      [selectedId]: {
        loading: true,
      },
    });
    return api
      .fetchList(params)
      .then((res) => {
        const { directBuyAgainBtnConfig = {} } = state;
        res.list = (res.list || []).map((item) => {
          item.directBuyAgainBtnConfig = directBuyAgainBtnConfig;
          return item;
        });
        const data = {
          list: selectedListDataList.concat(
            formatList(res.list, pointsName, canUseTradeUmpV1)
          ),
          page: res.page + 1,
          pageId: res.pageId,
          finished: !res.hasNext,
          loading: false,
        };
        commit('UPDATE_LIST', {
          [selectedId]: data,
        });
        if (res.page === 1 && res.hasNext && res.list.length <= 4) {
          return dispatch('FETCH_ORDER_LIST');
        }
      })
      .catch((error) => {
        commit('UPDATE_LIST', {
          [selectedId]: {
            loading: false,
            finished: true,
          },
        });

        wx.showToast({
          title: error.msg || '网络抖了下，请稍候再试',
          icon: 'none',
        });
        const app = getApp();
        app.logger.appError({
          message: '订单列表请求报错',
          detail: {
            ...error,
            errMsg: error?.msg || error?.message,
          },
        });
      });
  },

  // 获取再来一单[直接进入下单页]配置
  FETCH_DIRECT_BUY_AGAIN_BTN_CONFIG({ commit }) {
    return api
      .fetchDirectBuyAgainBtnConfig()
      .then((res) => {
        commit('SET_DIRECT_BUY_AGAIN_BTN_CONFIG', res);
      })
      .catch((error) => {
        wx.showToast({
          title: error.msg || '网络抖了下，请稍候再试',
          icon: 'none',
        });
      });
  },
};
