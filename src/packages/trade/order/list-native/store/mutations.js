export default {
  SET_UMP_GRAY(state, payload) {
    state.canUseTradeUmpV1 = payload;
  },

  SET_DESIGN(state, payload) {
    state.design = payload;
  },

  SET_EXTENSION_CONFIG(state, payload) {
    state.extensionConfig = {
      ...payload,
    };
  },

  SET_CALLER(state, payload) {
    state.caller = payload;
  },

  UPDTATE_TAB(state, payload) {
    Object.assign(state.tab, payload);
  },

  RESET_TAB(state, payload) {
    Object.assign(state.list[payload], {
      list: [],
      page: 1,
      finished: false,
      loading: false,
      pageId: payload === 'all' ? 'wsc' : '1',
    });
  },

  SET_DATA_LOADED(state, payload) {
    state.dataLoaded = payload;
  },

  UPDATE_LIST(state, payload) {
    Object.keys(payload).forEach((key) => {
      Object.assign(state.list[key], payload[key]);
    });
  },

  UPDATE_PAGE_TYPE(state, payload) {
    state.pageType = payload;
  },

  UPDATE_IS_DRUG(state, payload) {
    state.isDrug = payload;
  },

  UPDATE_IS_RETAIL_WEAPP_SCENE(state, payload) {
    state.isRetailWeappScene = payload;
  },

  UPDATE_POINT_NAME(state, payload) {
    state.pointsName = payload;
  },

  UPDATE_RECOMMEND(state, payload) {
    Object.assign(state, payload);
  },

  UPDATE_SEARCH_CONDITION(state, payload) {
    state.searchCondition = {
      ...payload,
    };
  },

  SET_DIRECT_BUY_AGAIN_BTN_CONFIG(state, payload) {
    state.directBuyAgainBtnConfig = payload;
  },

  UPDATE_RECEIVING_CONTENT(state, payload) {
    state.receivingContent = payload;
  },

  UPDATE_GLOBAL_BTNS_DISPLAY(state, payload) {
    state.globalBtnsDisplay.confirmReceiveHotel = payload.confirmReceiveHotel;
  },

  HIDDEN_BOTTOM_BTNS(state, payload) {
    state.globalBtnsDisplay.type = payload.type;
  },

  // 添加定制操作按钮
  ADD_BUTTON_LIST(state, payload) {
    state.cloudBtnListCondition = payload;
  },
};
