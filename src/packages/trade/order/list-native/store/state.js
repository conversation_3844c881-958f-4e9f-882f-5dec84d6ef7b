import deepClone from '@youzan/weapp-utils/lib/clone-deep';
import designMod from '../design.json';

const app = getApp();

const LIST = {
  all: {
    list: [],
    page: 1,
    finished: false,
    loading: false,
    pageId: 'wsc',
  },
  topay: {
    list: [],
    page: 1,
    finished: false,
    loading: false,
    pageId: 'wsc',
  },
  tosend: {
    list: [],
    page: 1,
    finished: false,
    loading: false,
    pageId: '1',
  },
  send: {
    list: [],
    page: 1,
    finished: false,
    loading: false,
    pageId: '1',
  },
  toevaluate: {
    list: [],
    page: 1,
    finished: false,
    loading: false,
    pageId: '1',
  },
  sign: {
    list: [],
    page: 1,
    finished: false,
    loading: false,
    pageId: '1',
  },
  totuan: {
    list: [],
    page: 1,
    finished: false,
    loading: false,
    pageId: '1',
  },
  safe: {
    list: [],
    page: 1,
    finished: false,
    loading: false,
    pageId: '1',
  },
  current: {
    list: [],
    page: 1,
    finished: false,
    loading: false,
    pageId: '1',
  },
  history: {
    list: [],
    page: 1,
    finished: false,
    loading: false,
    pageId: '1',
  },
};
// 目前先兼容个人中心跳转到订单列表页type不存在的情况
const keyList = [
  'all',
  'topay',
  'tosend',
  'send',
  'toevaluate',
  'sign',
  'totuan',
  'safe',
  'current',
  'history',
];

function setList() {
  const isRetailWeappScene = app.getShopInfoSync()?.shop_type === 7;
  let modList = designMod.design || [];
  if (isRetailWeappScene) {
    const isRetailUsercenterTemplate =
      getApp().getShopConfigDataSync()?.retail_weapp_usercenter_template ===
      '0';
    if (isRetailUsercenterTemplate) {
      modList = modList.map((data) => {
        if (data.type === 'list-tabs') {
          data.props.list = [
            {
              id: 'current',
              title: '当前订单',
            },
            {
              id: 'history',
              title: '历史订单',
            },
          ];
          return data;
        }
        return data;
      });
    }
  }

  const config = modList[0] && modList[0].type === 'config' && modList[0];
  const newDesign = config ? modList.slice(1) : modList;
  const tabComp =
    newDesign.find(
      (item) => item.type === 'list-tabs' || item.type === 'custom-list-tabs'
    ) || {};
  if (tabComp.props) {
    const { list } = tabComp.props;
    const groupList = {};
    list.forEach((item) => {
      const { id } = item;
      groupList[id] = {
        list: [],
        page: 1,
        finished: false,
        loading: false,
        pageId: id === 'all' || id === 'topay' ? 'wsc' : '1',
      };
    });
    keyList.forEach((item) => {
      if (!groupList[item]) {
        groupList[item] = {
          list: [],
          page: 1,
          finished: false,
          loading: false,
          pageId: item === 'all' || item === 'topay' ? 'wsc' : '1',
        };
      }
    });

    state.list = groupList;
  } else {
    state.list = deepClone(LIST);
  }
}

const state = {
  design: [],
  dataLoaded: false,
  tab: {
    show: true,
    activeIndex: 0,
    selectedId: 'all',
    list: [],
  },
  extensionConfig: {},
  pageType: 'single',
  isDrug: false,
  isRetailWeappScene: false, // 是否是零售小程序
  caller: '',
  pointsName: '积分',
  searchCondition: {}, // 订单搜索条件
  directBuyAgainBtnConfig: {},
  canUseTradeUmpV1: false,
  receivingContent: '', // 定制收货文案

  // 显示隐藏底部按钮使用
  globalBtnsDisplay: {
    confirmReceiveHotel: true, // 确认入住
    type: [],
  },

  // 云定制传入的操作栏列表按钮添加条件及添加的按钮列表
  cloudBtnListCondition: {
    condition: {
      filterOrderStatus: [], // 需要过滤的订单状态，[] - 全添加
      activityTypes: [], // 活动类型
    },
    cloudBtnList: [], // 添加的按钮列表
  },
};

setList();

export { state };
