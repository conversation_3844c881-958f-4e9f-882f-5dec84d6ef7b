export default {
  selectedTabId(state) {
    return state.tab.selectedId;
  },
  showEmpty(state, getters) {
    const { list } = state;
    return (
      list[getters.selectedTabId] &&
      !list[getters.selectedTabId].list.length &&
      list[getters.selectedTabId].finished
    );
  },
  showNoMore(state, getters) {
    const { list } = state;
    return (
      list[getters.selectedTabId] &&
      (list[getters.selectedTabId].list.length ||
        !list[getters.selectedTabId].finished)
    );
  },
  showRecommend(state, getters) {
    const { list } = state;
    return list[getters.selectedTabId] && list[getters.selectedTabId].finished;
  },
  hasPhoneBlock(state) {
    const { design } = state;
    return design.findIndex((item) => item.type === 'bind-phone') > -1;
  },
  hasSearchBlock(state) {
    // 不知道这个干啥用的，既然零售设计非要改UI，调换搜索bar的位置，为了安全，那这个在零售场景下返回false好了
    const { design, isRetailWeappScene } = state;
    return (
      !isRetailWeappScene &&
      design.findIndex((item) => item.type === 'search-block') > -1
    );
  },
};
