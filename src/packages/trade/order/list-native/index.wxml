<import src="./custom-tpl.wxml" />

<page-container
  forbid-copyright
  forbid-banner
  open-custom-nav
  class="{{ themeClass }} page-{{ deviceType }}"
  is-tab-page="{{ isTabPage }}"
  page-bg-color="{{isRetailWeappScene ? '#f2f2f2' : '#f9f9f9'}}"
  style="--nav-height: {{ navHeight }}px; --order-container: {{ navHeight ? 'none' : 'translateX(0)' }}"
>
<view class="order-container">
  <global-custom-loading wx:if="{{ globalCustomLoading }}" show="{{ !dataLoaded }}" top-offset="{{ isNewIphone ? '-88px': '-44px'}}"   />
  <!-- fixed 的头部 -->
  <view
    wx:if="{{ bindPhone.show}}"
    class="page-phone"
  >
    <!-- 绑定手机号 -->
    <view class="bind__container van-hairline--bottom">
      <view class="bind__tips">{{ bindPhone.tips }}</view>
      <view class="bind__button">
        <theme-view bg="main-bg" border="main-bg" border-after="main-bg">
          <user-authorize  class="btn-login btn-class" authTypeList="{{ ['mobile'] }}">
            登录
          </user-authorize>
        </theme-view>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <search-bar
    wx:if="{{ !(isDrug || isRetailWeappScene) }}"
    class="{{ bindPhone.show ? 'page-search': 'page-phone'}}"
    keyword="{{ keyword }}"
    kdtId="{{ kdtId }}"
    navigateType="{{ navigateType }}"
  />

  <!-- 外侧 view 防止层级错乱  -->
  <view>
    <block wx:for="{{ design }}" wx:key="type">

    <!-- 登录组件 -->
    <block wx:if="{{ item.type === 'bind-phone' }}">
      <!-- fixed 的头部 -->
      <view
        wx:if="{{ bindPhone.show}}"
        class="page-phone"
      >
        <!-- 绑定手机号 -->
        <view class="bind__container van-hairline--bottom">
          <view class="bind__tips">{{ bindPhone.tips }}</view>
          <view class="bind__button">
            <theme-view bg="main-bg" border="main-bg" border-after="main-bg">
               <user-authorize  class="btn-login btn-class" authTypeList="{{ ['mobile'] }}">
                登录
              </user-authorize>
            </theme-view>
          </view>
        </view>
      </view>
    </block>

    <!-- 搜索栏 -->
    <block wx:elif="{{ item.type === 'search-block' }}">
      <search-bar
        wx:if="{{ !isRetailWeappScene }}"
        class="{{ (bindPhone.show && hasPhoneBlock) ? 'page-search': 'page-phone'}}"
        keyword="{{ keyword }}"
        kdtId="{{ kdtId }}"
        navigateType="{{ navigateType }}"
      />
    </block>

    <!-- tab 栏 -->
    <block wx:elif="{{ item.type === 'list-tabs' }}">
      <block wx:if="{{ isRetailWeappScene }}">
        <view
          wx:if="{{ tab.show && !isDrug }}"
          class="{{ tabClass }}">
          <van-tabs
            style="flex: 1;"
            custom-class="th_tabs-l"
            color="{{ themeGeneralColor }}"
            title-active-color="{{ themeGeneralColor }} !important"
            swipe-threshold="{{ 5 }}"
            active="{{ tab.activeIndex }}"
            bind:change="handleTabChange"
          >
            <van-tab
              wx:for="{{ tab.list }}"
              wx:key="id"
              title-style="font-size: 16px;"
              title="{{ item.title }}"
            />
          </van-tabs>
          <view class="retail-opt-con">
            <view class="retail-opt" catch:tap="handlePlaceholderClick">
              <van-icon name="search" size="20px" />
            </view>
          </view>
        </view>
      </block>
      <block wx:else>
        <van-tabs
          wx:if="{{ tab.show && !isDrug }}"
          custom-class="th_tabs-l"
          class="{{ tabClass }}"
          color="{{ themeGeneralColor }}"
          swipe-threshold="{{ 5 }}"
          active="{{ tab.activeIndex }}"
          bind:change="handleTabChange"
        >
          <van-tab
            wx:for="{{ tab.list }}"
            wx:key="id"
            title="{{ item.title }}"
          />
        </van-tabs>
      </block>
    </block>

    <!-- 列表组件 -->
    <view wx:elif="{{ item.type === 'list' }}" style="padding-top: {{ listPaddingTop }}">
      <!-- sh -->
      <around-goods-orders wx:if="{{ tab.selectedId === 'all' }}" />

      <!-- 活动banner -->
      <flow-entrance-banner biz-name="order_list" />

      <!-- 订单列表 if -->
      <list
        wx:if="{{ dataLoaded }}"
        list="{{ list[tab.selectedId].list }}"
        list-type="{{ tab.selectedId }}"
        design-children="{{ item.children }}"
        is-retail-weapp-scene="{{ isRetailWeappScene }}"
        bind:order-click="handleOrderClick"
        bind:btn-click="handleBtnClick"
      />
      <!-- 骨架图 else -->
      <image
        wx:else
        class="skeleton-image"
        mode="widthFix"
        src="https://img01.yzcdn.cn/public_files/2020/10/12/b09031db8d4efaad36f46ae859063f81.png"
      />
    </view>

    <!-- 空列表提示 -->
    <block wx:elif="{{ item.type === 'empty-tip' }}">
      <block wx:if="{{ showEmpty }}">
        <welike-entry hidden="{{ !welikeEntryShow }}" bind:show="onWelikeEntryShow" source-page="order_list" />
        <empty wx:if="{{ showEmpty && !welikeEntryShow }}" />
      </block>
    </block>

    <!-- 列表底部 loading -->
    <block wx:elif="{{ item.type === 'no-more-tip' }}">
      <loadmore
        wx:if="{{ showNoMore }}"
        finished="{{ list[tab.selectedId].finished }}"
      />
    </block>

    <block wx:elif="{{ item.type === 'cps-goods-recommend' }}">
      <cps-goods-recommend
        wx:if="{{ showRecommend }}"
        cpsConfigKey="cps_goods_recommend_order_list"
      />
    </block>

    <block wx:elif="{{ item.type === 'recommend-block' }}">
      <recommend-goods
        wx:if="{{ showRecommend }}"
        biz-name="order_list"
        load-more={{true}}
      />
    </block>

    <!-- 三方自定义组件 -->
    <block wx:elif="{{ item.custom }}">
      <template is="{{ item.type }}" />
    </block>

   </block>
   </view>
  </view>
  <custom-tab-bar force-show="{{ isShowTabbar }}" />
  <account-wx-login id="account-wx-login" />

  <levelup-tip
    event-key="confirm-mission"
    title="收货成功，会员升级至${level}"
  />
  <self-fetch-popup show="{{ showSelfFetchPopup }}" self-fetch-info="{{ selfFetchInfo }}" bindclose="onCloseSelfFetch" />
  <cancel-order-popup is-tab-page="{{ isShowTabbar }}" id="cancel-order-popup" />
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
