import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';

const app = getApp();

WscComponent({
  properties: {},
  data: {
    visible: false,
  },

  attached() {
    // 获取店铺配置，判断是否展示入口
    app
      .request({
        path: '/wscstatcenter/cps/queryCpsConfig.json',
        method: 'GET',
        data: {
          params: JSON.stringify({
            configKeys: ['cps_goods_recommend_order_list'],
          }),
        },
      })
      .then((configs) => {
        try {
          const configJson = get(
            configs,
            'cps_goods_recommend_order_list',
            '{}'
          );
          const enable = JSON.parse(configJson).enable || false;
          this.setYZData({
            visible: enable,
          });
        } catch (e) {
          console.error(e);
        }
      })
      .catch((e) => {
        wx.showToast({ title: e.msg || '店铺配置获取失败', icon: 'none' });
      });
  },

  methods: {
    // 跳转到热卖小程序
    navigateToRmMini() {
      wx.navigateToMiniProgram({
        appId: 'wx63d3fc92d8fdeba2',
        path: 'packages/h5/all/index?entry=tang',
      });
    },
  },
});
