const defaultOptions = {
  show: true,
  selector: '#cancel-order-popup',
};

function getContext() {
  const pages = getCurrentPages();
  return pages[pages.length - 1];
}

function CancelOrderPopup(popupOptions) {
  const options = {
    ...defaultOptions,
    ...popupOptions,
  };

  const context = options.context || getContext();
  const popup = context.selectComponent('#cancel-order-popup');

  if (!popup) {
    console.warn(
      '未找到 cancel-order-popup 节点，请确认 selector 及 context 是否正确'
    );
    return false;
  }

  delete options.context;
  delete options.selector;
  popup.setData(options);
  popup.on('close', () => {
    popup.setData({ show: false });
    options.onClose && options?.onClose();
  });

  popup.on('confirm', (data) => {
    options.onConfirm?.(data);
  });

  popup.close = () => {
    popup.setData({ show: false });
  };

  return popup;
}

export default CancelOrderPopup;
