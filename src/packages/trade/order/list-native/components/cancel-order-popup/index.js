import WscComponent from 'pages/common/wsc-component/index';
import theme from 'shared/common/components/theme-view/theme';

const app = getApp();
WscComponent({
  properties: {
    reasonInfo: {
      type: Object,
      value: {
        buyerCloseReason: [],
        isShowItemPutInCartButton: false,
      },
    },
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'setShow',
    },
    orderNo: String,
    isTabPage: Boolean,
  },

  data: {
    isBuyAgain: true,
    reason: 0,
    themeGeneralColor: '',
    disabled: true,

    popupBottom: 30, // node_modules/@youzan/wsc-tee-trade-common/lib/mixins/tab-mixin.js
  },

  methods: {
    setShow(newVal, oldVal) {
      if (newVal && !oldVal) {
        this.setYZData({
          isBuyAgain: true,
          reason: 0,
          disabled: true,
          popupBottom: this.data.isTabPage ? 49 + 30 : 30,
        });

        theme
          .getThemeColor('general')
          .then((color) => {
            this.setYZData({
              themeGeneralColor: color,
            });
          })
          .catch(() => {});

        app.logger &&
          app.logger.log({
            et: 'view', // 事件类型
            ei: 'view_cancel_order_reason', // 事件标识
            en: '取消订单原因曝光', // 事件名称
            pt: 'ol', // 页面类型

            params: {
              order_no: this.data.orderNo,
            }, // 事件参数
          });
      }
    },

    show() {
      this.setData({ show: true });
    },

    onChangeReason(e) {
      const { reason } = e.target.dataset;

      this.setYZData({
        reason,
        disabled: reason === 0,
      });
    },

    onClose() {
      this.trigger('close');
    },

    onConfirm() {
      if (this.data.disabled) {
        wx.showToast({
          title: '请选择取消原因',
          icon: 'none',
        });
        return;
      }

      const { isBuyAgain, reason } = this.data;
      this.trigger('confirm', { isBuyAgain, reason });
    },

    onChange({ detail: reason }) {
      this.setYZData({ reason });
    },

    handlePutCart({ detail: isBuyAgain }) {
      this.setYZData({ isBuyAgain });
    },
  },
});
