<van-popup
  custom-class="cancel-order-popup"
  show="{{ show }}"
  bind:close="onClose"
  position="bottom"
>
  <view class="cancel-order-popup__title">选择取消原因</view>
  <van-radio-group
    class="cancel-order-popup__reason-list"
    value="{{ reason }}"
    bind:change="onChange"
  >
    <van-cell-group custom-class="cancel-order-popup__group">
      <van-cell
        wx:for="{{ reasonInfo.buyerCloseReason }}"
        wx:key="value"
        wx:for-item="item"
        custom-class="cancel-order-popup__van-cell"
        clickable
        title="{{ item.desc }}"
        data-reason="{{ item.value }}"
        bind:click="onChangeReason"
      >
        <van-radio
          icon-size="18px"
          checked-color="{{ themeGeneralColor }}"
          slot="right-icon"
          name="{{ item.value }}"
        >{{ item.reason }}</van-radio>
      </van-cell>
    </van-cell-group>
  </van-radio-group>
  <div
    wx:if="{{ reasonInfo.isShowItemPutInCartButton }}"
    class="cancel-order-popup__put-cart"
  >
    <van-checkbox
      icon-size="18px"
      checked-color="{{ themeGeneralColor }}"
      value="{{ isBuyAgain }}"
      bind:change="handlePutCart"
    >提交后将本单商品放入购物车</van-checkbox>
  </div>
  <div class="cancel-order-popup__btns" style="padding-bottom: {{ popupBottom }}px">
    <van-button class="cancel-order-popup__btns-van" block round bind:click="onClose">再想想</van-button>
    <van-button
      class="cancel-order-popup__btns-van"
      color="{{ disabled ? '#c8c9cc' : themeGeneralColor }}"
      block
      round
      bind:click="onConfirm"
      type="primary"
    >取消订单</van-button
    >
  </div>
</van-popup>
