<!-- retail -->
<block wx:if="{{isRetailWeappScene}}">
  <view
    class="order-item"
    data-list-type="{{ listType }}"
    data-list-index="{{ listIndex }}"
    data-order-index="{{ orderIndex }}"
    catch:tap="handleOrderClick"
  >
    <view
      wx:if="{{ retailPickUpCode || (listType === 'current' && pickUpCode && expressType !== 2) }}"
      class="order-item__code order-item__code_retail"
      catch:tap="handleRefreshWaitingTime"
    >
      <view class="order-item__code__text">
        {{ retailPickUpCode ? "取餐号：" : "取货号：" }}
        <text class="order-item__code__val">{{ retailPickUpCode || pickUpCode }}</text>
      </view>
      <view class="order-item__time__text">
        <block wx:if="{{ showWaitingTime }}">
          <block wx:if="{{ waitingTimeText }}">
            预计准备
            <text class="order-item__time__val">{{ waitingTimeText }}</text>
            <van-icon
              class="{{ retailIconAnimationClass }}"
              style="margin-left: 6px"
              name="replay"
              size="14px"
            />
          </block>
          <block wx:else>制作完成</block>
        </block>
      </view>
    </view>
    <retail-goods-card
      goodsList="{{goodsList}}"
      hotel="{{ hotel }}"
      payPrice="{{ payPrice }}"
    />
    <view wx:if="{{ showPayInfo&&payLast }}" class="order-item__pay">
      <view>{{ payLast }}</view>
    </view>
  </view>
</block>

<!-- wsc -->
<block wx:else>
  <view
    class="order-item"
    data-list-type="{{ listType }}"
    data-list-index="{{ listIndex }}"
    data-order-index="{{ orderIndex }}"
    catch:tap="handleOrderClick"
  >
    <view class="order-item__no van-c-gray-dark">
      <text>订单编号：{{  crmOfflineType === CRM_OFFLINE_TYPE.OFFLINE ? outBizNo : orderNo }}</text>
    </view>
    <view
      wx:if="{{ retailPickUpCode || listType === 'current' && pickUpCode }}"
      class="order-item__code"
      catch:tap="handleRefreshWaitingTime"
    >
      <view class="order-item__code__text">
        {{ retailPickUpCode ? "取餐号：" : "取货号：" }}
        <text class="order-item__code__val">{{ retailPickUpCode || pickUpCode }}</text>
      </view>
      <view class="order-item__time__text">
        <block wx:if="{{ showWaitingTime }}">
          <block wx:if="{{ waitingTimeText }}">
            预计准备
            <text class="order-item__time__val">{{ waitingTimeText }}</text>
            <van-icon
              class="{{ retailIconAnimationClass }}"
              style="margin-left: 6px"
              name="replay"
              size="14px"
            />
          </block>
          <block wx:else>制作完成</block>
        </block>
      </view>
    </view>
    <goods-card
      wx:for="{{ goodsList }}"
      goodsList="{{goodsList}}"
      wx:key="goodsId"
      wx:if="{{ index < limit }}"
      thumb="{{ item.thumb }}"
      title="{{ item.title }}"
      price="{{ item.price }}"
      sku="{{ item.sku }}"
      num="{{ item.num }}"
      hotel="{{ hotel }}"
      tag-list="{{ item.tagList }}"
      goods-img-tag="{{ item.goodsImgTag }}"
      is-shipped="{{ item.isShipped }}"
      is-prescription-drug-goods="{{item.isPrescriptionDrugGoods}}"
      pre-sale-express-time-desc="{{ item.preSaleExpressTimeDesc }}"
    />
    <view wx:if="{{ goodsList.length > limit && !hotel.show }}" class="order-item__check-more van-center van-hairline--bottom">
      <view style="color: #323233">查看全部{{ goodsList.length }}件商品</view>
    </view>
    <view wx:if="{{ showPayInfo }}" class="order-item__pay">
      {{ payDesc }}：
      <view style="color: #323233" class="order-item__pay__price">{{ payPrice }}</view>
      <view wx:if="{{payLast}}">{{ payLast }}</view>
    </view>
  </view>
</block>


