import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    hotel: Object,
    isRetailWeappScene: Boolean,
    payPrice: {
      type: String,
      observer: 'processPrice',
    },
    goodsList: {
      type: Array,
      value: [],
      observer: 'observerGoodsList',
    },
  },
  data: {
    drug: 'https://b.yzcdn.cn/path/to/cdn/dir/isDrugTag_3x.png',
    totalNum: 0,
    showGoodsList: [],
    price: {},
  },
  methods: {
    observerGoodsList(list) {
      let totalNum = 0;
      list.forEach((item) => {
        totalNum += item.num;
      });
      this.setData({
        totalNum,
        showGoodsList: list.slice(0, 3),
      });
    },

    processPrice(v) {
      const values = v.split(' + ');
      // const values = ['200积分'];
      // const values = ['¥100.00'];
      // const values = ['200积分', '¥100.00'];
      const replace = (v) => (v ? v.replace('¥', '') : '');

      const price = {};
      const value = values.pop();

      if (value.indexOf('¥') === 0) {
        price.value = replace(value);
      } else {
        // 如果最后一位不是金额 则一定是积分
        price.integral = value;
      }

      const integral = values.pop();
      // 如果还有 它一定是积分
      if (integral) {
        price.integral = integral;
      }

      this.setData({ price });
    },
  },
});
