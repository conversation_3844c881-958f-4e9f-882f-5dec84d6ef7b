.card__tag-item {
  margin-right: 5px;
}

.card__title {
  line-height: 20px;
  display: flex;
  align-items: center;
}

.card__title__goods-name {
  font-weight: 700;
  word-break: break-all;
  font-size: 14px;
}

.goods-card__title-tag {
  width: 43px;
  height: 16px;
  margin-right: 5px;
}

.card__title__shipped-tag {
  color: #faab0c;
  float: right;
}

.card__presale-date {
  font-size: 12px;
  color: #ed6a0c;
  margin: 5px 0;
}

.card__hotel {
  font-size: 12px;
  color: #666;
  line-height: 20px;
}

.goods-card-index--card__price {
  color: #323233;
}

$height: 74px;

.retail-goods-card {
  display: flex;
  align-items: flex-start;
  box-sizing: border-box;
  background-color: #fff !important;
  color: #323233;
  font-size: 12px;
  padding: 8px 16px;
  position: relative;
  min-height: $height;
  border-radius: 8px;

  .retail-left {
    width: 72px;
    height: $height;
    flex: none;

    &.retail-left__more {
      width: auto;
      flex: 1;
      display: flex;
      overflow: auto;
      white-space: nowrap;
    }

    .retail-card__thumb {
      width: 64px;
      height: 64px;
      flex: none;
      border-radius: 8px;
      overflow: hidden;
      margin-right: 8px;
      position: relative;
      display: inline-block;
      margin-top: 8px;

      &_img {
        width: 100%;
        height: 100%;
      }

      &_num {
        position: absolute;
        bottom: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        font-size: 12px;
        height: 20px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
      }
    }
  }

  .retail-right {
    width: auto;
    flex: none;
    padding-left: 10px;
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    font-size: 12px;
    border-radius: 8px;
    min-height: $height;
    justify-content: center;
    background-color: #fff;

    .card-price__integral {
      font-size: 14px;
      font-weight: 500;
    }

    .card-price__price {
      font-weight: 700;
      margin-bottom: 3px;
      font-family: 'Avenir';

      &_pr {
        font-size: 16px;
      }

      &_desc {
        margin-right: 1px;
      }
    }

    .card-price__num {
      color: #969799;
    }
  }

  .retail-center {
    flex: 1;
    padding-top: 8px;

    .van-card__desc {
      color: #999;
      line-height: 20px;
    }
  }
}
