<van-card
  price="{{ price }}"
  num="{{ num }}"
  custom-class="card"
  currency=""
  tag="{{ goodsImgTag || '' }}"
  style="--card-price-color: #323233"
>
  <image
    slot="thumb"
    class="card__thumb"
    src="{{ thumb }}"
    mode="aspectFit"
  />

  <view
    slot="title"
    class="card__title"
  >
    <view
      wx:if="{{ isShipped }}"
      class="card__title__shipped-tag"
    >
      已发货
    </view>
    <image
      wx:if="{{ isPrescriptionDrugGoods }}"
      class="goods-card__title-tag"
      src="{{drug}}"
      alt="处方药"
    />
    <text class="card__title__goods-name">{{ title }}</text>
  </view>

  <view slot="desc">
    <view class="sku-desc">{{ sku }}</view>
    <view class="card__presale-date" v-if="preSaleExpressTimeDesc">
      {{ preSaleExpressTimeDesc }}
    </view>
  </view>

  <view slot="tags">
    <van-tag
      wx:for="{{ tagList }}"
      wx:key="index"
      color="var(--ump-tag-bg, #f2f2ff)"
      text-color="var(--ump-tag-text, #323233)"
      plain="{{ item.plain }}"
      class="card__tag-item"
    >{{ item.text }}</van-tag>

    <view class="card__hotel" wx:if="{{ hotel.show }}">
      <view>入住：{{ hotel.checkInTime }}</view>
      <view>离店：{{ hotel.checkOutTime }}，共{{ hotel.num }}晚</view>
    </view>
  </view>
</van-card>
