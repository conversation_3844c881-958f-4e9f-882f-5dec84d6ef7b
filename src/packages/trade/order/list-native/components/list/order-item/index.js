import WscComponent from 'pages/common/wsc-component/index';
import { CRM_OFFLINE_TYPE } from '../../../common/constant';
import { mapState } from '@youzan/vanx';
import api from '../../../common/api';
import { formatWaitingTime } from '../../../common/format';

WscComponent({
  properties: {
    listType: String,
    listIndex: Number,
    orderIndex: Number,
    orderNo: String,
    outBizNo: String,
    crmOfflineType: Number,
    goodsList: Array,
    hotel: Object,
    showPayInfo: Boolean,
    payDesc: String,
    payLast: String,
    payPrice: String,
    pickUpCode: String,
    retailPickUpCode: String,
    waitingTimeList: {
      type: Array,
      default: null,
      observer: 'obWaitingTimeList',
    },
    isRetailWeappScene: Boolean,
    expressType: Number,
  },

  data: {
    limit: 1, // 展示商品的数量
    isDrugList: false,
    orderNoShowText: '',
    CRM_OFFLINE_TYPE,
    showWaitingTime: false,
    waitingTimeText: '',
    retailIconAnimationClass: '',
  },

  mapData: {
    ...mapState(['isDrug']),
  },

  attached() {
    this.canRefreshWaitingTime = true;
  },

  ready() {
    this.setYZData({
      isDrugList: this.data.isDrug,
    });
  },

  methods: {
    /**
     * 点击订单回传"坐标"
     * {
     *   listType    当前 tab
     *   listIndex   列表第几项
     *   orderIndex  此项第几个 order
     * }
     */
    handleOrderClick(e) {
      this.triggerEvent('order-click', e.currentTarget.dataset);
    },

    obWaitingTimeList(val) {
      const { orderNo } = this.data;
      if (!orderNo || val?.length === 0) return;
      // 为什么每次都要遍历
      // 因为父组件传入的orderIndex都是0，很奇怪的bug，怀疑是上层wx:for="{{ listItem.orderList }}"这个写法导致的
      const waitingTime = (val || []).find(
        (order) => order.orderNo === orderNo
      );
      const {
        productTime = 0,
        text = '',
        needDisplay = false,
      } = waitingTime || {};
      this.setYZData({
        showWaitingTime: needDisplay,
        waitingTimeText: productTime > 0 ? text : '',
      });
    },

    handleRefreshWaitingTime() {
      this.setYZData(
        {
          retailIconAnimationClass: 'order-item__time__icon',
        },
        () => {
          setTimeout(() => {
            this.setYZData({
              retailIconAnimationClass: '',
            });
          }, 1000);
        }
      );

      if (!this.canRefreshWaitingTime) return;
      this.canRefreshWaitingTime = false;
      const { showWaitingTime, waitingTimeText, orderNo } = this.data;
      if (!showWaitingTime || !waitingTimeText) return;
      api.getBatchShopWaitingTime([orderNo]).then((data) => {
        const { productTime = 0 } = data[0];
        const text = formatWaitingTime(data[0]);
        this.setYZData({
          waitingTimeText: productTime > 0 ? text : '',
        });
      });
      setTimeout(() => {
        this.canRefreshWaitingTime = true;
      }, 5000);
    },
  },
});
