@import 'shared/common/css/helper/index.wxss';

.order-item__no {
  padding: 0 15px 10px;
  font-size: 12px;
}

.order-item__check-more {
  line-height: 40px;
}

.order-item__pay {
  padding: 0 15px;
  line-height: 40px;
  display: flex;
  justify-content: flex-end;
}

.order-item__pay view {
  padding-left: 5px;
}

.order-item__pay__price {
  display: inline-block;
}

.order-item__code {
  display: flex;
  margin: 0 16px 8px;
  padding: 0 12px;
  height: 40px;
  font-size: 12px;
  line-height: 20px;
  align-items: center;
  justify-content: space-between;
  color: #000;
  background: #F2F3F5;
  border-radius: 4px;
}

.order-item__code_retail {
  margin: 10px 16px 0;
}

.order-item__code__text {
  display: flex;
}

.order-item__time__icon {
  -webkit-animation: iconAnimation 0.5s linear 1;
  /* Safari 和 Chrome */
  animation: iconAnimation 0.5s linear 1;
}

@keyframes iconAnimation {
  from {
    transform: rotate(0deg)
  }

  to {
    transform: rotate(360deg)
  }
}

.order-item__code__val {
  font-size: 14px;
  font-weight: 700;
}

.order-item__time__text {
  display: flex;
  font-weight: 500;
}

.order-item__time__val {
  color: var(--general);
}