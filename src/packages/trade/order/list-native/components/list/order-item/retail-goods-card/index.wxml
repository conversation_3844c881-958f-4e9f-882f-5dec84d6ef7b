<block>
  <!-- 多个商品 -->
  <block wx:if="{{goodsList.length>1}}">
    <view class="retail-goods-card">
      <view class="retail-left retail-left__more">
        <view wx:for="{{showGoodsList}}" wx:key="index" class="retail-card__thumb">
          <image
            class="retail-card__thumb_img"
            src="{{ item.thumb }}"
            mode="aspectFit"
          />
          <view class="retail-card__thumb_num" wx:if="{{item.num>1}}">
            x{{item.num}}
          </view>
        </view>
        <van-icon wx:if="{{goodsList.length>3}}"
          name="https://b.yzcdn.cn/public_files/2023/01/03/img_v2_925421af-3732-460b-9caf-831ee416711g.png"
          size="30"
        />
      </view>
      <view class="retail-right">
        <view class="card-price__integral" wx:if="{{price.integral}}">
          {{price.integral}}
        </view>
        <view class="card-price__price" wx:if="{{price.value}}">
          <text wx:if="{{price.integral}}">+</text>
          <text class="card-price__price_desc">¥</text>
          <text class="card-price__price_pr">
            {{price.value}}
          </text>
        </view>
        <view class="card-price__num">共{{ totalNum }}件</view>
      </view>
    </view>
  </block>

  <!-- 单个商品 -->
  <block wx:else>
    <block wx:for="{{goodsList}}" wx:key="index">
      <view class="retail-goods-card" wx:key="goodsId">
        <view class="retail-left">
          <image
            class="retail-card__thumb"
            src="{{ item.thumb }}"
            mode="aspectFit"
          />
        </view>
        <view class="retail-center">
          <view class="retail-title card__title">
            <view wx:if="{{ item.isShipped }}" class="card__title__shipped-tag">已发货</view>
            <image
              wx:if="{{ item.isPrescriptionDrugGoods }}"
              class="goods-card__title-tag"
              src="{{drug}}"
              alt="处方药"
            />
            <text class="card__title__goods-name">{{ item.title }}</text>
          </view>
          <view class="retail-desc">
            <view class="van-card__desc">{{item.sku}}</view>
            <view class="card__presale-date" v-if="preSaleExpressTimeDesc">
              {{ item.preSaleExpressTimeDesc }}
            </view>
          </view>
          <view class="retail-tag">
            <van-tag
              wx:for="{{ item.tagList }}"
              wx:key="index"
              wx:for-item="tagItem"
              color="var(--ump-tag-bg, #f2f2ff)"
              text-color="var(--ump-tag-text, #323233)"
              plain="{{ tagItem.plain }}"
              round
              class="card__tag-item"
            >
              {{ tagItem.text }}
            </van-tag>
            <view class="card__hotel" wx:if="{{ hotel.show }}">
              <view>入住：{{ hotel.checkInTime }}</view>
              <view>离店：{{ hotel.checkOutTime }}，共{{ hotel.num }}晚</view>
            </view>
          </view>
        </view>
        <view class="retail-right">
          <view class="card-price__integral" wx:if="{{price.integral}}">
            {{price.integral}}
          </view>
          <view class="card-price__price" wx:if="{{price.value}}">
            <text wx:if="{{price.integral}}">+</text>
            <text class="card-price__price_desc">¥</text>
            <text class="card-price__price_pr">
              {{price.value}}
            </text>
          </view>
          <view class="card-price__num">共{{ item.num }}件</view>
        </view>
      </view>
    </block>
  </block>

</block>
