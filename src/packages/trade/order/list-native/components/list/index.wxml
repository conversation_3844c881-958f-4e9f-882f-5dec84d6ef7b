<import src="../../custom-tpl.wxml" />

<block
  wx:for="{{ formattedList }}"
  wx:for-item="listItem"
  wx:for-index="listIndex"
  wx:key="listIndex"
>
  <view class="list-item {{isRetailWeappScene?'list-item-retail':''}}">
    <block wx:for="{{ designChildren }}" wx:key="type">
      <block wx:if="{{ item.type === 'list-item-header' }}">
        <view
          class="list-item__header"
          data-list-type="{{ listType }}"
          data-list-index="{{ listIndex }}"
          data-order-index="{{ 0 }}"
          catch:tap="handleClickListHeader"
        >
          <template data="{{listItem: listItem}}" is="{{isRetailWeappScene?'retail-header':'header'}}" />
        </view>
      </block>

      <block wx:if="{{ item.type === 'order-list' }}">
        <order-item
          wx:for="{{ listItem.orderList }}"
          wx:for-item="orderItem"
          wx:for-index="orderIndex"
          wx:key="orderIndex"
          hotel="{{ orderItem.hotel }}"
          list-type="{{ listType }}"
          list-index="{{ listIndex }}"
          order-index="{{ orderIndex }}"
          order-no="{{ orderItem.orderNo }}"
          out-biz-no="{{ orderItem.outBizNo }}"
          crm-offline-type="{{ listItem.crmOfflineType }}"
          goods-list="{{ orderItem.goodsList }}"
          show-pay-info="{{ orderItem.showPayInfo }}"
          pay-desc="{{ orderItem.payDesc }}"
          pay-last="{{ orderItem.payLast }}"
          pay-price="{{ orderItem.payPrice }}"
          pick-up-code="{{ orderItem.pickUpCode }}"
          retail-pick-up-code="{{ listItem.retailPickUpCode }}"
          waiting-time-list="{{ waitingTimeList }}"
          is-retail-weapp-scene="{{isRetailWeappScene}}"
          express-type="{{ orderItem.expressType }}"
          bind:order-click="handleOrderClick"
        />
      </block>

      <!-- TODO交易3.0订单暂时屏蔽操作按钮 -->
      <block wx:if="{{ item.type === 'list-item-footer' && !listItem.isVideoThirdComponent }}">
        <view
          wx:if="{{ listItem.btnList.length }}"
          class="list-item__footer {{isRetailWeappScene?'':'van-hairline--top'}} van-right"
        >
          <view class="list-item-footer__extend">
            <block wx:if="{{ listItem.restBtnList.length}}">
              <van-icon name="ellipsis" size="24" bind:click="toggleShowRestBtn" data-list-item="{{ listItem }}" />
              <view
                wx:if="{{ showRestBtn[listItem.orderNo] }}"
                class="list-item-footer__extend-popup"
              >
                <view
                  class="list-item-footer__extend-btn"
                  wx:for="{{listItem.restBtnList}}"
                  wx:for-item="btn"
                  wx:for-index="btnIndex"
                  data-list-type="{{ listType }}"
                  data-list-index="{{ listIndex }}"
                  data-btn="{{ btn.value }}"
                  data-list-item="{{ listItem }}"
                  bind:tap="handleBtnClick"
                >
                  {{ btn.text }}
                </view>
              </view>
            </block>
          </view>
          <view class="list-item-footer__content">
          <view
            wx:for="{{ listItem.showBtnList }}"
            wx:for-item="btnItem"
            wx:for-index="btnIndex"
            wx:key="btnIndex"
            class="list-item-footer__block"
          >
            <van-button
              size="small"
              type="{{ btnItem.type }}"
              plain="{{ btnItem.plain }}"
              disabled="{{ btnItem.disabled }}"
              icon="{{ btnItem.icon }}"
              custom-class="list-item__btn {{ btnItem.type === 'danger' ?  btnItem.plain ? 'th_btn-d-p' : 'th_btn-d' : '' }}"
              data-list-type="{{ listType }}"
              data-list-index="{{ listIndex }}"
              data-btn="{{ btnItem.value }}"
              data-list-item="{{ listItem }}"
              bind:click="handleBtnClick"
              round
              >{{ btnItem.text }}</van-button
            >
            <view
              wx:if="{{ (btnItem.value === 'buyAgain' || btnItem.value === 'directBuyAgain') && listItem.repurchaseCoupon.id && btnList[listIndex][btnIndex].showCoupon }}"
              class="list-item-footer__tip"
            >
              <van-icon
                name="https://b.yzcdn.cn/public_files/e49f5b413529e624ab6a538642bf4eeb.png"
                size="16"
              />
              <view
                data-list-type="{{ listType }}"
                data-list-index="{{ listIndex }}"
                data-btn="{{ btnItem.value }}"
                data-list-item="{{ listItem }}"
                catch:tap="handleBtnTipClick"
              >
                领取 {{ listItem.repurchaseCoupon.valueCopywriting }}{{
                listItem.repurchaseCoupon.unitCopywriting }}复购券，下单立享优惠
              </view>
              <van-icon
                name="cross"
                color="#fff"
                size="12"
                data-list-index="{{ listIndex }}"
                data-btn-index="{{ btnIndex }}"
                data-btn-list="{{ btnList }}"
                data-list-item="{{ listItem }}"
                bind:click="closeCouponShow"
              />
            </view>
            </view>
          </view>
        </view>
      </block>

      <!-- 三方自定义组件 -->
      <block wx:elif="{{ item.custom  }}">
        <template
          is="{{ item.type }}"
          data="{{  data: { listItem, headerData: listItem.listItemHeaderData, itemData: listItem.orderItemsData, footerData: listItem.listItemFooterData }  }}"
        />
      </block>
    </block>

    <view
      class="video-third-component-guide"
      wx:if="{{ listItem.isVideoThirdComponent }}"
      bind:tap="handleShowWxVideoGuide"
    >
      <view class="video-third-component-text">视频号订单，请前往视频号订单中心操作</view>
      <van-icon name="arrow" color="#ED6A0C" size="12" />
    </view>
  </view>

  <welike-entry wx:if="{{ listIndex === 0 }}" source-page="order_list" />
</block>

<!-- 交易组件3.0订单引导弹窗,后面会下掉 -->
<van-popup
  show="{{ showWxVideoGuide }}"
  round
  position="bottom"
  bind:close="onWxVideoGuideClose"
>
  <view class="wxvideo-guide-pop">
    <view class="pop-header">查看/处理视频号订单</view>
    <image
      src="https://b.yzcdn.cn/public_files/b4bd57493510a13fe591b1c6e8bd65f0.png"
      mode="widthFix"
      class="wxvideo-guide-img"
      alt=""
    />
     <view class="wxvideo-guide-btn" bind:tap="onWxVideoGuideClose">我知道了</view>
  </view>
</van-popup>



<template name="retail-header">
    <view class="retail-list-item__shop">
      <van-icon name="https://b.yzcdn.cn/public_files/2023/01/03/img_v2_866bad2b-59d9-4542-b554-3131525c7d8g.png" size="20" class="retail-list-item__shop-icon"/>
      <text class="retail-list-item__shop-name">
        {{ listItem.shopName }}
      </text>
    </view>
    <view class="retail-list-item__order-state" style="color: #323233"> {{ listItem.orderStateStr }} </view>
</template>

<template name="header">
    <!-- 批发订单展示批发订单标识 -->
    <view wx:if="{{ listItem.listItemHeaderData.isWholesaleOrder }}" class="list-item__tag list-item-wholesale-flag">批发订单</view>
    <view wx:if="{{ listItem.crmOfflineOrderTag }}" class="list-item__tag">{{ listItem.crmOfflineOrderTag }}</view>
    <text class="list-item__shop-name"> {{ listItem.shopName }} </text>
    <view class="list-item__order-state" style="color: #323233"> {{ listItem.orderStateStr }} </view>
</template>


