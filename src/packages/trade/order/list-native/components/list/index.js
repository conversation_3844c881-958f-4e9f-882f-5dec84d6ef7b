import WscComponent from 'pages/common/wsc-component/index';
import asyncEvent from 'shared/utils/async-event';
import api from '../../common/api';
import { CRM_OFFLINE_TEXT } from '../../common/constant';
import { formatWaitingTime } from '../../common/format';

const app = getApp();

WscComponent({
  properties: {
    list: {
      type: Array,
      value: [],
      observer: 'formatterOrderItem',
    },
    listType: String,
    designChildren: {
      type: Array,
      value: [],
    },
    isRetailWeappScene: Boolean,
  },

  data: {
    formattedList: [],
    btnList: [],
    showRestBtn: {},
    showWxVideoGuide: false,
    waitingTimeList: [],
  },

  methods: {
    toggleShowRestBtn(e) {
      const { listItem } = e.currentTarget.dataset;
      const showRestBtn = {
        [listItem.orderNo]: !this.data.showRestBtn[listItem.orderNo],
      };
      this.setYZData({
        showRestBtn,
      });
    },
    moveButtonSequence(sequence, btnList) {
      sequence.forEach((item) => {
        const confirmReceiveIndex = btnList.findIndex((btnItem) => {
          return btnItem.value === item;
        });
        if (confirmReceiveIndex !== -1) {
          const item = btnList[confirmReceiveIndex];
          btnList.splice(confirmReceiveIndex, 1);
          btnList.push(item);
        }
      });
    },
    formatterOrderItem(value = []) {
      const { listType } = this.data;
      const formattedList = value.map((listItem, index) => {
        const { btnList = [], crmOfflineType } = listItem;
        const crmOfflineOrderTag = CRM_OFFLINE_TEXT[crmOfflineType];

        let restBtnList = [];
        let showBtnList = [];
        const newBtnList = [...btnList];
        // 零售店铺,确认按钮显示在最右侧
        if (this.data.isRetailWeappScene) {
          this.moveButtonSequence(
            ['confirmReceive', 'buyAgain', 'directBuyAgain'],
            newBtnList
          );
        }

        if (newBtnList.length > 3) {
          restBtnList = newBtnList.slice(0, newBtnList.length - 3);
          showBtnList = newBtnList.slice(-3);
        } else {
          showBtnList = [...newBtnList];
        }
        return {
          ...listItem,
          restBtnList,
          showBtnList,
          listItemHeaderData: {
            orderStateStr: listItem.orderStateStr,
            shopName: listItem.shopName,
            listType,
            listIndex: index,
            isWholesaleOrder: listItem?.orderList?.[0]?.extra?.isWholesaleOrder,
          },
          orderItemsData: {
            listType,
            listIndex: index,
            orderList: listItem.orderList.map((orderItem, orderIndex) => {
              const {
                hotel,
                orderNo,
                goodsList,
                showPayInfo,
                payDesc,
                payLast,
                payPrice,
                pickUpCode,
                outBizNo,
              } = orderItem;
              return {
                outBizNo,
                orderIndex,
                listType,
                hotel,
                orderNo,
                goodsList,
                showPayInfo,
                payDesc,
                payLast,
                payPrice,
                pickUpCode,
              };
            }),
          },
          listItemFooterData: {
            listType,
            listIndex: index,
            btnList: listItem.btnList.map((btnItem, btnIndex) => {
              const { disabled, value, text, showCoupon = false } = btnItem;
              return {
                disabled,
                value,
                text,
                btnIndex,
                showCoupon,
              };
            }),
          },
          crmOfflineOrderTag,
          isVideoThirdComponent:
            listItem?.orderList?.[0]?.extra?.isVideoThirdComponent,
        };
      });

      const btnList = formattedList.map((item) =>
        item.showBtnList.map((btnItem, btnIndex) => {
          const { disabled, value, text, showCoupon = false } = btnItem;
          return {
            disabled,
            value,
            text,
            btnIndex,
            showCoupon,
          };
        })
      );
      const paidPromotionBtn = ['paidPromotion', 'liveQrCode', 'coupon'];
      formattedList.forEach((item) => {
        item.btnList.forEach((btn) => {
          if (paidPromotionBtn.indexOf(btn.value) > -1) {
            app.logger &&
              app.logger.log({
                et: 'view', // 事件类型
                ei: 'view_marketing_paidpro', // 事件标识
                en: '支付有礼按钮曝光', // 事件名称
                params: {
                  type: btn.value,
                  orderNo: item.orderNo,
                },
              });
          } else if (btn.value === 'buyAgain' && item.repurchaseCoupon) {
            app.logger &&
              app.logger.log({
                et: 'view', // 事件类型
                ei: 'view_buyagain_coupon', // 事件标识
                en: '复购券浮层曝光', // 事件名称
                params: {
                  type: btn.value,
                  orderNo: item.orderNo,
                },
              });
          } else if (btn.value === 'fissionCoupon') {
            app.logger &&
              app.logger.log({
                et: 'view', // 事件类型
                ei: 'splitcoupon_button_view', // 事件标识
                en: '领裂变券按钮曝光', // 事件名称
                params: {
                  type: btn.value,
                  orderNo: item.orderNo,
                },
              });
          }
        });
      });

      this.setYZData({
        formattedList,
        btnList,
        showRestBtn: {},
      });
      this.getBatchWaitingTime();
    },

    getBatchWaitingTime() {
      const { listType, list } = this.properties;
      if (listType === 'current' && list.length > 0) {
        const orderNos = list.map((item) => item.orderNo);
        api
          .getBatchShopWaitingTime(orderNos)
          .then((data = []) => {
            const waitingTimeList = data.map((order) => {
              const text = formatWaitingTime(order);
              return {
                ...order,
                text,
              };
            });
            this.setYZData({ waitingTimeList });
          })
          .catch(() => {
            this.setYZData({ waitingTimeList: [] });
          });
      } else {
        this.setYZData({ waitingTimeList: [] });
      }
    },

    handleOrderClick(e) {
      this.triggerEvent(e.type, e.detail);
      this.setYZData({
        showRestBtn: {},
      });
    },

    closeCouponShow(e) {
      const { listIndex, btnIndex, btnList, listItem } =
        e.currentTarget.dataset;
      const newBtnList = btnList;
      newBtnList[listIndex][btnIndex].showCoupon = false;
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'close_buyagain_coupon', // 事件标识
          en: '复购券浮层关闭', // 事件名称
          pt: 'ol', // 页面类型
          params: {
            orderNo: listItem.orderNo,
          }, // 事件参数
        });
      this.setYZData({
        btnList: newBtnList,
      });
    },

    handleShowWxVideoGuide() {
      this.setYZData({
        showWxVideoGuide: true,
      });
    },

    onWxVideoGuideClose() {
      this.setYZData({
        showWxVideoGuide: false,
      });
    },

    /**
     * 点击订单的头部信息，需跳转订单详情页
     * {
     *   listType   当前tab
     *   listIndex  列表第几项
     *   orderIndex 0
     * }
     */
    handleClickListHeader(e) {
      const { listType, listIndex, orderIndex } = e.currentTarget.dataset;
      const param = {
        listType,
        listIndex,
        orderIndex,
      };

      this.triggerEvent('order-click', param);
      this.setYZData({
        showRestBtn: {},
      });
    },

    /**
     * 点击按钮回传"坐标"
     * {
     *   listType    当前 tab
     *   listIndex   列表第几项
     *   btn         btn 的 value
     * }
     */
    handleBtnClick(e) {
      const {
        btn,
        listItem: { orderNo, btnList },
      } = e.currentTarget.dataset;
      const value = btnList.find((e) => e.value === btn);
      asyncEvent.triggerAsync
        .apply(getApp(), [
          'beforeOrderBtnClickAsync',
          {
            type: btn,
            value: value.text,
            orderNo,
          },
        ])
        .then(() => {
          this.handleBtnCenter(e);
        })
        .catch(() => {});
    },

    handleBtnCenter(e) {
      const { listType, listIndex, btn, listItem } = e.currentTarget.dataset;

      const param = {
        listType,
        listIndex,
        btn,
      };
      if (
        (btn === 'buyAgain' || btn === 'directBuyAgain') &&
        listItem.repurchaseCoupon.id
      ) {
        return this.handleBtnTipClick(e);
      }
      this.triggerEvent('btn-click', param);
      this.setYZData({
        showRestBtn: {},
      });
    },

    handleBtnTipClick(e) {
      const { listType, listIndex, btn, listItem } = e.currentTarget.dataset;

      const param = {
        listType,
        listIndex,
        btn,
      };

      api
        .afterPurchaseVouchers(listItem.repurchaseCoupon.id)
        .then((res = {}) => {
          const repurchaseCouponInfo = listItem.repurchaseCoupon;
          param.extraData = {
            repurchaseCoupon: {
              ...res,
              code: 0,
              valueCopywriting: repurchaseCouponInfo.valueCopywriting,
              unitCopywriting: repurchaseCouponInfo.unitCopywriting,
            },
          };
          this.triggerEvent('btn-click', param);
        })
        .catch((err) => {
          param.extraData = {
            repurchaseCoupon: err,
          };
          this.triggerEvent('btn-click', param);
        })
        .finally(() => {
          this.setYZData({
            showRestBtn: {},
          });
        });
    },
  },
});
