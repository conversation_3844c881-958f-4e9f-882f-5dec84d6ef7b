.list-item {
  &__tag {
    box-sizing: border-box;
    min-width: 56px;
    line-height: 16px;
    background-color: #e8eaee;
    border-radius: 2px;
    color: #2c2c2d;
    font-size: 12px;
    text-align: center;
    margin-right: 4px;
    padding: 0 4px;
  }

  &-wholesale-flag {
    height: 16px;
    min-width: 112rpx;
    color: #323233;
    background-color: #ebedf0;
  }

  &__shop-name {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 32px;
  }

  .retail-list-item__shop {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 32px;

    &-icon {
      margin-right: 2px;
      position: relative;
      top: 4px;
    }

    &-name{
      position: relative;
      top: -1px;
    }
  }

  &__order-state {
    white-space: nowrap;
    text-align: right;
  }

  .retail-list-item__order-state {
    white-space: nowrap;
    text-align: right;
    position: relative;
    top: 2px;
  }

  .th_btn-d {
    background: var(--main-bg, #323233) !important;
    color: var(--main-text, #fff) !important;
    border: var(--main-bg, #323233) 1px solid !important;
  }

  .th_btn-d-p {
    color: var(--icon, #323233) !important;
    border: var(--icon, #323233) 1px solid !important;
  }
}

.video-third-component-guide {
  height: 40px;
  padding: 10px 16px;
  box-sizing: border-box;
  background-color: #fffbe8;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .video-third-component-text {
    font-size: 14px;
    line-height: 20px;
    color: #ed6a0c;
  }
}

.wxvideo-guide-pop {
  display: flex;
  flex-direction: column;
  align-items: center;

  .pop-header {
    font-weight: 500;
    font-size: 32rpx;
    line-height: 88rpx;
    margin-bottom: 16rpx;
  }

  .wxvideo-guide-img {
    width: 636rpx;
  }

  .wxvideo-guide-btn {
    width: 686rpx;
    height: 80rpx;
    margin-top: 32rpx;
    background: linear-gradient(270deg, #ff6034 0%, #ee0a24 100%);
    border-radius: 40rpx;
    line-height: 80rpx;
    font-size: 14px;
    text-align: center;
    color: #ffffff;
  }
}

.list-item-footer {
  &__block {
    position: relative;

    &:first-child {
      .list-item-footer__tip {
        left: 0;
        right: auto;
        transform: translate(0, 0);
      }

      .list-item-footer__tip::after {
        left: 30px;
        transform: translate(-6px, 0);
      }
    }

    &:last-child {
      .list-item-footer__tip {
        left: auto;
        right: 0;
        transform: translate(0, 0);
      }

      .list-item-footer__tip::after {
        left: auto;
        right: 30px;
        transform: translate(6px, 0);
      }
    }
  }

  &__btns {
    display: flex;
  }

  &__tip {
    position: absolute;
    height: 32px;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    border-radius: 8px;
    padding: 0 8px;
    background-color: rgba($color: #000, $alpha: 0.7);
    color: #fff;
    font-size: 13px;
    white-space: nowrap;
    z-index: 9;

    >span {
      margin: 0 8px 0 4px;
      flex: 1;
    }
  }

  &__tip::after {
    position: absolute;
    content: '';
    top: 32px;
    left: 50%;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-top: 6px solid rgba($color: #000, $alpha: 0.7);
  }

  &__btn {
    min-width: 80px;
    font-size: 14px;
  }

  &__extend {
    position: relative;
    font-size: 0;

    &-popup {
      width: 128px;
      position: absolute;
      left: 0;
      top: 30px;
      background: #fff;
      box-sizing: border-box;
      z-index: 999;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(50, 50, 51, 0.12);

      &::before {
        content: '';
        display: block;
        position: absolute;
        top: -14px;
        left: 8px;
        width: 0;
        height: 0;
        border: 8px solid transparent;
        border-bottom-color: #fff;
      }
    }

    &-popup_last {
      top: auto;
      bottom: 30px;

      &::before {
        top: auto;
        bottom: -14px;
        transform: rotate(180deg);
      }
    }

    &-btn {
      height: 43px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      &:not(:first-child)::after {
        position: absolute;
        box-sizing: border-box;
        content: ' ';
        pointer-events: none;
        top: 0;
        left: 16px;
        right: 16px;
        border-bottom: 1px solid #ebedf0;
        transform: scaleY(0.5);
      }
    }
  }
}