import WscComponent from 'pages/common/wsc-component/index';
const app = getApp();

WscComponent({
  properties: {
    keyword: String,
    kdtId: String,
    navigateType: String
  },

  methods: {
    handlePlaceholderClick() {
      const { kdtId, navigateType } = this.data;

      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'search_order_click', // 事件标识
          en: '订单搜索入口点击', // 事件名称
          pt: 'ol', // 页面类型
          params: {},
        });
      
      const queryStr = kdtId ? `?kdt_id=${kdtId}` : '';
      const url = `/packages/trade/order-search/index${queryStr}`;
      if (navigateType === 'redirectTo') {
        wx.redirectTo({
          url
        });
      } else {
        wx.navigateTo({
          url
        });
      }
    }
  },
});
