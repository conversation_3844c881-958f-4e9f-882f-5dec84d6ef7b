@import "shared/common/css/helper/index.wxss";

.order-container{
  transform: var(--order-container);
  position: relative;
}
.page-phone {
  position: fixed;
  left: 0;
  right: 0;
  top: var(--nav-height);
  z-index: 10;
}
.page-search {
  position: fixed;
  left: 0;
  right: 0;
  top: calc(46px + var(--nav-height));
  z-index: 10;
}
.page-header {
  position: fixed;
  left: 0;
  right: 0;
  top: calc(54px + var(--nav-height));
  z-index: 10;
}
.page-retail-header {
  position: fixed;
  display: flex;
  left: 0;
  right: 0;
  top: var(--nav-height);
  background: #fff;
  z-index: 10;
}
.retail-opt-con {
  display: flex;
  padding-left: 90px;
  padding-right: 8px;
  align-items: center;
}

.retail-opt {
  display: flex;
  padding: 0 10px;
  height: 100%;
  align-items: center;
}

.page-header-tab {
  position: fixed;
  left: 0;
  right: 0;
  top: calc(100px + var(--nav-height));
  z-index: 10;
}

.list-wrapper {
  padding-top: 44px;
}

.list-placeholder {
  height: 10px;
}

.list-wrapper--bind {
  padding-top: 90px;
}

.bind__container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 46px;
  background-color: #FFF;
}

.bind__tips {
  padding-left: 15px;
  color: #333;
  text-align: center;
  font-size: 12px;
}

.bind__button {
  margin-right: 15px;
}

.btn-login {
  display: block;
  padding: 0;
  border-radius: 0;
  background-color: transparent;
  text-align: center;
  width: 50px;
  height: 30px;
  line-height: 30px;
  color: #fff;
  font-size: 12px;
}

.btn-login::after {
  border: none;
}

.skeleton-image {
  width: 100%;
}
