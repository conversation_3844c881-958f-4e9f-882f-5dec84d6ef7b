import each from '@youzan/weapp-utils/lib/each';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

export default {
  parseSafeBasicData(data, safe_no) {
    var parsedData = {};
    var originData = {};

    // 处理方式
    var methods = [{ text: '请选择处理方式', code: 0 }];
    each(data.type, (text, code) => {
      methods.push({ text, code });
    });
    parsedData.methods = methods;

    // 支付订单信息
    parsedData.pay_time = data.pay_time;
    parsedData.goods_title = data.goods_title;

    // 如果已经有维权单号了，说明是修改维权，就要解析之前的维权数据
    if (safe_no) {
      var safeInfo = data.safe_info || {};
      parsedData.moneyStr = safeInfo.refund_fee;
      parsedData.phone = safeInfo.phone || '';
      parsedData.message = safeInfo.remark || '';
      parsedData.method = safeInfo.safe_type || 0;
      parsedData.express = 0;
      parsedData.reason = safeInfo.safe_reason || 0;
      parsedData.imgs = (safeInfo.ext_info || []).map(src => ({
        uploading: false,
        src,
        key: generateKey(),
        srcPreview: cdnImage(src, '!200x200.jpg')
      }));
    }

    // real_pay
    parsedData.real_pay = data.real_pay;

    // is_full_refund 是否一定要全额退款
    parsedData.is_full_refund = data.is_full_refund;

    // reason_relation 退款原因的恶心的数据处理
    originData.reason_relation = parseReasonRelationData(data.reason_relation);

    parsedData.originData = originData;

    return parsedData;
  },

  getReasons(reasonRelationData, method = 0, express = 0) {
    var result = [{ code: 0, text: '请选择退款原因' }];

    // 处理方式还没有选择的时候
    if (!method) {
      return result;
    }

    if (method == 1) {
      result = result.concat(reasonRelationData.refund[express] || []);
    } else {
      result = result.concat(reasonRelationData.refund_and_return);
    }

    return result;
  }
};

function generateKey() {
  return makeRandomString(8) + (new Date()).getTime();
}

function parseReasonRelationData(data) {
  var parsedData = {};
  parsedData.refund = data.refund.map((reasons) => {
    var parsedReasons = [];
    each(reasons, (text, code) => {
      parsedReasons.push({ code, text });
    });
    return parsedReasons;
  });

  var refundReturnData = [];
  each(data.refund_and_return, (text, code) => {
    refundReturnData.push({ code, text });
  });
  parsedData.refund_and_return = refundReturnData;

  return parsedData;
}
