var app = getApp();

export default {
  getApplyBasicData(data, success, fail) {
    app.carmen({
      api: 'kdt.trade.safe.detail/1.0.0/getSafeApplyBasicInfo',
      data,
      success: (res) => {
        success && success(res);
      },
      fail: (res) => {
        fail && fail(res.msg);
      }
    });
  },

  submitFormId(requestData, success, fail) {
    app.carmen({
      api: 'youzan.trade.weapp.formid/3.0.0/update',
      data: requestData,
      method: 'GET',
      success: (res) => {
        success && success(res);
      },
      fail: (res) => {
        fail && fail(res.msg, res);
      }
    });
  },

  submit(data, success, fail) {
    var requestData = {
      safe_no: data.safe_no,
      order_no: data.order_no,
      phone: data.phone,
      photos: data.photos,
      refund_fee: data.money,
      remark: data.message,
      safe_reason: data.reason,
      safe_type: data.method
    };

    if (data.item_id) {
      requestData.item_id = data.item_id;
    }

    app.carmen({
      api: 'kdt.trade.safe.creator/1.0.0/safeCreateOrUpdate',
      data: requestData,
      method: 'POST',
      success: (res) => {
        success && success(res);
      },
      fail: (res) => {
        fail && fail(res.msg, res);
      }
    });
  }
}
