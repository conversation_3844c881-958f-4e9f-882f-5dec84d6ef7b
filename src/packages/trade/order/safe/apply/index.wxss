@import "helpers/index.wxss";
@import "../wxss/multi-image-upload.wxss";
@import "themes/zan-btn.scss";


.apply-detail {
  padding: 10px 15px;
  font-size: 14px;
}

.apply-detail__row {
  line-height: 24px;
}

.apply-detail__content {
  padding-left: 10px;
}

.apply-form__title--top {
  align-self: flex-start;
}

.apply-form__message {
  display: flex;
  align-items: center;
}

.apply-form__msg {
  width: auto;
  flex: 1;
}

.apply-form__img-contain {
  position: relative;
  float: left;
  margin-top: 10px;
  margin-right: 10px;
  width: 60px;
  height: 60px;
}

.apply-form__img-add {
  background: transparent url("https://img01.yzcdn.cn/public_files/2016/12/20/2937218b768d94864cfbc16900bc237c.png") center no-repeat;
  background-size: 60px 60px;
}

.apply-form__img {
  width: 60px;
  height: 60px;
}

.apply-form__clear {
  position: absolute;
  right: -18px;
  top: -18px;
  padding: 10px;
}
