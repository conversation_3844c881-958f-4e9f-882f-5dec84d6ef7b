import WscPage from 'pages/common/wsc-page/index';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import isPhone from '@youzan/weapp-utils/lib/validators/is-phone';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import moneyHelper from '@youzan/weapp-utils/lib/money';
import multiUpload from 'utils/multi-uploader';
import Toast from '@vant/weapp/dist/toast/toast';
import request from './utils/request';
import format from './utils/format';

const app = getApp();

const MAX_PICTURES = 5;

WscPage({

  data: {
    fetching: true,
    // 维权单号 只有在编辑的时候才有
    safe_no: '',
    // 订单号
    order_no: '',
    // 订单内维权的商品item id，这个不是商品id
    item_id: '',
    // 支付时间
    pay_time: '',
    // 商品标题
    goods_title: '',
    // 上传的图片信息
    imgs: [],
    // 备注信息
    message: '',
    // 手机号码
    phone: '',
    // 退款金额（分）
    money: 0,
    // 退款金额格式化数据（元）
    moneyStr: '',
    // 选择的处理方式
    method: 0,
    methodIndex: 0,
    // 货物状态
    express: 0,
    expressIndex: 0,
    // 选择的退款原因
    reason: 0,
    reasonIndex: 0,
    // 最多选择的图片
    MAX_PICTURES,
    // 可选的处理方式
    methods: [],
    // 货物状态可选列表
    expressList: [{ code: 0, text: '未收到货' }, { code: 1, text: '已收到货' }],
    // 可选的退款原因
    reasons: [],
    // 可退款的金额 买家实付金额
    real_pay: '0.00',
    // 是否必须全额退款
    is_full_refund: false,
    // 一些后期需要使用的原始数据，比如select切换的时候
    originData: {}
  },

  onLoad(query) {
    var dbid = query.dbid || '';
    var originData = app.db.get(dbid);
    var initData = {};
    if (dbid) {
      initData = {
        safe_no: originData.safe_no || '',
        order_no: originData.order_no || '',
        item_id: originData.item_id || ''
      };
    } else {
      initData = {
        safe_no: query.safe_no || '',
        order_no: query.order_no || '',
        item_id: query.item_id || ''
      };
    }
    this.setYZData(initData);

    // loading展示
    wx.showToast({
      title: '数据加载中',
      icon: 'loading',
      // 持续时间最长为10s
      duration: 10000
    });

    var requestData = {
      order_no: this.data.order_no
    };
    if (this.data.safe_no) {
      requestData.safe_no = this.data.safe_no;
    } else {
      requestData.item_id = this.data.item_id;
    }
    request.getApplyBasicData(requestData, (res) => {
      wx.hideToast();
      this.setYZData(Object.assign({
        fetching: false
      }, format.parseSafeBasicData(res, this.data.safe_no)));

      // 如果是修改维权的话
      if (this.data.safe_no) {
        this.onMoneyChange({
          detail: {
            value: this.data.moneyStr
          }
        });

        // 辅助选择下picker
        this.generateSelect();
      } else {
        this.rebuildReasonSelect();
      }

      // 如果是全额退款的话，那么支付金额就作为他的退款金额
      this.onMoneyChange({
        detail: {
          value: this.data.real_pay
        }
      });
    });
  },

  // 删除已经选择的图片
  onImageDelete(e) {
    var data = e.currentTarget.dataset || {};
    var key = data.key || '';
    var imgs = this.data.imgs || [];

    var imgIndex = imgs.findIndex((imgData) => {
      return imgData.key == key;
    });

    if (imgIndex < 0) {
      return;
    }

    imgs.splice(imgIndex, 1);

    this.setYZData({ imgs });
  },

  // 点击添加图片的时候
  onImageAdd() {
    var picsLeftNum = MAX_PICTURES - this.data.imgs.length;
    picsLeftNum = picsLeftNum > 0 ? picsLeftNum : 0;

    wx.chooseImage({
      count: picsLeftNum,
      sizeType: ['compressed'],
      success: (res) => {
        var tempFilePaths = res.tempFilePaths || [];
        var imgs = this.data.imgs || [];
        var newImgs = [];

        if (imgs.length + tempFilePaths.length > MAX_PICTURES) {
          Toast(`最多一共只能上传${MAX_PICTURES}张图片`);
          return;
        }

        tempFilePaths.forEach((filePath) => {
          newImgs.push({
            uploading: true,
            src: filePath,
            srcPreview: filePath,
            key: this.generateKey()
          });
        });

        this.uploadImg(newImgs);

        imgs = imgs.concat(newImgs);
        this.setYZData({
          imgs
        });
      }
    });
  },

  // 当处理方式选择修改的时候
  bindMethodChange(e) {
    var methodIndex = e.detail.value;
    var methodData = this.data.methods[methodIndex] || {};
    var method = methodData.code || 0;
    this.setYZData({ method, methodIndex });

    this.rebuildReasonSelect();
  },

  // 货物状态改变时
  bindExpressChange(e) {
    var expressIndex = e.detail.value;
    var expressData = this.data.expressList[expressIndex] || {};
    var express = expressData.code || 0;
    this.setYZData({ express, expressIndex });

    this.rebuildReasonSelect();
  },

  // 当退款原因选择改变的时候
  bindReasonChange(e) {
    var reasonIndex = e.detail.value;
    var reasonData = this.data.reasons[reasonIndex] || {};
    var reason = reasonData.code || 0;
    this.setYZData({ reason, reasonIndex });
  },

  // 备注信息修改的时候
  onMessageChange(e) {
    var message = e.detail.value;
    this.setYZData({ message });
  },

  // 电话修改的时候
  onPhoneChange(e) {
    var phone = e.detail.value;
    this.setYZData({ phone });
  },

  // 退款金额修改的时候
  onMoneyChange(e) {
    var moneyStr = e.detail.value;
    var money = moneyHelper(moneyStr).toCent();
    moneyStr = moneyHelper(money).toYuan();
    this.setYZData({
      money,
      moneyStr
    });
  },

  onSubmitClick(event) {
    let formId = event.detail.formId || '';
    request.submitFormId({ form_id: formId, order_no: this.data.order_no });

    var requestData = this.getPostData();
    if (!this.validData(requestData)) {
      return;
    }

    // 防止重复提交
    if (this.applying) {
      return;
    }
    this.applying = true;

    request.submit(requestData, (res) => {
      this.applying = false;
      let safeNo = res.safe_no;

      // 创建时，返回数据可能是对象？需要从里面取出 safe_no
      if (typeof safeNo === 'object') {
        safeNo = safeNo.data;
      }

      const dbid = app.db.set({
        order_no: this.data.order_no,
        safe_no: safeNo
      });
      wx.redirectTo({
        url: '/packages/trade/order/safe/info/index?dbid=' + dbid
      });
    }, (msg) => {
      this.applying = false;

      Toast(msg || '网络卡了下，稍候再试试');
    });
  },

  getPostData() {
    var data = this.data;
    var requestData = {
      safe_no: data.safe_no,
      order_no: data.order_no,
      method: data.method || 0,
      reason: data.reason || 0,
      money: data.money,
      phone: data.phone,
      message: data.message,
      photos: JSON.stringify(data.imgs.map(imgData => imgData.src))
    };

    // 当没有维权id的时候，才需要把item_id塞进去
    if (!data.safe_no) {
      requestData.item_id = data.item_id;
    }

    return requestData;
  },

  validData(data) {
    // 非空判断
    if (!data.method) {
      Toast('请选择处理方式');
      return;
    }

    if (!data.reason) {
      Toast('请选择退款原因');
      return;
    }

    if (!data.money) {
      Toast('请填写退款金额');
      return;
    }

    if (!data.phone) {
      Toast('请填写手机号码');
      return;
    }

    // 数据合法性校验
    if (!isPhone(data.phone)) {
      Toast('请填写正确的手机号码');
      return;
    }

    // 备注信息过长校验
    if (data.message.length > 200) {
      Toast('备注信息不要超过200字');
      return;
    }

    // @@ 校验退款金额
    // if ()

    var hasUploading = this.data.imgs.some(imgData => imgData.uploading);
    if (hasUploading) {
      Toast('部分图片正在上传中，请稍候');
      return;
    }

    return true;
  },

  uploadImg(imgs = []) {
    multiUpload(imgs, {
      afterUploadSuccess: (src, imgData) => {
        var imgs = this.data.imgs;
        var imgIndex = imgs.findIndex((originImgData) => {
          return imgData.key == originImgData.key;
        });

        if (imgIndex < 0) {
          return;
        }

        var originImgData = imgs[imgIndex];
        originImgData.src = src;
        originImgData.srcPreview = cdnImage(src, '!200x200.jpg');
        originImgData.uploading = false;

        this.setYZData({ imgs });
      },
      afterUploadFail: (imgData) => {
        var imgs = this.data.imgs;
        var imgIndex = imgs.findIndex((originImgData) => {
          return imgData.key == originImgData.key;
        });

        if (imgIndex < 0) {
          return;
        }

        imgs.splice(imgIndex, 1);

        this.setYZData({ imgs });

        Toast('部分图片上传出错，已经自动剔除');
      }
    });
  },

  // 生成唯一的图片key
  generateKey() {
    return makeRandomString(8) + (new Date()).getTime();
  },

  generateSelect() {
    var methodIndex = this.data.methods.findIndex((methodData) => {
      return methodData.code == this.data.method;
    }) || 0;

    var expressIndex = this.data.expressList.findIndex((expressData) => {
      return expressData.code == this.data.express;
    }) || 0;

    var reasons = this.generateReasons();
    var reasonIndex = reasons.findIndex((reasonData) => {
      return reasonData.code == this.data.reason || 0;
    });

    if (reasonIndex === -1) {
      reasonIndex = 0;
    }
    this.setYZData({
      methodIndex, expressIndex, reasons, reasonIndex
    });
  },

  generateReasons() {
    var data = this.data;
    var reasonRelationData = data.originData.reason_relation;
    var reasons = format.getReasons(reasonRelationData, data.method, data.express) || [];
    return reasons;
  },

  rebuildReasonSelect() {
    var reasons = this.generateReasons();
    var reasonIndex = 0;
    var reason = 0;

    this.setYZData({ reasons, reasonIndex, reason });
  }

});
