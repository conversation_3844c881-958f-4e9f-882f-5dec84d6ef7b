<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <block wx:if="{{ !fetching }}">
    <!-- 维权信息简介 -->
    <view class="apply-detail zan-panel zan-panel--without-margin-top">
      <view class="apply-detail__row">
        <text class="van-pull-left">商品名称:</text>
        <view class="apply-detail__content van-right van-ellipsis">{{ goods_title }}</view>
      </view>

      <view class="apply-detail__row">
        <text class="van-pull-left">付款金额:</text>
        <view class="apply-detail__content van-right van-c-red">￥ {{ real_pay }}</view>
      </view>

      <view class="apply-detail__row">
        <text class="van-pull-left">订单编号:</text>
        <view class="apply-detail__content van-right">{{ order_no }}</view>
      </view>

      <view class="apply-detail__row">
        <text class="van-pull-left">交易时间:</text>
        <view class="apply-detail__content van-right">{{ pay_time }}</view>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="zan-panel">
      <view class="zan-cell zan-field">
        <view class="zan-field__title">
          处理方式
        </view>
        <picker class="zan-field__input zan-cell__bd" mode="selector" value="{{ methodIndex }}" range="{{ methods }}" range-key="text" bindchange="bindMethodChange">
          <view class="">{{ methods[methodIndex].text }}</view>
        </picker>
        <view class="van-arrow"></view>
      </view>

      <!-- 只有仅退款需要知道货物状态，退货退款的默认为已收到货了 -->
      <view wx:if="{{ method == 1 }}" class="zan-cell zan-field">
        <view class="zan-field__title">
          货物状态
        </view>
        <picker class="zan-field__input zan-cell__bd" mode="selector" value="{{ expressIndex }}" range="{{ expressList }}" range-key="text" bindchange="bindExpressChange">
          <view class="">{{ expressList[expressIndex].text }}</view>
        </picker>
        <view class="van-arrow"></view>
      </view>

      <view class="zan-cell zan-field">
        <view class="zan-field__title">
          退款原因
        </view>
        <picker class="zan-field__input zan-cell__bd" mode="selector" value="{{ reasonIndex }}" range="{{ reasons }}" range-key="text" bindchange="bindReasonChange">
          <view class="">{{ reasons[reasonIndex].text || '请选择退款原因' }}</view>
        </picker>
        <view class="van-arrow"></view>
      </view>

      <view class="zan-cell zan-field">
        <view class="zan-field__title">
          退款金额
        </view>
        <input
          type="digit"
          maxlength="20"
          class="zan-field__input zan-cell__bd"
          placeholder="最多可退{{ real_pay }}元。退款将返还实际支付金额，支付优惠金额将从退款金额中扣除。"
          bindblur="onMoneyChange"
          value="{{ moneyStr }}"
          disabled="{{ is_full_refund ? 'disabled' : '' }}"
        />
      </view>

      <view class="zan-cell zan-field">
        <view class="zan-field__title">
          手机号码
        </view>
        <input class="zan-field__input zan-cell__bd" type="number" maxlength="20" placeholder="便于卖家联系你" bindblur="onPhoneChange" value="{{ phone }}" />
      </view>

      <view class="zan-cell zan-field">
        <view class="zan-field__title">
          备注信息
        </view>
        <textarea class="zan-cell__bd zan-field__input" auto-height maxlength="200" placeholder="最多可填写200字" bindblur="onMessageChange" value="{{ message }}"/>
      </view>

      <view class="zan-cell zan-field zan-cell--last-child">
        <view class="multi-upload__title zan-field__title">
          图片举证
        </view>
        <view class="zan-cell__bd">
          <view wx:if="{{ imgs.length == 0 }}" class="van-c-gray-dark">可上传{{ MAX_PICTURES }}张图片</view>
          <view wx:else class="van-c-gray-dark">还能上传{{ MAX_PICTURES - imgs.length }}张图片</view>
          <view>
            <view
              wx:for="{{ imgs }}" wx:key="key" wx:for-item="imgData"
              class="multi-upload__img-contain {{ imgData.uploading ? 'multi-upload__img-contain--loading' : '' }}"
            >
              <image class="multi-upload__img" src="{{ imgData.srcPreview }}" mode="aspectFit"></image>
              <icon
                class="multi-upload__clear"
                data-key="{{ imgData.key }}"
                type="clear" size="16" color="black"
                catchtap="onImageDelete"
              />
            </view>

            <view wx:if="{{ imgs.length < MAX_PICTURES }}" class="multi-upload__img-contain multi-upload__img-add" catchtap="onImageAdd">
            </view>
          </view>
        </view>
      </view>

    </view>

    <!-- 提交按钮 -->
    <form bindsubmit="onSubmitClick" report-submit>
      <view class="zan-btns">
        <button class="zan-btn zan-btn--danger" formType="submit">提交</button>
      </view>
    </form>
  </block>
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
