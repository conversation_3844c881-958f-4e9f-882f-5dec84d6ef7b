import WscPage from 'pages/common/wsc-page/index';
import each from '@youzan/weapp-utils/lib/each';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import multiUpload from 'utils/multi-uploader';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import isPhone from '@youzan/weapp-utils/lib/validators/is-phone';
import Toast from '@vant/weapp/dist/toast/toast';

const app = getApp();
const MAX_PICTURES = 5;

WscPage({

  data: {
    safe_no: '',
    order_no: '',
    express: [{ code: 0, text: '物流公司获取中' }],
    expressIndex: 0,
    express_no: '',
    phone: '',
    message: '',
    imgs: [],
    MAX_PICTURES
  },

  onLoad(query) {
    var dbid = query.dbid;
    var data = app.db.get(dbid) || {};

    this.setYZData({
      safe_no: data.safe_no || '',
      order_no: data.order_no || ''
    });

    // 获取可用物流公司列表
    this.fetchExpressCompany();
  },

  bindExpressChange(e) {
    this.setYZData({
      expressIndex: e.detail.value || 0
    });
  },

  onExpressNoChange(e) {
    this.setYZData({
      express_no: e.detail.value || ''
    });
  },

  onPhoneChange(e) {
    this.setYZData({
      phone: e.detail.value || ''
    });
  },

  onMessageChange(e) {
    this.setYZData({
      message: e.detail.value || ''
    });
  },

  onSubmitClick() {
    var requestData = this.getFormData();

    if (!this.validateData(requestData)) {
      return;
    }

    if (this.submiting) {
      return;
    }

    this.submiting = true;
    app.carmen({
      api: 'kdt.trade.safe.modify/1.0.0/postExp',
      method: 'POST',
      data: requestData,
      success: () => {
        this.submiting = false;
        var dbid = app.db.set({
          safe_no: this.data.safe_no,
          order_no: this.data.order_no
        });

        wx.redirectTo({
          url: '/packages/trade/order/safe/info/index?dbid=' + dbid
        });
      },
      fail: () => {
        this.submiting = false;
      }
    });
  },

  onImageAdd() {
    var data = this.data;
    var picsLeftNum = data.MAX_PICTURES - data.imgs.length;
    picsLeftNum = picsLeftNum > 0 ? picsLeftNum : 0;

    wx.chooseImage({
      count: picsLeftNum,
      sizeType: ['compressed'],
      success: (res) => {
        var tempFilePaths = res.tempFilePaths || [];
        var imgs = this.data.imgs || [];
        var newImgs = [];

        if (imgs.length + tempFilePaths.length > data.MAX_PICTURES) {
          Toast(`最多一共只能上传${data.MAX_PICTURES}张图片`);
          return;
        }

        tempFilePaths.forEach((filePath) => {
          newImgs.push({
            uploading: true,
            src: filePath,
            srcPreview: filePath,
            key: this.generateKey()
          });
        });

        this.uploadMessageImgs(newImgs);

        imgs = imgs.concat(newImgs);
        this.setYZData({ imgs });
      }
    });
  },

  onMessageImageDelete(e) {
    var data = e.currentTarget.dataset || {};
    var key = data.key || '';
    var imgs = this.data.imgs || [];

    var imgIndex = imgs.findIndex((imgData) => {
      return imgData.key == key;
    });

    if (imgIndex < 0) {
      return;
    }

    imgs.splice(imgIndex, 1);

    this.setYZData({ imgs });
  },

  uploadMessageImgs(imgs = []) {
    multiUpload(imgs, {
      afterUploadSuccess: (src, imgData) => {
        var imgs = this.data.imgs;
        var imgIndex = imgs.findIndex((originImgData) => {
          return imgData.key == originImgData.key;
        });

        if (imgIndex < 0) {
          return;
        }

        var originImgData = imgs[imgIndex];
        originImgData.src = src;
        originImgData.srcPreview = cdnImage(src, '!100x100.jpg');
        originImgData.uploading = false;

        this.setYZData({ imgs });
      },
      afterUploadFail: (imgData) => {
        var imgs = this.data.imgs;
        var imgIndex = imgs.findIndex((originImgData) => {
          return imgData.key == originImgData.key;
        });

        if (imgIndex < 0) {
          return;
        }

        imgs.splice(imgIndex, 1);
        this.setYZData({ imgs });
        Toast('部分图片上传出错，已经自动剔除');
      }
    });
  },

  fetchExpressCompany() {
    app.carmen({
      api: 'kdt.logistics.express.map/1.0.0/get',
      success: (res) => {
        if (res.code != 200) {
          Toast(res.message || '获取物流公司列表失败，请稍候再试');
          return;
        }

        var express = [{ code: 0, text: '请选择物流公司' }];
        each(res.data, (text, code) => {
          if (code == '41') {
            return;
          }
          express.push({ text, code });
        });

        // 其它物流方式要放在最后
        if (res.data[41]) {
          express.push({ code: '41', text: res.data[41] });
        }

        this.setYZData({ express });
      },
      fail: (res) => {
        Toast(res.msg || '网络抖了一下，请稍候再试');
      }
    });
  },

  // 生成唯一的图片key
  generateKey() {
    return makeRandomString(8) + (new Date()).getTime();
  },

  // 表单数据校验
  validateData(data) {
    if (!data.express_id) {
      Toast('请选择物流公司');
      return;
    }

    if (!data.express_no) {
      Toast('请填写物流单号');
      return;
    }

    if (!data.phone) {
      Toast('请填写手机号码');
      return;
    }

    if (!isPhone(data.phone)) {
      Toast('请填写正确的手机号码');
      return;
    }

    var hasUploading = this.data.imgs.some((imgData) => imgData.uploading);
    if (hasUploading) {
      Toast('部分图片正在上传中，请稍候');
      return;
    }

    return true;
  },

  getFormData() {
    var requestData = {
      safe_no: this.data.safe_no,
      order_no: this.data.order_no,
      express_no: this.data.express_no,
      phone: this.data.phone,
      remark: this.data.message,
    };

    var imgs = this.data.imgs.map(imgData => imgData.src);
    requestData.photos = JSON.stringify(imgs);

    var selectedExpress = this.data.express[this.data.expressIndex] || {};
    var express = selectedExpress.code || 0;
    requestData.express_id = express;

    return requestData;
  }

});
