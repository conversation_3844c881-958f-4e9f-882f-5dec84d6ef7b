<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view class="zan-panel zan-panel--without-margin-top">
    <view class="zan-cell zan-field">
      <view class="zan-field__title">
        物流公司
      </view>
      <picker class="zan-field__input zan-cell__bd" mode="selector" value="{{ expressIndex }}" range="{{ express }}" range-key="text" bindchange="bindExpressChange">
        <view class="">{{ express[expressIndex].text }}</view>
      </picker>
      <view class="van-arrow"></view>
    </view>

    <view class="zan-cell zan-field">
      <view class="zan-field__title">
        物流单号
      </view>
      <input
        class="zan-field__input zan-cell__bd"
        type="text"
        placeholder="请输入物流单号"
        bindblur="onExpressNoChange"
        value="{{ express_no }}"
      />
    </view>

    <view class="zan-cell zan-field">
      <view class="zan-field__title">
        手机号码
      </view>
      <input
        class="zan-field__input zan-cell__bd"
        type="number"
        maxlength="20"
        placeholder="填写手机号便于卖家联系你"
        bindblur="onPhoneChange"
        value="{{ phone }}"
      />
    </view>

    <view class="zan-cell zan-field">
      <view class="zan-field__title">
        备注信息
      </view>
      <textarea class="zan-cell__bd zan-field__input" auto-height maxlength="200" placeholder="最多可填写200字" bindblur="onMessageChange" value="{{ message }}"/>
    </view>

    <view class="zan-cell zan-field zan-cell--last-child">
      <view class="multi-upload__title zan-field__title">
        图片举证
      </view>
      <view class="zan-cell__bd">
        <view wx:if="{{ imgs.length == 0 }}" class="van-c-gray-dark">可上传{{ MAX_PICTURES }}张图片</view>
        <view wx:else class="van-c-gray-dark">还能上传{{ MAX_PICTURES - imgs.length }}张图片</view>
        <view>
          <view
            wx:for="{{ imgs }}" wx:key="key" wx:for-item="imgData"
            class="multi-upload__img-contain {{ imgData.uploading ? 'multi-upload__img-contain--loading' : '' }}"
          >
            <image class="multi-upload__img" src="{{ imgData.srcPreview }}" mode="aspectFit"></image>
            <icon
              class="multi-upload__clear"
              data-key="{{ imgData.key }}"
              type="clear" size="16" color="black"
              catchtap="onImageDelete"
            />
          </view>

          <view wx:if="{{ imgs.length < MAX_PICTURES }}" class="multi-upload__img-contain multi-upload__img-add" catchtap="onImageAdd">
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="zan-btns">
    <button class="zan-btn zan-btn--danger" catchtap="onSubmitClick">提交</button>
  </view>
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
