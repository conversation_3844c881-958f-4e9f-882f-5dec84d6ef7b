import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import CountDown from 'utils/countdown';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import Tab from './components/zan-tab';
import request from './utils/request';
import format from './utils/format';
import messageDialogView from './js/message';
import youzanDialogView from './js/youzan';

const app = getApp();
const MAX_PICTURES = 5;

WscPage(Tab, messageDialogView, youzanDialogView, {

  data: {
    fetching: true,
    safe_no: '',
    order_no: '',
    item_id: '',
    // 当前维权状态
    type: 'safe-ing',
    // 倒计时时间
    timeout: 0,
    countdown: {},
    countdownStr: {},
    // goods 商品信息
    goods: {},
    // 退款进度 只有成功维权的才会有信息
    refund_process: {
      show: false
    },
    tab: {
      selectedId: '1',
      list: [{
        id: '1',
        title: '退款详情'
      }, {
        id: '2',
        title: '协商记录'
      }]
    },
    // 底部按钮
    btns: [],
    // 维权log
    log: [],

    // 我要留言的信息
    messageDialog: {
      show: false,
      message: '',
      imgs: []
    },

    // 申请有赞介入弹层的信息
    youzanDialog: {
      show: false,
      message: '',
      imgs: []
    },

    MAX_PICTURES
  },

  onPullDownRefresh() {
    this.fetchSafeData(true);
  },

  onLoad(query) {
    var data = app.db.get(query.dbid) || {};
    this.setYZData({
      safe_no: data.safe_no || query.safe_no || '',
      order_no: data.order_no || query.order_no || ''
    });
  },

  onShow() {
    this.fetchSafeData();
  },

  handleZanTabChange({ selectedId }) {
    this.setYZData({
      'tab.selectedId': selectedId
    });
  },

  showPicture(e) {
    var src = e.currentTarget.dataset.src;
    wx.previewImage({
      urls: [src]
    });
  },

  closeSafe() {
    wx.showModal({
      title: '提示',
      content: '确定要关闭维权么？',
      success: (res) => {
        if (res.confirm) {
          request.close({
            safe_no: this.data.safe_no,
            order_no: this.data.order_no
          }, () => this.fetchSafeData(), (res) => {
            Toast(res || '关闭维权失败');
          });
        }
      }
    });
  },

  backToOrder() {
    wx.navigateBack();
  },

  modifySafe() {
    var dbid = app.db.set({
      safe_no: this.data.safe_no,
      order_no: this.data.order_no
    });

    wx.redirectTo({
      url: '/packages/trade/order/safe/apply/index?dbid=' + dbid
    });
  },

  fetchSafeData(isRefresh) {
    // 刷新前先暂定倒计时
    this.countdown && this.countdown.stop();

    // loading展示
    wx.showToast({
      title: '数据加载中',
      icon: 'loading',
      // 持续时间最长为10s
      duration: 10000
    });

    request.getSafeDetail(this.data, (res) => {
      wx.hideToast();

      if (isRefresh) {
        wx.stopPullDownRefresh();
      }

      var safeData = format.parseSafeData(res);
      this.setYZData(Object.assign({
        fetching: false
      }, safeData));

      if (this.data.type == 'agreed') {
        this.fetchRefundProcess();
      }

      this.startTimeout();
    }, (msg) => {
      wx.hideToast();

      if (isRefresh) {
        wx.stopPullDownRefresh();
      }
      Toast(msg || '网络出了点问题，请稍候再试');
    });
  },

  fetchRefundProcess() {
    request.getRefundProcess(this.data, (res) => {
      var refund_process = Object.assign({}, this.data.refund_process, format.parseRefundProcessData(res));

      this.setYZData({ refund_process });
    });
  },

  startTimeout() {
    if (!this.data.timeout) {
      return;
    }

    this.countdown = new CountDown(this.data.timeout, {
      onChange: (timeData, strData) => {
        this.setYZData({
          countdown: timeData,
          countdownStr: strData
        });
      },
      onEnd: () => {
        this.fetchSafeData();
      }
    });
  },

  // 生成唯一的图片key
  generateKey() {
    return makeRandomString(8) + (new Date()).getTime();
  },

  validateImages(imgs = []) {
    return imgs.every((imgData) => {
      return !imgData.uploading;
    });
  },

  onUnload() {
    // 离开页面前停止计时器
    this.countdown && this.countdown.stop();
  }
});
