var app = getApp();

export default {
  getSafeDetail(data = {}, success, fail) {
    app.carmen({
      api: 'kdt.trade.safe.detail/1.0.0/getBySafeNoWithValidate',
      data: {
        safe_no: data.safe_no,
        order_no: data.order_no
      },
      success: res => success && success(res),
      fail: res => fail && fail(res.msg)
    });
  },

  getRefundProcess(data, success) {
    app.carmen({
      api: 'kdt.trade.refund.detail/1.0.0/getProcess',
      data: {
        item_id: data.item_id,
        safe_no: data.safe_no,
        order_no: data.order_no
      },
      success: res => success && success(res)
    });
  },

  submitMessage(data, success, fail) {
    var dialogData = data.messageDialog;

    var imgs = dialogData.imgs.map(imgData => imgData.src);
    app.carmen({
      api: 'kdt.trade.safe.creator/1.0.0/createMessage',
      method: 'POST',
      data: {
        safe_no: data.safe_no,
        order_no: data.order_no,
        message: dialogData.message,
        ext_info: JSON.stringify(imgs)
      },
      success: (res) => {
        success && success(res);
      },
      fail: (res) => {
        fail && fail(res.msg);
      }
    });
  },

  submitYouzan(data, success, fail) {
    var dialogData = data.youzanDialog;

    var imgs = dialogData.imgs.map(imgData => imgData.src);
    app.carmen({
      api: 'kdt.trade.safe.modify/1.0.0/involve',
      method: 'POST',
      data: {
        safe_no: data.safe_no,
        order_no: data.order_no,
        explain: dialogData.message,
        ext_info: JSON.stringify(imgs)
      },
      success: (res) => {
        success && success(res);
      },
      fail: (res) => {
        fail && fail(res.msg);
      }
    });
  },

  close(data, success, fail) {
    app.carmen({
      api: 'kdt.trade.safe.modify/1.0.0/close',
      data: {
        safe_no: data.safe_no,
        order_no: data.order_no
      },
      success: res => success && success(res),
      fail: res => fail && fail(res.msg)
    });
  }
};
