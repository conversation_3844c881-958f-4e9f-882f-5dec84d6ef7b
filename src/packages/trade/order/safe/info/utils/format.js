import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import money from '@youzan/weapp-utils/lib/money';
import config from '../data';

export default {
  parseSafeData(data) {
    var parsedData = {};

    // 原始数据保留
    parsedData.safe = data;
    // 维权类型
    parsedData.type = config.stateMap[data.state] || 'safe-ing';

    // 维权底部按钮
    parsedData.btns = config.btnsMap[data.state] || [];

    // item_id
    parsedData.item_id = data.item_id;

    // 维权 log
    var logData = data.log;
    logData.forEach(logItem => {
      logItem.ext_info = logItem.ext_info || [];
      logItem.ext_info = logItem.ext_info.map((src) => ({
        src,
        srcPreview: cdnImage(src, '!200x200.jpg')
      }));
    });
    parsedData.log = logData;

    // goods 商品信息
    var goodsData = data.item || {};
    var skuStr = '';
    data.item.sku.forEach((skuItem) => {
      skuStr += `${skuItem.v} `;
    });
    goodsData.skuStr = skuStr;
    goodsData.payPriceStr = money(data.item.pay_price).toYuan();
    goodsData.imgPreview = cdnImage(goodsData.img_url, '!200x200.jpg');
    goodsData.orderStateStr = data.order_state.status_str;
    parsedData.goods = goodsData;
    // 超时时间
    parsedData.timeout = data.timeout * 1000 || 0;
    return parsedData;
  },

  parseRefundProcessData(data) {
    data = data[0] || {};
    var refund_process = {};
    refund_process.pay_method_str = data.pay_method_str || '';
    refund_process.add_time = data.add_time;

    var steps = [];
    let active = 0;
    steps.push({
      text: `卖家退款(${data.add_time})`
    });

    steps.push({
      text: `${data.pay_method_str}(${data.update_time})`
    });

    var step3 = {
      text: '确认到账'
    };
    active = 1;
    if (data.refund_state == 2) {
      step3.text = `确认到账(${data.update_time})`;
      active = 2;
    }
    steps.push(step3);

    refund_process.steps = steps;
    refund_process.active = active;

    return refund_process;
  }
};
