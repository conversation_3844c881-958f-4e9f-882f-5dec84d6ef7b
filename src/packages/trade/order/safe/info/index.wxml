<import src="./wxml/info.wxml"/>
<import src="./wxml/btns.wxml"/>

<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
	<block wx:if="{{ !fetching }}">
		<safe-top
			type="{{ type }}"
			safe="{{ safe }}"
			order-no="{{ order_no }}"
			refund-process="{{ refund_process }}"
			countdown="{{ countdown }}"
			countdown-str="{{ countdownStr }}"
		/>

		<template is="safe-info" data="{{ type, tab, safe, safe_no, log, goods }}"/>

		<template is="btns" data="{{ btns, messageDialog, youzanDialog, MAX_PICTURES, selectedId: tab.selectedId }}"/>

		<recommend-goods
			biz-name="refund"
		/>
	</block>
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
