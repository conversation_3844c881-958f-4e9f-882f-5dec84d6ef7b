import Toast from '@vant/weapp/dist/toast/toast';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import multiUpload from 'utils/multi-uploader';
import request from '../utils/request';

export default {
  showYouzanDialog() {
    this.setYZData({
      'youzanDialog.show': true
    });
  },

  hideYouzanDialog() {
    this.setYZData({
      'youzanDialog.show': false
    });
  },

  onYouzanDialogTextChange(e) {
    this.setYZData({
      'youzanDialog.message': e.detail.value
    });
  },

  submitYouzanDialog() {
    var dialogData = this.data.youzanDialog;

    if (!dialogData.message) {
      Toast('请填写维权理由');
      return;
    }

    if (!this.validateImages(dialogData.imgs)) {
      Toast('还有部分图片还没有上传完成，请稍候再试');
      return;
    }

    // 防止重复提交
    if (this.isSubmitingYouzan) {
      return;
    }
    this.isSubmitingYouzan = true;

    request.submitYouzan(this.data, () => {
      this.isSubmitingYouzan = false;

      this.setYZData({
        'youzanDialog.message': '',
        'youzanDialog.show': false,
        'youzanDialog.imgs': []
      });

      this.fetchSafeData();
    }, (msg) => {
      this.isSubmitingYouzan = false;

      Toast(msg || '网络抖了下，请稍候再试');
    });
  },

  chooseYouzanImages() {
    var data = this.data;
    var picsLeftNum = data.MAX_PICTURES - data.youzanDialog.imgs.length;
    picsLeftNum = picsLeftNum > 0 ? picsLeftNum : 0;

    wx.chooseImage({
      count: picsLeftNum,
      sizeType: ['compressed'],
      success: (res) => {
        var tempFilePaths = res.tempFilePaths || [];
        var imgs = this.data.youzanDialog.imgs || [];
        var newImgs = [];

        if (imgs.length + tempFilePaths.length > data.MAX_PICTURES) {
          Toast(`最多一共只能上传${data.MAX_PICTURES}张图片`);
          return;
        }

        tempFilePaths.forEach((filePath) => {
          newImgs.push({
            uploading: true,
            src: filePath,
            srcPreview: filePath,
            key: this.generateKey()
          });
        });

        this.uploadYouzanImgs(newImgs);

        imgs = imgs.concat(newImgs);
        this.setYZData({
          'youzanDialog.imgs': imgs
        });
      }
    });
  },

  onYouzanImageDelete(e) {
    var data = e.currentTarget.dataset || {};
    var key = data.key || '';
    var imgs = this.data.youzanDialog.imgs || [];

    var imgIndex = imgs.findIndex((imgData) => {
      return imgData.key == key;
    });

    if (imgIndex < 0) {
      return;
    }

    imgs.splice(imgIndex, 1);

    this.setYZData({
      'youzanDialog.imgs': imgs
    });
  },

  uploadYouzanImgs(imgs = []) {
    multiUpload(imgs, {
      afterUploadSuccess: (src, imgData) => {
        var imgs = this.data.youzanDialog.imgs;
        var imgIndex = imgs.findIndex((originImgData) => {
          return imgData.key == originImgData.key;
        });

        if (imgIndex < 0) {
          return;
        }

        var originImgData = imgs[imgIndex];
        originImgData.src = src;
        originImgData.srcPreview = cdnImage(src, '!100x100.jpg');
        originImgData.uploading = false;

        this.setYZData({
          'youzanDialog.imgs': imgs
        });
      },
      afterUploadFail: (imgData) => {
        var imgs = this.data.youzanDialog.imgs;
        var imgIndex = imgs.findIndex((originImgData) => {
          return imgData.key == originImgData.key;
        });

        if (imgIndex < 0) {
          return;
        }

        imgs.splice(imgIndex, 1);

        this.setYZData({
          'youzanDialog.imgs': imgs
        });

        Toast('部分图片上传出错，已经自动剔除');
      }
    });
  }
};
