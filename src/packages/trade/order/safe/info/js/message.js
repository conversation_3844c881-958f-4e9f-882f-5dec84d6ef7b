import Toast from '@vant/weapp/dist/toast/toast';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import multiUpload from 'utils/multi-uploader';
import request from '../utils/request';

export default {
  showMessageDialog() {
    this.setYZData({
      'messageDialog.show': true
    });
  },

  hideMessageDialog() {
    this.setYZData({
      'messageDialog.show': false
    });
  },

  onMessageDialogTextChange(e) {
    this.setYZData({
      'messageDialog.message': e.detail.value
    });
  },

  submitMessageDialog() {
    var dialogData = this.data.messageDialog;

    if (!dialogData.message) {
      Toast('请填写留言信息');
      return;
    }

    if (!this.validateImages(dialogData.imgs)) {
      Toast('还有部分图片还没有上传完成，请稍候再试');
      return;
    }

    // 防止重复提交
    if (this.isSubmitingMessage) {
      return;
    }
    this.isSubmitingMessage = true;

    request.submitMessage(this.data, (res) => {
      this.isSubmitingMessage = false;

      var messageDialog = this.data.messageDialog || {};
      messageDialog.message = '';
      messageDialog.imgs = [];
      messageDialog.show = false;

      var newLogItem = res;
      newLogItem.ext_info = newLogItem.ext_info.map(src => ({
        src,
        srcPreview: cdnImage(src, '!200x200.jpg')
      }));
      var logData = this.data.log || [];
      logData.unshift(newLogItem);

      this.setYZData({
        log: logData,
        messageDialog
      });
    }, (msg) => {
      this.isSubmitingMessage = false;

      Toast(msg || '网络抖了下，请稍候再试');
    });
  },

  chooseMessageImages() {
    var data = this.data;
    var picsLeftNum = data.MAX_PICTURES - data.messageDialog.imgs.length;
    picsLeftNum = picsLeftNum > 0 ? picsLeftNum : 0;

    wx.chooseImage({
      count: picsLeftNum,
      sizeType: ['compressed'],
      success: (res) => {
        var tempFilePaths = res.tempFilePaths || [];
        var imgs = this.data.messageDialog.imgs || [];
        var newImgs = [];

        if (imgs.length + tempFilePaths.length > data.MAX_PICTURES) {
          Toast(`最多一共只能上传${data.MAX_PICTURES}张图片`);
          return;
        }

        tempFilePaths.forEach((filePath) => {
          newImgs.push({
            uploading: true,
            src: filePath,
            srcPreview: filePath,
            key: this.generateKey()
          });
        });

        this.uploadMessageImgs(newImgs);

        imgs = imgs.concat(newImgs);
        this.setYZData({
          'messageDialog.imgs': imgs
        });
      }
    });
  },

  onMessageImageDelete(e) {
    var data = e.currentTarget.dataset || {};
    var key = data.key || '';
    var imgs = this.data.messageDialog.imgs || [];

    var imgIndex = imgs.findIndex((imgData) => {
      return imgData.key == key;
    });

    if (imgIndex < 0) {
      return;
    }

    imgs.splice(imgIndex, 1);

    this.setYZData({
      'messageDialog.imgs': imgs
    });
  },

  uploadMessageImgs(imgs = []) {
    multiUpload(imgs, {
      afterUploadSuccess: (src, imgData) => {
        var imgs = this.data.messageDialog.imgs;
        var imgIndex = imgs.findIndex((originImgData) => {
          return imgData.key == originImgData.key;
        });

        if (imgIndex < 0) {
          return;
        }

        var originImgData = imgs[imgIndex];
        originImgData.src = src;
        originImgData.srcPreview = cdnImage(src, '!100x100.jpg');
        originImgData.uploading = false;

        this.setYZData({
          'messageDialog.imgs': imgs
        });
      },
      afterUploadFail: (imgData) => {
        var imgs = this.data.messageDialog.imgs;
        var imgIndex = imgs.findIndex((originImgData) => {
          return imgData.key == originImgData.key;
        });

        if (imgIndex < 0) {
          return;
        }

        imgs.splice(imgIndex, 1);

        this.setYZData({
          'messageDialog.imgs': imgs
        });

        Toast('部分图片上传出错，已经自动剔除');
      }
    });
  }
};
