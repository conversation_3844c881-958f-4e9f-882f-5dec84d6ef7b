.zan-chat {
  position: relative;
  margin: 10px 20px;
  padding: 0 10px;
  line-height: 1.4;
  border-radius: 4px;
  font-size: 14px;
}

.zan-chat__detail {
  position: relative;
  z-index: 2;
}

.zan-chat__arrow {
  position: absolute;
  width: 10px;
  height: 10px;
  z-index: 1;
  top: 15px;
}

.zan-chat__hd {
  padding: 12px 0;
  border-bottom: 1rpx solid #e5e5e5;
}

.zan-chat__hd--no-border {
  border-bottom: 0px none;
}

.zan-chat__bd {
  padding: 10px 0;
}

.zan-chat__row {
  padding: 3px 0;
  display: flex;
  word-break: break-all;
  white-space: normal;
}

.zan-chat__label {
  min-width: 70px;
  white-space: nowrap;
}

.zan-chat__content {
  flex: 1;
}

.zan-chat--dark {
  background-color: #8E9CB6;
  color: #fff;
}

.zan-chat--dark .zan-chat__arrow {
  background-color: #8E9CB6;
}

.zan-chat--light {
  background-color: #fff;
  color: #333;
  border: 1px solid #e5e5e5;
}

.zan-chat--light .zan-chat__arrow {
  background-color: #fff;
  box-shadow: 1px -1px #e5e5e5;
}

.zan-chat--left .zan-chat__arrow {
  left: 0;
  transform: translateX(-50%) rotate(45deg) skew(10deg, 10deg);
  transform-origin: center center;
}

.zan-chat--right .zan-chat__arrow {
  right: 0;
  transform: translateX(50%) rotate(45deg) skew(10deg, 10deg);
  transform-origin: center center;
}
