<template name="btns">
  <view class="zan-btns">
    <block wx:for="{{ btns }}" wx:for-item="btnType" wx:key="unique">
      <button wx:if="{{ btnType == 'modify' }}" class="zan-btn zan-btn--danger" catchtap="modifySafe">修改退款申请</button>
      <button wx:if="{{ btnType == 'close' }}" class="zan-btn" catchtap="closeSafe">撤销退款申请</button>
      <button wx:if="{{ btnType == 'youzan' }}" class="zan-btn" catchtap="showYouzanDialog">申请有赞客服介入</button>
      <button wx:if="{{ btnType == 'order' }}" class="zan-btn" catchtap="backToOrder">查看订单详情</button>
    </block>
    <button wx:if="{{ selectedId == 2 }}" class="zan-btn" catchtap="showMessageDialog">我要留言</button>
  </view>

  <template is="message-dialog" data="{{ messageDialog, MAX_PICTURES }}"></template>
  <template is="youzan-apply-dialog" data="{{ youzanDialog, MAX_PICTURES }}"></template>
</template>

<template name="message-dialog">
  <view class="order-dialog zan-popup zan-popup--bottom {{ messageDialog.show ? 'zan-popup--show' : '' }}">
    <view class="zan-popup__mask" catchtap="hideMessageDialog"></view>
    <view class="zan-popup__container">
      <view class="order-dialog__header zan-popup__header">
        <text>我要留言</text>
      </view>
      <view class="order-dialog__content">
        <view class="zan-cell zan-field">
          <view class="zan-field__title">
            留言信息
          </view>
          <input class="zan-field__input zan-cell__bd" type="text" placeholder="请尽可能详细地提交留言信息" bindblur="onMessageDialogTextChange" value="{{ messageDialog.message }}" />
        </view>
        <view class="zan-cell zan-field">
          <view class="multi-upload__title zan-field__title">图片信息</view>
          <view class="zan-cell__bd">
            <view wx:if="{{ messageDialog.imgs.length == 0 }}" class="van-c-gray-dark">可上传{{ MAX_PICTURES }}张图片</view>
            <view wx:else class="van-c-gray-dark">还能上传{{ MAX_PICTURES - messageDialog.imgs.length }}张图片</view>
            <view>
              <view
                wx:for="{{ messageDialog.imgs }}" wx:key="key" wx:for-item="imgData"
                class="multi-upload__img-contain {{ imgData.uploading ? 'multi-upload__img-contain--loading' : '' }}"
              >
                <image class="multi-upload__img" src="{{ imgData.srcPreview }}" mode="aspectFit"></image>
                <icon
                  class="multi-upload__clear"
                  data-key="{{ imgData.key }}"
                  type="clear" size="16" color="black"
                  catchtap="onMessageImageDelete"
                />
              </view>

              <view
                wx:if="{{ messageDialog.imgs.length < MAX_PICTURES }}"
                class="multi-upload__img-contain multi-upload__img-add"
                catchtap="chooseMessageImages"
              ></view>
            </view>
          </view>
        </view>
      </view>
      <view class="order-dialog__action">
        <button class="order-dialog__action-btn zan-btn zan-btn--last-child" catchtap="hideMessageDialog">取消</button>
        <button class="order-dialog__action-btn zan-btn zan-btn--danger zan-btn--last-child" catchtap="submitMessageDialog">提交</button>
      </view>
    </view>
  </view>
</template>

<template name="youzan-apply-dialog">
  <view class="order-dialog zan-popup zan-popup--bottom {{ youzanDialog.show ? 'zan-popup--show' : '' }}">
    <view class="zan-popup__mask" catchtap="hideYouzanDialog"></view>
    <view class="zan-popup__container">
      <view class="order-dialog__header zan-popup__header">
        <text>申请有赞客服介入</text>
      </view>
      <view class="order-dialog__content">
        <view class="zan-cell zan-field">
          <view class="zan-field__title">
            维权理由
          </view>
          <input class="zan-field__input zan-cell__bd" type="text" placeholder="有赞客服将尽快为你处理" bindblur="onYouzanDialogTextChange" value="{{ youzanDialog.message }}" />
        </view>
        <view class="zan-cell zan-field zan-cell--last-child">
          <view class="multi-upload__title zan-field__title">图片举证</view>
          <view class="zan-cell__bd">
            <view wx:if="{{ youzanDialog.imgs.length == 0 }}" class="van-c-gray-dark">可上传{{ MAX_PICTURES }}张图片</view>
            <view wx:else class="van-c-gray-dark">还能上传{{ MAX_PICTURES - youzanDialog.imgs.length }}张图片</view>
            <view>
              <view
                wx:for="{{ youzanDialog.imgs }}" wx:key="key" wx:for-item="imgData"
                class="multi-upload__img-contain {{ imgData.uploading ? 'multi-upload__img-contain--loading' : '' }}"
              >
                <image class="multi-upload__img" src="{{ imgData.srcPreview }}" mode="aspectFit"></image>
                <icon
                  class="multi-upload__clear"
                  data-key="{{ imgData.key }}"
                  type="clear" size="16" color="black"
                  catchtap="onYouzanImageDelete"
                />
              </view>

              <view
                wx:if="{{ youzanDialog.imgs.length < MAX_PICTURES }}"
                class="multi-upload__img-contain multi-upload__img-add"
                catchtap="chooseYouzanImages"
              ></view>
            </view>
          </view>
        </view>
      </view>
      <view class="order-dialog__action">
        <button class="order-dialog__action-btn zan-btn zan-btn--last-child" catchtap="hideYouzanDialog">取消</button>
        <button class="order-dialog__action-btn zan-btn zan-btn--danger zan-btn--last-child" catchtap="submitYouzanDialog">提交</button>
      </view>
    </view>
  </view>
</template>
