<import src="../components/zan-tab/index.wxml" />

<template name="safe-info">
  <view class="zan-panel {{ tab.selectedId == 1 ? '' : 'safe-panel--no-border-bottom' }}">
    <template is="zan-tab" data="{{tab: tab, componentId: 'tab'}}"></template>

    <template wx:if="{{ tab.selectedId == 1 }}" is="info" data="{{ safe_no, safe, goods }}"></template>
    <template wx:else is="chat" data="{{ log }}"></template>

  </view>
</template>

<template name="chat">
  <view class="safe-chat">
    <block wx:for="{{ log }}" wx:key="id" wx:for-item="logItem">
      <view class="safe-chat__time van-c-gray-dark van-center">{{ logItem.add_time }}</view>
      <view class="zan-chat {{ logItem.explain_detail.who == '买家' ? 'zan-chat--light zan-chat--right' : 'zan-chat--dark zan-chat--left' }}">
        <view class="zan-chat__detail">
          <view
            wx:if="{{ logItem.explain_detail.state_str }}"
            class="zan-chat__hd {{ logItem.explain_detail.detail.length > 0 || logItem.ext_info.length > 0 ? '' : 'zan-chat__hd--no-border' }}"
          >{{ logItem.explain_detail.state_str }}</view>
          <view
            wx:if="{{ logItem.explain_detail.detail.length > 0 || logItem.ext_info.length > 0 }}"
            class="zan-chat__bd"
          >
            <view wx:for="{{ logItem.explain_detail.detail }}" wx:for-item="explainItem" wx:key="unique" class="zan-chat__row">
              <view class="zan-chat__label">{{ explainItem.title }}：</view>
              <view class="zan-chat__content">{{ explainItem.str }}</view>
            </view>
            <view wx:if="{{ logItem.ext_info.length > 0 }}" class="zan-chat__row">
              <view class="zan-chat__label">图片举证：</view>
              <view class="zan-chat__content">
                <image
                  wx:for="{{ logItem.ext_info }}" wx:for-item="pictureData" wx:key="src"
                  class="van-pull-left"
                  src="{{ pictureData.srcPreview }}"
                  mode="aspectFit"
                  data-src="{{ pictureData.src }}"
                  catchtap="showPicture"
                  style="width: 60px; height: 60px; margin-right: 10px; margin-bottom: 10px;"
                />
              </view>
            </view>
          </view>
        </view>
        <view class="zan-chat__arrow"></view>
      </view>
    </block>
  </view>
</template>

<template name="info">
  <view class="info">
    <view class="safe-row">
      <text class="van-c-black">退款编号：</text>
      <text class="van-pull-right van-c-gray-darker">{{ safe_no }}</text>
    </view>
    <view class="safe-row">
      <text class="van-c-black">申请时间：</text>
      <text class="van-pull-right van-c-gray-darker">{{ safe.add_time }}</text>
    </view>
    <view class="safe-row" wx:if="{{ safe.safe_reason_str }}">
      <text class="van-c-black">退款原因：</text>
      <text class="van-pull-right van-c-gray-darker">{{ safe.safe_reason_str }}</text>
    </view>
    <view class="safe-row">
      <text class="van-c-black">处理方式：</text>
      <text class="van-pull-right van-c-gray-darker">{{ safe.safe_type_str }}</text>
    </view>
    <view class="safe-row">
      <text class="van-c-black">退款金额：</text>
      <text class="van-pull-right van-c-red">￥{{ safe.refund_fee }}</text>
    </view>
  </view>

  <view class="info-goods">
    <view class="info-goods__namecard zan-card">
      <view class="zan-card__thumb">
        <image class="zan-card__img" src="{{ goods.imgPreview }}" mode="aspectFit"></image>
      </view>
      <view class="zan-card__detail">
        <view class="zan-card__detail-row">
          <view class="zan-card__right-col">¥ {{ goods.payPriceStr }}</view>
          <view class="zan-card__left-col van-multi-ellipsis--l2">{{ goods.title }}</view>
        </view>

        <view class="zan-card__detail-row van-c-gray-darker">
          <view class="zan-card__right-col">x{{ goods.num }}</view>
          <view class="zan-card__left-col">{{ goods.skuStr }}</view>
        </view>

        <view class="zan-card__detail-row van-c-gray-darker">
          <view class="zan-card__left-col van-c-red">{{ goods.orderStateStr }}</view>
        </view>

      </view>
    </view>
  </view>
</template>
