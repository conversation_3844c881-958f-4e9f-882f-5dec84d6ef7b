// 从事件对象中解析得到 componentId
// 需要在元素上声明 data-component-id
function extractComponentId(event = {}) {
  const {
    dataset: { componentId },
  } = event.currentTarget || {};
  return componentId;
}

const Tab = {
  _handleZanTabChange(e) {
    const componentId = extractComponentId(e);
    const dataset = e.currentTarget.dataset;
    const selectedId = dataset.itemId;
    const data = { componentId, selectedId };

    if (this.handleZanTabChange) {
      this.handleZanTabChange(data);
    } else {
      console.warn('页面缺少 handleZanTabChange 回调函数');
    }
  },
};

export default Tab;
