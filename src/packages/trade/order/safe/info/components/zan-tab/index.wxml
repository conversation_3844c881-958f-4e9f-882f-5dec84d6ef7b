<template name="zan-tab">
  <view class="zan-tab">
    <block wx:if="{{ tab.scroll || scroll }}">
      <scroll-view
        class="zan-tab__bd zan-tab__bd--scroll {{ fixed ? 'zan-tab__bd--fixed' : '' }}"
        scroll-x="true"
        style="height: {{ tab.height || height ? ((tab.height || height) + 'px') : 'auto' }}; transition: linear top 300ms;top: {{fixedTop}}px;"
      >
        <template
          is="zan-tab-list"
          data="{{ list: tab.list || list, selectedId: tab.selectedId || selectedId, componentId, color: tab.color || '' }}">
        </template>
      </scroll-view>
    </block>
    <block wx:else>
      <view class="zan-tab__bd {{ fixed ? 'zan-tab__bd--fixed' : '' }}" style="transition: linear top 300ms;top: {{fixedTop}}px;">
        <template
          is="zan-tab-list"
          data="{{ list: tab.list || list, selectedId: tab.selectedId || selectedId, componentId, color: tab.color || '' }}">
        </template>
      </view>
    </block>
  </view>
</template>

<template name="zan-tab-list">
  <view
    wx:for="{{ list }}"
    wx:key="id"
    class="zan-tab__item {{ selectedId == item.id ? 'zan-tab__item--selected' : '' }}"
    data-component-id="{{ componentId }}"
    data-item-id="{{ item.id }}"
    bindtap="_handleZanTabChange"
  >
    <theme-view
      custom-class="zan-tab__title"
      color="general"
      border="general"
    >
      {{ item.title }}
    </theme-view>
  </view>
</template>
