@import "shared/common/css/helper/index.wxss";
@import "themes/zan-btn.scss";


.top-status {
  line-height: 20px;
  padding: 20px 0;
  border-bottom: 1rpx solid #e5e5e5;
}

.top-status__msg {
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
  font-size: 16px;
}

.top-info {
  font-size: 14px;
}

.top-info--desc {
  padding:10px 15px;
}

.top-info--action {
  padding: 0 15px 13px;
}

.top-info__row {
  padding: 4px 0;
}

.top-info__cell-hd {
  align-self: flex-start;
}

.top-info__btn {
  margin-bottom: 0;
}

.top-steps {
  padding: 5px 15px;
  border-top: 1rpx solid #e5e5e5;
}
