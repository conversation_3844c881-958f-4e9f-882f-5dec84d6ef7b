<view name="top-agreed">
  <top-icon type="success" msg="{{ safe.state_str }}" />
  <view class="zan-cell">
    <view class="zan-cell__bd">
      <view class="safe-row">退款金额：¥ {{ safe.refund_fee }}</view>
      <view class="safe-row">退款时间：{{ refundProcess.add_time }}</view>
    </view>
  </view>
  <view class="zan-cell zan-cell--last-child">
    <view class="zan-cell__bd">
      原路{{ refundProcess.pay_method_str }}
    </view>
    <view class="zan-cell__ft van-c-blue" catchtap="doShowRefundProcess">
      查看退款进程
    </view>
  </view>
  <view wx:if="{{ showRefundProcess && refundProcess.steps.length > 0 }}" class="top-steps">
    <van-steps direction="vertical" steps="{{ refundProcess.steps }}" active="{{ refundProcess.active }}" />
  </view>
</view>