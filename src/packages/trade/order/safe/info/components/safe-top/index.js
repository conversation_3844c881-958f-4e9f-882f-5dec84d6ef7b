import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  properties: {
    type: {
      type: String,
      value: ''
    },
    orderNo: {
      type: String,
      value: ''
    },
    safe: {
      type: Object,
      value: {}
    },
    refundProcess: {
      type: Object,
      value: {}
    },
    countdown: {
      type: Object,
      value: {}
    },
    countdownStr: {
      type: Object,
      value: {}
    }
  },

  methods: {
    jumpToFillAddress() {
      var dbid = app.db.set({
        safe_no: this.data.safe.safe_no,
        order_no: this.data.orderNo
      });

      wx.redirectTo({
        url: '/packages/trade/order/safe/express/index?dbid=' + dbid
      });
    },
  }
});
