<view>
  <view class="zan-panel zan-panel--without-margin-top">
    <top-agreed
      wx:if="{{ type === 'agreed' }}"
      safe="{{ safe }}"
      refund-process="{{ refundProcess }}"
      countdown="{{ countdown }}"
      countdown-str="{{ countdownStr }}"
    />
    <block wx:elif="{{ type === 'sended' }}">
      <template is="top-sended" data="{{ state_str: safe.state_str,  countdown, countdownStr }}" />
    </block>
    <block wx:elif="{{ type === 'closed' }}">
      <template is="top-closed" data="{{ state_str: safe.state_str, safe }}" />
    </block>
    <block wx:elif="{{ type === 'fill-address' }}">
      <template is="top-fill-address" data="{{ state_str: safe.state_str, safe,  countdown, countdownStr }}" />
    </block>
    <block wx:elif="{{ type === 'safe-ing' }}">
      <template is="top-safe-ing" data="{{ state_str: safe.state_str, safe,  countdown, countdownStr }}" />
    </block>
    <block wx:elif="{{ type === 'disagree' }}">
      <template is="top-disagree" data="{{ state_str: safe.state_str, safe,  countdown, countdownStr }}" />
    </block>
    <block wx:elif="{{ type === 'no-receive' }}">
      <template is="top-no-receive" data="{{ state_str: safe.state_str, safe,  countdown, countdownStr }}" />
    </block>
    <block wx:elif="{{ type === 'youzan' }}">
      <template is="top-youzan" data="{{ state_str: safe.state_str, safe }}" />
    </block>
  </view>

  <template name="top-sended">
    <top-icon type="warn" msg="{{ state_str }}"/>
    <view class="top-info top-info--desc zan-cell">
      <view class="zan-cell__bd">
        <view class="safe-row">如商家确认：退款成功并退回至买家的支付账户</view>
        <view class="safe-row">如商家拒绝：买家需要重新修改退款申请或申请维权</view>
      </view>
    </view>
    <view class="top-info zan-cell zan-cell--last-child">
      <view class="zan-cell__bd van-c-gray-dark">
        若卖家在
        <top-time countdown="{{ countdown }}" countdown-str="{{ countdownStr }}" />
        内未处理，则申请达成并退款给你
      </view>
    </view>
  </template>

  <template name="top-closed">
    <top-icon type="warn" msg="{{ state_str }}"/>
    <view class="top-info top-info--desc zan-cell">
      <view class="zan-cell__bd">
        <view wx:if="{{ safe.close_state == 2 }}" class="safe-row">
          退款关闭：买家超时未处理，退款关闭
        </view>
        <view wx:elif="{{ safe.is_involved == 1 }}" class="safe-row">
          退款关闭：有赞客服拒绝退款，退款关闭。
        </view>
        <view wx:else class="safe-row">
          退款关闭：买家主动撤销退款，退款关闭。
        </view>
        <view class="safe-row">结束时间：{{ safe.update_time }}</view>
      </view>
    </view>
    <view class="top-info zan-cell zan-cell--last-child">
      <view wx:if="{{ safe.close_state == 2 }}" class="zan-cell__bd van-c-gray-dark">
        你因超时未修改退款申请，无法再次发起退款。如有疑问请联系客服协商处理。
      </view>
      <view wx:elif="{{ safe.is_involved == 1 }}" class="zan-cell__bd van-c-gray-dark">
        经有赞客服核实，买卖双方同意关闭退款申请；退款申请关闭，无法再次发起退款，如有疑问请联系商家协商处理。
      </view>
      <view wx:else class="zan-cell__bd van-c-gray-dark">
        你已主动关闭退款申请，无法再次发起退款，如有疑问请联系商家协商处理。
      </view>
    </view>
  </template>

  <template name="top-fill-address">
    <top-icon type="warn" msg="{{ state_str }}"/>
    <view class="top-info zan-cell">
      <view class="top-info__cell-hd zan-cell__hd safe-row">退货地址：</view>
      <view class="zan-cell__bd">
        <view class="safe-row">{{ safe.address.address_detail }}</view>
      </view>
    </view>
    <view class="top-info zan-cell zan-cell--last-child">
      <view class="zan-cell__bd van-c-gray-dark">
        请在
        <top-time countdown="{{ countdown }}" countdown-str="{{ countdownStr }}" />
        内退货给卖家，否则将自动关闭退款申请。
      </view>
    </view>
    <view class="top-info top-info--action">
      <button class="top-info__btn zan-btn zan-btn--danger" catchtap="jumpToFillAddress">填写退货物流信息</button>
    </view>
  </template>

  <template name="top-safe-ing">
    <top-icon type="warn" msg="{{ state_str }}"/>
    <view class="top-info top-info--desc zan-cell">
      <view class="zan-cell__bd">
        <view class="safe-row">如商家同意：申请将达成并退款给买家</view>
        <view class="safe-row">如商家拒绝：买家需要重新修改退款申请或申请维权</view>
      </view>
    </view>
    <view class="top-info zan-cell zan-cell--last-child">
      <view class="zan-cell__bd van-c-gray-dark">
        若卖家在
        <top-time countdown="{{ countdown }}" countdown-str="{{ countdownStr }}" />
        内未处理，则申请达成并退款给你
      </view>
    </view>
  </template>

  <template name="top-disagree">
    <top-icon type="warn" msg="{{ state_str }}"/>
    <view class="top-info top-info--desc zan-cell">
      <view class="top-info__cell-hd zan-cell__hd safe-row">拒绝理由：</view>
      <view class="zan-cell__bd">
        <view class="safe-row">{{ safe.reject || '' }}</view>
      </view>
    </view>
    <view class="top-info zan-cell zan-cell--last-child">
      <view class="zan-cell__bd van-c-gray-dark">
        你需在
        <top-time countdown="{{ countdown }}" countdown-str="{{ countdownStr }}" />
        内重新申请退款，否则退款本次申请将关闭，你无法再次发起。
      </view>
    </view>
  </template>

  <template name="top-no-receive">
    <top-icon type="warn" msg="{{ state_str }}"/>
    <view class="top-info top-info--desc zan-cell">
      <view class="top-info__cell-hd zan-cell__hd safe-row">拒绝理由：</view>
      <view class="zan-cell__bd">
        <view class="safe-row">你需要重新修改退货信息或申请维权</view>
      </view>
    </view>
    <view class="top-info zan-cell zan-cell--last-child">
      <view class="zan-cell__bd van-c-gray-dark">
        你需在
        <top-time countdown="{{ countdown }}" countdown-str="{{ countdownStr }}" />
        内重新申请退款，否则退款本次申请将关闭，你无法再次发起。
      </view>
    </view>
    <view class="top-info top-info--action">
      <button class="top-info__btn zan-btn zan-btn--danger" catchtap="jumpToFillAddress">填写退货物流信息</button>
    </view>
  </template>

  <template name="top-youzan">
    <top-icon type="warn" msg="{{ state_str }}"/>
    <view class="top-info top-info--desc zan-cell">
      <view class="top-info__cell-hd zan-cell__hd safe-row">维权申请：</view>
      <view class="zan-cell__bd">
        <view class="safe-row">请等待有赞客满工作人员核对信息</view>
      </view>
    </view>
    <view class="top-info zan-cell zan-cell--last-child">
      <view class="zan-cell__bd van-c-gray-dark">
        有赞客服已收到你的维权申请，我们将尽快帮你和商家沟通，处理完成退款。
      </view>
    </view>
  </template>
</view>