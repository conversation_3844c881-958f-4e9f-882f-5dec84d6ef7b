import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    type: {
      type: String,
      value: ''
    },
    msg: {
      type: String,
      value: ''
    }
  },

  data: {
    newType: ''
  },

  attached() {
    let _newType = this.data.type;
    switch (this.data.type) {
      case 'success':
        _newType = 'checked';
        break;
      case 'warn':
        _newType = 'warn-o';
        break;
      default:
        break;
    }
    this.setYZData({
      newType: _newType
    });
  }
});
