.explain-container {
  padding: 24px 24px 104px;
}
.explain-steps {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
}
.explain-steps:not(:nth-child(3))::after {
  content: '';
  width: 1px;
  height: 100%;
  position: absolute;
  z-index: -1;
  left: 12px;
  background-color: #fbe7e7;
}
.step-num {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  background-color: #fbe7e7;
  font-size: 16px;
  font-family: PingFangSC-Medium, sans-serif;
  color: #ee0a24;
  font-style: italic;
  width: 24px;
  height: 24px;
}
.step-content {
  display: flex;
  flex-direction: column;
  margin-left: 12px;
}

.step-content .title {
  font-size: 16px;
  line-height: 24px;
  font-family: PingFangSC-Medium, sans-serif;
}

.step-content .help-desc {
  font-size: 10px;
  color: #969799;
  margin-top: 4px;
}
.step-one {
  width: 142px;
  height: 42px;
  margin: 10px 0 31px;
}
.step-two {
  width: 150px;
  height: 126px;
  margin: 12px 0 24px;
}
.step-three {
  width: 151px;
  height: 203px;
  margin: 11px 0 16px;
}
.explain-footer {
  position:fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px 0;
  margin-bottom: 34px;
}
.explain-btn {
  width: 200px;
  height: 44px;
  color: var(--main-text, #ffffff);
  background: var(--main-bg, #323233);
  margin: 0 auto;
  display: block;
  border-radius: 24px;
  font-size: 18px;
  text-align: center;
  line-height: 44px;
}
.explain-btn-small {
  font-size: 12px;
  width: 142px;
  height: 32px;
  line-height: 32px;
  color: var(--main-text, #ffffff);
  background: var(--main-bg, #323233);
  border-radius: 16px;
  margin: 10px 0 30px;
}
.explain-finger {
  width: 25px;
  height: 25px;
  position: absolute;
  left: 110px;
  top: 62px;
}