import WscPage from 'pages/common/wsc-page/index';
import { requestSubscribeMessage } from 'utils/subscribe-message';
const app = getApp();

WscPage({
  data: {
    templateIds: [],
    isRetailShop: false,
  },
  onLoad(query = {}) {
    const { isRetailShop } = query;
    this.getTemplateIdList();
    // query中拿到的参数是string类型
    this.setYZData({ isRetailShop: isRetailShop === 'true' });
  },
  // 获取模板id
  getTemplateIdList() {
    return app
      .request({
        path: '/wscump/common/get-template.json',
        data: {
          scene: 'deliveryDetail',
        },
      })
      .then((data = {}) => {
        this.setYZData({ templateIds: data.templateIdList || [] });
      });
  },
  clickSubscription() {
    const { isRetailShop } = this.data;
    let { templateIds = [] } = this.data;
    requestSubscribeMessage({
      templates: templateIds,
      noToast: true,
      onFail: (err) => {
        let errorMsg = '订阅微信通知失败';
        if (err.errMsg && err.errMsg.indexOf('switched off') > -1) {
          errorMsg = '请在小程序设置中允许订阅消息';
        }
        wx.showToast({ title: errorMsg, icon: 'none' });
        wx.navigateBack();
      },
      onSuccess: (res) => {
        const accept = JSON.stringify(res).indexOf('accept') > -1;
        if (isRetailShop) {
          app.logger &&
            app.logger.log({
              et: 'click',
              ei: accept ? 'accept_msg_subscribe' : 'reject_msg_subscribe',
              en: accept
                ? '接受授权订阅小程序消息'
                : '拒绝授权订阅小程序消息',
              params: {
                subscribe_pos: '物流详情页订阅', // 订阅点 从哪个位置发起的订阅
                subscribe_source: 'unknown', // 来源 是微信小程序、24小时货架、未知
                delivery_way: 'deliveryDetail', // 配送方式  到店自提、同城配送、快递  若没有则不传
              },
            });
        }
        if (accept) {
          wx.showToast({
            title: '订阅成功，继续订阅可获得多次提醒',
            icon: 'none',
          });

          app.logger &&
            app.logger.log({
              et: 'click',
              ei: 'allow_click',
              en: '点击允许',
              pt: 'SubscribeToLogistics',
              params: { 
              }
            });
            
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          app.logger &&
            app.logger.log({
              et: 'click',
              ei: 'cancle_click',
              en: '点击取消',
              pt: 'SubscribeToLogistics',
              params: { 
              }
            });
          wx.navigateBack();
        }
      },
    });

    app.logger &&
      app.logger.log({
        et: 'click',
        ei: 'Subscribe_logistics_click',
        en: '点击订阅物流',
        pt: 'SubscribeToLogistics',
        params: { 
        }
      });
  },
});