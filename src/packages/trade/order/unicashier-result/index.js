import money from '@youzan/weapp-utils/lib/money';
import WscPage from 'pages/common/wsc-page/index';

var app = getApp();

WscPage({
  data: {
    orderNo: '',
    order: {},
    fetched: false
  },

  onLoad(query) {
    const { order_no: orderNo } = query;

    this.setYZData({ orderNo });
  },

  onShow() {
    // 没有 orderNo 直接返回
    if (!this.data.orderNo) {
      wx.showModal({
        title: '提示',
        content: '订单号不存在，请稍后再试',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    app.carmen({
      api: 'youzan.trade.detail/1.0.0/composite',
      data: {
        app: 'wsc',
        biz_group: 'weapp',
        order_no: this.data.orderNo,
        options_with_payment_info: true,
        options_with_ump_info: true
      },
      success: ({ fullOrderInfo = {}, umpInfo = {} } = {}) => {
        const { mainOrderInfo = {}, paymentInfo = {} } = fullOrderInfo;
        const { goodsActivities = [] } = umpInfo;

        wx.setNavigationBarTitle({
          title: `${mainOrderInfo.stateDesc}的订单`
        });

        this.setYZData({
          fetched: true,
          order: {
            state: mainOrderInfo.state,
            payTime: mainOrderInfo.payTime,
            showDecrease: goodsActivities.length > 0,
            activity: goodsActivities,
            shopName: mainOrderInfo.shopName || '',
            buyWayStr: mainOrderInfo.buyWayDesc,
            pay: money(paymentInfo.pay).toYuan(),
            realPay: money(paymentInfo.realPay).toYuan()
          }
        });
      },
      fail: (res) => {
        console.log('fail', res);
      }
    });
  }
});
