<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view wx:if="{{ fetched }}">
    <van-panel class="panel">
      <view class="unicashier-result__cell unicashier-info">
        <view class="unicashier-result__cell--bd">
          <view>
            消费总额
            <text class="van-pull-right van-c-gray-darker">¥ {{ order.pay }}</text>
          </view>
          <view wx:if="{{ order.showDecrease }}">
            活动优惠
            <text class="van-pull-right van-c-gray-darker">
              {{ utils.getActivityStr(order.activity) }}
            </text>
          </view>
        </view>
      </view>
      <view class="unicashier-result__cell unicashier-info">
        <view class="unicashier-result__cell--bd">
          <block wx:if="{{ order.state > 10 }}">实付金额</block>
          <block wx:else>应付金额</block>
          <text class="van-pull-right van-c-red">¥ {{ order.realPay }}</text>
        </view>
      </view>
    </van-panel>

    <van-panel class="panel">
      <view class="unicashier-result__cell unicashier-info">
        <view class="unicashier-result__cell--bd">
          <view>店铺<text class="van-pull-right van-c-gray-darker">{{ order.shopName }}</text></view>
          <view>支付渠道<text class="van-pull-right van-c-gray-darker">小程序买单</text></view>
          <view>支付方式<text class="van-pull-right van-c-gray-darker">{{ order.buyWayStr || '-' }}</text></view>
          <view>消费时间<text class="van-pull-right van-c-gray-darker">{{ utils.getTimeStr(order.payTime) }}</text></view>
          <view>订单编号<text class="van-pull-right van-c-gray-darker">{{ orderNo }}</text></view>
        </view>
      </view>
    </van-panel>
  </view>
</page-container>

<wxs src="./index.wxs" module="utils"></wxs>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
