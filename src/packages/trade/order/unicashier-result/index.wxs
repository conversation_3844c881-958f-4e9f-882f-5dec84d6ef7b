function getTimeStr(millsec) {
  if (!millsec) {
    return '-';
  }

  var date = getDate();
  date.setTime(millsec);

  var datePart = [
    date.getFullYear(),
    addZero(date.getMonth() + 1),
    addZero(date.getDate())
  ];
  var timePart = [
    addZero(date.getHours()),
    addZero(date.getMinutes()),
    addZero(date.getSeconds())
  ];

  return datePart.join('-') + ' ' + timePart.join(':');
}

function addZero(num) {
  if (!num) {
    return num;
  }

  if (num < 10) {
    return '0' + num;
  }

  return num;
}

function getActivityStr(activity) {
  activity = activity || [];

  var desc = '';
  var present = [];
  var decrease = 0;

  // 遍历活动
  for (var i = 0; i < activity.length; i++) {
    var activityItem = activity[i];
    decrease += activityItem.decrease;
    // present
    if (activityItem.extraInfo && activityItem.extraInfo.present) {
      present.push(activityItem.extraInfo.present);
    }
  }

  // 如果有金额减免
  if (decrease) {
    var decreaseDesc = parseFloat(decrease / 100).toFixed(2);
    desc += ('- ¥ ' + decreaseDesc);
  }

  // 赠品显示
  if (present.length > 0) {
    desc += ('送' + present.join('，'));
  }

  return desc;
}

module.exports = {
  getTimeStr,
  getActivityStr
};
