import { setStorage, getStorageSync } from '@youzan/tee-api';

Page({
  onLoad(query = {}) {
    if (query.title) {
      wx.setNavigationBarTitle({ title: decodeURIComponent(query.title) });
    }

    let { orderNo, kdtId } = query;
    if (!kdtId) {
      kdtId = getApp().getKdtId();
    }

    // 催付订单弹窗是否在其他页面已经展示过
    const orderPayPromptPopup =
      getStorageSync('order-pay-prompt-popup') || null;
    let url = `https://h5.youzan.com/wsctrade/express/detail?order_no=${orderNo}&kdtId=${kdtId}`;
    if (orderPayPromptPopup) {
      url = `${url}&isOrderPayPromptPopup=1`;
    }
    this.setData({ viewSrc: url });
  },

  handlePostMessage(e) {
    const { data } = e.detail;
    const msg = data.find((msg) => msg.type === 'order-pay-prompt-popup');

    if (msg?.payload) {
      setStorage('order-pay-prompt-popup', true);
    }
  },
});
