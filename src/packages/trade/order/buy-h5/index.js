// 下单页（载入H5页面）
import Event from '@youzan/weapp-utils/lib/event';

const app = getApp();

const page = {
  data: {
    viewSrc: ''
  },

  onLoad(query = {}) {
    this.query = query;
    const { bookKey = '' } = query;
    Event.on('buy-h5:pay:call', detail => {
      // console.log('on buy-h5-pay: ', detail);
      this.orderNo = detail.orderNo;
    });
    this.setData({
      viewSrc: `https://cashier.youzan.com/pay/wsctrade_buy?book_key=${bookKey}`
    });
  },

  onShow() {
    const orderNo = this.orderNo;
    if (orderNo) {
      app
        .request({
          origin: 'cashier',
          path: '/pay/wsctrade/order/buy/prepare.json',
          method: 'POST',
          data: { order_no: orderNo }
        })
        .then(data => {
          const redirect = data.redirectConfig || {};

          if (redirect.timeout) {
            // 订单已超时 跳转到订单详情
            wx.redirectTo({
              url: `/packages/trade/order/result/index?orderNo=${orderNo}`
            });
          } else if (redirect.orderPaid) {
            // 订单已支付 跳转到支付成功
            wx.redirectTo({
              url: `/packages/order/paid/index?order_no=${orderNo}`
            });
          } else if (redirect.orderCanceled) {
            // 订单已取消 跳转到订单详情
            wx.redirectTo({
              url: `/packages/trade/order/result/index?orderNo=${orderNo}`
            });
          } else if (redirect.peerpay) {
            const errorId = app.db.set({ text: '小程序不支持找人代付' });
            wx.redirectTo({
              url: '/packages/common/error/index?dbid=' + errorId
            });
          }
        })
        .catch((err) => {
          // do nothing
          // 仅用于兜底极速下单待支付页跳转，异常码10000是node层在订单状态为极速下单待支付时才会抛出的
          const errorCode = +err.code;
          if (errorCode === 10000) {
            wx.redirectTo({
              url: `/packages/order-native/fastbuy/index?orderNo=${orderNo}`,
            });
          }
        });
    }
  },
};

Page(page);
