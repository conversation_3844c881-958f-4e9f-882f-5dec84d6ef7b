@import '../../../../common/index.wxss';
@import 'shared/common/css/helper/index.wxss';

.order-item {
  padding-top: 5px;
  line-height: 1.4;
  font-size: 14px;
  transition: all 1s ease;
}

.order-item--hide {
  opacity: 0;
}

.order-item__hd {
  padding: 10px 15px;
}

.order-item__extra-row {
  padding: 10px 15px;
}

.order-item__status-tag {
  color: white;
  background-color: #ff4444;
  font-size: 10px;
  line-height: 14px;
  padding: 2px 3px;
  margin-right: 5px;
  border-radius: 2px;
  border-color: #ff5050;
}

/* 订单列表的行动按钮们 */
.order-item__actions {
  margin-right: -10px;
}

.order-item__action {
  margin-right: 10px;
}

.order-item__total {
  display: inline;
}

.re-order__btn {
  background-color: #fff;
}

.view-card-btn {
  padding-left: 12rpx !important;
  padding-right: 12rpx !important;
}
