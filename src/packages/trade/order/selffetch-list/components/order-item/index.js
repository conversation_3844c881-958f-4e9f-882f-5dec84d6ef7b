import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';

WscComponent({
  properties: {
    list: {
      type: Array,
      value: [],
    },
  },
  methods: {
    onOrderItemClicked(e) {
      const orderItem = e.currentTarget.dataset.item;

      navigate.navigate({
        url: `/packages/trade/order/result/index?orderNo=${orderItem.order_no}`,
      });
    },

    onGoToDetailPage(e) {
      const { item } = e.target.dataset;
      this.triggerEvent('goToDetailPage', item);
    },
  },
});
