<view
  class="order-item zan-panel zan-panel--without-border"
  wx:for="{{ list }}"
  wx:for-item="item"
  wx:key="order_no"
  data-item="{{ item }}"
  catchtap="onOrderItemClicked"
>
  <view class="order-item__hd">
    <view>
      <text>店铺: {{ item.team_name }}</text>
      <text class="van-pull-right van-c-red">{{ item.order_state_str }}</text>
    </view>
    <view class="van-c-gray-dark van-font-12">订单编号：{{ item.order_no }}</view>
  </view>

  <!-- 商品内容 -->
  <view class="order-item__bd goods-card goods-card--first zan-card">
    <view class="zan-card__thumb">
      <image
        class="zan-card__img"
        src="{{ item.items[0].image_url }}"
        mode="aspectFit"
      />
    </view>
    <view class="zan-card__detail">
      <view class="zan-card__detail-row">
        <view class="zan-card__right-col">￥{{ item.items[0].pay_price }}</view>
        <view class="zan-card__left-col van-multi-ellipsis--l2">{{ item.items[0].title }}</view>
      </view>

      <view class="zan-card__detail-row van-c-gray-darker">
        <view class="zan-card__right-col">x{{ item.items[0].num }}</view>
        <view class="zan-card__left-col goods-card__detail-sku">{{ item.items[0].skuStr }}</view>
      </view>

      <text wx:if="{{ item.items[0].goods_type === 24 }}" class="order-item__status-tag">周期购</text>
      <text class="order-item__status-tag">自提</text>
    </view>
  </view>

  <view
    wx:if="{{ item.items.length > 1 }}"
    class="order-item__extra-row zan-cell"
  >
    <view class="van-center zan-cell__bd">
      <theme-view custom-class="order-item__view-all" color="general">查看全部{{ item.items.length }}件商品</theme-view>
    </view>
  </view>

  <!-- 价格区域 -->
  <view class="order-item__extra-row zan-cell">
    <view class="zan-cell__bd van-right">
      <text>合计：</text>
      <theme-view custom-class="order-item__total" color="general">￥{{ item.real_pay }}</theme-view>
    </view>
  </view>

  <!-- 按钮区域 -->
  <view class="order-item__extra-row zan-cell zan-cell--last-child">
    <view class="order-item__actions zan-cell__bd van-right">
      <view class="order-item__action zan-btn zan-btn--small zan-btn--plain">订单详情</view>
      <view
        class="order-item__action zan-btn zan-btn--small zan-btn--primary"
        data-item="{{ item }}"
        catchtap="onGoToDetailPage"
      >
        提货
      </view>
    </view>
  </view>
</view>
