import openWebView from 'shared/utils/open-web-view';
import navigate from '@/helpers/navigate';
import { VanxPage } from 'pages/common/wsc-page/index';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';

const app = getApp();

VanxPage({
  data: {
    list: [],
    finished: false,
    page: 1,
    offlineId: '',
    writeOffType: '',
    selfFetchId: '',
    selfPointKdtId: '',
    otherSelfFetchList: [],
    otherListFinished: false,
    otherListPage: 1,
    supportCrossShop: false,
  },

  onLoad(query) {
    // 通过落地页跳转过来，落地页默认会删除kdtId字段，只能用shopId替换一下
    const {
      shopId,
      offlineId = 0,
      selfFetchId = '',
      selfPointKdtId = '',
    } = query;
    this.setYZData({
      kdtId: shopId,
      offlineId,
      selfFetchId,
      selfPointKdtId,
    });

    wx.showLoading({
      title: '加载中',
    });
  },

  onShow() {
    this.fetchSelffetchData();
  },

  onPullDownRefresh() {
    this.clearSelffetchList();
    this.fetchSelffetchData(true);
  },

  watch: {
    'data.finished': function (val) {
      if (this.data.supportCrossShop && val) {
        this.fetchOtherListData();
      }
    },
  },

  onOrderItemClicked(e) {
    const orderItem = e.currentTarget.dataset.item;

    navigate.navigate({
      url: `/packages/trade/order/result/index?orderNo=${orderItem.order_no}`,
    });
  },

  onGoToDetailPage({ detail: item }) {
    const item0 = item.items[0];

    const url =
      item0.goods_type === 24
        ? '/v2/trade/order/periodselffetchcode'
        : '/wsctrade/order/selffetch/detail';

    const query = {
      // 普通自提码
      orderNo: item.order_no,
      // 周期购自提码
      order_no: item.order_no,
      // 当前自提点id
      selfFetchId: this.data.selfFetchId,
    };

    const { chainStoreInfo = {} } = app.getShopInfoSync();
    const { isChainStore } = chainStoreInfo;
    if (isChainStore) {
      query.hqKdtId = app.getHQKdtId();
    }

    if (this.data.writeOffType === '1') {
      query.can_selffetch = true;
    }

    openWebView(url, {
      title: '自提订单提货凭证',
      query,
    });
  },

  fetchSelffetchData(isRefresh) {
    const { finished, supportCrossShop } = this.data;
    if (finished) {
      supportCrossShop && this.fetchOtherListData();
      return;
    }

    app
      .request({
        path: 'wsctrade/order/selffetch/shopConfig.json',
        query: {
          selfPointKdtId: this.data.selfPointKdtId,
          isMultiConfig: 1,
        },
      })
      .then((data) => {
        const { writeOffType, supportCrossShop } = mapKeysToCamelCase(data);
        this.setYZData({
          writeOffType,
          supportCrossShop: !!+supportCrossShop,
        });
        this.fetchSelffetchOrderList(isRefresh);
      })
      .catch((err) => {
        if (isRefresh) {
          wx.stopPullDownRefresh();
        }

        wx.hideLoading();
        wx.showToast({
          title: err.msg || err.message,
          icon: 'none',
        });
      });
  },

  fetchSelffetchOrderList(isRefresh) {
    app
      .request({
        path: 'wsctrade/orderlist/selffetch.json',
        query: {
          page: this.data.page,
          pageSize: 10,
          kdtId: this.data.kdtId,
          storeId: this.data.offlineId,
          selfFetchId: this.data.selfFetchId,
        },
        config: {
          noStoreId: true,
        },
      })
      .then((data) => {
        wx.hideLoading();

        if (data.length === 1 && !this.data.supportCrossShop) {
          this.onGoToDetailPage({
            detail: data[0],
          });
          return;
        }

        if (isRefresh) {
          wx.stopPullDownRefresh();
        }

        const list = [...this.data.list, ...data];

        this.setYZData({
          page: ++this.data.page,
          finished: data.length < 10,
          list,
        });
      })
      .catch((err) => {
        if (isRefresh) {
          wx.stopPullDownRefresh();
        }

        wx.hideLoading();
        wx.showToast({
          title: err.msg || err.message,
          icon: 'none',
        });
      });
  },

  // 获取其它自提点待自提订单列表
  fetchOtherListData() {
    if (this.data.otherListFinished) return;

    app
      .request({
        path: 'wsctrade/orderlist/selffetch.json',
        query: {
          page: this.data.otherListPage,
          pageSize: 10,
          kdtId: this.data.kdtId,
          storeId: this.data.offlineId,
          selfFetchId: this.data.selfFetchId,
          crossShopSearch: true,
        },
        config: {
          noStoreId: true,
        },
      })
      .then((data) => {
        const list = [...this.data.otherSelfFetchList, ...data];

        this.setYZData({
          otherListPage: ++this.data.otherListPage,
          otherListFinished: data.length < 10,
          otherSelfFetchList: list,
        });
      })
      .catch((err) => {
        wx.showToast({
          title: err.msg || err.message,
          icon: 'none',
        });
      });
  },

  clearSelffetchList() {
    this.setYZData({
      page: 1,
      list: [],
      finished: false,
      otherSelfFetchList: [],
      otherListFinished: false,
      otherListPage: 1,
    });
  },
});
