<import src="./templates/empty.wxml" />

<page-container class="{{ themeClass }} page-{{ deviceType }}">
  <view class="order-list">
    <!-- list -->
    <scroll-view
      class="order-list__list"
      scroll-y
      bindscrolltolower="fetchSelffetchData"
      lower-threshold="150"
    >
      <view class="order-list__container">
        <order-item list="{{ list }}" bind:goToDetailPage="onGoToDetailPage" />

        <template
          wx:if="{{ finished && list.length == 0 }}"
          is="selffetch-empty"
        />

        <!-- 其它自提点待自提订单列表 -->
        <block wx:if="{{ supportCrossShop && otherSelfFetchList.length > 0 }}">
          <van-divider content-position="center" custom-style="color:#333;border-color:#333;padding:0 15%;">其它自提点待自提订单</van-divider>
          <order-item list="{{ otherSelfFetchList }}" bind:goToDetailPage="onGoToDetailPage" />
        </block>
        <!-- loadmore -->
        <loadmore wx:if="{{ !finished || (supportCrossShop && !otherListFinished) }}" type="loading" />
      </view>

    </scroll-view>
  </view>
</page-container>
