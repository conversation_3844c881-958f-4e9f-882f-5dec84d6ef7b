<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view class="order-share__container {{!is_share && 'is-not-share'}}">
    <image class="circular" src="https://img01.yzcdn.cn/public_files/2016/06/02/556e3fc8c9e2851785f67c58c109e31f.png" />
    <view class="mata" wx:if="{{is_share}}">\(^o^)/ 哦耶！在买买买的路上越战越勇</view>
    <view class="mata" wx:if="{{!is_share}}">
      <view>\(^o^)/ YES! 买到一件好货！</view>
      <view>快去分享给小伙伴吧！</view>
    </view>
  </view>
  <scroll-view class="order-share__scroll-container {{goods.length === 1 && 'only-one'}}" scroll-x>
    <view hover-class="none" class="share-good__base-info" wx:for="{{goods}}" wx:key="*this">
      <view class="share-good__pic">
        <image src="{{item.img_url}}" />
      </view>
      <view class="share-good__title">{{item.title}}</view>
      <view class="share-good__price van-font-14">¥ {{item.price}}</view>
      <view class="van-c-gray van-font-12 share-good__shop">来自{{item.shop}}</view>
      <view class="share-good__active-btns">
        <van-button 
          class="order-share__btn" 
          bind:click="globalNavigate"
          data-type="switch"
          data-url="/packages/home/<USER>/index"
          plain 
          size="small"
          type="primary">进店逛逛</van-button>
        
        <van-button 
          wx:if="{{item.goods_type != 206}}"
          class="order-share__btn" 
          bind:click="globalNavigate"
          data-url="/pages/goods/detail/index?alias={{item.alias}}"
          size="small"
          type="primary">我也要买</van-button>
        <van-button 
          wx:else
          class="order-share__btn" 
          bind:click="globalNavigate"
          data-type="switch"
          data-url="/packages/home/<USER>/index"
          size="small"
          type="primary">我也要买</van-button>
      </view>
    </view>
  </scroll-view>
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />