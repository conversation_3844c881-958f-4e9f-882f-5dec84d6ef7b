import WscPage from 'pages/common/wsc-page/index';

/** TODO 获取订单信息 */
const app = getApp();
const page = {
  data: {
    themeClass: app.themeClass,
  },
  onReady() {
    app.getShopInfo()
      .then(() => {
        this.setYZData({
          shop: app.getShopInfoSync(),
        });
      });
  },
  onLoad(query) {
    this.setYZData(query);
    this.fetchOrderInfo(query.order_no);
  },
  onShareAppMessage() {
    return {
      path: 'packages/trade/order/share/index?order_no=' + this.data.order_no + '&is_share=1',
      title: this.data.goods[0].title,
      imageUrl: this.data.goods[0].img_url
    };
  }
};

page.fetchOrderInfo = function (order_no) {
  app.carmen({
    api: 'kdt.trade.buyer.search/1.0.0/get',
    query: { order_no },
    success: ({ trades }) => {
      let order = trades[0];
      let goods = order.items.map(({
        goods_info, price, shop_info, goods_type
      }) => {
        goods_info.price = price;
        goods_info.shop = shop_info.shop_name;
        goods_info.goods_type = goods_type;
        return goods_info;
      });
      this.setYZData({ goods });
    }
  });
};

WscPage(page);
