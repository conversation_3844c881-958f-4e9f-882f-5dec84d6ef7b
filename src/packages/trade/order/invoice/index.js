import WscPage from 'pages/common/wsc-page/index';

WscPage({
  data: {
    src: ''
  },

  onLoad(query) {
    const {
      type = '',
      kdt_id = '',
      order_no = ''
    } = query;

    let src = '';

    switch (type) {
      case 'detail':
        src = `https://h5.youzan.com/wsctrade/order/invoice?order_no=${order_no}&kdt_id=${kdt_id}`;
        break;
      case 'success':
        src = `https://h5.youzan.com/wsctrade/order/invoice/success?order_no=${order_no}`;
        break;
      default: break;
    }

    this.setYZData({ src });
  },
});
