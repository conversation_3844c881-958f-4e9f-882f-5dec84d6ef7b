<view class="time">
  <view wx:if="{{ isUsingNewSnapshot }}" class="time__item snapshot" catch:tap="handleGoSnapshotClick">
    <text>交易快照：
      <text class="time__item__text__color">发生交易争议时，可作为判断依据</text>
    </text>
    <van-icon custom-class="snapshot-icon" name="arrow" />
  </view>
  <view class="time__item time__item__order-no">
    <view class="time__item__order-no__text">订单编号：
      <text class="time__item__text__color">{{ orderNo }}</text>
    </view>
    <van-button size="mini" custom-class="time__item__copy" catch:tap="handleCopyBtnClick">复制</van-button>
  </view>
  <view wx:if="{{ !!time.createTime }}" class="time__item">创建时间：
    <text class="time__item__text__color">{{ time.createTime }}</text>
  </view>
  <view wx:if="{{ !!time.payTime }}" class="time__item">{{ time.payTitle }}：
    <text class="time__item__text__color">{{ time.payTime }}</text>
  </view>
  <view wx:if="{{ !!time.expressTime }}" class="time__item">发货时间：
    <text class="time__item__text__color">{{ time.expressTime }}</text>
  </view>
  <view wx:if="{{ !!time.successTime }}" class="time__item">完成时间：
    <text class="time__item__text__color">{{ time.successTime }}</text>
  </view>
  <view class="time__action">
    <view wx:if="{{ yzGuaranteeUrl }}" class="time__action__item" catch:tap="toYzGuaranteeUrl">联系你的专属客服</view>
    <block wx:else>
      <!-- 对此订单有疑问先下线，由于登录态问题 -->
      <view wx:if="{{ orderQuestionUrl && !moneyToWhereUrl }}" class="time__action__item" catch:tap="toOrderQuestion">对此订单有疑问？</view>
      <view wx:if="{{ !!moneyToWhereUrl }}" class="time__action__item money-to-where">
        温馨提示：货款已通过微信支付付给商家。
        <text class="time__action__item__link" catch:tap="toMoneyToWhere">钱款去向</text>
      </view>
    </block>
  </view>
</view>
