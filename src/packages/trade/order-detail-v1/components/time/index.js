import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'shared/utils/open-web-view';
import args from '@youzan/weapp-utils/lib/args';

const app = getApp();

WscComponent({
  properties: {
    orderNo: String,
    time: Object,
    moneyToWhereUrl: String,
    orderQuestionUrl: String,
    yzGuaranteeUrl: String,
    orderExtra: Object,
    isUsingNewSnapshot: Boolean
  },

  methods: {
    isTradeComponent3() {
      let BIZ_ORDER_ATTRIBUTE = {};
      try {
        BIZ_ORDER_ATTRIBUTE = JSON.parse(
          this.data.orderExtra?.BIZ_ORDER_ATTRIBUTE || '{}'
        );
        return (
          BIZ_ORDER_ATTRIBUTE.WX_CHANNELS_COMPONENT_VERSION ===
          'TRADE_COMPONENT_3_0'
        );
      } catch (error) {
        return false;
      }
    },

    handleGoSnapshotClick() {
      app.logger
        && app.logger.log({
          et: 'click',
          ei: 'click_history',
          en: '进入交易快照',
          si: app.getKdtId()
        });

      const url = args.add('/wsctrade/order/snapshot', {
        order_no: this.data.orderNo,
        kdt_id: app.getKdtId()
      });

      wx.navigateTo({
        url: args.add('/pages/common/webview-page/index', {
          src: encodeURIComponent(url)
        })
      });
    },

    handleCopyBtnClick() {
      wx.setClipboardData({
        data: this.data.orderNo,
        success() {
          wx.showToast({
            title: '订单编号已复制',
            icon: 'success'
          });
        }
      });
    },

    toMoneyToWhere() {
      openWebView(this.data.moneyToWhereUrl, { title: '钱款去向' });
    },

    toOrderQuestion() {
      if (this.isTradeComponent3()) {
        wx.showToast({
          title: '视频号订单，请咨询腾讯客服 400-670-0700',
          icon: 'none',
        });
        return;
      }

      openWebView(this.data.orderQuestionUrl);
    },

    toYzGuaranteeUrl() {
      openWebView(args.add(this.data.yzGuaranteeUrl, {
        from_biz: 'wsc',
        from_scene: 'youzandanbao'
      }));
    }
  }
});
