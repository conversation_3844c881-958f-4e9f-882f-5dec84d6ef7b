.time {
  margin-top: 10px;
  font-size: 12px;
  line-height: 22px;
  background: #fff;
}

.time__item {
  line-height: 22px;
  padding: 0 16px;
  color: #969799;
}

.time__item:first-child {
  padding-top: 13px;
}

.time__item__text__color {
  color: #323233;
}

.time__item__order-no {
  display: flex;
  align-items: center;
}

.time__item__order-no__text {
  margin-right: 10px;
}

.time__item__copy {
  height: 18px !important;
  line-height: 16px !important;
  min-width: 30px !important;
  justify-content: center !important;
}

.time__action {
  margin-top: 10px;
  display: flex;
  position: relative;
}

.time__action::after {
  content: ' ';
  position: absolute;
  pointer-events: none;
  box-sizing: border-box;
  transform-origin: center;
  top: -50%;
  bottom: -50%;
  left: 16px;
  right: 16px;
  transform: scaleY(0.5);
  border-top: 1px solid #eee;
}

.time__action .time__action__item {
  flex: 1;
  padding: 14px 0;
  color: #1989fa;
  text-align: center;
  vertical-align: middle;
  position: relative;
  font-size: 14px;
}

.time__action .time__action__item.money-to-where {
  color: #646566;
  text-align: left;
  padding: 0 15px;
}

.time__action .time__action__item__link {
  color: #1989fa;
  padding: 4px;
}

.time__action__item + .time__action__item::after {
  content: ' ';
  position: absolute;
  pointer-events: none;
  box-sizing: border-box;
  transform-origin: center;
  height: 50%;
  top: 25%;
  left: 0;
  right: -50%;
  bottom: 25%;
  transform: scaleY(0.5);
  border-left: 1px solid #eee;
}

.snapshot {
  display: flex;
  align-items: center;
}

.snapshot-icon {
  display: block!important;
  margin-left: 4px;
}