@import '~mixins/index.scss';

.wrapper {
  padding: 0 16px;
  background: #fff;
}

.status {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 20px 0 20px 52px;
  min-height: 40px;
  background: url('https://img01.yzcdn.cn/public_files/2018/08/30/345a61fbbf62d65a3a8c528272426666.png')
    left center no-repeat;
  background-size: 40px 40px;
}

.status__title {
  font-size: 14px;
  font-weight: bold;
  color: $text-color;
}

.status__desc {
  font-size: 12px;
  color: $gray-darker;
}

.order-countdown {
  width: 16px;
  text-align: center;
  display: inline-block;
}

.steps {
  padding: 0 0 10px;
}

.status--100 {
  background-image: url('https://img01.yzcdn.cn/public_files/2018/08/31/6556fce0b55b6a74bc68cba692bdb1ea.png');
}

.status--99 {
  background-image: url('https://img01.yzcdn.cn/public_files/2018/08/31/85f176382a5babc1eeed69ab34eac3ab.png');
}

.status--60 {
  background-image: url('https://img01.yzcdn.cn/public_files/2018/08/31/4cb7dad7d11e401a07081ebb1f3914de.png');
}

.status--50 {
  background-image: url('https://img01.yzcdn.cn/public_files/2018/08/31/017f720391c682d5cb50c8ac770e93c6.png');
}

.status--10 {
  background-image: url('https://img01.yzcdn.cn/public_files/2018/08/31/6eb5418154ef15f9454b0500c800cfcb.png');
}

.status--20 {
  background-image: url('https://img01.yzcdn.cn/public_files/2018/08/31/6eb5418154ef15f9454b0500c800cfcb.png');
}
