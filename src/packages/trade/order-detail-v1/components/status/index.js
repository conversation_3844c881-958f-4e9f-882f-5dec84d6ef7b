import WscComponent from 'pages/common/wsc-component/index';
import CountDown from 'utils/countdown';

const TIME_MAP = {
  DAY: 'day',
  HOUR: 'hour',
  MIN: 'min',
  SEC: 'sec'
};
const countdownData = [
  {
    type: TIME_MAP.DAY,
    value: '',
    text: '天',
    width: '0'
  },
  {
    type: TIME_MAP.HOUR,
    value: '',
    text: '时',
    width: '0'
  },
  {
    type: TIME_MAP.MIN,
    value: '',
    text: '分',
    width: '0'
  },
  {
    type: TIME_MAP.SEC,
    value: '',
    text: '秒',
    width: '0'
  }
];

WscComponent({
  properties: {
    state: Number,
    stateTitle: String,
    stateDescInfo: {
      type: Object,
      observer: 'setStateDesc'
    },
    groupLeftTime: String,
    groupLeftPeople: String,
    steps: Object
  },

  data: {
    desc: '',
    descArray: []
  },

  lifetimes: {
    detached() {
      this.stopCountDown();
    }
  },

  methods: {
    stopCountDown() {
      this.sendedCountDown && this.sendedCountDown.stop();
      this.grouponCountDown && this.grouponCountDown.stop();
    },

    setStateDesc() {
      this.stopCountDown();

      const { stateDescInfo } = this.data || {};

      if (stateDescInfo.showOrderStatusToGroup) {
        this.startGrouponCountDown();
      } else if (stateDescInfo.showOrderStatusSended) {
        this.startSendedCountDown();
      } else if (stateDescInfo.closeTypeStr) {
        this.setYZData({
          desc: stateDescInfo.closeTypeStr
        });
      } else {
        this.setYZData({
          desc: ''
        });
      }
    },

    // 开始确认收货倒计时
    startSendedCountDown() {
      const { stateDescInfo } = this.data || {};

      this.sendedCountDown = new CountDown(stateDescInfo.leftTime * 1000, {
        onChange: (timeData, strData) => {
          const { day, hour, minute, second } = strData;
          const count = countdownData.map((item) => {
            let value = '';
            switch (item.type) {
              case TIME_MAP.DAY:
                value = day;
                break;
              case TIME_MAP.HOUR:
                value = hour;
                break;
              case TIME_MAP.MIN:
                value = minute;
                break;
              default:
                value = second;
                break;
            }
            return {
              ...item,
              width: `${value.length * 8}px`,
              value
            };
          });
          let descArray = [];
          if (day > 0) {
            descArray = count.slice(0, 2);
          } else if (hour == 0 && minute == 0 && second == 0) {
            descArray = count;
          } else {
            const countdown = [hour, minute, second];
            for (let i = 0; i < 3; i++) {
              if (countdown[i] != 0) {
                descArray = count.slice(i + 1);
                break;
              }
            }
          }
          this.setYZData({
            descArray
          });
        }
      });
    },

    // 开始拼团倒计时
    startGrouponCountDown() {
      const { groupLeftTime = 0, groupLeftPeople = 0 } = this.data;
      this.grouponCountDown = new CountDown(groupLeftTime * 1000, {
        onChange: (timeData, strData) => {
          const { day, hour, minute, second } = strData;
          const timeStrArr = [`${day}天`, `${hour}时`, `${minute}分`, `${second}秒`];

          if (day == 0) {
            timeStrArr.shift();
          }
          if (day == 0 && hour == 0) {
            timeStrArr.shift();
          } else {
            timeStrArr.pop();
          }

          this.setYZData({
            desc: `拼团单需在${timeStrArr.join('')}内邀请${groupLeftPeople}位好友参团`
          });
        },
        onEnd: () => {
          this.triggerEvent('refresh');
        }
      });
    }
  }
});
