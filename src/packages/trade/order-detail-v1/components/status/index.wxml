<view class="wrapper">
  <view class="status status--{{ state }}">
    <view class="status__title">{{ stateTitle }}</view>
    <view wx:if="{{ desc }}" class="status__desc">
      <text>{{ desc }}</text>
    </view>
    <view wx:elif="{{ descArray.length }}" class="status__desc">
      <text>还剩</text>
      <text wx:for="{{ descArray }}" wx:key="type">
        <text class="order-countdown" style="width:{{item.width}}">{{ item.value }}</text><text>{{ item.text }}</text>
      </text>
      <text>自动确认完成</text>
    </view>
  </view>

  <!-- steps -->
  <van-steps
    wx:if="{{ steps && steps.list.length }}"
    custom-class="steps"
    steps="{{ steps.list }}"
    active="{{ steps.active }}"
  />
</view>
