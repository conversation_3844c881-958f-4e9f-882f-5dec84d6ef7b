import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    orderNo: String,
    groupon: {
      type: Object,
      observer: 'onGrouponChange'
    },
    activityType: Number
  },

  data: {
    showPopup: false
  },

  methods: {
    onGrouponChange(groupon) {
      const { agencyReceiveType } = groupon;

      const popupDescContent =
        agencyReceiveType === 1
          ? '拼团商品可以在提交订单时选择团长代收，这样也可以免除团员的邮费哦，但需注意，团长的收货地址、收货手机号、收货人姓名将会展示给团员。团长收货后会分发给团员，团员也可以向团长领取哦'
          : '该拼团商品商家设置必须由团长代收，成团后商品统一发货给团长，这样也可以免除团员的邮费哦，但需注意，团长的收货地址、收货手机号、收货人姓名将会展示给团员。团长收货后会分发给团员，团员也可以向团长领取哦';

      this.setYZData({
        popupDescContent
      });
    },

    togglePopup() {
      this.setYZData({
        showPopup: !this.data.showPopup
      });
    },

    handleGrouponCollectionClick() {
      const { orderNo, activityType } = this.data;
      const { alias, isHead, isGrouponSuccess } = this.data.groupon;

      if (isHead) {
        if (isGrouponSuccess) {
          wx.navigateTo({
            url: `/packages/collage/groupon/detail/index?orderNo=${orderNo}&groupAlias=${alias}&type=${activityType}`
          });
        } else {
          this.togglePopup();
        }
      }
    }
  }
});
