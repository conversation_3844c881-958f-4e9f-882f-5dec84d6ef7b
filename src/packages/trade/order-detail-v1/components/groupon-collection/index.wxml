<van-cell-group custom-class="groupon-collection" border="{{ false }}">
  <van-cell
    icon="point-gift"
    title="{{ groupon.groupAgencyReceiveDesc }}"
    is-link="{{ groupon.isHead }}"
    border="{{ false }}"
    bind:click="handleGrouponCollectionClick"
  />
</van-cell-group>

<van-popup
  show="{{ showPopup }}"
  position="bottom"
  bind:click-overlay="togglePopup"
>
  <view class="popup__desc">
    <view class="popup__desc__title">团长代收</view>
    <view class="popup__desc__content van-c-gray-dark">{{ popupDescContent }}</view>
  </view>

  <van-button
    size="large"
    type="danger"
    bind:click="togglePopup"
  >完成</van-button>
</van-popup>

