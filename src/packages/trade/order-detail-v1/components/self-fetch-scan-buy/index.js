import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    orderNo: String,
    selfFetchInfo: {
      type: Object,
      value: {},
      observer: 'splitSelfFetchCode',
    },
  },

  data: {
    fetchNo: '',
  },

  methods: {
    splitSelfFetchCode(newVal) {
      if (newVal && newVal.fetchNo) {
        this.setYZData({
          fetchNo: newVal.fetchNo.replace(/(\d{4})(\d{4})(\d*)/g, '$1 $2 $3'),
        });
      }
    },
  },
});
