@import "shared/common/css/helper/index.wxss";

.self-fetch {
  background: #fff;
  margin-top: 10px;
}

.self-fetch__title {
  color: #323233;
  font-size: 16px;
  font-weight: 500;
  line-height: 44px;
  text-align: center;
}

.self-fetch__content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 178px;
  width: 178px;
  margin: 15px auto 0;
}

.self-fetch__qrcode {
  width: 178px;
  height: 178px;
  border: 1px solid #dcdee0;
}

.self-fetch__content__loading {
  height: 80px;
  width: 80px;
  margin-right: 15px;
  background-size: 120%;
  background-position: center;
  text-align: center;
  line-height: 80px;
}

.self-fetch__code {
  font-family: Avenir;
  font-size: 16px;
  line-height: 20px;
  font-weight: bold;
  color: #333;
  text-align: center;
  padding: 16px 0 12px;
}

.self-fetch__desc {
  color: #969799;
  font-size: 14px;
  padding: 10px 0 15px;
  text-align: center;
}

.cell-label-self-fetch {
  flex-grow: 0;
  flex-basis: 90px;
}
