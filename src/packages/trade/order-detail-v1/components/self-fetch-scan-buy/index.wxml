<view class="self-fetch">
  <view wx:if="{{ !selfFetchInfo.selfFetchState }}">
    <!-- 未核销正常展示 -->
    <view class="self-fetch__title">核销码</view>
    <view class="self-fetch__content">
      <image wx:if="{{ selfFetchInfo.qrcode }}" class="self-fetch__qrcode" src="{{ selfFetchInfo.qrcode }}" alt="二维码" />
      <van-loading class="self-fetch__content__loading" wx:else />
    </view>
    <view class="self-fetch__code">{{ fetchNo || '正在生成中...' }}</view>
    <view wx:if="{{ selfFetchInfo.qrcode }}" class="self-fetch__desc">请将核销码出示给收银员</view>
  </view>
  <view wx:else>
    <!-- 已核销 -->
    <van-cell title="提货码" label-class="cell-label-self-fetch" border="{{ false }}">{{ fetchNo + '（已核销）' }}</van-cell>
  </view>
</view>