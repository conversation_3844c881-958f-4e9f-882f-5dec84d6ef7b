import WscComponent from 'pages/common/wsc-component/index';
import config from '../../common/config';

const {
  VIRTURL_TICKET_STATE,
  VIRTURL_TICKET_CONF
} = config;

WscComponent({
  properties: {
    orderNo: String,
    virtualTicketInfo: {
      type: Object,
      value: {}
    }
  },

  data: {
    VIRTURL_TICKET_STATE,
    VIRTURL_TICKET_CONF
  },

  methods: {
    clickQrCode() {
      let orderNo = this.properties.orderNo;
      wx.navigateTo({
        url: `/packages/trade/cert/verify-ticket/index?order_no=${orderNo}`
      });
    },
    onCopy(e) {
      wx.setClipboardData({
        data: e.currentTarget.dataset.ticket,
        success() {
          wx.showToast({
            title: '复制成功',
            icon: 'success'
          });
        }
      });
    }
  },
});
