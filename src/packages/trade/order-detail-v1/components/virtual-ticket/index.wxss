@import "shared/common/css/helper/index.wxss";

.virtual-ticket {
  margin-top: 10px;
  background: #fff;
}

.virtual-ticket__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  text-align: left;
}

.virtual-ticket__header__title__value {
  font-weight: bold;
  color: #111;
}

.virtual-ticket__header__title__label {
  font-size: 10px;
  color: #666;
}

.virtual-ticket__header__qrcode {
  height: 22px;
  width: 22px;
  background-image: url('https://b.yzcdn.cn/fix-base64/e76e5a8715a0f19d4fb38b313828dabf57df7d7c7eee05b222fdd840d4794c99.png');
  background-repeat: no-repeat;
  background-size: 22px 22px;
  background-position: center center;
}

.virtual-ticket__list {
  text-align: left;
}

.virtual-ticket__list__item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.virtual-ticket__list__item:not(:first-child) {
  margin-top: 10px;
}

.virtual-ticket__list__item__code {
  margin-left: 10px;
  font-size: 14px;
  color: #111;
  font-weight: bold;
}

.virtual-ticket__list__item__code--del {
  color: #999;
  text-decoration: line-through;
}

.virtual-ticket__list__item__copy {
  background: #f7f8fa;
  border-radius: 12px;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  color: #111;
  text-align: right;
  line-height: 16px;
  padding: 4px 12px;
}

.virtual-ticket__list__item__copy:active {
  background: #ccc;
}

.virtual-ticket__list__item__state {
  font-size: 14px;
}