<van-cell-group custom-class="virtual-ticket">
  <van-cell>
    <view class="virtual-ticket__header">
      <view class="virtual-ticket__header__title">
        <view class="virtual-ticket__header__title__value">{{ virtualTicketInfo.title }}</view>
        <view class="virtual-ticket__header__title__label">有效期：{{ virtualTicketInfo.time }}</view>
      </view>
      <view 
        bindtap="clickQrCode"
        class="virtual-ticket__header__qrcode"/> 
    </view>
  </van-cell>

  <van-cell>
    <view class="virtual-ticket__list">
      <view
        wx:for="{{ virtualTicketInfo.list }}"
        wx:key="item.ticketNo"
        class="virtual-ticket__list__item"
      >
        <view>
          <text>券码{{ index + 1 }}：</text>
          <text
            class="virtual-ticket__list__item__code virtual-ticket__list__item__code{{VIRTURL_TICKET_CONF[item.verifyState].suffix}}"
          >{{ item.ticketNo }}</text>
        </view>
        <view
          wx:if="{{ item.verifyState === 1 }}"
          class="virtual-ticket__list__item__copy"
          data-ticket="{{ item.ticketNo }}"
          bindtap="onCopy"
        >复制</view>
        <view
          wx:else
          class="virtual-ticket__list__item__state"
        >{{ VIRTURL_TICKET_CONF[item.verifyState].value }}</view>
      </view>
    </view>
  </van-cell>
</van-cell-group>