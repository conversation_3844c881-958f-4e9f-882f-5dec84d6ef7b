<wxs module="filter">
  module.exports.priceFilter = function(v) {
    var sign = v >= 0 ? '- ¥' : '+ ¥';
    return sign + ' ' + (Math.abs(v) / 100).toFixed(2);
  }
</wxs>

<van-cell-group custom-class="price-panel" border="{{ false }}">
  <!-- 支付信息 -->
  <van-cell custom-class="price-cell_margin">
    <view wx:if="{{ total }}" class="price-panel__item">
      <text class="price-panel__item__title">商品金额</text>
      <text class="price-panel__item__value">{{ total }}</text>
    </view>
    <view wx:if="{{ postage }}" class="price-panel__item">
      <text class="price-panel__item__title">运费</text>
      <text class="price-panel__item__value">{{ postage }}</text>
    </view>
    <view wx:if="{{ taxPirce }}" class="price-panel__item">
      <text class="price-panel__item__title">进口税（含运费税款）</text>
      <text class="price-panel__item__value">{{ taxPirce }}</text>
    </view>
    <view
      wx:if="{{ popupActivitiesList.length }}"
      class="price-panel__item"
      catch:tap="toggleActivitiesPopup"
    >
      <text class="price-panel__item__title">店铺活动</text>
      <view class="price-panel__item-activity">
        <text class="price-panel__item__value">{{ filter.priceFilter(decreaseAmount) }}</text>
        <van-icon class="price-panel__item__icon" color="#1989fa" name="question-o" size="14px" />
      </view>
    </view>

    <!-- 内购券 -->
    <view wx:if="{{ fissionTicketNum }}" class="price-panel__item">
      <text class="price-panel__item__title">内购券</text>
      <text class="price-panel__item__value">{{ fissionTicketNum }}张</text>
    </view>

    <!-- 支付优惠 -->
    <view wx:if="{{ payUmpDiscountMoney }}" class="price-panel__item">
      <text class="price-panel__item__title">支付优惠</text>
      <text class="price-panel__item__value">{{ filter.priceFilter(payUmpDiscountMoney) }}</text>
    </view>

    <!-- 活动 popup -->
    <activities-popup
      show="{{ showPopup }}"
      list="{{ popupActivitiesList }}"
      decrease-amount="{{ decreaseAmount }}"
      bind:close="toggleActivitiesPopup"
    />

    <!-- 活动 -->
    <view
      wx:for="{{ panelActivitiesList }}"
      wx:key="index"
      class="price-panel__item"
    >
      <text class="price-panel__item__title">{{ item.promotionTypeName }}</text>
      <text class="price-panel__item__value">{{ filter.priceFilter(item.decrease) }}</text>
    </view>

    <!-- 附加费用 -->
    <view
      wx:for="{{ extraPrices }}"
      wx:key="index"
      class="price-panel__item"
    >
      <text class="price-panel__item__title">{{ item.title }}</text>
      <text class="price-panel__item__value">{{ item.price }}</text>
    </view>

    <!-- 储值卡/礼品卡 -->
    <view wx:if="{{ prepayCardPay }}" class="price-panel__item">
      <text class="price-panel__item__title">储值卡/礼品卡</text>
      <text class="price-panel__item__value">{{ prepayCardPay }}</text>
    </view>

    <!-- 优惠信息 -->
  </van-cell>

  <!-- 支付方式 -->
  <van-cell
    wx:if="{{ buyWay }}"
    title="付款方式"
    center="{{ true }}"
    title-class="price-panel__buy-way__title"
    is-link="{{ allowBuyWayLink }}"
    bind:click="openBuyWayDetail"
  >
    <text class="price-panel__item__value">{{ buyWay }}</text>
    <text wx:if="{{ buyWayExtendDescInner }}" class="price-panel__item__value {{ buyWayExtendDescOutLimit ? 'price-panel__buy-way__desc' : '' }}"> {{ buyWayExtendDescInner }}</text>
  </van-cell>

  <!-- 合计 -->
  <van-cell border="{{ !!(showAfterSaleMobile || contact.isImOrder) }}" wx:if="{{!isPriorUse}}">
    <text class="real-pay__title">实付款：</text>
    <text class="real-pay__value van-c-red">{{ realPay }}</text>
  </van-cell>

  <van-cell border="{{ !!(showAfterSaleMobile || contact.isImOrder) }}" wx:if="{{isPriorUse}}">
    <view wx:if="{{!isPriorUsePaid}}">
      <text class="real-pay__title">应付款：</text>
      <text class="real-pay__value van-c-red">{{ realPay }}</text>
      <text class="real-pay__title">，确认收货后自动扣款</text>
    </view>
    <view wx:else>
      <text class="real-pay__title">先用后付，实付款：</text>
      <text class="real-pay__value van-c-red">{{ realPay }}</text>
    </view>
  </van-cell>

  <!-- 小额打款 -->
  <microtransfer-popup
    wx:if="{{ transferStat.totalFinishedCount }}"
    count="{{ transferStat.totalFinishedCount }}"
    amount="{{ transferStat.totalFinishedAmount }}"
    list="{{ transferOrderList }}" 
  />

  <!-- 售后 -->
  <van-cell
    wx:if="{{ showAfterSaleMobile || contact.isImOrder }}"
    custom-class="after-sale"
    border="{{ false }}"
  >
    <view class="after-sale__slot">
      <!-- 拨打电话 -->
      <view wx:if="{{ showAfterSaleMobile }}" class="after-sale__item">
        <form-view>
          <view
            class="after-sale__btn"
            catch:tap="handleMakePhoneCall"
          >
            <van-icon
              class="after-sale__btn__icon"
              name="phone-o"
              size="14px"
            />
            <text>拨打电话</text>
          </view>
        </form-view>
      </view>

      <view wx:if="{{ showAfterSaleMobile }}" class="vertical-line"></view>

      <!-- 联系客服 -->
      <view
        wx:if="{{ contact.isImOrder }}"
        class="{{ contact.isImOrder && showAfterSaleMobile ? 'after-sale__item after-sale__item--border-left' : 'after-sale__item' }}"
      >
        <form-view>
          <message-contact
            wx:if="{{ hasUserInfo }}"
            contact-class="after-sale__btn van-c-blue"
            hover-class="none"
            open-type="contact"
            bindcontact="onContactBack"
            send-message-path="{{ messagePath }}"
            send-message-img="{{ messageImg }}"
            show-message-card="{{ messageCard }}"
            business-id="{{ businessId }}"
            session-from="{{ sourceParam }}"
          >
            <van-icon
              class="after-sale__btn__icon"
              name="comment-circle-o"
              size="14px"
            />
            <text>{{ contentText }}</text>
          </message-contact>

          <!-- 假装成联系客服的获取用户信息按钮 -->
          <user-authorize
            wx:else
            authTypeList="{{ ['nicknameAndAvatar'] }}"
            class="van-c-blue"
            btn-class="after-sale__btn"
            bindgetuserinfo="onGetUserInfo"
          >
            <van-icon
              class="after-sale__btn__icon"
              name="chat-o"
              size="14px"
            />
            <text>{{ contentText }}</text>
          </user-authorize>
        </form-view>
      </view>
    </view>
  </van-cell>
</van-cell-group>

