@import "shared/common/css/helper/index.wxss";

.price-panel {
  margin-top: 10px;
}

.price-cell_margin {
  padding-top: 4px!important;
}

.price-panel__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  line-height: 24px;
  color: #333;
  margin-top: 6px;
}

.price-panel__item-activity {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.price-panel__item__title {
  font-size: 14px;
  color: #969799;
}

.price-panel__item__value {
  font-size: 14px;
  color: #323233;
}

.price-panel__item__icon {
  margin-left: 6px;
}

.real-pay__title {
  color: #323233;
}

.real-pay__value {
  font-weight: bold;
}

.after-sale {
  padding: 0 !important;
}

.after-sale__slot {
  display: flex;
}

.after-sale__item {
  position: relative;
  flex: 1;
}

.after-sale__item--border-left::after {
  content: '';
  position: absolute;
  left: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background-color: #e5e5e5;
  transform: scaleX(.5);
}

.vertical-line {
  width: 0;
  height: 21px;
  border-left: 1px solid #EBEDF0;
  transform: scaleX(.5);
  margin-top: 11px;
}

.after-sale__btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 43px;
  font-size: 14px;
  color: #323233;
  background-color: #fff;
}

.after-sale__btn::after {
  border: none;
}

.after-sale__btn__icon {
  height: 14px;
  margin-right: 4px;
  font-size: 0;
}

.price-panel__buy-way__title {
  flex-grow: 0 !important;
  flex-basis: 80px !important;
}

.price-panel__buy-way__desc {
  display: block;
  color: #999;
  font-size: 12px;
  line-height: 18px;
}
