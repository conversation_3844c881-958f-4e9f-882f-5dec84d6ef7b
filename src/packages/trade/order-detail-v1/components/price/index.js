import WscComponent from 'pages/common/wsc-component/index';
import { getCurrentPage } from 'shared/common/base/wsc-component';
import openWebView from 'shared/utils/open-web-view';
import args from '@youzan/weapp-utils/lib/args';
import navigate from '@/helpers/navigate';

const app = getApp();

WscComponent({
  properties: {
    total: String,
    buyWay: {
      type: String,
      observer: 'setBuyWay'
    },
    buyWayExtendDesc: {
      type: String,
      observer: 'setBuyWayExtendDesc'
    },
    buyWayDetailUrl: String,
    postage: String,
    orderExtraPrices: {
      type: Array,
      observer: 'getExtraPrices'
    },
    prepayCardPay: String,
    taxPirce: String,
    realPay: String,
    showAfterSaleMobile: Boolean,
    contact: {
      type: Object,
      observer: 'setContentText'
    },
    hasUserInfo: Boolean,
    kdtId: String,
    messageImg: String,
    decreaseAmount: Number,
    payUmpDiscountMoney: Number,
    activitiesList: {
      type: Array,
      observer: 'setList'
    },
    pointsName: String,
    orderNo: {
      type: String,
      observer: 'setPath'
    },
    goodsNum: {
      type: Number,
      observer: 'setPath'
    },
    fissionTicketNum: Number,
    transferStat: Object,
    transferOrderList: Array,
    isPriorUse:Boolean,
    isPriorUsePaid:Boolean,
  },

  data: {
    showPopup: false,
    allowBuyWayLink: false,
    buyWayExtendDescInner: '',
    buyWayExtendDescOutLimit: false,
    contentText: '在线客服'
  },

  ready() {
    const getContactInfo = getCurrentPage().getContactInfo;
    getContactInfo &&
      getContactInfo().then((contactData) => {
        // console.log(contactData);
        this.setYZData(contactData);
      });
  },

  methods: {
    onContactBack: navigate.contactBack,

    toggleActivitiesPopup() {
      this.setYZData({
        showPopup: !this.data.showPopup
      });
    },

    setContentText(val = {}) {
      const { imOrderText = '' } = val;
      let imOrderTextParse = {};

      try {
        imOrderTextParse = JSON.parse(imOrderText);
      } catch (error) {
        imOrderTextParse = {};
      }

      this.setYZData({
        contentText: imOrderTextParse.default === 1 ? imOrderTextParse.label : '在线客服'
      });
    },

    setList() {
      const replacePointsName = (name) =>
        (name || '').replace('积分', this.data.pointsName || '积分');
      const panelActivitiesList = [];
      const popupActivitiesList = [];

      this.data.activitiesList.forEach((item) => {
        const found = item.type === 'pointDeduction';
        if (found) {
          item.promotionTypeName = replacePointsName(item.promotionTypeName);
          item.activityName = replacePointsName(item.activityName);
        }
        if (item.isShopActivity === 1) {
          popupActivitiesList.push(item);
        } else if (item.isShopActivity === 0) {
          panelActivitiesList.push(item);
        }
      });

      this.setYZData({
        panelActivitiesList,
        popupActivitiesList
      });
    },

    getExtraPrices() {
      const extraPrices = [];
      (this.data.orderExtraPrices || []).forEach((item) => {
        extraPrices.push({
          title: item.desc,
          price: `+ ¥ ${(item.realPay / 100).toFixed(2)}`
        });
      });

      this.setYZData({ extraPrices });
    },

    setPath() {
      const path = `/packages/trade/order/result/index?orderNo=${this.data.orderNo}&num=${this.data.goodsNum}`;
      this.setYZData({ path });
    },

    setBuyWay() {
      if (this.data.buyWay === '分期支付') {
        this.setYZData({ allowBuyWayLink: true });
      }
    },

    setBuyWayExtendDesc(newBuyWayDesc) {
      if (newBuyWayDesc) {
        const _buyWayExtendDescOutLimit = newBuyWayDesc ? newBuyWayDesc.length >= 11 : false;
        this.setYZData({ buyWayExtendDescOutLimit: _buyWayExtendDescOutLimit });
        this.setYZData({
          // eslint-disable-next-line no-nested-ternary
          buyWayExtendDescInner: newBuyWayDesc
            ? _buyWayExtendDescOutLimit
              ? newBuyWayDesc
              : `(${newBuyWayDesc})`
            : ''
        });
      }
    },

    openBuyWayDetail() {
      const { allowBuyWayLink, buyWayDetailUrl } = this.data;
      if (allowBuyWayLink && buyWayDetailUrl) {
        app.logger &&
          app.logger.log({
            et: 'click',
            ei: 'installment',
            en: '跳转到分期支付',
            si: app.getKdtId()
          });

        const hashArray = buyWayDetailUrl.split('#');
        const domainUrl = hashArray[0].split('?')[0];

        openWebView(domainUrl, {
          query: args.getAll(buyWayDetailUrl),
          title: '分期详情'
        });
      }
    },

    handleMakePhoneCall() {
      const { areaCode = '', phoneNumber = '', mobileNumber = '' } = this.data.contact;
      const _phoneNumber = `${areaCode}${phoneNumber}`;

      wx.makePhoneCall({
        phoneNumber: _phoneNumber || mobileNumber
      });
    },

    onGetUserInfo() {
      this.triggerEvent('check-user-info');
    }
  }
});
