import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'shared/utils/open-web-view';

WscComponent({
  properties: {
    count: Number,
    amount: String,
    list: Array
  },

  data: {
    show: false
  },

  methods: {
    doGoMyChange(e) {
      const change = e.currentTarget.dataset.change;
      change && this[change]();
    },
    showPopup() {
      this.setYZData({ show: true });
    },

    onClose() {
      this.setYZData({ show: false });
    },

    goMyChange() {
      openWebView('/wscassets/change/myChange', {
        title: '零钱明细'
      });
    }
  }
});
