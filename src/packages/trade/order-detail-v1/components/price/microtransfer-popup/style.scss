.micro-cell {
  font-family: PingFangSC-Regular;
  font-size: 12px !important;
  color: #333;

  &__count {
    display: inline;
    color: #323233;
  }

  &__amount {
    display: inline;
    color: #141415;
    font-family: PingFangSC-Medium;
  }
}

.micro-popup {
  color: #323233;
  font-family: PingFangSC-Regular;
  height: 500px;

  &__title {
    width: 100%;
    height: 44px;
    line-height: 44px;
    text-align: center;
    font-size: 16px;
    font-family: PingFangSC-Medium;
  }

  &__scroll {
    max-height: 456px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__list {
    width: 100%;
    margin-top: 32px;
  }

  &__list-title {
    height: 20px;
    margin-bottom: 16px;
    font-size: 16px;
    text-align: center;
    line-height: 20px;
  }

  &__cash {
    height: 40px;
    margin-bottom: 40px;
    text-align: center;
    font-family: Avenir-Heavy;
    font-size: 32px;
    line-height: 40px;
  }

  &__divider {
    color: #f2f3f5;
    margin: 0 24px;
  }

  &__main {
    font-size: 14px;
    line-height: 18px;
    margin: 16px 0 16px 24px;

    view {
      line-height: 34px;
      color: #646566;

      .micro-span {
        display: inline;
        margin-left: 32px;
        color: #323233;
      }
    }

    .both-phone-label {
      margin-left: 32px;
      display: inline-block;
      vertical-align: top;
      color: #323233;
      max-width: calc(100vw - 120px);
    }

    .micro-phone {
      display: inline;
      margin: 0;
      color: #323233;
    }

    .micro-check {
      display: inline;
      margin-left: 8px;
      color: #38f;
    }

    .micro-check-else {
      display: inline;
      margin-left: 8px;
      color: #3c3d3f;
    }

    &__number {
      display: inline-block;
      vertical-align: top;
      max-width: calc(100% - 100px);
    }
  }

  &__divide {
    height: 12px;
    width: 100%;
    background: #f7f8fa;
  }
}
