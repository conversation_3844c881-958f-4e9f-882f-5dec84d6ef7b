<view>
  <van-cell 
    title="补偿金额"
    is-link  
    custom-class="micro-cell"
    bind:click="showPopup" 
    >
    <view wx:if="{{ count > 1 }}">
      <view class="micro-cell__count">存在{{ count }}笔，共</view>
      <view class="micro-cell__amount">￥{{ amount }}</view>
    </view>
    <view wx:elif="{{ count === 1 }}" class="micro-cell__count">
      ￥{{ amount }}
    </view>
  </van-cell>
  <van-popup 
    show ="{{ show }}"
    closeable
    round
    position="bottom" 
    class="micro-popup"
    bind:close="onClose"
    >
    <view class="micro-popup__title">打款详情</view>
    <view class="micro-popup__scroll">
      <view wx:for="{{ list }}">
        <view class="micro-popup__list">
          <view class="micro-popup__list-title">来自商家补偿金额</view>
          <view class="micro-popup__cash">+{{ item.amount }}</view>
          <van-divider custom-class="micro-popup__divider" />
          <view class="micro-popup__main">
            <view>
              当前状态
              <view class="micro-span">{{ item.statusStr }}</view>
            </view>
            <view>
              补偿金额
              <view class="micro-span">￥{{ item.amount }}</view>
            </view>
            <view>
              到账账号
              <view class="both-phone-label">
                <view class="micro-phone">{{ item.targetPhoneNo }}</view>
                <view
                  class="{{ item.showText.class }}"
                  bindtap="doGoMyChange"
                  data-change="{{ item.showText.change }}"
                >
                  {{ item.showText.label }}
                </view>
              </view>
            </view>
            <view>
                到账时间
              <view class="micro-span">{{ item.finishedTime }}</view>
            </view>
            <view>
                交易单号
              <view class="micro-popup__main__number micro-span">{{ item.outOrderNo }}</view>
            </view>
          </view>
          <view wx:if="{{ item.showDivide }}"  class="micro-popup__divide"></view>
        </view>
      </view>
    </view>
  </van-popup>
</view>
