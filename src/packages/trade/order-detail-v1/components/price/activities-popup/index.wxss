@import "helpers/index.wxss";

.list {
  min-height: 430px;
}

.list__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
}

.list__item:not(:first-child) {
  margin-top: 10px;
}

.list__item__title {
  display: flex;
  align-items: center;
  line-height: 0;
}

.list__item__title__tag {
  color: inherit !important;
  margin-right: 6px;
}

.list__item__title__name {
  font-size: 12px;
  line-height: 1;
  color: #323233;
}

.list__item__value {
  font-size: 12px;
  line-height: 1;
  color: #323233;
}

.list__item__value text {
  font-weight: bold;
  color: #000;
}

.decrease-amount {
  font-size: 12px;
  color: #323233;
  text-align: right;
}

.decrease-amount__value {
  display: inline-block;
  font-weight: bold;
}

.activities__bottom-wrapper {
  margin: 0 16px 16px;
  border-radius: 999px;
}

.activities__bottom-btn {
  color: inherit;
  background: transparent;
  font-size: 16px;
  line-height: 50px;
}

.activities__bottom-btn::after {
  border: none;
}
