<wxs module="filter">
  module.exports.priceFilter = function(v) {
    return '¥ ' + (v / 100).toFixed(2);
  }
</wxs>

<van-popup
  show="{{ show }}"
  round
  position="bottom"
  bind:close="handleClose"
>
  <van-cell-group
    border="{{ false }}"
    custom-class="list"
  >
    <van-cell>
      <view
        wx:for="{{ list }}"
        wx:key="index"
        class="list__item"
      >
        <theme-view color="general" border="main-bg" class="list__item__title">
          <van-tag
            wx:if="{{ item.activityName }}"
            plain
            type="danger"
            custom-class="list__item__title__tag"
          >{{ item.promotionTypeName }}</van-tag>
          <text wx:if="{{ item.activityName }}" class="list__item__title__name">{{ item.activityName }}</text>
        </theme-view>
        <view class="list__item__value">省 <text>{{ filter.priceFilter(item.decrease) }}</text></view>
      </view>
    </van-cell>
    <van-cell border="{{ false }}">
      <view class="decrease-amount">合计：省 <theme-view color="general" class="decrease-amount__value">{{ filter.priceFilter(decreaseAmount)}}</theme-view></view>
    </van-cell>
  </van-cell-group>

  <theme-view
    bg="main-bg"
    border="main-bg"
    color="main-text"
    custom-class="activities__bottom-wrapper"
  >
    <button
      class="activities__bottom-btn"
      bind:tap="handleClose"
    >我知道了</button>
  </theme-view>
</van-popup>
