@import '~mixins/index.scss';

.receiver-info {
  position: relative;
  display: flex;
  padding: 16px 16px 16px 40px;
  background: #fff;
}

.receiver-info__icon {
  position: absolute;
  left: 16px;
  top: 17px;
  font-size: 0;
}

.receiver-info__content {
  flex: 1;
}

.receiver-info__title {
  display: flex;
  justify-content: space-between;
  line-height: 18px;
  font-size: 14px;
  font-weight: bold;
  color: $text-color;
}

.receiver-info__address {
  margin-top: 4px;
  font-size: 12px;
  color: $gray-darker;
}

.receiver-info--stripe-border::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: #fff url('https://b.yzcdn.cn/v2/image/wap/address/<EMAIL>') repeat-x;
  background-size: 36px 2px;
}

.van-hairline--top {
  &::before {
    content: ' ';
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    transform-origin: center;
    top: 0;
    left: 16px;
    right: 16px;
    bottom: auto;
    transform: scaleY(0.5);
    border-bottom: 1px solid #ebedf0;
  }
}
