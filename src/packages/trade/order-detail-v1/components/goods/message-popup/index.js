import WscComponent from 'pages/common/wsc-component/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

WscComponent({
  properties: {
    message: {
      type: Object,
      observer(value) {
        // ?? ob obj
        this.setYZData({
          formatedMessage: this.formatMessage(value)
        });
      }
    },
    show: Boolean
  },

  data: {
    formatedMessage: [] // 留言列表
  },

  methods: {
    handleClose() {
      this.triggerEvent('close');
    },

    // 处理留言列表 区分图片和文本
    formatMessage(message = {}) {
      return Object.keys(message).map(key => {
        const type = /^\s*http(s)*:\/\/.+/.test(message[key]) ? 'image' : 'text';

        return {
          key,
          type,
          value: type === 'image'
            ? cdnImage(message[key], '!200x200.jpg')
            : message[key]
        };
      });
    },

    // 预览图片
    previewImg(e) {
      const src = e.currentTarget.dataset.src;

      wx.previewImage({
        current: src,
        urls: [src]
      });
    }
  }
});
