<van-popup
  show="{{ show }}"
  position="bottom"
  bind:close="handleClose"
>
  <view class="message-popup__title van-hairline--bottom">商品留言</view>

  <van-cell-group
    custom-class="message-popup__cell-group"
    border="{{ false }}"
  >
    <van-cell
      wx:for="{{ formatedMessage }}"
      wx:key="index"
      title="{{ item.key }}"
      title-width="80px"
      value-class="message-popup__cell__value"
      border="{{ index !== formatedMessage.length - 1 }}"
    >
      <image
        wx:if="{{ item.type === 'image' }}"
        src="{{ item.value }}"
        mode="aspectFit"
        style="width: 70px; height: 70px;"
        data-src="{{ item.value }}"
        catch:tap="previewImg"
      />
      <text wx:else>{{ item.value || '无' }}</text>
    </van-cell>
  </van-cell-group>

  <theme-view
    bg="main-bg"
    border="main-bg"
    color="main-text"
    custom-class="message-popup__bottom-wrapper"
  >
    <van-button
      round
      block
      size="large"
      color="inherit"
      custom-class="message-popup__bottom-btn"
      bind:click="handleClose"
    >关闭</van-button>
  </theme-view>
</van-popup>
