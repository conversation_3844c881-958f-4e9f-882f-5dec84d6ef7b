import WscComponent from 'pages/common/wsc-component/index';
import config from '../../common/config';
import * as processUtils from '../../common/process';
import { handleUrlWithShopAutoEnter } from 'common-api/multi-shop/multi-shop-redirect';
import asyncEvent from 'shared/utils/async-event';

const BTN = config.btn;
const app = getApp();

WscComponent({
  properties: {
    url: String,
    orderNo: String,
    orderExtra: Object,
    itemId: String,
    thumb: String,
    title: String,
    price: String,
    originPrice: String,
    desc: String,
    num: String,
    preSaleDate: String,
    message: Object,
    tags: Array,
    imgTag: Object,
    btns: Array,
    taxTips: String,
    refundOrderItem: Object,
    processInfo: Object,
    isShipped: Boolean,
  },

  data: {
    showMessagePopup: false,
  },

  methods: {
    isTradeComponent3() {
      let BIZ_ORDER_ATTRIBUTE = {};
      try {
        BIZ_ORDER_ATTRIBUTE = JSON.parse(
          this.data.orderExtra?.BIZ_ORDER_ATTRIBUTE || '{}'
        );
        return (
          BIZ_ORDER_ATTRIBUTE.WX_CHANNELS_COMPONENT_VERSION ===
          'TRADE_COMPONENT_3_0'
        );
      } catch (error) {
        return false;
      }
    },

    // 分发商品按钮点击事件
    handleBtnClick(e) {
      if (this.isTradeComponent3()) {
        wx.showToast({
          title: '视频号订单，请前往视频号订单中心操作',
          icon: 'none',
        });
        return;
      }
      const data = e.currentTarget.dataset;

      const afterProcess = () => {
        switch (data.type) {
          case BTN.refund.value:
            this.refund(data.type);
            break;
          case BTN.message.value:
            this.toggleMessagePopup();
            break;
          case BTN.postpone.value:
            this.handlePostpone();
            break;
          default:
            break;
        }
      };

      asyncEvent.triggerAsync
        .apply(getApp(), [
          'beforeGoodsBtnClick',
          {
            type: data.type,
            itemId: this.data.itemId,
            orderNo: this.data.orderNo,
          },
        ])
        .then(() => {
          afterProcess();
        })
        .catch(() => {});
    },

    handlePostpone() {
      Promise.all([
        new Promise((resolve) =>
          wx
            .createSelectorQuery()
            .in(this)
            .select(`#goods-btn-${BTN.postpone.value}`)
            .boundingClientRect(resolve)
            .exec()
        ),
        new Promise((resolve) =>
          wx.createSelectorQuery().selectViewport().scrollOffset(resolve).exec()
        ),
      ]).then(([rect, offset]) => {
        this.trigger('show:postpone', {
          position: {
            left: rect.left - 5 + offset.scrollLeft,
            top: rect.top + rect.height / 2 + offset.scrollTop,
          },
          itemId: this.data.itemId,
        });
      });
    },

    handleTitleClick() {
      const { url } = this.data;
      url && wx.navigateTo({ url: handleUrlWithShopAutoEnter(url) });
    },

    // 查看留言
    toggleMessagePopup() {
      this.setYZData({
        showMessagePopup: !this.data.showMessagePopup,
      });
    },

    // 退款
    refund() {
      processUtils.refund(this.data.itemId);
      const order_no = this.data.orderNo;
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'goto_refund',
          en: '订单详情-发起退货退款',
          params: {
            order_no,
          },
        });
    },
  },
});
