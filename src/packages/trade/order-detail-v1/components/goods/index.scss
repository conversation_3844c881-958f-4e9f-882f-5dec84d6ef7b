.goods {
  background-color: #fafafa;
}

.card__row {
  display: flex;
  font-size: 14px;
  line-height: 20px;
}

.card__title {
  line-height: 20px;
}

.card__title__goods-name {
  word-break: break-all;
  max-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card__title__shipped-tag {
  color: #faab0c;
  float: right;
}

.card__btn-cell {
  padding-top: 16px;
}

.card__btn:not(:last-child) {
  margin-right: 5px;
}

.card__tag {
  margin-top: 6px;
}

.card__tag:not(:last-child) {
  margin-right: 6px;
}

.card__extra {
  background-color: #fafafa !important;
}

.card__tax {
  font-size: 10px;
  color: #f44;
}

.goods-thumb {
  background-color: #fff !important;
}
