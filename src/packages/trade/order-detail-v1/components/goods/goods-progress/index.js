import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    processInfo: {
      type: Object,
      value: {},
      observer(val) {
        val = val || {};
        const { bomProcessList = [] } = val;
        const current = bomProcessList.findIndex(item => item.current === 1);

        this.setYZData({
          process: bomProcessList,
          current
        });
      }
    }
  },
  data: {
    current: 0,
    process: []
  }
});
