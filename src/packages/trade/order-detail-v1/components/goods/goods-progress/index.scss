@keyframes identifier {
  from {
    opacity: .1;
  }

  to {
    opacity: 1;
  }
}

.goods-progress {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 10px;
  margin-top: 8px;

  &__item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #C8C9CC;
  }

  &__text {
    &.current {
      color: #323233;
      animation: identifier 1.5s infinite;
    }
  }

  &__line {
    height: 1px;
    width: 9px;
    background: #C8C9CC;
    margin: 0 2.7px;
  }
}