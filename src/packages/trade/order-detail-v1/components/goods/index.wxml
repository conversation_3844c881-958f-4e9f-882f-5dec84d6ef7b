<view class="goods">
  <van-card
    desc="{{ desc }}"
    num="{{ num }}"
    thumb="{{ thumb }}"
    thumb-link="{{ url }}"
    thumb-mode="aspectFit"
    tag="{{ imgTag.text || '' }}"
    price="{{ price }}"
    origin-price="{{ originPrice }}"
    currency=""
    thumb-class="goods-thumb"
  >
    <view
      slot="title"
      class="card__title"
      catch:tap="handleTitleClick"
    >
      <view
        wx:if="{{ isShipped }}"
        class="card__title__shipped-tag"
      >
        已发货
      </view>
      <view class="card__title__goods-name">{{ title }}</view>
    </view>

    <view slot="tags">
      <van-tag
        wx:for="{{ tags }}"
        wx:key="text"
        round
        color="#fde6e9"
        text-color="#ee0a24"
        type="{{ item.type }}"
        plain="{{ item.plain }}"
        class="card__tag"
      >{{ item.text }}</van-tag>
    </view>

    <view slot="bottom">
      <view class="card__tax" wx:if="{{ taxTips }}">
        {{ taxTips }}
      </view>

      <goods-progress process-info="{{ processInfo }}" />
    </view>

    <view slot="footer" class="card__btn-cell">
      <van-button
        wx:for="{{ btns }}"
        wx:key="text"
        round="{{ item.round }}"
        size="small"
        type="{{ item.type }}"
        plain="{{ item.plain }}"
        id="goods-btn-{{ item.value }}"
        class="card__btn"
        data-type="{{ item.value }}"
        catch:tap="handleBtnClick"
      >
        {{ item.text }}
      </van-button>
    </view>
  </van-card>

  <van-cell-group wx:if="{{ preSaleDate }}" border="{{ false }}">
    <van-cell
      title="发货时间"
      value="{{ preSaleDate }}"
      custom-class="card__extra"
      value-class="van-c-gray-darker"
      border="{{ false }}"
    />
  </van-cell-group>

  <!-- 商品留言弹窗 -->
  <message-popup
    message="{{ message }}"
    show="{{ showMessagePopup }}"
    bind:close="toggleMessagePopup"
  />

  <diff-price
    wx:if="{{ refundOrderItem && refundOrderItem.refundFee }}"
    refund-order-item="{{ refundOrderItem }}"
  />
</view>
