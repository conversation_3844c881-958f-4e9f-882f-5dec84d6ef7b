@import "shared/common/css/helper/index.wxss";

.presale-steps {
  padding: 8px 16px;
  margin-top: 10px;
  background: #fff;
}

.presale-steps__item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 26px;
  font-size: 14px;
  color: #666;
  margin: 8px 0;
}

.presale-steps__item:not(:first-of-type)::before {
  content: '';
  position: absolute;
  left: 5px;
  bottom: 24px;
  transform: translateX(-50%);
  width: 1px;
  height: 10px;
  background: #e5e5e5;
}

.presale-steps__item-content {
  display: flex;
  align-items: center;
}

.presale-steps__item__dot {
  margin-right: 12px;
  width: 6px;
  height: 6px;
  border: 2px solid #fff;
  border-radius: 100%;
  background: #e5e5e5;
}
.presale-steps__item__title {
  font-size: 14px;
  color: #646566;
}

.presale-steps__item__status {
  font-size: 14px;
  color: #323233;
}

.presale-steps__item-info-icon {
  vertical-align: middle;
  padding: 4px;
  position: relative;
  top: 1px;
}

.presale-steps__item-info-icon:active {
  color: #000;
}

.presale-steps__item__price text {
  display: inline-block;
  text-align: right;
  color: #232333;
}

.presale-steps__item--arrived .presale-steps__item__dot {
  background: #ff7272;
  border: 2px solid #ffd3d3;
}

.presale-steps__item--arrived::before {
  background: #ffd3d3 !important;
}

.pay-ump-detail-dialog {
  padding: 0 16px 7px 16px;
  display: flex;
  width: 84%;
  flex-flow: column nowrap;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: 12px;
}

.pay-ump-detail-dialog__title {
  padding: 11px;
  color: #323233;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  text-align: center;
}

.pay-ump-detail-dialog__main {
  width: 100%;
  padding: 14px 0;
}

.pay-ump-detail-dialog__item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 14px;
  font-weight: 400;
}

.pay-ump-detail-dialog__btn {
  width: 100% !important;
  height: 36px !important;
  line-height: 36px !important;
  border-radius: 18px !important;
}

.agreement {
  max-height: 60vh;
  padding: 10px 30px 20px;
  overflow-y: auto;
  font-size: 12px;
  color: #666;
  white-space: pre-line;
}

.agreement__paragraph {
  margin-top: 10px;
}

.agreement__title {
  font-weight: bold;
}

.presale-steps__bottom-wrapper {
  margin: 0 16px 16px;
  border-radius: 999px;
}

.presale-steps__bottom-btn::after {
  border: none !important;
}

.presale-steps__bottom-btn::before {
  border: none !important;
}

.presale-steps__bottom-btn {
  border: none !important;
}
