<wxs module="filter">
  module.exports.formatMoney = function(v) {
    return '¥' + (v / 100).toFixed(2);
  }
</wxs>

<view class="presale-steps">
  <view
    wx:for="{{ steps }}"
    wx:key="index"
    class="presale-steps__item {{ item.phaseArrived ? 'presale-steps__item--arrived' : '' }}"
  >
    <view class="presale-steps__item-content">
      <view class="presale-steps__item__dot"></view>
      <text class="presale-steps__item__title">{{ item.phaseDesc }}</text>
      <text class="presale-steps__item__status">（{{ item.phasePayStatusDesc }}）</text>
      <van-icon wx:if="{{ item.phase === 1 }}" class="icon" color="#1989fa" name="question-o" bind:click="onShowAgreement" />
    </view>
    <view class="presale-steps__item__price van-c-gray-darker">
      <!-- 支付优惠说明 -->
      <van-icon
        wx:if="{{ item.payUmpDiscountMoney > 0 }}"
        class="presale-steps__item-info-icon"
        name="info-o"
        data-phase="{{ item.phase }}"
        bind:tap="onShowPayUmpDetail"
      />
      <text style="min-width: {{ priceSpanWidth }}px">{{ filter.formatMoney(item.buyerRealPay) }}</text>
    </view>
  </view>
</view>

<!-- 支付优惠详情弹窗 -->
<van-popup
  show="{{ showPayUmpDetail }}"
  bind:close="onClosePayUmpDetail"
  custom-class="pay-ump-detail-dialog"
>
  <view class="pay-ump-detail-dialog__title">
    价格说明
  </view>
  <view class="pay-ump-detail-dialog__main">
    <view class="pay-ump-detail-dialog__item">
      <text class="pay-ump-detail-dialog__left">
        {{ currentPhase.phase === 1 ? '定金' : '尾款' }}应付
      </text>
      <text class="pay-ump-detail-dialog__right">
        ￥{{ currentPhase.currentPrice }}
      </text>
    </view>
    <view class="pay-ump-detail-dialog__item">
      <text class="pay-ump-detail-dialog__left">
        支付优惠
      </text>
      <text class="pay-ump-detail-dialog__right">
        -￥{{ currentPhase.payUmpDiscountMoney }}
      </text>
    </view>
  </view>
  <van-button
    round
    block
    type="danger"
    size="large"
    custom-class="pay-ump-detail-dialog__btn theme-button"
    style="width: 100%"
    bind:tap="onClosePayUmpDetail"
  >
    知道了
  </van-button>
</van-popup>

<!-- 定金不退协议 -->
<van-action-sheet title="定金不退协议" show="{{ showAgreement }}" bind:close="onCloseAgreement">
  <view class="agreement">
    <view wx:for="{{ agreement }}" wx:key="index" wx:for-item="block" class="agreement__paragraph">
      <view class="agreement__title">{{ block.title }}</view>
      <view wx:for="{{ block.words }}" wx:key="index" wx:for-item="line">{{ line }}</view>
    </view>
  </view>
  <theme-view
    bg="main-bg"
    border="main-bg"
    color="main-text"
    custom-class="presale-steps__bottom-wrapper"
  >
    <van-button
      type="danger"
      size="large"
      color="inherit"
      custom-class="presale-steps__bottom-btn"
      bind:click="onCloseAgreement"
    >我知道了</van-button>
  </theme-view>
</van-action-sheet>
