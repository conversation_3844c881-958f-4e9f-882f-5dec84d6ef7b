import WscComponent from 'pages/common/wsc-component/index';
import money from '@youzan/weapp-utils/lib/money';
import agreement from './agreement';

WscComponent({
  properties: {
    steps: {
      type: Array,
      value: [],
      observer(val) {
        // 计算价格占用宽度
        let priceSpanWidth = 0;
        if (val && val.length > 0) {
          priceSpanWidth = 12 + this.formatPrice(val.sort((item1, item2) => {
            return item2.buyerRealPay - item1.buyerRealPay;
          })[0].buyerRealPay).toString().length * 7;
        }
        this.setYZData({
          priceSpanWidth
        });
      }
    }
  },

  data: {
    agreement,
    showAgreement: false,
    showPayUmpDetail: false,
    currentPhase: {},
    priceSpanWidth: 0
  },

  methods: {
    formatPrice(v) {
      return (v / 100).toFixed(2).toString();
    },

    onShowAgreement() {
      this.setYZData({
        showAgreement: true
      });
    },

    onCloseAgreement() {
      this.setYZData({
        showAgreement: false
      });
    },

    onClosePayUmpDetail() {
      this.setYZData({
        showPayUmpDetail: false
      });
    },

    onShowPayUmpDetail(event) {
      const { phase = 1 } = event.detail;
      const currentPhase = this.data.steps[phase - 1];
      if (currentPhase) {
        this.setYZData({
          showPayUmpDetail: true,
          currentPhase: {
            ...currentPhase,
            phase,
            currentPrice: money(currentPhase.realPrice).toYuan(),
            payUmpDiscountMoney: money(currentPhase.payUmpDiscountMoney).toYuan()
          }
        });
      }
    }
  }
});
