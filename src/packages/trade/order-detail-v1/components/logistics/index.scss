@import '~mixins/index.scss';

.order-logistics {
  display: flex;
  position: relative;
  padding-left: 22px;
}

.order-logistics__icon {
  position: absolute;
  left: 0;
  top: 2px;
  height: 16px;
  font-size: 0;
  color: $text-color;
}

.order-logistics__desc {
  text-align: left;
}

.order-logistics__desc__detail {
  font-size: 14px;
  line-height: 20px;
  color: $text-color;
  font-weight: bold;
  white-space: pre-line;
}

.order-logistics__desc__time {
  margin-top: 10px;
  font-size: 12px;
  line-height: 16px;
  color: $gray-darker;
}

.van-hairline--top {
  position: relative;

  &::after {
    content: ' ';
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    transform-origin: center;
    top: 0;
    left: 16px;
    right: 16px;
    bottom: auto;
    transform: scaleY(0.5);
    border-bottom: 1px solid #ebedf0;
  }
}
