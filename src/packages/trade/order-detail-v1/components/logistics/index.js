import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'shared/utils/open-web-view';
import * as processUtils from '../../common/process';

WscComponent({
  properties: {
    orderNo: String,
    detailUrl: String,
    detail: String,
    time: String,
    showPeriodBuyDelivery: Boolean
  },

  methods: {
    onClick() {
      if (!this.data.detailUrl) {
        return;
      }

      if (this.data.showPeriodBuyDelivery) {
        openWebView(this.data.detailUrl);
      } else {
        processUtils.toExpressPage();
      }
    }
  }
});
