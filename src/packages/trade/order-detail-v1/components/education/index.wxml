<view class="order-detail__education">
  <!-- 课程信息 -->
  <van-cell-group
    wx:if="{{ education.courseAttend && education.courseAttend.courseTime }}"
    class="order-detail__course-info"
  >
    <van-cell
      class="order-detail__education-cell"
      wx:if="{{ education.courseAttend.courseTime }}"
      title="上课时间"
      value="{{ education.courseAttend.courseTime }}"
    />
    <van-cell
      class="order-detail__education-cell"
      wx:if="{{ education.courseAttend.address }}"
      title="上课地点"
      value="{{ education.courseAttend.address }}"
    />
  </van-cell-group>
  <!-- 学员信息 -->
  <van-cell-group
    wx:if="{{ education.courseAttend && education.courseAttend.courseTime }}"
    class="order-detail__student-info"
  >
    <block wx:if="{{ attributes.length > 0 }}">
      <block wx:for="{{ attributes }}">
        <van-cell
          wx:if="{{ item.isValid }}"
          title="{{ item.name }}"
          value="{{ item.value }}"
        />
      </block>
    </block>
    <block wx:else>
      <van-cell wx:if="{{ education.name }}" class="order-detail__education-cell" title="学员姓名" value="{{ education.name }}" />
      <van-cell wx:if="{{ education.phoneNumber }}" class="order-detail__education-cell" title="手机号" value="{{ education.phoneNumber }}" />
      <van-cell wx:if="{{ education.wechatAccount }}" class="order-detail__education-cell" title="微信号" value="{{ education.wechatAccount }}" />
      <van-cell wx:if="{{ education.gender }}" class="order-detail__education-cell" title="性别" value="{{ education.gender === 1 ? '男' : '女' }}" />
      <van-cell wx:if="{{ education.bornDate }}" class="order-detail__education-cell" title="生日" value="{{ education.bornDate }}" />
      <van-cell wx:if="{{ education.grade }}" class="order-detail__education-cell" title="年级" value="{{ education.grade }}" />
      <van-cell wx:if="{{ education.address }}" class="order-detail__education-cell" title="联系地址" value="{{ education.address }}" />
    </block>
  </van-cell-group>
</view>
