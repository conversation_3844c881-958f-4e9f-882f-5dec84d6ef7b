import WscComponent from 'pages/common/wsc-component/index';
import { calculateDistance } from '@youzan/utils/number/distance';

WscComponent({
  properties: {
    verifyAddressInfo: {
      type: Object,
      value: {}
    }
  },

  attached() {
    this.setDistance();
  },

  methods: {
    setDistance() {
      const {
        lat,
        lng
      } = this.data.verifyAddressInfo;

      if (lat && lng) {
        wx.getLocation({
          type: 'gcj02',
          success: ({ latitude = '', longitude = '' }) => {
            if (latitude && longitude) {

              calculateDistance(latitude, longitude, lat, lng).then(distance => {
                this.setYZData({
                  distance
                });
              }).catch(() => {});
            }
          }
        });
      }
    },

    onLocationIconClick() {
      const {
        lng,
        lat,
        name,
        address
      } = this.data.verifyAddressInfo;

      if (lng && lat) {
        wx.openLocation({
          name,
          address,
          latitude: lat,
          longitude: lng
        });
      }
    },
  }
});
