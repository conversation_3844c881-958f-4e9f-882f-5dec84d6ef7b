@import "helpers/index.wxss";

.address {
  padding: 10px 15px;
  margin-top: 10px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #fff;
}

.address__content {
  flex: 1;
}

.address__content__name {
  color: #111;
  font-weight: bold;
  font-size: 14px;
  line-height: 20px;
}

.address__content__address {
  margin-top: 10px;
  color: #909090;
  font-size: 12px;
  line-height: 16px;
}

.address__content__distance {
  margin-top: 5px;
  color: #999;
  font-size: 10px;
  line-height: 14px;
}

.address__map__icon {
  margin: 0 5px 0 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 24px;
  width: 38px;
  background: url('https://img01.yzcdn.cn/public_files/2018/12/17/b3caca272aa807ce5b3056ea043df7be.png') no-repeat right center;
  background-size: 14px 18px;
}