<view class="address">
  <view class="address__content">
    <view class="address__content__name">{{ verifyAddressInfo.name }}</view>
    <view class="address__content__address">{{ verifyAddressInfo.address }}</view>
    <view
      wx:if="{{ distance }}"
      class="address__content__distance"
    >
      距离{{ distance }}m
    </view>
  </view>

  <view
    wx:if="{{ verifyAddressInfo.lat && verifyAddressInfo.lng }}"
    class="address__map__icon van-hairline--left"
    catch:tap="onLocationIconClick"
  />
</view>