$popover-bg-color: #e5e5e5;
$popover-font-color: #fff;
$popover-border-radius: 3px;
$popover-border-width: 5px;

.van-popover {
  position: absolute;
  left: 0;
  top: 0;
  color: $popover-font-color;
  border: 1px solid #e5e5e5;
  border-radius: $popover-border-radius;
  z-index: 500;
  transform: translate(-100%, -50%);
}

.van-popover-arrow {
  position: absolute;
  width: 0;
  height: 0;
}

.van-popover-arrow-up {
  border-left: $popover-border-width solid transparent;
  border-right: $popover-border-width solid transparent;
  border-bottom: $popover-border-width solid $popover-bg-color;
  left: 50%;
  transform: translateX(-50%);
  top: -$popover-border-width;
}

.van-popover-arrow-down {
  border-left: $popover-border-width solid transparent;
  border-right: $popover-border-width solid transparent;
  border-top: $popover-border-width solid $popover-bg-color;
  left: 50%;
  transform: translateX(-50%);
  bottom: -$popover-border-width;
}

.van-popover-arrow-left {
  border-top: $popover-border-width solid transparent;
  border-bottom: $popover-border-width solid transparent;
  border-right: $popover-border-width solid $popover-bg-color;
  top: 50%;
  transform: translateY(-50%);
  left: -$popover-border-width;
}

.van-popover-arrow-right {
  border-top: $popover-border-width solid transparent;
  border-bottom: $popover-border-width solid transparent;
  border-left: $popover-border-width solid $popover-bg-color;
  top: 50%;
  transform: translateY(-50%);
  right: -$popover-border-width;
}

.postpone-action {
  display: block !important;
  border: 0 !important;
  border-bottom: 1px solid #eee !important;
}

.postpone-action--last {
  display: block !important;
  border: 0 !important;
}
