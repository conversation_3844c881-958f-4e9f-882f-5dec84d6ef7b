<view 
  wx:if="{{ show }}"
  class="van-popover" 
  style="top: {{ top }}px; left: {{ left }}px;" 
  bindblur="onBlur"
>
  <view class="van-popover-arrow van-popover-arrow-right"></view>
  <van-button 
    size="small" 
    custom-class="postpone-action" 
    data-key="postponeDeliveryUrl" 
    bind:click="onClick"
  >顺延</van-button>
  <van-button 
    size="small" 
    custom-class="postpone-action--last" 
    data-key="cancelPostponeDeliveryUrl" 
    bind:click="onClick"
  >取消顺延</van-button>
</view>
