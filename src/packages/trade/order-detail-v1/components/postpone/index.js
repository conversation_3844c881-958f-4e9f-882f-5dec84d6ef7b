import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'shared/utils/open-web-view';

WscComponent({
  properties: {
    goodsList: Array
  },

  data: {
    left: 0,
    top: 0,
    show: false
  },

  created() {
    this.on('show:postpone', ({ position, itemId }) => {
      this.setYZData({
        ...position,
        show: true
      });
      this.itemId = itemId;
    });

    this.on('hide:postpone', () => {
      this.setYZData({
        show: false
      });
    });
  },

  methods: {
    onClick(event) {
      const { key } = event.target.dataset;
      const item = this.data.goodsList.find(item => item.itemId === this.itemId);
      if (item && item[key]) {
        openWebView(item[key]);
        this.setYZData({ show: false });
      }
    }
  }
});
