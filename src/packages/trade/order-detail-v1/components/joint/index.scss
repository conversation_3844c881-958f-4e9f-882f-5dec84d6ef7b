@import '~mixins/index.scss';

.joint {
  display: flex;
  margin-top: 10px;
  padding: 0 16px;
  flex-direction: column;
  background-color: #fff;

  &__title {
    display: flex;
    width: 100%;
    height: 40px;
    font-size: 14px;
    color: #323233;
    font-weight: 500;
    justify-content: flex-start;
    align-items: center;
  }

  &__container {
    display: flex;
    height: 68px;
    align-items: center;
    justify-content: space-between;

    &__participants {
      display: flex;
      position: relative;
      height: 100%;

      .participant {
        display: flex;
        position: absolute;
        top: 16px;
        width: 36px;
        height: 36px;
        justify-content: center;
        align-items: center;
        background-color: #fafafa;
        border: 2px solid #fff;
        border-radius: 50%;
        box-sizing: border-box;

        &__img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }

        &__icon {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;

          &--circle {
            width: 4px;
            height: 4px;
            background-color: #dcdee0;
            border-radius: 50%;
          }
        }
      }
    }

    &__opts {
      display: flex;
      font-size: 14px;
      color: #646566;
      align-items: center;

      &--icon {
        margin-left: 8px;
      }
    }
  }
}

/* 设计给的前面的覆盖一部分后面的，只能用绝对定位的方式了 */
.participant0 {
  left: 0;
  z-index: 5;
}

.participant1 {
  left: 30px;
  z-index: 4;
}

.participant2 {
  left: 60px;
  z-index: 3;
}

.participant3 {
  left: 90px;
  z-index: 2;
}

.participant4 {
  left: 120px;
  z-index: 1;
}

.participant5 {
  left: 150px;
  padding: 0 4px;
  background-color: #fff;
  border-color: #dcdee0;
  z-index: 0;
}
