<view class="joint" bindtap="onJumpDetail">
  <view class="joint__title">拼单</view>
  <view class="joint__container">
    <view class="joint__container__participants">
      <view
        class="participant participant{{index}}"
        wx:for="{{ data }}"
        wx:key="imgUrl">
        <image class="participant__img" src="{{ item.imgUrl }}" />
      </view>
      <view 
        class="participant participant5"
        wx:if="{{ dataMore }}">
        <view class="participant__icon">
          <view class="participant__icon--circle"></view>
        </view>
        <view class="participant__icon">
          <view class="participant__icon--circle"></view>
        </view>
        <view class="participant__icon">
          <view class="participant__icon--circle"></view>
        </view>
      </view>
    </view>
    <view class="joint__container__opts">
      <block wx:if="{{ status === 100 }}">
        分享账单
        <van-icon
          custom-class="joint__container__opts--icon"
          name="arrow" 
          size="12px"
          color="#646566"
        />
      </block>
      <block wx:elif="{{ status === 99 }}">拼单已关闭</block>
    </view>
  </view>
</view>

