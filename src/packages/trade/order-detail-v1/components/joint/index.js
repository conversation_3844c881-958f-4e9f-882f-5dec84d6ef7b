import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    participants: Array,
    status: Number,
    jointId: String,
  },

  data: {
    data: [],
    dataMore: false,
  },

  observers: {
    participants(val) {
      // 默认最多展示5个头像，超过展示省略号
      this.setYZData({
        data: val.slice(0, 5),
        dataMore: val.length > 5,
      });
    },
  },

  methods: {
    onJumpDetail() {
      const { status, jointId } = this.properties;
      if (+status === 100) {
        wx.navigateTo({
          url: `/packages/retail/order-pool/detail/index?orderPoolId=${jointId}`,
        });
      }
    },
  },
});
