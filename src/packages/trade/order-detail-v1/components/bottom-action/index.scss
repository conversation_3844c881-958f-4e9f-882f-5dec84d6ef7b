@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.bottom-action {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
  background: #fff;

  .btns-wrapper {
    padding: 10px 16px;
    overflow: hidden;
  }

  &.iPhone-X::after {
    @include iphone-x-after($iphone-x-bottom);
  }
}

.action__btn {
  float: right;
  margin-left: 12px;
  font-size: 14px !important;
}

.action__btn--more {
  float: right;
  margin-left: 10px;
  font-size: 14px !important;
  min-width: 0 !important;
  border: none !important;
}

.van-hairline--top {
  position: relative;

  &::after {
    content: ' ';
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    transform-origin: center;
    top: 0;
    left: 16px;
    right: 16px;
    bottom: auto;
    transform: scaleY(0.5);
    border-top: 1px solid #ebedf0;
  }
}
