import { VanxComponent } from 'pages/common/wsc-component/index';
import { isNewIphone } from 'shared/utils/browser/device-type';
import store from '../../store/index';
import config from '../../common/config';
import * as processUtils from '../../common/process';
import asyncEvent from 'shared/utils/async-event';

const LIMIT_NUM = 3;
const BTN = config.bottom;
const app = getApp();

VanxComponent({
  store,
  properties: {
    orderNo: String,
    orderExtra: {
      type: Object,
      observer: 'checkIsTradeComponent3'
    },
    btns: {
      type: Array,
      observer: 'setBtns'
    },
    guaranteeOrderInfo: Object,
    isTradeComponent3: false,
  },

  data: {
    deviceType: isNewIphone() ? 'iPhone-X' : '',
  },

  methods: {
    checkIsTradeComponent3() {
      let BIZ_ORDER_ATTRIBUTE = {};
      try {
        BIZ_ORDER_ATTRIBUTE = JSON.parse(
          this.data.orderExtra?.BIZ_ORDER_ATTRIBUTE || '{}'
        );
        this.setYZData({
          isTradeComponent3:
            BIZ_ORDER_ATTRIBUTE.WX_CHANNELS_COMPONENT_VERSION ===
            
            'TRADE_COMPONENT_3_0',
        });

      } catch (error) {
        this.setYZData({ isTradeComponent3: false });
      }
    },

    // 控制底部按钮的数量，超过展示"更多"
    setBtns() {
      const bottomBtns = this.data.btns.slice(0, LIMIT_NUM);

      if (this.data.btns.length > LIMIT_NUM) {
        bottomBtns.push(BTN.more);
      }

      this.setYZData({ bottomBtns });
    },

    toggleActionSheet() {
      this.setYZData({
        showActionSheet: !this.data.showActionSheet
      });
    },

    // 处理 action 选中
    handleActionSelect({ detail }) {
      this.toggleActionSheet();
      this.dispatchClick(detail);
    },

    // 打开分享
    openShareSheet() {
      this.$dispatch('SHOW_SHARE_SHEET');
    },

    // 处理底部按钮点击
    handleBottomBtnClick(e) {
      asyncEvent.triggerAsync
        .apply(getApp(), [
          'beforeBottomBtnClick',
          {
            type: e.currentTarget.dataset.btn.value,
          },
        ])
        .then(() => {
          this.dispatchClick(e.currentTarget.dataset.btn);
        })
        .catch(() => {});
    },

    // 分发点击事件
    dispatchClick(btn) {
      const { value, link } = btn;
      const { payWay } = this.data.guaranteeOrderInfo;
      if (link) {
        wx.navigateTo({ url: link });
      } else {
        switch (value) {
          case BTN.evaluate.value:
          case BTN.viewEvaluate.value:
            processUtils[value]();
            break;
          case BTN.confirm.value:
            this.confirmReceive('', payWay);
            break;
          case BTN.confirmHotel.value:
            this.confirmReceive(BTN.confirmHotel.value, payWay);
            break;
          case BTN.more.value:
            this.toggleActionSheet();
            break;
          case BTN.share.value:
            this.openShareSheet();
            break;
          default:
            break;
        }
      }
    },

    // 确认收货
    confirmReceive(type = '', payWay) {
      processUtils
        .confirmReceive(type, payWay)
        .then(() => {
          if (type === 'confirmHotel') {
            // 酒店商品 请求成功后打点
            app.logger &&
              app.logger.log({
                et: 'click', // 事件类型
                ei: 'click_checkin', // 事件标识
                en: '点击入住', // 事件名称
                si: app.getKdtId()
              });
          }

          this.triggerEvent('refresh');
        })
        .catch(() => {
          this.triggerEvent('refresh');
        });
    }
  }
});
