<view class="bottom-action {{ deviceType }}" wx:if="{{ !isTradeComponent3 }}">
  <view class="btns-wrapper van-hairline--top">
    <van-button
      wx:for="{{ bottomBtns }}"
      wx:key="value"
      plain
      round
      size="small"
      type="{{ item.type }}"
      custom-class="{{ item.value === 'more' ? 'action__btn--more' : 'action__btn' }}"
      data-btn="{{ item }}"
      catch:tap="handleBottomBtnClick"
    >{{ item.name }}</van-button>

    <van-action-sheet
      show="{{ showActionSheet }}"
      actions="{{ btns }}"
      cancel-text="取消"
      bind:close="toggleActionSheet"
      bind:select="handleActionSelect"
    />
  </view>
</view>
