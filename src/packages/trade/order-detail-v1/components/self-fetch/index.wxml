<view class="self-fetch">
  <take-goods-code order-mark="{{ orderMark }}" take-goods-code="{{ takeGoodsCode }}" />
  <view class="self-fetch__cell">
    <view class="self-fetch__title">提货凭证 {{ selfFetchInfo.name ? "(" + selfFetchInfo.name + ")" : '' }}</view>
    <view class="self-fetch__addr-cell">
      <view class="self-fetch__addr-cell__addr">{{ selfFetchInfo.address }}</view>
      <view wx:if="{{ selfFetchInfo.lng && selfFetchInfo.lat }}" class="self-fetch__addr-cell__icon van-hairline--left" catch:tap="onLocationIconClick" />
    </view>
    <view class="self-fetch__content {{ selfFetchInfo.qrcode ? '' : 'self-fetch__content--without-qacode' }}" catch:tap="onContactContentClick">
      <view wx:if="{{ selfFetchInfo.qrcode }}" class="self-fetch__content__qrcode" style="background-image: url({{ selfFetchInfo.qrcode }})" />
<!--      <van-loading class="self-fetch__content__loading" wx:else />-->
      <view class="self-fetch__content__info">
        <view wx:if="{{ selfFetchInfo.qrcode }}" class="self-fetch__content__info__code">
          提货码：
          <text>{{ selfFetchInfo.fetchNo || '正在生成中...' }}</text>
        </view>
        <view wx:if="{{ selfFetchInfo.fetcher }}" class="self-fetch__content__info__fetcher">
          提货人：
          <text>{{ selfFetchInfo.fetcher }}</text>
        </view>
        <view wx:if="{{ selfFetchInfo.time }}" class="self-fetch__content__info__time">
          预约时间：
          <text>{{ selfFetchInfo.time }}</text>
        </view>
      </view>
    </view>
    <view wx:if="{{ selfFetchInfo.tel }}" class="self-fetch__action">
      <view class="self-fetch__action__call" catch:tap="onContactBtnClick">
        <view class="self-fetch__action__call__icon" />
        <text>联系提货点</text>
      </view>
    </view>
  </view>
</view>
