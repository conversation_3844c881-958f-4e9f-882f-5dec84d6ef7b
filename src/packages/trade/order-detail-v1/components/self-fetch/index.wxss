@import "shared/common/css/helper/index.wxss";

.self-fetch {
  margin-top: 10px;
}

.self-fetch__cell {
  background: #fff;
  padding: 15px;
}

.self-fetch__title {
  color: #323233;
  font-size: 14px;
  font-weight: 500;
}

.self-fetch__addr-cell {
  margin-top: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.self-fetch__addr-cell__addr {
  font-size: 12px;
  line-height: 16px;
  color: #999;
}

.self-fetch__addr-cell__icon {
  margin-left: 12px;
  height: 24px;
  width: 38px;
  background: url('https://img01.yzcdn.cn/public_files/2018/12/17/b3caca272aa807ce5b3056ea043df7be.png') no-repeat right center;
  background-size: 14px 18px;
}

.self-fetch__content {
  display: flex;
  align-items: stretch;
  margin-top: 15px;
  padding: 15px;
  min-height: 40px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.self-fetch__content--without-qacode {
  padding: 0;
  box-shadow: none;
  border-radius: 0;
}

.self-fetch__content__qrcode {
  height: 80px;
  width: 80px;
  margin-right: 15px;
  background-size: 100%;
  background-position: center;
}

.self-fetch__content__loading {
  height: 80px;
  width: 80px;
  margin-right: 15px;
  background-size: 120%;
  background-position: center;
  text-align: center;
  line-height: 80px;
}

.self-fetch__content__info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 12px;
  color: #646566;
}

.self-fetch__content__info text {
  color: #323233;
}

.self-fetch__content__info__code text {
  font-size: 14px;
  font-weight: bold;
  color: #323233;
}

.self-fetch__action {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.self-fetch__action__call {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0 8px;
  min-width: 60px;
  font-size: 14px;
  line-height: 28px;
  color: #323233;
  background-color: #fff;
  border: 1px solid #eee;
  box-sizing: border-box;
  border-radius: 15px;
}

.self-fetch__action__call__icon {
  margin-right: 6px;
  display: inline-block;
  height: 12px;
  width: 12px;
  background: url('https://img01.yzcdn.cn/public_files/2018/12/17/c7e95ec374fd995d4dd7694d7345cebb.png') no-repeat center center;
  background-size: 12px;
}
