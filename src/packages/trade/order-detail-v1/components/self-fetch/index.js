import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'shared/utils/open-web-view';

WscComponent({
  properties: {
    orderNo: String,
    selfFetchInfo: {
      type: Object,
      value: {},
    },
    orderMark: {
      type: String,
      value: '',
    },
    takeGoodsCode: {
      type: String,
      value: '',
    },
  },

  methods: {
    onLocationIconClick() {
      const { lng, lat, shopName, address } = this.data.selfFetchInfo;

      if (lng && lat) {
        wx.openLocation({
          address,
          latitude: lat,
          longitude: lng,
          name: shopName,
        });
      }
    },

    onContactContentClick() {
      const { selfFetchUrl, qrcode } = this.data.selfFetchInfo;
      if (selfFetchUrl && qrcode) {
        openWebView(selfFetchUrl, {
          title: '自提订单提货凭证',
        });
      }
    },

    onContactBtnClick() {
      wx.makePhoneCall({
        phoneNumber: this.data.selfFetchInfo.tel,
      });
    },
  },
});
