import args from '@youzan/weapp-utils/lib/args';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import WscComponent from 'pages/common/wsc-component/index';
import navigate from 'shared/utils/navigate';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();
const PROMO_TYPE_ACTION = {
  tradeincard({ orderNo, type = 'promocard' }) {
    navigate.navigate({
      url: `/packages/user/coupon/detail/index?orderNo=${orderNo}&type=${type}&from=orderDetail`,
    });
  },

  promocode(options) {
    PROMO_TYPE_ACTION.tradeincard({
      ...options,
      type: 'promocode',
    });
  },

  couponpackage(options) {
    PROMO_TYPE_ACTION.tradeincard({
      ...options,
      type: 'couponpackage',
    });
  },

  seller() {
    navigate.navigate({
      url: '/packages/salesman/tutorial/index',
    });
  },

  wheel(options) {
    const detailUrl = encodeURIComponent(options.detailUrl);
    navigate.navigate({
      url: `/pages/common/webview-page/index?src=${detailUrl}`,
      success: () => {},
      error: () => {
        wx.showToast({
          icon: 'none',
          title: '打开失败',
        });
      },
    });
  },

  seniorseller() {
    PROMO_TYPE_ACTION.seller();
  },

  feature({ detailUrl }) {
    const match = detailUrl.match(/feature\/([^?/]+)/);
    navigate.navigate({
      url: `/packages/home/<USER>/index?alias=${match[1]}`,
    });
  },

  present({ detailUrl, present }) {
    const match = detailUrl.match(/goods\/([^?/]+)/);
    navigate.navigate({
      url: `/packages/goods/present/index?alias=${match[1]}&type=present&activityId=${present}`,
    });
  },
};

const PROMO_TYPE_ACTION_KEYS = Object.keys(PROMO_TYPE_ACTION);

WscComponent({
  properties: {
    paidPromotion: {
      type: Object,
      observer: 'paidPromotionObserver',
    },
    orderNo: {
      type: String,
    },
  },

  data: {
    PROMO_TYPE_ACTION_KEYS,
  },

  methods: {
    paidPromotionObserver(paidPromotion) {
      if (
        paidPromotion
        && Object.keys(PROMO_TYPE_ACTION).indexOf(paidPromotion.promotionType) > -1
      ) {
        app.logger
          && app.logger.log({
            et: 'view',
            ei: 'show_zhifuyouli',
            en: '支付有礼曝光',
            si: app.getKdtId(),
          });
      }
    },

    activityClickHandler() {
      app.logger
        && app.logger.log({
          et: 'click',
          ei: 'click_zhifuyouli',
          en: '支付有礼点击',
          si: app.getKdtId(),
        });
      const { orderNo } = this.data;
      const { detailUrl, promotionType } = this.data.paidPromotion;
      const action = PROMO_TYPE_ACTION[promotionType];
      action
        && action({
          orderNo,
          detailUrl,
          ...mapKeysCase.toCamelCase(args.getAll(detailUrl)),
        });
    },
  },
});
