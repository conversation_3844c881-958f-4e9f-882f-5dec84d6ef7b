<van-cell-group custom-class="service" border="{{ false }}">
  <!-- 配送方式 -->
  <van-cell wx:if="{{ showDeliveryType }}">
    <view class="express">
      <view class="express__col--left">
        <view class="express__way">配送方式</view>
        <view
          wx:if="{{ showExpressDescLink }}"
          class="express__desc-link"
          catch:tap="toggleExpressPopup"
        >同城配送－配送范围说明</view>
      </view>
      <view class="express__col--right">
        <view>{{ postage }}</view>
        <view>{{ expressType }}</view>
      </view>
    </view>
  </van-cell>

  <!-- 定时达 -->
  <van-cell
    wx:if="{{ showDeliveryTime }}"
    title="送达时间"
    title-width="80px"
    title-class="service-cell__title"
    value-class="service-cell__value"
    value="{{ deliveryTime }}"
  />

  <!-- 发票状态 -->
  <van-cell
    wx:if="{{ invoice.show }}"
    is-link="{{ !!invoice.url }}"
    url="{{ invoice.url }}"
    title="发票"
    title-class="service-cell__title"
  >
    <view class="invoice">
      <view>{{ invoice.value }}</view>
      <view wx:if="{{ invoice.tip }}" class="invoice__tip">{{ invoice.tip }}</view>
    </view>
  </van-cell>

  <!-- 买家留言 -->
  <van-cell
    wx:if="{{ showMessage }}"
    title="买家留言"
    title-width="80px"
    title-class="service-cell__title2"
    value-class="service-cell__value2"
    value="{{ buyerMemo }}"
    border="{{ false }}"
  />

  <!-- 查看优惠券 -->
  <van-cell
    wx:if="{{ showLookCoupon }}"
    title="查看已购优惠券"
    title-width="100px"
    title-class="service-cell__sub-title"
    value=" "
    is-link
    link-type="navigateTo"
    url="/packages/user/coupon/list/index?type=promocard"
    border="{{ true }}"
  />
</van-cell-group>

<!-- 配送方式 popup -->
<van-popup
  show="{{ showExpressPopup }}"
  round
  position="bottom"
  bind:close="toggleExpressPopup"
>
  <view class="express-popup">
    <view class="express-popup__title van-hairline--bottom">
      <text>同城配送－配送范围说明</text>
      <van-icon 
        class="express-popup__close"
        size="18px"
        name="close"
        catch:tap="toggleExpressPopup"
      />
    </view>

    <view class="express-popup__desc">
      <view class="express-popup__desc__title">配送范围：</view>
      <view class="express-popup__desc__content van-c-gray-darker">{{ localDeliveryDesc }}</view>
      <image
        wx:if="{{ localDeliveryImg }}"
        class="express-popup__desc__img"
        mode="aspectFit"
        src="{{ localDeliveryImg }}"
      />
    </view>
  </view>
</van-popup>
