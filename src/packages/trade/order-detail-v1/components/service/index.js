import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'shared/utils/open-web-view';

WscComponent({
  properties: {
    showDeliveryType: Boolean,
    showExpressDescLink: Boolean,
    postage: String,
    expressType: String,
    localDeliveryDesc: String,
    localDeliveryImg: String,
    showDeliveryTime: Boolean,
    showLookCoupon: Boolean,
    deliveryTime: String,
    invoice: Object,
    buyerMemo: String,
    showFreight: Boolean,
    freight: Object,
    showMessage: Boolean,
  },

  data: {
    showExpressPopup: false,
  },

  methods: {
    toggleExpressPopup() {
      this.setYZData({
        showExpressPopup: !this.data.showExpressPopup
      });
    },

    onFreightClick() {
      openWebView(this.data.freight.url, {
        query: {
          title: '退货包运费'
        }
      });
    }
  }
});
