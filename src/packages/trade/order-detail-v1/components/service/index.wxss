@import "shared/common/css/helper/index.wxss";

.service-cell__title {
  color:#969799 !important;
}

.service-cell__value {
  color: #323233 !important;
}

.service-cell__title2 {
  color:#969799 !important;
  margin-top: 6px;
  margin-bottom: 6px;
}

.service-cell__value2 {
  color: #323233 !important;
  margin-top: 6px;
  margin-bottom: 6px;
}

.service-cell__sub-title {
  color: rgba(0, 0, 0, 0.5) !important;
}

.service {
  margin-top: 10px;
}

.express {
  display: flex;
  justify-content: space-between;
  line-height: 1.6;
  font-size: 14px;
  color: #323233;
}

.express__col--left {
  text-align: left;
}

.express__col--right {
  text-align: right;
}

.express__way {
  color:#969799;
}

.express__desc-link {
  color: #38f;
}

.express-popup {
  width: 100vw;
  background: #fff;
}

.express-popup__title {
  position: relative;
  font-size: 16px;
  line-height: 44px;
  text-align: center;
}

.express-popup__close {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}

.express-popup__desc {
  max-height: 350px;
  padding: 12px 15px;
  line-height: 1.4;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.express-popup__desc::-webkit-scrollbar {
  display: none;
}

.express-popup__desc__title {
  font-size: 14px;
}

.express-popup__desc__content {
  font-size: 14px;
  word-break: break-all;
}

.express-popup__desc__img {
  width: 100%;
  margin-top: 12px;
}

.invoice {
  color: #111;
  line-height: 1.6;
}

.invoice__tip {
  font-size: 12px;
}
