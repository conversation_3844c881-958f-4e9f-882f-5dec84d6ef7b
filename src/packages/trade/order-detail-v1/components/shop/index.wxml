<view class="shop">
  <view class="shop__title" catch:tap="handleShopNameClick">
    <van-icon
      class="shop__title__icon"
      name="shop-o"
      size="18px"
    />
    {{ shopName }}
  </view>

  <!-- 商品卡片坑位 -->
  <slot></slot>

  <van-cell-group border="{{ false }}">
    <!-- 周期购 -->
    <block wx:if="{{ showPeriodBuyDelivery }}">
      <van-cell
        title="配送次数"
        title-width="80px"
        value-class="van-c-gray-darker"
        value="{{ periodTime }}"
      />

      <van-cell
        title="送达时间"
        title-width="80px"
        value-class="van-c-gray-darker"
        value="{{ periodDeliveryTime }}"
      />
    </block>

    <guarantee-order-freight-insurance-bar
      wx:if="{{ guaranteeOrderInfo }}"
      freight-url="{{ freight.url }}"
      kdtId="{{ kdtId }}"
      orderInfo="{{ guaranteeOrderInfo }}"
      bind:onShow="setGuarantee"
      bind:infoChange="handleInfoChange"
      bind:docsChange="handleDocsChange"
    />
    <!-- 合计 -->
    <van-cell border="{{ false }}" value-class="total-price__desc">
      商品小计：
      <text class="total-price__value">{{ totalPrice }}</text>
    </van-cell>
  </van-cell-group>
</view>
