import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';
import { handleUrlWithShopAutoEnter } from 'common-api/multi-shop/multi-shop-redirect';

WscComponent({
  properties: {
    shopName: String,
    totalPrice: String,
    showPeriodBuyDelivery: Boolean,
    periodTime: String,
    periodDeliveryTime: String,
    yzGuarantee: Boolean,
    orderNo: String,
    kdtId: Number,
    guaranteeOrderInfo: Object,
    freight: Object,
  },

  methods: {
    handleShopNameClick() {
      navigate.switchTab({
        url: handleUrlWithShopAutoEnter('/packages/home/<USER>/index'),
      });
    },
    setGuarantee(event) {
      this.triggerEvent('setGuarantee', event.detail);
    },
    handleInfoChange({ detail: info }) {
      this.triggerEvent('setYzGuaranteeInfo', info);
    },
    handleDocsChange({ detail: docs }) {
      this.triggerEvent('setYzGuaranteeDocs', docs);
    },
  },
});
