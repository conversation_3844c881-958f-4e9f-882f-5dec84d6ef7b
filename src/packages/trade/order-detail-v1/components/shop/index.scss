@import '~mixins/index.scss';

.shop {
  margin-top: 10px;
  background: #fff;
}

.shop__title {
  position: relative;
  padding: 16px 16px 16px 42px;
  font-size: 14px;
  font-weight: bold;
}

.shop__title__icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0;
}

.total-price__desc {
  color: $text-color !important;
}

.total-price__value {
  font-weight: bold;
  color: #f44;
}
