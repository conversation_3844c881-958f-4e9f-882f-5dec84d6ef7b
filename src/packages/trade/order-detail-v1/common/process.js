import args from '@youzan/weapp-utils/lib/args';
import api from './api';
import AsyncEvent from 'shared/utils/async-event';
import getStore from '../store';
import config from './config';

let orderNo;
let kdtId;

const { BTN } = config

const REFUND_ACTION = {
  TO_DETAIL: 1,
  TO_APPLY: 2,
  TOAST: 3,
  NOT_SUPPORT: 4,
};

export function initProcessContext({ orderNo: _orderNo, kdtId: _kdtId }) {
  orderNo = _orderNo;
  kdtId = _kdtId;
}

/**
 * 退款
 */
export function refund(itemId) {
  api.getRefundState({ kdtId, itemId, orderNo }).then((res = {}) => {
    const { redirectTarget, redirectUrl, reason = '服务器开小差了' } = res;
    if (redirectTarget === REFUND_ACTION.TOAST) {
      wx.showToast({
        title: reason,
        icon: 'none',
      });
    } else if (redirectUrl) {
      const query = {
        src: redirectUrl,
        title: '申请退款',
      };
      wx.navigateTo({
        url: args.add('/packages/trade/order/safe/index', query, true),
      });
    }
  });
}

/**
 * 确认收货
 */
export function confirmReceive(type = '', payWay) {
  const app = getApp();
  app.logger &&
    app.logger.log({
      et: 'click',
      ei: 'confirm_goods',
      en: '确认收货',
      params: {
        order_no: orderNo,
      },
    });

  const { state } = getStore();
  let content =
    payWay === 1
      ? '请确保已收到商品并检查无误。该订单货款已直接给商家，售后问题需要联系商家解决。'
      : '请确保已收到商品并检查无误。确认收货后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。';
  let title = '确认收货';
  if (state.receivingContent !== '') {
    content = state.receivingContent;
  }
  if (type === 'confirmHotel') {
    content =
      payWay === 1
        ? '请确保已入住，该订单货款已直接给商家，售后问题需要联系商家解决。'
        : '请确保已入住，确认入住后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。';
    title = '确认入住';
  }

  return new Promise((resolve, reject) => {
    wx.showModal({
      title,
      content,
      success: (res) => {
        if (res.confirm) {
          wx.showLoading();
          api
            .confirmReceive({
              orderNo,
            })
            .then(() => {
              const afterConfirmed = (tMsg) => {
                wx.hideLoading();
                tMsg &&
                  wx.showToast({
                    title: `${tMsg}成功`,
                    icon: 'success',
                  });
                resolve();
              };

              AsyncEvent.triggerAsync('confirm-mission')
                .then(([res = {}] = []) => {
                  afterConfirmed(res.show ? '' : title);
                })
                .catch(() => {
                  afterConfirmed(title);
                });
            })
            .catch((err) => {
              wx.hideLoading();
              wx.showToast({
                title: err.msg || '确认收货失败',
                icon: 'none',
              });
              reject();
            });
        }
      },
    });
  });
}

/**
 * 评价
 */
export function evaluate() {
  const url = `/packages/evaluation/order/create/index?order_no=${orderNo}`;
  wx.navigateTo({ url });
}

/**
 * 查看评价
 */
export function viewEvaluate() {
  const url = `/packages/evaluation/order/detail/index?order_no=${orderNo}`;
  wx.navigateTo({ url });
}

/**
 * 查看物流页面
 */
export function toExpressPage() {
  wx.navigateTo({
    url: `/packages/trade/order/express/index?orderNo=${orderNo}`,
  });
}

/**
 * 分享订单 - 展示分享订单弹框
 */
export function shareOrder() {
  getStore().dispatch('SHOW_SHARE_SHEET');
}

/**
 * 批量退款
 */
export function showBatchRefund() {
  wx.navigateTo({
    url: `/packages/trade/order/batch-refund/index?order_no=${orderNo}&kdt_id=${kdtId}`,
  });
}

/**
 * 收货自定义文案
 */
export function updateConfirmReceiveText(_content) {
  getStore().commit('UPDATE_RECEIVING_CONTENT', _content);
}

/**
 * 隐藏底部按钮
 */
export function setBottomBtnsDisplay(payload) {
  const { state, commit } = getStore();
  const { bottomBtns } = state;

  if ((payload.confirmHotel ?? '') !== '' && !payload.confirmHotel) {
    commit('UPDATE_BOTTOM_BTNS_DISPLAY', payload);
    if (payload.type) {
      payload.type.push('confirmHotel');
    } else {
      payload.type = [];
      payload.type.push('confirmHotel');
    }
  }
  commit('HIDDEN_BOTTOM_BTNS', payload);

  const bottomList = bottomBtns.filter((item) => {
    return !payload.type.includes(item.value);
  });

  commit('SET_ORDER', {
    bottomBtns: bottomList,
  });
}

/**
 * 隐藏申请退款按钮
 */
export function setRefundBtnsDisplay(goodsId) {
  const { state, commit, dispatch } = getStore();
  const { processGoodsId = [] } = state;
  processGoodsId.push(goodsId);
  const newProcessGoodsId = [...new Set(processGoodsId)];
  commit('SET_PROCESS_GOODS_ID', newProcessGoodsId);
  dispatch('HIDDEN_REFUND_BTN');
}

/**
 * 支持隐藏买家留言
 */
export function setShowMessage(display) {
  const { commit } = getStore();
  commit('SET_SHOW_MESSAGE', display);
}
