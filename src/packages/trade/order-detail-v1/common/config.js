import { EVALUATION } from 'shared/common/sensetive-words';

export default {
  // 商品按钮
  btn: {
    refund: {
      text: '申请退款',
      value: 'refund',
      type: 'danger',
      plain: true,
      round: true,
    },
    message: {
      text: '查看留言',
      value: 'message',
      type: 'default',
      plain: true,
      round: true,
    },
    postpone: {
      text: '我要顺延',
      value: 'postpone',
      type: 'default',
      plain: true,
      round: true,
    },
  },

  // 商品类型 tag
  tag: {
    presale: { text: '预售', type: 'danger' },
    periodBuy: { text: '周期购', type: 'danger' },
    enjoyBuy: { text: '随心订', type: 'danger' },
    fCode: { text: 'F码专享', type: 'danger' },
    bargain: { text: '砍价', type: 'danger' },
    inSourcing: { text: '内购价', type: 'danger' },
    seckill: { text: '秒杀', type: 'danger' },
    present: { text: '赠品', type: 'danger' },
    timelimitedDiscount: { text: '限时折扣', type: 'danger' },
    auction: { text: '降价拍', type: 'danger' },
    customerDiscount: { text: '会员折扣', type: 'danger' },
    plusBuy: { text: '加价购', type: 'danger' },
    crossBorder: { text: '海淘', type: 'danger' },
    exchangeCoupon: { text: '兑换券', type: 'danger' },
  },

  // 商品图片tag
  goodsImgTag: {},

  // 底部按钮
  bottom: {
    more: { name: '更多', value: 'more', type: 'default' },
    confirm: { name: '确认收货', value: 'confirm', type: 'primary' },
    confirmHotel: { name: '确认入住', value: 'confirmHotel', type: 'primary' },
    groupon: { name: '查看团详情', value: 'confirm', type: 'default' },
    coupon: { name: '领优惠券', value: 'coupon', type: 'default' },
    gift: { name: '查看礼包', value: 'gift', type: 'default' },
    evaluate: { name: '立即' + EVALUATION, value: 'evaluate', type: 'default' },
    viewEvaluate: {
      name: '查看' + EVALUATION,
      value: 'viewEvaluate',
      type: 'default',
    },
    invoice: { name: '补开发票', value: 'invoice', type: 'default' },
    share: { name: '我要晒单', value: 'share', type: 'default' },
    batchRefund: { name: '批量退款', value: 'batchRefund', type: 'default' },
    wholeAfterSales: {
      name: '整单售后',
      value: 'wholeAfterSales',
      type: 'default',
    },
  },

  // 虚拟卡券状态
  VIRTURL_TICKET_STATE: {
    NOT_VERIFIED: 1,
    VERIFIED: 2,
    DISABLED: 3,
    EXPIRED: 4,
    REFUND: 5,
  },

  // 虚拟卡券配置
  VIRTURL_TICKET_CONF: {
    1: {
      value: '',
      title: '待使用',
      suffix: '',
      weight: 1,
    },
    2: {
      value: '已使用',
      title: '已使用',
      suffix: '--del',
      weight: 3,
    },
    3: {
      value: '已退款',
      title: '已退款',
      suffix: '--del',
      weight: 2,
    },
    4: {
      value: '已过期',
      title: '已过期',
      suffix: '--del',
      weight: 4,
    },
    5: {
      value: '退款中',
      title: '退款中',
      suffix: '',
      weight: 0,
    },
  },
  BTN: {
    refund: {
      value: 'refund',
    },
  },
};
