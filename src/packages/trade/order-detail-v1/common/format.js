// eslint-disable-next-line
import { moment as formatDate } from 'utils/time';
import { stringifyAddress } from 'utils/stringify-address';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { baiduToGcj } from '@/helpers/lbs';
import get from '@youzan/weapp-utils/lib/get';
import config from './config';
import getStore from '../store';

const {
  VIRTURL_TICKET_STATE,
  VIRTURL_TICKET_CONF,
  tag: TAG,
  btn: BTN,
  bottom: BOTTOM,
} = config;

// 小额打款零钱查看配置
const MICRO_LABEL = {
  BALANCE_BUYER: {
    label: '(有赞零钱)',
    class: 'micro-check',
    change: 'goMyChange',
  },
  BUYER: {
    label: '(有赞零钱)',
    class: 'micro-check',
    change: 'goMyChange',
  },
  RECEIVER: {
    label: '(联系收货人查看余额)',
    class: 'micro-check-else',
    change: '',
  },
  BALANCE_RRECEIVER: {
    label: '(联系收货人查看余额)',
    class: 'micro-check-else',
    change: '',
  },
  APIPAY_USERID: {
    label: '',
    class: 'micro-check-else',
    change: '',
  },
  APIPAY_LOGONID: {
    label: '(支付宝)',
    class: 'micro-check-else',
    change: '',
  },
  WEPAY_USERID: {
    label: '',
    class: 'micro-check-else',
    change: '',
  },
  BALANCE_LOGONID: {
    label: '(联系对方查看余额)',
    class: 'micro-check-else',
    change: '',
  },
  OTHER: {
    label: '(联系对方查看余额)',
    class: 'micro-check-else',
    change: '',
  },
};

// 格式化金额
function formatPrice(price = 0, operator) {
  const symb = operator ? `${operator} ¥` : '¥';
  return `${symb} ${(Math.abs(price) / 100).toFixed(2)}`;
}

// 格式化总金额(积分 + 金额)
function formatTotalPrice(points = 0, price = 0, pointsName = '积分') {
  const totalArr = [];
  points && totalArr.push(`${points}${pointsName}`);
  (price || !points) && totalArr.push(formatPrice(price));
  return totalArr.join(' + ');
}

// 获取生成订单状态描述的相关参数
function getOrderStateDescInfo(order) {
  const nowTime = new Date().getTime() / 1000;
  const successTime = order.autoReceiveOrderTime || 0;

  return {
    showOrderStatusToGroup: order.showOrderStatusToGroup || false,
    showOrderStatusSended: order.showOrderStatusSended || false,
    leftTime: successTime > nowTime ? successTime - nowTime : 0,
    closeTypeStr: order.closeType > 0 ? order.closeTypeStr : '',
  };
}

// 获取订单状态 steps
function getOrderSteps(order) {
  const steps = {
    list: [],
    active: 0,
  };

  steps.list = (order.progressBar || []).map((item, index, list) => {
    if (item.arrive && (!list[index + 1] || !list[index + 1].arrive)) {
      steps.active = index;
    }

    return {
      text: item.type,
    };
  });
  return steps;
}

// 获取收货相关信息
function getAddressInfo(address, orderBizExtra) {
  const res = {
    completeAddress: '',
    receiverName: '',
    receiverTel: '',
    receiverLabel: orderBizExtra.showVirtualTicket ? '联系人' : '收货人',
  };

  let [country, province, city, county, addressDetail] = new Array(5).fill('');

  if (!address.selfFetchInfo) {
    country = address.deliveryCountry || '';
    province = address.deliveryProvince || '';
    city = address.deliveryCity || '';
    county = address.deliveryDistrict || '';
    addressDetail = address.deliveryStreet || '';
    res.receiverName = address.receiverName || '';
    res.receiverTel = address.receiverTel || '';
  }

  res.completeAddress = stringifyAddress({
    country,
    province,
    city,
    county,
    addressDetail,
  });

  return res;
}

function getSelfFetchInfo(address, order, orderBizExtra) {
  const res = {
    qrcode: '',
    fetchNo: '',
    fetcher: '',
    time: '',
    tel: '',
    address: '',
    lat: '',
    lng: '',
    shopName: order.shopName,
  };

  if (address.selfFetchInfo) {
    const {
      country = '',
      province = '',
      city = '',
      county = '',
      addressDetail = '',
      name,
    } = address.selfFetchInfo;

    res.address = stringifyAddress({
      country,
      province,
      city,
      county,
      addressDetail,
    });

    const { selfFetchResult = {}, selfFetchInfo = {} } = address;

    res.qrcode = selfFetchResult.qrCodeBase64 || '';
    res.fetchNo = selfFetchResult.selfFetchNo || '';
    res.selfFetchState = selfFetchResult.selfFetchState;
    res.fetcher = selfFetchInfo.userName || '';
    res.time = selfFetchInfo.userTime || '';
    res.tel = selfFetchInfo.tel || '';
    res.name = name || '';

    // 周期购自提
    if (orderBizExtra.isPeriodBuy) {
      res.selfFetchUrl = `/v2/trade/order/periodselffetchcode?order_no=${order.orderNo}`;
    } else {
      res.selfFetchUrl = `/wsctrade/order/selffetch/detail?orderNo=${order.orderNo}&kdtId=${order.kdtId}`;
    }

    const lng = selfFetchInfo.lng || selfFetchInfo.lon || '';
    const lat = selfFetchInfo.lat || '';

    if (lng && lat) {
      const gcjLocation = baiduToGcj(lng, lat);
      res.lng = gcjLocation.lng;
      res.lat = gcjLocation.lat;
    }
  }

  return res;
}

// 获取返现信息
function getCashBackInfo(order) {
  const { cashBackUmp = {} } = order.ump || {};
  let { cash = 0, maxCash = 0 } = cashBackUmp;

  cash = (cash / 100).toFixed(2);
  maxCash = (maxCash / 100).toFixed(2);

  return Object.assign(cashBackUmp, {
    cash,
    maxCash,
  });
}

// 获取优惠券相关信息
function getCouponInfo(order) {
  const { couponUmp = {}, couponCodeUmp = {} } = order.ump || {};
  const showCoupon = Object.keys(couponUmp).length > 0;
  const showCouponCode = Object.keys(couponCodeUmp).length > 0;

  let coupon = {};

  if (showCoupon) {
    coupon = couponUmp;
  } else if (showCouponCode) {
    coupon = couponCodeUmp;
  }

  return {
    show: !!(showCoupon || showCouponCode),
    name: coupon.name || '',
    condition: coupon.condition || '',
    decrease: coupon.decrease || '',
    desc: `省${formatPrice(coupon.decrease)}`,
  };
}

// 获取金额信息
function getPriceInfo(order, payment, orderBizExtra, ump, orderBizUrl, goods) {
  let taxPirce = 0;
  goods.forEach((item) => {
    // 1表示不是海淘订单，2表示含税，3表示不含税
    if (item.tariffTag === 3) {
      taxPirce += item.tariffPay;
    }
  });
  const res = {
    total: '',
    postage: '',
    realPay: formatPrice(payment.buyerRealPay),
    buyWay: order.isAllowShowBuyWay ? order.buyWayStr || '' : '',
    buyWayExtendDesc: order.isAllowShowBuyWay
      ? order.buyWayExtendDesc || ''
      : '',
    buyWayDetailUrl: order.isAllowShowBuyWay
      ? orderBizUrl.buyWayDetailUrl || ''
      : '',
    activitiesList: ump.orderActivities || [],
    decreaseAmount: ump.decreaseAmount || 0,
    payUmpDiscountMoney: payment.payUmpDiscountMoney || 0,
    pointsName: payment.pointsName,
    prepayCardPay: '',
    taxPirce: taxPirce ? formatPrice(taxPirce, '+') : '',
  };

  const { payPrice = 0 } = payment;
  const { pointsPrice = 0 } = order;
  res.total = formatTotalPrice(pointsPrice, payPrice, payment.pointsName);

  // 运费
  if (orderBizExtra.showPostage && payment.postage >= 0) {
    res.postage = formatPrice(payment.postage, '+');
  }

  // 储值卡/礼品卡
  const { deductionPay = 0 } = payment;
  res.prepayCardPay = deductionPay ? formatPrice(deductionPay, '-') : '';

  return res;
}

// 获取 shop 需要的数据
function getShopInfo(order, payment, goods, orderBizExtra, shopInfo) {
  const res = {
    name: '',
    totalPrice: '',
    periodTime: '',
    periodDeliveryTime: '',
    showPeriodBuyDelivery: false,
  };

  res.name = shopInfo.offlineStoreName || order.shopName || '';
  res.totalPrice = formatPrice(payment.payPrice);

  // 多周期购判断，新希望独有
  const isMultiPeriodBuy =
    order.goodsPackages &&
    order.goodsPackages.length > 1 &&
    goods.length > 0 &&
    goods[0].goodsType === 24;

  res.showPeriodBuyDelivery =
    orderBizExtra.isPeriodBuy &&
    !orderBizExtra.isSelfFetch &&
    !isMultiPeriodBuy;

  if (orderBizExtra.isPeriodBuy) {
    res.periodTime = order.periodDetail[0].periodAlias || '';
    res.periodDeliveryTime = order.periodDetail[0].deliverTimeAlias || '';
  }

  return res;
}

// 获取 service 需要的数据
function getServiceInfo(order, payment, address) {
  const res = {
    postage: '',
    expressType: '',
    localDeliveryDesc: '',
    localDeliveryImg: '',
    deliveryTime: '',
    buyerMemo: '',
  };

  res.postage = payment.postage || 0;
  res.postage = res.postage > 0 ? formatPrice(res.postage) : '免运费';

  res.expressType = order.expressTypeDesc || '';

  const localDelivery = address.localDeliveryScope || {};
  res.localDeliveryDesc = localDelivery.desc || '';
  res.localDeliveryImg = localDelivery.attachPic || '';

  res.deliveryTime = address.deliveryTimeDisplay || '';
  res.buyerMemo = order.buyerMemo || '无';

  return res;
}

// 获取发票数据
function getInvoiceInfo(invoice, order) {
  const res = {
    show: true,
    type: '',
    value: '',
    tip: '',
  };

  const { kdtId = '', orderNo = '' } = order;

  const successUrl = `/packages/trade/order/invoice/index?type=success&order_no=${orderNo}`;
  const detailUrl = `/packages/trade/order/invoice/index?type=detail&order_no=${orderNo}&kdt_id=${kdtId}`;

  switch (invoice.status) {
    case 1: // 待开票
      res.value = '待开票';
      res.tip = '交易完成后自动开票';
      res.url = detailUrl;
      break;
    case 10: // 开票中
      res.value = '开票中';
      res.url = detailUrl;
      break;
    case 20: // 失败
      res.value = '开票失败';
      res.tip = '请联系商家客服处理';
      break;
    case 30: // 成功
    case 40: // 冲红
      res.value = '开票成功';
      res.url = successUrl;
      break;
    case 50: // 已填写开票信息（对应非电子发票服务填了抬头的情况）
      res.value = '已填写开票信息';
      res.url = detailUrl;
      break;
    default:
      res.show = false;
      break;
  }

  return res;
}

// 获取商品属性
function getPropertiesStr(goods) {
  const extra = goods.extra || {};
  // 商品属性
  const properties = extra.GOODS_PROPERTY || [];

  return properties.map((property) => property.valName).join(', ');
}

// 获取商品sku信息
function getGoodsDesc(goods = {}) {
  const skuStr = (goods.sku || [])
    .filter((item) => item.v)
    .map((item) => item.v)
    .join(', ');
  const propertiesStr = getPropertiesStr(goods);

  return [skuStr, propertiesStr].filter((item) => !!item).join(', ');
}

// 获取 goods 数据
function getGoodsList(goods, order, pointsName, orderBizExtra = {}) {
  return goods.map((item) => {
    const res = {
      url: '',
      thumb: item.goodsInfo.imgUrl
        ? cdnImage(item.goodsInfo.imgUrl, '!180x180.jpg')
        : '',
      title: item.goodsInfo.shortTitle || item.goodsInfo.title || '',
      price: formatTotalPrice(
        item.goodsInfo.pointsPrice,
        item.unitPrice,
        pointsName
      ),
      desc: getGoodsDesc(item),
      num: item.num || 0,
      itemId: item.itemId || '',
      preSaleDate:
        (item.controlExtra &&
          item.controlExtra.preSale &&
          item.controlExtra.preSale.preSaleDate) ||
        '',
      skuId: item.skuId || '',
      goodsId: item.goodsId || '',
      alias: item.goodsInfo.alias || '',
      goodsType: item.goodsType || '',
      message: item.buyerMemo || {},
      safeRefund: item.safeRefund || {},
      originPrice: '',
      imgTag: {},
      tags: [],
      btns: [],
      taxTips: '',
      refundOrderItem: item.refundOrderItem || {},
      cancelPostponeDeliveryUrl: item.cancelPostponeDeliveryUrl,
      postponeDeliveryUrl: item.postponeDeliveryUrl,
      processInfo: item.processInfo, // 商品制作进度
      isShipped: !!item.isShipped,
    };

    const extra = item.extra || {};
    const usedPro = extra.USED_PRO || {};

    // 跨境商品税费计算
    // tariffTag， 1表示不是海淘订单，2表示含税，3表示不含税
    const { tariffTag, tariffPay = 0 } = item;
    if (tariffTag === 2) {
      res.taxTips = '进口税(含运费税款)：商品已含税';
    } else if (tariffTag === 3) {
      res.taxTips = `进口税(含运费税款)：¥ ${(tariffPay / 100).toFixed(2)}`;
    } else {
      res.taxTips = '';
    }

    // 商品标签
    item.controlButton.isPreSale && res.tags.push(TAG.presale);
    item.controlButton.isPeriodBuy && res.tags.push(TAG.periodBuy);
    orderBizExtra.isEnjoyBuy && res.tags.push(TAG.enjoyBuy);
    order.activityType === 20 && res.tags.push(TAG.fCode);
    order.activityType === 21 && res.tags.push(TAG.bargain);
    item.isUseFissionUmp && res.tags.push(TAG.inSourcing);
    item.controlButton.isSeckill && res.tags.push(TAG.seckill);
    item.controlButton.isPresent && res.tags.push(TAG.present);
    item.controlButton.isTimelimitedDiscount &&
      res.tags.push(TAG.timelimitedDiscount);
    item.controlButton.isAuction && res.tags.push(TAG.auction);
    item.controlButton.isCustomerDiscount &&
      res.tags.push(TAG.customerDiscount);
    +usedPro.activityType === 24 && res.tags.push(TAG.plusBuy); // 加价购
    item.controlButton.isCrossBorder && res.tags.push(TAG.crossBorder);
    item.controlButton.isUseGoodsExchangeCoupon &&
      res.tags.push(TAG.exchangeCoupon);

    // 划线价
    if (
      item.controlButton.isSeckill ||
      item.controlButton.isPresent ||
      item.controlButton.isTimelimitedDiscount ||
      item.controlButton.isAuction ||
      item.controlButton.isCustomerDiscount
    ) {
      res.originPrice = item.originUnitPrice
        ? formatPrice(item.originUnitPrice)
        : '';
    }

    // 商品按钮-查看留言
    if (item.buyerMemo && Object.keys(item.buyerMemo).length) {
      res.btns.push(BTN.message);
    }

    // 周期购顺延
    if (order.isShowPostponeShip) {
      res.btns.push(BTN.postpone);
    }

    // 商品按钮-退款
    const showTypes = [1, 3, 4];
    const showRefund =
      item.safeRefund &&
      Object.keys(item.safeRefund) &&
      showTypes.indexOf(item.safeRefund.buttonType) !== -1;
    if (showRefund) {
      res.btns.push({ ...BTN.refund, text: item.safeRefund.buttonText });
    }

    return res;
  });
}

// 获取时间相关信息
function getTimeInfo(order) {
  return {
    createTime: order.createTime
      ? formatDate(order.createTime, 'YYYY-MM-DD HH:mm:ss')
      : 0,
    payTitle: order.buyWay === 9 ? '下单时间' : '付款时间',
    payTime: order.payTime
      ? formatDate(order.payTime, 'YYYY-MM-DD HH:mm:ss')
      : 0,
    expressTime: order.expressTime
      ? formatDate(order.expressTime, 'YYYY-MM-DD HH:mm:ss')
      : 0,
    successTime: order.successTime
      ? formatDate(order.successTime, 'YYYY-MM-DD HH:mm:ss')
      : 0,
  };
}

// 获取底部按钮
function getBottomBtns({
  order,
  gift,
  orderBizExtra,
  goods,
  invoice,
  isNewHotelGood,
}) {
  let btns = [];

  // 分享订单items
  const shareItems = [
    {
      key: 'sendFriend',
      name: '发送给朋友',
      openType: 'share',
    },
    {
      key: 'sharePoster',
      name: '生成海报',
    },
  ];

  const { state } = getStore();

  // 确认收货
  if (order.isAllowConfirmReceive) {
    if (orderBizExtra.isHotel) {
      state.bottomBtnsDisplay.confirmHotel && btns.push(BOTTOM.confirmHotel);
    } else {
      btns.push(BOTTOM.confirm);
    }
  }

  // 查看团详情
  if (order.isAllowGroupon) {
    const btn = BOTTOM.groupon;
    const goodsItem = goods[0];

    if (goodsItem.goodsType == 31) {
      // 知识付费拼团
      const alias = get(goodsItem.goodsInfo || {}, 'alias', '');
      btn.link = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
        `https://h5.youzan.com/wscvis/ump/groupon/groupon-detail?order_no=${order.orderNo}&alias=${alias}&kdt_id=${order.kdtId}&activity_type=${order.activityType}`
      )}`;
    } else {
      // 普通拼团
      btn.link = `/packages/collage/groupon/detail/index?orderNo=${order.orderNo}&groupAlias=${order.groupBuy.alias}&type=${order.activityType}`;
    }

    btns.push(btn);
  }

  // 查看礼包
  if (Object.keys(gift) && gift.giftInvite && Object.keys(gift.giftInvite)) {
    const { alias = '', orderAlias = '' } = gift.giftInvite;
    if (alias && orderAlias) {
      const btn = BOTTOM.gift;
      btn.link = `/packages/paidcontent/gift/index?alias=${alias}&share_alias=&channel_type=3&order_alias=${orderAlias}&gift_type=1`;
      btns.push(btn);
    }
  }

  // 领优惠券
  if (order.isAllowShareCoupon) {
    const btn = BOTTOM.coupon;
    btn.link = `/packages/ump/fission/index?sharer=1&order_no=${order.bizNo}`;
    btns.push(btn);
  }

  // 评价
  if (orderBizExtra.isShowEvaluate) {
    const btn = BOTTOM.evaluate;
    btns.push(btn);
  }

  // 我要晒订单
  if (order.isAllowShareOrder) {
    const btn = BOTTOM.share;
    btn.shareUrl = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
      `/packages/order/share-page/index?order_no=${order.orderNo}&kdt_id=${order.kdtId}`
    )}`;
    btns.push(btn);

    // 新酒店订单只保留sendFriend
    if (isNewHotelGood) {
      shareItems.pop();
    }
  } else {
    shareItems.length = 0;
  }

  // 查看评价
  if (orderBizExtra.isShowViewEvaluate) {
    const btn = BOTTOM.viewEvaluate;
    btns.push(btn);
  }

  // 批量退款 or 整单售后
  if (orderBizExtra.showBatchRefund || orderBizExtra.showWholeRefund) {
    const { showWholeRefund = false } = orderBizExtra;
    const btn = showWholeRefund ? BOTTOM.wholeAfterSales : BOTTOM.batchRefund;
    btn.link = `/packages/trade/order/batch-refund/index?order_no=${order.orderNo}&kdt_id=${order.kdtId}`;
    btns.push(btn);
  }

  // 补开发票
  if (invoice.isAllowInvoice) {
    const btn = BOTTOM.invoice;
    btn.link = `/packages/trade/order/invoice/index?type=detail&order_no=${order.orderNo}&kdt_id=${order.kdtId}`;
    btns.push(btn);
  }
  // yunSdk 处理底部按钮操作
  if (state.bottomBtnsDisplay.type.length !== 0) {
    const newBtnList = btns.filter((item) => {
      return !state.bottomBtnsDisplay.type.includes(item.value);
    });
    btns = newBtnList;
  }
  return { bottomBtns: btns, shareItems };
}

// 获取虚拟卡券相关信息
function getVirtualTicketInfo(order) {
  const res = {
    title: '',
    time: '',
    list: [],
  };

  const { virtualTicketResponse: virtualTicket = {} } = order;
  res.time = virtualTicket.effectiveTimeDesc || '';
  res.list = virtualTicket.ticketItemResponseDTOList || [];

  // 根据权重设置 title
  let state = null;
  let weight = null;
  let notVerifiedNum = 0;

  res.list.forEach((item) => {
    const itemConfig = VIRTURL_TICKET_CONF[item.verifyState];

    if (item.verifyState === VIRTURL_TICKET_STATE.NOT_VERIFIED) {
      notVerifiedNum++;
    }

    if (weight === null || itemConfig.weight < weight) {
      res.title = itemConfig.title;
      weight = itemConfig.weight;
      state = item.verifyState;
    }
  });

  res.title =
    state === VIRTURL_TICKET_STATE.NOT_VERIFIED
      ? `${res.title}（${notVerifiedNum}张）`
      : res.title;

  return res;
}

function getVerifyAddressInfo(shopInfo) {
  const res = {
    name: '',
    address: '',
    lat: '',
    lng: '',
  };

  const { storeAddress = {} } = shopInfo;
  res.name = storeAddress.name;

  const {
    province = '',
    city = '',
    area = '',
    address = '',
    lat = '',
    lng = '',
  } = storeAddress;

  res.address = `${province}${city}${area}${address}`;

  if (lat && lng) {
    Object.assign(res, baiduToGcj(lng, lat));
  }

  return res;
}

function getGoodsIds(goods = []) {
  return goods.filter((item) => !!item.goodsId).map((item) => item.goodsId);
}

function getReturnValueCard(orderBizExtra = {}) {
  return {
    assertBusinessDetail: get(orderBizExtra, 'assertBusinessDetail', []),
    cardAssetIsActive: orderBizExtra.cardAssetIsActive,
  };
}

function getIsNewHotelGood(goods = []) {
  const firstItem = goods[0] || {};
  return !!(
    firstItem.extra &&
    firstItem.extra.BIZ_ITEM_ATTRIBUTE &&
    +firstItem.extra.BIZ_ITEM_ATTRIBUTE.NEW_HOTEL_GOOD
  );
}

function isAttrValid(item) {
  const { value } = item || {};
  const isValid = !!value;
  if (isValid && Array.isArray(value)) {
    return value.length > 0;
  }
  return isValid;
}

// 处理小额打款到账金额和到账时间
function listFormat(list = []) {
  return list.map((item) => {
    const amount = (item.amount / 100).toFixed(2);
    const finishedTime = formatDate(item.finishedTime, 'YYYY-MM-DD HH:mm:ss');
    const showDivide =
      list.length > 1 && list.indexOf(item) !== list.length - 1;
    return {
      ...item,
      amount,
      finishedTime,
      showText: MICRO_LABEL[item.targetType],
      showDivide,
    };
  });
}
// 处理小额打款总金额
function amountFormat(transferStat = {}) {
  const amount = transferStat.totalFinishedAmount || 0;
  transferStat.totalFinishedAmount = (amount / 100).toFixed(2);
  return transferStat;
}

// 格式化数据
export function formatOrder(data) {
  const {
    mainOrderInfo: order,
    orderAddressInfo: address,
    paymentInfo: payment,
    itemInfo: goods,
    invoiceInfo: invoice,
    shopInfo = {},
    gift = {},
    orderBizUrl = {},
    orderBizExtra = {},
    orderExtra = {},
    ump = {},
    microTransferInfo = {},
    sourceInfo = {},
  } = data;
  const goodsPackages = order.goodsPackages || [];
  const attributeItems = order.attributeItems || [];

  // 物流信息 detail 换行符处理
  const logistics = goodsPackages[0] || {};
  if (logistics != null && logistics.detail != null) {
    logistics.detail = logistics.detail.split('<br>').join('\n');
  }

  const isNewHotelGood = getIsNewHotelGood(goods);
  const { bottomBtns, shareItems } = getBottomBtns({
    order,
    gift,
    orderBizExtra,
    goods,
    invoice,
    isNewHotelGood,
  });
  const isScanBuyOrder =
    get(data, 'sourceInfo.orderMark', '') === 'online_scan_buy';

  return {
    show: true,
    showLogisticsInfo: !!(goodsPackages && goodsPackages.length),
    showVirtualTicket: orderBizExtra.showVirtualTicket || false,
    showShopVerifyAddress: order.showShopVerifyAddress || false,
    showReceiverInfo: !!(
      orderBizExtra.showAddress && !orderBizExtra.isSelfFetch
    ),
    showSelfFetch: orderBizExtra.showSelfFetch || false,
    showTopInfoBorder: !!(
      orderBizExtra.showAddress && !orderBizExtra.isSelfFetch
    ),
    showRefundInfo: order.isShowRefundInfo || false,
    showQrCode:
      !!order.virtualResponse && Object.keys(order.virtualResponse).length > 0,
    showGrouponCollection: order.showGroupAgencyReceive || false,
    showIdCard: order.showIdCard || false,
    showPresaleSteps: !!(payment.phasePays && payment.phasePays.length),
    showCheckDeliveryScope: order.showCheckDeliveryScope || false,
    showDeliveryType: orderBizExtra.showDeliveryType || false,
    showDeliveryTime: order.showDeliveryTime || false,
    showAfterSaleMobile: order.showAfterSaleMobile || false,
    showWxShoppingList: !!(
      order.extra &&
      order.extra.WECHAT_SYNC_SHOPPING_LIST === 1 &&
      goods[0].goodsType != 31 &&
      order.state > 4
    ),
    showEducation: order.showEducation || false,
    showLookCoupon: orderBizExtra.hasPayCouponsOrder || false,
    isNewPeriodBuyDelivery: order.isNewPeriodBuyDelivery || false,
    showFreight: Object.keys(order.freight || {}).length > 0,
    isUsingNewSnapshot: orderBizExtra.isUsingNewSnapshot || false,
    isScanBuyOrder,

    // 是否是新酒店商品
    isNewHotelGood,

    // 订单状态
    orderState: order.state,
    orderStateTitle: order.stateStr,
    orderStateDescInfo: getOrderStateDescInfo(order),
    orderSteps: getOrderSteps(order),

    // 物流
    logistics,

    // 附加费
    orderExtraPrices: payment.orderExtraPrices || [],

    // 电子卡券
    virtualTicketInfo: getVirtualTicketInfo(order),

    // 核销网点信息
    verifyAddressInfo: getVerifyAddressInfo(shopInfo),

    // 收货人或自提信息
    addressInfo: getAddressInfo(address, orderBizExtra),

    // 获取自提信息
    selfFetchInfo: getSelfFetchInfo(address, order, orderBizExtra),

    // 知识付费送礼退款
    giftInvite: gift.giftInvite || {},

    // 虚拟商品二维码
    qrCode: order.virtualResponse || {},

    // 拼团
    groupon: order.groupBuy || {},

    // 身份证号
    idCardNumber: order.idCardNumber || '',

    // 活动类型
    activityType: order.activityType || '',

    // 返现
    cashBack: getCashBackInfo(order),

    // 返储值金
    returnValueCard: getReturnValueCard(orderBizExtra),

    // 优惠券信息
    coupon: getCouponInfo(order),

    // 价格相关
    price: getPriceInfo(order, payment, orderBizExtra, ump, orderBizUrl, goods),

    // 定金预售
    presaleSteps: payment.phasePays || [],

    // 订单时间相关
    time: getTimeInfo(order),

    orderBizUrl,

    // shop
    shop: getShopInfo(order, payment, goods, orderBizExtra, shopInfo),

    // service
    service: getServiceInfo(order, payment, address),

    // 教育信息
    education: order.education,
    eduAttributeItems: attributeItems.map((item) => ({
      id: item.attributeId,
      name: item.attributeTitle,
      value: item.value,
      isValid: isAttrValid(item),
    })),

    // invoice
    invoice: getInvoiceInfo(invoice, order),

    // goods
    goodsList: getGoodsList(goods, order, payment.pointsName, orderBizExtra),

    allGoodsIds: getGoodsIds(goods),

    // 售后联系
    contact: order.afterSaleContact || {},

    // 运费险
    freight: order.freight,

    // 有赞担保
    yzGuarantee: order.yzGuarantee || false,
    guaranteeOrderInfo: {
      aliases: (goods || []).map((res) => get(res, 'goodsInfo.alias', '')), // 商品 alias
      feedback: order.feedback, // 是否售后中
      hasYzSecured: order.yzGuarantee, // 是否是有赞担保订单
      orderNo: order.orderNo, // E单号
      orderSuccessTime: order.successTime, // 订单完成时间
      orderStatus: order.state, // 订单状态
      orderPayTime: order.payTime, // 订单支付时间
      payWay: order.buyWay, // 支付方式
      orderCreateTime: order.createTime, // 订单创建时间
    },

    // 订单状态
    feedback: order.feedback || 0,

    // 底部按钮
    bottomBtns,

    // 分享订单items
    shareItems,

    // 订单是否使用内购券
    fissionTicketNum: get(orderBizExtra, 'fissionTicketNum', 0),

    // 积分自定义名称
    pointsName: payment.pointsName || '积分',

    // 小额打款信息
    transferStat: amountFormat(microTransferInfo.transferStat),
    transferOrderList: listFormat(microTransferInfo.transferOrderList),

    // 支付有礼
    paidPromotion: ump.paidPromotion,

    // 订单来源
    orderMark: sourceInfo.orderMark,
    takeGoodsCode: orderBizExtra.takeGoodsCode,

    // 拼单
    jointId: orderBizExtra?.jointDTO?.jointId,

    orderExtra,
  };
}
