import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

const app = getApp();

export default {
  /**
   * 获取订单信息
   * @param orderNo
   * @param kdtId
   */
  fetchOrderInfo({ orderNo, kdtId = 0, supportBlindBox }) {
    return new Promise((resolve, reject) => {
      app.request({
        path: 'wsctrade/order/detail/getOrderInfo.json',
        method: 'POST',
        data: {
          orderNo,
          kdtId,
          supportBlindBox
        },
        success(res = {}) {
          if (Object.keys(res).length) {
            resolve(mapKeysCase.toCamelCase(res));
          } else {
            reject(res);
          }
        },
        fail(err) {
          reject(err);
        },
      });
    });
  },

  /**
   * 获取退款相关信息
   * @param orderNo
   */
  fetchSafeInfo({ orderNo }) {
    return new Promise((resolve, reject) => {
      app.carmen({
        api: 'youzan.trade.weapp.refunds/1.0.0/get',
        data: {
          order_no: orderNo,
        },
        success(res = {}) {
          resolve(mapKeysCase.toCamelCase(res));
        },
        fail(err) {
          reject(err);
        },
      });
    });
  },

  /**
   * 获取知识付费商品(alias)
   * @param goodsIds
   */
  fetchOwlGoodsList({ goodsIds }) {
    return new Promise((resolve, reject) => {
      app.carmen({
        api: 'youzan.owl.items/1.0.0/listbygoodsids',
        data: {
          goods_ids: JSON.stringify(goodsIds),
        },
        success(res = {}) {
          resolve(mapKeysCase.toCamelCase(res));
        },
        fail(err) {
          reject(err);
        },
      });
    });
  },

  /**
   * 确认收货
   * @param orderNo
   */
  confirmReceive({ orderNo }) {
    return new Promise((resolve, reject) => {
      app.request({
        path: 'wsctrade/order/confirmReceive.json',
        method: 'POST',
        data: {
          order_no: orderNo,
        },
        success(res = {}) {
          resolve(res);
        },
        fail(err) {
          reject(err);
        },
      });
    });
  },

  getRefundState({ orderNo, kdtId = 0, itemId }) {
    return new Promise((resolve, reject) => {
      app.request({
        path: '/wsctrade/refund/getRefundState.json',
        method: 'GET',
        data: {
          kdtId,
          itemId,
          orderNo,
        },
        success(res = {}) {
          if (Object.keys(res).length) {
            resolve(mapKeysCase.toCamelCase(res));
          } else {
            reject(res);
          }
        },
        fail(err) {
          reject(err);
        },
      });
    });
  },
};
