import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import EventUtil from '@youzan/weapp-utils/lib/event';
import { openState } from './open-state';
import {
  showRefund,
  viewExpress,
  confirmOrder,
  viewEvaluate,
  evaluateOrder,
  shareOrder,
  showBatchRefund,
  updateConfirmReceiveText,
  setBottomBtnsDisplay,
  setRefundBtnsDisplay,
  showMessage,
} from './process';
import { updateComponent } from './update-component';
import {   
  onOrderLoadedEvent,
  beforeGoodsBtnClickAsync,
  beforeBottomBtnClickAsync,
} from './event';

export default {
  ...YunPageConfig,
  onLoad() {
    const sdk = this.getYunSdk();

    sdk.setPageData(this, openState);
    // 同步事件
    sdk.setPageEvent('onOrderLoaded', onOrderLoadedEvent);
    sdk.setPageEvent('beforeGoodsBtnClick', beforeGoodsBtnClickAsync);
    sdk.setPageEvent('beforeBottomBtnClick', beforeBottomBtnClickAsync);

    EventUtil.on('orderResultDataLoaded', (data) => {
      // updateOrderData(sdk, deepClone(data));

      onOrderLoadedEvent.trigger();
    });

    this.on('beforeGoodsBtnClick', (args) => {
      return beforeGoodsBtnClickAsync.trigger(args);
    });

    this.on('beforeBottomBtnClick', (args) => {
      return beforeBottomBtnClickAsync.trigger(args);
    });

    sdk.setPageProcess('showRefund', showRefund);
    sdk.setPageProcess('confirmOrder', confirmOrder);
    sdk.setPageProcess('viewEvaluate', viewEvaluate);
    sdk.setPageProcess('evaluateOrder', evaluateOrder);
    sdk.setPageProcess('viewExpress', viewExpress);
    sdk.setPageProcess('shareOrder', shareOrder);
    sdk.setPageProcess('showBatchRefund', showBatchRefund);
    sdk.setPageProcess('updateConfirmReceiveText', updateConfirmReceiveText);
    sdk.setPageProcess('setBottomBtnsDisplay', setBottomBtnsDisplay);
    sdk.setPageProcess('setRefundBtnsDisplay', setRefundBtnsDisplay);
    sdk.setPageProcess('showMessage', showMessage);

    sdk.page.updateComponent = updateComponent.bind(this);

    /* onOrderLoadedEvent.on('ssss', () => {
      console.log('onOrderLoadedEventTrigger');
    }); */
  },

  onUnload() {
    //
    EventUtil.off('orderResultDataLoaded');
  },
};
