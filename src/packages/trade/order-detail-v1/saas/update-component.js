import getStore from '../store';

const CAN_UPDATE_COMPONENTS_MAP = {
  OrderAddress: 'TOGGLE_SHOW_ORDER_ADDRESS',
  OrderStatus: 'TOGGLE_SHOW_ORDER_STATUS',
};

/**
 * 更新组件默认行为
 * @param {*} compName 组件名称
 * @param {*} display 是否隐藏组件
 */
export function updateComponent(compName, display) {
  if (CAN_UPDATE_COMPONENTS_MAP[compName]) {
    getStore().commit(CAN_UPDATE_COMPONENTS_MAP[compName], { display });
  }
}
