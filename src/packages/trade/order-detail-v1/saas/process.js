import * as processUtils from '../common/process';

const _fn = () => { };

// 申请退款
export function showRefund(args) {
  processUtils.refund(args);
}

/**
 * 获取支付方式
 */
export function confirmOrder() {
  return processUtils.confirmReceive();
}

/**
 * 查看评价
 */
export function viewEvaluate() {
  return processUtils.viewEvaluate();
}

/**
 * 评价
 */
export function evaluateOrder() {
  return processUtils.evaluate();
}

/**
 * 查看物流
 */
export function viewExpress() {
  return processUtils.toExpressPage();
}

/**
 * 分享订单
 */
export function shareOrder() {
  return processUtils.shareOrder();
}

/**
 * 批量退款
 */
export function showBatchRefund() {
  return processUtils.showBatchRefund();
}

/**
 * 收货自定义文案
 */
export function updateConfirmReceiveText(_content) {
  return processUtils.updateConfirmReceiveText(_content);
}

/**
 * 隐藏底部按钮
 */
export function setBottomBtnsDisplay(payload) {
  return processUtils.setBottomBtnsDisplay(payload);
}

/**
 * 隐藏申请退款按钮
 */
export function setRefundBtnsDisplay(goodsId) {
  return processUtils.setRefundBtnsDisplay(goodsId);
}

/**
 * 支持隐藏买家留言
 */
export function showMessage(display) {
  return processUtils.setShowMessage(display);
}
