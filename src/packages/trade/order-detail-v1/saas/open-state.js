import pick from '@youzan/weapp-utils/lib/pick';

const openState = {
  orderInfo(state) {
    const { mainOrderInfo, orderBizExtra } = state.bigOrder;
    const goodsPackages = mainOrderInfo.goodsPackages || [];

    return {
      ...pick(mainOrderInfo, [
        'orderNo',
        'createTime',
        'payTime',
        'stateStr',
        'expressType',
      ]),
      isShowExpress: !!(goodsPackages && goodsPackages.length),
      isShowConfirmOrder: mainOrderInfo.isAllowConfirmReceive,
      isShowEvaluateOrder: orderBizExtra.isShowEvaluate,
      isShowViewEvaluate: orderBizExtra.isShowViewEvaluate,
      isShowShareOrder: state.bottomBtns.some((item) => item.value === 'share'),
    };
  },

  shopInfo(state) {
    const { shopInfo } = state.bigOrder;
    return pick(shopInfo, ['shopName', 'kdtId']);
  },

  paymentInfo(state) {
    const { paymentInfo } = state.bigOrder;

    return {
      realPay: paymentInfo.realPay,
      postage: paymentInfo.postage,
    };
  },

  goodsList(state) {
    const { goodsList } = state;

    return goodsList.map((item) => {
      return {
        goodsId: item.goodsId,
        itemId: item.itemId,
        goodsType: item.goodsType,
        num: item.num,
        skuId: item.skuId,
        goodsInfo: { alias: item.alias, title: item.title, imgUrl: item.thumb },
        isShowRefund: item.btns.some((btn) => btn.value === 'refund'),
      };
    });
  },

  preSalePhaseInfo(state) {
    const { presaleSteps } = state;
    return presaleSteps.map((step) => {
      return pick(step, [
        'buyerRealPay',
        'payUmpDiscountMoney',
        'phase',
        'phaseArrived',
        'phaseDesc',
        'phasePayStatusDesc',
        'realPrice',
      ]);
    });
  },

  enjoyBuyOrderInfo(state) {
    const { orderExtra = {} } = state;
    const enjoyBuyOrderData = JSON.parse(
      orderExtra.ENJOY_BUY_ORDER_EXTRA || '{}'
    );
    return pick(enjoyBuyOrderData, [
      'deliveryMode',
      'customDeliveryMode',
      'deliveryCycle',
      'deliveryCycleCustomStartDate',
      'deliveryCycleCustomEndDate',
      'deliveryIssueTotal',
      'deliveryAmount',
      'deliveryStartDate',
      'deliveryTimeSection',
      'deliveryLeadTime',
      'deliveryCycleModifyMaxLimit',
    ]);
  },
};

export { openState };

function formatSaasOrder(innerOrder) {
  const {
    mainOrderInfo,
    orderBizExtra,
    paymentInfo,
    shopInfo,
    itemInfo = [],
  } = innerOrder;
  const orderInfo = pick(mainOrderInfo, [
    'orderNo',
    'createTime',
    'payTime',
    'stateStr',
    'expressType',
  ]);

  const goodsPackages = mainOrderInfo.goodsPackages || [];

  return {
    orderInfo: Object.assign(orderInfo, {
      isShowExpress: !!(goodsPackages && goodsPackages.length),
      isShowConfirmOrder: mainOrderInfo.isAllowConfirmReceive,
      isShowEvaluateOrder: orderBizExtra.isShowEvaluate,
      isShowViewEvaluate: orderBizExtra.isShowViewEvaluate,
    }),
    shopInfo: pick(shopInfo, ['shopName', 'kdtId']),
    paymentInfo: {
      realPay: paymentInfo.realPay,
      postage: paymentInfo.postage,
    },
    goodsList: itemInfo.map((item) => {
      const showTypes = [1, 4];
      const showRefund =
        item.safeRefund &&
        Object.keys(item.safeRefund) &&
        showTypes.indexOf(item.safeRefund.buttonType) !== -1;

      return {
        goodsId: item.goodsId,
        itemId: item.itemId,
        goodsType: item.goodsType,
        num: item.num,
        skuId: item.skuId,
        goodsInfo: pick(item.goodsInfo, ['alias', 'title', 'imgUrl']),
        isShowRefund: showRefund,
      };
    }),
  };
}

export function updateOrderData(sdk, newOrder) {
  Object.assign(sdk.page.data, formatSaasOrder(newOrder));
}
