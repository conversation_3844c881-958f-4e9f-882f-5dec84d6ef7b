import { VanxPage } from 'pages/common/wsc-page/index';
import { mapState, mapGetters } from '@youzan/vanx';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { formatOrder } from './common/format';
import api from './common/api';
import store, { resetStore } from './store';
import EventUtil from '@youzan/weapp-utils/lib/event';
import * as processUtils from './common/process';
import get from '@youzan/weapp-utils/lib/get';
import { getOrderEnterShopPolicy } from 'common-api/multi-shop/multi-shop-redirect';
import { PAGE_TYPE, NAVIGATE_TYPE, navigateToRantaPage } from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
/* #ifdef BUILD_ENV=youzanyun */
import SaasPageConfig from './saas/page-config';
/* #endif */

const app = getApp();
let extendPageConfig = {};
/* #ifdef BUILD_ENV=youzanyun */
extendPageConfig = SaasPageConfig;
/* #endif */

VanxPage(extendPageConfig, {
  store,

  mapData: {
    ...mapState([
      'kdtId',
      'orderNo',
      'orderExtra',
      'design',
      'show',
      'time',
      'bottomBtns',
      'groupon',
      'cashBack',
      'education',
      'showShare',
      'shareItems',
      'orderState',
      'orderSteps',
      'activityType',
      'orderBizUrl',
      'allGoodsIds',
      'youzanSecured',
      'showEducation',
      'returnValueCard',
      'virtualTicketInfo',
      'eduAttributeItems',
      'isNewHotelGood',
      'verifyAddressInfo',
      'showTopInfoBorder',
      'orderStateTitle',
      'orderStateDescInfo',
      'showVirtualTicket',
      'isUsingNewSnapshot',
      'showWxShoppingList',
      'showShopVerifyAddress',
      'paidPromotion',
      'guaranteeOrderInfo',
      'jointId',
      'jointParticipants',
      'jointStatus',
      'showOrderStatus',
    ]),
    ...mapGetters(['hideCopyright', 'hideYouzanSecured', 'hasShareBtn']),
  },

  onLoad(query) {
    // 处理页面的入参
    query = mapKeysCase.toCamelCase(query);
    const kdtId = query.kdtId || app.getKdtId();
    const dbid = query.dbid || '';
    let orderNo = query.orderNo || '';

    this.__orderReady = false;

    if (dbid) {
      const orderData = app.db.get(dbid) || {};

      if (orderData.order_no) {
        orderNo = orderData.order_no;
      }
    }

    this.$commit('SET_PREORDER', {
      orderNo,
      kdtId,
    });

    this.$dispatch('GET_DESIGN');

    processUtils.initProcessContext({ orderNo, kdtId });
  },

  onShow() {
    this.$dispatch('CHECK_USER_INFO');
    this.fetchOrderInfo({ showToast: true });
    getOrderEnterShopPolicy();
  },

  onPullDownRefresh() {
    this.fetchOrderInfo();
  },

  onShareAppMessage() {
    const { isNewHotelGood, orderNo, kdtId, orderBizUrl } = this.data;
    const { goodsList } = this.$store.state;
    let nameMaxlen = 0;
    let message = '';
    let subTitle = '';
    if (goodsList.length > 1) {
      nameMaxlen = 14;
      message = '等，必须推荐给你';
    } else {
      nameMaxlen = 15;
      message = '，必须推荐给你';
    }
    if (goodsList[0].title.length > nameMaxlen) {
      subTitle =
        goodsList[0].title.substr(0, nameMaxlen) + '...等' || '一些东西';
    } else {
      subTitle = goodsList[0].title || '一些东西';
    }

    let path = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
      `/packages/order/share-page/index?order_no=${orderNo}&kdt_id=${kdtId}&is_share=1`
    )}`;

    if (isNewHotelGood) {
      path = `/pages/common/webview-page/index?src=${encodeURIComponent(
        orderBizUrl.newHotelGoodsDetailUrl
      )}`;
    }

    return {
      title: `我买了${subTitle}${message}`,
      imageUrl: goodsList[0] ? goodsList[0].thumb : '',
      path,
    };
  },

  handleRefresh() {
    wx.startPullDownRefresh();
  },

  // 显示不支持查看modal
  showUnsupportedModal(unsupportedInfo) {
    wx.showModal({
      title: '',
      content: unsupportedInfo.content,
      confirmText: '复制链接',
      showCancel: false,
      success(res) {
        if (res.confirm) {
          const duration = 1500;

          wx.setClipboardData({
            data: unsupportedInfo.url,
            success() {
              wx.showToast({
                duration,
                title: '已复制',
              });
              setTimeout(() => {
                if (getCurrentPages().length === 1) {
                  wx.redirectTo({
                    url: '/packages/trade/order/list/index',
                  });
                } else {
                  wx.navigateBack();
                }
              }, duration);
            },
          });
        }
      },
    });
  },

  onPageDataReady() {
    return new Promise((resolve) => {
      if (this.__orderReady) {
        resolve();
      } else {
        EventUtil.on('orderResultDataLoaded', () => {
          resolve();
        });
      }
    });
  },

  // 获取订单数据
  fetchOrderInfo({ showToast = false } = {}) {
    showToast &&
      wx.showToast({
        title: '数据加载中',
        mask: true,
        icon: 'loading',
      });

    const supportBlindBox = true;
    const { orderNo, kdtId } = this.data;
    this.__orderReady = false;

    api
      .fetchOrderInfo({ orderNo, kdtId, supportBlindBox })
      .then((res) => {
        wx.hideToast();
        wx.stopPullDownRefresh();

        const goodsItem = get(res, 'itemInfo[0]', {});
        const cardGoodsId = goodsItem.goodsType == 20 ? goodsItem.goodsId : '';

        // 不支持查看
        if (res.unsupportedInfo && Object.keys(res.unsupportedInfo).length) {
          return this.showUnsupportedModal(res.unsupportedInfo);
        }

        // 待支付重定向至支付页
        if (res.mainOrderInfo.state === 10) {
          return navigateToRantaPage({
            pageType: PAGE_TYPE.PAY,
            type: NAVIGATE_TYPE.REDIRECT,
            query: {
              orderNo,
            },
          });
        }

        // 会员卡订单
        if (cardGoodsId) {
          return wx.redirectTo({
            url: `/packages/card/detail/index?goods_id=${cardGoodsId}`,
          });
        }

        // 重定向到社区团购 详情
        const originSource = get(res.sourceInfo, 'originSource', {});
        const source = get(originSource, 'source', '');
        if (source === 'mall_group_buy') {
          return wx.redirectTo({
            url: `/packages/groupbuying/buyer-trade/detail/index?orderNo=${orderNo}`,
          });
        }

        // 塞入格式化后的订单数据
        this.$commit('SET_ORDER', formatOrder(res));
        this.$commit('SET_BIG_ORDER', res);
        EventUtil.trigger('orderResultDataLoaded', res);

        // 获取拼单信息
        this.$dispatch('GET_JOINT');

        // this.setImData();
        // 新退款页面不需要safeNo了
        // this.updateGoodsRefundInfo();
        this.updateGoodsUrl();

        this.__orderReady = true;
      })
      .catch((err) => {
        wx.hideToast();
        wx.stopPullDownRefresh();
        // 前往错误页
        const errorId = app.db.set({ text: err.msg, code: err.code });
        wx.redirectTo({
          url: '/packages/common/error/index?dbid=' + errorId,
        });
      });
  },

  // 生成im参数
  getContactInfo() {
    return new Promise((resolve) => {
      this.onPageDataReady().then(() => {
        const { goodsList, orderNo, kdtId } = this.$store.state;

        const messageImg = goodsList[0].thumb;
        const messagePath = `/packages/trade/order/result/index?orderNo=${orderNo}&num=${goodsList.length}&kdtId=${kdtId}`;
        const messageCard = true;

        app.getImData().then((shopImConfig) => {
          shopImConfig.storeId = app.getOfflineId();

          const goodsImgs = [];
          let goodsNum = 0;

          goodsList.forEach((item) => {
            goodsNum += item.num;
            goodsImgs.push(item.thumb);
          });

          const sourceParam = {
            kdt_id: kdtId,
            source: 'order',
            endpoint: shopImConfig.endpoint,
            detail: JSON.stringify({
              order_no: orderNo,
              piece: goodsNum,
              imgs: goodsImgs.splice(0, 3),
            }),
          };

          shopImConfig.storeId && (sourceParam.site_id = shopImConfig.storeId);

          resolve({
            sourceParam: JSON.stringify(sourceParam),
            ...shopImConfig,
            messagePath,
            messageImg,
            messageCard,
          });
        });
      });
    });
  },

  // 更新商品跳转链接
  updateGoodsUrl() {
    const { goodsList = [], processGoodsId = [] } = this.$store.state;

    const owlGoodsIds = []; // 知识付费送礼商品id列表
    goodsList.forEach((item, index) => {
      if (item.goodsType === 31) {
        // 知识付费商品
        owlGoodsIds.push(item.goodsId);
      } else {
        // 其他商品  储值卡类型21 不跳转
        this.$commit('UPDATE_GOODS_URL', {
          index,
          url:
            item.goodsType === 21
              ? ''
              : `/pages/goods/detail/index?alias=${item.alias}`,
        });
        if (processGoodsId.length !== 0) {
          this.$dispatch('HIDDEN_REFUND_BTN');
        }
      }
    });

    owlGoodsIds.length &&
      api
        .fetchOwlGoodsList({
          goodsIds: owlGoodsIds,
        })
        .then((res) => {
          goodsList.forEach((item, index) => {
            const { owlType = null, alias = '' } = res[item.goodsId] || {};

            let url = '';
            switch (owlType) {
              case 1:
                url = `/packages/paidcontent/column/index?alias=${alias}`;
                break;
              case 2:
                url = `/packages/paidcontent/content/index?alias=${alias}`;
                break;
              case 4:
                url = `/packages/paidcontent/live/index?alias=${alias}`;
                break;
              case 5:
                url = `/packages/punch/activity/index?alias=${alias}`;
                break;
              case 9:
                url = `/packages/new-punch/introduction/index?alias=${alias}`;
                break;
              default:
                url = `/pages/goods/detail/index?alias=${item.alias}`;
                break;
            }

            this.$commit('UPDATE_GOODS_URL', { index, url });
          });
        })
        .catch(() => {});
  },

  onClickPage() {
    this.trigger('hide:postpone');
  },

  // 关闭分享
  closeShareSheet() {
    this.$commit('TOGGLE_SHARE_SHEET', false);
  },

  onUnload() {
    resetStore();
  },
});
