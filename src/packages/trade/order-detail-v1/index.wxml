<import src="./custom-tpl.wxml" />

<page-container
  fixed-bottom="{{ !!bottomBtns.length }}"
  forbid-copyright="{{ hideCopyright }}"
  bindtap="onClickPage"
>
  <view wx:if="{{ show }}">
    <block wx:for="{{ design }}" wx:key="type">
      <block wx:if="{{ item.type === 'order-status' &&  showOrderStatus }}">
        <!-- 订单状态 -->
        <status
          order-no="{{ orderNo }}"
          state="{{ orderState }}"
          state-title="{{ orderStateTitle }}"
          state-desc-info="{{ orderStateDescInfo }}"
          group-left-time="{{ groupon.groupLeftTime }}"
          group-left-people="{{ groupon.groupLeftPeople }}"
          steps="{{ orderSteps }}"
          bind:refresh="handleRefresh"
        />
      </block>

      <!-- 物流信息,收货信息,自提,海淘身份证,团长代收等 -->
      <order-address wx:elif="{{ item.type === 'order-address' }}" />

      <activity paid-promotion="{{ paidPromotion }}" order-no="{{ orderNo }}" wx:elif="{{ item.type === 'paid-promotion' }}" />

      <!-- 知识付费送礼退款, 虚拟订单二维码 -->
      <virtual-goods wx:elif="{{ item.type === 'virtual-goods' }}" />

      <block wx:elif="{{ item.type === 'special-feature' }}">
        <!-- 订单返现 -->
        <cash-back
          wx:if="{{ cashBack.showCashBack || cashBack.showMaxCashBack }}"
          order-no="{{ orderNo }}"
          activityType="{{ activityType }}"
          show-cash-back="{{ cashBack.showCashBack }}"
          show-max-cash-back="{{ cashBack.showMaxCashBack }}"
          cash="{{ cashBack.cash }}"
          max-cash="{{ cashBack.maxCash }}"
        />

        <return-value-card
          return-value-card="{{ returnValueCard }}"
        />
      </block>

      <!-- 拼单 -->
      <block wx:elif="{{ item.type === 'joint' }}">
        <joint
          wx:if="{{ jointParticipants.length }}"
          joint-id="{{ jointId }}"
          participants="{{ jointParticipants }}"
          status="{{ jointStatus }}" />
      </block>

      <!-- 店铺+商品 -->
      <goods-block wx:elif="{{ item.type === 'goods-info' }}" />

      <block wx:elif="{{ item.type === 'virtual-ticket-tpl' }}">
        <!-- 电子卡券 -->
        <virtual-ticket
          wx:if="{{ showVirtualTicket }}"
          order-no="{{ orderNo }}"
          virtual-ticket-info="{{ virtualTicketInfo }}"
        />

        <!-- 卡券核销地址 -->
        <verify-address
          wx:if="{{ showVirtualTicket && showShopVerifyAddress }}"
          verify-address-info="{{ verifyAddressInfo }}"
        />
      </block>

      <!-- 服务 -->
      <service-block wx:elif="{{ item.type === 'service-block' }}" />

      <block wx:elif="{{ item.type === 'education' }}">
        <!-- 教育信息 -->
        <education
          wx:if="{{ showEducation }}"
          education="{{ education }}"
          attributes="{{ eduAttributeItems }}"
        />
      </block>

      <!-- 优惠券,计价版,定金预售 -->
      <price-block wx:elif="{{ item.type === 'pay-panel' }}" custom-props="{{ item.customProps }}" />

      <!-- 订单时间相关 -->
      <time
        wx:elif="{{ item.type === 'order-base-info' }}"
        order-no="{{ orderNo }}"
        order-extra="{{ orderExtra }}"
        time="{{ time }}"
        money-to-where-url="{{ orderBizUrl.moneyToWhereUrl }}"
        order-question-url="{{ orderBizUrl.orderQuestionUrl }}"
        is-using-new-snapshot="{{ isUsingNewSnapshot }}"
        yz-guarantee-url="{{ orderBizUrl.yzGuaranteeUrl }}"
      />

      <block wx:elif="{{ item.type === 'wx-favorite-goods' }}">
        <!-- 微信好物圈入口 -->
        <shopping-list
          wx:if="{{ showWxShoppingList }}"
          style-set="{{ 2 }}"
          class="favorite-goods"
        />
      </block>

      <recommend-goods
        wx:elif="{{ item.type === 'recommend-block' }}"
        biz-name="order_detail"
      />

      <block wx:elif="{{ item.type === 'order-footer' }}">
        <!-- 订单享受xxx的条条 -->
        <order-ensure
          wx:if="{{ !hideYouzanSecured }}"
          has-secured="{{ yzGuarantee }}"
        />
      </block>

      <block wx:elif="{{ item.type === 'bottom-action' }}">
        <!-- 底部操作按钮 -->
        <bottom-action
          wx:if="{{ !!bottomBtns.length }}"
          order-no="{{ orderNo }}"
          order-extra="{{ orderExtra }}"
          btns="{{ bottomBtns }}"
          guarantee-order-info="{{ guaranteeOrderInfo }}"
          bind:refresh="handleRefresh"
        />
      </block>

      <!-- 三方自定义组件 -->
      <block wx:elif="{{ item.custom }}">
        <template is="{{ item.type }}" />
      </block>
    </block>

    <!-- 会员等级 -->
    <levelup-tip
      event-key="confirm-mission"
      title="收货成功，会员升级至${level}"
    />

    <!-- 订单分享组件 -->
    <trade-share
      wx:if="{{ hasShareBtn }}"
      visable="{{ showShare }}"
      orderNo="{{ orderNo }}"
      items="{{ shareItems }}"
      bind:close="closeShareSheet"
    />

    <block wx:elif="{{ item.type === 'cps-goods-recommend' }}">
      <cps-goods-recommend cpsConfigKey="cps_goods_recommend_order_detail" />
    </block>

  </view>

  <!-- 骨架图 -->
  <image
    wx:else
    class="skeleton-image"
    mode="scaleToFill"
    src="https://img01.yzcdn.cn/public_files/2020/09/21/3825c94b8615284bd0daae6372cec367.png"
  />
</page-container>
