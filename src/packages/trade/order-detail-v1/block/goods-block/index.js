import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState, mapMutations } from '@youzan/vanx';
import store from '../../store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState([
      'kdtId',
      'shop',
      'orderNo',
      'goodsList',
      'yzGuarantee',
      'freight',
      'guaranteeOrderInfo',
      'bigOrder',
      'orderExtra',
    ]),
  },

  methods: {
    ...mapMutations(['SET_ORDER']),
    setGuarantee({ detail }) {
      this.$commit('SET_YZ_GUARANTEE', detail);
    },
    setYzGuaranteeInfo({ detail: info }) {
      this.$commit('SET_YZ_GUARANTEE_INFO', info);
    },
    setYzGuaranteeDocs({ detail: docs }) {
      this.$commit('SET_YZ_GUARANTEE_DOCS', docs);
    },
  },
});
