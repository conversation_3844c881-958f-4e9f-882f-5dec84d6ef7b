<flow-entrance-banner biz-name="order_detail" />

<!-- 店铺 -->
<shop
  show-period-buy-delivery="{{ shop.showPeriodBuyDelivery }}"
  period-time="{{ shop.periodTime }}"
  period-delivery-time="{{ shop.periodDeliveryTime }}"
  shop-name="{{ shop.name }}"
  total-price="{{ shop.totalPrice }}"
  kdt-id="{{ kdtId }}"
  order-no="{{ orderNo }}"
  yz-guarantee="{{ yzGuarantee  }}"
  guarantee-order-info="{{ guaranteeOrderInfo }}"
  freight="{{ freight }}"
  bind:setGuarantee="setGuarantee"
  bind:setYzGuaranteeInfo="setYzGuaranteeInfo"
  bind:setYzGuaranteeDocs="setYzGuaranteeDocs"
>
  <view
    wx:for="{{ goodsList }}"
    wx:key="index"
    class="goods"
  >
    <goods
      order-no="{{ orderNo }}"
      order-extra="{{ orderExtra }}"
      url="{{ item.url }}"
      num="{{ item.num }}"
      desc="{{ item.desc }}"
      tags="{{ item.tags }}"
      img-tag="{{ item.imgTag }}"
      btns="{{ item.btns }}"
      thumb="{{ item.thumb }}"
      title="{{ item.title }}"
      price="{{ item.price }}"
      origin-price="{{ item.originPrice }}"
      item-id="{{ item.itemId }}"
      pre-sale-date="{{ item.preSaleDate }}"
      message="{{ item.message }}"
      tax-tips="{{ item.taxTips }}"
      refund-order-item="{{ item.refundOrderItem }}"
      processInfo="{{ item.processInfo }}"
      is-shipped="{{ item.isShipped }}"
    />
  </view>
</shop>

<postpone goods-list="{{ goodsList }}" />
