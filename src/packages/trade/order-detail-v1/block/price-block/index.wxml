<!-- 优惠券 -->
<coupon
  wx:if="{{ coupon.show }}"
  name="{{ coupon.name }}"
  desc="{{ coupon.desc }}"
  condition="{{ coupon.condition }}"
/>

<!-- 计价版 -->
<price
  show-after-sale-mobile="{{ showAfterSaleMobile }}"
  contact="{{ contact }}"
  has-user-info="{{ hasUserInfo }}"
  kdt-id="{{ kdtId }}"
  message-img="{{ goodsList[0].thumb }}"
  order-no="{{ orderNo }}"
  goods-num="{{ goodsList.length }}"
  buy-way="{{ price.buyWay }}"
  buy-way-extend-desc="{{ price.buyWayExtendDesc }}"
  buy-way-detail-url="{{ price.buyWayDetailUrl }}"
  fission-ticket-num="{{ fissionTicketNum }}"
  total="{{ price.total }}"
  orderExtraPrices="{{ orderExtraPrices }}"
  postage="{{ price.postage }}"
  real-pay="{{ price.realPay }}"
  prepay-card-pay="{{ price.prepayCardPay }}"
  tax-pirce="{{ price.taxPirce }}"
  decrease-amount="{{ price.decreaseAmount }}"
  pay-ump-discount-money="{{ price.payUmpDiscountMoney }}"
  activities-list="{{ price.activitiesList }}"
  points-name="{{ price.pointsName }}"
  transfer-stat="{{ transferStat }}"
  transfer-order-list="{{ transferOrderList }}"
  bind:check-user-info="checkUserInfo"
  is-prior-use="{{ bigOrder && bigOrder.mainOrderInfo && bigOrder.mainOrderInfo.buyWay === 49 }}"
  is-prior-use-paid="{{ bigOrder && bigOrder.mainOrderInfo && bigOrder.mainOrderInfo.extra && bigOrder.mainOrderInfo.extra.PRIOR_USE_COMPLETED === 1 }}"

/>

<!-- 定金预售 -->
<presale-steps
  wx:if="{{ showPresaleSteps && !customProps.hidePreSale }}"
  steps="{{ presaleSteps }}"
/>