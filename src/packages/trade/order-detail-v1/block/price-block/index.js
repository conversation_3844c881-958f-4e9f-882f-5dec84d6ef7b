import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '../../store/index';

VanxComponent({
  store,

  properties: {
    customProps: {
      type: Object,
      default: () => ({ hidePreSale: false }),
    },
  },

  mapData: {
    ...mapState([
      'kdtId',
      'orderNo',
      'price',
      'coupon',
      'contact',
      'goodsList',
      'hasUserInfo',
      'presaleSteps',
      'fissionTicketNum',
      'orderExtraPrices',
      'showAfterSaleMobile',
      'showPresaleSteps',
      'transferStat',
      'transferOrderList',
      'bigOrder',
    ]),
  },

  methods: {
    // 检查是否有用户信息
    checkUserInfo() {
      this.$dispatch('CHECK_USER_INFO');
    },
  },
});
