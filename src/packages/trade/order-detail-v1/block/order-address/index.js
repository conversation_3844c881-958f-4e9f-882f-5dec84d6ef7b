import { VanxComponent } from 'pages/common/wsc-component/index';
import { mapState } from '@youzan/vanx';
import store from '../../store/index';

VanxComponent({
  store,

  mapData: {
    ...mapState([
      'shop',
      'orderNo',
      'groupon',
      'logistics',
      'addressInfo',
      'activityType',
      'idCardNumber',
      'selfFetchInfo',
      'isScanBuyOrder',
      'showIdCard',
      'showSelfFetch',
      'showOrderAddress',
      'showReceiverInfo',
      'showLogisticsInfo',
      'showGrouponCollection',
      'orderMark',
      'takeGoodsCode',
    ]),
  },
});
