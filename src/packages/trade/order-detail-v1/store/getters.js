function hasOrderFooterBlock(state) {
  const design = state.design;

  // 底部版权和有赞担保 在order-footer区块，没有配置则不显示
  return design.some((block) => {
    return block.type === 'order-footer';
  });
}

export default {
  hideCopyright(state) {
    // 后面可以扩展其他条件做隐藏操作
    return !hasOrderFooterBlock(state);
  },

  hideYouzanSecured(state) {
    // 后面可以扩展其他条件做隐藏操作
    return !hasOrderFooterBlock(state);
  },

  hasShareBtn(state) {
    return state.bottomBtns.some((item) => item.value === 'share');
  }
};
