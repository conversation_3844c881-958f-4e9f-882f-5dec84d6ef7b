export const state = {
  kdtId: 0,
  orderNo: '',
  showMessage: true,
  show: false,
  showLogisticsInfo: false,
  showVirtualTicket: false,
  showShopVerifyAddress: false,
  showReceiverInfo: false,
  showSelfFetch: false,
  showTopInfoBorder: false,
  showRefundInfo: false,
  showQrCode: false,
  showGrouponCollection: false,
  showIdCard: false,
  showPresaleSteps: false,
  showCheckDeliveryScope: false,
  showDeliveryType: false,
  showDeliveryTime: false,
  showAfterSaleMobile: false,
  showWxShoppingList: false,
  showEducation: false,
  isNewPeriodBuyDelivery: false,
  showFreight: false,
  showOrderAddress: true,
  showOrderStatus: true,
  isScanBuyOrder: false,

  showLookCoupon: false,
  isUsingNewSnapshot: false,

  // 大订单，存放一些原始数据初步是open-state使用
  bigOrder: {
    shopInfo: {},
    paymentInfo: {},
    mainOrderInfo: {},
    orderBizExtra: {},
  },

  hasUserInfo: false,
  design: [],
  extensionConfig: {},

  // 是否是新酒店商品
  isNewHotelGood: false,

  // 订单状态
  orderState: 0,
  orderStateTitle: '',
  orderSteps: [],
  orderStateDescInfo: {},

  // 物流
  logistics: {},

  // 附加费
  orderExtraPrices: [],

  // 电子卡券
  virtualTicketInfo: {},

  // 核销网点信息
  verifyAddressInfo: {},

  // 收货人或自提信息
  addressInfo: {},

  // 获取自提信息
  selfFetchInfo: {},

  // 知识付费送礼退款
  giftInvite: {},

  // 虚拟商品二维码
  qrCode: {},

  // 拼团
  groupon: {},

  // 身份证号
  idCardNumber: '',

  // 活动类型
  activityType: '',

  // 返现
  cashBack: {},

  // 返储值金
  returnValueCard: {},

  // 优惠券信息
  coupon: {},

  // 价格相关
  price: {},

  // 定金预售
  presaleSteps: [],

  // 订单时间相关
  time: {},

  orderBizUrl: {},

  // shop
  shop: {},

  // service
  service: {},

  // 教育信息
  education: {},
  eduAttributeItems: [],

  // 发票
  invoice: {},

  // goods
  goodsList: [],

  allGoodsIds: [],

  // 售后联系
  contact: {},

  // 运费险
  freight: {},

  youzanSecured: false,

  // 底部按钮
  bottomBtns: [],

  // 订单是否使用内购券
  fissionTicketNum: 0,

  // 积分自定义名称
  pointsName: '积分',

  // 展示分享actionsheet
  showShare: false,

  // 分享订单items
  shareItems: [],

  // 有赞担保
  yzGuarantee: false, // 是否展示有赞担保
  // 作为 props 传给 @youzan/assets-weapp-components/guarantee-order-freight-insurance-bar
  guaranteeOrderInfo: null,
  yzGuaranteeInfo: {}, // 有赞担保商详页展示信息（提交订单时传给交易后端做交易快照）
  yzGuaranteeDocs: {}, // 有赞担保文案信息（配置在 Apollo）

  // 订单状态
  feedback: 0,

  // 小额打款信息
  transferStat: {},
  transferOrderList: [],

  // 支付有礼
  paidPromotion: {},
  // 订单来源
  orderMark: '',

  // 拼单
  jointId: '',
  jointParticipants: [],
  jointStatus: '',

  // 定制收货文案
  receivingContent: '',

  // 隐藏底部按钮
  bottomBtnsDisplay: {
    confirmHotel: true, // 确认入住
    type: [],
  },

  // 隐藏申请退款按钮
  processGoodsId: [],

  orderExtra: {},
};
