export default {
  SET_PREORDER(state, payload) {
    state.orderNo = payload.orderNo;
    state.kdtId = payload.kdtId;
  },

  SET_ORDER(state, payload) {
    Object.assign(state, payload);
  },

  SET_BIG_ORDER(
    state,
    { mainOrderInfo, shopInfo, orderBizExtra, paymentInfo }
  ) {
    Object.assign(state.bigOrder, {
      mainOrderInfo,
      shopInfo,
      orderBizExtra,
      paymentInfo,
    });
  },

  SET_HAS_USER_INFO(state, payload) {
    state.hasUserInfo = payload;
  },

  SET_DESIGN(state, payload) {
    state.design = payload;
  },

  SET_EXTENSION_CONFIG(state, payload) {
    state.extensionConfig = {
      ...payload,
    };
  },

  UPDATE_GOODS_URL(state, payload) {
    const { index, url } = payload;
    state.goodsList.splice(index, 1, { ...state.goodsList[index], url });
  },

  TOGGLE_SHARE_SHEET(state, payload) {
    state.showShare = !!payload;
  },

  TOGGLE_SHOW_ORDER_ADDRESS(state, { display }) {
    state.showOrderAddress = display;
  },

  TOGGLE_SHOW_ORDER_STATUS(state, { display }) {
    state.showOrderStatus = display;
  },

  SET_YZ_GUARANTEE(state, payload) {
    state.yzGuarantee = payload;
  },
  SET_YZ_GUARANTEE_INFO(state, payload) {
    state.yzGuaranteeInfo = payload;
  },
  SET_YZ_GUARANTEE_DOCS(state, payload) {
    state.yzGuaranteeDocs = payload;
  },
  SET_JOINT(state, { jointOrderStatus, participantBriefInfoList, jointId }) {
    state.jointParticipants = participantBriefInfoList;
    state.jointStatus = jointOrderStatus;
    state.jointId = jointId;
  },
  UPDATE_RECEIVING_CONTENT(state, payload) {
    state.receivingContent = payload;
  },

  UPDATE_BOTTOM_BTNS_DISPLAY(state, payload) {
    state.bottomBtnsDisplay.confirmHotel = payload.confirmHotel;
  },
  SET_GOODSLIST(state, payload) {
    state.goodsList = payload;
  },
  SET_PROCESS_GOODS_ID(state, payload) {
    state.processGoodsId = payload;
  },
  SET_SHOW_MESSAGE(state, payload) {
    state.showMessage = payload;
  },
  HIDDEN_BOTTOM_BTNS(state, payload) {
    state.bottomBtnsDisplay.type = payload.type;
  },
};
