import Store from '@youzan/vanx';
import { state } from './state';
import getters from './getters';
import actions from './actions';
import mutations from './mutations';

const store = {
  state,
  actions,
  getters,
  mutations,
};

let theStore;

export default function getStore() {
  return theStore || (theStore = new Store(store));
}

export function resetStore() {
  theStore = new Store(store);
}
