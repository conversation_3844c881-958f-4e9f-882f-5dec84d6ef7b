import getApp from 'shared/utils/get-safe-app';
import designMod from '../design.json';
import config from '../common/config';

const app = getApp();
const { BTN } = config;

export default {
  // 检查是否有用户信息
  CHECK_USER_INFO({ commit }) {
    return app.getUserInfo(
      (res) => {
        commit('SET_HAS_USER_INFO', !!res.userInfo);
      },
      () => {
        commit('SET_HAS_USER_INFO', false);
      }
    );
  },

  GET_DESIGN({ commit }) {
    const modList = designMod.design || [];
    const config = modList[0] && modList[0].type === 'config' && modList[0];
    const newDesign = config ? modList.slice(1) : modList;

    commit('SET_DESIGN', newDesign);
    commit('SET_EXTENSION_CONFIG', { ...(config.profile || {}) });

    return Promise.resolve({
      config,
      design: newDesign,
    });
  },

  // 显示分享弹框
  SHOW_SHARE_SHEET({ commit }) {
    app.logger &&
      app.logger.log({
        et: 'click',
        ei: 'click_page_share',
        en: '点击我要晒单',
        si: app.getKdtId(),
      });
    commit('TOGGLE_SHARE_SHEET', true);
  },

  // 获取拼单信息
  GET_JOINT({ commit, state }) {
    const { orderNo, jointId, kdtId } = state;
    if (!jointId) {
      return;
    }

    app
      .request({
        path: '/retail/h5/miniprogram/orderPool/getParticipantDetail.json',
        data: {
          orderNo,
          jointId,
          kdtId,
        },
      })
      .catch(() => {
        return {
          jointOrderStatus: 0,
          participantBriefInfoList: [],
        };
      })
      .then((result) => {
        commit('SET_JOINT', {
          jointId,
          ...result,
        });
      });
  },

  // 隐藏申请退款按钮
  HIDDEN_REFUND_BTN({ commit, state }) {
    const { goodsList = [], processGoodsId = [] } = state;
    const newGoodsList = goodsList.map((list) => {
      if (processGoodsId.includes(list.goodsId)) {
        return {
          ...list,
          btns: list.btns.filter((item) => item.value !== BTN.refund.value),
        };
      }
      return list;
    });
    commit('SET_GOODSLIST', newGoodsList);
  },
};
