import WscComponent from 'pages/common/wsc-component';

import { node as request } from 'shared/utils/request';
import throttle from '@youzan/weapp-utils/lib/throttle';

const app = getApp();

WscComponent({
  properties: {
    sourcePage: String,
    orderId: String,
  },

  data: {
    showAnimation: false,

    showComponent: false,
    avatarList: [],

    // 用户数量和头像列表
    userCount: 0,
    avatars: [],

    // 动画效果第一个元素的位置
    animationIndex: 0,
    // 动画效果元素的范围
    animationRange: [],
  },

  observers: {
    // 展示动画效果
    'avatars.length': function (length) {
      this.setYZData({ showAnimation: length > 3 }, { immediate: true });
    },

    // 动画效果元素的范围
    'avatars.length, animationIndex': function (length, animationIndex) {
      const range = [
        animationIndex,
        animationIndex + 1,
        animationIndex + 2,
        animationIndex + 3,
      ].map((item) => (item > length - 1 ? item - length : item));
      this.setYZData({ animationRange: range }, { immediate: true });
    },

    // 元素列表
    'avatars, animationRange': function (avatars, animationRange) {
      let list = [];

      if (avatars.length <= 3) {
        list = avatars.map((avatar, index) => ({
          key: avatar,
          url: avatar,
          class: `avatar-${index}`,
        }));
      } else {
        list = avatars.map((avatar) => ({
          key: avatar,
          url: avatar,
          class: 'avatar__hide',
        }));
        animationRange.forEach((item, index) => {
          list[item].class = `avatar-${index}`;
        });
      }

      this.setYZData({ avatarList: list }, { immediate: true });
    },
  },

  lifetimes: {
    attached() {
      this.getWelikeInfo();
    },
  },

  pageLifetimes: {
    show() {
      this.updateAnimationIndex();
    },
  },

  methods: {
    fetchWelikeInfo() {
      return request({
        path: '/wsctrade/welike/entry-info.json',
        data: { sourcePage: this.data.sourcePage },
      });
    },

    getWelikeInfo() {
      this.fetchWelikeInfo().then((res) => {
        const { showEntry = false, userCount = 0, avatars = [] } = res || {};
        if (!showEntry || userCount === 0 || avatars.length === 0) {
          return;
        }

        this.setYZData(
          { showComponent: showEntry, userCount, avatars },
          { immediate: true }
        );

        this.triggerEvent('show');
        app.logger &&
          app.logger.log({
            et: 'view', // 事件类型
            ei: 'entry_view', // 事件标识
            en: '入口曝光', // 事件名称
            params: {
              source_page: 'order_list',
              component: 'welike_entry',
            }, // 事件参数
          });
      });
    },

    animationEventCallback: throttle(
      function () {
        this.updateAnimationIndex();
      },
      120,
      { leading: false }
    ),

    updateAnimationIndex() {
      const { avatars, animationIndex } = this.data;

      let newIndex = animationIndex;
      if (animationIndex >= avatars.length - 1) {
        newIndex = 0;
      } else {
        newIndex += 1;
      }
      this.setYZData({ animationIndex: newIndex }, { immediate: true });
    },

    navigateToWelikePage() {
      const kdtId = app.getKdtId();
      const path = '/packages/ump/welike/index';
      const url = kdtId ? `${path}?kdt_id=${kdtId}` : path;

      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'entry_click', // 事件标识
          en: '入口点击', // 事件名称
          params: {
            source_page: 'order_list',
            component: 'welike_entry',
          }, // 事件参数
        });
      wx.navigateTo({ url });
    },
  },
});
