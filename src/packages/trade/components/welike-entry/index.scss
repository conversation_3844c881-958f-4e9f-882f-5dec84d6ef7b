.welike-entry {
  margin-top: 10px;

  &::after {
    border-bottom: none;
  }

  & &-title {
    flex: 0 1 auto;
  }

  & &-value {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #323233;
  }

  .avatar-container {
    position: relative;
    width: 70px;
    height: 30px;
    margin-right: 8px;

    .avatar {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 28px;
      height: 28px;
      border: 1px solid #fff;
      border-radius: 14px;
    }

    .avatar__hide {
      display: none;
    }
  }

  .animation {
    .avatar {
      &-0 {
        left: 0;
        animation: 1.2s element-a linear forwards;
        z-index: 1;
      }

      &-1 {
        animation: 1.2s element-b linear forwards;
        z-index: 2;
      }

      &-2 {
        animation: 1.2s element-c linear forwards;
        z-index: 3;
      }

      &-3 {
        animation: 1.2s element-d linear forwards;
        z-index: 4;
      }
    }
  }

  .no-animation {
    .avatar {
      &-0 {
        right: 0;
        z-index: 3;
      }

      &-1 {
        right: 20px;
        z-index: 2;
      }

      &-2 {
        right: 40px;
        z-index: 1;
      }
    }
  }
}

@keyframes element-a {
  0% {
    left: 0;
    width: 28px;
    height: 28px;
    opacity: 1;
  }
  100% {
    left: 0;
    width: 0;
    height: 0;
    opacity: 0;
  }
}

@keyframes element-b {
  0% {
    left: 20px;
  }
  100% {
    left: 0;
  }
}

@keyframes element-c {
  0% {
    left: 40px;
  }
  100% {
    left: 20px;
  }
}

@keyframes element-d {
  0%,
  20% {
    right: 0;
    width: 0;
    height: 0;
    opacity: 0;
  }
  100% {
    right: 0;
    width: 28px;
    height: 28px;
    opacity: 1;
  }
}
