<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view wx:if="{{!loading}}">
    <view class="{{isKfc ? 'ticket-info__wrapper tpl-kfc' : 'ticket-info__wrapper'}}">
      <view class="inner-wrapper">
        <view class="ticket-header">
          <view class="ticket-info__title">
            {{currentTicketInfo.title}}
          </view>
          <view class="ticket-info__price">
            总价：¥{{currentTicketInfo.pay_price}} <text wx:if="{{!isKfc}}">( {{currentTicketInfo.total_count}}张 ）</text>
          </view>
          <view class="{{getTicketSuccess ? 'ticket-info__valid-time' : 'ticket-info__valid-time hidden-time'}}">
            有效期：{{currentTicketInfo.validity}}
          </view>
          <view wx:if="{{isKfc}}" class="ticket-info__remain-time {{getTicketSuccess ? '' : 'invisible'}}">
            剩余次数：{{currentTicketInfo.remain_times}}次
          </view>
          <view wx:if="{{isKfc}}" class="goods-image">
            <image src="{{currentTicketInfo.image_url}}" />
          </view>
          <view wx:else class="goods-image">
              <view class="icon-status-success" wx:if="{{currentTicketInfo.state === 'VERIFIED'}}"></view>
              <view class="icon-status-refund" wx:if="{{currentTicketInfo.state === 'DISABLED'}}"></view>
              <view class="icon-status-out-date" wx:if="{{currentTicketInfo.state === 'EXPIRED'}}"></view>
          </view>
        </view>
        <view class="gap-line">
           <view class="gap-text" wx:if="{{getTicketSuccess && currentTicketInfo.state === 'NOT_VERIFY'}}">使用时请向工作人员出示二维码或券码</view>
        </view>

        <view wx:if="{{getTicketSuccess}}">
          <view class="ticket-body">

            <view class="kfc-style" wx:if="{{isKfc}}">
              <view class="verify-code">
                核销码：{{currentTicketInfo.code}}
              </view>
              <view>
                <view
                  wx:if="{{showBarCode}}"
                  class="{{currentTicketInfo.state !== 'NOT_VERIFY' ? 'verify-bar-code invalid' : 'verify-bar-code'}}"
                >
                  <image bindtap="previewImage" src="{{currentTicketInfo.barcode}}" data-src="{{currentTicketInfo.barcode}}"/>
                  <view class="icon-status-success" wx:if="{{currentTicketInfo.state === 'VERIFIED'}}"></view>
                  <view class="icon-status-refund" wx:if="{{currentTicketInfo.state === 'DISABLED'}}"></view>
                  <view class="icon-status-out-date" wx:if="{{currentTicketInfo.state === 'EXPIRED'}}"></view>
                </view>
                <view
                  wx:else
                  class="{{currentTicketInfo.state !== 'NOT_VERIFY' ? 'verify-qr-code invalid' : 'verify-qr-code'}}"
                >
                  <image bindtap="previewImage" src="{{currentTicketInfo.qrcode}}" data-src="{{currentTicketInfo.qrcode}}"/>
                  <view class="icon-status-success" wx:if="{{currentTicketInfo.state === 'VERIFIED'}}"></view>
                  <view class="icon-status-refund" wx:if="{{currentTicketInfo.state === 'DISABLED'}}"></view>
                  <view class="icon-status-out-date" wx:if="{{currentTicketInfo.state === 'EXPIRED'}}"></view>
                </view>
              </view>
                                            <!-- 增加提示 -->
          <view class="ticket-safe-tips">
            <span>为保障您的权益，未消费前请不要将二维码/条形码 提供给商家</span>
          </view>
              <view
                class="verify-translate"
                bindtap="handleCodeBtnClick">
                点击使用
                <text wx:if="{{showBarCode}}">二维码</text>
                <text wx:else>条形码</text>
              </view>
              <view class="gap-line"></view>
            </view>

            <view wx:else>
              <view
                class="{{currentTicketInfo.state !== 'NOT_VERIFY' ? 'invalid' : ''}}"
              >
                <image wx:if="{{currentTicketInfo.barcode}}" class="verify-bar-code-image" bindtap="previewImage" src="{{currentTicketInfo.barcode}}" data-src="{{currentTicketInfo.barcode}}"/>
                <image wx:if="{{currentTicketInfo.qrcode}}" class="verify-qr-code-image" bindtap="previewImage" src="{{currentTicketInfo.qrcode}}" data-src="{{currentTicketInfo.qrcode}}"/>
              </view>
              <view class="{{currentTicketInfo.state !== 'NOT_VERIFY' ? 'verify-code invalid' : 'verify-code'}}">
                {{currentTicketInfo.code}}
              </view>
            </view>
            <view class="action-btn-wrap" wx:if="{{ currentTicketInfo.state === 'NOT_VERIFY'}}">
              <view
                wx:if="{{!isKfc}}"
                bindtap="handleSaveCodeClick"
                class="action-btn-item {{showAddWxPocket ? 'save-to-album' : 'save-to-album no-padding'}}">
                <view class="icon-save"></view>保存至相册
              </view>
              <view
                bindtap="handleWxPocketClick"
                wx:if="{{showAddWxPocket}}"
                class="action-btn-item add-to-card-pocket">
                <view class="icon-add-pocket"></view>添加到微信卡包
              </view>
              <view
                wx:if="{{isKfc}}"
                bindtap="toggleUsageIntroPopup"
                class="action-btn-item {{showAddWxPocket ? 'usage-intro' : 'usage-intro no-margin'}}"
              >
                <view class="icon-usage-intro"></view>使用说明
              </view>
            </view>
          </view>
        </view>

        <view wx:else class="ticket-body no-pseudo">
          <image
          class="ticket-status-image"
          src="{{ticketStatus === 1 ? 'https://img01.yzcdn.cn/public_files/2018/05/05/64c8bac3cf5b01b38cfea39bf78e4848.png' : 'https://img01.yzcdn.cn/public_files/2018/05/05/53f5b6bab4b90c2d9e64eee8fee78606.png'}}"/>
          <view
            class="ticket-status-title">{{ticketStatusMap[ticketStatus]}}</view>
          <view
            class="ticket-tips"
            wx:if="{{ticketStatus === 1}}">
            超过30分钟未出票，请咨询在线客服
          </view>
          <view wx:else class="ticket-tips">
            预计1-5个工作日完成
            <view class="ticket-tips-phone" bindtap="handlePhoneCallClick">
              <view class="icon-telphone"></view>
              {{currentTicketInfo.tel}}
            </view>
          </view>
          <view>
          </view>
        </view>
      </view>

      <view
        class="use-info"
        wx:if="{{!isKfc}}"
      >
        <view
          wx:if="{{currentTicketInfo.addressInfo.name}}"
          class="useinfo-list"
        >
          <view class="head big-text">可用地点</view>
          <view class="item row alignCenter">
            <view class="left">
              <view class="shop-name big-text">{{currentTicketInfo.addressInfo.name}}</view>
              <view>{{currentTicketInfo.addressInfo.detail}}</view>
            </view>
            <view class="right tel-icon" bindtap="handlePhoneCallClickForHexiao" data-tel="{{currentTicketInfo.service_phone}}">
              <image class="tel-icon" src="https://img01.yzcdn.cn/public_files/2018/07/11/628acc4d0fa59048f4e5021327b70676.png" />
            </view>
          </view>
        </view>

        <view
          wx:for="{{currentTicketInfo.ticketsList}}"
          wx:for-item="ticketsListItem"
          wx:key="{{index}}"
          wx:if="{{ticketsListItem.isShow}}"
          class="useinfo-list"
        >
          <view class="head big-text">{{ticketsListItem.name}}</view>
          <view class="{{ticketsListItem.name == '待使用' ? 'item' : 'gray item'}}">
            <view
              wx:for="{{ticketsListItem.list}}"
              wx:for-item="listItem"
              wx:key="{{index}}"
            >
              <view
                wx:if="{{ticketsListItem.name == '已使用'}}"
                class="flex-between"
              >
                <view>{{"券码" + (index + 1) + ': '+ listItem.ticket_code}}</view>
                <view>{{listItem.verify_time}}</view>
              </view>
              <view wx:else>
                {{"券码" + (index + 1) + ': '+ listItem}}
              </view>
            </view>
          </view>
        </view>

      </view>
    </view>

    <view
      wx:if="{{isKfc && getTicketSuccess && currentTicketInfo.state === 'NOT_VERIFY' && expandInfo.type_code && expandInfo.type_code != 1}}"
      class="use-wrapper"
    >
      <view class="use-title">立即使用</view>
      <view class="use-content" style="width: 100%">
        <view
          wx:for="{{expandInfo.button_d_t_o}}"
          wx:key="{{index}}"
          class="zan-cell zan-cell--access zan-cell--last-child use-content-item"
          data-index="{{index}}"
          bindtap="handleUseBtnClick"
        >
          <view class="zan-cell__bd use-item-wrap">
            <image src="{{item.icon_base64}}" class="use-content-img" />
            <text class="use-content-text">{{item.button_type}}</text>
          </view>
          <view class="zan-cell__ft"></view>
        </view>
      </view>
    </view>

    <view wx:if="{{isKfc && getTicketSuccess}}" class="kfc-goods-block">
      <view class="goods-list" style="width: {{currentTicketInfo.tickets.length * 110 + 10 + 'rpx'}};">
        <view
          wx:for="{{currentTicketInfo.tickets}}"
          wx:key="{{index}}"
          class="{{index === activeIndex ? 'active' : ''}} goods-item"
          data-index="{{index}}"
          bindtap="handleGoodsItemClick"
        >
          <image src="{{currentTicketInfo.image_url}}" />
        </view>
      </view>
    </view>
  </view>

  <view class="zan-popup verify-code-container zan-popup--bottom {{showUsageIntroPop && 'zan-popup--show'}}">
    <view bindtap="toggleUsageIntroPopup" class="zan-popup__mask"></view>
    <view class="zan-popup__container" style="width: 100%">
      <view>
        <view class="popup__header">使用说明</view>
        <view class="popup__content instructions-text">
          <text>{{currentTicketInfo.instructions}}</text>
        </view>
      </view>
      <theme-view
        custom-class="zan-btn zan-btn--danger ensure-btn"
        bg="main"
        color="main-text"
        bindtap="toggleUsageIntroPopup"
      >
        确定
      </theme-view>
    </view>
  </view>

  <canvas canvas-id="qrcode" style="width:148px; height: 148px;" class="canvas-temp"></canvas>
  <canvas canvas-id="barcode" style="width: 276px; height: 105px;" class="canvas-temp"></canvas>
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
