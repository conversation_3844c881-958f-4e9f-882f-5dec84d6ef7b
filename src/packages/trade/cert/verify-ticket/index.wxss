@import "shared/common/css/helper/index.wxss";

.inner-wrapper {
  padding: 0 20rpx;
}

.ticket-header {
  background: #fff;
  padding: 0 28rpx;
  box-sizing: border-box;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
  position: relative;
}

.ticket-header:after,
.ticket-body:after {
  content: '';
  display: block;
  position: absolute;
  width: 36rpx;
  height: 36rpx;
  background: #f9f9f9;
  border-radius: 36rpx;
  bottom: -18rpx;
  left: -18rpx;
  z-index: 10;
}
.ticket-safe-tips {
font-family: PingFangSC-Regular;
font-size: 12px;
color: #636566;
margin-bottom: 20px;
}
.ticket-header:before,
.ticket-body:before {
  content: '';
  display: block;
  position: absolute;
  width: 36rpx;
  height: 36rpx;
  background: #f9f9f9;
  border-radius: 36rpx;
  bottom: -18rpx;
  right: -18rpx;
  z-index: 10;
}

.ticket-body {
  background: #fff;
  padding-top: 26rpx;
  text-align: center;
  position: relative;
}

.gap-line {
  height: 1px;
  border-top: 1px dashed #dbdbdb;
  margin: 0 40rpx;
  position: relative;
  z-index: 10;
}

.gap-text {
  position: absolute;
  font-size: 22rpx;
  color: #333333;
  line-height: 32rpx;
  padding: 0 25rpx;
  top: -16rpx;
  left: 50%;
  margin-left: -190rpx;
  background: #fff;
}

.ticket-info__title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  line-height: 44rpx;
  margin-bottom: 16rpx;
}

.ticket-info__price {
  font-size: 28rpx;
  line-height: 40rpx;
  color: #000;
  margin-bottom: 16rpx;
}

.ticket-info__valid-time {
  font-size: 24rpx;
  color: #f5a623;
  line-height: 36rpx;
  padding-bottom: 34rpx;
}

.hidden-time.ticket-info__valid-time {
  visibility: hidden;
  line-height: 25rpx;
  padding-bottom: 0;
}

.verify-code {
  margin: 0 auto;
  margin-top: 15px;
  font-size: 32rpx;
  line-height: 32rpx;
  color: #333333;
  display: inline-block;
  position: relative;
  top: -25rpx;
}
.invalid.verify-code {
  opacity: 0.4;
}
.invalid.verify-code::before {
  content: ' ';
  display: inline-block;
  height: 3rpx;
  background-color: #333;
  opacity: 0.5;
  position: absolute;
  left: 0;
  right: 0;
  top: 16rpx;
}

.verify-bar-code {
  margin: 0 auto;
  text-align: center;
  margin-bottom: 48rpx;
  height: 210rpx;
  width: 552rpx;
  margin-top: 30rpx;
  position: relative;
}

.verify-bar-code-image {
  height: 210rpx;
  width: 552rpx;
}

.verify-qr-code {
  margin: 0 auto;
  text-align: center;
  margin-bottom: 48rpx;
  width: 296rpx;
  height: 296rpx;
  position: relative;
}

.invalid image {
  opacity: 0.4;
}

.verify-qr-code-image {
  width: 296rpx;
  height: 296rpx;
}

.verify-translate {
  margin: 0 auto;
  border-radius: 36rpx;
  border: 1px solid var(--icon, #323233);
  color: var(--icon, #323233);
  line-height: 40rpx;
  padding: 6rpx 36rpx;
  text-align: center;
  width: 268rpx;
  font-size: 28rpx;
  margin-bottom: 48rpx;
}

.ticket-bottom {
  background: #fff;
  height: 70rpx;
  padding: 0 38rpx;
  padding-top: 28rpx;
  border-bottom-left-radius: 10rpx;
  border-bottom-right-radius: 10rpx;
}

.view-card-code {
  color: #666;
  font-size: 28rpx;
  position: relative;
}

.icon-save {
  width: 34rpx;
  height: 30rpx;
  background: url(https://img01.yzcdn.cn/upload_files/2022/02/09/FhOIWP1CO92F2NAkJ63HFet7geNB.png);
  background-size: 34rpx 34rpx;
  display: inline-block;
  margin-right: 10rpx;
  position: relative;
  top: 0rpx;
}

.icon-add-pocket {
  width: 32rpx;
  height: 30rpx;
  background: url(https://img01.yzcdn.cn/upload_files/2022/02/09/FqauON3iWUHqGfvYxwP3Yeuum3sI.png);
  background-size: 32rpx 30rpx;
  display: inline-block;
  margin-right: 10rpx;
  vertical-align: middle;
  position: relative;
  top: -3rpx;
}

.save-to-album,
.add-to-card-pocket,
.usage-intro {
  font-size: 28rpx;
  color: var(--link, #576b95);
  display: inline-block;
  height: 40rpx;
  line-height: 40rpx;
}

.no-padding {
  padding-right: 0;
  border-right: none;
}

.tpl-kfc .usage-intro.no-margin {
  margin-left: 0;
}

.action-btn-wrap {
  padding-bottom: 32rpx;
  display: flex;
}

.action-btn-wrap .action-btn-item {
  flex: 1;
  position: relative;
}

.action-btn-item:not(:first-of-type)::after {
  content: '';
  position: absolute;
  top: 4px;
  bottom: 4px;
  left: 0;
  transform: translateX(-50%);
  border-left: 1px solid #ebedf0;
}

.ticket-info__title {
  padding-top: 28rpx;
}

.ticket-info__wrapper {
  padding-top: 20rpx;
}

.icon-arrow {
  width: 14rpx;
  height: 26rpx;
  background: url('https://b.yzcdn.cn/fix-base64/6b4c4ee97a572fa270d2eaa6e6de41a5a298ce6d0465b8ed91986bed85210a01.png');
  background-size: 14rpx 26rpx;
  position: absolute;
  right: 38rpx;
  top: 28rpx;
}

.verify-code-container .popup__header {
  height: 92rpx;
  line-height: 92rpx;
  font-size: #333;
  border-bottom: 1px solid #e7e7e7;
  text-align: center;
  font-size: 14px;
  color: #333;
}

.popup__content {
  padding: 40rpx 20rpx;
  max-height: 800rpx;
  overflow: scroll;
}

.popup__content .header {
  font-size: 24rpx;
  color: #666;
  margin: 10rpx 0 20rpx;
}

.popup__content .item {
  line-height: 68rpx;
  font-size: 28rpx;
  color: #333;
}

/*肯德基定制需求*/

.tpl-kfc .ticket-info__title {
  padding-top: 38rpx;
  max-width: 460rpx;
}

.tpl-kfc .ticket-info__price {
  max-width: 460rpx;
}

.tpl-kfc .ticket-info__valid-time {
  color: #666;
  padding-bottom: 0;
  margin-bottom: 12rpx;
  max-width: 460rpx;
}

.tpl-kfc .ticket-info__remain-time {
  color: var(--warn, #ee0a24);
  font-size: 28rpx;
  padding-bottom: 45rpx;
  max-width: 460rpx;
}

.tpl-kfc .verify-translate {
  line-height: 34rpx;
}

.tpl-kfc .verify-code {
  line-height: 19rpx;
  font-size: 24rpx;
}

.tpl-kfc .icon-usage-intro {
  width: 32rpx;
  height: 30rpx;
  display: inline-block;
  margin-right: 6rpx;
  background: url('https://img01.yzcdn.cn/upload_files/2022/02/09/FuaC3BQdHqafnDUMH68ypoDYDRIx.png');
  background-size: 32rpx 30rpx;
  position: relative;
  top: 4rpx;
}

.tpl-kfc .add-to-card-pocket {
  margin-left: 0;
  border-right: 1rpx solid #979797;
}

.tpl-kfc .verify-qr-code {
  margin-bottom: 26rpx;
}

.tpl-kfc .verify-translate {
  margin-bottom: 34rpx;
}



.tpl-kfc .ticket-body {
  border-bottom-left-radius: 10rpx;
  border-bottom-right-radius: 10rpx;
}
.tpl-kfc .ticket-body .gap-line {
  margin-bottom: 32rpx;
}

.tpl-kfc .ticket-body:after,
.tpl-kfc .ticket-body:before,
.no-pseudo.ticket-body:before,
.no-pseudo.ticket-body:after {
  display: none;
}

.tpl-kfc .action-btn-wrap {
  padding-bottom: 42rpx;
}

.goods-image image {
  width: 170rpx;
  height: 170rpx;
  position: absolute;
  right: 46rpx;
  top: 38rpx;
}

.goods-image .icon-status-success,
.goods-image .icon-status-refund,
.goods-image .icon-status-out-date {
  width: 176rpx;
  height: 138rpx;
  position: absolute;
  right: 46rpx;
  top: 38rpx;
}

.icon-status-success {
  background: url('https://b.yzcdn.cn/fix-base64/153a2607899fe1e67c7ee7750db642090641bf731b2eff59f277bec408a4d8da.png');
  background-size: 176rpx 138rpx;
}

.icon-status-refund {
  background: url('https://b.yzcdn.cn/fix-base64/06cc74d6dbf8b9b097c036a98beeb323c53e326a603ca3444484d530b405e88c.png');
  background-size: 176rpx 138rpx;
}

.icon-status-out-date {
  background: url('https://b.yzcdn.cn/fix-base64/c54b07568f47ccc64b2f6bffd100aa8c2ce6a12a6b1beb32ad436a1404a6cf74.png');
  background-size: 176rpx 138rpx;
}

.kfc-goods-block {
  width: auto;
  height: 98rpx;
  margin-top: 98rpx;
  text-align: center;
  overflow-x: scroll;
}

.kfc-goods-block .goods-list {
  width: 980rpx;
  height: 98rpx;
  min-width: 750rpx;
  overflow: hidden;
}

::-webkit-scrollbar{
  width: 0;
  height: 0;
  color: transparent;
}

.kfc-goods-block .goods-item image {
  width: 90rpx;
  height: 90rpx;
  border-radius: 18rpx;
  opacity: 0.4;
}

.kfc-goods-block .goods-item {
  display: inline-block;
  margin-right: 10px;
  height: 98rpx;
}

.kfc-goods-block .goods-item.active image{
  width: 90rpx;
  height: 90rpx;
  border-radius: 18rpx;
  border: 2px solid #d93d39;
  opacity: 1;
}

.icon-telphone {
  background: url(https://b.yzcdn.cn/fix-base64/6a751a5afdcf5f21f1293a307c5bc2208e36e4388a1706ad35f4ce5a76a31133.png);
  background-size: 24rpx 24rpx;
  width: 24rpx;
  height: 24rpx;
  display: inline-block;
  position: relative;
  left: 4rpx;
  top: 1rpx;
}

.ticket-status-image {
  width: 220rpx;
  height: 220rpx;
  margin: 0 auto;
  display: block;
  margin-top: 37rpx;
  margin-bottom: 26rpx;
}

.ticket-status-title {
  font-size: 40rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
  line-height: 56rpx;
}

.ticket-tips {
  font-size: 28rpx;
  color: #666;
  padding-bottom: 44rpx;
}

.ticket-tips-phone {
  display: inline-block;
  color: var(--link, #576b95);
}

.invisible {
  visibility: hidden;
  height: 25rpx;
  padding-bottom: 14rpx !important;
}

.canvas-temp {
  position: fixed;
  top: -1000px;
}

.instructions-text {
  font-size: 14px;
}
.use-info{
  margin-top:30rpx;
  color: #333;
}
.big-text{
  font-size: 28rpx;
  font-weight: 800;
}
.useinfo-list{
  margin-top: 20rpx;
  background-color: #fff;
}
.useinfo-list .head,
.useinfo-list .item {
  padding: 16rpx 20rpx;
  background-color: #fff;
}
.useinfo-list .head{
  border-bottom: 0.5rpx solid #e5e5e5;
}
.useinfo-list .item{
  font-size: 24rpx;
}
.useinfo-list .item.valid{
  opacity: 0.3;
}
.useinfo-list .item.row{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.useinfo-list .item.gray{
  color: #999999;
}
.alignCenter{
  align-items: center;
}
.useinfo-list .item .shop-name{
  font-size: 28rpx;
}
.use-info .tel-icon{
  width: 50rpx;
  height: 50rpx;
}
.use-info .flex-between{
  display: flex;
  justify-content: space-between;
}


.kfc-style .verify-bar-code .icon-status-success,
.kfc-style .verify-bar-code .icon-status-refund,
.kfc-style .verify-bar-code .icon-status-out-date {
  right: -106rpx;
  top: -30rpx;
}


.kfc-style .icon-status-success {
  background: url('https://b.yzcdn.cn/fix-base64/e9a998f53020c591b3a7eedca7579eac0218c32e81bb1df1dc09b7ff16c8d754.png');
  background-size: 176rpx 138rpx;
}


.kfc-style .icon-status-success,
.kfc-style .icon-status-refund,
.kfc-style .icon-status-out-date {
  width: 176rpx;
  height: 138rpx;
  position: absolute;
  top: 0;
  right: -126rpx;
}

.kfc-style .verify-qr-code.invalid image,
.kfc-style .verify-bar-code.invalid image {
  opacity: 0.4;
}

.kfc-style .verify-qr-code image {
  width: 296rpx;
  height: 296rpx;
}


.kfc-style .verify-code {
  margin: 0 auto;
  font-size: 28rpx;
  line-height: 40rpx;
  color: #666;
  padding: 16rpx 40rpx;
  background-color: #f8f7f7;
  margin-bottom: 12rpx;
  display: inline-block;
  top: 0
}


.kfc-style .verify-bar-code {
  margin: 0 auto;
  text-align: center;
  margin-bottom: 48rpx;
  height: 210rpx;
  width: 552rpx;
  margin-top: 30rpx;
  position: relative;
}
.kfc-style .verify-qr-code {
  margin: 0 auto;
  text-align: center;
  margin-bottom: 48rpx;
  width: 296rpx;
  height: 296rpx;
  position: relative;
}

.kfc-style .verify-bar-code image {
  height: 210rpx;
  width: 552rpx;
}

.kfc-style .verify-qr-code image {
  width: 296rpx;
  height: 296rpx;
}

.kfc-style .verify-qr-code.invalid image,
.kfc-style .verify-bar-code.invalid image {
  opacity: 0.4;
}

.use-wrapper {
  padding: 0 20rpx;
}
.use-wrapper .use-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  padding: 20rpx;
}
.use-wrapper .use-content {
  display: flex;
}
.use-wrapper .use-content-item:first-of-type {
  margin-right: 20rpx;
}
.use-wrapper .use-content-item {
  flex: 1;
  background: #fff;
  box-shadow: 4rpx 6rpx 10rpx #efefef;
  border-radius: 10rpx;
}
.use-content-item .use-item-wrap {
  display: flex;
}
.use-content-item .use-content-img {
  width: 110rpx;
  height: 87rpx;
}
.use-content-item .use-content-text {
  padding-left: 20rpx;
  padding-top: 24rpx;
}