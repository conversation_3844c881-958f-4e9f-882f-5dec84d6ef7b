import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
/* eslint-disable-next-line */
import { moment } from 'utils/time';

const app = getApp();

const ticketStatusMap = [
  '',
  '支付成功 正在出票...',
  '出票失败 已为你办理退款...',
];

// 从微信侧理解，1- 旧的券是通过微信js-sdk的卡券能力领取的微信卡券；2-新的类型是微信支付发放的商家券，领取方式不一样
const CARD_TYPE = {
  CARD: 1,
  COUPON: 2,
};

WscPage({
  data: {
    themeClass: app.themeClass,
    loading: true,
    showBarCode: false,
    showAddWxPocket: false,
    showVerifyCodePop: false,
    showUsageIntroPop: false,
    cardDetail: {},
    // kfc 定制相关字段
    isKfc: false,
    getTicketSuccess: true,
    ticketInfo: {},
    currentTicketInfo: {},
    activeIndex: 0,
    ticketStatus: 1,
    ticketStatusMap,
    expandInfo: {}, // 扩展信息
  },

  onLoad(query) {
    if (!query.order_no) {
      return wx.showModal(
        { content: '缺少必要参数', showCancel: false, confirmText: '返回' },
        () => wx.navigateBack()
      );
    }

    this.checkIsKfcShop().then(() => {
      if (this.data.isKfc) {
        this.fetchOuterCardInfo().then(() => {
          this.getCurrentTicketInfo(this.data.ticketInfo, 0);
          this.fetchCardPocketConfig();
        });
        this.fetchExpandinfo(query.order_no);
      } else {
        this.fetchCardInfo(query.order_no);
      }
    });
  },

  previewImage({ currentTarget }) {
    const code = currentTarget.dataset.src;
    wx.previewImage({
      current: code,
      urls: [code],
    });
  },

  fetchCardInfo(orderNo) {
    wx.showToast({
      title: '数据查询中',
      icon: 'loading',
      // 持续时间最长为10s
      duration: 10000,
    });
    this.setYZData({ loading: true });

    const _this = this;
    app
      .request({
        path: '/wsctrade/order/virtualTicket/detail.json',
        query: {
          orderNo,
        },
      })
      .then((res) => {
        const tickets = {
          NOT_VERIFY: [],
          VERIFIED: [],
          REFUNDING: [],
          DISABLED: [],
          EXPIRED: [],
        };
        const ticketsList = [];

        // 怕影响之前结构, 添加一个新的映射
        const stateMap = {
          NOT_VERIFY: { name: '待使用', list: [] },
          VERIFIED: { name: '已使用', list: [] },
          REFUNDING: { name: '退款中', list: [] },
          DISABLED: { name: '已失效', list: [] },
          EXPIRED: { name: '已过期', list: [] },
        };

        // 格式化数据
        if (res.bar_code) {
          res.barcode = res.bar_code;
        }
        if (res.qr_code) {
          res.qrcode = res.qr_code;
        }
        if (res.state === 'NOT_VERIFIED') {
          res.state = 'NOT_VERIFY';
        }

        if (res.code && res.tickets && Array.isArray(res.tickets)) {
          res.tickets.forEach((ticket) => {
            let _ticket = { ticket_code: ticket.ticket_code };
            // 格式化数据
            if (ticket.ticket_state === 'NOT_VERIFIED') {
              ticket.ticket_state = 'NOT_VERIFY';
            }
            if (ticket.ticket_state === 'VERIFIED') {
              _ticket.verify_time = moment(
                ticket.verify_time.replace(/-/g, '/'),
                'YYYY-MM-DD HH:mm:ss'
              );
            } else {
              _ticket = ticket.ticket_code;
            }
            tickets[ticket.ticket_state].push(_ticket);
            stateMap[ticket.ticket_state].list.push(_ticket);
          });
          res.tickets = tickets;
          // eslint-disable-next-line no-unused-vars
          for (const key in stateMap) {
            if (stateMap[key]) {
              stateMap[key].isShow = stateMap[key].list.length > 0;
              ticketsList.push(stateMap[key]);
            }
          }

          // 是否有核销地址
          const storeAddress = res.store_address;
          let addressInfo = null;
          if (storeAddress && !Array.isArray(storeAddress)) {
            const { province, city, area, address, name } = storeAddress;
            addressInfo = {
              name,
              detail: `${province}${city}${area}${address}`,
            };
          }
          Object.assign(res, {
            addressInfo,
            ticketsList,
          });

          _this.setYZData({
            currentTicketInfo: res,
          });
        } else {
          // 99 出单失败
          const ticketStatus = res.orderState !== 99 ? 1 : 2;
          _this.setYZData({
            getTicketSuccess: false,
            ticketStatus,
            currentTicketInfo: res,
            'currentTicketInfo.tel': res.service_phone || '',
          });
        }

        _this.setYZData({ loading: false });
        // 获取添加到卡包相关
        _this.fetchCardPocketConfig();
        wx.hideToast();
      })
      .catch((err) => {
        Toast(err.message || err.msg || '电子卡券信息获取失败');
        wx.hideToast();
        this.setYZData({ loading: false });
      });
  },

  fetchOuterCardInfo() {
    wx.showToast({
      title: '数据查询中',
      icon: 'loading',
      // 持续时间最长为10s
      duration: 10000,
    });
    this.setYZData({ loading: true });
    // kfc 小程序定制需求
    const _this = this;
    return new Promise((resolve, reject) => {
      app.carmen({
        api: 'youzan.ebiz.external.ticket.kfc/1.0.0/deatil',
        method: 'GET',
        data: {
          order_no: this.__query__.order_no,
        },
        success: (resp) => {
          if (resp.data.tickets.length === 0) {
            const ticketStatus = resp.data.state;
            // 支付成功
            this.setYZData({
              getTicketSuccess: false,
              ticketStatus,
              ticketInfo: resp.data,
            });
          } else {
            this.setYZData({
              getTicketSuccess: true,
              ticketInfo: resp.data,
            });
          }
          _this.setYZData({
            loading: false,
            showBarCode: resp.data.first_display === 'barcode',
          });
          wx.hideToast();
          resolve();
        },
        fail: (err) => {
          reject(err);
          Toast(err.message || err.msg || '获取卡券信息失败');
        },
      });
    });
  },

  handleCodeBtnClick() {
    const showBarCode = !this.data.showBarCode;
    this.setYZData({
      showBarCode,
    });
  },

  handleSaveCodeClick() {
    wx.showToast({
      title: '请点击后长按保存' + (this.data.showBarCode ? '条形码' : '二维码'),
      icon: 'none',
    });
  },

  handleWxPocketClick() {
    // 添加卡券至微信卡包
    const { timestamp, nonceStr, signature, cardType, mchId } =
      this.data.cardDetail;
    const { code } = this.data.currentTicketInfo || {};
    let cardExt = '{}';
    if (cardType === CARD_TYPE.COUPON) {
      cardExt = JSON.stringify({
        out_request_no: code,
        coupon_code: code,
        sign: signature,
        send_coupon_merchant: mchId,
      });
    } else {
      cardExt = JSON.stringify({
        code,
        timestamp,
        nonce_str: nonceStr,
        signature,
      });
    }

    wx.addCard({
      cardList: [
        {
          cardId: this.data.cardDetail.templateId,
          cardExt,
        },
      ],
      success() {
        wx.showToast({
          title: '成功添加至微信卡包',
          icon: 'none',
        });
      },
      fail(err) {
        if (err.errMsg !== 'addCard:fail cancel') {
          wx.showToast({
            title: err.errMsg,
            icon: 'none',
          });
        }
      },
    });
  },

  toggleUsageIntroPopup() {
    const showUsageIntroPop = !this.data.showUsageIntroPop;
    if (showUsageIntroPop && !this.data.currentTicketInfo.instructions) {
      return wx.showToast({
        title: '该商品没有使用说明',
        icon: 'none',
      });
    }
    this.setYZData({
      showUsageIntroPop,
    });
  },

  toggleVerifyCodePopup() {
    const showVerifyCodePop = !this.data.showVerifyCodePop;
    this.setYZData({
      showVerifyCodePop,
    });
  },

  // 保存图片 api 调用
  saveImage(tempFilePath) {
    return new Promise((resolve, reject) => {
      wx.saveImageToPhotosAlbum({
        filePath: tempFilePath,
        success: resolve,
        fail: reject,
      });
    });
  },

  // 下载图片获得临时路径
  loadImage(src) {
    const app = getApp();
    return new Promise((resolve, reject) => {
      app.downloadFile({
        url: src,
        success: (res) => {
          resolve(res.tempFilePath);
        },
        fail: (e) => {
          reject(e);
        },
      });
    });
  },

  // 获取当前应该展示的券码信息
  getCurrentTicketInfo(ticketData, index) {
    const currentTicketInfo = {
      ...ticketData,
    };

    const ticketStateMap = ['NOT_VERIFY', 'VERIFIED', 'DISABLED', 'EXPIRED'];

    const tickets = ticketData.tickets[index] || '';

    if (tickets) {
      currentTicketInfo.qrcode = tickets.qrcode;
      currentTicketInfo.barcode = tickets.barcode;
      currentTicketInfo.code = tickets.ticket_code;
      currentTicketInfo.state = tickets.ticket_state;
      currentTicketInfo.remain_times =
        tickets.total_count - tickets.verify_count;
      currentTicketInfo.state = ticketStateMap[tickets.ticket_state - 1];
    }

    this.setYZData({
      currentTicketInfo,
    });
  },

  handleGoodsItemClick(e) {
    const index = e.currentTarget.dataset.index;
    this.getCurrentTicketInfo(this.data.ticketInfo, index);
    this.setYZData({
      activeIndex: index,
    });
  },

  handlePhoneCallClick() {
    wx.makePhoneCall({
      phoneNumber: this.data.currentTicketInfo.tel.replace('-', ''),
    });
  },
  // 建议提供一个 纯函数类型 的拨打电话的事件
  handlePhoneCallClickForHexiao(e) {
    const tel = e.currentTarget.dataset.tel;
    wx.makePhoneCall({
      phoneNumber: tel.replace('-', ''),
    });
  },

  fetchCardPocketConfig() {
    // 获取当前券码是否可添加至微信卡包
    const cardData = {
      kdt_id: app.getKdtId(),
      order_no: this.__query__.order_no,
      card_no: this.data.currentTicketInfo.code,
    };
    const _this = this;
    app.carmen({
      api: 'youzan.wsc.trade.ticket/1.0.0/getsyncwx',
      method: 'POST',
      data: cardData,
      success(resp) {
        // 添加至微信卡包
        let showAddWxPocket = false;
        let cardDetail = {};
        if (Object.keys(resp).length > 0) {
          showAddWxPocket = true;
          cardDetail = resp;
        }
        _this.setYZData({
          showAddWxPocket,
          cardDetail,
        });
      },
      fail(err) {
        wx.showToast({
          title: err.message || err.msg || '获取同步微信卡包信息失败',
          icon: 'none',
        });
      },
    });
  },

  checkIsKfcShop() {
    return new Promise((resolve) => {
      app.carmen({
        api: 'youzan.ebiz.external.ticket.provider/1.0.0/get',
        success: (res) => {
          this.setYZData({
            isKfc: res.providerName === 'KFC',
          });
        },
        complete: () => {
          resolve();
        },
      });
    });
  },

  // 获取电子卡券类型（1.线下券，2.线上券，3.通用券）
  fetchExpandinfo(order_no) {
    this.tagPromise = app
      .request({
        path: '/wsctrade/kfc/item/id/expandinfo.json',
        data: {
          orderId: order_no,
        },
      })
      .then((res) => {
        this.setYZData({
          expandInfo: res,
        });
      })
      .catch(() => {
        console.log('err');
      });
  },

  handleUseBtnClick(e) {
    const index = e.currentTarget.dataset.index;
    wx.navigateToMiniProgram({
      appId: this.data.expandInfo.button_d_t_o[index].app_id,
      path: this.data.expandInfo.button_d_t_o[index].app_path,
    });
  },
});
