import { getKdtId } from '@/base-api/shop/kdt-id';

export const pointsMallGetKdtId = (query) => {
  // 如果query 里面有kdt_id 使用query 里面的kdt_id, 否则使用 getKdtId 获取
  const { kdt_id: kdtId } = query;
  return new Promise((resolve, reject) => {
    if (kdtId) {
      resolve(kdtId);
    } else {
      getKdtId()
        .then((res) => {
          resolve(res.current);
        })
        .catch((msg) => {
          reject(msg);
        });
    }
  });
};
