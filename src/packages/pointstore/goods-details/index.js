import WscPage from 'pages/common/wsc-page/index';
import args from '@youzan/weapp-utils/lib/args';
import { pointsMallGetKdtId } from '../utils';
import { getIsSupportPointStoreV2 } from '../api';

const app = getApp();

WscPage({
  data: {
    src: '',
  },

  onShareAppMessage() {
    return (
      this.__webviewMsg['ZNB.share'] || {
        path: '/packages/home/<USER>/index',
      }
    );
  },

  onShow() {
    Promise.all([getIsSupportPointStoreV2(), app.getPointsName()])
      .then(([isPointStoreV2, { pointsName = '积分' }]) => {
        if (!isPointStoreV2) {
          wx.setNavigationBarTitle({ title: `${pointsName}商品详情` });
        } else {
          wx.setNavigationBarTitle({ title: `兑换详情` });
        }
      })
      .catch(() => {
        wx.setNavigationBarTitle({ title: `积分商品详情` });
      });
  },

  onLoad(query = {}) {
    const srcQuery = {};
    const { goods_id: goodsId } = query;
    if (goodsId) {
      srcQuery.goods_id = goodsId;
    }

    pointsMallGetKdtId(query).then((kdtId) => {
      this.setYZData({
        src: args.add('/wscump/pointstore/goodsdetails', {
          ...srcQuery,
          kdt_id: kdtId,
        }),
      });
    });
  },
});
