import WscPage from 'pages/common/wsc-page/index';
import { getIsSupportPointStoreV2 } from '../api';

const app = getApp();

WscPage({
  data: {
    src: '',
  },

  onShareAppMessage() {
    return (
      this.__webviewMsg['ZNB.share'] || {
        path: '/packages/home/<USER>/index',
      }
    );
  },

  onShow() {
    Promise.all([getIsSupportPointStoreV2(), app.getPointsName()])
      .then(([isPointStoreV2, { pointsName = '积分' }]) => {
        if (!isPointStoreV2) {
          wx.setNavigationBarTitle({ title: `${pointsName}明细` });
        }
      })
      .catch(() => {
        wx.setNavigationBarTitle({ title: `积分明细` });
      });
  },

  onLoad(query = {}) {
    const { kdt_id: kdtId } = query;
    this.setYZData({
      src: `/wscump/pointstore/pointdetails?kdt_id=${kdtId}`,
    });
  },
});
