import WscPage from 'pages/common/wsc-page/index';
import args from '@youzan/weapp-utils/lib/args';
import { pointsMallGetKdtId } from '../utils';
import { getIsSupportPointStoreV2 } from '../api';

const app = getApp();

WscPage({
  data: {
    src: '',
  },

  onShareAppMessage() {
    return (
      this.__webviewMsg['ZNB.share'] || {
        path: '/packages/home/<USER>/index',
      }
    );
  },

  onShow() {
    Promise.all([getIsSupportPointStoreV2(), app.getPointsName()])
      .then(([isPointStoreV2, { pointsName = '积分' }]) => {
        if (!isPointStoreV2) {
          wx.setNavigationBarTitle({ title: `花${pointsName}` });
        }
      })
      .catch(() => {
        wx.setNavigationBarTitle({ title: `花积分` });
      });
  },

  onLoad(query = {}) {
    const { tab } = query;
    const srcQuery = {};

    if (tab) {
      srcQuery.tab = tab;
    }

    pointsMallGetKdtId(query).then((kdtId) => {
      this.setYZData({
        src: args.add('/wscump/pointstore', {
          ...srcQuery,
          kdt_id: kdtId,
        }),
      });
    });
  },
});
