import WscPage from 'pages/common/wsc-page/index';
import request from '@youzan/tee-biz-request';
import { pointsMallGetKdtId } from '../utils';
import args from '@youzan/weapp-utils/lib/args';
import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';
import { getIsSupportPointStoreV2 } from '../api';

const app = getApp();

const pointMallPath = '/wscshop/point-mall-showcase';

WscPage({
  // 默认埋点标题
  logConfig: {
    title: '积分商城',
  },

  data: {
    src: '',
  },

  onShareAppMessage() {
    return (
      this.__webviewMsg['ZNB.share'] || {
        path: '/packages/home/<USER>/index',
      }
    );
  },

  onShow() {
    Promise.all([getIsSupportPointStoreV2(), app.getPointsName()])
      .then(([isPointStoreV2, { pointsName = '积分' }]) => {
        if (!isPointStoreV2) {
          wx.setNavigationBarTitle({ title: `${pointsName}中心` });
        }
      })
      .catch(() => {
        wx.setNavigationBarTitle({ title: `积分中心` });
      });
  },

  onLoad(query = {}) {
    const redirectUrl = args.add('/' + this.__route__, query);
    autoEnterShop({
      ...query,
      redirectUrl,
    }).then((targetKdtId) => {
      pointsMallGetKdtId(query).then((kdtId) => {
        request({
          path: '/wscuser/scrm/useNewPointsMall.json',
          query: {
            checkKdtId: targetKdtId || kdtId,
          },
        })
          .then((res) => {
            const path = res ? pointMallPath : '/wscump/pointstore/pointcenter';
            this.setYZData({
              src: `${path}?kdt_id=${targetKdtId || kdtId}`,
            });
          })
          .catch(() => {
            // 默认跳转至新积分商城
            this.setYZData({
              src: `${pointMallPath}?kdt_id=${targetKdtId || kdtId}`,
            });
          });
      });
    });
  },
});
