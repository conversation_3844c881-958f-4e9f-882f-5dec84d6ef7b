.exchange-container {
  padding: 40px 16px 0;
  font-size: 14px;
  background-color: #fff;
  min-height: calc(100vh - 146px);
  box-sizing: border-box;
}

.result {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;

  &-icon {
    font-size: 20px;
  }

  &-text {
    height: 20px;
    line-height: 20px;
    margin-left: 5px;
    color: #333;
  }
}

.actions {
  text-align: center;
}

.theme-view-wrap {
  overflow: hidden;
  width: 170px;
  margin: auto;
  border-radius: 20px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  margin-top: 40px;
  margin-bottom: 10px;
}

.sync-wx-card {
  height: 20px;
  line-height: 20px;
  color: #666;
}

.card-details {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;

  .card-cover {
    width: 100%;
    height: auto;
    min-height: 136px;
    object-fit: cover;
  }

  &-info {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    box-sizing: border-box;
    padding: 20px 20px 18px;
    text-align: left;
    color: #fff;
    font-size: 12px;
    font-family: PingFangSC-Regular, sans-serif;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .name {
      line-height: 20px;
      font-size: 18px;
      font-family: PingFangSC-Medium, sans-serif;
    }

    .expired {
      margin-top: 6px;
      line-height: 16px;
    }
  }

  .notes {
    display: inline-block;
    padding: 2px 15px;
    line-height: 20px;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
    border-radius: 12px;
    text-align: center;
  }
}

.coupon-details {
  width: 100%;
  height: 100px;
  padding: 24px 25px;
  box-sizing: border-box;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  background: #fff;
  border-radius: 4px;
  display: flex;
  justify-content: flex-start;
  font-family: PingFangSC-Redular, sans-serif;
  font-size: 12px;
  color: #999;

  .left-info {
    margin-right: 21px;

    .notes {
      height: 30px;

      .big {
        font-size: 30px;
        font-family: PingFangSC-Semibold, sans-serif;
        word-break: keep-all;
      }

      .exchange-coupon {
        font-size: 20px;
      }

      .delivery-coupon {
        font-size: 16px;
        line-height: 32px;
      }
    }

    .condition {
      margin-top: 6px;
      height: 16px;
      line-height: 16px;
    }
  }

  .right-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: left;

    .coupon-name {
      font-family: PingFangSC-Regular, sans-serif;
      line-height: 20px;
      color: #323232;
      font-size: 14px;
    }

    .coupon-expired {
      height: 16px;
      line-height: 16px;
      color: #323232;
    }
  }
}

@media only screen and (max-width: 320px) {
  .coupon-details {
    padding: 24px 10px;
  }
}
