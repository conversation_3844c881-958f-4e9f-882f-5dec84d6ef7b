<view class="exchange-container">
	<view wx:if="{{ show }}">
		<view class="result">
			<theme-view color="main-bg">
				<van-icon name="checked" class="result-icon" />
			</theme-view>
			<text class="result-text">兑换成功</text>
		</view>
		<view wx:if="{{ goodsType === 2 }}" class="coupon-details">
			<view class="left-info">
				<theme-view color="main-bg" bindtap="handleClickUse">
					<view class="notes">
						<block wx:if="{{ preferentialType === 1 || preferentialType === 2 }}">
							<text>
								<text class="big">{{ descriptionInfo.denomination }}</text>
								{{ descriptionInfo.unit }}
							</text>
						</block>
						<block wx:elif="{{ preferentialType === 3 }}">
							<text class="exchange-coupon">兑换商品</text>
						</block>
            <block wx:elif="{{ preferentialType === 5 }}">
							<text class="delivery-coupon">{{ deliveryCouponInfo.text }}</text>
              {{ deliveryCouponInfo.unit }}
						</block>
					</view>
				</theme-view>
				<view class="condition">
					{{ descriptionInfo.condition }}
				</view>
			</view>
			<view class="right-info">
				<view class="coupon-name">
					{{ goodsDetails.groupName }}
				</view>
				<view class="coupon-expired">
					{{ goodsDetails.termDesc }}
				</view>
			</view>
		</view>
		<view wx:if="{{ goodsType === 3 }}" class="card-details">
			<image src="{{ cardCoverUrl }}" class="card-cover" mode="widthFix" />
			<view class="card-details-info">
				<view class="title">
					<text class="name">
          {{ goodsDetails.cardName }}
        </text>
					<text class="notes">¥{{ price }}</text>
				</view>
				<text class="expired">
        {{ goodsDetails.termDesc }}
      </text>
			</view>
		</view>
		<view class="actions">
			<view class="theme-view-wrap">
				<theme-view bg="main-bg" color="main-text" bindtap="handleClickUse">
					<view>完成</view>
				</theme-view>
			</view>
			<view wx:if="{{ canSyncWX && isSyncWX }}" class="sync-wx-card" bindtap="addCardWithSDK">
				添加到微信卡包
			</view>
		</view>
	</view>
	<subscribe-guide show="{{ showSubscribeMask }}" bind:close="toggleSubscribeMask" />
</view>

