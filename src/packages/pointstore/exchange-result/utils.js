import fullfillImage from '@youzan/utils/url/fullfillImage';
import get from '@youzan/weapp-utils/lib/get';
import accDiv from '@youzan/utils/number/accDiv';

export const NEW_COLORS = {
  Color200: {
    direction: '135deg',
    colors: ['#B7D0FF', '#4E67B7'],
    bgColor: '#526AB9',
    imageUrl:
      '//img01.yzcdn.cn/public_files/2019/03/11/704e415d887541d2bf9ce945483af305.png',
  },
  Color201: {
    direction: '135deg',
    colors: ['#959595', '#20242A'],
    bgColor: '#444',
    imageUrl:
      '//img01.yzcdn.cn/public_files/2019/03/11/fb7d33fa35a5c118ba0495c09cdd1b3e.png',
  },
  Color202: {
    direction: '135deg',
    colors: ['#8BECBD', '#319E69'],
    bgColor: '#2F835A',
    imageUrl:
      '//img01.yzcdn.cn/public_files/2019/03/11/7ac9de05b089cb13d36323d6864b8d49.png',
  },
  Color203: {
    direction: '135deg',
    colors: ['#FF9E97', '#C44E46'],
    bgColor: '#8B3C36',
    imageUrl:
      '//img01.yzcdn.cn/public_files/2019/03/11/17fbdf65817fef6f4bd7acdb8fa414ab.png',
  },
  Color204: {
    direction: '135deg',
    colors: ['#FFDAA6', '#CE9A51'],
    bgColor: '#8C714B',
    imageUrl:
      '//img01.yzcdn.cn/public_files/2019/03/11/659d46363828ac8f571bd4a13ad89458.png',
  },
  Color205: {
    direction: '135deg',
    colors: ['#FFB5C1', '#D73B5B'],
    bgColor: '#9D4456',
    imageUrl:
      '//img01.yzcdn.cn/public_files/2019/03/11/7646eb0f59b1ce45d3cfbcb67686e93d.png',
  },
  Color206: {
    direction: '135deg',
    colors: ['#FCFCFC', '#A8A8A8'],
    bgColor: '#444444',
  },
};

export const getCardCoverUrl = (colorCode = 'Color200', coverUrl) => {
  if (coverUrl) {
    return fullfillImage(coverUrl, 'middle');
  }
  const defaultCoverUrl = NEW_COLORS.Color200.imageUrl;
  return fullfillImage(
    get(NEW_COLORS, `${colorCode}.imageUrl`, defaultCoverUrl),
    'middle'
  );
};

export const CouponMetaInfo = {
  // 优惠券类型
  preferentialType: {
    denominations: 1, // 代金券
    discount: 2, // 折扣券
    exchange: 3, // 兑换券
    meetGive: 4, // 买赠券
  },
  // 限制类型
  thresholdType: {
    none: 0, // 无门槛
    amount: 1, // 满多少元
    count: 2, // 满件
    /** 满赠 & 满元 （买赠券才有） */
    all: 3,
  },
  thresholdValueKey: {
    none: '',
    amount: 'condition',
    count: 'thresholdPiece',
  },
};

export const getCouponUnit = (couponInfo) => {
  switch (couponInfo.preferentialType) {
    case CouponMetaInfo.preferentialType.denominations:
      return '元';
    case CouponMetaInfo.preferentialType.discount:
      return '折';
    case CouponMetaInfo.preferentialType.meetGive:
      return `送${couponInfo.exchangeablePiece}件`;
    default:
      return '';
  }
};

export const getCouponCondition = (couponInfo) => {
  const { thresholdMaxPiece } = couponInfo;
  switch (couponInfo.thresholdType) {
    case CouponMetaInfo.thresholdType.none:
      return '无门槛';
    case CouponMetaInfo.thresholdType.amount:
      return `满${accDiv(
        couponInfo[CouponMetaInfo.thresholdValueKey.amount],
        100
      )}元使用`;
    case CouponMetaInfo.thresholdType.count:
      return `满${couponInfo[CouponMetaInfo.thresholdValueKey.count]}件使用${
        thresholdMaxPiece ? `,超过${thresholdMaxPiece}不可用` : ''
      }`;
    case CouponMetaInfo.thresholdType.all:
      return `满${accDiv(
        couponInfo[CouponMetaInfo.thresholdValueKey.amount],
        100
      )}元使用`;
    default:
      return '';
  }
};

export const getCouponDenomination = (couponInfo) => {
  switch (couponInfo.preferentialType) {
    case CouponMetaInfo.preferentialType.denominations:
      return accDiv(couponInfo.denominations || 0, 100);
    case CouponMetaInfo.preferentialType.discount:
      return accDiv(couponInfo.discount || 0, 10);
    case CouponMetaInfo.preferentialType.exchange:
      return '商品兑换券';
    case CouponMetaInfo.preferentialType.meetGive:
      return `买${couponInfo.thresholdPiece}件`;
    default:
      return '';
  }
};

export const getCouponDenominationText = (couponInfo) => {
  const value = getCouponDenomination(couponInfo);
  switch (couponInfo.preferentialType) {
    case CouponMetaInfo.preferentialType.denominations:
      return `减${value}元`;
    case CouponMetaInfo.preferentialType.discount:
      return `打${value}折`;
    case CouponMetaInfo.preferentialType.exchange:
      return value;
    default:
      return '';
  }
};

// 格式化优惠券展示描述信息
export const getCouponDescriptionInfo = (couponInfo) => {
  const unit = getCouponUnit(couponInfo);
  const condition = getCouponCondition(couponInfo);
  const denomination = getCouponDenomination(couponInfo);
  const denominationText = getCouponDenominationText(couponInfo);

  return {
    unit,
    condition,
    denomination,
    denominationText,
  };
};

export const getDeliveryCouponInfo = (couponInfo) => {
  const { denominations, preferenceCopywriting } = couponInfo;
  if (denominations) {
    return {
      text: accDiv(denominations || 0, 100),
      unit: '元',
    };
  }
  return { text: preferenceCopywriting, unit: '' };
};
