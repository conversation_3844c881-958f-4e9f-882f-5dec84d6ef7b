import WscPage from 'pages/common/wsc-page/index';
import navigate from '@/helpers/navigate';
import get from '@youzan/weapp-utils/lib/get';
import Toast from '@vant/weapp/dist/toast/toast';
import { getPlugins } from '@youzan/ranta-helper-tee';
import args from '@youzan/weapp-utils/lib/args';
import openWebView from 'utils/open-web-view';
import { wscSubscribeMessage } from '@/utils/subscribe-message';
import invert from 'lodash/invert';
import accDiv from '@youzan/utils/number/accDiv';
import {
  getCardCoverUrl,
  getCouponDescriptionInfo,
  getDeliveryCouponInfo,
} from './utils';
import {
  getResultGoods,
  getPointGoods,
  getJspApiTicket,
  getSyncTicket,
  getCouponNavigateUrl,
} from './api';

// 积分商品类型
const goodsTypeMap = {
  general: 1, // 普通商品
  coupon: 2, // 优惠券/码
  card: 3, // 权益卡
  cross_coupon: 4, // 跨店优惠券
};

// 优惠券/码
const couponTypeMap = {
  card: 0, // 优惠券
  code: 1, // 优惠码
  thirdparty: 2, // 三方券
};

const GroupCouponTypeMap = invert(couponTypeMap);

const app = getApp();

// 积分商城兑换结果页，逻辑和h5保持一致
WscPage({
  data: {
    kdtId: app.getKdtId(),
    isSyncWX: false, // 是否能够同步卡包，获取到openid时为true,
    canSyncWX: false, // 判断是否获取到openid
    show: false,
    showSubscribeMask: false,
    goodsType: undefined,
    goodsDetails: {},
    // 卡相关字段
    cardId: '',
    cardData: {},
    cardAlias: '',
    cardCoverUrl: '',
    price: 0,
    // 券相关字段
    preferentialType: 0,
    descriptionInfo: {},
    deliveryCouponInfo: {},
  },

  onLoad(query = {}) {
    const {
      kdt_id: kdtId,
      goodsId = '',
      groupType = '',
      couponId = '',
      cardId = '',
      couponGroupId = '',
      couponType = '',
    } = query;

    this.query = query;

    // 更新店铺
    app.updateKdtId(kdtId);

    Promise.all([
      getResultGoods({ cardId, groupType, couponId }),
      getPointGoods({ goodsId }),
    ])
      .then(([params, pointGoods]) => {
        const {
          isSyncWx = false,
          cardNo,
          templateId,
          verifyCode,
          cardAlias,
          isToH5,
        } = params;

        const goodsType = get(pointGoods, 'goodsType', -1);

        // 跳转回h5页面
        if (isToH5 || goodsType === goodsTypeMap.cross_coupon) {
          // 大客定制 || crm店铺
          openWebView('/wscump/pointstore/exchangeresult', {
            method: 'redirectTo',
            query: {
              kdt_id: kdtId,
              cardId,
              goodsId,
              groupType,
              couponId,
              couponGroupId,
              couponType,
            },
          });
          return;
        }

        this.setYZData({
          isSyncWX: isSyncWx,
          cardAlias,
          goodsType,
          show: true,
        });

        if (goodsType === goodsTypeMap.coupon) {
          const coupon = get(pointGoods, 'couponGroupInfoDTO', {});
          this.setYZData({
            goodsDetails: coupon,
            preferentialType: coupon.preferentialType || 0,
            descriptionInfo: getCouponDescriptionInfo(coupon),
            deliveryCouponInfo: getDeliveryCouponInfo(coupon),
          });
          isSyncWx && this.fetchSyncWXCoupon(couponGroupId, verifyCode);
        } else if (goodsType === goodsTypeMap.card) {
          const card = get(pointGoods, 'benefitCardInfoDTO', {});
          this.setYZData({
            goodsDetails: card,
            cardCoverUrl: getCardCoverUrl(
              card.backgroundColorCode,
              card.backgroundImg
            ),
            price: accDiv(card.sellingPrice || 0, 100),
          });
          isSyncWx && this.fetchSyncWXCard(templateId, cardNo);
        }
      })
      .catch((msg) => {
        Toast.fail(msg || '网络错误');
      });
  },

  // 获取权益卡同步微信卡包需要的参数
  fetchSyncWXCard(templateId, cardNo) {
    getSyncTicket({ templateId, cardNo })
      .then((data) => {
        const params = {
          code: data.card_no,
          openid: data.open_id,
          timestamp: data.timestamp,
          signature: data.signature,
          nonce_str: data.nonce_str,
        };
        this.setYZData({
          canSyncWX: !!data.open_id,
          cardData: params,
          cardId: templateId,
        });
      })
      .catch((err) => {
        app.logger &&
          app.logger.appError({
            name: '兑换结果页获取信息异常',
            message: '获取权益卡同步微信卡包需要的参数失败',
            detail: {
              errMsg: err?.msg,
              params: {
                templateId,
                cardNo,
              },
            },
          });
      });
  },

  // 获取券码同步微信卡包参数
  fetchSyncWXCoupon(couponGroupId, verifyCode) {
    getJspApiTicket({
      groupId: couponGroupId,
      verifyCode,
    })
      .then((res) => {
        const {
          cardId,
          nonceStr,
          signature,
          timestamp,
          verifyCode: code,
        } = res;
        const cardData = {
          nonce_str: nonceStr,
          signature,
          timestamp,
          code,
        };
        this.setYZData({
          cardData,
          cardId,
          canSyncWX: true,
        });
      })
      .catch((err) => {
        app.logger &&
          app.logger.appError({
            name: '兑换结果页获取信息异常',
            message: '获取券码同步微信卡包参数失败',
            detail: {
              errMsg: err?.msg,
              params: {
                groupId: couponGroupId,
                verifyCode,
              },
            },
          });
      });
  },

  toggleSubscribeMask(showSubscribeMask) {
    this.setYZData({
      showSubscribeMask,
    });
  },

  subscribe(next) {
    const params = {
      scene: 'point_center_and_point_exchange',
      windowType: 'point_center',
      authorizationType: 'points',
      subscribePage: '积分兑换成功页',
      subscribeType: '积分',
      options: {
        next,
        onShowTips: () => this.toggleSubscribeMask(true),
        onCloseTips: () => this.toggleSubscribeMask(false),
        onComplete: next,
      },
    };
    wscSubscribeMessage(params);
  },

  // 同步微信卡包
  addCardWithSDK() {
    const { cardId, cardData } = this.data;
    const wxCardData = [
      {
        cardId,
        cardExt: JSON.stringify(cardData),
      },
    ];
    navigate.navigate({
      url: `/packages/pointstore/sync-wxcard/index?data=${JSON.stringify(
        wxCardData
      )}`,
    });
  },

  // 点击完成
  handleClickUse() {
    const { goodsType, cardAlias, goodsDetails } = this.data;
    const { dmc } = getPlugins();
    const next = () => {
      if (goodsType === goodsTypeMap.card) {
        navigate.navigate({
          url: args.add('/pages/membercard/detail/index', {
            alias: cardAlias,
            kdt_id: this.query.kdtId,
          }),
        });
      } else if (goodsType === goodsTypeMap.coupon) {
        Toast.loading('正在跳转');
        const couponId = get(this.query, 'couponId');
        const couponType = get(this.query, 'couponType');
        // 降级处理
        const groupType = get(goodsDetails, 'groupType', couponTypeMap.card);
        getCouponNavigateUrl({
          couponId,
          groupType: GroupCouponTypeMap[couponType || groupType],
        })
          .then((res) => {
            const { weappUrl, isSwitchTab } = res;
            Toast.clear();
            if (isSwitchTab) {
              navigate.switchTab({ url: weappUrl });
            }
            navigate.navigate({ url: weappUrl });
          })
          .catch((msg) => {
            Toast.fail(msg);
          });
      } else {
        dmc.switchTab('Home');
      }
    };
    // 唤起消息订阅
    this.subscribe(next);
  },
});
