const app = getApp();

// 获取前置详情
export const getResultGoods = (data) => {
  return app.request({
    path: '/wscump/pointstore/getResultPagePreDataForWeapp.json',
    data,
  });
};

// 获取积分商品详情
export const getPointGoods = (data) => {
  return app.request({
    path: '/wscump/pointstore/getPointGoods.json',
    data,
  });
};

// 券码获取同步微信参数
export const getJspApiTicket = (data) => {
  return app.request({
    path: '/wscump/coupon/get_js_ticket.json',
    data,
  });
};

// 获取权益卡同步微信参数
export const getSyncTicket = (data) => {
  return app.request({
    path: '/wscuser/wx/getSyncTicket.json',
    data,
  });
};

export const getCouponNavigateUrl = (data) => {
  return app.request({
    path: '/wscump/coupon/coupon_use_redirect.json',
    data,
  });
};
