import WscPage from 'pages/common/wsc-page/index';

WscPage({

  onLoad(query = {}) {
    const { data } = query;
    const cardData = JSON.parse(data);
    wx.addCard({
      cardList: cardData,
      success() {
        wx.navigateBack();
      },
      fail(e) {
        console.log(e);
        if (e.errMsg !== 'addCard:fail cancel') {
          wx.showToast({
            title: '同步卡包失败',
            duration: 2000,
          });
        }
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    });
  }
});
