.setting {
  padding-top: 10px;

  .switch-cell {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .sync-btn {
    border: 0;
    height: 44px;
    line-height: 44px;
  
    &:disabled {
      color: #333;
    }
  }
}

.switch-container {
  padding: 0;
  border-radius:0;
  background-color:transparent;
  font-size: inherit;
  color: inherit;
  line-height: 1;
  text-align: right;
}

.switch-container::after {
  border: none;
}

.switch-status {
  font-size: 12px;
  color: #888;
  margin: 10px 15px 20px;
}

.value-class {
  flex: none;
  overflow: visible;
  position: absolute;
  top: 0;
  right: 0;
}

.sync-status {
  font-size: 12px;
  color: #323233;
  margin: 10px 0 5px;
  padding: 0 15px;
}

.sync-tip {
  font-size: 12px;
  color: #888;
  padding: 0 15px;
}
