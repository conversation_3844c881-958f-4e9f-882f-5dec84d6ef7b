<page-container
  page-container-class="setting"
>
  <van-cell
    center
    border="{{ false }}"
    title="允许同步信息至好物圈"
    custom-class="switch-cell"
  >
    <button
      class="switch-container"
      open-type="{{ needOpenSetting ? 'openSetting' : '' }}"
    >
      <van-switch
        active-color="#09BB07"
        size="24px"
        checked="{{ authed }}"
        loading="{{ authLoading }}"
        bindtap="toggleAuthHandler"
      />
    </button>
  </van-cell>

  <view
    class="switch-status"
  >
    关闭后，将清空已同步的订单等服务信息，并终止同步。
  </view>

  <block
    wx:if="{{ authed }}"
  >
    <van-cell
      title="同步半年内历史订单至好物圈"
      value-class="value-class"
      border="{{ false }}"
    >
      <van-button
        custom-class="sync-btn"
        plain
        disabled="{{ !syncBtn }}"
        loading="{{ syncLoading }}"
        bind:click="syncHistory"
      >
        {{ syncBtn ? '立即同步' : statusMap[status] }}
      </van-button>
    </van-cell>
    <view
      wx:if="{{ updateTime }}"
      class="sync-status"
    >
      上一次历史订单同步时间：{{ updateTime }}
    </view>
    <view
      class="sync-tip"
    >
      好物圈将展示允许同步后产生的小程序订单。如果你希望在好物圈内查看或管理在此之前产生的订单，可以选择将半年内小程序历史订单同步至好物圈。
    </view>
  </block>
</page-container>

<van-toast id="van-toast" />
