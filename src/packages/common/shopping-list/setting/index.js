import Toast from '@vant/weapp/dist/toast/toast';
import { moment } from '@youzan/weapp-utils/lib/time';
import WscPage from 'pages/common/wsc-page/index';
import { queryRecord, syncHistoryOrder } from '../fetch';

const statusMap = {
  '-1': '网络异常',
  1: '正在同步',
  99: '同步失败',
  100: '已同步'
};

const SCOPE_SHOPPING_LIST = 'scope.shoppingList';
const app = getApp();

WscPage({
  data: {
    statusMap,
    inited: false,

    // 授权按钮状态
    authed: false,
    authLoading: false,
    needOpenSetting: false,

    // 同步按钮状态
    syncBtn: false,
    syncLoading: false,
    updateTime: '',
    status: 0
  },

  onLoad() {
    this.getSyncHistoryStatus();
  },

  onShow() {
    // 关闭 / 打开 授权会跳转微信设置页，所以每次页面显示都刷新下开关状态
    this.getAuthStatus();
  },

  onPullDownRefresh() {
    this.getAuthStatus();
    this.getSyncHistoryStatus();
  },

  onUnload() {
    clearTimeout(this.getStatusTimer);
  },

  getAuthStatus() {
    if (this.data.authLoading) {
      return;
    }
    this.setYZData({
      authLoading: true
    });
    wx.getSetting({
      success: ({ authSetting }) => {
        const status = authSetting[SCOPE_SHOPPING_LIST];
        const authed = !!status;
        // 已弹过微信授权弹窗
        if (typeof status === 'boolean') {
          this.setYZData({
            needOpenSetting: true
          });
        }

        this.setYZData({
          authed
        });
        app.trigger('shopping-list:auth:change', authed);
      },
      complete: () => {
        this.setYZData({
          authLoading: false
        });
      }
    });
  },

  toggleAuthHandler() {
    const { needOpenSetting } = this.data;
    if (needOpenSetting) {
      return;
    }

    this.setYZData({
      authLoading: true
    });
    wx.authorize({
      scope: SCOPE_SHOPPING_LIST,
      success: () => {
        app.trigger('shopping-list:auth:change', true);
        this.setYZData({
          authed: true,
          authLoading: false,
          needOpenSetting: true
        });
      },
      fail: () => {
        this.setYZData({
          authed: false,
          authLoading: false,
          needOpenSetting: true
        });
      }
    });
  },

  getSyncHistoryStatus() {
    if (this.data.syncLoading) {
      return;
    }

    this.setYZData({
      syncLoading: true
    });

    clearTimeout(this.getStatusTimer);
    queryRecord().then(data => {
      const { syncBtn, status = 0, updateTime } = data;

      this.setYZData({
        syncLoading: false,
        syncBtn,
        status,
        updateTime: updateTime ? moment(updateTime, 'YYYY-MM-DD HH:mm:ss') : '无'
      });

      // 同步中做轮询
      if (status === 1) {
        this.getStatusTimer = setTimeout(() => {
          this.getSyncHistoryStatus();
        }, 5000);
      }
    }).catch(() => {
      Toast('网络异常，请刷新重试');
      this.setYZData({
        syncLoading: false,
        status: -1
      });
    }).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  syncHistory() {
    this.setYZData({
      syncLoading: true
    });

    syncHistoryOrder()
      .then(data => {
        if (data) {
          Toast.success('同步成功');
          this.setYZData({
            syncLoading: false
          });
          this.getSyncHistoryStatus();
        } else {
          throw data;
        }
      })
      .catch(() => {
        Toast.fail('同步失败');
        this.setYZData({
          syncLoading: false
        });
      });
  }
});
