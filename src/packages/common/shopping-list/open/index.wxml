<page-container class="container">
  <image
    class="desc"
    mode="widthFix"
    src="https://img.yzcdn.cn/public_files/2019/03/08/3bc2de20c83377e9bdfc395191b9913b.png"
  />
  <view
    class="checkbox"
  >
    <label
      bindtap="toggleSyncHandler"
    >
      <van-icon
        class="icon"
        name="success"
        color="{{ isSyncHistoryOrder ? '#08B906' : 'transparent' }}"
      />
      同步近半年历史订单至好物圈
    </label>
  </view>
  <van-button
    type="primary"
    open-type="{{ needOpenSetting ? 'openSetting' : '' }}"
    bind:click="openHandler"
  >
    开启并使用
  </van-button>
</page-container>

<van-toast id="van-toast" />
