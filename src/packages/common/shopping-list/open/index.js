import Toast from '@vant/weapp/dist/toast/toast';
import WscPage from 'pages/common/wsc-page/index';
import { syncHistoryOrder } from '../fetch';

const SCOPE_SHOPPING_LIST = 'scope.shoppingList';
const app = getApp();

WscPage({
  data: {
    isSyncHistoryOrder: true,
    needOpenSetting: false
  },

  onLoad() {
    wx.getSetting({
      success: ({ authSetting }) => {
        const status = authSetting[SCOPE_SHOPPING_LIST];
        if (status === false) {
          this.setYZData({
            needOpenSetting: true
          });
        }
      }
    });
  },

  onShow() {
    if (this.isJumpedSetting) {
      this._openShoppingList()
        .catch(() => {
          Toast('请开启微信好物圈开关');
        });
    }
    this.isJumpedSetting = false;

    // 标记用户已经了解过好物单，下次不会进入这个页面了
    app.storage.set('app:shopping-list:entered', 1, {
      expire: 14
    });
  },

  onUnLoad() {
    clearTimeout(this.backTimer);
  },

  toggleSyncHandler() {
    this.setYZData({
      isSyncHistoryOrder: !this.data.isSyncHistoryOrder
    });
  },

  openHandler() {
    const { needOpenSetting } = this.data;
    if (needOpenSetting) {
      // 跳转到授权设置页，wx.authorize 只会弹一次，拒绝后需要到设置页用户主动开启
      this.isJumpedSetting = true;
      return;
    }

    this._openShoppingList();
  },

  _successDo() {
    Toast.success({
      forbidClick: true,
      message: '开启成功'
    });

    app.trigger('shopping-list:auth:change', true);

    clearTimeout(this.backTimer);
    this.backTimer = setTimeout(() => {
      wx.navigateBack();
    }, 1000);
  },

  _openShoppingList() {
    const { isSyncHistoryOrder } = this.data;
    return new Promise((resolve, reject) => {
      wx.authorize({
        scope: 'scope.shoppingList',
        success: () => {
          if (isSyncHistoryOrder) {
            // 勾选同步历史订单
            this._syncHistory();
          } else {
            this._successDo();
          }
          resolve();
        },
        fail: () => {
          this.setYZData({
            needOpenSetting: true
          });
          reject();
        }
      });
    });
  },

  _syncHistory() {
    Toast.loading({
      forbidClick: true,
      message: '请稍候'
    });

    return syncHistoryOrder().then(() => {
      this._successDo();
    }).catch(({ msg }) => {
      Toast.fail(msg || '同步失败');
    });
  }
});
