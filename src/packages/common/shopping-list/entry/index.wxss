.container {
  position: relative;
  overflow: hidden;
}

.desc {
  display: block;
  width: auto;
  padding: 40rpx 30rpx 0;
}

.bottom {
  position: absolute;
  top: 830rpx;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  box-shadow: 0 -1px 19px 0 rgba(202, 202, 202, 0.39);
  padding: 50rpx;
}

.title {
  width: 100px;
  font-size: 14px;
  color: #000;
  margin: 0 auto 20px;
}

.list {
  font-size: 13px;
  color: #666;
  margin: 5px auto;
  text-align: center;
}

.auth-btn {
  position:absolute;
  bottom: 50rpx;
  left: 50%;
  padding: 5px;
  transform: translateX(-50%);
  font-size: 14px;
  color: #5E7595;
}
