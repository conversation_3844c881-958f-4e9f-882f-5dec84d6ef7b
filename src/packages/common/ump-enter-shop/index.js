import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';

const app = getApp();

export function umpEnterShop(
  options = {},
  umpAlias,
  umpType,
  redirectUrl,
  logParams = {}
) {
  // 参数校验
  if (!umpType || !umpAlias || !redirectUrl) {
    app.logger.appError({
      name: logName || 'umpEnterShop_without_necessary_params',
      message: logMsg || '调用进店方法缺少必要参数',
      detail: {
        umpType,
        umpAlias,
        redirectUrl,
        appVersion: app.getVersion(),
        launchOption: wx.getLaunchOptionsSync(),
      },
    });
    return Promise.reject();
  }
  const { logName, logMsg } = logParams;

  // 兼容其他页面跳转未传umpAlias的场景，理论上这里都需要走进店逻辑，由进店逻辑判断是否进店
  return autoEnterShop({
    ...options,
    umpAlias,
    umpType,
    redirectUrl,
  });
}
