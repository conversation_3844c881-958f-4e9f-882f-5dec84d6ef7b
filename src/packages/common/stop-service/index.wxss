.error__container {
  padding: 32px 0 44px 0;
  background-color: #fff;
}

.error__icon {
  height: 160px;
  background: transparent url("https://b.yzcdn.cn/public_files/1b20f59022a1da6a866a86596d2cffb4.png") center no-repeat;
  background-size: 160px 160px;
}

.error__texts {
  padding-top: 16px;
}

.closed-title {
  color: #323233;
  line-height: 20px;
  margin-bottom: 10px;
}

.closed-tip {
  color: #969799;
  line-height: 16px;
  margin-bottom: 16px;
}

.closed-button {
  display: block;
  color: #323233;
  margin: 0 15px 10px;
  text-align: center;
  padding: 11px 10px;
  line-height: 16px;
  border-radius: 999px;
  box-sizing: border-box;
  display: inline-block;
  background-color: #fff;
  border: 1px solid #e5e5e5;
}

.service-phone {
  text-align: center;
  color: #155bd4;
  line-height: 20px;
  width: 100%;
  font-size: 14px;
}

.closed-recommend {
  background-color: #f7f8fa;
}

.recommend-title {
  box-sizing: border-box;
  text-align: center;
  width: 100%;
  border: transparent 16px;
  border-style: none solid;
  font-size: 14px;
  line-height: 48px;
  position: relative;
}

.recommend-title::before {
  content: '';
  width: 200%;
  height: 1px;
  position: absolute;
  top: 24px;
  left: 0px;
  background-color: #d4d6d9;
  transform-origin: 0;
  transform: scale(0.5);
  z-index: 0;
}

.recommend-title span {
  position: relative;
  z-index: 1;
  padding: 0 12px;
  background-color:#f8f8f8;
}