import navigate from '@/helpers/navigate';
import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    // text: '',
    shopName: '',
    // code: 0
    layoutConfig: {
      textStyleType: 2,
      borderRadiusType: 2,
      showCornerMark: false
    }
  },

  onLoad() {
    const shopName = app.getShopInfoSync().base.shop_name;
    this.setYZData({
      shopName
    });
  },

  callphone() {
    const mobile = this.data.CURRENT_GLOBAL_SHOP.service.service_mobile;
    if (mobile) {
      wx.makePhoneCall({ phoneNumber: mobile });
    }
  },

  navTo() {
    navigate.switchTab({
      url: '/packages/usercenter/dashboard/index'
    });
  },
  onShow() {
    wx.setNavigationBarTitle({
      title: this.data.shopName || ' '
    });
  }
});
