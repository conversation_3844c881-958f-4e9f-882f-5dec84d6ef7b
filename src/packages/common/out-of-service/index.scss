.out-of-service {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 90px);
  background: #f7f8fa;

  &__img {
    width: 160px;
    height: 160px;
    object-fit: contain;
  }

  &__text {
    margin-top: 16px;
    line-height: 20px;
    font-size: 14px;
    color: #969799;

    &.is-main {
      font-size: 16px;
      color: #323233;
      line-height: 22px;
    }
  }

  .single-button {
    width: 200px;
    background: #ffffff;
  }

  &__actions {
    margin-top: 16px;

    .van-button:nth-child(n + 2) {
      margin-left: 32px;
    }
  }
}
