import navigate from '@/helpers/navigate';
import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    shopName: '',
  },
  onShow() {
    wx.setNavigationBarTitle({
      title: this.data.shopName || ' ',
    });
  },
  onLoad() {
    const { base = {} } = app.getShopInfoSync() || {};
    const { shop_name: shopName } = base;
    this.setYZData({
      shopName,
    });
  },

  goToUsercenter() {
    navigate.switchTab({
      url: '/packages/usercenter/dashboard/index',
    });
  },
});
