import navigate from '@/helpers/navigate';
import WscPage from 'pages/common/wsc-page/index';
import { reportSkynet } from 'utils/log/logv3';

const app = getApp();

WscPage({
  data: {
    text: '',
    code: 0,
    discountErr: false,
  },

  onLoad(query) {
    var data = app.db.get(query.dbid) || {};

    reportSkynet('哎呀，出错了', data);

    if (+data.code === 101305032) {
      wx.setNavigationBarTitle({ title: '确认订单' });
      this.setYZData({ discountErr: true });
    }

    this.setYZData({
      text: data.text || query.text || '哎呀，出错了',
      code: data.code || query.code || 0,
    });
  },

  onShow() {
    if (this.isShowed) {
      navigate.switchTab({ url: '/packages/home/<USER>/index' });
    }
    this.isShowed = true;
  },
});
