import url from 'utils/url';
import md5 from './modules/md5';

const app = getApp();

const CAPTCHA_BIZ_LOGIN_WITHOUT_PWD = 'uic_login_without_password';
const COUNTRY_CODE_CN = '+86';
/**
 * 获取服务器时间
 */
function getServerTime() {
  return new Promise((resolve, reject) => {
    app.carmen({
      api: 'kdt.utility.time/1.0.0/get',
      method: 'GET',
      config: {
        skipKdtId: true,
        skipShopInfo: true
      },
      success: (res) => {
        resolve(res);
      },
      fail: (res) => {
        reject(res);
      }
    });
  });
}

/**
 * 生成获取验证码 token
 * @param {String} mobile 手机号
 * @param {} millsec
 * @param {*} bizString
 */
function getSmsToken(mobile, millsec, bizString) {
  const STR_SALT_SMS_TOKEN = 'youzan_app_iphone6';

  let date = new Date(millsec);
  // 将 UTCxxx 方法返回的格林尼治时间改成东8区的时间
  date.setTime(date.getTime() + (8 * 60 * 60 * 1000));

  var month = date.getUTCMonth();
  month = (month + 1) > 9 ? (month + 1).toString() : '0' + (month + 1);

  var aDate = date.getUTCDate();
  let day = aDate > 9 ? aDate.toString() : '0' + aDate;

  var hour = date.getUTCHours();
  hour = hour > 9 ? hour.toString() : '0' + hour;

  var minute = date.getUTCMinutes();
  minute = minute > 9 ? minute.toString() : '0' + minute;

  let timestamp = '' + date.getUTCFullYear() + month + day + hour + minute;

  let arr = [STR_SALT_SMS_TOKEN, timestamp, mobile, bizString];

  return md5(arr.join(''));
}

/**
 * 发起登录（如果未注册则自动创建新账号）
 * /sso/wx/codeLogin
 */
function codeLogin(phoneNumber, captchaCode, success, fail) {
  url({
    origin: 'uic',
    pathname: '/sso/wx/codeLogin',
  }).then((reqUrl) => {
    wx.request({
      url: reqUrl,
      method: 'POST',
      config: {
        skipKdtId: true,
        skipShopInfo: true,
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded',
      },
      data: {
        mobile: phoneNumber,
        countryCode: COUNTRY_CODE_CN,
        verifyCode: captchaCode,
        sessionId: app.getSessionId(),
      },
      success: (res) => {
        if (res.data && res.data.code === 0) {
          success && success(res);
        } else {
          fail(res.data);
        }
      },
      fail,
    });
  });
}

/**
 * 获取验证码
 * @param {*} mobile
 * @param {*} times
 * @param {*} success
 * @param {*} fail
 */
function fetchCode(mobile, times, success, fail) {
  getServerTime()
    .then((res = {}) => {
      app.carmen({
        api: 'kdt.auth.sms/1.0.0/send',
        method: 'POST',
        config: {
          skipKdtId: true,
          skipShopInfo: true
        },
        data: {
          sms_token: getSmsToken(mobile, res.stamp_millisecond, CAPTCHA_BIZ_LOGIN_WITHOUT_PWD),
          send_times: times,
          mobile,
          countryCode: '',
          biz: CAPTCHA_BIZ_LOGIN_WITHOUT_PWD
        },
        success: (res) => {
          if (res.is_success) {
            success && success();
          } else {
            fail(res);
          }
        },

        fail
      });
    })
    .catch(fail);
}

export default {
  fetchCode,
  codeLogin
};
