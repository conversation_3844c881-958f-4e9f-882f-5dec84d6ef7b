<view class="content" hidden="{{ hidden }}">
  <text class="title">欢迎进入演示店铺</text>
  <view class="form">
    <view class="form-item">
      <input type="number" placeholder="请输入手机号" maxlength="11" placeholder-class="placeholder" value="{{ mobile }}" bindinput="bindMobileInput"
      />
    </view>
    <view class="form-item">
      <input type="number" placeholder="请输入验证码" placeholder-class="placeholder" value="{{ captcha }}" bindinput="bindCaptchaInput"
      />
      <view class="auth-code-btn {{captchaDisabled || captchaLoading ? ' disabled' : ''}}" bindtap="getAuthCode">
        <text hidden="{{ !captchaDisabled || captchaLoading }}">已发送({{countdown}}s)</text>
        <text hidden="{{ !captchaLoading }}">获取中</text>
        <text hidden="{{ captchaDisabled || captchaLoading }}">{{ captchaInit ? "重新获取" : "获取验证码" }}</text>
      </view>
    </view>
    <button class="btn" type="button" disabled="{{ !(mobile && captcha) }}" bindtap="submit">立即体验</button>
    <view class="tips">演示店铺非正式店铺，所有数据均为模拟数据，仅供流程体验使用，祝体验愉快！</view>
  </view>
</view>
