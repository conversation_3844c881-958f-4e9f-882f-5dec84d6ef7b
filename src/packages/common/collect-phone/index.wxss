page {
  background: #f8f8f8;
}

.content {
    padding: 47px 30px;
}

.title {
  line-height: 33px;
  font-size: 24px;
  color: #333;
  margin: 0;
}

.form {
  padding-top: 4px;
}

.form-item {
  border-bottom: 1rpx solid #e5e5e5;
  line-height: 50px;
  display: flex;
  height: 50px;
}

.form-item input {
  line-height: 20px;
  padding: 15px 0;
  height: 50px;
  border: 0;
  outline: 0;
  box-sizing: border-box;
  background: transparent;
  font-size: 14px;
  flex: 1;
}

.auth-code-btn {
  color: #38f;
  font-size: 14px;
}

.auth-code-btn.disabled {
  color: #ccc;
}

.form-item .placeholder {
  color: #ccc;
}

.btn {
  margin-top: 30px;
  width: 100%;
  display: block;
  line-height: 44px;
  height: 44px;
  background: #4b0;
  color: #fff;
  text-align: center;
  border-radius: 2px;
  position: relative;
}

.btn[disabled] {
  opacity: .5;
}

.tips {
  margin-top: 10px;
  font-size: 11px;
  color: #999;
}
