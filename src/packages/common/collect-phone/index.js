import bindPhoneNumber from './utils';

const app = getApp();

Page({
  data: {
    mobile: '',
    captcha: '',
    times: 1,
    hidden: true,
    countdown: 60,
    captchaInit: false,
    captchaLoading: false,
    captchaDisabled: false,
  },
  query: {},
  onLoad(options) {
    app.hasToken()
      ? this.init(options)
      : this.once('app:token:success', () => this.init(options));
  },
  init(options) {
    this.query.kdtId = options.kdt_id;
    this.query.referrerId = options.userId;
    this.query.referrerType = options.userType;
    if (this.query.kdtId) {
      this.save();
    } else {
      this.setData({ hidden: false });
    }
  },
  bindMobileInput(e) {
    this.setData({
      mobile: e.detail.value,
    });
  },
  bindCaptchaInput(e) {
    this.setData({
      captcha: e.detail.value,
    });
  },
  getAuthCode() {
    if (this.data.captchaDisabled) return;
    if (!/^1[0-9]{10}$/.test(`${this.data.mobile}`)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none',
        duration: 800,
      });
      return;
    }
    if (!this.data.captchaInit) this.setData({ captchaInit: true });
    this.setData({ captchaLoading: true });
    bindPhoneNumber.fetchCode(
      this.data.mobile,
      this.data.times,
      () => {
        this.setData({
          captchaLoading: false,
          captchaDisabled: true,
          times: this.data.times + 1,
          countdown: 60,
        });
        this.countDown();
      },
      (e) => {
        this.setData({ captchaLoading: false });
        wx.showToast({
          title: String(e.msg),
          icon: 'none',
          duration: 800,
        });
      }
    );
  },
  countDown() {
    setTimeout(() => {
      if (this.data.countdown) {
        this.setData({ countdown: this.data.countdown - 1 });
        this.countDown();
      } else {
        this.setData({ captchaDisabled: false });
      }
    }, 1000);
  },
  save() {
    const mobile = this.data.mobile || app.getMobile();
    if (!mobile) {
      this.setData({ hidden: false });
      return;
    }
    wx.showLoading();
    app
      .request({
        path: 'v3/shop/collect-phone/save-customer.json',
        method: 'POST',
        data: {
          mobile,
          ...this.query,
          source: 'weapp',
        },
      })
      .then(() => {
        setTimeout(() => {
          wx.hideLoading();
          app.updateKdtId(this.query.kdtId, false, {
            mark: '903',
          });
          wx.redirectTo({
            url: '/packages/home/<USER>/index',
          });
        }, 600);
      })
      .catch((e) => {
        wx.hideLoading();
        this.setData({ hidden: false });
        wx.showToast({
          title: String(e.msg),
          icon: 'none',
          duration: 800,
        });
      });
  },
  submit() {
    bindPhoneNumber.codeLogin(
      this.data.mobile,
      this.data.captcha,
      (res) => {
        if (+res.data.code === 0) {
          if (res.data.data && res.data.data.buyer_id) {
            app.login(() => this.save());
          }
        }
      },
      (e) => {
        if (e.code === 135000024) {
          return app.login(() => this.save());
        }
        wx.showToast({
          title: String(e.msg),
          icon: 'none',
          duration: 800,
        });
      }
    );
  },
});
