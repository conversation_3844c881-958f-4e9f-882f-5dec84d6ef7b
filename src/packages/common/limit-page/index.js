import WscPage from 'pages/common/wsc-page/index';
import args from '@youzan/weapp-utils/lib/args';
import { NORMAL_AUTO, NORMAL_MANUAL, WAIT_IMAGE } from './constant';

WscPage({
  data: {
    image: WAIT_IMAGE,
    status: '',
    buttonText: '',
  },
  countdown: 10,
  onLoad(query) {
    const { callBackUrl, redirectCount, options } = query;
    const status = Number(redirectCount) === 0 ? NORMAL_AUTO : NORMAL_MANUAL;
    let buttonText = '';
    if (status === NORMAL_MANUAL) {
      buttonText = this.countdown ? `重新加载(${this.countdown}s)` : '重新加载';
    }

    const title = status === NORMAL_AUTO ? `抢购人数太多，请耐心等待(${this.countdown}s)` : '店铺太火爆啦，请稍后重试';

    wx.setNavigationBarTitle({
      title: status === NORMAL_AUTO ? '加载中' : '重新加载'
    });

    this.setYZData({
      status,
      callBackUrl,
      buttonText,
      redirectCount: Number(redirectCount),
      options,
      title,
    });

    this.tick();
  },

  onUnload() {
    this.countdown = 10;
    clearTimeout(this.timer);
  },

  tick() {
    this.timer = setTimeout(() => {
      const { status } = this.data;
      this.countdown--;
      if (status === NORMAL_MANUAL) {
        const buttonText = this.countdown ? `重新加载(${this.countdown}s)` : '重新加载';
        this.setYZData({
          buttonText,
        });
      }
      if (status === NORMAL_AUTO) {
        this.setYZData({
          title: `抢购人数太多，请耐心等待(${this.countdown}s)`
        });
      }

      if (this.countdown > 0) {
        this.tick();
      } else if (status === NORMAL_AUTO) {
        this.redirect();
      }
    }, 1000);
  },

  onClick() {
    if (!this.countdown) {
      this.redirect();
    }
  },
  redirect() {
    const { redirectCount, callBackUrl, options } = this.data;
    const pureCallbackUrl = callBackUrl.split('?')[0]
    const tabbarList = __wxConfig.tabBar.list || []
    const isTabPage = tabbarList.some(item => pureCallbackUrl.indexOf(item.pagePath.split('.')[0]) !== -1);
    if (!isTabPage) {
      const pageOptions = JSON.parse(options);
      const url = args.add(callBackUrl, {
        ...pageOptions,
        redirectCount: redirectCount + 1
      });
      wx.redirectTo({
        url: url.startsWith('/') ? url : `/${url}`,
      });
    } else {
      wx.switchTab({
        url: pureCallbackUrl.startsWith('/') ? pureCallbackUrl : `/${pureCallbackUrl}`,
      });
    }
  }
});
