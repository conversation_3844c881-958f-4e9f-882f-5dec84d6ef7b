<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view class="membercard-active">
    <view class="setting-panel">
      <van-cell-group>
        <van-cell center custom-class="setting-item">
          <view slot="icon" class="setting-item__title">姓名</view>
          <input 
            slot="title"
            class="slot-item"
            bindinput="onNameInputBlur"
            value="{{ name }}"
            placeholder="请输入姓名" 
          />
        </van-cell>

        <van-cell center custom-class="setting-item">
          <view slot="icon" class="setting-item__title">生日</view>
          <picker 
            slot="title"
            class="slot-item"
            bindchange="bindDatePickerChange" 
            mode="date" 
            value="{{ birthday }}" 
            placeholder="请输入生日"
          >
            <view class="picker">
              <view wx:if="{{ slectedBirth }}">{{ birthday }}</view>
              <view wx:else>请选择</view>
            </view>
          </picker>   
        </van-cell>

        <van-cell center custom-class="setting-item">
          <view slot="icon" class="setting-item__title">性别</view>
          <picker 
            slot="title"
            class="slot-item"
            bindchange="bindSexPickerChange" 
            value="{{ sexIndex }}" 
            range="{{ sexArray }}"
          >
            <view class="picker">
              <view wx:if="{{ slectedSex }}">{{ sexArray[sexIndex] }}</view>
              <view wx:else>请选择</view>
            </view>
          </picker>
        </van-cell>

        <van-cell center custom-class="setting-item">
          <view slot="icon" class="setting-item__title">所在地</view>
          <view 
            slot="title" 
            class="slot-item address-edit__pickers"
          >
            <picker 
              mode="selector"
              class="address-edit__picker"
              range="{{ area.province }}"
              range-key="text"
              value="{{ edit_data.provinceIndex }}"
              data-type="province"
              bindchange="onAreaChange"
            >
              <view class="picker">{{ area.province[edit_data.provinceIndex].text }}</view>
            </picker>
            <picker 
              mode="selector"
              class="address-edit__picker"
              range="{{ area.city }}"
              range-key="text"
              value="{{ edit_data.cityIndex }}"
              data-type="city"
              bindchange="onAreaChange"
            >
              <view class="picker">{{ area.city[edit_data.cityIndex].text }}</view>
            </picker>
            <picker 
              mode="selector"
              class="address-edit__picker"
              range="{{ area.county }}"
              range-key="text"
              value="{{ edit_data.countyIndex }}"
              data-type="county"
              bindchange="onAreaChange"
            >
              <view class="picker">{{ area.county[edit_data.countyIndex].text }}</view>
            </picker>
          </view>
        </van-cell>
      </van-cell-group>
    </view>
    <view class="btn-container">
      <van-button custom-class="active_btn" type="primary" size="large" bind:click="saveUserInfo">保存</van-button>
    </view>
  </view>
</page-container>
