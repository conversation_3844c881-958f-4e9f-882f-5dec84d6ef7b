import WscPage from 'pages/common/wsc-page/index';
import * as areaDataHelper from '../common/area';

const app = getApp();
WscPage({
  data: {
    hasUserInfo: false,
    name: '',
    disableMobile: true,
    mobile: '',
    weixin: '',
    sexIndex: -1,
    slectedSex: false,
    sexArray: ['请选择', '男', '女'],
    slectedBirth: false,
    birthday: '1990-01-01',
    edit_data: {
      provinceIndex: 0,
      cityIndex: 0,
      countyIndex: 0,
      selectedValue: 0,
    },
    area: {},
  },
  /**
   * mode: 显示一个月; 显示所有月份
   * @param query
   */
  onLoad() {
    app.fetchAreaMapData((areaData) => {
      const area = areaDataHelper.formatAreaData(0, areaData);
      this.setYZData({ area_origin: areaData, area });
    });
    wx.showLoading({
      title: '努力加载中',
    });
    app
      .request({
        path: '/wscuser/scrm/api/customer/get.json',
      })
      .then((data) => {
        wx.hideLoading();
        if (data) {
          let genderIndex = 0;
          switch (data.profile.gender) {
            case 'FEMALE':
              genderIndex = 2;
              break;
            case 'MALE':
              genderIndex = 1;
              break;
            default:
              genderIndex = 0;
          }
          const mobile = app.getMobile() || '';
          const birdDay = data.profile.birthday.split(' ')[0];
          this.setYZData({
            hasUserInfo: true,
            name: data.name,
            mobile,
            weixin: data.profile.weixin,
            sexIndex: genderIndex,
            slectedSex: true,
            slectedBirth: !!birdDay,
            birthday: birdDay,
          });
          // 处理地址数据
          setTimeout(() => {
            // 处理省市区列表
            const areaCode = data.profile.area_code || 0;
            const area = areaDataHelper.formatAreaData(
              areaCode,
              this.data.area_origin
            );
            // 处理选中的省市区
            const { provinceIndex, cityIndex, countyIndex } =
              areaDataHelper.findSelectedAddressIndex(areaCode, area);
            const editAddress = {
              provinceIndex,
              cityIndex,
              countyIndex,
              selectedValue: areaCode,
            };

            this.setYZData({
              edit_data: editAddress,
              area,
            });
          }, 200);
        } else {
          // eslint
        }
      })
      .catch((e) => {
        console.log(e);
      });
  },

  onAreaChange(e) {
    const selectedIndex = e.detail.value || 0;
    const { type } = e.currentTarget.dataset;
    let { edit_data } = this.data;
    const selectedData = this.data.area[type][selectedIndex];
    const selectedValue = selectedData.code;

    // 如果选择的没变过，就不需要触发下面的变动逻辑了
    // eslint-disable-next-line eqeqeq
    if (edit_data[`${type}Index`] == selectedIndex) {
      return;
    }

    const area = areaDataHelper.formatAreaData(
      selectedValue,
      this.data.area_origin
    );
    let provinceIndex = edit_data.provinceIndex || 0;
    let cityIndex = edit_data.cityIndex || 0;
    let countyIndex = edit_data.countyIndex || 0;

    switch (type) {
      case 'province':
        provinceIndex = selectedIndex;
        edit_data.province = area.province[provinceIndex].text;
        cityIndex = 0;
        countyIndex = 0;
        edit_data.area_code = undefined;
        break;
      case 'city':
        cityIndex = selectedIndex;
        edit_data.city = area.city[cityIndex].text;
        countyIndex = 0;
        edit_data.area_code = undefined;
        break;
      case 'county':
        countyIndex = selectedIndex;
        edit_data.county = area.county[countyIndex].text;
        edit_data.area_code = area.county[countyIndex].code;
        break;
      default:
        break;
    }

    edit_data = {
      ...edit_data,
      provinceIndex,
      cityIndex,
      countyIndex,
      selectedValue,
    };

    this.setYZData({ edit_data, area });
  },

  saveUserInfo() {
    let genderStr = 'OTHER';
    switch (+this.data.sexIndex) {
      case 1:
        genderStr = 'MALE';
        break;
      case 2:
        genderStr = 'FEMALE';
        break;
      default:
        genderStr = 'OTHER';
    }
    app
      .request({
        path: '/wscuser/scrm/api/customer/update.json',
        method: 'post',
        data: {
          type: this.data.hasUserInfo ? 'update' : 'create',
          customer: {
            name: this.data.name,
            mobile: this.data.mobile,
            gender: genderStr,
            birthday: this.data.slectedBirth ? this.data.birthday : '',
            weixin: this.data.weixin,
            areaCode: +this.data.edit_data.area_code,
          },
        },
      })
      .then(() => {
        wx.navigateBack();
      })
      .catch((e) => {
        console.log(e);
      });
  },

  onNameInputBlur(e) {
    const name = e.detail.value;
    this.setYZData({ name });
  },

  onMobileInputBlur(e) {
    const mobile = e.detail.value;
    this.setYZData({ mobile });
  },

  bindSexPickerChange(e) {
    this.setYZData({
      slectedSex: true,
      sexIndex: e.detail.value,
    });
  },

  bindDatePickerChange(e) {
    this.setYZData({
      slectedBirth: true,
      birthday: e.detail.value,
    });
  },

  bindRegionChange(e) {
    this.setYZData({
      slectedRegion: true,
      region: e.detail.value,
    });
  },
});
