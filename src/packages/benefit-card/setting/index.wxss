@import "shared/common/css/helper/index.wxss";

page {
  min-height: 100%;
  background-color: #F8F8F8;
}

.setting-panel {
  background-color: #fff;
}

.setting-item {
  height: 58px;
}

.setting-item__title {
  width: 65px;
  padding-right: 10px;
}

.btn-container {
  padding: 40px 10px 0px 10px;
}

.active_btn {
  border-radius: 4px !important;
}

.address-edit__pickers {
  height: 28px;
}

.address-edit__picker {
  display: inline-block;
  width: 33%;
  height: 28px;
  overflow: hidden;
}

.slot-item {
  line-height: 34px;
  height: 34px;
  color:#000;
}

.disabled {
  color: #999;
}