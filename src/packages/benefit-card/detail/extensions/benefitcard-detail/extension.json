{"name": "@wsc-tee-user/benefitcard-detail", "extensionId": "@wsc-tee-user/benefitcard-detail", "bundle": "<builtin>", "lifecycle": ["beforePageMount", "onShareAppMessage", "onPullDownRefresh"], "framework": "weapp", "platform": ["weapp"], "data": {"provide": {"salesman": ["r", "w"]}, "consume": {}}, "widget": {"provide": ["ProtocolBlock", "NavigationBarBlock", "CardBlock", "ExclusiveContainer", "AdditionAction", "CardCellGroup", "RecommendGoods", "OtherBlock", "AgreementBlock", "ActionBlock"], "consume": ["ProtocolBlock", "NavigationBarBlock", "CardBlock", "ExclusiveContainer", "AdditionAction", "CardCellGroup", "RecommendGoods", "OtherBlock", "AgreementBlock", "ActionBlock"]}, "process": {"define": [], "invoke": []}}