/* eslint @youzan-open/tee/valid-same-extension-import: "off", @youzan-open/tee/no-platform-field: "off" */
import { bridge, useAsEvent } from '@youzan/ranta-helper';
import get from '@youzan/weapp-utils/lib/get';

import { mapState, mapActions } from '@ranta/store';
import { vanxToRantaStore } from 'shared/common/base/ranta/store';
import { initSalesman } from 'shared/utils/salesman-share';
import theme from 'shared/common/components/theme-view/theme';
import logv3 from 'utils/log/logv3';
import { resolveShareDataFunc } from '@/packages/ump/utils/resolve-share';
import set from 'utils/set';
import { benefitCardEnterShop } from '../../../common/enter-shop';
import { GUIDE_OPERATE_TYPE, shareImageUrl } from '../../../constants';
import { store } from './store';
import { formatBenefits, getMaiDianData } from '../../../utils';
import { getCurrentPageUrl } from './util';

const app = getApp();

export default class {
  ctx;

  store = vanxToRantaStore(store);

  constructor({ ctx }) {
    ctx.store = this.store;

    this.ctx = ctx;

    this.initDataAndActions();
  }

  @bridge('setPageTitle', 'process')
  setPageTitle(_content) {
    wx.setNavigationBarTitle({
      title: _content,
    });
  }

  @bridge('hideCardExpiryDate', 'process')
  hideCardExpiryDate() {
    this.HIDE_CARD_EXPIRY_DATE();
  }

  @bridge('hideProtocolBlock', 'process')
  hideProtocolBlock() {
    this.HIDE_PROTOCOL_BLOCK();
  }

  @bridge('checkViewAggreementStatusSyncEvent', 'event')
  checkViewAggreementStatusSyncEvent = useAsEvent<() => boolean>();

  @bridge('beforeSubmitAsyncEvent', 'asyncEvent')
  beforeSubmitAsyncEvent = useAsEvent<() => Promise<boolean>>();

  beforePageMount = ({ query }) => {
    initSalesman.call(this, {
      scene: 'benefit_card',
      sst: 4,
      isUseRanta: true,
      getSalesmanData: this.setSalesmanData,
    });
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1].__route__;
    benefitCardEnterShop(query, currentPage)
      .then(() => {
        this.handleOnLoad(query);
      })
      .catch(() => {
        this.handleOnLoad(query);
      });
  };

  setSalesmanData = (err, data) => {
    if (err) return;
    const isNeedshowMore = data.isNeedShareMore !== false;
    if (isNeedshowMore) {
      if (data && data.seller) {
        this.ctx.data.salesman = data;
      }
    }
  };

  setYZData(obj) {
    Object.entries(obj).forEach(([key, value]) => {
      set(this.ctx.data, key, value);
    });
  }

  handleOnLoad(query) {
    const {
      alias = '',
      goods_id: goodsId = '',
      orderNo = '',
      from = '',
    } = query;
    const maiDianData = getMaiDianData(query);
    this.SET_MAI_DIAN_DATA({ maiDianData });
    this.SET_QUERY({ query });
    this.SET_KDT_ID({ kdtId: app.getKdtId() });
    this.SET_QUERY_INFO({ alias, goodsId, orderNo, from });
    app.getUserInfo((res) => {
      this.SET_USER_INFO({ userInfo: res.userInfo });
    });

    this.initShopInfo();
    this.initData(GUIDE_OPERATE_TYPE).then(() => {
      // const url = getCurrentPageUrl();
      // app.logger.performance('app', 'pageshow');
      // app.logger.performance('page', 'pageshow', false, url);
    });
    const self = this;
    wx.getSystemInfo({
      success({ windowWidth }) {
        const width = windowWidth - 48;
        const height = (width * 184) / 327;
        self.SET_CARD_WRAPPER_STYLE({
          cardWrapperStyle: {
            width: `${width}px`,
            height: `${height}px`,
          },
        });
      },
    });
    theme.getTheme().then((res) => {
      self.SET_THEME_COLORS({
        themeColors: res.colors,
      });
    });
  }

  initData(guideType) {
    const hasMobile = !!app.getBuyerId();
    this.SET_LOADING({ loading: true });
    this.SET_HAS_MOBILE({ hasMobile });
    return this.fetchCardTplInfo(guideType);
  }

  initDataAndActions() {
    mapActions(this, [
      'SET_QUERY_INFO',
      'SET_USER_INFO',
      'SET_CARD_WRAPPER_STYLE',
      'SET_THEME_COLORS',
      'SET_LOADING',
      'SET_QUERY',
      'SET_KDT_ID',
      'SET_HAS_MOBILE',
      'SET_MAI_DIAN_DATA',
      'HIDE_CARD_EXPIRY_DATE',
      'HIDE_PROTOCOL_BLOCK',
      'initShopInfo',
      'fetchCardTplInfo',
    ]);
    mapState(this, ['benefitBag', 'subType', 'shopName', 'cardNo', 'alias']);
  }

  // 获取分享需要的所有的权益描述
  getCardDescp() {
    const benefits = formatBenefits(this.benefitBag, this.subType);
    return benefits.map((item) => item.label).join(',');
  }

  onShareAppMessage(res) {
    const { from, target = {} } = res;
    if (from === 'button' && target.id === 'share') {
      // 分享描述
      const desc = this.getCardDescp();
      const title = `分享你一张${this.shopName}的体验卡`;
      // 分享链接
      const path = `/packages/benefit-card/take/index?alias=${get(
        this,
        'alias'
      )}&card_no=${get(this, 'cardNo')}`;
      // 分享图标

      return resolveShareDataFunc({
        title,
        path,
        imageUrl: shareImageUrl,
        desc,
        salesman: this.ctx.data.salesman,
      });
    }

    const shareUrl = `/packages/benefit-card/detail/index?alias=${this.alias}`;
    const title = `分享你一张${this.shopName}的权益卡`;
    // 分享描述
    const desc = this.getCardDescp();
    return resolveShareDataFunc(
      logv3.processShareData({
        title,
        path: shareUrl,
        imageUrl: shareImageUrl,
        desc,
        salesman: this.ctx.data.salesman,
      })
    );
  }

  // 下拉刷新
  onPullDownRefresh() {
    this.initData();
  }
}
