/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import navigate from 'shared/utils/navigate';
import { RantaWidget } from 'shared/common/base/ranta/widget';

import { isNil } from '../../../../../utils';
import get from '@youzan/weapp-utils/lib/get';

RantaWidget({
  data: {
    exInfo: null,
  },

  properties: {
    benefit: {
      type: Object,
      value: {},
    },
    userBenefitDetail: {
      type: Object,
      value: {},
    },
    themeGeneral: String,
    hasCard: Boolean,
    hasUseAbility: Boolean,
    cardIsExpired: Boolean,
    isCurrentShopAvailable: Boolean,
  },
  methods: {
    toCoupon() {
      navigate.navigate({
        url: '/packages/user/coupon/list/index',
      });
    },
    /**
     * 导航至知识付费权益详情页
     */
    toPaid() {
      const { benefit } = this.properties;
      const pAlias = get(benefit, 'benefitPluginInfo.payContentAlias', '');
      navigate.navigate({
        url: pAlias ? `/packages/paidcontent/rights/index?alias=${pAlias}` : '',
      });
    },
    headerExtra() {
      const {
        cardIsExpired,
        benefit,
        hasCard,
        hasUseAbility,
        isCurrentShopAvailable,
      } = this.properties;
      const {
        key,
        discount,
        stockNum,
        presentList,
        total,
        points,
        rate = 0,
      } = benefit;
      switch (key) {
        case 'discount':
          return {
            opposite: false,
            value: discount / 10,
            unit: '折',
          };
        case 'coupon':
          // 已领取
          if (hasUseAbility) {
            return {
              isBtn: true,
              label: '查看',
              onClick: 'toCoupon',
            };
          }
          return {
            opposite: false,
            value: total,
            unit: '张',
          };
        case 'paidContent':
          return {
            isBtn: true,
            label: '查看',
            onClick: 'toPaid',
          };
        case 'present':
          return {
            opposite: false,
            value: presentList.length || 0,
            unit: '份',
          };
        case 'points':
          return {
            opposite: true,
            value: points,
            unit: '',
          };
        case 'pointsFeedBack':
          return {
            opposite: false,
            value: rate / 10,
            unit: '倍',
          };
        case 'experienceCard':
          if (hasCard) {
            const disabled = !hasUseAbility || !hasCard;
            return {
              isBtn: true,
              label: '分享',
              btnId: 'share',
              disabled: cardIsExpired,
              openType: disabled || !isCurrentShopAvailable ? '' : 'share',
              onClick: '',
            };
          }
          if (isNil(stockNum)) return null;
          return {
            opposite: false,
            value: stockNum,
            unit: '张',
          };
        case undefined:
          if (isNil(stockNum)) return null;
          return {
            opposite: false,
            value: stockNum,
            unit: '次',
          };
        default:
          return null;
      }
    },
  },
  attached() {
    const extraInfo = this.headerExtra();
    this.setYZData({
      exInfo: extraInfo,
    });
  },
});
