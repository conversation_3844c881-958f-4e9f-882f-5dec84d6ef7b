<view class="ecs-c">
  <view class="t-in">
    <view class="i-m">
      <view class="m-n">{{ benefit.appName || benefit.title }}</view>
      <text class="mid-d">{{ benefit.label }}</text>
    </view>
    <view wx:if="{{ exInfo }}" class="he-ex">
      <button
        disabled="{{ exInfo.disabled }}"
        wx:if="{{ exInfo.isBtn }}"
        type="default" 
        size="small"
        id="{{ exInfo.btnId }}"
        round
        class="b-r"
        bindtap="{{ exInfo.onClick }}"
        open-type="{{ exInfo.openType ? exInfo.openType: '' }}"
      >
        {{ exInfo.label }}
      </button>
      <view wx:elif="{{ exInfo.opposite }}" class="dis-in" style="color: {{themeGeneral}}">
        <view class="dis-u-l">{{ exInfo.unit }}</view>
        <view class="dis-n">{{exInfo.value}}</view>
      </view>
      <view wx:else class="dis-in" style="color: {{themeGeneral}}">
        <view class="dis-n">{{exInfo.value}}</view>
        <view class="dis-u-r">{{ exInfo.unit }}</view>
      </view>
    </view>
  </view>
  <slot></slot>
</view>
