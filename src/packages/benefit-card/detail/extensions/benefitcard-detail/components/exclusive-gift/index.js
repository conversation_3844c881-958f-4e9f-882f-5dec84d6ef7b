import { RantaWidget } from 'shared/common/base/ranta/widget';

import get from '@youzan/weapp-utils/lib/get';
import Args from '@youzan/utils/url/args';
import navigate from '@/helpers/navigate';
import buildUrl from '@youzan/utils/url/buildUrl';

const giftStatus = {
  unSent: 0, // 未发放
  unTaken: 1, // 未领取
  received: 2, // 已领取
  deleted: 3, // 已删除
  expired: 4, // 已过期
};

RantaWidget({
  data: {
    statusMap: giftStatus,
  },
  properties: {
    cardIsExpired: Boolean,
    themeGeneral: String,
    hasCarrier: {
      type: Boolean,
      value: false,
    },
    gifts: {
      type: Array,
      value: [],
    },
  },
  methods: {
    takeEduPresent() {
      const gift = this.properties.gifts[0];
      const alias = get(gift, 'cocGoodsInfo.goodsAlias');
      const presentSource = get(gift, 'source');
      const presentId = get(gift, 'presentId');
      const recordId = get(gift, 'recordId');
      const presentQueryParams = JSON.stringify([
        {
          presentId,
          presentRecordId: recordId,
        },
      ]);

      let url = Args.add('https://h5.youzan.com/wscvis/ump/receive-present', {
        alias,
        presentSource,
        presentQueryParams,
      });
      url = buildUrl(url, 'h5', this.kdtId);

      const weappUrl = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
        url
      )}`;

      navigate.navigate({
        url: weappUrl || '',
      });
    },
    takeNormalPresent() {
      const gift = this.properties.gifts[0];
      const alias = get(gift, 'cocGoodsInfo.goodsAlias');
      const presentRecordAlias = get(gift, 'receiveRecordAlias');

      // eslint-disable-next-line max-len
      const weappUrl = `/packages/goods/present/index?alias=${alias}&type=present&activityId=${presentRecordAlias}`;
      navigate.navigate({
        url: weappUrl || '',
      });
    },
    takePresent() {
      const gift = this.properties.gifts[0];
      if (gift) {
        switch (+get(gift, 'cocGoodsInfo.goodsType', 0)) {
          // 教育赠品
          case 31:
            this.takeEduPresent();
            break;
          default:
            this.takeNormalPresent();
            break;
        }
      } else {
        this.takeNormalPresent();
      }
    },
  },
  attached() {},
});
