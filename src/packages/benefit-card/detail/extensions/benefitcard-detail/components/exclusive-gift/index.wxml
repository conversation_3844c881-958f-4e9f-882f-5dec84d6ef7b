<view wx:if="{{ gifts && gifts.length }}" class="pre-it">
  <view class="it-l">
    <view class="title">{{ gifts[0].cocGoodsInfo && gifts[0].cocGoodsInfo.title }}</view>
    <view class="expires">{{ gifts[0].expirationDate }}天内领取有效</view>
  </view>
  <view wx:if="{{ hasCarrier }}">
    <block wx:if="{{ cardIsExpired }}">
      <view class="h-re" wx:if="{{ cardIsExpired }}">过期无法领取</view>
    </block>
    <block wx:else>
      <view class="pr-btn" wx:if="{{ gifts[0].status === statusMap.unTaken }}" bindtap="takePresent" style="color: {{ themeGeneral }}">
        <view>{{ gifts[0].number }}份待领取</view>
        <van-icon class="arrow-icon" name="arrow" />
      </view>
      <view class="h-re" wx:if="{{ gifts[0].status === statusMap.received }}">已领取</view>
      <view class="h-re" wx:if="{{ [statusMap.expired, statusMap.deleted].includes(gifts[0].status) === statusMap.deleted }}">过期无法领取</view>
    </block>
  </view>
  <view wx:else>{{ gifts[0].number }}份</view>
</view>