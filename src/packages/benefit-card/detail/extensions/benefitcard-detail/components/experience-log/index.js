import { RantaWidget } from 'shared/common/base/ranta/widget';

RantaWidget({
  data: {
    logList: [],
  },
  properties: {
    number: {
      type: Number,
      observer: 'computedLogList',
    }, // 体验卡数量
    experienceLog: {
      type: Array,
      observer: 'computedLogList',
    },
    rewardTip: String,
  },
  methods: {
    computedLogList() {
      const { number, experienceLog } = this.properties;
      const emptyRecord = new Array(number).fill({});
      this.setYZData({
        logList: experienceLog.concat(emptyRecord) || [],
      });
    },
  },
});
