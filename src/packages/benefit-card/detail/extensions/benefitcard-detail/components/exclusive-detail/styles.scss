.exclusive-detail {
  padding: 0 24px 20px;
  .btm-cont {
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    .epd-tip {
      color: #ccc;
    }
    .rst-n-i {
      font-weight: 500;
    }
  }
  .btm-cont-p {
    padding-top: 12px;
  }
  .mid-l {
    width: 100%;
    height: 1px;
    position: relative;
    &::after {
      content: '';
      width: 200%;
      height: 1px;
      background-color: #e0e0e0;
      position: absolute;
      top: 0;
      left: 0;
      transform: scale(0.5);
      transform-origin: 0 0;
    }
  }
  .c-lt {
    padding: 0 2px;
    .c-im {
      font-size: 12px;
      font-weight: 500;
      display: flex;
      justify-content: space-between;
      padding-top: 20px;
      .im-l {
        max-width: 90%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
