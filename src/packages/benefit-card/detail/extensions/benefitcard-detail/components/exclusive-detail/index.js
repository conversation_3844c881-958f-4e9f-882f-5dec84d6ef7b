/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import get from '@youzan/weapp-utils/lib/get';
import { isNil } from '../../../../../utils';

RantaWidget({
  data: {
    showCode: false,
    showDetail: false,
    rewardTip: '',
    cycleTime: '',
    usedCount: 0,
  },

  properties: {
    themeGeneral: String,
    hasCard: Boolean,
    benfitKey: String,
    hasUseAbility: Boolean,
    cardIsExpired: Boolean,
    benefit: {
      type: Object,
      value: {},
    },
    experienceLog: Array,
    userBenefitDetail: {
      type: Object,
      value: {
        couponList: [],
        presentList: [],
      },
    },
    cardNo: String,
  },
  methods: {
    computedCycleTime() {
      const dateMap = {
        1: '日',
        7: '周',
        30: '月',
        90: '季度',
        365: '年',
      };
      const { grantCycle = false } = this.properties.benefit;
      if (grantCycle) {
        return `本${dateMap[grantCycle.termDays]}`;
      }
      return '';
    },
    computedRewardTip() {
      const reward = get(
        this.properties.benefit,
        'extInfo.scrm_bn_biz_extends_info',
        null
      );
      if (reward) {
        try {
          const rewardData = JSON.parse(reward);
          const rewardValue = get(rewardData, '[0].rewardValue', '');
          return `+${rewardValue}`;
        } catch (e) {
          return '';
        }
      }
      return '';
    },
    computedShowDetail(curShowCode) {
      const { benfitKey, hasUseAbility } = this.properties;
      if (!benfitKey && curShowCode) {
        return true;
      }
      if (benfitKey === 'experienceCard' && hasUseAbility) {
        return true;
      }
      if (benfitKey === 'present' || benfitKey === 'coupon') {
        return true;
      }
      return false;
    },
    computedShowCode() {
      if (this.checkCanGetRecordList(this.properties.benefit)) {
        return true;
      }
      return false;
    },
    checkCanGetRecordList(benefit) {
      const { hasUseAbility } = this.properties;
      return (
        hasUseAbility &&
        benefit?.benefitPluginInfo?.applyStock &&
        benefit?.key !== 'discount' &&
        !isNil(benefit?.resetNum)
      );
    },
    init() {
      const curShowCode = this.computedShowCode();
      const curShowDetail = this.computedShowDetail(curShowCode);
      this.setYZData({
        showCode: curShowCode,
        showDetail: curShowDetail,
        cycleTime: this.computedCycleTime(),
      });
    },
    getRecordList() {
      const { benefit } = this.properties;
      if (!this.checkCanGetRecordList(benefit) || !benefit.benefitId) {
        return;
      }
      getApp()
        .request({
          path: '/wscuser/scrm/api/benefitcard/getBenefitRecordList.json',
          data: {
            card_no: this.properties.cardNo,
            benefit_id: benefit.benefitId,
          },
        })
        .then((res) => {
          this.setYZData({
            usedCount: ((res || {}).items || []).reduce(
              (prev, cur) => prev + (cur.num || 0),
              0
            ),
          });
        });
    },
  },
  attached() {
    this.init();
    this.getRecordList();
    this.setYZData({
      rewardTip: this.computedRewardTip(),
    });
  },
});
