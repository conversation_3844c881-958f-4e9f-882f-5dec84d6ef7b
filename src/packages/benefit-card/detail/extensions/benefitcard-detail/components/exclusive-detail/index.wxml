<view wx:if="{{ showDetail }}" class="exclusive-detail">
  <view class="mid-l"></view>
  <view wx:if="{{ benfitKey === 'coupon' && userBenefitDetail.couponList && userBenefitDetail.couponList.length }}" class="c-lt">
    <view class="c-im" wx:for="{{ userBenefitDetail.couponList }}" wx:key="{{ item.key }}" wx:for-index="index">
      <view class="im-l">{{ item.title }}</view>
      <view>{{ item.number }}张</view>
    </view>
  </view>
  <block wx:if="{{ benfitKey === 'present' }}">
    <exclusive-gift 
      card-is-expired="{{cardIsExpired }}"
      gifts="{{ userBenefitDetail.presentList }}"
      has-carrier="{{ userBenefitDetail.hasCarrierTpl }}"
      theme-general="{{ themeGeneral }}"
    />
  </block>
  <view wx:if="{{ showCode }}" class="btm-cont">
    <view class="rst-n-i">剩余：{{ cycleTime }}{{ benefit.resetNum || 0 }}次</view>
    <view class="epd-tip" wx:if="{{ cardIsExpired }}">权益卡已过期，无法使用</view>
    <exclusive-code
      wx:else
      benefit-id="{{ benefit.benefitId }}"
      stock-num="{{ benefit.resetNum }}"
      theme-general="{{ themeGeneral }}"
    />
  </view>
  <view wx:if="{{ usedCount > 0 }}" class="btm-cont btm-cont-p">
    <view class="rst-n-i">已用：{{ usedCount }}次</view>
    <exclusive-code
      is-record="{{ true }}"
      benefit-id="{{ benefit.benefitId }}"
      theme-general="{{ themeGeneral }}"
      card-no="{{ cardNo }}"
    />
  </view>
  <block wx:if="{{ hasCard && benefit.key === 'experienceCard' }}">
    <experience-log
      number="{{ benefit.stockNum || 0 }}"
      experience-log="{{ experienceLog }}"
      reward-tip="{{ rewardTip }}"
    />
  </block>
</view>