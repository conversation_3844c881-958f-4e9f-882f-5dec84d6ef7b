<!-- 按次权益底部会员码按钮 -->
<view class="co-btn-wra">
  <view
    class="code-btn"
     bindtap="changePopup"
  >
    <view style="color: {{ themeGeneral }}">{{ isRecord ? '核销记录' : '立即使用' }}</span>
    <van-icon class="arr-ic" name="arrow" />
  </view>
</view>
<!-- 按次权益会员码组件 -->
<van-popup
  show="{{ show }}"
  close-on-click-overlay="{{ false }}"
  class="co-po-wra"
  position="bottom"
  round
  custom-style="height: 600px;border-radius: 20px 20px 0 0;"
>
  <view class="co-po">
    <view class="cont">
      <van-icon class="close-pop-icon" color="#969799" size="18px" name="cross" bindtap="changePopup" />
      <view class="van-dialog__header">向商家展示会员码</view>
      <image src="{{ bar_code }}" class="barcode" />
      <image src="{{ qr_code }}" class="qrcode" />
    </view>
  </view>
  <view class="use-rest-info">
    <text class="info">当前剩余<text class="stock-num" style="color: {{ themeGeneral }}">{{ stockNum }}</text>次</text>
  </view>
  <view class="use-rest-info">使用时，出示此码</view>
</van-popup>