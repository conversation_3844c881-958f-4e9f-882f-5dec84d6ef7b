import { RantaWidget } from 'shared/common/base/ranta/widget';
import get from '@youzan/weapp-utils/lib/get';
import openWebView from 'utils/open-web-view';

const app = getApp();

RantaWidget({
  properties: {
    themeGeneral: String,
    benefitId: {
      type: String,
      value: '',
    },
    stockNum: {
      type: Number,
      value: '',
    },
    isRecord: Boolean,
    cardNo: String,
  },
  data: {
    qr_code: '',
    bar_code: '',
    codeLoading: false,
    show: false,
    idVisible: false,
  },
  methods: {
    initData() {
      this.getQrcode();
    },
    // 获取一维码和二维码
    getQrcode() {
      app
        .request({
          path: '/wscuser/scrm/api/benefitcard/getBenefitQrCodeAndBarCode.json',
          query: {
            benefit: this.properties.benefitId,
          },
        })
        .then((res) => {
          this.setYZData({
            qr_code: res.qr_code,
            bar_code: res.bar_code,
            codeLoading: false,
          });
        })
        .catch((e) => {
          wx.showToast({
            title: e.msg || '出错了',
            icon: 'error',
            duration: 1000,
          });
          this.setYZData({
            codeLoading: false,
          });
        });
    },
    // 是否展示卡号对应二维码和条形码
    getCardCodeVisible() {
      const { hasCard, isEnabled, isExpired } = this;
      const activated = get(this.cardInfo, 'activated', false);
      const activationCondition = get(
        this.cardInfo,
        'cardTemplateDTO.activationCondition',
        {}
      );
      const needActivated =
        activationCondition.requireMobile && activationCondition.requireProfile;
      return (
        hasCard &&
        isEnabled &&
        !isExpired &&
        ((needActivated && activated) || !needActivated)
      );
    },
    changePopup() {
      const { isRecord, cardNo, benefitId } = this.properties;
      if (isRecord) {
        openWebView(
          `/wscuser/scrm/benefitcard/recordlist?kdt_id=${app.getKdtId()}&card_no=${cardNo}&benefit_id=${benefitId}`
        );
        return;
      }
      this.setYZData({
        show: !this.data.show,
      });
      if (this.data.show) {
        this.initData();
      }
    },
    showBarCode() {
      this.setYZData({
        idVisible: true,
      });
    },
  },
  attached() {
    this.initData();
  },
});
