import get from '@youzan/weapp-utils/lib/get';
// eslint-disable-next-line @youzan-open/tee/valid-same-extension-import
import {
  PAY,
  GRANT_IN_DAYS,
  GRANT_TO_ENDLESS,
  BEGIN_AT_TO_END_AT,
  NEW_COLORS,
  NO_RULE,
  COLOR_MAP,
} from '../../../constants';
// eslint-disable-next-line @youzan-open/tee/valid-same-extension-import
import { formatDateStr } from '../../../utils';

// 处理卡片有效期
export const getCardExpire = (curCardInfo, curHasCard) => {
  const lifeTime = get(curCardInfo, 'cardTemplateDTO.lifeTime', {});
  const { termType, termBeginAt, termDays, termEndAt } = lifeTime;
  // 永久有效
  if (curHasCard) {
    // 已经领卡，直接取用户卡过期信息
    if (curCardInfo.termEndAt === 0) {
      return { type: 'normal', text: '永久' };
    }
  }
  // 未领卡， 取卡模版过期信息
  switch (termType) {
    case BEGIN_AT_TO_END_AT:
      return {
        type: 'inverval',
        text: `${formatDateStr(termBeginAt)} 至 ${formatDateStr(termEndAt)}`,
      };
    case GRANT_IN_DAYS:
      return { type: 'normal', text: `${termDays}天` };
    case GRANT_TO_ENDLESS:
      return { type: 'normal', text: '永久' };
    default:
      return null;
  }
};

// 获取是否支持续费
export const getIsRenewable = (hasCard, cardTplInfo) => {
  const enabled = get(cardTplInfo, 'enabled', false);
  return (
    hasCard &&
    enabled &&
    cardTplInfo.isRenewable &&
    cardTplInfo.isCurrentShopAvailable &&
    cardTplInfo.cardGoods &&
    cardTplInfo.cardGoods.isDisplay &&
    cardTplInfo.cardGoods.skuList.length > 0 &&
    cardTplInfo.cardGoods.skuList[0].stock > 0
  );
};

// 领卡后判断卡是否过期
export const checkExpireHasCard = (cardInfo) => {
  const termEndAt = get(cardInfo, 'termEndAt');
  const currentDate = new Date().getTime();
  return termEndAt > 0 && termEndAt < currentDate;
};

// 如果是老版本的会员卡，将colorCode映射到新的
export const getColorCode = ({ colorCode }) => {
  return COLOR_MAP[colorCode] || colorCode;
};

// 未领卡时判断是否过期
export const checkExpireWithoutCard = (cardInfo) => {
  const { termType = GRANT_TO_ENDLESS, termEndAt = 0 } = get(
    cardInfo,
    'cardTemplateDTO.lifeTime'
  );
  switch (termType) {
    case GRANT_TO_ENDLESS:
      return false;
    case BEGIN_AT_TO_END_AT: {
      const dataNow = new Date().getTime();
      return termEndAt > 0 && termEndAt < dataNow;
    }
    case GRANT_IN_DAYS:
      return false;
    default:
      return false;
  }
};

export const getIsExpires = (hasCard, cardInfo) => {
  return hasCard
    ? checkExpireHasCard(cardInfo)
    : checkExpireWithoutCard(cardInfo);
};

// 获取卡背景相关
export const getCardStyle = (colorCode, coverUrl) => {
  if (coverUrl) {
    return {
      imageUrl: `url(${coverUrl})`,
    };
  }
  return {
    bgColor: NEW_COLORS[colorCode] ? NEW_COLORS[colorCode].bgColor : '',
    imageUrl: NEW_COLORS[colorCode]
      ? `url(${NEW_COLORS[colorCode].imageUrl})`
      : '',
  };
};
// 获取cardGoods
export const formatCardGoods = (cardGoods) => {
  let innerCardGoods = {};
  if (cardGoods) {
    // eslint-disable-next-line prefer-object-spread
    innerCardGoods = Object.assign({}, cardGoods, {
      maxPrice:
        '' +
        Math.max.apply(
          null,
          cardGoods.skuList.map((item) => item.price)
        ),
    });
  }
  return innerCardGoods;
};

// 检查付费卡是否已售罄
export const checkIsSoldOut = (cardTplInfo) => {
  const cardType = get(cardTplInfo, 'cardAcquireSetting', NO_RULE);
  return (
    cardType === PAY && get(cardTplInfo, 'cardGoods.skuList[0].stock', 0) <= 0
  );
};

export const compCardExpire = (expire, curPrice) => {
  if (!expire) return '';

  const { type, text } = expire || {};
  if (type === 'inverval') return '';

  if (type === 'normal') {
    return curPrice ? { type: 1, val: text } : { val: `有效期：${text}` };
  }
};

export const checkIsNeedTranslateGoodsId = (data) => {
  // 如果选中的店铺是大网店总部，不需要转换
  if (data.isUnitedHqStore || !data.isChainStore) {
    return false;
  }
  return data.isChainHqStore || data.isPartnerStore || data.isRenew;
};

// 复用页面，可能有多个打点值，需要url中带上指定query作区分
const reuseRouteMap = {
  'packages/paidcontent/list/index': 'type',
};

export function getCurrentPageUrl() {
  // 获取加载的页面
  const pages = getCurrentPages();
  // 获取当前页面的对象
  const currentPage = pages[pages.length - 1];
  // 当前页面url
  let url = currentPage.route;
  // 复用url，拼接上识别query str
  if (
    reuseRouteMap[url] &&
    currentPage.options &&
    currentPage.options[reuseRouteMap[url]]
  ) {
    url = `${url}?${reuseRouteMap[url]}=${
      currentPage.options[reuseRouteMap[url]]
    }`;
  }
  return url;
}
