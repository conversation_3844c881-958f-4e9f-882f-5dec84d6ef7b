/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';

RantaWidget({
  attached() {
    mapState(this, [
      'loading',
      'isChainStore',
      'isCurrentShopAvailable',
      'availableTips',
      'servicePhone',
      'hasFetchedChainShopInfo',
    ]);
  },
  methods: {
    // 打电话
    takeCall() {
      if (this.data.servicePhone) {
        wx.makePhoneCall({
          phoneNumber: this.data.servicePhone,
        });
      }
    },
  },
});
