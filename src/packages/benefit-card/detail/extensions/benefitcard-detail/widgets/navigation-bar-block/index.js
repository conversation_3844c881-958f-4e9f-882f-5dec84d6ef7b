import { RantaWidget } from 'shared/common/base/ranta/widget';
import navigate from 'shared/utils/navigate';
import { mapState } from '@ranta/store';

RantaWidget({
  attached() {
    mapState(this, ['statusH']);
  },
  methods: {
    /** 主页按钮 */
    onHomeTap() {
      navigate.switchTab({ url: `/pages/home/<USER>/index` });
    },
    /** 返回按钮 */
    onBackTap() {
      const pages = getCurrentPages();
      if (pages.length === 1) {
        this.onHomeTap();
      } else {
        // eslint-disable-next-line @youzan/dmc/wx-check
        wx.navigateBack();
      }
    },
  },
});
