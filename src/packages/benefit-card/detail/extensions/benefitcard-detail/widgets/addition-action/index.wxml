<view wx:if="{{ btnVisible && !loading && hasFetchedChainShopInfo && !isExpired && isEnabled && hasCard && isCurrentShopAvailable }}" class="btn-wrapper {{ hasCard ? '' : 'untake'}}">
  <view>
    <user-authorize
      wx:if="{{ action === 'activate' && activationCondition.requireMobile && !activated }}"
      authTypeList="{{ ['mobile'] }}"
      bind:next="handleCallAuth"
    >
      <theme-view color="general" border="general">
        <button class="action-btn theme-view-button action-btn__small">
          {{ btnText }}
        </button>
      </theme-view>
    </user-authorize>
    <theme-view wx:else color="general" border="general">
      <button
        class="action-btn theme-view-button action-btn__small"
        bindtap="bindClick"
      >
        {{ btnText }}
      </button>
    </theme-view>
  </view>
</view>
