/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import get from '@youzan/weapp-utils/lib/get';
import { mapState, mapActions } from '@ranta/store';

RantaWidget({
  attached() {
    mapState(this, [
      'hasMobile',
      'cardGoods',
      'price',
      'isRenewable',
      'cardSyncInfo',
      'syncWeixin',
      'activationCondition',
      'activated',
      'cardType',
      'hasCard',
      'isSoldOut',
      'isExpired',
      'isDisplay',
      'isEnabled',
      'loading',
      'isNeedWillRenew',
      'isRenewable',
      'isCurrentShopAvailable',
      'hasFetchedChainShopInfo',
    ]);
    mapActions(this, ['onBtnAction']);
    this.store.watch('loading', (val) => {
      if (!val) {
        this.initData();
      }
    });
    this.store.watch('syncWeixin', (nv, ov) => {
      if (nv !== ov) {
        this.initData();
      }
    });
    this.store.watch('activated', (nv, ov) => {
      if (nv !== ov) {
        this.initData();
      }
    });
  },
  data: {
    action: '',
    btnText: '',
    btnVisible: false,
  },
  methods: {
    initData() {
      this.formatBtnActionAndText();
    },
    formatBtnActionAndText() {
      const defaultData = {
        action: '',
        btnText: '',
        btnVisible: false,
      };
      const data = this.getBtnInfoHasCard(defaultData);
      this.setYZData({
        ...data,
      });
    },
    // 有卡时获取按钮信息
    getBtnInfoHasCard(defaultData) {
      const activateVisible = this.activateVisible();
      const syncWeixinVisible = this.syncWeixinVisible();
      let data;
      const result = [
        {
          cond: activateVisible,
          action: 'activate',
          btnText: '激活权益卡',
          btnVisible: true,
        },
        {
          cond: syncWeixinVisible,
          action: 'addWxCard',
          btnText: '添加到微信卡包',
          btnVisible: true,
        },
      ];

      result.some((item) => {
        if (item.cond) {
          data = item;
          return true;
        }
        return false;
      });
      return data || defaultData;
    },
    // 是否显示激活按钮
    activateVisible() {
      const { activationCondition = {}, activated } = this.data;
      const { requireMobile, requireProfile } = activationCondition;
      return (requireMobile || requireProfile) && !activated;
    },
    // 是否显示同步微信卡包
    syncWeixinVisible() {
      const { syncWeixin, cardSyncInfo } = this.data;
      const isSyncWeixin = get(
        cardSyncInfo,
        'cardWeixinSyncInfo.isSyncWeixin',
        false
      );
      const syncWeixinState = get(
        cardSyncInfo,
        'cardWeixinSyncInfo.syncWeixinState',
        -1
      );
      // syncWeixinState 是卡模版同步到微信的状态，3 表示同步成功，否则不展示同步卡包的按钮
      return isSyncWeixin && syncWeixinState === 3 && !syncWeixin;
    },
    // 按钮点击事件
    bindClick() {
      const { action } = this.data;
      if (action === '') return;
      this.onBtnAction({ action });
    },

    handleCallAuth() {
      const { action } = this.data;
      this.onBtnAction({ action }, { action, bindPhone: true });
    },
  },
});
