/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { node } from 'shared/utils/request';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import Args from '@youzan/weapp-utils/lib/args';
import money from '@youzan/weapp-utils/lib/money';
import getSystemInfo from 'shared/utils/browser/system-info';
import appLoggerBehavior from 'shared/components/showcase/behaviors/app-logger-behavior';
import { mapState } from '@ranta/store';

import { RULE, PAY } from '../../../../../constants';

const { toCamelCase: mapKeysToSnakeCase } = mapKeysCase;
const RECOMMEND_NAME = '更多精选商品';
const PAGE_RANDOM_NUMBER = makeRandomString(8);
const PLACEHOLDER_SELECTOR = '#placeholder';

const goodsComponentData = {
  goods: [],
  image_ratio: 1,
  layout: 1,
  size_type: 0,
  show_buy_button: false,
  buy_button_type: 1,
  buy_btn_express: false,
  button_text: '',
  show_title: true,
  show_sub_title: false,
  show_price: true,
  show_corner_mark: false,
  corner_mark_type: 0,
  corner_mark_image: '',
  default_image_url: '',
  image_fill_style: 1,
  page_margin: 7,
  goods_margin: 6,
  border_radius_type: 1,
  text_align_type: 'left',
  text_style_type: 2,
};

RantaWidget({
  behaviors: [appLoggerBehavior],

  data: {
    goodsComponentData,
    RECOMMEND_NAME,
  },

  attached() {
    mapState(this, ['cardType', 'spmId']);
    this.store.watch('loading', (val) => {
      if (!val) {
        this.goodsList = goodsComponentData.goods;
        this.refresh();
        this.createIntersectionObserver()
          .relativeToViewport({ bottom: 100 })
          .observe(PLACEHOLDER_SELECTOR, () => {
            if (this.data.goodsComponentData.goods !== this.goodsList) {
              this.setYZData({
                'goodsComponentData.goods': this.goodsList,
              });
              this.triggerEvent('afterload', this.goodsList.length);
            }
          });
      }
    });
  },

  methods: {
    renderGoods() {
      if (this.data.goodsComponentData.goods === this.goodsList) {
        return;
      }

      this.createSelectorQuery()
        .select(PLACEHOLDER_SELECTOR)
        .boundingClientRect((rect) => {
          if (!rect) {
            return;
          }

          const { screenHeight } = getSystemInfo() || {};
          // 在视图窗口内
          if (!screenHeight || (rect.top > 0 && rect.top < screenHeight)) {
            // debugger;
            this.setYZData({
              'goodsComponentData.goods': this.goodsList,
            });
          }
        })
        .exec();
    },

    refresh() {
      if (this.isFetching) {
        return;
      }

      this.isFetching = true;
      node({
        path: '/wscuser/scrm/api/benefitcard/getGoodsList.json',
        data: {
          itemSize: 6,
        },
      })
        .then((goodsList = []) => {
          const loggerList = [];
          const cardType = this.getCardType(this.data.cardType);
          this.goodsList = mapKeysToSnakeCase(goodsList).map((item, index) => {
            const bannerId = `${cardType}.${this.data.spmId}~recommend_fixed~${
              index + 1
            }~${PAGE_RANDOM_NUMBER}`;
            const baseParams = {
              alg: item.algs,
              banner_id: bannerId,
              recommend_name: RECOMMEND_NAME,
            };

            const loggerParams = {
              component: 'recommend_fixed',
              goods_id: item.id + '',
              item_id: item.id + '',
              item_type: 'goods',
              ...baseParams,
            };

            item.loggerParams = loggerParams;
            item.url = Args.add('/pages/goods/detail/index', {
              alias: item.alias,
              ...baseParams,
            });
            item.price = money(item.price).toYuan();
            loggerList.push(loggerParams);
            return item;
          });

          this.ensureAppLogger('view', loggerList);
        })
        .catch(() => {
          // do nothing
        })
        .then(() => {
          this.isFetching = false;
          this.renderGoods();
        });
    },

    getCardType(cardType) {
      switch (cardType) {
        case PAY:
          return 'paycard';
        case RULE:
          return 'rulecard';
        default:
          return 'quanyika';
      }
    },
  },
});
