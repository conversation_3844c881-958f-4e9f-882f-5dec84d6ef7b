/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { RantaWidget } from 'shared/common/base/ranta/widget';
// eslint-disable-next-line @youzan-open/tee/valid-import-blacklist
import openWebView from 'utils/open-web-view';
import { mapState, mapActions } from '@ranta/store';

RantaWidget({
  attached() {
    mapState(this, [
      'isIphoneX',
      'themeColors',
      'agreed',
      'btnVisible',
      'loading',
      'isEnabled',
      'isExpired',
      'isNeedWillRenew',
      'isRenewable',
      'isCurrentShopAvailable',
      'hasCard',
      'hasFetchedChainShopInfo',
      'customShowProtocol',
    ]);
    mapActions(this, ['SET_AREED']);
  },
  methods: {
    handleAgreedChange(ev) {
      this.SET_AREED({ agreed: ev.detail });
    },
    viewAgreement() {
      openWebView('https://www.youzan.com/intro/rule/detail', {
        query: {
          alias: 'roattxf9',
          pageType: 'rules',
        },
      });
    },
  },
});
