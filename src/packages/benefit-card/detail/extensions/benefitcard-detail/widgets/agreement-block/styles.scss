  .ag-wrap {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 8;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 200%;
      height: 1px;
      background-color: #e0e0e0;
      transform: scale(0.5);
      transform-origin: 0 0;
    }
    padding: 10px 20px;
    background-color: #fff;
    font-size: 12px;
    display: flex;
    margin-bottom: 60px;

    .ag-label {
      color: #969799;
    }

    .van-checkbox {
      align-items: center;
    }

    .agreement {
      display: inline-block;
      color: #323233;
    }
  }

.is-n-ph {
  margin-bottom: calc(60px + constant(safe-area-inset-bottom)) !important;
  margin-bottom: calc(60px + env(safe-area-inset-bottom)) !important;
}