
<view class="ag-wrap {{ isIphoneX ? 'is-n-ph' : '' }}" wx:if="{{btnVisible && customShowProtocol && !loading && hasFetchedChainShopInfo && isEnabled && (!isExpired || (isNeedWillRenew && isRenewable)) && (isCurrentShopAvailable || hasCard )}}">
  <van-checkbox checked-color="{{ themeColors.general }}" value="{{ agreed }}" bind:change="handleAgreedChange">
    <view class="ag-label">
      已阅读并同意
      <view class="agreement" style="color: {{ themeColors.general }}" bindtap="viewAgreement">《权益卡使用协议》</view>
    </view>
  </van-checkbox>
</view>