<view class="exc-con" wx:if="{{ !loading && hasFetchedChainShopInfo }}">
  <view class="title" wx:if="{{ benefitsList && benefitsList.length }}">专属权益</view>
  <block wx:for="{{ benefitsList }}" wx:key="{{ item.key }}" wx:for-index="index">
    <exclusive-block 
      benefit="{{ item }}"
      user-benefit-detail="{{ userBenefitDetail }}"
      has-card="{{ hasCard }}"
      has-use-ability="{{ hasUseAbility }}"
      card-is-expired="{{isExpired }}"
      theme-general="{{ themeColors.general }}"
      is-current-shop-available="{{ isCurrentShopAvailable }}"
    >
      <exclusive-detail
        has-card="{{ hasCard }}"
        has-use-ability="{{ hasUseAbility }}"
        benfit-key="{{ item.key }}"
        benefit="{{ item }}" 
        user-benefit-detail="{{ userBenefitDetail }}"
        experience-log="{{ experienceLog }}"
        card-is-expired="{{isExpired }}"
        theme-general="{{ themeColors.general }}"
        card-no="{{ cardNo }}"
      />
    </exclusive-block>
  </block>
  <!-- 权益有效期 -->
  <view class="exc-exp" style="padding-top: 28px">
    <view class="exp-tit">权益有效期</view>
    <view class="exp-da">{{ cardExpire }}</view>
  </view>
  <!-- 使用说明 -->
  <view class="exc-exp">
    <view class="exp-tit">使用说明</view>
    <view class="in-cont">{{ cardDescription }}</view>
  </view>
</view>
