/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { RantaWidget } from 'shared/common/base/ranta/widget';

import get from '@youzan/weapp-utils/lib/get';
import { mapState } from '@ranta/store';

import { formatBenefits, formatDateStr } from '../../../../../utils';
import {
  FORM_CARD,
  CARD_TYPE_MAP,
  BEGIN_AT_TO_END_AT,
  GRANT_IN_DAYS,
  GRANT_TO_ENDLESS,
} from '../../../../../constants';

const app = getApp();

RantaWidget({
  data: {
    hasCard: false, // 当前用户是否已领取卡
    benefitsList: [],
    userBenefitDetail: {},
    hasUseAbility: false,
    experienceLog: [],
    cardExpire: '',
    cardDescription: '',
  },
  attached() {
    mapState(this, [
      'loading',
      'hasFetchedChainShopInfo',
      'themeColors',
      'cardInfo',
      'benefitBag',
      'subType',
      'alias',
      'isExpired',
    ]);
    this.store.watch('loading', (val) => {
      if (!val) {
        this.initData();
      }
    });
  },
  methods: {
    initData() {
      this.getBenefitDetail();
      this.formatCardInfo(this.data.cardInfo);
    },
    formatCardInfo(cardInfo) {
      const {
        hasCard = false,
        cardTemplateDTO: cardTplInfo,
        cardNo = 0,
        subType = FORM_CARD,
        activated: hasActivated,
      } = cardInfo;
      // cardInfo 上的数据主要是与用户相关的数据
      // cardInfo.cardTemplateDTO 是卡相关的数据
      const benefitBag = get(cardTplInfo, 'benefitBag', {});
      const curCardDescription = get(cardTplInfo, 'description', '');
      let stockNum = get(benefitBag, 'experienceCard.stockNum', 0);
      const requireProfile = get(
        cardTplInfo,
        'activationCondition.requireProfile',
        true
      );
      const requireMobile = get(
        cardTplInfo,
        'activationCondition.requireMobile',
        true
      );
      const cardType = get(
        cardTplInfo,
        'cardAcquireSetting',
        CARD_TYPE_MAP.NO_RULE
      );
      const needActivated = requireProfile && requireMobile;
      const isCurrentShopAvailable = get(
        cardTplInfo,
        'isCurrentShopAvailable',
        false
      );
      let benefitsList = [];
      // 拥有卡并激活处理
      const hasUseAbility =
        (hasCard && !needActivated) ||
        (hasCard && needActivated && hasActivated);
      // 如果当前卡未激活，不允许分享
      if ((needActivated && !hasActivated) || !isCurrentShopAvailable) {
        wx.hideShareMenu && wx.hideShareMenu();
      }

      // 有使用卡的权限，获取c端权益列表
      if (hasUseAbility) {
        app
          .request({
            path: '/wscuser/scrm/api/benefitcard/getBenefitsList.json',
            data: {
              card_no: cardNo,
            },
          })
          .then((data) => {
            benefitsList = formatBenefits(data, subType, 'Tpl', benefitBag);
            stockNum = get(benefitBag, 'experienceCard.stockNum', 0);
            if (stockNum && stockNum > 0 && cardNo) {
              this.getExperienceLog(cardNo, stockNum);
            } else {
              this.setYZData({
                experienceLog: [],
                tokenNum: 0,
              });
            }
            this.setYZData({
              hasCard,
              cardInfo,
              stockNum,
              cardNo,
              hasUseAbility,
              openType: needActivated && !hasActivated ? '' : 'share',
              benefitsList,
              empty: benefitsList.length === 0,
              cardType,
              loading: false,
              isCurrentShopAvailable,
              cardDescription: curCardDescription,
              cardExpire: this.computedCardExpire(hasCard),
            });
          })
          .catch((err) => {
            wx.hideLoading();
            wx.showToast({
              title: err.msg,
              icon: 'none',
              duration: 1000,
            });
          });
      } else {
        benefitsList = formatBenefits(benefitBag, subType);
        if (stockNum && stockNum > 0 && cardNo) {
          this.getExperienceLog(cardNo, stockNum);
        } else {
          this.setYZData({
            experienceLog: [],
            tokenNum: 0,
          });
        }
        this.setYZData({
          hasCard,
          cardInfo,
          stockNum,
          cardNo,
          openType: needActivated && !hasActivated ? '' : 'share',
          benefitsList,
          empty: benefitsList.length === 0,
          cardType,
          loading: false,
          hasUseAbility,
          cardDescription: curCardDescription,
          cardExpire: this.computedCardExpire(hasCard),
        });
      }
    },
    getExperienceLog(cardNo) {
      app
        .request({
          path: '/wscuser/scrm/api/benefitcard/getExperienceLog.json',
          data: { cardNo },
        })
        .then((res) => {
          this.setYZData({
            experienceLog: res.items,
          });
        });
    },
    // 获取卡信息
    getBenefitDetail() {
      return app
        .request({
          path: '/wscuser/scrm/api/benefitcard/benefit-info.json',
          data: {
            carrierType: '1', // 1 权益卡 2 等级
            carrierTplAlias: this.data.alias,
          },
        })
        .then((res) => {
          this.setYZData({
            userBenefitDetail: res,
          });
        })
        .catch(() => {});
    },
    computedCardExpire(curHasCard) {
      const { cardInfo } = this.data;
      const lifeTime = get(cardInfo, 'cardTemplateDTO.lifeTime', {});
      const { termType, termBeginAt, termDays, termEndAt } = lifeTime;
      // 永久有效
      if (curHasCard) {
        // 已经领卡，直接取用户卡过期信息
        if (cardInfo.termEndAt === 0) {
          return '永久有效';
        }
        return `${formatDateStr(cardInfo.termBeginAt)} 至 ${formatDateStr(
          cardInfo.termEndAt
        )}`;
      }
      // 未领卡， 取卡模版过期信息
      if (termType === BEGIN_AT_TO_END_AT) {
        return `${formatDateStr(termBeginAt)} 至 ${formatDateStr(termEndAt)}`;
      }
      if (termType === GRANT_IN_DAYS) {
        return `购买后${termDays}天内有效`;
      }
      if (termType === GRANT_TO_ENDLESS) {
        return '永久有效';
      }

      return '';
    },
  },
});
