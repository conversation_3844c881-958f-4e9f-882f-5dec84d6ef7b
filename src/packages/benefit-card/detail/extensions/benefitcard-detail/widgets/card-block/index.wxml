
<view class="card-block" wx:if="{{ !loading && hasFetchedChainShopInfo }}">
  <view class="banner-wrapper" style="padding-top: {{ statusH }}px">
    <view class="card-bg">
      <view class="in" style="background-image: {{ cardStyle.imageUrl }}" />
    </view>
    <view class="card-container" style="width: {{ cardWrapperStyle.width }};height: {{ cardWrapperStyle.height }};">
      <view class="banner">
        <view class="co-u-bg" style="background-image: {{ cardStyle.imageUrl }};"></view>
        <view wx:if="{{ showCardTips }}" class="exp-t {{ action && 'no-co' }}">
          <view class="t-c" bindtap="executeAction" wx:if="{{action && btnText}}">
            <view>{{ btnText }}</view>
            <van-icon name="arrow" />
          </view>
          <view wx:else class="t-c">{{ cardTips ? cardTips: '已过期' }}</view>
        </view>
        <view class="ca-co" wx:if="{{ cardCodeVisible }}">
          <image class="code-image" src="//img.yzcdn.cn/public_files/2019/02/21/e0a192cee152c41f4a6efe05da1ebef7.png" bindtap="openCodeDialog"></image>
        </view>
      </view>
    </view>
  </view>
  <view class="c-i-c">
    <view class="c-name">{{ cardName }}</view>
    <view class="e-icon" wx:if="{{ subType === exType }}"><view class="icon-inner" style="background: {{ themeColors.general }}">体验卡</view></view>
    <view wx:if="{{ !hasCard }}" class="c-p-info {{ (price || topCardExpire) ? 'block-comm': '' }}">
      <view class="info-price" wx:if="{{ price }}">{{ price }}</view>
      <view class="in-expi" wx:if="{{ topCardExpire }}">
        <view wx:if="{{ topCardExpire.type === 1 }}">/</view>
        <view class="in-t">{{ topCardExpire.val }}</view>
      </view>
    </view>
  </view>
  <!-- 二维码弹窗 -->
  <van-popup use-slot async-close show="{{ modalVisible }}" show-confirm-button="{{ false }}" custom-style="background-color: transparent;" custom-class="code-dialog">
    <view class="content">
      <view wx:if="{{ codeLoading }}" class="code-loading">
        <van-loading type="spinner">加载中</van-loading>
      </view>
      <block wx:else>
        <view class="van-dialog__header">{{ cardName }}</view>
        <image src="{{ cardCodeInfo.bar_code }}" class="barcode" mode="widthFix" />
        <view class="b-num">{{ cardNo }}</view>
        <image src="{{ cardCodeInfo.qr_code }}" class="qrcode" />
      </block>
    </view>
    <van-icon custom-class="dialog-close" name="close" bindtap="closeCodeDialog" />
  </van-popup>
</view>

