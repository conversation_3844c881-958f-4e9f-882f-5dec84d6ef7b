/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import get from '@youzan/weapp-utils/lib/get';
import money from '@youzan/weapp-utils/lib/money';
import {
  RULE,
  NO_RULE,
  PAY,
  GRANT_TO_ENDLESS,
  BEGIN_AT_TO_END_AT,
  GRANT_IN_DAYS,
} from '../../../../../constants';
import { formatDateStr } from '../../../../../utils';
import { mapState, mapActions } from '@ranta/store';

const app = getApp();

RantaWidget({
  data: {
    modalVisible: false,
    cardCodeInfo: {
      qr_code: '',
      bar_code: '',
    },
    expireTips: '',
    codeLoading: true,
    cardName: '',
    action: '',
    btnText: '',
    cardTips: '',
    showCardTips: false,
    cardCodeVisible: false,
    cardStatus: '', // 存储权益卡状态
  },
  attached() {
    mapState(this, [
      'loading',
      'cardInfo',
      'cardType',
      'cardStyle',
      'isEnabled',
      'isDisplay',
      'isExpired',
      'isSoldOut',
      'hasCard',
      'cardNo',
      'isRenewable',
      'cardWrapperStyle',
      'statusH',
      'cardName',
      'subType',
      'exType',
      'themeColors',
      'price',
      'topCardExpire',
      'hasFetchedChainShopInfo',
    ]);
    mapActions(this, ['onRenew']);
    this.store.watch('loading', (val) => {
      if (!val) {
        this.initData();
      }
    });
  },
  methods: {
    // 打开二维码弹窗
    openCodeDialog() {
      this.setYZData({
        modalVisible: true,
        codeLoading: true,
      });
      this.getQrcode();
    },
    // 关闭二维码弹窗
    closeCodeDialog() {
      this.setYZData({
        modalVisible: false,
      });
    },
    // 获取一维码和二维码
    getQrcode() {
      app
        .request({
          path: '/wscuser/scrm/api/benefitcard/getQrcodeAndBarCode.json',
          query: {
            txt: this.data.cardNo,
          },
        })
        .then((res) => {
          this.setYZData({
            cardCodeInfo: res,
            codeLoading: false,
          });
        })
        .catch((e) => {
          wx.showToast({
            title: e.msg,
            icon: 'error',
            duration: 1000,
          });
          this.setYZData({
            codeLoading: false,
          });
        });
    },
    // 规则卡的体验卡过期信息处理
    ruleCardTips(type, count) {
      const tipsMap = {
        amountLimit: `消费${money(count).toYuan()}元可再次领卡`,
        tradeLimit: `完成${count}笔订单可再次领卡`,
        pointsLimit: `获得${count}积分可再次领卡`,
      };
      return tipsMap[type];
    },
    // 处理卡片有效期
    getCardExpire() {
      const { cardInfo, hasCard } = this.data;
      const lifeTime = get(cardInfo, 'cardTemplateDTO.lifeTime', {});
      const { termType, termBeginAt, termDays, termEndAt } = lifeTime;
      // 永久有效
      if (hasCard) {
        // 已经领卡，直接取用户卡过期信息
        if (cardInfo.termEndAt === 0) {
          return '永久有效';
        }
        return `${formatDateStr(cardInfo.termBeginAt)} 至 ${formatDateStr(
          cardInfo.termEndAt
        )}`;
      }
      // 未领卡， 取卡模版过期信息
      switch (termType) {
        case BEGIN_AT_TO_END_AT:
          return `${formatDateStr(termBeginAt)} 至 ${formatDateStr(termEndAt)}`;
        case GRANT_IN_DAYS:
          return `${termDays}天`;
        case GRANT_TO_ENDLESS:
          return '永久有效';
        default:
          return '';
      }
    },
    // 无门槛卡过期信息处理
    expireTipNoRule() {
      this.setYZData({
        showCardTips: true,
        cardStatus: 'expired',
      });
    },
    // 规则卡过期信息处理
    expireTipRule() {
      const { cardInfo } = this.data;
      const rule = get(cardInfo, 'cardTemplateDTO.ruleCondition', {});
      const ruleKeys = ['amountLimit', 'tradeLimit', 'pointsLimit'];
      let tip = '';
      ruleKeys.some((key) => {
        if (rule[key]) {
          tip = this.ruleCardTips(key, rule[key]);
          return true;
        }
        return false;
      });
      this.setYZData({
        cardTips: tip,
        showCardTips: true,
        cardStatus: 'expired',
      });
    },
    expireTipPay() {
      this.setYZData({
        showCardTips: true,
        cardStatus: 'expired',
      });
    },
    // 获取过期信息
    gCExpired() {
      const { cardType } = this.data;
      switch (cardType) {
        case NO_RULE:
          this.expireTipNoRule();
          break;
        case RULE:
          this.expireTipRule();
          break;
        default:
          this.expireTipPay();
          break;
      }
    },
    // 获取下架信息
    gCNotDisplay() {
      const { hasCard } = this.data;
      this.setYZData({
        cardTips: '已下架',
        showCardTips: !hasCard,
        cardStatus: 'off-self',
      });
    },
    // 获取禁用信息
    gCDisabled() {
      const { isEnabled } = this.data;
      this.setYZData({
        cardTips: '已禁用',
        showCardTips: !isEnabled,
        cardStatus: 'disabled',
      });
    },
    // 获取卡售罄信息
    gCSoldOut() {
      const { hasCard } = this.data;
      this.setYZData({
        cardTips: '已售罄',
        showCardTips: !hasCard,
        cardStatus: 'sold-out',
      });
    },
    renew() {
      this.onRenew('renewal');
    },
    // 获得需要续费信息
    gCNWillRenew() {
      const isRenewable = get(
        this.data.cardInfo,
        'cardTemplateDTO.isRenewable',
        false
      );

      const isCurrentShopAvailable = get(
        this.data.cardInfo,
        'cardTemplateDTO.isCurrentShopAvailable',
        false
      );

      const activated = get(this.data.cardInfo, 'activated', false);

      if (activated && isRenewable && isCurrentShopAvailable) {
        this.setYZData({
          cardTips: '',
          btnText: '立即续费',
          action: 'renew',
          showCardTips: true,
          cardStatus: '',
        });
      }
    },

    // 卡片过期后按钮操作
    executeAction() {
      this[this.data.action] && this[this.data.action]();
    },

    gCCodeVisible() {
      const { hasCard, isEnabled, isExpired } = this.data;
      const activated = get(this.data.cardInfo, 'activated', false);
      const activationCondition = get(
        this.data.cardInfo,
        'cardTemplateDTO.activationCondition',
        {}
      );
      const needActivated =
        activationCondition.requireMobile && activationCondition.requireProfile;
      return (
        hasCard &&
        isEnabled &&
        !isExpired &&
        ((needActivated && activated) || !needActivated)
      );
    },
    initData() {
      const cardName = get(this.data.cardInfo, 'cardTemplateDTO.name', '');
      this.setYZData({
        cardName,
        cardCodeVisible: this.gCCodeVisible(),
      });
      if (!this.data.isDisplay && this.data.cardType === PAY) {
        this.gCNotDisplay();
        if (!this.data.hasCard) return;
      }
      if (this.data.isExpired) {
        return this.gCExpired();
      }
      if (!this.data.isEnabled) {
        return this.gCDisabled();
      }
      if (this.data.isSoldOut && this.data.cardType === PAY) {
        return this.gCSoldOut();
      }

      if (
        !this.data.isExpired &&
        this.data.isEnabled &&
        this.data.cardType === PAY
      ) {
        this.gCNWillRenew();
      }
    },
  },
});
