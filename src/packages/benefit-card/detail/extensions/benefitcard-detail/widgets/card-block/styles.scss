.card-block {
  width: 100vw;
  overflow: hidden;
}

.banner-wrapper {
  max-width: 686px;
  max-height: 360px;
  margin: 0 auto;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;

  .card-container {
    padding: 24px 24px 16px;
    margin-top: 80rpx;
  }

  .card-code {
    width: 48rpx;
    height: 48rpx;
  }
  .card-bg {
    width: 1048rpx;
    height: 500rpx;
    position: absolute;
    top: 0;
    left: -149rpx;
    border-bottom-left-radius: 48%;
    border-bottom-right-radius: 48%;
    overflow: hidden;
    &::after {
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.1);
    }
    .in {
      width: 200%;
      height: 1000rpx;
      filter: blur(40px);
      background-repeat: no-repeat;
      background-size: 200%;
      background-position: center;
      position: absolute;
      top: -50%;
      left: -50%;
    }
  }
}

.banner {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  background-size: cover;
  background-repeat: no-repeat;
  color: #fff;
  font-size: 12px;
  box-sizing: border-box;
  padding: 23px 20px 0;
  border-radius: 12px;
  overflow: hidden;

  .co-u-bg {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 7px;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .exp-t {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    background-color: rgba(255, 255, 255, 0.5);
    flex-direction: column;
    justify-content: center;
    border-radius: 7px;
    &.no-co {
      background-color: rgba(0, 0, 0, 0);
    }

    .t-c {
      display: flex;
      align-content: center;
      position: absolute;
      bottom: 16px;
      right: 16px;
      color: #fff;
      background-color: rgba(0, 0, 0, 0.3);
      padding: 7px 16px;
      border-radius: 20px;
    }
  }
}

.ca-co {
  width: 64rpx;
  height: 64rpx;
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 8px;
  box-sizing: border-box;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  .code-image {
    width: 100%;
    height: 100%;
  }
}

.code-dialog {
  color: #333;
  text-align: center;
  .content {
    width: 610rpx;
    padding-bottom: 40px;
    margin: 0 auto 24px;
    background: #fff;
    border-radius: 12px;
    box-sizing: border-box;
    overflow: hidden;

    .code-loading {
      height: 160px;
      text-align: center;

      .van-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        margin: -45px 0 0 -15px;
      }
    }
  }

  .van-dialog__header {
    height: 80px;
    line-height: 80px;
    font-size: 14px;
    font-weight: bold;
    background-color: #fafcff;
    margin-bottom: 24px;
    box-sizing: border-box;
  }

  .van-dialog__content {
    box-sizing: border-box;
  }

  .qrcode {
    width: 320rpx;
    height: 320rpx;
    margin: 0 auto;
  }

  .barcode {
    width: 546rpx;
    height: 170rpx;
    margin: 0 auto;
  }

  .b-num {
    margin: 8px 0 16px;
    height: 17px;
    line-height: 17px;
    font-size: 12px;
    color: #999;
  }

  .dialog-close {
    color: #979797;
    font-size: 32px;
  }
}


.c-i-c {
  margin: 0 24px;
  position: relative;
  padding-bottom: 21px;
  &::after {
    content: '';
    width: calc(200% - 32px);
    height: 1px;
    background-color: #e0e0e0;
    position: absolute;
    bottom: 0;
    left: 0;
    transform: scale(0.5);
    transform-origin: 16px 0;
  }
  .c-name {
    color: #111;
    font-size: 22px;
    font-weight: 500;
    text-align: center;
  }
  .e-icon {
    text-align: center;
    padding-top: 8px;
    .icon-inner {
      display: inline-block;
      font-size: 12px;
      color: #fff;
      border-radius: 12%;
      padding: 1px 5px;
    }
  }
  .c-p-info {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 4px;
    &.block-comm {
      padding-top: 10px;
    }
    .info-price {
      color: #111;
      font-size: 18px;
      line-height: 17px;
    }
    .in-expi {
      color: #666;
      display: flex;
      font-size: 12px;
      align-items: center;
      .in-t {
        font-size: 12px;
        padding-top: 2px;
      }
    }
  }
}
