<!-- 权益列表 -->
<view class="benefit-list {{ customClass }}" wx:if="{{!loading && hasFetchedChainShopInfo && showList && showList.length > 0 }}">
  <view class="benefit-item" wx:for="{{ showList }}" wx:key="{{ item.key }}" wx:for-index="index" bindtap="goToBenefitList" data-nav="{{ item.key }}" data-id="{{ '' + item.benefitTplId + '_'+ index }}">
    <view class="benefit-icon-container">
      <text class="name-badge" wx:if="{{ item.key === 'experienceCard' && takeTipsVisible }}">待领取</text>
      <image class="benefit-icon" src="{{ item.icon }}" />
    </view>
    <view class="benefit-name">{{ item.appName }}</view>
  </view>
</view>
