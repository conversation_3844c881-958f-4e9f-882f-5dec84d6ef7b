.benefit-list {
  display: flex;
  justify-content: flex-start;
  text-align: center;
  flex-direction: row;
  flex-wrap: wrap;
  background-color: #fff;
  .benefit-item {
    position: relative;
    width: 25%;
    margin-bottom: 20px;
    text-align: center;

    .benefit-icon {
      width: 40px;
      height: 40px;
    }

    .name-badge {
      display: inline-block;
      width: 45px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      background-image: linear-gradient(90deg, #ffaf79 0%, #f44 100%);
      border-radius: 8px;
      font-size: 11px;
      color: #fff;
      position: absolute;
      top: 0;
      left: 50%;
    }

    .benefit-name {
      width: 160rpx;
      max-width: 100%;
      line-height: 17px;
      margin: 10rpx auto 0;
      font-size: 12px;
      color: #323233;
    }
  }
}

.align-center-list {
  justify-content: center;
}

.open-or-close-warpper {
  text-align: center;
  margin-bottom: 10px;

  .open-or-close {
    left: 331px;
    bottom: 31px;
    cursor: pointer;
    font-size: 12px;
    color: #b1b1b2;

    .open-or-close-icon {
      margin-left: 6px;
      transform: rotate(90deg);
      transition: all 0.3s ease-out; // sass-lint:disable-line no-transition-all
    }

    .up {
      transform: rotate(-90deg);
    }
  }
}

.hide {
  // height: 105px;
  overflow: hidden;
}

.benefit-icon-container {
  width: 40px;
  height: 40px;
  position: relative;
  display: inline-block;
}
