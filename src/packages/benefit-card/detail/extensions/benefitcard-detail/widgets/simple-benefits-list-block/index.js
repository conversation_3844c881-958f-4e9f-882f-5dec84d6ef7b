/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';
import { formatBenefits } from '../../../../../utils';
import { EXPERIENCE_CARD, moreBenefit } from '../../../../../constants';

const app = getApp();

RantaWidget({
  data: {
    benefitsList: [],
    showMore: false,
    takeTipsVisible: false,
    isOpen: false,
    customClass: '',
  },
  attached() {
    mapState(this, [
      'loading',
      'hasFetchedChainShopInfo',
      'benefitBag',
      'cardNo',
      'stockNum',
      'stockNum',
      'alias',
    ]);
    this.store.watch('loading', (val) => {
      if (!val) {
        this.initData();
      }
    });
  },
  methods: {
    initData() {
      if (this.data.subType !== EXPERIENCE_CARD && this.data.cardNo) {
        this.fetchExperienceLog();
      } else {
        this.setYZData({
          takeTipsVisible: this.getTakeTipsVisible([]),
        });
      }
      this.getShowBenefits();
    },
    fetchExperienceLog() {
      app
        .request({
          path: '/wscuser/scrm/api/benefitcard/getExperienceLog.json',
          data: {
            cardNo: this.data.cardNo,
          },
        })
        .then((res) => {
          this.setYZData({
            takeTipsVisible: this.getTakeTipsVisible(res.items),
          });
        });
    },
    getTakeTipsVisible(data) {
      const { stockNum } = this.data;
      return stockNum > 0 && data.length < stockNum;
    },
    getShowBenefits() {
      const { benefitBag, subType } = this.data;
      let benefits = formatBenefits(benefitBag, subType);
      if (benefits.length > 8) {
        benefits = benefits.slice(0, 7);
        benefits.push(moreBenefit);
      }
      this.setYZData({
        benefitsList: benefits,
        showList: benefits,
        customClass: benefits.length < 4 ? 'align-center-list' : '',
      });
    },
    goToBenefitList(e) {
      const index = e.currentTarget.dataset.id || '';
      const { cardNo, alias } = this.data;
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateTo({
        url: `/packages/benefit-card/new-benefits/index?alias=${alias}&benefit_id=${index}&card_no=${cardNo}`,
      });
    },
  },
});
