/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { RantaWidget } from 'shared/common/base/ranta/widget';
import get from '@youzan/weapp-utils/lib/get';
import { mapState, mapActions } from '@ranta/store';

const app = getApp();

RantaWidget({
  attached() {
    mapState(this, [
      'cardNo',
      'hasCard',
      'subType',
      'cardType',
      'themeColors',
      'salesmanCubeGoodsInfo',
      'alias',
      'isShareable',
      'loading',
      'hasFetchedChainShopInfo',
      'cardInfo',
      'presentDialogVisible',
      'actionSheetVisible',
      'shopList',
    ]);
    mapActions(this, [
      'buyCard',
      'handleOnCloseSelectShop',
      'searchCardSellShop',
      'closePresentDialog',
    ]);
  },
  methods: {
    // 删除卡
    removeCard() {
      wx.showModal({
        title: '删除会员卡',
        content: '删除后，你将不再享受相应的会员权益。确定要删除吗？',
        success: (res) => {
          if (res.confirm) {
            wx.showLoading({
              title: '请求中',
            });
            app
              .request({
                path: '/wscuser/scrm/api/benefitcard/remove.json',
                method: 'DELETE',
                data: {
                  cardNo: this.data.cardNo,
                },
              })
              .then(() => {
                wx.hideLoading();
                // 跳转到卡列表页
                // eslint-disable-next-line @youzan/dmc/wx-check
                wx.navigateTo({
                  url: '/packages/benefit-card/list/index',
                });
              })
              .catch((e) => {
                wx.hideLoading();
                wx.showToast({
                  title: e.msg,
                  icon: 'none',
                  duration: 1000,
                });
              });
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        },
      });
    },

    closePresentDialog() {
      this.closePresentDialog();
      app.request({
        path: '/wscuser/scrm/api/benefitcard/setReviewTag.json',
        data: {
          cardAlias: this.data.alias,
        },
      });
    },

    /**
     * 获取教育赠品
     */
    takeEduPresent(presentRecordInfo) {
      const alias = get(presentRecordInfo, 'goodsInfo.alias');
      const presentSource = get(presentRecordInfo, 'source');
      const presentId = get(presentRecordInfo, 'presentId');
      const presentRecordId = get(presentRecordInfo, 'recordId');
      const presentParams = JSON.stringify([{ presentRecordId, presentId }]);

      // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
      let url = `https://h5.youzan.com/wscvis/ump/receive-present?alias=${alias}&presentSource=${presentSource}&presentQueryParams=${presentParams}`;
      url += `&kdt_id=${app.getKdtId()}`;
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateTo({
        url: `/packages/edu/webview/index?targetUrl=${encodeURIComponent(url)}`,
      });
    },

    /**
     * 获取赠品
     */
    takeNormalPresent() {
      const presentRecordAlias = get(
        this.data.cardInfo,
        'presentRecordAlias',
        ''
      );
      const goodsAlias = get(
        this.data.cardInfo,
        'cardTemplateDTO.benefitBag.present.benefitPluginInfo.goodsAlias'
      );
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateTo({
        url: `/packages/goods/present/index?alias=${goodsAlias}&type=present&activityId=${presentRecordAlias}`,
      });
    },

    handleTakePresent() {
      const cardInfo = this.data.cardInfo || {};
      const { presentRecordInfo } = cardInfo;

      // 标记已查看
      this.closePresentDialog();

      if (cardInfo && presentRecordInfo) {
        switch (`${presentRecordInfo.goodsType}`) {
          case '31': {
            this.takeEduPresent(presentRecordInfo);
            break;
          }
          default:
            this.takeNormalPresent();
        }
      } else {
        this.takeNormalPresent();
      }
    },

    handleOnSelectShop(event) {
      const shopInfo = event.detail;
      this.buyCard(shopInfo);
    },

    handleRefreshShopList(e) {
      const value = e.detail;
      this.searchCardSellShop(value);
    },
  },
});
