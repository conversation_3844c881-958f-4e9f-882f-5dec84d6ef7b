<block>

  <block wx:if="{{ !loading && hasFetchedChainShopInfo }}">
    <!-- 删除权益卡 -->
    <view
      wx:if="{{ hasCard && subType !== 1 && cardType !== 8 }}" 
      style="color: {{ themeColors.general }}"
      class="remove-btn"
      bindtap="removeCard"
    >
      删除权益卡
    </view>

    <!-- 统一浮层放在最后 -->
    <!-- 零售导购名片左侧悬浮窗 -->
    <bi-icon />

    <!-- 分享 -->
    <salesman-cube
      wx:if="{{ isShareable }}"
      scenes="benefit_card"
      goods-info="{{ salesmanCubeGoodsInfo }}"
      page-query="{{ { alias } }}"
      need-bind-relation="{{false}}"
    />
</block>


  <!-- 页面loading -->
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading
      type="spinner"
      class="detail-loading"
      color="white"
    />
  </view>

  <van-toast id="van-toast" />

  <!-- 领取赠品弹窗 -->
  <van-dialog
    use-slot
    show="{{ presentDialogVisible }}"
    show-confirm-button="{{ false }}"
    custom-style="background-color: transparent;"
  >
    <view class="present-dialog">
      <view class="content">
        <image src="https://img.yzcdn.cn/public_files/1ad6b10c7e09d746d449445c8f11e4f5.png" class="present-pic" />
        <view class="present-descp">恭喜获得一份赠品</view>
      </view>
      <view class="dialog-button">
        <button
          class="benefit-btn"
          style="background-color: {{ themeColors.general }}"
          bindtap="handleTakePresent"
        >立即领取
        </button>
      </view>
    </view>
    <view class="operator-bar">
      <van-icon
        name="close"
        custom-style="font-size: 64rpx; color: #969799;"
        class="dialog-close"
        bindtap="closePresentDialog"
      />
    </view>
  </van-dialog>
  <van-dialog id="van-dialog" />

  <!-- 选择店铺模块 -->
  <select-shop
    value="{{ actionSheetVisible }}"
    shop-list="{{ shopList }}"
    bindselect="handleOnSelectShop"
    bindclose="handleOnCloseSelectShop"
    bindrefresh="handleRefreshShopList"
  />
</block>