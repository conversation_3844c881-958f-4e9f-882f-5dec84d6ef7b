.info-content {
  margin: 10px 0;
}

.van-cell {
  padding: 13px 15px;
}


.loading-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
}

.remove-btn {
  width: calc(100% - 48px);
  margin: 31px auto;
  height: 36px;
  line-height: 36px;
  box-sizing: border-box;
  border: none !important;
  font-size: 14px;
  text-align: center;
  background-color: #f7f7f7;
}

.detail-loading {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.present-dialog {
  text-align: center;
  background-color: transparent;

  .content {
    padding: 40px 0 56px;
    font-size: 18px;
    text-align: center;
    background-color: #fff;
    border-radius: 20px;

    .present-pic {
      height: 155px;
      width: 222px;
      display: inline-block;
    }

    .present-descp {
      height: 40px;
      line-height: 40px;
      margin-top: 10px;
      text-align: center;
      font-weight: bold;
    }
  }

  .dialog-button {
    width: 100%;
    margin: 24px auto;
  }

  .benefit-btn {
    color: #fff;
    box-sizing: border-box;
    border-color: transparent;
    text-align: center;
    border-style: solid;
    border-width: 1px;
    border-radius: 10em;
    height: 40px;
    line-height: 38px;
    font-size: 16px;
    font-weight: bold;
  }
}

.operator-bar {
  width: 100%;
  text-align: center;
}

.guide-footer__guide-notice__consumers {
  padding: 20px 15px;
  color: #333;
  font-size: 14px;
  text-align: center;
}
