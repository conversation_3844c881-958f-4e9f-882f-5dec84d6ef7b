/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import get from '@youzan/weapp-utils/lib/get';
import { RantaWidget } from 'shared/common/base/ranta/widget';
// eslint-disable-next-line @youzan-open/tee/valid-import-blacklist
import Toast from '@vant/weapp/dist/toast/toast';
import { NO_RULE, PAY } from '../../../../../constants';
import { mapState, mapActions } from '@ranta/store';

RantaWidget({
  data: {
    action: '',
    btnText: '',
  },
  attached() {
    mapState(this, [
      'isIphoneX',
      'themeColors',
      'hasMobile',
      'cardGoods',
      'price',
      'isRenewable',
      'cardSyncInfo',
      'syncWeixin',
      'activationCondition',
      'activated',
      'cardType',
      'hasCard',
      'isSoldOut',
      'isExpired',
      'isDisplay',
      'isEnabled',
      'cardInfo',
      'customShowProtocol',
      'agreed',
      'loading',
      'hasFetchedChainShopInfo',
      'isCurrentShopAvailable',
      'btnVisible',
      'isNeedWillRenew',
    ]);
    mapActions(this, ['onBtnAction', 'handleSetBtnVisible']);
    this.store.watch('loading', (val) => {
      if (!val) {
        this.initData();
      }
    });
  },
  methods: {
    initData() {
      this.formatBaAndTx();
    },
    formatBaAndTx() {
      const defaultData = {
        action: '',
        btnText: '',
        btnVisible: false,
      };
      const { hasCard } = this.data;
      const data = hasCard
        ? this.getBtnInfoHasCard(defaultData)
        : this.getBtnInfoWithoutCard(defaultData);
      this.handleSetBtnVisible(data.btnVisible);
      this.setYZData({
        ...data,
      });
    },

    // 有卡时获取按钮信息
    getBtnInfoHasCard(defaultData) {
      const isNeedWillRenew = get(
        this.data.cardInfo,
        'cardTemplateDTO.isNeedWillRenew',
        false
      );
      const activateVisible = this.activateVisible();
      const { hasCard, isRenewable, cardType } = this.data;
      let data;
      let cond = false;
      // 需要激活不展示续费

      if (activateVisible) {
        // 未激活不展示
        cond = false;
      } else if (isRenewable && hasCard && cardType === 8 && isNeedWillRenew) {
        // 展示续费
        cond = true;
      }
      const result = [
        {
          // 不是要激活状态，付费卡，已领卡，可续费
          cond,
          action: 'renewal',
          btnText: '立即续费',
          btnVisible: this.data.isCurrentShopAvailable,
        },
      ];

      result.some((item) => {
        if (item.cond) {
          data = item;
          return true;
        }
        return false;
      });

      return data || defaultData;
    },
    // 无卡时获取按钮信息
    getBtnInfoWithoutCard(defaultData) {
      const { cardType, isEnabled, isSoldOut, isDisplay, isExpired, price } =
        this.data;
      switch (cardType) {
        case NO_RULE:
          return {
            action: 'takeCard',
            btnText: price ? '立即领卡' : '免费领取',
            btnVisible: true && isEnabled && !isExpired,
          };
        case PAY:
          return {
            action: 'buyCard',
            btnText: '立即开通',
            btnVisible: isEnabled && !isSoldOut && isDisplay && !isExpired,
          };
        default:
          return defaultData;
      }
    },
    // 是否显示激活按钮
    activateVisible() {
      const { activationCondition = {}, activated } = this.data;
      const { requireMobile, requireProfile } = activationCondition;
      return (requireMobile || requireProfile) && !activated;
    },
    // 是否显示同步微信卡包
    syncWeixinVisible() {
      const { syncWeixin, cardSyncInfo } = this.data;
      const isSyncWeixin = get(
        cardSyncInfo,
        'cardWeixinSyncInfo.isSyncWeixin',
        false
      );
      const syncWeixinState = get(
        cardSyncInfo,
        'cardWeixinSyncInfo.syncWeixinState',
        -1
      );
      // syncWeixinState 是卡模版同步到微信的状态，3 表示同步成功，否则不展示同步卡包的按钮
      return isSyncWeixin && syncWeixinState === 3 && !syncWeixin;
    },
    async checkViewAggreementStatus() {
      if (!this.data.customShowProtocol) {
        return true;
      }

      const res = await this.ctx.cloud.invoke(
        'checkViewAggreementStatusSyncEvent'
      );
      if (res?.length && res[0]) {
        return res;
      }

      if (!this.data.agreed) {
        Toast('请先阅读并勾选协议');
        return false;
      }
      return true;
    },
    // 按钮点击事件
    async bindClick() {
      if (!(await this.checkViewAggreementStatus())) return;
      const { action } = this.data;
      if (action === '') return;
      this.ctx.cloud.invoke('beforeSubmitAsyncEvent').then(() => {
        this.onBtnAction({ action, bindPhone: true });
      });
    },
    handleCallAuth() {
      const { action } = this.data;
      this.ctx.cloud.invoke('beforeSubmitAsyncEvent').then(() => {
        this.onBtnAction({ action, bindPhone: true });
      });
    },
  },
});
