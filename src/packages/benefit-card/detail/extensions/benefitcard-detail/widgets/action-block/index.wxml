<block wx:if="{{btnVisible && !loading && hasFetchedChainShopInfo && isEnabled && (!isExpired || (isNeedWillRenew && isRenewable)) }}">
  <view class="fill-warpper"></view>
  <view class="act-bt-warpper {{ isIphoneX ? 'is-n-ph' : '' }} ">
    <view wx:if="{{ isCurrentShopAvailable || hasCard }}">
      <block>
        <user-authorize
          wx:if="{{ agreed && activationCondition.requireMobile }}"
          authTypeList="{{ ['mobile']}}"
          bind:next="handleCallAuth"
        >
          <button class="act-bt" style="background-color: {{ themeColors.general }}">
            {{ btnText }}
          </button>
        </user-authorize>
        <button wx:else style="background-color: {{ themeColors.general }}" class="act-bt" bindtap="bindClick">
          {{ btnText }}
        </button>
      </block>
    </view>
    <view wx:else>
      <button class="act-bt disabled-btn">
        该卡不适用于本店，不支持领取/购买
      </button>
    </view>
  </view>
</block>
