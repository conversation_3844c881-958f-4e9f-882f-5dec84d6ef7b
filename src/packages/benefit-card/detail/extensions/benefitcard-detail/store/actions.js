/* eslint @youzan-open/tee/valid-same-extension-import: "off", @youzan-open/tee/no-platform-field: "off" */
import get from '@youzan/weapp-utils/lib/get';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import money from '@youzan/weapp-utils/lib/money';
import args from '@youzan/weapp-utils/lib/args';
import { checkPartnerStore } from '@youzan/utils-shop';
import {
  getGuideUrl,
  saveGuideSearch,
  GUIDE_DIALOG_TYPE,
  GUIDE_SUCCESS_SIGN,
  guideJumpDialog,
} from 'utils/guide-jump';
import {
  PAY,
  RULE,
  NO_RULE,
  FORM_CARD,
  EXPERIENCE_CARD,
  GUIDE_OPERATE_TYPE,
} from '../../../../constants';
import {
  getCardExpire,
  getIsRenewable,
  getColorCode,
  getIsExpires,
  getCardStyle,
  checkIsSoldOut,
  formatCardGoods,
  compCardExpire,
  checkIsNeedTranslateGoodsId,
} from '../util';

const app = getApp();

const actions = {
  fetchCardTplInfo({ state, commit, dispatch }, guideType) {
    return app
      .request({
        path: '/wscuser/scrm/api/benefitcard/getCardTemplate.json',
        data: {
          cardAlias: state.alias,
          goodsId: state.goodsId,
          tradeNo: state.orderNo,
          isNewVersionApp: 1,
          // 是否支持支持付费赠品 v2.55 添加
          // 老版本赠品跳转直接到 /packages/goods/present/index 重定向过多
          // supportKnowledgePresent
          skp: 1,
        },
      })
      .then((cardInfo) => {
        wx.stopPullDownRefresh();
        if (!cardInfo.cardTemplateDTO) {
          commit('SET_LOADING', { laoding: false });
          return;
        }
        const cardName = get(cardInfo, 'cardTemplateDTO.name', '');
        const {
          subType = FORM_CARD,
          activated = false,
          syncWeixin = false,
          hasCard = false,
        } = cardInfo;
        const cardTplInfo = get(cardInfo, 'cardTemplateDTO', {});
        const cardType = cardTplInfo.cardAcquireSetting;
        if (
          !hasCard &&
          state.from !== 'member-card-groupon' &&
          cardType === PAY
        ) {
          dispatch('checkGrouponJump', { cardData: cardInfo });
        }

        const isNeedWillRenew = get(
          cardInfo,
          'cardTemplateDTO.isNeedWillRenew',
          false
        );
        // 赠品弹窗
        dispatch('checkShowPresentDialog', { cardInfo, guideType });
        const benefitBag = cardTplInfo.benefitBag || {};
        const stockNum = get(benefitBag, 'experienceCard.stockNum', 0);
        const cardSyncInfo = get(cardTplInfo, 'cardSyncInfo', {});
        const isShareable = get(cardTplInfo, 'isShareable', false);
        const price = money(
          get(cardTplInfo, 'cardGoods.skuList[0].price', 0)
        ).toYuan();
        const isDisplay = get(cardTplInfo, 'cardGoods.isDisplay', false);
        const isRenewable = getIsRenewable(hasCard, cardTplInfo);
        const activationCondition = get(cardTplInfo, 'activationCondition', {});
        const colorCode = getColorCode({
          colorCode: cardTplInfo.colorCode || 'Color200',
        });
        const coverUrl = (cdnImage(cardTplInfo.coverUrl) || '').replace(
          /yzcdn\.cn\/\//g,
          'yzcdn.cn/'
        );

        const isExpired = getIsExpires(hasCard, cardInfo);
        const alias = get(cardTplInfo, 'cardTplAlias', '');
        if (isShareable && subType !== EXPERIENCE_CARD) {
          wx.showShareMenu({
            withShareTicket: true,
          });
        } else {
          wx.hideShareMenu();
        }
        const cardGoods = formatCardGoods(cardTplInfo.cardGoods);
        const cardGoodsSource = formatCardGoods(cardTplInfo.cardGoodsSource);
        const availableKdtIdList = get(cardTplInfo, 'availableKdtIdList', []);
        const isCurrentShopAvailable = get(
          cardTplInfo,
          'isCurrentShopAvailable',
          false
        );
        let availableTips = '';
        if (!isCurrentShopAvailable && availableKdtIdList.length > 0) {
          availableTips = `本店不适用，其他${availableKdtIdList.length}家店铺可用`;
        }
        const curCardExpire = getCardExpire(cardInfo, hasCard);
        const computedPrice = price === '0.00' ? '' : `￥${price}`;
        const data = {
          cardName,
          isShareable,
          hasCard,
          cardType,
          cardTplInfo,
          cardInfo,
          isNeedWillRenew,
          benefitBag,
          servicePhone: cardTplInfo.servicePhone,
          cardGoods,
          salesmanCubeGoodsInfo: {
            id: cardGoodsSource.goodsId,
            goodsPrice: { maxPrice: cardGoodsSource.maxPrice },
          },
          cardNo: cardInfo.cardNo || '',
          subType,
          isRenewable,
          stockNum,
          price: computedPrice,
          activationCondition,
          activated,
          syncWeixin,
          cardSyncInfo,
          cardStyle: getCardStyle(colorCode, coverUrl),
          coverUrl,
          isSoldOut: checkIsSoldOut(cardTplInfo),
          isEnabled: cardTplInfo.enabled,
          isDisplay,
          isExpired,
          alias,
          spmId: get(
            cardTplInfo,
            'cardGoods.goodsId',
            get(cardTplInfo, 'cardTplId')
          ),
          needMobile: activationCondition.requireMobile,
          availableTips,
          isCurrentShopAvailable,
          cardExpire: curCardExpire,
          topCardExpire: compCardExpire(curCardExpire, computedPrice),
        };
        commit('SET_INIT_PAGE_INFO', data);
        if (cardType === PAY) {
          // 获取店铺列表
          dispatch('searchCardSellShop');
        }
        return cardInfo;
      })
      .then((cardInfo) => {
        dispatch('handleQrcodeLogger', cardInfo);
      })
      .catch((e) => {
        wx.stopPullDownRefresh();
        wx.showToast({
          title: e.msg,
          icon: 'none',
          duration: 1000,
        });
        commit('SET_BENEFIT_LIST', { benefitList: [] });
        commit('SET_HAS_CARD', { hasCard: false });
      })
      .finally(() => {
        commit('SET_LOADING', { loading: false });
      });
  },
  handleQrcodeLogger({ state }, cardInfo) {
    const cardTplInfo = get(cardInfo, 'cardTemplateDTO', {});
    const qrcodeId = state.query.qrcodeId || state.query.qrcode_id || '';
    if (qrcodeId && cardTplInfo.cardTplId) {
      app.logger &&
        app.logger.log({
          et: 'custom',
          ei: 'qrcode_bind_benefit_visit_weapp',
          en: '权益卡跟踪参数二维码访问',
          pt: 'quanyika',
          params: {
            card_name: cardTplInfo.name,
            card_tpl_id: cardTplInfo.cardTplId,
            qrcode_id: qrcodeId,
          },
        });
    }
  },
  // 获取店铺列表
  searchCardSellShop({ state, commit }, keyword = '') {
    app
      .request({
        path: '/wscuser/scrm/api/benefitcard/searchCardSellShop.json',
        data: {
          alias: state.alias,
          keyword,
          page: 1,
          pageSize: 50,
          isExcludeCurrentShop: true,
        },
      })
      .then((res) => {
        const { items = [] } = res || {};
        if (!keyword) {
          const shopInfo = app.getShopInfoSync();
          const isPartnerStore = checkPartnerStore(
            get(shopInfo, 'shopMetaInfo')
          );
          // eslint-disable-next-line camelcase
          const { chainStoreInfo, shop_name, kdt_id } = shopInfo;
          if (
            chainStoreInfo &&
            !(chainStoreInfo.isRootShop || isPartnerStore)
          ) {
            items.unshift({
              shopName: shop_name,
              kdtId: kdt_id,
            });
          }
        }
        commit('SET_SHOP_LIST', { shopList: items });
      });
  },
  initShopInfo({ commit }) {
    return app
      .getShopInfo()
      .then((shop) => {
        const { shopMetaInfo = {} } = shop;
        const isChainStore = get(shop, 'chainStoreInfo.isChainStore', false);
        const shopInfo = {
          shopName: shop.shop_name,
          isRootShop: get(shop, 'chainStoreInfo.isRootShop', false),
          isChainStore,
          isChainHqStore:
            isChainStore && get(shop, 'chainStoreInfo.isRootShop', false),
          isUnitedHqStore: get(shop, 'chainStoreInfo.isUnitedHqStore'),
          isPartnerStore: checkPartnerStore(shopMetaInfo),
        };
        commit('SET_SHOP_INFO', shopInfo);
      })
      .finally(() => {
        commit('SET_HAS_FETCHED_CHAIN_SHOP_INFO', {
          hasFetchedChainShopInfo: true,
        });
      });
  },
  // 检查是否需要跳转到拼团
  checkGrouponJump({ state, commit }, { cardData }) {
    if (!cardData.cardTemplateDTO) {
      commit('SET_LOADING', { loading: false });
      return;
    }
    const goodsId = get(cardData, 'cardTemplateDTO.cardGoods.goodsId', 0);
    app
      .request({
        path: '/wscuser/membercard/groupon/getGroupOnForMemberCard.json',
        data: {
          goodsId,
        },
        success: (resp) => {
          const { status, activityAlias, activityType = 4 } = resp;
          if (status === 2 && !cardData.hasCard) {
            const activityTypeQuery = `&activity_type=${activityType}`;
            // eslint-disable-next-line @youzan/dmc/wx-check
            wx.redirectTo({
              url: `/packages/ump/membercard-groupon/index?cardAlias=${state.alias}&ump_alias=${activityAlias}&ump_type=groupOn${activityTypeQuery}`,
            });
          } else {
            commit('SET_LOADING', { loading: false });
          }
        },
      })
      .catch(() => {
        commit('SET_LOADING', { loading: false });
        wx.hideLoading();
      });
  },
  // 赠品弹窗
  checkShowPresentDialog({ state, dispatch }, { cardInfo, guideType }) {
    // 2.55 之前的小程序直接判断的 cardInfo.isFirstReview 展示的
    const isFirstReview = get(
      cardInfo,
      'presentRecordInfo.isFirstReview',
      false
    );
    if (guideType === GUIDE_OPERATE_TYPE) {
      // 引导权益卡付费页面支付成功后会带标识guideDialogType=success
      guideJumpDialog(GUIDE_DIALOG_TYPE.equityCard, '', state.query, () => {
        if (isFirstReview) {
          dispatch('openPresentDialog');
        }
      });
    } else if (isFirstReview) {
      dispatch('openPresentDialog');
    }
  },
  openPresentDialog({ commit }) {
    commit('SET_PRESENT_DIALOG_VISIBLE', { presentDialogVisible: true });
  },
  closePresentDialog({ commit }) {
    commit('SET_PRESENT_DIALOG_VISIBLE', { presentDialogVisible: false });
  },
  /**
   *
   * @param {*} action 续期或者是购买
   */
  handleOpenSelectShop({ state, commit, dispatch }, action) {
    if (state.isUnitedHqStore) {
      dispatch('buyCard');
      return;
    }

    if (action === 'renewal') {
      commit('SET_ISRENEW', { isRenew: true });
      if (state.isChainHqStore || state.isPartnerStore) {
        commit('SET_ACTION_SHEET_VISIBLE', { actionSheetVisible: true });
        return;
      }
    }
    if (action === 'buyCard') {
      if (state.isChainHqStore || state.isPartnerStore) {
        commit('SET_ACTION_SHEET_VISIBLE', { actionSheetVisible: true });
        return;
      }
    }
    dispatch('buyCard');
  },
  // 按钮事件
  onBtnAction({ commit, dispatch }, { action, bindPhone }) {
    if (bindPhone) {
      commit('SET_HAS_MOBILE', { hasMobile: bindPhone });
    }
    if (action === 'buyCard' || action === 'renewal') {
      dispatch('handleOpenSelectShop', action);
      return;
    }
    dispatch(action);
  },
  // 激活,如果不需要填写用户信息 则自动激活
  activate({ state, dispatch }) {
    if (state.hasMobile) {
      dispatch('activateReal');
    }
  },
  // 获取同步微信卡包的参数
  fetchWxTicket({ state }, weixinCardId) {
    return app.request({
      path: '/wscuser/wx/getSyncTicket.json',
      data: {
        templateId: weixinCardId,
        cardNo: state.cardNo,
      },
    });
  },
  // 同步微信卡包
  addWxCard({ state, dispatch }) {
    const { weixinCardId } = get(state.cardSyncInfo, 'cardWeixinSyncInfo', {});
    dispatch('fetchWxTicket', weixinCardId).then((data) => {
      const cardExt = JSON.stringify({
        code: data.card_no,
        openid: data.open_id,
        timestamp: data.timestamp,
        signature: data.signature,
        nonce_str: data.nonce_str,
      });
      const cardList = [
        {
          cardId: weixinCardId,
          cardExt,
        },
      ];
      wx.addCard({
        cardList,
        success() {
          wx.showToast({
            title: '添加成功',
            icon: 'none',
          });
          dispatch('fetchCardTplInfo');
        },
        fail(err) {
          if (err.errMsg !== 'addCard:fail cancel') {
            wx.showToast({
              title: err.errMsg,
              icon: 'none',
            });
          }
        },
      });
    });
  },
  // 激活
  activateReal({ state, dispatch }) {
    const requireProfile = get(
      state.cardTplInfo,
      'activationCondition.requireProfile',
      true
    );
    if (requireProfile) {
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateTo({
        url: getGuideUrl(
          `/packages/benefit-card/active/index?card_no=${state.cardNo}&alias=${state.alias}&sub_type=${state.subType}`,
          state.query
        ),
      });
    } else {
      wx.showLoading({
        title: '请求中',
      });
      dispatch('takeAndActivate');
    }
  },

  // 领卡和激活
  takeAndActivate({ state, dispatch }) {
    const { cardType } = state;
    const data = {
      cardAlias: state.alias,
    };
    let method = 'GET';
    let path = '/wscuser/scrm/api/benefitcard/take.json';
    if (cardType === RULE || cardType === PAY) {
      data.cardNo = state.cardNo;
      method = 'PUT';
      path = '/wscuser/scrm/api/benefitcard/activate.json';
    }
    app
      .request({
        path,
        data,
        method,
      })
      .then(() => {
        if (cardType === NO_RULE) {
          // eslint-disable-next-line @youzan/dmc/wx-check
          wx.navigateTo({
            url: getGuideUrl(
              args.add('/packages/benefit-card/result/index', {
                card_no: state.cardNo,
                alias: state.alias,
                from: 'take',
                card: JSON.stringify({}),
              }),
              state.query
            ),
          });
        } else {
          wx.showToast({
            title: '激活成功',
            icon: 'none',
            duration: 1000,
          });
          dispatch('fetchCardTplInfo');
        }
      })
      .catch((e) => {
        wx.hideLoading();
        wx.showToast({
          title: e.msg,
          icon: 'none',
          duration: 1000,
        });
      });
  },

  getCacheJson({ state }, { goodsData, kdtId }) {
    const wxpayEnv = 1; // 老版本会员卡的 _global.wxpay_env, 和后台确认了一直为true
    const postData = {
      postage: 0,
      use_wxpay: wxpayEnv,
      accept_price: 1,
      order_from: 'membercard',
      kdt_id: kdtId,
      store_id: app.getOfflineId() || 0,
      is_new_version: true,
    };

    app
      .request({
        path: '/wsctrade/order/goodsBook.json',
        data: {
          goodsList: JSON.stringify([goodsData]),
          common: JSON.stringify(postData),
        },
        method: 'POST',
      })
      .then((res) => {
        const { bookKey } = res;
        wx.hideLoading();
        saveGuideSearch(state.query);
        // eslint-disable-next-line @youzan/dmc/wx-check
        wx.navigateTo({
          url: `/packages/order/index?orderFrom=membercard&bookKey=${bookKey}`,
        });
      })
      .catch((e) => {
        wx.hideLoading();
        wx.showToast({
          title: e.msg,
          icon: 'none',
          duration: 1000,
        });
      });
  },
  // 领无门槛卡
  takeCard({ state, dispatch }) {
    if (state.cardType === NO_RULE) {
      const requireProfile = get(
        state.activationCondition,
        'requireProfile',
        true
      );
      const requireMobile = get(
        state.activationCondition,
        'requireMobile',
        true
      );
      if (!requireProfile && !requireMobile) {
        app
          .request({
            path: '/wscuser/scrm/api/benefitcard/take.json',
            data: {
              cardAlias: state.alias,
            },
          })
          .then(() => {
            dispatch('initData');
            guideJumpDialog(
              GUIDE_DIALOG_TYPE.equityCard,
              GUIDE_SUCCESS_SIGN,
              state.query,
              () => {
                wx.showToast({
                  title: '领卡成功',
                  icon: 'none',
                  duration: 1000,
                });
              }
            );
          })
          .catch((e) => {
            wx.showToast({
              title: e.msg,
              icon: 'none',
              duration: 1000,
            });
          });
      } else {
        dispatch('activate');
      }
    }
  },

  // 购买卡
  buyCard({ state, commit, dispatch }, originShopInfo) {
    if (state.cardType !== 8) return;
    const sku = get(state.cardGoods, 'skuList', {});
    const goodsId = get(state, 'cardGoods.goodsId', 0);
    if (!sku || !sku.length) {
      return wx.showToast({
        title: '该权益卡暂不支持购买',
        icon: 'none',
        duration: 1000,
      });
    }

    commit('SET_ACTION_SHEET_VISIBLE', { actionSheetVisible: false });

    wx.showLoading({
      title: '正在跳转',
      mask: true,
    });

    const sourceKdtId = state.kdtId;
    let kdtId = sourceKdtId;
    if (originShopInfo && originShopInfo.kdtId) {
      kdtId = originShopInfo.kdtId;
    }
    // 如果是总部店铺需要转化下goodsId为分店
    if (checkIsNeedTranslateGoodsId(state)) {
      app
        .request({
          path: '/wscuser/scrm/api/benefitcard/getOriginGoodsId.json',
          data: {
            kdtId,
            goodsId,
            sourceKdtId,
          },
        })
        .then((res) => {
          const goodsData = {
            goods_id: res.originGoodsId,
            num: 1,
            sku_id: get(res, 'originSkuId[0]'),
            price: sku[0].price,
            ...state.maiDianData,
          };
          dispatch('getCacheJson', { goodsData, kdtId });
        })
        .catch((e) => {
          wx.hideLoading();
          wx.showToast({
            title: e.msg,
            icon: 'none',
            duration: 1000,
          });
        });
      return;
    }
    const goodsData = {
      goods_id: goodsId,
      num: 1,
      sku_id: sku[0].goodsSkuId,
      price: sku[0].price,
      ...state.maiDianData,
    };
    dispatch('getCacheJson', { goodsData, kdtId });
  },

  initData({ commit, dispatch }, guideType) {
    commit('SET_LOADING', { loading: true });
    dispatch('fetchCardTplInfo', guideType);
  },

  handleOnCloseSelectShop({ commit }) {
    commit('SET_ACTION_SHEET_VISIBLE', { actionSheetVisible: false });
  },

  // 卡面点击续费
  onRenew({ dispatch }) {
    dispatch('handleOpenSelectShop', 'renewal');
  },

  handleSetBtnVisible({ commit }, btnVisible) {
    commit('SET_BTN_VISIBLE', { btnVisible });
  },
};

export default actions;
