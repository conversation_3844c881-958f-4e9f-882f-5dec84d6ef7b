/* eslint-disable @youzan-open/tee/valid-same-extension-import */
const mutations = {
  SET_KDT_ID(state, payload) {
    state.kdtId = payload.kdtId;
  },

  SET_QUERY_INFO(state, payload) {
    state.alias = payload.alias;
    state.orderNo = payload.orderNo;
    state.from = payload.from;
    state.goodsId = payload.goodsId;
  },

  SET_USER_INFO(state, payload) {
    state.userInfo = payload.userInfo;
  },

  SET_SHOP_INFO(state, payload) {
    state.shopName = payload.shopName;
    state.isRootShop = payload.isRootShop;
    state.isChainStore = payload.isChainStore;
    state.isChainHqStore = payload.isChainHqStore;
    state.isUnitedHqStore = payload.isUnitedHqStore;
    state.isPartnerStore = payload.isPartnerStore;
  },

  SET_ISRENEW(state, payload) {
    state.isRenew = payload.isRenew;
  },

  SET_HAS_FETCHED_CHAIN_SHOP_INFO(state, payload) {
    state.hasFetchedChainShopInfo = payload.hasFetchedChainShopInfo;
  },

  SET_CARD_WRAPPER_STYLE(state, payload) {
    state.cardWrapperStyle = payload.cardWrapperStyle;
  },

  SET_BENEFIT_LIST(state, payload) {
    state.benefitList = payload.benefitList;
  },

  SET_HAS_CARD(state, payload) {
    state.hasCard = payload.hasCard;
  },

  SET_THEME_COLORS(state, payload) {
    state.themeColors = payload.themeColors;
  },

  SET_LOADING(state, payload) {
    state.loading = payload.loading;
  },

  SET_ACTION_SHEET_VISIBLE(state, payload) {
    state.actionSheetVisible = payload.actionSheetVisible;
  },

  SET_PRESENT_DIALOG_VISIBLE(state, payload) {
    state.presentDialogVisible = payload.presentDialogVisible;
  },

  SET_QUERY(state, payload) {
    state.query = payload.query;
  },

  SET_INIT_PAGE_INFO(state, payload) {
    state.cardName = payload.cardName;
    state.isShareable = payload.isShareable;
    state.hasCard = payload.hasCard;
    state.cardType = payload.cardType;
    state.cardTplInfo = payload.cardTplInfo;
    state.cardInfo = payload.cardInfo;
    state.isNeedWillRenew = payload.isNeedWillRenew;
    state.benefitBag = payload.benefitBag;
    state.servicePhone = payload.servicePhone;
    state.cardGoods = payload.cardGoods;
    state.salesmanCubeGoodsInfo = payload.salesmanCubeGoodsInfo;
    state.cardNo = payload.cardNo;
    state.subType = payload.subType;
    state.isRenewable = payload.isRenewable;
    state.stockNum = payload.stockNum;
    state.price = payload.price;
    state.activationCondition = payload.activationCondition;
    state.activated = payload.activated;
    state.syncWeixin = payload.syncWeixin;
    state.cardSyncInfo = payload.cardSyncInfo;
    state.cardStyle = payload.cardStyle;
    state.coverUrl = payload.coverUrl;
    state.isSoldOut = payload.isSoldOut;
    state.isEnabled = payload.isEnabled;
    state.isDisplay = payload.isDisplay;
    state.isExpired = payload.isExpired;
    state.loading = payload.loading;
    state.alias = payload.alias;
    state.spmId = payload.spmId;
    state.needMobile = payload.needMobile;
    state.availableTips = payload.availableTips;
    state.isCurrentShopAvailable = payload.isCurrentShopAvailable;
    state.cardExpire = payload.cardExpire;
    state.topCardExpire = payload.topCardExpire;
  },

  SET_SHOP_LIST(state, payload) {
    state.shopList = payload.shopList;
  },

  SET_HAS_MOBILE(state, payload) {
    state.hasMobile = payload.hasMobile;
  },

  HIDE_CARD_EXPIRY_DATE(state) {
    state.customShowExpireDate = false;
  },

  HIDE_PROTOCOL_BLOCK(state) {
    state.customShowProtocol = false;
  },

  SET_AREED(state, payload) {
    state.agreed = payload.agreed;
  },

  SET_BTN_VISIBLE(state, payload) {
    state.btnVisible = payload.btnVisible;
  },

  SET_MAI_DIAN_DATA(state, payload) {
    state.maiDianData = payload.maiDianData;
  },
};

export default mutations;
