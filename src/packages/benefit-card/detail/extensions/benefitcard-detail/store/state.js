/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { getSystemInfoSync } from '@youzan/tee-api';

import { SystemInfo } from 'shared/utils/browser/system-info';

import { NO_RULE, FORM_CARD, EXPERIENCE_CARD } from '../../../../constants';
import { checkIsIphoneInWeapp } from '../../../../utils';

const app = getApp();

function checkIsIphone() {
  const { model } = getSystemInfoSync();
  return checkIsIphoneInWeapp(model);
}

const state = {
  query: {},
  isIphoneX: checkIsIphone(),
  exType: EXPERIENCE_CARD,
  navigationbarConfigData: {
    origin_immersion: true,
    navigationbar_type: 'immersion',
  },
  kdtId: '',
  hasMobile: !!app.getBuyerId(),
  alias: '',
  orderNo: '',
  from: '',
  goodsId: 0,
  shopName: '',
  benefitBag: {},
  hasCard: false,
  cardType: NO_RULE,
  loading: true,
  servicePhone: '',
  cardGoods: {},
  salesmanCubeGoodsInfo: {},
  cardNo: '',
  cardInfo: {},
  isNeedWillRenew: false,
  cardTplInfo: {},
  isRenewable: false,
  price: 0,
  subType: FORM_CARD,
  activationCondition: {},
  syncWeixin: false,
  cardSyncInfo: {},
  isShareable: false,
  expireTip: '',
  cardStyle: {
    bgColor: '',
    imageUrl: '',
  },
  coverUrl: '',
  showMore: false,
  isExpired: false, // 是否过期，true：过期，false： 未过期
  isSoldOut: false,
  sourceGoodsId: 0,
  isDisplay: false,
  isEnabled: false,
  maiDianData: {}, // 埋点需要的数据
  presentDialogVisible: false, // 赠品弹窗
  shopList: [],
  actionSheetVisible: false,
  isRootShop: true,
  isChainStore: false,
  isChainHqStore: false, // 是否是连锁总部
  isPartnerStore: false,
  tradeNo: '',
  hasFetchedChainShopInfo: false,
  isSupportRecommendGoods: false,
  spmId: '',
  needMobile: false,
  activated: false,
  cardWrapperStyle: {
    width: '686rpx',
    height: '360rpx',
  },
  isCurrentShopAvailable: false,
  availableTips: '',
  // 定制配置
  design: [],
  cardName: '',
  cardExpire: null,
  topCardExpire: '',
  themeColors: {},
  statusH: SystemInfo.statusBarHeight,
  isUnitedHqStore: false,
  agreed: false,
  btnVisible: false,
  // 卡面有效期是否展示
  customShowExpireDate: true,
  // 底部协议是否展示
  customShowProtocol: true,
  isRenew: false,
};

export default state;
