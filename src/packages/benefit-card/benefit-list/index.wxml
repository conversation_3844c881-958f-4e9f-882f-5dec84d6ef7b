<block wx:if="{{ !loading }}">
  <view class="benefits-list">
    <benefits-list
      wx:if="{{ !empty }}"
      benefitList="{{ benefitList }}"
      card-no="{{ cardNo }}"
      has-card="{{ hasCard }}"
      experience-log="{{ experienceLog }}"
      need-activate="{{ needActivate }}"
      has-activated="{{ hasActivated }}"
    />
    <view class="empty" wx:if="{{ empty }}">暂无权益</view>
    <view class="benefit-btn-wrapper" wx:if="{{ !hasCard || (needActivate && !hasActivated) }}">
      <view class="theme-view-wrapper">
        <theme-view bg="main-bg" border="general" color="main-text">
          <button
            class="benefit-btn theme-view-button"
            bindtap="takeCard"
            block
            round
            wx:if="{{ cardType === 3 || cardType === 8 }}"
            >{{ btnText }}
          </button>
        </theme-view>
      </view>
    </view>
  </view>
</block>
<van-toast id="van-toast" />
