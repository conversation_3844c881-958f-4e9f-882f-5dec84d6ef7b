import WscPage from 'pages/common/wsc-page/index';
import get from '@youzan/weapp-utils/lib/get';
import { CARD_TYPE_MAP, FORM_CARD, shareImageUrl } from '../constants';
import { formatBenefits } from '../utils';

const app = getApp();
const shopInfo = app.getShopInfoSync();
WscPage({
  data: {
    benefitList: [], // 权益列表
    hasCard: false, // 当前用户是否已领取卡
    cardType: CARD_TYPE_MAP.NO_RULE, // 权益卡类型
    experienceLog: [], // 体验卡领取记录
    cardInfo: {}, // 卡信息
    rewardTips: '', // 权益卡领取提示文案
    tokenNum: 0, // 已被领取的次数
    cardNo: 0, // 卡 Id
    shopName: get(shopInfo, 'base.shop_name', ''), // 店铺名称，
    openType: '', // 当库存不足或者未激活的时候，openType 为空
    empty: false,
    btnText: '',
    loading: false,
  },

  onLoad(query = {}) {
    // TODO: YXR 要不要多给一个提示，当参数错误的时候
    this.alias = query.alias;
  },

  onShow() {
    this.initData();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.initData();
  },

  // 初始加载数据
  initData() {
    const hasMobile = !!app.getBuyerId();
    this.setYZData({
      hasMobile,
    });
    wx.showLoading({ title: '加载中' });
    this.setYZData({
      loading: true,
    });
    this.fetchCardTplInfo(this.alias);
  },

  // 获取卡相关信息
  fetchCardTplInfo(cardAlias) {
    app.request({
      path: '/wscuser/scrm/api/benefitcard/getCardTemplate.json',
      data: {
        cardAlias
      }
    }).then((cardInfo = {}) => {
      wx.stopPullDownRefresh();
      wx.hideLoading();
      this.formatCardInfo(cardInfo);
    }).catch(err => {
      wx.stopPullDownRefresh();
      wx.hideLoading();
      wx.showToast({
        title: err.msg,
        icon: 'none',
        duration: 1000,
      });

      // 数据加载失败
      this.setYZData({
        benefitList: [],
        hasCard: false,
        empty: true,
      });
    });
  },

  formatCardInfo(cardInfo) {
    const {
      hasCard = false,
      cardTemplateDTO: cardTplInfo,
      cardNo = 0,
      subType = FORM_CARD,
      activated: hasActivated,
    } = cardInfo;
    // cardInfo 上的数据主要是与用户相关的数据
    // cardInfo.cardTemplateDTO 是卡相关的数据
    const benefitBag = get(cardTplInfo, 'benefitBag', {});
    const stockNum = get(benefitBag, 'experienceCard.stockNum', 0);
    const requireProfile = get(cardTplInfo, 'activationCondition.requireProfile', true);
    const requireMobile = get(cardTplInfo, 'activationCondition.requireMobile', true);
    const cardType = get(cardTplInfo, 'cardAcquireSetting', CARD_TYPE_MAP.NO_RULE);
    const needActivated = requireProfile && requireMobile;
    const benefitList = formatBenefits(benefitBag, subType);

    // 如果当前卡未激活，不允许分享
    if (needActivated && !hasActivated) {
      wx.hideShareMenu();
    }
    if (stockNum && stockNum > 0 && cardNo) {
      this.getExperienceLog(cardNo, stockNum);
    } else {
      this.setYZData({
        experienceLog: [],
        tokenNum: 0,
      });
    }
    let btnText = '';
    if (!hasCard) {
      btnText = cardType === CARD_TYPE_MAP.NO_RULE ? '立即领取' : '立即开通';
    } else {
      btnText = '立即激活';
    }
    this.setYZData({
      hasCard,
      cardInfo,
      stockNum,
      cardNo,
      hasActivated,
      needActivated,
      openType: (needActivated && !hasActivated) ? '' : 'share',
      benefitList,
      empty: benefitList.length === 0,
      cardType,
      btnText,
      loading: false,
    });
  },

  // 设置体验卡领取信息
  getExperienceLog(cardNo, stockNum) {
    return app.request({
      path: '/wscuser/scrm/api/benefitcard/getExperienceLog.json?from=weapp',
      data: {
        cardNo
      }
    }).then(res => {
      this.setYZData({
        experienceLog: this.formatExperienceLog(res.items, stockNum),
        tokenNum: get(res, 'items.length', 0),
      });
    });
  },

  formatExperienceLog(experienceLog, stockNum) {
    // 确保领卡记录与 stockNum 一致
    const tmp = new Array(stockNum);
    tmp.splice(0, experienceLog.length, ...experienceLog);
    return tmp;
  },

  // 分享
  onShareAppMessage() {
    // 分享描述
    const desc = this.getCardDesc();
    const title = `送你一张${get(this, 'data.shopName', '')}的体验卡`;
    // 分享链接
    const path = `/packages/benefit-card/take/index?alias=${this.alias}&card_no=${get(this, 'data.cardNo')}`;
    // 分享图标
    return {
      title,
      path,
      imageUrl: shareImageUrl,
      desc
    };
  },

  getCardDesc() {
    const benefits = this.data.benefitList.filter(item => item.key !== 'experienceCard');
    return benefits.map(item => item.label).join(',');
  },

  // 领卡流程
  takeCard() {
    wx.navigateTo({
      url: '/packages/benefit-card/detail/index?alias=' + this.alias,
    });
  },

  getCardDescp() {
    const benefits = this.benefitsList.filter(item => item.key !== 'experienceCard');
    return benefits.map(item => item.label).join(',');
  },
});
