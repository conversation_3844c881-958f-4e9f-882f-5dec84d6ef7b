import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';

WscComponent({
  properties: {
    benefit: {
      type: Object,
      value: {}
    },
    experienceLog: {
      type: Array,
      value: []
    },
    needActivate: {
      type: Boolean,
      value: false
    },
    hasActivated: {
      type: Boolean,
      value: false
    },
    hasCard: {
      type: Boolean,
      value: false
    }
  },

  data: {
    rewardTip: '',
    disabled: true,
    openType: '',
  },

  methods: {
    getRewardTips() {
      const reward = get(this.properties.benefit, 'extInfo.scrm_bn_biz_extends_info', null);
      if (reward) {
        try {
          const rewardData = JSON.parse(reward);
          const rewardValue = get(rewardData, '[0].rewardValue', '');
          if (rewardValue) {
            return `+${rewardValue}`;
          }
          return '';
        } catch (e) {
          return '';
        }
      }
      return '';
    }
  },

  attached() {
    const { hasCard, needActivate, hasActivated } = this.properties;
    console.log(this.properties)
    const disabled = hasCard && needActivate && !hasActivated || !hasCard;
    this.setYZData({
      rewardTip: this.getRewardTips(),
      disabled,
      openType: disabled ? '' : 'share'
    });
  }
});
