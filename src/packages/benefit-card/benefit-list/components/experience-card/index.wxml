<van-cell
    custom-class="list-item"
    border="{{ false }}"
    wx:if="{{ benefit }}"
  >
    <view class="list-item__icon" slot="icon">
      <image src="{{ benefit.icon }}" class="icon-pic">
    </view>
    <view slot="title">
      <view class="van-cell__title">
        <text>{{ benefit.appName }}</text>
        <view class="van-cell__label">{{ benefit.label || benefit.desc }}</view>
      </view>
      <block wx:if="{{ hasCard}}">
        <experience-log
          experience-log="{{ experienceLog }}"
          reward-tip="{{ rewardTip }}"
        />
      </block>
    </view>
    <van-button
      disabled="{{ disabled }}"
      open-type="{{ openType }}"
      icon="share"
      round
      size="small"
      color="#7232dd"
      border="{{ false }}"
      wx:if="shareBtnVisible"
      bind:click="handleClickShare"
      custom-class="{{ disabled ? 'disabled-button' : 'share-button' }}"
    >
      分享
    </van-button>
  </van-cell>