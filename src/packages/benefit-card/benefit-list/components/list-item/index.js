import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';

WscComponent({
  properties: {
    benefit: {
      type: Object,
      value: {}
    },
    hasCard: {
      type: Boolean,
      value: false,
    }
  },
  data: {
    benefitLink: '',
    isLink: false,
  },
  methods: {
    getBenefitLink() {
      const { benefit } = this.properties;
      const payContentAlias = get(benefit, 'benefitPluginInfo.payContentAlias', '');
      const linkItem = ['present', 'paidContent'];
      const linkMap = {
        present: '/packages/ump/presents/index',
        paidContent: payContentAlias ? `/packages/paidcontent/rights/index?alias=${payContentAlias}` : ''
      };
      if (benefit && linkItem.indexOf(benefit.key) > -1) {
        return linkMap[benefit.key];
      }
    },
    getIsLink() {
      const { benefit, hasCard } = this.properties;
      const payContentAlias = get(benefit, 'benefitPluginInfo.payContentAlias', '');
      const presentRecordAlias = get(benefit, 'benefitPluginInfo.presentRecordAlias', '');
      if (benefit.key === 'paidContent') {
        return !!payContentAlias;
      }
      if (benefit.key === 'present') {
        return hasCard && !!presentRecordAlias;
      }
      return false;
    },
    handleOnClick() {
      if (this.data.isLink) {
        wx.navigateTo({
          url: this.data.benefitLink,
        });
      }
    }
  },
  attached() {
    const isLink = this.getIsLink();
    const benefitLink = this.getBenefitLink();
    this.setYZData({
      isLink,
      benefitLink,
    });
  }
});
