<view class="benefit-list">
    <block wx:for="{{ benefitList }}" wx:for-item="benefit" wx:key="{{ benefit.key }}" >
      <experience-card
        experience-log="{{ experienceLog }}"
        benefit="{{ benefit }}"
        wx:key="{{ benefit.key }}"
        wx:if="{{ benefit.key === 'experienceCard' }}"
        need-activate="{{ needActivate }}"
        has-activated="{{ hasActivated }}"
        has-card="{{ hasCard }}"
      />
      <list-item
        wx:else
        benefit="{{ benefit }}"
        has-card="{{ hasCard }}"
      />
    </block>
  </view>