import WscPage from 'pages/common/wsc-page/index';

WscPage({
  data: {
    alias: '',
    cardNo: 0,
    subType: '',
  },

  onLoad(query = {}) {
    // 引导权益卡领卡多guideType和redirectUrl两个参数
    const {
      alias,
      card_no: cardNo,
      sub_type: subType,
      guideType,
      redirectUrl,
    } = query;
    this.setYZData({
      alias,
      cardNo,
      subType,
      guideType,
      redirectUrl,
    });
  },
});
