<!-- 页面内容 -->
<page-container fixed-bottom class="page-container" pageBgColor="#ebedf0" wx:if="{{ !loading }}">
  <view class="take-card">
    <view class="card-wrapper">

      <!-- 用户信息 -->
      <view class="userinfo">
        <image src="{{ share.avatar || 'https://img.yzcdn.cn/public_files/01912119437160ddf12c554e8281b402.png'}}" alt="头像" class="avatar" />
      </view>
      <!-- 用户信息 -->

      <view class="card-title">{{ share.nickName }}送你一张{{ shopName }}的体验卡</view>

      <!-- 卡信息 -->
      <view class="card">
        <view class="card-box" style="background-image: {{ coverUrl }}">
          <view class="card-name">
            <text class="card-name-text">{{ cardInfo.cardTemplateDTO && cardInfo.cardTemplateDTO.name }}</text>
             <image src="https://img.yzcdn.cn/public_files/e3c23fe51288f4e1d1128d63b4be0df1.png" class="badge-icon">
          </view>
          <view class="card-expire">
            有效期:{{ cardExpire }}
          </view>
        </view>
      </view>
      <!-- 卡信息 -->

      <!-- 卡权益 -->
      <view class="card-benefits">
        <view class="benefit-title" wx:if="{{ benefits.length }}">{{ benefits.length }}项专享权益</view>
        <view class="{{ 'benefit-list ' + customClass}}">
          <view wx:for="{{ benefits }}" wx:key="key" class="benefit-item">
            <image src="{{ item.icon }}" class="icon-img" />
            <view class="icon-name">{{ item.appName }}</view>
          </view>
        </view>
      </view>
      <!-- 卡权益 -->

      <!-- 操作按钮 -->
      <view class="btn-wrapper">
       <view wx:if="{{ isCurrentShopAvailable }}">
          <user-authorize wx:if="{{ !unQualified }}" authTypeList="{{ ['mobile'] }}"  bind:next="takeCardAfterLogin">
          <theme-view color="general" border="general">
            <view class="btn theme-view-button" type="danger" block disabled="{{ unQualified }}">立即领取</view>
          </theme-view>
        </user-authorize>
        <theme-view wx:else color="general" border="general">
          <view class="btn theme-view-button {{ unQualified ? 'disabled-btn' : '' }}" type="danger" block bindtap="handleTakeCard">立即领取</view>
        </theme-view>
       </view>
        <view wx:else>
          <button class="btn disabled-btn">
            该卡不适用于本店，不支持领取/购买
          </button>
        </view>
      </view>
      <!-- 操作按钮 -->

    </view>
  </view>
</page-container>
<!-- 页面内容 -->

<van-dialog id="van-dialog" />
