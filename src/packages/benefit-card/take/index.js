import WscPage from 'pages/common/wsc-page/index';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import get from '@youzan/weapp-utils/lib/get';
import money from '@youzan/weapp-utils/lib/money';
import { travel } from '@youzan/weapp-utils/lib/time';
import { formatBenefits, formatDateStr } from '../utils';
import {
  EXPERIENCE_CARD,
  NEW_COLORS,
  CARD_TYPE_MAP,
  COLOR_MAP,
  moreBenefit,
} from '../constants';
import { benefitCardEnterShop } from '../common/enter-shop';

const app = getApp();
const shopInfo = app.getShopInfoSync();

WscPage({
  data: {
    alias: '', // 卡 alias
    cardInfo: {}, // 卡信息
    benefits: [], // 权益
    unQualified: false, // 有没有领卡权限
    hasMobile: false, // 是否已绑定电话号码
    shopName: get(shopInfo, 'base.shop_name', ''),
    share: {
      nickName: '', // 分享用户名称
      avatar: '', // 分享用户头像
    },
    tokenNum: 0, // 体验卡领取数量
    cardExpire: '', // 过期信息
    cardNo: 0, // 用户 卡id
    coverUrl: '', // 卡图片展示
    customClass: '',
    isCurrentShopAvailable: false,
  },

  onLoad(query = {}) {
    benefitCardEnterShop(query, this.__route__)
      .then(() => {
        this.handleOnLoad(query);
      })
      .catch(() => {
        this.handleOnLoad(query);
      });
  },

  handleOnLoad(query) {
    const { alias, card_no: cardNo } = query;
    this.cardNo = cardNo;
    this.alias = alias;
    const hasMobile = !!app.getBuyerId();

    this.fetchCardInfo();
    this.fetchShareInfo();
    this.fetchExperienceLog();
    this.setYZData({
      alias,
      hasMobile,
      cardNo,
    });
  },

  onPullDownRefresh() {
    this.fetchCardInfo();
    this.fetchShareInfo();
    this.fetchExperienceLog();
  },

  // #region 初始数据
  // 获取卡信息
  fetchCardInfo() {
    wx.showLoading({
      title: '请求中',
    });
    app
      .request({
        path: '/wscuser/scrm/api/benefitcard/getExperienceCardTplInfo.json',
        data: {
          cardAlias: this.alias,
          cardNo: this.cardNo,
        },
      })
      .then((res) => {
        wx.stopPullDownRefresh();
        if (!res) return;
        this.checkCanTakeCard();

        const { termEndAt } = res;
        const dateNow = new Date().getTime();

        if (termEndAt > 0 && termEndAt < dateNow) {
          return this.takeResultDialog({
            message: '体验卡已过期',
            confirmText: '去领权益卡',
            cancelText: '进店逛逛',
            confirmLink: 'card',
            cancelLink: 'shop',
          });
        }

        // 设置卡相关信息
        const benefitBag = get(res, 'cardTemplateDTO.benefitBag', {});
        let benefits = formatBenefits(benefitBag, EXPERIENCE_CARD);
        if (benefits.length > 4) {
          benefits = benefits.slice(0, 3);
          benefits.push(moreBenefit);
        }
        this.setYZData({
          cardInfo: res,
          benefits,
          customClass:
            benefits && benefits.length < 4 ? 'align-center-list' : '',
          cardExpire: this.getCardExpire(res),
          coverUrl: this.getCoverUrl(res),
          isCurrentShopAvailable: get(
            res,
            'cardTemplateDTO.isCurrentShopAvailable',
            false
          ),
        });
      })
      .catch(({ msg }) => {
        wx.stopPullDownRefresh();
        wx.hideLoading();
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 1000,
        });
      });
  },

  // 获取分享者信息
  fetchShareInfo() {
    app
      .request({
        path: '/wscuser/scrm/api/benefitcard/getShareInfo.json',
        query: {
          cardNo: this.cardNo,
        },
      })
      .then((res) => {
        this.setYZData({
          share: res,
        });
      })
      .catch((e) => {
        wx.showToast({
          title: e.msg,
          icon: 'none',
          duration: 2000,
        });
      });
  },

  // 设置体验卡领取信息
  fetchExperienceLog() {
    app
      .request({
        path: '/wscuser/scrm/api/benefitcard/getExperienceLog.json',
        query: {
          cardNo: this.cardNo,
        },
      })
      .then((res) => {
        this.setYZData({
          tokenNum: get(res, 'items.length', 0),
          disabled: false,
        });
      });
  },

  checkCanTakeCard() {
    app
      .request({
        path: '/wscuser/scrm/api/benefitcard/canTakeExperienceCardJson.json',
        data: {
          card_alias: this.data.alias,
        },
      })
      .then(() => {
        wx.hideLoading();
        this.setYZData({
          unQualified: false,
        });
      })
      .catch(({ msg }) => {
        wx.hideLoading();
        this.setYZData({
          unQualified: true,
        });
        this.takeResultDialog({
          message: msg || '暂无法领取',
          confirmText: '查看我的权益',
          cancelText: '进店逛逛',
          confirmLink: 'userCenter',
          cancelLink: 'shop',
        });
      });
  },

  // #endregion 初始数据

  // #region 提示文案相关
  // 获取过期相关信息
  getCardExpire(cardInfo) {
    const lifeTime = get(
      cardInfo,
      'cardTemplateDTO.benefitBag.experienceCard.experienceCardTermCycle',
      {}
    );
    const { termDays = 0 } = lifeTime;
    const dateNow = new Date();
    return `${formatDateStr(dateNow)} 至 ${formatDateStr(
      travel(termDays - 1)
    )}`;
  },

  // 规则卡领取提示文案
  getTakeCardTips() {
    const rule = get(this, 'data.cardInfo.cardTemplateDTO.ruleCondition', {});
    const ruleKeys = ['amountLimit', 'tradeLimit', 'pointsLimit'];
    let tip = '';
    ruleKeys.some((key) => {
      if (rule[key] !== undefined) {
        tip = this.getRuleTips(key, rule[key]);
        return true;
      }
      return false;
    });
    return tip;
  },

  getRuleTips(ruleType, ruleCount) {
    const tipsMap = {
      amountLimit: `体验卡被领完啦，消费${money(ruleCount).toYuan()}元即可领卡`,
      tradeLimit: `体验卡被领完啦，完成${ruleCount}笔订单即可领卡`,
      pointsLimit: `体验卡被领完啦，获得${ruleCount}积分即可领卡`,
    };
    return tipsMap[ruleType];
  },
  // #endregion 提示文案相关

  // #region 领卡逻辑
  handleTakeCard() {
    const takeOut = this.checkCardNum(); // 判断体验卡是否已领完
    if (takeOut) return;
    // 判断是否有手机号
    if (this.data.hasMobile) {
      this.doActivate();
    }
  },

  takeCardAfterLogin() {
    const takeOut = this.checkCardNum(); // 判断体验卡是否已领完
    if (takeOut) return;
    this.doActivate();
  },

  /**
   * 检测体验卡是否已被领完
   * @returns {Boolean} true: 领完 false: 未领完
   */
  checkCardNum() {
    const stockNum = get(
      this.data.cardInfo,
      'cardTemplateDTO.benefitBag.experienceCard.stockNum',
      0
    );
    const cardType = get(
      this.data.cardInfo,
      'cardTemplateDTO.cardAcquireSetting',
      CARD_TYPE_MAP.NO_RULE
    );

    if (this.data.tokenNum && this.data.tokenNum >= stockNum) {
      if (cardType !== CARD_TYPE_MAP.RULE) {
        this.takeResultDialog({
          message: '来晚了，体验卡被领取完了',
          confirmText: '去领权益卡',
          cancelText: '进店逛逛',
          confirmLink: 'card',
          cancelLink: 'shop',
        });
      } else {
        this.takeResultDialog({
          message: this.getTakeCardTips(),
          confirmText: '进店逛逛',
          cancelText: '查看我的权益',
          confirmLink: 'shop',
          cancelLink: 'userCenter',
        });
      }
      return true;
    }

    return false;
  },

  // 实际处理填写 profile 信息
  doActivate() {
    const requireProfile = get(
      this.data.cardInfo,
      'cardTemplateDTO.activationCondition.requireProfile',
      true
    );

    // TODO: YXR 判断当前是否满足条件，如果满足条件不要去激活
    // 如果需要填写个人信息
    if (requireProfile) {
      wx.navigateTo({
        url: `/packages/benefit-card/active/index?card_no=${this.data.cardNo}&alias=${this.data.alias}&sub_type=${EXPERIENCE_CARD}`,
      });
    } else {
      wx.showLoading({
        title: '请求中',
      });

      app
        .request({
          path: '/wscuser/scrm/api/benefitcard/experience/take.json',
          data: {
            cardAlias: this.data.alias,
            cardNo: this.data.cardNo,
          },
        })
        .then(() => {
          wx.hideLoading();
          wx.redirectTo({
            url: `/packages/benefit-card/detail/index?alias=${this.data.alias}`,
          });
        })
        .catch((e) => {
          wx.hideLoading();
          wx.showToast({
            title: e.msg,
            icon: 'none',
            duration: 1000,
          });
        });
    }
  },

  takeResultDialog({
    message,
    confirmText,
    cancelText,
    confirmLink,
    cancelLink,
  }) {
    const userCenterLink = app.globalData.isRetailApp
      ? '/packages/retail/usercenter/dashboard-v2/index'
      : '/packages/usercenter/dashboard/index';
    const linkMap = {
      shop: '/packages/home/<USER>/index', // 首页
      userCenter: userCenterLink, // 个人中心
      card: `/packages/benefit-card/detail/index?alias=${this.data.alias}`, // 卡详情页
    };

    Dialog.confirm({
      message,
      confirmButtonText: confirmText,
      cancelButtonText: cancelText,
    })
      .then(() => {
        if (confirmLink === 'card') {
          wx.navigateTo({
            url: linkMap[confirmLink],
          });
        } else {
          wx.reLaunch({ url: linkMap[confirmLink] });
        }
      })
      .catch(() => {
        if (cancelLink === 'card') {
          wx.navigateTo({
            url: linkMap[cancelLink],
          });
        } else {
          wx.reLaunch({ url: linkMap[cancelLink] });
        }
      });
  },

  // #endregion 领卡逻辑

  // 设置背景图片
  getCoverUrl(cardInfo) {
    const colorCode = this.getColorCode(
      get(cardInfo, 'cardTemplateDTO.colorCode', 'Color200')
    );
    const coverUrl = get(cardInfo, 'cardTemplateDTO.coverUrl', '');

    if (coverUrl) {
      return `url(https://img.yzcdn.cn${coverUrl})`;
    }
    return NEW_COLORS[colorCode]
      ? `url(${NEW_COLORS[colorCode].imageUrl})`
      : '';
  },
  // 将老版本的colorCode映射到新版本
  getColorCode(colorCode) {
    return COLOR_MAP[colorCode] || colorCode;
  },
});
