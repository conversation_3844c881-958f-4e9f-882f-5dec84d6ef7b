.benefits-list {
  background-color: #252525;
  padding-top: 24px;
  height: 140px;

  .benefit-btn-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 7px 16px;
    background: #fff;

    .benefit-btn {
      height: 36px;
      line-height: 34px;
    }
  }

  .benefits-loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .code-popup-wrapper {
    .van-popup--bottom {
      text-align: center;
      height: calc(100vh - 109px);
      border-radius: 15px 15px 0 0;
    }

    .close-pop-icon {
      position: absolute;
      bottom: 24px;
      color: #969799;
    }

    .use-rest-info {
      font-size: 14px;
      color: #7d7e80;
      margin-bottom: 8px;

      p {
        font-size: 16px;
        color: #323233;

        span {
          color: #f44;
          font-size: 20px;
          margin: 0 5px;
        }
      }
    }
  }

  .empty {
    text-align: center;
    height: 60px;
    line-height: 60px;
  }
}

.footer {
  padding-bottom: 40px !important;
}
