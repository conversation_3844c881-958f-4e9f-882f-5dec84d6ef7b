import WscPage from 'pages/common/wsc-page/index';
import get from '@youzan/weapp-utils/lib/get';
import { CARD_TYPE_MAP, FORM_CARD, shareImageUrl } from '../constants';
import { formatBenefits } from '../utils';

const app = getApp();

WscPage({
  data: {
    alias: '',
    benefitId: '',
    benefitsList: [], // 权益列表
    hasCard: false, // 当前用户是否已领取卡
    cardType: CARD_TYPE_MAP.NO_RULE, // 权益卡类型
    experienceLog: [], // 体验卡领取记录
    cardInfo: {}, // 卡信息
    rewardTips: '', // 权益卡领取提示文案
    tokenNum: 0, // 已被领取的次数
    cardNo: 0, // 卡 Id
    shopName: '', // 店铺名称，
    openType: '', // 当库存不足或者未激活的时候，openType 为空
    empty: false,
    btnText: '',
    loading: false,
    activeIndex: 0,
    isCurrentShopAvailable: false,
  },

  onLoad(query = {}) {
    const benefitId = query.benefit_id;
    const params = benefitId.split('_');
    const shopInfo = app.getShopInfoSync();
    this.setYZData({
      alias: query.alias,
      activeIndex: +params[0] ? +params[1] : 0,
      benefitId: params[0],
      shopName: get(shopInfo, 'base.shop_name', ''),
    });
  },

  onShow() {
    this.initData();
  },

  // 初始加载数据
  initData() {
    const hasMobile = !!app.getBuyerId();
    this.setYZData({
      hasMobile,
    });
    wx.showLoading({ title: '加载中' });
    this.setYZData({
      loading: true,
    });
    this.fetchCardTplInfo(this.data.alias);
  },

  // 获取卡相关信息
  fetchCardTplInfo(cardAlias) {
    app
      .request({
        path: '/wscuser/scrm/api/benefitcard/getCardTemplate.json',
        data: {
          cardAlias,
          isNewVersionApp: 1,
        },
      })
      .then((cardInfo = {}) => {
        wx.hideLoading();
        this.formatCardInfo(cardInfo);
      })
      .catch((err) => {
        wx.hideLoading();
        wx.showToast({
          title: err.msg,
          icon: 'none',
          duration: 1000,
        });

        // 数据加载失败
        this.setYZData({
          benefitsList: [],
          hasCard: false,
          empty: true,
        });
      });
  },

  formatCardInfo(cardInfo) {
    const {
      hasCard = false,
      cardTemplateDTO: cardTplInfo,
      cardNo = 0,
      subType = FORM_CARD,
      activated: hasActivated,
    } = cardInfo;
    // cardInfo 上的数据主要是与用户相关的数据
    // cardInfo.cardTemplateDTO 是卡相关的数据
    const benefitBag = get(cardTplInfo, 'benefitBag', {});
    let stockNum = get(benefitBag, 'experienceCard.stockNum', 0);
    const requireProfile = get(
      cardTplInfo,
      'activationCondition.requireProfile',
      true
    );
    const requireMobile = get(
      cardTplInfo,
      'activationCondition.requireMobile',
      true
    );
    const cardType = get(
      cardTplInfo,
      'cardAcquireSetting',
      CARD_TYPE_MAP.NO_RULE
    );
    const needActivated = requireProfile && requireMobile;
    const isCurrentShopAvailable = get(
      cardTplInfo,
      'isCurrentShopAvailable',
      false
    );
    let benefitsList = [];

    // 拥有卡并激活处理
    const hasUseAbility =
      (hasCard && !needActivated) || (hasCard && needActivated && hasActivated);
    // 如果当前卡未激活，不允许分享
    if ((needActivated && !hasActivated) || !isCurrentShopAvailable) {
      wx.hideShareMenu && wx.hideShareMenu();
    }
    // 有使用卡的权限，获取c端权益列表
    let currentBenefitId = this.data.benefitId;
    if (hasUseAbility) {
      app
        .request({
          path: '/wscuser/scrm/api/benefitcard/getBenefitsList.json',
          data: {
            card_no: cardNo,
          },
        })
        .then((data) => {
          benefitsList = formatBenefits(data, subType, 'Tpl', benefitBag);
          stockNum = get(benefitBag, 'experienceCard.stockNum', 0);
          // 根据benefitTplId找到对应的benefitId
          const { benefitId = '' } = this.data;
          const currentBenefitTplId =
            +benefitId || get(benefitsList, '0.benefitTplId');
          currentBenefitId = benefitsList.find(
            (item) => item.benefitTplId === +currentBenefitTplId
          ).benefitId;
          if (stockNum && stockNum > 0 && cardNo) {
            this.getExperienceLog(cardNo, stockNum);
          } else {
            this.setYZData({
              experienceLog: [],
              tokenNum: 0,
            });
          }
          this.setYZData({
            hasCard,
            cardInfo,
            stockNum,
            cardNo,
            hasUseAbility,
            openType: needActivated && !hasActivated ? '' : 'share',
            benefitsList,
            empty: benefitsList.length === 0,
            cardType,
            loading: false,
            benefitId: currentBenefitId,
            isCurrentShopAvailable,
          });
        })
        .catch((err) => {
          wx.hideLoading();
          wx.showToast({
            title: err.msg,
            icon: 'none',
            duration: 1000,
          });
        });
    } else {
      benefitsList = formatBenefits(benefitBag, subType);
      if (stockNum && stockNum > 0 && cardNo) {
        this.getExperienceLog(cardNo, stockNum);
      } else {
        this.setYZData({
          experienceLog: [],
          tokenNum: 0,
        });
      }
      this.setYZData({
        hasCard,
        cardInfo,
        stockNum,
        cardNo,
        openType: needActivated && !hasActivated ? '' : 'share',
        benefitsList,
        empty: benefitsList.length === 0,
        cardType,
        loading: false,
        benefitId: currentBenefitId,
        hasUseAbility,
      });
    }
  },

  // 设置体验卡领取信息
  getExperienceLog(cardNo, stockNum) {
    return app
      .request({
        path: '/wscuser/scrm/api/benefitcard/getExperienceLog.json?from=weapp',
        data: {
          cardNo,
        },
      })
      .then((res) => {
        this.setYZData({
          experienceLog: this.formatExperienceLog(res.items, stockNum),
          tokenNum: get(res, 'items.length', 0),
        });
      });
  },

  formatExperienceLog(experienceLog, stockNum) {
    // 确保领卡记录与 stockNum 一致
    const tmp = new Array(stockNum).fill({});
    tmp.splice(0, experienceLog.length, ...experienceLog);
    return tmp;
  },

  // 分享
  onShareAppMessage() {
    // 分享描述
    const desc = this.getCardDesc();
    const title = `送你一张${get(this, 'data.shopName', '')}的体验卡`;
    // 分享链接
    const path = `/packages/benefit-card/take/index?alias=${get(
      this,
      'data.alias'
    )}&card_no=${get(this, 'data.cardNo')}`;
    // 分享图标
    return {
      title,
      path,
      imageUrl: shareImageUrl,
      desc,
    };
  },

  getCardDesc() {
    const benefits = this.data.benefitsList.filter(
      (item) => item.key !== 'experienceCard'
    );
    return benefits.map((item) => item.label).join(',');
  },

  // 领卡流程
  takeCard() {
    wx.navigateTo({
      url: '/packages/benefit-card/detail/index?alias=' + this.data.alias,
    });
  },

  getCardDescp() {
    const benefits = this.benefitsList.filter(
      (item) => item.key !== 'experienceCard'
    );
    return benefits.map((item) => item.label).join(',');
  },

  // 打电话
  takeCall() {
    if (this.data.servicePhone) {
      wx.makePhoneCall({
        phoneNumber: this.data.servicePhone,
      });
    }
  },
});
