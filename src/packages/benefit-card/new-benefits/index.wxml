 <page-container>
 <view class="benefits-list">
  <!-- 权益背景图 -->
  <!-- 权益tab切换 -->
  <benefit-tabs
    benefits-list="{{ benefitsList }}"
    card-no="{{ cardNo }}"
    experience-log="{{ experienceLog }}"
    is-link="{{ isLink }}"
    benefit-index="{{ benefitId }}"
    has-card="{{ hasCard }}"
    has-use-ability="{{ hasUseAbility }}"
    active-index="{{ activeIndex }}"
    open-type="{{ openType }}"
    is-current-shop-available="{{ isCurrentShopAvailable }}"
  />
</view>
<van-toast id="van-toast" />
</page-contaier>
