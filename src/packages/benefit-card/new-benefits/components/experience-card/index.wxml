<view
 	wx:if="{{ benefit }}"
  class="experience-card__cell"
>
  <view class="title">
		<view class="benefit-name">
			<text>{{ benefit.appName }}</text>
			<view class="benefit-label">{{ benefit.label || benefit.desc }}</view>
			<block wx:if="{{ hasCard}}">
				<experience-log
					experience-log="{{ experienceLog }}"
					reward-tip="{{ rewardTip }}"
				/>
			</block>
		</view>
  </view>
  <van-button
	  disabled="{{ !hasUseAbility }}"
	  open-type="{{ openType }}"
	  round
	  size="small"
		hairline="{{ false }}"
	  border="{{ false }}"
	  bind:click="handleClickShare"
		custom-style="border: none;{{ btnStyle }}"
		custom-class="{{ disabled ? 'disabled-share' : '' }}"
  >
	立即分享
	</van-button>
</view>