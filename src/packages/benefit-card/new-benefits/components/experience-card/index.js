import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';
import Toast from '@vant/weapp/dist/toast/toast';

WscComponent({
  properties: {
    benefit: {
      type: Object,
      value: {}
    },
    experienceLog: {
      type: Array,
      value: []
    },
    hasUseAbility: {
      type: Boolean,
      value: false
    },
    hasCard: {
      type: Boolean,
      value: false
    },
    isCurrentShopAvailable: {
      type: Boolean,
    }
  },

  data: {
    rewardTip: '',
    disabled: true,
    openType: '',
    btnStyle: ''
  },

  methods: {
    getRewardTips() {
      const reward = get(this.properties.benefit, 'extInfo.scrm_bn_biz_extends_info', null);
      if (reward) {
        try {
          const rewardData = JSON.parse(reward);
          const rewardValue = get(rewardData, '[0].rewardValue', '');
          if (rewardValue) {
            return `+${rewardValue}`;
          }
          return '';
        } catch (e) {
          return '';
        }
      }
      return '';
    },

    handleClickShare() {
      if (!this.properties.isCurrentShopAvailable) {
        Toast('权益卡本店不适用，不支持分享');
      }
    }
  },
  attached() {
    const { hasCard, hasUseAbility } = this.properties;
    const disabled = !hasUseAbility || !hasCard;
    this.setYZData({
      rewardTip: this.getRewardTips(),
      disabled,
      btnStyle: disabled
        ? 'background-color: #f2f3f5;color: #979797;'
        : 'background: linear-gradient(90deg, #ffaf79, #ff5c5c, #f44);color: #fff;',
      openType: disabled || !this.properties.isCurrentShopAvailable ? '' : 'share'
    });
  }
});
