import WscComponent from 'pages/common/wsc-component/index';
import { formatDateToTime } from '../../../utils';

WscComponent({
  properties: {
    recordList: {
      type: Array,
      value: [],
      observer(newValue) {
        if (newValue.length !== 0) {
          this.initData();
        } else {
          this.setYZData({
            showList: []
          });
        }
      }
    },
    isAllShow: {
      type: Boolean,
      value: false
    },
  },
  data: {
    recordLoading: false,
    finished: false
  },
  methods: {
    toRecordDetailLink(e) {
      if (!this.properties.isAllShow) {
        return;
      }
      const { id: benefitId, scene, biz: sceneBizValue } = e.target.dataset;
      const url = `/packages/benefit-card/record-detail/index?id=${benefitId}&scene=${scene}&biz=${sceneBizValue}`;
      wx.navigateTo({
        url
      });
    },
    getShowList() {
      const { recordList, isAllShow } = this.properties;
      return isAllShow ? recordList : recordList.slice(0, 10);
    },
    handleList(list) {
      return list.map((item) => {
        return {
          createdDate: formatDateToTime(item.createdAt),
          ...item
        };
      });
    },
    initData() {
      const originList = this.getShowList();
      const showList = this.handleList(originList);
      this.setYZData({
        showList
      });
    }
  },
});
