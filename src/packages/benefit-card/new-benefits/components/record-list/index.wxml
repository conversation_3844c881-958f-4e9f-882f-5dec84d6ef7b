<view class="{{ !isAllShow ? 'record-list' : '' }}">
  <van-cell
	  wx:for="{{ showList }}"
	  wx:key="{{ item.templatePluginBaseDTO.requestId }}"
	  title="{{ item.templatePluginBaseDTO.benefitPluginInfo.pluginName }}"
	  label="{{ item.createdDate }}"
	  bind:click="toRecordDetailLink"
		data-id="{{ item.benefitId }}"
		data-scene="{{ item.scene }}"
		data-biz="{{ item.sceneBizValue }}"
    >
	  <view class="use-info">
	    <image
		  wx:if="{{ !isAllShow }}"
		  class="use-info-background"
		  src="https://b.yzcdn.cn/public_files/e77c7892b8cc3c7b4219d085f4e8d2d8.png"
	    />
		  <span>已使用{{ item.num }}次</span>
	  </view>
  </van-cell>
</view>