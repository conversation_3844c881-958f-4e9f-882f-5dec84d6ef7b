.code-popup {
  color: #333;
  text-align: center;
  background-color: transparent;

  .content {
    // margin-bottom: 38px;
    background: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .code-loading {
      height: 160px;
      text-align: center;

      .van-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        margin: -45px 0 0 -15px;
      }
    }
  }

  .van-dialog__header {
    padding-top: 17%;
    margin-bottom: 5%;
    font-size: 18px;
    font-weight: bold;
    color: #323233;
  }

  .qrcode {
    width: 192px;
    height: 192px;
    margin: 10% 0 10%;
  }

  .barcode {
    width: 275px;
    height: 84px;
    text-align: center;
  }

  .bar-number {
    margin: 12px 0;
    height: 17px;
    line-height: 17px;
    font-size: 12px;
  }

  .bar-number_text {
    margin: 12px 0;
    color: #999;
    font-size: 12px;
  }
}

.code-btn-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  padding: 8px 16px;
  background: #fff;
  height: 50px;
  display: flex;
  box-shadow: 0 -2px 10px 0 rgba(150, 151, 153, 0.12);

  .van-popup--bottom {
    text-align: center;
    height: calc(100vh - 105px);
    border-radius: 15px 15px 0 0;
  }

  .code-btn {
    height: 40px;
    width: 100%;
    font-size: 16px;

    button {
      background: linear-gradient(90deg, #e2bb7c, #e8c388);
      border-radius: 999px;
      color: #724804;

      .van-button__text {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      image {
        width: 16px;
        height: 16px;
        margin-left: 8px;
      }
    }
  }
}

.code-popup-wrapper {
  text-align: center;
  height: calc(100vh - 109px);
  border-radius: 15px 15px 0 0;

  .close-pop-icon {
    width: 14px;
    position: absolute;
    top: 16px;
    height: 14px;
    right: 20px;
  }

  .use-rest-info {
    font-size: 14px;
    color: #7d7e80;
    margin-bottom: 8px;

    .info {
      font-size: 16px;
      color: #323233;
    }

    .stock-num {
      color: #f44;
      font-size: 20px;
      margin: 0 5px;
    }
  }
}

@media screen and (max-width: 374px) {
  .card-warpper .card-info {
    width: 282px;
    height: 132px;
  }
}
