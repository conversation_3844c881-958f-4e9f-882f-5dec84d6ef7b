<!-- 按次权益底部会员码按钮 -->
<view class="code-btn-wrapper">
  <van-button
    block
    round
    class="code-btn"
    bindtap="changePopup"
    custom-style="color: #724804"
    color="linear-gradient(90deg, #e2bb7c, #e8c388)"
  >
    向商家展示会员码
  </van-button>
</view>
<!-- 按次权益会员码组件 -->
<van-popup
  show="{{ show }}"
  close-on-click-overlay="{{ false }}"
  class="code-popup-wrapper"
  position="bottom"
  round
  custom-style="height: calc(100vh - 109px);border-radius: 20px 20px 0 0;"
>
  <view class="code-popup">
    <view class="content">
      <van-icon class="close-pop-icon" color="#969799" size="18px" name="cross" bindtap="changePopup" />
      <view class="van-dialog__header">向商家展示会员码</view>
      <image src="{{ bar_code }}" class="barcode" />
      <image src="{{ qr_code }}" class="qrcode" />
    </view>
  </view>
  <view class="use-rest-info">
    <text class="info">当前剩余<text class="stock-num">{{ stockNum }}</text>次</text>
  </view>
  <view class="use-rest-info">使用时，出示此码</view>
</van-popup>