import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';
import { transformUseRule, transformValidTime } from '../../../utils';

WscComponent({
  properties: {
    benefitInfo: {
      type: Object,
      value: {},
      observer(newValue) {
        // 切换tab，更新权益的值
        if (newValue) {
          const applyStock = get(newValue, 'benefitPluginInfo.applyStock', false);
          if (applyStock) {
            const useRule = this.useRule();
            const vaildTime = this.vaildTime();
            this.setYZData({
              useRule,
              vaildTime,
              applyStock,
            });
          } else {
            this.setYZData({
              applyStock,
            });
          }
          this.setLink();
        }
      }
    },
    experienceLog: {
      type: Array,
      value: []
    },
    hasCard: {
      type: Boolean,
      value: false
    },
    hasUseAbility: {
      type: Boolean,
      value: false
    },
    cardAlias: {
      type: String,
      value: ''
    },
    kdtId: {
      type: String,
      value: ''
    },
    recordList: {
      type: Array,
      value: []
    },
    isCurrentShopAvailable: {
      type: Boolean,
    }
  },
  data: {
    page: 0,
    loadingFinished: false,
    refreshing: false,
    vaildTime: '',
    useRule: '',
    isLink: false,
    isSupportLink: false,
    applyStock: false,
  },
  methods: {
    useRule() {
      return transformUseRule(this.properties.benefitInfo, this.properties.hasUseAbility);
    },
    vaildTime() {
      const termCycle = get(this.properties, 'benefitInfo.termCycle', {});
      return transformValidTime(termCycle);
    },
    getBenefitLink() {
      const { benefitInfo } = this.properties;
      const payContentAlias = get(benefitInfo, 'benefitPluginInfo.payContentAlias', '');
      const linkItem = ['present', 'paidContent'];
      const linkMap = {
        present: '/packages/ump/presents/index',
        paidContent: payContentAlias
          ? `/packages/paidcontent/rights/index?alias=${payContentAlias}`
          : ''
      };
      if (benefitInfo && linkItem.indexOf(benefitInfo.key) > -1) {
        return linkMap[benefitInfo.key];
      }
    },
    getIsLink() {
      const { benefitInfo, hasCard } = this.properties;
      const payContentAlias = get(benefitInfo, 'benefitPluginInfo.payContentAlias', '');
      const presentRecordAlias = get(benefitInfo, 'benefitPluginInfo.presentRecordAlias', '');
      if (benefitInfo.key === 'paidContent') {
        return !!payContentAlias;
      }
      if (benefitInfo.key === 'present') {
        return hasCard && !!presentRecordAlias;
      }
      return false;
    },
    handleOnClick() {
      if (this.data.isLink) {
        wx.navigateTo({
          url: this.data.benefitLink
        });
      }
    },
    setLink() {
      const isLink = this.getIsLink();
      const benefitLink = this.getBenefitLink();
      this.setYZData({
        isLink,
        isSupportLink: isLink,
        benefitLink
      });
    }
  },
  attached() {}
});
