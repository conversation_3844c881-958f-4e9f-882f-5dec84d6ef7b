<experience-card
	wx:if="{{ benefitInfo.key === 'experienceCard' }}"
	experience-log="{{ experienceLog }}"
	benefit="{{ benefitInfo }}"
	key="{{ benefitInfo.benefitId }}"
	has-use-ability="{{ hasUseAbility }}"
	has-card="{{ hasCard }}"
	is-current-shop-available="{{ isCurrentShopAvailable }}"
/>

<view
	wx:elif="{{ isLink }}"
	class="link-item__cell"
>
	<view class="benefit-name">
		<view class="link-wrapper">
			{{ benefitInfo.appName }}
			<view
				wx:if="{{ hasUseAbility && isSupportLink }}"
				bindtap="handleOnClick"
			>
				<view class="van-button__text">查看详情<van-icon name="arrow" color="#969799" /></view>
			</view>
		</view>
		<view class="benefit-label">{{ benefitInfo.label || benefitInfo.desc }}</view>
	</view>
</view>

<view
	wx:else
	class="link-item__cell"
>
	<view class="benefit-name">
		<text>{{ benefitInfo.appName }}</text>
		<view class="benefit-label">{{ benefitInfo.label || benefitInfo.desc }}</view>
	</view>
</view>

<block wx:if="{{ applyStock }}">
	<view
		wx:if="{{ hasUseAbility }}"
		class="link-item__cell"
	>
		<view class="benefit-name">
			<text>有效时间</text>
			<view class="benefit-label">{{ vaildTime }}</view>
		</view>
	</view>
	<view
		class="link-item__cell"
	>
		<view class="benefit-name">
			<text>使用规则</text>
			<view class="benefit-label">{{ useRule }}</view>
		</view>
	</view>
</block>

