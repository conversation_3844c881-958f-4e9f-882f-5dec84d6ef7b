<!-- 权益tab滚动 -->
<scroll-view 
  class="tab {{ benefitsList.length > 4 ? 'scroll-tab' : '' }}" 
  scroll-x
  scroll-left="{{ tabScroll }}" 
  scroll-with-animation="true"
>
  <view class="tabs__nav--line">
    <view class="tabs__line" style="{{ lineStyle }}" />
    <block wx:for="{{ benefitsList }}"  wx:key="item.benefitId">
      <view 
        class="tab-item {{ scrollable ? 'tab-item-scrollable' : '' }} {{ currentTab == index ? 'active' : ''}}" 
        style="{{ scrollable ? 'flex-basis:' + 22 + '%' : '' }}"
        data-current="{{ index }}"
        bindtap='clickMenu'
      >
        <view class="item"  id="{{index}}">
          <view class="list-item__icon">
            <image src="{{ item.icon }}" class="icon-pic" />
	        </view>
	        <text class="list-item__name">{{ item.appName }}</text>
          <view class="list-item__border"></view>
        </view>
      </view>
    </block>
  </view>
</scroll-view>
<!-- 权益内容和使用内容 -->
<view class="benefit-info-wrapper">
  <view class="benefit-info">
  	<!-- 权益内容 -->
    <benefit-content
      active-index="{{ currentTab }}"
	    benefit-info="{{ benefitsList[currentTab] }}"
	    has-card="{{ hasCard }}"
	    experience-log="{{ experienceLog }}"
	    has-use-ability="{{ hasUseAbility }}"
      is-current-shop-available="{{ isCurrentShopAvailable }}"
    />
    <!-- 使用内容 -->
	  <view class="record-content" wx:if="{{ hasUseAbility && benefitsList[currentTab].benefitPluginInfo.applyStock && benefitsList[currentTab].key !== 'discount' && benefitsList[currentTab].resetNum !== null && benefitsList[currentTab].resetNum !== undefined}}" class="simple-record-list">
      <view wx:if="{{ recordList.length !== 0 }}">
        <van-cell
          title="使用记录"
          value="全部权益"
          is-link
          bind:click="toListUrl"
          label-class="cell-label-item"
          border="{{ false }}"
          title-class="use-list-title"
        />
        <record-list is-all-show="{{ false }}" record-list="{{ recordList }}"/>
      </view>
	    <benefit-code benefit-id="{{ benefitId || benefitIndex}}" stock-num="{{ benefitsList[currentTab].resetNum }}"/>
    </view>
  </view>
</view>
