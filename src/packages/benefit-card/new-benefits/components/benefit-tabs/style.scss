.benefit-info-wrapper {
  padding: 8px 16px 0;
  border-radius: 20px 20px 0 0;
  background-color: #fff;
  margin-top: 5px;

  .benefit-info {
    overflow: scroll;
    height: calc(100vh - 100px);
    padding-bottom: 100px;
    box-sizing: border-box;
  }

  .van-cell {
    padding: 15px 0;

    span {
      font-size: 16px;
      font-weight: 400;
      color: #323233;
    }

    .van-cell__label {
      font-size: 14px;
      color: #7d7e80;
    }
  }

  .van-cell:not(:last-child)::after {
    border-width: 0;
  }

  .van-hairline--top-bottom::after {
    border-width: 0 0 2px;
  }
}

.container {
  width: 100%;
  height: 79rpx;
  background: #fcfcfc;
}

.tab {
  white-space: nowrap;
  box-sizing: border-box;
  overflow: hidden;
  height: 85px;

  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }

  .active {
    color: #e1b467;
    opacity: 1;
    font-weight: bold;

    .list-item__name {
      color: #e1b467;
      font-size: 28rpx;
    }
  }
}

.tab-item {
  display: inline-block;
  width: 25%;
  font-size: 30rpx;
  color: #c4c4c4;
  flex: 1;
  opacity: 0.24;
}

.tab-item-scrollable {
  flex: 0 0 22%;
}

.item {
  width: fit-content;
  margin: auto;
  font-size: 28rpx;
  color: #c4c4c4;
  text-align: center;
  padding: 0 4px;

  .list-item__icon {
    width: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;

    > image {
      width: 40px;
      height: 40px;
      margin-bottom: 5px;
    }
  }
}

.tabs__nav--line {
  height: 105px;
  padding-bottom: 0;
  box-sizing: content-box;
  display: flex;
  -webkit-user-select: none;
  user-select: none;
  position: relative;
}

.tabs__line {
  z-index: 1;
  left: 0;
  bottom: 30px;
  height: 2px;
  position: absolute;
  background-color: #e1b467;
}

.record-content {
  margin: 0 -16px;
}

.use-list-title {
  font-size: 16px;
  font-weight: bold;
}
