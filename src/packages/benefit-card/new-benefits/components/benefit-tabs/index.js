import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';

const app = getApp();

WscComponent({
  properties: {
    cardNo: {
      type: Number,
      value: false
    },
    experienceLog: {
      type: Array,
      value: []
    },
    hasCard: {
      type: Boolean,
      value: false
    },
    hasUseAbility: {
      type: Boolean,
      value: false
    },
    isLink: {
      type: Boolean,
      value: false
    },
    benefitsList: {
      type: Array,
      value: [],
      observer(newValue, oldValue) {
        if (newValue.length !== oldValue.length && oldValue.length === 0) {
          const index = +this.properties.activeIndex;
          this.setYZData({
            currentTab: index,
            scrollable: newValue.length > 4
          });
          this.nextTick(() => this.scrollIntoView(index));
          const applyStock = get(newValue, `[${index}].benefitPluginInfo.applyStock`, false);
          if (
            this.properties.hasUseAbility
            && applyStock
            && newValue[index].key !== 'discount'
            && !this.isNil(newValue[index].resetNum)
          ) {
            this.getBenefitRecordList(index);
          }
        }
      }
    },
    activeIndex: {
      type: String,
      value: '',
      observer(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.setYZData({
            currentTab: newValue
          });
        }
      }
    },
    benefitIndex: {
      type: String,
      value: '',
    },
    isCurrentShopAvailable: {
      type: Boolean,
    }
  },
  data: {
    benefitId: '',
    currentTab: 0,
    tabScroll: 0,
    lineStyle: '',
    lineWidth: 30,
    flexStyle: '',
    scrollable: false
  },
  methods: {
    nextTick(fn) {
      setTimeout(() => {
        fn();
      }, 1000 / 30);
    },
    getRect(selector, all) {
      return new Promise(resolve => {
        wx.createSelectorQuery()
          .in(this)[all ? 'selectAll' : 'select'](selector)
          .boundingClientRect(rect => {
            if (all && Array.isArray(rect) && rect.length) {
              resolve(rect);
            }
            if (!all && rect) {
              resolve(rect);
            }
          })
          .exec();
      });
    },
    isNil(item) {
      if (item === null || item === undefined) {
        return true;
      }
      return false;
    },
    getBenefitRecordList(index) {
      const { benefitId } = this.properties.benefitsList[index];
      app
        .request({
          path: '/wscuser/scrm/api/benefitcard/getBenefitRecordList.json',
          data: {
            card_no: this.properties.cardNo,
            benefit_id: benefitId
          }
        })
        .then(data => {
          this.setYZData({
            recordList: data.items,
          });
        })
        .catch(err => {
          wx.hideLoading();
          wx.showToast({
            title: err.msg,
            icon: 'none',
            duration: 1000
          });
        });
    },
    clickMenu(event) {
      const current = event.currentTarget.dataset.current;
      if (this.data.currentTab == current) {
        return;
      }
      this.scrollIntoView(current);
      this.setYZData({
        currentTab: current,
        benefitId: this.data.benefitsList[current].benefitId
      });
      this.triggerEvent('clickMenu', { current: event.currentTarget.dataset.current }, {});
      const benefit = this.data.benefitsList[current];
      const applyStock = get(benefit, 'benefitPluginInfo.applyStock', false);
      if (this.properties.hasUseAbility && applyStock && benefit.key !== 'discount' && !this.isNil(benefit.resetNum)) {
        this.getBenefitRecordList(current);
      } else {
        this.setYZData({
          recordList: [],
        });
      }
    },
    scrollIntoView(current) {
      Promise.all([
        this.getRect('.tab-item', true),
        this.getRect('.tab')
      ]).then(([tabRects, navRect]) => {
        const tabRect = tabRects[current];
        const offsetLeft = tabRects
          .slice(0, current)
          .reduce((prev, curr) => prev + curr.width, 0);
        this.setYZData({
          tabScroll: offsetLeft - (navRect.width - tabRect.width) / 2
        });
        this.setLine(current);
      });
    },
    setLine(active) {
      const { duration = 0.3, lineWidth } = this.data;
      this.getRect('.tab-item', true).then((rects) => {
        const rect = rects[active];
        const width = lineWidth > 0 ? lineWidth : rect.width / 2;
        let left = rects
          .slice(0, active)
          .reduce((prev, curr) => prev + curr.width, 0);
        left += (rect.width - width) / 2;
        const transition = `transition-duration: ${duration}s; -webkit-transition-duration: ${duration}s;`;
        this.setYZData({
          lineStyle: `width: ${width}px;
          position: absolute;
          bottom: 30px;
          -webkit-transform: translateX(${left}px);
          transform: translateX(${left}px);
          ${transition}`
        });
      });
    },
    toListUrl() {
      const { currentTab } = this.data;
      const { cardNo } = this.properties;
      const { benefitId } = this.properties.benefitsList[currentTab];
      const url = `/packages/benefit-card/record-list/index?card_no=${cardNo}&benefit_id=${benefitId}`;
      wx.navigateTo({
        url
      });
    }
  },
});
