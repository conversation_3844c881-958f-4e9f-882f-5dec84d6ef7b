import WscPage from 'pages/common/wsc-page/index';
import get from '@youzan/weapp-utils/lib/get';
import { transformValidTime, transformUseRule, formatDateToTime } from '../utils';

const app = getApp();

WscPage({
  data: {
    tel: '', // 客服电话
    address: '', // 门店地址
    benefitId: '',
    scene: null,
    sceneBizValue: null,
    shopName: '',
  },

  onLoad(query = {}) {
    const { id: benefitId, scene, biz: sceneBizValue } = query;
    this.setYZData({
      benefitId,
      scene,
      sceneBizValue
    });
  },

  onShow() {
    this.initData();
  },

  // 初始加载数据
  initData() {
    this.getRecordDetail();
    this.getShopAddress();
    this.getShopPhone();
  },

  getRecordDetail() {
    const { scene, benefitId, sceneBizValue } = this.data;
    app
      .request({
        path: '/wscuser/scrm/api/benefitcard/getBenefitRecord.json',
        data: {
          scene,
          benefit_id: benefitId,
          scene_biz_value: sceneBizValue
        }
      })
      .then(data => {
        const rule = transformUseRule(data.templatePluginBaseDTO);
        const validTime = transformValidTime(data.termCycle);
        const createdAt = formatDateToTime(data.createdAt);
        this.setYZData({
          record: data,
          benefit: get(data, 'templatePluginBaseDTO.benefitPluginInfo', {}),
          grantCycle: get(data, 'templatePluginBaseDTO.grantCycle', {}),
          validTime,
          rule,
          createdAt,
        });
      })
      .catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: err.msg,
          icon: 'none',
          duration: 1000
        });
      });
  },

  getShopAddress() {
    const kdtId = app.getKdtId();
    return app.request({
      path: '/wscuser/scrm/api/benefitcard/getPositionBySourceKdtId.json',
      data: {
        sourceKdtid: kdtId
      }
    }).then((data) => {
      const address = data.province + data.city + data.area + data.address;
      const shopName = data.shopName;
      this.setYZData({
        address,
        shopName,
      });
    });
  },

  getShopPhone() {
    const kdtId = app.getKdtId();
    return app.request({
      path: '/wscuser/membercenter/contact.json',
      data: {
        sourceKdtid: kdtId
      }
    }).then((item) => {
      const { afterSaleContact: { mobileNumber = null, areaCode = null, phoneNumber = null } = {} } = item;
      let tel = '';
      if (phoneNumber) {
        tel = `${areaCode}-${phoneNumber}`;
      } else if (mobileNumber) {
        tel = mobileNumber;
      }
      this.setYZData({
        tel,
      });
    });
  },

  // 打电话
  takeCall() {
    if (this.data.tel) {
      wx.makePhoneCall({
        phoneNumber: this.data.tel
      });
    } else {
      wx.showToast({
        title: '商家暂无配置客服电话',
        icon: 'none',
        duration: 1000
      });
    }
  }
});
