.benefit-item {
  .van-cell {
    padding: 12px;

    span {
      font-size: 14px;
      color: #333;
    }

    .van-cell__label {
      font-size: 12px;
      color: #7d7e80;
    }
  }

  .benefit-item__title {
    border-bottom: 1px dashed #c8c9cc;

    .benefit_name {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    .benefit_detail {
      font-size: 12px;
      color: #7d7e80;
      word-break: break-word;
      display: flex;
    }
  }

  .benefit-item__icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 16px;

    > image {
      width: 48px;
      height: 48px;
    }
  }
}

.use-detail {
  padding: 12px 12px 0;
  font-size: 14px;
  height: 100vh;
  background-color: #f8f8f8;

  .panel-title {
    padding: 16px;
    font-weight: bold;
  }

  .van-cell:not(:last-child)::after {
    left: 12px;
    right: 12px;
    border-color: #d8d8d8;
  }

  .first-name {
    color: #323233;
    margin-bottom: 14px;
  }

  .info_wrapper {
    padding: 10px 16px 16px;

    .use-detail_info {
      color: #323233;
    }

    .use-detail_info-label {
      display: inline-block;
      width: 80px;
      margin-right: 20px;
      color: #7d7e80;
    }
  }

  .shop-info_wrapper {
    padding: 16px;

    .shop-info {
      color: #7d7e80;
      font-size: 13px;
      word-break: break-word;
      display: flex;

      .shop-address {
        padding-right: 5px;
        flex-grow: 1;
      }

      .shop-service_phone {
        float: right;
        padding-left: 16px;
        border-left: 1px solid #dcdee0;
        font-size: 20px;
      }
    }
  }
}

.mar_bot_10 {
  margin-bottom: 10px;
}
