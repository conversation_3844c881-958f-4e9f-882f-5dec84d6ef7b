export const benefitMap = {
  coupon: {
    name: '送优惠券',
  },
  discount: {
    name: '打折',
  },
  experienceCard: {
    name: '好友体验卡',
  },
  growth: {
    name: '送成长值',
  },
  points: {
    name: '送积分',
  },
  pointsFeedBack: {
    name: '积分倍率',
  },
  paidContent: {
    name: '知识付费',
  },
  present: {
    name: '送赠品',
  },
  postage: {
    name: '包邮',
  },
  times: {
    name: '次数',
  },
};

// 权益卡类型
export const NO_RULE = 3; // 无门槛卡
export const RULE = 6; // 规则卡
export const PAY = 8; // 付费卡
export const ERROR_CARD_TYPE = -1;

export const GUIDE_OPERATE_TYPE = 'init'; // 进入页面标识

export const CARD_TYPE_MAP = {
  NO_RULE,
  RULE,
  PAY,
  ERROR_CARD_TYPE,
};

export const EXPERIENCE_CARD = 1; // 体验卡
export const FORM_CARD = 0; // 正式卡

export const DEFAULT_COLORS = {
  direction: '135deg',
  colors: ['#FCFCFC', '#A8A8A8'],
  bgColor: '#444444',
  imageUrl: '',
};

// 会员卡背景主题
export const NEW_COLORS = {
  Color200: {
    direction: '135deg',
    colors: ['#B7D0FF', '#4E67B7'],
    bgColor: '#526AB9',
    imageUrl:
      '//img.yzcdn.cn/public_files/2019/03/11/704e415d887541d2bf9ce945483af305.png',
  },
  Color201: {
    direction: '135deg',
    colors: ['#959595', '#20242A'],
    bgColor: '#444',
    imageUrl:
      '//img.yzcdn.cn/public_files/2019/03/11/fb7d33fa35a5c118ba0495c09cdd1b3e.png',
  },
  Color202: {
    direction: '135deg',
    colors: ['#8BECBD', '#319E69'],
    bgColor: '#2F835A',
    imageUrl:
      '//img.yzcdn.cn/public_files/2019/03/11/7ac9de05b089cb13d36323d6864b8d49.png',
  },
  Color203: {
    direction: '135deg',
    colors: ['#FF9E97', '#C44E46'],
    bgColor: '#8B3C36',
    imageUrl:
      '//img.yzcdn.cn/public_files/2019/03/11/17fbdf65817fef6f4bd7acdb8fa414ab.png',
  },
  Color204: {
    direction: '135deg',
    colors: ['#FFDAA6', '#CE9A51'],
    bgColor: '#8C714B',
    imageUrl:
      '//img.yzcdn.cn/public_files/2019/03/11/659d46363828ac8f571bd4a13ad89458.png',
  },
  Color205: {
    direction: '135deg',
    colors: ['#FFB5C1', '#D73B5B'],
    bgColor: '#9D4456',
    imageUrl:
      '//img.yzcdn.cn/public_files/2019/03/11/7646eb0f59b1ce45d3cfbcb67686e93d.png',
  },
  Color206: {
    direction: '135deg',
    colors: ['#FCFCFC', '#A8A8A8'],
    bgColor: '#444444',
  },
};

export const GRANT_TO_ENDLESS = 1; // 永久有效
export const BEGIN_AT_TO_END_AT = 2; // 从固定时刻开始到固定时刻结束
export const GRANT_IN_DAYS = 3; // 从领取持续一定时长(单位日)

// 新颜色和老颜色的 Map
export const COLOR_MAP = {
  Color030: 'Color200',
  Color040: 'Color200',
  Color050: 'Color200',
  Color102: 'Color201',
  Color010: 'Color202',
  Color020: 'Color202',
  Color082: 'Color202',
  Color090: 'Color203',
  Color100: 'Color203',
  Color101: 'Color203',
  Color060: 'Color204',
  Color070: 'Color204',
  Color080: 'Color204',
  Color081: 'Color204',
};

export const shareImageUrl =
  'https://img.yzcdn.cn/public_files/23f9f5089197722cff6962bc33a66929.png';

export const moreBenefit = {
  appName: '更多权益',
  icon:
    'https://img.yzcdn.cn/public_files/a1fb1079aa056c48631a190bc5247dd9.png',
  benefitTplId: 0,
};
