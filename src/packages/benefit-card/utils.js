/* eslint-disable dot-notation */
import get from '@youzan/weapp-utils/lib/get';
import { moment as formatDate } from 'utils/time';
import pick from '@youzan/weapp-utils/lib/pick';
import omit from '@youzan/weapp-utils/lib/omit';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { EXPERIENCE_CARD } from './constants';

export const isNil = (item) => {
  return item === undefined || item === null;
};

/**
 * 获取权益展示信息
 */
const formatAppNameAndLabel = (benefits) => {
  return benefits.map((item) => ({
    ...item,
    appName: get(item, 'benefitPluginInfo.showName', ''),
    label: formatDescpOfBenefit(item),
    icon: cdnImage(get(item, 'benefitPluginInfo.icon', '')),
  }));
};

/**
 * 获取优惠券总数量
 * @param {Array} coupon 优惠券列表
 * @return {Number} 优惠券总数
 */
function getCouponTotalCount(coupon) {
  return coupon.reduce((total, item) => total + item.number, 0);
}

/**
 * 处理权益展示，主要是对权益排序，以及统一调整为 list
 */
export const formatBenefits = (
  benefitBag,
  subType,
  subObjName,
  handleBenefitBag
) => {
  const { diyTemplateList = [], experienceCard, diyBenefitList } = benefitBag;
  benefitBag = omit(benefitBag, [
    'diyTemplateList',
    'experienceCard',
    'diyBenefitList',
  ]);
  let benefits = [];
  const beforeBenefitsKeys = Object.keys(
    pick(benefitBag, [
      'postage',
      'discount',
      'memberPrice',
      'pointsFeedBack',
      'paidContent',
    ])
  );
  const afterBenefitsKeys = Object.keys(
    pick(benefitBag, ['points', 'growth', 'coupon', 'present'])
  );

  let diyBenefits = [];

  if (subObjName && diyBenefitList) {
    diyBenefits = diyBenefitList.map((item) => {
      const diyTemplate = Object.assign(
        item.diyTemplate.benefitTemplateExternal,
        omit(item.diyTemplate, 'benefitTemplateExternal')
      );
      return {
        ...diyTemplate,
        benefitId: item.benefitId,
        termCycle: item.termCycle,
        resetNum: item.stockNum,
      };
    });
  } else {
    diyBenefits = diyTemplateList.map((item) => {
      const diyTemplate = Object.assign(
        get(item, 'benefitTemplateExternal', {}),
        omit(item, 'benefitTemplateExternal')
      );
      return {
        ...diyTemplate,
      };
    });
  }

  const beforeBenefits = beforeBenefitsKeys
    .map((key) => {
      if (benefitBag[key]) {
        let benefit = benefitBag[key];
        if (subObjName) {
          /* eslint-disable-next-line max-len */
          benefit =
            key === 'paidContent'
              ? Object.assign(
                  benefitBag[key][key + subObjName],
                  handleBenefitBag[key]
                )
              : benefitBag[key][key + subObjName];
          benefit['resetNum'] = benefitBag[key]['stockNum'];
          benefit['termCycle'] = benefitBag[key]['termCycle'];
        } else {
          benefit['benefitId'] = benefitBag[key]['benefitId'];
        }
        benefit.key = key;
        delete benefitBag[key];
        return benefit;
      }
      return null;
    })
    .filter((item) => !!item);

  const afterBenefits = afterBenefitsKeys
    .map((key) => {
      if (benefitBag[key]) {
        let benefit = benefitBag[key];
        if (subObjName) {
          /* eslint-disable-next-line max-len */
          benefit =
            key === 'present'
              ? Object.assign(
                  benefitBag[key][key + subObjName],
                  handleBenefitBag[key]
                )
              : benefitBag[key][key + subObjName];
          benefit['resetNum'] = benefitBag[key]['stockNum'];
          benefit['termCycle'] = benefitBag[key]['termCycle'];
        } else {
          benefit['benefitId'] = benefitBag[key]['benefitId'];
        }
        benefit.key = key;
        if (key === 'coupon') {
          benefit.total = getCouponTotalCount(benefit.couponList);
        }
        delete benefitBag[key];
        return benefit;
      }
      return null;
    })
    .filter((item) => !!item);

  // 体验卡权益存在的话放在第一位
  let handleExperienceCard = experienceCard;
  if (experienceCard && subObjName) {
    handleExperienceCard = Object.assign(
      experienceCard.experienceCardTpl,
      omit(experienceCard, 'experienceCardTpl')
    );
  }
  experienceCard &&
    subType !== EXPERIENCE_CARD &&
    benefits.unshift({
      ...handleExperienceCard,
      key: 'experienceCard',
    });

  benefits = benefits
    .concat(beforeBenefits)
    .concat(diyBenefits)
    .concat(afterBenefits);
  return formatAppNameAndLabel(benefits);
};

const formatDescpOfBenefit = (benefit) => {
  const key = get(benefit, 'key', '');
  const descp = get(benefit, 'benefitPluginInfo.description', '');
  switch (key) {
    case 'discount':
      return `${descp}${get(benefit, 'discount', 100) / 10}折`;
    case 'experienceCard':
      return `${descp}${get(benefit, 'stockNum', 0)}张`;
    case 'points':
      return `${descp}${get(benefit, 'points', 0)}个`;
    case 'pointsFeedBack':
      return `${descp}${get(benefit, 'rate', 0) / 10}倍`;
    case 'growth':
      return `${descp}${get(benefit, 'growthValue', 0)}个`;
    default:
      return get(benefit, 'benefitPluginInfo.description', '');
  }
};

export const formatDateStr = (date, formatStr = 'YYYY-MM-DD') => {
  return formatDate(date, formatStr);
};

// 获取埋点数据
export const getMaiDianData = (query) => {
  const keysArrayOfMainDian = [
    'is_share',
    'dc_ps',
    'from_source',
    'from_params',
    'qr',
    'atr_ps',
    'gdt_vid',
    'qz_gdt',
  ];
  let maiDianData = {};

  keysArrayOfMainDian.forEach((key) => {
    maiDianData[key] = get(query, key, '');
  });

  const app = getApp();
  const loggerData = app.logger.getLogParams();

  const fromParams =
    maiDianData.from_params || (loggerData && loggerData?.context?.from_params);

  if (fromParams) {
    maiDianData = {
      ...maiDianData,
      biz_trace_point_ext: {
        ...(maiDianData.biz_trace_point_ext || {}),
        from_params: fromParams,
      },
    };
  }

  return maiDianData;
};
// 按次权益的日期转为文字描述
export const transformUseRule = (benefit, hasUseAbility) => {
  const dateMap = {
    1: '日',
    7: '周',
    30: '月',
    90: '季度',
    365: '年',
  };
  const { grantCycle = false, stockNum, resetNum } = benefit;
  const showName = get(
    benefit,
    'benefitPluginInfo.showName',
    get(benefit, 'appName', '')
  );
  if (grantCycle) {
    return `权益卡有效期内，每${dateMap[grantCycle.termDays]}可使用${
      benefit.stockNum
    }次${showName}权益`;
  }
  if (
    (!isNil(stockNum) && !isNil(resetNum)) ||
    (!isNil(stockNum) && !hasUseAbility)
  ) {
    return `权益卡有效期内，可使用${stockNum}次${showName}权益`;
  }
  return `权益卡有效期内，可使用${showName}权益`;
};

export const formatDateToDay = (time) => {
  return formatDate(time, 'YYYY-MM-DD');
};

export const formatDateToTime = (time) => {
  return formatDate(time, 'YYYY-MM-DD HH:mm:ss');
};

// 按次权益的有效期数据转为文字描述
export const transformValidTime = (termCycle) => {
  switch (termCycle.termType) {
    case 1:
      return '永久有效';
    case 2:
      return `${formatDateToDay(termCycle.termBeginAt)} - ${formatDateToDay(
        termCycle.termEndAt
      )}`;
    case 4:
      return `领卡后${termCycle.termDays}内有效`;
    default:
      return '';
  }
};

export function checkIsIphoneInWeapp(deviceType = '') {
  return (
    /iphone-x|iPhone11|iPhone13|iPhone14|iPhone12(?!,8>)/i.test(
      deviceType.replace(/\s/g, '-') || ''
    ) || /iPhone14/i.test(deviceType.replace(/\s/g, '') || '')
  );
}
