import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    recordList: '',
    cardNo: null,
    benefitId: null,
  },

  onShow() {
    this.initData();
  },

  onLoad(query = {}) {
    const benefitId = query.benefit_id;
    const cardNo = query.card_no;
    this.setYZData({
      cardNo,
      benefitId
    });
  },

  initData() {
    this.getBenefitRecordList();
  },

  getBenefitRecordList() {
    const { benefitId, cardNo } = this.data;
    app
      .request({
        path: '/wscuser/scrm/api/benefitcard/getBenefitRecordList.json',
        data: {
          card_no: cardNo,
          benefit_id: benefitId
        }
      })
      .then(data => {
        this.setYZData({
          recordList: data.items,
        });
      })
      .catch(err => {
        wx.stopPullDownRefresh();
        wx.hideLoading();
        wx.showToast({
          title: err.msg,
          icon: 'none',
          duration: 1000
        });
      });
  },
});
