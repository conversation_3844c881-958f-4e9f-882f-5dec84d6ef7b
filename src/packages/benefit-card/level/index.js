import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    cardList: [],
  },
  /**
   * mode: 显示一个月; 显示所有月份
   * @param query
   */
  onLoad(query) {
    app?.logger?.appError?.({
      name: 'WeappAccessLog',
      appName: 'wsc-h5-user',
      logIndex: 'wsc_user_log',
      message: `【WeappAccessLog】${this.route}`,
      level: 'warn',
      extra: query || {},
    });

    wx.showLoading({
      title: '努力加载中',
    });

    // 请求初始数据
    app
      .request({
        path: '/wscuser/scrm/api/benefitcard/levelinfo.json',
      })
      .then((data) => {
        wx.hideLoading();
        this.setYZData({
          cardList: data,
        });
      })
      .catch((e) => {
        console.log(e);
      });
  },
});
