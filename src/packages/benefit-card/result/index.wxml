<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
    <view class="membercard-result container">
      <view class="img-container">
        <image 
          mode="aspectFit"
          class="top-img" 
          src="https://b.yzcdn.cn/v2/image/scrm/wap/icon/<EMAIL>" 
        />
      </view>
      <view wx:if="{{ from == 'take' }}" class="status_text">已成功领取会员卡</view>
      <view wx:else class="status_text">已成功激活会员卡</view>

      <view class="btn-container">
        <!-- 领卡 进店逛逛 -->
        <view
          wx:if="{{wxCard}}"
          bindtap="addCard"
          class="active_btn zan-btn zan-btn--large zan-btn--primary btn-add-card"
        >
          添加至微信卡包
        </view>
        <van-button
          wx:if="{{!needActive}}"
          custom-class="active_btn"
          type="primary"
          size="large"
          bind:click="globalNavigate"
          data-url="/pages/home/<USER>/index"
          data-type="switch"
        >
          进店逛逛
        </van-button>

        <van-button wx:else bind:click="goToActive" custom-class="active_btn" type="primary" size="large">
          立即激活会员卡
        </van-button>
      </view>
    </view>
</page-container>

<account-login
  wx:if="{{ accountLogin }}"
  show="{{ accountLogin }}"
  bind:loginSuccess="onAccountSuccess"
  bind:closeAccountLogin="onAccountClose"
></account-login>
<van-dialog id="van-dialog" />
<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
