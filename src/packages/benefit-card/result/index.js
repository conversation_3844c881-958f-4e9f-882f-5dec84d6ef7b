import WscPage from 'pages/common/wsc-page/index';
import {
  guideJumpDialog,
  GUIDE_SUCCESS_SIGN,
  GUIDE_DIALOG_TYPE,
} from 'utils/guide-jump';

const app = getApp();

WscPage({
  data: {
    alias: '',
    cardNo: '',
    from: 'take',
    hasActive: false,
    needActive: false,
    hasMobile: false,
    wxCard: false,
    card: {},
    // 登录弹窗标志位
    accountLogin: false,
  },
  /**
   * mode: 激活
   * @param query
   */
  onLoad(query) {
    // 查找服务
    // eslint-disable-next-line camelcase
    const { alias, card_no, need_active = 0, from = 'take' } = query;
    let { card } = query;

    // 是否自动激活
    this.getActiveStatus(alias);
    // 引导领卡成功弹框
    guideJumpDialog(GUIDE_DIALOG_TYPE.equityCard, GUIDE_SUCCESS_SIGN, query);

    try {
      card = JSON.parse(card);
    } catch (e) {
      card = {};
    }
    const { wxCard } = card;
    // eslint-disable-next-line camelcase
    const needActive = Number(need_active) === 1;
    let hasMobile = !!app.getBuyerId();
    this.setYZData({
      alias,
      cardNo: card_no,
      needActive,
      from,
      hasMobile,
      wxCard,
      card,
    });
    setTimeout(() => {
      hasMobile = !!app.getBuyerId();
      this.setYZData({
        hasMobile,
      });
    }, 200);
  },

  goToActive() {
    if (this.data.hasMobile) {
      this.realActive();
    } else {
      this.setYZData({
        accountLogin: true,
      });
    }
  },

  getActiveStatus(alias) {
    app
      .request({
        path: '/wscuser/card/has-buyer-active.json',
        query: { alias },
      })
      .then((data) => {
        if (data.value) {
          this.data.hasActive = true;
        }
      });
  },

  onAccountSuccess() {
    this.realActive();

    this.setYZData({
      accountLogin: false,
    });
  },

  onAccountClose() {
    this.setYZData({
      accountLogin: false,
    });
  },

  realActive() {
    // 需要profile且存在必填项未填写则跳转激活页面
    // 不需要profile则直接激活
    const { alias = '', hasActive } = this.data;
    wx.showLoading({
      title: '努力加载中',
    });
    app
      .request({
        path: '/wscuser/scrm/api/benefitcard/get.json',
        query: {
          alias,
        },
      })
      .then((data) => {
        const card = data.card || data.customer_card.card;
        const customerCard = data.customer_card;
        if (card.detail.activation_condition.require_profile && !hasActive) {
          wx.navigateTo({
            url: `/packages/benefit-card/active/index?card_no=${customerCard.card_no}&alias=${alias}&sub_type=0`, // 0表示正式卡 1 表示体验卡 体验卡领取结果页面只有正式卡才会跳转过来，sub_type可以固定为0
          });
        } else {
          app
            .request({
              path: '/wscuser/scrm/api/benefitcard/activate-card.json',
              method: 'post',
              data: {
                cardNo: customerCard.card_no,
                customerInfo: {},
              },
            })
            .then(() => {
              wx.hideLoading();
              wx.navigateBack();
            })
            .catch((e) => {
              wx.hideLoading();
              wx.showToast({
                title: e.msg,
                icon: 'none',
                duration: 1000,
              });
            });
        }
      })
      .catch((e) => {
        wx.hideLoading();
        wx.showToast({
          title: e.msg,
          icon: 'none',
          duration: 1000,
        });
      });
  },

  addCard() {
    const { wxCard, card } = this.data;
    if (!wxCard) return;
    const cardId = card.template_id;
    const cardExt = JSON.stringify({
      code: card.card_no,
      openid: card.open_id,
      timestamp: card.timestamp,
      signature: card.signature,
      nonce_str: card.nonce_str,
    });
    const cardList = [
      {
        cardId,
        cardExt,
      },
    ];

    wx.addCard({
      cardList,
      success() {
        wx.showToast({
          title: '成功添加至微信卡包',
          icon: 'none',
        });
      },
      fail(err) {
        if (err.errMsg !== 'addCard:fail cancel') {
          wx.showToast({
            title: err.errMsg,
            icon: 'none',
          });
        }
      },
    });
  },
});
