import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';
import args from '@youzan/weapp-utils/lib/args';

const bizTypeMap = {
  benefitCard: 2,
};

const app = getApp();

function checkBenefitCardIsNeedEnterShop(cardTplAlias) {
  return app.request({
    path: '/wscuser/scrm/api/benefitcard/is-need-enter-shop.json',
    data: {
      cardTplAlias,
    },
  });
}

export function scrmEnterShop(
  redirectUrl,
  bizList,
  query = {},
  logParams = {}
) {
  const { logName, logMsg } = logParams;
  if (!redirectUrl || !bizList) {
    app.logger.appError({
      name: logName || 'scrmEnterShop_without_necessary_params',
      message: logMsg || '调用进店方法缺少必要参数',
      detail: {
        bizList,
        appVersion: app.getVersion(),
        launchOption: wx.getLaunchOptionsSync(),
      },
    });

    return Promise.reject();
  }

  return autoEnterShop({
    ...query,
    redirectUrl,
    bizList,
  });
}

export function benefitCardEnterShop(query, path) {
  const { alias: cardTplAlias } = query;
  const redirectUrl = args.add(`/${path}`, query);
  return checkBenefitCardIsNeedEnterShop(cardTplAlias)
    .then((res) => {
      if (res.value) {
        const bizList = [
          {
            bizType: bizTypeMap.benefitCard,
            bizAlias: {
              cardTplAlias,
            },
          },
        ];
        return scrmEnterShop(
          redirectUrl,
          encodeURIComponent(JSON.stringify(bizList)),
          query,
          { logName: 'benefitCardEnterShop_without_necessary_params' }
        );
      }
      return Promise.resolve();
    })
    .catch((e) => {
      app.logger.appError({
        name: 'scrmEnterShop_app_reject',
        message: e.message,
        detail: {
          e,
          appVersion: app.getVersion(),
          launchOption: wx.getLaunchOptionsSync(),
        },
      });
      return Promise.reject();
    });
}
