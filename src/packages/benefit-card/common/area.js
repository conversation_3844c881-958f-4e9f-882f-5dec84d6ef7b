import each from '@youzan/weapp-utils/lib/each';

const formatAreaData = function (code, area) {
  let formatedAreaData = {};

  // 地区code格式化
  let selectedProvince = code.toString().slice(0, 2) || -1;
  let selectedCity = code.toString().slice(0, 4) || -1;

  // 省市区循环格式化
  // 省份信息全要
  formatedAreaData.province = filterAndFormatAreaData(0, area.province, '省份');
  formatedAreaData.city = filterAndFormatAreaData(selectedProvince, area.city, '城市');
  formatedAreaData.county = filterAndFormatAreaData(selectedCity, area.county, '区县');

  return formatedAreaData;
};

var filterAndFormatAreaData = function (areaCode, areaData, defaultText) {
  var areaList = [];

  if (defaultText) {
    areaList.push({
      text: defaultText,
      code: areaCode
    });
  }

  // 这里可以做个优化，因为区县的遍历一次遍历3000多个项目，有点多
  // 考虑到原始areaOrigin里面地址code码都是从小到大排列的
  each(areaData, (text, code) => {
    // 如果有areacode或者省市区编码开始不对应
    if (areaCode && code.indexOf(areaCode) !== 0) {
      return;
    }

    areaList.push({
      text,
      code
    });
  });

  return areaList;
};

// 根据area_code和所有省市区列表 计算出当前选择的省市区的index
// 微信的picker是根据index来判断选中的是哪个
function findSelectedAddressIndex(areaCode, area) {
  // 地区code格式化
  var selectedProvince = areaCode.toString().slice(0, 2) + '0000';
  var selectedCity = areaCode.toString().slice(0, 4) + '00';

  // 初始值
  var provinceIndex = 0;
  var cityIndex = 0;
  var countyIndex = 0;

  // 如果没有地区编码的话，就直接返回默认值
  if (!areaCode) {
    return {
      provinceIndex,
      cityIndex,
      countyIndex
    };
  }

  provinceIndex = area.province.findIndex(({
    code
  }) => {
    return code == selectedProvince;
  });

  cityIndex = area.city.findIndex(({
    code
  }) => {
    return code == selectedCity;
  });

  countyIndex = area.county.findIndex(({
    code
  }) => {
    return code == areaCode;
  });

  return {
    provinceIndex,
    cityIndex,
    countyIndex
  };
}

export {
  formatAreaData,
  findSelectedAddressIndex
};
