export const OWL_TYPE = {
  COLUMN: 1,
  CONTENT: 2,
  VIP_BENEFIT: 3,
  LIVE: 4,
  PUNCH: 5,
  NEW_PUNCH: 9,
};

export const OWL_PATH_FUNC = {
  [OWL_TYPE.COLUMN]: (alias) =>
    `/packages/paidcontent/column/index?alias=${alias}`,
  [OWL_TYPE.CONTENT]: (alias) =>
    `/packages/paidcontent/content/index?alias=${alias}`,
  [OWL_TYPE.VIP_BENEFIT]: (alias) =>
    `/packages/paidcontent/rights/index?alias=${alias}`,
  [OWL_TYPE.LIVE]: (alias) => `/packages/paidcontent/live/index?alias=${alias}`,
  [OWL_TYPE.PUNCH]: (alias) => `/packages/punch/activity/index?alias=${alias}`,
  [OWL_TYPE.NEW_PUNCH]: (alias) =>
    `/packages/new-punch/introduction/index?alias=${alias}`,
};

// 角色类型 2—外部销售员  1—内部销售员
const EXTERNAL_SALESMAN = 2;
const INTERNAL_SALESMAN = 1;

export const SALESMAN_TYPE = {
  EXTERNAL_SALESMAN,
  INTERNAL_SALESMAN,
};

export const SALESMAN_TYPE_TEXT = {
  [EXTERNAL_SALESMAN]: '外部销售员',
  [INTERNAL_SALESMAN]: '内部销售员',
};

export const PROMOTE_GOODS_TAB_TYPE = {
  /** 营销 */
  UMP_TYPE: 1,
  /** 佣金 */
  COMMISSION_TYPE: 2,
  /** 销量 */
  SOLDNUM_TYPE: 3,
  /** 上新 */
  NEWEST_TYPE: 4,
  /** 价格 */
  PRICE_TYPE: 5,
};

/** 推广商品tabs */
export const PROMOTE_GOODS_TABS = [
  {
    title: '销量',
    bizType: PROMOTE_GOODS_TAB_TYPE.SOLDNUM_TYPE,
  },
  {
    title: '营销',
    bizType: PROMOTE_GOODS_TAB_TYPE.UMP_TYPE,
  },
  {
    title: '佣金',
    bizType: PROMOTE_GOODS_TAB_TYPE.COMMISSION_TYPE,
  },
  {
    title: '上新',
    bizType: PROMOTE_GOODS_TAB_TYPE.NEWEST_TYPE,
  },
  {
    title: '价格',
    bizType: PROMOTE_GOODS_TAB_TYPE.PRICE_TYPE,
  },
];

/** 价格排序sort字段值 */
export const PRICE_SORT_TYPE = {
  ASC: 'asc',
  DESC: 'desc',
};

export const PRICE_SORT_TEXT_MAP = {
  [PRICE_SORT_TYPE.ASC]: '升序',
  [PRICE_SORT_TYPE.DESC]: '降序',
};

export const UMP_ALL = 0; // 全部

export const UMP_ACTIVITY_TYPE = {
  /** 拼团 */
  GROUPON: 4,
  /** 秒杀 */
  SECKILL: 6,
  /** 限时折扣 */
  TIME_LIMITED_DISCOUNT: 11,
  /** 砍价 */
  HELPCUT: 21,
};

/** 营销活动tabs */
export const ACTIVITY_TYPE_TABS = [
  {
    title: '全部',
    activityType: UMP_ALL,
  },
  {
    title: '拼团',
    activityType: UMP_ACTIVITY_TYPE.GROUPON,
  },
  {
    title: '限时折扣',
    activityType: UMP_ACTIVITY_TYPE.TIME_LIMITED_DISCOUNT,
  },
  {
    title: '秒杀',
    activityType: UMP_ACTIVITY_TYPE.SECKILL,
  },
  {
    title: '砍价',
    activityType: UMP_ACTIVITY_TYPE.HELPCUT,
  },
];

/** 商品请求的来源 1表示导购工作台 */
export const SOURCE_FROM = 1;

/** 当前用户的导购类型 */
export const GUIDE_TYPE = {
  /** 暂未获取到导购身份 */
  UNKNOWN: 'unknown',
  /** 是导购身份 */
  IS_GUIDE: 'is_guide',
  /** 不是导购身份 */
  NOT_GUIDE: 'not_guide',
};
