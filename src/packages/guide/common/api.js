const app = getApp();

// 获取当前佣金tab是否展示
export function getCommissionRuleType() {
  return app
    .request({
      path: '/guide/goods/getCommissionRuleType.json',
      method: 'GET',
    })
    .catch(() => {
      // 调用失败时 默认展示佣金tab
      return { showCommission: true };
    });
}

// 获取商品列表
export function getPromoteGoodsList(data) {
  return app.request({
    path: '/guide/goods/listGoods.json',
    data,
    method: 'GET',
  });
}

/** 初始化多渠道网店渠道参数 */
export function getValidOnlineShopList(data) {
  return app.request({
    path: '/guide/getValidOnlineShopList.json',
    data,
    method: 'GET',
  });
}
