import WeappLogSdk from '@youzan/weapp-log-sdk';
import { setLogBizInfo } from 'utils/log/logv3';

const app = getApp();

const defaultExtra = {
  kdtId: app.getKdtId(),
  type: 'weapp',
  buyerId: app.getBuyerId(),
};

// 导购埋点上报对象
const guideLog = new WeappLogSdk({
  batch: true,
  plat: {
    yai: 'shop_guide_workbench_b',
    st: 'weapp',
  },
});

setLogBizInfo(guideLog);

const log = (logData, params = {}) => {
  const { sl = '' } = params;
  const { params: logParams = {} } = logData;
  logData.params = {
    ...defaultExtra,
    ...logParams,
    ...params,
    sl,
  };

  if (!guideLog.getLogParams()?.user.buyer_id) {
    guideLog.setUser(app.logger.getGlobal()?.user);
  }

  guideLog.log({
    ...logData,
    durl: app.logger.durl,
  });
};

export { log };
