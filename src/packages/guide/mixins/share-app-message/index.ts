import event from '@youzan/tee-event';

export default {
  data() {
    return {
      shareInfo: {},
    };
  },

  mounted() {
    event.on(`update${this.pageKey || 'guide'}ShareInfo`, (shareInfo) => {
      this.shareInfo = shareInfo;
    });
  },

  destroyed() {
    event.off(`update${this.pageKey || 'guide'}ShareInfo`);
  },

  methods: {
    onShareAppMessage() {
      return this.shareInfo;
    },
  },
};
