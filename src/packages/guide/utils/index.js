/**
 * 获取导购员分享链接用户二次转发id
 * 只有在导购员分享链接用户二次转发逻辑中才会使用到，判断了链接是否携带from_params参数
 * @returns
 */
export function getGuideSecondSharerId() {
  try {
    const app = getApp();
    const pages = getCurrentPages?.() || []; // 获取加载的页面
    const currentPage = pages.length > 0 ? pages[pages.length - 1] : {}; // 获取当前页面的对象
    // 如果要获取url中所带的参数可以查看options
    const query = currentPage?.options || {};

    // 导购员分享链接用户二次转发逻辑
    // 从 query 中获取 from_params 新增分享参数 id，则拼接如果解析失败则不处理，
    if (query?.from_params) {
      return app?.getBuyerId() || '';
    }
  } catch (err) {}

  return '';
}
