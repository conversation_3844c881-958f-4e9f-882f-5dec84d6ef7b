const PAGE_TYPE = 'salesGoods';

const LOG_CONFIG_MAP = {
  enterPage: {
    et: 'display', // 事件类型
    ei: 'enterpage', // 事件标识
    en: '浏览页面', // 事件名称
    pt: PAGE_TYPE, // 页面类型
  },
  scanClick: {
    et: 'click',
    ei: 'gzt_splb_scan_code_click',
    en: '推广商品列表页点击扫码',
    pt: PAGE_TYPE,
  },
  tabClick: {
    et: 'click',
    ei: 'guide_promote_goods_tab_click',
    en: '导购推广商品tab点击',
    pt: PAGE_TYPE,
  },
  searchClick: {
    et: 'click',
    ei: 'guide_search_promote_goods',
    en: '导购搜索推广商品',
    pt: PAGE_TYPE,
  },
  promoteBtnClick: {
    et: 'click',
    ei: 'guide_promote_goods_promote_btn_click',
    en: '导购推广商品按钮点击',
    pt: PAGE_TYPE,
  },
  hitGoodsListCache: {
    et: 'view',
    ei: 'hit_guide_promote_goods_cache',
    en: '命中推广商品第一屏缓存数据',
    pt: PAGE_TYPE,
  },
};

export { LOG_CONFIG_MAP };
