<page-container
  page-container-class="{{ themeClass }} page-{{ deviceType }}"
  pageBgColor="#f5f5f5"
  copyrightBgColor="#f5f5f5"
>
  <view class="guide-promote-goods-container">
    <van-sticky>
      <online-shop-switcher 
        bind:change="handleChangeShop"
        valid-online-shop-list="{{ validOnlineShopList }}" 
        show-switcher="{{ showShopName }}"
      />
      <van-search
        value="{{ keywords }}"
        placeholder="输入商品名称、条码/编码查询"
        shape="round"
        custom-class="guide-goods-search"
        background="#fff"
        use-right-icon-slot
        show-action="{{ !!keywords }}"
        bind:change="handleSearch"
      >
        <view
          wx:if="{{!keywords}}"
          class="search-box-right-area"
          slot="right-icon"
          bind:tap="scanBarCode"
        >
          <van-icon name="scan" />
          <text>扫码</text>
        </view>
      </van-search>
      <van-tabs
        active="{{ active }}"
        bind:click="handleTabClick"
        swipe-threshold="5"
        line-height="0"
        custom-class="guide-goods-tabs {{ showCommission ? '' : 'no-show-commission' }}"
        tab-class="biz-type-tab"
        tab-active-class="active-tab {{ sortType }}"
        border="{{ false }}"
      >
        <van-tab
          wx:for="{{ promoteGoodsTabs }}"
          title="{{ item.title }}"
          wx:key="title"
          wx:for-index="index"
          title-style="{{ active === index ? 'color:#ff720d;font-size:28rpx;font-weight: bold' : 'font-size:28rpx;' }}"
        >
        </van-tab>
      </van-tabs>
      <ump-tab
        active="{{ umpActive }}"
        bind:handleActivityTypeChange="handleActivityTypeChange"
        class="guide-activity-type-tabs"
        wx:if="{{ showUmpTabs }}"
      />
    </van-sticky>
    <view style="height: {{ showUmpTabs ? paddingHeight + 44 : paddingHeight }}px"></view>
    <good-list
      show-shop-name="{{ showShopName }}"
      goods-list="{{ goodsList }}"
      loading="{{ loading }}"
      failed="{{ failed }}"
      finished="{{ finished }}"
      bind:handlePromoteBtnClickLog="handlePromoteBtnClickLog"
      bind:reFetchList="reFetchList"
      bind:updateShareInfo="updateShareInfo"
    />
    <van-toast id="van-toast" />
  </view>
</page-container>
