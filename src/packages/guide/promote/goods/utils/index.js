import { UMP_ACTIVITY_TYPE } from '../../../common/constant';
import isNumber from 'lodash/isNumber';

/** 格式化传给赚字的商品信息 */
export function formatGoodsInfo(goodDetail) {
  return {
    ...goodDetail,
    goodsActivityInfo: {
      activityId: goodDetail.activityId,
      activityName: goodDetail.activityName,
      activityType: goodDetail.activityType,
      price: goodDetail.price,
    },
    goodsPrice: {
      maxOriginPrice: goodDetail.maxOriginPrice,
      minOriginPrice: goodDetail.minOriginPrice,
      maxPrice: goodDetail.maxPrice,
      minPrice: goodDetail.minPrice,
    },
    img: goodDetail.pic,
    path: goodDetail.weappUrl,
    goodsMainPictures: JSON.parse(goodDetail.picture || '[]'),
  };
}

/** 拼接商详链接 */
export function getGoodsDetailUrl(goodDetail) {
  const { goodsAlias, activityAlias, activityType } = goodDetail || {};
  let goodsDetailUrl = `/pages/goods/detail/index?alias=${goodsAlias}`;
  // 只有秒杀活动需要添加额外的活动参数，其余活动不需要
  if (activityType === UMP_ACTIVITY_TYPE.SECKILL) {
    goodsDetailUrl += `&umpType=seckill&activityType=seckill&umpAlias=${activityAlias}`;
  }
  return goodsDetailUrl;
}

/** 分转元 */
export function cent2yuan(value) {
  return Number(value / 100).toFixed(2);
}

export function formatCent(value) {
  // 移除小数位后的末尾0: 1.05 => 1.05 2.50 => 2.5 3.00 => 3
  return +cent2yuan(value);
}

function getStr(prefix, first, scend) {
  if (first || scend) {
    return `${prefix}${[first, scend].filter((v) => !!v).join('；')}`;
  }
  return '';
}

/** 佣金按金额时展示文案处理 */
function fixedAmountHandler(bindRelationCommissionValue, orderCommissionValue) {
  const orderCommissionStr =
    +orderCommissionValue > 0
      ? `销售导购 ￥${formatCent(orderCommissionValue)}`
      : '';
  const bindRelationCommissionStr =
    +bindRelationCommissionValue > 0
      ? `专属导购 ￥${formatCent(bindRelationCommissionValue)}`
      : '';
  const str = getStr(
    '佣金金额：',
    orderCommissionStr,
    bindRelationCommissionStr
  );
  return str;
}

/** 佣金按比例时展示文案处理 */
function percentageHandler(bindRelationCommissionValue, orderCommissionValue) {
  const orderCommissionStr =
    +orderCommissionValue > 0
      ? `销售导购 ${formatCent(orderCommissionValue)}%`
      : '';
  const bindRelationCommissionStr =
    +bindRelationCommissionValue > 0
      ? `专属导购 ${formatCent(bindRelationCommissionValue)}%`
      : '';
  const str = getStr(
    '佣金比例：',
    orderCommissionStr,
    bindRelationCommissionStr
  );
  return str;
}

const commissionHandlerMap = {
  fixed_amount_per: fixedAmountHandler,
  fixed_amount: fixedAmountHandler,
  percentage: percentageHandler,
};

const ProfitDimensionType = {
  NO: 0,
  YES: 1,
};

// 按分销员返佣时，分销员侧佣金设置类型
const CustomType = {
  // 默认比例
  defaultRate: 0,
  // 自定义比例
  customRate: 1,
  // 自动以佣金金额
  customAmount: 2,
};

export function getCommissionTips(goodDetail) {
  const {
    commissionType,
    bindRelationCommissionValue,
    orderCommissionValue,
    profitDimension,
    isCustom,
    rate,
    constantCommission,
  } = goodDetail || {};
  // 商品是否按照利润比例设置佣金: 若是-不展示佣金比例和佣金
  if (profitDimension === ProfitDimensionType.YES) {
    return '';
  }

  // 存在commissionType 按导购提成模板配置展示
  if (commissionType) {
    const commissionTips = commissionHandlerMap[commissionType](
      bindRelationCommissionValue,
      orderCommissionValue
    );
    return commissionTips;
  }

  if (
    (isCustom === CustomType.customRate ||
      isCustom === CustomType.defaultRate) &&
    +rate > 0
  ) {
    return `佣金比例：${formatCent(rate)}%`;
  }

  if (isCustom === CustomType.customAmount && +constantCommission > 0) {
    return `佣金金额：￥${formatCent(constantCommission)}`;
  }
  return '';
}

export const getValidKdtIds = (shops) => {
  return shops?.length === 0
    ? undefined
    : shops.map((shop) => {
        if (isNumber(shop)) return shop;
        return shop.id;
      });
};
