/**
 * 埋点项目：https://data.qima-inc.com/track/workspace/project/1531
 */
import spm from 'shared/utils/spm';
import {
  PROMOTE_GOODS_TAB_TYPE,
  PRICE_SORT_TEXT_MAP,
} from '../../../common/constant';
import { log } from '@/packages/guide/common/log';
import { LOG_CONFIG_MAP } from '../constants';

/** 处理log参数 */
function handleLogParams(params) {
  const { bizType, title, sortType, activityType, activityTypeTabName } =
    params;
  let logParams = {
    tab_name: `「${title}」tab`,
    tab_type: bizType,
  };
  if (bizType === PROMOTE_GOODS_TAB_TYPE.PRICE_TYPE) {
    const name = `${title}-${PRICE_SORT_TEXT_MAP[sortType]}}`;
    logParams = {
      ...logParams,
      tab_name: `「${name}」tab`,
      sort_type: sortType,
    };
  } else if (bizType === PROMOTE_GOODS_TAB_TYPE.UMP_TYPE) {
    logParams.activity_type = activityType;
    logParams.activity_type_tab_name = activityTypeTabName;
  }
  return logParams;
}

export function enterPageLog() {
  log(LOG_CONFIG_MAP.enterPage, {
    spm: spm.getSpm(),
  });
}

/** tab点击埋点 */
export function tabClickLog(params) {
  const logParams = handleLogParams(params);
  log(LOG_CONFIG_MAP.tabClick, logParams);
}

/** 搜索埋点 */
export function searchLog(extra) {
  log(LOG_CONFIG_MAP.searchClick, extra);
}

/** 推广按钮点击埋点 */
export function promoteBtnClickLog(params) {
  const { profitDesc } = params;
  const logParams = handleLogParams(params);
  log(LOG_CONFIG_MAP.promoteBtnClick, {
    ...logParams,
    // 是否有佣金推广
    profit_desc: profitDesc,
  });
}

/** 点击扫码埋点 */
export function scanCodeLog(params) {
  log(LOG_CONFIG_MAP.scanClick, params);
}

/** 命中商品缓存数据埋点 */
export function hitGoodsCacheLog() {
  log(LOG_CONFIG_MAP.hitGoodsListCache);
}
