/* eslint-disable camelcase */
import WscPage from 'pages/common/wsc-page';
import debounce from '@youzan/weapp-utils/lib/debounce';
import args from '@youzan/weapp-utils/lib/args';
import { getSalesmanParamsObject } from '@youzan/salesman-params-handler';
import Toast from '@vant/weapp/dist/toast/toast';

import {
  PROMOTE_GOODS_TAB_TYPE,
  PROMOTE_GOODS_TABS,
  PRICE_SORT_TYPE,
  ACTIVITY_TYPE_TABS,
  SOURCE_FROM,
} from '../../common/constant';
import { getGoodsDetailUrl, getValidKdtIds } from './utils';
import {
  enterPageLog,
  searchLog,
  tabClickLog,
  promoteBtnClickLog,
  scanCodeLog,
  hitGoodsCacheLog,
} from './log';
import {
  getCommissionRuleType,
  getPromoteGoodsList,
  getValidOnlineShopList,
} from '../../common/api';
import logv3 from 'utils/log/logv3';
import { ScanCodeFailType, ScanType } from '@/packages/guide/enum/wxSDK';

const app = getApp();
const PAGE_SIZE = 10;
const DEFAULT_STICKY_HEIGHT = 0;
const TAB_HIGHT = 44;

WscPage({
  data: {
    promoteGoodsTabs: [...PROMOTE_GOODS_TABS],
    activityTypeTabs: ACTIVITY_TYPE_TABS,
    // 价格tab默认升序排序
    sortType: PRICE_SORT_TYPE.ASC,
    // 默认选中销量tab（索引=<>0）
    active: 0,
    // 营销tab下活动默认选中所有（索引=0）
    umpActive: 0,
    // 是否显示营销的 tabs
    showUmpTabs: false,
    goodsList: [],
    allGoodsList: {},
    keywords: '',
    page: 1,
    finished: false,
    loading: false,
    // 是否展示佣金tab
    showCommission: false,
    // 请求商品列表是否失败
    failed: false,
    currentKdtId: app.getKdtId(),
    // 当前分享的商品
    shareGoods: null,
    // 赚字分享信息
    shareData: null,
    // 扫条码内容
    barCodeResult: '',
    // 是否是扫码搜索
    isScanCode: false,
    // 多渠道网店渠道店铺列表
    validOnlineShopList: [],
    // 当前筛选的多渠道网店渠道店铺
    onlineShopList: [],
    // goodsItem是否需要展示shopName
    showShopName: false,
    // sticky缺失的高度补偿
    paddingHeight: DEFAULT_STICKY_HEIGHT,
  },

  onShow() {
    enterPageLog();
  },

  onLoad(options) {
    this.initCurrentKdtId(options).then(() => {
      this.initData();
      this.initEvent();
    });
  },

  /** 初始化当前店铺 id */
  initCurrentKdtId(options) {
    const kdtId = options.kdt_id || options.kdtId || app.getKdtId();
    this.setYZData({
      currentKdtId: kdtId,
    });
    return getValidOnlineShopList({ kdt_id: kdtId }).then(
      (validOnlineShopList = []) => {
        this.setYZData({
          validOnlineShopList,
          onlineShopList: validOnlineShopList,
          showShopName: validOnlineShopList.length > 1,
          paddingHeight:
            this.data.paddingHeight + validOnlineShopList.length
              ? TAB_HIGHT
              : 0,
        });
      }
    );
  },

  /** 初始化数据 */
  initData() {
    wx.showToast({
      title: '加载中...',
      icon: 'loading',
    });

    getCommissionRuleType().then(({ showCommission }) => {
      wx.hideToast();
      let promoteGoodsTabs = [...PROMOTE_GOODS_TABS];
      if (!showCommission) {
        // 不展示佣金tab
        promoteGoodsTabs = promoteGoodsTabs.filter(
          (item) => item.bizType !== PROMOTE_GOODS_TAB_TYPE.COMMISSION_TYPE
        );
      }
      this.setYZData({
        showCommission,
        promoteGoodsTabs,
      });

      this.getList(false);
    });
  },

  /** 初始化事件绑定 */
  initEvent() {
    this.handleSearch = debounce((e) => {
      const keywords = e.detail;
      this.setYZData({
        page: 1,
        finished: false,
        keywords,
        goodsList: [],
        // tab也需要重置
        sortType: PRICE_SORT_TYPE.ASC,
        active: 0,
        umpActive: 0,
        showUmpTabs: false,
      });
      this.getList(false);
      searchLog({ keywords });
    }, 400);
  },

  /** 获取推广商品列表 */
  getList(doLog = true) {
    const {
      keywords,
      finished,
      page,
      active,
      sortType,
      umpActive,
      promoteGoodsTabs,
      currentKdtId,
      isScanCode,
      barCodeResult,
      allGoodsList,
      onlineShopList,
    } = this.data;
    let { goodsList } = this.data;

    if (finished && !isScanCode) {
      return;
    }

    const { bizType } = promoteGoodsTabs[active];
    const params = {
      keywords,
      page,
      pageSize: PAGE_SIZE,
      bizType,
      sourceFrom: SOURCE_FROM,
      kdt_id: currentKdtId,
      searchBarCode: isScanCode,
      kdtIds: getValidKdtIds(onlineShopList)?.join(','),
    };

    if (bizType === PROMOTE_GOODS_TAB_TYPE.UMP_TYPE) {
      params.activityType = ACTIVITY_TYPE_TABS[umpActive].activityType;
    } else if (bizType === PROMOTE_GOODS_TAB_TYPE.PRICE_TYPE) {
      params.sortType = sortType;
    }

    const goodsCacheKey = this.getGoodsListCacheKey(params);

    if (doLog) {
      // 搜索和初次进入请求时 不进行埋点上报
      tabClickLog({
        ...promoteGoodsTabs[active],
        ...params,
        activityTypeTabName: ACTIVITY_TYPE_TABS[umpActive].title,
      });
    }

    // 扫条码需要将tab重置第一个，关键词为条码内容
    if (isScanCode) {
      params.page = 1;
      params.keywords = barCodeResult;
      params.bizType = PROMOTE_GOODS_TAB_TYPE.SOLDNUM_TYPE;
      Toast.loading();
    } else if (goodsCacheKey && params.page === 1) {
      const cacheGoodsList = allGoodsList[goodsCacheKey];
      if (cacheGoodsList && cacheGoodsList.length) {
        hitGoodsCacheLog();
        this.setYZData({
          goodsList: cacheGoodsList,
        });
      }
    }

    this.setYZData({
      loading: true,
      failed: false,
    });
    getPromoteGoodsList(params)
      .then((data) => {
        const { hasNext, list = [], page: pageOfCurrent } = data;

        // 扫码搜索，且结果为空，弹窗提示，列表内容不渲染
        if (isScanCode) {
          if (list?.length) {
            Toast.clear();
            this.setYZData({
              active: 0,
              keywords: barCodeResult,
            });
            goodsList = [];
          } else {
            Toast({
              message: '没有搜索到对应商品，请重新扫码',
              duration: 2000,
            });
            this.setYZData({
              isScanCode: false,
              loading: false,
            });
            return;
          }
        }

        // 视图滚动到顶部
        if (goodsList.length === 0 || isScanCode) {
          wx.pageScrollTo({
            scrollTop: 0,
          });
        }

        list?.forEach((v) => {
          v.alias = v.goodsAlias;
          // 处理商详链接
          v.weappUrl = getGoodsDetailUrl(v);
        });

        // 缓存每个 tab 的第一页数据，保证二次切换 tab 时，导购可以更早看到商品
        if (!isScanCode && page === 1 && list?.length && goodsCacheKey) {
          allGoodsList[goodsCacheKey] = list;
          this.setYZData({
            allGoodsList,
          });
        }

        const newGoodsList = goodsList.concat(list);
        this.setYZData({
          goodsList: newGoodsList,
          page: pageOfCurrent + 1,
          finished: !hasNext,
          isScanCode: false,
        });
        if (hasNext && (!list?.length || newGoodsList.length < 10)) {
          // ① 还有数据，但本次请求返回是[] 需要再次请求
          // ② 还有数据，但当前总商品条数不够10条 无法触发触底自动加载 需要再次请求 直到总条数满足10条
          this.getList(false);
        } else {
          this.setYZData({
            loading: false,
          });
        }
      })
      .catch(() => {
        wx.showToast({ title: '获取商品列表失败', icon: 'none' });
        this.setYZData({
          loading: false,
          failed: true,
          isScanCode: false,
        });
      });
  },

  resetList() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    });
    this.setYZData({
      active: 0,
      page: 1,
      loading: true,
      failed: false,
      finished: false,
      goodsList: [],
      sortType: PRICE_SORT_TYPE.ASC,
      umpActive: 0,
      showUmpTabs: false,
    });
  },

  handleChangeShop({ detail }) {
    this.resetList();
    this.setYZData({
      onlineShopList: detail,
    });
    this.getList();
  },

  handleTabClick(e) {
    const { index } = e.detail;
    const { active, promoteGoodsTabs } = this.data;
    let { sortType, umpActive } = this.data;
    const fromTabInfo = promoteGoodsTabs[active];
    const ToTabInfo = promoteGoodsTabs[index];
    const showUmpTabs =
      promoteGoodsTabs[index].bizType === PROMOTE_GOODS_TAB_TYPE.UMP_TYPE;
    if (index === active) {
      // ① tab没变化
      if (ToTabInfo.bizType === PROMOTE_GOODS_TAB_TYPE.PRICE_TYPE) {
        // 价格tab点击时 变换排序顺序
        sortType =
          sortType === PRICE_SORT_TYPE.ASC
            ? PRICE_SORT_TYPE.DESC
            : PRICE_SORT_TYPE.ASC;
      } else {
        // tab没变化 不用重新请求
        return;
      }
    } else {
      // ② tab变化
      switch (fromTabInfo.bizType) {
        case PROMOTE_GOODS_TAB_TYPE.PRICE_TYPE:
          // 如果是从价格tab切成其他tab 排序重置
          sortType = PRICE_SORT_TYPE.ASC;
          break;
        case PROMOTE_GOODS_TAB_TYPE.UMP_TYPE:
          // 如果是从营销tab切成其他tab 营销类别重置
          umpActive = 0;
          break;
        default:
          break;
      }
    }
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    });
    this.setYZData({
      active: index,
      page: 1,
      loading: true,
      failed: false,
      finished: false,
      goodsList: [],
      sortType,
      umpActive,
      showUmpTabs,
    });
    this.getList();
  },

  handleActivityTypeChange(e) {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    });
    const index = e.detail;
    this.setYZData({
      page: 1,
      finished: false,
      goodsList: [],
      umpActive: index,
    });
    this.getList();
  },

  onReachBottom() {
    const { finished, loading } = this.data;
    if (finished || loading) {
      return;
    }
    this.getList(false);
  },

  // 点击推广按钮埋点
  handlePromoteBtnClickLog(e) {
    const { index } = e.detail;
    this.setYZData({
      shareGoods: this.data.goodsList[index],
    });
    const { active, promoteGoodsTabs } = this.data;
    const { sortType, umpActive } = this.data;
    const { title: activityTypeTabName, activityType } =
      ACTIVITY_TYPE_TABS[umpActive];
    promoteBtnClickLog({
      ...promoteGoodsTabs[active],
      sortType,
      activityType,
      activityTypeTabName,
      ...e.detail,
    });
  },

  /** 获取缓存商品列表的 key */
  getGoodsListCacheKey({ keywords, bizType, activityType, sortType }) {
    // 搜索情况下，不缓存
    if (keywords) {
      return '';
    }

    let key = bizType;
    if (activityType) {
      key += '-' + activityType;
    }

    if (sortType) {
      key += '-' + sortType;
    }

    return key;
  },

  /**
   * 更新分享参数
   * @param e
   */
  updateShareInfo(e) {
    const { shareData } = e.detail;
    this.setYZData({
      shareData,
    });
  },

  // 监听分享
  onShareAppMessage() {
    const { shareGoods, shareData, currentKdtId } = this.data;

    if (!shareGoods || !shareData) {
      return;
    }

    const { seller } = shareData;
    const { from_params } = getSalesmanParamsObject({
      sl: seller,
      kdtId: currentKdtId,
    });

    const title = `好友给你推荐了${shareGoods.title}`;

    return logv3.processShareData({
      title,
      path: args.add(shareGoods.weappUrl, {
        from_params,
        kdt_id: currentKdtId,
        sl: seller,
      }),
      imageUrl: shareGoods.pic,
    });
  },

  // 请求失败 重新请求
  reFetchList() {
    const { loading } = this.data;
    if (loading) {
      return;
    }
    this.getList(false);
  },

  /**
   * 扫描条形码搜索
   */
  scanBarCode() {
    wx.scanCode({
      // 仅允许扫条形码
      scanType: [ScanType.BarCode],
      success: (res) => {
        const { result } = res;
        // 校验条码格式
        this.validateBarCode(result);
      },
      fail: (res = {}) => {
        const { errMsg } = res;
        if (errMsg !== ScanCodeFailType.Cancel) {
          Toast.fail('扫描条形码失败，请重试');
        }
      },
    });
    // 上报埋点
    scanCodeLog({
      kdtId: this.data.currentKdtId,
    });
  },

  /**
   * 扫条码码结果校验&执行分页请求
   * @param {string} str 条码字符串
   */
  validateBarCode(str) {
    // 条码需要为数字、大小写字母及常用符号，1-32位
    const regExp = /^[\w!@#$%^&*()_+:"|?`[\]'\\./-]{1,32}$/;
    const result = regExp.test(str);

    // 格式不对，弹窗提示
    if (!result) {
      return Toast('条码格式错误，请重新扫码');
    }

    this.setYZData({
      barCodeResult: str,
      isScanCode: true,
    });
    this.getList();
  },
});
