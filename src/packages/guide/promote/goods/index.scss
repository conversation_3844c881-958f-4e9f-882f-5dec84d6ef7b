.guide-promote-goods-container {
  .guide-goods-search {
    padding: 6px 16px;
  }

  .van-search {
    padding: 6px 16px;
    flex: 1;

    &__action {
      padding: 0 0 0 10px;
      font-weight: 400;
      font-size: 16px;
      line-height: 32px;
    }

    &__content {
      padding-left: 10px;
    }

    &__field {
      .van-icon-search {
        color: #c8c9cc;
      }
    }
  }

  .search-box-right-area {
    position: relative;
    display: flex;
    align-items: center;
    line-height: 20px;
    color: #333;
    font-weight: 400;
    font-size: 14px;

    .van-icon {
      margin-left: 10px;
      font-size: 16px;
    }

    &::before {
      content: '';
      height: 16px;
      width: 0;
      border-left: 1px solid #e0e0e0;
    }

    text {
      margin-left: 6px;
    }
  }

  .guide-goods-tabs {
    .biz-type-tab {
      line-height: 48px;
    }

    .biz-type-tab:last-child {
      position: relative;

      .van-tab__title::after {
        position: absolute;
        right: 14px;
        top: 19px;
        content: '';
        width: 8px;
        height: 10px;
        background: url(https://b.yzcdn.cn/salesman/image/<EMAIL>) no-repeat
          center;
        background-size: 100% !important;
        z-index: 99;
      }

      &.active-tab {
        .van-tab__title::after {
          background: url(https://b.yzcdn.cn/salesman/image/<EMAIL>)
            no-repeat center;
        }

        &.desc {
          .van-tab__title::after {
            transform: rotate(180deg);
          }
        }
      }
    }

    &.no-show-commission {
      .biz-type-tab:last-child .van-tab__title::after {
        right: 25px;
      }
    }
  }

  .guide-activity-type-tabs {
    .van-tabs__wrap.van-hairline--top-bottom::after {
      border-bottom-width: 0;
    }

    .van-tabs__nav {
      padding-left: 28rpx;
    }

    .van-tab {
      flex: none;
      margin-right: 2px;

      .van-tab__title {
        background: #f7f7f7 !important;
        border-radius: 12px !important;
        line-height: 24px;
        padding: 0 12px !important;
        font-size: 12px;
        margin-top: 10px;
      }

      &.van-tab--active {
        .van-tab__title {
          background: #fff0e6 !important;
        }
      }
    }
  }

  .guide-good-commission-info {
    .share-btn {
      button {
        .van-button__text {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

@media screen and (max-width: 375px) {
  .guide-promote-goods-container {
    .guide-goods-tabs {
      .biz-type-tab:last-child {
        .van-tab__title::after {
          right: 18rpx;
        }
      }

      &.no-show-commission {
        .biz-type-tab:last-child .van-tab__title::after {
          right: 36rpx;
        }
      }
    }

    .guide-activity-type-tabs {
      .van-tab {
        .van-tab__title {
          font-size: 24rpx;
          padding: 0 24rpx !important;
        }
      }
    }
  }
}
