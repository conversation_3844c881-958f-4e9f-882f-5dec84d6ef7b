<view class="guide-promote-goods-list">
  <view wx:if="{{ !!goodsList.length }}">
    <good-item
      show-shop-name="{{ showShopName }}"
      wx:for="{{ goodsList }}"
      wx:for-item="goodDetail"
      wx:key="index"
      good-detail="{{ goodDetail }}"
      data-index="{{index}}"
      bind:handlePromoteBtnClickLog ="handlePromoteBtnClickLog"
      bind:updateShareInfo="updateShareInfo"
    />
    <view wx:if="{{ finished }}" class="no-more">没有更多了</view>
  </view>
  <view wx:if="{{ loading }}" class="guide-goods-list__loading">
    <van-loading type="spinner" />
  </view>
  <view wx:if="{{ failed }}" class="no-more" bind:tap="reLoad">加载失败，点击重新加载</view>
  <van-empty
    wx:if="{{ !loading && !failed && goodsList.length === 0 }}"
    description="暂无数据"
    image="https://b.yzcdn.cn/images/salesman/center/icons/no_order.png"
  />
</view>