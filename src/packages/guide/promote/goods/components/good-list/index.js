import WscComponent from 'pages/common/wsc-component';

WscComponent({
  properties: {
    goodsList: {
      type: Array,
      value: [],
    },
    loading: {
      type: Boolean,
      value: false,
    },
    failed: {
      type: Boolean,
      value: false,
    },
    finished: {
      type: Boolean,
      value: false,
    },
    showShopName: {
      type: Boolean,
      value: false,
    },
  },
  methods: {
    handlePromoteBtnClickLog(e) {
      const { index } = e.target.dataset;
      this.triggerEvent('handlePromoteBtnClickLog', {
        ...e.detail,
        index,
      });
    },

    reLoad() {
      this.triggerEvent('reFetchList');
    },

    /**
     * 更新分享参数
     * @param e
     */
    updateShareInfo(e) {
      this.triggerEvent('updateShareInfo', e.detail);
    },
  },
});
