import WscComponent from 'pages/common/wsc-component';
import { ACTIVITY_TYPE_TABS } from '../../../../common/constant';

WscComponent({
  options: {
    styleIsolation: 'apply-shared',
  },

  properties: {
    active: {
      type: Number,
      value: 0,
    },
  },

  data: {
    activityTypeTabs: ACTIVITY_TYPE_TABS,
  },

  methods: {
    handleTabClick(e) {
      const { index } = e.detail;
      const { active } = this.properties;
      if (index === active) return;
      this.triggerEvent('handleActivityTypeChange', index);
    },
  },
});
