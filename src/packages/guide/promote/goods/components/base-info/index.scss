.guide-good-base-info {
  position: relative;
  padding: 24rpx 32rpx;

  .mr8 {
    margin-right: 16rpx;
  }

  .to-good-detail {
    display: flex;
  }

  &__pic {
    height: 68px;
    width: 68px;
    border-radius: 4px;
  }

  &__content {
    flex: 1;
    padding-left: 24rpx;

    .item {
      margin-bottom: 8rpx;

      &.title {
        font-size: 28rpx;
        line-height: 48rpx;
        color: #333;
        font-weight: 500;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        word-break: break-all;

        .activity-tag {
          position: relative;
          top: -1px;
          margin-right: 4px;
        }
      }

      &.desc {
        font-size: 24rpx;
        line-height: 36rpx;
        color: #999;
      }

      &.price {
        display: flex;
        flex-direction: row;

        .cur-price {
          margin-right: 8px;
          font-size: 28rpx;
          line-height: 40rpx;
          color: #d42f15;
          font-weight: 500;
          word-break: break-word;
        }

        .old-price {
          font-size: 24rpx;
          line-height: 40rpx;
          color: #999;
          text-decoration: line-through;
          word-break: break-word;
        }
      }
    }
  }

  &.has-cube-btn {
    .guide-good-base-info__content {
      .item {
        &.price {
          padding-right: 62px;
        }
      }
    }
  }

  .share-btn {
    position: absolute;
    right: 16px;
    bottom: 14px;

    button {
      font-size: 28rpx;
    }
  }
}

@media screen and (max-width: 375px) {
  .guide-good-base-info {
    padding: 24rpx 32rpx;

    &__content {
      .item {
        &.title {
          // font-size: 28rpx;
        }

        &.desc {
          font-size: 24rpx;
        }

        &.price {
          .cur-price {
            margin-right: 16rpx;
            font-size: 26rpx;
          }

          .old-price {
            font-size: 22rpx;
          }
        }
      }
    }

    .share-btn {
      right: 24rpx;
      bottom: 22rpx;
    }
  }
}
