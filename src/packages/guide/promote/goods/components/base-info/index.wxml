<view class="{{!goodDetail.profit ? 'guide-good-base-info has-cube-btn': 'guide-good-base-info' }}">
  <navigator class="to-good-detail" url="{{ goodDetail.weappUrl }}">
    <image wx:if="{{ goodsImgUrl }}" src="{{ goodsImgUrl }}" class="guide-good-base-info__pic" />
    <view class="guide-good-base-info__content">
      <view class="item title">
        <van-tag wx:if="{{ goodDetail.activityName }}" text-color="#d42f15" color="#fbeaea" class="activity-tag">{{ goodDetail.activityName }}</van-tag>
        {{ goodDetail.title }}
      </view>
      <view class="item desc">
        <text class="mr8">库存：{{ goodDetail.stock || 0 }}</text>
        <text>销量：{{ goodDetail.totalSoldNum || 0 }}</text>
      </view>
      <view class="item price">
        <text class="cur-price">¥{{ goodDetail.priceStr }}</text>
        <text wx:if="{{ !!goodDetail.originPriceStr }}" class="old-price">¥{{ goodDetail.originPriceStr }}</text>
      </view>
    </view>
  </navigator>

  <salesman-cube
    wx:if="{{ !goodDetail.profit }}"
    custom-icon
    special
    needUpdatePath
    useExternalProfit
    goods-info="{{ goodsInfoForCube }}"
    need-bind-relation="{{ false }}"
    scenes="guide_promote_goods"
    get-share-opportunity="show"
    bind:set-share="updateShareInfo"
  >
    <van-button round slot="icon" icon="https://b.yzcdn.cn/salesman/image/share.png" class="share-btn" size="small" color="#ff720d" bind:click="logCubeClick">推广</van-button>
  </salesman-cube>
</view>
