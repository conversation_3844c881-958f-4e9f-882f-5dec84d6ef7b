import fullfillImage from '@youzan/weapp-utils/lib/cdn-image';
import WscComponent from 'pages/common/wsc-component';

WscComponent({
  properties: {
    goodsInfoForCube: {
      type: Object,
      value: {},
    },
    goodDetail: {
      type: Object,
      value: {},
      observer() {
        if (this.data.goodDetail.pic) {
          this.setYZData({
            goodsImgUrl: fullfillImage(
              this.data.goodDetail.pic,
              '!120x120.jpg'
            ),
          });
        }
      },
    },
  },
  data: {
    goodsImgUrl: '',
  },
  methods: {
    logCubeClick() {
      this.triggerEvent('handlePromoteBtnClickLog', {
        profitDesc: '无佣金推广',
      });
    },

    /**
     * 分享信息更新，获取赚字内分享参数
     */
    updateShareInfo(e) {
      this.triggerEvent('updateShareInfo', e.detail);
    },
  },
});
