import WscComponent from 'pages/common/wsc-component';

const ALL_SHOP_KEY = 0;
const DEFAULT_OPTIONS = [
  {
    text: '全选',
    value: ALL_SHOP_KEY,
  },
];

WscComponent({
  options: {
    styleIsolation: 'shared',
  },

  properties: {
    validOnlineShopList: {
      type: Array,
      value: [],
      observer(newVal) {
        this.setYZData({
          options: DEFAULT_OPTIONS.concat(
            newVal.map(({ name, id }) => ({
              text: name,
              value: id,
            }))
          ),
        });
      },
    },
    showSwitcher: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    selected: ALL_SHOP_KEY,
    options: DEFAULT_OPTIONS,
  },

  methods: {
    onChange(event) {
      const { detail: kdtId } = event;
      this.triggerEvent(
        'change',
        kdtId === ALL_SHOP_KEY ? this.data.validOnlineShopList : [kdtId]
      );
    },
    toggle() {
      this.selectComponent('#dropDown').toggle();
    },
  },
});
