.online-shop-switcher {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #333;
  height: 44px;
  background-color: #fff;
  font-family: 'PingFang SC';
  font-style: normal;
}

.switch-btn {
  display: flex;
  align-items: center;
  height: 100%;
  width: 72px;
  justify-content: space-between;
  .van-icon {
    transform: translateY(1px);
    color: #999;
  }
}

.van-dropdown-menu {
  box-shadow: none;
}

.van-dropdown-menu__title {
  box-shadow: none;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  font-family: 'PingFang SC';
  font-style: normal;
  padding: 0;
  &::after {
    display: none;
  }
}

.van-dropdown-item__option {
  &:first-child {
    border-top: 0.5px solid #ebedf0;
  }

  display: flex;
  align-items: center;
}

.van-dropdown-item__title {
  font-size: 16px;
  height: 28px;
  font-weight: 400;
  font-family: 'PingFang SC';
  font-style: normal;
}
