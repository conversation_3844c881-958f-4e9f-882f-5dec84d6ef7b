import WscComponent from 'pages/common/wsc-component';
import { formatCent, getCommissionTips } from '../../utils';

WscComponent({
  options: {
    styleIsolation: 'apply-shared',
  },
  properties: {
    goodsInfoForCube: {
      type: Object,
      value: {},
    },
    goodDetail: {
      type: Object,
      value: {},
      observer(newVal, oldVal) {
        if (newVal.goodsAlias !== oldVal.goodsAlias) {
          this.getComputedValue(newVal);
        }
      },
    },
  },
  data: {
    profitStr: '',
    commissionDesc: '',
  },
  methods: {
    // 计算部分字段 方便展示
    getComputedValue(detail) {
      const { profit } = detail;
      this.setYZData({
        profitStr: formatCent(profit),
        commissionDesc: getCommissionTips(detail),
      });
    },
    logCubeClick() {
      this.triggerEvent('handlePromoteBtnClickLog', {
        profitDesc: '有佣金推广',
      });
    },

    /**
     * 分享信息更新，获取赚字内分享参数
     */
    updateShareInfo(e) {
      this.triggerEvent('updateShareInfo', e.detail);
    },
  },
});
