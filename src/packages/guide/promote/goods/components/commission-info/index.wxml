<view class="guide-good-commission-info" wx:if="{{ goodDetail.profit > 0 }}">
  <salesman-cube
    class="cube-wrapper"
    need-bind-relation="{{ false }}"
    custom-icon
    special
    needUpdatePath
    useExternalProfit
    goods-info="{{ goodsInfoForCube }}"
    scenes="guide_promote_goods"
    get-share-opportunity="show"
    bind:set-share="updateShareInfo"
  >
    <van-button
      round
      slot="icon"
      icon="https://b.yzcdn.cn/salesman/image/share.png"
      class="share-btn"
      size="small"
      color="#ff720d"
      bind:click="logCubeClick"
    >
      预计赚 ¥ {{ profitStr }}
    </van-button>
  </salesman-cube>
  <view wx:if="{{ commissionDesc }}" class="commission-detail">{{ commissionDesc }}</view>
</view>