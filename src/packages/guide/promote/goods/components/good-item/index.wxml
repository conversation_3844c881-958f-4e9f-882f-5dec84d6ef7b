<view class="guide-good-item">
  <view wx:if="{{ showShopName && goodDetail.shopName }}" class="goods-shop">
    {{ goodDetail.shopName }}
  </view>
  <base-info
    goodDetail="{{ goodDetail }}"
    goodsInfoForCube="{{ goodsInfoForCube }}"
    bind:handlePromoteBtnClickLog="handlePromoteBtnClickLog"
    bind:updateShareInfo="updateShareInfo"
  />
  <commission-info
    goodDetail="{{ goodDetail }}"
    goodsInfoForCube="{{ goodsInfoForCube }}"
    bind:handlePromoteBtnClickLog ="handlePromoteBtnClickLog"
    bind:updateShareInfo="updateShareInfo"
  />
</view>
