import WscComponent from 'pages/common/wsc-component';
import { formatGoodsInfo } from '../../utils';

WscComponent({
  properties: {
    goodDetail: {
      type: Object,
      value: {},
      observer(newVal, oldVal) {
        if (newVal.goodsAlias !== oldVal.goodsAlias) {
          this.formatGoodsInfoHandler(newVal);
        }
      },
    },
    showShopName: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    goodsInfoForCube: {},
  },

  methods: {
    formatGoodsInfoHandler(detail) {
      this.setYZData({
        goodsInfoForCube: formatGoodsInfo(detail),
      });
    },

    /**
     * 更新分享参数
     * @param e
     */
    updateShareInfo(e) {
      this.triggerEvent('updateShareInfo', e.detail);
    },

    handlePromoteBtnClickLog(e) {
      this.triggerEvent('handlePromoteBtnClickLog', e.detail);
    },
  },
});
