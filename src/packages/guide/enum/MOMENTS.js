export const MOMENT_TASK_CLICKED_MAP = 'MOMENT_TASK_CLICKED_MAP';
export const PROCESS_STEP = 'PROCESS_STEP';
export const WebviewPagePath = '/pages/common/webview-page/index';
export const MOMENTS_TYPE = {
  0: '未执行',
  1: '部分执行',
  2: '已完成',
  3: '部分完成',
};

// 朋友圈任务发布渠道： 1-企微 2-个微 3-企微+个微
export const MOMENTS_TARGET = {
  WxWork: 1,
  WeChat: 2,
  WxWorkAndWeChat: 3,
};

// 点击发送任务类型
export const MOMENTS_TASK_TYPE = {
  WxWork: 'MOMENTS_QW',
  WeChat: 'MOMENTS_WX',
};

// 图片加码类型
export const CODE_TYPE = {
  /** 店铺小程序码 */
  SHOP_WEAPP_CODE: 1,
  /** 店铺二维码 */
  SHOP_CODE: 2,
  /** 商品小程序码 */
  GOODS_WEAPP_CODE: 3,
  /** 商品二维码 */
  GOODS_CODE: 4,
  /** 不加码 */
  NOT_CODE: 5,
  /** 微页面小程序码 */
  FEATURE_WEAPP_CODE: 6,
  /** 微页面二维码 */
  FEATURE_CODE: 7,
};

export const RETURN_VISIT_TASK_TYPE = {
  VISIT: 1,
  ADD_FRIENDS: 2,
  NEW_CUSTOMER: 3,
  CRM_VISIT: 4,
  OLD_CUSTOMER: 5,
  MOMENTS: 6,
};

export const STEPS = {
  FIRST: 1,
  SECOND: 2,
  THIRD: 3,
  FORTH: 4,
};

export const STEP_TEXT = {
  [STEPS.FIRST]: '第一步',
  [STEPS.SECOND]: '第二步',
  [STEPS.THIRD]: '第三步',
  [STEPS.FORTH]: '第四步',
};

export const COMPONENT_TYPES = {
  TextLinkCopy: 'TextLinkCopy',
  MomentsImageList: 'MomentsImageList',
  SendMomentsTask: 'SendMomentsTask',
  ConfirmUpload: 'ConfirmUpload',
};

// 是否需要上传截图
export const Necessary = {
  Need: 1,
  NoNeed: 0,
};

export const PLATFORM_TYPE = {
  H5: 'h5',
  WEAPP: 'weapp',
  ALIPAY: 'aliPay',
  QQ: 'qq',
  SWANAPP: 'swanapp', // 百度小程序
  GUANG: 'guang', // 爱逛小程序
};

/** 回访数据tab枚举 */
export const TaskDataTabType = {
  /** 任务业绩 */
  Earning: 'earnings',
  /** 客户互动数据 */
  Interaction: 'interaction',
  /** 客户访问 */
  Content: 'content',
  /** 客户访问 */
  Customer: 'customer',
};
