<page-container pageBgColor="#fff">
  <van-loading wx:if="{{ loading }}" size="24px" />
  <view wx:else class="auth-container">
    <view class="auth-header">
      <image class="auth-image" src="{{ logo }}" />
      <view class="auth-company">{{ shopName }}</view>
    </view>
    <view class="auth-hr" />
    <view class="auth-desc">
      <view class="auth-title">微信授权登录，享受更多服务</view>
      <view class="auth-content">登录获取您的公开信息（{{ authType === 'info' ? '昵称、头像' : '手机号'}}等），以便为您提供专属的定制服务，不会涉及您的个人隐私</view>
      <user-authorize wx:if="{{ authType === 'info' }}" authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="onGetUserInfo">
        <button type="primary" size="large">微信授权登录</button>
      </user-authorize>
      <user-authorize wx:else authTypeList="{{ ['mobile'] }}" bind:next="onGetPhoneNumber">
        <button type="primary" size="large">微信授权登录</button>
      </user-authorize>
    </view>
  </view>
</page-container>

<account-wx-login id="account-wx-login" />
