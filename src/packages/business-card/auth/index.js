/* eslint-disable @youzan/dmc/wx-check */
import WscPage from 'pages/common/wsc-page/index';
import { promisifiedCarmen } from 'utils/promisified-api';
import openWebView from 'shared/utils/open-web-view';
import { init<PERSON>alesman } from 'shared/utils/salesman-share';
import { getCurrentPage } from 'shared/common/base/wsc-component';
import { handleQueryParams } from '../common/utils';
import { EShoppingGuideBindSourceType } from 'constants/guide';

const app = getApp();

const NOT_GET_PHONE = 0;
const GET_PHONE = 1;
const GET_PHONE_SUCCESS = 2;
const GET_PHONE_FAIL = 3;

WscPage({
  data: {
    authType: 'info',
    fromSource: '',
    fromParams: '',
    sl: '',
    loading: false,
    logo: '',
    shopName: '',
    sales: {},
    getPhoneInfo: NOT_GET_PHONE,
    src: '',
  },

  onLoad(query = {}) {
    const kdtId = app.getKdtId();
    app
      .requestUseCdn({
        path: '/wscsalesman/salesman/shop/checkShopAbility.json',
        data: {
          abilityName: 'salesman_advance_personal_space_ability',
          kdtId,
        },
        method: 'GET',
      })
      .then((res) => {
        // 无店铺能力时直接跳转到缺省页
        if (!res.valid) {
          openWebView(
            `https://h5.youzan.com/wscump/salesman/center-v2/empty-page?kdt_id=${kdtId}&titleKey=SalesCard`,
            {
              method: 'redirectTo',
            }
          );
        }
      })
      .finally(() => {
        const {
          from_source: fromSource,
          from_params: fromParams,
          auth_type: authType,
          get_mobile_info: getMobileInfo,
          sl,
        } = query;
        let sales = {};
        if (fromParams) {
          sales = handleQueryParams(fromParams);
        }

        app.getUserInfo(() => {
          if (this.data.authType === 'info') {
            this.injectTrackData({
              fromSource,
              fromParams,
              salesId: sales.sales_id,
            });
            this.onGetUserInfo();
          }
        });

        app.getShopInfo().then((shopInfo) => {
          this.setYZData({
            authType: authType || 'info',
            fromSource,
            fromParams,
            sl,
            logo: shopInfo.logo,
            shopName: shopInfo.shop_name,
            sales,
            getPhoneInfo: +getMobileInfo || NOT_GET_PHONE,
          });
        });
      });
  },

  injectTrackData({ fromSource, fromParams, salesId }) {
    app.globalData.from_source = fromSource;
    app.globalData.from_params = fromParams;
    app
      .request({
        path: '/wscsalesman/sales-card/isShowSalesCardEntry.json',
      })
      .then((res) => {
        if (!res.value) {
          app.globalData.bi_card_sales = null;
          app.globalData.bi_card_sales_from = null;
          return;
        }
        app.globalData.bi_card_sales = salesId;
        app.globalData.bi_card_sales_from = 'new_bi_card';
      });
  },

  onGetUserInfo() {
    const { options = {} } = getCurrentPage();
    const { from_source: fromSource, from_params: fromParams, sl } = options;
    initSalesman.call(this, {
      query: {
        ...options,
        st: 29,
        gst: EShoppingGuideBindSourceType.TINY_PAGE,
      },
    });
    wx.navigateTo({
      url: `/packages/business-card/detail/index?kdt_id=${app.getKdtId()}&from_params=${fromParams}&from_source=${fromSource}&sl=${sl}`,
    });
  },

  onGetPhoneNumber() {
    const {
      getPhoneInfo,
      fromSource,
      fromParams,
      sales: { sales_id: salesId },
    } = this.data;
    this.injectTrackData({ fromSource, fromParams, salesId });
    if (getPhoneInfo === GET_PHONE) {
      this.createMember();
    } else {
      this.createCustomer();
    }
  },

  createCustomer() {
    const { fromSource, fromParams, sl } = this.data;
    promisifiedCarmen({
      api: 'youzan.retail.sales.card.customer/1.0.0/createbyuid',
      method: 'POST',
      data: {
        sales_id: this.data.sales.sales_id,
        online_kdt_id: this.data.sales.online_kdt_id || app.getKdtId(),
        store_kdt_id: this.data.sales.store_kdt_id || app.getKdtId(),
        bind_source_type: 29, // 鉴于绑定来源已经被传乱了，商业化拆分项目中重新指定枚举
      },
    });
    wx.navigateTo({
      url: `/packages/business-card/detail/index?from_params=${fromParams}&from_source=${fromSource}&kdt_id=${app.getKdtId()}&sl=${sl}`,
    });
  },

  createMember() {
    const { fromSource, fromParams, sl } = this.data;
    const url = `/packages/business-card/detail/index?from_params=${fromParams}&from_source=${fromSource}&kdt_id=${app.getKdtId()}&sl=${sl}`;
    promisifiedCarmen({
      api: 'com.youzan.retail.sales.vcard.member/1.0.0/create',
      method: 'POST',
      data: {
        sales_id: this.data.sales.sales_id,
        kdt_id: this.data.sales.store_kdt_id || app.getKdtId(),
      },
    })
      .then((data) => {
        if (data) {
          wx.navigateTo({
            url: `${url}&get_phone_info=${GET_PHONE_SUCCESS}`,
          });
          return;
        }
        wx.navigateTo({
          url: `${url}&get_phone_info=${GET_PHONE_FAIL}`,
        });
      })
      .catch(() => {
        wx.navigateTo({
          url: `${url}&get_phone_info=${GET_PHONE_FAIL}`,
        });
      });
  },
});
