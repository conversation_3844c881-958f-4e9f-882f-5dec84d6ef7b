import args from '@youzan/weapp-utils/lib/args';
import WscPage from 'pages/common/wsc-page/index';
import logv3 from 'utils/log/logv3';
import { handleQueryParams } from '../common/utils';

const app = getApp();

WscPage({
  data: {
    src: '',
    sales: {},
    fromSource: '',
    fromParams: '',
  },

  onLoad(query) {
    let sales = {};
    if (query.from_params) {
      sales = handleQueryParams(decodeURIComponent(query.from_params));
    }
    const kdtId = app.getKdtId();
    const params = {
      sl: query.sl,
      from_source: query.from_source,
      from_params: query.from_params,
      kdt_id: kdtId,
      get_phone_info: query.get_phone_info,
    };
    if (query.define_title) {
      params.define_title = query.define_title;
    }

    this.setYZData({
      src: args.add('https://h5.youzan.com/wscump/sales/sales-card', params),
      sales,
      fromParams: query.from_params,
      fromSource: query.from_source,
    });
  },

  onMessage(event) {
    const messages = event.detail.data;
    const shareMessages = messages.filter(
      (message) => message.type === 'ZNB.share'
    );
    this.shareConfig = shareMessages.pop().config;
  },

  onShareAppMessage() {
    return logv3.processShareData({
      title: this.shareConfig.title,
      path: `packages/business-card/auth/index?from_source=${this.data.fromSource}&from_params=${this.data.fromParams}`,
      loggerParams: {
        title: '名片',
        spm: `retailvc.${this.data.sales.sales_id}`,
      },
    });
  },
});
