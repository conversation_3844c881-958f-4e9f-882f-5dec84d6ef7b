import { OWL_PATH_FUNC } from './constant';

export function getNormalGoodsWeappPath(alias) {
  return `/pages/goods/detail/index?alias=${alias}`;
}

export function getOwlWeappPath(owlType, alias) {
  const owlPathFunc = OWL_PATH_FUNC[owlType] || getNormalGoodsWeappPath;
  return owlPathFunc(alias);
}

export function getGoodsWeappPath({
  goodsType, owlType, owlAlias, alias
}) {
  if (+goodsType === 31) {
    return getOwlWeappPath(+owlType, owlAlias);
  }
  return getNormalGoodsWeappPath(alias);
}
