export const OWL_TYPE = {
  COLUMN: 1,
  CONTENT: 2,
  VIP_BENEFIT: 3,
  LIVE: 4,
  PUNCH: 5,
  NEW_PUNCH: 9,
};

export const OWL_PATH_FUNC = {
  [OWL_TYPE.COLUMN]: alias => `/packages/paidcontent/column/index?alias=${alias}`,
  [OWL_TYPE.CONTENT]: alias => `/packages/paidcontent/content/index?alias=${alias}`,
  [OWL_TYPE.VIP_BENEFIT]: alias => `/packages/paidcontent/rights/index?alias=${alias}`,
  [OWL_TYPE.LIVE]: alias => `/packages/paidcontent/live/index?alias=${alias}`,
  [OWL_TYPE.PUNCH]: alias => `/packages/punch/activity/index?alias=${alias}`,
  [OWL_TYPE.NEW_PUNCH]: alias => `/packages/new-punch/introduction/index?alias=${alias}`
};

// 角色类型 2—外部销售员  1—内部销售员
const EXTERNAL_SALESMAN = 2;
const INTERNAL_SALESMAN = 1;

export const SALESMAN_TYPE = {
  EXTERNAL_SALESMAN,
  INTERNAL_SALESMAN,
};

export const SALESMAN_TYPE_TEXT = {
  [EXTERNAL_SALESMAN]: '外部销售员',
  [INTERNAL_SALESMAN]: '内部销售员',
};
