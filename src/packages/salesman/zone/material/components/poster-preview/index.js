/* eslint-disable @youzan/koko/no-async-await */
// import WscComponent from 'pages/common/wsc-component';
import { VanxComponent } from 'pages/common/wsc-component/index';
import { getPosterSetting, createPoster } from '../../../api';
import queryString from '@youzan/utils/url/queryString';
import { saveImage } from '../../utils';

const app = getApp();

VanxComponent({
  properties: {
    images: {
      type: Array,
      value: [],
    },
    buyer: {
      type: Object,
      default: () => ({}),
    },
    show: {
      type: Boolean,
      value: false,
    },
    codeUrl: {
      type: Object,
      value: {},
    },
  },
  data: {
    curIndex: 0,
    selectShow: false,
    loading: true,
    saving: false,
    handleImages: [],
  },
  watch: {
    'data.show': function (val) {
      if (val) {
        const { images = [] } = this.data;
        const handleImages = images.map((item) => ({
          imgUrl: item,
          posterUrl: '',
        }));
        const selectShow = images.length > 1;
        this.setYZData({
          selectShow,
          handleImages,
        });
      } else {
        this.setYZData({
          selectShow: false,
          saving: false,
          handleImages: [],
        });
      }
    },
  },
  methods: {
    handleSave() {
      const { handleImages, curIndex } = this.data;
      this.setYZData({
        saving: true,
      });
      saveImage([handleImages[curIndex].posterUrl])
        .then(() => {
          this.triggerEvent('save-end');
        })
        .finally(() => {
          this.setYZData({
            saving: false,
          });
        });
    },
    handleClose() {
      // 一定要先关popup，不然会锁定scroll，不要改顺序
      this.setYZData({
        selectShow: false,
        curIndex: 0,
      });
      this.triggerEvent('cancel');
    },
    handleSwitch(e) {
      const { index } = e.detail;
      this.setYZData({
        curIndex: index,
      });
    },
    async handlePreLoad(e) {
      const kdtId = app.getKdtId();
      const { width, height } = e.detail;
      const { handleImages, curIndex, buyer, codeUrl } = this.data;

      this.setYZData(
        {
          loading: true,
        },
        { immediate: true }
      );

      let url = '';
      let qrcode = '';
      const { weappPath } = codeUrl;
      let { supplyQuery } = codeUrl;

      if (!supplyQuery.alias) {
        try {
          const data = await getPosterSetting();
          if (data) {
            url = data.qrcode;
            qrcode = '';

            // 1:公众号 2：店铺首页，如果是公众号情况，返回的直接是微信的二维码图片流。不需要做二维码处理
            if (data.poster_type === 1) {
              url = '';
              qrcode = data.qrcode;
            }

            if (url) {
              const queryParams = url.split('?')[1] || {};
              supplyQuery = queryString.parse(queryParams);
            }
          }
          // 上一次生成商品海报，再生成店铺海报，会残留alias
          delete supplyQuery.alias;
        } catch (e) {}
      }

      // qrcode > page&scene（小程序环境） or url（h5环境）
      createPoster({
        kdtId,
        type: 'material',
        url,
        page: 'pages/common/blank-page/index',
        scene: JSON.stringify({
          ...supplyQuery,
          page: weappPath,
          kdtId,
          guestKdtId: kdtId,
        }),
        qrcode,
        imgUrl: `${handleImages[curIndex].imgUrl}!middle.png`,
        nickName: buyer.nickname,
        avatar: buyer.avatar,
        width,
        height,
      })
        .then(({ value }) => {
          const newImages = handleImages.slice();
          newImages[curIndex].posterUrl = value;

          this.setYZData({
            handleImages: newImages,
          });
        })
        .catch((msg) => {
          console.log(msg);
        });
    },
    handlePosterLoad() {
      wx.nextTick(() => {
        this.setYZData(
          {
            loading: false,
          },
          { immediate: true }
        );
      });
    },
  },
});
