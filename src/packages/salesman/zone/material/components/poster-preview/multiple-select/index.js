import WscComponent from 'pages/common/wsc-component';

WscComponent({
  properties: {
    imgList: {
      type: Array,
      value: [],
    },
    loading: {
      type: Boolean,
      value: false,
    },
    saving: {
      type: Boolean,
      default: false,
    },
    show: {
      type: Boolean,
      value: false,
    },
    curIndex: {
      type: Number,
      value: 0,
    },
  },
  data: {
    scrollPercent: 0,
  },
  methods: {
    cancel() {
      this.triggerEvent('cancel');
    },
    onSwitch(e) {
      const { index } = e.currentTarget.dataset;
      this.triggerEvent('switch', { index });
    },
    handleScroll(e) {
      const { scrollLeft, scrollWidth } = e.detail;
      this.setYZData({
        scrollPercent: (scrollLeft / scrollWidth) * 100,
      });
    },
    saveImage() {
      const { loading, saving } = this.data;
      if (loading || saving) {
        return;
      }
      this.triggerEvent('save');
    },
  },
});
