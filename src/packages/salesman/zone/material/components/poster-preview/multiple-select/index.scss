.multiple-select {
  .title {
    margin: 16px auto;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    text-align: center;
    color: #323233;
  }

  .img-list {
    overflow-x: scroll;
    white-space: nowrap;
  }

  .img-list::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }

  .img-wrap {
    display: inline-block;
    width: 100px;
    height: 100px;
    border-radius: 2px;
    position: relative;
    margin-left: 16px;

    image {
      width: 100%;
      height: 100%;
      display: block;
    }
    .select-tag {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 15px;
      height: 15px;
      border-radius: 100%;
      background: #fff;
      box-sizing: border-box;
      border: 1px solid #7d7e80;
    }
    .selected {
      background: #2da641;
      color: #fff;
      border: none;
      font-size: 8px;
      line-height: 16px;
      text-align: center;
    }
  }

  .scroll-bar {
    width: 40px;
    height: 5px;
    background: #ebedf0;
    border-radius: 2.5px;
    margin: 16px auto;
    text-align: center;
    position: relative;
    .scroll-block {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      background: #c8c9cc;
      border-radius: 2.5px;
      width: 16px;
    }
  }
  .button-group {
    margin: 4px 16px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .btn-item {
      width: 162px;
      height: 42px;
      line-height: 42px;
      font-weight: 500;
      font-size: 16px;
      text-align: center;
      color: #666;
    }
    .btn-cancel {
      box-sizing: border-box;
      border: 1px solid rgba(153, 153, 153, 0.6);
      border-radius: 22px;
    }
    .btn-save {
      background: linear-gradient(135deg, #ffaa1e 0%, #ff720d 100%);
      border-radius: 22px;
      color: #fff;
    }
  }
}
