<view>
  <van-popup
    show="{{show}}"
    overlay="{{false}}"
    position="bottom"
    round="{{true}}"
    safe-area-inset-bottom="{{true}}"
    closeable="{{true}}"
    lock-scroll="{{false}}"
    z-index="{{22}}"
    bind:close="cancel"
  >
    <view class="multiple-select">
      <view class="title">选择图片，生成对应海报</view>
      <scroll-view scroll-x="{{true}}" class="img-list" id="imgList" bindscroll="handleScroll" wx:if="{{show}}">
        <view wx:for="{{imgList}}" wx:for-item="img" wx:for-index="index"  class="img-wrap">
          <view wx:if="{{curIndex === index}}" class="select-tag selected">
            <van-icon name="success" />
          </view>
          <view wx:else class="select-tag"></view>
          <image src="{{img}}" bind:tap="onSwitch" data-index="{{index}}" />
        </view>
      </scroll-view>
      <view class="scroll-bar">
        <view class="scroll-block" style="left: {{scrollPercent}}%!important;"></view>
      </view>
      <view class="button-group">
        <view class="btn-item btn-cancel" bind:tap="cancel">取消</view>
        <view class="btn-item btn-save" bind:tap="saveImage">
          <van-loading size="16px" color="#fff" wx:if="{{loading || saving}}">
            <text wx:if="{{loading}}" style="color: #fff">生成中</text>
            <text wx:if="{{saving}}" style="color: #fff">保存中</text>
          </van-loading>
          <text wx:else>保存图片</text>
        </view>
      </view>
    </view>
  </van-popup>
</view>