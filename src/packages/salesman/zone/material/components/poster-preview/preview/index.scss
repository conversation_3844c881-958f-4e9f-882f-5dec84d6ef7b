.preview-wrap {
  padding: 80px 10px;
  overflow-x: hidden;
}
.relative-wrap {
  position: relative;
  background-color: #fff;
}
.padding-wrap {
  width: 292px;
  box-sizing: border-box;
  padding: 20px;
}
.preview-close {
  position: absolute;
  top: -40px;
  right: -12px;
}
.preview-tip {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -42px;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  text-align: center;
  color: #fff;
}

.poster-wrap {
  overflow-x: scroll;
}
.real-poster {
  width: 302px;
}
.poster-img {
  width: 100%;
}
.poster-info {
  display: flex;
  align-items: center;
  margin-top: 20px;
  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 100%;
  }
  .user-text {
    margin-left: 8px;
    .user-nickname {
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      color: #323233;
    }

    .text {
      margin-top: 2px;
      font-weight: 400;
      font-size: 10px;
      line-height: 14px;
      color: #969799;
      transform: scale(0.85);
      transform-origin: left;
      width: 160px;
    }
  }
  .user-qrcode {
    width: 62px;
    height: 62px;
    background-color: #f6f7f9;
  }
}

.btn-item {
  width: 162px;
  height: 42px;
  line-height: 42px;
  font-weight: 500;
  font-size: 16px;
  text-align: center;
  color: #666;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -78px;
}

.btn-save {
  background: linear-gradient(135deg, #ffaa1e 0%, #ff720d 100%);
  border-radius: 22px;
  color: #fff;
}
