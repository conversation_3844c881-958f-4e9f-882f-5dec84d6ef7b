<view>
  <van-popup
    show="{{show}}"
    overlay="{{false}}"
    lock-scroll="{{false}}"
    z-index="{{21}}"
    custom-style="background-color: transparent;top: {{previewTop}}px;"
  >
    <view class="preview-wrap">
      <view class="relative-wrap">
        <van-icon
          wx:if="{{showClose}}"
          name="close"
          class="preview-close"
          color="#fff"
          size="30"
          bind:click="onCancel"
        />
        <view class="poster-wrap" style="max-height: {{previewHeight}}px;">
          <view class="padding-wrap" wx:if="{{!posterUrl}}">
            <image
              mode="widthFix"
              class="poster-img"
              src="{{imgUrl}}?imageView2/2/w/355"
              id="posterImg"
              bindload="handleImgChange"
            />
            <view class="poster-info">
              <image class="user-avatar" src="{{userInfo.avatar}}" />
              <view class="user-text">
                <view class="user-nickname">{{ userInfo.nickname }}</view>
                <view class="text">"这个真不错，推荐你也来看看"</view>
              </view>
              <view class="user-qrcode"></view>
            </view>
          </view>
          <image wx:else src="{{posterUrl}}" mode="widthFix" class="real-poster" bindload="handlePosterLoad"/>
        </view>
        <view class="btn-item btn-save" wx:if="{{showClose}}" bind:tap="saveImage">
          <van-loading size="16px" color="#fff" wx:if="{{loading || saving}}">
            <text wx:if="{{loading}}" style="color: #fff">生成中</text>
            <text wx:if="{{saving}}" style="color: #fff">保存中</text>
          </van-loading>
          <text wx:else>保存图片</text>
        </view>
      </view>
    </view>
  </van-popup>
</view>