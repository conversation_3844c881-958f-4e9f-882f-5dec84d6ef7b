// import WscComponent from 'pages/common/wsc-component';
import { VanxComponent } from 'pages/common/wsc-component/index';

const app = getApp();

VanxComponent({
  properties: {
    show: {
      type: Boolean,
      default: false,
    },
    showClose: {
      type: Boolean,
      default: false,
    },
    userInfo: {
      type: Object,
      default: () => ({}),
    },
    imgList: {
      type: Array,
      default: () => [],
    },
    curIndex: {
      type: Number,
      default: 0,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    saving: {
      type: Boolean,
      default: false,
    },
    positionTop: {
      type: Number,
      default: 0,
    },
    maxHeight: {
      type: Number,
      default: 0,
    },
  },
  data: {
    fixHeight: 0,
    imgUrl: '',
    posterUrl: '',
    previewHeight: '',
    previewTop: '',
  },
  watch: {
    'data.imgList': function () {
      this.getCurImageUrl();
    },
    'data.curIndex': function () {
      this.getCurImageUrl();
    },
  },
  methods: {
    getCurImageUrl() {
      const { curIndex, imgList } = this.data;
      this.setYZData({
        imgUrl: imgList[curIndex]?.imgUrl,
        posterUrl: imgList[curIndex]?.posterUrl,
      });
    },
    handleImgChange(e) {
      const { width, height } = e.detail;
      const ratio = 355 / width;
      const realHeight = parseInt(height * ratio + 120, 10);
      this.triggerEvent('pre-load', {
        index: this.curIndex,
        width: 375,
        height: realHeight,
      });
      this.customStyle(realHeight);
    },
    handlePosterLoad() {
      this.triggerEvent('poster-load');
    },
    onCancel() {
      this.triggerEvent('cancel');
    },
    customStyle(imgHeight) {
      const { showClose } = this.data;
      const {
        safeArea: { height: safeAreaHeight },
      } = app.getSystemInfoSync();
      // 底部图片多选高度
      const selectHeight = showClose ? 0 : 236;
      // 预览图距离顶部高度占剩余高度百分比
      // 0.35 为偏上（单图偏上），0.5 为居中
      const toRemainRate = showClose ? 0.35 : 0.5;
      // 预览图可用的最大高度
      const maxHeight = parseInt((safeAreaHeight - selectHeight) * 0.8, 10);
      const finalHeight = imgHeight > maxHeight ? maxHeight : imgHeight;
      // finalHeight * 0.5 是校正css transform的偏移
      this.setYZData({
        previewHeight: finalHeight,
        previewTop: parseInt(
          (safeAreaHeight - selectHeight - finalHeight) * toRemainRate +
            finalHeight * 0.5,
          10
        ),
      });
    },
    saveImage() {
      const { loading, saving } = this.data;
      if (loading || saving) {
        return;
      }
      this.triggerEvent('save');
    },
  },
});
