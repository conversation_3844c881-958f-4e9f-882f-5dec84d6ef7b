.material-list {
  &-empty {
    width: 160px;
    margin: 120px auto 0;
    font-size: 14px;
    color: #999;
    padding-top: 176px;
    background: url('https://b.yzcdn.cn/public_files/580ff7ee1659834e8d6419caf388b7a4.png')
      no-repeat center center;
    background-size: 100% 160px;
    text-align: center;
  }

  &-content {
    padding: 0 16px;
    &--loading {
      margin-top: 10px;
      text-align: center;
    }
  }

  .feed-item {
    margin-top: 16px;
  }

  .item-class {
    background: #f7f8fa;
  }

  .action-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;

    &--text {
      font-size: 12px;
      color: #999;
      height: 18px;
      line-height: 18px;
    }

    &--right {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 20px;
      background-color: #f2f3f5;
      position: relative;
    }

    .round {
      background-color: #586b93;
      width: 4px;
      height: 4px;
      border-radius: 50%;

      &:first-of-type {
        margin-right: 4px;
      }
    }

    .action-area-common {
      position: absolute;
      right: 52px;
      background-color: #323233;
      height: 40px;
      line-height: 40px;
      color: #fff;
      font-size: 13px;
      display: flex;
      align-items: center;
      border-radius: 2px;

      &--common {
        padding: 0 10px;
        display: inline-flex;
        align-items: center;
        width: 82px;
      }

      &--publish {
        width: 70px;
      }

      &--poster {
        padding: 0;
      }

      &--save,
      &--publish,
      &--poster {
        &__img {
          width: 18px;
          height: 18px;
          padding-right: 4px;
        }
      }

      &--line {
        width: 1px;
        height: 16px;
        background-color: #000;
      }

      .triangle {
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-left: 7px solid #323233;
        position: absolute;
        right: -12px;
        top: 13px;
      }
    }

    .hide-action-area {
      display: none;
    }
  }
}

.goods-title {
  width: 72vw;
}

.action-area-moment {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-area-moment__img {
  display: block;
  width: 64px;
  height: 26px;
}
