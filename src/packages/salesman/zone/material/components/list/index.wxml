<view class="material-list">
	<view wx:if="{{ !materialList.length }}" class="material-list-empty">暂无素材，快去添加吧！
	</view>
	<view wx:else class="material-list-content">
		<feed-item
		  wx:for="{{ materialList }}"
		  wx:for-item="material"
		  wx:key="index"
		  title="{{ material.formatTitle }}"
		  text="{{ material.displayText }}"
		  item="{{ material.item }}"
		  image-list="{{ material.formatImageList }}"
		  video="{{ material.videoData }}"
		  custom-class="feed-item"
		  item-custom-class="item-class"
		  goods-title-class="goods-title"
			link-material="{{ material.linkMaterial }}"
			material-type="{{ material.materialType }}"
			material-id="{{ material.id }}"
			link-url="{{material.linkUrl }}"
			note-type="{{material.noteType}}"
		  bind:get-src="handleGetSrc"
		  bind:preview="handleTriggerHide"
			logger-type="salesMtrl"
		>
			<view slot="bottom" class="action-area van-hairline--bottom">
				<view class="action-area--text">
					分享 {{ material.formatUsedNum }}
				</view>
				<view
				 class="action-area--right"
				 data-material="{{ material }}"
				 data-index="{{ index }}"
				 catchtap="toggleAction"
				 wx:if="{{canPublish || (!material.materialType && !!material.formatImageList.length)}}"
				>
					<view class="round" />
					<view class="round" />
					<view class="action-area-common {{ material.showAction ? 'show-action-area' : 'hide-action-area' }}">
						<view class="action-area-common--save action-area-common--common" data-material="{{ material }}" bindtap="handleSave">
							<image class="action-area-common--save__img" src="https://b.yzcdn.cn/public_files/86049adb27f7d7ab17cb077e798883ad.png" />
							发朋友圈
						</view>
						<view class="action-area-common--line" wx:if="{{canPublish}}"/>
						<view class="action-area-common--publish action-area-common--common" wx:if="{{canPublish}}" data-material="{{ material }}" bindtap="handlePublish">
							<image class="action-area-common--publish__img" src="https://b.yzcdn.cn/public_files/9b48f042817348cc8131a7dac9155fca.png" />
							发动态
						</view>
						<view class="action-area-common--line" />
						<!-- 仅支持图片类型 -->
						<user-authorize
							wx:if="{{!material.materialType && !!material.formatImageList.length}}"
							authTypeList="{{ ['nicknameAndAvatar'] }}"
							bindgetuserinfo="onGetUserInfo"
							bind:next="handlePoster"
							style="height:40px;width:100px;"
							data-material="{{ material }}">
							<view class="action-area-common--poster action-area-common--common">
								<image class="action-area-common--poster__img" src="https://img01.yzcdn.cn/upload_files/2023/04/25/FpVJxMBTbRz_EmfZ9aUZlUHsgR1L.png" />
								生成海报
							</view>
						</user-authorize>
						<view class="triangle" />
					</view>
				</view>
				<view wx:else class="action-area-moment" >
					<image class="action-area-moment__img" data-material="{{ material }}" bindtap="handleSave" src="https://b.yzcdn.cn/public_files/cf8741e111343a2491b730291cbd48f3.png" />
				</view>
		</feed-item>
		<view wx:if="{{ loading && !finished }}" class="material-list-content--loading">
			<van-loading type="spinner" />
		</view>
	</view>
</view>

<wxs src="../../../helpers/wx.wxs" module="utils"></wxs>