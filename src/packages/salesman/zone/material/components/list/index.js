import navigate from '@/helpers/navigate';
import WscComponent from 'pages/common/wsc-component';

const app = getApp();

WscComponent({
  properties: {
    materialList: {
      type: Array,
      value: [],
    },
    loading: {
      type: Boolean,
      value: false,
    },
    finished: {
      type: Boolean,
      value: false,
    },
    canPublish: {
      type: Boolean,
      value: false,
    },
  },

  methods: {
    handlePublish(e) {
      const { material } = e.currentTarget.dataset;
      const { id, kdtId } = material;
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'click_sendstory',
          en: '点击发布到动态',
          si: app.getKdtId(),
          params: { materialId: id },
        });
      navigate.navigate({
        url: `/packages/salesman/zone/editor/index?materialId=${id}&materialKdtId=${kdtId}`,
      });
    },
    toggleAction(e) {
      const { material, index } = e.currentTarget.dataset;
      this.triggerEvent('toggle-action', { material, index });
    },
    handleSave(e) {
      const { material } = e.target.dataset;
      this.triggerEvent('handle-save', { material });
    },

    handleGetSrc(e) {
      this.triggerEvent('get-src', e.detail);
    },

    handleTriggerHide() {
      this.triggerEvent('preview');
    },

    handlePoster(e) {
      const { material } = e.target.dataset;
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'click_create_poster',
          en: '点击生成海报',
          si: app.getKdtId(),
          params: { materialId: material.id },
        });
      this.triggerEvent('show-poster', { buyer: e.detail, material });
      // 只需要复制文本，不需要保存图片和视频
      this.triggerEvent('handle-save', {
        material: {
          ...material,
          formatImageList: [],
          videoData: { video: {} },
        },
      });
    },
  },
});
