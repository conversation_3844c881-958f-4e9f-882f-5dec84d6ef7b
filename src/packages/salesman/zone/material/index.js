import WscPage from 'pages/common/wsc-page';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import debounce from '@youzan/weapp-utils/lib/debounce';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import get from '@youzan/weapp-utils/lib/get';
import Toast from '@vant/weapp/dist/toast/toast';
import args from '@youzan/weapp-utils/lib/args';

import {
  getMaterialList,
  checkMomentsAccessAndGetBaseInfo,
  getShortUrl,
  getPersonalAbility,
} from '../api';
import {
  materialTabs,
  PromoterRoleType,
  FeatureMaterialType,
  NoteMaterialType,
} from '../common/constant';
import {
  getFeatureImg,
  getShopNoteImg,
  currentUserCanOperate,
  getMaterialBannerId,
} from '../common/utils';
import { getLinkMaterialUrl } from '../common';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';

import { getGoodsWeappPath } from '../../common/url';
import { getSalesmanParamsObject } from '@youzan/salesman-params-handler';
import { saveImage } from './utils';

const app = getApp();
const PAGE_SIZE = 5; // 低端手机请求过多会卡死。。（可能是因为素材含视频、图片）
const kdtId = app.getKdtId();

WscPage({
  _triggerHide: false, // 用于解决preivew触发onHide onShow的问题

  $running: false, // 当前是否有展开的toggle-action

  data: {
    showEmpty: false,
    showSaveTip: false,
    keyword: '',
    page: 1,
    finished: false,
    loading: false,
    videoSrcs: [],
    materialList: [],
    active: 0,
    canOperate: false,
    materialTabs,
    sl: '',
    canPublish: false,
    posterShow: false,
    imageList: [],
    buyer: {},
    codeUrl: {},
  },

  onShareAppMessage() {
    const { bannerImage } = this.data;
    return {
      title: '最新最全的图文引流素材',
      imageUrl: bannerImage,
      path: `/packages/salesman/zone/material/index?kdtId=${app.getKdtId()}`,
    };
  },

  onShow() {
    app.logger &&
      app.logger.log({
        et: 'display',
        ei: 'enterpage',
        en: '浏览页面',
        si: app.getKdtId(),
        params: { materialId: 'str' },
      });

    if (app.globalData.zoneDraft && !this._triggerHide) {
      if (app.globalData.isFromSelectMaterial) {
        // 点击选择素材过来的就直接保存草稿
        this.saveDraft();
        return;
      }

      Dialog.alert({
        message: '是否保留本次编辑',
        showCancelButton: true,
        confirmButtonText: '保留',
        cancelButtonText: '不保留',
      })
        .then(() => {
          this.saveDraft();
        })
        .catch(() => {
          app.globalData.zoneDraft = null;
          wx.setStorage({
            key: 'moments',
            data: null,
          });
        });
    }
  },

  onLoad() {
    this.init();
  },

  init() {
    // eslint-disable-next-line prefer-const
    let { sl = '', sls = '' } = this.__query__;
    sl = sl || sls;
    const kdtId = app.getKdtId();
    Toast.loading('加载中...');
    checkMomentsAccessAndGetBaseInfo({ sl })
      .then((res) => {
        Toast.clear();
        const { canEdit, accountType, sl: newSl } = res;
        const canOperate = currentUserCanOperate({
          canEdit,
          accountType,
        });
        this.setYZData({
          canOperate,
          sl: newSl,
        });
        if (canOperate) {
          this.fetchList();
        }
      })
      .catch((err) => {
        Toast.fail(err.msg || '网络错误');
      });
    if (!kdtId) return;
    getPersonalAbility(kdtId).then((res) => {
      this.setYZData({
        canPublish: res.valid,
      });
    });
  },

  fetchList() {
    this.getList();
    this.handleChange = debounce((e) => {
      const value = e.detail;
      this.setYZData({
        page: 1,
        finished: false,
        keyword: value,
        materialList: [],
      });
      this.getList();
    }, 300);
  },

  saveDraft() {
    const {
      goods,
      location,
      preImages,
      videoData,
      textContent,
      contentLength,
      linkInfo,
    } = app.globalData.zoneDraft;
    wx.setStorage({
      key: 'moments',
      data: {
        goods,
        location,
        preImages,
        videoData,
        textContent,
        contentLength,
        linkInfo,
      },
    });
  },

  handleGetSrc(e) {
    this.data.videoSrcs.push(e.detail);
  },

  handleTriggerHide() {
    this._triggerHide = true;
  },

  getList() {
    const { keyword, materialList, finished, page, active, sl } = this.data;
    const { materialTypes } = materialTabs[active];
    const { itemId } = this.__query__;
    const params = {
      keyword,
      page,
      pageSize: PAGE_SIZE,
      roleType: PromoterRoleType,
      materialTypes,
      itemId,
    };
    this.setYZData({
      loading: true,
    });
    if (finished) {
      return;
    }
    getMaterialList(params)
      .then((data) => {
        // eslint-disable-next-line prefer-const
        let { list, hasNext } = data;
        if (!hasNext) {
          this.setYZData({
            finished: true,
          });
        }
        list = list.map((item) => {
          const {
            id,
            itemMaterialDTO,
            imageMaterialDTOs = [],
            videoMaterialDTO = {},
            featureMaterial = {},
            shopNoteMaterial = {},
            usedNum,
            kdtId,
            materialType,
            name,
            textContent = '',
            noteType,
            linkUrl,
          } = item;
          let formatGoodsWeappUrl;
          if (itemMaterialDTO) {
            const { join, alias, goodsType, owlAlias, owlType } =
              itemMaterialDTO || {};
            formatGoodsWeappUrl = getGoodsWeappPath({
              alias,
              goodsType,
              owlAlias,
              owlType,
            });
            if (join) {
              formatGoodsWeappUrl = addSalesmanParams({
                url: formatGoodsWeappUrl,
                sls: sl,
              });
            }
          }
          return {
            id,
            formatTitle: this.handleTitle({
              materialType,
              featureMaterial,
              shopNoteMaterial,
              name,
            }),
            ...this.handleText({
              materialType,
              shopNoteMaterial,
              textContent,
            }),
            videoData: {
              video: {
                video_id: videoMaterialDTO.id,
                cover_url: videoMaterialDTO.coverUrl,
                coverHeight: videoMaterialDTO.coverHeight,
                coverWidth: videoMaterialDTO.coverWidth,
              },
            },
            item: itemMaterialDTO && {
              goodsInfo: {
                ...itemMaterialDTO,
                title: itemMaterialDTO.itemName,
                image: itemMaterialDTO.pictureUrl,
                formatGoodsWeappUrl,
              },
            },
            kdtId,
            showAction: false,
            formatUsedNum: this.handleNumberFormat(usedNum),
            formatImageList: this.handleImg({
              materialType,
              featureMaterial,
              shopNoteMaterial,
              imageMaterialDTOs,
            }),
            linkMaterial: this.handleLinkMaterial({
              featureMaterial,
              shopNoteMaterial,
              materialType,
            }),
            featureMaterial,
            shopNoteMaterial,
            materialType,
            noteType,
            linkUrl,
          };
        });
        let showEmpty = false;
        if (!materialList.concat(list).length) {
          showEmpty = true;
        }
        this.setYZData({
          materialList: materialList.concat(list),
          page: page + 1,
          loading: false,
          showEmpty,
        });
      })
      .catch(() => {
        wx.showToast({
          title: '网络错误',
          icon: 'none',
        });
      });
  },

  handleNumberFormat(val) {
    val = val || 0;
    if (val <= 9999) {
      return val;
    }
    const newVal = val / 10000;
    if (newVal < 1.1) {
      return '1万';
    }
    return `${newVal.toFixed(1)}万`;
  },

  handleClose() {
    this.setYZData({
      showSaveTip: false,
    });
    this.handlePreviewClose();
  },

  setClipboardData(material) {
    let itemUrl = '';
    const { sl } = this.data;
    const linkMaterial = this.handleLinkMaterial(material);
    let linkUrl = '';
    const { id, formatText = '' } = material;
    const kdtId = app.getKdtId();

    if (get(material, 'item.goodsInfo')) {
      const params = {
        banner_id: getMaterialBannerId({ materialId: id, spm: 'salesMtrl' }),
        sub_kdt_id: kdtId,
      };
      itemUrl = args.add(material.item.goodsInfo.itemUrl, params);
      if (sl) {
        itemUrl = addSalesmanParams({ url: itemUrl, sl });
      }
    }
    if (linkMaterial) {
      const { alias } = linkMaterial;
      const { url } = getLinkMaterialUrl({
        alias,
        materialType: material.materialType,
        kdtId,
        noteType: material.noteType,
        linkUrl: material.linkUrl,
      });
      linkUrl = url;
      if (sl) {
        linkUrl = addSalesmanParams({ url: linkUrl, sl });
      }
    }
    const clipboardDataUrl = itemUrl || linkUrl;
    this.handleUrlToShort(clipboardDataUrl).then(({ res }) => {
      const url = res || clipboardDataUrl;
      const br = formatText && url ? '\n' : '';
      const clipboardData = `${formatText}${br}${url}`;
      wx.setClipboardData({
        data: clipboardData,
      });

      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'click_save_story',
          en: '点击保存到本地',
          si: app.getKdtId(),
          params: { materialId: material.id, short_url: url },
        });
    });
  },

  handleUrlToShort(url) {
    if (url) {
      return getShortUrl({ url }).catch(() => ({ res: url }));
    }
    return Promise.resolve({ res: '' });
  },

  saveImage(imageList) {
    saveImage(imageList).then(() => {
      this.setYZData({
        showSaveTip: true,
      });
    });
  },

  saveVideo(url) {
    return new Promise((resolve) => {
      wx.showLoading({
        title: '视频下载中',
      });
      app.downloadFile({
        url,
        success: (res) => {
          const { tempFilePath } = res;
          wx.saveVideoToPhotosAlbum({
            filePath: tempFilePath,
            success: () => {
              this.setYZData({
                showSaveTip: true,
              });
              resolve();
            },
            fail: () => {
              Toast({
                message: '保存视频到系统相册失败',
                duration: 3000,
              });
              resolve();
            },
          });
        },
        fail: (err) => {
          let message = get(err, 'errMsg', '下载视频失败');
          if (message === 'downloadFile:fail exceed max file size') {
            message = '不支持下载超过50MB的视频';
          }
          if (
            message ===
            'downloadFile:fail createDownloadTask:fail url not in domain list'
          ) {
            message = '该视频暂不支持下载';
          }
          if (message === 'downloadFile:fail url not in domain list') {
            message = '该视频暂不支持下载';
          }
          Toast({
            message,
            duration: 4000,
          });
          resolve();
        },
      });
    });
  },

  handleSaveEnd() {
    this.setYZData({
      showSaveTip: true,
    });
  },
  handleSave(e) {
    const { material } = e.detail;
    const { formatImageList: imageList = [], videoData } = material;
    if (imageList.length > 0) {
      this.saveImage(imageList);
    }
    const { video_id: id = '' } = videoData.video;
    const video = this.data.videoSrcs.find((item) => item.id === id);
    if (video) {
      this.saveVideo(video.src).then(() => {
        this.setClipboardData(material);
      });
    } else {
      this.setClipboardData(material);
    }
  },

  onReachBottom() {
    this.getList();
  },

  toggleAction(e) {
    const { material, index } = e.detail;
    const { showAction } = material;
    const { materialList } = this.data;
    let newMaterialList;
    // 当前显示action
    if (showAction) {
      newMaterialList = materialList.map((item) => ({
        ...item,
        showAction: false,
      }));
      this.setYZData({
        materialList: newMaterialList,
      });
      this.$running = false;
      return;
    }
    // 当前未显示action
    newMaterialList = materialList.map((item, i) => {
      if (i === index) {
        return {
          ...item,
          showAction: true,
        };
      }
      return {
        ...item,
        showAction: false,
      };
    });
    this.setYZData({
      materialList: newMaterialList,
    });
    this.$running = true;
  },

  handleClearAction() {
    const materialList = this.data.materialList.map((item) => ({
      ...item,
      showAction: false,
    }));
    this.setYZData({
      materialList,
    });
  },

  onPageScroll() {
    if (!this.$running) {
      return;
    }
    this.$running = false;
    this.handleClearAction();
  },

  handleTabChange(e) {
    const { index } = e.detail;
    this.setYZData({
      active: index,
      page: 1,
      finished: false,
      materialList: [],
    });
    this.getList();
  },

  handleTitle({ materialType, featureMaterial, shopNoteMaterial, name }) {
    if (NoteMaterialType === materialType) {
      // eslint-disable-next-line prefer-const
      let { title, description } = shopNoteMaterial;
      if (title) {
        return title;
      }
      title = description.slice(0, 40);
      if (description.length > 40) {
        return `${title}...`;
      }
      return title;
    }
    if (FeatureMaterialType === materialType) {
      return featureMaterial.title;
    }
    return name;
  },

  handleText({ materialType, shopNoteMaterial, textContent = '' }) {
    if (NoteMaterialType === materialType) {
      return shopNoteMaterial.description;
    }
    // 小程序rich-text不识别换行符，需要转换成<br>标签
    return {
      formatText: textContent,
      displayText: textContent ? textContent.replace(/\n/g, '<br>') : '',
    };
  },

  handleImg({
    materialType,
    featureMaterial,
    shopNoteMaterial,
    imageMaterialDTOs,
  }) {
    if (materialType === FeatureMaterialType) {
      return getFeatureImg(featureMaterial).map((item) => cdnImage(item));
    }
    if (materialType === NoteMaterialType) {
      return getShopNoteImg(shopNoteMaterial).map((item) => cdnImage(item));
    }
    return imageMaterialDTOs.map((item) => cdnImage(item.url));
  },

  handleLinkMaterial({ featureMaterial, shopNoteMaterial, materialType }) {
    if (NoteMaterialType === materialType) {
      return shopNoteMaterial;
    }
    if (FeatureMaterialType === materialType) {
      return featureMaterial;
    }
  },

  getCodeUrl(material) {
    const { id } = material;
    const goodsInfo = get(material, 'item.goodsInfo');
    const { sl } = this.data;
    let weappPath = 'pages/home/<USER>/index';
    const supplyQuery = {
      banner_id: getMaterialBannerId({
        materialId: id,
        spm: 'salesMtrl',
      }),
      sub_kdt_id: kdtId,
      ...getSalesmanParamsObject({ kdtId, sl }),
    };
    if (goodsInfo) {
      weappPath = 'packages/goods/detail/index';
      supplyQuery.alias = goodsInfo.alias;
    }
    return { weappPath, supplyQuery };
  },
  handleShowPoster(e) {
    const { material, buyer } = e.detail;
    const { formatImageList: imageList } = material;
    const codeUrl = this.getCodeUrl(material);
    this.setYZData({
      imageList,
      buyer,
      posterShow: true,
      codeUrl,
    });
  },

  handlePreviewClose() {
    this.setYZData({
      posterShow: false,
    });
  },
});
