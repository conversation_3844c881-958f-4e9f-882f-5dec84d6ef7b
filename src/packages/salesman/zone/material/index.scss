.material {
  background: #fff;
  min-height: 100vh;

  &-empty {
    padding-top: 212px;

    &-tip {
      line-height: 20px;
      width: 320px;
      margin: 0 auto;
      font-size: 14px;
      color: #999;
      padding-top: 176px;
      background: url("https://b.yzcdn.cn/public_files/70d5d226b66b39fb9ac3d4ad63f755bb.png") no-repeat center center;
      background-size: 160px 160px;
      text-align: center;
    }
  }

  &-search {
    padding: 10px 16px;
    background: #fff;

    &--input {
      padding: 0 12px !important;
      border-radius: 17px;
    }
  }

  &-tip {
    background: #f2f3f5;

    &--main {
      width: 100%;
      height: 200px;
      background: url("https://img01.yzcdn.cn/image/salesman/tip-m.png") no-repeat center center;
      background-size: 100% 100%;
    }

    &--btn {
      margin-top: 8px;
      font-size: 16px;
      color: #333;
      text-align: center;
      background: #fff;
      width: 100vw;
      height: 50px;
      line-height: 50px;
    }
  }

  &-tabs {
    .van-tab {
      color: #323233;

      &--active {
        color: #FF4444;
      }
    }
  }
}
