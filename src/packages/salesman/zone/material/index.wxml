<page-container
  page-container-class="{{ themeClass }} page-{{ deviceType }}"
>
  <view class="material">
    <view
      wx:if="{{ !canOperate }}"
      class="material-empty"
    >
      <view
        class="material-empty-tip"
      >
        你还不是当前店铺的销售员，可以去申请成为销售员后查看素材。
      </view>
    </view>
    <view wx:else>
      <view class="material-search van-hairline--bottom">
        <van-search
          value="{{ keyword }}"
          background="#f7f8fa"
          placeholder="请输入商品名称，素材标题等关键字"
          placeholder-style="color: #c8c9cc"
          custom-class="material-search--input"
          bind:change="handleChange"
        />
      </view>
      <van-tabs
        active="{{ active }}"
        bind:change="handleTabChange"
        swipe-threshold="5"
        line-width="20"
        color="#FF4444"
        custom-class="material-tabs"
        z-index='100'
      >
        <van-tab
          wx:for="{{ materialTabs }}"
          title="{{ item.title }}"
          wx:key="title"
        />
          <material-list-cpn
            material-list="{{ materialList }}"
            loading="{{ loading }}"
            finished="{{ finished }}"
            loading="{{ loading }}"
            can-publish="{{canPublish}}"
            bind:toggle-action="toggleAction"
            bind:handle-save="handleSave"
            bind:get-src="handleGetSrc"
            bind:preview="handleTriggerHide"
            bind:show-poster="handleShowPoster"
          />
        </van-tab>
      </van-tabs>
    </view>
  </view>
  <poster-preview
    show="{{ posterShow }}"
    images="{{imageList}}"
    buyer="{{buyer}}"
    codeUrl="{{codeUrl}}"
    bind:cancel="handlePreviewClose"
    bind:save-end="handleSaveEnd"
  />
  <van-popup
    show="{{ showSaveTip }}"
    position="bottom"
    bind:close="handleClose"
  >
    <view class="material-tip">
      <view class="material-tip--main"></view>
      <view
        class="material-tip--btn"
        bindtap="handleClose"
      >我知道了</view>
    </view>
  </van-popup>
  <van-dialog id="van-dialog" />
  <van-toast id="van-toast" />
</page-container>
