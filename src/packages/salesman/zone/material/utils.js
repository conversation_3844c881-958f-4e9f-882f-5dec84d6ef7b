const app = getApp();

export const saveImage = (imageList) =>
  new Promise((resolve, reject) => {
    imageList.forEach((url) => {
      app.downloadFile({
        url,
        success: (res) => {
          const { tempFilePath } = res;
          wx.saveImageToPhotosAlbum({
            filePath: tempFilePath,
            success: () => {
              resolve();
            },
            fail: () => {
              app.logger &&
                app.logger.appError({
                  name: 'download fail',
                  message: `download image ${url} fail!`,
                  alert: 'warn',
                  detail: {
                    imageList,
                    currentImage: url,
                  },
                });
              reject();
            },
          });
        },
      });
    });
  });
