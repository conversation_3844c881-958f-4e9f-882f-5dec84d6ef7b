import WscComponent from 'pages/common/wsc-component';
import args from '@youzan/weapp-utils/lib/args';
import navigate from 'shared/utils/navigate';
import getApp from 'shared/utils/get-safe-app';
import theme from 'shared/common/components/theme-view/theme';
import { isNewIphone } from 'shared/utils/browser/device-type';
import {
  URL_MAP, ICON_MAP, NAME_MAP, NAVIGATE_MAP
} from './config';

WscComponent({
  externalClasses: ['custom-class'],

  properties: {
    activeIndex: {
      type: Number,
      value: 0,
    },
    sl: String,
    extraQuery: String,
    hideTab: String
  },

  data: {
    navs: [],
    isIphoneX: false,
  },

  lifetimes: {
    ready() {
      theme.getThemeColor('general')
        .then(color => {
          this._generateNav(color);
        });

      const isIphoneX = isNewIphone();
      if (isIphoneX) {
        this.setYZData({
          isIphoneX: true
        });
      }
    },
  },

  methods: {
    _handleIconClick(e) {
      const { type, active } = e.currentTarget.dataset.item;
      if (active) return;
      const isRetailApp = getApp().globalData.isRetailApp;
      const _type = isRetailApp && type === 'my' ? 'retail-my' : type;
      const navigateType = NAVIGATE_MAP[_type];
      let urlPath = URL_MAP[_type];
      // 硬编码了。
      if (type === 'businessCard') {
        urlPath = args.add(urlPath, {
          from_source: 'retail_sales',
          from_params: this.data.extraQuery,
        });
      }
      navigate[navigateType]({
        url: urlPath,
      });
    },

    _generateNav(activeColor) {
      const { activeIndex } = this.data;
      const navs = Object.keys(ICON_MAP).map((key, index) => {
        const icon = ICON_MAP[key];
        const active = activeIndex === index;
        return {
          type: key,
          name: NAME_MAP[key],
          icon: active ? icon.active : icon.default,
          color: active ? activeColor : 'inherit',
          fontWeight: active ? 'bold' : 'normal',
          active,
        };
      });
      this.setYZData({
        navs,
      });
    },
  },
});
