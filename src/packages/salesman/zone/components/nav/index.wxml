<view class="nav">
  <view
    class="nav-content {{ isIphoneX ? 'iphonx' : '' }}"
  > 
    <block wx:for="{{ navs }}" wx:key="type">
      <view
        wx:if="{{ item.type !== hideTab }}"
        class="nav-item"
        data-item="{{ item }}"
        bind:tap="_handleIconClick"
      >
        <custom-icon
          name="{{ item.icon }}"
          color="{{ item.color }}"
          size="24px"
        />
        <view
          class="nav-item__name"
          style="color: {{ item.color }}; font-weight: {{ item.fontWeight }}"
        >
          {{ item.name }}
        </view>
      </view>
    </block>
  </view>
</view>
