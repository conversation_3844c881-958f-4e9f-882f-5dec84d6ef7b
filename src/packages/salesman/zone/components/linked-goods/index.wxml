<view>
  <view
    wx:if="{{ useNavigator }}"
    class="linked-goods-nav custom-class"
    bind:tap="handleItemClick"
  >
      <image 
        class="linked-goods-nav__img"
        src="https://b.yzcdn.cn/public_files/2709fc3cc5ea68071ab3409ff9729a27.png"
      />
      商品详情
  </view>
  <view
    wx:else
    class="linked-goods custom-class"
    bind:tap="handleItemClick"
  >
    <view class="linked-goods__img-wrap">
      <image
        class="linked-goods__img image-class {{ showImgMask ? 'linked-goods__img--has-mask' : '' }}"
        src="{{ goodsInfo.image || goodsInfo.pictureUrl }}"
        mode="aspectFill"
      />
      <view
        wx:if="{{ showImgMask }}"
        class="linked-goods__img-tip"
      >
        {{ imgMaskText }}
      </view>
    </view>
    <view class="linked-goods__content">
      <view class="linked-goods__title title-class">{{ goodsInfo.title }}</view>
      <view class="linked-goods__price price-class">¥{{ utils.cent2yuan(goodsInfo.price) }}</view>
      <slot />
    </view>
  </view>
</view>

<wxs src="../../helpers/wx.wxs" module="utils"></wxs>
