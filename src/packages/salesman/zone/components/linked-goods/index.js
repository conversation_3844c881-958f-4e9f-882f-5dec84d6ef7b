import WscComponent from 'pages/common/wsc-component';
import navigate from 'shared/utils/navigate';
import args from '@youzan/weapp-utils/lib/args';

import { getMaterialBannerId } from '../../common/utils';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';

WscComponent({
  externalClasses: [
    'custom-class',
    'image-class',
    'title-class',
    'price-class'
  ],

  properties: {
    useNavigator: {
      type: Boolean,
      value: false,
    },
    showImgMask: {
      type: Boolean,
      value: false,
    },
    imgMaskText: {
      type: String,
      value: '已下架'
    },
    goodsInfo: Object,
    materialId: {
      type: Number,
    },
    loggerType: {
      type: String,
    },
  },

  methods: {
    handleItemClick() {
      const { useNavigator, goodsInfo, materialId, loggerType } = this.data;
      if (useNavigator) {
        let { url, formatGoodsWeappUrl } = goodsInfo;
        url = url || formatGoodsWeappUrl;
        if (materialId) {
          url = args.add(url, { banner_id: getMaterialBannerId({ materialId, spm: loggerType }) });
        }
        url = args.add(url, defaultEnterShopOptions);
        navigate.navigate({
          url,
        });
      } else {
        this.triggerEvent('click', goodsInfo);
      }
    }
  }
});
