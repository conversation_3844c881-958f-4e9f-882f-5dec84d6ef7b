.linked-goods-nav {
  color: #586b93;
  font-size: 15px;
  display: inline-flex;
  align-items: center;

  &__img {
    width: 14px;
    height: 14px;
    padding-right: 4px;
  }
}

.linked-goods {
  display: flex;
  padding: 8px;
  background-color: #f7f8fa;

  &__img {
    width: 40px;
    height: 40px;

    &--has-mask {
      opacity: .5;
    }
  }

  &__img-tip {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 16px;
    line-height: 16px;
    background-color: #000;
    text-align: center;
    font-size: 10px;
    color: #fff;
  }

  &__img-wrap {
    position: relative;
    margin-right: 8px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 14px;
  }

  &__title {
    overflow: hidden;
    color: #323233;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: keep-all;
  }

  &__price {
    color: #969799;
  }
}
