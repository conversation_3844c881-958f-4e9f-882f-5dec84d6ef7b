import WscComponent from 'pages/common/wsc-component';
import { emojiSprits, emojiList } from './config';

// 原图宽高
const ORIGIN_WIDTH = 628;
const ORIGIN_HEIGHT = 208;

function linkSplicing(key) {
  return `https://img01.yzcdn.cn/image/emoji/${key}.png`;
}

WscComponent({
  // 容器节点
  node: null,

  // 组件渲染大小相对于设计图的比例
  scale: 1,

  swiperCurrent: undefined,

  // 组件偏移量
  offsetLeft: 0,
  offsetTop: 0,

  properties: {
    // 是否为popup展示
    isPopup: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 表情大图
    emojiSprits,
    height: 0
  },

  ready() {
    this.getScale();
    this.swiperCurrent = 0;
  },

  methods: {
    getScale() {
      const query = wx.createSelectorQuery().in(this);
      query.selectAll('.emoji-picker--swiper').boundingClientRect(rects => {
        this.node = rects[0];
        this.scale = this.node.width / ORIGIN_WIDTH;
        // 拿不到就递归再拿
        if (!this.scale) {
          return this.getScale();
        }
        // 根据比例设置高度
        this.setYZData({
          height: this.scale * ORIGIN_HEIGHT
        });
        this.getOffsetTop();
      }).exec();
    },

    getOffsetTop() {
      if (this.data.isPopup) {
        wx.getSystemInfo({
          success: ({ windowWidth, windowHeight }) => {
            this.offsetLeft = windowWidth - this.node.width;
            this.offsetTop = windowHeight - this.node.height;
          }
        });
      } else {
        this.offsetLeft = this.node.left;
        this.offsetTop = this.node.top;
      }
    },

    handleSwiperChange(e) {
      const { current } = e.detail;
      this.swiperCurrent = current;
    },

    handleTap(e) {
      let x = this.data.isPopup ? e.touches[0].clientX : e.touches[0].pageX;
      let y = this.data.isPopup ? e.touches[0].clientY : e.touches[0].pageY;
      // 减去偏移量后的x y
      x -= this.offsetLeft;
      y -= this.offsetTop;
      // 还原比例到设计图
      const scaleX = Math.floor(x / this.scale);
      const scaleY = Math.floor(y / this.scale);
      const currentEmojis = emojiList[this.swiperCurrent];
      Object.keys(currentEmojis).forEach(key => {
        const scopes = key.split(',');
        // 取得key对应的x y范围
        const [minX, maxX] = scopes[0].split('~');
        const [minY, maxY] = scopes[1].split('~');
        if (
          (scaleX < maxX && scaleX > minX)
          && (scaleY < maxY && scaleY > minY)
        ) {
          this.triggerEvent('pick', {
            text: currentEmojis[key],
            link: linkSplicing(currentEmojis[key])
          });
        }
      });
    }
  }
});
