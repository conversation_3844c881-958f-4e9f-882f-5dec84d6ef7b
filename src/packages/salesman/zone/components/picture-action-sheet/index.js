import WscComponent from 'pages/common/wsc-component';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import navigate from 'shared/utils/navigate';

const app = getApp();

WscComponent({
  properties: {
    show: {
      type: Boolean,
      value: false
    },

    needSetGlobal: {
      type: Boolean,
      value: false
    },

    hideMaterial: {
      type: Boolean,
      value: false
    }
  },

  data: {
    actions: [
      {
        name: '拍摄',
        id: 0
      },
      {
        name: '从手机相册选择',
        id: 1
      },
      {
        name: '从素材中心选择',
        id: 2
      }
    ]
  },

  observers: {
    hideMaterial(newValue) {
      const actions = this.data.actions;
      if (newValue) {
        actions.pop();
        this.setYZData({
          actions
        });
      }
    }
  },


  methods: {
    chooseImage(type, success) {
      wx.chooseImage({
        count: 9,
        sizeType: ['original'],
        sourceType: [type],
        success
      });
    },

    setPathsToGlobal(tempFilePaths) {
      app.globalData.zoneImages = tempFilePaths;
    },

    handleClose() {
      this.triggerEvent('close');
    },

    handleSelect(e) {
      this.triggerEvent('close');
      const { id } = e.detail;
      switch (id) {
        case 0:
          this.chooseImage('camera', ({ tempFilePaths }) => {
            this.pictureHandler(tempFilePaths);
          });
          break;
        case 1:
          this.chooseImage('album', ({ tempFilePaths }) => {
            this.pictureHandler(tempFilePaths);
          });
          break;
        case 2:
          if (this.data.needSetGlobal) {
            navigate.navigate({
              url: '/packages/salesman/zone/material/index'
            });
            return;
          }
          Dialog.alert({
            message: '选择素材后，当前内容会被覆盖。',
            showCancelButton: true
          }).then(() => {
            this.triggerEvent('select-material');
            navigate.navigate({
              url: '/packages/salesman/zone/material/index'
            });
          });
          break;
        default:
          break;
      }
    },

    pictureHandler(tempFilePaths) {
      this.triggerEvent('select', { tempFilePaths });
      if (this.data.needSetGlobal) {
        this.setPathsToGlobal(tempFilePaths);
        navigate.navigate({
          url: '/packages/salesman/zone/editor/index'
        });
      }
    }
  }
});
