import Toast from '@vant/weapp/dist/toast/toast';
import WscComponent from 'pages/common/wsc-component';
import upload from 'shared/utils/upload';

const app = getApp();

WscComponent({
  uploadQueue: [],

  properties: {
    count: {
      type: Number,
      value: 9
    },

    hideMaterial: {
      type: Boolean,
      value: false
    },

    /*
    * [{ uid: Math.ceil(Math.random() * 100000), src: '' }]
    */
    preImages: {
      type: Array,
      value: []
    },

    showDelete: {
      type: Boolean,
      value: true,
    },
  },

  data: {
    show: false,
    tempImages: [],
    uploadImages: []
  },

  observers: {
    preImages(images) {
      images = images.map(item => {
        if (!item.uid) {
          item.uid = Math.ceil(Math.random() * 100000);
        }
        return item;
      });
      this.setYZData({
        tempImages: images,
        uploadImages: images
      });

      // 上次传的照片
      this.triggerEvent('update', {
        type: 'init',
        images: this.data.tempImages
      });
    }
  },

  ready() {
    // 列表页选择的照片
    const { zoneImages } = app.globalData;
    if (zoneImages) {
      this.upload(zoneImages);
      app.globalData.zoneImages = null;
    }
  },

  methods: {
    setValue(item, cb) {
      this.data.tempImages.find(image => {
        if (image.uid === item.uid) {
          cb(image);
          return true;
        }
        return false;
      });
      this.setYZData({
        tempImages: this.data.tempImages
      });
    },

    handleSelect() {
      this.setYZData({
        show: true
      });
    },

    handleSelectMaterial() {
      this.triggerEvent('select-material');
    },

    handlePicture(e) {
      this.handleClose();
      const { tempFilePaths } = e.detail;
      this.upload(tempFilePaths);
    },

    upload(tempFilePaths) {
      const { count, tempImages } = this.data;
      const tempMap = tempFilePaths.map(item => {
        return {
          src: item,
          uid: Math.ceil(Math.random() * 100000)
        };
      });
      const currentImages = tempImages.concat([...tempMap]);
      if (currentImages.length > count) {
        Toast.fail({
          message: '最多可添加9张图片',
          context: this
        });
        return false;
      }
      this.setYZData({
        tempImages: tempImages.concat(tempMap)
      });
      tempMap.forEach(item => {
        this.setValue(item, image => {
          image.progress = true;
        });
      });
      this.uploadQueue = tempMap;
      this.uploadFile();
    },

    uploadFile() {
      const item = this.uploadQueue.shift();
      if (item) {
        upload({
          file: item.src,
          success: res => {
            this.setValue(item, image => {
              image.progress = false;
            });
            const currUploadImage = {
              ...res,
              uid: item.uid
            };
            this.setYZData({
              uploadImages: this.data.uploadImages.concat([currUploadImage])
            });
            this.triggerEvent('update', {
              type: 'upload',
              images: currUploadImage
            });
            this.uploadFile();
          },
          fail: () => {
            this.setValue(item, image => {
              image.progress = false;
            });
            this.setValue(item, image => {
              image.error = '上传失败';
            });
            this.uploadFile();
          }
        });
      }
    },

    handleDelete(e) {
      const { uid } = e.currentTarget.dataset;
      const { tempImages, uploadImages } = this.data;
      tempImages.find((item, index) => {
        if (item.uid === uid) {
          tempImages.splice(index, 1);
          return true;
        }
        return false;
      });
      uploadImages.find((item, index) => {
        if (item.uid === uid) {
          uploadImages.splice(index, 1);
          return true;
        }
        return false;
      });
      this.setYZData({
        tempImages,
        uploadImages
      });
      this.triggerEvent('update', {
        type: 'delete',
        images: uploadImages
      });
    },

    handlePreview(e) {
      const urls = this.data.tempImages.map(item => {
        return item.src;
      });
      const { index } = e.currentTarget.dataset;
      this.triggerEvent('preview');
      wx.previewImage({
        urls,
        current: urls[index]
      });
    },

    handleClose() {
      this.setYZData({
        show: false
      });
    }
  }
});
