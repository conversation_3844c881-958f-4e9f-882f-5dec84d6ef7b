<view class="image-upload">
  <view
    wx:for="{{ tempImages }}"
    wx:key="id"
    class="image-upload__img"
  >
    <view
      wx:if="{{ (item.progress && item.progress !== 100) || item.error }}"
      class="image-upload__img--progress"
    >
      <view
        wx:if="{{ item.progress }}"
        class="image-upload__img--progress-bar"
      >上传中</view>
      <view
        wx:if="{{ item.error }}"
        class="image-upload__img--progress-error"
      >{{ item.error }}</view>
    </view>
    <image
      src="{{ item.src }}"
      data-index="{{ index }}"
      bindtap="handlePreview"
    />
    <van-icon
      wx:if="{{ showDelete }}"
      name="clear"
      size="16px"
      color="#999"
      class="image-upload__img--delete"
      data-uid="{{ item.uid }}"
      bind:tap="handleDelete"
    />
  </view>
  <view
    wx:if="{{ tempImages.length < 9 && showDelete }}"
    class="image-upload__img image-upload__selector"
    bind:tap="handleSelect"
  >
    <van-icon
      name="plus"
      color="#c8c9cc"
      size="32px"
    />
  </view>
  <action-sheet
    show="{{ show }}"
    hide-material="{{ hideMaterial }}"
    bind:close="handleClose"
    bind:select-material="handleSelectMaterial"
    bind:select="handlePicture"
  />
</view>
<van-toast id="van-toast" />