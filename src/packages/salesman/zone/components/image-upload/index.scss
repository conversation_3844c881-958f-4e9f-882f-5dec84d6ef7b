.image-upload {
  display: flex;
  flex-wrap: wrap;
  padding: 16px 0 40px 0;

  &__img {
    position: relative;
    width:216rpx;
    height:216rpx;
    border-radius: 2px;
    margin: 0 16rpx 16rpx 0;

    &:nth-of-type(3n) {
      margin-right: 0;
    }

    &:nth-of-type(n+7) {
      margin-bottom: 0;
    }

    &--progress {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, .3);

      &-bar, &-error {
        font-size: 12px;
        text-align: center;
      }

      &-bar {
        color: #fff;
      }

      &-error {
        color: rgb(225, 79, 79);
      }
    }

    image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &--delete {
      position: absolute;
      top: -8px;
      right: -8px;
    }
  }

  &__selector {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f7f8fa;
  }
}