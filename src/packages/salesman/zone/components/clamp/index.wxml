<view class="clamp custom-class">
  <cap-rich-text
    class="clamp__content {{ contentClass }}"
    style="-webkit-line-clamp: {{ maxLine }};max-height: {{ isClamped ? maxHeight + 'px' : 'inherit' }}"
    html="{{ content }}"
  />
  <block wx:if="{{ needAction }}">
    <!-- 小程序没有scrollHeight对比是否内容超出，用一个不可见节点获取内容真实高度 -->
    <cap-rich-text
      id="fake-content"
      class="clamp__fake-content"
      html="{{ content }}"
    />
    <view
      wx:if="{{ showAction }}"
      class="clamp__action-area custom-action-class"
      bind:tap="handleActionClick"
    >
      {{ isClamped ? expandText : shrinkText }}
    </view>
  </block>
</view>
