<van-popup
  show="{{ show }}"
  position="bottom"
  bind:close="handleClose"
>
  <view class="goods-selector">
    <view class="goods-selector--header">
      <view
        class="goods-selector--header__cancel"
        bindtap="handleClose"
      >取消</view>
      <view class="goods-selector--header__title">选择商品</view>
      <view
        class="goods-selector--header__complete {{ completed ? 'theme-bg-color' : '' }}"
        bindtap="handleComplete"
      >完成</view>
    </view>
    <view class="goods-selector--search">
      <van-search
        value="{{ keywords }}"
        background="#f7f8fa"
        placeholder="请输入商品名称等关键字"
        placeholder-style="color: #c8c9cc"
        custom-class="goods-selector--search__input"
        bind:change="handleChange"
      />
    </view>
    <view class="goods-selector--main">
      <view
        wx:if="{{ showEmpty || showNoMatch }}"
        class="goods-selector--main__empty"
      >{{ showNoMatch ? '暂无匹配的商品' : '暂无推广的商品' }}</view>
      <scroll-view
        wx:else
        scroll-y
        class="goods-selector--main__list"
        bindscrolltolower="handleLoad"
      >
        <view wx:if="{{ preGoods.goodsId }}" class="goods-selector--main__list-last van-hairline--bottom">
          <template
            is="goods-container"
            data="{{ goods: preGoods, selectedGoods }}"
          />
          <view class="goods-selector--main__list-last--text">上次所选</view>
        </view>
        <template
          wx:for="{{ goodsList }}"
          wx:for-item="goods"
          wx:key="goodsId"
          is="goods-container"
          data="{{ goods, selectedGoods }}"
        />
        <view
          wx:if="{{ loading && !finished }}"
          class="goods-selector--main__list-loading"
        >
          <van-loading type="spinner" />
        </view>
      </scroll-view>
    </view>
  </view>
</van-popup>

<template name="goods-container">
  <view
    class="goods-selector--main__list-wrap {{ selectedGoods.goodsId === goods.goodsId && 'theme-border-color' }}"
  >
    <item
      goodsInfo="{{ goods }}"
      image-class="goods-img"
      price-class="goods-price"
      title-class="goods-title"
      class="goods-selector--main__list-wrap--container"
      bind:click="handleSelected"
    >
      <view
        wx:if="{{ goods.profitDimension }}"
        class="goods-cps"
      >
        <block wx:if="{{ goods.constantCommission || goods.profitRange }}">
          <view class="goods-cps--money">
            最高赚 <text class="theme-color">¥{{ utils.cent2yuan(goods.profitRange[1] || goods.profitRange[0] || goods.constantCommission) }}</text>
          </view>
        </block>
      </view>
      <view
        wx:elif="{{ goods.rate || goods.profit }}"
        class="goods-cps"
      >
        <view
          wx:if="{{ goods.rate }}"
          class="goods-cps--range"
        >
          佣金比例{{ utils.cent2yuan(goods.rate) }}%
        </view>
        <block wx:if="{{ goods.profit || goods.profitRange }}">
          |
          <view class="goods-cps--money">
            最高赚 <text class="theme-color">¥{{ utils.cent2yuan(goods.profitRange[1] || goods.profitRange[0] || goods.profit) }}</text>
          </view>
        </block>
      </view>
    </item>
  </view>
</template>

<wxs src="../../helpers/wx.wxs" module="utils"></wxs>
