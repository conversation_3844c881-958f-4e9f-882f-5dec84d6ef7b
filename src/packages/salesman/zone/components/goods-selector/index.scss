.goods-selector {
  width: 100vw;
  height: 80vh;
  padding: 0 16px;
  box-sizing: border-box;
  border-radius: 12px 12px 0 0;
  background-color: #fff;

  &--header {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &__cancel {
      color: #999;
      font-size: 14px;
    }

    &__title {
      color: #333;
      font-size: 16px;
      font-weight: 500;
      flex: 1;
      text-align: center;
    }

    &__complete {
      width: 64px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      border-radius: 18px;
      background: #dcdee0;
      color: #fff;
      font-size: 14px;
    }
  }

  &--search {
    width: 100%;
    margin: 8px 0;

    &__input {
      padding: 0 12px;
      border-radius: 17px;
    }
  }

  &--main {
    &__empty {
      width: 100%;
      text-align: center;
      font-size: 14px;
      color: #999;
      margin-top: 20vh;
    }

    &__list {
      height: calc(80vh - 92px);
      
      &-last {
        position: relative;
        width: 100%;
        height: 110px;
        margin-bottom: 12px;

        &--text {
          font-size: 12px;
          color: #c8c9cc;
          background-color: #fff;
          position: absolute;
          bottom: -6px;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      &-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        overflow: hidden;
        background: #f7f8fa;
        height: 88px;
        border: 2px solid #f7f8fa;
        margin-bottom: 12px;

        &--container {
          width: 100%;
        }
      }

      &-loading {
        margin-top: 10px;
        text-align: center;
      }
    }
  }
}

.goods-img {
  width: 72px;
  height: 72px;
  border-radius: 2px;
}

.goods-title {
  width: 60vw;
}

.goods-price {
  color: #323233;
  font-size: 14px;
  font-weight: 500;
  height: 20px;
  line-height: 20px;
  margin: 8px 0;
}

.goods-cps {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;

  &--range {
    margin-right: 2px;
  }

  &--money {
    margin-left: 2px;

    text {
      font-weight: bold;
    }
  }
}
