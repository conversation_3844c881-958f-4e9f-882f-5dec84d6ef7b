import WscComponent from 'pages/common/wsc-component';
import debounce from '@youzan/weapp-utils/lib/debounce';
import { getSelectGoods } from '../../api';

const PAGE_SIZE = 20;

WscComponent({
  properties: {
    show: {
      type: Boolean,
      value: false
    },

    preGoods: {
      type: Object,
      value: {}
    },
  },

  data: {
    completed: false,
    showEmpty: false,
    showNoMatch: false,
    selectedGoods: {},
    keywords: '',
    page: 1,
    loading: false,
    finished: false,
    goodsList: []
  },

  observers: {
    show(val) {
      if (!val) {
        this.setYZData({
          page: 1,
          keywords: '',
          finished: false,
          goodsList: []
        });
      } else {
        this.getList();
      }
    },
  },

  ready() {
    this.handleChange = debounce(e => {
      const value = e.detail;
      this.setYZData({
        page: 1,
        keywords: value,
        finished: false,
        goodsList: []
      });
      this.getList();
    }, 300);
  },

  methods: {
    handleClose() {
      this.triggerEvent('close');
    },

    handleComplete() {
      if (this.data.completed) {
        this.triggerEvent('select', {
          goods: this.data.selectedGoods
        });
        this.handleClose();
      }
    },

    handleLoad() {
      this.getList();
    },

    getList() {
      const {
        page,
        keywords,
        goodsList,
        finished,
        preGoods
      } = this.data;
      this.setYZData({
        loading: true
      });
      if (finished) return;
      getSelectGoods({
        keywords,
        page,
        pageSize: PAGE_SIZE
      }).then(data => {
        const list = data;
        // 去除上次已选的商品
        const findIndex = list.findIndex(item => item.goodsId === preGoods.goodsId);
        if (findIndex > -1) {
          list.splice(findIndex, 1);
        }
        if (list.length < PAGE_SIZE) {
          this.setYZData({
            finished: true
          });
        }
        const currentGoodsList = goodsList.concat(list);
        this.setYZData({
          goodsList: currentGoodsList,
          page: page + 1,
          loading: false
        });
        if (!currentGoodsList.length) {
          if (this.keywords) {
            this.setYZData({
              showNoMatch: true
            });
          } else {
            this.setYZData({
              showEmpty: true
            });
          }
        } else {
          this.setYZData({
            showEmpty: false,
            showNoMatch: false
          });
        }
      }).catch(() => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      });
    },

    handleSelected(e) {
      const goods = e.detail;
      let currentGoods = {};
      let completed = false;
      if (this.data.selectedGoods.goodsId !== goods.goodsId) {
        currentGoods = goods;
        completed = true;
      }
      this.setYZData({
        selectedGoods: currentGoods,
        completed
      });
    }
  }
});
