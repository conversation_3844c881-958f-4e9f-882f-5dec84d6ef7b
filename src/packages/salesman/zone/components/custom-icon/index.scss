@font-face {
  font-style: normal;
  font-weight: normal;
  font-family: "iconfont";
  src: url('https://img01.yzcdn.cn/public_files/2d65aae205a165be5a13289f9fc2ae5f.woff2') format('woff2'),
  url('https://img01.yzcdn.cn/public_files/628afb5f8721a7f556a6d33dab855f62.woff') format('woff'),
  url('https://img01.yzcdn.cn/public_files/62e6459cc51812bbda257eaf7d855e55.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: inherit;
  font-style: normal;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
}

.icon-mall-o:before {
  content: "\e606";
}

.icon-mall:before {
  content: "\e607";
}

.icon-moments:before {
  content: "\e608";
}

.icon-moments-o:before {
  content: "\e609";
}

.icon-business-card:before {
  content: "\e60a";
}

.icon-business-card-o:before {
  content: "\e60b";
}

.icon-wode:before {
  content: "\e60c";
}

.icon-wode-o:before {
  content: "\e60d";
}

.icon-link::before {
  content: "\e602";
}

// 为了使用van-icon 自定义的类名
.iconfont-link::before {
  content: "\e602";
}

