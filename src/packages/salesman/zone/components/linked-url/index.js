import WscComponent from 'pages/common/wsc-component';
import navigate from 'shared/utils/navigate';

import { getLinkMaterialUrl } from '../../common';

const app = getApp();

WscComponent({
  properties: {
    linkMaterial: {
      type: Object,
    },
    materialType: {
      type: Number,
    },
    contentId: {
      type: Number,
    },
    materialId: {
      type: Number,
    },
    noteType: {
      type: String,
    },
    linkUrl: {
      type: String,
    },
  },

  methods: {
    _onViewDetail() {
      const {
        linkMaterial,
        materialType,
        contentId,
        materialId,
        noteType,
        linkUrl,
      } = this.data;
      let weappUrl;
      const kdtId = app.getKdtId();
      if (materialType) {
        // 处理跳转笔记或文章详情
        const { alias } = linkMaterial;
        const urlObj = getLinkMaterialUrl({
          alias,
          materialType,
          kdtId,
          noteType,
          linkUrl,
        });

        app.logger &&
          app.logger.log({
            et: 'click',
            ei: 'clickdetail',
            en: '点击查看详情',
            si: kdtId,
            params: { materialId },
          });
        weappUrl = urlObj.weappUrl;
      } else {
        app.logger &&
          app.logger.log({
            et: 'click',
            ei: 'click_detail',
            en: '点击查看详情',
            si: kdtId,
            params: { contentId },
          });
        weappUrl = `/${linkMaterial.weappUrl}`;
      }
      navigate.navigate({
        url: weappUrl,
      });
    },
  },
});
