import WscComponent from 'pages/common/wsc-component';

const app = getApp();

WscComponent({
  externalClasses: ['custom-class', 'item-custom-class', 'goods-title-class'],

  options: {
    multipleSlots: true,
  },

  properties: {
    showLogo: Boolean,
    itemId: Number,
    logo: String,
    title: String,
    text: String,
    item: Object,
    imageList: Array,
    video: {
      type: Object,
      value: {},
    },
    linkMaterial: {
      type: Object,
    },
    materialType: {
      type: Number,
    },
    materialId: {
      type: Number,
    },
    loggerType: {
      type: String,
    },
    noteType: {
      type: String,
    },
    linkUrl: {
      type: String,
    },
  },

  data: {
    videoKdtId: 0,
  },

  attached() {
    let kdtId = app.getKdtId();
    const shopInfo = app.getShopInfoSync();
    if (shopInfo.chainStoreInfo && shopInfo.chainStoreInfo.rootKdtId) {
      kdtId = shopInfo.chainStoreInfo.rootKdtId;
    }
    // setYZData设置后，props没有变
    this.setData({
      videoKdtId: kdtId,
    });
  },

  methods: {
    handleGetSrc(e) {
      this.triggerEvent('get-src', e.detail);
    },

    _handlePreview() {
      this.triggerEvent('preview');
    },

    _handleImageLoad() {
      this.triggerEvent('image-load', {
        id: this.data.itemId,
      });
    },
  },
});
