<view class="feed-item custom-class">
  <image
    wx:if="{{ showLogo }}"
    class="feed-item__thumb"
    src="{{ logo }}"
    mode="aspectFill"
  />
  <view class="feed-item__content {{ !showLogo ? 'feed-item__content--no-img' : '' }}">
    <view class="feed-item__title">{{ title }}</view>
    <clamp
      custom-class="feed-item__text"
      content="{{ text }}"
      expand-text="全文"
    />
    <!-- media -->
    <!-- TODO: 视频宽高样式处理先注释，因为安卓不好看 -->
    <view
      wx:if="{{ video.video.video_id }}"
      class="feed-item__video"
    >
      <showcase-video
        kdt-id="{{ videoKdtId }}"
        componentData="{{ video }}"
        bind:get-src="handleGetSrc"
      />
    </view>
    <gallery
      wx:if="{{ imageList.length > 0 }}"
      img-list="{{ imageList }}"
      bind:preview="_handlePreview"
      bind:load="_handleImageLoad"
    />
    <!-- media end -->
    <slot name="after-media" />
    <!-- link -->
    <linked-url
      wx:if="{{ !!linkMaterial }}"
      link-material="{{ linkMaterial }}"
      material-type="{{ materialType }}"
      content-id="{{ itemId }}"
      material-id="{{ materialId || linkMaterial.linkId }}"
      note-type="{{noteType}}"
      linkUrl="{{linkUrl}}"
    />
    <!-- goods -->
    <linked-goods
      wx:if="{{ !!item }}"
      custom-class="feed-item__linked-goods"
      title-class="goods-title-class"
      goods-info="{{ item.goodsInfo }}"
      show-img-mask="{{ item.showImgMask }}"
      img-mask-text="{{ item.imgMaskText }}"
      material-id="{{ materialId }}"
      logger-type="{{ loggerType }}"
      use-navigator
    />
    <!-- action-area -->
    <slot name="before-bottom" />
    <!-- action-area end -->
    <slot name="bottom" />
  </view>
</view>
