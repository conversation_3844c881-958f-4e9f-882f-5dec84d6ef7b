import WscComponent from 'pages/common/wsc-component';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

WscComponent({
  externalClasses: ['custom-gallery'],

  properties: {
    imgList: Array,
  },

  data: {
    imageWidth: 0,
    imageHeight: 0,
    imgClass: 'gallery__img',
    galleryImgList: [],
  },

  observers: {
    imgList(newVal) {
      const isMultiple = newVal.length > 1;
      this.setYZData({
        galleryClass: isMultiple ? 'gallery--multiple' : 'gallery--single',
        galleryTianClass: newVal.length === 4 ? 'gallery--tian' : '',
        imgClass: isMultiple ? 'gallery__img' : 'gallery--single_img',
        galleryImgList: newVal.map((item) => cdnImage(item, '!300x300.jpg')),
      });
    },
  },

  methods: {
    handlePreviewImage(e) {
      const { current: index } = e.target.dataset;
      const urls = this.data.imgList;
      this.triggerEvent('preview');
      wx.previewImage({
        current: urls[index],
        urls,
      });
    },

    _handleLoad(e) {
      if (this.data.imgList.length === 1) {
        const { width, height } = e.detail;
        this._setImageSize(width, height);
      }
      this.triggerEvent('load');
    },

    _setImageSize(srcWidth, srcHeight) {
      const max = 200;
      const ratio = srcWidth / srcHeight;
      let width = 0;
      let height = 0;
      if (ratio > 1) {
        width = max;
        height = max / ratio;
      } else {
        width = max * ratio;
        height = max;
      }

      this.setYZData({
        imageWidth: width + 'px',
        imageHeight: height + 'px',
      });
    },
  },
});
