<view
  class="gallery custom-gallery {{ galleryClass }}"
  bind:tap="handlePreviewImage"
>
  <view
    wx:for="{{ galleryImgList }}"
    wx:for-item="img"
    wx:key="*this"
    class="gallery__img-wrap {{ galleryTianClass }}"
  >
    <image
      class="{{ imgClass }}"
      src="{{ img }}"
      mode="{{ galleryImgList.length > 1 ? 'aspectFill' : 'aspectFit' }}"
      style="width: {{ imageWidth || '100%' }};height: {{ imageHeight  || '100%'}}"
      data-current="{{ index }}"
      bind:load="_handleLoad"
    />
  </view>
</view>
