import WscComponent from 'pages/common/wsc-component';

WscComponent({
  properties: {
    showEditBtns: Boolean,
    disabled: Boolean,
    leftText: String,
    contentId: Number,
    thumbUpCount: Number,
    liked: Boolean,
  },

  lifetimes: {
    ready() {
      this.setYZData({
        iconColor: this.data.disabled ? '#c8c9cc' : '',
      });
    },
  },

  methods: {
    _handleIconClick(e) {
      const { id, type } = e.target.dataset;
      this.triggerEvent(type, { id });
    }
  }
});
