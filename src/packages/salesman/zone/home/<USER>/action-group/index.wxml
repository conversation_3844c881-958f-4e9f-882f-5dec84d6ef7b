<view class="action-area">
  <text class="action-area__text">{{ leftText }}</text>
  <view class="action-area__btns">
    <block>
      <view
        class="action-area__favor-icon {{ liked && 'action-area__favor-icon--active' }}"
        data-type="thumbup"
        data-id="{{ contentId }}"
        bind:tap="_handleIconClick"
      />
      <view
        wx:if="{{ thumbUpCount }}"
        class="action-area__favor-num"
      >
        {{ thumbUpCount }}
      </view>
    </block>
    <view
      wx:if="{{ showEditBtns }}"
      class="action-area__edit-btns"
      bind:tap="_handleIconClick"
    >
      <van-icon
        name="edit"
        size="20px"
        custom-style="margin-left: 24px;"
        color="{{ iconColor }}"
        data-type="edit"
        data-id="{{ contentId }}"
      />
      <van-icon
        name="delete"
        size="20px"
        custom-style="margin-left: 24px;"
        color="{{ iconColor }}"
        data-type="delete"
        data-id="{{ contentId }}"
      />
    </view>
  </view>
</view>
