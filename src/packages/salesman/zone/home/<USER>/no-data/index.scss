@import "~themes/order-theme.scss";

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 42.667vw - 51px);
  font-size: 14px;
  text-align: center;

  &--self {
    .no-data__text {
      margin-top: 20px;
    }
  }

  &--customer {
    .no-data__text {
      margin-top: 24px;
    }
  }

  &__button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;

    &-text {
      line-height: 16px;
    }
  }

  &__img {
    width: 150px;
    height: 150px;
  }

  &__text {
    font-size: 14px;
    color: #969799;
  }
}
