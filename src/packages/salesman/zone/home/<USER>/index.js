const app = getApp();
const http = app.request;

// http://zanapi.qima-inc.com/site/service/view/429830
export const getMomentsList = (data) => {
  return http({
    path: '/wscump/salesman/zone/moments-list.json',
    data,
  });
};

// http://zanapi.qima-inc.com/site/service/view/429886
export const postMomentsThumbUp = (data) => {
  return http({
    path: '/wscump/salesman/zone/moments-thumb-up.json',
    method: 'POST',
    data,
  });
};

// http://zanapi.qima-inc.com/site/service/view/429852
export const deleteMoments = (data) => {
  return http({
    path: '/wscump/salesman/zone/delete-moments.json',
    method: 'POST',
    data,
  });
};

// 获取是否有名片
export function getCardAccess(data) {
  return http({
    path: '/wscump/salesman/zone/getCardAccess.json',
    method: 'GET',
    data,
  });
}

// 获取推广页面白名单
export function getPromoteWhiteList() {
  return http({
    path: '/wscsalesman/promotion/promote/getPromoteWhiteList.json',
    method: 'GET',
    config: {
      priority: 'low',
    },
  });
}
