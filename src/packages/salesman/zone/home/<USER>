import Toast from '@vant/weapp/dist/toast/toast';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import get from '@youzan/weapp-utils/lib/get';
import navigate from 'shared/utils/navigate';
import openWebView from 'shared/utils/open-web-view';
import WscPage from 'pages/common/wsc-page';
import * as CommonService from '../api';
import * as Service from './api';
import { ACCOUNT_TYPE, handleQueryParams } from '../common/utils';
import { getGoodsWeappPath } from '../../common/url';
import { materialTypeRevertMap } from '../common/constant';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';
import { initSalesman } from 'shared/utils/salesman-share';

const app = getApp();

WscPage({
  _thumbIdMap: {}, // 当前点击赞的ids合集

  _showedContentMap: {}, // 用于曝光埋点记录

  _triggerHide: false, // 用于解决preivew和地图触发onHide onShow的问题

  data: {
    loading: false,
    finished: false,
    sl: '',
    canEdit: false,
    allow: false,
    page: 1,
    pageSize: 8,
    nickname: '',
    bannerImage: '',
    list: [],
    salesman: {},
    // 发布action
    showActionSheet: false,
    showCard: false,
  },

  onShow() {
    const kdtId = app.getKdtId();
    CommonService.getPersonalAbility(kdtId).then((res) => {
      if (!res.valid) {
        openWebView(
          `https://h5.youzan.com/wscump/salesman/center-v2/empty-page?kdt_id=${kdtId}&titleKey=Moment`,
          {
            method: 'redirectTo',
          }
        );
      }
      // 有店铺能力时继续渲染，否则跳转H5缺省页
      if (app.globalData.zoneDraft && !this._triggerHide && value) {
        Dialog.alert({
          message: '是否保留本次编辑',
          showCancelButton: true,
          confirmButtonText: '保留',
          cancelButtonText: '不保留',
        })
          .then(() => {
            const {
              goods,
              location,
              preImages,
              videoData,
              contentLength,
              textContent,
              linkInfo,
            } = app.globalData.zoneDraft;
            wx.setStorage({
              key: 'moments',
              data: {
                goods,
                location,
                preImages,
                videoData,
                contentLength,
                textContent,
                linkInfo,
              },
            });
          })
          .catch(() => {
            app.globalData.zoneDraft = null;
            wx.setStorage({
              key: 'moments',
              data: null,
            });
          });
      }
    });
  },

  onLoad(query) {
    query.st = 30; // 鉴于绑定来源已经被传乱了，商业化拆分项目中重新指定枚举，便于后端控制绑定关系
    initSalesman.call(this, { scene: 'zone', sst: 6, query });
    this.setYZData({
      shopName: get(app.getShopInfoSync(), 'base.shop_name', ''),
    });
    this._init();
  },

  onUnload() {
    app.globalData.zoneDraft = null;
  },

  onPullDownRefresh() {
    this.setYZData({
      page: 1,
    });
    this._fetchList({ page: 1 }, true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    this._fetchList();
  },

  onShareAppMessage() {
    const { nickname, bannerImage, sl, queryParam } = this.data;
    // 传入销售员所属网店 kdtId
    const { online_kdt_id: onlineKdtId } = handleQueryParams(queryParam);
    let sharePath = `/packages/salesman/zone/home/<USER>
    if (sl) {
      sharePath = addSalesmanParams({ url: sharePath, sl });
    }
    return {
      title: `${nickname}的空间 ，好货多多，常来逛逛`,
      imageUrl: bannerImage,
      path: sharePath,
    };
  },

  _init() {
    let { sl = '', sls = '', from_params: fromParams } = this.__query__;
    sl = sl || sls;
    app.logger &&
      app.logger.log({
        et: 'display',
        ei: 'enterpage',
        en: '浏览页面',
        params: { sl },
      });
    Toast.loading('加载中...');
    this._getCardAccess();
    CommonService.checkMomentsAccessAndGetBaseInfo({ sl })
      .then((res) => {
        Toast.clear();
        const {
          sl,
          canEdit,
          allow,
          bannerImage,
          accountType,
          queryParam,
          userNickname,
          nickname,
        } = res;
        this._getCardAccess(sl);
        const isActionDisabled =
          !canEdit && accountType === ACCOUNT_TYPE.SALESMAN;
        const isOperatorNeedShow = canEdit || isActionDisabled;
        this.setYZData({
          canEdit: !!canEdit,
          userNickname: userNickname || '匿名用户',
          sl,
          allow,
          nickname,
          queryParam: fromParams || queryParam,
          bannerImage: cdnImage(bannerImage),
          isActionDisabled,
          isOperatorNeedShow,
        });
        this._fetchList();
      })
      .catch((err) => {
        Toast.fail(err.msg || '网络错误');
      });
  },

  _getCardAccess(sl) {
    Service.getCardAccess({ sl }).then((res) => {
      const { show } = res;
      this.setYZData({
        showCard: show,
      });
    });
  },

  _fetchList(data = {}, needRefresh = false) {
    const { loading, finished, page, pageSize, sl } = this.data;
    if (loading || (finished && !needRefresh)) return Promise.resolve();
    this.setYZData({ loading: true });
    return Service.getMomentsList({
      sl,
      page,
      pageSize,
      ...data,
    }).then((res) => {
      const { list: currentList, page, pageSize } = this.data;
      const _list = this._processListData(res);
      const list = needRefresh ? _list : [...currentList, ..._list];
      const finished = res.length < pageSize;
      this.setYZData({
        loading: false,
        page: page + 1,
        list,
        finished,
      });
    });
  },

  _handlePublish() {
    this._checkActionAccess()
      .then(() => {
        // 有草稿直接跳转
        const draft = wx.getStorageSync('moments');
        if (draft) {
          this._triggerHide = false;
          navigate.navigate({
            url: '/packages/salesman/zone/editor/index',
          });
        } else {
          this.setYZData({
            showActionSheet: true,
          });
        }
      })
      .catch(() => {});
  },

  _handleCloseActionSheet() {
    this.setYZData({
      showActionSheet: false,
    });
  },

  _handleImageLoad(e) {
    const { id } = e.detail;
    if (!this._showedContentMap[id]) {
      const currentMoments = this.data.list.find(
        (item) => item.contentId === id
      );
      // 只对商家动态曝光埋点
      if (currentMoments.momentsDetail) {
        app.logger &&
          app.logger.log({
            et: 'view',
            ei: 'story_view',
            en: '商家动态曝光',
            si: app.getKdtId(),
            params: { contentId: id },
          });
      }
      this._showedContentMap[id] = true;
    }
  },

  _handleTriggerHide() {
    this._triggerHide = true;
  },

  _handleThumbUp(e) {
    const { id } = e.detail;
    if (this._thumbIdMap[id]) return;
    this._thumbIdMap[id] = true;
    Service.postMomentsThumbUp({
      contentId: id,
    })
      .then((res) => {
        delete this._thumbIdMap[id];
        if (res.value) {
          const { list, userNickname } = this.data;
          const curIndex = list.findIndex((item) => item.contentId === +id);
          const curItem = list[curIndex];
          const { thumbUpCount, nickNames, liked } = curItem;
          let newThumbUpCount;
          let newNickNames;
          if (liked) {
            newThumbUpCount = thumbUpCount - 1;
            const userIndex = nickNames.findIndex(
              (item) => item === userNickname
            );
            newNickNames = nickNames
              .slice(0, userIndex)
              .concat(nickNames.slice(userIndex + 1))
              .slice(0, 100);
          } else {
            newThumbUpCount = thumbUpCount + 1;
            newNickNames = nickNames.concat(userNickname).slice(0, 100);
          }

          this.setYZData({
            [`list[${curIndex}].thumbUpCount`]: newThumbUpCount,
            [`list[${curIndex}].nickNames`]: newNickNames,
            [`list[${curIndex}].liked`]: !liked,
          });
        }
      })
      .catch((err) => {
        delete this._thumbIdMap[id];
        Toast.fail(err.msg || '网络错误');
      });
  },

  _handleEditZone(e) {
    this._checkActionAccess()
      .then(() => {
        this._triggerHide = false;
        navigate.navigate({
          url: `/packages/salesman/zone/editor/index?contentId=${e.detail.id}`,
        });
      })
      .catch(() => {});
  },

  _handleDeleteZone(e) {
    this._checkActionAccess()
      .then(() => {
        Dialog.confirm({
          message: '确认删除该条动态？',
        })
          .then(() => {
            const id = e.detail.id;
            Service.deleteMoments({
              contentId: id,
            })
              .then((res) => {
                if (res.value) {
                  const { list } = this.data;
                  const deleteIndex = list.findIndex(
                    (item) => item.contentId === id
                  );
                  const newList = list
                    .slice(0, deleteIndex)
                    .concat(list.slice(deleteIndex + 1));
                  this.setYZData({
                    list: newList,
                  });
                }
              })
              .catch((err) => {
                Toast.fail(err.msg || '网络错误');
              });
          })
          .catch(() => {});
      })
      .catch(() => {});
  },

  _processListData(data = []) {
    const { allow, isOperatorNeedShow } = this.data;
    return data.map((item) => {
      const { headImage, senderName, type } = item.momentsSenderInfo || {};
      const { videoId, coverUrl, coverHeight = 200, coverWidth = 200 } =
        item.momentsVideo || {};
      const { nickNames = [], thumbUpCount = 0, liked = 0 } =
        item.momentsThumbUp || {};
      const { momentsGoodsInfo: goodsInfo, momentsLinkInfo } = item;
      // 构建符合video组件的数据格式
      const video = {
        video: {
          video_id: videoId,
          cover_url: coverUrl,
          coverHeight,
          coverWidth,
        },
      };
      // type 0 表示是分销员发的动态
      // 需要是分销员自己发的动态，且是pro版本才可以编辑
      const showEditBtns = type === 0 && allow && isOperatorNeedShow;
      const momentsGoodsInfo = this._processGoodsData(goodsInfo);
      const sourceId = this.getSourceId(momentsGoodsInfo);
      const materialType = this.getMaterialType(momentsLinkInfo);
      return {
        ...item,
        liked: !!liked,
        video,
        nickNames,
        headImage,
        senderName,
        recommendInfo: unescape(item.recommendInfo),
        thumbUpCount,
        momentsGoodsInfo,
        showEditBtns,
        sourceId,
        materialType,
      };
    });
  },

  getMaterialType(momentsLinkInfo) {
    if (momentsLinkInfo) {
      return +materialTypeRevertMap[momentsLinkInfo.type];
    }
    return null;
  },

  getSourceId(momentsGoodsInfo) {
    const sourceId = get(momentsGoodsInfo, 'goodsInfo.sourceId');
    if (sourceId) {
      return sourceId;
    }
  },

  _processGoodsData(data) {
    if (!data) return;
    const { sl } = this.data;
    const {
      alias,
      goodsType,
      owlType,
      owlAlias,
      goodsState,
      join,
      image,
      price,
      title,
      sourceId,
    } = data;
    // goodsState: 0-正常，1-下架，2-删除
    const maskTextMap = {
      1: '已下架',
      2: '已删除',
    };
    const showImgMask = goodsState !== 0;
    const imgMaskText = maskTextMap[goodsState];
    let link = getGoodsWeappPath({
      alias,
      goodsType,
      owlAlias,
      owlType,
    });
    if (join) {
      link = addSalesmanParams({ url: link, sls: sl });
    }
    return {
      goodsInfo: {
        url: link,
        image,
        title,
        price,
        sourceId: +sourceId,
      },
      showImgMask,
      imgMaskText,
    };
  },

  _checkActionAccess() {
    return new Promise((resolve, reject) => {
      const { isActionDisabled } = this.data;
      if (isActionDisabled) {
        Toast('你还不是分销员，请成为分销员后重试');
        reject();
      } else {
        resolve();
      }
    });
  },
});
