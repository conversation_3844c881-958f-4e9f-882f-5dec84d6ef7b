<page-container
  page-container-class="zone {{ themeClass }}"
>
  <image
    class="zone-banner"
    src="{{ bannerImage }}"
    mode="aspectFill"
  />
  <no-data
    wx:if="{{ finished && list.length === 0 }}"
    can-edit="{{ canEdit }}"
    bind:publish="_handlePublish"
  />
  <view
    wx:if="{{ list.length > 0 }}"
    class="zone-content"
  >
    <feed-item
      custom-class="zone-feed-item {{ index === list.length - 1 ? 'zone-feed-item--last' : '' }}"
      wx:for="{{ list }}"
      wx:key="id"
      goods-title-class="goods-title"
      item-id="{{ item.contentId }}"
      logo="{{ item.headImage }}"
      title="{{ item.senderName }}"
      text="{{ item.recommendInfo }}"
      item="{{ item.momentsGoodsInfo }}"
      image-list="{{ item.imageList }}"
      video="{{ item.video }}"
      link-material="{{ item.momentsDetail || item.momentsLinkInfo }}"
      material-type="{{ item.materialType }}"
      material-id="{{ item.sourceId }}"
      note-type="{{item.momentsLinkInfo && item.momentsLinkInfo.noteType}}"
      link-url="{{item.momentsLinkInfo && item.momentsLinkInfo.linkUrl}}"
      logger-type="salesStory"
      show-logo
      bind:image-load="_handleImageLoad"
      bind:preview="_handleTriggerHide"
    >
      <view slot="after-media">
        <!-- user moment extra -->
        <user-location
          wx:if="{{ !!item.momentsLocation }}"
          custom-class="zone-feed-item__location"
          location="{{ item.momentsLocation }}"
          bind:open="_handleTriggerHide"
        />
      </view>
      <!-- action area -->
      <view
        slot="before-bottom"
        class="zone-feed-item__action-group"
      >
        <action-group
          show-edit-btns="{{ item.showEditBtns }}"
          disabled="{{ isActionDisabled }}"
          left-text="{{ item.sendAt }}"
          liked="{{ item.liked }}"
          thumb-up-count="{{ item.thumbUpCount }}"
          content-id="{{ item.contentId }}"
          bind:thumbup="_handleThumbUp"
          bind:edit="_handleEditZone"
          bind:delete="_handleDeleteZone"
        />
      </view>
      <!-- bottom -->
      <view
        wx:if="{{ item.thumbUpCount > 0 }}"
        slot="bottom"
        class="zone-feed-item__bottom"
      >
        <view class="zone-feed-item__icon" />
        <clamp
          custom-class="zone-feed-item__clamp"
          custom-action-class="zone-feed-item__clamp-action"
          content="{{ item.nickNames }}"
          max-line="5"
        />
      </view>
    </feed-item>
  </view>

  <view
    class="zone-feed-item__loading"
    style="display: {{ loading ? 'block' : 'none' }};"
  >
    <van-loading type="spinner" />
  </view>

  <view
    class="operation"
    style="display: {{ canEdit && !isActionDisabled && list.length > 0 ? 'block' : 'none' }};"
  >
    <view class="share">
      <salesman-cube
        scenes="zone"
        custom-icon
        need-bind-relation="{{false}}"
      >
        <van-icon
          slot="icon"
          name="share"
        />
      </salesman-cube>
    </view>
    <view
      class="photo theme-bg-color"
      style="background-color: {{ isActionDisabled ? '#c8c9cc!important' : '' }}"
      bind:tap="_handlePublish"
    >
      <van-icon name="photograph" />
    </view>
  </view>

  <picture-action-sheet
    show="{{ showActionSheet }}"
    need-set-global
    bind:close="_handleCloseActionSheet"
  />

  <nav
    sl="{{ sl }}"
    active-index="{{ 2 }}"
    hide-tab="{{ !showCard ? 'businessCard' : '' }}"
    extra-query="{{ queryParam }}"
  />

  <van-toast id="van-toast" />
  <van-dialog id="van-dialog" />
</page-container>

