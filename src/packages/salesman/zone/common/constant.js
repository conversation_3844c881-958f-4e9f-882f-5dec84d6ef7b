import { isWscChainStore } from '@youzan/utils-shop';
import { revertMap } from './utils';

export const PictureMaterialType = 0;
export const VideoMaterialType = 1;
export const FeatureMaterialType = 2;
export const NoteMaterialType = 3;

let materialTabsVal = [
  {
    title: '全部',
    materialTypes: [
      PictureMaterialType,
      VideoMaterialType,
      FeatureMaterialType,
      NoteMaterialType,
    ],
  },
  {
    title: '图片',
    materialTypes: [PictureMaterialType],
  },
  {
    title: '视频',
    materialTypes: [VideoMaterialType],
  },
  {
    title: '文章',
    materialTypes: [FeatureMaterialType],
  },
  {
    title: '笔记',
    materialTypes: [NoteMaterialType],
  },
];

// 微商城(教育)连锁店铺没有`店铺笔记`
if (isWscChainStore) {
  materialTabsVal = [
    {
      title: '全部',
      materialTypes: [
        PictureMaterialType,
        VideoMaterialType,
        FeatureMaterialType,
      ],
    },
    {
      title: '图片',
      materialTypes: [PictureMaterialType],
    },
    {
      title: '视频',
      materialTypes: [VideoMaterialType],
    },
    {
      title: '文章',
      materialTypes: [FeatureMaterialType],
    },
  ];
}

export const materialTabs = materialTabsVal;

export const LinkUrl = {
  note: {
    h5Url: 'https://h5.youzan.com/wscshop/shopnote/detail',
    weappUrl: '/packages/shop/shopnote/detail/index',
  },
  feature: {
    h5Url: 'https://h5.youzan.com/wscshop/showcase/feature',
    weappUrl: '/packages/home/<USER>/index',
  },
};

export const NoteMaterialTypeLink = 'SHOP_NOTE';
const FeatureMaterialTypeLink = 'ESSAY';

export const materialTypeMap = {
  [NoteMaterialType]: NoteMaterialTypeLink,
  [FeatureMaterialType]: FeatureMaterialTypeLink,
};

export const materialTypeRevertMap = revertMap(materialTypeMap);

export const SalesmanRoleType = 'shoppingGuide';

export const PromoterRoleType = 'directSeller';

export const MaterialSourceType = 'MATERIAL';

export const NoteTye = {
  WxArticle: 'mp_article',
};
