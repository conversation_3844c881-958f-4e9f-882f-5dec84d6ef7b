import { makeRandomString } from '@youzan/weapp-utils/lib/str';

export const PAGE_RANDOM_KEY = makeRandomString(8);

export const getMaterialBannerId = ({ materialId, spm }) => `${spm}.${materialId}~goods.1~1~${PAGE_RANDOM_KEY}`;

export function getFeatureImg({ coverUrl }) {
  if (coverUrl) {
    return [coverUrl];
  }
  return [];
}

export function getShopNoteImg(item) {
  if (item.coverPhotos && item.coverPhotos.length) {
    return item.coverPhotos;
  }
  if (item.coverUrl) {
    return [item.coverUrl];
  }
  if (item.noteItemBriefInfos && item.noteItemBriefInfos.length) {
    return [item.noteItemBriefInfos[0].imageUrl];
  }
  return [];
}

export function revertMap(obj) {
  return Object.keys(obj).reduce((res, key) => {
    res[obj[key]] = key;
    return res;
  }, {});
}

export const ACCOUNT_TYPE = {
  NORMAL: 0, // 普通用户
  SALESMAN: 1, // 分销员
  SALES: 2, // 导购员
  ALL: 3 // 分销员+导购员
};

// 有分销能力
const hasDirectSellerAbility = accountType => {
  return accountType === ACCOUNT_TYPE.SALESMAN || accountType === ACCOUNT_TYPE.ALL;
};

// 当前用户是否可以操作`动态`和`素材`
export const currentUserCanOperate = ({ canEdit, accountType }) => {
  if (canEdit && hasDirectSellerAbility(accountType)) {
    return true;
  }
  return false;
};

export function handleQueryParams(params) {
  const arr = params.split('!');
  return arr
    .map(item => item.split('~'))
    .filter(item => item.length > 1)
    .reduce((total, [a, b]) => {
      total[a] = b;
      return total;
    }, {});
}
