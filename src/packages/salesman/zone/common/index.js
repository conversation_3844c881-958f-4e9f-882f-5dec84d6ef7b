import args from '@youzan/weapp-utils/lib/args';
import { NoteMaterialType, LinkUrl, NoteTye } from './constant';

export const getLinkMaterialUrl = ({
  alias,
  materialType,
  kdtId,
  noteType,
  linkUrl,
}) => {
  let link = LinkUrl.feature;
  let params = {
    alias,
  };

  if (NoteMaterialType === materialType) {
    link = LinkUrl.note;
    params = {
      noteAlias: alias,
    };
  }

  if (noteType === NoteTye.WxArticle) {
    link = {
      h5Url: linkUrl,
      weappUrl: '/packages/shop/shopnote/mparticle/detail/index',
    };
  }

  return {
    url: args.add(link.h5Url, {
      ...params,
      sub_kdt_id: kdtId,
    }),
    weappUrl: args.add(link.weappUrl, params),
  };
};
