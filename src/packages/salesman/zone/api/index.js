const app = getApp();
const http = app.request;

// 获取选择商品列表
export function getSelectGoods(data) {
  return http({
    path: '/wscump/salesman/zone/getSelectGoods.json',
    data,
    method: 'GET',
  });
}

// 获取素材列表
export function getMaterialList(data) {
  return http({
    path: '/wscsalesman/promotion/promote/listMaterialPagedByConditionV2.json',
    data,
    method: 'POST',
  });
}

// 发布动态
export function publishMoments(data) {
  return http({
    path: '/wscump/salesman/zone/publishMoments.json',
    data,
    method: 'POST',
  });
}

// 获取动态
export function getMoments(data) {
  return http({
    path: '/wscump/salesman/zone/getMoments.json',
    data,
    method: 'GET',
  });
}

// 根据商品Id匹配素材
export function getMaterialByItemId(data) {
  return http({
    path: '/wscump/salesman/zone/getMaterialByItemId.json',
    data,
    method: 'GET',
  });
}

// 根据素材Id获取素材
export function getMaterialById(data) {
  return http({
    path: '/wscump/salesman/zone/getMaterialById.json',
    data,
    method: 'GET',
  });
}

// 根据Id获取商品信息
export function getGoodsInfo(data) {
  return http({
    path: '/wscump/salesman/getGoodsInfo.json',
    data,
    method: 'GET',
  });
}

// 编辑动态
export function editorMoments(data) {
  return http({
    path: '/wscump/salesman/zone/editorMoments.json',
    data,
    method: 'POST',
  });
}

// http://zanapi.qima-inc.com/site/service/view/429829
export const checkMomentsAccessAndGetBaseInfo = (data) => {
  return http({
    path: '/wscump/salesman/zone/check-moments-access-and-get-baseinfo.json',
    data,
  });
};

export const getShortUrl = (data) => {
  return http({
    path: '/wscump/salesman/zone/getShortUrl.json',
    data,
  });
};

export const getPersonalAbility = (kdtId) => {
  // 查询店铺是否有某个能力
  return app.requestUseCdn({
    path: '/wscsalesman/salesman/shop/checkShopAbility.json',
    method: 'GET',
    data: {
      kdtId,
      abilityName: 'salesman_advance_personal_space_ability',
    },
  });
};

export const getPosterSetting = () => {
  return http({
    path: '/wscump/salesman/poster/setting.json',
  });
};

export const createPoster = (data) => {
  return http({
    path: '/wscsalesman/poster-service/create-poster.json',
    data,
  });
};
