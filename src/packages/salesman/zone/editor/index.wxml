<page-container page-container-class="{{ themeClass }} page-{{ deviceType }}">
  <view class="editor-header">
    <!-- 文本编辑器 -->
    <editor
      id="editor"
      class="ql-container"
      placeholder="请填写推荐语"
      bindready="handleEditorReady"
      bindinput="handleEditorInput"
    />
    <!-- 文本操作栏 -->
    <view class="editor-header--bar">
      <view class="editor-header--bar__left">
        <view
          class="editor-header--bar__emoji"
          bind:tap="handleShowEmojiPick"
        >
          <image src="{{ 'https://img01.yzcdn.cn/image/salesman/zone/smile-'+ (showEmojiPicker ? 'i' : 'o') +'.png' }}" />
        </view>
      </view>
      <view class="editor-header--bar__limit">
        {{ contentLength }}/{{ editorMaxLength }}
      </view>
    </view>
  </view>

  <!-- 表情选择区 -->
  <view
    class="picker-wrap"
    style="height: {{ showEmojiPicker ? '180px' : 0 }}"
  >
    <emoji-picker bind:pick="handleEmojiPick" />
  </view>

  <view class="editor-main">
    <!-- 图片视频区 -->
    <view
      wx:if="{{ videoData.video && videoData.video.video_id }}"
      class="editor-main--video"
    >
      <showcase-video
        kdt-id="{{ videoKdtId }}"
        component-data="{{ videoData }}"
      />
      <van-icon
        name="clear"
        size="16px"
        color="#999"
        class="editor-main--video__delete"
        bind:tap="handleDeleteVideo"
      />
    </view>
    <image-upload
      wx:else
      pre-images="{{ preImages }}"
      hide-material="{{ hideMaterial }}"
      show-delete="{{ showImgDelete }}"
      bind:update="handleImageUpdate"
      bind:select-material="handleSelectMaterial"
    />
    <!-- 地址商品选择区 -->
    <van-cell-group class="editor-main--lg">
      <van-cell
        wx:if="{{ !linkInfo.alias }}"
        is-link
        bindtap="handleShowGoodsSelector"
        class="editor-main--lg__wrap"
      >
        <view class="editor-main--lg__nouse {{ goods.title && 'editor-main--lg__nouse-main' }}">
          <text>{{ goods.title || '添加商品' }}</text>
          <van-icon
            wx:if="{{ goods.title }}"
            name="clear"
            color="#c8c9cc"
            size="16px"
            catch:tap="handleDeleteGoods"
            class="editor-main--lg__wrap-clear"
          />
        </view>
        <van-icon
          slot="icon"
          name="bag"
          size="18px"
          color="#c8c9cc"
          custom-class="editor-main--lg__nouse-icon {{ goods.title && 'theme-color' }}"
        />
      </van-cell>
      <van-cell
        wx:else
        class="editor-main--lg__wrap"
      >
        <view class="editor-main--lg__nouse editor-main--lg__nouse-main editor-main--lg__nouse-all-width">
          <text>{{ linkInfo.title }}</text>
        </view>
        <van-icon
          slot="icon"
          class-prefix="iconfont"
          name="link"
          size="18px"
          color="#c8c9cc"
          custom-class="editor-main--lg__nouse-icon editor-main--lg__nouse-icon theme-color"
        />
      </van-cell>
      <van-cell
        is-link
        bindtap="handleMap"
        class="editor-main--lg__wrap"
      >
        <view class="editor-main--lg__nouse {{ location.name && 'editor-main--lg__nouse-main' }}">
          <text>{{ location.name || '添加地址' }}</text>
          <van-icon
            wx:if="{{ location.name }}"
            name="clear"
            color="#c8c9cc"
            size="16px"
            catch:tap="handleDeleteLocation"
            class="editor-main--lg__wrap-clear"
          />
        </view>
        <van-icon
          slot="icon"
          name="location"
          size="18px"
          color="#c8c9cc"
          custom-class="editor-main--lg__nouse-icon {{  location.name && 'theme-color' }}"
        />
      </van-cell>
    </van-cell-group>
    <!-- 发布操作区 -->
    <view class="editor-main--footer">
      <view
        class="editor-main--footer__fabu theme-shandow-color {{ canPublish ? 'theme-bg-color' : '' }}"
        bind:tap='handlePublish'
      >发布</view>
    </view>
  </view>

  <!-- 商品选择区 -->
  <goods-selector
    show="{{ showGoodsSelector }}"
    pre-goods="{{ preGoods }}"
    theme-bg-color="theme-bg-color"
    theme-color="theme-color"
    theme-border-color="theme-border-color"
    bind:select="handleGoodsSelect"
    bind:close="handleCloseGoodsSelector"
  />
  <van-dialog id="van-dialog" />
</page-container>
