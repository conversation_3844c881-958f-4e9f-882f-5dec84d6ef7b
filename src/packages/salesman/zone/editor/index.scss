@import '../components/custom-icon/index.scss';

.editor-header {
  width: 100vw;
  margin-bottom: 10px;
  background-color: #fff;

  .ql-container {
    width: 100vw;
    height: 96px;
    padding: 8px 16px;
    font-size: 15px;
    color: #333;
    min-height: auto;

    .ql-editor.ql-blank::before {
      color: #c8c9cc;
      content: attr(data-placeholder);
      font-style: normal;
      pointer-events: none;
      position: absolute;
    }
  }

  &--bar {
    width: 100vw;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 16px;

    &__left {
      display: flex;
      align-items: center;
    }

    &__emoji {
      width: 26px;
      height: 26px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background-color: #f2f3f5;
      margin-right: 32px;

      image {
        width: 18px;
        height: 18px;
      }
    }

    &__limit {
      font-size: 12px;
      color: #999;
    }
  }
}

.picker-wrap {
  width: 100vw;
  overflow: hidden;
  transition: all 300ms linear;
}

.editor-main {
  width: 100vw;
  min-height:calc(100vh - 146px);
  background-color:#fff;
  padding:0 15px 30px 15px;
  box-sizing: border-box;

  &--lg {
    &__wrap {
      position: relative;

      &-clear {
        position: absolute;
        top: 13px;
        right: 40px;
      }
    }

    &__nouse {
      color: #999;
      text-align: left;

      &-main {
        color: #333;
        width: 232px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      &-all-width {
        width: 100%;
      }
  
      &-icon {
        top: 3px;
        color: #999;
        margin-right:-10px;
        left:-14px;
      }
    }
  }

  &--video {
    position: relative;

    &__delete {
      position: absolute;
      top: -8px;
      right: -8px;
      z-index: 10;
    }
  }
}

.editor-main--footer {
  margin-top: 40px;
  display: flex;
  justify-content: flex-end;

  &__fabu {
    width: 70px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 18px;
    background: #dcdee0;
    color: #fff;
    font-size: 14px;
  }
}

.iconfont {
  position: relative;
  top: 0;  
}
