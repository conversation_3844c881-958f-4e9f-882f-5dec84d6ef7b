import WscPage from 'pages/common/wsc-page';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import navigate from 'shared/utils/navigate';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

import { NoteMaterialType, FeatureMaterialType, materialTypeMap, MaterialSourceType, NoteMaterialTypeLink } from '../common/constant';
import { getFeatureImg, getShopNoteImg } from '../common/utils';
import {
  getMaterialByItemId,
  publishMoments,
  getMoments,
  getMaterialById,
  getGoodsInfo,
  editorMoments
} from '../api';

const app = getApp();

WscPage({
  editorCtx: null,

  data: {
    videoKdtId: app.getKdtId(),
    params: {},
    showEmojiPicker: false,
    images: [],
    preImages: [],
    videoData: {},
    location: {},
    textContent: '',
    goods: {},
    // 上次选择的商品
    preGoods: {},
    contentLength: 0,
    canPublish: false,
    editorMaxLength: 200,
    showGoodsSelector: false,
    // 内容已发布
    isPublished: false,
    hideMaterial: false,
    linkInfo: {},
    materialType: '',
    showImgDelete: true,
  },

  onLoad(params) {
    let kdtId = app.getKdtId();
    const shopInfo = app.getShopInfoSync();
    if (shopInfo.chainStoreInfo && shopInfo.chainStoreInfo.rootKdtId) {
      kdtId = shopInfo.chainStoreInfo.rootKdtId;
    }
    this.setYZData({
      params,
      videoKdtId: kdtId,
    });
  },

  onUnload() {
    if (!this.data.isPublished) {
      this.saveDraft();
    }
  },

  saveDraft() {
    const {
      textContent,
      goods,
      location,
      images,
      videoData,
      contentLength,
      linkInfo,
    } = this.data;
    app.globalData.zoneDraft = {
      goods,
      location,
      preImages: images.map(item => { return { src: item.attachment_url || item.src } }),
      videoData,
      textContent,
      contentLength,
      linkInfo,
    };
  },

  getMoments(contentId) {
    wx.showLoading();
    getMoments({ contentId }).then(data => {
      const {
        imageList,
        recommendInfo = '',
        momentsGoodsInfo = {},
        momentsVideo = {},
        momentsLocation = {},
        momentsLinkInfo = {},
      } = data;
      this.setContents(unescape(recommendInfo));
      // 微信会将html转换为delta对象，编辑过就不会设置，先这么修改
      this.editorCtx.insertText({
        text: ' '
      });
      this.setYZData({
        preImages: imageList.map(url => { return { src: url } }),
        goods: momentsGoodsInfo,
        videoData: {
          video: {
            ...mapKeysCase.toSnakeCase(momentsVideo),
            coverHeight: momentsVideo.coverHeight,
            coverWidth: momentsVideo.coverWidth,
          }
        },
        location: momentsLocation,
        canPublish: true,
        linkInfo: momentsLinkInfo
      });
      wx.hideLoading();
    });
  },

  setDraftToData(draft) {
    const { textContent } = draft;
    this.setYZData({
      ...draft
    });
    this.setContents(textContent);
  },

  setContents(text) {
    this.editorCtx.setContents({
      html: text
    });
    this.setYZData({
      contentLength: text.length
    });
  },

  matchMaterial({ id, match, materialKdtId }) {
    // 区分商品匹配素材和发布素材
    let getMaterial = getMaterialById;
    let key = 'id';
    let itemId;
    const rest = {};
    const { editorMaxLength } = this.data;
    if (match) {
      getMaterial = getMaterialByItemId;
      key = 'itemId';
      itemId = id;
    } else if (materialKdtId) {
      rest.materialKdtId = materialKdtId;
    }
    wx.showLoading();
    getMaterial({
      [key]: id,
      ...rest
    }).then(data => {
      wx.hideLoading();
      const {
        textContent,
        itemMaterialDTO = {},
        imageMaterialDTOs = [],
        videoMaterialDTO = {},
        featureMaterial = {},
        shopNoteMaterial = {},
        materialType,
      } = data;
      const hasMaterial = JSON.stringify(data) !== '{}';
      // 商品单独调商品接口
      let { itemId: goodsId, kdtId } = itemMaterialDTO;
      goodsId = goodsId || itemId;
      if (goodsId) {
        this.getGoodsInfo({ itemId: goodsId, kdtId, hasMaterial });
      }
      if (hasMaterial) {
        let textCon = textContent;
        let images = imageMaterialDTOs.map(item => item.url);
        let linkInfo;
        if (FeatureMaterialType === materialType) {
          const { title } = featureMaterial;
          textCon = title;
          images = getFeatureImg(featureMaterial);
          linkInfo = this.setLinkInfo(featureMaterial, materialTypeMap[FeatureMaterialType]);
        }
        if (NoteMaterialType === materialType) {
          const { description, title } = shopNoteMaterial;
          let text = description;
          if (!text) {
            text = title;
          }
          textCon = text;
          images = getShopNoteImg(shopNoteMaterial);
          linkInfo = this.setLinkInfo(shopNoteMaterial, materialTypeMap[NoteMaterialType]);
        }
        textCon = textCon || '';
        this.setContents(textCon.slice(0, editorMaxLength)); // 最大长度限制，防止笔记文章等旧素材
        this.setYZData({
          preImages: images.map(item => { return { src: item } }),
          videoData: {
            video: {
              video_id: videoMaterialDTO.id,
              cover_url: videoMaterialDTO.coverUrl,
              coverHeight: videoMaterialDTO.coverHeight,
              coverWidth: videoMaterialDTO.coverWidth,
            }
          },
          contentLength: textCon.length,
          linkInfo,
          materialType,
          canPublish: images.length > 0 || videoMaterialDTO.id,
          textContent: textCon,
          showImgDelete: materialType !== FeatureMaterialType
        });
      }
    });
  },

  setLinkInfo(material, type) {
    let { alias, title, description } = material;
    const { materialId } = this.data.params;
    if (NoteMaterialTypeLink === type && !title) {
      title = description;
    }
    return {
      alias,
      type,
      title,
      linkId: materialId,
    };
  },

  getGoodsInfo({ itemId, kdtId, hasMaterial }) {
    const params = { itemId };
    const { materialId } = this.data.params;
    if (kdtId) {
      params.kdtId = kdtId;
    }
    getGoodsInfo(params).then(goods => {
      this.setYZData({
        goods: {
          ...goods,
          sourceId: materialId,
          sourceType: MaterialSourceType,
        }
      });
      // 没有素材，使用商品轮播图和推荐语
      if (!hasMaterial) {
        const { picture = '[]', sellPoint = '' } = goods;
        this.setContents(sellPoint);
        const images = [];
        JSON.parse(picture).forEach((item, index) => {
          // 取商品的最多9张图
          if (index < 9) {
            images.push({ src: item.url });
          }
        });
        this.setYZData({
          preImages: images,
          contentLength: sellPoint.length
        });
      }
    });
  },

  handleShowEmojiPick() {
    this.setYZData({
      showEmojiPicker: !this.data.showEmojiPicker
    });
  },

  handleEmojiPick(e) {
    const { text } = e.detail;
    this.editorCtx.insertText({
      text: `[${text}]`
    });
  },

  handleMap() {
    wx.chooseLocation({
      success: data => {
        const { name, latitude, longitude } = data;
        this.setYZData({
          location: {
            name,
            latitude,
            longitude
          }
        });
      }
    });
  },

  handleDeleteLocation() {
    this.setYZData({
      location: {}
    });
  },

  handleSelectMaterial() {
    // 点击素材中心保存草稿
    app.globalData.isFromSelectMaterial = true;
    this.saveDraft();
  },

  handleEditorReady() {
    wx.createSelectorQuery().select('#editor').context(res => {
      this.editorCtx = res.context;
      const { itemId, contentId, materialId, materialKdtId } = this.data.params;
      const draft = wx.getStorageSync('moments');
      if (contentId) {
        this.setYZData({
          hideMaterial: true
        });
        this.getMoments(contentId);
        return;
      }
      if (itemId || materialId) {
        this.matchMaterial({ id: itemId || materialId, match: Boolean(itemId), materialKdtId });
      } else if (draft) {
        this.setDraftToData(draft);
      }
    }).exec();
  },

  handleEditorInput(e) {
    const { text } = e.detail;
    const textLength = text.length - 1; // 有个转折号的长度
    this.setYZData({
      contentLength: textLength,
      textContent: text
    });
    if (textLength > this.data.editorMaxLength) {
      this.setContents(text.slice(0, this.data.editorMaxLength));
    }
  },

  handleImageUpdate(e) {
    const { images, type } = e.detail;
    if (type === 'upload') {
      this.setYZData({
        images: this.data.images.concat([images]),
        canPublish: true
      });
    } else {
      let canPublish = true;
      if (!images.length) {
        canPublish = false;
      }
      this.setData({
        images,
        canPublish
      });
    }
  },

  handleShowGoodsSelector() {
    this.setYZData({
      showGoodsSelector: true
    });
  },

  handleDeleteGoods() {
    this.setYZData({
      goods: {}
    });
  },

  handleCloseGoodsSelector() {
    this.setYZData({
      showGoodsSelector: false
    });
  },

  handleGoodsSelect(e) {
    const { goods } = e.detail;
    this.setYZData({
      goods,
      preGoods: goods
    });
  },

  handleDeleteVideo() {
    this.setYZData({
      videoData: {},
      canPublish: false
    });
  },

  handlePublish() {
    if (this.data.canPublish) {
      app.logger && app.logger.log({
        et: 'click',
        ei: 'storysend',
        en: '动态发送次数',
        params: {}
      });
      wx.showLoading({
        title: '发布中'
      });
      this.editorCtx.getContents({
        success: res => {
          const { html, delta } = res;
          let recommendInfo = '';
          if (html !== '<p><br></p>') {
            recommendInfo = html;
          } else if (delta.ops[0].insert) {
            recommendInfo = delta.ops[0].insert;
          }

          const {
            images, location, videoData, goods, params, linkInfo
          } = this.data;

          // 编辑动态和发布动态不同接口
          let momentsApi = publishMoments;
          let body = {
            imageList: images.map(item => cdnImage(item.attachment_url || item.src)),
            location,
            video: mapKeysCase.toCamelCase(videoData.video),
            goods,
            recommendInfo: escape(recommendInfo),
            momentsLinkInfo: linkInfo,
          };
          if (params.contentId) {
            momentsApi = editorMoments;
            body = {
              ...body,
              contentId: params.contentId
            };
          }

          momentsApi(body).then(() => {
            app.globalData.zoneDraft = null;
            wx.setStorage({
              key: 'moments',
              data: null
            });
            this.setYZData({
              isPublished: true
            });
            wx.hideLoading();
            navigate.navigate({
              url: '/packages/salesman/zone/home/<USER>'
            });
          }).catch((e) => {
            wx.showToast({
              title: e.msg || '发布失败，请稍后重试',
              icon: 'none'
            });
          });
        }
      });
    }
  }
});
