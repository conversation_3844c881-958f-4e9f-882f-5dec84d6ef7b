import WscPage from 'pages/common/wsc-page/index.js';
import args from '@youzan/weapp-utils/lib/args';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';

const app = getApp();

const http = app.request;

function getTutorial(query) {
  return http({
    path: '/wscump/salesman/getTutorial.json',
    query,
  });
}

function getRecruitShareConfig() {
  return http({
    path: '/wscsalesman/getRecruitShareConfig.json',
  }).catch(() => {});
}

const getUrlByOptions = (options) => {
  const { kdtId = app.getKdtId(), sls = '', dbid } = options || {};
  let url = 'https://h5.youzan.com/wscump/salesman/tutorial';
  url = args.add(url, { kdt_id: kdtId, from: 'weapp_' + app.getFansType() });
  if (sls) {
    url = args.add(url, { from_seller: sls });
  }
  if (dbid) {
    const data = app.db.get(options.dbid);
    if (data) {
      const payResult = data.pay_result || '';
      const payMoney = data.pay_money || '';
      if (payResult === 'success') {
        url = args.add(url, { pay_result: 'success', pay_money: payMoney });
      }
    }
  }
  return url;
};

WscPage({
  data: {
    src: '',
    salesmanAlias: '',
    shareConfig: {
      shareContent: '',
      shareTitle: '招募计划',
      sharePicture:
        'https://img01.yzcdn.cn/public_files/2018/05/28/c3ee2694f593ec6e08d9a44b8eb4e79e.png',
    },
  },
  onShow() {},
  onLoad(options) {
    this.setYZData({
      src: getUrlByOptions(options),
    });
    const kdtId = app.getKdtId();
    const buyerId = app.getBuyerId();
    const fromSeller = options.sls || '';
    const query = {
      kdtId,
      buyerId,
      fromSeller,
    };
    Promise.all([getTutorial(query), getRecruitShareConfig()]).then(
      ([res, shareConfig = null]) => {
        this.setYZData({
          salesmanAlias: res.sls || '',
          title: res.title || '招募计划',
          shareConfig: shareConfig || this.data.shareConfig,
        });
      }
    );
  },
  onShareAppMessage() {
    let sharePath = '/packages/salesman/tutorial/index';
    if (this.data.salesmanAlias) {
      sharePath = addSalesmanParams({
        url: sharePath,
        sls: this.data.salesmanAlias,
      });
    }
    return {
      path: sharePath,
      title: this.data.shareConfig.shareTitle,
      imageUrl: this.data.shareConfig.sharePicture,
    };
  },
});
