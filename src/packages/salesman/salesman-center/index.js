import WscPage from 'pages/common/wsc-page/index';

const app = getApp();
const http = app.request;

WscPage({
  data: {
    src: '',
    refreshPath: null,
  },

  onShow() {
    if (this.data.refreshPath) {
      wx.showLoading();

      wx.redirectTo({
        url: `/packages/salesman/salesman-center/index?t=${Date.now()}&refreshPath=${
          this.data.refreshPath
        }`,
      });

      this.setYZData({
        refreshPath: null,
      });
    }
    this.setTitle();
  },

  onLoad(options) {
    const { kdtId = app.getKdtId() } = options || {};
    let url = `https://h5.youzan.com/wscump/salesman/index?kdtId=${options.dsKdtId ||
      kdtId}`;

    if (options.refreshPath) {
      url += `#/${options.refreshPath}`;
    }

    if (url.indexOf('https://h5.youzan.com') < 0) {
      app.logger.appError({
        name: 'wsc',
        message: 'check enter shop url',
        detail: {
          appId: app.getAppId(),
          kdtId,
          buyerId: app.getBuyerId(),
          appVersion: app.getVersion(),
          mobile: app.getMobile(),
          url,
          time: new Date().toLocaleString(),
        },
      });
    }

    this.setYZData({
      refreshPath: null,
      src: url,
    });
  },

  setTitle() {
    http({
      path: '/wscump/salesman/getAccount.json',
      method: 'GET',
    }).then((data) => {
      const { settingName } = data;
      wx.setNavigationBarTitle({
        title: `${settingName || '分销员'}中心`
      });
    });
  },
});
