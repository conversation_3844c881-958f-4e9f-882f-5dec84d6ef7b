import WscPage from 'pages/common/wsc-page';
import resolveMsg from 'pages/common/wsc-page/utils/resolve-msg';
import args from '@youzan/weapp-utils/lib/args';

const app = getApp();

WscPage({
  data: {
    src: '',
    shareImg: 'https://img01.yzcdn.cn/images/salesman/rank/rank_poster.png',
  },

  onLoad(query) {
    const kdtId = query.kdtId || app.getKdtId();
    const {
      interval = '',
      rankType = '',
    } = this.__query__;

    const url = args.add('https://h5.youzan.com/wscump/salesman/rank', {
      kdt_id: kdtId,
      interval,
      rankType,
    });

    this.__webviewMsg = {};

    this.setYZData({
      src: url
    });
  },

  handlePostMessage(ev) {
    resolveMsg.call(this, ev.detail.data || []);
  },

  onShareAppMessage() {
    return this.__webviewMsg['ZNB.share'] || {};
  },
});
