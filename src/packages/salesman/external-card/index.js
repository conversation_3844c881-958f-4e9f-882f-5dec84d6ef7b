import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    src: '',
  },

  onLoad() {
    const kdtId = app.getKdtId();
    const { vcardId, kdtId: salesmanKdtId } = this.__query__;
    const url = `https://h5.youzan.com/wscump/salesman/business-card?kdt_id=${salesmanKdtId ||
      kdtId}#/external/${vcardId}`;

    this.setYZData({
      src: url,
    });
  },
  onMessage(event) {
    const messages = event.detail.data;
    const shareMessages = messages.filter(
      (message) => message.type === 'ZNB.share'
    );
    this.shareConfig = shareMessages.pop().config;
  },
  onShareAppMessage() {
    return this.shareConfig || {};
  },
});
