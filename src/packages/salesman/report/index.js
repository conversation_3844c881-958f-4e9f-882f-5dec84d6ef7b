import WscPage from 'pages/common/wsc-page';
import resolveMsg from 'pages/common/wsc-page/utils/resolve-msg';
import args from '@youzan/weapp-utils/lib/args';

const app = getApp();

WscPage({
  data: {
    src: '',
    shareImg: 'https://img01.yzcdn.cn/images/salesman/rank/report_poster.png',
  },

  onLoad() {
    const kdtId = app.getKdtId();
    const {
      startDay,
      endDay,
      encryptData,
    } = this.__query__;

    let url = args.add('https://h5.youzan.com/wscump/salesman/report', { kdt_id: kdtId });
    if (startDay && endDay) {
      url = args.add(url, { startDay, endDay });
    }
    if (encryptData) {
      url = args.add(url, { encryptData });
    }

    this.__webviewMsg = {};

    this.setYZData({
      src: url
    });
  },

  handlePostMessage(ev) {
    resolveMsg.call(this, ev.detail.data || []);
  },

  onShareAppMessage() {
    return this.__webviewMsg['ZNB.share'] || {};
  }
});
