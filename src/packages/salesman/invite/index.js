// TODO 整个换成common webview
import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    src: '',
  },

  dsKdtId: 0,

  onLoad(query) {
    const { sls } = query;
    const kdtId = query.kdtId || query.kdt_id;
    if (kdtId && sls) {
      this.setYZData({
        src: `https://h5.youzan.com/wscump/salesman/invite?kdt_id=${kdtId}&from_seller=${sls}`,
      });
    } else {
      // 兼容老的逻辑，不知道是否还有别的路口了。
      this.initWithoutQuery(kdtId);
    }
  },

  initWithoutQuery(kdtId) {
    wx.showLoading({ title: '正在加载' });
    this.getSalesmanAccount()
      .then((sls) => {
        if (this.dsKdtId) {
          this.setYZData({
            src: `https://h5.youzan.com/wscump/salesman/invite?kdt_id=${this.dsKdtId}&from_seller=${sls}`,
          });
        } else {
          this.setYZData({
            src: `https://h5.youzan.com/wscump/salesman/remind?kdt_id=${kdtId}&remindType=1`,
          });
        }
      })
      .catch((e) => console.error(e))
      .then(() => {
        wx.hideLoading();
      });
  },

  getSalesmanAccount() {
    return new Promise((resolve, reject) => {
      app
        .request({
          method: 'GET',
          path: '/wscump/salesman/getAccount.json',
        })
        .then((data) => {
          this.setYZData({
            sls: data.sls,
          });
          this.dsKdtId = data.dsKdtId;
          resolve(data.sls);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
});
