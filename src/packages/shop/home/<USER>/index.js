import Toast from '@vant/weapp/dist/toast/toast';
import coupon from 'components/coupon/coupon-cell';
import WscPage from '../../../../pages/common/wsc-page/index';

const app = getApp();
let disable_unshare;
let include_understock;
let page_size;
let method;
let ids;
let loadmore = false;
const PERPAGE_SIZE = 20;
/*
微页面优惠券领取列表
*/
WscPage(coupon, {

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    page: 1,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let component = JSON.parse(options.component || {});
    method = component.coupon_source; // 0:自动获取 1:手动获取
    let coupon_list = component.list || []; // 手动获取传数组
    page_size = method == 0 ? component.coupon_num : coupon_list.length;
    disable_unshare = component.hide_unshared_coupon == 1 ? 1 : 0;
    include_understock = component.hide_empty_coupon == 1 ? 0 : 1;
    ids = coupon_list.join(',');
    this.fetchList();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (page_size == 0) {
      loadmore = true;
    }
    // page = 1;
    this.setYZData({ page: 1 });
    this.fetchList();
  },

  /**
    * 页面上拉触底事件的处理函数
    */
  onReachBottom() {
    if (!loadmore) {
      return;
    }
    this.fetchList();
  },

  fetchList() {
    let params = {
      with_user_status: app.getBuyerId() ? 1 : 0,
      disable_unshare,
      include_understock,
      perpage: page_size == 0 ? PERPAGE_SIZE : page_size,
      page: this.data.page,
      types: '7',
    };
    if (method == 1) {
      params.ids = ids;
    }
    wx.showLoading({
      title: '加载中',
    })
    app.carmen({
      api: 'youzan.ump.coupon/1.0.0/search',
      query: params,
      success: (response) => {
        let listResponse = response.list || [];
        let convertedList = [];
        listResponse.forEach((item) => {
          let coupon = this.handleCouponData(item);
          convertedList.push(coupon);
        })
        let oldlist = this.data.page == 1 ? [] : this.data.list;
        if (page_size == 0 && listResponse.length == PERPAGE_SIZE) {
          loadmore = true;
          let page = this.data.page + 1;
          this.setYZData({
            page
          });
        } else {
          loadmore = false;
        }
        this.setYZData({ list: oldlist.concat(convertedList) });
      },
      fail: (res) => {
        Toast(res.msg || '获取信息失败');
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  onMoredescTaped(coupon, index) {
    this.setYZData({
      [`list[${index}]`]: coupon,
    });
  },

  onCouponObtainedSuccess(coupon, index) {
    this.setYZData({
      [`list[${index}]`]: coupon
    });
  },
});
