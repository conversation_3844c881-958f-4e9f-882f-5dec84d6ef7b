import { featureSkuUtilsPromise } from 'shared/utils/async-base-sku';

const app = getApp();
export default {
  onLoad() {
    this.on('component:sku:cart', (res) => {
      if (res.type === 'add') {
        // search页面 Toast不出来，先用wx.showToast;
        // Toast('添加购物车成功');
        wx.showToast({
          title: '添加购物车成功',
          icon: 'none',
        });
      }
    });
  },
  showcaseHandleGoodsBuy(e) {
    const alias = e.currentTarget.dataset.alias || e.detail.alias;
    const extraInfo =
      e.currentTarget.dataset.extraInfo || e.detail.extraInfo || {};
    const slg = extraInfo.slg || '';
    const alg = extraInfo.alg || '';
    // 购物策按钮点击-为加入购物车和立即购买提前设置 slg 上下文 透传埋点
    app.logger.setContext(
      {
        slg,
        alg_id: alg,
      },
      30
    );
    const { tagsList } = e.detail;
    let activityTypes = null;
    if (tagsList && tagsList.length) {
      try {
        activityTypes = JSON.stringify(tagsList.map((item) => item.type));
      } catch (error) {}
    }

    featureSkuUtilsPromise()
      .then(({ getSkuData }) => getSkuData(alias, { activityTypes }))
      .then((skuData) => {
        this.setYZData({
          featureSkuData: skuData,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  },
  handleSkuClose() {
    this.setYZData({
      'featureSkuData.showGoodsSku': false,
    });
  },
};
