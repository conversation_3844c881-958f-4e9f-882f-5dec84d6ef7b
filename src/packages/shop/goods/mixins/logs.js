import { LOG_VIEW_PARAM } from '../constant/index';
import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';

const app = getApp();
export default {
  setGoodsViewLog(data, type = 'search') {
    // 商品列表-商品曝光 slg alg_id 上下文 透传埋点
    const extParams = {
      slg: (data[0].extra_info && data[0].extra_info.slg) || '',
      alg_id: (data[0].extra_info && data[0].extra_info.alg) || '',
    };
    if (type !== 'search') {
      extParams.words = '';
      extParams.words_type = '';
    }
    app.logger.setContext(extParams, 30);
    // 延迟埋点，保障slg和alg_id更新
    setTimeout(() => {
      this.__yzLog__(
        Object.assign(LOG_VIEW_PARAM[type], {
          params: {
            goods: this.formatData(data),
          },
        })
      );
      // 商品曝光
      ensureAppLogger(
        getComponentLoggerParams('view', this.formatLoggerList(data))
      );
    }, 1000);
  },
  formatData(data) {
    const result = data.map((goods) => ({
      goods_id: goods.alias,
      goods_name: goods.title,
    }));
    return JSON.stringify(result);
  },
  formatLoggerList(data) {
    const loggerList = [];
    data.forEach((item) => {
      loggerList.push(item.loggerParams);
    });
    return loggerList;
  },
};
