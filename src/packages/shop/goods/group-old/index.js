import Toast from '@vant/weapp/dist/toast/toast';
import { moment } from '@youzan/weapp-utils/lib/time';
import { shopCanPayForKnowledge } from 'utils/judgement';
import WscPage from 'pages/common/wsc-page/index';
import openWebView from 'shared/utils/open-web-view';
import group from './types/group';
import coupon from './types/coupon';
import groupon from './types/groupon';
import { initSalesman } from 'shared/utils/salesman-share';
import { getPlugins } from '@youzan/ranta-helper-tee';
import { EShoppingGuideBindSourceType } from 'constants/guide';

const { dmc } = getPlugins();

const app = getApp();
const PAGE_SIZE = 8;

/**
 * pageType === 'group' 商品分组已迁移至
 * /packages/shop/goods/tag-list/index
 * @type {string}
 */
const PAGETYPE_GOODSGROUP = 'group';
const PAGETYPE_GOODSCOUPON = 'coupon';
const PAGETYPE_GOODSGROUPON = 'groupon';

const Types = {
  group,
  coupon,
  groupon,
};

// 从 微页面的拼团类型 到 营销的拼团类型 转换
const FEATURE_2_UMP_TYPE_MAP = {
  0: { activityType: 4, groupType: 0 },
  1: {},
  2: { activityType: 4, groupType: 1 },
  3: { activityType: 26 },
};

WscPage({
  data: {
    themeClass: app.themeClass,
    pageType: '',
    alias: '',
    fetching: true,
    requestParams: {},
    // 优化用
    loading: false,
    nomore: false,
    nodata: false,
    salesman: {},
    tab: {
      list: [
        {
          id: 'paidColumn',
          title: '知识付费',
        },
        {
          id: 'normalGoods',
          title: '其他商品',
        },
      ],
      selectedId: 'paidColumn',
      height: 45,
      isFixed: false,
      isShow: false,
      paidData: {},
      groupalias: '',
    },
  },

  fetchAlias(currentAlias) {
    return new Promise((resolve) => {
      app.getShopInfo().then((data) => {
        const { chainStoreInfo } = data;
        const { isRootShop = false, isMultiOnlineShop = false } =
          chainStoreInfo;

        // 只有连锁多网店才进行alias映射
        if (!isMultiOnlineShop) {
          resolve(currentAlias);
          // 如果是总店则监听进店事件
        } else if (isRootShop) {
          app.once('app:chainstore:kdtid:update', ({ kdtId }) => {
            this.getAliasPromise(currentAlias, kdtId).then((alias) => {
              this.setYZData({ alias });
              resolve(alias);
            });
          });
          // 如果是分店则直接更新alias
        } else {
          this.getAliasPromise(
            currentAlias,
            app.getKdtId() || data.kdt_id
          ).then((alias) => {
            this.setYZData({ alias });
            resolve(alias);
          });
        }
      });
    });
  },

  // 获取真正的alias
  // 如果是连锁店，需要做alias的映射，否则单店直接使用query中的alias
  getAliasPromise(currentAlias, kdtId) {
    if (currentAlias) {
      return app
        .request({
          path: '/wscshop/api/showcase-retail/getRetailSubShopTagInfo.json',
          data: {
            source_alias: currentAlias,
            target_kdt_id: kdtId,
          },
        })
        .then((data) => {
          return data.alias;
        })
        .catch(() => {
          return currentAlias;
        });
    }

    return new Promise((resolve) => {
      resolve(currentAlias);
    });
  },

  fetchGroupPage(query) {
    this.fetchAlias(query.alias).then((alias) => {
      // group:商品分组，coupon:优惠券可用列表
      const pageType = query.pageType || PAGETYPE_GOODSGROUP;
      this.data.pageType = pageType;
      this.typeComponent = Types[pageType];
      // 低版本手机不对特殊字符 encode，会导致请求数据为空
      delete query.title;
      if (
        [
          PAGETYPE_GOODSCOUPON,
          PAGETYPE_GOODSGROUP,
          PAGETYPE_GOODSGROUPON,
        ].indexOf(pageType) === -1
      ) {
        wx.reLaunch({ url: '/packages/home/<USER>/index' });
        return;
      }
      // 商品分组但是没有 alias
      if (pageType === PAGETYPE_GOODSGROUP && !alias) {
        wx.reLaunch({ url: '/packages/home/<USER>/index' });
        return;
      }

      if (pageType === PAGETYPE_GOODSCOUPON && !query.group_id) {
        wx.reLaunch({ url: '/packages/home/<USER>/index' });
        return;
      }

      if (pageType === PAGETYPE_GOODSGROUPON) {
        query.component = JSON.parse(query.component || '{}');
        const {
          grouponType = 0,
          showPrice = true,
          hideGoodsSold = 0,
        } = query.component;
        this.typeComponent.components[0].list = []; // 需要重置 list
        this.typeComponent.components[0].grouponType = grouponType;
        this.typeComponent.components[0].showPrice = showPrice;
        this.typeComponent.params.hideSoldOutGoods = hideGoodsSold;

        wx.setNavigationBarTitle({ title: '全部拼团' });
      }

      if (pageType === PAGETYPE_GOODSCOUPON) {
        // list 是全局的，进入的时候需要先重置一下
        this.typeComponent.components[0].list = [];
      }

      this.tagPromise = Promise.resolve();
      if (pageType === PAGETYPE_GOODSGROUP) {
        this.tagPromise = app
          .request({
            path: '/wscshop/showcase/tag/detail.json',
            data: {
              alias: query.alias,
            },
          })
          .then((res) => {
            if (res && res.components && res.components.length) {
              res.components[2].goods = [];
              this.showShowcaseComponents([res.components[2]], 3);
              // 修改标题
              const { subtitle, title } = res.components[0];
              const pageTitle = subtitle || title || '商品分组';
              wx.setNavigationBarTitle({
                title: pageTitle,
              });
              this.groupPageTitle = pageTitle;
            }
          })
          .catch(() => {
            this.showShowcaseComponents(this.typeComponent.components, 3);
          });
      } else {
        this.showShowcaseComponents(
          this.typeComponent.components,
          3,
          false,
          false,
          false,
          true
        );
      }

      query.page = 1;
      if (pageType === PAGETYPE_GOODSCOUPON && query.group_id) {
        // 判断优惠券 存在特定商品类型
        this.typeComponent
          .getCouponSupportGoodsInfo({
            groupId: query.group_id,
          })
          .then((res) => {
            this.setYZData({
              pageType: 'coupon',
            });
            if (res.exist_normal_goods && res.exist_owl_goods) {
              // 知识付费和 普通商品都有 显示tab
              this.setYZData({
                'tab.isShow': true,
                fetching: true,
              });
              // 默认显示 知识付费 默认获取全部内容
              this.fetchPaidcontentList({ owlType: 0 });
            } else if (res.exist_normal_goods) {
              //  不显示tab
              this.setYZData({
                'tab.isShow': false,
                'tab.selectedId': 'normalGoods',
              });
              this.setYZData({ fetching: true });
              this.fetchList(query).then(() => {
                this.setYZData({ fetching: false });
              });
            } else {
              // 只有知识付费商品
              this.setYZData({
                'tab.isShow': false,
                fetching: true,
              });
              this.fetchPaidcontentList({ owlType: 0 });
            }
          });
        this.groupId = query.group_id;
      } else {
        this.setYZData({ fetching: true });
        this.tagPromise.then(() => {
          if (alias && alias !== query.alias) {
            query.alias = alias;
          }
          this.fetchList(query).then(() => {
            this.setYZData({ fetching: false });
          });
        });
      }

      if (pageType === PAGETYPE_GOODSGROUP) {
        // 商品分组 取 groupalias， 供后面分享链接用
        this.groupalias = alias;
        this.setYZData({
          groupalias: alias,
        });
      } else {
        // 不是商品分组，不显示 转发按钮
        wx.hideShareMenu();
      }
    });
  },

  onLoad(query) {
    const { pageType, alias } = query;
    // 商品分组重定向到新的商品分组页面
    if (!pageType || (pageType === 'group' && alias)) {
      dmc.redirectTo('GoodsTag', { alias });
      return;
    }
    // 如果是优惠券过来的且带有优惠券alias的路径，重定向到新版可用商品列表页
    if (pageType === 'coupon' && alias) {
      wx.redirectTo({
        url: `/packages/user/coupon/goods-list/index?alias=${alias}`,
      });
      return;
    }

    if (pageType === 'groupon') {
      const { isGrouponPro, grouponType } = JSON.parse(query.component || '{}');

      if (isGrouponPro) {
        openWebView('/wscump/groupon/list', {
          title: '全部拼团',
          query: {
            ...FEATURE_2_UMP_TYPE_MAP[grouponType],
            kdtId: app.getKdtId(),
          },
          method: 'redirectTo',
        });

        return;
      }
    }
    initSalesman.call(this, {
      sst: 8,
      gst: EShoppingGuideBindSourceType.TINY_PAGE,
    });

    // 多网点重新选择网点后要更新页面数据
    this.on('app:offlineId:change', () => {
      this.fetchGroupPage(query);
    });
    this.fetchGroupPage(query);
  },
  handleTabChange(event) {
    const activeIndex = event.detail.index;
    const newSelectedId = this.data.tab.list[activeIndex].id;
    const oldSelectedId = this.data.tab.selectedId;
    if (oldSelectedId === newSelectedId) {
      return;
    }
    this.setYZData({
      'tab.selectedId': newSelectedId,
    });
    // 没有优惠券商品数据 才去 获取新的数据（仅限于普通商品）
    if (newSelectedId === 'normalGoods' && !this.fetchedCouponGoods) {
      this.fetchList({
        page: 1,
        group_id: this.groupId,
      }).then(() => {
        this.fetchedCouponGoods = true;
        this.setYZData({ fetching: false });
      });
    } else if (newSelectedId === 'paidColumn') {
      // 获取优惠券 知识付费商品
      this.fetchPaidcontentList({ owlType: 0 });
    }
  },

  // 获取知识付费相关内容
  fetchPaidcontentList(query) {
    const { loading } = this.data;
    const params = {
      group_id: this.groupId,
      owlType: query.owlType,
    };
    this.setYZData({ loading: true });

    !loading &&
      this.typeComponent.fetchOwlList(params).then((data) => {
        console.log('data', data);
        this.parsePaidData(data, query.owlType);
      });
  },

  parsePaidData(data, owlType) {
    owlType = +owlType;
    let { paidData } = this.data;
    const columns = data.columnList || [];
    const contents = data.contentList || [];

    if (owlType === 0) {
      paidData = data;
    } else if (owlType === 1) {
      paidData.columnList = (paidData.columnList || []).concat(columns);
    } else {
      paidData.contentList = (paidData.contentList || []).concat(contents);
    }

    (paidData.columnList || []).forEach((item) => {
      item.showPriceInfo = shopCanPayForKnowledge();
    });

    (paidData.contentList || []).forEach((item) => {
      item.publishAt = moment(item.publishAt, 'YYYY-MM-DD');
      item.showPriceInfo = shopCanPayForKnowledge();
    });

    this.setYZData({
      fetching: false,
      loading: false,
      paidData,
    });
  },

  // 获取更多知识付费内容
  fetchMorePaidContent(evt) {
    const owlType = evt.target.dataset.type;
    this.fetchPaidcontentList({ owlType });
  },

  fetchList(query) {
    this.setYZData({ loading: true });
    const shopInfo = app.getShopInfoSync();
    if (shopInfo.chainStoreInfo && shopInfo.chainStoreInfo.rootKdtId) {
      query.root_kdt_id = shopInfo.chainStoreInfo.rootKdtId;
    }
    return this.typeComponent
      .fetch(query)
      .then((list) => {
        this.setYZDataList(list);
      })
      .catch((err) => {
        Toast(err.msg || '获取信息失败');
        this.typeComponent.params.page--;
      })
      .then(() => this.setYZData({ loading: false }));
  },
  setYZDataList(list) {
    const component = this.data.theme.feature[0] || {};
    const goodsList = (component[this.typeComponent.listName] || []).concat(
      list
    );
    component[this.typeComponent.listName] = goodsList;
    component.goods = goodsList;

    this.setYZData({
      'theme.feature[0]': component,
      loading: false,
      nomore: list.length < PAGE_SIZE,
      nodata: component[this.typeComponent.listName].length === 0,
    });
  },

  // 滚动触底，原生钩子
  onReachBottom() {
    if (this.data.loading || this.data.nomore || this.data.nodata) return;
    if (
      this.data.pageType !== 'coupon' ||
      this.data.tab.selectedId === 'normalGoods'
    ) {
      this.fetchList({
        page:
          (this.typeComponent.params.page ||
            this.typeComponent.params.page_no) + 1,
      });
    }
  },

  // 增加分享钩子
  onShareAppMessage() {
    // 仅仅增加 商品分组页的分享
    const sharePath = `/packages/shop/goods/group/index?alias=${this.groupalias}`;
    return {
      path: sharePath,
      title:
        this.groupPageTitle ||
        (this.typeComponent && this.typeComponent.groupPageTitle) ||
        '商品分组',
    };
  },
});
