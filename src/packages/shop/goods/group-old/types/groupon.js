import get from '@youzan/weapp-utils/lib/get';

const app = getApp();

export default {
  listName: 'list',
  params: {
    page: 1,
    pageSize: 20,
    orderRule: '2',
    from: 'weapp',
    v: '2'
  },
  components: [
    {
      skipFetch: true,
      list: [],
      layout: 1,
      sizeType: 0,
      pageMargin: 15,
      goodsMargin: 10,
      borderRadiusType: 1,
      showTitle: true,
      showSubTitle: false,
      showPrice: true,
      showOriginPrice: true,
      showCountDown: false,
      showGrouponNum: true,
      showBuyButton: false,
      buyButtonType: 8,
      showMoreButton: false,
      imageRatio: 1,
      type: 'groupon'
    }
  ],
  fetch(params) {
    Object.assign(this.params, params);
    const requestData = { kdtId: app.getKdtId(), ...this.params };
    const grouponType = +this.components[0].grouponType || 0;

    return new Promise((resolve, reject) => {
      // 抽奖拼团↓↓↓
      if (grouponType === 1) {
        app
          .request({
            path: '/wscshop/ump/groupon/grouponLuckyList.json',
            data: requestData
          })
          .then(({ list = [] }) => {
            resolve(this.format(list));
          })
          .catch(err => {
            reject(err);
          });

        return;
      }

      // 多人拼团↓↓↓
      app
        .request({
          path: '/wscshop/ump/groupon/autoObtain.json',
          data: requestData
        })
        .then(({ list = [] }) => {
          resolve(this.format(list));
        })
        .catch(err => {
          reject(err);
        });
    });
  },
  format(list = []) {
    const { grouponType, showGrouponNum } = this.components[0];

    return list
      .filter(item => +get(item, 'goodsInfo.isDisplay', 1) !== 0)
      .map(item => {
        const { alias, id, extraTagText } = item;
        const url = +grouponType === 1
          ? `/packages/goods/lucky-draw-group/index?alias=${alias}&type=luckyDrawGroup&activityId=${id}`
          : `/packages/goods/groupon/index?alias=${alias}`;

        return {
          ...item,
          url, // 覆盖 H5 的商品链接
          extraTagText: showGrouponNum ? extraTagText : '' // 如果不显示成团人数，则把 tagText 置为空串
        };
      });
  }
};
