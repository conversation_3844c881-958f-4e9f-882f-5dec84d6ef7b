<!-- 微杂志页面 -->
<import src="./templates/owl-goods/index.wxml" />

<page-container
  show-shop-status
  switch-page-options="{{ { alias } }}"
  class="{{ themeClass }} page-{{ deviceType }}">
  <van-tabs
    wx:if="{{ tab.isShow }}"
    active="{{ tab.selectedId }}"
    bind:change="handleTabChange"
  >
    <van-tab
      title="{{ item.title }}"
      wx:for="{{ tab.list }}"
      wx:key="{{ item.id }}"
    />
  </van-tabs>
  <template
    wx:if="{{ !fetching && (pageType === 'coupon' && tab.selectedId === 'paidColumn') }}"
    is="owl-goods"
    data="{{ themeClass, columnList: paidData.columnList, columnTotal: paidData.columnTotal, contentList: paidData.contentList, contentTotal: paidData.contentTotal }}" />

  <zan-loadmore wx:if="{{ nomore || nodata }}" type="text" text="{{ nomore ? '' : '暂无数据' }}" />

  <salesman-cube
    scenes="feature"
    page-query="{{ { groupalias } }}"
    need-bind-relation="{{false}}"
  />
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
