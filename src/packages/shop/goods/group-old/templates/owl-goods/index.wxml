<template name="owl-goods">
  <view class="owl-goods">
    <!-- 专栏列表 -->
    <view wx:if="{{ columnList.length }}" class="owl-goods__section">
      <view class="owl-goods__label van-hairline--bottom">专栏</view>
      <view wx:for="{{ columnList }}" wx:key="id">
        <cap-paid-column
          theme-class="{{ themeClass }}"
          column="{{ item }}"
          show-price-info="{{ item.showPriceInfo }}"
        />
      </view>

      <view class="owl-goods__footer">
        <view 
          wx:if="{{ columnList.length < columnTotal }}" 
          class="owl-goods__footer--more" 
          data-type="1" 
          bindtap="fetchMorePaidContent">
          点击查看更多
        </view>
        <view 
          wx:else 
          class="owl-goods__footer--more" >
          没有更多了
        </view>
      </view>
    </view>

    <!-- 内容列表 -->
    <view wx:if="{{ contentList.length }}" class="owl-goods__section">
      <view class="owl-goods__label van-hairline--bottom">内容</view>
      <view wx:for="{{ contentList }}" wx:key="id">
        <cap-paid-content
          theme-class="{{ themeClass }}"
          content="{{ item }}"
          show-price-info="{{ item.showPriceInfo }}"
        />
      </view>

      <view class="owl-goods__footer">
        <view 
          wx:if="{{ contentList.length < contentTotal }}" 
          class="owl-goods__footer--more" data-type="2" 
          bindtap="fetchMorePaidContent">
          点击查看更多
        </view>
        <view
          wx:else 
          class="owl-goods__footer--more" >
          没有更多了
        </view>
      </view>
    </view>
  </view>
</template>