import money from '@youzan/weapp-utils/lib/money';

const app = getApp();

export default {
  listName: 'list',
  params: {
    page_size: 20,
    page_no: 1,
    group_id: ''
  },
  paidPagination: {
    contentPage: 2,
    columnPage: 2
  },

  components: [{
    type: 'goods',
    layout: 3,
    size_type: 0,
    show_title: true,
    show_price: true,
    image_fill_style: 1,
    buy_button_type: 1,
    show_buy_button: true,
    goods: [],
    show_sold_num: false,
    page_margin: 15,
    goods_margin: 10,
    border_radius_type: 1,
  }],
  fetch(params) {
    // 获取普通商品 列表
    params.page_no = params.page;
    delete params.page;
    return app.request({
      path: 'wscump/coupon/get_coupon_normal_goods.json',
      query: Object.assign(this.params, params)
    })
      .then(this.format);
  },
  format(res) {
    const DEFAULT_IMAGE = 'https://img.yzcdn.cn/upload_files/no_pic.png?imageView2/2/w/280/h/280/q/75/format/webp?imageView2/2/w/380/h/380/q/75/format/webp';
    let items = res.good_list || [];
    return items.map((item) => {
      // item.image_url = (item && item.picture.length) ? item.picture[0].url : DEFAULT_IMAGE;
      item.image_url = item.image_url || DEFAULT_IMAGE;
      item.price = money(item.price).toYuan();
      return item;
    });
  },
  // 判断优惠券是否有适用的知识付费商品
  getCouponSupportGoodsInfo(params) {
    return app.request({
      path: 'wscump/coupon/get_coupon_support_goods_info.json',
      query: params
    });
  },

  // 获取知识付费商品列表
  fetchOwlList(params) {
    let { owlType, group_id } = params;
    owlType = +owlType;
    let page = 1;

    if (owlType === 1) {
      // 获取专栏列表
      page = this.paidPagination.columnPage;
    } else if (owlType === 2) {
      // 获取内容列表
      page = this.paidPagination.contentPage;
    } else {
      this.paidPagination = {
        contentPage: 2,
        columnPage: 2
      };
    }
    const owlParam = {
      owlType,
      couponGroupId: group_id,
      page,
      pageSize: 3
    };

    return app.request({
      path: 'wscump/coupon/get_coupon_owl_goods.json',
      query: owlParam
    }).then((data) => {
      if (owlType === 1) {
        // 获取专栏列表
        this.paidPagination.columnPage++;
      } else if (owlType === 2) {
        // 获取内容列表
        this.paidPagination.contentPage++;
      }
      return data;
    });
  }
};
