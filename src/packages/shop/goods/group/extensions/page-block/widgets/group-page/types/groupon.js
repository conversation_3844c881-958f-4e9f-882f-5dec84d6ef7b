import get from '@youzan/weapp-utils/lib/get';

const app = getApp();

export default {
  listName: 'list',
  params: {
    page: 1,
    pageSize: 20,
    orderRule: '2',
    from: 'weapp',
    v: '2',
  },
  components: [
    {
      type: 'groupon',
      itemIds: [1033977009, 2782604347],
      itemCardOpt: {
        type: 'card',
        layout: 'vertical',
        corner: 'rect',
        align: 'left',
        imgOpt: {
          ratio: '1-1',
          fill: 'cover',
          cornerMark: false,
          showKeyLabel: false,
          useGoodsCoverImage: false,
        },
        priceOpt: {
          fontWeight: '400',
          fontSize: 18,
          tagGap: 2,
          style: 'color: var(--ump-price, var(--general))',
        },
        btnOpt: {
          kdtId: 63077,
          type: 'btn',
          corner: 'circle',
          name: '去开团',
          btnStyle:
            'color: var(--ump-icon, var(--general));border: 1px solid var(--ump-icon, var(--general));',
        },
        oPriceOpt: { fontSize: 12, delLine: true, tagGap: 2, color: '#c8c9cc' },
        titleOpt: {
          titleFontWeight: '400',
          vMargin: 0,
          hMargin: 0,
          bgColor: 'transparent',
          textAlign: 'left',
          height: '36px',
          titleLines: 2,
        },
        subTitleOpt: null,
        tagsOpt: null,
        countdownOpt: null,
        extOpt: {
          align: 'right',
          list: [],
        },
      },
      layoutOpt: { layoutMargin: 15, itemMargin: 10, type: 'two' },
      moduleTitleOpt: {},
      queryExtra: {
        goodsSource: 0,
        grouponType: 0,
        type: 'groupon',
        hideSoldOutGoods: 0,
        orderRule: 0,
        v: '2',
        layout: 1,
        goodsNum: 20,
        showPrice: true,
        hideGoodsSold: 0,
      },
      showMoreButton: false,
      uuid: '',
      items: [
        {
          activityEndTime: 1703779200,
          activityPrice: 1,
          activityStartTime: 1702526547,
          conditionNum: 2,
          groupNums: 0,
          id: 4347079501,
          isEnd: false,
          isStart: true,
          itemId: 1033977009,
          itemsInfo: {
            abilityMarkCodeList: [
              20001, 30002, 30003, 40008, 10012, 30007, 10020, 10022, 10023,
              10021, 10029, 20002,
            ],
            alias: '362tywn1ei0s5h0',
            attachmentUrl:
              'https://img01.yzcdn.cn/upload_files/2021/01/15/aa90b7511c762d1df9435bc342a748f5.jpg',
            goodsType: 0,
            height: '360',
            id: 1033977009,
            isDisplay: 1,
            subTitle: '',
            supplierKdtId: 0,
            title: '女巫003',
            totalStock: 998,
            width: '640',
          },
          kdtId: 63077,
          originPrice: 2000,
          skuPrices: { 37819106: 1 },
          thumUrl:
            'https://img01.yzcdn.cn/upload_files/2021/01/15/aa90b7511c762d1df9435bc342a748f5.jpg',
          title: '女巫测试',
        },
        {
          activityEndTime: 1704766147,
          activityPrice: 100,
          activityStartTime: 1702001347,
          conditionNum: 4,
          groupNums: 0,
          id: 4346815505,
          isEnd: false,
          isStart: true,
          itemId: 2782604347,
          itemsInfo: {
            abilityMarkCodeList: [
              20001, 10020, 30007, 40008, 10009, 10012, 10029, 10023,
            ],
            alias: '3f3z47evqg8t13l',
            attachmentUrl:
              'https://img01.yzcdn.cn/upload_files/2022/05/17/Fux_lrsgLLHBJR7JVPGR29X4O0iM.jpeg',
            goodsType: 0,
            height: '592',
            id: 2782604347,
            isDisplay: 1,
            subTitle: '',
            supplierKdtId: 0,
            title: '一个有关税的橙子',
            totalStock: 100,
            width: '800',
          },
          kdtId: 63077,
          originPrice: 9900,
          skuPrices: { 14507949356: 100 },
          thumUrl:
            'https://img01.yzcdn.cn/upload_files/2022/05/17/Fux_lrsgLLHBJR7JVPGR29X4O0iM.jpeg',
          title: 'q1',
        },
      ],
    },
  ],
  fetch(params) {
    Object.assign(this.params, params);
    const requestData = { kdtId: app.getKdtId(), ...this.params };
    const grouponType = +this.components[0].grouponType || 0;

    return new Promise((resolve, reject) => {
      // 抽奖拼团↓↓↓
      if (grouponType === 1) {
        app
          .request({
            path: '/wscshop/ump/groupon/grouponLuckyList.json',
            data: requestData,
          })
          .then(({ list = [] }) => {
            resolve(this.format(list));
          })
          .catch((err) => {
            reject(err);
          });

        return;
      }

      // 多人拼团↓↓↓
      // 组件内部获取数据
      resolve([]);
    });
  },
  format(list = []) {
    const { grouponType, showGrouponNum } = this.components[0];

    return list
      .filter((item) => +get(item, 'goodsInfo.isDisplay', 1) !== 0)
      .map((item) => {
        const { alias, id, extraTagText } = item;
        const url =
          +grouponType === 1
            ? `/packages/goods/lucky-draw-group/index?alias=${alias}&type=luckyDrawGroup&activityId=${id}`
            : `/packages/goods/groupon/index?alias=${alias}`;

        return {
          ...item,
          url, // 覆盖 H5 的商品链接
          extraTagText: showGrouponNum ? extraTagText : '', // 如果不显示成团人数，则把 tagText 置为空串
        };
      });
  },
};
