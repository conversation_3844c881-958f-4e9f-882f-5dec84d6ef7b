import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

const app = getApp();

export default {
  listName: 'list',
  params: {
    alias: '',
    page_size: 20,
    page_no: 1
  },
  components: [{
    size: 1,
    title: 1,
    price: 1,
    buy_btn: 1,
    buy_btn_type: 1,
    size_type: 0,
    type: 'goods_weapp',
    show_corner_mark: 0,
    show_sub_title: 0,
    goods: []
  }],
  fetch(params) {
    return new Promise((resolve, reject) => {
      const options = Object.assign(this.params, params);
      app.request({
        path: '/wscshop/goods/goodsByTagAlias.json',
        data: {
          alias: options.alias,
          page: options.page,
          pageSize: options.page_size,
          isShowPeriod: 1,
          supportCombo: true,
          excludedComboSubType: '["optional_combo"]',
        }
      }).then(({ list = [] }) => {
        resolve(this.format(list));
      }).catch(res => {
        reject(res);
      });
    });
  },
  format(items) {
    return items.map((item) => {
      if (!item.image_url) {
        item.image_url = cdnImage('/upload_files/no_pic.png', '!300x300.jpg');
      } else {
        item.image_url = cdnImage(item.image_url);
      }
      return item;
    });
  }
};
