import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import debounce from 'utils/debounce';
import args from '@youzan/weapp-utils/lib/args';
import { tryLocation, gcjToBaidu, searchAddress } from '@/helpers/lbs';
import common from '../common/index';

const {
  tryLocationAgain,
  saveLocation,
  fetchShops,
  saveStoreAndJumpBack
} = common;

const app = getApp();
const page = {
  data: {
    themeClass: app.themeClass,
    range: ['门店', '配送至'],
    rangeIndex: 0,
    keyword: '',
    placeholder: '可搜索区县、商圈、路名等',
    list: []
  },
  onLoad(query) {
    this.autoSearch = debounce(this._search, 1000);
    if (query.title) {
      wx.setNavigationBarTitle({ title: query.title });
    }
    if (query.range) {
      this.setYZData({ rangeIndex: +query.range });
    }
  },
  pickerChange(e) {
    this.setYZData({ rangeIndex: e.detail.value, placeholder: e.detail.value == 0 ? '可搜索区县、商圈、路名等' : '你要配送的小区、大楼等' });
    wx.setNavigationBarTitle({ title: e.detail.value == 0 ? '按名称、地址搜索门店' : '按收货地址搜索门店' });
    if (this.data.keyword) {
      this._search(this.data.keyword);
    }
  },
  clearInput() {
    this.setYZData({ keyword: '' });
  },
  cancelSearch() {
    wx.navigateBack();
  },
  inputChange(e) {
    this.setYZData({ keyword: e.detail });
    this.autoSearch(e.detail);
  },
  search(e) {
    this.setYZData({ keyword: e.detail });
    this._search(e.detail);
    this.autoSearch.cancel();
  },
  onToLower() {
    if (this.data.nomore || this.data.loading || +this.data.rangeIndex === 1) return;
    this.setYZData({ loading: true });

    this.lastQuery.page = this.lastQuery.page + 1;
    this.searchShops(this.lastQuery);
  },
  navigateBack(e) {
    const { itemIndex } = e.target.dataset;
    if (+itemIndex !== 0 && !itemIndex) return;
    const item = this.data.list[itemIndex];
    if (+this.data.rangeIndex === 0) {
      // 通知已找到 offlineid
      item.image = item.images;
      saveStoreAndJumpBack.call(this, item, 2);
      return;
    }
    const { lng, lat } = gcjToBaidu(item.location.lng, item.location.lat);
    wx.navigateTo({
      url: args.add(
        '/packages/shop/multi-store/index/index',
        {
          address: item.name,
          lng,
          lat
        }
      )
    });
  },
  chooseCloseShop() {
    tryLocation(
      ({ lng, lat }) => {
        saveLocation({ lng, lat });
        this.setYZData({ rangeIndex: 0, isUsedLocation: true });
        this.searchShops({
          lat, lng, page: 1, perpage: 20
        });
      },
      () => Toast('获取位置失败'),
      tryLocationAgain
    );
  }
};

page._search = function (keyword) {
  if (!keyword || this.loading) return;
  this.setYZData({
    loading: true, isUsedLocation: false, nomore: false, nodata: false, list: [], scrollPosition: 0
  });
  if (+this.data.rangeIndex === 1) {
    this.searchKeyword(keyword);
  } else {
    this.searchShops({ keyword, page: 1, perpage: 20 });
  }
};

page.searchKeyword = function (keyword) {
  searchAddress({
    keyword,
    policy: 1
  })
    .then((res) => {
      let ads = res.data.map(({
        title,
        address,
        location
      }) => ({
        name: title,
        address,
        location
      }));
      this.setYZData({
        list: ads,
        loading: false,
        nodata: ads.length === 0
      });
    })
    .catch((err) => {
      this.setYZData({
        loading: false
      });
      Toast(err.message || '获取地址失败');
    });
};

page.searchShops = fetchShops;

WscPage(page);
