<import src="../common/list.wxml" />

<van-toast id="van-toast" />

<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view class="search-bar">
    <van-field
      clearable
      type="search"
      border="{{ false }}"
      confirm-type="search"
      class="search-bar__field"
      value="{{ value }}"
      placeholder="{{ placeholder }}"
      custom-style="padding: 3px 10px"
      bind:change="inputChange"
      bind:confirm="search"
    >
      <view
        slot=left-icon
        class="search-bar__left-part"
      >
        <van-icon custom-style="margin-bottom: 2px; vertical-align: middle" name="search"/>
        <picker
          class="search-bar__picker"
          slot="input"
          mode="selector"
          range="{{ range }}"
          value="{{ rangeIndex }}"
          style="width: 60px"
          bindchange="pickerChange"
        >
          <view class="search-bar__picker-cnt">{{ range[rangeIndex] }}</view>
        </picker>
        
      </view>
    </van-field>
    <view class="search-bar__action">
      <view bind:tap="cancelSearch" class="cancel-class">取消</view>
    </view>
  </view>

  <template wx:if="{{list.length !== 0}}" is="shop-or-location-list"
    data="{{list, isAddress: rangeIndex == 1, loading, onToLower, scrollPosition, navigateBack}}" />

  <view wx:if="{{list.length === 0 && !nodata}}" class="search-shop__tip van-c-gray van-font-14">
    <block wx:if="{{rangeIndex == 0}}">通过门店名称、地址等搜索你想访问的门店。</block>
    <block wx:else>搜索并选择你想要配送的小区 写字楼、学校等，我们给你推荐附近的门店。</block>
  </view>

  <view wx:if="{{list.length === 0 && nodata && rangeIndex == 0}}" class="search-shop__tip van-c-gray van-font-14">
    <view>没找到你要的店铺，</view>
    <view>请尝试搜索区县信息试试，比如“西湖区”等。</view>
    <navigator open-type="redirect" url="/packages/shop/multi-store/index/index?useLocationTab=1" class="van-c-blue">点此查看你附近的门店</navigator>
  </view>

  <view wx:if="{{list.length === 0 && nodata && rangeIndex != 0}}" class="search-shop__tip van-c-gray van-font-14">
    <view>没找到你要的地址，</view>
    <view>请尝试只输入小区、写字楼或学校名试试。</view>
  </view>
</page-container>

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
