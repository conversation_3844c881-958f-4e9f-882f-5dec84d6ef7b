import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import args from '@youzan/weapp-utils/lib/args';
/* #ifdef BUILD_ENV=youzanyun */
import SaasPageConfig from './saas/page-config';
/* #endif */
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { tryLocation, reverseGeocoder } from '@/helpers/lbs';
import common from '../common/index';
import { writeEnterStatus } from '@youzan/shop-core-tee';
import { switchTabOrder } from './saas/utils';

const {
  tryLocationAgain,
  saveLocation,
  fetchShops,
  saveStoreAndJumpBack
} = common;

let extendPageConfig = {};
/* #ifdef BUILD_ENV=youzanyun */
extendPageConfig = SaasPageConfig;
/* #endif */
const height = 120;
const app = getApp();
const page = {
  data: {
    themeClass: app.themeClass,
    activeTab: switchTabOrder.type,
    tabOrder: 'default',
    shop: app.getShopInfoSync(),
    list: [],
    // 0->不显示城市筛选器，1->城市筛选，2->城市区县筛选
    areaFilterLevel: 0,
    alphaGroupCityStores: {}
  },
  onLoad(query) {
    this.queryAddress = query.address;
    this.redirectto = query.to;
    delete query.address;
    this.query = Object.assign({}, query, { page: 1, perpage: 20 });
    if (query.useLocationTab) {
      this.chooseCloseShop();
    }
    if (switchTabOrder && switchTabOrder.type === 'location') {
      this.setYZData({
        tabOrder: switchTabOrder.type,
      });
      if (this.query.lng && this.query.lat) {
        this.fetchShops(this.query);
      } else {
        this.chooseCloseShop();
      }
    }

    this.getCitiesStoreNum().then((res) => {
      // 网点总数
      let storeSum = 0;
      let areaFilterLevel = 0;
      // 城市数量
      let cityCount = 0;
      let alphaStoresData = [];
      for (let alpha in res) {
        if (typeof alpha === 'string' && alpha.length === 1 && alpha >= 'A' && alpha <= 'Z') {
          res[alpha].forEach((city) => {
            storeSum += city.num;
            if (city.num >= 20) {
              areaFilterLevel = 2;
            }
          });
          cityCount += res[alpha].length;
          alphaStoresData.push({
            // 当前首字母之前的城市总数
            beforeCities: cityCount,
            alpha
          });
        }
      }
      // 网点总数>=20 开启城市筛选器
      if (storeSum >= 20) {
        // 某城市网点数>=20使用城市、区县筛选，否则只使用城市筛选
        areaFilterLevel = areaFilterLevel || 1;
      } else {
        areaFilterLevel = 0;
      }
      this.setYZData({
        alphaGroupCityStores: res,
        areaFilterLevel,
        alphaStoresData
      });
    });
  },
  onReady() {
    app.getShopInfo()
      .then(() => {
        this.setYZData({
          shop: app.getShopInfoSync()
        });
      });


    if (this.queryAddress) {
      this.queryAddress = decodeURIComponent(this.queryAddress);
      this.setYZData({ activeTab: 'location', scrollPosition: 0, queryAddress: this.queryAddress });
    }
    if (!(switchTabOrder && switchTabOrder.type === 'location')) {
      this.fetchShops(this.query);
    }
  },
  onHide() {
    // 不管如何 离开这个页面就表示切换完成
    writeEnterStatus(/** 未完成 */ 0);
  },
  onUnload() {
    // 不管如何 离开这个页面就表示切换完成
    writeEnterStatus(/** 未完成 */ 0);
  },
  chooseDefaultShop() {
    this.setYZData({ activeTab: 'default', scrollPosition: 0, loading: true });
    this.query = { page: 1, perpage: 20 };
    this.fetchShops({ page: 1, perpage: 20 });
  },
  chooseCloseShop() {
    tryLocation(
      ({ lat, lng }, res) => {
        let query = {
          lng, lat, page: 1, perpage: 20
        };
        // 通过地址信息来获取列表
        this.query = query;
        saveLocation({ lat, lng });
        this.setYZData({
          activeTab: 'location', scrollPosition: 0, loading: true, list: []
        });
        this.fetchShops(query);
        this.getAddressByLocation(res);
      },
      (err) => {
        if (/auth deny/.test(err.errMsg)) {
          this.tryAlign = true;
          return this.chooseCloseShop();
        }

        Toast('位置获取失败，无法推荐你附近的店');
      },
      this.tryAlign ? tryLocationAgain : null
    );
  },

  navigateBack(e) {
    const { itemIndex } = e.target.dataset;
    if (+itemIndex !== 0 && !itemIndex) return;
    const item = Object.assign({}, this.data.list[itemIndex]);
    item.business_hours_advanced = item.openingTimeSections || [];
    item.image = item.images.map((src) => {
      if (!/http[s]/i.test(src) || /^upload_files/.test(src)) {
        return 'https://img01.yzcdn.cn/' + src;
      }
      return src;
    });
    saveStoreAndJumpBack.call(this, item);
  },
  navToSearchPage() {
    wx.navigateTo({
      url: args.add(
        '/packages/shop/multi-store/search/index',
        {
          range: 1,
          keyword: this.data.queryAddress
        }
      )
    });
  },
  onScroll(e) {
    let st = e.detail.scrollTop;

    if (st > height && !this.data.isFixed) {
      this.setYZData({ isFixed: true });
    } else if (st < height && this.data.isFixed) {
      this.setYZData({ isFixed: false });
    }

    if (st >= 568 * 2 && !this.data.showToTopBtn) {
      this.setYZData({ showToTopBtn: true });
    } else if (st < 568 * 2 && this.data.showToTopBtn) {
      this.setYZData({ showToTopBtn: false });
    }
  },
  onToLower() {
    if (this.data.loading || this.data.nomore) return;
    this.setYZData({ loading: true });

    this.query.page = this.query.page + 1;
    this.fetchShops(this.query);
  },
  onScrollToTop() {
    this.setYZData({ scrollPosition: 0, showToTopBtn: false });
  },
  getCitiesStoreNum() {
    return app.request({
      path: '/wscump/multistore/getListEachCityStoreNumAndGroupByAlpha.json'
    }).then((res) => {
      return mapKeysCase.toCamelCase(res);
    });
  },
  handleAreaFiltered(e) {
    const { regionId = '', city = '' } = e.detail;
    this.query.regionId = regionId;
    this.query.city = city;
    this.query.page = 1;
    this.fetchShops(this.query);
  }
};

page.getAddressByLocation = function (location) {
  reverseGeocoder({
    location,
    poi_options: 'policy=2'
  })
    .then(({
      result
    }) => {
      this.setYZData({
        queryAddress: result.formatted_addresses.recommend || result.address || ''
      });
    })
    .catch((err) => {
      console.log(err);
    });
};

page.fetchShops = fetchShops;

WscPage(extendPageConfig, page);
