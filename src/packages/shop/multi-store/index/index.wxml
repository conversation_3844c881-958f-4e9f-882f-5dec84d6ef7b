
<import src="../common/list.wxml" />

<page-container
  forbid-copyright
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view class="multi-store__choose-shop {{activeTab === 'location' ? 'location-type' : ''}}">
    <view class="choose-shop__header">
      <view>
        <image src="{{shop.logo}}" />
      </view>
      <view class="van-font-12 van-c-gray-darker">
        {{shop.shop_name}}
      </view>
    </view>
    <view class="choose-shop__action-bar zan-panel">
      <view class="van-c-gray">请选择要进入的店：</view>
      <view class="choose-shop__tabs">
        <view wx:if="{{tabOrder==='default'}}" class="choose-shop__tab {{activeTab === 'default' ? 'active' : ''}}" bindtap="chooseDefaultShop">默认</view>
        <view class="choose-shop__tab {{activeTab === 'location' ? 'active' : ''}}" bindtap="chooseCloseShop">我附近的店</view>
        <view wx:if="{{tabOrder==='location'}}" class="choose-shop__tab {{activeTab === 'default' ? 'active' : ''}}" bindtap="chooseDefaultShop">默认</view>
        <navigator url="/packages/shop/multi-store/search/index?title=按名称、地址搜索门店" class="choose-shop__tab"><icon type="search" color="#999" size="14"></icon></navigator>
      </view>
    </view>

    <area-filter
      wx:if="{{ areaFilterLevel && activeTab !== 'location' }}"
      cityList="{{ alphaGroupCityStores }}"
      filterLevel="{{ areaFilterLevel }}"
      alphaStoresData="{{ alphaStoresData }}"
      bindareafiltered="handleAreaFiltered"
    />
  
    <view wx:if="{{activeTab === 'location' && queryAddress}}" class="zan-cell choose-shop__send-address">
      <view class="zan-cell__bd">
        <view class="zan-cell__text text-nowrap-clip" style="width: 54px">配送至：</view>
        <view class="zan-cell__text" style="flex: 1" bindtap="navToSearchPage">{{queryAddress}}</view>
      </view>
      <view class="zan-cell__ft"></view>
    </view>
    <template is="shop-or-location-list" data="{{list, isAddress: false, loading, nomore, onToLower, scrollPosition, navigateBack, copyright, is_big_shop}}" />
  </view>
</page-container>

<van-toast id="van-toast" />

<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />