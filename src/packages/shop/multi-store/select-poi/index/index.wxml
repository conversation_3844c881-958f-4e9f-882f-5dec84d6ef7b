<view class="select-store">

  <city-select
    current="{{ currentInfo }}"
    bindselectPoi="getStore"
  />
  
  <position-deny wx:if="{{ getLocationFailed }}"/>

  <block wx:else>
    <current-poi
      poi="{{ currentPoI }}"
      bindgetStore="getStore"
      bindgetLocation="getLocation"
    />

    <address-list
      wx:if="{{ addressList.length > 0 }}"
      list="{{ addressList }}"
      bindeditAddress="goEditAddress"
      bindselect="getStore"
    />
    <nearby-poi 
      list="{{ nearPoIs }}"
      coords="{{ coords }}"
      bindselect="getStore"
      wx:if="{{ nearPoIs.length > 0 }}"
    />
  </block>

  <view class="bottom" bindtap="goAddAdress">新建收货地址</view>
  <van-toast id="van-toast" />
</view>
<inject-protocol noAutoAuth />