import WscPage from 'pages/common/wsc-page/index';
import Storage from '@youzan/weapp-utils/lib/storage';
import Toast from '@vant/weapp/dist/toast/toast';
import { tryLocation, reverseGeocoder, gcjToBaidu } from '@/helpers/lbs';
import common from '../../common/index';
import {
  getStoreId,
  savePoiInfo,
  getAddressList,
  getLocationByIp,
} from './api';

const { saveStoreAndJumpBack } = common;

const storage = Storage();
const app = getApp();
const page = {
  data: {
    currentLongitude: '', // 当前纬度
    currentLatitude: '', // 当前经度
    currentPoI: null, // 当前定位信息
    currentInfo: {}, // 包了一层name的currentPoI
    nearPoIs: [], // 附近poi信息
    addressList: [], // 收货地址列表
    positonDenied: false,
    nearPoiPage: 1,
    nearPoiPageSize: 20,
  },

  onLoad() {
    this.getLocation();
  },

  onReachBottom() {
    // 加载附近地址
    this.getNearPois();
  },

  onShow() {
    // 加载收货地址
    this.getAddressList();
  },

  getNearPois() {
    let { coords, nearPoiPage, nearPoiPageSize, nearPoIs } = this.data;

    if (!coords) {
      return;
    }

    wx.showLoading('加载中');
    nearPoiPage++;
    reverseGeocoder({
      location: coords,
      get_poi: 1,
      poi_options: `radius=500;page_size=${nearPoiPageSize};page_index=${nearPoiPage}`,
      coord_type: 3,
    })
      .then((result) => {
        if (result.status === 0) {
          nearPoIs.push(...result.result.pois);
          this.setYZData(
            {
              nearPoiPage,
              nearPoIs,
            },
            () => setTimeout(() => wx.hideLoading(), 2000)
          );
        }
        wx.hideLoading();
      })
      .catch(() => wx.hideLoading());
  },

  // 定位
  getLocation() {
    wx.showLoading({
      title: '定位中',
    });

    this.setYZData({
      currentPoI: null,
    });

    tryLocation(
      (position) => {
        const { lat, lng } = position;
        this.getPoiInfo({ latitude: lat, longitude: lng });
      },
      (err) => {
        setTimeout(() => wx.hideLoading(), 1000);
        console.log('[gps: get position err]', err);
        if (err.code === err.PERMISSION_DENIED) {
          this.setYZData({
            currentPoI: {},
            currentInfo: {},
          });
        }
        this.getLocationByIp();
      }
    );
  },

  // ip定位
  getLocationByIp() {
    getLocationByIp()
      .then((res) => {
        wx.hideLoading();
        if (res !== null) {
          const { lat, lng } = res;
          this.getPoiInfo([lat, lng]);
        } else {
          // 展示定位错误
          this.setYZData({
            currentPoI: {},
            currentInfo: {},
          });
        }
      })
      .catch(() => {
        // 展示定位错误
        this.setYZData({
          currentPoI: {},
          currentInfo: {},
        });
        wx.hideLoading();
      });
  },

  // 通过坐标，获取POI
  getPoiInfo(coords) {
    this.poiPage = 0;
    const { nearPoiPageSize } = this.data;

    reverseGeocoder({
      location: coords,
      get_poi: 1,
      poi_options: `radius=500;page_size=${nearPoiPageSize};page_index=1`,
      coord_type: 3,
    })
      .then((result) => {
        if (result.status !== 0) {
          this.setYZData(
            {
              currentPoi: {},
              currentInfo: {},
            },
            () => wx.hideLoading()
          );
          return;
        }
        const resultInfo = result.result;
        const currentInfo = {
          name: resultInfo.address_component.city,
          coords,
        };

        const currentPoI = resultInfo.pois[0];
        const nearPoIs = resultInfo.pois;
        if (this.data.autolocate) {
          this.getStore({
            detail: {
              ...currentPoI.location,
              name: currentPoI.title,
            },
          });
        }
        this.setYZData(
          {
            currentInfo,
            currentPoI,
            nearPoIs,
            coords,
          },
          () => wx.hideLoading()
        );
      })
      .catch(() => {
        this.setYZData(
          {
            currentPoI: {},
            currentInfo: {},
          },
          () => wx.hideLoading()
        );
      });
  },
  // 获取storeId
  getStore(e) {
    const params = e.detail;
    const { lng, lat } = gcjToBaidu(params.lng, params.lat);
    getStoreId({
      kdt_id: app.getKdtId(),
      lng,
      lat,
    })
      .then((res) => {
        app.setOfflineId(res.value);

        app.$store &&
          app.$store.commit(
            'UPDATE_USER_INFO',
            Object.assign(app.getUserInfoSync(), {
              poi: {
                lng: params.lng,
                lat: params.lat,
                title: params.name,
              },
            })
          );

        storage.setAsync('multistore:user:poi', params.name, {
          expire: 0.8,
        });

        savePoiInfo({
          kdt_id: app.getKdtId(),
          store_id: res.value,
          location: params.name,
        });
        saveStoreAndJumpBack.call(this, { id: res.value }, 0);
      })
      .catch((err) => {
        Toast(err.msg || '系统忙～稍后再试');
      });
  },

  // 获取收货地址
  getAddressList() {
    const _global = app.globalData;
    let addressList;
    if (!_global.address_list) {
      getAddressList().then((res) => {
        const addressList = res;
        this.setYZData({
          addressList,
        });
      });
    } else {
      addressList =
        (_global.address_list.length > 0 && _global.address_list) || [];
      addressList.map((item) => {
        return {
          ...item,
          addressDetail: `${item.province}${item.city}${item.address_detail}`,
        };
      });
      this.setYZData({
        addressList,
      });
    }
  },

  goAddAdress() {
    wx.navigateTo({
      url: '/packages/shop/multi-store/select-poi/address-edit/index',
    });
  },
};
WscPage(page);
