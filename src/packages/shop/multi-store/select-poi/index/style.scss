@import '~themes/abstracts/_mixins.scss';
$gray-dark: #999;
$gray: #333;
$blue: #38f;
$white: #fff;
html,
body,
.content,
.select-store {
  height: 100%;
}

.content {
  overflow: scroll;
}

.select-store {
  position: relative;
  padding-bottom: 65px;
  background-color: #f8f8f8;
}

.list {
  .item {
    position: relative;
    padding: 15px 0;

    &:after {
      @include border-line(top);
    }

    &:first-child {

      &:after {
        content: none;
      }
    }
  }
}
.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: $white;

  &::after {
    @include border-line(top, rgba(0, 0, 0, .1));
  }
}

.autolocate {
  height: 100%;
  text-align: center;

  img {
    margin-top: 50%;
  }
  
  p {
    margin-top: 10px;
  }
}
