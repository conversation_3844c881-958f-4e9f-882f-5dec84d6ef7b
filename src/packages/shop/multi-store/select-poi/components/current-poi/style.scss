.current-position {
    padding: 60px 15px 0 15px;
    background-color: #fff;
    .title {
      font-size: 12px;
      color: #999;
    }
    &.container {
      margin-top: 0;
    }

    > view {
      justify-content: space-between;

      .poi-name {
        padding: 15px 0;
        flex: 1;
        display: inline-block;
        font-size: 14px;
      }
    }

    .re-location {
      float: right;
      display: flex;
      align-items: center;

      image {
        width: 16px;
        height: 16px;
        margin-right: 5px;
        display: inline-block;
      }

      view {
        color: #38f;
        display: inline-block;
        padding: 15px 0;
        font-size: 14px;
      }
    }
  }