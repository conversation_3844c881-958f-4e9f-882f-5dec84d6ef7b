import WscComponent from 'pages/common/wsc-component/index';
import getSystemInfo from 'shared/utils/browser/system-info';

WscComponent({
  data: {
    letters: [],
    listScrollTop: 0,
    currentCityText: '北京市',
    height: '800px',
  },
  properties: {
    cityData: {
      type: Object,
    },
    currentCity: {
      type: Object,
      observer(currentCity) {
        // ?? ob obj
        let currentCityText = '定位中';
        if (currentCity) {
          currentCityText = currentCity.name
            ? '当前定位'
            : '定位失败，请选择城市';
        }
        this.setYZData({
          currentCityText,
        });
      },
    },
  },
  attached() {
    const info = getSystemInfo();
    if (info) {
      this.setYZData({
        height: info.windowHeight + 'px',
      });
    }
  },
  methods: {
    clickCity(e) {
      const data = e.currentTarget.dataset;
      this.triggerEvent('selected', this.data.cityData.adcodeMap[data.code]);
    },

    handleClickCurrentCity() {
      this.triggerEvent('selected', this.data.currentCity);
    },

    handleClickLetterBar(e) {
      const { letter } = e.currentTarget.dataset;
      this.setYZData({
        currentLetterId: 'letter' + letter,
      });
    },
  },
});
