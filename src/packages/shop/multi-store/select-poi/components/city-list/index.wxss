@import '../../base.scss';
.city-list {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  background-color: #ffffff;
  z-index: 100;
}

.city-list .cruent {
  padding-bottom: 15px;
}
.city-list .cruent .h4 {
  font-size: 12px;
  color: #999;
}

.city-list .cruent {
  padding: 15px 0 15px 15px;
}

.city-list .cruent .current-city {
  display: inline-block;
  height:14px;
  line-height:14px;
  border: 1px solid #E5E5E5;
  border-radius: 1px;
  text-align: center;
  padding: 0 5px;
  margin-top: 10px;
  padding: 11px 30px;
  font-size: 14px;
}

.city-list .letter {
  background-color: #F8F8F8;
  height: 26px;
  line-height: 26px;
  color: #999999;
  font-size: 12px;
  padding-left: 15px;
}

.city-list .letter.top {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 101;
}

.city-list .city-container {
  overflow: scroll;
  -webkit-overflow-scrolling: touch;
  height: 100%;
}

.city-list .city-container .group {
  text-align: left;
}

.city-list .city-container .group .city {
   padding: 0 15px;
  height: 44px;
  line-height: 44px;
  color: #333333;
  font-size: 14px;
  position: relative;
}

.city-list .city-container .group .city:after {
  content: ' ';
  display: inline-block;
  position: absolute;
  height: 1px;
  background-color: #E5E5E5;
  bottom: 0;
  left: 15px;
  right: 0;
  transform: scaleY(0.5);
} 

.city-list .city-container .group .city:last-child:after {
  height: 0;
} 

.city-list .city-letter {
  position: fixed;
  right: 0;
  top: 10px;
  text-align: center;
  padding: 10px 0px 10px 5px;
  font-size: 12px;
  color: #bbb;
  line-height: 12px;
}

.city-list .city-letter view {
  display: block;
  padding: 3px 10px;
}
