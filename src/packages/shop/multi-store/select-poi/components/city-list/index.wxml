
<view class="city-list">
  <scroll-view 
    class="city-container" 
    id="city-C"
    style="height: {{ height }}"
    scroll-y
    scroll-into-view="{{ currentLetterId }}"
  >
    <view class="cruent">
      <view>{{ currentCityText }}</view>
      <view
        class="current-city"
        bindtap="handleClickCurrentCity"
        wx:if="{{ currentCity.name }}"
      >
        {{currentCity.name}}
      </view>
    </view>
    <view class="letter top" wx:if="{{ letterHeader }}">{{letterHeader}}</view>
    <view
      wx:for="{{ cityData.letterGroup }}"
      wx:for-item="letter"
      wx:for-index="index"
      wx:key="index"
      class="group"
      id="scroll-{{ letter.title }}"
      data-letter="{{ letter.title }}"
      wx:if="{{ letter.adcodes.length > 0 }}"
    >
      <view 
        class="letter"
        data-letter="{{ letter.title }}"
        id="letter{{ letter.title }}"
      >
        {{ letter.title }}
      </view>
      <view
        class="city"
        wx:for="{{ letter.adcodes }}"
        wx:for-item="code"
        wx:index="indexC"
        wx:key="indexC"
        data-code="{{ code }}"
        bindtap="clickCity"
      >
        {{ cityData.adcodeMap[code].name  }}
      </view>
    </view>
  </scroll-view>
  <view class="city-letter">
    <view
      href="#scroll-{{ letter.title }}"
      wx:for="{{ cityData.letterGroup }}"
      wx:for-item="letter"
      wx:if="letter.adcodes.length > 0"
      wx:key="index"
      data-letter="{{ letter.title }}"
      bindtap="handleClickLetterBar"
    >
      {{ letter.title }}
    </view>
  </view>
</view>
