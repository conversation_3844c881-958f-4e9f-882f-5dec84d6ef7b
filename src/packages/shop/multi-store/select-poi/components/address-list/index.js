import WscComponent from 'pages/common/wsc-component/index';
import { reverseGeocoder, geocoder } from '@/helpers/lbs';

const app = getApp();
WscComponent({
  properties: {
    // 所有地址
    list: {
      type: Array,
      observer(list) {
        // ?? ob obj
        this.setYZData({
          renderAddressList: list.slice(0, this.data.showAllList ? list.length : 3)
        });
      }
    }
  },
  data: {
    showAllList: false,
    // 当前显示的所有地址
    renderAddressList: []
  },
  methods: {
    extend() {
      const { list, showAllList } = this.data;
      this.setYZData({
        showAllList: !showAllList,
        renderAddressList: list.slice(0, !showAllList ? list.length : 3)
      });
    },

    handleSelect(e) {
      const item = e.currentTarget.dataset.item;

      if (parseFloat(item.lat) && parseFloat(item.lon) && item.lat !== '' && item.lon !== '') {
        const { lon, lat } = item;
        this.getPoiInfo({ longitude: lon, latitude: lat })
          .then(res => {
            if (res.status === 0) {
              const result = res.result;
              this.triggerEvent('select', { ...result.location, name: result.formatted_addresses.recommend, addressId: item.id });
            }
          });
      } else {
        this.findPositionByAddress(item)
          .then(res => {
            if (res.status === 0) {
              const result = res.result;
              this.triggerEvent('select', { ...result.location, name: result.title, addressId: item.id });
            }
          });
      }
      app.globalData.choosed_address_id = item.id;
    },

    findPositionByAddress(address) {
      return geocoder({
        address
      })
        .then((result) => {
          return result;
        })
        .catch(() => {
          return '通过详细地址获取poi失败';
        });
    },

    // 通过坐标，获取POI
    getPoiInfo(coords) {
      return reverseGeocoder({
        location: coords,
        get_poi: 1,
        poi_options: 'radius=500',
        coord_type: 3
      }).then((result) => {
        return result;
      });
    },

    handleEditAddress(e) {
      const data = e.currentTarget.dataset;
      app.globalData.editAddress = data.item;
      wx.navigateTo({
        url: '/packages/shop/multi-store/select-poi/address-edit/index?status=edit'
      });
    }
  }
});
