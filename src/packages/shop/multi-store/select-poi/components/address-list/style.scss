@import '~themes/abstracts/_mixins.scss';
.address-list {
  padding-left: 0px;

  .h4 {
    padding-left: 15px;
  }

  .item:first-child {
    border-top: 0;
  }
  .item.flex {
    justify-content: space-between;
  }

  .icon-arrow-down {
    font-size: 8px;
    margin-left: 5px;
    transform: rotate(90deg);
    padding-top: 2px;
  }

  .icon-arrow-up {
    font-size: 8px;
    margin-left: 5px;
    transform: rotate(-90deg);
    padding-top: 2px;
  }

  .address-info {
    flex: 1;
    font-size: 14px;
    padding: 15px 60px 15px 15px;

    &__detail {
      line-height: 20px;
      text-align: left;
    }
    &__user-info{
      margin-top: 10px;
      font-size: 12px;
      color: #999;
      text-align: left;
      line-height: 17px;
    }
  }

  .edit {
    font-size: 20px;
    right: 20px;
    top: 50%;
    position: absolute;
    transform: translateY(-50%);
  }

  .extend {
    color: #38f;
    height: 30px;
    line-height: 30px;
    width: 100%;
    text-align: center;
    font-size: 12px;

    .extend-arrow {
      transform: rotate(90deg);
      font-size: 12px;
      margin-left: 5px;

      &.arrow-down {
        transform: rotate(-90deg);
      }
    }
  }
}
