<view class="container address-list">
  <view class="h4">我的收货地址</view>
    <van-cell-group border="{{ false }}">
      <van-cell
        wx:for="{{ renderAddressList }}"
        wx:for-item="item"
        wx:for-index="index"
        wx:key="index"
        title-width="100%"
        custom-class="address-info"
        data-item="{{ item }}"
        bind:click="handleSelect"
        border="{{ index != renderAddressList.length - 1 }}"
      >
          <view class="address-info__detail">{{ item.province + item.city + item.county + item.addressDetail }}</view>
          <view class="address-info__user-info">{{ item.userName + ' ' + item.tel }}</view>
          <view 
            class="edit"
            data-item="{{item}}"
            catchtap="handleEditAddress"
          >
            <van-icon 
              name="edit"
              color="#666666"
            />
          </view>
      </van-cell>
    </van-cell-group>
  <view 
    class="extend" 
    bindtap="extend" 
    wx:if="{{ list.length > 3 }}"
  >
    <span>{{showAllList ? '收起':'展开更多'}}</span>
    <van-icon name="arrow" custom-class="{{ showAllList ? 'icon-arrow-up' : 'icon-arrow-down' }}"/>
  </view>
</view>