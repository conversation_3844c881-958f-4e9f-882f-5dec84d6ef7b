import WscComponent from 'pages/common/wsc-component/index';
import { searchAddress } from '@/helpers/lbs';
import debounce from 'utils/debounce';

WscComponent({
  data: {
    selectedCity: '',
    keyWord: '',
    showkeyWordList: false,
    showSearchedPoi: false,
    showCityList: false,
    historySearchList: [],
    searchedPoiList: [],
    searchEd: false,
    isShowPop: false,
    cityData: {
      letterGroup: [],
      adcodeMap: {},
    },
  },
  properties: {
    current: Object,
  },
  created() {
    this.getPoiInfo = debounce(this.__getPoiInfo, 200);
    this.getCityJson();
  },
  methods: {
    getCityJson() {
      // eslint-disable-next-line prettier/prettier
      const url =
        'https://file.yzcdn.cn/upload_files/yz-file/2024/12/19/FvmEQWLrl7rF9cUJkpVnCbgKSwHx.json';
      wx.request({
        url,
        header: { 'content-type': 'application/json' },
        success: (res) => {
          this.setYZData({
            cityData: res,
          });
        },
      });
    },
    clearKeyword() {
      const { keyWord } = this.data;
      if (keyWord) {
        this.setYZData({
          keyWord: '',
        });
      }
    },
    redKeyWord(keyWord, list) {
      const tmpList = [];
      list.map((item) => {
        const texts = item.title.split(keyWord, 2);
        tmpList.push({
          ...item,
          redText: texts.length === 2 ? keyWord : '',
          beforeText: texts[0],
          afterText: texts[1] || '',
        });
        return item;
      });
      return tmpList;
    },

    closePop() {
      this.setYZData({
        showkeyWordList: false,
        showSearchedPoi: false,
        keyWord: '',
      });
    },

    showkeyWord() {
      if (this.showSearchedPoi) return;
      this.setYZData({
        showkeyWordList: true,
      });
    },

    handleSelectCity(e) {
      const { current } = this.data;

      const selectedCity = e.detail.name;
      this.setYZData({
        showCityList: false,
        selectedCity: e.detail.name,
        selectedCityName:
          selectedCity ||
          (current === null ? '定位中' : (current && current.name) || '北京市'),
      });
    },

    goCityList() {
      this.setYZData({
        showCityList: true,
      });
    },

    getPoi(e) {
      this.getPoiInfo(e);
    },

    __getPoiInfo(e) {
      const params = e.detail;
      if (!params.value) return;
      if (!params.value) {
        return;
      }
      this.setYZData({
        keyWord: params.value,
      });

      const city =
        this.data.selectedCity ||
        (this.data.current === null
          ? '定位中'
          : (this.data.current && this.data.current.name) || '北京市');
      searchAddress({
        keyword: params.value,
        region: city,
        region_fix: 1,
      }).then((result) => {
        const searchedPoiList = this.redKeyWord(
          params.value,
          (result.data && result.data.slice(0, 5)) || []
        );
        this.setYZData({
          showSearchedPoi: true,
          showkeyWordList: true,
          searchEd: true,
          searchedPoiList,
        });
      });
    },
    handleClick(e) {
      const poi = e.currentTarget.dataset.item;
      this.triggerEvent('selectPoi', { ...poi.location, name: poi.title });
    },
  },
});
