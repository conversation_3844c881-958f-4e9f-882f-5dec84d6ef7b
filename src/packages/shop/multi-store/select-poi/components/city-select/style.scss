@import '~themes/abstracts/_mixins.scss';
.city-select {
  position: fixed;
  width: 100%;
  z-index: 2;
  box-sizing: border-box;
  background-color: #f8f8f8;

  .flex-wrapper {
    display: flex;

  }

  .item:first-child {
    border-top: 0;
  }

  .important-text {
    color: #ff4444;
    display: inline-block;
  }
  .icon-arrow {
    width:8px;
    height:5px;
    display: inline-block;
    padding: 0 10px 0px 4px;
    margin-top: 13px;
  }
  .icon-search {
    width: 14px;
    height: 14px;
    display: inline-block;
    margin: 8px 8px 0 0;
  }
  .city-name {
    max-width: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 30px;
    display: inline-block;
    font-size: 14px;
  }
  .icon-clear {
    width: 14px;
    height: 14px;
    display: inline-block;
    margin: 8px 10px 0 0;
  }

  .top {
    padding: 6px 15px;
    position: relative;
    background-color: #fafafa;
    &::after {
      @include border-line(bottom);
      top: -0.5px
    }
  }

  &.full-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    padding: 0;

    .search {
      overflow: auto;
      background-color: #fff;
      
      .poi {
        line-height: 14px;
        padding: 15px;
      }
      .h4 {
        font-size: 12px;
        color: #999;
        flex: 1;
      }
    }
  }

  .select {
    overflow: hidden;
    flex: 1;
    height: 30px;
    background-color: #fff;
    // border: 1px solid #e5e5e5;
    &::after {
      @include border-all(#e5e5e5);
      border-radius: 4px;
    }
    padding-left: 10px;
    position: relative;

    &.flex {
      justify-content: flex-start;
      display: flex;
      flex: 1;
    }

    .arrow-down {
      transform: rotate(90deg) scale(.8);
      font-size: 12px;
      margin: 0 10px 0 5px;
      display: none;
    }

    .field {
      height: 20px;
      padding: 0 10px;
      flex: 1;
    }
  }

  .cancel {
    text-align: center;
    line-height: 32px;
    float: right;
    z-index: 5;
    padding-left: 15px;
    font-size: 12px;
    color: #666;
  }
  .suggetion-poi__address-name {
    line-height: 14px;
    font-size: 14px;
  }
  .suggestion-poi__address-detail {
    font-size: 12px;
    color: #999;
    line-height: 12px;
    min-height: 12px;
    margin-top: 5px;
  }

  .suggestion-poi__no-data__text {
    font-size: 14px;
    line-height: 20px;
    color: #999;
    margin-top: 133px;
    text-align:center;
  }
}

  .clear-history-confirm {
    .van-dialog__message {
      text-align: center;
      padding: 20px 0;
    }
  }

  input {
    flex: 1;
    height: 100%;
    font-size: 14px;
    padding-right: 5px;
  }
  .city-select__input__placeholder {
    color: #ccc;
  }