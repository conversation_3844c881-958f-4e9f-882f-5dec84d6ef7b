<view class="{{ showkeyWordList || showSearchedPoi ? 'city-select full-screen' : 'city-select' }}">
  <view class="flex-wrapper top flex">
    <view class="select flex flex-wrapper">
      <view class="city-name" bindtap="goCityList">
        {{ selectedCity || (current === null ? '定位中' : current && current.name || '北京市') }}
      </view>
      <image class="icon-arrow" src="https://b.yzcdn.cn/public_files/2018/08/28/3fb991dc090274931be8a1cf807c62b5.png" />
      <image class="icon-search" src="https://b.yzcdn.cn/public_files/2018/08/28/e0bbd3e69bc136b8bf2e9d8820753372.png" />
      <input value="{{ keyWord }}" placeholder="输入收货地址，匹配周边门店" bindinput="getPoi" bindfocus="showkeyWord" placeholder-class="city-select__input__placeholder" />
      <image wx:if="{{ keyWord }}" bindtap="clearKeyword" class="icon-clear" src="https://b.yzcdn.cn/public_files/2018/08/28/b1a0da9510f2ed278bd284a1b7cc61d3.png" />
    </view>
    <view class="cancel" bindtap="closePop" wx:if="{{ showkeyWordList || showSearchedPoi }}">
      取消
    </view>
  </view>
  <view class="list search" wx:if="{{ showkeyWordList || showSearchedPoi }}">
    <block wx:if="{{ historySearchList.length > 0 && showkeyWordList }}">
      <view class="flex">
        <view class="h4">历史搜索</view>
        <van-icon name="delete" color="#666666" bindtap="clearHistorySearchList" />
      </view>
      <view class="item" wx:for="{{ historySearchList }}" wx:for-item="item" wx:key="index" data-type="history" data-keyword="{{ item }}" bindtap="getPoi">
        {{item}}
      </view>
    </block>
    <van-cell-group border="{{ false }}" v-if="searchedPoiList.length > 0 && showSearchedPoi">
      <van-cell wx:for="{{ searchedPoiList }}" wx:key="index" data-item="{{ item }}" bindtap="handleClick" customer-class="poi" border="{{ index !== searchedPoiList.length - 1 }}">
        <view slot="title">
          <view class="suggetion-poi__address-name">
            {{ item.beforeText }}
            <view class="important-text">{{ item.redText }}</view>
            {{ item.afterText }}
          </view>
          <view class="suggestion-poi__address-detail">{{ item.address }}</view>
        </view>
      </van-cell>
    </van-cell-group>
  </view>
  <block wx:if="{{ searchEd && searchedPoiList.length == 0 }}">
    <view class="suggestion-poi__no-data__text">未找到信息～，换个关键词试试吧</view>
  </block>
  <city-list class="city-list" current-city="{{ current }}" city-data="{{ cityData }}" bindselected="handleSelectCity" wx:if="{{ showCityList }}" />
</view>