import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    coords: Object,
    list: {
      type: <PERSON><PERSON><PERSON>,
      observer() {
        this.setYZData({
          page: 1
        });
      }
    }
  },
  data: {
  },
  methods: {
    handleClick(e) {
      const poi = e.currentTarget.dataset.poi;
      this.triggerEvent('select', { ...poi.location, name: poi.title });
    }
  }
});
