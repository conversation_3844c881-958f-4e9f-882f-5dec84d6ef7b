@import '../../base.scss';
@import './style.scss';


.zan-cell {
  position: relative;
  padding: 15px 0;
  display: flex;
  align-items: center;
  line-height: 14px;
  font-size: 14px
}

.zan-cell::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid #e5e5e5;
  border-bottom-width: 1px;
  right: 0
}

.h4 {
  font-size: 12px;
  color: #999;
}
.p {
  font-size: 14px;
}