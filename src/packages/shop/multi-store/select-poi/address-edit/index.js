import WscPage from 'pages/common/wsc-page/index';
import isPhone from '@youzan/weapp-utils/lib/validators/is-phone';
import throttle from 'utils/throttle';
import { searchAddress, gcjToBaidu } from '@/helpers/lbs';
import Toast from '@vant/weapp/dist/toast/toast';
import areaDataHelper from './utils/area';
import addressRequest from './utils/request';

const app = getApp();
const page = {
  data: {
    fetching: true,
    status: 'add',
    editData: {},
    areaOrigin: {},
    area: {},
    isSaving: false,
    isDeleting: false,
    searchResult: [],
    areaList: {},
    addressInfo: {},
    locationMap: []
  },
  onLoad(option) {
    app.fetchAreaMapData((areaData) => {
      let area;
      let status = 'add';
      let editData = {
        provinceIndex: 0,
        cityIndex: 0,
        countyIndex: 0,
        selectedValue: 0
      };
      if (option.status) {
        status = option.status;
      }
      let navBarTitle = '新增收货地址';
      if (status === 'edit') {
        navBarTitle = '编辑收货地址';
        const editAddress = app.globalData.editAddress;
        const areaCode = editAddress.areaCode || 0;
        area = areaDataHelper.formatAreaData(areaCode, areaData);
        // 处理选中的省市区
        const { provinceIndex, cityIndex, countyIndex } = this.findSelectedAddressIndex(areaCode, area);
        Object.assign(editAddress, {
          provinceIndex,
          cityIndex,
          countyIndex,
          selectedValue: areaCode
        });
        editData = editAddress;
      } else {
        area = areaDataHelper.formatAreaData(0, areaData);
      }
      wx.setNavigationBarTitle({
        title: navBarTitle
      });
      this.setYZData({
        areaOrigin: areaData,
        area,
        status,
        editData
      });
    });
    this.getSuggestion = throttle(params => searchAddress(params), 1000);
  },
  findSelectedAddressIndex(areaCode, area) {
    // 地区code格式化
    var selectedProvince = areaCode.toString().slice(0, 2) + '0000';
    var selectedCity = areaCode.toString().slice(0, 4) + '00';

    // 初始值
    var provinceIndex = 0;
    var cityIndex = 0;
    var countyIndex = 0;

    // 如果没有地区编码的话，就直接返回默认值
    if (!areaCode) {
      return { provinceIndex, cityIndex, countyIndex };
    }

    provinceIndex = area.province.findIndex(({ code }) => code == selectedProvince);
    cityIndex = area.city.findIndex(({ code }) => code == selectedCity);
    countyIndex = area.county.findIndex(({ code }) => code == areaCode);

    return { provinceIndex, cityIndex, countyIndex };
  },
  onShow() {
    this.setYZData({
      searchResult: [],
    });
  },
  onAddressDelete() {
    wx.showModal({
      title: '友情提示',
      content: '确定要删除这个地址么?',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '删除中',
            icon: 'loading',
            duration: 10000
          });

          const { editData } = this.data;
          addressRequest.removeAddress(editData.id, () => {
            wx.hideToast();
            wx.navigateBack();
          }, () => {
            wx.hideToast();
            Toast('删除地址失败, 请稍后重试');
          });
        }
      }
    });
  },
  onAddClick() {
    this.setYZData({
      editData: {
        provinceIndex: 0,
        cityIndex: 0,
        countyIndex: 0,
        selectedValue: 0
      },
      area: areaDataHelper.formatAreaData(0, this.data.areaOrigin),
      status: 'add'
    });
  },
  onAddressSave() {
    const { editData } = this.data;
    const dataToSave = {
      id: editData.id || '',
      user_name: editData.userName || '',
      tel: editData.tel,
      area_code: editData.areaCode || 0,
      address_detail: editData.addressDetail || '',
      postal_code: editData.postalCode || '',
      province: editData.province || '',
      city: editData.city || '',
      county: editData.county || '',
      lat: editData.lat,
      lon: editData.lon
    };

    const validData = this.validateAddress(dataToSave);
    if (validData) {
      return;
    }

    wx.showToast({
      title: '地址数据提交中',
      icon: 'loading',
      // 持续时间最长为10s
      duration: 10000
    });
    if (dataToSave.lat && dataToSave.lon) {
      this.saveAddress(dataToSave);
    } else {
      // 查询经纬度
      addressRequest.queryGeo(
        dataToSave.city,
        `${dataToSave.province}${dataToSave.city}${dataToSave.county}${dataToSave.address_detail}`,
        (res) => {
          let { lat, lon } = res;
          dataToSave.lon = lon;
          dataToSave.lat = lat;
          this.saveAddress(dataToSave);
        },
        () => {
          this.saveAddress(dataToSave);
        }
      );
    }
  },
  saveAddress(dataToSave) {
    const { status } = this.data;
    const safeFunc = status == 'edit' ? addressRequest.save : addressRequest.add;
    safeFunc(dataToSave, (res) => {
      // 隐藏loading弹窗
      wx.hideToast();

      if (!res.value) {
        return;
      }

      wx.navigateBack();
    }, (res = {}) => {
      // 隐藏loading弹窗
      wx.hideToast();
      var errorMsg = status == 'edit' ? '编辑地址失败' : '新增地址失败';
      Toast(res.msg || errorMsg);
    });
  },
  onInputChange(e) {
    const type = e.currentTarget.dataset.type;
    const value = e.detail.value || '';
    const editData = this.data.editData;

    editData[type] = value;

    this.setYZData({ editData }, { immediate: true });

    if (type === 'addressDetail') {
      this.onDetailChange(value);
    }
  },
  // 根据详细地址展示推荐地址
  onDetailChange(keyword) {
    // 地址变更时，重置经纬度
    const { editData } = this.data;
    editData.lat = '';
    editData.lon = '';
    this.setYZData({ editData });

    if (keyword) {
      this.getSuggestion({
        keyword,
        region: editData.city || '',
        policy: 1,
        success: (res) => {
          this.setYZData({
            searchResult: res.data,
            showDetailClear: true
          });
        }
      });
    } else {
      this.setYZData({
        searchResult: [],
        showDetailClear: false
      });
    }
  },
  onDetailFocus() {
    this.setYZData({
      showSearchResult: true,
      showDetailClear: !!this.data.editData.addressDetail
    });
  },

  onDetailBlur() {
    this.setYZData({
      showDetailClear: false,
      showSearchResult: false
    });
  },
  onSelectAddress(event) {
    const { index } = event.currentTarget.dataset;
    const selected = this.data.searchResult[index];
    const { editData } = this.data;
    const { location, title, district } = selected;

    // 剔除详细地址中的省市区部分
    let addressDetail = selected.address || '';
    if (addressDetail && district && addressDetail.findexOf(district) !== -1) {
      const tail = addressDetail.split(district)[1];
      if (tail) {
        addressDetail = tail;
      }
    }

    editData.addressDetail = `${addressDetail} ${title}`.trim();
    if (location && location.lat && location.lng) {
      const { lat, lng } = gcjToBaidu(location.lng, location.lat);
      editData.lat = lat;
      editData.lon = lng;
    }

    this.setYZData({
      editData,
      searchResult: []
    });
  },

  clearDetail() {
    const editData = this.data.editData;
    editData.addressDetail = '';
    this.setYZData({ editData }, { immediate: true });
  },
  onAreaChange(e) {
    var selectedIndex = e.detail.value || 0;
    var type = e.currentTarget.dataset.type;
    var editData = this.data.editData;
    var selectedData = this.data.area[type][selectedIndex];
    var selectedValue = selectedData.code;

    // 如果选择的没变过，就不需要触发下面的变动逻辑了
    if (editData[`${type}Index`] == selectedIndex) {
      return;
    }

    var area = areaDataHelper.formatAreaData(selectedValue, this.data.areaOrigin);
    var provinceIndex = editData.provinceIndex || 0;
    var cityIndex = editData.cityIndex || 0;
    var countyIndex = editData.countyIndex || 0;

    switch (type) {
      case 'province':
        provinceIndex = selectedIndex;
        editData.province = area.province[provinceIndex].text;
        cityIndex = 0;
        countyIndex = 0;
        editData.areaCode = undefined;
        break;
      case 'city':
        cityIndex = selectedIndex;
        editData.city = area.city[cityIndex].text;
        countyIndex = 0;
        editData.areaCode = undefined;
        break;
      case 'county':
        countyIndex = selectedIndex;
        editData.county = area.county[countyIndex].text;
        editData.areaCode = area.county[countyIndex].code;
        break;
      default:
        break;
    }

    editData = Object.assign({}, editData, {
      provinceIndex,
      cityIndex,
      countyIndex,
      selectedValue,
      lat: '',
      lon: ''
    });

    this.setYZData({ editData, area });
  },
  // 地址编辑新增 结束
  /**
   * 验证地址项目的合法性
   * @param {Object} data
   * @param {String} data.id
   * @param {String} data.user_name
   * @param {Number} data.tel
   * @param {Number} data.area_code
   * @param {String} data.address_detail
   * @param {Number} data.postal_code
   * @param {String} data.province
   * @param {String} data.city
   * @param {String} data.county
   */
  validateAddress(data) {
    // 为空校验
    if (!data.user_name) {
      Toast('请填写收货人姓名');
      return {
        type: 'empty',
        field: 'user_name'
      };
    }

    if (!data.tel) {
      Toast('请填写联系电话');
      return {
        type: 'empty',
        field: 'tel'
      };
    }

    if (!data.area_code) {
      Toast('请选择收货地区');
      return {
        type: 'empty',
        field: 'area_code'
      };
    }

    if (!data.address_detail) {
      Toast('请填写详细地址');
      return {
        type: 'empty',
        field: 'address_detail'
      };
    }

    // 数据合法性校验
    if (!isPhone(data.tel)) {
      Toast('请填写正确的联系电话');
      return {
        type: 'error',
        field: 'tel'
      };
    }
  }
};


WscPage(page);
