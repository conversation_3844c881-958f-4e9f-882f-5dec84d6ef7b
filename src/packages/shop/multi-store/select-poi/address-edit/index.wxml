
<view class="address-edit zan-panel">
  <view class="address-edit__item zan-cell zan-field">
    <text class="zan-field__title">收货人</text>
    <input
      class="address-edit__input zan-field__input zan-cell__bd"
      data-type="userName"
      bindinput="onInputChange"
      value="{{ editData.userName || '' }}"
      placeholder="名字"
    />
  </view>
  <view class="address-edit__item zan-cell zan-field">
    <text class="zan-field__title">联系电话</text>
    <input
      class="address-edit__input zan-field__input zan-cell__bd"
      type="number"
      maxlength="20"
      data-type="tel"
      bindinput="onInputChange"
      value="{{ editData.tel || '' }}"
      placeholder="手机或固定电话"
    />
  </view>
  <view class="address-edit__item zan-cell zan-field">
    <view class="zan-field__title van-pull-left">收货地区</view>
    <view wx:if="{{ area }}" class="zan-field__input address-edit__pickers zan-cell__bd">
      <picker mode="selector"
        class="address-edit__picker"
        range="{{ area.province }}"
        range-key="text"
        value="{{ editData.provinceIndex }}"
        data-type="province"
        bindchange="onAreaChange">
        <view class="picker">
          {{ area.province[editData.provinceIndex].text }}
        </view>
      </picker>
      <picker mode="selector"
        class="address-edit__picker"
        range="{{ area.city }}"
        range-key="text"
        value="{{ editData.cityIndex }}"
        data-type="city"
        bindchange="onAreaChange">
        <view class="picker">
          {{ area.city[editData.cityIndex].text }}
        </view>
      </picker>
      <picker mode="selector"
        class="address-edit__picker"
        range="{{ area.county }}"
        range-key="text"
        value="{{ editData.countyIndex }}"
        data-type="county"
        bindchange="onAreaChange">
        <view class="picker">
          {{ area.county[editData.countyIndex].text }}
        </view>
      </picker>
    </view>
  </view>
  <view class="address-edit__item zan-cell zan-field">
    <text class="zan-field__title">详细地址</text>
    <input
      class="address-edit__input zan-field__input zan-cell__bd"
      data-type="addressDetail"
      value="{{ editData.addressDetail || '' }}"
      placeholder="街道门牌信息"
      bindfocus="onDetailFocus"
      bindblur="onDetailBlur"
      bindinput="onInputChange"
    />
    <van-icon wx:if="{{ showDetailClear }}" name="close" bind:touchstart="clearDetail" />
  </view>
  <van-cell-group wx:if="{{ showSearchResult && searchResult.length }}">
    <van-cell
      wx:for="{{ searchResult }}"
      wx:for-item="express"
      wx:key="{{ express.id }}"
      title="{{ express.title }}"
      label="{{ express.address }}"
      data-index="{{ index }}"
      bind:touchstart="onSelectAddress"
    >
      <van-icon slot="icon" name="location-o" />
    </van-cell>
  </van-cell-group>
  <view wx:else class="address-edit__item zan-cell zan-field zan-cell--last-child">
    <text class="zan-field__title">邮政编码</text>
    <input
      class="address-edit__input zan-field__input zan-cell__bd"
      data-type="postalCode"
      bindinput="onInputChange"
      value="{{ editData.postalCode || '' }}"
      placeholder="邮政编码(选填)"
    />
  </view>
</view>
<view class="zan-btns" wx:if="{{ !showSearchResult || !searchResult.length }}">
  <button class="zan-btn zan-btn--primary" catchtap="onAddressSave">保存</button>
  <button wx:if="{{ status == 'edit' }}" class="zan-btn" catchtap="onAddressDelete">删除收货地址</button>
</view>
<van-toast id="van-toast" />
