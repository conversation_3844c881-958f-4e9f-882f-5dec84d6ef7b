import each from '@youzan/weapp-utils/lib/each';

var formatAreaData = function (code, area) {
  var formatedAreaData = {};

  // 地区code格式化
  var selectedProvince = code.toString().slice(0, 2) || -1;
  var selectedCity = code.toString().slice(0, 4) || -1;

  // 省市区循环格式化
  // 省份信息全要
  formatedAreaData.province = filterAndFormatAreaData(0, area.province, '省份');
  formatedAreaData.city = filterAndFormatAreaData(selectedProvince, area.city, '城市');
  formatedAreaData.county = filterAndFormatAreaData(selectedCity, area.county, '区县');

  return formatedAreaData;
};

var filterAndFormatAreaData = function (areaCode, areaData, defaultText) {
  var areaList = [];

  if (defaultText) {
    areaList.push({ text: defaultText, code: areaCode });
  }

  // 这里可以做个优化，因为区县的遍历一次遍历3000多个项目，有点多
  // 考虑到原始areaOrigin里面地址code码都是从小到大排列的
  each(areaData, (text, code) => {
    // 如果有areacode或者省市区编码开始不对应
    if (areaCode && code.indexOf(areaCode) !== 0) {
      return;
    }

    areaList.push({ text, code });
  });

  return areaList;
};

export default {
  formatAreaData
};
