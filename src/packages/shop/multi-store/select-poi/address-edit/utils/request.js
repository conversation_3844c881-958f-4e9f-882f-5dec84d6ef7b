import { gcjToBaidu } from '@/helpers/lbs';

const app = getApp();

export default {
  // 修改单个地址
  save(data, success, fail) {
    app.carmen({
      api: 'account.address/1.0.0/modifyById',
      data: Object.assign({
        community: ''
      }, data),
      success: (res) => {
        success(res);
      },
      fail: (res) => {
        fail && fail(res);
      }
    });
  },
  // 新增一个地址
  add(data, success, fail) {
    app.carmen({
      api: 'account.address/1.0.0/add',
      data: Object.assign({
        id: '',
        community: ''
      }, data),
      success: (res) => {
        success(res);
      },
      fail: (res) => {
        fail && fail(res);
      }
    });
  },
  // 获取地址列表
  getAddressList(success, errorFunc) {
    app.carmen({
      api: 'account.address/1.0.0/get',
      data: {
        type: 1
      },
      success(res) {
        success(res);
      },
      fail(res) {
        errorFunc && errorFunc(res);
      }
    });
  },
  // 删除地址
  removeAddress(id, success, fail) {
    app.carmen({
      api: 'account.address/1.0.0/removeById',
      data: {
        id
      },
      success(res) {
        success(res);
      },
      fail() {
        fail && fail();
      }
    });
  },
  // 查询经纬度
  queryGeo(city, address, success, fail) {
    app.carmen({
      api: 'youzan.logistics.geo/1.0.0/get',
      data: {
        city,
        address
      },
      method: 'GET',
      success(res) {
        let { lat, lng } = res;
        let transformed = gcjToBaidu(lng, lat);
        // 高德坐标转百度坐标
        success({ lat: transformed.lat, lon: transformed.lng });
      },
      fail(res) {
        fail && fail(res);
      }
    });
  }
};
