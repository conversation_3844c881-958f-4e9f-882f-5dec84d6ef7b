import navigate from '@/helpers/navigate';
import Toast from '@vant/weapp/dist/toast/toast';
import { tryLocation, parseDistance } from '@/helpers/lbs';

function tryLocationAgain(success, fail) {
  const app = getApp();
  checkScope(
    'userLocation',
    // 用户已经授权，说明是接口调用失败
    () => fail,
    // 用户未授权，去授权
    () => {
      wx.showModal({
        content: `打开"${
          app.getShopInfoSync().base.shop_name
        }"的地理位置权限，为你推荐最近的门店，是否去打开`,
        success: e => {
          if (e.cancel) return;
          wx.openSetting({
            success: ({ authSetting }) => {
              authSetting['scope.userLocation'] && tryLocation(success, fail);
            }
          });
        }
      });
    }
  );
}

function checkScope(scope, success, fail) {
  wx.getSetting({
    success({ authSetting }) {
      if (authSetting[`scope.${scope}`]) return success();
      fail();
    },
    fail
  });
}

function saveLocation(query) {
  const app = getApp();

  app.request({
    path: '/wscump/multistore/setBuyerLocation.json',
    query
  }).catch(() => null);
}

function fetchShops(query) {
  const app = getApp();
  if (query.page === 1) {
    this.data.list = [];
  }

  this.setYZData({ loading: true });

  app.request({
    path: '/wscump/multistore/recommendOfflineListNew.json',
    query
  }).then(res => {
    let list = res.items || [];
    list = list.map(item => {
      item.distance = parseDistance(item.distance);
      return item;
    });
    this.setYZData({
      list: this.data.list.concat(list),
      loading: false,
      nodata: this.data.list.length === 0 && list.length === 0 && !this.data.isUsedLocation,
      nomore: list.length < query.perpage
    });
  }).catch(err => {
    this.setYZData({ loading: false });
    console.log(err);
    Toast(err.msg || '获取店铺列表失败');
  });
}

function saveStoreAndJumpBack(store, delta = 1) {
  const app = getApp();
  let pages = getCurrentPages();

  wx.showLoading({ title: '加载数据中...' });
  if (pages.length - 1 - delta < 0) {
    app.setOfflineId(store.id);
    return navigate.switchTab({ url: '/packages/home/<USER>/index' });
  }

  let page = pages[pages.length - 1 - delta];
  let pagePath = this.redirectto || '/' + page.route;
  let isMultiPage = /packages\/shop\/multi-store/.test(pagePath);
  let isFeaturePage = /(packages\/(old-)?home\/(dashboard|feature|tab))|packages\/showcase-template/.test(
    pagePath
  );

  // 设置了需要跳转的路径
  if (this.redirectto) {
    app.setOfflineId(store.id);
    wx.hideLoading();
    return navigate.switchTab({ url: this.redirectto });
  }

  // 多网点页面则到首页
  if (
    isMultiPage
    || pages.length === 1 /** 鬼知道这个条件是不是成立，只是为了保险，不会因为 navigateBack 导致退出 */
  ) {
    app.setOfflineId(store.id);
    wx.hideLoading();

    return navigate.switchTab({ url: '/packages/home/<USER>/index' });
  }

  // 主页和feature页面需要等待页面数据加载完成后才返回，直接返回会出现页面刷新不及时
  if (isFeaturePage) {
    pages[pages.length - 1].__uniqueKey__ = page.__uniqueKey__;
    app.setOfflineId(
      store.id,
      notChange => {
        wx.hideLoading();
        // 网点未变时不会触发app:offlineId:change事件，不会执行这个方法。
        if (notChange && delta !== 0) {
          wx.navigateBack({ delta });
        }
      },
      { delta, wxExparserNodeId: page.__wxExparserNodeId__ }
    );
  } else {
    app.setOfflineId(store.id);
    wx.hideLoading();
    if (delta !== 0) {
      wx.navigateBack({ delta });
    }
  }
}

export default {
  tryLocationAgain,
  checkScope,
  fetchShops,
  saveStoreAndJumpBack,
  saveLocation
};
