<template name="shop-or-location-list">
  <scroll-view scroll-y class="choose-shop__shop-list" bindtap="navigateBack" scroll-top="{{scrollPosition}}" bindscrolltolower="onToLower" >
    <block wx:if="{{isAddress}}">
      <view data-item-index="{{index}}" wx:for-index="index" wx:for="{{list}}" wx:key="*this" wx:for-item="item">
        <view class="zan-cell zan-cell--access result-item">
          <view class="zan-cell__bd">
            <view class="zan-cell__text" style="color: #333">{{item.name}}</view>
            <view class="zan-cell__desc" style="margin-top: 10px">{{item.address}}</view>
          </view>
          <view class="zan-cell__ft"></view>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="result-item" data-item-index="{{index}}" wx:for-index="index" wx:for="{{list}}" wx:key="*this" wx:for-item="item">
        <view class="location-result__item-base">
          <view class="zan-cell__text" style="color: #333"><view class="zan-icon zan-icon-shop"></view> {{item.name}} <text wx:if="{{!item.opening}}" class="zan-tag zan-tag--disabled">暂停接单</text></view>
          <view wx:if="{{item.distance}}" class="van-font-12">{{item.distance}}</view>
        </view>
        <view class="zan-cell zan-cell--access">
          <view class="zan-cell__bd">
            <view class="zan-cell__desc" style="margin-top: 10px">{{item.address.province}} {{item.address.city}} {{item.address.area}} {{item.address.address}}</view>
          </view>
          <view class="zan-cell__ft"></view>
        </view>
      </view>
    </block>

    <van-loading wx:if="{{ loading }}" size="24px" style="display: block;width: 24px;margin: 10px auto;" />
    <view wx:if="{{!loading && nomore}}" style="text-align: center;margin: 15px 0;color: #BFBFC3" class="van-font-14">没有更多了</view>
  </scroll-view>
</template>