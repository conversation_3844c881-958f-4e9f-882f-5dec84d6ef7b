<!-- 筛选器入口 -->
<van-cell-group class="area-filter-container" v-if="filterLevel > 0">
  <van-cell
    title="{{ currentFilter }}"
    bindtap="startSelect"
    is-link
    right-icon-class="area-filter__icon-rotate"
  >
  </van-cell>
</van-cell-group>

<!-- 筛选器 -->
<van-popup 
  show="{{ show }}" 
  position="bottom" 
  overlay
>
  <view class="loading-layer {{ loading ? '' : 'loading-layer__hidden' }} ">
    <van-loading
      type="spinner"
      color="white"
      custom-class="loading-layer__loading"
    />
  </view>
  
  <view class="container">
    <view class="header">
      <view class="tabs">
        <view 
          data-tab-index="{{ 0 }}"
          bindtap="selectTab"
          class="tab {{ currentCity ? 'choosed-tab' : '' }} {{ currentTabIndex === 0 ? 'selected-tab' : '' }}"
        >
          城市
        </view>
        
        <view 
          wx:if="{{ filterLevel > 1 && currentCity && currentCity !== '全部城市' }}"
          data-tab-index="{{ 1 }}"
          bindtap="selectTab"
          class="tab {{ currentTabIndex === 1 ? 'selected-tab' : '' }} {{ currentRegionId ? 'choosed-tab' : '' }}"
        >
          区县
        </view>
        <view 
          class="close-btn"
          bindtap="handleCloseClick"
        >
          <image src="https://b.yzcdn.cn/public_files/2018/09/06/b56b5719b03c9f2cc81bfde8f1205b40.png"/>
        </view>
      </view>
    </view>

    <!-- 列表 -->
    <view
      class="list-block"
    >
      <!-- 城市列表当前列表头部城市的首字母栏 -->
      <van-cell
        border="{{ false }}"
        custom-class="current-city-block-header"
        title="{{ currentWord }}"
        wx:if="{{ currentWord && !currentTabIndex }}"
      />

      <!-- 城市列表 -->
      <scroll-view
        scroll-y
        bindscroll="handleCityListScroll"
        class="city-list-container list-container"
        wx:if="{{ !currentTabIndex }}"
        wx:key="city"
      >
        <van-cell
          bindtap="handleCitySelected"
          custom-class="{{ currentCity==='全部城市' ? 'selected-cell city-cell' : 'city-cell' }}"
          border="{{ false }}"
          title="全部城市"
        />

        <van-cell-group 
          wx:for="{{ cityList }}"
          wx:for-item="cities"
          wx:for-index="word"
          border="{{ false }}"
          wx:key="word"
        >
          <van-cell
            title="{{ word }}"
            custom-class="city-block-header"
          />

          <van-cell 
            border="{{ false }}"
            wx:for="{{ cities }}"
            wx:for-item="city"
            wx:for-index="cityIndex"
            data-storeNum="{{ city.num }}"
            data-cityName="{{ city.city }}"
            data-city="{{ city }}"
            data-cityIndex="{{ cityIndex }}"
            wx:key="city"
            bindtap="handleCitySelected"
            custom-class="{{ currentCity===city.city ? 'selected-cell city-cell' : 'city-cell' }}"
            title="{{ city.city }}"
            titleWidth="100%"
          />
        </van-cell-group>
      </scroll-view>

      <!-- 区县列表 -->
      <scroll-view
        wx:else
        wx:key="area"
        class="list-container"
        scroll-y
      >
        <van-cell-group border="{{ false }}">
          <van-cell 
            custom-class="{{ currentRegionId === -1 ? 'selected-cell area-cell' : 'area-cell' }}"
            bindtap="handleAreaSelected"
            border="{{ false }}"
            title="全部区县"
          />
          <van-cell
            border="{{ false }}"
            wx:for="{{ areaList }}"
            wx:for-item="area"
            wx:for-index="areaIndex"
            wx:key="regionId"
            data-area="{{ area }}"
            data-areaIndex="{{ areaIndex }}"
            bindtap="handleAreaSelected"
            custom-class="{{ currentRegionId === area.regionId ? 'selected-cell area-cell' : 'area-cell' }}"
            title="{{ area.district }}"
            titleWidth="100%"
          />
        </van-cell-group>
      </scroll-view>
    </view>
  </view>
</van-popup>
