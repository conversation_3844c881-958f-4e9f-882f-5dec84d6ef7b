import WscComponent from 'pages/common/wsc-component/index';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

const app = getApp();

WscComponent({
  properties: {
    cityList: Object,
    alphaStoresData: Array,
    filterLevel: {
      type: Number,
      value: 0
    }
  },
  data: {
    // 筛选器入口的文案
    currentFilter: '全部城市',
    // 控制筛选器的显示
    show: false,
    // 0：城市，1：区县
    currentTabIndex: 0,
    // 区县数据
    areaList: [],
    // 已选择的区县Id，null：未选，-1：全部区县
    currentRegionId: null,
    loading: false
  },
  methods: {
    startSelect() {
      this.setYZData({
        show: true
      });
    },

    selectTab(e) {
      const { tabIndex } = e.currentTarget.dataset;
      let { currentWord = '' } = this.data;
      // 切换到区县tab时，不显示当前城市首字母栏
      if (tabIndex !== 0) {
        currentWord = '';
      }
      this.setYZData({
        currentTabIndex: tabIndex,
        currentWord
      });
    },

    handleCityListScroll(evt) {
      const { alphaStoresData } = this.data;
      const scrollTop = evt.detail.scrollTop;
      // 城市列表第一栏为全部城市，不显示当前城市首字母栏
      if (scrollTop < 44) {
        this.setYZData({
          currentWord: ''
        });
        return;
      }
      let len = alphaStoresData.length;
      // 计算当前城市首字母
      for (var i = 0; i < len; i++) {
        if (scrollTop < (alphaStoresData[i].beforeCities + 1) * 44 + 26 * (i + 1) - 7) {
          this.setYZData({
            currentWord: alphaStoresData[i].alpha
          });
          break;
        }
      }
    },

    handleCitySelected(e) {
      const { city } = e.currentTarget.dataset;
      // city为空->选择了全部城市
      if (!city) {
        this.setYZData({
          currentCity: '全部城市',
          currentRegionId: null,
          currentFilter: '全部门店',
          show: false,
          loading: false
        });
        this.triggerEvent('areafiltered', { regionId: '', city: '' });
        return;
      }
      const { filterLevel } = this.data;
      // 当筛选级别为1时，选中城市后不再继续筛选
      if (filterLevel === 1) {
        this.setYZData({
          currentFilter: city.city,
          currentCity: city.city,
          show: false,
          loading: false
        });
        this.triggerEvent('areafiltered', { city: city.city });
        return;
      }

      this.setYZData({
        loading: true
      });

      // 获取所选城市的区县数据，并切换tab
      this.getCityRegionsData(city.city).then((res) => {
        this.setYZData({
          areaList: res,
          currentTabIndex: 1,
          loading: false,
          currentWord: ''
        });
      }).catch(() => {
        this.setYZData({
          loading: false
        });
      });

      this.setYZData({
        currentRegionId: null,
        currentCity: city.city
      });
      setTimeout(() => {
        this.setYZData({
          loading: false
        });
      }, 6000);
    },

    getCityRegionsData(city) {
      return app.request({
        path: '/wscump/multistore/getListDistrictByKdtIdAndCity.json',
        data: {
          city
        }
      }).then((res) => mapKeysCase.toCamelCase(res));
    },

    handleAreaSelected(e) {
      const { area } = e.currentTarget.dataset;
      const { currentCity } = this.data;

      // area为空->选中全部区县
      if (!area) {
        this.setYZData({
          currentRegionId: -1,
          show: false,
          currentFilter: `${currentCity}\\全部区县`
        });
        this.triggerEvent('areafiltered', { regionId: '', city: currentCity });
        return;
      }

      this.setYZData({
        currentRegionId: area.regionId,
        currentFilter: `${area.city}\\${area.district}`,
        show: false
      });

      this.triggerEvent('areafiltered', {
        city: area.city,
        regionId: area.regionId
      });
    },

    handleCloseClick() {
      this.setYZData({
        show: false
      });
    }
  }
});
