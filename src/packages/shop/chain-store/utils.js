import args from '@youzan/weapp-utils/lib/args';
import omit from '@youzan/weapp-utils/lib/omit';
import { setIsReLaunch } from '@/base-api/shop/chain-store.js';
import {
  SuitableBizType,
  EnterShopSceneMap,
  DeliveryMethodMap,
} from './constant';
import { getCurrentPage } from 'shared/common/base/wsc-component';
import sortBy from 'lodash/sortBy';

const app = getApp();

function isBlankPage(url) {
  return /pages\/common\/blank-page/.test(url);
}

function addLocalRetailBizType(bizListJson) {
  let suitableBizList = [];
  try {
    if (bizListJson) {
      suitableBizList = JSON.parse(decodeURIComponent(bizListJson)) || [];
    }
  } catch (_) {
    //
  }
  suitableBizList.push({
    bizType: SuitableBizType.LocalRetail,
    bizAlias: {
      filterStoreNotSupportDelivery: false,
    },
  });
  return encodeURIComponent(JSON.stringify(suitableBizList));
}

export function formatRedirectUrl(url) {
  let redirectUrl = url || '/packages/home/<USER>/index';
  if (isBlankPage(redirectUrl)) {
    const query = args.getAll(redirectUrl);
    let weappSharePath = query.weappSharePath;
    if (weappSharePath) {
      weappSharePath = decodeURIComponent(weappSharePath);
      const sharePathQuery = args.getAll(weappSharePath);
      // B端复制总店页面链接进入小程序，手动选完店铺后需要移除kdt_id以免又更新成总店的kdt_id
      const newShareQuery = omit(sharePathQuery, 'kdt_id');
      let [newSharePath] = weappSharePath.split('?');
      newSharePath = args.add(newSharePath, newShareQuery);
      redirectUrl = `/pages/common/blank-page/index?weappSharePath=${encodeURIComponent(
        newSharePath
      )}`;
    }
  }

  return redirectUrl;
}

export function redirectPage(currentKdtId, kdtId, redirectUrl) {
  // 选择网店重定向回去时，先把 relaunch 标志置为 false
  setIsReLaunch(false);

  redirectUrl = args.add(redirectUrl, { subKdtId: kdtId });

  // 切换店铺时删除强制自动进店标示
  redirectUrl = args.remove(redirectUrl, 'shopAutoEnter');

  // 如果kdtId没变，则直接转跳页面
  if (currentKdtId === kdtId) {
    wx.reLaunch({
      url: redirectUrl || '/packages/home/<USER>/index',
    });
    return;
  }

  const url = formatRedirectUrl(redirectUrl);
  wx.reLaunch({
    url,
  });
}

export function parseData(rawData) {
  return rawData.items.map((item) => {
    const {
      storeName,
      storeKdtId,
      distance,
      storeAddress,
      lat,
      lon,
      shopMetaInfo,
    } = item;

    const { offlineShopOpen, onlineShopOpen } = shopMetaInfo || {};
    return {
      name: storeName,
      kdtId: +storeKdtId,
      isCurrent: storeKdtId === rawData.lastVisitedKdtId,
      distance: distance && distance / 1000,
      desc: storeAddress,
      lat,
      lon,
      offlineShopOpen,
      onlineShopOpen,
      rawItem: item,
    };
  });
}

export function parseLocalRetaillData(items, options) {
  const { deliveryMethod } = options || {};

  // 按照 1.是否超出配送范围 2.是否营业 3.距离 权重排序下。
  return sortBy(
    items.map((item) => {
      const { rawItem } = item;
      const {
        onlineBusinessHours = {},
        supportDelivery = true,
        // 客服电话区号
        customerServiceAreaCode = '',
        //  客服电话或手机号。输入手机号时区号可为空
        customerServicePhoneNumber = '',
      } = rawItem;
      const { summary = '', currentBusinessStatus = {} } = onlineBusinessHours;
      const { isOpen = true } = currentBusinessStatus;

      // 自提模式下不受配送范围限制
      const isSupportDelivery =
        +deliveryMethod === DeliveryMethodMap.SelfFetch || supportDelivery;

      return {
        ...item,
        // 是否在配送范围内
        isSupportDelivery,
        // 营业时间
        summary,
        // 是否在营业时间内
        isOpen,
        // 超出配送范围或不在营业时间内时禁止点击
        disableClick: !isOpen || !isSupportDelivery,
        customerServiceAreaCode,
        customerServicePhoneNumber,
      };
    }),
    (o) => {
      return !o.isOpen;
    },
    (o) => {
      return !o.isSupportDelivery;
    },
    'distance'
  );
}

export function injectKeyToData(data = {}, options) {
  const {
    dbKey = '',
    alias = '',
    umpAlias = '',
    umpType = '',
    redirectUrl,
    // I don't know why call it goodId
    goodId: goodsId = '',
    dbid,
    from = '',
    kdt_id = +app.getKdtId(),
    ignoreStoreLimit = '',
    enterShopOptionsJsonString = '',
    isRedirectDisable = '',
    isHideLocation,
    bizIndex,
    bizListJson = '',
  } = options;

  const dbData = app.db.get(dbid);

  const result = {
    ...data,
    dbKey,
    goodsId,
    from,
    kdt_id,
    umpAlias,
    umpType,
    ignoreStoreLimit,
    enterShopOptionsJsonString,
    isRedirectDisable,
    isHideLocation,
    bizIndex,
    bizListJson,
  };

  if (dbKey === 'location' && dbData) {
    Object.assign(result, {
      lon: dbData.lng,
      lat: dbData.lat,
      currentLocation: dbData.poi || dbData.currentLocation,
    });
  }

  if (dbKey === 'alias') {
    result.alias = alias;
  }

  if (umpType === 'coupon') {
    result.umpAlias = alias || umpAlias;
  } else if (+data.sceneId === +EnterShopSceneMap.LocalRetail) {
    // 非优惠券定制场景，B端进店规则的进店模式为同城模式时，列表请求添加同城业务参数
    const { deliveryMethod } = getExtOpt();
    result.deliveryMethod = deliveryMethod;
    result.bizListJson = addLocalRetailBizType(result.bizListJson);
  }
  result.redirectUrl = redirectUrl ? decodeURIComponent(redirectUrl) : '';
  return result;
}

export function getObjWithoutEmptyAttr(obj) {
  return Object.keys(obj).reduce((add, key) => {
    if (obj[key]) {
      add[key] = obj[key];
    }
    return add;
  }, {});
}

export function formatListQuery({
  dbKey: type,
  keyword,
  lat,
  lon,
  alias,
  umpAlias,
  umpType,
  pageSize,
  kdt_id,
  from,
  isNewEnterShop,
  isFilterClose,
  countryName,
  provinceName,
  cityName,
  countyName,
  ignoreStoreLimit,
  enterShopOptionsJsonString,
  bizIndex,
  bizListJson,
  // 同城零售配送方式
  deliveryMethod,
  shopOperationStatusList,
  ignoreRetailScene,
  sceneId,
}) {
  const result = {
    from,
    pageSize,
    kdt_id,
    isNewEnterShop,
    isFilterClose,
    countryName,
    provinceName,
    cityName,
    countyName,
    deliveryMethod,
    ignoreStoreLimit,
    enterShopOptionsJsonString,
    bizIndex,
    bizListJson,
    shopOperationStatusList,
    sceneId,
  };

  switch (type) {
    case 'keyword':
      result.keyword = keyword;
      break;
    case 'location':
      result.lon = lon;
      result.lat = lat;
      break;
    default:
      result.keyword = '';
  }

  // 优惠券适用店铺列表
  if (alias) {
    result.umpAlias = alias;
    result.umpType = 'coupon';
  }

  // 其他营销活动适配连锁
  if (umpAlias && umpType) {
    result.umpAlias = umpAlias;
    result.umpType = umpType;
  }

  // 忽略零售场景值，删除相关参数
  if (ignoreRetailScene) {
    delete result.bizListJson;
    delete result.deliveryMethod;
  }

  return getObjWithoutEmptyAttr(result);
}

export function getExtOpt() {
  const { options = {} } = getCurrentPage(this);
  const { extOptJson } = options;
  try {
    const extOpt = JSON.parse(decodeURIComponent(extOptJson)) || {};
    return extOpt;
  } catch (_) {
    return {};
  }
}
