import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { selectSubShop, redirect, getStoreList, getLocation } from './process';
import { getPageData } from './init-page-data';

export default {
  ...YunPageConfig,

  onLoad(options) {
    const sdk = this.getYunSdk();
    this.__yunPage = sdk.page;

    Object.assign(this.__yunPage.data, getPageData(options));

    // 注册流程
    sdk.setPageProcess('selectSubShop', selectSubShop);
    sdk.setPageProcess('redirect', redirect);
    sdk.setPageProcess('getStoreList', (data) => getStoreList(data, options));
    sdk.setPageProcess('getLocation', getLocation);
  }
};
