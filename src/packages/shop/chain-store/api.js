import promisify from 'utils/promisify';
import { getObjWithoutEmptyAttr } from './utils';
import { EnterShopSceneMap } from './constant';

const enterShopSceneKey = 'shop_visit_current_scene';

const app = getApp();

const carmen = promisify(app.carmen);

function getList(
  {
    lon,
    lat,
    keyword,
    umpAlias,
    umpType,
    page,
    pageSize = 20,
    kdt_id,
    mode,
    from,
    isNewEnterShop,
    appendShopMetaInfo,
    isFilterClose,
    countryName,
    provinceName,
    cityName,
    countyName,
    ignoreStoreLimit,
    enterShopOptionsJsonString,
    bizListJson,
    deliveryMethod,
    shopOperationStatusList
  },
  path
) {
  const data = {
    mode,
    page,
    pageSize,
    hqKdtId: app.getHQKdtId(),
    kdt_id,
    storeName: keyword,
    from,
    isNewEnterShop,
    isFilterClose,
    countryName,
    provinceName,
    cityName,
    countyName,
    ignoreStoreLimit,
    enterShopOptionsJsonString,
    bizListJson,
    deliveryMethod,
    shopOperationStatusList,
  };

  if (lat && lon) {
    data.lon = lon;
    data.lat = lat;
  }

  if (umpAlias && umpType) {
    data.umpAlias = umpAlias;
    data.umpType = umpType;
  }

  // 优惠券支持搜索门店
  if (appendShopMetaInfo) {
    data.appendShopMetaInfo = 1;
  }

  return app.request({
    path,
    data: getObjWithoutEmptyAttr(data),
  });
}

// 获取店铺列表
export function getShopList(data = {}) {
  const path = 'wscump/multistore/store/list.json';
  return getList(data, path);
}

// 获取适用店铺列表（包含门店&网店，目前适用于券码场景）
export function getAllStoreList(data = {}) {
  const path = '/wscump/multistore/store/shop-list.json';
  return getList(data, path);
}

// 获取商品alias
export function getGoodsAlias({ goodsId, kdtId }) {
  const data = {
    goods_id: goodsId,
    parent_kdt_id: app.getHQKdtId(),
    sub_kdt_id: kdtId,
  };
  return carmen({
    method: 'GET',
    api: 'youzan.retail.ump.common.subgoods/1.0.0/get',
    data,
  });
}

export function getEnterShopScene() {
  return app
    .request({
      path: '/wscshop/shop/config.json',
      data: {
        key: enterShopSceneKey,
        kdt_id: app.getHQKdtId(),
      },
    })
    .catch(() => {
      return {
        [enterShopSceneKey]: EnterShopSceneMap.Normal,
      };
    });
}
