import { SuitableBizType } from '../../constant';
import { queryTradeConfig } from './api';

const CUSTOMER_LATITUDE_CACHE = '_customer_latitude_cache';

const app = getApp();

Component({
  properties: {
    useCancel: <PERSON><PERSON>an,
    shopLists: {
      type: A<PERSON>y,
      observer(data) {
        // ?? ob obj
        if (!this.initShopList && data.length > 0) {
          this.initShopList = data;
          this.setData({
            initShopList: data,
          });
        }

        this.setData({
          showHistory: !data?.length,
        });
      },
    },
    title: String,
    hiddenListTitle: {
      type: Boolean,
      value: false,
    },
    hiddenShopList: {
      type: Boolean,
      value: false,
    },
    placeholder: String,
    redirectUrl: {
      type: String,
    },
    normalTitle: String,
    focusTitle: String,
    blankContent: String,
    noMore: Boolean,
    isOffline: Boolean,
    bizType: {
      type: Number | String,
      observer(data) {
        this.setData({
          isLocalRetailCustom: +data === +SuitableBizType.LocalRetail,
        });
      },
    },
    addCreate: Bo<PERSON>an,
  },

  data: {
    isFocus: false,
    keyword: '',
    showHistory: false,
  },

  methods: {
    onSelectKeyword(event) {
      this.handleInput(event);
    },
    handleSearch({ detail: { value } = {} }) {
      if (this.data.isLocalRetailCustom) {
        // 零售店铺 自定义存储搜索店铺记录
        this.retailShopSearchHistoryComponent = this.selectComponent(
          '#retail-shop-search-histroy'
        );
        this.retailShopSearchHistoryComponent.saveKeyword(value);
      }
    },
    searchFocus() {
      // 定位组件隐藏
      this.setData({
        useCancel: true,
        hiddenListTitle: true,
        isFocus: true,
      });
      const { isFocus } = this.data;
      this.triggerEvent('focus', { isFocus });
    },

    handleChooseItem({ detail }) {
      const { shop = {} } = detail;

      if (shop.lon && shop.lat) {
        // 搜索定位缓存数据
        wx.setStorageSync(CUSTOMER_LATITUDE_CACHE, {
          lng: shop.lon,
          lat: shop.lat,
          timestamp: Date.now(),
        });
      }
      this.triggerEvent('select', { detailValue: shop });
    },

    handleInput(event) {
      // 用input的value值去调接口
      const { detail } = event;
      const { value } = detail;

      if (!value) {
        this.setData({
          hiddenShopList: true,
          shopLists: [],
          hiddenListTitle: true,
          keyword: '',
        });
        return;
      }

      this.setData({
        hiddenListTitle: true,
        hiddenShopList: false,
        keyword: value,
      });

      this.triggerEvent('change', { detailValue: value });
    },

    handleCancel() {
      // 点击取消，回到初始页，
      this.setData({
        useCancel: false,
        hiddenShopList: false,
        shopLists: this.data.initShopList,
        hiddenListTitle: false,
        isFocus: false,
        keyword: '',
      });

      this.triggerEvent('focus', { isFocus: false });
    },

    async getTradeConfig() {
      if (this._forcePoiSelect) return;
      const ret = await queryTradeConfig('local_delivery_position');
      this._forcePoiSelect = ret.config === '1';
    },

    async toAddAddress() {
      await this.getTradeConfig();
      const dbid = app.db.set({
        list: [],
        id: null,
        forcePoiSelect: this._forcePoiSelect,
        delta: 1,
      });
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateTo({
        url: `/packages/trade-buy-subpage/order/address-edit/index?dbid=${dbid}&isShowRetailDeliveryAddress=true&isRetailAdd=1&isCityForcePoiSelect=${Number(this._forcePoiSelect)}`,
      });
    },
  },
});
