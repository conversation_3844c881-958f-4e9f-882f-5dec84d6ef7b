.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-container {
    display: flex;
    justify-content: space-between;
    margin: 8px 16px;
  }

  .add-address {
    font-size: 14px;
    color: #333333;
    width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
  }

  .shop-list-container {
    flex: 1 0 auto;
    // 不设置高度 iphone11下自动无法撑开充满屏幕
    height: 400px;
  }
}
