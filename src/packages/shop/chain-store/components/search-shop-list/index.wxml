<navigator-bar
  title="{{isFocus ? focusTitle : normalTitle}}"
  class="navigator"
>
  <view class="page-container">
    <!-- 搜索框  -->
    <view class="search-container">
      <search
        style="flex: auto"
        bind:focus="searchFocus"
        bind:change="handleInput"
        bind:cancel="handleCancel"
        bind:search="handleSearch"
        keyword="{{keyword}}"
        input-style="background: #f9f9f9; border-radius: 18px; height: 36px;"
        placeholder="{{placeholder}}"
        align-left
        useCancel="{{ useCancel }}"
        cancelStyle="color: black"
        adjustPosition="{{ false }}"
      />
      <view bind:tap="toAddAddress" class="add-address" wx:if="{{addCreate && !isFocus}}">添加地址</view>
    </view>
    <retail-shop-search-histroy
      hidden="{{!keyword.length && isFocus}}"
      id="retail-shop-search-histroy"
      wx:if="{{ isLocalRetailCustom && isFocus && (showHistory || !keyword)}}"
      bind:onSelectKeyword="onSelectKeyword"
    />
    <!-- 定位 -->
    <view hidden="{{isFocus}}">
      <slot />
    </view>
    <!-- 店铺列表 -->
    <view class="shop-list-container">
      <local-retail-shop-list
        wx:if="{{ isLocalRetailCustom }}"
        isFocus="{{isFocus}}"
        shopLists="{{ shopLists }}"
        title="{{title}}"
        noMore="{{ noMore }}"
        hiddenTitle="{{ hiddenListTitle }}"
        hidden="{{ hiddenShopList }}"
        blank-content="{{blankContent}}"
        bind:choose="handleChooseItem"
        is-offline="{{ isOffline }}"
      />
      <shop-list
        wx:else
        shopLists="{{ shopLists }}"
        title="{{title}}"
        noMore="{{ noMore }}"
        hiddenTitle="{{ hiddenListTitle }}"
        hidden="{{ hiddenShopList }}"
        blank-content="{{blankContent}}"
        bind:choose="handleChooseItem"
        is-offline="{{ isOffline }}"
      />
    </view>
  </view>
</navigator-bar>
