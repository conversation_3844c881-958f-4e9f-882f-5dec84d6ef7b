.history-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 32rpx;

  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .title {
      font-size: 26rpx;
      color: #646566;
    }

    .delete-img {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .body {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .keyword {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 12rpx 16rpx;
      margin-right: 16rpx;
      margin-bottom: 16rpx;
      background-color: #f2f3f5;
      border-radius: 32rpx;
      flex-grow: 0;
      max-width: 100%;

      &-content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 28rpx;
        color: #323233;
      }
    }
  }
}
