const RETAIL_SHOP_SEARCH_KEY = 'RET<PERSON><PERSON>_SHOP_SEARCH_KEY';
Component({
  data: {
    keywords: [],
  },

  lifetimes: {
    attached() {
      const shopHistoryKey = wx.getStorageSync(RETAIL_SHOP_SEARCH_KEY) || [];
      this.setData({
        keywords: shopHistoryKey,
      });
    },
  },

  methods: {
    deleteAllKeywords() {
      wx.removeStorageSync(RETAIL_SHOP_SEARCH_KEY);
      this.setData({
        keywords: [],
      });
    },

    onSelectKeyword({
      currentTarget: {
        dataset: { keyword },
      },
    }) {
      this.triggerEvent('onSelectKeyword', { value: keyword });
    },

    saveKeyword(keyword) {
      const realKeyword = keyword?.trim() ?? '';
      if (!realKeyword) return;
      const { keywords } = this.data;
      if (~keywords.indexOf(realKeyword)) return;
      keywords.unshift(realKeyword);
      // 超出10条历史记录，则删除
      if (keywords.length > 10) {
        keywords.pop();
      }
      this.setData({
        keywords: [...keywords],
      });
      wx.setStorageSync(RETAIL_SHOP_SEARCH_KEY, keywords);
    },
  },
});
