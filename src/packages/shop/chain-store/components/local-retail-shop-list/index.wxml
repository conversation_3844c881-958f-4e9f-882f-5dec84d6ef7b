<view
  hidden="{{ hidden }}"
  class="container"
>
  <view hidden="{{isFocus || isHideAddress}}">
    <view class="shop-list-title">收货地址</view>
    <select-address
      bind:select-address="reFetchList"
      addressId="{{addressId}}"
    />
    <view class="shop-list-title">{{ '附近门店' }}</view>
  </view>
  <view
    wx:if="{{ localRetailShopList && localRetailShopList.length > 0 }}"
    class="shop-list-wrap"
  >
    <view
      class="shop-list-item {{(item.kdtId === currentKdtId && !isFocus) ? 'shop-item--selected' : ''}}"
      wx:for="{{ localRetailShopList }}"
      wx:key="name"
      bindtap="handleChooseShop"
      data-shop="{{ item }}"
    >
      <block wx:if="{{ item.kdtId === currentKdtId && !isFocus}}">
        <view class="shop-item--selected-bg" />
        <van-icon
          class="shop-item--selected-icon"
          name="success"
          color="#fff"
        />
      </block>
      <view class="item-content {{ item.disableClick ? 'disabled' : '' }}">
        <view class="item-head">
          <view class="item-head__container">
            <view class="name" i18n-off>{{ item.name }}</view>
            <block wx:if="{{!isFocus}}">
              <van-tag
                class="item-head__icon"
                wx:if="{{ item.isOpen }}"
                round
                type="success"
                color="#EDF4FF"
                text-color="#1989FA"
              >营业中</van-tag>
              <van-tag
                wx:else
                class="item-head__icon"
                round
                type="success"
                color="#F2F3F5"
                text-color="#323233"
              >休息中</van-tag>
              <van-tag
                wx:if="{{ !item.isSupportDelivery }}"
                class="item-head__icon"
                round
                type="success"
                color="rgba(238,10,36,0.1)"
                text-color="#EE0A24"
              >超出配送范围</van-tag>
            </block>
          </view>
          <view
            wx:if="{{ item.distance }}"
            class="distance"
          >{{ item.distance }}km</view>
        </view>
        <view class="item-desc">
          <van-icon
            name="location-o"
            class="icon-location"
          />
          {{ item.desc }}
        </view>
        <view
          class="item-footer"
          wx:if="{{!isFocus}}"
        >
          <view
            wx:if="{{item.summary}}"
            class="item-footer__time"
          >
            <van-icon
              name="underway-o"
              class="icon-time"
            />
            {{ item.summary }}
          </view>
          <view wx:else />
          <view
            wx:if="{{item.customerServicePhoneNumber}}"
            class="item-footer__contact"
            data-customer-service-phone-number="{{item.customerServicePhoneNumber}}"
            data-customer-service-area-code="{{item.customerServiceAreaCode}}"
            catch:tap="handleContact"
          >
            <van-icon
              name="phone-o"
              class="icon-phone"
            />
            <text>联系商家</text>
          </view>
        </view>
      </view>
    </view>
    <view
      wx:if="{{ noMore }}"
      class="no-more"
    >没有更多了...</view>
  </view>
  <view
    wx:else
    class="about-blank"
  >{{ blankContent || '暂无相关店铺' }}</view>
</view>

<phone-popup
  show="{{showPhonePopup}}"
  customer-service-phone-number="{{customerServicePhoneNumber}}"
  customer-service-area-code="{{customerServiceAreaCode}}"
  bind:onClosePopup="handleClosePhonePopup"
/>
