$itemBottomMargin: 8px;

.container {
  background-color: #f2f3f5;
  min-height: 100%;
  padding-top: 1px;

  .shop-list-title {
    display: flex;
    justify-content: space-between;
    margin: 24px 0 0 10px;
    font-size: 14px;
    color: #969799;

    .all {
      display: flex;
      align-items: center;

      .title-arrow {
        margin: 1px 16px 0 4px;
        font-size: 15px;
        color: #c8c9cc;
      }
    }
  }

  .shop-list-wrap {
    padding-bottom: 1px;
  }

  .shop-item {
    &--selected {
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 200%;
        height: 200%;
        border: 2px solid #ee0a24;
        transform-origin: left top;
        transform: scale(0.5);
        border-radius: 32rpx;
      }

      &-bg {
        position: absolute;
        right: -2rpx;
        top: 0;
        width: 0;
        height: 0;
        border-top-right-radius: 16rpx;
        border: 28rpx solid transparent;
        border-top: 28rpx solid #ee0a24;
        border-right: 28rpx solid #ee0a24;
      }
      &-icon {
        position: absolute;
        top: 4rpx;
        right: 4rpx;
        font-size: 24rpx;
      }
    }
  }

  .shop-list-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 12px;
    padding: 12px 12px;
    font-size: 14px;
    background-color: #fff;
    border-radius: 16rpx;

    .item-content {
      width: 100%;
      flex: 1 1 auto;
      overflow: hidden;

      &.disabled {
        .name,
        .item-desc,
        .item-footer__time {
          color: #c8c9cc !important;
        }
      }

      .item-head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        line-height: 20px;
        color: #323233;

        &__icon {
          position: relative;
          top: -2rpx;
          flex-shrink: 0;
          margin-right: 4rpx;
          margin-left: 4rpx;
        }

        &__container {
          display: flex;
          flex-direction: row;
          align-items: center;
          width: 100%;

          .name {
            font-size: 16px;
            line-height: 20px;
            color: #323233;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            margin-right: 4rpx;
          }
        }

        .distance {
          flex: 0 0 auto;
          font-size: 13px;
          line-height: 16px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          flex-shrink: 0;
        }
      }

      .item-desc {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        margin-top: $itemBottomMargin;
        font-size: 13px;
        color: #969799;
        line-height: 16px;

        .icon-location {
          margin-right: 4rpx;
          font-size: 28rpx;
          transform: translateY(2rpx);
        }
      }

      .item-footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-top: $itemBottomMargin;
        flex-wrap: wrap;
        color: #969799;

        &__time {
          display: flex;
          flex-direction: row;
          align-items: flex-start;
          margin-bottom: 16rpx;

          .icon-time {
            font-size: 28rpx;
            margin-right: 4rpx;
            transform: translateY(6rpx);
          }
        }

        &__contact {
          flex-shrink: 0;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 6rpx 16rpx;
          font-size: 24rpx;
          color: #323233;
          margin-left: auto;

          .icon-phone {
            font-size: 28rpx;
            margin-right: 4rpx;
          }
          &::before {
            content: '';
            position: absolute;
            box-sizing: border-box;
            top: 0;
            left: 0;
            width: 200%;
            height: 200%;
            border-radius: 48rpx;
            border: 2px solid #dcdee0;
            transform-origin: left top;
            transform: scale(0.5);
          }
        }
      }
    }
  }

  .about-blank {
    margin: 40px;
    text-align: center;
    font-size: 14px;
    color: #999;
  }
}
