import WscComponent from 'pages/common/wsc-component/index';
import { parseLocalRetaillData, getExtOpt } from '../../utils';
import getApp from 'shared/utils/get-safe-app';
import Event from '@youzan/weapp-utils/lib/event';

const app = getApp();
const GOODS_SHELF_IN_PAGE_STACK_KEY = 'GOODS_SHELF_IN_PAGE_STACK_KEY';
// 点单页
const GOODS_SHELF_ORDER_PAGE = 'packages/retail/goods-shelf/index';
// 首页
const GOODS_SHELF_INDEX_PACKAGES = 'packages/home/<USER>/index';
const GOODS_SHELF_INDEX_PAGE = 'pages/home/<USER>/index';

WscComponent({
  properties: {
    hidden: {
      type: Boolean,
      value: false,
    },
    hiddenTitle: {
      type: Boolean,
      value: false,
    },
    currentKdtId: Number,
    title: String,
    blankContent: String,
    shopLists: {
      type: Array,
      value: [],
      observer(shopLists) {
        const { deliveryMethod } = getExtOpt();
        this.setData({
          localRetailShopList: parseLocalRetaillData(shopLists, {
            deliveryMethod,
          }),
        });
      },
    },
    noMore: {
      type: Boolean,
      value: false,
    },
    isFocus: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    showPhonePopup: false,
    customerServicePhoneNumber: '',
    customerServiceAreaCode: '',
    isHideAddress: false,
  },

  ready() {
    const { addressId, isHideAddress } = getExtOpt();

    if (this.judgePageInPageStack(GOODS_SHELF_ORDER_PAGE)) {
      if (isHideAddress) {
        wx.setStorageSync(GOODS_SHELF_IN_PAGE_STACK_KEY, true);
      } else {
        wx.removeStorageSync(GOODS_SHELF_IN_PAGE_STACK_KEY);
      }
    } else if (
      this.judgePageInPageStack(GOODS_SHELF_INDEX_PAGE) ||
      this.judgePageInPageStack(GOODS_SHELF_INDEX_PACKAGES)
    ) {
      wx.removeStorageSync(GOODS_SHELF_IN_PAGE_STACK_KEY);
    }
    this.setYZData({
      currentKdtId: +app.getKdtId(),
      addressId,
      isHideAddress:
        isHideAddress || wx.getStorageSync(GOODS_SHELF_IN_PAGE_STACK_KEY),
    });
  },

  methods: {
    // 判断某个页是否在页面栈中
    judgePageInPageStack(path = '') {
      const pageArr = getCurrentPages ? getCurrentPages() : [];
      return pageArr.some((page) => page.route.indexOf(path) !== -1);
    },
    reFetchList({ detail = {} }) {
      const { lat, lon: lng, firstLoad = false } = detail;

      this.setYZData({
        currentKdtId: +app.getKdtId(),
        addressInfo: detail,
      });

      // addressId初始化传入时会触发一次地址选择事件，重复拉取店铺选择列表，需要忽略
      if (firstLoad) {
        return;
      }

      Event.trigger('shop-select:reFetchListWithLocation', { lat, lng });
    },

    handleChooseShop({ currentTarget }) {
      const { dataset = {} } = currentTarget;
      const { shop = {} } = dataset;
      const { disableClick } = dataset.shop;

      // 店铺处于休息时间或店铺超出配送范围时，禁止进入店铺
      if (disableClick) return;

      if (this.data.addressInfo) {
        const dbid = app.db.set(this.data.addressInfo);
        shop.extArgs = { dbid };
      }

      // 地理位置也带过去
      this.triggerEvent('choose', { shop });
    },

    // 联系商家
    handleContact({
      currentTarget: {
        dataset: { customerServiceAreaCode, customerServicePhoneNumber } = {},
      } = {},
    }) {
      this.setYZData({
        showPhonePopup: true,
        customerServiceAreaCode,
        customerServicePhoneNumber,
      });
    },

    handleClosePhonePopup() {
      this.setYZData({
        showPhonePopup: false,
      });
    },
  },
});
