<view hidden="{{ hidden }}" class="container">
  <view wx:if="{{ !hiddenTitle }}" class="shop-list-title">
    {{ title || '全部店铺' }}
    <view wx:if="{{ isShowAll }}" class="all" bindtap="goAllStore">
      <text>查看全部</text>
      <van-icon name="arrow" color="#969799" class="title-arrow" />
    </view>
  </view>
  <view wx:if="{{ shopLists && shopLists.length > 0 }}">
    <view class="shop-list-wrapper" wx:for="{{ shopLists }}" wx:key="name">
      <view class="shop-list-item" bindtap="handleChooseShop" data-shop="{{ item }}">
        <view class="item-content">
          <view class="item-head">
            <view class="name" i18n-off>{{ item.name }}</view>
            <view wx:if="{{ item.isCurrent }}" class="mark">最近访问</view>
          </view>
          <view class="item-desc">
            <text wx:if="{{ item.distance }}" class="num">{{ item.distance }} km</text>
            {{ item.desc }}
          </view>
        </view>
        <van-icon wx:if="{{ !(isOffline || item.offlineShopOpen) && !disableLink  }}" name="arrow" color="#000" class="icon-arrow" size="14" />
      </view>
    </view>
    <view wx:if="{{ noMore }}" class="no-more">没有更多了...</view>
  </view>
  <view wx:else class="about-blank">
    {{ blankContent || '暂无相关店铺' }}
  </view>
</view>
