.container {
  .shop-list-title {
    display: flex;
    justify-content: space-between;
    height: 20px;
    line-height: 30px;
    padding: 16px 16px 6px;
    padding-top: 16px;
    font-size: 14px;
    color: #666;
    position: relative;

    &:before {
      content: '';
      width: 100%;
      height: 10px;
      background: #f2f2f2;
      position: absolute;
      top: 0;
      left: 0;
    }

    .all {
      display: flex;
      align-items: center;

      .title-arrow {
        margin: 1px 16px 0 4px;
        font-size: 15px;
        color: #c8c9cc;
      }
    }
  }
  .shop-list-wrapper {
    &:last-child {
      .shop-list-item {
        border-bottom: none;
      }
    }
  }

  .shop-list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    font-size: 14px;
    box-sizing: border-box;
    width: 100vw;

    .item-content {
      width: 260px;
      flex: 1;

      .item-head {
        display: flex;
        align-items: center;
        font-size: 18px;
        line-height: 20px;
        color: #323233;

        .name {
          font-size: 18px;
          line-height: 20px;
          color: #323233;
          margin-right: 8px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .mark {
          min-width: 52px;
          line-height: 16px;
          text-align: center;
          padding: 0;
          color: #ee0a24;
          border-radius: 8px;
          font-size: 10px;
          background-color: #fbe8e8;
        }
      }
    }

    .item-desc {
      margin-top: 8px;
      font-size: 14px;
      color: #969799;
      line-height: 18px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      .num::after {
        content: ' | ';
        color: #ebedf0;
      }
    }
  }

  .about-blank {
    margin: 40px;
    text-align: center;
    font-size: 14px;
    color: #999999;
  }
}
