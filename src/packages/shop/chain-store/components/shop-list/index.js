import WscComponent from 'pages/common/wsc-component/index';
import { getCurrentPage } from 'shared/common/base/wsc-component';

WscComponent({
  properties: {
    hidden: {
      type: Boolean,
      value: false,
    },
    hiddenTitle: {
      type: Boolean,
      value: false,
    },
    isOnline: Boolean,
    isOffline: Boolean,
    isShowAll: Boolean,
    disableLink: Boolean,
    title: String,
    blankContent: String,
    umpType: String,
    shopLists: Array,
    noMore: {
      type: Boolean,
      value: false,
    },
  },

  ready() {
    const { options = {} } = getCurrentPage(this);
    this.setYZData({
      disableLink: !!options.isRedirectDisable,
    });
  },

  methods: {
    // 查看全部网店或门店
    goAllStore() {
      const { isOnline, isOffline } = this.data;
      this.triggerEvent('go-list', { isOnline, isOffline });
    },

    // TODO: 交给外部操作，不同的回调
    handleChooseShop({ currentTarget }) {
      const { disableLink, isOffline, umpType } = this.data;

      const { dataset = {} } = currentTarget;
      const { shop = {} } = dataset;
      const { offlineShopOpen, onlineShopOpen } = shop;

      // 搜索模式下无法用isOffline区分店铺类型，只能通过列表的offlineShopOpen、onlineShopOpen判断
      const isOfflineStore = isOffline || (offlineShopOpen && !onlineShopOpen);

      // 优惠券搜索门店弹Toast
      if (umpType === 'coupon' && isOfflineStore) {
        wx.showToast({
          title: '请前往该门店使用',
          icon: 'none',
          duration: 3000,
        });
        return;
      }
      // 如果是门店或禁用跳转，不做跳转
      if (isOfflineStore || disableLink) return;
      // 地理位置也带过去
      this.triggerEvent('choose', { shop });
    },
  },
});
