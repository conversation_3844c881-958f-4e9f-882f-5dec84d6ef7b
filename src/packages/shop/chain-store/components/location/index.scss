view {
  box-sizing: border-box;
}

@mixin verticalCenter {
  display: flex;
  align-items: center;
}

.location-wrapper {
  padding: 20px 15px;
  height: 66px;
  @include verticalCenter();
  justify-content: space-between;

  .information {
    @include verticalCenter();
    max-width: 240px;
    .text {
      font-size: 18px;
      font-weight: 500;
      color: #333333;
      margin: 0px 8px;
      text-overflow: ellipsis;
      overflow-x: hidden;
      white-space: nowrap;
      flex-grow: 0;
      max-width: 200px;
    }

    .icon {
      flex-grow: 0;
      background-image: url('https://img01.yzcdn.cn/wsc-minapp/icon/retail/location@2x 2.png');
      background-size: 12px 16px;
      width: 12px;
      height: 16px;
    }

    .edit {
      flex-grow: 0;
      background-image: url('https://img01.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
      background-size: 16px 16px;
      width: 16px;
      height: 16px;
    }
  }

  .relocation {
    @include verticalCenter();
    .icon {
      background-image: url('https://img01.yzcdn.cn/wsc-minapp/icon/retail/<EMAIL>');
      background-size: 20px 20px;
      width: 20px;
      height: 20px;
    }
    .text {
      margin-left: 8px;
      color: #3388ff;
      font-size: 12px;
    }

    &.disabled {
      opacity: 0.3;
    }
  }
}
