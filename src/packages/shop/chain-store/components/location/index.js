const defaultCopy = '重新定位';

Component({
  properties: {
    currentLocation: String,
    dbKey: String,
    redirectUrl: String,
    alias: String,
    goodsId: String,
    copy: {
      type: String,
      value: defaultCopy,
      observer(val) {
        this.setData({
          disabled: !!val && val !== defaultCopy
        });
      }
    }
  },
  methods: {
    reSetLocation() {
      if (this.data.disabled) return;
      this.triggerEvent('reSetLocation');
    }
  }
});
