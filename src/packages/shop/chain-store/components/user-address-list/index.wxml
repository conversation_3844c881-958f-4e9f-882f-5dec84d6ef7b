<view class="user-address">
  <view class="current">
    <view class="current-title">当前定位</view>
    <view class="current-address">
      <view class="current-name">
        <van-icon name="location-o" class="icon-location" size="20" />
        <text class="text ml4px">{{currentLocation || '华泰创业园'}}</text>
      </view>
      <view class="current-reset" bind:tap="onReset">
        <van-icon name="aim" size="16" />
        <text class="text ml4px">重新定位</text>
      </view>
    </view>
  </view>
  <view class="gap"></view>
  <view class="title">收货地址</view>
  <view
    class="item"
    wx:for="{{ userAddressList }}"
    wx:key="id"
    class="item"
    data-address="{{ item }}"
    data-index="{{ index }}"
    bind:tap="onSelect"
  >
    <view class="left">
      <view class="name"> {{ item.location || item.addressDetail }}</view>
      <view class="address">
        {{ item.province }}{{ item.city }}{{ item.county }}{{ item.addressDetail }}{{ item.houseNumber }}
      </view>
      <view class="info">
        <text>{{ item.userName }}</text>
        <text>{{ item.tel }}</text>
      </view>
    </view>
    <van-icon class="arrow-icon" name="arrow" color="#000" size="14"/>
  </view>
</view>