.current {
  padding: 0 16px;
  &-title {
    font-size: 14px;
    line-height: 20px;
    color: #999;
    padding: 6px 0;
  }

  &-name {
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    color: #323233;
    display: flex;
    align-items: center;
    flex-grow: 1;
  }

  &-reset {
    font-size: 14px;
    line-height: 20px;
    color: #333333;
    width: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  &-address {
    display: flex;
    align-items: center;
    height: 42px;
  }
}

.ml4px {
  margin-left: 4px;
}

.gap {
  width: 100%;
  height: 10px;
  background: #f2f2f2;
}

.title {
  font-size: 14px;
  line-height: 20px;
  color: #666;
  padding: 16px 16px 6px;
}

.item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
}

.left {
  margin-right: 24px;
  flex-grow: 1;
}

.name {
  font-weight: 500;
  font-size: 18px;
  color: #323233;
  margin-bottom: 8px;
}

.address,
.info {
  font-size: 14px;
  line-height: 17px;
  color: #666666;
}

.address {
  margin-bottom: 7px;
}
