import { getUserAddressList as getUserAddressListApi } from './api';
import common from '@/packages/shop/multi-store/common';

Component({
  properties: {
    themeColor: {
      type: String,
      default: '#ee0a24',
    },
    themeBgColor: String,
  },

  data: {
    userAddressList: [],
    selectedId: null,
    houseNumberRequired: false,
  },

  pageLifetimes: {
    show() {
      this.getUserAddressList();
    },
  },

  attached() {
    this.getUserAddressList();
  },

  methods: {
    getUserAddressList() {
      return getUserAddressListApi().then((userAddressList = []) => {
        this.setData({
          userAddressList,
        });
      });
    },
    onSelect({
      currentTarget: {
        dataset: { address },
      },
    }) {
      this.triggerEvent('select', {
        detailValue: {
          name: address.location || address.addressDetail || '',
          desc: address.addressDetail || '',
          lon: address.lon || '',
          lat: address.lat || '',
        },
      });
    },
    onReset() {
      const app = getApp();
      common.checkScope(
        'userLocation',
        // 用户已经授权，说明是接口调用失败
        () => {
          this.triggerEvent('select');
        },
        // 用户未授权，去授权
        () => {
          wx.showModal({
            content: `"${
              app.getShopInfoSync().base.shop_name
            }"要获取你的地理位置，是否允许？`,
            success: (e) => {
              if (e.cancel) return;
              wx.openSetting({
                success: ({ authSetting }) => {
                  this.triggerEvent('select');
                  authSetting['scope.userLocation'];
                },
              });
            },
          });
        }
      );
    },
  },
});
