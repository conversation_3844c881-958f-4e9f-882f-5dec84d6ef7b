import WscPage from 'pages/common/wsc-page/index';
import { getUrlFromQueryObject } from 'retail/util/base';

const app = getApp();

WscPage({
  onLoad(options) {
    // 埋点
    app.logger &&
      app.logger.log({
        et: 'custom',
        ei: 'shop_select_old',
        en: '店铺选择页面（旧）',
      });

    // 重定向到新的店铺选择页面
    wx.redirectTo({
      url: `/packages/shop-select/chain-store/shopselect/index?${getUrlFromQueryObject(
        options
      )}`,
    });
  },
});
