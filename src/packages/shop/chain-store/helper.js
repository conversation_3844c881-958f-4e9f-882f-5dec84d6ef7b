import { getGoodsAlias } from './api';
import { redirectPage } from './utils';
import { ExtraShopArgs } from './constant';
import args from '@youzan/weapp-utils/lib/args';

const app = getApp();
const storageKey = 'select-shop-info';

export function handleSelectItem(detail = {}, data = {}) {
  const { desc, kdtId, extArgs } = detail;

  const { goodsId } = data;
  let { redirectUrl } = data;

  // 允许传递额外参数至原页面
  if (extArgs) {
    redirectUrl = args.add(redirectUrl, extArgs);
    wx.setStorageSync(ExtraShopArgs, extArgs);
  }

  const currentKdtId = app.getKdtId();
  // 选择一个item时切换所选店铺（修改globalData和CURRENT_GLOBAL_SHOP）并跳到redirectUrl
  app
    .updateKdtId(kdtId, false, {
      mark: '921',
    })
    .then(() => {
      app.getShopInfo().then(() => {
        // 如果有goodId则调接口拿商品的alias
        if (goodsId) {
          getGoodsAlias({
            kdtId,
            goodsId,
          })
            .then((res) => {
              const { alias } = res;
              redirectUrl += `${
                redirectUrl.indexOf('?') != -1 ? '&' : '?'
              }alias=${alias}`;
              redirectPage(currentKdtId, kdtId, redirectUrl);
            })
            .catch((err) => {
              console.log(err);
            });
        } else {
          redirectPage(currentKdtId, kdtId, redirectUrl);
        }
      });
    });

  app.storage.set(storageKey, {
    poi: desc,
  });
}
