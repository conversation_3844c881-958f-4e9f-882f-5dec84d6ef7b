import {
  searchAddress,
  tryLocation,
  reverseGeocoder,
  gcjToBaidu,
} from '@/helpers/lbs';
import debounce from 'utils/debounce';
import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  onLoad(query) {
    app.setLangNavTitle('搜索地址');
    const { redirectUrl, alias, goodsId, dbKey, extOptJson = {} } = query;
    this.setData({
      redirectUrl,
      alias,
      goodsId,
      dbKey,
      extOptJson,
    });
  },

  onShow() {
    tryLocation(({ lng, lat }) => {
      reverseGeocoder({
        location: {
          latitude: lat,
          longitude: lng,
        },
        poi_options: 'policy=2',
      }).then((res) => {
        const { result: { formatted_addresses = {} } = {} } = res;
        formatted_addresses.recommend &&
          this.fetchAddress({ keyword: formatted_addresses.recommend });
      });
    });
  },

  handleFocus(even) {
    const { detail } = even;
    this.setData({
      isFocus: detail.isFocus,
    });
  },

  parseLocation(rawDataArr) {
    return rawDataArr.map((rawData) => {
      const { title: name, address: desc, location } = rawData;
      const { lng, lat } = gcjToBaidu(location.lng, location.lat);
      return {
        name,
        isCurrent: false,
        distance: null,
        desc,
        id: rawData.adcode,
        lon: lng,
        lat,
      };
    });
  },

  fetchAddress(data) {
    searchAddress(data).then((res) => {
      this.setData({
        shopLists: this.parseLocation(res.data),
      });
    });
  },

  handleInputChange: debounce(function (e) {
    // 调根据关键字搜索地址 的接口
    if (e.detail && e.detail.detailValue) {
      this.fetchAddress({ keyword: e.detail.detailValue });
    }
  }, 800),

  handleSelect(e) {
    const { detail: { detailValue = {} } = {} } = e;
    const { redirectUrl, alias, goodsId, dbKey, extOptJson } = this.data;
    // 选择一个item时转跳到选择店铺页面并带上所选item的相应信息
    const dbid = app.db.set({
      name: detailValue?.name || '',
      poi: detailValue?.desc || '',
      lng: detailValue?.lon || '',
      lat: detailValue?.lat || '',
    });
    // eslint-disable-next-line @youzan/dmc/wx-check
    wx.reLaunch({
      url: `/packages/shop-select/chain-store/shopselect/index?dbid=${dbid}&redirectUrl=${redirectUrl}&alias=${alias}&goodsId=${goodsId}&dbKey=${
        dbKey || 'location'
      }&extOptJson=${extOptJson}`,
    });
  },
});
