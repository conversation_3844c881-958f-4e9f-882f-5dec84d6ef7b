import WscPage from 'pages/common/wsc-page/index';
import { getShopList } from '../api';
import { parseData } from '../utils';
import { handleSelectItem } from '../helper';

const PAGE_SIZE = 20;

WscPage({
  data: {
    page: 1,
    isLock: false,
    isFinished: false,
    shopLists: []
  },

  onLoad(options = {}) {
    const { isOffline } = options;
    wx.setNavigationBarTitle({
      title: +isOffline ? '全部门店' : '全部网店'
    });
    this.setYZData({
      isOnfline: !+isOffline,
      isOffline: +isOffline
    });
  },

  onShow() {
    // 获取店铺列表
    this.fetchList();
  },

  // 下拉刷新
  onReachBottom() {
    const { isFinished } = this.data;
    if (isFinished) return;
    this.data.page += 1;
    this.fetchList();
  },

  fetchList() {
    const { lat, lon, umpAlias, umpType, isOffline } = this.__query__ || {};
    const { shopLists = [], isLock } = this.data;
    // 死锁。。。防止重复加载同一个页的数据
    if (isLock) {
      return;
    }
    this.data.isLock = true;

    return getShopList({
      lon,
      lat,
      umpAlias,
      umpType,
      mode: +isOffline ? 'offline' : 'online',
      page: this.data.page,
      pageSize: PAGE_SIZE
    })
      .then((res = {}) => {
        const list = parseData(res);
        list.length &&
          this.setYZData({
            shopLists: shopLists.concat(list)
          });
        if (res.items && res.items.length < PAGE_SIZE) {
          this.data.isFinished = true;
        }
        this.data.isLock = false;
      })
      .catch((e) => {
        throw e;
      });
  },

  handleChooseItem(e) {
    const { detail } = e;
    const { shop = {} } = detail;
    handleSelectItem(shop, this.data);
  }
});
