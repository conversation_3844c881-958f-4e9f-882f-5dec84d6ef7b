<van-toast id="van-toast" />

<web-view src="{{ noteData.linkUrl }}">
    
    <cover-view 
        wx:if="showCoverView" 
        class="buttom-btns-one shopnote-fixedicon"  
        bind:tap="thumbClick"
    >
        <cover-image
            src="{{ thumbsStatus ? 'https://img01.yzcdn.cn/upload_files/2021/05/17/FkvrfKb38t62FHd0r801biIWt6Mf.png' : 'https://img01.yzcdn.cn/upload_files/2021/05/17/FoLEFAbKlYf7pQJgxPSEnRIvNl9L.png' }}"
            class="shopnote-fixedicon-thumb"
        >
        </cover-image>
    </cover-view>
    <cover-view 
        wx:if="showCoverView" 
        class="buttom-btns-two shopnote-fixedicon"
    >
        <button class="button btn-class" open-type="share" hover-class="none">
        <cover-image
            src="https://img01.yzcdn.cn/upload_files/2021/05/17/FpgeFFbsOf4fPMZOyC6A51pabImH.png"
            class="shopnote-fixedicon-thumb"
        >
        </cover-image>
        </button>
    </cover-view>
    <cover-view 
        wx:if="{{ showCoverView && salesman.show }}" 
        class="buttom-btns-three shopnote-fixedicon"  
        bind:tap="salesmanClick"
    >
        <cover-image
            wx:if="{{ !!salesman.icon }}"
            src="{{ salesman.icon }}"
            class="shopnote-fixedicon-full"
        >
        </cover-image>
    </cover-view>
</web-view>
