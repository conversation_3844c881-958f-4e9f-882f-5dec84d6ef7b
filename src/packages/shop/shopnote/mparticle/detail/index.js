import WscPage from 'pages/common/wsc-page/index';
import Theme from '../../../showcase/index';
import Toast from '@vant/weapp/dist/toast/toast';
import get from '@youzan/weapp-utils/lib/get';
import args from '@youzan/weapp-utils/lib/args';
import { SalesmanCubeCore } from '@youzan/sales-icon-utils';
import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';
import navigate from 'shared/utils/navigate';
import adaptorComponents from 'constants/adaptor-components';
import { setThumbStatus } from './api';
import { SHOPNOTE_TPL, SHOP_LOCK_STATUS } from './constants';
import { getBannerId } from './components/common/logger';
import { init, bindSalesman } from '@youzan/salesman-share';
import { newRequest } from 'shared/utils/salesman-share';
import { getCurrentPage } from 'shared/common/base/wsc-component';

const app = getApp();

WscPage(Theme, {
  data: {
    themeClass: app.themeClass,
    noteData: {},
    noteGoodIds: [],
    thumbsStatus: 0,
    clickThumbFlag: true,
    noteType: SHOPNOTE_TPL,
    noteId: '',
    salesman: {},
    shopName: '',
    showCoverView: false,
  },
  fetchNoteData(currentAlias) {
    return new Promise((resolve) => {
      app.getShopInfo().then((data) => {
        const { chainStoreInfo, kdt_id: kdtId } = data;
        const { isRootShop = false } = chainStoreInfo;
        const currentKdtId = app.getKdtId();
        const queryData = {
          noteAlias: currentAlias,
          targetKdtId: currentKdtId || kdtId,
        };

        // 沿用原笔记详情的逻辑：如果是总店则监听进店事件
        if (isRootShop) {
          app.once('app:chainstore:kdtid:update', ({ kdtId }) => {
            this.getNoteDetailData({
              ...queryData,
              targetKdtId: kdtId,
              isChain: true,
            }).then((res) => {
              resolve(res);
            });
          });
        } else {
          this.getNoteDetailData(queryData).then((res) => {
            resolve(res);
          });
        }
      });
    });
  },

  getNoteDetailData({ noteAlias, sourceKdtId, targetKdtId, isChain }) {
    return app
      .request({
        path: '/wscshop/shopnote/detail.json',
        data: {
          noteAlias,
          sourceKdtId,
          targetKdtId,
          isChain,
          adaptorComponents: adaptorComponents.join(','),
        },
      })
      .then((data) => {
        if (data?.requestData?.length === 0) {
          Toast({
            message: '公众号文章已失效',
            duration: 1000,
          });
          setTimeout(() => {
            // eslint-disable-next-line @youzan/dmc/wx-check
            wx.switchTab({
              url: '/pages/home/<USER>/index',
            });
          }, 1000);
        } else {
          return data;
        }
      })
      .catch(() => {});
  },

  bindCustomer(sellerFrom, fromParams) {
    const { route, options } = getCurrentPage();
    const currentPage = args.add(route, options);
    return app
      .request({
        path: '/wscsalesman/bind-customer.json',
        method: 'POST',
        data: {
          sellerFrom,
          fromParams,
          sourceUrl: currentPage,
        },
      })
      .then((data) => data)
      .catch(() => {});
  },

  initNoteData(res) {
    if (res) {
      if (res.isLocked) {
        // eslint-disable-next-line @youzan/dmc/wx-check
        return wx.redirectTo({ url: '/packages/common/lock/index' });
      }
      if (res.noteData) {
        const {
          noteId,
          noteType,
          linkUrl,
          noteGoodIds,
          title,
          showBrowseCount,
          showThumbsUpCount,
          thumbsUpCount,
          browseCount,
          publishTime,
          showPublishTime,
          templateId,
          showEnterShop,
          noteItemsBriefInfo = [],
          description,
          featureComponents = [],
          headPhoto = '',
          coverPhotos = [],
          userThumbsStatus,
        } = res.noteData;

        this.trigger('mpArticleDetail:loaded', noteId);

        this.setYZData(
          {
            noteId,
            noteData: {
              linkUrl,
              title,
              showBrowseCount,
              showThumbsUp: showThumbsUpCount,
              thumbsUpCount: thumbsUpCount || 0,
              browseCount: browseCount || 0,
              publishTime,
              showPublishTime,
              templateId,
              showEnterShop,
              noteId,
              noteItemsBriefInfo,
              description,
              sharePhoto:
                headPhoto ||
                get(coverPhotos, '[0]') ||
                get(noteItemsBriefInfo, '[0].imageUrl'),
              featureComponents: (featureComponents || []).map((item) => {
                if (
                  item.type === 'rich_text' ||
                  item.type === 'rich_text_xss'
                ) {
                  item.externalStyle = 'padding: 0 15px';
                }

                return item;
              }),
            },
            noteGoodIds,
            thumbsStatus: userThumbsStatus || 0,
          },
          () => {
            setTimeout(() => {
              this.setYZData({
                showCoverView: true,
              });

              // 店铺笔记详情埋点
              app.logger.log({
                et: 'custom',
                ei: 'shopnote',
                en: '店铺笔记',
                params: {
                  note_id: noteId,
                  note_type: noteType,
                  note_model_id: templateId,
                  spm: `shopnote.${noteId}`,
                },
              });
            }, 1000);
          }
        );
      } else {
        this.setYZData({
          noteData: {
            linkUrl: 'https://h5.youzan.com/wscshop/feature/emptypage',
          },
        });
      }
    }
  },

  initSalesmanData({ kdtId, query } = {}) {
    const { sl } = query || {};
    const instance = new SalesmanCubeCore({
      kdtId,
      nativeAjax: app.request,
      nativeLogger: (...args) => app.logger.log(...args),
      query,
      fromSeller: sl,
    });

    // instance.bindRelation()

    try {
      instance.getShareData().then((data) => {
        this.setYZData({
          'salesman.show': data.share,
          'salesman.icon': data.iconUrl,
          'salesman.alias': data.seller,
          'salesman.name': data.salesmanName,
        });
      });
    } catch (error) {
      console.log({ error });
    }
  },

  onLoad(query) {
    app.getShopInfo().then((shopRes) => {
      const { shopMetaInfo = {}, kdtId } = shopRes || {};

      if (shopMetaInfo.lock_status === SHOP_LOCK_STATUS.LOCK) {
        // eslint-disable-next-line @youzan/dmc/wx-check
        return wx.redirectTo({ url: '/packages/common/lock/index' });
      }
      const { noteAlias, sl } = query || {};
      if (noteAlias) {
        this.initSalesmanData({
          kdtId,
          query,
        });
        this.setYZData({ sl });
        const url = args.add(
          'packages/shop/shopnote/mparticle/detail/index',
          query
        );

        init({
          url,
          request: newRequest,
        });
        const { route, options } = getCurrentPage();
        const currentPage = args.add(route, options);
        bindSalesman({ sst: 14, sourceUrl: currentPage });
        // sl && this.bindCustomer(sl, fromParams);
        this.fetchNoteData(noteAlias)
          .then((res) => {
            this.initNoteData(res);
          })
          .catch(() => {
            Toast('页面参数错误');
          });
      } else {
        Toast('页面参数错误');
      }
    });
  },

  onShow() {
    const { noteId = '' } = this.data;
    if (noteId) {
      this.trigger('mpArticleDetail:show', noteId);
    }
  },
  thumbClick() {
    if (this.data.clickThumbFlag === false) return;
    this.setYZData({
      clickThumbFlag: false,
    });
    const params = {
      thumbsStatus: this.data.thumbsStatus ? 0 : 1,
      shopNoteId: this.data.noteData.noteId,
    };
    setThumbStatus(params)
      .then((res) => {
        this.setYZData({
          clickThumbFlag: true,
        });
        if (res) {
          const curThumbsStatus = this.data.thumbsStatus ? 0 : 1;
          this.setYZData({
            thumbsStatus: curThumbsStatus,
          });
        }
      })
      .catch((err) => {
        console.log(err);
        this.setYZData({
          clickThumbFlag: true,
        });
      });
  },
  salesmanClick() {
    const { noteAlias } = this.__query__;
    const { alias, name } = this.data.salesman || {};
    const url = args.add('/packages/shop/shopnote/mparticle/share/index', {
      noteAlias,
      salesmanAlias: alias,
      salesmanName: name,
    });

    navigate.navigate({
      url,
    });
  },
  enterShop() {
    navigate.switchTab({ url: '/packages/home/<USER>/index' });
  },

  clickShareLog() {
    // banner_id
    const params = {
      banner_id: getBannerId('share'),
    };
    // 获取埋点格式
    const loggerMsg = getComponentLoggerParams('click_content', params);
    // 上报
    ensureAppLogger(loggerMsg);
  },

  getShareData() {
    const { noteAlias } = this.__query__;
    const { noteData = {}, salesman } = this.data;
    const { title, description } = noteData || {};
    const { show, alias } = salesman || {};
    const shopName = get(this.data, 'CURRENT_GLOBAL_SHOP.shop_name', '');
    const desc = description || `${shopName}的精彩内容分享给你，快来看一看吧`;
    let cover = get(noteData, 'sharePhoto');
    const shareUrl = `packages/shop/shopnote/mparticle/detail/index?noteAlias=${noteAlias}&sl=${
      show ? alias : ''
    }&is_share=1&shopAutoEnter=1`;
    const isHttp = (url) => /^(http|https):\/\//.test(url);
    cover = isHttp(cover) ? cover : `https:${cover}`;

    this.setYZData({
      shareUrl,
    });
    return {
      title,
      desc,
      path: shareUrl,
      imageUrl: cover,
    };
  },

  onShareAppMessage() {
    const shareData = this.getShareData();
    // 分享
    this.clickShareLog();

    return shareData;
  },

  onUnload() {
    this.off('mpArticleDetail:loaded');
    this.off('mpArticleDetail:show');
  },

  redirectToHome() {
    navigate.switchTab({ url: '/packages/home/<USER>/index' });
  },
});
