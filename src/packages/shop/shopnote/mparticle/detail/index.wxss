@import '/components/showcase/index.wxss';

.buttom-btns-one,
.buttom-btns-two,
.buttom-btns-three {
  z-index: 999999;
  position: fixed;
  box-sizing: border-box;
}

.buttom-btns-one,
.buttom-btns-two {
  padding: 9px;
  bottom: 15px;
  width: 38px;
  height: 38px;
}

.buttom-btns-one {
  margin-right: 20px;
  left: 15px;
}

.buttom-btns-two {
  left: 73px;
}

.shopnote-fixedicon {
  width: 38px;
  height: 38px;
  color: #fff;
  line-height: 38px;
  text-align: center;
  vertical-align: middle;
  background-color: rgba(0, 0, 0, 0.5);
  background-repeat: no-repeat;
  background-position: 50%;
  border-radius: 50%;
}

.shopnote-fixedicon-thumb {
  width: 20px;
  height: 20px;
}

.shopnote-fixedicon-full {
  width: 50px;
  height: 50px;
}

.buttom-btns-three {
  bottom: 150px;
  right: 15px;
  width: 50px;
  height: 50px;
  border-radius: 0;
  background-color: transparent;
}

.shopnote-fixedicon-thumb-active {
  background-image: url('https://img01.yzcdn.cn/upload_files/2021/05/17/FkvrfKb38t62FHd0r801biIWt6Mf.png');
}

.shopnote-fixedicon-thumb-no-active {
  background-image: url('https://img01.yzcdn.cn/upload_files/2021/05/17/FoLEFAbKlYf7pQJgxPSEnRIvNl9L.png');
}

button {
  position: static;
  height: 100%;
  display: block;
  margin-left: 0;
  margin-right: 0;
  padding-left: 0;
  padding-right: 0;
  box-sizing: border-box;
  font-size: inherit;
  text-align: inherit;
  text-decoration: inherit;
  line-height: inherit;
  border-radius: 0;
  -webkit-tap-highlight-color: transparent;
  overflow: inherit;
  color: inherit;
  user-select: none;
  background: transparent;
}
