.poster {
    position: absolute;
    top: 16px;
    bottom: 116px;
    right: 0;
    left: 0;
}
.poster-content {
    position: relative;
    height: 100%;
}

.panel {
    position: relative;
    width: 100%;
    height: 100px;
    box-sizing: border-box;
}

.icon-list-row {
    display: flex;
    padding: 16px 48px;
    overflow-x: auto;
    overflow-y: hidden;
    user-select: none;
    background: transparent;
    -webkit-overflow-scrolling: touch;
}

.icon-list-row__option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 0;
    width: 50%;
    user-select: none;
    background: transparent;
    -webkit-tap-highlight-color: transparent;
}
.icon-list-row__option:active {
    opacity: 0.7;
}

.icon-list-row__option:after {
    display: none;
}

.icon-list-row__icon {
    width: 48px;
    height: 48px;
    margin: 0 16px;
}

.icon-list-row__name {
    max-width: 72px;
    margin-top: 8px;
    padding: 0 4px;
    color: #323233;
    line-height: 20px;
    font-size: 14px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.van-transition {
    background-color: transparent !important;
}