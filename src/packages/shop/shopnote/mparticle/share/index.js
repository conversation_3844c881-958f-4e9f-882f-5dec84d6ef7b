import WscPage from 'pages/common/wsc-page/index';
import Theme from '../../../showcase/index';
import Toast from '@vant/weapp/dist/toast/toast';
import get from '@youzan/weapp-utils/lib/get';
import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';
import navigate from 'shared/utils/navigate';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import adaptorComponents from 'constants/adaptor-components';
import { SHOP_LOCK_STATUS } from '../detail/constants';
import { getBannerId } from '../detail/components/common/logger';
import { ICON_MAP } from './constant';

const app = getApp();

WscPage(Theme, {
  data: {
    themeClass: app.themeClass,
    noteData: {},
    noteId: '',
    salesmanAlias: '',
    salesmanName: '',
    noteAlias: '',
    iconList: ICON_MAP,
    posterSrc: '',
  },
  fetchNoteData(currentAlias) {
    return new Promise((resolve) => {
      app.getShopInfo().then((data) => {
        const { chainStoreInfo, kdt_id: kdtId } = data;
        const { isRootShop = false } = chainStoreInfo;
        const currentKdtId = app.getKdtId();
        const queryData = {
          noteAlias: currentAlias,
          targetKdtId: currentKdtId || kdtId,
        };

        // 沿用原笔记详情的逻辑：如果是总店则监听进店事件
        if (isRootShop) {
          app.once('app:chainstore:kdtid:update', ({ kdtId }) => {
            this.getNoteDetailData({
              ...queryData,
              targetKdtId: kdtId,
              isChain: true,
            }).then((res) => {
              resolve(res);
            });
          });
        } else {
          this.getNoteDetailData(queryData).then((res) => {
            resolve(res);
          });
        }
      });
    });
  },

  getNoteDetailData({ noteAlias, sourceKdtId, targetKdtId, isChain }) {
    return app
      .request({
        path: '/wscshop/shopnote/detail.json',
        data: {
          noteAlias,
          sourceKdtId,
          targetKdtId,
          isChain,
          adaptorComponents: adaptorComponents.join(','),
        },
      })
      .then((data) => data)
      .catch(() => {});
  },

  initNoteData(res) {
    if (res) {
      if (res.isLocked) {
        return wx.redirectTo({ url: '/packages/common/lock/index' });
      }
      if (res.noteData) {
        const {
          noteId,
          title,
          description,
          headPhoto = '',
          coverPhotos = [],
          noteItemsBriefInfo = [],
        } = res.noteData;

        this.setYZData({
          noteId,
          noteData: {
            title,
            noteId,
            description,
            sharePhoto:
              headPhoto ||
              get(coverPhotos, '[0]') ||
              get(noteItemsBriefInfo, '[0].imageUrl'),
          },
        });
      }
    }
  },

  onLoad(query) {
    app.getShopInfo().then((shopRes) => {
      const { shopMetaInfo = {} } = shopRes || {};
      if (shopMetaInfo.lock_status === SHOP_LOCK_STATUS.LOCK) {
        return wx.redirectTo({ url: '/packages/common/lock/index' });
      }
      const { noteAlias, salesmanAlias } = query || {};
      if (noteAlias) {
        this.fetchNoteData(noteAlias)
          .then((res) => {
            this.initNoteData(res);
            this.setYZData({ noteAlias: res, salesmanAlias });
          })
          .catch(() => {
            Toast('页面参数错误');
          });
      } else {
        Toast('页面参数错误');
      }
    });
  },

  enterShop() {
    navigate.switchTab({ url: '/packages/home/<USER>/index' });
  },

  handleSetSrc(e) {
    const { src } = e.detail;

    this.setYZData({
      posterSrc: src,
    });
  },

  handleClickIcon(e) {
    const { alias } = e.currentTarget.dataset;

    if (alias === 'tuwenPoster') {
      this.saveImage();
    }
  },

  // 点击保存海报
  saveImage() {
    const { posterSrc } = this.data;
    if (!posterSrc) return;

    wx.showLoading({ title: '保存中' });

    app.logger &&
      app.logger.log({
        et: 'click',
        ei: 'goods_savephoto',
        en: '点击海报保存按钮',
        si: '', // 店铺id，没有则置空
        params: {},
      });

    this.saveShareImage(posterSrc)
      .then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000,
        });
        this.triggerEvent('saved');
      })
      .catch(({ errMsg } = {}) => {
        wx.hideLoading();
        if (
          errMsg === 'saveImageToPhotosAlbum:fail auth deny' ||
          errMsg === 'saveImageToPhotosAlbum:fail authorize no response'
        ) {
          this.getSavePhotosRights();
          return;
        }
        wx.showToast({
          title: '保存失败',
          icon: 'none',
          duration: 2000,
        });
      });
  },

  // 保存图片 api 调用
  saveShareImage(tempFilePath) {
    return new Promise((resolve, reject) => {
      wx.saveImageToPhotosAlbum({
        filePath: tempFilePath,
        success: resolve,
        fail: reject,
      });
    });
  },

  getSavePhotosRights() {
    this.triggerEvent('open-setting');

    if (wx.openSetting) {
      wx.openSetting({
        success: ({ authSetting }) => {
          if (authSetting['scope.writePhotosAlbum']) {
            this.saveImage();
          }
        },
      });
    }
  },

  clickShareLog() {
    // banner_id
    const params = {
      banner_id: getBannerId('share'),
    };
    // 获取埋点格式
    const loggerMsg = getComponentLoggerParams('click_content', params);
    // 上报
    ensureAppLogger(loggerMsg);
  },

  formatObjToQuery(obj) {
    const keys = Object.keys(obj);

    return keys.reduce((query, key) => {
      query += `&${key}=${obj[key] || ''}`;

      return query;
    }, '');
  },

  getShareData() {
    const { noteAlias } = this.__query__;
    const { title, description } = this.data.noteData || {};
    const { salesmanAlias: sl } = this.data;
    const shopName = get(this.data, 'CURRENT_GLOBAL_SHOP.shop_name', '');
    const desc = description || `${shopName}的精彩内容分享给你，快来看一看吧`;
    const data = this.data.noteData;
    let cover = get(data, 'sharePhoto');
    const querySuffix = this.formatObjToQuery(getSalesmanParamsObject({ sl }));
    const shareUrl = `packages/shop/shopnote/mparticle/detail/index?noteAlias=${noteAlias}&${querySuffix}`;
    const isHttp = (url) => /^(http|https):\/\//.test(url);

    cover = isHttp(cover) ? cover : `https:${cover}`;

    return {
      title,
      desc,
      path: shareUrl,
      imageUrl: cover,
    };
  },

  onShareAppMessage() {
    const shareData = this.getShareData();
    // 分享
    this.clickShareLog();

    return shareData;
  },

  onUnload() {},

  redirectToHome() {
    navigate.switchTab({ url: '/packages/home/<USER>/index' });
  },
});
