<view class="container">
  <van-popup
    show="{{ true }}"
    position="bottom"
    z-index="{{ 9 }}"
    custom-style="border-radius: 0; bottom: 0;"
  >
    <view class="panel">
      <view
        class="icon-list-row"
      >
        <view
          name="icon-option"
          wx:for="{{ iconList }}"
          wx:for-item="item"
          wx:key="index"
          class="icon-list-row__option"
        >
          <button
            class="icon-list-row__option"
            open-type="{{ item.openType }}"
            data-config="{{ item }}"
            data-alias="{{ item.alias }}"
            bindtap="handleClickIcon"
          >
            <image src="{{ item.iconUrl }}" class="icon-list-row__icon" />
            <text class="icon-list-row__name">{{ item.title }}</text>
          </button>
        </view>
      </view>
    </view>
  </van-popup>

  <view class="poster">
    <view class="poster-content">
      <share-goods
        id="share-goods"
        wx:if="{{noteData.noteId}}"
        goodsImage="{{noteData.sharePhoto}}"
        salesmanAlias="{{salesmanAlias}}"
        shopName="{{CURRENT_GLOBAL_SHOP.shop_name}}"
        title="{{noteData.title}}"
        desc="{{noteData.description}}"
        bind:set-src="handleSetSrc"
      ></share-goods>
    </view>
  </view>
</view>