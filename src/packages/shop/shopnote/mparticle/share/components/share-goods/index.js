import WscComponent from 'shared/common/base/wsc-component/index';
import getApp from 'shared/utils/get-safe-app';
import get from '@youzan/weapp-utils/lib/get';
import { toHttps } from 'shared/utils/url';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import { fetchUltraCode } from 'shared/utils/qrcode';
import { drawOneLineText, drawTwoLineText, getPosterInfo } from './utils'

const app = getApp();
const { windowHeight: WINDOW_HEIGHT } = wx.getSystemInfoSync()
const IMAGE_DEFAULT_WIDTH = 292

WscComponent({
  properties: {
    goodsImage: {
      type: String,
      value: ''
    },
    salesmanAlias: String,
    shopName: String,
    path: String,
    title: String,
    desc: String
  },
  data: {
    canvasId: 'drawerImage',
    // 控制 canvas 是否展示
    showCanvas: false,
    // canvas 绘制的分享图临时路径
    src: '',
    img: '',
    // 小程序码图片路径
    weappCode: '',
    posterStyle: {
      width: `${IMAGE_DEFAULT_WIDTH}px`,
      height: '320px',
      top: '16px',
      left: 0
    }
  },
  attached() {
  },
  methods: {
    /**
     * @deprecated 临时方法
     * @description 直接生成销售员海报
     */
    drawSalemanCard() {
      wx.showLoading({ title: '正在生成' });
      this.setYZData(
        {
          showCanvas: true
        },
        this.draw.bind(this)
      );
    },

    handlePoster(e = {}) {
      const { openType } = e.detail || {}

      if (openType === 'share') return;
      wx.showLoading({ title: '正在生成' });
      this.setYZData(
        {
          showCanvas: true
        },
        this.draw.bind(this)
      );
    },

    draw() {
      this.loadGoodsImage()
        .then(this.drawQrCode.bind(this))
        .then(this.createTempPath.bind(this))
        .then((src) => {
          this.setYZData(
            {
              src
            },
            () => {
              this.triggerEvent('success');

              // 同步海报保存路径
              this.triggerEvent('set-src', {
                src,
              });
            }
          );
        })
        .catch((e) => {
          let errMsg = '';

          if (typeof e === 'object') {
            try {
              errMsg = JSON.stringify(e);
            } catch (err) {
              errMsg = String(e);
            }
          } else {
            errMsg = String(e);
          }

          wx.showToast({
            title: errMsg || '生成图片路径失败',
            icon: 'none'
          });

          app.getUserInfo((res) => {
            app.logger && app.logger.appError({
              name: 'draw_goods_poster_error',
              message: '绘制海报失败',
              detail: {
                errMsg,
                userName: get(res, 'userInfo.nickName')
              }
            });
          });
          this.triggerEvent('failed', { err: e });
        })
        .then(() => {
          this.setYZData({ showCanvas: false });
          this.triggerEvent('finished');
        });
    },

    drawQrCode() {
      const { weappCode, salesmanAlias: sl } = this.data
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      if (weappCode) {
        return Promise.resolve();
      }

      if (!pages.length) {
        return Promise.reject();
      }

      const { noteAlias } = currentPage.options;
      let query = {
        noteAlias
      }

      if (sl) {
        query = {
          ...query,
          ...getSalesmanParamsObject({ sl })
        };
      }

      return fetchUltraCode('packages/shop/shopnote/mparticle/detail/index', query).then((img) => {
        this.setYZData({
          weappCode: img
        });

        return this.drawShareImage(img);
      });
    },

    // 生成分享图临时路径
    createTempPath() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          const { canvasId } = this.data

          wx.canvasToTempFilePath(
            {
              canvasId,
              success: (res) => {
                resolve(res.tempFilePath);
              },
              fail: reject,
              complete: () => {
                wx.hideLoading();

                this.setYZData({
                  show: false
                });
              }
            },
            this
          );
        }, 100);
      });
    },

    loadGoodsImage() {
      if (this.data.img) return Promise.resolve();

      if (!this.data.goodsImage) return Promise.reject();

      return new Promise((resolve, reject) => {
        const url = cdnImage(this.data.goodsImage, '!730x0.jpg');
        const imageUrl = toHttps(url);

        if (!imageUrl) {
          return wx.showToast({
            title: '生成卡片失败:',
            icon: 'none',
            duration: 2000
          });
        }

        loadImage(imageUrl).then((img) => {
          this.setYZData({ img }, resolve);
        }).catch((e) => {
          e && wx.showToast({
            title: '生成卡片失败:',
            icon: 'none',
            duration: 2000
          });
          reject(e);
        });
      });
    },


    handleLoad(e) {
      const { width, height } = e.detail

      this.setYZData({
        imageInfo: {
          width,
          height
        }
      }, () => {
        this.drawSalemanCard()
      })
    },

    getGoodImageInfo() {
      const { imageInfo: { width = 0, height = 0 } = {} } = this.data
      const imageRatio = Number((width / height).toFixed(2))
      // 宽高比若不在2.35：1至1：1之间，按照1：1进行裁切（图片过高的情况）
      if (imageRatio < 1 || imageRatio > 2.35) {
        return {
          w: IMAGE_DEFAULT_WIDTH,
          h: IMAGE_DEFAULT_WIDTH
        }
      }

      // 宽高比若在2.35：1至1：1之间，保持原比例
      return {
        w: IMAGE_DEFAULT_WIDTH,
        h: IMAGE_DEFAULT_WIDTH / imageRatio
      }
    },

    // 预绘制和实际输出分享图绘制（ 通过有无 qrCodeUrl 判断 ）
    async drawShareImage(qrCodeUrl) {
      const { img, desc, title, canvasId, shopName } = this.data;
      const {
        imgLRPadding,
        posterWidth,
        coverImgHeight,
        textStartX,
        titleWidth,
        titleFontSize,
        titleLineHeight,
        titleStartY,
        descShadowStartY,
        descShadowBorder,
        descWidth,
        descFontSize,
        descLineHeight,
        descStartY,
        descHeight,
        qrCodeWH,
        qrCodeStartY,
        qrCodeFontSize,
        qrCodeFirstTextStartY,
        shareText,
        qrCodeFirstTextShopNameWidth,
        qrCodeSecondTextStartY,
        posterHeight
      } = getPosterInfo({
        imageInfo: this.getGoodImageInfo(),
        desc,
        title
      })
      const bottomHeight = 100
      const imgPadding = 16
      const baseLRPadding = 8

      const maxPosterHeight = WINDOW_HEIGHT - bottomHeight - imgPadding * 2
      const posterRatio = Number(maxPosterHeight / posterHeight).toFixed(2)

      const width = posterHeight > maxPosterHeight ? posterWidth * posterRatio : posterWidth
      const height = posterHeight > maxPosterHeight ? maxPosterHeight : posterHeight

      this.setYZData({
        posterHeight,
        posterStyle: {
          width: `${width}px`,
          height: `${height}px`,
          top: `${(maxPosterHeight - height) / 2}px`,
          left: `${(IMAGE_DEFAULT_WIDTH - width) / 2}px`
        }
      }, () => {
        const ctx = wx.createCanvasContext(this.data.canvasId, this);

        const getHeight = (height) => {
          if (!desc) {
            return height - descHeight;
          }

          return height;
        };

        ctx.setFillStyle('white');
        ctx.fillRect(0, 0, posterWidth, posterHeight);
        ctx.drawImage(img, 0, 0, posterWidth, coverImgHeight, posterWidth, coverImgHeight);
        // 最多两行的标题……
        drawTwoLineText(title, '#323233', titleFontSize, ctx, textStartX + 1, titleStartY + (titleLineHeight - titleFontSize) / 2, titleLineHeight, titleWidth);

        if (!!desc) {
          ctx.setFillStyle('#F7F8FA');
          ctx.lineJoin = "round"
          ctx.lineWidth = descShadowBorder
          ctx.strokeStyle = '#F7F8FA'
          ctx.strokeRect(textStartX + 1, descShadowStartY, titleWidth, descHeight)
          ctx.fillRect(textStartX + 1, descShadowStartY, titleWidth, descHeight);
          drawTwoLineText(desc, '#969799', descFontSize, ctx, textStartX + baseLRPadding + 1, descStartY + (descLineHeight - descFontSize) / 2, descLineHeight, descWidth);
        }
        // 小程序码
        !!qrCodeUrl && ctx.drawImage(qrCodeUrl, textStartX + 1, getHeight(qrCodeStartY), qrCodeWH, qrCodeWH);
        ctx.setFontSize(qrCodeFontSize);
        ctx.setFillStyle('#969799');
        ctx.fillText(shareText, textStartX + qrCodeWH + imgLRPadding + 1, getHeight(qrCodeFirstTextStartY));
        ctx.setFillStyle('#323233');
        // 店铺名称
        drawOneLineText(shopName, '#323233', qrCodeFontSize, ctx, textStartX + qrCodeWH + imgLRPadding + 1 + shareText.length * qrCodeFontSize + 2, getHeight(qrCodeFirstTextStartY), qrCodeFirstTextShopNameWidth)
        ctx.setFillStyle('#969799');
        ctx.fillText('长按识别小程序码阅读全文', textStartX + qrCodeWH + imgLRPadding + 1, getHeight(qrCodeSecondTextStartY));
        ctx.draw();

        this.triggerEvent(!!qrCodeUrl ? 'created' : 'inited', { canvasId });
      })
    },
  }
});

// 下载图片获得临时路径
function loadImage(src) {
  const app = getApp();

  return new Promise((resolve, reject) => {
    app.downloadFile({
      url: src,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (e) => {
        reject(e);
      }
    });
  });
}
