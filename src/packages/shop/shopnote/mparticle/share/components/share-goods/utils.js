const getCharLenInfo = ({ width, fontSize, text = '' } = {}) => {
    const charLen = Math.floor(width / fontSize);

    return {
        charLen,
        isMore: text.length > charLen,
        isLess: text.length < charLen
    }
}

// 获取最多一行文本
export const drawOneLineText = (text, color, fontSize, ctx, x, y, contentWidth) => {
    let title = text;
    
    const { charLen, isLess } = getCharLenInfo({ width: contentWidth, fontSize, text: title })
    title = isLess ? title : title.slice(0, charLen - 1) + '...'
    
    ctx.setFontSize(fontSize);
    ctx.setFillStyle(color);
    ctx.fillText(title, x, y);
}

// 获取最多两行文本
export const drawTwoLineText = (text, color, fontSize, ctx, x, y, lineHeight, contentWidth) => {
    let title = text;
    let titleMore = '';
    const { charLen, isMore, isLess } = getCharLenInfo({ width: contentWidth, fontSize, text: title })
    // 一行大约 12 个汉字需要换行
    if (isMore) {
        titleMore = title.slice(charLen);
        title = title.slice(0, charLen);
    }
    if (!isLess) {
        titleMore = titleMore.slice(0, charLen - 1) + '...';
    }
    ctx.setFontSize(fontSize);
    ctx.setFillStyle(color);
    ctx.fillText(title, x, y);
    ctx.fillText(titleMore, x, y + lineHeight);
}

export const getPosterInfo = ({ imageInfo, desc, title } = {}) => {
    const { w: posterWidth, h: coverImgHeight } = imageInfo
    const imgLRPadding = 16
    const imgTBPadding = 16

    const titleWidth = posterWidth - imgLRPadding * 2
    const titleFontSize = 20
    const titleLineHeight = 28
    const { isLess: titleOneline } = getCharLenInfo({ width: titleWidth, fontSize: titleFontSize, text: title })
    const titleHeight = titleLineHeight * (titleOneline ? 1 : 2)
    const titleStartY = coverImgHeight + imgTBPadding * 2
    const descPadding = 8
    const descShadowBorder = 4
    const descLineHeight = 18
    const descFontSize = 12
    const descShadowStartY = titleStartY + titleHeight - imgTBPadding + descPadding
    const descStartY = descShadowStartY + imgTBPadding + descPadding / 2
    const descWidth = titleWidth - descPadding * 2
    const { isLess: descOneline } = getCharLenInfo({ width: descWidth, fontSize: descFontSize, text: desc })
    const qrCodeWH = 80
    const qrCodeTextLineHeight = 18
    const qrCodeFontSize = 12
    const descHeight = !!desc ? descLineHeight * (descOneline ? 1 : 2) + descPadding * 2 : 0
    const qrCodeStartY = descShadowStartY + descHeight + (!!desc ? imgTBPadding : descPadding)
    const qrCodeTextGap = 4
    const shareText = '分享自'
    const qrCodeFirstTextShopNameWidth = titleWidth - qrCodeWH - imgLRPadding - shareText.length * qrCodeFontSize
    const qrCodeFirstTextStartY = imgTBPadding + qrCodeStartY + (qrCodeWH - qrCodeTextLineHeight * 2 - qrCodeTextGap) / 2 - (qrCodeTextLineHeight - descFontSize) / 2
    const qrCodeSecondTextStartY = qrCodeFirstTextStartY + + qrCodeFontSize + (qrCodeTextLineHeight - qrCodeFontSize) + qrCodeTextGap

    return {
        imgLRPadding,
        posterWidth,
        coverImgHeight,
        textStartX: 16,
        titleWidth,
        titleFontSize,
        titleLineHeight,
        titleStartY,
        descShadowStartY,
        descStartY,
        descWidth,
        descShadowBorder,
        descFontSize,
        descLineHeight,
        descHeight,
        qrCodeWH,
        qrCodeStartY,
        qrCodeFontSize,
        qrCodeTextLineHeight,
        shareText,
        qrCodeFirstTextShopNameWidth,
        qrCodeFirstTextStartY,
        qrCodeSecondTextStartY,
        posterHeight: qrCodeStartY + qrCodeWH + 16
    }

}