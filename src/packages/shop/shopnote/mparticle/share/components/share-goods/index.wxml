<canvas style="width: 292px; height: {{ posterHeight }}px; display: {{ showCanvas ? 'block' : 'none' }}" class="share-canvas" canvas-id="{{ canvasId }}" />

<view 
  class="share-image__container"
>
<view 
  class="share-image__content"
>
    <image
      src="{{ src }}"
      class="share-image--preview"
      style="width: {{ posterStyle.width }}; height: {{ posterStyle.height }}; top: {{ posterStyle.top }}; left: {{ posterStyle.left }}"
    />
    <image 
      bindload="handleLoad" 
      mode='widthFix' 
      src="{{ goodsImage || mockGoodsImage.equal }}" 
      style="display: none"
    />
</view>
</view>

