import WscPage from 'pages/common/wsc-page/index';
import get from '@youzan/weapp-utils/lib/get';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';
import { getSalesmanAccountInfo } from 'components/showcase/components/salesman-cube/api/salesman-account';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';
import args from '@youzan/utils/url/args';
import ZanWeappLogger from '@youzan/zan-weapp-logger';

const app = getApp();
WscPage({
  data: {
    src: '',
    sl: '',
  },

  onLoad(options) {
    const { navIndex = '', sl } = options;
    let src = 'https://h5.youzan.com/wscshop/shopnote/list';
    app.isSwitchTab().then((isTab) => {
      if (isTab) {
        src = `${src}?newTabbarColor=1&customNav=1&navIndex=${navIndex}${
          app.deviceType === 'iPhone-X' ? '&isNewIphone=1' : ''
        }`;
      }

      if (sl) {
        src = addSalesmanParams({ url: src, sl });
      }
      this.setYZData({
        src,
      });
    });

    // 获取销售员信息
    getSalesmanAccountInfo().then((data) => {
      this.sl = data.sls;
    });
  },

  onError(event) {
    ZanWeappLogger.log({
      appName: 'wsc-h5-decorate',
      logIndex: 'wsc_decorate_log',
      level: 'info',
      name: '[weapp]店铺笔记列表页webview加载失败',
      extra: {
        ...event?.detail,
        appId: app.getAppId(),
        kdtId: app.getKdtId(),
        buyerId: app.getBuyerId(),
        appVersion: app.getVersion(),
      },
    });
  },

  onShareAppMessage() {
    const shopName = get(this.data, 'CURRENT_GLOBAL_SHOP.shop_name', '');
    const title = shopName ? `${shopName}-品牌内容推荐给你` : '品牌故事分享';
    const desc = shopName ? `${shopName}-品牌精彩内容分享给你` : '品牌故事分享';
    let shareUrl = 'pages/shop/shopnote/list/index';

    // 兼容老代码，理解这里只是区分 ,不是通过销售员分享进入笔记列表，分享内容都打上shopAutoEnter的标识
    const { sl } = this.__query__;
    if (!sl) {
      shareUrl = args.add(shareUrl, defaultEnterShopOptions);
    }

    // 当前用户是销售员的情况分享需要带上销售员先关标识
    if (this.sl) {
      shareUrl = addSalesmanParams({ url: shareUrl, sl: this.sl });
    }

    return {
      title,
      path: shareUrl,
      desc,
    };
  },
});
