import { getGoodsList } from '../api';

export function injectProcesses(sdk, ctx) {
  sdk.setPageProcess('getGoodsList', (params) => {
    return getGoodsList(params);
  });

  sdk.setPageProcess('resetShareData', (yunShareData) => {
    getApp().logger.skynetLog({
      appName: 'wsc',
      logIndex: 'shop_note_detail',
      message:
        'shop note detail yun share data' + JSON.stringify(yunShareData || {}),
    });
    ctx.setData({ yunShareData });
  });
}
