<view>
  <view class="nav-container" wx:if="{{ showNav }}">
    <view class="nav-enter-shop" bind:tap="redirectToHome">
      <image src="{{ shop.logo }}" />
    </view>
    <view class="nav-enter-name">{{ shop.shopName }}</view>
    <view class="nav-enter-right">
      <form-view type="default">
        <view class="nav-sub-btn {{ subscribeStatus ? 'nav-enter-right__gray' : '' }}" wx:if="{{ showSubBtn }}" bind:tap="handleSubscribe">
          {{ subscribeStatus ? '已订阅' : '订阅' }}
        </view>
      </form-view>
    </view>
    <van-dialog id="van-dialog" />
    <van-dialog use-slot show="{{ guideShow }}" showConfirmButton="{{ false }}" custom-style="{{ dialogCustomStyle }}">
      <image src="https://b.yzcdn.cn/public_files/2019/08/01/df3bfea69c6c333e48d7e4bf26d9ef1f.png" />
      <button class="nav-guide-confirm__btn" bind:tap="closeGuideDialog">我知道了</button>
    </van-dialog>
  </view>
</view>