<view>
  <shopnote-new-arrival wx:if="{{ noteData.templateId === noteType.NEW_ARRIVAL }}" note-data="{{ noteData }}" note-good-ids="{{ noteGoodIds }}" thumbs-status="{{ thumbsStatus }}" salesman="{{ salesman }}" bind:clickcart="showcaseHandleGoodsBuy" bind:thumbclick="thumbClick" bind:shareclick="shareClick"></shopnote-new-arrival>
  <shopnote-keeper-talk wx:if="{{ noteData.templateId === noteType.KEEPER_TALK }}" note-data="{{ noteData }}" thumbs-status="{{ thumbsStatus }}" salesman="{{ salesman }}" featureComponents="{{featureComponents}}" bind:thumbclick="thumbClick" bind:shareclick="shareClick"></shopnote-keeper-talk>
  <shopnote-shop-circle wx:if="{{ noteData.templateId === noteType.SHOP_CIRCLE }}" note-data="{{ noteData }}" thumbs-status="{{ thumbsStatus }}" note-good-ids="{{ noteGoodIds }}" salesman="{{ salesman }}" bind:thumbclick="thumbClick" bind:shareclick="shareClick" bind:clickcart="showcaseHandleGoodsBuy"></shopnote-shop-circle>
  <shopnote-single-production wx:if="{{ noteData.templateId === noteType.SINGLE_PRODUCTION }}" note-data="{{ noteData }}" salesman="{{ salesman }}" thumbs-status="{{ thumbsStatus }}" note-good-ids="{{ noteGoodIds }}" bind:thumbclick="thumbClick" bind:shareclick="shareClick"></shopnote-single-production>
</view>