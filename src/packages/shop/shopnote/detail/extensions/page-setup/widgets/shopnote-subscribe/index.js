import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapData } from '@youzan/ranta-helper-tee';
import navigate from 'shared/utils/navigate';
import { editShopNoteSubscription } from '../../api';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';

RantaWidget({
  data: {
    clickThumbFlag: true,
  },
  attached() {
    mapData(this, [
      'showNav',
      'showSubBtn',
      'dialogCustomStyle',
      'subscribeStatus',
      'guideShow',
      'shop',
    ]);
  },
  ready() { },
  methods: {
    redirectToHome() {
      navigate.switchTab({ url: '/packages/home/<USER>/index' });
    },
    handleSubscribe() {
      const { subscribeStatus } = this.data;
      const flag = wx.getStorageSync('hasEnteredPage');
      if (subscribeStatus) {
        const that = this;
        wx.showModal({
          title: '取消订阅将无法及时了解到商家的资讯信息和优惠活动',
          confirmText: '继续订阅',
          cancelText: '取消订阅',
          success() {
            that.editShopNoteSubscription();
          },
        });
        // Dialog.confirm({
        //   title: '取消订阅将无法及时了解到商家的资讯信息和优惠活动',
        //   confirmButtonText: '取消订阅',
        //   cancelButtonText: '继续订阅',
        // }).then(this.editShopNoteSubscription);
      } else {
        // 第一次进入页面订阅 弹出引导框
        if (!flag && !subscribeStatus) {
          this.setYZData({
            guideShow: true,
          });
        }
        this.editShopNoteSubscription();
      }
    },
    editShopNoteSubscription() {
      const { subscribeStatus } = this.data;
      const flag = wx.getStorageSync('hasEnteredPage');
      editShopNoteSubscription({
        channel: 3,
        status: subscribeStatus ? 0 : 1,
      }).then((res) => {
        if (res.value) {
          if (!subscribeStatus && !flag) {
            wx.setStorageSync('hasEnteredPage', true);
          } else {
            wx.showToast({
              title: `${subscribeStatus ? '取消订阅成功' : '订阅成功'}`,
              icon: 'none',
            });
          }
          this.setYZData({
            subscribeStatus: Number(!subscribeStatus),
          });
        }
      });
    },
    closeGuideDialog() {
      this.setYZData({
        guideShow: false,
      });
    },
  },
});
