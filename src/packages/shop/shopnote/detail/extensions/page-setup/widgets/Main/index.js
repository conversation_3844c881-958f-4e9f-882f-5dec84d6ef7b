import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapData } from '@youzan/ranta-helper-tee';
import { setThumbStatus } from '../../api';
import navigate from 'shared/utils/navigate';

RantaWidget({
  data: {
    clickThumbFlag: true,
  },
  attached() {
    mapData(this, [
      'noteData',
      'noteId',
      'noteType',
      'design',
      'noteGoodIds',
      'thumbsStatus',
      'salesman',
      'shop',
    ]);
  },
  ready() { },
  methods: {
    thumbClick() {
      if (this.data.clickThumbFlag === false) return;
      this.setYZData({
        clickThumbFlag: false,
      });
      const params = {
        thumbsStatus: this.data.thumbsStatus ? 0 : 1,
        shopNoteId: this.data.noteId,
      };
      setThumbStatus(params)
        .then((res) => {
          this.setYZData({
            clickThumbFlag: true,
          });
          if (res) {
            const curThumbsStatus = this.data.thumbsStatus ? 0 : 1;

            this.setYZData({
              thumbsStatus: curThumbsStatus,
            });
          }
        })
        .catch((err) => {
          console.log(err);
          this.setYZData({
            clickThumbFlag: true,
          });
        });
    },
    showcaseHandleGoodsBuy(e) {
      navigate.navigate({
        url: e.detail.url,
      });
    },
    onSalesmanShareCard() {
      try {
        this.selectComponent('#share-goods').drawSalemanCard();
      } catch (e) {
        console.log(e.message);
      }
    },
  },
});
