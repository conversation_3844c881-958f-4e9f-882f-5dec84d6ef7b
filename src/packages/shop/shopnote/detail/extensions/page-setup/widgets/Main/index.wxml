<view>
  <block wx:for="{{ design }}" wx:for-item="component" wx:key="type">
    <block wx:if="{{ component.type === 'shopnote-subscribe' }}">
      <shopnote-subscribe></shopnote-subscribe>
    </block>
    <block wx:elif="{{ component.type === 'shopnote-content' }}">
      <view class="{{ showNav ? 'note-detail-wrapper' : '' }}">
        <shopnote-content></shopnote-content>
      </view>
    </block>
    <block wx:elif="{{ component.type === 'shopnote-feature' }}">
      <!-- 插入微页面 -->
      <shopnote-feature></shopnote-feature>
    </block>
    <block wx:elif="{{ component.type === 'shopnote-readdata' }}">
      <shopnote-readdata show-browse-count="{{ noteData.showBrowseCount }}" show-thumbs-up="{{ noteData.showThumbsUp }}" browse-count="{{ noteData.browseCount }}" thumbs-up="{{ noteData.thumbsUpCount }}" externalClasses="shopnote-sp-readdata" />
    </block>
    <block wx:elif="{{ component.type === 'shopnote-shopinfo' }}">
      <shopnote-shopinfo wx:if="{{ noteData.showEnterShop }}"></shopnote-shopinfo>
    </block>
    <view id="custom-components" />
  </block>
  <share-goods id="share-goods" wx:if="{{ noteData.noteId }}" goodsImage="{{ noteData.sharePhoto }}" salesmanAlias="{{ salesman.seller }}" title="{{ noteData.title }}" desc="{{ noteData.description }}" shopName="{{ CURRENT_GLOBAL_SHOP.shop_name }}"></share-goods>
  <salesman-cube use-page-poster="{{ true }}" bind:share-card="onSalesmanShareCard" need-bind-relation="{{ false }}" />
  <view class="live-entry-icon" wx:if="{{ liveInfo.hasLiving }}" bind:tap="handleEnterLiveRoom">
    <live-dynamic-effect>
      <image slot="cus-img" src="{{ shop.logo }}" />
    </live-dynamic-effect>
  </view>
  <inject-protocol noAutoAuth />
</view>