// eslint-disable-next-line @youzan-open/ranta-tee/valid-ranta-io
interface INoteItemsBriefInfoDTO {
  // 商品图片
  imageUrl?: string;
}
export interface noteDetailInfo {
  /** 浏览量 */
  browseCount: number;
  // 单图:0 ;多图:1
  coverPhotoMode: number;
  /** 封面图列表 */
  coverPhotos: Array<String>;
  /** 笔记id */
  noteId: number;
  /** 笔记商品简要信息列表 */
  noteItemsBriefInfo: INoteItemsBriefInfoDTO[];
  // 笔记状态  - draft 草稿  - published 已发布  - timing_published 定时发布  - deleted 已删除
  noteStatus: string;
  // 笔记模板类型  - up_new 上新  - shop_keeper 掌柜说  - shop_circle 店铺圈  - single_prod_intro 单品介绍
  noteType: string;
  /** 发布时间 */
  publishTime: Number;
  /** 是否展示浏览量 */
  showBrowseCount: Number;
  /** 是否展示进店 */
  showEnterShop: Number;
  /** 显示发布时间 */
  showPublishTime: Number;
  /** 显示推荐商品 */
  showRecommendItems: Number;
  /** 显示点赞数量 */
  showThumbsUpCount: Number;
  /** 模版id */
  templateId: Number;
  /** 点赞数量 */
  thumbsUpCount: Number;
  /** 笔记名称 */
  title: String;
  /** 用户点赞状态 */
  userThumbsStatus: Number;
}
