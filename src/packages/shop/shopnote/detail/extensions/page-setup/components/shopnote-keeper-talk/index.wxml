<view class="shopnote-keeper-talk">
  <shopnote-title title="{{ noteData.title }}" />
  <shopnote-time wx:if="{{ noteData.showBottomTime}}" time="{{ noteData.publishTime }}" externalClasses="shopnote-sp-time" />
  <shopnote-fixedicon thumbs-status="{{ thumbsStatus }}" show-goods-btn="{{ !!goodsInfo.goods.length }}" goods-num="{{ goodsInfo.contentGoods.length }}" bind:thumbclick="thumbClick" bind:shareclick="shareClick" bind:showtoast="showToast" />
  <shopnote-toast show="{{ toastShow }}" goods-list="{{ goodsInfo.contentGoods }}" bind:closeClick="closeClick" />
</view>