import { getGoodsList } from '../../api';

const app = getApp();

// 商品数组去重
const deduplicationGoodsList = (list) => {
  const map = {};

  const result = list.reduce((acc, cur) => {
    const { id } = cur;

    if (!map[id]) {
      acc.push(cur);
      map[id] = true;
    }
    return acc;
  }, []);

  return result;
};

export default Behavior({
  properties: {
    salesman: {
      type: Object,
      default: {},
    },
  },

  data: {
    toastShow: false,
    goodsInfo: {
      type: 'goods',
      layout: 1,
      size_type: 0,
      show_title: true,
      show_price: true,
      image_fill_style: 1,
      image_ratio: 1,
      buy_button_type: 1,
      show_buy_button: true,
      page_margin: 0,
      goods_margin: 10,
      goods: [],
      contentGoods: [],
      border_radius_type: 2,
    },
  },

  methods: {
    // 获取笔记中商品数据
    getNoteGoodsByIds(noteGoodIds) {
      const goodIds = noteGoodIds.join(',');
      getGoodsList({
        page: 1,
        pageSize: 30,
        offlineId: app.getOfflineId() || 0,
        goodsIds: goodIds,
      })
        .then((res) => {
          if (res) {
            const list = res.list || [];
            // 设置商品 loggerParams
            const goodsList = this.setListLoggerParams(list);
            const contentGoodsList = deduplicationGoodsList(
              this.setListLoggerParams(list, 'content_goods')
            );
            // 商品曝光
            this.goodsLoadLogger(goodsList);
            this.setYZData({
              'goodsInfo.goods': goodsList,
              'goodsInfo.contentGoods': contentGoodsList,
            });
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    showToast() {
      this.clickLog('content_goods');
      this.setYZData({
        toastShow: true,
      });
      // 第一次点击上报埋点
      if (!this.clickedContentGoods) {
        this.clickedContentGoods = true;
        const {
          goodsInfo: { contentGoods = [] },
        } = this.data;
        this.goodsLoadLogger(contentGoods);
      }
    },
    thumbClick() {
      // 点赞
      this.triggerEvent('thumbclick');
    },
    shareClick() {
      this.triggerEvent('shareclick');
    },
    closeClick() {
      this.setYZData({
        toastShow: false,
      });
    },
    thumbsStatusUpdate(val) {
      if (this.firstFlag) {
        const { thumbsUpCount } = this.data.noteData;
        if (val) {
          this.setYZData({
            'noteData.thumbsUpCount': thumbsUpCount + 1,
          });
        } else {
          this.setYZData({
            'noteData.thumbsUpCount': thumbsUpCount - 1,
          });
        }
      }
    },
  },
});
