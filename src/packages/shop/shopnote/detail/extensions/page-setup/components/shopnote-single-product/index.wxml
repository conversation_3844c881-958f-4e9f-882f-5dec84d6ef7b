<view class="shopnote-single-product">
  <shopnote-headpic head-pic-url="{{ headPhoto }}" />
  <view class="shopnote-single-product__content">
    <shopnote-title title="{{ noteData.title }}" />
    <shopnote-high-light list="{{ noteData.highLights }}" />
    <shopnote-time wx:if="{{ noteData.showPublishTime }}" time="{{ noteData.publishTime }}" />
    <shopnote-fixedicon thumbs-status="{{ thumbsStatus }}" show-goods-btn="{{ !!goodsInfo.goods.length }}" goods-num="{{ goodsInfo.goods.length }}" bind:thumbclick="thumbClick" bind:shareclick="shareClick" bind:gotoBuy="gotoBuy" single />
  </view>
</view>