import WscComponent from 'pages/common/wsc-component/index';
import templateBehavior from '../common/template-behavior';
import linkBehavior from '../common/link-behavior';
import get from '@youzan/weapp-utils/lib/get';

WscComponent({
  behaviors: [linkBehavior, templateBehavior],
  properties: {
    noteData: Object,
    thumbsStatus: {
      type: Number,
      observer: 'thumbsStatusUpdate',
    },
    featureComponents: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        if (val.length) {
          this.firstFlag = true;
          const featureList = val;
          const ids = [];
          featureList.forEach((item) => {
            const goodsId = item.itemIds;
            if (item.type === 'goods' && Array.isArray(goodsId)) {
              ids.push(goodsId.join().split());
            }
          });

          if (ids.length) {
            this.getNoteGoodsByIds(ids);
          }
        }
      },
    },
  },

  attached() { },
});
