<view class="cap-shopnote-fixedicon">
  <view class="cap-shopnote-fixedicon__left">
    <form-view type="default">
      <van-icon name="like" size="20px" class="cap-shopnote-fixedicon__left-thumb {{ thumbsStatus ? 'cap-shopnote-fixedicon__left-thumb-active' : '' }}" bindtap="thumbClick" />
    </form-view>
    <form-view type="default" open-type="share">
      <!-- <img class="cap-shopnote-fixedicon__left-share" src="share.svg" /> -->
      <van-icon name="share" size="20px" class="cap-shopnote-fixedicon__left-share"/>
    </form-view>
  </view>
  <form-view type="default">
    <view class="cap-shopnote-fixedicon__button" bindtap="gotoBuy" wx:if="{{ showGoodsBtn && single}}" style="background-color: {{themeColor}}">
      去购买
    </view>
    <view class="cap-shopnote-fixedicon__button" bindtap="showToast" wx:elif="{{ showGoodsBtn}}" style="background-color: {{themeColor}}">
      文中商品({{ goodsNum }})
    </view>
  </form-view>
</view>