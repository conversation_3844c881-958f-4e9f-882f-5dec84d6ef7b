<canvas style="width: 260px; display: {{ showCanvas ? 'block' : 'none' }}" class="share-canvas {{wrapperClass}}" canvas-id="{{ canvasId }}" />

<!-- 分享遮罩层 -->
<van-popup
  show="{{ showModal }}"
  custom-class="share-image__popup"
  z-index="{{ 1000 }}"
  bind:click-overlay="closeShareImageModal"
>
  <view class="share-image__container {{ wrapperClass}}">
    <image
      src="{{ src }}"
      class="share-image--preview {{ wrapperClass}}"
    />
    <!-- <view class="share-image__info">保存图片后，可分享到朋友圈</view> -->
    <view class="share-image__btn">
      <theme-view
        custom-class="zan-btn zan-btn--danger save-image--btn"
        bg="main"
        color="main-text"
        bindtap="clickSaveImage"
      >
        保存图片
      </theme-view>
    </view>
  </view>
</van-popup>

<van-action-sheet
  show="{{ sheet.show }}"
  close-on-click-overlay="{{ sheet.closeOnClickOverlay }}"
  bind:close="closeActionSheet"
  bind:cancel="closeActionSheet" >
  <view>
    <button
      wx:if="{{ isSupportOpenBusinessView && isShowRecommendGoodThings }}"
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--top action-recommend"
      bind:tap="handleRecommend"
    >
      <view class="big-word"><view class="recommend-icon icon-like-bold"></view>推荐好物</view>
      <view class="small-word">(分享到好物圈)</view>
    </button>
    <button
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--top"
      open-type="share"
    >
      发送给朋友
    </button>
    <button
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--top"
      bind:tap="handlePoster"
    >
      生成海报
    </button>
  </view>
  <view
    class="action-sheet__cancel"
    hover-class="action-sheet__cancel--hover"
    hover-stay-time="70"
    bind:tap="closeActionSheet"
  >
    取消
  </view>
</van-action-sheet>

