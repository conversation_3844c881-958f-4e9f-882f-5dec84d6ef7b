import WscComponent from 'pages/common/wsc-component/index';
import linkBehavior from '../common/link-behavior';
import templateBehavior from '../common/template-behavior';

WscComponent({
  behaviors: [linkBehavior, templateBehavior],
  data: {
    goodsInfo: {
      type: 'goods',
      layout: 1,
      size_type: 0,
      show_title: true,
      show_price: true,
      image_fill_style: 1,
      image_ratio: 1,
      buy_button_type: 1,
      show_buy_button: true,
      page_margin: 15,
      goods_margin: 10,
      goods: [],
      contentGoods: [],
      border_radius_type: 2
    }
  },
  properties: {
    noteData: Object,
    noteGoodIds: Array,
    thumbsStatus: {
      type: Number,
      observer: 'thumbsStatusUpdate'
    },
  },

  attached() {
    this.firstFlag = true;
    const noteGoodIds = this.data.noteGoodIds;
    this.getNoteGoodsByIds(noteGoodIds);
  },

  methods: {
    showcaseHandleGoodsBuy(e) {
      this.triggerEvent('clickcart', e.detail);
    },
  }
});
