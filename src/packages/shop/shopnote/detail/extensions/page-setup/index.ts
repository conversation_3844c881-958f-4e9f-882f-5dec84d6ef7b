// @ts-nocheck

// import { createStore } from '@ranta/store';
import Toast from '@vant/weapp/dist/toast/toast';
import get from '@youzan/weapp-utils/lib/get';
import Args from '@youzan/weapp-utils/lib/args';
import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';
import navigate from 'shared/utils/navigate';
import adaptorComponents from 'constants/adaptor-components';
import { bridge, useAsEvent } from '@youzan/ranta-helper';

import {
  querySubscriptionInfo,
  queryShopNoteSubscibeSetting,
  fetchLiveInfoApi,
  getGoodsList,
} from './api';
import { SHOPNOTE_TPL, SHOP_LOCK_STATUS } from './constants';
import { getBannerId } from './components/common/logger';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';
import { initSalesman } from 'shared/utils/salesman-share';
// eslint-disable-next-line @youzan-open/tee/valid-same-extension-import
import yunDesign from '../../design.json';
import set from 'utils/set';
import { noteDetailInfo } from './type';
import { logPageEnter } from 'utils/log/logv3';
import {
  addParamPreventCouponLeak,
  FeaturePageType,
} from '@/utils/prevent-coupon-leak';
import { EShoppingGuideBindSourceType } from 'constants/guide';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';

const HomePath = '/pages/home/<USER>/index';

const app = getApp();
export default class {
  ctx: any;

  /**
   * assetsInfo
   * @desc 店铺笔记详情数据
   * @type {AssetsInfo}
   */
  @bridge('noteDetailData', 'data')
  noteDetailData: noteDetailInfo;

  /**
   * @desc 获取商品组件列表
   */
  @bridge('getGoodsList', 'process')
  getGoodsList(params) {
    return getGoodsList(params);
  }

  /**
   * @desc 重置分享数据
   */
  @bridge('resetShareData', 'process')
  resetShareData(yunShareData) {
    getApp().logger.skynetLog({
      appName: 'wsc',
      logIndex: 'shop_note_detail',
      message:
        'shop note detail yun share data' + JSON.stringify(yunShareData || {}),
    });
    this.ctx.data.yunShareData = yunShareData;
  }

  // store = createStore({
  //   actions: {
  //     handleSkuClose() {
  //       this.setYZData({
  //         'featureSkuData.showGoodsSku': false,
  //       });
  //     },
  //   },
  // });

  constructor({ ctx }) {
    ctx.store = this.store;
    this.ctx = ctx;
    this.ctx.data.themeClass = app.themeClass;
    this.ctx.data.noteData = {};
    this.ctx.data.theme = {};
    this.ctx.data.noteGoodIds = [];
    this.ctx.data.thumbsStatus = 0;
    this.ctx.data.pageBgColor = '#fff';
    this.ctx.data.noteType = SHOPNOTE_TPL;
    this.ctx.data.noteId = '';
    this.ctx.data.salesman = {};
    this.ctx.data.noteAlias = '';
    this.ctx.data.shopName = '';
    this.ctx.data.showSubBtn = false;
    this.ctx.data.subscribeStatus = false;
    this.ctx.data.guideShow = false;
    this.ctx.data.showNav = false;
    this.ctx.data.dialogCustomStyle = 'background: transparent !important';
    this.ctx.data.liveInfo = {
      hasLiving: false,
      roomId: undefined,
    };
    this.ctx.data.featureComponents = [];
    this.ctx.data.navigationbarConfigData = {
      style_color_type: 'global',
      navigationbar_config_type: 'global',
    };
    this.ctx.data.design = [];
    // 云定制可修改的分享数据
    this.ctx.data.yunShareData = null;
  }

  /**
   * 生命周期函数--监听页面加载
   */
  beforePageMount(options) {
    this.initDesign();
    initSalesman.call(this, {
      sst: 10,
      gst: EShoppingGuideBindSourceType.TINY_PAGE,
      isUseRanta: true,
    });

    app.getShopInfo().then((shopRes) => {
      const { shopMetaInfo = {} } = shopRes || {};
      if (shopMetaInfo.lock_status === SHOP_LOCK_STATUS.LOCK) {
        return wx.redirectTo({ url: '/packages/common/lock/index' });
      }
      const { noteAlias } = options.query || {};
      this.ctx.data.noteAlias = noteAlias;
      if (noteAlias) {
        this.fetchSalesmanBaseData();
        this.fetchShopSubscribeSetting();
        this.fetchNoteData(noteAlias)
          .then((res) => {
            this.initNoteData(res);
          })
          .catch(() => {
            Toast('页面参数错误');
          });
      } else {
        Toast('页面参数错误');
      }
    });
  }

  setYZData(obj) {
    Object.entries(obj).forEach(([key, value]) => {
      set(this.ctx.data, key, value);
    });
  }

  initDesign() {
    const { design: oldDesign } = yunDesign;

    const index = oldDesign.findIndex((v) => v.type === 'config');

    this.setYZData({
      design: index === 0 ? oldDesign.slice(1) : oldDesign,
    });
  }

  fetchNoteData(currentAlias) {
    return new Promise((resolve) => {
      app.getShopInfo().then((data) => {
        const { chainStoreInfo, kdt_id: kdtId } = data;
        const { isRootShop = false, isMultiOnlineShop = false } =
          chainStoreInfo;
        const currentKdtId = app.getKdtId();
        const queryData = {
          noteAlias: currentAlias,
          targetKdtId: currentKdtId || kdtId,
        };
        if (!isMultiOnlineShop) {
          this.fetchLiveInfo();
          this.getNoteDetailData(queryData).then((res) => {
            resolve(res);
          });
          this.fetchShowcaseContainerData(queryData);
          // 如果是总店则监听进店事件
        } else if (isRootShop) {
          app.once('app:chainstore:kdtid:update', ({ kdtId }) => {
            this.fetchLiveInfo();
            this.fetchShowcaseContainerData({
              ...queryData,
              targetKdtId: kdtId,
              isChain: true,
            });
            this.getNoteDetailData({
              ...queryData,
              targetKdtId: kdtId,
              isChain: true,
            }).then((res) => {
              resolve(res);
            });
          });
        } else {
          this.fetchLiveInfo();
          this.fetchShowcaseContainerData({
            ...queryData,
          });
          this.getNoteDetailData({
            ...queryData,
          }).then((res) => {
            resolve(res);
          });
        }
      });
    });
  }

  getNoteDetailData({ noteAlias, sourceKdtId, targetKdtId, isChain }) {
    return app
      .request({
        path: '/wscshop/shopnote/detail.json',
        data: {
          noteAlias,
          sourceKdtId,
          targetKdtId,
          isChain,
          adaptorComponents: adaptorComponents.join(','),
        },
      })
      .then((data) => {
        if (data?.requestData?.length === 0) {
          Toast({
            message: '店铺笔记已失效',
            duration: 1000,
          });
          setTimeout(() => {
            // eslint-disable-next-line @youzan/dmc/wx-check
            wx.switchTab({
              url: HomePath,
            });
          }, 1000);
        } else {
          this.noteDetailData = data;
          return data;
        }
      })
      .catch((res) => {
        if (res && res.code === 302) {
          const { data } = res;

          let { location = '' } = data || {};
          if (location && /^(\/?)pages|packages/.test(location)) {
            if (location.slice(0, 1) !== '/') {
              location = '/' + location;
            }
            location = Args.add(location, this.options);

            return wx.redirectTo({ url: location });
          }
        }
      });
  }

  initNoteData(res) {
    if (res) {
      if (res.isLocked) {
        return wx.redirectTo({ url: '/packages/common/lock/index' });
      }
      if (res.noteData) {
        const { noteData } = res;
        this.setYZData({
          noteId: noteData.noteId,
        });
        // const url = this.ctx.env.route;
        // app.logger.performance('app', 'pageshow');
        // app.logger.performance('page', 'pageshow', false, url);
        const { noteGoodIds } = res;
        // let featureComponents = res.featureComponents || [];
        // featureComponents = featureComponents.map((item) => {
        //   if (item.type === 'rich_text' || item.type === 'rich_text_xss') {
        //     item.externalStyle = 'padding: 0 15px';
        //   }
        //   return item;
        // });
        // featureComponents = addParamPreventCouponLeak(
        //   featureComponents,
        //   FeaturePageType.SHOP_NOTE,
        //   noteData.noteId
        // );
        const sharePhoto =
          noteData?.shareImgaeUrl ||
          noteData.headPhoto ||
          get(noteData, 'coverPhotos[0]') ||
          get(noteData, 'noteItemsBriefInfo[0].imageUrl');
        // 选取渲染相关的笔记属性
        const simpleNoteData = {
          title: noteData.realTitle || noteData.title,
          shareTitle: noteData.shareTitle,
          showBrowseCount: noteData.showBrowseCount,
          showThumbsUp: noteData.showThumbsUpCount,
          thumbsUpCount: noteData.thumbsUpCount || 0,
          browseCount: noteData.browseCount || 0,
          publishTime: noteData.publishTime,
          showPublishTime: noteData.showPublishTime,
          templateId: noteData.templateId,
          showEnterShop: noteData.showEnterShop,
          noteId: noteData.noteId,
          noteItemsBriefInfo: noteData.noteItemsBriefInfo,
          sharePhoto,
        };
        // 掌柜说 店铺圈 单品分析模版发布时间展示在底部
        if (
          (noteData.templateId === SHOPNOTE_TPL.KEEPER_TALK ||
            noteData.templateId === SHOPNOTE_TPL.SHOP_CIRCLE ||
            noteData.templateId === SHOPNOTE_TPL.SINGLE_PRODUCTION) &&
          noteData.showPublishTime
        ) {
          simpleNoteData.showBottomTime = true;
        }
        if (noteData.templateId === SHOPNOTE_TPL.NEW_ARRIVAL) {
          simpleNoteData.headPhoto = noteData.headPhoto;
          simpleNoteData.description = noteData.description;
          this.setYZData({
            noteData: simpleNoteData,
            noteGoodIds,
            thumbsStatus: noteData.userThumbsStatus || 0,
          });
        } else if (noteData.templateId === SHOPNOTE_TPL.KEEPER_TALK) {
          this.setYZData({
            noteData: simpleNoteData,
            thumbsStatus: noteData.userThumbsStatus || 0,
          });
        } else if (noteData.templateId === SHOPNOTE_TPL.SHOP_CIRCLE) {
          simpleNoteData.description = noteData.description;
          this.setYZData({
            noteData: simpleNoteData,
            noteGoodIds,
            thumbsStatus: noteData.userThumbsStatus || 0,
          });
        } else if (noteData.templateId === SHOPNOTE_TPL.SINGLE_PRODUCTION) {
          simpleNoteData.description = noteData.description;
          simpleNoteData.highLights = noteData.highLights;
          this.setYZData({
            noteData: simpleNoteData,
            thumbsStatus: noteData.userThumbsStatus || 0,
            noteGoodIds,
          });
        }

        // 店铺笔记详情埋点
        app.logger.log({
          et: 'custom',
          ei: 'shopnote',
          en: '店铺笔记',
          params: {
            note_id: noteData.noteId,
            note_type: noteData.noteTemplateType,
            note_model_id: noteData.templateId,
            spm: `shopnote.${noteData.noteId}`,
          },
        });
      }
    }
  }

  // 获取分销员信息
  fetchSalesmanBaseData() {
    app.carmen({
      api: 'youzan.salesman.wap.account/1.0.0/get',
      success: (res) => {
        this.setYZData({
          'salesman.openSalesman': res.openSalesman,
          'salesman.icon': res.settingIcon,
          'salesman.name': res.settingName,
          'salesman.isSalesman': res.salesman,
          'salesman.alias': res.sls,
        });
      },
    });
  }

  fetchShopSubscribeSetting() {
    querySubscriptionInfo().then((res) => {
      if (res.status === 1 && res.weappPush === 1) {
        this.setYZData({
          showSubBtn: true,
          showNav: true,
        });
        queryShopNoteSubscibeSetting().then((res) => {
          if (res.value) {
            this.setYZData({
              subscribeStatus: true,
            });
          }
        });
      }
    });
  }

  // 获取直播信息
  async fetchLiveInfo() {
    const kdtId = app.getKdtId();
    // eslint-disable-next-line @youzan/koko/no-async-await
    const result = await fetchLiveInfoApi(kdtId);
    this.setYZData({
      liveInfo: {
        hasLiving: result.hasLiving,
        roomId: result.roomId,
      },
    });
    return result;
  }

  fetchShowcaseContainerData({ noteAlias, sourceKdtId, targetKdtId, isChain }) {
    app
      .request({
        path: '/wscdeco/biz-component/shop-note.json',
        method: 'GET',
        data: {
          noteAlias,
          sourceKdtId,
          targetKdtId,
          isChain,
          stage: 100,
        },
      })
      .then((res) => {
        let featureComponents = res.components.map((item) => {
          if (item.type === 'rich_text' || item.type === 'rich_text_xss') {
            item.externalStyle = 'padding: 0 15px';
          }
          if (item.type === 'goods') {
            item.preventBuyClickItem = true;
          }
          return item;
        });

        featureComponents = addParamPreventCouponLeak(
          res.components,
          FeaturePageType.SHOP_NOTE,
          this.ctx.data.noteData.noteId
        );

        // this.ctx.data.featureComponents = featureComponents;
        this.setYZData({
          featureComponents,
        });
      })
      .catch(() => {});
  }

  onPageShow() {
    if (this.noteId) {
      this.enterpageLog([this.ctx.env.route, this.noteId, {}]);
    }
  }

  enterpageLog(logParams, pageName) {
    this.__firstShow = false;

    this._logParams = logParams || this._logParams || [];
    this._logTitle = pageName || this._logTitle || '';

    if (this._logTitle) {
      const app = getApp();
      app.logger.setPageName(this._logTitle);
    }
    logPageEnter(...this._logParams);
  }

  // 增加分享钩子
  onShareAppMessage() {
    const { noteAlias, noteData, yunShareData } = this.ctx.data;

    const { title, description, shareTitle } = noteData || {};
    const shopName = get(this.ctx.data.shop, 'shopName', '');

    const desc = description || `${shopName}的精彩内容分享给你，快来看一看吧`;
    const data = noteData;
    let query = {
      noteAlias,
      is_share: '1',
      ...defaultEnterShopOptions,
    };
    let cover = get(data, 'sharePhoto');
    if (this.ctx.data.salesman && this.ctx.data.salesman.share) {
      query = {
        ...query,
        ...getSalesmanParamsObject({ sl: this.ctx.data.salesman.seller }),
      };
    }

    const shareUrl = Args.add('packages/shop/shopnote/detail/index', query);
    const isHttp = (url) => /^(http|https):\/\//.test(url);
    cover = isHttp(cover) ? cover : `https:${cover}`;
    // 分享
    this.clickShareLog();
    if (yunShareData) {
      return yunShareData;
    }

    return {
      title: shareTitle || title,
      desc,
      path: shareUrl,
      imageUrl: cover,
    };
  }

  clickShareLog() {
    // banner_id
    const params = {
      banner_id: getBannerId('share'),
    };
    // 获取埋点格式
    const loggerMsg = getComponentLoggerParams('click_content', params);
    // 上报
    ensureAppLogger(loggerMsg);
  }

  handleEnterLiveRoom() {
    const { liveInfo = {} } = this;
    wx.navigateTo({
      url: `/packages/weapp-live/room/index?id=${liveInfo.roomId}`,
    });
  }

  enterShop() {
    wx.switchTab({ url: HomePath });
  }
}
