const app = getApp();

// 点赞
export const setThumbStatus = (data) => {
  return app.request({
    path: '/wscshop/shopnote/setThumbStatus.json',
    data,
    method: 'POST',
  });
};

export const getShopNoteDetail = (data) => {
  return app.request({
    path: '/wscshop/shopnote/detail.json',
    data,
  });
};

// 获取商品列表
export const getGoodsList = (data) => {
  return app.request({
    path: '/wscshop/showcase/goodsList.json',
    data: { ...data, isShowPeriod: 1 },
  });
};

// 获取用户订阅设置
export const queryShopNoteSubscibeSetting = (data) => {
  return app.request({
    path: '/wscshop/shopnote/queryShopNoteSubscription.json',
    data,
  });
};

// 获取店铺订阅设置
export const querySubscriptionInfo = (data = {}) => {
  return app.request({
    path: '/wscshop/shopnote/querySubscriptionInfo.json',
    data,
  });
};

// 编辑用户订阅
export const editShopNoteSubscription = (data) => {
  return app.request({
    path: '/wscshop/shopnote/editShopNoteSubscription.json',
    data,
    method: 'POST',
  });
};

// 绑定分销员
export const bindSalesMan = (salesmanAlias) => {
  if (!salesmanAlias) return;

  app.carmen({
    api: 'youzan.salesman.wap.customer.relation/1.0.0/add',
    query: {
      sellerFrom: salesmanAlias,
    },
    success: () => {
      console.log('salesman:bind:success');
    },
  });
};

export function fetchLiveInfoApi(kdtId) {
  return app
    .request({
      path: '/wscshop/content-center/fetchLiveInfo.json',
      data: {
        kdtId,
        scene: 3, // scene = 3;
      },
    })
    .then((res) => {
      return res;
    });
}
