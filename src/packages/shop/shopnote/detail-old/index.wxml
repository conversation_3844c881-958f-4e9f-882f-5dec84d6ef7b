<import src="./custom-tpl.wxml" />
<page-container
  class="shopnote-container youzan-{{ appType }} {{ themeClass }} page-{{ deviceType }} feature-page__top-hook"
  page-bg-color="{{ pageBgColor }}"
  copyright-bg-color="{{ pageBgColor }}"
  show-store-switch
  open-custom-nav
  navigationbar-config-data="{{ navigationbarConfigData }}"
>
  <block wx:for="{{ design }}" wx:key="type">

    <block wx:if="{{ item.type === 'shopnote-subscribe' }}">
      <view class="nav-container" wx:if="{{ showNav }}">
        <view class="nav-enter-shop" bind:tap="redirectToHome">
          <image src="{{ CURRENT_GLOBAL_SHOP.logo }}" />
        </view>
        <view class="nav-enter-name">{{ CURRENT_GLOBAL_SHOP.shop_name }}</view>
        <view class="nav-enter-right">
          <form-view type="default">
            <view
              class="nav-sub-btn {{ subscribeStatus ? 'nav-enter-right__gray' : '' }}"
              wx:if="{{ showSubBtn }}"
              bind:tap="handleSubscribe"
            >{{ subscribeStatus ? '已订阅' : '订阅' }}</view>
          </form-view>
        </view>
        <van-dialog id="van-dialog" />
        <van-dialog use-slot show="{{ guideShow }}" showConfirmButton="{{ false }}" custom-style="{{ dialogCustomStyle }}">
          <image src="https://b.yzcdn.cn/public_files/2019/08/01/df3bfea69c6c333e48d7e4bf26d9ef1f.png" />
          <button class="nav-guide-confirm__btn" bind:tap="closeGuideDialog">我知道了</button>
        </van-dialog>
      </view>
    </block>

    <block wx:if="{{ item.type === 'shopnote-content' }}">
      <view class="{{ showNav ? 'note-detail-wrapper' : '' }}">
        <shopnote-new-arrival
          wx:if="{{ noteData.templateId === noteType.NEW_ARRIVAL }}"
          note-data="{{ noteData }}"
          note-good-ids="{{ noteGoodIds }}"
          thumbs-status="{{ thumbsStatus }}"
          salesman="{{ salesman }}"
          bind:clickcart="showcaseHandleGoodsBuy"
          bind:thumbclick="thumbClick"
          bind:shareclick="shareClick"
        ></shopnote-new-arrival>
        <shopnote-keeper-talk
          wx:if="{{ noteData.templateId === noteType.KEEPER_TALK }}"
          note-data="{{ noteData }}"
          thumbs-status="{{ thumbsStatus }}"
          salesman="{{ salesman }}"
          bind:thumbclick="thumbClick"
          bind:shareclick="shareClick"
        ></shopnote-keeper-talk>
        <shopnote-shop-circle
          wx:if="{{ noteData.templateId === noteType.SHOP_CIRCLE }}"
          note-data="{{ noteData }}"
          thumbs-status="{{ thumbsStatus }}"
          note-good-ids="{{ noteGoodIds }}"
          salesman="{{ salesman }}"
          bind:thumbclick="thumbClick"
          bind:shareclick="shareClick"
          bind:clickcart="showcaseHandleGoodsBuy"
        ></shopnote-shop-circle>
        <shopnote-single-production
          wx:if="{{ noteData.templateId === noteType.SINGLE_PRODUCTION }}"
          note-data="{{ noteData }}"
          salesman="{{ salesman }}"
          thumbs-status="{{ thumbsStatus }}"
          note-good-ids="{{ noteGoodIds }}"
          bind:thumbclick="thumbClick"
          bind:shareclick="shareClick"
        ></shopnote-single-production>
      </view>
    </block>

    <block wx:if="{{ item.type === 'shopnote-feature' }}">
      <!-- 插入微页面 -->
      <feature-sku
        wx:if="{{ featureSkuData }}"
        theme-class="{{ themeClass }}"
        feature-sku-data="{{ featureSkuData }}"
        bind:close="handleSkuClose"
      />
      <van-toast id="van-toast" />
      <van-notify id="van-notify" />
    </block>

    <block wx:if="{{ item.type === 'shopnote-readdata' }}">
      <shopnote-readdata
        show-browse-count="{{ noteData.showBrowseCount }}"
        show-thumbs-up="{{ noteData.showThumbsUp }}"
        browse-count="{{ noteData.browseCount }}"
        thumbs-up="{{ noteData.thumbsUpCount }}"
        externalClasses="shopnote-sp-readdata"
      />
    </block>

    <block wx:if="{{ item.type === 'shopnote-shopinfo' }}">
      <shopnote-shopinfo wx:if="{{ noteData.showEnterShop }}"></shopnote-shopinfo>
    </block>

    <block wx:if="{{ item.custom }}">
      <template is="{{ item.type }}" />
    </block>
    
  </block>
  <share-goods
    id="share-goods"
    wx:if="{{ noteData.noteId }}"
    goodsImage="{{ noteData.sharePhoto }}"
    salesmanAlias="{{ salesman.seller }}"
    title="{{ noteData.title }}"
    desc="{{ noteData.description }}"
    shopName="{{ CURRENT_GLOBAL_SHOP.shop_name }}"
  ></share-goods>
  <salesman-cube use-page-poster="{{ true }}" bind:share-card="onSalesmanShareCard" need-bind-relation="{{ false }}" />
  <view class="live-entry-icon" wx:if="{{ liveInfo.hasLiving }}" bind:tap="handleEnterLiveRoom">
    <live-dynamic-effect>
      <image slot="cus-img" src="{{ CURRENT_GLOBAL_SHOP.logo }}" />
    </live-dynamic-effect>
  </view>
</page-container>