import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';
import navigate from '@/helpers/navigate';
import linkBehavior from '../common/link-behavior';
import templateBehavior from '../common/template-behavior';

WscComponent({
  behaviors: [linkBehavior, templateBehavior],
  data: {
    headPhoto: '',
  },
  properties: {
    noteData: Object,
    noteGoodIds: Array,
    thumbsStatus: {
      type: Number,
      observer: 'thumbsStatusUpdate'
    },
  },

  attached() {
    const noteGoodIds = this.data.noteGoodIds;
    const headPhoto = get(this, 'data.noteData.headPhoto', '') || get(this, 'data.noteData.noteItemsBriefInfo[0].imageUrl', '');
    this.setYZData({
      headPhoto
    });
    this.firstFlag = true;
    this.getNoteGoodsByIds(noteGoodIds);
  },

  methods: {
    gotoBuy() {
      const url = get(this.data, 'goodsInfo.goods[0].url', '');
      navigate.navigate({
        url
      });
    }
  }
});
