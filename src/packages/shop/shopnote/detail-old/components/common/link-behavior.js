import Args from '@youzan/weapp-utils/lib/args';
import appLoggerBehavior from 'shared/components/showcase/behaviors/app-logger-behavior';
import get from '@youzan/weapp-utils/lib/get';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';
import { getBannerId } from './logger';

export default Behavior({
  behaviors: [appLoggerBehavior],

  methods: {
    /**
     * 详情获取banner_id
     * @param { String } list/home/<USER>
     * @param { Number }
     * @return {string}
     */
    getBannerId(type, index = 0) {
      return getBannerId(type, index);
    },

    /**
     * 设置商品列表 loggerParams
     * @param { Array } list
     * @param { Array } type
     * @return {Array}
     */
    setListLoggerParams(list, type = 'goods') {
      return list.map((item, index) => this.setItemLoggerParams(item, index, type));
    },

    /**
     * 设置商品loggerParams
     * @param { Object }item
     * @param { Number }index
     * @return { Object }
     */
    setItemLoggerParams(item, index, type) {
      const params = { ...item };
      const bannerId = this.getBannerId(type, index + 1);
      const sls = get(this.data, 'salesman.seller', '');
      params.loggerParams = {
        goods_id: item.id,
        banner_id: bannerId,
        item_id: item.id,
        item_type: 'goods'
      };
      params.url = Args.add('/pages/goods/detail/index', { alias: item.alias, banner_id: bannerId });
      if (sls) {
        params.url = addSalesmanParams({ url: params.url, sls });
      }
      return params;
    },

    /**
     * 商品曝光埋点
     * @param { Array } list
     */
    goodsLoadLogger(list) {
      const params = list.map(item => item.loggerParams);
      this.ensureAppLogger('view', params);
    },

    /**
     * 点击手动打点
     * @param { String } home/more
     */
    clickLog(type) {
      const params = { banner_id: this.getBannerId(type) };
      this.ensureAppLogger('click_content', params);
    },
  }
});
