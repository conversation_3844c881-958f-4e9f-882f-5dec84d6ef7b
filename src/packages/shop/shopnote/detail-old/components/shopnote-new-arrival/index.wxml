<view class="shopnote-new-arrival">
  <view class="shopnote-new-arrival__content">
    <shopnote-title
      title="{{ noteData.title }}"
    />
    <shopnote-time
      wx:if="{{ noteData.showPublishTime }}"
      time="{{ noteData.publishTime }}"
    />
    <shopnote-desc
      desc="{{ noteData.description }}"
    />
    <view class="shopnote-new-arrival__content--goods">
      <showcase-goods
        component-data="{{ goodsInfo }}"
        offline-id="{{ shopInfo.offline_id }}"
        page-lifetimes="{{ ['onPageScroll'] }}"
        kdt-id="{{ shopInfo.kdt_id }}"
        bind:buy="showcaseHandleGoodsBuy"
      />
    </view>
    <shopnote-fixedicon
      thumbs-status="{{ thumbsStatus }}"
      show-goods-btn="{{ !!goodsInfo.goods.length }}"
      goods-num="{{ goodsInfo.goods.length }}"
      bind:thumbclick="thumbClick"
      bind:shareclick="shareClick"
      bind:showtoast="showToast"
    />
    <shopnote-toast
      show = "{{ toastShow }}"
      goods-list = "{{ goodsInfo.contentGoods }}"
      bind:closeClick = "closeClick"
    />
  </view>
</view>
