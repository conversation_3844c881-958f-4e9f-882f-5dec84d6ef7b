import WscComponent from 'pages/common/wsc-component/index';
import theme from 'shared/common/components/theme-view/theme';

WscComponent({
  properties: {
    list: {
      type: <PERSON><PERSON>y,
      observer(highList) {
        this.setYZData({
          highList
        });
      }
    }
  },
  data: {
    highList: [],
    themeColor: '#f44',
    themeBgColor: 'rgba(255,64,64,0.1)'
  },
  attached() {
    theme.getThemeColor('general').then((color) => {
      this.setYZData({
        themeColor: color,
        themeBgColor: `rgba(${theme.switchHexToRgb(color).join(',')},0.1)`
      });
    });
    console.log('themeGgbColor:', this.data.themeBgColor);
  }
});
