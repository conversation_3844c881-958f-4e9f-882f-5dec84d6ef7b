@import '~shared/common/css/_mixins.scss';
.shopnote-shopinfo {
  position: relative;
  text-align: center;
  font-size: 14px;
  margin-top: 20px;
  margin-bottom: 60px;
}

.shopnote-shopinfo__banner {
  display: inline-block;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 1px solid #e5e5e5;
}

.shopnote-shopinfo__banner--img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.shopnote-shopinfo__name {
  margin: 10px 0;
  color: #333333;
  text-align: center;
  line-height: 20px;
}

.shopnote-shopinfo__btn--more,
.shopnote-shopinfo__btn--intro {
  position: relative;
  font-size: 13px;
  display: inline-block;
  color: #f44;
  line-height: 24px;
  padding: 0 12px;
  margin-right: 10px;

  & .after {
    position: absolute;
    top: 0;
    left: 0;
    box-sizing: border-box;
    width: 200%;
    height: 200%;
    border: 0 solid #c8c9cc;
    transform: scale(0.5);
    transform-origin: 0 0;
    pointer-events: none;
    border-width: 1px;
    border-radius: 50px;
  }
}
