import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';
import linkBehavior from '../../common/link-behavior';

const app = getApp();

WscComponent({
  behaviors: [linkBehavior],

  properties: {
    shopInfo: Object
  },

  attached() {
    app.getShopInfo().then((shop) => {
      this.setYZData({
        shopInfo: {
          shopLogo: shop.logo,
          shopName: shop.shop_name
        }
      });
    });
  },

  methods: {
    directToHome() {
      // 跳转店铺首页打点
      this.clickLog('enter_home');
      navigate.switchTab({ url: '/packages/home/<USER>/index' });
    },
    linkMore() {
      // 跳转首页打点
      this.clickLog('enter_more');

      navigate.switchTab({
        url: '/packages/shop/shopnote/list/index'
      });
    }
  }
});
