import WscComponent from 'pages/common/wsc-component/index';
import theme from 'shared/common/components/theme-view/theme';

WscComponent({
  properties: {
    thumbsStatus: Number,
    showGoodsBtn: Boolean,
    goodsNum: Number,
    single: {
      type: Boolean,
      value: false
    }
  },

  data: {
    themeColor: '#f44'
  },

  methods: {
    thumbClick() {
      this.triggerEvent('thumbclick');
    },
    shareClick() {
      this.triggerEvent('shareclick');
    },
    showToast() {
      this.triggerEvent('showtoast');
    },
    gotoBuy() {
      this.triggerEvent('gotoBuy');
    }
  },
  attached() {
    theme.getThemeColor('general').then((color) => {
      this.setYZData({
        themeColor: color
      });
    });
  }
});
