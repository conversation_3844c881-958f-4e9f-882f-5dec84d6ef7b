import WscComponent from 'pages/common/wsc-component/index';
import navigate from '@/helpers/navigate';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import theme from 'shared/common/components/theme-view/theme';

WscComponent({
  properties: {
    show: <PERSON><PERSON><PERSON>,
    goodsList: {
      type: <PERSON><PERSON><PERSON>,
      observer(newVal) {
        // ?? ob obj
        const list = newVal.map((item) => {
          item.image_url = cdnImage(item.image_url);
          return item;
        });
        this.setYZData({ list });
      }
    }
  },

  data: {
    list: [],
    themeColor: '#f44'
  },

  methods: {
    move() {},
    gotoBuy(event) {
      const index = event.currentTarget.dataset.index;
      const goodsItem = this.data.goodsList[index];
      navigate.navigate({
        url: goodsItem.url
      });
    },
    closeClick() {
      this.triggerEvent('closeClick');
    }
  },
  attached() {
    theme.getThemeColor('general').then((color) => {
      this.setYZData({
        themeColor: color
      });
    });
  }
});
