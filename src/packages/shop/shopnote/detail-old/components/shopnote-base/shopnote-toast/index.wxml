<van-popup round position="bottom" custom-style="max-height:78vh;height:auto" show="{{ show }}" bind:close="closeClick">
  <view class="note-article-product">
    <view class="note-article-product__title">
      <view class="note-article-product__title-main">文中商品</view>
      <view class="note-article-product__title-close" bindtap="closeClick">
        <view class="icon-cross" />
      </view>
    </view>
    <form-view type="default">
      <view class="note-article-product__content" wx:for="{{list}}" wx:for-item="item" wx:key="index" data-index="{{index}}" bindtap="gotoBuy">
        <view class="note-article-product__content-item">
          <view>
            <image class="note-article-product__content-item--image" src="{{item.image_url}}" alt="" />
          </view>
          <view class="note-article-product__content-item--right">
            <view class="note-article-product__content-item--right-des">{{ item.title }}</view>
            <view class="note-article-product__content-item--right-bottom">
              <view class="note-article-product__content-item--right-bottom-price" style="color:{{themeColor}}">
                <view class="unit">¥</view>
                {{ item.price }}
              </view>
              <view class="note-article-product__content-item--right-bottom-button" style="background-color:{{themeColor}}">
                购买
              </view>
            </view>
          </view>
        </view>
      </view>
    </form-view>
  </view>
</van-popup>