import { SyncEvent } from '@youzan/weapp-ecloud-sdk';

const resetShareDataEvent = new SyncEvent();
const noteDataEvent = new SyncEvent();

export function injectEvents(sdk, ctx) {
  sdk.setPageEvent('resetShareDataEvent', resetShareDataEvent);
  sdk.setPageEvent('noteDataEvent', noteDataEvent);

  resetShareDataEvent.on('data', (yunShareData) => {
    getApp().logger.skynetLog({
      appName: 'wsc',
      logIndex: 'shop_note_detail',
      message:
        'shop note detail yun share data' + JSON.stringify(yunShareData || {}),
    });
    ctx.setData({ yunShareData });
  });

  ctx.on('noteDetailData', (data) => {
    noteDataEvent.trigger(data);
    sdk.page.__setData('noteDetailData', data);
  });
}
