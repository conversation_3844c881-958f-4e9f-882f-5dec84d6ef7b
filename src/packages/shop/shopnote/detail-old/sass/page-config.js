import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { getYunSdk } from '@youzan/weapp-ecloud-sdk';
import { openState } from './open-state';
import { injectProcesses } from './process';
import { injectEvents } from './event';
import EcloudSpaceBiz from '../ecloud-space';

export default {
  ...YunPageConfig,

  onLoad() {
    const sdk = getYunSdk();
    // 1. 注册开放数据
    sdk.setPageData(this, openState);

    // 2. 注册开放流程
    injectProcesses(sdk, this);

    // 3. 注册开放事件
    injectEvents(sdk, this);

    // 4. 载入自定义定制逻辑
    try {
      EcloudSpaceBiz && EcloudSpaceBiz(sdk);
    } catch (e) {
      //
    }
  },
};
