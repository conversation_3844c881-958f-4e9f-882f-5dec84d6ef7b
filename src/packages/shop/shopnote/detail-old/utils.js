export function formatTime(time) {
  if (typeof time !== 'number' || isNaN(time)) return '这里显示时间';
  const date = new Date(time);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  if (diff < 0) return '';
  if (diff <= 60 * 1000) {
    return '刚刚';
  } if (diff < 59 * 60 * 1000) {
    return `${Math.floor(diff / (1000 * 60))}分钟前`;
  } if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (1000 * 60 * 60))}小时前`;
  }
  return `${(date.getMonth() + 1)}月${date.getDate()}日`;
}
