import WscPage from 'pages/common/wsc-page/index';
import Toast from '@vant/weapp/dist/toast/toast';
import get from '@youzan/weapp-utils/lib/get';
import Args from '@youzan/weapp-utils/lib/args';
import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';
import navigate from 'shared/utils/navigate';
import adaptorComponents from 'constants/adaptor-components';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import {
  setThumbStatus,
  querySubscriptionInfo,
  queryShopNoteSubscibeSetting,
  editShopNoteSubscription,
  fetchLiveInfoApi,
} from './api';
import { SHOPNOTE_TPL, SHOP_LOCK_STATUS } from './constants';
import { getBannerId } from './components/common/logger';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';
import { initSalesman } from 'shared/utils/salesman-share';
import yunDesign from './design.json';
import {
  addParamPreventCouponLeak,
  FeaturePageType,
} from '@/utils/prevent-coupon-leak';

/* #ifdef BUILD_ENV=youzanyun */
import SaasPageConfig from './sass/page-config';
import { EShoppingGuideBindSourceType } from 'constants/guide';
/* #endif */

// eslint-disable-next-line no-undef
let extendPageConfig = {};
/* #ifdef BUILD_ENV=youzanyun */
extendPageConfig = SaasPageConfig;
/* #endif */

const app = getApp();

WscPage(extendPageConfig, {
  data: {
    themeClass: app.themeClass,
    noteData: {},
    theme: {},
    noteGoodIds: [],
    thumbsStatus: 0,
    pageBgColor: '#fff',
    clickThumbFlag: true,
    noteType: SHOPNOTE_TPL,
    noteId: '',
    salesman: {},
    noteAlias: '',
    shopName: '',
    showSubBtn: false,
    subscribeStatus: false,
    guideShow: false,
    showNav: false,
    dialogCustomStyle: 'background: transparent !important',
    liveInfo: {
      hasLiving: false,
      roomId: undefined,
    },
    navigationbarConfigData: {
      style_color_type: 'global',
      navigationbar_config_type: 'global',
    },
    degign: [],
    // 云定制可修改的分享数据
    yunShareData: null,
  },
  fetchNoteData(currentAlias) {
    return new Promise((resolve) => {
      app.getShopInfo().then((data) => {
        const { chainStoreInfo, kdt_id: kdtId } = data;
        const { isRootShop = false, isMultiOnlineShop = false } =
          chainStoreInfo;
        const currentKdtId = app.getKdtId();
        const queryData = {
          noteAlias: currentAlias,
          targetKdtId: currentKdtId || kdtId,
        };
        if (!isMultiOnlineShop) {
          this.fetchLiveInfo();
          this.getNoteDetailData(queryData).then((res) => {
            resolve(res);
          });
          // 如果是总店则监听进店事件
        } else if (isRootShop) {
          app.once('app:chainstore:kdtid:update', ({ kdtId }) => {
            this.fetchLiveInfo();
            this.getNoteDetailData({
              ...queryData,
              targetKdtId: kdtId,
              isChain: true,
            }).then((res) => {
              resolve(res);
            });
          });
        } else {
          this.fetchLiveInfo();
          this.getNoteDetailData({
            ...queryData,
          }).then((res) => {
            resolve(res);
          });
        }
      });
    });
  },

  getNoteDetailData({ noteAlias, sourceKdtId, targetKdtId, isChain }) {
    return app
      .request({
        path: '/wscshop/shopnote/detail.json',
        data: {
          noteAlias,
          sourceKdtId,
          targetKdtId,
          isChain,
          adaptorComponents: adaptorComponents.join(','),
        },
      })
      .then((data) => {
        if (data?.requestData?.length === 0) {
          Toast({
            message: '店铺笔记已失效',
            duration: 1000,
          });
          setTimeout(() => {
            // eslint-disable-next-line @youzan/dmc/wx-check
            wx.switchTab({
              url: '/pages/home/<USER>/index',
            });
          }, 1000);
        } else {
          this.trigger('noteDetailData', { ...data, noteAlias });
          return data;
        }
      })
      .catch((res) => {
        if (res && res.code === 302) {
          const { data } = res;

          let { location = '' } = data || {};
          if (location && /^(\/?)pages|packages/.test(location)) {
            if (location.slice(0, 1) !== '/') {
              location = '/' + location;
            }
            location = Args.add(location, this.options);

            return wx.redirectTo({ url: location });
          }
        }
      });
  },

  initNoteData(res) {
    if (res) {
      if (res.isLocked) {
        return wx.redirectTo({ url: '/packages/common/lock/index' });
      }
      if (res.noteData) {
        const { noteData } = res;
        this.setYZData({
          noteId: noteData.noteId,
        });
        this.trigger('shopNoteDetail:loaded', res.noteData.noteId);
        const { noteGoodIds } = res;
        let featureComponents = res.featureComponents || [];
        featureComponents = featureComponents.map((item) => {
          if (item.type === 'rich_text' || item.type === 'rich_text_xss') {
            item.externalStyle = 'padding: 0 15px';
          }
          return item;
        });
        featureComponents = addParamPreventCouponLeak(
          featureComponents,
          FeaturePageType.SHOP_NOTE,
          noteData.noteId
        );
        const sharePhoto =
          noteData.headPhoto ||
          get(noteData, 'coverPhotos[0]') ||
          get(noteData, 'noteItemsBriefInfo[0].imageUrl');
        // 选取渲染相关的笔记属性
        const simpleNoteData = {
          title: noteData.title,
          showBrowseCount: noteData.showBrowseCount,
          showThumbsUp: noteData.showThumbsUpCount,
          thumbsUpCount: noteData.thumbsUpCount || 0,
          browseCount: noteData.browseCount || 0,
          publishTime: noteData.publishTime,
          showPublishTime: noteData.showPublishTime,
          templateId: noteData.templateId,
          showEnterShop: noteData.showEnterShop,
          noteId: noteData.noteId,
          noteItemsBriefInfo: noteData.noteItemsBriefInfo,
          sharePhoto,
          featureComponents,
        };
        // 掌柜说 店铺圈 单品分析模版发布时间展示在底部
        if (
          (noteData.templateId === SHOPNOTE_TPL.KEEPER_TALK ||
            noteData.templateId === SHOPNOTE_TPL.SHOP_CIRCLE ||
            noteData.templateId === SHOPNOTE_TPL.SINGLE_PRODUCTION) &&
          noteData.showPublishTime
        ) {
          simpleNoteData.showBottomTime = true;
        }
        if (noteData.templateId === SHOPNOTE_TPL.NEW_ARRIVAL) {
          simpleNoteData.headPhoto = noteData.headPhoto;
          simpleNoteData.description = noteData.description;
          this.setYZData({
            noteData: simpleNoteData,
            noteGoodIds,
            thumbsStatus: noteData.userThumbsStatus || 0,
          });
        } else if (noteData.templateId === SHOPNOTE_TPL.KEEPER_TALK) {
          this.setYZData({
            noteData: simpleNoteData,
            thumbsStatus: noteData.userThumbsStatus || 0,
          });
          this.showShowcaseComponents(featureComponents, 3);
        } else if (noteData.templateId === SHOPNOTE_TPL.SHOP_CIRCLE) {
          simpleNoteData.description = noteData.description;
          this.setYZData({
            noteData: simpleNoteData,
            noteGoodIds,
            thumbsStatus: noteData.userThumbsStatus || 0,
          });
        } else if (noteData.templateId === SHOPNOTE_TPL.SINGLE_PRODUCTION) {
          simpleNoteData.description = noteData.description;
          simpleNoteData.highLights = noteData.highLights;
          this.setYZData({
            noteData: simpleNoteData,
            thumbsStatus: noteData.userThumbsStatus || 0,
            noteGoodIds,
          });
          this.showShowcaseComponents(featureComponents, 3);
        }
        // 店铺笔记详情埋点
        app.logger.log({
          et: 'custom',
          ei: 'shopnote',
          en: '店铺笔记',
          params: {
            note_id: noteData.noteId,
            note_type: noteData.noteTemplateType,
            note_model_id: noteData.templateId,
            spm: `shopnote.${noteData.noteId}`,
          },
        });
      }
    }
  },

  // 获取分销员信息
  fetchSalesmanBaseData() {
    app.carmen({
      api: 'youzan.salesman.wap.account/1.0.0/get',
      success: (res) => {
        this.setYZData({
          'salesman.openSalesman': res.openSalesman,
          'salesman.icon': res.settingIcon,
          'salesman.name': res.settingName,
          'salesman.isSalesman': res.salesman,
          'salesman.alias': res.sls,
        });
      },
    });
  },

  onLoad(query) {
    this.initDesign();
    initSalesman.call(this, {
      sst: 10,
      gst: EShoppingGuideBindSourceType.TINY_PAGE,
    });
    app.getShopInfo().then((shopRes) => {
      const { shopMetaInfo = {} } = shopRes || {};
      if (shopMetaInfo.lock_status === SHOP_LOCK_STATUS.LOCK) {
        return wx.redirectTo({ url: '/packages/common/lock/index' });
      }
      const { noteAlias } = query || {};
      if (noteAlias) {
        this.fetchSalesmanBaseData();
        this.fetchShopSubscribeSetting();
        this.fetchNoteData(noteAlias)
          .then((res) => {
            this.initNoteData(res);
            this.setYZData({ noteAlias: res });
          })
          .catch(() => {
            Toast('页面参数错误');
          });
      } else {
        Toast('页面参数错误');
      }
    });
  },

  onShow() {
    const { noteId = '' } = this.data;
    if (noteId) {
      this.trigger('shopNoteDetail:show', noteId);
    }
  },

  initDesign() {
    const { design } = yunDesign;
    const index = design.findIndex((v) => v.type === 'config');
    this.setData({
      design: index === 0 ? design.slice(1) : design,
    });
  },

  onSalesmanShareCard() {
    try {
      this.selectComponent('#share-goods').drawSalemanCard();
    } catch (e) {
      console.log(e.message);
    }
  },
  fetchShopSubscribeSetting() {
    querySubscriptionInfo().then((res) => {
      if (res.status === 1 && res.weappPush === 1) {
        this.setYZData({
          showSubBtn: true,
          showNav: true,
        });
        queryShopNoteSubscibeSetting().then((res) => {
          if (res.value) {
            this.setYZData({
              subscribeStatus: true,
            });
          }
        });
      }
    });
  },
  closeGuideDialog() {
    this.setYZData({
      guideShow: false,
    });
  },
  editShopNoteSubscription() {
    const { subscribeStatus } = this.data;
    const flag = wx.getStorageSync('hasEnteredPage');
    editShopNoteSubscription({
      channel: 3,
      status: subscribeStatus ? 0 : 1,
    }).then((res) => {
      if (res.value) {
        if (!subscribeStatus && !flag) {
          wx.setStorageSync('hasEnteredPage', true);
        } else {
          wx.showToast({
            title: `${subscribeStatus ? '取消订阅成功' : '订阅成功'}`,
            icon: 'none',
          });
        }
        this.setYZData({
          subscribeStatus: Number(!subscribeStatus),
        });
      }
    });
  },

  handleEnterLiveRoom() {
    const { liveInfo = {} } = this.data;
    wx.navigateTo({
      url: `/packages/weapp-live/room/index?id=${liveInfo.roomId}`,
    });
  },

  handleSubscribe() {
    const { subscribeStatus } = this.data;
    const flag = wx.getStorageSync('hasEnteredPage');
    if (subscribeStatus) {
      Dialog.confirm({
        title: '取消订阅将无法及时了解到商家的资讯信息和优惠活动',
        confirmButtonText: '取消订阅',
        cancelButtonText: '继续订阅',
      }).then(this.editShopNoteSubscription);
    } else {
      // 第一次进入页面订阅 弹出引导框
      if (!flag && !subscribeStatus) {
        this.setYZData({
          guideShow: true,
        });
      }
      this.editShopNoteSubscription();
    }
  },
  thumbClick() {
    if (this.data.clickThumbFlag === false) return;
    this.setYZData({
      clickThumbFlag: false,
    });
    const params = {
      thumbsStatus: this.data.thumbsStatus ? 0 : 1,
      shopNoteId: this.data.noteData.noteId,
    };
    setThumbStatus(params)
      .then((res) => {
        this.setYZData({
          clickThumbFlag: true,
        });
        if (res) {
          const curThumbsStatus = this.data.thumbsStatus ? 0 : 1;
          this.setYZData({
            thumbsStatus: curThumbsStatus,
          });
        }
      })
      .catch((err) => {
        console.log(err);
        this.setYZData({
          clickThumbFlag: true,
        });
      });
  },
  showcaseHandleGoodsBuy(e) {
    navigate.navigate({
      url: e.detail.url,
    });
  },
  enterShop() {
    navigate.switchTab({ url: '/packages/home/<USER>/index' });
  },

  clickShareLog() {
    // banner_id
    const params = {
      banner_id: getBannerId('share'),
    };
    // 获取埋点格式
    const loggerMsg = getComponentLoggerParams('click_content', params);
    // 上报
    ensureAppLogger(loggerMsg);
  },

  onShareAppMessage() {
    const { noteAlias } = this.__query__;
    const { title, description } = this.data.noteData || {};
    const shopName = get(this.data, 'CURRENT_GLOBAL_SHOP.shop_name', '');
    const desc = description || `${shopName}的精彩内容分享给你，快来看一看吧`;
    const data = this.data.noteData;
    let cover = get(data, 'sharePhoto');
    const shareUrl = Args.add('packages/shop/shopnote/detail/index', {
      noteAlias,
      is_share: '1',
      ...defaultEnterShopOptions,
    });
    const isHttp = (url) => /^(http|https):\/\//.test(url);
    cover = isHttp(cover) ? cover : `https:${cover}`;
    // 分享
    this.clickShareLog();
    if (this.data.yunShareData) {
      return this.data.yunShareData;
    }
    return {
      title,
      desc,
      path: shareUrl,
      imageUrl: cover,
    };
  },

  handleSkuClose() {
    this.setYZData({
      'featureSkuData.showGoodsSku': false,
    });
  },

  onUnload() {
    this.off('shopNoteDetail:loaded');
    this.off('shopNoteDetail:show');
  },

  // 获取直播信息
  async fetchLiveInfo() {
    const kdtId = app.getKdtId();
    const result = await fetchLiveInfoApi(kdtId);
    this.setYZData({
      liveInfo: {
        hasLiving: result.hasLiving,
        roomId: result.roomId,
      },
    });
    return result;
  },

  redirectToHome() {
    navigate.switchTab({ url: '/packages/home/<USER>/index' });
  },
});
