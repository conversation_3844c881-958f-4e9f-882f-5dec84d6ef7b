@import '/components/showcase/index.wxss';
.nav-container {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 17px;
  position: fixed;
  z-index: 9999;
  height: 60px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.03);
}
.note-detail-wrapper {
  margin-top: 72px;
  background: #fff;
}
.nav-enter-shop {
  width: 40px;
  height: 40px;
}
.nav-enter-name {
  margin-left: 10px;
  font-size: 14px;
  color: #333333;
  line-height: 20px;
}
.nav-enter-right {
  display: flex;
  margin-left: auto;
}
.nav-sub-btn, .nav-en-shop {
  border-radius: 13px;
  border: 1px solid #f44;
  border-radius: 13px;
  font-size: 14px;
  color: #333333;
  line-height: 26px;
  height: 26px;
  margin-right: 15px;
  color: #f44;
  padding: 0 15px;
}
.nav-enter-right__gray {
  border: 1px solid #999;
  border-radius: 13px;
  height: 26px;
  line-height: 26px;
  color: #999;
}
.nav-enter-shop image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.shopnote-time-content .shopnote-time {
  margin-right: 15px;
}

.shopnote-time-content .shopnote-readdata {
  margin-top: 0px;
}
.nav-van-popup {
  background: transparent !important;
}
.nav-guide-confirm__btn {
  background: transparent;
  text-align: center;
  width: 140px;
  margin-top: 30px;
  font-family: PingFangSC-Regular;
  font-size: 15px;
  color: #FFFFFF;
  border: 1px solid #FFFFFF;
  border-radius: 17.5px;
}

.live-entry-icon {
  position: fixed;
  top: 40px;
  right: 10px;
  z-index: 140;
}