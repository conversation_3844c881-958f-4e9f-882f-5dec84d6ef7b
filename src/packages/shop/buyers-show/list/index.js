import WscPage from 'pages/common/wsc-page/index';
import Args from '@youzan/weapp-utils/lib/args';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';

WscPage({
  data: {
    src: '',
    alias: '',
    evaluationAlias: ''
  },

  onLoad(options) {
    const { alias, evaluation_alias: evaluationAlias = '', sl = '' } = options;
    let extraParams = {};
    if (evaluationAlias) {
      extraParams.evaluation_alias = evaluationAlias;
    }

    if (sl) {
      extraParams = {
        ...extraParams,
        ...getSalesmanParamsObject({ sl })
      };
    }

    const src = Args.add(
      `https://h5.youzan.com/wscshop/buyers/show/list/${alias}`,
      extraParams
    );
    this.setYZData({
      src,
      alias,
      evaluationAlias,
    });
  },

  onMessage(event) {
    const messages = event.detail.data;
    const shareMessages = messages.filter(message => message.type === 'ZNB.share');
    this.shareConfig = shareMessages.pop().config;
  },

  onShareAppMessage() {
    const { title = '买家秀', link = '', imageUrl } = this.shareConfig;
    const queryParams = Args.getAll(link) || {};
    const sl = queryParams.sl || '';
    const evaluationAlias = queryParams.evaluation_alias || '';
    const { alias } = this.data;

    let extraParams = {};
    if (sl) {
      extraParams = {
        ...extraParams,
        ...getSalesmanParamsObject({ sl })
      };
    }

    if (evaluationAlias) {
      extraParams.evaluation_alias = evaluationAlias;
    }

    const shareUrl = Args.add('/packages/shop/buyers-show/list/index', {
      alias,
      ...defaultEnterShopOptions,
      ...extraParams,
    });

    return {
      imageUrl,
      title,
      path: shareUrl
    };
  }
});
