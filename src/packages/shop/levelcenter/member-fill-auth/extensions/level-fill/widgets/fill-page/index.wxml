<view class="wx-login">
  <user-authorize
    wx:if="{{ ready }}"
    authTypeList="{{ ['mobile'] }}"
    bind:next="afterAuthorize"
    bizDataMap="{{bizDataMap}}"
  >
    <view class="container">
      <view
        wx:if="{{!isLogoHeader}}"
        class="card-block"
        style="height: calc(var(--card-width) / (343 / 180)); width: var(--card-width);"
      >
        <image
          wx:if="{{coverUrl}}"
          src="{{coverUrl}}"
          class="image"
        ></image>
        <view
          class="level-bg-mask"
          style="{{bgStyle}}"
          wx:else
        ></view>
        <view class="level-name">{{ levelName }}</view>
      </view>
      <view class='logo-block' wx:else>
        <image
          wx:if="{{shopInfo.shopLogo}}"
          src="{{shopInfo.shopLogo}}"
          class="logo"
        />
        <view class='title'>欢迎加入{{shopInfo.shopName}}</view>
        <view class='sub-title'>完善资料，立即入会</view>
      </view>
      <view class="content-wrapper-container">
        <view wx:if="{{!isLogoHeader}}" class="benefit-block">
          <view class="benefit-container ">
            <view class="benefit-desc">
              <span class='be-member'>成为会员</span>
              <view
                wx:for="{{ benefitRenderArr}}"
                wx:for-item="benefit"
                wx:for-index="index"
              >
                <span>
                  ，{{ benefit.prefix }}
                  <span wx:if="benefit.content" class="highlight">{{benefit.content}}</span>
                  {{ benefit.suffix }}
                </span>
              </view>
            </view>
          </view>
        </view>
        <view class="fill-customer-info">
          <view
            class="cell-group"
            wx:for="{{attributeList}}"
            wx:key="title"
          >
            <view class="base-info-cell">
              <view class="cell-title">{{ item.title }}</view>
              <image
                class="avatar"
                wx:if="{{ item.isAvatar }}"
                src="{{ item.value || '//b.yzcdn.cn/showcase/membercenter/2018/08/06/<EMAIL>'}}"
              />
              <view wx:else class="cell-value {{item.isRealValue?'real-value':''}}">
                {{ item.value }}
                <view class="btn" wx:if="{{item.isMobile}}">一键授权</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="footer-wrapper">
        <view class="footer-btn">提交</view>
      </view>
    </view>
  </user-authorize>
</view>

