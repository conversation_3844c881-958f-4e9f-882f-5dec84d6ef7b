page {
  --card-width: calc(100vw - 48px);
}

.wx-login {
  background: #f8f8f8;
  font-size: 14px;
}

.wx-login .container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.card-block {
  margin: 0 auto;
  padding-top: 24px;
  display: flex;
  position: relative;
  border-radius: 12px;
}

.logo-block {
  padding-top: 30px;
  margin-bottom: 68px;
  height: 208px;
  color: #333333;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  margin-top: 12px;
  margin-bottom: 4px;
  font-size: 20px;
  font-weight: 500;
}

.sub-title {
  color: #999;
}

.logo {
  width: 66px;
  height: 66px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  border-radius: 50%;
}

.level-bg-mask,
.image {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 12px;
}

.level-name {
  position: absolute;
  margin: 20px 24px;
  left: 0;
  font-size: 18px;
  color: #fff;
  font-weight: bold;
}

.content-wrapper-container {
  margin-top: -102px;
  border-radius: 8px;
  z-index: 1;
  height: 100vh;
  background-color: #fff;
}

.benefit-block {
  height: 76px;
  background: linear-gradient(to bottom, #fef9ed, #fff);
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
}

.benefit-container {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border-radius: 4px;
  text-align: center;
  line-height: 26px;
  height: 26px;
}

.benefit-desc {
  display: inline-flex;
  color: #9a640c;
  font-size: 16px;
  font-weight: bold;
}

.be-member {
  line-height: 28px;
}

.highlight {
  display: inline;
  color: #f04d35;
  font-size: 22px;
  font-weight: bold;
  margin: 0 4px;
}

.base-info-cell {
  width: 100%;
  height: 60px;
  line-height: 60px;
  border-bottom: 0.6px solid #ebedf0;
  display: flex;
}
.fill-customer-info {
  background-color: #fff;
  margin: 0 24px 0 24px;
}

.cell-group {
  background-color: #fff;
}

.cell-title {
  width: 100px;
  margin-right: 20px;
  color: #323233;
  text-align: left;
}

.cell-value {
  color: #cecece;
}

.real-value {
  color: #323233;
}

.btn {
  height: 26px;
  width: 72px;
  line-height: 26px;
  color: #323233;
  border-radius: 4px;
  border: 1px solid #969799;
  position: absolute;
  right: 24px;
  margin-top: -45px;
  text-align: center;
  font-size: 12px;
}

.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
  display: inline-block;
  vertical-align: center;
  margin-top: 30rpx;
}

.footer-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  padding: 0 16px 21px;
  background-color: #fff;
}

.footer-wrapper .footer-btn {
  height: 44px;
  line-height: 44px;
  background: #e7c5a5;
  color: #724804;
  margin-top: 7px;
  border-radius: 18px;
  text-align: center;
  font-weight: bold;
}
