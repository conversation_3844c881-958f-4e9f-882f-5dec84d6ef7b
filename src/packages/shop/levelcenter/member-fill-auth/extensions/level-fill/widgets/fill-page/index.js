/* eslint @youzan-open/tee/valid-same-extension-import: "off" */
import accDiv from '@youzan/utils/number/accDiv';
import fullfillImage from '@youzan/utils/url/fullfillImage';
import { defaultColorCode, getLevelBgInfo } from '@youzan/scrm-common';

import { RantaWidget } from 'shared/common/base/ranta/widget';
import openWebView from 'shared/utils/open-web-view';

import { LevelType } from '../../../../../level-common/utils/constant';

const app = getApp();

const AttributeDataTypeEnum = {
  Text: 0,
  Number: 1,
  Date: 2,
  Province: 3,
  Gender: 4,
  Image: 5,
  SingleSelect: 7,
  MultipleSelect: 8,
};

const SEX_ENUM = {
  1: '男',
  2: '女',
  0: '未知',
};

// 注册会员页头部内容类型
const HEADER_ENUM = {
  CARD: 1,
  LOGO: 2,
};

const MOBILE_ATTRIBUTE_ID = 3;

RantaWidget({
  data: {
    ready: false,
    coverUrl: '',
    levelBg: '',
    levelName: '',
    couponAmount: 0,
    benefitAmount: 0,
    attributeList: [],
    isLogoHeader: false,
    shopInfo: {},
    benefitRenderArr: [],
    bgStyle: '',
    bizDataMap: {},
  },

  attached() {
    this.ctx.env
      .getQueryAsync()
      .then(({ alias, gotoParam, bizDataMap, kdt_id }) => {
        this.gotoParam = gotoParam;

        if (bizDataMap && typeof bizDataMap === 'string') {
          try {
            const bizDataMapJSON = JSON.parse(decodeURIComponent(bizDataMap));
            this.setYZData({ bizDataMap: bizDataMapJSON });
          } catch (err) {
            console.error(err);
          }
        }

        kdt_id && app.updateKdtId(kdt_id);

        app
          .request({
            path: '/wscuser/levelcenter/getAttributesAndAvailableLevel.json',
            query: {
              alias: alias || '',
            },
          })
          .then((res) => {
            const {
              availableShowLevel,
              customerAttributeInfoList = [],
              brandPagesSetting = { headType: HEADER_ENUM.CARD },
            } = res;
            const isLogoHeader =
              brandPagesSetting.headType === HEADER_ENUM.LOGO;

            if (isLogoHeader) {
              app.getShopInfo().then((shop) => {
                this.setYZData({
                  shopInfo: {
                    shopLogo: shop.logo,
                    shopName: shop.shop_name,
                  },
                });
              });
            }

            const benefitAmount = availableShowLevel.benefitNum || 0;
            const couponAmount = accDiv(
              availableShowLevel.allCouponPrice || 0,
              100
            );

            const benefitRenderArr = this.getBenefitRenderArr(
              benefitAmount,
              couponAmount
            );
            if (!isLogoHeader && !availableShowLevel.coverUrl) {
              this.setYZData({
                bgStyle: this.getLevelStyle(
                  availableShowLevel.colorCode || defaultColorCode
                ),
              });
            }

            this.setYZData({
              ready: true,
              coverUrl: fullfillImage(availableShowLevel.coverUrl, 'middle'),
              levelName: availableShowLevel.levelName,
              benefitAmount,
              couponAmount,
              attributeList: this.formatAttributeList(
                customerAttributeInfoList
              ),
              isLogoHeader,
              benefitRenderArr,
            });
          });
      });
  },

  detached() {
    const kdtId = app.getKdtId();
    const HQKdtId = app.getHQKdtId();
    if (kdtId !== HQKdtId) {
      app.updateKdtId(HQKdtId, true);
    }
  },

  methods: {
    afterAuthorize() {
      const { gotoParam } = this;
      const HQKdtId = app.getHQKdtId();
      app.updateKdtId(HQKdtId, true).finally(() => {
        // eslint-disable-next-line @youzan/dmc/wx-check -- 使用原生 back 方法因为要获取 back 回调
        /* eslint-disable */
        wx.navigateBack({
          fail: () => {
            // * 堆栈丢失的情况下（如进店重定向/直接进入该页面）仍然需要 redirect
            openWebView(
              `https://h5.youzan.com/wscuser/levelcenter/fill?skipMobileLogin=1&${decodeURIComponent(
                gotoParam
              )}`,
              {
                method: 'redirectTo',
                title: '注册会员',
              }
            );
          },
        });
        /* eslint-enable */
      });
    },

    getLevelStyle(colorCode) {
      const levelStyle = getLevelBgInfo({
        colorCode,
        levelType: LevelType.FREE,
      });

      return `background-size: cover; background-position: center;background:${levelStyle.backgroundStyle}`;
    },

    formatAttributeList(attrList) {
      if (!attrList || !attrList.length) {
        return [];
      }
      return attrList.map((item) => {
        const realValue = this.renderValue(item);

        const prefix = `${item.sysValues.length > 0 ? '请选择' : '请填写'}`;
        const suffix = `${item.isRequired ? '（必填）' : ''}`;
        return {
          title: item.name,
          value: realValue || `${prefix}${suffix}`,
          isRealValue: !!realValue,
          isAvatar: item.dataType === AttributeDataTypeEnum.Image,
          // 后端目前不支持通过 dataType 判断是否是手机号，只能通过 attributeId 判断
          isMobile: item.attributeId === MOBILE_ATTRIBUTE_ID,
        };
      });
    },

    renderValue(item) {
      const { dataType, value = '', extValue, sysValues } = item;
      // 对性别特殊处理
      if (dataType === AttributeDataTypeEnum.Gender) {
        // @ts-ignore
        return SEX_ENUM[value] || '';
      }
      if (dataType === AttributeDataTypeEnum.Province) {
        // 地区特殊处理
        return extValue || '';
      }
      if (dataType === AttributeDataTypeEnum.MultipleSelect) {
        // 从后端的数据是attributeId的字符串，需要解析下
        const tempValue = value
          .split('、')
          .filter((item) => !!item)
          .map((item) => Number(item));
        const formattedValue = sysValues
          .filter((item) => tempValue.indexOf(item.id) > -1)
          .map((item) => item.value);
        return formattedValue.join('、') || '';
      }
      if (dataType === AttributeDataTypeEnum.SingleSelect) {
        const formattedValue = sysValues
          .filter((item) => item.id === Number(value))
          .map((item) => item.value);
        return formattedValue.join('、') || '';
      }

      return value;
    },

    getBenefitRenderArr(benefitAmount, couponAmount) {
      const benefitRenderConfig = {
        coupon: {
          prefix: '领',
          content: couponAmount,
          suffix: '元优惠券',
        },
        benefit: {
          prefix: '享',
          content: benefitAmount,
          suffix: '大权益',
        },
        empty: {
          prefix: '享',
          suffix: '会员权益',
        },
      };

      const benefitRenderArr = [];

      if (couponAmount > 0 || benefitAmount > 0) {
        if (couponAmount > 0) {
          benefitRenderArr.push(benefitRenderConfig.coupon);
        }

        if (benefitAmount > 0) {
          benefitRenderArr.push(benefitRenderConfig.benefit);
        }
      } else {
        benefitRenderArr.push(benefitRenderConfig.empty);
      }

      return benefitRenderArr;
    },
  },
});
