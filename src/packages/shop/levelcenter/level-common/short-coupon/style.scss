.coupon-box {
  min-width: 112px;
  height: 116px;
  border-radius: 4px;
  background-image: linear-gradient(to bottom right, #fff6eb, #fff0db);
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  color: #b66f0f;
  font-size: 12px;
  line-height: 16px;

  .left-icon {
    left: 0;
    bottom: 28px;
    width: 6px;
    height: 12px;
    border-radius: 0 12px 12px 0;
    background-color: #fff;
    position: absolute;
  }

  .right-icon {
    right: 0;
    bottom: 28px;
    width: 6px;
    height: 12px;
    border-radius: 12px 0 0 12px;
    background-color: #fff;
    position: absolute;
  }

  .member-title {
    position: absolute;
    top: 0;
    left: 0;
    height: 14px;
    padding: 0 4px;
    background-color: rgba(237, 106, 12, 0.16);
    font-size: 10px;
    color: #ed6a0c;
    border-top-left-radius: 4px;
    border-bottom-right-radius: 4px;
    line-height: 14px;
    padding-top: 1px;
    box-sizing: border-box;
    max-width: 108px;
  }

  .text-overfollow {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .coupon {
    margin-top: 16px;
    .account {
      font-size: 26px;
      line-height: 34px;
      font-weight: bold;
    }
  }
  .use-condition {
    max-width: 90px;
  }

  .dotted-line {
    position: absolute;
    width: 92px;
    bottom: 34px;
    left: 50%;
    transform: translateX(-50%);
    height: 1px;
    background-image: linear-gradient(
      to right,
      rgba(182, 111, 15, 0.2) 0%,
      rgba(182, 111, 15, 0.2) 50%,
      transparent 0%
    );
    background-size: 8px 1px;
    background-repeat: repeat-x;
  }

  .btn-text {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    color: #b66f0f;
    font-size: 12px;
    line-height: 18px;
    font-weight: bold;
  }
}

.disable-coupon {
  background-color: #f7f8fa;
  background-image: inherit;
  color: #969799;

  .member-title {
    background-color: #ebedf0;
    color: #969799;
  }

  .dotted-line {
    background-image: linear-gradient(
      to right,
      rgba(150, 151, 153, 0.2) 0%,
      rgba(150, 151, 153, 0.2) 50%,
      transparent 0%
    );
  }

  .btn-text {
    color: #969799;
  }
}