import WscComponent from 'pages/common/wsc-component/index';
import { MEMBER_TYPE_ENUM } from '../utils/constant';

WscComponent({
  properties: {
    coupon: {
      type: Object,
    },
    levelType: {
      type: String,
      default: MEMBER_TYPE_ENUM.PAY_MENBER,
    },
  },
  data: {
    unitDesc: '',
    coupon: {},
    disable: false,
    btnText: '',
    fontSizeStyle: '',
    MEMBER_TYPE_ENUM,
  },
  methods: {
    receiveCoupon() {
      if (!this.data.disable) {
        this.triggerEvent('receiveCoupon', this.data.coupon);
      }
    },
  },
  observers: {
    coupon(val) {
      const { received, left, status, valueDesc, unitDesc } = val;
      const disable = status !== 0 || received || left === 0;

      let btnText = '立即领取';
      if (received) btnText = '已领取';
      if (status !== 0 || left === 0) btnText = '已抢光';

      // 优惠券面额的字体大小
      const { length } = `${valueDesc || ''}`;
      let size = 26;
      if (length >= 8) size = 12;
      else if (length >= 6) size = 16;
      else if (length >= 4) size = 20;
      const fontSizeStyle = `font-size: ${size}px`;

      this.setYZData({
        coupon: val,
        unitDesc: unitDesc || '',
        disable,
        btnText,
        fontSizeStyle,
      });
    },
  },
});
