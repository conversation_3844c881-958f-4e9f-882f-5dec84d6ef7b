import WscComponent from 'pages/common/wsc-component/index';
import { checkBirthdayStatus, getBenefitFormatSwitch } from '../utils/api';
import get from '@youzan/weapp-utils/lib/get';
import args from '@youzan/weapp-utils/lib/args';

const app = getApp();

WscComponent({
  properties: {
    userLevel: Object,
    birthdayPosIndex: Number,
  },
  data: {
    nickName: '',
    showTip: false,
    kdtId: app.getKdtId(),
    // 使用新版本权益详情页
    useNewVer: false,
  },
  methods: {
    toBenefitLink() {
      const { userLevel, birthdayPosIndex } = this.properties;
      const nowLevelAlias = userLevel?.level?.levelAlias;

      const weappUrl = args.add('/packages/levelcenter/benefit/index', {
        kdt_id: this.data.kdtId,
        alias: nowLevel<PERSON>lias,
        benefit: birthdayPosIndex,
        benefit_format: this.data.useNewVer ? '1' : '',
      });
      wx.navigateTo({
        url: weappUrl,
      });
    },
    checkBirthdayStatus() {
      const { userLevel } = this.properties;
      const levelType = userLevel?.level?.levelType;
      checkBirthdayStatus({ levelType }).then((res) => {
        this.setYZData({
          showTip: res?.inRange || false,
        });
      });
    },
    getBenefitFormatSwitch() {
      getBenefitFormatSwitch().then((res) => {
        this.setYZData({
          useNewVer: res?.value || false,
        });
      });
    },
  },
  attached() {
    app.getUserInfo((res) => {
      const nickName = get(res, 'userInfo.nickName');
      this.setYZData({
        nickName,
      });
    });
    this.checkBirthdayStatus();
    this.getBenefitFormatSwitch();
  },
});
