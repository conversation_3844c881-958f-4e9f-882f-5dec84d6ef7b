<view class="mission-item">
  <image src="{{ mission.icon }}" class="mission-icon" />

  <view class="mission-info">
    <text class="mission-info__title">{{ mission.subTitle }}</text>
    <text class="mission-info__desc">{{ mission.desc }}</text>
  </view>

    <user-authorize
      wx:if="{{ mission.name === 'BINDPHONE' && !mission.finished }}"
      authTypeList="{{ ['mobile'] }}"
      bind:next="bindGetPhoneNumber"
      bizDataMap="{{ bizDataMap }}"
    >
      <button class="mission-action">去完成</button>
    </user-authorize>

  <view 
    wx:if="{{ mission.name !== 'BINDPHONE' || mission.finished}}"
    class="{{ 'mission-action ' + (mission.finished ? 'mission-action__finished' : '') }}"
    bindtap="handleClickDoMission"
    >
    {{ mission.finished ? '已完成' : '去完成' }}
  </view>
</view>