<view wx:if='{{ isMsgOperationOpen }}' class="{{ showPayPlusExtraCls ? 'pay-plus-extra-cls' : ''}} {{ isIphoneX ? 'is-iphone-x' : 'is-not-x' }}">
  <view class="{{ hasButton ? 'has-btn' : 'no-btn' }}">
    <view  class="subscribe-box  {{ !hideMsgOperation ? 'to-show' : 'to-hide' }}">
      <view class="subscribe-box_body {{ hasTabbar ? 'box-bottom' : '' }}">
        <view
          class="subscribe-box_btn" 
          style="background-image: url({{ bgUrl }}); background-size: cover; backgroud-repeat: no-repeat;"
        >
          <view class="subscribe-box_confirm" bindtap="openReminder">
              <image class="icon" src="{{ iconUrl }}" />
              <view class='tips' >订阅更多特权通知</view>
          </view>
          <view class="subscribe-box_cancel" bindtap="closeReminderPopup">
            <van-icon name="cross" color="#945623" size="12" />
          </view>
        </view>
      </view>
      <subscribe-fail-popup show="{{ showSubscirbeFail }}" openImageSrc="https://img.yzcdn.cn/public_files/2019/12/23/7b987495fc6bbd066262153577881592.png" bind:close="closeSubscribeFailPopup"></subscribe-fail-popup>
    </view>
  </view>
</view>

