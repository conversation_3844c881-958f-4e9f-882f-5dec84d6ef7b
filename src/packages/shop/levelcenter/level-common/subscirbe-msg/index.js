import Toast from '@vant/weapp/dist/toast/toast';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import WscComponent from 'pages/common/wsc-component/index';
import {
  subscribeMessage,
  isSubscriptionMsgAvailable,
  isAuth4Push,
} from '@/helpers/subscribe.js';
import { isNewIphone } from 'shared/utils/browser/device-type';

const isIphoneX = isNewIphone();
const app = getApp();

const SCENE = 'cimWeMiniProMsg';

WscComponent({
  properties: {
    hasButton: Boolean,
    hasTabbar: Boolean,
    kdtId: String,
    showPayPlusExtraCls: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    isMsgOperationOpen: false, // 当前的小程序推送消息的状态
    hideMsgOperation: false,
    showSubscirbeFail: false, // 小程序订阅消息失败引导弹窗
    isIphoneX,
    bgUrl: cdnImage(
      'https://b.yzcdn.cn/public_files/99f21f7f10069ab3bfb9f4567d2bbd2c.png',
      '!160x160.jpg'
    ),
    iconUrl: cdnImage(
      'https://b.yzcdn.cn/public_files/c7b1ea7d3023a0047bb3f7df768643f0.png',
      '!12x16.jpg'
    ),
  },

  methods: {
    // 获取推送提醒状态
    getCheckinRemind() {
      this.fetchSubscribeStatus()
        .then((subscribeStatus) => {
          const { hasSubscribeAbility = false, isAuthPush = true } =
            subscribeStatus || {};
          // 有订阅消息能力，并没有订阅当前通知
          const needMsgOpen = hasSubscribeAbility && !isAuthPush;
          if (needMsgOpen) {
            const timer = setTimeout(() => {
              // 3秒后浮出
              this.setYZData({
                isMsgOperationOpen: true,
              });
              this.addLog({
                et: 'view', // 事件类型
                ei: 'memb_subscribe', // 事件标识
                en: '会员中心订阅按钮曝光', // 事件名称
              });
              clearTimeout(timer);
            }, 3000);
            // 30秒后消失
            const clear = setTimeout(() => {
              this.setYZData({
                hideMsgOperation: true,
              });
              clearTimeout(clear);
            }, 33000);
          } else {
            this.setYZData({
              isMsgOperationOpen: false,
            });
          }
        })
        .catch(() => {
          this.setYZData({
            isMsgOperationOpen: false,
          });
        });
    },

    fetchSubscribeStatus() {
      return new Promise((resolve) => {
        isSubscriptionMsgAvailable()
          .then((hasSubscribeAbility) => {
            if (hasSubscribeAbility) {
              isAuth4Push({ scene: SCENE })
                .then((isAuthPush) => {
                  resolve({
                    hasSubscribeAbility,
                    isAuthPush,
                  });
                })
                .catch(() => Toast('授权状态获取失败'));
            } else {
              resolve({
                hasSubscribeAbility,
              });
            }
          })
          .catch(() => Toast('授权能力获取失败'));
      });
    },

    // 开启活动推送提醒
    openReminder() {
      const self = this;
      this.addLog({
        et: 'click', // 事件类型
        ei: 'memb_subscribe_click', // 事件标识
        en: '会员中心订阅按钮点击', // 事件名称
      });
      subscribeMessage({
        scene: SCENE,
        needAlwaysToast: false,
        successCallBack: () => {
          // 订阅成功后，更新状态
          self.setYZData({
            isMsgOperationOpen: false,
          });
        },
        failCallBack: () => {
          // 失败则弹出失败弹窗
          self.setYZData({
            showSubscirbeFail: true,
          });
        },
      });
    },

    closeReminderPopup() {
      this.setYZData({
        isMsgOperationOpen: false,
      });
    },

    closeSubscribeFailPopup() {
      this.setYZData({
        showSubscirbeFail: false,
      });
    },

    addLog(params) {
      app.logger?.log(params);
    },
  },

  attached() {
    this.getCheckinRemind();
  },
});
