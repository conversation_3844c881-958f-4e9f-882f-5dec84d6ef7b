.subscribe-box {
  z-index: 10;
  position: fixed;
  width: 100%;
  display: flex;
  justify-content: center;
  animation-timing-function: cubic-bezier(0.17, 0.81, 0.56, 0.97);

  &_body {
    height: 40px;
    width: 160px;

    .icon {
      width: 12px;
      height: 15px;
      margin-right: 4px;
    }

    .tips {
      font-size: 13px;
      color: #945623;
      font-weight: 500;
    }

    .subscribe-box_close {
      position: absolute;
      top: -15px;
      right: 9px;
      width: 16px;
      height: 16px;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 8px;
      display: flex;
      justify-content: center;
    }
  }

  &_btn {
    width: 160px;
    height: 40px;
    border-radius: 20px;
    display: flex;
    align-items: center;
  }

  &_confirm {
    display: flex;
    width: 136px;
    border-radius: 20px;
    height: 40px;
    align-items: center;
    justify-content: flex-end;
  }

  &_cancel {
    width: 20px;
    height: 20px;
    padding-left: 4px;
    padding-top: 1px;
  }

  .box-bottom {
    margin-bottom: 60px;
  }
}

@mixin show-pos($bottom, $time, $showname, $hidename) {
  .to-show {
    animation-name: $showname;
    animation-duration: $time;
    bottom: $bottom;
  }
  .to-hide {
    animation-name: $hidename;
    animation-duration: $time;
    bottom: -40px;
  }

  @keyframes #{$showname} {
    from {
      bottom: 0;
    }
    to {
      bottom: $bottom;
    }
  }

  @keyframes #{$hidename} {
    from {
      bottom: $bottom;
    }
    to {
      bottom: -40px;
    }
  }
}

.is-not-x {
  .has-btn {
    @include show-pos(74px, 0.3s, 'btn-show', 'btn-hide');
  }
  .no-btn {
    @include show-pos(24px, 0.24s, 'show', 'hide');
  }
}

.is-iphone-x {
  .has-btn {
    @include show-pos(94px, 0.36s, 'x-btn-show', 'x-btn-hide');
  }
  .no-btn {
    @include show-pos(34px, 0.24s, 'x-show', 'x-hide');
  }
}

.pay-plus-extra-cls {
  &.is-not-x {
    .has-btn {
      @include show-pos(128px, 0.3s, 'btn-show', 'btn-hide');
    }
  }
  &.is-iphone-x {
    .has-btn {
      @include show-pos(168px, 0.36s, 'x-btn-show', 'x-btn-hide');
    }
  }
}
