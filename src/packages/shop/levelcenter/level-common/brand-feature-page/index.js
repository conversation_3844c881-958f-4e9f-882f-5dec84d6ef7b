export default function getPage(initData) {
  return {
    data: {
      isFeatureMemberCenter: false,
      title: '',
      pageBgColor: '#f7f7f7',
      navigationbarConfigData: {
        style_color_type: 'global',
        navigationbar_config_type: 'global',
        special_nav_bg_color: initData.special_nav_bg_color || '#252525',
        special_nav_color: '#fff',
      },
      ...initData,
    },
    setBrandFeatureData(featureData) {
      const isFeatureMemberCenter = !!featureData;
      this.setData({
        isFeatureMemberCenter,
      });
      const { components = [] } = featureData;
      const configData = components.find((i) => i.type === 'config');
      // 设置标题和页面背景色
      if (configData) {
        this.setData({
          title: configData.title || initData.title,
        });
        this._setFeaturePageConfigData(configData);
      }
      const { navigationbarConfigData } = this.data;
      this.setData({
        navigationbarConfigData: {
          ...navigationbarConfigData,
          special_nav_bg_color: '',
          special_nav_color: '',
        },
      });
      this.handleProtocol();
      this.showShowcaseComponents(components, 3);
    },

    getProtocol() {
      return import('@youzan/passport-protocol').then(({ InvokeProtocol }) => ({
        instance: new InvokeProtocol(),
      }));
    },
    handleProtocol() {
      const { isFeatureMemberCenter } = this.data;
      if (isFeatureMemberCenter) {
        this.getProtocol().then(({ instance }) => {
          instance
            .auth()
            .then(() => {
              console.log('已签署协议');
            })
            .catch(() => {});
        });
      }
    },
  };
}
