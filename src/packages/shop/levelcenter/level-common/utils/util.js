import get from '@youzan/weapp-utils/lib/get';
import pick from '@youzan/weapp-utils/lib/pick';

const app = getApp();

// 查找下一个未达到的等级
export const findNextLevel = (cards, nowLevel) => {
  return cards.find((item) => item.card.levelValue > nowLevel.levelValue);
};

export const getRecruitMemberParams = (query) => {
  // 后端字段名定义为大写 @韩维
  const paramsKeys = [
    'NEW_SALES_KDTID',
    'RECRUIT_ENTRANCE',
    'RECRUIT_MEMBER_TYPE',
    'RECRUIT_SRC',
    'from_params',
  ];
  const params = pick(query, paramsKeys);

  if (query.IS_SALES === 'true' && query.NEW_SALES_ID) {
    params.IS_SALES = query.IS_SALES;
    params.NEW_SALES_ID = query.NEW_SALES_ID;
  }
  return params;
};

export const tryToBindShoppingGuideRelation = (query) => {
  const shoppingGuideInfo = getRecruitMemberParams(query);
  if (shoppingGuideInfo.NEW_SALES_ID) {
    const bindParams = {
      bindSources: 'SCRM_CREATED_H5_OR_APPLET', // 会员拉新
      salesId: shoppingGuideInfo.NEW_SALES_ID,
      salesKdtId: shoppingGuideInfo.NEW_SALES_KDTID,
    };

    return getApp()
      .request({
        path: '/wscuser/shopping-guide/api/bindRelation.json',
        method: 'post',
        data: bindParams,
      })
      .catch(() => {});
  }

  return Promise.resolve();
};

export const showInformationDialog = (userLevel, hasCompleted) => {
  const level = get(userLevel, 'level.levelAlias', null);
  return !hasCompleted && level !== null;
};

export const getNeedMoreGrowth = (data, nextCard) => {
  const userGrowth = get(data, 'nowLevel.currentGrowth');
  const userLevel = get(data, 'nowLevel.levelValue', 0);
  const nextLevelGrowth = get(nextCard, 'card.minGrowth');
  return userLevel === 0 && userGrowth < nextLevelGrowth;
};

export const addLog = (params) => {
  app.logger?.log(params);
};

/** 底部按钮展示逻辑 */
export const isShowBottomBtn = (state, payload) => {
  const { neededMoreStoreBalance, hasCompleted, reachThreshold } = payload;
  const { hasLevel, isRemoved } = state;
  // 储值模式下未达到入会门槛
  if (neededMoreStoreBalance) {
    return true;
  }

  if (hasLevel) {
    return false;
  }

  // 是否完成必填信息
  if (!hasCompleted) {
    return true;
  }

  if (!reachThreshold) {
    return false;
  }

  // 手动退会
  if (isRemoved) {
    return true;
  }

  return true;
};

/**
 * 用指定标签标记字符串中的数字部分
 * @param str 原始字符串
 * @param labels 标签数组，第一个元素是开始标签，第二个是结束标签
 * @returns 标记后的字符串
 */
export const markWithLabel = (str, labels) => {
  // 匹配数字（包括小数点和负号）
  const numberRegex = /-?\d+\.?\d*/g;

  return str.replace(numberRegex, (match) => {
    return `${labels[0]}${match}${labels[1]}`;
  });
};
