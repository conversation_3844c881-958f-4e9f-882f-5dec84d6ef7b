import format from '@youzan/utils/money/format';
import accSub from '@youzan/utils/number/accSub';

/**
 * * 将分转化为元并保留指定位数小数
 * @param {number} cent
 * @param {number} precision
 * @returns {number}
 */
function moneyFormat(cent) {
  return Number(format(cent, true, false));
}

// 等级类型
export const LevelType = {
  FREE: 1, // 免费
  PAY: 2, // 付费
};

export const ConsumeMissionType = {
  AMOUNT: 1, // * 消费总金额
  COUNT: 2, // * 消费总次数
  SINGLE: 3, // * 单笔最大消费
};

// * 消费模式升级任务列表配置
export const consumeMissionMap = {
  totalTradeAmount(currentAmount, conditionAmount, levelName, isFullLevel) {
    currentAmount = moneyFormat(currentAmount);
    conditionAmount = moneyFormat(conditionAmount);
    const need = accSub(conditionAmount, currentAmount);
    const rate = Number((currentAmount / conditionAmount).toFixed(4));
    return {
      remark: '累计购买金额',
      type: ConsumeMissionType.AMOUNT,
      subTitle: `再购买${need}元商品`,
      infoTitle: `${currentAmount}/${conditionAmount}`,
      rate,
      need,
      unit: '元',
      current: currentAmount,
      condition: conditionAmount,
      desc: isFullLevel
        ? '您已升级为最高等级'
        : `再去购买${need}元商品即可升${levelName}`,
      isFullLevel,
      icon: 'https://b.yzcdn.cn/public_files/e2c3b82c8d7337ed7fcfdead41cee065.png',
    };
  },
  totalTradeCount(currentCount, conditionCount, levelName, isFullLevel) {
    const need = conditionCount - currentCount;
    const rate = Number((currentCount / conditionCount).toFixed(4));
    return {
      remark: '累计购买次数',
      type: ConsumeMissionType.COUNT,
      subTitle: `再去下单${need}次`,
      infoTitle: `${currentCount}/${conditionCount}`,
      rate,
      need,
      unit: '次',
      current: currentCount,
      condition: conditionCount,
      desc: isFullLevel
        ? '您已升级为最高等级'
        : `再去购买${need}次商品即可升${levelName}`,
      isFullLevel,
      icon: 'https://b.yzcdn.cn/public_files/03e79c38e9092d3e2156f1563f727e6f.png',
    };
  },
  singleTradeAmount(conditionSingleAmount, levelName, isFullLevel) {
    conditionSingleAmount = moneyFormat(conditionSingleAmount);
    const need = conditionSingleAmount;
    return {
      remark: '单笔消费最大金额',
      type: ConsumeMissionType.SINGLE,
      subTitle: `单笔消费${need}元`,
      infoTitle: '',
      rate: 0,
      need,
      current: '',
      condition: need,
      desc: isFullLevel
        ? '您已升级为最高等级'
        : `单次购买${need}元立即升级到${levelName}`,
      isFullLevel,
      icon: 'https://b.yzcdn.cn/public_files/04fda855e49f36479fdbb4f49e26e10c.png',
    };
  },
  singleStoredValue(conditionSingleStoredValue, levelName, isFullLevel) {
    conditionSingleStoredValue = moneyFormat(conditionSingleStoredValue);

    const need = conditionSingleStoredValue;
    return {
      remark: '单笔充值最大金额',
      type: ConsumeMissionType.SINGLE_STORE,
      subTitle: `单笔充值${need}元`,
      infoTitle: ``,
      rate: 0,
      need,
      current: ``,
      condition: need,
      desc: isFullLevel
        ? `您已升级为最高等级`
        : `单次充值${need}元立即升级到${levelName}`,
      isFullLevel,
      icon: 'https://b.yzcdn.cn/public_files/04fda855e49f36479fdbb4f49e26e10c.png', // eslint-disable-line
    };
  },
};

/**
 * * 会员等级模式
 */
export const LevelMode = {
  /** 成长值模式 */
  GROWTH: 1,
  /** 消费行为模式 */
  CONSUME: 2,
  /** 储值模式 */
  STORE: 3,
};

/**
 * * 会员升级条件类型
 */
export const LevelUpgradeConditionType = {
  /** 消费行为模式 */
  GROWTH: 1,
  REGISTER: 2,
  CONSUME: 3,
};

export const goodsRecommendTitleConfig = {
  title: '更多精选商品',
  showTitleComponent: 1,
  showMethod: 1,
};

// 等级变化操作类型
export const LevelUpgradeType = {
  SET: 1, // 商家设置等级
  MERGE: 2, // 资产合并
  RULE: 3, // 触发升级规则
  SYSTEM: 4, // 系统设置
  INITIATIVE_QUIT: 5, // 主动退出
  REENTER: 6, // 重新入会
};

// 权益类型
export const BenefitType = {
  BIRTHDAY: 'birthday',
};

// 会员协议code
export const MemberProtocolCode = 'SHOP_MEMBER_AGREEMENT';

// 会员专享券活动状态
export const MEMBERCOUPONSTATUS = {
  Unstart: 0, // 未开始
  Ongoing: 1, // 进行中
  InitiativeTerminate: 2, // 已结束 手动结束
  InitiativePause: 3, // 暂停 主动暂停
  ErrorPause: 4, // 暂停 异常暂停
  AutoTerminate: 5, // 已结束 自动结束
};

export const MEMBER_TYPE_ENUM = {
  FREE_MENBER: 'free',
  PAY_MENBER: 'pay',
};

export const LEVEL_TYPE_ENUM = {
  FREE_MENBER: 1,
  PAY_MENBER: 2,
};

/**
 * * 展示弹窗的场景
 */
export const GUIDE_SCENE_ENUM = {
  /** 等级重算 */
  LEVEL_CHANGE: 'LEVEL_CHANGE',
  /** 成为会员 */
  BE_MEMBER: 'BE_MEMBER',
  /* 纯前端状态，入会完成态。从完成信息页跳回来 */
  COMPLETE: 'complete',
  /** 非会员还差一步成为会员 */
  /** 非会员 && vip1有门槛 && 完成注册信息必填项 && 非主动退出 && 之前没有弹过这个框 */
  NOT_MEMBER: 'NOT_MEMBER',
};

export const jumpTypeEnum = {
  HOME: 1,
  MEMBERCODE: 2,
  MEMBERCENTER: 3,
  USERCENTER: 4,
  // 微信会员卡
  WECHATMEMBERCARD: 10,
};

/** 场景类型  1:（默认）  2:客户拉新-门店拉新-入会设置 */
export const LevelProgressSceneEnum = {
  DEFAULT: 1,
  OFFLINE_QRCODE: 2,
};

// 通过会员中心推广二维码进入完善资料页
export const SOURCE_TYPE_UC = 2;
