import adaptorComponents from 'constants/adaptor-components';

const app = getApp();
export function fetchComponentsOfType(type) {
  return app
    .request({
      path: 'wscdeco/biz-component/list-member.json?stage=100',
      method: 'get',
      data: {
        adaptorComponents: adaptorComponents.join(','),
        type,
      },
    })
    .then((components = []) => {
      components = components.filter(
        (item) =>
          item.type !== 'image_ad' ||
          (item.type === 'image_ad' && item.imgs.length > 0)
      );

      return components;
    });
}

export function getDecorateComponents(type) {
  return app
    .request({
      path: '/wscuser/levelcenter/api/getDecorateComponents.json',
      method: 'get',
      data: {
        adaptorComponents: adaptorComponents.join(','),
        type,
      },
    })
    .then((components = []) => {
      components = components.filter(
        (item) =>
          item.type !== 'image_ad' ||
          (item.type === 'image_ad' && item.sub_entry.length > 0)
      );

      return components;
    });
}

export function getCustomerAuth() {
  return app.request({
    path: '/wscuser/scrm/api/getCustomerAuth.json',
  });
}

export function checkIsSupportGoodsRecommend(data) {
  return app.request({
    path: '/wscuser/level/api/checkSupportRecommendGoods.json',
    data,
  });
}

// 当前用户是否在生日时间范围内
export function checkBirthdayStatus(data) {
  return app.request({
    path: '/wscuser/level/api/checkBirthdayStatus.json',
    data,
  });
}

// 判断是否使用新版本权益数据结构
export function getBenefitFormatSwitch() {
  return app.request({
    path: '/wscuser/level/api/getBenefitFormatSwitch.json',
  });
}

// 获取会员专享券活动信息
export const getMemberCouponInfo = () =>
  app.request({
    path: '/wscuser/level/api/getMemberCouponInfo.json',
  });

// 获取会员专享券活动信息
export const getLevelMemberCouponInfo = (data) =>
  app.request({
    path: '/wscuser/level/api/getLevelMemberCouponInfo.json',
    method: 'get',
    data,
  });

// 领券
export const receiveCoupon = (data) =>
  app.request({
    path: '/wscuser/level/api/receiveCoupon.json',
    data,
    method: 'POST',
  });

// 获取用户当前可以参加的会员满赠礼数据
export function getPayAmountGiftList() {
  return app.request({
    path: '/wscuser/level/api/getPayAmountGiftList.json',
    method: 'get',
  });
}

// 判断当前用户是否是会员
export function checkIsMember() {
  return app.request({
    path: '/wscuser/level/api/checkUserIsMember.json',
    method: 'get',
  });
}

// 用户领取赠品
export function receiveGift(data) {
  return app.request({
    path: '/wscuser/level/api/receivePayAmountGift.json',
    method: 'post',
    data,
  });
}

// 获取sl标身份信息
export function getSlInfo(sl) {
  return app.request({
    path: '/wscuser/salesman/getSlInfo.json',
    method: 'get',
    data: { sl },
  });
}
