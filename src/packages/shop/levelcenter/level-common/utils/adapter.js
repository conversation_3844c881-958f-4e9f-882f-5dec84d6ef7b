import get from '@youzan/weapp-utils/lib/get';
import pick from '@youzan/weapp-utils/lib/pick';
import omit from '@youzan/weapp-utils/lib/omit';
import { formatBenefits as originFormatBenefits } from '../../../../benefit-card/utils';
import {
  consumeMissionMap,
  ConsumeMissionType,
  LevelMode,
  LevelUpgradeConditionType,
  BenefitType,
} from './constant';
import MoneyFormat from '@youzan/weapp-utils/lib/money';

export const formatBenefits = originFormatBenefits;

const termTypeText = {
  31: '天',
  32: '周',
  33: '月',
};

// 场景营销的权益处理
export const planBenefitAdapter = (planBenefit = {}) => {
  const res = [];
  const {
    birthdayInfoList,
    festivalInfoList,
    memberdayInfoList,
    memberVoucherInfoList,
  } = planBenefit;

  if (birthdayInfoList?.length) {
    const birthdayInfo = birthdayInfoList[0] || {};
    const { termType } = birthdayInfo.effectiveTime || {};
    const benefit = {
      name: 'birthday',
      pluginName: '生日好礼',
      icon: `//img01.yzcdn.cn/memberlevel/v2/<EMAIL>`,
    };
    benefit.remark = termTypeText[termType]
      ? `生日当${termTypeText[termType]}享好礼`
      : '生日享好礼';
    res.push(benefit);
  }

  if (festivalInfoList?.length) {
    res.push({
      name: 'festival',
      pluginName: '节日特权',
      remark: '节日专属优惠',
      icon: `//img01.yzcdn.cn/memberlevel/v2/<EMAIL>`,
    });
  }

  if (memberdayInfoList?.length) {
    const memberdayInfo = memberdayInfoList[0] || {};
    const { termType } = memberdayInfo.effectiveTime || {};
    const benefit = {
      name: 'memberday',
      pluginName: '专属会员日',
      icon: `//img01.yzcdn.cn/memberlevel/v2/<EMAIL>`,
    };
    if (termType === 31) {
      benefit.remark = '每日专享优惠';
    } else if (termType === 32) {
      benefit.remark = '每周专享优惠';
    } else if (termType === 33) {
      benefit.remark = '每月专享优惠';
    } else {
      benefit.remark = '';
    }

    res.push(benefit);
  }

  if (memberVoucherInfoList?.length) {
    res.push({
      name: 'membervoucher',
      pluginName: '会员专享券',
      remark: '会员定期可领的优惠券',
      icon: `//img01.yzcdn.cn/memberlevel/v2/<EMAIL>`,
    });
  }

  return res;
};

// 等级权益的处理
const benefitListAdapter = (benefitList, planBenefit) => {
  const list = formatBenefits(benefitList);
  list.push(...planBenefitAdapter(planBenefit));
  return list;
};

// 等级卡面的数据处理
const cardAdapter = (level) => {
  const res = {};
  res.color = level.colorCode;
  res.image = level.coverUrl;
  res.alias = level.levelAlias;
  res.levelGroupAlias = level.levelGroupAlias;
  res.minGrowth = get(level, 'levelGrantConditionList[0].minGrowth', 0);
  res.name = level.name;
  res.levelValue = level.levelValue;
  res.levelGoods = level.levelGoods;
  return res;
};

// * 根据当前等级和条件列表生成升级任务列表
const geneUpgradeMissionList = (
  nowLevel,
  levelGrantConditionList,
  levelName,
  isFullLevel
) => {
  const { currentTotalTradeAmount = 0, currentTotalTradeCount = 0 } = get(
    nowLevel,
    'levels[0].customerConsumeInfo',
    {}
  );
  const consumeUpgrade = levelGrantConditionList.find(
    (condition) => condition.conditionType === LevelUpgradeConditionType.CONSUME
  );
  let list = [];
  if (consumeUpgrade) {
    const behaviorCondition = consumeUpgrade?.behaviorCondition || [];
    list = Object.keys(behaviorCondition)
      .filter((mission) => mission !== 'op')
      .map((condition) => {
        const item = behaviorCondition[condition];
        switch (condition) {
          case 'minTotalTradeAmount':
            return consumeMissionMap.totalTradeAmount(
              currentTotalTradeAmount,
              item,
              levelName,
              isFullLevel
            );
          case 'minTotalTradeCount':
            return consumeMissionMap.totalTradeCount(
              currentTotalTradeCount,
              item,
              levelName,
              isFullLevel
            );
          case 'minTradeAmount':
            return consumeMissionMap.singleTradeAmount(
              item,
              levelName,
              isFullLevel
            );
          case 'minStoredValue':
            return consumeMissionMap.singleStoredValue(
              item,
              levelName,
              isFullLevel
            );
          default:
            return {};
        }
      });
  }

  return list.sort((a, b) => b.rate - a.rate); // * 避免未获得等级；有消费金额；超过升级条件，显示负数;
};

const getNeed = (needCondition) => {
  let {
    minTotalTradeAmount: amount,
    // eslint-disable-next-line prefer-const
    minTotalTradeCount: count,
    minTradeAmount: single,
    minStoredValue: singleStore,
  } = needCondition.behaviorCondition || {};

  const hasAmount = !!amount && amount > 0;
  const hasCount = !!count && count > 0;
  const hasSingle = !!single;
  const hasSingleStore = !!singleStore;
  amount = MoneyFormat(amount).toYuan();
  single = MoneyFormat(single).toYuan();
  singleStore = MoneyFormat(singleStore).toYuan();

  if (!amount && !count && !single && !singleStore) {
    return '无门槛';
  }

  let preFix = '';
  // * 有总金额或总次数
  if (hasAmount || hasCount) {
    preFix = '再消费';

    if (hasAmount) {
      preFix += `<span class="big"> ${amount} </span>元`;
      if (hasCount) {
        preFix += `或<span class="big"> ${count}</span> 笔`;
      }
    } else {
      preFix += `<span class="big"> ${count}</span> 笔`;
    }
    if (hasSingle) {
      // * 并且有单次购买条件
      preFix += `或单笔消费<span class="big"> ${single}</span> 元`;
    }
    if (hasSingleStore) {
      // * 并且有单笔充值条件
      preFix += `${
        preFix ? '或' : ''
      }单笔充值<span class="big"> ${singleStore}</span> 元`;
    }
  } else if (hasSingle) {
    // * 只有单次购买条件
    preFix += `单笔消费<span class="big"> ${single}</span> 元`;
    if (hasSingleStore) {
      preFix += `或单笔充值<span class="big"> ${singleStore}</span> 元`;
    }
  } else if (hasSingleStore) {
    // * 只有单笔充值条件
    preFix = `单笔充值<span class="big"> ${singleStore}</span> 元`;
  }

  return preFix ? preFix + '升级为该等级' : '';
};

// * 等级的数据处理
const levelCardsAdapter = (levelList, nowLevel, isLevelGroupEnabled = true) => {
  const list = [];
  // * 先把小于当前等级的和禁用的过滤掉
  levelList = levelList.filter((levelItem) => {
    // 小于当前用户等级的等级不展示
    if (levelItem.levelValue < nowLevel.levelValue) return;
    // 如果当前等级禁用，但没有禁用等级组，不展示这个等级
    if (!levelItem.isEnabled && isLevelGroupEnabled) return;

    return true;
  });
  const isFullLevel = levelList.length === 1;
  const hasLevel = nowLevel.levelValue >= 1;

  let reachThreshold = true;

  levelList.forEach((level, index) => {
    const isConsume = level.mode === LevelMode.CONSUME; // * 是否为消费行为模式
    let upgradeMissionList = [];
    let priorityUpgradeCondition = {};
    let consumeNeed = '';

    if (isConsume) {
      // * 如果是未注册会员，则展示一个单独的不可滑动的upgradeConditionList，以LV1的condition作为计算条件
      // * 当前等级的升级条件，以下一等为依据进行计算
      // * 第一项 && 有等级 && 不是满级 --> 数组的后一项；
      const targetLevel =
        index === 0 && hasLevel && !isFullLevel ? levelList[1] : level;
      upgradeMissionList = geneUpgradeMissionList(
        nowLevel,
        get(targetLevel, 'levelGrantConditionList'),
        get(targetLevel, 'name'),
        isFullLevel
      );

      // * 升级条件
      const needCondition =
        level.levelGrantConditionList.find(
          (condition) =>
            condition.conditionType === LevelUpgradeConditionType.CONSUME
        ) || {};

      needCondition.behaviorCondition.minTotalTradeAmount = needCondition
        .behaviorCondition.minTotalTradeAmount
        ? needCondition.behaviorCondition.minTotalTradeAmount -
          get(
            nowLevel,
            'levels[0].customerConsumeInfo.currentTotalTradeAmount',
            0
          )
        : undefined;
      needCondition.behaviorCondition.minTotalTradeCount = needCondition
        .behaviorCondition.minTotalTradeCount
        ? needCondition.behaviorCondition.minTotalTradeCount -
          get(
            nowLevel,
            'levels[0].customerConsumeInfo.currentTotalTradeCount',
            0
          )
        : undefined;

      if (index === 0) {
        // 无笔数 或 无金额
        if (
          typeof needCondition.behaviorCondition.minTotalTradeAmount ===
            'undefined' ||
          typeof needCondition.behaviorCondition.minTotalTradeCount ===
            'undefined'
        ) {
          if (
            !(
              (typeof needCondition.behaviorCondition.minTotalTradeAmount !==
                'undefined' &&
                needCondition.behaviorCondition.minTotalTradeAmount <= 0) ||
              (typeof needCondition.behaviorCondition.minTotalTradeCount !==
                'undefined' &&
                needCondition.behaviorCondition.minTotalTradeCount <= 0)
            )
          ) {
            reachThreshold = false;
          }
        }
        // 无门槛
        if (
          typeof needCondition.behaviorCondition.minTotalTradeAmount ===
            'undefined' &&
          typeof needCondition.behaviorCondition.minTotalTradeCount ===
            'undefined'
        ) {
          reachThreshold = true;
        }

        if (
          needCondition.behaviorCondition.minTotalTradeAmount > 0 &&
          needCondition.behaviorCondition.minTotalTradeCount > 0
        ) {
          reachThreshold = false;
        }
      }

      consumeNeed = getNeed(needCondition);

      // * 加入优先升级条件
      // ? 如果满级，优先条件设置为累计购买金额
      if (levelList.length === 1) {
        priorityUpgradeCondition =
          upgradeMissionList.find(
            (mission) => mission.type === ConsumeMissionType.AMOUNT
          ) || upgradeMissionList[0];
      } else {
        priorityUpgradeCondition = upgradeMissionList[0];
      }
    } else if (index === 0) {
      const needCondition =
        level.levelGrantConditionList.find(
          (condition) =>
            condition.conditionType === LevelUpgradeConditionType.GROWTH
        ) || {};

      if (
        needCondition.minGrowth > 0 &&
        nowLevel.currentGrowth < needCondition.minGrowth
      ) {
        reachThreshold = false;
      }
    }

    const tempBenefitList = get(level, 'benefitList', []);

    const levelInfo = {
      ...level,
      isEnabled: !!level.isEnabled,
      card: isConsume
        ? { ...cardAdapter(level), priorityUpgradeCondition, consumeNeed }
        : { ...cardAdapter(level), currentGrowth: nowLevel.currentGrowth || 0 },
      levelBenefit: get(level, 'levelBenefit', {}),
      planBenefits: planBenefitAdapter(get(level, 'planBenefits', {})),
      benefitList:
        tempBenefitList.length > 0
          ? tempBenefitList
          : benefitListAdapter(
              get(level, 'levelBenefit', []),
              get(level, 'planBenefits', {})
            ),

      levelGoods: get(level, 'levelGoods', {}),
      upgradeMissionList,
      isConsume, // * 传入mode为2则表明为消费行为模式
    };

    list.push(levelInfo);
  });

  return { list, reachThreshold };
};

const SinglelevelCardsAdapter = (
  levelList,
  nowLevel,
  isLevelGroupEnabled = true,
  detailAlias
) => {
  const level =
    levelList.find((level) => detailAlias === level.levelAlias) || levelList[0];

  // 如果当前等级禁用，但没有禁用等级组，不展示这个等级
  if (!level.isEnabled && isLevelGroupEnabled) return [];

  const tempBenefitList = get(level, 'benefitList', []);
  const levelInfo = {
    isEnabled: !!level.isEnabled,
    card: cardAdapter(level),
    levelBenefit: get(level, 'levelBenefit', {}),
    planBenefits: planBenefitAdapter(get(level, 'planBenefits', {})),
    benefitList:
      tempBenefitList.length > 0
        ? tempBenefitList
        : benefitListAdapter(
            get(level, 'levelBenefit', []),
            get(level, 'planBenefits', {})
          ),
    levelGoods: get(level, 'levelGoods', {}),
  };

  const list = [levelInfo];
  return list;
};
// 付费等级处理
const paidLevelCardsAdapter = (
  levelList,
  nowLevel,
  isLevelGroupEnabled,
  lowestLevel = {},
  supportLowestLevelRenew = false
) => {
  const list = [];
  let index = 0;
  const indexMap = {};
  levelList.forEach((level) => {
    // 如果当前等级没有招募会员,且不是用户拥有的等级
    const lowestLevelValue = get(lowestLevel, 'level.levelValue', '');
    const isExpired = get(lowestLevel, 'isExpired', true);
    // 禁用 & 非当前等级
    if (!level.isDisplay && level.levelValue !== lowestLevelValue) return;

    // 禁用 & 当前等级 & 非体验 & 已经过期
    if (!level.isDisplay && level.levelValue === lowestLevelValue) {
      if (isExpired) {
        return;
      }
    }

    // 当前等级非体验未过期，切不支持低等级续费，则不展示低等级
    const isLowestLevel =
      lowestLevelValue && level.levelValue < lowestLevelValue;

    if (isLowestLevel && !isExpired && !supportLowestLevelRenew) return;
    const tempBenefitList = get(level, 'benefitList', []);
    const levelInfo = {
      isDisplay: !!level.isDisplay,
      card: cardAdapter(level),
      levelBenefit: get(level, 'levelBenefit', {}),
      benefitList:
        tempBenefitList.length > 0
          ? tempBenefitList
          : benefitListAdapter(
              get(level, 'levelBenefit', []),
              get(level, 'planBenefits', {})
            ),
      levelGoods: get(level, 'levelGoods', {}),
    };

    indexMap[level.levelAlias] = index;
    list.push(levelInfo);
    index++;
  });

  return {
    list,
    indexMap,
  };
};

// 成长规则文案
export const ruleAdapter = (rule) => {
  switch (rule.ruleType) {
    case 1:
      return `每消费${(rule.conditionVal / 100).toFixed(
        2
      )}元，交易完成即可获得${rule.rewardGrowth}个成长值；`;
    case 2:
      return `完成${rule.conditionVal}笔订单，交易完成时即可获得${rule.rewardGrowth}个成长值；`;
    default:
      return '';
  }
};

// 获取成长规则
export const growthRuleAdapter = (growthRule) => {
  const res = [];
  let i = 1;
  if (!growthRule || !growthRule.length) return res;
  growthRule.forEach((rule) => {
    const r = ruleAdapter(rule);
    if (r) res.push(`${i++}.${r}`);
  });
  return res;
};

// 处理得到当前等级和成长值信息
export const handleNowLevel = (
  level,
  { customerGrowth, customerConsumeInfo }
) => {
  let nowLevel = null;
  const levelObj = level;
  const simpleLevel = omit(level, 'level');
  const levels = simpleLevel ? [simpleLevel] : [];
  nowLevel = get(levelObj, 'level', null);
  if (!nowLevel) {
    nowLevel = {
      levelValue: 0,
    };
  } else {
    nowLevel.identityNo = levelObj.identityNo;
  }

  nowLevel.currentGrowth = customerGrowth.currentGrowth || 0;
  nowLevel.totalGrowth = customerGrowth.totalGrowth;
  nowLevel.currentTotalTradeAmount =
    customerConsumeInfo.currentTotalTradeAmount || 0;
  nowLevel.currentTotalTradeCount =
    customerConsumeInfo.currentTotalTradeCount || 0;

  nowLevel.levels = levels;
  nowLevel.keepLevelProgress = get(levelObj, 'keepLevelProgress', null);

  return nowLevel;
};

export const levelAdapter = (data, detailAlias = undefined, lowestLevel) => {
  let nowLevel = {};

  const levelV2List = get(data, 'levelList[0].levelV2List', []);

  if (data.cardBenefitList?.length > 0) {
    (levelV2List || []).forEach((level) => {
      const cardBenefitList = data.cardBenefitList.find(
        (benefitList) => benefitList.levelAlias === level.levelAlias
      );
      level.benefitList = cardBenefitList?.benefitList || [];
    });
  } else {
    // 兼容权益数据老处理方式 后续删除
    (levelV2List || []).forEach((level) => {
      const levelPlanBenefit = data?.levelPlanBenefits?.find(
        (levelBenefit) => levelBenefit.levelId === level.levelId
      );
      if (levelPlanBenefit) {
        level.planBenefits = pick(levelPlanBenefit, [
          'birthdayInfoList',
          'festivalInfoList',
          'memberdayInfoList',
          'memberVoucherInfoList',
        ]);
      }
    });
  }

  let cards = null;
  let indexMap = null;
  let reachThreshold = true;
  // 当只需要当个等级的处理时
  if (detailAlias) {
    nowLevel = handleNowLevel(
      data.levels,
      {
        customerGrowth: data.customerGrowth,
        customerConsumeInfo: data.customerConsumeInfo || {},
      },
      1
    );
    cards = SinglelevelCardsAdapter(
      levelV2List,
      nowLevel,
      data.isLevelGroupEnabled,
      detailAlias
    );
  } else if (lowestLevel) {
    nowLevel = handleNowLevel(
      data.levels,
      {
        customerGrowth: data.customerGrowth,
        customerConsumeInfo: data.customerConsumeInfo || {},
      },
      2
    );
    const res = paidLevelCardsAdapter(
      levelV2List,
      nowLevel,
      data.isLevelGroupEnabled,
      lowestLevel,
      data.supportLowestLevelRenew
    );
    cards = res.list;
    indexMap = res.indexMap;
  } else {
    // * 免费会员等级走这个逻辑
    nowLevel = handleNowLevel(
      data.levels,
      {
        customerGrowth: data.customerGrowth,
        customerConsumeInfo: data.customerConsumeInfo || {},
      },
      1
    );
    const { list, ...extraInfo } = levelCardsAdapter(
      levelV2List,
      nowLevel,
      data.isLevelGroupEnabled
    );
    cards = list;
    reachThreshold = extraInfo.reachThreshold;
  }

  return {
    nowLevel,
    growthRule: growthRuleAdapter(data.growthRule),
    cards,
    indexMap,
    reachThreshold,
  };
};

// 设置生日权益所在位置
export const getBirthdayIndex = (userLevel, cards = []) => {
  const levelAlias = userLevel?.level?.levelAlias;

  // 获取当前用户权益等级中生日权益的位置
  const curCard = cards.find((item) => item.card.alias === levelAlias);
  return (curCard?.benefitList || []).findIndex(
    (item) =>
      item.name === BenefitType.BIRTHDAY || item.key === BenefitType.BIRTHDAY
  );
};

export const toMissionCenter = '/wscuser/scrm/missioncenter';
