<view>
  <view wx:for="{{ activityList }}" wx:for-item="activity" wx:for-index="index" wx:key="id">
    <view class="pay-gift-container">
      <view class="title-box">
        <view class="title">消费送好礼</view>
        <view class="time">{{ activity.activityTime }}</view>
        <view class="detail-btn" data-activityId="{{activity.id}}" bindtap="jumpActivityDetail">
          活动详情
          <van-icon name="arrow" />
        </view>
      </view>
      <view wx:if="{{activity.giftList.length === 1}}" class="single-gift-container">
        <block wx:for="{{ activity.giftList }}" wx:for-item="gift" wx:for-index="index" wx:key="id">
          <view class="gift-box {{gift.leftStock <= 0 ? 'gray-bg':''}}">
            <image class="gift-image" src="{{gift.image}}" />
            <view>{{ gift.price }}</view>
            <view class="gift-num">{{ gift.stockStr }}</view>
          </view>
          <view class="gift-info-box">
            <view>
              <view class="line1">{{ gift.condition }}</view>
              <view class="line2">{{ gift.giftName }}</view>
              <view class="progress-box">
                <view class="progress">
                  <view class="progress-line" style="{{ gift.progressStyle }}"></view>
                </view>
                <view class="desc">已累计{{activity.completedProgress}}/{{activity.taskValue}}</view>
              </view>
            </view>
            <view wx:if="{{!isMember}}" class="bottom-btn" bindtap="jumpFillMember">
              {{activity.joinMemberBtnText}}
            </view>
            <view wx:elif="{{ gift.canReceiveGift }}" data-activityId="{{activity.id}}" data-gift="{{gift}}" class="bottom-btn" bindtap="receiveGift">
              领取奖品
            </view>
            <view wx:elif="{{gift.received}}" class="bottom-btn" bindtap="viewGift">查看奖品</view>
          </view>
        </block>
      </view>
      <view wx:else class="multiple-gift-container">
        <view class="multiple-gift-context">
          <view wx:for="{{ activity.giftList }}" wx:for-item="gift" wx:for-index="index" wx:key="id" class="multiple-gift-box {{activity.giftList.length === 2 ? 'big-margin' : ''}}">
            <view class="gift-box {{(gift.leftStock <= 0 || (gift.hasReceivedGift && !gift.received))?'gray-bg':'' }}">
              <image class="gift-image" src="{{gift.image}}" />
              <view>{{ gift.price }}</view>
              <view wx:if="{{ gift.canReceiveGift }}" class="gift-btn" data-activityId="{{activity.id}}" data-gift="{{gift}}" bindtap="receiveGift">
                领取奖品
              </view>
              <view wx:elif="{{gift.received}}" class="gift-btn" bindtap="viewGift">查看奖品</view>
              <view wx:else class="gift-stock">{{ gift.stockStr }}</view>
            </view>
            <view class="{{gift.hasFinishCurLevel ? 'check' : 'point'}}"></view>
            <view class="gift-desc">
              <view class="line1">{{ gift.condition }}</view>
              <view>{{ gift.giftName }}</view>
            </view>
          </view>
          <view class="progress-box">
            <!-- 根据赠品数量显示不同类型进度条 -->
            <view class="{{activity.progressContainerStyle}}">
              <view class="progress-line {{ activity.progresslineStyle }}"></view>
              <view wx:if="{{ activity.curLevelNum < activity.giftList.length }}" class="progress-num {{activity.progressNumStyle}}">
                {{ activity.completedProgress }}
              </view>
            </view>
          </view>
        </view>
        <view wx:if="{{!isMember}}" bindtap="jumpFillMember" class="join-member-btn">
          {{activity.joinMemberBtnText}}
        </view>
      </view>
    </view>
    <view class="bottom-border"></view>
  </view>
</view>