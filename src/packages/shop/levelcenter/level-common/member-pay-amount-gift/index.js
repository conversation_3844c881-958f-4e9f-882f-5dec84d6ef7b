import WscComponent from 'pages/common/wsc-component/index';
import { receiveGift, getPayAmountGiftList, checkIsMember } from '../utils/api';
import formatDate from '@youzan/utils/date/formatDate';
import openWebView from 'utils/open-web-view';
import debounce from '@youzan/weapp-utils/lib/debounce';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import Toast from '@vant/weapp/dist/toast/toast';
import accDiv from '@youzan/utils/number/accDiv';
import args from '@youzan/weapp-utils/lib/args';

const app = getApp();

WscComponent({
  properties: {},
  data: {
    isMember: false,
    activityList: [],
    kdtId: app.getKdtId(),
  },
  methods: {
    doReceiveGift(event) {
      this.postLog({
        et: 'click', // 事件类型
        ei: 'click_membergift', // 事件标识
        en: '会员满赠礼点击', // 事件名称
      });
      checkIsMember().then((isMember) => {
        if (isMember) {
          Dialog.confirm({
            title: '活动期间仅可领取1档奖励，确定领取该奖品？',
            confirmButtonText: '领取',
            cancelButtonText: '取消',
          }).then(() => {
            const { activityid, gift } = event.currentTarget.dataset;
            const { id, stageCode } = gift;
            receiveGift({
              activityId: Number(activityid),
              prizeId: id,
              prizeType: 'PRESENT',
              stageCode,
            }).then(
              () => {
                Toast.success({
                  message: '领取成功',
                  className: 'success-toast',
                });
                this.getPayAmountGiftList();
              },
              (res) => {
                wx.showToast({
                  title: res.msg,
                  icon: 'none',
                });
                this.getPayAmountGiftList();
              }
            );
          });
        } else {
          wx.showToast({
            title: '请先成为会员',
            icon: 'none',
          });
          this.setYZData({
            isMember: false,
          });
        }
      });
    },
    jumpActivityDetail(event) {
      const { activityid } = event.currentTarget.dataset;
      openWebView('/wscuser/levelcenter/activityDetail', {
        query: {
          kdt_id: this.data.kdtId,
          activity_id: activityid,
        },
      });
    },
    jumpFillMember() {
      this.postLog({
        et: 'click', // 事件类型
        ei: 'click_membergift_join', // 事件标识
        en: '会员满赠礼_引导入会点击', // 事件名称
      });
      openWebView('/wscuser/levelcenter/fill', {
        query: {
          kdt_id: this.data.kdtId,
          eT: Date.now(),
        },
      });
    },
    viewGift() {
      const weappUrl = args.add('/packages/ump/presents/index', {
        kdt_id: this.data.kdtId,
      });
      wx.navigateTo({
        url: weappUrl,
      });
    },
    getPayAmountGiftList() {
      getPayAmountGiftList().then(
        (res) => {
          if (res) {
            this.formatData(res);
          }
        },
        (err) => {
          console.log(err);
        }
      );
    },
    checkIsMember() {
      checkIsMember().then((res) => {
        this.setYZData({
          isMember: res.value || false,
        });
      });
    },
    formatData(fromData) {
      const activityList = [];
      (fromData || []).forEach((activity) => {
        const formatActivity = {};
        if (activity.beginAt && activity.endAt) {
          formatActivity.activityTime = `活动时间：${formatDate(
            activity.beginAt,
            'MM.DD'
          )} - ${formatDate(activity.endAt, 'MM.DD')}`;
        }

        // 已完成金额
        formatActivity.completedProgress = accDiv(
          activity.completedProgress,
          100
        );
        // 总金额
        formatActivity.taskValue = accDiv(activity.taskValue, 100);

        formatActivity.id = activity.id;
        const giftList = [];
        // 本活动是否已领取过奖励
        let hasReceivedGift = false;
        (activity.stageList || []).forEach((stage) => {
          const receivedGift = stage.prizes.find((gift) => gift.received);
          if (receivedGift) hasReceivedGift = true;
        });

        (activity.stageList || []).forEach((stage) => {
          const { taskValue, prizes = [], stageCode } = stage;
          const giftInfo = prizes?.[0] || {};
          const total = accDiv(taskValue, 100);
          const cur = formatActivity.completedProgress;
          const perGift = {
            id: giftInfo.prizeId,
            condition: `累计￥${total}可领`,
            giftName: ` ${giftInfo.title}`,
            price: `￥${accDiv(giftInfo.price, 100) || ''}`, // 商品价格
            stockStr:
              giftInfo.stock > 0 ? `共${giftInfo.promptAmount}份` : '已抢光', // 展示库存
            leftStock: giftInfo.stock, // 实际剩余库存
            image: giftInfo.imgUrl,
            // 是否领取过本奖品
            received: giftInfo.received,
            // 本次活动是否已经领过奖
            hasReceivedGift,
            // 是否完成本阶段目标
            hasFinishCurLevel: cur >= total,
            // 阶段code
            stageCode,
            progressStyle:
              cur > total ? `width: 100%` : `width: ${(cur / total) * 100}%`,
          };
          // 是否能领取赠品
          perGift.canReceiveGift =
            perGift.leftStock > 0 &&
            perGift.hasFinishCurLevel &&
            !perGift.hasReceivedGift;
          giftList.push(perGift);
        });
        formatActivity.giftList = giftList;

        let level = 0;
        (giftList || []).forEach((gift) => {
          if (gift.hasFinishCurLevel) {
            level++;
          }
        });
        // 当前活动已完成阶段数量
        formatActivity.curLevelNum = level;
        formatActivity.joinMemberBtnText =
          level > 0 ? '入会立即领奖' : '入会参与活动';

        formatActivity.progressContainerStyle = `progress-${formatActivity.giftList.length}`;
        formatActivity.progresslineStyle =
          formatActivity.curLevelNum >= formatActivity.giftList.length
            ? 'finish'
            : `line-stage${formatActivity.curLevelNum}`;
        formatActivity.progressNumStyle = `num-stage${formatActivity.curLevelNum}`;

        activityList.push(formatActivity);
      });
      if (activityList.length > 0) {
        this.postLog({
          et: 'view', // 事件类型
          ei: 'view_membergift', // 事件标识
          en: '会员满赠礼曝光', // 事件名称
        });
      }
      this.setYZData({
        activityList,
      });
    },
    postLog(params) {
      app.logger?.log(params);
    },
    checkMemberGuideLog() {
      if (!this.data.isMember && this.data.activityList.length > 0) {
        this.postLog({
          et: 'view', // 事件类型
          ei: 'view_membergift_join', // 事件标识
          en: '会员满赠礼_引导入会曝光', // 事件名称
        });
      }
    },
  },
  attached() {
    this.getPayAmountGiftList();
    this.checkIsMember();
    this.receiveGift = debounce(this.doReceiveGift, 300);
    // 延迟判断引导入会按钮曝光
    setTimeout(() => {
      this.checkMemberGuideLog();
    }, 1000);
  },
});
