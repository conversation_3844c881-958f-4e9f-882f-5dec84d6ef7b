import WscComponent from 'pages/common/wsc-component/index';
import { moment as formatDate } from 'utils/time';
import {
  MEMBERCOUPONSTATUS,
  MEMBER_TYPE_ENUM,
  LEVEL_TYPE_ENUM,
} from '../utils/constant';
import * as API from '../utils/api';

// 防止领券重复提交
let isReceivingCoupon = false;
let firstShow = false;
const app = getApp();

WscComponent({
  properties: {
    planList: {
      type: Array,
    },
    isMember: Boolean,
    levelType: {
      type: String,
      default: MEMBER_TYPE_ENUM.PAY_MENBER,
    },
    levelAlias: String,
    memberName: {
      type: String,
      value: '会员',
    },
  },
  observers: {
    levelAlias(levelAlias) {
      this.reflashPayCoupon(levelAlias);
    },
  },
  data: {
    planList: [],
    levelPlanList: [],
    coupons: [],
    couponsLength: 0,
    subTitle: '',
    curPlanTimeRange: '',
    nextPlanTimeRange: '',
    checkTabIndex: 0,
  },
  attached() {
    this.getMemberCoupon();
  },
  methods: {
    getMemberCouponLoggerPageName() {
      return this.properties.levelType === MEMBER_TYPE_ENUM.FREE_MENBER
        ? 'levelCenterFree'
        : 'membercenterpay';
    },
    reflashPayCoupon(levelAlias) {
      if (this.properties.levelType === MEMBER_TYPE_ENUM.PAY_MENBER) {
        const levelPlan = (this.data.levelPlanList || []).find(
          (x) => x.levelAlias === levelAlias
        );
        if (levelPlan) {
          this.formatData(levelPlan?.list || []);
        } else {
          this.setYZData({
            planList: [],
          });
        }
      }
    },
    getMemberCoupon() {
      if (this.properties.levelType === MEMBER_TYPE_ENUM.FREE_MENBER) {
        // 获取会员专享券活动信息
        return API.getMemberCouponInfo().then((data) => {
          isReceivingCoupon = false;
          this.formatData(data || []);
          if (!firstShow && data?.length > 0) {
            firstShow = true;
            this.log({
              et: 'view', // 事件类型
              ei: 'memb_coupon', // 事件标识
              en: '专享券组件曝光', // 事件名称
              pt: this.getMemberCouponLoggerPageName(),
            });
          }
        });
      }

      if (this.properties.levelType === MEMBER_TYPE_ENUM.PAY_MENBER) {
        const params = {
          levelGroupType: LEVEL_TYPE_ENUM.PAY_MENBER,
        };
        return API.getLevelMemberCouponInfo(params).then((data) => {
          isReceivingCoupon = false;
          this.setYZData(
            {
              levelPlanList: data || [],
            },
            () => {
              this.reflashPayCoupon(this.properties.levelAlias);
            }
          );
          if (!firstShow && data?.length > 0) {
            firstShow = true;
            this.log({
              et: 'view', // 事件类型
              ei: 'memb_coupon', // 事件标识
              en: '专享券组件曝光', // 事件名称
              pt: this.getMemberCouponLoggerPageName(),
            });
          }
        });
      }
    },
    formatData(planList) {
      const [curPlan, nextPlan] = planList;

      const curPlanTimeRange = this.getPlanTime(curPlan);
      const nextPlanTimeRange = this.getPlanTime(nextPlan);

      let subTitle = '';
      if (nextPlan) subTitle = '定期上新、期期可领';
      else subTitle = `活动时间：${curPlanTimeRange}`;

      const coupons = planList?.[0]?.voucherActivityList || [];
      this.setYZData({
        checkTabIndex: 0,
        coupons,
        couponsLength: coupons.length,
        subTitle,
        curPlanTimeRange,
        nextPlanTimeRange,
        planList,
      });
    },
    checkTab(event) {
      const { index } = event.currentTarget.dataset;
      const { planList } = this.data;
      const coupons = planList?.[index]?.voucherActivityList || [];
      this.setYZData({
        checkTabIndex: index,
        coupons,
        couponsLength: coupons.length,
      });
    },

    getPlanTime(planData) {
      const { beginAt, endAt } = planData || {};
      if (beginAt && endAt) {
        const beginStr = formatDate(beginAt, 'MM.DD');
        const endStr = formatDate(endAt, 'MM.DD');
        return beginStr === endStr ? `${beginStr}` : `${beginStr}-${endStr}`;
      }
      return '';
    },

    receiveCoupon(event) {
      this.log({
        et: 'click', // 事件类型
        ei: 'memb_coupon_click', // 事件标识
        en: '专享券立即领取', // 事件名称
        pt: this.getMemberCouponLoggerPageName(),
      });
      if (this.properties.isMember) {
        const { checkTabIndex, planList } = this.data;
        const planData = planList?.[checkTabIndex] || {};
        if (
          checkTabIndex > 0 ||
          planData.state !== MEMBERCOUPONSTATUS.Ongoing
        ) {
          return wx.showToast({ title: '活动未开始', icon: 'none' });
        }
        if (isReceivingCoupon) return;
        isReceivingCoupon = true;
        const coupon = event.detail;
        const secondarySource = `${planData.planId || ''}`;
        const params = {
          activityId: `${coupon.activityId || ''}`,
          secondarySource,
          source: 'member_coupon_type',
        };
        if (this.properties.levelType === MEMBER_TYPE_ENUM.PAY_MENBER) {
          params.levelGroupType = LEVEL_TYPE_ENUM.PAY_MENBER;
          params.levelAlias = this.data.levelAlias;
        }
        API.receiveCoupon(params)
          .then((data) => {
            const { couponId, bizMsg } = data || {};
            if (couponId) {
              wx.showToast({ title: '领取成功', icon: 'none' });
              this.log({
                et: 'click', // 事件类型
                ei: 'memb_coupon_receive', // 事件标识
                en: '专享券领取成功', // 事件名称
                pt: this.getMemberCouponLoggerPageName(),
              });
              // 领取完成 更新活动信息
              this.getMemberCoupon();
            } else {
              // 领取提示由后端拼接
              wx.showToast({ title: bizMsg || '领取失败', icon: 'none' });
              this.getMemberCoupon();
            }
          })
          .catch(() => {
            wx.showToast({ title: '领取失败', icon: 'none' });
            isReceivingCoupon = false;
          });
      } else {
        wx.showToast({
          title: '仅限会员领取，加入会员即可领券',
          icon: 'none',
        });
        this.log({
          et: 'click', // 事件类型
          ei: 'only_member_receive', // 事件标识
          en: '仅限会员领取提示', // 事件名称
          pt: this.getMemberCouponLoggerPageName(),
        });
      }
    },
    log(log) {
      app.logger && app.logger.log(log);
    },
  },
});
