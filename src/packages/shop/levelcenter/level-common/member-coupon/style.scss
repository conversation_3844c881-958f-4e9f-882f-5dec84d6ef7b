.member-coupon {
  border-radius: var(--border-radius, 0);
  background-image: var(--bg-image, #fff);
  background-repeat: no-repeat;
  background-size: auto 90px;
  background-position: 0 0;
  background-color: #fff;
  margin-bottom: 12px;

  .title-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 12px 16px 0;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }

    .sub-title {
      color: #969799;
      font-size: 12px;
      margin-left: 8px;
      line-height: 16px;
    }
  }

  .term-tab {
    margin-top: 24px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    padding: 0 16px;
    .part-tab {
      width: 155px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: #969799;
      font-size: 12px;
      .tab-name {
        font-size: 16px;
        line-height: 24px;
        font-weight: bold;
      }
    }
    .checked {
      color: #323233;
    }

    .border {
      width: 1px;
      height: 32px;
      background-color: #ebedf0;
    }
  }

  .coupon-container {
    margin-top: 16px;
    position: relative;
    .coupon-box-list {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      flex-direction: row;
      padding: 0 12px 14px;

      .coupon-item {
        margin-right: 8px;
      }
      .coupon-item:last-child {
        margin-right: 0;
      }
    }

    .coupon-box-list::-webkit-scrollbar {
      display: none;
    }

    .coupon-center {
      justify-content: center;
      padding: 0 0 16px;
    }

    .other-benefit {
      flex: 1;
      height: 116px;
      border-radius: 4px;
      font-size: 12px;
      color: #b66f0f;
      line-height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: linear-gradient(to bottom right, #fff8f0, #fff2e0);

      .line1 {
        display: flex;
        flex-direction: row;
      }
      .line2 {
        display: flex;
        flex-direction: column;
      }
    }
    .mask {
      position: absolute;
      top: 0;
      right: 0;
      width: 48px;
      height: 116px;
      background-image: linear-gradient(
        to left,
        #fff,
        rgba(255, 255, 255, 0.1)
      );
      padding-right: 16px;
    }
  }
}
