<view class="member-coupon" wx:if="{{ planList.length }}">
  <view class="title-box">
    <view class="title">{{ memberName }}专享券</view>
    <view class="sub-title">{{ subTitle }}</view>
  </view>
  <view class="term-tab" wx:if="{{ planList.length > 1 }}">
    <view class="part-tab {{checkTabIndex == 0 ? 'checked' : ''}}" data-index="0" bindtap="checkTab">
      <view class="tab-name">本期</view>
      <view>{{ curPlanTimeRange }}</view>
    </view>
    <view class="border"></view>
    <view class="part-tab {{checkTabIndex == 1 ? 'checked' : ''}}" data-index="1" bindtap="checkTab">
      <view class="tab-name">下期</view>
      <view>{{ nextPlanTimeRange }}</view>
    </view>
  </view>
  <view class="coupon-container">
    <view class="coupon-box-list {{ couponsLength == 3 ? 'coupon-center' : ''}}">
      <view class="coupon-item" wx:for="{{ coupons }}" wx:for-item="coupon" wx:key="activityId">
        <short-coupon coupon="{{coupon}}" bindreceiveCoupon="receiveCoupon" level-type="{{ levelType }}" />
      </view>
      <view wx:if="{{ couponsLength < 3 }}" class="other-benefit">
        <view class="{{ couponsLength == 1 ? 'line1' : 'line2'}}">
          <text>暂无其他</text>
          <text>可领权益</text>
        </view>
      </view>
    </view>
    <view wx:if="{{ couponsLength > 3 }}" class="mask"></view>
  </view>
</view>