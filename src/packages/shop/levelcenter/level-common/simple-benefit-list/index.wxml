<view>
  <!-- 权益列表 -->
  <view wx:if="{{ showList.length > 0 }}" class="simple-benefit-list ">
    <view
      class="benefit-list {{ customClass }}"
      style="{{customBg}}"
    >
      <view
        wx:for="{{ showList }}"
        wx:key="key"
        wx:for-index="index"
        class="benefit-item"
      >
        <text wx:if="{{ item.key === 'experienceCard' && unTakeVisible }}" class="badge">待领取</text>
        <text wx:if="{{ item.key === 'coupon' && item.total }}" class="badge coupon-badge">{{ item.total }}张</text>
        <text wx:if="{{ item.badge }}" class="badge coupon-badge">{{ item.badge }}</text>
        <view
          data-index="{{ index }}"
          class="benefit_item-wrapper"
          bindtap="toBenefitLink"
        >
          <image
            wx:if="{{ item.icon }}" 
            src="{{ item.icon }}"
            class="icon-img"
          />
          <view wx:elif="{{ item.benefitCount > 0 }}" class="icon-num">
            {{ item.benefitCount }}
            <text class="icon-unit">项</text>
          </view>
          <image
            wx:else
            src="//img.yzcdn.cn/memberlevel/v2/{{ item.name }}@2x.png"
            class="icon-img"
          />
          <view class="icon-name" style="{{customNameColor}}">{{ item.facadeShowName || item.appName || item.pluginName || item.title }}</view>
        </view>
      </view>
    </view>
  </view>
</view>
