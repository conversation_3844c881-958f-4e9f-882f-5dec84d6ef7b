import { getSubscribePreInfo } from '@/helpers/subscribe';

import args from '@youzan/weapp-utils/lib/args';

import { SCENE_MARKET_SCENE } from 'constants/member-constants';

import WscComponent from 'pages/common/wsc-component/index';

import { subscribeInSceneMarketActivity } from 'utils/scene-market-subscribe-message';

const app = getApp();
WscComponent({
  properties: {
    benefitsList: {
      type: Array,
      value: [],
      observer(nv) {
        this.setShowList(nv);
      },
    },
    unTakeVisible: Boolean,
    subType: Number,
    rowSize: {
      type: Number, // 每一行几个
      value: 4,
    },
    maxRow: {
      type: Number, // 最多有几行
      value: 2,
    },
    nowLevelAlias: String,
    kdtId: String,
    needSubscribe: Boolean, // * 是否需要采集订阅消息
    customNameColor: {
      type: String,
      value: '',
    },
    customBg: {
      type: String,
      value: '',
    },
    page: {
      type: String,
      value: '',
    },
  },

  data: {
    showList: [],
    customClass: '',
    useBenefitFormat: false,
    templateIdList: [],
  },
  attached() {
    this.getSubscribeInfo();
  },
  methods: {
    toBenefitLink(e) {
      const { rowSize, maxRow, benefitsList } = this.properties;
      const mostIconNum = rowSize * maxRow;
      let { index } = e.currentTarget.dataset;
      if (benefitsList.length > mostIconNum && index === mostIconNum - 1) {
        index = 0;
      }
      if (
        benefitsList[index].name === 'membervoucher' ||
        benefitsList[index].key === 'memberVoucher'
      ) {
        this.log({
          et: 'click', // 事件类型
          ei: 'memb_coupon_icon_click', // 事件标识
          en: '专享券去哪权益项点击', // 事件名称
        });
      }
      const { kdtId, nowLevelAlias } = this.properties;

      const params = {
        kdt_id: kdtId,
        alias: nowLevelAlias,
        benefit: index,
      };
      if (this.data.useBenefitFormat) params.benefit_format = '1';
      const weappUrl = args.add('/packages/levelcenter/benefit/index', params);

      // * 订阅拦截
      if (
        this.properties.needSubscribe &&
        this.data.templateIdList.length > 0
      ) {
        const self = this;
        subscribeInSceneMarketActivity({
          logParam: {
            subscribe_type: '权益领取',
            subscribe_page: this.page,
          },
          templateIdList: this.data.templateIdList,
          onSuccess: () => {
            // * 回调参数，true 代表订阅成功；false 代表订阅失败；
            self.triggerEvent('subscribeCallback', true);
          },
          onClose: () => {
            // eslint-disable-next-line @youzan/dmc/wx-check
            wx.navigateTo({
              url: weappUrl,
            });
          },
        });
      } else {
        // eslint-disable-next-line @youzan/dmc/wx-check
        wx.navigateTo({
          url: weappUrl,
        });
      }
    },
    setShowList(benefitsList) {
      const { rowSize, maxRow } = this.properties;
      const mostIconNum = rowSize * maxRow;
      let showList = benefitsList;

      const keys = Object.keys(benefitsList?.[0] || {});
      // 新版本权益数据结构判断
      if (keys.includes('title') && keys.includes('benefitType')) {
        this.setYZData({ useBenefitFormat: true });
      }

      if (benefitsList.length > mostIconNum) {
        showList = benefitsList.slice(0, mostIconNum - 1);
        showList.push({
          title: '全部权益',
          appName: '全部权益', // 全量更新后可以删除
          benefitCount: benefitsList.length,
        });
      }
      const index = showList.findIndex(
        (benefit) =>
          benefit.name === 'membervoucher' || benefit.key === 'memberVoucher'
      );
      if (index >= 0) {
        this.log({
          et: 'view', // 事件类型
          ei: 'memb_coupon_icon', // 事件标识
          en: '专享券权益项曝光', // 事件名称
        });
      }
      this.setYZData({
        showList,
        customClass:
          showList.length > 4
            ? 'benefit-list-flex-start'
            : 'benefit-list-flex-center',
      });
    },
    log(log) {
      app.logger && app.logger.log(log);
    },
    getSubscribeInfo() {
      getSubscribePreInfo({
        scene: SCENE_MARKET_SCENE,
      }).then((templateIdList) => {
        this.setYZData({
          templateIdList,
        });
      });
    },
  },
});
