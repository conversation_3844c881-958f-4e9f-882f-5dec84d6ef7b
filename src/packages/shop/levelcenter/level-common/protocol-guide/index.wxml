<view>
  <protocol-component
    bind:refresh-protocol-data="initData"
    bind:agree="handleAgree"
    kdtId="{{ kdtId }}"
    code="{{ [type] }}"
    visible="{{ visible }}"
    btn-config="{{ btnConfig }}"
    ready="{{ true }}"
  >
    <view slot="title" class="title">{{ title }}</view>
    <view slot="content">
      <view
        class="agreement-content"
        style="{{ themeStyle }}"
      >
        {{ changeType === 'sign' ? '你好，请阅读' : '你好，我们对'}}
        <view
          class="agreement"
          wx:for="{{ protocolArr }}"
          wx:key="agreementTplId"
          data-url="{{ item.url }}"
          bindtap="viewAgreement"
        >《{{ item.agreementTplName }}》</view>
        {{
          changeType === 'sign' ?
            '，以便你了解商家会员卡注册规则、你的个人信息授权使用情况以及如何保障你的会员权益。'
            : '进行了更新，为保障你的会员权益，请仔细阅读并确定了解我们对你个人信息的处理规则。'
        }}
      </view>
    </view>
  </protocol-component>
</view>
