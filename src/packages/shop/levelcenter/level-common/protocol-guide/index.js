import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'utils/open-web-view';

const BtnConfig = {
  agreeBtn: '我已知晓，继续',
  hideDisagree: true,
};

WscComponent({
  properties: {
    kdtId: {
      type: Number,
      value: 0,
    },
  },

  data: {
    type: 'SHOP_MEMBER_AGREEMENT',
    title: '',
    changeType: 'sign', // sign 签署, modify 更改
    protocolArr: [],
    btnConfig: BtnConfig,
    visible: true,
    themeStyle: '',
  },
  methods: {
    initData(ev) {
      // 会员协议更新或者签署，有且只有一种类型的协议，更新或者签署，后端无法判断title
      // 在没有协议的时候，组件会trigger agree 触发 handleAgree 进行next不需要手动触发
      const { sign = [], modify = [], themeStyle } = ev.detail;
      if (sign.length) {
        this.setYZData({
          title: '会员协议签署指引',
          changeType: 'sign',
          protocolArr: sign,
          themeStyle,
        });
        return;
      }
      if (modify.length) {
        this.setYZData({
          title: '会员协议更新通知',
          changeType: 'modify',
          protocolArr: modify,
        });
      }
    },
    handleAgree() {
      this.setYZData({ visible: false });
      this.triggerEvent('next', false);
    },
    viewAgreement({
      currentTarget: {
        dataset: { url },
      },
    }) {
      openWebView(url, {});
    },
  },
});
