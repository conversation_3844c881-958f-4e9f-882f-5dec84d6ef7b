import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import WscComponent from 'pages/common/wsc-component/index';
import { isNewIphone } from 'shared/utils/browser/device-type';

const isIphoneX = isNewIphone();

WscComponent({
  properties: {
    hasButton: Boolean,
    hasTabbar: Boolean,
  },

  data: {
    isIphoneX,
    bgUrl: cdnImage(
      'https://b.yzcdn.cn/public_files/99f21f7f10069ab3bfb9f4567d2bbd2c.png',
      '!160x160.jpg'
    ),
    iconUrl: cdnImage(
      'https://b.yzcdn.cn/public_files/c7b1ea7d3023a0047bb3f7df768643f0.png',
      '!12x16.jpg'
    ),
  },

  methods: {
    // * 点击订阅
    handleSubscribe() {
      this.triggerEvent('subscribe');
    },
  },
});
