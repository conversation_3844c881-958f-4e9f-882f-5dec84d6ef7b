import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'utils/open-web-view';
import args from '@youzan/weapp-utils/lib/args';
import navigate from '@/helpers/navigate';

const missionState = {
  start: 0,
  done: 30
};

const app = getApp();
const kdtId = app.getKdtId();

WscComponent({
  properties: {
    mission: {
      type: Object
    },

    hasBindPhone: {
      type: Boolean
    },

    index: {
      type: Number
    },

    disabledDefaultAction: {
      type: Boolean
    }
  },

  data: {
    showMore: false,
    isOpen: false
  },

  methods: {
    handleClickDoMission() {
      this.navigateWithUrlParams({
        url: '/v2/home',
        weappUrl: '/pages/home/<USER>/index',
        type: 'switchTab',
        aliappUrl: '/pages/home/<USER>/index',
        qqUrl: '/pages/home/<USER>/index',
      });
    },

    navigateWithUrlParams({ url, weappUrl, type }) {
      if (weappUrl) {
        if (type && navigate[type]) {
          navigate[type]({ url: weappUrl });
        } else {
          navigate.navigate({ url: weappUrl });
        }
      } else {
        openWebView(args.add(url, { kdt_id: kdtId }));
      }
    },

    // 更新任务状态
    updateMissionState(name) {
      const formData = {
        missionTplType: name,
        state: missionState.done
      };
      return app.request({
        path: '/wscuser/scrm/api/ump/update',
        method: 'POST',
        data: formData
      });
    }
  }
});
