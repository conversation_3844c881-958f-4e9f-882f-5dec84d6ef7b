.container {
  padding-bottom: 50px;
  min-height: 100vh;
  background: #fff;
  box-sizing: border-box;
}

.showcase-container {
  height: auto !important;
}

.swiper-wrapper {
  margin-left: 15px;
}

.levels-wrapper {
  overflow: hidden;
  margin-top: -2px;
  padding-top: 12px;
  position: relative;
  min-height: 150px;
}

.card-bg {
  position: absolute;
  top: 0;
  width: 100%;
  height: 80px;

  .bg-img {
    height: 80px;
    background: #252525;
  }
}

.swipe-wrapper {
  min-height: 180px;
  margin-left: 16px;
  position: relative;
  z-index: 1;
}

.showcase-components {
  margin-top: 26px;
}

.simple-benefit-list {
  margin-top: 24px;
}

.benefit-slogan + .simple-benefit-list {
  margin-top: 0;
}

.footer .copyright {
  padding-bottom: 55px;
}

.split-border {
  width: 100%;
  height: 10px;
  background-color: #f7f8fa;
}
