<view>
    <view wx:if="{{!!sloganPrice}}" class="start-info">{{ sloganPrice }}</view>
    <view class="center-flex">
        <view class="set-name">{{ item.setName }}</view>
        <view class="set-price">
            ￥
            <view class="price" style="font-size: {{priceSize}}px">{{ price }}</view>
        </view>
        <p wx:if="{{item.handlePrice && item.handlePrice.hasDiscount}}" class="origin-price">
            <i>￥</i>
            {{ item.handlePrice && item.handlePrice.price }}
        </p>
    </view>
</view>