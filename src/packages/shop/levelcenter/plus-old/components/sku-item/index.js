import WscComponent from 'pages/common/wsc-component/index';
import { UNIT_CYCLE_TEXT } from '../../utils/handle';

const isNil = (item) => {
  return item === undefined || item === null;
};

WscComponent({
  data: {
    sloganPrice: '',
    price: '',
    priceSize: 0,
  },
  properties: {
    item: {
      type: Object,
      observer() {
        this.renderComputed();
      },
    },
    hasMonthNotCheapest: {
      type: <PERSON><PERSON><PERSON>,
      observer() {
        this.renderComputed();
      },
    },
    bestPrice: {
      type: Number,
      observer() {
        this.renderComputed();
      },
    },
  },
  methods: {
    sloganPrice() {
      const unitCycle = UNIT_CYCLE_TEXT;
      const { item } = this.properties;
      const handlePrice = item?.handlePrice ?? {};
      if (!isNil(handlePrice.startPrice)) {
        return `首${item.originCycle}仅${handlePrice.startPrice}元`;
      }
      if (
        this.properties.hasMonthNotCheapest &&
        item.originCycle !== unitCycle.MONTH &&
        item.monthPirce &&
        Number(this.properties.bestPrice) === Number(item.monthPirce)
      ) {
        return `每${unitCycle.MONTH}低至${item.monthPirce}元`;
      }
      return '';
    },
    price() {
      const { item } = this.properties;
      const handlePrice = item?.handlePrice ?? {};
      if (!isNil(handlePrice.startPrice)) {
        return handlePrice.startPrice;
      }
      if (!isNil(handlePrice.hasDiscount)) {
        return handlePrice.consecutivePrice;
      }
      return handlePrice.price;
    },
    priceSize() {
      return 28 - 6 * (Math.ceil(this.price().toString().length / 2) - 1);
    },
    renderComputed() {
      this.setYZData({
        sloganPrice: this.sloganPrice(),
        price: this.price(),
        priceSize: this.priceSize(),
      });
    },
  },
});
