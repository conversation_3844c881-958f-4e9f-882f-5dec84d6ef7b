<view class="mobile-check-action {{ isIphoneX ? 'is-new-phone' : '' }}">
  <view class="can-not-promot" wx:if="{{ currentCycleSku.goodsSkuType === 2 && !isWaitReNewStatus}}">
    <view class="text-line">根据小程序平台运营规则，小程序暂不支持付费会员连续订购，请复制链接后，在浏览器环境中打开并订购。</view>
  </view>
  <view wx:if="{{ currentCycleSku.goodsSkuType !== 2 && !isWaitReNewStatus }}" class="check-text {{ (skuInfo.showPrice || skuInfo.showPrice === 0) ? 'check-text-left' : '' }}">
    <van-checkbox value="{{ checked }}" bind:change="handleChecked" icon-size="18px" checked-color="#e2bb7b" class="agreement"></van-checkbox>
    <view class="agreement">
      已阅读并同意
      <view class="link" bind:tap="handleReadAgreement">《付费会员协议》</view>
    </view>
  </view>
  <view wx:if="{{ skuInfo.showPrice || skuInfo.showPrice === 0 }}" class="button-wrapper">
    <view class="check-btn">
      <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="handleBuy" bizDataMap="{{ bizDataMap }}">
        {{ btnText || '' }}
      </user-authorize>
    </view>
    <view class="promot-info">
      <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="handleBuyExperience" bizDataMap="{{ bizDataMap }}">
        <view class="experience-price">{{ skuInfo.showPrice }}元特价体验</view>
      </user-authorize>
      <view class="limit-discount first-limit" wx:if="{{ currentCycleSku.goodsSkuType !== 2}}">
        限时特价优惠
      </view>
    </view>
  </view>
  <view wx:else class="button-wrapper">
    <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="handleBuy" bizDataMap="{{ bizDataMap }}" style="width:100%">
      <van-button size="large" round block class="check-full-btn" color="linear-gradient(to right, #E2BB7C, #E8C388)" custom-style="border: 0px;color: #724804;">
        {{ btnText }}
        <view wx:if="{{ limitTime }}" class="limit-discount">限时优惠</view>
      </van-button>
    </user-authorize>
  </view>
  <view class="check-text upgrade-text" wx:if="{{ hasAlias && !isWaitReNewStatus }}">升级后，当前等级剩余有效期保留，可与商家协商退款</view>
</view>