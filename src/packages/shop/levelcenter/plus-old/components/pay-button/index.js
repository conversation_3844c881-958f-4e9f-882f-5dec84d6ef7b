import WscComponent from 'pages/common/wsc-component/index';
import { isNewIphone } from 'shared/utils/browser/device-type';
import { LevelGoodsSkuType } from '../../utils/handle';
import Toast from '@vant/weapp/dist/toast/toast';
import openWebView from 'shared/utils/open-web-view';

const isIphoneX = isNewIphone();

WscComponent({
  properties: {
    btnText: {
      type: String,
      value: '',
    },
    skuInfo: {
      type: Object,
      value: {},
    },
    linkTo: {
      type: String,
    },
    userLevel: {
      type: Object,
      value: {},
      observer(nv) {
        if (nv) {
          this.getLimitTime();
        }
      },
    },
    hasAlias: Boolean,
    nowLevelAlias: {
      type: String,
      observer(nv) {
        if (nv) {
          this.setYZData({
            isWaitReNewStatus: this.getIsWaitReNewStatus(),
          });
        }
      },
    },
    experienceRenew: Boolean,
    hasMobile: Boolean,
    bizDataMap: {
      type: Object,
      value: {},
    },
    currentCycleSku: {
      type: Object,
      value: {},
    },
  },
  data: {
    experiencePrice: false,
    limitTime: '',
    isIphoneX,
    checked: false,
    isWaitReNewStatus: false,
  },
  methods: {
    getLimitTime() {
      const { userLevel, nowLevelAlias, experienceRenew } = this.properties;
      const levelAlias = userLevel?.level?.levelAlias;
      const isExperienceLevel = userLevel?.isExperienceLevel || false;
      const limitTime =
        isExperienceLevel && levelAlias === nowLevelAlias && experienceRenew;
      this.setYZData({
        limitTime,
      });
    },
    getIsWaitReNewStatus() {
      const { userLevel, nowLevelAlias } = this.properties;
      const isExperienceLevel = userLevel?.isExperienceLevel;
      const isExpired = userLevel?.isExpired || false;
      const alias = userLevel?.level?.levelAlias;
      return alias === nowLevelAlias && !isExpired && !isExperienceLevel;
    },
    handleReadAgreement() {
      openWebView(
        'https://www.youzan.com/intro/rule/detail?alias=1odjry5d&pageType=rules'
      );
    },
    handleChecked(e) {
      this.setYZData({
        checked: e.detail,
      });
    },
    handleBuyExperience() {
      this.triggerEvent('experience');
    },
    handleBuy() {
      if (this.getIsWaitReNewStatus()) {
        this.triggerEvent('renew');
        return;
      }
      if (
        this.properties.currentCycleSku?.goodsSkuType ===
        LevelGoodsSkuType.Renewal
      ) {
        this.triggerEvent('copy');
        return;
      }
      if (!this.data.checked) {
        Toast('请先阅读并勾选协议');
        return;
      }
      this.triggerEvent('pay');
    },
    bindGetPhoneNumber() {
      this.triggerEvent('bindMobile');
    },
  },
});
