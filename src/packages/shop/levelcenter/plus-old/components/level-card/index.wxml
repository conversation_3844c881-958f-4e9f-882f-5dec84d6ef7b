<!-- 付费等级卡面 -->
<view style="{{ bgStyle }}" class="level-wrap">
  <view style="{{ maskStyle }}" class="bg-mask" />
  <view class="level-top">
    <view class="card-level">
      <text>{{ card.name }}</text>
      <text wx:if="{{ levelStatus === 'trial' }}" class="trial">体验</text>
      <view class="vaild-time">{{ vaildTime[levelStatus] }}</view>
    </view>
  </view>
  <view class="level-middle">
    <view wx:if="{{ levelStatus !== 'trial' && levelStatus !== 'member' }}" class="level-gift">
      <text class="level-num">{{ bestPirce }}</text>
      <text>元/{{ bestType }}起</text>
    </view>
  </view>
  <view class="level-bottom">
    <view wx:if="{{ levelStatus !== 'trial' }}" class="level-value">
      {{ receiveBenefit[levelStatus] }}
    </view>
  </view>
</view>