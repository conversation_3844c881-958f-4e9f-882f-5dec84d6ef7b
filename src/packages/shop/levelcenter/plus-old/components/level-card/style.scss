.bg-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 12px;
  overflow: hidden;
}

.level-wrap {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 17px 16px 16px 20px;
  height: 180px;
  color: #fff;
  box-sizing: border-box;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  background-size: cover;
  margin-right: 16px;
}

.level-top {
  font-size: 18px;
  font-weight: bold;
  position: relative;
  flex: 1;
  height: 26px;
  justify-content: space-between;

  .trial {
    display: inline-block;
    background: url('//img.yzcdn.cn/public_files/4c280810058fe0a1826ba4906d8cc370.png') no-repeat
      center/contain;
    font-size: 11px;
    color: #fff;
    text-align: center;
    line-height: 12px;
    padding: 3px 4px;
    margin-left: 4px;
    border-radius: 3px;
  }

  .level-btn {
    position: absolute;
    right: 0;
    top: 0;
    color: #323233;
    font-weight: bold;
    background-color: #fff;
    flex: 1;
    height: 30px;
    text-align: center;
    font-size: 12px;
    line-height: 30px;
    border-radius: 999px;

    &::after {
      border: none;
    }

    .van-button--defaul {
      border: 0;
    }

    .van-button__text {
      color: #323233;
      font-size: 14px;
      font-weight: 450;
    }
  }

  .level-btn-default {
    mix-blend-mode: screen;
  }
}

.level-middle {
  height: 50px;
  margin-bottom: 13px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  z-index: 1;

  .level-gift {
    margin-bottom: 10px;
    font-weight: bold;

    .level-num {
      font-size: 26px;
      padding-right: 4px;
    }

    span {
      font-size: 12px;
      margin-top: -10px;
    }
  }
}

.vaild-time {
  margin-top: 5px;
  font-size: 12px;
  font-weight: 400;
}

.level-bottom {
  font-size: 12px;
  position: relative;
  margin: 0 0 8px 0;

  .level-upgrade {
    position: absolute;
    right: 0;
    top: 0;
  }
}
