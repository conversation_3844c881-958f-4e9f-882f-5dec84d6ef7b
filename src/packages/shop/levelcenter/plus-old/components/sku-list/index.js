import WscComponent from 'pages/common/wsc-component/index';
import Toast from '@vant/weapp/dist/toast/toast';
import { UNIT_CYCLE_TEXT } from '../../utils/handle';
import { fetchLevelGoods, handleSkuListWithCalc } from './utils';

WscComponent({
  properties: {
    nowLevelAlias: {
      type: String,
      observer() {
        this.fetchLevelGoods();
        this.resetRadio();
        this.updateShow();
      },
    },
    userLevel: {
      type: Object,
      value: {},
      observer() {
        this.updateShow();
      },
    },
    nowLevelValue: {
      type: Number,
    },
  },
  data: {
    hideSkuList: true,
    current: 0,
    width: 130,
    height: 140,
    radio: 0,
    startPay: false,
    skuHandleLst: [],
    needBindMoblie: false,
    skuList: [],
    loading: false,
    bestPrice: null,
    unitCycle: UNIT_CYCLE_TEXT,
    hasMonthNotCheapest: false,
  },
  lifetimes: {
    attached() {
      const width = (wx.getSystemInfoSync().windowHeight - 22) / 6;
      const height = width * 1.08;
      this.setYZData({ width, height });
    },
  },
  methods: {
    updateShow() {
      const { userLevel, nowLevelAlias } = this.properties;
      const isExperienceLevel = userLevel?.isExperienceLevel;
      const isExpired = userLevel?.isExpired || false;
      const alias = userLevel?.level?.levelAlias;

      this.setYZData({
        hideSkuList:
          alias === nowLevelAlias && !isExpired && !isExperienceLevel,
      });
    },
    resetRadio() {
      this.setYZData({ radio: 0 });
    },
    toggle(e) {
      this.triggerEvent('onSkuChange', e.currentTarget?.dataset?.skuInfo);
      this.setYZData({ radio: e.currentTarget?.dataset?.index || 0 });
    },
    fetchLevelGoods() {
      this.setYZData({
        loading: true,
        skuList: [],
      });
      const alias = this.properties.nowLevelAlias;

      // 缓存数据 + 新数据请求
      const [cachedLevelInfo, fetchPromise] = fetchLevelGoods(
        [],
        alias,
        handleSkuListWithCalc
      );
      if (cachedLevelInfo) {
        this.formatLevelInfoForDisplay(cachedLevelInfo);
      }

      // 数据请求处理
      fetchPromise
        .then((data) => {
          this.formatLevelInfoForDisplay(data);
        })
        .catch((err) => {
          Toast(err);
        });
    },

    formatLevelInfoForDisplay(levelInfo) {
      const userLevelValue = this.properties.userLevel?.level?.levelValue;
      const currentLevelValue = this.properties.nowLevelValue;
      let { skuList } = levelInfo;
      if (userLevelValue && currentLevelValue < userLevelValue) {
        skuList = skuList.filter((i) => i.goodsSkuType !== 2);
      }
      this.setYZData({
        loading: false,
        skuList,
        bestPrice: levelInfo.lowestMonthPrice,
        hasMonthNotCheapest: levelInfo.hasMonthNotCheapest,
        radio: 0,
      });
      console.log('onSkuChange', skuList[0]);
      this.triggerEvent('onSkuChange', skuList[0]);
    },
  },
});
