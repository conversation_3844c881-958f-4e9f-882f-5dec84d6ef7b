<view wx:if="{{ !hideSkuList }}">
  <view wx:if="{{loading}}" class="set-choice-wrapper">
    <van-loading class="detail-loading" type="spinner" />
  </view>
  <view wx:else>
    <view class="swipe-chioce-wrapper">
      <view wx:if="{{skuList.length}}" class="task-label">可购套餐</view>
      <view class="sku-list">
        <view wx:for="{{skuList}}" wx:for-item="item" wx:for-index="index" wx:key="{{item.levelSkuId}}" class="choice-item {{ radio === index ? 'item-active':'' }}" style="width: {{width}}px;height: {{height}}px" bind:tap="toggle" data-index="{{index}}" data-sku-info="{{item}}">
          <sku-item item="{{item}}" has-month-not-cheapest="{{hasMonthNotCheapest}}" best-price="{{bestPrice}}" />
        </view>
      </view>
    </view>
  </view>
</view>