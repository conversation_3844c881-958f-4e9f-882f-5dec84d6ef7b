const app = getApp();

export const kdtId = app.getKdtId();

// 数据接口地址：http://zanapi.qima-inc.com/site/service/view/212661
// com.youzan.ebiz.mall.trade.buyer.api.service.TradeService.cacheOrderCreation
const State = {
  isWeapp: 1,
  delivery: {
    expressTypeChoice: 0,
    hasFreightInsurance: false,
    contacts: {
      recipients: app.getMobile(),
      tel: app.getMobile(),
    },
  },
  seller: {
    kdtId,
    storeId: 0, // 多网店下单
  },
  config: {
    containsUnavailableItems: false,
    paymentExpiry: 0,
    receiveMsg: true,
    usePoints: false,
    useWxpay: 1,
    // preWxPay: true, 极速下单参数
    paymentSuccessRedirect: '',
    forceZeroOrder: false,
  },
  source: {
    kdtSessionId: app.getSessionId(),
    platform: 'mobile',
    fromThirdApp: false,
    fromScrmCenter: true, // 表明从会员等级传入
  },
  // 暂时无用的参数
  // wxPayDiscount: _global.wxPayDiscount || 0,
  // cloudOrderExt: {
  //   extension: {},
  // },
  // ump: {
  //   coupon: {},
  // },
  // unavailableItems: [],
  // usePayAsset: {},
};

export default State;
