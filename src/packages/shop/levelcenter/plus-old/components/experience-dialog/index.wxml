<van-popup
  round
  custom-style="background-color: rgba(0, 0, 0, 0);"
  show="{{ visible }}"
  show-confirm-button="{{ false }}"
  class="info-dialog"
>
  <view class="experience-dialog-container">
    <view class="experience-bg">
      <view class="content">
        <view class="img" style="background-image: url({{ bgUrl }})"></view>
        <view class="prompt-title">特价体验<text class="experience-num">{{ skuInfo.experienceTime.termDays }}</text>天会员</view>
        <view class="prompt-label">特价体验，限时抢购</view>
      </view>

      <view class="dialog-button">
        <user-authorize
          authTypeList="{{ ['mobile'] }}"
          bind:next="handleToPay"
          bizDataMap="{{ bizDataMap }}"
        >
          <view class="benefit-btn">{{ skuInfo.experienceTime.termDays ? skuInfo.showPrice : '' }}元特价体验</view>
        </user-authorize>
      </view>

      <view class="operator-bar">
        <van-icon
          class="dialog-close"
          name="close"
          bindtap="handleCloseDialog"
        />
      </view>
    </view>
  </view>
</van-popup>