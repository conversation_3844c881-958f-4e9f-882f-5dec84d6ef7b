import WscComponent from 'pages/common/wsc-component/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

WscComponent({
  properties: {
    value: {
      type: Boolean,
      value: false,
      observer(nv, ov) {
        if (nv !== ov) {
          this.setYZData({
            visible: nv
          });
        }
      }
    },
    skuInfo: {
      type: Object,
      default: () => ({})
    },
    hasMobile: Boolean,
    bizDataMap: {
      type: Object,
      value: {},
    },
  },
  data: {
    visible: false,
    bgUrl: cdnImage('https://b.yzcdn.cn/public_files/e63c79fbe797c4304e7d7c1cd925abb1.png')
  },
  methods: {
    handleCloseDialog() {
      this.setYZData({
        visible: false
      });
    },
    handleToPay() {
      this.triggerEvent('experience');
      this.handleCloseDialog();
    },
    bindGetPhoneNumber() {
      this.triggerEvent('bindMobile');
    }
  },
  attached() {}
});
