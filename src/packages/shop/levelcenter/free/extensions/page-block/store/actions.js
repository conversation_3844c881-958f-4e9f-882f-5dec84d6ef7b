import { getSubscribePreInfo } from '@/helpers/subscribe';

import Toast from '@youzan-open/vant-tee/dist/toast/toast';

import { getPlugins } from '@youzan/ranta-helper-tee';
import Args from '@youzan/weapp-utils/lib/args';
import get from '@youzan/weapp-utils/lib/get';

import { SCENE_MARKET_SCENE } from 'constants/member-constants';

import { getGuideBizDataMap } from 'utils/guide';
import openWebView from 'utils/open-web-view';
import {
  getIsNeedSubscribeInSceneMarketActivity,
  subscribeInSceneMarketActivity,
} from 'utils/scene-market-subscribe-message';

import * as API from '../api';
import { levelAdapter } from '../../../../level-common/utils/adapter';
import {
  fetchComponentsOfType,
  getCustomerAuth,
} from '../../../../level-common/utils/api';
import {
  jumpTypeEnum,
  LevelMode,
  LevelProgressSceneEnum,
  LevelType,
  SOURCE_TYPE_UC,
} from '../../../../level-common/utils/constant';
import {
  findNextLevel,
  getNeedMoreGrowth,
  getRecruitMemberParams,
  isShowBottomBtn,
  showInformationDialog,
  tryToBindShoppingGuideRelation,
} from '../../../../level-common/utils/util';

const app = getApp();

const actions = {
  /** 判断商家是否启用微信会员卡2.0 */
  checkWeixinCardEnabled({ state, commit }, payload) {
    const { ctx } = payload;

    return app.getShopInfo().then(({ chainStoreInfo = {} }) => {
      const { isMultiOnlineShop } = chainStoreInfo;
      const rootKdtId = isMultiOnlineShop ? app.getHQKdtId() : state.kdtId;
      state.rootKdtId = rootKdtId;
      return API.checkWeixinCardEnabled(rootKdtId)
        .then((response) => {
          commit('SET_IS_WEIXIN_CARD_ENABLED', {
            isWeixinCardEnabled: response.value,
          });

          ctx.data.isWeixinCardEnabled = response.value;
        })
        .catch(() => {
          commit('SET_IS_WEIXIN_CARD_ENABLED', {
            isWeixinCardEnabled: false,
          });

          ctx.data.isWeixinCardEnabled = false;
        });
    });
  },

  /** 判断是否是手动退出等级 */
  checkRemove({ commit }) {
    return API.checkRemoveLevel()
      .then((res) => {
        commit('SET_IS_REMOVED', { isRemoved: res.value });
      })
      .catch((e) => {
        wx.showToast({
          title: e.msg,
          icon: 'none',
          duration: 1000,
        });
      });
  },

  getJoinProcessScene({ state, dispatch }, { hasLevel }) {
    const { pageQuery } = state;
    const { kdt_id, sub_kdt_id, bizType } = pageQuery;
    // 1. 社店推广链接匹配逻辑
    const useDefaultJoinConf =
      (!!sub_kdt_id && !!kdt_id && Object.keys(pageQuery).length === 2) ||
      (!!kdt_id && !sub_kdt_id && Object.keys(pageQuery).length === 1);

    // 2. CRM推广链接匹配逻辑（招募会员）
    const useCrmLevelJoinConf = bizType && kdt_id;

    // 3. CRM推广链接匹配逻辑（门店拉新扫码入会），暂时用不到
    // const useCrmOfflineQrcodeJoinConf = +recruitType === OFFLINE_RECRUIT_TYPE;

    if (!useDefaultJoinConf && !useCrmLevelJoinConf) return;

    state.needCiSourceType = true;
    state.joinProcessScene = LevelProgressSceneEnum.DEFAULT_JOIN;
    // 如果配置了新资料页则走新资料页入会
    dispatch('fetchFillConf', { hasLevel });
  },

  getCustomerLevelProgress({ state }) {
    API.getCustomerLevelProgress().then((res) => {
      if (!res) return;
      state.levelProgressList = res.upLevelProgressDTOList;
      state.beMemberTip = res.welcomeMessage;
    });
  },

  fetchUserLevel({ state, commit, dispatch }, payload) {
    const { pageQuery, globalCustomLoading } = state;
    const { ctx, isRejoinMember } = payload;
    const recruitParams = getRecruitMemberParams(pageQuery);
    commit('SET_RECRUIT_PARAMS', { recruitParams });
    tryToBindShoppingGuideRelation(pageQuery);
    if (!globalCustomLoading) {
      Toast.loading({
        duration: 0,
        forbidClick: true,
        message: '加载中...',
        loadingType: 'spinner',
      });
    }
    return API.getUserLevelInfo({
      ...recruitParams,
      type: LevelType.FREE,
      withConsumerData: '1', // * 获取用户近x个月的消费金额数据
    })
      .then((data) => {
        API.getBenefitAssetInfo().then((res) => {
          commit('SET_ASSETS_INFO', { assetsInfo: res });
        });

        commit('SET_USER_LEVEL_AND_SWIPER', {
          userLevel: data,
          hasLevel: !!data.identityNo,
          isConsume: data.mode === LevelMode.CONSUME,
          mode: data.mode,
        });
        dispatch('getJoinProcessScene', { hasLevel: !!data.identityNo });

        ctx.data.userLevel = data;

        dispatch('changeMemberGiftStatusByData', { userLevel: data });

        if (isRejoinMember && !!data.identityNo) {
          // 调用子组件事件 rejoinMethod
          app.trigger('level-center:rejoin');
        }
        dispatch('fetchLevelList', { ctx, ...data });
      })
      .finally(() => {
        Toast.clear();
      });
  },

  changeMemberGiftStatusByData({ commit }, payload) {
    const { userLevel } = payload;
    const hideMemberGiftModule = wx.getStorageSync('hideMemberGiftModule');
    let p = Promise.resolve(false);
    // 这版先隐藏成为会员后组件内容 等优惠券接口更新
    if (!hideMemberGiftModule) {
      const request = userLevel?.level?.levelId
        ? API.getCurrentBenefitInfo
        : API.getLevelGiftBag;
      p = request().then((data) => {
        commit('SET_BENEFIT_GIFT', { benefitGift: data });
        // 有数据返回 则显示组件
        return (
          data.couponList?.length > 0 ||
          data.presentList?.length > 0 ||
          data.points?.points > 0
        );
      });
    }
    p.then((res) =>
      commit('SET_SHOW_NEW_MEMBER_GIFT', { showNewMemberGift: res })
    );
  },

  fetchLevelList({ state, commit, dispatch }, { ctx, ...nowLevel }) {
    API.getLevelList({ type: LevelType.FREE, withConsumerData: 1 })
      .then((data) => {
        data.levels = nowLevel;
        const level = levelAdapter(data);
        const groupAlias = get(data, 'levelList.[0].levelGroup.alias', '');
        const nextCard = findNextLevel(level.cards, level.nowLevel);

        const showInfo = showInformationDialog(nowLevel, data.hasCompleted);
        const needMoreGrowth = getNeedMoreGrowth(level, nextCard);

        // 储值模式下行动按钮文案&跳转链接适配 开始
        // 设置了入门门槛，用户未满足入会门槛（即 非会员），
        const { userLevel, pageQuery, isConsume } = state;

        const isMember = !!(userLevel && userLevel.identityNo);

        //  vip1's levelGapInfo
        const joinGapInfo = get(level.cards, `[0].levelGapInfo`, {});

        const hasThreshold = get(
          level.cards,
          `[0].levelGapInfo.hasEnterThreshold`,
          false
        );

        const neededMoreStoreBalance =
          userLevel.mode === LevelMode.STORE && !isMember && hasThreshold;
        // 储值模式下行动按钮文案&跳转链接适配 结束

        // 顶部引导完善信息提示条展示逻辑--已入会 && 未完成必填信息
        const levelGapInfo = get(level.cards, `[0].levelGapInfo`, {});

        const isShow = !levelGapInfo.isRegistrationInfoCompleted && isMember;

        const showRegisterBtn = isShowBottomBtn(state, {
          neededMoreStoreBalance,
          hasCompleted: data.hasCompleted,
          reachThreshold: level.reachThreshold,
        });

        const redirectUrl = dispatch('getFillUrl');

        commit('SET_LEVEL_LIST_INFO', {
          requireMobile: get(
            data,
            'levelList[0].levelV2List[0].levelGrantConditionList[1].registerCondition.requireMobile',
            false
          ),
          cards: level.cards,
          isFullLevel: level.cards.length === 1,
          groupAlias,
          nowLevel: level.nowLevel,
          redirectUrl,
          hasCompleted: data.hasCompleted,
          nextCard,
          showInfo,
          needMoreGrowth,
          reachThreshold: level.reachThreshold,
          identityNo: get(data, 'levels.identityNo'),
          showRegisterCompleteBar: isShow,
          neededMoreStoreBalance,
          showRegisterBtn,
          pointsInfo: data.pointsInfo,
          joinGapInfo,
        });

        ctx.data.groupAlias = groupAlias;
        ctx.data.neededMoreStoreBalance = neededMoreStoreBalance;
        ctx.data.showRegisterBtn = showRegisterBtn;

        commit('SET_CURRENT_INDEX', 0);
        commit('CHANGE_NOW_LEVEL_BENEFIT', level.cards[0]);
        if (isConsume) {
          commit('CHANGE_UPGRADE_MISSION_LIST', {
            upgradeMissionList: get(level, 'cards[0].upgradeMissionList', []),
          });
        }
        const isAllDisabled = (level.cards || []).every(
          (levelCard) => !levelCard.isEnabled
        );
        if (isAllDisabled) {
          wx.showToast({
            title: '会员等级已停用',
            icon: 'error',
            duration: 1000,
          });
        }
        commit('CHANGE_READY', true);
      })
      .catch(() => {
        wx.showToast({
          title: '加载会员等级失败，请重试',
          icon: 'error',
          duration: 1000,
        });
      });
  },

  // 判断当前会员在当前店铺是否可享权益，不符合则展示提示条
  checkBenefitMatch({ commit }) {
    return API.checkBenefitMatch().then(({ matched }) => {
      commit('SET_SHOW_BENEFITS_ALERT', { showBenefitsAlert: !matched });
    });
  },

  initIsThirdPartyCenter({ commit }) {
    API.getIsThirdPartyCenter().then((res) => {
      commit('SET_IS_THIRD_PARTY_CENTER', { isThirdPartyCenter: res.value });
    });
  },

  checkIsFromSubscribeAndCanSubscribe({ commit }) {
    // * 获取订阅能力 & 是否已经订阅过
    getIsNeedSubscribeInSceneMarketActivity()
      .then((isNeed) => {
        getSubscribePreInfo({
          scene: SCENE_MARKET_SCENE,
        }).then((res) => {
          commit('SET_TEMPLATE_LIST', res);
          commit('SET_NEED_SUBSCRIBE', {
            needSubscribe: isNeed && res.length,
          });
        });
      })
      .catch((err) => {
        Toast(err);
      });
  },

  // * 有活动时通知我按钮订阅采集
  handleSubscribe({ state, commit }) {
    if (state.subscribing) return;

    commit('SET_SUBSCRIBING', true);

    subscribeInSceneMarketActivity({
      logParam: {
        subscribe_page: '会员中心',
        subscribe_type: '活动通知',
      },
      templateIdList: state.templateIdList,
      onSuccess: () => {
        commit('SET_NEED_SUBSCRIBE', { needSubscribe: false });
        commit('SET_SUBSCRIBING', false);
      },
      onFail: () => {
        // * 订阅消息失败处理函数
        commit('SET_SHOW_SUBSCRIBE_FAIL', { showSubscribeFail: true });
        // 失败弹窗的动画过程延迟
        setTimeout(() => {
          commit('SET_NEED_SUBSCRIBE', false);
        }, 1000);
      },
      onClose: () => {
        commit('SET_NEED_SUBSCRIBE', false);
      },
    });
  },

  // * 订阅消息执行回调
  handleSubscribeCallback({ commit }, ev) {
    const isSuccess = ev.detail;

    if (isSuccess) {
      commit('SET_NEED_SUBSCRIBE', { needSubscribe: false });
    } else {
      // * 订阅消息失败处理函数
      commit('SET_SHOW_SUBSCRIBE_FAIL', { showSubscribeFail: true });
    }
  },

  // * 关闭订阅失败弹窗
  closeSubscribeFailPopup({ commit }) {
    commit('SET_SHOW_SUBSCRIBE_FAIL', { showSubscribeFail: false });
  },

  handleSwipeChange({ state, commit }, e) {
    if (e.type !== 'change') return;
    const { current } = e.detail;
    const level = get(state.cards, `${current}`, {});
    commit('SET_CURRENT_INDEX', current);
    commit('CHANGE_NOW_LEVEL_BENEFIT', level);
    commit('CHANGE_UPGRADE_MISSION_LIST', {
      upgradeMissionList: get(level, 'upgradeMissionList', []),
    });
  },

  missionsNumber({ state }) {
    state.showMission = false;
  },

  jumpToMissionCenter({ state }) {
    openWebView(state.toMissionCenter);
  },

  handleAfterFetchMission({ commit }) {
    commit('SET_FETCH_MISSION_FINISHED', true);
  },

  hideMemberGift({ commit }) {
    commit('SET_SHOW_NEW_MEMBER_GIFT', { showNewMemberGift: false });
  },

  jumpCrmWechatCardPortal({ state }) {
    const { joinProcessScene, kdtId, rootKdtId, pageQuery } = state;
    const { bizType } = pageQuery;
    openWebView('/wscuser/ump/wechat-card-portal', {
      query: {
        hqKdtId: rootKdtId,
        kdt_id: kdtId,
        joinProcessScene,
        bizType,
      },
    });
  },

  getFillUrl({ state }) {
    const { pageQuery, joinProcessScene, needCiSourceType, kdtId } = state;
    const extraParams = {
      joinProcessScene,
    };
    needCiSourceType && (extraParams.ci_source_type = SOURCE_TYPE_UC);

    const redirectUrl = Args.add('/wscuser/levelcenter/fill', {
      kdt_id: kdtId,
      fromScene: 'complete',
      fromBiz: 'levelcenter',
      ...pageQuery,
      ...extraParams,
    });

    return redirectUrl;
  },

  fetchFillConf({ state, dispatch }, { hasLevel }) {
    const { joinProcessScene, isRedirecting, kdtId } = state;
    if (isRedirecting) return;
    state.isRedirecting = true;
    const { dmc } = getPlugins();
    API.getFillPageConf({
      scene: joinProcessScene,
    }).then(({ joinConf, redirectUrlMap = {} }) => {
      const {
        fillAttrEnterMemberCenter = false,
        beMemberRedirectPage,
        isCrmShop,
      } = joinConf || {};
      state.joinConf = joinConf;
      state.isCrmShop = isCrmShop;
      state.redirectUrlMap = redirectUrlMap;
      if (fillAttrEnterMemberCenter) {
        if (
          hasLevel &&
          beMemberRedirectPage !== jumpTypeEnum.WECHATMEMBERCARD
        ) {
          if (
            redirectUrlMap.weappUrl &&
            beMemberRedirectPage !== jumpTypeEnum.MEMBERCENTER
          ) {
            const isToHome = beMemberRedirectPage === jumpTypeEnum.HOME;
            const isLevelCenter =
              beMemberRedirectPage === jumpTypeEnum.USERCENTER;
            if (isToHome || isLevelCenter) {
              dmc.switchTab(isToHome ? 'Home' : 'Usercenter').catch(() => {
                // eslint-disable-next-line @youzan/dmc/wx-check
                wx.reLaunch({
                  url: redirectUrlMap.weappUrl,
                });
              });
            } else {
              dmc.navigate(redirectUrlMap.weappUrl);
            }
          }
        } else if (isCrmShop) {
          // crm商家 ,非开通微信会员卡场景跳转到注册页，开通微信会员卡场景跳转微信会员卡注册页
          if (state.isWeixinCardEnabled) {
            dispatch('jumpCrmWechatCardPortal', { hasLevel });
          } else {
            openWebView(dispatch('getFillUrl'));
          }
        } else {
          dmc.navigate(
            `/packages/shop/levelcenter/member-fill-page/index?kdt_id=${kdtId}&shopAutoEnter=1&eT=${Date.now()}`
          );
        }
      }
    });
  },

  goToCompleteInfo({ state }) {
    const { kdtId, redirectUrl, groupAlias, pageQuery } = state;

    const query = {
      kdtId,
      alias: groupAlias,
      fromScene: 'complete',
      ...getGuideBizDataMap(pageQuery), // 携带导购标
    };

    tryToBindShoppingGuideRelation(pageQuery).then(() => {
      openWebView(redirectUrl, {
        query: {
          ...query,
          fromBiz: 'levelcenter',
          eT: Date.now(),
        },
      });
    });
  },

  handleProtocol({ state }) {
    if (state.isFeatureMemberCenter) {
      import('@youzan/passport-protocol')
        .then(({ InvokeProtocol }) => ({
          instance: new InvokeProtocol(),
        }))
        .then(({ instance }) => {
          instance
            .auth()
            .then(() => {
              console.log('已签署协议');
            })
            .catch(() => {});
        });
    }
  },

  fetchData(
    { commit, dispatch },
    { branchStoreOpenOnline, isRejoinMember, ctx }
  ) {
    /**
     * 在读取会员信息前，先调用账号提供的方法，清除会员协议的接口调用缓存，
     * 解决重新入会读取会员协议指向请求缓存的问题（后续账号端接口变更调整，需要修改）
     */
    app.clearProtocolCache().then(() => {
      commit('CHECK_BIND_PHONE');
      // * 获取用户当前等级信息，获取当前是否是退出等级
      const requestArr = [
        dispatch('checkWeixinCardEnabled', { ctx }),
        dispatch('checkRemove'),
        dispatch('fetchUserLevel', { ctx, isRejoinMember }),
        dispatch('getCustomerLevelProgress'),
        dispatch('initFeatureMemberCenterData', {
          branchStoreOpenOnline,
          ctx,
        }),
      ];
      Promise.all(requestArr).finally(() => {
        setTimeout(() => {
          commit('HIDE_LOADING');
        }, 400);
      });
      // * 获取判断是否需要手机授权信息
      getCustomerAuth().then((res) => {
        commit('SET_MOBILE_AUTHORIZED', {
          mobileAuthorized: res.canMobileUse,
        });
      });
    });
  },

  initFeatureMemberCenterData({ dispatch, commit }, payload) {
    const { branchStoreOpenOnline, ctx } = payload;
    const promise = app.globalData.brandFeaturePromise;
    if (promise) {
      promise
        .then(({ freeMemberCenter }) => {
          const isFeatureMemberCenter = !!freeMemberCenter;
          if (!isFeatureMemberCenter) {
            dispatch('fetchComponentsOfType', { branchStoreOpenOnline, ctx });
            return;
          }
          commit('SET_IS_FEATURE_MEMBER_CENTER', true);
          dispatch('setBrandFeatureData', { freeMemberCenter, ctx });
        })
        .catch(() => {
          commit('SET_IS_FEATURE_MEMBER_CENTER', false);
          dispatch('fetchComponentsOfType', { branchStoreOpenOnline, ctx });
        });
      return;
    }
    dispatch('fetchComponentsOfType', { branchStoreOpenOnline, ctx });
  },

  setBrandFeatureData({ dispatch }, { freeMemberCenter, ctx }) {
    const { components = [] } = freeMemberCenter;
    const configData = components.find((i) => i.type === 'config');
    const navigationBarData = components.find(
      (i) => i.type === 'navigationbar_config'
    );
    // 设置标题和页面背景色
    if (configData) {
      ctx.data.pageTitle = configData.title || '会员中心';
    }
    ctx.data.featureComponents = components;
    if (navigationBarData) {
      ctx.data.navigationBarConfigData = navigationBarData;
    }

    dispatch('handleProtocol');
  },

  fetchComponentsOfType(_, { branchStoreOpenOnline, ctx }) {
    if (branchStoreOpenOnline) {
      fetchComponentsOfType('member_free').then((comps) => {
        ctx.data.featureComponents = comps;
      });
    }
  },

  // 重新加载数据
  reloadData({ state, dispatch, commit }, payload) {
    const { isRejoinMember, ctx } = payload;
    const { branchStoreOpenOnline } = state;
    // cards没有唯一标识，用unique做为key,直接覆盖会有更新问题，
    // 先置空或者用唯一标识作为key
    commit('SET_CARDS_EMPTY_ARRAY');
    dispatch('fetchData', {
      branchStoreOpenOnline,
      isRejoinMember,
      ctx,
    });
  },
};

export default actions;
