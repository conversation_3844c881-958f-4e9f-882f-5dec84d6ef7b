/* eslint @youzan-open/tee/valid-same-extension-import: "off" */
import { toMissionCenter } from '../../../../level-common/utils/adapter';
import {
  goodsRecommendTitleConfig,
  LevelMode,
} from '../../../../level-common/utils/constant';

const app = getApp();

const state = {
  toMissionCenter,
  goodsRecommendTitleConfig,

  hasMobile: false,
  hasCompleted: true, // 是否完善资料
  showInfo: false, // 弹窗展示完善信息
  showMission: true, // 是否显示成长任务
  isShow: false, // 会员码弹窗
  isWeixinCardEnabled: false,
  needMoreGrowth: false,
  requireMobile: false,
  mobileAuthorized: false,
  qrCodeExpired: false,
  fetchMissionFinished: false,
  isSupportGoodsRecommend: false,
  isConsume: false, // * 是否为消费行为模式
  hasLevel: false, // * 是否已经有等级（达到了LV.1）
  isFullLevel: false, // * 当前是否已经满级
  isThirdPartyCenter: false, // * 等级规则计算中心是否三方系统；
  isRemoved: false,
  showNewMemberGift: false, // 是否显示新会员礼包
  isUnionCode: false, // 是否是多码合一流程中
  showTabBar: false,
  needSubscribe: false, // * 是否能采集订阅消息
  showSubscribeFail: false, // * 场景营销订阅消息采集失败
  subscribing: false, // 订阅消息采集中标识，用于防止按钮重复点击触发
  templateIdList: [], // 订阅消息模板id
  reachThreshold: true,
  showBenefitsAlert: false, // 默认不展示会员与店铺不匹配文案提示
  showRegisterCompleteBar: false, // 是否展示顶部引导完善信息的提示
  neededMoreStoreBalance: false, // 不满足储值模式入会条件，需要引导用户去充值中心
  showRegisterBtn: false,
  branchStoreOpenOnline: false, // 是否开通网店渠道
  isReady: false,
  isFeatureMemberCenter: false,
  needCiSourceType: false, // 跳转有赞会员卡是否需要携带 ci_source_type
  isCrmShop: false,
  joinConf: {},
  redirectUrlMap: {},

  kdtId: app.getKdtId(),
  deviceType: app.deviceType || '',

  redirectUrl: null,
  current: 0,
  groupAlias: '', // 等级组alias
  nowLevelAlias: '', // 滑动等级alias
  mode: LevelMode.GROWTH, // 当前会员等级模式
  identityNo: '',
  cardName: '',
  guideScene: '',

  upgradeMissionList: [], // * 初始化当前等级的升级任务列表
  recruitParams: {},
  pageQuery: {},
  assetsInfo: {},
  userLevel: {},
  userInfo: {},
  cards: [], // 等级列表
  nowLevel: {}, // 当前等级
  nowBenefitList: [], // 滑动等级权益
  nextCard: {},
  pointsInfo: {},
  joinGapInfo: {},
  benefitGift: {}, // 新会员礼包的权益
  bizDataMap: {}, // 携带给授权组件的业务信息
  crmFromScenePlanId: '',
  memberCodeRefreshInterval: 30000, // 会员码刷新的间隔，单位为 ms
  loading: true,
  globalCustomLoading: true, // 全局自定义加载动画
  isRedirecting: false,
  rootKdtId: '',
  joinProcessScene: '',
  levelProgressList: [],
  beMemberTip: '',
};

export default state;
