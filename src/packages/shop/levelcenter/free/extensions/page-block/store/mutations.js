/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import get from '@youzan/weapp-utils/lib/get';

import { addLog } from '../../../../level-common/utils/util';

const app = getApp();

// 以下方法顺序根据state自上而下排列
const mutations = {
  SET_KDT_ID(state, payload) {
    state.kdtId = payload.kdtId;
  },

  SET_LEVEL_LIST_INFO(state, payload) {
    const {
      requireMobile,
      cards,
      isFullLevel,
      groupAlias,
      nowLevel,
      redirectUrl,
      hasCompleted,
      nextCard,
      showInfo,
      needMoreGrowth,
      reachThreshold,
      identityNo,
      showRegisterCompleteBar,
      neededMoreStoreBalance,
      showRegisterBtn,
      pointsInfo,
      joinGapInfo,
    } = payload;
    state.requireMobile = requireMobile;
    state.cards = cards;
    state.isFullLevel = isFullLevel;
    state.groupAlias = groupAlias;
    state.nowLevel = nowLevel;
    state.redirectUrl = redirectUrl;
    state.hasCompleted = hasCompleted;
    state.nextCard = nextCard;
    state.showInfo = showInfo;
    state.needMoreGrowth = needMoreGrowth;
    state.reachThreshold = reachThreshold;
    state.identityNo = identityNo;
    state.showRegisterCompleteBar = showRegisterCompleteBar;
    state.neededMoreStoreBalance = neededMoreStoreBalance;
    state.showRegisterBtn = showRegisterBtn;
    state.pointsInfo = pointsInfo;
    state.joinGapInfo = joinGapInfo;
  },

  SET_USER_LEVEL_AND_SWIPER(state, payload) {
    state.userLevel = payload.userLevel;
    state.hasLevel = payload.hasLevel;
    state.isConsume = payload.isConsume;
    state.mode = payload.mode;
  },

  SET_IS_WEIXIN_CARD_ENABLED(state, payload) {
    state.isWeixinCardEnabled = payload.isWeixinCardEnabled;
  },

  SET_USER_INFO(state, payload) {
    state.userInfo = payload.userInfo;
  },

  SET_CARDS_EMPTY_ARRAY(state) {
    state.cards = [];
  },

  CHANGE_NOW_LEVEL_BENEFIT(state, level) {
    state.nowBenefitList = get(level, 'card.name') ? level.benefitList : [];
    state.nowLevelAlias = level.card.alias;
  },

  SET_CURRENT_INDEX(state, current) {
    state.current = current;
  },

  SET_IS_SHOW(state) {
    state.isShow = false;
  },

  SET_CARD_NAME(state, payload) {
    state.cardName = payload.cardName;
  },

  INIT_PAGE_DATA(state, payload) {
    state.guideScene = payload.guideScene;
    state.pageQuery = payload.pageQuery;
    state.crmFromScenePlanId = payload.crmFromScenePlanId;
    state.isUnionCode = payload.isUnionCode;
    state.bizDataMap = payload.bizDataMap;
    state.globalCustomLoading = payload.globalCustomLoading;
  },

  SET_ASSETS_INFO(state, payload) {
    state.assetsInfo = payload.assetsInfo;
  },

  SET_MOBILE_AUTHORIZED(state, payload) {
    state.mobileAuthorized = payload.mobileAuthorized;
  },

  SET_BIND_MOBILE(state, payload) {
    state.hasMobile = payload.hasMobile;
    state.mobileAuthorized = payload.mobileAuthorized;
  },

  CHECK_BIND_PHONE(state) {
    let showBindPhoneNumber = false;
    if (!app.getBuyerId()) {
      showBindPhoneNumber = true;
    }
    state.hasMobile = !showBindPhoneNumber;
  },

  SET_FETCH_MISSION_FINISHED(state, payload) {
    state.fetchMissionFinished = payload;
  },

  SET_IS_SUPPORT_GOODS_RECOMMEND(state, payload) {
    state.isSupportGoodsRecommend = payload.isSupportGoodsRecommend;
  },

  SET_RECRUIT_PARAMS(state, payload) {
    state.recruitParams = payload.recruitParams;
  },

  CHANGE_UPGRADE_MISSION_LIST(state, payload) {
    state.upgradeMissionList = payload.upgradeMissionList;
  },

  SET_HAS_LEVEL(state, payload) {
    state.hasLevel = payload.hasLevel;
  },

  SET_IS_THIRD_PARTY_CENTER(state, payload) {
    state.isThirdPartyCenter = payload.isThirdPartyCenter;
  },

  SET_BENEFIT_GIFT(state, payload) {
    state.benefitGift = payload.benefitGift;
  },

  SET_IS_REMOVED(state, payload) {
    state.isRemoved = payload.isRemoved;
  },

  SET_SHOW_NEW_MEMBER_GIFT(state, payload) {
    state.showNewMemberGift = payload.showNewMemberGift;
  },

  CHECK_TAB_BAR_STATUS(state) {
    app.isSwitchTab().then((isTabPage) => {
      isTabPage &&
        addLog({
          et: 'view', // 事件类型
          ei: 'member_nav', // 事件标识
          en: '会员中心底导曝光', // 事件名称
        });
      state.showTabBar = isTabPage;
    });
  },

  SET_NEED_SUBSCRIBE(state, payload) {
    state.needSubscribe = payload.needSubscribe;
  },

  SET_SHOW_SUBSCRIBE_FAIL(state, payload) {
    state.showSubscribeFail = payload.showSubscribeFail;
  },

  SET_SUBSCRIBING(state, payload) {
    state.subscribing = payload;
  },

  SET_TEMPLATE_LIST(state, payload) {
    state.templateIdList = payload;
  },

  SET_SHOW_BENEFITS_ALERT(state, payload) {
    state.showBenefitsAlert = payload.showBenefitsAlert;
  },

  SET_BRANCH_STORE_OPEN_ONLINE(state, payload) {
    state.branchStoreOpenOnline = payload.branchStoreOpenOnline;
  },

  CHANGE_READY(state, payload) {
    state.isReady = payload;
  },

  SET_IS_FEATURE_MEMBER_CENTER(state, payload) {
    state.isFeatureMemberCenter = payload;
  },

  HIDE_LOADING(state) {
    state.loading = false;
  },
};

export default mutations;
