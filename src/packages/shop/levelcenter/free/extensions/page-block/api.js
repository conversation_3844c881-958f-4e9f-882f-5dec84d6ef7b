import { requestWithGuideParams } from 'utils/guide';

const app = getApp();
const BASE_URL = '/intro/api/shop';

export const getCustomerLevelProgress = () => {
  return app.request({
    path: '/wscuser/levelcenter/api/getCustomerLevelProgress.json',
  });
};

export function getShopConfig(query) {
  return app.request({
    method: 'GET',
    path: `${BASE_URL}/getShopConfig`,
    query,
  });
}

// 获取当前用户的等级
export const getUserLevelInfo = (data) => {
  return requestWithGuideParams({
    path: '/wscuser/levelcenter/api/userLevelDetail.json',
    data,
  });
};

// 获取该店铺的免费等级
export const getLevelList = (data) => {
  return app.request({
    path: '/wscuser/levelcenter/api/getLevelListV2.json',
    data,
  });
};

export const getMemberQR = (data) => {
  return app.request({
    path: '/wscuser/memberlevel/api/genQrAndBaCode.json',
    data,
  });
};

export const getBenefitAssetInfo = (data) => {
  return app.request({
    path: '/wscuser/level/api/getBenefitAssetInfo.json',
    data,
  });
};

// * 是否计算中心在三方系统
export const getIsThirdPartyCenter = () => {
  return app.request({
    path: '/wscuser/level/api/isThirdPartyCenterLevel.json',
  });
};

// 获取免费等级1礼包
export const getLevelGiftBag = (data) => {
  return app.request({
    path: '/wscuser/level/api/getLevelGiftBag.json',
    data,
  });
};

// 判断商家是否启用微信会员卡2.0
export const checkWeixinCardEnabled = (kdtId) => {
  return app.request({
    path: '/wscuser/wx/card-enabled.json',
    data: { kdtId },
  });
};

// 获取当前等级免费权益的赠品，优惠券，积分信息
export const getCurrentBenefitInfo = (data) => {
  return app.request({
    path: '/wscuser/level/api/getCurrentBenefitInfo.json',
    data,
  });
};
// 退出当前等级
export function removeLevel(data) {
  return app.request({
    path: '/wscuser/level/api/removeLevel.json',
    data,
  });
}

// 查询是否是手动退出的免费等级
export function checkRemoveLevel() {
  return app.request({
    path: '/wscuser/level/api/checkRemoveLevel.json',
  });
}

// 重新入会
export const recalculateUserLevel = (data) => {
  return requestWithGuideParams({
    path: '/wscuser/level/api/rejoin.json',
    data,
    method: 'post',
  });
};

// * 查询当前店铺场景营销活动轮播列表
export const getPlanList = () => {
  return app.request({
    path: '/wscuser/level/api/queryUnderwayPlan.json',
  });
};

export const checkBenefitMatch = () => {
  return app.request({
    path: '/wscuser/membercenter/checkBenefitMatch.json',
  });
};

export const getFillPageConf = (data) => {
  return app.request({
    path: '/wscuser/levelcenter/api/getFillPageConf',
    data,
  });
};
