{"name": "@wsc-tee-user/level-center-free-page-block", "extensionId": "@wsc-tee-user/level-center-free-page-block", "framework": "weapp", "platform": ["weapp"], "lifecycle": ["beforePageMount", "onPageShow", "onPageScroll", "onShareAppMessage", "onPullDownRefresh"], "data": {"provide": {"pageTitle": ["r", "w"], "featureComponents": ["r", "w"], "navigationBarConfigData": ["r", "w"], "userLevel": ["r", "w"], "showRegisterBtn": ["r", "w"], "groupAlias": ["r", "w"], "neededMoreStoreBalance": ["r", "w"], "isWeixinCardEnabled": ["r", "w"], "pageQuery": ["r", "w"]}, "consume": {"shopConfigs": ["r"], "memberConfig": ["r"], "isShowMemberCode": ["r", "w"], "isWeShop": ["r"]}}, "widget": {"provide": ["JoinGift", "RegisterButton", "JoinGiftRegisterButton"], "consume": ["ShowcaseContainer", "JoinGift", "RegisterButton", "JoinGiftRegisterButton", "MemberCodePopup"], "default": "Main"}, "process": {"define": ["beforeOutMemberLevel", "beforeQuitMemberLevel", "showMemberLevelBenefits", "reloadData"], "invoke": ["beforeOutMemberLevel", "beforeQuitMemberLevel", "showMemberLevelBenefits", "showLiveCodeAddFansPop", "beforeSignInMemberAddQuery"]}, "event": {"emit": ["removeLevel"]}, "lambda": {"consume": ["checkIsWeShop", "getWeShopBizDataMap"]}}