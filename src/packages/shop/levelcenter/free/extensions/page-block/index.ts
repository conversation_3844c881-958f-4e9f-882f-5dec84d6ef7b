// @ts-nocheck
/* eslint @youzan-open/tee/valid-same-extension-import: "off", @youzan-open/tee/no-platform-field: "off" */
import Args from '@youzan/weapp-utils/lib/args';
import {
  checkOnlineBranchStore,
  checkOfflineBranchStore,
  checkPartnerStore,
} from '@youzan/utils-shop';
import { mapState, mapActions } from '@ranta/store';
import { bridge, cloud, useAsHook } from '@youzan/ranta-helper';
import TeeEvent from '@youzan/tee-event';
import getUniquePageKey from '@youzan/decorate-tee/src/common/utils/unique';

import { getCurrentPageOption } from '@/base-api/shop/chain-store.js';

import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';
import { getGuideBizDataMap } from 'utils/guide';
import { vanxToRantaStore } from 'shared/common/base/ranta/store';

import { checkIsSupportGoodsRecommend } from '../../../level-common/utils/api';

import { store } from './store';

const app = getApp();
// const { data: brandPageData, ...brandPageMethods } = getBrandFeaturePage({
//   title: '会员中心',
//   special_nav_bg_color: '#393F5B',
//   pageBgColor:
//     'linear-gradient(175.13deg, #393F5B 10.41%, #191C30 30.15%, #2F354A 73.88%)',
// });

export default class {
  @bridge('beforeOutMemberLevel', 'asyncEvent')
  beforeOutMemberLevel = useAsHook<() => any[]>();

  @cloud('beforeQuitMemberLevel', 'hook')
  beforeQuitMemberLevel = useAsHook<() => Promise<void>>();

  ctx;

  store = vanxToRantaStore(store);

  constructor({ ctx }) {
    ctx.store = this.store;

    this.ctx = ctx;

    this.initDataAndActions();
    this.initCtx();
  }

  beforePageMount = ({ query, route }) => {
    const { fromScene, fromBiz, waFromBiz, activityInfo, crmFromScenePlanId } =
      query;
    let weShopBizDataMap = {};
    try {
      weShopBizDataMap = this.ctx.lambdas.getWeShopBizDataMap(query) || {};
    } catch (error) {
      console.error('getWeShopBizDataMap error', error);
      this.ctx.hummer && this.ctx.hummer.capture(error);
    }
    delete query.crmFromScenePlanId;

    this.query = query;
    const guideBizDataMap = getGuideBizDataMap(query);
    this.INIT_PAGE_DATA({
      guideScene: fromScene || '',
      pageQuery: query,
      crmFromScenePlanId: crmFromScenePlanId || '',
      isUnionCode:
        (fromBiz === 'union-code' && activityInfo) ||
        (fromBiz === 'wa' && waFromBiz === 'union-code' && activityInfo),
      bizDataMap: {
        ...guideBizDataMap,
        ...weShopBizDataMap,
      },
      globalCustomLoading: app.globalData.globalCustomLoading,
    });
    this.ctx.data.pageQuery = query;

    let branchStoreOpenOnline = false;
    app.getShopInfo().then((shopRes) => {
      const { chainStoreInfo = {}, shopMetaInfo = {} } = shopRes || {};
      const { isRootShop = false } = chainStoreInfo;

      branchStoreOpenOnline =
        !checkOfflineBranchStore(shopMetaInfo) ||
        checkOnlineBranchStore(shopMetaInfo);
      this.SET_BRANCH_STORE_OPEN_ONLINE({ branchStoreOpenOnline });
      const isPartnerStore = checkPartnerStore(shopMetaInfo);
      // 总店 || 合伙人店铺
      const shouldWaitForEnterShop = isRootShop || isPartnerStore;
      if (shouldWaitForEnterShop) {
        const { query } = getCurrentPageOption();

        const redirectUrl = Args.add('/' + (this.route || route), query);
        autoEnterShop({ ...query, redirectUrl }).then(() => {
          // autoEnterShop可能不返回kdtId, 进店后从app.getKdtId拿最新kdtId
          this.SET_KDT_ID({ kdtId: app.getKdtId() });
          this.fetchData({ branchStoreOpenOnline, ctx: this.ctx });
          this.checkBenefitMatch();
        });
      } else {
        this.fetchData({ branchStoreOpenOnline, ctx: this.ctx });
        this.checkBenefitMatch();
      }
    });

    // type === 1 表示免费等级
    checkIsSupportGoodsRecommend({ type: 1 }).then((res = {}) => {
      this.SET_IS_SUPPORT_GOODS_RECOMMEND({
        isSupportGoodsRecommend: res.value,
      });
    });

    // * 判断店铺的成长值计算是否在三方中心
    this.initIsThirdPartyCenter();

    // * 判断是否为场景营销订阅入口订阅状态
    this.checkIsFromSubscribeAndCanSubscribe(query);
  };

  onPageShow = () => {
    this.CHECK_TAB_BAR_STATUS();
    // onShow 的时候需要重新获取下任务的配置，将 fetchMissionFinished 设置为 false
    this.SET_FETCH_MISSION_FINISHED(false);
    this.fetchUserLevel({
      ctx: this.ctx,
      // 微信小店入会后返回免费会员页触发入会弹窗
      isRejoinMember: this.ctx.data.isWeShop,
    });
  };

  getShareInfo = () => {
    const userInfo = new Promise((resolve) => {
      app.getUserInfo(({ userInfo }) => {
        resolve(userInfo);
      });
    });
    return Promise.all([app.getShopInfo(), userInfo]);
  };

  async onShareAppMessage() {
    const [shopInfo, userInfo] = await this.getShareInfo();
    return {
      title: `${userInfo?.nickName || '好友'}邀请你加入${
        shopInfo?.shop_name || ''
      }会员，享会员权益`,
    };
  }

  // 装修导航头强依赖该事件
  // FIXME: 接入的导航组件当前存在bug，会挂载空函数导致eventBus存在调用异常的情况，找@戴挺远解决
  onPageScroll = (ev) => {
    TeeEvent.trigger('onPageScroll' + getUniquePageKey(), ev);
  };

  onPullDownRefresh = () => {
    wx.stopPullDownRefresh();
  };

  initDataAndActions = () => {
    const res = mapState(this, ['kdtId', 'userLevel'], { setSelf: true });

    Object.assign(this, res);

    mapActions(this, [
      'INIT_PAGE_DATA',
      'SET_KDT_ID',
      'SET_IS_SUPPORT_GOODS_RECOMMEND',
      'CHECK_TAB_BAR_STATUS',
      'SET_FETCH_MISSION_FINISHED',
      'SET_BRANCH_STORE_OPEN_ONLINE',
      'SET_HAS_LEVEL',
      'checkBenefitMatch',
      'initIsThirdPartyCenter',
      'checkIsFromSubscribeAndCanSubscribe',
      'fetchData',
      'fetchUserLevel',
      'reloadData',
    ]);

    this.store.watch('userLevel', (userLevel) => {
      // * 用户等级详情接口返回数据中含有 identityNo 则表示有等级
      this.SET_HAS_LEVEL({ hasLevel: !!(userLevel && userLevel.identityNo) });
    });
  };

  initCtx = () => {
    this.ctx.data.navigationBarConfigData = {
      origin_immersion: 1,
    };
    const memberName = this.ctx.data.memberConfig?.name || '会员';
    this.ctx.data.pageTitle = memberName + '中心';
    this.ctx.process.define('reloadData', (isRejoinMember) =>
      this.reloadData({ isRejoinMember, ctx: this.ctx })
    );
  };
}
