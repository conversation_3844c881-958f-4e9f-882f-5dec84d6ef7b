.coupon-box {
  display: flex;
  align-items: center;
  height: 68px;
  border-radius: 4px;
  background: #fcfaf7;
  border: 0.5px solid #ece3d0;
  flex-direction: row;
  position: relative;
  margin-bottom: 8px;

  .left-part {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: 4px;

    .img {
      width: 60px;
      height: 60px;
      border-radius: 4px;
    }
  }

  .right-part {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    font-size: 12px;
    margin-left: 8px;

    .coupon-content-wrap {
      display: flex;
      flex-direction: row;
      align-items: flex-end;
      line-height: 26px;

      .content-desc {
        margin-right: 8px;
        line-height: 23px;
        color: #323233;

        .pre-unit {
          font-size: 16px;
          font-weight: 500;
          margin-right: 2px;
        }

        .num {
          font-size: 24px;
          font-weight: 500;
        }

        .name {
          font-size: 18px;
          font-weight: 500;
        }

        .unit {
          margin: 12px 0 0 2px;
        }

        .decimal-num {
          text-align: center;
          font-weight: 500;
        }

        .integer {
          font-size: 24px;
        }

        .decimal {
          font-size: 16px;
        }
      }
    }

    .coupon-title {
      color: #969799;
      line-height: 16px;
      margin-top: 2px;
    }

    .desc {
      color: #323233;
      font-size: 14px;
      font-weight: 400;
      line-height: 26px;
    }

    .time-count {
      display: flex;
      flex-direction: row;
      align-items: center;
      color: #e73222;

      .time-item {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;
        height: 16px;
        border-radius: 1px;
        font-size: 12px;
        margin: 0 2px;
        padding-top: 2px;
      }

      .time-point {
        margin: 0 1px;
      }

      .last-desc {
        font-size: 12px;
      }
    }
  }

  .coupon-num {
    height: 16px;
    background: #40250b66;
    border-top-right-radius: 4px;
    border-bottom-left-radius: 4px;
    font-size: 12px;
    color: #fff;
    padding: 0 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 0;
    top: 0;
  }

  .endCount {
    font-size: 12px;
    color: #969799;
    position: absolute;
    right: 24px;
  }

  .coupon-status {
    color: #969799;
    position: absolute;
    right: 0;
    bottom: 0;
    background: url(//img01.yzcdn.cn/upload_files/2023/09/25/FjFO9e1HqecmjKUHriDn0MZgimID.png)
      no-repeat;
    background-size: 100%;
    height: 35px;
    width: 42px;

    .status {
      color: #fff;
      transform: rotate(-30deg);
      font-size: 11px;
      line-height: 46px;
      text-align: center;
      margin-left: 4px;
    }
  }

  .coupon-link {
    position: absolute;
    top: 12px;
    right: 8px;
    width: 60px;
    font-size: 12px;
    color: #463c32 !important;
  }
}

.disable-coupon {
  background: inherit;
  background-color: #f7f8fa;
  color: #969799 !important;

  .left-part {
    border-right: 1px dashed rgba(150, 151, 153, 0.2);
  }
}
