<view class="coupon-box {{endCount ? 'disable-coupon' : ''}}">
  <view class="left-part">
    <image class="img" src="//img01.yzcdn.cn/upload_files/2023/09/25/FovbJTSV7STAm-xqpPv80129T_FJ.png" />
  </view>
  <view class="right-part">
    <view class='coupon-content-wrap'>
      <view class='content-desc'>
        <text wx:if="{{couponData.couponName}}" class="name">{{ couponData.couponName }}</text>
        <text wx:else>
          <text class="pre-unit">{{ couponData.preUnit }}</text>
          <!-- 优惠券中满减券的金额，小数点和小数部分字号做特殊处理 -->
          <text wx:if="{{!couponData.hasDecimal}}" class="num" style="{{ valueStyle }}">
            {{ couponData.value }}
          </text>
          <text wx:else class="decimal-num">
            <text class="integer">{{ couponData.integerPart }}</text>
            <text class="decimal">.{{ couponData.decimalPart }}</text>
          </text>
          <text class="unit">{{ couponData.unit }}</text>
        </text>
      </view>
      <view wx:if="{{couponData.useCondition}}" class="desc">{{ couponData.useCondition }}</view>
    </view>
    <view>
      <van-count-down use-slot time="{{ time }}" millisecond wx:if="{{ shouldCount }}" bind:change="handleChange">
        <view class="time-count">
          <text class="time-item">{{ timeData.hours }}</text>
          <text class="time-point">:</text>
          <text class="time-item">{{ timeData.minutes }}</text>
          <text class="time-point">:</text>
          <text class="time-item">{{ timeData.seconds }}</text>
          <text class="time-point">.</text>
          <text class="time-item">{{ timeData.milliseconds }}</text>
          <text class="last-desc">后到期</text>
        </view>
      </van-count-down>
      <view wx:else class="coupon-title">{{ couponData.title }}</view>
    </view>
  </view>
  <view wx:if="{{coupon.number > 1}}" class="coupon-num">x{{ couponData.number }}</view>
  <view wx:if="{{endCount}}" class="endCount">已过期</view>
  <view wx:elif="{{couponStatus}}" class="coupon-status">
    <view class="status">{{couponStatus }}</view>
  </view>
  <van-button wx:if="{{originalStatus === 1}}" color="#E7C5A5" round block size="mini" bind:tap="linkToCoupon" class="coupon-link" custom-class="coupon-link">
    去使用
  </van-button>
</view>