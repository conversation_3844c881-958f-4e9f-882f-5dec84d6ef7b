import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();
WscComponent({
  properties: {
    point: {
      type: Object,
    },
  },
  data: {
    pointsName: '积分',
    smallSize: false,
    // 领取状态
    statusStr: '',
  },
  attached() {
    this.initPointsName();
  },
  methods: {
    initPointsName() {
      app.getPointsName().then(({ pointsName = '积分' }) => {
        this.setYZData({
          pointsName,
        });
      });
    },
    getStatusStr(status) {
      return status === 2 ? '已领' : status ? '未领' : '';
    },
  },
  observers: {
    point(val) {
      const { smallSize, status } = val;
      const statusStr = this.getStatusStr(status);
      this.setYZData({
        smallSize,
        statusStr,
      });
    },
  },
});
