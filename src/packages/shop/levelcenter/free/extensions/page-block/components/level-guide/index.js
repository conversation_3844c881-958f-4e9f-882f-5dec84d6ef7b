import WscComponent from 'pages/common/wsc-component/index';

import { properties } from './mixins';

const app = getApp();

WscComponent({
  properties: {
    ...properties,
    bizDataMap: Object,
    crmFromScenePlanId: String,
    hasMobile: Boolean,
    nowLevel: Object,
    nowBenefitList: {
      type: Array,
      value: [],
    },
    memberShopConfigs: Object,
    memberName: {
      type: String,
      value: '会员',
    },
    isWeShop: {
      type: Boolean,
      value: false,
    },
    showRegisterBtn: {
      type: Boolean,
      value: false,
    },
    beMemberTip: String,
  },

  data: {
    guideList: [], // 组件列表，带顺序
    curIdx: -1, // 当前展示 idx
  },

  attached() {
    this.initialize();

    app.on('level-center:rejoin', () => {
      this.initialize();
    });
  },
  detached() {
    app.off('level-center:rejoin');
  },

  methods: {
    initialize() {
      this.initGuideList().then(() => {
        this.start();
      });
    },
    initGuideList() {
      // 这里做成异步是为了未来好扩展，后续如果要添加或调整顺序，这里处理就好了
      const guideList = ['scene-market-subscribe', 'level-welcome'];
      if (this.properties.hasLevel) {
        guideList.unshift('protocol-guide');
      }
      this.setYZData({
        guideList,
      });
      return Promise.resolve(guideList);
    },

    // 如果要添加处理逻辑直接放开 shown 和 finish 保持一致
    next(/* shown */) {
      const newIdx = this.data.curIdx + 1;
      this.setYZData({
        curIdx: newIdx,
      });
    },

    finish(ev) {
      const shown = ev.detail;
      if (shown) {
        this.setYZData({
          curIdx: -1,
        });
      } else {
        this.setYZData({
          curIdx: this.data.curIdx + 1,
        });
      }
    },

    start() {
      this.setYZData({
        curIdx: 0,
      });
    },

    // * 更改 hasAuthorize
    triggerBindMobile() {
      this.triggerEvent('bindMobile');
    },

    // * 订阅回调
    handleSubscribeCallback(ev) {
      // * 回调参数，true 代表订阅成功；false 代表订阅失败；
      this.triggerEvent('subscribeCallback', ev.detail);
    },

    showLiveCodeAddFansPop(e) {
      this.triggerEvent('showLiveCodeAddFansPop', e.detail);
    },
  },
});
