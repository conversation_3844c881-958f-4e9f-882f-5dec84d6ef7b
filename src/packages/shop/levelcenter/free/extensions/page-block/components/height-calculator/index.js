import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  attached() {
    setTimeout(() => {
      wx.createSelectorQuery()
        .in(this)
        .select('#height-cal')
        .boundingClientRect()
        .exec((res) => {
          const { height } = (res && res[0]) || {};
          if (height > 0) {
            this.triggerEvent('heightCalculate');
          }
        });
    }, 500);
  },
});
