import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    benefits: Array,
    planBenefits: Array,
    limit: Number,
  },

  data: {
    renderBenefits: [],
  },

  attached() {
    this.initRenderBenefits();
  },

  methods: {
    initRenderBenefits() {
      const { benefits, planBenefits, limit } = this.properties;
      const renderBenefits = [].concat(benefits).concat(planBenefits);
      if (renderBenefits.length > limit) {
        const benefitLength = renderBenefits.length;
        renderBenefits.splice(limit - 1, 0, {
          appName: '全部权益',
          benefitCount: benefitLength,
        });
      }

      this.setYZData({
        renderBenefits: renderBenefits.slice(0, limit),
      });
    },
  },
});
