/* eslint-disable @youzan/dmc/wx-check */
import Args from '@youzan/weapp-utils/lib/args';

import { complete, fetchAfterBeMemberInfo } from './api';

const app = getApp();
const {
  safeArea: { top },
  brand,
} = wx.getSystemInfoSync();
const isIphoneX = top >= 44 && (brand === 'iPhone' || brand === 'devtools');

const qrcodeProcessLandingPage = (params) => {
  const { activityInfo, queryInfo = {} } = params;
  let weappUrl = '';
  return fetchAfterBeMemberInfo(activityInfo)
    .then((afterBeMemberInfo) => {
      if (afterBeMemberInfo.completePageType === 1) {
        // 等海哗更新小程序会员码页面后再上
        weappUrl = `/pages/common/webview-page/index?src=${encodeURIComponent(
          Args.add('/wscuser/scrm/member-code', queryInfo)
        )}`;
      }

      if (afterBeMemberInfo.completePageType === 2) {
        if (/(^pages)|(^packages)/.test(afterBeMemberInfo.completeJumpUrl)) {
          // 跳转小程序链接，最前面不加/跳不过去
          weappUrl = `/${afterBeMemberInfo.completeJumpUrl}`;
        } else {
          weappUrl = afterBeMemberInfo.completeJumpUrl || '';
        }
      }
      return weappUrl;
    })
    .catch((e) => {
      console.error('e', e);
    });
};

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    query: Object,
    kdtId: String,
  },

  /**
   * 组件的初始数据
   */
  data: {
    isIphoneX,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * @description 用户点击下一步，先完成多码合一API，后进行跳转
     */
    nextStep() {
      wx.showLoading();
      const { activityInfo } = this.data.query;
      const url = Args.add('/packages/weass/qrcode-process/index', {
        ...this.data.query,
        bridgeType: 'qrcodeProcess', // 企微结果页通用化业务识别参数 接入参考 https://shimo.im/docs/vVAXVb2BQYu8vnqm/
        fromMemberCenter: true, // 从会员中心页来的标识
      });

      Promise.all([
        complete(activityInfo, this.data.kdtId),
        qrcodeProcessLandingPage({
          activityInfo,
          queryInfo: { kdtId: this.data.kdtId },
        }),
      ])
        .then(([_, weappUrl]) => {
          app.isSwitchTab(weappUrl).then((isTab) => {
            (isTab ? wx.switchTab : wx.navigateTo)({
              url: weappUrl || url,
              success: () => {
                wx.hideLoading();
              },
              fail: () => {
                wx.reLaunch({
                  url: weappUrl || url,
                });
              },
            });
          });
        })
        .catch(() => {
          wx.navigateTo({
            url,
          }).finally(() => wx.hideLoading());
        });
    },
  },
});
