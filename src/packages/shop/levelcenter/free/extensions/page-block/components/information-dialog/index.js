import WscComponent from 'pages/common/wsc-component/index';
import openWebView from 'utils/open-web-view';

WscComponent({
  properties: {
    value: {
      type: Boolean,
      value: false,
      observer(nv, ov) {
        if (nv !== ov) {
          this.setYZData({
            visible: nv,
          });
        }
      },
    },
    alias: {
      // 等级或者卡别名
      type: String,
    },
    kdtId: {
      type: [String, Number],
    },
    redirectUrl: {
      type: String,
    },
  },
  data: {
    visible: false,
  },

  methods: {
    handleCloseDialog() {
      this.setYZData({
        visible: false,
      });
    },
    handleToCompletd() {
      const { kdtId, alias, redirectUrl } = this.properties;
      openWebView(redirectUrl, {
        query: { kdt_id: kdtId, alias, eT: Date.now() },
      });
    },
  },
  attached() {},
});
