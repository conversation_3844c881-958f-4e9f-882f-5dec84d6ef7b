// .bg-mask {
//   position: absolute;
//   top: 0;
//   right: 0;
//   bottom: 0;
//   left: 0;
//   border-radius: 12px;
//   overflow: hidden;
// }

.level-wrap {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 19px 16px 16px;
  color: #fff;
  box-sizing: border-box;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  background-size: cover;
  margin-right: 16px;

  .level-name-wrap {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .level-name {
      font-size: 20px;
      line-height: 28px;
    }

    .expiry {
      margin-top: 4px;
      font-size: 11px;
      font-weight: 400;
      line-height: 15px;
      color: rgba(255, 255, 255, 0.7);
    }
  }
}

.right-wrap {
  position: absolute;
  align-items: center;
  width: 56px;
  height: 16px;
  top: -19px;
  right: -16px;
  border-radius: 0px 12px 0px 12px;
  font-size: 11px;
  font-weight: 400;
  line-height: 14px;
  background: rgba(0, 0, 0, 0.3);
  text-align: center;
}
.bot-right-wrap {
  display: flex;
  flex-direction: row-reverse;
  position: relative;
  bottom: -25px;
  right: -8px;
}

.level-top {
  font-size: 18px;
  font-weight: bold;
  position: relative;
  flex: 1;
  height: 26px;
  justify-content: space-between;
  display: flex;

  .trial {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    font-size: 11px;
    color: #fff;
    text-align: center;
    line-height: 12px;
    padding: 3px 4px;
    margin-left: 4px;
    border-radius: 3px;
  }

  .level-btn {
    position: absolute;
    right: 0;
    top: 0;
    color: #252525;
    font-weight: bold;
    background-color: #fff;
    flex: 1;
    height: 30px;
    text-align: center;
    font-size: 12px;
    line-height: 30px;
    border-radius: 999px;

    &::after {
      border: none;
    }

    .van-button--defaul {
      border: 0;
    }

    .van-button__text {
      color: #323233;
      font-size: 14px;
      font-weight: 450;
    }
  }

  .level-btn-default {
    mix-blend-mode: screen;
  }
}

.level-bottom {
  font-size: 11px;
  .card-level-container {
    position: absolute;
    right: 16px;
    bottom: 12px;

    .card-level-container-logo {
      width: 80px;
      height: 80px;
    }
  }
  .points-info {
    text-align: start;
  }
}

.card-code {
  margin-top: 13.5px;

  .QR {
    background: url('//img.yzcdn.cn/public_files/2019/02/21/e0a192cee152c41f4a6efe05da1ebef7.png')
      center no-repeat;
    background-size: 100% 100%;
  }

  .icon {
    width: 24px;
    height: 24px;
    display: inline-block;
    vertical-align: top;
  }
}

.bigger {
  font-weight: 500;
  font-size: 22px;
  line-height: 22px;
  margin-right: 4px;
}

.need {
  font-size: 12px;
  color: #fff;
  line-height: 18px;
  text-align: left;

  .big {
    font-weight: bold;
    font-size: 14px;
    line-height: 20px;
  }

  .bigger {
    font-weight: bold;
    font-size: 26px;
    line-height: 32px;
  }
}

.stay-level-desc {
  display: block;
  font-size: 11px;
  font-weight: 500;
  margin-top: 4px;
  word-break: break-all;
  line-height: 15px;
}
