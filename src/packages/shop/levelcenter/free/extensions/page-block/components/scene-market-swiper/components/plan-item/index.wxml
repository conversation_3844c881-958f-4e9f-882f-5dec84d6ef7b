<view class="plan-detail" bindtap="onViewPlan">
  <view class="icon-text-group">
    <image src="{{ planIcon }}" class="plan-icon" />
    <view class="plan-info">
      <view class="birthday-text" wx:if="{{ planDetail.planType === PlanTypeEnum.Birthday }}">
        <view class="nick-name">{{ nickName || '' }}</view>
        生日快乐！请查看生日特权
      </view>
      <view wx:elif="{{ planDetail.planType === PlanTypeEnum.Festival }}">
        <text>店铺节日庆典，会员享专属权益</text>
      </view>
      <view wx:else>
        <text>会员日，会员享专属权益</text>
      </view>
    </view>
  </view>
  <view class="view-wrap">
    <text>查看</text>
    <van-icon
      name="arrow"
      color="#979797"
    />
  </view>
</view>