import get from '@youzan/weapp-utils/lib/get';
import { requestWithGuideParams } from 'utils/guide';

const app = getApp();

/**
 * 获取用户当前等级信息
 */
export const getUserLevelInfo = (data) => {
  return requestWithGuideParams({
    path: '/wscuser/levelcenter/api/userLevelDetail.json',
    data,
  });
};

/**
 * 获取等级列表信息
 * @param {number} levelType 等级组类型
 */
export const getLevelList = (levelType) => {
  return app
    .request({
      path: '/wscuser/levelcenter/api/getLevelList.json',
      data: { type: levelType },
    })
    .then((res) => {
      return {
        levelList: get(res, 'levelList.0.levelV2List', []),
        planBenefits: get(res, 'levelPlanBenefits', []),
      };
    })
    .catch(() => {
      return {
        levelList: [],
        planBenefits: [],
      };
    });
};

export const getCouponByIds = (couponIds) => {
  return app.request({
    path: '/wscuser/level/api/getCouponsByCouponIds.json',
    data: {
      query: JSON.stringify({ couponIds }),
    },
  });
};

/**
 * 根据赠品 id 获取赠品信息
 * @param {number} presentId 赠品id
 */
export const getPresentInfo = (presentId) => {
  return app.request({
    path: '/wscuser/level/api/getPresentInfoById.json',
    data: {
      id: presentId,
    },
  });
};

/**
 * 获取可用任务列表
 */
export const fetchMissionList = () => {
  return app
    .request({
      path: '/wscuser/scrm/api/ump/getMissionListV2.json',
    })
    .then((missions = []) => {
      return missions.reduce((res, mission) => {
        if (mission.index === 3) {
          res.push(
            ...get(mission, 'list', []).filter(
              (citem) => citem.name === 'DAILYSIGN'
            )
          );
        } else {
          res.push(...mission.list);
        }
        return res;
      }, []);
    });
};

// 会员中心是否展示弹窗引导
export const getLevelCenterShowGuide = () => {
  return app.request({
    path: '/wscuser/level/api/guide/levelCenterShowGuideV2.json',
  });
};

// 获取当前等级免费权益的赠品，优惠券，积分信息
export const getCurrentBenefitInfo = (data) => {
  return app.request({
    path: '/wscuser/level/api/getCurrentBenefitInfo.json',
    method: 'get',
    data,
  });
};

// 清除会员中心引导标
export const clearLevelCenterShowGuide = getLevelCenterShowGuide;

// 用户完成内容预览动作
// http://zanapi.qima-inc.com/site/service/view/1324644
export const readPopContent = (data) => {
  return app.request({
    path: '/wscuser/level/api/readPopContent',
    method: 'post',
    data,
  });
};

// 判断当前客户是否为新客
export const hasCustomerRel = () => {
  return app.request({
    path: '/wscuser/wa/wecom-customer/hasCustomerRel.json',
    method: 'get',
  });
};
