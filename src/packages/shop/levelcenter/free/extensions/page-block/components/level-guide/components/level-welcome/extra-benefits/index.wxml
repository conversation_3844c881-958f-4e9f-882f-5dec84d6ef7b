<view>
  <view wx:if="{{ renderBenefits.length > 0 }}" class="extra-benefits">
    <view
      wx:for="{{ renderBenefits }}"
      key="{{ item.key }}"
      class="benefit-item-wrap"
    >
      <view
        wx:if="{{ item.key === 'present' }}"
        class="benefit-item benefit-item__{{item.key}} van-hariline--surround"
      >
        <view class="benefit-name">
          <view class="benefit-desc">赠品</view>
        </view>
        <view class="benefit-tip">{{ presentInfo.title }}</view>
      </view>
      <view
        wx:if="{{ item.key === 'points' }}"
        class="benefit-item benefit-item__{{item.key}} van-hariline--surround"
      >
        <view class="benefit-name">
          <view class="benefit-desc">{{ item.points }}</view>
        </view>
        <view class="benefit-tip">赠送 {{ pointsName }}</view>
      </view>
    </view>
  </view>
</view>
