import WscComponent from 'pages/common/wsc-component/index';
import {
  BeneitNumTypeEnum,
  PreferentialModeEnum,
  GroupType,
} from 'constants/member-constants';
import accDiv from '@youzan/utils/number/accDiv';
import args from '@youzan/weapp-utils/lib/args';

WscComponent({
  properties: {
    coupon: {
      type: Object,
    },
  },
  data: {
    couponData: {},
    time: 0,
    timeData: {
      hours: '',
      minutes: '',
      seconds: '',
      milliseconds: '',
    },
    shouldCount: false,
    endCount: false,
    couponStatus: '',
    originalStatus: 0,
    valueStyle: '',
  },
  methods: {
    handleChange(e) {
      const { hours, minutes, seconds, milliseconds } = e.detail;
      if (hours > 0 || minutes > 0 || seconds > 0 || milliseconds > 0) {
        const timeData = {
          hours: this.formatNum(hours),
          minutes: this.formatNum(minutes),
          seconds: this.formatNum(seconds),
          milliseconds: Math.floor(milliseconds / 100),
        };
        this.setYZData({ timeData });
      } else {
        this.setYZData({
          shouldCount: false,
          endCount: true,
          couponStatus: '已过期',
        });
      }
    },
    countDowm(countTime) {
      const time = countTime - +new Date();

      // 先计算倒计时默认值 小程序可能会有显示问题
      const timeData = {
        hours: this.formatNum(Math.floor(time / 3600 / 1000)),
        minutes: this.formatNum(Math.floor((time % (3600 * 1000)) / 60000)),
        seconds: this.formatNum(Math.floor((time % (60 * 1000)) / 1000)),
      };

      this.setYZData({
        shouldCount: true,
        time,
        timeData,
      });
    },
    formatNum(num) {
      return num < 10 ? '0' + num : num;
    },
    linkToCoupon() {
      const { couponType, couponId } = this.properties.coupon;
      const groupType =
        couponType === 1
          ? GroupType.Code
          : couponType === 0
          ? GroupType.Card
          : GroupType.Thirdparty;

      // eslint-disable-next-line @youzan/dmc/wx-check
      return wx.navigateTo({
        url: args.add('/packages/user/coupon/detail/index', {
          id: couponId,
          from: 'levelcenter',
          type: groupType,
          couponType,
        }),
      });
    },
  },
  observers: {
    coupon(val) {
      const {
        // 标题
        title,
        value,
        // 使用限制
        thresholdCopywriting,
        number,
        // 优惠券类型
        preferentialMode,
        // 优惠券值类型
        voucherValueGenerateType,

        // 过期时间
        validEndTime,
        status,
      } = val;
      // 有效期小于一天 则显示倒计时
      // validEndTime = +new Date() + 3600 * 1000 + 120 * 1000;
      const shouldCount =
        validEndTime > +new Date() &&
        validEndTime < +new Date() + 3600 * 24 * 1000;

      let tempValue;
      let tempUnit;
      let couponName;
      let tempPreUnit;
      let hasDecimal = false;
      let decimalPart;
      let integerPart;

      switch (Number(preferentialMode)) {
        case PreferentialModeEnum.NUM:
          if (voucherValueGenerateType === BeneitNumTypeEnum.RANDOM) {
            couponName = '随机金额';
          } else {
            tempValue = value ? accDiv(value, 100) : '';
            hasDecimal = tempValue % 1 !== 0;

            const numStr = tempValue.toString().split('.');
            integerPart = numStr[0];
            decimalPart = numStr[1];
          }
          tempPreUnit = '¥';
          break;
        case PreferentialModeEnum.DISCOUNT:
          tempValue = value ? accDiv(value, 10) : '';
          tempUnit = '折';
          break;
        case PreferentialModeEnum.EXCHANGE:
          couponName = '0元兑换';
          break;
        case PreferentialModeEnum.BUY_PRESENT:
          couponName = '买赠券';
          break;
        case PreferentialModeEnum.EXPRESS:
          couponName = '运费券';
          break;
      }
      shouldCount && this.countDowm(validEndTime);

      const couponData = {
        hasDecimal,
        integerPart,
        decimalPart,
        value: tempValue,
        unit: tempUnit,
        preUnit: tempPreUnit,
        title,
        couponName,
        useCondition: thresholdCopywriting,
        number,
      };
      // 0 已抢完
      const statusStr = status === 0 ? '已抢完' : status ? '已发放' : '';
      this.setYZData({
        couponData,
        originalStatus: status,
        couponStatus: statusStr,
      });
    },
  },
});
