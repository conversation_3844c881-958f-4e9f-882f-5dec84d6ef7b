import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';

WscComponent({
  properties: {
    benefit: Object,
    limit: Number,
    presentInfo: Object,
    pointsName: String,
  },

  attached() {
    this.initRenderBenefits();
  },

  methods: {
    initRenderBenefits() {
      // 没有权益 返回 null
      const { benefit, limit } = this.properties;
      if (!benefit) return [];

      // 优惠券数量
      const coupons = get(benefit, 'coupon.couponList', []);
      const remind = limit - coupons.length;
      if (remind <= 0) return [];

      const renderBenefits = ['present', 'points']
        .reduce((res, key) => {
          if (benefit[key]) {
            res.push({
              ...benefit[key],
              key,
            });
          }
          return res;
        }, [])
        .slice(0, remind);

      this.setYZData({
        renderBenefits,
      });
    },
  },
});
