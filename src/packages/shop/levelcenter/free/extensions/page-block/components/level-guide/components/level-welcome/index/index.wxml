<view>
  <van-popup show="{{ visible }}" custom-class="benefit-popup" custom-style="background-color: rgba(255, 255, 255, 0);">
    <view wx:if="{{ visible }}" class="level-welcome-popup">
      <view class="pop-content">
        <pop-header be-member-tip="{{beMemberTip}}" is-we-shop="{{ isWeShop }}" has-level="{{ hasLevel }}" is-consume="{{isConsume}}" coupon-count="{{ couponCount }}" benefit-count="{{ benefitCount }}" growth-left="{{ growthLeft }}" join-gap-info="{{ joinGapInfo }}" show-mission-list="{{showMissionList}}" is-level-change="{{isLevelChange}}" is-be-member="{{isBeMember}}" current-level-name="{{currentLevelName}}" mode="{{mode}}" member-name="{{ memberName }}"/>
        <benefits class="benefits" benefits="{{ displayBenefits }}" plan-benefits="{{ displayPlanBenefits }}" limit="{{ 4 }}" />
        <view wx:if="{{presentList.length>0 || coupons.length >0 || showPoint}}" class="benefit-list-box">
          <!-- 赠品 -->
          <present wx:for="{{ presentList }}" wx:key="presentName" wx:for-item="present" present="{{ present }}" />
          <!-- 优惠券 + 积分 -->
          <coupon wx:for="{{ coupons }}" wx:key="couponId" wx:for-item="coupon" coupon="{{ coupon }}" />
          <point wx:if="{{ showPoint }}" point="{{ pointsData }}" />
        </view>
      </view>
      <view class="pop-actions">
        <block wx:if="{{ hasGift }}">
          <view class="gift-tip">{{giftTip}}</view>
        </block>
        <view class="action-btn-wrap">
          <van-button class="action-btn {{isWeShop && !hasLevel ? 'more-benefit' : ''}}" custom-class="action-btn {{isWeShop && !hasLevel ? 'more-benefit' : ''}}" block plain bindtap="beforeHandleAction">
            {{ actionText }}
          </van-button>
          <view wx:if="{{isWeShop && !hasLevel }}" class="flex-placehodler"></view>
          <van-button wx:if="{{ isWeShop && !hasLevel }}" class="action-btn" custom-class="action-btn" block plain bindtap="handleBackToStore">
            返回小店
          </van-button>
        </view>
    </view>
    <!-- 关闭弹窗 -->
    <van-icon class="close-btn" name="cross" size="20" bindtap="handleClose" />
  </van-popup>
</view>