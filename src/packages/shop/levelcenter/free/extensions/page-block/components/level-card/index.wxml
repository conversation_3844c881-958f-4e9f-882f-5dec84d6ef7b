<!-- 免费等级卡面 -->
<view style="{{ bgStyle }}" class="level-wrap">
  <view class="reflective" style="{{ reflectiveStyle }}" />
  <view class="level-top">
    <view class="card-level">
      <view class="level-name-wrap">
        <text class="level-name">{{ card.name }}</text>
        <text wx:if="{{expiryDesc}}" class="expiry">{{expiryDesc}}</text>
      </view>
    </view>
    <view wx:if="{{ hasCurrentLevel }}" class="right-wrap">
      当前等级
      <!-- 所在为当前等级并且不被禁用，则显示二维码 -->
      <view wx:if="{{!disabled}}" class="card-code" bindtap="handleQRClick">
        <view class="QR icon" />
      </view>
    </view>
  </view>
  <!-- 底部提示信息开始 -->
  <view class="level-bottom">
    <!-- 卡面右下角 -->
    <view class="bot-right-wrap">
      <van-button wx:if="{{ showGoPrepaidBtn }}" type="default" plain round block size="small" bindtap="goToPrepaid">
        去充值
      </van-button>
    </view>
    <view wx:if="{{ memberCenterLogoUrl && !card.image && !showGoPrepaidBtn}}" class="card-level-container">
      <image class="card-level-container-logo" src="{{ memberCenterLogoUrl}}" />
    </view>
    <!-- 卡面左下角 -->
    <!-- 用户已是当前等级 -->
    <view wx:if="{{ hasCurrentLevel }}">
      <view wx:if="{{curLevelBottomLeft}}" class="points-info" bindtap="goToPointsStore">
        <text class="bigger">{{curLevelBottomLeft.val}}</text>
        <text>{{curLevelBottomLeft.suffix}}</text>
      </view>
      <text wx:if="{{keepLevelProgress}}" class="stay-level-desc">
        {{keepLevelProgress}}
      </text>
    </view>
    <!-- 用户未达到当前等级 -->
    <view wx:else>
      <view wx:if="{{bottomText.showHtmlSnip}}">
        <rich-text class="need" nodes="{{ bottomText.htmlSnip }}" />
      </view>
      <view wx:else>
        <text>{{bottomText.desc}}</text>
      </view>
    </view>
    <!-- 卡面左下角结束 -->
  </view>
  <!-- 底部提示信息结束 -->
</view>