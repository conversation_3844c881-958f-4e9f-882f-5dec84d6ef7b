import WscComponent from 'pages/common/wsc-component/index';
import { hasCustomerRel } from '../level-guide/components/level-welcome/index/api';
import { SCENE as LIVE_CODE_SCENE } from '@youzan/tee-user-components/src/live-qrcode-add-fans/constants';

const app = getApp();

WscComponent({
  properties: {
    memberShopConfigs: {
      type: Object,
      observer(newValue, oldValue) {
        newValue = newValue || {};
        oldValue = oldValue || {};
        if (newValue && JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
          this.init(newValue);
        }
      },
    },
  },
  data: {
    liveCodeConfigs: {},
    isOpen: false,
    sedimentationChannel: 0,
    personalWechat:
      'https://img01.yzcdn.cn/upload_files/2021/11/25/FhFEDAOAfLdcGp48ZEuHUI-r9l-1.png',
    groupWechat:
      'https://img01.yzcdn.cn/upload_files/2021/11/25/Fh9wCQjlWDerFbnXykFdEPCaLtbC.png',
  },
  methods: {
    async init(memberShopConfigs) {
      const isOpen = await this.updateIsOpen(memberShopConfigs);
      this.setYZData({
        liveCodeConfigs: { ...memberShopConfigs },
        isOpen,
        sedimentationChannel: Number(memberShopConfigs?.sedimentation_channel),
      });

      if (isOpen) {
        app.logger &&
          app.logger.log({
            et: 'view', // 事件类型
            ei: 'join_group_view', // 事件标识
            en: '会员加群组件曝光', // 事件名称
          });
      }
    },
    handleButtonClick() {
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'join_group_click', // 事件标识
          en: '会员加群组件点击', // 事件名称
        });

      this.triggerEvent('showLiveCodeAddFansPop');
    },
    async updateIsOpen(memberConfigs) {
      const {
        liveCode,
        show_rule: showRule,
        open_membership: openMembership,
      } = memberConfigs;
      // 引导办会员是否开启，0-关闭，1-开启
      if (+openMembership === 0) {
        return false;
      }
      // 活码类型为个人活码与去重活码，且展示规则为已是好友不展示，showRule为 0
      if (
        [LIVE_CODE_SCENE.WeassPerson, LIVE_CODE_SCENE.WeassDistinct].includes(
          liveCode?.scene
        ) &&
        +showRule === 0
      ) {
        const isCustomer = await hasCustomerRel();
        return !isCustomer?.value;
      }
      return true;
    },
  },
});
