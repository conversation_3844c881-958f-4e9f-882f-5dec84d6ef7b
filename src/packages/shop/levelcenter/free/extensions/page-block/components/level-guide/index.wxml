<view>
	<scene-market-subscribe
	 wx:if="{{ guideList[curIdx] === 'scene-market-subscribe' }}"
	 bindnext="finish"
	 bind:bindMobile="triggerBindMobile"
	 biz-data-map="{{ bizDataMap }}"
	 planId="{{ crmFromScenePlanId }}"
	/>
	<level-welcome
	 wx:if="{{ guideList[curIdx] === 'level-welcome' }}"
	 group-alias="{{ groupAlias }}"
	 kdt-id="{{ kdtId }}"
	 has-authorized="{{ hasAuthorized }}"
	 page-query="{{ pageQuery }}"
	 scene="{{ scene }}"
	 bindnext="finish"
	 bind:subscribeCallback="handleSubscribeCallback"
	 join-gap-info="{{ joinGapInfo }}"
	 biz-data-map="{{ bizDataMap }}"
	 hasMobile="{{ hasMobile }}"
	 removed="{{ removed }}"
	 now-level="{{ nowLevel }}"
	 now-benefit-list="{{ nowBenefitList }}"
	 memberShopConfigs="{{memberShopConfigs}}"
	 bind:showLiveCodeAddFansPop="showLiveCodeAddFansPop"
	 member-name="{{ memberName }}"
	 is-we-shop="{{ isWeShop }}"
	 show-register-btn="{{ showRegisterBtn }}"
	 be-member-tip="{{ beMemberTip }}"
	/>
	<protocol-guide wx:if="{{ guideList[curIdx] === 'protocol-guide' }}" kdt-id="{{ kdtId }}" bindnext="finish" />
</view>

