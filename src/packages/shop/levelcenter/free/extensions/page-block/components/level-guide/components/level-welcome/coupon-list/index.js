import WscComponent from 'pages/common/wsc-component/index';

const accDiv = (num, system) => {
  return Number((num / system).toFixed(2));
};

WscComponent({
  properties: {
    coupons: Array,
    limit: Number,
  },

  data: {
    renderCoupons: [],
    couponCount: 0,
  },

  attached() {
    this.initRenderCoupons();
  },

  methods: {
    initRenderCoupons() {
      const { coupons, limit } = this.properties;
      const renderCoupons = (coupons || []).slice(0, limit).map((coupon) => {
        return {
          ...coupon,
          value:
            coupon.preferentialType === 1
              ? accDiv(coupon.denominations || 0, 100)
              : accDiv(coupon.discount || 0, 10),
          unit: coupon.preferentialType === 1 ? '元' : '折',
          threshold: coupon.usingThresholdCopywriting || '',
          name: coupon.title,
          // 不显示倒计时
          validEndTime: undefined,
          validStartTime: undefined,
        };
      });

      this.setYZData({
        renderCoupons,
      });
    },
  },
});
