.box {
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  padding: 0 16px 7px 16px;
  box-shadow: 0 -2px 10px 0 rgba(150, 151, 153, 0.12);
  position: fixed;
  font-weight: bold;
  background-color: #fff;
}

.button-wrapper {
  display: flex;
  flex-direction: column;

  .tip {
    font-size: 14px;
    padding: 16px 0;
    color: #333333;
    text-align: center;
  }

  .button {
    width: 100%;
    font-size: 14px;
    line-height: 36px;
    border-radius: 20px;
    color: #724804;
    text-align: center;
    background: linear-gradient(90deg, #e2bb7c, #e8c388);
  }
}

.is-new-phone {
  padding-bottom: 7px;
  padding-bottom: calc(7px + constant(safe-area-inset-bottom));
  padding-bottom: calc(7px + env(safe-area-inset-bottom));
}
