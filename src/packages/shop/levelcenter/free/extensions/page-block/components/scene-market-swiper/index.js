/* eslint-disable @youzan-open/tee/valid-same-extension-import */
import { PlanTypeEnum } from 'constants/member-constants';
import WscComponent from 'pages/common/wsc-component/index';

import { checkBirthdayStatus } from '../../../../../level-common/utils/api';
import { LevelType } from '../../../../../level-common/utils/constant';
import * as API from '../../api';

WscComponent({
  properties: {
    pageQuery: Object,
    groupAlias: String,
  },
  data: {
    list: [],
    isShowPlanDialog: false, // * 是否展示计划详情弹框
    specificPlanId: 0, // * 弹窗的计划 Id
  },
  attached() {
    this.getPlanList();
  },

  methods: {
    getPlanList() {
      return API.getPlanList()
        .then((originData) => {
          let data = [...originData];
          if (data && data.length) {
            if (data.some((item) => item.planType === PlanTypeEnum.Birthday)) {
              checkBirthdayStatus({ levelType: LevelType.FREE })
                .then((res) => {
                  data = data.filter((item) => {
                    return (
                      item.planType !== PlanTypeEnum.Birthday || res?.inRange
                    );
                  });
                  this.setYZData({
                    list: data || [],
                  });
                })
                .catch(() => {
                  wx.showToast({
                    title: '获取生日区间失败',
                    icon: 'error',
                    duration: 500,
                  });
                });
            } else {
              this.setYZData({
                list: data,
              });
            }
          }
        })
        .catch(() => {
          wx.showToast({
            title: '计划列表获取失败',
            icon: 'error',
            duration: 1000,
          });
        });
    },
    openPlanDialog(ev) {
      const specificPlanId = ev.detail;
      this.setYZData({
        isShowPlanDialog: true,
        specificPlanId,
      });
    },
    closePlanDialog() {
      this.setYZData({
        isShowPlanDialog: false,
      });
    },
    onBindMobile() {
      this.triggerEvent('bindMobile');
    },

    stopTouchMove() {
      return false;
    },
  },
});
