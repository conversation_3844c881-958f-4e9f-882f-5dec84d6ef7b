import WscComponent from 'pages/common/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';
import openWebView from 'utils/open-web-view';
import { properties } from '../../mixins';
import { getGuideBizDataMap, requestWithGuideParams } from 'utils/guide';

const app = getApp();

WscComponent({
  properties: {
    ...properties,
  },

  data: {
    visible: false,
  },

  attached() {
    this.checkShow();
  },

  methods: {
    checkShow() {
      // 未授权时不展示完善信息弹窗，此时由卡面引导
      if (!this.properties.hasAuthorized) {
        this.next(0, false);
        return;
      }

      Promise.all([this.checkUserRegisterComplete(), this.getUserLevel()])
        .then(([hasCompleteRes, userLevelDetail]) => {
          const levelAlias = get(userLevelDetail, 'level.levelAlias', null);
          // 简单值会被修改为 { value: res } 的形式，历史原因是要缓存
          // https://gitlab.qima-inc.com/weapp/wsc/blob/master/shared/utils/request/index.js#L366
          if (!hasCompleteRes.value && levelAlias) {
            this.setYZData({
              visible: true,
            });
          } else {
            this.next(0, false);
          }
        })
        .catch(() => {
          this.next(0, false);
        });
    },

    checkUserRegisterComplete() {
      return app.request({
        path: '/wscuser/level/api/checkUserLevelRegisterInfoComplete.json',
        data: {
          levelGroupAlias: this.properties.groupAlias,
        },
      });
    },

    getUserLevel() {
      // TODO: 小程序上的 recruitMemberParams
      return requestWithGuideParams({
        path: '/wscuser/levelcenter/api/userLevelDetail.json',
        data: {
          // TODO: MAGIC_NUMBER
          type: 1, // 免费等级
          withSyncInfo: 1,
          withConsumerData: '1', // * 获取用户近x个月的消费金额数据
        },
      });
    },

    handleClose() {
      this.setYZData({
        visible: false,
      });
      this.next(1000, true);
    },

    handleComplete() {
      const { kdtId, groupAlias, pageQuery } = this.properties;
      this.setYZData({
        visible: false,
      });
      this.next(1000, true);
      openWebView('/wscuser/levelcenter/fill', {
        query: {
          ...getGuideBizDataMap(pageQuery),
          kdt_id: kdtId,
          alias: groupAlias,
          eT: Date.now(),
        },
      });
    },

    next(timeout = 1000, shown) {
      if (timeout) {
        setTimeout(() => {
          this.triggerEvent('next', shown);
        });
      } else {
        this.triggerEvent('next', shown);
      }
    },
  },
});
