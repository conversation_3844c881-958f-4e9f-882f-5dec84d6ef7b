<view>
  <view>
    <view wx:if="{{isLevelChange && hasLevel}}">你已成为{{currentLevelName}}</view>
    <view wx:else>
      <view wx:if="{{ !hasLevel }}" class="title">还差一步，成为{{ memberName }}</view>
      <view wx:else class="title">
        <text wx:if="{{ !isWeShop }}" class="name">{{ nickName }}</text>
        <text>欢迎入会</text>
      </view>
    </view>
  </view>
  <view wx:if="{{ !hasLevel || isLevelChange }}">
    <view class="sub-title">
      <rich-text nodes="{{ subTitle }}" />
    </view>
  </view>
  <view class="sub-title" wx:else>
    <view wx:if="{{ couponCount > 0 }}">
      获得
      <text class="highlight">{{ couponCount }}张</text>
      {{ couponName }}，享
      <text class="highlight">{{ benefitCount }}大</text>
      {{ memberName }}权益
    </view>
    <view wx:else>
      <view wx:if="{{ benefitCount > 0 }}">
        享
        <text class="highlight">{{ benefitCount }}</text>
        大{{ memberName }}权益
      </view>
      <view wx:else>享受{{ memberName }}福利</view>
    </view>
  </view>
  <view wx:if="{{ showMissionList }}" class="mission-list">
    <mission-list hasMobile="{{ hasMobile }}" bizDataMap="{{ bizDataMap }}" />
  </view>
</view>