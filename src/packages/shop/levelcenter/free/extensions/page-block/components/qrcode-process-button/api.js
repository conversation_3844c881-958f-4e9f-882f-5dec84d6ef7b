const app = getApp();

const complete = (activityInfo, kdtId) =>
  app.request({
    path: '/wa/api/operation/qrcode-process/member-node/complete',
    method: 'post',
    data: {
      activityInfo,
      targetKdtId: kdtId,
    },
  });

const fetchAfterBeMemberInfo = (activityInfo) =>
  app.request({
    path: `/wa/api/operation/qrcode-process/member-node/info`,
    method: 'GET',
    data: {
      activityInfo,
    },
  });

export { complete, fetchAfterBeMemberInfo };
