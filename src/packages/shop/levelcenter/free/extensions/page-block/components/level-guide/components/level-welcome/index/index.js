import navigate from '@/helpers/navigate';
import { getSubscribePreInfo } from '@/helpers/subscribe';

import Toast from '@youzan-open/vant-tee/dist/toast/toast';

import {
  DISPLAY_TYPE,
  SCENE as LIVE_CODE_SCENE,
} from '@youzan/tee-user-components/src/live-qrcode-add-fans/constants';
import get from '@youzan/weapp-utils/lib/get';

import { SCENE_MARKET_SCENE } from 'constants/member-constants';

import WscComponent from 'pages/common/wsc-component/index';

import {
  getUrlParams,
  GUIDE_DIALOG_TYPE,
  jumpGuidePage,
  logGuideDialogView,
} from 'utils/guide-jump';
import {
  getIsNeedSubscribeInSceneMarketActivity,
  subscribeInSceneMarketActivity,
} from 'utils/scene-market-subscribe-message';

import { properties } from '../../../mixins';
import {
  formatBenefits,
  planBenefitAdapter,
} from '../../../../../../../../level-common/utils/adapter';
import {
  GUIDE_SCENE_ENUM,
  LevelMode,
} from '../../../../../../../../level-common/utils/constant';

import Tee from '@youzan/tee';

import * as API from './api';

let isAllDisabled = false;
const app = getApp();

WscComponent({
  properties: {
    ...properties,
    nowLevel: Object,
    nowBenefitList: {
      type: Array,
      value: [],
    },
    memberShopConfigs: {
      type: Object,
      value: {},
    },
    memberName: {
      type: String,
      value: '会员',
    },
    isWeShop: {
      type: Boolean,
      value: false,
    },
    showRegisterBtn: {
      type: Boolean,
      value: false,
    },
    beMemberTip: String,
  },

  data: {
    userLevel: null, // 用户当前等级信息
    levelInfo: null, // 用户当前等级详情
    levelPlanBenefit: null,
    levelList: [], // 等级组等级列表
    coupons: [], // 优惠券信息
    presentList: null, // 赠品信息
    allMissionCompleted: true, // 是否已完成所有任务

    visible: false,

    actionType: '',
    actionText: '',

    // computed 属性
    // 未来解决方案 https://developers.weixin.qq.com/miniprogram/dev/extended/utils/computed.html
    hasLevel: false,
    isConsume: false,
    couponCount: 0,
    benefitCount: 0,
    levelBenefit: null,
    needGrowth: false,
    displayBenefits: [],
    displayPlanBenefits: [],
    hasGift: false,
    showPoint: false,
    pointsData: {},
    growthLeft: 0,
    needSubscribe: false, // * 是否需要执行订阅
    showCloseBtn: false, // * 是否展示关闭弹窗按钮
    showActionBtn: true,
    showMissionList: false,
    isLevelChange: false,
    isBeMember: false,
    giftTip: '',
    currentLevelName: '',
    scene: '',
    mode: LevelMode.GROWTH,
    templateIdList: [],
  },

  attached() {
    // handleAction会存在重复调用的情况，新增一把锁来防止重复重定向
    this.actionProcessTimes = 0;
    this.preCheckShow().then((res) => {
      const { scene } = res;
      let { isPop: shouldShowGuide } = res;
      const isBeMember = scene === GUIDE_SCENE_ENUM.BE_MEMBER;
      const isLevelChange = scene === GUIDE_SCENE_ENUM.LEVEL_CHANGE;
      this.setYZData({
        scene,
        isLevelChange,
        isBeMember,
        showCloseBtn: true,
        showActionBtn: !isBeMember,
        giftTip: isLevelChange
          ? '等级礼包已发放至你的账户'
          : '礼包仅在首次入会时发放到账户',
      });

      // 判断是否需要展示弹窗
      // 1. 如果是小店进入(isWeShop=true)且不展示注册按钮，需要展示弹窗
      // 2. 如果是小店进入且展示注册按钮但需要更多储值，也需要展示弹窗
      // 3. 其他情况根据shouldShowGuide判断是否展示弹窗
      if (
        this.properties.isWeShop &&
        (!this.properties.showRegisterBtn ||
          this.properties.neededMoreStoreBalance)
      ) {
        // 小店场景下特殊处理：不展示注册按钮或需要更多储值时，强制展示弹窗
        shouldShowGuide = true;
      }

      // 非小店场景或不满足上述条件，根据接口返回结果判断
      if (!shouldShowGuide) {
        // 接口返回不需要展示弹窗
        return this.next(0, false);
      }

      // 1. 获取用户等级
      this.getUserLevel()
        .then((userLevel) => {
          this.setYZData({
            showMissionList:
              !this.data.hasLevel && userLevel.mode === LevelMode.GROWTH,
          });

          // 1.1 无等级， 判断是否完成任务，流程终止
          if (!get(userLevel, 'level.levelAlias')) {
            return Promise.all([
              this.getLevelInfo(),
              this._fetchMissionList(),
            ]).then(() => {
              if (isAllDisabled) {
                this.next(0, false);
                return;
              }
              this.showPopup();
              // Terminal the promise
              return new Promise(() => {});
            });
          }

          // 1.2 有等级继续加载等级数据
          return userLevel;
        })
        // 2. 获取用户等级信息，以及等级组列表
        .then((userLevel) => this.getLevelInfo(userLevel))
        // 3. 获取优惠券信息 和 赠品信息
        .then(() => {
          return this.getBenefitGift();
        })
        .then(() => {
          this.showPopup();
        })
        .catch((err) => {
          console.error(err);
          this.next(0, false);
        });
    });
  },

  methods: {
    next(timeout, shown) {
      if (timeout) {
        setTimeout(() => {
          this.triggerEvent('next', shown);
        }, timeout);
      } else {
        this.triggerEvent('next', shown);
      }
    },

    beforeHandleAction() {
      if (!this.data.needSubscribe) {
        this.handleAction();
        return;
      }
      const self = this;
      subscribeInSceneMarketActivity({
        logParam: {
          subscribe_page: '会员中心',
          subscribe_type: '加入会员',
        },
        templateIdList: self.data.templateIdList,
        onSuccess: () => {
          this.triggerEvent('subscribeCallback', true);
          self.handleAction();
        },
        onReject: () => {
          self.handleAction();
        },
        onFail: () => {
          // * 采集失败也不弹出弹框，直接跳过不阻塞流程
          self.handleAction();
        },
        onClose: () => {
          self.handleAction();
        },
      });
    },

    handleBackToStore() {
      Tee.$native.exitMiniProgram();
    },

    openAddFansLiveCode() {
      const {
        liveCode: { scene: liveCodeScene },
        show_rule: showRule,
        open_membership: openMembership,
      } = this.properties.memberShopConfigs;
      // 引导办会员是否开启，0-关闭，1-开启
      if (+openMembership === 0) {
        return;
      }

      // 活码类型为个人活码与去重活码，且展示规则为已是好友不展示，showRule为 0
      if (
        [LIVE_CODE_SCENE.WeassPerson, LIVE_CODE_SCENE.WeassDistinct].includes(
          liveCodeScene
        ) &&
        +showRule === 0
      ) {
        API.hasCustomerRel().then((res) => {
          !res?.value &&
            this.triggerEvent('showLiveCodeAddFansPop', DISPLAY_TYPE.POP);
        });
      } else {
        this.triggerEvent('showLiveCodeAddFansPop', DISPLAY_TYPE.POP);
      }
    },

    handleAction() {
      const { actionType, scene, isLevelChange, isBeMember } = this.data;
      // handleAction会存在重复调用的情况，新增一把锁来防止重复重定向
      if (this.actionProcessTimes > 0) {
        return;
      }

      this.actionProcessTimes += 1;

      this.next(0, true);

      this.addLog({
        et: 'click', // 事件类型
        ei: 'memb_success_click', // 事件标识
        en: '会员中心入会成功弹窗按钮点击', // 事件名称
      });

      if (actionType === 'complete') {
        navigate.navigate({
          url: '/packages/user/task-center/index',
        });
      } else if (actionType === 'guide') {
        jumpGuidePage(
          GUIDE_DIALOG_TYPE.freeLevelcenter,
          this.properties.pageQuery
        );
      } else {
        this.openAddFansLiveCode();
      }
      if (
        scene &&
        (isBeMember || isLevelChange || scene === GUIDE_SCENE_ENUM.COMPLETE)
      ) {
        this.setYZData({
          visible: false,
        });
      }
    },

    handleClose() {
      this.next(0, true);
      this.setYZData({
        visible: false,
      });
    },

    // 前置校验是否展示，减少一些不必要的请求
    preCheckShow() {
      return new Promise((resolve) => {
        if (this.properties.scene === 'complete') {
          resolve({ isPop: true, scene: 'complete' });
          return;
        }

        API.getLevelCenterShowGuide()
          .then((res) => {
            resolve(res);
          })
          .catch(() => {
            resolve(false);
          });
      });
    },

    showPopup() {
      const self = this;
      if (this.data.hasLevel) {
        // * 有等级 - 获取订阅能力 & 是否已经订阅过
        getIsNeedSubscribeInSceneMarketActivity()
          .then((needSubscribe) => {
            return getSubscribePreInfo({
              scene: SCENE_MARKET_SCENE,
            }).then((templateIdList) => {
              self.setYZData({
                needSubscribe: needSubscribe && templateIdList.length > 0,
                templateIdList,
              });
            });
          })
          .catch((err) => {
            Toast(err);
          });
      } else {
        self.setYZData({
          showActionBtn: false,
        });
      }

      this.setActionTypeAndText();

      // 完善信息，有等级的时候要清除 showGuide
      if (this.properties.scene === 'complete' && this.data.hasLevel) {
        API.clearLevelCenterShowGuide();
      }

      this.addLog({
        et: 'view', // 事件类型
        ei: 'memb_success', // 事件标识
        en: '会员中心入会成功弹窗', // 事件名称
      });
      this.setYZData({
        visible: true,
      });
    },

    setActionTypeAndText() {
      const { hasLevel, allMissionCompleted, needGrowth } = this.data;
      const { isGuideType, config } = getUrlParams(this.properties.pageQuery);
      let actionType = 'default';
      let configActionText = '';

      // 是否是引导类型
      if (isGuideType && hasLevel) {
        actionType = 'guide';
        configActionText = config.confirmButtonText;
        // 埋点-弹框曝光
        logGuideDialogView(GUIDE_DIALOG_TYPE.freeLevelcenter, config);
      } else if (!hasLevel && !allMissionCompleted && needGrowth) {
        actionType = 'complete';
      }
      const actionText = this._getActionText(actionType, configActionText);
      this.setYZData({
        actionText,
        actionType,
      });
    },

    getUserLevel() {
      return API.getUserLevelInfo({
        withConsumerData: '1', // * 获取用户近月的消费金额数据
      }).then((data) => {
        this.setYZData({
          userLevel: data,
          hasLevel: this._computeHasLevel({ userLevel: data }),
          isConsume: data.mode === LevelMode.CONSUME,
          mode: data.mode,
          currentLevelName: this._computeCurrentLevelName({
            userLevel: data,
          }),
        });
        return data;
      });
    },

    getLevelInfo(userLevel) {
      return new Promise((resolve) => {
        this._getLevelList().then(({ levelList = [], planBenefits = [] }) => {
          const growthLeft = this._computeNeedGrowth({
            userLevel,
            levelList,
          });

          // 等级组所有等级列表
          this.setYZData({
            levelList,
            needGrowth: growthLeft > 0,
            growthLeft,
          });

          isAllDisabled = (levelList || []).every(
            (levelCard) => !levelCard.isEnabled
          );

          const userLevelAlias = get(userLevel, 'level.levelAlias', '');

          // 用户无等级时的处理
          if (!userLevelAlias) {
            resolve(null);
            return;
          }

          const targetLevel = levelList.filter(
            (level) => level.levelAlias === userLevelAlias
          )[0];
          if (targetLevel) {
            const targetPlanBenefit = planBenefits.filter(
              (planBenefit) => planBenefit.levelAlias === userLevelAlias
            )[0];
            const levelBenefit = this._computeLevelBenefit({
              levelInfo: targetLevel,
            });
            const benefits = this._computeBenefits({
              levelBenefit,
              levelPlanBenefit: targetPlanBenefit,
            });
            const benefitCount = this._computeBenefitCount(benefits);
            const hasGift = this._computeHasGift({ levelBenefit });
            if (!benefitCount) {
              this.setYZData({ giftTip: '' });
            }
            this.setYZData({
              levelInfo: targetLevel,
              levelBenefit,
              levelPlanBenefit: targetPlanBenefit,
              benefitCount,
              hasGift,

              ...benefits,
            });
            resolve(targetLevel);
            return;
          }

          // 找不到用户对应等级
          resolve(null);
        });
      });
    },

    getBenefitGift() {
      return API.getCurrentBenefitInfo().then((benefitGift) => {
        const coupons = (benefitGift?.couponList || [])
          .filter((v) => !!v.status)
          .map((coupon) => {
            // 引导弹框不显示优惠券状态
            return { ...coupon, status: '', smallSize: true };
          });
        const presentList = (benefitGift?.presentList || [])
          .filter((v) => !!v.status)
          .map((present) => {
            // 引导弹框不显示赠品状态
            return { ...present, status: '', smallSize: true };
          });
        const pointsStatus = get(benefitGift, 'points.status', 3);
        const pointsValue = get(benefitGift, 'points.points', 0);
        this.setYZData({
          coupons,
          couponCount: this._computeCouponCount({ coupons }),
          presentList,
          pointsData: {
            value: pointsValue,
            smallSize: true,
          },
          showPoint: pointsValue > 0 && pointsStatus !== 3,
        });
      });
    },

    _getLevelList() {
      return API.getLevelList(1);
    },

    _fetchMissionList() {
      API.fetchMissionList()
        .then((missions) => {
          this.setYZData({
            allMissionCompleted: !missions.some((mission) => !mission.finished),
          });
          return missions;
        })
        .catch(() => {
          return [];
        });
    },

    _getActionText(type, text) {
      if (text) {
        return text;
      }
      switch (type) {
        case 'complete':
          return '获取成长值';
        case 'default':
        default:
          return this.properties.isWeShop ? '了解权益' : '我知道了';
      }
    },

    _getBenefitCount(levelBenefit, planBenefit) {
      let count = 0;
      count = Object.keys(levelBenefit).length;
      if (levelBenefit.diyTemplateList) {
        count--;
        count += levelBenefit.diyTemplateList.length;
      }
      return count + planBenefit.length;
    },

    _computeHasLevel(data) {
      return !!get(data, 'userLevel.level.levelAlias');
    },

    _computeCurrentLevelName(data) {
      return get(data, 'userLevel.level.name');
    },

    _computeCouponCount(data) {
      return (data.coupons || []).reduce((res, coupon) => {
        return res + coupon.number || 0;
      }, 0);
    },

    _computeBenefitCount(data) {
      const { displayBenefits, displayPlanBenefits } = data;
      return displayBenefits.length + displayPlanBenefits.length;
    },

    _computeLevelBenefit(data) {
      return get(data, 'levelInfo.levelBenefit', {});
    },

    _computeNeedGrowth(data) {
      const { nowLevel } = this.properties;
      const minGrowth = get(
        data,
        'levelList[0].levelGrantConditionList[0].minGrowth',
        0
      );

      return minGrowth - nowLevel.currentGrowth;
    },

    _computeBenefits(data) {
      const levelBenefit = get(data, 'levelBenefit', {});
      const levelPlanBenefit = get(data, 'levelPlanBenefit', {});

      let formattedPlanBenefits = planBenefitAdapter(levelPlanBenefit);

      formattedPlanBenefits = formattedPlanBenefits.map((planBenefit) => {
        const target = this.properties.nowBenefitList.find(
          (item) => item.key === planBenefit.name
        );

        if (target) {
          return { ...planBenefit, icon: target.icon };
        }
        return planBenefit;
      });

      return {
        displayBenefits: formatBenefits(levelBenefit),
        displayPlanBenefits: formattedPlanBenefits,
      };
    },

    _computeHasGift(data) {
      const levelBenefit = get(data, 'levelBenefit', {});
      const { isLevelChange } = this.data;
      return (
        ['coupon', 'present', 'points'].some((key) => !!levelBenefit[key]) ||
        isLevelChange
      );
    },

    addLog(params) {
      app.logger?.log(params);
    },
  },
  observers: {
    visible(val) {
      if (!val) {
        const { scene: originScene } = this.data;
        const scene =
          originScene === GUIDE_SCENE_ENUM.COMPLETE
            ? this.data.hasLevel
              ? GUIDE_SCENE_ENUM.BE_MEMBER
              : GUIDE_SCENE_ENUM.NOT_MEMBER
            : originScene;
        API.readPopContent({
          scene,
        })
          .then(() => {})
          .catch((msg) => {
            Toast.fail(msg);
          });
      } else {
        // 弹窗打开时计数器清零
        this.actionProcessTimes = 0;
      }
    },
  },
});
