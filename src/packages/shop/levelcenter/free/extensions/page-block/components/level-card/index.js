import navigate from '@/helpers/navigate';

import { getLevelBgInfo } from '@youzan/scrm-common';
import formatDate from '@youzan/utils/date/formatDate';
import format from '@youzan/utils/money/format';
import args from '@youzan/weapp-utils/lib/args';

import WscComponent from 'pages/common/wsc-component/index';

import { Hummer } from 'shared/utils/hummer';

import openWebView from 'utils/open-web-view';

import {
  ConsumeMissionType,
  LevelMode,
  LevelType,
} from '../../../../../level-common/utils/constant';
import { markWithLabel } from '../../../../../level-common/utils/util';

const app = getApp();
const kdtId = app.getKdtId();

const InfoTextEnum = {
  GOTOBUY: '去购买',
  AUTHORIZED: '立即授权',
  GETMOREGROWTH: '获得更多成长值',
  COMPLETEINFO: '完善信息',
  REMOVED: '重新入会',
};

WscComponent({
  properties: {
    card: {
      type: Object,
      value: {},
    },
    nowLevel: Object, // 当前等级
    expiryDesc: String, // 会员有效期
    nextCard: Object, // 下一个未达到等级信息，
    disabled: Boolean,
    hasLevel: Boolean, // * 是否达到了LV.1
    btnShow: Boolean,
    redirect: String,
    alias: String,
    hasCompleted: Boolean,
    hasMobile: {
      type: Boolean,
      observer(nv, ov) {
        if (nv !== ov) {
          this.updateLevelCardData();
        }
      },
    },

    requireMobile: {
      type: Boolean,
      observer(nv, ov) {
        if (nv !== ov) {
          this.updateLevelCardData();
        }
      },
    },

    mobileAuthorized: {
      type: Boolean,
      value: false,
      observer(nv, ov) {
        if (nv !== ov) {
          this.updateLevelCardData();
        }
      },
    },
    recruitParams: Object,
    isConsume: Boolean, // * 是否为消费行为模式
    mode: Number, // 当前会员等级模式
    isThirdPartyCenter: Boolean, // * 等级信息计算是否在三方中心
    isRemoved: {
      type: Boolean,
      value: false,
      observer(nv, ov) {
        if (nv !== ov) {
          this.updateLevelCardData();
        }
      },
    }, // 是否手动退出当前等级
    // TODO: 20220906 储值模式需求，相关展示逻辑收拢到该字段。成长/消费模式还是老逻辑，待技改
    cardTextSource: Object,
    pointsInfo: {
      type: Object,
      value: {},
    },
    userLevel: {
      type: Object,
      value: {},
      observer(nv) {
        this.updateExpiryDesc(nv);
      },
    },
    memberCenterLogoUrl: String,
    levelProgressList: Array,
  },
  data: {
    btnText: '',
    levelStatus: '', // * 等级状态信息
    bgStyle: '',
    receiveProcess: 0,
    consumeProcess: 0, // * 消费行为模式的进度条
    isFullLevel: false, // * 当前是否已满级
    bottomText: {},
    curLevelBottomLeft: {},
    needMoreValue: false,
    showMobilePopup: false,
    priorityDisplay: {}, // * 优先展示信息对象
    isSingleAmountPriority: false, // * 消费行为模式下，优先项是否为最大单笔消费
    isTopLevel: false,
    showCurrentValue: false,
    showGoPrepaidBtn: false,
    expiryDesc: null, // 等级有效期
    hasCurrentLevel: false, // 是否已获取当前等级
    reflectiveStyle: '',
    keepLevelProgress: '',
  },
  methods: {
    updateExpiryDesc(nv) {
      const { isThirdPartyCenter } = this.properties;

      let expiryDesc = '';

      if (isThirdPartyCenter) {
        expiryDesc = '';
      } else if (!nv || !nv.level || !nv.level.levelAlias) {
        expiryDesc = '未入会';
      } else if (nv && nv.termEndAt === 0) {
        expiryDesc = '永久有效';
      } else {
        expiryDesc = `当前等级有效期至 ${formatDate(
          nv.termEndAt,
          'YYYY-MM-DD'
        )}`;
      }

      this.setYZData({ expiryDesc });
    },

    getBtnText() {
      const {
        hasMobile,
        hasCompleted,
        nowLevel,
        requireMobile,
        mobileAuthorized,
        mode,
        isRemoved,
      } = this.properties;
      if (
        requireMobile &&
        (!hasMobile || !mobileAuthorized) &&
        nowLevel.levelValue === 0
      ) {
        this.viewLog(InfoTextEnum.AUTHORIZED);
        return InfoTextEnum.AUTHORIZED;
      }
      // 重新入会
      if (isRemoved) {
        return InfoTextEnum.REMOVED;
      }
      // 非会员显示完善信息
      if (!hasCompleted && nowLevel.levelValue === 0) {
        this.viewLog(InfoTextEnum.COMPLETEINFO);
        return InfoTextEnum.COMPLETEINFO;
      }

      // * 消费模式则展示去购买
      if (mode === LevelMode.CONSUME) {
        return InfoTextEnum.GOTOBUY;
      }

      return InfoTextEnum.GETMOREGROWTH;
    },

    viewLog(type) {
      switch (type) {
        case InfoTextEnum.COMPLETEINFO:
        case InfoTextEnum.AUTHORIZED:
          app.logger &&
            app.logger.log({
              et: 'view', // 事件类型
              ei: 'view_fill_button', // 事件标识
              en: '入会按钮曝光', // 事件名称
            });
      }
    },

    clickLog(type) {
      switch (type) {
        case InfoTextEnum.COMPLETEINFO:
        case InfoTextEnum.AUTHORIZED:
          app.logger &&
            app.logger.log({
              et: 'click', // 事件类型
              ei: 'click_fill', // 事件标识
              en: '入会按钮点击', // 事件名称
            });
      }
    },

    getLevelStatus() {
      const {
        hasMobile,
        card,
        nowLevel,
        requireMobile,
        hasCompleted,
        mobileAuthorized,
      } = this.properties;

      if (nowLevel.levelValue === card.levelValue) {
        return 'levelName';
      }

      // 手机授权
      if (
        requireMobile &&
        (!hasMobile || !mobileAuthorized) &&
        nowLevel.levelValue === 0
      ) {
        return 'authorized';
      }
      if (!hasCompleted) {
        // * 未完善信息
        return 'needComplete';
      }
      // * 未获得等级则展示
      if (nowLevel.levelValue === 0) {
        return 'needMore';
      }
      return 'levelName';
    },

    // 卡面左下角（未达到当前等级）
    getBottomText() {
      const {
        nowLevel,
        card,
        isThirdPartyCenter,
        mode,
        cardTextSource,
        levelProgressList,
      } = this.properties;

      const levelProgress = levelProgressList?.find(
        (item) => item.levelAlias === card.alias
      );

      if (levelProgress?.upLevelProgress) {
        return {
          htmlSnip: markWithLabel(levelProgress.upLevelProgress, [
            '<span class="big">',
            '</span>',
          ]),
          showHtmlSnip: true,
        };
      }

      if (isThirdPartyCenter) {
        return { desc: '' };
      }

      if (mode === LevelMode.STORE) {
        const {
          isCurrentLevel,
          currentStoreBalance,
          neededStoreBalance,
          hasEnterThreshold,
        } = cardTextSource;
        // 已是当前等级 && 不是最高等级
        if (isCurrentLevel) {
          this.setYZData({ showCurrentValue: true });
          const currentValHtmlSnip = `<span class="bigger">${format(
            currentStoreBalance,
            true,
            false
          )} </span>元店铺余额`;
          return { showHtmlSnip: true, currentValHtmlSnip };
        }

        if (!hasEnterThreshold) {
          return {
            showHtmlSnip: true,
            htmlSnip: '',
          };
        }

        const htmlSnip = `单笔充值 <span class="big">${format(
          neededStoreBalance,
          true,
          false
        )}</span> 元升级为该等级`;

        return { showHtmlSnip: true, htmlSnip };
      }

      if (mode === LevelMode.CONSUME) {
        if (card.consumeNeed === '无门槛') {
          return {
            showHtmlSnip: true,
            htmlSnip: '',
          };
        }
        return {
          showHtmlSnip: true,
          htmlSnip: card.consumeNeed,
        };
      }

      if (card.minGrowth <= nowLevel.currentGrowth) {
        this.setYZData({ showCurrentValue: true });
        const htmlSnip = `<span class="bigger">${nowLevel.currentGrowth}</span>成长值`;
        return { showHtmlSnip: true, htmlSnip };
      }

      if (
        nowLevel.levelValue < card.levelValue &&
        card.minGrowth - nowLevel.currentGrowth > 0
      ) {
        const htmlSnip = `再获得 <span class="big">${
          card.minGrowth - nowLevel.currentGrowth
        } </span>成长值升级为该等级`;

        return { showHtmlSnip: true, htmlSnip };
      }
      return '';
    },

    updateLevelCardData() {
      const btnText = this.getBtnText();
      const levelStatus = this.getLevelStatus();
      this.setYZData({
        btnText,
        levelStatus,
      });
    },

    updateLevelData() {
      const { mode, nowLevel, card, cardTextSource, pointsInfo } =
        this.properties;
      const style = `height: 100%; background-size: cover; background-position: center;`;
      const _levelStyle = getLevelBgInfo({
        colorCode: card.color,
        cover: card.image,
        levelType: LevelType.FREE,
      });

      const keepLevelProgress =
        mode === LevelMode.CONSUME ? nowLevel?.keepLevelProgress : null;
      const bgStyle = `${style}background:${_levelStyle.backgroundStyle}`;
      const { reflectiveStyle } = _levelStyle;
      const receiveProcess =
        nowLevel.currentGrowth >= card.minGrowth
          ? 100
          : Math.ceil((nowLevel.currentGrowth * 100) / card.minGrowth);
      const needMoreValue = nowLevel.levelValue < card.levelValue;
      const contentMap = {
        // 成长值 -》 xx成长值  消费--》xx积分  储值--》xx店铺余额
        [LevelMode.GROWTH]: {
          val: nowLevel.currentGrowth,
          suffix: '成长值',
        },
        [LevelMode.CONSUME]: {
          val: pointsInfo.currentPoints,
          suffix: pointsInfo.pointsName || '积分',
        },
        [LevelMode.STORE]: !Number.isNaN(
          Number(cardTextSource.currentStoreBalance)
        )
          ? {
              val: format(cardTextSource.currentStoreBalance, true, false),
              suffix: '元店铺余额',
            }
          : null,
      };

      const btnText = this.getBtnText();
      const levelStatus = this.getLevelStatus();
      const bottomText = this.getBottomText();
      const curLevelBottomLeft = contentMap[mode];

      this.setYZData({
        btnText,
        levelStatus,
        bgStyle,
        receiveProcess,
        bottomText,
        curLevelBottomLeft,
        needMoreValue,
        reflectiveStyle,
        keepLevelProgress,
      });
    },

    checkTopLevel() {
      const { nextCard, nowLevel, card, priorityDisplay, mode } =
        this.properties;

      if (
        (!nextCard &&
          nowLevel.levelValue >= card.levelValue &&
          mode !== LevelMode.CONSUME) ||
        (mode === LevelMode.CONSUME && priorityDisplay.isFullLevel)
      ) {
        this.setYZData({
          isTopLevel: true,
          showCurrentValue: false,
        });
      }
    },

    handleQRClick() {
      this.triggerEvent('handleQRClick', this.properties.card.name);
    },

    bindGetPhoneNumber() {
      const btnText = this.getBtnText();
      if (btnText === InfoTextEnum.AUTHORIZED) {
        this.clickLog(InfoTextEnum.AUTHORIZED);
      }
      this.triggerEvent('bindMobile', this.properties.card.name);
    },

    handleClick() {
      // * 如果是去购买，则跳转到商城首页
      const btnText = this.getBtnText();
      if (btnText === InfoTextEnum.GOTOBUY) {
        this.goToBuy();
        return;
      }
      const app = getApp();
      const kdtId = app.getKdtId();
      const { redirect, alias, hasCompleted } = this.properties;
      // 重新入会且资料完整，直接变成会员
      if (btnText === InfoTextEnum.REMOVED) {
        if (hasCompleted) {
          this.triggerEvent('handleRejoin');
          return;
        }
      }
      if (btnText === InfoTextEnum.COMPLETEINFO) {
        this.clickLog(InfoTextEnum.COMPLETEINFO);
      }
      const query = {
        kdtId,
        eT: Date.now(),
        fromScene: 'complete',
        ...this.properties.recruitParams,
      };
      if (alias) query.alias = alias;
      openWebView(redirect, {
        query,
      });
    },
    goToBuy() {
      this.navigateWithUrlParams({
        url: '/v2/home',
        weappUrl: '/pages/home/<USER>/index',
        type: 'switchTab',
        aliappUrl: '/pages/home/<USER>/index',
        qqUrl: '/pages/home/<USER>/index',
      });
    },
    goToPointsStore() {
      const { mode } = this.properties;

      if (mode === LevelMode.CONSUME) {
        this.navigateWithUrlParams({
          url: '/wscump/pointstore/pointcenter',
        });
      }
    },
    goToPrepaid() {
      navigate.redirect({
        url: `/packages/pre-card/recharge/index?entry=1&kdtId=${kdtId}&fromScene=MemberCenter`,
      });
    },
    navigateWithUrlParams({ url, weappUrl, type }) {
      if (weappUrl) {
        if (type && navigate[type]) {
          navigate[type]({ url: weappUrl });
        } else {
          navigate.navigate({ url: weappUrl });
        }
      } else {
        openWebView(args.add(url, { kdt_id: kdtId }));
      }
    },
  },
  attached() {
    this.updateLevelData();
    this.checkTopLevel();
  },
  ready() {
    app.trigger('first-render', 'level-free');
    Hummer.mark?.log?.({ tag: 'user-center', scene: ['appLaunch', 'route'] });
  },
  observers: {
    card(card) {
      const priorityDisplay = card.priorityUpgradeCondition || {};
      this.setYZData({
        priorityDisplay,
      });
      const { current, condition, need, isFullLevel } = priorityDisplay;
      let rate = 0;
      if (!Object.keys(priorityDisplay).length || need <= 0 || isFullLevel) {
        // * 满级情况下进度条拉满
        rate = 100;
      } else if (current > condition) {
        rate = 100;
      } else {
        rate = Math.ceil((current / condition) * 100);
      }
      this.setYZData({
        consumeProcess: rate,
        isFullLevel,
      });
    },
    'isConsume, priorityDisplay': function (isConsume, priorityDisplay) {
      this.setYZData({
        isSingleAmountPriority:
          isConsume && priorityDisplay.type === ConsumeMissionType.SINGLE,
      });
    },
    'mode,card,nowLevel,hasLevel': function (mode, card, nowLevel, hasLevel) {
      this.setYZData({
        // 储值模式下 已入会&&未达到当前等级 时，展示’去充值‘按钮
        showGoPrepaidBtn:
          mode === LevelMode.STORE &&
          hasLevel &&
          nowLevel.levelValue !== card.levelValue,
      });
    },
    'hasLevel,nowLevel,card': function (hasLevel, nowLevel, card) {
      this.setYZData({
        hasCurrentLevel: hasLevel && nowLevel.levelValue === card.levelValue,
      });
    },
  },
});
