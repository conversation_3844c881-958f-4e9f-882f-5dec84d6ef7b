import { PlanTypeEnum } from 'constants/member-constants';
import WscComponent from 'pages/common/wsc-component/index';

const app = getApp();

WscComponent({
  properties: {
    planDetail: {
      type: Object,
      value: {},
    },
  },
  data: {
    planIcon: '',
    nickName: '',
    PlanTypeEnum,
  },
  attached() {
    this.getNickName();
    this.getPlanIcon();
  },
  methods: {
    getNickName() {
      const userInfo = app.getUserInfoSync() || {};
      this.setYZData({
        nickName: userInfo.nickname,
      });
    },
    getPlanIcon() {
      const planIcon = this._computedIcon();
      this.setYZData({ planIcon });
    },
    onViewPlan() {
      this.triggerEvent('open', this.properties.planDetail.planId);
    },
    _computedIcon() {
      switch (this.properties.planDetail.planType) {
        case PlanTypeEnum.Birthday:
          return 'https://b.yzcdn.cn/public_files/c6a96d4fcc0c3633fe4c946aa77880a2.png';
        case PlanTypeEnum.Festival:
          return 'https://b.yzcdn.cn/public_files/ae1186b9558570e9ea6aceb9960fe020.png';
        case PlanTypeEnum.MemberDay:
          return 'https://b.yzcdn.cn/public_files/26829beabebd5775fdae64a0caff5e6f.png';
        default:
          return '';
      }
    },
  },
});
