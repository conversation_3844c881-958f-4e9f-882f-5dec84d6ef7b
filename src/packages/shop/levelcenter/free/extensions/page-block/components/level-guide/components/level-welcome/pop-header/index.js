/* eslint @youzan-open/tee/valid-same-extension-import: "off" */
import MoneyFormat from '@youzan/weapp-utils/lib/money';

import WscComponent from 'pages/common/wsc-component/index';

import { properties } from '../../../mixins';
import { LevelMode } from '../../../../../../../../level-common/utils/constant';
import { markWithLabel } from '../../../../../../../../level-common/utils/util';

const app = getApp();

WscComponent({
  properties: {
    title: String,
    hasLevel: Boolean,
    benefitCount: Number,
    couponCount: Number,
    scene: properties.scene,
    growthLeft: Number,
    isConsume: Boolean,
    joinGapInfo: Object,
    bizDataMap: Object,
    hasMobile: Boolean,
    showMissionList: Boolean,
    isLevelChange: <PERSON>olean,
    currentLevelName: String,
    isBeMember: Boolean,
    mode: Number,
    memberName: {
      type: String,
      value: '会员',
    },
    isWeShop: {
      type: Boolean,
      value: false,
    },
    beMemberTip: String,
  },

  data: {
    nickName: '',
    showTitle: '',
    couponName: '',
    subTitle: '',
  },

  attached() {
    this.getNickName();
    this.setCouponName();
    this.getSubTitle();
  },

  methods: {
    getNickName() {
      const userInfo = app.getUserInfoSync() || {};
      this.setYZData({
        nickName: userInfo.nickname,
      });
    },

    setCouponName() {
      this.setYZData({
        couponName:
          this.properties.benefitCount === 0
            ? `${this.properties.memberName}专享优惠券`
            : '优惠券',
      });
    },

    // 未入会 或 等级变更时 ，副标题内容
    generateSubTitle() {
      const {
        mode,
        joinGapInfo,
        isBeMember,
        hasLevel,
        growthLeft,
        couponCount,
        benefitCount,
        isLevelChange,
        memberName,
        beMemberTip,
      } = this.properties;
      if (isLevelChange && hasLevel) {
        const levelChangePrefix = '已重新计算等级，';
        return benefitCount
          ? `${levelChangePrefix}享<span class='highlight'>${benefitCount}</span>大${memberName}权益`
          : `享更多${memberName}福利`;
      }

      if (!isBeMember && !hasLevel) {
        if (beMemberTip) {
          return markWithLabel(beMemberTip, [
            '<span class="highlight">',
            '</span>',
          ]);
        }

        let {
          neededMinTradeAmount: single = 0, // 单笔消费差额(分)
          neededMinStoredValue: singleStore = 0, // 单笔充值差额(分)
          neededTotalTradeAmount: amount = 0, // 累计消费差额(分)
          // eslint-disable-next-line prefer-const
          neededTotalTradeCount: count = 0, // 消费笔数差额(笔)
        } = joinGapInfo;

        const needMore = single || amount || count || singleStore;

        if (mode === LevelMode.CONSUME && needMore) {
          amount = amount ? MoneyFormat(amount).toYuan() : '';
          single = single ? MoneyFormat(single).toYuan() : '';
          singleStore = singleStore ? MoneyFormat(singleStore).toYuan() : '';

          const conditions = [];
          let conditionPrefix = '';

          if (amount)
            conditions.push(`<span class="highlight">${amount}</span>元`);
          if (count)
            conditions.push(`<span class="highlight">${count}</span>笔`);
          if (single)
            conditions.push(
              `单笔消费<span class="highlight">${single}</span>元`
            );
          if (singleStore) {
            conditions.push(
              `单笔充值<span class="highlight">${singleStore}</span>元`
            );
          }

          if (conditions.length > 0) {
            conditionPrefix =
              single && conditions.length === 1 ? '再' : '再消费';
          }

          const desc = conditionPrefix + conditions.join('或');

          return `${desc}即可成为${memberName}`;
        }

        if (mode === LevelMode.GROWTH && joinGapInfo.neededGrowth > 0) {
          return `再获得<span class='highlight'>${growthLeft}</span>成长值即可成为${memberName}`;
        }

        if (mode === LevelMode.STORE && joinGapInfo.neededStoreBalance) {
          const money = MoneyFormat(joinGapInfo.neededStoreBalance).toYuan();
          return `单笔充值<span class='highlight'>${money}</span>元即可成为${memberName}`;
        }
      }

      if (couponCount) {
        const couponName =
          benefitCount === 0 ? `${memberName}专享优惠券` : '优惠券';
        const couponDesc = `获得<span class='highlight'>${couponCount}张</span>${couponName}`;
        const benefitDesc = `享<span class='highlight'>${benefitCount}大</span>${memberName}权益`;

        return `${couponDesc}，${benefitDesc}`;
      }

      return benefitCount
        ? `享<span class='highlight'>${benefitCount}</span>大${memberName}权益`
        : `享更多${memberName}福利`;
    },

    getSubTitle() {
      const subTitle = this.generateSubTitle();
      this.setYZData({
        subTitle,
      });
    },
  },
});
