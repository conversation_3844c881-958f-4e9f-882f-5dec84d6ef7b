<view wx:if="{{ list.length }}">
  <swiper class="plan-list-swiper" autoplay="{{ !isShowPlanDialog }}" vertical="true" circular="true" interval="5000">
  <!-- <swiper-item wx:for="{{ list }}" wx:key="unique" catchtouchmove="stopTouchMove"> -->
    <swiper-item wx:for="{{ list }}" wx:key="unique">
      <plan-item plan-detail="{{ item }}" bind:open="openPlanDialog" />
    </swiper-item>
  </swiper>
  <view class="split-border" />
  <scene-market-subscribe wx:if="{{ isShowPlanDialog }}" plan-id="{{ specificPlanId }}" bindclose="closePlanDialog" fromPage="memberCenterClick" bind:bindMobile="onBindMobile" />
</view>