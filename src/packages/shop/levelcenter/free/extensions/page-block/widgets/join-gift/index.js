import navigate from '@/helpers/navigate';

import { RantaWidget } from 'shared/common/base/ranta/widget';

RantaWidget({
  properties: {
    benefitGift: {
      type: Object,
    },
    isMember: Boolean,
    showRegisterBtn: Boolean,
    bizDataMap: {
      type: Object,
      value: {},
    },
    mode: Number,
    neededMoreStoreBalance: Boolean,
    memberName: {
      type: String,
      value: '会员',
    },
  },
  data: {
    couponList: [],
    presentList: [],
    point: {},
    visible: false,
  },
  methods: {
    closeMemberGift() {
      wx.setStorageSync('hideMemberGiftModule', true);
      this.triggerEvent('hideMemberGift');
    },

    btnConfirm() {
      this.triggerEvent('bindMobile');
    },

    getMoreGrowth() {
      navigate.navigate({
        url: '/packages/user/task-center/index',
      });
    },
  },
  observers: {
    benefitGift(val) {
      const { couponList = [], presentList = [], points = {} } = val || {};
      let point = {};
      if (points?.points > 0) {
        point = {
          value: points.points,
          status: points?.status,
        };
      }
      couponList.forEach((coupon) => {
        coupon.preKey = '' + coupon.status + coupon.couponId;
      });

      this.setYZData({
        couponList,
        presentList,
        point,
        visible:
          couponList?.length > 0 || presentList?.length > 0 || point?.value > 0,
      });
    },
  },
});
