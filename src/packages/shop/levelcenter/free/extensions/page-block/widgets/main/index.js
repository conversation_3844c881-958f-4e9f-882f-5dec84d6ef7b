import pageComponentBehavior from '@/custom-tab-bar-v2/native-helper/page-component-behavior';
import navs from '@/helpers/navigate';
import { openWechatCard } from '@/utils/wechat-card';

import { mapActions, mapState } from '@ranta/store';

import { mapData, mapProcess } from '@youzan/ranta-helper-tee';
import get from '@youzan/weapp-utils/lib/get';

import { RantaWidget } from 'shared/common/base/ranta/widget';
import { getHeight } from 'shared/utils/nav-config';

import { getGuideBizDataMap } from 'utils/guide';
import openWebView from 'utils/open-web-view';

import * as API from '../../api';
import {
  LevelMode,
  LevelUpgradeType,
} from '../../../../../level-common/utils/constant';
import { tryToBindShoppingGuideRelation } from '../../../../../level-common/utils/util';

const app = getApp();
const kdtId = app.getKdtId();
const MEMBER_SHOP_CONFIGS_KEY = 'guide_members_add_groups';

RantaWidget({
  behaviors: [pageComponentBehavior],
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持(包括命名slot)
  },

  data: {
    topBarHeight: getHeight() || 0,
    memberLevelBenefit: true,
    customNameColor: 'color: #ffe8ce',
    customBg: 'background-color: transparent',
    hasContent: false,
    globalCustomLoading: true,
    loading: true,
    memberCenterLogoUrl: '',
    memberShopConfigs: {},
    memberName: '会员',
    isWeShop: false,
  },

  ready() {
    mapProcess(this, {
      showMemberLevelBenefits: (show) => this.showMemberLevelBenefits(show),
    });
  },

  attached() {
    mapState(this, [
      'deviceType',
      'identityNo',
      'hasMobile',
      'redirectUrl',
      'kdtId',
      'toMissionCenter',
      'current',
      'isWeixinCardEnabled',
      'userLevel',
      'userInfo',
      'cards',
      'nowLevel',
      'nextCard',
      'groupAlias',
      'hasCompleted',
      'nowBenefitList',
      'nowLevelAlias',
      'showInfo',
      'showMission',
      'isShow',
      'guideScene',
      'requireMobile',
      'mobileAuthorized',
      'fetchMissionFinished',
      'isSupportGoodsRecommend',
      'goodsRecommendTitleConfig',
      'recruitParams',
      'pageQuery',
      'isConsume',
      'mode',
      'hasLevel',
      'isFullLevel',
      'isThirdPartyCenter',
      'benefitGift',
      'isRemoved',
      'showNewMemberGift',
      'isUnionCode',
      'showTabBar',
      'bizDataMap',
      'needSubscribe',
      'showSubscribeFail',
      'crmFromScenePlanId',
      'showBenefitsAlert',
      'showRegisterCompleteBar',
      'neededMoreStoreBalance',
      'showRegisterBtn',
      'pointsInfo',
      'joinGapInfo',
      'isReady',
      'memberCodeRefreshInterval',
      'isFeatureMemberCenter',
      'branchStoreOpenOnline',
      'globalCustomLoading',
      'loading',
      'isCrmShop',
      'rootKdtId',
      'joinProcessScene',
      'levelProgressList',
      'beMemberTip',
    ]);

    mapActions(this, [
      'fetchData',
      'SET_USER_INFO',
      'SET_IS_SHOW',
      'SET_BIND_MOBILE',
      'SET_CARD_NAME',
      'handleSubscribe',
      'handleSubscribeCallback',
      'closeSubscribeFailPopup',
      'handleSwipeChange',
      'missionsNumber',
      'jumpToMissionCenter',
      'handleAfterFetchMission',
      'hideMemberGift',
      'goToCompleteInfo',
      'handleProtocol',
      'reloadData',
      'jumpCrmWechatCardPortal',
    ]);
    mapData(this, ['isWeShop']);
    mapData(this, {
      shopConfigs: (shopConfigs) => {
        this.setYZData({
          memberCenterLogoUrl: shopConfigs?.member_center_logo_url || '',
        });
      },
    });
    mapData(this, {
      memberConfig: (memberConfig) => {
        this.setYZData({
          memberName: memberConfig.name,
        });
      },
    });

    if (kdtId) {
      this.getShopConfig(MEMBER_SHOP_CONFIGS_KEY)
        .then((data) => {
          const memberShopConfigs = JSON.parse(
            data.configs[MEMBER_SHOP_CONFIGS_KEY]
          );
          this.setYZData({
            memberShopConfigs,
          });
        })
        .catch(() => {});
    }
  },

  methods: {
    getShopConfig(key) {
      return API.getShopConfig({
        kdtId,
        shopConfig: JSON.stringify([key]),
      });
    },

    reloadDataWithCtx(isRejoinMember) {
      this.reloadData({
        isRejoinMember,
        ctx: this.ctx,
      });
    },

    // 重新入会
    handleRejoin() {
      const recruitParams = this.data;
      const params = {
        ...recruitParams,
        actionType: LevelUpgradeType.REENTER,
      };
      API.recalculateUserLevel(params)
        .then(() => {
          // 重新加载会员等级页面
          wx.showToast({
            title: '重新入会成功',
            icon: 'none',
            duration: 1000,
          });
          this.reloadDataWithCtx(true);
        })
        .catch((e) => {
          wx.showToast({
            title: e.msg,
            icon: 'none',
            duration: 1000,
          });
        });
    },

    handleAfterAuthorized(e) {
      const { detail } = e;
      const { branchStoreOpenOnline } = this.data;
      this.fetchData({ branchStoreOpenOnline });
      this.handleAllTriggerEvent({
        type: 'bindMobile',
        detail,
      });
    },

    handleAllTriggerEvent(e) {
      const { type, detail } = e;
      const {
        redirectUrl,
        kdtId,
        groupAlias,
        neededMoreStoreBalance,
        isWeixinCardEnabled,
        pageQuery,
      } = this.data;

      if (type === 'hasAuth') {
        this.SET_USER_INFO({ userInfo: detail });
      } else if (type === 'closeCodeDialog') {
        this.SET_IS_SHOW();
        if (this.timer) {
          clearInterval(this.timer);
          this.timer = null;
        }
      } else if (type === 'bindMobile') {
        this.SET_BIND_MOBILE({
          hasMobile: true,
          mobileAuthorized: true,
        });
        const query = {
          ...getGuideBizDataMap(pageQuery), // 携带导购标
          alias: groupAlias,
          fromScene: 'complete',
        };
        tryToBindShoppingGuideRelation(pageQuery).then(() => {
          // 是储值模式&&未达到储值门槛，跳转充值中心
          if (neededMoreStoreBalance) {
            navs.redirect({
              url: `/packages/pre-card/recharge/index?entry=1&kdtId=${kdtId}&fromScene=MemberCenter`,
            });
          } else {
            if (isWeixinCardEnabled) {
              const { from_params, bizType } = pageQuery;
              const { isCrmShop } = this.data;

              // crm 商家不直接打开微信会员卡，而是走中间页逻辑。或者微信小店场景，为了流程更短，直接唤起原生开卡
              if (isCrmShop && !this.data.isWeShop) {
                this.jumpCrmWechatCardPortal();
                return;
              }

              if (this.data.isWeShop) {
                app.globalData.doBindWeixinXiaoDianMember = true;
              }

              openWechatCard(
                {
                  from_params,
                  // 微信小店bizType 15
                  bizType: this.data.isWeShop ? 15 : bizType,
                },
                () => this.reloadDataWithCtx(true)
              );
              return;
            }
            let newQuery = query;
            if (this.data.isWeShop) {
              // 微信小店入会前添加query
              [newQuery] = this.ctx.process.invoke(
                'beforeSignInMemberAddQuery',
                query
              );
            }
            openWebView(redirectUrl, {
              query: {
                ...newQuery,
                fromBiz: 'levelcenter',
                eT: Date.now(),
                kdt_id: getApp().getKdtId(),
              },
            });
          }
        });
      } else {
        return null;
      }
    },

    handleQRClick(e) {
      const { detail } = e;
      this.SET_CARD_NAME({ cardName: detail });
      this.ctx.data.isShowMemberCode = true;
    },

    // 退出当前等级
    removeLevel() {
      return this.ctx.cloud.invoke('beforeQuitMemberLevel').then(() => {
        this.ctx.cloud.invoke('beforeOutMemberLevel').then(() => {
          const { userLevel, mode } = this.data;
          const levelAlias = get(userLevel, `level.levelAlias`, null);
          const content =
            mode === LevelMode.STORE
              ? '退出后，你将不再继续享有会员权益。用户需重新充值才可享有会员身份。'
              : '退出后，你将不再享受相应的等级权益。确定要退出吗？';
          wx.showModal({
            title: '退出当前等级',
            content,
            success: (res) => {
              if (res.confirm) {
                wx.showLoading({
                  title: '请求中',
                });
                API.removeLevel({
                  operatorType: 'INITIATIVE_QUIT', // LevelUpgradeType.INITIATIVE_QUIT 5 退出，后端接口要求传字符串
                  levelAlias,
                })
                  .then(() => {
                    wx.hideLoading();
                    wx.showToast({
                      title: '成功退出当前等级',
                      icon: 'none',
                      duration: 1000,
                    });
                    this.reloadDataWithCtx();
                    if (this.data.isWeShop) {
                      this.ctx.event.emit('removeLevel');
                    }
                  })
                  .catch((e) => {
                    wx.hideLoading();
                    wx.showToast({
                      title: e.msg,
                      icon: 'none',
                      duration: 1000,
                    });
                  });
              } else if (res.cancel) {
                return true;
              }
            },
          });
        });
      });
    },

    showMemberLevelBenefits(show = true) {
      this.setData({ memberLevelBenefit: show });
    },

    linkToRule() {
      openWebView('/wscuser/levelcenter/rule', {
        query: { kdt_id: this.data.kdtId },
      });
    },

    handleHeightCalculate() {
      this.setYZData({ hasContent: true });
    },
    showLiveCodeAddFansPop(e) {
      const {
        qrcode: qrcodeId,
        qrcodeUrl,
        scene: qrcodeScene,
      } = this.data.memberShopConfigs.liveCode;

      this.ctx.process.invoke('showLiveCodeAddFansPop', {
        qrcodeId,
        qrcodeScene,
        qrcodeUrl,
        source: 'member_guide',
        kdtId,
        display: e.detail,
      });
    },
  },
});
