import { RantaWidget } from 'shared/common/base/ranta/widget';

const app = getApp();
RantaWidget({
  properties: {
    isShow: {
      type: Boolean,
    },
    isWeixinCardEnabled: {
      type: Boolean,
      require: true,
    },
    pageQuery: {
      type: Object,
      required: true,
    },
    showBottomPadding: {
      type: Boolean,
    },
    bizDataMap: {
      type: Object,
      value: {},
    },
    neededMoreStoreBalance: {
      type: Boolean,
    },
  },
  data: {
    btnText: '立即入会',
  },
  methods: {
    btnConfirm() {
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'click_fill', // 事件标识
          en: '入会按钮点击', // 事件名称
        });
      this.triggerEvent('bindMobile');
    },
  },
  attached() {
    app.logger &&
      app.logger.log({
        et: 'view', // 事件类型
        ei: 'view_fill_button', // 事件标识
        en: '入会按钮曝光', // 事件名称
      });
    this.setYZData({
      btnText: this.properties.neededMoreStoreBalance ? '充值入会' : '立即入会',
    });
  },
});
