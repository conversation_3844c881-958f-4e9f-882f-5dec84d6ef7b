<global-custom-loading wx:if="{{ globalCustomLoading }}" show="{{ loading }}" />
<view class="{{ deviceType }} {{ showTabBar ? 'pg-c-tb' : '' }} {{ showRegisterBtn ? 'pg-c-fb' : '' }}">
  <view wx:if="{{ isFeatureMemberCenter && !isUnionCode }}">
    <view class="feature-page__top-hook" />
    <showcase-container />
    <custom-tab-bar wx:if="{{ showTabBar }}" forceShow />
  </view>
  <block wx:else>
    <view wx:if="{{ isReady }}" class="ctr {{ isUnionCode ? 'uc-ctr' : '' }}">
      <view class="nav-bar-title" style="padding-top: {{ topBarHeight/2 }}px;height:{{topBarHeight}}px">
        <text>{{ memberName }}中心</text>
      </view>
      <!-- 非会员 && 已经满足入会条件 && 没有完善必填信息 -->
      <view wx:if="{{ showRegisterCompleteBar }}" class="lc-cmp-info" bindtap="goToCompleteInfo">
        <view class="text">
          <van-icon custom-class="arrow-custom" name="https://img01.yzcdn.cn/public_files/f31c2cc64cc43f0ef9e35de9eb29067f.png" size="16" custom-style="margin-right: 8px" />
          完善资料尊享更多{{ memberName }}权益
        </view>
        <view>
          <text class="lc-lvl-tip">去完善</text>
          <van-icon color="#9a640c" custom-style="vertical-align: -2px;" name="arrow" custom-class="arrow-custom" />
        </view>
      </view>
      <view class="lvl-wrap">
        <view class="c-bg" />
        <!-- 等级滚动 -->
        <view class="sw-wrap sw-height">
          <user-authorize wx:if="{{ requireMobile && !mobileAuthorized && !hasMobile }}" authTypeList="{{ ['mobile'] }}" bind:next="handleAfterAuthorized" custom-style="position: absolute;right: 42px;top: 17px;overflow: visible;width: 90px;height: 30px;" bizDataMap="{{ bizDataMap }}">
            <swiper next-margin="{{ 10 }}" bindtap="handleSwipeChange" bindchange="handleSwipeChange" style="height: var(--swiper-height);width: calc(100vw - 15px);position: absolute;right: -42px;top: -17px;" current="{{ current }}">
              <swiper-item wx:for="{{ cards }}" wx:key="unique">
                <card level-progress-list="{{levelProgressList}}" memberCenterLogoUrl="{{memberCenterLogoUrl}}" card="{{ item.card }}" disabled="{{ !item.isEnabled }}" now-level="{{ nowLevel }}" next-card="{{ nextCard }}" btn-show="{{ true }}" redirect="{{ (!showInfo && !hasCompleted) ? redirectUrl : toMissionCenter  }}" alias="{{ (!showInfo && !hasCompleted) ? groupAlias : null }}" has-mobile="{{ hasMobile }}" has-completed="{{ hasCompleted }}" require-mobile="{{ requireMobile }}" mobile-authorized="{{ mobileAuthorized }}" recruitParams="{{ recruitParams }}" is-consume="{{ isConsume }}" has-level="{{ hasLevel }}" is-third-party-center="{{ isThirdPartyCenter }}" mode="{{mode}}" card-text-source="{{ item.levelGapInfo }}" points-info="{{ pointsInfo }}" />
              </swiper-item>
            </swiper>
          </user-authorize>
          <swiper wx:else next-margin="{{ 10 }}" bindtap="handleSwipeChange" bindchange="handleSwipeChange" style="height: var(--swiper-height);" current="{{ current }}">
            <swiper-item wx:for="{{ cards }}" wx:key="unique">
              <card level-progress-list="{{levelProgressList}}" memberCenterLogoUrl="{{memberCenterLogoUrl}}" user-level="{{ userLevel }}" card="{{ item.card }}" disabled="{{ !item.isEnabled }}" now-level="{{ nowLevel }}" next-card="{{ nextCard }}" btn-show="{{ true }}" redirect="{{ (!showInfo && !hasCompleted) ? redirectUrl : toMissionCenter  }}" alias="{{ (!showInfo && !hasCompleted) ? groupAlias : null }}" has-mobile="{{ hasMobile }}" mobile-authorized="{{ mobileAuthorized }}" has-completed="{{ hasCompleted }}" bindhandleQRClick="handleQRClick" bindbindMobile="handleAllTriggerEvent" bindhandleRejoin="handleRejoin" require-mobile="{{ requireMobile }}" is-consume="{{ isConsume }}" is-full-level="{{ isFullLevel }}" has-level="{{ hasLevel }}" is-third-party-center="{{ isThirdPartyCenter }}" is-removed="{{ isRemoved }}" mode="{{mode}}" card-text-source="{{ item.levelGapInfo }}" points-info="{{ pointsInfo }}" />
            </swiper-item>
          </swiper> 
        </view>
        <!-- 权益列表 -->
        <simple-benefit-list page="会员中心" custom-name-color="{{ customNameColor }}" custom-bg="{{customBg}}" wx:if="{{memberLevelBenefit}}" now-level-alias="{{ nowLevelAlias }}" benefits-list="{{ nowBenefitList }}" kdt-id="{{ kdtId }}" max-row="{{ 1 }}" bindsubscribeCallback="handleSubscribeCallback" need-subscribe="{{ needSubscribe }}" />
        <van-notice-bar wx:if="{{ showBenefitsAlert }}" scrollable="{{ false }}" text="权益说明：以上权益限本店{{ memberName }}可用，您当前无法享受" />
        <block wx:if="{{ !isUnionCode }}">
          <view class="btm-ctr {{ hasContent ? 'has-ct' : '' }}">
            <view class="btm-content">
              <!-- 场景营销活动轮播 - 删除原生日礼组件 -->
              <scene-market-swiper page-query="{{ pageQuery }}" group-alias="{{ groupAlias }}" bind:bindMobile="handleAllTriggerEvent" />
              <!-- 会员加群 -->
              <live-code memberShopConfigs="{{memberShopConfigs}}" bind:showLiveCodeAddFansPop="showLiveCodeAddFansPop" />
              <!-- 会员礼包 -->
              <join-gift wx:if="{{showNewMemberGift}}" bindbindMobile="handleAllTriggerEvent" bindhideMemberGift="hideMemberGift" benefit-gift="{{ benefitGift }}" is-member="{{ hasLevel }}" show-register-btn="{{ showRegisterBtn }}" bizDataMap="{{ bizDataMap }}" mode="{{mode}}" needed-more-store-balance="{{ neededMoreStoreBalance }}" member-name="{{ memberName }}"/>
              <!-- 会员满赠礼活动 -->
              <member-pay-amount-gift />
              <!-- 会员专享券 -->
              <member-coupon is-member="{{ hasLevel }}" level-type="free" member-name="{{ memberName }}"/>
              <!-- 成长任务 -->
              <view wx:if="{{ !isThirdPartyCenter && showMission && mode === 1 }}">
                <view class="task-list">
                  <view class="task-label">
                    成长任务
                    <van-icon name="arrow" color="#979797" bindtap="jumpToMissionCenter" />
                  </view>
                  <mission-list hasMobile="{{ hasMobile }}" bindmissionsNumber="missionsNumber" bind:after-fetch-missions="handleAfterFetchMission" fetch-mission-finished="{{ fetchMissionFinished }}" bizDataMap="{{ bizDataMap }}" neededMoreStoreBalance="{{ neededMoreStoreBalance }}" />
                </view>
              </view>
              <height-calculator class='h-cal' bind:heightCalculate="handleHeightCalculate" />
            </view>
            <!-- 自定义装修组件 开始 -->
            <view style="background-color: #fff;margin-bottom: 12px;">
              <showcase-container />
            </view>
            <!-- 自定义装修组件 结束 -->
            <view class="g-rec-wrap">
              <goods-recommend wx:if="{{ isSupportGoodsRecommend }}" biz-name="levelcenter~rec" title-config="{{ goodsRecommendTitleConfig }}" custom-recommend-name="更多精选商品" goods-number="{{ 6 }}" />
            </view>
            <!-- 底部导航栏 高度占位，这代码不知道干嘛用的，先注释，等和开发确认决定是否删除 -->
            <!-- <view wx:if="{{showTabBar}}" class="pg-btm-h"></view> -->
            <view class="act-btns {{ isWeShop ? 'a-btn-ws' : ''}}" wx:if="{{ !isThirdPartyCenter || hasLevel  }}">
              <view class="rm-btn" wx:if="{{ !isThirdPartyCenter  }}" bindtap="linkToRule">
                查看{{ memberName }}规则
              </view>
              <text wx:if="{{ !isThirdPartyCenter && hasLevel  }}" class='split'>|</text>
              <view wx:if="{{ hasLevel }}" class="rm-btn" bindtap="removeLevel">退出当前等级</view>
            </view>
            <!-- 立即入会按钮 -->
            <register-button wx:if="{{ showRegisterBtn }}" bindbindMobile="handleAllTriggerEvent" bindreloadData="reloadDataWithCtx" isWeixinCardEnabled="{{ isWeixinCardEnabled }}" pageQuery="{{ pageQuery }}" showBottomPadding="{{ showTabBar }}" bizDataMap="{{ bizDataMap }}" neededMoreStoreBalance="{{ neededMoreStoreBalance }}" />
          </view>
        </block>
        <!-- 多码合一继续按钮 -->
        <qrcode-process-button wx:else query="{{ pageQuery }}" kdtId="{{ kdtId }}" />
      </view>
      <!-- 会员码组件 -->
      <member-code-popup />
      <level-guide wx:if="{{ !!groupAlias && !isUnionCode }}" be-member-tip="{{beMemberTip}}" memberShopConfigs="{{memberShopConfigs}}" bind:showLiveCodeAddFansPop="showLiveCodeAddFansPop" group-alias="{{ groupAlias }}" kdt-id="{{ kdtId }}" bindbindMobile="handleAllTriggerEvent" has-authorized="{{ mobileAuthorized }}" scene="{{ guideScene }}" page-query="{{ pageQuery }}" has-level="{{ hasLevel }}" removed="{{ isRemoved }}" biz-data-map="{{ bizDataMap }}" bindsubscribeCallback="handleSubscribeCallback" crmFromScenePlanId="{{ crmFromScenePlanId }}" hasMobile="{{ hasMobile }}" nowLevel="{{nowLevel}}" now-benefit-list="{{ nowBenefitList }}" joinGapInfo="{{joinGapInfo}}" member-name="{{ memberName }}" is-we-shop="{{ isWeShop }}" show-register-btn="{{ showRegisterBtn }}" />
    </view>
    <!-- 订阅小程序消息 -->
    <block wx:if="{{ !isUnionCode }}">
      <!-- 原"订阅更多特权通知"按钮，已经被下掉，全部换成"有活动时通知我"，统一疲劳度管理 -->
      <activity-subscribe-button wx:if="{{ needSubscribe }}" has-tabbar="{{ showTabBar }}" has-button="{{ !hasLevel && !hasCompleted }}" bindsubscribe="handleSubscribe" />
      <subscribe-fail-popup show="{{ showSubscribeFail }}" openImageSrc="https://img.yzcdn.cn/public_files/2019/12/23/7b987495fc6bbd066262153577881592.png" bind:close="closeSubscribeFailPopup"></subscribe-fail-popup>
      <custom-tab-bar wx:if="{{ showTabBar }}" forceShow></custom-tab-bar>
    </block>
  </block>
</view>
<protocol z-index="10001" noAutoAuth="false" bind:onProtocolInitListen="handleProtocol" />
<user-authorize-popup />