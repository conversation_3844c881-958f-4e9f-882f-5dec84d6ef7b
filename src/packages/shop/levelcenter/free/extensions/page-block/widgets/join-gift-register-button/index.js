import { RantaWidget } from 'shared/common/base/ranta/widget';

const app = getApp();

RantaWidget({
  properties: {
    bizDataMap: {
      type: Object,
      value: {},
    },
    neededMoreStoreBalance: <PERSON>olean,
    memberName: {
      type: String,
      value: '会员',
    },
  },
  methods: {
    btnConfirm() {
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'click_fill', // 事件标识
          en: '入会按钮点击', // 事件名称
        });
      this.triggerEvent('confirm');
    },
  },
  attached() {
    app.logger &&
      app.logger.log({
        et: 'view', // 事件类型
        ei: 'view_fill_button', // 事件标识
        en: '入会按钮曝光', // 事件名称
      });
  },
});
