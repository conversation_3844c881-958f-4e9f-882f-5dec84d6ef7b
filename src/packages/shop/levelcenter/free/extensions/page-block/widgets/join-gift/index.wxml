<view class="benefit-gift" wx:if="{{ visible }}">
  <view class="title">
    <view>{{ isMember ? "新" + memberName + "会员礼包" : "新" + memberName + "礼包，入会可领" }}</view>
    <van-icon wx:if="{{ isMember }}" name="cross" bindtap="closeMemberGift" class="close" />
  </view>
  <view class="scroll-list">
    <present wx:for="{{ presentList }}" wx:key="presentName" wx:for-item="present" present="{{present}}" />
    <coupon wx:for="{{ couponList }}" wx:key="preKey" wx:for-item="coupon" coupon="{{coupon}}" />
    <point wx:if="{{ point.value > 0 }}" point="{{ point }}" />
  </view>
  <join-gift-register-button wx:if="{{ showRegisterBtn }}" bind:confirm="btnConfirm" bizDataMap="{{ bizDataMap }}" neededMoreStoreBalance="{{ neededMoreStoreBalance }}" member-name="{{ memberName }}"></join-gift-register-button>
  <!-- 历史逻辑 成长模式特有  -->
  <view wx:if="{{ !showRegisterBtn && !isMember && mode===1 }}" bindtap="getMoreGrowth" class="bottom-btn">
    获取更多成长值
  </view>
</view>