page {
  /* stylelint-disable-line */
  --border-radius: 12px;
  --bg-image: url('//img01.yzcdn.cn/upload_files/2023/09/18/FkkCnGcdoo8lTKARc-i8RGKTc9xw.png');
  --swiper-height: calc((100vw - 41px) / (343 / 180));
}

.s-border {
  width: 100%;
  height: 10px;
  background-color: #f7f8fa;
}

.ctr {
  background: url(//img01.yzcdn.cn/upload_files/2023/09/15/FhgkHp8cGWe7pyVI5RN-Zv_O9HUt.png)
    0 0 / cover no-repeat;

  .nav-bar-title {
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    box-sizing: border-box;
    font-size: 32rpx;
    font-weight: 500;
    position: relative;
  }

  .lc-cmp-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    height: 40px;
    background: #e8effa;
    font-size: 14px;
    color: #323233;
    line-height: 18px;
    background-color: #fefbf4;
  }

  .lc-cmp-info .text {
    display: flex;
    align-items: center;
    color: #9a640c;
  }

  .lc-lvl-tip {
    font-size: 14px;
    color: #9d6200;
  }
}
.uc-ctr {
  background-color: #f7f8fa;
}

.sw-wrap {
  position: relative;
  margin-left: 15px;
  height: 180px;
}

.lvl-wrap {
  overflow: hidden;
  padding-top: 16px;
  position: relative;
  min-height: 150px;

  .task-list {
    margin-bottom: 12px;
    padding: 12px 12px 9px 6px;
    border-radius: var(--border-radius, 0);
    background-image: var(--bg-image, #fff);
    background-repeat: no-repeat;
    background-size: auto 90px;
    background-position: 0 0;
    background-color: #fff;
  }

  .task-label {
    color: #333;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    margin-left: 10px;
  }
}

.c-bg {
  position: absolute;
  top: 0;
  width: 100%;
  height: 80px;
}

.sw-wrapper {
  margin-left: 16px;
  position: relative;
  z-index: 1;
  min-height: 150px;
}

.sw-height {
  height: var(--swiper-height);
}

.sc-comp {
  margin-top: 26px;
}

.pg-btm-h {
  height: 45px;
  background-color: #f9f9f9;
}

.act-btns {
  display: flex;
  justify-content: center;
  color: #666;
  font-size: 12px;
  text-align: center;
  margin-top: 16px;
  padding-bottom: 7px;
}

.a-btn-ws {
  padding-bottom: calc(58px + env(safe-area-inset-bottom));
}

.split {
  margin: 0 12px;
  color: #d9d9d9;
  display: flex;
  align-items: center;
}

.btm-ctr {
  background-color: #f7f7f7;
  overflow: auto;
  margin-top: 4px;
  padding-top: 0;

  .btm-content {
    position: relative;
    padding: 0 12px 0;
    border-radius: 20px 20px 0 0;
  }

  // 如果自定义装修组件上方有其他组件，外层包裹容器bottom-container就展示成圆角。否则默认直角。
  &.has-ct {
    border-radius: 20px 20px 0 0;
    padding-top: 12px;
  }

  .h-cal {
    z-index: -2;
  }
}

.g-rec-wrap {
  background-color: #fff;
}

.pg-c-tb {
  padding-bottom: 50px;
}

.pg-c-tb.iPhone-X {
  padding-bottom: calc(84px + env(safe-area-inset-bottom));
}

.pg-c-tb.pg-c-fb {
  padding-bottom: 94px;
}

.pg-c-tb.pg-c-fb.iPhone-X {
  padding-bottom: calc(94px + env(safe-area-inset-bottom));
}

.pg-c-fb {
  padding-bottom: 44px;
}

.pg-c-fb.iPhone-X {
  padding-bottom: calc(44px + env(safe-area-inset-bottom));
}

.footer .copyright {
  background-color: #f7f7f7;
}

.rm-btn {
  width: 300rpx;
  margin: 31px auto;
  height: 36px;
  line-height: 36px;
  box-sizing: border-box;
  border: none !important;
  color: #969799;
  font-size: 14px;
  text-align: center;
}
