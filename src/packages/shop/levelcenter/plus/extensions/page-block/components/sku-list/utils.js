import { getLevelGoods } from '../../utils/api';
import { handleGoodInfo, calcPrice } from '../../utils/handle';
import { SET_MAP, UNIT_CYCLE, LevelGoodsSkuType } from '../../constants';
import format from '@youzan/utils/money/format';

const calcSkuList = (data) => {
  const { skuList = [] } = data;
  if (!skuList.length) return {};
  let lowestPrice = Number.POSITIVE_INFINITY;
  let lowestMonthPrice = Number.POSITIVE_INFINITY;
  let lowestType = 0;
  let isExperienceRenew = false;
  let isStartCheapest = false;
  const list = [];
  let smallestCycle = UNIT_CYCLE.QUARTER;

  for (let i = skuList.length - 1; i > -1; i--) {
    const items = skuList[i];
    const item = items;
    const { goodsSkuType, lifeTime: { termMonths } = {} } = items;
    item.goodsId = data.goodsId;
    item.skuId = item.goodsSkuId;
    smallestCycle = termMonths < smallestCycle ? termMonths : smallestCycle;
    // 过滤体验会员
    if (goodsSkuType === LevelGoodsSkuType.ExperienceRenewal) continue;
    item.handlePrice = calcPrice(items);
    const featureInfo = handleGoodInfo(item);
    item.setName = featureInfo.name;
    // 记录原等级商品的周期值
    item.originCycle = featureInfo.originCycle;
    // 商品的月平均价格
    const price = featureInfo.lowestMonthPrice;
    item.monthPirce = +format(price, false, false);
    // 是否是续费优惠
    isExperienceRenew =
      isExperienceRenew || item.handlePrice.isExperienceRenew || false;
    const isContinuous = goodsSkuType === LevelGoodsSkuType.Renewal;
    // 连续商品优先展示
    isContinuous ? list.unshift(item) : list.push(item);
    // 记录最低价格信息
    if (
      lowestMonthPrice > price ||
      (lowestMonthPrice === price && !isStartCheapest)
    ) {
      lowestMonthPrice = price;
      isStartCheapest = featureInfo.isCheapestIsStart;
      lowestPrice = featureInfo.lowestPrice;
      lowestType = featureInfo.originCycle;
    }
  }
  const originType = lowestType;
  // 当价格format后约等于0，用原有周期值
  const isTooSmall = +format(lowestMonthPrice, false, false) === 0;
  if (isTooSmall) {
    lowestMonthPrice = lowestPrice;
  } else if (smallestCycle === UNIT_CYCLE.MONTH) {
    lowestPrice = lowestMonthPrice;
    lowestType = SET_MAP[smallestCycle];
  }

  // 最低价展示为套餐价，最低月均价做比较
  return {
    lowestPrice: +format(lowestPrice, false, false),
    lowestMonthPrice: +format(lowestMonthPrice, false, false),
    lowestType,
    originType,
    isStartCheapest,
    list,
    isExperienceRenew,
    hasMonthNotCheapest:
      smallestCycle === UNIT_CYCLE.MONTH && originType !== lowestType,
  };
};
// 处理等级的sku购买信息
export const handleSkuListWithCalc = (data) => {
  const {
    list,
    lowestPrice,
    lowestType,
    lowestMonthPrice,
    hasMonthNotCheapest,
  } = calcSkuList(data);
  return {
    skuList: list,
    bestPrice: +format(lowestPrice, false, false),
    bestType: lowestType,
    lowestMonthPrice,
    hasMonthNotCheapest,
  };
};

export const fetchLevelGoods = (() => {
  const levelGoodsMap = {};

  return (levelAliases, curLevelAlias, handleFunction) => {
    // 剔除当前正在查看的等级
    const curLevelIdx = levelAliases.indexOf(curLevelAlias);
    const aliases = [...levelAliases];
    if (curLevelIdx) {
      aliases.splice(curLevelIdx, 1);
    }

    // 提前加载其他等级数据
    aliases.map((alias) => {
      // 不是当前正在查看的数据，且已有缓存，不加载数据
      if (levelGoodsMap[alias]) {
        return;
      }
      // 加载等级数据并缓存
      return getLevelGoods({ levelAlias: alias })
        .then((data) => {
          levelGoodsMap[alias] = handleFunction(data);
        })
        .catch((err) => {
          // eslint-disable-next-line no-console
          console.error(err);
        });
    });

    // 返回缓存数据，和 promise
    return [
      levelGoodsMap[curLevelAlias],
      getLevelGoods({ levelAlias: curLevelAlias }).then((data) => {
        const handledData = handleFunction(data);
        levelGoodsMap[curLevelAlias] = handledData;
        return handledData;
      }),
    ];
  };
})();
