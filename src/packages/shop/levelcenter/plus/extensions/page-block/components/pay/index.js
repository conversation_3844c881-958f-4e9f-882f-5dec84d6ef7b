// TODO: for in lint error
/* eslint-disable no-unused-vars */
/* eslint-disable guard-for-in */

import Toast from '@vant/weapp/dist/toast/toast';
import WscComponent from 'pages/common/wsc-component/index';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import get from '@youzan/weapp-utils/lib/get';
import { structureGuideFromParams } from 'utils/guide-jump';
import State from './store';
import * as API from './api';

const app = getApp();

function jsonToURLEncoded(element, key, list) {
  const nowlist = list || [];
  if (typeof element == 'object') {
    for (const idx in element) {
      jsonToURLEncoded(
        element[idx],
        key ? key + '[' + idx + ']' : idx,
        nowlist
      );
    }
  } else {
    nowlist.push(key + '=' + encodeURIComponent(element));
  }
  return nowlist.join('&');
}

WscComponent({
  properties: {
    experienceSkuInfo: {
      type: Object,
      value: {},
    },
    cycleSkuInfo: {
      type: Object,
      value: {},
    },
  },
  methods: {
    async initCashier({ detail }) {
      const { default: PayService } = await import('@youzan/zan-pay-weapp');
      this.zanPay = new PayService({
        toast: wx.showToast,
        clear: wx.hideToast,
        request: app.request,
        biz: 'member_level',
        account: app.getMobile(),
        quickMode: true,
      });
      const crypto = await import('@youzan/crypto');
      this.zanPay.init(detail, crypto);
    },

    onPaySuccess() {
      wx.showToast({
        title: '购买成功',
        icon: 'none',
      });
      setTimeout(() => {
        wx.reLaunch({
          url: '/packages/shop/levelcenter/plus/index',
        });
      }, 1000);
    },

    onCashierNavi({ detail: destination }) {
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${destination.url}&title=${destination.title}`,
      });
      // 跳转到 webview page
      // destination 包含 url 和 title 两个 key
      // 自定义
    },

    onCashierClose() {
      // 每次收银台弹层 close 时触发
      // 可选
    },

    // 创建订单 拿商品信息和用户信息完成 cache 操作
    toCreateOrder(type, recruitParams) {
      // 体验会员下单sku
      const nowExperienceSku = this.properties.experienceSkuInfo;
      // 包月会员下单sku
      const nowCycleSku = this.properties.cycleSkuInfo;
      const params = {
        ...State,
      };
      const nowSku = type === 'experience' ? nowExperienceSku : nowCycleSku;
      const skuInfo = {
        activityType: 202, // 等级商品类型
        activityId: 0,
        goodsId: nowSku.goodsId,
        kdtId: app.getKdtId(),
        num: 1,
        price: nowSku.price,
        skuId: nowSku.skuId,
        // 多网点下单需要传类似网点id的东西，其他场景传 0
        storeId: 0,
      };
      params.seller.kdtId = app.getKdtId();
      params.items = [skuInfo];
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'click_buy_plus_member_card', // 事件标识
          en: '点击购买付费会员卡', // 事件名称
          pt: 'membercenterpay', // 页面类型
          params: {
            type,
          }, // 事件参数
        });

      // 连续下单额外参数
      const isContinousOrder = false;
      params.config.isContinousOrder = isContinousOrder;
      const scene = 'VALUE_COMMON';

      // 数据接口地址：http://zanapi.qima-inc.com/site/service/view/212661
      const queryString = jsonToURLEncoded(params);
      API.postCache(queryString)
        .then((data) => {
          this.toBill(params, data, scene, recruitParams);
        })
        .catch((err) => {
          this.show = false;
          Toast.fail(err.msg || '购买失败，请重试！');
        });
    },

    // 下单
    toBill(orderData, cache, scene, recruitParams) {
      orderData.source.bookKey = cache.bookKey;
      if (!isEmpty(recruitParams)) {
        const kdtId = app.getKdtId();
        let rootKdtId = kdtId;
        const shopInfo = app.getShopInfoSync();
        if (shopInfo.chainStoreInfo && shopInfo.chainStoreInfo.rootKdtId) {
          rootKdtId = shopInfo.chainStoreInfo.rootKdtId;
        }
        const guideFromParams = structureGuideFromParams(recruitParams);
        const fromParams = `head_kdt_id~${rootKdtId}!online_kdt_id~${kdtId}${guideFromParams}!source_kdt_id~${recruitParams.NEW_SALES_KDTID}`;
        const itemSource = {
          bizTracePointExt: JSON.stringify({
            from_params: fromParams,
            recurit_src: recruitParams.RECRUIT_SRC,
            recurit_entrance: recruitParams.RECRUIT_ENTRANCE,
            recurit_member_type: recruitParams.RECRUIT_MEMBER_TYPE,
          }),
          ...get(orderData, 'items[0]', {}),
        };
        orderData.source.itemSources = [itemSource];
      }
      // 透传scene，连续场景仅支持银行卡
      API.doBill(jsonToURLEncoded(orderData))
        .then((data) => {
          const { paymentPreparation, prePaymentPreparation, orderNo } = data;
          const payInfo = {
            prepay: true,
            prepaySuccess: true,
            payParams: paymentPreparation,
            prePayParams: { ...prePaymentPreparation, scene },
            orderNo,
          };

          // 零元单直接付费成功
          // 无须支付，直接跳转到回调地址
          if (data.zeroOrder || data.showPayResult) {
            wx.showToast({
              title: '购买成功',
              icon: 'none',
            });
            setTimeout(() => {
              this.onPaySuccess();
            }, 1000);
          } else {
            this.startPay(payInfo);
          }
        })
        .catch((err) => {
          Toast.fail(err.msg || '购买失败，请重试！');
          this.show = false;
        });
    },

    startPay(pay) {
      const { prePayParams, orderNo, payParams } = pay;
      const { cashierSalt, cashierSign, partnerId, prepayId, scene } =
        prePayParams;

      const tokenData = app.getToken() || {};
      // 在小程序进行支付时，需要额外传入参数 appId（有赞小程序需要）
      let outBizCtx;
      try {
        outBizCtx = JSON.parse(payParams.bizExt);
        outBizCtx.appId = app.getAppId();
      } catch (e) {
        outBizCtx = {};
      }

      this.zanPay.startPay({
        cashierSalt,
        cashierSign,
        partnerId,
        prepayId,
        // 以上四个会跟随下单返回  prepayData 之类的

        // uic 数据
        wxSubOpenId: tokenData.openId,
        // 收银台后端约定场景值  可以传 'COMMON'
        scene,
        // 一般会跟随下单返回, 或者自定义
        tradeBizExt: outBizCtx,

        // 订单号
        orderNo,

        // 银行卡支付完成后的跳转 url
        // 可以是 webview 也可以是 小程序 path
        afterPayUrl: { weappUrl: '/package/levelcenter/plus/index' },
      });
    },
  },
});
