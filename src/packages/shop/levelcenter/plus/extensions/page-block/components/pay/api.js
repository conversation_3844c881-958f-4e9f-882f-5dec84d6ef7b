const app = getApp();
// 获取跳转到下单页需要的参数
export const doBill = (data) => {
  return app
    .request({
      origin: 'cashier',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      path: '/pay/wscuser/level/pay/bill.json',
      method: 'POST',
      data
    });
};

// 获取跳转到下单页需要的参数
export const postCache = (data) => {
  return app
    .request({
      origin: 'cashier',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      path: '/pay/wscuser/level/pay/cache.json',
      method: 'POST',
      data
    });
};

