import WscComponent from 'pages/common/wsc-component/index';
import { getLevelBgInfo } from '@youzan/scrm-common';

import { LevelType } from '../../../../../level-common/utils/constant';
import get from '@youzan/weapp-utils/lib/get';
import { moment as formatDate } from 'utils/time';

WscComponent({
  properties: {
    height: Number,
    card: Object,
    disabled: Boolean,
    redirect: String,
    userLevel: {
      type: Object,
      value: {},
    },
    benefitsList: {
      type: Array,
      observer(nv, ov) {
        if (nv.length !== ov.length) {
          const receiveBenefit = this.getReceiveBenefit();
          this.setYZData({
            receiveBenefit,
          });
        }
      },
    },
    bestPirce: String,
    bestType: String,
    hasAuthorized: {
      type: Boolean,
    },
    memberCenterLogoUrl: String,
  },
  data: {
    levelStatus: '',
    bgStyle: '',
    maskStyle: '',
    receiveBenefit: '',
    vaildTime: '',
  },
  methods: {
    getNeedMoreValue() {
      const { nowLevel, card } = this.properties;
      return nowLevel.levelValue < card.levelValue;
    },

    getLevelStatus() {
      const { userLevel, card } = this.properties;
      const levelAlias = get(userLevel, 'level.levelAlias', '');
      const isNowLevel = levelAlias === card.alias;
      const isExperienceLevel = get(userLevel, 'isExperienceLevel', false);
      const isExpired = get(userLevel, 'isExpired', false);
      // 当前等级且过期
      if (isExpired && isNowLevel) {
        return 'expired';
      }
      // 当前等级且体验
      if (isExperienceLevel && isNowLevel) {
        return 'trial';
      }
      // 普通等级
      if (isNowLevel) {
        return 'member';
      }
      return 'none';
    },

    getLevelStyle() {
      const { height } = this.properties;
      const style = `height: ${height}px; background-size: cover; background-position: center;`;
      const { card } = this.properties;
      const levelStyle = getLevelBgInfo({
        colorCode: card.color,
        cover: card.image,
        levelType: LevelType.PAY,
      });
      return {
        bgStyle: `${style}background:${levelStyle.backgroundStyle}`,
        maskStyle: `background: ${levelStyle.maskBgStyle}`,
      };
    },

    getReceiveBenefit() {
      const { benefitsList = [] } = this.properties;
      const benefitsNum = benefitsList.length;
      return {
        trial: '一个用户仅拥有一次付费会员体验资格',
        member: '',
        expired: benefitsNum > 0 ? `开通立享 ${benefitsNum} 项特权` : '',
        none: benefitsNum > 0 ? `开通立享 ${benefitsNum} 项特权` : '',
      };
    },

    getVaildTime() {
      const { userLevel } = this.properties;
      const termEndAt = get(userLevel, 'termEndAt', '');
      const time = formatDate(termEndAt, 'YYYY-MM-DD');
      return {
        trial: `将于${time}到期`,
        member: `有效期至${time}`,
        expired: '已过期',
        none: '',
      };
    },

    updateLevelData() {
      const levelStatus = this.getLevelStatus();
      const levelStyle = this.getLevelStyle();
      const { bgStyle } = levelStyle;
      const { maskStyle } = levelStyle;
      const receiveBenefit = this.getReceiveBenefit();
      const vaildTime = this.getVaildTime();
      const { card } = this.properties;
      const isSingleRule = card.levelGoods.skuList.length <= 1;
      this.setYZData({
        levelStatus,
        bgStyle,
        maskStyle,
        receiveBenefit,
        vaildTime,
        isSingleRule,
      });
    },
  },
  attached() {
    this.updateLevelData();
  },
});
