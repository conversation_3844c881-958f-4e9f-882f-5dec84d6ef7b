export const UNIT_CYCLE = {
  MONTH: 1,
  QUARTER: 3,
  MONTH_6: 6,
  YEAR: 12,
  YEAR_2: 24,
  YEAR_3: 36,
  YEAR_5: 60,
};

export const UNIT_CYCLE_TEXT = {
  MONTH: '月',
  QUARTER: '季',
  MONTH_6: '半年',
  YEAR: '年',
  YEAR_2: '2年',
  YEAR_3: '3年',
  YEAR_5: '5年',
};

export const SET_MAP = {
  [UNIT_CYCLE.MONTH]: UNIT_CYCLE_TEXT.MONTH,
  [UNIT_CYCLE.QUARTER]: UNIT_CYCLE_TEXT.QUARTER,
  [UNIT_CYCLE.MONTH_6]: UNIT_CYCLE_TEXT.MONTH_6,
  [UNIT_CYCLE.YEAR]: UNIT_CYCLE_TEXT.YEAR,
  [UNIT_CYCLE.YEAR_2]: UNIT_CYCLE_TEXT.YEAR_2,
  [UNIT_CYCLE.YEAR_3]: UNIT_CYCLE_TEXT.YEAR_3,
  [UNIT_CYCLE.YEAR_5]: UNIT_CYCLE_TEXT.YEAR_5,
};

// sku 类型
export const LevelGoodsSkuType = {
  Pay: 1, // 付费规则
  Renewal: 2, // 自动续费规则
  ExperienceRenewal: 3, // 体验续费规则
};

// sku 优惠类型
export const PreferentialType = {
  Price: 1, // 指定价格
  Discount: 2, // 指定折扣
  None: 3, // 暂不优惠
};

// sku 优惠场景
export const PreferentialSceneType = {
  FirstRenew: 1, // 首开优惠场景
  ExperienceRenew: 2, // 体验续费优惠场景
};

export const sceneTypeMap = {
  1: 'startPrice', // 首开优惠
  2: 'consecutivePrice', // 连续优惠
};
