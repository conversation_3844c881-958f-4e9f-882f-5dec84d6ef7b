const app = getApp();

// 获取当前用户的等级
export const getPlusUserLevelInfo = data => {
  return app.request({
    origin: 'cashier',
    path: '/pay/wscuser/pluscenter/api/userLevelDetail.json',
    data
  });
};

// 获取该店铺的等级
export const getPlusLevelList = data => {
  return app.request({
    origin: 'cashier',
    path: '/pay/wscuser/pluscenter/api/getLevelListV2.json',
    data
  });
};

export const getExperienceHighestLevel = () => {
  return app.request({
    origin: 'cashier',
    path: '/pay/wscuser/pluscenter/api/getExperienceHighestLevel.json'
  });
};

export const checkUserLevelRegisterInfoComplete = (levelGroupAlias) => {
  return app.request({
    path: '/wscuser/level/api/checkUserLevelRegisterInfoComplete.json',
    data: {
      levelGroupAlias,
    },
  });
};

export const getExperienceLevelList = () => {
  return app.request({
    origin: 'cashier',
    path: '/pay/wscuser/pluscenter/api/getExperienceLevelList.json'
  });
};

export const getNowExperienceInfo = data => {
  return app.request({
    origin: 'cashier',
    path: '/pay/wscuser/pluscenter/api/getNowExperienceInfo.json',
    data
  });
};

export const getAgreement = () => {
  return app.request({
    origin: 'cashier',
    path: '/pay/wscuser/pluscenter/api/renew.json'
  });
};

export const getLevelGoods = ({ levelAlias }) => {
  return app.request({
    origin: 'cashier',
    path: '/pay/wscuser/pluscenter/api/getLevelGoods.json',
    data: {
      levelAlias
    }
  });
};
