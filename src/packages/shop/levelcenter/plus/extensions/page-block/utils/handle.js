import money from '@youzan/weapp-utils/lib/money';
import { getLevelGoods } from './api';
import {
  UNIT_CYCLE,
  LevelGoodsSkuType,
  PreferentialType,
  sceneTypeMap,
  SET_MAP,
} from '../constants';

// 格式化金额
export const formatDivMoney = (price, div) => {
  if (price === Number.POSITIVE_INFINITY) {
    return Number.POSITIVE_INFINITY;
  }
  return price / div;
};

// 按优惠类型计算不同的价格
export const calcPrice = (skuInfo) => {
  if (!skuInfo) return '';
  const res = {};
  const { levelSkuPreferentials = [], price } = skuInfo;
  res.consecutivePrice = money(price).toYuan();
  res.hasDiscount = false;
  res.price = money(price).toYuan();
  res.ExperienceRenew = false;
  if (!levelSkuPreferentials.length) {
    return res;
  }
  levelSkuPreferentials.forEach((item) => {
    switch (item.preferentialType) {
      // 价格优惠
      case PreferentialType.Price:
        res[sceneTypeMap[item.sceneType]] = money(item.price).toYuan();
        res.hasDiscount = true;
        break;
      // 折扣优惠
      case PreferentialType.Discount:
        res[sceneTypeMap[item.sceneType]] =
          money(price).toYuan() * money(item.discount).toYuan() ||
          money(price).toYuan();
        res.hasDiscount = true;
        res.ExperienceRenew = true;
        break;
      // 原价
      default:
        break;
    }
  });
  return res;
};

// 商品处理信息
export const handleGoodInfo = (skuInfo) => {
  if (!skuInfo) return '';
  const res = {};
  const {
    goodsSkuType,
    lifeTime: { termMonths } = {},
    handlePrice: {
      startPrice = Number.POSITIVE_INFINITY,
      consecutivePrice = Number.POSITIVE_INFINITY,
      price,
    } = {},
  } = skuInfo;
  // 商品名
  res.name =
    goodsSkuType === LevelGoodsSkuType.Renewal
      ? `连续包${(termMonths > 12 ? '/' : '') + SET_MAP[termMonths]}`
      : termMonths > 12
      ? `${termMonths / 12}年`
      : `${termMonths}个月`;
  res.originCycle = SET_MAP[termMonths];
  // 取最小的每月价格
  res.lowestPrice = [price, startPrice, consecutivePrice].reduce(
    (pre, curr) => {
      return formatDivMoney(curr, termMonths) < formatDivMoney(pre, termMonths)
        ? curr
        : pre;
    },
    Number.POSITIVE_INFINITY
  );
  res.isCheapestIsStart =
    Math.min(price, startPrice, consecutivePrice) === Number(startPrice);
  res.lowestMonthPrice = formatDivMoney(res.lowestPrice, termMonths);
  return res;
};

// 处理sku，取等级卡片的最优价格sku信息
export const getCheapestSku = (skuList) => {
  let lowestPrice = Number.POSITIVE_INFINITY;
  let lowestMonthPrice = Number.POSITIVE_INFINITY;
  let lowestType = 0;
  let ExperienceRenew = false;
  let isStartCheapest = false;
  let cheapestCycle = UNIT_CYCLE.YEAR + 1;
  let smallestCycle = UNIT_CYCLE.QUARTER;
  skuList.forEach((originItem) => {
    const item = originItem;
    const { goodsSkuType, lifeTime: { termMonths } = {} } = originItem;
    // 过滤体验会员
    if (goodsSkuType === LevelGoodsSkuType.ExperienceRenewal) return;
    smallestCycle = termMonths < smallestCycle ? termMonths : smallestCycle;
    item.handlePrice = calcPrice(originItem);
    const featureInfo = handleGoodInfo(item);
    // 商品的月平均价格
    const price = featureInfo.lowestMonthPrice;
    ExperienceRenew =
      ExperienceRenew || item.handlePrice.ExperienceRenew || false;
    if (
      lowestMonthPrice > price ||
      (lowestMonthPrice === price &&
        !isStartCheapest &&
        cheapestCycle > termMonths)
    ) {
      lowestMonthPrice = price;
      isStartCheapest = featureInfo.isCheapestIsStart;
      lowestPrice = featureInfo.lowestPrice;
      lowestType = featureInfo.originCycle;
      cheapestCycle = termMonths;
    }
  });
  // 当价格format后约等于0，用原有周期值
  const originType = lowestType;
  const isTooSmall = Number(lowestMonthPrice).toFixed(2) == 0;
  if (isTooSmall) {
    lowestMonthPrice = lowestPrice;
  } else if (smallestCycle === UNIT_CYCLE.MONTH) {
    lowestPrice = lowestMonthPrice;
    lowestType = SET_MAP[smallestCycle];
  }

  return {
    bestPrice: Number(lowestPrice).toFixed(2),
    ExperienceRenew,
    bestType: lowestType,
    originType,
    monthPirce: Number(lowestMonthPrice).toFixed(2),
    isStartCheapest,
  };
};

/**
 * 获取等级商品数据，并缓存，优化交互体验
 * @param levelAlias 等级 Alias 列表，用于提前加载前后两个等级的数据
 * @param curLevelAlias 当前正在查看的等级
 */
export const fetchLevelGoods = (() => {
  const levelGoodsMap = {};

  return (levelAliases, curLevelAlias) => {
    // 剔除当前正在查看的等级
    const curLevelIdx = levelAliases.indexOf(curLevelAlias);
    const aliases = [...levelAliases];
    if (curLevelIdx) {
      aliases.splice(curLevelIdx, 1);
    }

    // 提前加载其他等级数据
    aliases.forEach((alias) => {
      // 不是当前正在查看的数据，且已有缓存，不加载数据
      if (levelGoodsMap[alias]) {
        return;
      }
      // 加载等级数据并缓存
      getLevelGoods({ levelAlias: alias })
        .then((data) => {
          levelGoodsMap[alias] = data;
        })
        .catch((err) => {
          // eslint-disable-next-line no-console
          console.error(err);
        });
    });

    // 返回缓存数据，和 promise
    return [
      levelGoodsMap[curLevelAlias],
      getLevelGoods({ levelAlias: curLevelAlias }).then((data) => {
        levelGoodsMap[curLevelAlias] = data;
        return data;
      }),
    ];
  };
})();
