<block wx:if="{{ isFeatureMemberCenter }}">
		<view class="feature-page__top-hook" />
		<showcase-container />
		<custom-tab-bar wx:if="{{ showTabBar }}" forceShow />
</block>
<block wx:else>
		<view class="container {{ showcaseClass }}">
				<!-- 用户信息头部 -->
				<personal-info-header user-level="{{ userLevel }}" link-text="{{ hasAgreement ? '自动续费管理' : '' }}" desc-icon="{{ true }}" desc-link="{{ freeCenterLink }}" link="{{ renewLink }}" link-query="{{ { kdt_id: kdtId, alias: nowLevelAlias } }}" bindhasAuth="handleAllTriggerEvent" bizDataMap="{{ bizDataMap }}" />
				<view class="levels-wrapper">
						<view class="card-bg">
								<view class="bg-img" />
						</view>
						<!-- 等级滚动 -->
						<view class="swiper-wrapper">
								<swiper next-margin="{{ 10 }}" bindtap="handleSwipeChange" bindchange="handleSwipeChange" style="height: {{ height + 'px' }}" current="{{ current }}">
										<swiper-item wx:for="{{ cards }}" wx:key="unique">
												<card card="{{ item.card }}" disabled="{{ !item.isEnabled }}" user-level="{{ userLevel }}" height="{{ height }}" btn-show="{{ !isContinous }}" redirect="{{ url }}" best-pirce="{{ bestPrice }}" best-type="{{ bestType }}" benefits-list="{{ nowBenefitList }}" has-authorized="{{ true }}" memberCenterLogoUrl="{{memberCenterLogoUrl}}" />
										</swiper-item>
								</swiper>
						</view>
						<!-- 权益列表 -->
						<simple-benefit-list now-level-alias="{{ nowLevelAlias }}" benefits-list="{{ nowBenefitList }}" kdt-id="{{ kdtId }}" max-row="{{ 1 }}" />
						<view class="split-border"></view>
						<!-- 生日礼提示 -->
						<birthday-tip wx:if="{{ userLevel.level }}" birthday-pos-index="{{ birthdayPosIndex }}" user-level="{{ userLevel }}" />
						<sku-list now-level-value="{{ nowLevelValue }}" now-level-alias="{{ nowLevelAlias }}" user-level="{{ userLevel }}" bind:onSkuChange="handleSkuChange" />
						<!-- 会员专享券 -->
						<view class="split-border"></view>
						<member-coupon is-member="{{ hasLevel }}" level-type="pay" level-alias="{{ nowAlias }}" />
				</view>
				<showcase-container />
				<goods-recommend wx:if="{{ isSupportGoodsRecommend }}" biz-name="levelcenter~rec" title-config="{{ goodsRecommendTitleConfig }}" custom-recommend-name="更多商品推荐" goods-number="{{ 6 }}" />
				<pay-button wx:if="{{ !isContinous }}" show-tab-bar="{{ showTabBar }}" btn-text="{{ btnText }}" current-cycle-sku="{{ currentCycleSku }}" sku-info="{{ nowExperienceSku }}" has-alias="{{ hasAlias }}" has-mobile="{{ hasMobile }}" confirom-funciton="{{ beforePay }}" link-to="{{ url }}" now-level-alias="{{ nowLevelAlias }}" user-level="{{ userLevel }}" experience-renew="{{ experienceRenew }}" bind:copy="handleAllTriggerEvent" bind:pay="handleAllTriggerEvent" bind:experience="handleAllTriggerEvent" bind:renew="handleAllTriggerEvent" bindbindMobile="handleAllTriggerEvent" bizDataMap="{{ bizDataMap }}" />
				<pay-view id="payViewRef" experience-sku-info="{{ nowExperienceSku }}" cycle-sku-info="{{ currentCycleSku }}" />
				<!-- 体验会员提示弹窗 -->
				<experience-dialog value="{{ bindDialogShow }}" sku-info="{{ nowExperienceSku }}" link-to="{{ url }}" has-mobile="{{ hasMobile }}" bindexperience="handleAllTriggerEvent" bindbindMobile="handleAllTriggerEvent" bizDataMap="{{ bizDataMap }}" />
				<!-- 小程序消息订阅组件 -->
				<subscirbe-msg has-tabbar="{{ showTabBar }}" has-button="{{ !isContinous }}" show-pay-plus-extra-cls="{{ true }}" />
				<!-- 会员协议变更组件 -->
				<protocol-guide wx:if="{{ hasLevel }}" kdt-id="{{ kdtId }}" bindnext="finish"></protocol-guide>
				<custom-tab-bar wx:if="{{ showTabBar }}" forceShow></custom-tab-bar>
				<view wx:if="{{ showTabBar }}" class="page-bottom-height"></view>
		</view>
</block>
<van-dialog id="van-dialog" />
<account-wx-login id="account-wx-login" />
<van-toast id="van-toast" />

<inject-protocol noAutoAuth />
