import openWebView from 'utils/open-web-view';
import money from '@youzan/weapp-utils/lib/money';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import {
  checkOnlineBranchStore,
  checkOfflineBranchStore,
  checkPartnerStore,
} from '@youzan/utils-shop';
import {
  levelAdapter,
  getBirthdayIndex,
} from 'packages/shop/levelcenter/level-common/utils/adapter';
import {
  LevelType,
  goodsRecommendTitleConfig,
} from 'packages/shop/levelcenter/level-common/utils/constant';
import {
  fetchComponentsOfType,
  checkIsSupportGoodsRecommend,
  getSlInfo,
} from 'packages/shop/levelcenter/level-common/utils/api';
import {
  getRecruitMemberParams,
  tryToBindShoppingGuideRelation,
} from 'packages/shop/levelcenter/level-common/utils/util';
import { getGuideBizDataMap } from 'utils/guide';
import {
  addParamPreventCouponLeak,
  FeaturePageType,
} from '@/utils/prevent-coupon-leak';
import {
  guideJumpDialog,
  GUIDE_SUCCESS_SIGN,
  GUIDE_DIALOG_TYPE,
} from 'utils/guide-jump';
import * as API from '../../utils/api';
import { getCheapestSku, fetchLevelGoods } from '../../utils/handle';
import { LevelGoodsSkuType } from '../../constants';
import { init, bindSalesman } from '@youzan/salesman-share';
import { newRequest } from 'shared/utils/salesman-share';
import args from '@youzan/weapp-utils/lib/args';
import pick from '@youzan/weapp-utils/lib/pick';
import get from '@youzan/weapp-utils/lib/get';
import { mapData, mapEvent } from '@youzan/ranta-helper-tee';
import { RantaWidget } from 'shared/common/base/ranta/widget';
import pageComponentBehavior from '@/custom-tab-bar-v2/native-helper/page-component-behavior';

const app = getApp();

RantaWidget({
  behaviors: [pageComponentBehavior],
  data: {
    kdtId: app.getKdtId(),
    loading: false,
    width: null,
    height: null,
    current: 0,

    userLevel: {},
    toRegister: false,
    hasAuthorized: false, // 是否授权
    hasMobile: false,
    cards: [], // 等级列表
    indexMap: {}, // 等级alis和index对应
    nowBenefitList: [], // 滑动等级权益
    nowLevelAlias: '', // 滑动等级alias
    nowLevelValue: 0,
    url: '',
    bestPrice: '',
    monthPirce: '',
    bestType: '',
    originType: '',
    hasLevel: false,
    hasAlias: false, // 是否是会员
    isContinous: false,
    hasAgreement: false,
    experienceMap: {},
    bindDialogShow: false,
    nowExperienceSku: {},
    nowAlias: '',
    nowName: '',

    btnText: '',
    renewLink: '/wscuser/pluscenter/renew',
    freeCenterLink: '/packages/levelcenter/free/index',
    isSupportGoodsRecommend: false,
    goodsRecommendTitleConfig,
    birthdayPosIndex: 0, // 生日权益在对应卡片中的位置
    bizDataMap: {}, // 携带给授权组件的业务自定义信息
    currentCycleSku: {},
    memberCenterLogoUrl: '',
    showTabBar: false,
  },

  ready() {
    app.trigger('first-render', 'level-plus');
  },

  attached() {
    mapData(this, {
      shopConfigs: (shopConfigs) => {
        this.setYZData({
          memberCenterLogoUrl: shopConfigs?.member_center_logo_url || '',
        });
      },
    });
    const { query = {} } = this.ctx.data || {};
    this.alias = query.alias || '';
    this.query = query;
    let branchStoreOpenOnline = false;
    const guideBizDataMap = getGuideBizDataMap(query);
    this.setYZData({
      bizDataMap: {
        ...guideBizDataMap,
      },
    });
    mapEvent(this, {
      onPageShow: () => {
        const { nowAlias, toRegister } = this.data;
        if (nowAlias && toRegister) {
          this.fetchUserLevelRegisterInfoComplete(this.levelGroupAlias);
        }
      },
    });
    app.getShopInfo().then((shopRes) => {
      const { chainStoreInfo = {}, shopMetaInfo = {} } = shopRes || {};
      const { isRootShop = false } = chainStoreInfo;

      branchStoreOpenOnline =
        !checkOfflineBranchStore(shopMetaInfo) ||
        checkOnlineBranchStore(shopMetaInfo);
      const isPartnerStore = checkPartnerStore(shopMetaInfo);
      // 总店 || 合伙人
      const shouldWaitForEnterShop = isRootShop || isPartnerStore;

      if (shouldWaitForEnterShop) {
        app.once('app:chainstore:kdtid:update', ({ kdtId }) => {
          if (kdtId !== this.data.kdtId) {
            this.setYZData({
              kdtId,
            });
          }
          this.fetchData(query, branchStoreOpenOnline);
        });
      } else {
        this.fetchData(query, branchStoreOpenOnline);
        this.setYZData({
          kdtId: app.getKdtId(),
        });
      }
    });

    // 是否要展示tabBar
    app.isSwitchTab().then((showTabBar) => {
      this.setYZData({
        showTabBar,
      });
    });

    // type === 2 表示付费等级
    checkIsSupportGoodsRecommend({ type: 2 }).then((res = {}) => {
      this.setYZData({
        isSupportGoodsRecommend: res.value,
      });
    });

    tryToBindShoppingGuideRelation(this.query);
    const url = args.add('packages/shop/levelcenter/plus/index', query);
    init({ url, request: newRequest });
    try {
      const params = pick(query, 'sl');
      if (params.sl) {
        getSlInfo(params.sl).then((data) => {
          if (data.salesman && data.salesmanType === 2) {
            // 是分销员 且 不是导购
            bindSalesman({ st: 14 });
          }
        });
      }
    } catch (error) {}
  },

  methods: {
    initFeatureMemberCenterData(branchStoreOpenOnline) {
      const promise = app.globalData.brandFeaturePromise;
      if (promise) {
        promise
          .then(({ payMemberCenter }) => {
            const isFeatureMemberCenter = !!payMemberCenter;
            if (!isFeatureMemberCenter) {
              this.fetchComponentsOfType(branchStoreOpenOnline);
              return;
            }
            payMemberCenter.components = addParamPreventCouponLeak(
              payMemberCenter.components,
              FeaturePageType.CUSTOM_COMPONENT,
              'member_pay'
            );
            this.setBrandFeatureData(payMemberCenter);
          })
          .catch(() => {
            this.SET_IS_FEATURE_MEMBER_CENTER({ isFeatureMemberCenter: false });
            this.fetchComponentsOfType(branchStoreOpenOnline);
          });
        return;
      }
      this.fetchComponentsOfType(branchStoreOpenOnline);
    },
    fetchComponentsOfType(branchStoreOpenOnline) {
      if (branchStoreOpenOnline) {
        fetchComponentsOfType('member_pay').then((comps) => {
          const components = addParamPreventCouponLeak(
            comps,
            FeaturePageType.CUSTOM_COMPONENT,
            'member_pay'
          );

          this.ctx.data.featureComponents = components;

          this.ctx.data.navigationBarConfigData = {
            style_color_type: 'global',
            navigationbar_config_type: 'global',
            special_nav_bg_color: '#252525',
            special_nav_color: '#fff',
          };
        });
      }
    },
    setBrandFeatureData(payMemberCenter) {
      const { components = [] } = payMemberCenter;
      const configData = components.find((i) => i.type === 'config');
      const navigationBarData = components.find(
        (i) => i.type === 'navigationbar_config'
      );

      console.log('set brand feature data', components);
      // 设置标题和页面背景色
      if (configData) {
        this.ctx.data.pageTitle = configData.title || 'PLUS会员中心';
      }
      this.ctx.data.featureComponents = components;
      if (navigationBarData) {
        this.ctx.data.navigationBarConfigData = navigationBarData;
      }
    },

    fetchData(query, branchStoreOpenOnline) {
      this.checkBindPhone();
      const requestArr = [
        this.fetchUserLevel(),
        this.fetchExperienceList(),
        this.initFeatureMemberCenterData(branchStoreOpenOnline),
      ];
      Promise.all(requestArr);
    },

    fetchUserLevel() {
      API.getPlusUserLevelInfo({
        type: LevelType.PAY,
      }).then((data) => {
        this.fetchUserLevelRegisterInfoComplete(data.levelGroupAlias);
        const width = wx.getSystemInfoSync().windowWidth - 41;
        const height = Number((width / (343 / 180)).toFixed(1));

        if (data.level) {
          this.fetchgAgreement();
        }
        this.setYZData({
          userLevel: data,
          width,
          height,
        });
        this.fetchLevelList(data);
      });
    },

    fetchUserLevelRegisterInfoComplete(levelGroupAlias) {
      API.checkUserLevelRegisterInfoComplete(levelGroupAlias).then((data) => {
        this.setYZData({
          toRegister: !data.value,
        });
        if (data.value) {
          const btnText = this.getBtnText({
            current: this.data.current,
            sku: this.nowSku,
          });
          this.setData({ btnText });
        }
        this.levelGroupAlias = levelGroupAlias;
      });
    },
    // 获取付费等级列表
    fetchLevelList(nowLevel) {
      API.getPlusLevelList({
        type: LevelType.PAY,
      })
        .then((data) => {
          data.levels = nowLevel;
          const level = levelAdapter(data, undefined, nowLevel);
          const { nowAlias } = this.data;

          const birthdayPosIndex = getBirthdayIndex(nowLevel, level.cards);

          // 如果当前有等级，则直接滚动到当前等级
          const currentLevelIndex = nowLevel?.level?.levelAlias
            ? level.indexMap[nowLevel?.level?.levelAlias]
            : 0;
          const current =
            level.indexMap[nowAlias] ||
            level.indexMap[this.alias] ||
            currentLevelIndex ||
            0;

          this.setYZData({
            cards: level.cards,
            indexMap: level.indexMap,
            loading: false,
            current,
            birthdayPosIndex,
          });

          this.changeNowLevelBenefit(level.cards[current]);
          this.changeInitData(current);
          // 如果不是会员，不用判断是否需要签署协议，直接引导弹框
          const hasLevel = this.setHasAlias();
          if (!hasLevel) {
            this.handleGuideJump();
          }
          const isAllDisabled = (level.cards || []).every(
            (levelCard) => !levelCard.isDisplay
          );
          const lowestLevelValue = get(nowLevel, 'level.levelValue', '');
          const isExpired = get(nowLevel, 'isExpired', true);
          // 停止招募还是会员的不展示
          if (
            isAllDisabled &&
            (!lowestLevelValue || (lowestLevelValue && !isExpired))
          ) {
            wx.showToast({
              title: '会员等级已停用',
              icon: 'error',
              duration: 1000,
            });
          }
        })
        .catch(() => {
          wx.showToast({
            title: '加载会员等级失败，请重试',
            icon: 'error',
            duration: 1000,
          });
        });
    },

    // 获取协议签署情况
    fetchgAgreement() {
      API.getAgreement()
        .then((data) => {
          const hasAgreement = !isEmpty(data) && data.userAgId;
          this.setYZData({
            hasAgreement,
          });
        })
        .catch((msg) => {
          wx.showToast({
            title: msg,
            icon: 'error',
          });
        });
    },

    // 获取体验会员信息列表
    fetchExperienceList() {
      API.getExperienceLevelList()
        .then((data) => {
          if (data.length) {
            const experienceMap = this.handleExperienceList(data);
            this.isFirstEnter = false;
            this.setYZData({
              experienceMap,
            });
          }
        })
        .catch((msg) => {
          wx.showToast({
            title: msg,
            icon: 'error',
          });
        });
    },

    // 获取体验会员信息列表
    fetchNowExperienceInfo(alias) {
      API.getNowExperienceInfo({
        alias,
      })
        .then((data) => {
          let res = {};
          let bindDialogShow = false;
          if (data.type === 'promot' && data.data.goodsId) {
            res = this.handleExperienceInfo(data.data);
            bindDialogShow = true;
          }
          if (data.type === 'highest' && data.data.levelExperience) {
            res = this.handleExperienceInfo(data.data.levelExperience);
            bindDialogShow = true;
          }

          this.setYZData({
            bindDialogShow,
            ...res,
          });
        })
        .catch((msg) => {
          wx.showToast({
            title: msg,
            icon: 'error',
          });
        });
    },

    handleExperienceInfo(levelExperience) {
      const goods = levelExperience;
      goods.goodsId = levelExperience.goodsId;
      goods.skuId = levelExperience.goodsSkuId;
      goods.price = levelExperience.experiencePrice;
      goods.showPrice = money(levelExperience.experiencePrice).toYuan();
      goods.isContinousOrder = false;
      goods.experienceTime = levelExperience.experienceTime;
      return {
        nowExperienceSku: goods,
        nowAlias: levelExperience.levelAlias,
        levelExperience,
      };
    },

    // 获取当前可参与的最高等级的体验会员
    fetchHighestLevel() {
      API.getExperienceHighestLevel()
        .then((data) => {
          if (data.levelExperience) {
            const { levelExperience = {} } = data;
            const goods = this.handleExperienceInfo(levelExperience);
            this.setYZData({
              bindDialogShow: true,
              nowExperienceSku: goods,
              nowAlias: levelExperience.levelAlias,
              levelExperience,
            });
          }
        })
        .catch((msg) => {
          wx.showToast({
            title: msg,
            icon: 'error',
          });
        });
    },

    fetchLevelGoods(nowLevelAlias) {
      const [levelGoods, fetchPromise] = fetchLevelGoods([], nowLevelAlias);
      if (levelGoods) {
        this.formatLevelGoodsDisplayInfo(levelGoods);
      } else {
        fetchPromise
          .then((data) => {
            this.formatLevelGoodsDisplayInfo(data);
          })
          .catch((msg) => {
            wx.showToast({
              title: msg,
              icon: 'error',
            });
          });
      }
    },

    formatLevelGoodsDisplayInfo(levelGoodsInfo) {
      const res = getCheapestSku(levelGoodsInfo.skuList);
      this.setYZData({
        bestPrice: res.bestPrice,
        bestType: res.bestType,
        monthPirce: res.monthPirce,
        originType: res.originType,
        experienceRenew: res.ExperienceRenew,
        isStartCheapest: res.isStartCheapest,
      });
    },

    handleExperienceList(data) {
      const experienceMap = {};
      data.forEach((item) => {
        experienceMap[item.levelAlias] = {
          ...item,
          skuId: item.goodsSkuId,
          price: item.experiencePrice,
        };
      });
      return experienceMap;
    },

    showInfotmationDialog(userLevel, hasCompleted) {
      const level = get(userLevel, 'level.levelAlias', null);
      return !hasCompleted && level;
    },

    handleSwipeChange(e) {
      if (e.type !== 'change') return;
      const { current } = e.detail;
      const level = get(this.data.cards, `${current}`, {});
      this.changeNowLevelBenefit(level);
      this.changeInitData(current);
    },

    changeNowLevelBenefit(level) {
      this.setYZData({
        nowBenefitList: level.benefitList,
        nowLevelAlias: level.card.alias,
      });
    },

    changeInitData(current) {
      const hasAlias = this.setHasAlias();
      const hasLevel = this.setHasLevel(current);
      const isContinous = this.setIsContinous(current);

      const level = get(this.data.cards, `${current}`, {});
      const name = get(level, 'card.name', '');
      const alias = get(level, 'card.alias', '');
      const levelValue = get(level, 'card.levelValue', 0);

      this.setYZData({
        hasLevel,
        hasAlias,
        isContinous,
        bestPrice: '',
        bestType: '',
        originType: '',
        current,
        nowAlias: alias,
        nowName: name,
        nowLevelValue: levelValue,
      });
      this.fetchLevelGoods(level.card.alias);
      const { isFirstEnter, experienceMap } = this.data;
      if (isFirstEnter) return;
      const info = experienceMap[alias];
      this.setNowExperienceSku(info);
    },
    setHasAlias() {
      const { userLevel } = this.data;
      return !!get(userLevel, 'level.levelAlias', null);
    },
    setHasLevel(current) {
      const { userLevel, cards } = this.data;
      const nowAlias = get(cards, `[${current}].card.alias`, '');
      const hasAlias = get(userLevel, 'level.levelAlias', null);
      return nowAlias === hasAlias;
    },

    setIsContinous(current) {
      const { cards, userLevel, hasAgreement } = this.data;
      const card = get(cards, `[${current}]`, {});
      // 当前等级没有禁用，没有绑定协议，等级已经过期展示
      if (card.isDisplay && (!hasAgreement || userLevel.isExpired)) {
        return false;
      }
      const nowAlias = get(cards, `[${current}].card.alias`, '');
      const hasAlias = get(userLevel, 'level.levelAlias', null);
      return nowAlias === hasAlias;
    },

    // 筛选当前等级商品的购买信息
    setNowExperienceSku(info) {
      const goods = info || {};
      if (info) {
        goods.goodsId = info.goodsId;
        goods.skuId = info.goodsSkuId;
        goods.price = info.experiencePrice;
        goods.showPrice = money(info.experiencePrice).toYuan();
        goods.isContinousOrder = false;
      }
      this.setYZData({
        nowExperienceSku: goods,
      });
    },

    getBtnText({ current, sku }) {
      const { userLevel, nowLevelAlias, cards, toRegister } = this.data;
      const isExperienceLevel = get(userLevel, 'isExperienceLevel', null);
      const isExpired = get(userLevel, 'isExpired', false);
      const alias = get(userLevel, 'level.levelAlias', null);
      const price = get(sku, 'handlePrice.price', null);
      if (alias === nowLevelAlias && !isExpired) {
        return '立即续费';
      }

      if (sku.goodsSkuType === LevelGoodsSkuType.Renewal) {
        return '复制订购链接';
      }

      if (toRegister) {
        return '立即开通';
      }

      if (alias === nowLevelAlias && isExperienceLevel) {
        return '正式开通会员';
      }

      if (price) {
        return `立即支付${price}元`;
      }

      // 兜底逻辑
      const name = get(cards, `[${current}].card.name`, '');
      return `立即开通${name || ''}`;
    },

    callStartPay({ type }) {
      const recruitParams = getRecruitMemberParams(this.query);
      const payWaysRef = this.selectComponent('#payViewRef');
      payWaysRef.toCreateOrder(type, recruitParams);
    },

    toRenewH5Html() {
      const { nowAlias, kdtId } = this.data;
      const recruitParams = getRecruitMemberParams(this.query);
      openWebView('https://cashier.youzan.com/pay/wscuser_paylevel', {
        query: {
          ...recruitParams,
          kdt_id: kdtId,
          alias: nowAlias,
        },
      });
    },

    checkBindPhone() {
      let showBindPhoneNumber = false;
      if (!app.getBuyerId()) {
        showBindPhoneNumber = true;
        this.on('app:token:success', () => {
          if (app.getBuyerId()) {
            this.setYZData({
              hasMobile: true,
            });
          }
        });
      }
      this.setYZData({
        hasMobile: !showBindPhoneNumber,
      });
    },

    handleAllTriggerEvent(e) {
      const { type, detail } = e;
      const { nowAlias, kdtId } = this.data;
      switch (type) {
        case 'register':
          openWebView('/wscuser/pluscenter/fill', {
            query: {
              ...this.query,
              kdt_id: kdtId,
              alias: nowAlias,
              groupAlias: this.levelGroupAlias,
            },
          });
          break;
        case 'hasAuth':
          this.setYZData({
            hasAuthorized: detail,
          });
          break;
        case 'bindMobile': {
          tryToBindShoppingGuideRelation(this.query);
          this.setYZData({
            hasMobile: true,
          });
          break;
        }
        case 'closeCodeDialog':
          this.setYZData({
            isShow: false,
          });
          break;
        case 'pay':
          if (this.data.hasMobile) {
            this.callStartPay({ type: 'cycle' });
          }
          break;
        case 'experience':
          if (this.data.hasMobile) {
            this.callStartPay({ type: 'experience' });
          }
          break;
        case 'renew':
          this.toRenewH5Html();
          break;
        case 'copy': {
          const { nowAlias, kdtId } = this.data;
          const recruitParams = getRecruitMemberParams(this.query);
          const copyLink = args.add(
            'https://cashier.youzan.com/pay/wscuser_paylevel',
            {
              ...recruitParams,
              kdt_id: kdtId,
              alias: nowAlias,
            }
          );
          wx.setClipboardData({
            data: copyLink,
            success() {},
          });
          break;
        }
        default:
          return null;
      }
    },
    // 同意协议
    finish() {
      this.handleGuideJump();
    },
    // 领卡成功弹框
    handleGuideJump() {
      // 是否显示领卡成功弹框
      guideJumpDialog(
        GUIDE_DIALOG_TYPE.payLevelcenter,
        GUIDE_SUCCESS_SIGN,
        this.query,
        () => {
          this.fetchNowExperienceInfo(this.query.alias);
        }
      );
    },
    handleSkuChange(data) {
      const sku = data.detail || {};
      const btnText = this.getBtnText({
        current: this.data.current,
        sku,
      });
      this.nowSku = sku;
      // setYZData有未知问题，视图层更新有问题
      this.setData({ currentCycleSku: data.detail || {}, btnText });
    },
  },
});
