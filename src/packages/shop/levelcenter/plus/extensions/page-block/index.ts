const app = getApp();
export default class {
  ctx: any;

  constructor({ ctx }) {
    this.ctx = ctx;

    this.ctx.data.pageTitle = 'PLUS会员中心';
  }

  beforePageMount({ query, route }) {
    this.ctx.data.query = query;
    this.ctx.data.route = route;
  }

  onPullDownRefresh = () => {
    wx.stopPullDownRefresh();
  };

  onPageShow() {
    this.ctx.event.emit('onPageShow');
  }

  getShareInfo = () => {
    const userInfo = new Promise((resolve) => {
      app.getUserInfo(({ userInfo }) => {
        resolve(userInfo);
      });
    });
    return Promise.all([app.getShopInfo(), userInfo]);
  };

  async onShareAppMessage() {
    const [shopInfo, userInfo] = await this.getShareInfo();
    return {
      title: `${userInfo?.nickName || '好友'}邀请你加入${
        shopInfo?.shop_name || ''
      }会员，享会员权益`,
    };
  }
}
