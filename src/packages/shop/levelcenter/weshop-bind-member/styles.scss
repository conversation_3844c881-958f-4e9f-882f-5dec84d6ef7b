.loading {
  width: 100vw;
  height: 100vh;
}

.container {
  width: 100vw;
  position: relative;

  .bind-info,
  .no-bind-info {
    color: #111;
    margin-top: 25%;
    text-align: center;
  }

  .title {
    font-size: 24px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title-icon {
    width: 24px;
    height: 24px;
    margin-right: 3px;
  }

  .subTitle {
    font-size: 16px;
    margin-top: 5px;
    font-weight: 400;
  }

  .btn {
    background-color: #262021;
    color: #fff;
    width: 250px;
    height: 50px;
    border-radius: 25px;
    position: fixed;
    margin-left: -125px;
    left: 50%;
    bottom: calc(150px + constant(safe-area-inset-bottom));
    bottom: calc(150px + env(safe-area-inset-bottom));
  }
}
