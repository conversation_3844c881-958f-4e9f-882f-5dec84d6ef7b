
<van-loading custom-class="loading" wx:if="{{ loading }}" size="32px" type="spinner">加载中...</van-loading>
<page-container 
  wx:else
  class="container" 
  pageContainerStyle="background-image: url('https://img01.yzcdn.cn/upload_files/2024/12/27/FiGGPHQxsEx4-aMIa3YEz9wyV0w3.png');background-size: cover;background-repeat: no-repeat; background-position: center center;" 
  forbidCopyright 
  openCustomNav
>
  <view wx:if="{{ isBind }}" class="bind-info">
    <view class="title">
      <image class="title-icon" src="https://img01.yzcdn.cn/upload_files/2024/12/27/FkTyLmq_fyRxtilYxGDzVYGcvM-h.png" />
      成功绑定
    </view>
    <view class="subTitle">当前等级：{{ bindInfo.levelName }}</view>
  </view>
  <view wx:else class="no-bind-info">
    <view class="title">
      微信小店下单
    </view>
    <view class="subTitle">可累计会员成长值，获取会员福利</view>
  </view>
  <user-authorize
    authTypeList="{{['mobile']}}"
    bind:next="handleCallAuth"
  >
    <button class="btn">
      {{ btnText }}
    </button>
  </user-authorize>
</page-container>