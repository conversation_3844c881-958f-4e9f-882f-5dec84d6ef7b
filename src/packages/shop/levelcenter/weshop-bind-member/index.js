import WscPage from 'pages/common/wsc-page/index';
import args from '@youzan/weapp-utils/lib/args';
import openWebView from 'utils/open-web-view';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';

const app = getApp();

WscPage({
  data: {
    bindInfo: {},
    btnText: '关联微信小店',
    isBind: false,
    loading: true,
  },

  onShow() {
    if (app.globalData.doBindWeixinXiaoDianMember) {
      this.setYZData({
        loading: true,
      });
      this.handleBind().then(() => {
        this.setYZData({
          loading: false,
        });
      });
    } else {
      this.getBindInfo();
    }
  },

  onUnload() {
    app.globalData.doBindWeixinXiaoDianMember = null;
  },

  onPullDownRefresh() {
    this.getBindInfo();
  },

  handleCallAuth() {
    const _this = this;
    const { bindInfo, isBind } = this.data;

    if (isBind) {
      return this.goWeShop();
    }

    if (!bindInfo.isMember) {
      return _this.handleSignInMember();
    }

    Dialog.confirm({
      title: '关联绑定提醒',
      message:
        '您是否同意将当前微信小店与当前登录的小程序会员账号进行关联，关联后无法解绑。',
      confirmButtonText: '确定关联',
      confirmButtonColor: '#FF0000',
      cancelButtonText: '我再想想',
      cancelButtonColor: '#999',
    }).then(() => {
      _this.handleBind();
    });
  },

  handleSignInMember() {
    app.globalData.doBindWeixinXiaoDianMember = true;
    openWebView(
      args.add('/wscuser/levelcenter/fill', {
        kdt_id: getApp().getKdtId(),
        levelType: 1,
        fromScene: 'complete',
        jumpTo: 'Home',
        navigateBackPreviousPage: 1,
        weappNavigateBack: 1,
      }),
      { title: '完善信息' }
    );
  },

  goWeShop() {
    wx.exitMiniProgram({
      success() {
        console.log('退出小程序成功');
      },
      fail(error) {
        console.error('退出小程序失败', error);
      },
    });
  },

  getBindInfo() {
    this.setYZData({
      loading: true,
    });
    app
      .request({
        path: '/wscuser/weapp/query-customer.json',
        data: {
          extra: JSON.stringify(this.__query__),
        },
        method: 'GET',
      })
      .then((data) => {
        const isBind = data?.isSyncWeiXiaoDian && data?.isMember;
        this.setYZData({
          loading: false,
          bindInfo: data,
          isBind,
          btnText: isBind ? '去小店逛逛' : '关联微信小店',
        });
      })
      .catch((e) => {
        this.setYZData({
          loading: false,
        });
        e &&
          wx.showToast({
            title: e.msg,
            icon: 'none',
          });
      });
  },

  handleBind() {
    const { session_id } = this.__query__;
    return app
      .request({
        path: '/wscuser/weapp/bind-weshop.json',
        data: {
          wxdSessionId: session_id,
        },
        method: 'POST',
      })
      .then((data) => {
        const isBind = data?.isSyncWeiXiaoDian && data?.isMember;

        this.setYZData({
          bindInfo: data,
          isBind,
          btnText: isBind ? '去小店逛逛' : '关联微信小店',
        });
      })
      .catch((e) => {
        e &&
          wx.showToast({
            title: e.msg,
            icon: 'none',
          });
      });
  },
});
