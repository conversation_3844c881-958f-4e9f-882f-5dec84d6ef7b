<import src="./custom-tpl.wxml" />
<view>
  <block wx:for="{{ design }}" wx:key="type">
    <block wx:if="{{ item.type === 'search-input' }}">
      <search-input focus="{{ inputFocus }}" bind:search="handleInputSearch" data-type="suggest" bind:automated-word-click="handleWordItemClick" defaultValue="{{ searchDefaultValue }}"></search-input>
    </block>
    <block wx:elif="{{ item.type === 'history-words-list' }}">
      <words-list wx:if="{{ historyList && historyList.length > 0 }}" data-type="history" title="历史搜索" list="{{ historyList }}" show-clear max-line-num="{{ 2 }}" bind:clear="handleClearHistory" bind:word-item-click="handleWordItemClick" />
    </block>
    <block wx:elif="{{ item.type === 'hot-words-list' }}">
      <words-list wx:if="{{ hotList && hotList.length > 0 }}" data-type="hot" title="热门搜索" loggerParams="{{ hotListLoggerParams }}" list="{{ hotList }}" bind:word-item-click="handleWordItemClick" />
    </block>
    <block wx:elif="{{ item.custom }}">
      <template is="{{ item.type }}" />
    </block>
  </block>
  <van-dialog id="van-dialog" />
</view>
<inject-protocol noAutoAuth />