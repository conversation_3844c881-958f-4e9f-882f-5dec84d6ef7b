<view class="deco-search-input-wrap">
  <van-search
    value="{{ defaultValue }}"
    placeholder="搜索商品"
    focus="{{ focus }}"
    shape="round"
    input-class="search-page-input"
    use-action-slot
    bind:change="handleInput"
    bind:search="handleSearch"
  >
    <view slot="action" class="text-action" bind:tap="handleSearch">搜索</view>
  </van-search>
  <view class="automated-words-list" wx:if="{{ automatedWordsList.length > 0 }}">
    <view
      wx:for="{{ automatedWordsList }}"
      wx:key="*this"
      class="automated-words-list__item"
      data-item="{{ item }}"
      bind:tap="handleAutomatedWordClick"
    >
      <highlight-text
        text="{{ item }}"
        color="#969799"
        highlight-color="#323233"
        highlight-text="{{ searchText }}"
      />
    </view>
  </view>
</view>
