import debounce from '@youzan/weapp-utils/lib/debounce';
import WscComponent from 'shared/common/base/wsc-component/index';

import Api from '../../api';

WscComponent({
  properties: {
    defaultValue: String,
    focus: Boolean,
  },

  data: {
    automatedWordsList: [],
    searchText: '',
    extraInfo: {},
  },

  attached() {
    this.debouncedSearch = debounce(this.getAutomatedWords, 300);
    this.setYZData({
      searchText: this.data.defaultValue || '',
    });
  },

  methods: {
    getAutomatedWords() {
      const searchWords = this.data.searchText;
      if (searchWords) {
        Api.getAutomatedWords(searchWords).then((res) => {
          const { recommendWordsList = [], extraInfo = {} } = res || {};
          this.setYZData({
            automatedWordsList: recommendWordsList,
            extraInfo,
          });
        });
      } else {
        this.setYZData({
          automatedWordsList: [],
          extraInfo: {},
        });
      }
    },
    handleInput({ detail }) {
      this.setYZData(
        {
          searchText: detail,
        },
        () => {
          this.debouncedSearch();
        }
      );
    },
    handleSearch() {
      setTimeout(() => {
        this.triggerEvent('search', { text: this.data.searchText });
      }, 100);
    },
    handleAutomatedWordClick(event) {
      this.triggerEvent('automated-word-click', {
        wordItem: { text: event.currentTarget.dataset.item },
        slg: this.data.extraInfo.slg || '',
        alg: this.data.extraInfo.alg || '',
      });
    },
  },
});
