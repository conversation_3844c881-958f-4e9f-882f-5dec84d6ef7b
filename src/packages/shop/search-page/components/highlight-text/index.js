import WscComponent from 'shared/common/base/wsc-component/index';

WscComponent({
  properties: {
    text: String,
    color: String,
    highlightText: {
      type: String,
      observer: 'getHighlightText',
    },
    highlightColor: String,
  },

  data: {
    textArray: [],
  },

  methods: {
    getHighlightText() {
      let { text } = this.data;
      const { highlightText } = this.data;

      if (!highlightText || !text) {
        return text || '';
      }

      for (let i = 0; i < highlightText.length; i++) {
        const re = new RegExp(highlightText[i], 'g');
        if (re.test(this.data.text)) {
          text = text.replace(re, '~~$&~~');
        }
      }

      this.setYZData({
        textArray: text.split('~~'),
      });
    },
  },
});
