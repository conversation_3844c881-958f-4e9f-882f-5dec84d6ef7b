import WscComponent from 'shared/common/base/wsc-component/index';

const LINE_HEIGHT = 40;

WscComponent({
  properties: {
    list: {
      type: Array,
      value: [],
      observer: 'initToggle',
    },
    hotListLoggerParams: {
      type: Object,
      value: {},
    },
    title: String,
    showClear: <PERSON>olean,
    maxLineNum: Number,
  },

  data: {
    showToggle: false,
  },

  methods: {
    initToggle(list) {
      const { maxLineNum } = this.data;
      if (list.length > 0 && maxLineNum > 0) {
        const query = wx.createSelectorQuery().in(this);
        query.select('.deco-words-list__inner').boundingClientRect();
        query.exec((res) => {
          if (res && res.length) {
            const maxHeight = maxLineNum * LINE_HEIGHT;
            const targetElementAttr = res[0];
            if (targetElementAttr.height > maxHeight) {
              this.setYZData({
                showToggle: true,
                isHidden: true,
                hiddenStyle: `max-height: ${maxHeight}px; overflow: hidden;`,
              });
            }
          }
        });
      }
    },
    toggleHidden() {
      this.setYZData({
        isHidden: !this.data.isHidden,
        immediate: true,
      });
    },
    handleClickClear() {
      this.triggerEvent('clear');
    },
    handleClickWordItem(e) {
      const { hotListLoggerParams } = this.data;
      this.triggerEvent('word-item-click', {
        ...hotListLoggerParams,
        wordItem: e.detail,
      });
    },
  },
});
