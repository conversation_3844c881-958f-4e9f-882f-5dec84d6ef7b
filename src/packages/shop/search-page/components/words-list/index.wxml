<view class="deco-words-list">
  <view wx:if="{{ title }}" class="deco-words-list__title">{{ title }}</view>
  <view wx:if="{{ showClear && list.length > 0 }}" class="deco-words-list__clear" bind:tap="handleClickClear">
    <van-icon name="delete" />
    <view class="text">清空</view>
  </view>
  <view class="deco-words-list__list" style="{{ isHidden ? hiddenStyle : '' }}">
    <view class="deco-words-list__inner">
      <word-item wx:for="{{ list }}" wx:for-item="wordItem" wx:key="index" wordItem="{{ wordItem }}" bind:click="handleClickWordItem"></word-item>
    </view>
  </view>
  <view wx:if="{{ showToggle }}" class="deco-words-list__arrow {{ !isHidden ? 'is-up': '' }}" bind:tap="toggleHidden">
    <van-icon name="arrow-down" />
  </view>
</view>