import Dialog from '@vant/weapp/dist/dialog/dialog';
import WscPage from 'pages/common/wsc-page/index';
import jumpToLink from 'shared/components/showcase/utils/jumpToLink';
import { Hummer } from 'shared/utils/hummer';
import { getWordConfigs, mapWords } from '@/utils/hot-words';
import { getPlugins } from '@youzan/ranta-helper-tee';
import yunDesign from './design.json';
import sassPageConfig from './sass/page-config.js';

import Api from './api';

const { dmc } = getPlugins();

const SEARCHPAGE_HISTORY_KEY = 'deco:search_page_history';
const SEARCHPAGE_OLDHOTS_KEY = 'deco:search_page_oldhotwords';

const MAX_HISTORY_LENGTH = 15;

const app = getApp();

const { system } = wx.getSystemInfoSync();
const isIOS = system && system.indexOf('iOS') > -1;

const extendPageConfig = ECLOUD_MODE ? sassPageConfig : {};

WscPage(extendPageConfig, {
  data: {
    historyList: [],
    hotList: [],
    keepWords: '',
    inputFocus: isIOS,
    hotListLoggerParams: {},
    design: [],
    searchDefaultValue: '',
  },

  onLoad(options) {
    const { keepWords = '', oldHots = '', isFromList = '' } = options;
    const oldHotsList = this.getOldHotWords(oldHots);

    let decodeKeepWords = '';
    try {
      decodeKeepWords = decodeURIComponent(keepWords);
    } catch (error) {
      decodeKeepWords = '';
    }

    Api.getHotWords().then((res) => {
      let { chosenWords = [], normalWords = [] } = res || {};
      const { automaticSwitch = 0, isCover, extraInfo = {} } = res || {};

      const originNormalWords = [...normalWords];

      const wordConfigs = getWordConfigs(res);
      chosenWords = mapWords(chosenWords, true, wordConfigs.chosenWords || []);
      normalWords = mapWords(normalWords, false, wordConfigs.normalWords || []);

      let hotList = [];
      if (+automaticSwitch && isCover) {
        hotList = chosenWords.concat(
          mapWords(originNormalWords || [], false, [])
        );
      } else {
        hotList =
          isCover || oldHotsList.length === 0
            ? chosenWords.concat(normalWords)
            : mapWords(oldHotsList, false, []);
      }

      Hummer.mark?.log?.({ tag: 'search-page', scene: ['route'] });

      this.setYZData({
        hotList,
        keepWords: decodeKeepWords,
        hotListLoggerParams: extraInfo,
        searchDefaultValue: isFromList === '1' ? decodeKeepWords : '',
      });
    });

    this.renderHistory();

    this.initDesign();
  },

  onShow() {
    if (isIOS) {
      setTimeout(() => {
        this.setData({
          inputFocus: true,
        });
      }, 100);
    }
  },

  initDesign() {
    const { design } = yunDesign;
    const index = design.findIndex((v) => v.type === 'config');

    this.setData({
      design: index === 0 ? design.slice(1) : design,
    });
  },

  getOldHotWords(oldHotsFromQuery) {
    let oldHotsList = [];
    let oldHotStr = oldHotsFromQuery;
    // 如果query上没带参数，尝试从 storage 读取
    if (!oldHotStr) {
      oldHotStr = wx.getStorageSync(SEARCHPAGE_OLDHOTS_KEY);
    } else {
      // 有值存入本地
      wx.setStorageSync(SEARCHPAGE_OLDHOTS_KEY, oldHotStr);
    }
    try {
      oldHotsList = decodeURIComponent(oldHotStr).split(',');
    } catch (error) {
      oldHotsList = [];
    }

    return oldHotsList;
  },

  handleClearHistory() {
    Dialog.confirm({
      title: '确定删除搜索历史？',
    }).then(() => {
      this.clearHistory();
    });
  },

  handleInputSearch(e) {
    const { text: keywords = '' } = e.detail;
    this.handleLog({
      words: keywords || this.data.keepWords || '',
      words_type: 'common',
      slg: '',
      alg_id: '',
    });
    e.detail.type = 'common';
    this.startSearch(e.detail);
  },

  handleWordItemClick(e) {
    const { type = '' } = e.currentTarget.dataset || {};
    const { wordItem = {}, ...params } = e.detail;
    this.handleLog({
      words: wordItem.text || '',
      words_type: type,
      ...params,
      slg: params.slg || '',
      alg_id: params.alg || '',
    });
    e.detail.type = type;

    this.startSearch({ ...params, text: wordItem.text }, wordItem);
  },

  startSearch(data, wordItem = {}) {
    const { text: keywords = '', ...params } = data;
    const searchWords = keywords || this.data.keepWords;

    if (wordItem.link_switch && wordItem.link_url) {
      jumpToLink(wordItem.link_type, wordItem);
      return;
    }

    if (searchWords) {
      this.setYZData({
        keepWords: searchWords,
      });
      this.setHistory(searchWords);
      dmc.redirectTo('Search', {
        q: encodeURIComponent(searchWords),
        ...params,
      });
    }
  },

  handleLog(params) {
    if (app.logger) {
      app.logger.setContext(params, 30);
      app.logger.log({
        et: 'click',
        ei: 'search',
        en: '点击搜索',
        params,
      });
    }
  },

  setHistory(keywords) {
    const historyList = this.data.historyList.filter(
      (item) => item.text !== keywords
    );
    historyList.unshift({ text: keywords });
    if (historyList.length > MAX_HISTORY_LENGTH) {
      historyList.splice(MAX_HISTORY_LENGTH, historyList.length);
    }
    this.setYZData(
      {
        historyList,
      },
      () => {
        wx.setStorage({
          key: SEARCHPAGE_HISTORY_KEY,
          data: this.data.historyList,
        });
      }
    );
  },

  renderHistory() {
    wx.getStorage({
      key: SEARCHPAGE_HISTORY_KEY,
      success: (res) => {
        const historyList = res.data || [];
        this.setYZData({
          historyList,
        });
      },
    });
  },

  clearHistory() {
    this.setYZData(
      {
        historyList: [],
      },
      () => {
        wx.removeStorage({
          key: SEARCHPAGE_HISTORY_KEY,
        });
      }
    );
  },
});
