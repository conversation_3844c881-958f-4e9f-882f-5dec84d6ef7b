<!-- 签到提醒 -->
<view wx:if='{{ showReminder }}' class='reminder'>
  <view class='tips'>{{ isReminderOpen ? '已' : '' }}开启签到提醒</view>
  <form-view>
    <van-switch canReminderOpen="{{canReminderOpen}}" checked='{{ isReminderOpen }}' size='22px' active-color='{{ themeColor }}' bind:change='onChangeReminderOpen' />
  </form-view>

  <subscribe-fail-popup show="{{ showSubscirbeFail }}"
    openImageSrc="https://img01.yzcdn.cn/public_files/2019/12/23/7b987495fc6bbd066262153577881592.png"
    bind:close="closeSubscribeFailPopup"/>
</view>
