import WscComponent from 'pages/common/wsc-component/index';
import { subscribeMessage } from '@/helpers/subscribe.js';
import { SCENE } from '@/utils/subscribe-message';

const REFERENCE = 3; // 模板类型：1-小程序模板消息; 2-h5; 3-小程序一次性订阅消息
const app = getApp();

WscComponent({
  properties: {
    themeColor: {
      type: String,
    },
    canReminderOpen: {
      type: Boolean,
    },
  },

  data: {
    showReminder: false,
    isReminderOpen: false,
    showSubscirbeFail: false, // 小程序订阅消息失败引导弹窗
  },

  observers: {
    canReminderOpen(open) {
      if (open && !this.data.isReminderOpen) {
        this.switchCheckinRemind(true);
      }
    },
  },

  ready() {
    this.getCheckinRemindTabShow();
  },

  methods: {
    // 获取签到提醒开关状态
    getCheckinRemindTabShow() {
      app
        .request({
          path: '/wscump/checkin/checkin-remind-tab-show.json',
          query: {
            reference: REFERENCE,
          },
        })
        .then((res) => {
          this.setYZData({
            showReminder: res.isShopCheckinRemindOpen,
            isReminderOpen: res.isUserCheckinRemindOpen,
          });
        })
        .catch((err) => {
          err &&
            wx.showToast({
              title: err,
              icon: 'none',
            });
        });
    },

    onChangeReminderOpen(event) {
      const checked = event.detail;
      if (checked) {
        this.openReminder();
      } else {
        this.closeReminder();
      }
    },

    // 开启签到提醒
    openReminder() {
      const self = this;
      subscribeMessage({
        scene: SCENE.SIGN,
        pageId: 3,
        needAlwaysToast: true,
        successCallBack: () => {
          // 订阅成功后，更新开关状态
          self.switchCheckinRemind(true);
        },
        failCallBack: () => {
          // 失败则弹出失败弹窗
          self.setYZData({
            showSubscirbeFail: true,
          });
        },
        showCallBack: () => {
          self.triggerEvent('toggle-subscribe-mask', true);
        },
        closeCallBack: () => {
          self.triggerEvent('toggle-subscribe-mask', false);
        },
        logParam: {
          subscribe_page: '签到页',
          subscribe_type: '签到提醒',
        },
      });
    },

    // 关闭签到提醒
    closeReminder() {
      const self = this;
      wx.showModal({
        title: '关闭提醒',
        content: '关闭提醒，可能会漏签而无法获得连签奖励。你可以随时开启提醒',
        confirmText: '保持开启',
        cancelText: '关闭',
        success(e) {
          if (e.cancel) {
            self.switchCheckinRemind(false);
          }
        },
      });
    },

    closeSubscribeFailPopup() {
      this.setYZData({
        showSubscirbeFail: false,
      });
    },

    // 开启或关闭消息提醒
    switchCheckinRemind(isReminderOpen) {
      const msg = `${isReminderOpen ? '开启' : '关闭'}签到提醒失败，请重试`;
      app
        .request({
          path: 'wscump/checkin/open-checkin-remind.json',
          query: {
            isOpen: isReminderOpen,
            reference: REFERENCE,
          },
        })
        .then((res) => {
          if (!res) {
            wx.showToast({
              title: msg,
              icon: 'none',
            });
            return;
          }

          this.setYZData({
            isReminderOpen,
          });

          isReminderOpen &&
            wx.showToast({
              title: '已开启提醒',
              icon: 'none',
            });
        })
        .catch(() => {
          wx.showToast({
            title: msg,
            icon: 'none',
          });
        });
    },
  },
});
