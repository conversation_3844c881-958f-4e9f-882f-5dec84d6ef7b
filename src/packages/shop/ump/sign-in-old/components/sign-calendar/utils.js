// 每月天数
const MONTH_MAP = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

function isLeapYear(year) {
  return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

// 计算一个月的天数
function calcDateAmount(year, month) {
  let amount = MONTH_MAP[month - 1];
  return isLeapYear(year) && month === 2 ? amount + 1 : amount;
}

// 计算本月日历中含上月的天数
function calcPreviousMonth(year, month) {
  let firstDate = `${year}/${month}/01`;
  return new Date(firstDate).getDay() - 0;
}

// 计算本月日历中含下月的天数
function calcLastMonth(year, month, dateAmount) {
  let lastDate = `${year}/${month}/${dateAmount}`;
  return 6 - new Date(lastDate).getDay();
}

export default {
  calcDateAmount,
  calcPreviousMonth,
  calcLastMonth,
};
