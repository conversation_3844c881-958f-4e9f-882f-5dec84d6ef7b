export default class {
  ctx: any;

  constructor({ ctx }) {
    this.ctx = ctx;
  }

  beforePageMount({ query, route }) {
    this.ctx.data.query = query;
    this.ctx.data.route = route;
  }

  onShareAppMessage() {
    const { shareTitle, bannerImageUrl, checkInId } = this.ctx.data;

    return {
      title: shareTitle,
      path: `packages/shop/ump/sign-in/index?checkin_id=${checkInId}`,
      imageUrl: bannerImageUrl,
    };
  }
}
