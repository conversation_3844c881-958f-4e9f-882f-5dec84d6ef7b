import parseData from 'utils/parse-data';
import { moment } from 'utils/time';

const urlImgqn = 'https://img01.yzcdn.cn';

export const CHECKINAWARDTYPE = {
  day: 1, // 日签
  coutinue: 2, // 连续签到
  surpriseCoupon: 3, // 惊喜券
  noneAward: 4, // 无奖励
};

export const AWARDTYPE = {
  credit: 'credit',
  bonus: 'bonus',
  coupon: 'coupon',
};

// 优惠券类型
export const COUPON = {
  cash: 1, // 代金券
  discount: 2, // 折扣券
  voucher: 3, // 兑换券
};

// 日历签到-自定义配置默认展示
export const CHECKINCONFIG = {
  backgroundImage: `${urlImgqn}/upload_files/2021/11/02/Fky7vV_QA-ZpljXibtX50Ia8RU4R.png`,
  themeColor: '#FF9300',
  backgroundBtn: 'linear-gradient(-180deg,#ffa428 3%,#ff9300 98%)',
  dialogBackgroundColor: 'linear-gradient(to bottom, #ffa347, #ff9300)',
};

// 优惠券显示的类型
export const COUPONTYPE = {
  coutinue: 1, // 连续签到
  checkin: 2, // 签到弹框
  dayCheckin: 3, // 日签
};

// 解决某些用户出国时区发生了偏差的问题
const transferTimeZone = (timeStamp) => {
  const chinaTimeZoneOffset = -8 * 60 * 60 * 1000;
  const timeZoneOffset = new Date().getTimezoneOffset() * 60 * 1000;
  const gap = timeZoneOffset - chinaTimeZoneOffset;
  return timeStamp + gap;
};

// 计算年份和月份
export function calcYearAndMonth(year, month, e) {
  const MONTH_PER_YEAR = 12;
  const step = e.detail;
  const totalMonths = year * MONTH_PER_YEAR + month + step;
  const tempYear = Math.floor(totalMonths / MONTH_PER_YEAR);
  const tempMonth = totalMonths % MONTH_PER_YEAR;
  return {
    year: tempMonth === 0 ? tempYear - 1 : tempYear,
    month: tempMonth === 0 ? MONTH_PER_YEAR : tempMonth,
  };
}

export const parseTime = (data) =>
  parseData(
    {
      startTime: ({ current_cycle_start_at }) =>
        current_cycle_start_at
          ? moment(transferTimeZone(current_cycle_start_at), 'YYYY.MM.DD')
          : '',
      endTime: ({ current_cycle_end_at }) =>
        current_cycle_end_at
          ? moment(transferTimeZone(current_cycle_end_at), 'YYYY.MM.DD')
          : '',
      checkInDates: ({ checkin_date }) =>
        checkin_date.map((elem) => ({
          year: new Date(transferTimeZone(elem)).getFullYear(),
          month: new Date(transferTimeZone(elem)).getMonth() + 1,
          date: new Date(transferTimeZone(elem)).getDate(),
        })),
    },
    [],
    data
  );
