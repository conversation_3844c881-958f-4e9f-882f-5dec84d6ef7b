import WscComponent from 'pages/common/wsc-component/index';
import time from './utils';

WscComponent({
  properties: {
    canJumpPrevious: {
      type: Boolean,
      value: false,
    },
    canJumpNext: {
      type: Boolean,
      value: false,
    },
    signDates: {
      type: Array,
      value: [],
      observer: 'setMonthTable',
    },
    year: {
      type: Number,
      value: new Date(Date.now()).getFullYear(),
      observer: 'setMonthTable',
    },
    month: {
      type: Number,
      value: new Date(Date.now()).getMonth() + 1,
      observer: 'setMonthTable',
    },
    themeColor: {
      type: String,
    },
    canReminderOpen: {
      type: Boolean,
    },
  },

  data: {
    weeksMap: ['日', '一', '二', '三', '四', '五', '六'],
    monthTable: [],
  },

  methods: {
    // 设置日历
    setMonthTable() {
      const wholeDates = this.getWholeMonthDate();
      const monthTable = this.processDatesToWeek(wholeDates);
      this.setYZData({
        monthTable,
      });
    },

    // 获取完整日历，目前不需要显示上月和下月日期，故用0表示
    getWholeMonthDate() {
      const { year, month } = this.data;
      const previousMonthDate = time.calcPreviousMonth(year, month);
      const dateAmount = time.calcDateAmount(year, month);
      const lastMonthDate = time.calcLastMonth(year, month, dateAmount);
      const wholeDates = [];
      const totalDate = previousMonthDate + dateAmount + lastMonthDate;
      for (let i = 1; i <= totalDate; i++) {
        const elem = {
          value: 0,
          hasSign: false,
        };
        if (i > previousMonthDate && i <= previousMonthDate + dateAmount) {
          elem.value = i - previousMonthDate;
          elem.hasSign = this.isSignDate(year, month, elem.value);
        }
        wholeDates.push(elem);
      }
      return wholeDates;
    },

    // 该日是否签到
    isSignDate(year, month, date) {
      const signDatesJSON = JSON.stringify(this.data.signDates);
      const dayJSON = JSON.stringify({
        year,
        month,
        date,
      });
      const result = signDatesJSON.indexOf(dayJSON) !== -1;
      return result;
    },

    // 处理成week
    processDatesToWeek(wholeDates) {
      const WEEK_LENGTH = 7;
      const monthTable = [];
      for (let i = 0; i < wholeDates.length / WEEK_LENGTH; i++) {
        const week = wholeDates.slice(i * WEEK_LENGTH, (i + 1) * WEEK_LENGTH);
        monthTable.push(week);
      }
      return monthTable;
    },

    // 显示上月打卡结果
    showPreviousMonth() {
      const previousStep = -1;
      this.triggerEvent('showAdjacent', previousStep);
    },

    // 显示下月打卡结果
    showNextMonth() {
      const nextStep = 1;
      this.triggerEvent('showAdjacent', nextStep);
    },

    toggleSubscribeMask(e) {
      // 开关订阅蒙尘
      this.triggerEvent('toggle-subscribe-mask', e.detail);
    },
  },
});
