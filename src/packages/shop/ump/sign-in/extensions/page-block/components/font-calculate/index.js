import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    amount: {
      type: Number,
    },
    amountUnit: {
      type: String,
    },
    type: {
      type: Number,
    },
    maxFontSize: {
      type: Number,
      default: 30,
    },
  },

  data: {
    fontsize: 12,
  },

  ready() {
    this.calculate();
  },

  methods: {
    calculate() {
      const { maxFontSize, type, amountUnit } = this.properties;
      const minFontSize = 12;
      let { length } = this.properties.amount.toString();
      let fontsize = minFontSize;
      if (type === 1) {
        // 最高x元的显示
        const fontsizeArray = [
          maxFontSize,
          maxFontSize,
          maxFontSize,
          20,
          17,
          14,
        ];
        fontsize =
          length > fontsizeArray.length
            ? minFontSize
            : fontsizeArray[length - 1];
      } else {
        const fontsizeArray = [
          maxFontSize,
          maxFontSize,
          maxFontSize,
          24,
          18,
          16,
          14,
        ];
        if (amountUnit === '万元') {
          length += 1;
        }

        fontsize =
          length > fontsizeArray.length
            ? minFontSize
            : fontsizeArray[length - 1];
      }

      this.setYZData({
        fontsize,
      });
    },
  },
});
