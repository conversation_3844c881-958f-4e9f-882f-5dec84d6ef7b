<view class="c-calendar__container">
  <view class="c-calendar__header">
    <view class="c-calendar__choose">
      <van-icon name="arrow-left" wx:if="{{ canJumpPrevious }}" class="c-header__arrow c-header__arrow--left" bindtap="showPreviousMonth" />
      <view class="l-general__text c-header__text">{{ year }}年{{ month }}月</view>
      <van-icon name="arrow"  wx:if="{{ canJumpNext }}" class="c-header__arrow c-header__arrow--right" bindtap="showNextMonth" />
    </view>
    <!-- 签到提醒 -->
    <reminder canReminderOpen="{{ canReminderOpen }}" themeColor="{{ themeColor }}" bind:toggle-subscribe-mask="toggleSubscribeMask" />
  </view>
  <view class="c-calendar__main">
    <view class="l-calendar__row">
      <view wx:for="{{ weeksMap }}" wx:key="index" class="l-general__text l-calendar__item">
        {{item}}
      </view>
    </view>
    <view wx:for="{{ monthTable }}" wx:for-item="week" wx:key="index" class="l-calendar__row">
      <view wx:for="{{ week }}" wx:key="index"
        class="l-calendar__item c-calendar__date {{ item.hasSign ? 'c-calendar__date--sign' : '' }}"
        style="{{ item.hasSign ? 'background:'+themeColor:'' }}">
        {{ item.value === 0 || item.hasSign ? '' : item.value }}
      </view>
    </view>
  </view>
</view>
