import navigate from 'shared/utils/navigate';
import Toast from '@vant/weapp/dist/toast/toast';
import args from '@youzan/weapp-utils/lib/args';
import { autoEnterShop } from 'common-api/multi-shop/multi-shop-redirect';
import { wscSubscribeMessage, SCENE } from '@/utils/subscribe-message';
import { checkPureWscSingleStore } from '@youzan/utils-shop';
import { RantaWidget } from 'shared/common/base/ranta/widget';

import {
  parseTime,
  calcYearAndMonth,
  CHECKINCONFIG,
  AWARDTYPE,
  COUPON,
  CHECKINAWARDTYPE,
} from '../../utils';

const app = getApp();
const URLMAP = {
  credit: '/packages/user/integral/index',
  coupon: '/packages/user/coupon/list/index?type=promocard&title=我的优惠券',
  bonus: '/packages/ump/presents/index', // 赠品重构中，重构完成后替换url
};

const DEFAULT_TITLE = '签到送好礼';

RantaWidget({
  data: {
    continuesDay: 0, // 累计签到时间
    checkInTitle: '签到赢好礼', // 签到活动头部文案
    checkInTimeDesc: '', // 签到活动日期
    showSignBtn: false, // 展示立即签到按钮
    showResult: false, // 查看签到结果
    showGift: false, // 查看礼包
    canReminderOpen: false, // 开启签到提醒按钮是否通过签到开启
    canSign: true, // 签到按钮是否可点击
    canJumpPrevious: true, // 查看前一个月
    canJumpNext: true, // 查看后一个月
    currentYear: new Date(Date.now()).getFullYear(), // 日历组件当前年份
    currentMonth: new Date(Date.now()).getMonth() + 1, // 日历组件当前月份
    checkInDates: [], // 实际签到日期列表 { year: '', month: '', date: '' }
    dailyRewards: [], // 日签奖励
    rewards: [], // 签到活动设置的礼品
    gifts: {}, // 礼包奖品
    deservedRewards: {}, // 当日签到应得奖励
    shareTitle: DEFAULT_TITLE, // 分享链接标题
    bannerImageUrl: 'https://img01.yzcdn.cn/wsc/sign/<EMAIL>',
    themeClass: app.themeClass,
    pageBgColor: '#f9f9f9',
    showRules: false,
    rules: [],
    rewardsAllWidth: 0,
    checkinFlag: false, // 标志位
    isCheckin: false, // 是否签到
    showReminder: false, // 商家设置是否展示签到提醒
    isReminderOpen: false, // 当前的签到提醒的开启状态
    showSubscirbeFail: false, // 小程序订阅消息失败引导弹窗
    canAutoSubscribe: true, // 是否能点击签到时自动弹起订阅消息
    showSubscribeMask: false, // 订阅消息蒙尘
    pageConfigs: {
      backgroundImage: CHECKINCONFIG.backgroundImage,
      themeColor: CHECKINCONFIG.themeColor,
      backgroundBtn: CHECKINCONFIG.backgroundBtn,
      dialogBackgroundColor: CHECKINCONFIG.dialogBackgroundColor,
    },
    awardType: AWARDTYPE,
    checkinAwardType: CHECKINAWARDTYPE,
    couponType: COUPON,
    openCycle: true, // 是否是循环周期
    preloading: false,
  },

  attached() {
    this.logicData = {};
    this.ctx.data.logicData = this.logicData;

    const { query = {} } = this.ctx.data;
    const { dbid } = this.ctx.data.query;

    // 如果从非首页进入，走进店逻辑
    const redirectUrl = args.add('/' + this.ctx.data.route, query);
    this.redirectUrl = redirectUrl;
    if (!dbid) {
      console.log('---init base---');

      autoEnterShop({
        ...query,
        umpAlias: '',
        umpType: 'checkin',
        redirectUrl,
      })
        .then(() => {
          this.initBase(query);
        })
        .catch((e) => {
          console.log('---errror', e);
        });
      return;
    }
    this.initBase(query);
  },

  methods: {
    initBase(query) {
      this.__initLogicData(query);
      this.initPageInfo();
      this.queryShareSetting();

      this.on('component:sku:cart', (res) => {
        if (res.type === 'add') {
          Toast('添加购物车成功');
        }
      });
    },

    // 初始化非视图层数据
    __initLogicData(query) {
      const { dbid } = query;
      this.logicData = {
        checkInId: '', // 签到活动id
        activityOwnerKdtId: 0, // 获取微页面组件的请求 kdtId
        isCheckin: false, // 是否已签到,默认没签到
        isOpen: true, // 活动是否开启，默认开启
        realYear: new Date().getFullYear(), // 真实年份，其他年月可查看签到记录，但不出现签到按钮
        realMonth: new Date().getMonth() + 1, // 真实月份
      };
      // 有可能是从签到弹窗跳转过来的，此时应得的奖励存在db里
      const dbData = app.db.get(dbid);
      app
        .getPointsName()
        .then(({ pointsName = '积分' }) => {
          this.pointsName = pointsName;
          dbData && this.hanldeResult(dbData.res);
        })
        .catch(() => {
          this.pointsName = '积分';
          dbData && this.hanldeResult(dbData.res);
        });
    },

    // 获取 checkInId
    getCheckinId() {
      return new Promise((resolve) => {
        const { checkInId } = this.logicData || {};
        if (checkInId) resolve();
        app
          .request({
            path: 'wscump/checkin/check-in-info.json',
          })
          .then((res) => {
            const { activityOwnerKdtId, checkInId } = res || {};
            if (activityOwnerKdtId) {
              this.logicData.activityOwnerKdtId = activityOwnerKdtId;
              this.getPageComponent();
            }
            if (checkInId) {
              this.logicData.checkInId = checkInId;
              this.ctx.data.checkInId = checkInId;
              resolve();
            }
          })
          .catch((err) => {
            console.error('签到弹窗接口', err);
          });
      });
    },

    // 初始化页面数据
    initPageInfo() {
      const haveBind = app.getMobile();
      this.setYZData({ haveBind });

      this.getCheckinId().then(() => {
        this.getActivityInfo();
      });
    },

    // 查看签到规则
    showSignRules() {
      const { checkInId } = this.logicData;
      wx.showLoading({
        title: '获取规则中',
      });
      app
        .request({
          path: 'wscump/checkin/get_rule.json',
          query: {
            checkin_id: checkInId,
          },
        })
        .then((res) => {
          wx.hideLoading();
          this.setYZData({
            showRules: true,
            rules: res.value.split('\n'),
          });
        })
        .catch((err) => {
          wx.hideLoading();
          wx.showToast({
            title: err.msg || '获取签到规则失败',
            icon: 'none',
            duration: 1000,
          });
        });
    },

    onAuthPopupShow() {
      this.setYZData({
        canAutoSubscribe: false,
      });
    },

    toggleSubscribeMask(e) {
      this.setYZData({
        showSubscribeMask: e.detail,
      });
    },

    // 绑定手机号成功后回调
    async signIn() {
      const self = this;
      const onComplete = () => {
        app
          .getPointsName()
          .then(({ pointsName = '积分' }) => {
            this.pointsName = pointsName;
            this.getActivityInfo(this.hasCheckin.bind(this));
          })
          .catch(() => {
            this.pointsName = '积分';
            this.getActivityInfo(this.hasCheckin.bind(this));
          });
      };
      const { shopMetaInfo = {} } = await app.getShopInfo();
      if (this.data.canAutoSubscribe && checkPureWscSingleStore(shopMetaInfo)) {
        wscSubscribeMessage({
          scene: SCENE.SIGN,
          subscribePage: '签到页',
          subscribeType: '签到提醒',
          authorizationType: 'sign',
          windowType: 'sign_message',
          options: {
            next: onComplete,
            onShowTips: () => {
              self.toggleSubscribeMask({ detail: true });
            },
            onCloseTips: () => {
              self.toggleSubscribeMask({ detail: false });
              onComplete();
            },
          },
        });
      } else {
        onComplete();
      }
    },

    // 签到前验证是否已签到，若未签到，才执行实际签到
    hasCheckin(res) {
      const { isCheckin } = res;
      const { checkInId } = this.logicData;

      this.setYZData({ isCheckin });

      if (isCheckin) {
        this.handleActivityInfo(res);
        wx.showToast({
          title: '你今天已经签过了',
          icon: 'none',
          duration: 1000,
        });
      } else {
        this.doCheckin(checkInId);
      }
    },

    resolveError(err) {
      wx.hideLoading();
      if (err.code === 160540418) {
        const url = this.redirectUrl;
        wx.showModal({
          title: '提示',
          content: '签到失败，请刷新后重试',
          showCancel: false,
          confirmText: '刷新页面',
          confirmColor: '#ff9c15',
          success(res) {
            if (res.confirm) {
              wx.reLaunch({
                url,
              });
            }
          },
        });
        return;
      }

      wx.showToast({
        title: err.msg || '签到失败',
        icon: 'none',
        duration: 1000,
      });
      this.setYZData({
        canSign: true,
      });
    },

    // 执行签到
    doCheckin(checkInId) {
      wx.showLoading({
        title: '签到中',
      });
      this.setYZData({
        canSign: false,
      });
      app
        .request({
          path: 'wscump/checkin/checkinV2.json',
          query: {
            checkinId: checkInId,
          },
        })
        .then((res) => {
          wx.hideLoading();
          this.hanldeResult(res);
        })
        .catch((err) => {
          this.resolveError(err);
        });
      // 埋点
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'sign_in',
          en: '立即签到',
          pt: 'signForGift',
        });
    },

    // 弹窗关闭的回调函数,复原签到标志位
    afterHide() {
      this.setYZData({
        checkinFlag: false,
      });
    },

    hanldeResult(result) {
      const { success } = result;

      if (success) {
        this.setYZData({
          deservedRewards: result,
        });
        this.refreshPageInfo();
        this.showSignResult();
      } else {
        this.setYZData({
          canSign: true,
        });
      }
    },

    // 签到结果弹出层
    showSignResult() {
      const { deservedRewards } = this.data;
      const MIN_REWARD_AMOUNT = 0;

      if (deservedRewards.list.length > MIN_REWARD_AMOUNT) {
        this.setYZData({
          showResult: true,
        });

        this.trackPrize();
        app.logger?.log({
          et: 'view',
          ei: 'view_popup',
          en: '弹窗曝光',
          pt: 'sign',
        });
      } else {
        wx.showToast({
          title: '签到成功',
          icon: 'success',
          duration: 1000,
        });
      }
    },

    trackPrize() {
      const { awardType } = this.data;
      const list = this.data.deservedRewards?.list || [];
      let logInfo = null;
      list.forEach((item) => {
        switch (item.type) {
          case awardType.coupon:
            logInfo = {
              et: 'view', // 事件类型
              ei: 'view_coupon', // 事件标识
              en: '优惠券组件曝光', // 事件名称
              pt: 'sign', // 页面类型
            };
            break;
          case awardType.credit:
            logInfo = {
              et: 'view', // 事件类型
              ei: 'view_jifen', // 事件标识
              en: '积分组件曝光', // 事件名称
              pt: 'sign', // 页面类型
            };
            break;
          case awardType.bonus:
            logInfo = {
              et: 'view', // 事件类型
              ei: 'view_gift', // 事件标识
              en: '赠品组件曝光', // 事件名称
              pt: 'sign', // 页面类型
            };
            break;
        }

        logInfo && app.logger?.log(logInfo);
      });
    },

    // 隐藏签到结果弹出层
    onTapHideResult() {
      const self = this;
      const { awardType } = self.data;
      const onComplete = () => {
        self.setYZData({
          showResult: false,
          canReminderOpen: true,
        });
        // 埋点
        app.logger?.log({
          et: 'click',
          ei: 'click_close',
          en: '关闭按钮点击',
          pt: 'sign',
        });
      };
      const list = self.data.deservedRewards?.list || [];
      const isCouponAward = list.reduce((pre, item) => {
        if (item.type === awardType.coupon) return true;
        return pre;
      }, false);
      if (isCouponAward) {
        wscSubscribeMessage({
          scene: SCENE.COUPON,
          subscribePage: '签到页',
          subscribeType: '发券',
          authorizationType: 'coupon',
          windowType: 'sign_gift',
          options: {
            next: onComplete,
            onShowTips: () => {
              self.toggleSubscribeMask({ detail: true });
            },
            onCloseTips: () => {
              self.toggleSubscribeMask({ detail: false });
              onComplete();
            },
          },
        });
      } else {
        onComplete();
      }
    },

    // 隐藏礼包弹出层
    onTapHideGift() {
      this.setYZData({
        showGift: false,
      });
    },

    // 隐藏规则弹出层
    onTapHideRules() {
      this.setYZData({
        showRules: false,
      });
    },

    // 获取签到活动信息
    getActivityInfo(successCb) {
      const { checkInId } = this.logicData;
      successCb = successCb || this.handleActivityInfo.bind(this);
      app
        .request({
          path: 'wscump/checkin/get_activity_by_yzuid_v2.json',
          query: {
            checkinId: checkInId,
          },
        })
        .then((res) => {
          successCb(res);
        })
        .catch((err) => {
          wx.showToast({
            title: err.msg || '签到信息获取失败',
            icon: 'none',
            duration: 1000,
          });
        });
    },

    // 处理签到活动信息
    handleActivityInfo(res) {
      // 获取用户签到
      this.getSignInfo();
      const {
        continuesDay,
        rewards,
        dailyRewards,
        isCheckin,
        isOpen,
        cycleTimes = 0,
        openCycle,
      } = res;
      let checkInTitle = '';
      if (!isOpen) {
        checkInTitle = '签到活动已结束';
      } else if (isCheckin) {
        checkInTitle = '今日已签到';
      } else if (dailyRewards.length > 0) {
        checkInTitle = '每日签到' + dailyRewards[0].desc;
      } else {
        checkInTitle = '签到赢好礼';
      }
      const showSignBtn = isOpen;
      const rewardsList = rewards.filter(
        (item) => +item.duration <= cycleTimes
      );
      const rewardsLength = rewardsList.reduce((prev, item) => {
        return prev + item.prize.length;
      }, 0);
      const rewardsAllWidth = 130 * rewardsLength + rewardsList.length * 30;
      this.setYZData({
        continuesDay,
        rewards: rewardsList,
        rewardsAllWidth,
        dailyRewards,
        checkInTitle,
        showSignBtn,
        isCheckin,
        openCycle,
      });
      this.logicData.isCheckin = isCheckin;
      this.logicData.isOpen = isOpen;
    },

    // 获取某月签到信息
    getSignInfo() {
      const { currentYear, currentMonth } = this.data;
      const { checkInId } = this.logicData;
      app
        .request({
          path: 'wscump/checkin/find_checkin_info_by_month.json',
          query: {
            checkin_id: checkInId,
            year: currentYear,
            month: currentMonth,
          },
        })
        .then((res) => {
          this.processCheckinCycle(res);
        })
        .catch((err) => {
          wx.showToast({
            title: err.msg || '签到信息获取失败',
            icon: 'none',
            duration: 1000,
          });
        });
    },

    // 设置签到活动页面数据
    processCheckinCycle(res) {
      const { startTime, endTime, checkInDates } = parseTime(res);
      const checkInTimeDesc =
        this.data.openCycle && startTime && endTime
          ? `${startTime} - ${endTime} `
          : '';
      this.setYZData({
        checkInTimeDesc,
        checkInDates,
      });
    },

    // 显示临近月份的签到情况,限制在leftEdge和rightEdge之间的月份
    showAdjacentMonth(step) {
      const LEFT_EDGE = 2010;
      const RIGHT_EDGE = 2050;
      const { currentYear, currentMonth } = this.data;
      const { isOpen } = this.logicData;
      const { realYear, realMonth } = this.logicData;
      const { year, month } = calcYearAndMonth(currentYear, currentMonth, step);
      if (year < LEFT_EDGE) {
        this.setYZData({
          canJumpPrevious: false,
        });
      } else if (year > RIGHT_EDGE) {
        this.setYZData({
          canJumpNext: false,
        });
      } else {
        this.setYZData(
          {
            currentYear: year,
            currentMonth: month,
            canJumpPrevious: true,
            canJumpNext: true,
            showSignBtn: isOpen && year === realYear && month === realMonth, // 未签到且当前月时才出现签到按钮
          },
          () => {
            this.getSignInfo();
          }
        );
      }
    },

    goToHome() {
      // 进店逛逛
      navigate.switchTab({
        url: '/packages/home/<USER>/index',
      });

      // 埋点
      app.logger?.log({
        et: 'click',
        ei: 'click_popup',
        en: '进店逛逛按钮点击',
        pt: 'sign',
      });
    },

    closePopupAndRedirect(url, isSwitchTab) {
      this.setYZData({
        showResult: false,
      });
      setTimeout(() => {
        if (isSwitchTab) {
          navigate.switchTab({
            url,
          });
        } else {
          navigate.navigate({
            url,
          });
        }
      }, 100);
    },

    // 查看领取到的奖品
    onTapNavigate(event) {
      const self = this;
      const { awardType } = this.data;
      const { type, infos } = event.currentTarget.dataset;

      // 优惠券-去使用
      if (type === awardType.coupon) {
        const onComplete = () => {
          app
            .request({
              path: '/wscump/coupon/coupon_use_redirect.json',
              query: {
                couponId: infos.fetchId,
                groupType: 'card',
                kdtId: app.getKdtId(),
              },
            })
            .then((res) => {
              self.closePopupAndRedirect(res.weappUrl, res.isSwitchTab);
            })
            .catch(() => {});
          app.logger?.log({
            et: 'click', // 事件类型
            ei: 'click_coupon', // 事件标识
            en: '优惠券组件点击', // 事件名称
            pt: 'sign', // 页面类型
          });
        };
        wscSubscribeMessage({
          scene: SCENE.COUPON,
          subscribePage: '签到页',
          subscribeType: '发券',
          authorizationType: 'coupon',
          windowType: 'sign_gift',
          options: {
            next: onComplete,
            onShowTips: () => {
              self.toggleSubscribeMask({ detail: true });
            },
            onCloseTips: () => {
              self.toggleSubscribeMask({ detail: false });
              onComplete();
            },
          },
        });
      } else if (type === awardType.bonus) {
        let url = '';
        // 赠品跳转
        if (infos.isKnowledge) {
          // 教育赠品
          url = URLMAP[type];
        } else {
          url = `/pages/goods/detail/index?alias=${infos.goodsAlias}&present=${infos.benefitAlias}`;
        }
        this.closePopupAndRedirect(url);

        app.logger?.log({
          et: 'click', // 事件类型
          ei: 'click_gift', // 事件标识
          en: '赠品组件点击', // 事件名称
          pt: 'sign', // 页面类型
        });
      } else if (type === awardType.credit) {
        const url = URLMAP[type];
        this.closePopupAndRedirect(url);

        app.logger?.log({
          et: 'click', // 事件类型
          ei: 'click_jifen', // 事件标识
          en: '积分组件点击', // 事件名称
          pt: 'sign', // 页面类型
        });
      }
    },

    // 刷新页面数据
    refreshPageInfo() {
      this.setYZData(
        {
          currentYear: new Date(Date.now()).getFullYear(),
          currentMonth: new Date(Date.now()).getMonth() + 1,
        },
        () => {
          this.initPageInfo();
        }
      );
    },

    initPageTheme(result) {
      // 获取主题色和图片
      if (result && result.length) {
        const {
          backgroundImage = this.data.pageConfigs.backgroundImage,
          themeColor = this.data.pageConfigs.themeColor,
        } = result[0];

        this.setYZData({
          pageConfigs: {
            backgroundImage,
            themeColor,
            dialogBackgroundColor: themeColor,
            backgroundBtn: themeColor,
          }
        });
      }
    },

    // 获取微页面组件配置数据
    getPageComponent() {
      const { activityOwnerKdtId } = this.logicData || {};
      // app
      //   .request({
      //     path: 'wscump/checkin/query_checkin_page.json',
      //     data: {
      //       adaptorComponents: adaptorComponents.join(','),
      //       kdt_id: activityOwnerKdtId,
      //     },
      //   })
      app
        .request({
          path: 'wscdeco/biz-component/list.json',
          query: {
            bizName: 'checkin',
            queryValues: encodeURIComponent(`${activityOwnerKdtId}`),
            stage: 100,
          },
        })
        .then((res) => {
          const { title, configContext } = res;

          let components = [];
          let checkinConfig = [];
          let config = {};
          try {
            components = res.components.filter(
              (elem) => elem.type !== 'checkin'
            );
            checkinConfig = res.components.filter(
              (elem) => elem.type === 'checkin'
            );
            config = JSON.parse(configContext);
          } catch (error) {
            console.log(error);
          }
          const pageBgColor = config.color;
          this.setYZData({
            pageBgColor,
          });
          // 没有设置的分享标题，就取页面标题
          if (this.data.shareTitle === DEFAULT_TITLE && title) {
            this.setYZData({
              shareTitle: title,
            });
            this.ctx.data.shareTitle = title;
          }
          // 主题色
          this.initPageTheme(checkinConfig);
          // 设置导航栏标题
          // wx.setNavigationBarTitle({
          //   title,
          // });
          this.ctx.data.pageTitle = title;
          this.setShowCase(components);
        })
        .catch((err) => {
          wx.showToast({
            title: err.msg || '微页面配置加载失败',
            icon: 'none',
            duration: 1000,
          });
        })
        .finally(() => {
          this.setYZData({
            preloading: true,
          });
        });
    },

    setShowCase(components) {
      if (!Array.isArray(components) || components.length === 0) return;
      // 以微页面形式加载商品详情
      setTimeout(() => {
        // this.showShowcaseComponents(components, 3);
        console.log('set show case', components);

        this.ctx.data.featureComponents = components;
      }, 500);
    },

    queryShareSetting() {
      app
        .request({
          path: 'wscump/checkin/query_share_setting.json',
        })
        .then((res) => {
          if (res) {
            const { shareTitle, bannerImageUrl } = res;
            if (shareTitle) {
              this.setYZData({
                shareTitle,
              });
              this.ctx.data.shareTitle = shareTitle;
            }
            if (bannerImageUrl) {
              this.setYZData({
                bannerImageUrl,
              });
              this.ctx.data.bannerImageUrl = bannerImageUrl;
            }
          }
        });
    },

    onTapPrizes(e) {
      const item = e.currentTarget.dataset.reward || {};
      const { sendFailInfo = '' } = item;
      if (!sendFailInfo) return;
      wx.showToast({
        title: sendFailInfo,
        icon: 'none',
        duration: 1000,
      });
    },
  },
});
