<van-notify id="van-notify" />
<subscribe-guide show="{{ showSubscribeMask }}" bind:close="toggleSubscribeMask" zIndex="{{ 10010 }}" />

<view class="l-sign__container {{ rewards.length?'container-center':''}}"
  style="background-image:url({{pageConfigs.backgroundImage}})" wx:if="{{ preloading }}">
  <!-- 签到页header -->
  <view class="c-sign__header">
    <view class="c-header__reward">{{checkInTitle}}</view>
    <view class="c-header__duration">{{ openCycle ? '当前周期' : '' }}已连续签到{{continuesDay}}天</view>
    <view class="c-header__rule" bindtap="showSignRules">签到玩法</view>
  </view>

  <!-- 签到日历 -->
  <view class="c-sign__info">
    <sign-calendar canReminderOpen="{{canReminderOpen}}" canJumpPrevious="{{canJumpPrevious}}"
      canJumpNext="{{canJumpNext}}" class="c-sign__calendar" sign-dates="{{checkInDates}}" year="{{currentYear}}"
      month="{{currentMonth}}" bind:changeReminderOpen='onChangeReminderOpen' themeColor="{{pageConfigs.themeColor}}"
      bind:showAdjacent="showAdjacentMonth" bind:toggle-subscribe-mask="toggleSubscribeMask">
    </sign-calendar>
    <form-view wx:if="{{ showSignBtn}}">
      <user-authorize wx:if="{{ canSign }}" bind:next="signIn" authTypeList="{{ ['mobile'] }}"
        bind:auth-popup-show="onAuthPopupShow">
        <button class="l-general__btn c-sign__btn" style="background:{{ pageConfigs.backgroundBtn }}"
          disabled="{{ isCheckin }}">
          {{ isCheckin ? '今日已签到' : '立即签到' }}
        </button>
      </user-authorize>
      <button wx:else class="l-general__btn c-sign__btn" disabled="{{ true }}"
        style="background:{{ pageConfigs.backgroundBtn }}" bindtap="signIn">
        {{ isCheckin ? '今日已签到' : '立即签到' }}
      </button>
    </form-view>
  </view>

  <!-- 签到奖励 -->
  <view class="c-sign__reward" wx:if="{{rewards.length !== 0}}">
    <view class="c-reward__time">{{checkInTimeDesc}}连续签到可领取以下奖励:</view>
    <scroll-view scroll-x>
      <view class="l-reward__list" style="width: {{ rewardsAllWidth }}px;">
        <view class="c-reward__item {{ item.fetched ? 'c-reward__item--fetched' : '' }}" wx:for="{{ rewards }}"
          wx:key="duration" data-reward="{{ item }}" bindtap="onTapPrizes">
          <view class="c-item__duration">连续{{ item.duration }}天</view>
          <view class="l-award__list">
            <view class="l-award__item" wx:for="{{ item.prize }}" wx:for-item="record">
              <view class="l-award__item--content" wx:if="{{record.type === awardType.bonus}}">{{ record.desc.right }}
              </view>
              <view class="l-award__item--text" wx:else>
                <view class="l-award__item--credit">
                  {{ record.desc.left }}
                  <view class="l-award__item--amount {{ record.desc.middle >= 1000 ? 'l-award__amount' : '' }}">{{
                    record.desc.middle
                    }}</view>
                  {{ record.desc.right }}
                </view>
              </view>
              <image mode="widthFix"
                class="{{ record.type === awardType.coupon ? 'l-award__small' : 'l-award__item--picture' }}"
                src="{{record.desc.picture}}" alt=""></image>
            </view>
          </view>
          <image class="l-award__icon" wx:if="{{item.receivedMark && !item.sendFailInfo}}"
            src="https://img01.yzcdn.cn/upload_files/2021/10/22/Fs_tt1Tvlcla4iF6G5zIdhucq_Vr.png"></image>
        </view>
    </scroll-view>
  </view>
</view>

<!-- 签到结果弹出层 -->
<van-popup class="checkin-popup" show="{{ showResult }}" custom-style="width: 324px; background-color: rgba(0,0,0,0); overflow-y: initial;">
  <view class="c-result__container l-general__container" style="background: {{ pageConfigs.dialogBackgroundColor }}">
    <view class="c-popup__header">
      <form-view>
        <view class="c-result__close-icon" bindtap="onTapHideResult"></view>
      </form-view>
      <view class="c-result__title"></view>
      <view class="c-result__subtitle">{{ deservedRewards.desc }}</view>
    </view>
    <view class="c-popup__main">
      <!-- 奖励 start-->
      <view
        class="coupon-list {{ deservedRewards.awardType === checkinAwardType.noneAward  ? 'coupon-list__noaward' : '' }}">
        <view
          class="coupon-item {{ (deservedRewards.awardType !== checkinAwardType.noneAward && !item.isSuccess) ? 'coupon-item__prize-fail' : '' }}"
          wx:for="{{ deservedRewards.list }}" wx:for-item="item">
          <view class="coupon-item__value">
            <view class="coupon-detail" wx:if="{{ item.type === awardType.coupon }}">
              <view class="coupon-value">
                <block wx:if="{{ item.infos.type === couponType.voucher }}">
                  <view class="coupon-goods-value">{{ item.infos.copyWriting }}</view>
                </block>
                <block wx:elif="{{ item.infos.prefixWriting }}">
                  <theme-view color="general">
                    <view class="unit">{{ item.infos.prefixWriting }}</view>
                    <font-calculate amount="{{ item.infos.amount }}" type="1" max-font-size="24">
                      {{ item.infos.amount }}
                    </font-calculate>
                    <view class="unit">{{ item.infos.copyWriting }}</view>
                  </theme-view>
                </block>
                <block wx:else>
                  <theme-view color="general">
                    <font-calculate amount="{{ item.infos.amount }}" type="2" max-font-size="30"
                      amount-unit="{{ item.infos.copyWriting }}">
                      {{ item.infos.amount }}
                    </font-calculate>
                    <view class="unit">{{ item.infos.copyWriting }}</view>
                  </theme-view>
                </block>
              </view>
            </view>
            <image src="{{ item.infos.picture }}" alt="" class="coupon-condition" wx:else></image>
          </view>
          <view class="coupon-item__content">
            <view class="coupon-title">{{ item.infos.title }}</view>
            <view class="coupon-time">{{ item.infos.desc }}</view>
          </view>
          <theme-view bg="main-bg" color="main-text" custom-class="coupon-item__btn"
            wx:if="{{ deservedRewards.awardType !== checkinAwardType.noneAward && item.isSuccess }}">
            <view data-type="{{ item.type }}" data-infos="{{ item.infos }}" bindtap="onTapNavigate">{{
              item.action }}
            </view>
          </theme-view>
        </view>
      </view>
      <!-- 奖励 end -->
    </view>
    <image wx:if="{{ deservedRewards.isGoHome}}" bindtap="goToHome" class="c-home-button"
      src="https://img01.yzcdn.cn/upload_files/2021/10/19/FhhNmFKa8LwGsz_KzDRZSCHTTZal.png" alt="进店逛逛"></image>
  </view>
  <image class="c-result-backages" src="https://img01.yzcdn.cn/upload_files/2021/10/11/FgQX1wlMF5v6i-b-bYWXihI6c7GA.png"
    alt="" bindtap="onTapHideResult"></image>
</van-popup>

<!-- 规则弹出层 -->
<van-popup show="{{ showRules }}" bind:close="onTapHideRules" safe-area-inset-bottom position="bottom" closeable
  custom-class="c-rules__popup">
  <view class="c-rules__container">
    <view class="c-rules__title">签到规则</view>
    <view class="c-rules__message">
      <view class="c-rules__message__item" wx:for="{{rules}}" wx:for-item="rule">{{ rule }}</view>
    </view>
    <button class="c-rules__btn" bindtap="onTapHideRules" style="background:{{ pageConfigs.backgroundBtn }}">
      知道了
    </button>
  </view>
</van-popup>

<view style="background-color: {{pageBgColor}}">
  <showcase-container />
</view>

<cps-goods-recommend cpsConfigKey="cps_goods_recommend_checkin" />
<recommend-goods biz-name="cs~mg"></recommend-goods>
<van-toast id="van-toast" />

<inject-protocol noAutoAuth />