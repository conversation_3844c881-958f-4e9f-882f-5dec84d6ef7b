import { mergeStore } from './utils/helper';

import imageBlockStore from '@/packages/shop/salesman/promote/containers/image-block/store';
import baseInfoStore from '@/packages/shop/salesman/promote/containers/base-info-block/store';
import couponBlock from '@/packages/shop/salesman/promote/containers/coupon-block/store';
import goodsDetailStore from '@/packages/shop/salesman/promote/containers/goods-detail-block/store';
import taskAwardStore from '@/packages/shop/salesman/promote/containers/task-award-block/store';
import submitStore from '@/packages/shop/salesman/promote/containers/submit-block/store';
import tradeCarousel from '@/packages/shop/salesman/promote/containers/trade-carousel/store';

const stores = [
  imageBlockStore,
  baseInfoStore,
  couponBlock,
  goodsDetailStore,
  taskAwardStore,
  submitStore,
  tradeCarousel
];

export default stores.reduce((a, b) => mergeStore(a, b), {});
