import {
  get,
  omit,
  fullfillImage,
  getCdnImageUrl,
  getRedirectUrl,
  mapKeysToCamelCase,
  getDetailPath,
  getRateProfit,
} from '@/packages/shop/salesman/promote/utils';
import {
  formatSkuData,
  formatSkuPrice,
  updateAsyncSku,
  parseGoodsExtendInfo,
} from './helper';
import formatCommission from '@youzan/salesman-cube-core-utils/lib/formatCommission';
// 根据用户session得到的信息
function formatUserData(state, payload) {
  // 下单配置
  Object.assign(state.buyConfig, {
    // 是否公众号粉丝
    isFans: payload.isFans,
    // 是否必须关注购买
    noFansBuy: !payload.isFansBuy,
  });
}

function formatActivityCommissionInfo(state, payload = {}) {
  const { maxPrice } = payload;
  const { commissionInfo } = state;

  const {
    // 推广佣金比例 0.1 -> 10%
    cpsRate,
    nextLevelCpsRate,
  } = commissionInfo;

  if (cpsRate && maxPrice) {
    const profit = getRateProfit(cpsRate, maxPrice);
    payload = { ...payload, profit };
  }
  if (nextLevelCpsRate && maxPrice) {
    const nextLevelProfit = getRateProfit(nextLevelCpsRate, maxPrice);
    payload = { ...payload, nextLevelProfit };
  }
  return payload;
}

function formatCommissionInfo(state, payload = {}) {
  const {
    profitRange,
    nextProfitRange,
    commissionSendType,
    commissionConfigDTO,
    customPointsName,
  } = payload;

  if (profitRange && profitRange.length) {
    const { maxCommissionStr } = formatCommission({
      profitRange,
      commissionSendType,
      commissionConfigDTO,
      customPointsName,
    });
    payload = { ...payload, profit: maxCommissionStr };
  }
  if (nextProfitRange && nextProfitRange.length) {
    const { maxCommissionStr } = formatCommission({
      profitRange: nextProfitRange,
      commissionSendType,
      commissionConfigDTO,
      customPointsName,
    });
    payload = { ...payload, nextLevelProfit: maxCommissionStr };
  }
  return payload;
}

function formatDisplayData(state, payload) {
  // 环境变量
  Object.assign(state.env, {
    // 是否是新希望店铺
    isNewHopeKdt: payload.isNewHopeKdt,
    // 是否是全局购物车
    isAllCart: payload.isAllCart,
    // 是否隐藏底部
    hideGoodsBottom: payload.hideGoodsBottom,
  });
}

function formatGoodsStaticData(state, payload = {}) {
  const { pageParams = {}, goods: goodsData = {}, shopMetaInfo } = payload;
  const {
    // 商品类型
    type = '',
    // 商品
    goods = {},
    // 店铺
    shop = {},
    // 店铺设置
    shopConfig = {},
    // 优惠券列表
    voucherModelList = [],
    // sku
    skuInfo: staticSkuInfo,
    // 商品扩展信息
    extendModelList,
  } = goodsData;

  const originSku = formatSkuData(staticSkuInfo);
  const originNormalSku = { ...originSku };
  const goodsExtendInfo = parseGoodsExtendInfo(extendModelList);
  // SKU数据结构
  Object.assign(state, {
    originSku,
    originNormalSku,
    staticSkuInfo,
    goodsExtendInfo,
  });

  Object.assign(state, {
    // 店铺信息
    shop: {
      kdtId: shop.kdtId,
      logo: shop.logo,
      name: shop.shopName,
      city: shop.city || '',
      province: shop.province || '',
      address: shop.address || '',
      rootKdtId: shopMetaInfo.rootKdtId,
      shopMetaInfo,
      phone: '',
      // 7表示零售店铺
      shopType: shop.shopType || '',
      // 是否是总店预览模式，通过query和连锁模式判断
      isHqShopPreview: shop.isHqShopPreview,
    },
  });

  // 视频
  const videoConfig = get(goods, 'video', {});
  Object.assign(state.video, {
    width: +videoConfig.coverWidth,
    height: +videoConfig.coverHeight,
    coverUrl: videoConfig.coverUrl,
    videoUrl: videoConfig.videoUrl || '',
    // 统计播放
    countPlayedUrl: videoConfig.countPlayedUrl || '',
  });

  const { pictures } = goods;
  if (videoConfig.coverUrl) {
    pictures.unshift({
      url: videoConfig.coverUrl,
    });
  }

  // 主图信息
  Object.assign(state.picture, {
    pictures: pictures || [],
    // 服务端评估图片高度
    height: goods.pictureHeight,
  });

  const { showPrice, showOldPrice } = formatSkuPrice(originNormalSku);
  const {
    umpAlias = '',
    umpType = '',
    activityId = '',
    activityType = '',
  } = mapKeysToCamelCase(pageParams);
  const query = omit(pageParams, [
    'ump_alias',
    'ump_type',
    'access_token',
    'app_id',
  ]);

  const path = getDetailPath({
    ...query,
    umpType,
    umpAlias,
    activityId,
    activityType,
    alias: goods.alias,
  });

  Object.assign(state.goods, {
    type,
    id: goods.id,
    alias: goods.alias,
    title: goods.title,
    subTitle: goods.subTitle,
    // 小程序路径
    path,
    // 商品默认头图，sku弹层展示用
    picture: fullfillImage(get(goods, 'pictures.0.url', '')),
    // 商品卖点，页面子标题内容
    sellPoint: goods.sellPoint || '',
    origin: goods.origin,

    // 是否上架
    isDisplay: goods.isDisplay,
    // 是否是外链商品 购买方式 0：外部购买商品 1：非外部购买商品
    isOutlink: goods.buyWay === 0,
    // 商品外链
    outlinkUrl: getRedirectUrl(goods.buyUrl),
    // 是否是虚拟商品，包含 虚拟商品和电子卡券
    isVirtual: goods.isVirtual,
    // 是否是优惠券
    isVirtualCoupon: goods.isVirtualCoupon,
    // 海淘
    isHaitao: goods.isHaitao,
    // 餐饮
    isCatering: goods.isCatering,
    // 酒店
    isHotel: goods.isHotel,
    // 店铺风险提示
    risk: goods.risk,

    limit: {
      // 商品限购数
      quota: goods.quota,
      // 已购买数 异步数据给
      quotaUsed: 0, // goods.quotaUsed,
      singleQuota: goods.isHaitao || goods.isVirtualCoupon ? 100 : 0, // 海淘/电子卡券/优惠券每单限购 100
      // 限购周期
      quotaCycle: goods.quotaCycle,
      // 起售数量
      startSaleNum: goods.startSaleNum || 1,
    },

    // 销量
    soldNum: goods.soldNum,
    // 库存
    stockNum: originNormalSku.stockNum,
    hideStock: originNormalSku.hideStock,
    // 显示价格
    price: showPrice,
    oldPrice: showOldPrice,
    minPrice: originNormalSku.minPrice,
    maxPrice: originNormalSku.maxPrice,
    oldMinPrice: 0,
    oldMaxPrice: 0,
    itemSalePropList: goods.itemSalePropList || [],
    createdType: goods.createdType,
    // 商品是否支持七天无理由退换货
    supportUnconditionalReturn: goods.supportUnconditionalReturn,
    // 换货
    barterModel: goods.barterModel,
  });

  // 店铺信息
  Object.assign(state.shop, {
    kdtId: shop.kdtId,
    phone: shop.phone,
  });

  // 店铺配置
  Object.assign(state.shopConfig, {
    // 是否有赞担保
    isYouzanSecured: shopConfig.isYouzanSecured,
    // 是否加入担保交易
    isSecuredTransactions: shopConfig.isSecuredTransactions,
    // 是否开启销量与成交记录
    showBuyRecord: shopConfig.buyRecord,
    // 主体认证类型 0 初始化状态 1 个人认证，2企业认证，3 个体工商户 4其他组织，5政府及事业单位  98网店 99认证被驳回
    principalCertType: shopConfig.principalCertType || 0,
    // 品牌认证类型 0 初始化状态 1 旗舰店 2专卖店 3直营店 4 专营店 99 认证被驳回
    brandCertType: shopConfig.brandCertType || 0,
    // 成交记录设置 列表0， 悬浮窗1
    goodsDetailBuyRecord: shopConfig.goodsDetailBuyRecord || '',
    // 销售数量设置 // { show: 0 // 商详页销量是否展示：0-不展示，1-展示 limit: false,// 销量展示时是否限定值 limit_num:'',// 销量展示的限定值，大于0 }
    goodsDetailSales: shopConfig.goodsDetailSales || null,
    // 立即购买按钮自定义文案设置
    buyBtnConfig: shopConfig.buyBtnConfig || null,
    // 是否开启微信担保
    weixinTransactionSecured: shopConfig.weixinTransactionSecured,
  });

  // 店铺担保配置
  const youzanGuaranteeModel = get(goods, 'youzanGuaranteeModel', {});
  Object.assign(state.guarantee, {
    // 是否加入有赞担保
    on: youzanGuaranteeModel.youzanGuarantee || 0,
    // 担保样式
    style: shopConfig.guaranteeShowStyleType,
    // 担保详细文案
    desc: youzanGuaranteeModel.guaranteedComponents || [],
  });

  // 商品tab栏数据
  const tplStyle = goods.itemDetailTplStyle || 0;
  Object.assign(state.goodsDetail, {
    // 默认激活的详情tab，小程序目前没有这个需求
    defaultType: 'goods',
    // 模板风格 0：普通版 1：极速版
    tplStyle,
  });

  // 优惠券列表
  if (Array.isArray(voucherModelList)) {
    state.umpCouponList = voucherModelList.map((voucher) => {
      voucher.disabled = false;
      return voucher;
    });
  }
}

// 活动价数据得到后，需要更新商品信息，如价格等
function updateGoodsActivityInfo(state = {}, activity = {}) {
  const { minPrice, maxPrice } = state.goods;
  const { showPrice, showOldPrice } = formatSkuPrice({
    ...activity,
    ...{ oldMinPrice: minPrice, oldMaxPrice: maxPrice },
  });

  state.goods = {
    ...state.goods,
    price: showPrice,
    minPrice: activity.minPrice,
    maxPrice: activity.maxPrice,
    oldPrice: showOldPrice,
    oldMinPrice: minPrice,
    oldMaxPrice: maxPrice,
  };
}

// 动态数据接口
function formatGoodsAsyncData(state, payload = {}) {
  const {
    // template对象
    pageFeature = {},
    // 异步的商品对象
    goodsExtra = {},
    soldNum = null,
    skuStocks,
    spuStock,
    goodsActivityInfo = {},
  } = payload;

  // 更新异步sku新数据
  updateAsyncSku(state, spuStock, skuStocks);

  state.fetchSuccess = true;

  const { hasPurchaseRight = true, quotaUsed = 0 } = goodsExtra;

  Object.assign(state.goods, {
    // 是否仅限特定会员购买
    limitBuy: !hasPurchaseRight,
    // 异步销量字段更准确
    soldNum: soldNum === null ? state.goods.soldNum : soldNum,
  });
  // 用户已购数量
  Object.assign(state.goods.limit, {
    quotaUsed,
  });

  // 弹层、按钮、类目等展示控制
  // 不可购买原因
  Object.assign(state.display, {
    // 禁止购买原因
    forbidBuyReason: pageFeature.forbidBuyReason,
    // 是否展示购买按钮
    showBuyBtn: pageFeature.showBuyButton,
    // 是否展示不可购买按钮，包含 下架、售罄等
    showForbidBuyBtn: pageFeature.showForbidBuyButton,
    // 是否展示店铺按钮
    showMiniShopBtn: pageFeature.showMiniShopButton,
    // 是否展示分享按钮
    showShareBtn: pageFeature.showShareButton,
  });

  /* --------------------------
   * 活动信息
   --------------------------- */
  if (goodsActivityInfo.activityName) {
    updateGoodsActivityInfo(state, goodsActivityInfo);
  }

  if (!state.originActivitySku) {
    // 如果不存在activitySku，用普通sku替代
    Object.assign(state, {
      originActivitySku: state.originNormalSku,
    });
  }
}

// 规格条需要显示已选商品及商品图片
function formatGoodsSkuBarData(state) {
  const goodsImage = state.goods.picture;
  const { itemSalePropList = [] } = state.goods;
  const { tree = [] } = state.originNormalSku;

  // 规格图片列表
  let barPictures = [];
  let barPictureName = '';
  const firstSkuTree = tree[0] || {};
  if (get(firstSkuTree, 'v.0.imgUrl', '')) {
    barPictures = firstSkuTree.v.map((value) => {
      return getCdnImageUrl(fullfillImage(value.imgUrl || goodsImage));
    });
    barPictureName = firstSkuTree.k;
  }
  const barPictureCount = barPictures.length;

  // 选择规格
  const selectedSkuTree = tree.map((item) => ({
    id: item.kId,
    name: item.k,
    value: null,
  }));

  // 商品属性
  const selectedPropList = itemSalePropList.map((item) => ({
    id: item.kId,
    name: item.k,
    value: [],
  }));

  state.skuExtraData = {
    ...state.skuExtraData,
    // 是否显示规格栏
    showSkuBar: selectedSkuTree.length + selectedPropList.length > 0,
    // 规格栏图片
    barPictures: barPictures.slice(0, 5),
    // 共N种xx可选
    barPictureName,
    barPictureCount,
    // 已选规格
    selectedSkuTree,
    // 已选商品属性
    selectedPropList,
  };
}

export {
  formatCommissionInfo,
  formatUserData,
  formatDisplayData,
  formatGoodsStaticData,
  formatGoodsAsyncData,
  formatSkuData,
  formatGoodsSkuBarData,
  formatActivityCommissionInfo,
};
