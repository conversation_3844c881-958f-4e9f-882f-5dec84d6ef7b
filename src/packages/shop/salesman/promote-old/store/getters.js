import { get, args, getActivityInfo } from '@/packages/shop/salesman/promote/utils';
import { getUrl, HOME } from '@/packages/shop/salesman/promote/common/page-path';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';

const getters = {
  kdtId(state) {
    return get(state, 'shop.kdtId', 0);
  },

  alias(state) {
    return get(state, 'goods.alias', '');
  },

  itemId(state) {
    return get(state, 'goods.id', 0);
  },

  sl(state) {
    return get(state, 'salesman.seller', 0);
  },

  goodsActivityInfo(state) {
    return get(state, 'goodsActivityInfo', {});
  },

  platform(state) {
    return get(state, 'env.platform', '');
  },

  isSupportWish() {
    return false;
  },

  version(state) {
    return get(state, 'env.version', '');
  },

  skuId(state, getters) {
    const { skuData } = getters;
    const { skuExtraData } = state;

    if (skuData.noneSku) {
      return skuData.collectionId;
    }

    if (skuExtraData.selectedSkuComb) {
      return skuExtraData.selectedSkuComb.id;
    }

    return null;
  },

  customStock() {
    return false;
  },

  // 店铺首页
  homepageUrl(state, getters) {
    return getUrl(HOME, { kdt_id: getters.kdtId });
  },

  // 原始图片
  originGoodsPictures(state) {
    const {
      picture: { pictures },
    } = state;

    return pictures || [];
  },

  // 用于活动页面重载
  currentActivity(state, getters) {
    if (getters.isTimelimitedDiscount) {
      return getters.timelimitedDiscountActivity;
    }
    if (getters.isPresale) {
      return getters.presaleActivity;
    }
    return {};
  },

  // activity-banner / goods-info会用到
  // 倒计时模块
  countdown(state, getters) {
    const { timelimitedDiscountCountdown } = getters;
    if (timelimitedDiscountCountdown) {
      return timelimitedDiscountCountdown;
    }
    return null;
  },

  // Sku相关，多页面逻辑复用，且可能逻辑追加
  // 数据
  skuData(state, getters) {
    const sku = state.skuData || {};
    // 如果定金实物预售
    if (getters.isDepositPresale && !getters.isDepositPresaleOver) {
      return getters.depositPresaleSkuData;
    }

    if (getters.isFCode) {
      return state.originActivitySku;
    }

    return sku;
  },

  // 是否是多网点
  isMultiStore(state) {
    return get(state, 'multistore.id', 0) > 0;
  },

  // 是否是普通版商品详情
  isNormalTplStyle() {
    // 小程序内，不支持商品详情极速版
    return true;
    // return state.goodsDetail.tplStyle === 0;
  },

  // 是否售罄
  isSellOut(state) {
    return state.goods.stockNum <= 0;
  },

  // 链接上需要带ump信息才能展示营销信息，如秒杀、砍价0元购、0元抽奖团，现在主要用于商品属性价格计算
  activityInfoForPrice() {
    return {};
  },

  shareWxData(state) {
    const { salesman, goods, activityInfo, shareCardInfo } = state;
    const { activityAlias, activityType, activityId, umpType } = activityInfo;
    const { title, alias, picture } = goods;
    const { shareTitle, imgUrl, alg = '', share_from = '' } = shareCardInfo;
    const activityData = getActivityInfo({
      activityAlias,
      activityType,
      activityId,
      umpType,
    });
    const params = {
      alias,
      ...activityData,
    };

    const wxShareData = {
      title: shareTitle || title,
      path: args.add('pages/goods/detail/index', {
        ...params,
        alg,
        share_from,
      }),
    };
    params.timestamp = +new Date();
    // 小程序分享卡片图片
    wxShareData.imageUrl = imgUrl || picture;

    // 统一收尾 sl 参数
    if (salesman && salesman.seller) {
      wxShareData.path = addSalesmanParams({
        url: wxShareData.path,
        sl: state.salesman.seller,
      });
    }

    return wxShareData;
  },
  // 跳转商详参数
  jumpGoodsDetailQuery(state, getters) {
    const {
      activityAlias,
      activityType,
      activityId,
      umpType,
    } = state.activityInfo;
    const { alias } = getters;
    const activityData = getActivityInfo({
      activityAlias,
      activityType,
      activityId,
      umpType,
    });
    const params = {
      alias,
      fromType: 'sp',
      ...activityData,
    };
    return params;
  },
};

export default getters;
