import api from '@/packages/shop/salesman/promote/utils/api';
import { get, getActivityInfo } from '@/packages/shop/salesman/promote/utils';
import Toast from '@vant/weapp/dist/toast/toast';
import { setLogger } from '@/packages/shop/salesman/promote/common/logger';
import { setPlatfromSpecialUI } from '@/packages/shop/salesman/promote/common/platform';
import {
  getGoodsRecommendRateInfo,
  getCouponsAndActivityInfo,
  getWeappCard,
} from '@/packages/shop/salesman/promote/api';
import { DEFAULT_DESIGN } from '../constants/design';

const actions = {
  fetchGoodsData({ dispatch }, query) {
    return api
      .get({
        url: '/wscgoods/weapp/detail-v2.json',
        query: {
          ...query,
          notQueryVoucher: 1, // 不再查询优惠券列表
          notQueryYouzanGuarantee: true, // 小程序 2.67版本后的默认传true(默认不查询有赞担保信息)
          withSeckillError: true, // 为了兼容 2.49 版本错误，只有在有 withSeckillError 这个参数的情况下才会在检查到店铺不支持秒杀
        },
        config: { noStoreId: true },
      })
      .then((res) => {
        const { goods } = res.goodsData;

        if (goods.buyWay === 0) {
          return Promise.reject({
            message: '外链商品不支持展示',
            NO_TRY: true,
          });
        }

        // 没用的参数
        delete res.pageParams.withSeckillError;

        dispatch('updateGoodsStaticData', { ...res, design: DEFAULT_DESIGN });
        dispatch('getImgUrl');
        return res;
      });
  },

  setItemId({ commit }, itemId) {
    commit('SET_ITEM_ID', itemId);
  },

  setActivityInfo({ commit }, data) {
    commit('updateActivityInfo', data);
  },

  // 初始化商品 - 基础商品信息，为接口拆分做准备
  updateGoodsStaticData(
    { commit },
    {
      goodsData,
      pageParams,
      design,
      displayData,
      apolloSwitch,
      offlineData,
      shopMetaInfo,
      platformSpecialUI,
    }
  ) {
    commit('initStaticData', {
      goodsData,
      pageParams,
      offlineData,
      shopMetaInfo,
    });
    commit('SET_PAGE_SWITCH', apolloSwitch);
    commit('updatePageDesign', design);
    commit('initDisplayData', displayData);
    setPlatfromSpecialUI(platformSpecialUI);
  },

  // 补充增量商品信息，如活动信息
  updateGoodsAsyncData({ commit, dispatch }, userState) {
    // 获取异步数据
    dispatch('updateCouponsAndActivityInfo', userState);

    const clientTimestamp = +new Date();
    const { serverTimestamp = +new Date() } = userState;
    commit('updateServerTime', { serverTimestamp, clientTimestamp });
  },

  updateCouponsAndActivityInfo(
    { commit, getters },
    { userState, activityData }
  ) {
    const { itemId } = getters;
    getCouponsAndActivityInfo({ itemId, ...activityData })
      .then((res) => {
        const { activityInfo, couponsInfo } = res || {};
        // 商品活动处理
        commit('updateGoodsActivityInfo', activityInfo);
        commit('updateActivityProfitCommissionInfo', activityInfo);

        // 优惠券数据处理
        commit('updateGoodsCouponsInfo', couponsInfo);
        const couponList = get(couponsInfo, 'couponList', []);
        const optimismCoupon = get(couponsInfo, 'optimismCoupon', null);
        if (couponList.length || optimismCoupon) {
          commit('updateSalesmanActivityTags', { text: '可发券' });
        }

        commit('updateGoodsAsyncData', {
          ...userState,
          goodsActivityInfo: activityInfo,
        });
      })
      .catch((_e) => {
        console.error(_e);
      });
  },

  reload() {
    wx.startPullDownRefresh();
  },

  setSalesmanData({ commit }, salesman) {
    commit('setSalesmanData', salesman);
  },

  // 获取商品佣金等级信息
  getGoodsRecommendRateLevelInfo({ getters, commit }) {
    const { itemId } = getters;
    return getGoodsRecommendRateInfo({ itemId })
      .then((res) => {
        commit('updateCommissionInfo', res);
        return res;
      })
      .catch((e) => {
        console.error(e);
      });
  },

  // // 初始化页面状态
  initPageState({ getters, dispatch }) {
    // 判断是否展示零售店铺信息
    if (getters.isRetailShop) {
      dispatch('getRetailShopInfo');
    }
  },

  // // 倒计时结束
  countdownEnded({ dispatch }) {
    dispatch('reload');
  },

  // 获取导航项距离顶部值
  getNavItemTop({ getters, commit }, payload = {}) {
    const { scrollTop = 0, self } = payload;
    const navSelectorList = getters.navTabList.map(
      (it) => `.page-goods >>> #js-nav-${it.selector}`
    );
    const query = self.createSelectorQuery();
    const navItemTopList = [];
    query
      .selectAll(navSelectorList.join(','))
      .boundingClientRect((rect) => {
        if (!rect) return;
        rect.reduce((pre, cur) => {
          if (cur.id && cur.id !== pre.id) {
            navItemTopList.push(cur.top + scrollTop);
          }
          return cur;
        }, {});
        commit('setNavItemTopList', navItemTopList);
      })
      .exec();
  },

  updateAccountInfo({ commit }, payload = {}) {
    commit('updateAccountInfo', payload);
  },

  // // 显示/隐藏遮罩
  toggleGoodsMask({ commit }, showMask = false) {
    commit('toggleGoodsMask', showMask);
  },

  showCommonToast({ dispatch }, options = {}) {
    if (!options) {
      return;
    }
    // autoReload 单位为秒
    const { message = '', autoReload = 0 } = options;
    if (message) {
      const toast = Toast.loading({
        message,
        duration: (autoReload || 2) * 1000,
        forbidClick: !!autoReload,
      });
      if (autoReload) {
        let remainTime = autoReload;
        const updateTime = () => {
          remainTime--;
          toast.message = `${message}(${remainTime}s)`;
          if (remainTime > 0) {
            setTimeout(updateTime, 1000);
          } else {
            dispatch('reload');
          }
        };
        updateTime();
      }
    }
  },

  setTrackLooger({ getters }, logData, params = {}) {
    setLogger(logData, { ...params, sl: getters.sl });
  },
  getImgUrl({ state, commit }) {
    const { goods, activityInfo } = state;
    const { activityAlias, activityType, activityId, umpType } = activityInfo;
    const { alias } = goods;

    const activityData = getActivityInfo({
      activityAlias,
      activityType,
      activityId,
      umpType,
    });
    const params = {
      alias,
      ...activityData,
    };
    getWeappCard(params).then((res) => commit('updateShareCardInfo', res));
  },
};

export default actions;
