import { get } from '../utils';

const app = getApp();
const adConfig = get(app, 'globalData.adData', {});
const state = {
  promoteThemeConfig: {
    priceCommissionText: '最高赚',
    buyBtnMainText: '立即购买',
    buyBtnDescText: '购买后最高赚',
    shareBtnMainText: '分享赚钱',
    shareBtnDescText: '分享后预计可赚',
  },
  bottomHeight: 40,
  // 异步数据标志位，失败不可点击购买
  fetchSuccess: false,
  // 服务器时间，用于校正本地时间
  serverDeltaTime: 0,
  clientTimestamp: +new Date(),
  serverTimestamp: +new Date(),

  // 有赞云模板 将数组转行成驼峰对象
  design: [],

  // 环境配置
  env: {
    platform: 'weixin',

    isMobile: true,
    isWeixin: true,
    // 微信支付环境
    wxpayEnv: {},
    // app版本
    version: '',
    // 是否需要自动展示sku弹层
    showSku: false,
    // 第三方重定向登录标识
    redirectLoginByPlatform: false,
    // 是否是全局购物车
    isAllCart: false,
    // 是否是新希望店铺
    isNewHopeKdt: false,
    // 隐藏底部栏
    hideGoodsBottom: false,
  },

  pageSwitch: {},

  // 公众号配置
  mpData: {
    // 公众号账号、二维码
    account: {},
    // 公众号店铺
    shop: {},
  },

  // 广告配置
  // TODO 不确定对不对
  adConfig: {
    isGdt: adConfig.gdt_vid,
    // 广点通版本号
    version: adConfig.gdt_vid || '',
    adType: adConfig.qz_gdt || '',
  },

  // 用户状态
  user: {
    // 登录状态标识，标识是否是登录组件登录成功状态
    logined: true,
    // 是否是管理员账户
    isAdmin: false,
  },

  // 下单配置
  buyConfig: {
    // 是否公众号粉丝
    isFans: false,
    // 非粉丝不可购买
    noFansBuy: false,
  },

  // app engine
  appEngineConfig: null,

  // open app 配置
  appConfig: null,

  // 页面基本配置 传参等，不会被重置
  pageParams: null,

  // 主图信息
  picture: {
    // 商品图片列表
    pictures: [],
    // 服务端评估图片高度
    height: 320,
  },

  // 视频
  video: {
    width: 0,
    height: 0,
    coverUrl: '',
    videoUrl: '',
    // 统计播放
    countPlayedUrl: '',
  },

  // 商品基本信息
  goods: {
    type: 'normal',
    id: 0,
    alias: '',
    title: '',
    // 商品页面路径
    path: '',
    // 商品头图，用于sku展示
    picture: '',
    // 商品卖点，页面子标题内容
    sellPoint: '',
    // 划线价格
    origin: '',

    // 是否上架
    isDisplay: 0,
    // 是否外链商品
    isOutlink: false,
    outlinkUrl: '',
    // 商品是否被收藏，有赞精选
    isGoodsCollected: false,
    // 是否仅限特定会员购买 || 限定积分购买
    limitBuy: false,
    // 商品是否参与f码活动
    isSupportFCode: false,

    // 是否是虚拟商品，包含虚拟商品/电子卡券/优惠券
    isVirtual: 0,
    // 是否是优惠券
    isVirtualCoupon: 0,
    // 是否是海淘
    isHaitao: false,
    // 餐饮
    isCatering: false,
    // 是否是酒店商品
    isHotel: false,

    // 风险店铺提示
    risk: null,
    // 显示积分商品价格
    pointsPrice: '',
    // 积分显示名称，如金币、积分
    pointsName: '积分',
    // 显示价格
    price: '0.00',
    // 最小价格
    minPrice: 0,
    // 最大价格
    maxPrice: 0,
    // 销量
    soldNum: 0,
    // 库存
    stockNum: 0,
    // 隐藏库存
    hideStock: false,
    // 总库存
    totalStockNum: 0,
    // 当前商品展示的限购信息
    limit: {
      // 商品限购数
      quota: 0,
      // 限购周期
      quotaCycle: 0,
      // 已购买数
      quotaUsed: 0,
      // 每单限购
      singleQuota: 0,
      // 起售数量
      startSaleNum: 1,
    },
    // 子标题
    subTitle: '',
    // 商品属性
    itemSalePropList: [],
    // 0: 不是自建商品； 1： 是网店自建商品
    createdType: 0,
    // 商品是否支持七天无理由退换货
    supportUnconditionalReturn: false,
    // 换货
    barterModel: {
      // 是否支持换货，1 支持 0 不支持
      isSupportBarter: 0,
    },
  },

  // 商品活动,只关心活动名称和结束时间
  goodsActivityInfo: {
    activityName: '',
    activityId: 0,
    status: false,
    startTime: 0,
    endTime: 0,
    maxPrice: 0,
    minPrice: 0,
  },

  // 推广商品优惠券信息
  goodsCouponsInfo: {
    couponList: [],
    optimismCoupon: null,
  },

  // 销售员支持活动tag，根据各个活动返回数据确定
  salesmanActivityTags: [],

  // 销售员推广佣金信息
  commissionInfo: {
    levelsVisible: 0,
    allowSelfBuy: 0,
    cpsRate: 0,
    nextLevelCpsRate: 0,
    nextLevelName: '',
    profit: 0,
    nextLevelProfit: 0,
    levelUpgradeInfo: '',
    showLevelInfoStatus: 3,
    // 0：有佣金 1：人工结算
    isNotShowCpsRateWhenManualSettle: 0,
    isCustom: 0,
    constantCommission: 0,
    nextLevelCpsMoney: 0,
    profitRange: [],
    upgradeNewLevelVersion: false,
  },

  // 任务奖励数据
  taskAwardData: {
    hasRunningTask: false,
    taskId: 0,
    unit: '',
    unitStr: '',
    nextAwardGap: '',
  },

  // 精选素材
  materials: {
    list: [],
    loading: false,
  },

  // 销售员信息
  accountInfo: {
    salesmanType: '',
    sl: '',
  },

  // 这个state会根据不同sku做展示
  limit: {
    // 商品限购数
    quota: 0,
    // 限购周期
    quotaCycle: 0,
    // 已购买数
    quotaUsed: 0,
    // 每单限购
    singleQuota: 0,
  },

  // 分销员
  salesman: {
    seller: '',
  },

  // 店铺信息
  shop: {
    kdtId: 0,
    rootKdtId: 0,
    logo: '',
    name: '',
    city: '',
    province: '',
    address: '',
    phone: '',
    // 7表示零售店铺
    shopType: '',
    // 是否是总店预览模式，通过query和连锁模式判断
    isHqShopPreview: '',
  },

  // 店铺配置
  shopConfig: {
    // 商品页是否展示立即购买按钮
    isShowBuyBtn: false,
    // 是否是有赞担保
    isYouzanSecured: false,
    // 是否加入担保交易
    isSecuredTransactions: false,
    // 是否店铺关闭/歇业
    isCloseBusiness: false,
    // 是否开启销量与成交记录
    showBuyRecord: false,
    // 主体认证类型 0 初始化状态 1 个人认证，2企业认证，3 个体工商户 4其他组织，5政府及事业单位  98网店 99认证被驳回
    principalCertType: 0,
    // 品牌认证类型 0 初始化状态 1 旗舰店 2专卖店 3直营店 4 专营店 99 认证被驳回
    brandCertType: 0,
    showDefaultCartText: true,
    cartText: '购物车',
    // 是否开启微信担保 0 未开通 1 开通
    weixinTransactionSecured: '0',
  },

  // 公众号关注信息
  followInfo: {
    qrcodeWeixinImg: '',
    // 是否请求加载过数据
    qrcodeWeixinFetched: false,
  },

  // 店铺担保配置
  guarantee: {
    // 是否加入有赞担保
    on: false,
    // 担保样式
    style: 0,
    // 担保详细文案
    desc: [],
  },

  // 商品tab栏数据
  goodsDetail: {
    // 默认激活的详情tab
    defaultType: 'goods',
    // 用来确定商品详情微页面正常加载，加载后才能显示推荐商品
    featureLoaded: false,
    // 模板风格 0：普通版 1：极速版
    // 小程序目前不支持极速版
    tplStyle: 0,
    // 富文本内容
    richText: '',
    // 商品详情组件数据
    showcaseComponents: [],
  },

  // 成交记录
  tradeRecords: {
    list: [],
    page: 1,
    loading: false,
    finished: false,
  },

  // sku 详细信息，后端传入
  originSku: {}, // 原始格式化后的商品SKU，原始数据，不修改
  originNormalSku: null, // 原始格式化后的商品SKU，含会员价格
  originActivitySku: null, // 原始格式化后的活动SKU
  originPointsSku: null, // 原始格式化后的积分SKU

  // 特殊活动，从商品页带过来的活动信息,存储一份，方便查询活动信息
  activityInfo: {
    // 活动alias
    activityAlias: '',
    // 活动id
    activityId: 0,
    // 活动类型
    activityType: 0,
    // 活动类型
    umpType: '',
  },

  // 用于展示的sku数据
  // 流程为，按需获取sku / activitySku => 赋值给 state.sku => 运算 getters.sku => 渲染
  skuData: {},

  staticSkuInfo: {
    spuPrice: {},
    skuPrices: [],
    spuStock: {},
    skuStocks: [],
  },

  // postData，下单表单提交数据
  // 因为校验登录需要缓存数据
  postData: {},

  // 页面展示层控制；后端获得的数据，保护goods的逻辑与display的逻辑
  display: {
    // 禁止购买原因
    forbidBuyReason: '',
    // 是否展示购买按钮
    showBuyBtn: true,
    // 是否展示不可购买按钮，包含 下架、售罄等
    showForbidBuyBtn: false,
    // 是否展示默认的按钮文案
    showDefaultBuyButtonText: true,
    // 自定义购买按钮文案
    buyButtonText: '',
  },

  // 浮层处理，不依赖后端数据
  displayPop: {
    // 是否显示分享
    sharePopShow: false,
    // 是否sku弹层
    skuPopShow: false,
    // 全局遮罩层
    maskShow: false,
    // 是否显示优惠券弹窗
    showCouponPopUp: false,
  },

  // 促销活动限流
  orderPromotionLimit: false,
  // 促销活动内容
  orderPromotion: [],

  // 优惠券列表
  umpCouponList: [],

  // 最佳优惠券
  optimalCouponList: [],

  // 浮动交易记录
  tradeRecordsV2: [],

  // 个人中心标题
  userCenterTitle: '个人中心',

  // 商品扩展信息
  goodsExtendInfo: {
    // 店铺笔记
    SHOP_NOTE: null,
    // 商品热榜
    HOT_RECOMMEND: null,
  },

  // 顶部导航元素top值列表
  navItemTopList: [],

  /** 分享卡片信息 */
  shareCardInfo: {},
};

export default state;
