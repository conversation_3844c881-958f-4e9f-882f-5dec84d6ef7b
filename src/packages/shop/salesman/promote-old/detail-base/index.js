import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import { mapActions, mapMutations } from '@youzan/vanx';

GoodsComponent({
  name: 'detail-base',

  state: ['design', 'displayPop'],

  options: {
    multipleSlots: true
  },

  data: {
    loading: true,
    showShopPopManager: false,
    bottomFixedHeight: 0
  },

  watch: {
    'data.design': function (val) {
      val &&
        setTimeout(() => {
          this.setYZData({ loading: false });
        }, 150);
    }
  },

  ready() {
    const { design } = this.data;
    design.length > 0 &&
      setTimeout(() => {
        this.setYZData({ loading: false });
      }, 150);

    setTimeout(() => {
      this.setYZData({
        showShopPopManager: true
      });
    }, 2000);
  },

  methods: {
    ...mapActions(['setSalesmanData']),
    ...mapMutations(['showSharePop']),

    // 获取导航栏内容距离顶部值
    getNavItemTop(e) {
      this.$dispatch('getNavItemTop', {
        self: this,
        scrollTop: e.detail
      });
    }
  }
});
