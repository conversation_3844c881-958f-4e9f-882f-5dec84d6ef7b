  <template name="spread-slot">
    <slot
      wx:for="{{ item.slots }}"
      wx:key="*this"
      name="{{ item }}"
    />
  </template>
  <view class="feature-page__top-hook" />

  <view class="page-goods" hidden="{{ loading }}">
    <block wx:for="{{ design }}" wx:key="type">

      <image-block wx:if="{{ item.type == 'image-block'}}" >
      </image-block>
      
      <base-info-block wx:if="{{ item.type == 'base-info-block'}}" >
      </base-info-block>

      <coupon-block wx:if="{{ item.type == 'coupon-block'}}" >
      </coupon-block>
      
      <goods-detail-block wx:if="{{ item.type == 'goods-detail-block'}}" >
      </goods-detail-block>
      
      <task-award-block wx:if="{{ item.type == 'task-award-block'}}" >
      </task-award-block>
      
      <price-intro-block wx:if="{{ item.type == 'price-intro-block'}}" >
      </price-intro-block>
      
      <trade-carousel wx:if="{{ item.type == 'trade-carousel'}}" >
      </trade-carousel>

      <fixed-bottom-block wx:if="{{ item.type === 'fixed-bottom-block' }}">
      </fixed-bottom-block>
    </block>
  </view>

  <shop-pop-manager
    wx:if="{{ showShopPopManager }}"
    source="{{ 2 }}"
  />
  
  <slot name="detail-base-footer"></slot>
