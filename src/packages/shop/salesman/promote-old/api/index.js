const app = getApp();
const request = app.request;

const basePath = '/wscsalesman/promotion/promote';

// 获取商品佣金信息
export const getGoodsRecommendRateInfo = (data) => {
  return request({
    path: `${basePath}/getGoodsRecommendRateInfo.json`,
    data,
    method: 'GET'
  });
};

// 获取商品佣金信息
export const getCouponsAndActivityInfo = (data = {}) => {
  return request({
    path: `${basePath}/getCouponsAndActivityInfo.json`,
    data,
    method: 'GET'
  });
};

// 获取素材列表
export const listMaterialPagedByConditionV2 = (data = {}) => {
  return request({
    path: `${basePath}/listMaterialPagedByConditionV2.json`,
    data,
    method: 'POST'
  });
};

export const getShortUrl = (data) => {
  return request({
    path: '/wscump/salesman/zone/getShortUrl.json',
    data
  });
};

// 发布动态
export const publishMoments = (data) => {
  return request({
    path: '/wscump/salesman/zone/publishMoments.json',
    data,
    method: 'POST'
  });
};

// 获取任务奖励信息
export const getTaskByItemId = (data) => {
  return request({
    path: `${basePath}/getTaskByItemId.json`,
    data,
    method: 'GET'
  });
};

// 获取推广商品记录
export const getRecommendRecord = (data) => {
  return request({
    path: `${basePath}/getRecommendRecord.json`,
    data,
    method: 'GET'
  });
};

// 获取卡片
export const getWeappCard = (params) => {
  return request({
    path: '/wscgoods/poster/card/goods-v2',
    data: params,
    method: 'GET',
  });
};
