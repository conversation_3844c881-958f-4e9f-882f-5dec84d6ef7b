import { GOODS, PAGE_MAP } from './page-path';
// 弹窗报错提醒
export function showWarningModal(options, NO_TRY = false) {
  const {
    title = '提示',
    content
  } = options;
  const pages = getCurrentPages() || [];
  const currentPage = (pages[pages.length - 1] || {}).route;
  if (`/${currentPage}` !== PAGE_MAP[GOODS]) return;

  wx.showModal({
    title,
    content,
    showCancel: !NO_TRY,
    cancelText: '进店逛逛',
    confirmText: NO_TRY ? '进店逛逛' : '重试',
    success: res => {
      if (!res.confirm || NO_TRY) {
        wx.reLaunch({
          url: '/packages/home/<USER>/index'
        });
      } else {
        wx.startPullDownRefresh();
      }
    }
  });
}
