// 有赞担保展示样式
export const GUARANTEE_STYLE = {
  // 默认样式，白底灰字
  BASE: 0,
  // 样式1，白底绿字
  WHITE: 1,
  // 样式2, 绿底白字
  GREEN: 2
};

export const LOGGER_CONFIG = {
  copyMaterial: {
    et: 'click', // 事件类型
    ei: 'copy_material', // 事件标识
    en: '复制素材', // 事件名称
    pt: 'salesmanpromotion', // 页面类型
  },
  shareStory: {
    et: 'custom', // 事件类型
    ei: 'share_story', // 事件标识
    en: '同步到动态', // 事件名称
    pt: 'salesmanpromotion', // 页面类型
  },
  clickPurchase: {
    et: 'click', // 事件类型
    ei: 'click_purchase', // 事件标识
    en: '点击自购', // 事件名称
    pt: 'salesmanpromotion', // 页面类型
  },
  clickLevelCenter: {
    et: 'click', // 事件类型
    ei: 'click_levelcenter', // 事件标识
    en: '等级中心', // 事件名称
    pt: 'salesmanpromotion', // 页面类型
  },
  clickRewardCenter: {
    et: 'click', // 事件类型
    ei: 'click_rewardcenter', // 事件标识
    en: '点击奖励中心', // 事件名称
    pt: 'salesmanpromotion', // 页面类型
  },
  issueCoupon: {
    et: 'click', // 事件类型
    ei: 'issue_coupon', // 事件标识
    en: '发放优惠券', // 事件名称
    pt: 'salesmanpromotion', // 页面类型
  },
  enterpage: {
    et: 'display', // 事件类型
    ei: 'enterpage', // 事件标识
    en: '浏览页面', // 事件名称
    pt: 'salesmanpromotion', // 页面类型
  },
};
