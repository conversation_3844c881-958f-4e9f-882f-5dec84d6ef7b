import { get, omit } from '@/packages/shop/salesman/promote/utils';
import api from '@/packages/shop/salesman/promote/utils/api';

const formatUserStateData = (data = {}, pointsName) => {
  const {
    pageFeature,
    // 用户已购数量
    quotaUsed,
    // 是否有购买权限，如VIP限购
    hasPurchaseRight,
    salableStores,
    soldNum,
    spuStock,
    skuStocks
  } = data;

  return {
    pageFeature,
    goodsExtra: {
      quotaUsed,
      hasPurchaseRight,
      pointsName
    },
    salableStores,
    soldNum,
    spuStock,
    skuStocks,
    isFastBuy: false
  };
};

const postUserStateJson = (pageData) => {
  const { goodsData, shopData, pageParams, shopMetaInfo } = pageData;

  const {
    alias,
    goods: { id: goodsId },
    shopConfig = {},
    delivery = {}
  } = goodsData;
  const { kdtId } = shopData;
  const { shopType, shopRole } = shopMetaInfo;
  // 是否有可用配送方式
  const { supportExpress = false, supportLocalDelivery = false, supportSelfFetch = false } = delivery;
  // 一些按钮展示的店铺设置
  const {
    hideShoppingCart = 0,
    freightInsurance = 1,
    isGift = 1,
    isWebImInGoods = 1,
    isWish = 1,
    showBuyBtn = 1,
    showShopBtn = 1,
    showWscWebIm = 1,
    scrmCreditDiyName = '{}'
  } = shopConfig;

  let pointsConfig = {};
  try {
    pointsConfig = JSON.parse(scrmCreditDiyName);
  } catch (e) {
    // ...
  }
  const { name: pointsName = '积分' } = pointsConfig;

  const omitParams = omit(pageParams, ['refUrl', 'salesmanType', 'sl']);

  // 参数回传给后端为了优化异步接口性能
  const requestQuery = {
    ...omitParams,
    alias,
    goodsId,
    kdtId,
    offlineId: 0,
    stockNum: get(goodsData, 'skuInfo.spuStock.stockNum', 0),
    supportExpress,
    supportLocalDelivery,
    supportSelfFetch,
    shopType,
    shopRole,
    pointsName,
    hideShoppingCart,
    freightInsurance,
    isGift,
    isWebImInGoods,
    isWish,
    showBuyBtn,
    showShopBtn,
    showWscWebIm,
    soldOutRecommendSwitch: get(goodsData, 'shop.multiStore.setting.soldOutRecommendSwitch', false),
    isFastBuy: get(goodsData, 'fastbuyFeature.isFastBuy', false),
    buyBtnConfig: get(goodsData, 'shopConfig.buyBtnConfig', '')
  };

  return api
    .post({
      url: '/wscgoods/weapp/user-goods-state.json',
      headers: {
        'content-type': 'application/json'
      },
      data: requestQuery
    })
    .then((data = {}) => {
      const result = formatUserStateData(data, pointsName);
      return Promise.resolve(result);
    });
};

export function initUserState(pageData) {
  return Promise.resolve()
    .then(() => postUserStateJson(pageData));
}
