import { VanxComponent, getCurrentPage } from 'shared/common/base/wsc-component';

export function GoodsComponent(component) {
  component.store = component.store || function () {
    const page = getCurrentPage(this);
    // 在商品详情也使用自己生成的 Store 实例，所以组件都要使用这个实例，而不能使用全局的实例
    return page.$store;
  };

  component.options = {
    multipleSlots: true,
    ...component.options
  };

  const properties = component.properties || {};

  component.properties = Object.keys(properties).reduce((res, key) => {
    const item = res[key] = properties[key];
    if (item.default) {
      item.value = item.default;
    }

    return res;
  }, {});
  VanxComponent(component);
}
