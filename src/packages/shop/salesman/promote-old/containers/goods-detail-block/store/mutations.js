import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import { get, fullfillImage } from '@/packages/shop/salesman/promote/utils';
import { NoteMaterialType } from '../../../constants/material';

const handleText = (material) => {
  const { materialType, shopNoteMaterial, textContent = '' } = material;
  if (NoteMaterialType === materialType) {
    return shopNoteMaterial.description;
  }
  return textContent;
};

const materialsListAdaptor = (list) => {
  return list.map((item) => {
    const {
      id,
      itemMaterialDTO,
      imageMaterialDTOs = [],
      videoMaterialDTO = {},
      featureMaterial = {},
      shopNoteMaterial = {},
      name,
      usedNum,
      kdtId,
      materialType
    } = item;
    return {
      id,
      title: name,
      text: handleText(item),
      imageList: imageMaterialDTOs.map((item) => fullfillImage(item.url)),
      videoData: {
        video: {
          video_id: videoMaterialDTO.id,
          cover_url: videoMaterialDTO.coverUrl,
          coverHeight: videoMaterialDTO.coverHeight,
          coverWidth: videoMaterialDTO.coverWidth
        }
      },
      item: itemMaterialDTO && {
        goodsInfo: {
          ...itemMaterialDTO,
          title: itemMaterialDTO.itemName,
          image: itemMaterialDTO.pictureUrl
        }
      },
      usedNum,
      kdtId,
      featureMaterial,
      shopNoteMaterial,
      materialType,
      syncShareToZone: true,
      showAction: false,
      isClipboardDataReady: false, // 当前素材的本地数据是否准备完毕
      initOpenPop: true // 是否首次打开当前素材操作pop，首次打开加载`保存本地数据`，为保存操作准备
    };
  });
};

export default {
  GET_SHOWCASE_COMPONENTS_SUCCESS(state, { components = [], alias = '' }) {
    if (components.length > 0) {
      const showcaseComponents = components.map((comp) => {
        if (comp.type === 'goods_recommend') {
          return {};
        }
        comp.key = `${String(Math.random()).slice(0, 6)}-${comp.type}`;
        comp.risk_type = 1;
        comp.risk_alias = alias;
        return comp;
      });
      // 商品推荐组件依赖（临时）
      state.goodsDetail = {
        ...state.goodsDetail,
        showcaseComponents,
        // 微页面组件分页用
        themeFeature: {
          uniqueKey: alias + makeRandomString(8),
          componentsLength: showcaseComponents.length
        }
      };
    } else {
      this.commit('SET_FEATURE_LOADED');
    }
  },

  SET_FEATURE_LOADED(state) {
    state.goodsDetail.featureLoaded = true;
  },

  getMaterialList(state) {
    state.materials.loading = true;
  },

  getMaterialListSuccess(state, data) {
    const { list, totalItems } = data;
    state.showMoreMaterial = totalItems > 2;

    const materialList = materialsListAdaptor(list);
    state.materials = {
      list: materialList,
      loading: false
    };
  },

  getMaterialListFail(state) {
    state.materials.loading = false;
  },

  updateCheckMaterialStatus(state, checkMaterialIndex) {
    const checkMaterial = get(state, `materials.list[${checkMaterialIndex}]`);
    checkMaterial.syncShareToZone = !checkMaterial.syncShareToZone;
  },

  updateVideoSrcs(state, videoSrc) {
    state.videoSrcs.push(videoSrc);
  },

  updateSaveTipPublishStatus(state, data) {
    state.syncShareToZone = data;
  },
  SET_PERSONAL_SPACE_ABILITY(state, data) {
    state.hasPersonalSpaceAbility = data;
  },
};
