import {
  get,
  fullfillImage,
  mapKeysToCamelCase,
} from '@/packages/shop/salesman/promote/utils';
import Toast from '@vant/weapp/dist/toast/toast';
import adaptorComponents from 'constants/adaptor-components';
import { node as request } from 'shared/utils/request';
import { listMaterialPagedByConditionV2, publishMoments } from '../../../api';
import { PromoterRoleType } from '../../../constants/material';

const app = getApp();

const getCurrentMaterialItem = (goodsInfo = {}) => {
  const { alias, itemId, pictureUrl, itemName, price } = goodsInfo;
  return {
    alias,
    goodsId: itemId,
    image: pictureUrl,
    title: itemName,
    price,
  };
};

export default {
  setVideoSrc({ commit }, data) {
    commit('updateVideoSrcs', data);
  },

  getShowcaseComponents({ getters, state, commit }) {
    request({
      path: '/wscshop/goods/showcase-components.json',
      query: {
        alias: getters.alias,
        isGdt: state.adConfig.isGdt,
        version: state.adConfig.version,
        adType: state.adConfig.adType,
        adaptorComponents: adaptorComponents.join(','),
      },
      config: {
        cache: true,
        expire: 10,
      },
    })
      .then((res) => {
        commit('GET_SHOWCASE_COMPONENTS_SUCCESS', res);
      })
      .catch((err) => {
        console.log(err.msg || '成交记录获取失败');
      });
  },

  getMaterialList({ state, commit, getters }) {
    const { loading } = state.materials;
    if (loading) return;
    const itemId = getters.itemId;

    commit('getMaterialList');

    const roleType = PromoterRoleType;

    return listMaterialPagedByConditionV2({
      roleType,
      itemId,
      pageSize: 2,
    })
      .then((data) => {
        commit('getMaterialListSuccess', data);
        const materialLen = get(data, 'list.length');
        if (materialLen) {
          commit('updateSalesmanActivityTags', { text: '精选素材' });
        }
        return materialLen;
      })
      .catch((err) => {
        commit('getMaterialListFail');
        Toast(err.msg || '精选素材获取失败');
      });
  },

  changeMaterialCheckStatus({ state, commit }, data) {
    const { materials } = state;
    const checkMaterialIndex = materials.list.findIndex(
      (material) => material.id === data.id
    );
    commit('updateCheckMaterialStatus', checkMaterialIndex);
  },

  publishMaterial({ commit }, data) {
    return new Promise((resolve) => {
      const { imageList = [], item, videoData, text, syncShareToZone } = data;
      console.log('publishMaterial', data);
      commit('updateSaveTipPublishStatus', syncShareToZone);
      if (syncShareToZone) {
        const goods = getCurrentMaterialItem(item.goodsInfo);
        let moment = {
          imageList: imageList.map((url) => fullfillImage(url)),
          goods,
          recommendInfo: escape(text),
        };

        if (get(videoData, 'video.video_id', '')) {
          moment = {
            ...moment,
            video: mapKeysToCamelCase(videoData.video),
          };
        }

        publishMoments(moment)
          .then((res) => {
            if (res) {
              resolve({ type: 'material', status: 1 });
            }
          })
          .catch(() => {
            resolve({ type: 'material', status: 0 });
          });
      } else {
        resolve({});
      }
    });
  },

  getShopAbility({ commit }, kdtId) {
    app
      .requestUseCdn({
        path: '/wscsalesman/salesman/shop/checkShopAbility.json',
        method: 'GET',
        data: {
          kdtId,
          abilityName: 'salesman_advance_personal_space_ability',
        },
      })
      .then((res) => {
        commit('SET_PERSONAL_SPACE_ABILITY', res.valid);
      });
  },
};
