import Event from '@youzan/weapp-utils/lib/event';
import theme from 'shared/common/components/theme-view/theme';
import FeatureLoadControl from 'shared/components/showcase-options/feature-load';
import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import { mapState, mapGetters, mapActions, mapMutations } from '@youzan/vanx';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';

const app = getApp();

GoodsComponent({
  mapData: {
    ...mapState({
      goods: (state) => state.goods || {},
      shop: (state) => state.shop || {},
    }),
    ...mapState('/', {
      showcaseExtra() {
        const { token = {}, shop = {} } = app.$store.state;
        const { chainStoreInfo = {} } = app.getShopInfoSync();
        const { isMultiOnlineShop } = chainStoreInfo;
        const lsKdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId();

        return {
          appId: app.getAppId(),
          kdtId: shop.kdtId,
          lsKdtId,
          buyerId: token.userId,
          offlineId: shop.offlineId || '',
        };
      },
    }),
    ...mapGetters([
      'goodsDetailTabs',
      'activeDetailTabIndex',
      // 店铺基础信息
      'shopLogo',
    ]),
  },

  data: {
    mainColor: '',
    selectedId: 0,
    selectedType: 'material',
    showcaseBuyData: {},
    showShowcaseBuy: false,
    uniqueKey: '',
    componentsLength: 0,
  },

  computed: {
    ...mapState(['goodsDetail']),
  },

  watch: {
    goodsDetail(newVal) {
      this.initShowCase(newVal);
    },
  },

  attached() {
    theme.getThemeColor('main-bg').then((color) => {
      this.setYZData({
        mainColor: color,
      });
    });
  },

  ready() {
    const kdtId = app.getKdtId();
    this.getShowcaseComponents();
    this.getMaterialList().then((res) => {
      if (res > 0) {
        this.handleTabChange({ detail: { index: 0 } });
      } else {
        this.handleTabChange({ detail: { index: 1 } });
      }
    });
    this.getShopAbility(kdtId);
  },

  detached() {
    const { uniqueKey } = this.data;
    FeatureLoadControl.clearShowcaseComponents(uniqueKey);
  },

  methods: {
    ...mapActions([
      'getShowcaseComponents',
      'getMaterialList',
      'getShopAbility',
    ]),
    ...mapMutations(['SET_FEATURE_LOADED']),

    onFeatureLoaded() {
      this.SET_FEATURE_LOADED();
    },

    handleTabChange({ detail }) {
      this.setYZData({
        selectedId: detail.index,
      });
    },

    initShowCase(goodsDetail) {
      const { showcaseComponents } = goodsDetail;
      const uniqueKey = this.data.goods.alias + makeRandomString(8);

      // 确保该商品 只绑定一个 event key
      const reg = new RegExp('feature-load:.+' + uniqueKey);
      Object.keys(Event._events).forEach((key) => {
        if (reg.test(key)) Event.off(key);
      });

      FeatureLoadControl.setShowcaseComponents(
        showcaseComponents,
        false,
        false,
        uniqueKey
      );

      this.setYZData({
        componentsLength: showcaseComponents.length,
        uniqueKey,
      });
    },

    showcaseHandleGoodsBuy({ detail }) {
      const presale = detail.preSale || detail.pre_sale;
      const alias = detail.alias;

      if (+presale === 1) {
        wx.navigateTo({
          url: `/pages/goods/detail/index?alias=${alias}`,
        });
        return;
      }

      this.setYZData({
        showShowcaseBuy: true,
        showcaseBuyData: { alias },
      });
    },

    handleSkuHide() {
      this.setYZData({
        showShowcaseBuy: false,
      });
    },
  },
});
