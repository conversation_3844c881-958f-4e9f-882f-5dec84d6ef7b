<view id="js-nav-detail" class="goods-detail-wrap">
  <van-tabs
    wx:if="{{ goodsDetailTabs.length > 1 }}"
    color="{{ mainColor }}"
    z-index="{{ 1 }}"
    active="{{ selectedId }}"
    class="goods-detail__tab"
    line-width="{{ 24 }}"
    border="{{ false }}"
    bind:click="handleTabChange"
  >
    <van-tab title="{{ item.desc }}" wx:for="{{ goodsDetailTabs }}" wx:key="type" />
  </van-tabs>

  <!-- 只有商品描述的内容时候 显示专有的头部 -->
  <view class="goods-header" wx:if="{{ goodsDetailTabs.length === 1 && componentsLength }}">
    商品描述
  </view>

  <view hidden="{{ selectedId !== 0 }}">
    <material-list />
  </view>
</view>
