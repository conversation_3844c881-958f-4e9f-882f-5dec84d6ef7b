<view class="feed-item custom-class">
  <view class="feed-item__content feed-item__content--no-img">
    <clamp
      wx:if="{{ text }}"
      custom-class="feed-item__text"
      content="{{ text }}"
      expand-text="全文"
    />
    <!-- media -->
    <!-- TODO: 视频宽高样式处理先注释，因为安卓不好看 -->
    <view
      wx:if="{{ video.video.video_id }}"
      class="feed-item__video"
    >
      <showcase-video
        kdt-id="{{ videoKdtId }}"
        componentData="{{ video }}"
        bind:get-src="handleGetSrc"
      />
    </view>
    <gallery
      wx:if="{{ imageList.length > 0 }}"
      img-list="{{ imageList }}"
      bind:load="_handleImageLoad"
    />
    <!-- media end -->
    <slot name="after-media" />
    <!-- action-area -->
    <slot name="before-bottom" />
    <!-- action-area end -->
    <slot name="bottom" />
  </view>
</view>
