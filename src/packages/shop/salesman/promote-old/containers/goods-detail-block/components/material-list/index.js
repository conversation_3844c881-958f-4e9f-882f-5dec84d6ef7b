import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import Theme from 'shared/common/components/theme-view/theme';
import { get, args, jumpLink } from '@/packages/shop/salesman/promote/utils';
import {
  NoteMaterialType,
  FeatureMaterialType
} from '@/packages/shop/salesman/promote/constants/material';
import { mapActions } from '@youzan/vanx';
import Toast from '@vant/weapp/dist/toast/toast';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';
import {
  getMaterialBannerId,
  getLinkMaterialUrl,
  downloadAndSaveImage,
  downloadAndSaveViedo,
  getTipsData
} from './utils';
import { getShortUrl } from '@/packages/shop/salesman/promote/api';
import { getUrl, MATERIAL_CENTER } from '@/packages/shop/salesman/promote/common/page-path';
import { LOGGER_CONFIG } from '@/packages/shop/salesman/promote/common/config';

const app = getApp();

GoodsComponent({
  name: 'MaterialList',

  state: [
    'currentImages',
    'syncShareToZone',
    'displayPop',
    'materials',
    'videoSrcs',
    'showMoreMaterial',
    'hasPersonalSpaceAbility',
  ],

  getters: ['kdtId', 'itemId', 'sl'],

  data: {
    themeMainBgColor: '',
    showSaveTip: false,
    tipsData: []
  },

  created() {
    Theme.getThemeColor('main-bg').then((color) => {
      this.setYZData({
        themeMainBgColor: color
      });
    });
  },

  methods: {
    ...mapActions([
      'changeMaterialCheckStatus',
      'publishMaterial',
      'setVideoSrc',
      'setTrackLooger'
    ]),
    handleChangeCheck(e) {
      const { material = {} } = e.currentTarget.dataset;
      const { id, syncShareToZone } = material;
      this.setTrackLooger(LOGGER_CONFIG.shareStory, { materialId: id, syncShareToZone });
      this.changeMaterialCheckStatus(material);
    },

    handleGetMore({ currentTarget }) {
      const { query } = currentTarget.dataset;
      jumpLink(getUrl(MATERIAL_CENTER, query));
    },

    handleGetSrc(e) {
      this.setVideoSrc && this.setVideoSrc(e.detail);
    },

    onClose() {
      this.setYZData({
        showSaveTip: false
      });
    },

    handleSave(e) {
      const { material = {} } = e.currentTarget.dataset;
      const { imageList, videoData, id: materialId } = material;
      const publishMaterial = {
        ...material,
        syncShareToZone:
          material.syncShareToZone && this.data.hasPersonalSpaceAbility,
      };

      this.setTrackLooger(LOGGER_CONFIG.copyMaterial, { materialId });

      const pfun = [
        this.publishMaterial(publishMaterial),
        this.setClipboardData(material),
      ];
      if (imageList.length > 0) {
        pfun.push(this.saveImage(imageList));
      }
      if (videoData.video && videoData.video.video_id) {
        const { video_id: id } = videoData.video;
        const video = this.data.videoSrcs.find((item) => item.id === id);
        if (video) {
          pfun.push(this.saveVideo(video.src));
        }
      }
      Promise.all(pfun).then((res) => {
        console.log(res);
        const tipsData = getTipsData(res);
        this.setYZData({
          showSaveTip: true,
          tipsData
        });
      });
    },

    setClipboardData(material) {
      return new Promise((resolve) => {
        let itemUrl = '';
        const { sl } = this.data;
        const linkMaterial = this.handleLinkMaterial(material);
        let linkUrl = '';
        const { id, text = '' } = material;
        const kdtId = app.getKdtId();

        if (get(material, 'item.goodsInfo')) {
          const params = {
            banner_id: getMaterialBannerId({ materialId: id, spm: 'salesMtrl' }),
            sub_kdt_id: kdtId
          };
          itemUrl = args.add(material.item.goodsInfo.itemUrl, params);
          if (sl) {
            itemUrl = addSalesmanParams({ url: itemUrl, sl });
          }
        }
        if (linkMaterial) {
          const { alias } = linkMaterial;
          const { url } = getLinkMaterialUrl({
            alias,
            materialType: material.materialType,
            kdtId
          });
          linkUrl = url;
          if (sl) {
            linkUrl = addSalesmanParams({ url: linkUrl, sl });
          }
        }
        const clipboardDataUrl = itemUrl || linkUrl;
        return this.handleUrlToShort(clipboardDataUrl).then(({ res }) => {
          const url = res || clipboardDataUrl;
          const br = text && url ? '\n' : '';
          const clipboardData = `${text}${br}${url}`;
          wx.setClipboardData({
            data: clipboardData,
            success: () => {
              resolve({ type: 'copy', status: 1 });
            }
          });
        });
      });
    },

    handleUrlToShort(url) {
      if (url) {
        return getShortUrl({ url }).catch(() => ({ res: url }));
      }
      return Promise.resolve({ res: '' });
    },

    handleLinkMaterial({ featureMaterial, shopNoteMaterial, materialType }) {
      if (NoteMaterialType === materialType) {
        return shopNoteMaterial;
      }
      if (FeatureMaterialType === materialType) {
        return featureMaterial;
      }
    },

    saveImage(imageList) {
      return new Promise((resolve) => {
        const pf = [];
        imageList.forEach((url) => {
          pf.push(downloadAndSaveImage(url));
        });
        Promise.all(pf)
          .then(() => {
            console.log('保存完了');
            resolve({ type: 'image', status: 1 });
          })
          .catch(() => {
            console.log('图片保存异常');
            resolve({ type: 'image', status: 0 });
          });
      });
    },

    saveVideo(url) {
      return new Promise((resolve) => {
        downloadAndSaveViedo(url)
          .then(() => {
            console.log('保存视频成功');
            resolve({ type: 'video', status: 1 });
          })
          .catch((err) => {
            let message = get(err, 'errMsg', '下载视频失败');
            if (message === 'downloadFile:fail exceed max file size') {
              message = '不支持下载超过50MB的视频';
            }
            if (message === 'downloadFile:fail createDownloadTask:fail url not in domain list') {
              message = '该视频暂不支持下载';
            }
            if (message === 'downloadFile:fail url not in domain list') {
              message = '该视频暂不支持下载';
            }
            resolve({ type: 'video', status: 0 });
            Toast({
              message,
              duration: 4000
            });
          });
      });
    }
  }
});
