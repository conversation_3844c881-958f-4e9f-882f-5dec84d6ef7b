  <view class="module-unit">
    <!-- 商品活动价在activity里 -->
    <view class="m-base-info">
      <goods-activity wx:if="{{ showActivityBanner }}" tag="{{ baseInfoBarTag }}"/>
      <!-- 普通商品价格 -->
      <goods-price wx:if="{{ !showActivityBanner }}" class="u-base-info-row"/>
      <!-- 等级信息 -->
      <level-info />
      <!-- 商品标题 -->
      <goods-title class="u-base-info-row" />
      <salesman-activity class="u-base-info-row last-child" />
    </view>
    <!-- 有赞担保 -->
    <guarantee-detail-bar
      is-wx-promise="{{ isWxPromise }}"
      alias="{{ goods.alias }}"
      kdt-id="{{ shop.kdtId }}"
      bind:onShow="guaranteeOnShow" />
  </view>