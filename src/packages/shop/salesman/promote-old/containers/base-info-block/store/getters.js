import { formatPrice, accMul } from '@/packages/shop/salesman/promote/utils';
import { GUARANTEE_STYLE } from '@/packages/shop/salesman/promote/common/config';
import formatCommission from '@youzan/salesman-cube-core-utils/lib/formatCommission';

const SHOW_COMMISSION_RATE = 1;
// const SHOW_LEVEL_UPGRADE = 2;
const HIDE_LEVEL_INFO = 3;

export default {
  // 是否显示活动推广栏
  showActivityBanner(state, getters) {
    const { activityName } = getters.goodsActivityInfo;

    if (activityName) {
      return true;
    }

    return false;
  },

  activityPrice(state, getters) {
    const { oldPrice = '' } = state.goods;
    const { minPrice = 0, maxPrice = 0 } = getters.goodsActivityInfo;

    return {
      // 是否是价格区间
      isRange: minPrice !== maxPrice,
      showPrice: formatPrice(+minPrice).split('.'),
      originPrice: oldPrice,
    };
  },

  activityNotStart(getters) {
    const { status } = getters.goodsActivityInfo;
    return !!status;
  },

  baseInfoBarTag(state, getters) {
    const { activityName } = getters.goodsActivityInfo;
    return activityName;
  },

  // 是否展示划线价/原价
  showOriginPrice(state) {
    const { price, oldPrice, origin } = state.goods;

    if (oldPrice && oldPrice !== price) {
      return {
        label: '价格',
        price: `￥${oldPrice}`,
      };
    }

    const originPrice = origin || '';
    if (/^\s*\d+(\.\d{1,2})?\s*$/.test(originPrice)) {
      return {
        label: '价格',
        price: `￥${originPrice.trim()}`,
      };
    }

    return {
      label: '',
      price: originPrice,
    };
  },

  // 是否展示全程护航
  showGuaranteeBar(state) {
    return state.guarantee.on;
  },

  isWxPromise(state) {
    return state.shopConfig.weixinTransactionSecured === '1';
  },

  // 有赞担保更多描述文案
  guaranteeDesc(state) {
    const { guarantee = {} } = state;
    if (
      guarantee.on &&
      guarantee.style !== GUARANTEE_STYLE.WHITE &&
      guarantee.style !== GUARANTEE_STYLE.GREEN
    ) {
      return [...(guarantee.desc || [])]
        .sort((a, b) => a.sort - b.sort)
        .slice(0, 5)
        .map((it) => it.desc)
        .join(' · ');
    }
    return '';
  },

  soldNumText(state) {
    const { soldNum } = state.goods;

    if (soldNum) {
      return `已成交 ${soldNum}笔`;
    }
    return '';
  },
  showLevelBlock(state) {
    const { commissionInfo = {} } = state;
    const { showLevelInfoStatus } = commissionInfo;
    return showLevelInfoStatus !== HIDE_LEVEL_INFO;
  },

  levelUpgradeTextData(state) {
    const { commissionInfo = {} } = state;
    const {
      nextLevelCpsRate,
      nextLevelProfit,
      nextLevelName,
      showLevelInfoStatus,
      nextLevelCpsMoney,
    } = commissionInfo;
    if (
      +showLevelInfoStatus === SHOW_COMMISSION_RATE &&
      (nextLevelCpsRate || nextLevelCpsMoney || nextLevelProfit)
    ) {
      return {
        showNextLevelRate: true,
        nextLevelName,
        nextLevelCpsRate: `${accMul(nextLevelCpsRate, 100)}%`,
        nextLevelProfit,
      };
    }
    return {};
  },

  commissionData(state) {
    const {
      cpsRate,
      isNotShowCpsRateWhenManualSettle,
      profitRange,
      commissionSendType,
      commissionConfigDTO,
      customPointsName,
    } = state.commissionInfo;
    const result = {
      type: 'money',
      text: '最高赚',
      value: 0,
    };
    if (isNotShowCpsRateWhenManualSettle) {
      return { ...result, ...{ type: 'none', value: '人工结算', text: '' } };
    }

    const { maxCommissionStr } = formatCommission({
      profitRange,
      isYuan: true,
      commissionSendType,
      commissionConfigDTO,
      customPointsName,
    });

    if (maxCommissionStr) {
      return { ...result, ...{ value: maxCommissionStr } };
    }
    if (cpsRate > 0) {
      return {
        type: 'rate',
        text: '佣金比例',
        value: `${accMul(cpsRate, 100)}%`,
      };
    }

    return result;
  },
};
