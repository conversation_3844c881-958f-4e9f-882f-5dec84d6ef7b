@import '~mixins/index.scss';

.common-popup {
  width: 100%;
  min-height: 480px;
  height: 80vh;
  border-radius: 20px 20px 0 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

@mixin guarantee-bar__base {
  padding: 13px 16px !important;
  background-color: $white;
  margin-top: 8px;
}

.guarantee-bar {
  &--0,
  &--1,
  &--2 {
    @include guarantee-bar__base;
  }

  &--1 {
    .guarantee-bar__text {
      color: #07c160;
    }
  }

  &--2 {
    background-color: #07c160 !important;

    .guarantee-bar__text {
      color: $white;
    }

    .guarantee-bar__icon {
      color: $white;
    }
  }

  &__info {
    height: 18px;
    display: flex;
  }

  &__logo {
    height: 18px;
    width: 77px;

    &-wx {
      width: 165px;
    }
  }

  &__text {
    padding-left: 8px;
    line-height: 18px;
    height: 18px;
    font-size: 14px;
    color: $gray-dark;
  }

  &__icon {
    display: flex;
    align-items: center;
    color: #969799;
  }

  &__title {
    text-align: left;
  }

  &__desc {
    padding-left: 22px;
    font-size: 12px;
    height: 16px;
    line-height: 16px;
    margin-top: 6px;
    color: $gray-dark;

    @include ellipsis;
  }
}

.guarantee-pop {
  &__container {
    display: flex;
    position: relative;
    width: 100%;
    padding: 20px 15px;
    box-sizing: border-box;
    flex-direction: column;
  }

  &__title {
    &-logo {
      display: block;
      margin: 10px auto;
      width: 110px;
      height: 25px;
    }

    &-desc {
      margin: 15px auto;
      font-size: 14px;
      text-align: center;
    }

    &-dot {
      display: inline-block;
      width: 12px;
      height: 10px;
      margin: 0 10px;
      vertical-align: middle;
    }
  }

  &__content {
    margin-top: 32px;
    flex: 1;
    overflow-y: auto;

    .item {
      display: flex;
      margin: 0 0 24px 0;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;

      .left {
        width: 15%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: flex-start;

        .bg {
          position: absolute;
          width: 100%;
          height: 100%;
          object-fit: contain;
          opacity: 0.1;
          background-image: linear-gradient(314deg, #07c160 0%, #7aff00);
        }

        .icon {
          width: 100%;
          height: 22px;
          background-size: auto 100%;
          background-repeat: no-repeat;
          background-position: center;
          margin-top: 2px;
        }
      }

      .right {
        width: 83%;

        .title {
          margin-top: 5px;
          font-size: 14px;
          font-weight: bold;
        }

        .extrainfo {
          color: #07c160;
          font-size: 14px;
          font-weight: normal;
          float: right;
          margin-right: 10px;
        }

        .detail {
          margin-top: 8px;
          font-size: 12px;
          color: #777;
          line-height: 1.5;
        }
      }
    }

    &-icon {
      position: relative;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 42px;
      height: 42px;
      overflow: hidden;
      border-radius: 100%;
    }
  }

  &__button {
    margin-top: 10px;
    line-height: 40px;
    height: 40px;
    text-align: center;
    border-radius: 40px;
  }
}

.guarantee-declare {
  &__container {
    position: relative;
    padding: 0 16px;
    display: flex;
    flex-direction: column;
  }

  &__back-icon {
    position: absolute !important;
    left: 16px;
    z-index: 1;
    color: #969799;
    font-size: 18px;
  }

  &__title {
    font-size: 16px;
    text-align: center;
    margin-top: 10px;
    color: #111;
    font-weight: 700;
  }

  &__content {
    margin: 20px 0;
    overflow-y: auto;
    flex: 1;
  }

  &__sub-title {
    font-size: 14px;
    color: #111;
    padding: 20px 0 10px;
  }

  &__text {
    font-size: 12px;
    color: #666;
    line-height: 1.3;
  }
}

.icon.dkbz {
  background-image: url(https://b.yzcdn.cn/public_files/d49651dec0751556f8fc746de4d9ec9b.png);
}

.icon.lybz {
  background-image: url(https://b.yzcdn.cn/public_files/609282989c47f127d744414dc64180ec.png);
}

.icon.wqbz {
  background-image: url(https://b.yzcdn.cn/public_files/1b47dde544bac8c30883b4bedd355f70.png);
}

.icon.pfbz {
  background-image: url(https://b.yzcdn.cn/public_files/e7e092c59f0c7799777d76ca2de9a080.png);
}
