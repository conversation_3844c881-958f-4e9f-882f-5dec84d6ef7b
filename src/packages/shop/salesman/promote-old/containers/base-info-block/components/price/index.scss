@import '~mixins/index.scss';

.goods-price {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  align-items: center;
  position: relative;
  text-align: left;
  margin: 2px 0 8px;

  &__tag {
    margin: 6px 0;
  }

  // &__tag-vip {
  //   display: inline-block;
  //   width: 50px;
  //   height: 16px;
  //   background: url(https://img01.yzcdn.cn/public_files/f2a7689f3fe84adb04aa43c68b7e9d2d.png) no-repeat;
  //   background-size: cover;
  //   vertical-align: middle;
  //   position: relative;
  //   top: -1px;
  // }

  &__tag-content {
    display: inline-flex;
    align-items: center;
    border-radius: 12px;
    height: 20px;
    padding: 2px 6px;
    box-sizing: border-box;
    font-size: 12px;
    margin-right: 8px;
  }

  &__tag-font--l {
    font-size: 16px;
    font-weight: bold;
    margin-right: 2px;
    position: relative;
    height: 20px;
  }

  &__current {
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
    font-size: 16px;
    color: $red;
  }

  &__current-label {
    display: inline-block;
    font-size: 13px;
    line-height: 24px;
    margin-right: 2px;
    font-weight: 500;
    vertical-align: bottom;
  }

  .two-space {
    margin-right: 0.5em;
  }

  &__current-price {
    display: inline-block;
    vertical-align: middle;
    font-size: 20px;
    line-height: 24px;
    font-weight: bolder;
  }

  &__origin {
    display: block;
    font-size: 13px;
    color: $gray-dark;
    line-height: 24px;
    margin-right: 12px;
  }

  &__origin-label {
    margin-right: 4px;
  }

  &__origin-price {
    text-decoration: line-through;
  }
}
