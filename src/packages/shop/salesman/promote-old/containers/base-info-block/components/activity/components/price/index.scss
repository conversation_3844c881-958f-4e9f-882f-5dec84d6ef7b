@import '~mixins/index.scss';

.activity-price {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-left: 8px;

  &__label {
    display: flex;
  }

  &__current-label {
    display: inline-block;
    font-size: 13px;
    line-height: 24px;
    font-weight: 400;
    vertical-align: bottom;
  }

  &__label-xs {
    font-size: 13px;
    margin-left: 2px;
    line-height: 24px;
    vertical-align: bottom;
  }

  &__label-sm {
    font-size: 13px;
    line-height: 24px;
    font-weight: bolder;
    vertical-align: bottom;
  }

  &__label-xl {
    font-size: 20px;
    line-height: 24px;
    font-weight: bolder;
    vertical-align: bottom;
  }

  &__more {
    margin-left: 6px;
  }

  &__more-price {
    font-family: Avenir-Book;
    margin-top: 6px;
    font-size: 12px;
    line-height: 16px;
    opacity: 0.6;
    white-space: nowrap;
    color: #969799;

    .origin-price {
      text-decoration: line-through;
      vertical-align: bottom;
    }
  }
}

.price-length-5,
.price-length-6 {
  &.activity-price__label {
    &-xl {
      font-size: 18px;
    }
  }
}

.price-length-1,
.price-length-2,
.price-length-3,
.price-length-4 {
  &.activity-price__label {
    &-xl {
      font-size: 18px;
    }
  }
}
