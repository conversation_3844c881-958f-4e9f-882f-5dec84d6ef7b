import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import { mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'ActivityPrice',

  getters: ['activityPrice'],

  mapData: mapState({
    activityOriginPrice(state, getters) {
      let { originPrice } = getters.activityPrice;

      if (originPrice.indexOf('-') !== -1) {
        originPrice = originPrice.split('-')[0] + ' 起';
      }
      return originPrice;
    }
  })
});
