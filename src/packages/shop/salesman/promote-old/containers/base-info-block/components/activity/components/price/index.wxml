<view class="activity-price">
  <view class="activity-price__label">
    <!-- 活动只显示最低价 -->
    <theme-view
      color="general"
    >
      <text
        class="activity-price__label-xl"
      >
        <text class="activity-price__current-label">¥</text>
        {{ activityPrice.showPrice[0] }}
      </text>
      <text
        wx:if="{{ activityPrice.showPrice[1] }}"
        class="activity-price__label-xl"
      >.{{ activityPrice.showPrice[1] }}</text>
      <text wx:if="{{ activityPrice.isRange }}" class="activity-price__label-xs">起</text>
    </theme-view>
  </view>
  <view class="activity-price__more">
    <view wx:if="{{ activityPrice.originPrice }}" class="activity-price__more-price">
      <text class="origin-price">￥{{ activityOriginPrice }}</text>
    </view>
  </view>
</view>