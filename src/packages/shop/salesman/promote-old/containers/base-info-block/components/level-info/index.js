import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import {
  getUrl,
  SALESMAN_LEVEL_CENTER,
  SALESMAN_NEW_LEVEL_CENTER
} from '@/packages/shop/salesman/promote/common/page-path';
import { mapActions } from '@youzan/vanx';
import { jumpLink } from '@/packages/shop/salesman/promote/utils';
import { LOGGER_CONFIG } from '@/packages/shop/salesman/promote/common/config';

GoodsComponent({
  name: 'LevelInfo',

  state: ['commissionInfo'],

  getters: ['levelUpgradeTextData', 'sl', 'showLevelBlock'],

  methods: {
    ...mapActions(['setTrackLooger']),
    goLevelCenter() {
      this.setTrackLooger(LOGGER_CONFIG.clickLevelCenter);
      if (this.data.commissionInfo.upgradeNewLevelVersion) {
        jumpLink(getUrl(SALESMAN_NEW_LEVEL_CENTER, {}));
      } else {
        jumpLink(getUrl(SALESMAN_LEVEL_CENTER, {}));
      }
    }
  }
});
