import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import { mapGetters, mapState } from '@youzan/vanx';
import { remainTimestampToArray } from '@/packages/shop/salesman/promote/utils';
import { timerBehaivor } from '@/packages/shop/salesman/promote/common/behaviors';

const DAY = 24 * 3600 * 1000;

GoodsComponent({
  name: 'ActivityCountDown',
  behaviors: [timerBehaivor],

  getters: ['countdown', 'activityNotStart'],

  mapData: mapState({
    hasCountDown(state, getters) {
      return !!getters.countdown;
    },

    showProgress(state, getters) {
      return getters.countdown && getters.countdown.showProgress;
    }
  }),

  data: {
    // 倒计时计数，用于确定是否已开始就已经结束了
    remainDesc: '',
    remainObj: {},
    ended: false
  },

  computed: mapGetters(['countdown']),

  watch: {
    countdown(val) {
      if (val) {
        this.timeRunner();
      }
    }
  },

  created() {
    this.count = 0;

    this.tid = null;
  },

  ready() {
    if (this.countdown && !this.hasDetached) {
      this.timeRunner();
    }
  },

  methods: {
    timeRunner() {
      clearTimeout(this.tid);
      if (!this.countdown) return;
      const { activityNotStart } = this;
      const { end } = this.countdown;
      const now = Date.now();
      const remain = end - now;
      // 交互逻辑, 未开始是“仅剩” 不是”还剩“
      let remainDesc = '';
      if (remain > DAY && !activityNotStart) {
        remainDesc = this.countdown.desc.replace('仅剩', '还剩');
      } else {
        remainDesc = this.countdown.desc;
      }
      // 倒计时结束
      if (remain <= 0 && this.count > 0) {
        this.setYZData({
          remainDesc: '已结束',
          ended: true
        });
        this.$dispatch('countdownEnded');
        return;
      }
      if (remain <= 0) {
        this.setYZData({
          remainDesc: '已结束',
          ended: true
        });
        return;
      }
      this.count += 1;

      const remainObj = remainTimestampToArray(remain);
      remainObj.milliseconds = `${remainObj.milliseconds}`.substr(0, 2);

      this.setYZData({
        remainDesc,
        remainObj
      });

      let delta = 500;
      if (!remainObj.days) {
        delta = 50;
      }
      this.tid = setTimeout(this.timeRunner.bind(this), delta);
    }
  },

  detached() {
    this.hasDetached = true; // 这个存在比ready先执行的情况
    clearInterval(this.tid);
    this.tid = null;
  }
});
