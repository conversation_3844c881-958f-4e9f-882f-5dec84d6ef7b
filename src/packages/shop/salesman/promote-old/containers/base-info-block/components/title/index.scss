@import '~mixins/index.scss';

.goods-title {
  padding: 0;
  margin: 8px 0;

  &__tags {
    margin-bottom: 8px;

    &:empty {
      margin-bottom: 0;
    }
  }

  &__tag-label {
    display: inline-block;
    height: 16px;
    vertical-align: middle;
  }

  &__tag-desc {
    display: inline-block;
    margin-left: 8px;
    font-size: 12px;
    line-height: 20px;
    color: $gray-dark;
    vertical-align: middle;
  }

  &__retail-logo {
    background-color: $blue !important;
    color: $white;
    text-align: center;
  }

  &__box {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    min-height: 30px;
  }

  &__left {
    width: 100%;
    box-sizing: border-box;
  }

  &__main {
    display: block;
    width: 88%;
    font-size: 16px;
    line-height: 20px;
    font-weight: 500;
    text-align: left;
    word-break: break-all;
    word-wrap: break-word;

    span {
      vertical-align: middle;
    }
  }

  &__sub {
    margin: 8px 0;
    color: $gray-dark;
    font-size: 13px;
  }

  &__more {
    position: absolute;
    right: -16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background: $background-color;
    padding: 4px 6px 4px;
    border-top-left-radius: 1rem;
    border-bottom-left-radius: 1rem;
  }

  &__more-item-word {
    margin-left: 2px;
    font-size: 12px;
    color: $gray-darker;
    line-height: normal;
  }
}

.goods-title::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  bottom: 0;
  left: 0;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(0.5);
}
