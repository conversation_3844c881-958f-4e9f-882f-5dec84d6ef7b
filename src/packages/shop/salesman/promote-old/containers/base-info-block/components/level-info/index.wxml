  <view class="level-info-wrap" wx:if="{{ showLevelBlock }}">
    <van-cell custom-class="level-van-cell" is-link bind:tap="goLevelCenter">
      <view slot="title">
        <view class="level-info__info-wrap" wx:if="{{ levelUpgradeTextData.showNextLevelRate }}">
          <view wx:if="{{ levelUpgradeTextData.nextLevelName && !commissionInfo.inKeepLevel }}" class="level-info__desc">
             <view class="level-info__desc">升级至{{ levelUpgradeTextData.nextLevelName }}</view>
          </view>
          <view wx:else class="level-info__desc">
             <view class="level-info__desc">当前为{{ commissionInfo.levelName }}</view>
          </view>
          <view class="level-info__desc" wx:if="{{levelUpgradeTextData.nextLevelProfit}}">
            <view class="level-info__desc">
              <view class="level-info__desc">，佣金最高赚 </view>
              <view class="level-info__commission">{{ levelUpgradeTextData.nextLevelProfit }}</view>
            </view>
          </view>
          <view class="level-info__desc" wx:else>
            <view class="level-info__desc">佣金提升至</view>
            <view class="level-info__commission-rate">{{ levelUpgradeTextData.nextLevelCpsRate }}</view>
            <view wx:if="{{levelUpgradeTextData.nextLevelProfit}}" class="level-info__desc">
              <view class="level-info__desc">，佣金最高赚 </view>
              <view class="level-info__commission">{{ levelUpgradeTextData.nextLevelProfit }}</view>
            </view>
          </view>
        </view>
        <view wx:else class="level-info__info-wrap">
          <view class="level-info__desc">
            {{ commissionInfo.levelUpgradeInfo }}
          </view>
        </view>
      </view>
    </van-cell>
  </view>
