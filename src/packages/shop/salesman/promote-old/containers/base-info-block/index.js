import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';

const app = getApp();

GoodsComponent({
  name: 'BaseInfoBlock',

  state: ['goods', 'shop'],

  getters: ['showActivityBanner', 'showGuaranteeBar', 'baseInfoBarTag', 'isWxPromise'],

  methods: {
    guaranteeOnShow(val) {
      app.logger
        && app.logger.log({
          et: 'view',
          ei: 'enterpage_goods_detail',
          en: '有赞担保曝光',
          params: {
            guarantee_on: val?.detail,
          },
          si: app.getKdtId(),
        });
    },
  },
});
