  <view class="task-award-main" wx:if="{{ taskAwardData.hasRunningTask }}">
    <van-cell
      custom-class="task-award-van-cell"
      is-link
      data-query="{{ {taskId:taskAwardData.taskId, kdtId} }}"
      bind:tap="goTaskDetail"
    >
      <view slot="title">
        <view class="task-award-content">
          <text class="task-award-info__title">奖励</text>
          <van-icon
            class="task-award-info__icon"
            size="1.2em"
            name="//img01.yzcdn.cn/upload_files/2020/06/21/FmQTYIviTCZOXBJSMfzWdGxAePsg.png"
          />
          <text class="task-award-info__desc red-theme">做任务拿奖励</text>
          <text class="task-award-info__desc">各种好礼等你来拿</text>
        </view>
      </view>
    </van-cell>
  </view>
