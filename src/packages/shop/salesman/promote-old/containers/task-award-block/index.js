import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import { mapActions } from '@youzan/vanx';
import { getUrl, TASK_DETAIL } from '@/packages/shop/salesman/promote/common/page-path';
import { jumpLink } from '@/packages/shop/salesman/promote/utils';
import { LOGGER_CONFIG } from '@/packages/shop/salesman/promote/common/config';

GoodsComponent({
  name: 'task-award-block',

  state: ['taskAwardData'],

  getters: ['kdtId'],

  ready() {
    this.getTaskAwardData();
  },

  methods: {
    ...mapActions(['getTaskAwardData', 'setTrackLooger']),
    goTaskDetail({ currentTarget }) {
      const { query } = currentTarget.dataset;
      this.setTrackLooger(LOGGER_CONFIG.clickRewardCenter);
      jumpLink(getUrl(TASK_DETAIL, query));
    }
  }
});
