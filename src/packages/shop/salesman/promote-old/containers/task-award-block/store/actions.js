import { getTaskByItemId } from '@/packages/shop/salesman/promote/api';

export default {
  getTaskAwardData({ getters, commit }) {
    const param = {
      itemId: getters.itemId,
      needShowNextGap: true,
    };
    getTaskByItemId(param)
      .then((res) => {
        if (res) {
          const { hasRunningTask } = res;
          if (hasRunningTask) {
            commit('updateSalesmanActivityTags', { text: '任务奖励' });
          }
          commit('updateSalesmanTaskAwardData', res);
        }
      })
      .catch((e) => {
        console.error(e);
      });
  },
};
