<view
  class="trade-carousel"
  style="top: {{ topBarHeight }}px"
>
  <view
    class="trade-carousel__swipe"
  >
    <block wx:for="{{ tradeRecords }}" wx:for-item="tradeRecord" wx:key="payTime">
      <view
        wx:if="{{ tradeRecord.showEnter || tradeRecord.showLeave }}"
        class="trade-carousel__swipe-item {{ tradeRecord.showEnter ? 'move-enter-active': '' }} {{ tradeRecord.showLeave ? 'move-leave-active': '' }} "
      >
        <image
          src="{{ tradeRecord.avatar }}"
          class="trade-carousel__swipe-avatar"
        />
        <text class="trade-carousel__swipe-text">{{ tradeRecord.desc }}</text>
      </view>
    </block>
  </view>
</view>
