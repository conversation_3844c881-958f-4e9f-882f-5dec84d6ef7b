// import { getRandomAvatar } from './avatars';
import { fullfillImage, moneyFormat } from '@/packages/shop/salesman/promote/utils';

function getTradeDesc({ dsNickName = '', cpsMoneyFormat = 0 }) {
  return `${dsNickName} 成功卖出该商品${cpsMoneyFormat > 0 ? `赚${cpsMoneyFormat}元` : ''}`;
}

export default {
  setTradeRecords(state, payload) {
    state.tradeRecordsV2 = payload.map((tradeRecord) => {
      tradeRecord.show = false;
      // tradeRecord.avatar = getRandomAvatar();
      const { cpsMoney, avatar } = tradeRecord;
      tradeRecord.avatar = fullfillImage(avatar);
      tradeRecord.cpsMoneyFormat = moneyFormat(cpsMoney, true);
      tradeRecord.desc = getTradeDesc(tradeRecord);
      return tradeRecord;
    });
  }
};
