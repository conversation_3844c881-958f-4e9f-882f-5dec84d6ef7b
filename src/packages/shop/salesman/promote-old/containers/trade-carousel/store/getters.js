import get from '@youzan/weapp-utils/lib/get';

// 0-列表模式，1-悬浮滚动
const TRADE_MODE = {
  list: 0,
  float: 1
};

export default {
  showBuyRecordListStyle(state, getters) {
    return getters.parsedGoodsDetailBuyRecord.indexOf(TRADE_MODE.list) > -1;
  },

  showBuyRecordFloatStyle(state, getters) {
    return getters.parsedGoodsDetailBuyRecord.indexOf(TRADE_MODE.float) > -1;
  },

  parsedGoodsDetailBuyRecord(state) {
    const goodsDetailBuyRecord = get(state, 'shopConfig.goodsDetailBuyRecord', null);
    const { showBuyRecord } = state.shopConfig;

    /*
      如果没有解析数据就都显示； 0-列表模式，1-悬浮滚动;
      默认值根据老的成交记录字段老处理，老的字段是显示的话，那么0，1都显示；
    */
    let parsedGoodsDetailBuyRecord = showBuyRecord ? [TRADE_MODE.list, TRADE_MODE.float] : [];

    if (goodsDetailBuyRecord) {
      try {
        parsedGoodsDetailBuyRecord = JSON.parse(goodsDetailBuyRecord) || parsedGoodsDetailBuyRecord;
      } catch (error) {
        // do nothing
      }
    }

    return parsedGoodsDetailBuyRecord;
  },
};
