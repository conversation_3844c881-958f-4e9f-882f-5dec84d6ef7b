import { getRecommendRecord } from '@/packages/shop/salesman/promote/api';

export default {
  getTradeRecordsV2({ commit, getters }) {
    const itemId = getters.itemId;

    if (!itemId) {
      return;
    }

    getRecommendRecord({
      itemId
    })
      .then((res) => {
        if (!res || !res.length) {
          return;
        }

        commit('setTradeRecords', res);
      })
      .catch(() => {
        // console.log(err);
      });
  }
};
