import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import { mapActions } from '@youzan/vanx';
import { LOGGER_CONFIG } from '@/packages/shop/salesman/promote/common/config';
import { themeColorBehaivor } from '@/packages/shop/salesman/promote/common/behaviors';

GoodsComponent({
  name: 'PromotionCard',

  state: ['accountInfo'],

  behaviors: [themeColorBehaivor],

  properties: {
    alias: String,
    // 核心卖点单位
    unit: String,

    // 核心卖点 下面的条件
    condition: String,

    // 主要标题
    title: String,

    // 有效期
    timeLimit: String,

    // 行动点文案
    discountInfo: String,

    // 有效数量
    availableNum: {
      type: Number,
      value: 0
    }
  },

  methods: {
    ...mapActions(['goCouponFetch', 'setTrackLooger']),
    handleGetCoupon() {
      this.setTrackLooger(LOGGER_CONFIG.issueCoupon);
      this.goCouponFetch(this.properties.alias);
    }
  }
});
