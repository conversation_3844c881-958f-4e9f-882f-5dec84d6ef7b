@import '~shared/common/css/_variables.scss';

.goods-promotion {
  &__more-title {
    margin: 10px auto;
    font-size: 18px;
    text-align: center;
  }

  &__popup {
    display: flex;
    line-height: 1.5;

    .common-popup__content {
      width: 100%;

      .goods-promotion__detail {
        width: 85%;

        .van-icon-arrow {
          top: 2px;
          float: right;
          font-size: 16px;
        }
      }

      .common-tag {
        display: block;
      }

      .goods-promotion__tag {
        position: relative;
        margin-top: 2px;
      }
    }

    &-cnt-title {
      margin: 16px 0 10px 0;
      font-size: 13px;
      line-height: 18px;
      color: $text-color;

      &:first-child {
        margin-top: 0;
      }
    }

    &-tip {
      font-size: 12px;
      color: #969799;
      text-align: center;
    }

    &-more {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #969799;

      &-icon {
        margin-left: 6px;
        position: relative;
        top: 2px;
      }
    }
  }

  &__more-coupon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;

    &-content {
      display: flex;
      align-items: center;
    }

    &-desc {
      font-weight: 500;
      font-size: 14px;
      color: #323233;
    }

    &-icon {
      margin-top: 2px;
    }
  }
}

