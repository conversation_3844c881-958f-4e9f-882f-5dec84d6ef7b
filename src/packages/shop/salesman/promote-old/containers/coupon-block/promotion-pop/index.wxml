<!-- 点击外侧促销模块显示的弹窗 -->
<common-pop
  class="goods-promotion__popup"
  title="发券"
  button="完成"
  show="{{ show }}"
  content-style="margin-top: 0;"
  bind:btn-click="hidePopUp"
  bind:close="hidePopUp"
>
  <view>
    <view
      wx:if="{{ optimalCouponData.hasOptimalCoupon && optimalCouponData.optimismCoupon }}"
      class="goods-promotion__popup-cnt-title"
    >
      <text>为你推荐最优券</text>
    </view>

    <!-- 最佳优惠券卡片，现在最多显示20个 -->
    <promotion-card
      wx:if="{{ optimalCouponData.hasOptimalCoupon && optimalCouponData.optimismCoupon }}"
      wx:key="{{ optimalCouponData.optimismCoupon.couponId }}"
      alias="{{ optimalCouponData.optimismCoupon.alias }}"
      title="{{ optimalCouponData.optimismCoupon.title }}"
      unit="{{ optimalCouponData.optimismCoupon.discountUnit }}"
      condition="{{ optimalCouponData.optimismCoupon.condition }}"
      extra-condition="{{ coupon.extraCondition }}"
      discount-info="{{ optimalCouponData.optimismCoupon.discountInfo }}"
      available-num="{{ optimalCouponData.optimismCoupon.availableNum }}"
      time-limit="{{ optimalCouponData.optimismCoupon.timeLimit }}"
      valid="{{ optimalCouponData.optimismCoupon.valid }}"
    />

    <view
      wx:if="{{ couponsList.length }}"
      class="goods-promotion__popup-cnt-title">
      {{ optimalCouponData.hasOptimalCoupon ? '更多专属优惠券' : '全部专属优惠券' }}
    </view>

    <promotion-card
      wx:for="{{ couponsList }}"
      wx:key="alias"
      wx:for-item="coupon"
      data-index="{{ index }}"
      alias="{{ coupon.alias }}"
      title="{{ coupon.title }}"
      unit="{{ coupon.discountUnit }}"
      condition="{{ coupon.condition }}"
      discount-info="{{ coupon.discountInfo }}"
      available-num="{{ coupon.availableNum }}"
      time-limit="{{ coupon.timeLimit }}"
      valid="{{ coupon.valid }}"
    />

    <view
      wx:if="{{ showMoreCoupon }}"
      class="goods-promotion__more-coupon"
      bind:tap="handleGetMore"
    >
      <view class="goods-promotion__more-coupon-content">
        <view class="goods-promotion__more-coupon-desc">更多优惠券</view>
        <van-icon class="goods-promotion__more-coupon-icon" name="arrow" color="#dcdee0" />
      </view>
    </view>
  </view>
</common-pop>
