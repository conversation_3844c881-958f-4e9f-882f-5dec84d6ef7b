import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import { mapActions } from '@youzan/vanx';

GoodsComponent({
  name: 'PromotionPop',

  properties: {
    show: Boolean,
  },

  data: {
    isShow: false,
  },

  state: ['displayPop', 'accountInfo'],

  getters: ['couponsList', 'optimalCouponData', 'showMoreCoupon'],

  methods: {
    ...mapActions([
      'toggleCouponPopUp',
      'handleGetCoupon',
      'showMoreOptimalCoupon',
    ]),

    hidePopUp() {
      this.triggerEvent('close');
    },

    handleGetMore() {
      // ZNB.navigate({
      //   url: `/wscump/salesman/center-v2/coupon`,
      // });
    },
  },
});
