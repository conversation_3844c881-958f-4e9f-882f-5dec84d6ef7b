import { addSalesmanParams } from 'shared/utils/salesman-params-handler';
import navigate from '@/helpers/navigate';
import { SALESMAN_TYPE } from '@/packages/salesman/common/constant';

export default {
  goCouponFetch({ state }, alias) {
    const { sl, salesmanType } = state.accountInfo;
    const isExternalSalesman = salesmanType !== SALESMAN_TYPE.INTERNAL_SALESMAN;
    const url = `/packages/user/coupon/detail/index?type=promocard&alias=${alias}`;
    navigate.navigate({
      url: isExternalSalesman
        ? addSalesmanParams({ url, sl })
        : url
    });
  }
};
