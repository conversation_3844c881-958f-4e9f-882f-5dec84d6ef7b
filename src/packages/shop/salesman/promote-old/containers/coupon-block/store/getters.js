import { SALESMAN_TYPE } from '@/packages/salesman/common/constant';

export default {
  // 是否展示优惠券模块，外链不展示
  showGoodsCoupon(state) {
    // 如果无优惠券列表，不展示优惠券模块
    const { goodsCouponsInfo } = state;
    const { couponList, optimismCoupon } = goodsCouponsInfo;
    const hasOptimalCoupon = !!optimismCoupon;
    if (!hasOptimalCoupon && (!couponList || !couponList.length)) {
      return false;
    }

    return true;
  },

  couponsList(state) {
    const { goodsCouponsInfo } = state;
    const { couponList } = goodsCouponsInfo;
    if (!couponList || !couponList.length) {
      return [];
    }
    return couponList;
  },

  couponsNumber(state) {
    const { goodsCouponsInfo } = state;
    const { couponList, optimismCoupon } = goodsCouponsInfo;
    const hasOptimalCoupon = !!optimismCoupon;
    if (hasOptimalCoupon) {
      return couponList.length + 1;
    }
    return couponList.length;
  },

  showMoreCoupon(state, getters) {
    const hasMoreList = getters.couponsList.length > 10;
    const { salesmanType } = state.accountInfo;
    const isExternalSalesman = salesmanType !== SALESMAN_TYPE.INTERNAL_SALESMAN;
    return hasMoreList && isExternalSalesman;
  },

  optimalCouponData(state, getters) {
    const { couponsList } = getters;
    const { goodsCouponsInfo } = state;
    const { optimismCoupon } = goodsCouponsInfo;
    const hasOptimalCoupon = !!optimismCoupon;
    const optimalCouponData = {
      hasOptimalCoupon,
      couponText: hasOptimalCoupon
        ? `发放最优券给客户，最多可省${optimismCoupon.discountInfo}${optimismCoupon.discountUnit}`
        : '给好友发券，好友领取后可绑定客户关系',
      optimismCoupon,
    };
    if (!optimismCoupon && couponsList && couponsList.length) {
      optimalCouponData.optimismCoupon = couponsList[0];
    }
    return optimalCouponData;
  }
};
