import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';

GoodsComponent({
  name: 'GoodsCoupon',

  getters: ['couponsList', 'showGoodsCoupon', 'couponsNumber', 'optimalCouponData'],

  data: {
    isShow: false,
  },

  methods: {
    showPopUp({ currentTarget }) {
      const { couponsNumber } = currentTarget.dataset;
      if (couponsNumber > 1) {
        this.setYZData({
          isShow: true
        });
      }
    },

    hidePopUp() {
      this.setYZData({
        isShow: false
      });
    },
  },
});
