@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.goods-coupon {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  height: 44px;

  .m-promotion__label {
    margin-right: 3px;
  }

  &__info {
    position: relative;
    font-size: 12px;
    color: #969799;
    line-height: 20px;
  }

  &__desc-ctn {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: clip;

    > span {
      display: inline-block;
      margin-left: 8px;
    }
  }

  &__icon-right {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #969799;
  }

  &__desc-item {
    position: relative;
    height: 16px;
    margin-left: 4px;
    text-align: center;
    line-height: 16px;
    background-size: 100% 100%;
    border-radius: 3px;

    & > span {
      font-size: 12px;
      transform: scale(0.84);
      display: inline-block;
    }

    &:first-child {
      margin-left: 0;
    }
  }
}
