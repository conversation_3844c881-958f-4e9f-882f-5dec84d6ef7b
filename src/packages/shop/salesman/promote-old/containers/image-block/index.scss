.image-block {
  position: relative;
}

.goods-video {
  height: 100%;
  width: 100%;
  display: block;
  position: absolute;
  left: 0;
  top: 0;

  &.hidden {
    z-index: -1;
  }
}

.swp {
  position: relative;
  display: block;
  width: 100%;
  margin: 0;
  padding: 0;
  height: 100%;
  // background-color: $background-color;

  &__item {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__img {
    display: block;
    width: 100%;
    height: 100%;
    background: #fff;
  }

  &__play-video {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 55px;
    height: 55px;
    background-image: url('https://img01.yzcdn.cn/public_files/2019/07/25/d033c4ff8d85e23a18a6becc46411d09.png');
    background-size: 55px 55px;
    transform: translate(-50%, -50%);
    text-indent: -999px;
  }

  &__indicator {
    position: absolute;
    right: 15px;
    bottom: 15px;
    box-sizing: border-box;
    min-width: 32px;
    padding: 2px 7px;
    border-radius: 999px;
    font-size: 12px;
    line-height: 16px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.36);
  }
}

.goods-video-play {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 55px;
  height: 55px;
  background-image: url(https://img01.yzcdn.cn/public_files/2019/03/05/e60e0173ebce201fa56135e0b4394e98.png);
  background-size: 55px 55px;
  transform: translate(-50%, -50%);
  text-indent: -999px;
}
