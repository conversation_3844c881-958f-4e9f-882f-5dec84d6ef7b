<view
  style="height: {{ goodsPictureHeight }}px"
  id="js-nav-goods"
  class="image-block"
>
  <swiper
    loop="{{ loop }}"
    autoplay="{{ showGoodsVideo ? 0 : 3000 }}"
    show-indicators="{{ showIndicator && goodsPictures.length > 0 }}"
    class="swp"
    indicator-color="white"
    circular
    bind:change="changeSwiperPage"
  >
    <swiper-item
      wx:for="{{ goodsPictures }}"
      wx:key="index"
      wx:for-index="index"
      class="swp__item"
      data-index="{{ index }}"
      bind:tap="previewImage"
    >
      <block wx:if="{{ index === 0 && showGoodsVideo }}">
        <block wx:if="{{ hideVideo }}">
          <image mode="aspectFit" src="{{ item }}" class="swp__img" />
          <view
            class="goods-video-play"
            catch:tap="handleVideoPlayClicked"
          />
        </block>
          <goods-video
            wx:if="{{ showGoodsVideo }}"
            class="goods-video {{ hideVideo ? 'hidden' : ''}}"
            bind:video-ready="onVideoReady"
            bind:hide-video="hideVideo"
          />
      </block>
      <image wx:else mode="aspectFit" src="{{ item }}" class="swp__img" >
    </swiper-item>
  </swiper>

  <view hidden="{{ !hideVideo && currentSwpPage === 0 }}" slot="indicator" class="swp__indicator">
    {{ currentSwpPage + 1 }}/{{ goodsPictures.length }}
  </view>
</view>
