import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import api from '@/packages/shop/salesman/promote/utils/api';
import { setLogger } from '@/packages/shop/salesman/promote/common/logger';
import getSystemInfo from 'shared/utils/browser/system-info';

GoodsComponent({
  name: 'ImageBlock',

  state: ['video'],

  getters: ['goodsPictures', 'goodsPictureHeight', 'showGoodsVideo'],

  data: {
    // 是否自动轮播
    loop: true,

    hideVideo: true,

    // 是否显示视频播放控制条
    showPlayer: false,
    // 是否显示轮播数目提示
    showIndicator: true,
    // 当前swp页
    currentSwpPage: 0,
    // 视频播放统计标志
    videoPaidCountFlag: false,
    windowHeight: getSystemInfo().windowHeight
  },

  methods: {
    onVideoReady({ detail: videoComponent }) {
      this.videoComponent = videoComponent;
    },

    hideVideo() {
      this.setYZData({
        hideVideo: true
      });
    },
    // 点击播放按钮
    onPlayClick() {
      // 流量计费统计
      if (!this.videoPaidCountFlag) {
        this.videoPaidCountFlag = true;
        api.get({
          url: this.video.countPlayedUrl,
        });
      }
      this.showPlayer = true;
      this.$refs.video[0].playVideo();
    },

    previewImage({ currentTarget }) {
      setLogger({
        et: 'click', // 事件类型
        ei: 'click_goods_pic', // 事件标识
        en: '点击头图', // 事件名称
      });

      const { index } = currentTarget.dataset;
      if (this.data.showGoodsVideo) {
        if (+index === 0) {
          return;
        }
      }

      const images = this.data.goodsPictures;
      wx.previewImage({
        current: images[index],
        urls: images
      });
    },

    changeSwiperPage({ detail }) {
      // 视频展示的情况需要暂停或播放
      if (this.data.showGoodsVideo && !this.data.hideVideo) {
        const { currentSwpPage } = this.data;
        const picsLen = this.data.goodsPictures.length;
        // 视频在swiper中被切换走时，暂停播放
        const isBackVideo = (currentSwpPage === 1 || picsLen - 1 === currentSwpPage) && detail.current === 0;
        const isLeaveVideo = (detail.current === 1 || picsLen - 1 === detail.current) && currentSwpPage === 0;

        if (isBackVideo || isLeaveVideo) {
          this.videoComponent.recover();
        }
      }

      this.setYZData({
        currentSwpPage: detail.current
      });
    },

    handleVideoPlayClicked() {
      this.setYZData({
        hideVideo: false
      });
      this.videoComponent.play();
    }
  },
});
