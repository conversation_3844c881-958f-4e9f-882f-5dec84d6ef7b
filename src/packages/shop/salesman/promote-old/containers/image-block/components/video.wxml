<video
  id="goods-video-id" 
  class="goods-video"
  objectFit="contain"
  hidden="{{ hidden }}"
  loop="{{ false }}"
  autoplay="{{ false }}"
  src="{{ video.videoUrl }}"
  bindended="onVideoEnded"
  bindplay="setVideoPlay" 
  bindpause="setVideoPause"
  enable-progress-gesture="{{ false }}"
  bindfullscreenchange="handleFullscreenChange"
>
  <!-- <cover-view
    hidden="{{ !showCloseIcon }}"
    class="goods-video__close-btn {{ isVideoFullscreen ? 'goods-video__close-btn--fullscreen' : '' }}"
    bind:tap="handleVideoClose"
  >
    ×
  </cover-view> -->
</video>