import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
/**
 * andriod 兼容性问题
 * 1. enable-progress-gesture 开启手势，会存在切换 swiper 时暂停/播放和手势冲突，导致播放/暂停失效
 * 2. video 在 android 手机，不能通过 display none 来控制隐藏，会导致 objectFit 变 fill
 * 3. 开发者工具设置暂停播放会触发暂停播放事件
 */
GoodsComponent({
  properties: {
    hidden: {
      type: Boolean
    }
  },

  state: ['video'],

  ready() {
    this.paused = true;
    this.triggerEvent('video-ready', this);
  },

  methods: {
    getVideo() {
      if (this.video) {
        return this.video;
      }

      this.video = wx.createVideoContext('goods-video-id', this);
      return this.video;
    },

    setVideoPlay() {
      this.playing = true;
      this.paused = false;

      this.setYZData({
        showCloseIcon: false
      });
    },

    setVideoPause(e) {
      // 表示手动点击的暂停，当切换到视频的时候，不应该播放
      if (!this.isInvokePause) {
        this.paused = true;
      }

      if (e && e.detail) {
        this.isInvokePause = false;
      }
      this.playing = false;
      this.setYZData({
        showCloseIcon: true
      });
    },

    onVideoEnded() {
      this.handleVideoClose();
    },
    // 外部传入当前是否可以播放
    recover() {
      if (this.paused) return; // 被受到暂停的不处理
      this.playing ? this.pause() : this.play();
    },

    handleVideoClose() {
      this.triggerEvent('hide-video');
    },

    pause() {
      const video = this.getVideo();
      video && video.pause();
      this.isInvokePause = true;
      this.setVideoPause();
    },

    play() {
      const video = this.getVideo();

      if (!video) {
        return;
      }

      video.exitFullScreen();
      video.play();
      this.isInvokePlay = true;
      this.setVideoPlay();
    },

    handleFullscreenChange(e) {
      const fullScreen = e.detail.fullScreen;

      // 全屏时一直显示关闭按钮
      this.setYZData({
        isVideoFullscreen: fullScreen,
        showCloseVideoBtn: fullScreen
      });
    }
  }
});
