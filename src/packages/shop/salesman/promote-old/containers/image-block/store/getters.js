import { fullfillImage } from '@/packages/shop/salesman/promote/utils';
import getSystemInfo from 'shared/utils/browser/system-info';

export default {
  // 是否展示视频
  showGoodsVideo(state) {
    return !!state.video.videoUrl;
  },
  // 商品主图
  goodsPictures(state, getters) {
    return getters.originGoodsPictures.map((item) => fullfillImage(item.url, '!750x0.jpg'));
  },
  goodsPictureHeight(state, getters) {
    let ratio = 0.75;
    getters.originGoodsPictures.forEach((item = {}) => {
      const { height = 0, width = 0 } = item;
      if (height && width) {
        ratio = Math.max(ratio, height / width);
      }
    });
    ratio = Math.min(4 / 3, ratio);
    ratio = Math.max(3 / 4, ratio);

    const winWidth = getSystemInfo().windowWidth;
    const computedHeight = Math.floor(winWidth * ratio);
    return computedHeight;
  }
};
