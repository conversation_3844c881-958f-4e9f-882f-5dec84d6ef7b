import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import { themeColorBehaivor } from '@/packages/shop/salesman/promote/common/behaviors';
import { getUrl, TASK_DETAIL } from '@/packages/shop/salesman/promote/common/page-path';
import { jumpLink } from '@/packages/shop/salesman/promote/utils';

GoodsComponent({
  name: 'task-award-tips',

  behaviors: [themeColorBehaivor],

  state: ['taskAwardData'],

  getters: ['goodsBottomDisabledTip', 'kdtId'],

  methods: {
    goTaskDetail({ currentTarget }) {
      const { query } = currentTarget.dataset;
      jumpLink(getUrl(TASK_DETAIL, query));
    }
  }
});
