
@import '~mixins/index.scss';

.diabled {
  pointer-events: none;
}

.goods-bottom-bar {
  margin: 0;
  min-height: 35px;
  line-height: 35px;
  background: #fff7cc;
  font-size: 12px;
  color: $orange;
  text-align: center;

  &--fcode > a {
    color: $blue;
  }

  &__countdown {
    font-size: 14px;
    color: $text-color;

    .num {
      padding: 0;
      line-height: 1.5;
      background-color: transparent;
      font-size: 14px;
      color: $orange;
      font-family: monospace;
      margin: 0 3px;
    }
  }

  &--space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;

    > span {
      display: flex;
      align-items: center;
      color: $gray-darker;
    }

    .van-icon-arrow {
      transform: scale(0.83);
    }
  }

  &__text {
    color: $orange;
  }
}
