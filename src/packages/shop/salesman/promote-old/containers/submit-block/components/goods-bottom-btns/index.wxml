<view class="goods-buttons">
  <van-button wx:if="{{ !disabledBtnTextInfo }}" class="self-buy-btn" round type="default" data-query="{{ jumpGoodsDetailQuery }}" bind:tap="handleToGoodsDetail">
    <view class="btn-title self-buy-btn-title">{{ buyBtnTextInfo.btnText }}</view>
    <view wx:if="{{ buyBtnTextInfo.btnDesc }}" class="btn-desc">{{ buyBtnTextInfo.btnDesc }}</view>
  </van-button>
  <salesman-cube
    class="share-btn"
    wx:if="{{ !disabledBtnTextInfo && cubeGoodsInfo }}"
    custom-icon
    scenes="item_promote"
    need-bind-relation="{{false}}"
    hide-goods-poster="{{ false }}"
    goods-info="{{ cubeGoodsInfo }}"
    special="{{ specialConfig }}"
    cube="{{ cubeConfig }}"
  >
    <van-button wx:if="{{ !disabledBtnTextInfo }}" slot="icon" class="share-btn" round color="{{ mainColor }}">
      <view class="btn-title">{{ shareBtnTextInfo.btnText }}</view>
      <view wx:if="{{ shareBtnTextInfo.btnDesc }}" class="btn-desc">{{ shareBtnTextInfo.btnDesc }}</view>
    </van-button>
  </salesman-cube>
  <van-button wx:if="{{ disabledBtnTextInfo }}" class="disabled-btn theme__button--main" round color="{{ mainColor }}" bind:tap="handleJumpHome">
    <view class="btn-title">{{ disabledBtnTextInfo.btnText }}</view>
    <view wx:if="{{ disabledBtnTextInfo.btnDesc }}" class="btn-desc">
      {{ disabledBtnTextInfo.btnDesc }}
    </view>
  </van-button>
  <task-award-tips wx:if="{{ taskAwardData.hasRunningTask && taskAwardData.nextAwardGap }}" />
</view>