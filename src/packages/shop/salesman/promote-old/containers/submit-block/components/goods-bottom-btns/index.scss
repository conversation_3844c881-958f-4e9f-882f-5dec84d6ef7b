@import '~mixins/index.scss';

.goods-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 8px;
  height: 50px;

  &::after {
    @include border-retina(bottom);
  }

  .self-buy-btn {
    width: 40vw;
    height: 42px;
    margin-right: 8px;

    button {
      width: 100%;
    }

    .btn-desc {
      color: #969799;
    }
  }

  .self-buy-btn-title {
    color: #646566;
  }

  .share-btn {
    width: 53vw;
    height: 42px;

    button {
      width: 100%;
    }
  }

  .disabled-btn {
    width: 91vw;
    height: 42px;

    button {
      width: 100%;
    }
  }

  .btn-title {
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0;
    text-align: center;
    line-height: 18px;
    margin-bottom: 2px;
  }

  .btn-desc {
    font-size: 10px;
    letter-spacing: 0;
    text-align: center;
    line-height: 12px;
  }

  &__big {
    .van-button__text {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;
      width: 100%;
    }
  }

  &__big-col {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: flex-start;
    font-size: 12px;
    line-height: 1.2;
  }

  &__font-xl {
    font-size: 16px;
  }

  &__font--s {
    transform: scale(0.83);
    transform-origin: center bottom;
  }

  .goods-buttons--disabled {
    background: $gray-light !important;
    color: $gray-dark !important;
  }

  .van-goods-action-mini-btn {
    min-width: auto;
  }

  .buyer-icon img {
    border-radius: 20px;
    object-fit: cover !important;
  }
}

@media screen and (max-width: 320px) {
  .goods-buttons {
    .goods-buttons__mini--1,
    .goods-buttons__mini--2 {
      width: 60px;
    }

    .goods-buttons__mini--3,
    .goods-buttons__mini--4 {
      width: 43px;
    }
  }
}
