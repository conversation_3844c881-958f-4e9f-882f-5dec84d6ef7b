import { GoodsComponent } from '@/packages/shop/salesman/promote/common/vanx-component';
import { mapActions } from '@youzan/vanx';
import { themeColorBehaivor } from '@/packages/shop/salesman/promote/common/behaviors';
import {
  getUrl,
  GOODS,
  HOME,
} from '@/packages/shop/salesman/promote/common/page-path';
import { jumpLink } from '@/packages/shop/salesman/promote/utils';
import { LOGGER_CONFIG } from '@/packages/shop/salesman/promote/common/config';

GoodsComponent({
  name: 'GoodsBottomBtns',

  behaviors: [themeColorBehaivor],

  state: ['taskAwardData', 'goodsActivityInfo', 'goods'],

  getters: [
    'kdtId',
    'alias',
    'buyBtnTextInfo',
    'shareBtnTextInfo',
    'disabledBtnTextInfo',
    'cubeGoodsInfo',
    'jumpGoodsDetailQuery',
  ],

  data: {
    specialConfig: true,
    cubeConfig: [['share', 'code', 'salesman', 'material', 'zoom']],
  },

  methods: {
    ...mapActions([
      'handleBigButtonClick',
      'handleMiniButtonClick',
      'setSalesmanData',
      'setTrackLooger',
    ]),
    handleToGoodsDetail({ currentTarget }) {
      const { query } = currentTarget.dataset;
      this.setTrackLooger(LOGGER_CONFIG.clickPurchase);
      jumpLink(getUrl(GOODS, query));
    },

    handleJumpHome() {
      jumpLink(getUrl(HOME));
    },
  },
});
