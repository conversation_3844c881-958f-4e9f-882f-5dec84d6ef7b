@import '~shared/common/css/_mixins.scss';
@import '~shared/common/css/_variables.scss';

.price-intro-wrap {
  position: relative;
  display: block;
  background: $white;
  margin-top: 8px;
  overflow: hidden;
}

.goods-price-intro {
  position: relative;
  box-sizing: border-box;
  font-size: 12px;
  color: $text-color;
  overflow: hidden;

  &__title {
    height: 40px;
    text-align: center;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #323233;
    line-height: 40px;
    margin: 0 16px;
    position: relative;

    &::before {
      @include border-retina(top);

      top: 20px;
    }
  }

  .white-block {
    display: inline-block;
    height: 100%;
    background: #fff;
    padding: 0 11px;
    position: absolute;
    z-index: 1;
    transform: translateX(-50%);
  }

  &__icon {
    font-size: 16px;
    margin-left: 5px;
    position: relative;
    color: #dcdee0;
    top: 4px;
    transition: transform 300ms;
  }

  &__content {
    background-color: #f7f8fa;
    border-radius: 4px;
    height: 0;
    box-sizing: border-box;
    transition: all 300ms;
    overflow: hidden;
    margin: 0 16px;

    &-show {
      height: auto;
      margin: 8px 16px 16px;
    }
  }

  &__block {
    margin-bottom: 12px;
    padding: 0 8px;
    line-height: 16px;
    color: $gray-dark;

    &-top {
      margin-top: 12px;
    }

    &--small {
      font-size: 12px;
    }
  }

  &__text {
    line-height: 20px;
    font-size: 12px;
    color: $text-color;
  }
}
