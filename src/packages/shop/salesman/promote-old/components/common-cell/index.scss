@import '~mixins/index.scss';

.common-cell {
  width: 100% !important;
  padding: 10px 16px !important;

  &__title {
    font-size: 14px;
    color: $gray-dark;
    flex: none !important;
    margin-right: 12px;
  }

  &__value {
    font-size: 14px;
    color: $text-color;
    text-align: left;

    @include multi-ellipsis(1);
  }
}

.u-activity-intro-title {
  display: block;
  font-size: 14px;
  color: $text-color !important;
  text-align: left !important;
  flex: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.u-activity-intro-title-text-right {
  @extend .u-activity-intro-title;

  text-align: right !important;
}
