import { node as request } from 'shared/utils/request';
import { reportSkynet } from 'utils/log/logv3';

class Api {
  ajax(options) {
    // if (!userStateReady) {
    //   return Promise.reject('网络异常，用户数据获取失败');
    // }
    let { method, url, data, errorMsg = '', headers = {} } = options;
    headers = {
      Accept: 'application/json, text/plain, */*',
      ...headers,
    };

    return new Promise((resolve, reject) => {
      return request({
        ...options,
        path: url,
        headers,
        data,
        withCredentials: true,
      })
        .then(data => {
          return resolve(data);
        })
        .catch((error = {}) => {
          if (/request:/.test(error.msg || '')) {
            error = {
              ...error,
              msg: '网络错误'
            };
          }
          if (error.code === 302) return reject(error);

          reject(error.msg || typeof error === 'string' ? error : errorMsg);

          reportSkynet(
            `${method} ${url}`,
            {
              method,
              url,
              data,
              code: error.code,
              message: typeof error === 'string' ? error : errorMsg,
              response: error,
            }
          );
        });
    });
  }

  jsonp(options) {
    return this.ajax({
      dataType: 'jsonp',
      ...options,
    });
  }
}

['get', 'post', 'delete', 'put'].forEach(method => {
  Api.prototype[method] = function (options) {
    return this.ajax({
      method,
      ...options,
    });
  };
});

export default new Api();
