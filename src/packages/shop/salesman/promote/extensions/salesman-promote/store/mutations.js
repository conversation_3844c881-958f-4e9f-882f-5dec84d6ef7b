import {
  formatDisplayData,
  formatGoodsAsyncData,
  formatGoodsStaticData,
  formatGoodsSkuBarData,
  formatCommissionInfo,
  formatActivityCommissionInfo,
} from './utils/parse';

const mutations = {
  updatePageDesign(state, design) {
    state.design = design;
  },

  updateServerTime(state, { clientTimestamp, serverTimestamp }) {
    state.serverDeltaTime = serverTimestamp - clientTimestamp;
    state.serverTimestamp = serverTimestamp;
    state.clientTimestamp = clientTimestamp;
  },

  SET_PAGE_SWITCH(state, pageSwitch) {
    state.pageSwitch = pageSwitch;
  },

  SET_ITEM_ID(state, itemId) {
    state.goods.id = itemId;
  },

  initStaticData(state, { pageParams, goodsData, offlineData, shopMetaInfo }) {
    formatGoodsStaticData(state, {
      pageParams,
      goods: goodsData,
      offlineData,
      shopMetaInfo,
    });
    formatGoodsSkuBarData(state);

    state.pageParams = pageParams;
  },

  initUserData(state, payload) {
    Object.assign(state.buyConfig, {
      // 是否公众号粉丝
      isFans: payload.isFans,
      // 是否必须关注购买
      noFansBuy: !payload.isFansBuy,
    });
  },

  initDisplayData(state, payload) {
    formatDisplayData(state, payload);
  },

  UPDATE_GOODS_ASYNC_DATA(state, payload) {
    formatGoodsAsyncData(state, payload);
  },

  updateGoodsActivityInfo(state, payload) {
    if (payload) {
      Object.assign(state.goodsActivityInfo, payload);
    }
  },

  updateActivityInfo(state, payload) {
    if (payload) {
      Object.assign(state.activityInfo, payload);
    }
  },

  updateGoodsCouponsInfo(state, payload) {
    Object.assign(state.goodsCouponsInfo, payload);
  },

  updateSalesmanActivityTags(state, payload) {
    const hadPayload = state.salesmanActivityTags.some(
      (item) => item.text === payload.text
    );
    if (!hadPayload) {
      state.salesmanActivityTags.push(payload);
    }
  },

  updateCalendarData(state, { skuId, calendarData }) {
    state.priceCalendarData = {
      ...state.priceCalendarData,
      activeSkuId: skuId,
      [skuId]: calendarData,
    };
  },

  updateSalesmanTaskAwardData(state, payload) {
    payload && Object.assign(state.taskAwardData, payload);
  },

  updateCommissionInfo(state, payload) {
    if (payload) {
      const formatData = formatCommissionInfo(state, payload);
      Object.assign(state.commissionInfo, formatData);
    }
  },

  updateActivityProfitCommissionInfo(state, payload) {
    if (payload) {
      const formatData = formatActivityCommissionInfo(state, payload);
      Object.assign(state.commissionInfo, formatData);
    }
  },

  // 显示/隐藏遮罩
  toggleGoodsMask(state, showMask = false) {
    state.displayPop.maskShow = showMask;
  },

  updateMiniButtons(state, btns) {
    state.miniButtons = btns;
  },

  UPDATE_SHOP_DATA(state, payload = {}) {
    state.shop = {
      ...state.shop,
      ...payload,
    };
  },
  setSalesmanData(state, payload = {}) {
    state.salesman = {
      ...state.salesman,
      ...payload,
    };
  },

  updateAccountInfo(state, payload = {}) {
    state.accountInfo = payload;
  },

  hideSharePop(state) {
    state.displayPop.sharePopShow = false;
  },

  // 显示分享
  showSharePop(state) {
    state.displayPop.sharePopShow = true;
  },

  SET_BOTTOM_HRIGHT(state, height) {
    state.bottomHeight = height;
  },

  setNavItemTopList(state, itemTopList = []) {
    state.navItemTopList = itemTopList;
  },

  // 分享信息内容展示
  updateShareCardInfo(state, cardInfo) {
    state.shareCardInfo = cardInfo;
  },
  // 分享信息内容展示
  updateThirdYunInfo(state, yunInfo) {
    state.thirdYunInfo = {
      ...state.thirdYunInfo,
      ...yunInfo,
    };
  },
  updatePromoteThemeConfig(state, config) {
    if (config) {
      state.promoteThemeConfig = {
        ...state.promoteThemeConfig,
        ...config,
      };
    }
  },
  updateUnusedQuery(state, data) {
    state.unusedQuery = {
      ...state.unusedQuery,
      ...data,
    };
  },
};

export default mutations;
