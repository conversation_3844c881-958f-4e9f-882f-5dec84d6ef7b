import { mergeStore } from './utils/helper';

import imageBlockStore from '../widgets/image-container-block/store';
import taskAwardStore from '../widgets/task-award-container-block/store';
import tradeCarouselStore from '../widgets/trade-carousel-container-block/store';
import submitStore from '../widgets/submit-container-block/store';
import baseInfoStore from '../widgets/base-info-container-block/store';
import couponBlockStore from '../widgets/coupon-container-block/store';
import goodsDetailStore from '../widgets/goods-detail-container-block/store';

const stores = [
  imageBlockStore,
  taskAwardStore,
  tradeCarouselStore,
  submitStore,
  baseInfoStore,
  couponBlockStore,
  goodsDetailStore,
];

export default stores.reduce((a, b) => mergeStore(a, b), {});
