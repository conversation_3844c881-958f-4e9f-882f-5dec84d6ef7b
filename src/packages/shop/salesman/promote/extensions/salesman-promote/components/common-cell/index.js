import { GoodsComponent } from '../../common/vanx-component';
import { jumpLink } from '../../utils';

GoodsComponent({
  name: 'CommonCell',

  properties: {
    border: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    isLink: {
      type: Boolean,
      default: false
    },
    valueClass: {
      type: String,
      default: 'common-cell__value'
    },
    titleClass: {
      type: String,
      default: 'common-cell__title'
    }
  },

  methods: {
    onClick() {
      if (this.url) {
        jumpLink(this.url);
      }
      this.triggerEvent('click');
    }
  }
});
