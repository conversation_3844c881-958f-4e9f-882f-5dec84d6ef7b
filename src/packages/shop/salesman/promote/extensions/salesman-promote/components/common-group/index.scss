@import '~shared/common/css/_variables.scss';

.common-group {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  padding: 13px 16px;
  background-color: $white;
  box-sizing: border-box;
  overflow: hidden;
  line-height: 1.5;
  font-size: 14px;

  &__label {
    color: $gray-dark;
    margin-right: 12px;
    font-size: 14px;
    white-space: nowrap;
  }

  &__value {
    position: relative;
    width: 90%;
  }

  &__arrow {
    color: $gray-dark;
    font-size: 16px;
    line-height: 24px;
    height: 24px;
  }
}
