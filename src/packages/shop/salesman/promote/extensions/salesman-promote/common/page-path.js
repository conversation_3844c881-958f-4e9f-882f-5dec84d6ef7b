import { args } from '../utils';

let pageId = 0;
export const HOME = pageId++;
export const GOODS = pageId++;
export const CART = pageId++;
export const MEMBER = pageId++;
export const TASK_DETAIL = pageId++;
export const SALESMAN_LEVEL_CENTER = pageId++;
export const SALESMAN_NEW_LEVEL_CENTER = pageId++;
export const MATERIAL_CENTER = pageId++;

export const WEBVIEW = '/pages/common/webview-page/index';

export function getWebviewPath(url, query) {
  return args.add(WEBVIEW, { src: encodeURIComponent(args.add(url, query)) });
}
export const PAGE_MAP = {
  [HOME]: '/packages/home/<USER>/index',
  [GOODS]: '/pages/goods/detail/index',
  [MEMBER]: '/packages/ump/membercard-groupon/index',
  [TASK_DETAIL]: '/packages/salesman/task-detail/index',
  [MATERIAL_CENTER]: '/packages/salesman/zone/material/index',
  [SALESMAN_LEVEL_CENTER]: (query) => getWebviewPath('/wscump/salesman/center-v2/level', query),
  [SALESMAN_NEW_LEVEL_CENTER]: (query) =>
    getWebviewPath('/wscump/salesman/center-v2/new-level', query)
};
/**
 * 统一页面路径输出
 * @param {*} pageName
 * @param {*} query
 */
export function getUrl(pageName, query = {}) {
  const getPath = PAGE_MAP[pageName];

  if (typeof getPath === 'function') {
    return args.add(getPath(query), query);
  }

  if (!getPath || typeof getPath !== 'string') {
    throw Error(`接收到一个错误的 ${pageName}，请传入正确的 pageName`);
  }
  return args.add(getPath, query);
}
