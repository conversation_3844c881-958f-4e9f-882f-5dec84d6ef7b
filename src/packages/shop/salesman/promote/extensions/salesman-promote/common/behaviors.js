
export const timerBehaivor = Behavior({
  pageLifetimes: {
    show() {
      this.timeRunner && this.timeRunner();
    },
    hide() {
      clearInterval(this.tid);
      clearTimeout(this.tid);
    }
  }
});

// 获取全店风格主题色
export const themeColorBehaivor = Behavior({
  attached() {
    this.getThemeColor();
  },
  methods: {
    getThemeColor() {
      const app = getApp();
      app &&
        app
          .getShopTheme()
          .then(({ colors }) => {
            const mainColor = colors.general;
            this.setYZData({ mainColor });
          })
          .catch((err) => {
            throw new Error(err.message);
          });
    }
  }
});
