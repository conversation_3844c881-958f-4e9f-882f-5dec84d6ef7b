import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import args from '@youzan/weapp-utils/lib/args';
import get from '@youzan/weapp-utils/lib/get';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import accMul from '@youzan/utils/number/accMul';
import accDiv from '@youzan/utils/number/accDiv';
import navigate from 'shared/utils/navigate';
import { isObject } from './lodash-helper';
import { minBy, maxBy } from './array';
import { moment } from '@youzan/weapp-utils/lib/time';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import MoneyFormat from '@youzan/weapp-utils/lib/money';
import event from 'shared/utils/app-event';
import omit from '@youzan/weapp-utils/lib/omit';

const DAY = 24 * 3600 * 1000;
const HOUR = 3600 * 1000;
const MINUTE = 60 * 1000;
const SECOND = 1000;

function getCurrentDay(date) {
  return moment(date, 'YYYY-MM-DD');
}

/**
 * @param {*} cpsRate 0.1
 * @param {*} price 338 分
 */
function getRateProfit(cpsRate, price) {
  const m = accMul(+price, +cpsRate);
  return (Math.floor(m) / 100).toFixed(2);
}

// 为了保证前端数据和后端数据一致，原生 toFixed 方法算法和后端不一致
function toFixed(number, d) {
  let s = `${number}`;
  if (!d) d = 0;
  if (s.indexOf('.') === -1) s += '.';
  s += new Array(d + 1).join('0');
  if (new RegExp(`^(-|\\+)?(\\d+(\\.\\d{0,${d + 1}})?)\\d*$`).test(s)) {
    let s = `0${RegExp.$2}`;

    const pm = RegExp.$1;

    let a = RegExp.$3.length;

    let b = true;
    if (a === d + 2) {
      a = s.match(/\d/g);
      if (parseInt(a[a.length - 1], 10) > 4) {
        for (let i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i], 10) + 1;
          if (a[i] === 10) {
            a[i] = 0;
            b = i !== 1;
          } else break;
        }
      }
      s = a.join('').replace(new RegExp(`(\\d+)(\\d{${d}})\\d$`), '$1.$2');
    }
    if (b) s = s.substr(1);
    return (pm + s).replace(/\.$/, '');
  }
  return `${number}`;
}

function formatDistance(distance) {
  if (!distance) {
    return '';
  }
  if (distance <= 100) {
    return '<100m ';
  }
  if (distance > 100 && distance <= 1000) {
    return `${toFixed(distance, 0)}m `;
  }
  if (distance > 1000 && distance < 10000) {
    return `${toFixed(distance / 1000, 1)}km `;
  }
  return '>10km ';
}

function numberToPrice(number, decimal = 2, leaveZero = false) {
  if (typeof number !== 'number') return '';
  const price = (number / 100).toFixed(decimal);
  if (leaveZero) {
    return parseFloat(price);
  }
  return price;
}

function formatNum(num = 0, unit = 2) {
  if (unit === 2) {
    return num <= 9 ? `0${num}` : `${num}`;
  }
  if (unit === 3) {
    if (num <= 9) {
      return `00${num}`;
    }
    if (num <= 99) {
      return `0${num}`;
    }
  }
  return `${num}`;
}

// 根据剩余时间戳计算剩余多少天、时、分、秒
function remainTimestampToArray(remainTime = 0) {
  let currRemain = remainTime;
  const days = Math.floor(currRemain / DAY);
  currRemain -= days * DAY;
  const hours = Math.floor(currRemain / HOUR);
  currRemain -= hours * HOUR;
  const minutes = Math.floor(currRemain / MINUTE);
  currRemain -= minutes * MINUTE;
  const seconds = Math.floor(currRemain / SECOND);
  const milliseconds = currRemain - seconds * SECOND;
  return {
    days,
    hours: formatNum(hours),
    minutes: formatNum(minutes),
    seconds: formatNum(seconds),
    milliseconds: formatNum(milliseconds, 3),
    remainArr: [days, hours, minutes, seconds, milliseconds],
  };
}

/**
 *
 * @param {*} price string or {min: number, max: number}
 * @param {*} separator
 * @returns min ~ max or min
 */
function formatPrice(price, separator = '~') {
  if (isObject(price)) {
    return numberToPrice(price.min) + separator + numberToPrice(price.max);
  }
  return numberToPrice(price);
}

// 小程序不需要考虑链接问题，直接返回
function getRedirectUrl(url) {
  return url;
}

// 小程序不需要考虑链接问题，直接返回
function buildUrl(url) {
  return url;
}

const mapKeysToCamelCase = mapKeysCase.toCamelCase;
const mapKeysToSnakeCase = mapKeysCase.toSnakeCase;

const fullfillImage = cdnImage;

function jumpLink(url) {
  navigate.navigate({ url });
}

function getCdnImageUrl(url, formater) {
  return cdnImage(url, formater || '!100x100.jpg');
}

function getDetailPath(query = {}) {
  return args.add('/pages/goods/detail/index', query);
}

function getActivityInfo({ activityAlias, activityType, activityId, umpType }) {
  let params = { activityType, activityId, activityAlias };
  if (umpType === 'seckill') {
    params = { umpAlias: activityAlias, umpType: umpType || activityType };
  } else if (umpType === 'helpCut' || umpType === 'helpcut') {
    params.umpType = umpType;
  }
  return params;
}

// 下载图片获得临时路径
function loadImage(src) {
  return new Promise((resolve, reject) => {
    wx.downloadFile({
      url: src,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (e) => {
        reject(e);
      },
    });
  });
}

function moneyFormat(price = 0) {
  return MoneyFormat(price).toYuan();
}
function splitPrice(price = 0) {
  const priceForYuan = moneyFormat(price);
  const [yuan, fen = '00'] = priceForYuan.split('.');

  return {
    yuan,
    fen,
    desc: priceForYuan,
  };
}

function or(v1, v2) {
  if (typeof v1 === 'undefined') {
    return v2;
  }

  return v1;
}

// 一个中文顶2个英文字符
function getRealLen(str) {
  // 这个把所有双字节的都给匹配进去了
  /* eslint-disable */
  return str.replace(/[^\x00-\xff]/g, '__').length;
}

export {
  or,
  event,
  get,
  args,
  omit,
  isObject,
  accMul,
  minBy,
  maxBy,
  toFixed,
  buildUrl,
  fullfillImage,
  getCdnImageUrl,
  getRedirectUrl,
  formatPrice,
  numberToPrice,
  formatDistance,
  moment,
  accDiv,
  getRateProfit,
  getCurrentDay,
  makeRandomString,
  mapKeysToCamelCase,
  mapKeysToSnakeCase,
  moneyFormat,
  jumpLink,
  loadImage,
  splitPrice,
  getDetailPath,
  getRealLen,
  remainTimestampToArray,
  getActivityInfo,
};
