import Toast from '@vant/weapp/dist/toast/toast';
import { tryLocation } from '@/helpers/lbs';
import { get } from './index';

const app = getApp();

export function getUserLocation() {
  const { lat, lng } = get(app, '$store.state.poi.location', {});

  if (lng && lat) {
    return Promise.resolve({ lng, lat });
  }

  return geoLocation();
}

function geoLocation() {
  const cache = {
    timer: null
  };
  return new Promise((resolve) => {
    cache.timer = setTimeout(() => {
      Toast('获取地理位置超时...');
      resolve({ lng: 0, lat: 0 });
    }, 5000);

    tryLocation(
      ({ lng, lat }) => {
        clearTimeout(cache.timer);
        resolve({ lng, lat });
      },
      () => {
        clearTimeout(cache.timer);
        resolve({ lng: 0, lat: 0 });
      }
    );
  });
}

// 根据经纬度计算距离
const EARTH_RADIUS = 6378137.0;

function validateLat(lat) {
  return lat >= -90 && lat <= 90;
}

function validateLng(lng) {
  return lng >= -180 && lng <= 180;
}

function validate(lat, lng) {
  return validateLat(lat) && validateLng(lng);
}

export function calculateDistance(latA, lngA, latB, lngB) {
  if (!validate(latA, lngA) || !validate(latB, lngB)) {
    return -1;
  }

  const radLat1 = (latA * Math.PI) / 180.0;
  const radLat2 = (latB * Math.PI) / 180.0;
  const a = ((latA - latB) * Math.PI) / 180.0;
  const b = ((lngA - lngB) * Math.PI) / 180.0;
  let s =
    2 *
    Math.asin(
      Math.sqrt(
        Math.pow(Math.sin(a / 2), 2) +
          Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)
      )
    );
  s *= EARTH_RADIUS;
  s = Math.round(s * 1000) / 1000;
  return s;
}
