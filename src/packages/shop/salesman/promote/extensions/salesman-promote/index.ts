// @ts-nocheck
import Event from '@youzan/weapp-utils/lib/event';
import { mapState, mapActions } from '@ranta/store';

import {
  autoEnterShop,
  checkShopStatausAfterES,
} from 'common-api/multi-shop/multi-shop-redirect';
import { mapKeysToCamelCase, args, get, jumpLink } from './utils';
import { initUserState } from './common/init-user-state';
import store from './store';
import { showWarningModal } from './common/helpers';
import { reportSkynet } from 'utils/log/logv3';
import { setExtensions } from './extensions-v1';
import openWebView from 'shared/utils/open-web-view';
import { getUrlLogParams } from 'utils/log/get-url-log-params';
import { vanxToRantaStore } from 'shared/common/base/ranta/store';
import getUniquePageKey from 'shared/utils/unique';
import { getUrl, GOODS } from './common/page-path';
import { initSalesman } from 'shared/utils/salesman-share';

const app = getApp();

export default class {
  ctx;

  store = vanxToRantaStore(store);

  constructor({ ctx }) {
    ctx.store = this.store;

    this.ctx = ctx;

    const res = mapState(
      this,
      ['shareWxData', 'jumpGoodsDetailQuery', 'thirdYunInfo'],
      {
        setSelf: true,
      }
    );

    Object.assign(this, res);

    mapActions(this, [
      'fetchThemeConfig',
      'fetchGoodsData',
      'setActivityInfo',
      'fetchUmpData',
      'updateModule',
      'updateGoodsAsyncData',
      'getGoodsRecommendRateLevelInfo',
      'initPageState',
      'setItemId',
      'updateAccountInfo',
      'resetActivityTags',
      'setSalesmanData',
      'cacheUnusedQuery',
    ]);
  }

  // 初始化分销员信息
  beforePageMount() {
    initSalesman.call(this, {
      scene: 'item_promote',
      sst: 3,
      getSalesmanData: this.setSalesmanInfo,
    });
    setExtensions(this);
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const { options, route } = currentPage;

    this.options = options;
    this.route = route;
    this.bootstrap();
  }

  onShareAppMessage() {
    const { shareWxData, thirdYunInfo } = this;
    // 合并三方的分享信息
    return { ...shareWxData, ...(thirdYunInfo.share || {}) } || {};
  }

  // 下拉刷新
  onPullDownRefresh(e) {
    Event.trigger('home:refresh');
    Event.trigger('onPullDownRefresh' + getUniquePageKey(this), e);

    this.bootstrap({
      forceRefresh: true,
    });

    // eslint-disable-next-line @youzan-open/tee/no-platform-field
    wx.stopPullDownRefresh();
  }

  onPageScroll(e) {
    Event.trigger('onPageScroll', e);
  }

  onReachBottom(e) {
    Event.trigger('onReachBottom' + getUniquePageKey(this), e);
  }

  pageDestroyed() {
    app.off('app:chainstore:kdtid:update', null, this);
  }

  setSalesmanInfo(err, res) {
    if (err) return;
    this.setSalesmanData(res);
  }

  getActivity(options) {
    const { activityType = '', umpType = '' } = options;
    let { activityAlias = '', activityId = '' } = options;

    if (
      (activityType === 'seckill' || umpType === 'seckill') &&
      !activityAlias &&
      /\w/.test(activityId)
    ) {
      activityAlias = activityId;
      activityId = '';
    }

    return {
      activityAlias,
      activityType,
      activityId,
      umpType,
    };
  }

  bootstrap(moreParams) {
    const query = mapKeysToCamelCase(this.options);

    const {
      salesmanType = '',
      sl = '',
      activityAlias,
      activityType,
      activityId,
      umpType,
      ...otherParams
    } = query;

    const handleParams = this.getActivity({
      activityAlias,
      activityType,
      activityId,
      umpType,
    });

    // 保存非分销员使用的参数，供后续跳转
    this.cacheUnusedQuery(otherParams);

    this.initGoodsBaseInfo(moreParams)
      .catch((error) => this.whenApiError(error))
      .then((goodsData) => {
        const goods = get(goodsData, 'goodsData.goods');
        // 纠正alias为最后商品详情的 alias，防止下拉刷时没有处理营销进店的情况
        this.options.alias = goods.alias;
        this.setItemId(goods.id);
        Event.trigger('salesmanDetail:loaded', goods.id);
        return initUserState(goodsData);
      })
      .then((userState) => {
        return this.updateGoodsAsyncData({
          userState,
          activityData: {
            ...handleParams,
            activityType: handleParams.activityType || handleParams.umpType,
          },
        });
      })
      .then(() => {
        this.updateAccountInfo({ salesmanType, sl });
        // eslint-disable-next-line camelcase
        const { salesChannel = '' } = query;
        this.getGoodsRecommendRateLevelInfo({
          sales_channel: salesChannel,
        }).then((res = {}) => {
          if (!res.isSalesman) {
            jumpLink(getUrl(GOODS, this.jumpGoodsDetailQuery));
          }
        });
        this.initPageState();
      })
      .catch((e) => console.log(e))
      // eslint-disable-next-line
      .then(() => wx.hideLoading());
  }

  initGoodsBaseInfo(moreParams = {}) {
    const query = mapKeysToCamelCase(this.options);

    if (!this.checkCanRequest(query)) {
      return Promise.reject('不请求商品详情');
    }

    const {
      alias = '',
      activityAlias = '',
      activityType = '',
      activityId = '',
      umpType = '',
    } = query;

    delete query.activityId;
    delete query.activityType;
    delete query.umpAlias;
    delete query.umpType;

    this.setActivityInfo(
      this.getActivity({
        activityAlias,
        activityType,
        activityId,
        umpType,
      })
    );
    // eslint-disable-next-line
    wx.showLoading({
      title: '加载中...',
    });

    const fetchGoodsParams = {
      ...query,
      ...moreParams,
      alias,
      ump_alias: activityAlias,
      ump_type: activityType || umpType,
      activityId,
      activityType,
      version: 2,
    };

    return this.fetchGoodsData(fetchGoodsParams).then((goodsData) => {
      // 处理进店逻辑
      const { isChainStore, kdtId, rootKdtId } = goodsData.shopMetaInfo;

      this.fetchThemeConfig({ kdtId: rootKdtId || kdtId });

      if (isChainStore) {
        const redirectUrl = args.add('/' + this.route, this.options);
        return autoEnterShop(
          { ...this.options, umpAlias: activityAlias, umpType, redirectUrl },
          kdtId
        ).then((subKdtId) => {
          checkShopStatausAfterES();
          if (subKdtId) {
            return this.fetchGoodsData({ ...fetchGoodsParams, subKdtId });
          }
          return Promise.resolve(goodsData);
        });
      }
      return Promise.resolve(goodsData);
    });
  }

  reportSkynet(message, ext = {}) {
    reportSkynet(message, {
      ...ext,
      // eslint-disable-next-line @youzan-open/tee/no-platform-field
      launchInfo: wx.getLaunchOptionsSync(),
    });
  }

  checkCanRequest(query) {
    if (!query.alias) {
      this.reportSkynet('商品alias为空');
      showWarningModal(
        {
          content: '访问的商品不存在',
        },
        true
      );

      return false;
    }

    return true;
  }

  whenApiError(errorRes) {
    const { code, msg, res = {}, message } = errorRes;
    let { NO_TRY } = errorRes;
    let rejectMesg = msg || message || '';
    // 可以在 node 中通过 showError 来控制报错信息要不要提示
    let { showError: showTip = true } = res.data || {};

    // 商品不存在
    if (code === 301010050 || code === 5001001) {
      NO_TRY = true;
    }

    // 总店商品访问
    if (code === 4001001) {
      showTip = false;

      this.reportSkynet('以总店KDTID访问总店商品', {
        entryKdtId: this._when_fetch_updated_kdtID_,
        currentKdtId: app.getKdtId(),
        currentOptions: this.options,
      });
      // 如果是以总店KDTID访问总店商品，然后进店后重新访问了分店商品，那后续的刷新，网点切换应该也是分店kdtId，不应该仍然存在总店访问总店的情况
      if (this._when_fetch_updated_kdtID_) {
        this.bootstrap({ subKdtId: this._when_fetch_updated_kdtID_ });
      } else {
        let hasEntrySubShop = false;

        const timer = setTimeout(() => {
          !hasEntrySubShop &&
            this.reportSkynet('可能进店失败', {
              currentKdtId: app.getKdtId(),
              currentOptions: this.options,
            });

          // eslint-disable-next-line @youzan/dmc/wx-check, @youzan-open/tee/no-platform-field
          wx.navigateTo({
            url: args.add(
              '/packages/shop-select/chain-store/shopselect/index',
              {
                redirectUrl: encodeURIComponent(
                  args.add(`/${this.route}`, this.options)
                ),
              }
            ),
          });
        }, 3000);

        app.once(
          'app:chainstore:kdtid:update',
          ({ kdtId }) => {
            hasEntrySubShop = true;
            clearTimeout(timer);
            this.bootstrap({ subKdtId: kdtId });
          },
          this
        );
      }
    }

    // 指定访问总店，强制切换店铺
    if (code === 4001002) {
      showTip = false;

      this.reportSkynet('强制以总店KDTID访问商品', {
        currentOptions: this.options,
      });

      // eslint-disable-next-line @youzan/dmc/wx-check, @youzan-open/tee/no-platform-field
      wx.navigateTo({
        url: args.add('/packages/shop-select/chain-store/shopselect/index', {
          redirectUrl: encodeURIComponent(
            args.add(`/${this.route}`, this.options)
          ),
        }),
      });
    }

    // 特殊商品重定向处理
    if (code === 302) {
      const { location } = res.data || {};

      rejectMesg = 'Redirect to ' + location;
      showTip = false;

      if (location) {
        if (/^https:/.test(location)) {
          openWebView(location);
        } else {
          // eslint-disable-next-line @youzan/dmc/wx-check, @youzan-open/tee/no-platform-field
          wx.redirectTo({
            url: args.add(location, getUrlLogParams(this.options)),
          });
        }
      }
    }

    if (code === 160900100) {
      showTip = false;
      rejectMesg = '多网点，需要跳转至网点选择';
      // eslint-disable-next-line @youzan/dmc/wx-check, @youzan-open/tee/no-platform-field
      wx.navigateTo({ url: '/packages/shop/multi-store/index/index' });
    }

    if (code === 429 || code === 101302001 || code === 101302002) {
      showTip = false;
      rejectMesg = '店铺太火爆啦，请稍后重试';
      let opt;
      try {
        opt = JSON.stringify(this.options);
      } catch (e) {
        opt = '{}';
      }
      // eslint-disable-next-line @youzan/dmc/wx-check, @youzan-open/tee/no-platform-field
      wx.redirectTo({
        url: args.add('/packages/common/limit-page/index', {
          redirectCount: 0,
          callBackUrl: `/${this.route}`,
          options: opt,
        }),
      });
    }

    showTip &&
      showWarningModal(
        {
          content: rejectMesg || '网络开了个小差，请稍后再试',
        },
        NO_TRY
      );
    return Promise.reject(errorRes);
  }
}
