<view class="material-list">
	<view wx:if="{{ !materials.list.length }}" class="material-list-empty">商家暂时没有上传素材
	</view>
	<view wx:else class="material-list-content">
		<feed-item
		  wx:for="{{ materials.list }}"
		  wx:for-item="material"
		  wx:key="index"
		  text="{{ material.text }}"
		  item="{{ material.item }}"
		  image-list="{{ material.imageList }}"
		  video="{{ material.videoData }}"
		  custom-class="feed-item"
		  item-custom-class="item-class"
		  goods-title-class="goods-title"
			link-material="{{ material.linkMaterial }}"
			material-type="{{ material.materialType }}"
			material-id="{{ material.id }}"
			logger-type="salesMtrl"
			bind:get-src="handleGetSrc"
		>
			<view slot="bottom" class="action-area van-hairline--bottom">
				<view class="action-area--text">
					<van-checkbox
						value="{{ material.syncShareToZone }}"
						data-material="{{ material }}"
						bind:change="handleChangeCheck"
						icon-size="10px"
						label-class="material-checkbox-label"
						checked-color="{{ themeMainBgColor }}"
						wx:if="{{hasPersonalSpaceAbility}}"
					>
						同步分享到空间动态
					</van-checkbox>
					</view>
				<view
				  class="action-area--right"
				  data-material="{{ material }}"
					data-index="{{ index }}"
					catchtap="handleSave"
				>
					<view class="bar-btn" style="background-color: {{ themeMainBgColor }}">
						<van-icon
							name="//img01.yzcdn.cn/upload_files/2020/08/05/Ft8yemSuSuKNAeoh7WbgdoWVfgUH.png"
							size="14px"
						/>
						<text class="span">保存素材</text>
					</view>
				</view>
			</view>
		</feed-item>
		<view
			wx:if="{{ showMoreMaterial }}"
			class="more-material"
			data-query="{{ { itemId, kdtId } }}"
			bind:tap="handleGetMore">
      <view class="more-material-content">
        <text class="more-material-desc">查看更多素材</text>
        <van-icon name="arrow" size="16px" color="#c8c9cc" />
      </view>
    </view>
		<view wx:if="{{ loading && !finished }}" class="material-list-content--loading">
			<van-loading type="spinner" />
		</view>
	</view>
	<van-dialog
		use-slot
		show="{{ showSaveTip }}"
		bind:close="onClose"
		width="240px"
		confirmButtonText="我知道了"
	>
		<view class="tips-wrap">
			<view class="tips-content">
				<view wx:for="{{ tipsData }}" wx:for-item="tip" class="tip-item {{ tip.status ? 'comm-text' : 'error-text'}}">
					<van-icon wx:if="{{ tip.status }}" name="passed" />
					<van-icon wx:else name="close" />
					<text class="tip-text">{{ tip.text }}</text>
				</view>
			</view>
		</view>
	</van-dialog>
</view>
