import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

export default {
  // 商品详情展示类目
  goodsDetailTabs(state) {
    const tabs = [];

    const { list } = state.materials;
    if (list && list.length > 0) {
      tabs.push({
        type: 'material',
        desc: '精选素材',
      });
    }

    tabs.push({
      type: 'goods',
      desc: '商品详情'
    });

    return tabs;
  },

  // 没有设置商品素材tab定位到商品tab
  activeDetailTabIndex() {
    // const { list } = state.materials;
    // if (list && list.length > 0) {
    //   return 0;
    // }

    return 0;
  },
  // 是否是普通版商品详情
  isNormalTplStyle() {
    // 小程序内，不支持商品详情极速版
    return true;
  },

  shopLogo({ shop }) {
    const formatedImage = shop.image || shop.logo;
    return /\?/.test(formatedImage) ? formatedImage : cdnImage(formatedImage, '!100x100.png');
  },

  // 店铺链接
  shopLink(state) {
    const { id } = state.multistore || {};
    let { url = '' } = state.shop;

    if (id) {
      const symbol = ~url.indexOf('?') ? '&' : '?';
      url += `${symbol}oid=${id}`;
    }

    return url;
  }
};
