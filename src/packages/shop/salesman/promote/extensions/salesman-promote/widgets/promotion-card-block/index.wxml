<theme-view
  class="coupon-item"
  custom-class="coupon-item__inner"
>
  <view class="coupon-item__head">
    <view class="coupon-item__banner">
      <theme-view
        bg="main-bg"
        opacity="0.04"
        borderAfterStyle="height: 80px;"
        class="banner-theme"
        custom-class="custom-height"
      >
        <image
          class="banner-image"
          src="https://img01.yzcdn.cn/upload_files/2020/06/15/FhVbKAkgOCKklSEBZTgfdq75CmtY.png">
      </theme-view>
      <view class="coupon-item__head-wrap">
        <view class="coupon-item__price-top">
          <theme-view
            color="general"
            class="coupon-item__price-value {{ 'price-length-' + discountInfo.length }}"
            inner-style="display: inline-block; font-weight: bold;"
          >
            {{ discountInfo }}
          </theme-view>
          <theme-view
            wx:if="{{ unit }}"
            class="coupon-item__price-value-unit"
            color="general"
            inner-style="display: inline-block;"
          >{{ unit }}{{ title }}</theme-view>
        </view>
        <view
          wx:if="{{ condition }}"
          class="coupon-item__condition"
        >
          {{ condition }}
        </view>
        <view
          wx:if="{{ timeLimit }}"
          class="coupon-item__condition coupon-item__condition-1"
        >{{ timeLimit }}</view>
      </view>
    </view>
  </view>
  <theme-view class="coupon-item__action" custom-class="coupon-item__inner" bg="main-bg">
    <view class="coupon-item__action-content" bind:tap="handleGetCoupon">
      <view class="coupon-item__btn">立即发放</view>
      <view
        wx:if="{{availableNum && availableNum < 10}}"
        class="coupon-item__left-desc"
      >
        仅剩{{ availableNum }}张
      </view>
    </view>
    <view class="board-point top-point" />
    <view class="board-point bottom-point" />
    <view wx:if="{{ availableNum > 1 }}" class="coupon-item__more-border coupon-item__more-border-1 theme__border" style="border-color: {{ mainColor }};" />
    <view wx:if="{{ availableNum > 1 }}" class="coupon-item__more-border coupon-item__more-border-2 theme__border" style="border-color: {{ mainColor }};" />
  </theme-view>
</theme-view>
