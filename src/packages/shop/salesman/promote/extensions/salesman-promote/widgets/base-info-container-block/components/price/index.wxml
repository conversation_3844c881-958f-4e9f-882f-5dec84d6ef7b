<view class="goods-price">
    <theme-view
      color="general"
      class="goods-price__current"
    >
      <view>
        <text class="goods-price__current-label">¥</text>
        <text class="goods-price__current-price">{{ goods.price }}</text>
      </view>
    </theme-view>
    <view
      wx:if="{{ showOriginPrice && showOriginPrice.price }}"
      class="goods-price__origin"
    >
      <!-- <text wx:if="{{ showOriginPrice.label }}" class="goods-price__origin-label">{{ showOriginPrice.label }} </text> -->
      <text class="goods-price__origin-price">{{ showOriginPrice.price }}</text>
    </view>
    <salesman-commission commission-data="{{commissionData}}" />
  </view>
