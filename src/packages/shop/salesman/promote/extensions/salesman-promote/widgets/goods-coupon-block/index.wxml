  <view>
    <van-cell custom-class="goods-coupon" wx:if="{{ couponsNumber > 0 }}" is-link="{{ couponsNumber > 1}}" border="{{ false }}" data-coupons-number="{{ couponsNumber }}" bind:tap="showPopUp">
      <view slot="title">
        <view>
          <text class="m-promotion__label">发券({{ couponsNumber }})</text>
          <text class="goods-coupon__info">{{ optimalCouponData.couponText }}</text>
        </view>
      </view>
    </van-cell>
    <promotion-pop-block
      show="{{ isShow }}"
      bind:close="hidePopUp"
    />
  </view>