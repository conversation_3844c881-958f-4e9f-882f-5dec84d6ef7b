import { mapState, mapActions } from '@ranta/store';
import { RantaWidget } from 'shared/common/base/ranta/widget';

RantaWidget({
  attached() {
    mapState(this, ['design', 'displayPop']);
    mapActions(this, ['setSalesmanData', 'showSharePop', 'getNavItemTop']);
  },

  options: {
    multipleSlots: true,
  },

  data: {
    loading: true,
    showShopPopManager: false,
    bottomFixedHeight: 0,
  },

  observers: {
    design(val) {
      val &&
        setTimeout(() => {
          this.setData({ loading: false });
        }, 150);
    },
  },

  ready() {
    const { design = [] } = this;
    design.length > 0 &&
      setTimeout(() => {
        this.setData({ loading: false });
      }, 150);

    setTimeout(() => {
      this.setData({
        showShopPopManager: true,
      });
    }, 2000);
  },

  methods: {
    // 获取导航栏内容距离顶部值
    getNavItemTop(e) {
      this.getNavItemTop({
        self: this,
        scrollTop: e.detail,
      });
    },
  },
});
