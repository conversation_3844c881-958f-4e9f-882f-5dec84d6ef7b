import { mapState } from '@ranta/store';
import { RantaWidget } from 'shared/common/base/ranta/widget';
import api from '../../utils/api';
import { setLogger } from '../../common/logger';
import getSystemInfo from 'shared/utils/browser/system-info';

RantaWidget({
  attached() {
    mapState(this, [
      'goodsPictures',
      'goodsPictureHeight',
      'showGoodsVideo',
      'video',
    ]);
  },

  data: {
    // 是否自动轮播
    loop: true,

    hideVideo: true,

    // 是否显示视频播放控制条
    showPlayer: false,
    // 是否显示轮播数目提示
    showIndicator: true,
    // 当前swp页
    currentSwpPage: 0,
    // 视频播放统计标志
    videoPaidCountFlag: false,
    windowHeight: getSystemInfo().windowHeight,
  },

  methods: {
    onVideoReady({ detail: videoComponent }) {
      this.videoComponent = videoComponent;
    },

    hideVideo() {
      this.setData({
        hideVideo: true,
      });
    },
    // 点击播放按钮
    onPlayClick() {
      // 流量计费统计
      if (!this.videoPaidCountFlag) {
        this.videoPaidCountFlag = true;
        api.get({
          url: this.data.video.countPlayedUrl,
        });
      }
      this.showPlayer = true;
      this.$refs.video[0].playVideo();
    },

    previewImage({ currentTarget }) {
      setLogger({
        et: 'click', // 事件类型
        ei: 'click_goods_pic', // 事件标识
        en: '点击头图', // 事件名称
      });

      const { index } = currentTarget.dataset;
      if (this.showGoodsVideo) {
        if (+index === 0) {
          return;
        }
      }

      const images = this.goodsPictures;
      // eslint-disable-next-line @youzan-open/tee/no-platform-field
      wx.previewImage({
        current: images[index],
        urls: images,
      });
    },

    changeSwiperPage({ detail }) {
      // 视频展示的情况需要暂停或播放
      if (this.showGoodsVideo && !this.data.hideVideo) {
        const { currentSwpPage } = this.data;
        const picsLen = this.goodsPictures.length;
        // 视频在swiper中被切换走时，暂停播放
        const isBackVideo =
          (currentSwpPage === 1 || picsLen - 1 === currentSwpPage) &&
          detail.current === 0;
        const isLeaveVideo =
          (detail.current === 1 || picsLen - 1 === detail.current) &&
          currentSwpPage === 0;

        if (isBackVideo || isLeaveVideo) {
          this.videoComponent.recover();
        }
      }

      this.setData({
        currentSwpPage: detail.current,
      });
    },

    handleVideoPlayClicked() {
      this.setData({
        hideVideo: false,
      });
      this.videoComponent.play();
    },
  },
});
