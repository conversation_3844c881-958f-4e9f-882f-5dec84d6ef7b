import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import { themeColorBehaivor } from '../../common/behaviors';
import { getUrl, GOODS, HOME } from '../../common/page-path';
import { jumpLink } from '../../utils';
import { LOGGER_CONFIG } from '../../common/config';

RantaWidget({
  name: 'GoodsBottomBtns',

  behaviors: [themeColorBehaivor],
  attached() {
    mapState(this, [
      'taskAwardData',
      'goodsActivityInfo',
      'goods',
      'kdtId',
      'alias',
      'buyBtnTextInfo',
      'shareBtnTextInfo',
      'commissionInfo',
      'disabledBtnTextInfo',
      'cubeGoodsInfo',
      'jumpGoodsDetailQuery',
      'goodsPictures',
      'shareWxData',
    ]);
    mapActions(this, [
      'handleBigButtonClick',
      'handleMiniButtonClick',
      'setSalesmanData',
      'setTrackLooger',
      'updateThirdYunInfo',
    ]);
  },

  ready() {
    this.ctx.data.shopBaseInfo = { kdtId: this.data.kdtId };
    this.ctx.data.customOptionListConfig = {
      weapp: [['share', 'code', 'salesman', 'material', 'zoom']],
    };
    this.ctx.event.listen('share:query', this.listener.bind(this));
  },
  observers: {
    'goodsPictures, cubeGoodsInfo.**, commissionInfo.**': function (
      goodsPictures,
      goodInfo,
      commissionInfo
    ) {
      const pictures = JSON.parse(JSON.stringify(goodsPictures));
      const cubeGoodsInfo = JSON.parse(JSON.stringify(goodInfo));
      const { goodsPrice, goodsActivityInfo } = cubeGoodsInfo;
      if (commissionInfo?.fromChannel) {
        cubeGoodsInfo.profit = commissionInfo.profit;
      }
      this.ctx.data.currentActivity = { ...goodsActivityInfo };
      this.ctx.data.goodsPriceInfo = { ...goodsPrice };
      this.ctx.data.goodsSkuData = {};
      this.ctx.data.goodsBaseInfo = {
        ...cubeGoodsInfo,
        pictures,
      };
    },
  },

  methods: {
    listener(value) {
      const shareInfo = JSON.parse(
        JSON.stringify({ ...this.data.shareWxData, ...value })
      );
      this.ctx.process
        .invokePipe('setShareInfo', shareInfo)
        .then((shareData) => {
          this.ctx.data.shareInfo = shareInfo;
          const { title } = shareData;
          // 赚字改为app module后，从商祥-赚字-推广页进入分享微信时，有多个[分享],原因未知，先这样临时解决一下
          const finalTitle = title?.split('[分享]')?.slice(-1)?.[0] || title;
          this.updateThirdYunInfo({
            share: { ...shareData, title: `[分享]${finalTitle}` },
          });
        });
    },
    handleToGoodsDetail({ currentTarget }) {
      const { query } = currentTarget.dataset;
      this.setTrackLooger(LOGGER_CONFIG.clickPurchase);
      jumpLink(getUrl(GOODS, query));
    },

    handleJumpHome() {
      jumpLink(getUrl(HOME));
    },
  },
});
