import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapActions } from '@ranta/store';
import { LOGGER_CONFIG } from '../../common/config';
import { themeColorBehaivor } from '../../common/behaviors';

RantaWidget({
  name: 'PromotionCard',

  state: ['accountInfo'],

  behaviors: [themeColorBehaivor],

  properties: {
    alias: String,
    // 核心卖点单位
    unit: String,

    // 核心卖点 下面的条件
    condition: String,

    // 主要标题
    title: String,

    // 有效期
    timeLimit: String,

    // 行动点文案
    discountInfo: String,

    // 有效数量
    availableNum: {
      type: Number,
      value: 0,
    },
  },

  attached() {
    mapActions(this, ['goCouponFetch', 'setTrackLooger']);
  },

  methods: {
    handleGetCoupon() {
      this.setTrackLooger(LOGGER_CONFIG.issueCoupon);
      this.goCouponFetch(this.properties.alias);
    },
  },
});
