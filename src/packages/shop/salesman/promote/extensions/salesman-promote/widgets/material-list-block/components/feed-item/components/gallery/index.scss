.gallery {
  display: flex;
  position: relative;

  &__img-wrap {
    position: relative;
  }

  &--multiple {
    flex-wrap: wrap;
    align-content: space-between;

    .gallery__img-wrap {
      margin: 0 2% 2% 0;
      width: 32%;
      padding-top: 32%;


      &:nth-child(3n) {
        margin-right: 0;
      }
    }

    .gallery__tian {
      &:nth-child(2) {
        margin-right: 10%;
      }

      &:nth-child(3) {
        margin-right: 2%;
      }
    }

    .gallery__img {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
}
