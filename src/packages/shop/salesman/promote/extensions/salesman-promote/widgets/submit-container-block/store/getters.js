import formatCommission from '@youzan/salesman-cube-core-utils/lib/formatCommission';

export default {
  buyBtnTextInfo(state) {
    const {
      allowSelfBuy,
      profitRange,
      commissionSendType,
      commissionConfigDTO,
      customPointsName,
    } = state.commissionInfo;

    const { buyBtnMainText, buyBtnDescText } = state.promoteThemeConfig;

    if (allowSelfBuy) {
      const { maxCommissionStr } = formatCommission({
        profitRange,
        commissionSendType,
        commissionConfigDTO,
        customPointsName,
      });

      if (maxCommissionStr) {
        return {
          btnText: `${buyBtnMainText || '自购省钱'}`,
          btnDesc: `${buyBtnDescText} ${maxCommissionStr}`,
        };
      }
    }

    return {
      btnText: `${buyBtnMainText || '立即购买'} `,
    };
  },

  shareBtnTextInfo(state) {
    const {
      profitRange,
      commissionSendType,
      commissionConfigDTO,
      customPointsName,
    } = state.commissionInfo;

    const { shareBtnMainText, shareBtnDescText } = state.promoteThemeConfig;

    const { maxCommissionStr: money, compatibleCommission } = formatCommission({
      commissionSendType,
      commissionConfigDTO,
      customPointsName,
      profitRange,
    });

    if (money) {
      return {
        btnText: `${shareBtnMainText}`,
        btnDesc: `${shareBtnDescText}${compatibleCommission}`,
      };
    }

    return {
      btnText: `${shareBtnMainText}`,
    };
  },

  disabledBtnTextInfo(state, getters) {
    const { stockNum = 0, isDisplay = 1 } = state.goods;
    const { homepageUrl } = getters;
    // 商品下架 如果没有库存
    if (isDisplay !== 1 || !stockNum) {
      return {
        btnText: '查看店铺其他商品',
        url: homepageUrl,
      };
    }
    return null;
  },

  // 底部不可购买展示内容
  goodsBottomDisabledTip(state, getters) {
    const { stockNum = 0, isDisplay = 1, limitBuy } = state.goods;
    const { homepageUrl } = getters;
    // 商品下架
    if (isDisplay !== 1) {
      return {
        text: '商品下架啦',
        url: homepageUrl,
      };
    }
    // 如果没有库存
    if (!stockNum) {
      return {
        text: '该商品售罄了',
        url: homepageUrl,
      };
    }
    if (limitBuy) {
      // 特定会员购买
      return {
        text: '该商品仅限特定会员购买',
        showTask: true,
      };
    }
    return {
      text: '',
      url: '',
      showTask: true,
    };
  },

  cubeGoodsInfo(state, getters) {
    const { goodsActivityInfo } = state;
    const activityInfoParam = {
      activityId: goodsActivityInfo.activityId,
      activityType: goodsActivityInfo.activityType || goodsActivityInfo.umpType,
      activityAlias: goodsActivityInfo.activityAlias,
      activityName: goodsActivityInfo.activityName,
    };
    return {
      ...state.goods,
      goodsActivityInfo: activityInfoParam,
      goodsMainPictures: getters.goodsPictures,
      goodsOriginPictures: getters.originGoodsPictures,
      goodsPrice: {
        maxPrice: state.goods.maxPrice,
        minPrice: state.goods.minPrice,
        maxOriginPrice: state.goods.oldMaxPrice,
        minOriginPrice: state.goods.oldMinPrice,
      },
    };
  },
};
