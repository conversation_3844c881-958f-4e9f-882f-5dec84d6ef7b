@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.goods-bottom {
  position: relative;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(125, 126, 128, .16);

  .van-goods-action {
    position: static;
    box-shadow: 0 0 10px rgba(125, 126, 128, .16);

    &-button {
      height: 40px;
    }

    .van-goods-action-button--last {
      margin-right: 8px;
      border-top-right-radius: 20px;
      border-bottom-right-radius: 20px;
    }

    .van-goods-action-button--first {
      border-top-left-radius: 20px;
      border-bottom-left-radius: 20px;
    }
  }

  &.iPhone-X::after {
    @include iphone-x-after($iphone-x-bottom);
  }
}
