.material-list {
  &-empty {
    width: 160px;
    margin: 60px auto;
    font-size: 14px;
    color: #999;
    padding-top: 176px;
    background: url('https://b.yzcdn.cn/public_files/580ff7ee1659834e8d6419caf388b7a4.png')
      no-repeat center center;
    background-size: 100% 160px;
    text-align: center;
  }

  &-content {
    padding: 0 16px;
    &--loading {
      margin-top: 10px;
      text-align: center;
    }
  }

  .feed-item {
    margin-top: 16px;
  }

  .item-class {
    background: #f7f8fa;
  }

  .action-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;

    &--text {
      font-size: 12px;
      color: #999;
      height: 18px;
      line-height: 18px;
    }

    &--right {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 95px;
      height: 20px;
      position: relative;

      .clip-btn {
        height: 30px;
        padding: 0 10px;
        font-size: 13px;

        .van-button {
          min-width: 73px;
          padding: 0 10px;
        }
      }
    }

    .hide-action-area {
      display: none;
    }
  }

  .material-checkbox-label {
    font-size: 13px;
  }

  .tips-wrap {
    display: flex;
    justify-content: center;
    padding: 24px 0 14px;
  }

  .tips-content {
    display: flex;
    flex-direction: column;
    justify-items: center;
  }

  .tip-item {
    display: flex;
    align-items: center;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #323233;
    line-height: 20px;
    margin-bottom: 10px;
  }

  .tip-text {
    margin-left: 8px;
  }

  .error-text {
    color: #ee0a24;
  }

  .more-material {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;

    &-content {
      display: flex;
      align-items: center;
    }

    &-desc {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #323233;
    }
  }

  .more-material::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0;
    top: 0;
    left: 0;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(0.5);
  }
}

.goods-title {
  width: 72vw;
}

.bar {
  &-btn {
    position: relative;
    display: flex;
    width: 86px;
    height: 30px;
    align-items: center;
    font-size: 13px;
    color: #fff;
    background-color: #f44;
    border-radius: 16px;
    justify-content: center;

    > .span {
      margin-left: 4px;
    }
  }
}
