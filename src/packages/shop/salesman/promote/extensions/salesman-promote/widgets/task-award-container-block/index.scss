@import '~shared/common/css/_mixins.scss';
@import '~shared/common/css/_variables.scss';

.task-award-main {
  position: relative;
  display: block;
  background: $white;
  margin-top: 8px;
  overflow: auto;

  .task-award-van-cell {
    .van-cell__title {
      display: flex;
      align-items: center;
    }
  }

  .task-award-content {
    display: flex;
    align-items: center;
  }

  .task-award-info {
    &__title {
      font-weight: 500;
      font-size: 14px;
      color: #323233;
      line-height: 20px;
      margin-right: 8px;
    }

    &__icon {
      margin-right: 4px;
      line-height: 20px;
    }

    &__desc {
      font-size: 14px;
      letter-spacing: 0;
      line-height: 20px;
      margin-right: 6px;
    }
  }

  .red-theme {
    font-weight: 500;
    color: #f44;
  }
}
