<view wx:if="{{ countdown }}" class="activity-count-down">
  <view class="flex-column">
    <view wx:if="{{ !ended }}" class="activity-count-down__time">
      <text
        wx:if="{{ remainObj.days }}"
        class="activity-count-down__time-text day-text"
      >{{ remainObj.days }}天</text>
      <text class="activity-count-down__time-text" color="general">
        {{ remainObj.hours }}
      </text>
      <text class="">:</text>
      <text class="activity-count-down__time-text">{{ remainObj.minutes }}</text>
      <text class="">:</text>
      <text class="activity-count-down__time-text">{{ remainObj.seconds }}</text>
      <text wx:if="{{ !remainObj.days }}" class="">:</text>
      <view wx:if="{{ !remainObj.days }}" class="activity-count-down__time-box" color="general">
        <text class="activity-count-down__time-text">{{ remainObj.milliseconds }}</text>
      </view>
    </view>
  </view>
  <view
    class="activity-count-down__label {{ ended ? 'activity-end' : '' }}"
  >{{ remainDesc }}</view>
</view>