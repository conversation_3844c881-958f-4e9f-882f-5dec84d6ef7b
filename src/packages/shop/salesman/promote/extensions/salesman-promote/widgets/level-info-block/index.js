import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import {
  getUrl,
  SALESMAN_LEVEL_CENTER,
  SALESMAN_NEW_LEVEL_CENTER,
} from '../../common/page-path';
import { jumpLink } from '../../utils';
import { LOGGER_CONFIG } from '../../common/config';

RantaWidget({
  name: 'LevelInfo',

  attached() {
    mapState(this, [
      'commissionInfo',
      'levelUpgradeTextData',
      'sl',
      'showLevelBlock',
    ]);
    mapActions(this, ['setTrackLooger']);
  },

  methods: {
    goLevelCenter() {
      this.setTrackLooger(LOGGER_CONFIG.clickLevelCenter);
      if (this.data.commissionInfo.upgradeNewLevelVersion) {
        jumpLink(getUrl(SALESMAN_NEW_LEVEL_CENTER, {}));
      } else {
        jumpLink(getUrl(SALESMAN_LEVEL_CENTER, {}));
      }
    },
  },
});
