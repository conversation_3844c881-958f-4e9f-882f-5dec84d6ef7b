import { GoodsComponent } from '../../../../../../common/vanx-component';
import { mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'ActivityPrice',
  properties: {
    activityPrice: {
      type: Object,
      observer() {
        this.setOriginPrice();
      },
    },
  },
  data: {
    activityOriginPrice: '',
  },
  methods: {
    setOriginPrice() {
      let { originPrice } = this.properties.activityPrice;

      if (originPrice.indexOf('-') !== -1) {
        originPrice = originPrice.split('-')[0] + ' 起';
      }
      this.setData({
        activityOriginPrice: originPrice,
      });
    },
  },
});
