import { mapState, mapActions } from '@ranta/store';
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { getUrl, TASK_DETAIL } from '../../common/page-path';
import { jumpLink } from '../../utils';
import { LOGGER_CONFIG } from '../../common/config';

RantaWidget({
  name: 'task-award-block',
  attached() {
    mapState(this, ['taskAwardData', 'kdtId']);
    mapActions(this, ['getTaskAwardData', 'setTrackLooger']);
  },

  ready() {
    this.getTaskAwardData();
  },

  methods: {
    goTaskDetail({ currentTarget }) {
      const { query } = currentTarget.dataset;
      this.setTrackLooger(LOGGER_CONFIG.clickRewardCenter);
      jumpLink(getUrl(TASK_DETAIL, query));
    },
  },
});
