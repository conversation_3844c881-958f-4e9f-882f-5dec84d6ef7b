import { mapState } from '@ranta/store';
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { get } from '../../utils';
import { checkChainStore } from '@youzan/utils-shop';

RantaWidget({
  attached() {
    mapState({
      title: (store = {}) => store.goods && store.goods.title,
      showStoreSwitch: (store = {}) => {
        const shopMetaInfo = get(store, 'shop.shopMetaInfo', {});
        if (checkChainStore(shopMetaInfo)) {
          return true;
        }
        return false;
      },
    });
  },
});
