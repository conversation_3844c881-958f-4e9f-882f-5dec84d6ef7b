@import '~mixins/index.scss';

.level-info-wrap {
  .level-van-cell {
    margin-top: 8px;
    background: #f2f3f5;
    border-radius: 0 0 4px 4px;
  }

  .level-info__info-wrap {
    font-size: 12px;
    color: #646566;

    @include multi-ellipsis(1);
  }

  .level-info__desc {
    display: inline;
  }

  .level-info__commission,
  .level-info__commission-rate {
    display: inline;
    font-size: 16px;
    font-weight: 600;
    margin-left: 4px;
  }

  .level-info__commission {
    margin-left: 0;
  }
}
