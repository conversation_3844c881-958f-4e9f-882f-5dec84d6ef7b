import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';

RantaWidget({
  name: 'GoodsCoupon',

  attached() {
    mapState(this, [
      'couponsList',
      'showGoodsCoupon',
      'couponsNumber',
      'optimalCouponData',
    ]);
  },

  data: {
    isShow: false,
  },

  methods: {
    showPopUp({ currentTarget }) {
      const { couponsNumber } = currentTarget.dataset;
      if (couponsNumber > 1) {
        this.setData({
          isShow: true,
        });
      }
    },

    hidePopUp() {
      this.setData({
        isShow: false,
      });
    },
  },
});
