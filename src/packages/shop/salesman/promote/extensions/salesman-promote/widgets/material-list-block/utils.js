import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import args from '@youzan/weapp-utils/lib/args';
import { NoteMaterialType, LinkUrl } from '../../constants/material';

export const PAGE_RANDOM_KEY = makeRandomString(8);

export const getMaterialBannerId = ({ materialId, spm }) => {
  return `${spm}.${materialId}~goods.1~1~${PAGE_RANDOM_KEY}`;
};

export const getLinkMaterialUrl = ({ alias, materialType, kdtId }) => {
  let link = LinkUrl.feature;
  let params = {
    alias
  };

  if (NoteMaterialType === materialType) {
    link = LinkUrl.note;
    params = {
      noteAlias: alias
    };
  }

  return {
    url: args.add(link.h5Url, {
      ...params,
      sub_kdt_id: kdtId
    }),
    weappUrl: args.add(link.weappUrl, params)
  };
};

export function getFeatureImg({ coverUrl }) {
  if (coverUrl) {
    return [coverUrl];
  }
  return [];
}

export function getShopNoteImg(item) {
  if (item.coverPhotos && item.coverPhotos.length) {
    return item.coverPhotos;
  }
  if (item.coverUrl) {
    return [item.coverUrl];
  }
  if (item.noteItemBriefInfos && item.noteItemBriefInfos.length) {
    return [item.noteItemBriefInfos[0].imageUrl];
  }
  return [];
}

export function revertMap(obj) {
  return Object.keys(obj).reduce((res, key) => {
    res[obj[key]] = key;
    return res;
  }, {});
}

export const ACCOUNT_TYPE = {
  NORMAL: 0, // 普通用户
  SALESMAN: 1, // 分销员
  SALES: 2, // 导购员
  ALL: 3 // 分销员+导购员
};

// 有分销能力
const hasDirectSellerAbility = (accountType) => {
  return accountType === ACCOUNT_TYPE.SALESMAN || accountType === ACCOUNT_TYPE.ALL;
};

// 当前用户是否可以操作`动态`和`素材`
export const currentUserCanOperate = ({ canEdit, accountType }) => {
  if (canEdit && hasDirectSellerAbility(accountType)) {
    return true;
  }
  return false;
};

export function handleQueryParams(params) {
  const arr = params.split('!');
  return arr
    .map((item) => item.split('~'))
    .filter((item) => item.length > 1)
    .reduce((total, [a, b]) => {
      total[a] = b;
      return total;
    }, {});
}

/**
 * 下载接口 promise 化
 */
export function downloadFile(url) {
  return new Promise((resolve, reject) => {
    wx.downloadFile({
      url,
      success(res) {
        if (res.statusCode === 200) {
          resolve(res.tempFilePath);
        } else {
          reject();
        }
      },
      fail(err) {
        reject(err);
      }
    });
  });
}

// 保存图片 api 调用
export function saveShareImage(tempFilePath) {
  return new Promise((resolve, reject) => {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: resolve,
      fail: reject
    });
  });
}

// 保存视频 api 调用
export function saveShareVideo(tempFilePath) {
  return new Promise((resolve, reject) => {
    wx.saveVideoToPhotosAlbum({
      filePath: tempFilePath,
      success: resolve,
      fail: reject
    });
  });
}

export function downloadAndSaveViedo(url) {
  return downloadFile(url).then((tempFilePath) => {
    return saveShareVideo(tempFilePath);
  });
}

export function downloadAndSaveImage(url) {
  return downloadFile(url).then((tempFilePath) => {
    return saveShareImage(tempFilePath);
  });
}

export function getTipsData(data = []) {
  const result = [];
  const textMap = {
    copy_0: '文案/链接复制失败',
    copy_1: '文案/链接已复制',
    material_0: '素材分享到动态失败',
    material_1: '素材已分享到动态',
    image_0: '图片保存到相册失败',
    image_1: '图片已保存到相册',
    video_0: '视频保存到相册失败',
    video_1: '视频已保存到相册'
  };
  data.forEach((item) => {
    if (item && item.type) {
      const { type, status } = item;
      item.text = textMap[`${type}_${status}`];
      result.push(item);
    }
  });
  return result;
}
