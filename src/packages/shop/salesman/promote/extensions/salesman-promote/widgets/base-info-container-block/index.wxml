  <view class="module-unit">
    <!-- 商品活动价在activity里 -->
    <view class="m-base-info">
      <goods-activity
        wx:if="{{ showActivityBanner }}"
        activity-price="{{activityPrice}}"
        countdown="{{countdown}}"
        activity-not-start="{{activityNotStart}}"
        commission-data="{{commissionData}}"
        tag="{{ baseInfoBarTag }}"
        bind:count-end="countdownEnded" />
      <!-- 普通商品价格 -->
      <goods-price
        wx:if="{{ !showActivityBanner }}"
        class="u-base-info-row"
        goods="{{goods}}"
        show-origin-price="{{showOriginPrice}}"
        commission-data="{{commissionData}}" />
      <!-- 等级信息 -->
      <level-info-block />
      <!-- 商品标题 -->
      <goods-title class="u-base-info-row" goods="{{ goods }}" />
      <salesman-activity
        class="u-base-info-row last-child"
        salesman-activity-tags="{{salesmanActivityTags}}"
        sold-num-text="{{soldNumText}}" />
    </view>
    <!-- 有赞担保 -->
    <guarantee-detail-bar
      is-wx-promise="{{ isWxPromise }}"
      alias="{{ goods.alias }}"
      kdt-id="{{ shop.kdtId }}"
      bind:onShow="guaranteeOnShow" />
  </view>