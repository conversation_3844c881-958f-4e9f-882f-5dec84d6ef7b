import { GoodsComponent } from '../../../../common/vanx-component';

GoodsComponent({
  externalClasses: ['custom-class', 'custom-action-class'],

  properties: {
    maxLine: {
      type: Number,
      value: 4,
    },
    needAction: {
      type: Boolean,
      value: true,
    },
    expandText: {
      type: String,
      value: '展开',
    },
    shrinkText: {
      type: String,
      value: '收起',
    },
    content: String,
  },

  data: {
    showAction: false,
    isClamped: true,
    contentClass: '',
  },

  observers: {
    content() {
      if (this.refNode) {
        this._checkIsClamped();
      }
    },
  },

  lifetimes: {
    ready() {
      this.refNode = this.createSelectorQuery().select('#fake-content');
      this._checkIsClamped();
    },
  },

  methods: {
    handleActionClick() {
      const isClamped = !this.data.isClamped;
      this.setYZData({
        contentClass: isClamped ? '' : 'clamp__content--expanded',
        isClamped,
      });
    },

    _checkIsClamped() {
      const { maxLine, needAction } = this.data;
      if (needAction) {
        this.refNode
          .fields(
            {
              size: true,
              computedStyle: ['lineHeight', 'fontSize'],
            },
            ({ height, lineHeight, fontSize }) => {
              fontSize = this._toNumber(fontSize);
              lineHeight =
                lineHeight === 'normal'
                  ? 1.5 * fontSize
                  : this._toNumber(lineHeight);
              this.setYZData({
                maxHeight: maxLine * lineHeight,
                showAction: height > maxLine * lineHeight,
              });
            }
          )
          .exec();
      }
    },

    _toNumber(str) {
      const getDigitalReg = /([\d.])+/g;
      let num;
      try {
        num = str.match(getDigitalReg)[0];
      } catch (e) {
        console.log(e.message);
      }
      return num;
    },
  },
});
