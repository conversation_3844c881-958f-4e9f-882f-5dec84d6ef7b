import { mapState, mapActions } from '@ranta/store';
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { timerBehaivor } from '../../common/behaviors';

RantaWidget({
  behaviors: [timerBehaivor],
  name: 'fixed-bottom-block',

  attached() {
    mapActions(this, ['SET_BOTTOM_HRIGHT']);
    mapState(this, ['displayPop']);
  },

  ready() {
    this.timeRunner();
  },

  methods: {
    timeRunner() {
      this.tid =
        !this.hasDetached &&
        setInterval(() => {
          this.queryHeight();
        }, 2000);
    },
    queryHeight() {
      this.createSelectorQuery()
        .select('#module-bottom')
        .boundingClientRect((rect) => {
          if (!rect) return;

          const height = rect.bottom - rect.top;

          if (height === this._height) return;
          this._height = height;
          this.SET_BOTTOM_HRIGHT(height);
        })
        .exec();
    },
  },

  detached() {
    this.hasDetached = true; // 这个存在比ready先执行的情况
    clearInterval(this.tid);
    this.tid = null;
  },
});
