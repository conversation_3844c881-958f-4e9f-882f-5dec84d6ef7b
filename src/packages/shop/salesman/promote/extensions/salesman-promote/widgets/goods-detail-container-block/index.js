import Event from '@youzan/weapp-utils/lib/event';
import theme from 'shared/common/components/theme-view/theme';
import FeatureLoadControl from 'shared/components/showcase-options/feature-load';
import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';

const app = getApp();

RantaWidget({
  attached() {
    mapState(this, ['goodsDetailTabs', 'activeDetailTabIndex', 'shopLogo']);
    mapState(this, ['goodsDetail']);
    mapState(this, {
      goods: (store) => store.goods || {},
      shop: (store) => store.shop || {},
      showcaseExtra() {
        const { token = {}, shop = {} } = app.$store.state;
        const { chainStoreInfo = {} } = app.getShopInfoSync();
        const { isMultiOnlineShop } = chainStoreInfo;
        const lsKdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId();

        const extraData = {
          appId: app.getAppId(),
          kdtId: shop.kdtId,
          lsKdtId,
          buyerId: token.userId,
          offlineId: shop.offlineId || '',
        };
        if (this?.ctx) {
          this.ctx.data.extraData = extraData;
        }
        return extraData;
      },
    });

    mapActions(this, [
      'getShowcaseComponents',
      'getMaterialList',
      'getShopAbility',
      'SET_FEATURE_LOADED',
    ]);
    theme.getThemeColor('main-bg').then((color) => {
      this.setData({
        mainColor: color,
      });
    });
  },

  data: {
    mainColor: '',
    selectedId: 0,
    selectedType: 'material',
    showcaseBuyData: {},
    showShowcaseBuy: false,
    uniqueKey: '',
    componentsLength: 0,
  },

  observers: {
    'goodsDetail.**': function (newVal) {
      this.initShowCase(newVal);
    },
  },

  ready() {
    const kdtId = app.getKdtId();
    this.getShowcaseComponents();
    this.getMaterialList().then((res) => {
      if (res > 0) {
        this.handleTabChange({ detail: { index: 0 } });
      } else {
        this.handleTabChange({ detail: { index: 1 } });
      }
    });
    this.getShopAbility(kdtId);
  },

  detached() {
    const { uniqueKey } = this.data;
    FeatureLoadControl.clearShowcaseComponents(uniqueKey);
  },

  methods: {
    onFeatureLoaded() {
      this.SET_FEATURE_LOADED();
    },

    handleTabChange({ detail }) {
      this.setData({
        selectedId: detail.index,
      });
    },

    initShowCase(goodsDetail) {
      const { showcaseComponents } = goodsDetail;
      const uniqueKey = this.data.goods.alias + makeRandomString(8);

      // 确保该商品 只绑定一个 event key
      const reg = new RegExp('feature-load:.+' + uniqueKey);
      Object.keys(Event._events).forEach((key) => {
        if (reg.test(key)) Event.off(key);
      });

      FeatureLoadControl.setShowcaseComponents(
        showcaseComponents,
        false,
        false,
        uniqueKey
      );

      if (this.ctx) {
        this.ctx.data.featureComponents = showcaseComponents;
      }

      this.setData({
        componentsLength: showcaseComponents.length,
        uniqueKey,
      });
    },

    showcaseHandleGoodsBuy({ detail }) {
      const presale = detail.preSale || detail.pre_sale;
      const { alias } = detail;

      if (+presale === 1) {
        // eslint-disable-next-line @youzan/dmc/wx-check, @youzan-open/tee/no-platform-field
        wx.navigateTo({
          url: `/pages/goods/detail/index?alias=${alias}`,
        });
        return;
      }

      this.setData({
        showShowcaseBuy: true,
        showcaseBuyData: { alias },
      });
    },

    handleSkuHide() {
      this.setData({
        showShowcaseBuy: false,
      });
    },
  },
});
