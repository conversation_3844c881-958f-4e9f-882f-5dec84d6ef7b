@import '~mixins/index.scss';

.activity-count-down {
  // position: relative;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  font-size: 12px;
  color: #969799;

  .flex-column {
    display: flex;
    align-items: center;
  }

  &__label {
    font-size: 12px;
    line-height: 16px;

    &.activity-end {
      font-size: 18px;
      line-height: 36px;
    }
  }

  &__time {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    font-size: 12px;
    line-height: 16px;
  }

  &__process {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 10px;
    color: $white;
    font-size: 13px;
  }

  &__time-text {
    // margin: 0 2px;
    white-space: nowrap;
  }

  &__time-box {
    min-width: 1.1rem;
  }

  &__seconds-text {
    width: 5px;
  }

  .day-text {
    margin-right: 5px;
    font-weight: normal;
  }
}
