import { GoodsComponent } from '../../../../../../common/vanx-component';
import { remainTimestampToArray } from '../../../../../../utils';
import { timerBehaivor } from '../../../../../../common/behaviors';

const DAY = 24 * 3600 * 1000;

GoodsComponent({
  name: 'ActivityCountDown',
  behaviors: [timerBehaivor],
  properties: {
    countdown: Object,
    activityNotStart: Boolean,
  },

  data: {
    // 倒计时计数，用于确定是否已开始就已经结束了
    remainDesc: '',
    remainObj: {},
    ended: false,
  },

  watch: {
    countdown(val) {
      if (val) {
        this.timeRunner();
      }
    },
  },

  created() {
    this.count = 0;

    this.tid = null;
  },

  ready() {
    if (this.properties.countdown && !this.hasDetached) {
      this.timeRunner();
    }
  },

  methods: {
    timeRunner() {
      clearTimeout(this.tid);
      if (!this.properties.countdown) return;
      const { activityNotStart } = this.properties;
      const { end } = this.properties.countdown;
      const now = Date.now();
      const remain = end - now;
      // 交互逻辑, 未开始是“仅剩” 不是”还剩“
      let remainDesc = '';
      if (remain > DAY && !activityNotStart) {
        remainDesc = this.properties.countdown.desc.replace('仅剩', '还剩');
      } else {
        remainDesc = this.properties.countdown.desc;
      }
      // 倒计时结束
      if (remain <= 0 && this.count > 0) {
        this.setYZData({
          remainDesc: '已结束',
          ended: true,
        });
        this.triggerEvent('countdown-ended');
        return;
      }
      if (remain <= 0) {
        this.setYZData({
          remainDesc: '已结束',
          ended: true,
        });
        return;
      }
      this.count += 1;

      const remainObj = remainTimestampToArray(remain);
      remainObj.milliseconds = `${remainObj.milliseconds}`.substr(0, 2);

      this.setYZData({
        remainDesc,
        remainObj,
      });

      let delta = 500;
      if (!remainObj.days) {
        delta = 50;
      }
      this.tid = setTimeout(this.timeRunner.bind(this), delta);
    },
  },

  detached() {
    this.hasDetached = true; // 这个存在比ready先执行的情况
    clearInterval(this.tid);
    this.tid = null;
  },
});
