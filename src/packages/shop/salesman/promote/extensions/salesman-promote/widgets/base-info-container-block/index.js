import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';

const app = getApp();

RantaWidget({
  name: 'BaseInfoBlock',
  attached() {
    mapState(this, [
      'goods',
      'shop',
      'showActivityBanner',
      'showGuaranteeBar',
      'baseInfoBarTag',
      'isWxPromise',
      'showOriginPrice',
      'salesmanActivityTags',
      'soldNumText',
      'commissionData',
      'countdown',
      'activityNotStart',
      'activityPrice',
    ]);
    mapActions(this, ['countdownEnded']);
  },

  methods: {
    guaranteeOnShow(val) {
      app.logger
        && app.logger.log({
          et: 'view',
          ei: 'enterpage_goods_detail',
          en: '有赞担保曝光',
          params: {
            guarantee_on: val?.detail,
          },
          si: app.getKdtId(),
        });
    },
  },
});
