  <template name="spread-slot">
    <slot
      wx:for="{{ item.slots }}"
      wx:key="*this"
      name="{{ item }}"
    />
  </template>
  <view class="feature-page__top-hook" />
  <view class="page-goods" hidden="{{ loading }}">
    <block wx:for="{{ design }}" wx:key="type">
      <!-- 商品主图 -->
      <image-container-block wx:if="{{ item.type == 'image-block'}}" >
      </image-container-block>

      <!-- 商品标题、价格等信息 -->
      <base-info-container-block wx:if="{{ item.type == 'base-info-block'}}" >
      </base-info-container-block>

      <!-- 优惠券信息 -->
      <coupon-container-block wx:if="{{ item.type == 'coupon-block'}}" >
      </coupon-container-block>

      <!-- 商品详情信息 -->
      <goods-detail-container-block wx:if="{{ item.type == 'goods-detail-block'}}" >
      </goods-detail-container-block>

      <!-- 任务信息 -->
      <task-award-container-block wx:if="{{ item.type == 'task-award-block'}}" >
      </task-award-container-block>

      <!-- 价格说明信息 -->
      <price-intro-container-block wx:if="{{ item.type == 'price-intro-block'}}" >
      </price-intro-container-block>

      <!-- 成交信息走马灯 -->
      <trade-carousel-container-block wx:if="{{ item.type == 'trade-carousel'}}" >
      </trade-carousel-container-block>

      <!-- 底部按钮 -->
      <fixed-bottom-container-block wx:if="{{ item.type === 'fixed-bottom-block' }}">
      </fixed-bottom-container-block>
    </block>
  </view>

  <shop-pop-manager
    wx:if="{{ showShopPopManager }}"
    source="{{ 2 }}"
  />
  
  <slot name="detail-base-footer"></slot>
