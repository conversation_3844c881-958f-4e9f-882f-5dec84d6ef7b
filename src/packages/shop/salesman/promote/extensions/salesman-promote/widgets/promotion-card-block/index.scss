@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.coupon-item {
  display: flex;
  height: 80px;
  position: relative;
  color: #ee0a24;
  font-size: 12px;
  line-height: 16px;
  border-radius: 8px;
  margin-bottom: 8px;

  &__banner {
    position: relative;
    display: flex;
    width: 100%;
    height: 80px;
    box-sizing: border-box;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0 16px 12px;

    .banner-theme {
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      border-radius: 4px;
      overflow: hidden;
    }

    .custom-height {
      height: 80px;
    }

    .banner-image {
      width: 100%;
      height: 80px;
    }
  }

  &--showcase {
    height: 100px;

    .promotion-card__arrow {
      top: 60%;
    }

    .promotion-card__action {
      width: 10%;
    }
  }

  &--disabled {
    background-color: #f7f8fa;
    color: #c8c9cc;

    .promotion-card__btn {
      color: #c8c9cc;
      background-color: transparent;
    }
  }

  &__head {
    width: 75%;
    height: 100%;
  }

  &__head-wrap {
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    padding: 14px 0 14px 12px;
  }

  &__price-top {
    max-width: 100%;
    height: 28px;
    line-height: 28px;
    font-size: 14px;

    @include ellipsis;
  }

  &__price-value {
    font-size: 14px;
    line-height: 30px;
    font-weight: bold;

    &.price-length-0,
    &.price-length-1,
    &.price-length-2,
    &.price-length-3,
    &.price-length-4 {
      font-size: 26px;
    }

    &.price-length-5 {
      font-size: 16px;
    }

    &.price-length-6 {
      font-size: 14px;
    }

    &-unit {
      margin-left: 5px;
    }
  }

  &__condition {
    display: flex;
    flex-direction: initial;
    align-items: flex-start;
    max-width: 100%;
    margin-top: 6px;
    color: $gray-dark;
    margin-right: 8px;
    line-height: 16px;

    @include ellipsis;
  }

  &__content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    padding: 10px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    &-showcase {
      display: flex;
    }

    &-image-wrapper {
      margin-left: 10px;
      width: 56px;
      height: 56px;
      position: relative;
      border-radius: 4px;
      overflow: hidden;

      &:first-child {
        margin-left: 0;
      }
    }

    &-image {
      width: 100%;
      height: 100%;
    }

    &-image-desc {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 14px;
      line-height: 14px;
      background-color: rgba(#000, 0.24);
      color: $white;
      font-size: 10px;
      text-align: center;
      font-weight: bold;
    }

    &-title2 {
      margin-bottom: 5px;
      font-size: 12px;
      color: $text-color;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  &__title {
    display: inline;
    font-size: 14px;
    line-height: 20px;
    color: $text-color;
    text-overflow: ellipsis;
  }

  &__sub-title {
    margin-top: 10px;
    color: $gray-dark;
  }

  &__inner {
    display: flex;
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 4px;
  }

  &__action {
    position: relative;
    min-width: 25%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 4px;

    .board-point {
      border: 6px solid;
      border-radius: 6px;
      border-color: #fff;
    }

    .top-point {
      position: absolute;
      top: -6px;
      left: -6px;
    }

    .bottom-point {
      position: absolute;
      bottom: -6px;
      left: -6px;
    }
  }

  &__more-border {
    position: absolute;
    border: 4px solid;
    border-left-color: transparent;
    border-top-color: transparent;
    border-radius: 4px;
  }

  &__more-border-1 {
    right: -4px;
    top: 5%;
    height: 80%;
    opacity: 0.3;
  }

  &__more-border-2 {
    right: -8px;
    top: 11%;
    height: 68%;
    opacity: 0.1;
  }

  &__action-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    position: relative;
  }

  &__left-desc {
    margin-top: 4px;
    opacity: 0.6;
    font-size: 10px;
    color: #fff;
    text-align: center;
  }

  &__btn {
    color: #fff;
    font-size: 14px;
    text-align: center;
    line-height: 20px;
  }
}
