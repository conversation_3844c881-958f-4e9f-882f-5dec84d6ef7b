import { mapState, mapActions } from '@ranta/store';
import { RantaWidget } from 'shared/common/base/ranta/widget';
import Theme from 'shared/common/components/theme-view/theme';
import { get, args, jumpLink } from '../../utils';
import {
  NoteMaterialType,
  FeatureMaterialType,
} from '../../constants/material';
import Toast from '@vant/weapp/dist/toast/toast';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';
import {
  getMaterialBannerId,
  getLinkMaterialUrl,
  downloadAndSaveImage,
  downloadAndSaveViedo,
  getTipsData,
} from './utils';
import { getShortUrl } from '../../api';
import { getUrl, MATERIAL_CENTER } from '../../common/page-path';
import { LOGGER_CONFIG } from '../../common/config';

const app = getApp();

RantaWidget({
  name: 'MaterialList',

  attached() {
    mapState(this, [
      'syncShareToZone',
      'displayPop',
      'materials',
      'videoSrcs',
      'showMoreMaterial',
      'hasPersonalSpaceAbility',
      'kdtId',
      'itemId',
      'sl',
    ]);
    mapActions(this, [
      'changeMaterialCheckStatus',
      'publishMaterial',
      'setVideoSrc',
      'setTrackLooger',
    ]);
  },

  data: {
    themeMainBgColor: '',
    showSaveTip: false,
    tipsData: [],
  },

  created() {
    Theme.getThemeColor('main-bg').then((color) => {
      this.setData({
        themeMainBgColor: color,
      });
    });
  },

  methods: {
    handleChangeCheck(e) {
      const { material = {} } = e.currentTarget.dataset;
      const { id, syncShareToZone } = material;
      this.setTrackLooger(LOGGER_CONFIG.shareStory, {
        materialId: id,
        syncShareToZone,
      });
      this.changeMaterialCheckStatus(material);
    },

    handleGetMore({ currentTarget }) {
      const { query } = currentTarget.dataset;
      jumpLink(getUrl(MATERIAL_CENTER, query));
    },

    handleGetSrc(e) {
      this.setVideoSrc && this.setVideoSrc(e.detail);
    },

    onClose() {
      this.setData({
        showSaveTip: false,
      });
    },

    handleSave(e) {
      const { material = {} } = e.currentTarget.dataset;
      const { imageList, videoData, id: materialId } = material;
      const publishMaterial = {
        ...material,
        syncShareToZone:
          material.syncShareToZone && this.data.hasPersonalSpaceAbility,
      };

      this.setTrackLooger(LOGGER_CONFIG.copyMaterial, { materialId });

      const pfun = [
        this.publishMaterial(publishMaterial),
        this.setClipboardData(material),
      ];
      if (imageList.length > 0) {
        pfun.push(this.saveImage(imageList));
      }
      if (videoData.video && videoData.video.video_id) {
        const { video_id: id } = videoData.video;
        const video = this.data.videoSrcs.find((item) => item.id === id);
        if (video) {
          pfun.push(this.saveVideo(video.src));
        }
      }
      Promise.all(pfun).then((res) => {
        console.log(res);
        const tipsData = getTipsData(res);
        this.setData({
          showSaveTip: true,
          tipsData,
        });
      });
    },

    setClipboardData(material) {
      return new Promise((resolve) => {
        let itemUrl = '';
        const { sl } = this.data;
        const linkMaterial = this.handleLinkMaterial(material);
        let linkUrl = '';
        const { id, text = '' } = material;
        const kdtId = app.getKdtId();

        if (get(material, 'item.goodsInfo')) {
          const params = {
            banner_id: getMaterialBannerId({
              materialId: id,
              spm: 'salesMtrl',
            }),
            sub_kdt_id: kdtId,
          };
          itemUrl = args.add(material.item.goodsInfo.itemUrl, params);
          if (sl) {
            itemUrl = addSalesmanParams({ url: itemUrl, sl });
          }
        }
        if (linkMaterial) {
          const { alias } = linkMaterial;
          const { url } = getLinkMaterialUrl({
            alias,
            materialType: material.materialType,
            kdtId,
          });
          linkUrl = url;
          if (sl) {
            linkUrl = addSalesmanParams({ url: linkUrl, sl });
          }
        }
        const clipboardDataUrl = itemUrl || linkUrl;
        return this.handleUrlToShort(clipboardDataUrl).then(({ res }) => {
          const url = res || clipboardDataUrl;
          const br = text && url ? '\n' : '';
          const clipboardData = `${text}${br}${url}`;
          // eslint-disable-next-line @youzan-open/tee/no-platform-field
          wx.setClipboardData({
            data: clipboardData,
            success: () => {
              resolve({ type: 'copy', status: 1 });
            },
          });
        });
      });
    },

    handleUrlToShort(url) {
      if (url) {
        return getShortUrl({ url }).catch(() => ({ res: url }));
      }
      return Promise.resolve({ res: '' });
    },

    handleLinkMaterial({ featureMaterial, shopNoteMaterial, materialType }) {
      if (NoteMaterialType === materialType) {
        return shopNoteMaterial;
      }
      if (FeatureMaterialType === materialType) {
        return featureMaterial;
      }
    },

    saveImage(imageList) {
      return new Promise((resolve) => {
        const pf = [];
        imageList.forEach((url) => {
          pf.push(downloadAndSaveImage(url));
        });
        Promise.all(pf)
          .then(() => {
            console.log('保存完了');
            resolve({ type: 'image', status: 1 });
          })
          .catch(() => {
            console.log('图片保存异常');
            resolve({ type: 'image', status: 0 });
          });
      });
    },

    saveVideo(url) {
      return new Promise((resolve) => {
        downloadAndSaveViedo(url)
          .then(() => {
            console.log('保存视频成功');
            resolve({ type: 'video', status: 1 });
          })
          .catch((err) => {
            let message = get(err, 'errMsg', '下载视频失败');
            if (message === 'downloadFile:fail exceed max file size') {
              message = '不支持下载超过50MB的视频';
            }
            if (
              message ===
              'downloadFile:fail createDownloadTask:fail url not in domain list'
            ) {
              message = '该视频暂不支持下载';
            }
            if (message === 'downloadFile:fail url not in domain list') {
              message = '该视频暂不支持下载';
            }
            resolve({ type: 'video', status: 0 });
            Toast({
              message,
              duration: 4000,
            });
          });
      });
    },
  },
});
