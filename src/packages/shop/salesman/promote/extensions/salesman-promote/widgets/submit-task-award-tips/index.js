import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState } from '@ranta/store';
import { themeColorBehaivor } from '../../common/behaviors';
import { getUrl, TASK_DETAIL } from '../../common/page-path';
import { jumpLink } from '../../utils';

RantaWidget({
  name: 'task-award-tips',

  behaviors: [themeColorBehaivor],

  attached() {
    mapState(this, ['taskAwardData', 'goodsBottomDisabledTip', 'kdtId']);
  },

  methods: {
    goTaskDetail({ currentTarget }) {
      const { query } = currentTarget.dataset;
      jumpLink(getUrl(TASK_DETAIL, query));
    },
  },
});
