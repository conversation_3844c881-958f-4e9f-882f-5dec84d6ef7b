import { RantaWidget } from 'shared/common/base/ranta/widget';
import { mapState, mapActions } from '@ranta/store';

RantaWidget({
  name: 'PromotionPop',

  properties: {
    show: Boolean,
  },

  data: {
    isShow: false,
  },

  attached() {
    mapState(this, [
      'displayPop',
      'accountInfo',
      'couponsList',
      'optimalCouponData',
      'showMoreCoupon',
    ]);
    mapActions(this, [
      'toggleCouponPopUp',
      'handleGetCoupon',
      'showMoreOptimalCoupon',
    ]);
  },

  methods: {
    hidePopUp() {
      this.triggerEvent('close');
    },

    handleGetMore() {
      // ZNB.navigate({
      //   url: `/wscump/salesman/center-v2/coupon`,
      // });
    },
  },
});
