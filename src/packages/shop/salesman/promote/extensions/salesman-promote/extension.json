{"name": "@wsc-salesman/salesman-promote-block", "extensionId": "@wsc-salesman/salesman-promote-block", "version": "0.0.1", "bundle": "<builtin>", "lifecycle": ["beforePageMount", "onPullDownRefresh", "onShareAppMessage", "onPageScroll", "onReachBottom", "pageDestroyed"], "framework": "weapp", "platform": ["weapp"], "process": {"invoke": ["setShareInfo"]}, "widget": {"provide": ["PromoteSetupBlock", "DetailBaseBlock", "ImageContainerBlock", "BaseInfoContainerBlock", "CouponContainerBlock", "GoodsDetailContainerBlock", "TaskAwardContainerBlock", "PriceIntroContainerBlock", "TradeCarouselContainerBlock", "FixedBottomContainerBlock", "SubmitContainerBlock", "LevelInfoBlock", "BottomHook", "SubmitGoodsBottom", "SubmitGoodsBottomBar", "SubmitGoodsBottomBtns", "SubmitTaskAwardTips", "GoodsCouponBlock", "PromotionPopBlock", "PromotionCardBlock", "OptimalGoodsCouponBlock", "MaterialListBlock"], "consume": ["PromoteSetupBlock", "DetailBaseBlock", "ImageContainerBlock", "BaseInfoContainerBlock", "CouponContainerBlock", "GoodsDetailContainerBlock", "TaskAwardContainerBlock", "PriceIntroContainerBlock", "TradeCarouselContainerBlock", "FixedBottomContainerBlock", "SubmitContainerBlock", "LevelInfoBlock", "BottomHook", "SubmitGoodsBottom", "SubmitGoodsBottomBar", "SubmitGoodsBottomBtns", "SubmitTaskAwardTips", "GoodsCouponBlock", "PromotionPopBlock", "PromotionCardBlock", "OptimalGoodsCouponBlock", "MaterialListBlock", "ShowcaseContainer", "SalesmanCubeBlock"]}, "data": {"provide": {"featureComponents": ["r", "w"], "extraData": ["r"], "customOptionListConfig": ["r"], "goodsBaseInfo": ["r"], "currentActivity": ["r"], "goodsSkuData": ["r"], "shopBaseInfo": ["r"], "goodsPriceInfo": ["r"], "shareInfo": ["r"]}, "consume": {}}, "event": {"emit": ["ready"], "listen": ["ready", "share:query"]}}