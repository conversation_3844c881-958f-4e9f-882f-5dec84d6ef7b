<wxs src="../common/tools.wxs" module="tools" />

<page-container
  class="{{ themeClass }} page-{{ deviceType }}" fixed-bottom="{{ true }}"
>
  <view class="pay">
    <view class="order-status">
      <image class="status-icon" src="{{ icon }}"></image>
      <view class="" >
        <text class="status-text">{{ text }}</text>
        <view class="p" wx:if="{{expire}}"><text class="span">{{tools.counterText(expire)}}</text>后订单将自动关闭</view>
      </view>
    </view>

    <van-cell-group custom-class="pay-panel">
      <van-cell icon="shop" title="桌位: {{ tableNum }}" class="table"></van-cell>
      <van-cell title="商品合计({{choosedGoodsNum}}件)" value="¥ {{tools.cent2yuan(orderPayment.itemPay || 0)}}"></van-cell>
      <van-cell wx:if="{{orderPayment.decrease}}" value="-￥{{tools.cent2yuan(orderPayment.decrease)}}" >
        <view slot="title">
          <span class="van-cell-text">店铺活动</span>
          <van-icon bindtap="toggleThemeTakeAwayBannerDialog" name="question-o" color="#3388FF" size="14px" custom-class="cell-question" />
        </view>
      </van-cell>
      <van-cell wx:if="{{coupons!=''}}" bindtap="goToCoupon" class="coupon" is-link value="-￥{{tools.cent2yuan(chosenCoupon.denominations || 0)}}" >
        <view slot="title">
          <van-tag plain type="danger">券</van-tag>
          <span class="van-cell-text">优惠券</span>
        </view>
      </van-cell>
      <view class="total">需付：<span>¥{{ tools.cent2yuan(orderPayment.realPay-chosenCoupon.denominations>0 ? orderPayment.realPay-chosenCoupon.denominations : 0) }}</span></view>
    </van-cell-group>
    <view class="bottom-bar">
      <view wx:if="{{!orderNo}}" class="bottom-bar__left" bindtap="addGoods">加菜</view>
      <view class="bottom-bar__right pay" bindtap="onPayClick">确认支付￥{{tools.cent2yuan(orderPayment.realPay-chosenCoupon.denominations>0 ? orderPayment.realPay-chosenCoupon.denominations : 0)}}</view>
    </view>
  </view>
  <van-popup
    show="{{ showDialog }}"
    position="bottom"
    bind:close="toggleThemeTakeAwayBannerDialog"
  >
    <view class="banner-popup__item">
      <view class="banner-popup__item-hd">满减送活动名称</view>
      <view wx:for="{{activityName}}" wx:key="{{item.id}}" class="banner-popup__item-bd">{{ item }}</view>
    </view>
    <van-button size="large" type="danger" bind:click="toggleThemeTakeAwayBannerDialog">我知道了</van-button>
  </van-popup>
  <van-dialog id="van-dialog" />
  <!-- 支付方式 -->
  <pay-view
    id="payViewRef"
    order-pay-class="order-pay-class"
    order-no="{{ orderNo }}"
    prepay="{{ prepay }}"
    prepay-success="{{ prepaySuccess }}"
    prepay-params="{{ payParams }}"
    zero-order="{{ zeroOrder }}"
    catch:get-pay-ways="getPaywaysDone"
    catch:paid-success="navigateSuccess"
    catch:pay-fail="onPayFail"
  />
</page-container>
<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
