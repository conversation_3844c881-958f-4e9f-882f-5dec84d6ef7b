.pay {
  .order-status,
  &-panel {
    margin: 10px 5px;
    border-radius: 2px;
  }

  &-panel {
    margin: 10px 5px;
    border-radius: 2px;
    .table {
      font-weight: 500;
    }

    .coupon {
      .van-cell-text {
        margin-left: 5px;
      }
    }
    .cell-question{
      margin: 5px;
    }

    .total {
      text-align: right;
      font-size: 12px;
      color: #666;
      height: 44px;
      line-height: 44px;
      padding-right: 15px;
      background-color: #fff;

      span {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  .van-cell-group.van-hairline--top-bottom {
    &::after {
      border: 0;
    }
  }
}

.van-dialog__message {
  text-align: center;
}
.order-status {
  padding: 15px;
  background: #fff;
  box-shadow: rgba(0, 0, 0, .1) 0 1px 2px 0;
  display: flex;
  align-items: center;

  .status-icon {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }

  .status-text {
    color: #333;
    font-size: 14px;
    font-weight: bold;
  }
  .p {
    color: #f44;
    font-size: 10px;

    .span {
      color: #f44;
      font-size: 10px;
      font-weight: normal;
    }
  }
}
.bottom-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 9;
  display: flex;
  width: 100%;

  &__left,
  &__right {
    height: 50px;
    line-height: 50px;
    color: #fff;
    font-size: 16px;
    text-align: center;
  }

  &__left {
    flex: 1;
    background-color: #444;
  }

  &__right {
    flex: 1;
    background-color: #ff4c4c;

    &.pay {
      flex: 3;
    }
  }
}
.banner-popup__container {
  width: 100%;
  text-align: left;
  z-index: 10000 !important;
}

.banner-popup__item {
  margin: 20px 15px;
}

.banner-popup__item-hd {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}

.banner-popup__item-bd {
  font-size: 12px;
  color: #999;
  line-height: 18px;
}
