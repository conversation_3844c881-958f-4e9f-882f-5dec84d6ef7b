import WscPage from 'pages/common/wsc-page/index';
import PayAction from '../common/action/pay';

const app = getApp();

WscPage(PayAction, {

  data: {
    icon: 'https://img.yzcdn.cn/image/wait.png',
    text: '',
    expire: 0,
    coupons: [],
    choosedGoodsNum: 0,
    allGoodsPrice: 0,
    couponsPrice: 0,
    showDialog: false,
    chosenCoupon: {},
    orderNo: '',
    $cashier: {
      actions: [],
      show: false,
      componentId: 'cashier'
    },
    // 唤起收银台map数据
    payParams: {},
    prepay: false,
    prepaySuccess: false,
    zeroOrder: false,
    prepayParams: {}
  },

  onLoad() {
    const { mode = 0, choosedCouponDetail = { denominations: 0 } } = app.globalData.tangshi || {};
    this.setYZData({
      text: mode === 1 ? '订单已提交至厨房，待支付' : '请先付款，订单才能下单至厨房',
      orderNo: app.globalData.tangshi.orderNo,
      chosenCoupon: choosedCouponDetail
    });

    // this.pay = new PayConstructor();
    this.init();
  },

  // 每次刷新选择优惠券的数据
  onShow() {
    const { choosedCouponDetail = { denominations: 0 } } = app.globalData.tangshi || {};
    this.setYZData({
      chosenCoupon: choosedCouponDetail
    });
  },

  onUnload() {
    clearTimeout(this.timer);
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.init();
  },

  // 从后台获取下单的信息 预下单
  init() {
    // 之前没有下过单
    if (!app.globalData.tangshi.orderNo) {
      this.prepare();
    } else {
      this.preparePayment();
    }
  },

  // 支付时间倒计时
  calcRemainTime() {
    this.setYZData({
      expire: Math.max(0, this.data.endTime - Date.now())
    });
    this.timer = setTimeout(this.calcRemainTime, 200);
    if (this.expire <= 0) {
      clearTimeout(this.timer);
      wx.navigateTo({
        url: '/packages/tangshi/start/index'
      });
    }
  },

  // 提交订单
  onPayClick() {
    // 订单号已存在
    if (app.globalData.tangshi.orderNo) {
      this.startPay();
    } else {
      // 下单
      this.create()
        .then((res) => {
          if (res.zeroOrder) {
            wx.navigateTo({
              url: '/packages/tangshi/success/index'
            });
          } else {
            // this.doPay(this.data.payParams);
            this.startPay();
          }
        });
    }
  },

  startPay() {
    const payWaysRef = this.selectComponent('#payViewRef');

    if (payWaysRef) {
      try {
        payWaysRef.startPay();
      } catch (e) {
        this.onPayFail({ detail: e.message });
      }
    } else {
      this.onPayFail({ detail: 'payWaysRef不存在' });
    }
  },

  onPayFail(event = {}) {
    this.isSubmitting = false;
    app.logger.appError({
      name: 'order_submit_payview_error',
      message: event.detail
    });
  },

  getPaywaysDone() {
    this.isSubmitting = false;
  },

  // 收银台支付点击
  // $cashierClick(payChannelData, payData) {
  //   this.pay
  //     .doPayAction(payChannelData, payData)
  //     .then(() => {
  //       // 跳转到订单详情页面
  //       this.navigateSuccess();
  //     })
  //     .catch(({ msg, type }) => {
  //       if (type === 'need_password') {
  //         this.setYZData({
  //           '$cashier.showPassword': true
  //         });
  //       } else if (type !== 'cancel' && type !== 'adjust_price') {
  //         // 如果不是（卖家取消付款 || 不同意改价），就给个提示语
  //         wx.showToast({
  //           title: msg || '网络抖了下，再点下试试',
  //           icon: 'none'
  //         });
  //       }
  //     });
  // },

  // // 取消支付
  // $cashierCancel() {
  //   this.setYZData({
  //     '$cashier.show': false
  //   });
  // },

  // 控制店铺活动展示弹层显示
  toggleThemeTakeAwayBannerDialog() {
    this.setYZData({
      showDialog: !this.data.showDialog
    });
  },

  // 支付成功跳转
  navigateSuccess() {
    clearTimeout(this.timer);
    wx.navigateTo({
      url: '/packages/tangshi/success/index'
    });
  },

  // 加菜
  addGoods() {
    clearTimeout(this.timer);
    wx.navigateTo({
      url: '/packages/tangshi/start/index?extra=true'
    });
  },

  // 跳转到优惠券页面
  goToCoupon() {
    const params = {
      charge_coupon: this.data.coupons.map(this.formatCoupon),
      selected_coupon: this.formatCoupon(this.data.chosenCoupon)
    };

    const dbid = app.db.set({ ...params });
    wx.navigateTo({
      url: '/packages/tangshi/coupon/index?dbid=' + dbid
    });
  },

  // format传给优惠券页面的优惠券数据
  formatCoupon(item = {}) {
    return {
      ...item,
      endAt: item.endAt,
      isAvailable: item.available,
      originCondition: item.originCondition,
      priceStr: '',
      startAt: item.startAt,
    };
  },
});
