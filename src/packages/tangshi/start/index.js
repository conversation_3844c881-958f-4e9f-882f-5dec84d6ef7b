import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    isLoaing: true,
    seatId: null,
    kdtId: null,
    storeId: null,
    // 登录弹窗标志位
    accountLogin: false
  },
  onLoad(query) {
    this.setYZData({
      seatId: query.seatId || app.globalData.tangshi.alias,
      kdtId: app.getKdtId() || query.kdt_id || app.globalData.tangshi.kdtId,
      storeId: query.storeId || app.globalData.tangshi.storeId || 0
    }, () => {
      app.setOfflineId(this.data.storeId);
    });
    app.globalData.tangshi = {
      ...app.globalData.tangshi,
      alias: this.data.seatId,
      kdtId: this.data.kdtId,
      storeId: this.data.storeId,
    };
    setTimeout(() => {
      if (!app.getBuyerId()) {
        this.tapBindZanAccount();
      } else {
        this.getSetting(query);
      }
    }, 0);
  },

  // 获取配置的堂食模式等数据
  getSetting(query) {
    const { alias, kdtId, storeId } = app.globalData.tangshi;
    app.request({
      path: '/wscshop/showcase/tangshi/getSetting.json',
      query: {
        alias,
        kdtId,
        storeId
      }
    })
      .then((res) => {
        app.globalData.tangshi = {
          ...app.globalData.tangshi,
          kdtId: res.kdtId,
          storeId: res.storeId,
          mode: res.mode,
          multiStoreOn: res.multiStoreOn,
          orderVersion: res.orderVersion || 0
        };
        this.getUrl(res, alias, query.extra);
      })
      .catch(err => {
        wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  // 获取跳转链接 以及后续会用到的参数
  getUrl(data, alias, extra = false) {
    app.request({
      path: '/wscshop/showcase/tangshi/getRedirectPath.json',
      query: {
        buyerId: app.getBuyerId(),
        alias,
        kdtId: data.kdtId,
        storeId: data.storeId,
        mode: data.mode,
        extra
      }
    })
      .then((res) => {
        app.globalData.tangshi = {
          ...app.globalData.tangshi,
          bookKey: res.bookKey || '',
          orderNo: res.orderNo || '',
          orderVersion: res.orderVersion || 0,
          tableNum: res.tableName || ''
        };
        wx.navigateTo({
          url: res.weappUrl
        });
      })
      .catch(err => {
        wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  /**
   * 账号绑定
   */
  tapBindZanAccount() {
    this.setYZData({
      accountLogin: true
    });
  },

  onAccountSuccess() {
    let hasMobile = !!app.getBuyerId();
    this.setYZData({
      hasMobile,
      accountLogin: false
    });
    this.getSetting(this.data);
  },

  onAccountClose() {
    this.setYZData({
      accountLogin: false
    });
  },
});
