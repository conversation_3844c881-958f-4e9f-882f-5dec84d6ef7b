<import src="./components/take-away/index.wxml" />
<page-container
  class="{{ themeClass }} page-{{ deviceType }}" fixed-bottom="{{ true }}"
>
  <view class="theme-takeAway">
    <template
      is="theme-takeAway"
      data="{{ alias, themeClass, CURRENT_GLOBAL_SHOP, isMultiStore, banner, tags, goods, systemInfo, scrollIntoView, scrollTop, fixedGoodsTag, theme: theme.takeAway, extra: theme.extra }}"
    />
  </view>

  <!-- <template is="component-sku" data="{{ componentSKU }}" /> -->
  <van-dialog id="van-dialog" />
  <van-toast id="van-toast" />
</page-container>
<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />