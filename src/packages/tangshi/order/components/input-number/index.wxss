.input-number {
  height: 30px;
}

.input-number__icon-minus,
.input-number__icon-add {
  display: inline-block;
  height: 22px;
  width: 22px;
  top: 4px;
  background-size: 22px 22px;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  padding-left: 12px;
}

.input-number__icon-minus {
  background-image: url('https://b.yzcdn.cn/fix-base64/9720386cdb309f7d2a5b5fc9048d9699fa3aedf56cf21f10d2853b473a1e55af.png');
}

.input-number__icon-add {
  background-image: url('https://b.yzcdn.cn/fix-base64/a6544ea190d6f28110fe6955a6688dd731b65fceddfa823cc9c185bf47a05675.png');
}

.input-number__icon-minus--disabled {
  background-image: url('https://b.yzcdn.cn/fix-base64/e3db5ddfce3b4c3b09e390e3aa2b66e349c3a810d46dce3f18e458f6fe533151.png');
}

.input-number__icon-add--disabled {
  background-image: url('https://b.yzcdn.cn/fix-base64/01717c8df8c8932b05f732ad4e8913500d1dfc80f5168405d58dab4c8d324e95.png');
}

.input-number__value {
  display: inline-block;
  line-height: 30px;
  min-width: 14px;
  font-size: 14px;
  color: #333;
  text-align: center;
}
