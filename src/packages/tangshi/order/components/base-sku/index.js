import WscComponent from 'pages/common/wsc-component/index';
import skuEventBus from './common/sku-event-bus';
import {
  isAllSelected,
  isSkuChoosable,
  getSkuComb,
  getSelectedSkuValues
} from './common/sku-helper';
import { LIMIT_TYPE, UNSELECTED_SKU_VALUE_ID } from './common/constants';

WscComponent({
  options: {
    multipleSlots: true
  },

  properties: {
    themeClass: String,
    sku: {
      type: Object,
      observer: 'resetSelectedSku'
    },
    goods: Object,
    extraData: {
      type: Object,
      value: {}
    },
    stepperTitle: {
      type: String,
      value: '购买数量'
    },
    quotaText: {
      type: String,
      value: ''
    },
    quota: {
      type: Number,
      value: 0
    },
    quotaUsed: {
      type: Number,
      value: 0
    },
    hideStock: {
      type: Boolean,
      value: false
    },
    buyText: {
      type: String,
      value: '立即购买'
    },
    resetStepperOnHide: {
      type: Boolean,
      value: false
    },
    showAddCartBtn: {
      type: Boolean,
      value: true
    },
    bodyOffsetTop: {
      type: Number,
      value: 200
    },
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'onShowChange'
    }
  },

  data: {
    selectedSku: {},
    selectedNum: 1,
    selectedSkuComb: null,
    refs: {}
  },

  created() {
    skuEventBus.on('sku:close', this.onClose, this);
    skuEventBus.on('sku:select', this.onSelect, this);
    skuEventBus.on('sku:numChange', this.onNumChange, this);
    skuEventBus.on('sku:overLimit', this.onOverLimit, this);
    skuEventBus.on('sku:addCart', this.onAddCart, this);
    skuEventBus.on('sku:buy', this.onBuy, this);
    skuEventBus.on('sku:addRef', this.addRef, this);
  },

  detached() {
    skuEventBus.off('sku:close', this.onClose, this);
    skuEventBus.off('sku:select', this.onSelect, this);
    skuEventBus.off('sku:numChange', this.onNumChange, this);
    skuEventBus.off('sku:overLimit', this.onOverLimit, this);
    skuEventBus.off('sku:addCart', this.onAddCart, this);
    skuEventBus.off('sku:buy', this.onBuy, this);
    skuEventBus.off('sku:addRef', this.addRef, this);
  },

  methods: {
    addRef(data) {
      const { refKey, ref } = data;
      this.setData({
        [`refs.${refKey}`]: ref
      });
    },

    resetSelectedSku(sku) {
      const skuTree = sku.tree;
      const selectedSku = {};
      // 重置selectedSku
      skuTree.forEach((item) => {
        selectedSku[item.k_s] = UNSELECTED_SKU_VALUE_ID;
      });
      // 只有一个sku规格值时默认选中
      skuTree.forEach((item) => {
        const key = item.k_s;
        const valueId = item.v[0].id;
        if (
          item.v.length === 1
          && isSkuChoosable(sku.list, selectedSku, { key, valueId })
        ) {
          selectedSku[key] = valueId;
        }
      });
      const selectedSkuComb = this.getSelectedSkuComb(selectedSku);

      this.setData({
        selectedSku,
        selectedSkuComb
      });
    },

    getSelectedSkuComb(selectedSku) {
      const { sku } = this.data;

      if (sku.none_sku) {
        return {
          id: sku.collection_id,
          price: sku.price,
          stock_num: sku.stock_num
        };
      } if (this.isSkuCombSelected(selectedSku)) {
        return getSkuComb(sku.list, selectedSku);
      }
      return null;
    },

    isSkuCombSelected(selectedSku) {
      return isAllSelected(this.data.sku.tree, selectedSku);
    },

    getSkuMessages() {
      const { refs } = this.data;
      return refs.skuMessages ? refs.skuMessages.getMessages() : {};
    },

    validateSkuMessages() {
      const { refs } = this.data;
      return refs.skuMessages
        ? refs.skuMessages.validateMessages()
        : '';
    },

    validateSku() {
      if (this.data.selectedNum === 0) {
        return '商品已经无法购买啦';
      }

      if (this.isSkuCombSelected(this.data.selectedSku)) {
        return this.validateSkuMessages();
      }

      return '请选择完整的规格';
    },

    onSelect(skuValue) {
      // 点击已选中的sku时则取消选中
      const selectedSku = this.data.selectedSku[skuValue.skuKeyStr] === skuValue.id
        ? { ...this.data.selectedSku, [skuValue.skuKeyStr]: UNSELECTED_SKU_VALUE_ID }
        : { ...this.data.selectedSku, [skuValue.skuKeyStr]: skuValue.id };

      const selectedSkuComb = this.getSelectedSkuComb(selectedSku);

      this.setData({
        selectedSku,
        selectedSkuComb
      }, () => {
        this.triggerEvent('sku-selected', {
          skuValue,
          selectedSku,
          selectedSkuComb
        });
      });
    },

    onClose() {
      this.togglePopup();
    },

    onNumChange(num) {
      this.setData({
        selectedNum: num
      });
    },

    onOverLimit(data) {
      const {
        action, limitType, quota, quotaUsed
      } = data;

      if (action === 'minus') {
        wx.showToast({
          title: '至少选择一件',
          icon: 'none'
        });
      } else if (action === 'plus') {
        if (limitType === LIMIT_TYPE.QUOTA_LIMIT) {
          let msg = `限购${quota}件`;
          if (quotaUsed > 0) msg += `，你已购买${quotaUsed}件`;
          wx.showToast({
            title: msg,
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: '库存不足',
            icon: 'none'
          });
        }
      }
    },

    onAddCart() {
      this.onBuyOrAddCart('addcart');
    },

    onBuy() {
      this.onBuyOrAddCart('buy');
    },

    onBuyOrAddCart(type) {
      const error = this.validateSku();
      if (error) {
        wx.showToast({
          title: error,
          icon: 'none'
        });
      } else {
        this.triggerEvent(type, this.getSkuData());
      }
    },

    onShowChange(val) {
      if (!val) {
        const {
          sku, selectedSku, selectedNum,
          selectedSkuComb, resetStepperOnHide
        } = this.data;

        const selectedSkuValues = getSelectedSkuValues(
          sku.tree,
          selectedSku
        );

        this.triggerEvent('sku-close', {
          selectedSkuValues,
          selectedNum,
          selectedSkuComb
        });

        if (resetStepperOnHide) {
          this.onNumChange(1);
        }
      }
    },

    getSkuData() {
      const {
        goods: { id },
        sku,
        selectedSku,
        selectedNum,
        selectedSkuComb
      } = this.data;
      const selectedSkuValues = getSelectedSkuValues(sku.tree, selectedSku).map(value => value.name);
      return {
        selectedNum,
        selectedSkuComb,
        selectedSkuValues,
        goodsId: id,
        messages: this.getSkuMessages()
      };
    },

    togglePopup() {
      // 没有v-model的hack写法=。=
      this.setData({
        show: false
      });
    }
  }
});
