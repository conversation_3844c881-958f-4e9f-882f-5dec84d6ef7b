import WscComponent from 'pages/common/wsc-component/index';
import skuBehavior from '../common/sku-behavior';
import skuEventBus from '../common/sku-event-bus';
import { isSkuChoosable } from '../common/sku-helper';

WscComponent({
  behaviors: [skuBehavior],

  properties: {
    skuValue: Object,
    skuKeyStr: String,
    selectedSku: {
      type: Object,
      observer: 'updateRowItemClass'
    }
  },

  data: {
    rowItemClass: 'van-sku-row__item',
    isActive: false
  },

  relations: {
    '../sku-row/index': {
      type: 'parent'
    }
  },

  methods: {
    updateRowItemClass() {
      let className = 'van-sku-row__item';
      let isActive = false;
      if (!this.isChoosable()) {
        className = 'van-sku-row__item--disabled';
      } else if (this.isChoosed()) {
        className = 'van-sku-row__item--active';
        isActive = true;
      }

      this.setData({
        rowItemClass: className,
        isActive
      });
    },

    isChoosed() {
      return +this.data.skuValue.id === +this.data.selectedSku[this.data.skuKeyStr];
    },

    isChoosable() {
      const {
        sku, selectedSku, skuKeyStr, skuValue
      } = this.data;
      return isSkuChoosable(sku.list, selectedSku, {
        key: skuKeyStr,
        valueId: skuValue.id
      });
    },

    onSkuSelect() {
      if (this.isChoosable()) {
        skuEventBus.trigger('sku:select', {
          ...this.data.skuValue,
          skuKeyStr: this.data.skuKeyStr
        });
      }
    }
  }
});
