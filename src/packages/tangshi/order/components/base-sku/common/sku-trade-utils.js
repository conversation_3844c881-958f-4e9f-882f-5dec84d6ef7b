import navigate from '@/helpers/navigate';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();
const defaultBuyLogOptions = {
  actName: 'buy',
  et: 'click',
  ei: 'buy',
  en: '购买'
};

function tradeLog() {
  // 增加埋点数据
  const logGlobalInfo = app.logger.getGlobal() || {};

  const userInfo = logGlobalInfo.user || {};
  const contextInfo = logGlobalInfo.context || {};
  const plat = logGlobalInfo.plat || {};

  return JSON.stringify({
    ...contextInfo,
    ...plat,
    platform: 'weapp',
    uuid: userInfo.uuid,
    userId: userInfo.li || ''
  });
}
/**
 * 跳转下单页辅助函数
 * @param  {Array}  options.goodsList 商品列表
 * @param  {Object} options.extraData 下单需要的额外参数（activityAlias之类的）
 * @param  {String} orderFrom         下单来源（购物车/商品页）
 * @param  {Object} logOptions        日志参数
 * @return {undefined}
 */
export function goToBuy({
  goodsList = [],
  extraData = {},
  orderFrom = '',
  logOptions = defaultBuyLogOptions
}) {
  const {
    et,
    ei,
    en
  } = logOptions;

  let bizTracePointExt = tradeLog();

  goodsList = goodsList.map(item => {
    item.bizTracePointExt = bizTracePointExt;

    return item;
  });

  const dbid = app.db.set({
    type: 'goods',
    goods_list: goodsList,
    ...extraData
  });

  app.logger && app.logger.log({
    et,
    ei,
    en,
    si: app.getKdtId()
  });

  let url = `/packages/order/index?dbid=${dbid}`;
  if (orderFrom) {
    url += `&orderFrom=${orderFrom}`;
  }

  navigate.navigate({
    url
  });
}

export function addToCart(data) {
  data.bizTracePointExt = tradeLog();

  return app.request({
    data,
    method: 'POST',
    path: '/wscshop/trade/cart/goods.json'
  });
}
