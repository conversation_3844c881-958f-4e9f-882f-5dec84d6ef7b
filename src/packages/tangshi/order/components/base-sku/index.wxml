<van-popup
  position="bottom"
  custom-style="overflow-y:visible;"
  show="{{ show }}"
  bind:close="togglePopup"
>
  <view class="van-sku-container">
    <!-- sku header 商品标题、价格、图片信息 -->
    <sku-header
      theme-class="{{ themeClass }}"
      goods="{{ goods }}"
      sku="{{ sku }}"
      selected-sku="{{ selectedSku }}"
      selected-sku-comb="{{ selectedSkuComb }}"
      extra-data="{{ extraData }}"
    >
      <sku-header-price
        theme-class="{{ themeClass }}"
        goods="{{ goods }}"
        sku="{{ sku }}"
        selected-sku="{{ selectedSku }}"
        selected-sku-comb="{{ selectedSkuComb }}"
        extra-data="{{ extraData }}"
      ></sku-header-price>
    </sku-header>

    <!-- sku body -->
    <view class="van-sku-body">
      <!-- sku选择区域 -->
      <slot wx:if="{{ extraData.useCustomSkuGroup }}" name="sku-group"></slot>
      <block wx:else>
        <view
          wx:if="{{ !sku.none_sku }}"
          class="van-sku-group-container van-hairline--bottom"
        >
          <sku-row
            wx:for="{{ sku.tree }}"
            wx:for-item="skuTreeItem"
            wx:key="index"
            index="{{ index }}"
            theme-class="{{ themeClass }}"
            sku-row="{{ skuTreeItem }}"
            sku="{{ sku }}"
            selected-sku="{{ selectedSku }}"
            selected-sku-comb="{{ selectedSkuComb }}"
            extra-data="{{ extraData }}"
          >
            <sku-row-item
              wx:for="{{ skuTreeItem.v }}"
              wx:for-item="skuValue"
              wx:key="index"
              theme-class="{{ themeClass }}"
              sku-value="{{ skuValue }}"
              sku-key-str="{{ skuTreeItem.k_s }}"
              sku="{{ sku }}"
              selected-sku="{{ selectedSku }}"
              selected-sku-comb="{{ selectedSkuComb }}"
              extra-data="{{ extraData }}"
            >
            </sku-row-item>
          </sku-row>
        </view>
      </block>

      <!-- 扩展sku选择区 -->
      <sku-group-extra
        theme-class="{{ themeClass }}"
        sku="{{ sku }}"
        selected-sku="{{ selectedSku }}"
        selected-sku-comb="{{ selectedSkuComb }}"
        extra-data="{{ extraData }}"
      ></sku-group-extra>
      <!-- 支持抽象组件和slot两种方式，按需使用 -->
      <slot name="extra-sku-group"></slot>

      <!-- 数量选择 -->
      <sku-stepper
        wx:if="{{ !extraData.hideSkuStepper }}"
        theme-class="{{ themeClass }}"
        sku="{{ sku }}"
        selected-sku="{{ selectedSku }}"
        selected-sku-comb="{{ selectedSkuComb }}"
        extra-data="{{ extraData }}"
        selected-num="{{ selectedNum }}"
        stepper-title="{{ stepperTitle }}"
        quota="{{ quota }}"
        quota-text="{{ quotaText }}"
        quota-used="{{ quotaUsed }}"
        hide-stock="{{ hideStock }}"
      ></sku-stepper>

      <!-- sku留言 -->
      <sku-messages
        theme-class="{{ themeClass }}"
        sku="{{ sku }}"
        selected-sku="{{ selectedSku }}"
        selected-sku-comb="{{ selectedSkuComb }}"
        extra-data="{{ extraData }}"
        messages="{{ sku.messages }}"
        refs="{{ refs }}"
      ></sku-messages>
    </view>
    <sku-actions
      theme-class="{{ themeClass }}"
      sku="{{ sku }}"
      selected-sku="{{ selectedSku }}"
      selected-sku-comb="{{ selectedSkuComb }}"
      extra-data="{{ extraData }}"
      buy-text="{{ buyText }}"
      show-add-cart-btn="{{ showAddCartBtn }}"
    ></sku-actions>
  </view>
</van-popup>
