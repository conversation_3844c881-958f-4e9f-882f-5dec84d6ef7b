<view class="van-sku-stepper-stock">
  <view class="van-sku-stepper-container">
    <view class="van-sku__stepper-title">{{ stepperTitle }}：</view>
    <van-stepper
      class="van-sku__stepper"
      size="small"
      value="{{ selectedNum }}"
      min="{{ min }}"
      max="{{ max }}"
      integer
      bind:change="onSelectNumChange"
    />
  </view>
  <view wx:if="{{ !hideStock }}" class="van-sku__stock">剩余{{ stockNum }}件</view>
  <view wx:if="{{ quotaText }}" class="van-sku__quota">{{ quotaText }}</view>
</view>
