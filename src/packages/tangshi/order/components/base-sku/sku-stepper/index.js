import WscComponent from 'pages/common/wsc-component/index';
import skuBehavior from '../common/sku-behavior';
import skuEventBus from '../common/sku-event-bus';
import { LIMIT_TYPE } from '../common/constants';

WscComponent({
  behaviors: [skuBehavior],

  properties: {
    hideStock: Boolean,
    stepperTitle: String,
    quotaText: String,
    quota: Number,
    quotaUsed: Number,
    selectedNum: Number,
    selectedSkuComb: {
      type: Object,
      observer: 'updateStockNum'
    },
    sku: {
      type: Object,
      observer: 'updateStockNum'
    }
  },

  data: {
    min: 1,
    max: Number.MAX_VALUE,
    stockNum: Number.MAX_VALUE,
    limitType: LIMIT_TYPE.STOCK_LIMIT
  },

  methods: {
    getStockNum() {
      const { selectedSkuComb, sku } = this.data;

      if (selectedSkuComb) {
        return selectedSkuComb.stock_num;
      }

      return sku && sku.stock_num;
    },

    // 数据变更影响链路， sku/selectedSkuComb -> stockNum -> max -> selectedNum
    updateStockNum() {
      const stockNum = this.getStockNum();
      this.setData({
        stockNum
      }, this.updateStepperLimit);
    },

    updateStepperLimit() {
      const {
        quota, quotaUsed, stockNum, selectedNum
      } = this.data;
      const quotaLimit = quota - quotaUsed;
      let limit;
      let limitType;

      // 无限购时直接取库存，有限购时取限购数和库存数中小的那个
      if (quota > 0 && quotaLimit <= stockNum) {
        // 修正负的limit
        limit = quotaLimit < 0 ? 0 : quotaLimit;
        limitType = LIMIT_TYPE.QUOTA_LIMIT;
      } else {
        limit = stockNum;
        limitType = LIMIT_TYPE.STOCK_LIMIT;
      }

      this.setData({
        max: limit,
        limitType
      });

      // 当前选择数量大于最大可选时，需要重置已选数量
      if (selectedNum > limit) {
        this.updateSelectedNum(limit);
      }
    },

    updateSelectedNum(num) {
      skuEventBus.trigger('sku:numChange', num);
    },

    onSelectNumChange(e) {
      const value = e.detail;

      this.updateSelectedNum(value);
    }
  }
});
