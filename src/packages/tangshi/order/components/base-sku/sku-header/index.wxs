function getSkuImg(skuTree, id) {
  if (!id) return;
  var treeItem = {};
  skuTree.forEach(function(item) {
    if (item.k_s === 's1') treeItem = item;
  });

  if (!treeItem.v) return;

  var matchedSku = treeItem.v.filter(function(skuValue) {
    return skuValue.id === id;
  })[0];

  if (matchedSku && matchedSku.imgUrl) return matchedSku.imgUrl;
}

function getGoodsImg(selectedSku, sku, goods) {
  if (!selectedSku || !sku || !goods) return;

  var s1Id = selectedSku.s1;
  var skuImg = getSkuImg(sku.tree, s1Id);

  return skuImg || goods.picture;
}

function getGoodsPrice(selectedSkuComb, sku) {
  if (!sku) return;

  // sku.price是一个格式化好的价格区间
  var result = sku.price;

  if (selectedSkuComb) {
    result = selectedSkuComb.price;
  }

  return result;
}

module.exports = {
  getGoodsImg: getGoodsImg,
  getGoodsPrice: getGoodsPrice
};
