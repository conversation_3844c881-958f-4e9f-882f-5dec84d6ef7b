@import "shared/common/css/helper/index.wxss";
@import 'themes/base.scss';

.van-sku-header {
  margin-left: 15px
}

.van-sku-header__img-wrap {
  position: relative;
  float: left;
  margin-top: -10px;
  width: 80px;
  height: 80px;
  background: #f8f8f8;
  border-radius: 2px
}

.van-sku-header__img-wrap image {
  position: absolute;
  margin: auto;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  max-width: 100%;
  max-height: 100%
}

.van-sku-header__goods-info {
  padding: 10px;
  min-height: 82px;
  overflow: hidden;
  box-sizing: border-box
}

.van-sku__goods-name {
  padding-right: 40px;
  font-size: 12px
}

.van-sku__price-symbol {
  font-size: 14px;
  vertical-align: middle
}

.van-sku__price-num {
  vertical-align: middle
}

.van-sku__goods-price {
  color: #f44;
  margin-top: 10px;
  font-size: 16px;
  vertical-align: middle
}

.van-sku__close-icon {
  top: 10px;
  right: 15px;
  font-size: 20px;
  color: #999;
  position: absolute;
  text-align: center
}
