<view class="van-sku-header van-hairline--bottom">
  <view class="van-sku-header__img-wrap">
    <image mode="aspectFit" src="{{ utils.getGoodsImg(selectedSku, sku, goods) }}"></image>
  </view>
  <view class="van-sku-header__goods-info">
    <view class="van-sku__goods-name van-ellipsis">{{ goods.title }}</view>
    <!-- 价格区域可以用自定义组件替换 -->
    <slot wx:if="{{ extraData.useCustomHeaderPrice }}"></slot>
    <theme-view
      wx:else
      color="general"
      class="van-sku__goods-price">
      <text class="van-sku__price-symbol">￥</text><text class="van-sku__price-num">{{ utils.getGoodsPrice(selectedSkuComb, sku) }}</text>
    </theme-view>
    <van-icon
      name="close"
      class="van-sku__close-icon"
      bindtap="onSkuCloseClicked"
    />
  </view>
</view>

<wxs src="./index.wxs" module="utils" />
