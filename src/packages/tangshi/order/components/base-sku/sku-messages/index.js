import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import WscComponent from 'pages/common/wsc-component/index';
import upload from 'shared/utils/upload';
import skuBehavior from '../common/sku-behavior';
import skuEventBus from '../common/sku-event-bus';

WscComponent({
  behaviors: [skuBehavior],

  properties: {
    messages: {
      type: Array,
      observer: 'resetMessageValues'
    }
  },

  data: {
    messageValues: []
  },

  attached() {
    skuEventBus.trigger('sku:addRef', {
      refKey: 'skuMessages',
      ref: this
    });
  },

  methods: {
    resetMessageValues(val) {
      const messageValues = (val || []).map(() => ({ value: '' }));
      this.setData({
        messageValues
      });
    },

    onMessageValueChange(e) {
      const { index } = e.currentTarget.dataset;
      const value = e.detail.value;

      this.setData({
        [`messageValues[${index}].value`]: value
      });
    },

    onMessageDateTimeValueChange(e) {
      const value = e.detail.value;
      const date = value.slice(0, 3);
      const time = value.slice(3);
      e.detail.value = date.join('/') + ' ' + time.join(':');

      this.onMessageValueChange(e);
    },

    onMessageUploadImage(e) {
      const index = e.currentTarget.dataset.index;

      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 上传
          this.setData({
            [`messageValues[${index}].uploading`]: true
          });

          upload({
            file: res.tempFilePaths[0],
            success: (data) => {
              const url = data.attachment_url;

              this.setData({
                [`messageValues[${index}].value`]: cdnImage(url),
                [`messageValues[${index}].formatedValue`]: cdnImage(url, '!120x120.jpg'),
                [`messageValues[${index}].uploading`]: false
              });
            },
            fail: (res) => {
              wx.showToast({
                title: res.msg,
                icon: 'none'
              });

              this.setData({
                [`messageValues[${index}].uploading`]: false
              });
            }
          });
        },
        fail: () => {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },

    getMessages() {
      const messages = {};

      this.data.messageValues.forEach((item, index) => {
        const message = this.data.messages[index];
        messages[message.name] = item.value;
      });

      return messages;
    },

    validateMessages() {
      const values = this.data.messageValues;

      for (let i = 0; i < values.length; i++) {
        let value = values[i].value;
        value = value.trim ? value.trim() : value;
        const message = this.data.messages[i];

        if (value === '') {
          // 必填字段的校验
          if (+message.required === 1) {
            const textType = message.type === 'image'
              ? '请上传'
              : '请填写';
            return textType + message.name;
          }
        } else {
          const errorMsg = this.validateMessage(message.type, value);
          if (errorMsg) return errorMsg;
        }

        if (value.length > 200) {
          return '写的太多了，不要超过200字';
        }
      }
    },

    validateMessage(type, value) {
      let msg = '';

      const validateObj = {
        mobile: /^\d{6,20}$/,
        email: /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/,
        tel: /^\d+$/,
        id_no: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      };
      const validateMsg = {
        mobile: '手机号请填写6-20位的数字',
        email: '请填写正确的邮箱',
        tel: '请填写数字',
        id_no: '请填写正确的身份证'
      };
      const rule = validateObj[type];

      if (rule && !rule.test(value)) {
        return validateMsg[type];
      }

      return msg;
    }
  }
});
