<view class="van-sku-messages van-hairline--top-bottom">
  <block
    wx:for="{{ messages }}"
    wx:for-item="message"
    wx:key="{{ index }}">
    <view class="zan-cell zan-field">
      <view class="zan-cell__hd zan-field__title van-sku__field-title">
        <view class="{{ message.required == '1' ? 'van-sku__message-required' : '' }}">
          {{ message.name }}
        </view>
        <view wx:if="{{ message.type == 'image' }}" class="van-sku__field-label">仅限一张</view>
      </view>
      <!-- 图片留言 -->
      <block wx:if="{{ message.type == 'image' }}">
        <view class="zan-field__input zan-cell__bd">
          <view wx:if="{{ messageValues[index].uploading }}" class="van-sku__select-photo">
            <view style="margin-right: 0">正在上传...</view>
          </view>
          <view wx:else bindtap="onMessageUploadImage" data-index="{{ index }}">
            <view class="van-sku__select-photo">
              <van-icon name="photograph" />
              <view>{{ messageValues[index].value ? '重拍' : '拍照' }}</view>
              <view>或</view>
              <van-icon name="photo-o" />
              <view style="margin-right: 0">选择照片</view>
            </view>
          </view>
          <image
            wx:if="{{ messageValues[index].value }}"
            src="{{ messageValues[index].formatedValue }}"
            mode="aspectFit"
            style="width: 60px; height: 60px;"
          ></image>
        </view>
      </block>
      <!-- 时间、日期留言 -->
      <block wx:elif="{{ message.type == 'date' || message.type == 'time' }}">
        <van-datetime-picker
          wx:if="{{ message.type === 'time' && message.datetime }}"
          type="date"
          data-index="{{ index }}"
          class="zan-field__input zan-cell__bd"
          bind:change="onMessageDateTimeValueChange"
          placeholder="选择时间"
        />
        <picker
          wx:else
          class="zan-field__input zan-cell__bd"
          mode="{{ message.type }}"
          value="{{ messageValues[index].value }}"
          data-index="{{ index }}"
          placeholder="选择时间"
          bindchange="onMessageValueChange"
        >
          <view style="height: 26px;">{{ messageValues[index].value || '' }}</view>
        </picker>
      </block>
      <!-- 身份证留言 -->
      <block wx:elif="{{ message.type == 'id_no' }}">
        <input
          class="zan-field__input zan-cell__bd"
          type="idcard"
          data-index="{{ index }}"
          bindchange="onMessageValueChange"
          placeholder="点击填写身份证"
        ></input>
      </block>
      <!-- 数字格式留言 -->
      <block wx:elif="{{ message.type == 'tel' || message.type == 'mobile' }}">
        <input
          class="zan-field__input zan-cell__bd"
          type="number"
          data-index="{{ index }}"
          bindchange="onMessageValueChange"
          placeholder="点击填写数字"
        ></input>
      </block>
      <!-- 文本、邮件格式留言 -->
      <block wx:else>
        <input
          class="zan-field__input zan-cell__bd"
          type="text"
          data-index="{{ index }}"
          bindchange="onMessageValueChange"
          placeholder="点击填写留言"
        ></input>
      </block>
    </view>
  </block>
</view>
