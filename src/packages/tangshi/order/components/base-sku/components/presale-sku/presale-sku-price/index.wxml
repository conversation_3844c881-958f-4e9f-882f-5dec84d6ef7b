<view
  wx:if="{{ extraData && extraData.presaleInfo }}"
  class="presale-wrap {{ themeClass }}">
  <view class="van-sku__presale-price">
    <theme-view
      color="general"
      class="van-sku__presale-label">定金：￥</theme-view>
    <theme-view
      wx:if="{{ selectedSkuComb }}"
      color="general"
      class="van-sku__presale-price-number">
      {{ utils.presaleCentToYuan(selectedSkuComb.price * extraData.presaleInfo.depositRatio) }}
      <van-tag custom-class="goods-detail__tag--plain" type="danger" plain wx:if="{{  selectedDecrease || sku.decrease }}">定金可抵{{  selectedDecrease || sku.decrease }}</van-tag>
    </theme-view>
    <theme-view
      wx:if="{{ !selectedSkuComb && sku }}"
      color="general"
      class="van-sku__presale-price-number">
      <view wx:if="{{ extraData.presaleInfo.deposit.min === extraData.presaleInfo.deposit.max }}">{{ utils.presaleCentToYuan(extraData.presaleInfo.deposit.min) }}</view>
      <view wx:else>{{ utils.presaleCentToYuan(extraData.presaleInfo.deposit.min) }} - {{ utils.presaleCentToYuan(extraData.presaleInfo.deposit.max) }}</view>
      <van-tag custom-class="goods-detail__tag--plain" type="danger" plain wx:if="{{ selectedDecrease || sku.decrease }}">定金可抵{{ selectedDecrease || sku.decrease }}</van-tag>
    </theme-view>
  </view>
  <view class="van-sku__goods-price-wrap">
    <view class="van-sku__goods-price-label">总价：</view>
    <view class="van-sku__goods-price">
      <view class="van-sku__price-symbol">￥</view>
      <view class="van-sku__price-num">
        {{ utils.getGoodsPrice(selectedSkuComb, sku) }}
      </view>
    </view>
  </view>
</view>

<wxs src="./index.wxs" module="utils" />
