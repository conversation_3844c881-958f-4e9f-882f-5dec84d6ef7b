function leftPad(number) {
  return number > 9 ? number : '0' + number;
}

function parseDateToString(date) {
  const time = new Date(date);
  const M = leftPad(time.getMonth() + 1);
  const D = leftPad(time.getDate());
  const h = leftPad(time.getHours());
  const m = leftPad(time.getMinutes());
  const s = leftPad(time.getSeconds());
  return `${M}.${D} ${h}:${m}:${s}`;
}

export function buildPresaleInfo(sku) {
  const {
    extend,
    deposit,
    deposit_ratio: depositRatio
  } = sku;
  const {
    pre_sale_end: presaleEnd,
    etd_start: presaleShipTime,
    etd_days: presaleShipDays,
    etd_type: presaleShipType
  } = extend;

  // 预售是否已经结束
  const isPresaleOutDate = Date.now() > presaleEnd;
  const presaleEndTimeStr = parseDateToString(presaleEnd) + ' 预售结束';

  let presaleShipTimeStr = '';
  if (+presaleShipType === 1) {
    presaleShipTimeStr = '尾款支付' + presaleShipDays + '天后发货';
  } else {
    presaleShipTimeStr = parseDateToString(presaleShipTime) + ' 开始发货';
  }

  return {
    deposit,
    depositRatio,
    isPresaleOutDate,
    presaleShipTimeStr,
    presaleEndTimeStr
  };
}

export function calcPresaleDeposit(val) {
  // 抹去分
  if (val > 10) {
    val -= val % 10;
  }
  // 预售商品不足一分返回为1分
  if (val < 1) {
    val = 1;
  }

  return (val / 100).toFixed(2);
}
