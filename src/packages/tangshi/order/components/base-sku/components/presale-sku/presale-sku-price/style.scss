@import '~themes/abstracts/_variables.scss';
@import '~themes/abstracts/_functions.scss';
@import '~themes/abstracts/_mixins.scss';

@each $theme,
$param in $THEME_MAP {
  .#{$theme} {
    .goods-detail__tag--plain {
      color: getGeneralColor($param...) !important;

      &::after {
        border-color: getBtnMainBgColor($param...) !important;
      }
    }
  }
}

.goods-detail__tag--plain {
  margin-left: 7px;
}

.presale-wrap {
  .van-sku__presale-label {
    display: inline-block;
    font-size: 12px;
  }

  .van-sku__presale-price {
    margin-top: 10px;
    display: flex;
    align-items:center;
  }

  .van-sku__presale-price-number {
    font-size: 16px;
    flex: 1;
    display: flex;
  }

  .van-sku__goods-price-wrap {
    font-size: 12px;
    color: #999;
    height: 12px;
    line-height: 12px;
  }

  .van-sku__price-symbol,
  .van-sku__price-num {
    display: inline-block;
    color: #999;
    font-size: 12px;
    vertical-align: middle;
  }

  .van-sku__goods-price {
    display: inline-block;
    margin-top: 0;
  }

  .van-sku__goods-price-label,
  .van-sku__goods-price-number {
    font-size: 12px;
    display: inline-block;
    vertical-align: middle;
  }

  .van-sku__goods-price-wrap {
    display: inline-block;
  }
}
