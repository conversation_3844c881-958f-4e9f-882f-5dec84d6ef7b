import WscComponent from 'pages/common/wsc-component/index';
import skuBehavior from 'shared/common/components/base-sku/common/sku-behavior';
import money from '@youzan/weapp-utils/lib/money';
import theme from 'shared/common/components/theme-view/theme';

WscComponent({
  behaviors: [skuBehavior],
  properties: {
    selectedSkuComb: {
      type: Object,
      observer(val) {
        // ?? ob obj
        val && val.deposit_offer && this.setData({
          selectedDecrease: '¥' + money(val.deposit_offer).toYuan()
        });
      }
    }
  },

  data: {
    themeGeneral: ''
  },

  ready() {
    theme.getThemeColor('general')
      .then(color => {
        this.setYZData({
          themeGeneral: color
        });
      });
  }
});
