function presaleCentToYuan(val) {
  // 抹去分
  if (val > 10) {
    val = val - (val % 10);
  }
  // 预售商品不足一分返回为1分
  if (val < 1) {
    val = 1;
  }

  val /= 100;

  return val.toFixed(2);
}

function getGoodsPrice(selectedSkuComb, sku) {
  if (!sku) return;

  // sku.price是一个格式化好的价格区间
  var result = sku.price;

  if (selectedSkuComb) {
    result = selectedSkuComb.price;
  }

  return result;
}

module.exports = {
  presaleCentToYuan: presaleCentToYuan,
  getGoodsPrice: getGoodsPrice
};
