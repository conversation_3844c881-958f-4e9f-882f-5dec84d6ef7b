import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    alias: String,
    startFee: {
      type: Number,
      value: 0
    },
    allChoosedNum: {
      type: Number,
      value: 0
    },
    allChoosedPrice: {
      type: Number,
      value: 0
    },
    takeoutCartData: {
      type: Array,
      value: []
    }
  },

  externalClasses: ['iphonex-class'],

  methods: {
    // 唤起购物车
    showTakeoutCartAction() {
      if (!this.data.takeoutCartData.length) return;
      this.triggerEvent('toggleCart');
    },
    goToPay() {
      wx.navigateTo({
        url: '/packages/tangshi/shopping-cart/index'
      });
    }
  }
});
