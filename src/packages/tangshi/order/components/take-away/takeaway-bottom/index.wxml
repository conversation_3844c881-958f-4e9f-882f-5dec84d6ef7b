<view class="bottomcart iphonex-class">
  <view
    class="bottomcart__cart-icon {{ allChoosedNum == 0 ? 'bottomcart__cart-icon--zero bottomcart__cart-icon--disabled' : '' }}"
  >
    <view class="bottomcart__icon-wrapper">
      <view class="bottomcart__icon"></view>
      <view class="bottomcart__choosed-num {{ allChoosedNum >= 10 ? 'bottomcart__choosed-num--big' : '' }}">
        {{ allChoosedNum }}
      </view>
    </view>
    <view class="bottomcart__choosed-price-wrapper">
      <text class="bottomcart__choosed-price">￥{{ utils.cent2yuan(allChoosedPrice) }}</text>
    </view>
  </view>
  <view class="bottomcart__buy-button" bindtap="goToPay">购物车</view>
</view>

<wxs src="../index.wxs" module="utils" />
