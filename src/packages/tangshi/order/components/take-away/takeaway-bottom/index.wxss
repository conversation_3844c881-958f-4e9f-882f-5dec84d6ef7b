.bottomcart {
  width: 100%;
  height: 44px;
  background-color: #fff;
  box-shadow: 0 -0.5px 0 0 rgba(211, 211, 211, .5), 0 -2px 4px 0 rgba(0, 0, 0, .1);
}

.bottomcart::after {
  content: '';
  display: block;
  visibility: hidden;
  clear: both;
}

.bottomcart__cart-icon {
  float: left;
}

.bottomcart__icon-wrapper {
  position: absolute;
  top: 7px;
  left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.bottomcart__icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  background-image: url('https://b.yzcdn.cn/fix-base64/e05630f288b5e623dbfc90fadcf11e11abaeda1b3df59b7e8e120bcbc487a099.png');
  background-size: 30px 30px;
}

.bottomcart__choosed-num {
  position: absolute;
  top: -6px;
  left: 26px;
  height: 14px;
  min-width: 14px;
  border: 1px solid #f44;
  border-radius: 8px;
  background: white;
  font-size: 10px;
  line-height: 14px;
  text-align: center;
  color: #f44;
}

.bottomcart__choosed-num--big {
  padding: 0 2px;
}

.bottomcart__choosed-price-wrapper {
  padding-left: 55px;
  line-height: 44px;
  font-size: 12px;
  color: #FF4444;
}

.bottomcart__choosed-price {
  font-weight: 600;
  color: #FF4444;
}

/**
 * 购物车为空
 */
.bottomcart__cart-icon--zero .bottomcart__choosed-num {
  opacity: 0;
}

.bottomcart__cart-icon--zero .bottomcart__choosed-price {
  font-weight: 400;
  color: #a3a3a3;
}

/**
 * 不能去结算（购物车为空，或者还没到起送金额）
 */
.bottomcart__cart-icon--disabled .bottomcart__icon-wrapper {
  /* background: #f2f2f2; */
}

.bottomcart__cart-icon--disabled .bottomcart__choosed-num {
  border-color: #f2f2f2;
  color: #666;
}

.bottomcart__cart-icon--disabled .bottomcart__choosed-price {
  color: #999;
}

.bottomcart__cart-icon--disabled .bottomcart__icon {
  background-image: url('https://b.yzcdn.cn/fix-base64/e05630f288b5e623dbfc90fadcf11e11abaeda1b3df59b7e8e120bcbc487a099.png');
}

/**
 * 右下角的结算按钮
 */
.bottomcart__buy-button {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 0 31px;
  background: #f44;
  line-height: 44px;
  font-size: 16px;
  color: #fff;
}

.bottomcart__buy-button--disabled {
  background: #efefef;
  color: #a3a3a3;
}

@media (max-width: 320px) {
  .bottomcart__buy-button--disabled {
    padding: 0 15px;
  }
}
