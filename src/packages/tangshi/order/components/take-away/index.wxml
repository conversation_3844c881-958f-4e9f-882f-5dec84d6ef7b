<template name="theme-takeAway">
  <takeaway-header
    logo="{{ CURRENT_GLOBAL_SHOP.image || CURRENT_GLOBAL_SHOP.logo }}"
    title="{{ CURRENT_GLOBAL_SHOP.name || CURRENT_GLOBAL_SHOP.shop_name }}"
    show-switch-offline-shop="{{ isMultiStore }}"
    notice-content="{{ banner.notice }}"
    promotion-str="{{ banner.preference }}"
    shop-style="{{ banner.shopStyle }}"
    show-homepage-url="{{ banner.showHomepageUrl }}"
    show-record-url="{{ banner.showRecordUrl }}"
  />

  <view class="main-container zan-hairline--top">
    <scroll-view scroll-y="true" class="tag-list" scrollIntoView="{{ tagItemIntoView }}">
      <view
        wx:for="{{ tags.list }}"
        wx:for-item="item"
        wx:for-index="index"
        wx:key="tagId"
        id="tag-item-{{ item.id }}"
        data-jjj="{{theme.selectedTagId }}"
        class="tag-item zan-hairline--bottom {{ item.id === theme.selectedTagId ? 'tag-item--active' : '' }}"
        style="{{ (tags.list.length - 1) == index ? 'border-bottom: 0' : ''  }}"
        data-tag-id="{{ item.id }}"
        data-tag-index="{{ index }}"
        bindtap="handleThemeTakeAwayTagTap"
      >
        <view class="zan-ellipsis--l3" style="line-height: 18px; max-height: 54px;">
          {{ item.title }}
        </view>
        <view
          wx:if="{{ tags.choosedNum[index] > 0 }}"
          class="tag-list__quantity"
        >
          {{ tags.choosedNum[index] }}
        </view>
      </view>
      <view style="height: 120px"></view>
    </scroll-view>
    <scroll-view
      scroll-y="true"
      scroll-into-view="{{ theme.goodsListIntoView }}"
      bindscroll="handleThemeTakeAwayGoodsListScroll"
      class="tag-goods">
      <view
        wx:for="{{ tags.list }}"
        wx:for-item="item"
        wx:for-index="index"
        wx:key="id"
      >
        <view class="tag-goods__hd zan-hairline--bottom zan-hairline--left" id="tag-goods-{{ item.id }}">{{ item.title }}</view>
        <view wx:if="{{ goods[item.id] && goods[item.id].list && goods[item.id].list.length }}" class="tag-goods_bd">
          <view
            wx:for="{{ goods[item.id].list }}"
            wx:for-item="goods"
            wx:for-index="goodsIndex"
            wx:key="id"
            class="goods-item zan-hairline--bottom zan-hairline--left"
            data-alias="{{ goods.alias }}"
          >
            <image
              src="{{ goods.pic_url }}"
              class="goods-image"
              data-tag-index="{{ index }}"
              data-goods-index="{{ goodsIndex }}"
            />
            <view class="goods-container">
              <view class="goods-title zan-ellipsis">{{ goods.title }}</view>
              <view class="goods-describe">{{ goods.sub_title }}</view>
              <view class="goods-price">¥ {{ goods.price }}</view>
            </view>
            <input-number
              class="takeout-input"
              data-alias="{{ goods.alias }}"
              data-groupid="{{ item.id }}"
              data-id="{{ goods.id }}"
              data-title="{{ goods.title }}"
              min="{{ theme.choosedGoods[goods.alias].noneSku ? 0 : theme.choosedGoods[goods.alias].num }}"
              max="{{ theme.choosedGoods[goods.alias].noneSku ? theme.choosedGoods[goods.alias].stockNum : theme.MAX_VALUE }}"
              value="{{ theme.choosedGoods[goods.alias].num }}"
              is-value-show="{{ theme.choosedGoods[goods.alias].num > 0 }}"
              is-minus-show="{{ theme.choosedGoods[goods.alias].num > 0 }}"
              disabled="{{ goods.sold_status == 2 }}"
              bind:change="ThemeTakeAwayhandleGoodsBuy"
              bind:overlimit="ThemeTakeAwayhandleGoodsOverlimit"
            ></input-number>
          </view>
        </view>
        <view wx:elif="{{ goods[item.id].nodata }}" class="tag-goods__empty">
          暂无商品哦~
        </view>
        <view wx:else class="tag-goods__empty">
          商品加载中...
        </view>
      </view>
      <view style="height: 120px"></view>
    </scroll-view>

    <!-- 在多网点不营业的情况下，不展示这个条 -->
    <takeaway-bottom
      wx:if="{{ !CURRENT_GLOBAL_SHOP.isMultiStore || CURRENT_GLOBAL_SHOP.is_opening }}"
      alias="{{ alias }}"
      iphonex-class="bottom-bar-container"
      class="takeaway-bottom"
      start-fee="{{ theme.takeAwayStartFee }}"
      takeout-cart-data="{{ theme.takeoutCartData }}"
      all-choosed-num="{{ theme.allChoosedNum }}"
      all-choosed-price="{{ theme.allChoosedPrice }}"
      bind:toggleCart="toggleThemeTakeAwayCart"
    >
    </takeaway-bottom>
    <!-- <takeaway-cart
      iphonex-class="bottom-bar-container"
      takeout-cart-data="{{ theme.takeoutCartData }}"
      showTakeoutCart="{{ theme.showTakeoutCart }}"
      bind:toggleCart="toggleThemeTakeAwayCart"
      bind:clearCart="clearThemeTakeAwayCart"
      bind:goodsNumChange="handleThemeTakeAwayGoodsNumChange"
    >
    </takeaway-cart> -->

    <base-sku
      wx:if="{{ theme.activeSku }}"
      theme-class="{{ themeClass }}"
      goods="{{ theme.activeGoods }}"
      sku="{{ theme.activeSku }}"
      show="{{ theme.showTakeawaySku }}"
      buy-text="加入购物车"
      show-add-cart-btn="{{ false }}"
      hide-stock="{{ theme.activeSku.hide_stock }}"
      bind:buy="handleThemeTakeAwayBuyClicked"
      bind:sku-close="handleThemeTakeAwaySkuClosed"
      reset-stepper-on-hide="{{ true }}"
    >
    </base-sku>
  </view>
</template>
