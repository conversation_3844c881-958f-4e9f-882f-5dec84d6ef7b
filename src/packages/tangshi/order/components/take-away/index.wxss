.theme-takeAway .banner {
  position: relative;
  padding-top: 86px;
  border-bottom: 1px solid #e5e5e5;
  color: #3a3a3a;
  overflow: hidden;
}
.theme-takeAway .banner__filter-blur {
  position: absolute;
  top: 0;
  width: 100%;
  height: 160px;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  -webkit-filter: blur(46px);
  filter: blur(15px);
}
.theme-takeAway .banner__mask {
  position: absolute;
  top: 0;
  width: 100%;
  height: 160px;
  background: rgba(0, 0, 0, 0.1);
}
.theme-takeAway .banner__container {
  position: relative;
  padding-bottom: 8px;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 -2px 4px 0 rgba(0, 0, 0, .1);
  background: white;
  z-index: 2;
}
.theme-takeAway .banner__logo {
  position: absolute;
  top: -66px;
  left: 50%;
  width: 80px;
  height: 80px;
  margin-left: -40px;
  border-radius: 4px;
  background-color: #f8f8f8;
  box-shadow: 0 2px 6px 0 rgba(0,0,0,.5);
}
.theme-takeAway .banner__bd {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 29px;
}

.theme-takeAway .banner__title {
  padding-bottom: 10px;
  font-size: 16px;
  line-height: 22px;
  font-weight: 600;
}

.theme-takeAway .banner__notice {
  padding: 0 10px;
  font-size: 12px;
  line-height: 14px;
  color: #9a9a9a;
  overflow: hidden;
}

.theme-takeAway .banner__notice-text {
  vertical-align: middle;
}

.theme-takeAway .banner__preference {
  width: 100%;
  padding: 10px;
  font-size: 12px;
  line-height: 14px;
  text-align: center;
}

.banner__preference-tag {
  padding: 2px 5px;
  margin-right: 10px;
  font-size: 10px;
  line-height: 10px;
}

.banner__preference-text {
}

.theme-takeAway .banner-popup__container {
  width: 100%;
}
.theme-takeAway .banner-popup__item {
  margin: 20px 15px;
}
.theme-takeAway .banner-popup__item-hd {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}
.theme-takeAway .banner-popup__item-bd {
  font-size: 12px;
  color: #999;
  line-height: 18px;
}

/* 商品列表 */
/* 商品分组列表 */
.theme-takeAway .tag-list {
  float: left;
  height: 100vh;
  width: 160rpx;
  background: #F8F8F8;
  box-sizing: border-box;
}
.theme-takeAway .tag-item {
  position: relative;
  font-size: 12px;
  color: #666;
  line-height: 20px;
  padding: 17px 14px 17px 11px;
  /* border-left: 4px solid transparent; */
  /* border-bottom: 1px solid #E5E5E5; */
  word-break: break-all;
}

.theme-takeAway .tag-item--active {
  /* color: #3a3a3a;
  border-left-color: #f44; */
  background-color: #fff;
  color: #111;
}

.theme-takeAway .tag-list__quantity {
  position: absolute;
  top: 2px;
  right: 2px;
  height: 14px;
  min-width: 14px;
  line-height: 14px;
  padding: 0 4px;
  font-size: 10px;
  font-family: tahoma;
  border-radius: 10px;
  background: #f44;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  z-index: 4;
  box-shadow: 0 0 0 1px #fff;
  box-sizing: border-box;
}

/* 商品列表 */
.theme-takeAway .tag-goods {
  float: left;
  height: 100vh;
  width: 590rpx;
  background: #fff;
  box-sizing: border-box;
}
.theme-takeAway .tag-goods__hd {
  font-size: 12px;
  padding: 0 10px 0 10px;
  color:#666;
  line-height: 30px;
  overflow: hidden;
  background-color: #F8F8F8;
}
.theme-takeAway .tag-goods_bd {
  /* padding-left: 10px; */
}
.theme-takeAway .goods-item {
  position: relative;
  height: 76px;
  padding: 7px 10px 7px 0;
  box-sizing: border-box;
  padding-left: 10px;
}
.theme-takeAway .goods-item:last-child {
  border-bottom: 0px none;
}
.theme-takeAway .goods-image {
  float: left;
  width: 60px;
  height: 60px;
  border: 1rpx solid #f8f8f8;
  border-radius: 2px;
  box-sizing: border-box;
  background-color: #f2f2f2;
  background: #f2f2f2 url(https://img.yzcdn.cn/public_files/2017/01/11/4e25040b6a2b9336bcaf21d21eddcfc9.png) no-repeat;
  background-size: 40px 40px;
  background-position: center center;
}
.theme-takeAway .goods-container {
  margin-left: 70px;
}
.theme-takeAway .goods-title {
  font-size: 14px;
  line-height: 20px;
}
.theme-takeAway .goods-describe {
  font-size: 12px;
  height: 17px;
  line-height: 17px;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.theme-takeAway .goods-price {
  margin-top: 7px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  color: #f44;
}
.theme-takeAway .goods-buy {
  position: absolute;
  padding: 10px;
  width: 22px;
  height: 22px;
  right: 10rpx;
  bottom: 0;
  border-radius: 22px;
  background-color: #f44;
  background-clip: content-box;
}
.theme-takeAway .goods-buy:before {
  content: '';
  position: absolute;
  width: 10px;
  height: 2px;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  background-color: white;
}
.theme-takeAway .goods-buy:after {
  content: '';
  position: absolute;
  width: 2px;
  height: 10px;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  background-color: white;
}
.theme-takeAway .tag-goods__empty {
  font-size: 14px;
  height: 54px;
  line-height: 54px;
  color: #999;
  text-align: center;
}

/* 购物车按钮 */
.theme-takeAway .cart-icon {
  position: fixed;
  width: 88rpx;
  height: 88rpx;
  right: 30rpx;
  bottom: 30rpx;
  z-index: 1;
  background: url(https://img.yzcdn.cn/public_files/2017/02/08/380ec55f446a369dc5488b0e8cd96d38.png) no-repeat center;
  background-size: 88rpx;
}
.multi-store--selector {
  display: none;
  font-size: 14px;
}
.is-multi-store .multi-store--selector {
  display: inline;
}
.page-is-feature .is-multi-store .multi-store--selector {
  display: none;
}

.takeout-input {
  position: absolute;
  bottom: 4px;
  right: 5px;
}

.takeaway-bottom {
  z-index: 11;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}

/* .youzan-public .takeaway-bottom {
  bottom: 48px;
} */
