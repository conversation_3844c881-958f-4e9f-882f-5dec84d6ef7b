<view class="cap-shop-banner-takeout cap-shop-banner-takeout--left zan-hairline--bottom">
  <view class="cap-shop-banner-takeout__logo">
    <image class="banner__logo" src="{{ logo }}" />
  </view>
  <view class="cap-shop-banner-takeout__background" style="background-image: url({{ backgroundImage || logo }})" />
  <view class="cap-shop-banner-takeout__mask" />
  <view class="cap-shop-banner-takeout__content">
    <view class="cap-shop-banner-takeout__title-wrapper">
      <view class="cap-shop-banner-takeout__title" bindtap="handleTitleClick">
        {{ title }}
      </view>
    </view>
    <view
      class="cap-shop-banner-takeout__notice zan-ellipsis"
      wx:if="{{ noticeContent }}"
      bindtap="toggleBannerDialog"
    >
      公告：{{ noticeContent }}
    </view>
    <view
      wx:if="{{ promotionStr }}"
      class="cap-shop-banner-takeout__promotion zan-ellipsis"
      bindtap="toggleBannerDialog"
    >
      <theme-view
        custom-class="meet-reduce-tag zan-tag zan-tag--danger"
        bg="main"
        border="main"
      >
        满减
      </theme-view>
      <text class="cap-shop-banner-takeout__promotion-desc">{{ promotionStr }}</text>
    </view>

    <view class="cap-shop-banner-takeout__links">

      <view>
        <view class="zan-cell zan-cell--access">
          <view class="zan-cell__bd" bindtap="handleSingleLinkClick" data-type="record">
            <view
              class="zan-cell__text"
            >
              我的订单
          </view>
          </view>
          <view class="zan-cell__ft"></view>
        </view>
      </view>
    </view>
  </view>

  <van-popup
    show="{{ showDialog }}"
    position="bottom"
    bind:close="toggleBannerDialog"
  >
    <view wx:if="{{ promotionStr }}" class="banner-popup__item">
      <view class="banner-popup__item-hd">满减</view>
      <view class="banner-popup__item-bd">{{ promotionStr }}</view>
    </view>
    <view wx:if="{{ noticeContent }}" class="banner-popup__item">
      <view class="banner-popup__item-hd">公告</view>
      <view class="banner-popup__item-bd">{{ noticeContent }}</view>
    </view>
    <van-button size="large" type="danger" bind:click="toggleBannerDialog">我知道了</van-button>
  </van-popup>
</view>

