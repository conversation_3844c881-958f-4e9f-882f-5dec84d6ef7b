import navigate from '@/helpers/navigate';

Component({
  properties: {
    /**
     * 是否显示 店铺首页 返回按钮
     */
    showHomepageUrl: {
      type: Boolean,
      value: false
    },

    /**
     * 是否显示 我的记录 返回按钮
     */
    showRecordUrl: {
      type: Boolean,
      value: true
    },

    /**
     * 是否显示 多门店 切换按钮
     */
    showSwitchOfflineShop: Boolean,

    /**
     * 店铺样式：center-居中，left-居左
     */
    shopStyle: {
      type: String,
      value: 'center'
    },

    /**
     * 公告内容
     */
    noticeContent: String,

    /**
     * 店铺标题
     */
    title: String,

    /**
     * 店铺logo
     */
    logo: {
      type: String,
      value: 'https://img.yzcdn.cn/upload_files/2017/11/27/FsQbwe4BB_y0eEbjQ-2bm_XLfT0z.png'
    },

    /**
     * 背景图
     */
    backgroundImage: String,

    /**
     * 满减信息
     */
    promotionStr: String
  },

  data: {
    showDialog: false
  },

  methods: {
    toggleBannerDialog() {
      this.setData({
        showDialog: !this.data.showDialog
      });
    },

    handleTitleClick() {
      navigate.switchTab({ url: '/packages/home/<USER>/index' });
    },

    handleRecordClick() {
      navigate.switchTab({ url: '/packages/usercenter/dashboard/index' });
    },

    handleSingleLinkClick(e) {
      const { type } = e.currentTarget.dataset;

      if (type === 'record') {
        this.handleRecordClick();
      } else if (type === 'homepage') {
        this.handleTitleClick();
      }
    }
  }
});
