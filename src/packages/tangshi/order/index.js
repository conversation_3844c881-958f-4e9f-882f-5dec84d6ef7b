/*
 * @Author: tianya
 * @Date: 2018-10-16 20:25:42
 * @Last Modified by: tianya
 * @Last Modified time: 2019-05-05 16:42:46
 */
import WscPage from 'pages/common/wsc-page/index';
import each from '@youzan/weapp-utils/lib/each';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import omit from '@youzan/weapp-utils/lib/omit';
import Events from 'utils/event';
import Toast from '@vant/weapp/dist/toast/toast';
import takeAwayTheme from './components/take-away/index';
import OrderAction from '../common/action/order';

const app = getApp();
let featureTitle;

WscPage(Toast, Events, takeAwayTheme, OrderAction, {
  data: {
    id: 0,
    alias: '',
    themeClass: app.themeClass,
    fetching: true,
    type: 'default',
    banner: {
      logo: '',
      title: ''
    },
    tags: {
      list: [],
      selectedId: 0,
      scroll: false,
      choosedNum: []
    },
    pageBgColor: '',
    goods: {},
    systemInfo: {},
    scrollIntoView: '',
    scrollTop: 0,
    success: true,
    showSelfFetchPopup: true,
    visitGift: {},
    salesman: {},
    youzanAppHideTabbar: false,
    buyerAddress: ''
  },

  __featureFirstLoad: true,
  __hideTabBar: false,
  __maybeTabPage: false,
  __featureFetched: false,

  onUnload() {
    app.off(null, null, this);
  },

  onLoad(options) {
    // 单人点餐模式下，自动调用开始点餐接口
    if (app.globalData.tangshi.mode === 0) {
      this.startEat();
    }
    // 记录下来，在分享的时候需要
    this.options = options;
    this.__fetchPromise = new Promise(() => {});

    var systemInfo = app.getSystemInfoSync();
    this.setYZData({
      systemInfo
    });

    app.on('component:sku:cart', (res) => {
      if (res.type == 'add') {
        Toast('添加购物车成功~');
      }
    }, this);
  },

  onShow() {
    if (!app.getAppId()) {
      wx.showModal({
        title: '提示',
        content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试'
      });
    }

    if (this.__featureFirstLoad) {
      this.__fetchPromise = this.fetchHomepage();
    }

    if (this.__hideTabBar) {
      // 隐藏导航栏
      this.setYZData({
        youzanAppHideTabbar: true
      });
    }
    this.trigger('page:show');
  },

  onReady() {
    this.__featureFirstLoad = false;

    // 对界面的设置如wx.setNavigationBarTitle请在onReady之后设置
    // https://developers.weixin.qq.com/miniprogram/dev/framework/app-service/page.html
    // 店铺主页和微杂志页标题
    Promise.all([
      app.getShopConfigData(),
      this.__fetchPromise
    ]).then(([config]) => {
      const title = this.resolveFeatureTitle(config);
      wx.setNavigationBarTitle({
        title
      });
      // this.getCart();
    });
  },

  onPullDownRefresh(cb) {
    this.__fetchPromise = this.fetchHomepage(true, typeof cb === 'function' ? cb : '');
    this.trigger('home:refresh');
  },

  showShowcaseComponents(components, isRefresh) {
    this.fetchTakeAwayThemeDataSuccess(components, isRefresh);
  },

  // 根据分组alias 获取对应商品列表
  fetchGoodsByTagAlias(tagAlias, pageNo = 1, options) {
    app.carmen({
      api: 'weapp.wsc.tag.items/1.0.0/get',
      query: {
        alias: tagAlias,
        page_size: options.page_size || 20,
        store_id: app.globalData.tangshi.storeId,
        page_no: pageNo,
      },
      config: {
        noStoreId: true
      },
      success: (response) => {
        each(response.items, (item) => {
          if (!item.pic_url) {
            item.pic_url = '/upload_files/no_pic.png';
          }
        });

        options.success && options.success(response);
      },
      fail: (response) => {
        options.fail && options.fail(response);
      },
      complete: () => {
        options.complete && options.complete();
      }
    });
  },

  fetchTakeawayGoodsByTagAlias(tagAlias, pageNo = 1, options) {
    app.request({
      path: '/wscshop/goods/goodsByTagAlias.json',
      data: {
        alias: tagAlias,
        page: pageNo,
        pageSize: options.page_size,
        isShowPeriod: 1,
      }
    }).then(res => {
      if (res && res.list && res.list.length) {
        res.list = res.list.map(item => {
          if (!item.image_url) {
            item.image_url = '/upload_files/no_pic.png';
          }
          item.pic_url = cdnImage(item.image_url, '!300x300.jpg');

          return omit(item, ['image_url', 'picture', 'origin', 'buy_url', 'width', 'height']);
        });
      }
      options.success && options.success(res);
    }).catch(res => {
      options.fail && options.fail(res);
    });
  },

  // showcaseHandleGoodsBuy(e) {
  //   const { presale, alias } = e.currentTarget.dataset;

  //   if (+presale === 1) {
  //     getPresaleSkuData(alias).then((presaleSkuData) => {
  //       this.setYZData({
  //         'theme.featureSkuData': presaleSkuData
  //       });
  //     }).catch(() => {
  //       // 普通预售走老sku
  //       this.showComponentSKU({
  //         alias,
  //         needFetch: true,
  //         btns: ['cart', 'buy']
  //       });
  //     });

  //     return;
  //   }

  //   this.showComponentSKU({
  //     alias,
  //     needFetch: true,
  //     btns: ['cart', 'buy']
  //   });
  // },

  // 使用 zanui-weapp tab入口函数名需要固定
  handleZanTabChange(e) {
    switch (this.data.type) {
      case 'default':
        this.handleDefaultThemeZuiTabChange(e);
        break;
      case 'feature':
        this.handleFeatureThemeZuiTabChange(e);
        break;
      case 'topNav':
        this.handleTopNavThemeZuiTabChange(e);
        break;
      default:
        break;
    }
  },

  resolveFeatureTitle(config) {
    const {
      feature_title: pageTitleOptions,
      page_title_suffix: pageTitleSuffix,
      is_page_title_suffixed: isPageTitleSuffixDisplayed
    } = config;
    const { shopName } = app.globalData;
    let title = featureTitle;
    if (+pageTitleOptions === 1 && shopName) {
      title = shopName;
    }
    if (+isPageTitleSuffixDisplayed === 1) {
      title += ` - ${pageTitleSuffix}`;
    }
    return title;
  },

  fetchHomepage(isRefresh, cb) {
    return new Promise((resolve, reject) => {
      // 没有appId就不运行了
      if (!app.getAppId()) {
        return reject();
      }

      if (!isRefresh) {
        wx.showLoading({
          title: '加载中'
        });
      }

      // 获取点餐页面三个组件信息
      let featureQueryOption = {
        path: '/wscshop/showcase/tangshi/getCanteenComponents.json',
        data: {
          store_id: app.globalData.tangshi.storeId
        }
      };
      // 在初始加载时，先不等待 init.json, 把数据拉回来
      const isUseShopInfo = app.globalData.fetchedShop || isRefresh;
      // 微页面采用node接口
      // if (this.data.alias && !this.data.isHomePage) {
      // let ajax = request(app);
      app.request({ ...featureQueryOption }).then(res => {
        let response = res;
        // 检查返回值是否可用
        // 因为可能需要异步等待 店铺初始化 数据的返回，所以改为 promise
        this.checkCanUseResponse.call(this, isUseShopInfo)
          .catch((error) => {
            // 让外层promise能够resolve掉
            resolve();
            this.__fetchPromise = this.fetchHomepage(false, cb);
            return Promise.reject(error);
          })
          .then(() => {
            this.resolveFeatureData.call(this, {
              response,
              cb,
              resolve,
              isRefresh
            });

            // 获取过数据，直接重置为 true
            this.__featureFetched = true;
          });
        if (isRefresh) {
          wx.stopPullDownRefresh();
        }
      }).catch(res => {
        wx.hideLoading();

        // 获取数据失败，也重置为 true
        this.__featureFetched = true;

        if (res.code === 60501) {
          this.showZanToast('小程序和有赞店铺信息不匹配');
          app.storage.remove('app:token');
        } else {
          this.showZanToast('获取信息失败');
        }
        if (isRefresh) {
          wx.stopPullDownRefresh();
        }
        reject();
      });
    });
  },

  checkCanUseResponse(isUseShopInfo) {
    return new Promise((resolve, reject) => {
      if (!isUseShopInfo) {
        if (app.globalData.fetchedShop) {
          if (!this.data.isMultiStore) {
            resolve();
          } else {
            reject();
          }
          return;
        }

        app.once('app:fetchshopinfo:success', () => {
          app.off('app:fetchshopinfo:fail', null, this);
          if (!this.data.isMultiStore) {
            resolve();
          } else {
            reject();
          }
        }, this);
        app.once('app:fetchshopinfo:fail', () => {
          app.off('app:fetchshopinfo:success', null, this);
          resolve();
        }, this);
        return;
      }

      resolve();
    });
  },

  resolveFeatureData(options = {}) {
    const { cb = () => {}, resolve, isRefresh } = options;
    let { response } = options;
    const showFloatingNav = true;

    wx.hideLoading();
    featureTitle = response.title || '';

    // 兼容数据
    response = JSON.parse(response.components);
    this.showShowcaseComponents(response.sub_entry, isRefresh);

    this.setYZData({
      fetching: false,
      id: response.id,
      showFloatingNav
    }, () => {
      // 异步获取数据成功后发信号
      this.trigger('feature:loaded', this.data.isHomePage ? response.featureId : response.id, response.title);
    });
    cb && cb();
    resolve();
  },

  // 单人模式开吃请求 人数默认为1
  // startEat() {
  //   app.request({
  //     method: 'POST',
  //     path: '/wscshop/showcase/tangshi/startEatInfo.json',
  //     data: {
  //       alias: app.globalData.tangshi.alias,
  //       mode: app.globalData.tangshi.mode,
  //       headCount: 1
  //     }
  //   })
  //     .then(() => {})
  //     .catch(err => {
  //       this.setYZData({
  //         isLoading: false
  //       });
  //       wx.showToast({
  //         title: err.msg || '请求错误',
  //         icon: 'none'
  //       });
  //     });
  // },

  // 获取购物车内商品数据
  // getCart() {
  //   const { alias, mode = 0, orderVersion = 0 } = app.globalData.tangshi || {};
  //   app.request({
  //     path: '/wscshop/showcase/tangshi/getCart.json',
  //     query: {
  //       alias,
  //       mode,
  //       orderVersion
  //     }
  //   })
  //     .then((res) => {
  //       res.itemList.forEach(item => {
  //         if (item.buyerInfo.buyerId == app.getBuyerId()) {
  //           item.cartItems.forEach(i => {
  //             const params = {
  //               goodsId: i.goodsId,
  //               goodsName: i.title,
  //               alias: i.alias,
  //               num: i.num,
  //               noneSku: i.sku === '',
  //               skuName: (i.sku && [i.sku[0].v]) || '',
  //               stockNum: i.totalStock,
  //               skuId: i.skuId,
  //               message: {},
  //               price: i.price,
  //               groupid: i.groupIndex,
  //               type: 'plus',
  //               picture: i.imageUrl
  //             };
  //             this.ThemeTakeAwayhandleSkuSelected(params, 'refresh');
  //           });
  //         }
  //       });
  //     })
  //     .catch(err => {
  //       wx.showToast({
  //         title: err.msg || '请求错误',
  //         icon: 'none'
  //       });
  //     });
  // },

});
