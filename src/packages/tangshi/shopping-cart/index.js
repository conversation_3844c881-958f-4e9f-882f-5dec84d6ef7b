import WscPage from 'pages/common/wsc-page/index';
import cartAction from '../common/action/cart';

const app = getApp();

WscPage(cartAction, {
  data: {
    showMode: true,
    headcount: 1,
    text: '',
    btnText: '',
    icon: 'https://img.yzcdn.cn/image/wait.png',
    tableNum: '',
    totalPrice: 0,
    showEmpty: true,
    goodsList: [],
    cartGoods: {},
    orderInfo: {
      title: '口味偏好',
      value: '',
      isLink: true,
      toUrl: ''
    },
    memos: {
      list: [
        {
          categoryId: 0,
          id: '001',
          title: '不要辣',
          selected: false
        },
        {
          categoryId: 0,
          id: '002',
          title: '微辣',
          selected: false
        },
        {
          categoryId: 0,
          id: '003',
          title: '中辣',
          selected: false
        },
        {
          categoryId: 0,
          id: '004',
          title: '重辣',
          selected: false
        },
        {
          categoryId: 1,
          id: '005',
          title: '少盐',
          selected: false
        },
        {
          categoryId: 2,
          id: '006',
          title: '少油',
          selected: false
        },
        {
          categoryId: 3,
          id: '007',
          title: '不要蒜',
          selected: false
        },
        {
          categoryId: 4,
          id: '008',
          title: '不要葱',
          selected: false
        },
        {
          categoryId: 5,
          id: '009',
          title: '少放醋',
          selected: false
        },
        {
          categoryId: 5,
          id: '010',
          title: '多放醋',
          selected: false
        }
      ],
      text: ''
    },
    clearCartDialog: false,
    submitCartDialog: false
  },
  onLoad() {
    const { mode = 0, headcount = 1, memos = {} } = app.globalData.tangshi || {};
    const orderInfoFlavor = memos.list || this.data.memos.list;
    let orderInfoFlavorValue = memos.text || this.data.memos.text;
    orderInfoFlavor.forEach(item => {
      if (item.selected === true) {
        orderInfoFlavorValue = orderInfoFlavorValue === '' ? `${item.title}` : `${orderInfoFlavorValue},${item.title}`;
      }
    });
    this.setYZData({
      orderInfo: {
        ...this.data.orderInfo,
        value: orderInfoFlavorValue
      },
      showMode: mode !== 0,
      headcount,
      text: mode === 1 ? '确认菜品后请下单至厨房备菜' : '请先付款，订单才能下单至厨房',
      btnText: mode === 1 ? '下单到后厨' : '去支付'
    });
    app.globalData.tangshi.orderInfoFlavorValue = orderInfoFlavorValue;
    mode === 0 ? this.getCart() : this.initCartData();
  },

  onShow() {
    this.setYZData({
      headcount: app.globalData.tangshi.headCount || 1
    });
  },

  onUnload() {
    clearTimeout(this.timer);
  },

  // 接口轮询 获取购物车内容
  initCartData() {
    this.getCart();
    this.timer = setTimeout(this.initCartData, 5000);
  },

  // 加菜
  addGoods() {
    clearTimeout(this.timer);
    wx.navigateTo({
      url: '/packages/tangshi/order/index'
    });
  },

  // 点击支付按钮
  gotoPay() {
    if (this.data.showEmpty) {
      return wx.showToast({
        title: '购物车内没有菜品，请前去加菜',
        icon: 'none'
      });
    }
    if (app.globalData.tangshi.mode !== 1) {
      return this.getCart().then(() => this.cacheAction());
    }
    this.setYZData({
      submitCartDialog: true
    });
  },

  // 下单至后厨
  goToSubmit() {
    // 更新购物车数据内容后下单至后厨
    this.getCart().then(() => this.submit());
  },

  // 更新购物车内商品数量
  handleGoodsNumChange(e) {
    const { value, type } = e.detail;
    const {
      itemid, skuid, title, sku, imageurl, price, groupindex, buyerid
    } = e.target.dataset;
    const {
      alias, mode = 0, storeId = 0, orderVersion = 0
    } = app.globalData.tangshi || {};
    const choosedData = {
      alias,
      itemId: itemid,
      skuId: skuid,
      title,
      sku,
      imageUrl: imageurl,
      price,
      num: type === 'plus' ? 1 : -1,
      groupIndex: groupindex,
      mode,
      storeId,
      orderVersion,
      buyerId: buyerid
    };
    this.setYZData({
      [`cartGoods.${e.target.dataset.skuid + '' + e.target.dataset.buyerid}.num`]: value
    });
    this.notifyChangeChoosed(choosedData);
  },

  // 关闭弹窗
  onClose(event) {
    if (event.detail === 'confirm') {
      // 异步关闭弹窗
      setTimeout(() => {
        this.setYZData({
          clearCartDialog: false,
          submitCartDialog: false
        });
      }, 500);
    } else {
      this.setYZData({
        clearCartDialog: false,
        submitCartDialog: false
      });
    }
  },

  // 清空购物车时需要弹框二次确认
  clearCart() {
    this.setYZData({
      clearCartDialog: true
    });
  },

  // 清楚定时器
  clearTimerFuc() {
    clearTimeout(this.timer);
  }
});
