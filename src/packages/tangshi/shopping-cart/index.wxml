<wxs src="../common/tools.wxs" module="tools" />
<page-container
  class="{{ themeClass }} page-{{ deviceType }}" fixed-bottom="{{ true }}"
>
  <view class="cart">
    <view class="order-status">
      <image class="status-icon" src="{{ icon }}"></image>
      <text class="status-text">{{ text }}</text>
    </view>
    <view class="cart-goods-panel">
      <view class="cart-goods-panel__table van-hairline--bottom">桌位：{{ tableNum }}</view>
      <view class="cart-goods-panel__empty" wx:if="{{showEmpty}}">菜单里还没菜哦</view>
      <block wx:if="{{!showEmpty}}" wx:for="{{ goodsList }}" wx:key="unique" wx:for-item="goodsItem">
        <view class="cart-goods-panel__buyer">
          <image class="img" src="{{goodsItem.buyerInfo.headImage}}"></image>
          <view class="span">{{ goodsItem.buyerInfo.nickName }}</view>
        </view>
        <view class="goods-wrap" wx:for="{{ goodsItem.cartItems }}" wx:key="unique" wx:for-item="cartItem">
          <view class="goods" wx:if="{{cartGoods[cartItem.skuId+''+goodsItem.buyerInfo.buyerId].num > 0}}">
            <view class="goods-detail goods-detail--listmode">
              <view class="section">
                <view class="goods-detail__title goods-detail__title--listmode">{{ cartItem.title }}</view>
                <view class="goods-detail__sku">{{ tools.skuStr(cartItem.sku) }}</view>
              </view>
              <view class="goods-detail__price goods-detail__price--listmode">
                <view class="goods-detail__price--pay listmode">¥<text class=".span">{{ tools.cent2yuan(cartItem.price) }}</text></view>
              </view>
            </view>
            <input-number
              data-alias="{{cartItem.alias}}"
              data-itemid="{{cartItem.itemId}}"
              data-skuid="{{cartItem.skuId}}"
              data-title="{{cartItem.title}}"
              data-sku="{{cartItem.sku}}"
              data-imageurl="{{cartItem.imageUrl}}"
              data-price="{{cartItem.price}}"
              data-groupindex="{{cartItem.groupIndex}}"
              data-buyerid="{{goodsItem.buyerInfo.buyerId}}"
              class="goods-iptn goods-iptn--listmode"
              value="{{ cartGoods[cartItem.skuId+''+goodsItem.buyerInfo.buyerId].num }}"
              max="{{ cartItem.totalStock }}"
              min="{{0}}"
              is-value-show="{{ cartItem.num > 0 }}"
              is-minus-show="{{ cartItem.num > 0 }}"
              bind:change="handleGoodsNumChange"
            >
            </input-number>
          </view>
        </view>
      </block>
      <view class="cart-goods-panel__opt van-hairline--top">
        <view bindtap="clearCart" class="cart-goods-panel__opt--clear">清空菜品</view>
        <view class="cart-goods-panel__opt--total">小计：<text>¥{{ tools.cent2yuan(totalPrice) }}</text></view>
      </view>
    </view>
    <view class="order-panel">
      <van-cell-group>
        <van-cell title="订单信息" class="order-panel__title" />
          <van-cell
            title="{{orderInfo.title}}"
            is-link
            title-width="76px"
            value-class="order-info-detail"
            url="/packages/tangshi/order-info/index"
            bind:click="clearTimerFuc"
          >
            <view class="order-info-detail">
              {{orderInfo.value}}
            </view>
          </van-cell>
          <van-cell
            wx:if="{{showMode}}"
            title="用餐人数"
            is-link
            title-width="76px"
            value-class="order-info-detail"
            url="/packages/tangshi/order-info/index"
            bind:click="clearTimerFuc"
          >
            <view class="order-info-detail">
              {{headcount}}
            </view>
          </van-cell>
      </van-cell-group>
    </view>
  </view>
  <view class="bottom-bar">
    <view class="bottom-bar__left" bindtap="addGoods">加菜</view>
    <view class="bottom-bar__right" bindtap="gotoPay">{{ btnText }}</view>
  </view>
  <van-dialog
    async-close
    title=""
    message="确认清空购物车菜品?"
    confirm-button-text="确认"
    show="{{ clearCartDialog }}"
    show-cancel-button
    bind:close="onClose"
    bind:confirm="confirmClearCart"
    closeOnClickOverlay=true
    confirm-button-open-type="confirmClearCart"
  >
  </van-dialog>
  <van-dialog
    async-close
    title=""
    message="确认提交订单后至后厨吗？"
    confirm-button-text="提交"
    cancelButtonText="再看看"
    show="{{ submitCartDialog }}"
    show-cancel-button
    bind:close="onClose"
    bind:confirm="goToSubmit"
    closeOnClickOverlay=true
    confirm-button-open-type="submit"
  >
  </van-dialog>
  <van-dialog id="van-dialog" />
</page-container>
<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />