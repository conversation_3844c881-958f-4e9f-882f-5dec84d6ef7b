.cart {
  .order-status,
  .order-goods-panel,
  .cart-goods-panel,
  .order-panel {
    margin: 10px 5px;
    border-radius: 2px;
  }
}
.order-status {
  padding: 15px;
  background: #fff;
  box-shadow: rgba(0, 0, 0, .1) 0 1px 2px 0;
  display: flex;
  align-items: center;

  .status-icon {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }

  .status-text {
    color: #333;
    font-size: 14px;
    font-weight: bold;
  }
}
.cart-goods-panel {
  padding-left: 15px;
  background: #fff;
  box-shadow: rgba(0, 0, 0, .1) 0 1px 2px;

  &__table {
    color: #333;
    font-size: 14px;
    padding: 15px 0;
    background: url("https://img.yzcdn.cn/image/shop_icon.png") no-repeat center left;
    background-size: 16px 16px;
    text-indent: 20px;
    font-weight: 500;
  }

  &__empty {
    font-size: 12px;
    color: #999;
    text-align: center;
    padding: 20px 0;
  }

  &__buyer {
    display: flex;
    align-items: center;
    margin: 20px 0 15px 0;

    .img {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .span {
      font-size: 12px;
      color: #666;
    }
  }

  .goods-wrap {
    width: calc(100% - 7px);
    margin: 20px 0;
  }

  &__opt {
    padding: 15px 15px 15px 0;
    display: flex;
    justify-content: space-between;

    &--clear {
      background: url("https://img.yzcdn.cn/image/ciear.png") no-repeat center left;
      background-size: 16px 16px;
      text-indent: 20px;
      color: #3388FF;
      font-size: 14px;
    }

    &--total {
      color: #666;
      font-size: 12px;

      span {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}
// good
@mixin multi-ellipsis ($lines) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}
.goods {
  width: 100%;
  background-color: #fff;
  position: relative;
  display: flex;
  line-height:30px;

  &-img__wrap {
    display: inline-block;
    width: 60px;
    height: 60px;
    overflow: hidden;
    margin-right: 10px;
    background: #fafafa;
    position: relative;
  }

  &-img {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate3d(-50%, -50%, 0);
    max-width: 100%;
    max-height: 100%;
    border-radius: 2px;
  }

  &-detail {
    .section {
      width: 80%;
    }

    &__title {
      margin-bottom: 5px;
      color: #333;
      font-size: 14px;
      line-height: 18px;
      @include multi-ellipsis (2);
    }

    &__title--listmode {
      width: 90%;
      @include multi-ellipsis (1);
      margin: 0;
    }

    &__sku {
      margin-top: 5px;
      color: #666;
      font-size: 12px;
    }

    &__desc {
      margin-bottom: 5px;
      color: #666;
      font-size: 12px;
    }

    &__num {
      color: #333;
      font-size: 14px;
      margin: 2px 20px 0 0;
    }

    &__price {
      margin-top: 10px;

      &--pay {
        color: #FF4C4C;
        font-size: 10px;
        margin-right: 5px;

        .span {
          font-size: 14px;
        }

        &.listmode {
          font-size: 14px;
          color: #333;
        }
      }

      &--origin {
        color: #666;
        font-size: 10px;
        text-decoration: line-through;
      }
    }

    &__price--listmode {
      margin: 0;
    }
  }

  &-detail--listmode {
    width: 70%;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    &.show-num {
      width: 97%;
    }
  }

  &-iptn {
    position: absolute;
    right: 0;
    bottom: 0;
  }

  // &-iptn--listmode {
  //   top: -8px;
  // }
}
.order-panel {
  box-shadow: rgba(0, 0, 0, .1) 0 1px 2px 0;

  &__title {
    font-size: 12px;
    font-weight: bold;
  }

  .van-cell-group.van-hairline--top-bottom {
    &::after {
      border: 0;
    }
  }
  .order-info-detail{
    overflow:hidden; 
    text-overflow:ellipsis;
    white-space:nowrap; 
  }
}

.bottom-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 99;
  display: flex;
  width: 100%;

  &__left,
  &__right {
    height: 50px;
    line-height: 50px;
    color: #fff;
    font-size: 16px;
    text-align: center;
  }

  &__left {
    flex: 1;
    background-color: #444;
  }

  &__right {
    flex: 1;
    background-color: #ff4c4c;

    &.pay {
      flex: 3;
    }
  }
}