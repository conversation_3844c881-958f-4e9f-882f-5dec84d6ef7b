import { gcjToBaidu } from '@/helpers/lbs';

const app = getApp();

// 查询经纬度
export function queryGeo(address) {
  return new Promise((resolve, reject) => {
    app.carmen({
      api: 'youzan.logistics.geo/1.0.0/get',
      data: {
        city: address.city,
        address: `${address.province}${address.city}${address.county}${
          address.addressDetail
        }`
      },
      method: 'GET',
      success(res) {
        const { lat, lng } = res;
        const transformed = gcjToBaidu(lng, lat);
        // 高德坐标转百度坐标
        resolve({ lat: transformed.lat, lon: transformed.lng });
      },
      fail: reject
    });
  });
}

export function fetchAreaList() {
  return app
    .request({
      origin: 'cashier',
      method: 'GET',
      path: '/wsctrade/uic/address/getAllRegion.json',
      data: {
        showOversea: true
      }
    });
}
