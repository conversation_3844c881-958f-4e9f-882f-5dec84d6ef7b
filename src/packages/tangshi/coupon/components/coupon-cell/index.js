import WscComponent from 'pages/common/wsc-component/index';
import { format } from 'utils/time-utils';

WscComponent({
  externalClasses: [
    'theme-color'
  ],
  properties: {
    coupon: {
      type: Object,
      observer(value) {
        // ?? ob obj
        this.setYZData({
          extra: this.handleCoupon(value)
        });
      }
    },
    disabled: <PERSON><PERSON><PERSON>,
    selected: <PERSON><PERSON><PERSON>
  },
  data: {
    showMore: false, // 是否展开不可用条件
    extra: {}, // 用于展示，由优惠券数据产生
  },
  methods: {
    handleCoupon(coupon) {
      const startAt = format(new Date(1000 * coupon.startAt), 'yyyy.MM.dd');
      const endAt = format(new Date(1000 * coupon.endAt), 'yyyy.MM.dd');
      const condition = coupon.originCondition / 100;
      const extra = {
        conditionMessage: condition === 0 ? '无使用门槛' : `满${condition}元可用`,
        value: coupon.denominations / 100,
        discount: coupon.discount / 10,
        validPeriod: `有效期：${startAt} - ${endAt}`,
      };
      return extra;
    },
    // 切换不可用原因 展开状态
    handleShowMore() {
      this.setYZData({
        showMore: !this.data.showMore
      });
    },
    // 选中优惠券
    handleClick() {
      this.triggerEvent('click', this.data.coupon);
    }
  }
});
