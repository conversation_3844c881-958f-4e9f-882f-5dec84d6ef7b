<view class="component {{ disabled ? 'disabled' : '' }}" bindtap="handleClick">
  <view class="body">
    <view class="left">
      <view wx:if="{{ extra.value }}" class="price theme-color">{{ extra.value }}</view>
      <view wx:else class="discount theme-color">{{ extra.discount }}</view>
      <view class="condition">{{ extra.conditionMessage }}</view>
    </view>
    <view class="right">
      <view class="title">{{ coupon.name }}</view>
      <view class="valid">{{ extra.validPeriod }}</view>
    </view>
    <van-icon wx:if="{{ selected }}" name="checked" class="check-icon theme-color" />
  </view>

  <view
    wx:if="{{ disabled && coupon.reason }}"
    class="footer van-hairline--top"
    catchtap="handleShowMore"
  >
    <view class="{{ showMore ? '' : 'van-ellipsis' }}">{{ coupon.reason }}</view>
    <view class="dot left"></view>
    <view class="dot right"></view>
  </view>
</view>
