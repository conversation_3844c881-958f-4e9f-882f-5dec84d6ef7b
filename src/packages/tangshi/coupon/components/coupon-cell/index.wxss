@import 'shared/common/css/helper/index.wxss';

.component {
  margin: 10px 15px;
  border-radius: 4px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
}

.body {
  display: flex;
  height: 100px;
  padding: 0 15px;
  background-color: #fff;
  border-radius: 4px;
  align-items: center;
}

.left {
  flex-shrink: 0;
  width: 90px;
}

.right {
  flex: 1;
}

.price {
  font-size: 24px;
  font-weight: bold;
  color: #f44;
}

.price::before {
  margin-right: 5px;
  font-size: 12px;
  content: '¥';
}

.discount {
  font-size: 24px;
  font-weight: bold;
  color: #f44;
}

.discount::after {
  margin-left: 5px;
  font-size: 12px;
  content: '折';
}

.condition {
  font-size: 12px;
  color: #999;
}

.title {
  font-size: 14px;
  font-weight: bold;
  color: #111;
}

.disabled .price {
  color: #999;
}

.disabled .title {
  color: #999;
}

.disabled .discount {
  color: #999;
}

.valid {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
}

.footer {
  position: relative;
  padding: 6px 15px;
  margin-top: -10px;
  font-size: 12px;
  color: #999;
  background-color: #fbfbfb;
  border-radius: 4px;
}

.footer::after {
  border-top-style: dashed;
}

.dot {
  position: absolute;
  top: 0;
  z-index: 2;
  width: 10px;
  height: 10px;
  background-color: #f9f9f9;
  border-radius: 50%;
}

.dot::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  content: '';
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1) inset;
}

.dot.left {
  left: 0;
  transform: translate(-50%, -50%);
}

.dot.left::after {
  clip: rect(0, 10px, 10px, 5px);
}

.dot.right {
  right: 0;
  transform: translate(50%, -50%);
}

.dot.right::after {
  clip: rect(0, 5px, 10px, 0);
}

.check-icon {
  flex-shrink: 0;
}
