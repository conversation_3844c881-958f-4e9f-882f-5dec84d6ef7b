<view class="page {{ themeClass }} page-{{ deviceType }}">
  <!--优惠码兑换-->
  <van-field
    wx:if="{{ !forbidcode }}"
    value="{{ code }}"
    center
    placeholder="请输入优惠码"
    use-button-slot
    maxlength="20"
    border="{{ false }}"
    bind:change="onCodeInput"
    bind:focus="onCodeFocus"
  >
    <van-button
      slot="button"
      size="small"
      custom-class="theme-bg-color"
      disabled="{{ code.length <= 0 }}"
      bind:click="onExchangeTaped"
    >兑换</van-button>
  </van-field>

  <van-tabs
    custom-class="th_tabs-l"
    color="{{ themeGeneralColor }}"
  >
    <van-tab title="可使用优惠券（{{charge_coupon.length}}）">
      <coupon-cell
        wx:for="{{ charge_coupon }}"
        wx:key="{{ item.id }}"
        coupon="{{ item }}"
        selected="{{ selected_coupon.id == item.id }}"
        bind:click="handleChooseCoupon"
        theme-color="theme-color"
      />
    </van-tab>
    <van-tab title="不可使用优惠券（{{ unavailable_coupon.length }}）">
      <coupon-cell
        wx:for="{{ unavailable_coupon }}"
        coupon="{{ item }}"
        wx:key="{{ item.id }}"
        disabled
      />
    </van-tab>
  </van-tabs>

  <view class="coupon-not-use" bindtap="onNotUseTapped">不使用优惠</view>
</view>

<van-toast id="van-toast" />
