import Toast from '@vant/weapp/dist/toast/toast';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import WscPage from 'pages/common/wsc-page/index';
import theme from 'shared/common/components/theme-view/theme';
import { validCouponCode } from './request';

const app = getApp();

WscPage({
  data: {
    themeGeneralColor: '',
    forbidcode: true, // 禁用优惠码兑换功能
    charge_coupon: [], // 可用优惠券
    unavailable_coupon: [], // 不可用优惠券
    selected_coupon: {}, // 已选中优惠券
    code: '', // 优惠码
    exchangeParams: {} // 从下单页传过来的参数，不用关心内容，用于兑换优惠码接口
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const dbid = options.dbid || '';
    const forbidcode = !!options.forbidcode;
    const param = app.db.get(dbid) || {};
    const { charge_coupon = [], unavailable_coupon = [] } = param;

    theme.getThemeColor('general').then(color => {
      this.setYZData({
        themeGeneralColor: color
      });
    }).catch(() => {});

    this.setYZData({
      forbidcode,
      charge_coupon: mapKeysCase.toCamelCase(charge_coupon),
      unavailable_coupon: mapKeysCase.toCamelCase(unavailable_coupon),
      selected_coupon: param.selected_coupon || {},
      exchangeParams: param.exchangeParams || {}
    });
  },

  // 选择优惠券
  handleChooseCoupon(event) {
    const coupon = event.detail;
    // this.trigger('order-coupon-change', mapKeysCase.toSnakeCase(coupon));
    coupon.priceStr = (coupon.price_value / 100) + '';
    app.globalData.tangshi.choosedCouponDetail = coupon;
    wx.navigateBack();
  },

  // 选择优惠券
  onCouponCellTaped(coupon) {
    if (!coupon) return;
    coupon.priceStr = (coupon.price_value / 100) + '';
    app.globalData.tangshi.choosedCouponDetail = coupon;
    wx.navigateBack();
  },


  // 兑换优惠码
  onExchangeTaped() {
    if (this.data.code.length == 0) return;

    const { charge_coupon, exchangeParams, code } = this.data;

    validCouponCode({ ...exchangeParams, code }).then(res => {
      res.start_at = Date.now() / 1000;
      res.discount = 0; // 优惠码没有折扣功能
      res.denominations = res.value;
      if (res.condition.indexOf('立减') >= 0) {
        res.origin_condition = 0;
      } else {
        const end = res.condition.indexOf('元');
        res.origin_condition = res.condition.substring(1, end) * 100;
      }

      // 查找是否有重复的
      const isDuplicate = charge_coupon.map(item => +item.id).indexOf(+res.id) > -1;

      // 没有找到重复的，才往list里面添加
      if (!isDuplicate) {
        charge_coupon.unshift(mapKeysCase.toCamelCase(res));

        this.trigger('order-coupon-change', res);
        this.setYZData({ code: '' });
      } else {
        Toast('这张优惠券你已经有啦，已经为你选中');
      }
      this.setYZData({ charge_coupon, selected_coupon: res });
    }).catch(err => {
      Toast(err.msg || '兑换失败～');
    });
  },
  // 输入优惠码
  onCodeInput(event) {
    this.setYZData({ code: event.detail });
  },
  // 点击不使用优惠券
  onNotUseTapped() {
    this.trigger('order-coupon-change', {});
    wx.navigateBack();
  },
});
