import WscPage from 'pages/common/wsc-page/index';

const app = getApp();
WscPage({
  data: {
    currentEatNum: 1,
    showNum: false,
    flavorMsg: '',
    flavorCollection: [
      {
        categoryId: 0,
        id: '001',
        title: '不要辣',
        selected: false
      },
      {
        categoryId: 0,
        id: '002',
        title: '微辣',
        selected: false
      },
      {
        categoryId: 0,
        id: '003',
        title: '中辣',
        selected: false
      },
      {
        categoryId: 0,
        id: '004',
        title: '重辣',
        selected: false
      },
      {
        categoryId: 1,
        id: '005',
        title: '少盐',
        selected: false
      },
      {
        categoryId: 2,
        id: '006',
        title: '少油',
        selected: false
      },
      {
        categoryId: 3,
        id: '007',
        title: '不要蒜',
        selected: false
      },
      {
        categoryId: 4,
        id: '008',
        title: '不要葱',
        selected: false
      },
      {
        categoryId: 5,
        id: '009',
        title: '少放醋',
        selected: false
      },
      {
        categoryId: 5,
        id: '010',
        title: '多放醋',
        selected: false
      }
    ]
  },

  onLoad() {
    const { mode = 0, memos = {} } = app.globalData.tangshi || {};
    this.setYZData({
      showNum: mode !== 0,
      flavorCollection: memos.list || this.data.flavorCollection,
      flavorMsg: memos.text || this.data.flavorMsg,
      currentEatNum: app.globalData.tangshi.headCount || 1
    });
  },

  // 挑选口味及时更新数据
  chooseFlavor(e) {
    const { categoryid, id } = e.target.dataset;

    this.data.flavorCollection.forEach((item, index) => {
      if (item.id === id) {
        this.setYZData({
          [`flavorCollection[${index}].selected`]: !this.data.flavorCollection[index].selected
        });
      }
      // 同类其他口味要清除
      if (item.categoryId === categoryid && item.id !== id) {
        this.setYZData({
          [`flavorCollection[${index}].selected`]: false
        });
      }
    });
  },

  flavorTextChange(event) {
    // 保证迟于input原生赋值
    setTimeout(() => {
      this.setYZData({
        flavorMsg: event.detail
      });
    }, 100);
  },

  // 保存口味
  keepMemos() {
    const { alias, mode, headCount = 1 } = app.globalData.tangshi;
    const memos = {
      list: this.data.flavorCollection,
      text: this.data.flavorMsg
    };
    // 提交口味
    app.request({
      method: 'POST',
      path: '/wscshop/showcase/tangshi/saveCartMemo.json',
      data: {
        alias,
        mode,
        headCount,
        memos
      }
    })
      .then(() => {
        app.globalData.tangshi.memos = memos;
        wx.navigateTo({
          url: '/packages/tangshi/shopping-cart/index'
        });
      })
      .catch(err => {
        wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  onChangeNum(e) {
    this.setYZData({
      currentEatNum: e.detail
    });
    app.globalData.tangshi.headCount = e.detail;
  }
});
