<page-container
  class="{{ themeClass }} page-{{ deviceType }}" fixed-bottom="{{ true }}"
>
  <view class="order-info">
    <view class="order-info__flavor">
      <van-cell-group>
        <van-cell title="口味偏好" />
        <view class="order-info__flavor--block">
          <view
            wx:for="{{flavorCollection}}"
            wx:key="unique"
            data-categoryId="{{item.categoryId}}"
            data-id="{{item.id}}"
            data-title="{{item.title}}"
            class="flavor van-hairline--surround {{item.selected ? 'active' : ''}}"
            bindtap="chooseFlavor"
          >{{ item.title }}</view>
        </view>
        <van-field
          value="{{flavorMsg}}"
          type="textarea"
          placeholder="请输入口味说明（最多输入50个字）"
          rows="1"
          clearable
          autosize="{ maxHeight: 50, minHeight: 50 }"
          bind:change="flavorTextChange"
        />
      </van-cell-group>
    </view>
    <view class="order-info__num" wx:if="{{showNum}}">
      <van-cell-group>
        <van-cell title="用餐人数" />
        <view class="order-info__num--stepper">
          <van-stepper
            max="20"
            value="{{currentEatNum}}"
            bind:change="onChangeNum"
          />
        </view>
      </van-cell-group>
    </view>

    <van-button class="order-info__btn" size="large" type="danger" bindtap="keepMemos">保存</van-button>
  </view>
</page-container>
<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />