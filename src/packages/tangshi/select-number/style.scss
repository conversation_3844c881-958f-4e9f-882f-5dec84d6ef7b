.select-number {
  min-height: 100vh;
  background-color: #fff;

  &__wrap {
    margin: 0 5px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    position: relative;
    top: 18px;
  }

  &__item {
    margin: 0 15px 15px 0;

    &:nth-child(4n) {
      margin-right: 0;
    }
  }

  &__btn {
    position: fixed;
    bottom: 15px;
    left: 50%;
    z-index: 1;
    transform: translateX(-50%);
    width: 92vw;
    line-height: 12vw;
    border-radius: 2px;
    background-color: #e33;
    text-align: center;
    color: #fff;
  }

  .van-field {
    width: 275px;
    height: 45px;
    margin: 20px auto;
    font-size: 16px;

    &__control {
      height: 22px;
      line-height: 22px;
    }
  }
}
.number-block {
  width: 75px;
  line-height: 75px;
  text-align: center;
  color: #333;
  font-size: 20px;
  border-radius: 2px;

  &.active {
    color: #fff;
    background: #e33;
    border: 1px solid #e33;
  }
}

@media screen and (max-device-width: 360px){
  .number-block {
    width: 60px;
    line-height: 60px;
  }
}

@media screen and (min-device-width: 768px){
  .number-block {
    width: 92px;
    line-height: 92px;
  }
}