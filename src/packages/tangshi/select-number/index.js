import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    isActive: 1,
    num: 1,
    inputNum: null,
    // 用户有登录有赞账号
    binded: true,
    show: false,
    isLoading: false,
    btnComfirmDisabled: true
  },

  // 选择人数
  onSelectNum(e) {
    // 选择超过7人 弹出输入框
    if (e.target.dataset.num == 8) {
      this.setYZData({
        show: true
      });
    } else {
      this.setYZData({
        isActive: e.target.dataset.num,
        num: e.target.dataset.num
      });
    }
  },

  // 关闭弹框
  onClose(event) {
    if (event.detail === 'confirm') {
      // 异步关闭弹窗
      setTimeout(() => {
        this.setYZData({
          show: false
        });
      }, 500);
    } else {
      this.setYZData({
        show: false
      });
    }
  },

  // 获取人数
  getNum() {
    this.setYZData({
      isLoading: true
    });
    if (this.data.inputNum != null) {
      this.setYZData({
        num: this.data.inputNum
      });
    }
    // 提交人数
    app.request({
      method: 'POST',
      path: '/wscshop/showcase/tangshi/startEatInfo.json',
      data: {
        alias: app.globalData.tangshi.alias,
        mode: app.globalData.tangshi.mode,
        headCount: this.data.num
      }
    })
      .then(() => {
        this.setYZData({
          isLoading: false
        });
        app.globalData.tangshi.headCount = this.data.num;
        wx.navigateTo({
          url: '/packages/tangshi/order/index'
        });
      })
      .catch(err => {
        this.setYZData({
          isLoading: false
        });
        wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  // 监听输入框
  onInputChange(event) {
    // 自动校正输入内容 不能超过20的正整数
    const reg = /^(1?\d|20)$/;
    const ifNum = /^[0-9]+.?[0-9]*$/;
    let showNum;
    if (reg.test(event.detail)) {
      showNum = event.detail;
    } else {
      showNum = ifNum.test(event.detail) ? event.detail.slice(0, -1) : null;
    }
    // 保证迟于input原生赋值
    setTimeout(() => {
      this.setYZData({
        inputNum: showNum
      });
    }, 100);
  },
});
