<page-container
  class="{{ themeClass }} page-{{ deviceType }}" fixed-bottom="{{ true }}"
>
  <view class="select-number">
    <view class="select-number__wrap">
      <block wx:for="{{[1, 2, 3, 4, 5, 6, 7, 8]}}" wx:key="i" wx:for-item="i">
        <view bindtap="onSelectNum" data-num="{{i}}" class="zan-hairline--surround select-number__item number-block {{ isActive == i ? 'active' : '' }}">{{i == 8 ? '更多' : i}}</view>
      </block>
    </view>
    <view class="select-number__btn" bindtap="getNum">
      开始点餐
    </view>
  </view>
  <van-dialog
    class="up-index"
    use-slot
    async-close
    title="输入用餐人数"
    confirm-button-text="开始点餐"
    show="{{ show }}"
    show-cancel-button
    bind:close="onClose"
    bind:confirm="getNum"
    closeOnClickOverlay
    confirm-button-open-type="getNum"
  >
    <van-field
      type="number"
      value="{{ inputNum }}"
      maxlength="2"
      placeholder="请输入用餐人数"
      bind:change="onInputChange"
    />
  </van-dialog>
  <van-loading wx:if="{{isLoaing}}" />
</page-container>
<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />