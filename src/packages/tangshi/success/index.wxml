<wxs src="../common/tools.wxs" module="tools" />
<page-container
  class="{{ themeClass }} page-{{ deviceType }}" fixed-bottom="{{ true }}"
>
  <view class="paid-status">
    <view class="header van-hairline--bottom">
      <view>
        <view class="success-icon"></view>
        <view class="paid-success-tips">订单支付成功</view>
      </view>
      <view class="price-warp">
        <text>¥ {{ tools.cent2yuan(realPay) }}</text>
      </view>
    </view>
    <van-cell title="支付方式" value="{{payWay}}"/>
    <van-button class="btn" bindtap="goDetailPage">查看订单详情</van-button>
  </view>
</page-container>