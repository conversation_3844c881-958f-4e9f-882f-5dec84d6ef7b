.paid-status {
  .header {
    position: relative;
    padding: 30px 0;
    background: #fff;
    text-align: center;

    p {
      position: relative;
      top: 3px;
      font-size: 12px;
      color: #666;
    }

    .success-icon {
      background: url("https://b.yzcdn.cn/v2/image/wap/cashier/<EMAIL>") no-repeat center;
      display: block;
      height: 60px;
      background-size: 60px 60px;
    }

    .paid-success-tips {
      padding: 5px 0;
    }

    .price-warp {
      font-size: 20px;
      text-align: center;
      color: #f60;
    }
  }

  .van-cell__value {
    color: #666;
  }

  .btn {
    text-align:center;
    display: block;
    margin: 20px auto 0;
  }
}
