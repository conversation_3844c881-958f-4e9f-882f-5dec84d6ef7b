import WscPage from 'pages/common/wsc-page/index';

const app = getApp();

WscPage({
  data: {
    orderNo: null,
    realPay: 0,
    payWay: ''
  },
  onLoad() {
    this.setYZData({
      orderNo: app.globalData.tangshi.orderNo
    });
    app.request({
      path: '/pay/wsctrade_tangshi/paySuccess.json',
      origin: 'cashier',
      query: {
        orderNo: this.data.orderNo
      }
    })
      .then((res) => {
        this.setYZData({
          realPay: res.payAmount,
          payWay: res.buyWayDesc
        });
      })
      .catch(err => {
        wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },
  goDetailPage() {
    wx.navigateTo({
      url: '/packages/tangshi/order-detail/index'
    });
  }
});
