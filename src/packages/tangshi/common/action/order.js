import Dialog from '@vant/weapp/dist/dialog/dialog';
import { ERROR_CODE } from '../config';

const app = getApp();
export default {
  // 单人模式开吃请求 人数默认为1
  startEat() {
    app.request({
      method: 'POST',
      path: '/wscshop/showcase/tangshi/startEatInfo.json',
      data: {
        alias: app.globalData.tangshi.alias,
        mode: app.globalData.tangshi.mode,
        headCount: 1
      }
    })
      .then(() => {})
      .catch(err => {
        this.setYZData({
          isLoading: false
        });
        wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  // 获取购物车内商品数据
  getCart() {
    const { alias, mode = 0, orderVersion = 0 } = app.globalData.tangshi || {};
    app.request({
      path: '/wscshop/showcase/tangshi/getCart.json',
      query: {
        alias,
        mode,
        orderVersion
      }
    })
      .then((res) => {
        res.itemList.forEach(item => {
          if (item.buyerInfo.buyerId == app.getBuyerId()) {
            item.cartItems.forEach(i => {
              const params = {
                goodsId: i.goodsId,
                goodsName: i.title,
                alias: i.alias,
                num: i.num,
                noneSku: i.sku === '',
                skuName: (i.sku && [i.sku[0].v]) || '',
                stockNum: i.totalStock,
                skuId: i.skuId,
                message: {},
                price: i.price,
                groupid: i.groupIndex,
                type: 'plus',
                picture: i.imageUrl
              };
              this.ThemeTakeAwayhandleSkuSelected(params, 'refresh');
            });
          }
        });
      })
      .catch(err => {
        if (err.code === ERROR_CODE.PAIED) {
          return Dialog.confirm({
            title: '',
            message: JSON.parse(err.msg).errorMsg,
            confirmButtonText: '去看看',
            showCancelButton: false
          }).then(() => {
            wx.navigateTo({
              url: '/packages/tangshi/order-detail/index?extra=' + JSON.parse(err.msg).extra
            });
          });
        } if (err.code === ERROR_CODE.CANCELED) {
          return Dialog.confirm({
            title: '',
            message: JSON.parse(err.msg).errorMsg,
            confirmButtonText: '我知道了',
            showCancelButton: false
          }).then(() => {
            wx.navigateTo({
              url: '/packages/tangshi/start/index'
            });
          });
        }
        return wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  // 通知后端变动的商品
  notifyChangeChoosed(goods) {
    const { mode = 0, storeId = 0, orderVersion = 0 } = app.globalData.tangshi || {};
    const choosedData = {
      alias: app.globalData.tangshi.alias,
      itemId: goods.goodsId,
      skuId: goods.skuId,
      title: goods.goodsName,
      sku: '',
      imageUrl: goods.picture,
      price: goods.price,
      num: goods.type === 'minus' ? -goods.num : goods.num,
      groupIndex: goods.groupid,
      mode,
      storeId,
      orderVersion
    };
    app.request({
      method: 'POST',
      path: '/wscshop/showcase/tangshi/operateCart.json',
      data: choosedData
    })
      .then(() => {
      })
      .catch(err => {
        if (err.code === ERROR_CODE.SUBMITTED || err.code === ERROR_CODE.ORDERED || err.code === ERROR_CODE.CANCELED) {
          const message = err.code === ERROR_CODE.PAIED ? JSON.parse(err.msg).errorMsg : err.msg;
          const confirmButtonText = err.code === ERROR_CODE.PAIED ? '去看看' : '知道啦';
          return Dialog.confirm({
            title: '',
            message,
            confirmButtonText,
            showCancelButton: false
          }).then(() => {
          // return Dialog.confirm({
          //   title: '',
          //   message: err.msg,
          //   confirmButtonText: '去看看',
          //   showCancelButton: false
          // }).then(() => {
            clearTimeout(this.timer);
            wx.navigateTo({
              url: '/packages/tangshi/start/index'
            });
          });
        }
        return wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  }
};
