import Dialog from '@vant/weapp/dist/dialog/dialog';
import { ERROR_CODE } from '../config';

const app = getApp();
export default {
  // 预下单
  prepare() {
    app.request({
      method: 'POST',
      path: '/pay/wsctrade_tangshi/prepare.json',
      origin: 'cashier',
      data: {
        bookKey: app.globalData.tangshi.bookKey
      }
    })
      .then((res) => {
        let goodsNum = 0;
        res.tradeConfirmation.orderItems.forEach(e => {
          goodsNum += e.num;
        });
        this.setYZData({
          tableNum: app.globalData.tangshi.tableNum,
          orderPayment: res.tradeConfirmation.orderPayment,
          coupons: res.tradeConfirmation.coupons,
          choosedGoodsNum: goodsNum,
          activityName: res.tradeConfirmation.activityName,
          foodOrderCreation: res.foodOrderCreation
        });
        let choosedCouponsValue = [];
        if (JSON.stringify(this.data.chosenCoupon) != '{}') {
          choosedCouponsValue = [this.data.chosenCoupon];
        } else if (this.data.coupons == []) {
          choosedCouponsValue = [{
            denominations: 0
          }];
        } else {
          choosedCouponsValue = this.sortedCouponList();
        }

        this.setYZData({
          chosenCoupon: choosedCouponsValue[0]
        });
      })
      .catch(err => {
        wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  // 获取支付超时时间
  preparePayment() {
    app.request({
      method: 'POST',
      path: '/pay/wsctrade_tangshi/preparePayment.json',
      origin: 'cashier',
      data: {
        orderNo: app.globalData.tangshi.orderNo
      }
    })
      .then((res) => {
        const {
          expire, prePaymentPreparation, paymentPreparation, tradeConfirmation, zeroOrder
        } = res;
        let goodsNum = 0;
        tradeConfirmation.orderItems.forEach(e => {
          goodsNum += e.num;
        });
        const newPayResult = prePaymentPreparation.prepay ? prePaymentPreparation : paymentPreparation;
        this.setYZData({
          tableNum: app.globalData.tangshi.tableNum,
          orderPayment: tradeConfirmation.orderPayment,
          coupons: tradeConfirmation.coupons || [],
          choosedGoodsNum: goodsNum,
          activityName: tradeConfirmation.activityNames || [],
          // payInfo: {
          //   prePaymentPreparation,
          //   paymentPreparation
          // },
          payParams: newPayResult,
          zeroOrder: zeroOrder || (!prePaymentPreparation.prepay && paymentPreparation.payAmount === 0),
          prepay: prePaymentPreparation.prepay,
          prepaySuccess: prePaymentPreparation.prepaySuccess,
          expire,
          endTime: Date.now() + expire
        });
        if (expire) {
          setInterval(this.calcRemainTime, 200);
        }
      })
      .catch(err => {
        wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  // 下单
  create() {
    const { mode, alias, orderVersion } = app.globalData.tangshi;
    const params = {
      ...this.data.foodOrderCreation,
      config: {
        paymentSuccessRedirect: ''
      },
      ump: {
        coupon: {
          id: this.data.chosenCoupon.id || 0,
          couponType: this.data.chosenCoupon.type || ''
        },
        activities: [
          {
            activityId: 0
          }
        ]
      },
      foodConfig: {
        mode,
        orderVersion,
        tableAlias: alias
      }
    };
    return new Promise((resolve) => {
      app.request({
        method: 'POST',
        path: '/pay/wsctrade_tangshi/create.json',
        origin: 'cashier',
        data: {
          createParams: params
        }
      })
        .then((res) => {
          this.setYZData({
            payInfo: res
          });
          app.globalData.tangshi.orderNo = res.orderNo;
          const {
            prePaymentPreparation,
            paymentPreparation,
            zeroOrder
          } = res;
          const newPayResult = prePaymentPreparation.prepay ? prePaymentPreparation : paymentPreparation;
          // 记录订单号和支付map
          this.setYZData({
            orderNo: res.orderNo || this.data.orderNo,
            payParams: newPayResult,
            zeroOrder,
            prepay: prePaymentPreparation.prepay || true,
            prepaySuccess: prePaymentPreparation.prepaySuccess || true
          }, () => {
            resolve(res);
          });
          // this.doPay();
        })
        .catch(err => {
          if (err.code === ERROR_CODE.CART_CHANGED
            || err.code === ERROR_CODE.ORDERED
            || err.code === ERROR_CODE.PAIED
            || err.code === ERROR_CODE.CANCELED) {
            const message = err.code === ERROR_CODE.PAIED ? JSON.parse(err.msg).errorMsg : err.msg;
            const confirmButtonText = err.code === ERROR_CODE.PAIED ? '去看看' : '知道啦';
            return Dialog.confirm({
              title: '',
              message,
              confirmButtonText,
              showCancelButton: false
            }).then(() => {
              if (err.code === ERROR_CODE.PAIED) {
                clearTimeout(this.timer);
                wx.navigateTo({
                  url: '/packages/tangshi/order-detail/index?extra=' + JSON.parse(err.msg).extra
                });
              } else if (err.code === ERROR_CODE.CART_CHANGED && app.globalData.tangshi.mode === 1) {
                clearTimeout(this.timer);
                wx.navigateTo({
                  url: '/packages/tangshi/start/index?extra=true'
                });
              } else {
                clearTimeout(this.timer);
                wx.navigateTo({
                  url: '/packages/tangshi/start/index?extra='
                });
              }
            });
          }
          return wx.showToast({
            title: err.msg || '请求错误',
            icon: 'none'
          });
        });
    });
  },

  // 按金额排序的优惠券列表
  sortedCouponList() {
    return this.data.coupons.slice(0).sort((prev, next) => next.value - prev.value);
  }
};
