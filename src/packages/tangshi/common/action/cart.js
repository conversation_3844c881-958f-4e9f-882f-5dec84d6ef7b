import Dialog from '@vant/weapp/dist/dialog/dialog';
import { ERROR_CODE } from '../config';

const app = getApp();
const p = 'packages';
export default {
  // 获取购物车内商品数据
  getCart() {
    const { alias, mode = 0, orderVersion = 0 } = app.globalData.tangshi || {};
    return new Promise((resolve) => {
      app.request({
        path: '/wscshop/showcase/tangshi/getCart.json',
        query: {
          alias,
          mode,
          orderVersion
        }
      })
        .then((res) => {
          this.setYZData({
            tableNum: res.name,
            showEmpty: !(res.itemList.length > 0) || !(res.itemList[0].cartItems.length > 0),
            goodsList: res.itemList
          });
          let cartGoods = {};
          let totalPrice = 0;
          res.itemList.forEach(list => {
            list.cartItems.forEach(goods => {
              cartGoods[goods.skuId + '' + list.buyerInfo.buyerId] = {
                num: goods.num,
                price: goods.price
              };
              totalPrice += goods.num * goods.price;
            });
          });
          this.setYZData({
            cartGoods,
            totalPrice
          });
          app.globalData.tangshi.tableNum = res.name;
          resolve(res);
        })
        .catch(err => {
          if (err.code === ERROR_CODE.PAIED) {
            return Dialog.confirm({
              title: '',
              message: JSON.parse(err.msg).errorMsg,
              confirmButtonText: '去看看',
              showCancelButton: false
            }).then(() => {
              clearTimeout(this.timer);
              wx.navigateTo({
                url: `/${p}/tangshi/order-detail/index?extra=` + JSON.parse(err.msg).extra
              });
            });
          } if (err.code === ERROR_CODE.CANCELED) {
            return Dialog.confirm({
              title: '',
              message: JSON.parse(err.msg).errorMsg,
              confirmButtonText: '我知道了',
              showCancelButton: false
            }).then(() => {
              clearTimeout(this.timer);
              wx.navigateTo({
                url: `/${p}/tangshi/start/index`
              });
            });
          }
          return wx.showToast({
            title: err.msg || '请求错误',
            icon: 'none'
          });
        });
    });
  },

  // 下单至后厨
  submit() {
    const {
      alias, mode, storeId, orderVersion, headCount = 1
    } = app.globalData.tangshi;
    const params = {
      alias,
      mode,
      storeId,
      payAmount: this.data.totalPrice,
      memos: app.globalData.tangshi.memos || this.data.memos,
      headCount,
      itemList: this.data.goodsList,
      orderVersion
    };
    app.request({
      method: 'POST',
      path: '/wscshop/showcase/tangshi/submit.json',
      data: params
    })
      .then(() => {
        this.cacheAction();
      })
      .catch(err => {
        // 如果已经有人下单 出弹窗提醒并重新定位页面
        if (err.code === ERROR_CODE.SUBMITTED || err.code === ERROR_CODE.ORDERED || err.code === ERROR_CODE.PAIED || err.code === ERROR_CODE.CANCELED) {
          const message = err.code === ERROR_CODE.PAIED ? JSON.parse(err.msg).errorMsg : err.msg;
          const confirmButtonText = err.code === ERROR_CODE.PAIED ? '去看看' : '知道啦';
          return Dialog.confirm({
            title: '',
            message,
            confirmButtonText,
            showCancelButton: false
          }).then(() => {
            if (err.code === ERROR_CODE.PAIED) {
              clearTimeout(this.timer);
              wx.navigateTo({
                url: `/${p}/tangshi/order-detail/index?extra=` + JSON.parse(err.msg).extra
              });
            } else {
              clearTimeout(this.timer);
              wx.navigateTo({
                url: `/${p}/tangshi/start/index`
              });
            }
          });
        }
        return wx.showToast({
          title: err.msg || '下单至厨房失败',
          icon: 'none'
        });
      });
  },

  // 缓存需要下单的信息
  cacheAction() {
    const {
      alias, mode, orderVersion, headCount = 1
    } = app.globalData.tangshi;
    let items = [];
    this.data.goodsList.forEach(i => {
      items = items.concat(i.cartItems);
    });
    // 整合商品 不同的人点了相同的商品需要合并
    const itemMap = {};
    const orderItems = [];
    items.forEach(item => {
      if (!itemMap[`${item.alias}-${item.skuId}`]) {
        itemMap[`${item.alias}-${item.skuId}`] = item;
        orderItems.push(item);
      } else {
        itemMap[`${item.alias}-${item.skuId}`].num = itemMap[`${item.alias}-${item.skuId}`].num + item.num;
      }
    });
    app.request({
      method: 'POST',
      path: '/wscshop/trade/tangshi/cache.json',
      data: {
        alias,
        mode,
        orderVersion,
        headCount,
        memos: app.globalData.tangshi.memos || this.data.memos,
        payAmount: this.data.totalPrice,
        items: orderItems
      }
    })
      .then((res) => {
        app.globalData.tangshi.bookKey = res.bookKey;
        clearTimeout(this.timer);
        wx.navigateTo({
          url: `/${p}/tangshi/pay/index`
        });
      })
      .catch(err => {
        // 如果已经有人下单 出弹窗提醒并重新定位页面
        if (err.code === ERROR_CODE.SUBMITTED || err.code === ERROR_CODE.ORDERED || err.code === ERROR_CODE.PAIED || err.code === ERROR_CODE.CANCELED) {
          const message = err.code === ERROR_CODE.PAIED ? JSON.parse(err.msg).errorMsg : err.msg;
          const confirmButtonText = err.code === ERROR_CODE.PAIED ? '去看看' : '知道啦';
          return Dialog.confirm({
            title: '',
            message,
            confirmButtonText,
            showCancelButton: false
          }).then(() => {
            if (err.code === ERROR_CODE.PAIED) {
              clearTimeout(this.timer);
              wx.navigateTo({
                url: `/${p}/tangshi/order-detail/index?extra=` + JSON.parse(err.msg).extra
              });
            } else {
              clearTimeout(this.timer);
              wx.navigateTo({
                url: `/${p}/tangshi/start/index`
              });
            }
          });
        }
        return wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  // 清空购物车
  confirmClearCart() {
    const { alias, mode, orderVersion } = app.globalData.tangshi;
    app.request({
      method: 'POST',
      path: '/wscshop/showcase/tangshi/clearCart.json',
      data: {
        alias,
        mode,
        orderVersion
      }
    })
      .then(() => {
        clearTimeout(this.timer);
        wx.navigateTo({
          url: `/${p}/tangshi/order/index`
        });
      })
      .catch(err => {
        wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  // 通知后端变动的商品
  notifyChangeChoosed(choosedData) {
    app.request({
      method: 'POST',
      path: '/wscshop/showcase/tangshi/operateCart.json',
      data: choosedData
    })
      .then(() => {
        this.setYZData({
          totalPrice: choosedData.num === 1 ? (this.data.totalPrice + choosedData.price) : (this.data.totalPrice - choosedData.price)
        });
      })
      .catch(err => {
        if (err.code === ERROR_CODE.SUBMITTED || err.code === ERROR_CODE.ORDERED || err.code === ERROR_CODE.CANCELED) {
          const message = err.code === ERROR_CODE.PAIED ? JSON.parse(err.msg).errorMsg : err.msg;
          const confirmButtonText = err.code === ERROR_CODE.PAIED ? '去看看' : '知道啦';
          return Dialog.confirm({
            title: '',
            message,
            confirmButtonText,
            showCancelButton: false
          }).then(() => {
            clearTimeout(this.timer);
            wx.navigateTo({
              url: `/${p}/tangshi/start/index`
            });
          });
        }
        return wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  }

};
