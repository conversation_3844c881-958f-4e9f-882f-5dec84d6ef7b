function cent2yuan(value) {
  return Number(value / 100).toFixed(2);
}

function skuStr(sku) {
  let newSkuName = '';
  if (sku) {
    JSON.parse(sku).forEach(e => {
      newSkuName += e.v + ' ';
    });
  }
  return newSkuName;
}

function counterText(expireTime) {
  let day = 0;
  let hour = 0;
  let min = 0;
  let sec = 0;
  let remain = Math.floor(expireTime / 1000);

  day = Math.floor(remain / 86400);
  remain %= 86400;
  hour = Math.floor(remain / 3600);
  remain %= 3600;
  min = Math.floor(remain / 60);
  sec = remain % 60;

  const texts = [
    pad<PERSON>ero(day) + '天',
    padZero(hour) + '时',
    padZero(min) + '分',
    padZero(sec) + '秒'
  ];

  if (!day) {
    texts.shift();
    if (!hour) {
      texts.shift();
      if (!min) {
        texts.shift();
      }
    }
  }

  return `${texts.join(' ')}`;
}

function padZero(num) {
  return (num < 10 ? '0' : '') + num;
}

module.exports = {
  skuStr,
  cent2yuan,
  counterText
};
