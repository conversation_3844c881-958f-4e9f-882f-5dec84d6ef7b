<wxs src="../common/tools.wxs" module="tools" />
<page-container
  class="{{ themeClass }} page-{{ deviceType }}"
>
  <view class="order-detail">
    <view class="order-status">
      <image class="status-icon" src="https://img.yzcdn.cn/image/over.png"></image>
      <view>
        <view class="status-text">已付款，订单已完成</view>
      </view>
    </view>
    <view class="order-goods-panel">
      <van-cell-group class="order-goods-panel__group">
        <van-cell class="shop">
          <view class="shop" slot="title">
            <image class="img" src="{{orderInfo.shop.shopUrl}}"></image>
            <view>
              {{orderInfo.shop.shopName}}
            </view>
          </view>
        </van-cell>
        <view class="goods-group van-cell">
          <view
            wx:for="{{orderInfo.items}}"
            wx:key="{{item.itemId}}"
            class="goods-wrap"
          >
            <view class="goods">
              <view class="goods-detail">
                <view class="goods-detail__box">
                  <view class="goods-detail__title">{{ item.title }}</view>
                  <view class="goods-detail__sku">{{ tools.skuStr(item.sku) }}</view>
                </view>
                <view wx:if="{{item.num > 0}}" class="goods-detail__num">x{{ item.num }}</view>
                <view class="goods-detail__price">
                  <text class="goods-detail__price--pay">¥<text class="span">{{ tools.cent2yuan(item.price) }}</text></text>
                </view>
              </view>
            </view>

          </view>
        </view>
        <van-cell wx:if="{{activities}}" class="shop-ump" title="店铺活动" value="-￥ {{tools.cent2yuan(activities)}}">
          <van-icon slot="right-icon" name="question-o" bindtap="showActPopup" />
        </van-cell>
        <van-cell wx:if="{{coupons}}" class="coupon" title="优惠券" value="-¥ {{tools.cent2yuan(coupons)}}" />
        <view class="total">实付：<span>¥{{ tools.cent2yuan(orderInfo.realPay) }}</span></view>
      </van-cell-group>
    </view>
    <view class="order-panel">
      <van-cell-group>
        <van-cell title="订单信息" class="order-panel__title" />
        <van-cell
          wx:for="{{orderDetail}}"
          wx:key="{{item.title}}"
          title="{{item.title}}"
          value="{{item.value}}"
        />
      </van-cell-group>
    </view>
  </view>
  <van-popup
    show="{{ showDialog }}"
    position="bottom"
    bind:close="showActPopup"
  >
    <view class="banner-popup__item">
      <view class="banner-popup__item-hd">店铺活动名称</view>
      <view wx:for="{{orderInfo.activities}}" class="banner-popup__item-bd">{{ item.title }}</view>
    </view>
    <van-button size="large" type="danger" bind:click="showActPopup">我知道了</van-button>
  </van-popup>
</page-container>
<import src="/pages/common/wsc-page/index.wxml" />
<template is="wsc-page" data="{{ wscpage }}" />
