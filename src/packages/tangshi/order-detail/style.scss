.order-detail {
  .order-status,
  .order-goods-panel,
  .order-panel {
    margin: 10px 5px;
    border-radius: 2px;

    .van-cell__value {
      overflow: visible;
    }
  }
}
.order-status {
  padding: 15px;
  background: #fff;
  box-shadow: rgba(0, 0, 0, .1) 0 1px 2px 0;
  display: flex;
  align-items: center;

  .status-icon {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }

  .status-text {
    color: #333;
    font-size: 14px;
    font-weight: bold;
  }

  .p {
    color: #f44;
    font-size: 10px;

    text {
      color: #f44;
      font-size: 10px;
      font-weight: normal;
    }
  }
}
.order-panel {
  box-shadow: rgba(0, 0, 0, .1) 0 1px 2px 0;

  &__title {
    font-size: 12px;
    font-weight: bold;
  }

  .van-cell-group.van-hairline--top-bottom {
    &::after {
      border: 0;
    }
  }
}
.goods {
  width: 100%;
  background-color: #fff;
  position: relative;
  display: flex;

  &-img__wrap {
    display: inline-block;
    width: 60px;
    height: 60px;
    overflow: hidden;
    margin-right: 10px;
    background: #fafafa;
    position: relative;
  }

  &-img {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate3d(-50%, -50%, 0);
    max-width: 100%;
    max-height: 100%;
    border-radius: 2px;
  }

  &-detail {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 97%;

    &__box {
      min-width: 79%;
    }

    &__title {
      color: #333;
      font-size: 14px;
      line-height: 18px;
      display: -webkit-box;
      width: 90%;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      margin: 0;
    }

    &__sku {
      margin-top: 5px;
      color: #666;
      font-size: 12px;
    }

    &__desc {
      margin-bottom: 5px;
      color: #666;
      font-size: 12px;
    }

    &__num {
      color: #333;
      font-size: 14px;
      margin-right: 20px;
    }

    &__price {
      margin: 0;

      &--pay {
        font-weight: 500;
        margin-right: 5px;
        font-size: 14px;
        color: #333;

        .span {
          font-size: 14px;
        }
      }

      &--origin {
        color: #666;
        font-size: 10px;
        text-decoration: line-through;
      }
    }
  }
}

.order-goods-panel {
  box-shadow: rgba(0, 0, 0, .1) 0 1px 2px 0;
  background-color: #fff;

  &__group {
    .shop {
      display: flex;
      align-items: center;

      .img {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 5px;
      }
    }

    .goods-group {
      padding: 0 0 1px 15px;
      overflow-x: hidden;
      display: block;

      .goods-wrap {
        width: calc(100% - 7px);
        margin: 20px 0;
      }
    }

    .van-hairline--bottom::after {
      left: 15px;
    }

    .shop-ump {
      &.van-hairline::after {
        width: 0;
      }

      .van-icon-question {
        color: #38f;
        z-index: 10;
        margin: 5px 0 0 5px;
      }
    }

    .coupon {
      background: url("https://img.yzcdn.cn/image/ump.png") no-repeat 15px center;
      background-size: 16px 16px;

      .van-cell__title {
        text-indent: 20px;
      }

      .van-cell__value {
        color: #f44;
      }
    }

    .total {
      text-align: right;
      font-size: 12px;
      color: #666;
      height: 44px;
      line-height: 44px;
      padding-right: 15px;

      span {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}
.banner-popup__container {
  width: 100%;
  text-align: left;
  z-index: 10000 !important;
}

.banner-popup__item {
  margin: 20px 15px;
}

.banner-popup__item-hd {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}

.banner-popup__item-bd {
  font-size: 12px;
  color: #999;
  line-height: 18px;
}