import WscPage from 'pages/common/wsc-page/index';
import { moment as formatDate } from '@youzan/weapp-utils/lib/time';

const app = getApp();
WscPage({
  data: {
    orderInfo: {},
    showDialog: false
  },

  onLoad(query) {
    const { mode = 0 } = app.globalData.tangshi || {};
    // 区分先付后吃与先吃后付的文案
    this.setYZData({
      text: mode === 1 ? '确认菜品后请下单至厨房备菜' : '请先付款，订单才能下单至厨房',
      btnText: mode === 1 ? '下单到后厨' : '去支付'
    });
    query.extra && (app.globalData.tangshi.orderNo = query.extra);
    this.getOrderInfo();
  },

  // 获取订单信息
  getOrderInfo() {
    const { alias, orderNo } = app.globalData.tangshi;
    const param = {
      alias,
      orderNo
    };

    app.request({
      path: '/pay/wsctrade_tangshi/orderDetail.json',
      origin: 'cashier',
      query: param
    })
      .then((res) => {
        this.setYZData({
          orderInfo: res
        });
        const orderDetail = [
          {
            title: '桌号',
            value: res.tableName
          },
          {
            title: '订单号',
            value: res.orderNo
          },
          {
            title: '口味偏好',
            value: app.globalData.tangshi.orderInfoFlavorValue || ''
          },
          {
            title: '用餐人数',
            value: res.headCount
          },
          {
            title: '支付方式',
            value: res.buyWayDesc
          },
          {
            title: '下单人',
            value: res.buyerName
          },
          {
            title: '下单时间',
            value: formatDate(res.payTime, 'YYYY-MM-DD HH:mm:ss')
          }
        ];
        this.setYZData({
          orderDetail,
          coupons: res.coupons && res.coupons[0] ? res.coupons[0].value : 0,
          activities: res.activities && res.activities[0] ? res.activities[0].value : 0
        });
      })
      .catch(err => {
        wx.showToast({
          title: err.msg || '请求错误',
          icon: 'none'
        });
      });
  },

  showActPopup() {
    this.setYZData({
      showDialog: !this.data.showDialog
    });
  }
});
