/**
 * 处理有赞云网关token
 */
import { reportSkynet } from 'utils/log/logv3';
import cloudApp from '@/ecloud-space/cloudApp';
import setter from '../defaulter';

// 应用名数组
function getAppNameArray() {
  return Object.keys(cloudApp);
}

// 有赞云网关token接口
function getGatewayUrl() {
  return CLOUD_ENV === 'dev'
    ? 'https://oauth.isv-dev.youzan.com/oauth/getToken'
    : 'https://oauth.isv.youzan.com/oauth/getToken';
}

// 重试获取token
function retryGetYouzanyunGatewayToken(app, params = {}) {
  const { appNames = [] } = params;
  let retryCount = 0;

  return new Promise((resolve, reject) => {
    const failed = (error) => {
      if (retryCount < 5) {
        retryCount++;
        getToken();
      } else {
        reject(error);
      }
    };

    const getToken = () => {
      const { sessionId } = app.userTokenInfo;
      wx.request({
        url: getGatewayUrl(),
        header: {
          'content-type': 'application/json',
        },
        method: 'POST',
        data: {
          env: CLOUD_ENV || 'prod',
          appNames,
          approveScene: 2, // 代表小程序
          approveAgentId: sessionId,
        },
        success: (res) => {
          if (+res.statusCode === 200) {
            if (res.data.code === 200) {
              resolve(res.data.data);
            } else {
              failed({ message: res.data.msg });
            }
          } else {
            failed(res);
          }
        },
        fail: failed,
      });
    };

    getToken();
  });
}

// 获取token并处理数据
function getYouzanyunGatewayToken(app, params = {}) {
  // appNames中要么一个，要么所有应用名
  const { appNames = [] } = params;
  const isGetSingleToken = appNames.length === 1;

  retryGetYouzanyunGatewayToken(app, params)
    .then((tokenMap = {}) => {
      // 删除之前的token信息
      let youzanyunGatewayToken = app.youzanyunGatewayToken.filter(
        (item) => !appNames.includes(item.appName)
      );

      // 重新写入token信息
      if (isGetSingleToken) {
        youzanyunGatewayToken.push({
          appName: appNames[0],
          token: tokenMap[appNames[0]],
          status: 'using',
        });
      } else {
        // 触发登录事件后重新刷新所有应用token
        youzanyunGatewayToken = Object.keys(tokenMap).map((appName) => ({
          appName,
          token: tokenMap[appName],
          status: 'using',
        }));
      }
      app.youzanyunGatewayToken = youzanyunGatewayToken;

      // 发送队列中的请求
      app.trigger('ecloud:request:queue:send', params);

      reportSkynet('获取有赞云网关token成功', {
        appNames,
        userTokenInfo: app.userTokenInfo,
        youzanyunGatewayToken: app.youzanyunGatewayToken,
      });
    })
    .catch((error) => {
      // 失败时将对应应用的状态清空
      app.youzanyunGatewayToken = app.youzanyunGatewayToken.map((item) => {
        if (appNames.includes(item.appName)) {
          return {
            ...item,
            token: '',
            status: '',
          };
        }
        return item;
      });

      // 重试后仍没有拿到token时，降级处理
      app.trigger('ecloud:request:queue:send', params);

      reportSkynet('获取有赞云网关token失败', {
        appNames,
        userTokenInfo: app.userTokenInfo,
        youzanyunGatewayToken: app.youzanyunGatewayToken,
        error,
      });
    });
}

export default function (app) {
  setter(app, 'youzanyunGatewayToken', []);
  setter(app, 'userTokenInfo', {});
  setter(app, 'isAlreadyTriggerLogin', false);

  // 刷新单个应用的token
  app.on('ecloud:gateway:get-token', (params = {}) => {
    const { appNames = [] } = params;
    const appName = appNames[0];
    const { sessionId } = app.userTokenInfo;
    const currentTokenInfo = app.youzanyunGatewayToken.find(
      (i) => i.appName === appName
    );
    const { status } = currentTokenInfo || {};

    // 如果已经触发过登录，但是没有sessionId，则降级处理
    if (app.isAlreadyTriggerLogin && !sessionId) {
      app.trigger('ecloud:request:queue:send', params);

      reportSkynet('已经触发过登录,但是没有sessionId', {
        appNames,
        userTokenInfo: app.userTokenInfo,
        youzanyunGatewayToken: app.youzanyunGatewayToken,
      });

      return;
    }

    // 如果请求在登录事件之前发送，会没有sessionId
    if (!sessionId || status === 'getting') return;

    if (currentTokenInfo) {
      currentTokenInfo.status = 'getting';
    } else {
      app.youzanyunGatewayToken.push({
        appName,
        status: 'getting',
      });
    }

    getYouzanyunGatewayToken(app, params);
  });

  // 登录成功后刷新所有应用的token
  app.on('app:token:success', (data) => {
    const { sessionId: newSessionId } = data;
    const { sessionId: oldSessionId } = app.userTokenInfo;
    const appNames = getAppNameArray();
    app.isAlreadyTriggerLogin = true;

    // 登录异常没返回sessionId时，降级处理
    if (!newSessionId) {
      app.trigger('ecloud:request:queue:send', { appNames });

      reportSkynet('app:token:success事件未返回sessionId', {
        appNames,
        data,
        youzanyunGatewayToken: app.youzanyunGatewayToken,
      });

      return;
    }

    if (newSessionId === oldSessionId) return;

    app.userTokenInfo = data;
    getYouzanyunGatewayToken(app, { appNames });
  });
}
