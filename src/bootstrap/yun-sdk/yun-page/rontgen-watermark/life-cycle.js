// import debounce from '@youzan/weapp-utils/lib/debounce';
// import RontgenWeapp from '@youzan/rontgen-weapp/dist';

export const rontgenWatermarkLife = {
  onLoad() {
    // const inst = RontgenWeapp.getInstance();
    // inst.bindComponent(this);
    // inst.onResponseFail(() => {
    //   const opt = RontgenWeapp.getOptions();
    //   if (opt.disableWatermark === false && !this.data.showRontgenWatermark) {
    //     this.setData({ showRontgenWatermark: true }, () => {
    //       inst.reDraw();
    //       if (!this.debounceCallback) {
    //         this.debounceCallback = debounce(function () {
    //           this.setData({ showRontgenWatermark: false });
    //         }, RontgenWeapp.getOptions()?.watermark?.autoRemove ?? 5000);
    //       }
    //       this.debounceCallback?.();
    //     });
    //   }
    // });
  },
  onHide() {
    // RontgenWeapp.flush();
  },
};
