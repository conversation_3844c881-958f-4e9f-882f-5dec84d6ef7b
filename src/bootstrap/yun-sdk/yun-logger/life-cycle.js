import { setLogBizInfo, getLogBizInfoFromQuery } from 'utils/log/logv3';
import { yunEnterpage, yunLeavePage, yunLogger } from './logger';
import { getYunUserInfo, beforeLog } from './utils';

export const lifeCycle = {
  onLoad(query) {
    getLogBizInfoFromQuery(query);
    setLogBizInfo(yunLogger);

    beforeLog(yunLogger);

    getYunUserInfo().then((user) => {
      yunLogger.setEvent(
        {
          params: {
            yz_open_id: user.userOpenId,
          },
        },
        'page'
      );

      yunLogger.setUser({
        yz_open_id: user.userOpenId,
      });
    });

    yunEnterpage();
  },
  onShow() {
    if (!this.__pageShown) {
      this.__pageShown = true;
    } else {
      yunEnterpage();
    }
  },
  onHide() {
    yunLeavePage();
  },
  onUnload() {
    yunLeavePage();
  },
};
