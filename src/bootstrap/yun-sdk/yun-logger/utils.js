import args from '@youzan/weapp-utils/lib/args';
import { getExtConfigSync } from '@youzan/tee-api';
import cloudApp from '@/ecloud-space/cloudApp';

export const getCurrentPage = () => {
  const pages = getCurrentPages() || [];
  const currentPage = pages[pages.length - 1] || {};
  return currentPage;
};

export const getDurl = () => {
  const page = getCurrentPage();
  const { route, options } = page;
  return args.add(route, options);
};

export const getRurl = () => {
  const pages = getCurrentPages() || [];
  const refererPage = pages[pages.length - 2];
  return refererPage ? args.add(refererPage.route, refererPage.options) : '';
};

export const getProjectName = () => {
  const keys = Object.keys(cloudApp);

  if (keys.length === 1) {
    return keys[0];
  }

  return getRurl().split('/')[1];
};

export const getYunSdk = () => {
  const app = getApp();
  return app.getYouZanYunSdk();
};

let userCache = null;
export const getYunUserInfo = () => {
  return new Promise((resolve) => {
    if (userCache) {
      resolve(userCache);
    } else {
      getYunSdk()
        .app.getUserInfo()
        .then(({ userInfo }) => {
          userCache = userInfo;
          resolve(userCache);
        });
    }
  });
};

export function beforeLog(yunLogger) {
  const { scene } = wx.getEnterOptionsSync();
  const { userVersion, appId } = getExtConfigSync();

  // 有赞云的环境env
  const projectName = getProjectName();
  const { env = '' } = getYunSdk().getEnv(projectName) || {};

  yunLogger.addSessionParams({
    weapp_version: userVersion,
  });

  yunLogger.addSessionParams(
    JSON.parse(
      JSON.stringify({
        appId,
        scene,
      })
    ),
    10 * 365 * 24 * 60
  );

  yunLogger.setPlat({
    app: projectName,
    yzy_sv: userVersion,
    env,
  });
}
