import Logger from '@youzan/weapp-log-sdk';
import { getRurl, getDurl } from './utils';

export const yunLogger = new Logger({
  plat: {
    yai: 'yzy_isv',
  },
  batch: false,
});

export const yunEnterpage = () => {
  yunLogger.spmHelper.setCurrentSpm(getDurl(), '', '');

  yunLogger.pageShow(
    {},
    {
      durl: getDurl(),
      rurl: getRurl(),
    }
  );
};

export const yunLeavePage = () => {
  yunLogger.log({
    et: 'custom',
    ei: 'leave_page',
    en: '离开页面',
    params: {},
  });
};
