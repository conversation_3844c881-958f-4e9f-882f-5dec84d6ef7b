/* #ifdef BUILD_ENV=youzanyun */
import get from '@youzan/weapp-utils/lib/get';
/* #endif */

const extLoggerParams = {};

export function setExtLoggerParams(config) {
  /* #ifdef BUILD_ENV=youzanyun */
  const componentTypeArray = [
    'goods',
    'image_ad',
    'social_fans',
    'cube_v3',
    'shop_ad_pop',
  ];

  Object.keys(config).forEach((k) => {
    if (componentTypeArray.indexOf(k) === -1) {
      console.warn(`isv 定制埋点组件暂时不支持${k}`);
    } else {
      extLoggerParams[k] = config[k];
    }
  });
  /* #endif */
}

export const getExtLoggerParams = (componentType) => {
  return extLoggerParams[componentType] || [];
};

export const getLoggerExtraParams = (ct, data) => {
  let extraParams = {};

  /* #ifdef BUILD_ENV=youzanyun */
  const pickItems = getExtLoggerParams(ct);
  const pickType = Object.prototype.toString.call(pickItems);

  const pickDeep = (object, paths) => {
    let index = -1;
    const { length } = paths;
    const result = {};

    while (++index < length) {
      const path = paths[index];
      const key = path.split('.').pop();
      const value = get(object, path);
      result[key] = value;
    }
    return result;
  };

  switch (pickType) {
    case '[object String]':
      extraParams = pickDeep(data, [pickItems]);
      break;
    case '[object Array]':
      extraParams = pickDeep(data, pickItems);
      break;
    case '[object Function]':
      extraParams = pickItems(data);
      break;
    default:
      extraParams = {};
      break;
  }
  /* #endif */

  return extraParams;
};
