import { yunLogger } from './logger';

export const COMMON_EVENTS = [
  {
    event: 'log',
    requiredArgs: ['event_type', 'event_name'],
    et: 'custom',
    ei: 'isv_click',
    en: '点击事件',
  },
];

export const STD_EVENTS = [
  {
    event: 'orderCreate',
    requiredArgs: ['order_id'],
    et: 'custom',
    ei: 'orderCreate',
    en: '创建订单',
  },
  {
    event: 'addCart',
    requiredArgs: ['goods_id', 'sku_id', 'sku_name', 'goods_name', 'add_num'],
    et: 'click',
    ei: 'add_cart',
    en: '添加购物车',
  },
  {
    event: 'bulkAddCart',
    requiredArgs: ['goods_id_list'],
    et: 'click',
    ei: 'bulk_add_cart',
    en: '批量加购',
  },
  {
    event: 'buy',
    requiredArgs: ['goods_id'],
    et: 'click',
    ei: 'buy',
    en: '点击购买',
  },
  {
    event: 'bulkBuy',
    requiredArgs: ['goods_id_list'],
    et: 'click',
    ei: 'bulk_buy',
    en: '批量购买',
  },
  {
    event: 'search',
    requiredArgs: ['words', 'words_type'],
    et: 'click',
    ei: 'search',
    en: '点击搜索',
  },
  {
    event: 'paySuccess',
    requiredArgs: ['order_id'],
    et: 'custom',
    ei: 'pay_success',
    en: '支付成功',
  },
];

// 根据ISV_EVENTS生成事件
export const makeLogger = (sdk, isvEvents) => {
  isvEvents.forEach(({ event, requiredArgs, ...eventArgs }) => {
    sdk[event] = function (params = {}) {
      if (checkIsvArgs(event, params, requiredArgs)) {
        yunLogger.log({
          ...eventArgs,
          params,
        });
      }
    };
  });
};

export const checkIsvArgs = (eventType, params, requiredKeys) => {
  for (let i = 0; i < requiredKeys.length; i++) {
    const argKey = requiredKeys[i];
    if (!params[argKey]) {
      console.warn(`isv 埋点方法${eventType}: 请传入${argKey}参数`);
    }
  }
  return true;
};
