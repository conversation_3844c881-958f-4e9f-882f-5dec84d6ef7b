import { setLogBizInfo } from '../../../utils/log/logv3';
import { COMMON_EVENTS, STD_EVENTS, makeLogger } from './events';
import { yunLogger } from './logger';
import { beforeLog } from './utils';

export const loggerWrapper = (sdk) => {
  makeLogger(sdk, COMMON_EVENTS);

  if (!sdk.stdEvent) {
    sdk.stdEvent = {};
  }

  makeLogger(sdk.stdEvent, STD_EVENTS);
};

// 一般不建议这么搞。。。
export const rewriteYunLogger = function (app, yunSdk) {
  const originLog = yunLogger.log;

  const eventList = [];
  let yunLogReady = false,
    reporting = false;

  const userTokenPromise = new Promise((resolve) => {
    app.on('app:token:success', (token) => {
      resolve(token);
    });
  });

  const getUserInfo = new Promise((resolve) => {
    yunSdk.app.on('ecloud:login:success', (user) => {
      resolve(user);
    });
  });

  Promise.all([userTokenPromise, getUserInfo])
    .then(([_, { userOpenId }]) => {
      yunLogReady = true;

      setLogBizInfo(yunLogger);
      yunLogger.setEvent(
        {
          params: {
            yz_open_id: userOpenId,
          },
        },
        'page'
      );

      yunLogger.setUser({
        yz_open_id: userOpenId,
      });

      startLog();
    })
    .catch((e) => {
      console.error(e);
    });

  const startLog = () => {
    if (reporting) {
      return;
    }

    if (yunLogReady) {
      reporting = true;
      let ev;
      while ((ev = eventList.shift())) {
        originLog.apply(yunLogger, ev);
      }
      reporting = false;
    }
  };

  const overridedLog = function (...args) {
    beforeLog(yunLogger);

    eventList.push(args);
    startLog();
  };

  yunLogger.log = overridedLog;
};
