/**
 * 处理大客协议逻辑
 */
import asyncEvent from 'shared/utils/async-event';
import tee from '@youzan/tee';
import {
  isUIComponent,
  getProcolListByCode,
  protocolBatchSign,
  PROTOCOL_SHOW_EVENT,
  PROTOCOL_CLOSE_EVENT,
  PROTOCOL_AGREE_EVENT,
  PROTOCOL_DISAGREE_EVENT,
} from 'shared/components/account/protocol/shared';

function onAsyncFn(app) {
  return (name, callback, context) => {
    context = context || {};

    return asyncEvent.onAsync.apply(app, [name, callback, context]);
  };
}

// 定义大客事件名称
const CUSTOM_EVENT = [
  [PROTOCOL_SHOW_EVENT, 'ecloud:authorize:custom-ui-protocol-show'],
  [PROTOCOL_CLOSE_EVENT, 'ecloud:authorize:custom-ui-protocol-close'],
  [PROTOCOL_AGREE_EVENT, 'ecloud:authorize:custom-ui-protocol-agree'],
  [PROTOCOL_DISAGREE_EVENT, 'ecloud:authorize:custom-ui-protocol-disagree'],
];

function getProtocol() {
  return import('@youzan/passport-protocol').then(
    ({ InvokeProtocol }) => new InvokeProtocol()
  );
}

export function getProtocolInstance() {
  const auth = ({ needSkipSigned } = {}) => {
    return getProtocol().then((protocol) =>
      protocol.auth({ bizType: 'vip-bespoke', needSkipSigned })
    );
  };

  const close = () => getProtocol().then((protocol) => protocol.close());

  return { auth, close };
}

export default function ({ app, sdk }) {
  // 获取协议信息
  sdk.app.getProtocolData = getProcolListByCode;

  // 协议签署
  sdk.app.signProtocol = protocolBatchSign;

  // 获取全局的协议组件实例
  sdk.app.getProtocolInstance = getProtocolInstance;

  CUSTOM_EVENT.forEach(([accountEvent, ecloudEvent]) => {
    onAsyncFn(app)(accountEvent, (args) => {
      const eventResult = sdk.app.trigger(ecloudEvent, args);

      // 如果是数组
      if (Array.isArray(eventResult)) {
        return Promise.all(eventResult).then((res) => res && res[0]);
      }

      return Promise.resolve(eventResult).then((res) => res && res[0]);
    });
  });

  // 放这里不是很合适，临时方案。
  tee.setGlobal('account:protocol:isUIComponent', isUIComponent());
}
