/**
 * 有赞云获取用户基本信息
 */
import getAuthorizedState from 'shared/utils/get-authorized-state';
import pick from '@youzan/weapp-utils/lib/pick';

export default function ({ app, sdk }) {
  // 工具方法：队列-出队
  const flush = (fnList, data) => {
    let fn = fnList.shift();
    while (fn) {
      typeof fn === 'function' && fn(data);
      fn = fnList.shift();
    }
  };

  // 工具方法： promise 请求节流 + 失败重试 + 内存缓存
  const createWaitFn = (
    promise,
    resolveHandler = (v) => v,
    rejectFnHandler = (v) => v,
    logIndex = ''
  ) => {
    let result = null;
    let isRequesting = false;
    const resolveFnList = [];
    const rejectFnList = [];
    const defaultOptions = { retryTimes: 3, enableCache: false };

    const handler = (params, options = {}) => {
      const { enableCache, retryTimes } = { ...defaultOptions, ...options };
      return new Promise((resolve, reject) => {
        if (enableCache && result !== null) return resolve(result);
        resolveFnList.push(resolve);
        rejectFnList.push(reject);
        if (isRequesting) return;
        isRequesting = true;

        const temp = promise(params);
        if (temp instanceof Promise) {
          temp
            .then((res) => {
              isRequesting = false;
              result = resolveHandler(res);
              return flush(resolveFnList, result);
            })
            .catch((err) => {
              isRequesting = false;

              if (retryTimes === 0) {
                // 日志上报
                app.logger.appError({
                  name: 'yun-sdk-getUserInfo',
                  level: 'info',
                  message: `${logIndex} retry_end_fail`,
                });
                return flush(rejectFnList, rejectFnHandler(err));
              }
              setTimeout(() => {
                handler(params, { retryTimes: retryTimes - 1 });
              }, 300);
            });
        } else {
          isRequesting = false;
          result = resolveHandler(temp);
          return flush(resolveFnList, result);
        }
      });
    };
    return handler;
  };

  // 获取有赞用户信息
  const createGetUserInfo = () => {
    let isRequesting = false;
    let timer;
    const resolveFnList = [];
    const rejectFnList = [];
    app.on('app:before:login', () => {
      isRequesting = true;
      clearTimeout(timer);
      timer = setTimeout(() => {
        isRequesting = false;
        // 日志上报
        app.logger.appError({
          name: 'yun-sdk-getUserInfo',
          level: 'info',
          message: 'youzan_user_info_timeout',
        });
        flush(rejectFnList, '获取有赞用户信息超时');
      }, 3000);
    });
    app.on('app:token:success', () => {
      isRequesting = false;
      flush(resolveFnList, app.getUserInfoSync());
    });
    return () => {
      return new Promise((resolve, reject) => {
        // 如果静默登录未进行且有 userId, 数据是最新的，可以使用
        const userInfo = app.getUserInfoSync();
        if (!isRequesting && userInfo.userId) return resolve(userInfo);
        resolveFnList.push(resolve);
        rejectFnList.push(reject);
      });
    };
  };

  // 获取客户身份创建状态
  const getClientStatus = createWaitFn(
    (userInfo) => {
      // 有手机号才创建客户身份
      if (userInfo.yzUserId && userInfo.mobile) {
        return app.request({
          config: {
            skipShopInfo: true,
          },
          method: 'POST',
          path: '/wscuser/weapp/create-client.json',
          data: {
            appName: 'wsc-weapp',
            yzUid: userInfo.yzUserId,
          },
        });
      }
      return {
        createClientStatus: 2,
        createClientMsg: 'create error, no mobile',
      };
    },
    (res) => {
      return {
        createClientStatus: res.status,
        createClientMsg: res.msg,
      };
    },
    () => {
      return {
        createClientStatus: 2,
        createClientMsg: 'create error',
      };
    },
    'getClientStatus'
  );

  // 获取有赞 userOpenId
  const getUserOpenId = createWaitFn(
    (userInfo) => {
      // 有手机号再把 userOpenId 给大客
      if (userInfo.yzUserId) {
        return app.request({
          path: '/wscuser/weapp/getUserOpenInfo.json',
          query: {
            buyerId: userInfo.yzUserId,
          },
        });
      }
      return 0;
    },
    (result) => {
      return result.userOpenId;
    },
    () => {
      return 0;
    },
    'getUserOpenId'
  );

  // 获取有赞用户信息
  const getUserInfo = createGetUserInfo();

  // 过滤用户信息
  const filterUserInfo = (userInfo, authState) => {
    // 暴露出去的数据字段
    const keys = ['gender', 'openId'];
    // 手机号已授权
    if (authState.mobile) {
      keys.push('mobile');
    }
    // 头像昵称已授权
    if (authState.nicknameAndAvatar) {
      keys.push('nickname', 'avatar');
    }
    return pick(userInfo, keys);
  };

  /**
   * 最终给云的获取用户信息方法
   * @returns {Promise<{userInfo: {[p: string]: *}, state: unknown}>}
   */
  const yunGetUserInfo = () => {
    return getUserInfo()
      .then((userInfo) => {
        return Promise.all([
          getAuthorizedState(),
          getUserOpenId(userInfo),
          getClientStatus(userInfo),
        ]).then(
          ([
            // 1、获取用户授权状态
            authState,
            // 2、获取 userOpenId
            userOpenId,
            // 3、获取客户身份
            clientStatus,
          ]) => {
            return {
              userInfo: {
                ...filterUserInfo(userInfo, authState),
                userOpenId,
              },
              state: {
                ...authState,
                ...clientStatus,
                userOpenId: !!userOpenId,
              },
            };
          }
        );
      })
      .catch((e) => {
        // 日志上报
        app.logger.appError({
          name: 'yun-sdk-getUserInfo',
          level: 'info',
          message: 'unexpected error',
          detail: {
            error: e.toString(),
          },
        });
        throw e;
      });
  };
  sdk.app.getUserInfo = yunGetUserInfo;
}
