import { init, SyncEvent, AsyncEvent } from '@youzan/weapp-ecloud-sdk';
// eslint-disable-next-line import/no-unresolved
import { chooseImage } from 'youzanyun-sdk/utils/choose-image';
import { setExtLoggerParams } from './yun-logger/cloud-logger';
import asyncEvent from 'shared/utils/async-event';

export default function (app, setter) {
  const yunSdk = init(app, { env: CLOUD_ENV || 'prod' });

  setter(
    app,
    'getYouZanYunSdk',
    () => {
      return yunSdk;
    },
    true
  );

  setter(app, 'getYunAsyncEvent', () => {
    return {
      trigger(eventName, ...args) {
        return asyncEvent.triggerAsync.apply(app, [eventName, ...args]);
      },
    };
  });

  // 注册登录成功事件
  yunSdk.app.__setEvent('ecloud:login:success', new SyncEvent());

  // 授权组件
  // 1、是否需要显示协议
  yunSdk.app.__setEvent(
    'ecloud:authorize:protocol-need-show',
    new AsyncEvent()
  );
  // 2、用户是否同意协议
  yunSdk.app.__setEvent(
    'ecloud:authorize:protocol-user-agree',
    new AsyncEvent()
  );
  // 3、sku选择器组件加入云校验函数
  yunSdk.app.__setEvent('ecloud:sku:pay:checked:goods', new AsyncEvent());

  // 注册地址选择事件
  yunSdk.app.__setEvent('ecloud:address:select', new SyncEvent());
  // 全局加购事件
  yunSdk.app.__setEvent('beforeAddCart', new SyncEvent());
  // 进店事件
  yunSdk.app.__setEvent('ecloud:chain-store:shop-select', new AsyncEvent());
  // kdtid变化事件，扫码、切店等触发
  yunSdk.app.__setEvent('ecloud:chain-store:kdtId:update', new SyncEvent());
  // 注册选择图片API
  yunSdk.chooseImage = chooseImage;
  // 注册云定制日志process
  yunSdk.app.__addProcess('extLogger', setExtLoggerParams);
}
