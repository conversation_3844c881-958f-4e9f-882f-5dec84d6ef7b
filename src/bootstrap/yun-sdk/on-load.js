import Event from '@youzan/weapp-utils/lib/event';
import pick from '@youzan/weapp-utils/lib/pick';
import EcloudSpaceBiz from '../../ecloud-space';
import handleChainShop from './handle-chain-shop';
import handleProcess from './handle-process';
import handleAuthorize from './handle-authorize';
import handleUserInfo from './handle-user-info';
import handleSkuConfirm from './handle-sku-validate';
import handleProtocol from './handle-protocol';
import { loggerWrapper, rewriteYunLogger } from './yun-logger';
import handleGatewayToken from './handle-gateway-token';

let nativeApp;
let sdk;
let queryingOpenId = false;
let userOpenId = 0;
let createUserClientResult = null; // 创建过客户资料返回结果

/**
 * 更新sdk.app.user用户信息
 */
function setAppUser(userInfo) {
  sdk.app.__setData(
    'user',
    pick(userInfo, [
      'gender',
      'avatar',
      'mobile',
      'nickName',
      'openId',
      'userOpenId',
    ])
  );
}

/**
 * 更新sdk.app.user用户信息
 */
function setAppShop(shop) {
  try {
    nativeApp.logger.skynetLog({
      appName: 'wsc',
      message: '开放1.0设置kdtId',
      detail: {
        ...shop,
      },
    });
  } catch (e) {
    console.error('开放1.0设置kdtId', e);
  }
  sdk.app.__setData('shop', {
    kdtId: shop.kdtId,
  });
}

/**
 * 创建客户身份
 */
function createWeappClient(param, retryTimes = 0) {
  return new Promise((resolve) => {
    nativeApp.request({
      config: {
        skipShopInfo: true,
      },
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
      },
      origin: 'h5',
      pathname: '/wscuser/weapp/create-client.json',
      path: '/wscuser/weapp/create-client.json',
      data: param,
      success: (res) => {
        resolve({
          createClientStatus: res.status,
          createClientMsg: res.msg,
        });
      },
      fail: () => {
        if (retryTimes <= 4) {
          createWeappClient(param, ++retryTimes).then((res) => resolve(res));
        } else {
          resolve({
            createClientStatus: 2,
            createClientMsg: 'create error',
          });
        }
      },
    });
  });
}

function triggerLoginSuccess(userInfo) {
  const outUserInfo = pick(userInfo, [
    'userOpenId',
    'openId',
    'nickName',
    'avatar',
    'gender',
    'mobile',
    'createClientStatus',
    'createClientMsg',
    'triggerRoute',
  ]);
  setAppUser(userInfo);
  sdk.app.trigger('ecloud:login:success', outUserInfo);
  nativeApp.logger.appError({
    name: 'triggerLoginSuccess',
    message: 'triggerLoginSuccess',
    detail: outUserInfo,
  });
}

/**
 * 换取userOpenId
 */
function getUserOpenId(userInfo) {
  return new Promise((resolve, reject) => {
    // 未请求过 && 有手机号
    if (!userOpenId && userInfo.mobile) {
      queryingOpenId = true;
      nativeApp
        .request({
          path: '/wscuser/weapp/getUserOpenInfo.json',
          query: {
            buyerId: userInfo.buyerId,
          },
        })
        .then((result) => {
          userOpenId = result.userOpenId;
          queryingOpenId = false;
          resolve(userOpenId);
        })
        .catch(() => {
          queryingOpenId = false;
          reject(0);
        });
    } else {
      resolve(userOpenId);
    }
  });
}

function pendingQueryOpenId(userInfo) {
  if (queryingOpenId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(pendingQueryOpenId(userInfo));
      }, 30);
    });
  }
  return getUserOpenId(userInfo);
}

export default function (app, options) {
  nativeApp = app;
  sdk = nativeApp.getYouZanYunSdk();
  if (!sdk) return;

  setAppUser(nativeApp.getUserInfoSync());
  setAppShop({ kdtId: nativeApp.getKdtId() });

  // 外部全局脚本
  try {
    EcloudSpaceBiz && EcloudSpaceBiz(sdk);
  } catch (e) {
    //
  }

  // 触发sdk onLoad
  sdk.app.__triggerLifetimes('onLoad', options);

  // 触发sdk onShow
  nativeApp.on('show', (options) => {
    sdk.app.__triggerLifetimes('onShow', options);
  });

  // 处理进店
  handleChainShop({ app, sdk });

  // 处理获取用户信息 API
  handleUserInfo({ app, sdk });

  // 授权组件定制
  handleAuthorize({ app, sdk });

  // 添加流程函数
  handleProcess({ app, sdk });

  // 添加sku选择器定制流程
  handleSkuConfirm({ app, sdk });

  // 处理协议定制
  handleProtocol({ app, sdk });

  // 监听全局登录事件
  nativeApp.on('app:token:success', (token) => {
    const currPage = getCurrentPages()[0] || {};
    nativeApp.logger.appError({
      name: 'onAppTokenSuccess',
      message: 'app:token:success',
      detail: token,
    });
    pendingQueryOpenId(token)
      .then(() => {
        const newToken = { ...token, userOpenId };

        // 创建客户之后触发登录操作
        if (!newToken.mobile) {
          triggerLoginSuccess({
            ...newToken,
            createClientStatus: 2,
            createClientMsg: 'create error, no mobile',
            triggerRoute: currPage.route,
          });
        } else if (
          createUserClientResult &&
          (createUserClientResult.createClientStatus === 0 ||
            createUserClientResult.createClientStatus === 1)
        ) {
          triggerLoginSuccess({
            ...newToken,
            ...createUserClientResult,
            triggerRoute: currPage.route,
          });
        } else {
          // 绑定手机号完成后，补偿创建客户，客户归属门店为进店之后的网店
          createWeappClient(
            {
              appName: 'wsc-weapp',
              yzUid: newToken.yzUserId,
            },
            3
          ).then((result) => {
            createUserClientResult = result;
            triggerLoginSuccess({
              ...newToken,
              ...result,
              triggerRoute: currPage.route,
            });
          });
        }
      })
      .catch(() => {
        // 换取openId等异常时，也正常触发登录
        triggerLoginSuccess({
          ...token,
          triggerRoute: currPage.route,
        });
      });
  });

  nativeApp.on('app:chainstore:kdtid:update', ({ kdtId }) => {
    sdk.app.trigger('ecloud:chain-store:kdtId:update', {
      kdtId,
    });
  });

  // 监听 kdtId 更新
  Event.on('shop:kdt_id:change', (kdtId) => {
    // 更新app.shop.kdtId
    setAppShop({ kdtId });
  });

  loggerWrapper(sdk, app);
  rewriteYunLogger(app, sdk);

  // 有赞云token
  handleGatewayToken(app);
}
