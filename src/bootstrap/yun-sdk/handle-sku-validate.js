import asyncEvent from 'shared/utils/async-event';

function onAsyncFn(app) {
  return (name, callback, context) => {
    context = context || {};

    return asyncEvent.onAsync.apply(app, [name, callback, context]);
  };
}

export default function ({ app, sdk }) {
  // 点击结算按钮事件
  onAsyncFn(app)('app:sku:pay:checked:goods', (args) => {
    return sdk.app
      .trigger('ecloud:sku:pay:checked:goods', args)
      .then((res) => res && res[0]);
    // const promise = sdk.app.trigger('ecloud:sku:pay:checked:goods', args);
    // if (promise) {
    //   if (Array.isArray(promise)) {
    //     return Promise.all(promise).then((res) => res && res[0]);
    //   }
    //   return promise;
    // }
    // return Promise.resolve();
  });
}
