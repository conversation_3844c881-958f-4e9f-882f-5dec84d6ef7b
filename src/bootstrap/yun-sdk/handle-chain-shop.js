import asyncEvent from 'shared/utils/async-event';

function onAsyncFn(app) {
  return (name, callback, context) => {
    context = context || {};

    return asyncEvent.onAsync.apply(app, [name, callback, context]);
  };
}

export default function({ app, sdk }) {
  // 监听进店事件
  onAsyncFn(app)('beforeEnterChainShop', (args) => {
    // 返回promise
    return sdk.app.trigger('ecloud:chain-store:shop-select', args).then((res) => {
      return res && res[0];
    });
  });

  /* asyncEvent.triggerAsync.apply(app, ['beforeEnterChainShop'].concat({ test: 'scc' })).then((res) => {
    console.log('resssss:', res);
    if (res && res[0] === false) {
      // 中断进店流程
      console.log('i"m happy, let you show...');
    } else {
      // 继续进店
      console.log('i will go store...');
    }
  }).catch(e => {
    console.error(e);
  }); */
}
