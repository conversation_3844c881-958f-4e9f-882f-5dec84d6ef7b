export default function ({ app, sdk }) {
  if (typeof sdk.app.__addProcess === 'function') {
    sdk.app.__addProcess('waitForEnterShop', () => app.waitForEnterShop());

    sdk.app.__addProcess('setOfflineId', (offlineId, cb, options) =>
      app.setOfflineId(offlineId, cb, { ...options, forbidJump: true })
    );

    sdk.app.__addProcess('saveOfflineId', (offlineId) =>
      app.saveOfflineId(offlineId)
    );

    sdk.app.__addProcess('getOfflineData', () => app.$store.state.shop.store);
  }
}
