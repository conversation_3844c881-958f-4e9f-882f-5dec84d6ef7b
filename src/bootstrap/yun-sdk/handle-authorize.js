import asyncEvent from 'shared/utils/async-event';

function onAsyncFn(app) {
  return (name, callback, context) => {
    context = context || {};

    return asyncEvent.onAsync.apply(app, [name, callback, context]);
  };
}

export default function ({ app, sdk }) {
  // 是否需要显示大客协议
  onAsyncFn(app)('app:account:authorize:custom-protocol-need-show', (args) => {
    return sdk.app
      .trigger('ecloud:authorize:protocol-need-show', args)
      .then((res) => res && res[0]);
  });

  // 用户同意大客协议
  onAsyncFn(app)('app:account:authorize:custom-protocol-agree', (args) => {
    return sdk.app.trigger('ecloud:authorize:protocol-user-agree', args);
  });

  // 用户同意了有赞协议、手机号或头像昵称授权
  onAsyncFn(app)('app:account:authorized', (args) => {
    return sdk.app.trigger('ecloud:authorize:user-agree', args);
  });

  if (typeof sdk.app.__addProcess === 'function') {
    // 提供更新本地授权缓存数据能力
    sdk.app.__addProcess('ecloud:authorize:sync-auth-state', () => {
      app.syncAuthState();
    });
  }
}
