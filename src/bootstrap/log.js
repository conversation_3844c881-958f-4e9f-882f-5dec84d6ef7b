import { getExtConfigSync } from '@youzan/tee-api';
import YZLogger, {
  getLogBizInfoFromQuery,
  setLogBizInfo,
  setLogAppInfo,
} from 'utils/log/logv3';
import spm from 'shared/utils/spm';
import Event from '@youzan/weapp-utils/lib/event';
import { getPagesReplaceRules } from '@/helpers/pages-path';
import { storage } from '../base-api/utils';
import ZanWeappLogger from './skynet-report';
import titleMap from 'utils/log/set-page-title';
import { isValidFromParams } from 'utils/guide';

const PARAM_LIST = [
  'is_share',
  'dc_ps',
  'banner_id',
  'from_source',
  'wxShoppingListScene',
];

function initLogger() {
  const { kdtId } = getExtConfigSync();

  /**
   * 此处是logger实例化过程
   * 可以配置全局参数
   */
  const platData = {
    yai: 'wsc_c',
  };

  const config = {
    plat: platData,
    /**
     * 关闭 autoUUID：当本地没有 UUID 时，不使用埋点自动生成 UUID，等待业务侧通过 setUuid 设置。
     * 微信小程序会把用户的 platformFansId 设置为 UUID.
     */
    autoUUID: false,
    appName: 'wsc',
    logIndex: 'wsc_weapp_log',
    traceable: true,
  };

  const rawLogger = new YZLogger(config);

  rawLogger.setEvent(
    {
      si: kdtId || '',
    },
    'instance'
  );

  rawLogger.setPageTitleMap(titleMap);

  return rawLogger;
}

function updateSession(appId, logContext) {
  const { userVersion } = getExtConfigSync();

  return function (options) {
    const query = options.query || {};
    const queryKeys = Object.keys(query);
    const keys = queryKeys.filter((key) => PARAM_LIST.indexOf(key) >= 0);
    if (keys.length) {
      this.addSessionParams(
        {
          ...logContext,
          ...keys.reduce((res, key) => {
            res[key] = query[key];
            if (key === 'dc_ps') {
              res.dc_ps_utime = parseInt(Date.now() / 1000, 10);
              storage.set('logv3:dc_ps', query.dc_ps, {
                // 有效时间 3 小时
                expire: 3 / 24,
              });
            }
            return res;
          }, {}),
        },
        2 * 60
      );
    }

    // from_params 有效期24h：禁止随意修改有效期 涉及导购和分销员业绩计算问题！！！
    const fromParamsStr = query.from_params;
    if (isValidFromParams(fromParamsStr)) {
      this.addSessionParams({ from_params: fromParamsStr }, 24 * 60);
    }

    // 如果有广告来源参数 clickId，就保存 30天
    if (options.clickId) {
      this.addSessionParams(
        {
          click_id: options.clickId,
        },
        30 * 24 * 60
      );
    }

    this.addSessionParams({
      weapp_version: userVersion,
    });

    // 有效期10年
    this.addSessionParams(
      JSON.parse(
        JSON.stringify({
          appId,
          scene: options.scene,
        })
      ),
      10 * 365 * 24 * 60
    );

    // 企微助手来源
    if (queryKeys.indexOf('wecom_uuid') >= 0) {
      this.addSessionParams({ wecom_uuid: options.query.wecom_uuid });
    }
  };
}

function warningLog(logger, name, data) {
  const en = {
    network_warning: '弱网告警',
    memory_warning: '内存告警',
  };
  try {
    const { appError, report } = logger;
    //  上报埋点
    report &&
      report.report &&
      report.report({
        et: 'custom', // 事件类型
        ei: name, // 事件标识
        en: en[name], // 事件名称
        params: {
          ...data,
        },
      });

    // 上报天网
    appError &&
      appError({
        name,
        detail: {
          ...data,
        },
      });
  } catch (e) {
    console.log('warningLog error', e);
  }
}

/**
 * @param {*} logger
 */
function watchMemoryAndNetwork(logger) {
  const { onNetworkWeakChange, onMemoryWarning } = wx;
  // 弱网监控日志
  onNetworkWeakChange &&
    onNetworkWeakChange((res) => {
      warningLog(logger, 'network_warning', res);
    });

  // 内存监控日志
  onMemoryWarning &&
    onMemoryWarning((res) => {
      warningLog(logger, 'memory_warning', res);
    });
}

function onPageNotFound(logger) {
  return async ({ path, query }) => {
    const PAGES_REPLACE_RULES = await getPagesReplaceRules();
    const targetRule = PAGES_REPLACE_RULES[path];

    if (!targetRule) {
      logger.appError({
        message: '请求到未被监控的路径',
        alert: 'warn',
        detail: {
          path,
          query,
        },
      });
      return wx.reLaunch({ url: '' });
    }

    const { target, handle, query: extraQuery } = targetRule;
    if (extraQuery) {
      Object.assign(query, extraQuery);
    }
    handle && handle(target, query);

    const app = getApp();

    logger.appError({
      message: '请求到已经替换的路径',
      detail: {
        path,
        query,
        isRetail: app.globalData && app.globalData.isRetailApp,
        spm: spm.getSpm(),
      },
    });
  };
}

/**
 * 添加通过链接跳转到小程序的埋点记录
 */
function addFromLinkRecord(logger) {
  return function ({ query }) {
    if (query && query.fromLink) {
      const { kdtId } = getExtConfigSync();
      logger.log({
        et: 'display', // 事件类型
        ei: 'enterpage', // 事件标识
        en: '浏览页面', // 事件名称
        pt: 'linkJumpWeapp',
        params: {
          spm: 'linkJumpWeapp',
          kdtId,
        },
      });
    }
  };
}

/**
 * @description 记录小程序启动性能
 * @param {Obejct} app
 * @param {Object} logger
 */
function performance() {
  // 上报指标
  const keyList = {
    app: [
      'applogin', // 登录成功
      'appinit', // init接口返回
      'pageshow', // 页面展示
    ],
    page: [
      'pageshow', // 异步请求回调时间，并没有包含模板层的渲染时间
      'speedindex', // 首屏展示时间
      'pagefinished', // 包含模板层渲染时间的首屏展示时间
    ],
  };

  function logFactory() {
    // performance 已无用，do nothing, 防止业务调用未删除
    return () => {
      // do nothing
    };
  }

  const performanceLogger = {
    app: logFactory('app', keyList.app, 'applaunch'),
    page: logFactory('page', keyList.page, 'pageload', true, true),
  };

  return (scope, ...args) => {
    performanceLogger[scope](...args);
  };
}

const LOG_LIST = [];

export default function (app, setter) {
  const appId = app.getAppId();
  let logger = {};

  try {
    logger = initLogger();

    logger.setBeforeAppshow((logger, logEvent) => {
      setLogBizInfo(logger, app);
      setLogAppInfo(app, logEvent);
    });

    logger.setBeforeEnterpage((logger) => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      currentPage && getLogBizInfoFromQuery(currentPage.options);

      setLogBizInfo(logger);
    });

    Event.on('logger:skynet', (detail) => {
      logger.appError(detail);
    });

    Event.on('shop:kdt_id:change', (kdtId) => {
      logger.setShopId(kdtId);
    });

    Event.on('request:server:fail', (detail) => {
      const extraData = {};
      const {
        requestOptions: {
          _options: { query = {} },
        },
      } = detail;
      const orderNo = query.order_no || query.orderNo;
      if (orderNo) extraData.orderNo = orderNo;
      logger.requestError({
        name: 'request server fail',
        message: '请求服务端出现错误',
        detail,
        ...extraData,
      });
    });

    Event.on('request:business:fail', (detail) => {
      logger.appError({
        name: 'request business fail',
        message: '请求处理逻辑发生错误',
        detail,
      });
    });

    Event.on('request:code:fail', (detail) => {
      const extraData = {};
      const {
        requestOptions: {
          _options: { query = {} },
        },
      } = detail;
      const orderNo = query.order_no || query.orderNo;
      if (orderNo) extraData.orderNo = orderNo;
      logger.requestError({
        name: 'request code fail',
        message: '请求响应了一个错误的 code',
        detail,
        ...extraData,
      });
    });

    Event.on('request:api:fail', (detail) => {
      logger.appError({
        name: 'request api fail',
        message: 'wx.request 接口调用失败',
        detail,
      });
    });

    Event.on('downloadFile:fail', (detail) => {
      logger.appError({
        name: 'downloadFile fail',
        message: 'downloadFile 调用失败',
        detail,
      });
    });
  } catch (e) {
    const noop = () => ({});
    // 模拟 logger 中的函数
    const mimicFuncs = [
      'addSessionParams',
      'setBizInfo',
      'appShow',
      'appHide',
      'pageShow',
      'pageHide',
      'log',
      'setShopId',
      'setPageName',
      'setPageParams',
      'getGlobal',
      'appError',
      'requestError',
    ];
    mimicFuncs.forEach((funcName) => (logger[funcName] = noop));
    logger.pageEvent = {};
  }

  // spm.initSpm();

  logger.updateSession = updateSession(appId, app.config.context);

  logger.performance = performance(app, logger);

  logger.addFromLinkRecord = addFromLinkRecord(logger);

  watchMemoryAndNetwork(logger);

  const { pageShow } = logger;

  app.once('app:token:success', (token) => {
    // 在小程序上，为了维持 uuid 的稳定性，使用用户的 fansId 作为 uuid.
    // 这是建立在小程序是一定可以拿到用户 fansId 的基础上的。
    logger.setUuid(token.platformFansId);

    setTimeout(() => {
      setLogBizInfo(logger);
      doLog();
    }, 100);
  });

  function doLog() {
    if (app.hasToken() && (app.getKdtId() || !app.needUpdateKdtIdByServer())) {
      let fn = null;
      /* eslint-disable no-cond-assign */
      while ((fn = LOG_LIST.shift())) {
        fn.call(null);
      }
    }
  }

  // logger.appShow = (data) => {
  //   LOG_LIST.push(() => appShow.call(logger, data));
  //   doLog();
  // };

  logger.pageShow = (data, url) => {
    setLogBizInfo(logger);
    LOG_LIST.push(() => pageShow.call(logger, data, url));
    doLog();
  };

  /**
   * 天网 log
   *
   * @param {Object} options 日志数据
   * @param {String} options.appName 日志平台上的应用名和运维平台上的应用名是一致的
   * @param {String} options.logIndex 日志平台上的log_index，从属于应用名
   * @param {String} options.name 日志类型，比如 ReferenceError
   * @param {String} options.message 日志内容，比如 a is not defined
   * @param {String} options.detail 日志详情
   * @param {String} options.level 日志等级，比如 error info
   * @param {String} options.extra 拓展信息
   */
  logger.skynetLog = (options) => {
    ZanWeappLogger.log({
      ...options,
      name: options.name || 'app-log',
      extra: {
        ...options.extra,
        appId: app.getAppId(),
        kdtId: app.getKdtId(),
        buyerId: app.getBuyerId(),
        appVersion: app.getVersion(),
        loggerParams: logger.getLogParams && logger.getLogParams(),
      },
    });
  };

  /**
   * app 错误日志，参数说明同 `logger.skynetLog`
   */
  logger.appError = (options) => {
    logger.skynetLog({
      ...options,
      name: options.name || 'app-error',
    });
  };

  /**
   * 请求错误日志，参数说明同 `logger.skynetLog`
   */
  logger.requestError = (options) => {
    logger.skynetLog({
      ...options,
      name: options.name || 'request-error',
      extra: {
        ...options.extra,
        orderNo: options.orderNo || '',
      },
    });
  };

  setter(app, 'logger', logger, true);
  setter(app, 'onPageNotFound', onPageNotFound(logger), true);
}
