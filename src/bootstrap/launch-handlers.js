/* #ifdef BUILD_ENV=youzanyun */
import sdkOnLoad from './yun-sdk/on-load';
/* #endif */

export default function (options) {
  try {
    /* #ifdef BUILD_ENV=youzanyun */
    ECLOUD_MODE && sdkOnLoad(this, options);
    /* #endif */
  } catch (e) {
    console.log('global yunsdk error', e);
    this.logger.appError({
      name: 'global yunsdk api init error',
      message: `全局扩展点(shop, user 初始化失败) ${e.message}`,
      detail: {
        message: e.message,
        stack: e.stack,
      },
    });
  }
}
