import db from 'utils/db';
import { downloadFile, setDownloadFileDep } from 'shared/utils/download-file';
import {
  carmen,
  node as request,
  nodeUseCdn as requestUseCdn,
  setRequestDep,
  baymax,
} from 'shared/utils/request';
import { isNewIphone } from 'shared/utils/browser/device-type';
import { storage } from '../base-api/utils';
import setter from './defaulter';
import store from './store';

import API from './api';
import Log from './log';
/* #ifdef BUILD_ENV=youzanyun */
import YunSdkSetters from './yun-sdk/setters';
/* #endif */
import config from './config';

export default function (app) {
  const { common, clientId, clientSecret } = config;
  app.config = app.config || {};

  store.config = Object.assign(config, app.config);

  if (!app.config.clientId) {
    console.warn('App 实例未提供自定义的 clientId，使用默认的 clientId：', clientId);
    console.warn('App 实例未提供自定义的 clientId，使用默认的 clientSecret：', clientSecret);
    app.config.clientId = clientId;
    app.config.clientSecret = clientSecret;
  }

  if (!app.config.clientSecret) {
    console.warn('App 实例未提供自定义的 clientSecret，使用默认的 clientId：', clientId);
    console.warn('App 实例未提供自定义的 clientSecret，使用默认的 clientSecret：', clientSecret);
    app.config.clientId = clientId;
    app.config.clientSecret = clientSecret;
  }

  // iphonex 标识
  if (isNewIphone()) {
    setter(app, 'deviceType', 'iPhone-X');
  }

  // 设置默认主题
  setter(app, 'themeClass', 'th0');

  setter(app.config, 'common', common, true);

  setter(app.globalData, 'copyright', {
    logo: app.config.common.yzLogo,
    isCustomized: false
  });

  // 全店风格相关配置信息
  setter(app.globalData, 'themeData', {
    themeClass: 'th0',
    isFantasy: false,
    themeFetched: false
  });

  API(app, setter);
  Log(app, setter);
  // Shop(app, setter);
  // InitData(app, setter);
  app.$store.commit('UPDATE_CONFIG', {
    appId: app.getAppId(),
    clientId: config.clientId,
    clientSecret: config.clientSecret
  });

  const requestConfig = {
    appId: app.getAppId(),
    version: app.getVersion()
  };

  setter(app, 'db', db, true);
  setter(app, 'storage', storage, true);

  setRequestDep(requestConfig);
  setDownloadFileDep(requestConfig);

  setter(app, 'carmen', carmen, true);
  setter(app, 'request', request, true);
  setter(app, 'requestUseCdn', requestUseCdn, true);
  setter(app, 'downloadFile', downloadFile, true);
  if (app.globalData.isRetailApp) {
    setter(app, 'baymax', baymax, true);
  }

  // 有赞云sdk
  /* #ifdef BUILD_ENV=youzanyun */
  ECLOUD_MODE && YunSdkSetters(app, setter);
  /* #endif */
}
