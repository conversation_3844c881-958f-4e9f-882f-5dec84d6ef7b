const ignoreAPI = ['onPageNotFound'];

export default function setDefault(target, property, value, force) {
  if (!force && target[property]) return;
  else if (force && target[property] && ignoreAPI.indexOf(property) === -1) {
    console.error(`属性 ${property} 将被覆盖，请检查程序`);
    console.table([
      {
        原始数据: target[property] + '',
        新数据: value + ''
      }
    ]);
  }
  target[property] = value;
}
