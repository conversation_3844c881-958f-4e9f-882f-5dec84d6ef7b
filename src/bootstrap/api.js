import { getExtSync } from 'shared/utils/browser/ext-config';
import store from './store';

store.extConfig = getExtSync() || {};

export default function (app, setter) {
  // 小程序版本
  setter(
    app,
    'getVersion',
    () => {
      return store.extConfig.userVersion || '';
    },
    true
  );

  setter(
    app,
    'getAppId',
    () => {
      return store.extConfig.appId || store.config.appId || '';
    },
    true
  );

  setter(app, 'getExtConfig', () => store.getExtConfig, true);

  setter(app, 'getExtKdtId', () => {
    const { extConfig = {} } = store;
    return extConfig.kdtId || 0;
  });

  setter(app, 'getDeviceType', () => {
    return app.deviceType;
  });

  setter(app, 'getPoints', () => {
    return app.request({
      path: 'wscump/integral/user_points.json',
    });
  });

  // 积分自定义名称
  setter(app, 'getPointsName', () => {
    return app.requestUseCdn({
      path: '/wscuser/membercenter/pointsName.json',
    });
  });
}
