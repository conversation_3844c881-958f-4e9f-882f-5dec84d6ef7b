import navigate from '@/helpers/navigate';
import args from '@youzan/weapp-utils/lib/args';
import pick from '@youzan/weapp-utils/lib/pick';
import {
  getWeappSceneCodeJson,
  handleSceneCodeResult,
} from '../../base-api/home.js';
import {
  autoEnterShop as teeAutoEnterShop,
  initEnterShopSetting as teeInitEnterShopSetting,
  prefetchEnterShop,
  isChainStoreSyncInWX as teeIsChainStoreSyncInWX,
  CHAIN_BIZ_TYPE,
  isHome,
  debug,
  localCache
} from '@youzan/tee-chain-store';
import {
  getEnterShopParams,
  ShopAutoEnterEnum,
} from '@youzan/shop-reenter-tee';
import { prefetch } from '@youzan/biz-center';
import { Hummer } from 'shared/utils/hummer';

export {
  LOCATIONSTATUS,
  localCache,
  isHome,
  enterShopApollo,
  isChainStoreSyncInWX,
  getAsyncApolloConfig,
  getSyncApolloConfig,
  getAuthStatusLBS,
  handleUrlWithShopAutoEnter,
  getOrderEnterShopPolicy,
  accordSkyLogger,
  setApplicationScene,
  CHAIN_BIZ_TYPE,
  EXT_SCENE_ID,
  ENTER_APOLLO_KEYS,
  getEnterShopCacheByBizType,
} from '@youzan/tee-chain-store';

// 执行完整进店逻辑时需要携带的进店业务参数
export const defaultEnterShopOptions = getEnterShopParams({
  type: ShopAutoEnterEnum.DEFAULT,
});

export const SHOP_OPE_STATUS = {
  RUNNING: 2,
  STOP: 3,
};

/**
 * 店铺选择列表跳转方法
 * @param {Object} options 需要透传至店铺选择列表的url query参数
 * @param {Object} options.redirectUrl 选择店铺后重定向的链接(必传)
 * @param {Object} extraConfig 附加参数
 * @param {Object} extraConfig.location 定位信息 { lat: number, lng: number, currentLocation: string}
 * @param {function} extraConfig.redirectFunc 支持业务方传入重定向方法
 * @param {function} extraConfig.extOpt 业务额外参数，不过滤，业务方可通过getExtOpt方法自取
 */
export function enterShopSelect(options = {}, extraConfig = {}) {
  const {
    redirectFunc = navigate.redirect,
    location = {},
    extOpt = {},
  } = extraConfig;

  const params = pick(options, [
    'redirectUrl',
    'umpAlias',
    'umpType',
    'alias',
    'kdt_id',
    'isRedirectDisable',
    'isHideLocation',
    'from',
    'dbKey',
    'dbid',
    'ignoreStoreLimit',
    'enterShopOptionsJsonString',
    'bizIndex',
    'bizListJson',
  ]);

  params.extOptJson = encodeURIComponent(JSON.stringify(extOpt));

  // 允许业务方指定定位参数
  const { lat, lng, currentLocation } = location || {};

  if (lat && lng && currentLocation) {
    const dbid = getApp().db.set(location);
    params.dbid = dbid;
    params.dbKey = 'location';
  }

  const url = args.add(
    '/packages/shop-select/chain-store/shopselect/index',
    params
  );

  redirectFunc({ url });
}

/**
 * 进店方法
 * @param {Objtct} options url 进店参数
 * @param {number} forceKdtId 进店kdtId(默认取url、小程序全局的，可通过该参数覆盖)
 * @param {boolean} ignoreCheckShopType 忽略检查店铺类型,此属性在onLaunch中使用
 * @param {boolean} needAuthProtocol 是否需要授权协议,获取定位需要
 * @param {string} options.logTag 进店调用来源,用于日志区分场景,默认other(global:全局;ext:中台化;other:其他)
 * @param {string} options.enterLogId 埋点上报使用,默认不用传
 * @param {string} options.chainBizType 业务场景调用
 * @returns {Promise} 进店结果 Promise
 */
export const autoEnterShop = (
  {
    redirectUrl,
    logTag,
    enterLogId = null,
    ignoreCheckShopType,
    needAuthProtocol,
    prefetchStatus,
    chainBizType,
    prefetchKdtId,
    ...options
  },
  forceKdtId
) => {
  return teeAutoEnterShop(options, {
    redirectUrl,
    forceKdtId,
    logTag,
    enterLogId,
    prefetchStatus,
    prefetchKdtId,
    needAuthProtocol,
    ignoreCheckShopType,
    chainBizType,
  });
};

/**
 * 检测当前网店状态是否停用或者打烊（进店后调用）
 * ES ===> EnterShop
 */
export const checkShopStatausAfterES = () => {
  const app = getApp();
  app.getShopInfo().then((res) => {
    const { config = {} } = res;
    const { shop_operation_status: shopOpeStatus } = config;
    if (+shopOpeStatus === SHOP_OPE_STATUS.STOP) {
      wx.redirectTo({
        url: '/packages/common/out-of-service/index',
      });
    }
  });
};

/**
 * 请求解析scene
 * @param {*} query
 * @param {*} originKdtId
 */
const formatWeappSceneCodeJson = (query, originKdtId) => {
  const { scene, ...restQuery } = query;
  return getWeappSceneCodeJson(
    {
      key: scene,
      kdt_id: originKdtId,
    },
    restQuery
  ).catch(() => {
    return query;
  });
};

/**
 * 如果 query 没有scene 则返回原始query, 否则解析scene
 * @param {*} query
 * @param {*} originKdtId
 * @param {*} valid 预请求数据有效性
 * @returns
 */
const handleWeappSceneCode = (query = {}, originKdtId) => {
  const { scene, ...restQuery } = query;
  if (!scene) return Promise.resolve(query);

  return Promise.race([
    new Promise((resolve) => {
      prefetch.getMiniappPrefetchRawData('scanCode').then((prefetch) => {
        const { fetchedData, fetchedQuery } = prefetch || {};
        if (fetchedData && fetchedQuery?.scene === scene) {
          resolve(handleSceneCodeResult(originKdtId, restQuery, prefetch));
        }
      });
    }),
    formatWeappSceneCodeJson(query, originKdtId),
  ])
    .then((scanData) => {
      return scanData;
    })
    .catch(() => {
      return query;
    });
};

/**
 * 页面是否接入初始化进店
 * @param {*} path
 * @returns
 */
export const shouldInitEnterShop = (path) => {
  if (isHome(path)) {
    return true;
  }
};

/**
 * 初始化进店配置 & 预拉取进店
 * @param {*} options
 * @returns {hadEnterShop} true 已经进过店 false 未进店
 */
export const initEnterShopSetting = async (options) => {
  const startTime = Date.now();
  const { path, query, kdtId } = options;

  const needInitEnterShop = shouldInitEnterShop(path);
  // 扫码点单埋点
  Hummer.mark.start(`scan_order_on_launch`);
  // 初始化进店配置
  teeInitEnterShopSetting({
    ...options,
    getApollo: !needInitEnterShop,
  });

  // 非连锁小程序不走进店||页面未接入初始化进店,不处理
  if (!teeIsChainStoreSyncInWX() || !needInitEnterShop) {
    return options;
  }

  try {
    const result = await Promise.race([
      prefetchEnterShop().catch((err) => {
        debug(err);
        return new Promise(() => {});
      }),
      (async () => {
        const resQuery = await handleWeappSceneCode(query, kdtId);
        const redirectUrl = args.add('/' + path, resQuery);
        const { kdtId: enterKdtId, homeDetailInfo } = await autoEnterShop({
          kdt_id: kdtId,
          ...resQuery,
          redirectUrl,
          logTag: 'on_launch',
          chainBizType: CHAIN_BIZ_TYPE.HOME,
          needAuthProtocol: true,
          ignoreCheckShopType: true,
          supportPreHomeDetail: 1,
        });
        const resultKdtId = enterKdtId || resQuery.kdt_id || kdtId;
        return { resultKdtId, homeDetailInfo, type: 'rt' };
      })(),
    ]);
    const { status, resultKdtId, homeDetailInfo } = result;
    // 延迟进店 || 非连锁(理论上不存在)
    if (['DELAY_ENTER_SHOP'].includes(status)) {
      return options;
    }
    debug('on_launch', Date.now() - startTime);
    localCache.set('_is_init_enter_shop_', true);
    return {
      path,
      kdtId: resultKdtId,
      homeDetailInfo,
      hadEnterShop: true, // 已经进过店
    };
    // const sceneCodePromise = handleWeappSceneCode(query, kdtId);
    // // 服务预拉取
    // const {
    //   status,
    //   uuid,
    //   prefetchKdtId = '',
    //   prefetchData,
    //   // eslint-disable-next-line @youzan/koko/no-async-await
    // } = await prefetchEnterShop();

    // // 延迟进店 || 非连锁(理论上不存在)
    // if (['DELAY_ENTER_SHOP', 'notChainStore'].includes(status)) {
    //   return options;
    // }

    // const { scene, ...restQuery } = query;

    // const { scanCode } = prefetchData || {};

    // if (scene && scanCode && scanCode.data && scanCode.query.scene === scene) {
    //   resQuery = handleSceneCodeResult(kdtId, restQuery, scanCode.data);
    // } else {
    //   // eslint-disable-next-line @youzan/koko/no-async-await
    //   resQuery = await sceneCodePromise;
    // }
    // redirectUrl = args.add('/' + path, resQuery);

    // // eslint-disable-next-line @youzan/koko/no-async-await
    // const { kdtId: enterKdtId, homeDetailInfo } = await autoEnterShop({
    //   kdt_id: kdtId,
    //   ...resQuery,
    //   redirectUrl,
    //   enterLogId: uuid, // 进店日志id
    //   logTag: 'on_launch',
    //   prefetchStatus: status, // 预拉取状态
    //   prefetchKdtId,
    //   chainBizType: CHAIN_BIZ_TYPE.HOME,
    //   needAuthProtocol: true,
    //   ignoreCheckShopType: true,
    //   supportPreHomeDetail: 1,
    // });
    // const resultKdtId = enterKdtId || resQuery.kdt_id || kdtId;
    // debug('on_launch', Date.now() - startTime, resultKdtId);
    // // 中间包含地理位置授权点击的动作会被统计，本数值仅供参考
    // return {
    //   path,
    //   query: resQuery,
    //   kdtId: resultKdtId,
    //   homeDetailInfo,
    //   hadEnterShop: true, // 已经进过店
    // };
  } catch (error) {
    debug('on_launch_error', error);
    return {
      path,
      hadEnterShop: false,
    };
  }
};
