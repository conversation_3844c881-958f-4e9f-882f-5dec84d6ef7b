import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

const COMPRESS_RULE = '@64k.mp3';

function getCdnAudio(url) {
  // 获取音频时长
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${url}?avinfo`,
      success: (res) => {
        resolve(res.data);
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

function getDuration(data) {
  // amr音频在ios中可能获取不正确的时间
  return Math.floor(+data.format.duration);
}

function getAudioTranscodeSrc(url) {
  const transcodeParam = '?avthumb/mp3/ab/64k/writeXing/0';
  let suffix = '';

  // amr音频需要强制在线转码，mp3音频直接播放
  if (/\.amr$/i.test(url)) {
    suffix = transcodeParam;
  }

  return url + suffix;
}

function removeQuery(url) {
  return url.split('?')[0];
}

export default {
  getAudioInfo(url) {
    const audioUrl = cdnImage(removeQuery(url), '', false);
    const compressedAudioUrl = `${audioUrl}${COMPRESS_RULE}`;

    // 先尝试获取压缩音频
    return getCdnAudio(compressedAudioUrl).then((data) => {
      return {
        url: compressedAudioUrl,
        duration: getDuration(data)
      };
    }).catch(() => {
      // 获取压缩音频失败，则降级获取原音频
      return getCdnAudio(audioUrl).then((data) => {
        return {
          url: getAudioTranscodeSrc(audioUrl),
          duration: getDuration(data)
        };
      });
    });
  }
};
