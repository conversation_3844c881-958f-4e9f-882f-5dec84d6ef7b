/* eslint-disable @youzan/dmc/wx-check */
import { Hummer } from 'shared/utils/hummer';
import each from '@youzan/weapp-utils/lib/each';
import event from 'shared/utils/app-event';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { cdnImage as cdnImageTee } from '@youzan/tee-biz-util';
import ExtendCreator from 'utils/extend-creator';
import Theme from 'components/showcase/index';
import Toast from '@vant/weapp/dist/toast/toast';
import getSystemInfo from 'shared/utils/browser/system-info';
import openWebView from 'shared/utils/open-web-view';
import { getCurrentRoute } from 'shared/utils/url';
import Event from '@youzan/weapp-utils/lib/event';
import navigate from '@/helpers/navigate';
import { checkReLaunchShopSelect } from '@/base-api/shop/chain-store';
import {
  autoEnterShop,
  enterShopApollo,
  checkShopStatausAfterES,
  defaultEnterShopOptions,
  CHAIN_BIZ_TYPE,
} from 'common-api/multi-shop/multi-shop-redirect';
import store from '@/base-api/store';
import Args from '@youzan/weapp-utils/lib/args';
import omit from '@youzan/weapp-utils/lib/omit';
import {
  resolveFeatureTitle,
  resolveFeatureData,
  isFeatureTabPage,
} from './helper';
/* #ifdef BUILD_ENV=youzanyun */
import SaasPageConfig from 'shared/sass/index';
/* #endif */
import { initSalesman } from 'shared/utils/salesman-share';
import {
  handleWeappSceneCode,
  checkPathIsHome,
  getWeappSceneCodeJson,
} from './home-api';
import pageComponentBehavior from '@/custom-tab-bar-v2/native-helper/page-component-behavior';
import findTabBarIndex from '@youzan/wsc-tab-bar-utils/lib/weapp/findTabBarIndex';
import { TAB_CONTAINER_RULE } from '@youzan/wsc-tab-bar-utils/lib/constants';
import { getCoverAdPromisePreload } from '../../../base-api/home';
import { getStorageSync, setStorageSync } from '@youzan/tee-api';

import {
  addParamPreventCouponLeak,
  FeaturePageType,
} from '@/utils/prevent-coupon-leak';

const SHARE_TIME_LINE_SCENE = 1154;
const HOME_IS_SHOW_LOADING_FIRST = 'home_is_show_loading_first';
const CHAIN_STORE_CHANG_KDT_ID_REG =
  /(pages(\/home)?\/(dashboard|feature|tab))/;

const app = getApp();
const { state } = store;
const extend = ExtendCreator();
const systemInfo = getSystemInfo();
let offlineChangeCb;
// 微页面是否曾经获取过数据
// __featureFirstLoad 有问题，语义有点怪
let __featureFetched = false;

let extendPageConfig = {};
/* #ifdef BUILD_ENV=youzanyun */
extendPageConfig = SaasPageConfig;
/* #endif */

const data = {
  data: {
    id: 0,
    alias: '',
    themeClass: app.themeClass,
    fetching: true,
    type: 'feature',
    // 微页面: 零售24小时货架模板
    isRetailShelfTemplate: false,
    banner: {
      logo: '',
      title: '',
    },
    tags: {
      list: [],
      selectedId: 0,
      scroll: false,
      choosedNum: [],
    },
    pageBgColor: '',
    goods: {},
    systemInfo: {},
    scrollIntoView: '',
    scrollTop: 0,
    success: true,
    showSelfFetchPopup: true,
    visitGift: {},
    buyerAddress: '',
    asyncShowcaseComponents: [],
    showFeatureVisitGift: false,
    showLotteryCode: false,
    share: {
      title: '',
      imageUrl: '',
      iconImageUrl: '',
    },
    salesman: {},
    pageTitle: '',
    shareTag: '',
    navigationbarConfigData: {},
    featureComLoaded: false,
    showSceneMarketSubscribe: false, // * 优先展示场景营销订阅消息
    canShowSceneMarketSubscribe: false, // * 确保场景营销订阅消息 onReady 之后展示
    crmFromScenePlanId: '', // * 场景营销计划 id
    appLogo: __wxConfig?.accountInfo?.icon,
    pageHide: false, // cover-ad 组件埋点用
    showAd: false,
    isEnterShop: false,
    homepageGray: false,
    preLoadImages: [], // 预拉取图片资源
    isRetailApp: app.globalData.isRetailApp,
    needHideLoading: false,
    isChainStore: app.isChainStoreSync(),
    isShowLoading: true, // 初始化为true是为了避免切店loading文案闪动问题
    usePreloadWebviewTicket: false,
  },
};

const lifeCycle = {
  __featureTitle: '',

  __featureFirstLoad: true,

  __isNewFeatureData: true,

  __needUpdateHomepage: false,

  __needEnterShop: true, // 标记是否需要进店
  behaviors: [pageComponentBehavior],
  onLoad(options) {
    try {
      Hummer.setExtra({
        // eslint-disable-next-line no-undef
        is_retail_kq: LS_COMPILE_MODE === 'kq',
        is_chain: app.isChainStoreSync(),
      });
    } catch (err) {
      console.error(err);
    }
    this.setYZData({
      isShowLoading: getStorageSync(HOME_IS_SHOW_LOADING_FIRST),
    });
    // TODO 同步代码
    const { scene } = wx.getLaunchOptionsSync();
    getCoverAdPromisePreload().then((result) => {
      if (scene === SHARE_TIME_LINE_SCENE) return;
      const { imageUrl, onOrOff = false, videoSource } = result;
      let config = onOrOff;
      if (videoSource === 2) {
        config = !!(onOrOff && imageUrl);
      }
      // 通过配置获取开屏广告展示状态
      const showAdConfig = config;
      // 如果用户这个打开看过开屏广告了就不展示了
      const showAd = showAdConfig && !app.globalData.showAd;
      app.globalData.showAd = showAdConfig;
      // 展示开屏广告，层级会覆盖loading
      this.setYZData(
        {
          showAd,
        },
        {
          immediate: true,
        }
      );
    });
    if (scene === SHARE_TIME_LINE_SCENE) {
      this.isFromTimeline = true;
      this.setYZData(
        {
          featureComLoaded: true,
          isFromTimeline: true,
          alias: options.alias,
          kdtId: options.kdt_id,
        },
        {
          immediate: true,
        }
      );
      return;
    }

    setTimeout(() => {
      this.closeFeatureLoading();
    }, 5000);
    app.on('home_dashboard_com_loaded', () => {
      if (!this.data.featureComLoaded) {
        this.closeFeatureLoading();
        // appLaunch是否监听冷启动场景
        Hummer?.mark?.log?.({
          tag: 'home-dashboard',
          scene: ['appLaunch', 'route'],
        });
      }
    });
    this.options = options;

    const hasCurPageProloadData =
      app.globalData.homeDetailPromise &&
      app.globalData.homeDetailPromisePath &&
      app.globalData.homeDetailPromisePath.includes('pages/home');

    if (
      app.globalData.homeDetailPromise &&
      !(
        app.globalData.homeDetailPromisePath &&
        app.globalData.homeDetailPromisePath.includes('pages/home')
      )
    ) {
      app.globalData.homeDetailPromise = null;
    }

    if (hasCurPageProloadData) {
      app.globalData.homeDetailPromise
        .then((featureDetail) => {
          // 查看是否开启日志的开关
          app.globalData.needPointSwitch = featureDetail.needPointSwitch;
          app.globalData.wscHomeRequestId = featureDetail?.requestId;

          const {
            needEnterShop,
            status: enterShopStatus,
            preLoadImages,
          } = featureDetail || {};
          if (preLoadImages && preLoadImages.length) {
            const images = Array.from(
              new Set(
                preLoadImages.map((i) => cdnImageTee(i.img, `!${i.size}x0.jpg`))
              )
            );

            this.setYZData(
              {
                preLoadImages: images,
              },
              {
                immediate: true,
              }
            );
            setTimeout(() => {
              this.setYZData({
                preLoadImages: [],
              });
            }, 5000);
          }
          if (enterShopStatus === 'DELAY_ENTER_SHOP') {
            this.setFeatureData(featureDetail, {
              isRefresh: false,
              isHomePage: true,
              startKdtId: featureDetail.kdtId,
              needToUpdateGlobalKdtId: false,
              cb: () => { },
              resolve: () => { },
              needRefresh: false,
            });
            return this.handlePreLoadHomeDetailEnterShop({
              scenes: enterShopStatus,
            });
          }

          if (!needEnterShop) {
            return new Promise((resolve) => {
              this.setFeatureData(featureDetail, {
                isRefresh: false,
                isHomePage: true,
                startKdtId: featureDetail.kdtId,
                cb: () => { },
                resolve,
                needRefresh: false,
              });
            });
          }
          return this.handlePreLoadHomeDetailEnterShop();
        })
        .then(() => {
          this.updatePageTitle(true);
        })
        .catch((err) => {
          // 预加载接口404 .youzan的com被封网
          app.globalData.homeDetailPromise = null;
          if (err === 'preError') {
            // 拉取原来的数据
            this.getHomeOrFeaturePageOnShow();
          } else {
            this.handleFeatureRequestError(err, {
              startKdtId: app.getKdtId(),
              isRefresh: false,
              cb: () => { },
            });
          }
        })
        .finally(() => {
          app.globalData.homeDetailPromise = null;
        });
    }

    if (options.crmFromScenePlanId) {
      this.setYZData({
        showSceneMarketSubscribe: true,
        crmFromScenePlanId: options.crmFromScenePlanId,
      });
    }

    checkReLaunchShopSelect();
    initSalesman.call(this, { sst: 1, scene: 'feature_page' });
    // 记录下来，在分享的时候需要

    this.__fetchPromise = Promise.resolve();

    const pagePath = this.route;
    // 乱七八糟的判断是否是店铺主页什么的，alias 代表是新编辑器数据
    if (isFeatureTabPage(pagePath)) {
      // 更新导航数据
      // 根据当前页面路径和 ext.json 中导航数据，确定加载的微页面内容
      const { list: navList = [] } = app.getNavConfig();
      if (navList.length) {
        this.processNavFeature(navList);
      } else {
        app.getNavConfigSync().then((res) => {
          const { list = [] } = res || {};
          // 在没有任何匹配的情况下，默认加载首页
          this.processNavFeature(list);
        });
      }
    } else if (options.id || options.alias) {
      this.setYZData({
        isHomePage: false,
        id: options.id || 0,
        alias: options.alias || '',
        title: options.title || '',
        showLotteryCode: !!options.codeAlias,
      });
    } else {
      this.setYZData({
        isHomePage: true,
      });
    }

    this.setYZData({
      systemInfo,
    });

    // // 首屏打点
    // Event.off('feature:first_screen_cover');
    // Event.once('feature:first_screen_cover', () => {
    //   Hummer?.markRendered('fs');
    // });

    // 修复点击首页relaunch后，切换网点不是最新页面实例导致页面不刷新
    offlineChangeCb = this.offlineChangeCallBack;
    // 多网点重新选择网点后要更新页面数据
    this.off('app:offlineId:change', offlineChangeCb);
    this.on('app:offlineId:change', offlineChangeCb);
    // 处理 “连锁店铺A -> 个人中心 -> 订单列表 -> 店铺B商品/课程” 这种场景下再回到首页还是店铺A首页的问题
    // 返回,更新上一个店铺kdtid的灰度,进店控制
    enterShopApollo('navBackUpdatePreKdtId').then((isTrue) => {
      if (
        this.data.isHomePage ||
        (isTrue && CHAIN_STORE_CHANG_KDT_ID_REG.test(this.route))
      ) {
        this.off('app:chainstore:kdtid:update', this.handleKdtIdChange);
        this.on(
          'app:chainstore:kdtid:update',
          (...args) => {
            if (app.globalData.homeDetailPromise) return;
            this.handleKdtIdChange(...args);
          },
          this
        );
      }
    });

    this.on('component:sku:cart', (res) => {
      const pages = getCurrentPages() || [];
      const { route } = pages[pages.length - 1] || {};
      if (route !== this.route) return;

      if (res.type === 'add') {
        Toast('添加购物车成功');
      }
    });
    if (options.shareTag) {
      this.setYZData({
        shareTag: options.shareTag,
      });
    }
  },

  closeFeatureLoading() {
    const { isRetailApp, isEnterShop, isChainStore } = this.data;
    if (isRetailApp && isChainStore && !isEnterShop) {
      this.setYZData({
        needHideLoading: true,
      });
      return;
    }
    // 标记已经看过打开小程序的loading，为了跟切店loading区分开
    setStorageSync(HOME_IS_SHOW_LOADING_FIRST, true);
    this.setYZData(
      {
        featureComLoaded: true,
      },
      {
        immediate: true,
      }
    );
  },

  // 处理导航页的微页面初始数据
  processNavFeature(list) {
    let updateTabData = { isHomePage: true };
    const nativeTabBarIndex = findTabBarIndex();

    list.some(({ page_path: pagePath, id, alias }, index) => {
      if (
        this.route === pagePath ||
        this.route === pagePath.replace(/^pages(-|\/)/, 'packages/') ||
        nativeTabBarIndex === index
      ) {
        this.tabbarIndex = index;
        // id 为老的微页面标志
        // alias 为新的微页面标志
        if (id > 0 || alias) {
          updateTabData = {
            // 确定有导航信息以后，不加载首页内容，加载对应微页面内容
            isHomePage: false,
            isTabPage: true,
            id,
            alias,
          };
        }
        return true;
      }
      return false;
    });

    this.setYZData(updateTabData);
  },

  getHomePage() {
    // 这里的逻辑主要是针对，用户之前打开了线下小程序，如果微信后台被杀了，
    // 就可能会自动进入网店小程序。这个时候根据 kdtid 的逻辑，可能用的还是之前的门店 kdtid，就会导致微页面的配置拉取失败。主要针对大网店和多网店的场景
    // 2023年08月11日 无大网店,多网店，逻辑清理
    return this.fetchHomepage();
  },

  onShow() {
    this.setYZData({
      pageHide: false,
    });
    if (this.isFromTimeline) {
      return;
    }
    if (!app.getAppId()) {
      wx.showModal({
        title: '提示',
        content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试',
      });
    }

    this.getHomeOrFeaturePageOnShow();

    this.setYZData({
      buyerAddress: app.getUserInfoSync('poi.title') || '',
    });

    if (this.__needUpdateHomepage) {
      this.__needUpdateHomepage = false;
      this.updateHomepage();
    }
  },

  onHide() {
    this.setYZData({
      pageHide: true,
    });
  },

  getHomeOrFeaturePageOnShow() {
    if (this.__featureFirstLoad) {
      if (!app.globalData.homeDetailPromise) {
        this.__fetchPromise = this.getHomePage().then(() => {
          this.updatePageTitle();
        });
      }
    } else {
      // 这里需要再做一次，因为多网点切换时候，会在网点页面直接调用刷新店铺首页
      // 但是他的更新标题是在多网点切换页面生效的，页面切回来以后会自动失效
      // 所以需要在当前页面再做一次
      this.updatePageTitle();
    }
  },

  onReady() {
    this.__featureFirstLoad = false;
    // 对界面的设置如wx.setNavigationBarTitle请在onReady之后设置
    // https://developers.weixin.qq.com/miniprogram/dev/framework/app-service/page.html
    // 店铺主页和微杂志页标题
    this.updatePageTitle();
    setTimeout(() => {
      this.setYZData({
        canShowSceneMarketSubscribe: true,
        showFeatureVisitGift: true,
      });
    }, 2000);
  },

  onUnload() {
    this.off('app:chainstore:kdtid:update', this.handleKdtIdChange);
  },

  onPullDownRefresh(cb) {
    this.__fetchPromise = this.fetchHomepage(
      true,
      typeof cb === 'function' ? cb : ''
    );
    this.trigger('home:refresh');
  },

  onMessage(event) {
    const messages = event.detail.data;
    const shareMessages = messages.filter(
      (message) => message.type === 'ZNB.share'
    );
    if (shareMessages.length) {
      this.webviewShareConfig = shareMessages.pop().config;
    }
  },

  onShareAppMessage(e) {
    app.trigger('feature_close_collect_tip');
    if (this.webviewShareConfig) {
      // 此处对封面图做一个替换，webview 给的封面图不太对, 是给 H5 用的
      if (
        this.webviewShareConfig &&
        this.webviewShareConfig.imageUrl &&
        this.data.share &&
        this.data.share.imageUrl &&
        // https://jira.qima-inc.com/browse/ONLINE-589494  除去社区团购
        this.data?.theme?.extra?.templateId !== 117
      ) {
        return {
          ...this.webviewShareConfig,
          imageUrl: this.data.share.imageUrl,
        };
      }
      return this.webviewShareConfig;
    }

    const { from, target = {} } = e || {};
    const { dataset = {} } = target;
    const { punch, shareTag } = dataset;
    // 教育培训-群打卡-邀请好友
    if (from === 'button' && punch) {
      return {
        title: `邀请你参与${e.target.dataset.title}`,
        path: `/packages/new-punch/introduction/index?alias=${e.target.dataset.alias}`,
        imageUrl: 'https://img01.yzcdn.cn/punch/image/<EMAIL>',
      };
    }

    const { title, imageUrl } = this.data.share;
    let pagePath = this.route;

    if (TAB_CONTAINER_RULE.test(pagePath)) {
      // 分享真实导航路径
      const list = getApp().globalData.tabbarOriginList || [];
      const pathConfig = list[findTabBarIndex()];
      const { pagePath: realNavPath } = pathConfig || {};
      pagePath = realNavPath || pagePath;
    }

    const requestData = [];

    const requestOptions = {
      ...(this.options || {}),
      ...defaultEnterShopOptions,
    };

    if (this.data.isTabPage && this.data.alias) {
      requestOptions.alias = this.data.alias;
    }
    // 拼接请求参数
    each(requestOptions, (value, key) => {
      requestData.push(`${key}=${value}`);
    });

    const shareConfig = {
      title:
        title ||
        resolveFeatureTitle(this.__featureTitle, {
          withoutSuffix: true,
        }),
      imageUrl,
    };

    if (from === 'button' && shareTag) {
      requestData.push(`shareTag=${shareTag}`);
    }

    shareConfig.jumpBlankPage = this.data.isHomePage;

    shareConfig.path = `${pagePath}?${requestData.join('&')}`;
    return shareConfig;
  },

  onShareTimeline() {
    const shareData = [];
    const originOption = this.options || {};
    const { title, iconImageUrl } = this.data.share;
    const requestOptions = {
      kdt_id: app.getKdtId(),
      alias: this.data.alias,
      ...defaultEnterShopOptions,
      ...originOption,
    };
    // 拼接请求参数
    each(requestOptions, (value, key) => {
      shareData.push(`${key}=${value}`);
    });

    return {
      query: `${shareData.join('&')}`,
      title,
      imageUrl: iconImageUrl,
    };
  },

  handleKdtIdChange() {
    enterShopApollo('navBackUpdatePreKdtId').then((isTrue) => {
      if (this.__pageKdtId === app.getKdtId()) {
        isTrue && this.updatePageTitle();
        return;
      }
      const pages = getCurrentPages() || [];
      const { route, __wxWebviewId__ } = pages[pages.length - 1] || {};
      // 如果 kdtId 变化后，页面还是当前显示的页面，则刷新首页数据,
      // 否则设置标志，等后续 onShow 再刷新数据
      if (this.route === route && this.__wxWebviewId__ === __wxWebviewId__) {
        this.updateHomepage();
      } else if (!isTrue || !CHAIN_STORE_CHANG_KDT_ID_REG.test(this.route)) {
        this.__needUpdateHomepage = true;
      }
    });
  },

  updateHomepage() {
    this.__fetchPromise = this.fetchHomepage(true);
  },

  updateNavigationConfig() {
    let data = this.navigationbarConfigData;
    try {
      const jsonData = JSON.parse(this.__showcaseOriginDataStr);
      data = (jsonData.components || []).find(
        (i) => i.type === 'navigationbar_config'
      );
      if (data) {
        this.setYZData({
          navigationbarConfigData: data,
        });
      }
    } catch (error) { }
  },

  updatePageTitle(jumpFetchPromise = false) {
    const promiseArr = [app.getShopConfigData()];
    if (
      !jumpFetchPromise &&
      Object.prototype.toString.call(this.__fetchPromise) === '[object Promise]'
    ) {
      promiseArr.push(this.__fetchPromise);
    }

    Promise.all(promiseArr)
      .then(() => {
        const title = resolveFeatureTitle(this.__featureTitle);
        this.updateNavigationConfig();
        this.setYZData({
          pageTitle: title,
        });
        wx.setNavigationBarTitle({
          title,
        });
      })
      .catch((err) => console.error(err));
  },

  closeVisitGift() {
    this.setYZData({
      'visitGift.show': false,
    });
  },
};

const featureRequest = {
  getAliasPromise({ alias, targetKdtId }) {
    return app
      .request({
        path: '/wscshop/api/showcase-retail/getRetailSubShopFeatureInfo.json',
        data: {
          source_alias: alias,
          target_kdt_id: targetKdtId,
        },
      })
      .then((data) => {
        return data.alias;
      })
      .catch(() => {
        return alias;
      });
  },
  /** 预请求进店处理 */
  handlePreLoadHomeDetailEnterShop({ scenes = '' } = {}) {
    return this.requestFeature({
      resolve: () => {
        Promise.resolve();
      },
      reject: () => {
        Promise.reject();
      },
      scenes,
    });
  },
  /** query 包含 scene 时的处理 */
  getQueryPromise(options) {
    const currentQuery = options || this.options;
    const { hasBase, chainStoreInfo } = state.shop || {};
    const needParseScene =
      checkPathIsHome(this.route) &&
      currentQuery.scene &&
      (!hasBase || chainStoreInfo?.isChainStore);

    return needParseScene
      ? handleWeappSceneCode(
        currentQuery,
        currentQuery.kdt_id || app.getKdtId()
      ).then((query) => {
        this.options = query;
        return query;
      })
      : Promise.resolve(currentQuery);
  },
  // 进店函数
  autoEnterShopFunc(options) {
    // 单店不需要进店
    if (this.__needEnterShop) {
      return this.getQueryPromise(options).then((query) => {
        return autoEnterShop({
          ...query,
          needAuthProtocol: true,
          chainBizType: checkPathIsHome(this.route)
            ? CHAIN_BIZ_TYPE.HOME
            : CHAIN_BIZ_TYPE.COMMON,
        });
      });
    }
    // 这个标识只针对初始化下拉
    this.__needEnterShop = true;
    return Promise.resolve();
  },
  getSceneAlias(scene) {
    return new Promise((resolve) => {
      wx.getBackgroundFetchData({
        fetchType: 'pre',
        fail() {
          getWeappSceneCodeJson({ key: scene }).then((data) => {
            resolve(data.alias);
          });
        },
        success(res) {
          try {
            const {
              data: {
                fetchData: { scanCode },
              },
            } = JSON.parse(res.fetchedData);
            const { query, data } = scanCode;
            // 场景值不同有可能是缓存导致，兜底再查一次接口
            if (query?.scene === scene) {
              const alias = data?.pageData?.alias;
              // 预拉取数据没获取到，再兜底查询一次
              if (!alias) {
                getWeappSceneCodeJson({ key: scene }).then((data) => {
                  resolve(data.alias);
                });
              } else {
                resolve(alias);
              }
            } else {
              getWeappSceneCodeJson({ key: scene }).then((data) => {
                resolve(data.alias);
              });
            }
          } catch (error) {
            resolve('');
          }
        },
      });
    });
  },
  requestFeature({ resolve, reject, cb, isRefresh, scenes = '' }) {
    if (this.__isRequesting) {
      resolve();
      return;
    }
    this.__isRequesting = true;

    let featureQueryOption = {};

    const baseParams = {
      stage: 16,
      check_multi_store: 1,
      close_chainstore_webview_limit: true,
      check_old_home: 1,
      hadEnterShop: true,
    };
    const { isHomePage } = this.data;

    // 首页 和 微页面采用node接口, 移除scene,防止无限重定向
    const redirectUrl = Args.add(
      '/' + this.route,
      omit(this.options || {}, 'scene')
    );
    this.autoEnterShopFunc({ ...this.options, redirectUrl }).then(
      async (subKdtId) => {
        checkShopStatausAfterES();
        if (subKdtId) {
          baseParams.kdt_id = app.getKdtId();
          // 进店4期，支持微页面单独进店
          baseParams.support_feature_enter_shop = 1;
        }
        let { alias } = this.data;
        if (/pages\/(home\/)?tab\//.test(this.route) && !alias) {
          alias = this.options.alias;
        }

        // 当前页是微页面且没alias且存在scene
        if (
          /pages\/home\/<USER>\/index/.test(this.route) &&
          !alias &&
          this.options.scene
        ) {
          // eslint-disable-next-line @youzan/koko/no-async-await
          const sceneAlias = await this.getSceneAlias(this.options.scene);
          alias = sceneAlias || alias;
        }

        // 微页面
        if (alias) {
          featureQueryOption = {
            path: '/wscdeco/feature-detail.json',
            data: {
              ...baseParams,
              alias,
              check_chainstore: true,
              version_control: JSON.stringify({
                use_native_feature_page: 1,
                feature_page_path: this.route,
              }),
            },
          };
        } else {
          // 首页
          featureQueryOption = {
            path: '/wscdeco/homepage-detail.json',
            data: {
              ...baseParams,
              version_control: JSON.stringify({
                sync_goods: 1,
                use_native_feature_page: 1,
                feature_page_path: this.route,
              }),
              platform: 2,
            },
          };
        }
        // 如果是下拉刷新，就直接使用服务端数据
        const forceUseServerData = isRefresh;
        // 在初始加载时，先不等待 init.json, 把数据拉回来
        const isUseShopInfo = app.globalData.fetchedShop || isRefresh;

        const needRefresh = false;
        let skipToken = false;

        // 下拉刷新强制跳过这个
        if (!isRefresh) {
          skipToken = true;
        }

        const startKdtId = app.getKdtId();

        app
          .request({
            ...featureQueryOption,
            config: {
              // 店铺主页和老的微页面还是需要 kdtid 来获取主页
              skipToken,
              // 首开采用预加载数据，二开先使用缓存数据，之后（500ms之后）再下拉刷新，使用最新数据（
              //    获取到新的数据后，可能会触发tee的bug， tee的create不会重新执行, 目前的解决方案是在showcase-container下对循环进行wx::if，重新渲染
              // ）, 中间的时间过长 500ms + 接口返回，还不如等接口返回, 暂时不要cache
              // cache: true,
              priority: 'high',
              forceRefreshCache: forceUseServerData,
              skipShopInfo: !isUseShopInfo,
            },
          })
          .then((res) => {
            this.setFeatureData(res, {
              isRefresh,
              isHomePage,
              startKdtId,
              cb,
              resolve,
              needRefresh,
              scenes,
            });
          })
          .catch((res) => {
            return this.handleFeatureRequestError(res, {
              startKdtId,
              isRefresh,
              cb: () => {
                reject();
              },
            });
          });
      }
    );
  },

  handleFeatureRequestError(res, otherOptions) {
    const { startKdtId, isRefresh, cb } = otherOptions;
    wx.hideLoading();
    if (res.code === 'multistore need user info') {
      wx.startPullDownRefresh();
      return;
    }

    if (res.code === 'chainstore need select shop') {
      // 如果 kdtId 已经变得不一样，说明已经完成进店操作了
      if (+startKdtId !== +app.getKdtId()) {
        wx.startPullDownRefresh();
        return;
      }
      app.once('app:chainstore:kdtid:update', () => {
        wx.startPullDownRefresh();
      });
      return;
    }

    // 查询连锁 alias 映射关系失败，跳转回首页
    // 2020/04/02：回退首页 改为 跳转到空状态页。后续可能需要换个 code，现在的 code 有点迷惑
    if (
      res.code === 'chainstore need enter homepage' ||
      res.code === 'chainstore check relation fail'
    ) {
      openWebView('/wscshop/common/error/not-exist', {
        title: '页面创建中/不存在',
        method: 'redirectTo',
        query: {
          kdt_id: app.getKdtId(),
          type: 'feature',
          redirect_url: getCurrentRoute({
            withSlash: true,
            withQuery: true,
          }),
        },
      });
      return;
    }

    // 处理重定向逻辑
    if (res && res.code === 302) {
      this.__isRequesting = false;
      __featureFetched = true;
      let data = {};
      if (res.data) {
        data = res.data;
      } else if (res.res && res.res.data) {
        data = res.res.data;
      }
      let { location = '' } = data || {};
      if (location && /^(\/?)pages|packages/.test(location)) {
        if (location.slice(0, 1) !== '/') {
          location = '/' + location;
        }
        location = Args.add(location, this.options);

        if (location.startsWith('/pages/home/<USER>')) {
          return wx.switchTab({
            url: location,
          });
        }
        return wx.redirectTo({ url: location });
      }
    }

    // 获取数据失败，也重置为 true
    __featureFetched = true;

    app.logger.appError({
      name: '微页面请求失败',
      message: '微页面请求失败',
      alert: 'warn',
      detail: {
        global: app.globalData,
        data: this.data,
        res,
      },
    });

    if (isRefresh) {
      wx.stopPullDownRefresh();
    }

    if (res.code === 60501) {
      Toast('小程序和有赞店铺信息不匹配');
      app.storage.remove('app:token');
    } else {
      wx.showModal({
        title: '获取信息失败',
        content: '是否重新加载页面',
        success: (res) => {
          if (res.confirm) {
            wx.reLaunch({ url: `/${this.route}` });
          }
        },
      });
    }

    cb && cb();
  },

  setFeatureData(res, otherOptions) {
    const {
      alias = '',
      title = '',
      isLock = 0,
      featureAlias,
      videoNumberId,
      isWebviewFeature = false,
      usePreloadWebviewTicket = false,
      kdtId: enterShopKdtId = 0,
      shopConfig = {},
      needPointSwitch,
      requestId,
    } = res;
    const {
      isRefresh,
      isHomePage,
      startKdtId,
      cb,
      resolve,
      needRefresh,
      needToUpdateGlobalKdtId = true,
      scenes = '',
    } = otherOptions;

    app.globalData.needPointSwitch = needPointSwitch;
    app.globalData.wscHomeRequestId = requestId;

    if (res.components[0]?.isGroupSquare) {
      app.logger.log({
        et: 'view',
        ei: 'e_v_w',
        en: '群曝光',
      });
    }
    // 已经进店 且 开始渲染装修数据了
    this.setYZData({
      isEnterShop: true,
      homepageGray: !!shopConfig?.openHomeGray,
    });

    if (this.data.needHideLoading) {
      this.closeFeatureLoading();
    }

    if (res && res.redirectInfo && res.redirectInfo.path) {
      let location = res.redirectInfo.path;
      if (location.slice(0, 1) !== '/') {
        location = '/' + location;
      }
      location = Args.add(location, this.options);

      if (location.startsWith('/pages/home/<USER>')) {
        return wx.switchTab({
          url: location,
        });
      }
      return wx.redirectTo({ url: location });
    }

    // 页面锁定，跳转锁定页面
    if (isLock === 1) {
      return wx.redirectTo({ url: '/packages/common/lock/index' });
    }

    app.globalData.featurePageConfig = {
      alias,
      title,
      featureAlias,
    };

    const response = res;
    // 从接口返回的新数据，不是从缓存返回的数据
    // 字段转换
    if (!response.data) {
      response.components = addParamPreventCouponLeak(
        response.components,
        FeaturePageType.MICROPAGE,
        // 优先用featureAlias因为首页需要
        featureAlias || alias
      );
      response.data = response.components;
      delete response.components;
      if (
        (response.data && response.templateId === 91) ||
        response.templateId === 17
      ) {
        const subEntry = response.data[1] && response.data[1].sub_entry;
        if (response.data[1].type === 'tpl_new_take_away') {
          subEntry[0].show_sold_num = response.data[1].showSoldNum;
        }
        response.data[1] = subEntry;
      }
    } else {
      response.data = addParamPreventCouponLeak(
        response.data,
        FeaturePageType.MICROPAGE,
        featureAlias || alias
      );
    }

    if (isRefresh) {
      wx.stopPullDownRefresh();
    }

    if (videoNumberId) {
      this.getVideoNumberInfo(videoNumberId);
    }

    // 是三方模板 且在白名单内
    if (isWebviewFeature) {
      // redirectUrl对三方模板没用,移除下
      const queryOptions = omit(this.options || {}, 'redirectUrl');
      // 首页或者路径符合 packages/home/<USER>
      const isTabPage = isHomePage || this.data.isTabPage;
      // 对于主页优先取 featureAlias，如果没有（多网点自定义首页的情况）则取 alias
      const finalAlias = isHomePage ? featureAlias || alias : alias;

      const baseUrl = `/wscshop/feature/${finalAlias}`;
      // TODO 同步代码
      const { isChainStore } = app.getShopInfoSync().chainStoreInfo || {};

      // 连锁店铺微页面扩展参数
      const chainStoreExtParams = isChainStore
        ? {
          // webview中kdtId是根据alias获取的，所以需要透传当前网店的alias
          alias: finalAlias,
          ...(needToUpdateGlobalKdtId ? { shopAutoEnter: 2 } : {}), // 进入三方模板前已经进过店了,就不需要在h5中再进店
        }
        : {};

      const params = {
        ...queryOptions,
        ...chainStoreExtParams,
        kdt_id: startKdtId,
        kdtId: startKdtId,
        ...(app.globalData.isRetailApp ? { is_retail_app: 1 } : {}),
        ...(isTabPage
          ? {
            has_weapp_tabbar: 1,
            weapp_tabbar_index: this.tabbarIndex || 0,
            use_native: 1,
            is_ecloud: app.ecloudMode ? 1 : 0,
          }
          : {}),
        ...(isHomePage ? { homepageWebview: 1 } : {}),
        oid: state.shop.offlineId,
      };
      // page这个参数webview不需要，带上了可能出现奇奇怪怪的问题
      delete params.page;
      try {
        // https://jira.qima-inc.com/browse/ONLINE-616802
        // 测试下来发现params很多参数自己又带了参数，也就是带了?
        // 转成url后webview内的h5识别不出来，就不展示底部导航了
        // 和社电大哥沟通后，先把所有带？的参数都去掉
        Object.keys(params).forEach((key) => {
          if (
            typeof params[key] === 'string' &&
            params[key].indexOf('?') > -1
          ) {
            delete params[key];
          }
        });
      } catch { }

      params.hummerStartTime =
        app?.globalData?.hummerPerformanceTime || new Date().getTime();

      const webviewFeatureUrl = Args.add(baseUrl, params);

      this.setData({
        isWebviewFeature,
        usePreloadWebviewTicket,
        webviewFeatureUrlStorage: webviewFeatureUrl,
        webviewFeatureUrl: this.data.showAd ? '' : webviewFeatureUrl,
      });
      wx.hideLoading();
    }

    resolveFeatureData.call(this, {
      response,
      cb,
      resolve,
      isRefresh,
      needRefresh,
      scenes,
    });
    this.__pageKdtId = enterShopKdtId;

    this.__isRequesting = false;
    // 单独进店后更新global的kdtid
    needToUpdateGlobalKdtId &&
      enterShopKdtId &&
      app.updateKdtId(enterShopKdtId, false, {
        mark: 916,
      });
    this.updatePageTitle();

    // 获取过数据，直接重置为 true
    __featureFetched = true;
    Event.trigger('FeatureAliasLoaded', alias);
  },

  // 获取频号直播信息
  getVideoNumberInfo(finderUserName) {
    wx.getChannelsLiveInfo &&
      wx.getChannelsLiveInfo({
        finderUserName,
        success: (res) => {
          wx.setStorageSync('channelsLiveInfo', JSON.stringify(res));
        },
        fail: (error) => {
          console.log('获取视频号直播信息失败', error);
        },
      });
  },

  fetchHomepage(isRefresh, cb = () => { }) {
    return new Promise((resolve, reject) => {
      // 没有appId就不运行了
      if (!app.getAppId()) {
        return reject();
      }

      // if (!isRefresh) {
      //   wx.showLoading({
      //     title: '加载中',
      //   });
      // }

      if (!this.___beforeFetchHomepageTiggered && this.triggerAsync) {
        this.triggerAsync('beforeFetchHomepage', {
          alias: this.data.alias,
        }).then((res = []) => {
          this.___beforeFetchHomepageTiggered = true;
          // 同步事件返回stop: true, 说明外部监听到时间并暂停获取主页流程
          if (res && res[0] && res[0].stop) {
            Event.on('home:fetchedSpecAlias', (e) => {
              // 获取到alias, 重新走一下获取微页面的逻辑
              const { alias } = e || {};
              this.setYZData({ alias });
              // this.__fetchedSpecAlias = true;
              this.updateHomepage();
            });
            reject();
          } else {
            // 继续获取alias
            this.requestFeature({ resolve, reject, cb, isRefresh });
          }
        });
      } else {
        this.requestFeature({ resolve, reject, cb, isRefresh });
      }
    });
  },

  // 获取分享设置
  fetchShareSetting(pageType, pageId) {
    app
      .requestUseCdn({
        path: '/wscshop/showcase/share/setting.json',
        data: {
          kdtId: app.getKdtId(),
          pageType,
          pageId,
        },
      })
      .then((res = {}) => {
        const { shareTitle = '', bannerImageUrl = '', iconUrl = '' } = res;
        // 安卓分享不能显示 gif，所以转成 jpg
        const imageUrl = /android/i.test(systemInfo.system)
          ? cdnImage(bannerImageUrl, '!730x0.jpg').replace(/gif$/, 'jpg')
          : bannerImageUrl;

        const iconImageUrl = /android/i.test(systemInfo.system)
          ? cdnImage(iconUrl, '!730x0.jpg').replace(/gif$/, 'jpg')
          : iconUrl;
        if (shareTitle) {
          this.setYZData({
            share: {
              title: shareTitle,
              imageUrl,
              iconImageUrl,
            },
          });
        }
      })
      .catch((err) => {
        console.log(err || '获取分享设置失败');
      });
  },

  offlineChangeCallBack(cb, options) {
    // 如果还没有获取到第一次微页面数据，并且有多网点数据改变事件触发，就直接忽略
    if (!__featureFetched) {
      return;
    }

    // 不执行当前页面绑定cb，针对多次切换微页面的场景需要options带上回退页面的wxExparserNodeId
    if (
      options &&
      options.wxExparserNodeId &&
      this.__wxExparserNodeId__ !== options.wxExparserNodeId
    ) {
      return;
    }

    // https://jira.qima-inc.com/browse/ONLINE-215573
    // 绿姿大客定制，现在的微页面会监听多网点变化，并且满足某个历史页面是首页/微页面的情况，跳回对应页面或者首页
    // src/packages/shop/multi-store/common/index.js，在多网点里面逻辑写了，如果直接回跳会存在首页数据未加载
    // 完就刷新，导致和网点首页模板展示不一致的情况。所以是首页相关监听网点变化，再做实际的小程序页面栈跳转
    if (options && options.forbidJump) {
      return;
    }

    const { delta = 1 } = options;
    // 使用 pullDown 是因为部分组件根据这个事件来处理定时器
    this.onPullDownRefresh((backToSamePage) => {
      // 跳分包的微页面不要再执行回调了，回调里会navigateBack
      // 切换网店后的几种场景（依据切换后网店对应的页面模板是否在分包而定）：
      // 1. 首页 -> 分包
      // 2. 首页 -> 首页
      // 3. 分包 -> 首页
      // 4. 分包 -> 分包
      // 对应 2和4 两种情况
      const pages = getCurrentPages() || [];
      const { route } = pages[pages.length - 1 - delta] || {};
      const isFeaturePage =
        /(pages\/home\/<USER>\/showcase-template/.test(
          route
        );

      if (isFeaturePage) {
        if (!backToSamePage) {
          navigate.switchTab({ url: '/pages/home/<USER>/index' });
        }
      }

      cb && typeof cb === 'function' && cb(true);
    });
  },

  handleCloseSceneMarketSubscribe() {
    this.setYZData({
      showSceneMarketSubscribe: false,
    });
  },

  coverAdHide() {
    this.setYZData({
      webviewFeatureUrl: this.data.webviewFeatureUrlStorage,
    });
  },
};

export default extend(
  {},
  data,
  event,
  featureRequest,
  lifeCycle,
  Theme,
  extendPageConfig
);
