const app = getApp();

export const preLoadHomeDetail = () => {
  return app.globalData.homeDetailPromise
    .then((featureDetail) => {
      const { needEnterShop } = featureDetail || {};
      if (!needEnterShop) {
        return Promise.resolve(featureDetail);
      }

      // return enterShopFunc(featureDetail);
    })
    .catch((res) => {
      if (res && res.code) {
        return Promise.reject(res);
      }
      return Promise.reject('preError');
    });
};

/**
 * scene换取
 * @param {*} query
 * @param {*} router
 * @returns
 */
export const getWeappSceneCodeJson = (data, router) => {
  return app
    .request({
      path: '/v3/weapp/scene-code-v2.json',
      data,
      cache: true,
    })
    .then(({ pageData }) => {
      const { guestKdtId, shopAutoEnter } = pageData || {};

      const kdtId = guestKdtId || data.kdt_id;
      return { ...pageData, ...router, kdt_id: kdtId, kdtId, shopAutoEnter };
    });
};

// 是否是首页
export const checkPathIsHome = (path) =>
  /(\/)?packages\/(ext-)?home\/dashboard\/index/.test(path) ||
  path === 'pages/home/<USER>/index';

/**
 * 如果 query 没有scene 则返回原始query, 否则解析scene
 * @param {*} query
 * @returns
 */
export const handleWeappSceneCode = (query = {}, originKdtId) => {
  const { scene, ...restQuery } = query;
  if (!scene) return Promise.resolve(query);

  return getWeappSceneCodeJson(
    {
      key: scene,
      kdt_id: originKdtId,
    },
    restQuery
  ).catch(() => {
    return query;
  });
};
