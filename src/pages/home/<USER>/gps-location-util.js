export function updateGpsLocationIfNeeded() {
  wx.getStorage({
    key: 'gps_location',
    success: ({ data }) => {
      // 未定位过 || 半小时后过期
      if (!data || Date.now() + 1800000 > data.expire) {
        gpsLocation();
      }
    },
    fail: gpsLocation
  });
}

function gpsLocation() {
  const app = getApp();
  app.getShopConfigData().then(data => {
    if (data && +data.record_buyer_gps === 1) {
      wx.navigateTo({
        url: '/packages/retail/gps-location/index'
      });
      wx.stopPullDownRefresh();
    }
  });
}
