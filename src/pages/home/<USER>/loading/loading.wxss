.feature-loading-warp {
  background: #f2f4f6;
  width: 100%;
  height: 100vh;
  position: fixed;
  z-index: 99999;
}
.feature-loading-circle {
  position: absolute;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  flex-direction: column;
}
.feature-loading-circle-bar {
  background-image: linear-gradient(#ebedf0, #969799);
  position: relative;
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}
.feature-loading-circle-bar-common {
  background-color: #ebedf0;
  width: 112rpx;
  height: 112rpx;
  position: absolute;
  right: 0;
  top: 0;
  border-radius: 50%;
}
.feature-loading-circle-bar-left {
  clip: rect(0, 56rpx, auto, 0);
}
.feature-loading-circle-bar-right {
  clip: rect(0, auto, auto, 56rpx);
  transform: rotate(118.8deg);
}
.feature-loading-mask {
  width: 100rpx;
  height: 100rpx;
  position: absolute;
  background: #f2f4f6;
  border-radius: 50%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
.feature-loading-logo {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  position: absolute;
  top: 8rpx;
  left: 50%;
  transform: translateX(-50%);
}
.feature-loading-bg {
  position: fixed;
  width: 654rpx;
  height: 112rpx;
  left: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  padding: 0 48rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: #fff;
}

.feature-loading-text {
  font-weight: 600;
  font-size: 32rpx;
  line-height: 48rpx;
  color: #333;
  text-align: center;
  margin-top: 32rpx;
}
