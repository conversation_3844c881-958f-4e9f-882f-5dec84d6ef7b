<template name="feature-loading">
  <view class="feature-loading-warp" style="display:{{ featureComLoaded ? 'none' : 'block' }};">
    <view class="feature-loading-circle">
      <view class="feature-loading-circle-bar">
        <view class="feature-loading-circle-bar-left feature-loading-circle-bar-common"></view>
        <view class="feature-loading-circle-bar-right feature-loading-circle-bar-common"></view>
        <view class="feature-loading-mask"></view>
      </view>
      <image class="feature-loading-logo" wx:if="{{ appLogo }}" src="{{ appLogo }}" mode="aspectFill" />
      <view class="feature-loading-text" wx:if="{{ isRetailApp && isChainStore && !isShowLoading }}">
        <view>正在搜索附近门店</view>
      </view>
    </view>
    <view class="feature-loading-bg" wx:if="{{ !isRetailApp }}"></view>
  </view>
</template>