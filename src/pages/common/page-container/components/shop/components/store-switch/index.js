import Storage from '@youzan/weapp-utils/lib/storage';
import WscComponent from 'pages/common/wsc-component/index';

const storage = Storage();
const app = getApp();
const INFO_MAP = {
  FAIL: '定位失败，请重试',
};
WscComponent({
  properties: {
    storeName: String,
    storeImage: String,
    storeSwitch: Boolean,
    buyerAddress: String,
    hideTopBar: Boolean,
  },

  data: {
    showTopBar: true,
    hideStore: false,
    isHomePage: false,
    poiInfo: INFO_MAP.FAIL,
    iconUrl: 'https://b.yzcdn.cn/2024j11d20/',
  },

  attached() {
    if (app.getOfflineId()) {
      this.initHideStore();
    } else {
      this.once('app:fetchedshopinfo', () => this.initHideStore());
    }
  },

  methods: {
    initHideStore() {
      // 是否隐藏网点
      const hideStore = app.getShopInfoSync().openHideStore || false;
      // 当前地址
      const poiInfo = app.getUserInfoSync('poi.title');
      let isHomePage = false;

      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      // 判断是否是首页
      if (/packages\/(ext-)?home\/dashboard\/index/.test(currentPage.route)) {
        isHomePage = true;
      }

      // 首页
      if (hideStore && isHomePage && !poiInfo) {
        app
          .request({
            path: '/wscshop/showcase/getPOIInfo.json',
            data: {
              kdt_id: app.getKdtId(),
              store_id: app.getOfflineId(),
            },
          })
          .then((res) => {
            if (res) {
              this.setYZData({
                poiInfo: res.address || INFO_MAP.FAIL,
              });
            } else {
              // 读取本地storage
              const userAddress = storage.get('multistore:user:poi');
              if (userAddress) {
                this.setYZData({
                  poiInfo: userAddress,
                });
              }
            }
          });
      }
      this.setYZData({
        hideStore,
        isHomePage,
        poiInfo: poiInfo || INFO_MAP.FAIL,
      });
    },
  },
});
