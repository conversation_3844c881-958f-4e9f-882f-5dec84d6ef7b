import mapKeysCase from '../utils/map-keys-case';

/**
 * 获取查询商详参数
 *
 * ATTENTION:
 *  这是 blank-page 预请求参数，要跟 src/ext-tee-wsc-goods/extensions/page-setup-block/common/api.js 同步修改保持一致
 */
function getGoodsParams(query) {
  const queryData = mapKeysCase.toCamelCase(query);

  const {
    alias,
    umpAlias = '',
    umpType = '',
    type = '',
    activityId = '',
    oid = 0,
    subKdtId,
  } = queryData;

  let { activityType = '' } = queryData;
  if (type === 'helpcut') {
    activityType = 'helpCut';
  }

  const paramsData = {
    ...queryData,
    ump_alias: umpAlias,
    ump_type: umpType,
    activityId,
    activityType: activityType || type,
    alias,
    subKdtId: +subKdtId || 0,
    // notQueryVoucher: 1, // 不再查询优惠券列表
    oid,
    // 中台化商详和老商详的区别标识 中台化商详需要在活动未开始的时候返回活动信息
    // 后端要求字符串类型 并且通过true/false区分
    fullPresaleSupportCart: 'true',
    // 接口是否为预请求, 不要随意复制这个字段, 只能用于商详首开预请求场景
    isDetailPrefetch: 1,
    // 接口静态化传入platform和client
    /* #ifdef weapp */
    platform: 'weixin',
    client: 'weapp',
    isGoodsWeappNative: 1,
    withoutSkuDirectOrder: '1',
    skuOrderVersion: 1,
    /* #endif */
  };
  return paramsData;
}

export function fetchGoodsDataForWeapp(pageQuery) {
  return new Promise((resolve, reject) => {
    const params = getGoodsParams(pageQuery);

    wx.request({
      url: 'https://h5.youzan.com/wscgoods/tee-app/detail-v2.json',
      data: params,
      withCredentials: true,
      success: ({ data: res = {}, statusCode }) => {
        const { code, data = {}, msg } = res;

        if (statusCode !== 200 || code !== 0) {
          reject(msg);
          return;
        }

        resolve(data);
      },
      fail: (err) => reject(err),
    });
  });
}
