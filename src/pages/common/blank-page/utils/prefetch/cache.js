import { getGlobal } from './global';

export function setCache(cacheKey, data) {
  return new Promise(() => {
    const globalData = getGlobal();

    if (!globalData.prefetchCache) {
      globalData.prefetchCache = {};
    }

    globalData.prefetchCache[cacheKey] = data;
  });
}

export function removeCache(cacheKey) {
  return new Promise((resolve) => {
    const globalData = getGlobal();

    if (!(cacheKey && globalData.prefetchCache)) {
      resolve(false);
      return;
    }

    resolve(delete globalData.prefetchCache[cacheKey]);
  });
}
