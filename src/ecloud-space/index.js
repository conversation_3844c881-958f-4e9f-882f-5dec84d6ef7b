export default function (sdk) {
  // const user = sdk.app.user;
  /* sdk.app.onAsync('ecloud:chain-store:shop-select', data => {
    console.log('ecloud:chain-store:shop-select triggered:', data);

    // 根据需求逻辑，判断是否需要用标准流程进店
    // 如果走标准流程，则返回 resolve；如果需要自己处理进店逻辑，则返回 reject
    if (!data.list.length) {
      // 阻断有赞进店逻辑
      console.log('skip youzan enter shop process...');
      return Promise.reject();
    }

    return Promise.resolve();
  }); */
}
