const webpack = require('webpack');

// webpack 插件：抽离重复的对象配置 (webpack 5)
class CommonConfigExtractionPlugin {
  constructor(options = {}) {
    this.options = {
      // 匹配的文件模式
      test: /\.js$/,
      // 最小重复次数才进行抽离
      minOccurrences: 2,
      // 变量名前缀，避免冲突
      varPrefix: '_ccfg',
      // 是否需要 source map
      needSourceMap: false,
      ...options,
    };

    // 预编译正则表达式，提升性能
    this.patterns = {
      base: /configurable\s*:\s*true\s*,\s*enumerable\s*:\s*true\s*,\s*writable\s*:\s*true/g,
      pure: /\{\s*configurable\s*:\s*true\s*,\s*enumerable\s*:\s*true\s*,\s*writable\s*:\s*true\s*\}/g,
      extended:
        /\{\s*configurable\s*:\s*true\s*,\s*enumerable\s*:\s*true\s*,\s*writable\s*:\s*true\s*,\s*([^{}]+)\s*\}/g,
      useStrict: /^"use strict";\s*/,
    };

    this.stats = {
      processedFiles: 0,
      totalReplacements: 0,
      totalSaved: 0,
    };
  }

  apply(compiler) {
    const pluginName = 'CommonConfigExtractionPlugin';

    compiler.hooks.compilation.tap(pluginName, (compilation) => {
      compilation.hooks.processAssets.tap(
        {
          name: pluginName,
          // webpack 5 的资源处理阶段
          // OPTIMIZE: 在 TerserPlugin 之前执行，使用 RawSource 让 TerserPlugin 重新生成 source map
          // OPTIMIZE_INLINE: 在 TerserPlugin 之后执行，需要正确处理已有的 source map
          stage: compilation.PROCESS_ASSETS_STAGE_OPTIMIZE,
        },
        (assets) => {
          Object.keys(assets).forEach((filename) => {
            if (!this.options.test.test(filename)) return;

            const asset = assets[filename];
            const source = asset.source().toString();

            if (typeof source !== 'string') return;

            // 处理代码
            const result = this.processSource(source, filename);

            if (result.processed && result.operations.length > 0) {
              this.stats.processedFiles++;
              this.stats.totalReplacements += result.replacements;

              // 使用 ReplaceSource 进行精确的代码替换，保持完全准确的 source map
              const { ReplaceSource } = webpack.sources;
              const replaceSource = new ReplaceSource(asset);

              // 执行所有替换操作
              result.operations.forEach((operation) => {
                if (operation.type === 'replace') {
                  replaceSource.replace(
                    operation.start,
                    operation.end - 1, // ReplaceSource 的 end 是 inclusive
                    operation.replacement
                  );
                } else if (operation.type === 'insert') {
                  replaceSource.insert(operation.start, operation.replacement);
                }
              });

              // 计算节省的字节数
              const finalSource = replaceSource.source();
              this.stats.totalSaved += source.length - finalSource.length;

              // 使用 ReplaceSource，source map 会自动正确更新
              assets[filename] = replaceSource;
            }
          });
        }
      );
    });
  }

  processSource(source) {
    // 生成唯一变量名，避免冲突
    const varName = this.generateUniqueVarName(source);

    // 检查基础模式出现次数
    this.patterns.base.lastIndex = 0; // 重置正则表达式
    const baseMatches = source.match(this.patterns.base);
    if (!baseMatches || baseMatches.length < this.options.minOccurrences) {
      return { processed: false, source, replacements: 0, operations: [] };
    }

    // 收集所有需要进行的替换操作，而不是直接修改源码
    const operations = [];
    let replacements = 0;

    // 策略1: 替换纯对象 {configurable:true,enumerable:true,writable:true}
    this.patterns.pure.lastIndex = 0;
    let match;
    /* eslint-disable no-cond-assign */
    while ((match = this.patterns.pure.exec(source)) !== null) {
      operations.push({
        type: 'replace',
        start: match.index,
        end: match.index + match[0].length,
        replacement: varName,
      });
      replacements++;
    }
    /* eslint-enable no-cond-assign */

    // 策略2: 替换扩展对象，支持复杂的initializer函数
    const extendedMatches = this.findExtendedMatches(source);
    extendedMatches.forEach((matchInfo) => {
      const { otherProps, index, length } = matchInfo;
      const replacement = `{...${varName},${otherProps}}`;

      operations.push({
        type: 'replace',
        start: index,
        end: index + length,
        replacement,
      });
      replacements++;
    });

    if (replacements === 0) {
      return { processed: false, source, replacements: 0, operations: [] };
    }

    // 添加变量定义操作
    const commonConfig = `var ${varName}={configurable:true,enumerable:true,writable:true};\n`;
    const useStrictMatch = source.match(this.patterns.useStrict);
    const insertPosition = useStrictMatch ? useStrictMatch[0].length : 0;

    operations.push({
      type: 'insert',
      start: insertPosition,
      end: insertPosition,
      replacement: commonConfig,
    });

    // 按位置排序，从后往前处理
    operations.sort((a, b) => b.start - a.start);

    return {
      processed: true,
      source,
      replacements,
      operations,
      varName,
    };
  }

  // 生成唯一变量名，避免与现有代码冲突
  generateUniqueVarName(source) {
    let varName = this.options.varPrefix;
    let counter = 0;

    // 检查变量名是否已存在（更全面的检查）
    while (this.isVarNameConflict(source, varName)) {
      counter++;
      varName = `${this.options.varPrefix}${counter}`;
    }

    return varName;
  }

  // 检查变量名冲突（包括定义、使用、参数等）
  isVarNameConflict(source, varName) {
    const conflictPatterns = [
      new RegExp(`\\bvar\\s+${varName}\\b`, 'g'),
      new RegExp(`\\blet\\s+${varName}\\b`, 'g'),
      new RegExp(`\\bconst\\s+${varName}\\b`, 'g'),
      new RegExp(`\\bfunction\\s+${varName}\\b`, 'g'),
      new RegExp(`\\bfunction\\s*\\([^)]*\\b${varName}\\b`, 'g'),
      new RegExp(`\\b${varName}\\s*=`, 'g'),
    ];

    return conflictPatterns.some((pattern) => pattern.test(source));
  }

  // 查找扩展对象，支持复杂的initializer函数
  findExtendedMatches(source) {
    const matches = [];
    const startPattern =
      /\{\s*configurable\s*:\s*true\s*,\s*enumerable\s*:\s*true\s*,\s*writable\s*:\s*true\s*,/g;

    let match = startPattern.exec(source);
    while (match !== null) {
      const startIndex = match.index;
      const afterBaseProps = startIndex + match[0].length;

      // 从对象开始的{位置开始解析，找到匹配的结束括号
      const endResult = this.findMatchingBrace(source, startIndex);

      if (endResult.found) {
        const totalLength = endResult.endIndex - startIndex + 1;
        const innerContent = source.slice(afterBaseProps, endResult.endIndex);

        // 提取除基础属性外的其他属性
        const otherProps = innerContent.trim();

        if (otherProps) {
          matches.push({
            otherProps,
            index: startIndex,
            length: totalLength,
          });
        }
      }

      match = startPattern.exec(source);
    }

    return matches;
  }

  // 找到匹配的结束大括号，正确处理嵌套
  findMatchingBrace(source, startIndex) {
    let braceCount = 0;
    let inString = false;
    let stringChar = '';
    let escaped = false;

    for (let i = startIndex; i < source.length; i++) {
      const char = source[i];

      if (escaped) {
        escaped = false;
        continue;
      }

      if (char === '\\') {
        escaped = true;
        continue;
      }

      // 处理字符串状态
      if (!inString && (char === '"' || char === "'" || char === '`')) {
        inString = true;
        stringChar = char;
        continue;
      }

      if (inString) {
        if (char === stringChar) {
          inString = false;
          stringChar = '';
        }
        continue;
      }

      // 处理大括号计数
      if (char === '{') {
        braceCount++;
      } else if (char === '}') {
        braceCount--;
        if (braceCount === 0) {
          return { found: true, endIndex: i };
        }
      }
    }

    return { found: false, endIndex: -1 };
  }
}

module.exports = {
  CommonConfigExtractionPlugin,
};
