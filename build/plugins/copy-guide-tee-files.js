const path = require('path');
const { isDev, fs } = require('@kokojs/shared');

// 递归创建目录
function ensureDirectoryExistence(filePath) {
  if (fs.existsSync(filePath)) return;
  ensureDirectoryExistence(path.dirname(filePath));
  fs.mkdirSync(filePath);
}

// 复制文件夹内容
function copyFiles(sourceDir, targetDir) {
  if (!fs.existsSync(sourceDir)) {
    console.error(`Source directory ${sourceDir} does not exist.`);
    return;
  }

  // 创建目标目录（如果不存在）
  ensureDirectoryExistence(targetDir);

  // 遍历并复制文件
  const files = fs.readdirSync(sourceDir);
  files.forEach((file) => {
    const sourcePath = path.join(sourceDir, file);
    const targetPath = path.join(targetDir, file);

    if (fs.lstatSync(sourcePath).isDirectory()) {
      // 如果是目录，递归调用
      copyFiles(sourcePath, targetPath);
    } else {
      // 如果是文件，先确保目标目录存在
      ensureDirectoryExistence(path.dirname(targetPath));
      fs.copyFileSync(sourcePath, targetPath);
    }
  });
}

const srcPath = path.resolve(__dirname, '../../src');

class CopyGuideTeeFielsPlugin {
  constructor(options) {
    this.options = options;
  }

  apply(compiler) {
    compiler.hooks.beforeCompile.tapAsync(
      'CopyGuideTeeFielsPlugin',
      (_, callback) => {
        const { APP_NAME } = process.env || {};

        if (isDev() || !APP_NAME) return;

        const sourceDir = path.resolve(
          srcPath,
          'ext-tee-guide/extensions/tee-client'
        );
        const targetDir = path.resolve(srcPath, 'packages/guide/tee-client');

        console.log('Copying guide tee files...');
        copyFiles(sourceDir, targetDir);

        callback();
      }
    );
  }
}

module.exports = {
  CopyGuideTeeFielsPlugin,
};
