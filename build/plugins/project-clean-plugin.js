/**
 * 插件功能：项目清理
 */

const path = require('path');
const { root, fs, isProd } = require('@kokojs/shared');

const pluginName = 'ProjectCleanPlugin';

/**
 * 无依赖文件
 *
 * NOTE: 这些文件是由于 koko.config.js 中 convertPathConfig 转换后导致的无依赖，插件内部未删除，需手动删除以减少体积
 */
const deadFiles = [
  'packages/async-main/@y-open/vt/native/weapp/button',
  'packages/async-main/@y-open/vt/native/weapp/icon',
  'packages/async-main/@y-open/vt/native/weapp/info',
  'packages/async-main/@y-open/vt/native/weapp/loading',
  'packages/async-main/@y-open/vt/native/weapp/overlay',
  'packages/async-main/@y-open/vt/native/weapp/popup',
  'packages/async-main/@y-open/vt/native/weapp/skeleton',
  'packages/async-main/@y-open/vt/native/weapp/transition',
];

class ProjectCleanPlugin {
  apply(compiler) {
    if (!isProd() || process.env.BUILD_ENV === 'youzanyun') {
      return;
    }

    const log = (...args) => console.log(`[${pluginName}]`, ...args);

    compiler.hooks.afterEmit.tap(pluginName, (compilation) => {
      console.log('\n');
      log('start.');

      deadFiles.forEach((filePath) => {
        const fullPath = path.join(root, 'dist', filePath);
        fs.rmSync(fullPath, { force: true, recursive: true }); // rm -rf
        log('remove dead file:', fullPath);
      });

      log('end.\n');
    });
  }
}

module.exports = {
  ProjectCleanPlugin,
};
