#!/bin/bash
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/*
set -e
basepath=$(cd `dirname $0`; pwd)

if [ "$FASTBUILD" = "true" ]; then

  WSC_YARN_CACHE_DIR="$HOME/wsc_yarn_cache"

  export YARN_CACHE_FOLDER=$WSC_YARN_CACHE_DIR

  echo -e "yarn 缓存目录为 $YARN_CACHE_FOLDER"

  pushd $basepath/..
  if [ -d "node_modules" ]; then
    find node_modules -type d -name '@youzan-open' -exec rm -rf {} +
    find node_modules -type d -name '@youzan' -exec rm -rf {} +
    find node_modules -type d -name '@youzan-cloud' -exec rm -rf {} +
    find node_modules -type d -name '@kokojs' -exec rm -rf {} +
    echo "成功删除 @youzan-open、@youzan、@youzan-cloud、@kokojs"
  else
    echo "node_modules 不存在，无需执行删除操作"
  fi
  popd
fi

# 有赞云构建时直接下载子仓库代码，不执行 postinstall
if [ -z "$ISV_BUILD_SERVER" ] || [ "$ISV_BUILD_SERVER" != "1" ]; then

  yarn --ignore-scripts

  if [ "$FASTBUILD" = "true" ]; then
    yarn sparse
  else
    yarn postinstall
  fi
fi

source $basepath/run.sh
run 'build' $1
