#!/bin/bash
source ./build/parse.sh

# weapp-analysis-plugin 依赖 appName，默认值为 'wsc'
while getopts "t:p:m:u" opt; do
  case $opt in
    t)
      appName=$OPTARG
      ;;
    p)
      availablePackages=$OPTARG
      ;;
    u)
      ufile=true
      ;;
  esac
done

echo "ISV_BUILD_SERVER = $ISV_BUILD_SERVER"

# 有赞云场景下 ISV_BUILD_SERVER==1 不拉取diff-pack动态规则
if [ -z "$ISV_BUILD_SERVER" ] || [ "$ISV_BUILD_SERVER" != "1" ]; then
  node ./diff-pack/build.js
fi

run () {
  env=$1

  # 环境变量确认
  # 交易切流依赖此逻辑，后续可移除
  if test $env = 'dev'
  then
    export NODE_ENV=development
  elif test $env = 'build'
  then
    query="$query --use-local-config"
    export NODE_ENV=production
  fi

  if test $availablePackages
  then
    query="$query -p $availablePackages"
  fi

  if [ -n "$APP_NAME" ]
  then
    # 如果进程传入则不处理
    # 1. 云定制: 通过spawn进程传入环境变量
    # 2. 本地dev
    echo "使用进程环境变量$APP_NAME"
  else
    # 标品构建: 机器控制构建产物类型
    build_extra=$HARDWORKER_TASK_EXTRA_JSON

    app_name="wsc"
    app_version=""

    json_app=$(get_json_value "${build_extra}" "appName")
    json_app_version=$(get_json_value "${build_extra}" "version")
    echo "解析的appName为$json_app"

    if test $json_app
    then
      app_name=$json_app
    fi

    if test $json_app_version
    then
      app_version=$json_app_version
    fi

    export APP_NAME=$app_name
    export APP_VERSION=$app_version
  fi

  echo -e "\033[32m运行 koko $env 命令:\033[0m koko $env weapp $query"
  # koko $env weapp $query --skip-minify
  koko $env weapp $query
  node ./build/script/after-build.js

  if [[ $ufile == true ]]
  then
    koko ufile
  fi
}
