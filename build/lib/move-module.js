const path = require('path');
const { existsSync } = require('fs-extra');

const ANALYSIS_FILE = path.resolve(__dirname, '../../.analysis/analysis.json');

const getMovableListMap = () => {
  try {
    if (!existsSync(ANALYSIS_FILE)) {
      return {};
    }
    const { moveableList } = require(ANALYSIS_FILE);
    return moveableList.reduce((pre, cur) => {
      const key = cur.name.replace('/node_modules/', '').replace('.vue', '.js');
      pre[key] = cur.packages;
      return pre;
    }, {});
  } catch (error) {
    throw new Error('获取文件出错', error);
  }
};

const isIncludeUsedModule = (usedModules, movableModules) => {
  if (!usedModules.length) {
    return false;
  }
  return usedModules.every((child) => {
    return movableModules.indexOf(child) > -1;
  });
};

module.exports = {
  getMovableListMap,
  isIncludeUsedModule,
};
