const path = require('path');
const { isRetailBiz } = require('../script/biz/retail');
const fs = require('fs');
const { hash } = require('@kokojs/shared');

function getDefaultAppId(extfile) {
  const context = path.join(__dirname, '..', '..');
  const extJSON = require(typeof extfile === 'string'
    ? extfile
    : path.join(context, 'src/ext.json'));
  return extJSON.extAppid;
}

function getMiniProgramConfig(bizInfoList = []) {
  let extfile = true;
  if (isRetailBiz) {
    extfile = path.resolve(__dirname, '../../src/ext-retail.json'); // 零售小程序特殊的 ext.json
  }

  // 定制模式不需要清空，否则都需要清空
  const mergedEmptyComponent =
    process.env.BUILD_ENV === 'youzanyun' ? '' : /custom-components/;

  const NEEDED_PAGES = process.env.NEEDED_PAGES;
  const CLOUD_V2_DEV_ENTRY_PATH = process.env.CLOUD_V2_DEV_ENTRY_PATH;

  const miniPluginConfig = {
    extfile,
    ignoreTabbar: true, // 插件不处理 tabbar 以免和 copy 插件冲突
    analyze: false,
    resources: [path.join(__dirname, '../../shared')],
    optimizeIgnoreDirs: [],
    emptyComponent: mergedEmptyComponent,
    defaultAppId: getDefaultAppId(),
    enableES5Compile: false,
    transformAppJsonHook(appJson) {
      if (CLOUD_V2_DEV_ENTRY_PATH)
        appJson.entryPagePath = CLOUD_V2_DEV_ENTRY_PATH;

      // home 按需打包，无法注入 ranta-tab-bar，自动删除一下
      if (process.env.HOME_PRESET) {
        // delete appJson.tabBar;
      }

      if (NEEDED_PAGES) {
        delete appJson.tabBar;
        const neededPagesArr = NEEDED_PAGES.split(',');
        const pages = appJson.pages.filter((pagePath) =>
          neededPagesArr.some((route) => route === pagePath)
        );
        appJson.pages = pages;
      }

      Object.assign(appJson.usingComponents, {
        _: 'components/_/index',
      });

      if (process.env.HOME_PRESET === 'youzanyun') {
        Object.assign(appJson.usingComponents, {
          // 定制商详可能用到了 van-progress
          'van-progress': '@vant/weapp/progress/index',
        });
      }

      // https://developers.weixin.qq.com/miniprogram/dev/framework/client-lib/version.html
      if (
        process.env.BUILD_ENV === 'youzanyun' ||
        process.env.APP_NAME === 'kq'
      ) {
        Object.assign(appJson.rendererOptions.skyline, {
          disableABTest: true,
          sdkVersionBegin: '15.200.200',
          sdkVersionEnd: '15.255.255',
        });
      } else {
        // note: >3.8.0 对应 >=8.0.57 版本
        Object.assign(appJson.rendererOptions.skyline, {
          disableABTest: true,
          sdkVersionBegin: '3.8.1',
          sdkVersionEnd: '15.255.255',
          minimumMemorySize: '5121',
          androidVersionBegin: '8.0.58',
          androidVersionEnd: '15.255.255',
        });
      }

      return appJson;
    },
    placeholderComponent: '_',
  };

  return miniPluginConfig;
}

function getCacheVersion() {
  const packageJsonPath = path.resolve(__dirname, '../../package.json');
  const kokoConfigPath = path.resolve(__dirname, '../../koko.config.js');
  const jsonContent = fs.readFileSync(packageJsonPath, 'utf-8');
  const kokoConfigContent = fs.readFileSync(kokoConfigPath, 'utf-8');
  return hash(`${jsonContent}${kokoConfigContent}`);
}

const outputPath = 'dist';

module.exports = {
  getMiniProgramConfig,
  outputPath,
  getCacheVersion,
};
