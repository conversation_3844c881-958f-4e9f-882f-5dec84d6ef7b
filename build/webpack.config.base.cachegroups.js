const mainCacheGroups = [
  // '@youzan/ump-biz-utils',
  // '@youzan/utils/ranta-adapter',
  'tslib',
  '@youzan/utils/object',
  '@youzan/utils/url',
  '@youzan/utils/string',
  '@youzan/utils/version',
  '@youzan/utils/number',
  '@youzan/utils/date',
  '@youzan/weapp-utils/lib/countdown',
  // '@youzan/goods-biz-utils',
  '@youzan/tee-logger',
  '@youzan/tee-biz-navigate',
  '@youzan/tee-biz-util',
  '@youzan-open/ranta-config-compressor',
  '@ranta/store',
  '@youzan/tee-api',
  '@youzan-open/tee-api',
  '@youzan/sales-icon-utils',
  '@youzan/passport-tee-shared',
  '@youzan/ranta-weapp',
  'shared/common/base/ranta/widget',
  '@youzan/retail-tee-component/src/common',
  // '@youzan/wsc-tee-trade-common/lib/order-utils',

  // 消息订阅
  'utils/subscribe-message',
  'src/helpers/subscribe',

  // 授权组件
  'shared/components/account/user-authorize',

  // 店铺
  'src/helpers/shop',
];

const mainBlackCacheGroups = [
  '@youzan-open/vant-tee/dist/mixins/touch.js',
  '@youzan-open/vant-tee/native/weapp/common/color.js',
  '@youzan-open/vant-tee/native/weapp/dialog/props.js',
  'src/constants/member-constants.js',
  'shared/utils/sticky-control',
  'src/common-api/sku/index.js',
  'components/feature-sku/utils.js',
  'shared/common/components/base-sku',
  'packages/order-native/common/config.js',
  '@youzan/decorate-tee/src/common/utils/async',
];

const isInMainCacheGroup = (module) => {
  const result = mainCacheGroups.filter((item) => module.includes(item));
  return result.length > 0;
};

const isInMainBlackCacheGroup = (module) => {
  const result = mainBlackCacheGroups.filter((item) => module.includes(item));
  return result.length > 0;
};

const excludeCommonGroups = [
  'components/usercenter',
  'components/pay-manager',
  'components/cart',
  'components/ump',
  'components/showcase-options',
  '@youzan/tee-biz-ui',
  'showcase/coupon',
  'goods-layout/behaviors/goods-item-price-behavior',
  'showcase/wxvideo-live',
  'components/goods-showcase',
  'decorate-tee/src/decorate/coupon/constants',
  'decorate-tee/src/decorate/ump-reward',
  'vant-weapp',
  '@youzan/tee-user-components',
  '@youzan/retail-tee-component',
  '@youzan/tee-multi-app-shared',
];

const excludeShowcaseGroups = [
  'showcase/rich-text',
  'showcase/captain-components',
  'components/showcase',
  'components/showcase-options',
];

const isInExclude = (module, groups) => {
  return groups.some((item) => module.includes(item));
};

const groups = {
  commons: {
    name: 'c',
    chunks: 'all',
    test: (module, { chunkGraph }) => {
      if (module.resource && isInMainBlackCacheGroup(module.resource)) {
        return false;
      }

      // TODO [临时解]
      // 目前底层的 _usedModules 只包含 .js 文件，而 ranta-adapter 是被 .vue 文件引用的
      // 无法命中主包的 commons 匹配规则，因此在此处手动处理
      if (module.resource && isInMainCacheGroup(module.resource)) {
        return true;
      }

      if (!/\.js/.test(module.resource)) {
        return;
      }

      if (chunkGraph.isEntryModule(module)) {
        return;
      }

      if (isInExclude(module.resource, excludeCommonGroups)) {
        return;
      }

      let allPackageDep = true;

      (module._usedModules || []).forEach((itemName) => {
        if (!itemName.startsWith('packages/')) {
          allPackageDep = false;
        }
      });

      return !allPackageDep;
    },
    minSize: 0,
    minChunks: 2,
    priority: 1,
    maxInitialRequests: Infinity,
  },

  vendors: {
    test: (module, { chunkGraph }) => {
      if (!/\.js/.test(module.resource)) {
        return;
      }

      if (chunkGraph.isEntryModule(module)) {
        return;
      }

      if (!/[\\/]node_modules[\\/]/.test(module.resource)) {
        return;
      }
      let allPackageDep = true;
      (module._usedModules || []).forEach((itemName) => {
        if (!itemName.startsWith('packages/')) {
          allPackageDep = false;
        }
      });
      return !allPackageDep;
    },
    name: 'v',
    chunks: 'all',
    maxInitialRequests: Infinity,
    priority: 2,
  },

  showcaseCommons: {
    name: 'showcase',
    test: (module, { chunkGraph }) =>
      !chunkGraph.isEntryModule(module) &&
      /\.js$/.test(module.resource) &&
      !isInExclude(module.resource, excludeShowcaseGroups) &&
      /components\/showcase/.test(module.resource),
    chunks: 'initial',
    minSize: 0,
    minChunks: 1,
    priority: 3,
  },
};

module.exports = groups;
