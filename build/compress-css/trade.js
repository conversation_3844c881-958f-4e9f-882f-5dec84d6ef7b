const TRADE_OTHER_FILES = [
  'order/list/components/list/order-item/retail-goods-card/index',
  'order/list-native/components/list/order-item/retail-goods-card/index',
  'retail-detail-order-local/SelFetchInfo',
  'retail-detail-order-local/components/LogisticsMap',
  'retail-detail-order-local/widgets/ranta-temp/RetailLogistics'
]

const TRADE_FILES = [
  // 跟最后打包的输出路径一致
  'detail-bottom-action-local/FailGoodsDialog',
  'detail-goods-local/components/DiffPrice',
  'detail-goods-local/components/FaceToFaceCourse',
  'detail-goods-local/components/GoodsMessageList',
  'detail-goods-local/components/GoodsProgress',
  'detail-goods-local/components/HotelCard',
  'detail-goods-local/components/MessagePopup',
  'detail-price-local/components/paymentInfoPopup',
  'detail-self-fetch-local/components/SelfFetch',
  'detail-shop-local/components/PriceDetail',

  // 最后打包的输出路径，需要将rt 换成 ranta-temp
  'detail-after-sale-local/ranta-temp/AfterSale',
  'detail-bottom-action-local/ranta-temp/BottomAction',
  'detail-coupon-local/ranta-temp/Coupon',
  'detail-drug-user-local/ranta-temp/Main',
  'detail-goods-local/ranta-temp/Goods',
  'detail-goods-local/widgets/ranta-temp/SellerRefundRecordPopup',
  'detail-id-card-local/ranta-temp/IdCard',
  'detail-logistics-local/ranta-temp/Logistics',
  'detail-order-status-local/ranta-temp/OrderStatus',
  'detail-order-tips-local/ranta-temp/OrderTips',
  'detail-postpone-popup-local/ranta-temp/PostponePopup',
  'detail-presale-steps-local/ranta-temp/PresaleSteps',
  'detail-price-local/ranta-temp/Price',
  'detail-receiver-info-local/ranta-temp/ReceiverInfo',
  'detail-self-fetch-scan-buy-local/ranta-temp/SelfFetchScanBuy',
  'detail-service-local/ranta-temp/Service',
  'detail-shop-local/ranta-temp/Shop',
  'detail-skeleton-local/ranta-temp/Skeleton',
  'detail-time-local/ranta-temp/Time',
  'detail-trade-share-local/ranta-temp/TradeShare',
  'detail-verify-address-local/ranta-temp/VerifyAddress',
  'detail-virtual-code-local/ranta-temp/VirtualCode',
  'detail-virtual-ticket-local/ranta-temp/VirtualTicket',
  // 'detail-welike-entry-local/ranta-temp/WelikeEntry', // 样式有问题

  ...TRADE_OTHER_FILES
]
module.exports = {
  TRADE_FILES
}