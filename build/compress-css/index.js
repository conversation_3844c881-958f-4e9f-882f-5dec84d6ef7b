const { TRADE_FILES } = require('./trade');
const { TRADE_BUY_FILES } = require('./trade-buy');
const { GOODS_FILES } = require('./goods');
const { GOODS_LIST_FILES } = require('./goods-list');
const { GOODS_MINOR } = require('./goods-minor');
const {
  DECORATE_FILES,
  DECORATE_MAIN_USER_CENTER_FILES,
  DECORATE_COMPONENTS_FILES,
} = require('./decorate');
const { FILTER_PATH } = require('./filter');
const { RETAIL_USER_CENTER_FILES } = require('./retail-user-center');
const { RETAIL_SHELF_FILES } = require('./retail-shelf');
const { FULL_PATH_MATCHING_LIST } = require('./other');
const { UMP_FILES } = require('./ump');
const { isDev } = require('@kokojs/shared');

function compressCssClassName(request) {
  if (isDev()) {
    return false;
  }
  if (
    (request.startsWith('packages/trade-buy/') &&
      TRADE_BUY_FILES.some((path) => request.includes(path))) ||
    (request.startsWith('packages/trade/') &&
      TRADE_FILES.some((path) => request.includes(path))) ||
    (request.startsWith('packages/ump/') &&
      UMP_FILES.some((path) => request.includes(path))) ||
    (request.startsWith('ext-tee-wsc-goods/extensions/') &&
      GOODS_FILES.some((path) => request.includes(path))) ||
    (request.startsWith('ext-tee-wsc-decorate/extensions/') &&
      DECORATE_FILES.some((path) => request.includes(path))) ||
    (request.startsWith('packages/goods-list/all/exensions/') &&
      GOODS_LIST_FILES.some((path) => request.includes(path))) ||
    (request.startsWith('packages/goods-minor/') &&
      GOODS_MINOR.some((path) => request.includes(path))) ||
    (request.startsWith('packages/retail/usercenter') &&
      RETAIL_USER_CENTER_FILES.some((path) => request.includes(path))) ||
    (request.startsWith('packages/retail-shelf') &&
      RETAIL_SHELF_FILES.some((path) => request.includes(path))) ||
    (request.startsWith('pages/usercenter') &&
      DECORATE_MAIN_USER_CENTER_FILES.some((path) => request.includes(path))) ||
    (request.startsWith('components/showcase') &&
      DECORATE_COMPONENTS_FILES.some((path) => request.includes(path))) ||
    FULL_PATH_MATCHING_LIST.some((path) => request.includes(path))
  ) {
    return true;
  }

  if (FILTER_PATH.some((path) => request.includes(path))) {
    return false;
  }

  const tradePackages = [
    'packages/groupbuying/',
    'packages/order-native/',
    'packages/paid/',
    'packages/paidcontent/',
    'packages/pay/',
    'packages/scan-code-buy/',
    'packages/trade-buy-subpage',
    'packages/trade-buy/',
    'packages/trade-cart/',
    'packages/trade-pay/',
    'packages/trade/',
  ];

  const goodsPackages = [
    'packages/goods-core/',
    'packages/goods-list/',
    'packages/goods-main/',
    'packages/goods-minor/',
    'packages/goods/',
  ];

  const allPackages = tradePackages.concat(goodsPackages);

  return allPackages.some((item) => request.startsWith(item));
}

module.exports = {
  compressCssClassName,
};
