const TRADE_BUY_FILES = [
  // 跟最后打包的输出路径一致
  'trade-buy-address-pre-local/components/GrouponHeaderCell',
  'trade-buy-address-pre-local/components/GrouponMemberCell',
  'trade-buy-address-pre-local/components/InfoCard',
  'trade-buy-address-pre-local/components/delivery-scope/index',
  'trade-buy-address-pre-local/components/express-way/ExpressWayCell',
  'trade-buy-address-pre-local/widgets/period-buy/PeriodBuyInfo',
  'trade-buy-address-pre-local/widgets/period-buy/PeriodBuyTime',
  'trade-buy-address-pre-local/widgets/period-buy/PeriodBuyTimePicker',
  'trade-buy-bill-core-local/widgets/QueueDialog',
  'trade-buy-goods-local/components/GiveawayCard',
  'trade-buy-goods-local/components/Good',
  'trade-buy-goods-local/components/GoodsMessage',
  'trade-buy-goods-local/components/InvalidGoodsCard',
  // 'trade-buy-goods-local/components/Present',
  'trade-buy-goods-local/components/PresentList',
  'trade-buy-goods-local/components/StoreGifts',
  'trade-buy-header-container-local/components/head-prompt/index',
  'trade-buy-misc-pre-local/widgets/order-keep-navigator/TradeCarousel',
  'trade-buy-service-block-local/invoice/Invoice',
  'trade-buy-service-block-local/invoice/form/category',
  'trade-buy-service-block-local/invoice/form/content',
  'trade-buy-service-block-local/invoice/form/corp-suggestion',
  'trade-buy-service-block-local/invoice/form/goods-tips',
  'trade-buy-service-block-local/invoice/form/index',
  'trade-buy-service-block-local/invoice/form/message',
  'trade-buy-service-block-local/invoice/form/wx-invoice-title',
  'trade-buy-service-block-local/invoice/goods-info/index',
  'trade-buy-submit-local/ShopStatus',
  'trade-buy-submit-local/components/SubmitContent',
  'trade-buy-ump-block-local/BlockContainer',
  'trade-buy-ump-block-local/components/activity/ActivityCell',
  'trade-buy-ump-block-local/components/amount/AmountBlock',
  'trade-buy-ump-block-local/components/display-card/PaidCard',
  'trade-buy-ump-block-local/components/display-card/index',
  'trade-buy-ump-block-local/components/membership/Checkbox',
  'trade-buy-ump-block-local/components/membership/MembershipDialog',
  'trade-buy-ump-block-local/components/reward/RewardBlock',
  'trade-buy-ump-block-local/widgets/coupon-list/ActionSheet',
  
  // 最后打包的输出路径，需要将rt 换成 ranta-temp
  // 'trade-buy-address-pre-local/widgets/address-card/ranta-temp/index', // 编译后样式有问题
  // 'trade-buy-address-pre-local/widgets/address-tab/ranta-temp/index', // 编译后样式有问题
  // 'trade-buy-ump-block-local/widgets/plus-buy-card/ranta-temp/index', // 注释原因：走压缩的话体积增多
  // 'trade-buy-address-pre-local/widgets/self-fetch/DistanceIcon', // 注释原因：走压缩的话体积增多
  'cart-present-popup-local/ranta-temp/PresentPopup',
  'retail-order-error-info-local/ranta-temp/Main',
  'retail-order-local/widgets/ranta-temp/Dine',
  'trade-buy-address-pre-local/ranta-temp/DeliveryBlock',
  'trade-buy-address-pre-local/widgets/address-list/ranta-temp/index',
  'trade-buy-address-pre-local/widgets/address-list-item/ranta-temp/index',
  'trade-buy-address-pre-local/widgets/contact-list/ranta-temp/index',
  'trade-buy-address-pre-local/widgets/contact-main/ranta-temp/index',
  'trade-buy-address-pre-local/widgets/idcard-popup/ranta-temp/index',
  'trade-buy-address-pre-local/widgets/self-fetch/ranta-temp/index',
  'trade-buy-address-pre-local/widgets/time-picker/ranta-temp/index',
  'trade-buy-address-pre-local/widgets/wechat-address/ranta-temp/index',
  'trade-buy-cashier-pre-local/widgets/cashier-pre/ranta-temp/index',
  'trade-buy-drug-local/ranta-temp/Main',
  'trade-buy-goods-local/ranta-temp/ExtraFees',
  'trade-buy-goods-local/ranta-temp/Presale',
  'trade-buy-goods-local/ranta-temp/StoreGiftsList',
  'trade-buy-goods-local/ranta-temp/UnavailableGoods',
  'trade-buy-goods-local/widgets/ranta-temp/Empty',
  'trade-buy-header-container-local/widgets/guide/ranta-temp/index',
  'trade-buy-misc-local/widgets/haitao-footer-notice/ranta-temp/index',
  'trade-buy-misc-pre-local/widgets/order-keep-navigator/ranta-temp/index',
  'trade-buy-misc-pre-local/widgets/present-goods/ranta-temp/index',
  'trade-buy-misc-pre-local/widgets/price/ranta-temp/index',
  'trade-buy-price-local/widgets/presale/ranta-temp/index',
  'trade-buy-prior-use-local/ranta-temp/Main',
  'trade-buy-reward-popup-local/ranta-temp/Main',
  'trade-buy-service-block-local/ranta-temp/Main',
  'trade-buy-submit-local/ranta-temp/Main',
  'trade-buy-ump-block-local/ranta-temp/CouponListPopupBottom',
  'trade-buy-ump-block-local/widgets/activity-dialog/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/base-block/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/coupon-cell/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/coupon-container/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/coupon-item/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/coupon-list/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/extra-fees/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/fans-dialog/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/membership-cell/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/plus-buy/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/plus-buy-swipe/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/point-cell/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/point-deduction-cell/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/postagetool/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/postagetool-card/ranta-temp/index',
  'trade-buy-ump-block-local/widgets/postagetool-tip/ranta-temp/index',
  'trade-buy-goods-local/ranta-temp/Main',
  'trade-buy-ump-block-local/widgets/plus-buy-swipe-item/ranta-temp/index'
];

module.exports = {
  TRADE_BUY_FILES
}