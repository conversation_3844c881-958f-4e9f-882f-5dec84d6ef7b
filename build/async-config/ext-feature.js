const IS_YOUZANYUN = process.env.BUILD_ENV === 'youzanyun';

// 首页中台化后逐步放到 async-main 中
const coreComs = [
  'components/showcase/components/share-feature', //
  'components/showcase/components/floating-nav/components/dine-entry',
  // '@vant/weapp/dist/icon',
  // '@vant/weapp/dist/loading',
  // '@vant/weapp/dist/overlay',
  'shared/components/showcase/extension/', // 有赞云定制微页面组件
];

// 在有赞云下，biz-sku-manage因为是sku开放组件会被打进主包(保证加载时序)，此时不需要将biz-sku-manage打进分包
// 在标品下，biz-sku-manage还是打进分包中
const BIZ_SKU_MANAGE_EXT = IS_YOUZANYUN
  ? []
  : ['@ext-tee-wsc-goods/biz-sku-manage'];

const EXTS = [
  '@wsc-tee-salesman/salesman-cube-block',
  '@wsc-tee-salesman/salesman-share-popup',
  '@wsc-tee-decorate/floating-nav',
  ...BIZ_SKU_MANAGE_EXT,
  '@wsc-tee-decorate/live-pop',
  '@wsc-tee-decorate/subscribe-message',
  '@wsc-tee-decorate/jump-link',
  '@wsc-tee-statcenter/goods-showcase-block',
  '@ext-tee-wsc-im/subscription-message',
  '@ext-tee-wsc-im/im-message-contact',
  '@wsc-tee-shop/footer',
  '@wsc-tee-decorate/feature-popup-container',
  '@wsc-tee-decorate/cloud-open-process',
  '@wsc-tee-decorate/feature-user-authorize-popup',
  '@wsc-tee-decorate/goods-prefetch',
  '@ext-tee-wsc-goods/independent-sku',
  '@ext-tee-wsc-goods/follow-popup-block',
];

module.exports = {
  name: 'packages/async-feature-ext',
  priority: 20,
  exts: EXTS,
  test({ request }) {
    return coreComs.some((com) => request.includes(com));
  },
};
