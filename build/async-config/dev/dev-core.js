const { isRetailBiz, isRetailKQ } = require('../../script/biz/retail');

const isRetail = isRetailBiz;

const coreComsPrefix = '@youzan/decorate-tee/src';

// 零售烘焙版首页
const lsBakeComsBlacklist = [
  // `${coreComsPrefix}/decorate/shelf-customer-asset`,
  // `${coreComsPrefix}/decorate/shelf-order-pool`,
  // `${coreComsPrefix}/decorate/shelf-member-card`,
  // `${coreComsPrefix}/decorate/shelf-banner`,
  // `${coreComsPrefix}/decorate/shelf-entry`,
  // `${coreComsPrefix}/decorate/member-value`,
];
const coreComs = [
  coreComsPrefix,
  'components/showcase/components/navigation-bar/nav-bar-open-panel',
  // '@youzan/decorate-tee/src/captain/item-card',
  // '@youzan/decorate-tee/src/captain/layout',
  // '@youzan/decorate-tee/src/captain/sold-num',
  // '@youzan/decorate-tee/src/captain/title',
  // '@youzan/decorate-tee/src/captain/countdown',
];

// 下面两个前缀在黑名单里,但要放到core里面
const coreListWithBlackPrefix = [
  '@youzan/decorate-tee/src/decorate/goods/component/coupon',
  '@youzan/decorate-tee/src/decorate/goods/component/note',
  '@youzan/decorate-tee/src/decorate/goods/component/extra', // 仅在为你推荐组件中出现，主要是商品中掺杂优惠券与店铺笔记，子组件已经在异步分包中，这个组件可以放入异步分包
  '@youzan/decorate-tee/src/captain/item-card/components/coupon-style',
  '@youzan/decorate-tee/src/captain/countdown', // 倒计时组件，尝试放入分包
  '@youzan/decorate-tee/src/captain/captain/module-title', // 倒计时组件，尝试放入分包
  '@youzan/decorate-tee/src/captain/item-card/components/progress-info/index',
  '@youzan/decorate-tee/src/decorate/goods-tags-left/components/empty-block', // 旧版左侧商品分组
  '@youzan/decorate-tee/src/decorate/goods-tags-left/components/screen-slide', // 旧版左侧商品分组
  '@youzan/decorate-tee/src/decorate/goods-tags-left/components/tag-group', // 旧版左侧商品分组
  '@youzan/decorate-tee/src/decorate/fans/components/new-show-qrcode', // 新版的涨粉组件-展示二维码
  '@youzan/decorate-tee/src/decorate/fans/components/new-hidden-qrcode', // 新版的涨粉组件-不展示二维码
];

// 零售客群小程序主页组件
const coreRetailKqComsBlacklist = [
  `${coreComsPrefix}/decorate/image`,
  `${coreComsPrefix}/decorate/line`,
  `${coreComsPrefix}/decorate/text-nav`,
  `${coreComsPrefix}/decorate/title-text`,
  `${coreComsPrefix}/captain/title/index/index`,
  `${coreComsPrefix}/decorate/shelf-customer-asset`,
  `${coreComsPrefix}/decorate/shelf-order-pool`,
  `${coreComsPrefix}/decorate/shelf-member-card`,
  `${coreComsPrefix}/decorate/shelf-banner`,
  `${coreComsPrefix}/decorate/shelf-entry`,
  `${coreComsPrefix}/decorate/member-value`,
  `${coreComsPrefix}/decorate/component-placeholder`,
  `${coreComsPrefix}/captain/item-card`,
  `${coreComsPrefix}/captain/layout`,
  `${coreComsPrefix}/captain/load-mode`,
  '@youzan/decorate-tee/src/captain/title',
  `${coreComsPrefix}/decorate/goods`,
  '@youzan/decorate-tee/src/captain/skeleton',
];

const coreComsBlacklist = isRetailKQ
  ? coreRetailKqComsBlacklist
  : [
      `${coreComsPrefix}/decorate/component-placeholder`,
      `${coreComsPrefix}/decorate/image`,
      `${coreComsPrefix}/decorate/search`,
      `${coreComsPrefix}/decorate/goods`,
      `${coreComsPrefix}/decorate/cube`,
      '@youzan/decorate-tee/src/captain/skeleton',
      // skyline 临时加入主包
      // `${coreComsPrefix}/decorate/shop`,
      // `${coreComsPrefix}/decorate/text-nav`, //  B 端微页面已经无法创建这个组件了，移出主包
      // `${coreComsPrefix}/decorate/unicashier`,
      // `${coreComsPrefix}/decorate/coupon`,
      // `${coreComsPrefix}/decorate/recommend`,
      // `${coreComsPrefix}/decorate/enter-shop`,
      // `${coreComsPrefix}/decorate/audio`,
      // `${coreComsPrefix}/decorate/store`,
      // `${coreComsPrefix}/decorate/video`,
      // skyline 临时加入主包

      `${coreComsPrefix}/decorate/notice`,
      `${coreComsPrefix}/decorate/line`,
      `${coreComsPrefix}/decorate/title-text`,
      `${coreComsPrefix}/decorate/fans`,
      ...(isRetail ? [] : [`${coreComsPrefix}/decorate/rich-text`]), // 零售主包先移除富文本
      `${coreComsPrefix}/captain/item-card`,
      `${coreComsPrefix}/captain/title/index/index`,
      `${coreComsPrefix}/captain/countdown/index/index`,
      `${coreComsPrefix}/captain/sold-num/index/index`,
      `${coreComsPrefix}/captain/layout`,
      `${coreComsPrefix}/captain/load-mode`,
      '@youzan/decorate-tee/src/captain/item-card',
      '@youzan/decorate-tee/src/captain/layout',
      '@youzan/decorate-tee/src/captain/title',
      ...(isRetail ? lsBakeComsBlacklist : []),
    ];

module.exports = {
  name: 'packages/async-core',
  priority: 20,
  test({ request }) {
    if (coreListWithBlackPrefix.some((com) => request.includes(com))) {
      return true;
    }
    if (coreComsBlacklist.some((com) => request.includes(com))) {
      return false;
    }
    return coreComs.some((com) => request.includes(com));
  },
};
