const minorComs = [
  'shared/common/components/theme-view/index',
  'shared/common/components/form-view/index',
  'shared/common/components/base-sku/index',
  'shared/components/showcase/category',
  'shared/components/showcase/double-eleven',
  'components/cps-goods-recommend/index',
  'pages/common/page-container/components/copyright/index',
  'shared/components/cart/custom-sku-header-price/index',
  'components/showcase/components/salesman-cube/index',
  'shared/components/account/protocol/ecloud-custom/index',
  'components/showcase/components/platform-coupon-pop/index',
  'components/collect-tip/index',
  'components/ump/collect-gift/entry-in-homepage/index',
  'components/scene-market-subscribe', // CRM 场景营销弹窗
  'components/shop-pop-manager/index',
  'components/bi-list-icon/index',
  'components/ump/marketing-page/icon/index',
  'components/showcase/components/timeline-share/index',
  'shared/components/showcase/category/index',
  'shared/components/showcase/double-eleven/index',
];
const minorComsBlacklist = [
  'chain-store/components/navigator-bar',
  'components/message/contact/index',
];

module.exports = {
  name: 'packages/async-minor',
  priority: 20,
  test({ request }) {
    if (minorComsBlacklist.some((com) => request.includes(com))) {
      return false;
    }
    return minorComs.some((com) => request.includes(com));
  },
};
