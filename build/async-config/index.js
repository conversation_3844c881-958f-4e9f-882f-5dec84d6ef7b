const isDev = process.env.NODE_ENV === 'development';

const core = require('./core');
const main = require('./main');
const minor = require('./minor');
const goods = require('./goods');
const wscShaking = require('./wsc-shaking');
const dev = require('./dev/dev');
const devFeature = require('./dev/dev-feature');
const devMirror = require('./dev/dev-mirror');
const devMain = require('./dev/dev-main');
const devCore = require('./dev/dev-core');
const devTee = require('./dev/dev-tee');
const cloud = require('./cloud/index');
const tee = require('./tee');
const drop = require('../script/drop-check-npm');
const usercenter = require('./usecenter');
const retail = require('./retail');
// const chain = require('./chain');
// const dcNative = require('./dc-native');
const extFeature = require('./ext-feature');
const { isRetailBiz } = require('../script/biz/retail');

const isRetail = isRetailBiz;

function patchBuild(build) {
  for (const item of build) {
    if (item.exts) {
      const oldTest = item.test;
      /** tee 中仅能处理 test */
      item.test = ({ request }) =>
        (oldTest && oldTest({ request })) || request.includes(item.name);
    }
  }
  return build;
}
// 用于build阶段的分包配置以及dev阶段的语法检测
// 不允许修改当前配置写法
// 允许新增配置和删除配置
const build = patchBuild([
  core,
  main,
  minor,
  tee,
  ...retail,
  ...goods,
  cloud,
  extFeature,
  ...(isRetail ? [] : [wscShaking]),
  usercenter,
]);

module.exports = {
  asyncPackageCheck: {
    rules: build, // 该配置项需要和asyncPackageRules在build阶段配置保持一致
    drop,
    // 跳过检测命中的配置项
    exclude: (_resourcePath, request) => {
      // 暂时关闭 错误使用case过多 需要治理
      return true;
    },
  },
  asyncPackageRules: isDev
    ? [dev, devMain, devFeature, devMirror, ...retail, devCore, devTee]
    : build,
};
