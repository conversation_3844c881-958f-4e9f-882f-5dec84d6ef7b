const comps = [
  // 装修组件
  'shared/components/showcase/goods',
  'shared/components/showcase/link',
  'shared/components/showcase/coupon',
  'shared/components/showcase/cube',
  'shared/components/showcase/line',
  'shared/components/showcase/notice',
  'shared/components/showcase/points-goods',
  'shared/components/showcase/rich-text',
  'shared/components/showcase/contact-us',
  'shared/components/showcase/store',
  'shared/components/showcase/title-text',
  'shared/components/showcase/title',
  'shared/components/showcase/text',
  'shared/components/showcase/image',
  'shared/components/showcase/video-plugin',
  'shared/components/showcase/white',
  'shared/components/showcase/search',
  'shared/components/showcase/new-tag-list-left',
  'shared/components/showcase/tag-list-top',
  'shared/components/showcase/ump-seckill',
  'shared/components/showcase/ump-bargain',
  'shared/components/showcase/offline-shop-info',
  'shared/components/showcase/storeinfo',
  'shared/components/showcase/note-card/index',
  'shared/components/showcase/oriented-poster/index',
  'shared/components/showcase/unicashier',
  'shared/components/showcase/goods-recommend',
  'shared/components/showcase/social-fans',
  'shared/components/showcase/audio',
  'shared/components/showcase/course',
  'shared/components/showcase/course-group',
  'shared/components/showcase/content',
  'shared/components/showcase/column',
  'shared/components/showcase/live',
  'shared/components/showcase/member',
  'shared/components/showcase/member-goods',
  'shared/components/showcase/ump-groupon',
  'shared/components/showcase/ump-timelimited-discount',
  'shared/components/showcase/guang-live',
  'shared/components/showcase/period-buy',
  'shared/components/showcase/wxvideo-live/index',

  // 精选商品组件
  'shared/components/recommend-goods/index',
];

module.exports = {
  name: 'packages/async-usecenter',
  test({ request }) {
    return comps.some((com) => request.includes(com));
  },
};
