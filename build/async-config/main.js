const mainComs = [
  '@youzan/zan-pay-weapp',
  '@youzan/zan-pay-core',
  '@youzan/behavior-verify',
  '@youzan/passport-tee-behavior',
  '@youzan/zan-pay-weapp/dist/index',
  '@vant/weapp/dist/toast',
  '@vant/weapp/dist/notify',
  '@vant/weapp/dist/divider',
  '@vant/weapp/dist/empty',
  // '@vant/weapp/dist/popup',
  '@vant/weapp/dist/goods-action',
  // '@vant/weapp/dist/field',
  '@vant/weapp/dist/notice-bar',
  'vant-weapp/dist/tab',
  'vant-weapp/dist/tabs',
  'shared/components/account/wx-login', //
  'shared/components/account/wx-login-button', //
  '@vant/weapp/dist/checkbox',
  '@vant/weapp/dist/dialog',
  'shared/components/account/inject-protocol',
  '@vant/weapp/dist/icon',
  '@vant/weapp/dist/loading',
  '@vant/weapp/dist/overlay',
  // '@vant/weapp/dist/cell',
  // '@youzan-open/vant-tee/dist/area',
  // '@youzan-open/vant-tee/dist/notice-bar',
  // '@youzan-open/vant-tee/dist/picker',
  // '@youzan-open/vant-tee/dist/datetime-picker',
  // '@youzan-open/vant-tee/dist/loading',
  // '@youzan-open/vant-tee/dist/toast',
  // '@youzan-open/vant-tee/dist/overlay',
  // '@youzan-open/vant-tee/dist/skeleton',
  // '@youzan-open/vant-tee/dist/field',
  // // '@youzan-open/vant-tee/dist/cell',
  // // '@youzan-open/vant-tee/dist/icon',
  // '@youzan-open/vant-tee/dist/checkbox',
  // // '@youzan-open/vant-tee/dist/cell-group',
  // '@youzan-open/vant-tee/native/weapp/notice-bar',
  'page-container/components/copyright/index',
  'page-container/components/shop/components/shop-status',
  'page-container/components/shop/components/service-due',
  'shared/components/account/sms-login', //
  'shared/components/account/password-login', //
  'shared/components/account/login', //
  // '@youzan/behavior-verify',// 同步脚本调用（selectComponent）了异步组件的UI
  'shared/components/account/protocol-field', //
  'shared/components/account/ecloud',
  '@youzan/sales-icon-utils',
  'components/showcase/components/salesman-cube', //
  'components/showcase/components/floating-nav', //
  'components/showcase/components/video-ad-link', //
  'shared/components/feature-sku', //
  'components/live-pop', //
  'shared/common/components/form-view',
  'components/message/contact/index',
  'shared/components/message/ecloud-contact/index',
  'shared/common/components/base-sku', //
  '@youzan/crypto', //
  '@youzan/hummer-tee',
  'pages/home/<USER>/resourceLoad',
  '@youzan/wsc-tee-trade-common/lib/self-fetch/default',
  '@youzan/wsc-tee-trade-common/lib/biz/trade-buy-prerender/set-cache',
  // 'components/showcase/index',
  // 'components/showcase/topNav',
  // 'shared/components/showcase-options-home',
  // 'utils/subscribe-message/helper', // requestSubscribeMessage:fail can only be invoked by user TAP gesture.

  '@youzan/tee-multi-app-shared/components/PageInfoHelper',
  // 账号-协议授权
  '@youzan/passport-protocol',
  'shared/components/account/protocol',

  // 新版缩减体积
  '@vant/weapp/dist/field',
  '@vant/weapp/dist/button',
  '@vant/weapp/dist/action-sheet',
  '@vant/weapp/dist/cell',
  '@vant/weapp/dist/radio',
  '@vant/weapp/dist/tag',
  '@vant/weapp/dist/search',
  '@vant/weapp/dist/switch',
  '@vant/weapp/dist/sticky',
  '@vant/weapp/dist/count-down',
  // '@vant/weapp/dist/tab', // 需要确认
  // '@vant/weapp/dist/tabs', // 需要确认
  '@vant/weapp/dist/cell-group',
  '@vant/weapp/dist/datetime-picker',
  '@vant/weapp/dist/stepper',
  // '@vant/weapp/dist/picker',
  '@vant/weapp/dist/share-sheet',
  '@vant/weapp/dist/panel',
  '@vant/weapp/dist/row',
  '@vant/weapp/dist/col',
  '@vant/weapp/dist/image',
  '@vant/weapp/dist/info',
  // '@vant/weapp/dist/picker-column',
  // 新版缩减体积
  'shared/utils/sku/index',
  'shared/components/recommend',
  'shared/components/recommend-goods',
  'components/showcase/captain-components/search',
  'components/showcase/components/live-dynamic-effect',
  'shared/components/showcase/select-address',
  'shared/components/ump/coupon/actions/stamp',
  'shared/components/ump/coupon/actions/checkbox',
  'shared/components/ump/coupon/actions/status',
  '@youzan/tee-user-components/src/live-qrcode-add-fans',
  'packages/groupbuying/components/area-popup',
  'packages/order-native/address-edit/components/select-area',
];
const mainComsBlacklist = [
  '@youzan-open/vant-tee/dist/list',
  // '@vant/weapp/dist/field',
  '@vant/weapp/dist/common',
  'components/live-popup',
  'components/showcase/components/navigation-bar/nav-bar-open-panel',
  'components/showcase/components/floating-nav/components/dine-entry',
  // '@youzan/passport-tee-components/native/weapp/components/user-authorize/index/index',
  // '@youzan/passport-tee-components/native/weapp/components/user-authorize/components',
  // 'shared/components/showcase/captain-components/navigator/index',
];

module.exports = {
  name: 'packages/async-main',
  priority: 10,
  test({ request }) {
    if (mainComsBlacklist.some((com) => request.includes(com))) {
      return false;
    }
    return mainComs.some((com) => request.includes(com));
  },
};
