const teeComs = [
  /* -------dist目录-------- */
  '@youzan-open/vant-tee/dist/area',
  '@youzan-open/vant-tee/dist/notice-bar',
  '@youzan-open/vant-tee/dist/picker',
  '@youzan-open/vant-tee/dist/datetime-picker',
  '@youzan-open/vant-tee/dist/loading',
  '@youzan-open/vant-tee/dist/toast',
  '@youzan-open/vant-tee/dist/calendar',
  '@youzan-open/vant-tee/dist/action-sheet',
  '@youzan-open/vant-tee/dist/swipe',
  '@youzan-open/vant-tee/dist/swipe-item',
  '@youzan-open/vant-tee/dist/badge',
  '@youzan-open/vant-tee/dist/radio',
  '@youzan-open/vant-tee/dist/divider',
  '@youzan-open/vant-tee/dist/empty',
  '@youzan-open/vant-tee/dist/info',
  // '@youzan-open/vant-tee/dist/overlay',
  '@youzan-open/vant-tee/dist/skeleton',
  '@youzan-open/vant-tee/dist/field',
  // '@youzan-open/vant-tee/dist/cell',
  '@youzan-open/vant-tee/dist/icon',
  '@youzan-open/vant-tee/dist/checkbox',
  // '@youzan-open/vant-tee/dist/cell-group',
  // 体积优化二期
  '@youzan-open/vant-tee/dist/dialog',
  '@youzan-open/vant-tee/dist/stepper',
  '@youzan-open/vant-tee/dist/progress',
  '@youzan-open/vant-tee/dist/slider',
  '@youzan-open/vant-tee/dist/tag',
  '@youzan-open/vant-tee/dist/uploader',
  // '@youzan-open/vant-tee/dist/action-sheet',
  '@youzan-open/vant-tee/dist/text-omitted',

  '@youzan-open/vant-tee/dist/search',
  '@youzan-open/vant-tee/dist/switch',
  '@youzan-open/vant-tee/dist/share-sheet',
  '@youzan-open/vant-tee/dist/swipe-cell',
  '@youzan-open/vant-tee/dist/steps',
  '@youzan-open/vant-tee/dist/dropdown-menu',
  '@youzan-open/vant-tee/dist/dropdown-item',

  // 账号
  '@youzan/passport-tee/', // 加 '/' 避免包含其他的
  '@youzan/passport-tee-api',
  '@youzan/passport-tee-behavior',
  '@youzan/passport-tee-components',
  'components/user-authorize/authorize/log', // @youzan/passport-tee-components/lib/components/user-authorize/authorize/log.js
  'ext-tee-passport/extensions/user-authorize/components/UserAuthorizePopup',
  'ext-tee-passport/extensions/user-authorize/widgets/ranta-temp/Popup',
  'ext-tee-passport/extensions/user-authorize/widgets/ranta-temp/CloudUserAuthorize',
  'ext-tee-passport/extensions/protocol/widgets/ranta-temp/ProtocolPopup',
  'ext-tee-passport/extensions/protocol/widgets/ranta-temp/ProtocolContent',
  'ext-tee-passport/extensions/protocol/widgets/ranta-temp/CloudProtocolContent',
  'ext-tee-passport/extensions/protocol/widgets/ranta-temp/InjectProtocol',

  /*
    -------native组件--------
    skyline 项目，从@vant/weapp 替换到 vant-tee/native 的组件
   */
  '@youzan-open/vant-tee/native/weapp/notice-bar', // 历史
  '@youzan-open/vant-tee/native/weapp/icon',
  '@youzan-open/vant-tee/native/weapp/tag',
  '@youzan-open/vant-tee/native/weapp/tab',
  '@youzan-open/vant-tee/native/weapp/tabs',
  '@youzan-open/vant-tee/native/weapp/search',
  '@youzan-open/vant-tee/native/weapp/sticky',
  '@youzan-open/vant-tee/native/weapp/count-down',
  '@youzan-open/vant-tee/native/weapp/dropdown-menu',
  '@youzan-open/vant-tee/native/weapp/dropdown-item',
  // 体积优化
  // '@youzan-open/vant-tee/dist/sticky',
  '@youzan-open/vant-tee/dist/count-down',
  '@youzan-open/vant-tee/dist/cell-group',
  '@youzan-open/vant-tee/dist/transition',

  // 确认这两个是否能改掉
  // '@youzan-open/vant-tee/dist/cell',
  //   ext-tee-wsc-goods/extensions/group-block/widgets/select-sku-bar/rt/index.json:
  //   1: {"component":true,"usingComponents":{"a":"/@y-open/vt/dist/cell/index","b":"/packages/goods-core/@y/gc/components/group-block/price-calendar-bar/index"},"componentPlaceholder":{"b":"_"}}
  // ext-tee-wsc-goods/extensions/group-block/widgets/service-bar/rt/index.json:
  //   1: {"component":true,"usingComponents":{"a":"/packages/async-tee/@y-open/vt/native/weapp/icon/index/index","b":"/@y-open/vt/dist/cell/index","c":"/@y-open/vt/dist/button/index.weapp","d":"/@y-open/vt/dist/popup/index.weapp"},"componentPlaceholder":{"a":"_"}}
];

/**
 * 不进异步分包的路径（即主包）
 */
const teeComsBlacklist = [
  '@youzan-open/vant-tee/dist/swipe-cell',

  '@youzan/passport-tee-shared',

  // 授权组件-多端
  '@youzan/passport-tee/lib/user-authorize/index',
  '@youzan/passport-tee-components/lib/components/user-authorize/index',
  '@youzan/passport-tee-components/lib/components/user-authorize/components/trigger-button/index',
];

// 确保异步分包里，优先级更高（可能被黑名单包含）
const teeAsyncComs = ['@youzan/passport-tee-api'];

module.exports = {
  name: 'packages/async-tee',
  test({ request }) {
    // 指定异步分包的必须在异步分包
    if (teeAsyncComs.some((com) => request.includes(com))) {
      return true;
    }
    // 不在确保异步分包里的，判断黑名单
    if (teeComsBlacklist.some((com) => request.includes(com))) {
      return false;
    }
    return teeComs.some((com) => request.includes(com));
  },
};
