const path = require('path');
const { exts: featureExts } = require('./ext-feature');

const resolveSrc = (srcPath) => {
  return require(path.resolve(__dirname, '../../src', srcPath));
};

// 分包名
const PackagesName = {
  CORE: 'goods-core',
  MAIN: 'goods-main',
  MINOR: 'goods-minor',
};

// 指定以下 ext 不被打进商详分包
const excludeExts = [
  '@ext-tee-wsc-goods/trade-submit-block',
  '@wsc-tee-decorate/showcase-container',
  '@ext-tee-wsc-goods/biz-sku-manage',
  '@ext-tee-wsc-decorate/page-style',
  ...featureExts,
];

function filterExcludeExts(exts) {
  return exts.filter((ext) => {
    return !excludeExts.includes(ext);
  });
}

// 处理商详 npm 包
const npmPackageName = '@youzan/wsc-tee-goods-common/';

// 分包配置
const packagesExtMap = {
  [PackagesName.CORE]: [
    '@ext-tee-wsc-goods/share-block',
    '@ext-tee-wsc-goods/goods-review-block',
    '@ext-tee-wsc-goods/trade-records-block',
    '@ext-tee-wsc-goods/shop-block',
  ],
  [PackagesName.MAIN]: [
    '@ext-tee-wsc-goods/preview-image-block',
    '@ext-tee-wsc-goods/promotion-block',
    '@ext-tee-wsc-goods/goods-extra-block',
    '@ext-tee-wsc-goods/goods-ranking-block',
    '@ext-tee-wsc-goods/multi-store-block',
    '@ext-tee-wsc-goods/buyer-show-block',
    '@ext-tee-wsc-goods/combine-api-block',
    '@ext-tee-wsc-goods/shop-note-block',
    '@ext-tee-wsc-goods/shop-physical-block',
    '@ext-tee-wsc-goods/recommend-plugin-block',
    '@ext-tee-wsc-goods/recommend-block',
    '@assets-tee-extensions/guarantee-components',
    '@assets-tee-extensions/sku-pay-ways',
    '@wsc-tee-shop/shop-top-bar',
    '@wsc-tee-statcenter/cps-recommend-goods',
    '@wsc-tee-statcenter/recommend-goods',
    '@wsc-tee-shop/shop-rest',
    '@ext-tee-wsc-im/im-message-contact',
    '@ext-tee-assets/prior-use-block',
    '@ext-tee-wsc-goods/combine-goods-block',
    '@ext-tee-wsc-im/subscription-message',
    '@ext-tee-wsc-goods/shop-evaluation-entry',
    '@wsc-tee-trade/trade-buy-id-card-popup',
  ],
  [PackagesName.MINOR]: [
    '@wsc-tee-salesman/salesman-cube-block',
    '@ext-tee-wsc-goods/goods-detail-block',
    '@wsc-tee-shop/footer',
    '@ext-tee-wsc-goods/trade-submit-block',
    '@ext-tee-wsc-goods/trade-carousel',
    '@ext-tee-wsc-goods/nav-bar-block',
    '@ext-tee-wsc-im/im-sdk-core',
    '@wsc-tee-decorate/floating-nav',
    '@ext-tee-wsc-goods/biz-sku-manage',
    '@wsc-tee-salesman/salesman-recruit-goods-block',
    '@ext-tee-wsc-goods/timeline-block',
    '@ext-tee-wsc-im/im-layer',
    '@wsc-tee-trade/order-pay-prompt-popup',
    '@ext-tee-wsc-goods/sold-out-rec-shop-block',
    '@ext-tee-wsc-goods/popup-container',
    '@wsc-tee-decorate/live-pop',
    '@ext-tee-wsc-goods/sku-order-core',
    '@ext-tee-wsc-ump/trade-buy-ump-data',
    '@assets-tee-extensions/cashier-pre',
    '@wsc-tee-decorate/subscribe-message',
    '@ext-tee-shop/shop-cert-notice',
    '@wsc-tee-trade/trade-buy-misc-pre',
    '@ext-tee-wsc-goods/goods-explain-video',
    '@ext-tee-wsc-goods/goods-ecloud',
    '@ext-tee-wsc-goods/cloud-item-detail-data',
    '@wsc-tee-decorate/showcase-container',
    '@wsc-tee-decorate/jump-link',
    '@ext-tee-user/fast-join-sdk',
    'trade-buy-privacy-bill',
    '@ext-tee-wsc-goods/optional-goods-block',
    '@wsc-tee-trade/trade-buy-core',
    '@wsc-tee-trade/trade-buy-ump-block',
    '@assets-tee-extensions/cashier',
    '@retail-tee-prepaid/prepaid',
    '@wsc-tee-trade/trade-buy-pay-view',
    '@wsc-tee-trade/trade-buy-service-block',
    '@wsc-tee-trade/trade-buy-self-fetch-address-city',
    '@wsc-tee-trade/trade-buy-common-popup',
    '@wsc-tee-trade/trade-buy-self-fetch-address',
    '@wsc-tee-trade/trade-buy-platform-address',
    '@wsc-tee-statcenter/goods-showcase-block',
    '@wsc-tee-trade/trade-buy-id-card',

    '@wsc-tee-trade/retail-order-error-info',
    '@wsc-tee-trade/retail-order',
    '@ext-tee-wsc-goods/sku-order-popup',
  ],
};

packagesExtMap[PackagesName.CORE] = filterExcludeExts(
  packagesExtMap[PackagesName.CORE]
);
packagesExtMap[PackagesName.MAIN] = filterExcludeExts(
  packagesExtMap[PackagesName.MAIN]
);
packagesExtMap[PackagesName.MINOR] = filterExcludeExts(
  packagesExtMap[PackagesName.MINOR]
);

// 组件黑名单: 放主包
const ignoredComponent = [`${npmPackageName}components/sku/index`, `${npmPackageName}components/image-block/goods-video`];

const packagesNpmMap = {
  [PackagesName.CORE]: [npmPackageName],
  [PackagesName.MAIN]: [npmPackageName],
  [PackagesName.MINOR]: [npmPackageName, '@youzan/tee-user-components'],
};

// NOTE: 如果增加或修改 name 路径，记得同步修改 app.json/preloadRule 中的配置
const configs = Object.entries(packagesExtMap).map(([name, exts]) => ({
  name: `packages/${name}`,
  deps: ['@youzan/tee-user-components'],
  exts,
  test({ request }) {
    if (ignoredComponent.some(ignorePath => request.includes(ignorePath))) {
      return false;
    }
    return packagesNpmMap[name].some((pkg) => request.includes(pkg));
  },
  includeExtInBiz: {
    // 该配置的含义是当前分包中的 trade-buy-ump-data 只对后面指定的biz生效, 未指定的biz按自己的逻辑打包, 实际是为了排除下单
    '@ext-tee-wsc-ump/trade-buy-ump-data': [
      '@wsc-tee-weapp-goods/goods-detail',
      '@wsc-tee-weapp/feature',
      '@wsc-tee-weapp/feature-home',
      '@ext-tee-retail-shelf/retail-shelf',
      '@ext-tee-retail-shelf/retail-shelf-index',
    ],
    '@wsc-tee-trade/trade-buy-core': [
      '@wsc-tee-weapp-goods/goods-detail',
      '@wsc-tee-weapp/feature',
      '@wsc-tee-weapp/feature-home',
      '@ext-tee-retail-shelf/retail-shelf',
      '@ext-tee-retail-shelf/retail-shelf-index',
    ],
    '@wsc-tee-trade/trade-buy-ump-block': [
      '@wsc-tee-weapp-goods/goods-detail',
      '@wsc-tee-weapp/feature',
      '@wsc-tee-weapp/feature-home',
      '@ext-tee-retail-shelf/retail-shelf',
      '@ext-tee-retail-shelf/retail-shelf-index',
    ],
    '@wsc-tee-trade/trade-buy-service-block': [
      '@wsc-tee-weapp-goods/goods-detail',
      '@wsc-tee-weapp/feature',
      '@wsc-tee-weapp/feature-home',
      '@ext-tee-retail-shelf/retail-shelf',
      '@ext-tee-retail-shelf/retail-shelf-index',
    ],
    '@wsc-tee-trade/retail-order-error-info': [
      '@wsc-tee-weapp-goods/goods-detail',
      '@wsc-tee-weapp/feature',
      '@wsc-tee-weapp/feature-home',
      '@ext-tee-retail-shelf/retail-shelf',
      '@ext-tee-retail-shelf/retail-shelf-index',
    ],
    '@wsc-tee-trade/retail-order': [
      '@wsc-tee-weapp-goods/goods-detail',
      '@wsc-tee-weapp/feature',
      '@wsc-tee-weapp/feature-home',
      '@ext-tee-retail-shelf/retail-shelf',
      '@ext-tee-retail-shelf/retail-shelf-index',
    ],
    '@ext-tee-wsc-goods/sku-order-popup': [
      '@wsc-tee-weapp-goods/goods-detail',
      '@wsc-tee-weapp/feature',
      '@wsc-tee-weapp/feature-home',
      '@ext-tee-retail-shelf/retail-shelf',
      '@ext-tee-retail-shelf/retail-shelf-index',
    ],
  },
}));

module.exports = configs;

// 生成 minor exts
function getMinorPkgExts() {
  const bizConfig = resolveSrc(
    'ranta-config/bizs/goods-detail/index.page.json'
  );
  const explicitAsyncExts = [
    ...packagesExtMap[PackagesName.CORE],
    ...packagesExtMap[PackagesName.MAIN],
  ];

  // 除了显式指定的异步 exts 之外的异步 exts
  const minorPkgExts = bizConfig.modules
    .filter(({ stage, extensionName }) => {
      return stage !== 'pre' && !explicitAsyncExts.includes(extensionName);
    })
    .map((ext) => {
      // 固定版本号，否则会把用到的 ext 都打到异步分包
      if (!ext.isRemote) {
        return ext.extensionName;
      }
      return `${ext.extensionName}#${ext.extensionVersion}`;
    });
  console.log('minorPkgExts', minorPkgExts);
  return minorPkgExts;
}

// getMinorPkgExts()

// 输出各分包的配置
function getPkgDetail() {
  const bizConfig = resolveSrc(
    'ranta-config/bizs/goods-detail/index.page.json'
  );

  const appExts = bizConfig.modules
    .filter(({ stage, extensionName }) => {
      return (
        !Object.values(packagesExtMap).flat().includes(extensionName) &&
        !featureExts.includes(extensionName)
      );
    })
    .map((i) => i.extensionName);

  // 商详与首页共用的, 打在 feature 分包
  const extraPkgExts = bizConfig.modules
    .filter(({ stage, extensionName }) => {
      return (
        !Object.values(packagesExtMap).flat().includes(extensionName) &&
        !appExts.includes(extensionName)
      );
    })
    .map((i) => i.extensionName);
  console.log('主包', appExts, appExts.length);
  console.log(
    '商品分包',
    packagesExtMap,
    Object.values(packagesExtMap).flat().length
  );
  console.log('打在feature分包', extraPkgExts, extraPkgExts.length);
}

// getPkgDetail();
