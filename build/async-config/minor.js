const { isRetailBiz } = require('../script/biz/retail');

const isRetail = isRetailBiz;

let minorComs = [
  'components/showcase/components/lottery-code-icon',
  'components/cps-goods-recommend', //
  'components/bi-list-icon', // 导购？
  'components/navigator', //
  'components/web-view-navigator', //?
  'components/zan-contact', //?
  'shared/components/cart/custom-sku-header-price', //
  'shared/components/showcase/hot-words', //?
  'shared/components/showcase/present-gift', //?
  'shared/components/showcase/category',
  'shared/components/showcase/double-eleven',
  'shared/components/showcase/contact-us',
  '@youzan/ump-tee-components', //?
  'components/collect-tip',
  'components/shop-pop-manager', //
  'components/scene-market-subscribe', // CRM 场景营销弹窗
  'components/showcase/components/timeline-share',
  'components/showcase/components/platform-coupon-pop',
  'components/ump/marketing-page/icon',
  'components/ump/collect-gift',
  'shared/components/showcase/captain-components', //
  'components/member/simple-coupon-list/index', // 会员码优惠券列表

  '@youzan/captain-weapp/dist/packages/rich-text/index',
  'ext-tee-user/extensions/member-code',
  // edu sc
  // `${coreComsPrefix}/decorate/edu-`,
  // `${coreComsPrefix}/decorate/teacher`,
  // `${coreComsPrefix}/decorate/member`,
  // `${coreComsPrefix}/decorate/column`,
  // `${coreComsPrefix}/decorate/content`,
  // `${coreComsPrefix}/decorate/live`,
  // `${coreComsPrefix}/decorate/course`,
  // `${coreComsPrefix}/decorate/course-group`,
  // `${coreComsPrefix}/decorate/regis-form`,
  // `${coreComsPrefix}/decorate/registration-guide`,
  // `${coreComsPrefix}/captain/paidcontent`,
];

const minorRetailComs = [
  // app-retail 迁移到分包
  'shared/components/showcase/rishiji-ump',
  '@youzan/captain-weapp/dist/packages/showcase/components/new-goods',
  'shared/components/showcase/feature-video',
  'shared/components/showcase/feature-video-search',
];

minorComs = isRetail ? [...minorComs, ...minorRetailComs] : minorComs;

const wscMinorComsBlacklist = [
  'chain-store/components/navigator-bar',
  'components/message/contact/index',
  'shared/components/showcase/captain-components/goods-search/index',
  'shared/components/showcase/captain-components/search/index',
  'shared/components/showcase/captain-components/image/index',
];

const lsMinorComsBlacklist = [
  'chain-store/components/navigator-bar',
  'components/message/contact/index',
];

const minorComsBlacklist = isRetail
  ? lsMinorComsBlacklist
  : wscMinorComsBlacklist;

module.exports = {
  name: 'packages/async-minor',
  priority: 20,
  test({ request }) {
    if (minorComsBlacklist.some((com) => request.includes(com))) {
      return false;
    }
    return minorComs.some((com) => request.includes(com));
  },
};
