while getopts "b:r:v:m:u" opt; do
case $opt in
  v)
    tagVersion=$OPTARG
    ;;
  esac
done

currentBranch=$(git branch | grep \* | cut -d ' ' -f2)

echo "tag version: ${tagVersion}"
echo "推送到打包仓库..."
git remote add origin_build ***********************:retail-web/retail-weapp-build.git
git add .
git commit -am "feat: build commit" --no-verify
git tag -a $tagVersion -m "云 SDK： ${tagVersion}"
git push -u origin_build $currentBranch --follow-tags --force
git reset --hard HEAD~1
echo "code reseted"
git remote rm origin_build