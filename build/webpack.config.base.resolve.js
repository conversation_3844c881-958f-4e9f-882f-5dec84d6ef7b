const path = require('path');
const resolve = (src) => path.resolve(__dirname, src);
const { isDev } = require('@kokojs/shared');

const guideTeeClientPath = isDev()
  ? '../src/ext-tee-guide/extensions/tee-client'
  : '../src/packages/guide/tee-client';


module.exports = {
  roots: [resolve('../src')],
  alias: {
    src: resolve('../src'),
    pages: resolve('../src/pages'),
    packages: resolve('../src/packages'),
    retail: resolve('../src/pages-retail'),
    utils: resolve('../src/utils'),
    themes: resolve('../src/themes'),
    helpers: resolve('../src/helpers'),
    components: resolve('../src/components'),
    constants: resolve('../src/constants'),
    'common-api': resolve('../src/common-api'),
    'third-utils': resolve('../src/third-utils'),
    '@': resolve('../src'), // 这个只是在 js 中有效，考虑去掉
    // 20210729: goods-v2 会让 webpack-loader 产生命名冲突，先改成这个路径名了，有更好的命名可以再商量
    '@goods': resolve('../shared/packages/goods-shared/detail'), // 这个只是在 js 中有效，考虑去掉

    // node_modules 指定使用 wsc，在其他仓库使用该仓库有效
    node_modules: resolve('../node_modules'),
    'vant-weapp/dist': resolve('../node_modules/vant-weapp/dist'),
    '@vant/weapp/dist': resolve('../node_modules/@vant/weapp/dist'),
    'youzanyun-sdk': resolve('../src/youzanyun-sdk'),
    '@vant/weapp': resolve('../node_modules/@vant/weapp/dist'),
    '@youzan/assets-weapp-components': resolve(
      '../node_modules/@youzan/assets-weapp-components/dist'
    ),
    '@youzan/vant-tee/dist': resolve('../node_modules/@youzan/vant-tee/dist'),
    '@youzan/vant-tee/native': resolve(
      '../node_modules/@youzan-open/vant-tee/native'
    ),
    '@youzan-open/vant-tee/native': resolve(
      '../node_modules/@youzan-open/vant-tee/native'
    ),
    '@youzan/vant-tee': resolve('../node_modules/@youzan/vant-tee/dist'),
    '@youzan-cloud/tee-ui': resolve('../node_modules/@youzan-open/vant-tee'),
    '@youzan-open/cloud-tee-api': resolve(
      '../node_modules/@youzan-open/tee-api'
    ),
    '@youzan-cloud/cloud-component-sdk': resolve(
      '../node_modules/.cache/cloud-component-sdk.js'
    ),
    '@youzan/decorate-tee/native': resolve(
      '../node_modules/@youzan/decorate-tee/native'
    ),
    '@youzan/tee-api': resolve('../node_modules/@youzan-open/tee-api'),
    '@youzan/tee': resolve('../node_modules/@youzan-open/tee'),
    // extend 仓库在别处
    '@youzan-cloud/runtime': resolve('../node_modules/@youzan-cloud/runtime'),

    // shared 对应列表
    'shared/goods': path.resolve(__dirname, '../shared/packages/goods'),
    // 如果使用了上面那种映射，shared 必须放在最后
    shared: path.resolve(__dirname, '../shared'),
    mixins: path.resolve(__dirname, '../shared/common/css/styles/mixins'),
    // 导购多端 tee 文件
    'guide-tee-client': resolve(guideTeeClientPath),
  },
  cacheWithContext: false,
  mainFields: ['module', 'main'],
  mainFiles: ['index'],
  modules: ['node_modules'],
  preferAbsolute: true,
  unsafeCache: false,
};
