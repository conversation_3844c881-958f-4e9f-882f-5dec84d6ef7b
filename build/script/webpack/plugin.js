/**
 * Create webpack plugins by condition
 *
 * @param {object} config config
 * @param {boolean | function | undefined} [config.match] Match condition
 * @param {() => function[]} config.plugins A function that returns plugins array
 *
 * @returns {function[]} Plugins array
 */
function createPlugins(config) {
  const { match, plugins } = config;

  let isMatch = match === undefined; // match by default.

  if (typeof match === 'boolean') {
    isMatch = match;
  } else if (typeof match === 'function') {
    isMatch = match();
  }

  if (!isMatch) {
    return [{ apply: () => {} }];
  }

  return plugins();
}

module.exports = {
  createPlugins,
};
