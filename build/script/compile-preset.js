const minimist = require('minimist');

const presets = {
  common: {
    common: 'pages/common,pages/tab,packages/ump',
    home: 'pages/home,pages/bear,packages/async-main,packages/async-minor,packages/async-video,packages/async-feature-ext,packages/ump',
    booking:
      'pages/home,pages/bear,packages/async-main,packages/async-minor,packages/async-video,packages/async-feature-ext,packages/ump,pages/usercenter,packages/usercenter',
  },
  trade: {
    trade:
      'packages/trade-buy,packages/paid,packages/common,packages/trade-cart,packages/order-native,packages/trade,packages/trade-pay,packages/trade-buy-subpage,packages/home',
  },
  goods: {
    detail: 'pages/goods,packages/trade-cart,packages/member-code',
    list: 'packages/goods-list',
  },
  shop: {
    shop: 'packages/shop',
    home: 'pages/home,packages/home,packages/shop,pages/bear,pages/common,packages/async-main,packages/async-minor,packages/async-video,packages/async-feature-ext,packages/trade-cart,packages/member-code,packages/usercenter,packages/trade,packages/goods-list',
  },
  furnish: {
    // 原生首页/微页面
    'native-home': 'pages/home',
    // 店铺笔记等（其他乱七八糟的东西）
    'shop-notes': 'packages/shop',
    // 中台化分包
    'ranta-home': 'packages/home',
    // 个人中心
    'user-center': 'pages/usercenter,packages/usercenter',
    // 支付页面
    'wx-h5-pay': 'packages/pay',
  },
  account: {
    account: 'packages/account,packages/trade-cart',
  },
  retail: {
    // 点单宝
    shelf: 'packages/retail-shelf,packages/shop-select,packages/retail,packages/async-retail',
    // 扫码点单
    index: 'pages-retail/dashboard,packages/retail,packages/retailb',
    // 预约订座
    reservation:
      'packages/reservation,packages/shop-select,packages/home,pages/common',
  },
  member: {
    'member-code': 'packages/member-code',
  },
  guide: {
    material: 'packages/async-video,pages/bear,packages/guide/pages/material',
    coupon: 'packages/async-video,pages/bear,packages/guide/pages/coupon',
    'member-promotion':
      'packages/async-video,pages/bear,packages/guide/pages/member-promotion',
  },
};

function getBuildPackagesCommandOptions() {
  const parsedArgs = minimist(process.argv.slice(2));

  return parsedArgs.p || parsedArgs.package;
}

function getPresetName(p) {
  const presetNameList = p.split(',');
  return presetNameList.map((presetName) => presetName.trim().slice(1));
}

function getPreset(presetNameList) {
  const presetSet = new Set();
  presetNameList.forEach((name) => {
    const [domin, preset] = name.split('/');
    const matchPreset = presets[domin][preset];
    if (matchPreset) {
      const matchPresetArr = matchPreset.split(',');
      matchPresetArr.forEach((P) => presetSet.add(P.trim()));
    }
  });
  return Array.from(presetSet).join(',');
}

function presetHandleFun() {
  let packages = getBuildPackagesCommandOptions();
  if (!packages || typeof packages !== 'string') return;

  const isPreset = packages.startsWith('@');
  if (!isPreset) return;

  packages = getPresetName(packages);

  const preset = getPreset(packages);
  if (preset) {
    const index = process.argv.findIndex((arg) =>
      ['-p', '-package'].some((item) => arg === item)
    );
    process.argv[index + 1] = preset;
    console.log('Compile preset matched:', preset.split(','));
    injectHomePresentTag(preset);
  }
}

// dev 模式、零售、编译 home，注入一个标志
// 自动删除掉 tab-bar 的引用，目前的按需编译情况下无法打入 ranta-tab-bar，还需要手动去删除
// 如果之后有方案可以解决这个问题，可以把这个逻辑去掉
function injectHomePresentTag(preset) {
  if (
    process.env.NODE_ENV === 'development' &&
    preset.includes('pages/home') &&
    process.env.APP_NAME === 'ls'
  ) {
    process.env.HOME_PRESET = true;
  }
}

module.exports = {
  presetHandleFun,
  getBuildPackagesCommandOptions,
};
