/**
 * ranta 基础库灰度切流脚本
 * 编译切流仅对大客与应用市场商家有效（触发构建），标品无法覆盖。
 * TODO 全量后移除
 */

const fse = require('fs-extra');
const path = require('path');
const axios = require('axios');

const RANTA_GRAY_KEY = 'lib-ranta';

// 立即执行
(async function injectPkgJSONForRantaGrayLibs(env = 'prod') {
  console.log('[Ranta] 检查 Ranta 灰度规则');
  const CWD = process.cwd();

  const { kdtId } = await fse.readJSON(path.join(CWD, 'cloud-task.json'));
  const { rules, packages } = await getMontageApolloConfig(env);

  const matched = matchGrayConfig(rules, kdtId);

  console.log(
    `[Ranta] ${
      matched ? '启用' : '未启用'
    } Ranta 灰度版本，当前店铺ID：${kdtId}，使用 NPM 包：${JSON.stringify(
      packages
    )}`
  );

  if (matched) {
    const pkgJSON = await fse.readJSON(path.join(CWD, 'package.json'));
    Object.assign(pkgJSON.dependencies, packages);
    Object.assign(pkgJSON.resolutions, packages);
    await fse.writeJSON(path.join(CWD, 'package.json'), pkgJSON, { spaces: 2 });
  }
})(process.env.NODE_ENV);

/** 获取中台化编译切流规则 */
async function getMontageApolloConfig(env = 'prod') {
  const urlMap = {
    prod: 'http://montage-core.s.qima-inc.com',
    qa: 'http://montage-core.qa.s.qima-inc.com',
  };
  const url = urlMap[env] || urlMap.prod;
  const res = await axios.get(`${url}/api/app/koko/config`);

  if (res.status !== 200) {
    console.error('中台化编译切流灰度规则获取失败');
    throw res.statusText;
  }

  return res.data.data[RANTA_GRAY_KEY];
}

/**
 * 解析apollo配置的切流决策
 * apollo的文本配置以半角逗号分隔，百分比值写在第一项，其次白名单，最后黑名单
 * 遇到0或者0%会判为不匹配
 * '%1,491391,603367,-160,-11998'
 */
function matchGrayConfig(config, kdtId) {
  const kdtIdList = config.split(',');
  // 先判断0或者黑名单的情况
  if (
    kdtIdList.includes('0') ||
    kdtIdList.includes('0%') ||
    kdtIdList.includes('-' + kdtId)
  ) {
    return false;
  }
  if (kdtIdList.includes(String(kdtId))) {
    // kdtId全匹配
    return true;
  }
  if (config.indexOf('%') > 0) {
    // 百分比判断
    const percentArr = kdtIdList
      .filter((singleConfig) => {
        return singleConfig.endsWith('%');
      })
      .map((singleConfig) => {
        return singleConfig.slice(0, singleConfig.length - 1);
      });
    if (percentArr && percentArr.length) {
      // 只取第一个百分比配置
      const onlyPercent = Number(percentArr[0]);
      return !!(
        onlyPercent >= 0 &&
        onlyPercent <= 100 &&
        Number(kdtId) % 100 <= onlyPercent
      );
    }
    return false;
  }
  return false;
}
