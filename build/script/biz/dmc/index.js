const {
  isNativeCustom,
  isRantaCustom,
} = require('@youzan/ranta-mp-ext-adapter-builder');
const { ROUTE_CONF } = require('../trade/config');
const { buildDmcConfig } = require('./dmcConfig');
const { setDmcCustomConfig, setDmcPageNotFoundData } = require('./writeConfig');

const isEcloud = process.env.BUILD_ENV === 'youzanyun';
const isProd = process.env.NODE_ENV === 'production';
const root = process.cwd();

const DMC_PAGE_NOT_FOUND_DATA = {}; // 用于 onPageNotFound 兜底
const DMC_CUSTOM_CONFIG = { routes: {} };

// 与 build/biz/trade/config.js 对应
// 后续有增加再往上加
const map = {
  paid: 'OrderPayResult',
  cart: 'Cart',
  detail: 'OrderDetail',
  order: 'ConfirmOrder',
  pay: 'PayWsctradePay',
};

function processTrade() {
  Object.keys(ROUTE_CONF).forEach((key) => {
    const routeName = map[key];

    // 要求在 map 中指定 routeName
    if (!routeName) return;

    const value = ROUTE_CONF[key];

    if (isRantaCustom(root, value.NEW)) {
      // 中台化定制
      DMC_CUSTOM_CONFIG.routes[routeName] = buildDmcConfig(
        routeName,
        value.NEW
      );
      DMC_PAGE_NOT_FOUND_DATA[value.OLD] = routeName;
      console.log(`统一路由中台化定制 ${routeName} -> ${value.NEW}`);
    }

    if (isNativeCustom(root, value.TRANS) || isNativeCustom(root, value.OLD)) {
      // 原生定制
      DMC_CUSTOM_CONFIG.routes[routeName] = buildDmcConfig(
        routeName,
        value.OLD
      );
      DMC_PAGE_NOT_FOUND_DATA[value.NEW] = routeName;
      console.log(`统一路由原生定制 ${routeName} -> ${value.OLD}`);
    }
  });
}

function dmcRun() {
  if (isEcloud && isProd) {
    console.log('dmcRun inner');
    processTrade();

    console.log(`统一路由配置计算完成`);

    // 不直接参与计算，只是用于方便排查，最终插入数据是通过 transformAppRawConfig 注入
    setDmcCustomConfig(DMC_CUSTOM_CONFIG);
    setDmcPageNotFoundData(DMC_PAGE_NOT_FOUND_DATA);
  }

  return {
    customConfig: DMC_CUSTOM_CONFIG,
    onPageNotFoundData: DMC_PAGE_NOT_FOUND_DATA,
  };
}

module.exports = {
  dmcRun,
};
