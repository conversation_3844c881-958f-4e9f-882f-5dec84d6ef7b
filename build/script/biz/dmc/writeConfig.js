const path = require('path');
const fse = require('fs-extra');

const RANTA_CONFIG_FILE = path.resolve(
  process.cwd(),
  'src/ranta-config/app.config.json'
);
const DMC_PAGE_NOT_FOUND_FILE = path.resolve(
  process.cwd(),
  'dmc-page-not-found.json'
);

function setDmcCustomConfig(data) {
  const rantaConfig = fse.readJsonSync(RANTA_CONFIG_FILE);

  rantaConfig.plugins.dmc.customConfig = data;

  fse.writeJsonSync(RANTA_CONFIG_FILE, rantaConfig);
}

function setDmcPageNotFoundData(data) {
  fse.writeJsonSync(DMC_PAGE_NOT_FOUND_FILE, data);
}

module.exports = {
  setDmcCustomConfig,
  setDmcPageNotFoundData,
};
