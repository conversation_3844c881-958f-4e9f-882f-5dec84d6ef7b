const webpack = require('webpack');

const convertPathConfig = {
  // 账号-行为验证组件

  '@youzan/passport-tee-behavior/native/weapp/behavior/components/container/index':
    '@youzan/passport-tee-behavior/lib/behavior/components/container/index',

  '@youzan/passport-tee-behavior/native/weapp/behavior/components/layout/header':
    '@youzan/passport-tee-behavior/lib/behavior/components/layout/header',

  '@youzan/passport-tee-behavior/native/weapp/behavior/components/layout/index':
    '@youzan/passport-tee-behavior/lib/behavior/components/layout/index',

  '@youzan/passport-tee-behavior/native/weapp/behavior/components/loading/index':
    '@youzan/passport-tee-behavior/lib/behavior/components/loading/index',

  '@youzan/passport-tee-behavior/native/weapp/behavior/slide/slide-control':
    '@youzan/passport-tee-behavior/lib/behavior/slide/slide-control',

  '@youzan/passport-tee-behavior/native/weapp/behavior/slide/slide-display':
    '@youzan/passport-tee-behavior/lib/behavior/slide/slide-display',

  '@youzan/passport-tee-behavior/native/weapp/behavior/index':
    '@youzan/passport-tee-behavior/lib/behavior/index',

  // 账号-协议组件

  '@youzan/passport-protocol/native/weapp/view/tee/protocol-list':
    '@youzan/passport-protocol/lib/view/tee/protocol-list',

  '@youzan/passport-protocol/native/weapp/view/tee/protocol-popup':
    '@youzan/passport-protocol/lib/view/tee/protocol-popup',

  '@youzan/passport-protocol/native/weapp/view/tee/protocol/index':
    '@youzan/passport-protocol/lib/view/tee/protocol.', // 最后一个 . 用于区分 protocol.XXX 目录

  // 账号-授权组件

  '@youzan/passport-tee/native/weapp/user-authorize/index':
    '@youzan/passport-tee/lib/user-authorize/index',

  '@youzan/passport-tee/native/weapp/user-authorize/popup':
    '@youzan/passport-tee/lib/user-authorize/popup',

  '@youzan/passport-tee-components/native/weapp/components/captcha/bottom':
    '@youzan/passport-tee-components/lib/components/captcha/bottom',

  '@youzan/passport-tee-components/native/weapp/components/captcha/index':
    '@youzan/passport-tee-components/lib/components/captcha/index',

  '@youzan/passport-tee-components/native/weapp/components/captcha/password':
    '@youzan/passport-tee-components/lib/components/captcha/password',

  '@youzan/passport-tee-components/native/weapp/components/captcha/sms':
    '@youzan/passport-tee-components/lib/components/captcha/sms',

  '@youzan/passport-tee-components/native/weapp/components/login/index':
    '@youzan/passport-tee-components/lib/components/login/index',

  '@youzan/passport-tee-components/native/weapp/components/user-info/index':
    '@youzan/passport-tee-components/lib/components/user-info/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-authorize/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-authorize/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-mobile/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-mobile/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-mobile/captcha':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-mobile/captcha',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-mobile/simple-login':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-mobile/simple-login',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-protocol/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-protocol/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-user-info/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-user-info/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-external/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-external/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-external/components/protocol-list/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-external/components/protocol-list/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/trigger-button/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/trigger-button/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/trigger-button/native-mobile/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/trigger-button/native-mobile/index',

  '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/trigger-button/native-user-info/index':
    '@youzan/passport-tee-components/lib/components/user-authorize/components/trigger-button/native-user-info/index',

  '@youzan/passport-tee-components/native/weapp/components/common/auth-actions':
    '@youzan/passport-tee-components/lib/components/common/auth-actions',
};

const ACCOUNT_PATH_REGEXP =
  /@youzan\/passport-(tee|tee-api|tee-components|tee-behavior|tee-shared|protocol)\/native\/weapp\//;

const RELATIVE_PATH_REGEXP = /^\.{1,2}\//;

const accountPlugins = [
  // 接续 convertPathConfig，将 native/weapp 下的纯 js 文件指向 lib 目录，避免 /native/weapp 被无效引入
  new webpack.NormalModuleReplacementPlugin(/.*/, function (resource) {
    if (!ACCOUNT_PATH_REGEXP.test(resource.context)) {
      return;
    }

    // import '@youzan/passport-xxx/native/weapp';
    if (ACCOUNT_PATH_REGEXP.test(resource.request)) {
      resource.request = resource.request.replace('native/weapp', 'lib');
      return;
    }

    // import '../foo';
    if (RELATIVE_PATH_REGEXP.test(resource.request)) {
      const libContext = resource.context.replace('native/weapp', 'lib');
      resource.request = `${libContext}/${resource.request}`;
    }
  }),
];

module.exports = {
  convertPathConfig,
  accountPlugins,
};
