const fs = require('fs');

const searchFile = (path, fileNames, cb) => {
  const isDirectory = fs.lstatSync(path).isDirectory();

  if (isDirectory) {
    const files = fs.readdirSync(path);
    files.forEach((file) => searchFile(`${path}/${file}`, fileNames, cb));
  } else {
    const file = path.split('/')[path.split('/').length - 1];
    if (fileNames.includes(file)) {
      cb(path);
    }
  }
};

const everyFile = (path, cb) => {
  const isDirectory = fs.lstatSync(path).isDirectory();

  if (isDirectory) {
    const files = fs.readdirSync(path);
    files.forEach((file) => everyFile(`${path}/${file}`, cb));
  } else {
    cb(path);
  }
};

module.exports = {
  searchFile,
  everyFile,
};
