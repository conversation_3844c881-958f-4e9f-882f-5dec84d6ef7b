const path = require('path');
const fsExt = require('fs-extra');

const { searchFile, everyFile } = require('./utils');

const srcPath = path.resolve(__dirname, '../../../../src');

const rantaAppConfig = require(path.resolve(
  srcPath,
  'ranta-config/app.config.json'
));

const rantaConfigDirPath = path.resolve(srcPath, 'ranta-config');

const spm = rantaAppConfig.plugins.logger.spm;

// 白名单不需要同时满足
const pageTypeWhiteList = [
  'g',
  'ag',
  'f',
  'uc',
  'cart',
  'od',
  'ol',
  'trade',
  'paySuccess',
];

const pathWhiteList = [
  // ump
  'packages/ump',
  // 优惠券
  'packages/user/coupon/list/index',
  'packages/user/coupon/detail/index',
].concat(
  Object.entries(spm)
    .filter(([_path, pageType]) => pageTypeWhiteList.includes(pageType))
    .map(([path]) => path)
);

const createPageTitleMap = () => {
  const map = {};

  const rawFileCallback = (path) => {
    const pageTitle = getPageTitle(path);

    if (!pageTitle) {
      return;
    }

    const { pagePath, title } = pageTitle;
    map[pagePath] = title;
  };

  const rantaBizConfigCallback = (path) => {
    try {
      const json = fsExt.readJsonSync(path);
      if (json.routes && json.config && json.config.navigationBarTitleText) {
        json.routes.forEach((route) => {
          const path = route.startsWith('/') ? route.substring(1) : route;
          map[path] = json.config.navigationBarTitleText;
        });
      }
    } catch (e) {}
  };

  searchFile(
    srcPath,
    ['index.json', 'one.json', 'two.json', 'three.json'],
    rawFileCallback
  );

  everyFile(rantaConfigDirPath, rantaBizConfigCallback);

  writePageTitleMapJs(map);
};

const getPageTitle = (indexJsonPath) => {
  try {
    const json = fsExt.readJSONSync(indexJsonPath);

    const pagePath = indexJsonPath
      .replace('.json', '')
      .substring(srcPath.length + 1);

    if (pagePath.startsWith('components')) {
      return false;
    }

    if (json.navigationBarTitleText) {
      return { pagePath, title: json.navigationBarTitleText };
    }
  } catch (e) {
    return false;
  }

  return false;
};

const filterMap = (map) => {
  const kvs = Object.entries(map);

  const filterdKvs = kvs.filter(([path, title]) =>
    pathWhiteList.some((it) => path.includes(it))
  );

  return filterdKvs.reduce((prev, cur) => {
    prev[cur[0]] = cur[1];
    return prev;
  }, {});
};

const writePageTitleMapJs = (map) => {
  const filteredMap = filterMap(map);

  fsExt.writeFileSync(
    `${srcPath}/utils/log/` + 'title-map.js',
    `// 该文件用于埋点页面静态title的采集
// 会在每次dev或者build时更新路径与title的映射关系
// 不需要手动更新
    export const titleMap = ${JSON.stringify(filteredMap)}`
  );
};

const removePageTitleMapFile = () => {
  fsExt.removeSync(`${srcPath}/utils/log/` + 'title-map.js');
};

module.exports = {
  createPageTitleMap,
  removePageTitleMapFile,
};
