const path = require('path');
const fs = require('fs');

const srcPath = path.resolve(__dirname, '../../../../src');
const targetDir = path.resolve(srcPath, 'packages/guide/tee-client');

// 移除目录函数，支持容错
function removeGuideTeeFiles() {
  try {
    // 检查目录是否存在
    if (!fs.existsSync(targetDir)) {
      console.warn(`Directory ${targetDir} does not exist. Skipping removal.`);
      return;
    }

    // 删除目录及其内容
    fs.rmSync(targetDir, { recursive: true, force: true });
    console.log(`Directory ${targetDir} has been removed successfully.`);
  } catch (error) {
    console.error(`Error while removing directory ${targetDir}:`, error);
  }
}

module.exports = { removeGuideTeeFiles };
