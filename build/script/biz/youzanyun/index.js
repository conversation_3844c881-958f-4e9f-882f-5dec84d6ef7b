// @ts-check

const _ = require('lodash');
const path = require('path');
const { execa, fs } = require('@kokojs/shared');

const PWD = process.cwd();
const APP_JSON_PATH = path.join(PWD, 'src/app.json');
const APP_RETAIL_JSON_PATH = path.join(PWD, 'src/app-retail.json');
const IS_YOUZANYUN = process.env['BUILD_ENV'] === 'youzanyun';
const IS_FAIL_CUSTOM_TAB_BAR = process.env['IS_FAIL_CUSTOM_TAB_BAR'] === 'true';

const CART_TAB_CTN = [
  path.join(PWD, 'src/pages/tab/one/index'),
  path.join(PWD, 'src/pages/tab/two/index'),
  path.join(PWD, 'src/pages/tab/three/index'),
];

// TODO 后续迁移到有赞云插件中
function wscModeDropConfigsOnlyUseInYouzanyun() {
  // 非有赞云场景，排除以下页面的编译
  // const DROP_PAGE_TARGET = ['pages/common/thirdparty-app-midway/index'];

  const appJson = fs.readJsonSync(APP_JSON_PATH);
  const appRetailJson = fs.readJsonSync(APP_RETAIL_JSON_PATH);

  const pages = _.get(appJson, 'pages', []);
  const preloadRule = _.get(appJson, 'preloadRule', {});
  const tabBar = _.get(appJson, 'tabBar', { list: [] });
  const retailTabBar = _.get(appRetailJson, 'tabBar', { list: [] });

  if (!IS_YOUZANYUN) {
    // DROP_PAGE_TARGET.forEach((dropPage) => {
    //   if (pages.includes(dropPage)) {
    //     pages.splice(pages.indexOf(dropPage), 1);
    //     console.log(`\n非有赞云场景排除${dropPage}页面的编译\n`);
    //   }
    // });
  }

  if (IS_YOUZANYUN && IS_FAIL_CUSTOM_TAB_BAR) {
    const DROP_TAB = [
      'pages/tab/one/index',
      'pages/tab/two/index',
      'pages/tab/three/index',
    ];
    DROP_TAB.forEach((dropPage) => {
      if (pages.includes(dropPage)) {
        pages.splice(pages.indexOf(dropPage), 1);
        console.log(`\n有赞云场景排除${dropPage}页面的编译\n`);
      }
      delete preloadRule[dropPage];
    });
    // 恢复 tabbarlist 配置
    tabBar.list = [
      tabBar.list[0],
      {
        pagePath: 'pages/goods/cart/index',
        text: '购物车',
        iconPath: 'icons/cart.png',
        selectedIconPath: 'icons/cart-selected.png',
      },
      tabBar.list[tabBar.list.length - 1],
    ];
    retailTabBar.list = [
      retailTabBar.list[0],
      {
        pagePath: 'pages/goods/cart/index',
        text: '购物车',
        iconPath: 'icons/cart.png',
        selectedIconPath: 'icons/cart-selected.png',
      },
      retailTabBar.list[retailTabBar.list.length - 1],
    ];
  }
  fs.outputJsonSync(APP_JSON_PATH, appJson, { spaces: 2 });
  fs.outputJsonSync(APP_RETAIL_JSON_PATH, appRetailJson, { spaces: 2 });
}

/**
 * 大客模式下，tab 页不能再引用了中台的购物车路径了
 * 因为 dist 产物会因为编译切流只保留 trade/cart-v1 路径
 * 会导致微信解析报错
 */
function dropCartTab() {
  if (IS_YOUZANYUN && IS_FAIL_CUSTOM_TAB_BAR) {
    try {
      CART_TAB_CTN.forEach((dir) => {
        const jsonConfig = fs.readJsonSync(`${dir}.json`);
        delete jsonConfig.usingComponents.cart;
        delete jsonConfig.componentPlaceholder.cart;
        fs.outputJsonSync(`${dir}.json`, jsonConfig, { spaces: 2 });

        const wxmlConfig = fs.readFileSync(`${dir}.wxml`, {
          encoding: 'utf8',
        });
        const dropContent = wxmlConfig.replace(/<cart[\s|\S]+\/>/, '');
        fs.outputFileSync(`${dir}.wxml`, dropContent);
      });
    } catch (error) {}
  }
}

function getGistStatus() {
  try {
    const res = execa.sync('git', ['status', '--porcelain']);
    return res.stdout;
  } catch (error) {
    return '';
  }
}

function restoreScriptChangeFiles() {
  if (IS_YOUZANYUN && IS_FAIL_CUSTOM_TAB_BAR) {
    const res = getGistStatus();
    if (res.includes('/pages/tab/')) {
      // let discardPaths = [];
      try {
        CART_TAB_CTN.forEach((dir) => {
          const jsonPath = `${dir}.json`;
          const wxmlPath = `${dir}.wxml`;
          execa.sync('git', ['checkout', jsonPath]);
          execa.sync('git', ['checkout', wxmlPath]);
          // discardPaths = discardPaths.concat([jsonPath, wxmlPath]);
        });
        // console.log(discardPaths.join(' '));
      } catch (error) {
        // ignore
      }
    }
  }
}

// 定制场景下增加全局UI容器
function insertAppContentAppendExt() {
  if (!IS_YOUZANYUN) return;
  const RANTA_CONFIG = path.join(PWD, 'src/ranta-config/');
  const APP_JSON_PATH = `${RANTA_CONFIG}app.config.json`;
  const appJson = fs.readJsonSync(APP_JSON_PATH);
  const bizList = [
    '@wsc-tee-weapp-feature/feature.page.json',
    '@wsc-tee-weapp-feature-home/feature-home.page.json',
    'goods-detail/index.page.json',
    'salesman-promote/index.page.json',
    'share-goods/index.page.json',
  ];
  const MODULE = {
    id: '@wsc-tee-common/app-content-append~VAtrAMhi',
    extensionName: '@wsc-tee-common/app-content-append',
    extensionVersion: '0.0.0',
    isRemote: false,
  };
  const CONTAINER = {
    contentType: 'module',
    layout: 'column',
    contents: ['@wsc-tee-common/app-content-append~VAtrAMhi'],
    style: {
      position: 'relative',
      'z-index': 999999,
    },
  };
  appJson.modules.push(MODULE);
  fs.outputJsonSync(APP_JSON_PATH, appJson, { spaces: 2 });
  bizList.forEach((biz) => {
    const PATH = `${RANTA_CONFIG}bizs/${biz}`;
    const bizJson = fs.readJsonSync(PATH);
    bizJson.containers.push(CONTAINER);

    fs.outputJsonSync(PATH, bizJson, { spaces: 2 });
  });
}
module.exports = {
  customConfigInYouzanyun: () => {
    wscModeDropConfigsOnlyUseInYouzanyun();
    insertAppContentAppendExt();
  },
  dropCartTab,
  restoreScriptChangeFiles,
};
