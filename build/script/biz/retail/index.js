const path = require('path');
const { fs, root } = require('@kokojs/shared');
const { resolve } = path;

const { APP_NAME = '' } = process.env;

const SRC_PATH = path.join(process.cwd(), 'src');
const APP_JSON_PATH = path.join(SRC_PATH, './app-retail.json');
const RETAIL_SHELF_BIZ = '@ext-tee-retail-shelf/retail-shelf';
const RETAIL_BIZ_LIST = [RETAIL_SHELF_BIZ];
const RETAIL_SHELF_V1_NAME = 'packages/retail/goods-shelf-v1';

const isRetailBiz = APP_NAME
  ? APP_NAME === 'ls' ||
    APP_NAME === 'kq' ||
    APP_NAME === 'brand-retail' ||
    APP_NAME.endsWith('-ls')
  : false;

const isRetail = APP_NAME
  ? APP_NAME === 'ls' || APP_NAME === 'brand-retail' || APP_NAME.endsWith('-ls')
  : false;

const isRetailKQ = APP_NAME === 'kq';

function retailTransformBiz(bizInfo) {
  // bizInfo 可能会因为前置的处理不存在
  const { config = '' } = bizInfo || {};

  if (!isRetailBiz && RETAIL_BIZ_LIST.includes(config.biz)) {
    return false;
  }

  // 点单页，判断老的页面是否存在（前置 Phaeton 中的切流流程处理）
  // 老的页面在，就不打入新的页面
  // 否则打入新页面
  // console.log('config.biz: ', config.biz);

  if (config.biz === RETAIL_SHELF_BIZ) {
    const appJson = fs.readJsonSync(APP_JSON_PATH);

    // console.log('app-retail.json: ', appJson);

    if (appJson.subPackages.find((pkg) => pkg.root === RETAIL_SHELF_V1_NAME)) {
      return false;
    }
  }

  return bizInfo;
}

// 单个tab增加组件
function addRetailShelfComponent(page) {
  const indexPath = resolve(root, `src/pages/tab/${page}/index.json`);
  const indexJSON = fs.readJsonSync(indexPath);
  indexJSON.usingComponents['retail-shelf'] =
    '**../../../packages/retail-shelf/shelf/index';
  fs.writeFileSync(indexPath, JSON.stringify(indexJSON), 'utf-8');
  console.log(`零售客群构建前点单页处理：【tab-${page}】增加 retail-shelf`);
}
// 修改 tab 页json配置，增加 点单页组件
function adaptorTabRouter() {
  addRetailShelfComponent('one');
  addRetailShelfComponent('two');
  addRetailShelfComponent('three');
}

module.exports = {
  RETAIL_BIZ_LIST,
  isRetail,
  isRetailKQ,
  isRetailBiz,
  retailTransformBiz,
  adaptorTabRouter,
};
