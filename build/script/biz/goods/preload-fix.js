/** app.json preloadRule 中商详异步分包配置 */
const { join } = require('path');
const { isDev, fs } = require('@kokojs/shared');

const AYSN_GOODS_PKG = [
  'packages/goods-main',
  'packages/goods-core',
  'packages/goods-minor',
];

function removeGoodsInvalidAppJsonRules(outputPath) {
  if (!isDev()) {
    return;
  }
  const APP_JSON_PATH = join(outputPath, 'app.json');
  const appJson = fs.readJSONSync(APP_JSON_PATH);
  const appPreloadRule = appJson.preloadRule;
  Object.keys(appPreloadRule).forEach((pagePath) => {
    const packages = appPreloadRule[pagePath].packages || [];

    const newPackages = packages.filter((package) => {
      return AYSN_GOODS_PKG.indexOf(package) === -1;
    });

    if (newPackages.length) {
      appPreloadRule[pagePath].packages = newPackages;
    } else {
      // 如果packages 为空，则移除该配置
      delete appPreloadRule[pagePath];
    }
  });

  fs.writeJsonSync(APP_JSON_PATH, appJson);
}

class RemoveGoodsInvalidRulesPlugin {
  apply(compiler) {
    // dev编译完成后执行
    compiler.hooks.done.tap('RemoveGoodsInvalidRulesPlugin', () => {
      if (this.isFirst) {
        return;
      }
      removeGoodsInvalidAppJsonRules(compiler.options.output.path);
      this.isFirst = true;
    });
  }
}

module.exports = RemoveGoodsInvalidRulesPlugin;
