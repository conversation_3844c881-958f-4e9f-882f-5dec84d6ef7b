const fse = require('fs-extra');
const path = require('path');
const { isProd } = require('@kokojs/shared');

const { outputPath } = require('../../../lib/util');

const DIST_PATH = path.join(process.cwd(), outputPath);
const GOODS_DETAIL_JSON_PATH = path.join(
  DIST_PATH,
  '/weapp/ext-tee-wsc-goods/extensions/goods-detail-block/rt/GoodsDetail.json'
);
const DC_FLOATING_NAV_JSON_PATH = path.join(
  DIST_PATH,
  '/weapp/ext-tee-wsc-dc/extensions/floating-nav/rt/Main.json'
);

// 适配商详预设@goods/detail
function reviseJsonConfig() {
  if (isProd()) {
    return;
  }
  if (fse.existsSync(GOODS_DETAIL_JSON_PATH)) {
    const goodsDetailJSON = fse.readJsonSync(GOODS_DETAIL_JSON_PATH);
    goodsDetailJSON.usingComponents['native-showcase-container'] =
      '../../../../components/showcase-container';
    fse.writeJsonSync(GOODS_DETAIL_JSON_PATH, goodsDetailJSON);
  }

  if (fse.existsSync(DC_FLOATING_NAV_JSON_PATH)) {
    const dcFloatingNavJSON = fse.readJsonSync(DC_FLOATING_NAV_JSON_PATH);
    dcFloatingNavJSON.usingComponents['share-feature'] =
      '../../../../components/showcase/components/share-feature';
    dcFloatingNavJSON.usingComponents['dine-entry'] =
      '../../../../components/showcase/components/floating-nav/components/dine-entry/index';
    fse.writeJsonSync(DC_FLOATING_NAV_JSON_PATH, dcFloatingNavJSON);
  }
}

reviseJsonConfig();
