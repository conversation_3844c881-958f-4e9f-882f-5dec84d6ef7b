const { join, resolve } = require('path');
const { onExitProcess } = require('@youzan-open/ranta-compiler-core');
const {
  fs,
  noop,
  hash,
  root,
  isProd,
  getCacheDir,
  isIsvBuildServer,
} = require('@kokojs/shared');
const { isRetailKQ } = require('../retail');

const OLD_GOODS_PATH = 'packages/goods/detail/index';

function getGoodsDetialConfig() {
  return {
    root: `packages/goods`,
    name: `商品详情`,
    pages: [
      'cart/index',
      'detail/index',
      'lucky-draw-group/index',
      'seckill/index',
      'seckill/end',
      'help-cut/index',
      'groupon/index',
      'tuan/index',
      'present/index',
      'points/index',
    ],
  };
}

function snapshotGoodsV2() {
  const goodsV2Path = join(root, 'src', 'packages', 'goods-v2');
  const dest = getCacheDir(hash(Date.now()));

  if (!fs.existsSync(goodsV2Path)) {
    return noop;
  }

  fs.copySync(goodsV2Path, dest);

  return () => {
    fs.removeSync(goodsV2Path);
    fs.moveSync(dest, goodsV2Path);
  };
}

function snapshotMainPkgGoods() {
  const goodsMainPkgPath = join(root, 'src', 'pages', 'goods', 'detail');
  const dest = getCacheDir(hash(Date.now()));

  if (!fs.existsSync(goodsMainPkgPath)) {
    return noop;
  }

  fs.copySync(goodsMainPkgPath, dest);

  return () => {
    fs.removeSync(goodsMainPkgPath);
    fs.moveSync(dest, goodsMainPkgPath);
  };
}

// 某些特定场景下，商详打回分包
function backToSubPkg() {
  // KQ Step2. 修改商详主包路径
  const rantaGoodsIndexPath = resolve(
    root,
    'src/ranta-config/bizs/goods-detail/index.page.json'
  );
  const rantaGoodsIndexJSON = fs.readJsonSync(rantaGoodsIndexPath);
  // /pages/goods/detail/index ----> /packages/goods/detail/index
  rantaGoodsIndexJSON.routes = ['/packages/goods/detail/index'];
  fs.writeFileSync(
    rantaGoodsIndexPath,
    JSON.stringify(rantaGoodsIndexJSON),
    'utf-8'
  );
  console.log(`零售客群构建前商品详情处理：删除主包路径定义`);
  // KQ Step3. 移除分包重定向
  const rantaGoodsRedirectPath = resolve(
    root,
    'src/ranta-config/bizs/goods-redirect/redirect.page.json'
  );
  const rantaGoodsRedirectJSON = fs.readJsonSync(rantaGoodsRedirectPath);
  // ['/packages/goods/detail/index'] ----> []
  rantaGoodsRedirectJSON.routes = rantaGoodsRedirectJSON.routes.filter(
    (route) => route !== '/packages/goods/detail/index'
  );
  fs.writeFileSync(
    rantaGoodsRedirectPath,
    JSON.stringify(rantaGoodsRedirectJSON),
    'utf-8'
  );
  console.log(`零售客群构建前商品详情处理：删除分包重定向`);
}

function removePrefix(arr) {
  return arr.map(function (item) {
    return item.replace(/^yun:|^cloud:/, '');
  });
}

// 非有赞云定制商详场景删掉主包商详文件
function removeMainPkgGoods() {
  const nativePages = fs.readJsonSync(join(root, 'mp-diy-config-native.json'));
  const rantaPages = fs.readJsonSync(join(root, 'mp-diy-config-ranta.json'));
  const removePrefixRantaPages = removePrefix(rantaPages);
  const rantaGoodsJSON = fs.readJsonSync(
    resolve(root, 'src/ranta-config/bizs/goods-detail/index.page.json')
  );

  // 原生定制场景不移除（要兼容中台化定制场景）
  if (
    nativePages.includes(OLD_GOODS_PATH) &&
    !removePrefixRantaPages.includes(rantaGoodsJSON.id)
  ) {
    return;
  }

  // KQ Step1. 零售客群小程序，保留原有主包路径
  if (isRetailKQ) return;

  const goodsMainPkgPath = join(root, 'src', 'pages', 'goods', 'detail');

  if (!isIsvBuildServer()) {
    const restoreFn2 = snapshotMainPkgGoods();
    onExitProcess(restoreFn2);
  }
  fs.removeSync(goodsMainPkgPath);
}

/** TODO 商品详情标品 build 前需要对 goods-v2 进行处理 */
function goodsDetailCustomCompile() {
  // if (isProd() && !isIsvBuildServer()) {
  //   const goodsV2Path = join(root, 'src', 'packages', 'goods-v2');
  //   const goodsV3Path = join(root, 'src', 'packages', 'goods-v3');
  //   const restoreFn = snapshotGoodsV2();
  //   onExitProcess(restoreFn);
  //   // 删除 goods-v2
  //   fs.removeSync(goodsV2Path);
  //   // 将中间跳转页复制到 goods-v2 中
  //   fs.copySync(goodsV3Path, goodsV2Path);
  // }

  /**
   * 放开 publish ext 的场景
   */
  if (process.argv.includes('ext') && process.argv.includes('publish')) {
    return;
  }

  removeMainPkgGoods();
  // KQ 零售客群小程序，商详重新回到分包 - 通过kq插件处理
}

function getGoodsDetailDropModules(isRetail) {
  const dropList = [];
  const rantaPages = fs.readJsonSync(join(root, 'mp-diy-config-ranta.json'));
  const removePrefixRantaPages = removePrefix(rantaPages);
  const rantaGoodsJSON = fs.readJsonSync(
    resolve(root, 'src/ranta-config/bizs/goods-detail/index.page.json')
  );

  if (!isRetail) {
    dropList.push('@ext-tee-wsc-goods/sold-out-rec-shop-block#local');
    dropList.push('@ext-tee-wsc-goods/combine-goods-block#local');
    dropList.push('@ext-tee-wsc-goods/optional-goods-block#local');
    dropList.push('@wsc-tee-trade/retail-order#local');
    dropList.push('@wsc-tee-trade/retail-order-error-info#local');
  }

  // 非中台化定制商详，移除goods-ecloud
  if (!removePrefixRantaPages.includes(rantaGoodsJSON.id)) {
    dropList.push('@ext-tee-wsc-goods/goods-ecloud#local');
  }
  return dropList;
}

function getShareGoodsDropModules(isRetail) {
  const dropList = [];

  if (!isRetail) {
    dropList.push('@ext-tee-wsc-goods/optional-goods-block#local');
  }

  return dropList;
}

module.exports = {
  getGoodsDetialConfig,
  goodsDetailCustomCompile,
  getGoodsDetailDropModules,
  getShareGoodsDropModules,
};
