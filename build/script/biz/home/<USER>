const { join } = require('path');
const { root, fs, execa } = require('@kokojs/shared');
const { isRetailBiz } = require('../retail');

const OLD_HOME = {
  root: 'packages/old-home',
  name: '首页分包2',
  pages: [
    'feature/index',
    'dashboard/index',
    'tab/one',
    'tab/two',
    'tab/three',
    'bear/index',
  ],
};
const appJsonPath = join(root, 'src', 'app.json');
const retailAppJsonPath = join(root, 'src', 'app-retail.json');

function injectOldhome() {
  if (isRetailBiz) {
    const appJsonPath = join(root, 'src', 'app.json');

    const content = JSON.parse(
      fs.readFileSync(appJsonPath, { encoding: 'utf-8' })
    );

    content.subPackages.push(OLD_HOME);

    fs.writeFileSync(appJsonPath, JSON.stringify(content, null, 2));
  }
}

function getGistStatus() {
  try {
    const res = execa.sync('git', ['status', '--porcelain']);
    return res.stdout;
  } catch (error) {
    return '';
  }
}

function restoreAppJson() {
  const res = getGistStatus();
  if (res.includes('app.json') || res.includes('app-retail.json')) {
    try {
      execa.sync('git', ['checkout', appJsonPath]);
      execa.sync('git', ['checkout', retailAppJsonPath]);
    } catch (error) {
      // ignore
    }
  }
}

module.exports = {
  injectOldhome,
  restoreAppJson,
};
