const { TARGET_TYPE } = require('./common/constants');
const {
  isNativeCustom,
  isRantaCustom,
} = require('@youzan/ranta-mp-ext-adapter-builder');

const root = process.cwd();

// 和 weapp/wsc/shared/utils/trade/navigate-common.js 保持一致
// 里面的 key 需要和 build/biz/dmc/index.js 的映射保持一致
const ROUTE_CONF = {
  paid: {
    NEW: 'packages/paid/pay-result/success/index',
    OLD: 'packages/order/paid-v1/index',
    TRANS: 'packages/order/paid/index',
  },
  cart: {
    NEW: 'packages/trade-cart/cart/index',
    OLD: 'packages/trade/cart-v1/index',
    TRANS: 'packages/goods/cart/index',
  },
  detail: {
    NEW: 'packages/trade/order-detail/index',
    OLD: 'packages/trade/order-detail-v1/index',
    TRANS: 'packages/trade/order/result/index',
  },
  order: {
    NEW: 'packages/trade-buy/order/buy/index',
    OLD: 'packages/order-native/index',
    TRANS: 'packages/order/index',
  },
  pay: {
    NEW: 'packages/trade-pay/pay/index',
    OLD: 'packages/order-native/index',
    TRANS: 'packages/order/index',
  },
};

function getPageType(pathName, defaultTarget) {
  if (isRantaCustom(root, ROUTE_CONF[pathName].NEW)) {
    return TARGET_TYPE.RANTA.value;
  }
  if (isNativeCustom(root, ROUTE_CONF[pathName].TRANS)) {
    return TARGET_TYPE.NATIVE.value;
  }
  if (isNativeCustom(root, ROUTE_CONF[pathName].OLD)) {
    return TARGET_TYPE.NATIVE.value;
  }
  return defaultTarget;
}

// 涉及中台化页面的分包
const TRADE_RANTA_PACKAGES = [
  'packages/paid',
  'packages/trade-buy',
  'packages/trade-pay',
  'packages/trade',
  'packages/order',
  'packages/order-native',
  'packages/trade-cart',
  'packages/trade-buy-subpage',
];

// 标品构建原生下单支付可被打掉的分包配置
// TODO 即在云环境下 标品需要加上这部分ability
const ORDER_NATIVE_DROP_CONFIG = {
  subPackages: [
    {
      root: 'packages/order-native',
      pages: [
        'index',
        'invoice/index',
        'coupon/index',
        'idcard/index',
        'contact/index',
        'prepay-card/index',
        'self-fetch-address/index',
        'self-fetch-address-city/index',
        'address-edit/index',
        'address-map/index',
        'address-city/index',
      ],
    },
  ],
};

/**
 * 有赞云构建原生下单支付可被打掉的分包配置
 * 因为有些路径已经开给云了，暂时先保留
 */
const ECLOUD_ORDER_NATIVE_DROP_CONFIG = {
  subPackages: [
    {
      root: 'packages/order-native',
      pages: [
        'index',
        'coupon/index',
        'idcard/index',
        'contact/index',
        'prepay-card/index',
        'self-fetch-address/index',
        'self-fetch-address-city/index',
      ],
    },
  ],
};

module.exports = {
  ROUTE_CONF,
  TRADE_RANTA_PACKAGES,
  ORDER_NATIVE_DROP_CONFIG,
  ECLOUD_ORDER_NATIVE_DROP_CONFIG,
  getPageType,
};
