/**
 * 原生/中台化页面场景下对app.json的修复规则
 */
const { getPageType } = require('./config');
const { TARGET_TYPE } = require('./common/constants');

const PACKAGES_NAME_MAPS = {
  cart: 'packages/trade-cart',
};

const appJsonActions = {
  /**
   * 购物车在原生编译下，删除中台化的preloadRule和分包定义
   */
  cart: {
    [TARGET_TYPE.NATIVE.value]: function (appJson) {
      /**
       * 购物车在编译原生页面时，删除中台化的分包定义与预加载能力
       */
      const { subPackages, preloadRule } = appJson;
      appJson.subPackages = subPackages.filter(
        (vo) => vo.root !== PACKAGES_NAME_MAPS.cart
      );
      Object.keys(preloadRule).forEach((key) => {
        const itemRule = preloadRule[key];
        if (!itemRule?.packages.length) {
          return;
        }
        itemRule.packages = itemRule.packages.filter(
          (name) => name !== PACKAGES_NAME_MAPS.cart
        );
      });
    },
  },
};

function handleAction(appJson, pathName) {
  const actionConfig = appJsonActions[pathName];
  const pageType = getPageType(pathName, TARGET_TYPE.RANTA.value);
  const actionHandler = actionConfig[pageType];
  actionHandler && actionHandler(appJson);
}

const actionKeys = Object.keys(appJsonActions);

function appJsonFixHandler(appJson) {
  try {
    actionKeys.forEach((key) => handleAction(appJson, key));
  } catch (err) {
    console.error('app-json-fix error: ', err);
  }
  return appJson;
}

module.exports = appJsonFixHandler;
