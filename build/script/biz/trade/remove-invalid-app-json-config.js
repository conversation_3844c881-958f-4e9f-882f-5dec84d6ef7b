const fse = require('fs-extra');
const path = require('path');
const { isProd } = require('@kokojs/shared');
const { TRADE_RANTA_PACKAGES } = require('./config');

const appJsonFix = require('./app-json-fix');
const { outputPath } = require('../../../lib/util');

const DIST_PATH = path.join(process.cwd(), outputPath);
const APP_JSON_PATH = path.join(DIST_PATH, 'app.json');

function removeTips(tipsArr = []) {
  if (!tipsArr) {
    return;
  }

  console.log('\n---- app.json  ----');
  tipsArr.forEach((tip) => console.log(tip));
  console.log(`\n(可在 ${APP_JSON_PATH} 查看确认)`);
  console.log('---- app.json  ----\n');
}

/**
 * 生产构建
 * 删除掉交易的空分包以及指向空分包的预加载配置
 */
module.exports = function () {
  if (!isProd()) {
    return;
  }

  const appJson = fse.readJsonSync(APP_JSON_PATH);
  const appSubpackages = appJson.subPackages;
  const appPreloadRule = appJson.preloadRule;
  const emptyRoots = [];
  const consoleList = [];

  TRADE_RANTA_PACKAGES.forEach((root) => {
    const emptyTradePkgIndex = appSubpackages.findIndex(
      (item) => item.root === root && !item.pages.length
    );

    if (emptyTradePkgIndex === -1) {
      return;
    }

    // 移除该分包
    appSubpackages.splice(emptyTradePkgIndex, 1);
    emptyRoots.push(root);
    consoleList.push(`已删除无效分包配置 ${root}`);
  });

  if (!emptyRoots.length) {
    appJsonFix(appJson);
    fse.writeJsonSync(APP_JSON_PATH, appJson);
    return;
  }

  Object.keys(appPreloadRule).forEach((pagePath) => {
    const removeInvalidRuleItem = () => {
      delete appPreloadRule[pagePath];
      consoleList.push(`已删除无效预加载规则 ${pagePath}`);
    };

    const isEmptyRootPage = emptyRoots.some((root) => {
      return pagePath.startsWith(`${root}/`);
    });

    // 兜个底, 如果 path 是一个空分包的子页面, 直接删除这个规则
    if (isEmptyRootPage) {
      return removeInvalidRuleItem();
    }

    const appPreloadRuleItem = appPreloadRule[pagePath] || {};
    if (appPreloadRuleItem.packages) {
      // 去除指向空分包的预加载
      appPreloadRuleItem.packages = appPreloadRuleItem.packages.filter(
        (pkgRoot) => {
          if (emptyRoots.some((item) => item === pkgRoot)) {
            consoleList.push(`已删除无效预加载规则 ${pagePath} | ${pkgRoot}`);
            return false;
          }

          return true;
        }
      );
    }

    // 预加载分包被删空, 这条预加载规则也就没有意义了
    if (!appPreloadRuleItem.packages.length) {
      return removeInvalidRuleItem();
    }
  });

  removeTips(consoleList);

  appJsonFix(appJson);

  fse.writeJsonSync(APP_JSON_PATH, appJson);
};
