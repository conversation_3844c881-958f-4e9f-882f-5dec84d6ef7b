# 交易编译时切流配置说明

> TIPS：编译切流逻辑仅在 build 时生效，dev 依然会打入多版本页面！

**流程概述，在 `config.js` 中（详细说明见后文）**
1. 配置编译策略 `SHUNT_STRATEGY`
2. 在 `TRADE_RANTA_PACKAGES` 补充新涉及的分包，无则略过

## 一、确认新、老页面通过统一跳转方法跳转
如购物车页，需确保购物车的所有上游页面均是通过统一跳转方法跳转过来，这个方法需具备运行时动态切流、有赞云场景走指定版本等能力，形式不限于 phoenix、业务域自己的封装方法、中间页等。

在运行时切流的过程中，即便硬编码到某版本页面也不会出现大问题，新、老页面都有重定向的兜底，但在编译切流的时候，不需要的版本会被直接打掉，跳过来就是故障 +1。

## 二、配置编译策略
有赞云编译切流策略技术方案：https://shimo.im/docs/KrkEVn1xJKF6XVAJ 无权限查看找交易加
标品的编译时切流在运行时之后，确认运行时已经全量且足够稳定了，将运行时的 apollo 请求下掉，如果该页面未上云，旧版直接删除；上云了，去配置标品的编译切流策略。

策略在 `config.js` 的 `SHUNT_STRATEGY` 配置：

``` javascript
const SHUNT_STRATEGY = {
  '@wsc-tee-weapp-trade/cart': {
    name: '购物车',
    dropConfig: {
      [COMPILE_MODE.ECLOUD.value]: [TARGET_TYPE.RANTA.value],
      [COMPILE_MODE.WSC.value]: [TARGET_TYPE.NATIVE.value],
    },
    nativeConfig: {
      subPackages: [
        {
          root: 'packages/trade',
          pages: ['cart-v1/index'],
        },
      ],
    },
  }
}
```

还是以购物车为例说明，`name` 只是为了在控制台输出提示，`dropConfig` 配置的是 **"xx场景下掉xx版本页面"**，这里配的是将哪个版本下掉而不是编入，那上述购物车的配置翻译成人话就是 **"有赞云将中台化购物车下掉，标品将原生版本下掉"**。

如果下掉的是中台化页面，脚本直接根据 key `@wsc-tee-weapp-trade/cart` 就可以找到对应的中台化配置并处理；如果是下掉原生页面，需配置 `nativeConfig`，这里配置和 `app.json` 一样，脚本会根据这个信息在构建前删除 `src/app.json` 里的页面入口，这样该页面就不参与打包了。

## 三、dist/app.json 修正配置
中台化开发是先在 `src/app.json` 申明好了分包、预加载规则，像支付结果就有两条预先申明的配置：
``` json
{
  "subPackages": [
    {
      "root": "packages/paid",
      "name": "支付结果",
      "pages": []
    }
  ],
  "preloadRule": {
    "packages/paid/pay-result/success/index": {
      "network": "wifi",
      "packages": [
        "packages/trade"
      ]
    }
  }
}
```

如果是有赞云场景不打入中台化版本，这两处配置就是无效的，因为 `pay-result/success/index` 页面不存在，对微信小程序来说是非法的直接报错。所以在所有编译流程结束后，`remove-invalid-app-json-config.js` 会去 `dist/app.json` 下检查并修正相关配置。

检查修正范围是以白名单的形式配置在 `config.js` 的 `TRADE_RANTA_PACKAGES` 中。
