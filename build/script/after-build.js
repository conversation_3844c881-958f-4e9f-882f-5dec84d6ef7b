const removeInvalidAppJsonConfig = require('./biz/trade/remove-invalid-app-json-config');
const { restoreAppJson } = require('./biz/home');
const { restoreScriptChangeFiles } = require('./biz/youzanyun/index');
const { removeGuideTeeFiles } = require('./biz/guide/remove-tee-file');
const fse = require('fs-extra');
const path = require('path');

const injectVersion = () => {
  const { APP_VERSION } = process.env;
  if (!APP_VERSION) {
    return;
  }
  const extJson = fse.readJSONSync(
    path.resolve(process.cwd(), 'dist/ext.json')
  );
  extJson.ext.userVersion = APP_VERSION;
  fse.writeJsonSync(path.resolve(process.cwd(), 'dist/ext.json'), extJson);
};

removeInvalidAppJsonConfig();
restoreAppJson();
restoreScriptChangeFiles();
removeGuideTeeFiles();
injectVersion();

if (process.env.TEST_ENV === 'weapptest') {
  const { patchWeapp } = require('@youzan/fe-test-wx-sdk/bin/utils');
  patchWeapp(path.resolve(process.cwd(), 'dist'));
}
