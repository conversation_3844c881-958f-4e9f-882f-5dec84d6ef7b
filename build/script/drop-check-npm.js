// 在引入异步import语法校验时 存量的语法问题
// 目前仅存在于分包 暂时通过逻辑过滤 后续分包体积治理可以作为方向
const leftInvalidSubPackages = [
  {
    resourcePath: 'src/packages/ump/discount-packages/index.js',
    request: 'shared/common/components/base-sku/common/sku-helper',
  },
  {
    resourcePath: 'src/packages/ump/discount-packages/index.js',
    request: 'shared/common/components/base-sku/common/sku-trade-utils',
  },
  {
    resourcePath: 'src/packages/ump/bargain-purchase/home/<USER>',
    request: 'shared/common/components/base-sku/common/sku-helper',
  },
  {
    resourcePath: 'src/packages/ump/bargain-purchase/home/<USER>',
    request: 'shared/common/components/base-sku/common/sku-trade-utils',
  },
  {
    resourcePath: 'src/packages/shop/shopnote/list/index.js',
    request:
      'components/showcase/components/salesman-cube/api/salesman-account',
  },
  {
    resourcePath: 'src/packages/shop/shopnote/mparticle/detail/index.js',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath: 'src/packages/collage/lottery/detail/index.js',
    request: 'shared/common/components/base-sku/common/sku-trade-utils',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/trigger-button/native-mobile/index/index.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/trigger-button/native-user-info/index/index.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/trigger-button/native-user-info/index/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      'shared/packages/goods-shared/detail/containers/goods-detail/components/goods-sku-simple/index.js',
    request: 'shared/common/components/base-sku/common/sku-trade-utils',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-authorize/index/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-authorize/index/index.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-mobile/index/index.js',
    request: '@youzan/passport-tee-shared/native/weapp/utils/env',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-mobile/index/index.js',
    request: '@youzan/passport-tee-shared/native/weapp/utils/global-reader',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-mobile/index/index.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/components/popup/yz-protocol/index/index.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/index/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/index/index.js',
    request: '@youzan/passport-tee-shared/native/weapp/utils/env',
  },
  {
    resourcePath:
      '@youzan/decorate-tee/native/weapp/decorate/coupon/guide-button/index/index.js',
    request: '@youzan/passport-tee',
  },
  {
    resourcePath:
      '@youzan/passport-tee/native/weapp/user-authorize/index/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee/native/weapp/user-authorize/index/index.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      'shared/components/showcase/ump-timelimited-discount/components/countdown-banner/index.js',
    request:
      'shared/components/showcase/captain-components/ump-goods-layout/helper',
  },
  {
    resourcePath:
      'shared/components/showcase/ump-timelimited-discount/components/countdown-banner/index.js',
    request:
      'shared/components/showcase/captain-components/ump-goods-layout/behaviors/countdown-util-behavior',
  },
  {
    resourcePath:
      'shared/components/showcase/ump-timelimited-discount/components/goods-item-expand/index.js',
    request:
      'shared/components/showcase/captain-components/goods-layout/behaviors/goods-item-info-behavior',
  },
  {
    resourcePath:
      'shared/common/components/base-sku/components/presale-sku/presale-sku-actions/index.js',
    request: 'shared/common/components/base-sku/common/sku-behavior',
  },
  {
    resourcePath:
      'shared/common/components/base-sku/components/presale-sku/presale-sku-price/index.js',
    request: 'shared/common/components/base-sku/common/sku-behavior',
  },
  {
    resourcePath:
      'shared/components/feature-sku/components/header-price/index.js',
    request: 'shared/common/components/base-sku/common/sku-behavior',
  },
  {
    resourcePath:
      'src/components/showcase/components/salesman-cube/poster-popup/index.js',
    request: 'components/showcase/components/salesman-cube/helpers',
  },
  {
    resourcePath: 'shared/components/account/inject-protocol/index.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      'src/components/shop-pop-manager/components/marketing-page/index.js',
    request: 'components/shop-pop-manager/behaviors/logger-behaviors',
  },
  {
    resourcePath:
      'src/components/shop-pop-manager/components/member-guide/index.js',
    request: 'components/shop-pop-manager/behaviors/logger-behaviors',
  },
  {
    resourcePath: 'src/components/shop-pop-manager/components/shop-ad/index.js',
    request: 'components/shop-pop-manager/behaviors/logger-behaviors',
  },
  {
    resourcePath:
      'src/components/shop-pop-manager/components/visit-gift/index.js',
    request: 'components/shop-pop-manager/behaviors/logger-behaviors',
  },
  {
    resourcePath:
      'src/components/shop-pop-manager/components/sign-popup/index.js',
    request: 'components/shop-pop-manager/behaviors/logger-behaviors',
  },
  {
    resourcePath: 'src/packages/member-code/components/user-info/index.js',
    request: '@youzan/passport-tee',
  },
  {
    resourcePath:
      'src/packages/collage/groupon/detail/components/groupon-sku/index.js',
    request: 'shared/common/components/base-sku/common/sku-trade-utils',
  },
  {
    resourcePath: 'src/packages/ump/lottery-code/containers/code-sku/index.js',
    request: 'shared/common/components/base-sku/common/sku-trade-utils',
  },
  {
    resourcePath: '@youzan/behavior-verify/src/miniapp/weapp/index.js',
    request: '@youzan/behavior-verify/src/miniapp/weapp/main',
  },
  {
    resourcePath: 'shared/components/recommend/index.js',
    request: 'shared/components/feature-sku/utils',
  },
  {
    resourcePath: '@youzan/zan-pay-weapp/dist/index.js',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      '@youzan/decorate-tee/native/weapp/decorate/shelf-customer-asset/index/index.js',
    request: '@youzan/passport-tee',
  },
  {
    resourcePath:
      '@youzan/decorate-tee/native/weapp/decorate/coupon/index/index.js',
    request: '@youzan/passport-tee',
  },
  {
    resourcePath:
      'shared/components/showcase/ump-timelimited-discount/index.js',
    request:
      'shared/components/showcase/captain-components/ump-goods-layout/helper',
  },
  {
    resourcePath: 'shared/components/showcase/audio/index.js',
    request: 'shared/components/showcase/captain-components/audio/audio-event',
  },
  {
    resourcePath: 'shared/components/feature-sku/index.js',
    request: 'shared/common/components/base-sku/common/sku-trade-utils',
  },
  {
    resourcePath: 'src/components/showcase/components/salesman-cube/index.js',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath: 'src/components/external-sku/index.js',
    request: 'shared/components/feature-sku/utils',
  },
  {
    resourcePath: 'src/components/znb-web-view/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: 'src/components/showcase/index.js',
    request: 'shared/components/feature-sku/utils',
  },
  {
    resourcePath:
      'shared/packages/goods-shared/detail/containers/goods-detail/components/goods-sku-simple/fetch.js',
    request: 'shared/common/components/base-sku/common/sku-helper',
  },
  {
    resourcePath: 'shared/components/feature-sku/utils.js',
    request: 'shared/common/components/base-sku/components/presale-sku/utils',
  },
  {
    resourcePath: 'shared/components/feature-sku/utils.js',
    request: 'shared/common/components/base-sku/common/sku-helper',
  },
  {
    resourcePath: 'shared/components/feature-sku/utils.js',
    request: 'shared/common/components/base-sku/common/sku-trade-utils',
  },
  {
    resourcePath: 'src/packages/goods-list/mixins/sku.js',
    request: 'shared/components/feature-sku/utils',
  },
  {
    resourcePath: 'src/packages/order-native/biz-service/pay-service.js',
    request: '@youzan/zan-pay-weapp',
  },
  {
    resourcePath: 'src/bootstrap/yun-sdk/handle-protocol.js',
    request: 'shared/components/account/protocol/shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/utils.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/service.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: '@youzan/zan-pay-weapp/dist/tracker.js',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/mobile.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/mobile.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/data.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/user.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/user.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/auth.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/auth.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/auth.js',
    request: '@youzan/passport-tee-api/native/weapp/utils/authorize',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/utils.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/utils.js',
    request: '@youzan/passport-tee-api/native/weapp/utils/authorize',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/event/main.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/common/page-root.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      'src/ranta-temp/passport-tee-account-login/ranta-temp/Main.vue',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      'src/ranta-temp/passport-tee-account-login/ranta-temp/Main.vue',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      'src/ranta-temp/ext-tee-wsc-goods-image-block/ranta-temp/Widget.vue',
    request: '@youzan/wsc-tee-goods-common/components/danmaku/constants',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/ranta-temp/Widget.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath: 'src/ranta-temp/wxvideo-order-submit-bar/ranta-temp/Main.vue',
    request: '@youzan/passport-tee-behavior',
  },
  {
    resourcePath: 'src/packages/ump/helpcut/store/actions.js',
    request: 'shared/common/components/base-sku/common/sku-helper',
  },
  {
    resourcePath: 'src/packages/ump/helpcut/store/actions.js',
    request: 'shared/common/components/base-sku/common/sku-trade-utils',
  },
  {
    resourcePath: 'src/packages/ump/lottery-code/store/actions.js',
    request: 'shared/common/components/base-sku/common/sku-helper',
  },
  {
    resourcePath: 'src/packages/collage/groupon/detail/store/actions.js',
    request: 'shared/common/components/base-sku/common/sku-helper',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/user.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/user.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      'src/ranta-temp/assets-tee-extensions-cashier/components/index.vue',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      'src/ranta-temp/passport-tee-user-authorize/components/UserAuthorizePopup.vue',
    request: '@youzan/passport-tee/lib/user-authorize/popup',
  },
  {
    resourcePath:
      'src/ranta-temp/assets-tee-extensions-cashier/components/index.vue',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ext-tee-assets/extensions/cashier/components/index.vue',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      'src/packages/showcase-template/new-take-away/helpers/index.js',
    request: 'shared/common/components/base-sku/common/sku-helper',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/services/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/event/registry.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/ranta-temp/Widget.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath: '@youzan/passport-tee-api/lib/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: '@youzan/wsc-tee-goods-common/components/sku/Container.vue',
    request: '@youzan/wsc-tee-goods-common/components/sku/mixins',
  },
  {
    resourcePath: '@youzan/wsc-tee-goods-common/components/sku/Container.vue',
    request: '@youzan/wsc-tee-goods-common/components/sku/utils/messages',
  },
  {
    resourcePath: '@youzan/wsc-tee-goods-common/components/sku/Container.vue',
    request: '@youzan/wsc-tee-goods-common/components/sku/utils/action',
  },
  {
    resourcePath: 'src/ranta-temp/sku-order-block/ranta-temp/SkuOrderPopup.vue',
    request: '@youzan/wsc-tee-goods-common/components/sku/mixins',
  },
  {
    resourcePath: 'src/base-api/passport-tee-login.js',
    request: '@youzan/passport-tee',
  },
  {
    resourcePath: 'src/base-api/passport-tee-login.js',
    request: '@youzan/passport-tee-shared/lib/hooks/login',
  },
  {
    resourcePath: '@youzan/zan-pay-weapp/dist/service.js',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/packages/takeaway-search/store/actions.js',
    request: 'shared/common/components/base-sku/common/sku-helper',
  },
  {
    resourcePath:
      '@youzan/passport-tee-behavior/native/weapp/services/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: 'shared/components/showcase-options/behaviors/audio.js',
    request: 'shared/components/showcase/captain-components/audio/audio-event',
  },
  {
    resourcePath: '@youzan/passport-tee/lib/user-authorize/index.vue',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: '@youzan/passport-tee/lib/user-authorize/index.vue',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath: 'src/ranta-temp/passport-tee-user-authorize/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: 'src/ranta-temp/passport-tee-user-authorize/index.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/script/methods/shared.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: 'src/ranta-temp/passport-tee-api/index.ts',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath: 'src/ranta-temp/passport-tee-api/index.ts',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/index.vue',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/index.vue',
    request: '@youzan/passport-tee-shared/lib/utils/env',
  },
  {
    resourcePath:
      'src/ranta-temp/assets-tee-extensions-cashier-pre/ranta-temp/CellGroup.vue',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      'src/ranta-temp/assets-tee-extensions-cashier-pre/ranta-temp/CellGroup.vue',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      'src/ranta-temp/assets-tee-extensions-cashier-pre/ranta-temp/Cashier.vue',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: '@youzan/decorate-tee/src/decorate/coupon/index.vue',
    request: '@youzan/passport-tee',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/components/trigger-button/native-mobile/index.vue',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/components/trigger-button/native-user-info/index.vue',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/components/trigger-button/native-user-info/index.vue',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/decorate-tee/src/decorate/coupon/guide-button/index.vue',
    request: '@youzan/passport-tee',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/utils.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      'src/ranta-temp/assets-tee-extensions-cashier-pre/components/channel-v2.vue',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      'src/ranta-temp/assets-tee-extensions-cashier-pre/components/channel-v2.vue',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: '@youzan/passport-tee-behavior/native/weapp/utils/crypto.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: '@youzan/passport-tee/lib/account/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: '@youzan/passport-tee/lib/account/index.js',
    request: '@youzan/passport-tee-shared/lib/utils/env',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/components/earn-icon/index.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/components/cube-footer/index.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/components/tax-sign-pop/index.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-trade-trade-buy-ump-block/components/display-card/index.vue',
    request: '@youzan/passport-tee/lib/user-authorize/popup',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/components/user-authorize/authorize/main.js',
    request: '@youzan/passport-tee-api/native/weapp/utils/authorize',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/ranta-temp/CubeTabContainer.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/ranta-temp/CubeTabPoster.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/ranta-temp/CubeTabPromote.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath: '@youzan/tee-biz-shop/lib/index.js',
    request: '@youzan/passport-tee/shell',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/components/earn-icon/index.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/components/cube-footer/index.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/components/tax-sign-pop/index.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath: '@youzan/passport-tee-api/lib/login/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/ranta-temp/CubeTabContainer.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-mobile/index.vue',
    request: '@youzan/passport-tee-shared/lib/utils/env',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-mobile/index.vue',
    request: '@youzan/passport-tee-shared/lib/utils/global-reader',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-mobile/index.vue',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-protocol/index.vue',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-authorize/index.vue',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/components/popup/yz-authorize/index.vue',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath: '@youzan/passport-tee-api/lib/user/info/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/ranta-temp/CubeTabPoster.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/ranta-temp/CubeTabPromote.vue',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath: '@youzan/passport-tee-components/lib/services/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/native/weapp/common/logger.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-behavior/native/weapp/utils/query-selector/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/index.js',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath: '@youzan/passport-tee-api/lib/service.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: '@youzan/passport-tee-api/lib/data-center/cache.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/mobile.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/mobile.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/data.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/index.js',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      'shared/packages/goods-shared/detail/containers/shop-block/store/actions.js',
    request: 'shared/components/flow-entrance/api',
  },
  {
    resourcePath:
      'shared/packages/goods-shared/detail/containers/sku-block/store/actions.js',
    request: 'shared/common/components/base-sku/common/sku-trade-utils',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier/request.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: '@youzan/passport-tee-behavior/lib/utils/crypto.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/service.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: '@youzan/passport-tee-api/lib/login/adapter/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      'src/ranta-temp/assets-tee-extensions-cashier-core/request.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      '@youzan/passport-tee-behavior/lib/utils/query-selector/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier/tracker.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier/tracker.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ext-tee-assets/extensions/cashier/tracker.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/auth.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/auth.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/auth.js',
    request: '@youzan/passport-tee-api/lib/utils/authorize',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/utils.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/utils.js',
    request: '@youzan/passport-tee-api/lib/utils/authorize',
  },
  {
    resourcePath: '@youzan/passport-tee-components/lib/common/page-root.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/event/main.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier-pre/service.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier-pre/tracker.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier-pre/service.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier-pre/tracker.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier-pre/request.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier/skynet.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier/request.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier/skynet.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/utils/biz.util.js',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      'src/ranta-temp/wsc-tee-salesman-salesman-cube-block/utils/biz.util.js',
    request: '@youzan/sales-icon-utils',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/event/registry.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier-pre/service.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier-pre/tracker.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier-pre/skynet.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier-pre/request.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: '@youzan/passport-tee/shell/account/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: '@youzan/passport-tee/shell/account/index.js',
    request: '@youzan/passport-tee-shared/lib/utils/env',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/script/methods/shared.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier/service.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier/service.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: '@youzan/passport-tee-behavior/lib/services/index.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: 'src/ext-tee-assets/extensions/cashier/service.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath:
      '@youzan/passport-tee-components/lib/components/user-authorize/authorize/main.js',
    request: '@youzan/passport-tee-api/lib/utils/authorize',
  },
  {
    resourcePath: '@youzan/passport-tee-components/lib/common/audit.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: '@youzan/passport-tee-components/lib/common/audit.js',
    request: '@youzan/passport-tee-api',
  },
  {
    resourcePath: '@youzan/passport-tee-components/lib/common/logger.js',
    request: '@youzan/passport-tee-shared',
  },
  {
    resourcePath: 'src/ranta-temp/assets-tee-extensions-cashier-core/skynet.ts',
    request: '@youzan/zan-pay-core',
  },
  {
    resourcePath: 'src/ranta-temp/ext-tee-wsc-goods-image-block/api.ts',
    request: '@youzan/wsc-tee-goods-common/components/danmaku/types',
  },
  {
    resourcePath: 'src/ranta-temp/sku-order-block/modules/process/actions.js',
    request: '@youzan/wsc-tee-goods-common/components/sku/utils/submit',
  },
];

module.exports = leftInvalidSubPackages;
