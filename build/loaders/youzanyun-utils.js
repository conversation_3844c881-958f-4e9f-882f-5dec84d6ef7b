const PREPARE_FILTER_DIRS = [
  '/app',
  '/app-base',
  '/bootstrap/index',
  '/bootstrap/launch-handlers',
  '/packages/order/paid-v1/index',
  '/packages/order-native/address-map/index',
  '/packages/trade/order/list/index',
  '/packages/trade/order/list-native/index',
  '/packages/trade/order-detail-v1/index',
  '/packages/usercenter/dashboard/index',
  '/packages/collage/groupon/detail/index',
  '/goods-shared/detail/extensions/index',
  '/packages/home/<USER>/index',
  '/pages/home/<USER>/index',
  '/packages/old-home/dashboard/index',
  '/packages/home/<USER>/feature',
  '/pages/home/<USER>/feature',
  '/packages/old-home/home-util/feature',
  '/packages/shop/goods/search/index',
  '/packages/goods-list/search-result/index',
  '/packages/shop/multi-store/index/index',
  '/shared/utils/ranta-sdk/ranta-page',
  '/shared/utils/ranta-sdk/ranta-runtime',
  '/packages/shop/levelcenter/free/index',
  '/packages/shop/levelcenter/plus/index',
  '/shared/components/showcase-options-home/index',
  '/bootstrap/yun-sdk/yun-logger/cloud-logger',
  'shared/components/account/user-authorize/index',
];

const EXT_DIRS = [
  '/ext-tee-passport',
  '/ext-tee-wsc-goods/extensions/base-info-block/store',
  '/ext-tee-wsc-goods/extensions/base-info-block/ranta-temp/Main',
  '/ext-tee-wsc-goods/extensions/fix-bottom-block/modules/bottom-btns/actions',
];

// 为了打包更快 先写单个，后续可以变为数组
const GOODS_BIZ_UTILS = '@youzan/goods-biz-utils/lib/helper/skeleton.js';
// const NODE_MODULES =
module.exports.ifDefMatchPaths = function (resource) {
  if (resource.includes('/node_modules')) {
    return resource.includes(GOODS_BIZ_UTILS);
  }
  // 支持部分子仓库
  if (EXT_DIRS.some((dir) => resource.includes(dir))) {
    return true;
  }
  if (resource.includes('/ranta-temp')) return false;

  return PREPARE_FILTER_DIRS.some((dir) => {
    return resource.includes(dir + '.js');
  });
};