const IF_TEMP = '#ifdef((?:\\s+[\\w=]+)+)';
const ELSE_IF_REG = '#else if((?:\\s+[\\w=]+)+)';
const ELSE_REG = '#else';
const END_REG = '#endif';

// 获取 js style 的注释模版
function getJsRegStr(regStr) {
  return `\\/\\*+\\s*${regStr}\\s*\\*+\\/`;
}

module.exports = function parser(source) {
  // console.log('im in if def zgrong: ', this.resourcePath);
  if (this.cacheable) this.cacheable();

  if (source.indexOf('#ifdef') === -1) return source;

  const { PLATFORM_TYPE = 'weapp' } = process.env;

  let sourceResult = source;

  sourceResult = parseContent(sourceResult, PLATFORM_TYPE, getJsRegStr);

  return sourceResult;
};

function parseContent(content, plat, formatter) {
  if (typeof content !== 'string') return '';
  const ifReg = new RegExp(
    `${formatter(IF_TEMP)}([\\w\\W]*?)${formatter(END_REG)}`,
    'g'
  );

  const env = process.env;

  function includePlat(platString = '') {
    const trimedExp = platString.trim();
    const REG_VARIABLE = /^\w{1,}$/;

    // 匹配表达式为普通变量，env.plat非空即为true
    if (REG_VARIABLE.test(trimedExp)) {
      // 有bug，env压根没有对应的环境，
      // 如果是weapp先默认都是true，因为本身这个脚本就是在小程序跑的
      if (trimedExp === 'weapp') return true;
      return !!env[trimedExp];
    } else if (trimedExp.indexOf('=') > 0) {
      // 匹配表达式为判断格式 aa=xx，那么env[aa] === xx为匹配结果
      const arr = trimedExp.split('=');
      return env[arr[0]] === arr[1];
    }
    return false;
  }

  const elifReg = new RegExp(
    `${formatter(ELSE_IF_REG)}|(${formatter(ELSE_REG)})`,
    'g'
  );
  return content.replace(ifReg, ($, platString, code = '') => {
    // 从 0 开始匹配
    elifReg.lastIndex = 0;
    let lastIndex = 0;
    let isMatched = false;
    let result = null;
    // eslint-disable-next-line no-constant-condition
    while (true) {
      isMatched = includePlat(platString);

      result = elifReg.exec(code);
      if (!result) {
        // 所有 plat 未匹配到 (else & else if), 返回 lastIndex 到最后
        return isMatched ? code.slice(lastIndex) : '';
      }

      // 匹配到 else if, 并且上一个 platString 含有当前 plat 就分割到当前
      if (isMatched) return code.slice(lastIndex, result.index);

      if (result[2]) {
        // 匹配到 else
        platString = plat;
      } else {
        // 下一次查询开始的位置, 和 platform
        platString = result[1];
      }

      lastIndex = elifReg.lastIndex;
    }
  });
}
