## 为什么写这个文档
由于在小程序中 App 是唯一实例，如果不对该对象进行约束，将导致项目难以维护，在该对象上的任何修改都将变得非常危险，所以希望做一个约定。

我们约定 App 实例可以接受放一些什么：
1. 属性。在用户一次访问中，全局不变的，至少含义不会变。  
  a. 应用配置信息  
  b. 登录态  
  c. 用户信息  
  d. 当前访问的店铺信息  

2. 方法。设置或者获取 App 实例属性的方法，已经 App 实例自身生命周期和方法。

我们拒绝继续往 `app.globalData` 上放任何数据
  
### 现在 App 组成
- 登录模块
- 店铺模块
- $store vanx 实例
- 配置信息以及获取配置信息的 API
- 其他应该被废弃的属性和 API

#### 登录模块
从 2.25 版本开始登录模块从启动流程中解偶出来作为完整的独立模块使用。其中包含了会话状态校验，登录以及一些获取数据的方法。

使用方式：
```JavaScript
  import * as authApis from './base-api/auth';
```

该模块对外提供以下方法：
- hasToken(): boolean  判断是否有登录态
- login(): Promise<token>  登录有赞服务器（以前有使用 callback 的方式，虽然保留了，但是不建议继续使用）
- checkSession(): Promise<token> 检查会话状态，现在只是简单检查本地是否存在token
- getToken(key?): object | any  获取后端返回的完整登录态信息（驼峰命名），可以指定获取对应 key 值
- getUserInfo(success: fn, fail: fn)  异步获取用户信息
- getUserInfoSync(key?): userInfo  同步获取用户信息，可以指定获取对应 key 值
- getMobile(): string | null （废弃）
- getBuyerId(): number | null  (废弃)
- getSessionId(): string | null  (废弃)
- getFansType(): string | null  (废弃)
- getAccessToken(): string | null (废弃)

[会话信息和用户信息完整结构](../src/base-api/store.js)

#### 店铺模块
从 2.25 版本开始店铺模块从启动流程中解偶出来作为完整的独立模块使用。该模块分为一个主模块和两个自模块（多网点和多网店）。主模块包括切换店铺，店铺信息获取等，多网点模块包括多网点进店逻辑，多网店同。

使用方式：
```JavaScript
  import * as shopApis from './base-api/shop';
```

默认情况，主模块不包含子模块，如果希望使用多网店和多网点，你可以这样使用：

```JavaScript
  import * as shopApis from './base-api/shop';
  // 如果有多网点业务
  import * as multiStoreApis from './base-api/shop/multi-store';
  // 如果有多网店业务
  import * as chainStoreApis from './base-api/shop/chain-store';

  // 多网点逻辑需要先调用这个方法来监听主模块的事件，默认不监听（其他项目中不需要直接监听）。多网店默认监听
  multiStoreApis.listenMultiStoreResolved();
```

该模块对外提供以下方法：

主模块：
- getKdtId(): number | null  获取店铺ID
- updateKdtId(): any  更新店铺ID，这个 API 暂时没有保证返回值类型一致
- setShopInfo(shopInfo: object): shopInfo 更新店铺信息(合并的方式)，和老的 API 不同，这个 API 只会更新数据，不做其他逻辑（原来的多网点逻辑需要使用 setOfflineId)
- getShopInfoSync(): object 同步获取店铺信息
- getNavConfig(): Promise<object>  获取导航信息
- getShopTheme(): Promise<object>  获取主题信息
- getCopyright(): Promise<object>  获取版权信息
- getShopInfo(): Promise<object>  获取完整店铺信息
- getImBusinessData(): Promise<object>  获取多客服信息
- getHiddenPowerBy(): Promise<object>  获取大客定制信息
- getShopConfigData(): Promise<object>  获取店铺配置信息
- setOfflineId() 兼容子模块
- getOfflineId() 兼容子模块，获取到的一定是空字符串
- getHQKdtId() 兼容子模块，获取到的一定是 `null`

多网点：
- setOfflineId(offlineId: number, cb: fn, options: object) 设置网点 ID
- getOfflineId(): number | null 获取网点ID
- listenMultiStoreResolved() 设置监听主模块通知

多网店：
- getHQKdtId(): number | null 多网店 ID

[店铺信息完整结构](../src/base-api/shop/index.js)

#### $store
`$store` 是提供了和 vuex 基本一致的数据管理工具。该属性由 [base-api/store.js](../src/base-api/store.js) 提供。
其提供了基础的数据和方法，详细信息可以进入文件查看。[如何在项目中使用 vanx 可以查看 vanx 文档](http://gitlab.qima-inc.com/weapp/vanx/blob/hotfix/watcher/README.md)，在项目如果需要额外定义一些全局的属性和方法，你可以：

```JavaScript
  // store.js
  export default {
    namespaced: true,
    state: {},
    actions: {},
    mutations: {},
  };
```

```javascript
  // app.js
  import store from 'wsc/base-api/store';
  import * as authApis from 'wsc/base-api/auth';
  import * as shopApis from 'wsc/base-api/shop';
  import * as multiStoreApis from 'wsc/base-api/shop/multi-store';

  import config from 'config';
  import bizStore from './store';

  store.commit('UPDATE_CONFIG', {
    appId: config.appId,
    clientId: config.clientId,
    clientSecret: config.clientSecret,
    weappType: config.weappType
  });

  // 使用业务名来避免污染基础的数据
  store.registerModule(['业务名'], bizStore);

  App({
    store,
    config,
    ...event,
    ...authApis,
    ...shopApis,
    ...multiStoreApis,
    // 其他内容
  })
```

[其他业务方如何共用代码可以看这里](https://doc.qima-inc.com/pages/viewpage.action?pageId=140706375)

#### 其他