1. 模块

基础模块：@youzan/weapp-utils，vant-weapp，vanx
工具：mini-program-webpack-loader
项目：微商城小程序，有赞小程序，零售小程序

大概介绍模块关系

2. 业务模块

主包：微页面，购物车，个人中心，特殊页面
分包：商品，下单，订单，营销工具，知识付费，各种卡

大概介绍公共业务模块的关系，以及为什么要这么拆分


3. 术语介绍

- 第三方小程序：微信小程序中的一种特殊的小程序，可以通过被授权的方式为其他小程序开发并提交代码。比如我们的“心怡画坊”。
- 专享版：特指商家授权给有赞（心怡画坊）的小程序。所用有的功能即我们开发的小程序版微商城的功能。一般我们把他理解为一个只是 APPID 不同的微商城小程序。
- 公共版：我们有一个小程序 —— “有赞”。商家如果希望在有赞的店铺使用小程序的能力，但是又没有自己的小程序，则可以购买公共版服务，以“有赞”作为载体，享有微商城小程序所有功能。
- 体验版：针对部分参与体验内测的部分店铺的专享版小程序，最新的功能会先在这部分小程序中使用。我们的体验版和微信的体验版是不同的，微信的体验版是指一个小程序的发布状态，我们的体验版是指商家小程序所享有的功能。
- 稳定版：所有非体验版的专享版小程序。稳定版的功能是所有非体验版商家都可以升级到的，而体验版的功能是只有体验商家可升级的。

4. 小程序初始化

4.1 启动流程
- 微信加载小程序代码包
- 框架触发 onLaunch（同时页面也会初始化）
- 检查 app 实例 是否存在 isThirdApp needUpdateKdtIdByServer 两个方法，isThirdApp 方法已经废弃，但是目前还是有做检查。needUpdateKdtIdByServer 方法用于判断更新店铺信息的时机。
- 给 app 实例注册属性和方法，默认配置信息
  - 设置默认属性
  - 设置 API
  - 初始化埋点实例 logger
    - 初始化埋点 SDK
    - 初始化 spm
    - 监听小程序内存报警
    - 设置 logger
    - 设置页面找不到监听
  - 设置店铺操作方法
    - 更新店铺信息
    - 设置获取店铺信息
  - 设置认证方法
    - 检查 session 状态，完成登陆流程
    - 设置 login 方法
  - 设置获取店铺配置方法
  - 设置 db
  - 设置 carmen
  - 设置 storage
  - 设置 request
  - 设置 downloadFile
- 注册性能监控监听：上传微信登陆时间，登陆有赞开始时间，登陆有赞成功时间，启动数据获取成功时间
- 注册登陆成功监听：（特殊逻辑）根据 needUpdateKdtIdByServer 方法来判断是否需要及时更新店铺信息
- 注册更新店铺ID监听：获取并更新店铺数据

- 框架触发 onShow
  - 检查参数是否有 kdt_id（零售和有赞小程序特有）
  - 上报应用打开事件
  - 添加会话级参数
  - 设置页面默认参数
  - 检查参数是否带有网点ID - 触发网点更新逻辑
  - 检查进入页面是不是特殊落地页 - 是特殊落地页则结束
  - 检查会话状态

4.2 APP 实例
属性：
  config 
    - clientId 业务中不用
    - clientSecret 业务中不用
    - common
      - yzLogo
  themeClass
  globalData
    - isRetailApp
    - isYouzanApp
  db：内存存储
  logger：埋点实例
  storage：本地存储
  deviceType 废弃

方法：
  login(Function: complete) 登陆有赞，complete 函数不管成功还是失败都会调用
  carmen(Object: options) Carmen 接口调用方法
  request(Object: options) Node 接口调用方法
  downloadFile(Object: options) wx.downloadFile 调用
  getAppId
  getVersion
  getExtConfig
  getDeviceType

  getKdtId
  getMobile 
  getBuyerId
  getOfflineId

  updateKdtId(Number: kdtId) 更新店铺信息
  getShopTheme
  getShopInfo
  getNavConfig
  getShopConfigData

  getFansType 业务中不用
  getSessionId 业务中不用
  getCopyright 业务中不用
  getAccessToken 业务中不用
  getHiddenPowerBy 业务中不用
  needUpdateKdtIdByServer 业务中不用
  setShopInfo 业务中不用（现在在很多业务中用于设置网点，后边会通过提供 setOfflineId 的方式替换）

4.3 app.carmen 与 app.request 与 app.downloadFile

4.4 app.updateKdtId

1. 开发套路
5.1 wsc-page
5.2 wsc-component
5.3 page-container
5.4 blank-page
5.5 主题颜色使用
5.6. 埋点
5.6.1 自动埋点
5.6.2 手动埋点

6. 全局方法
6.1 api
6.2 lbs
6.3 navigate
6.4 pages-path 配置

7. 调试技巧

7.1 添加编译模式（特殊场景）
7.2 修改 ext.json
7.3 长按底部版权信息
7.4 查询店铺 APPID 在开发者工具复现

