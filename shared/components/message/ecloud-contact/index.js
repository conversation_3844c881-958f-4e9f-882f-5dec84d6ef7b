import WscComponent from 'shared/common/base/wsc-component';
import navigate from '@/helpers/navigate';
import args from '@youzan/weapp-utils/lib/args';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();

WscComponent({
  externalClasses: ['contact-class'],
  properties: {
    style: {
      type: String,
      value: '',
    },

    size: String,
    type: String,
    plain: Boolean,
    disabled: Boolean,
    loading: Boolean,
    formType: String,
    hoverClass: String,
    hoverStopPropagation: Boolean,
    hoverStartTime: Number,
    hoverStayTime: Number,
    lang: String,
    sendMessageTitle: String,
    sendMessagePath: String,
    sendMessageImg: String,
    appParamter: String,
    showMessageCard: String,
  },
  data: {
    im: {
      sourceParam: '',
      businessId: '',
    },
  },

  lifetimes: {
    attached: function attached() {
      this.getDefaultImData();
    },
    moved: function moved() {},
    detached: function detached() {},
  },

  methods: {
    getDefaultImData() {
      app.getDefaultImData().then((im) => {
        this.setYZData({ im });
      });
    },

    handleContact({ detail }) {
      if (detail.path) {
        navigate.navigateTo({
          url: args.add(detail.path, detail.query),
        });
      }
    },
  },
});
