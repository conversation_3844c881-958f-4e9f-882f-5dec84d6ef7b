<button
  wx:if="{{ mode === WX_KF }}"
  style="{{ contactStyle }}"
  type="{{ type }}"
  size="{{ size }}"
  class="contact-class {{opacityFull ? 'opacity-full': ''}}"
  hover-class="{{ hoverClass }}"
  bind:tap="onWeixinKfTap"
>
  <slot></slot>
</button>
<button
  wx:if="{{ mode === WX_IM }}"
  size="{{ size }}"
  style="{{ contactStyle }}"
  class="contact-class {{opacityFull ? 'opacity-full': ''}}"
  type="{{ type }}"
  plain="{{ plain }}"
  disabled="{{ disabled || !sessionFrom}}"
  loading="{{ loading }}"
  form-type="{{ formType }}"
  open-type="{{ openType }}"
  hover-class="{{ hoverClass }}"
  hover-stop-propagation="{{ hoverStopPropagation }}"
  hover-start-time="{{ hoverStartTime }}"
  hover-stay-time="{{ hoverStayTime }}"
  lang="{{ lang }}"
  session-from="{{ sessionFrom }}"
  send-message-title="{{ sendMessageTitle }}"
  send-message-path="{{ sendMessagePath }}"
  send-message-img="{{ sendMessageImg }}"
  show-message-card="{{ showMessageCard }}"
  app-paramter="{{ appParamter }}"
  business-id="{{ businessId }}"
  bind:contact="onContact"
  bind:error="onError"
  bind:opensetting="onOpensetting"
  bind:lauchapp="onLauchapp"
  bind:tap="onWXIMTap"
>
  <slot></slot>
</button>
<button
  wx:elif="{{mode === YZ_IM}}"
  style="{{ contactStyle }}"
  class="contact-class {{opacityFull ? 'opacity-full': ''}}"
  hover-class="{{ hoverClass }}"
  bind:tap="onYZIMTap"
>
  <slot></slot>
</button>
<button
  wx:elif="{{ mode === NO_IM }}"
  size="{{ size }}"
  style="{{ contactStyle }}"
  class="contact-class {{opacityFull ? 'opacity-full': ''}}"
  type="{{ type }}"
  plain="{{ plain }}"
  disabled="{{ disabled }}"
  loading="{{ loading }}"
  form-type="{{ formType }}"
  hover-class="{{ hoverClass }}"
  hover-stop-propagation="{{ hoverStopPropagation }}"
  hover-start-time="{{ hoverStartTime }}"
  hover-stay-time="{{ hoverStayTime }}"
>
  <slot></slot>
</button>

