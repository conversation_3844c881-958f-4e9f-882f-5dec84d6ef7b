import args from '@youzan/weapp-utils/lib/args';
import Storage from '@youzan/weapp-utils/lib/storage';
import Dialog from '@vant/weapp/dist/dialog/dialog';
import WscComponent from 'pages/common/wsc-component';
import {
  fetchContactMode,
  fetchWxKfConfig,
  reportVisitedMmpError,
} from './api';

export const AUTO_IM = 'AUTO_IM'; // 根据店铺规则控制
export const YZ_IM = 'YZ_IM'; // 有赞原生IM
export const WX_IM = 'WX_IM'; // 微信原生IM
export const WX_KF = 'WX_KF'; // 微信客服
export const NO_IM = 'NO_IM'; // 默认点击没有反应,因为小程序异步获取太慢了
const IM_ENTRY_CONFIG_KEY = 'IM_ENTRY_CONFIG';
const CACHE_EXPIRE = 5 / (24 * 60); // 5分钟

export const MMP_CHANNEL = {
  WEBVIEW: 'webview',
  ORIGINAL: 'original',
};

const storage = Storage();
const app = getApp();

WscComponent({
  properties: {
    /**
     * concat 组件提供的属性
     */
    imConcatWay: {
      type: String,
      value: AUTO_IM,
    },
    opacityFull: {
      type: Boolean,
      value: false,
    },
    contactStyle: {
      type: String,
      value: '',
    },
    /**
     * 原生button提供的属性
     */
    size: String,
    type: String,
    plain: Boolean,
    disabled: Boolean,
    loading: Boolean,
    formType: String,
    openType: String,
    hoverClass: String,
    hoverStopPropagation: Boolean,
    hoverStartTime: Number,
    hoverStayTime: Number,
    lang: String,
    sessionFrom: String,
    sendMessageTitle: String,
    sendMessagePath: String,
    sendMessageImg: String,
    appParamter: String,
    showMessageCard: String,
    businessId: String,
    wxKfConfig: Object,
  },
  externalClasses: ['contact-class'],
  data: {
    kfLink: '',
    corpId: '',
    mode: NO_IM,
    WX_IM,
    YZ_IM,
    WX_KF,
    NO_IM,
    // downGradeMode: WX_IM, // 客服模式为微信客服时，降级使用的客服模式
  },
  created() {
    this.init();
  },
  ready() {
    // 一段时间内没有完成初始化就先降级成微信原生客服
    setTimeout(() => {
      if (this.data.mode === NO_IM) {
        app.logger.appError({
          message: '多客服触发降级',
          detail: this.getLogBaseParams(),
        });

        this.setYZData({
          mode: WX_IM,
        });
      }
    }, 10000);
  },
  methods: {
    async init() {
      const { kfLink, corpId } = this.data.wxKfConfig || {};
      if (kfLink && corpId) {
        this.setYZData({
          kfLink,
          corpId,
          mode: WX_KF,
          // downGradeMode: mode === YZ_IM ? YZ_IM : WX_IM,
        });
        return;
      }

      try {
        const cacheData = await storage.getAsync(IM_ENTRY_CONFIG_KEY);
        if (!cacheData) {
          throw new Error('cache expired');
        }

        const { mode, kfLink, corpId } = cacheData.value || {};

        if (kfLink && corpId) {
          this.setYZData({
            kfLink,
            corpId,
            mode: WX_KF,
            // downGradeMode: mode === YZ_IM ? YZ_IM : WX_IM,
          });
          return;
        }

        if (![WX_IM, YZ_IM].includes(mode)) {
          throw new Error('cache no contact mode');
        } else {
          this.setYZData({ mode });
        }
      } catch (e) {
        return this.fetchEntryConfig();
      }
    },
    async fetchEntryConfig() {
      try {
        const canUseWxKf = wx.canIUse('openCustomerServiceChat');
        const [
          contactMode = { mmpChannelType: MMP_CHANNEL.ORIGINAL },
          wxKfConfig,
        ] = await Promise.all([
          fetchContactMode().catch(() => ({
            mmpChannelType: MMP_CHANNEL.ORIGINAL,
          })),
          canUseWxKf
            ? fetchWxKfConfig().catch(() => false)
            : Promise.resolve(false),
        ]);

        // 是否使用 有赞定制版小程序客服消息 模式
        const isMmpWebview = contactMode.mmpChannelType === MMP_CHANNEL.WEBVIEW;
        const mode = isMmpWebview ? YZ_IM : WX_IM;

        // 配置了使用微信客服，优先取微信客服配置，原客服配置作为降级处理
        if (wxKfConfig && wxKfConfig.corpId && wxKfConfig.kfLink) {
          this.setYZData({
            kfLink: wxKfConfig.kfLink,
            corpId: wxKfConfig.corpId,
            mode: WX_KF,
            // downGradeMode: mode,
          });
          storage.setAsync(
            IM_ENTRY_CONFIG_KEY,
            {
              mode: WX_KF,
              kfLink: wxKfConfig.kfLink,
              corpId: wxKfConfig.corpId,
            },
            { expire: CACHE_EXPIRE }
          );
          return;
        }

        storage.setAsync(
          IM_ENTRY_CONFIG_KEY,
          {
            mode,
            kfLink: '',
            corpId: '',
          },
          {
            expire: CACHE_EXPIRE,
          }
        );

        this.setYZData({
          mode,
          kfLink: '',
          corpId: '',
        });
      } catch (e) {
        this.setYZData({ mode: WX_IM });
      }
    },
    // 若接入方没处理bindcontact，会导致客服消息中点击小程序卡片无法跳转到正确路径，且此处易遗漏
    // 此逻辑改为组件内统一处理，不再暴露bindcontact事件event.detail内容(防止重复跳转)，接入方跳转逻辑代码可删除
    onContact(e) {
      if (!e) return;
      const { detail = {} } = e;
      this.triggerEvent('contact', {});
      if (detail.path) {
        wx.navigateTo({
          url: args.add(detail.path, detail.query),
        });
      }
    },
    onError(e) {
      e && this.triggerEvent('error', e.detail);
    },
    onOpensetting(e) {
      e && this.triggerEvent('opensetting', e.detail);
    },
    onLaunchapp(e) {
      e && this.triggerEvent('launchapp', e.detail);
    },

    onIMTap(e) {
      e && this.triggerEvent('tap', e.detail);

      app.logger.skynetLog({
        message: '点击客服会话按钮',
        detail: this.getLogBaseParams(),
      });

      if (!this.data.sessionFrom) {
        app.logger.appError({
          message: 'sessionFrom数据异常',
          detail: this.getLogBaseParams(),
        });
      }
    },

    onWXIMTap(e) {
      this.onIMTap(e);
    },

    onYZIMTap(e) {
      this.onIMTap(e);
      wx.navigateTo({
        url: args.add('/packages/message/contact/index', {
          sessionFrom: encodeURIComponent(this.data.sessionFrom),
        }),
      });
    },
    onWeixinKfTap(e) {
      this.onIMTap(e);
      const { kfLink, corpId } = this.data;
      wx.openCustomerServiceChat({
        extInfo: { url: kfLink },
        corpId,
        fail: (err) => {
          const { errCode, errMsg } = err || {};
          reportVisitedMmpError({
            mmpErrInfo: JSON.stringify({ errCode, errMsg }),
          });
          Dialog.alert({ message: '网络错误，请重试' });
          // this.setYZData({ mode: this.downGradeMode });
        },
      });
    },

    getLogBaseParams() {
      return {
        mode: this.data.mode,
        openType: this.data.openType,
        formType: this.data.formType,
        sessionFrom: this.data.sessionFrom,
        extInfo: { url: this.data.kfLink },
        corpId: this.data.corpId,
      };
    },
  },
});
