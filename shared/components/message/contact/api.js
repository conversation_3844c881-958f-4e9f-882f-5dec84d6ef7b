const app = getApp();

export const fetchContactMode = () => {
  return app.requestUseCdn({
    origin: 'h5',
    path: '/v3/im/api/setting/contact-mode',
    method: 'GET',
  });
};

export const fetchWxKfConfig = () => {
  if (!wx.canIUse('openCustomerServiceChat')) return Promise.resolve(false);

  return app.request({
    origin: 'h5',
    path: '/v3/im/api/weixin-kf/weixin-kf-config.json',
    method: 'GET',
  });
};

export const reportVisitedMmpError = (data) => {
  return app.request({
    path: '/v3/im/api/weixin-kf/report-visited-mmp-error.json',
    method: 'POST',
    withCredentials: true,
    data,
  });
};
