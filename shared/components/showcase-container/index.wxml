<view class="showcase-components-container" wx:if="{{ isShowcaseContainerShow }}" style="{{ themeCss }}">
  <view
    wx:for="{{ curComponents }}"
    wx:for-item="comp"
    wx:for-index="index"
    wx:key="unique"
    class="{{ comp.uuidClass }}"
    id="{{ comp.uuidId }}"
    style="{{ comp.minHeightOpt && comp.minHeightOpt.warpStyle }}"
  >
    <dc-notice
      wx:if="{{ comp.component === 'dc-notice' }}"
      _opt="{{ comp }}"
      theme-colors="{{themeColors}}"
    />

    <dc-coupon
      wx:elif="{{ comp.component === 'dc-coupon' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      need-subscribe-message="{{ true }}"
      bind:handleCouponClick="requestSubscribeMessagePushForTpl"
    />

    <dc-new-zone
      wx:elif="{{ comp.type === 'new_zone' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      need-subscribe-message="{{ true }}"
      data-component="{{ comp }}"
      bind:item-click="handleGoodsClick"
      bind:handleCouponClick="requestSubscribeMessagePushForTpl"
    />

    <dc-elevator-navigation
      wx:elif="{{ comp.type === 'elevator_navigation' }}"
      _opt="{{ comp }}"
      page-scroll-key="{{ pageScrollEvent }}"
      data-component="{{ comp }}"
      theme-colors="{{themeColors}}"
      bind:item-jump="handleGoodsClick"
      bind:item-click="handleElevatorNavigationClick"
    />

    <dc-cube
      wx:elif="{{ comp.component === 'dc-cube' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      extra="{{ extra }}"
      need-subscribe-message="{{true}}"
      data-component="{{ comp }}"
      bind:jumpToLink="handleItemClick"
      bind:handleCouponClick="requestSubscribeMessagePushForTpl"
    />

    <dc-goods
      wx:elif="{{comp.component === 'dc-goods' || comp.type === 'ump_limitdiscount' || comp.type === 'member_goods' || comp.type === 'ump_seckill' || comp.type === 'period_buy' || comp.type === 'groupon' || comp.type === 'point_goods'}}"
      _opt="{{ comp }}"
      _index="{{ index }}"
      kdt-id="{{ extra.kdtId }}"
      uuid="{{ uuid }}"
      data-component="{{ comp }}"
      cloud-logger-field="{{ cloudLoggerField && cloudLoggerField.goods }}"
      bind:button-click="handleBuyClick"
      bind:item-click="handleGoodsClick"
      bind:component-loaded="handleComponentLoaded"
    />

    <dc-goods-tags-left
      wx:elif="{{ comp.type === 'tag_list_left' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      offline-id="{{ extra.offlineId }}"
      app-id="{{ extra.appId }}"
      data-component="{{ comp }}"
      page-scroll-key="{{ pageScrollEvent }}"
      bind:button-click="handleBuyClick"
      bind:item-click="handleGoodsClick"
    />

    <dc-goods-tags-top
      wx:elif="{{ comp.type === 'tag_list_top' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      page-scroll-key="{{pageScrollEvent}}"
      data-component="{{ comp }}"
      bind:button-click="handleBuyClick"
      bind:component-loaded="handleComponentLoaded"
      bind:item-click="handleGoodsClick"
    />

    <dc-text-nav
      wx:elif="{{ comp.type === 'link' }}"
      _opt="{{ comp }}"
      data-component="{{ comp }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-coupon
      wx:elif="{{ comp.component === 'dc-coupon' }}"
      kdt-id="{{ extra.kdtId }}"
      _opt="{{ comp }}"
    />

    <dc-line
      wx:elif="{{ comp.component === 'dc-line' || comp.type === 'white' }}"
      _opt="{{ comp }}"
    />

    <dc-rich-text
      wx:elif="{{ comp.component === 'dc-rich-text' }}"
      _opt="{{ comp }}"
    />

    <showcase-contact-us
      wx:elif="{{ comp.itemType === 'contact_us' }}"
      business-id="{{ extra.businessId }}"
      source-param="{{ extra.sourceParam }}"
      tab-page="{{ extra.inTabPage }}"
      component-data="{{ comp }}"
      index="{{ index }}"
    />

    <dc-present-gift
      wx:elif="{{ comp.component === 'dc-present-gift' && !extra.hideGift }}"
      _opt="{{ comp }}"
    />

    <dc-store
      wx:elif="{{ comp.component === 'dc-store' }}"
      _opt="{{ comp }}"
    />

    <dc-title-text
      wx:elif="{{ comp.component === 'dc-title-text' || comp.type === 'text' || comp.type === 'title' }}"
      _opt="{{ comp }}"
      need-subscribe-message="{{true}}"
      data-component="{{ comp }}"
      bind:jumpToLink="handleItemClick"
      bind:handleCouponClick="requestSubscribeMessagePushForTpl"
    />

    <dc-image
      wx:elif="{{ comp.component === 'dc-image' }}"
      kdt-id="{{ extra.kdtId }}"
      unique-key="{{ curUniqueKey }}"
      _opt="{{ comp }}"
      extra="{{ extra }}"
      need-pre-load="{{ index < 6 }}"
      need-subscribe-message="{{true}}"
      data-component="{{ comp }}"
      bind:component-loaded="handleComponentLoaded"
      bind:jumpToLink="handleItemClick"
      bind:handleCouponClick="onCouponImageClick"
      bind:indexChange="onImageIndexChange"
    />

    <dc-video
      wx:elif="{{ comp.type === 'video' }}"
      _opt="{{ comp }}"
      app-id="{{ extra.appId }}"
      kdt-id="{{ extra.hqKdtId || extra.lsKdtId ||  extra.kdtId }}"
      root-kdt-id="{{ extra.hqKdtId }}"
      multi-size="{{ true }}"
      bind:play="handlePlay"
      bind:ended="handleEnded"
      bind:pause="handlePause"
      bind:videoContext="handleVideoContext"
    />

    <!-- TODO -->
    <!-- <showcase-white
      wx:elif="{{ item.itemType === 'white' }}"
      component-data="{{ item }}"
    /> -->

    <dc-search
      class="cap-search-view"
      wx:elif="{{ comp.component === 'dc-search' }}"
      page-scroll-key="{{ pageScrollEvent }}"
      _opt="{{ comp }}"
      kdt-id="{{extra.kdtId}}"
      feature-alias="{{ alias }}"
      data-component="{{ comp }}"
      bind:tap="loggerComponentClick"
    />

    <dc-hot-words
      wx:elif="{{ comp.component === 'dc-hot-words' }}"
      _opt="{{ comp }}"
      kdt-id="{{extra.kdtId}}"
      theme-colors="{{themeColors}}"
      feature-alias="{{ alias }}"
      data-component="{{ comp }}"
      bind:jumpToLink="handleItemClick"
    />

    <!-- 日食记定制 去掉了 没用 -->
    <!-- <showcase-feature-video-search
      class="cap-search-view"
      wx:elif="{{ item.itemType === 'feature_video_search' }}"
      component-data="{{ item }}"
    />

    <showcase-goods-new
      wx:elif="{{ item.itemType == 'goods_new'}}"
      kdt-id="{{ extra.kdtId }}"
      component-data="{{ item }}"
    />

    <showcase-new-tag-list-left
      wx:elif="{{ item.itemType == 'tag_list_left' }}"
      kdt-id="{{ extra.kdtId }}"
      app-id="{{ extra.appId }}"
      offline-id="{{ extra.offlineId }}"
      template-id="{{ extra.templateId }}"
      component-data="{{ item }}"
      page-lifetimes="{{ ['onPullDownRefresh', 'onPageScroll'] }}"
      bind:buy="handleGoodsBuy"
    /> -->

    <!-- <showcase-tag-list-top
      wx:elif="{{ item.itemType == 'tag_list_top'}}"
      kdt-id="{{ extra.kdtId }}"
      app-id="{{ extra.appId }}"
      offline-id="{{ extra.offlineId }}"
      template-id="{{ extra.templateId }}"
      component-data="{{ item }}"
      page-lifetimes="{{ ['onPullDownRefresh'] }}"
      bind:buy="handleGoodsBuy"
    /> -->

    <dc-shop
      wx:elif="{{ comp.component === 'dc-shop'}}"
      _opt="{{ comp }}"
      data-component="{{ comp }}"
      bind:jumpToLink="shopClick"
    />

    <dc-enter-shop
      wx:elif="{{ comp.component === 'dc-enter-shop'}}"
      _opt="{{ comp }}"
      data-component="{{ comp }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-note-card
      wx:elif="{{ comp.component === 'dc-notecard' }}"
      _opt="{{ comp }}"
    />

    <dc-poster
      wx:elif="{{ comp.type === 'oriented_poster'}}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      buyer-id="{{ extra.buyerId }}"
      data-component="{{ comp }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-crowds-image
      wx:elif="{{ comp.component === 'dc-crowds-image'}}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      extra="{{extra}}"
      feature-alias="alias"
      data-component="{{ comp }}"
      bind:jumpToLink="onCrowdsImageClick"
      bind:indexChange="onCrowdsImageIndexChange"
    />

    <!-- <showcase-feature-video
      wx:elif="{{ item.itemType === 'feature_video'}}"
      kdt-id="{{ extra.kdtId }}"
      hq-kdt-id="{{ hqKdtId }}"
      buyer-id="{{ extra.buyerId }}"
      component-data="{{ item }}"
    /> -->

    <!-- <showcase-rishiji-ump
      wx:elif="{{ item.itemType === 'rishiji_ump'}}"
      kdt-id="{{ extra.kdtId }}"
      buyer-id="{{ extra.buyerId }}"
      component-data="{{ item }}"
      page-lifetimes="{{ ['onPullDownRefresh'] }}"
      page-random-string="{{ pageCommonData.pageRandomString }}"
    /> -->

    <dc-unicashier
      wx:elif="{{ comp.type === 'unicashier'}}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <dc-recommend
      wx:elif="{{ comp.type === 'goods_recommend' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      offline-id="{{ extra.offlineId }}"
      buyer-id="{{ extra.buyerId }}"
      data-component="{{ comp }}"
      bind:item-click="handleGoodsClick"
      bind:button-click="handleBuyClick"
    />

    <dc-fans
      wx:elif="{{ comp.component === 'dc-fans' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      business-id="{{ extra.businessId }}"
      session-from="{{ extra.sourceParam }}"
      shop-info="{{ shopInfo }}"
      data-component="{{ comp }}"
      bind:handleFansClick="onFansClick"
      bind:jumpToLink="handleItemClick"
    />

    <!-- 双十一模板 -->
    <sc-double-eleven
      wx:elif="{{ comp.itemType === 'double_eleven' }}"
      component-data="{{ comp }}"
      data-component="{{ comp }}"
      extra-data="{{ extra }}"
      bind:navigate="onNavigate"
      bind:contactback="onContactBack"
    />

    <dc-audio
      wx:elif="{{ comp.component === 'dc-audio' }}"
      index="{{ index }}"
      _opt="{{ comp }}"
      shop-info="{{ shopInfo }}"
      bind:btn-click="audio__trigger"
      bind:slider-drag="audio__updateProgress"
      bind:src-change="audio__srcChange"
    />

    <!-- 分类模板 -->
    <sc-category
      wx:elif="{{ comp.itemType === 'classification' }}"
      component-data="{{ comp }}"
      data-component="{{ comp }}"
      extra-data="{{ extra }}"
      bind:navigate="onNavigate"
      bind:contactback="onContactBack"
    />

     <!-- 课程组件 -->
    <dc-course
      wx:elif="{{ comp.component === 'dc-course' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{ shopInfo }}"
      theme-colors="{{ themeColors }}"
      buyer-id="{{ extra.buyerId }}"
    />

    <!-- 课程分组 -->
    <dc-course-group
      wx:elif="{{ comp.component === 'dc-course-group' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{ shopInfo }}"
      theme-colors="{{ themeColors }}"
      buyer-id="{{ extra.buyerId }}"
    />

    <dc-regis-form
      wx:elif="{{ comp.component === 'dc-regis-form' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      is-home-page="{{ extra.isHomePage}}"
      feature-alias="{{ alias }}"
      user-id="{{ userId }}"
      theme-colors="{{ themeColors }}"
    />

    <!-- 知识付费组件 -->
    <dc-content
      wx:elif="{{ comp.component === 'dc-content' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{ shopInfo }}"
      theme-colors="{{ themeColors }}"
    />

    <dc-live
      wx:elif="{{ comp.component === 'dc-live' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{ shopInfo }}"
      theme-colors="{{ themeColors }}"
    />

    <dc-member
      wx:elif="{{ comp.component === 'dc-member' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{shopInfo}}"
      theme-colors="{{themeColors}}"
    />

    <dc-column
      wx:elif="{{ comp.component === 'dc-column' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{shopInfo}}"
      theme-colors="{{themeColors}}"
    />

    <!-- punch 没有对应？ -->
    <showcase-punch
      wx:elif="{{ item.itemType === 'punch' }}"
      component-data="{{ item }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <dc-teacher
      wx:elif="{{ comp.component === 'dc-teacher'}}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <!-- <showcase-ump-groupon
      wx:elif="{{ item.itemType == 'groupon'}}"
      kdt-id="{{ extra.kdtId }}"
      app-id="{{ extra.appId }}"
      offline-id="{{ extra.offlineId }}"
      component-data="{{ item }}"
      page-lifetimes="{{ ['onPullDownRefresh'] }}"
      page-data="{{ pageCommonData }}"
      page-random-number="{{ pageRandomNumber }}"
      page-random-string="{{ pageCommonData.pageRandomString }}"
    />

     <showcase-ump-timelimited-discount
      wx:elif="{{ item.itemType === 'ump_limitdiscount' }}"
      component-data="{{ item }}"
      kdt-id="{{ extra.kdtId }}"
      page-data="{{ pageCommonData }}"
      page-lifetimes="{{ ['onPullDownRefresh'] }}"
      page-random-string="{{ pageCommonData.pageRandomString }}"
    /> -->

    <dc-guang
      wx:elif="{{ comp.component === 'dc-guang' }}"
      kdt-id="{{ extra.kdtId }}"
      _opt="{{ comp }}"
    />

    <!-- :offline-id="offlineId"
      :theme-colors="themeColors"
      :_opt="component"
      @click-content="rankingClick(component)"
      @click-more="rankingMoreClick(component)"
      @item-click="rankingItemClick(component)" -->
    <dc-shop-ranking-list
      wx:elif="{{ comp.type === 'shop_ranking_list' }}"
      _opt="{{ comp }}"
      offline-id="{{ extra.offlineId }}"
      theme-colors="{{ themeColors }}"
      data-component="{{ comp }}"
      bind:click-content="rankingClick"
      bind:click-more="rankingMoreClick"
      bind:item-click="rankingItemClick"
    />

    <dc-weapp-live
      wx:elif="{{ comp.type === 'weapp_live' }}"
      _opt="{{ comp }}"
      app-id="{{ extra.appId }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <dc-hotel
      wx:elif="{{ comp.component === 'dc-hotel' || comp.type === 'hotel' || comp.type === 'room_type' || comp.type === 'combo' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      theme-colors="{{ themeColors }}"
      data-component="{{ comp }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-hotel-search
      wx:elif="{{ comp.component === 'dc-hotel-search' || comp.type === 'hotel_search' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      alias="{{ alias }}"
      city="{{ city }}"
      theme-colors="{{ themeColors }}"
      data-component="{{ comp }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-edu-brand
      wx:elif="{{ comp.component === 'dc-edu-brand' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{ shopInfo }}"
      theme-colors="{{ themeColors }}"
    />

    <dc-edu-trial
      wx:elif="{{ comp.component === 'dc-edu-trial' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      shop-info="{{ shopInfo }}"
      theme-colors="{{ themeColors }}"
    />

    <dc-member-value
      wx:elif="{{ comp.type === 'member_value' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      theme-colors="{{ themeColors }}"
    />

    <!-- <showcase-shelf-banner
      wx:elif="{{ item.itemType === 'shelf_banner' && item.isShowCpn === 0 }}"
      component-data="{{ item }}"
      kdt-id="{{ extra.kdtId }}"
      extra-data="{{ extra }}"
      bind:contactback="onContactBack"
      bind:showAuthDialog="handleShowAuthDialog"
    /> -->

    <!-- <showcase-shelf-entry
      wx:elif="{{ item.itemType === 'shelf_order' && item.isShowCpn === 0 }}"
      component-data="{{ item }}"
      bind:showAuthDialog="handleShowAuthDialog"
    /> -->

    <!-- <showcase-shelf-order-pool
      wx:elif="{{ item.itemType === 'shelf_order_pool' }}"
      component-data="{{ item }}"
      kdt-id="{{ extra.kdtId }}"
    /> -->

    <dc-shelf-banner
      wx:elif="{{ comp.type === 'shelf_banner' && comp.isShowCpn === 0 }}"
      _opt="{{ comp }}"
      theme-colors="{{ themeColors }}"
      kdt-id="{{ extra.kdtId }}"
      extra-data="{{ extra }}"
      data-component="{{ comp }}"
      bind:jumpToLink="handleItemClick"
      bind:handleCouponTake="requestSubscribeMessage"
    />

    <dc-shelf-entry
      wx:elif="{{ comp.type === 'shelf_order' && comp.isShowCpn === 0 }}"
      table-code="{{tableCode}}"
      component-data="{{ comp }}"
      data-component="{{ comp }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-shelf-order-pool
      wx:elif="{{ comp.type === 'shelf_order_pool' }}"
      _opt="{{ comp }}"
      theme-colors="{{ themeColors }}"
    />

    <!-- <showcase-shelf-member
      wx:elif="{{ item.itemType === 'shelf_member' && item.isShowCpn === 0 }}"
      component-data="{{ item }}"
    /> -->

    <dc-shelf-customer-asset
      wx:elif="{{ comp.type === 'shelf_asset' }}"
      component-data="{{ comp }}"
      unique-key="{{ curUniqueKey }}"
      theme-colors="{{ themeColors }}"
      kdt-id="{{ extra.kdtId }}"
      data-component="{{ comp }}"
      shop-info="{{ shopInfo }}"
      bind:jumpToLink="handleItemClick"
    />

    <dc-shelf-store-card
      wx:elif="{{ comp.type === 'shelf_nearby_store' }}"
      component-data="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <dc-shelf-member-card
      wx:elif="{{ comp.type === 'shelf_member' && comp.isShowCpn === 0 }}"
      kdt-id="{{ extra.kdtId }}"
      unique-key="{{ curUniqueKey }}"
      component-data="{{ comp }}"
    />

    <dc-reward-points
      wx:elif="{{ comp.type === 'reward_points' }}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      theme-colors="{{ themeColors }}"
    />

    <dc-registration-guide
      wx:elif="{{ comp.type === 'registration_guide'}}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
    />

    <dc-wxvideo-live
      wx:elif="{{comp.component === 'dc-wxvideo-live'}}"
      _opt="{{ comp }}"
      kdt-id="{{ extra.kdtId }}"
      theme-colors="{{ themeColors }}"
    />
  </view>

  <view class="showcase-loading" wx:if="{{ loading }}">
    <van-loading />
  </view>
  <van-dialog
    class-name="dialog-open-setting"
    title="地理位置未授权"
    message="如需使用该小程序，请打开手机的定位授权，开启后重新打开小程序"
    confirm-button-open-type="openSetting"
    show-cancel-button
    cancel-button-text="取消"
    confirm-button-text="去授权"
    show="{{ showAuthDialog }}"
    bind:openSetting="handleHideAuthDialog"
    bind:cancel="handleHideAuthDialog"
  />
  <!-- 需要放在showcase层？ -->
  <subscribe-guide show="{{ showFeatureSubscribeMask }}" bind:close="toggleFeatureSubscribeMask" zIndex="{{ 10010 }}" imgTop="25%"/>
  <view style="position: relative">
    <view id="theme-feature__content-end-hook" style="position: absolute; width: 100vw;height: 1px;"></view>
  </view>
</view>
