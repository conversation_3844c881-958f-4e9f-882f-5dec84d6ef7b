<!-- 实物定金预售sku -->
<template name="presale">
  <base-sku
    theme-class="{{ themeClass }}"
    goods="{{ featureSkuData.skuGoodsDetail }}"
    sku="{{ featureSkuData.sku }}"
    show="{{ featureSkuData.showGoodsSku }}"
    quota="{{ featureSkuData.quota }}"
    quotaUsed="{{ featureSkuData.quotaUsed }}"
    quotaText="{{ featureSkuData.quotaText }}"
    buy-text="{{ featureSkuData.buyText }}"
    hide-stock="{{ featureSkuData.hideStock }}"
    start-sale-num="{{ featureSkuData.startSaleNum }}"
    properties="{{ featureSkuData.itemSalePropList }}"
    extra-data="{{ extra }}"
    bind:buy="handleBuy"
    bind:sku-close="handleSkuClose"
    show-add-cart-btn="{{ false }}"
    reset-stepper-on-hide
    generic:sku-header-price="presale-header-price"
    generic:sku-actions="presale-actions"
  >
  </base-sku>
</template>
