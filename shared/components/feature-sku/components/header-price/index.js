import WscComponent from 'shared/common/base/wsc-component/index';
import skuBehavior from 'shared/common/components/base-sku/common/sku-behavior';
import theme from 'shared/common/components/theme-view/theme';

function parsePrice(price) {
  return typeof price === 'number' ? Number(price / 100).toFixed(2) : price;
}

WscComponent({
  behaviors: [skuBehavior],

  properties: {
    sku: {
      type: Object,
      observer: 'setPrice',
    },
    selectedSkuComb: {
      type: Object,
      observer: 'setPrice',
    },
    extraData: {
      type: Object,
      observer: 'setPrice',
    },
  },

  data: {
    themeGeneral: '',
  },

  ready() {
    theme.getThemeColor('general').then((color) => {
      this.setYZData({
        themeGeneral: color,
      });
    });
  },

  methods: {
    setPrice() {
      const { sku, selectedSkuComb, extraData } = this.properties;

      if (!sku) {
        return;
      }

      // sku.price是一个格式化好的价格区间
      let { price = '', originPrice = '' } = sku;

      if (selectedSkuComb) {
        const { price: _price, oldPrice: _originPrice } = selectedSkuComb;

        if (_price) {
          price = parsePrice(_price);
        }

        if (_originPrice) {
          originPrice = parsePrice(_originPrice);
        }
      }

      if (extraData && extraData.displayPrice) {
        price = extraData.displayPrice;
      }

      this.setYZData({ price, originPrice });
    },
  },
});
