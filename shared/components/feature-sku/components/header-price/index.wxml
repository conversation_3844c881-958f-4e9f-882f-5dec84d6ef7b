<view class="sc-sku-price {{ themeClass }}">
  <theme-view
    wx:if="{{ extraData.isGroupon }}"
    class="sc-sku-price__label"
    color="general"
  >
    单人购买价：
  </theme-view>

  <theme-view class="sc-sku-price__show" color="general">
    <text class="sc-sku-price__symbol">￥</text><text class="sc-sku-price__num">{{ price }}</text>
  </theme-view>

  <van-tag
    wx:if="{{ extraData.isMember }}"
    custom-class="sc-sku-price__tag"
    color="{{ themeGeneral }}"
    type="danger"
    plain
  >
    会员折扣
  </van-tag>

  <van-tag
    wx:elif="{{ extraData.priceTitle }}"
    custom-class="sc-sku-price__tag"
    color="{{ themeGeneral }}"
    type="danger"
    plain
  >
    {{ extraData.priceTitle }}
  </van-tag>
  <view
    wx:if="{{ extraData.isMember && originPrice }}"
    class="sc-sku-price__origin"
  >
    {{ originPrice }}
  </view>
</view>
