import WscComponent from 'shared/common/base/wsc-component/index';

WscComponent({
  properties: {
    sku: {
      type: Object,
      observer: 'initData',
    },
    selectedSkuComb: {
      type: Object,
      observer: 'initData',
    },
  },

  data: {
    showPlaceHolder: false,
  },

  methods: {
    initData() {
      const { sku, selectedSkuComb } = this.properties;
      if (!sku) return;
      // 字段有可能是null，catch一下
      try {
        const { itemDataModel = {} } = sku;
        const { comboMark = {} } = itemDataModel;
        const { isCombo = false } = comboMark;
        if (isCombo && !selectedSkuComb) {
          this.setYZData({
            showPlaceHolder: true,
          });
        } else {
          this.setYZData({
            showPlaceHolder: false,
          });
        }
      } catch (error) {
        this.setYZData({
          showPlaceHolder: false,
        });
      }
    },
  },
});
