<view class="sku-group-extra" wx:if="{{formatComboDetail}}" style="{{singleSku ? '' : 'margin-top:-12px'}}">
  <view class="title">{{ formatTitle }}已包含</view>
  <view class="combo-list">
    <view class="combo-list__item" wx:for="{{formatComboDetail}}" key="{{item.itemId}}">
      <view class="combo-list__item__image">
        <image class="image" src="{{item.picture[0].url}}" />
      </view>
      <view class="combo-list__main">
        <view class="combo-list__main-title">{{ item.title }}</view>
      </view>
      <view class="combo-list__item__specification" hidden="{{!item.skuInfo}}">
        {{ item.skuInfo }};
      </view>
    </view>
  </view>
</view>