.sku-group-extra {
  background: #fff;
  overflow: hidden;
  position: relative;
  &::after {
    content: '';
    margin: 0 32px;
    border-top: 1px solid #ebedf0;
    position: absolute;
    bottom: 0;
    left: calc(-50%);
    right: calc(-50%);
    transform: scale(0.5);
  }

  .pacleholder {
    height: 201.5px;
  }
}

.title {
  font-size: 14px;
  color: #969799;
  height: 18px;
  margin: 12px 16px;
}

.combo-list {
  overflow-x: auto;
  display: flex;
  margin-bottom: -10px;
  padding-bottom: 10px;
  flex-direction: row;

  &::before,
  &::after {
    content: '';
    width: 16px;
    flex-shrink: 0;
  }

  &__main {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    margin-top: 8px;
    height: 32px;

    &-title {
      color: #323233;
      font-size: 12px;
      line-height: 16px;
      font-weight: 400;
      -webkit-line-clamp: 2;
    }
  }

  &__item {
    width: 104.5px;
    margin-right: 8px;
    padding-bottom: 12px;

    &__image {
      .image {
        width: 104.5px;
        height: 104.5px;
        border-radius: 8px;
        object-fit: cover;
      }
    }

    &__specification {
      margin-top: 4px;
      color: #969799;
      font-size: 12px;
      height: 18px;
      line-height: 18px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      -webkit-line-clamp: 1;
    }
  }

  &__item:last-of-type {
    margin-right: 0;
  }
}
