import WscComponent from 'shared/common/base/wsc-component/index';

WscComponent({
  properties: {
    themeClass: {
      type: String,
    },
    sku: {
      type: Object,
      observer: 'initData',
    },
    selectedSkuComb: {
      type: Object,
      observer: 'initData',
    },
  },

  data: {
    formatComboDetail: null,
    formatTitle: '',
    singleSku: false,
  },

  methods: {
    initData() {
      const { sku, selectedSkuComb } = this.properties;
      if (!sku) return;
      // 字段有可能是null，保护一下
      const { itemDataModel = {}, tree = [] } = sku || {};
      const { comboMark = {} } = itemDataModel || {};
      const { isCombo = false } = comboMark || {};
      if (!isCombo) return;

      if (!selectedSkuComb) {
        this.setYZData({
          formatComboDetail: null,
          formatTitle: '',
        });
        return;
      }
      if (!tree.length) {
        // 没有sku选项的，样式要做个调整，这里存一下状态
        this.setYZData({
          singleSku: true,
        });
      }
      const chosedComboDetail = this.chosedComboDetail();
      const formatComboDetail = this.formatComboDetail(chosedComboDetail);
      const formatTitle = this.formatTitle();
      this.setYZData({
        formatComboDetail,
        formatTitle,
      });
    },
    chosedComboDetail() {
      const { sku = {}, selectedSkuComb } = this.properties;
      const { comboDetailModel = {} } = sku.itemDataModel || {};
      const { comboGroupModels = [] } = comboDetailModel;
      if (comboGroupModels.length === 1) return comboGroupModels[0];
      if (!selectedSkuComb || comboGroupModels.length === 0) return null;
      const { id: skuId } = selectedSkuComb;
      return comboGroupModels.find((item) => item.skuId === skuId) || {};
    },
    formatTitle() {
      const { selectedSkuComb = {} } = this.properties;
      if (selectedSkuComb.sku) {
        return JSON.parse(selectedSkuComb.sku)[0].v;
      }
      return '';
    },
    formatComboDetail(chosedComboDetail) {
      const result = [];
      try {
        (chosedComboDetail.comboSubItemModels || []).forEach((item) => {
          if (item.isDisplay === 1) {
            const {
              skuRelatedModels = [],
              picture = '[]',
              propModels = [],
            } = item;
            const skuInfoArr = []; // 属性描述
            propModels.forEach((propModel) => {
              const { textModels = [] } = propModel;
              const textDesc = textModels.map(
                (textModel) => textModel.textName
              );
              skuInfoArr.push(...textDesc);
            });
            const goodsImg = JSON.parse(picture);
            skuRelatedModels.forEach((skuRelatedModel) => {
              const { sku, combineNum = 1 } = skuRelatedModel;
              const skuArr = (sku && JSON.parse(sku)) || [];
              const skuString = skuArr
                .map((item) => item.v)
                .concat(skuInfoArr)
                .join(';'); // 子商品规格描述
              result.push({
                title: `${item.title} ×${combineNum}`,
                picture: goodsImg,
                skuInfo: skuString,
              });
            });
          }
        });
      } catch (err) {
        console.error(err);
      }
      return result;
    },
  },
});
