import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import get from '@youzan/weapp-utils/lib/get';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
// import skuApi from 'common-api/sku/index';
import { buildPresaleInfo } from 'shared/common/components/base-sku/components/presale-sku/utils';
import { formatSkuTree } from 'shared/common/components/base-sku/common/sku-format-tree';
import { getCouponId } from 'shared/common/components/base-sku/common/sku-get-coupon-id';
import {
  skuTradeUtilsPromise,
  skuApiPromise,
} from 'shared/utils/async-base-sku';

const app = getApp();

// 属于以下活动的商品，直接跳到详情页
const whiteList = [
  '降价拍',
  '团购返现',
  '多人拼团',
  '周期购',
  '定金膨胀',
  '阶梯拼团',
  '积分兑换',
];
const activityTitle = {
  customerDiscount: '会员折扣',
  timelimitedDiscount: '限时折扣',
};

function parsePrice(sku, goodsPreference) {
  sku.originPrice = sku.origin || sku.original_price || sku.price;
  sku.price = goodsPreference.show_price;
  const skus = goodsPreference.skus || {};

  sku.list.forEach((sku) => {
    sku.originPrice = sku.origin || sku.price;
    sku.price = (skus[sku.id] && skus[sku.id].price) || sku.price;
  });
}

export function getSkuData(alias, options) {
  wx.showToast({
    title: '加载中',
    icon: 'loading',
  });

  if (!alias) {
    return Promise.reject(`alias is ${alias}`);
  }

  const { timingSaleHideBuyBtnInSku } = options || {};

  return skuApiPromise()
    .then(({ getSkuData, getActivity }) => {
      return Promise.all([
        getSkuData(alias, options),
        app.getShopConfigData(),
        Promise.resolve(getActivity),
      ]);
    })
    .then((res) => {
      return res[2](res[0].brief.item_id).then((list) => {
        // 特定的活动类型，微页面不处理，reject然后跳转商品页
        if (list && whiteList.some((item) => list.indexOf(item) >= 0)) {
          return Promise.reject();
        }
        return res;
      });
    })
    .then(([data, shopConfig = {}]) => {
      wx.hideToast();

      const { brief, sku = {}, components = [], dataVersion } = data;
      const {
        item_id: id,
        title,
        presale = 0,
        presale_info_new: presaleInfoNew,
        quota_used: quotaUsed,
        startSoldTime,
      } = brief;
      const goodsPreference = get(data, 'activity.goods_preference', {});
      const activityType = goodsPreference.type || '';
      const isStarted = get(
        goodsPreference,
        'is_started',
        !(dataVersion && dataVersion === 'v1')
      );
      let priceTitle = goodsPreference.priceTitle || '';
      const startSaleNum = +brief.start_sale_num || 1;
      const itemSalePropList = mapKeysCase.toCamelCase(
        data.item_sale_prop_list || []
      );
      const quotaTexts = startSaleNum > 1 ? [`${startSaleNum}件起售`] : [];
      let { hide_stock: hideStock, cart_text: cartText = '加入购物车' } = sku;
      let { picture, quota, is_virtual: isVirtual } = brief;
      let type = 'default';
      let presaleType; // 预售类型：0-普通，1-定金
      let presaleInfo; // 定金预售的信息
      let showAddCartBtn = true;
      let showBuyBtn = true;
      let isMember = false; // 是否是会员商品
      let isGroupon = false; // 是否拼团商品
      let buyText = '立即购买';
      // cartText = cartText || '加入购物车';

      // 图片
      picture = get(picture, '[0].url', '');

      // 配额
      quota = +quota ? Math.max(quota, 1) : 0;

      // 虚拟商品
      isVirtual = !!+isVirtual;

      // 隐藏库存
      hideStock = !!(+hideStock || +get(components, '0.hide_stock'));

      // 直接跳转商详
      if (
        activityType === 'depositExpansion' ||
        (isVirtual && get(data, 'virtual.validity_type') === 3) || // 价格日历
        Array.isArray(sku) // 无效的 sku，如外链商品的 sku 是 `[]`
      ) {
        return Promise.reject({ jumpToGoodsDetail: true });
      }

      // 售罄
      if (get(sku, 'stock_num') === 0) {
        return Promise.reject('该商品已售罄');
      }

      // 预售
      if (presaleInfoNew) {
        presaleType = +presaleInfoNew.pre_sale_type || 0;
        // 当预售商品未到开始时增加校验
        if (
          presaleInfoNew.pre_sale_start &&
          Date.now() < presaleInfoNew.pre_sale_start
        ) {
          return Promise.reject('该商品未到预售时间');
        }
      }

      // 定金预售。会在 extraData 上挂载 presaleInfo
      if (presaleType === 1) {
        type = 'presale';
        presaleInfo = buildPresaleInfo(sku);
      }

      // 拼团商品
      if (activityType === 'groupOn') {
        isGroupon = true;
        // 商品组件不支持拼团价购买，所以显示的是原价，
        // 所以限购数也需要取商品的原限购数，不能是拼团限购数
        quota = +get(components, '0.quota', quota);
      }

      // 会员折扣
      if (activityType === 'customerDiscount') {
        isMember = true;
      }

      // 价格计算
      try {
        if (
          activityType === 'customerDiscount' ||
          (activityType === 'timelimitedDiscount' && isStarted)
        ) {
          parsePrice(sku, goodsPreference);
        }
      } catch (err) {
        console.log(err);
      }

      // 虚拟、预售、设置了关闭购物车，则隐藏加入购物车按钮
      // 全款预售
      const isFullPresale = presaleType === 0;
      if (
        isVirtual ||
        (!!+presale && !isFullPresale) ||
        +shopConfig.hide_shopping_cart === 1
      ) {
        // return reject({ jumpToGoodsDetail: false });
        showAddCartBtn = false;
        buyText = '下一步';
      }

      if (getCouponId(sku)) {
        buyText = '领券购买';
      }

      // 当显示了加入购物车且没有设置购买按钮，则可以隐藏购买按钮
      // 不能同时隐藏加入购物车和购买按钮
      if (showAddCartBtn && +shopConfig.show_buy_btn !== 1) {
        showBuyBtn = false;
        cartText = '确定';
      }

      /**
       * 一键加购：
       * 1. 商品无规格/商品单规格
       * 2. 通用设置-商品设置-商详页立即购买按钮不开启
       * 3. 通用设置-基础设置-购物车按钮开启
       * 4. 不是虚拟商品
       * 5. 没参加预售活动
       * 6. 没有留言
       * 7. 起售数量 <= 1
       * 8. 没有商品属性
       * 9. 餐饮商品不含有加料
       */
      const {
        noneSku,
        messages,
        cateringGoodsExtraModel,
        list,
        itemDataModel,
        price,
        collectionId,
      } = mapKeysCase.toCamelCase(sku);
      if (
        (noneSku || list.length <= 1) &&
        !showBuyBtn &&
        !messages?.length > 0 &&
        !cateringGoodsExtraModel?.ingredientList?.length &&
        startSaleNum <= 1 &&
        (!itemSalePropList || !itemSalePropList.length)
      ) {
        const skuId = list.length === 1 ? list[0].id : collectionId;
        const selectedSkuValue = list?.[0]?.sku
          ? JSON.parse(list?.[0]?.sku).v
          : '';

        skuTradeUtilsPromise().then(({ addToCart }) => {
          addToCart(
            {
              couponId: null,
              goodsId: id,
              messages: [],
              cartMessages: [],
              num: 1,
              price,
              skuId,
              kdtId: app.getKdtId(),
              propertyIds: [],
              itemDataModel,
              activityType: 0,
              activityId: 0,
              activityAlias: '',
            },
            {
              params: {
                num: 1,
                goods_id: id,
                goods_name: title,
                sku_id: skuId,
                sku_name: selectedSkuValue ? [selectedSkuValue] : [],
                no_sku: !noneSku ? 0 : 1,
              },
            }
          )
            .then(() => {
              if (app.getYouZanYunSdk) {
                const pages = getCurrentPages();
                const currentPage = pages[pages.length - 1];
                const url = `/${currentPage.route}`;
                app.getYouZanYunSdk().app.trigger('beforeAddCart', {
                  skuId,
                  title,
                  id,
                  selectedNum: 1,
                  pageUrl: url,
                });
              }
              app.trigger('component:sku:cart', {
                type: 'add',
              });
            })
            .catch(({ msg }) => {
              wx.showToast({
                title: msg,
                icon: 'none',
              });
            });
        });

        return Promise.resolve({ jumpToGoodsDetail: false });
      }

      if (quota > 0) {
        quotaTexts.push(`每人限购${quota}件`);
      }

      if (!priceTitle && activityType) {
        priceTitle = activityTitle[activityType];
      }

      // 商品信息
      const skuGoodsDetail = {
        id,
        alias,
        title,
        picture: cdnImage(picture, '!300x300.jpg'),
        originPicture: picture,
        isVirtual,
      };

      // https://qima.feishu.cn/docx/doxcnJbZVUlVSvg7m3IGLY3iUye
      // 不能同时隐藏加入购物车和购买按钮
      if (timingSaleHideBuyBtnInSku && startSoldTime) {
        // const startTime
        if (Date.now() < startSoldTime * 1000) {
          showBuyBtn = false;
          showAddCartBtn = true;
        }
      }

      return {
        sku: formatSkuTree(sku),
        skuGoodsDetail,
        type,
        quota,
        quotaUsed,
        quotaText: quotaTexts.join('，'),
        buyText,
        cartText,
        showAddCartBtn,
        showBuyBtn,
        startSaleNum,
        itemSalePropList,
        hideStock: +hideStock === 1,
        showGoodsSku: true,
        extraData: {
          priceTitle,
          isGroupon,
          isMember,
          presaleInfo,
          useCustomHeaderPrice: true,
        },
      };
    })
    .catch((err) => {
      let jumpToGoodsDetail = true;

      if (err && typeof err === 'string' && err !== '不支持会员卡商品') {
        jumpToGoodsDetail = false;
        wx.showToast({
          title: err,
          icon: 'none',
        });
      }

      // 只当 jumpToGoodsDetail 为 false 时才不跳转商详
      if (typeof err === 'object' && err.jumpToGoodsDetail === false) {
        jumpToGoodsDetail = false;
      }

      if (jumpToGoodsDetail) {
        wx.navigateTo({
          url: `/pages/goods/detail/index?alias=${alias}`,
        });
      }

      return Promise.reject(err);
    });
}
