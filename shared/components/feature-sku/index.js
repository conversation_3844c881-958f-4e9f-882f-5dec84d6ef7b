import WscComponent from 'pages/common/wsc-component/index';
import debounce from '@youzan/weapp-utils/lib/debounce';
import getApp from 'shared/utils/get-safe-app';
import { skuTradeUtilsPromise } from 'shared/utils/async-base-sku';

const app = getApp();

WscComponent({
  properties: {
    themeClass: String,
    featureSkuData: {
      type: Object,
      observer: 'observeData',
    },
  },

  data: {
    extra: {},
    show: false,
  },

  ready() {
    this.debouncedCalcPrice = debounce((comb) => {
      skuTradeUtilsPromise().then(({ calcPrice }) => {
        calcPrice({
          kdtId: app.getKdtId(),
          itemId: this.properties.featureSkuData.skuGoodsDetail.id,
          skuId: comb.id,
          itemSalePropList: comb.properties,
        })
          .then((res) => {
            if (res && typeof res.umpTotalPrice !== 'undefined') {
              this.setYZData({
                'extra.displayPrice': (res.umpTotalPrice / 100).toFixed(2),
              });
            }
          })
          .catch((err) => {
            console.log(err);
          });
      });
    }, 500);
  },

  methods: {
    observeData(data) {
      if (!data) {
        return;
      }

      this.setYZData({
        extra: {
          ...this.data.extra,
          ...data.extraData,
        },
      });

      if (data.showGoodsSku) {
        this.setYZData({ show: true });
      } else {
        setTimeout(() => {
          this.setYZData({ show: false });
        }, 500);
      }
    },

    updatePropPrice(data) {
      const { selectedSkuComb: comb } = data;

      const { itemSalePropList: propList } = this.properties.featureSkuData;

      if (propList.length && comb) {
        // 有商品属性，则调接口查价格
        this.debouncedCalcPrice(comb);
      } else {
        // 置空外部传入的展示价格，显示sku组件内部处理的价格
        this.setYZData({
          'extra.displayPrice': '',
        });
      }
    },

    handleSkuSelect(e) {
      this.updatePropPrice(e.detail);
    },

    handlePropSelect(e) {
      this.updatePropPrice(e.detail);
    },

    handleBuy(e) {
      skuTradeUtilsPromise().then(({ goToBuySingle }) => {
        goToBuySingle(this.getPassData(e.detail, true));
      });
    },

    handleAddCart(e) {
      skuTradeUtilsPromise().then(({ addToCart }) => {
        addToCart(this.getPassData(e.detail), this.getLogParams(e.detail))
          .then(() => {
            if (app.getYouZanYunSdk) {
              const pages = getCurrentPages();
              const currentPage = pages[pages.length - 1];
              const url = `/${currentPage.route}`;
              app.getYouZanYunSdk().app.trigger('beforeAddCart', {
                skuId: e.detail.skuId,
                title: this.__preData.featureSkuData.skuGoodsDetail.title,
                id: e.detail.goodsId,
                selectedNum: e.detail.num,
                pageUrl: url,
              });
            }
            this.triggerEvent('close');
            app.trigger('component:sku:cart', {
              type: 'add',
            });
          })
          .catch(({ msg }) => {
            wx.showToast({
              title: msg,
              icon: 'none',
            });
          });
      });
    },

    handleSkuClose() {
      this.triggerEvent('close');
    },

    getPropertyIds(skuComb) {
      if (!skuComb) {
        return [];
      }

      return (skuComb.properties || []).reduce((res, cur) => {
        return res.concat((cur.v || []).map((item) => item.id));
      }, []);
    },

    getLogParams(data) {
      const { num, goodsId, skuId, selectedSkuValues = [] } = data;
      const { featureSkuData: { skuGoodsDetail: { title } = {} } = {} } =
        this.data;

      const params = {
        num,
        goods_id: goodsId,
        goods_name: title,
        sku_id: skuId,
        sku_name: selectedSkuValues,
        no_sku: selectedSkuValues.length ? 0 : 1,
      };

      return { params };
    },

    getPassData(data, isAddActivityParams) {
      // 支持组合商品
      const { sku = {} } = this.properties.featureSkuData;
      const { itemDataModel = {} } = sku;
      const skuId = data.selectedSkuComb.id;
      const { birthdayInfo = {} } = data;
      let activityParams = {
        activityType: 0,
        activityId: 0,
        activityAlias: '',
      };
      // 秒杀需要额外带活动标
      if (isAddActivityParams) {
        const {
          activityType,
          activitySkuIds,
          activityId,
          activityAlias,
          noneSku,
        } = sku.itemActivitySpuModels?.[0] || {};
        if (
          activityType === 6 &&
          (noneSku || activitySkuIds?.includes(skuId))
        ) {
          activityParams = {
            activityType,
            activityId,
            activityAlias,
          };
        }
      }

      return {
        couponId: data.couponId,
        goodsId: data.goodsId,
        messages: data.messages,
        cartMessages: data.cartMessages,
        num: data.selectedNum,
        price: data.price,
        skuId,
        kdtId: app.getKdtId(),
        propertyIds: this.getPropertyIds(data.selectedSkuComb),
        itemDataModel,
        birthdayInfo,
        ...activityParams,
      };
    },
  },
});
