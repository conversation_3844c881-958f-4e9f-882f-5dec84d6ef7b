<!-- 默认 sku -->
<template name="default">
  <base-sku
    theme-class="{{ themeClass }}"
    goods="{{ featureSkuData.skuGoodsDetail }}"
    is-virtual="{{ featureSkuData.skuGoodsDetail.isVirtual }}"
    sku="{{ featureSkuData.sku }}"
    show="{{ featureSkuData.showGoodsSku }}"
    buy-text="{{ featureSkuData.buyText }}"
    cart-text="{{ featureSkuData.cartText }}"
    hide-stock="{{ featureSkuData.hideStock }}"
    quota="{{ featureSkuData.quota }}"
    quotaUsed="{{ featureSkuData.quotaUsed }}"
    quotaText="{{ featureSkuData.quotaText }}"
    show-add-cart-btn="{{ featureSkuData.showAddCartBtn }}"
    show-buy-btn="{{ featureSkuData.showBuyBtn }}"
    start-sale-num="{{ featureSkuData.startSaleNum }}"
    properties="{{ featureSkuData.itemSalePropList }}"
    extra-data="{{ extra }}"
    reset-stepper-on-hide
    generic:sku-header-price="default-header-price"
    bind:sku-selected="handleSkuSelect"
    bind:sku-prop-selected="handlePropSelect"
    bind:buy="handleBuy"
    bind:add-cart="handleAddCart"
    bind:sku-close="handleSkuClose"
    generic:sku-group-extra="combo-goods-sku-group-extra"
    generic:sku-stepper-bottom-block="placeholder-block"
  />
</template>
