import { fetchInfo } from '../api';
import LoggerBehavior from '../behavior';

const app = getApp();

Component({
  behaviors: [LoggerBehavior],

  externalClasses: ['custom-class'],

  properties: {
    bizName: {
      type: String,
      value: '',
    },
  },

  data: {
    info: null,
    bannerStyle: '',
    componentName: 'flow_entrance_banner',
  },

  attached() {
    this.getInfo();
  },

  methods: {
    getInfo() {
      const { bizName } = this.data;
      fetchInfo({
        bizName,
        kdtId: app.getKdtId(),
      }).then((res) => {
        const { open = false, info = null } = res;
        const currentInfo = open && info ? info : null;
        if (currentInfo) {
          const { bannerStyle = '' } = currentInfo;
          this.setData({ info: currentInfo, bannerStyle });
          this.traggerLogger(
            'view',
            'flow_entrance_banner_view',
            '流量入口图片曝光'
          );
        }
      });
    },

    traggerClickLogger() {
      this.traggerLogger(
        'click',
        'flow_entrance_banner_click',
        '流量入口图片点击'
      );
    },
  },
});
