import throttle from 'utils/throttle';
import { isNewIphone } from 'shared/utils/browser/device-type';
import pageScrollBehavior from 'shared/components/showcase/behaviors/page-scroll-control-behavior';
import { checkIsChannels } from 'shared/utils/channel';

import { fetchInfo } from '../api';
import LoggerBehavior from '../behavior';

const app = getApp();
const isIphoneX = isNewIphone();

Component({
  properties: {
    bizName: String,
    closeable: Boolean,
  },

  behaviors: [pageScrollBehavior, LoggerBehavior],

  data: {
    info: null,
    hidden: false,
    isClosed: true,
    closeKey: '',
    isIphoneX,
    iconStyle: '',
    componentName: 'flow_entrance_icon',
    // 是否是视频号场景
    isChannels: checkIsChannels(),
  },

  attached() {
    // 视频号不展示悬浮窗
    if (checkIsChannels()) return;
    this.scrollTop = 0;
    this.cpmputedStatus();
    this.getInfo();
    this.pageKey = this.setPageScrollControlSubscribe(this.scrollHandler, this);
  },

  methods: {
    scrollHandler(ev) {
      if (!this.__handleScroll) {
        this.__handleScroll = throttle(this.handleScroll, 300);
      }
      this.__handleScroll(ev);
    },

    handleScroll(e) {
      const { scrollTop } = e;
      const { isClosed, hidden } = this.data;
      if (isClosed) return;

      const offset = scrollTop - this.scrollTop;
      let _hidden = hidden;

      if (scrollTop <= 0) {
        _hidden = false;
      }

      // 往上滚 展示
      if (offset < 0) {
        _hidden = false;
      } else if (offset > 50) {
        // 往下滚超过 50 展示
        _hidden = true;
      }

      this.scrollTop = scrollTop;

      _hidden !== hidden && this.setData({ hidden: _hidden });
    },

    cpmputedStatus() {
      const { bizName, closeable } = this.data;
      const kdtId = app.getKdtId();
      const closeKey = `FLOW_ICON_CLOSED_${bizName}_${kdtId}`;

      let isClosed = false;

      if (closeable) {
        const value = wx.getStorageSync(closeKey);
        isClosed = !!value;
      }

      this.setData({ isClosed, closeKey });
    },

    getInfo() {
      const { bizName } = this.data;
      fetchInfo({
        bizName,
        kdtId: app.getKdtId(),
      }).then((res) => {
        const { open = false, info = null } = res;
        const currentInfo = open && info ? info : null;
        if (currentInfo) {
          const { iconStyle = '' } = currentInfo;
          this.setData({ info: currentInfo, iconStyle });

          this.traggerLogger(
            'view',
            'flow_entrance_icon_view',
            '流量入口图标曝光'
          );
        }
      });
    },

    handleClose() {
      const { closeKey, closeable } = this.data;
      if (closeable) {
        wx.setStorage({
          key: closeKey,
          data: 1,
        });
      }

      this.setData({
        isClosed: true,
      });
    },

    traggerClickLogger() {
      this.traggerLogger(
        'click',
        'flow_entrance_icon_click',
        '流量入口图标点击'
      );
    },
  },
});
