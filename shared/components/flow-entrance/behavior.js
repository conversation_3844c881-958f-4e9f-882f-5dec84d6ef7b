import { ensureAppLogger } from 'shared/utils/logger-type';
import Args from '@youzan/weapp-utils/lib/args';

const app = getApp();

export default Behavior({
  methods: {
    traggerLogger(et, ei, en) {
      const { info, componentName = '', bizName = '' } = this.data;
      const { linkOpt: { source = '' } = {} } = info;

      const kdtId = app.getKdtId();

      ensureAppLogger({
        et,
        ei,
        en,
        params: {
          biz_name: bizName,
          from_source: `${source}_${kdtId}`,
          component: componentName,
        },
      });
    },

    handelClick() {
      const { linkOpt: { path = '', source = '' } = {} } = this.data.info;
      const kdtId = app.getKdtId();

      if (!path) return;

      this.traggerClickLogger && this.traggerClickLogger();

      const curPath = Args.add(path, {
        from_source: `${source}_${kdtId}`,
        kdt_id: kdtId,
      });

      wx.navigateToMiniProgram({
        appId: 'wxf1fdc416d4ced1b3',
        path: curPath,
      });
    },
  },
});
