import Toast from '@vant/weapp/dist/toast/toast';
import WscComponent from 'pages/common/wsc-component/index';
import getAuthorizedState from 'shared/utils/get-authorized-state';
import getApp from 'shared/utils/get-safe-app';
import {
  TargetType,
  TargetTypeTitle,
  GuideTypeTitle,
  TargetTypeButton,
  ErrorCode,
  LoggerParam,
  CouponTargetTypeButton,
  GuideType,
} from './constant';
import { goToGuide } from './utils';
import { getGuideInfo } from './api';

const app = getApp();

WscComponent({
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持
  },
  properties: {
    guideType: {
      type: String,
      value: '',
    },
    requestParam: {
      type: Object,
      value: {},
    },
    beforGuide: {
      type: Function,
      value: () => {},
    },
    kdtId: {
      type: String || Number,
      value: '',
    },
    redirectUrl: {
      type: String,
      value: '',
    },
    authorizeScene: {
      type: String,
      value: '',
    },
    authTypeList: {
      type: Array,
      value: [],
    },
    hasPopupSlot: {
      type: Boolean,
      value: false,
    },
    customStyle: {
      type: String,
      value: '',
    },
    containerStyle: {
      type: String,
      value: '',
    },
  },
  data: {
    guideInfo: {
      // 引导的身份类型 1：免费会员 2：付费会员 [3：权益卡 | 3: 免费卡 4: 付费卡]
      targetType: TargetType.FreeMember,
      // 会员/权益卡alias（免费会员是等级组alias）
      guideTargetAlias: '',
    },
    // 是否展示弹窗
    showPop: false,
    // 引导弹窗文案
    popupTitle: '',
    // 引导弹窗按钮文案
    confirmButtonText: '',
    // 是否手机号授权
    mobile: false,
  },
  methods: {
    buttonClick() {
      const { beforGuide, requestParam, guideType } = this.properties;
      // 业务操作
      beforGuide()?.catch(async (err) => {
        // 若是身份不符需要引导的错误分支
        // eslint-disable-next-line no-restricted-properties
        if (ErrorCode[guideType].includes(err.code)) {
          const { mobile } = await getAuthorizedState();
          // 获取引导身份信息
          getGuideInfo({
            guideType,
            ...requestParam,
          })
            .then((data) => {
              const {
                needGuide,
                guideType: targetType,
                guideTargetAlias,
              } = data;
              if (needGuide) {
                // 展示埋点
                app.logger &&
                  app.logger.log({
                    et: 'view',
                    ei: `view_${guideType}`,
                    en: `${GuideTypeTitle[guideType]}引导曝光`,
                    params: {
                      targetType: LoggerParam[targetType],
                    },
                  });
                // 展示引导弹窗
                this.setYZData({
                  showPop: true,
                  guideInfo: {
                    targetType,
                    guideTargetAlias,
                  },
                  popupTitle: `${TargetTypeTitle[targetType]}即可${GuideTypeTitle[guideType]}`,
                  confirmButtonText:
                    guideType === GuideType.Coupon
                      ? CouponTargetTypeButton[targetType]
                      : TargetTypeButton[targetType],
                  mobile,
                });
              } else {
                Toast('当前身份不满足条件');
              }
              // 业务方自定义引导逻辑
              this.triggerEvent('afterGetGuide', data);
            })
            .catch((e) => {
              Toast.fail(e?.msg || '引导信息获取失败');
              // 业务方自定义引导逻辑
              this.triggerEvent('catchGuide', e);
            });
        }
      });
    },

    onCancel() {
      this.setYZData({
        showPop: false,
      });
    },

    onConfirm() {
      const { guideInfo, mobile } = this.data;
      const { beforGuide, guideType, redirectUrl, kdtId } = this.properties;
      const { targetType, guideTargetAlias } = guideInfo;
      const guideData = {
        guideType,
        targetType,
        kdtId,
        redirectUrl,
        guideTargetAlias,
      };
      // 点击埋点
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: `click_${guideType}`,
          en: `${GuideTypeTitle[guideType]}引导点击`,
          params: {
            targetType: LoggerParam[targetType],
          },
        });
      if (mobile) {
        // 若已授权手机号，则跳转引导
        goToGuide(guideData);
      } else {
        // 若未授权手机号，则授权后重复业务操作看授权后是否满足身份
        this.setYZData({
          showPop: false,
        });
        beforGuide()?.catch((err) => {
          // eslint-disable-next-line no-restricted-properties
          if (ErrorCode[guideType].includes(err.code)) {
            goToGuide(guideData);
          }
        });
      }
    },
  },
});
