<view style="{{ containerStyle }}">
  <user-authorize
    wx:if="{{ authorizeScene !== '' || authTypeList !== [] }}"
    scene="{{ authorizeScene }}"
    bind:next="buttonClick"
    wrapper-class="auth-btn"
    auth-type-list="{{ authTypeList }}"
  >
    <slot name="guide-button" />
  </user-authorize>
  <view bindtap="buttonClick" wx:else>
    <slot name="guide-button" />
  </view>
  <van-popup custom-class="popup-content" custom-style="{{ customStyle }}" round show="{{ showPop }}">
    <view class="popup-container" wx:if="{{ !hasPopupSlot }}">
      <view class="popup-container__title">{{ popupTitle }}</view>
      <view class="popup-container__msg">当前身份不满足条件</view>
    </view>
    <slot name="popup-container" wx:else></slot>
    <view class="popup-button" bindtap="onCancel">
      <van-button custom-class="popup-button-cancel" type="default" wx:if="{{ !hasPopupSlot }}">取消</van-button>
      <slot name="popup-button-cancel" wx:else></slot>
    </view>
    <view class="popup-button">
      <user-authorize id="user-authorize" authTypeList="{{ ['mobile'] }}" bind:next="onConfirm">
        <van-button custom-class="popup-button-main" type="default" wx:if="{{ !hasPopupSlot }}">{{ confirmButtonText }}</van-button>
        <slot name="popup-button-main" wx:else></slot>
      </user-authorize>
    </view>
  </van-popup>
  <van-toast id="van-toast" />
</view>
