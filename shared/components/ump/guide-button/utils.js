import Args from '@youzan/utils/url/args';
import buildUrl from '@youzan/utils/url/buildUrl';
import navigate from 'shared/utils/navigate';
import { TargetType } from './constant';

export const goToGuide = (data) => {
  const {
    // 引导的身份类型 1：免费会员 2：付费会员 [3：权益卡 | 3: 免费卡 4: 付费卡]
    targetType,
    // 会员/权益卡alias（免费会员是等级组alias）
    guideTargetAlias,
    // 业务类型：优惠券/秒杀/商品
    guideType,
    // 店铺号
    kdtId,
    // 返回地址
    redirectUrl,
  } = data;
  let weappUrl = '';
  if (targetType === TargetType.FreeMember) {
    const h5Url = buildUrl(
      Args.add('/wscuser/levelcenter/fill', {
        kdt_id: kdtId,
        alias: guideTargetAlias,
        fromScene: 'complete',
        guideType,
        redirectUrl,
        eT: Date.now(),
      }),
      'h5',
      kdtId
    );
    weappUrl = `/pages/common/webview-page/index?src=${encodeURIComponent(
      h5Url
    )}`;
  } else if (targetType === TargetType.PaidMember) {
    weappUrl = Args.add('/packages/shop/levelcenter/plus/index', {
      kdt_id: kdtId,
      alias: guideTargetAlias,
      guideType,
      redirectUrl,
    });
  } else if (
    targetType === TargetType.FreeBenefitCard
    || targetType === TargetType.PaidBenefitCard
  ) {
    weappUrl = Args.add('/packages/card/detail/index', {
      alias: guideTargetAlias,
      kdt_id: kdtId,
      shopAutoEnter: 1,
      guideType,
      redirectUrl,
    });
  }
  console.log(weappUrl);
  navigate.navigate({
    url: weappUrl,
  });
};
