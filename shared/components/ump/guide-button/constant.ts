// 优惠券引导无免费/付费权益卡区别，权益卡都是3
export const TargetType = {
  FreeMember: 1,
  PaidMember: 2,
  FreeBenefitCard: 3,
  PaidBenefitCard: 4,
};

export const GuideType = {
  Coupon: 'coupon',
  Goods: 'goods',
  Casino: 'casino',
};

export const ErrorCode = {
  [GuideType.Coupon]: [160700605, 160700606, 161201051, 160700517],
  [GuideType.Goods]: [],
  [GuideType.Casino]: [1605406008, 1605406013],
};

export const TargetTypeTitle = {
  [TargetType.FreeMember]: '开通会员',
  [TargetType.PaidMember]: '开通会员',
  [TargetType.FreeBenefitCard]: '获得权益卡',
  [TargetType.PaidBenefitCard]: '获得权益卡',
};

export const GuideTypeTitle = {
  [GuideType.Coupon]: '领券',
  [GuideType.Goods]: '购买',
  [GuideType.Casino]: '抽奖',
};

export const TargetTypeButton = {
  [TargetType.FreeMember]: '注册会员',
  [TargetType.PaidMember]: '开通会员',
  [TargetType.FreeBenefitCard]: '免费领卡',
  [TargetType.PaidBenefitCard]: '立即开卡',
};

export const CouponTargetTypeButton = {
  [TargetType.FreeMember]: '注册会员',
  [TargetType.PaidMember]: '开通会员',
  [TargetType.FreeBenefitCard]: '立即开卡',
};

export const LoggerParam = {
  [TargetType.FreeMember]: '免费会员',
  [TargetType.PaidMember]: '付费会员',
  [TargetType.FreeBenefitCard]: '权益卡',
  [TargetType.PaidBenefitCard]: '权益卡',
};
