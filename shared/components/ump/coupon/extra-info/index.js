import WscComponent from 'shared/common/base/wsc-component/index';
import mixins from './mixins';

const behaviors = [mixins];

WscComponent({
  behaviors,

  options: {
    multipleSlots: true
  },

  data: {
    visibleCopy: false,
    iconName: 'arrow-down'
  },

  lifetimes: {
    attached() {
      const { visible } = this.data;

      this.setYZData({
        visibleCopy: visible,
        iconName: visible ? 'arrow-up' : 'arrow-down'
      });
    }
  },

  methods: {
    toggleCollapse() {
      const { visibleCopy } = this.data;

      this.setYZData({
        visibleCopy: !visibleCopy,
        iconName: !visibleCopy ? 'arrow-up' : 'arrow-down'
      });

      this.triggerEvent('toggle-collapse', !visibleCopy);
    }
  }
});
