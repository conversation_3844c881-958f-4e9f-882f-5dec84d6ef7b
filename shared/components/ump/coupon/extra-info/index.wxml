<view class="coupon-extrainfo">
  <view class="coupon-extrainfo__line">
    <view class="coupon-extrainfo__line-content">
      <slot />
    </view>

    <van-icon
      wx:if="{{ hasCollapse }}"
      custom-class="coupon-extrainfo__line-icon"
      name="{{ iconName }}"
      bind:click="toggleCollapse"
    />
  </view>

  <view wx:if="{{ hasCollapse && visibleCopy }}" class="coupon-extrainfo__line">
    <slot name="collapsed" />
  </view>
</view>