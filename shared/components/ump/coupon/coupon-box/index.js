import WscComponent from 'shared/common/base/wsc-component/index';
import disabledMixins from '../mixins/disabled-mixins';
import themeClassMixins from '../mixins/theme-class-mixins';

const behaviors = [disabledMixins, themeClassMixins];

WscComponent({
  behaviors,

  externalClasses: ['custom-class', 'custom-action-class'],

  options: {
    multipleSlots: true
  },

  properties: {
    displayType: {
      type: String,
      value: 'card',
      observer: 'computedClass'
    },
    noBoxshadow: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'computedClass'
    },
    collapse: {
      type: Number,
      default: 0,
      observer() {
        this.computedClass();
        this.computedCollapseCls();
      }
    }
  },

  data: {
    boxCls: '',
    collapseCls: '',
    showCollapse: false
  },

  methods: {
    disabledObserver() {
      this.computedClass();
    },

    computedClass() {
      const { displayType, noBoxshadow, collapse, disabled, unavailable } = this.data;
      const isFlat = displayType === 'flat';

      const cls = {
        'coupon-box__box': true,
        'coupon-box__flat': isFlat,
        'coupon-box__card': !isFlat,
        'coupon-box__disabled': disabled,
        'coupon-box__unavailable': unavailable,
        'coupon-box__no-boxshadow': noBoxshadow,
        // 如果有叠加优惠券，前后优惠券的间隔应该变大
        'coupon-box__show-collapse': collapse > 1
      };

      this.setYZData({
        boxCls: Object.keys(cls)
          .filter((key) => cls[key])
          .join(' ')
      });
    },

    computedCollapseCls() {
      const { collapse } = this.data;
      const collapseNum = Math.min(collapse || 0, 3);
      const showCollapse = collapse > 1;

      const cls = {
        'coupon-box__collapse': true,
        [`coupon-box__collapse-${collapseNum}`]: showCollapse
      };

      this.setYZData({
        showCollapse,
        collapseCls: Object.keys(cls)
          .filter((key) => cls[key])
          .join(' ')
      });
    }
  }
});
