@import '../style/mixin.scss', '../style/variables.scss';

.coupon-box {
  @include setFont();

  position: relative;

  &__disabled {
    color: $color-text-disabled;
  }

  &__unavailable {
    color: $color-text-unavailable;
  }

  &__box {
    @include setBoxRadius();

    width: inherit;
    margin-bottom: 12px;
    overflow: hidden;
    font-size: 12px;
    transition: height 0.2s linear;
  }

  &__flat {
    box-sizing: content-box;
    background-color: $color-bg;
    border: 1px solid $color-vice;
  }

  &__card {
    @include setBoxShadowCard(#fff);
  }

  &__section {
    display: flex;
    width: 100%;
  }

  &__block {
    position: relative;
    display: flex;
    align-items: center;

    &:empty {
      display: none;
    }

    &-discount {
      flex: none;
      box-sizing: border-box;
      width: 112px;
      height: inherit;
      padding: 16px 12px;
      overflow: hidden;
    }
    &-content {
      flex: 1;
      width: 0;
      max-width: 100%;
      padding: 22px 8px 16px;
      padding-left: 0;
      color: #323233;
    }
    &-action {
      flex: none;
      min-width: 76px;
      height: inherit;
      box-sizing: border-box;
    }
    &-des {
      box-sizing: border-box;
      width: 100%;
      margin: 0 12px;
      padding: 8px 0;
      border-top: 1px dashed #ebedf0;
    }
  }

  // 折叠优惠券样式
  &__collapse {
    position: absolute;
    bottom: 0;
    z-index: 3;
    width: 100%;

    &-card {
      @include setBoxShadowCard(#fff);
      @include setBoxRadius();

      position: relative;
      bottom: -8px;
      z-index: 2;
      height: 10px;
      margin: 0 7px;

      & + & {
        z-index: 1;
        margin: -6px 14px 0;
        box-shadow: unset;
      }
    }
  }

  /** 额外控制一些边框和折叠效果的样式 */
  &__no-boxshadow {
    box-shadow: unset;
  }

  &__show-collapse {
    margin-bottom: 16px; // 折叠的消费券中间的卡片距离下一组消费券为 12px
    position: relative;
    z-index: 4;
  }

  &__collapse-2 &__collapse-card {
    bottom: -4px;
  }
}
