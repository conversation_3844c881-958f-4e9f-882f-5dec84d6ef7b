# 优惠券组件

Api 和 [captain-ui Coupon](http://fedoc.qima-inc.com/captain/#/coupon) 组件 api 基本保持一致。

可以查看 shared/components/ump/coupon/coupon-demo/index.wxml 了解更多姿势

## TODO
[] 倒计时

## 基本优惠券

```json
{
  "usingComponents": {
    "coupon": "shared/components/ump/coupon/base-coupon/index",
  }
}
```

```html
<coupon
  theme-class="{{ theme-class }}"
  coupon="{{ coupon }}"
  btn-text="去推广"
  collapse="{{ 3 }}"
  coupon-name-suffix="(共3张)"
  top-left-icon="https://img.yzcdn.cn/public_files/87da52370823a5097d1415d58ac01dad.png"
  bottom-right-icon="{{ true }}"
  bind:click-btn="handleBtnClick"
>
  <view slot="btnSuffix">
    <coupon-description description="剩余100张" />
  </view>

  <coupon-extra-info slot="couponExtra" hasCollapse="{{ true }}" style="width: 100%;">
    <span>额外内容</span>
    <view slot="collapsed">
      <span>这是折叠的内容</span>
    </view>
  </coupon-extra-info>
</coupon>
```

```js
WscPage({
  data: {
    coupon: {
      value: '1000000',
      unit: '元',
      threshold: '满20元使用',
      name: '优惠券名称优惠券名称优惠券名称',
      validTime: '2018.04.09-2019.08.10'
    }
  }
});
```