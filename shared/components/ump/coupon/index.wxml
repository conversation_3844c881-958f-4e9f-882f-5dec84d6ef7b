<coupon-box
  theme-class="{{ themeClass }}"
  custom-class="custom-coupon-box-class"
  custom-action-class="custom-coupon-box-action-class"
  display-type="{{ displayType }}"
  disabled="{{ disabled }}"
  unavailable="{{ unavailable }}"
  collapse="{{ collapse }}"
>
  <view slot="A">
    <coupon-discount-box
      theme-class="{{ themeClass }}"
      unit="{{ coupon.unit }}"
      auto-format-value="{{ autoFormatValue }}"
      disabled="{{ disabled }}"
      unavailable="{{ unavailable }}"
      value="{{ coupon.value }}"
      prefix="{{ discountPrefix }}"
      description="{{ coupon.threshold }}"
    />

    <coupon-stamp
      visible="{{ !!topLeftIcon }}"
      position="topLeft"
      width="36px"
      height="36px"
      theme-class="{{ themeClass }}"
      url="{{ topLeftIcon }}"
    />
  </view>

  <coupon-content-box
    theme-class="{{ themeClass }}"
    slot="C"
    content="{{ coupon.name }}"
    disabled="{{ disabled }}"
    unavailable="{{ unavailable }}"
    extra="{{ couponNameSuffix }}"
    description="{{ coupon.validTime }}"
    valid-end-time="{{ coupon.validEndTime }}"
    valid-start-time="{{ coupon.validStartTime }}"
    style="max-width: 100%;"
  />
  
  <view
    wx:if="{{ !hideBtnWrap }}"
    slot="B"
    style="width: 100%;"
  >
    <coupon-checkbox
      wx:if="{{ isCheckbox }}"
      theme-class="{{ themeClass }}"
      value="{{ checkboxValue }}"
      disabled="{{ disabled }}"
      unavailable="{{ unavailable }}"
      bind:change="handleCheckboxChange"
    >
      <slot name="checkbox"></slot>
    </coupon-checkbox>

    <coupon-status
      wx:elif="{{ coupon.status }}"
      status="{{ coupon.status }}"
    />

    <coupon-button
      wx:else
      theme-class="{{ themeClass }}"
      icon="{{ btnIcon }}"
      disabled="{{ disabled }}"
      unavailable="{{ unavailable }}"
      btn-type="{{ btnType }}"
      btn-text="{{ btnText }}"
      bind:click-btn="handleBtnClick"
    >
      <slot slot="prefix" name="btnPrefix" />

      <slot slot="suffix" name="btnSuffix" />
    </coupon-button>

    <coupon-stamp url="{{ getBRUrl }}" theme-class="{{ themeClass }}" visible="{{ showCheckIn }}">
  </view>

  <slot slot="D" name="couponExtra" />
</coupon-box>