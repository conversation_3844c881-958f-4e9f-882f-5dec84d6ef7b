import WscComponent from 'shared/common/base/wsc-component/index';
import disabledMixins from './mixins/disabled-mixins';
import themeClassMixins from './mixins/theme-class-mixins';

const behaviors = [disabledMixins, themeClassMixins];

WscComponent({
  behaviors,

  externalClasses: [
    'custom-coupon-box-class',
    'custom-coupon-box-action-class',
  ],

  properties: {
    coupon: {
      type: Object,
      value: {},
    },

    // 是否是分
    autoFormatValue: Boolean,

    displayType: String,

    collapse: Number,

    topLeftIcon: String,

    bottomRightIcon: {
      type: String,
      optionalTypes: [<PERSON><PERSON><PERSON>],
      observer(bottomRightIcon) {
        this.setYZData({
          showCheckIn: !!bottomRightIcon,
          getBRUrl:
            typeof bottomRightIcon === 'string' ? bottomRightIcon : undefined,
        });
      },
    },

    hideBtnWrap: Boolean,

    discountPrefix: String,

    couponNameSuffix: String,

    isCheckbox: {
      type: Boolean,
      value: false,
    },

    checkboxValue: {
      type: Boolean,
      value: false,
    },

    // actions-button
    btnType: String,
    btnText: String,
    btnIcon: {
      type: String,
      optionalTypes: [Boolean],
      value: 'arrow',
    },
  },

  options: {
    multipleSlots: true,
  },

  data: {
    showCheckIn: false,
    getBRUrl: '',
  },

  methods: {
    handleBtnClick(e) {
      this.triggerEvent('click-btn', e);
    },

    handleCheckboxChange(e) {
      this.triggerEvent('checkbox-change', e);
    },
  },
});
