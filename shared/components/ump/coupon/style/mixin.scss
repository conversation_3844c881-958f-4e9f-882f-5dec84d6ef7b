@mixin setFont {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial,
    Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

@mixin setBoxRadius {
  border-radius: 4px;
}

@mixin setBoxShadowCard($_bgColor) {
  background-color: $_bgColor;
  box-shadow: rgba($color: #000, $alpha: 0.06) 0 2px 6px 1px;
}

@mixin multiEllipsis($lines) {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: $lines;

  /* autoprefixer: ignore next */
  -webkit-box-orient: vertical;
}
