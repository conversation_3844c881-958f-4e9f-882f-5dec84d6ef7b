import { getColorConfig } from '../theme';

export default Behavior({
  properties: {
    themeClass: {
      type: String,
      value: '',
      observer(themeClass) {
        const themeConfig = getColorConfig(themeClass);
        const { mainColor, borderColor, bgColor } = themeConfig;
        let themeMainStyle = '';
        let themeMainBgStyle = '';
        let themeBorderStyle = '';
        let themeBgStyle = '';

        if (mainColor && borderColor && bgColor) {
          themeMainStyle = `color: ${mainColor};`;
          themeMainBgStyle = `background-color: ${mainColor};`;
          themeBorderStyle = `border: 1px solid ${borderColor};`;
          themeBgStyle = `background-color: ${bgColor};`;
        }

        this.setYZData({
          themeConfig,
          themeMainStyle,
          themeBorderStyle,
          themeBgStyle,
          themeMainBgStyle,
        });

        this.themeClassChange && this.themeClassChange(themeConfig);
      },
    },
  },
});
