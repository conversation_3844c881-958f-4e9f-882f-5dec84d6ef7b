export default Behavior({
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer() {
        this.init && this.init();
      }
    },

    position: {
      type: String,
      value: 'bottomRight',
      observer() {
        this.init && this.init();
      }
    },

    url: {
      type: String,
      observer(url) {
        this.setDefaultUrl && this.setDefaultUrl(url);
      }
    },

    width: {
      type: String,
      value: '44px'
    },

    height: {
      type: String,
      value: '44px'
    }
  }
});
