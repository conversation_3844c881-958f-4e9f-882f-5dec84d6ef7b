import WscComponent from 'shared/common/base/wsc-component/index';
import mixins from './mixins';
import themeClassMixins from '../../mixins/theme-class-mixins';

const behaviors = [mixins, themeClassMixins];

WscComponent({
  behaviors,

  data: {
    defaultUrl:
      'https://img.yzcdn.cn/public_files/8f1af7ae720f0aa1634d4fd6f93f7cd6.png',
    cls: '',
    style: '',
  },

  methods: {
    init() {
      const { position, width, height } = this.data;

      const cls = ['coupon-stamp', `coupon-stamp__pos-${position}`];

      this.setYZData({
        cls: cls.join(' '),
        style: `width: ${width}; height: ${height}`,
      });
    },

    themeClassChange() {
      const { url } = this.data;
      this.setDefaultUrl(url);
    },

    setDefaultUrl(url) {
      const { defaultUrl, themeConfig } = this.data;
      this.setYZData({
        defaultUrl: url || themeConfig.stamp || defaultUrl,
      });
    },
  },
});
