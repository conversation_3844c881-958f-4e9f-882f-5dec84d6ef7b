import WscComponent from 'shared/common/base/wsc-component/index';
import mixins from './mixins';

const behaviors = [mixins];
const HIDE_STATE = 1;
const MAP = {
  1: {
    text: '有效的',
    color: '#969799'
  },
  2: {
    text: '已使用',
    color: '#969799'
  },
  3: {
    text: '已过期',
    color: '#969799'
  },
  4: {
    text: '无效的',
    color: '#969799'
  },
  5: {
    text: '已锁定',
    color: '#969799'
  },
  6: {
    text: '已转赠',
    color: '#969799'
  }
};

WscComponent({
  behaviors,

  externalClasses: ['custom-class'],

  options: {
    multipleSlots: true
  },

  ready() {
    this.computedData();
  },

  data: {
    style: '',
    statusData: {}
  },

  methods: {
    computedData() {
      const { color, status, statusText } = this.data;

      let statusData = MAP[status] || {};

      // 状态为 1 时，不显示
      if (status === HIDE_STATE) {
        statusData = {};
      }

      if (statusText) {
        statusData = {
          text: statusText
        };
      }

      const colorData = color || statusData.color;

      this.setYZData({
        statusData,
        style: `color: ${colorData};`
      });
    }
  }
});
