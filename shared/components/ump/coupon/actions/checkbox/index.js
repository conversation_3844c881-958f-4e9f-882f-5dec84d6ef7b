import WscComponent from 'shared/common/base/wsc-component/index';
import mixins from './mixins';
import disabledMixins from '../../mixins/disabled-mixins';
import themeClassMixins from '../../mixins/theme-class-mixins';

const behaviors = [mixins, disabledMixins, themeClassMixins];

WscComponent({
  behaviors,

  externalClasses: ['custom-class'],

  options: {
    multipleSlots: true
  },

  data: {
    themeConfig: {}
  },

  methods: {
    handleChange(e) {
      this.triggerEvent('change', e);
    }
  }
});
