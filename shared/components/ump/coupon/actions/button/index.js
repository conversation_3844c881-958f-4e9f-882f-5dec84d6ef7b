import WscComponent from 'shared/common/base/wsc-component/index';
import mixins from './mixins';
import disabledMixins from '../../mixins/disabled-mixins';
import themeClassMixins from '../../mixins/theme-class-mixins';

const behaviors = [mixins, disabledMixins, themeClassMixins];

WscComponent({
  behaviors,

  options: {
    multipleSlots: true
  },

  data: {
    btnBoxCls: '',
    hasIcon: true
  },

  methods: {
    handleClickBtn(e) {
      const { disabled, unavailable } = this.data;
      if (disabled || unavailable) return;

      this.triggerEvent('click-btn', e);
    },

    disabledObserver() {
      this.init();
    },

    init() {
      const { btnType, disabled, unavailable, icon } = this.data;
      const isFlatBtn = btnType === 'flat';

      const cls = {
        'coupon-button__box': true,
        'coupon-button__flat': isFlatBtn,
        'coupon-button__disabled': disabled,
        'coupon-button__unavailable': unavailable
      };

      this.setYZData({
        hasIcon: icon !== false,
        btnBoxCls: Object.keys(cls)
          .filter((key) => cls[key])
          .join(' ')
      });
    }
  }
});
