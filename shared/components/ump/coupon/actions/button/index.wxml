<view class="coupon-button">
  <view class="coupon-button__placeholder">
    <slot name="prefix" />
  </view>

  <view wx:if="{{ btnText }}" class="{{ btnBoxCls }}" style="{{ btnType ? '' : themeMainBgStyle }}">
    <view
      class="coupon-button__btn"
      catch:tap="handleClickBtn"
    >
      {{ btnText }}
    </view>

    <van-icon wx:if="{{ hasIcon }}" custom-class="coupon-button__icon" name="{{ icon }}" />
  </view>

  <view class="coupon-button__placeholder">
    <slot name="suffix" />
  </view>
</view>