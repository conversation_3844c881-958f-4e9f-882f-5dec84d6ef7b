@import '../../style/variables.scss';

.coupon-button {
  box-sizing: border-box;
  padding-right: 12px;
  position: relative;
  z-index: 1;

  &__placeholder ~ &__box {
    margin-top: 4px;
  }

  &__box ~ &__placeholder {
    margin-top: 4px;
  }

  &__box {
    display: flex;
    align-items: center;
    padding: 0 6px;
    height: 20px;
    color: #fff;
    background-color: $color-primary;
    border-radius: 12px;
    box-sizing: border-box;
  }

  &__flat {
    color: $color-primary;
    background-color: transparent;
    border: 1px solid $color-primary;
  }

  &__disabled,
  &__unavailable {
    color: #fff;
    background-color: #dcdee0;
    border-color: #dcdee0;
  }

  &__btn {
    flex: 1;
    padding: 0;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    background-color: inherit;
    color: inherit;
  }

  &__icon {
    line-height: 16px;
  }
}
