@import '../../style/variables.scss';

.coupon-contentbox {
  width: 100%;
  height: inherit;

  &__section {
    color: $color-text-primary;
    text-align: inherit;
  }

  &__content,
  &__wrap {
    width: inherit;
    overflow: hidden;
  }

  &__content {
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &__wrap {
    margin-top: 12px;
    word-break: break-all;
  }

  &__slot-default {
    display: none;
  }

  &__slot:empty + &__slot-default {
    display: block;
  }

  &__description {
    color: $color-text-primary !important;
  }
}
