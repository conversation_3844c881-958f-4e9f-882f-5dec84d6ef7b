import WscComponent from 'shared/common/base/wsc-component/index';
import contentBodyMixin from '../content-body/mixins';
import descriptionMixin from '../../description/mixins';
import disabledMixins from '../../mixins/disabled-mixins';

const behaviors = [contentBodyMixin, descriptionMixin, disabledMixins];

WscComponent({
  behaviors,

  options: {
    multipleSlots: true
  },

  properties: {},

  data: {
    disabledClass: '',
    unavailableClass: ''
  },

  methods: {
    disabledObserver() {
      const { disabled, unavailable } = this.data;

      this.setYZData({
        disabledClass: disabled ? 'coupon-contentbox__disabled' : '',
        unavailableClass: unavailable ? 'coupon-contentbox__unavailable' : ''
      });
    }
  }
});
