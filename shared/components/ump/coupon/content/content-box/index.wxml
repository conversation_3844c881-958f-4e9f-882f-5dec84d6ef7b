<view class="coupon-contentbox {{ disabledClass }} {{ unavailableClass }}">
  <view class="coupon-contentbox__section coupon-contentbox__content">
    <view class="coupon-contentbox__slot">
      <slot name="C1"></slot>
    </view>

    <content-body
      class="coupon-contentbox__slot-default"
      disabled="{{ disabled }}"
      unavailable="{{ unavailable }}"
      content="{{ content }}" 
      extra="{{ extra }}" 
    />
  </view>

  <view class="coupon-contentbox__section coupon-contentbox__wrap">
    <view class="coupon-contentbox__slot">
      <slot name="C2"></slot>
    </view>

    <description
      class="coupon-contentbox__slot-default"
      custom-class="{{ (disabled || unavailable) ? '' : 'coupon-contentbox__description' }}"
      disabled="{{ disabled }}"
      unavailable="{{ unavailable }}"
      description="{{ description }}"
      valid-end-time="{{ validEndTime }}"
      valid-start-time="{{ validStartTime }}"
    />
  </view>
</view>