import WscComponent from 'shared/common/base/wsc-component/index';
import mixins from './mixins';
import disabledMixins from '../../mixins/disabled-mixins';

const behaviors = [mixins, disabledMixins];

WscComponent({
  behaviors,

  data: {
    disabledClass: '',
    unavailableClass: ''
  },

  methods: {
    disabledObserver() {
      const { disabled, unavailable } = this.data;

      this.setYZData({
        disabledClass: disabled ? 'coupon-contentbody__disabled' : '',
        unavailableClass: unavailable ? 'coupon-contentbody__unavailable' : ''
      });
    }
  }
});
