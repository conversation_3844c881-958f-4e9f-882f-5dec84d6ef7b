@import '../../style/variables.scss';

.coupon-contentbody__disabled {
  .coupon-contentbody {
    &__content,
    &__extra {
      color: $color-text-disabled;
    }
  }
}

.coupon-contentbody__unavailable {
  .coupon-contentbody {
    &__content,
    &__extra {
      color: $color-text-unavailable;
    }
  }
}

.coupon-contentbody {
  display: flex;
  font-size: 14px;
  line-height: 18px;

  &__content {
    overflow: hidden;
    color: $color-text-primary;
    font-weight: 500;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &__extra {
    flex: 0 0 auto;
    color: $color-text-desc;
  }
}
