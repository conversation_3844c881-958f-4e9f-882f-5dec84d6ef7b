<wxs module="util">
module.exports.padZero = function(num, targetLength = 2) {
  var str = num + '';

  while (str.length < targetLength) {
    str = '0' + str;
  }

  return str;
}
</wxs>

<view class="coupon-description custom-class {{ unavailableClass }} {{ disabledClass }}" style="{{ valueStyle }}">
  <view wx:if="{{ computedDescription && computedDescription.length }}" class="coupon-description__content">
    <block wx:if="{{ computedDescription.length === 1 }}">
      {{ computedDescription[0] }}
    </block>

    <block wx:else>
      <view wx:for="{{ computedDescription }}" wx:key="*this">
        {{ item }}
        <block wx:if="{{ index + 1 !== computedDescription.length }}"> - </block>
      </view>
    </block>
  </view>

  <view wx:elif="{{ validEndTime }}" class="coupon-description__countdown-wrap">
    <block wx:if="{{ showTimeData }}">
      <view class="coupon-description__countdown-text">距到期仅剩</view>
      <view wx:if="{{ timeDate.days }}" class="coupon-description__countdown-days">{{ timeDate.days }}天</view>

      <view class="coupon-description__countdown-time">
        <view>{{ util.padZero(timeDate.hours) }}</view>
        <view>:</view>
        <view>{{ util.padZero(timeDate.minutes) }}</view>
        <view>:</view>
        <view>{{ util.padZero(timeDate.seconds) }}</view>
      </view>
    </block>
  </view>

  <van-count-down 
    class="coupon-description__countdown" 
    time="{{ time }}" 
    format="{{ format }}" 
    use-slot="{{ true }}"
    bind:change="handleCountDownChange"
  >

  </van-count-down >
</view>

<!-- 用于动态字体大小计算 -->
<view class="coupon-description-copy">
  <view wx:if="{{ description }}" class="coupon-description-copy__content">
    {{ description }}
  </view>
</view>