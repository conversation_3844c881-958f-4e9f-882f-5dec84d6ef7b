import WscComponent from 'shared/common/base/wsc-component/index';
import mixins from './mixins';
import disabledmixin from '../mixins/disabled-mixins';
import {
  format as formatDate,
  newDate,
} from '@youzan/weapp-utils/lib/time-utils';
import { DISTANCE_2_DAY } from '../constant';

const behaviors = [mixins, disabledmixin];

WscComponent({
  behaviors,

  externalClasses: ['custom-class'],

  options: {
    multipleSlots: true,
  },

  data: {
    computedDescription: [],
    time: 0,
    timeDate: {},
    showTimeData: false,
    valueStyle: '',
    disabledClass: '',
    unavailableClass: '',
  },

  ready() {
    this.initData();

    setTimeout(() => {
      // 动态计算描述字体大小
      this.computedFontSize().then((fontsize) => {
        this.setYZData({
          valueStyle: `font-size: ${fontsize};`,
        });
      });
    });
  },

  methods: {
    async computedFontSize() {
      const { isAuthFontSize, description } = this.data;
      if (!isAuthFontSize || !description) return '12px';

      const MAX_FONT_SIZE = 12;
      const MIN_FONT_SIZE = 10;

      const wrapOffsetWidth = await this.getBoundingClientRectWidth(
        '.coupon-description'
      );
      const wrapCopyOffsetWidth = await this.getBoundingClientRectWidth(
        '.coupon-description-copy'
      );

      const scaleRatio = wrapOffsetWidth / wrapCopyOffsetWidth;

      if (scaleRatio > 1) return MAX_FONT_SIZE + 'px';
      const scaleFontSize = Math.trunc(scaleRatio * MAX_FONT_SIZE);

      return Math.max(scaleFontSize, MIN_FONT_SIZE) + 'px';
    },

    getBoundingClientRectWidth(selector) {
      return new Promise((resolve) => {
        const query = wx.createSelectorQuery().in(this);
        query
          .select(selector)
          .boundingClientRect((res = {}) => {
            resolve(res.width || 0);
          })
          .exec();
      });
    },

    disabledObserver() {
      const { disabled, unavailable } = this.data;

      this.setYZData({
        disabledClass: disabled ? 'coupon-description__disabled' : '',
        unavailableClass: unavailable ? 'coupon-description__unavailable' : '',
      });
    },

    handleCountDownChange({ detail = {} }) {
      this.setYZData(
        {
          showTimeData: true,
          timeDate: {
            ...detail,
          },
        },
        {
          immediate: true,
        }
      );
    },

    initData() {
      this.setYZData({
        time: this.computedTime(),
        computedDescription: this.formatDescription(),
      });
    },

    formatDescription() {
      const { description, validStartTime, validEndTime } = this.data;
      if (description) return description.split('-');

      if (validStartTime && validEndTime) {
        const distance = validEndTime - validStartTime;
        if (distance > DISTANCE_2_DAY) {
          return [
            `${formatDate(newDate(validStartTime), 'yyyy.MM.dd')} `,
            ` ${formatDate(newDate(validEndTime), 'yyyy.MM.dd')}`,
          ];
        }
      }

      return '';
    },

    computedTime() {
      const { validEndTime } = this.data;
      let time = 0;

      if (validEndTime) {
        const timeDistance = validEndTime - +new Date();
        time = Math.max(0, timeDistance);
      }

      return time;
    },
  },
});
