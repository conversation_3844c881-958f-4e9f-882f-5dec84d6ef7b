@import '../style/variables.scss';
@import '../style/mixin.scss';

.coupon-description {
  width: inherit;
  overflow: hidden;
  color: $color-text-desc;
  font-size: 12px;
  line-height: 16px;

  &__disabled {
    .coupon-description {
      &__content {
        color: $color-text-disabled;
      }
    }
  }

  &__unavailable {
    .coupon-description {
      &__content {
        color: $color-text-unavailable;
      }
    }
  }

  &__content {
    @include multiEllipsis(2);

    view {
      display: inline-block;
      white-space: nowrap;
    }
  }

  &__countdown-wrap {
    color: #fa1919;
    font-size: 0;
  }

  &__countdown-text {
    margin-right: 3px;
    font-size: 12px;
    vertical-align: middle;
    display: inline;
  }

  &__countdown-days {
    display: inline-block;
    margin-right: 3px;
    font-size: 12px;
    vertical-align: middle;
  }

  &__countdown-time {
    display: inline-block;
    min-width: 54px;
    font-size: 12px;
    line-height: 16px;
    vertical-align: middle;

    view {
      display: inline;
    }
  }

  &-copy {
    font-size: 12px;
    position: fixed;
    left: -100%;
    bottom: -100%;
    z-index: -1;

    &__content {
      white-space: nowrap;
    }
  }
}
