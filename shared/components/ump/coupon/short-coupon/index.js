import WscComponent from 'shared/common/base/wsc-component/index';
import disabledMixins from '../mixins/disabled-mixins';
import themeClassMixins from '../mixins/theme-class-mixins';

const behaviors = [disabledMixins, themeClassMixins];

WscComponent({
  behaviors,

  externalClasses: ['custom-class'],

  properties: {
    coupon: {
      type: Object,
      value: {},
    },

    // 是否是分
    autoFormatValue: Boolean,

    displayType: String,

    collapse: Number,

    topLeftIcon: String,

    discountPrefix: String,

    // actions-button
    btnType: String,
    btnText: String,
    btnIcon: {
      type: String,
      optionalTypes: [Boolean],
      value: 'arrow',
    },
  },

  options: {
    multipleSlots: true,
  },

  data: {},

  methods: {},
});
