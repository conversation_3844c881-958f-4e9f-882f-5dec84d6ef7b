 <view class="coupon-short custom-class">
  <coupon-box 
    theme-class="{{ themeClass }}"
    display-type="{{ displayType }}"
    disabled="{{ disabled }}"
    unavailable="{{ unavailable }}"
    collapse="{{ collapse }}"
  >
    <view slot="A">
      <coupon-discount-box
        theme-class="{{ themeClass }}"
        unit="{{ coupon.unit }}"
        :auto-format-value="{{ autoFormatValue }}"
        disabled="{{ disabled }}"
        unavailable="{{ unavailable }}"
        value="{{ coupon.value }}"
        prefix="{{ discountPrefix }}"
        description="{{ coupon.threshold }}"
      />

      <coupon-stamp
        theme-class="{{ themeClass }}"
        visible="{{ !!topLeftIcon }}"
        position="topLeft"
        width="36px"
        height="36px"
        url="{{ topLeftIcon }}"
      />
    </view>

    <coupon-button
      slot="B"
      theme-class="{{ themeClass }}"
      icon="{{ btnIcon }}"
      disabled="{{ disabled }}"
      unavailable="{{ unavailable }}"
      btn-type="{{ btnType }}"
      btn-text="{{ btnText }}"
      bind:click-btn="handleBtnClick"
    >
      <slot slot="prefix" name="btnPrefix" />

      <slot slot="suffix" name="btnSuffix" />
    </coupon-button>
  </coupon-box>
</view>