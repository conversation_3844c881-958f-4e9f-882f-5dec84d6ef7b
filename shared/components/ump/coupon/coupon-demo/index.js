import WscPage from 'pages/common/wsc-page/index';

WscPage({
  data: {
    coupon: {
      value: '99',
      unit: '元',
      threshold: '满20元使用',
      name: '优惠券名称优惠券名称优惠券名称',
      validTime: '2018.04.09 - 2019.08.10',
    },
    couponValidEndTime: {
      value: '9999999',
      unit: '元',
      threshold: '满20元使用',
      name: '优惠券名称优惠券名称优惠券名称',
      validStartTime: Date.now(),
      validEndTime: +new Date() + 24 * 60 * 60 * 1000 * 2,
    },
    checkboxChangeValue: false,
  },

  handleBtnClick(e) {
    console.log('handleBtnClick', e);
  },

  handleCheckboxClick(e) {
    console.log('handleBtnClick', e);

    this.handleCheckboxChange();
  },

  handleCheckboxChange() {
    const { checkboxChangeValue } = this.data;

    this.setYZData({
      checkboxChangeValue: !checkboxChangeValue,
    });
  },
});
