<page-container>
  <view class="container">
    <!-- 通用优惠券 -->
    <coupon
      theme-class="th12"
      coupon="{{ coupon }}"
      btn-text="去推广"
      collapse="{{ 3 }}"
      coupon-name-suffix="(共3张)"
      top-left-icon="https://img.yzcdn.cn/public_files/87da52370823a5097d1415d58ac01dad.png"
      bottom-right-icon="{{ true }}"
      bind:click-btn="handleBtnClick"
    >
      <view slot="btnSuffix">
        <coupon-description description="剩余100张" />
      </view>

      <coupon-extra-info slot="couponExtra" hasCollapse="{{ true }}" style="width: 100%;">
        <span>额外内容</span>
        <view slot="collapsed">
          <span>这是折叠的内容</span>
        </view>
      </coupon-extra-info>
    </coupon>
    
    <!-- 禁用优惠券 -->
    <coupon
      coupon="{{ coupon }}"
      btn-text="去推广"
      unavailable
      bind:click-btn="handleBtnClick"
    >
      <view slot="btnSuffix">
        <coupon-description unavailable description="剩余100张" />
      </view>

      <coupon-extra-info slot="couponExtra" style="width: 100%;">
        <span style="color: #323233;">不可用原因：仅支持线下购物使用</span>
      </coupon-extra-info>
    </coupon>
   
    <!-- 禁用优惠券 -->
    <coupon
      coupon="{{ coupon }}"
      btn-text="去推广"
      disabled
      bind:click-btn="handleBtnClick"
    >
      <view slot="btnSuffix">
        <coupon-description disabled description="剩余100张" />
      </view>

      <coupon-extra-info slot="couponExtra" style="width: 100%;">
        <span>不可用原因：仅支持线下购物使用</span>
      </coupon-extra-info>
    </coupon>


    <!-- 倒计时优惠券 -->
    <coupon
      coupon="{{ couponValidEndTime }}"
      auto-format-value="{{ true }}"
      btn-text="去推广"
      bind:click-btn="handleBtnClick"
    >
    </coupon>
    
    <!-- 选择优惠券 -->
    <coupon
      theme-class="th7"
      custom-coupon-box-action-class="custom-coupon-box-action-class"
      isCheckbox="{{ true }}"
      checkboxValue="{{ checkboxChangeValue }}"
      coupon="{{ coupon }}"
      bind:checkbox-change="handleCheckboxClick"
    >
      <!-- <view slot="checkbox">
        使用
      </view> -->
    </coupon>

    <!-- 短券 -->
    <short-coupon
      custom-class="short-coupon"
      coupon="{{ coupon }}"
      top-left-icon="https://img.yzcdn.cn/public_files/87da52370823a5097d1415d58ac01dad.png"
      bind:click-btn="handleBtnClick"
    >
      <view slot="btnSuffix">
        <coupon-description description="剩余100张" />
      </view>
    </short-coupon>

    <!-- checkbox 极短券 -->
    <coupon-box
      custom-class="coupon-box"
      coupon="{{ coupon }}"
    > 
      <view slot="A">
        <coupon-discount-box>
          <view slot="A1">
            <coupon-discount-box
              unit="{{ coupon.unit }}"
              value="{{ coupon.value }}"
              description="{{ coupon.threshold }}"
            />

            <coupon-stamp
              visible
              position="topLeft"
              width="36px"
              height="36px"
              url="https://img.yzcdn.cn/public_files/87da52370823a5097d1415d58ac01dad.png"
            />
          </view>
        </coupon-discount-box>

        <coupon-checkbox 
          slot="A2"
          value="{{ checkboxChangeValue }}"
          custom-class="coupon-checkbox"
          bind:change="handleCheckboxChange"
        >
          选中使用
        </coupon-checkbox>
      </view>
    </coupon-box>
  </view>
</page-container>