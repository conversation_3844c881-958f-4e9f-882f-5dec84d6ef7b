@import '../../style/variables.scss';

.coupon-discountbody__disabled {
  .coupon-discountbody {
    &__value,
    &__unit,
    &__prefix {
      color: $color-text-disabled;
    }
  }
}

.coupon-discountbody__unavailable {
  .coupon-discountbody {
    &__value,
    &__unit,
    &__prefix {
      color: $color-text-unavailable;
    }
  }
}

.coupon-discountbody {
  max-width: inherit;
  height: 30px;
  overflow: hidden;
  font-size: 12px;

  &__value,
  &__unit,
  &__prefix {
    display: inline;
    color: $color-primary;
    line-height: 30px;
  }

  &__value {
    display: inline;
    font-weight: 600;
    font-size: 29px;
    white-space: nowrap;
  }
}
