export default Behavior({
  properties: {
    value: {
      type: Number,
      optionalTypes: [String],
    },
    prefix: {
      type: String,
      value: '',
    },
    unit: {
      type: String,
      value: '元',
    },
    autoWidth: {
      type: Boolean,
      value: true,
    },
    maxWidth: {
      type: Number,
      value: 112,
    },
    // 是否是分
    autoFormatValue: {
      type: Boolean,
      value: false,
    },
  },
});
