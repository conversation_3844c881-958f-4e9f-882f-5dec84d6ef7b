import WscComponent from 'shared/common/base/wsc-component/index';
import mixins from './mixins';
import disabledMixins from '../../mixins/disabled-mixins';
import themeClassMixins from '../../mixins/theme-class-mixins';
import { formatMoney } from '../../utils';

const behaviors = [mixins, disabledMixins, themeClassMixins];

WscComponent({
  behaviors,

  options: {
    multipleSlots: true,
  },

  data: {
    valueStyle: '',
    disabledClass: '',
    unavailableClass: '',
    money: {},
  },

  ready() {
    this.setYZData(
      {
        money: this.computedMoney(),
      },
      {
        immediate: true,
      }
    );
    setTimeout(() => {
      this.computedFontSize().then((fontsize) => {
        this.setYZData({
          valueStyle: `font-size: ${fontsize};`,
        });
      });
    });
  },

  methods: {
    computedMoney() {
      const { autoFormatValue, value, unit } = this.data;
      if (autoFormatValue) {
        return formatMoney(value);
      }

      return {
        value,
        unit,
      };
    },

    disabledObserver() {
      const { disabled, unavailable } = this.data;

      this.setYZData({
        disabledClass: disabled ? 'coupon-discountbody__disabled' : '',
        unavailableClass: unavailable ? 'coupon-discountbody__unavailable' : '',
      });
    },

    async computedFontSize() {
      let { maxWidth } = this.data;
      // 显示折扣信息的容器会有左右 12px 的内边距，计算的时候需要将最大边距减去单位的宽度以及 24px
      maxWidth -= 24;
      const MAX_FONT_SIZE = 29;
      const MIN_FONT_SIZE = 12;

      const wrapOffsetWidth = await this.getBoundingClientRectWidth(
        '.coupon-discountbody'
      );
      const prefixWidth = await this.getBoundingClientRectWidth(
        '.coupon-discountbody__prefix'
      );
      const unitWidth = await this.getBoundingClientRectWidth(
        '.coupon-discountbody__unit'
      );

      const decorateWidth = prefixWidth + unitWidth;
      const scaleRatio =
        (maxWidth - decorateWidth) / (wrapOffsetWidth - decorateWidth);

      if (scaleRatio > 1) return MAX_FONT_SIZE + 'px';
      const scaleFontSize = Math.trunc(scaleRatio * MAX_FONT_SIZE);

      return Math.max(scaleFontSize, MIN_FONT_SIZE) + 'px';
    },

    getBoundingClientRectWidth(selector) {
      return new Promise((resolve) => {
        const query = wx.createSelectorQuery().in(this);
        query
          .select(selector)
          .boundingClientRect((res = {}) => {
            resolve(res.width || 0);
          })
          .exec();
      });
    },
  },
});
