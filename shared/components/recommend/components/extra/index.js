Component({
  properties: {
    item: {
      type: Object,
      value: {},
      observer() {
        this.setItemStyle();
      }
    },
    layoutClass: Array
  },

  options: {
    styleIsolation: 'apply-shared'
  },

  data: {
    itemInfo: {},
    itemIndex: 0,
    itemStyle: '',
    layoutConfig: {}
  },

  attached() {
    const { info: itemInfo, index: itemIndex, layoutConfig } = this.data.item;

    this.setData({
      itemInfo,
      itemIndex,
      layoutConfig
    });

    this.setItemStyle();
  },

  methods: {
    setItemStyle() {
      const { heightStyle, goodsMarginStyle } = this.data.item;
      this.setData({
        itemStyle: `${goodsMarginStyle};${heightStyle}`
      });
    }
  }
});
