<view
  class="recommend-coupon"
  style="{{ heightStyle }}" 
  bind:tap="handleFetch"
>
  <!-- 横向展示优惠券 -->
  <view
    wx:if="{{ layout === COUPON_LAYOUT.horizontal }}"
    class="recommend-coupon__row"
  >
    <view
      class="coupon-fetch"
      bind:tap="handleFetch"
    />
    <view class="coupon-detail">
      <view class="coupon-price">
        <text class="value">
          {{ value }}
        </text>
        <text class="unit">
          {{ componentData.unit }}
        </text>
      </view>
      <view class="threshold">
        {{ componentData.threshold }}
      </view>
    </view>
  </view>
  <!-- 纵向展示优惠券 -->
  <view
    wx:else
    class="recommend-coupon__column {{  verticalClass }}"
  >
    <view class="coupon-detail">
      <view class="coupon-price">
        <text class="value">
          {{ value }}
        </text>
        <text class="unit">
          {{ componentData.unit }}
        </text>
      </view>
      <view class="threshold">
        {{ threshold }}
      </view>
    </view>
    <view class="coupon-fetch">
      <view class="title">
        {{ componentData.title }}
      </view>
      <view wx:if="{{ couponValidDesc }}" class="valid-time">
        <view class="valid-time">
          {{ couponValidDesc }}
        </view>
      </view>
      <text class="fetch-btn">
        立即领取
      </text>
    </view>
  </view>
</view>
