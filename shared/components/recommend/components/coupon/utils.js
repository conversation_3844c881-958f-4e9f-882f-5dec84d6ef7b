import { moment as formatDate } from 'utils/time';

const COUPON_TYPE = {
  /**
   * 绝对有效期
   * 例： 优惠券使用时间从xxxx-xx-xx到xxxx-xx-xx
   */
  ABSOLUTE: 1,
  /**
   * 动态有效期
   * 例： 优惠券从1天后开始生效，2天内有效
   */
  RELATIVE: 2,
};

export const getCouponValidDesc = ({ extendedData = {} } = {}) => {
  const { validTimeGenerateType } = extendedData;

  /**
   * 绝对有效期
   */
  if (validTimeGenerateType === COUPON_TYPE.ABSOLUTE) {
    const { absoluteValidStartTime, absoluteValidEndTime } = extendedData;
    const startDate = formatDate(absoluteValidStartTime, 'YYYY.MM.DD');
    const endDate = formatDate(absoluteValidEndTime, 'YYYY.MM.DD');
    return `${startDate} -\n${endDate}`;
  }

  /**
   * 动态有效期
   *
   * 如果设置了领券后立即生效（原领券当日起y天可用），仍展示“领券当日起y天可用“
   * 如果设置了领券x天后生效（x=1），仍展示“领券次日起y天内可用“
   * 如果设置了领券x天后生效（x>1），展示“领券x天后生效，有效期y天“
   */
  if (validTimeGenerateType === COUPON_TYPE.RELATIVE) {
    const { relativeValidTimeBeginInterval, relativeValidTimeDuration } =
      extendedData;

    if (relativeValidTimeBeginInterval === 0) {
      return `领券当日起${relativeValidTimeDuration}天可用`;
    }

    if (relativeValidTimeBeginInterval === 1) {
      return `领券次日起${relativeValidTimeDuration}天内可用`;
    }

    return `领券${relativeValidTimeBeginInterval}天后生效，\n有效期${relativeValidTimeDuration}天`;
  }
};

export const getCouponThreshold = ({ threshold }) => {
  const thresholdNum = Number(threshold);
  if (thresholdNum === 0) {
    return '满任意金额可用';
  }
  return `满${thresholdNum}元使用`;
};
