.recommend-coupon {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
  text-align: center;
  background-image: linear-gradient(to bottom, #ffcec7, #ffe7e4);

  &__row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: url('https://img.yzcdn.cn/public_files/33089dca42f94d782ee202b1b48d84c8.png');
    background-size: 100% 100%;

    .coupon-fetch {
      width: 40px;
      height: 40px;
      margin-left: 18px;
      background: url('https://img.yzcdn.cn/public_files/f4809eb2078d87e7f913253ee768a496.png');
      background-size: 100% 100%;
    }

    .coupon-detail {
      color: #ee0a24;

      .coupon-price {
        margin-top: 20px;

        .value {
          font-size: 26px;
          font-family: Avenir;
          font-weight: bold;
        }

        .unit {
          font-size: 10px;
        }
      }

      .threshold {
        font-size: 10px;
        margin: 3px 8px 21px 18px;
      }
    }
  }

  &__column {
    width: 85%;
    background: url('https://img.yzcdn.cn/public_files/992e00505b5546181f01670b6afc15be.png');
    background-size: 100% 100%;

    .coupon-detail {
      color: #ee0a24;

      .coupon-price {
        margin-top: 13px;

        .value {
          font-size: 30px;
          font-family: Avenir;
          font-weight: bold;
        }

        .unit {
          font-size: 12px;
          vertical-align: 1px;
          margin-left: 2px;
        }
      }

      .threshold {
        font-size: 13px;
        line-height: 17px;
        margin-top: 2px;
      }
    }

    .coupon-fetch {
      margin: 30px 0 14px 0;

      .title {
        color: #fff;
        font-size: 13px;
        line-height: 17px;
        font-weight: bold;
        margin-bottom: 12px;
      }

      .valid-time {
        position: relative;
        display: none;
        font-size: 10px;
        line-height: 14px;
        color: #fff;
        opacity: 0.8;
        white-space: pre-line;
      }

      .fetch-btn {
        display: block;
        margin: 0 auto;
        width: 76px;
        height: 24px;
        font-size: 12px;
        line-height: 24px;
        color: #723e08;
        border-radius: 45px;
        background: linear-gradient(to right bottom, #feb565, #fad8a1);
      }
    }

    &.vertical-long {
      background: url('https://img.yzcdn.cn/public_files/2f6ef56df6facae3cc2beacbaf60d7e7.png');
      background-size: 100% 100%;

      .coupon-detail {
        .coupon-price {
          margin-top: 15px;
        }

        .threshold {
          margin-top: 8px;
        }
      }

      .coupon-fetch {
        margin: 38px 0 16px 0;

        .title {
          margin-bottom: 8px;
        }

        .valid-time {
          display: block;
          margin-bottom: 20px;
        }
      }
    }
  }
}
