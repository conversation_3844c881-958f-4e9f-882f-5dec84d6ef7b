import WscComponent from 'shared/common/base/wsc-component/index';
import navigate from 'shared/utils/navigate';
import { COUPON_LAYOUT, WATERFALL_TYPE, IMAGE_RADIO } from './constants';
import { getCouponThreshold, getCouponValidDesc } from './utils';

WscComponent({
  properties: {
    componentData: {
      type: Object,
      value: {},
      observer: 'initData',
    },
    layoutConfig: {
      type: Object,
      value: {},
      observer: 'initLayout',
    },
  },

  data: {
    value: '',
    layout: 1,
    heightStyle: '',
    verticalClass: '',
    COUPON_LAYOUT,
    threshold: '',
    couponValidDesc: '',
  },

  methods: {
    initData() {
      let { value } = this.data.componentData || {};
      value = +value;

      const threshold = getCouponThreshold(this.data.componentData);
      const couponValidDesc = getCouponValidDesc(this.data.componentData);

      console.log(couponValidDesc, this.data.componentData);

      this.setYZData({
        value,
        threshold,
        couponValidDesc,
      });
    },

    /**
     * 根据B端配置计算页面展示的样式
     */
    initLayout() {
      const {
        showTitle,
        showSubTitle,
        showPrice,
        showBuyButton,
        imageRatio,
        sizeType,
      } = this.data.layoutConfig || {};

      // 瀑布流的场景特殊处理
      if (+sizeType === WATERFALL_TYPE) {
        this.setYZData({
          heightStyle: 'height: 200px',
          layout: COUPON_LAYOUT.verticalShort,
        });
        return;
      }

      let verticalClass = '';
      let curLayout = COUPON_LAYOUT.horizontal;
      // 勾选了购买和金额，展示是一样的
      const showPriceOrBuy = showBuyButton || showPrice;

      if (+imageRatio === IMAGE_RADIO.threeToFour) {
        // 图片比例为3:4
        curLayout = COUPON_LAYOUT.verticalLong;
      } else if (+imageRatio === IMAGE_RADIO.square) {
        // 图片比例为1:1
        if (showSubTitle + showTitle + showPriceOrBuy > 1) {
          curLayout = COUPON_LAYOUT.verticalLong;
        } else {
          curLayout = COUPON_LAYOUT.verticalShort;
        }
      } else if (showSubTitle + showTitle + showPriceOrBuy > 1) {
        // 图片比例为3:2或者16:9
        curLayout = COUPON_LAYOUT.verticalShort;
      } else {
        curLayout = COUPON_LAYOUT.horizontal;
      }

      if (curLayout === COUPON_LAYOUT.verticalLong) {
        verticalClass = 'vertical-long';
      }
      this.setYZData({
        verticalClass,
        layout: curLayout,
      });
    },

    handleFetch() {
      const { activityId } = this.data.componentData || {};
      navigate.navigate({
        url: `/packages/user/coupon/shop/index?activityId=${activityId}`,
      });
    },
  },
});
