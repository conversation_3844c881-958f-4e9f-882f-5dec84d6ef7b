## 个性化推荐额外组件

### 如何添加组件

1. 本目录下开发
2. 添加到extra 中

```html
<view class="extra-item" style="{{ itemStyle }}">
  <recommend-note 
    wx:if="{{itemInfo.type === 'note'}}"
    component-data="{{ itemInfo }}"
    layout-config="{{ layoutConfig }}"
  />
  <recommend-xxx
    wx:if="{{itemInfo.type === 'xxx'}}"
    component-data="{{ itemInfo }}"
    layout-config="{{ layoutConfig }}"
  />
</view>
```

```json
{
  "component": true,
  "usingComponents": {
    "recommend-note": "../note/index",
    "recommend-xxx": "../xxx/index"
  }
}
  
```

### 组件数据

针对额外插入组件，这边传入俩个数据

`componentData` 为后端透传数据

```javascript
componentData = {
  isExtra: true, // 额外组件标识  
  type: 'note', // 额外组件类型 
  loggeerParams: {
    item_id: '',
    item_type: '',
    banner_id: '',
    alg: '',
    recommend_name: '',
  }, // 埋点相关
  // 透传 跟业务对应后端同学确认
}
```

`layoutConfig` 布局配置，类商品布局跟商品保持一致

[点我看一看](http://zanui.qima-inc.com/zanui/captain-showcase#/zh-CN/new-goods)