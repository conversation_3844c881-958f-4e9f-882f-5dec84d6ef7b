import { TAG_COLORS } from './constant';

Component({
  data: {
    colors: TAG_COLORS
  },

  properties: {
    show: {
      type: Boolean,
      value: false
    },
    goodsInfo: Object,
    maxCount: {
      type: Number,
      value: 1
    }
  },

  observers: {
    goodsInfo(goodsInfo = {}) {
      const { showDefaultTags = [], showTags = [] } = goodsInfo;
      this.setData({
        normalTags: showTags.filter((tag) => tag).slice(0, this.data.maxCount),
        defaultTags: showDefaultTags.filter((tag) => tag).slice(0, this.data.maxCount)
      });
    }
  }
});
