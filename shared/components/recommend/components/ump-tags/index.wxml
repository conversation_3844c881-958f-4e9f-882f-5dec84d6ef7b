<view class="ump-tags-container" wx:if="{{ show }}">
  <view wx:if="{{normalTags.length > 0}}">
    <van-tag custom-class="ump-tag" wx:for="{{normalTags}}" key="{{index}}" round color="{{colors.tagBgColor}}" text-color="{{colors.tagTextColor}}">
      {{ item }}
    </van-tag>
  </view>
  <view class="ump-tags" wx:else>
      <view
        wx:for="{{ defaultTags }}"
        class="default-tag default-tag-{{item.id}} }}"
        key="{{ item.id }}"
      >
        <text class="tag-icon"></text>
        {{ item.name }}
      </view>
    </view>
</view>