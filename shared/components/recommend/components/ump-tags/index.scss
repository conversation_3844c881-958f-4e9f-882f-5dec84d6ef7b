.ump-tags-container {
  font-size: 12px;
  margin-top: 8px;
  display: flex;
}

.ump-tags {
  display: flex;
}

.ump-tag,
.default-tag {
  display: flex;
  align-items: center;
  height: 16px;
  padding: 0 4px;
  font-size: 12px;
  line-height: 16px;

  &:not(:first-child) {
    margin-left: 4px;
  }
}

.default-tag {
  border-radius: 8px;
  border: 1px solid rgba(238, 10, 36, 0.32);
  color: #ee0a24;
  box-sizing: border-box;

  .tag-icon {
    display: inline-block;
    margin-right: 2px;
    width: 12px;
    height: 12px;
    overflow: hidden;
    background: url('https://b.yzcdn.cn/wsc-h5-statcenter/recommend-showcase-page/default_tag_icons_sprite.png')
      no-repeat;
    background-size: 12px;
  }

  &.default-tag-1 .tag-icon {
    background-position: 0 -1px;
  }

  &.default-tag-2 .tag-icon {
    background-position: 0 -15px;
  }

  &.default-tag-3 .tag-icon {
    background-position: 0 -28px;
  }
}
