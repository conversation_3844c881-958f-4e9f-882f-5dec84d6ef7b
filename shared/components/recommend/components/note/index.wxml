<view class="recommend-note {{ isWaterFall ? 'isWaterFall': '' }}" bind:tap="jump">
  <view class="recommend-note-banner">
    <view class="recommend-note-banner__img" style="{{ paddingTopStyle }}">
      <image mode="{{ imageFillMode }}" class="recommend-note-banner__img-waterfall" wx:if="{{ isWaterFall }}" src="{{ componentData.headImageUrl }}" />
      <view wx:else style="background-image: url({{ componentData.headImageUrl }})" key="{{ componentData.headImageUrl }}" class="{{ imageFillStyleClass }} recommend-note-banner__img-bg" />
    </view>
    <view class="recommend-note-wrapper recommend-note-{{ layoutConfig.textAlignType }}">
      <view class="recommend-note-wrapper__title" wx:if="{{layoutConfig.showTitle || layoutConfig.showSubTitle}}">
        <view class="title {{ textStyleClass }}" wx:if="{{ layoutConfig.showTitle }}">
          {{ componentData.title || description }}
        </view>
        <view class="subtitle" wx:if="{{ layoutConfig.showSubTitle }}">
          {{ componentData.description }}
        </view>
      </view>
      <view wx:if="{{ layoutConfig.showPrice || layoutConfig.showBuyButton }}" class="recommend-note-wrapper__info">
        <view wx:if="{{ layoutConfig.showPrice }}" class="recommend-note-wrapper__info-label">
          <theme-view bg="main-bg" opacity="0.1" color="main-bg" class="textStyleClass {{ textStyleClass }}" custom-class="note-label">
            #{{ componentData.label }}
          </theme-view>
        </view>
        <view wx:if="{{ layoutConfig.showBuyButton }}" class="recommend-note-wrapper__info-view">
          <van-icon size="18px" name="eye-o" class="icon-wrapper" />
          <view class="view-num">{{ componentData.pv }}</view>
        </view>
      </view>
    </view>
  </view>
</view>