import WscComponent from 'shared/common/base/wsc-component/index';
import navigate from 'shared/utils/navigate';
import {
  IMAGE_RATIO_MAP,
  IMAGE_FILL_STYLE_MAP,
  SIZE_MAP,
  TEXT_STYLE_MAP,
} from './constant';

WscComponent({
  properties: {
    componentData: {
      type: Object,
      value: {},
    },

    layoutConfig: {
      type: Object,
      value: {},
      observer: 'initLayout',
    },

    iStyle: {
      type: [String, Object],
    },
  },
  options: {
    styleIsolation: 'shared',
  },
  data: {
    isWaterFall: false,
    paddingTopStyle: {},
    imageFillStyleClass: '',
    textStyleClass: '',
    imageFillMode: 'aspectFit',
  },
  attached() {
    this.setYZData({
      imageFillMode: this.computeImageFillMode(),
    });
  },
  methods: {
    jump() {
      const { componentData } = this.properties;
      const { noteAlias } = componentData;
      navigate.navigate({
        url: `/packages/shop/shopnote/detail/index?noteAlias=${noteAlias}`,
      });
    },

    initLayout() {
      const { layoutConfig = {} } = this.properties;
      const {
        sizeType = 0,
        imageRatio = 1,
        imageFillStyle = 1,
        textStyleType = 1,
      } = layoutConfig;
      let paddingTopStyle = {};
      const isWaterFall = +sizeType === SIZE_MAP.WATERFALL;
      const imageFillStyleClass = `recommend-note-banner__img-${IMAGE_FILL_STYLE_MAP[imageFillStyle]}`;
      const textStyleClass =
        TEXT_STYLE_MAP[textStyleType] === 'bold' ? 'title-bold' : '';
      if (!isWaterFall) {
        const [w, h] = IMAGE_RATIO_MAP[imageRatio].split('-');
        paddingTopStyle = `padding-top: ${(+h / +w) * 100 + '%'}`;
      }
      this.setYZData({
        isWaterFall,
        imageFillStyleClass,
        textStyleClass,
        paddingTopStyle,
      });
    },
    /**
     * 计算图片 imageFillMode
     */
    computeImageFillMode() {
      const { layoutConfig = {} } = this.properties;
      const { imageFillStyle, sizeType } = layoutConfig;
      const isWaterFall = +sizeType === SIZE_MAP.WATERFALL;
      // 瀑布流高度自适应
      if (isWaterFall) return 'widthFix';
      return +imageFillStyle === 2 ? 'aspectFit' : 'aspectFill';
    },
  },
});
