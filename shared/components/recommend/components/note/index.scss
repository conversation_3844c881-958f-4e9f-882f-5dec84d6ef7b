@mixin multi-ellipsis($lines) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}

.recommend-note {
  height: 100%;

  &-banner {
    &__img {
      position: relative;

      &-waterfall {
        width: 100%;
        height: auto;
      }

      &-contain {
        background-size: contain;
      }

      &-bg {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        background-repeat: no-repeat;
        background-position: center;
      }

      &-cover {
        background-size: cover;
      }
    }
  }

  &-wrapper {
    box-sizing: border-box;
    width: 100%;
    padding: 0 10px 4px;

    &__img {
      width: 100%;
      object-fit: cover;
    }

    &__title {
      margin-top: 8px;
      font-size: 13px;
      line-height: 17px;

      .title {
        height: 34px;
        color: #323233;
        font-weight: normal;
        margin-bottom: 5px;

        @include multi-ellipsis(2);
      }

      .title-bold {
        font-weight: bold;
      }

      .subtitle {
        color: #969799;
        height: 16px;

        @include multi-ellipsis(1);
      }
    }

    &__subtitle {
      color: #999;
      line-height: 17px;
    }

    &__info {
      position: relative;
      width: 100%;

      &-label {
        display: flex;
        align-items: center;
        height: 44px;
        padding-right: 25px;

        .note-label {
          padding: 0 4px;
          font-size: 12px;
          line-height: 16px;
          border-radius: 2px;

          &.title-bold {
            font-weight: bold;
          }
        }
      }

      &-view {
        position: absolute;
        right: 0;
        display: flex;
        align-items: center;
        color: #c8c9cc;
        font-size: 14px;
        height: 24px;
        top: 10px;
        margin-bottom: 10px;

        .icon-wrapper {
          height: 18px;
          margin-right: 2px;
        }
      }
    }
  }

  &-center {
    text-align: center;

    .recommend-note-wrapper__info-label {
      height: 44px;
      flex-direction: column;
      justify-content: center;
      padding-right: 0;
    }

    .recommend-note-wrapper__info-view {
      position: relative;
      justify-content: center;
      top: 0;
    }
  }
}

.waterfall {
  .recommend-note-left {
    .recommend-note-wrapper__info {
      height: 30px;

      &-label {
        position: relative;
        top: -5px;
      }
    }

    .recommend-note-wrapper__info-view {
      top: 4px;
      right: 0;
    }
  }
}

.view-num {
  height: 20px;
  line-height: 20px;
}

// 无边透明
.simple {
  padding: 0;

  &.circle {
    .recommend-note-banner__img {
      border-radius: 8px;
      overflow: hidden;
    }
  }
  .recommend-note-wrapper {
    padding: 0;
    bottom: 0;
  }
}
