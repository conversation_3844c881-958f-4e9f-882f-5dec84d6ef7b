export const computedLogger = {
  coupon: (item, loggerParams) => {
    loggerParams.item_id = item.activityId;
    return loggerParams;
  },

  note: (item, loggerParams) => {
    loggerParams.item_id = item.noteId;
    return loggerParams;
  },
};

export const defaultLayoutConfigInSnakeCase = {
  layout: 1,
  size_type: 0,
  border_radius_type: 2,
  image_ratio: 1,
  image_fill_style: 1,
  text_style_type: 1,
  text_align_type: 'left',
  page_margin: 16,
  goods_margin: 8,
  show_title: true,
  show_sub_title: false,
  show_price: true,
  show_origin_price: false,
  show_buy_button: false,
  show_corner_mark: false,
  corner_mark_type: 0,
};
