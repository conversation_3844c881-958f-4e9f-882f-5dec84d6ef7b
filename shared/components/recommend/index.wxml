<showcase-title wx:if="{{ showTitle }}" component-data="{{ curTitleConfig }}" />
<slot name="title"></slot>

<cap-list
  loading="{{ isLoading }}"
  finished="{{ !hasMore }}"
  bind:load="handleLoadMore"
>
  <cap-recommend
    id="cap-recommend"
    class="recommend-list"
    list="{{ recommendList }}"
    layout="{{ curLayoutConfig.layout }}"
    generic:goods-item-corner-mark="goods-item-corner-mark"
    generic:goods-item-ump-tags="ump-tags"
    height-style="{{ heightStyle }}"
    size-type="{{ curLayoutConfig.sizeType }}"
    show-title="{{ curLayoutConfig.showTitle }}"
    show-sub-title="{{ curLayoutConfig.showSubTitle }}"
    show-price="{{ curLayoutConfig.showPrice }}"
    show-sold-num="{{ curLayoutConfig.showSoldNum }}"
    show-origin-price="{{ curLayoutConfig.showOriginPrice }}"
    show-buy-button="{{ curLayoutConfig.showBuyButton }}"
    buy-button-type="{{ curLayoutConfig.buyButtonType }}"
    buy-btn-express="{{ curLayoutConfig.buyBtnExpress }}"
    show-corner-mark="{{ curLayoutConfig.showCornerMark }}"
    corner-mark-type="{{ curLayoutConfig.cornerMarkType }}"
    corner-mark-image="{{ curLayoutConfig.cornerMarkImage }}"
    button-text="{{ curLayoutConfig.buttonText }}"
    image-fill-style="{{ curLayoutConfig.imageFillStyle }}"
    image-ratio="{{ curLayoutConfig.imageRatio }}"
    page-margin="{{ curLayoutConfig.pageMargin }}"
    goods-margin="{{ curLayoutConfig.goodsMargin }}"
    text-style-type="{{ curLayoutConfig.textStyleType }}"
    text-align-type="{{ curLayoutConfig.textAlignType }}"
    border-radius-type="{{ curLayoutConfig.borderRadiusType }}"
    show-ump-tags="{{ curLayoutConfig.showUmpTags }}"
    ump-tags-max-count="{{ curLayoutConfig.umpTagsMaxCount }}"
    show-last-self-define-item="{{ showLastSelfDefineItem }}"
    skeleton-number="{{ !hasMore ? 0 : 2 }}"
    bind:buy="handleGoodsBuy"
    bind:item-click="handleGoodsItemClick"
  >
    <view slot="last-item-in-list">
      <slot name="last-item-in-list" />
    </view>
  </cap-recommend>
</cap-list>

<feature-sku
  wx:if="{{ skuData }}"
  theme-class="{{ themeClass }}"
  feature-sku-data="{{ skuData }}"
  bind:close="handleSkuClose"
/>
