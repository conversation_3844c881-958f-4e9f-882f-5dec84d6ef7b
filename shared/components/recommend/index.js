import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import Args from '@youzan/weapp-utils/lib/args';
import getApp from 'shared/utils/get-safe-app';
import { featureSkuUtilsPromise } from 'shared/utils/async-base-sku';
import navigate from 'shared/utils/navigate';
import spm from 'shared/utils/spm';
import {
  getComponentLoggerParams,
  ensureAppLogger,
} from 'shared/utils/logger-type';
import { fetchGoodsRecommend } from './api';
import { computedLogger, defaultLayoutConfigInSnakeCase } from './config';
import { LAYOUT_MAP, SIZE_MAP, SHOP_RANKING_MAP } from './constants';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';

const app = getApp();

Component({
  behaviors: [],

  options: {
    multipleSlots: true,
  },

  properties: {
    bizName: String,
    goodsNumber: Number,
    layoutConfig: {
      type: Object,
      value: {},
      observer(val) {
        if (val) {
          this.setData({
            curLayoutConfig: mapKeysCase.toCamelCase(val),
          });
        }
      },
    },
    titleConfig: {
      type: Object,
      value: {},
    },
    pageRandomNumber: {
      type: String,
      value: makeRandomString(8),
    },
    componentIndex: {
      type: Number,
      value: 0,
    },
    customRecommendName: {
      type: String,
      value: '',
    },
    // hasCustomClickHandler 为true时， handleGoodsItemClick、handleGoodsBuy生效
    hasCustomClickHandler: { type: Boolean, value: false },
    handleGoodsItemClick: {
      type: Function,
    },
    handleGoodsBuy: {
      type: Function,
    },
    showLastSelfDefineItem: {
      type: Boolean,
      value: false,
    },
    page: {
      type: Number,
      value: 1,
    },
    // 分页加载
    loadMore: {
      type: Boolean,
      value: false,
    },
    handleLoadMore: {
      type: Function,
    },
  },

  data: {
    height: null,
    heightStyle: '',
    isLoading: false,
    recommendList: [],
    curLayoutConfig: {},
    curTitleConfig: {},
    showTitle: false,
    recommendName: '个性化推荐',
    themeClass: app.themeClass,
  },

  attached() {
    this.initTitle();
    this.initList();
  },

  ready() {
    const { sizeType, layout } = this.data.curLayoutConfig;
    // 仅在 一行两个对齐展示时会有 extra 内容，如果非瀑布流展示需要每行对齐
    // uc~mg代表个人中心更多推荐场景值，此场景需要展示店铺榜单信息 需要高度自适应
    if (+layout === LAYOUT_MAP.SMALL && +sizeType !== SIZE_MAP.WATERFALL) {
      return setTimeout(() => {
        this.createSelectorQuery()
          .select('.recommend-list >>> .c-goods-recommend >>> .goods-item')
          .boundingClientRect((res) => {
            this.updateHeight(res?.height);
          })
          .exec();
      }, 500);
    }
    this.updateHeight();
  },

  methods: {
    updateHeight(height = null) {
      this.setData(
        {
          height,
        },
        () => {
          this.updateHeightStyle();
        }
      );
    },
    updateHeightStyle() {
      const { height } = this.data;
      this.setData({
        heightStyle: +height
          ? `overflow: hidden;height: ${Math.ceil(+height)}px;`
          : 'height: auto;',
      });
    },
    initTitle() {
      const titleConfig = mapKeysCase.toCamelCase(this.data.titleConfig || {});
      const { customRecommendName, recommendName = customRecommendName } =
        this.data;

      const curTitleConfig = {
        ...titleConfig,
        show_method: titleConfig.showMethod,
        sub_title: titleConfig.subTitle,
      };

      this.setData({
        recommendName:
          customRecommendName ||
          (titleConfig.title && titleConfig.showTitleComponent
            ? titleConfig.title
            : recommendName),
        curTitleConfig,
        showTitle: titleConfig.showTitleComponent || false,
      });
    },

    initList() {
      const layoutConfigParse = {
        ...defaultLayoutConfigInSnakeCase,
        ...mapKeysCase.toSnakeCase(this.data.layoutConfig),
      };

      this.setData({
        curLayoutConfig: mapKeysCase.toCamelCase(layoutConfigParse),
      });
      this.fetchList(1);
    },

    computedExtraRecs() {
      const { layout } = this.data.curLayoutConfig;
      const extraRecs = +layout === 1 ? 'note,coupon' : '';

      return extraRecs;
    },

    fetchList(page) {
      const { bizName, goodsNumber, recommendName, loadMore } = this.data;

      fetchGoodsRecommend({
        page,
        pageSize: goodsNumber,
        isMini: true,
        bizName,
        buyerId: app.getBuyerId() || 0,
        storeId: app.getOfflineId() || 0,
        extraRecs: this.computedExtraRecs(),
      })
        .then(({ list = [], has_more = false }) => {
          const loggerList = [];
          const recommendList = mapKeysCase.toCamelCase(list);
          // 如果列表需要展示榜单信息，需要将商品列表高度增加20展示榜单信息
          const isShowRankingInfo = recommendList.some(
            (el) => el.showRankingInfo
          );
          const { height } = this.data;
          if (isShowRankingInfo && height) {
            this.updateHeight(+height + 20);
          }

          recommendList.map((item, index) => {
            const bannerId = this.getBannerId(index + 1);
            item.type = item.itemType;
            if (item.type !== 'goods') {
              item.isExtra = true;
            }

            const { rankNo, rankType } = item.extendedData || {};
            let shopRankingText = '';
            if (rankNo && rankType) {
              shopRankingText = `本店${SHOP_RANKING_MAP[rankType]}第${rankNo}`;
            }
            item.showExtra = !!shopRankingText;
            item.extra = [];

            let rankLogger = {};
            if (shopRankingText) {
              item.extra.push({
                text: shopRankingText,
                color: 'var(--icon, #323233)',
              });
              rankLogger = {
                rank_type: rankType,
                rank_no: rankNo,
              };
            }

            let loggerParams = {
              item_id: item.id,
              item_type: item.type || 'goods',
              banner_id: bannerId,
              g_kdt_id: item.kdtId,
              alg: item.algs,
              recommend_name: recommendName,
              ...rankLogger,
            };

            if (computedLogger[item.type]) {
              loggerParams = computedLogger[item.type](item, loggerParams);
            }

            item.loggerParams = loggerParams;
            item.bannerId = bannerId;

            item.h5Url = Args.add(item.url, {
              alias: item.alias,
              banner_id: bannerId,
              alg: item.algs,
              ...rankLogger,
            });
            item.url = Args.add('/pages/goods/detail/index', {
              alias: item.alias,
              banner_id: bannerId,
              alg: item.algs,
              ...rankLogger,
            });
            loggerList.push(loggerParams);

            return item;
          });

          const newRecommendList =
            this.data.recommendList.concat(recommendList);
          this.setData({
            recommendList: newRecommendList,
            isLoading: false,
            hasMore: has_more && loadMore,
            page,
          });

          ensureAppLogger(getComponentLoggerParams('view', loggerList));
          this.triggerEvent('afterload', {
            recommendList: newRecommendList,
          });
        })
        .catch(() => {
          this.setData({
            isLoading: false,
          });
        });
    },

    handleLoadMore() {
      if (this.data.isLoading) return;
      this.fetchList(this.data.page + 1);
    },
    getBannerId(index) {
      const { componentIndex, pageRandomNumber } = this.data;
      return `${spm.getPageSpmTypeId()}~recService.${
        componentIndex + 1
      }~${index}~${pageRandomNumber}`;
    },

    handleGoodsItemClick(e) {
      if (this.data.hasCustomClickHandler) {
        return this.triggerEvent('handleGoodsItemClick', e.detail);
      }
      const { loggerParams = null, url = '', alias, extendedData } = e.detail;
      let goodsUrl = url;

      // https:// http:// 替换成 小程序商品链接
      if (/^http[s]?:\/\//.test(url) || !loggerParams) {
        goodsUrl = `/pages/goods/detail/index?alias=${alias}`;
      }

      if (extendedData && extendedData.isKdtUnion) {
        const path = Args.add(extendedData.cpsPath, {
          s: extendedData.shortUrl.split('/').pop(),
        });
        wx.navigateToMiniProgram({
          appId: extendedData.appid,
          path,
        });
      } else {
        navigate.navigate({
          url: goodsUrl,
        });
      }
    },

    handleGoodsBuy(e) {
      if (this.data.hasCustomClickHandler) {
        return this.triggerEvent('handleGoodsBuy', e.detail);
      }
      const alias = e.currentTarget.dataset.alias || e.detail.alias;
      featureSkuUtilsPromise()
        .then(({ getSkuData }) => {
          return getSkuData(alias);
        })
        .then((skuData) => {
          this.setData({
            skuData,
          });
        })
        .catch(() => {});
    },

    handleSkuClose() {
      this.setData({
        'skuData.showGoodsSku': false,
      });
    },
  },
});
