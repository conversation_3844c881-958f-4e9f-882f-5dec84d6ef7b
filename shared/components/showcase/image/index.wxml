<cap-image
  wx:if="{{ images && images.length }}"
  images="{{ images }}"
  size="{{ size }}"
  count="{{ count }}"
  swipe-fill="{{ imageFillStyle === '1' }}"
  image-style="{{ imageStyle }}"
  corner-type="{{ cornerType }}"
  indicator="{{ indicator || 1 }}"
  page-margin="{{ pageMargin }}"
  slide-setting="{{ slideSetting === '1' }}"
  count="{{ count }}"
  show-method="{{ showMethod }}"
  border-width="{{ borderWidth }}"
  background-color="{{ backgroundColor }}"
  color="{{ color }}"
  im="{{ extraData }}"
  bind:itemClick="handleImageClick"
  bind:navigate="handleImageClick"
  bind:contactback="handleContactBack"
/>

<van-dialog
  show="{{guideInfo.display}}"
  title="{{guideInfo.title || ''}}"
  message="{{guideInfo.message || ''}}"
  show-cancel-button="{{true}}"
  confirm-button-text="{{guideInfo.confirmButtonText || ''}}"
  bind:cancel="dialogCancel"
  use-confirm-button-slot="{{true}}"
>
  <view slot="confirm-button" style="flex:1">
    <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="dialogConfirm">
      <van-button
        size="large"
        class="van-dialog__button"
        custom-style="border: none;color: #ee0a24;"
        custom-class="van-dialog__confirm confirm-button-class"
      >
        {{ guideInfo.confirmButtonText || '' }}
      </van-button>
    </user-authorize>
  </view>
</van-dialog>

