import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import navigationToBehavior from '../behaviors/navigation-to-behavior';
import { getLink, isValidLink } from '../utils/linkAdaptor';
import { handleGetCouponError, getGuideInfo } from '../coupon/helper';
import couponApi from '../coupon/api';
import { SHOW_METHOD_NAME_MAP } from '../captain-components/image/constants';
import buildUrl from '@youzan/utils/url/buildUrl';
import getAuthorizedState from 'shared/utils/get-authorized-state';
import { GuideType, LogViewEvent, LogClickEvent } from '../coupon/constant';
import navigate from '@/helpers/navigate';
import { SHELF_LINK_TYPE_ENUM } from '../utils/config';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
import jumpVideoNumber from '../utils/jumpVideoNumber';
import jumpVideoNumberDynamic from '../utils/jumpVideoNumberDynamic';

const ReceiveCouponType = {
  openGuideDialog: 1,
  guideResult: 2,
};

Component({
  behaviors: [navigationToBehavior, componentBehavior, loggerBehavior],
  data: {
    images: [],
    guideInfo: {
      display: false,
    },
    hasMobile: false,
  },

  async attached() {
    const authorizedState = await getAuthorizedState();
    const { componentData } = this.data;
    const config = mapKeysCase.toCamelCase(componentData);
    let { subEntry: images = [] } = config;
    images = images.filter((image) => {
      if (image.imageUrl) {
        image.imageUrl = cdnImage(image.imageUrl, '!730x0.jpg');
      }
      if (image.linkType === 'hotarea') {
        image.hotAreas = image.hotAreas || [];
        image.hotAreas.forEach((hotzone) => {
          Object.assign(hotzone, getLink(hotzone));
        });
      }
      const shelfLink = this.getValuesOfObject(SHELF_LINK_TYPE_ENUM);
      return isValidLink(image, ['hotarea'].concat(shelfLink));
    });
    this.sendLogger(images, this.logText(config.showMethod));
    this.setData({
      ...config,
      images,
      hasMobile: authorizedState.mobile,
    });
  },

  methods: {
    getValuesOfObject(obj) {
      const res = [];
      Object.keys(obj).forEach((k) => {
        res.push(obj[k]);
      });
      return res;
    },
    sendLogger(images, logText) {
      const loggerList = [];
      images.forEach((image, index) => {
        image.loggerParams = {
          index: index + 1,
          banner_id: this.getBannerId(index + 1),
          item_id: image.alias,
          item_type: logText,
          component: logText,
          ...this.getComponentLoggerExtraParams(),
        };

        if (image.hotAreas && image.hotAreas.length) {
          image.hotAreas.forEach((hotArea) => {
            hotArea.loggerParams = image.loggerParams;
          });
        }

        loggerList.push(image.loggerParams);
      });

      this.ensureAppLogger('logger', {
        et: 'view',
        ei: 'view',
        en: '内容曝光', // 这个字段不一样所以不能用 'view' 方式
        params: {
          view_objs: loggerList,
        },
      });
    },

    logText(showMethod) {
      if (this.isImageTextNav(showMethod)) {
        return 'image_nav';
      }

      return 'image_ad';
    },

    isImageTextNav(showMethod) {
      return (
        +showMethod === SHOW_METHOD_NAME_MAP.textNav ||
        +showMethod === SHOW_METHOD_NAME_MAP.imageNav
      );
    },

    getExtraParamsByMethod(config) {
      const { showMethod, slideSetting } = config;
      const params = {
        components_style_show_method: config.showMethod,
      };

      if (this.isImageTextNav(showMethod)) {
        params.components_style_slide_setting = slideSetting;
      }

      return params;
    },

    handleImageClick({ detail }) {
      const { componentData } = this.data;
      const config = mapKeysCase.toCamelCase(componentData);
      const extraParams = this.getExtraParamsByMethod(config);
      const cloudLoggerInfo = getLoggerExtraParams(
        componentData.type,
        this.getCloudLoggerInfo(detail)
      );
      const { linkType, linkId, alias } = detail;

      const extra = {
        banner_id: detail.loggerParams.banner_id,
        ...extraParams,
      };
      // logger

      const loggerParams = {
        ...extra,
        ...cloudLoggerInfo,
      };
      this.ensureAppLogger('click_content', loggerParams);

      // 如果是优惠券，则当前页面直接领取
      if (linkType === 'coupon') {
        const { kdtId } = this.data;
        this.getCoupon(alias, linkId, kdtId);
        return;
      }

      // 如果是视频号，则跳转视频号
      if (linkType === 'video_number' && jumpVideoNumber(linkId)) return;

      if (linkType === 'video_number_dynamic') {
        jumpVideoNumberDynamic(detail);
        return;
      }

      this.navigateIncludeShelf(mapKeysCase.toSnakeCase(detail), extra);
    },

    handleContactBack({ detail = {} }) {
      this.triggerEvent('contactback', detail);
    },

    getCoupon(alias, linkId, kdtId, type = ReceiveCouponType.openGuideDialog) {
      const { guideInfo: guideInfoData, componentData } = this.data;
      const { risk_type, risk_alias } = componentData || {};
      couponApi
        .receiveCoupon({
          groupId: linkId,
          alias,
          source: 'wap_showcase',
          kdtId,
          pageAlias: risk_alias,
          pageType: risk_type,
        })
        .then((data) => {
          const { need_guide } = data;
          if (need_guide) {
            const redirectUrl = buildUrl(
              `/wscump/coupon/fetch?alias=${alias}&shopAutoEnter=1`,
              'h5',
              kdtId
            );
            const { guide_type, guide_target_alias } = data;
            const { confirmButtonText, weappUrl } = getGuideInfo({
              guide_type,
              guide_target_alias,
              redirectUrl,
              kdtId,
            });
            if (type === ReceiveCouponType.openGuideDialog) {
              const guideInfo = {
                title:
                  guide_type === GuideType.BenefitCard
                    ? '获得权益卡即可领券'
                    : '开通会员即可领券',
                message: '当前身份不满足领取条件',
                confirmButtonText,
                display: true,
                weappUrl,
              };
              this.ensureAppLogger('logger', LogViewEvent[guide_type]);
              this.setData({
                guideType: guide_type,
                guideInfo,
                alias,
                linkId,
                kdtId,
              });
            }
            if (type === ReceiveCouponType.guideResult) {
              this.setData({
                guideInfo: {
                  ...guideInfoData,
                  display: false,
                },
              });
              navigate.navigate({
                url: weappUrl,
              });
            }
          } else {
            wx.showToast({
              title: '领取成功',
            });
          }
        })
        .catch((err) => handleGetCouponError(err));
    },
    dialogCancel() {
      const { guideInfo } = this.data;
      this.setData({
        guideInfo: {
          ...guideInfo,
          display: false,
        },
      });
    },
    dialogConfirm() {
      const { guideInfo, guideType } = this.data;
      this.ensureAppLogger('logger', LogClickEvent[guideType]);
      this.setData({
        guideInfo: {
          ...guideInfo,
          display: false,
        },
      });
      setTimeout(() => {
        navigate.navigate({
          url: guideInfo.weappUrl || '',
        });
      }, 500);
    },
  },
});
