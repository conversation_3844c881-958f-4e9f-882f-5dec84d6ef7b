import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';

Component({
  behaviors: [componentBehavior, loggerBehavior],

  properties: {
    templateId: {
      type: Number,
      value: 0,
    },
  },

  data: {
    tags: {
      tabs: [],
      scroll: true,
      height: 44,
      isSticky: false,
    },
    goodsList: {},
    activeTab: 0,
    // loggerParams 从 父 传到 子组件上
    loggerParams: {},
  },
  attached() {
    this.initComponent();
  },
  canPullDownRefresh: false,
  methods: {
    /**
     * 下拉
     */
    onPullDownRefresh() {
      if (this.isComponentHide) return;
      if (!this.canPullDownRefresh) {
        this.canPullDownRefresh = true;
        return;
      }

      this.refreshComponent();
    },

    refreshComponent() {
      this.setData(
        {
          tabs: [],
        },
        () => {
          this.initComponent();
        }
      );
    },

    /**
     * 初始化组件
     */
    initComponent() {
      const {
        componentData,
        kdtId,
        appId,
        offlineId,
        extraData,
        isPlugin,
        templateId,
      } = this.properties;
      const tagListData = mapKeysCase.toCamelCase(componentData);
      const {
        tabs = [],
        goodsListConfig,
        tagsConfig,
        isShowAllGoodsTag = false,
      } = tagListData;
      // 是否需要 tab 吸顶
      const isSticky = +tagsConfig.sticky === 1;
      // 加入全部
      if (
        isShowAllGoodsTag &&
        tabs.findIndex((item) => item.isGlobalTag) === -1
      ) {
        tabs.unshift({
          alias: 'tagsAll',
          isGlobalTag: true,
          goodsNumber: Number.MAX_VALUE,
          tagName: '全部',
          title: '全部',
        });
      }

      tabs.map((item, index) => {
        item.tagIndex = index;
        return item;
      });

      const tags = {
        scroll: tabs.length > 4,
        height: 44,
        isSticky,
        navStyle: +tagsConfig.navStyle,
      };

      const loggerParams = this.computedLoggerParams();

      const toCamelCaseList = mapKeysCase.toCamelCase(goodsListConfig);

      this.setData({
        tags,
        tabs,
        goodsListConfig: toCamelCaseList,
        extra: {
          kdtId,
          appId,
          offlineId,
          extraData,
          isPlugin,
          templateId,
        },
        loggerParams,
        activeTab: 0,
      });
    },

    /**
     * 计算 bannerId 子组件替换 x.x
     */
    computedLoggerParams() {
      const loggerParams = {
        extraParams: this.getComponentLoggerExtraParams(),
        bannerId: this.getBannerId('x.x'),
      };
      return loggerParams;
    },

    handleGoodsBuy(e) {
      this.triggerEvent('buy', e.detail);
    },

    tabChange(e) {
      const { index } = e.detail;
      this.setData({
        activeTab: index,
      });
    },
  },
});
