import Args from '@youzan/weapp-utils/lib/args';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import appLoggerBehavior from 'shared/components/showcase/behaviors/app-logger-behavior';
import navigate from 'shared/utils/navigate';
import { getPlugins } from '@youzan/ranta-helper-tee';
import formatPrice from '@youzan/utils/money/formatPrice';
import formatCommission from '@youzan/salesman-cube-core-utils/lib/formatCommission';
import { fetchGoodsByTagAlias } from '../api';

import {
  TAGS_THEME_MAP,
  TAGS_THEME_OTHER_COLOR,
  JUMP_UMP_GOODS_DETAIL_MAP,
  MEMBER_CARD_TYPE_LIST,
  MEMBER_CARD_UNSUPPORT_OTHER_TAG_LIST,
} from '../../goods/constants';

const GOODS_NUMBER_PERPAGE = 10;
const app = getApp();
const { dmc } = getPlugins();

Component({
  type: 'tag-list-top-item',

  behaviors: [appLoggerBehavior],
  options: {
    addGlobalClass: true,
  },

  properties: {
    tagItem: {
      type: Object,
      value: {},
    },

    activeTab: {
      type: Number,
      value: 0,
      observer() {
        this.initTabList();
      },
    },

    goodsListConfig: {
      type: Object,
      value: {},
    },

    // { kdtId, appId, offlineId, extraData, isPlugin }
    extraData: {
      type: Object,
      value: {},
    },

    // { extraParams bannerId }
    loggerParams: {
      type: Object,
      value: {},
    },
  },

  data: {
    page: 1,
    loading: false,
    nodata: false,
    loaded: false,
    goodsList: [],
    // 是否初始化过
    hasInit: false,
    curActiveTab: 0,
    isSwipeLayout: false,
    skeletonNumber: 0,
    showTags: false,
  },

  attached() {
    // 计算 moreUrl 和 layout
    this.computedLayout();

    const {
      tagItem: { tagIndex },
    } = this.data;
    this.setData(
      {
        curActiveTab: tagIndex,
      },
      () => {
        this.initTabList(tagIndex);
      }
    );
  },

  methods: {
    getMoreUrl({ isGlobalTag, alias, title }) {
      if (isGlobalTag) {
        return dmc.route.readRoute('AllGoodsList').then((route) => route.path);
      }
      return dmc.route
        .createUrl('GoodsTag', { alias, title })
        .then((route) => route.url);
    },
    computedLayout() {
      const {
        goodsListConfig: { layout },
        tagItem: { isGlobalTag, title, alias },
      } = this.data;

      this.getMoreUrl({ isGlobalTag, alias, title }).then((moreUrl) => {
        this.setData({
          isSwipeLayout: +layout === 6,
          moreUrl,
        });
      });
    },

    /**
     * 判断是否需要请求
     */
    initTabList() {
      const { curActiveTab, hasInit, activeTab } = this.data;
      // 初始化过 或者 tab 不相同 return；
      if (hasInit || +curActiveTab !== +activeTab) return;
      this.setData(
        {
          hasInit: true,
        },
        () => {
          this.fetchGoodsByAlias();
        }
      );
    },

    fetchGoodsByAlias() {
      const { loaded, loading } = this.data;

      // 加载过 并且已经加载完成
      if (loaded || loading) {
        return;
      }

      const {
        extraData: { kdtId, appId, offlineId, templateId },
        page,
        tagItem: { alias, isGlobalTag = false },
      } = this.data;

      const params = {
        alias,
        pageSize: GOODS_NUMBER_PERPAGE,
        page,
        kdt_id: kdtId,
        offlineId,
        app_id: appId,
        json: 1,
        activityPriceIndependent: 1,
        uuid: app?.logger?.options?.user?.uuid || '',
      };
      // 会员价-纽曼客诉需求 需要在1658模板中添加字段
      if (templateId === 1658) {
        params.needCustomerDiscount = true;
      }

      // 设置 loading
      this.setData(
        {
          loading: true,
          skeletonNumber: 2,
        },
        () => {
          fetchGoodsByTagAlias(params, isGlobalTag)
            .then((res) => {
              // 判断是否需要加载更对
              const { list = [], hasMore } = mapKeysCase.toCamelCase(res);
              this.setFetchGoods(
                page,
                list || [],
                isGlobalTag
                  ? list.length >= GOODS_NUMBER_PERPAGE
                  : hasMore || false
              );
            })
            .catch(() => {
              this.setFetchGoods(page, [], false);
            });
        }
      );
    },

    /**
     * 设置 taglist
     * @param index
     * @param p
     * @param items
     */
    setFetchGoods(p, items, hasMore) {
      const {
        goodsList,
        tagItem,
        curActiveTab,
        loggerParams: { extraParams, bannerId },
      } = this.data;
      // 请求的商品 数据处理
      const loggerList = [];
      const newGoods = items.map((item, index) => {
        // 替换 bannerId index
        const bannerIndex = `${curActiveTab + 1}.${
          (p - 1) * GOODS_NUMBER_PERPAGE + index + 1
        }`;
        const itemBannerId = bannerId.replace('x.x', bannerIndex);

        // 是否展示返佣价格
        item = this.setSalesManRebateInfo(item);
        // 会员再享受优惠展示
        item = this.setMenberDiscount(item);

        // 划线价存在 展示 划线价 划线价
        // 划线价不存在 如果活动价小于原价 展示原价
        // 否则不展示
        if (item.origin) {
          item.originPrice = item.origin;
        } else if (+item.activityPrice < +item.price) {
          item.originPrice = item.price;
        }

        const loggerParams = {
          goods_id: item.id,
          item_id: item.id,
          item_type: 'goods',
          banner_id: itemBannerId,
          ...extraParams,
        };
        loggerList.push(loggerParams);
        item.loggerParams = loggerParams;

        item.preUrl = `/pages/goods/detail/index?alias=${item.alias}`;
        item.price = item.activityPrice || item.price;
        item = this.computedTags(item);

        item.url = Args.add(item.preUrl, {
          banner_id: itemBannerId,
          ...extraParams,
        });

        return item;
      });

      // 合并 新老商品 p为1时 重置dataList
      let totalGoods = p === 1 ? newGoods : [...(goodsList || []), ...newGoods];
      // 是否加载完 商品总数 小于 需要请求的商品数目
      let loaded = false;
      // 数据请求完 || 总数大于需要展示数
      if (!hasMore || totalGoods.length > tagItem.goodsNumber) {
        loaded = true;
        // 取 需要展示的商品数目
        totalGoods = totalGoods.slice(0, tagItem.goodsNumber);
      }

      // 无数据 加载完 无商品[]
      const nodata = loaded && totalGoods.length === 0;

      this.setData(
        {
          goodsList: totalGoods,
          page: p + 1,
          loaded,
          loading: false,
          nodata,
          skeletonNumber: 0,
        },
        () => {
          // 设置 isSwipeLayout 状态
          const {
            tagItem: { alias },
            isSwipeLayout,
          } = this.data;
          if (isSwipeLayout) {
            this.selectComponent(`#cap-tab-list-${alias}`).setLoadStatus({
              loading: false,
              swipeFinished: loaded,
            });
          }
        }
      );
      // 批量埋点上报
      this.ensureAppLogger('view', loggerList);
    },

    computedTags(item) {
      const { activityInfos = [] } = item;

      if (!activityInfos || !activityInfos.length) {
        return item;
      }
      // 有活动 整个列表才展示 标签。
      this.setData({
        showTags: true,
      });

      item.tagsList = activityInfos.map((tag) => {
        const { type, labelThemeType, activityAlias, activityId } = tag;
        // 营销标签分类 转化中 标签主题
        tag.theme = labelThemeType;

        if (tag.theme === TAGS_THEME_MAP.other) {
          tag.color = TAGS_THEME_OTHER_COLOR;
        }

        // 独立商详
        if (type === JUMP_UMP_GOODS_DETAIL_MAP.SECKILL && activityAlias) {
          item.preUrl = `/packages/goods/seckill/index?alias=${activityAlias}`;
        }

        if (type === JUMP_UMP_GOODS_DETAIL_MAP.HELPCUT && activityId) {
          item.preUrl = Args.add('/packages/goods/help-cut/index', {
            alias: item.alias,
            activityId,
            type: 'helpcut',
          });
        }

        return tag;
      });
      // 会员价标签处理
      item = this.computedMenberActivity(item);
      return item;
    },

    computedMenberActivity(item) {
      if (
        item.tagsList.find(({ type }) => MEMBER_CARD_TYPE_LIST.includes(type))
      ) {
        item.tagsList = item.tagsList.filter(
          ({ type }) => !MEMBER_CARD_UNSUPPORT_OTHER_TAG_LIST.includes(type)
        );
      }
      return item;
    },

    /**
     * 分销员与导购员角色互斥，故只存在一种角色，返佣字段展示位置复用一处
     */
    setSalesManRebateInfo(item) {
      const { maxCommissionStr } = formatCommission({
        profitRange: [item.salesmanRebateMaxPrice],
        commissionSendType: item.salesmanCommissionSendType,
        commissionConfigDTO: item.salesmanCommissionConfigDTO,
        customPointsName: item.customPointsName,
      });
      // 是否展示返佣价格
      const showRebate = !!maxCommissionStr;
      // 是否展示导购员返佣
      const showGuideCommission = !!+item.guideCommission;

      item.showExtra =
        showRebate ||
        (item.extension &&
          (item.extension.openSalesmanBooth ||
            item.extension.openGuideBooth)) ||
        showGuideCommission;

      item.extra = [];
      if (item.showExtra) {
        if (showRebate) {
          item.extra.push({
            text: `最高赚 ${maxCommissionStr}`,
          });
        } else if (showGuideCommission) {
          const guideCommissionPrice = formatPrice(
            Number(item.guideCommission)
          );

          item.extra.push({
            text: `预计赚${guideCommissionPrice}元`,
          });
        }
      }
      return item;
    },

    setMenberDiscount(item) {
      // 如果有标识了，就不加了
      if (
        item &&
        item.extra &&
        Array.isArray(item.extra) &&
        item.extra.length > 0 &&
        item.extra.find((x) => x.text && x.text.trim() !== '')
      ) {
        return item;
      }
      // 用于解决瀑布流炸掉的问题
      const showMenberDiscount = !!item.showRecommendedCard;
      const hasRecommendedCard = !!item.recommendedCard;
      if (showMenberDiscount) {
        item.showExtra = true;
      }

      if (
        item &&
        item.extra &&
        Array.isArray(item.extra) &&
        item.extra.length === 0 &&
        hasRecommendedCard
      ) {
        item.extra.push({
          text: item.recommendedCard,
          color: '#999999',
        });
      } else if (
        item &&
        item.extra &&
        Array.isArray(item.extra) &&
        item.extra.length > 0 &&
        item.extra[item.extra.length - 1] &&
        hasRecommendedCard
      ) {
        // 适配插了个空extra text的case
        item.extra[item.extra.length - 1].text = item.recommendedCard;
        item.extra[item.extra.length - 1].color = '#999999';
      } else {
        item.extra = [];
        if (hasRecommendedCard) {
          item.extra.push({
            text: item.recommendedCard,
            color: '#999999',
          });
        }
      }
      return item;
    },

    handleGoodsBuy(e) {
      this.triggerEvent('buy', e.detail);
    },

    handleGoodsItemClick({ detail: { url } }) {
      navigate.navigate({
        url,
      });
    },
  },
});
