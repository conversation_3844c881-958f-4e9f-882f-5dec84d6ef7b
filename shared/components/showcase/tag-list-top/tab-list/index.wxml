<view>
  <cap-list
    loading="{{ loading }}"
    finished="{{ loaded }}"
    bind:load="fetchGoodsByAlias"
    auto-check
    disabled="{{ isSwipeLayout }}"
  >
    <cap-goods
      id="{{ 'cap-tab-list-' + tagItem.alias }}"
      app-id="{{ extraData.appId }}"
      list="{{ goodsList }}"
      layout="{{ goodsListConfig.layout }}"
      size-type="{{ goodsListConfig.sizeType }}"
      show-tags="{{ showTags }}"
      show-title="{{ goodsListConfig.showTitle }}"
      show-sub-title="{{ goodsListConfig.showSubTitle }}"
      show-price="{{ goodsListConfig.showPrice }}"
      show-origin-price="{{ goodsListConfig.showOriginPrice }}"
      show-buy-button="{{ goodsListConfig.showBuyButton }}"
      buy-button-type="{{ goodsListConfig.buyButtonType }}"
      buy-btn-express="{{ goodsListConfig.buyBtnExpress }}"
      show-corner-mark="{{ goodsListConfig.showCornerMark }}"
      corner-mark-type="{{ goodsListConfig.cornerMarkType }}"
      corner-mark-image="{{ goodsListConfig.cornerMarkImage }}"
      button-text="{{ goodsListConfig.buttonText }}"
      image-fill-style="{{ goodsListConfig.imageFillStyle }}"
      image-ratio="{{ goodsListConfig.imageRatio }}"
      page-margin="{{ goodsListConfig.pageMargin }}"
      goods-margin="{{ goodsListConfig.goodsMargin }}"
      text-style-type="{{ goodsListConfig.textStyleType }}"
      text-align-type="{{ goodsListConfig.textAlignType }}"
      border-radius-type="{{ goodsListConfig.borderRadiusType }}"
      is-show-more="{{ false }}"
      more-url="{{ moreUrl }}"
      extra-data="{{ extraData.extraData }}"
      is-plugin="{{ extraData.isPlugin }}"
      open-swipe-pagination="{{ isSwipeLayout && goodsList.length && !loaded }}"
      skeleton-number="{{ skeletonNumber }}"
      bind:buy="handleGoodsBuy"
      bind:item-click="handleGoodsItemClick"
      bind:load-more="fetchGoodsByAlias"
    />
  </cap-list>
</view>
