<view>
  <view wx:if="{{ list && list.length > 0 }}" class="theme-feature-member">
    <view wx:if="{{ showTitle }}" class="member-title">
      <text class="theme-feature-member__title-text">{{ title }}</text>
      <view wx:if="{{ showTitleAll }}" class="theme-feature-member__view-all">
        <text class="theme-feature-member__all-text" bindtap="goToAllMember">全部</text>
        <!-- <text class="zan-icon zan-icon-arrow theme-feature-member__zan-icon"></text> -->
        <van-icon name="arrow" custom-class="theme-feature-member__van-icon" />
      </view>
    </view>
    <block wx:for="{{ list }}" wx:key="id">
      <cap-member-item
        alias="{{ item.alias }}"
        cover="{{ item.cover }}"
        title="{{ item.title }}"
        summary="{{ item.summary }}"
        count="{{ item.count }}"
        sub-count="{{ item.subCount }}"
        price="{{ item.price }}"
        show-price="true"
        no-border="{{ index === list.length - 1 }}"
        is-free="{{ item.is_free == 1 }}"
        has-buy="{{ item.isBought == 1 }}"
        is-paid="{{ item.isBought == 1 }}"
        is-vip="{{ item.isVip}}"
        show-price-info="{{ item.showPriceInfo }}"
      />
    </block>
  </view>
</view>
