
import { node as request } from 'shared/utils/request';
import componentBehavior from '../behaviors/component-behavior';
import { shopCanPayForKnowledge } from '../utils/pctUtils';

Component({
  options: {
    addGlobalClass: true
  },

  type: 'member',

  behaviors: [componentBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
    },
    kdtId: {
      type: String
    }
  },

  data: {
    itemType: 'member',
    title: '',
    showTitle: false,
    showTitleAll: false,
    list: [],
    hideContentSubsCount: 0
  },

  attached() {
    const itemData = this.data.componentData;
    this.setData({
      itemType: 'member',
      title: itemData.title || itemData.name || '',
      showTitle: parseInt(itemData.show_title, 10) === 1,
      showTitleAll: parseInt(itemData.show_title_all, 10) === 1,
      list: []
    });
    this.getMemberList(itemData);
  },

  methods: {
    showPriceInfo() {
      return shopCanPayForKnowledge();
    },

    getMemberList(itemData) {
      if (itemData.member_from === 'custom') {
        itemData.ids = itemData.sub_entry.map(item => item.id).join(',');
      }

      request({
        path: '/wscshop/showcase/knowledge/memberlist',
        data: {
          source: itemData.member_from === 'custom' ? 1 : 0,
          ids: itemData.ids ? itemData.ids : '',
          kdt_id: this.data.kdtId,
          pageSize: 6
        }
      }).then((result) => {
        if (!result) {
          return;
        }
        this.setData({ list: this.mergeMember(itemData, result) });
      });
    },

    mergeMember(itemData, newItemData) {
      function getOldItemById(id) {
        for (let i = 0; i < itemData.sub_entry.length; i++) {
          let item = itemData.sub_entry[i];

          if (item.id === id) {
            return item;
          }
        }

        return {};
      }

      return newItemData.map((newItem) => {
        let {
          buyStatus = {},
          contentCount = 0,
          cardUserCount = 0
        } = newItem;

        const oldItem = getOldItemById(newItem.id);

        return {
          ...oldItem,
          ...newItem,
          ...buyStatus,
          price: buyStatus.price || 0,
          title: newItem.name || '',
          isVip: !!buyStatus.isVipDiscount,
          count: contentCount,
          subCount: cardUserCount,
          isFree: buyStatus.isFree === 1,
          showPriceInfo: this.showPriceInfo()
        };
      });
    },

    goToAllMember() {
      wx.navigateTo({
        url: '/packages/paidcontent/list/index?type=rights'
      });
    }
  },
});
