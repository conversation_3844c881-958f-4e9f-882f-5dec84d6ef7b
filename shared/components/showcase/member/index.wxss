.theme-feature-member {
  background: #fff;
  border-top: 1rpx solid #eee;
  border-bottom: 1rpx solid #eee;
}

.member-title {
  display: flex;
  position: relative;
  padding: 0 15px;
  flex-direction: row;
  height: 45px;
  overflow-x: hidden;
  align-items: center;
  justify-content: space-between;
}

.member-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 1rpx;
  background: #eee;
}

.theme-feature-member__all-text {
  font-size: 13px;
  color: #999;
}

.theme-feature-member__view-all {
  display: flex;
  justify-content: center;
  align-items: center;
}

.theme-feature-member__van-icon {
  display: block !important;
  font-size: 10px !important;
  margin-left: 4px;
  color: #999;
}

.theme-feature-member__title-text {
  color: #333;
  font-weight: 700;
  font-size: 15px;
  width: 75%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
