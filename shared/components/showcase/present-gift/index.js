import Args from '@youzan/weapp-utils/lib/args';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';

Component({
  behaviors: [componentBehavior, loggerBehavior],

  data: {
    src: ''
  },

  attached() {
    const { componentData: { data = {} } } = this.data;
    const { images = [] } = data;
    const src = images.length > 0 ? images[0].image_url : '';

    this.setData({ src });
  },

  methods: {
    jumpToGiftPage() {
      // 添加埋点跳转SKU
      const bannerId = this.getBannerId();
      wx.navigateTo({
        url: Args.add('/packages/gift/cart/index', { banner_id: bannerId })
      });
    }
  }
});
