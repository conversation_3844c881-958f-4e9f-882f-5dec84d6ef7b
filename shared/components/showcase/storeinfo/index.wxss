.item-panel-storeinfo {
  height: 370rpx;
  width: 100%;
  background-color: #fff;
}

.item-panel-extra-1 {
  height: 460rpx;
}

.item-panel-extra-3 {
  height: 520rpx;
}

.item-panel-extra-4 {
  height: 500rpx;
}

.storeinfo-container {
  position: relative;
  height: 100%;
}

.storeinfo-container-mask {
  height: 370rpx;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.3);
}

.storeinfo-container-mask-1,
.storeinfo-container-mask-2 {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, .55));
}

.storeinfo-container-mask-3 {
  height: 300rpx;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, .55));
}

.storeinfo-container-mask-4 {
  height: 500rpx;
}

.storeinfo__bg-image {
  display: block;
  height: 370rpx;
  width: 100%;
}

.storeinfo__bg-image-3 {
  height: 300rpx;
}

.storeinfo__bg-image-4 {
  height: 500rpx;
}

.logo-img {
  position: absolute;
  top: 210rpx;
  left: 35rpx;
  background-color: #fff;
  width: 130rpx;
  height: 130rpx;
  border: 1px solid #fff;
}

.logo-img-1 {
  top: 310rpx;
  border-radius: 100%;
}

.logo-img-3 {
  top: 215rpx;
  left: 295rpx;
  width: 160rpx;
  height: 160rpx;
  border-radius: 100%;
}

.logo-img-4 {
  top: 95rpx;
  left: 295rpx;
  width: 160rpx;
  height: 160rpx;
  border-radius: 100%;
}

.store-name {
  position: absolute;
  top: 220rpx;
  left: 185rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-weight: bold;
  width: 80%;
  font-size: 37rpx;
  color: #fff;
}

.store-name-no-activity-container {
  width: 100%;
  position: absolute;
  top: 250rpx;
  left: 185rpx;
}

.store-name-1 {
  top: 315rpx;
  left: 178rpx;
}

/*center-dark*/
.store-name-3 {
  top: 400rpx;
  left: 0;
  text-align: center;
  width: 100%;
  color: #000;
}

/*center-light*/
.store-name-4 {
  top: 275rpx;
  left: 0;
  width: 100%;
  text-align: center;
}

.activity-desc-header {
  position: absolute;
  top: 290rpx;
  left: 185rpx;
  color: #fff;
  padding-left: 4rpx;
  padding-right: 7rpx;
  text-align: center;
  box-sizing: content-box;
  white-space: nowrap;
  background-color: #f00;
  margin-right: 2px;
  height: 17px;
  line-height: 17px;
  border-radius: 2px;
  font-size: 11px;
}

.activity-desc-body {
  position: absolute;
  top: 289rpx;
  left: 256rpx;
  width: 60%;
  color: #fff;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 25rpx;
}

.storeinfo__goods-info {
  position: absolute;
  top: 380rpx;
  left: 178rpx;
  color: #677480;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 26rpx;
}

.storeinfo__goods-info-2 {
  top: 288rpx;
  left: 185rpx;
  color: #fff;
  z-index: 1;
}

/*center-dark*/
.storeinfo__goods-info-3 {
  top: 460rpx;
  width: 100%;
  left: 0;
  text-align: center;
}

/*center-light*/
.storeinfo__goods-info-4 {
  top: 350rpx;
  width: 100%;
  left: 0;
  text-align: center;
  color: #fff;
}

.goods-info-total {
  padding-right: 10px;
  position: relative;
}

.goods-info-total:after {
  content: "|";
  position: absolute;
  right: -1px;
  width: 2px;
  height: 26rpx;
  font-size: 26rpx;
  color: #e5e5e5;
}

.goods-info-new {
  padding-left: 10px;
}

.storeinfo__divider {
  position: absolute;
  height: 1px;
  width: 230rpx;
  z-index: 999;
  background-color: rgba(255, 255, 255, 0.5);
  top: 338rpx;
  left: 260rpx;
}
