<view class="item-panel-storeinfo {{ 'item-panel-extra-' + storeStyle }}">
  <view class="storeinfo-container">
    <view class="storeinfo-container-mask {{ 'storeinfo-container-mask-' + storeStyle }}" />
    <image class="storeinfo__bg-image {{ 'storeinfo__bg-image-' + storeStyle }}" src="{{ bgImg }}" mode="aspectFill" />

    <view class="storeinfo__logo-and-text-container">
      <template is="storeinfo__logo-img" data="{{ logoImg, storeStyle }}"/>
      <template is="storeinfo__store-name" data="{{ storeName, storeNameClass }}"/>
      <template is="storeinfo__activity-info" wx:if="{{ storeStyle == '0' }}" data="{{ promotionType, promotionDesc }}"/>
      <template is="storeinfo__goods-info" wx:if="{{ storeStyle != '0' }}" data="{{ goodsTotal, goodsNew, storeStyle }}"/>
      <view wx:if="{{ storeStyle == '4' }}" class="storeinfo__divider" />
    </view>
  </view>
</view>

<template name="storeinfo__logo-img">
  <image class="logo-img logo-img-{{ storeStyle }}" src="{{ logoImg }}" mode="aspectFill"/>
</template>

<template name="storeinfo__store-name">
  <text class="store-name {{ storeNameClass }}">{{ storeName }}</text>
</template>

<template name="storeinfo__activity-info">
  <text class="activity-desc-header" wx:if="{{ promotionDesc }}">{{ promotionType }}</text>
  <text class="activity-desc-body">{{ promotionDesc }}</text>
</template>

<template name="storeinfo__goods-info">
  <view class="storeinfo__goods-info {{ 'storeinfo__goods-info-' + storeStyle }}">
    <text class="goods-info-total" data-link-type="allgoods" bind:tap="handleNavigate">全部商品 {{ goodsTotal }}</text>
    <text class="goods-info-new">上新 {{ goodsNew }}</text>
  </view>
</template>
