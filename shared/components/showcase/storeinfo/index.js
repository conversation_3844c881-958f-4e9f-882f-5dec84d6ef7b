import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState } from '@youzan/vanx';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import componentBehavior from '../behaviors/component-behavior';

VanxComponent({
  behaviors: [componentBehavior],

  mapData: {
    ...mapState('/', {
      logoImg: state => cdnImage(state.shop.logo, '!160x160.jpg'),
      storeName: state => state.shop.shop_name,
    }),
  },

  data: {
    bgImg: '',
    goodsTotal: 0,
    goodsNew: 0,
    promoType: '',
    promotionDesc: '',
    storeStyle: 0,
    storeNameClass: ''
  },

  attached() {
    const { componentData = {} } = this.data;
    const promotionInfo = this.getPromotionInfo(componentData);
    const {
      background_image: backgroundImage,
      goods_total: goodsTotal,
      goods_new: goodsNew,
      store_info_style: storeStyle
    } = componentData;
    const { promotionType, promotionDesc } = promotionInfo;

    this.setData({
      bgImg: cdnImage(backgroundImage, '!730x0.jpg'),
      goodsTotal,
      goodsNew,
      promotionType,
      promotionDesc,
      storeStyle,
      storeNameClass: this.getStoreNameClass(storeStyle, promotionDesc)
    });
  },

  methods: {
    /**
     * 获取促销活动信息
     */
    getPromotionInfo(storeInfoData) {
      // 最终返回的结果
      const ret = {
        // 促销的类型
        promotionType: '',
        // 促销的详情内容
        promotionDesc: ''
      };
      const { meet_reduce: meetReduce } = storeInfoData;

      // 如果没有任何促销信息，返回默认值
      if (!meetReduce) return ret;

      // 获取促销信息详情对象
      const { reward_detail: rewardDetail } = meetReduce;

      if (!rewardDetail) return ret;

      ret.promotionType = rewardDetail.type || '满减';
      const promoData = rewardDetail.content || [];

      if (promoData.length >= 0) {
        const allPromoData = [];

        promoData.forEach(promoItem => {
          promoItem.forEach(item => {
            allPromoData.push(item.title);
          });
        });

        ret.promotionDesc = allPromoData.join('，');
      }

      return ret;
    },

    handleNavigate(ev) {
      const { linkType } = ev.target.dataset;
      this.triggerEvent('navigate', { linkType });
    },

    getStoreNameClass(storeStyle, promotionDesc) {
      let clsStr = `store-name-${storeStyle}`;

      if (storeStyle == '0' && !promotionDesc) {
        clsStr += ' store-name-no-activity-container';
      }

      return clsStr;
    }
  }
});
