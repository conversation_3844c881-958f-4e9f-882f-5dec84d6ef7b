$default-card-margin: 20rpx;

// 卡片布局样式
.entry-layout {
  display: flex;
  margin: 20rpx 32rpx;

  &__vertical {
    flex-direction: column;

    & > .entry-card {
      align-items: center;
    }

    & > .entry-card:not(:last-child),
    & > .entry-image:not(:last-child) {
      margin-bottom: $default-card-margin;
    }
  }

  &__horizontal {
    flex-direction: row;
    justify-content: space-between;

    & > .entry-card:not(:last-child),
    & > .entry-image:not(:last-child) {
      margin-right: $default-card-margin;
    }
  }

  &__type-three {
    flex-direction: row;

    .entry-image {
      flex: 1;
    }

    & > .entry-image--layout-2 {
      margin-right: $default-card-margin;
    }

    & > .entry-card {
      flex: 1;
      margin-right: 30rpx;
    }

    &--right {
      flex: 1;

      & > .entry-card:first-child {
        margin-bottom: $default-card-margin;
      }

      .entry-image {
        height: 280rpx;

        &:first-child {
          margin-bottom: $default-card-margin;
        }
      }
    }
  }

  &__row {
    display: flex;
    flex-direction: row;

    & > .entry-card:not(:last-child) {
      margin-right: $default-card-margin;
    }

    &:first-child {
      margin-bottom: 20rpx;
    }
  }
}

// 卡片样式
.entry-card {
  display: flex;
  flex: 1;
  box-sizing: border-box;
  background-color: white;
  box-shadow: 0 4rpx 20rpx rgba(125, 125, 125, 0.16);
  border-radius: 16rpx;
  padding: 32rpx;
  position: relative;

  &__horizontal {
    flex-direction: row;
    justify-content: space-between;
  }

  &__vertical {
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
  }

  &__small {
    padding: 32rpx 16rpx;
  }

  &__small,
  &__horizontal {
    .entry-icon {
      margin-top: 0;
    }
  }
}

.entry-text {
  &__title {
    font-size: 36rpx;
    font-weight: bold;
    color: #323233;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &__desc {
    font-size: 24rpx;
    color: #969799;
    margin-top: 8rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &__small {
    text-align: center;
    font-size: 28rpx;
    font-weight: bold;
    color: #323233;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-top: 16rpx;
  }
}

.entry-text-center,
.entry-text-2 {
  text-align: center;
}

.entry-icon {
  display: flex;
  justify-content: center;
  margin-top: 32rpx;

  &__content {
    height: 96rpx;
    width: 96rpx;
    object-fit: cover;

    &.large {
      height: 192rpx;
      width: 192rpx;
    }
  }

  &__right {
    justify-content: flex-end;
  }
}

.entry-card__image {
  &-1 {
    justify-content: flex-start;
  }
  &-2 {
    justify-content: center;
  }
}

// 第五种布局
.entry-layout__type-four {
  .entry-layout-row {
    justify-content: space-between;
  }

  .entry-image {
    flex: 1;

    &:not(:last-child) {
      margin-right: 20rpx;
    }
  }

  .entry-image--layout-5 {
    height: 280rpx;
  }
}

.entry-image {
  box-sizing: border-box;
  background-color: white;
  box-shadow: 0 4rpx 20rpx rgba(125, 125, 125, 0.16);
  border-radius: 16rpx;
  padding: 0;
  background-size: cover;
  background-position: center;
  position: relative;

  .image {
    height: 100%;
    width: 100%;
  }

  &--layout {
    &-0 {
      height: 172rpx;
      width: 684rpx;
    }

    &-1 {
      height: 280rpx;
      // width: 328rpx;
      width: 50%;
    }

    &-3 {
      height: 280rpx;
      width: 33.3%;
      // width: 218rpx;
    }

    &-4 {
      // width: 160rpx;
      width: 25%;
      height: 200rpx;
    }
  }
}

.entry-card__type {
  // 无边白底
  &-1 {
    box-shadow: none;
  }

  // 描边白底
  &-3 {
    box-shadow: none;
    border: 1px solid #ebedf0;
  }

  // 无边透明底
  &-4 {
    box-shadow: none;
    background-color: transparent;
  }
}

.entry-card__border-type {
  &-1 {
    border-radius: 0;
  }
}

.entry-btn {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;

  &::before {
    border: none;
  }
  
  &::after{
    border: none;
  }
}
