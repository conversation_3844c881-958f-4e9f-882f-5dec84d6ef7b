<!--
  入口卡片，支持多种布局和样式，Coding 的时候可以考虑尽量优化代码结构
  对应字段：templateType
  1. 一行多个，都保持在一行；
  2. 一行一个；
  3. 左1右2；
  4. 上3下2；
-->

<!-- 一行一个 -->
<view
  wx:if="{{ templateType === 0 }}"
  class="entry-layout entry-layout__vertical"
  style="{{ util.getLastCardMargin(cardMargin, 'bottom') }}"
>
  <template
    is="card"
    wx:for="{{ util.getSliceItems(templateType, items) }}"
    wx:key="type"
    wx:for-index="idx"
    wx:for-item="item"
    data="{{ item, templateType, templateMode, customerStyle: util.getCardMargin(cardMargin, 'bottom'), cardType: cardType, borderRadiusType: borderRadiusType }}"
  />
</view>

<!-- 一行多个 -->
<view
  wx:elif="{{ util.includes([1, 3, 4], templateType) }}"
  class="entry-layout entry-layout__horizontal"
  style="margin-right: {{ 32 - cardMargin }}rpx"
>
  <template
    is="card"
    wx:for="{{ util.getSliceItems(templateType, items) }}"
    wx:key="type"
    wx:for-index="idx"
    wx:for-item="item"
    data="{{ item, direction: 'vertical', iconPosition: templateType === 1 ? 'right' : 'center', templateMode, templateType, customerStyle:  util.getCardMargin(cardMargin), cardType: cardType, borderRadiusType: borderRadiusType, imageAlignType: imageAlignType, textAlignType: textAlignType }}"
  />
</view>

<!-- 左1右2 -->
<view
  wx:elif="{{ templateType === 2 }}"
  class="entry-layout entry-layout__type-three"
>
  <template
    is="card"
    data="{{ item: items[0], direction: 'vertical', iconPosition: 'right', iconSize: 'large', templateMode, templateType, customerStyle: util.getCardMargin(cardMargin), cardType: cardType, borderRadiusType: borderRadiusType, imageAlignType: imageAlignType, textAlignType: textAlignType  }}"
  />
  <view class="entry-layout__type-three--right" style="{{ util.getLastCardMargin(cardMargin, 'bottom') }}">
    <template
      is="card"
      wx:for="{{ util.slice(items, 1, 3) }}"
      wx:key="type"
      wx:for-index="unique"
      wx:for-item="item"
      data="{{ item, direction: 'vertical', iconPosition: 'right', templateMode, templateType, customerStyle: util.getCardMargin(cardMargin, 'bottom'), cardType: cardType, borderRadiusType: borderRadiusType, imageAlignType: imageAlignType, textAlignType: textAlignType }}"
    />
  </view>
</view>

<!-- 实现第五种布局 -->
<view
  wx:elif="{{ templateType === 5 }}"
  class="entry-layout entry-layout__vertical entry-layout__type-four"
>
  <view class="entry-layout__row" style="{{ util.getLastCardMargin(cardMargin) }} {{ util.getCardMargin(cardMargin, 'bottom') }}">
    <template
      is="card"
      wx:for="{{ util.slice(items, 0, 2) }}"
      wx:key="type"
      wx:for-index="unique"
      wx:for-item="item"
      data="{{ item, direction: 'vertical', iconPosition: 'right', templateMode, templateType, customerStyle: util.getCardMargin(cardMargin), cardType: cardType, borderRadiusType: borderRadiusType, imageAlignType: imageAlignType, textAlignType: textAlignType }}"
    />
  </view>
  <view class="entry-layout__row" style="{{ util.getLastCardMargin(cardMargin) }}">
    <template
      is="card"
      wx:for="{{ util.slice(items, 2, 5) }}"
      wx:key="type"
      wx:for-index="unique"
      wx:for-item="item"
      data="{{ item, direction: 'vertical', templateMode, templateType, customerStyle: util.getCardMargin(cardMargin), cardType: cardType, borderRadiusType: borderRadiusType, textAlignType: 2  }}"
    />
  </view>
</view>

<template name="card">
  <view
    wx:if="{{ templateMode === 0 && !(templateType === 0 && item.isShow === 1) }}"
    class="entry-card entry-card__{{ direction ? direction : 'horizontal' }}  {{ templateType === 4 ? 'entry-card__small' : '' }} entry-card__type-{{ cardType }} entry-card__border-type-{{ borderRadiusType }}"
    bind:tap="handleNavigation"
    data-item="{{ util.stringify(item.linkData) }}"
    style="{{ customerStyle }}"
  >
    <view wx:if="{{ templateType !== 4 }}" class="entry-text {{ util.includes([3, 4], templateType) ? 'entry-text-center' : 'entry-text-' + textAlignType }}">
      <view class="entry-text__title">{{ item.title }}</view>
      <view class="entry-text__desc">{{ item.desc }}</view>
    </view>
    <view class="entry-icon entry-icon__{{ iconPosition }} entry-card__image-{{ imageAlignType }}">
      <image
        class="entry-icon__content {{ iconSize }}"
        src="{{ item.useDefault ? item.defaultIcon : item.icon }}"
        mode="aspectFill"
      />
    </view>
    <view wx:if="{{ templateType === 4 }}" class="entry-text__small">
      {{ item.title }}
    </view>
    <!-- 支持在线客服功能 -->
    <button
      open-type="contact"
      bind:contact="handleContactBack"
      class="entry-btn"
      wx:if="{{item.linkData.link_type === 'chat'}}"
    >
    </button>
  </view>

  <view
    wx:elif="{{ templateMode === 1 && !(templateType === 0 && item.isShow === 1) }}"
    class="entry-image entry-image--layout-{{ templateType }} entry-card__border-type-{{ borderRadiusType }} entry-card__type-{{ cardType }}"
    bind:tap="handleNavigation"
    data-item="{{ util.stringify(item.linkData) }}"
    style="background-image: url({{ item.img }}); {{ customerStyle }}"
  >
    <!-- 支持在线客服功能 -->
    <button
      open-type="contact"
      bind:contact="handleContactBack"
      class="entry-btn"
      wx:if="{{item.linkData.link_type === 'chat'}}"
    >
    </button>
  </view>

</template>

<wxs module="util">
  function slice(arr, start, end) {
    return arr.slice(start, end);
  }

  function includes(arr, element) {
    return ~arr.indexOf(element);
  }

  // 根据 templateType 获取不同的渲染数组
  function getSliceItems(type, items) {
    if (type === 1) return items.slice(0, 2);
    if (type === 0 || type === 3) return items.slice(0, 3);
    if (type === 4) return items.slice(0, 4);
    return [];
  }

  function stringify(data) {
    return JSON.stringify(data);
  }

  function getCardMargin(cardMargin, direction = 'right') {
    return typeof cardMargin === 'number' ? 'margin-' + direction + ': ' + cardMargin + 'rpx;' : ''
  }

  function getLastCardMargin(cardMargin, direction = 'right') {
    return 'margin-' + direction + ': ' + -cardMargin + 'rpx;';
  }

  module.exports = { slice: slice, includes: includes, getSliceItems: getSliceItems, stringify: stringify, getCardMargin: getCardMargin, getLastCardMargin: getLastCardMargin };
</wxs>
