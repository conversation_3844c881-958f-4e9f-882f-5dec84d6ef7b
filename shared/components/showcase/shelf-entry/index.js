import navigationToBehavior from '../behaviors/navigation-to-behavior';

Component({
  behaviors: [navigationToBehavior],
  properties: {
    /**
     * 组件数据
     */
    componentData: {
      type: Object,
      value: {},
    },
    dataFromShareOnOrderPage: Object,
  },

  data: {
    items: [],
    // 入口卡片布局类型
    templateType: 0,
    // 模板类型（图文/图片）
    templateMode: 0,
  },

  observers: {
    componentData(data) {
      const defaultCardMargin = 20;
      const cardMargin = data.cardMargin;
      const defaultBorderRadiusType = 2;
      const defaultImageAlignType = 3;
      const defaultTextAlignType = 1;

      this.setData({
        templateType: data.templateType ?? 0,
        templateMode: data.templateMode ?? 0,
        items: data.items ?? [],
        cardMargin:
          typeof cardMargin === 'number' ? cardMargin * 2 : defaultCardMargin,
        cardType: data.cardType || '',
        borderRadiusType: data.borderRadiusType || defaultBorderRadiusType,
        imageAlignType: data.imageAlignType || defaultImageAlignType,
        textAlignType: data.textAlignType || defaultTextAlignType,
      });
    },
  },
  methods: {
    handleNavigation(event) {
      const {
        currentTarget: { dataset },
      } = event;
      const { item } = dataset;
      this.navigateIncludeShelf(JSON.parse(item));
    },
  },
});
