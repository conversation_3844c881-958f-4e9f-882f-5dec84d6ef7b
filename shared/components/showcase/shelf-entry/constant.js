// import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

// CDN 地址的前缀
const CDN_PREFIX = 'https://b.yzcdn.cn/retail/ump/miniprogram/order_entry_';
const getCDNUrl = (name) => `${CDN_PREFIX}${name}@2x.png`;

// 默认的 Icon 配置
export const DEFAULT_ICONS = {
  SELF: getCDNUrl('selffetch'),
  TAKEOUT: getCDNUrl('takeout'),
  SHOP: getCDNUrl('shop'),
};

// 链接类型
export const LINK_TYPE = {
  SELF_FETCH: 1,
  TAKEOUT: 2,
  FREE_GO: 4,
  SCAN_GO: 5,
};

export const DEFAULT_DATA = [
  {
    type: 'selffetch',
    name: '到店自提',
    desc: '自取不用等',
    icon: DEFAULT_ICONS.SELF,
    defaultIcon: DEFAULT_ICONS.SELF,
    useDefault: false,
    linkType: LINK_TYPE.SELF_FETCH,
  },
  {
    type: 'takeout',
    name: '外卖到家',
    desc: '外卖送到家',
    icon: DEFAULT_ICONS.TAKEOUT,
    defaultIcon: DEFAULT_ICONS.TAKEOUT,
    useDefault: false,
    linkType: LINK_TYPE.TAKEOUT,
  },
  {
    type: 'shop',
    name: '网上商城',
    desc: '好货任你选',
    icon: DEFAULT_ICONS.SHOP,
    defaultIcon: DEFAULT_ICONS.SHOP,
    useDefault: false,
  },
];

export const LINK_MAP = [
  '',
  '/packages/retail/goods-shelf/index?mode=0',
  '/packages/retail/goods-shelf/index?mode=1',
  '/pages/home/<USER>/index',
  '/pages-retail/dashboard/index?from=home-shelf&s=2',
  '/pages-retail/dashboard/index?from=home-shelf&s=1',
];
