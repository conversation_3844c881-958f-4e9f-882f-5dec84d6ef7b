import pointsGoodsAdaptor from '@youzan/feature-adaptor/es/points-goods';
import args from '@youzan/weapp-utils/lib/args';
import openWebView from 'shared/utils/open-web-view';
import getApp from 'shared/utils/get-safe-app';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

import loggerBehavior from '../behaviors/logger-behavior';
import componentBehavior from '../behaviors/component-behavior';
import navigate from '../../../utils/navigate';
import api from './api.js';
import { GOODS_TYPE } from './constants';

const app = getApp();

const { list2captain } = pointsGoodsAdaptor;
const GOOS_SOURCE = {
  AUTO: '0',
  MANUAL: '1',
};

Component({
  behaviors: [componentBehavior, loggerBehavior],

  data: {
    moreUrl: '',
    config: {},
    goodsList: [],
    GOOS_SOURCE,
  },

  attached() {
    this.initPage();
  },

  methods: {
    initPage() {
      const { componentData, GOOS_SOURCE } = this.data;
      const newComponentData = mapKeysCase.toCamelCase(componentData);
      const {
        goodsSource = {},
        goodsNum,
        orderRule,
        goodsIds,
      } = newComponentData;

      // 区分是不是根据id获取
      if (goodsSource === GOOS_SOURCE.AUTO) {
        api
          .getList({
            kdtId: this.data.kdtId,
            pageSize: goodsNum,
            sortRuleCode: orderRule,
          })
          .then((result) => {
            if (!result) {
              return;
            }

            this.setData({
              config: newComponentData,
              goodsList: this.getGoodsList(result.items),
            });
          });
      } else {
        api
          .getListById({
            kdtId: this.data.kdtId,
            ids: goodsIds.join(','),
          })
          .then((result) => {
            if (!result) {
              return;
            }

            this.setData({
              config: newComponentData,
              goodsList: this.getGoodsList(result),
            });
          });
      }
    },

    getGoodsList(list) {
      if (!list || !list.length) {
        return [];
      }

      const _list = mapKeysCase.toCamelCase(list2captain(list));
      const loggerList = [];
      _list.forEach((item, index) => {
        // 加入埋点参数
        const bannerId = this.getBannerId(index + 1);
        item.url = args.add(this.getUrl(item), {
          banner_id: bannerId,
          ...this.getComponentLoggerExtraParams(),
        });
        item.loggerParams = {
          index: index + 1,
          goods_id: item.id + '',
          banner_id: bannerId,
          component: 'points_goods',
          item_type: 'points_goods',
          ...this.getComponentLoggerExtraParams(),
        };
        loggerList.push(item.loggerParams);
      });

      this.ensureAppLogger('view', loggerList);

      return _list;
    },

    handleBuyBtnClick({ detail }) {
      const { isInWhitelist = false, url = '', pointsGoodsId = 0 } = detail;

      if (this.isCoupon(detail) && isInWhitelist) {
        // 如果是优惠券，并且类型是优惠券的话就跳转到新webview页面
        openWebView('/wscump/pointstore/goodsdetails', {
          query: {
            kdt_id: app.getKdtId() || 0,
            offline_id: app.getOfflineId() || 0,
            goods_id: pointsGoodsId,
          },
        });

        return;
      }

      if (!url) {
        return;
      }

      wx.navigateTo({
        url,
      });
    },

    /** 积分商品 */
    isNormalGoods(item) {
      return +item.goodsType === GOODS_TYPE.normal;
    },

    /** 积分优惠券 */
    isCoupon(item) {
      return +item.goodsType === GOODS_TYPE.coupon;
    },

    /** 积分权益卡，三期新支持 */
    isCard(item) {
      return +item.goodsType === GOODS_TYPE.card;
    },

    getUrl(item) {
      let url;

      if (this.isNormalGoods(item)) {
        url = `/packages/goods/points/index?alias=${item.alias}&pay_way=integral`;
      } else if (this.isCoupon(item)) {
        url = `/packages/ump/integral-store/coupon/index?id=${item.goodsId}&alias=${item.idAlias}&type=${item.groupType}`;
      } else if (this.isCard(item)) {
        url = `/packages/pointstore/goods-details/index?kdt_id=${this.data.kdtId}&goods_id=${item.pointsGoodsId}`;
      }

      return url;
    },

    handleViewAllClick() {
      navigate.navigate({
        url: '/packages/ump/integral-store/index',
      });
    },
  },
});
