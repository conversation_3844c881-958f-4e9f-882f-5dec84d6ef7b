<view class="showcase-goods">
  <cap-goods
    wx:if="{{ goodsList && goodsList.length }}"
    app-id="{{ appId }}"
    list="{{ goodsList }}"
    layout="{{ config.layout }}"
    size-type="{{ 6 }}"
    redirect-type="{{ 3 }}"
    show-title="{{ true }}"
    show-sub-title="{{ config.showSubTitle }}"
    show-price="{{ config.showPrice }}"
    buy-button-type="{{ config.layout === 1 ? 10 : 8 }}"
    show-buy-button="{{ config.showBuyButton }}"
    show-corner-mark="{{ showCornerMark }}"
    corner-mark-type="{{ config.cornerMarkType }}"
    corner-mark-image="{{ config.cornerMarkImage }}"
    button-text="{{ config.buttonText || '兑换' }}"
    image-fill-style="{{ config.imageFillStyle }}"
    image-ratio="{{ 1 }}"
    bind:item-click="handleBuyBtnClick"
    bind:buy="handleBuyBtnClick"
  />

  <block wx:if="{{ config.showAllBtn && goodsList.length }}">
    <view class="view-more">
      <view class="view-more-text" bind:tap="handleViewAllClick">
        <text>查看全部</text>
        <zan-icon type="arrow" class="arrow"/>
      </view>
    </view>
  </block>
</view>
