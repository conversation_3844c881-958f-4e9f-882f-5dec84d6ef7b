import { node as request } from 'shared/utils/request';

function getList(data) {
  return request({
    path: '/wscshop/showcase/pointsGoods/getList.json',
    data: { ...data, isWeappCardSupport: 1, }
  });
}

function getListById(data) {
  return request({
    path: '/wscshop/showcase/pointsGoods/getListById.json',
    data: { ...data, isWeappCardSupport: 1, },
  });
}
export default {
  getList,
  getListById
};
