// import pick from '@youzan/weapp-utils/lib/pick';
import { node as request } from 'shared/utils/request';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { shopCanPayForKnowledge } from '../utils/pctUtils';
import { LIST_LAYOUTS } from './constants';
// import { format } from '@youzan/weapp-utils/lib/time-utils';
import { moment as formatDate } from 'utils/time';

const app = getApp();

Component({
  options: {
    addGlobalClass: true,
  },

  type: 'knowledge-goods',

  behaviors: [componentBehavior, loggerBehavior],

  properties: {
    componentData: {
      type: Object,
      observer() {
        this.updateContent();
      },
      value: {},
    },
    kdtId: {
      type: String,
    },
  },

  data: {
    itemType: 'knowledge-goods',
    showAllGoodsEntry: true, // 是否显示全部
    title: '课程', // 标题名称
    displayTitleBar: 1, // 标题栏显示
    goodsFromMode: 0, // 选择方式
    maxNewestGoods: 6, // 最新商品个数
    goodList: [], // 商品列表
    goodsFrom: '', // 商品来源
    goodsPadding: 10, // 商品边距
    goodsRadius: 0, // 商品边角
    goodsStyle: 0, // 商品样式
    imgSize: 0, // 图片填充
    listMode: 0, // 列表样式
    pageMargin: 15, // 页面边距
    textAlign: 1, // 文本对齐
    textStyle: 0, // 文本样式
    displayContent: [], // 线下课名称， 线下课简介，线下课标签，上课时间，价格
    classWrap: 'cap-knowleadge-item',
    isGoodsCanBuy: true,
    fontSizes: {
      title: 0,
      subtitle: 0,
      price: 0,
    },
    courseType: 2, // 线下课类型
  },

  // attached() {
  // },

  methods: {
    showPriceInfo() {
      return shopCanPayForKnowledge();
    },

    updateContent() {
      const componentData = this.data.componentData;
      const itemData = componentData.knowledgeGoodsData;
      if (itemData.goodsFrom !== 'group') {
        itemData.displayContent.sort();
      }
      app.getShopTheme().then((res) => {
        this.setData(
          {
            itemType: 'knowledge-goods',
            classWrap: `cap-knowleadge-item goods-text-style${
              itemData.textStyle
            } goods-radius${itemData.goodsRadius} ${
              LIST_LAYOUTS[itemData.listMode]
            }`,
            isGoodsCanBuy: this.showPriceInfo(),
            fontSizes: this.getFontSizes(itemData),
            themeColor: res.colors['main-bg'],
            ...itemData,
          },
          () => {
            const { goodsFrom } = this.data;
            switch (goodsFrom) {
              case 'course':
                this.getAllCourseList();
                break;
              case 'column':
                this.getPaidContentList('columnlist');
                break;
              case 'content':
                this.getPaidContentList('contentlist');
                break;
              case 'live':
                this.getPaidContentList('livelist');
                break;
              case 'group':
                this.getGroupList();
                break;
              case 'classLive':
                this.getPaidContentList('livelist');
                break;
              default:
                this.getAllCourseList();
            }
          }
        );
      });
    },

    getGroupList() {
      const itemData = this.data.componentData.knowledgeGoodsData;
      const { groupList } = itemData;
      if (groupList && groupList.length) {
        const { kdtId } = this.data;
        const buyerId = app.getBuyerId();
        return request({
          path: 'wscshop/edu/course/group/findItemGroupPageForWym.json',
          data: {
            kdtId,
            pageNumber: 1,
            buyerId,
            alias: groupList[0].alias || '',
            pageSize: itemData.maxNewestGoods || 9,
          },
        }).then((data) => {
          if (data && data.content) {
            const finalResult = this.transformResult(data.content);
            this.setData({ goodList: this.getShowPrice(finalResult) });
          }
        });
      }
    },

    getFontSizes(itemData) {
      const { listMode = 0 } = itemData;
      let fontSizes = {
        title: 0,
        subtitle: 0,
        price: 0,
      };
      switch (listMode) {
        case 0:
          fontSizes = {
            title: 16,
            price: 16,
          };
          break;
        case 1:
        case 3:
        case 4:
          fontSizes = {
            title: 14,
            price: 14,
          };
          break;
        case 2:
        case 5:
          fontSizes = {
            title: 12,
            price: 14,
          };
          break;
        default:
          fontSizes = {
            title: 16,
            price: 16,
          };
      }
      return fontSizes;
    },

    getGoodsFrom(type) {
      switch (type) {
        case 1:
          return 'column';
        case 2:
          return 'content';
        case 4:
          return 'live';
        case 10:
          return 'course';
        default:
          return '';
      }
    },

    getDisplayContent(type) {
      const { knowledgeGoodsData = {} } = this.data.componentData;
      const { displayContent = {} } = knowledgeGoodsData;
      switch (type) {
        case 1:
          return displayContent.column;
        case 2:
          return displayContent.content;
        case 4:
          return displayContent.live;
        case 10:
          return displayContent.course;
        default:
          return '';
      }
    },

    getShowPrice(goodList) {
      // const { goodList } = this.data;
      const goodListWithPrice = [].concat(goodList);
      return goodListWithPrice.map((item) => {
        // eslint-disable-next-line no-nested-ternary
        item.showPrice =
          item.sellerType !== 2
            ? item.price
              ? `¥${item.price / 100}`
              : '免费'
            : '';
        if (this.data.goodsFrom === 'classLive') {
          return item;
        }
        if (item.buyStatus) {
          if (item.buyStatus.isBought) {
            item.showPrice = '已购买';
          } else if (item.buyStatus.isFree) {
            item.showPrice = '免费试读';
          } else if (item.buyStatus.isFreeForVip) {
            item.showPrice = '会员免费';
          } else if (item.buyStatus.isGroupOn || item.buyStatus.isVipDiscount) {
            item.showPrice = item.buyStatus.price
              ? `¥${item.buyStatus.price / 100}`
              : '免费';
          }
        }
        return item;
      });
    },

    getAllCourseList() {
      const { goodsFromMode, goodList, kdtId, courseType = 2 } = this.data;
      let params = {};
      if (goodsFromMode && goodList.length) {
        params = {
          kdtId,
          aliases: goodList.map((item) => item.alias).join(','),
        };
      } else {
        params = {
          kdtId,
          pageNumber: 1,
          pageSize: this.data.maxNewestGoods,
        };
      }
      if (goodsFromMode === 0) {
        params.courseType = courseType;
      }
      if (goodsFromMode === 1) {
        params.source = 1;
      }
      request({
        path: '/wscshop/edu/course/v2/findPageForWym.json',
        data: params,
      }).then((result) => {
        if (!result || !result.content || !result.content.length) {
          return;
        }
        const finalResult = this.transformResult(result.content);
        this.setData({ goodList: this.getShowPrice(finalResult) });
      });
    },

    getPaidContentList(path) {
      const { goodsFromMode, goodList, kdtId, goodsFrom } = this.data;
      let params = {};
      if (goodsFromMode) {
        params = {
          kdtId,
          ids: goodList.map((item) => item.id).join(','),
          source: 1,
        };
      } else {
        params = {
          kdtId,
          pageNumber: 1,
          pageSize: this.data.maxNewestGoods,
        };
      }
      if (goodsFrom === 'live' || goodsFrom === 'classLive') {
        params.liveQueryScene = goodsFrom === 'live' ? 1 : 2;
      }
      request({
        path: `/wscshop/showcase/knowledge/${path}`,
        data: params,
      }).then((result) => {
        if (!result || !result.length) {
          return;
        }
        const finalResult = this.transformResult(result);
        this.setData({ goodList: this.getShowPrice(finalResult) });
      });
    },

    transformResult(content) {
      if (content && content.length) {
        const finalResult = content
          .filter((item) => item != null)
          .map((item) => {
            item.goodsFrom =
              this.data.goodsFrom !== 'group'
                ? this.data.goodsFrom
                : this.getGoodsFrom(item.type);
            item.displayContent =
              this.data.goodsFrom !== 'group'
                ? this.data.displayContent
                : this.getDisplayContent(item.type);
            item.kdtId = this.data.kdtId;
            if (item.liveStartAt) {
              item.liveStartAt = this.getTimeStamp(item.liveStartAt,
                this.data.goodsFrom === 'classLive' ? 'YYYY-MM-DD HH:mm:ss' : 'MM-DD HH:mm:ss');
            }
            if (item.courseStartAt) {
              item.courseStartAt = this.getCourseTimeStamp(item.courseStartAt);
            }
            if (item.courseEndAt) {
              item.courseEndAt = this.getCourseTimeStamp(item.courseEndAt);
            }
            return item;
          });
        return finalResult;
      }
      return content;
    },

    getCourseTimeStamp(time) {
      if (typeof time === 'number') {
        return formatDate(new Date(time), 'YYYY-MM-DD');
      }
      return formatDate(time.replace(/-/g, '/'), 'YYYY-MM-DD');
    },

    getTimeStamp(time, format = 'MM-DD HH:mm:ss') {
      if (typeof time === 'number') {
        return formatDate(new Date(time), format);
      }
      return formatDate(time.replace(/-/g, '/'), format);
    },

    goToAllColumn() {
      let url = '';
      const { goodsFrom } = this.data;
      const itemData = this.data.componentData.knowledgeGoodsData;
      const { groupList = [] } = itemData;
      const alias = (groupList.length && groupList[0].alias) || '';
      switch (goodsFrom) {
        case 'course':
          // url = '/packages/edu/goods/list/index?subType=10';
          url = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
            'https://h5.youzan.com/wscvis/edu/all-course?kdt_id=' +
              app.getKdtId()
          )}`;
          break;
        case 'column':
          url = '/packages/paidcontent/list/index?type=column';
          break;
        case 'content':
          url = '/packages/paidcontent/list/index?type=content';
          break;
        case 'live':
          url = '/packages/paidcontent/list/index?type=live';
          break;
        case 'group':
          url = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
            'https://h5.youzan.com/wscvis/edu/course-group?alias=' +
              alias +
              '&kdt_id=' +
              app.getKdtId()
          )}`;
          break;
        case 'classLive':
          url = `/packages/paidcontent/list/index?type=classLive`;
          break;
        default:
          url = '';
      }
      wx.navigateTo({
        url,
      });
    },
  },
});
