<view wx:if="{{ goodList.length }}" class="knowleadge-container">
  <view class="theme-feature-knowledge-goods van-hairline--top-bottom">
    <view wx:if="{{ displayTitleBar }}" class="knowledge-goods-title">
      <text class="title-text">{{ title }}</text>
      <form-button bindtap="goToAllColumn" business-module="owlMiniProgram">
        <view wx:if="{{ showAllGoodsEntry }}" class="theme-feature-column__view-all">
          <text class="theme-feature-column__all-text">全部</text>
          <van-icon style="margin-top: 2px;" name="arrow" custom-class="theme-feature-column__van-icon" />
        </view>
      </form-button>
    </view>
    <view class="knowleadge-item-container">
      <view style="margin: {{ pageMargin }}px" class="{{ listMode === 5 ? 'knowleadge-scroll-wrap' : 'cap-knowleadge-wrap' }}">
        <block wx:for="{{ goodList }}" wx:key="id">
          <cap-knowleadge-item
            wx:if="{{ item.goodsFrom === 'course' }}"
            class="{{ listMode=== 4 ? (index%3 === 0 ? classWrap + '-big' : classWrap + '-small') : classWrap }}"
            itemType="course"
            content="{{ item }}"
            themeColor="{{ themeColor }}"
            itemIndex="{{ index }}"
            goodsDetail="{{ { goodsFrom: item.goodsFrom, goodsPadding, goodsRadius,goodsStyle, imgSize, listMode, pageMargin, textAlign, textStyle, displayContent: item.displayContent } }}"
          >
            <view slot='title' class='knowledge-title-wrap'> 
              <text style="font-size: {{ fontSizes.title }}px" class='knowledge-title'>{{ item.title }}</text>
            </view>
            <view wx:if="{{ item.sellPoint }}" class='knowledge-subtitle' slot='description'>{{ item.sellPoint }}</view>
            <view wx:if="{{ item.courseStartAt }}" class='knowledge-subtitle knowledge-timerange' slot='time-range'>
              <van-icon name="clock" color="#999" />
              <text >{{ item.courseStartAt }} ~ {{ item.courseEndAt }}</text>
            </view>
            <view class='knowledge-price-wrap' slot='course-price'>
              <text wx:if="{{ isGoodsCanBuy }}" class="knowledge-price" style="font-size: {{ fontSizes.price }}px; line-height: {{ fontSizes.price }}px; color: {{ themeColor }}">{{ item.showPrice }}</text>
              <text wx:if="{{ item.buyStatus &&  item.buyStatus.isVipDiscount}}" class='knowledge-isvip'>会员价</text>
            </view>
          </cap-knowleadge-item>
          <cap-knowleadge-item
            wx:if="{{ item.goodsFrom==='column' }}"
            class="{{ listMode=== 4 ? (index%3 === 0 ? classWrap + '-big' : classWrap + '-small') : classWrap }}"
            itemType='column'
            itemIndex="{{ index }}"
            themeColor="{{ themeColor }}"
            content="{{ item }}"
            goodsDetail="{{ { goodsFrom: item.goodsFrom, goodsPadding, goodsRadius,goodsStyle, imgSize, listMode, pageMargin, textAlign, textStyle, displayContent: item.displayContent } }}"
          >
            <view class='knowledge-title-wrap' slot='title' >
              <text wx:if="{{ item.buyStatus &&  item.buyStatus.isGroupOn}}" class='knowledge-isgroupon'>{{ item.buyStatus.groupOnNum }}人团</text>
              <text style="font-size: {{ fontSizes.title }}px" class='knowledge-title' >{{ item.title }}</text>
            </view>
            <view class='knowledge-subtitle' slot='description'>{{ item.summary }}</view>
            <view class='knowledge-price-wrap' slot='column-price'>
              <text wx:if="{{ isGoodsCanBuy }}" class='knowledge-price' style="font-size: {{ fontSizes.price }}px; line-height: {{ fontSizes.price }}px; color: {{ item.showPrice === '已购买' ? '#000' : themeColor }}">{{ item.showPrice }}</text>
              <text wx:if="{{ item.buyStatus &&  item.buyStatus.isVipDiscount}}" class='knowledge-isvip'>会员价</text>
            </view>
          </cap-knowleadge-item>
          <cap-knowleadge-item
            wx:if="{{ item.goodsFrom==='content' }}"
            class="{{ listMode=== 4 ? (index%3 === 0 ? classWrap + '-big' : classWrap + '-small') : classWrap }}"
            itemType='content'
            themeColor="{{ themeColor }}"
            itemIndex="{{ index }}"
            content="{{ item }}"
            goodsDetail="{{ { goodsFrom: item.goodsFrom, goodsPadding, goodsRadius,goodsStyle, imgSize, listMode, pageMargin, textAlign, textStyle, displayContent: item.displayContent } }}"
          >
            <view class='knowledge-title-wrap' slot='title'>
              <text wx:if="{{ item.buyStatus &&  item.buyStatus.isGroupOn}}" class='knowledge-isgroupon'>{{ item.buyStatus.groupOnNum }}人团</text>
              <text style="font-size: {{ fontSizes.title }}px" class='knowledge-title' >{{ item.title }}</text>
            </view>
            <view class='knowledge-subtitle' slot='description'>{{ item.summary }}</view>
            <view wx:if="{{ item.columnTitle }}" class='knowledge-subtitle' slot='belong-column'>专栏：{{ item.columnTitle }}</view>
            <view class='knowledge-price-wrap' slot='content-price'>
              <text wx:if="{{ isGoodsCanBuy }}" class='knowledge-price' style="font-size: {{ fontSizes.price }}px; line-height: {{ fontSizes.price }}px; color: {{ item.showPrice === '已购买' ? '#000' : themeColor }}">{{ item.showPrice }}</text>
              <text wx:if="{{ item.buyStatus &&  item.buyStatus.isVipDiscount}}" class='knowledge-isvip'>会员价</text>
            </view>
          </cap-knowleadge-item>
          <cap-knowleadge-item
            wx:if="{{ item.goodsFrom==='live' }}"
            class="{{ listMode=== 4 ? (index%3 === 0 ? classWrap + '-big' : classWrap + '-small') : classWrap }}"
            itemType='live'
            itemIndex="{{ index }}"
            themeColor="{{ themeColor }}"
            content="{{ item }}"
            goodsDetail="{{ { goodsFrom: item.goodsFrom, goodsPadding, goodsRadius,goodsStyle, imgSize, listMode, pageMargin, textAlign, textStyle, displayContent: item.displayContent } }}"
          >
            <view class='knowledge-title-wrap' slot='title'>
              <text wx:if="{{ item.buyStatus &&  item.buyStatus.isGroupOn}}" class='knowledge-isgroupon'>{{ item.buyStatus.groupOnNum }}人团</text>
              <text style="font-size: {{ fontSizes.title }}px" class='knowledge-title' >{{ item.title }}</text>
            </view>
            <view slot='description' class='knowledge-subtitle'>{{ item.summary }}</view>
            <view wx:if="{{ item.liveStartAt}}" class='knowledge-subtitle' slot='live-time'> 
              <van-icon name="clock" color="#e5e5e5" style="margin-right: 4px"/>
              <text wx:if="{{ !listMode || (listMode=== 4 && index%3 === 0) }}">开播时间：{{ item.liveStartAt }}</text>
              <text wx:else>{{ item.liveStartAt }}</text>
            </view>
            <view class='knowledge-price-wrap' slot='live-price'>
              <text wx:if="{{ isGoodsCanBuy }}" class='knowledge-price' style="font-size: {{ fontSizes.price }}px; line-height: {{ fontSizes.price }}px; color: {{ item.showPrice === '已购买' ? '#000' : themeColor }}">{{ item.showPrice }}</text>
              <text wx:if="{{ item.buyStatus &&  item.buyStatus.isVipDiscount}}" class='knowledge-isvip'>会员价</text>
            </view>
          </cap-knowleadge-item>
          <cap-knowleadge-item
            wx:if="{{ item.goodsFrom==='classLive' }}"
            class="{{ listMode=== 4 ? (index%3 === 0 ? classWrap + '-big' : classWrap + '-small') : classWrap }}"
            itemType='live'
            itemIndex="{{ index }}"
            themeColor="{{ themeColor }}"
            content="{{ item }}"
            goodsDetail="{{ { goodsFrom: item.goodsFrom, goodsPadding, goodsRadius,goodsStyle, imgSize, listMode, pageMargin, textAlign, textStyle, displayContent: item.displayContent } }}"
          >
            <view class='knowledge-title-wrap' slot='title'>
              <text wx:if="{{ item.buyStatus &&  item.buyStatus.isGroupOn}}" class='knowledge-isgroupon'>{{ item.buyStatus.groupOnNum }}人团</text>
              <text style="font-size: {{ fontSizes.title }}px" class='knowledge-title' >{{ item.title }}</text>
            </view>
            <view slot='description' class='knowledge-subtitle'>{{ item.summary }}</view>
            <view wx:if="{{ item.liveStartAt}}" class='knowledge-subtitle' slot='live-time'> 
              <van-icon name="clock" color="#e5e5e5" style="margin-right: 4px"/>
              <text wx:if="{{ !listMode || (listMode=== 4 && index%3 === 0) }}">上课时间：{{ item.liveStartAt }}</text>
              <text wx:else>{{ item.liveStartAt }}</text>
            </view>
            <view class='knowledge-price-wrap' slot='live-price'>
              <text wx:if="{{ isGoodsCanBuy }}" class='knowledge-price' style="font-size: {{ fontSizes.price }}px; line-height: {{ fontSizes.price }}px; color: {{ item.showPrice === '已购买' ? '#000' : themeColor }}">{{ item.showPrice }}</text>
              <text wx:if="{{ item.buyStatus &&  item.buyStatus.isVipDiscount}}" class='knowledge-isvip'>会员价</text>
            </view>
          </cap-knowleadge-item>
        </block>
      </view>
    </view>
  </view>
</view>