.knowleadge-container {
}

.knowleadge-container .title-text {
  margin-top: 15px;
  font-size: 16px;
}

.knowleadge-container .theme-feature-column__view-all {
  margin-top: 15px;
  font-size: 12px;
  color: #666;
  display: flex;
  line-height: 15px;
}



.knowledge-goods-title {
  position: relative;
  display: flex;
  padding: 0 15px;
  flex-direction: row;
  height: 40px;
  overflow-x: hidden;
  align-items: center;
  justify-content: space-between;
}

.knowleadge-item-container {
  position: relative;
  width: 100%;
  overflow: auto;
}

.cap-knowleadge-wrap {
  display: flex;
  flex-flow: wrap;
}

.cap-knowleadge-item {
  width: 100%;
  position: relative;
  margin-top: 5px;
}

.knowledge-subtitle {
  color: #999;
  font-size: 12px;
  margin: 0 10px 5px 10px ;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.knowledge-title-wrap {
  margin: 0 10px 5px 10px ;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* .knowledge-title {
  font-weight: bold;
} */

.knowledge-timerange text {
  margin-left: 5px;
}

.knowledge-price-wrap {
  margin: 10px ;
}

.knowledge-horizontal-layout .knowledge-price-wrap {
  margin: 0 10px 10px 10px;
}

/* .knowledge-price {
  color: #f44;
} */

.knowledge-isgroupon {
  color: #f44;
  margin-right: 5px;
  font-size: 10px;
  background-color: #ffdada;
  padding: 2px 4px;
  border-radius: 2px;
  /* border: 1px #f44 solid; */
}

.knowledge-isvip {
  font-size: 12px;
  color: #B29A37;
  background-color: #FFEFAD;
  margin-left: 10px;
  padding: 2px 4px;
}

/* 大图模式 */
.knowledge-huge {
  width: 100%
}

/* 一行两个 */
.knowledge-twoinrow {
  width: 50%;
  /* width: 50%; */
}

/* 一行三个 */
.knowledge-threeinrow {
  width: 33.3%;
}

/* 一大两小 */
.knowledge-huge-twosmall-big {
  width: 100%;
}

.knowledge-huge-twosmall-big .knowledge-title {
  font-size: 16px !important;
}

.knowledge-huge-twosmall-small {
  width: 50%;
}

/* 滑动模式 */
.knowleadge-scroll-wrap {
  display: flex;
  overflow: visible;
}

.knowledge-scroll {
  min-width: 40%;
}

/* 商品倒角 */
/* 直角 */
.goods-radius0 {
  border-radius: 0;
}

/* 圆角 */
.goods-radius1 {
  border-radius: 8px;
}

/* 文本样式 */
.goods-text-style0 .knowledge-price,
.goods-text-style0 .knowledge-title{
 font-weight: bold;
}