import spm from 'shared/utils/spm';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { BIZ_NAME_MAP, PLUGIN_SOURCE_MAP } from './constants';
import componentBehavior from '../behaviors/component-behavior';

Component({
  behaviors: [componentBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {}
    },
    componentIndex: {
      type: Number,
      value: -1
    },
    pageRandomNumber: String
  },

  data: {
    layoutConfig: {},
    titleConfig: {},
    bizName: '',
    goodsNumber: 6,
    componentLoggerType: '',
  },

  attached() {
    this.initBaseConfig();
  },

  methods: {
    initBaseConfig() {
      const { componentData, componentIndex } = this.data;
      const { goodsComponents, titleComponents } = mapKeysCase.toCamelCase(componentData);
      const { countType = 0, goodsNumberV2: goodsNumber = 6 } = goodsComponents;
      const { source = 0, showTitleComponent = 1 } = titleComponents;

      const titleConfig = {
        showTitleComponent: +showTitleComponent === 1,
        title: titleComponents.title,
        showMethod: titleComponents.showMethod,
        subTitle: titleComponents.subTitle,
        backgroundColor: titleComponents.backgroundColor
      };

      this.setData({
        titleConfig,
        layoutConfig: goodsComponents,
        bizName: this.getBizName(+source),
        goodsNumber: +countType === 0 ? goodsNumber : 30,
        componentLoggerType: 'recommend.' + (componentIndex + 1),
      });
    },

    getBizName(source) {
      const pageSpmType = spm.getPageSpmTypeId();
      let logType = 'f';
      if (pageSpmType) {
        logType = pageSpmType.split('.')[0];
      }

      if (source === 0) return BIZ_NAME_MAP[logType] || BIZ_NAME_MAP.f;

      return PLUGIN_SOURCE_MAP[source];
    }
  }
});
