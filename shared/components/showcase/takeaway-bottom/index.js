import WscComponent from 'pages/common/wsc-component/index';
import { node } from 'shared/utils/request';
import get from '@youzan/weapp-utils/lib/get';

const app = getApp();

WscComponent({
  properties: {
    alias: String,
    startFee: {
      type: Number,
      value: 0
    },
    allChoosedNum: {
      type: Number,
      value: 0
    },
    allChoosedPrice: {
      type: Number,
      value: 0
    },
    takeoutCartData: {
      type: Array,
      value: []
    }
  },

  externalClasses: ['iphonex-class'],

  methods: {
    showTakeoutCartAction() {
      const hasNum = this.data.takeoutCartData.some((goods) => goods.num > 0);
      if (!this.data.takeoutCartData.length || !hasNum) return;
      this.triggerEvent('toggleCart', true);
    },
    goToPay() {
      const goodsList = [];
      this.data.takeoutCartData.forEach((goods) => {
        if (goods.num > 0) {
          const skuId = goods.skuId === 'empty' ? goods.sku.collectionId : goods.skuId;
          goodsList.push({
            activityAlias: '',
            activityId: 0,
            activityType: 0,
            message: goods.message,
            num: goods.num,
            price: goods.price,
            skuId,
            goodsId: goods.id,
            kdtId: app.getKdtId(),
            bizTracePointExt: ''
          });
        }
      });

      const goToBuyData = this.generateBuyOrderData(goodsList);
      this.postBookKey(goToBuyData)
        .then(({ bookKey }) => {
          wx.navigateTo({
            url: `/packages/order/index?orderFrom=cart&bookKey=${bookKey}`
          });
        })
        .catch((err) => {
          if (typeof err === 'string') {
            wx.showToast({
              icon: 'none',
              title: err
            });
          } else {
            wx.showToast({
              icon: 'none',
              title: err.message
            });
          }
        });
    },
    postBookKey(data, method = 'POST') {
      const config = {};
      return node({
        path: '/wsctrade/order/book.json',
        data,
        method,
        config
      });
    },
    generateBuyOrderData(goodsList) {
      const config = {
        canyinChannel: 1,
        canyinIds: []
      };
      const items = [];
      const itemSources = [];
      const activities = [];

      const sellers = [
        {
          kdtId: app.getKdtId(),
          storeId: app.getOfflineId() || 0,
        },
      ];

      goodsList.forEach((goods) => {
        const skuId = goods.skuId === 'empty' ? goods.sku.collectionId : goods.skuId;
        const baseId = {
          kdtId: app.getKdtId(),
          goodsId: goods.goodsId,
          skuId,
          propertyIds: goods.propertyIds || [],
          activityId: goods.activityId || 0,
          activityType: +goods.activityType || 0
        };

        // 外部订单来源
        const tpps = get(goods, 'bizExtension.cartBizMark.tpps');
        const item = {
          ...baseId,
          storeId: goods.storeId || 0,
          price: goods.payPrice || 0,
          num: goods.num,
          itemMessage: JSON.stringify(goods.message || {}),
          extensions: { tpps },
          isSevenDayUnconditionalReturn: goods.isSevenDayUnconditionalReturn || false
        };

        if (goods.deliverTime) {
          item.deliverTime = goods.deliverTime;
        }

        const itemSource = {
          ...baseId
        };

        const activity = {
          ...baseId,
          activityAlias: goods.activityAlias || ''
        };

        items.push(item);
        itemSources.push(itemSource);
        activities.push(activity);

        if (goods.canyinId) {
          config.canyinIds.push(goods.canyinId);
        }

        // 餐饮加料按单个商品计算（小程序这里还用不上加料）
        (goods.ingredientInfoList || []).forEach((feedGoods) => {
          items.push({
            kdtId: feedGoods.kdtId,
            goodsId: feedGoods.goodsId,
            skuId: feedGoods.skuId,
            num: goods.num,
            price: feedGoods.price,
            extensions: { tpps }
          });

          activities.push({
            kdtId: feedGoods.kdtId,
            goodsId: feedGoods.goodsId,
            skuId: feedGoods.skuId
          });
        });
      });

      return {
        config,
        items,
        sellers,
        source: {
          itemSources,
          orderFrom: 'cart'
        },
        ump: {
          activities
        }
      };
    }
  }
});
