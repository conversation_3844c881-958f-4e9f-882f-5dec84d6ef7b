<view class="bottomcart iphonex-class">
  <view
    class="bottomcart__cart-icon {{ allChoosedNum == 0 ? 'bottomcart__cart-icon--zero bottomcart__cart-icon--disabled' : '' }}"
    bindtap="showTakeoutCartAction"
  >
    <view class="bottomcart__icon-wrapper">
      <view class="bottomcart__icon"></view>
      <view class="bottomcart__choosed-num {{ allChoosedNum >= 10 ? 'bottomcart__choosed-num--big' : '' }}">
        {{ allChoosedNum }}
      </view>
    </view>
    <view class="bottomcart__choosed-price-wrapper">
      合计：<text class="bottomcart__choosed-price">￥{{ utils.cent2yuan(allChoosedPrice) }}</text>
    </view>
  </view>

  <view wx:if="{{ ((allChoosedPrice === 0) && (allChoosedPrice <= startFee)) }}"
    class="bottomcart__buy-button bottomcart__buy-button--disabled"
  >
    满{{ utils.cent2yuan(startFee) }}元起送
  </view>
  <view wx:elif="{{ ( allChoosedPrice !== 0 && allChoosedPrice < startFee ) }}"
    class="bottomcart__buy-button bottomcart__buy-button--disabled"
  >
    还差{{ utils.getPriceGap(startFee, allChoosedPrice) }}元起送
  </view>
  <user-authorize
    wx:else
    scene="click_buy_now"
    style="float: right"
    bind:next="goToPay"
  >
    <view class="bottomcart__buy-button">去结算</view>
  </user-authorize>
</view>

<wxs src="./index.wxs" module="utils" />
