<view wx:if="{{ show }}">
  <van-cell
    wx:if="{{ showTitle }}"
    title="{{ componentData.title }}"
    custom-class="hotel-title-cell"
    title-class="hotel-title"
  />
  <view class="showcase-hotel-load" wx:if="{{ loading }}">
    <van-loading size="24px" vertical>加载中...</van-loading>
  </view>
  <view wx:else>
    <room-cell
      wx:for="{{ hotelList }}"
      wx:key="id"
      hotel="{{ item.roomType }}"
      data="{{ componentData }}"
      theme="{{ themeColor }}"
    />
  </view>
</view>