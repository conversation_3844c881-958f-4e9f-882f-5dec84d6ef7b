import { node as request } from 'shared/utils/request';
import openWebView from 'shared/utils/open-web-view';

const app = getApp();

Component({
  options: {
    addGlobalClass: true
  },

  type: 'room_type',

  properties: {
    componentData: {
      type: Object,
      value: {},
    },
    kdtId: {
      type: String
    }
  },

  data: {
    itemType: 'room_type',
    hotelList: [],
    themeColor: '',
    loading: true,
    show: true,
    showTitle: true,
    showTitleAll: true,
  },

  attached() {
    const itemData = this.data.componentData;
    app.getShopTheme().then(res => {
      this.setData({
        themeColor: res.colors['main-bg']
      }, () => {
      });
    });
    this.setData({
      title: itemData.title || '',
      showTitle: parseInt(itemData.show_title, 10) === 1,
      showTitleAll: parseInt(itemData.show_title_all, 10) === 1,
    });
    this.getRoomTypeList(itemData);
  },

  methods: {
    getRoomTypeList(componentData) {
      if (componentData.sub_entry.length === 0) {
        this.setData({
          show: false,
          loading: false,
        });
      } else {
        const newList = componentData.sub_entry.map((item) => {
          return item.id;
        });
        this.setData({
          ids: newList,
        });
        this.fetchRoomTypeList(newList);
      }
    },

    fetchRoomTypeList(id) {
      request({
        path: '/wscshop/showcase/hotel/roomlist.json',
        data: {
          roomTypeIds: JSON.stringify(id),
        }
      }).then((res) => {
        if (!res) {
          return;
        }
        this.getNewList(id, res);
      });
    },

    getNewList(ids, res) {
      res.sort(this.sortFunc('id', ids));
      this.setData({
        hotelList: res,
        loading: false,
      });
    },

    sortFunc(id, arr) {
      return (prev, next) => {
        return arr.indexOf(prev.roomType[id]) - arr.indexOf(next.roomType[id]);
      };
    },

    goToList() {
      if (!this.data.showTitleAll) return;
      openWebView('/wscindustry/hotel/list', {
        query: {
          kdtId: app.getKdtId() || 0,
        }
      });
    },
  },
});
