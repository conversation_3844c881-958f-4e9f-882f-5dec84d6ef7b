import WscComponent from 'shared/common/base/wsc-component';
import money from '@youzan/weapp-utils/lib/money';
import openWebView from 'shared/utils/open-web-view';

const app = getApp();
const defaultImg = '//img01.yzcdn.cn/public_files/2019/09/10/179bbc6e77d7cd4f4b774f868b1b3c26.png';

const defaultHotelList = {
  area: '50',
  bedType: 1,
  cover: {
    url: defaultImg,
  },
  hotelName: '这里显示所属酒店名称',
  id: null,
  name: '这里显示房型，最多显示一行',
  saleDesc: '这里显示房型卖点',
  showPrice: 2988,
  windowType: 1,
};

WscComponent({
  properties: {
    data: {
      type: Object,
      value: {},
    },
    hotel: {
      type: Object,
      value: defaultHotelList,
    },
    theme: {
      type: String,
      value: '#333',
    },
  },

  data: {
    bedTypeContent: {
      1: '大床',
      2: '单人床',
      3: '双床',
      4: '多张床',
      5: '大/双床',
    },
    windowTypeContent: {
      1: '有窗',
      2: '无窗',
      3: '部分有窗',
    },
    hotelClass: 'hotel',
    titleClass: '',
    price: '29.88',
    imgUrl: defaultImg,
    bed: '大床',
    window: '有窗',
  },

  attached() {
    const itemData = this.data.data;
    const itemHotel = this.data.hotel;
    this.setData({
      hotelClass:
        'hotel '
        + (+itemData.border_radius_type === 2 ? 'hotel-radius ' : '')
        + (+itemData.size_type === 7 ? 'hotel-shadow ' : '')
        + (+itemData.size_type === 5 ? 'hotel-border ' : '')
        + (+itemData.size_type === 2 ? 'hotel-simple ' : ''),
      titleClass: +itemData.text_style_type === 2 ? 'text-bolder' : '',
      price: money(itemHotel.showPrice).toYuan(),
      imgUrl: (itemHotel.cover && itemHotel.cover.url) || defaultImg,
      bed: this.data.bedTypeContent[+itemHotel.bedType],
      window: this.data.windowTypeContent[+itemHotel.windowType],
    });
  },

  methods: {
    goToRoom() {
      openWebView('/wscindustry/hotel/room', {
        query: {
          kdtId: app.getKdtId() || 0,
          id: this.data.hotel.hotelId,
          room: this.data.hotel.id,
        }
      });
    }
  },
});
