<view
  class="{{ hotelClass }}"
  style="margin: 0 {{ data.page_margin}}px {{ data.card_margin }}px"
  bindtap="goToRoom"
>
  <view class="hotel-img">
    <image
      src="{{ imgUrl }}"
      class="pic-bg"
      lazy-load="true"
    />
    <image
      src="{{ imgUrl }}"
      class="pic-img"
      lazy-load="true"
      mode="aspectFit"
    />
  </view>
  <view class="hotel-content">
    <view class="hotel-content-title">
      <view
        wx:if="{{ data.show_mark === '1' }}"
        class="hotel-content-title-tag"
      >
        <view class="hotel-content-title-tag-bg" style="background-color: {{ theme }}; opacity: .1;" />
        <view style="color: {{ theme }}">{{ data.mark_name }}</view>
      </view>
      <view class="hotel-content-title-name {{ titleClass }}">{{ hotel.name }}</view>
    </view>
    <view class="hotel-content-tag">
      {{ hotel.area }}m² · {{ window }} · {{ bed }}
    </view>
    <view class="hotel-content-desc">
      {{ hotel.saleDesc }}
    </view>
    <view class="hotel-content-address">
      <van-icon
        name="//b.yzcdn.cn/hotel/detail/icon/hotel-icon.png"
        size="14"
        color="#969799"
      />
      <view class="span">{{ hotel.hotelName }}</view>
    </view>
    <view class="hotel-content-price">
      <span style="color: {{ theme }}">￥</span>
      <span class="hotel-content-price-value" style="color: {{ theme }}">{{ price }}</span>
      <span>起</span>
    </view>
  </view>
</view>