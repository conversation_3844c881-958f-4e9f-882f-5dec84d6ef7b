.hotel {
  position: relative;
  display: block;
  overflow: hidden;

  &-img {
    position: relative;
    width: 100%;
    height: 0;
    padding-top: 56.25%;
    overflow: hidden;

    > .pic-bg {
      position: absolute;
      left: -7%;
      top: -7%;
      width: 114%;
      height: 114%;
      object-fit: cover;
      filter: blur(13px);
    }

    > .pic-img {
      position: absolute;
      display: block;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  &-content {
    position: relative;
    padding: 8px 12px 16px;
    background-color: #fff;

    &-title {
      position: relative;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;

      &-tag {
        position: relative;
        top: -2px;
        display: inline-block;
        border-radius: 2px;
        font-size: 10px;
        padding: 0 6px;
        height: 16px;
        line-height: 16px;
        margin-right: 4px;

        &-bg {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
        }
      }

      &-name {
        display: inline;
        font-size: 16px;
        line-height: 20px;
      }
    }

    &-tag {
      font-size: 12px;
      line-height: 16px;
      color: #969799;
      margin-bottom: 8px;
      overflow: hidden;
      display: -webkit-box;
      height: 18px;
    }

    &-desc {
      font-size: 13px;
      line-height: 17px;
      color: #646566;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    &-address {
      display: flex;
      color: #969799;
      font-size: 12px;
      height: 16px;
      margin: 8px 0 12px;
      align-items: center;

      > .span {
        position: relative;
        top: -1px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        margin-left: 4px;
      }
    }

    &-price {
      display: flex;
      justify-content: flex-end;
      height: 20px;
      font-size: 14px;
      align-items: baseline;
      margin-top: 12px;
      color: #969799;

      &-value {
        font-weight: bolder;
        font-size: 20px;
        margin: 0 2px;
      }
    }
  }

  &-radius {
    border-radius: 8px;
  }

  /* 卡片投影 */
  &-shadow {
    box-shadow: 0 2px 8px rgba(93, 113, 127, .08);
  }

  /* 描边白底 */
  &-border {
    border: 1px solid rgba(50, 50, 51, .1);
  }

  /* 无边透明 */
  &-simple {
    > .hotel-content {
      background-color: inherit;
    }
  }
}

.text-bolder {
  font-weight: bolder;
}