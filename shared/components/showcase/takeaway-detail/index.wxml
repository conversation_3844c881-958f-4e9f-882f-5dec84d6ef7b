<van-popup
  show="{{ show}}"
  bind:close="onClose"
>
  <view class="goods-detail">
    <view class="goods-detail__img">
      <image src="{{ goodsInfo.bigPicUrl }}" mode="widthFix"/>
    </view>
    <view class="goods-detail__info">
      <text class="goods-detail__info--title">{{ goodsInfo.title }}</text>
      <text wx:if="{{ goodsInfo.sellPoint }}" class="goods-detail__info--desc">{{ goodsInfo.sellPoint }}</text>
      <view wx:if="{{ goodsInfo.activity }}" class="goods-detail__info--ump">
        <block wx:if="{{ goodsInfo.activity.discountType }}">
        <van-tag plain type="danger" class="goods-detail__info--ump-tag">
          {{ goodsInfo.activity.discountType === 2 ? goodsInfo.activity.discountValue / 10 + '折' : '减价' }}
        </van-tag>
        </block>
        <block wx:else>
          <van-tag plain type="danger" class="goods-detail__info--ump-tag">会员折扣</van-tag>
        </block>
        <van-tag wx:if="{{ goodsInfo.activity.quota }}" plain type="danger" class="goods-detail__info--ump-tag">{{ '限购' + goodsInfo.activity.quota + '件' }}</van-tag>
      </view>
      <view class="goods-detail__info--price">{{ utils.cent2yuan(goodsInfo.sku.minPrice) + (goodsInfo.sku.noneSku ? '' : '起') }}</view>
      <view wx:if="{{ goodsInfo.sku.oldPrice }}" class="goods-detail__info--oldprice">{{ goodsInfo.sku.oldPrice }}</view>
      <view class="goods-detail__info--opt">
        <input-number
          class="goods-detail__info--opt-input"
          min="{{ choosedGoods[goodsInfo.alias].noneSku ? 0 : choosedGoods[goodsInfo.alias].num }}"
          max="{{ goodsInfo.activity.quota ? goodsInfo.activity.quota : choosedGoods[goodsInfo.alias].noneSku ? choosedGoods[goodsInfo.alias].stockNum : 99999 }}"
          value="{{ choosedGoods[goodsInfo.alias].num }}"
          is-value-show="{{ choosedGoods[goodsInfo.alias].num > 0 }}"
          is-minus-show="{{ choosedGoods[goodsInfo.alias].num > 0}}"
          disabled="{{ goodsInfo.sku.soldStatus === 2 }}"
          bind:change="onChange"
          bind:overlimit="onOverlimit"
        />
      </view>
    </view>
  </view>
</van-popup>

<wxs src="./index.wxs" module="utils"></wxs>
