@mixin ellipsis($line) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;
}

.goods-detail {
  width: 630rpx;
  overflow: scroll;

  &__img {
    width: 100%;
    height: 630rpx;
    text-align: center;

    image {
      max-width: 100%;
      max-height: 100%;
    }
  }

  &__info {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 10px 15px;

    &--title {
      color: #333;
      font-size: 16px;
      font-weight: 500;

      
      @include ellipsis(2);
    }

    &--desc {
      display: block;
      color: #666;
      font-size: 10px;

      @include ellipsis(2);
    }

    &--ump {
      &-tag {
        margin-right: 5px;
      }
    }

    &--price {
      display: inline;
      color: #f44;
      font-size: 14px;
      font-weight: bold;
    }

    &--oldprice {
      display: inline;
      margin-left: 5px;
      font-size: 10px;
      color: #999;
      text-decoration: line-through;
    }

    &--opt {
      position: absolute;
      right: 15px;
      bottom: 15px;
  
      &--sku {
        background-color: #f44;
        width: 45px;
        height: 22px;
        border-radius: 11px;
        font-size: 10px;
        color: #fff;
      }
    }
  }
}