import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    show: {
      type: Boolean
    },

    tagAlias: {
      type: String
    },

    tagId: {
      type: Number
    },

    goodsInfo: {
      type: Object
    },

    choosedGoods: {
      type: Object
    }
  },

  methods: {
    onClose() {
      this.triggerEvent('close-preview');
    },

    onChange(e) {
      const {
        goodsInfo: goods, tagAlias, tagId
      } = this.data;
      this.triggerEvent('change', {
        ...e.detail,
        goods,
        tagAlias,
        tagId
      });
    },

    onOverlimit(e) {
      const {
        goodsInfo: goods, tagAlias, tagId
      } = this.data;
      this.triggerEvent('overlimit', {
        ...e.detail,
        goods,
        tagAlias,
        tagId
      });
    }
  }
});
