import api from './api';
import navigate from 'shared/utils/navigate';

// const app = getApp();
const DEFAULT_IMG_URLS = {
  CHECKED:
    '//img01.yzcdn.cn/upload_files/2021/05/12/FrNNzqyyVNRK-QDZyG9CyarM2wlC.png',
  UNCHECKED:
    '//img01.yzcdn.cn/upload_files/2021/05/12/FvqqUKi11jhjFAwXl1uDZipI4OLB.png',
};

const ImgType = {
  Icon: 'icon',
  Img: 'img',
};

Component({
  properties: {
    componentData: {
      type: Object,
    },
  },

  data: {
    visiable: true,
    containerOversize: false,
    contentIconOversize: false,
    IMG_URLS: { ...DEFAULT_IMG_URLS },
    pointsNum: 0,
    costPoints: 10,
    pointRow: [], // 每行数量，如[10, 8]第一行十个，第二行8个
    rowType: [], // 一行显示几个，一种6个大icon，另一种10个小icon
  },

  attached() {
    this.fetchData();
  },

  methods: {
    async fetchData() {
      const { rewardPointsList } = this.data.componentData;
      const { id } = rewardPointsList[0];
      const { pointsNum = -1, activityModel } = await api.getRewardPointsInfo({
        activityId: id,
      });
      // 没有集点卡隐藏活动入口
      if (pointsNum === -1 && !activityModel) {
        this.setData({
          visiable: false,
        });
        return;
      }
      const {
        minCostPointsPresentRule: { costPoints = 10 } = {},
        receivedCollectionPointPic,
        unreceivedCollectionPointPic,
      } = activityModel || {};

      const imgType = unreceivedCollectionPointPic.startsWith('icon-')
        ? ImgType.Icon
        : ImgType.Img;
      const imgUrls = { ...DEFAULT_IMG_URLS };
      if (
        receivedCollectionPointPic &&
        unreceivedCollectionPointPic &&
        imgType !== ImgType.Icon
      ) {
        imgUrls.CHECKED = receivedCollectionPointPic;
        imgUrls.UNCHECKED = unreceivedCollectionPointPic;
      }

      this.setData({
        IMG_URLS: imgUrls,
        pointsNum,
        costPoints: costPoints > 20 ? 20 : costPoints,
      });
      this.calcSize(costPoints);
      this.calcPointRow(costPoints > 20 ? 20 : costPoints);
    },
    handleClick() {
      const app = getApp();
      const { rewardPointsList } = this.data.componentData;
      const { id } = rewardPointsList[0];
      navigate.navigate({
        url: `/packages/point/home/<USER>
      });
    },
    calcPointRow(costPoints) {
      const pointRow = [];
      while (costPoints > 0) {
        if (costPoints >= 10) {
          pointRow.push(10);
          costPoints -= 10;
        } else {
          pointRow.push(costPoints);
          costPoints = 0;
        }
      }
      this.setData({
        pointRow,
      });
    },
    calcSize(costPoints) {
      let rowType = [];
      let containerOversize = false;
      let contentIconOversize = false;
      if (costPoints > 6) {
        rowType = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      } else {
        rowType = [1, 2, 3, 4, 5, 6];
        contentIconOversize = true;
      }
      if (costPoints > 10) {
        containerOversize = true;
      }
      this.setData({
        rowType,
        containerOversize,
        contentIconOversize,
      });
    },
  },
});
