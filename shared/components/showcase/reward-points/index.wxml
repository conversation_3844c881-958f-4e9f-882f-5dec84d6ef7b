<view class="dc-reward-points {{containerOversize ? 'container--oversize' : 'container--normal'}}" wx:if="{{visiable}}" bind:tap="handleClick">
  <view class="title">
    <view class="left" wx:if="{{costPoints - pointsNum > 0}}">
      再集<view class="left--bold">{{costPoints - pointsNum}}</view>点即可兑换好礼
    </view>
    <view class="left left--done" wx:if="{{costPoints - pointsNum <= 0}}">
      恭喜你，点数已集满，赶紧去兑换吧
    </view>
    <view class="right">
      <view class="count">{{pointsNum}}</view>
      <view class="total">/{{costPoints}}</view>
    </view>
  </view>
  <view class="content {{contentIconOversize ? 'content--oversize' : 'content--normal'}}">
    <view class="icon-wrapper" wx:for="{{pointRow}}" wx:for-item="rowCount" wx:for-index="rowIndex">
      <!-- 为什么不用 j in 10，因为和vue表现不一致，tee是从0开始的 -->
      <view wx:for="{{rowType}}" wx:for-item="j" wx:for-index="k" class="icon {{contentIconOversize ? 'icon--oversize' : 'icon--normal'}}">
        <image wx:if="{{j <= rowCount}}" src="{{(j + rowIndex * rowType.length) <= pointsNum ? IMG_URLS.CHECKED : IMG_URLS.UNCHECKED}}" class="icon {{contentIconOversize ? 'icon--oversize' : 'icon--normal'}}"></image>
        <view wx:if="{{j > rowCount}}" class="icon {{contentIconOversize ? 'icon--oversize' : 'icon--normal'}}"></view>
      </view>
    </view>
  </view>
</view>