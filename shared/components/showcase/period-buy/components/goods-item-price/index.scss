.cap-goods-info {
  &__bottom {
    .cap-goods-info__price {
      font-size: 16px;
    }

    &-0 {
      margin-top: 20px;
      height: 30px;
      position: relative;

      .buy-button {
        top: 0px;
      }

      .cap-goods-info__price {
        display: flex;
        align-items: center;
        font-size: 16px;
      }
    }

    &-1 {
      margin-top: 15px;
      height: 26px;
      display: flex;
      align-items: center;
      position: relative;

      .buy-button {
        top: 4px;
      }
    }

    &-3 {
      height: 44px;
      display: flex;
      align-items: center;
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;

      .buy-button {
        top: 10px;
      }
    }
  }

  &__price-wrap {
    display: flex;
    align-items: flex-end;
  }

  &__symbol {
    font-size: 12px;
    position: relative;
    bottom: 0.15em;
    margin-right: 2px;
  }

  &__yuan,
  &__cent {
    font-size: 18px;
  }

  &__sku {
    height: 12px;
    color: #969799;
    font-weight: normal;
    font-size: 12px;
    line-height: 12px;
    word-break: break-all;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  &__btn {
    font-size: 14px;
  }
}

.buy-button {
  display: inline-block;
  position: absolute;
  font-size: 0;
  right: 0;
}