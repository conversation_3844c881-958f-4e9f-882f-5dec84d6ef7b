<view
  wx:if="{{ !showSkuAfterPrice }}"
  class="cap-goods-info__sku"
  style="width: {{ skuWidth }}px"
>{{ skuContent }}</view>
<view class="cap-goods-info__bottom cap-goods-info__bottom-{{layout}}">
  <view class="cap-goods-info__price">
    <view class="cap-goods-info__price-wrap">
      <theme-view class="cap-goods-info__symbol" color="main-bg">¥ </theme-view>
      <theme-view class="cap-goods-info__yuan" color="main-bg">{{ yuan }}</theme-view>
      <theme-view class="cap-goods-info__cent" color="main-bg">.{{ cent }}</theme-view>
    </view>
    <view
      wx:if="{{ showSkuAfterPrice }}"
      class="cap-goods-info__sku"
      style="width: {{ skuWidth }}px"
    >{{ skuContent }}</view>
  </view>
  <buy-button
    id="buy-button"
    wx:if="{{ infoData.showBuyBtn }}"
    btn-wrap-class="buy-button"
    type="{{ buttonType }}"
    button-text="{{ buttonText }}"
    alias="{{ goodsInfo.alias }}"
    extra-data="{{ extraData }}"
    button-size="{{ buttonSize }}"
    redirect-type="{{ 3 }}"
    need-wx-auth="{{ false }}"
    bind:buy="handleGoodsBuyClick" />
</view>