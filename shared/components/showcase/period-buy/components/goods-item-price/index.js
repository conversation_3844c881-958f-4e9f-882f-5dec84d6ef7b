import goodsItemPriceBehavior from '../../../captain-components/goods-layout/behaviors/goods-item-price-behavior';

const typeMap = {
  0: 4,
  1: 3,
  2: 8
};
const textMap = {
  0: '购买',
  1: '立即购买',
  2: '立即购买',
};

Component({
  behaviors: [goodsItemPriceBehavior],

  properties: {
    goodsInfo: {
      type: Object,
      value: {},
      observer() {
        this.initPriceInfo();
      }
    },
  },

  data: {
    yuan: 0,
    cent: '00',
    skuContent: ''
  },

  attached() {
    this.initData();
  },

  methods: {
    initData() {
      const { infoData: { buyBtnType } } = this.data;
      this.setData(({
        buttonType: typeMap[buyBtnType],
        buttonText: textMap[buyBtnType],
        showSkuAfterPrice: this.getShowSkuAfterPrice(),
      }));
    },
    initPriceInfo() {
      const { skus = [] } = this.data.goodsInfo;
      let minSku = '';
      let price = ['0', '00'];
      let cent = '00';
      let skuContent = '';
      if (skus && skus.length > 0) {
        minSku = skus.reduce((s, c) => (+s.price < +c.price ? s : c));
      }
      if (minSku) {
        price = ('' + (+minSku.price || 0) / 100).split('.');
        skuContent = minSku.description || '';
        if (this.data.layout === 0) {
          skuContent = `（ ${skuContent} ）`;
        }
      }
      const yuan = price[0];
      if (price[1]) {
        if (price[1].length === 1) {
          cent = price[1] + '0';
        }
        cent = price[1];
      }
      this.setData({
        yuan,
        cent,
        skuContent
      });
    },
    getShowSkuAfterPrice() {
      return (this.data.buttonSize !== 'small' || (this.data.layout === 3 && !this.infoData.showTitle));
    },
    calcSkuWidth() {
      if (this.$refs.infoWrap) {
        const wrapWidth = this.$refs.infoWrap.offsetWidth;
        const priceWidth = this.$refs.price.offsetWidth;
        const btnWidth = (this.$refs.buyBtn ? this.$refs.buyBtn.offsetWidth : 0);

        if (this.type === 'big') {
          const maxWidth = wrapWidth - priceWidth - btnWidth - 40;
          const skuContentWidth = this.$refs.sku.offsetWidth;
          this.skuWidth = Math.min(skuContentWidth, maxWidth);
        } else if (this.type === 'list') {
          this.skuWidth = wrapWidth - btnWidth - 10;
        } else if (this.type === 'small') {
          if (!this.showTitle && this.showBuyBtn) {
            this.skuWidth = wrapWidth - btnWidth - 30;
          } else if (!this.showBuyBtn && !this.showTitle) {
            this.skuWidth = wrapWidth - priceWidth - 25;
          } else {
            this.skuWidth = wrapWidth - 20;
          }
        }
      }
    },
    handleGoodsBuyClick() {
      const { goodsInfo } = this.data;
      this.triggerEvent('buy', goodsInfo);
    }
  }
});
