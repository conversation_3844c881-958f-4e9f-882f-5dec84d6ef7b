<view wx:if="{{ isLoaded }}" class="showcase-period-buy-container">
  <goods-layout
    id="cap-goods-layout"
    generic:goods-item-tag-info="goods-item-tag-info"
    generic:goods-item-price="goods-item-price"

    layout="{{ periodbuyData.layout }}"
    image-fill-style="{{ periodbuyData.imageFillStyle }}"
    list="{{ periodbuyData.goodsList }}"
    image-ratio="{{ periodbuyData.imageRatio }}"
    is-show-more="{{ periodbuyData.isShowViewMore }}"
    more-url="/packages/ump/periodbuy-list/index"
    info-data="{{ infoData }}"
    app-id="{{ appId }}"
    redirectType="{{ 3 }}"

    bind:item-click="handleGoodsBuyClick"
    bind:buy="handleGoodsBuyClick"
  />
</view>