
import periodbuyAdaptor from '@youzan/feature-adaptor/lib/periodbuy';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { getPeriodbuyData } from './api';
import getApp from 'shared/utils/get-safe-app';
import navigate from '../../../utils/navigate';
import Args from '@youzan/weapp-utils/lib/args';
// import isEmpty from 'lodash/isEmpty';
// import buildUrl from '@youzan/utils/url/buildUrl';
// import get from 'lodash/get';

const { new2captain } = periodbuyAdaptor;
const app = getApp();
const layoutMap = {
  0: 0,
  1: 1,
  2: 3
};

// - image_fill_style: "1"
// - goods: Array(0)
// - hide_goods_end: "0"
// - goods_ids: Array(0)
// - type: "period_buy"
// - show_all: "1" => isShowMore
// - show_buy_btn: "1" => infoData
// default_image_url: ""
// - activity_ids: Array(0)
// - size: "1" => layout
// show_title: "1" => infoData
// show_desc: "0"
// - order_rule: "0" => 排序方式
// - goods_num: 8
// - buy_btn_type: "0" => infoData
// - goods_source: "0" => 0 自动获取 1 手动
// - ratio: "1,1"
Component({
  behaviors: [componentBehavior, loggerBehavior],

  properties: {
    list: {
      type: Array,
      value: [],
      observer(newValue) {
        this.computedPeriodBuyData({ list: newValue, count: 0 });
      }
    },
  },

  data() {
    return {
      isLoaded: false,
      periodbuyInfo: {},
      infoData: {},
      goodsList: []
    };
  },

  attached() {
    this.initData();
  },

  methods: {
    initData() {
      const { componentData } = this.data;
      const {
        goods_num: goodsNum,
        activity_ids: activityIds,
        goods_source: goodsSource,
        order_rule: orderRule,
        hide_goods_end: hideSoldOutGoods,
        show_title: showTitle,
        show_buy_btn: showBuyBtn,
        buy_btn_type: buyBtnType,
        is_async: isAsync,
        show_desc: showDesc,
      } = componentData;

      this.setData({
        infoData: {
          showTitle: +showTitle === 1,
          showBuyBtn: +showBuyBtn === 1,
          buyBtnType: +buyBtnType,
          showPrice: true,
          showSubTitle: +showDesc === 1,
        }
      });

      if (isAsync) {
        return;
      }

      // goodsSource=0 是自动获取，直接调用接口，否则就根据activityIds 是否为空判断是否要调用获取接口
      if ((activityIds && activityIds.length) || +goodsSource === 0) {
        getPeriodbuyData({
          kdtId: app.getKdtId(),
          goodsNum,
          activityIds: activityIds.join(','),
          goodsSource,
          orderRule,
          hideSoldOutGoods: hideSoldOutGoods || 1,
        }).then(res => {
          this.computedPeriodBuyData(res);
        }).catch(() => {
          this.computedPeriodBuyData({ list: [] });
        });
      }
    },
    computedPeriodBuyData(res) {
      const { list } = res;
      const { componentData } = this.data;
      const { size, goods_num: goodsNum, goods } = this.data.componentData;
      const MAX_NUM = 8;
      const showGoodsNum = goodsNum === 0 ? MAX_NUM : Math.min(goodsNum, MAX_NUM);

      const periodbuyData = new2captain({
        ...componentData,
        goods: list.length > 0 ? list : goods,
      });

      periodbuyData.goodsList = periodbuyData.goodsList.map((item, index) => {
        if (!item.goodsInfo) {
          console.log(item);
        }
        const bannerId = this.getBannerId(index + 1);
        item.goodsInfo && (item.goodsInfo.loggerParams = {
          goods_id: item.id,
          banner_id: bannerId,
          item_type: 'goods',
          item_id: item.id + '',
          component: 'period_buy',
          ...this.getComponentLoggerExtraParams(),
        });

        return {
          ...item.goodsInfo,
          imageUrl: item.goodsInfo && item.goodsInfo.attachmentUrl || '',
          title: item.title,
          skus: item.skus,
          subTitle: item.description
        };
      });
      periodbuyData.layout = layoutMap[size];
      periodbuyData.imageRatio = periodbuyData.layout === 0 ? 0 : 1;

      this.setData({
        isLoaded: true,
        periodbuyData,
        isShowViewMore: periodbuyData.isShowViewMore && res.count > showGoodsNum,
      });
    },

    handleGoodsBuyClick({ detail: { alias = '', loggerParams: { banner_id: bannerId = '' } } }) {
      const url = Args.add('/pages/goods/detail/index', { alias, banner_id: bannerId, ...this.getComponentLoggerExtraParams() });
      navigate.navigate({ url });
    },
  },
});

