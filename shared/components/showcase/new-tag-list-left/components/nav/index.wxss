.nav-container {
  max-height: 100vh;
  width: 80px;
}

.scroll-nav {
  max-height: 100vh;
}

.scroll-nav::-webkit-scrollbar {
  height: 0;
  width: 0;
  color: transparent;
}

.scroll-nav.-fixed {
  position: fixed;
  left: 0;
  top: 0;
}

.scroll-nav.-bottom {
  position: absolute;
  left: 0;
  bottom: 0;
}

.nav-tab {
  font-size: 12px;
  line-height: 1.4;
  box-sizing: border-box;
  color: #666;
  background: #f8f8f8;
  word-break: break-all;
  overflow: hidden;
  border-left: 3px solid transparent;
  padding: 17px 14px 17px 12px;
}

.nav-tab__active {
  color: #333;
  font-weight: 700;
  border-color: #f44;
  background: #fff;
}
