import Args from '@youzan/weapp-utils/lib/args';
import omit from '@youzan/weapp-utils/lib/omit';
import pick from '@youzan/weapp-utils/lib/pick';
import throttle from '@youzan/weapp-utils/lib/throttle';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import formatPrice from '@youzan/utils/money/formatPrice';

import navigate from 'shared/utils/navigate';
import WscComponent from 'shared/common/base/wsc-component';

import appLoggerBehavior from 'shared/components/showcase/behaviors/app-logger-behavior';

import { fetchGoodsByTagAlias } from '../../api';
import capGoodsBehavior from '../../../captain-components/goods/behavior';
import {
  PRICE_ACTIVITY_LIST,
  ACTIVITY_MAP,
  TAGS_THEME_MAP,
  TAGS_THEME_OTHER_COLOR,
  JUMP_UMP_GOODS_DETAIL_MAP,
} from '../../../goods/constants';
const PAGE_SIZE = 6;
const app = getApp();

function createLoadItemCheck() {
  let { _loadItemCheck } = this;

  if (_loadItemCheck) return;

  _loadItemCheck = throttle((ev) => {
    this.genItem(ev);
  }, 300);

  this._loadItemCheck = _loadItemCheck;
}

WscComponent({
  type: 'tag-group',
  behaviors: [appLoggerBehavior, capGoodsBehavior],

  options: {
    addGlobalClass: true,
    pureDataPattern: /^_/, // _xxx 不同步到 视图
  },

  properties: {
    contentScrollTop: {
      type: Number,
      value: 0,
    },

    groupItem: {
      type: Object,
      value: {},
    },

    goodsListConfig: {
      type: Object,
      value: {},
    },

    kdtId: Number,

    offlineId: Number,

    appId: String,

    navIndex: Number,

    infoData: {
      type: Object,
      value: {}
    }
  },

  data: {
    alias: '',
    isLoading: false,
    isEmpty: false,
    page: 1,
    list: [],
    fetchedPages: [],
    _pureList: [],
  },

  ready() {
    this.initObserver();
  },

  methods: {
    initObserver() {
      const ob = this.createIntersectionObserver();
      ob.relativeToViewport({
        bottom: 100,
        top: -100,
      }).observe('.group-hook', () => {
        ob.disconnect();
        this.skeletonItem();
        this.fetchList(1);
      });
    },
    /**
     * 购买
     * @param e
     */
    handleGoodsBuy({ detail: index }) {
      const goodsItem = this.data._pureList[index];
      // 商品分组左侧加购埋点
      const { loggerParams = {} } = goodsItem;
      const { slg } = goodsItem.extra_info || {};
      const uuid = app.logger?.options?.user?.uuid || '';
      if (!isEmpty(loggerParams)) {
        this.ensureAppLogger('click_buy', {
          ...loggerParams,
          slg: slg || '',
          uuid
        });
      }

      this.triggerEvent('buy', goodsItem);
    },

    handleUserAuthorizeChange(e) {
      this.triggerEvent('user-authorize-change', e.detail);
    },

    handleItemClick({ detail: index }) {
      const goodsItem = this.data._pureList[index];
      // 商品分组左侧点击埋点
      const { loggerParams = {} } = goodsItem;
      const { slg } = goodsItem.extra_info || {};
      const uuid = app.logger?.options?.user?.uuid || '';
      if (!isEmpty(loggerParams)) {
        this.ensureAppLogger('open_goods', {
          ...loggerParams,
          slg: slg || '',
          uuid
        });
      }

      const { url } = goodsItem;
      navigate.navigate({
        url,
      });
    },
    /**
     * 滚动
     * @param e
     */
    handleScroll(e) {
      this.triggerEvent('handleScroll', e.detail);
    },

    /**
     * 右侧 滚动
     */
    handleTouchStart() {
      this.triggerEvent('handleTouchStart');
    },

    fetchList(page = 1) {
      const {
        kdtId,
        appId,
        offlineId,
        groupItem: { alias, number },
        goodsListConfig: { showSubTitle },
      } = this.data;

      // number 为 0 返回空
      if (+number === 0) {
        this.setYZData({
          isEmpty: true,
        });
        return;
      }


      fetchGoodsByTagAlias({
        page,
        alias,
        json: 1,
        offlineId,
        kdt_id: kdtId,
        app_id: appId,
        pageSize: PAGE_SIZE,
        activityPriceIndependent: 1,
        uuid: app?.logger?.options?.user?.uuid || '',
      }).then((res) => {
        let list = this.data.list;
        const items = mapKeysCase.toSnakeCase(res.list || []);

        // 分页数据起始索引
        const startIndex = (+page - 1) * PAGE_SIZE;
        const itemsMaped = {};
        const pureList = {};

        items.forEach((item, itemIndex) => {
          // 分页商品的索引
          const baseIdx = startIndex + itemIndex;

          item._id = baseIdx;
          item.loggerParams = {
            goods_id: item.id,
            item_id: item.id,
            item_type: 'goods',
          };
          item.goodsId = item.id
          item = omit(item, [
            'id',
            'url',
            'buy_url',
            'image_id',
            'total_stock',
            'postage',
            'picture',
            'pre_sale_type',
            'height',
            'width',
          ]);
          item.url = Args.add('/pages/goods/detail/index', {
            alias: item.alias,
          });
          item.image_url = cdnImage(item.image_url, '!200x0.jpg');

          if (+item.activity_price < +item.price && !item.origin) item.origin = item.price;
          item.price = (item.activity_price || item.price + '').replace(/\.?0+$/, '');
          item.is_sold_out = +item.sold_status === 2;
          item.sub_title = showSubTitle ? item.sub_title : '';

          // 营销标签
          item.activityInfos = mapKeysCase.toCamelCase(item.activity_infos) || [];

          // 兼容营销标签处理
          item = this.computedUmpAmbience(item);

          // 返佣价格
          this.setSalesmanRebateInfo(item);
          // 导购员佣金价格处理
          this.setGuideCommissionInfo(item);

          itemsMaped[baseIdx] = this.pickItem(item);

          pureList[`_pureList[${baseIdx}]`] = item;
        });

        list = list.map(n => itemsMaped[n._id] || n);

        const pageGoodsNum = startIndex + PAGE_SIZE;
        const endIdx = startIndex + items.length;
        if (!items.length) {
          // 为空删除后面多余的元素
          list = list.filter((n) => n._id < startIndex);
        } else if (PAGE_SIZE != items.length) {
          // 不相等删除多余的元素
          list = list.filter((n) => n._id < endIdx || n._id >= pageGoodsNum);
        }

        this.setYZData({
          ...pureList,
          list,
          isEmpty: list.length === 0
        });

        // 设置group高度
        this.triggerEvent('set-height', {
          alias,
          number: list.length,
          // 有返佣价格每个item高度加16px
          // 导购员和分销员返佣价格共用一处展示位
          groupExtraHeight:
            list.filter((n) => n.show_rebate_price || n.show_guide_commission)
              .length * 16,
        });
      });
    },

    needCorrectPreList(item, index) {
      const { list = [] } = this.data;
      const preItem = list[index] || {};
      if (item.alias && preItem.alias && item.alias !== preItem.alias) {
        return true;
      }
      return false;
    },

    setSalesmanRebateInfo(item) {
      // 是否展示返佣价格
      let rebatePrice = +item.salesman_rebate_price;
      if (rebatePrice) {
        rebatePrice = parseFloat(rebatePrice / 100).toFixed(2);
        item.show_rebate_price = `预计返${rebatePrice}元`;
      }
      return item;
    },

    setGuideCommissionInfo(item) {
      const guideCommission = Number(item.guide_commission);
      if (guideCommission) {
        item.show_guide_commission = `预计赚${formatPrice(guideCommission)}元`;
      }
      return item;
    },

    pickItem(item) {
      return pick(
        item,
        '_id',
        'title',
        'price',
        'alias',
        'image_url',
        'is_sold_out',
        'sub_title',
        'show_rebate_price',
        'show_guide_commission',
        'origin',
        'tagsList'
      );
    },

    skeletonItem() {
      const {
        groupItem: { number },
      } = this.data;
      const list = [];

      for (let i = 0; i < number; i++) {
        list.push({ _id: i, active: true });
      }

      this.setYZData({
        list,
      });
    },

    loadItem({ detail: { idx } }) {

      if (!this._loadItemCheck) {
        createLoadItemCheck.call(this);
      }
      this._loadItemCheck(idx);
    },

    genItem(idx) {

      // 根据索引获取page
      const page = Math.floor(idx / PAGE_SIZE) + 1;
      const fetchedPages = this.data.fetchedPages;

      if (fetchedPages.find(n => +n === page)) return;
      fetchedPages.push(page);

      this.setYZData({
        fetchedPages,
      })

      this.fetchList(page);
    },

    computedTags(item) {
      item.tagsList = item.activityInfos.map(tag => {
        const { type, labelThemeType, activityAlias, activityId } = tag;

        tag.theme = labelThemeType;
        if (tag.theme === TAGS_THEME_MAP.other) {
          tag.color = TAGS_THEME_OTHER_COLOR;
        }

        // 独立商详
        if (type === JUMP_UMP_GOODS_DETAIL_MAP.SECKILL && activityAlias) {
          item.url = `/packages/goods/seckill/index?alias=${activityAlias}`;
        }

        if (type === JUMP_UMP_GOODS_DETAIL_MAP.HELPCUT && activityId) {
          item.url = Args.add('/packages/goods/help-cut/index', {
            alias: item.alias,
            activityId,
            type: 'helpcut',
          });
        }
        return tag;
      })
      return item;
    },

    computedUmpAmbience(item) {
      const { showUmpAmbience = false } = this.data.goodsListConfig;

      const { activityInfos = [] } = item;
      if (!activityInfos || !activityInfos.length) {
        return item;
      }

      // 有活动 整个列表才展示 标签。
      if (this.data.infoData && !this.data.infoData.showTags) {
        this.setData({
          'infoData.showTags': true,
        });
      }

      if (!showUmpAmbience) return this.computedTags(item);

      item.tagsList = activityInfos.filter(
        (item) => PRICE_ACTIVITY_LIST.indexOf(item.type) < 0
      );

      // 包邮特殊处理
      item.tagsList = this.computedPostageActivity(item.tagsList);
      // 价格优惠特殊处理
      item = this.computedPriceActivity(item);

      return item;
    },

    computedPostageActivity(list) {
      // postageFreeType 0 包邮 1 件 2 元
      const postageMap = {};
      let filterList = [];
      const preFilterList = []; // 不包邮的活动

      return list
        .map((item, index) => {
          if (
            item.type === ACTIVITY_MAP.MEET_REDUCTION ||
            item.type === ACTIVITY_MAP.POSTAGE_FREE
          ) {
            const { postageFreeType } = item;
            // 已经存在包邮了 直接过滤别的活动
            if (postageMap[0]) {
              filterList.push(index);
              return item;
            }

            // 是包邮活动 把不是之前不是包邮的过滤掉
            if (+postageFreeType === 0) {
              filterList = [...filterList, ...preFilterList];
            }

            // 包邮类型存在 && 不包邮 记录下不包邮的活动
            if (+postageFreeType > 0) {
              preFilterList.push(index);
            }
            // map 里不存在则写入
            if (!postageMap[postageFreeType]) {
              postageMap[postageFreeType] = {
                postageFreeThreshold: +item.postageFreeThreshold,
                index,
              };
            } else if (
              +item.postageFreeThreshold >=
              postageMap[postageFreeType].postageFreeThreshold
            ) {
              filterList.push(index);
            } else {
              filterList.push(postageMap[postageFreeType].index);
              postageMap[postageFreeType] = {
                postageFreeThreshold: +item.postageFreeThreshold,
                index,
              };
            }
          }
          return item;
        })
        .filter((item, index) => filterList.indexOf(index) < 0);
    },

    computedPriceActivity(item) {
      const { activityInfos = [] } = item;
      const priceAmbienceList = activityInfos.filter(
        (item) => PRICE_ACTIVITY_LIST.indexOf(item.type) > -1
      );

      if (!priceAmbienceList.length) return item;

      const { originPrice, priceLabel, activityPrice } = priceAmbienceList[0];

      item.origin = originPrice
        ? parseFloat(+originPrice / 100).toFixed(2)
        : item.origin;
      item.price = activityPrice
        ? parseFloat(+activityPrice / 100).toFixed(2)
        : item.price;

      // 兼容价格标签
      item.tagsList.unshift({
        label: priceLabel,
        theme: 'primary',
      });

      return item;
    }

  },
});
