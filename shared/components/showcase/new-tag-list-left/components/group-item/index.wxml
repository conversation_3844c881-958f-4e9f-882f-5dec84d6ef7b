  <view class="group-hook" style="height: {{ groupItem.height }}px;">
    <view class="group-title" style="padding: 0 {{ goodsListConfig.pageMargin }}px;">{{ groupItem.title }}</view>
    <view wx:if="{{ list.length > 0 }}">
      <block
        wx:for="{{ list }}"
        wx:for-index="idx"
        wx:for-item="goodsItem"
        wx:key="_id"
      >
        <goods-item
          show-title="{{ goodsListConfig.showTitle }}"
          show-sub-title="{{ goodsListConfig.showSubTitle }}"
          show-price="{{ goodsListConfig.showPrice }}"
          show-origin-price="{{ goodsListConfig.showOriginPrice }}"
          image-fill-mode="{{ goodsListConfig.imageFillMode }}"
          page-margin="{{ goodsListConfig.pageMargin }}"
          goods-margin="{{ goodsListConfig.goodsMargin }}"
          text-weight="{{ goodsListConfig.textWeight }}"
          border-circle="{{ goodsListConfig.borderCircle }}"
          show-buy-btn="{{ goodsListConfig.showBuyBtn }}"
          buy-btn-type="{{ goodsListConfig.buyBtnType }}"
          button-text="{{ goodsListConfig.buttonText }}"
          layout-item-style="{{ goodsListConfig.layoutItemStyle }}"
          goods-index="{{ idx }}"
          goods-item="{{ goodsItem }}"
          active="{{ goodsItem.active }}"
          info-data="{{ infoData }}"
          bind:buy="handleGoodsBuy"
          bind:user-authorize-change="handleUserAuthorizeChange"
          bind:item-click="handleItemClick"
          bind:load="loadItem">
        </goods-item>
      </block>

    </view>
    <view wx:elif="{{ list.length === 0 }}" class="group-empty">
      <block wx:if="{{ isEmpty }}">
        此类下暂时没有商品
      </block>

      <loadmore wx:else type="loading" />
    </view>
  </view>

