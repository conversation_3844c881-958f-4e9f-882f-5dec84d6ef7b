<view
  wx:if="{{ !goodsItem.hide }}"
  style="{{ layoutItemStyle }}"
  catch:tap="handleGoodsItemClick"
  class="goods-item-hook goods-item-hook-{{goodsItem._id}}"
>
  <view class="goods-item {{ goodsItem.show_rebate_price ? 'goods-item-height' : ''}}">
    <view 
      class="goods-item__img-wp goods-item__img-holder {{ goodsItem.is_sold_out ? 'goods-item__img-holder--sold-out' : ''}} {{ imageInfoClass }}"
      style="{{ goodsItemImgStyle }}"
      >
      <image wx:if="{{ inViewport && goodsItem.image_url }}" src="{{ goodsItem.image_url }}" mode="{{ imageFillMode }}" class="goods-item__img" />
    </view>

    <view class="goods-item-content-wp">
      <view class="goods-item__title-wp" wx:if="{{ showTitle }}">
        <view wx:if="{{ goodsItem.title }}" class="goods-item__title {{ titleInfoClass }}">{{ goodsItem.title }}</view>
        <view wx:else class="goods-item__title-holder" />
      </view>

      <view class="goods-item__sub-title-wp" wx:if="{{ showSubTitle && goodsItem.sub_title }}">
        <view class="goods-item__sub-title">{{ goodsItem.sub_title }}</view>
      </view>

      <goods-item-tags
        wx:if="{{ goodsItem.tagsList }}"
        goods-info="{{ goodsItem }}"
        info-data="{{ infoData }}"
        exhibition-tag="{{ exhibitionTag }}"
      />

      <view class="goods_info__wrap">
        <view wx:if="{{ showPrice }}">
            <theme-view wx:if="{{ goodsItem.price }}" custom-class="goods-item__price" color="general">
                <text class="goods-item__price-tag">￥</text>{{ goodsItem.price }}
                <text class="goods-item__price-origin" wx:if="{{ showOriginPrice && goodsItem.origin }}"> ¥ {{goodsItem.origin}}</text>
            </theme-view>

            <view wx:else class="goods-item__price-holder" />
        </view>

        <buy-button
            wx:if="{{ inViewport && goodsItem.alias && showBuyBtn }}"
            class="goods-item__cart-wp"
            type="{{ buyBtnType }}"
            redirect-type="1"
            alias="{{ goodsItem.alias }}"
            button-text="{{ buttonText }}"
            button-size="big"
            bind:user-authorize-change="handleUserAuthorizeChange"
            btn-wrap-class="goods__btn-wrap"
            bind:buy="handleGoodsBuy"
        />
        <view class="salesman__rebate_price" wx:if="{{goodsItem.show_rebate_price}}">
          {{ goodsItem.show_rebate_price}}
        </view>
      </view>
    </view>
  </view>
</view>
