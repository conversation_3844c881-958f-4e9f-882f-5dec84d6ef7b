.goods-item-hook {
  position: relative;
}

.goods-item {
  position: relative;
  display: flex;
  height: 100px;
  background: #fff;
}
/* 有返佣价格价设置高度 */
.goods-item-height {
  height: 116px;
}
.goods-item__img-wp {
  position: relative;
  width: 100px;
  height: 100px;
}

.goods-item__img-wp--circle {
  overflow: hidden;
  border-radius: 4px;
}

.goods-item__img-holder {
  background: #f8f8f8;
}

.goods-item__img {
  width: 100%;
  height: 100%;
}

.goods-item__img-holder--sold-out::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  background-image: url(https://img01.yzcdn.cn/weapp/wsc/RbrTwN.png);
  background-color: rgba(0, 0, 0, 0.3);
  background-position: center;
  background-size: 45px 45px;
  background-repeat: no-repeat;
}
.goods-item__img-holder--sold-out::after {
  background-color: var(--soldOutBgColor);
  background-image: var(--soldOutImageUrl);
}

.goods-item-content-wp {
  position: relative;
  padding-left: 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.goods-item__title {
  font-size: 14px;
  line-height: 20px;
  max-height: 40px;
  color: #333;
  margin-bottom: 5px;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.goods-item__sub-title {
  font-size: 12px;
  line-height: 14px;
  color: #999;
  height: 14px;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.goods-item__title--weight {
  font-weight: bold;
}

.goods-item__title-holder {
  width: 100%;
  height: 20px;
  background: #f8f8f8;
}

.goods_info__wrap {
  /* position: absolute; */
  /* bottom: 0; */
  position: relative;
  width: 100%;
  margin-top: auto;
  /* min-height: 24px; */
}

.goods-item__price {
  color: #f44;
  font-size: 16px;
  /* height: 20px; */
  /* line-height: 20px; */
  width: 100%;
  box-sizing: border-box;
  padding-right: 40px;
  line-height: 12px;
  padding-top: 5px;
}

.goods-item__price-tag {
  font-size: 12px;
  margin-right: 2px;
  display: inline-block;
}

.goods-item__price-origin {
  font-size: 12px;
  color: #969799;
  vertical-align: bottom;
  text-decoration: line-through;
  margin-left: 3px;
  display: inline-block;
  max-width: 110px;
  word-break: break-all;
  overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.goods-item__price-holder {
  width: 120px;
  height: 24px;
  background: #f8f8f8;
}

.goods-item__cart-wp {
  display: inline-block;
  position: absolute;
  font-size: 0;
  top: -4px;
  right: 15px;
}

.goods-item__cart-wp__disabled {
  color: #f6f6f6;
}

.salesman__rebate_price {
  color: rgb(255, 114, 13);
  margin: 2px 0px;
  overflow: hidden;
  font-size: 10px;
  line-height: 12px;
  height: 12px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
