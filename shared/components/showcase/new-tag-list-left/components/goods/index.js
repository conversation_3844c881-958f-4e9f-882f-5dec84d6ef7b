import appLogger<PERSON>ehavior from 'shared/components/showcase/behaviors/app-logger-behavior';
import getSystemInfo from 'shared/utils/browser/system-info';

const { windowHeight } = getSystemInfo();
const app = getApp()

Component({
  type: 'group-good',

  behaviors: [appLoggerBehavior],

  options: {
    addGlobalClass: true,
    pureDataPattern: /^_/
  },

  properties: {
    goodsItem: {
      type: Object,
      value: {},
    },

    showTitle: {
      type: Boolean,
      value: true
    },

    showSubTitle: {
      type: Boolean,
      value: true
    },

    showPrice: {
      type: Boolean,
      value: true
    },

    imageFillMode: {
      type: String,
      value: 'aspectFit'
    },

    pageMargin: {
      type: Number,
      value: 15
    },

    goodsMargin: {
      type: Number,
      value: 20
    },

    textWeight: {
      type: Boolean,
      value: true
    },

    borderCircle: {
      type: Boolean,
      value: false
    },

    showBuyBtn: {
      type: Boolean,
      value: true
    },

    buyBtnType: {
      type: Number,
      value: 1
    },
    buttonText: String,

    layoutItemStyle: String,

    goodsIndex: Number,

    active: {
      type: Boolean,
      value: false
    },
    showOriginPrice: {
      type: Boolean,
      value: true
    },
    infoData: {
      type: Object,
      value: {}
    }
  },

  data: {
    goodsPrice: null,
    imageInfoClass: '',
    titleInfoClass: '',
    inViewport: true,
    goodsItemImgStyle: '',
    exhibitionTag: false,
  },

  observers: {
    'active': function(rs) {
      if (rs) {
        this.initObserver();
      } else {
        this.ob && this.ob.disconnect();
      }
    }
  },

  attached() {
    this.initInfo();
    this.getShopConfigData()
  },

  ready() {
    this.data.active && this.initObserver();
  },

  methods: {
    initObserver() {
      const { _id } = this.data.goodsItem;
      this.ob = this.createIntersectionObserver();
      this.ob.relativeToViewport({
        bottom: -10
      }).observe(`.goods-item-hook-${_id}`, (rs) => {
        if (this.data.goodsItem.alias) {
          this.ob.disconnect();
          return;
        }
        if (rs.intersectionRatio > 0) {
          this.triggerEvent('load', { idx: this.data.goodsIndex });
        }
      });
    },

    initOver() {
      const ob = this.createIntersectionObserver();
      ob.relativeToViewport({
        top: windowHeight * 10,
        bottom: windowHeight * 10
      }).observe('.goods-item-hook', ({ intersectionRatio: ratio }) => {
          if (this.data.goodsItem.alias) {
            const inViewport = ratio > 0;
            
            this.setData({
              inViewport
            });
        }
      });
    },

    handleUserAuthorizeChange(e) {
      this.triggerEvent('user-authorize-change', e.detail);
    },

    initInfo() {
      this.setData({
        imageInfoClass: this.computeImageInfoClass(),
        titleInfoClass: this.computeTitleInfoClass()
      });
    },

    getShopConfigData() {
      app.getShopConfigData().then((shopConfig = {}) => {
        const {sold_out_goods_flag} = shopConfig

        this.setData({
          goodsItemImgStyle: `--soldOutBgColor: ${!!sold_out_goods_flag ? '' : 'rgba(0, 0, 0, 0.3)'} ; --soldOutImageUrl: url(${sold_out_goods_flag || 'https://img01.yzcdn.cn/weapp/wsc/RbrTwN.png'})`
        })
      })
    },

    computeImageInfoClass() {
      const { borderCircle = false } = this.data;
      const classArr = [];
      if (borderCircle) classArr.push('goods-item__img-wp--circle');

      return classArr;
    },

    computeTitleInfoClass() {
      const { textWeight = true } = this.data;

      return textWeight ? 'goods-item__title--weight' : '';
    },

    handleGoodsItemClick() {
      const {
        goodsItem: { _id = {} }
      } = this.data;

      this.triggerEvent('item-click', _id);
    },

    handleGoodsBuy() {
      const { goodsItem: { _id = {} } } = this.data;
      this.triggerEvent('buy', _id);
    }
  }
});
