import { node as request } from 'shared/utils/request';

const app = getApp();
const http = app.request;

export const fetchGoodsByTagAlias = (data) => {
  return request({
    path: '/wscshop/goods/goodsByTagAlias.json',
    data: { ...data, isShowPeriod: 1 },
  });
};

export const getGoodsList = (data) => {
  return request({
    path: '/wscshop/showcase/goodsList.json',
    data: { ...data, isShowPeriod: 1 },
  });
};

export function getGoodsId(data) {
  return http({
    path: '/wscshop/showcase/takeout/getGoodsId.json',
    method: 'POST',
    data: {
      ...data,
      shopType: 0,
    },
  });
}
