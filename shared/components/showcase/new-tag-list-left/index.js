import throttle from '@youzan/weapp-utils/lib/throttle';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';

import { EMPTY_HEIGHT, GOODS_HEIGHT, TITLE_HEIGHT } from './constants';

import capGoodsBehavior from '../../showcase/captain-components/goods/behavior';

function createScrollCheckLoadFunc() {
  let { scrollCheckLoadFunc } = this;

  if (scrollCheckLoadFunc) return;

  scrollCheckLoadFunc = throttle((ev) => {
    this.focusTabOnScroll(ev);
  }, 300);

  this.scrollCheckLoadFunc = scrollCheckLoadFunc;
}

Component({
  behaviors: [componentBehavior, loggerBehavior, capGoodsBehavior],

  options: {
    addGlobalClass: true,
  },

  data: {
    tags: [],
    /**
     * 商品配置数据
     */
    goodsListConfig: {},

    selectedGroup: '',
    selectedTag: '',
    contentScrollTop: 0,
    aliasHeightMap: {},
    canScrollView: true,
    infoData: {},
    extraHeightMap: {},
  },

  attached() {
    this.initComponent();
  },

  methods: {
    /**
     * 下拉
     */
    onPullDownRefresh() {
      if (this.isComponentHide) return;
      if (!this.canPullDownRefresh) {
        this.canPullDownRefresh = true;
        return;
      }

      this.setData(
        {
          selectedGroup: '',
          selectedTag: '',
          tags: [],
        },
        () => {
          this.initComponent();
        }
      );
    },

    initComponent() {
      const { componentData } = this.data;
      const tagListData = mapKeysCase.toCamelCase(componentData);
      const { tags = [], goodsListConfig } = tagListData;
      const infoData = this.computeInfoData(goodsListConfig);

      const {
        showTitle,
        showSubTitle,
        showPrice,
        showOriginPrice,
        showBuyButton: showBuyBtn,
        buyButtonType: buyBtnType,
        imageFillStyle,
        pageMargin,
        goodsMargin,
        borderRadiusType,
        buttonText,
        textStyleType,
        showUmpAmbience,
      } = goodsListConfig;

      const curGoodsListConfig = {
        showTitle,
        showSubTitle,
        showPrice,
        showOriginPrice,
        showBuyBtn,
        buyBtnType,
        buttonText,
        showUmpAmbience,
        pageMargin: +pageMargin,
        goodsMargin: +goodsMargin / 2,
        textWeight: +textStyleType === 2,
        borderCircle: +borderRadiusType === 2,
        imageFillMode: +imageFillStyle === 1 ? 'aspectFill' : 'aspectFit',
        layoutItemStyle: `padding: ${+goodsMargin / 2}px ${+pageMargin}px;`,
      };

      let height = 0;
      const itemHeight = +goodsMargin + GOODS_HEIGHT;
      const aliasHeightMap = {};

      tags.forEach((item) => {
        aliasHeightMap[item.alias] = [height];
        const _height =
          (item.number > 0 ? itemHeight * item.number : EMPTY_HEIGHT) +
          TITLE_HEIGHT;
        height += _height;
        aliasHeightMap[item.alias].push(height);
        item.height = _height;
      });

      this.aliasHeightMap = aliasHeightMap;
      this.itemHeight = itemHeight;

      this.setData({
        tags,
        selectedGroup: tags[0].alias,
        goodsListConfig: curGoodsListConfig,
        infoData,
      });
    },

    setItemHeight(e) {
      const { alias: _alias, number, groupExtraHeight } = e.detail;
      const { tags, extraHeightMap } = this.data;
      const { aliasHeightMap, itemHeight } = this;

      let height = 0;
      extraHeightMap[_alias] = groupExtraHeight;
      tags.forEach((item) => {
        if (item.alias === _alias) {
          item.number = number;
        }

        aliasHeightMap[item.alias] = [height];
        const _height =
          (item.number > 0 ? itemHeight * item.number + (extraHeightMap[item.alias] || 0) : EMPTY_HEIGHT) +
          TITLE_HEIGHT;

        height += _height;

        aliasHeightMap[item.alias].push(height);

        item.height = _height;
      });

      this.aliasHeightMap = aliasHeightMap;
      this.setData({
        tags,
        extraHeightMap,
      });
    },

    // 选择切换
    handleNavChange({ detail: { alias } }) {
      this.navChange = true;
      this.setData({
        selectedGroup: alias,
        selectedTag: alias,
        contentScrollTop: this.aliasHeightMap[alias][0],
      });
    },

    handleGroupScroll(ev) {
      const { scrollCheckLoadFunc } = this;
      if (!scrollCheckLoadFunc) {
        createScrollCheckLoadFunc.call(this);
      } else {
        scrollCheckLoadFunc(ev);
      }
    },

    focusTabOnScroll(ev) {
      const {
        detail: { scrollTop },
      } = ev;

      let alias = '';

      Object.keys(this.aliasHeightMap).forEach((item) => {
        const [start, end] = this.aliasHeightMap[item];
        if (scrollTop >= start && scrollTop < end) {
          alias = item;
        }
      });

      // 点击左侧某一分组，右侧内容滚动不应影响左侧分组选中
      if (!!this.navChange) {
        this.navChange = false;
        return;
      }

      if (this.data.selectedGroup === alias) return;

      this.setData({
        selectedGroup: alias,
      });
    },

    handleUserAuthorizeChange(e) {
      this.setData({
        canScrollView: !e.detail,
      });
    },

    handleGoodsBuy(e) {
      const { alias = '' } = e.detail || {};
      if (!alias) return;

      this.triggerEvent('buy', e.detail);
    },
  },
});
