<view class="cap-tag-list-left th_vw-buy">
  <view class="tag-list-left theme-tag-list-left">

    <view class="tag-list-left__nav">
      <tag-nav
        selected-group="{{ selectedGroup }}"
        nav-list="{{ tags }}"
        bind:handleScroll="handleNavScroll"
        bind:handleChange="handleNavChange"
      />
    </view>

    <view class="tag-list-left__group">
      <scroll-view
        class="group-container"
        scroll-y="{{ canScrollView }}"
        scroll-into-view="{{ 'GROUP' + selectedTag }}"
        bind:scroll="handleGroupScroll"
        bind:touchstart="handleTouchStart"
      >
        <view
          wx:for="{{ tags }}"
          wx:for-index="navIndex"
          wx:for-item="group"
          wx:key="alias"
          id="{{ 'GROUP' + group.alias }}"
        >
          <tag-group 
            bind:buy="handleGoodsBuy"
            bind:set-height="setItemHeight"
            bind:user-authorize-change="handleUserAuthorizeChange"
            group-index="{{ navIndex }}"
            group-item="{{ group }}"
            goods-list-config="{{ goodsListConfig }}"
            info-data="{{ infoData }}"
            offline-id="{{ offlineId }}"
            kdt-id="{{ kdtId }}"
            app-id="{{ appId }}"
          />
        </view>
      </scroll-view>
    </view>
  </view>
</view>
