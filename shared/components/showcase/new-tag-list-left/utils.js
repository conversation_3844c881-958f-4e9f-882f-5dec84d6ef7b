import constants from './constants';

const { GOODS_HEIGHT, TITLE_HEIGHT, EMPTY_CONTAINER } = constants;

export function setNavRange(item = {}, index, arr) {
  let start = 0;
  const { goodsMargin = 0 } = item;
  const itemHeight = +goodsMargin + GOODS_HEIGHT;
  const height = item.number ? item.number * itemHeight : EMPTY_CONTAINER;
  if (index) {
    start = arr[index - 1].range[1];
  }
  item.tagHeight = TITLE_HEIGHT + height;
  item.range = [start, start + TITLE_HEIGHT + height];
  return item;
}
