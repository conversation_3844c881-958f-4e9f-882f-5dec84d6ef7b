import args from '@youzan/weapp-utils/lib/args';
import { isNewIphone } from 'shared/utils/browser/device-type';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';

Component({
  behaviors: [componentBehavior, loggerBehavior],
  properties: {
    sourceParam: String,
    businessId: String,
    tabPage: Boolean,
  },
  externalClasses: ['contact-us-class'],
  data: {
    isNewIphone: isNewIphone()
  },

  methods: {
    handleContact({ detail }) {
      this.ensureAppLogger('click_content', {
        banner_id: this.getBannerId(),
      });

      if (detail.path) {
        wx.navigateTo({
          url: args.add(detail.path, detail.query)
        });
      }
    }
  }
});
