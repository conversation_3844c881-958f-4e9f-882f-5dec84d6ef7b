import Args from '@youzan/weapp-utils/lib/args';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import getRedirectData from '../utils/getRedirectData';

Component({
  behaviors: [componentBehavior, loggerBehavior],

  data: {
    links: []
  },

  attached() {
    this.setData({
      links: this.convertLinks()
    });
  },

  methods: {
    convertLinks() {
      const { componentData } = this.data;

      return (componentData.link_arr || []).filter(item => {
        const { link_type: type } = item;

        return ['goods', 'feature', 'link'].indexOf(type) >= 0;
      }).map((item, index) => {
        const {
          alias,
          link_type: linkType,
          link_title: linkTitle,
          link_url: linkUrl
        } = item;
        let url = '';

        const bannerId = this.getBannerId(index + 1);
        // 老数据可能在微页面分类时没有 alias 字段，需要从 link_url 中获取
        if (!alias && linkType === 'feature') {
          // Carmen接口 linkUrl 是https://h5.youzan.com/v2/feature/JqPmG7Rvdi?alias=xxx
          if (linkUrl.indexOf('alias=') > 0) {
            url = '/packages/home/<USER>/index?alias=' + linkUrl.replace(/.*\?alias=(.*)/, '$1');
          } else {
            // node 接口linkURL 是 https://h5.youzan.com/v2/feature/JqPmG7Rvdi
            url = '/packages/home/<USER>/index?alias=' + linkUrl.replace(/.*\/feature\/(.*)/, '$1');
          }
        } else {
          url = getRedirectData(linkType, item).url;
        }

        url = Args.add(url, { banner_id: bannerId });

        return {
          linkTitle,
          linkUrl: url
        };
      });
    }
  }
});
