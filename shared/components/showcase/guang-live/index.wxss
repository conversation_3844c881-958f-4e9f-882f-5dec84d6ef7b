.live,
.loading {
  display: flex;
  height: 94px;
  padding: 12px;
  box-sizing: border-box;
  background-color: #fff;
}

.live {
  position: relative;
}

.loading {
  align-items: center;
  justify-content: center;
}

.live:active {
  background-color: #e8e8e8;
}

.logo {
  width: 70px;
  height: 70px;
}

.body {
  padding-left: 12px;
}

.title {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  margin: 6px 0 22px;
}

.status {
  color: #7d7e80 !important;
  font-size: 12px !important;
  background-color: transparent !important;
  margin-left: -6px;
}

.heart-icon {
  position: absolute;
  right: 18px;
  bottom: 18px;
  width: 24px;
  height: 24px;
}
