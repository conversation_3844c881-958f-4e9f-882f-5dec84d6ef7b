import args from '@youzan/weapp-utils/lib/args';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import Toast from '@vant/weapp/dist/toast/toast';
import { getLiveStatus } from 'shared/components/guang/live-status/utils';
import componentBehavior from '../behaviors/component-behavior';
import { getLivePreview, getLiveRoomInfo } from './api';

const GUANG_APP_ID = 'wxa2e624400134d690';
const LIVE_ROOM_URL = '/packages/main/live-room/index';
const LIVE_DETAIL_URL = '/packages/live/detail/index';

// 打开直播间页面
function getPath(params) {
  const { id, state, kdtId, coverUri } = params;
  const { statusType } = getLiveStatus({ state });
  const path = statusType === 'unstarted' ? LIVE_DETAIL_URL : LIVE_ROOM_URL;

  return args.add(path, {
    id,
    coverUri,
    guangRelationKdtId: kdtId,
    from: 'wsc',
  }, true);
}

Component({
  behaviors: [componentBehavior],

  attached() {
    const { componentData } = this.data;
    const { roomId } = componentData || {};
    let promise;
    if (roomId) {
      promise = getLiveRoomInfo(roomId);
    } else {
      promise = getLivePreview(this.data.kdtId);
    }
    promise.then((response) => {
      if (!response) {
        this.setData({
          loaded: true,
          empty: true
        });
        return;
      }

      const coverUri = cdnImage(response.coverUri, '!210x0.jpg');
      this.setData({
        ...response,
        loaded: true,
        coverUri
      });
    });
  },

  methods: {
    onClick() {
      wx.navigateToMiniProgram({
        appId: GUANG_APP_ID,
        path: getPath(this.data),
        fail() {
          Toast('跳转失败');
        }
      });
    }
  }
});
