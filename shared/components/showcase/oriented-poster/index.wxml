<view
  wx:if="{{ images.length > 0 }}"
>
  <swiper
    indicator-dots="{{ images.length > 1 }}"
    autoplay="{{ true }}"
    indicator-active-color="#ff4444"
    style="height:{{ swipeHeight }}rpx"
    bind:change="handleChange"
  >
    <block
      wx:for="{{ images }}"
      wx:key="unique"
      wx:for-item="item"
    >
      <swiper-item
        style="position: absolute;"
        bind:tap="handleClick"
        data-index="{{ index }}"
      >
        <view class="image__swiper-container">
          <image
            src="{{ item.imageUrl }}"
            mode="aspectFit"
            lazy-load="true"
            style="display:block; width: 100%; height: {{ swipeHeight }}rpx;"
          />
          <view
            wx:if="{{ item.title }}"
            class="image__title zan-ellipsis"
          >{{ item.title }}</view>
        </view>
      </swiper-item>
    </block>
  </swiper>
</view>
