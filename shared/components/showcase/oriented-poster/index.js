import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';

import api from './api.js';

Component({
  behaviors: [componentBehavior, loggerBehavior],

  data: {
    images: [],
    swipeHeight: 0
  },

  attached() {
    const { kdtId = '', buyerId = '' } = this.data;

    if (!buyerId) {
      return;
    }

    api.getPosters({
      kdtId,
      buyerId
    }).then((posters = []) => {
      if (!posters.length) {
        return;
      }

      this.setPosters(mapKeysCase.toCamelCase(posters));
    });
  },

  methods: {
    setPosters(images = []) {
      if (!images.length) {
        return;
      }

      images.forEach(image => {
        image.imageUrl = cdnImage(image.imageUrl, '!730x0.jpg');
      });

      const firstImageHeight = images[0].imageHeight;
      const firstImageWidth = images[0].imageWidth;
      const swipeHeight = Math.ceil(750 * (firstImageHeight / firstImageWidth));

      this.setData({
        images,
        swipeHeight
      });
      this.sendViewLogger(0); // 曝光打点
    },

    getImage(images = [], index) {
      return images[index] || {};
    },

    sendViewLogger(index, type) {
      const image = this.getImage(this.data.images, index);
      const {
        teamId = '',
        planType = '',
        planId = '',
        linkUrl = ''
      } = image;
      let baseParams;

      if (type === 'click') {
        baseParams = {
          et: 'click',
          ei: 'click_poster',
          en: '点击海报'
        };
      } else {
        baseParams = {
          et: 'view',
          ei: 'view_poster',
          en: '海报曝光'
        };
      }

      this.ensureAppLogger('logger', {
        ...baseParams,
        params: {
          index: index + 1,
          teamId,
          planType,
          planId,
          poster_url: linkUrl,
          banner_id: this.getBannerId(),
          ...this.getComponentLoggerExtraParams(),
        }
      });
    },

    handleChange({ detail: { current } }) {
      this.sendViewLogger(current);
    },

    handleClick({ currentTarget }) {
      const index = currentTarget.dataset.index;
      const image = this.getImage(this.data.images, index);
      const { detail: link = {} } = image;
      const { linkType } = link;

      if (!linkType) {
        return;
      }

      this.triggerEvent('jumpToLink', link);

      this.sendViewLogger(index, 'click');
    }
  }
});
