## OrientedPoster 定向海报

### 使用指南

在 index.json 中引入组件。

```json
{
  "usingComponents": {
    "cap-goods": "path/to/@youzan/captain-weapp/dist/oriented-poster/index"
  }
}
```

### 代码演示

#### 基础用法

```html
<cap-oriented-poster
  images="{{ images }}"
  bind:change="handleChange"
  bind:click="handleClick"
/>
```

### API

| 参数       | 说明      | 类型       | 默认值       | 可选值       |
|-----------|-----------|-----------|-------------|-------------|
| images | 图片数组 | `Array`  | ``  |  |

### 单张图片格式



### Event

| 事件名 | 说明 | 参数 |
|-----------|-----------|-----------|
| change | 图片滑动后触发 | event  |
| click | 点击图片时触发 | event |
