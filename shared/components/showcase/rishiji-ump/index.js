import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { moment } from '@youzan/weapp-utils/lib/time';
import navigate from 'shared/utils/navigate';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { getDiscountGoodsInfo, getGrouponGoodsInfo } from './api';
import componentBehavior from '../behaviors/component-behavior';

Component({
  behaviors: [componentBehavior],

  properties: {
    pageRandomString: {
      type: String,
      value: '',
      observer() {
        // 没有经过adaptor 不触发倒计时更新
        const { componentData } = this.data;
        if (!componentData.type) return;

        this.setData(
          {
            isLoading: true
          },
          () => {
            this.refreshCountDown();
          }
        );
      }
    }
  },

  data: {
    itemData: {},
    isLoading: true
  },

  attached() {
    this.setData({
      isLoading: true
    });
    const { componentData } = this.data;

    const originData = componentData.umpData.map(item => {
      if (item.type) {
        return {
          activityId: item.activity_ids[0],
          type: item.type
        };
      }

      // 获取当前显示的限时折扣参数
      const discountList = item.activities.map(activity => {
        return {
          activityId: activity.activityId,
          goodsId: activity.goods[0].goods_id,
          endAt: activity.endAt
        };
      });
      return {
        list: discountList,
        type: item.type
      };
    });
    this.fetchData(originData);
  },

  methods: {
    onPullDownRefresh() {
      this.setData(
        {
          isLoading: true
        },
        () => {
          this.refreshCountDown();
        }
      );
    },

    fetchData(originData) {
      const promises = originData.map(item => {
        if (item.type) {
          return this.getGrouponGoodsInfo(item.activityId);
        }
        return this.getDiscountGoodsInfo(item.list);
      });
      Promise.all(promises)
        .then(result => {
          this.setData({
            isLoading: false,
            umpData: result
          });
        })
        .catch(() => {
          this.setData({
            isLoading: false,
            umpData: []
          });
        });
    },
    refreshCountDown() {
      const { umpData } = this.data;

      if (!umpData) {
        return;
      }
      const data = {
        isLoading: false
      };
      umpData.forEach((item, index) => {
        if (item.type === 0) {
          const countdown = item.endAt * 1000 - Date.now();
          data[`umpData[${index}].countdown`] = countdown;
        }
      });

      this.setData(data);
    },
    handleGrouponLookMore() {
      const param = {
        hide_goods_sold: 0
      };
      navigate.navigate({
        url: '/packages/shop/goods/group/index?pageType=groupon&component=' + JSON.stringify(param)
      });
    },
    handleGoodsTap(e) {
      const { owlType = 0, alias, owlAlias } = e.detail;
      const urlMap = [
        '/pages/goods/detail/index',
        '/packages/paidcontent/column/index',
        '/packages/paidcontent/content/index'
      ];
      if (owlType > 0) {
        // 知识付费商品
        navigate.navigate({
          url: `${urlMap[owlType]}?alias=${owlAlias}`
        });
        return;
      }
      navigate.navigate({
        url: `${urlMap[0]}?alias=${alias}`
      });
    },
    getDiscountGoodsInfo(list) {
      return getDiscountGoodsInfo(list)
        .then(res => {
          if (!res) {
            return;
          }
          const camelRes = mapKeysCase.toCamelCase(res);
          camelRes.nextStartAtStr = moment(camelRes.nextStartAt, 'HH:mm');
          const leftMilliSecs = camelRes.endAt * 1000 - Date.now();
          camelRes.countdown = leftMilliSecs;
          camelRes.imageUrl = cdnImage(camelRes.imageUrl, '!220x220.jpg');
          camelRes.type = 0;
          return camelRes;
        })
        .catch(err => {
          console.log(err);
          return {};
        });
    },
    getGrouponGoodsInfo(activityId) {
      return getGrouponGoodsInfo(activityId)
        .then(res => {
          const camelRes = mapKeysCase.toCamelCase(res);
          camelRes.type = 1;
          camelRes.imageUrl = cdnImage(camelRes.imageUrl, '!220x220.jpg');
          return camelRes;
        })
        .catch(err => {
          console.log(err);
          return {};
        });
    }
  }
});
