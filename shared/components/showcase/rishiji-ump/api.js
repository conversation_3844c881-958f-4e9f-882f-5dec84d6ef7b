import { node as request } from 'shared/utils/request';

export const getDiscountGoodsInfo = list => {
  return request({
    path: '/wscump/rishiji-ump/getLimitDiscountGoodsInfo.json',
    origin: 'h5',
    data: {
      list
    },
    method: 'POST'
  });
};

export const getGrouponGoodsInfo = activityId => {
  return request({
    path: '/wscump/rishiji-ump/getGrouponGoodsInfo.json',
    origin: 'h5',
    data: {
      activityId
    }
  });
};
