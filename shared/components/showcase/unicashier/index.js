import navigate from 'shared/utils/navigate';
import Args from '@youzan/weapp-utils/lib/args';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import * as api from './api';

Component({
  options: {
    addGlobalClass: true
  },

  behaviors: [componentBehavior, loggerBehavior],

  data: {
    activityDesc: ''
  },

  attached() {
    const { qrcodeId } = this.data.componentData;

    api.getActivityDesc({
      qrcode_id: qrcodeId
    }).then(res => {
      this.setData({
        activityDesc: res.desc || ''
      });
    });
  },

  methods: {
    handleBuyButtonClick() {
      const { qrcodeId } = this.data.componentData;
      const bannerId = this.getBannerId();
      navigate.navigate({
        url: Args.add('/packages/pay/unicashier/index', { qrcode_id: qrcodeId, banner_id: bannerId })
      });
    }
  }
});
