<cap-audio
  index="{{ index }}"
  audio-style="{{ curComponentData.style }}"
  title="{{ curComponentData.title }}"
  loading-icon="{{ curComponentData.loadingIcon }}"
  player-img="{{ curComponentData.playerImg }}"
  logo="{{ logo }}"
  bubble="{{ curComponentData.bubble }}"
  src="{{ curComponentData.src }}"
  loop="{{ curComponentData.loop }}"
  reload="{{ curComponentData.reload }}"
  duration="{{ curComponentData.duration }}"
  formated-duration="{{ curComponentData.formatedDuration }}"
  can-play="{{ curComponentData.canPlay }}"
  is-loading="{{ curComponentData.isLoading }}"
  status="{{ curComponentData.status }}"
  bind:btn-click="handleTrigger"
  bind:slider-drag="handleUpdateProgress"
/>
