import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState } from '@youzan/vanx';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import omit from '@youzan/weapp-utils/lib/omit';
import audioApi from 'common-api/audio/index';
import audioEventBus from 'shared/components/showcase/captain-components/audio/audio-event';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';

const STATUS = {
  STOP: 0,
  PLAY: 1,
  PAUSE: 2
};

VanxComponent({
  behaviors: [componentBehavior, loggerBehavior],

  mapData: {
    ...mapState('/', {
      logo(state) {
        const { avatar } = this.data.componentData || {};
        return cdnImage(avatar || state.shop.logo, '!80x80.jpg');
      },
    }),
  },

  properties: {
    componentData: {
      type: Object,
      value: {},
      observer(newVal) {
        // ?? ob obj
        this.observerComponentData(newVal);
      }
    }
  },

  data: {
    curComponentData: {}
  },

  attached() {
    this.initComponent();
  },

  ready() {
    audioEventBus.on(`pause-${this.data.index}`, this.handlePause, this);
    audioEventBus.on(`stop-${this.data.index}`, this.handleStop, this);
    audioEventBus.on('play', this.handlePlay, this);
  },

  methods: {
    initComponent() {
      const {
        audio,
        bubble,
        style,
        title,
        loop,
        reload,
        type,
      } = this.data.componentData || {};

      const img = bubble === 'left' ? 'player' : 'green_player';
      const loadingIcon = 'https://b.yzcdn.cn/v2/image/wap/common/loading.gif';
      const playerImg = `https://b.yzcdn.cn/v2/image/wap/audio/${img}.gif`;

      this.setData({
        curComponentData: {
          style: +style,
          title,
          loadingIcon,
          playerImg,
          bubble,
          percentage: 0,
          loop: +loop === 1,
          reload: +reload === 1,
          status: STATUS.STOP,
          isLoaded: false,
          isLoading: false,
          formatedCurrentTime: '00:00',
          formatedDuration: '00:00',
          canPlay: false,
          type,
        }
      });

      audioApi.getAudioInfo(audio).then(({ url }) => {
        this.setData({
          'curComponentData.src': url
        });

        this.triggerEvent('src-change', {
          index: this.data.index,
          src: url
        });
      });
    },

    observerComponentData(newVal) {
      const newComponentData = omit(newVal, ['loop', 'reload', 'style']);
      this.setData({
        curComponentData: Object.assign({}, this.data.curComponentData, newComponentData)
      });
    },

    handleTrigger(e) {
      this.triggerEvent('btn-click', e.detail);
      this.sendStartLogger();
    },

    handleUpdateProgress(e) {
      this.triggerEvent('slider-drag', e.detail);
    },

    handlePause(e) {
      const { currentTime } = e;
      this.sendStopLogger(currentTime);
    },
    handleStop() {
      this.sendStopLogger();
    },
    sendStartLogger() {
      if (this.startLogger) {
        return;
      }
      this.startLogger = true;
      this.ensureAppLogger('logger', {
        et: 'click',
        ei: 'play',
        en: '开始播放',
        params: {
          banner_id: this.getBannerId(),
          ...this.getComponentLoggerExtraParams(),
        }
      });
    },

    sendStopLogger(currentTime) {
      if (this.hasSendStopLogger) {
        return;
      }

      this.hasSendStopLogger = true;
      this.ensureAppLogger('logger', {
        et: 'custom',
        ei: 'play_time',
        en: '播放时长',
        params: {
          time: Math.ceil(currentTime || this.data.duration),
          banner_id: this.getBannerId(),
          ...this.getComponentLoggerExtraParams(),
        }
      });
    },
  }
});
