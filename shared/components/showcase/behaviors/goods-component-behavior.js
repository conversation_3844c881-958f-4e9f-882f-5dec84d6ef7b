import { LAYOUT_MAP } from '../captain-components/goods-layout/constants';

const DEFAULT_PAGE_SIZE = 10;
// 最初是12，现在手机屏幕变长了，一行三个时12个可能无法触发下一页逻辑了
const THREE_PAGE_SIZE = 15;

const DEFAULT_SKELETON_NUMBER = 2;
const THREE_SKELETON_NUMBER = 3;
const ONE_SKELETON_NUMBER = 1;

export default Behavior({
  methods: {
    getPageSize(layout) {
      if (+layout === LAYOUT_MAP.THREE || +layout === LAYOUT_MAP.HYBRID) {
        // 一行三个 或者 一大两小 每页加载12个
        return THREE_PAGE_SIZE;
      }

      return DEFAULT_PAGE_SIZE;
    },

    checkIsSwipeLayout(layout) {
      return +layout === LAYOUT_MAP.SWIPE;
    },

    getSkeletonNumber(layout) {
      if (
        +layout === LAYOUT_MAP.THREE ||
        +layout === LAYOUT_MAP.HYBRID ||
        +layout === LAYOUT_MAP.SWIPE
      ) {
        // 一行三个 或者 一大两小 或者 横向滑动 骨架屏展示3个
        return THREE_SKELETON_NUMBER;
      }

      if (+layout === LAYOUT_MAP.BIG) return ONE_SKELETON_NUMBER;

      return DEFAULT_SKELETON_NUMBER;
    }
  }
});
