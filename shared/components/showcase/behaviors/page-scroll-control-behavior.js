import Event from '@youzan/weapp-utils/lib/event';
import getUniquePageKey from 'shared/utils/unique';

export default Behavior({
  methods: {
    setPageScrollControlSubscribe(fn, ctx) {
      const key = getUniquePageKey();
      Event.on('onPageScroll' + key, fn, ctx);
      return key;
    },

    delPageScrollControlSubscribe(fn, ctx, key = getUniquePageKey()) {
      Event.off('onPageScroll' + key, fn, ctx);
    }
  }
});
