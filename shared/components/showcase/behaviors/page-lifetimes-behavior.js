import Event from '@youzan/weapp-utils/lib/event';
import getUniquePageKey from 'shared/utils/unique';

const _f = () => {};

export default Behavior({
  properties: {
    pageLifetimes: {
      type: Array,
      value: []
    }
  },

  pageLifetimes: {
    show() {
      this.isComponentHide = false;
    },

    hide() {
      this.isComponentHide = true;
    }
  },

  attached() {
    this.pageKey = getUniquePageKey();
    this.data.pageLifetimes.forEach(item => {
      Event.on(item + this.pageKey, this[item] || _f, this);
    });
  },

  detached() {
    this.data.pageLifetimes.forEach(item => {
      Event.off(item + this.pageKey, this[item] || _f, this);
    });
  }
});
