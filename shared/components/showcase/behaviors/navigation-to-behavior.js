import { SHELF_LINK_TYPE_ENUM, SHELF_LINK_TYPE_TO_MODE } from '../utils/config';
import jumpToLink from '../utils/jumpToLink';
import promisify from 'utils/promisify';

const app = getApp();

/**
 * 支持24小时货架跳链的 behavior
 */
export default Behavior({
  methods: {
    navigateIncludeShelf(linkItem, extra = {}) {
      const { link_type: linkType, link_url: linkUrl } = linkItem;

      // 到店自提和外卖到家需要绑定手机号，而且只有小程序链接，不需要进行转换
      if (
        [SHELF_LINK_TYPE_ENUM.SELF, SHELF_LINK_TYPE_ENUM.TAKEOUT].indexOf(
          linkType
        ) > -1
      ) {
        app.globalData.shelfParams = {
          mode: SHELF_LINK_TYPE_TO_MODE[linkType],
        };
        this.navigateToShelfOrderPage(linkUrl);
      } else if (
        [SHELF_LINK_TYPE_ENUM.SCAN_GO, SHELF_LINK_TYPE_ENUM.FREE_GO].indexOf(
          linkType
        ) > -1
      ) {
        // 扫码点单 / 自助结账
        wx.navigateTo({ url: `/${linkUrl}` });
      } else {
        // 其他的直接跳转
        this.logTab(linkType, extra);
        jumpToLink(linkType, linkItem, extra);
      }
    },

    // 替换老的货架点单页路径，以提升重定向速度
    replaceOldShelfPath(url) {
      return url.replace('/retail/goods-shelf', '/retail-shelf/shelf');
    },

    navigateToShelfOrderPage(url) {
      promisify(wx.getSetting)().then((res) => {
        const isAuthLocation = res.authSetting['scope.userLocation'];

        if (isAuthLocation === undefined || isAuthLocation === true) {
          // 第一次请求授权 或者 已经授权过
          wx.getLocation({
            type: 'gcj02',
            fail: () => {
              // 引导授权
              this.triggerEvent('showAuthDialog');
            },
            success: () => {
              // const data =
              //   wx.getStorageSync('ExtraShopArgs') ||
              //   {};
              wx.navigateTo({ url: `/${url}` });
            },
          });
        } else if (isAuthLocation === false) {
          // 已经拒绝过授权，再次进行引导授权
          // this.triggerEvent('showAuthDialog');
          // 已经拒绝过授权，直接让进入点单页
          // const data =
          //   wx.getStorageSync('ExtraShopArgs') || {};
          wx.navigateTo({ url: `/${url}` });
        }
      });
    },

    logTab(type, extra) {
      const tabList = ['shopnote', 'usercenter', 'cart', 'homepage'];
      if (tabList.some((tabItem) => tabItem === type)) {
        const loggerMsg = {
          et: 'click',
          ei: 'click_content',
          en: '组件点击',
          params: { ...extra },
        };
        app.logger.log(loggerMsg);
      }
    },
  },
});
