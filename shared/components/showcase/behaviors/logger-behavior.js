import {
  getLoggerExtraParams,
  getLoggerType,
  loggerRandomKey,
} from 'shared/utils/log-params';
import spm from 'shared/utils/spm';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import appLoggerBehavior from './app-logger-behavior';

export default Behavior({
  behaviors: [appLoggerBehavior],

  methods: {
    getComponentLoggerExtraParams(cd = {}) {
      if (isEmpty(cd)) {
        cd = this.data.componentData;
      }
      return getLoggerExtraParams(cd);
    },

    getComponentIndex() {
      const { componentData } = this.data;
      const { componentIndex = 0 } = componentData;
      return componentIndex;
    },

    getComponentLoggerType(type = '') {
      if (!type) {
        type = this.data.componentData.type;
      }
      return getLoggerType(type);
    },

    getLoggerSpm() {
      return spm.getPageSpmTypeId();
    },

    getPageRandomNumber() {
      return loggerRandomKey;
    },

    getBannerId(index = 0) {
      return `${this.getLoggerSpm()}~${this.getComponentLoggerType()}.${
        this.getComponentIndex() + 1
      }~${index}~${this.getPageRandomNumber()}`;
    },
    getCloudLoggerInfo(data) {
      const pages = getCurrentPages();
      const { loggerParams, top, left, title } = data;
      const { components_style_is_hotarea: isHotArea, item_type: itemType } =
        loggerParams || {};
      const extraParams = isHotArea
        ? {
            hotarea_top: top,
            hotarea_left: left,
          }
        : {};
      itemType === 'image_ad' && (extraParams.image_title = title);

      return {
        page_url: pages[pages.length - 1].route,
        img_url: data.imageUrl,
        ...extraParams,
      };
    },
  },
});
