import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import Args from '@youzan/weapp-utils/lib/args';
import formatMoney from '@youzan/utils/money/format';
import navigate from 'shared/utils/navigate';
import * as api from './api';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { HTTP_URL_REX, NUMBER_TYPE_ALL } from './constants';
import goodsComponentBehavior from '../behaviors/goods-component-behavior';
import { LAYOUT_MAP } from '../captain-components/goods-layout/constants';

Component({
  behaviors: [componentBehavior, loggerBehavior, goodsComponentBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
      observer(newVal) {
        // ?? ob obj
        this.onPullDownRefresh(newVal);
      }
    },

    redirectType: {
      type: Number,
      value: 3
    },

    openSwipePagination: {
      type: Boolean,
      value: true
    },

    needLoggerParams: {
      type: Boolean,
      value: false
    },

    skeletonNumber: {
      type: Number,
      value: 0
    }
  },

  data: {
    isShowMore: false,
    goodsData: {},
    infoData: {},
    curGoodsList: [],
    loading: false,
    page: 1,
    isPullDownRefresh: false,
    curSkeletonNumber: 0
  },

  attached() {
    this.setData(this.getComponentData());
  },

  methods: {
    /**
     * 初始化组件
     */
    getComponentData() {
      const { componentData } = this.data;
      const goodsData = mapKeysCase.toCamelCase(componentData);
      const infoData = this.computeInfoData(goodsData);

      return {
        goodsData,
        isShowMore: false,
        loading: false,
        page: 1,
        infoData,
        showPriceLabel: true
      };
    },
    computeInfoData(data) {
      const isThree = this.computeIsSwipeOrThreeLayout(data.size);
      const priceStyle = isThree ? 'color: #323233;height: 40px;' : 'color: #323233';
      return {
        showTitle: +data.title === 1,
        showSubTitle: +data.showSubTitle === 1,
        showPrice: +data.price === 1,
        showBuyButton: false,
        textAlignType: data.textAlignType,
        textStyleType: data.textStyleType,
        priceStyle,
        priceInfoStyle: isThree ? 'display: block' : '',
        showOriginPrice: +data.showOriginPrice === 1
      };
    },
    /**
     * 是否为横向滑动布局
     */
    computeIsSwipeOrThreeLayout(size) {
      return LAYOUT_MAP.SWIPE === +size || LAYOUT_MAP.THREE === +size;
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
      this.setData(
        {
          isPullDownRefresh: true
        },
        () => {
          this.setObserveComponentData();
        }
      );
    },

    setObserveComponentData() {
      const { isPullDownRefresh } = this.data;
      if (isPullDownRefresh) {
        this.setData(
          {
            curGoodsList: [],
            ...this.getComponentData()
          },
          this.handleLoadMore
        );
      }
    },

    handleGoodsItemClick(e) {
      const { loggerParams = null, url = '', alias } = e.detail;
      let goodsUrl = url;

      // https:// http:// 替换成 小程序商品链接
      if (HTTP_URL_REX.test(url) || !loggerParams) {
        goodsUrl = `/pages/goods/detail/index?alias=${alias}`;
      }

      navigate.navigate({
        url: goodsUrl
      });
    },

    setGoodsItemLoggerParams(item, index) {
      const bannerId = this.getBannerId(index + 1);
      const loggerParams = {
        goods_id: item.goodsId,
        item_id: item.goodsId,
        item_type: 'member_goods',
        banner_id: bannerId,
        ...this.getComponentLoggerExtraParams()
      };
      item.loggerParams = loggerParams;
      item.url = Args.add('/pages/goods/detail/index', {
        alias: item.goodsAlias,
        banner_id: bannerId,
        ...this.getComponentLoggerExtraParams()
      });
      return item;
    },

    fetchGoodsList() {
      const { loading, componentData = {}, goodsData, offlineId } = this.data;

      if (loading) return;

      const { goodsFrom, goods = [], goodsNumType, goodsNum } = mapKeysCase.toCamelCase(
        componentData
      );
      const goodsNumber = goodsNumType === NUMBER_TYPE_ALL ? 30 : goodsNum;
      const goodsIds = goods.filter((_) => _ && _.goodsId).map((_) => _.goodsId);
      const fetchGoodsNumber = +goodsFrom === 0 ? goodsIds.length : goodsNumber;

      const { layout } = goodsData;

      const skeletonDefaultNumber = this.getSkeletonNumber(layout);
      const curSkeletonNumber =
        fetchGoodsNumber > skeletonDefaultNumber ? skeletonDefaultNumber : fetchGoodsNumber;

      // 如果goods_from
      if (+goodsFrom === 0 && !goodsIds) {
        this.handleLoadEnd();
        return;
      }

      this.setData({
        curSkeletonNumber,
        loading: true
      });

      return api
        .getGoodsList({
          pageSize: goodsNumber,
          goodsIds: goodsIds.join(','),
          goodsFrom,
          offlineId
        })
        .then((res) => {
          if (res && res.length) {
            const loggerList = [];
            const list = res.map((_, idx) => {
              const { goodsId: id, priceDTO, goodsItemDTO } = _;
              const loggerGoodsItem = this.setGoodsItemLoggerParams(goodsItemDTO, idx);
              const {
                goodsType,
                subTitle,
                goodsTitle,
                goodsPic,
                goodsId,
                goodsAlias,
                url,
                // 商品原价  单位为分
                minOriginPrice,
                // 商品划线价  单位为元
                strikethroughPrice,
                loggerParams
              } = loggerGoodsItem;
              loggerList.push(loggerParams);
              const { minPrice } = priceDTO;
              return {
                goodsType,
                id,
                price: (minPrice / 100).toFixed(2),
                // 新增商品原价, 如果有划线价则优先展示划线价(元), 否则展示商品原价(分)
                originPrice: strikethroughPrice || formatMoney(minOriginPrice),
                alias: goodsAlias,
                goodsId,
                imageUrl: goodsPic,
                title: goodsTitle,
                subTitle,
                url,
                loggerParams
              };
            });
            this.ensureAppLogger('view', loggerList);

            this.handleLoadEnd({
              curGoodsList: list,
              isPullDownRefresh: false
            });
          }
        })
        .catch(() => {
          // 出错之后重置状态
          setTimeout(() => {
            this.handleLoadEnd();
          }, 100);
        });
    },

    handleLoadEnd(params = {}) {
      return new Promise((resolve) => {
        this.setData(
          {
            ...params,
            curSkeletonNumber: 0,
            loading: false
          },
          () => {
            resolve();
          }
        );
      });
    },

    handleLoadMore() {
      const { loading } = this.data;

      if (loading) return;
      console.log('load');
      this.fetchGoodsList();
    }
  }
});
