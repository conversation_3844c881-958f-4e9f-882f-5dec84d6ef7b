import {
  LAYOUT_TYPE
} from '../../../captain-components/goods-layout/constants';

Component({
  properties: {
    goodsInfo: {
      type: Object,
      value: {}
    },
    layout: Number,
    showcaseClass: String
  },

  options: {
    styleIsolation: 'apply-shared'
  },

  externalClasses: ['custom-class'],

  attached() {
    const layout = +this.data.layout;
    this.setData({
      layoutClass: LAYOUT_TYPE[layout]
    });
  }
});