<cap-list class="member-goods-wrapper" loading="{{ loading }}" finished="{{ !needLoadMore }}" disabled="{{ isSwipeLayout }}" auto-check bind:load="handleLoadMore">
  <cap-goods-layout id="member-goods" showcase-class="member-goods" class="member-goods" generic:goods-item-price="goods-item-price" generic:goods-item-tags="price-label" app-id="{{ appId }}" list="{{ curGoodsList }}" image-fill-style="{{ goodsData.imageFillStyle }}" layout="{{ goodsData.size }}" size-type="{{ goodsData.sizeType }}" show-price-label="{{ showPriceLabel }}" extra-data="{{ extraData }}" redirect-type="{{ redirectType }}" info-data="{{ infoData }}" is-show-more="{{ false }}" page-margin="{{ goodsData.pageMargin }}" goods-margin="{{ goodsData.goodsMargin }}" border-radius-type="{{ goodsData.borderRadiusType }}" open-swipe-pagination="{{ false }}" bind:item-click="handleGoodsItemClick" bind:buy="handleGoodsItemClick" bind:load-more="handleLoadMore" />
</cap-list>