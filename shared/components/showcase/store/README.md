## Store 进入店铺

### 使用指南

在 index.json 中引入组件。

```json
{
  "usingComponents": {
    "cap-goods": "path/to/@youzan/captain-weapp/dist/store/index"
  }
}
```

### 代码演示
#### 基础用法
进入店铺组件展示
```html
<cap-store
  name="起码运动馆"
></cap-store>
```


#### 指定跳转链接
指定链接 进入店铺组件展示
```html
<cap-store
  name="起码运动馆"
  url="https://www.youzan.com"
></cap-store>
```


#### 自定义icon
自定义icon
```html
<cap-store
  icon="location"
  name="起码运动馆"
></cap-store>
```

#### 自定义行动区域文案
自定义icon
```html
<cap-store
  icon="location"
  name="起码运动馆"
  action-text="去逛逛"
></cap-store>
```


#### 更多用法
自定义icon
```html
<cap-store
  name="起码运动馆起码运动馆起码运动馆"
  action-text="去逛逛"
  tag="官方"
  label="宇宙中心分店"
></cap-store>
```

### API

| 参数       | 说明      | 类型       | 默认值       | 必须      |
|-----------|-----------|-----------|-------------|-------------|
| url | 点击后跳转链接 | string  | javascript:; |  否 |
| icon | 设置店铺名前的icon，目前只支持zanUI中提供的icon | string  | shop |  否 |
| name | 显示的店铺名 | string  | - |  否   |
| tag | 在店铺名后显示的标签文字 | string  | - |  否 |
| actionText | 右侧行动区域文案 | string | - | 否 |
| label | 描述副标题 | string | - | 否 |

