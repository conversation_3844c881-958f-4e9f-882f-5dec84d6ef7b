import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState } from '@youzan/vanx';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';

VanxComponent({
  behaviors: [componentBehavior, loggerBehavior],

  mapData: {
    ...mapState('/', {
      teamName: state => state.shop.shop_name,
    }),
  },

  data: {
    itemData: {}
  },

  attached() {
    const { componentData } = this.properties;
    const itemData = {
      ...mapKeysCase.toCamelCase(componentData),
    };

    this.setData({
      itemData
    });
  },

  methods: {
    linkStore() {
      var pages = getCurrentPages();
      var route = pages[pages.length - 1].route;
      // this.route
      if (route === 'pages/home/<USER>/index' || route === 'packages/home/<USER>/index') {
        wx.showToast({
          title: '您已经在店铺首页了',
          icon: 'none'
        });
        return;
      }

      const bannerId = this.getBannerId();
      this.triggerEvent('jumpToLink', {
        type: 'homepage',
        item: {},
        extra: {
          banner_id: bannerId
        }
      });
    }
  }
});
