<view class="c-store" catch:tap="linkStore">
  <view class="c-store__main">
    <van-icon name="shop-o" size="18px" custom-class="c-store__icon" />
    <text class="zan-ellipsis">{{ teamName }}</text>
    <van-tag wx:if="{{ itemData.tag }}" type="danger" custom-class="c-store__tag">
      {{ itemData.tag }}
    </van-tag>
    <view class="c-store__action-text">
      <text wx:if="{{ itemData.actionText }}">{{ itemData.actionText }}</text>
      <van-icon name="arrow" custom-class="c-store__arrow" />
    </view>
  </view>
  <view wx:if="{{ itemData.label }}" class="c-store__sub">{{ itemData.label }}</view>
</view>