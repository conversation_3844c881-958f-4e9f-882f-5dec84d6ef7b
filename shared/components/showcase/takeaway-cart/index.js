import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    showTakeoutCart: {
      type: Boolean
    },
    takeoutCartData: {
      type: Array
    }
  },

  externalClasses: ['iphonex-class'],

  methods: {
    onTakeoutCartMaskClick() {
      this.triggerEvent('toggle', false);
    },
    onClearCart() {
      this.triggerEvent('clear');
    },
    handleGoodsNumChange(e) {
      const { value: num } = e.detail;
      const { alias, skuId } = e.currentTarget.dataset;
      this.triggerEvent('change', {
        num,
        alias,
        skuId
      });
    }
  }
});
