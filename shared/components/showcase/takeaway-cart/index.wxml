<!-- 弹层 -->
<view class="zan-popup zan-popup--bottom {{ showTakeoutCart ? 'zan-popup--show' : '' }}">
  <view class="zan-popup__mask" bindtap="onTakeoutCartMaskClick" />
  <view class="takeoutcart zan-popup__container iphonex-class">
    <view class="takeoutcart__header">
      <view class="takeoutcart__title">购物车</view>
      <view class="takeoutcart__clear-wrapper" bindtap="onClearCart">
        <view class="takeoutcart__clear-icon">
        </view>
        <view class="takeoutcart__clear-text">清空</view>
      </view>
    </view>
    <view class="takoutcart__body">
      <block
        wx:for="{{ takeoutCartData }}"
        wx:key="index"
      >
        <template
          is="cart-row"
          wx:if="{{ item.num }}"
          data="{{ goods: item }}"
        >
        </template>
      </block>
    </view>
  </view>
</view>

<template name="cart-row">
  <view class="cartrow {{ goods.skuName.length > 0 ? 'cartrow_has-sku' : '' }}">
    <view class="cartrow__goods-title">
      {{ goods.title }}
    </view>
    <view wx:if="{{ goods.skuName }}" class="cartrow__sku-title">{{ goods.skuName }}</view>
    <view class="cartrow__goods-price">￥{{ utils.cent2yuan(goods.price) }}</view>
    <input-number
      class="cart-input"
      value="{{ goods.num }}"
      min="0"
      max="{{ (goods.activity && goods.activity.quota) ? goods.activity.quota : goods.stockNum }}"
      data-alias="{{ goods.alias }}"
      data-sku-id="{{ goods.skuId }}"
      bind:change="handleGoodsNumChange"
    >
    </input-number>
  </view>
</template>

<wxs src="./index.wxs" module="utils" />
