<async-video
  app-id="{{ appId }}"
  kdt-id="{{ kdtId }}"
  open-id="oDpvq0LNzwhMGfiNRbq-NthY5oUo"
  source-from="{{ sourceFrom }}"
  video-id="{{ videoId }}"
  video-kdt-id="{{ videoKdtId }}"
  custom-url="{{ customUrl }}"
  cover-url="{{ coverUrl }}"
  progress-bar="{{ progressBar }}"
  video-chamfer="{{ videoChamfer }}"
  page-padding="{{ pagePadding }}"
  multi-size="true"
  bind:play="handlePlay"
  bind:pause="handlePause"
  bind:ended="handleEnded"
  bind:timeupdate="handleUpdateTime"
  style="width:100%"
/>
