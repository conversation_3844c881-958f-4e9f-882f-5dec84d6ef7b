import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

/**
 * 包裹了一层插件的代码，将componentData的数据转成插件需要的格式
 */
Component({
  behaviors: [componentBehavior, loggerBehavior],

  attached() {
    const { componentData = {} } = this.data;
    const {
      video = {},
      sourceFrom = 1,
      customUrl = '',
      surfacePlot = 1,
      surfacePlotImage = '',
      progressBar = 2,
      videoChamfer = 1,
      pagePadding = 0,
    } = mapKeysCase.toCamelCase(componentData);
    const { coverUrl = '', videoId = 0, videoKdtId } = video;
    const CUSTOM_POSTER = 2;

    let calcCoverUrl = '';

    if (sourceFrom === 1) {
      calcCoverUrl = cdnImage(
        surfacePlot === CUSTOM_POSTER ? surfacePlotImage : coverUrl,
        '!730x0.jpg'
      );
    } else {
      calcCoverUrl = cdnImage(surfacePlotImage, '!730x0.jpg');
    }

    this.setData({
      sourceFrom,
      videoId,
      videoKdtId,
      customUrl,
      coverUrl: calcCoverUrl,
      progressBar,
      videoChamfer,
      pagePadding,
    });
  },

  methods: {
    sendStartLogger() {
      if (this.startLogger) {
        return;
      }

      this.startLogger = true;
      this.ensureAppLogger('logger', {
        et: 'click',
        ei: 'play',
        en: '开始播放',
        params: {
          banner_id: this.getBannerId(),
          ...this.getComponentLoggerExtraParams()
        }
      });
    },

    sendStopLogger() {
      if (this.hasSendStopLogger) {
        return;
      }

      this.hasSendStopLogger = true;
      this.ensureAppLogger('logger', {
        et: 'custom',
        ei: 'play_time',
        en: '播放时长',
        params: {
          time: Math.ceil(this.currentTime),
          banner_id: this.getBannerId(),
          ...this.getComponentLoggerExtraParams()
        }
      });
    },

    handlePlay({ detail }) {
      this.sendStartLogger();
      this.triggerEvent('play', detail);
    },

    handlePause({ detail }) {
      this.sendStopLogger();
      this.triggerEvent('pause', detail);
    },

    handleEnded({ detail }) {
      this.sendStopLogger();
      this.triggerEvent('ended', detail);
    },

    handleUpdateTime({ detail }) {
      const {
        detail: { currentTime = 0 }
      } = detail;
      this.currentTime = currentTime;
    }
  }
});
