.teacher {
  &__wrap {
    display: flex;
    flex-wrap: wrap;

    &--listMode0 {
      flex-direction: row;
    }
    &--listMode1 {
      flex-direction: row;
    }
    &--listMode2 {
      flex-direction: column;
    }
    &--listMode3 {
      overflow: auto;
      flex-direction: row;
      flex-wrap: nowrap;
    }
  }
}

.teacher-item {
  /* 一行两个 */
  &--size0 {
    width: 50%;
  }
  /* 一行三个 */
  &--size1 {
    width: 33.333%;
  }
  /* 详细列表 */
  &--size2 {
  }
  /* 横向滚动 */
  &--size3 {
    position: relative;
    flex: 0 0 30%;
    min-width: 98px;
  }
}
