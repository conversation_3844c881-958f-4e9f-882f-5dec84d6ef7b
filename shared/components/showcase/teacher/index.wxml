<view>
  <view class="theme-feature-teacher">
    <view class="teacher__wrap teacher__wrap--listMode{{ listMode }}" style="margin: {{ pageMargin }}px">
      <cap-teacher-item
        class="teacher-item teacher-item--size{{listMode}}"
        wx:for="{{ teacherList }}"
        wx:key="{{ item.id }}"
        theme-class="{{ themeClass }}"
        mode="card"
        teacher="{{ item }}"
        size="{{ listMode }}"
        goodsPadding="{{ goodsPadding }}"
        cardStyle="{{ cardStyle }}"
        avatarPos="{{ avatarPos }}"
        avatarShape="{{ avatarShape }}"
        goodsRadius="{{ goodsRadius }}"
        imgSize="{{ imgSize }}"
        textStyle="{{ textStyle }}"
        textAlign="{{ textAlign }}"
        displayContent="{{ displayContent }}"
      />
    </view>
  </view>
</view>
