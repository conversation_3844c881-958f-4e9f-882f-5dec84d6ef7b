import WscComponent from 'shared/common/base/wsc-component/index';
import api from './api.js';
import componentBehavior from '../behaviors/component-behavior';

WscComponent({
  options: {
    addGlobalClass: true
  },

  type: 'teacher',

  behaviors: [componentBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
    },
    kdtId: {
      type: String
    }
  },
  // https://doc.qima-inc.com/pages/viewpage.action?pageId=228183457
  data: {
    itemType: 'teacher',
    pickTeacher: 0,
    teacherList: [], // [{ teacherName: 'zys', duty: 'ok', description: 'desc', icon: 'https://img01.yzcdn.cn/upload_files/2019/07/18/FrASg48j8t3L-eqkj9ffzyHyVO_I.png!origin.png' }],
    avatarPos: 0,
    avatarShape: 1,
    displayContent: [0, 1, 2],
    listMode: 2,
    cardStyle: 0,
    goodsRadius: 0,
    imgSize: 0,
    textStyle: 1,
    textAlign: 0,
    pageMargin: 15,
    goodsPadding: 10
  },

  attached() {
    const itemData = this.data.componentData;
    this.setYZData({
      ...itemData.teacherData,
      displayContent: {
        label: itemData.teacherData.displayContent.indexOf(0) > -1,
        duty: itemData.teacherData.displayContent.indexOf(1) > -1,
        desc: itemData.teacherData.displayContent.indexOf(2) > -1
      }
    });
    this.fetchTeacherList();
  },

  methods: {
    /**
     * 获取老师列表
     */
    fetchTeacherList() {
      const { pickTeacher, kdtId } = this.data;
      const params = {
        kdtId
      };
      // 自定义老师
      if (pickTeacher === 0) {
        if (this.data.teacherList[0].isMock) {
          return;
        }
        params.source = 1;
        const idsStr = this.data.teacherList.map(item => item.id).join(',');
        params.teacherIds = idsStr === '' ? undefined : idsStr;
      }
      // 全部老师
      if (pickTeacher === 1) {
        params.source = 0;
        params.page = 1;
        params.page_size = 50;
      }
      api
        .fetchTeacherList(params)
        .then(res => {
          this.setYZData({
            teacherList: res.content
          });
        })
        .catch(err => {
          console.log(err);
        });
    }
  }
});
