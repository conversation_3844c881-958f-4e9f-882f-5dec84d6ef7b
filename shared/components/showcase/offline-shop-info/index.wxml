<view class="cap-offline-shop-info__container">
  <view class="cap-offline-shop-info__header" wx:if="{{ itemData.name }}">
    <swiper class="cap-offline-shop-info__swipe" autoplay="{{ true }}" interval="{{ 2000 }}" circular="{{ true }}">
      <block wx:for="{{ itemData.images }}" wx:for-index="listIndex" wx:key="{{ item }}-{{ listIndex }}">
        <swiper-item class="cap-offline-shop-info__shop-img-wrap" data-current="{{ listIndex }}" bind:tap="previewImage">
          <image src="{{ item }}" mode="aspectFill" class="cap-offline-shop-info__shop-img" />
        </swiper-item>
      </block>
    </swiper>
  </view>
  <view class="cap-offline-shop-info__body" wx:if="{{ itemData.name }}">
    <view class="cap-offline-shop-info__logo-wrap cap-offline-shop-info__logo-wrap--{{ logoStyleClass }}">
      <image class="cap-offline-shop-info__logo" mode="aspectFill" src="{{ logoUrl }}" />
    </view>
    <view class="cap-offline-shop-info__detail">
      <view class="cap-offline-shop-info__shop-name">{{ itemData.name }}</view>
      <view class="cap-offline-shop-info__tel" bind:tap="callShop">
        <van-icon size="16px" name="phone" class="cap-offline-shop-info__tel-icon" />
      </view>
    </view>
    <view class="cap-offline-shop-info__address" bind:tap="handleOpenLocation">
      <van-icon size="16px" name="location-o" class="cap-offline-shop-info__info-icon" />
      <text class="cap-offline-shop-info__address-detail">{{ itemData.address }}</text>
      <van-icon size="16px" name="arrow" class="cap-offline-shop-info__address-arrow" />
    </view>
    <view class="cap-offline-shop-info__open-time">
      <van-icon size="16px" name="clock-o" class="cap-offline-shop-info__info-icon" />
      {{ itemData.time }}
    </view>
  </view>
</view>