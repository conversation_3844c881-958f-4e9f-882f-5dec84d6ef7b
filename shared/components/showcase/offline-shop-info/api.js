import { node as request } from 'shared/utils/request';

export const getOfflineShopInfo = ({ id, kdtId }) => {
  return request({
    path: '/wscshop/showcase/multistore/detail.json',
    data: {
      id,
      kdt_id: kdtId,
    },
  });
};

export const getChainOfflineShopInfo = ({ id }) => {
  return request({
    path: '/wscdeco/decorate-api/getOfflineStoreInfo.json',
    data: {
      storeKdtId: id,
    },
  });
};
