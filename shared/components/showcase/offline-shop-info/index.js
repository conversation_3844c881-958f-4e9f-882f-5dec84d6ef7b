import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState } from '@youzan/vanx';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { baiduToGcj } from 'shared/utils/lbs';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { getOfflineShopInfo, getChainOfflineShopInfo } from './api';
import { checkRetailMinimalistShop } from '@youzan/utils-shop';

const showMethodMap = {
  0: 'left',
  1: 'center',
  2: 'right'
};

function processShopImages(images) {
  images = images || [];
  return images.map((image) => {
    return cdnImage(image, '!730x0.jpg');
  });
}

const convertData = (itemData) => {
  const storeData = itemData.store || {};
  // 目前存在字符串形式的单个img，暂时不知道数据来源，先做格式处理
  const storeDataImg = Array.isArray(storeData.images)
    ? storeData.images
    : [storeData.images];
  const images = processShopImages(storeDataImg);
  return {
    images,
    logoStyle: itemData.logo_style,
    name: storeData.name,
    id: storeData.id,
    tel: '',
    address: storeData.address,
    time: storeData.time,
    lat: '',
    lng: ''
  };
};

VanxComponent({
  behaviors: [componentBehavior, loggerBehavior],

  mapData: {
    ...mapState('/', {
      logoUrl(state) {
        const { logo_url: logoUrl } = this.data.componentData || {};
        // 优先用当前店铺的 logo
        return cdnImage(state.shop.logo || logoUrl, '!125x125.jpg');
      }
    })
  },

  properties: {
    offlineId: {
      type: Number,
      value: 0,
      observer: 'getOfflineShopInfo'
    }
  },

  data: {
    logoStyleClass: 'left',
    itemData: {},
    isAttached: false
  },

  attached() {
    const { componentData = {} } = this.data;
    const itemData = convertData(componentData);

    this.setData({
      logoStyleClass: showMethodMap[+itemData.logoStyle],
      itemData,
      isAttached: true
    });
    this.getOfflineShopInfo();
  },

  methods: {
    getOfflineShopInfo(offlineId) {
      const { componentData, kdtId, itemData, isAttached } = this.data;
      const id = offlineId || (componentData.store || {}).id;

      // 蜜汁小程序
      if (!isAttached) {
        return;
      }

      const app = getApp();
      const { shopMetaInfo = {} } = app.getShopInfoSync();

      if (checkRetailMinimalistShop(shopMetaInfo)) {
        getChainOfflineShopInfo({ id }).then((data) => {
          const {
            configs,
            address,
            shopName,
            customerServiceAreaCode: areaCode,
            customerServicePhoneNumber: phoneNumber,
            summary,
            lat,
            lng,
          } = data;

          const photos = configs?.physical_store_photo?.split(';');
          // const gcjLocation = baiduToGcj(+lng, +lat);
          itemData.images = processShopImages(photos);
          itemData.address = address;
          itemData.name = shopName;
          itemData.tel = areaCode ? `${areaCode}-${phoneNumber}` : phoneNumber;
          itemData.time = summary;
          itemData.lat = lat;
          itemData.lng = lng;

          this.setYZData({
            itemData,
          });
        });
        return;
      }

      getOfflineShopInfo({ id, kdtId }).then((res) => {
        const { address: addressInfo, name, phone, images, businessTimeSettingString } = res || {};
        const { areaCode: phone1, localNumber: phone2 } = phone || {};
        const { province, city, district, address, lat, lng } = addressInfo || {};

        itemData.name = name;
        itemData.images = processShopImages(images);
        itemData.tel = phone1 ? `${phone1}-${phone2}` : phone2;
        itemData.address = province + city + district + address;
        itemData.time = businessTimeSettingString;
        itemData.lat = lat;
        itemData.lng = lng;

        this.setYZData({
          itemData
        });
      });
    },

    callShop() {
      const loggerMsg = {
        et: 'click',
        ei: 'click_phone',
        en: '线下门店拨打电话',
        params: {
          banner_id: this.getBannerId()
        }
      };
      this.ensureAppLogger('logger', loggerMsg);

      const { tel } = this.data.itemData;
      tel &&
        wx.makePhoneCall({
          phoneNumber: tel
        });
    },

    previewImage(e) {
      const { images } = this.data.itemData;
      const { current } = e.currentTarget.dataset;

      wx.previewImage({
        current: images[current],
        urls: images
      });
    },

    handleOpenLocation() {
      const { lat, lng, name, address } = this.data.itemData;

      if (lat && lng) {
        const loggerMsg = {
          et: 'click',
          ei: 'open_location',
          en: '打开位置',
          params: {
            banner_id: this.getBannerId()
          }
        };
        this.ensureAppLogger('logger', loggerMsg);

        const gcjLocation = baiduToGcj(lng, lat);
        wx.openLocation({
          latitude: gcjLocation.lat,
          longitude: gcjLocation.lng,
          name,
          address
        });
      }
    }
  }
});
