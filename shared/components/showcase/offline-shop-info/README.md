## OfflineShopInfo 线下门店信息

### 使用指南

在 index.json 中引入组件。

```json
{
  "usingComponents": {
    "cap-goods": "path/to/@youzan/captain-weapp/dist/off-line-shop-info/index"
  }
}
```

### 代码演示

#### 基础用法

```html
<cap-offline-shop-info
  name="{{ name }}"
  tel="{{ tel }}"
  logo-url="{{ logoUrl }}"
  logo-style="1"
  address="{{ address }}"
  time="{{ time }}"
  images="{{ imgs }}"
/>
```
```javascript
Page({
  data: {
    name: '测试店铺',
    tel: '0571-88776644',
    logoUrl: 'https://img01.yzcdn.cn/upload_files/2017/09/29/Fop38RNrhlmSIvf3xqGmmOpBUJ2T.jpg?imageView2/2/w/280/h/280/q/75/format/jpg',
    address: '我是地址',
    time: '每周一 8：00 - 10：00',
    imgs: ['https://img01.yzcdn.cn/upload_files/2017/10/13/FuSm71c794c6f34Jfa7t5sFQjNOD.jpg', 'https://img01.yzcdn.cn/upload_files/2017/10/19/FpWnu__jZDyXybTg-r3a3bSw5R1R.jpg']
  }
});
```

### API

| 参数       | 说明      | 类型       | 默认值       | 必须      |
|-----------|-----------|-----------|-------------|-------------|
| name | 店铺名 | `String`  |   | 是 |
| tel | 店铺电话 |  `String` |  | 是 |
| logoUrl | logo链接 |  `String` |  | 是 |
| logoStyle | logo位置（可选值'0', '1', '2'），分别对应左、中、右 |  `String` | '0' | 否 |
| images | 店铺轮播图片 |  `Array` |  | 是 |
| address | 店铺地址 |  `String` |  | 是 |
| time | 店铺营业时间 |  `String` |  | 是 |
