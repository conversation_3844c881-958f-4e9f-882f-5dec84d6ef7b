.live {
  &-item {
    display: flex;
    align-items: stretch;
    height: 140px;
    margin: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    background-color: #fff;
    overflow: hidden;
  }

  &-img {
    flex: 0 1 auto;
    width: 140px;
    height: 100%;
    object-fit: cover;
  }

  &-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 12px 8px;
  }

  &-name {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 22px;
    font-size: 16px;
    font-weight: 700;
    color: #323233;
  }

  &-time {
    margin-top: 8px;
    line-height: 16px;
    font-size: 12px;
    color: #969799;
  }

  &-status {
    display: inline-flex;
    align-items: center;
    height: 20px;
    margin-top: auto;
    padding: 0 8px;
    border-radius: 10px;
    background-color: rgba(200, 201, 204, .2);
    font-size: 12px;
    color: #646566;

    &::before {
      content: '';
      width: 6px;
      height: 6px;
      margin-right: 6px;
      border-radius: 100%;
      background-color: #ed6a0c;
    }

    &--start::before {
      background-color: #07c160;
    }

    &--end::before {
      background-color: #969799;
    }
  }
}
