import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import componentBehavior from '../behaviors/component-behavior';
import transform from '../utils/transformComponentData';

const STATUS_BASE_CLASS = 'live-status';
const STATUS_CLASS_SUFFIX = ['not-start', 'start', 'end'];
const STATUS_TEXT = ['未开播', '进入直播', '直播结束'];

Component({
  behaviors: [componentBehavior],

  attached() {
    const liveData = transform(this.data.componentData, {
      rooms: this.transformRooms
    });

    this.setData({ rooms: liveData.rooms });

    // this.updateStatus();
  },

  methods: {
    transformRooms(value) {
      const now = new Date();

      return (value || []).map((item) => {
        const { startTime, endTime, shareImg, coverImg } = item;
        const status = now < new Date(startTime.replace(/-/g, '/')) ? 0 : 1;

        return {
          ...item,
          status,
          shareImg: cdnImage(shareImg || coverImg, '!400x400.jpg'),
          time: `${startTime.slice(5)} ~ ${endTime.slice(5)}`,
          statusClass: [STATUS_BASE_CLASS, `${STATUS_BASE_CLASS}--${STATUS_CLASS_SUFFIX[status]}`],
          statusText: STATUS_TEXT[status]
        };
      });
    },

    // 更新直播状态。暂时不要调用
    updateStatus() {
      // 引入获取直播状态接口
      // eslint-disable-next-line no-undef
      const livePlayerPlugin = requirePlugin('live-player-plugin');

      const fetchStatus = () => {
        const tasks = this.data.rooms.map((item) => {
          return livePlayerPlugin.getLiveStatus({ room_id: item.roomId }).catch((err) => {
            console.log(err);
          });
        });

        Promise.all(tasks).then((res) => {
          const update = res.reduce((obj, current, index) => {
            if (current) {
              obj[`rooms.${index}.status`] = current.liveStatus;
            }
            return obj;
          }, {});

          this.setData(update);
        });
      };

      fetchStatus();

      // 2 分钟频率去轮询获取直播状态
      setInterval(() => {
        fetchStatus();
      }, 120000);
    }
  }
});
