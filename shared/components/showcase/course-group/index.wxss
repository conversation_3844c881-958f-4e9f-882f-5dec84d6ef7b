.edu-goods-group {
  display: flex;
  width: 100%;
  /* background-color: #f8f8f8; */
}

.template_type0 .cap-edu-goods {
  width: 100%;
}

.template_type1 .cap-edu-goods { 
  width: calc(100% - 88px);
}

.edu-goods-group .edu-group-tabs {
  background-color:#fff;
}

.menu_type1 .edu-group-tabs,
.menu_type2 .edu-group-tabs {
  padding-bottom: 10px;
  padding-left: 16px;
}

.edu-group-tab-tag {
  position: relative;
}

.menu_type0 .edu-group-tab-tag {
  padding: 0 5px;
}

.menu_type1 .edu-group-tab-tag {
  color: #7d7e80;
  border-radius: 14px;
  background-color: #ebedf0;
  margin-top: 10px;
  height: 28px;
  line-height: 28px;
  padding: 0 16px;
  margin: 10px 5px 0 5px;
}

.menu_type2 .edu-group-tab-tag {
  color: #7d7e80;
  margin-top: 10px;
  height: 28px;
  line-height: 28px;
  border-radius: 2px;
  border: 1px solid #e5e5e5;
  padding: 0 16px;
  margin: 10px 5px 0 5px;
}

/* .menu_type0 .edu-group-tab-tag_active {
  border-bottom: 2px solid;
} */

.menu_type2 .edu-group-tab-tag_active, 
.menu_type1 .edu-group-tab-tag_active {
  background-color: #65c4aa !important;
  color: #fff !important;
}

/* .menu_type0 .edu-group-tab-tag {
  padding: 0 5px;
  flex: 0 0 20%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 0;
  max-width: 50%;
} */

.template_type0 {
  flex-direction: column;
}

/* .template_type1 .cap-edu-goods {
 background-color: #fff !important
} */

.template_type0 .edu-group-tabs {
  flex-direction: row ;
  display: flex;
  text-align: center;
  line-height: 44px;
}

.template_type0 .edu-group-tabs .edu-group-tab-tag {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 0;
  max-width: 50%;
  flex-grow:1;
}

.template_type0 .edu-group-tabs .edu-group-tab-tag-more {
  flex: 0 0 20% !important;
}

.template_type1 {
  flex-direction: row ;
}

.template_type1 .edu-group-tabs{
  display: flex;
  flex-direction: column;
  flex-basis: 88px;
  background-color: #f8f8f8;
  width: 0;
  flex-grow: 0;
  flex-shrink: 0;
}

.template_type1 .edu-group-tab-tag {
  font-size: 14px;
  text-align: center;
  height: 56px;
  border-left: 3px solid transparent;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  word-break: break-all;
  padding: 0 5px;
}

.template_type1 .edu-group-tab-tag_active {
  position: absolute;
  left: -2.5px;
  width: 2px;
  height: 100%;
  top: 0;
}

.menu_type0 .edu-group-tab-tag_active{
  position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -20%;
  width: 40%;
  height: 2px;
}




.menu_wrap0 .edu-group-tabs {
  flex-wrap: wrap;
}

.menu_wrap1 .edu-group-tabs {
  flex-wrap: nowrap;
  overflow: auto;
}
/* 
.menu_position1 .edu-group-tabs {
  position: fixed;
} */
