import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';

const app = getApp();

Component({
  options: {
    addGlobalClass: true
  },

  type: 'edu-goods-group',

  behaviors: [componentBehavior, loggerBehavior],

  properties: {
    componentData: {
      type: Object,
      observer() {
        this.updateContent();
      },
      value: {},
    },
    kdtId: {
      type: String
    }
  },

  data: {
    classWrap: 'edu-goods-group',
    groupList: [],
    displayAllGroup: false,
    currentGroup: 0,
    eduGoods: {},
    themeColor: '',
    loading: false,
  },

  methods: {

    updateContent() {
      const itemData = this.data.componentData;
      app.getShopTheme().then(res => {
        this.setData({
          themeColor: res.colors['main-bg'],
          groupList: this.getGroupList(itemData),
          classWrap: this.renderClassWrap(itemData),
          eduGoods: this.getEduGoods(itemData),
        }, () => {
        });
      });
    },

    getEduGoods(itemData = {}) {
      const eduGoods = {
        ...itemData,
      };
      return eduGoods;
    },

    getGroupList(itemData) {
      const { displayAllGroup, groupList = [] } = itemData || {};
      let totalList = [...groupList];
      if (displayAllGroup) {
        totalList = [{
          alias: '',
          title: '全部',
          displayTitle: '全部',
        }, ...groupList];
      }
      return totalList;
    },

    renderClassWrap(itemData) {
      const { menuPosition, menuType, menuWrap, templateType } = itemData || {};
      if (templateType === 1) {
        return `edu-goods-group template_type${templateType || 0}`;
      }
      return `edu-goods-group menu_position${menuPosition || 0} menu_type${menuType || 0} menu_wrap${menuWrap || 0} template_type${templateType || 0}`;
    },

    setActiveTab(e) {
      const { index = 0 } = e.currentTarget.dataset;
      this.setData({ currentGroup: index });
    }
  }
});
