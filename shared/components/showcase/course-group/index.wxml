<view class="edu-group__wrap">
  <view wx:if="{{ groupList.length }}" class="{{ classWrap }}">
    <view wx:if="{{ componentData.templateType !== 0 ? groupList.length >= 1 : groupList.length > 1}}" class="edu-group-tabs" style="{{groupList.length === 4 && componentData.templateType === 0 ? 'justify-content: space-between' : ''}}">
        <block wx:for="{{ groupList }}" wx:key="alias" >
            <view class="{{ groupList.length >=4 ? 'edu-group-tab-tag edu-group-tab-tag-more' : 'edu-group-tab-tag' }}" style="background-color: {{ index === currentGroup ? componentData.templateType === 1 ? '#fff' : componentData.menuType !== 0 ? themeColor : '' : '' }}; border-width: {{ index === currentGroup && componentData.menuType !== 0 ? 0 : ''}};  color: {{ componentData.templateType !== 1 && index === currentGroup ? componentData.menuType !== 0 ? '#fff' : themeColor : '' }}" data-index="{{ index }}" bindtap="setActiveTab"> 
              {{ item.displayTitle }}
              <view wx:if="{{ index === currentGroup && (componentData.menuType === 0 || componentData.templateType === 1 ) }}" class="edu-group-tab-tag_active" style="background-color: {{ themeColor }}"></view>
            </view>
        </block>
    </view>
    <view class="cap-edu-goods">
        <cap-edu-goods
          eduGoods="{{ eduGoods }}"
          kdtId="{{ kdtId }}"
          page-lifetimes="{{ ['onReachBottom', 'onPullDownRefresh', 'onPageScroll'] }}"
          currentGroup="{{ currentGroup }}"
          totalGroupList="{{ groupList }}"
          themeColor="{{ themeColor }}"
        />
    </view>
  </view>
</view>