.eduRegisForm {
  padding: 15px;

  .van-button--large {
    height: 44px;
    line-height: 44px;
  }

  &__cityWithAddr {
    &::after {
      left: 100px !important;
    }
  }

  &__field {
    &--black {
      input {
        color: #323233;
        -webkit-text-fill-color: #323233;
      }
    }
  }

  &__imgUploader {
    .imgUploader__label {
      display: flex;
      flex-direction: row;
      align-items: center;
      color: #666;
      > span {
        color: #a7a7a7;
        font-size: 12px;
      }
    }
    .visImgUploader {
      margin-top: 12px;
      .van-icon-photograph {
        font-size: 20px;
      }
      .visImgUploader__uploaderWrap {
        width: 76px;
        height: 76px;
        position: relative;

        .van-uploader {
          position: relative;
          display: flex;
          z-index: 1;
          width: 100%;
          height: 100%;
          box-align: center;
          align-items: center;
          justify-content: center;
          border-radius: 2px;
          background-color: #f7f8fa;
          color: #dcdee0;
          overflow: hidden;
        }

        &__deleteIcon {
          position: absolute;
          top: -10px;
          right: -10px;
          z-index: 2;

          font-size: 20px;
          color: #323233;
          &::before {
            background: #fff;
            border-radius: 100%;
          }
        }

        input {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          cursor: pointer;
        }
      }
    }
  }

  &__main {
    border-radius: 8px;
    overflow: hidden;
    .van-radio__label {
      color: #323233;
    }

    .van-cell:not(:last-child):after {
      right: 15px;
    }

    .van-cell__title {
      flex: 0 0 90px;
      color: #666;
    }

    .van-hairline--top-bottom:after {
      border-width: 0;
    }

    [class*=van-hairline]:after {
      left: 15px;
      right: 15px;
    }

    van-radio-group {
      display: flex;
      flex-wrap: wrap;
      van-radio + van-radio {
        margin-left: 30px;
      }
    }

    .van-radio__icon--checked .van-icon-custom {
      background-color: #f44;
      border-color: #f44;
    }
  }

  &__getCaptcha {
    color: #f44 !important;
    cursor: pointer !important;
    height: 24px !important;
    line-height: 24px !important;
    border: 0 !important;
  }

  &__btn {
    margin-top: 20px;
    .van-button {
      background-color: #f44;
      border-color: #f44;
      border-radius: 25px;
    }
  }

  &__radioActionsheet {
    .van-button {
      background-color: #f44;
      border-color:#f44;
      border-radius: 25px;
    }
    .radioActionsheet {
      &__wrap {
        display: flex;
        flex-direction: column;
      }
      &__bd {
        flex-grow: 1;
        overflow: auto;
      }
      &__item {
        position: relative;
        height: 48px;
        line-height: 48px;
        display: flex;
        align-items: center;
        color: #323233;
        font-size: 14px;
        padding: 0 15px;
        cursor: pointer;
        &:after {
          content: '';
          display: block;
          position: absolute;
          left: 15px;
          right: 0;
          bottom: 0;
          height: 1px;
          background-color: #f2f2f2;
          display: none;
        }
        // .van-icon-success {
        //   // visibility: hidden;
        // }
        // &--active {
        //   color: #f44;
        //   .van-icon-success {
        //     color: rgb(44, 29, 29);
        //     visibility: visible;
        //   }
        // }
      }
      &__ft {
        padding: 15px;
      }
      &__checkbox {
        margin-right: 8px;
        width: 18px;
        height: 18px;
        line-height: 18px;
        text-align: center;
        border-radius: 50%;
        overflow: hidden;
        border: 1px solid #e5e5e5;
        .van-icon-success {
          width: 18px;
          height: 18px;
          line-height: 18px;
        }
      }
    }
    .options {
      &-header {
        position: relative;
        height: 44px;
        line-height: 44px;
        text-align: center;
  
        van-icon {
          position: absolute;
          right: 16px;
          color: #969799;
          top: 0;
          bottom: 0;
          margin: auto 0;
        }
      }
    }
  }

  .van-number-keyboard {
    z-index: 200 !important;
  }
  .van-key--gray {
    font-size: 16px;
  }
}
