import Toast from '@vant/weapp/dist/toast/toast';
import {
  node as request
} from 'shared/utils/request';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import {
  format
} from '@youzan/weapp-utils/lib/time-utils';
import upload from 'utils/upload';
import WscComponent from 'shared/common/base/wsc-component/index';
import componentBehavior from '../behaviors/component-behavior';
import api from './api';
import getSystemInfo from 'shared/utils/browser/system-info';
import theme from 'shared/common/components/theme-view/theme';

const {
  windowHeight
} = getSystemInfo();

const app = getApp();
const NOW = new Date();
const MAX_AGE = 120;

function genCurrent() {
  return {
    radioValue: '', // 单选项当前选中项的值
    radioValueList: [], // 单选项列表
    dateValue: NOW.getTime(),
    areaCode: '',
    areaValue: [],
  };
}

function getProvince(itemValue) {
  if (typeof itemValue === 'object') {
    return itemValue.map((i) => i.name).join('');
  }
  return itemValue;
}

const Types = {
  stuTel: 'edu_stuContractPhone',
  stuName: 'edu_stuName',
  addr: 'edu_stuAddress',
  birth: 'edu_stuBirth',
};

const ProfileEnums = {
  province: 3,
  address: 6,
};

WscComponent({
  options: {
    addGlobalClass: true,
  },

  type: 'regis-form',

  behaviors: [componentBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
    },
    kdtId: {
      type: String,
    },
    isHomePage: Boolean,

    alias: String,
    props: {
      type: Object,
      value: {},
    },
    smsStock: {
      // 剩余短信数量
      type: Number,
      value: 0,
    },
    extraData: {
      type: Object,
    },
  },

  data: {
    prefix: 'eduRegisForm', // 组件前缀
    addr: '',
    form: {},
    convertedAddress: {},
    visible: {
      radio: false,
      date: false,
      area: false,
      numberKeyboard: false,
    },
    current: genCurrent(),
    loading: {
      area: false,
      captcha: false,
      submit: false,
      upload: false,
    },
    minDate: new Date(String(NOW.getFullYear() - MAX_AGE)).getTime(),
    maxDate: NOW.getTime(),
    smsCaptcha: '',
    areaList: {},
    captchaTimeout: 0,
    captchaTimer: null,

    standardDataItemList: [],
    submitText: '立即报名',
    dataList: [],
    popupStyle: `
      min-height: ${windowHeight * 0.5 - 44}px;
      max-height: ${windowHeight * 0.8 - 44}px
    `,
    themeGeneral: '',
    // 快照保存的报名表单的各个资料项id组成的数组
    oldFilterIdArray: [],
    // 快照保存的报名表单的资料项itemTags组成的数组
    queryDTO: {},
    // 判断单选或者多选的选项是否修改
    hasChangedOptions: false,
  },

  ready() {
    theme.getThemeColor('general').then((color) => {
      this.setYZData({
        themeGeneral: color,
      });
    });
  },

  attached() {
    const itemData = this.data.componentData;
    const oldFilterIdArray = [];
    let queryDTO = {
      excludeByAttribueKeyarray: [],
    };
    itemData.editorState.list.forEach((element) => {
      oldFilterIdArray.push(element.itemId);
      if (element.itemTags.length !== 0) {
        queryDTO.excludeByAttribueKeyarray.push(element.itemTags[0]);
      }
    });
    this.setYZData({
      oldFilterIdArray,
      queryDTO,
    });
    // this.setYZData({ props: itemData.editorState, ...itemData.editorState });
    this.getStudentDataItems(itemData.editorState)
      .then(() => {
        this.checkSmsStock();
        this.initFormValue();
        this.getAreaList();
      })
  },

  methods: {
    /**
     * C端查询学员资料项列表
     */
    getStudentDataItems(editorState) {
      const {
        oldFilterIdArray: itemIds,
        queryDTO
      } = this.data;
      return (
        api
        .findDataItems({
          itemIds,
          queryDTO,
        })
        .then((list) => {
          // 将获取到的list更新到editorState中
          let newestList = [];
          editorState.list.forEach((item) => {
            const currentConfig = list.filter(
              (li) => li?.itemId === item.itemId
            )[0];
            if (currentConfig) {
              newestList.push({
                ...item,
                ...currentConfig,
              });
            }
          });
          this.setYZData({
            dataList: newestList,
          });
          // 更新editorState
          let newestEditorState = {
            ...editorState,
            list: newestList,
          };
          this.setYZData({
            props: newestEditorState,
            ...newestEditorState,
          });
        })
        .catch((errorMsg) => {
          console.log('errorMsg :>> ', errorMsg);
          Toast(errorMsg);
        })
      );
    },
    /**
     * 检测短信验证码剩余数量
     */
    checkSmsStock() {
      api
        .checkSmsStock()
        .then((res) => {
          this.setYZData({
            smsStock: (res || {}).value,
          });
        })
        .catch((err) => {
          Toast(err || '未知错误');
        });
    },

    /**
     * 初始化表单值
     */
    initFormValue() {
      const {
        list = []
      } = this.data.props;

      // 如果同时存在“学员生日”和“学员年龄”两个资料项，禁用学员年龄资料量
      const ageConfIndex = list.findIndex(
        (item) => item.itemName === '学员年龄'
      );
      const birthConfIndex = list.findIndex(
        (item) => item.itemName === '学员生日'
      );
      if (ageConfIndex > -1 && birthConfIndex > -1) {
        list[ageConfIndex].placeholder = '选择生日后自动计算';
        list[ageConfIndex].disabled = true;
      }
      list.forEach((item) => {
        this.setYZData({
          form: {
            ...this.data.form,
            [item.itemId]: '',
          },
        });
        // this.$set(this.data.form, item.itemId, '');
      });

      this.setYZData({
        dataList: list,
        addr: '',
        current: genCurrent(),
        smsCaptcha: '',
        convertedAddress: {},
      });
    },

    /**
     * 获取地址数据
     */
    getAreaList() {
      this.setYZData({
        loading: {
          ...this.data.loading,
          area: true,
        },
      });

      request({
          method: 'GET',
          path: '/wsctrade/uic/address/getAllRegion.json',
        })
        .then((data) => {
          this.setYZData({
            areaList: data,
            loading: {
              ...this.data.loading,
              area: false,
            },
          });
        })
        .catch(() => {
          this.setYZData({
            loading: {
              ...this.data.loading,
              area: false,
            },
          });
        });
    },

    /**
     * 上传图片（学员头像）
     * @param {Object} e
     */
    uploadImage(e) {
      const {
        id
      } = e.currentTarget.dataset;

      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        // sourceType: ['album', 'camera'],
        success: (res) => {
          // 上传
          Toast.loading();
          this.setYZData({
            loading: {
              ...this.data.loading,
              upload: true,
            },
          });

          upload({
            file: res.tempFilePaths[0],
            success: (data) => {
              Toast.clear();

              const url = data.attachment_url;

              this.setYZData({
                form: {
                  ...this.data.form,
                  [id]: cdnImage(url),
                },
                loading: {
                  ...this.data.loading,
                  upload: false,
                },
              });
            },
            fail: (res) => {
              Toast.clear();
              wx.showToast({
                title: res.msg,
                icon: 'none',
              });

              this.setYZData({
                form: {
                  ...this.data.form,
                  [id]: '',
                },
                loading: {
                  ...this.data.loading,
                  upload: false,
                },
              });
            },
          });
        },
        fail: () => {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none',
          });
        },
      });
    },

    /**
     * 删除图片（学员头像）
     * @param {Object} e
     */
    deleteImage(e) {
      const {
        id
      } = e.currentTarget.dataset;
      this.setYZData({
        form: {
          ...this.data.form,
          [id]: '',
        },
      });
    },

    /**
     * 选择学员性别
     * @param {Object} e
     */
    chooseGenderRadio(e) {
      const {
        id
      } = e.currentTarget.dataset;
      this.setYZData({
        form: {
          ...this.data.form,
          [id]: e.detail,
        },
      });
    },

    /**
     * 默认 input 事件
     * @param {Object} e
     */
    onInput(e) {
      const {
        id
      } = e.currentTarget.dataset;
      this.setYZData({
        form: {
          ...this.data.form,
          [id]: e.detail,
        },
      });
    },

    /**
     * 验证码输入事件
     * @param {*} e
     */
    onInputSmsCaptcha(e) {
      this.setYZData({
        smsCaptcha: e.detail,
      });
    },

    /**
     * 详细地址 input 事件
     * @param {Object} e
     */
    onAddrInput(e) {
      this.setYZData({
        addr: e.detail,
      });
    },

    onAllTap() {
      wx.navigateTo({
        url: '/packages/new-punch/all/index',
      });
    },

    /**
     * 修改手机号
     */
    changeTelNumber(val) {
      const {
        current,
        form
      } = this;
      if (val === '清空') {
        form[current.itemId] = '';
        return;
      }

      if (val === -1) {
        const str = form[current.itemId];
        form[current.itemId] = str.substr(0, str.length - 1);
        return;
      }

      form[current.itemId] += val;
    },

    /**
     * 发送验证码
     */
    sendCaptcha() {
      if (this.data.captchaTimeout > 0) return;
      const {
        form,
        dataList
      } = this.data;
      const [stuTelItem] = dataList.filter((item) => this.checkDataItemTag(item, Types.stuTel));
      const stuTel = form[stuTelItem.itemId];
      if (/^1\d{10}$/.test(stuTel)) {
        // this.$emit('sendCaptcha', stuTel);
        api
          .createSendTelSmsCaptcha(stuTel)
          .then(() => {
            this.setYZData({
              loading: {
                ...this.data.loading,
                captcha: false,
              },
            });
            Toast('验证码已发送');
            this.captchaCountdown();
          })
          .catch((err) => {
            Toast(err.msg || '验证码发送失败');
            this.setYZData({
              loading: {
                ...this.data.loading,
                captcha: false,
              },
            });
          });
      } else {
        Toast('请输入11位的手机号码');
      }
    },

    /**
     * 验证码倒计时
     */
    captchaCountdown(count = 60) {
      if (this.data.captchaTimer) {
        clearTimeout(this.data.captchaTimer);
      }
      if (count <= 60 && count > 0) {
        this.setYZData({
          captchaTimeout: count - 1,
          captchaTimer: setTimeout(
            () => this.captchaCountdown(count - 1),
            1000
          ),
        });
      }
    },

    /**
     * 修改文件后回调
     */
    changeFiles(files, item) {
      this.data.form[item.itemId] = files
        .filter((item) => item !== undefined)
        .map((item) => item.attachment_url)
        .join(',');
    },

    /**
     * 打开数字键盘
     */
    /*
    openNumberKeyboard(item) {
      const { itemId } = item;
      this.data.current.itemId = itemId;
      this.data.visible.numberKeyboard = true;
    },
    */

    /**
     * 省市县选择器
     */
    openAreaActionsheet(e) {
      const {
        itemId
      } = e.currentTarget.dataset.item;
      this.setYZData({
        current: {
          ...this.data.current,
          itemId,
        },
        visible: {
          area: true,
        },
      });
    },

    /**
     * 打开日期选择
     */
    openDateActionsheet(e) {
      const {
        itemId
      } = e.currentTarget.dataset.item;
      const tag = e.currentTarget.dataset.item.itemName;
      this.setYZData({
        current: {
          ...this.data.current,
          itemId,
          tag,
        },
        visible: {
          date: true,
        },
      });
    },

    /**
     * 打开单选项列表
     */
    openRadioActionsheet(e) {
      const {
        itemValueList = [], itemId
      } = e.currentTarget.dataset.item;
      this.setYZData({
        current: {
          ...this.data.current,
          itemId,
          radioValueList: itemValueList,
          radioValue: this.data.form[itemId],
        },
        visible: {
          ...this.data.visible,
          radio: true,
        },
      });
    },

    /**
     * 确认省市县
     */
    confirmArea(e) {
      const {
        values: arr
      } = e.detail;
      const {
        current,
        form,
        visible,
        convertedAddress
      } = this.data;
      const city = arr[arr.length - 1] || {};
      const {
        code
      } = city;
      this.setYZData({
        form: {
          ...form,
          [current.itemId]: arr,
        },
        current: {
          ...current,
          areaCode: code,
          areaValue: arr,
        },
        visible: {
          ...visible,
          area: false,
        },
        convertedAddress: {
          ...convertedAddress,
          [current.itemId]: getProvince(arr),
        },
      });
    },

    /**
     * 确认学员生日
     */
    confirmDate(e) {
      const {
        current,
        form,
        visible,
        dataList
      } = this.data;
      const tag = current.tag;
      let date = new Date(e.detail);
      const readyData = {
        current: {
          ...current,
          dateValue: e.detail,
        },
        form: {
          ...form,
          [current.itemId]: format(date, 'yyyy-MM-dd'),
        },
        visible: {
          ...visible,
          date: false,
        },
      };
      const ageItem = dataList.find((item) => item.itemName === '学员年龄');
      const birthItem = dataList.find((item) => item.itemName === '学员生日');
      if (ageItem) {
        const ageItemId = ageItem.itemId;
        if (tag !== '学员生日') {
          const birthItemId = birthItem.itemId;
          date = new Date(readyData.form[birthItemId]);
        }
        readyData.form[ageItemId] = this.getAge(date.getTime());
      }
      this.setYZData(readyData);
    },

    getAge(birthDay) {
      const oneDay = 1000 * 60 * 60 * 24;
      const oneMonth = oneDay * 30;
      const oneYear = oneDay * 365;
      const diff = Date.now() - birthDay;
      if (diff >= oneYear) {
        const age = Math.floor(diff / oneYear);
        return `${age}岁`;
      }
      if (diff >= oneMonth) {
        const age = Math.floor(diff / oneMonth);
        return `${age}月`;
      }
      if (diff >= oneDay) {
        const age = Math.floor(diff / oneDay);
        return `${age}天`;
      }
      if (diff < oneDay) {
        return '0岁';
      }
      return '';
    },

    /**
     * 确认学员年级
     */
    confirmRadioActionsheet(e) {
      const {
        item
      } = e.currentTarget.dataset;
      this.setYZData({
        form: {
          ...this.data.form,
          [this.data.current.itemId]: item,
        },
        visible: {
          ...this.data.visible,
          radio: false,
        },
      });
    },

    /**
     * 关闭地区选项（学员联系地址）、日期选项（学员生日）、单选项（学员年纪）下拉菜单
     */
    cancelArea() {
      this.setYZData({
        visible: {
          ...this.data.visible,
          area: false,
        },
      });
    },
    cancelDate() {
      this.setYZData({
        visible: {
          ...this.data.visible,
          date: false,
        },
      });
    },
    cancelRadioActionsheet() {
      this.setYZData({
        visible: {
          ...this.data.visible,
          radio: false,
        },
      });
    },
    // 创建报名表单信息接口
    createRegistration(data) {
      api
        .createRegistrationInfo({
          ...data,
          userId: app.getToken('userId') || null,
          isHomepage: this.data.isHomePage,
        })
        .then((res) => {
          this.setYZData({
            loading: {
              ...this.data.loading,
              submit: false,
            },
          });
          this.initFormValue();
          this.initFiles();
          this.initSendCaptcha();

          if (res) {
            let h5Url = `https://h5.youzan.com/wscvis/edu/apply-result?kdt_id=${app.getKdtId()}&id=${
              res.value
            }&featureAlias=${app.globalData?.featurePageConfig?.featureAlias}`;
            const editorState = this.data.componentData.editorState || {};
            if (editorState.urlType === 'custom' && editorState.customUrl) {
              h5Url = editorState.customUrl.link_url;
            }
            const url = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
              h5Url
            )}`;
            wx.navigateTo({
              url,
            });
          } else {
            Toast({
              duration: 5000,
              message: '报名失败，请重试',
            });
          }
        })
        .catch((errorMsg) => {
          this.setYZData({
            loading: {
              ...this.data.loading,
              submit: false,
            },
          });
          Toast(errorMsg.msg || '未知错误');
        });
    },

    /**
     * 提交报名表单
     */
    submit() {
      const {
        form,
        alias: featureAlias,
        smsCaptcha,
        addr,
        smsStock,
        dataList,
        componentData,
      } = this.data;
      const [stuNameItem = {}] = dataList.filter((item) =>
        this.checkDataItemTag(item, Types.stuName)
      );
      const [stuTelItem = {}] = dataList.filter((item) =>
        this.checkDataItemTag(item, Types.stuTel)
      );
      const [dateTimeItem = {}] = dataList.filter((item) =>
        this.checkDataItemTag(item, Types.birth)
      );
      // 这里需要注意的是，地址可以不止是一个，需要根据保留字段即edu_stuAddress以及数据类型是否为3或者6来判断
      const addrItems = dataList.filter(
        (item) =>
        this.checkDataItemTag(item, Types.addr) ||
        item.itemType === ProfileEnums.address
      );
      const provinceItems = dataList.filter(
        (item) => item.itemType === ProfileEnums.province
      );

      const data = {
        featureAlias,
        stuName: form[stuNameItem.itemId] || '',
        stuTel: form[stuTelItem.itemId] || '',
        smsCaptcha,
        checkCaptcha: stuTelItem.feNeedCaptcha && smsStock > 0,
        // eslint-disable-next-line wrap-iife
        regInfo: (function () {
          const regInfo = [];
          Object.keys(form).forEach((itemId) => {
            const _itemId = Number(itemId);
            let _itemValue = form[itemId];
            if (
              Array.isArray(_itemValue) &&
              Array.isArray(addrItems) &&
              addrItems.findIndex((item) => item.itemId === _itemId) > -1
            ) {
              // 详细地址
              const addrArr = [..._itemValue];
              if (addr) {
                addrArr.push({
                  code: '0',
                  name: addr,
                });
              }
              _itemValue = addrArr.length === 4 ? JSON.stringify(addrArr) : '';
            }
            if (_itemId === dateTimeItem.itemId) {
              // 日期
              const timestamp = new Date(_itemValue).valueOf();
              _itemValue = Number.isNaN(timestamp) ?
                _itemValue :
                String(timestamp);
            }
            if (
              _itemValue &&
              Array.isArray(provinceItems) &&
              provinceItems.findIndex((item) => item.itemId === _itemId) > -1
            ) {
              // 省市区
              _itemValue = JSON.stringify(_itemValue);
            }
            regInfo.push({
              itemId: _itemId,
              itemValue: _itemValue,
            });
          });
          return regInfo;
        })(),
      };
      this.validate()
        .then(() => {
          this.getStudentDataItems(componentData?.editorState)
            .then(() => {
              const {
                dataList
              } = this.data;
              dataList.forEach((item) => {
                const {
                  itemId,
                  itemName,
                  itemType,
                  hasChangedName,
                  itemValueList: currentItemValue,
                  feItemCustomName,
                } = item;
                const currentItemName =
                  !hasChangedName ? itemName : feItemCustomName;
                const regInfoConfig = data.regInfo.filter(
                  (info) => info?.itemId === itemId
                )[0];
                let {
                  itemValue
                } = regInfoConfig || {};
                if (itemType === 8) {
                  itemValue = itemValue.split(',');
                }
                const params = {
                  itemId,
                  itemType,
                  itemValue,
                  currentItemValue,
                  currentItemName,
                };
                this.validateSingleAndMultipleOption(params);
              });
              if (!this.data.hasChangedOptions) {
                this.setYZData({
                  loading: {
                    ...this.data.loading,
                    submit: true,
                  },
                });
                this.createRegistration(data);
              }
            })
        })
        .catch((err) => {
          Toast(err);
        });
    },

    /**
     * 是否是某个资料项
     * 学员姓名 edu_stuName
     * 学员头像 edu_stuAva
     * 学员生日 edu_stuBirth
     * 学员年龄 edu_stuAge
     * 学员性别 edu_stuSex
     * 联系人手机 edu_stuContractPhone
     * 联系人微信号 edu_stuContractWeChat
     * 学员联系地址 edu_stuAddress
     * 学员年级 edu_stuGrade
     *
     * @param {Object} item 资料项
     * @param {string} targetTag 具体某个资料项的tag
     */
    checkDataItemTag(item, targetTag) {
      const {
        itemTags = []
      } = item;
      return itemTags.some((tag) => tag === targetTag);
    },

    /**
     * 重置imgUploader文件
     */
    initFiles() {
      /*
      const { imgUploader = [] } = this.$refs;
      if (imgUploader.length > 0) {
        imgUploader[0].files = [];
      }
      */
    },

    /**
     * 重置发送验证码
     */
    initSendCaptcha() {
      if (this.data.captchaTimer) {
        clearTimeout(this.data.captchaTimer);
      }
      this.setYZData({
        captchaTimeout: 0,
      });
    },

    /**
     * 针对单选项和多选项特殊场景校验：C端用户停留在微页面，商家修改选项内容
     * 如果用户填写的资料项选项的itemValue在接口获取到的最新的学员资料项itemValue中不存在，则说明已经被删除，弹出提示
     * @param {array} itemValue 用户填写的资料项选项数组
     * @param {array} currentItemValue 最新资料项选项数组
     * @param {string} currentItemName 删除选项资料项的名字
     */
    validateSingleAndMultipleOption(params) {
      const {
        itemId,
        itemType,
        itemValue,
        currentItemValue,
        currentItemName,
      } = params;
      // 多选
      if (itemType === 8) {
        itemValue.forEach((val) => {
          if (!currentItemValue.includes(val)) {
            Toast({
              duration: 2000,
              message: `${currentItemName}的选项被修改，请重新选择`,
            });
            this.setYZData({
              form: {
                ...this.data.form,
                [itemId]: '',
              },
              hasChangedOptions: true,
            });
          }
        });
      } else if (itemType === 7) {
        // 单选
        if (!currentItemValue.includes(itemValue)) {
          Toast({
            duration: 2000,
            message: `${currentItemName}的选项被修改，请重新选择`,
          });
          this.setYZData({
            hasChangedOptions: true,
          });
        }
      } else {
        this.setYZData({
          hasChangedOptions: false,
        });
      }
    },

    /**
     * 校验提交表单
     */
    validate() {
      let hasError = false;
      let errorMsg = '请正确填写表单后再提交';

      const {
        form,
        smsCaptcha,
        addr,
        smsStock,
        dataList
      } = this.data;
      dataList.forEach((item) => {
        const {
          itemId,
          itemName,
          itemType,
          feRequired,
          hasChangedName,
          feItemCustomName,
        } = item;
        const currentItemName =
          !hasChangedName ? itemName : feItemCustomName;

        if (!hasError && feRequired) {
          if (!form[itemId]) {
            hasError = true;
            errorMsg = `${currentItemName}是必填项`;
          } else if (itemType === 9 && !/^1\d{10}$/.test(form[itemId])) {
            hasError = true;
            errorMsg = `${currentItemName}格式错误，请重新输入`;
          } else if (
            itemType === 9 &&
            item.feNeedCaptcha &&
            smsStock > 0 &&
            !smsCaptcha
          ) {
            // 手机号
            hasError = true;
            errorMsg = '请输入验证码';
          } else if (itemType === 6 && !addr) {
            // 详细地址
            hasError = true;
            errorMsg = '请输入详细地址';
          } else if (itemType === 5 && !form[itemId]) {
            hasError = true;
            errorMsg = '请上传图片';
          }
        }

        if (!hasError && !feRequired && itemType === 6) {
          // 详细地址
          const bool = !((form[itemId] && addr) || (!form[itemId] && !addr));
          if (bool) {
            hasError = true;
            errorMsg = '地址格式错误';
          }
        }
      });
      return hasError ? Promise.reject(errorMsg) : Promise.resolve();
    },

    confirmMultiSelect(e) {
      const {
        itemId,
        itemType,
        itemValueList
      } = e.currentTarget.dataset.item;
      const data =
        itemType === 8 && this.data.hasChangedOptions ?
        e.detail.filter((item) => itemValueList.includes(item)) :
        e.detail;
      this.setYZData({
        form: {
          ...this.data.form,
          [itemId]: data.join(','),
        },
      });
    },
  },
});
