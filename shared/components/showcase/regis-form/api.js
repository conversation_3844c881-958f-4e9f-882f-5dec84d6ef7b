import { node as request } from 'shared/utils/request';

export default {
  /**
   * 检测店铺短信剩余数量
   * @return {Promise}
   */
  checkSmsStock() {
    return request({
      method: 'GET',
      path: '/wscshop/showcase/knowledge/checkSmsStock.json',
    });
  },
  /**
   * 发送验证码
   * @param {string} mobile
   * @return {Promise}
   */
  createSendTelSmsCaptcha(mobile) {
    return request({
      method: 'POST',
      path: '/wscshop/showcase/knowledge/createSendTelSmsCaptcha.json',
      data: {
        mobile,
      },
    });
  },

  /**
   * 提交表单
   * @param {object} data
   * @return {Promise}
   */
  createRegistrationInfo(data) {
    return request({
      method: 'POST',
      path: '/wscshop/showcase/knowledge/createRegistrationInfo.json',
      data,
    });
  },

  /**
   * C端查询学员资料项列表
   * @param {Array.<Array>} itemIds[] - 需要获取的学员资料项id，逗号相隔
   * @param {Object} IEnrollDataItemQueryDTO - 微页面报名表单相关的资料项QueryDTO
   * @return {Promise}
   */
  findDataItems(data) {
    return request({
      method: 'POST',
      path: '/wscshop/showcase/knowledge/findDataItems.json',
      data,
    });
  },
};
