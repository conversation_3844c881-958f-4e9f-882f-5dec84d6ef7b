import WscComponent from 'shared/common/base/wsc-component/index';
import Theme from 'shared/common/components/theme-view/theme';
import getSystemInfo from 'shared/utils/browser/system-info';

const { windowHeight } = getSystemInfo();

WscComponent({
  properties: {
    prefix: {
      type: String,
      value: 'se__field',
    },
    label: String,
    errorConf: {
      type: Object,
      value: () => ({
        isError: false,
        errorMsg: '',
      }),
    },
    options: {
      type: Array,
      value: () => ([]),
    },
    placeholder: {
      type: String,
      value: '请选择',
    },
    value: {
      type: String,
      value: '',
    }
  },

  data: {
    selectedTexts: [],
    showSelector: false,
    stateValue: '',
    themeMainBgColor: '',
    popupStyle: `
      min-height: ${windowHeight * 0.5 - 94}px;
      max-height: ${windowHeight * 0.8 - 94}px
    `,
  },

  observers: {
    value(val) {
      if (val === '' && this.data.stateValue) {
        this.setYZData({ selectedTexts: [], stateValue: [] });
      }
    },
  },

  methods: {
    handleValueChange(event) {
      this.setYZData({
        stateValue: event.detail
      });
    },
    toggleSelector() {
      this.setYZData({ showSelector: !this.data.showSelector });
    },
    getPlaceHolder() {
      console.log('placeholder');
      return 'placeholder';
    },
    handleCancel() {
      let value = this.data.value.split(',');
      if (this.data.value === '') {
        value = [];
      }
      this.toggleSelector();
      this.setYZData({ stateValue: value });
      this.handleValueChange({ detail: value });
    },
    handleConfirm() {
      this.setYZData({
        selectedTexts: this.data.stateValue,
      });
      this.toggleSelector();
      this.triggerEvent('confirm', this.data.stateValue);
    },
  },

  created() {
    Theme.getThemeColor('main-bg').then(color => {
      this.setYZData({
        themeMainBgColor: color
      });
    });
  },
});
