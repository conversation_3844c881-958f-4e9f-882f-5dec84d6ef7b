.multi-select {
  box-sizing: border-box;
  background-color: #fff;
  border-bottom: 1px solid #eee;

  .se-icon.van-icon {
    position: absolute;
    right: 15px;
    font-size: 10px;
    transform: scale(.8);
  }

  .input {
    flex: 1;
    position: relative;
    display: flex;
    padding: 10px 15px;
    align-items: center;
    font-size: 14px;

    .se_field-label {
      min-width: 90px;
      color: #666;
    }

    van-icon {
      position: absolute;
      right: 15px;
      font-size: 16px;
    }

    .se-icon {
      margin-top: 2px;
      color: #999;
    }

    .multi-values {
      display: flex;
      flex-wrap: wrap;

      &__item {
        padding: 2px 5px;
        margin: 5px 8px 0 0;
        font-size: 14px;
        line-height: 16px;
        color: #fff;
        background-color: #666;
        border-radius: 3px;
      }
    }
  }
}

.vis-ui-placeholder {
  color: #999;
}

.popup {
  flex-direction: column;
  padding-bottom: 15px;
  border-radius: 12px 12px 0 0;

  .options {
    &-header {
      position: relative;
      height: 44px;
      line-height: 44px;
      text-align: center;

      van-icon {
        position: absolute;
        right: 16px;
        color: #969799;
        top: 0;
        bottom: 0;
        margin: auto 0;
      }
    }

    &-container {
      display: flex;
      flex-direction: column;
      overflow-y: scroll;
    }
  }

  van-checkbox {
    height: 48px;
    padding: 14px;
    box-sizing: border-box;
  }

  .btn-container {
    display: flex;
    flex: 1;
    padding: 7px 16px;

    van-button {
      width: 100%;
    }

    .van-button {
      width: 100%;
      height: 36px;
      line-height: 36px;
      border-radius: 18px;
      color: inherit;
      border: none;
      background: transparent;
    }
    
    theme-view {
      width: 100%;
      border-radius: 18px;
      overflow: hidden;
    }
  }

}
