<view>
  <view>
    <view class="panel multi-select">
      <view class="input" bindtap="toggleSelector">
        <label class="se_field-label van-cell__title">
          {{ label }}
        </label>
        <view wx:if="{{ selectedTexts.length !== 0 }}" class="multi-values">
          <label
            wx:for="{{ selectedTexts }}"
            wx:key="text"
            wx:for-item="text"
            class="multi-values__item"
          >
            {{ text }}
          </label>
        </view>
        <span wx:else class="vis-ui-placeholder">{{ placeholder }}</span>
        <van-icon class="se-icon" name="arrow" />
      </view>
    </view>
    <view wx:if="{{ errorConf.isError }}" class="{{ prefix }}-panel panel error">
      <label class="se__field-label van-cell__title" />
      {{ errorConf.errorMsg }}
    </view>
    <van-popup
      show="{{ showSelector }}"
      class="popup"
      z-index="10000"
      position="bottom"
      custom-style="border-radius: 12px 12px 0 0;"
      bind:click-overlay="handleCancel"
    >
      <view class="options-header">
        请选择
        <van-icon name="cross" bind:click="handleCancel" />
      </view>
      <van-checkbox-group
        wx:if="{{ options.length }}"
        value="{{ stateValue }}"
        class="options-container"
        style="{{ popupStyle }}"
        bind:change="handleValueChange"
      >
        <van-checkbox
          wx:for="{{ options }}"
          wx:for-item="opt"
          wx:key="{{ index }}"
          name="{{ opt }}"
          icon-size="18px"
          checked-color="{{ themeMainBgColor }}"
        >
          {{ opt }}
        </van-checkbox>
      </van-checkbox-group>
      <p wx:else class="no-options">没有可用的选项</p>
      <view class="btn-container">
        <theme-view
          bg="main-bg"
          color="main-text"
          border="main-bg">
          <van-button
            custom-class="van-button"
            custom-style="width: 100%"
            type="primary"
            bind:click="handleConfirm"
          >
            确定
          </van-button>
        </theme-view>
      </view>
    </van-popup>
  </view>
</view>
