<wxs src="./util.wxs" module="util" />

<view>
  <form-header
    img-url="{{props.cover}}"
    title="{{props.title}}"
    desc="{{props.description}}"
  />
  <view class="{{ prefix }}">
    <view class="{{ prefix }}__main">
      <van-cell-group>
        <view
          wx:for="{{ dataList }}"
          wx:key="alias"
          wx:for-item="item"
        >
          <!-- 手机号 -->
          <view wx:if="{{ item.itemType === 9 }}">
            <van-field
              bind:input="onInput"
              class="{{ form[item.itemId] !== '' ? prefix + '__field--black' : '' }}"
              custom-style="flex: 0 0 90px;color: #666;"
              data-id="{{ item.itemId }}"
              label="{{ !item.hasChangedName ? item.itemName : item.feItemCustomName }}"
              placeholder="{{ util.getPlaceholder(item.feRequired) }}"
              type="number"
              value="{{ form[item.itemId] }}"
            />
            <van-field
              bind:input="onInputSmsCaptcha"
              custom-style="flex: 0 0 90px;color: #666;"
              wx:if="{{ item.feNeedCaptcha && smsStock > 0 }}"
              value="{{ smsCaptcha }}"
              label="验证码"
              placeholder="{{ util.getPlaceholder(item.feRequired) }}"
              use-button-slot
            >
              <van-button
                plain
                slot="button"
                type="danger"
                custom-class="{{ prefix }}__getCaptcha"
                loading="{{ loading.captcha }}"
                loading-text="发送中"
                bind:click="sendCaptcha"
              >
                {{ captchaTimeout === 0 ? '获取验证码' : captchaTimeout + 's' }}
              </van-button>
            </van-field>
          </view>
          <!-- 多选 -->
          <view wx:elif="{{ item.itemType === 8 }}">
            <multi-select-type
              custom-style="flex: 0 0 90px;color: #666;"
              itemId="{{ item.itemId }}"
              value="{{ form[item.itemId] }}"
              label="{{ !item.hasChangedName ? item.itemName : item.feItemCustomName }}"
              class="{{ form[item.itemId] !== '' ? prefix + '__field--black' : '' }}"
              placeholder="{{ util.getPlaceholder(item.feRequired, 'select') }}"
              data-item="{{ item }}"
              options="{{ item.itemValueList }}"
              bindconfirm="confirmMultiSelect"
            />
          </view>
          <!-- 单选项 -->
          <view wx:elif="{{ item.itemType === 7 }}">
            <van-field
              custom-style="flex: 0 0 90px;color: #666;"
              value="{{ form[item.itemId] }}"
              label="{{ !item.hasChangedName ? item.itemName : item.feItemCustomName }}"
              class="{{ form[item.itemId] !== '' ? prefix + '__field--black' : '' }}"
              is-link
              placeholder="{{ util.getPlaceholder(item.feRequired, 'select') }}"
              disabled
              data-item="{{ item }}"
              bindtap="openRadioActionsheet"
            />
          </view>
          <!-- 地址（包括省市县和详细地址） -->
          <view wx:elif="{{ item.itemType === 6 }}">
            <van-field
              custom-style="flex: 0 0 90px;color: #666;"
              value="{{ convertedAddress[item.itemId] }}"
              class="{{ prefix }}__cityWithAddr {{ form[item.itemId] !== '' ? prefix + '__field--black' : '' }}"
              wx:key="itemId"
              label="{{ !item.hasChangedName ? item.itemName : item.feItemCustomName }}"
              is-link
              placeholder="{{ item.feRequired ? '' : '(选填)' }}省/市/区"
              disabled
              data-item="{{ item }}"
              bindtap="openAreaActionsheet"
            />
            <van-field
              custom-style="flex: 0 0 90px;color: #666;"
              value="{{ addr }}"
              autosize
              rows="2"
              label=" "
              placeholder="{{ item.feRequired ? '' : '(选填)' }}门牌号、楼栋号、单元室等"
              data-id="{{ item.itemId }}"
              bind:input="onAddrInput"
            />
          </view>
          <!-- 学员头像 -->
          <view wx:elif="{{ item.itemType === 5 }}">
            <van-cell
              class="{{ prefix }}__imgUploader"
            >
              <view class="imgUploader__label">{{ !item.hasChangedName ? item.itemName : item.feItemCustomName }}
                <span wx:if="{{ !item.feRequired }}">（选填）</span>
              </view>
              <view
                class="visImgUploader"
              >
                <view class="visImgUploader__uploaderWrap">
                  <van-icon
                    class="visImgUploader__uploaderWrap__deleteIcon"
                    wx:if="{{ form[item.itemId] }}"
                    name="clear"
                    bind:click="deleteImage"
                    data-id="{{ item.itemId }}"
                  />
                  <view class="van-uploader" bindtap="uploadImage" data-id="{{ item.itemId }}">
                    <image
                      style="width: 76px;height: 76px;"
                      mode="aspectFit"
                      wx:if="{{ form[item.itemId] }}"
                      src="{{ form[item.itemId] }}"
                    />
                    <view wx:else>
                      <van-loading wx:if="{{ loading.upload }}" />
                      <van-icon wx:else custom-class="van-icon-photograph" name="photograph" />
                    </view>
                  </view>
                </view>
              </view>
            </van-cell>
          </view>
          <!-- 学员性别 -->
          <view wx:elif="{{ item.itemType === 4 }}">
            <van-cell title-class="van-cell__title">
              <span
                custom-class="van-cell__text"
                class="van-cell__text"
                slot="title"
              >
                {{ !item.hasChangedName ? item.itemName : item.feItemCustomName }}
              </span>
              <van-radio-group
                data-id="{{ item.itemId }}"
                value="{{ form[item.itemId] }}"
                bind:change="chooseGenderRadio"
              >
                <van-radio
                  checked-color="#f44"
                  wx:for="{{ item.itemValueList }}"
                  wx:for-item="radioItem"
                  wx:key="*this"
                  name="{{ radioItem }}"
                >
                  {{ radioItem }}
                </van-radio>
              </van-radio-group>
            </van-cell>
          </view>
          <!-- 省市县 -->
          <view wx:elif="{{ item.itemType === 3 }}">
            <van-field
              custom-style="flex: 0 0 90px;color: #666;"
              value="{{ convertedAddress[item.itemId] }}"
              label="{{ !item.hasChangedName ? item.itemName : item.feItemCustomName }}"
              class="{{ form[item.itemId] !== '' ? prefix + '__field--black' : '' }}"
              is-link
              placeholder="{{ item.feRequired ? '' : '(选填)' }}省/市/区"
              disabled
              data-item="{{ item }}"
              bindtap="openAreaActionsheet"
            />
          </view>
          <!-- 学员生日 -->
          <view wx:elif="{{ item.itemType === 2 }}">
            <van-field
              custom-style="flex: 0 0 90px;color: #666;"
              value="{{ form[item.itemId] }}"
              label="{{ !item.hasChangedName ? item.itemName : item.feItemCustomName }}"
              class="{{ form[item.itemId] !== '' ? prefix + '__field--black' : '' }}"
              is-link
              placeholder="{{ util.getPlaceholder(item.feRequired, 'select') }}"
              disabled
              data-item="{{ item }}"
              bindtap="openDateActionsheet"
            />
          </view>
          <!-- 数字 -->
          <view wx:elif="{{ item.itemType === 1 }}">
            <van-field
              custom-style="flex: 0 0 90px;color: #666;"
              data-id="{{ item.itemId }}"
              value="{{ form[item.itemId] }}"
              type="number"
              label="{{ !item.hasChangedName ? item.itemName : item.feItemCustomName }}"
              placeholder="{{ util.getPlaceholder(item.feRequired) }}"
              bind:input="onInput"
            />
          </view>
          <!-- 文本 -->
          <view wx:elif="{{ item.itemType === 0 }}">
            <van-field
              custom-style="flex: 0 0 90px;color: #666;"
              data-id="{{ item.itemId }}"
              value="{{ form[item.itemId] }}"
              label="{{ !item.hasChangedName ? item.itemName : item.feItemCustomName }}"
              disabled="{{ item.disabled }}"
              placeholder="{{ item.placeholder ? item.placeholder : util.getPlaceholder(item.feRequired) }}"
              bind:input="onInput"
            />
          </view>
        </view>
      </van-cell-group>
    </view>

    <form-button bindtap="submit" business-module="owlMiniProgram">
      <view class="{{ prefix }}__btn">
        <van-button
          custom-class="van-button"
          type="danger"
          size="large"
          color="{{themeGeneral}}"
          loading-text="提交中"
          loading="{{ loading.submit }}"
        >
          {{ submitText }}
        </van-button>
      </view>
    </form-button>

    <!-- 单选项(学员年级)弹出层 -->
    <van-popup
      class="{{ prefix }}__radioActionsheet"
      show="{{ visible.radio }}"
      z-index="10000"
      position="bottom"
      bind:click-overlay="cancelRadioActionsheet"
      custom-style="border-radius: 12px 12px 0 0;"
    >
      <view class="options-header">
        请选择
        <van-icon name="cross" bind:click="cancelRadioActionsheet" />
      </view>
      <view class="radioActionsheet__wrap" style="{{ popupStyle }}">
        <view class="radioActionsheet__bd">
          <view
            wx:key="itemId"
            class="radioActionsheet__item {{ item === current.radioValue ? 'radioActionsheet__item--active': '' }}"
            wx:for="{{ current.radioValueList }}"
            wx:for-item="item"
            data-item="{{ item }}"
            bindtap="confirmRadioActionsheet"
          > 
            <theme-view
              color="main-text"
              bg="{{ item === current.radioValue ? 'main-bg' : '' }}"
              border="{{ item === current.radioValue ? 'main-bg' : '' }}"
              custom-class="radioActionsheet__checkbox"
            >
              <van-icon custom-class="van-icon-success" name="success" wx:if="{{ item === current.radioValue }}" />
            </theme-view>
            <span>{{ item }}</span>
          </view>
        </view>
        <!-- <div :class="`radioActionsheet__ft`">
          <van-button
            type="primary"
            size="large"
            @click="() => {
              form[current.itemId] = current.radioValue;
              visible.radio = false;
            }"
          >
            确定
          </van-button>
        </div> -->
      </view>
    </van-popup>

    <!-- 日期弹弹出层 -->
    <van-popup
      class="{{ prefix }}__datePopup"
      position="bottom"
      z-index="10000"
      show="{{ visible.date }}"
      bind:close="cancelDate"
    >
      <van-datetime-picker
        value="{{ current.dateValue }}"
        type="date"
        min-date="{{ minDate }}"
        max-date="{{ maxDate }}"
        bind:confirm="confirmDate"
        bind:cancel="cancelDate"
      />
    </van-popup>

    <!-- 省市县弹出层 -->
    <van-action-sheet
      class="{{ prefix }}__areaActionsheet"
      show="{{ visible.area }}"
      z-index="10000"
      close-on-click-overlay="{{ true }}"
      bind:close="cancelArea"
    >
      <van-area
        area-list="{{ areaList }}"
        loading="{{ loading.area }}"
        value="{{ current.areaCode }}"
        bind:confirm="confirmArea"
        bind:cancel="cancelArea"
      />
    </van-action-sheet>
  </view>
</view>
