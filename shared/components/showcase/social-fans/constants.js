export const SUB_TYPE = {
  group: '1',
  official: '2',
};

export const LOGO_TYPE = {
  default: '1',
  custom: '2',
};

export const DEFAULT_BACKGROUND = '#f9f9f9';

export const DEFAULT_LOGO = 'https://img01.yzcdn.cn/upload_files/2019/04/22/FtUMoXQ36AQluQJnJodiR-pb-4rL.jpg';

export const PLACEHOLDER_BTN_NAME = {
  group: '立即进群',
  person: '立即添加',
};

export const PLACEHOLDER_TITLE = {
  group: '微信群名或者活动标题',
  person: '个人微信号、群名称或活动标题',
};

export const SCENE = {
  WeiXin: 'WeiXin',
  WeiXinGroup: 'WeiXinGroup',
};

export const SCENE_WHITE_LIST = [1011, 1047, 1089, 1038];

export const MSG_IMG = {
  member: 'https://img01.yzcdn.cn/weapp/wsc/1c9OGo.png',
  group: 'https://img01.yzcdn.cn/weapp/wsc/4RYyjww.png',
  official: 'https://img01.yzcdn.cn/weapp/wsc/UJ60enB.png'
};

export const MSG_TITLE = {
  member: '点击添加微信',
  group: '点击加入微信群',
  official: '点击关注公众号'
};

export const MSG_DESC = {
  member: '添加微信，获取更多优惠信息及服务',
  group: '加入微信群，获取更多优惠信息及服务',
  official: '关注公众号，获取更多优惠信息及服务',
};

// path没实际用途，是和后端的约定路径
export const BASE_SHARE_PATH = '/pages/home/<USER>/index';

export const H5_LOGO = 'https://img01.yzcdn.cn/weapp/wsc/1fxuP9X.png';
