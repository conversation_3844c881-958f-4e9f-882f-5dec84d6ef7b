/**
 * 社群涨粉组件
 * <AUTHOR>
 * type: social_fans || official_account
 */
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import navigate from 'shared/utils/navigate';
import { mapState } from '@youzan/vanx';
import Args from '@youzan/weapp-utils/lib/args';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import componentBehavior from '../behaviors/component-behavior';
import buildUrl from '@youzan/utils/url/buildUrl';
import transform from '../utils/transformComponentData';
import appLoggerBehavior from '../behaviors/app-logger-behavior';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
import loggerBehavior from '../behaviors/logger-behavior';
import compareVersion from 'shared/utils/compare-version';
import { getJumpLogic } from './api';
import { componentsPickParams } from 'shared/utils/log-params';

import {
  SUB_TYPE,
  SCENE_WHITE_LIST,
  MSG_IMG,
  LOGO_TYPE,
  SCENE,
  H5_LOGO,
  MSG_TITLE,
  MSG_DESC,
  BASE_SHARE_PATH,
} from './constants';

const app = getApp();

VanxComponent({
  behaviors: [componentBehavior, loggerBehavior, appLoggerBehavior],

  mapData: {
    ...mapState('/', {
      teamLogo: (state) => cdnImage(state.shop.logo, '!160x160.jpg'),
      teamName: (state) => state.shop.shop_name,
    }),
  },

  data: {
    SUB_TYPE,
    MSG_IMG,
    LOGO_TYPE,
    emptyTitle: ' ', // 占位不显示标题
    canUseOfficialAccount: false,
    isJumpH5: false,
    publicNumberPath: '',
  },

  attached() {
    this.getPublicLink();
    const { kdtId, componentData } = this.data;
    const config = transform(componentData, {
      booleanKeys: ['isWeappSupport', 'isWeappContactSupport'],
    });

    // 兼容老数据
    if (config.type === 'official_account') {
      config.isWeappSupport = true;
      config.isWeappContactSupport = true;
      config.subType = SUB_TYPE.official;
    }
    config.logo = cdnImage(config.logo, '!260x0.jpg');
    config.customLogo = cdnImage(config.customLogo, '!260x0.jpg');
    this.setData({
      config,
      msgImg: this.getMsgConfig(config).img,
      messagePath: this.getMessagePath(config, kdtId),
      canUseOfficialAccount: this.getCanUseOfficialAccount(),
      isJumpH5: this.checkWxVersion(),
    });
  },

  methods: {
    onContactBack(data) {
      this.onTap();
      navigate.contactBack(data);
    },

    onTap() {
      const { componentData } = this.data;
      const { type } = componentData;
      const pages = getCurrentPages();
      const cloudLoggerInfo = getLoggerExtraParams(type, {
        page_url: pages[pages.length - 1].route,
      });
      const otherLogger = componentsPickParams[type](componentData) || {};
      const extra = {
        banner_id: this.getBannerId(1),
        ...otherLogger,
      };
      const loggerParams = {
        ...extra,
        ...cloudLoggerInfo,
      };

      this.ensureAppLogger('click_content', loggerParams);
    },

    getMessagePath(config, kdtId) {
      const msgConfig = this.getMsgConfig(config);
      const urlParams = {
        yz_live_code_link: this.getH5Link(config, kdtId),
        yz_live_code_image: encodeURIComponent(H5_LOGO),
        yz_live_code_desc: msgConfig.desc,
        yz_live_code_title: msgConfig.title,
      };

      return Args.add(BASE_SHARE_PATH, urlParams);
    },

    getCanUseOfficialAccount() {
      const { scene } = wx.getLaunchOptionsSync();

      return SCENE_WHITE_LIST.some((item) => item === +scene);
    },

    getH5Link(config, kdtId) {
      const { subType } = config;
      let link = '';

      if (subType === SUB_TYPE.group) {
        link = this.getLiveCodeLink(config, kdtId);
      } else {
        link = this.getOfficialLink(kdtId);
      }

      return encodeURIComponent(link);
    },

    getLiveCodeLink(config, kdtId) {
      const { qrcode: activitiesId, scene } = config;

      const type = scene === SCENE.WeiXinGroup ? 'group' : 'member';

      return `https://h5.youzan.com/v3/message/live-qrcode/${type}?kdtId=${kdtId}&activitiesId=${activitiesId}&source=zhangfen`;
    },

    getOfficialLink(kdtId) {
      return `https://h5.youzan.com/wscshop/officialAccountQrcode?kdtId=${kdtId}&source=zhangfen`;
    },

    getMsgConfig(config) {
      const { subType, scene } = config;
      let type = 'member';

      if (subType === SUB_TYPE.official) {
        type = 'official';
      } else if (scene === SCENE.WeiXinGroup) {
        type = 'group';
      }

      return {
        title: encodeURIComponent(MSG_TITLE[type]),
        desc: encodeURIComponent(MSG_DESC[type]),
        img: MSG_IMG[type],
      };
    },

    handleOfficialAccountLoadError() {
      this.setData({
        canUseOfficialAccount: false,
      });
    },

    handleButtonClick() {
      const h5QrCodeUrl = this.getH5QrCodeLink();
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(
          h5QrCodeUrl
        )}`,
      });
    },

    getH5QrCodeLink() {
      const { qrcode } = this.data.config || {};
      const kdtId = app.getKdtId();
      return buildUrl(
        Args.add('/v3/message/live-qrcode/member', {
          kdtId,
          activitiesId: qrcode,
        }),
        'h5',
        kdtId
      );
    },

    // 微信8.0.8及以上版本加微信群/个人微信号可以跳转H5，微信8.0.8以下版本仍保持现状跳转客服
    checkWxVersion() {
      const { version } = wx.getSystemInfoSync();
      const result = compareVersion(version, '8.0.8');
      if (result === -1) return false;
      return true;
    },

    getPublicLink() {
      getJumpLogic().then((result) => {
        if (result) {
          const wxArticle =
            result.wx_attention_article &&
            JSON.parse(result.wx_attention_article);
          const { url } = wxArticle || {};
          this.setData({
            publicNumberPath: url,
          });
        }
      });
    },

    handleJumpPublic() {
      if (this.data.publicNumberPath) {
        const timestamp = Date.parse(new Date());
        const articleUrl = `${this.data.publicNumberPath}?t=${timestamp}`;
        navigate.navigate({
          url: `/pages/common/webview-page/index?src=${encodeURIComponent(
            articleUrl
          )}`,
        });
      }
    },
  },
});
