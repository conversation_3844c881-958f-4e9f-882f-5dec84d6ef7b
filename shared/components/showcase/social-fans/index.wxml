<view class="cap-social-fans">
  <button
    wx:if="{{ config.subType === SUB_TYPE.group && !isJumpH5 }}"
    class="cap-social-fans__button"
    open-type="contact"
    send-message-title="{{ emptyTitle }}"
    show-message-card="{{ true }}"
    send-message-path="{{ messagePath }}"
    send-message-img="{{ msgImg }}"
    session-from="{{ extraData.sourceParam || '' }}"
    business-id="{{ extraData.businessId || '' }}"
    bind:contact="onContactBack"
  >
    <template is="socialFansGroup" data="{{ config, LOGO_TYPE }}"></template>
  </button>

  <button
    wx:elif="{{ config.subType === SUB_TYPE.group && isJumpH5 }}"
    class="cap-social-fans__button"
    bind:tap='handleButtonClick'
  >
    <template is="socialFansGroup" data="{{ config, LOGO_TYPE }}"></template>
  </button>

  <cap-official-account
    wx:elif="{{ config.isWeappSupport && canUseOfficialAccount }}"
    bind:loadError="handleOfficialAccountLoadError"
  />

  <view
    wx:elif="{{ config.isWeappContactSupport && publicNumberPath }}"
    bindtap = "handleJumpPublic"
  >
    <view class="cap-social-fans__official">
      <view class="cap-social-fans__left">
        <view class="cap-social-fans__tag">{{ teamName }}的公众号</view>
        <image mode="aspectFill" class="cap-social-fans__logo" src="{{ teamLogo }}" ></image>
        <view class="cap-social-fans__cnt">
          <view class="cap-social-fans__title">{{ teamName }}</view>
        </view>
      </view>
      <view class="cap-social-fans__right">
        <view class="cap-social-fans__btn">关注</view>
      </view>
    </view>
  </view>

  <button
    wx:elif="{{ config.isWeappContactSupport && !publicNumberPath }}"
    class="cap-social-fans__button"
    open-type="contact"
    send-message-title="{{ emptyTitle }}"
    show-message-card="{{ true }}"
    send-message-path="{{ messagePath }}"
    send-message-img="{{ msgImg }}"
    session-from="{{ extraData.sourceParam || '' }}"
    business-id="{{ extraData.businessId || '' }}"
    bind:contact="onContactBack"
  >
    <view class="cap-social-fans__official">
      <view class="cap-social-fans__left">
        <view class="cap-social-fans__tag">{{ teamName }}的公众号</view>
        <image mode="aspectFill" class="cap-social-fans__logo" src="{{ teamLogo }}" ></image>
        <view class="cap-social-fans__cnt">
          <view class="cap-social-fans__title">{{ teamName }}</view>
        </view>
      </view>
      <view class="cap-social-fans__right">
        <view class="cap-social-fans__btn">关注</view>
      </view>
    </view>
  </button>
</view>

<template name="socialFansGroup">
  <view
    class="cap-social-fans__group" style="background-color: {{ config.bgColor }}"
  >
    <view class="cap-social-fans__left">
      <image mode="aspectFill"
        class="cap-social-fans__logo"
        src="{{ config.logoType === LOGO_TYPE.default ? config.logo : config.customLogo }}">
      </image>
      <view class="cap-social-fans__cnt">
        <view class="cap-social-fans__title">{{ config.title }}</view>
        <view class="cap-social-fans__desc">{{ config.desc }}</view>
      </view>
    </view>
    <view class="cap-social-fans__right">
      <view class="cap-social-fans__btn">{{ config.btnName }}</view>
    </view>
  </view>
</template>
