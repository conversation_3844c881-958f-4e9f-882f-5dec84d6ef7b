.cap-social-fans {
  &__button {
    display: block;
    border-radius: 0;
    margin: 0;
    padding: 0;

    &::after {
      border: none;
    }
  }

  &__group {
    padding: 0 15px;
  }

  &__group, &__official {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 90px;
    font-size: 12px
  }

  &__official {
    background: #fff;
    padding: 0 15px 0 10px;
    margin: 10px;
    position: relative;
    border: 1px solid #e9e9e9;

    .cap-social-fans__btn {
      background: #fff;
      color: #5a9d47;
      border-color: #5a9d47;
    }

    .cap-social-fans__left,
    .cap-social-fans__right {
      margin-top: 10px;
    }
  }

  &__tag {
    position: absolute;
    left: 10px;
    top: 5px;
    color: #b3b3b3;
    font-size: 8px;
    line-height:8px;
  }

  &__left {
    display: flex;
    width: 81%;
  }

  &__cnt {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    width: 100%;
    align-items: flex-start;
  }

  &__right {
    width: 19%
  }

  &__logo {
    display: block;
    min-width: 60px;
    width: 60px;
    height: 60px;
    margin-right: 10px
  }

  &__title {
    font-size: 14px;
    line-height: 16px;
    text-align:left;
    color: #333;
    font-weight: 700;
  }

  &__desc {
    text-align:left;
    color: #a9a9a9;
    line-height:14px;
  }

  &__desc, &__title {
    width: 70%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
  }

  &__btn {
    display: block;
    width: 60px;
    height: 26px;
    line-height: 26px;
    border: 1px solid #f44;
    border-radius: 2px;
    text-align: center;
    color: #fff;
    background: #f44;
  }
}


