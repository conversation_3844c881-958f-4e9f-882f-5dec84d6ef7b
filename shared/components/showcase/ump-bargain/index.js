import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import Args from '@youzan/weapp-utils/lib/args';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { getHelpCutData, getHelpCutBySort } from './api';
import new2captain from './adaptor';
import { getUpdateCountdownGoodsList } from '../captain-components/ump-goods-layout/helper';
import navigate from '../../../utils/navigate';

Component({
  behaviors: [componentBehavior, loggerBehavior],

  properties: {
    redirectType: {
      type: Number,
      value: 3
    },

    pageData: {
      type: Object,
      value: {},
    },

    pageUtils: {
      type: Object,
      value: {}
    },

    pageRandomString: {
      type: String,
      value: '',
      observer(newVal) {
        // 没有经过adaptor 不触发倒计时更新
        const { itemData } = this.data;

        if (!itemData.type) return;
        this.refreshCountDown(newVal);
      }
    },
  },

  data: {
    itemData: {},
    isLoading: true,
  },

  attached() {
    this.setData({
      isLoading: true
    }, () => {
      this.getLimitDiscountData();
    });
  },

  methods: {
    onPullDownRefresh() {
      this.getLimitDiscountData();
    },
    refreshCountDown(newVal) {
      const { itemData } = this.data;

      const { list = [] } = itemData;

      const prefix = 'itemData.list';
      const curGoodsList = getUpdateCountdownGoodsList(prefix, list, newVal);
      this.setData({
        ...curGoodsList,
        isComponentShow: true,
        isLoading: false
      });
    },
    getLimitDiscountData() {
      const {
        componentData = {},
        kdtId
      } = this.data;
      const {
        activityIds,
        goods = [],
        itemIds,
        goodsNum,
        accessToken = '',
        uniqueIdentifier = ''
      } = mapKeysCase.toCamelCase(componentData);
      const goodsIds = goods.map(item => item.itemId);
      if (+componentData.goods_source === 1) {
        getHelpCutData({
          kdtId,
          activityIds: activityIds.join(','),
          goodsIds: itemIds || goodsIds.join(','),
          accessToken,
          uniqueIdentifier
        }).then(res => {
          this.initListData(res);
        });
      } else if (+componentData.goods_source === 0) {
        getHelpCutBySort({
          kdtId,
          goodsNum,
          sortField: 'start_at',
          page: 1,
          accessToken,
          uniqueIdentifier
        }).then(res => {
          if (res && res.list.length > 0) {
            this.initListData(res.list);
          }
        });
      }
    },
    initListData(list) {
      const {
        componentData = {},
      } = this.data;
      const itemData = new2captain({
        ...componentData,
        goods: list
      });

      // 埋点相关逻辑
      const loggerList = [];
      itemData.list.map((item, index) => {
        const { id, alias, goodsId } = item;
        // banner_id
        const bannerId = this.getBannerId(index + 1);

        const loggerParams = {
          goods_id: goodsId,
          banner_id: bannerId,
          item_type: 'goods',
          item_id: goodsId,
          act_type: 6,
          act_id: id + '',
          component: this.getComponentLoggerType(),
          ...this.getComponentLoggerExtraParams()
        };
        loggerList.push(loggerParams);
        item.loggerParams = loggerParams;
        item.url = Args.add('/packages/goods/help-cut/index', {
          alias, type: 'helpcut', activityId: id, banner_id: bannerId, ...this.getComponentLoggerExtraParams()
        });
        return item;
      });

      this.ensureAppLogger('view', loggerList);

      this.setData({
        itemData,
        isLoading: false,
        isComponentShow: true
      });
    },
    handleGoodsBuy(e) {
      navigate.navigate({
        url: e.detail.url
      });
    }
  }
});
