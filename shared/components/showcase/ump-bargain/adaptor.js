import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

const new2captain = (data) => {
  const {
    type,
    size_type: sizeType,
    buy_btn: buyBtn,
    show_buy_btn: _showBuyButton,
    buy_btn_type: buyBtnType,
    buy_btn_express: buyBtnExpress,
    title,
    // 商品分组编辑里用的`show_title`表示是否展示商品标题
    show_title: showTitle,
    show_sub_title: showSubTitle = 1,
    price = 1, // 默认展示价格字段
    goods,
    show_stock_num: showStockNum = 1,
    show_count_down: showCountDown = 0,
    show_time_limit: showTimeLimit = 0,
    image_fill_style: imageFillStyle,
    page_margin: pageMargin = 15,
    goods_margin: goodsMargin = 10,
    text_align_type: textAlignType = 'left',
    text_style_type: textStyleType = 1,
    border_radius_type: borderRadiusType = 1,
    is_preview: isPreview,
    is_start: isStart,
    is_expired: isExpried,
    process_bar_type: processBarType = 1,
    process_bar_num_type: processBarNumType = 1,
    show_origin_price: showOriginPrice = 1,
    goods_source: goodsSource = 1,
    order_rule,
    isNew
  } = data;
  let {
    size,
    button_text: buttonText,
    display_scale: imageRatio,
  } = data;
  let showBuyButton;
  if (_showBuyButton) {
    showBuyButton = (_showBuyButton && +_showBuyButton === 1);
  } else {
    showBuyButton = +buyBtn === 1;
  }

  let buyButtonType = buyBtnType ? +buyBtnType : 1;

  // 一行三个和横向滑动老的按钮展示逻辑兼容
  const threeTypeShowBuyButton = +buyBtnExpress === 1;

  // 砍价老数据兼容
  if (order_rule !==undefined) {
    if (isNew === undefined) {
      if (+size ===2) {
        size = 3;
      }
      if (+size ===0) {
        imageRatio = 1;
      }
      buyButtonType = 3;
      buttonText = '发起砍价';
    }
  }
  const goodsList = goods.map((g) => {
    const camelGoods = mapKeysCase.toCamelCase(g);
    return {
      ...camelGoods,
      startAt: camelGoods.startAt,
      endAt: camelGoods.endAt
    };
  });

  return {
    isPreview,
    type,
    buttonText,
    list: goodsList || [],
    layout: size ? +size : 0,
    sizeType: sizeType ? +sizeType : 0,
    showTitle: +title === 1 || +showTitle === 1,
    showSubTitle: +showSubTitle === 1,
    showPrice: +price > 0,
    showBuyButton,
    buyButtonType,
    buyBtnExpress: threeTypeShowBuyButton,
    showTimeLimit: +showTimeLimit === 1,
    showCountDown: +showCountDown === 1,
    showStockNum: +showStockNum === 1,
    imageRatio: imageRatio ? +imageRatio : undefined,
    imageFillStyle: +imageFillStyle || 2,
    pageMargin: +pageMargin,
    goodsMargin: +goodsMargin,
    textAlignType,
    textStyleType: +textStyleType,
    processBarType: +processBarType,
    processBarNumType: +processBarNumType,
    borderRadiusType: +borderRadiusType,
    showOriginPrice,
    goodsSource,
    isNew
  };
};

export default new2captain;
