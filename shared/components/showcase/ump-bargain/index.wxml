<view class="showcase-limit-bargain">
  <cap-limit-bargain
    wx:if="{{ isComponentShow }}"
    app-id="{{ appId }}"
    list="{{ itemData.list }}"
    layout="{{ itemData.layout }}"
    size-type="{{ itemData.sizeType }}"
    show-title="{{ itemData.showTitle }}"
    show-sub-title="{{ itemData.showSubTitle }}"
    show-price="{{ itemData.showPrice }}"
    show-origin-price="{{ itemData.showOriginPrice }}"
    show-buy-button="{{ itemData.showBuyButton }}"
    image-ratio="{{ itemData.imageRatio }}"
    image-fill-style="{{ itemData.imageFillStyle }}"
    text-align-type="{{ itemData.textAlignType }}"
    text-style-type="{{ itemData.textStyleType }}"
    border-radius-type="{{ itemData.borderRadiusType }}"
    page-margin="{{ itemData.pageMargin }}"
    goods-margin="{{ itemData.goodsMargin }}"
    buy-button-type="{{ itemData.buyButtonType }}"
    button-text="{{ itemData.buttonText }}"
    show-stock-num="{{ itemData.showStockNum }}"
    show-count-down="{{ itemData.showCountDown }}"
    is-new="{{itemData.isNew}}"
    redirect-type="{{ redirectType }}"
    extra-data="{{ extraData }}"
    page-data="{{ pageData }}"
    page-utils="{{ pageUtils }}"
    page-random-number="{{ pageRandomNumber }}"
    bind:buy="handleGoodsBuy"
    bind:item-click="handleGoodsBuy"
  />
</view>