<view
  class="c-title {{ type == 1 ? 'c-title--wx' : '' }} {{ titleClass }}"
  style="{{ type == 0 ? 'background-color:' + backgroundColor + ';' : ''}} text-align: {{ showMethodStyle }}"
>
  <view class="c-title__main">
    <text>{{ title }}</text>
    <block wx:if="{{ type == 0 && link.title }}">
      <text class="c-title__divider">-</text>
      <cap-navigator
        link-type="{{ linkType }}"
        app-id="{{ appId }}"
        use-short-link="{{ useShortLink }}"
        short-link="{{ shortLink }}"
        path="{{ path }}"
        data-type="link"
        im="{{ extraData }}"
        bind:navigate="handleNavigate"
        bind:contactback="handleContactBack"
        custom-class="c-title__link"
      >
        {{ link.title }}
      </cap-navigator>
    </block>
  </view>
  <block wx:if="{{ type == 0 && subTitle }}">
    <view class="c-title__sub">{{ subTitle }}</view>
  </block>
  <!--微信文章样式-->
  <block wx:if="{{ type == 1 && (wxDate || wxAuthor || weappLinkData.title) }}">
    <view class="c-title__sub">
      <text class="c-title__date">{{ wxDate }}</text>
      <text class="c-title__author">{{ wxAuthor }}</text>
      <block wx:if="{{ weappLinkData.isShow }}" >
        <cap-navigator
          link-type="{{ linkType }}"
          use-short-link="{{ useShortLink }}"
          short-link="{{ shortLink }}"
          app-id="{{ appId }}"
          path="{{ path }}"
          data-type="wx"
          im="{{ extraData }}"
          bind:navigate="handleNavigate"
          bind:contactback="handleContactBack"
          custom-class="c-title__link"
        >
          {{ weappLinkData.title }}
        </cap-navigator>
      </block>
    </view>
  </block>
</view>
  
