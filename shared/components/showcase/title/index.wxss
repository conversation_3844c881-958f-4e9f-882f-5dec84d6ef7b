.c-title {
  position: relative;
  padding: 10px;
  border-bottom: 1rpx solid #e5e5e5;
  box-sizing: border-box;
  overflow: hidden;
  word-break: break-all;
}

.c-title__main {
  margin: 0;
  font-size: 18px;
  line-height: 22px
}

.c-title__sub {
  font-size: 11px;
  color: #8c8c8c;
  margin: 5px 0 0
}

.c-title__divider {
  margin: 0 10px;
  color: #999
}

.c-title__link {
  display: inline-block;
  font-size: 12px;
  color: #07d
}

.c-title__author,
.c-title__date {
  margin-right: 10px
}

.c-title--no-line {
  border-bottom: 0 none;
}

.c-title--wx .c-title__main {
  font-size: 20px;
  font-weight: bold;
  line-height: 24px;
}

.c-title--wx .c-title__sub {
  line-height: 1.6;
  font-size: 12px;
  font-weight: 400;
  margin-top: 3px
}

.c-title--wx .c-title__link {
  color: #607fa6;
}

.c-title .title-link-transparent {
  color: #07d;
}

.c-title--wx .title-link-transparent {
  color: #607fa6;
}

.title-link-transparent {
  display: inline;
  border: none;
  border-radius: 0;
  padding: 0;
  margin: 0;
  background: transparent;
  line-height: auto;
  font-size: 12px;
}

.title-link-transparent::after {
  display: none;
  visibility: hidden;
}

