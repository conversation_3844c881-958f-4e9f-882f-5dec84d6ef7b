import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { getLink } from '../utils/linkAdaptor';

const showMethodMap = {
  0: 'left',
  1: 'center',
  2: 'right'
};

Component({
  behaviors: [componentBehavior, loggerBehavior],

  data: {
    showMethodStyle: 'left',
    titleClass: '',
    weappLinkData: {
      title: '',
      url: '',
      isShow: false
    }
  },

  attached() {
    const { componentData } = this.properties;
    const itemData = mapKeysCase.toCamelCase(componentData);
    const {
      link = {},
      wxLink = {},
      titleTemplate = 0,
      title,
      subTitle,
      backgroundColor = '#fff',
      wxDate = '',
      wxAuthor = '',
      wxLinkTitle = '',
      showMethod
    } = itemData;
    const targetLink = +titleTemplate === 0 ? link : wxLink;
    const { weappLinkData = {} } = this.data;
    const { title: linkTitle = '', link_url: linkUrl = '' } = wxLink;

    weappLinkData.title = wxLinkTitle || linkTitle;
    weappLinkData.url = linkUrl;
    weappLinkData.isShow = !!(weappLinkData.title || weappLinkData.url);

    this.setData({
      ...getLink(targetLink),
      titleClass: (+titleTemplate === 1 || ['#fff', '#ffffff'].indexOf(backgroundColor) > -1) ? 'sc-title--no-line' : '',
      // 显示位置：left-巨左；center-居中；right-居右
      showMethodStyle: showMethodMap[+showMethod],
      // 显示类型：0-传统样式；1-微信图文样式
      type: titleTemplate,
      // 标题
      title,
      // 副标题
      subTitle,
      // 背景颜色
      backgroundColor,
      // 传统样式文本导航数据
      link,
      // 微信图文样式日期
      wxDate,
      // 微信图文样式作者
      wxAuthor,
      wxLink,
      weappLinkData,
    });
  },

  methods: {
    handleNavigate(e) {
      const extra = { banner_id: this.getBannerId() };
      const { type } = e.currentTarget.dataset;
      let item = {};

      switch (type) {
        case 'link':
          item = this.data.link;
          break;
        case 'wx':
          item = this.data.wxLink;
          break;
        default: break;
      }

      this.triggerEvent('jumpToLink', { ...item, extra });
    },

    handleContactBack({ detail = {} }) {
      this.triggerEvent('contactback', detail);
    }
  }
});
