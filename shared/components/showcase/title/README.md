## Title 标题

### 使用指南

在 index.json 中引入组件。

```json
{
  "usingComponents": {
    "cap-goods": "path/to/@youzan/captain-weapp/dist/title/index"
  }
}
```

### 代码演示
#### 传统样式

```html
<cap-title
  title="传统样式"
  sub-title="2017-06-07"
  link={{ link }}"
  show-method="1">
</cap-title>
```

#### 微信样式

```html
<cap-title
  title="传统样式"
  type="1"
  wx-date="2017-06-07"
  wx-author="作者"
  wx-link-title="链接"
  wx-link-type="1"
  show-method="1"
  wxLink="{{ link }}">
</cap-title>
```

### API

| 参数       | 说明      | 类型       | 默认值       | 可选值      |
|-----------|-----------|-----------|-------------|-------------|
| type | 显示类型 | `[string, number]`  | `0` |  `0`：传统样式，`1`：微信图文页样式   |
| title | 标题 | string  | - |  -   |
| subTitle | 副标题 | string  | - |  -   |
| backgroundColor | 传统样式背景颜色 | string  | `#fff` |  -   |
| showMethod | 传统样式显示方式 | `[string, number]`  | `0` |  `0`：居左显示，`1`：居中显示，`2`：居右显示   |
| link | 传统样式类型文本导航数据 | `Object`  | - |  -   |
| wxDate | 微信图文页样式类型日期 | `string`  | - |  -   |
| wxAuthor | 微信图文页样式类型作者 | `string`  | - |  -   |
| wxLinkTitle | 微信图文页样式类型链接标题 | `string`  | - |  -   |
| wxLinkUrl | 微信图文页样式链接url | `string`  | - |  -   |
| wxLinkType | 微信图文页样式链接类型 | ``[string, number]`  | `0` |  `0`：引导关注，`1`：其他链接   |
| wxLink | 微信图文页样式类型导航数据 | `Object`  | - |  -   |

### link和wxLink属性数据格式

| 字段名       | 说明      |
|-----------|-----------|
| title | 链接标题 |
| link_url | 链接url |

