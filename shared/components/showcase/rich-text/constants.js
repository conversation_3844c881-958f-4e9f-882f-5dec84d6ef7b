export const STYLE_MAP = {
  p: 'margin: 0 0 1em;',
  div: 'margin: 0; padding: 0;',
  h1: 'font-size: 2em; margin: .67em 0; font-weight: bolder;',
  h2: 'font-size: 18px; line-height: 22px; font-weight: bolder;',
  h3: 'font-size: 15px; line-height: 18px; font-weight: bolder;',
  h4: 'font-size: 15px;',
  h5: 'font-size: 15px;',
  i: 'font-style: italic;',
  cite: 'font-style: italic;',
  em: 'font-style: italic;',
  var: 'font-style: italic;',
  address: 'font-style: italic;',
  pre: 'font-family: monospace; white-space: pre;',
  tt: 'font-family: monospace;',
  code: 'font-family: monospace; display: inline; background:#f5f5f5;',
  kbd: 'font-family: monospace;',
  samp: 'font-family: monospace;',
  big: 'font-size: 1.17em;',
  small: 'font-size: .83em;',
  sub: 'font-size: .83em; vertical-align: sub;',
  sup: 'font-size: .83em; vertical-align: super;',
  s: 'text-decoration: line-through; display: inline;',
  strike: 'text-decoration: line-through;',
  del: 'text-decoration: line-through; display: inline;',
  strong: 'display: inilne;',
  a: 'color: deepskyblue; word-break: break-all; overflow: auto;',
  video: 'text-align: center; margin: 10px 0;',
  img: 'overflow: hidden; vertical-align: bottom; max-width: 100% !important; height: auto;',
  blockquote:
    'margin: 0 0 18px; padding: 0 0 0 15px; font-family:Courier, Calibri,"宋体"; border-left: 5px solid #eee;',
  ul: 'margin: 20rpx 10rpx;',
  ol: 'margin: 20rpx 10rpx;',
  li: 'align-items: baseline; margin: 10rpx 0;',
  u: 'text-decoration: underline;',
  hide: 'display: none;',
  tr: 'display: table-row;',
  th:
    'display: table-cell; vertical-align: middle; padding:5px 10px; font-size:28rpx; border:1px solid #ddd; word-break: break-all; background:#f0f0f0;',
  td:
    'display: table-cell; vertical-align: middle; padding:5px 10px; font-size:28rpx; border:1px solid #ddd; word-break: break-all;',
  figure: 'overflow: hidden;',
  table:
    'table-layout: fixed; border-collapse: collapse; width: auto !important; display: table; margin: 0 auto; margin-bottom: 10px;',
  tbody: 'display: table-row-group;'
};

export const POLYFILL_MAP = {
  section: 'div',
  header: 'div',
  footer: 'div',
  article: 'div',
  nav: 'div',
  figure: 'div',
  figcaption: 'div',
  detail: 'div',
  hgroup: 'div',
  summary: 'div',
  aside: 'div',
  inherit: 'div'
};
