<view
  class="rich-text"
  style="background-color: {{ componentData.color || 'white' }};{{ componentData.fullscreen === 1 ? '' : 'padding: 10px 10px 0 10px;' }}{{ componentData.externalStyle }}"
>
  <canvas
    class="rich-canvas"
    style="width: 600px; height: 600px; display: {{ showCanvas ? 'block' : 'none' }}"
    binderror="canvasErrorCallback"
    canvas-id="{{ 'rich-text-' + index }}"
  >
  </canvas>
  <rich-text nodes="{{ nodes }}" space="nbsp"></rich-text>
</view>
