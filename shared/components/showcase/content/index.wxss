.theme-feature-content {
  background: #fff;
}

.content-title {
  position: relative;
  display: flex;
  padding: 0 15px;
  flex-direction: row;
  height: 45px;
  overflow-x: hidden;
  align-items: center;
  justify-content: space-between;
}

.content-title::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border-bottom: 1px solid #eee;
}

.theme-feature-content__all-text {
  font-size: 13px;
  color: #999;
}

.theme-feature-content__view-all {
  display: flex;
  justify-content: center;
  align-items: center;
}

.theme-feature-content__van-icon {
  display: block !important;
  font-size: 10px !important;
  margin-left: 4px;
  color: #999;
}

.theme-feature-content__title-text {
  color: #333;
  font-weight: 700;
  font-size: 15px;
}

.all-text {
  font-size: 13px;
  color: #999;
}

.title-text {
  color: #333;
  font-weight: 700;
  font-size: 15px;
}
