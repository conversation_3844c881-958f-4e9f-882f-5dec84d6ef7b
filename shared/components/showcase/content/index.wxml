<view>
  <view class="theme-feature-content van-hairline--top-bottom">
    <view wx:if="{{ showTitle }}" class="content-title">
      <text class="title-text">{{ title }}</text>
      <view wx:if="{{ showTitleAll }}" class="theme-feature-content__view-all">
          <text class="theme-feature-content__all-text" bindtap="goToAllContent">全部</text>
          <!-- <text class="zan-icon zan-icon-arrow theme-feature-content__zan-icon"></text> -->
          <van-icon name="arrow" custom-class="theme-feature-content__van-icon" />
        </view>
    </view>
    <block wx:for="{{ list }}" wx:key="id">
      <cap-content-item
        content="{{ item }}"
        show-price-info="{{ item.showPriceInfo }}"
      />
    </block>
  </view>
</view>
