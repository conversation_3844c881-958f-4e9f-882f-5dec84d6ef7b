import pick from '@youzan/weapp-utils/lib/pick';
import { node as request } from 'shared/utils/request';
import componentBehavior from '../behaviors/component-behavior';
import { shopCanPayForKnowledge, parseTime } from '../utils/pctUtils';
import loggerBehavior from '../behaviors/logger-behavior';

Component({
  options: {
    addGlobalClass: true
  },

  type: 'content',

  behaviors: [componentBehavior, loggerBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
    },
    kdtId: {
      type: String
    }
  },

  data: {
    itemType: 'content',
    title: '',
    showTitle: false,
    showTitleAll: false,
    list: [],
    hideContentSubsCount: 0
  },

  attached() {
    const componentData = this.data.componentData;
    this.setData({
      itemType: 'content',
      title: componentData.title || '',
      showTitle: parseInt(componentData.show_title, 10) === 1,
      showTitleAll: parseInt(componentData.show_title_all, 10) === 1,
      list: componentData.sub_entry.map((item) => {
        return {
          ...pick(item, 'alias', 'cover', 'title', 'summary', 'buyTime'),
          price: item.price,
          subCount: +item.subscriptions_count,
          mediaType: +item.media_type,
          time: parseTime(item.publish_at, true),
          isFree: Boolean(item.is_free && item.column_alias),
          showPriceInfo: this.showPriceInfo()
        };
      }),
      hideContentSubsCount: 0
    });
    this.getContentList(componentData);
  },

  methods: {
    showPriceInfo() {
      return shopCanPayForKnowledge();
    },

    getContentList(itemData) {
      if (itemData.content_from === 'custom') {
        itemData.ids = itemData.sub_entry.map(item => item.id).join(',');
      }
      request({
        path: '/wscshop/showcase/knowledge/contentlist',
        data: {
          source: itemData.content_from === 'custom' ? 1 : 0,
          ids: itemData.ids ? itemData.ids : '',
          kdt_id: this.data.kdtId,
          pageSize: 6
        }
      }).then((result) => {
        if (!result) {
          return;
        }
        this.setData({ list: this.mergeContent(itemData, result) });
      });
    },

    mergeContent(itemData, newItemData) {
      function getOldItemById(id) {
        for (let i = 0; i < itemData.sub_entry.length; i++) {
          let item = itemData.sub_entry[i];

          if (item.id === id) {
            return item;
          }
        }

        return {};
      }

      let list = [];
      const loggerList = [];

      list = newItemData.filter((item) => item != null).map((newItem, index) => {
        let {
          buyStatus = {},
          contentCount = 0,
          cardUserCount = 0,
          publishAt
        } = newItem;

        const oldItem = getOldItemById(newItem.id);

        const bannerId = this.getBannerId(index + 1);
        const loggerParams = {
          goods_id: newItem.goodsId,
          item_id: newItem.id,
          item_type: 'paid_content',
          component: 'paid_content',
          banner_id: bannerId,
          ...this.getComponentLoggerExtraParams()
        };
        loggerList.push(loggerParams);

        return {
          ...oldItem,
          ...newItem,
          ...buyStatus,
          publishAt: parseTime(publishAt, true),
          price: buyStatus.price || 0,
          isVip: !!buyStatus.isVipDiscount,
          count: contentCount,
          subCount: cardUserCount,
          isFree: buyStatus.isFree === 1,
          showPriceInfo: this.showPriceInfo()
        };
      });

      this.ensureAppLogger('view', loggerList);

      return list;
    },

    goToAllContent() {
      wx.navigateTo({
        url: '/packages/paidcontent/list/index?type=content'
      });
    }
  },
});
