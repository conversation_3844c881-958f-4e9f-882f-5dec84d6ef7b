import promisify from 'utils/promisify';

const app = getApp();
const carmen = promisify(app.carmen);

/**
 * 获取用户信息
 *
 * @param {*} params
 */
export function getUserInfo() {
  const signQrcodeKey = app.globalData.signQrcodeKey;
  const isFirstSign = app.globalData.firstSign;

  const params = {
    need_query_member_benefits: true,
    need_query_growth_value: true,
    need_query_birthday_privilege: true,
    need_query_balance: true
  };

  if (isFirstSign && signQrcodeKey) {
    params.qr_code = signQrcodeKey;
    params.member_src_way = 800;
    params.member_src_channel = 1000;
  }

  return carmen({
    api: 'youzan.retail.scrm.customer.assets/1.0.0/get',
    data: params,
  });
}

/**
 * 获取等级信息
 *
 * @returns {Promise}
 */
export function queryLevelInfo() {
  return new Promise((resolve) => {
    wx.getStorage({
      key: 'app:token',
      success: (res) => {
        const userId = res.data.value.yzUserId;

        resolve(
          app.request({
            path: '/retail/h5/user/level.json',
            data: {
              userId,
            },
          })
        );
      },
    });
  });
}

/**
 * 获取等级信息是否已填写
 */
export function queryLevel() {
  return new Promise((resolve) => {
    wx.getStorage({
      key: 'app:token',
      success(res) {
        const userId = res.data?.value?.yzUserId;

        resolve(
          app.request({
            path: '/retail/h5/user/levelInfo.json',
            data: {
              userId,
            },
          })
        );
      },
    });
  });
}
