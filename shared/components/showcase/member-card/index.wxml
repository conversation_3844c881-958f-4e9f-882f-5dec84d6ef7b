<view
  wx:if="{{ loaded }}"
  class="member {{ borderRadiusType === 1 ? 'no-radius' : '' }}"
  style="background-image: url({{ coverUrl }}); background-color: {{cardColor}}"
>
  <!-- 当前用户为付费会员 -->
  <view
    wx:if="{{ supportPaidLevel && paidLevelInfo.level && hasPaidLevel }}"
    class="member-paid-level"
    bind:tap="navigateToUserCenter"
  >
    <view class="member-paid-level__header">
      <text class="level-header__name">{{ paidLevelInfo.level.name }}</text>
      <text class="level-header__date" >
        <block wx:if="{{ expired }}">
          已于 {{ endDate }} 到期
        </block>
        <block wx:elif="{{ willExpiredFlag }}">
          将于 {{ endDate }} 到期
        </block>
        <block wx:else>
          有效期至 {{ endDate }}
        </block>
      </text>
      <view class="member-paid-level__qrcode" catch:tap="navigateToLevelCode">
        <image
          class="member-paid-level__qrcode-icon"
          src="https://b.yzcdn.cn/retail/weapp/icons/<EMAIL>"
          alt="会员码"
          lazy-load
        />
      </view>
    </view>
    <view class="benefit-num">尊享 {{ benefitNum }} 项专享权益</view>
    <view class="benefit-btn" wx:if="{{ willExpireFlag }}" catch:tap="navigateToLevelDetail">立即续费</view>
  </view>
  <block wx:else>
    <!-- 自定义会员卡面的蒙层 -->
    <view wx:if="{{ hasCoverUrl && !hasPaidLevel }}" class="member-overlay"></view>
    <!-- 免费会员等级会员展示 -->
    <view wx:if="{{ isMember }}" class="member-content" bind:tap="navigateToUserCenter">
      <view class="member-content__header">
        <view class="member-content__header-level">
          <text class="level-name">{{ level === 0 ? '客户' : levelName }}</text>
          <view class="level-icon">
            <view class="level-icon__prefix" />
            <view class="level-icon__content">Lv.{{ level }}</view>
          </view>
        </view>
        <view
          wx:if="{{ level > 0 }}"
          class="member-content__qrcode"
          catch:tap="handleOpenPopup"
        >
          <image
            class="member-paid-level__qrcode-icon"
            src="https://b.yzcdn.cn/retail/weapp/icons/<EMAIL>"
            alt="会员码"
            lazy-load
          />
        </view>
      </view>
      <view class="member-content__growth">
        <text class="member-content__growth-value">{{ currentGrowth }}</text>
        <text class="member-content__growth-text">成长值</text>
        <view
          wx:if="{{ supportPaidLevel }}"
          class="member-content__upgrdae"
          catch:tap="navigateToPaidLevels"
        >
          升级付费会员
          <van-icon name="arrow" />
        </view>
      </view>
      <view class="member-content__progress">
        <view class="progress" style="width: {{ progress }}%" />
      </view>
      <view class="member-content__desc" wx:if="{{ !isRegistryComplete }}" catch:tap="navigateToCompletePage">
        完善资料尊享更多会员权益
      </view>
      <view class="member-content__desc" wx:elif="{{ isMaxLevel }}">
        已是最高等级
      </view>
      <view class="member-content__desc" wx:elif="{{ nextLevelGrowthGap }}">
        再获得 {{ nextLevelGrowthGap }} 成长值成为 VIP {{ level ? level + 1 : 1 }}
      </view>
    </view>
    <!-- 非会员的展示 -->
    <view class="member-card" wx:else>
      <view class="member-card__text">
        <view class="member-card__title">{{ cardTitle }}</view>
        <view class="member-card__desc">{{ cardDesc }}</view>
      </view>
      <block wx:if="{{ !hasMobile }}">
        <view class="member-card__action">
          <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="becomeMember">
            立即授权
          </user-authorize>
        </view>
      </block>
      <!-- <button
        class="member-card__action"
        wx:if="{{ !hasMobile }}"
        open-type="getPhoneNumber"
        bindgetphonenumber="bindMobileAndBecomeMember"
        bind:tap="noop"
      >
        立即授权
      </button> -->
      <button
        class="member-card__action"
        wx:else
        bind:tap="becomeMember"
      >
        领取权益
      </button>
    </view>
  </block>
</view>

<account-wx-login id="account-wx-login" />

<level-info-popup
  wx:if="{{ showPopup }}"
  show="{{ showPopup }}"
  identity="{{ identity }}"
  levelName="{{ levelName }}"
  nickName="{{ nickName }}"
  level="{{ level }}"
  bind:close="handleClosePopup"
/>

<level-info-popup
  wx:if="{{ showPaidPopup }}"
  show="{{ showPaidPopup }}"
  identity="{{ paidLevelInfo.identityNo }}"
  levelName="{{ paidLevelInfo.level.name }}"
  nickName="{{ nickName }}"
  bind:close="handleClosePaidPopup"
/>
