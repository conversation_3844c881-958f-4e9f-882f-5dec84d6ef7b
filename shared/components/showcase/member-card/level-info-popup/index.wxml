<popup show="{{show}}" bind:close="closePopup" custom-class="level-info__popup">
  <view bind:tap="handleTapCode">
    <view class="level-info__popup-header">
      <view class="level-info__popup-title">{{ nickName }}</view>
      <view class="level-info__popup-level">
        {{ levelName }} <block wx:if="{{ level }}">LV.{{ level }}</block>
      </view>
      <view class="level-info__popup-close" bind:tap="closePopup" />
    </view>
    <view class="level-info__popup-body">
      <block wx:if="{{!isLoading}}">
        <image class="level-info__popup-barcode" src="{{ barcode }}" />
        <view class="level-info__popup-code">
          {{ encodeCode }}
        </view>
        <image class="level-info__popup-qrcode" src="{{ qrcode }}" />
      </block>
      <loading color="#000" size="60px" wx:else />
    </view>
  </view>
</popup>
