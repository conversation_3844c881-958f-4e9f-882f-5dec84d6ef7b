.level-info__popup {
    width: 640rpx;
    background: transparent;
    border-radius: 16rpx;

    &-close {
        position: absolute;
        right: 5px;
        top: 7px;
        padding: 10px;
        height: 20px;
        width: 20px;
        border-radius: 50%;

        &:before,
        &::after {
            content: '';
            width: 15px;
            height: 2px;
            background: #c8c9cc;
            position: absolute;
            left: 50%;
            top: 50%;
        }

        &:after {
            transform: translateX(-50%) translateY(-50%) rotateZ(45deg);
        }

        &::before {
            transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
        }
    }

    &-logo {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        font-size: 14px;
    }

    &-shop {
        display: inline-block;
        margin-left: 10px;
        font-size: 14px;
        vertical-align: top;
        line-height: 26px;
    }

    &-title {
        text-align: center;
        font-size: 32rpx;
        font-weight: 500;
        margin-bottom: 16rpx;
    }

    &-level {
        font-size: 24rpx;
        color: #FAAB0C;
        text-align: center;
    }

    &-qrcode {
        width: 300rpx;
        height: 300rpx;
    }

    &-barcode {
        width: 560rpx;
        height: 140rpx;
    }

    &-header {
        border-radius: 8px 8px 0 0;
        height: 140rpx;
        padding: 15px 20px 10px 20px;
        color: #333;
        background-color: #f7f8fa;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    &-body {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #fff;
        border-radius: 0 0 8px 8px;
        min-height: 330px;
        box-sizing: border-box;
    }

    &-code {
        font-size: 24rpx;
        margin: 16rpx 0 32rpx;
    }

}
