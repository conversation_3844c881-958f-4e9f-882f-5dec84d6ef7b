import usePolling from 'retail/util/use-polling';

const app = getApp();

Component({
  properties: {
    show: <PERSON><PERSON><PERSON>,
    identity: String,
    levelName: String,
    level: String,
    nickName: String,
  },

  data: {
    barcode: '',
    qrcode: '',
    encodeCode: '',
    isLoading: false
  },

  ready() {
    const cycleMs = 60000;
    const [setQRCode, stopPolling] = usePolling(
      this.fetchCode.bind(this),
      cycleMs
    );
    this.setQRCode = setQRCode;
    this.stopPolling = stopPolling;

    this.setQRCode();
  },

  detached() {
    this.stopPolling();
  },

  methods: {
    closePopup() {
      this.triggerEvent('close');
    },

    handleTapCode() {
      this.setData({
        isLoading: true
      });
      this.stopPolling();
      this.setQRCode();
    },

    async fetchCode() {
      try {
        const {
          barCode: barcode,
          qrCode: qrcode,
          code: encodeCode
        } = await app.request({
          path: '/wscuser/levelcenter/api/retail/getQrAndBarCode.json',
          data: {
            accountId: app.getToken('userId'),
            accountType: 1
          }
        });
        this.setData({
          barcode,
          qrcode,
          encodeCode
        });
      } catch (err) {
        wx.showToast({
          title: '获取会员码失败，尝试再次点击获取',
          icon: 'none'
        });
      } finally {
        this.setData({
          isLoading: false
        });
      }
    }
  }
});
