/**
 * 微页面 - 会员卡组件
 *
 * NOTE: 目前仅同城零售 - 24H 货架模板业务使用
 */

import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { computeDayCount } from '@youzan/weapp-utils/lib/time-utils';
import { joinMember } from 'retail/util/bind-mobile';
import openWebView from 'utils/open-web-view';
import { moment as formatDate } from 'utils/time';

import { getUserInfo, queryLevelInfo, queryLevel } from './api';
import { COLOR_MAP } from './constant';

const app = getApp();
const defaultCoverUrl =
  'https://img01.yzcdn.cn/public_files/1ac9bfa34153ccaf5448c02a9ca60e4e.png';
const defaultCardColor = '#d09a45';

Component({
  properties: {
    componentData: {
      type: Object,
      observer(data) {
        const { title, desc } = data;
        const defaultBorderRadiusType = 2;

        this.setData({
          borderRadiusType: data.borderRadiusType || defaultBorderRadiusType,
          cardTitle: title?.length > 0 ? title : '成为会员',
          cardDesc: desc?.length > 0 ? desc : '尽享会员权益',
        });
      },
    },
  },

  data: {
    loaded: false,
    // 当前店铺 是否支持付费会员
    supportPaidLevel: false,
    // 是否为会员
    isMember: false,
    // 是否授权了手机号
    hasMobile: false,
    // 会员等级信息
    levelName: '',
    level: 0,

    cardTitle: '',
    cardDesc: '',

    // 成长值信息
    currentGrowth: '',
    isMaxLevel: false,
    nextLevelGrowthGap: '',

    coverUrl: defaultCoverUrl,
    cardColor: defaultCardColor,
    paidLevelInfo: {},

    showPopup: false,
    showPaidPopup: false,
  },

  lifetimes: {
    attached() {
      this.fetchData();
    },
  },

  methods: {
    noop() {},

    /**
     * 获取会员相关数据
     *
     * TODO: 目前通过多个接口获取，后续还是需要合并成一个
     */
    fetchData() {
      Promise.all([queryLevelInfo(), getUserInfo(), queryLevel()])
        .then(([paidLevelInfo, userInfo, levelInfo]) => {
          this.setData({
            loaded: true,
            ...this.transformPaidLevelInfo(paidLevelInfo),
            ...this.transformUserInfo(userInfo),
            ...this.transformLevelInfo(levelInfo),
          });
        })
        .catch((err) => {
          console.error(err);
        });
    },

    /**
     * 转换用户的数据
     *
     * @param {Object} data
     * @returns {Object}
     */
    transformUserInfo(data) {
      if (!data) return {};

      const {
        asset_info: assetInfo,
        customer_info: customerInfo,
        user_info: userInfo,
        member_level_right: memberLevelRight = {},
      } = data;

      if (!customerInfo || !userInfo) return {};

      // 判断是否为会员
      const isMember = customerInfo.is_member !== 0;
      // 是否有手机号
      const hasMobile = userInfo.mobile_flag;

      return {
        isMember,
        hasMobile,
        nickName: userInfo.nick_name,
        identity: memberLevelRight.identity_no || '',
        levelName:
          memberLevelRight.vip_name || `VIP ${memberLevelRight.vip_level || 0}`,
        level: memberLevelRight.vip_level || 0,
        nextLevelGrowthGap: memberLevelRight.next_growth_limit_gap || '',
        isMaxLevel: memberLevelRight.is_current_level_to_max || false,
        coverUrl: cdnImage(memberLevelRight.cover_url || defaultCoverUrl),
        cardColor: this.getColorByCode(memberLevelRight.color_code),
        hasCover: !!memberLevelRight.cover_url,
        currentGrowth: assetInfo.growth_value,
        progress: memberLevelRight.is_current_level_to_max
          ? 100
          : (assetInfo.growth_value /
              (assetInfo.growth_value +
                memberLevelRight.next_growth_limit_gap)) *
            100,
      };
    },

    /**
     * 换换付费会员卡数据
     *
     * @param {Object} data
     * @returns {Object}
     */
    transformPaidLevelInfo(data) {
      if (!data.enable) return {};

      if (Object.keys(data.info).length > 0) {
        const { enable, info } = data;

        // 没有付费等级
        if (!info.level) return {};

        const dayCount = computeDayCount(new Date(), info.termEndAt);
        const levelData = {
          supportPaidLevel: enable,
          paidLevelInfo: info,
          hasPaidLevel: enable && info.level,
          benefitNum: this.getBenefitNum(data.detail),
          endDate: formatDate(info.termEndAt, 'YYYY-MM-DD'),
          expired: dayCount === 0,
          willExpireFlag: dayCount <= 30,
        };

        if (enable && info.level) {
          return {
            ...levelData,
            coverUrl: 'https://b.yzcdn.cn/retail/shelf/paid_level_bg.png',
          };
        }

        return levelData;
      }

      return {};
    },

    /**
     * 转换等级信息
     *
     * @param {*} data
     * @returns {Object}
     */
    transformLevelInfo(data) {
      return {
        isRegistryComplete: data.isRegistryComplete ?? true,
        levelGroupAlias: data.levelGroupAlias ?? '',
      };
    },

    /**
     * 会员卡 code 获取 color
     * @param {*} code
     * @returns 颜色
     */
    getColorByCode(code) {
      return COLOR_MAP[code] || defaultCardColor;
    },

    /**
     * 成为会员
     */
    becomeMember() {
      joinMember(
        () => {
          this.fetchData();

          // 埋点
          // app.logger.log({
          //   et: 'custom', // 事件类型
          //   ei: 'bind_member_success', // 事件标识
          //   en: '绑定会员成功', // 事件名称
          //   pt: 'retailshelfhome' // 页面类型
          // });
        },
        {
          kdtId: app.getKdtId(),
          member_src_way: 800,
          member_src_channel: 1000,
          need_be_member: true,
        }
      );
    },

    /**
     * 重定向到会员中心
     */
    navigateToUserCenter() {
      const { isMember } = this.data;
      const kdtId = app.getKdtId();
      const memberUrl = 'https://h5.youzan.com/wscuser/memberlevel';

      if (isMember) {
        openWebView(`${memberUrl}?kdt_id=${kdtId}`, { title: '会员中心' });
      } else {
        openWebView(
          `${memberUrl}/mobilecheck?kdt_id=${kdtId}&sales_id=0&referee_id=0&referee_scene=0`
        );
      }
    },

    /**
     * 跳转到付费等级页面
     */
    navigateToPaidLevels() {
      const url = `https://cashier.youzan.com/pay/wscuser_paylevel?kdt_id=${app.getKdtId()}`;
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(url)}`,
      });
    },

    /**
     * 跳转到付费等级详情
     */
    navigateToLevelDetail() {
      const alias = this.data.paidLevelInfo?.level?.levelAlias;
      const url = `https://cashier.youzan.com/pay/wscuser_paylevel?kdt_id=${app.getKdtId()}&alias=${alias}`;
      wx.navigateTo({
        url: `/pages/common/webview-page/index?src=${encodeURIComponent(url)}`,
      });
    },

    /**
     * 会员码页面
     */
    navigateToLevelCode() {
      wx.navigateTo({
        url: `/packages/member-code/index?eT=${Date.now()}`,
      });
    },

    /**
     *  获取付费权益数量
     *
     * @param {Object} benefit
     * @returns {Number} num
     */
    getBenefitNum(benefit) {
      let num = 0;
      const benefitList = benefit.levelBenefit;

      if (benefitList) {
        Object.keys(benefitList).forEach((key) => {
          if (key === 'diyTemplateList') {
            num += benefitList[key].length;
          } else {
            num += 1;
          }
        });
      }

      return num;
    },

    /**
     * 跳转到填写信息页面
     */
    navigateToCompletePage() {
      // alias
      openWebView('/wscuser/levelcenter/fill', {
        query: {
          kdt_id: app.getKdtId(),
          alias: this.data.levelGroupAlias,
          eT: Date.now(),
        },
      });
    },

    handleOpenPopup() {
      this.setData({
        showPopup: true,
      });
    },

    handleOpenPaidPopup() {
      this.setData({
        showPaidPopup: true,
      });
    },

    handleClosePopup() {
      this.setData({
        showPopup: false,
      });
    },

    handleClosePaidPopup() {
      this.setData({
        showPaidPopup: false,
      });
    },
  },
});
