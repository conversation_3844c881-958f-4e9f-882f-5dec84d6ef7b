.member {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx 32rpx;
  box-sizing: border-box;
  padding: 24rpx;
  overflow: hidden;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  border-radius: 16rpx;

  &.no-radius {
    border-radius: 0;
  }

  &-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-position: cover;
    background-image: url('//b.yzcdn.cn/public_files/4f052ce6375fa10abac7ab7eab716ad7.png');
  }
}

.member-paid-level {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 272rpx;
  width: 100%;

  &__header {
    display: flex;
    flex-direction: column;

    .level-header__name {
      color: white;
      font-size: 34rpx;
      font-weight: bold;
    }

    .level-header__date {
      font-size: 24rpx;
      color: white;
      margin-top: 16rpx;
    }
  }

  &__qrcode {
    position: absolute;
    right: 0;
    top: 0;
    color: white;
    width: 88rpx;
    height: 88rpx;

    &-icon {
      // width: 52rpx;
      // height: 52rpx;
      width: 88rpx;
      height: 88rpx;
    }
  }

  .benefit-btn {
    position: absolute;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 32rpx;
    color: white;
    padding: 12rpx 26rpx;
    font-size: 26rpx;
    border: 1px solid white;
  }

  .benefit-num {
    color: white;
    font-size: 28rpx;
  }
}

.member-card {
  display: flex;
  flex: 1;
  justify-content: space-between;
  align-items: center;
  border-radius: 8rpx;

  &__title {
    color: #fff;
    font-size: 40rpx;
  }

  &__desc {
    color: #fff;
    font-size: 28rpx;
  }

  &__action {
    color: #d09a45;
    height: 56rpx;
    line-height: 56rpx;
    border-radius: 28rpx;
    background-color: #fff;
    font-size: 26rpx;
    margin: 0;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.member-content {
  position: relative;
  color: white;
  width: 100%;

  &__header {
    position: relative;
    display: flex;
    justify-content: space-between;

    &-level {
      display: flex;
      height: 46rpx;

      .level-name {
        font-size: 34rpx;
        font-weight: bold;
        display: flex;
      }

      .level-icon {
        display: flex;
        align-items: center;

        &__prefix {
          width: 28rpx;
          height: 28rpx;
          background: url(//b.yzcdn.cn/public_files/74c8dd5df409556a7d481f212b36cc38.png)
            50% / cover no-repeat;
          border-radius: 50%;
          margin-left: 0.5em;
          border: 2rpx solid #d09a45;
          overflow: hidden;
          z-index: 1;
        }

        &__content {
          position: relative;
          left: -8rpx;
          font-size: 24rpx;
          font-weight: 400;
          line-height: 1;
          padding: 0 6px;
          background-color: #fff;
          color: #d09a45;
          border-radius: 0 12rpx 12rpx 0;
          transform: scale(0.833);
          transform-origin: left center;
        }
      }
    }
  }

  &__qrcode {
    display: flex;
    position: relative;
    align-items: center;
    font-size: 28rpx;
    width: 88rpx;
    height: 88rpx;

    &-icon {
      // width: 52rpx;
      // height: 52rpx;
      width: 88rpx;
      height: 88rpx;
    }
  }

  &__growth {
    position: relative;
    font-weight: bold;
    margin-top: 30rpx;

    &-value {
      font-size: 68rpx;
    }

    &-text {
      font-size: 24rpx;
      margin-left: 16rpx;
    }
  }

  &__progress {
    margin-top: 8rpx;
    height: 4rpx;
    width: 100%;
    background-color: hsla(0, 0%, 100%, 0.2);
    overflow: hidden;
    border-radius: 2rpx;

    .progress {
      height: 100%;
      background-color: #fff;
    }
  }

  &__desc {
    font-size: 24rpx;
    margin-top: 40rpx;
  }

  &__upgrdae {
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    border: 1px solid white;
    border-radius: 24rpx;
    color: white;
    font-size: 28rpx;
    background-color: rgba(255, 255, 255, 0.2);
    line-height: 48rpx;
    height: 48rpx;
    padding: 0 20rpx;
    font-weight: normal;

    .van-icon {
      display: flex;
      align-items: center;
    }
  }
}
