<wxs src="./index.wxs" module="computed" />

<view class="address-item" bindtap="onSelect">
  <van-cell border="{{ false }}" custom-class="address-item__cell" value-class="address-item__value">
    <van-radio wx:if="{{ switchable }}" name="{{ item.id }}" value="{{ value }}" disabled="{{inactive}}">
      <view class="address-item__name {{inactive ? 'address-item__inactive' : ''}}">
        {{ item.name }} {{ item.tel }}
        <van-tag 
          wx:if="{{ item.isDefault }}"
          class="address-item__tag-root"
          custom-class="address-item__tag"
          type="danger"
          round
        >默认</van-tag>
        <van-tag 
          wx:if="{{ item.label }}"
          class="address-item__tag-root"
          custom-class="address-item__tag"
          color="rgba(238, 10, 36, .1)"
          text-color="#ee0a24"
          round
        >{{ item.label }}</van-tag>
      </view>
      <view class="address-item__address">{{ item.addressDetail }}</view>
    </van-radio>
    <view wx:else>
      <view class="address-item__name {{inactive ? 'address-item__inactive' : ''}}">
        {{ item.name }} {{ item.tel }}
        <van-tag 
          wx:if="{{ item.isDefault }}"
          custom-class="address-item__tag"
          type="danger"
          round
        >默认</van-tag>
        <van-tag 
          wx:if="{{ item.label }}"
          class="address-item__tag-root"
          custom-class="address-item__tag"
          color="rgba(238, 10, 36, .1)"
          text-color="#ee0a24"
          round
        >{{ item.label }}</van-tag>
      </view>
      <view class="address-item__address">{{ item.addressDetail }}</view>
    </view>

    <van-icon
      slot="right-icon"
      name="edit"
      custom-class="address-item__edit"
      bind:click="onEdit"
      catchtap="noop"
    />
  </van-cell>

  <view
    wx:if="{{ computed.showPoiPrompt(forcePoiSelect, item) }}"
    class="address-item__tip {{ switchable ? 'address-item__tip--switchable' : ''  }}"
    catchtap="onEdit"
  >
    为提高同城配送准确性，请进行地图定位选点
    <van-icon class="address-item__tip__icon" name="arrow" />
  </view>
</view>