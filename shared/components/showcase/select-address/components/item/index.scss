.address-item {
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;

  --radio-label-margin: 12px;
  --radio-checked-icon-color: var(--theme-general, #ee0a24);

  &__cell {
    padding: 0 !important;
  }

  &__value {
    padding-right: 44px;
    text-align: left !important;
    color: #323233 !important;
  }

  &__name {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 16px;
    line-height: 22px;
  }

  &__inactive {
    color: #c8c9cc;
  }

  &__tag-root {
    display: flex;
    align-items: center;
  }

  &__tag {
    margin-left: 8px;
    height: 14px;
    box-sizing: border-box;
  }

  &__address {
    font-size: 13px;
    line-height: 18px;
  }

  &--disabled {
    .address-item__name,
    .address-item__address {
      color: #c8c9cc;
    }
  }

  &__edit {
    position: absolute !important;
    top: 50%;
    right: 0;
    color: #969799;
    font-size: 20px !important;
    transform: translate(0, -50%);
  }

  &__tip {
    font-size: 12px;
    color: #ed6a0c;
    line-height: 16px;
    display: flex;
    align-items: center;
    margin: 8px 15px 0 0;
    position: relative;
    justify-content: space-between;

    &--switchable {
      margin-left: 30px;
    }

    &__icon {
      height: 1em;
    }
  }
}
