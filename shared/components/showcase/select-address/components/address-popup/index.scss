$address-container-height: 80vh;
$title-container-height: 40px;

.radius-popup {
  border-radius: 20px 20px 0 0;
}

.address-container {
  height: $address-container-height;

  .title-container {
    position: relative;
    height: $title-container-height;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 500;

    .close-btn {
      position: absolute;
      right: 5px;
      top: 0;
      width: $title-container-height;
      height: $title-container-height;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .close-btn:active {
      opacity: 0.6;
    }
  }

  .panel-container {
    height: calc(100% - 40px);
  }
}
