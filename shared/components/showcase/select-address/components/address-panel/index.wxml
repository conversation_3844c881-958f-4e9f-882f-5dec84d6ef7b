<view class="address-panel-container">
    <view class="address-panel-body">
        <block wx:if="{{addresses && addresses.length}}">
            <block wx:for="{{addresses}}" wx:for-item="address" wx:key="id">
                <item
                    item="{{ address }}"
                    switchable
                    value="{{isSelectedId }}"
                    bind:select="handleSelectAddress"
                    bind:edit="handleEditAddress"
                />
            </block>
        </block>
        <block wx:else>
            <view class="no-address">
                <view class="no-address_img" />
                <view class="no-address_title">你还没有收货地址哦</view>
            </view>
        </block>
    </view>
    <view class="address-panel-bottom">
        <view class="add-address-btn" bind:tap="clickNewAddress">+新增地址</view>
    </view>
</view>