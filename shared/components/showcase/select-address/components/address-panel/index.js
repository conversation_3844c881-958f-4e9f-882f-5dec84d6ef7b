Component({
  properties: {
    addresses: {
      type: Array,
      value: []
    },
    isSelectedId: Number,
    themeMainColor: String
  },
  methods: {
    clickNewAddress() {
      this.triggerEvent('clickNewAddress');
    },
    handleEditAddress({ detail }) {
      this.triggerEvent(
        'handleEditAddress',
        detail
      );
    },
    handleSelectAddress({ detail }) {
      if (this.data.isSelectedId == detail.id) return;
      this.triggerEvent(
        'handleSelectAddress',
        detail
      );
    }
  }
});
