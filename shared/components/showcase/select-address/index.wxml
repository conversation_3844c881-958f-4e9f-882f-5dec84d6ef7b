<view class="address-container">
  <block wx:if="{{!isSelectedAddressId}}">
    <view class="select-address">请选择收货地址</view>
    <view style="color: {{ themeColor }}" bind:tap="showAddressPop" class="change-address">选择地址</view>
  </block>
  <block wx:else>
    <view class="user-address">
      <view class="user-address__info">
        <text style="margin-right: 6px">{{currentAddress.userName + " "}}</text>
        <text>{{currentAddress.tel}}</text>
        <van-tag 
          wx:if="{{ currentAddress.isDefault }}"
          class="user-address__tag-root"
          custom-class="user-address__tag"
          type="danger"
          round
        >默认</van-tag>
        <van-tag 
          wx:if="{{ currentAddress.label }}"
          class="user-address__tag-root"
          custom-class="user-address__tag"
          color="rgba(238, 10, 36, .1)"
          text-color="#ee0a24"
          round
        >{{ currentAddress.label }}</van-tag>
      </view>
      
      <view class="user-address__detail">
        {{currentAddress.addressDetail + currentAddress.houseNumber}}
      </view>
    </view>
    <view style="color: {{ themeColor }}" class="change-address" bind:tap="showAddressPop">更换地址</view>
  </block>
</view>
<address-popup show="{{showAddress}}" bind:closePopup="closeAddressPop">
  <address-panel 
    addresses="{{addresses}}" 
    isSelectedId="{{ isSelectedAddressId }}" 
    style="--theme-main-color: {{themeMainColor}}" 
    themeMainColor="{{themeMainColor}}" 
    bind:handleSelectAddress="handleSelectAddress" 
    bind:clickNewAddress="handleNewAddress" 
    bind:handleEditAddress="handleEditAddress" 
   />
</address-popup>