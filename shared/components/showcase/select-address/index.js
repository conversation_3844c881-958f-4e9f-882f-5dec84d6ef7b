import { getUserAddresses } from './api';
import Toast from '@vant/weapp/dist/toast/toast';
import { stringifyAddress } from 'utils/stringify-address';
import Event from '@youzan/weapp-utils/lib/event';

const app = getApp();
const EventKeyShopSelectLocation = 'shop-select:locate';

const ADDRESS_DELETE = 'address-delete';
const ADDRESS_SAVE = 'address-save';

Component({
  properties: {
    addressId: {
      type: String,
      observer(addressId) {
        if (addressId) {
          this.initUserAddress().then((res) => {
            const address = res.find((address) => +address.id === +addressId);
            if (address) {
              address.firstLoad = true;
              this.handleSelectAddress({ detail: address });
            }
          });
        }
      },
    },
    themeColor: String
  },

  data: {
    addresses: [],
    showAddress: false,
    themeMainColor: '#f44',
    isSelectedAddressId: '',
    currentAddress: {},
  },

  attached() {
    this.initUserAddress();

    app.on(ADDRESS_DELETE, (id) => {
      if (id === this.data.isSelectedAddressId) {
        this.setData({ isSelectedAddressId: '' });
      }
      this.setData({
        addresses: (this.data.addresses || []).filter((item) => item.id !== id),
      });
    });

    app.on(ADDRESS_SAVE, (newAddress) => {
      const { addresses = [] } = this.data;
      const editingIndex = addresses.findIndex(
        (item) => item.id === newAddress.id
      );
      newAddress.name = newAddress.userName;
      newAddress.address = stringifyAddress(newAddress);
      if (editingIndex === -1) {
        // 添加地址
        addresses.push(newAddress);
      } else {
        // 编辑地址
        addresses.splice(editingIndex, 1, newAddress);
      }

      if (newAddress.isDefault) {
        addresses.forEach((item) => {
          item.isDefault = newAddress.id === item.id ? 1 : 0;
        });
      }

      this.setData({ addresses });
      if (newAddress.id === this.data.currentAddress.id) {
        this.setData({
          currentAddress: newAddress,
        });
      }
    });

    Event.on(EventKeyShopSelectLocation, this.clearCurrentAddress, this);
  },

  detached() {
    Event.off(EventKeyShopSelectLocation, this.clearCurrentAddress, this);
  },

  methods: {
    initUserAddress() {
      return getUserAddresses()
        .then((res) => {
          this.setData({
            addresses: res,
          });
          return res;
        })
        .catch((err) => {
          Toast.fail(err.msg || '获取用户地址信息失败');
        });
    },

    closeAddressPop() {
      this.setData({
        showAddress: false,
      });
    },

    showAddressPop() {
      this.setData({
        showAddress: true,
      });
    },

    // 清空所选地址
    clearCurrentAddress() {
      this.setData({
        isSelectedAddressId: '',
        currentAddress: {},
      });
    },

    // 选择地址
    handleSelectAddress({ detail }) {
      this.setData({
        isSelectedAddressId: detail.id,
        showAddress: false,
        currentAddress: detail,
      });
      this.triggerEvent('select-address', detail);
    },

    // 新增地址
    handleNewAddress() {
      const dbid = app.db.set({
        list: this.data.addresses,
        forcePoiSelect: true,
        delta: 1,
      });

      wx.navigateTo({
        url: `/packages/order-native/address-edit/index?dbid=${dbid}`,
      });
    },

    // 编辑地址
    handleEditAddress({ detail }) {
      const dbid = app.db.set({
        id: detail.id,
        list: this.data.addresses,
        forcePoiSelect: true,
        delta: 1,
      });

      wx.navigateTo({
        url: `/packages/order-native/address-edit/index?dbid=${dbid}`,
      });
    },
  },
});
