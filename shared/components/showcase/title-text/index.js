import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import navigationToBehavior from '../behaviors/navigation-to-behavior';
import { getLink } from '../utils/linkAdaptor';
import transform from '../utils/transformComponentData';
import jumpVideoNumber from '../utils/jumpVideoNumber';
import { COMPUTED } from './constants';

Component({
  behaviors: [navigationToBehavior, componentBehavior, loggerBehavior],

  data: {
    wrapClass: '',
    isWx: false,
    isWxFollow: true,
    showSub: false,
    showLinkText: true,
    showLinkArrow: true,
    onlyLinkText: false,
    alignRight: false,
    linkHeightStyle: '',
    titleFontStyle: '',
    descFontStyle: '',
  },

  attached() {
    const itemData = transform(this.properties.componentData, {
      numberKeys: [
        // 样式类型。0: 传统样式，1: 微信图文样式
        'templateType',
        // 显示位置。0: 居左，1: 居中，2: 居右(微信图文样式)
        'align',
        // 标题大小
        'titleSize',
        // 描述大小
        'descSize',
        // 标题字重
        'titleWeight',
        // 描述字重。0: 普通，1: 粗体
        'descWeight',
        // 链接样式。0: 文字，1: 文字+箭头，2: 箭头
        'linkStyle',
        // 链接类型。0: 引导关注，1: 普通链接
        'wxLinkType',
      ],
      booleanKeys: [
        // 底部分割线
        'showDivider',
        // 是否链接
        'isLink',
      ],
      // 传统样式的链接对象
      link: (value) => ({ ...value, ...getLink(value) }),
      // 微信样式的链接对象
      wxLink: (value) => ({ ...value, ...getLink(value) }),
    });

    this.setData({ ...itemData }, () => {
      this.setComputedData();
    });
  },

  methods: {
    setComputedData() {
      const needUpdate = {};

      Object.keys(COMPUTED).forEach((key) => {
        needUpdate[key] = COMPUTED[key].call(this);
      });

      this.setData(needUpdate);
    },

    handleNavigate() {
      const { isWx, link, wxLink } = this.data;
      const linkData = isWx ? wxLink : link;
      const { linkType, linkId } = linkData;
      this.sendClickLogger();
      if (linkType === 'video_number' && jumpVideoNumber(linkId)) return;
      this.navigateIncludeShelf(mapKeysCase.toSnakeCase(linkData));
    },

    handleContactBack({ detail = {} }) {
      this.triggerEvent('contactback', detail);
    },

    sendClickLogger() {
      this.ensureAppLogger('logger', {
        et: 'click',
        ei: 'click_content',
        en: '链接点击',
        params: {
          banner_id: this.getBannerId(),
          ...this.getComponentLoggerExtraParams(),
        },
      });
    },
  },
});
