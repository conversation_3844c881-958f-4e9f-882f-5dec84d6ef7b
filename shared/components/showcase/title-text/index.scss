.cap-title-text {
  display: block;

  /* 覆盖 cap-navigator 的样式 */
  padding: 0 16px !important;
  background-color: #fff !important;
  text-align: left !important;

  &-wrap {
    position: relative;
    padding: 16px 0;
    word-break: break-all;

    &--no-title {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
    }

    /* 居中 */
    &--center {
      display: flex;
      flex-direction: column-reverse;
      align-items: center;

      .cap-title-text__content {
        flex: auto;
        text-align: center;
      }

      .cap-title-text__link {
        margin: 8px 0 0;
      }
    }

    /* 居右 */
    &--right {
      .cap-title-text__content {
        text-align: right;
      }
    }

    /* 显示分割线 */
    &--has-divider {
      border-bottom: 1px solid #eee;
    }
  }

  &__link {
    float: right;
    display: flex;
    align-items: center;
    margin-left: 24px;
    font-size: 12px;
    line-height: 0;
    color: #969799;
    white-space: nowrap;

    &--only-text {
      color: #38f;
    }
  }

  &__link-text,
  &__arrow {
    font-size: inherit;
    line-height: 16px;
  }

  &__content {
    flex: 1;
  }

  &__title {
    margin: 0;
    font-size: 16px;
    line-height: 1.375;
    color: #323233;
    overflow: hidden;
  }

  &__sub {
    font-size: 12px;
    line-height: 1.375;
    color: #8c8c8c;

    &:nth-child(2) {
      margin-top: 8px;
    }
  }

  &__desc {
    display: inline-block;
    white-space: pre-wrap;
  }

  &__date,
  &__author {
    display: inline;
    margin-right: 10px;
  }

  &__wx-link {
    display: inline;
    color: #607fa6;
    font-size: inherit;
    line-height: inherit;
  }
}
