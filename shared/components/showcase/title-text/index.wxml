<cap-navigator
  link-type="{{ isWx ? '' : link.linkType }}"
  app-id="{{ isWx ? '' : link.appId }}"
  path="{{ isWx ? '' : link.path }}"
  use-short-link="{{ isWx ? '' : link.useShortLink }}"
  short-link="{{ isWx ? '' : link.shortLink }}"
  custom-class="cap-title-text"
  custom-style="{{ isWx ? '' : 'background-color:' + bgColor + ' !important' }}"
  im="{{ extraData }}"
  bind:navigate="{{ isWx ? '' : 'handleNavigate' }}"
  bind:contactback="{{ isWx ? '' : 'handleContactBack' }}"
>
  <view class="{{ wrapClass }}">
    <view
      class="cap-title-text__link {{ onlyLinkText ? 'cap-title-text__link--only-text' : '' }}"
      wx:if="{{ isLink && !alignRight && !isWx }}"
      style="{{ linkHeightStyle }}"
    >
      <view class="cap-title-text__link-text" wx:if="{{ showLinkText && linkText }}">{{ linkText }}</view>
      <van-icon wx:if="{{ showLinkArrow }}" name="arrow" custom-class="cap-title-text__arrow" />
    </view>

    <view class="cap-title-text__content">
      <view class="cap-title-text__title" wx:if="{{ title }}" style="{{ isWx ? '' : titleFontStyle }}">{{ title }}</view>
      <view class="cap-title-text__sub" wx:if="{{ showSub }}">
        <block wx:if="{{ isWx }}">
          <view class="cap-title-text__date" wx:if="{{ wxDate }}">{{ wxDate }}</view>
          <view class="cap-title-text__author" wx:if="{{ wxAuthor }}">{{ wxAuthor }}</view>
          <block wx:if="{{ wxLinkTitle }}">
            <view class="cap-title-text__wx-link" wx:if="{{ wxLinkType == 0 }}">{{ wxLinkTitle }}</view>
            <cap-navigator
              wx:else
              link-type="{{ wxLink.linkType }}"
              app-id="{{ wxLink.appId }}"
              use-short-link="{{ wxLink.useShortLink }}"
              short-link="{{ wxLink.shortLink }}"
              path="{{ wxLink.path }}"
              custom-class="cap-title-text__wx-link"
              im="{{ extraData }}"
              bind:navigate="handleNavigate"
              bind:contactback="handleContactBack"
            >
              {{ wxLinkTitle }}
            </cap-navigator>
          </block>
        </block>
        <view class="cap-title-text__desc" wx:else style="{{ descFontStyle }}">{{ desc }}</view>
      </view>
    </view>
  </view>
</cap-navigator>
