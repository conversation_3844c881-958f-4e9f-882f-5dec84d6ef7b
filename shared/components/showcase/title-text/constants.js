export const TEMPLATE_TYPE = {
  NORMAL: 0,
  WX: 1
};

export const ALIGN = {
  LEFT: 0,
  CENTER: 1,
  RIGHT: 2
};

export const LINK_STYLE = {
  TEXT: 0,
  TEXT_ICON: 1,
  ICON: 2
};

export const WEAPP_LINK_TYPE = {
  FOLLOW: 0,
  LINK: 1
};

export const LINK_LINE_HEIGHT = 1.375;

export const COMPUTED = {
  wrapClass() {
    const {
      templateType,
      align,
      title,
      showDivider,
    } = this.data;
    const alignText = ['left', 'center', 'right'][align];

    return `
      cap-title-text-wrap
      cap-title-text-wrap--${alignText}
      ${templateType !== TEMPLATE_TYPE.WX && !title ? 'cap-title-text-wrap--no-title' : ''}
      ${templateType !== TEMPLATE_TYPE.WX && showDivider ? 'cap-title-text-wrap--has-divider' : ''}
    `;
  },

  // 是否微信图文样式
  isWx() {
    return this.data.templateType === TEMPLATE_TYPE.WX;
  },

  // 是否引导关注
  isWxFollow() {
    return this.data.wxLinkType === WEAPP_LINK_TYPE.FOLLOW;
  },

  showSub() {
    const {
      templateType,
      desc,
      wxDate,
      wxAuthor,
      wxLinkTitle,
    } = this.data;

    return (
      (templateType === TEMPLATE_TYPE.NORMAL && desc)
      || (templateType === TEMPLATE_TYPE.WX && (wxDate || wxAuthor || wxLinkTitle))
    );
  },

  // 是否显示链接文字
  showLinkText() {
    return this.data.linkStyle !== LINK_STYLE.ICON;
  },

  // 是否显示链接箭头图标
  showLinkArrow() {
    return this.data.linkStyle !== LINK_STYLE.TEXT;
  },

  // 仅仅显示链接文字
  onlyLinkText() {
    return this.data.linkStyle === LINK_STYLE.TEXT;
  },

  alignRight() {
    return this.data.align === ALIGN.RIGHT;
  },

  // 链接的高度样式
  linkHeightStyle() {
    const {
      align,
      title,
      desc,
      titleSize,
      descSize,
    } = this.data;

    if (align === ALIGN.CENTER) {
      return '';
    }

    if (title) {
      return `height: ${titleSize * LINK_LINE_HEIGHT}px`;
    }

    if (desc) {
      return `height: ${descSize * LINK_LINE_HEIGHT}px`;
    }

    return '';
  },

  titleFontStyle() {
    const { titleSize, titleWeight, titleColor } = this.data;
    // 处理android ios 对于 fontWeight兼容问题。
    const newTitleWeight = titleWeight === 500 ? 700 : titleWeight;
    return `font-size: ${titleSize}px; font-weight: ${newTitleWeight}; color: ${titleColor}`;
  },

  descFontStyle() {
    const { descSize, descWeight, descColor } = this.data;
    return `font-size: ${descSize}px; font-weight: ${descWeight}; color: ${descColor}`;
  },
};
