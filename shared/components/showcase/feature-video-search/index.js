import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

const SEARCH_HISTORY = 'feature-video-search-history';
const app = getApp();

Component({
  properties: {
    componentData: {
      type: Object,
      value: {}
    }
  },

  data: {
    showSearch: false,
    focus: false,
    searchText: '',
    hotSearchKeys: [],
    placeholder: '',
    borderStyleColor: '',
    searchBg: '',
    textColor: '',
    borderMethodStyle: 'rect',
    positionType: 0,
    textAlignMethod: 0,
    positionShowMethod: 0,
    historyList: [],
    showHistory: true
  },

  attached() {
    const _searchData = mapKeysCase.toCamelCase(this.data.componentData);

    this.setData({
      hotSearchKeys:
        _searchData.hotSearchKeys.length > 0 ? _searchData.hotSearchKeys : ['搜索专题视频'],
      placeholder: _searchData.hotSearchKeys.length ? _searchData.hotSearchKeys[0] : '搜索专题视频',
      borderStyleColor: _searchData.borderStyleColor,
      searchBg: _searchData.color,
      textColor: _searchData.textColor,
      borderMethodStyle: _searchData.borderStyleMethod == 0 ? 'rect' : 'circle',
      positionType: _searchData.positionType,
      textAlignMethod: _searchData.textAlignMethod,
      positionShowMethod: _searchData.positionShowMethod,
      showHistory: _searchData.showHistory === undefined ? true : _searchData.showHistory
    });
  },

  methods: {
    // 点击搜索框，弹出热门搜索视图
    search_handleSearchBarTap() {
      this.setData({
        showSearch: true
      });
      this.setHistoryList();
    },

    setHistoryList() {
      const self = this;
      wx.getStorage({
        key: SEARCH_HISTORY,
        success(res) {
          const historyList = res.data || [];
          self.setData({
            historyList
          });
        }
      });
    },

    // 响应输入
    search_handleInput({ detail: { value } }) {
      this.setData({
        searchText: value
      });
    },

    // 点击热门搜索项
    search_handleItemTap({
      target: {
        dataset: { text }
      }
    }) {
      this.setData(
        {
          searchText: text
        },
        this.search_handleSearch
      );
    },

    // 点击“取消”，返回上一视图
    search_handleCancelTap() {
      this.setData({
        searchText: '',
        showSearch: false
      });
    },

    // 存储搜索历史
    search_saveHistory(searchValue) {
      let historyList = this.data.historyList.filter(item => item !== searchValue);
      historyList.unshift(searchValue);
      historyList = historyList.slice(0, 10);
      this.setData({
        historyList
      });

      wx.setStorage({
        key: SEARCH_HISTORY,
        data: historyList
      });
    },

    // 清除搜索历史
    search_handleClearHistory() {
      this.setData({
        historyList: []
      });
      wx.removeStorage({
        key: SEARCH_HISTORY
      });
    },

    // 搜索
    search_handleSearch() {
      const searchText = this.data.searchText;
      const firstHotKey = this.data.hotSearchKeys[0];
      const that = this;
      if (!searchText) {
        // 搜索框为空
        if (!firstHotKey) {
          // 无预设热搜关键词
          wx.showToast({
            title: '搜索关键字不能为空',
            icon: 'none'
          });
          return;
        }

        this.setData({
          searchText: firstHotKey
        });
      } else if (searchText.length > 100) {
        // 搜索框字数超过 100
        wx.showToast({
          title: '搜索关键字不能超过100字',
          icon: 'none'
        });
        return;
      }

      const searchValue = searchText || firstHotKey;
      this.search_saveHistory(searchValue);
      app.logger.log({
        et: 'click',
        ei: 'search',
        en: '搜索',
        params: {
          words: searchValue
        },
        si: app.getKdtId()
      });
      // this.__yzLog__({
      //   et: 'click',
      //   ei: 'search',
      //   en: '搜索',
      //   params: {
      //     words: searchValue
      //   }
      // });
      // 发起搜索请求
      wx.navigateTo({
        url: `/packages/feature-video/search/index?q=${searchValue}`,
        success() {
          setTimeout(() => {
            that.setData({
              searchText: '',
              showSearch: false
            });
          }, 2000);
        }
      });
    }
  }
});
