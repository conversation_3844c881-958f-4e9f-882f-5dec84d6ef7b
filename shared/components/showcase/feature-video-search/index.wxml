<cap-goods-search
  class="component-search"
  textColor="{{ textColor }}"
  searchBg="{{ searchBg }}"
  hotSearchKeys="{{ hotSearchKeys }}"
  borderMethodStyle="{{ borderMethodStyle }}"
  borderStyleColor="{{ borderStyleColor }}"
  positionType="{{ positionType }}"
  textAlignMethod="{{ textAlignMethod }}"
  positionShowMethod="{{ positionShowMethod }}"
  bindtap="search_handleSearchBarTap"
/>

<view
  wx:if="{{ showSearch }}"
  class="hot-search-view"
>
  <cap-search
    alignLeft
    searchStyle="height: 100rpx; padding: 0 30rpx; background: #f2f2f2;"
    inputStyle="height: 80rpx; border-radius: 4rpx;"
    useCancel
    cancelStyle="font-size: 28rpx; color: #666;"
    keyword="{{ searchText }}"
    placeholder="{{ placeholder }}"
    focus
    bindchange="search_handleInput"
    bindsearch="search_handleSearch"
    bindcancel="search_handleCancelTap"
  />

  <view wx:if="{{ hotSearchKeys.length > 1 }}">
    <view class="hot-search-title">热门搜索</view>
    <view class="hot-search-list">
      <view
        class="hot-search-list-item"
        wx:for="{{ hotSearchKeys }}"
        wx:key="*this"
        wx:if="{{ index > 0 }}"
        bindtap="search_handleItemTap"
        data-text="{{ item }}"
      >
        {{ item }}
      </view>
    </view>
  </view>
  <view wx:if="{{ showHistory && historyList.length > 0 }}">
    <view class="hot-search-title">历史搜索</view>
    <view class="hot-search-list">
      <view
        class="hot-search-list-item"
        wx:for="{{ historyList }}"
        wx:key="*this"
        bindtap="search_handleItemTap"
        data-text="{{ item }}"
      >
        {{ item }}
      </view>
    </view>

    <view class="hot-search-view__clear-history-wrapper">
      <view
        class="hot-search-view__clear-history"
        size="mini"
        plain
        bindtap="search_handleClearHistory"
      >
        <image class="hot-search-view__clear-icon" src='https://img01.yzcdn.cn/weapp/wsc/5oqfL6LF.png'/>
        清空历史搜索
      </view>
    </view>
  </view>
</view>

<!--跳转页面地址 /packages/shop/goods/search/index -->
