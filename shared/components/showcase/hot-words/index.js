import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import jumpToLink from 'shared/components/showcase/utils/jumpToLink';
import { getWordConfigs, mapWords } from '@/utils/hot-words';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import Api from './api';
import { MODE, STYLE, THEME_STYLE } from './constants';
import { getPlugins } from '@youzan/ranta-helper-tee';

const app = getApp();
Component({
  behaviors: [componentBehavior, loggerBehavior],

  data: {
    show: false,
    typeReady: false,
    hot: [],
    normal: [],
    hotStyle: ``,
    isSingle: false,
    needOpacity: false,
  },

  properties: {
    alias: String,
  },

  attached() {
    const { mode, colorStyle, bgColor, textColor } = mapKeysCase.toCamelCase(
      this.data.componentData
    );

    this.getHotWords();

    if (mode === MODE.multi && colorStyle === STYLE.theme) {
      this.getGlobalTheme();
    } else {
      this.setData({
        hotStyle: `color: ${textColor}; background-color: ${bgColor};`,
        typeReady: true,
        isSingle: mode === MODE.single,
      });
    }
  },

  methods: {
    getHotWords() {
      Api.getHotWords().then((res) => {
        const { chosenWords = [], normalWords = [] } = res || {};
        const wordConfigs = getWordConfigs(res);
        const show =
          [].concat(chosenWords || [], normalWords || []).length >= 3;

        show && this.handleLog();
        this.setData({
          hot: mapWords(chosenWords, true, wordConfigs.chosenWords || []),
          normal: mapWords(normalWords, false, wordConfigs.normalWords || []),
          show,
        });
      });
    },
    getGlobalTheme() {
      app.getShopTheme().then((res) => {
        const { mode, themeStyle } = mapKeysCase.toCamelCase(
          this.data.componentData
        );
        const { colors = {} } = res || {};
        const themeColor = colors['main-bg'];
        const color = `color: ${
          themeStyle === THEME_STYLE.dark ? '#fff' : themeColor
        };`;
        const backgroundColor = `background-color: ${
          themeStyle === THEME_STYLE.dark ? themeColor : ''
        }`;

        this.setData({
          hotStyle: `${color}${backgroundColor}`,
          needOpacity: themeStyle === THEME_STYLE.light,
          typeReady: true,
          isSingle: mode === MODE.single,
        });
      });
    },
    handleSearch(e) {
      const { wordItem = {}, index } = e.detail;
      const bannerId = this.getBannerId();
      const loggerParams = {
        words: wordItem.text || '',
        banner_id: bannerId,
        component_index: index,
      };
      this.handleClickLog(loggerParams);
      this.startSearch(
        { text: wordItem.text, bannerId, loggerParams },
        wordItem
      );
    },

    startSearch(data, wordItem = {}) {
      const { text: keywords = '', bannerId, loggerParams } = data;

      if (wordItem.link_switch && wordItem.link_url) {
        jumpToLink(wordItem.link_type, wordItem);
        return;
      }

      if (keywords) {
        this.handleSearchLog(loggerParams);
        const { dmc } = getPlugins();
        dmc.redirectTo('Search', {
          q: keywords,
          banner_id: bannerId,
        });
      }
    },
    handleLog() {
      this.ensureAppLogger('logger', {
        et: 'view',
        ei: 'component_view',
        en: '组件曝光',
        params: {
          component: 'hot_words_reference',
          banner_id: this.getBannerId(),
        },
      });
    },
    // 记录点击事件埋点
    handleClickLog(params) {
      this.ensureAppLogger('logger', {
        et: 'click',
        ei: 'click_content',
        en: '内容点击',
        params,
      });
    },
    handleSearchLog(params) {
      this.ensureAppLogger('logger', {
        et: 'click',
        ei: 'search',
        en: '搜索',
        params: {
          ...params,
          s_type: 'hot-words',
          template_alias: this.data.alias,
        },
      });
    },
  },
});
