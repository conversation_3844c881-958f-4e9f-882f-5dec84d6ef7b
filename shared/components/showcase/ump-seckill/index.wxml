<cap-list
  loading="{{ isLoading }}"
  finished="{{ !needLoadMore }}"
  disabled="{{ isSwipeLayout }}"
  bind:load="handleLoadMore"
>
  <cap-seckill
    id="cap-seckill"
    wx:if="{{ isComponentShow }}"
    app-id="{{ appId }}"
    list="{{ itemData.goodsList }}"
    layout="{{ itemData.layout }}"
    size-type="{{ itemData.sizeType }}"
    show-title="{{ itemData.showTitle }}"
    show-sub-title="{{ itemData.showSubTitle }}"
    show-price="{{ itemData.showPrice }}"
    show-origin-price="{{ itemData.showOriginPrice }}"
    show-buy-button="{{ itemData.showBuyButton }}"
    image-ratio="{{ itemData.imageRatio }}"
    image-fill-style="{{ itemData.imageFillStyle }}"
    text-align-type="{{ itemData.textAlignType }}"
    text-style-type="{{ itemData.textStyleType }}"
    border-radius-type="{{ itemData.borderRadiusType }}"
    page-margin="{{ itemData.pageMargin }}"
    goods-margin="{{ itemData.goodsMargin }}"
    buy-button-type="{{ itemData.buyButtonType }}"
    button-text="{{ itemData.buttonText }}"
    show-stock-num="{{ itemData.showStockNum }}"
    show-count-down="{{ itemData.showCountDown }}"
    redirect-type="{{ redirectType }}"
    extra-data="{{ extraData }}"
    page-data="{{ pageData }}"
    page-random-number="{{ pageRandomNumber }}"
    open-swipe-pagination="{{ isSwipeLayout && itemData.goodsList.length !== 0 && needLoadMore }}"
    skeleton-number="{{ skeletonNumber }}"
    bind:buy="handleGoodsBuy"
    bind:item-click="handleGoodsBuy"
    bind:load-more="handleLoadMore"
  />
</cap-list>
