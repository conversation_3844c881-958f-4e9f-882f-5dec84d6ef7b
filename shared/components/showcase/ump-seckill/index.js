import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import Args from '@youzan/weapp-utils/lib/args';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import getConcatData from '@youzan/weapp-utils/lib/get-concat-data';
import componentBehavior from '../behaviors/component-behavior';
import countdownUtilBehavior from '../captain-components/ump-goods-layout/behaviors/countdown-util-behavior';
import goodsComponentBehavior from '../behaviors/goods-component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { getUpdateCountdownGoodsList } from '../captain-components/ump-goods-layout/helper';
import { GOODS_TYPE } from '../captain-components/goods-layout/constants';

import { getSeckillData } from './api';
import navigate from '../../../utils/navigate';

const REFRESH_TYPE = {
  UPDATE_COUNTDOWN: 'update_countdown',
  UPDATE_LIST: 'update_list',
};

Component({
  behaviors: [
    componentBehavior,
    countdownUtilBehavior,
    loggerBehavior,
    goodsComponentBehavior,
  ],

  properties: {
    redirectType: {
      type: Number,
      value: 3,
    },

    pageRandomString: {
      type: String,
      value: '',
      observer() {
        // 没有经过adaptor 不触发倒计时更新
        const { itemData } = this.data;

        if (!itemData.type) return;

        this.initSeckill(REFRESH_TYPE.UPDATE_COUNTDOWN);
      },
    },
  },

  data: {
    itemData: {},
    isComponentShow: false,
    isLoading: false,
    page: 1,
    skeletonNumber: 0,
  },

  attached() {
    this.accumulativeDataLength = 0;
    this.initSeckill(REFRESH_TYPE.UPDATE_LIST);
  },

  methods: {
    /**
     * 下拉
     */
    onPullDownRefresh() {
      this.initSeckill(REFRESH_TYPE.UPDATE_LIST);
    },

    initSeckill(type) {
      const { isLoading, componentData = {} } = this.data;
      if (isLoading) return;

      const itemData = mapKeysCase.toCamelCase(componentData);

      let data = {
        isLoading: true,
      };

      if (type === REFRESH_TYPE.UPDATE_LIST) {
        data = {
          ...data,
          isSwipeLayout: this.checkIsSwipeLayout(itemData.layout),
          page: 1,
          itemData,
          needLoadMore: true,
        };
      }

      this.setData(data, () => {
        if (type === REFRESH_TYPE.UPDATE_LIST) {
          this.getSeckillData();
        } else if (type === REFRESH_TYPE.UPDATE_COUNTDOWN) {
          this.refreshCountDown();
        }
      });
    },

    getSeckillData() {
      const {
        componentData = {},
        kdtId,
        page,
        isSwipeLayout,
        itemData = {},
      } = this.data;
      const { layout } = itemData;

      const {
        activityId,
        activityIds = [],
        seckillIds,
        hideGoodsEnd = '1',
        hideGoodsSold = '1',
        goodsEndType = '1',
      } = mapKeysCase.toCamelCase(componentData);

      const pageSize = this.getPageSize(layout);

      const allSeckillIds = (
        activityId ||
        seckillIds ||
        activityIds.join(',')
      ).split(',');
      const seckillStartPos = (page - 1) * pageSize;
      const fetchSeckillIds = allSeckillIds
        .slice(seckillStartPos, seckillStartPos + pageSize)
        .join(',');

      getSeckillData({
        kdtId,
        seckillIds: fetchSeckillIds,
        hideGoodsEnd,
        hideGoodsSold,
        goodsEndType,
        adaptor: true,
        showKnowledge: 1,
      })
        .then((data) => {
          if ((!isEmpty(data) && data) || data.length) {
            const loggerList = [];
            const goodsList = [];
            mapKeysCase.toCamelCase(data).forEach((item, index) => {
              const { id, activityAlias, goodsId, goodsAlias } = item;
              // banner_id
              const bannerId = this.getBannerId(index + 1);

              const loggerParams = {
                goods_id: goodsId,
                banner_id: bannerId,
                item_type: 'goods',
                item_id: goodsId,
                act_type: 6,
                act_id: id + '',
                component: this.getComponentLoggerType(),
                ...this.getComponentLoggerExtraParams(),
              };
              loggerList.push(loggerParams);
              item.loggerParams = loggerParams;
              // 定制老商详 不支持 秒杀 /pages/goods/detail/index 跳转
              item.url = Args.add('/packages/goods/seckill/index', {
                alias: activityAlias,
                goodsAlias,
                banner_id: bannerId,
                ...this.getComponentLoggerExtraParams(),
              });
              item.hideImageStatus = true;

              const { goodsType } = item;
              // 课程商品
              if (+goodsType === GOODS_TYPE.KNOWLEDGE) {
                const url = Args.add(
                  'https://h5.youzan.com/wscvis/edu/prod-detail',
                  {
                    alias: goodsAlias,
                    ump_alias: activityAlias,
                    kdt_id: kdtId,
                    ump_type: 'seckill',
                    activityType: 'seckill',
                  }
                );

                item.url = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
                  url
                )}`;
                goodsList.push(item);
              } else {
                goodsList.push(item);
              }
            });

            this.ensureAppLogger('view', loggerList);
            const listData = getConcatData(
              goodsList,
              'itemData.goodsList',
              this.data.itemData.goodsList.length
            );

            this.accumulativeDataLength += data.length;
            this.setData({
              ...listData,
            });
          }

          const nextStartPos = page * pageSize;
          const nextPageSeckillIds = allSeckillIds.slice(
            nextStartPos,
            nextStartPos + pageSize
          );
          const needLoadMore =
            nextPageSeckillIds && nextPageSeckillIds.length !== 0;

          this.setData(
            {
              isComponentShow: true,
              isLoading: false,
              needLoadMore,
              page: page + 1,
              skeletonNumber: 0,
            },
            () => {
              if (isSwipeLayout) {
                this.selectComponent('#cap-seckill').setLoadStatus({
                  loading: false,
                  swipeFinished: !needLoadMore,
                });
              }

              this.handleLoadLessThanPagesize(pageSize);
            }
          );
        })
        .catch(() => {
          this.setData({
            isComponentShow: true,
            isLoading: false,
            skeletonNumber: 0,
          });
        });
    },

    refreshCountDown() {
      const { itemData, pageRandomString } = this.data;

      const { goodsList = [] } = itemData;

      const prefix = 'itemData.goodsList';
      const curGoodsList = getUpdateCountdownGoodsList(
        prefix,
        goodsList,
        pageRandomString
      );

      this.setData({
        ...curGoodsList,
        isComponentShow: true,
        isLoading: false,
      });
    },

    handleGoodsBuy(e) {
      navigate.navigate({
        url: e.detail.url,
      });
    },

    // 处理当前页返回数量不足个数时，不再加载下一页数据
    handleLoadLessThanPagesize(pageSize) {
      // 累计加载数据不足一页时
      if (this.accumulativeDataLength < pageSize) {
        this.handleLoadMore();

        return;
      }

      // 还原累计加载数据
      this.accumulativeDataLength = 0;
    },

    handleLoadMore() {
      const { isLoading, needLoadMore } = this.data;

      if (isLoading || !needLoadMore) return;

      this.setData(
        {
          isLoading: true,
          skeletonNumber: 2,
        },
        () => {
          this.getSeckillData();
        }
      );
    },
  },
});
