import { GuideType } from './constant';
import buildUrl from '@youzan/utils/url/buildUrl';
import Args from '@youzan/utils/url/args';

export const handleGetCouponError = (err) => {
  const { code = '', msg = '' } = err;

  // 系统信息不提示
  if (!code || +code < 10000) {
    return;
  }

  let content = '';
  switch (+err.code) {
    case 160700509:
      content = '已抢光';
      break;
    case 160700510:
      content = '已失效';
      break;
    default:
      content = msg || '领取失败';
      break;
  }

  wx.showModal({
    title: '提示',
    content,
    showCancel: false,
  });
};

export const padZero = (num) => {
  return (num < 10 ? '0' : '') + num;
};

export const getGuideInfo = ({
  guide_type,
  guide_target_alias,
  redirectUrl,
  kdtId,
}) => {
  let h5Url = '';
  let confirmButtonText = '';
  let weappUrl = '';
  if (guide_type === GuideType.FreeMember) {
    h5Url = buildUrl(
      Args.add('/wscuser/levelcenter/fill', {
        kdt_id: kdtId,
        alias: guide_target_alias,
        fromScene: 'complete',
        guideType: 'coupon',
        redirectUrl,
        eT: Date.now(),
      }),
      'h5',
      kdtId
    );
    weappUrl = `/pages/common/webview-page/index?src=${encodeURIComponent(
      h5Url
    )}`;
    confirmButtonText = '注册会员';
  } else if (guide_type === GuideType.PaidMember) {
    h5Url = Args.add('https://cashier.youzan.com/pay/wscuser_paylevel', {
      kdt_id: kdtId,
      alias: guide_target_alias,
      guideType: 'coupon',
      redirectUrl,
    });
    weappUrl = Args.add('/packages/shop/levelcenter/plus/index', {
      kdt_id: kdtId,
      alias: guide_target_alias,
      guideType: 'coupon',
      redirectUrl,
    });
    confirmButtonText = '开通会员';
  } else if (guide_type === GuideType.BenefitCard) {
    h5Url = buildUrl(
      Args.add('/wscuser/scrm/benefitcard', {
        kdt_id: kdtId,
        card_alias: guide_target_alias,
        shopAutoEnter: 1,
        guideType: 'coupon',
        redirectUrl,
      }),
      'h5',
      kdtId
    );
    weappUrl = Args.add('/packages/card/detail/index', {
      kdt_id: kdtId,
      alias: guide_target_alias,
      shopAutoEnter: 1,
      guideType: 'coupon',
      redirectUrl,
    });
    confirmButtonText = '立即开卡';
  }
  return { weappUrl, confirmButtonText };
};
