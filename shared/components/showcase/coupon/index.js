import WscComponent from 'shared/common/base/wsc-component/index';
import getAuthorizedState from 'shared/utils/get-authorized-state';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import buildUrl from '@youzan/utils/url/buildUrl';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import navigate from '@/helpers/navigate';
import Event from '@youzan/weapp-utils/lib/event';
import api from './api.js';
import {
  LAY_OUT_MAP,
  STYLE7_IMG,
  FONT_SIZE_MAP,
  GuideType,
  ReceiveCouponType,
  LogViewEvent,
  LogClickEvent,
} from './constant';
import { getGuideInfo } from './helper';

const COUPON_MAX_DISPLAY_NUMBER = 10;

WscComponent({
  behaviors: [componentBehavior, loggerBehavior],

  data: {
    coupons: [],
    layoutClass: '',
    config: {},
    guideInfo: {
      display: false,
    },
    isNormalTemplate: true,
    isRightImgStyle: false,
    disabledTextClass: true,
    dialogTitle: '已经添加至我的优惠券',
    isCrossStore: false,
    hasMobile: false,
  },

  attached() {
    const { componentData, kdtId } = this.data;
    const { coupon_active_type: couponAticeType } = componentData;
    this.setYZData({
      isCrossStore: couponAticeType === 1,
    });

    this.initCoupons(
      mapKeysCase.toCamelCase({
        coupon_style: 1,
        coupon_color: 1,
        kdtId,
        ...componentData,
      })
    );
  },

  methods: {
    setDisabledClass(coupons = []) {
      let flag = false;
      const { componentData = {} } = this.data;
      let { layout, coupon_style: couponStyle = 1 } = componentData;
      if (!layout) layout = coupons.length;
      layout = +layout;
      couponStyle = +couponStyle;
      const isSwipe = layout === 3 || layout === 4;
      const isOne = layout === 1;
      const isTwo = layout === 2;
      if (
        isOne ||
        (isTwo && couponStyle !== 7 && couponStyle !== 8) ||
        (isSwipe && (couponStyle === 6 || couponStyle === 8))
      ) {
        flag = true;
      }
      return flag;
    },

    getRightImg(_item, disabled) {
      const { layout = 4, coupon_style: couponStyle = 1 } =
        this.data.componentData;
      let { coupon_color: couponColor = 1 } = this.data.componentData;
      if (disabled) couponColor = 6;
      if (+couponStyle === 7 && (layout === 1 || layout === 2)) {
        return STYLE7_IMG[layout][couponColor];
      }
      return '';
    },

    getLayoutClass(config, coupons = []) {
      let { layout } = config;
      if (!layout) {
        layout = coupons.length > 3 ? 4 : coupons.length;
      }
      return `cap-coupon__item--${LAY_OUT_MAP[layout]} ${
        +layout !== 1 ? 'cap-coupon__item--multi' : ''
      }`;
    },

    async initCoupons(itemData) {
      const authorizedState = await getAuthorizedState();
      const { isCrossStore } = this.data;
      const couponList =
        (isCrossStore ? itemData.platformCoupon : itemData.coupon) || [];
      const couponIds = couponList.map((item) => {
        return item.id;
      });
      const apiSearchCoupons = isCrossStore
        ? this.fetchCrossStoreCouponList
        : this.fetchSingleStoreCouponList;
      apiSearchCoupons(itemData).then((result) => {
        const coupons = this.formatCoupon(result || []);
        if (coupons.length) {
          this.sendViewLogger(coupons);
          this.setYZData({
            coupons,
            config: itemData,
            layoutClass: this.getLayoutClass(itemData, coupons),
            isNormalTemplate: itemData.couponStyle !== 6,
            lookMore: couponIds.length > 6,
            loaded: true,
            disabledTextClass: this.setDisabledClass(coupons),
            hasMobile: authorizedState.mobile,
          });
        }
      });
    },
    fetchSingleStoreCouponList(itemData) {
      const { coupon: couponList = [], couponNum, kdtId } = itemData;
      let pageSize = +couponNum;
      if (pageSize === 0 || pageSize > COUPON_MAX_DISPLAY_NUMBER) {
        pageSize = COUPON_MAX_DISPLAY_NUMBER;
      }

      const ids = couponList
        .map((coupon) => coupon.id)
        .slice(0, pageSize)
        .join(',');

      const params = {
        ids,
        pageNum: 1,
        pageSize,
        type: 1,
        timelineStatus: 2,
        source: 'wap_showcase',
        kdtId,
        activityTypeGroup: 1,
        includeNotSupportSend: itemData.hideEmptyCoupon,
        isDisableUnshare: itemData.hideUnsharedCoupon,
        bizIdentity: 'SEC',
        bizScene: 'EC_BIZ_SD_VOUCHER_COMP',
      };
      return api.searchCoupons(params);
    },
    fetchCrossStoreCouponList(itemData) {
      const { platformCoupon, kdtId, hideEmptyCoupon } = itemData;
      const platformCouponAlias =
        (platformCoupon &&
          platformCoupon.map((coupon) => coupon.promoterResourceAlias)) ||
        [];
      const params = {
        kdtId,
        alias: platformCouponAlias.join(','),
        source: 1,
        hideInvalid: +hideEmptyCoupon,
      };
      return api.searchPlatformCoupons(params);
    },

    getCouponText(item) {
      const {
        activityBudgetDTO: { sendStockQty },
        activityDTO: { status },
      } = item;
      const { componentData = {} } = this.data;
      const { layout = 4, couponStyle = 1 } = componentData;
      let text;
      if (couponStyle !== 7) {
        // eslint-disable-next-line no-nested-ternary
        text = status === 1 ? '已失效' : sendStockQty === 0 ? '已抢光' : '';
      }
      if (text) return text;
      if (+layout === 1) return '立即领取';
      return '领取';
    },

    getFontSize(value = '') {
      const { componentData = {} } = this;
      const { couponStyle = 1, layout = 4 } = componentData;
      const fontLength = value.length;
      const key =
        +couponStyle !== 6 && +couponStyle !== 7 ? 'normal' : couponStyle;
      // eslint-disable-next-line no-nested-ternary
      const temp = fontLength > 7 ? 7 : fontLength < 3 ? 3 : fontLength;
      return FONT_SIZE_MAP[key][+layout][temp];
    },

    // 格式化优惠券数据
    formatCoupon(coupons) {
      const { componentData = {} } = this.data;
      const { coupon = [] } = componentData;
      return Array.isArray(coupons)
        ? coupons.map((item) => {
            const {
              activityDTO = {},
              activityBudgetDTO = {},
              voucherUsingRuleDTO = {},
              voucherGenRuleDTO = {},
              thresholdCopywriting,
              valueCopywriting,
              unitCopywriting,
              applicableGoodRangeDesc,
              shopName,
              promoterResourceAlias,
              sign = '',
            } = item;
            const { status, title, alias, id } = activityDTO;
            const { sendStockQty } = activityBudgetDTO;
            const { thresholdAmount: condition } = voucherUsingRuleDTO;
            const { preferentialMode: preferentialType } = voucherGenRuleDTO;
            const text = this.getDisabledText(status, sendStockQty);
            const disabled = !!text;
            const rightImg = this.getRightImg(item, disabled);
            let newSign = sign || '';
            if (coupon.length !== 0) {
              const findCoupon = coupon.find(
                (couponItem) => couponItem.id === alias
              );
              newSign = findCoupon && findCoupon.sign ? findCoupon.sign : '';
            }
            return {
              id,
              alias,
              sign: newSign,
              promoterResourceAlias,
              price0: item.valueCopyWriting,
              price1: item.unitCopyWriting,
              atLeast: (condition / 100).toFixed(2),
              discount: item.discount,
              disabledText: text,
              disabled,
              text: this.getCouponText(item),
              title,
              thresholdCopywriting,
              formativeContext: item.formative_context,
              valueCopyWriting: valueCopywriting,
              unitCopyWriting: unitCopywriting,
              isRecevied: false,
              conditionText: `${item.thresholdCopywriting},${item.applicableGoodRangeDesc}`,
              rightImg,
              status: 1,
              applicableGoodRangeDesc,
              preferentialType,
              isIncludeChineseCharacter: /\p{Unified_Ideograph}/u.test(
                valueCopywriting
              ),
              characterFontSize: this.getFontSize(valueCopywriting),
              shopName,
            };
          })
        : [];
    },

    getDisabledText(invalid, stock) {
      if (invalid === 1) {
        return '已失效';
      }

      if (stock === 0) {
        return '已抢光';
      }

      return '';
    },

    onClickCoupon(event) {
      const { coupons, isCrossStore } = this.data;
      const { index } = event.currentTarget.dataset;
      const coupon = coupons[index];
      if (!coupon) return;

      if (coupon.status === 3 && coupon.userCouponId) {
        api
          .fetchCouponGoodsLink({
            couponId: coupon.userCouponId,
            groupType: 'card',
          })
          .then((res) => {
            const { weappUrl = '', isSwitchTab } = res;
            if (weappUrl) {
              if (isSwitchTab) {
                navigate.switchTab({
                  url: weappUrl,
                });
              } else {
                navigate.navigate({
                  url: weappUrl,
                });
              }
            }
          })
          .catch((e) => {
            if (e && e.msg) {
              wx.showToast({
                title: e.msg,
                icon: 'none',
              });
            }
          });
        return;
      }

      if (coupon.disabled) {
        return;
      }

      this.sendClickLogger(coupon);

      if (isCrossStore) {
        Event.trigger('feature-platform-coupon:open', coupon);
        return;
      }
      this.receiveCoupon({ index, type: ReceiveCouponType.openGuideDialog });
    },

    sendViewLogger(coupons) {
      const loggerList = [];
      coupons.forEach((coupon, index) => {
        coupon.index = index;
        coupon.loggerParams = {
          index: index + 1,
          id: coupon.id,
          item_id: coupon.id,
          act_id: coupon.id,
          act_type: 105,
          item_type: 'promocard',
          component: 'promocard',
          banner_id: this.getBannerId(index + 1),
          ...this.getComponentLoggerExtraParams(),
        };

        loggerList.push(coupon.loggerParams);
      });

      this.ensureAppLogger('logger', {
        et: 'view',
        ei: 'view',
        en: '优惠券曝光',
        params: {
          view_objs: loggerList,
        },
      });
    },

    sendClickLogger(coupon) {
      this.ensureAppLogger('logger', {
        et: 'click',
        ei: 'click_content',
        en: '点击',
        params: coupon.loggerParams || {},
      });
    },

    dialogConfirm() {
      const { guideInfo, guideType } = this.data;
      this.ensureAppLogger('logger', LogClickEvent[guideType]);
      this.setYZData({
        guideInfo: {
          ...guideInfo,
          display: false,
        },
      });
      navigate.navigate({
        url: guideInfo.weappUrl || '',
      });
    },

    dialogCancel() {
      const { guideInfo } = this.data;
      this.setYZData({
        guideInfo: {
          ...guideInfo,
          display: false,
        },
      });
    },

    receiveCoupon({ index, type }) {
      const { kdtId, componentData, coupons, guideInfo, hasMobile } = this.data;
      const coupon = coupons[index];
      const { layout = 4, risk_type, risk_alias } = componentData;
      const params = {
        groupId: coupon.alias,
        alias: coupon.alias,
        sign: coupon.sign || '',
        source: 'wap_showcase',
        kdtId,
        pageAlias: risk_alias,
        pageType: risk_type,
      };
      api
        .receiveCoupon(params)
        .then((data) => {
          const { id, need_guide } = data;
          if (need_guide) {
            if (type === ReceiveCouponType.openGuideDialog) {
              const redirectUrl = buildUrl(
                `/wscump/coupon/fetch?alias=${coupon.alias}&shopAutoEnter=1`,
                'h5',
                kdtId
              );
              const { guide_type, guide_target_alias } = data;
              const { confirmButtonText, weappUrl } = getGuideInfo({
                guide_type,
                guide_target_alias,
                redirectUrl,
                kdtId,
              });
              const guideInfo = {
                title:
                  guide_type === GuideType.BenefitCard
                    ? '获得权益卡即可领券'
                    : '开通会员即可领券',
                message: '当前身份不满足领取条件',
                confirmButtonText,
                display: true,
                weappUrl,
              };
              if (hasMobile) {
                // 同失败分支
                if (coupon.status === 1) {
                  coupon.disabled = true;
                  coupon.text = '领取';
                } else if (coupon.status === 2) {
                  if (+layout === 1) {
                    coupon.text = '立即使用';
                  }
                  coupon.status = 3;
                }
              }
              this.ensureAppLogger('logger', LogViewEvent[guide_type]);
              this.setYZData({
                guideType: guide_type,
                coupons,
                guideInfo,
                couponIndex: index,
              });
            }
            if (type === ReceiveCouponType.guideResult) {
              // 同失败分支
              if (coupon.status === 1) {
                coupon.disabled = true;
                coupon.text = '领取';
              } else if (coupon.status === 2) {
                if (+layout === 1) {
                  coupon.text = '立即使用';
                }
                coupon.status = 3;
              }
              this.setYZData({
                coupons,
                guideInfo: {
                  ...guideInfo,
                  display: false,
                },
              });
              navigate.navigate({
                url: guideInfo.weappUrl || '',
              });
            }
          } else {
            wx.showToast({
              title: '领取成功',
              icon: 'none',
            });

            coupon.userCouponId = id;
            coupon.isRecevied = true;
            if (+layout === 1) {
              coupon.text = '继续领取';
            }
            coupon.status = 2;
            this.setYZData({
              coupons,
              guideInfo: {
                ...guideInfo,
                display: false,
              },
            });
          }
        })
        .catch((err) => {
          if (coupon.status === 1) {
            coupon.disabled = true;
            coupon.text = '领取';
          } else if (coupon.status === 2) {
            if (+layout === 1) {
              coupon.text = '立即使用';
            }
            coupon.status = 3;
          }
          wx.showToast({
            title: err.msg,
            icon: 'none',
          });
          this.setYZData({
            coupons,
            guideInfo: {
              ...guideInfo,
              display: false,
            },
          });
        });
    },
  },
});
