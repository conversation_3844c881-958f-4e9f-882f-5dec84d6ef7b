export const LAY_OUT_MAP = {
  1: 'one',
  2: 'two',
  3: 'three',
  4: 'swipe',
};

export const STYLE7_IMG = {
  1: {
    1: 'https://b.yzcdn.cn/public_files/8e46e87e7540fea1edf35bc7a128b03e.png',
    2: 'https://b.yzcdn.cn/public_files/4b331cb62abe0ee779fdd36aa379ce6f.png',
    3: 'https://b.yzcdn.cn/public_files/fee29048c599c581a923e6138c4a05e3.svg',
    4: 'https://b.yzcdn.cn/public_files/b47cd3a49b7e407723f2f6b4d10d15cb.png',
    5: 'https://b.yzcdn.cn/public_files/52172668e338ffd2d5b962d8b5043411.png',
    6: 'https://b.yzcdn.cn/public_files/2caa957aa42433f3921dc497ca4a88d5.png',
  },
  2: {
    1: 'https://b.yzcdn.cn/public_files/b9417dfa10326d7fb008f934eb3f5065.png',
    2: 'https://b.yzcdn.cn/public_files/2d9ef7f2c67ba6271c07c559d5656d54.png',
    3: 'https://b.yzcdn.cn/public_files/b9417dfa10326d7fb008f934eb3f5065.png',
    4: 'https://b.yzcdn.cn/public_files/bf7e7af08bfc632c8ad55620c600c773.png',
    5: 'https://b.yzcdn.cn/public_files/023bf33a26cc9d9149722a33f3792be8.png',
    6: 'https://b.yzcdn.cn/public_files/19ee53aa7c85eec2f0f678cb9e0495a8.png',
  },
};
export const FONT_SIZE_MAP = {
  normal: {
    1: {
      3: 20,
      4: 20,
      5: 20,
      6: 17,
      7: 17,
    },
    2: {
      3: 20,
      4: 20,
      5: 20,
      6: 20,
      7: 20,
    },
    3: {
      3: 20,
      4: 20,
      5: 20,
      6: 17,
      7: 15,
    },
    4: {
      3: 20,
      4: 20,
      5: 20,
      6: 17,
      7: 15,
    },
  },
  6: {
    1: {
      3: 26,
      4: 24,
      5: 20,
      6: 18,
      7: 16,
    },
    2: {
      3: 12,
      4: 12,
      5: 12,
      6: 12,
      7: 12,
    },
    3: {
      3: 18,
      4: 16,
      5: 16,
      6: 14,
      7: 14,
    },
    4: {
      3: 20,
      4: 20,
      5: 18,
      6: 16,
      7: 16,
    },
  },
  7: {
    1: {
      3: 28,
      4: 28,
      5: 28,
      6: 28,
      7: 28,
    },
    2: {
      3: 22,
      4: 22,
      5: 22,
      6: 18,
      7: 18,
    },
    3: {
      3: 20,
      4: 20,
      5: 18,
      6: 16,
      7: 16,
    },
    4: {
      3: 20,
      4: 20,
      5: 18,
      6: 16,
      7: 16,
    },
  },
};
export const GuideType = {
  FreeMember: 1,
  PaidMember: 2,
  BenefitCard: 3,
};

export const ReceiveCouponType = {
  openGuideDialog: 1,
  guideResult: 2,
};

export const LogViewEvent = {
  [GuideType.FreeMember]: {
    et: 'view',
    ei: 'view_member_free_poster',
    en: '微页面-引导免费会员',
    params: {},
  },
  [GuideType.PaidMember]: {
    et: 'view',
    ei: 'view_member_pay_poster',
    en: '微页面-引导付费会员',
    params: {},
  },
  [GuideType.BenefitCard]: {
    et: 'view',
    ei: 'view_member_card_poster',
    en: '微页面-引导权益卡',
    params: {},
  },
};

export const LogClickEvent = {
  [GuideType.FreeMember]: {
    et: 'click',
    ei: 'click_member_free_poster',
    en: '微页面-点击免费会员引导',
    params: {},
  },
  [GuideType.PaidMember]: {
    et: 'click',
    ei: 'click_member_pay_poster',
    en: '微页面-点击付费会员引导',
    params: {},
  },
  [GuideType.BenefitCard]: {
    et: 'click',
    ei: 'click_member_card_poster',
    en: '微页面-点击权益卡引导',
    params: {},
  },
};
