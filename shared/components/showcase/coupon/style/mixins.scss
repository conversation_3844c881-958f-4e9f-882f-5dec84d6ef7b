// 红 橙 白 黑 绿主题色
$red: #e74c2c;
$light-red: #fbeae4;
$gradient-red: linear-gradient(90deg, #fff2ee 2%, #ffd3cb 99%);
$gradient-bottom-red: linear-gradient(to bottom, #fff2ee 2%, #ffd3cb 99%);
$style6redbordercolor: #f18e79;

$yellow: #f39343;
$light-yellow: #ffe9b7;
$style6yellow: #fda538;
$gradient-yellow: linear-gradient(90deg, #fff6dc 0%, #fdd886 100%);
$gradient-bottom-yellow: linear-gradient(to bottom, #fff6dc 0%, #fdd886 100%);

$white: #fff;
$light-white: #fff;
$white-border-color: #969799;
$gradient-white: linear-gradient(90deg, #fff2ee 2%, #ffd3cb 99%);
$gradient-bottom-white: linear-gradient(to bottom, #fff2ee 2%, #ffd3cb 99%);

$black: #383b3e;
$light-black: #f2f3f3;
$gradient-black: linear-gradient(90deg, #fff 0%, #d1d2d2 100%);
$gradient-bottom-black: linear-gradient(to bottom, #fff 0%, #d1d2d2 100%);
$style6blackbordercolor: #9f9f9f;

$green: #4cac5a;
$light-green: #e0f4e4;
$gradient-green: linear-gradient(90deg, #fff 0%, #eaffed 100%);
$gradient-bottom-green: linear-gradient(to bottom, #fff 0%, #eaffed 100%);
$style6greenbordercolor: #9bd8a4;

$gray: #c8c9cc;
$light-gray: #dcdee0;
$gradient-gray: linear-gradient(90deg, #dadddf 2%, #a7a7a7 96%);
$gradient-bottom-gray: linear-gradient(to bottom, #dadddf 2%, #a7a7a7 96%);

// 失效颜色
$disabledBorderColor: rgba(134, 135, 138, .6);
$disabledFontColor: rgba(134, 135, 138, .6);
$whiteReceviedColor: rgba(255, 255, 255, .6);

@function getstyle6bgcolor($type) {
  @if $type ==red {
    @return $gradient-red;
  }

  @if $type == yellow {
    @return $gradient-yellow;
  }

  @if $type == white {
    @return $gradient-white;
  }

  @if $type == black {
    @return $gradient-black;
  }

  @if $type == green {
    @return $gradient-green;
  }

  @if $type == gray {
    @return $gradient-gray;
  }
}

@function getfontcolor($type) {
  @if $type ==red {
    @return $red;
  }

  @if $type == yellow {
    @return $yellow;
  }

  @if $type == white {
    @return $red;
  }

  @if $type == black {
    @return $black;
  }

  @if $type == green {
    @return $green;
  }

  @if $type == gray {
    @return $white;
  }
}

@function getstyle6color($type) {
  @if $type ==red {
    @return $red;
  }

  @if $type == yellow {
    @return $style6yellow;
  }

  @if $type == white {
    @return $red;
  }

  @if $type == black {
    @return $black;
  }

  @if $type == green {
    @return $green;
  }

  @if $type == gray {
    @return $white;
  }
}

@function getstyle6bordercolor($type) {
  @if $type ==red {
    @return $style6redbordercolor;
  }

  @if $type == yellow {
    @return $style6yellow;
  }

  @if $type == white {
    @return $red;
  }

  @if $type == black {
    @return $style6blackbordercolor;
  }

  @if $type == green {
    @return $style6greenbordercolor;
  }

  @if $type == gray {
    @return $white;
  }
}

@function getbordercolor($type) {
  @if $type ==red {
    @return #EC6E55;
  }

  @if $type == yellow {
    @return #F39343;
  }

  @if $type == white {
    @return $white-border-color;
  }

  @if $type == black {
    @return #383B3E;
  }

  @if $type == green {
    @return #63B270;
  }

  @if $type == gray {
    @return #969799;
  }
}

@function getstyle5bgcolor($type) {
  @if $type ==red {
    @return #fdf1ed;
  }

  @if $type == yellow {
    @return #fcf5e5;
  }

  @if $type == white {
    @return #fff;
  }

  @if $type == black {
    @return #f2f3f3;
  }

  @if $type == green {
    @return #f1f8f3;
  }

  @if $type == gray {
    @return $gray;
  }
}

@function getstyle5bordercolor($type) {
  @if $type ==red {
    @return #FDC5BA;
  }

  @if $type == yellow {
    @return #FDC37A;
  }

  @if $type == white {
    @return #abacad;
  }

  @if $type == black {
    @return #5D5F61;
  }

  @if $type == green {
    @return #7EC88A;
  }

  @if $type == gray {
    @return #ABACAD;
  }
}

@mixin getStyle5ColorStyle($type) {
  color: getfontcolor($type);
  background-color: getstyle5bgcolor($type);
  border-color: getbordercolor($type);

  .cap-coupon__right {
    border-color: getstyle5bordercolor($type);
  }

  &::after {
    border-color: getbordercolor($type);
  }
}

@mixin getStyle6ColorStyle($type) {
  .cap-coupon__left {
    color: getfontcolor($type);
    background: getstyle6bgcolor($type);

    &::before {
      background-color: getstyle6color($type);
    }

    &::after {
      border-color: getstyle6bordercolor($type);
    }
  }

  .cap-coupon__right {
    color: $white;
    background: getstyle6color($type);

    &::after {
      background-color: getstyle6color($type);
    }
  }
}

@mixin getStyle8ColorStyle($type) {
  color: #fff;

  .cap-coupon__left,
  .cap-coupon__right {
    background-color: getfontcolor($type);
  }
}

// 已领样式 样式六已领边框为白色
@function getreceviedcolor($type) {
  @if $type == gray {
    @return rgba(93,95,98,.6);
  }

  @if $type == red {
    @return rgba(243,160,142, .6);
  }

  @if $type == yellow {
    @return rgba(244,167,100, .6);
  }

  @if $type == white {
    @return rgba(243,160,142, .6);
  }

  @if $type == black {
    @return rgba(255,255,255,.6);
  }

  @if $type == green {
    @return rgba(112,190,124,.6);
  }
}
// 已领样式
@mixin getReceviedStyle($type) {
  .cap-coupon__disabled-text {
    color: getreceviedcolor($type);
  }

  .cap-coupon__disabled-text::after,
  .cap-coupon__disabled-text::before {
    border-color: getreceviedcolor($type);
  }
}

// 已领样式 样式
@mixin getReceviedWhiteStyle() {
  .cap-coupon__disabled-text {
    color: $whiteReceviedColor;
  }

  .cap-coupon__disabled-text::after,
  .cap-coupon__disabled-text::before {
    border-color: $whiteReceviedColor;
  }
}
