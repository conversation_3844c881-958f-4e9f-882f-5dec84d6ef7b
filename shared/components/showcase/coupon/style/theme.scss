@import './mixins.scss';

// 红 橙 白 黑 绿主题样式
.cap-coupon {
  &__color1 {
    color: $white;
    background: $red;

    @include getReceviedStyle(red);
  }

  &__color2 {
    color: $yellow;
    background: $light-yellow;

    @include getReceviedStyle(yellow);
  }

  &__color3 {
    color: #333;
    background: $white;

    @include getReceviedStyle(white);

    .cap-coupon__value {
      color: #d45134;

      &::after {
        border-color: $black;
      }
    }
  }

  &__color4 {
    color: $white;
    background: #383b3e;

    @include getReceviedStyle(black);
  }

  &__color5 {
    color: $green;
    background: $light-green;

    @include getReceviedStyle(green);

    .cap-coupon__value::after {
      border-color: $green;
    }
  }

  &__disabled {
    color: $white;
    background: #d3d6d9;

    .cap-coupon__value::after {
      border-color: #fff !important;
    }

    .cap-coupon__disabled-text {
      color: $disabledFontColor;
    }

    .cap-coupon__disabled-text::before {
      border-color: $disabledBorderColor;
    }

    .cap-coupon__disabled-text::after {
      border-color: $disabledBorderColor;
    }

    .cap-coupon__disabled {
      border-color: #5d5f62;
    }

    .cap-coupon__right {
      border-color: #fff !important;
    }

    .cap-coupon__value {
      color: $white;
    }

    .cap-coupon__action {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 14px;
    }
  }
}

@mixin platformCouponBg($type) {
  background-image: url('https://b.yzcdn.cn/coupon/platform/v1/' + $type + '.png');
}
.cross-store {
  &.cap-coupon {
    background-size: 100% 100%;
    &__color1 {
      @include platformCouponBg('red');
    }
    &__color2 {
      @include platformCouponBg('yellow');
    }
    &__color3 {
      @include platformCouponBg('white');
    }
    &__color4 {
      @include platformCouponBg('black');
    }
    &__color5 {
      @include platformCouponBg('green');
    }
    &__disabled {
      @include platformCouponBg('grey');
    }
  }
}
