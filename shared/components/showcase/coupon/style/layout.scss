@import './mixins.scss';

// 一行一个 一行两个 一行三个 横向滑动布局样式
.cap-coupon {
  &__item--multi {
    .cap-coupon {
      &:first-child {
        margin-left: 0;
      }

      &__left {
        flex-direction: column;
      }

      &__value {
        margin: 0;

        &::after {
          display: none;
        }
      }

      &__name {
        display: none;
      }

      &__right {
        display: none;
      }

      &__condition {
        font-size: 10px;
      }
    }

    .cap-coupon__style1 {
      .cap-coupon__dot-above,
      .cap-coupon__dot-below {
        display: none;
      }

      .cap-coupon__desc {
        margin-left: 0;
      }
    }

    .cap-coupon__style3 {
      .cap-coupon {
        &__left {
          flex: inherit;
          justify-content: center;
          width: 80%;
          padding-left: 20px;
          font-size: 30px;
        }
      }
    }
  }

  &__item--three {
    .cap-coupon__item-container {
      min-width: 32%;
      margin-right: 1%;
    }
  }

  &__item--three,
  &__item--multi {
    .cap-coupon__value {
      font-size: 28px;
    }

    .cap-coupon__style3 {
      .cap-coupon__left {
        width: 90%;
      }
    }

    .cap-coupon__style4,
    .cap-coupon__style8 {
      .cap-coupon__condition {
        padding: 0 8px;
      }
    }
  }

  // 一行一个
  &__item--one {
    flex-wrap: wrap;

    .cap-coupon__item-container {
      width: 100%;
    }

    .cap-coupon__name {
      text-align: left;
    }

    .cap-coupon__left {
      .cap-coupon__value {
        justify-content: center;
        min-width: 38%;
        margin-left: 0;
      }

      .cap-coupon__desc {
        margin-left: 13px;
      }
    }

    .cap-coupon {
      &__desc {
        margin-left: 28px;
      }

      &.cap-coupon__style1 {
        .cap-coupon__name {
          margin-right: 8px;
        }
      }

      &.cap-coupon__style5 {
        .cap-coupon__value::after {
          display: none;
        }

        .cap-coupon__desc {
          margin-left: 19px;
        }

        .cap-coupon__left {
          justify-content: flex-start;

          .cap-coupon__condition {
            margin-top: 8px !important;
          }
        }

        .cap-coupon__right {
          align-items: center;
          height: 52px;
          margin: auto 0;
          padding: 0;
          border-left: 1px dashed red;

          .cap-coupon__action {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 52px;
            font-size: 14px;
          }
        }
      }

      &.cap-coupon__style6 {
        height: auto;
        padding: 0;

        .cap-coupon__name {
          text-align: center;
        }

        .cap-coupon__left {
          justify-content: center;
          box-sizing: border-box;
        }

        .cap-coupon__right {
          flex: 1;
          justify-content: center;
          padding-left: 0;
          text-align: center;

          .cap-coupon__name {
            padding: 0 8px;
          }
        }

        .cap-coupon__writing {
          font-size: 32px;
          line-height: 32px;
        }

        .cap-coupon-unit {
          font-size: 12px;
          line-height: 16px;
        }

        .cap-coupon__condition {
          margin-top: 12px;
        }

        .cap-coupon__exchange {
          .cap-coupon__writing-sp {
            font-size: 18px !important;
          }
        }
      }

      &.cap-coupon__style7 {
        height: 88px;

        .cap-coupon__name {
          text-align: center;
        }

        .cap-coupon__exchange .cap-coupon__writing {
          font-size: 24px !important;
        }

        .cap-coupon__left {
          border-color: #fff;

          .cap-coupon__value .cap-coupon-unit {
            font-size: 40px;
            line-height: 32px;
          }

          .cap-coupon__value text:last-child {
            top: 6px;
            left: 4px;
            font-size: 16px;
            line-height: 16px;
          }

          .cap-coupon__condition {
            margin-top: 4px;
            padding: 0 35px;
          }
        }

        .cap-coupon__right {
          left: 7px;
          right: -25px;
          width: 132px;
        }

        .cap-coupon__name {
          display: none;
        }

        &.cap-coupon__disabled {
          .cap-coupon__left {
            border: none;
          }
        }
      }

      &.cap-coupon__style8 {
        .cap-coupon__left {
          border-right: 1px dashed;
        }

        .cap-coupon__desc {
          flex: 1;
          padding: 0 8px;
        }
      }
    }
  }

  // 一行两个
  &__item--two {
    .cap-coupon__item-container {
      max-width: 48%;
      min-width: 48%;
      margin-right: 2%;
    }

    .cap-coupon__value {
      font-size: 16px;
    }

    .cap-coupon__condition {
      padding: 0 8px;
    }

    .cap-coupon__value .cap-coupon__writing {
      font-size: 26px;
      line-height: 32px;
    }

    .cap-coupon__value .cap-coupon-unit {
      top: 5px;
      font-size: 16px;
      line-height: 20px;
    }

    .cap-coupon__style3 {
      .cap-coupon__left {
        width: 80%;
      }
    }

    .cap-coupon__style5 {
      .cap-coupon__action {
        max-height: 48px;
        padding: 6px 12px;
        line-height: 18px;
        border-left: 1px dashed !important;
      }

      .cap-coupon__condition {
        margin-top: 4px !important;
      }

      .cap-coupon__disabled-text {
        transform: scale(.8);
      }
    }

    .cap-coupon__style6 {
      height: auto;
      padding: 0;

      .cap-coupon__left {
        align-items: flex-start;
        min-width: 74px;
        max-width: 74px;
        padding-left: 13px;

        .cap-coupon__value {
          &-value {
            line-height: 20px;
          }

          &-unit {
            top: 0;
            left: 0;
          }
        }

        &::before {
          right: -15px;
          width: 30px;
          height: 168%;
        }
      }

      .cap-coupon__right {
        padding-left: 0;
      }

      .cap-coupon__right::after {
        left: -54%;
        width: 54%;
      }

      .cap-coupon__exchange {
        min-width: 40px;
        max-width: 40px;
      }
    }

    .cap-coupon__style7 {
      .cap-coupon__left {
        min-width: 92%;
        max-width: 92%;
        padding-right: 30px;

        .cap-coupon__value .cap-coupon__writing {
          font-size: 36px;
          line-height: 32px;
        }

        .cap-coupon__value .cap-coupon-unit {
          top: 6px;
          font-size: 12px;
          line-height: 20px;
        }

        .cap-coupon__desc {
          margin-top: 4px;
        }

        .cap-coupon__condition {
          margin-top: 0;
        }
      }

      .cap-coupon__right {
        left: -30px;
        display: inline-flex;

        image {
          width: 45px;
        }
      }

      &.cap-coupon__disabled {
        .cap-coupon__left {
          border: none;
        }
      }

      &.cap-coupon__disabled::after {
        border-right: none;
      }
    }

    .cap-coupon__style8 {
      .cap-coupon__left {
        display: inline-flex;
        justify-content: center;
        min-width: 80%;
        max-width: 80%;
        border-right: 1px dashed;

        .cap-coupon__value .cap-coupon__writing {
          font-size: 26px;
          line-height: 32px;
        }

        .cap-coupon__value .cap-coupon-unit {
          font-size: 12px;
          line-height: 16px;
        }
      }

      .cap-coupon__right {
        display: inline-flex;
        min-width: 20%;
        max-width: 20%;
      }

      .cap-coupon__action {
        width: 14px;
        line-height: 18px;
        text-align: center;
      }

      .cap-coupon__condition {
        padding: 0 8px;
      }

      .cap-coupon__disabled-text-wrap {
        transform: scale(.8) rotate(-45deg);
      }
    }
  }

  // 横向滑动
  &__item--swipe {
    flex-wrap: nowrap;

    .cap-coupon__item-container {
      min-width: 130px;
      padding-right: 12px;
    }

    .cap-coupon__style6 {
      .cap-coupon__left::before {
        transform: rotate(6deg) !important;
      }
    }
  }

  // 横向滑动 一行三个
  &__item--swipe,
  &__item--three {
    .cap-coupon__item-container {
      max-width: 32%;
    }

    .cap-coupon__desc {
      text-align: center;
    }

    .cap-coupon__style2 {
      .cap-coupon__condition {
        padding: 0 8px;
      }
    }

    .cap-coupon__style5 {
      flex-direction: column;
      height: auto;

      .cap-coupon__left {
        margin-top: 17px !important;
        margin-bottom: 16px;
      }

      .cap-coupon__right {
        left: 0;
        min-width: 64px;
        height: 36px;
        padding: 10px 12px 15px;
        border-top: 1px dashed;
      }

      .cap-coupon__condition {
        margin-top: 4px !important;
        padding: 0 8px;
      }
    }

    .cap-coupon__style6 {
      flex-direction: column;
      height: auto;
      padding: 0;

      &.cap-coupon__color1 {
        .cap-coupon__left {
          background: $gradient-bottom-red;
        }
      }

      &.cap-coupon__color2 {
        .cap-coupon__left {
          background: $gradient-bottom-yellow;
        }
      }

      &.cap-coupon__color3 {
        .cap-coupon__left {
          background: $gradient-bottom-white;
        }
      }

      &.cap-coupon__color4 {
        .cap-coupon__left {
          background: $gradient-bottom-black;
        }
      }

      &.cap-coupon__color5 {
        .cap-coupon__left {
          background: $gradient-bottom-green;
        }
      }

      &.cap-coupon__disabled {
        .cap-coupon__left {
          background: $gradient-bottom-gray;
        }
      }

      .cap-coupon__left {
        min-width: 90%;
        max-width: 90%;
        min-height: 54px;
        border-radius: unset;
        border-top-left-radius: 7px;
        border-top-right-radius: 7px;

        &::after {
          position: absolute;
          bottom: 0;
          width: calc(100% - 8px);
          height: 100%;
          border-radius: unset;
          border-top-left-radius: 7px;
          border-top-right-radius: 7px;
        }

        &::before {
          position: absolute;
          bottom: -12px;
          width: 126%;
          height: 20px;
          background: #e74c2c;
          transform: rotate(10deg);
          content: " ";
        }
      }

      .cap-coupon__right {
        justify-content: center;
        width: 100%;
        height: auto;
        min-height: 77px;
        padding-left: 0;
        border-radius: unset;
        border-bottom-right-radius: 7px;
        border-bottom-left-radius: 7px;

        .cap-coupon__desc {
          padding: 0 8px;
        }

        .cap-coupon__condition {
          margin-top: 8px;
        }

        .cap-coupon__name {
          font-size: 14px;
        }
      }

      .cap-coupon__right::after {
        top: -22px;
        left: 0;
        width: 100%;
        height: 22px;
        border-radius: unset;
        border-top-left-radius: 7px;
        border-top-right-radius: 7px;
      }

      .cap-coupon__disabled-text {
        top: 5px;
        transform: scale(.8);
      }
    }

    .cap-coupon__style7 {
      flex-direction: column;
      height: auto;

      &.cap-coupon__color1 {
        .cap-coupon__right {
          .cap-coupon__action {
            background: $red;
          }
        }
      }

      &.cap-coupon__color2 {
        .cap-coupon__right {
          .cap-coupon__action {
            background: $yellow;
          }
        }
      }

      &.cap-coupon__color3 {
        .cap-coupon__right {
          .cap-coupon__action {
            background: $red;
          }
        }
      }

      &.cap-coupon__color4 {
        .cap-coupon__right {
          .cap-coupon__action {
            background: $black;
          }
        }
      }

      &.cap-coupon__color5 {
        .cap-coupon__right {
          .cap-coupon__action {
            background: $green;
          }
        }
      }

      .cap-coupon__left {
        width: 100%;
        padding-top: 19px;
        padding-bottom: 15px;
        border: none;

        .cap-coupon__condition {
          margin-top: 0;
          padding: 0 8px;
        }
      }

      .cap-coupon__right {
        left: 0;
        display: inline-flex;
        min-width: 100%;
        padding: 0 0 12px;

        .cap-coupon__action {
          left: 0;
          width: 34px;
          height: 34px;
          color: #fff;
          font-size: 12px;
          line-height: 34px;
          text-align: center;
          border-radius: 50%;
        }
      }
    }

    .cap-coupon__style8 {
      .cap-coupon__left {
        justify-content: center;
      }

      .cap-coupon__disabled-text-wrap {
        transform: scale(.8) rotate(-45deg);
      }
    }
  }
}
