@import './mixins.scss';

// 样式一到样式八 样式
.cap-coupon {
  &__dot-above {
    position: absolute;
    top: -5px;
    right: 83px;
    display: flex;
    width: 10px;
    height: 10px;
    background-color: $white;
    border-radius: 50%;
    content: '';
  }

  &__dot-below {
    position: absolute;
    right: 83px;
    bottom: -5px;
    display: flex;
    width: 10px;
    height: 10px;
    background-color: $white;
    border-radius: 50%;
    content: '';
  }

  &__left {
    z-index: 1;
    .cap-coupon__desc {
      text-align: left;
    }
  }

  &__value {
    &::after {
      display: none;
    }
  }

  &.cap-coupon__style1 {
    background: transparent !important;

    .cap-coupon__right {
      z-index: 1;
      width: 82px;
      height: 70px;
      border-color: rgba(255, 255, 255, 0.5);
      border-left-width: 1px;
      border-left-style: dashed;
    }

    .cap-coupon__bg {
      position: absolute;
      top: 0;
      left: 10px;
      height: 100%;
      background-repeat: repeat-x;
      background-size: 1px 100%;
      width: calc(100% - 20px);
    }

    &.cap-coupon::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 10px;
      height: 100%;
      background-size: 10px 100%;
      content: ' ';
    }

    &.cap-coupon::after {
      position: absolute;
      top: 0;
      right: 1px;
      width: 11px;
      height: 100%;
      background-size: 10px 100%;
      background-repeat: no-repeat;
      transform: rotateY(180deg);
      content: ' ';
    }

    &.cap-coupon__color1 {
      .cap-coupon__right {
        border-color: rgba(255, 255, 255, 0.5);
      }

      .cap-coupon__bg {
        background-color: $red;
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/e5dadaaa21e02f24beb1dc4d080e58d8bc1cd5c92a6b1bc753761c7b5c27a2e6.png');
      }
    }

    &.cap-coupon__color2 {
      color: $yellow;
      background: $light-yellow;

      .cap-coupon__right {
        border-color: rgba(243, 147, 67, 0.5);
      }

      .cap-coupon__bg {
        background-color: $light-yellow;
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/34c930c70755d0c93239f65a3321287eb68ed6d35ecbe3c51088207e7a72d469.png');
      }
    }

    &.cap-coupon__color3 {
      color: $black;
      background: $white;
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);

      .cap-coupon__right {
        border-color: rgba(153, 153, 153, 0.3);
      }

      .cap-coupon__bg {
        background-color: $white;
      }

      &.cap-coupon::before,
      &.cap-coupon::after {
        background-color: #f5f5f5;
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/7c7ae6f8ca72578d88433a32375ccf478c94ec2e03f4983051fbbbd2101800ae.png');
      }

      .cap-coupon__dot-above,
      .cap-coupon__dot-below {
        background-color: #f9f9f9;
      }
    }

    &.cap-coupon__color4 {
      color: $white;
      background: $black;

      .cap-coupon__bg {
        background-color: #383b3e;
      }

      .cap-coupon__right {
        border-color: rgba(153, 153, 153, 0.5);
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/c84955e536656a6ce639a1588241b1efa747be2e01bc9f0059e62d7ba774889a.png');
      }
    }

    &.cap-coupon__color5 {
      .cap-coupon__bg {
        background-color: $light-green;
      }

      .cap-coupon__right {
        border-color: rgba(99, 190, 114, 0.5);
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/19bf86585cde12a150c5850db4f9897551c2a3189f61da09be3084f1bf103afb.png');
      }
    }

    &.cap-coupon__disabled {
      color: #fff !important;

      .cap-coupon__bg {
        background-color: #c8c9cc;
      }

      .cap-coupon__right {
        border-color: rgba(153, 153, 153, 0.5);
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/125f8ef06215a2f04416402cb130d306ddb66a04e851dd6eead4bafcdd794671.png');
      }
    }
  }

  &.cap-coupon__style2 {
    background-color: transparent;
    background-size: 100% 100%;

    .cap-coupon {
      &__value {
        &::after {
          visibility: hidden;
        }
      }

      &__left {
        z-index: 1;
      }

      &__right {
        z-index: 1;
      }
    }

    .cap-coupon__bg {
      position: absolute;
      top: 0;
      left: 18px;
      right: 18px;
      height: 100%;
      background-repeat: repeat-x;
      background-size: 1px 100%;
    }

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 18px;
      height: 100%;
      background-size: 100% 100%;
      content: '';
    }

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      width: 18px;
      height: 100%;
      background-size: 100% 100%;
      transform: rotateY(180deg);
      content: '';
    }

    &.cap-coupon__color1 {
      .cap-coupon__bg {
        background-image: url('https://b.yzcdn.cn/fix-base64/84c4f9e7d4c9dc8155cfe05bd8a89a2b081552b9a2608ca87393316236eed6fd.png');
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/40072fbefd453377c58a6f70e4ac0d40418a7a03fc8e42883af3877ad8c50323.png');
      }
    }

    &.cap-coupon__color2 {
      .cap-coupon__bg {
        background-image: url('https://b.yzcdn.cn/fix-base64/31cdd661bb9a79d3c8b24e51fe77dcc0747b731516943ce3f224638428ec4411.png');
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/6e696e43a5285ad75afbe73f5b38676b541b6d4b92ba506055512ee5aa83cb37.png');
      }
    }

    &.cap-coupon__color3 {
      .cap-coupon__bg {
        background-image: url('https://b.yzcdn.cn/fix-base64/b25fb3cd5b0c288ca25a3e14876a7bdbf9b581d5d3c91a6a9e600b67003464df.png');
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/0bd56e948b0f52fc2682097aa5d9d9df597a40457f6215f234868f1275eb2a87.png');
      }
    }

    &.cap-coupon__color4 {
      .cap-coupon__bg {
        background-image: url('https://b.yzcdn.cn/fix-base64/28ad6f8ad8b8a24773efa008c87f9cc258a01f04f821efcc5ca36f349c74f801.png');
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/b6e54e06c36f81914e029aced8e7319601b5c49da60ed165984087fb76d38b0a.png');
      }
    }

    &.cap-coupon__color5 {
      .cap-coupon__bg {
        background-image: url('https://b.yzcdn.cn/fix-base64/01fe7ec76cf841a2d9557d40fe2fc3bc39e1d84380a560929d423e31e1f96343.png');
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/ef4e2c25e679ff9facd781145713039ea45a2064730a5f0f7205a91e96297096.png');
      }
    }

    &.cap-coupon__disabled {
      .cap-coupon__bg {
        background-image: url('https://b.yzcdn.cn/fix-base64/fd33614e9c6e3d4844cea0f86bca7b8109b71f8df3b3e885b68e0a0ea687721d.png');
      }

      .cap-coupon__value {
        color: $white;
      }

      &::before,
      &::after {
        background-image: url('https://b.yzcdn.cn/fix-base64/4aa7a28dc4435ac7fd4f5135e43a37198e8e1b757f93df04037ee80b71267f85.png');
      }
    }
  }

  &.cap-coupon__style3 {
    border-radius: 4px;

    &.cap-coupon::after {
      position: absolute;
      top: 50%;
      right: -7px;
      display: flex;
      width: 14px;
      height: 14px;
      margin-top: -7px;
      background-color: $white;
      border-radius: 50%;
      content: '';
    }

    .cap-coupon {
      &__left {
        height: 100%;
        padding-left: 0;
        background: $white;
      }

      &__value {
        &::after {
          border-color: $black;
        }
      }
    }

    &.cap-coupon__color1 {
      color: $red;

      .cap-coupon__action {
        color: $white;
      }

      .cap-coupon__value::after {
        border-left-color: rgba(239, 77, 45, 0.5);
      }
    }

    &.cap-coupon__color2 {
      color: $yellow;
      background: $light-yellow;

      .cap-coupon {
        &__value {
          color: $yellow;

          &::after {
            border-left-color: rgba(243, 147, 67, 0.5);
          }
        }
      }
    }

    &.cap-coupon__color3 {
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);

      &.cap-coupon::after {
        background-color: #f9f9f9;
      }

      .cap-coupon__value::after {
        border-left-color: rgba(153, 153, 153, 0.3);
      }
    }

    &.cap-coupon__color4 {
      color: $black;
      background: $black;

      .cap-coupon {
        &__value {
          color: $black;

          &::after {
            border-left-color: rgba(153, 153, 153, 0.5);
          }
        }

        &__right {
          color: $white;
        }
      }
    }

    &.cap-coupon__color5 {
      color: $green;
      background: $light-green;

      .cap-coupon {
        &__value {
          color: $green;

          &::after {
            border-left-color: rgba(99, 190, 114, 0.5);
          }
        }

        &__right {
          color: $green;
        }
      }
    }

    &.cap-coupon__disabled {
      color: #d3d6d9;
      background: #c8c9cc;

      .cap-coupon {
        &__value {
          color: #d3d6d9;
        }

        &__desc {
          color: #d3d6d9;
        }
      }

      .cap-coupon__action {
        color: $white;
        font-size: inherit;
        border: none;
        transform: none;
      }
    }
  }

  &.cap-coupon__style4 {
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);

    &.cap-coupon__color2 {
      .cap-coupon__value::after {
        border-color: rgba(243, 147, 67, 0.5);
      }
    }

    &.cap-coupon__color3 {
      .cap-coupon__value::after {
        border-color: rgba(153, 153, 153, 0.5);
      }
    }

    &.cap-coupon__color5 {
      box-shadow: 0 0 8px rgba(99, 190, 114, 0.1);

      .cap-coupon__value::after {
        border-color: rgba(99, 190, 114, 0.5);
      }
    }
  }

  &.cap-coupon__style5::after {
    position: absolute;
    top: 3px;
    left: 3px;
    box-sizing: border-box;
    width: calc(100% - 6px);
    height: calc(100% - 6px);
    margin: 0;
    border: 1px solid $red;
    content: ' ';
  }

  &.cap-coupon__style5 {
    padding: 0 3px;
    border: 2px solid;

    .cap-coupon__left {
      justify-content: center;
      margin: 13px 0 16px;

      .cap-coupon__value {
        .cap-coupon__writing {
          font-size: 26px;
          line-height: 32px;
        }

        .cap-coupon-unit {
          font-size: 12px;
          line-height: 16px;
        }
      }
    }

    .cap-coupon__right {
      display: flex;
      box-sizing: border-box;
      width: 23%;
      padding: 0 12px;
      text-align: center;

      .cap-coupon__action {
        border: none;
        transform: none;
      }
    }

    .cap-coupon__condition {
      margin-top: 10px;
    }

    &.cap-coupon__color1 {
      @include getStyle5ColorStyle(red);
    }

    &.cap-coupon__color2 {
      @include getStyle5ColorStyle(yellow);
    }

    &.cap-coupon__color3 {
      @include getStyle5ColorStyle(white);
    }

    &.cap-coupon__color4 {
      @include getStyle5ColorStyle(black);
    }

    &.cap-coupon__color5 {
      @include getStyle5ColorStyle(green);
    }

    &.cap-coupon__disabled {
      @include getStyle5ColorStyle(gray);

      border-color: #c8c9cc;

      .cap-coupon__action {
        color: #fff;
      }
    }
  }

  &__style6 {
    background: none;
    border: none;

    .cap-coupon__left {
      position: relative;
      z-index: 2;
      justify-content: center;
      max-width: 35%;
      height: 88px;
      overflow: hidden;
      color: $red;
      background: $light-red;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;

      .cap-coupon__value {
        margin-left: 0;
      }

      .cap-coupon__value::after {
        display: none;
      }

      &::after {
        position: absolute;
        top: 4px;
        left: 4px;
        box-sizing: border-box;
        width: 100%;
        height: calc(100% - 8px);
        border: 1px solid $red;
        border-top-left-radius: 7px;
        border-bottom-left-radius: 7px;
        content: ' ';
      }

      &::before {
        position: absolute;
        right: -19px;
        z-index: 2;
        width: 30px;
        height: 117%;
        background: #e74c2c;
        transform: rotate(13deg);
        content: ' ';
      }
    }

    .cap-coupon__right {
      position: relative;
      z-index: 1;
      display: inline-flex;
      flex: 1;
      justify-content: flex-start;
      height: 96px;
      padding-left: 20px;
      background: $red;
      border-top-right-radius: 7px;
      border-bottom-right-radius: 7px;

      .cap-coupon__desc {
        margin-left: 0;
      }

      &::after {
        position: absolute;
        left: -71px;
        width: 71px;
        height: 100%;
        background: $red;
        border-top-left-radius: 7px;
        border-bottom-left-radius: 7px;
        content: ' ';
      }

      .cap-coupon__name {
        display: -webkit-inline-box;
        font-size: 16px;
        line-height: 18px;
      }
    }

    &.cap-coupon__color1 {
      @include getStyle6ColorStyle(red);
    }

    &.cap-coupon__color2 {
      @include getStyle6ColorStyle(yellow);
    }

    &.cap-coupon__color3 {
      @include getStyle6ColorStyle(white);
    }

    &.cap-coupon__color4 {
      @include getStyle6ColorStyle(black);
    }

    &.cap-coupon__color5 {
      @include getStyle6ColorStyle(green);
    }

    &.cap-coupon__disabled {
      @include getStyle6ColorStyle(gray);

      .cap-coupon__left {
        &::after {
          border-color: rgba(162, 162, 162, 0.5);
        }

        &::before {
          background: $gray;
        }
      }

      .cap-coupon__right {
        background: $gray;

        &::after {
          background: $gray;
        }
      }

      .cap-coupon__disabled-text {
        color: rgba(100, 101, 102, 0.6);
      }

      .cap-coupon__disabled-text::after,
      .cap-coupon__disabled-text::before {
        border-color: rgba(100, 101, 102, 0.6);
      }
    }

    @include getReceviedWhiteStyle();
  }

  &__style7 {
    padding: 0;
    background: #fff;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
    outline: 1px solid;
    outline-offset: -5px;

    .cap-coupon__exchange text:first-child {
      font-size: 20px !important;
      line-height: 20px;
    }

    .cap-coupon__color1 {
      border-right: none;
    }

    .cap-coupon__left {
      flex-direction: column;
      justify-content: center;
      box-sizing: border-box;
      width: 75%;
      height: 100%;
      padding: 0;
      border-right: none;

      .cap-coupon__value {
        margin-left: 0;
      }

      .cap-coupon__desc {
        margin-left: 0;
      }
    }

    &.cap-coupon__color1 {
      outline-color: #e74c2c;

      .cap-coupon__left {
        color: $red;
        border-color: $red;
      }
    }

    &.cap-coupon__color2 {
      outline-color: #383b3e;

      .cap-coupon__left {
        color: $yellow;
        border-color: $yellow;
      }
    }

    &.cap-coupon__color3 {
      .cap-coupon__left {
        color: $red;
        border-color: $red;
      }
    }

    &.cap-coupon__color4 {
      outline-color: #383b3e;

      .cap-coupon__left {
        color: $black;
        border-color: $black;
      }
    }

    &.cap-coupon__color5 {
      outline-color: #4cac5a;

      .cap-coupon__left {
        color: $green;
        border-color: $green;
      }
    }

    .cap-coupon__right {
      position: relative;
      left: -15px;

      image {
        height: 100%;
      }
    }

    .cap-coupon__value::after {
      display: none;
    }

    &.cap-coupon__disabled {
      color: #fff;
      background: $gray;
      border: none !important;

      .cap-coupon__desc {
        color: #fff !important;
      }

      .cap-coupon__action {
        background-color: #969799 !important;
      }
    }

    // &.cap-coupon__disabled::after {
    //   position: absolute;
    //   top: 2px;
    //   left: 2px;
    //   box-sizing: border-box;
    //   width: calc(100% - 4px);
    //   height: calc(100% - 4px);
    //   border: 1px solid #fff;
    //   content: ' ';
    // }
  }

  &__style8 {
    .cap-coupon__value {
      color: #fff;
    }

    &.cap-coupon {
      padding: 0;
      background: unset;
    }

    .cap-coupon__value::after {
      display: none;
    }

    .cap-coupon__left {
      height: 100%;
      overflow: hidden;
      border-radius: 7px;

      .cap-coupon__name {
        font-size: 14px;
        line-height: 18px;
      }
    }

    .cap-coupon__right {
      height: 100%;
      overflow: hidden;
      border-radius: 7px;
    }

    &.cap-coupon__color1 {
      @include getStyle8ColorStyle(red);
    }

    &.cap-coupon__color2 {
      @include getStyle8ColorStyle(yellow);
    }

    &.cap-coupon__color3 {
      @include getStyle8ColorStyle(white);
    }

    &.cap-coupon__color4 {
      @include getStyle8ColorStyle(black);
    }

    &.cap-coupon__color5 {
      @include getStyle8ColorStyle(green);
    }

    &.cap-coupon__disabled {
      .cap-coupon__left,
      .cap-coupon__right {
        background-color: $gray;
      }
    }

    @include getReceviedWhiteStyle();
  }
}

.cross-store {
  &.cap-coupon__style4 {
    border-radius: 8px;
    &.cap-coupon {
      padding-top: 12px;
      padding-bottom: 12px;
    }
    .cap-coupon__writing {
      font-size: 26px;
    }

    .cap-coupon__shop-name {
      font-weight: bold;
      font-size: 16px;
      line-height: 18px;
    }
    .cap-coupon__name {
      font-size: 12px;
      line-height: 18px;
      font-weight: normal;
      margin-top: 4px;
    }
    .cap-coupon__condition {
      font-size: 12px;
      line-height: 16px;
      font-weight: normal;
      margin-top: 4px;
    }
  }
}
