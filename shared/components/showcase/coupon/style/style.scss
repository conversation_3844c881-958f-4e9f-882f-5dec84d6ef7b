@import '~shared/common/css/_mixins.scss';

// 基础样式
.cap-coupon__container {
  padding: 12px 0;
  overflow-y: auto;
  margin-bottom: -12px;
}

.cap-coupon {
  position: relative;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  height: 84px;
  padding: 5px;
  overflow: hidden;

  &__exchange {
    min-width: 64px;

    .cap-coupon__writing {
      font-size: 16px !important;
    }
  }

  &__disabled-text-wrap,
  &__disabled-text {
    position: absolute;
    z-index: 2;
    width: 52px;
    height: 52px;
  }

  &__disabled-text-wrap {
    top: 0;
    left: 0;
    border-radius: inherit;
  }

  &__disabled-text {
    top: 0;
    left: 0;
    font-size: 12px;
    line-height: 52px;
    text-align: center;
  }

  &__disabled-text::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: calc(100% - 6px);
    height: calc(100% - 6px);
    border: 1px dashed;
    border-radius: 50%;
  }

  &__disabled-text::after {
    content: '';
    position: absolute;
    border: 1rpx solid rgba(134, 135, 138, 0.6);
    width: 100%;
    height: 100%;
    border-width: 1px;
    border-radius: 50%;
    top: 0;
    left: 0;
  }

  &__disabled-right {
    top: unset;
    right: -11px;
    bottom: -11px;
    left: unset;
    transform: rotate(-45deg);
  }

  &__disabled-left {
    top: -10px;
    left: -10px;
    transform: rotate(-45deg);
  }

  &--preview {
    overflow-x: hidden;
  }

  &__track {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    width: 100%;
    padding: 0 15px;

    .cap-coupon {
      box-sizing: border-box;
      margin-bottom: 12px;
    }
  }

  &__left {
    display: flex;
    flex: 1;
    align-items: center;
  }

  &__right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    height: 100%;
    font-size: 14px;
    line-height: 14px;
  }

  &__value {
    display: flex;
    align-items: center;
    height: 30px;
    margin-left: 20px;
    font-weight: bold;
    font-size: 30px;
    line-height: 30px;

    .cap-coupon__writing {
      @include multi-ellipsis(1);
    }

    .cap-coupon__unit {
      position: relative;
      top: 5px;
      left: 4px;
      font-size: 12px;
    }

    .cap-coupon__writing-sp {
      display: table;
    }

    .cap-coupon__unit-sp {
      display: table-cell;
      padding-left: 2px;
      font-size: 12px;
    }

    &::after {
      display: inline-block;
      width: 0;
      height: 32px;
      margin: 0 10px;
      border-color: rgba(255, 255, 255, 0.5);
      border-left-width: 1px;
      border-left-style: dashed;
      content: '';
    }
  }

  &__desc {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &__name {
    font-weight: bold;
    font-size: 14px;
    line-height: 18px;

    @include multi-ellipsis(1);
  }

  &__condition {
    margin-top: 5px;
    font-size: 12px;
    line-height: 16px;
    opacity: 0.8;

    @include multi-ellipsis(1);
  }
}

@import './layout.scss';
@import './theme.scss';
@import './pattern.scss';
