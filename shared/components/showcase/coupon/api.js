import { node as request } from 'shared/utils/request';

const paramsFilter = (params) => {
  Object.keys(params).forEach((key) => {
    if (params[key] === '' || params[key] === undefined) {
      delete params[key];
    }
  });
  return params;
};

export default {
  searchCoupons(data) {
    data = paramsFilter(data);
    return request({
      path: '/wscshop/ump/coupon/couponListV2.json',
      data,
    });
  },

  searchPlatformCoupons(data) {
    return request({
      path: '/wscdeco/ump/platformCoupon/couponList.json',
      data,
    });
  },

  receiveCoupon(data) {
    return request({
      path: '/wscshop/ump/coupon/takeCouponByAliasWithGuide.json',
      method: 'POST',
      data,
    });
  },

  fetchCouponGoodsLink(data) {
    return request({
      path: '/wscump/coupon/coupon_use_redirect.json',
      data,
    });
  },
};
