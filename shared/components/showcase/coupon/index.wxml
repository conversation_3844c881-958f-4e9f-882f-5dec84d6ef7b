<view wx:if="{{coupons && coupons.length > 0}}" class="theme-feature-coupon {{ !componentData.layout || componentData.layout === 4 ? 'swipe-coupon' : '' }}">
  <view class="cap-coupon__container {{loaded ? 'cap-coupon__loaded': ''}}">
    <view class="cap-coupon__track {{layoutClass}}">
      <block wx:for="{{coupons}}" wx:for-item="item" wx:for-index="coupon_index" wx:key="id">
        <user-authorize
          bind:next="onClickCoupon"
          need-wx-auth="{{ !isPlugin }}"
          class="cap-coupon__item-container cap-coupon__item-container__style{{config.couponStyle}} cap-coupon__item-container__color{{config.couponColor}}"
          scene="{{ isCrossStore ? '' : 'get_coupon'}}"
          authTypeList="{{ isCrossStore ? ['mobile'] : [] }}" 
          data-index="{{coupon_index}}"
          id="item{{coupon_index}}"
        >
          <view class="cap-coupon {{isCrossStore ? 'cross-store':''}} cap-coupon__style{{config.couponStyle}} cap-coupon__color{{config.couponColor}} {{item.disabled ? 'cap-coupon__disabled':''}}">
            <view wx:if="{{config.couponStyle == 1 || config.couponStyle == 2}}" class="cap-coupon__bg" />
            <view wx:if="{{item.isRecevied && !isCrossStore}}" class="cap-coupon__disabled-text-wrap {{disabledTextClass ? 'cap-coupon__disabled-right': 'cap-coupon__disabled-left'}}">
              <view class="cap-coupon__disabled-text">已领</view>
            </view>
            <view class="cap-coupon__left">
              <view class="cap-coupon__value {{item.preferentialType === 3 ? 'cap-coupon__exchange' : ''}}">
                <block wx:if="{{ item.isIncludeChineseCharacter}}">
                  <text class="cap-coupon__writing-sp" style="font-size:{{item.characterFontSize}}px">
                    {{ item.valueCopyWriting }}
                    <text class="cap-coupon__unit-sp">{{ item.unitCopyWriting }}</text>
                  </text>
                </block>
                <block wx:else>
                  <text class="cap-coupon__writing">{{item.valueCopyWriting}}</text>
                  <text class="cap-coupon__unit">{{item.unitCopyWriting}}</text>
                </block>
              </view>
              <view class="cap-coupon__value-after" />
              <view wx:if="{{config.couponStyle !== 6 && !isCrossStore}}" class="cap-coupon__desc">
                <text class="cap-coupon__name">{{item.title}}</text>
                <text class="cap-coupon__condition">{{ item.conditionText }}</text>
              </view>
               <view wx:if="{{isCrossStore}}" class="cap-coupon__desc">
                <text class="cap-coupon__shop-name">{{item.shopName}}</text>
                <text class="cap-coupon__name">{{item.title}}</text>
                <text class="cap-coupon__condition">{{ item.conditionText }}</text>
              </view>
            </view>
            <view class="cap-coupon__right">
              <block wx:if="{{config.couponStyle === 6}}">
                <view class="cap-coupon__desc">
                  <text class="cap-coupon__name">{{ item.title }}</text>
                  <text class="cap-coupon__condition">{{ item.conditionText }}</text>
                </view>
              </block>
              <block wx:elif="{{!isCrossStore}}">
                <block wx:if="{{item.rightImg}}">
                  <image src="{{item.rightImg}}" mode="heightFix"></image>
                </block>
                <block wx:else>
                  <text class="cap-coupon__action">{{item.text}}</text>
                </block>
              </block>
            </view>
            <view wx:if="{{config.couponStyle == 1}}" class="cap-coupon__dot-above"></view>
            <view wx:if="{{config.couponStyle == 1}}" class="cap-coupon__dot-below"></view>
          </view>
        </user-authorize>
      </block>
    </view>
  </view>
  <van-dialog show="{{guideInfo.display}}" title="{{guideInfo.title || ''}}" message="{{guideInfo.message || ''}}" show-cancel-button="{{true}}" confirm-button-text="{{guideInfo.confirmButtonText || ''}}" bind:cancel="dialogCancel" use-confirm-button-slot="{{true}}">
    <view slot="confirm-button" style="flex:1">
      <user-authorize authTypeList="{{ ['mobile'] }}" bind:next="dialogConfirm">
        <van-button size="large" class="van-dialog__button" custom-style="border: none;color: #ee0a24;" custom-class="van-dialog__confirm confirm-button-class">
          {{ guideInfo.confirmButtonText || '' }}
        </van-button>
      </user-authorize>
    </view>
  </van-dialog>
</view>