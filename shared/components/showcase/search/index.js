import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import scanGoodsBarCode from '@/utils/scan-goods-barcode';
import args from '@youzan/weapp-utils/lib/args';

import loggerBehavior from '../behaviors/logger-behavior';
import Api from './api';

Component({
  behaviors: [loggerBehavior],

  options: {
    addGlobalClass: true,
  },

  properties: {
    componentData: {
      type: Object,
      value: {},
    },
    alias: {
      type: String,
      value: null,
    },
  },

  data: {
    showSearch: false,
    focus: false,
    searchText: '',
    hotSearchKeys: [],
    placeholder: '搜索商品',
    borderStyleColor: '',
    borderStyleHeight: 40,
    searchBg: '',
    textColor: '',
    borderMethodStyle: 'rect',
    positionType: 0,
    textAlignMethod: 0,
    positionShowMethod: 0,
    showSearchComponent: true,
    historyList: [],
    showHistory: true,
    zIndex: 110,
    showScan: false,
  },

  attached() {
    const _searchData = mapKeysCase.toCamelCase(this.data.componentData);
    Api.getHotWords().then((res) => {
      const { chosenWords = [], normalWords = [], isCover = 0 } = res || {};
      this.useSearchPageConfig = isCover;
      const hotSearchKeys = this.useSearchPageConfig
        ? chosenWords.concat(normalWords)
        : _searchData.hotSearchKeys;
      this.setData({
        hotSearchKeys,
      });
      this.currentWords = hotSearchKeys && hotSearchKeys[0];
    });

    this.setData({
      placeholder: _searchData.hotSearchKeys.length
        ? _searchData.hotSearchKeys[0]
        : '搜索商品',
      borderStyleColor: _searchData.borderStyleColor,
      borderStyleHeight: _searchData.borderStyleHeight,
      searchBg: _searchData.color,
      textColor: _searchData.textColor,
      borderMethodStyle:
        +_searchData.borderStyleMethod === 0 ? 'rect' : 'circle',
      positionType: _searchData.positionType,
      textAlignMethod: _searchData.textAlignMethod,
      positionShowMethod: _searchData.positionShowMethod,
      showSearchComponent: !!_searchData.showSearchComponent,
      showHistory:
        _searchData.showHistory === undefined ? true : _searchData.showHistory,
      zIndex: _searchData.zIndex || 110, // z-index 顺序 修改请查看 docs/z-index.md
      showScan: !!_searchData.showScan,
    });
  },

  methods: {
    // 点击搜索框，弹出热门搜索视图
    search_handleSearchBarTap() {
      const { hotSearchKeys = [] } = this.data;
      const bannerId = this.getBannerId();
      const loggerMsg = {
        et: 'click',
        ei: 'click_search',
        en: '点击搜索',
        params: {
          has_hot_search_keys: Number(hotSearchKeys.length > 0),
          banner_id: bannerId,
          s_type: 'search',
          template_alias: this.data.alias,
        },
      };
      this.ensureAppLogger('logger', loggerMsg);
      // app.logger.log(loggerMsg);

      const pageQuery = {
        keepWords: this.currentWords || '',
        banner_id: bannerId,
      };
      if (!this.useSearchPageConfig) {
        pageQuery.oldHots = this.data.hotSearchKeys;
      }

      // 修改点击事件，改为直接进入搜索结果页面
      // eslint-disable-next-line @youzan/dmc/wx-check
      wx.navigateTo({
        url: args.add('/packages/shop/search-page/index', pageQuery),
      });
    },

    search_handleSwipeChange(e) {
      const { detail } = e.detail;
      this.currentWords = this.data.hotSearchKeys[detail.current] || '';
    },

    // 跳转扫码页面
    search_handleScanIconClick() {
      scanGoodsBarCode();
    },
  },
});
