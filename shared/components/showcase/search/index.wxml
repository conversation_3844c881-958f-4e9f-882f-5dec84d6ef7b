<view class="goods-search-wrap">
  <cap-goods-search
    wx:if='{{ showSearchComponent  }}'
    class='component-search'
    textColor='{{ textColor }}'
    searchBg='{{ searchBg }}'
    hotSearchKeys='{{ hotSearchKeys }}'
    borderMethodStyle='{{ borderMethodStyle }}'
    borderStyleHeight='{{ borderStyleHeight }}'
    borderStyleColor='{{ borderStyleColor }}'
    positionType='{{ positionType }}'
    textAlignMethod='{{ textAlignMethod }}'
    positionShowMethod='{{ positionShowMethod }}'
    zIndex='{{ zIndex }}'
    showScan='{{ showScan }}'
    scrollWords='{{ hotSearchKeys && hotSearchKeys.length > 1 }}'
    scrollInterval='{{ 2000 }}'
    bindtap='search_handleSearchBarTap'
    bind:swipe-change="search_handleSwipeChange"
    bind:scan-icon-click="search_handleScanIconClick"
  />
</view>
