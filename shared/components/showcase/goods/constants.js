export const GOODS_FROM = {
  GOODS: 0,
  GOODS_LIST: 1,
};

export const HTTP_URL_REX = /^http[s]?:\/\//;

export const ACTIVITY_MAP = {
  MEET_REDUCTION: 'MEET_REDUCTION', //  * 满减送
  SECKILL: 'SECKILL', //  * 秒杀
  LIMIT_TIME_DISDCOUNT: 'LIMIT_TIME_DISDCOUNT', //  * 限时折扣
  BALE: 'BALE', //  * 打包一口价
  DEPOSIT_EXPANSION: 'DEPOSIT_EXPANSION', //  * 定金膨胀
  HELP_DEPOSIT_EXPANSION: 'HELP_DEPOSIT_EXPANSION', //  * 助力定金膨胀
  GROUPON: 'GROUPON', //  * 多人拼团
  POSTAGE_FREE: 'POSTAGE_FREE', //  * 快递包邮工具
  SECOND_HALF: 'SECOND_HALF', //  * 第2件半价
  TUAN: 'TUAN', //  * 团购返现
  HELP_CUT: 'HELP_CUT', // * 助力砍价
  COUPON: 'COUPON', // * 优惠卡券,
  RECOMMENDED_CARD: 'RECOMMENDED_CARD', // * 非会员下的会员推荐卡
};

export const MEMBER_CARD_TYPE_LIST = [
  'customerDiscount',
  'mergedCustomerDiscount',
];

export const MEMBER_CARD_UNSUPPORT_OTHER_TAG_LIST = [
  'seckill',
  'limitTimeDiscount',
  'groupon',
  'tuan',
  'helpCut',
  'helpDepositExpansion',
  'coupon',
];

export const PRICE_ACTIVITY_LIST = [
  'SECKILL',
  'GROUPON',
  'LIMIT_TIME_DISDCOUNT',
  'TUAN',
];

export const TAGS_THEME_MAP = {
  primary: 'primary',
  weak: 'plain',
  service: 'other',
};

export const TAGS_THEME_OTHER_COLOR = '#FAAB0C';

export const JUMP_UMP_GOODS_DETAIL_LIST = ['seckill', 'helpCut'];

export const JUMP_UMP_GOODS_DETAIL_MAP = {
  SECKILL: 'seckill',
  HELPCUT: 'helpCut',
};

export const INTELLIGENT_SORT_CONFIG = {
  SHOW_INTELLIGENT_SORT: {
    OPEN: '1', // 开启
    CLOSE: '0', // 关闭
  },
  IS_SHOW_ALL: {
    ALL: '1', // 显示全部
    CUSTOM: '0', // 自定义
  },
};

// 是否开启 商品售罄展示；
export const SOLT_OUT_DISPLAY = {
  OPEN: '1', //开启；
  CLOSE: '0', // 关闭
};

// 标签
export const LABEL_TYPE_MAP = {
  1: '关键',
  0: '普通'
};

// 店铺榜单标签
export const SHOP_RANKING_MAP = {
  hot_sale: '销量榜',
  popular: '人气榜',
  new_arrival: '新品榜',
};
