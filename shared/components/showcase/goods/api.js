/* eslint-disable */
import { node as request } from 'shared/utils/request';
import { INTELLIGENT_SORT_CONFIG, GOODS_FROM, SOLT_OUT_DISPLAY } from './constants';

/* eslint-enable */

export const getGoodsList = (data) => {
  const { intelligentSortConfig = {}, templateId, showSoltOutDisplay } = data;
  // 调整为至少智能排序或者售罄商品置底一个选中时就调用推荐接口获取排序后的结果
  if (
    (intelligentSortConfig.showIntelligentSort ===
      INTELLIGENT_SORT_CONFIG.SHOW_INTELLIGENT_SORT.OPEN || showSoltOutDisplay === 
      SOLT_OUT_DISPLAY.OPEN) &&
    +data.goodsFrom === GOODS_FROM.GOODS
  ) {
    data.intelligentSortConfig = JSON.stringify(data.intelligentSortConfig);

    // showSoltOutDisplay 为undefined场景
    if (typeof showSoltOutDisplay == 'undefined') {
      delete data.showSoltOutDisplay;
    }

    return fetchIntelligentSortGoodsList(data);
  }

  // 会员价-纽曼客诉需求 需要在1658模板中添加字段
  if (templateId === 1658) {
    data.needCustomerDiscount = true;
  }

  delete data.templateId;
  delete data.intelligentSortConfig;
  delete data.allGoodsIds;
  delete data.showSoltOutDisplay;
  return fetchGoodsList(data);
};

function fetchGoodsList(data) {
  return request({
    path: '/wscshop/showcase/goodsList.json',
    data: { ...data, isShowPeriod: 1 },
  });
}

function fetchIntelligentSortGoodsList(data) {
  return request({
    path: '/wscshop/showcase/intelligentSortGoodsList.json',
    data: { ...data, isShowPeriod: 1 },
  });
}
