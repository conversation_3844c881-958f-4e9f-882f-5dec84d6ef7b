import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import omit from '@youzan/weapp-utils/lib/omit';
import Args from '@youzan/weapp-utils/lib/args';
import formatPrice from '@youzan/utils/money/formatPrice';
import formatCommission from '@youzan/salesman-cube-core-utils/lib/formatCommission';
import getRedirectData from 'shared/components/showcase/utils/getRedirectData';
import navigate from 'shared/utils/navigate';
import * as api from './api';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
import {
  GOODS_FROM,
  HTTP_URL_REX,
  ACTIVITY_MAP,
  PRICE_ACTIVITY_LIST,
  TAGS_THEME_MAP,
  TAGS_THEME_OTHER_COLOR,
  JUMP_UMP_GOODS_DETAIL_MAP,
} from './constants';
import goodsComponentBehavior from '../behaviors/goods-component-behavior';
import capGoodsBehavior from '../captain-components/goods/behavior';
import * as constants from './constants';

Component({
  behaviors: [
    componentBehavior,
    loggerBehavior,
    goodsComponentBehavior,
    capGoodsBehavior,
  ],

  properties: {
    componentData: {
      type: Object,
      value: {},
      observer(newVal) {
        // ?? ob obj
        this.setObserveComponentData(newVal);
      },
    },

    /**
     * 是否展示多标签
     */
    exhibitionTag: Boolean,

    isAsync: Boolean,

    redirectType: {
      type: Number,
      value: 3,
    },

    openSwipePagination: {
      type: Boolean,
      value: true,
    },

    needLoggerParams: {
      type: Boolean,
      value: false,
    },

    skeletonNumber: {
      type: Number,
      value: 0,
    },

    templateId: {
      type: Number,
      value: 0,
    },
    isUseFlexLayout: {
      Boolean,
      value: false,
    },
  },

  data: {
    isShowMore: false,
    goodsData: {},
    curGoodsList: [],
    loading: false,
    needLoadMore: true,
    loadingMore: false,
    page: 1,
    isPullDownRefresh: false,
    moreUrl: '',
    curSkeletonNumber: 0,
    hasRankInfoDTO: false,
  },

  attached() {
    this.setData(this.getComponentData());
  },

  methods: {
    /**
     * 初始化组件
     */
    getComponentData() {
      const { componentData } = this.data;
      const goodsData = mapKeysCase.toCamelCase(componentData);
      const {
        goodsFrom,
        intelligentSortConfig = {},
        showSoltOutDisplay,
      } = goodsData;
      const isFromGoodsList = +goodsFrom === GOODS_FROM.GOODS_LIST;
      const isSwipeLayout = this.checkIsSwipeLayout(goodsData.layout);
      const infoData = this.computeInfoData(goodsData);

      if (goodsData.showUmpAmbience) {
        infoData.showOriginPrice = true;
      }

      const originalData = {
        infoData,
        goodsData,
        isShowMore: false,
        loading: false,
        needLoadMore: true,
        loadingMore: false,
        page: 1,
        isFromGoodsList,
        isSwipeLayout,
        intelligentSortConfig,
        showSoltOutDisplay,
      };

      if (typeof showSoltOutDisplay !== 'undefined') {
        originalData.showSoltOutDisplay = showSoltOutDisplay;
      }

      return originalData;
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
      if (this.isComponentHide) return;
      const { isAsync } = this.data;

      if (!isAsync) return;

      this.setData(
        {
          isPullDownRefresh: true,
        },
        () => {
          this.setObserveComponentData();
        }
      );
    },

    setObserveComponentData(componentData) {
      componentData = componentData || this.data.componentData;

      const { isAsync, needLoggerParams } = this.data;
      if (isAsync) {
        this.setData(
          {
            curGoodsList: [],
            ...this.getComponentData(),
          },
          () => {
            const { isSwipeLayout, isPullDownRefresh } = this.data;

            if (isAsync && (isSwipeLayout || isPullDownRefresh)) {
              this.handleLoadMore();
            }
          }
        );
      } else {
        const goodsData = mapKeysCase.toCamelCase(componentData);
        const loggerList = [];
        this.checkHasRankInfoDTO(goodsData.goods || []);
        const list = goodsData.goods.map((item, index) => {
          item = this.computedUmpAmbience(item);
          if (item.loggerParams) {
            // 埋点转成下划线
            item.loggerParams = mapKeysCase.toSnakeCase(
              item.loggerParams || {}
            );
          } else if (needLoggerParams) {
            item = this.setGoodsItemLoggerParams(item, index);
            loggerList.push(item.loggerParams);
          }
          item = this.setSalesManRebateInfo(item);
          item = this.setMenberDiscount(item);
          item.isSoldout = +item.soldStatus === 2 && +item.buyWay !== 0;

          return item;
        });

        this.setData({
          curGoodsList: list,
          'goodsData.showSoldNum': goodsData.showSoldNum || false,
          'infoData.showSoldNum': goodsData.showSoldNum || false,
        });

        loggerList.length && this.ensureAppLogger('view', loggerList);
      }
    },

    handleGoodsBuy(e) {
      if (!e.detail.alias) return;

      this.triggerEvent('buy', e.detail);
    },

    handleGoodsItemClick(e) {
      const { loggerParams = null, url = '', alias } = e.detail;
      let goodsUrl = url;

      // https:// http:// 替换成 小程序商品链接
      if (HTTP_URL_REX.test(url) || !loggerParams) {
        goodsUrl = `/pages/goods/detail/index?alias=${alias}`;
      }
      if (e.detail.activityInfos && e.detail.activityInfos.length) {
        const { type, activityId, activityAlias } = e.detail.activityInfos[0];
        if (type === 'helpCut') {
          goodsUrl = Args.add(goodsUrl, {
            activityId,
            type,
          });
        }
        if (type === 'seckill') {
          goodsUrl = Args.add(goodsUrl, {
            type,
            activityId: activityAlias,
          });
        }
      }
      navigate.navigate({
        url: goodsUrl,
      });
    },
    setGoodsItemLoggerParams(item, index, algId = '0') {
      const bannerId = this.getBannerId(index + 1);
      const { componentData } = this.data;
      const { rankNo, rankType } = item.rankInfoDTO || {};
      let rankLogger = {};
      if (rankNo && rankType) {
        rankLogger = {
          rank_no: rankNo,
          rank_type: rankType,
        };
      }
      const cloudLoggerInfo = getLoggerExtraParams(
        componentData.type,
        this.getCloudLoggerInfo(item)
      );
      const loggerParams = {
        goods_id: item.id,
        item_id: item.id,
        item_type: 'goods',
        banner_id: bannerId,
        good_title: item.title,
        alg_id: algId,
        ...this.getComponentLoggerExtraParams(),
        ...cloudLoggerInfo,
        ...rankLogger,
      };
      item.loggerParams = loggerParams;
      item.isSoldout = +item.soldStatus === 2 && +item.buyWay !== 0;

      // 划线价存在 展示 划线价 划线价
      // 划线价不存在 如果活动价小于原价 展示原价
      // 否则不展示
      if (item.origin) {
        item.originPrice = item.origin;
      } else if (+item.activityPrice < +item.price) {
        item.originPrice = item.price;
      }

      item.preUrl = `/pages/goods/detail/index?alias=${item.alias}`;
      item.price = item.activityPrice;
      item = this.computedUmpAmbience(item);

      item.url = Args.add(item.preUrl, {
        banner_id: bannerId,
        alg_id: algId,
        rank_no: rankNo,
        rank_type: rankType,
        ...this.getComponentLoggerExtraParams(),
        ...rankLogger,
      });
      return item;
    },

    fetchGoodsList() {
      const {
        loading,
        needLoadMore,
        page,
        componentData = {},
        goodsData,
        kdtId,
        isAsync,
        offlineId,
        isPlugin,
        isPullDownRefresh,
        isFromGoodsList,
        isSwipeLayout,
        templateId,
      } = this.data;

      if (loading || !needLoadMore || !isAsync) return;

      const {
        goodsFrom,
        goodsList = {},
        goodsIds = [],
        goodsNumberV2,
        goodsNumber: goodsNumberV1,
        intelligentSortConfig = {},
        showSoltOutDisplay,
      } = mapKeysCase.toCamelCase(componentData);
      const goodsNumber = +(goodsNumberV2 || goodsNumberV1 || 0);

      const { id } = goodsList;

      const { layout, showUmpAmbience } = goodsData;
      const pageSize = this.getPageSize(layout);

      const goodsStartPos = (page - 1) * pageSize;
      const fetchGoodsIds = goodsIds
        .slice(goodsStartPos, goodsStartPos + pageSize)
        .join(',');
      // 1. 如果`goodsNumber`小于每页商品数，直接取`goodsNumber`；2. 否则每页商品数
      const goodsListPageSize = goodsNumber < pageSize ? goodsNumber : pageSize;

      const fetchGoodsNumber = isFromGoodsList
        ? goodsListPageSize
        : fetchGoodsIds.length;
      const skeletonDefaultNumber = this.getSkeletonNumber(layout);
      const curSkeletonNumber =
        fetchGoodsNumber > skeletonDefaultNumber
          ? skeletonDefaultNumber
          : fetchGoodsNumber;

      if (!isFromGoodsList && fetchGoodsIds.length === 0) {
        this.handleLoadEnd();
        return;
      }

      // 全部商品
      const allGoodsIds = goodsIds.join(',');
      // 智能排序需要排序的商品个数 默认为全部goodsIds
      let sortGoodsIds = goodsIds.join(',');

      const intelligentSortGoodsPageSize = pageSize;
      intelligentSortConfig.pageSize = intelligentSortGoodsPageSize;
      if (
        !isFromGoodsList &&
        (intelligentSortConfig.showIntelligentSort ===
          constants.INTELLIGENT_SORT_CONFIG.SHOW_INTELLIGENT_SORT.OPEN ||
          showSoltOutDisplay === constants.SOLT_OUT_DISPLAY.OPEN)
      ) {
        if (
          intelligentSortConfig.isShowAll ===
          constants.INTELLIGENT_SORT_CONFIG.IS_SHOW_ALL.CUSTOM
        ) {
          sortGoodsIds = goodsIds
            .slice(0, intelligentSortConfig.goodsNumber)
            .join(',');
        }
        if (intelligentSortGoodsPageSize === 0) {
          this.handleLoadEnd();
          return;
        }
      }

      this.setData({
        curSkeletonNumber,
        loading: !isSwipeLayout,
      });

      return api
        .getGoodsList({
          tagId: id,
          page,
          pageSize: goodsListPageSize,
          goodsIds: fetchGoodsIds,
          goodsFrom,
          kdtId,
          offlineId,
          goodsNumber,
          needActivity: showUmpAmbience ? 1 : 0,
          intelligentSortConfig,
          showSoltOutDisplay,
          allGoodsIds,
          activityPriceIndependent: 1,
          templateId,
        })
        .then((res) => {
          const { list = [], has_more: hasMore, alg_id: algId } = res;
          let moreUrl = '';
          if (isFromGoodsList && hasMore) {
            const {
              title: link_title = '',
              alias = '',
              url: link_url = '',
            } = goodsList;
            const { url } = getRedirectData('tag', {
              link_title,
              alias,
              link_url,
            });
            moreUrl = url;
          } else if (!isFromGoodsList && isSwipeLayout) {
            const { url } = getRedirectData('allgoods', {
              link_title: '查看全部',
            });
            moreUrl = url;
          }
          const curGoodsListData = {};
          const camelCaseList = mapKeysCase.toCamelCase(list);
          const loggerList = [];
          camelCaseList.map((item, index) => {
            const loggerGoodsItem = this.setGoodsItemLoggerParams(
              item,
              index,
              algId
            );
            // 关键标签
            let keyLabel = '';
            const { labelViewModel: { labelGroupModels = [] } = {} } = item;
            if (labelGroupModels.length > 0) {
              labelGroupModels.some(({ labelModels = [], type }) => {
                if (
                  constants.LABEL_TYPE_MAP[type] === '关键' &&
                  labelModels.length > 0
                ) {
                  keyLabel = labelModels[0]?.name || '';
                  return true;
                }
                return false;
              });
            }
            item.keyLabel = keyLabel;
            loggerList.push(loggerGoodsItem.loggerParams);
            return loggerGoodsItem;
          });

          this.ensureAppLogger('view', loggerList);

          const prefixCurGoodsList =
            page === 1 ? [] : this.data.curGoodsList || [];
          const prefixIndex = prefixCurGoodsList.length;
          this.checkHasRankInfoDTO(camelCaseList);
          for (let i = 0; i < camelCaseList.length; i++) {
            // 是否展示返佣价格
            camelCaseList[i] = this.setSalesManRebateInfo(camelCaseList[i]);
            camelCaseList[i] = this.setMenberDiscount(camelCaseList[i]);

            const index = i + prefixIndex;
            const key = `curGoodsList[${index}]`;
            if (!prefixCurGoodsList[index]) {
              curGoodsListData[key] = omit(camelCaseList[i], [
                'id',
                'buy_url',
                'image_id',
                'total_stock',
                'postage',
                'picture',
                'pre_sale_type',
              ]);

              curGoodsListData[key].goodsId = camelCaseList[i].id;
            }
          }

          let listData;
          // 下拉首页 清除掉第一页以外的数据
          if (isPullDownRefresh && page === 1) {
            listData = {
              curGoodsList: camelCaseList,
            };
          } else {
            listData = {
              ...curGoodsListData,
            };
          }

          const nextStartPos = page * pageSize;
          const nextPageGoodsIds = goodsIds.slice(
            nextStartPos,
            nextStartPos + pageSize
          );
          let needLoadMore;

          if (isFromGoodsList) {
            // 下一页商品数大于0
            needLoadMore = goodsNumber - nextStartPos > 0 && hasMore;
          } else {
            // 来源为商品且下一页商品数不为0
            needLoadMore = nextPageGoodsIds && nextPageGoodsIds.length !== 0;
            if (
              !isFromGoodsList &&
              intelligentSortConfig.showIntelligentSort ===
                constants.INTELLIGENT_SORT_CONFIG.SHOW_INTELLIGENT_SORT.OPEN &&
              intelligentSortConfig.isShowAll ===
                constants.INTELLIGENT_SORT_CONFIG.IS_SHOW_ALL.CUSTOM
            ) {
              needLoadMore =
                sortGoodsIds
                  .split(',')
                  .slice(nextStartPos, nextStartPos + pageSize).length !== 0;
            }
          }

          // 是否展示查看更多
          const isShowMore =
            hasMore &&
            list.length !== 0 &&
            !isPlugin &&
            !needLoadMore &&
            !isSwipeLayout;

          this.handleLoadEnd({
            ...listData,
            moreUrl,
            isPullDownRefresh: false,
            page: page + 1,
            needLoadMore: !!needLoadMore,
            isShowMore: !!isShowMore,
          }).then(() => {
            if (isSwipeLayout) {
              if (list.length === 0) {
                this.handleLoadMore();
                return;
              }
              this.selectComponent('#cap-goods-layout').setSwipeStatus({
                loading: false,
                swipeFinished: !needLoadMore,
                isPullDownRefresh,
              });
            }
          });
        })
        .catch(() => {
          // 出错之后重置状态
          setTimeout(() => {
            this.handleLoadEnd();
          }, 100);
        });
    },

    computedUmpAmbience(item) {
      const { showUmpAmbience = false } = this.data.goodsData;

      const { activityInfos = [] } = item;
      if (!activityInfos || !activityInfos.length) {
        return item;
      }

      // 有活动 整个列表才展示 标签。
      if (this.data.infoData && !this.data.infoData.showTags) {
        this.setData({
          'infoData.showTags': true,
        });
      }
      if (!showUmpAmbience) return this.computedTags(item);

      item.tagsList = activityInfos.filter(
        (item) => PRICE_ACTIVITY_LIST.indexOf(item.type) < 0
      );

      // 包邮特殊处理
      item.tagsList = this.computedPostageActivity(item.tagsList);
      // 价格优惠特殊处理
      item = this.computedPriceActivity(item);

      // 会员价标签处理
      item = this.computedMenberActivity(item);

      return item;
    },

    computedTags(item) {
      const { activityInfos = [] } = item;

      if (activityInfos && activityInfos.length >= 1) {
        item.tagsList = activityInfos.map((tag) => {
          const { type, labelThemeType, activityAlias, activityId } = tag;
          // 营销标签分类 转化中 标签主题
          tag.theme = labelThemeType;

          if (tag.theme === TAGS_THEME_MAP.other) {
            tag.color = TAGS_THEME_OTHER_COLOR;
          }

          // 独立商详
          if (type === JUMP_UMP_GOODS_DETAIL_MAP.SECKILL && activityAlias) {
            item.preUrl = `/packages/goods/seckill/index?alias=${activityAlias}`;
          }

          if (type === JUMP_UMP_GOODS_DETAIL_MAP.HELPCUT && activityId) {
            item.preUrl = Args.add('/packages/goods/help-cut/index', {
              alias: item.alias,
              activityId,
              type: 'helpcut',
            });
          }

          return tag;
        });
      }
      // 会员价标签处理
      item = this.computedMenberActivity(item);
      return item;
    },

    // 是否存在榜单标签信息
    // 在无榜单信息的商品需要保留榜单信息位置高度，防止位置错乱
    checkHasRankInfoDTO(list) {
      if (!this.data.hasRankInfoDTO) {
        const hasRankInfoDTO = list.some(
          (el) => el.rankInfoDTO && Object.keys(el.rankInfoDTO).length !== 0
        );
        this.setData({
          hasRankInfoDTO,
        });
      }
    },

    setSalesManRebateInfo(item) {
      // 店铺榜单信息与返佣信息互斥，两者只展示一个
      const { rankNo, rankType } = item.rankInfoDTO || {};
      let shopRankingText = '';
      if (rankNo && rankType) {
        shopRankingText = `本店${constants.SHOP_RANKING_MAP[rankType]}第${rankNo}`;
      }
      // 是否展示返佣价格
      const { maxCommissionStr } = formatCommission({
        profitRange: [item.salesmanRebateMaxPrice],
        commissionSendType: item.salesmanCommissionSendType,
        commissionConfigDTO: item.salesmanCommissionConfigDTO,
        customPointsName: item.customPointsName,
      });
      const showRebate = !!maxCommissionStr;
      // 是否展示导购员返佣
      const showGuideCommission = !!+item.guideCommission;

      item.showExtra =
        showRebate ||
        (item.extension &&
          (item.extension.openSalesmanBooth ||
            item.extension.openGuideBooth)) ||
        this.data.hasRankInfoDTO ||
        showGuideCommission;

      item.extra = [];
      if (item.showExtra) {
        // 榜单信息和返利信息互斥，榜单信息 > 返利信息
        if (showRebate && !shopRankingText) {
          item.extra.push({
            text: `最高赚 ${maxCommissionStr}`,
          });
        } else if (showGuideCommission && !showRebate && !shopRankingText) {
          // 导购返佣信息与分销员返利信息互斥
          const guideCommissionPrice = formatPrice(
            Number(item.guideCommission)
          );
          item.extra.push({
            text: `预计赚${guideCommissionPrice}元`,
          });
        } else if (this.data.hasRankInfoDTO) {
          item.extra.push({
            text: shopRankingText,
            color: 'var(--icon, #323233)',
          });
        }
      }
      return item;
    },

    setMenberDiscount(item) {
      // 如果有标识了，就不加了
      if (
        item &&
        item.extra &&
        Array.isArray(item.extra) &&
        item.extra.length > 0 &&
        item.extra.find((x) => x.text && x.text.trim() !== '')
      ) {
        return item;
      }
      // 用于解决瀑布流炸掉的问题
      const showMenberDiscount = !!item.showRecommendedCard;
      const hasRecommendedCard = !!item.recommendedCard;
      if (showMenberDiscount) {
        item.showExtra = true;
      }

      if (
        item &&
        item.extra &&
        Array.isArray(item.extra) &&
        item.extra.length === 0 &&
        hasRecommendedCard
      ) {
        item.extra.push({
          text: item.recommendedCard,
          color: '#999999',
        });
      } else if (
        item &&
        item.extra &&
        Array.isArray(item.extra) &&
        item.extra.length > 0 &&
        item.extra[item.extra.length - 1] &&
        hasRecommendedCard
      ) {
        // 适配插了个空extra text的case
        item.extra[item.extra.length - 1].text = item.recommendedCard;
        item.extra[item.extra.length - 1].color = '#999999';
      } else {
        item.extra = [];
        if (hasRecommendedCard) {
          item.extra.push({
            text: item.recommendedCard,
            color: '#999999',
          });
        }
      }
      return item;
    },

    computedPostageActivity(list) {
      // postageFreeType 0 包邮 1 件 2 元
      const postageMap = {};
      let filterList = [];
      const preFilterList = []; // 不包邮的活动

      return list
        .map((item, index) => {
          if (
            item.type === ACTIVITY_MAP.MEET_REDUCTION ||
            item.type === ACTIVITY_MAP.POSTAGE_FREE
          ) {
            const { postageFreeType } = item;
            // 已经存在包邮了 直接过滤别的活动
            if (postageMap[0]) {
              filterList.push(index);
              return item;
            }

            // 是包邮活动 把不是之前不是包邮的过滤掉
            if (+postageFreeType === 0) {
              filterList = [...filterList, ...preFilterList];
            }

            // 包邮类型存在 && 不包邮 记录下不包邮的活动
            if (+postageFreeType > 0) {
              preFilterList.push(index);
            }
            // map 里不存在则写入
            if (!postageMap[postageFreeType]) {
              postageMap[postageFreeType] = {
                postageFreeThreshold: +item.postageFreeThreshold,
                index,
              };
            } else if (
              +item.postageFreeThreshold >=
              postageMap[postageFreeType].postageFreeThreshold
            ) {
              filterList.push(index);
            } else {
              filterList.push(postageMap[postageFreeType].index);
              postageMap[postageFreeType] = {
                postageFreeThreshold: +item.postageFreeThreshold,
                index,
              };
            }
          }
          return item;
        })
        .filter((item, index) => filterList.indexOf(index) < 0);
    },

    computedPriceActivity(item) {
      const { activityInfos = [] } = item;
      const priceAmbienceList = activityInfos.filter(
        (item) => PRICE_ACTIVITY_LIST.indexOf(item.type) > -1
      );

      if (!priceAmbienceList.length) return item;

      const { originPrice, priceLabel, activityPrice } = priceAmbienceList[0];

      item.originPrice = originPrice
        ? parseFloat(+originPrice / 100).toFixed(2)
        : item.originPrice;
      item.price = activityPrice
        ? parseFloat(+activityPrice / 100).toFixed(2)
        : item.price;

      // 兼容价格标签
      item.tagsList.unshift({
        label: priceLabel,
        theme: 'primary',
      });

      return item;
    },

    computedMenberActivity(item) {
      if (
        item.tagsList.find(({ type }) =>
          constants.MEMBER_CARD_TYPE_LIST.includes(type)
        )
      ) {
        item.tagsList = item.tagsList.filter(
          ({ type }) =>
            !constants.MEMBER_CARD_UNSUPPORT_OTHER_TAG_LIST.includes(type)
        );
      }
      return item;
    },

    handleLoadEnd(params = {}) {
      return new Promise((resolve) => {
        this.setData(
          {
            ...params,
            curSkeletonNumber: 0,
            loading: false,
            loadingMore: false,
          },
          () => {
            resolve();
          }
        );
      });
    },

    handleLoadMore() {
      const { loading, needLoadMore, loadingMore, isAsync } = this.data;

      if (!isAsync || loading || !needLoadMore || loadingMore) return;

      this.setData(
        {
          loadingMore: true,
        },
        () => {
          this.fetchGoodsList();
        }
      );
    },
  },
});
