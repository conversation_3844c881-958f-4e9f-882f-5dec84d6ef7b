<cap-list
  loading="{{ loading }}"
  finished="{{ !needLoadMore }}"
  disabled="{{ isSwipeLayout || !isAsync }}"
  auto-check
  bind:load="handleLoadMore"
>
  <goods-layout
    id="cap-goods-layout"
    generic:goods-item-tag-info="goods-item-tag-info"
    generic:goods-item-price="goods-item-price"
    generic:goods-item-extra="goods-item-extra"
    componentIndex="{{componentData.componentIndex}}"
    list="{{ curGoodsList }}"
    showLabel="{{ goodsData.showCornerMarkVal == 2 }}"
    info-data="{{ infoData }}"
    layout="{{ goodsData.layout }}"
    size-type="{{ goodsData.sizeType }}"
    buy-btn-express="{{ goodsData.buyBtnExpress }}"
    show-corner-mark="{{ goodsData.showCornerMark }}"
    corner-mark-type="{{ goodsData.cornerMarkType }}"
    corner-mark-image="{{ goodsData.cornerMarkImage }}"
    image-fill-style="{{ goodsData.imageFillStyle }}"
    image-ratio="{{ goodsData.imageRatio }}"
    page-margin="{{ goodsData.pageMargin }}"
    goods-margin="{{ goodsData.goodsMargin }}"
    border-radius-type="{{ goodsData.borderRadiusType }}"
    is-show-more="{{ isShowMore && goodsData.goodsFrom == 1 }}"
    isUseFlexLayout="{{ isUseFlexLayout }}"
    more-url="{{ moreUrl }}"
    extra-data="{{ extraData }}"
    open-swipe-pagination="{{ openSwipePagination && curGoodsList.length !== 0 && (needLoadMore || (isFromGoodsList && !!moreUrl)) }}"
    skeleton-number="{{ curSkeletonNumber || skeletonNumber }}"
    bind:buy="handleGoodsBuy"
    bind:item-click="handleGoodsItemClick"
    bind:load-more="handleLoadMore"
    exhibition-tag= "{{ exhibitionTag }}"
  />
</cap-list>
