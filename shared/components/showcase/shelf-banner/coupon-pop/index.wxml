<view class="coupon">
  <view
    class="coupon-item"
    wx:for="{{ displayCoupons }}"
    wx:for-item="coupon"
    wx:key="id"
  >
    <view class="coupon-item__value">
      <text wx:if="{{ coupon.discount }}">
        {{ coupon.priceValue }}
        <text class="coupon-item__value-hint">折</text>
      </text>
      <text wx:else>￥{{ coupon.priceValue }}</text>
      <text
        class="coupon-item__value-hint"
        wx:if="{{ coupon.valueRandomTo }}"
      >
        起
      </text>
    </view>
    <view class="coupon-item__type">
      {{ coupon.threshold }}
    </view>
    <view class="coupon-item__opt">领取</view>
    <view class="before" />
    <view class="after" />
  </view>
</view>
