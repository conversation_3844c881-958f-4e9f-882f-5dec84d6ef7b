import { moment } from '@youzan/weapp-utils/lib/time';

const app = getApp();

Component({
  properties: {
    couponData: {
      type: Array,
      observer(data) {
        this.getDisplayCoupons(data);
      },
    },
  },

  data: {
    expanded: false,
    displayCoupons: [],
  },

  methods: {
    togglePopup() {
      this.setData({ expanded: !this.data.expanded });
    },

    /**
     * 获取优惠券可用时间字符串
     *
     * @param {number} dateType
     * @param {number} validStartTime
     * @param {number} validEndTime
     * @param {number} fixedBeginTerm
     * @param {number} fixedTerm
     *
     * @returns {string}
     */
    getCouponTimeRange(
      dateType,
      validStartTime,
      validEndTime,
      fixedBeginTerm,
      fixedTerm
    ) {
      if (dateType === 1) {
        return `${moment(validStartTime, 'YYYY.MM.DD')}-${moment(
          validEndTime,
          'YYYY.MM.DD'
        )}`;
      }

      if (dateType === 2) {
        return `领券${fixedBeginTerm ? '次' : '当'}日起${fixedTerm}天内可用`;
      }

      return '';
    },

    /**
     * 处理优惠券展示数据
     *
     * @param {*} data
     */
    getDisplayCoupons(data) {
      const displayCoupons = data.map((coupon) => {
        const {
          dateType,
          fixedTerm,
          fixedBeginTerm,
          denominations,
          discount,
          validEndTime,
          validStartTime,
          condition,
          ...rest
        } = coupon;

        return {
          ...rest,
          acquired: false,
          disabled: false,
          threshold: condition ? `满${condition / 100}元使用` : '无门槛',
          priceValue: discount ? discount / 10 : denominations / 100,
          discount,
          denominations,
          timeRange: this.getCouponTimeRange(
            dateType,
            validStartTime,
            validEndTime,
            fixedBeginTerm,
            fixedTerm
          ),
        };
      });

      this.setData({ displayCoupons });
    },

    /**
     * 领取优惠券
     *
     * @param {*} evt
     */
    takeCoupon(evt) {
      const { id } = evt.currentTarget.dataset;
      const { displayCoupons } = this.data;
      const index = displayCoupons.findIndex(
        ({ id: couponId }) => couponId === id
      );

      const newDisplayCoupons = [...displayCoupons];
      newDisplayCoupons[index].disabled = true;

      this.setData({ displayCoupons: newDisplayCoupons });

      app
        .request({
          path: '/wscshop/ump/coupon/fetchCoupon.json',
          method: 'POST',
          data: {
            source: 'mini_program',
            groupId: id,
          },
        })
        .then((res) => {
          const { discount, value } = res;

          newDisplayCoupons[index].acquired = true;
          newDisplayCoupons[index].disabled = false;
          newDisplayCoupons[index].priceValue = discount
            ? discount / 10
            : value / 100;
          newDisplayCoupons[index].valueRandomTo = 0;

          this.setData({ displayCoupons: newDisplayCoupons });

          wx.showToast({
            icon: 'none',
            title: '领取成功',
          });
        })
        .catch((err) => {
          newDisplayCoupons[index].disabled = false;
          this.setData({ displayCoupons: newDisplayCoupons });
          wx.showToast({
            icon: 'none',
            title: err.msg || '领取失败',
          });
        });
    },
  },
});
