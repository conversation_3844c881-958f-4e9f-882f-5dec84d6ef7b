.coupon {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  height: 48rpx;
  margin-top: 12rpx;

  &-item {
    display: flex;
    position: relative;
    overflow: hidden;
    align-items: center;
    height: 48rpx;
    width: 286rpx;
    margin-right: 20rpx;
    background-color: #ffe9b7;
    border-radius: 2px;
    color: #7a4a19;
    font-size: 22rpx;
    line-height: 48rpx;
    text-align: center;

    .before,
    .after {
      position: absolute;
      content: ' ';
      width: 6px;
      height: 4px;
      border-radius: 6rpx/4rpx;
      background-color: #fff;
    }

    .before {
      top: -2px;
      right: 64rpx;
    }

    .after {
      bottom: -2px;
      right: 64rpx;
    }

    &__value {
      font-size: 32rpx;
      font-weight: 500;

      &-hint {
        font-size: 20rpx;
      }
    }

    &__value,
    &__type {
      flex-grow: 1;
    }

    &__opt {
      flex-shrink: 0;
      width: 68rpx;
    }
  }
}

.pop {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 36rpx 36rpx 0 0;
  height: 60%;

  &-header {
    position: sticky;
    top: 0;
    text-align: center;
    background-color: white;
    z-index: 10;

    &__text {
      height: 88rpx;
      line-height: 88rpx;
      font-size: 32rpx;
      font-weight: 400;
      color: #323233;
    }

    &__close {
      position: absolute;
      right: 32rpx;
      top: 26rpx;
      color: #969799;
    }
  }

  &-content {
    padding: 32rpx;

    &__item {
      display: flex;
      position: relative;
      overflow: hidden;
      align-items: center;
      background-color: #fef5f6;
      border: 1rpx solid #fde6e9;
      border-radius: 10rpx;
      padding: 32rpx;
      margin-top: 20rpx;

      &-price {
        flex-shrink: 0;
        width: 188rpx;

        .price {
          color: #ee0a24;
          font-size: 24rpx;
        }

        .value {
          font-size: 60rpx;
        }

        .meet {
          font-size: 24rpx;
          color: #969799;
          margin-top: 2rpx;
        }
      }

      &-msg {
        flex-grow: 1;

        .desc {
          font-size: 28rpx;
          color: #323233;
        }

        .time-range {
          font-size: 24rpx;
          color: #969799;
          margin-top: 18rpx;
        }
      }

      &-opt {
        flex-shrink: 0;

        .obtain {
          width: 125rpx;
          padding: 0;
          border: 0;
          height: 48rpx;
          line-height: 48rpx;
          font-size: 24rpx;
          color: #fff;
          background-color: #ee0a24;
        }
      }
    }
  }
}
