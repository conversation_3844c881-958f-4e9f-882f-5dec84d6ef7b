import { enterShopSelect } from 'common-api/multi-shop/multi-shop-redirect';
import args from '@youzan/weapp-utils/lib/args';
import { queryUmpData, getShopInfo } from './api';
import navigationToBehavior from '../behaviors/navigation-to-behavior';
import navigate from '@/helpers/navigate';
import { tryLocation, reverseGeocoder } from '@/helpers/lbs';

const app = getApp();
const storageKey = 'select-shop-info';

Component({
  behaviors: [navigationToBehavior],
  properties: {
    /**
     * 组件数据
     */
    componentData: {
      type: Object,
      value: {},
      observer(data) {
        this.setData({
          alignCenter: data.alignType === 1,
          autoHeight: data.heightType === 1,
          showNotice: data.isShowNotice === 0,
          showUmp: data.isShowUmp === 0,
          cardType: data.cardType || 2,
          swiperHeight: (data.bannerImages || []).length
            ? this.getImageHeight(data.bannerImages[0])
            : '160px',
          borderRadiusType: data.borderRadiusType || 2,
        });
        this.setUmpData(data);
        this.initShopBannerData();
        this.getPersonalizedConfigs();
      },
    },
    extraData: {
      type: Object,
      value: {},
    },
  },

  data: {
    shopName: '',
    logo: '',
    notice: '',
    bannerImage:
      'https://img01.yzcdn.cn/upload_files/2020/04/14/Fu5SS6psLMn9xSH9T_gDXdjVHOv7.png',
    showNotice: false,
    showUmp: false,
    autoHeight: false,
    couponData: [],
    hasUmpData: false,
    umpData: {},
    shopAddress: '',
    isSingleShop: false,
    currentKdtId: 0,
    enterShopMode: 1,
    userLocation: null,
    showOnlineShopSwitch: false, // 是否显示切店按钮
  },

  pageLifetimes: {
    show() {
      app.getShopInfo().then((shopInfo) => {
        const { currentKdtId } = this.data;
        if (shopInfo.kdtId !== currentKdtId && currentKdtId !== 0) {
          this.initShopBannerData();
          this.getPersonalizedConfigs(shopInfo);
        }
      });
    },
  },

  methods: {
    initShopBannerData() {
      // 这里不要用getShopInfoSync，因为sync有坑，某些数据还没有set好，就返回了。
      app.getShopInfo().then((shopInfo) => {
        this.setData({
          shopName: shopInfo.shop_name,
          logo: shopInfo.logo,
          isSingleShop: shopInfo.shopMetaInfo.shop_role === 0,
          currentKdtId: shopInfo.kdtId,
        });

        getShopInfo().then(({ address: shopAddress }) => {
          this.setData({
            shopAddress,
          });
        });
      });
    },

    // 获取个性化配置
    async getPersonalizedConfigs(shopInfo) {
      const { chainStoreInfo } = shopInfo;
      /**
       * 获取进店模式
       * 1 默认  显示当前门店，可切换
       * 2 显示当前门店，不可切换
       * 3 显示消费者定位/收货地址，可切换
       * 4 显示消费者定位/收货地址，不可切换
       */
      const enterShopMode = chainStoreInfo.onlineShopVisitModel || 1;
      const showOnlineShopSwitch = chainStoreInfo.showBtn;

      const obj = {
        showOnlineShopSwitch,
      };
      // 3，4要显示消费者定位地址
      if ([3, 4].includes(+enterShopMode)) {
        obj.enterShopMode = +enterShopMode;
        // cp 进店代码内的逻辑
        const currentShopInfo = app.storage.get(storageKey) || {};
        if (currentShopInfo?.poi) {
          obj.userLocation = currentShopInfo.poi;
        } else {
          const userLocation = await new Promise((resolve, reject) => {
            tryLocation(
              ({ lat, lng }) => {
                reverseGeocoder({
                  location: {
                    latitude: lat,
                    longitude: lng,
                  },
                  poi_options: 'policy=2',
                  coord_type: 3,
                }).then(({ result = {} }) => {
                  const { recommend } = result.formatted_addresses || {};
                  resolve(recommend || result.address || '');
                });
              },
              (err) => {
                console.log('shelf-banner err: ', err);
                reject();
              },
              null
            );
          }).catch((err) => {
            console.log('userLocation err: ', err);
          });
          obj.userLocation = userLocation;
        }
      }
      this.setData(obj);
    },

    handleChangeShop() {
      if (!this.data.showOnlineShopSwitch) return;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const url = currentPage.route;
      const { options } = currentPage;
      // 打开通用店铺选择页
      enterShopSelect(
        {
          redirectUrl: encodeURIComponent(args.add(`/${url}`, options)),
        },
        {
          redirectFunc: navigate.navigate,
        }
      );
    },
    handleNavigation(event) {
      const {
        currentTarget: { dataset },
      } = event;
      const { item } = dataset;
      this.navigateIncludeShelf(item);
    },
    /**
     * 获取营销数据
     * @param {*} config
     */
    getUmpData(config) {
      const {
        type,
        hideEmptyCoupon,
        activities,
        addType,
        autoType,
        autoValue,
      } = config;
      let ids = [];

      // 没有配置类型
      if (addType === undefined) return Promise.resolve([]);

      // 手动添加
      if (addType === 0) {
        ids = activities.map(({ id }) => id);
      }

      return queryUmpData(
        type,
        ids,
        autoType === 1 ? +autoValue : 10,
        addType !== 0,
        hideEmptyCoupon
      );
    },

    // 设置营销数据
    setUmpData(componentData) {
      const { umpCoupon } = componentData;

      this.getUmpData({ type: 'coupon', ...umpCoupon })
        .then((res) => {
          const umpData = {
            hasUmpData: res.length > 0,
            couponData: res,
          };

          this.setData(umpData);

          const { shopName, showNotice, componentData } = this.data;

          wx.setStorage({
            key: 'RETAIL_SHELF_UMP_DATA',
            data: {
              ...umpData,
              shopName,
              notice: showNotice && componentData.notice,
            },
          });
        })
        .catch((err) => {
          console.log('err: ', err);
        });
    },
    handleContactBack(e) {
      this.triggerEvent('contactback', e.detail);
    },
    getImageHeight(img) {
      return ((375 / img.image_width) * img.image_height).toFixed(2) + 'px';
    },
  },
});
