Component({
  properties: {
    umpData: {
      type: Object,
      observer() {
        this.computeBriefList();
      },
    },
  },

  data: {
    expanded: false,

    briefList: [],
    meetReduce: [],
    limitDiscount: [],
    bale: [],
  },

  methods: {
    computeBriefList() {
      const {
        meetReduceList = [],
        limitDiscount = [],
        baleList = [],
      } = this.data.umpData;

      // 按照产品的优先级
      // 排列不同的营销活动顺序
      const briefList = [
        ...meetReduceList,
        ...baleList,
        ...limitDiscount,
      ].slice(0, 5);

      const allList = [
        {
          key: 'meetreduce',
          name: '满减',
          items: meetReduceList,
        },
        {
          key: 'bale',
          name: '打包一口价',
          items: baleList,
        },
        {
          key: 'limitdiscount',
          name: '限时折扣',
          items: limitDiscount,
        },
      ];

      this.setData({
        briefList,
        allList,
        limitDiscount,
        bale: baleList,
        meetReduce: meetReduceList,
      });
    },

    showPopup() {
      this.setData({ expanded: true });
    },

    closePopup() {
      this.setData({ expanded: false });
    },
  },
});
