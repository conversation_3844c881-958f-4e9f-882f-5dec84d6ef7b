.ump {
  display: flex;
  align-items: center;
  font-size: 20rpx;
  background-color: white;
  margin-top: 12rpx;

  &.center {
    justify-content: center;
  }

  &-tags {
    flex: 1;
    height: 32rpx;
    overflow: hidden;

    &__item {
      display: inline-block;
      font-size: 20rpx;
      padding: 0 8rpx;
      margin-right: 8rpx;
      border-radius: 4rpx;
      color: #f44;
      background-color: #fde6e9;
      line-height: 32rpx;
      vertical-align: top;
    }
  }

  &-more {
    line-height: 32rpx;
    color: #a0a1a3;

    &__text {
      margin-right: 4rpx;
    }
  }
}

.pop {
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &-title {
    position: relative;
    text-align: center;

    &__text {
      font-size: 32rpx;
      font-weight: 400;
      color: #323233;
      height: 88rpx;
      line-height: 88rpx;
    }

    &__close {
      position: absolute;
      font-size: 36rpx;
      bottom: 16rpx;
      right: 32rpx;
    }
  }

  &-group {
    display: flex;
    align-items: flex-start;
    padding: 0 32rpx 28rpx;

    &__title {
      text-align: right;
      line-height: 32rpx;
    }

    &__list {
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      padding-top: 1rpx;
      font-size: 24rpx;
      color: #323233;

      &-item {
        display: inline-block;
        vertical-align: top;
      }
    }
  }
}
