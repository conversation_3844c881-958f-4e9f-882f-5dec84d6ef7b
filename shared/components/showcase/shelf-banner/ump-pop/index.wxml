<view class="ump" bind:tap="showPopup" wx:if="{{ briefList.length > 0 }}">

  <view class="ump-tags">
    <text
      class="ump-tags__item"
      wx:for="{{ briefList }}"
      wx:for-item="briefItem"
      wx:key="id"
    >
      {{ briefItem.desc }}
    </text>
  </view>

  <view class="ump-more">
    <text class="ump-more__text">更多优惠</text>
    <van-icon name="arrow-down" />
  </view>

</view>

<popup
  position="bottom"
  z-index="{{ 10000 }}"
  show="{{ expanded }}"
  bind:close="closePopup"
  custom-class="pop"
  overlay
>
  <view class="pop-title">
    <view class="pop-title__text">优惠活动</view>
    <van-icon
      name="cross"
      color="#969799"
      bind:tap="closePopup"
      class="pop-title__close"
    />
  </view>
  <scroll-view
    scroll-y="{{ true }}"
    style="flex: 1; overflow: auto;"
  >
    <block
      wx:for="{{ allList }}"
      wx:for-item="umpItem"
      wx:for-index="umpItemIndex"
      wx:key="key"
    >
      <view
        class="pop-group"
        wx:if="{{ umpItem.items.length > 0 }}"
      >
        <view class="pop-group__title">
          <text class="ump-tags__item">{{ umpItem.name }}</text>
        </view>
        <view class="pop-group__list">
          <view
            class="pop-group__list-item"
            wx:for="{{ umpItem.items }}"
            wx:for-item="umpItemDetail"
            wx:for-index="umpItemDetailIndex"
            wx:key="id"
          >
            {{ umpItemDetail.name }}
            <text wx:if="{{ umpItemDetailIndex < umpItem.items.length - 1 }}">，</text>
          </view>
        </view>
      </view>
    </block>
  </scroll-view>
</popup>
