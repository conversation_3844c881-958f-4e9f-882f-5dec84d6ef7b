<view class="banner">
  <block wx:if="{{componentData.bannerImages && componentData.bannerImages.length}}">
    <swiper
      loop
      circular
      autoplay
      style="height: {{autoHeight ? swiperHeight : '160px'}}"
    >
      <block
        wx:for="{{ componentData.bannerImages }}"
        wx:key="index"
      >
        <swiper-item
          class="swp__item"
          wx:if="{{ item.link_type === 'chat' }}"
        >
          <button
            open-type="contact"
            session-from="{{ extraData.sourceParam || '' }}"
            business-id="{{ extraData.businessId || '' }}"
            bind:contact="handleContactBack"
            class="image-imgnav-btn--transparent chat-btn"
          >
            <image
              class="banner-image {{ autoHeight ? 'auto' : '' }}"
              mode="{{ autoHeight ? 'widthFix' : 'aspectFill' }}"
              src="{{ item.image_url }}"
            />
          </button>
        </swiper-item>
        <swiper-item
          class="swp__item"
          wx:else
        >
          <image
            class="banner-image"
            mode="{{ autoHeight ? 'widthFix' : 'aspectFill' }}"
            src="{{ item.image_url }}"
            data-item="{{item}}"
            bind:tap="handleNavigation"
          />
        </swiper-item>
      </block>
    </swiper>
  </block>
  <block wx:else>
    <image
      class="banner-image {{ autoHeight ? 'auto' : '' }}"
      mode="{{ autoHeight ? 'widthFix' : 'aspectFill' }}"
      src="{{ componentData.bannerImage }}"
    />
  </block>

  <view
    class="banner-center {{ alignCenter ? 'center' : '' }} {{'card-type-' + cardType}} {{ 'banner-border-' + borderRadiusType}}"
  >
    <!-- 店铺 Logo -->
    <view class="banner-mark {{ alignCenter ? '' : 'right' }}">
      <image
        class="banner-mark__icon"
        src="{{ logo || CURRENT_GLOBAL_SHOP.logo }}"
      />
    </view>

    <!-- 进店模式1、2或者未获取到用户当前定位均显示 -->
    <block wx:if="{{ (enterShopMode === 1 || enterShopMode === 2) || !userLocation }}">
      <!-- 店铺名称 -->
      <view class="banner-shop {{ alignCenter ? 'center' : 'right' }}" bind:tap="handleChangeShop">
        <view class="banner-shop-title">{{ shopName || CURRENT_GLOBAL_SHOP.shop_name }}</view>
        <van-icon
          wx:if="{{ showOnlineShopSwitch }}"
          name="arrow"
          color="#323233"
          class="banner-shop-icon"
        />
      </view>

      <!-- 店铺地址 -->
      <view
        wx:if="{{ shopAddress }}"
        class="banner-address {{ alignCenter ? 'center' : '' }}"
        bind:tap="handleChangeShop"
      >
        <van-icon
          name="location-o"
          size="16"
          class="banner-address-icon"
        />
        <text>{{shopAddress}}</text>
      </view>
    </block>

    <!-- 用户定位地址 -->
    <!-- 进店模式3、4且获取到用户定位地址 -->
    <block wx:else>
      <view
        class="banner-address {{ alignCenter ? 'center' : '' }}"
        bind:tap="handleChangeShop"
      >
        <van-icon
          name="location-o"
          size="16"
          class="banner-address-icon"
        />
        <text class="banner-address-text">{{ userLocation }}</text>
        <van-icon
          wx:if="{{ showOnlineShopSwitch}} "
          name="arrow"
          size="16"
          class="banner-address-icon"
        />
      </view>
    </block>

    <!-- 店铺公告 -->
    <view
      wx:if="{{ showNotice && componentData.notice }}"
      class="banner-notice"
    >公告：{{ componentData.notice }}</view>

    <!-- 营销内容 -->
    <ump-banner
      class="banner-ump"
      show="{{ showUmp }}"
      shopName="{{ shopName || CURRENT_GLOBAL_SHOP.shop_name }}"
      coupons="{{ couponData }}"
      notice="{{ showNotice && componentData.notice }}"
    />

  </view>
</view>


