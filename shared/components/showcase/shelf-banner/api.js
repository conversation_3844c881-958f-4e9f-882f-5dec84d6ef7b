import promisify from 'utils/promisify';

const app = getApp();
const carmen = promisify(app.carmen);

export const getShopInfo = ({ lng, lat } = {}) => {
  const data =
    lng && lat
      ? {
          current_lon: lng,
          current_lat: lat,
        }
      : {};
  return carmen({
    api: 'youzan.retail.shop.consumer.info/1.0.0/query',
    data,
  }).then((res) => ({
    ...res,
    currentLocation: {
      lon: data.current_lon,
      lat: data.current_lat,
    },
  }));
};

/**
 * 获取优惠信息
 *
 * @param {string} type 活动类型
 * @param {number[]} ids 活动的 id 数组
 * @param {boolean} additionNum  自动获取数量
 * @param {boolean} autoAddition 是否自动获取
 * @param {boolean} hideEmptyCoupon 是否隐藏已抢完的优惠券
 */
export function queryUmpData(
  type,
  ids,
  additionNum,
  autoAddition,
  hideEmptyCoupon
) {
  const params = {
    activity_type: type,
    ...(autoAddition
      ? {
          auto_addition: autoAddition,
          addition_num: additionNum,
        }
      : { ids }),
  };

  if (hideEmptyCoupon) params.hide_empty_coupon = !!hideEmptyCoupon;

  return carmen({
    api: 'youzan.retail.trade.misc.shelf/1.0.0/getactivitydetail',
    data: params,
  });
}
