/* eslint-disable */
import getApp from 'shared/utils/get-safe-app';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import Args from '@youzan/weapp-utils/lib/args';
import { APPJSON_TAB_BAR_LIST } from './config';
import {
  getSyncApolloConfig,
  accordSkyLogger,
} from 'common-api/multi-shop/multi-shop-redirect';
/* eslint-enable */
import { getPlugins } from '@youzan/ranta-helper-tee';

const { dmc } = getPlugins();

const p = 'packages';
const app = getApp();
const MY_WEAPP_TYPE = '1';

let tagGoodsPath = `/${p}/shop/goods/tag-list/index`;
let allGoodsListPath = `/${p}/shop/goods/all/index`;

function getWebviewPage(url) {
  return `/pages/common/webview-page/index?src=${encodeURIComponent(url)}`;
}

function getDmcGoodsListRoute() {
  try {
    Promise.all([
      dmc.route.readRoute('AllGoodsList'),
      dmc.route.readRoute('GoodsTag'),
    ]).then(([route1, route2]) => {
      allGoodsListPath = route1.path;
      tagGoodsPath = route2.path;
    });
  } catch (e) {
    console.error('获取商品列表路径失败', e);
  }
}

getDmcGoodsListRoute();

/**
 * 获取跳转链接
 * @param {String} type 链接类型
 * @param {Object} item item 格式 { alias, link_title, link_id, extra_data }
 * @param {Object} extra extra 格式 { banner_id }
 */
export default function getRedirectData(type, item, extra) {
  let url = '';
  let isTab = false;
  let isReLaunch = false;
  let dmcExtra = null;
  const {
    alias,
    link_title: linkTitle = '',
    link_url: linkUrl = '',
    link_id: linkId,
    extra_data: extraData = {},
  } = item;

  const { banner_id } = extra || {};
  const bannerIdOpt = {};
  if (banner_id) {
    bannerIdOpt.banner_id = banner_id;
  }

  if (type === 'goods' && alias) {
    url = `/pages/goods/detail/index?alias=${alias}`;
  } else if (type === 'tag') {
    // 兼容alias为空的情况
    // {alias: '', link_url: 'https://h5.youzan.com/v2/showcase/tag?alias=xxx'}
    const parameter = alias ? { alias } : Args.getAll(linkUrl);
    if (parameter.alias) {
      url = `${tagGoodsPath}?alias=${
        parameter.alias
      }&title=${encodeURIComponent(linkTitle)}`;
      dmcExtra = {
        pageName: 'GoodsTag',
        query: {
          alias: parameter.alias,
          title: linkTitle,
        },
      };
    }
  } else if (type === 'weappfeature') {
    url = `/pages/home/<USER>/index?id=${linkId}&title=${linkTitle}`;
  } else if (type === 'feature' && alias) {
    url = `/pages/home/<USER>/index?alias=${alias}`;
  } else if (type === 'homepage') {
    isTab = true;
    url = `/pages/home/<USER>/index`;
  } else if (type === 'cart') {
    isTab = true;
    url = `/${p}/goods/cart/index`;
  } else if (type === 'usercenter') {
    // packages/usercenter 不是tab页， 改为 pages
    isTab = true;
    url = `/pages/usercenter/dashboard/index`;
    if (app.globalData.isRetailApp) {
      url = `/${p}/retail/usercenter/dashboard-v2/index`;
    }
  } else if (type === 'allgoods') {
    url = `${allGoodsListPath}?title=${encodeURIComponent(linkTitle)}`;
    dmcExtra = {
      pageName: 'AllGoodsList',
      query: { title: linkTitle },
    };
  } else if (type === 'pointsstore') {
    url = `/${p}/ump/integral-store/index`;
  } else if (type === 'coupon') {
    // TODO: 李晨那边配置 coupon_type 字段
    url = `/${p}/user/coupon/detail/index?id=${item.link_id}${
      item.coupon_type === 7 ? '&type=promocard' : ''
    }`;
  } else if (type === 'seckill') {
    url = `/${p}/goods/seckill/index?alias=${alias}`;
  } else if (type === 'weapplink' && extraData.link_type === MY_WEAPP_TYPE) {
    url = extraData.my_weapp_link;
    if (url[0] !== '/') {
      url = '/' + url;
    }

    // 修复切店返回店铺更新的问题,下个链接切店时做reLaunch处理,灰度处理
    if (getSyncApolloConfig()?.client_enter_shop_linkto_switch) {
      try {
        const params = Args.getAll(url) || {};
        const { subKdtId, sub_kdt_id, kdtId, kdt_id } = params;
        const urlId = subKdtId || sub_kdt_id || kdtId || kdt_id;
        const currentKdtid = app.getKdtId();
        const HQKdtId = app.getHQKdtId();
        // url上的kdtId和当前店铺不一致时且不属于总部时,做reLaunch处理
        if (urlId && +urlId !== currentKdtid && +urlId !== HQKdtId) {
          // eslint-disable-next-line no-unused-vars
          isReLaunch = true;
        }
        // 上报处理
        accordSkyLogger({
          text: `[wx] 自定义外链处理ReLaunch`,
        });
      } catch (error) {
        accordSkyLogger({
          text: `[wx] 自定义外链处理有误`,
          err: error,
        });
      }
    }

    const urlWithNoParam = url.split('?')[0];
    if (APPJSON_TAB_BAR_LIST.indexOf(urlWithNoParam) > -1) {
      isTab = true;
    }
    // 移除小程序内跳转移除shopAutoEnter，避免推广链接携带进店参数额外触发进店逻辑
    url = Args.remove(url, 'shopAutoEnter');
    // 空白页的话，需要移除内部的标志，重新组装URL
    if (url.indexOf('/pages/common/blank-page/index') === 0) {
      const args = Args.getAll(url);
      if (args.weappSharePath) {
        let weappSharePath = decodeURIComponent(args.weappSharePath);
        weappSharePath = Args.remove(weappSharePath, 'shopAutoEnter');
        args.weappSharePath = encodeURIComponent(weappSharePath);
        url = Args.add('/pages/common/blank-page/index', args);
      }
    }
  } else if (type === 'paidcolumn') {
    url = `/${p}/paidcontent/column/index?alias=${alias}`;
  } else if (type === 'paidcontent') {
    url = `/${p}/paidcontent/content/index?alias=${alias}`;
  } else if (type === 'mypaidcontent') {
    url = `/${p}/paidcontent/list/index`;
  } else if (type === 'paidlive') {
    url = `/${p}/paidcontent/live/index?alias=${alias}`;
  } else if (type === 'allcourse') {
    // 教育 - 全部课程
    url = `/${p}/edu/goods/list/index`;
  } else if (
    type === 'course' ||
    type === 'educourse' ||
    type === 'allofflinecourse' ||
    type === 'eduappointment' ||
    type === 'course_group' ||
    type === 'course_category' ||
    type === 'edumoments'
  ) {
    url = `/${p}/edu/webview/index?targetUrl=${encodeURIComponent(
      linkUrl.replace(/^http(s)?:\/\/shop\d+-?\d+/, 'https://h5')
    )}`;
  } else if (type === 'link') {
    // 自定义链接只支持公众号文章 并且只在专享版小程序里
    const linkUrl = item.link_url;
    if (linkUrl && /mp.weixin.qq.com\/s/.test(linkUrl)) {
      url = getWebviewPage(linkUrl);
    }
  } else if (type === 'shopnote') {
    isTab = true;
    url = `/${p}/shop/shopnote/list/index`;
  } else if (type === 'calendar_checkin') {
    url = `/${p}/shop/ump/sign-in/index`;
  } else if (type === 'zodiac') {
    url = `/${p}/ump/new-lottery/casino/index?alias=${item.alias}`;
  } else if (['guaguale', 'wheel', 'crazyguess'].includes(type)) {
    let reatType = '';
    if (linkUrl?.includes('lottery')) reatType = 'LuckyLottery';
    if (linkUrl?.includes('cards')) reatType = 'Cards';
    if (linkUrl?.includes('zodiac')) reatType = 'Zodiac';
    if (linkUrl?.includes('crazy')) reatType = 'CrazyGuess';
    if (reatType) {
      dmcExtra = { pageName: reatType, query: { alias, ...bannerIdOpt } };
    }
  } else if (type === 'shopnote_detail') {
    url = `/${p}/shop/shopnote/detail/index?noteAlias=${item.alias}`;
  } else if (type === 'mp_article') {
    url = `/${p}/shop/shopnote/mparticle/detail/index?noteAlias=${item.alias}`;
  } else if (type === 'hotellist') {
    url = getWebviewPage(linkUrl);
  } else if (type === 'recharge_center') {
    url = getWebviewPage(linkUrl);
  } else if (type === 'red-package') {
    wx.showRedPackage({ url: linkUrl });
    return {};
  } else if (type === 'member_code') {
    url = `/${p}/member-code/index`;
  } else if (type === 'vipcenter') {
    url = `/${p}/shop/levelcenter/free/index`;
  } else if (type === 'weapp_marketing_page' || type === 'marketing_page') {
    // 营销会场
    url = `/${p}/ext-marketing-page/index?id=${extraData.id || ''}`;
  } else if (type === 'storelist' || type === 'nearby_store_way') {
    url = `/${p}/shop/physical-store/index`;
  } else if (type === 'goods_classify') {
    url = getWebviewPage(linkUrl);
  }

  // 除tab添加额外参数
  if (!isEmpty(extra) && !isTab) {
    url = Args.add(url, extra);
  }

  // 兜底通过 web-view 打开 youzan.com 域名链接
  if (!url && !dmcExtra) {
    if (/^https?:\/\/(.+)\.youzan\.com\//i.test(linkUrl)) {
      url = getWebviewPage(linkUrl);
    }
  }

  return { url, isTab, isReLaunch, dmcExtra };
}
