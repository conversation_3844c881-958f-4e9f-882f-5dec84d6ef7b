/**
 * 遍历
 * @param {*} data 值
 * @param {*} deep 递归遍历
 * @return 接受处理方法的函数
 */
export const walk = (data, deep = true) => processFn => {
  const isArrayData = Array.isArray(data);
  const keys = typeof data === 'object' ? Object.keys(data) : [];
  let res;

  if (isArrayData && data.length > 0) { // 数组
    res = [];
  } else if (keys.length > 0) { // 对象
    res = {};
  } else { // 其他类型，直接返回
    return data;
  }

  // 规约的输入。如果是对象，则把对象转成数组的形式，例：{ a: 1, b: 2 } -> [[a, 1], [b, 2]]
  const reduceInput = isArrayData ? data : keys.map(k => [k, data[k]]);

  return reduceInput.reduce((result, value, key) => {
    // 如果不是数组类型，则把 key 和 value 改为真正的键值
    if (!isArrayData) {
      key = value[0];
      value = value[1];
    }

    // 递归
    if (deep) {
      value = walk(value)(processFn);
    }

    const {
      key: newKey = key,
      value: newValue = value,
    } = processFn({ key, value }) || {};
    result[newKey] = newValue;

    return result;
  }, res);
};

/**
 * 转换组件数据
 * 把常见的 key 转驼峰，字符串转数字、转布尔值，封装一下
 * @param {Object} data 源数据
 * @return {Object}
 */
export default (data = {}, { numberKeys = [], booleanKeys = [], ...rest } = {}) => {
  return walk(data)(({ key, value }) => {
    // 如果 key 是字符串，转为驼峰
    if (typeof key === 'string') {
      key = key.replace(/_[a-z]/g, item => item[1].toUpperCase());
    }

    if (typeof rest[key] === 'function') {
      value = rest[key](value); // 如果有自定义的处理方法，则直接调用该方法
    } else if (numberKeys.indexOf(key) > -1) {
      value = +value; // 转数字
    } else if (booleanKeys.indexOf(key) > -1) {
      value = !!+value; // 转布尔值
    }

    return { key, value };
  });
};
