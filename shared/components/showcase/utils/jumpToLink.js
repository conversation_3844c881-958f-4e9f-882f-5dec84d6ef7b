import navigate from 'shared/utils/navigate';
import getRedirectData from './getRedirectData';
import jumpVideoNumberDynamic from './jumpVideoNumberDynamic';
import Event from '@youzan/weapp-utils/lib/event';
import getApp from 'shared/utils/get-safe-app';
import { getPlugins } from '@youzan/ranta-helper-tee';

const { dmc } = getPlugins();

const OTHER_WEAPP_TYPE = '2'; // 其他小程序

function navigateToOfflineShop(shopId = '') {
  if (!shopId) {
    return;
  }

  const app = getApp();
  app.setOfflineId(shopId);
  navigate.switchTab({ url: '/packages/home/<USER>/index' });
}

export default function jumpToLink(type, item, extra = {}) {
  if (type === 'offlinepage') {
    // 添加多网点跳转
    navigateToOfflineShop(item.link_id);
    return;
  }

  if (type === 'platform_coupon') {
    Event.trigger('feature-platform-coupon:open', item);
    return;
  }

  if (type === 'video_number_dynamic') {
    jumpVideoNumberDynamic(item);
    return;
  }

  if (type === 'weapplink') {
    const { extra_data: extraData } = item;
    if (extraData && extraData.link_type === OTHER_WEAPP_TYPE) {
      if (+extraData.use_short_link) {
        wx.navigateToMiniProgram({
          shortLink: extraData.short_link,
        });
      } else {
        wx.navigateToMiniProgram({
          appId: extraData.other_weapp_appid,
          path: extraData.other_weapp_link,
        });
      }

      return;
    }
  }

  if (type === 'embedded_weapp_link') {
    const { extra_data: data } = item;
    wx.openEmbeddedMiniProgram({
      appId: data.embedded_weapp_appid,
      path: data.embedded_weapp_link,
      allowFullScreen: true,
      reFreshTargetPage: true,
    });
  }

  const { url, isTab, isReLaunch, dmcExtra } = getRedirectData(
    type,
    item,
    extra
  );

  if (!url && !dmcExtra) {
    return;
  }

  const navigateApi = isTab
    ? 'switchTab'
    : isReLaunch
    ? 'reLaunch'
    : 'navigate';

  // dmc跳转
  if (dmcExtra) {
    const { pageName, query } = dmcExtra;
    dmc[navigateApi](pageName, query);
    return;
  }
  navigate[navigateApi]({ url });
}
