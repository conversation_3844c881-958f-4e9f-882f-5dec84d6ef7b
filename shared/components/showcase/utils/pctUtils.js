import getSystemInfo from 'shared/utils/browser/system-info';
/**
 * 知识付费相关
 */

export function parseTime(time, withYaer) {
  if (!time) return '';
  const date = new Date(time);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  let result = `${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
  return withYaer ? `${year}-${result}` : result;
}

export function parsePrice(price) {
  let priceCharList = (String(price) || '').split('');
  // 补充零位
  if (priceCharList.length < 3) {
    priceCharList = (new Array(3 - priceCharList.length)).fill('0').concat(priceCharList);
  }
  priceCharList.splice(-2, 0, '.');
  return priceCharList.join('');
}

/**
 * 针对知识付费商品，判断店铺是否可以在小程序中购买，如果不能购买，则隐藏其价格等信息
 */
export function shopCanPayForKnowledge() {
  const app = getApp();
  const isStopPay = app.getShopInfoSync().virtualGoodsCannotWePay;
  const { platform } = getSystemInfo();
  if (isStopPay && platform === 'ios') {
    return false;
  }
  return true;
}
