import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import { LINK_TYPE_LIST } from './config';

const OTHER_WEAPP = 2;

/**
 * 把老的链接数据 转化成新的链接数据，方便cap-navigator组件调用
 * @param {Object} oldLink 老的链接对象
 */
export function getLink(oldLink) {
  const { extraData = {} } = oldLink;
  return {
    linkType:
      oldLink.linkType === 'weapplink' && extraData.linkType == OTHER_WEAPP
        ? 'otherWeapp'
        : oldLink.linkType || '',
    appId: extraData.otherWeappAppid || '',
    path: extraData.otherWeappLink || '',
    useShortLink: +extraData.useShortLink || 0,
    shortLink: extraData.shortLink || '',
  };
}

/**
 * 校验linkItem的   需同步修改 src/ext-tee-wsc-decorate/extensions/showcase-container/utils/linkAdaptor.js
 * @param {Object} item 链接对象，包含linkType, linkUrl
 * @param {*} extraType 额外支持的类型
 */
export const isValidLink = (linkItem = {}, extraType = []) => {
  const SUPPORTED_LINK_TYPE = LINK_TYPE_LIST.concat([
    undefined,
    '',
    ...extraType,
  ]);
  linkItem = mapKeysCase.toCamelCase(linkItem);
  const { linkType, linkUrl } = linkItem;

  return (
    SUPPORTED_LINK_TYPE.indexOf(linkType) >= 0 ||
    (linkType === 'link' && /mp.weixin.qq.com\/s/.test(linkUrl))
  );
};
