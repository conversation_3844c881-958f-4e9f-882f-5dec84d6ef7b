const app = getApp();

export default function jumpVideoNumberDynamic(item) {
  const {
    videoDynamicParams: { type: _type, ...channelParam },
    linkTitle,
  } = item;
  const loggerParams = {
    ...item.videoDynamicParams,
    image_url: item?.imageUrl,
    link_title: linkTitle,
  };
  app.logger.log({
    et: 'click',
    ei: 'wxvideo_video_click',
    en: '视频号动态点击',
    params: loggerParams,
  });
  wx.openChannelsActivity &&
    wx.openChannelsActivity({
      ...channelParam,
      success: () => {
        app.logger.log({
          et: 'custom',
          ei: 'wxvideo_video_jump_success',
          en: '视频号动态跳转成功',
          params: loggerParams,
        });
      },
      fail: (error) => {
        app.logger.log({
          et: 'custom',
          ei: 'wxvideo_video_jump_fail',
          en: '视频号动态跳转失败',
          params: {
            ...loggerParams,
            error_msg: error?.errMsg,
          },
        });
      },
    });
}
