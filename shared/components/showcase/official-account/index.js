Component({
  data: {
    canIUse: wx.canIUse('official-account')
  },

  attached() {
    const SCENE_WHITE_LIST = [1011, 1047, 1089, 1038];
    const { scene } = wx.getLaunchOptionsSync();

    this.setData({
      canIUse: SCENE_WHITE_LIST.some(item => item === +scene)
    })
  },

  methods: {
    loadError(e) {
      this.setData({
        canIUse: false
      })

      this.triggerEvent('loadError', e)
      console.log('official-account-load-error', e);
    }
  }
});
