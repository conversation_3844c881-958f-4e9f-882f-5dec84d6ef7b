import navigate from 'shared/utils/navigate';
import { getSettingsForm } from './api';

const app = getApp();
// 拼单链接
const URL = `/packages/retail/goods-shelf/index?kdt_id=${app.getKdtId()}&mode=0&scene=1`;

Component({
  data: {
    isShowCpn: false,
    title: '',
    desc: '',
  },

  properties: {
    kdtId: {
      type: String,
    },
    componentData: {
      type: Object,
      value: {},
    },
  },

  async attached() {
    const { title, desc } = this.data.componentData || {};
    const { available } = await getSettingsForm();
    this.setData({
      title,
      desc,
      isShowCpn: available,
    });
  },

  methods: {
    toOrderPool() {
      app.globalData.shelfParams = {
        scene: 1,
      };
      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'joint_entry', // 事件标识
          en: '拼单入口', // 事件名称
          params: {
            entry: 'dashboard',
          }, // 事件参数
        });
      navigate.navigate({
        url: URL,
      });
    },
  },
});
