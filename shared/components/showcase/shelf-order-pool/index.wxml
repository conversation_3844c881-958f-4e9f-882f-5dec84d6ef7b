<view class="cap-order-pool" wx:if="{{isShowCpn}}" bind:tap="toOrderPool">
  <view class="cap-order-pool__left">
    <view class="cap-order-pool__left__title">{{title}}</view>
    <view class="cap-order-pool__left__desc">{{desc}}</view>
  </view>
  <view class="cap-order-pool__right">
    <van-button plain type="primary" round size="small" color="#ee0a24" custom-class="cap-order-pool__right__button">
      发起拼单
    </van-button>
  </view>
</view>