<view
  class="{{ hotelClass }}"
  style="margin: 0  {{ data.page_margin}}px {{ data.card_margin }}px"
  bindtap="goToHotel"
>
  <view class="hotel-img">
    <image
      src="{{ imgUrl }}"
      class="pic-bg"
      lazy-load="true"
    />
    <image
      src="{{ imgUrl }}"
      class="pic-img"
      lazy-load="true"
      mode="{{ data.size === '3' ? 'aspectFill' : 'aspectFit' }}"
    />
  </view>
  <view class="hotel-content">
    <view class="hotel-content-title">
      <view
        wx:if="{{ data.show_mark === '1' }}"
        class="hotel-content-title-tag"
      >
        <view class="hotel-content-title-tag-bg" style="background-color: {{ theme }}; opacity: .1;" />
        <view style="color: {{ theme }}">{{ data.mark_name }}</view>
      </view>
      <view class="hotel-content-title-name {{ titleClass }}">{{ hotel.name }}</view>
    </view>
    <view class="hotel-content-tag">
      <van-tag
        wx:for="{{ hotel.tags }}"
        wx:key="{{ item }}"
        color="{{ theme }}"
        custom-class="hotel-content-tag-item"
        plain
      >
        {{ item }}
      </van-tag>
    </view>
    <view class="hotel-content-address">
      <van-icon
        name="location-o"
        size="14"
        color="#969799"
      />
      <view class="span">{{ hotel.address.address }}</view>
    </view>
    <view class="hotel-content-price">
      <span style="color: {{ theme }}">￥</span>
      <span class="hotel-content-price-value" style="color: {{ theme }}">{{ price }}</span>
      <span>起</span>
    </view>
  </view>
</view>