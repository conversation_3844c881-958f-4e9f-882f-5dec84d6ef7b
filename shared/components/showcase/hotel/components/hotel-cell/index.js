import WscComponent from 'shared/common/base/wsc-component/index';
import money from '@youzan/weapp-utils/lib/money';
import openWebView from 'shared/utils/open-web-view';

const app = getApp();
const defaultImg = '//img01.yzcdn.cn/public_files/2019/09/10/179bbc6e77d7cd4f4b774f868b1b3c26.png';

const defaultHotelList = {
  address: {
    address: '这里显示酒店地址',
  },
  tags: ['酒店标签'],
  price: 2988,
  cover: {
    url: defaultImg,
  },
  id: null,
  name: '这里显示商品名称，最多显示2行',
};

WscComponent({
  properties: {
    data: {
      type: Object,
      value: {},
    },
    hotel: {
      type: Object,
      value: defaultHotelList,
    },
    theme: {
      type: String,
      value: '#333',
    },
  },

  data: {
    hotelClass: 'hotel',
    titleClass: '',
    price: '29.88',
    imgUrl: defaultImg,
  },

  attached() {
    const itemData = this.data.data;
    const itemHotel = this.data.hotel;
    this.setData({
      hotelClass:
        'hotel '
        + (+itemData.border_radius_type === 2 ? 'hotel-radius ' : '')
        + (+itemData.size_type === 7 ? 'hotel-shadow ' : '')
        + (+itemData.size_type === 5 ? 'hotel-border ' : '')
        + (+itemData.size_type === 2 ? 'hotel-simple ' : '')
        + (+itemData.size === 3 ? 'hotel-list ' : ''),
      titleClass: +itemData.text_style_type === 2 ? 'text-bolder' : '',
      price: money(itemHotel.price).toYuan(),
      imgUrl: (itemHotel.cover && itemHotel.cover.url) || defaultImg,
    });
  },

  methods: {
    goToHotel() {
      openWebView('/wscindustry/hotel/detail', {
        query: {
          kdtId: app.getKdtId() || 0,
          id: this.data.hotel.id,
        }
      });
    }
  },
});
