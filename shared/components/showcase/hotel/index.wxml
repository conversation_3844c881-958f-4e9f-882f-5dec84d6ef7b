<view wx:if="{{ show }}">
  <van-cell
    wx:if="{{ showTitle }}"
    is-link="{{ showTitleAll }}"
    value="{{ showTitleAll ? '查看全部' : '' }}"
    title="{{ componentData.title }}"
    border="{{ false }}"
    custom-class="hotel-title-cell"
    title-class="hotel-title"
    value-class="hotel-title-value"
    bindtap="goToList"
  />
  <view class="showcase-hotel-load" wx:if="{{ loading }}">
    <van-loading size="24px" vertical>加载中...</van-loading>
  </view>
  <view wx:else>
    <hotel-cell
      wx:for="{{ hotelList }}"
      wx:key="id"
      hotel="{{ item }}"
      data="{{ componentData }}"
      theme="{{ themeColor }}"
    />
  </view>
</view>