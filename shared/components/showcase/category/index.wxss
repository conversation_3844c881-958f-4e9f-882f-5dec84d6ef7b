.category {
  box-sizing: border-box;
  position: relative;
  padding-left: 90px;
  background-color: #f9f9f9;
}

.nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 90px;
  max-height: 100vh;
  overflow-y: scroll
}

.nav--fixed {
  position: fixed;
  top: 0;
  left: 0;
}

.nav-item {
  position: relative;
  padding: 16px;
  border: none;
  font-size: 14px;
  color: #666;
  word-break: break-all;
}

.nav-item--select {
  background-color: #fff;
  font-weight: 600;
  color: #333;
}

.nav-item--select::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 3px;
  height: 50%;
  transform: translateY(-50%);
  background-color: var(--main-bg, #323332);
}

.nav-skeleton {
  width: 100%;
  height: 87px;
}

.container {
  position: relative;
}

.hairline {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
}

.active-title {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 28px;
  padding-left: 15px;
  background-color: white;
  font-size: 12px;
  font-weight: 400;
  line-height: 28px;
  color: #3a3a3a;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 1;
}

.active-title--fixed {
  position: fixed;
  top: 0;
  left: 90px;
}

.groups {
  max-height: 100vh;
  background-color: white;
}

.group {
  position: relative;
  padding: 0 15px;
  overflow: hidden;
}

.group:last-child {
  min-height: 100vh;
  padding-bottom: 10px;
}

.banner {
  width: 100%;
  padding: 12px 0 7px;
}

.banner-img-wrap {
  position: relative;
  width: 100%;
  padding-top: 37%;
}

.banner-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.group-title {
  position: relative;
  height: 28px;
  background-color: white;
  font-size: 12px;
  font-weight: 400;
  line-height: 28px;
  color: #3a3a3a;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-top: 2px;
}

.list-item {
  flex: 1 1 30%;
  min-width: 30%;
  margin-bottom: 15px;
}

@media (min-width: 450px) {
  .list-item {
    align-items: center !important;
  }
}

.link {
  display: inline-block;
  width: 88%;
  max-width: 100px;
  font-size: 0;
}

@media (max-width: 350px) {
  .link {
    width: 96%;
  }
}

.link-img-wrap {
  display: inline-block;
  position: relative;
  width: 100%;
  padding-top: 100%;
}

.link-img {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
}

.link-title {
  display: block;
  margin-top: 10px;
  font-size: 14px;
  line-height: 18px;
  color: #3a3a3a;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
