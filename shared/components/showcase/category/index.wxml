<view class="category">
  <scroll-view
    scroll-y
    scroll-with-animation
    scroll-top="{{ navScrollTop }}"
    class="nav {{ navFixed ? 'nav--fixed' : '' }}"
  >
    <view
      wx:for="{{ categoryList }}"
      wx:key="{{ index }}"
      id="nav-item-{{ index }}"
      class="nav-item {{ index === activeIndex ? 'nav-item--select' : '' }}"
      data-nav-index="{{ index }}"
      bindtap="handleNavClick"
    >
      {{ item.title }}
    </view>
    <view class="nav-skeleton"></view>
  </scroll-view>

  <view class="container">
    <view class="hairline"></view>
    <view
      wx:if="{{ activeTitleIndex > -1 }}"
      class="active-title {{ navFixed ? 'active-title--fixed' : '' }}"
    >
      {{ categoryList[activeTitleIndex].title }}
    </view>

    <scroll-view
      class="groups"
      scroll-y
      scroll-with-animation
      scroll-into-view="{{ !shouldCheckActive ? 'group-' + activeIndex : null }}"
      bindscroll="handleScroll"
      bindtouchstart="handleGroupTouchStart"
    >
      <view
        class="group"
        wx:for="{{ categoryList }}"
        wx:key="{{ index }}"
        id="group-{{ index }}"
      >
        <cap-navigator
          wx:if="{{ item.banner && item.banner.imageUrl }}"
          item-info="{{ item.banner }}"
          link-type="{{ item.navInfo.linkType }}"
          app-id="{{ item.navInfo.appId }}"
          path="{{ item.navInfo.path }}"
          im="{{ extraData }}"
          bind:navigate="handleNavigate"
          bind:contactback="handleContactBack"
        >
          <view class="banner">
            <view class="banner-img-wrap">
              <image
                class="banner-img"
                src="{{ item.banner.imageUrl }}"
                mode="aspectFill"
                lazy-load
              />
            </view>
          </view>
        </cap-navigator>

        <view class="group-title">
          {{ item.title }}
        </view>

        <view class="list">
          <view
            class="list-item"
            wx:for="{{ item.subCategoryList }}"
            wx:for-item="link"
            wx:for-index="linkIndex"
            wx:key="{{ linkIndex }}"
            style="text-align: {{ ['left', 'center', 'right'][linkIndex % 3] }}"
          >
            <cap-navigator
              custom-class="link"
              item-info="{{ link }}"
              link-type="{{ link.navInfo.linkType }}"
              app-id="{{ link.navInfo.appId }}"
              path="{{ link.navInfo.path }}"
              im="{{ extraData }}"
              bind:navigate="handleNavigate"
              bind:contactback="handleContactBack"
            >
              <view class="link-img-wrap">
                <image
                  class="link-img"
                  src="{{ link.imageUrl }}"
                  mode="aspectFill"
                  lazy-load
                />
              </view>
              <text wx:if="{{ link.title }}" class="link-title">{{ link.title }}</text>
            </cap-navigator>
          </view>

          <view
            class="list-item"
            wx:for="{{ skeletonNumList[index] }}"
            wx:for-index="skeletonIndex"
            wx:key="{{ skeletonIndex }}"
          >
            <view class="link">
              <view class="link-img-wrap">
                <view class="link-img" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
