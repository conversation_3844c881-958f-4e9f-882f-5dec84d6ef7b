/* eslint-disable */
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import throttle from 'utils/throttle';
import getSystemInfo from 'shared/utils/browser/system-info';
import { getLink } from 'shared/components/showcase/utils/linkAdaptor';
/* eslint-enable */

const { windowHeight, system } = getSystemInfo();
const isAndroid = system.indexOf('Android') !== -1;

Component({
  properties: {
    componentData: {
      type: Object,
      value: {},
      observer: 'init'
    },
    extraData: {
      type: Object,
      value: {},
    },
  },

  data: {
    categoryList: [],
    skeletonNumList: [],
    activeIndex: 0,
    activeTitleIndex: -1,
    shouldCheckActive: false,
    navScrollTop: 0,
    navFixed: false
  },

  attached() {
    this.obList = []; // intersectionObserver
    this.groupHeightList = []; // 分组高度
    this.bannerHeightList = []; // 每个分组的 banner 高度
    this.top = 0; // 组件离顶部的距离
    this._handleScroll = null; // 节流的滚动处理函数
  },

  ready() {
    this.initObserver();
  },

  detached() {
    this.emptyObserver();
  },

  methods: {
    /*
     * 根据组件数据初始化
     */
    init() {
      let { categoryList = [] } = mapKeysCase.toCamelCase(this.properties.componentData);

      categoryList = categoryList.map(item => {
        const { subCategoryList = [], banner = {} } = item;

        // 子分类
        subCategoryList.forEach(item => {
          item.imageUrl = cdnImage(item.imageUrl, '!400x0.jpg'); // 压缩图片
          item.navInfo = getLink(item);
        });

        // banner
        if (banner.imageUrl) {
          banner.imageUrl = cdnImage(banner.imageUrl, '!650x0.jpg'); // 压缩图片
        }

        return {
          ...item,
          navInfo: getLink(banner),
          subCategoryList,
          banner
        };
      });

      this.setData({
        categoryList,
        skeletonNumList: this.getSkeletonNumList(categoryList),
        activeIndex: 0,
        activeTitleIndex: -1,
        shouldCheckActive: false,
        navScrollTop: 0,
        navFixed: false
      }, () => {
        this.calcGroupHeight();
      });
    },

    /*
     * 获取占位的数量
     */
    getSkeletonNumList(categoryList) {
      return categoryList.map(item => {
        const length = item.subCategoryList.length;
        const left = 3 - (length % 3);
        return left === 3 ? 0 : left;
      });
    },

    /*
     * 初始化 observer
     */
    initObserver() {
      const ob = this.createIntersectionObserver();

      ob.relativeToViewport().observe('.hairline', res => {
        if (res.intersectionRatio === 0) {
          this.setData({ navFixed: true });
        } else {
          this.setData({ navFixed: false });
        }
      });

      this.obList.push(ob);
    },

    // 清空 observer
    emptyObserver() {
      this.obList.forEach(ob => {
        ob.disconnect();
      });
      this.obList = [];
    },

    /*
     * 计算高度
     */
    calcGroupHeight() {
      // 计算整个分组的高度（包括 banner，子分类）
      wx.createSelectorQuery().in(this)
        .selectAll('.group')
        .boundingClientRect(rects => {
          if (!rects) {
            return;
          }
          this.groupHeightList = rects.map(rect => rect.height);
        })
        .exec();

      // 计算各个分组 banner 的高度
      wx.createSelectorQuery().in(this)
        .selectAll('.groups >>> .banner')
        .boundingClientRect(rects => {
          if (!rects || rects.length === 0) {
            return;
          }

          let i = 0;
          this.bannerHeightList = this.data.categoryList.map(item => {
            if (item.banner && item.banner.imageUrl) {
              return rects[i++].height;
            }
            return 0;
          });
        })
        .exec();

      // 计算组件距离顶部的高度
      wx.createSelectorQuery().in(this)
        .select('.category')
        .boundingClientRect(rect => {
          if (!rect) {
            return;
          }
          this.top = isAndroid ? rect.top : rect.top / 2;
        })
        .exec();
    },

    /*
     * 处理滚动事件
     */
    handleScroll(ev) {
      if (!this._handleScroll) {
        this.createScrollHandler();
      }
      this._handleScroll(ev);
    },

    /*
     * 创建节流滚动处理函数
     */
    createScrollHandler() {
      this._handleScroll = throttle(ev => {
        const { scrollTop, deltaY } = ev.detail;
        const {
          activeIndex,
          activeTitleIndex,
          shouldCheckActive,
          navFixed
        } = this.data;

        // 修正容器位置
        this.fixCategoryContainer(navFixed, deltaY);

        // 如果不用检查活跃项，则返回
        if (!shouldCheckActive) {
          return;
        }

        // 计算当前位置新的活跃项、活跃标题
        const [newActiveIndex, newActiveTitleIndex] = this.getActiveIndex(scrollTop);

        // 需要 setData 的数据
        const needUpdate = {
          activeTitleIndex: newActiveTitleIndex
        };

        if (activeIndex === newActiveIndex) {
          // 新老 active 和 titleActive 都相同，则不作处理
          if (activeTitleIndex === newActiveTitleIndex) {
            return;
          }
          this.setData(needUpdate);
          return;
        }

        needUpdate.activeIndex = newActiveIndex;

        // 调整左侧导航栏的位置
        wx.createSelectorQuery().in(this)
          .select(`#nav-item-${newActiveIndex}`)
          .boundingClientRect(rect => {
            const nav2Top = rect.top;
            wx.createSelectorQuery().in(this)
              .select('.nav')
              .scrollOffset(res => {
                needUpdate.navScrollTop = (nav2Top + res.scrollTop) - (windowHeight / 3);
                this.setData(needUpdate);
              })
              .exec();
          })
          .exec();
      }, 100);
    },

    /*
     * 修正右侧滚动容器的位置
     */
    fixCategoryContainer(navFixed, deltaY, threshold = 30) {
      if (navFixed && deltaY > threshold) {
        wx.pageScrollTo({
          scrollTop: this.top,
          duration: 0
        });
      }
    },

    /**
     * 获取活跃 index
     */
    getActiveIndex(scrollTop) {
      let totalHeight = 0;
      let activeIndex = 0;
      let activeTitleIndex = -1;

      this.groupHeightList.some((height, index) => {
        const { banner = {} } = this.data.categoryList[index];

        if (totalHeight + height > scrollTop) {
          // 滚动到当前的分组，更新分组活跃项
          activeIndex = index;

          // 滚动到 banner 的位置，则不显示活跃标题
          if (banner.imageUrl && totalHeight + this.bannerHeightList[index] > scrollTop) {
            activeTitleIndex = -1;
          } else {
            activeTitleIndex = index;
          }
          return true;
        }
        totalHeight += height;
        return false;
      });

      return [
        activeIndex,
        activeTitleIndex
      ];
    },

    /**
     * 点击左侧导航栏，更新活跃项，并禁止滚动检查活跃项
     */
    handleNavClick(ev) {
      const { navIndex } = ev.currentTarget.dataset;
      const { banner = {} } = this.data.categoryList[navIndex];
      const needUpdate = {
        activeIndex: navIndex,
        shouldCheckActive: false
      };

      if (banner.imageUrl) {
        needUpdate.activeTitleIndex = -1;
      } else {
        needUpdate.activeTitleIndex = navIndex;
      }

      this.setData(needUpdate);
    },

    /**
     * 触摸右侧分类列表，开启滚动检查活跃项
     */
    handleGroupTouchStart() {
      const { shouldCheckActive } = this.data;

      if (!shouldCheckActive) {
        this.setData({
          shouldCheckActive: true
        });
      }
    },

    handleNavigate({ detail = {} }) {
      this.triggerEvent('navigate', detail);
    },

    handleContactBack({ detail = {} }) {
      this.triggerEvent('contactback', detail);
    }
  }
});
