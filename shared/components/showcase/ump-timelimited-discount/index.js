import adaptor from '@youzan/feature-adaptor/es/timelimit-discount-v2/index';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import Args from '@youzan/weapp-utils/lib/args';
import { startPageCountdown } from 'shared/utils/countdown-list';
import { getCountdown } from 'shared/components/showcase/captain-components/ump-goods-layout/helper';
import loggerBehavior from '../behaviors/logger-behavior';
import componentBehavior from '../behaviors/component-behavior';
import countdownUtilBehavior from '../captain-components/ump-goods-layout/behaviors/countdown-util-behavior';
import goodsLayoutBehavior from '../captain-components/goods-layout/behaviors/goods-layout-behavior';
import { LAYOUT_MAP } from '../captain-components/goods-layout/constants';
import goodsComponentBehavior from '../behaviors/goods-component-behavior';
import navigate from '../../../utils/navigate';
import { getLimitDiscountData } from './api';
import {
  COMPONENT_NAME,
  ACTIVITY_STATUS,
  EDU_ABILITY,
  skeletonDefaultNumber,
} from './common/constants';
import * as helper from './common/helper';

const { new2captain, old2new } = adaptor;

Component({
  behaviors: [
    goodsLayoutBehavior,
    componentBehavior,
    countdownUtilBehavior,
    loggerBehavior,
    goodsComponentBehavior,
  ],

  properties: {
    redirectType: {
      type: Number,
      value: 3,
    },

    pageRandomString: {
      type: String,
      value: '',
      observer() {
        const { itemData } = this.data;
        // 没有经过adaptor 不触发倒计时更新
        if (!itemData.type) return;
        this.setData(
          {
            isLoading: true,
          },
          () => {
            this.refreshCountDown();
          }
        );
      },
    },
  },
  /** 当某次拉取限时折扣时不足pageSize，会触发下一次获取，增加一个限制数目，防止一直获取到结束 */
  limit: 0,

  data: {
    itemData: { list: [] },
    // 列表数据单独拿出来
    curGoodsList: [],
    // 合并cap-limit-discount的数据
    infoData: {},
    isComponentShow: false,
    isLoading: true,
    page: 0,
    loadFinished: false,
    showCountdownBanner: false,
    skeletonDefaultNumber,
  },

  attached() {
    this.init();
  },

  detached() {
    if (this.recycleCdFn) {
      this.recycleCdFn();
      this.recycleCdFn = null;
    }
  },

  methods: {
    init() {
      const { componentData = {} } = this.data;
      // 兼容商品详情页没有layout字段
      const { layout = null, size = null } = componentData;
      const { infoData, itemData } = this.computeItemData(componentData);

      this.setData(
        {
          isSwipeLayout: this.checkIsSwipeLayout(layout || size),
          showCountdownBanner:
            infoData.showCountDown && this.computeIsThreeOrSwipe(layout),
          camelComponentData: mapKeysCase.toCamelCase(componentData),
          infoData,
          itemData,
        },
        () => {
          this.refreshList();
        }
      );
    },

    reset() {
      this.setData(
        {
          curGoodsList: [],
          isComponentShow: false,
          page: 0,
          loadFinished: false,
        },
        this.init
      );
    },

    onPullDownRefresh() {
      const { isLoading } = this.data;
      if (isLoading) return;

      this.reset();
    },

    refreshList() {
      const {
        kdtId = '',
        page = 0,
        isSwipeLayout,
        camelComponentData = {},
        curGoodsList: oldList = [],
      } = this.data;
      const {
        activityId = '',
        goods = [],
        itemIds = '',
        v: version = '',
        layout,
      } = camelComponentData;
      const goodsIds = goods.map((item) => item.itemId || item.goodsId);
      const ids = itemIds || goodsIds.join(',');
      const pageSize = this.getPageSize(layout, ids.split(',').length);
      const idsArr = helper.getPagingIds(ids, page + 1, pageSize);
      const paramIds = idsArr.join(',');

      if (!ids || !paramIds) {
        this.setFinished();
        return;
      }
      this.setData({
        isLoading: true,
      });

      getLimitDiscountData({
        kdtId,
        activityId,
        goodsIds: paramIds,
        v: version,
      })
        .then((res) => {
          if (!res || !Array.isArray(res.list)) {
            this.setFinished();
            return;
          }
          const isFinished = paramIds.split(',').length < pageSize;
          const componentData = {
            page: page + 1,
            isLoading: false,
            loadFinished: isFinished,
            isComponentShow: true,
          };
          const { length } = res.list;
          const oldLength = oldList.length;

          if (length) {
            const curGoodsList = this.computeGoodsList(res.list);
            const startIndex = page === 0 ? 0 : oldLength;
            curGoodsList.forEach((item, index) => {
              componentData[`curGoodsList[${startIndex + index}]`] = item;
            });
          }

          this.setData(componentData, () => {
            if (isSwipeLayout) {
              this.setSwiperStatus();
            }
            this.updateCountdown();
            // 加载商品大于等于初始商品数量
            const {
              curGoodsList = [],
              itemData: { goods = [] },
            } = this.data;
            if (curGoodsList.length >= goods.length) {
              this.setFinished();
            }

            if (!isFinished && res.list.length < pageSize) {
              // 从该版本开始，异步获取处理限时折扣，读取到的数据多网点情况下可能缺少商品，继续读取下一页的商品数据
              this.limit += res.list.length;
              if (this.limit < pageSize) {
                this.refreshList();
              } else {
                this.limit = 0;
              }
            }
          });
        })
        .catch(() => {
          this.setFinished();
        });
    },

    // componentData里的属性字段 都是下划线的字段
    computeItemData(componentData) {
      const { v: version, layout } = componentData;
      let itemData = null;

      if (+version === 2) {
        // 修复商品详情页的数据，由于走的camen未经过iron-base层转化，就会丢失layout字段
        if (layout === undefined) {
          itemData = new2captain(componentData);
        } else {
          itemData = mapKeysCase.toCamelCase(componentData);
        }
      } else {
        itemData = new2captain(old2new(componentData));
      }
      const infoData = this.computeInfoData(itemData);
      return { itemData, infoData };
    },

    setSwiperStatus() {
      this.selectComponent('#cap-goods-layout').setSwipeStatus({
        loading: false,
        swipeFinished: this.data.loadFinished,
      });
    },

    computeGoodsList(list = []) {
      const { itemData, kdtId } = this.data;
      const { layout } = itemData;
      const loggerList = [];
      const newList = list.map((item, index) => {
        const bannerId = this.getBannerId(index + 1);
        const loggerParams = {
          goods_id: item.itemId,
          item_id: item.itemId,
          item_type: 'limitdiscount',
          banner_id: bannerId,
          ...this.getComponentLoggerExtraParams(),
        };
        const url = Args.add('/pages/goods/detail/index', {
          alias: item.alias,
          kdt_id: kdtId,
          banner_id: bannerId,
          ...this.getComponentLoggerExtraParams(),
        });
        const abilityMarkCodeList = item.abilityMarkCodeList || [];
        const isOlEduGoods = !!abilityMarkCodeList.find(
          (code) =>
            code === EDU_ABILITY.LIVE ||
            code === EDU_ABILITY.CONTENT ||
            code === EDU_ABILITY.COLUMN
        );
        const isEduGoods = !!abilityMarkCodeList.find(
          (code) =>
            code === EDU_ABILITY.LIVE ||
            code === EDU_ABILITY.CONTENT ||
            code === EDU_ABILITY.COLUMN ||
            code === EDU_ABILITY.COURSE
        );
        const isOffEduGoods = isEduGoods && !isOlEduGoods;
        item.loggerParams = loggerParams;
        loggerList.push(loggerParams);

        return {
          ...item,
          url,
          isSoldout: +item.stockNum <= 0,
          isEnd: helper.isEnd(item),
          showOriginPrice: helper.getGoodsShowOriginPrice(item),
          titleTagText: helper.getGoodsDiscountText(item),
          salePercent: helper.getGoodsSalePercent(item),
          buttonText: null,
          countdown: helper.getCountdown(item),
          countdownText: helper.getCountdownText(item, layout),
          description: item.description || COMPONENT_NAME,
          activityStatus: helper.getActivityStatus(item),
          itemIndex: index,
          originPrice: item.price,
          price: item.discountPrice,
          tag: item.description || COMPONENT_NAME,
          loggerParams: item.loggerParams,
          isOlEduGoods,
          isEduGoods,
          isOffEduGoods,
        };
      });
      this.ensureAppLogger('view', loggerList);
      return newList;
    },

    setFinished() {
      const { curGoodsList } = this.data;
      const list = curGoodsList.filter((item) => !item.skeleton);
      const data = {
        isLoading: false,
        loadFinished: true,
      };
      if (curGoodsList.length !== list.length) {
        data.curGoodsList = list;
      }
      this.setData(data);
    },

    refreshCountDown() {
      this.updateCountdown();

      this.setData({
        isComponentShow: true,
        isLoading: false,
      });
    },

    handleNavigate({
      detail: { url = '', abilityMarkCodeList = [], alias = '' },
    }) {
      const { baseUrl, extraParams } = this.getBaseUlr(
        url,
        abilityMarkCodeList,
        alias
      );
      const navigateUrl = Args.add(baseUrl, extraParams);
      navigate.navigate({ url: navigateUrl });
    },

    getBaseUlr(url, abilityMarkCodeList, alias) {
      let [baseUrl = '/pages/goods/detail/index', search = ''] =
        url.split('?');
      const code = abilityMarkCodeList || [];
      const { kdtId = '' } = this.data;
      const extraParams = {};
      if (code.find((_) => +_ === EDU_ABILITY.LIVE)) {
        baseUrl = '/packages/paidcontent/live/index';
      } else if (code.find((_) => +_ === EDU_ABILITY.CONTENT)) {
        baseUrl = '/packages/paidcontent/content/index';
      } else if (code.find((_) => +_ === EDU_ABILITY.COLUMN)) {
        baseUrl = '/packages/paidcontent/column/index';
      } else if (code.find((_) => +_ === EDU_ABILITY.COURSE)) {
        baseUrl = '/packages/edu/webview/index';
        const webviewUrl = encodeURIComponent(
          `https://h5.youzan.com/wscvis/edu/prod-detail?kdt_id=${kdtId}&alias=${alias}`
        );
        extraParams.targetUrl = webviewUrl;
      }
      return { baseUrl: `${baseUrl}?${search}`, extraParams };
    },

    handleLoadMore() {
      const { loadFinished, isLoading } = this.data;

      if (loadFinished || isLoading) {
        return;
      }

      this.setData(
        {
          isLoading: true,
        },
        () => {
          this.refreshList();
        }
      );
    },

    handleBuyBtnClick(goodsInfo) {
      this.$emit('buy-btn-click', goodsInfo);
    },

    updateCountdown() {
      const { curGoodsList } = this.data;
      if (!curGoodsList.length) {
        return;
      }
      if (this.recycleCdFn) {
        this.recycleCdFn();
        this.recycleCdFn = null;
      }

      const goodsInfo = curGoodsList[0];
      const change = this.updateCountdownData.bind(this);
      const end = this.onCountdownEnd.bind(this);
      const countdown = getCountdown({
        ...goodsInfo,
        activityStatus: helper.getActivityStatus(goodsInfo),
      });
      // 初始化计时器，返回
      this.recycleCdFn = startPageCountdown(countdown, { change, end });
    },

    onCountdownEnd() {
      const { curGoodsList } = this.data;
      const activityStatus = helper.getActivityStatus(curGoodsList[0]);
      if (activityStatus === ACTIVITY_STATUS.END) {
        this.setData({
          'infoData.isEnd': true,
          'infoData.closeCoundown': true,
        });
      } else {
        this.updateCountdown();
      }
    },

    updateCountdownData(timeDataArr, strDataArr) {
      const { itemData = {}, curGoodsList = [] } = this.data;
      const { layout, buttonText } = itemData;
      const goodsInfo = curGoodsList[0];
      const countdownText = helper.getCountdownText(goodsInfo, layout);
      this.setData({
        'infoData.countdownData': strDataArr,
        'infoData.countdownText': countdownText,
        'infoData.buttonText': helper.getBtnText(goodsInfo, buttonText),
      });
    },

    computeInfoData(itemData) {
      const {
        showProgressInfo,
        showCountdown = true,
        showStockNum,
        showTitle,
        showSubTitle,
        showPrice,
        showOriginPrice,
        showBuyButton,
        buyButtonType,
        processBarType,
        textAlignType,
        textStyleType,
        layout,
      } = itemData;
      const showTimeLimit =
        showProgressInfo && !this.computeIsThreeOrSwipe(layout);

      return {
        controlled: true,
        countdownData: {
          day: 0,
          hour: 0,
          minute: 0,
          second: 0,
        },
        showCountDown: showCountdown,
        showTitle,
        showSubTitle,
        showStockNum,
        showTimeLimit,
        showPrice,
        showOriginPrice,
        showBuyButton,
        buyButtonType,
        processBarType,
        textAlignType,
        textStyleType,
        showTitleTag: true,
      };
    },

    computeIsThreeOrSwipe(lay) {
      const layout = lay || (this.data.itemData || {}).layout;
      return layout === LAYOUT_MAP.SWIPE || layout === LAYOUT_MAP.THREE;
    },
  },
});
