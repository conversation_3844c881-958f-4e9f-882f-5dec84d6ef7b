<block wx:if="{{ isSwipeLayout }}">
  <template is="limitdiscount" data="{{ curGoodsList, infoData, isComponentShow, showCountdownBanner, appId, itemData, redirectType, extraData, pageData, pageUtils, isSwipeLayout, loadFinished }}"></template>
</block>

<cap-list
  wx:else
  loading="{{ isLoading }}"
  finished="{{ loadFinished }}"
  disabled="{{ isSwipeLayout }}"
  bind:load="handleLoadMore"
>
  <template is="limitdiscount" data="{{ curGoodsList, infoData, isComponentShow, showCountdownBanner, appId, itemData, redirectType, extraData, pageData, pageUtils, isSwipeLayout, loadFinished }}"></template>
</cap-list>

<template name="limitdiscount">
  <view class="c-limited-discount" wx:if="{{ isComponentShow }}">
    <countdown-banner
      page-utils="{{ pageUtils }}"
      layout="{{ itemData.layout }}"
      page-data="{{ pageData }}"
      wx:if="{{ showCountdownBanner && curGoodsList.length }}"
      goods-info="{{ curGoodsList[0] }}"
      info-data="{{ infoData }}"
    />

    <goods-layout
      generic:goods-item-expand="goods-item-expand"
      generic:goods-item-tag-info="goods-item-tag-info"
      generic:goods-item-price="goods-item-price"
      generic:buy-button="buy-button"
      list="{{ curGoodsList }}"
      layout="{{ itemData.layout }}"
      image-fill-style="{{ itemData.imageFillStyle }}"
      image-ratio="{{ itemData.imageRatio }}"
      size-type="{{ itemData.sizeType }}"
      page-margin="{{ itemData.pageMargin }}"
      goods-margin="{{ itemData.goodsMargin }}"
      border-radius-type="{{ itemData.borderRadiusType }}"
      swipe-load-max-times="{{ itemData.goods.length / 10 }}"
      open-swipe-pagination="{{ isSwipeLayout && curGoodsList.length !== 0 && !loadFinished }}"
      extra-data="{{ extraData }}"
      app-id="{{ appId }}"
      page-utils="{{ pageUtils }}"
      page-data="{{ pageData }}"
      redirect-type="{{ redirectType }}"
      info-data="{{ infoData }}"
      skeleton-number="{{ loadFinished ? 0 : skeletonDefaultNumber }}"
      bind:item-click="handleNavigate"
      bind:buy="handleNavigate"
      bind:load-more="handleLoadMore"
      id="cap-goods-layout"
      show-origin-price-by-goods-info="true"
    />
  </view>     
</template>
