import {
  getCountdownText,
  getCountdown
} from 'shared/components/showcase/captain-components/ump-goods-layout/helper';
import countdownUtilBehavior from 'shared/components/showcase/captain-components/ump-goods-layout/behaviors/countdown-util-behavior';

Component({
  behaviors: [countdownUtilBehavior],

  properties: {
    goodsInfo: {
      type: Object,
      value: {}
    },

    infoData: {
      type: Object,
      value: {}
    },

    layout: {
      type: Number,
      value: 0
    }
  },

  attached() {
    const { layout, infoData = {}, goodsInfo } = this.data;
    const { showCountDown } = infoData;

    this.setData({
      isShowTagText: true,
      countdownType: 'diamond',
      isShowCountdown: showCountDown && !goodsInfo.isEnd,
      timeSeparator: [':', ':', ':'],
      countdownText: getCountdownText(goodsInfo, layout),
      countdown: getCountdown(goodsInfo)
    });
  }
});
