<view
  wx:if="{{ isShowCountdown }}"
  class="limited-discount__banner"
>
  <view
    class="limited-discount__banner-left"
  >
    <view class="limited-discount__banner-label">{{ goodsInfo.tag || '限时折扣' }}</view>
    <countdown
      class="limited-discount__banner-countdown"
      custom-class="limited-discount__banner-countdown"
      type="{{ countdownType }}"
      controlled="{{ controlled }}"
      countdown-data="{{ infoData.countdownData || pageData[pageData.countdownKey][countdown] }}"
      time-separator="{{ timeSeparator }}"
      page-utils="{{ pageUtils }}"
    />
  </view>
  <!-- TODO：业务复杂后面日常再加 <view class="limited-discount__banner-more">更多</view> -->
</view>
