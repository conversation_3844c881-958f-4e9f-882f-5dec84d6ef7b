<view
  wx:if="{{ infoData.showTimeLimit || infoData.showStockNum }}"
  class="c-discount__expand {{ infoData.showTimeLimit && infoData.processBarType == 2 ? 'c-discount__expand--type2' : ''}}"
>
  <block wx:if="{{ !goodsInfo.skeleton }}">
    <theme-view
      wx:if="{{ infoData.showTimeLimit }}"
      bg="{{ infoData.processBarType == 2 ? 'vice-bg' : '' }}"
      border="main-bg"
      reverseGradient="{{ goodsInfo.isOlEduGoods }}"
      inner-style="{{ infoData.processBarType == 2 ? 'border-width:1px;border-style:solid;border-radius: 100px;height: 16px;' : '' }}"
      class="discount__bar"
    >
      <theme-view
        custom-class="discount__bar-inner"
        inner-style="width: {{ goodsInfo.isOlEduGoods ? 100 : goodsInfo.salePercent }}% "
        bg="general"
        gradient
        gradientDeg="{{ goodsInfo.isOlEduGoods ? 180 : 90 }}"
        twillGradient="{{ goodsInfo.isOlEduGoods }}"
      />
    </theme-view>
    <view class="discount__sales-info">
      <view class="discount__rest" wx:if="{{ infoData.showStockNum }}">
        {{ goodsInfo.isOffEduGoods ?  '剩余' : goodsInfo.isOlEduGoods ? '火热报名' : '仅剩' }}
        <theme-view
          wx:if="{{ !goodsInfo.isOlEduGoods }}"
          custom-class="discount__rest-num"
          color="main-bg"
        >{{ ' ' + goodsInfo.stockNum + ' ' }}</theme-view>
        {{ goodsInfo.isOffEduGoods ?  '名' : goodsInfo.isOlEduGoods ? '' : '件' }}
      </view>
      <view class="discount__sales"
        wx:if="{{ infoData.showTimeLimit }}"
      >
        {{ goodsInfo.isEduGoods ? '' : '已抢' }}
        <theme-view inner-style="display:inline;" color="main-bg">
          {{ ' ' + goodsInfo.totalSoldNum + ' ' }}
        </theme-view>
        {{ goodsInfo.isEduGoods ? '人已学' : '件' }}
      </view>
    </view>
  </block>

  <cap-skeleton
    wx:else
    type="rect"
    rect-style="width: 100%; height: 24px;"
  />
</view>
