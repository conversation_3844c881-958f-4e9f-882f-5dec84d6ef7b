import {
  DISCOUNT_TYPE,
  ACTIVITY_STATUS,
  MIN_PROCESSBAR_PERCENT_WIDTH,
} from './constants';
import { LAYOUT_MAP } from '../../captain-components/goods-layout/constants';

export const getActivityStatus = ({ startAt, endAt }) => {
  const nowTime = new Date().getTime();

  if (startAt > nowTime) {
    return ACTIVITY_STATUS.NOT_START;
  }

  if (endAt < nowTime) {
    return ACTIVITY_STATUS.END;
  }

  return ACTIVITY_STATUS.START;
};

/**
 * 如果折扣类型为折扣且折扣值小于10或类型为减价且减后价小于原价，则返回 true。否则，返回 false
 * @returns boolean。
 */
export const getGoodsShowOriginPrice = ({
  discountType,
  discountValue,
  discountPrice,
  price,
}) => {
  if (+discountType === DISCOUNT_TYPE.discount) {
    // 大于等于10折的不显示
    if (discountValue >= 10) {
      return false;
    }
  } else if (+discountType === DISCOUNT_TYPE.amount) {
    // 减后的价格大于于原价的不展示
    if (+discountPrice > +price) {
      return false;
    }
  }

  return true;
};

/**
 * 获取折扣文案
 */
export const getGoodsDiscountText = ({
  discountType,
  discountValue,
  discountPrice,
  price,
}) => {
  if (+discountType === DISCOUNT_TYPE.discount) {
    // 大于等于10折的不显示
    if (discountValue < 10) {
      return `${discountValue}折起`;
    }
  } else if (+discountType === DISCOUNT_TYPE.amount) {
    // 减后的价格大于于原价的不展示
    if (+discountPrice < +price) {
      return `减${discountValue}元起`;
    }
  }
};

export const getGoodsSalePercent = ({ totalSoldNum, stockNum }) => {
  let percent = Math.ceil((totalSoldNum / (totalSoldNum + stockNum)) * 100);

  if (percent > 0 && percent < MIN_PROCESSBAR_PERCENT_WIDTH) {
    percent = MIN_PROCESSBAR_PERCENT_WIDTH;
  }

  return percent;
};

export function isSoldOut(goodsInfo) {
  return +goodsInfo.stockNum <= 0;
}

export const isEnd = (goodsInfo) => {
  return getActivityStatus(goodsInfo) === ACTIVITY_STATUS.END;
};

export function isNotStart(goodsInfo) {
  return getActivityStatus(goodsInfo) === ACTIVITY_STATUS.NOT_START;
}

export const getBtnText = (goodsInfo, configText) => {
  let text = '';

  if (isNotStart(goodsInfo)) {
    text = '即将开抢';
  } else if (isEnd(goodsInfo) || isSoldOut(goodsInfo)) {
    text = '去看看';
  } else {
    text = configText || '立即抢购';
  }

  return text;
};

export const getCountdown = (goodsInfo = {}) => {
  const { startAt, endAt } = goodsInfo;
  let countdown;

  const nowTime = new Date().getTime();
  if (isNotStart(goodsInfo)) {
    countdown = startAt - nowTime;
  } else if (isEnd(goodsInfo)) {
    countdown = 0;
  } else {
    countdown = endAt - nowTime;
  }

  return countdown;
};

export const getCountdownText = (goodsInfo, layout) => {
  const isBig = +layout === LAYOUT_MAP.BIG;

  if (isNotStart(goodsInfo)) {
    return isBig ? '距开始仅剩' : '距开始';
  }

  if (isEnd(goodsInfo)) {
    return '';
  }

  return isBig ? '距结束仅剩' : '距结束';
};

export const getPagingIds = (ids, page, pageSize) => {
  const goodsStartPos = (page - 1) * pageSize;

  return ids.split(',').slice(goodsStartPos, goodsStartPos + pageSize);
};
