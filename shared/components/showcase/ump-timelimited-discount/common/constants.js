/**
 * 活动状态
 * @enum {number}
 */
export const ACTIVITY_STATUS = {
  NOT_START: 0,
  START: 1,
  END: 2
};

/**
 * 显示折扣优惠类型
 * @enum {number}
 */
export const DISCOUNT_TYPE = {
  discount: 2,
  amount: 3
};

/**
 * 最小进度条宽度，如果太小会有样式问题
 */
export const MIN_PROCESSBAR_PERCENT_WIDTH = 5;

export const COMPONENT_NAME = '限时折扣';

/** 线上教育课 */
export const EDU_ABILITY = {
  LIVE: 90001,
  CONTENT: 90002,
  COLUMN: 90003,
  COURSE: 10007
};

export const skeletonDefaultNumber = 2;

export const skeletonGoodsList = new Array(skeletonDefaultNumber).fill({
  skeleton: true
});
