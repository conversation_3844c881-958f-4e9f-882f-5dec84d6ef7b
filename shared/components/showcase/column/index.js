import pick from '@youzan/weapp-utils/lib/pick';
import { node as request } from 'shared/utils/request';
import componentBehavior from '../behaviors/component-behavior';
import { shopCanPayForKnowledge, parseTime } from '../utils/pctUtils';
import loggerBehavior from '../behaviors/logger-behavior';

Component({
  options: {
    addGlobalClass: true
  },

  type: 'column',

  behaviors: [componentBehavior, loggerBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
    },
    kdtId: {
      type: String
    }
  },

  data: {
    itemType: 'column',
    title: '',
    showTitle: false,
    showTitleAll: false,
    list: [],
    hideContentSubsCount: 0
  },

  attached() {
    console.log('come to column');
    const itemData = this.data.componentData;
    this.setData({
      itemType: 'column',
      title: itemData.title || '',
      showTitle: parseInt(itemData.show_title, 10) === 1,
      showTitleAll: parseInt(itemData.show_title_all, 10) === 1,
      list: itemData.sub_entry.map((item) => {
        return {
          ...pick(item, 'alias', 'cover', 'title', 'summary'),
          price: item.price,
          count: +item.contents_count,
          subCount: +item.subscriptions_count,
          showPriceInfo: this.showPriceInfo()
        };
      }),
      hideContentSubsCount: 0
    });
    this.getColumnList(itemData);
  },

  methods: {
    showPriceInfo() {
      return shopCanPayForKnowledge();
    },

    getColumnList(itemData) {
      if (itemData.column_from === 'custom') {
        itemData.ids = itemData.sub_entry.map(item => item.id).join(',');
      }

      request({
        path: '/wscshop/showcase/knowledge/columnlist',
        data: {
          source: itemData.column_from === 'custom' ? 1 : 0,
          ids: itemData.ids ? itemData.ids : '',
          kdt_id: this.data.kdtId,
          pageSize: 3
        }
      }).then((result) => {
        if (!result) {
          return;
        }
        this.setData({ list: this.mergeColumn(itemData, result) });
      });
    },

    mergeColumn(itemData, newItemData) {
      function getOldItemById(id) {
        for (let i = 0; i < itemData.sub_entry.length; i++) {
          let item = itemData.sub_entry[i];

          if (item.id === id) {
            return item;
          }
        }

        return {};
      }

      let list = [];
      const loggerList = [];

      list = newItemData.filter((item) => item != null).map((newItem, index) => {
        let {
          buyStatus = {},
          contentCount = 0,
          cardUserCount = 0,
          picture = {},
          lastUpdatedInfo = {}
        } = newItem;
        lastUpdatedInfo.publishAt = parseTime(lastUpdatedInfo.publishAt);
        const oldItem = getOldItemById(newItem.id);

        const bannerId = this.getBannerId(index + 1);
        const loggerParams = {
          goods_id: newItem.goodsId,
          item_id: newItem.id,
          item_type: 'paid_column',
          component: 'paid_column',
          banner_id: bannerId,
          ...this.getComponentLoggerExtraParams()
        };
        loggerList.push(loggerParams);

        return {
          ...oldItem,
          ...newItem,
          ...buyStatus,
          ...picture,
          lastUpdatedInfo,
          price: buyStatus.price || 0,
          isVip: !!buyStatus.isVipDiscount,
          count: contentCount,
          subCount: cardUserCount,
          isFree: buyStatus.isFree === 1,
          showPriceInfo: this.showPriceInfo()
        };
      });

      this.ensureAppLogger('view', loggerList);

      return list;
    },

    goToAllColumn() {
      wx.navigateTo({
        url: '/packages/paidcontent/list/index?type=column'
      });
    }
  },
});
