.theme-feature-column {
  background: #fff;
}

.column-title {
  display: flex;
  position: relative;
  padding: 0 15px;
  flex-direction: row;
  height: 45px;
  overflow-x: hidden;
  align-items: center;
  justify-content: space-between;
}

.column-title::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border-bottom: 1px solid #eee;
}

.theme-feature-column__all-text {
  font-size: 13px;
  color: #999;
}

.theme-feature-column__view-all {
  display: flex;
  justify-content: center;
  align-items: center;
}

.theme-feature-column__van-icon {
  display: block !important;
  font-size: 10px !important;
  margin-left: 4px;
  color: #999;
}

.theme-feature-column__title-text {
  color: #333;
  font-weight: 700;
  font-size: 15px;
}

.all-text {
  font-size: 13px;
  color: #999;
}

.title-text {
  color: #333;
  font-weight: 700;
  font-size: 15px;
}
