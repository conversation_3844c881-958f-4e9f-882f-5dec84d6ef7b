<view>
  <view class="theme-feature-column van-hairline--top-bottom">
    <view wx:if="{{ showTitle }}" class="column-title">
      <text class="title-text">{{ title }}</text>
      <view wx:if="{{ showTitleAll }}" class="theme-feature-column__view-all">
        <text class="theme-feature-column__all-text" bindtap="goToAllColumn">全部</text>
        <van-icon name="arrow" custom-class="theme-feature-column__van-icon" />
      </view>
    </view>
    <block wx:for="{{ list }}" wx:key="id">
      <cap-column-item
        column="{{ item }}"
        show-price-info="{{ item.showPriceInfo }}"
      />
    </block>
  </view>
</view>
