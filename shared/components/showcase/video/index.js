import Events from '@youzan/weapp-utils/lib/event';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { countPlay, getPlayInfo } from './api';

Component({
  behaviors: [componentBehavior, loggerBehavior],
  data: {
    paid: false, // 播放付费标识
    src: '',
    poster: '',
    countUrl: ''
  },

  attached() {
    const { componentData } = this.data;

    this.initVideo(componentData);
    this.initEvents();
  },

  methods: {
    initVideo(itemData) {
      const {
        video = {},
        sourceFrom = 1,
        customUrl = '',
        surfacePlot = 1,
        surfacePlotImage = ''
      } = itemData;
      const {
        cover_url: coverUrl = '',
        video_id: videoId = 0
      } = video;
      const CUSTOM_POSTER = 2;

      if (sourceFrom === 1) {
        getPlayInfo({
          videoId,
          kdt_id: this.data.hqKdtId || this.data.kdtId,
        }).then((result) => {
          if (!result) {
            return;
          }

          const {
            video_url: src = '',
            count_played_url: countUrl = ''
          } = result;
          this.triggerEvent('get-src', {
            id: videoId,
            src
          });

          this.setData({
            src,
            countUrl,
            poster: surfacePlot === CUSTOM_POSTER
              ? surfacePlotImage
              : coverUrl
          });
        });
      } else {
        this.setData({
          src: customUrl,
          poster: surfacePlotImage,
        });
      }
    },

    initEvents() {
      Events.on('pauseOtherVideo', (context) => {
        if (this !== context) {
          let video = this.selectComponent('.capVideo');
          video.pause();
        }
      });
    },

    handlePlay() {
      this.sendStartLogger();
      Events.trigger('pauseOtherVideo', this);

      if (!this.data.paid && this.data.countUrl) {
        this.data.paid = true;
        countPlay(this.data.countUrl || '');
      }
    },

    sendStartLogger() {
      if (this.startLogger) {
        return;
      }

      this.startLogger = true;
      this.ensureAppLogger('logger', {
        et: 'click',
        ei: 'play',
        en: '开始播放',
        params: {
          banner_id: this.getBannerId(),
          ...this.getComponentLoggerExtraParams(),
        }
      });
    },

    sendStopLogger() {
      if (this.hasSendStopLogger) {
        return;
      }

      this.hasSendStopLogger = true;
      this.ensureAppLogger('logger', {
        et: 'custom',
        ei: 'play_time',
        en: '播放时长',
        params: {
          time: Math.ceil(this.currentTime),
          banner_id: this.getBannerId(),
          ...this.getComponentLoggerExtraParams(),
        }
      });
    },

    handleUpdateTime({ detail: { currentTime = 0 } }) {
      this.currentTime = currentTime;
    }
  }
});
