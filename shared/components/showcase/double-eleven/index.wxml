<view
  class="cap-double-eleven {{ 'cap-double-eleven--' + subEntry.length }}"
  style="background-image: url({{ bgImageUrl }})"
>
  <view
    wx:for="{{ subEntry }}"
    wx:for-item="entry"
    wx:key="{{ index }}"
    class="cap-double-eleven__entry {{ 'cap-double-eleven__entry-' + index }}"
  >
    <cap-navigator
      custom-class="cap-double-eleven__entry-wrap"
      item-info="{{ entry }}"
      data-index="{{ index }}"
      link-type="{{ entry.navInfo.linkType }}"
      app-id="{{ entry.navInfo.appId }}"
      path="{{ entry.navInfo.path }}"
      im="{{ extraData }}"
      bind:navigate="handleNavigate"
      bind:contactback="handleContactBack"
    >
      <image
        class="cap-double-eleven__entry-img"
        lazy-load
        src="{{ entry.imageUrl }}"
        mode="aspectFill"
      />
      <view
        wx:if="{{ entry.title }}"
        class="cap-double-eleven__entry-title"
        style="color: {{ textColor }}"
      >
        {{ entry.title }}
      </view>
    </cap-navigator>
  </view>
</view>
