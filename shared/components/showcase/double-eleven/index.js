/* eslint-disable */
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { getLink } from 'shared/components/showcase/utils/linkAdaptor';
/* eslint-enable */

Component({
  properties: {
    componentData: {
      type: Object,
      value: {},
      observer: 'transformData'
    },
    extraData: {
      type: Object,
      value: {},
    },
  },

  methods: {
    transformData(newVal) {
      const {
        bgStyle = '1',
        bgImageUrl = '',
        textColor = '#fff',
        subEntry = [],
      } = mapKeysCase.toCamelCase(newVal || this.properties.componentData);

      const newSubEntry = subEntry.map(item => ({
        ...item,
        navInfo: getLink(item),
        imageUrl: cdnImage(item.imageUrl, '!450x0.jpg')
      }));

      this.setData({
        bgStyle: +bgStyle,
        bgImageUrl: cdnImage(bgImageUrl, '!800x0.jpg'),
        textColor,
        subEntry: newSubEntry
      });
    },

    handleNavigate({ detail = {} }) {
      this.triggerEvent('navigate', detail);
    },

    handleContactBack({ detail = {} }) {
      this.triggerEvent('contactback', detail);
    }
  }
});
