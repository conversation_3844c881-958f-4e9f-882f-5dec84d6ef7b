Component({
  properties: {
    styleType: String,
    coupon: Object,
  },

  data: {
    valueStyle: '',
  },

  ready() {
    setTimeout(() => {
      this.computedFontSize().then((fontsize) => {
        if (this.properties.coupon.activityType === 13) {
          this.setData({
            valueStyle: 'font-size: 18px;',
          });
          return;
        }
        this.setData({
          valueStyle: `font-size: ${fontsize};`,
        });
      });
    });
  },

  methods: {
    async computedFontSize() {
      const maxWidth = 88;
      const MAX_FONT_SIZE = 26;
      const MIN_FONT_SIZE = 12;

      const wrapOffsetWidth = await this.getBoundingClientRectWidth(
        '.discount-body__value'
      );
      const prefixWidth = await this.getBoundingClientRectWidth(
        '.discount-body__prefix'
      );
      const unitWidth = await this.getBoundingClientRectWidth(
        '.discount-body__unit'
      );

      const decorateWidth = prefixWidth + unitWidth;
      const scaleRatio = (maxWidth - decorateWidth) / wrapOffsetWidth;

      if (scaleRatio > 1) return MAX_FONT_SIZE + 'px';
      const scaleFontSize = Math.trunc(scaleRatio * MAX_FONT_SIZE);

      return Math.max(scaleFontSize, MIN_FONT_SIZE) + 'px';
    },

    getBoundingClientRectWidth(selector) {
      return new Promise((resolve) => {
        const query = wx.createSelectorQuery().in(this);
        query
          .select(selector)
          .boundingClientRect((res = {}) => {
            resolve(res.width || 0);
          })
          .exec();
      });
    },
  },
});
