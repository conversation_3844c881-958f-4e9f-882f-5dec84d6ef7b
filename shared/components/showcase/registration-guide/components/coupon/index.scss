.registration-guide-coupon-container {
  display: flex;
}

.short .discount-box {
  width: 100%;
  flex-flow: column;
}

.discount-box {
  position: relative;
  text-align: center;
  min-width: 108px;
  padding: 6px 0 8px 0;
  align-items: center;
  display: flex;
  height: 64px;
  box-sizing: border-box;

  .discount-body {
    height: 34px;
    overflow: hidden;
    font-size: 12px;
    width: 100%;

    &__value,
    &__unit,
    &__prefix {
      display: inline;
      line-height: 34px;
    }

    &__value {
      font-size: 26px;
      font-weight: 600;
      white-space: nowrap;
      margin-right: 2px;
    }
  }
}

.content-box {
  position: relative;
  flex: 1;
  padding: 12px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 18px;
    height: 26px;
    border-left: 1px dashed;
    color: inherit;
    transform: scaleY(1.7);
    opacity: 0.2;
  }

  .content-body__content {
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    margin-bottom: 6px;
  }
}

.amount {
  position: absolute;
  top: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.3);
  color: #fff;
  line-height: 16px;
  font-size: 12px;
  padding: 0 4px;
  border-radius: 0 4px 0 4px;
}
