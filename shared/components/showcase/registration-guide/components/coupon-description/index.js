Component({
  properties: {
    description: String,
  },

  data: {
    valueStyle: '',
  },

  ready() {
    setTimeout(() => {
      // 动态计算描述字体大小
      this.computedFontSize().then((fontsize) => {
        this.setData({
          valueStyle: `font-size: ${fontsize};`,
        });
      });
    });
  },

  methods: {
    async computedFontSize() {
      const MAX_FONT_SIZE = 12;
      const MIN_FONT_SIZE = 10;

      const wrapOffsetWidth = await this.getBoundingClientRectWidth(
        '.description'
      );
      const wrapCopyOffsetWidth = await this.getBoundingClientRectWidth(
        '.description__content'
      );

      const scaleRatio = wrapOffsetWidth / wrapCopyOffsetWidth;

      if (scaleRatio > 1) return MAX_FONT_SIZE + 'px';
      const scaleFontSize = Math.trunc(scaleRatio * MAX_FONT_SIZE);

      return Math.max(scaleFontSize, MIN_FONT_SIZE) + 'px';
    },

    getBoundingClientRectWidth(selector) {
      return new Promise((resolve) => {
        const query = wx.createSelectorQuery().in(this);
        query
          .select(selector)
          .boundingClientRect((res = {}) => {
            resolve((res && res.width) || 0);
          })
          .exec();
      });
    },
  },
});
