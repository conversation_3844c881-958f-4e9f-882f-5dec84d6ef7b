const app = getApp();

Component({
  properties: {
    title: String,
    subtitle: Object,
    layoutConfig: Object,
    linkUrl: String,
    cardTheme: String,
    backgroundImage: String,
    couponStyleType: String,
    isOneLine: Boolean,
    coupons: Array,
    benefitList: Array,
    memberCenterUrl: String,
    presentInfo: Object,
    pointsInfo: Object,
  },

  data: {
    maskClass: '',
    showTicketArea: false,
    twoLineCouponStyle: '',
  },

  attached() {
    const { layoutConfig, presentInfo, coupons, pointsInfo } = this.properties;
    this.setData({
      showTicketArea:
        layoutConfig.showBenefitInfo &&
        (presentInfo.title || coupons.length > 0 || pointsInfo.points),
      twoLineCouponStyle: this.getTwoLineCouponStyle(),
    });
  },

  methods: {
    handleScroll(e) {
      if (e.detail.scrollLeft === 0) {
        this.setData({
          maskClass: 'ticket-mask-right',
        });
      } else {
        this.setData({
          maskClass: 'ticket-mask-right ticket-mask-left',
        });
      }
    },
    handleScrollRight() {
      this.setData({
        maskClass: 'ticket-mask-left',
      });
    },
    handleBenefitClick() {
      wx.navigateTo({
        url: this.properties.memberCenterUrl,
      });
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'click_membercard',
          en: '办会员组件点击组件',
        });
    },
    handleClick() {
      this.triggerEvent('clickJoin');
    },
    getTwoLineCouponStyle() {
      const { isOneLine, presentInfo, pointsInfo, coupons } = this.properties;
      let count = 0;
      if (isOneLine) {
        if (presentInfo.title) {
          count++;
        }
        if (pointsInfo.points) {
          count++;
        }
      }
      if (coupons) {
        count += coupons.length;
      }
      return count <= 2 ? 'twoLine-coupon' : '';
    },
  },
});
