<view class="registration-guide">
  <theme-view class="cap-theme-view" gradient="{{ cardTheme === 'main-theme' }}" color="main-text" gradient-deg="{{80}}">
    <view
      class="{{['registration-guide__card', 'halo-background', cardTheme]}}"
      style="{{ backgroundImage }}"
    >
      <!-- 头部标题 & 按钮 -->
      <view class="card-header">
        <view class="title-area">
          <span class="title-main">{{ title }}</span>
          <p wx:if="{{ layoutConfig.showSubtitle }}" class="subtitle">
            {{ subtitle.text
            }}<span wx:if="{{ subtitle.value }}" style="{{ subtitle.valueFontSize }}" class="value">￥{{ subtitle.value }}</span>
          </p>
        </view>
        <a class="join-us" bindtap="handleClick">
          <theme-view wx:if="{{ cardTheme === 'main-theme' }}" color="main-bg">立即入会</theme-view>
          <span wx:else>立即入会</span>
        </a>
      </view>
      <!-- 权益图标列表 -->
      <view
        wx:if="{{ layoutConfig.showIcon && benefitList.length > 0 }}"
        class="benefit-area"
        bindtap="handleBenefitClick"
      >
        <ul class="{{['benefit-list', benefitList.length > 2 ? 'more' : '']}}">
          <li
            class="benefit-item"
            wx:for="{{ benefitList }}"
            wx:key="showName"
          >
            <image class="benefit-img" src="{{ item.icon }}" />
            <span class="benefit-name">{{ item.showName }}</span>
          </li>
        </ul>
      </view>
      <!-- 礼包内容展示 -->
      <view wx:if="{{ showTicketArea }}"
        class="ticket-area"
        bindtap="handleBenefitClick"
      >
        <!-- 左右两侧的渐变蒙层 -->
        <view wx:if="{{ maskClass }}" class="{{['ticket-mask', maskClass]}}"></view>
        <!-- 第一行礼包展示 -->
        <scroll-view
          scroll-x
          enable-flex="true"
          bindscroll="handleScroll"
          bindscrolltolower="handleScrollRight"
          wx:show="{{ coupons.length > 0 || isOneLine }}"
          class="{{['coupon-list', twoLineCouponStyle]}}"
        >
          <view class="list-content">
            <!-- 赠品放在最前面 -->
            <view wx:if="{{ isOneLine && presentInfo.title }}" class="present">
              <image class="present-img" src="{{ presentInfo.url }}" />
              <view class="present-info">
                <view class="name">{{ presentInfo.title }}</view>
                <view class="tips">{{ presentInfo.tips }}</view>
              </view>
            </view>
            <!-- 中间是优惠券 -->
            <coupon
              class="{{['registration-guide-coupon', couponStyleType]}}"
              wx:for="{{ coupons }}"
              wx:key="content"
              coupon="{{ item }}"
              style-type="{{ couponStyleType }}"
            />
            <!-- 最后是积分 -->
            <view wx:if="{{ isOneLine && pointsInfo.points }}" class="point">
              <view class="point-container">
                <view class="count">{{ pointsInfo.points }}</view>
                <view class="tips">送{{ pointsInfo.name }}</view>
              </view>
            </view>
          </view>
        </scroll-view>
        <!-- 第二行礼包展示区 -->
        <view wx:if="{{ !isOneLine && (presentInfo.title || pointsInfo.points) }}" class="second-line">
          <view wx:if="{{ presentInfo.title }}" class="present second-line-item">
            <image class="present-img" src="{{ presentInfo.url }}" />
            <view class="present-info">
              <view class="name">{{ presentInfo.title }}</view>
              <view class="tips">{{ presentInfo.tips }}</view>
            </view>
          </view>
          <view wx:if="{{pointsInfo.points }}" class="point second-line-item">
            <view class="count">{{ pointsInfo.points }}</view>
            <view class="tips">送{{ pointsInfo.name }}</view>
          </view>
        </view>
      </view>
    </view>
  </theme-view>
</view>