.registration-guide {
  padding: 16px;

  .cap-theme-view,
  .registration-guide__card {
    border-radius: 4px;
  }

  .halo-background {
    position: relative;

    &::before {
      content: '';
      top: 0;
      left: -10%;
      position: absolute;
      width: 100%;
      height: 100%;
      background-image: url('https://img01.yzcdn.cn/upload_files/2021/06/18/FpQ2VD0t2xhhTNk-nL5OG4MHra72.png');
      background-size: 110%;
      background-repeat: no-repeat;
      z-index: -1;
      opacity: 0.4;
    }
  }

  // 全店风格
  .main-theme {
    .join-us {
      background-color: #fff;
    }

    .benefit-name,
    .subtitle {
      opacity: 1;
    }

    .ticket-area {
      color: #646566;

      .tips,
      .cap-coupon-description__content {
        color: #969799;
        opacity: 1;
      }
    }
  }

  // 金色主题
  .gloden-theme {
    background-image: linear-gradient(80deg, #f3dfc7, #fedaae);
    color: #74410c;

    .ticket-area {
      color: #b66f0f;
    }

    .join-us {
      color: #fff;
      background-image: linear-gradient(90deg, #feae79, #ff3d3d);
    }
  }

  // 黑色主题
  .black-theme {
    background-image: linear-gradient(80deg, #353841, #2b2b2d);
    color: #f6e2b5;

    .ticket-area {
      color: #886e36;
    }

    .join-us {
      color: #776043;
      background-image: linear-gradient(90deg, #f6e2b5, #e0c19b);
    }
  }

  // 白色主题
  .white-theme {
    background-color: #fff;
    background-image: url('https://img01.yzcdn.cn/upload_files/2021/06/18/Fsi_PbWu6l87HuqMCvactnWrYlEZ.png');
    background-size: 100%;
    background-repeat: no-repeat;
    color: #34333f;

    .ticket-area {
      color: #4e5164;
    }

    .ticket-mask {
      top: 12px;
    }

    .present,
    .point {
      box-shadow: rgba(0, 0, 0, 0.08) 0 0 6px;
    }

    .coupon-list {
      margin: -12px 0 -8px 0;
    }

    .coupon-list.twoLine-coupon {
      width: calc(100% + 32px);
      margin-left: -16px;

      .list-content {
        padding: 0 16px;
      }
    }

    .list-content {
      margin-left: 2px;
      box-shadow: rgba(0, 0, 0, 0.08) 6px 0 6px -6px;

      .present,
      .point {
        margin-top: 12px;
        margin-bottom: 8px;
      }

      .registration-guide-coupon {
        margin-top: 12px;
        margin-bottom: 8px;
        filter: drop-shadow(0 0 6px rgba(0, 0, 0, 0.08));
      }
    }

    .join-us {
      color: #fff;
      background-image: linear-gradient(90deg, #a7a9b9, #75788f);
    }
  }

  // 自定义背景
  .light-theme,
  .dark-theme {
    background-color: #c8c9cc;

    &::before {
      display: none;
    }
  }

  // 深色字体
  .dark-theme {
    color: #323232;

    .join-us {
      color: #fff;
      background: rgba(0, 0, 0, 0.3);
    }

    .ticket-area {
      color: #646566;
    }
  }

  // 浅色字体
  .light-theme {
    color: #fff;

    .join-us {
      color: #fff;
      background: rgba(255, 255, 255, 0.3);
    }

    .ticket-area {
      color: #646566;
    }
  }
}

.registration-guide__card {
  position: relative;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
  padding: 16px;

  .benefit-name,
  .subtitle,
  .tips {
    opacity: 0.8;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .title-area {
    display: flex;
    flex-direction: column;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;

    .value {
      display: inline-block;
      font-size: 16px;
      line-height: 18px;
      font-weight: bold;
    }

    .title-main {
      display: block;
      line-height: 22px;
      font-size: 16px;
      font-weight: bold;
    }

    .subtitle {
      display: block;
      line-height: 18px;
      font-size: 12px;
      margin-top: 2px;
    }
  }

  .join-us {
    display: block;
    width: 56px;
    min-width: 56px;
    height: 20px;
    font-size: 14px;
    line-height: 20px;
    padding: 2px 10px;
    border-radius: 12px;
    font-weight: 600;
    white-space: nowrap;
  }

  .benefit-area {
    margin-top: 12px;

    .benefit-list {
      display: flex;
    }

    .benefit-item {
      flex: 1;

      .benefit-name {
        display: inline-block;
        font-size: 12px;
        line-height: 32px;
        margin-left: 8px;
      }

      .benefit-img {
        border-radius: 50%;
        width: 32px;
        height: 32px;
        background-color: #fff;
        vertical-align: middle;
      }
    }

    .more {
      .benefit-item {
        text-align: center;
      }

      .benefit-name {
        display: block;
        line-height: 18px;
        margin-top: 4px;
        margin-left: 0;
      }
    }
  }

  .ticket-area {
    margin-top: 12px;

    .ticket-mask {
      position: relative;
      width: 100%;

      &::after {
        left: 0;
        background-image: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0));
      }

      &::before {
        right: 0;
        background-image: linear-gradient(90deg, rgba(255, 255, 255, 0), #fff);
      }

      &::after,
      &::before {
        position: absolute;
        top: 0;
        content: '';
        width: 32px;
        height: 64px;
        z-index: 10;
        opacity: 0;
        transition: opacity 0.5s;
      }
    }

    .ticket-mask-right {
      &::before {
        opacity: 1;
      }
    }

    .ticket-mask-left {
      &::after {
        opacity: 1;
      }
    }

    .coupon-list {
      width: 100%;

      .list-content {
        display: flex;
      }

      .present {
        min-width: 196px;
        margin-right: 8px;
      }

      .point {
        min-width: 108px;
      }
    }

    .second-line {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;

      &-item:nth-child(2) {
        margin-left: 8px;
      }

      .point {
        padding: 6px 12px 8px 12px;
      }
    }

    .present,
    .point {
      border-radius: 4px;
      flex: 1;
      background-color: #fff;
      vertical-align: middle;

      .name,
      .tips {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .tips {
        line-height: 16px;
        font-size: 12px;
      }
    }

    .present {
      padding: 12px;
      overflow: hidden;
      display: flex;

      &-img {
        width: 40px;
        min-width: 40px;
        height: 40px;
        border-radius: 4px;
        margin-right: 8px;
      }

      .present-info {
        overflow: hidden;

        .name {
          width: 100%;
          font-weight: 600;
          line-height: 20px;
          font-size: 14px;
          margin-bottom: 4px;
        }
      }
    }

    .point {
      text-align: center;
      padding: 6px 0 8px 0;

      .count {
        line-height: 34px;
        font-size: 26px;
        font-weight: 600;
      }
    }
  }
}

.registration-guide-coupon {
  width: 100%;
  flex: 1;
  border-radius: 4px;
  margin-right: 8px;
  background: radial-gradient(circle at right, transparent 6px, #fff 0) center
      right / 51% 100% no-repeat,
    radial-gradient(circle at left, transparent 6px, #fff 0) center left / 51%
      100% no-repeat;

  &:last-child {
    margin-right: 0;
  }
}

.registration-guide-coupon.short {
  width: inherit;
  min-width: 108px;
}
