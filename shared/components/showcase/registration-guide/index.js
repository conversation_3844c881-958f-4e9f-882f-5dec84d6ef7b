import componentBehavior from '../behaviors/component-behavior';
import * as API from './api';
import {
  themeList,
  fontStyleTheme,
  LevelType,
  LevelMode,
  LevelUpgradeConditionType,
  PresentStatus,
} from './constants';
import formatMoney from '@youzan/utils/money/format';
import openWebView from 'utils/open-web-view';

const app = getApp();

Component({
  type: 'registration_guide',

  behaviors: [componentBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
      observer: 'init',
    },
  },

  data: {
    show: false,
    // 标题
    title: '',
    // 副标题
    subtitle: {},
    layoutConfig: {
      // 是否展示副标题
      showSubtitle: true,
      // 是否展示权益Icon
      showIcon: true,
      // 是否展示礼包信息
      showBenefitInfo: true,
    },
    // 卡片主题色
    cardTheme: 'gloden-theme',
    // 背景图片样式
    backgroundImage: '',
    // 优惠券风格
    couponStyleType: '',
    // 是否一行展示
    isOneLine: false,
    // 个人中心连接
    memberCenterUrl: '',
    // 权益图标列表
    benefitList: [],
    // 赠品信息
    presentInfo: {},
    // 积分信息
    pointsInfo: {},
    // 优惠券列表
    coupons: [],

    // 免费等级组
    freeLevelList: [],
    // 付费等级组
    payLevelList: [],
    // 用户个人信息
    userLevelInfo: {},
    // 当前展示的等级权益
    nowLevelBenefit: {},
    // 场景营销权益
    sceneMarketBenefit: [],
    benefitCount: 0,
  },

  attached() {
    Promise.all([this.getLevelListByLevelType(), this.getUserLevelInfo()])
      .then((res) => {
        return res[0] && res[1] && this.getLevelBenefitWorth();
      })
      .then((res) => {
        if (!res) return false;
        this.setBenefitList();
        this.setData({
          title: this.properties.componentData.title,
          isOneLine: this.properties.componentData.arrangementStyle === '0',
          cardTheme: this.getCardTheme(),
          backgroundImage: this.getBackgroundImage(),
          memberCenterUrl: this.getMemberCenterUrl(),
          layoutConfig: this.getLayoutConfig(),
        });
        return true;
      })
      .then((show) => {
        this.setData({
          couponStyleType: this.getCouponStyleType(),
          show,
        });
        if (show) {
          app.logger &&
            app.logger.log({
              et: 'view',
              ei: 'view_membercard',
              en: '办会员组件曝光',
            });
        }
      });
  },

  methods: {
    // 获取免费等级组数据，返回是否开启
    getFreeLevelList() {
      return API.getLevelListNoUid({ type: LevelType.FREE }).then((res) => {
        if (res.levelList.length === 0) return false;

        const { levelV2List } = res.levelList[0];
        this.setData({
          freeLevelList: levelV2List,
        });
        return res.isLevelGroupEnabled;
      });
    },
    // 获取付费等级组数据，返回是否开启
    getPayLevelList() {
      return API.getLevelListNoUid({ type: LevelType.PAY }).then((res) => {
        if (res.levelList.length === 0) return false;

        const { levelV2List } = res.levelList[0];
        this.setData({
          payLevelList: levelV2List,
        });
        return res.isLevelGroupEnabled;
      });
    },
    getLevelListByLevelType() {
      const { levelType } = this.properties.componentData;
      if (levelType === '0') {
        return this.getFreeLevelList();
      }
      return this.getPayLevelList();
    },
    // 获取用户等级数据 并判断是否曝光组件
    getUserLevelInfo() {
      if (this.properties.componentData.levelType === '0') {
        return API.getUserLevelInfo({ type: LevelType.FREE }).then((res) => {
          this.setData({
            userLevelInfo: res,
          });
          return !res.level || res.level.levelValue === 0;
        });
      }
      return API.getUserLevelInfo({ type: LevelType.PAY }).then((res) => {
        this.setData({
          userLevelInfo: res,
        });
        // b端配置的指定等级是数组下标，这里需要+1来计算
        return (
          !res.level ||
          res.level.levelValue < this.properties.componentData.level + 1
        );
      });
    },
    // 判断组件当前需要展示哪个等级的权益信息
    getShowLevelInfo() {
      const { levelType, level } = this.properties.componentData;

      if (levelType === '0') {
        const freelevel = this.findFreeLevel() || this.data.freeLevelList[0];
        return {
          isEnabled: freelevel?.isEnabled,
          nowLevelBenefit: freelevel?.levelBenefit,
          levelId: freelevel?.levelId,
        };
      }
      return {
        isEnabled: this.data.payLevelList[level]?.isDisplay,
        nowLevelBenefit: this.data.payLevelList[level]?.levelBenefit,
        levelId: this.data.payLevelList[level]?.levelId,
      };
    },
    findFreeLevel() {
      const { userLevelInfo, freeLevelList } = this.data;
      const {
        customerConsumeInfo = {
          currentTotalTradeAmount: 0,
          currentTotalTradeCount: 0,
        },
      } = userLevelInfo;

      if (userLevelInfo.mode === LevelMode.GROWTH) {
        // 免费等级组倒排，第一个小于或等于用户成长值的等级即为用户当前可达到的等级。
        return [...freeLevelList].reverse().find((levelInfo) => {
          const condition = levelInfo.levelGrantConditionList.find(
            (condition) =>
              condition.conditionType === LevelUpgradeConditionType.GROWTH
          );
          return condition.minGrowth <= userLevelInfo.growth;
        });
      }
      if (userLevelInfo.mode === LevelMode.CONSUME) {
        return [...freeLevelList].reverse().find((levelInfo) => {
          const condition = levelInfo.levelGrantConditionList.find(
            (condition) =>
              condition.conditionType === LevelUpgradeConditionType.CONSUME
          );
          const { behaviorCondition } = condition;
          return (
            behaviorCondition.minTotalTradeAmount <=
              customerConsumeInfo.currentTotalTradeAmount ||
            behaviorCondition.minTotalTradeCount <=
              customerConsumeInfo.currentTotalTradeCount ||
            behaviorCondition.minTradeAmount <=
              customerConsumeInfo.currentMaxTradeAmount
          );
        });
      }
    },
    // 获取当前等级的权益价值、优惠券列表、赠品信息
    getLevelBenefitWorth() {
      const { isEnabled, nowLevelBenefit, levelId } = this.getShowLevelInfo();
      if (!isEnabled) return null;

      this.setData({
        nowLevelBenefit,
      });

      return API.getLevelBenefitWorth({ levelId }).then((res) => {
        this.setData({
          subtitle: this.getSubtitle(res.totalPrice),
          coupons: this.getCouponList(res.couponAdapterList),
          presentInfo: this.presentFilter(res.presentInfo),
          pointsInfo: res.pointsInfo,
          sceneMarketBenefit: res.sceneMarketBenefit,
        });
        return true;
      });
    },
    presentFilter(presentInfo) {
      const { hideEmptyCoupon } = this.properties.componentData;

      if (
        hideEmptyCoupon === '0' &&
        (presentInfo.status === PresentStatus.OVERDUED ||
          presentInfo.status === PresentStatus.DELETED)
      ) {
        return {};
      }
      return presentInfo;
    },
    getSubtitle(totalPrice = 0) {
      const { descriptionType, descriptionContent, levelType, level } =
        this.properties.componentData;
      const value = (+formatMoney(totalPrice, true, false)).toFixed(1);

      switch (descriptionType) {
        case '0': {
          if (levelType === '0') {
            const valueFontSize = `font-size: ${this.calculateFontSize(
              value.length + 1
            )}px`;

            return +value > 0
              ? {
                  text: '免费立即入会，一年预计省',
                  value,
                  valueFontSize,
                }
              : {
                  text: '免费立即入会，享会员专属权益',
                };
          }
          // 付费会员取最低价格
          let minPrice =
            this.data.payLevelList[level].levelGoods.skuList[0].price;
          this.data.payLevelList[level].levelGoods.skuList.forEach((item) => {
            if (item.price < minPrice && item.goodsSkuType === 1)
              minPrice = item.price;
          });
          if (totalPrice <= minPrice) {
            if (this.data.benefitCount > 1) {
              return {
                text: `开通会员，享会员${this.data.benefitCount}大专属权益`
              };
            }
            return {
              text: '开通会员，享会员专属权益',
            };
          }
          const minPriceString = formatMoney(minPrice, true, false);
          // 这里减1是因为其他文本比较短
          const valueFontSize = `font-size: ${this.calculateFontSize(
            minPriceString.length + value.length - 1
          )}px`;

          return {
            text: `￥${minPriceString}开通会员，一年预计省`,
            value,
            valueFontSize,
          };
        }
        case '1': {
          return {
            text: descriptionContent,
          };
        }
        case '2':
        default:
          return null;
      }
    },
    getCardTheme() {
      const { backgroundType, backgroundTheme, fontStyle } =
        this.properties.componentData;

      switch (backgroundType) {
        case '0':
          return themeList[backgroundTheme];
        case '1':
          return 'main-theme';
        case '2':
          return fontStyleTheme[fontStyle];
        default:
          return null;
      }
    },
    getBackgroundImage() {
      const { backgroundImage, backgroundType } = this.properties.componentData;
      if (!backgroundImage || backgroundType !== '2') return null;
      return `
        background-image: url(${backgroundImage});
        background-size: cover;
        background-position: 50% 0;
      `;
    },
    getMemberCenterUrl() {
      const { levelType, payLevelAlias } = this.properties.componentData;
      const url =
        levelType === '0'
          ? '/packages/levelcenter/free/index'
          : `/packages/levelcenter/plus/index?alias=${payLevelAlias}`;

      return url;
    },
    setBenefitList() {
      let list = [];
      const { nowLevelBenefit, sceneMarketBenefit } = this.data;

      list.push(...sceneMarketBenefit);

      for (var item in nowLevelBenefit) {
        if (item) {
          if (item === 'diyTemplateList') {
            nowLevelBenefit[item].forEach((item) => {
              if (list.length < 3) {
                list.push(item.benefitPluginInfo);
              }
            });
          } else {
            list.push(nowLevelBenefit[item].benefitPluginInfo);
          }
        }
      }

      this.setData({
        benefitCount: list.length
      });

      if (list.length > 3) {
        list = list.slice(0, 3);
        list.push({
          icon:
            '//b.yzcdn.cn/public_files/a1fb1079aa056c48631a190bc5247dd9.png',
          showName: '更多权益'
        });
      }

      this.setData({
        benefitList: list,
      });
    },
    getCouponStyleType() {
      const { coupons, isOneLine, presentInfo, pointsInfo } = this.data;
      if (coupons.length >= 2) return 'short';
      if (isOneLine && (presentInfo.title || pointsInfo.points)) return 'short';
      return '';
    },
    getLayoutConfig() {
      const { descriptionType, showIcon, showBenefitInfo } =
        this.properties.componentData;

      return {
        // 是否展示副标题
        showSubtitle: descriptionType !== '2',
        // 是否展示权益Icon
        showIcon: showIcon === '1',
        // 是否展示礼包信息
        showBenefitInfo: showBenefitInfo === '1',
      };
    },
    // 隐藏失效的优惠券
    getCouponList(coupons) {
      const { hideEmptyCoupon } = this.properties.componentData;
      return hideEmptyCoupon === '0'
        ? coupons
        : coupons.filter((item) => item.status === 0);
    },
    calculateFontSize(length) {
      if (length <= 10 && length >= 8) {
        return -2 * length + 32;
      }
      if (length > 10) {
        return 12;
      }
      return 16;
    },
    handleClick() {
      const { freeLevelGroupAlias, payLevelAlias, levelType } =
        this.properties.componentData;
      if (levelType === '0') {
        openWebView('/wscuser/levelcenter/fill', {
          query: {
            kdt_id: app.getKdtId(),
            alias: freeLevelGroupAlias,
            eT: Date.now(),
          },
        });
      } else {
        wx.navigateTo({
          url: `/packages/levelcenter/pay/index?alias=${payLevelAlias}`,
        });
      }
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'click_membercardbutton',
          en: '办会员组件点击按钮',
        });
    },
  },
});
