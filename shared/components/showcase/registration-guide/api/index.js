import { node as request } from 'shared/utils/request';

// 获取用户免费等级信息
export function getUserLevelInfo(data) {
  return request({
    method: 'GET',
    path: '/wscuser/levelcenter/api/userLevelDetail.json',
    data: {
      ...data,
      withSyncInfo: 1,
      withConsumerData: '1',
    },
  });
}

// 获取免费 or 付费等级组信息
export function getLevelListNoUid({ type }) {
  return request({
    method: 'GET',
    path: '/wscuser/levelcenter/api/getLevelListNoUid.json',
    data: {
      type,
    },
  });
}

// 获取当前等级的权益价值
export function getLevelBenefitWorth({ levelId }) {
  return request({
    method: 'GET',
    path: '/wscuser/levelbenefit/api/getLevelBenefitWorth.json',
    data: {
      levelId,
    },
  });
}
