import { node as request } from 'shared/utils/request';

function countPlay(url) {
  return request({
    path: url
  });
}

function getVideoComments(data) {
  const result = request({
    path: '/wscump/feature-videos/videoComments.json',
    origin: 'h5',
    data
  });
  return result;
}

function likeVideoComment(data) {
  return request({
    path: '/wscump/feature-videos/likeComment.json',
    data,
    method: 'GET'
  });
}

function cancelLikeVideoComment(data) {
  return request({
    path: '/wscump/feature-videos/cancelLikeComment.json',
    data,
    method: 'GET'
  });
}

function getVideoPlayInfo({ videoAdId, hqKdtId }) {
  return request({
    path: '/wscump/feature-videos/playInfo.json',
    origin: 'h5',
    data: {
      videoAdId,
      hqKdtId
    }
  });
}

function getVideoDetailInfo({ videoId, hqKdtId }) {
  return request({
    path: '/wscump/feature-videos/videoDetail.json',
    origin: 'h5',
    data: {
      videoId,
      hqKdtId
    }
  });
}

function addVideoComment(data) {
  return request({
    path: '/wscump/feature-videos/addComment.json',
    method: 'POST',
    data
  });
}

export {
  getVideoComments,
  likeVideoComment,
  cancelLikeVideoComment,
  addVideoComment,
  getVideoPlayInfo,
  getVideoDetailInfo,
  countPlay
};
