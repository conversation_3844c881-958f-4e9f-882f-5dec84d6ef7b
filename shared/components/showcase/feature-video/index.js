// 日食记升级连锁3.0之后，关于专题视频能力收口总店，所以评论、视频获取都用总店kdtId
import get from '@youzan/weapp-utils/lib/get';
import { getMonthDayfix2Str, format } from '@youzan/weapp-utils/lib/time-utils';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import money from '@youzan/weapp-utils/lib/money';
import componentBehavior from '../behaviors/component-behavior';
import {
  getVideoComments,
  getVideoPlayInfo,
  getVideoDetailInfo,
  likeVideoComment,
  cancelLikeVideoComment,
  addVideoComment
} from './api';

const app = getApp();

Component({
  behaviors: [componentBehavior],

  data: {
    paid: false, // 播放付费标识
    src: '',
    countUrl: '',
    loadingComments: false,
    loadingCommentsFinished: false,
    videoId: '',
    page: 1,
    comments: []
  },

  attached() {
    const { componentData } = this.data;
    this.initVideo(componentData);
  },

  methods: {
    initVideo(itemData) {
      const { video = {} } = itemData;
      const { id: videoId = 0 } = video;

      this.getVideoDetailInfo(videoId).then((videoDetail = {}) => {
        this.setData(
          {
            video: videoDetail
          },
          () => {
            const videoAdId = get(videoDetail, 'video.videoId', 0);
            getVideoPlayInfo({ videoAdId, hqKdtId: this.data.hqKdtId }).then(playInfo => {
              this.setData({
                videoUrl: playInfo.videoUrl,
                countUrl: playInfo.countPlayedUrl
              });
            });
            this.reloadComments(video.id);
          }
        );
      });
    },
    handleTapHeart(e) {
      const { comment, index } = e.detail.detail;
      comment.favorited ? this.cancelLikeComment(index) : this.likeComment(index);
    },
    handleAddComment(e) {
      const { detail: comment } = e.detail;
      if (!comment) {
        return;
      }
      const { video, hqKdtId } = this.data;
      this.setData({
        loadingComments: true
      });
      addVideoComment({
        comment,
        videoAdId: video.id,
        hqKdtId
      })
        .then(() => {
          this.reloadComments();
        })
        .catch(() => {
          this.setData({
            loadingComments: false
          });
        });
    },
    likeComment(index) {
      const { comments, video, kdtId } = this.data;
      const comment = comments[index];
      this.setData({
        loadingComments: true
      });
      likeVideoComment({
        videoAdId: video.id,
        commentId: comment.id,
        kdtId
      })
        .then(res => {
          if (res && res.value) {
            this.setData({
              [`comments[${index}].favoriteCount`]: comment.favoriteCount + 1,
              [`comments[${index}].favorited`]: !comment.favorited,
              loadingComments: false
            });
          } else {
            this.setData({
              loadComments: false
            });
          }
        })
        .catch(() => {
          this.setData({
            loadingComments: false
          });
        });
    },
    cancelLikeComment(index) {
      const { comments, video, kdtId } = this.data;
      const comment = comments[index];
      this.setData({
        loadingComments: true
      });
      cancelLikeVideoComment({
        videoAdId: video.id,
        commentId: comment.id,
        kdtId
      })
        .then(res => {
          if (res && res.value) {
            this.setData({
              [`comments[${index}].favoriteCount`]: comment.favoriteCount - 1,
              [`comments[${index}].favorited`]: !comment.favorited,
              loadingComments: false
            });
          } else {
            this.setData({
              loadComments: false
            });
          }
        })
        .catch(() => {
          this.setData({
            loadingComments: false
          });
        });
    },
    handlePlay() {
      if (!this.data.paid) {
        this.data.paid = true;
        this.countPlay();
      }
    },
    getVideoDetailInfo(videoId) {
      return getVideoDetailInfo({
        videoId,
        kdtId: this.data.kdtId,
        hqKdtId: this.data.hqKdtId
      }).then(video => {
        if (video) {
          video.createdAtStr = getMonthDayfix2Str(new Date(video.createdAt));
          if (video.itemList) {
            video.itemList.forEach(item => {
              item.salePrice = money(item.salePrice).toYuan();
              item.imageUrl = cdnImage(item.imageUrl, '!160x160.jpg');
            });
          }
          video.displayCoverUrl = video.picType === 1 ? video.video.coverUrl : video.picUrl;
        }
        return video;
      });
    },
    getComments(videoAdId, page, pageSize = 15) {
      return getVideoComments({
        videoAdId,
        page,
        pageSize,
        hqKdtId: this.data.hqKdtId
      }).then(res => {
        if (!res) {
          return null;
        }
        const newComments = res.list;
        newComments.forEach(comment => {
          comment.createdAtStr = format(new Date(comment.createdAt), 'yyyy年MM月dd日 hh:mm:ss');
          if (comment.commentList) {
            comment.commentList.forEach(reply => {
              reply.createdAtStr = format(new Date(reply.createdAt), 'yyyy年MM月dd日 hh:mm:ss');
            });
          }
        });
        return {
          totalItems: res.totalItems,
          newComments
        };
      });
    },
    loadComments() {
      const { video, page, comments } = this.data;
      this.setData({
        loadingComments: true
      });
      this.getComments(video.id, page)
        .then(data => {
          const { newComments, totalItems } = data;
          comments.push(...newComments);
          this.setData({
            page: page + 1,
            comments,
            loadingComments: false,
            loadingCommentsFinished: comments.length === totalItems
          });
        })
        .then(() => {
          this.setData({
            loadingComments: false
          });
        });
    },
    reloadComments() {
      const { video } = this.data;
      const page = 1;
      this.setData({
        loadingComments: true,
        loadingCommentsFinished: false
      });
      this.getComments(video.id, page)
        .then(data => {
          const { newComments, totalItems } = data;
          this.setData({
            page: page + 1,
            comments: newComments,
            loadingComments: false,
            loadingCommentsFinished: newComments.length === totalItems
          });
        })
        .catch(() => {
          this.setData({
            loadComments: false
          });
        });
    },
    countPlay() {
      setTimeout(() => {
        app.logger && app.logger.log({
          et: 'click',
          ei: 'video_valid_played',
          en: '开始播放',
          params: {
            channel: 'wsc_advertise_video',
            partner_biz_type: 1,
            partner_biz_id: app.getHQKdtId(),
            video_id: this.data.video.id
          }
        });
      }, 3000);
    }
  }
});
