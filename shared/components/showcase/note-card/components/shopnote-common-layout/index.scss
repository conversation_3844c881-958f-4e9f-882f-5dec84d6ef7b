@import '../common/style.scss';

.wrapper-0 {
  .wsc-img-list__img {
    height: 193px !important;
  }

  .shopnote-common-layout__summary-title {
    max-height: 44px;
    height: unset !important;
  }
}

.wrapper-1 {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 10px;

  .wsc-img-list__img {
    height: 177px !important;
  }

  .wsc-img-list__label {
    left: 10px;
    bottom: 5px;
  }

  .cap-shopnote-layout__wrapper {
    margin-left: 5px;
    margin-right: 5px;
    flex: 1;
    max-width: calc(50% - 10px);
    margin-bottom: 10px;
    min-width: calc(50% - 10px);

    .shopnote-common-layout__summary {
      padding: 10px;
    }
  }
}

.wrapper-2 {
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  flex-wrap: nowrap;
  margin-top: 10px;
  padding-bottom: 10px;

  .wsc-img-list__img {
    height: 150px !important;
  }

  .cap-shopnote-layout__wrapper {
    margin-right: 10px;
    max-width: 150px;
    min-width: 150px;

    .shopnote-common-layout__summary {
      padding: 10px;

      &-title {
        height: 44px;
      }
    }
  }

  .cap-shopnote-layout__wrapper:last-child {
    margin-right: 15px;
  }
}

.wrapper-3 {
  .wsc-img-list__img {
    height: 150px !important;
  }
}

.shopnote-common-layout {
  overflow: hidden;
  background: #fff;
  margin-bottom: 10px;

  &__thumb {
    margin-top: 8px;
  }
}

.shopnote-common-layout__container {
  .wsc-img-list {
    position: relative;
    margin-top: 0;

    &__cover {
      position: absolute;
      bottom: 2px;
      left: 0;
      padding: 10px;
      width: 100%;
      box-sizing: border-box;
      background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.5));

      &-top {
        overflow: hidden;
        margin-bottom: 5px;

        .wsc-img-list__label {
          position: static;
          float: left;
        }

        &-text {
          color: #d60000;
        }

        .shopnote-thumb-view {
          float: right;
        }

        &-span {
          float: left;
          color: #d60000;
        }
      }

      &-bottom {
        line-height: 24px;

        &-title {
          color: #fff;
          @include multi-ellipsis(2);
        }
      }
    }

    &__img {
      width: 100%;
      height: auto;
      max-width: 100%;
    }

    &__label {
      position: absolute;
      bottom: 10px;
      left: 10px;
      font-size: 10px;
      color: #141415;
      line-height: 18px;
      background: #fff;
      opacity: 0.85;
      border-radius: 2px;
      padding: 0 5px;

      &-span {
        color: #d60000;
        display: inline-block;
        margin-right: 2px;
      }
    }
  }
  .shopnote-common-layout__summary {
    padding: 10px;

    &-title {
      height: 44px;
      line-height: 22px;
      color: #323233;

      @include multi-ellipsis(2);
    }
  }
}

.wrapper-5 {
  overflow: hidden;

  .wsc-img-list__label {
    position: static;
    margin-bottom: 4px;
    padding-left: 0;
    padding-right: 0;
    font-size: 12px;
  }

  .wsc-img-list__cover-top-span {
    margin-right: 2px;
  }

  .shopnote-common-layout__summary-title {
    font-size: 13px;
    line-height: 17px;
    height: auto;
  }

  .card-0 {
    margin-right: 2px;
    margin-left: 2px;
  }

  .card-3 {
    .shopnote-common-layout__summary {
      background: transparent;
      padding: 0;
    }

    .wsc-img-list__label {
      background: transparent;
    }
  }

  .card-3.shopnote-common-layout {
    background: transparent;
  }

  .shopnote-common-layout__summary {
    padding: 8px 12px 12px 12px;
  }
}
