import WscComponent from 'shared/common/base/wsc-component/index';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import navigate from '@/helpers/navigate';
import get from '@youzan/weapp-utils/lib/get';

WscComponent({
  properties: {
    itemData: {
      type: Object,
      value: {},
    },
    infoData: {
      type: Object,
      value: {},
    },
    list: {
      type: Array,
      value: [],
    },
    isSwipeLayout: {
      type: Boolean,
      value: false,
    },
    needLoadMore: {
      type: Boolean,
      value: false,
    },
  },

  addGlobalClass: true,

  methods: {
    fullImage(url) {
      return cdnImage(url);
    },
    linkToMore() {
      navigate.switchTab({
        url: '/packages/shop/shopnote/list/index',
      });
    },
    noteClick(event) {
      const noteAlias = get(event, 'currentTarget.dataset.item.noteAlias', '');
      const noteType = get(event, 'currentTarget.dataset.item.noteType', '');

      navigate.navigate({
        url:
          noteType === 'mp_article'
            ? `/packages/shop/shopnote/mparticle/detail/index?noteAlias=${noteAlias}`
            : `/packages/shop/shopnote/detail/index?noteAlias=${noteAlias}`,
      });
    },
  },
});
