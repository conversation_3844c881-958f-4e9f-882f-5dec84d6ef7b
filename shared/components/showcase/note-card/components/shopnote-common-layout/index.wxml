<view
  class="cap-showcase-wrapper__content wrapper-{{ infoData.listType }}"
>
  <view
    wx:for="{{ list }}"
    wx:for-item="itemData"
    key="{{ index }}"
    class="cap-shopnote-layout__wrapper"
    data-item="{{ itemData }}"
    bindtap="noteClick"
  >
    <view class="shopnote-common-layout card-{{ infoData.noteStyle }} border-{{ infoData.noteCornerStyle}}">
      <view class="shopnote-common-layout__container">
        <view class="wsc-img-list">
          <image class="wsc-img-list__img" src="{{ itemData.imgList[0] }}" mode="{{ infoData.listType === 5 ? 'widthFix' :'aspectFill'}}" />
          <view wx:if="{{ infoData.showNoteTypeTag && infoData.titlePosition===1 }}" class="wsc-img-list__label">
            <text class="wsc-img-list__label-span">#</text>
            <text>{{ itemData.label }}</text>
          </view>
          <view wx:if="{{ infoData.titlePosition === 0 }}" class="wsc-img-list__cover">
            <view class="wsc-img-list__cover-top">
              <view wx:if="{{ infoData.showNoteTypeTag }}" class="wsc-img-list__label">
                <text class="wsc-img-list__cover-top-span">#</text>
                <text>{{ itemData.label }}</text>
              </view>
              <view style="float:right">
                <shopnote-thumb-view
                  itemData="{{ itemData }}"
                  infoData="{{ infoData }}"
                  theme="white"
                  layout="right"
                  bind:thumbClick="thumbClick"
                />
              </view>
            </view>
            <view class="wsc-img-list__cover-bottom">
              <view class="wsc-img-list__cover-bottom-title font-{{infoData.textStyle}}">
                {{ itemData.title || itemData.description }}
              </view>
            </view>
          </view>
        </view>
        <view
          wx:if="{{ infoData.titlePosition === 1 || infoData.titlePosition === 3}}"
          class="shopnote-common-layout__summary"
        >
          <view wx:if="{{ infoData.showNoteTypeTag && infoData.titlePosition ===3 }}" class="wsc-img-list__label">
            <text class="wsc-img-list__label-span">#</text>
            <text style="color:#646566">{{ itemData.label }}</text>
          </view>
          <view class="shopnote-common-layout__summary-title font-{{ infoData.textStyle }}">
            {{ itemData.title || itemData.description }}
          </view>
          <shopnote-thumb-view
            itemData="{{ itemData }}"
            infoData="{{ infoData }}"
            wrapper-class="shopnote-common-layout__thumb"
            bind:thumbClick="thumbClick"
          />
        </view>
      </view>
    </view>
  </view>
  <shopnote-morebutton wx:if="{{ isSwipeLayout && needLoadMore }}" />
</view>
