<view>
  <view
    wx:for="{{ list }}"
    wx:for-item="itemData"
    class="shopnote-detail-list card-{{ infoData.noteStyle }} corner-{{ infoData.noteStyle }} border-{{ infoData.noteCornerStyle}}"
    bindtap="noteClick"
    data-item="{{ itemData }}"
    :key="{{ index }}"
  >
    <view class="shopnote-detail-list__wrapper">
      <view class="shopnote-detail-list__wrapper-left">
        <view class="shopnote-detail-list__wrapper-left-title font-{{ infoData.textStyle }}">
          {{ itemData.title || itemData.description }}
        </view>
        <view wx:if="{{ itemData.title }}" class="shopnote-detail-list__wrapper-left-title-desc">
          {{ itemData.description }}
        </view>
        <view style="position:absolute;bottom:0;">
          <shopnote-thumb-view
            itemData="{{ itemData }}"
            infoData="{{ infoData }}"
            layout="left"
            wrapper-class="shopnote-thumb__wrapper"
            bindthumbClick="{{ thumbClick }}"
          />
        </view>
      </view>
      <view class="shopnote-detail-list__wrapper-right">
        <view class="wsc-img-list">
          <image class="wsc-img-list__img" mode="aspectFit" src="{{ itemData.imgList[0] }}">
          <view class="wsc-img-list__cover">
            <view class="wsc-img-list__cover-top">
              <view wx:if="{{ infoData.showNoteTypeTag }}" class="wsc-img-list__label">
                <text class="wsc-img-list__cover-top-span">#</text>
                <text>{{ itemData.label }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>