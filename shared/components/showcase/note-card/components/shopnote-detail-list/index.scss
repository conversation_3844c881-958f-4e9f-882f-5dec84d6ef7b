@import '../common/style.scss';

.shopnote-detail-list {
  margin-top: 10px;
  padding: 10px;
  background: #fff;
  overflow: hidden;

  &__wrapper {
    display: flex;

    &-left {
      flex: 1;
      position: relative;

      &-title {
        @include multi-ellipsis(2);

        margin-right: 20px;

        &-desc {
          color: #969799;
          font-size: 12px;
          line-height: 16px;
          margin-right: 20px;
          margin-top: 15px;
          @include multi-ellipsis(2);
        }
      }

      .shopnote-thumb-view {
        width: 130px;
      }

      .shopnote-thumb-gray {
        position: absolute;
        bottom: 0;
      }
    }

    &-right {
      .wsc-img-list {
        position: relative;
        margin-top: 0;

        &__img {
          width: 120px;
          height: 120px;
          border-radius: 2px;
        }

        &__label {
          width: auto;
          position: absolute;
          bottom: 6px;
          left: 3px;
          font-size: 10px;
          color: #141415;
          line-height: 14px;
          background: #fff;
          opacity: 0.8;
          border-radius: 2px;
          text-align: center;
          padding: 0 5px;
        }

        .wsc-img-list__cover-top-span {
          color: #d60000;
          display: inline-block;
        }
      }
    }
  }
}

.shopnote-thumb__wrapper {
  width: 130px;
}
