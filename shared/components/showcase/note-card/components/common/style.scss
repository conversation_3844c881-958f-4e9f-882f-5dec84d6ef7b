
.card-0 {
  box-shadow: 0 2px 8px rgba(93, 113, 127, .08);
}

.card-1 {
  position: relative;
}

.card-2 {
  position: relative;
}

.border-0.card-3 {
  .wsc-img-list__img {
    border-radius: 8px;
  }
}

.card-2::after {
  content: "";
  transform: scale(.5);
  position: absolute;
  border: 1px solid #e5e5e5;
  top: -50%;
  right: -50%;
  bottom: -50%;
  left: -50%;
}

.border-0 {
  border-radius: 8px;  
}

.corner-0 {
  border-radius: 8px;
}

.font-1 {
  font-weight: bold;
}

// good
@mixin multi-ellipsis ($lines) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}