@import '../common/style.scss';

.shopnote-list-hybrid {
  margin-bottom: 10px;
  overflow: hidden;

  .shopnote-list-hybrid-content {
    .shopnote-common-layout__container {
      .wsc-img-list {
        height: 147px;
      }
    }

    .wsc-img-list__img {
      width: 100%;
      height: 147px !important;
    }
  }

  .wsc-img-list {
    position: relative;
    margin-top: 0;
    background-size: cover;

    &__cover {
      position: absolute;
      bottom: 0;
      left: 0;
      padding: 10px;
      width: 100%;
      box-sizing: border-box;
      background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));

      &-top {
        overflow: hidden;
        margin-bottom: 5px;

        .wsc-img-list__label {
          position: static;
          float: left;
        }

        &-span {
          color: #d60000;
        }

        .shopnote-thumb-view {
          float: right;
        }
      }

      &-bottom {
        &-title {
          color: #fff;
          @include multi-ellipsis(2);
        }
      }
    }

    &__label {
      font-size: 10px;
      color: #141415;
      line-height: 14px;
      background: #fff;
      opacity: 0.8;
      border-radius: 2px;
      width: 56px;
      text-align: center;
      margin-bottom: 4px;
    }

    &__cover-center-span {
      color: #d60000;
      display: inline-block;
      margin-right: 2px;
    }
  }

  .shopnote-list-hybrid-wrapper {
    background: #fff;
    overflow: hidden;
  }

  .shopnote-hybrid-small {
    display: flex;
    padding: 8px 12px;

    &__left {
      flex: 1;
      position: relative;

      &-title {
        @include multi-ellipsis(2);

        height: 44px;
        line-height: 22px;
        margin-bottom: 6px;
      }
    }

    .shopnote-thumb-gray {
      width: 130px;
      position: absolute;
      bottom: 0;
    }

    &__right {
      position: relative;

      .wsc-img-list {
        margin-top: 0;

        &__img {
          width: 65px;
          height: 65px;
          border-radius: 2px;
        }

        &__label {
          width: 56px;
          position: absolute;
          bottom: 6px;
          left: 3px;
          font-size: 10px;
          color: #141415;
          line-height: 14px;
          background: #fff;
          opacity: 0.8;
          border-radius: 2px;
          display: flex;
          justify-content: center;
        }

        .wsc-img-list__cover-top-span {
          color: #d60000;
          display: inline-block;
          margin-right: 2px;
        }
      }
    }
  }
  .shopnote-thumb__wrapper {
    width: 130px;
  }
}
