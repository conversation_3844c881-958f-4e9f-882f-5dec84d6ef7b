import WscComponent from 'shared/common/base/wsc-component/index';
import navigate from '@/helpers/navigate';
import get from '@youzan/weapp-utils/lib/get';

WscComponent({
  properties: {
    infoData: {
      type: Object,
      value: {},
    },
    list: {
      type: Array,
      value: [],
    }
  },
  methods: {
    noteClick(event) {
      const noteAlias = get(event, 'currentTarget.dataset.item.noteAlias', '');
      const noteType = get(event, 'currentTarget.dataset.item.noteType', '');
      
      navigate.navigate({
        url: noteType === 'mp_article' 
        ? `/packages/shop/shopnote/mparticle/detail/index?noteAlias=${noteAlias}`
        : `/packages/shop/shopnote/detail/index?noteAlias=${noteAlias}`,
      });
    }
  },
});
