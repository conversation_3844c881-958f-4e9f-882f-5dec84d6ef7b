<view>
  <view
    wx:for="{{ list }}"
    wx:for-item="flatList"
    wx:for-index="idx"
    wx:key="{{ idx }}"
    class="shopnote-list-hybrid card-{{ infoData.noteStyle }} border-{{ infoData.noteCornerStyle}}"
  >
    <view
      wx:for="{{ flatList }}"
      wx:for-item="itemData"
      wx:key="{{ index }}"
      class="shopnote-list-hybrid-wrapper"
      data-item="{{itemData}}"
      bindtap="noteClick"
    >
      <view
        class="shopnote-list-hybrid-content shopnote-common-layout"
        wx:if="{{ index === 0 }}"
      >
        <view class="shopnote-common-layout__container">
          <view class="wsc-img-list" style="background-image: url('{{ itemData.imgList[0] }}')">
            <view class="wsc-img-list__cover">
              <view class="wsc-img-list__cover-center">
                <view wx:if="{{ infoData.showNoteTypeTag }}" class="wsc-img-list__label">
                  <text class="wsc-img-list__cover-center-span"># </text>
                  <text>{{ itemData.label }}</text>
                </view>
                <view style="float:right">
                  <shopnote-thumb-view
                    itemData="{{ itemData }}"
                    infoData="{{ infoData }}"
                    theme="white"
                    layout="right"
                  />
                </view>
              </view>
              <view class="wsc-img-list__cover-bottom">
                <view class="wsc-img-list__cover-bottom-title font-{{ infoData.textStyle }}">
                  {{ itemData.title || itemData.description }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view wx:elif class="shopnote-hybrid-small">
        <view class="shopnote-hybrid-small__left">
          <view class="shopnote-hybrid-small__left-title font-{{ infoData.textStyle }}">
            {{ itemData.title || itemData.description }}
          </view>
          <shopnote-thumb-view
            itemData="{{ itemData }}"
            infoData="{{ infoData }}"
            wrapper-class="shopnote-thumb__wrapper"
            bindthumbClick="thumbClick"
            layout="left"
          />
        </view>
        <view class="shopnote-hybrid-small__right">
          <view class="wsc-img-list">
            <image class="wsc-img-list__img" src="{{ itemData.imgList[0] }}" mode="aspectFit">
            <view class="wsc-img-list__cover">
              <view class="wsc-img-list__cover-center">
                <view wx:if="{{ infoData.showNoteTypeTag }}" class="wsc-img-list__label">
                  <text class="wsc-img-list__cover-center-span">#</text>
                  {{ itemData.label }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>