import WscComponent from 'shared/common/base/wsc-component/index';

WscComponent({
  properties: {
    itemData: {
      type: Object,
      value: {},
    },
    infoData: {
      type: Object,
      value: {},
    },
    theme: {
      type: String,
      value: '',
    },
    layout: {
      type: String,
      value: ''
    }
  },
  data: {
    layoutClass: '',
  },
  observers: {
    layout() {
      const { layout = '' } = this.properties;
      const { showBrowseCount, showThumbsUpCount } = this.data.infoData;
      let res = '';
      if (layout === 'left' && !showBrowseCount) {
        res = 'left';
      } else if (layout === 'right' && !showThumbsUpCount) {
        res = 'right';
      }
      this.setYZData({
        layoutClass: res,
      });
    },
  },
  externalClasses: ['wrapper-class']
});
