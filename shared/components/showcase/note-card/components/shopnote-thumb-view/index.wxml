<view class="shopnote-thumb-{{ theme }}" wx:if="{{ infoData.showBrowseCount || infoData.showThumbsUpCount}}">
  <view class="shopnote-thumb-view wrapper-class shopnote-thumb-{{ layoutClass }}">
    <view class="shopnote-thumb-view__browse">
      <view wx:if="{{ infoData.showBrowseCount }}" class="shopnote-thumb-view__browse-count">
        阅读 {{ itemData.browseCount }}
      </view>
    </view>
    <view class="shopnote-thumb-view__praise" wx:if="{{ infoData.showThumbsUpCount }}" bindtap="thumbClick">
      <image wx:if="{{ theme==='white' }}" src="https://b.yzcdn.cn/public_files/2019/10/09/baf01f6a8966ab479e1e3c0cdac734e5.png" alt="" />
      <image wx:elif src="https://b.yzcdn.cn/public_files/f848ee85f27151cc73716eb3cc910e32.svg" alt="" />
      <view class="shopnote-thumb-view__praise-count">
        {{ itemData.thumbsUpCount }}
      </view>
    </view>
  </view>
</view>
