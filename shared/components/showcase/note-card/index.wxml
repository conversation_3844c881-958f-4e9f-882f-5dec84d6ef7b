<view>
  <view class="cap-showcase-wrapper cap-showcase-wrapper-{{infoData.listType}}" >
    <shopnote-common-top
      showEnterShop="{{ !isSwipeLayout && infoData.showEnterShop }}"
      pageTitle="{{ infoData.pageTitle }}"
    />
    <cap-list 
        loading="{{ loading }}" 
        finished="{{ isSwipeLayout || !needLoadMore }}"
        bind:load="refreshList"
        wx:if="{{ infoData.listType === 0 || infoData.listType === 1 || infoData.listType === 2 }}"
    >
      <shopnote-common-layout
        itemData="{{ item }}"
        infoData="{{ infoData }}"
        list="{{ list }}"
        needLoadMore="{{ needLoadMore }}"
        isSwipeLayout="{{ isSwipeLayout }}"
      />
    </cap-list>
    <cap-list 
        loading="{{ loading }}" 
        finished="{{ !needLoadMore }}"
        bind:load="refreshList"
        wx:elif="{{ infoData.listType === 3 }}"
    >
      <shopnote-list-hybrid
        list="{{ list }}"
        infoData="{{ infoData }}"
      />
    </cap-list>
    <cap-list 
        loading="{{ loading }}" 
        finished="{{ !needLoadMore }}"
        bind:load="refreshList"
        wx:elif="{{ infoData.listType === 4 }}"
    >
      <shopnote-detail-list
        list="{{ list }}"
        infoData="{{ infoData }}"
      />
    </cap-list>
    <view
      wx:elif="{{ infoData.listType === 5 }}"
      class="cap-showcase-wrapper-{{infoData.listType}}"
    >
      <cap-list 
        loading="{{ loading }}" 
        finished="{{ !needLoadMore }}"
        bind:load="refreshList"
      >
        <view
          wx:for="{{waterfallList}}"
          wx:for-item="waterfallRow"
          class="waterfall-list"
          :key="key"
        >
          <shopnote-common-layout
            itemData="{{ itemData }}"
            infoData="{{ infoData }}"
            list="{{ waterfallRow }}"
          />
        </view>
      </cap-list>
    </view>
    <shopnote-common-bottom
      wx:if="{{ infoData.listType !== 2 && infoData.showMore }}"
    />
  </view>
</view>
