import WscComponent from 'shared/common/base/wsc-component/index';
import navigate from '@/helpers/navigate';
import Args from '@youzan/weapp-utils/lib/args';
import { node as request } from 'shared/utils/request';
import timeUtils from 'shared/utils/time';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { DEFAULT_PAGE_SIZE, LIST_TYPE_MAP } from './constants';

WscComponent({
  behaviors: [componentBehavior, loggerBehavior],

  data: {
    list: [],
    clickThumbFlag: true,
    infoData: {},
    hybridList: [],
    waterfallList: [],
    loading: false,
    needLoadMore: false,
    page: 1,
    isSwipeLayout: false,
  },

  attached() {
    const { componentData = {} } = this.data;
    const {
      textStyle = 1,
      sourceFrom = 0,
      size = 3,
      listType = 0,
      showMore = 0,
    } = componentData;
    let { titlePosition = 1 } = componentData;
    const isSwipeLayout = listType === LIST_TYPE_MAP.SWIPE;
    // listType: 0 大图模式 | 1 一行两个 | 2 横向滚动 | 3 一大两小 | 4 详细列表 | 5 瀑布流
    if (listType !== LIST_TYPE_MAP.BIG) titlePosition = 1;
    if (listType === LIST_TYPE_MAP.HYBIRD) titlePosition = 0;
    if (listType === LIST_TYPE_MAP.WATERFALL) titlePosition = 3;

    this.setYZData({
      infoData: {
        textStyle,
        sourceFrom,
        size,
        listType,
        showMore,
        ...componentData,
        titlePosition,
      },
      isSwipeLayout,
    });

    this.refreshList();
  },

  methods: {
    refreshList() {
      const { componentData = {}, kdtId, page = 1 } = this.data;
      const {
        shopNoteIds = [],
        sourceFrom = 0,
        size = 3,
        // listType = 0,
      } = componentData;
      const pageSize = DEFAULT_PAGE_SIZE;
      const curStartPos = (page - 1) * pageSize;
      const curEndPos = curStartPos + pageSize;

      const ids =
        sourceFrom === 0 ? shopNoteIds : this.genArrayFilledWith('', size);

      if (
        !ids ||
        (Array.isArray(ids) && ids.length <= 0) ||
        (Array.isArray(ids) && ids.slice(curStartPos, curEndPos).length <= 0)
      ) {
        this.setYZData({
          loading: false,
          needLoadMore: false,
        });
        return;
      }

      // eslint-disable-next-line eqeqeq
      this.setYZData({
        loading: true,
      });
      const idsCur = ids.slice(curStartPos, curEndPos);

      if (sourceFrom === 0) {
        // 手动添加
        request({
          origin: 'h5',
          path: '/wscshop/shopnote/listByIds.json',
          query: {
            shopNoteIds: idsCur.join(','),
            kdtId,
          },
        })
          .then((res) => {
            this.setYZData({ loading: false });
            if (Array.isArray(res)) {
              const curNoteListLength = this.data.list.length;
              let list = res.map((item, idx) =>
                this.format.bind(this)(item, curNoteListLength + idx)
              );
              const { listType } = this.data.infoData;
              if (listType === LIST_TYPE_MAP.HYBIRD)
                list = this.getHybridList(list);
              list = page === 1 ? list : this.data.list.concat(list);
              const waterfallList = this.splitArrayByRow(list, 2);
              const nextStartPos = page * pageSize;
              const nextEndPost = page * pageSize + pageSize;
              this.setYZData({
                list,
                waterfallList,
                page: page + 1,
                needLoadMore: ids.slice(nextStartPos, nextEndPost).length > 0,
              });
            }
          })
          .catch((err) => {
            this.setYZData({
              loading: false,
            });
            throw err;
          });
      } else if (sourceFrom === 1) {
        // 自动获取
        request({
          origin: 'h5',
          path: '/wscshop/shopnote/list.json',
          query: {
            pageSize,
            kdtId: this.data.kdtId,
            page,
            querySource: 0,
            stickStatus: 0,
            noteStatus: 'published',
            noteTypes: JSON.stringify([
              'up_new',
              'shop_keeper',
              'shop_circle',
              'single_prod_intro',
            ]),
          },
        })
          .then((res) => {
            this.setYZData({ loading: false });
            let list = res.data || [];
            const curNoteListLength = this.data.list.length;
            list = list.map((item, idx) =>
              this.format.bind(this)(item, curNoteListLength + idx)
            );
            const { listType } = this.data.infoData;
            if (listType === LIST_TYPE_MAP.HYBIRD)
              list = this.getHybridList(list);
            list = (page === 1 ? list : this.data.list.concat(list)).slice(
              0,
              size
            );
            const waterfallList = this.splitArrayByRow(list, 2);
            this.setYZData({
              list,
              waterfallList,
            });
            const nextStartPos = page * pageSize;
            const nextEndPost = page * pageSize + pageSize;
            this.setYZData({
              page: page + 1,
              needLoadMore: ids.slice(nextStartPos, nextEndPost).length > 0,
            });
          })
          .catch((err) => {
            this.setYZData({
              loading: false,
            });
            throw err;
          });
      }
    },

    splitArrayByRow(arr, rowNum = 2) {
      const result = [];
      for (let i = 0; i < arr.length; i += rowNum) {
        for (let j = 0; j < rowNum; j++) {
          if (!result[j]) {
            result[j] = [];
          }
          if (arr[i + j]) {
            result[j].push(arr[i + j]);
          }
        }
      }

      return result;
    },

    format(item, idx) {
      item.index = idx;
      item.publishTime = timeUtils.formatTime(item.publishTime * 1000);
      item.title = this.mapTitle(item);
      item.type = this.mapType(item);
      item.browseCount = this.getNum(item.browseCount);
      item.thumbsUpCount = this.getNum(item.thumbsUpCount);
      item.imgList = this.computedUrl(item);
      item.label = this.computedLabel(item);
      return item;
    },

    getHybridList(list) {
      const res = [];
      const len = list.length;
      const n = 3; // 一块显示3个
      const lineNum = len % n === 0 ? len / n : Math.floor(len / n + 1);
      for (let i = 0; i < lineNum; i++) {
        const temp = list.slice(i * n, i * n + n);
        res.push(temp);
      }
      return res;
    },

    getNum(count) {
      if (count < 10000) {
        return count;
      }
      return `${(count / 10000).toFixed(1)}万`;
    },

    genArrayFilledWith(value, size) {
      const list = [];
      list.length = size;
      list.fill(value);
      return list;
    },

    computedUrl(item) {
      if (item.coverPhotos && item.coverPhotos.length) {
        return [cdnImage(item.coverPhotos[0], '!730x0.jpg')];
      }
      if (item.headPhoto !== '') {
        return [cdnImage(item.headPhoto, '!730x0.jpg')];
      }
      if (item.noteItemsBriefInfo && item.noteItemsBriefInfo.length) {
        return [cdnImage(item.noteItemsBriefInfo[0].imageUrl, '!730x0.jpg')];
      }
      return [];
    },

    computedLabel(item) {
      switch (item.templateId) {
        case 1:
          return '上新';
        case 2:
          return '掌柜说';
        case 3:
          return '单品介绍';
        case 4:
          return '种草清单';
        case 6:
          return '公众号文章';
        default:
          return '';
      }
    },

    linkToMore() {
      this.ensureAppLogger('click_content', { banner_id: this.getBannerId() });
      navigate.switchTab({
        url: '/packages/shop/shopnote/list/index',
      });
    },

    // 这个方法没有在用了
    itemClick(event) {
      const { index } = event.currentTarget.dataset;
      const alias = this.data.list[index].noteAlias;
      const bannerId = this.getBannerId(index + 1);
      // eslint-disable-next-line no-undef
      const noteType = get(event, 'currentTarget.dataset.item.noteType', '');

      const url = Args.add(
        noteType === 'mp_article'
          ? '/packages/shop/shopnote/mparticle/detail/index'
          : '/packages/shop/shopnote/detail/index',
        {
          noteAlias: alias,
          banner_id: bannerId,
        }
      );
      wx.navigateTo({
        url,
      });
    },

    mapType(item) {
      const type = item.noteType;
      switch (type) {
        case 'up_new':
          return '#上新';
        case 'shop_keeper':
          return '#掌柜说';
        case 'single_prod_intro':
          return '#单品介绍';
        case 'shop_circle':
          return '#店铺圈';
        case 'mp_article':
          return '#公众号文章';
        default:
          return '';
      }
    },

    mapTitle(item) {
      const type = item.noteType;
      switch (type) {
        case 'up_new':
        case 'shop_keeper':
        case 'single_prod_intro':
        case 'mp_article':
          return item.title;
        case 'shop_circle':
          return item.title;
        default:
          return '';
      }
    },
  },
});
