## Video 视频

### 使用指南

在 index.json 中引入组件。

```json
{
  "usingComponents": {
    "cap-goods": "path/to/@youzan/captain-weapp/dist/video/index"
  }
}
```

### 代码演示

#### 基础用法

```html
<cap-video
    src="{{ src }}"
    poster="{{ poster }}"
    bindplay="handlePlay"
/>
```

### API

| 参数       | 说明      | 类型       | 默认值       | 可选值       |
|-----------|-----------|-----------|-------------|-------------|
| src | 视频链接 | `String`  | ``  |  |
| poster | 封面图 | `String`  | ``  |  |

### Event

| 事件名 | 说明 | 参数 |
|-----------|-----------|-----------|
| play | 播放时触发 | event  |
| pause |播放时触发 | event |
