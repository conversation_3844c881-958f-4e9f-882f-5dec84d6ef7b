import getSystemInfo from 'shared/utils/browser/system-info';

Component({
  type: 'video',

  properties: {
    src: String,
    poster: String,
    objectFit: {
      type: String,
      value: 'contain'
    }
  },

  data: {
    height: 0,
    hidePoster: false,
    showVideo: false,
  },

  ready() {
    this.init();
  },

  methods: {
    init() {
      this.setData({
        height: (getSystemInfo().windowWidth * 9) / 16,
        id: Math.random().toString(36).substr(2, 9)
      });

      this.videoContext = wx.createVideoContext('video-id', this);
    },

    handlePosterClick() {
      this.setData({
        showVideo: true
      }, () => {
        // 设置延迟 隐藏视频模块出现的一刹那的缩放动画
        setTimeout(() => {
          this.setData({
            hidePoster: true,
            showVideo: true
          }, () => {
            this.videoContext.play();
          });
        }, 200);
      });
    },

    handleVideoPlay(e) {
      this.triggerEvent('play', e.detail);
    },

    handleVideoPause(e) {
      this.triggerEvent('pause', e.detail);
    },

    handleVideoEnded(e) {
      this.triggerEvent('ended', e.detail);
    },

    handleUpdateTime(e) {
      this.triggerEvent('timeupdate', e.detail);
    },

    pause() {
      this.videoContext.pause();
    },

    play() {
      this.videoContext.play();
    }
  }
});
