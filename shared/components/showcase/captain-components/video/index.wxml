<view
  class="c-video"
  wx:if="{{src}}"
  style="height: {{ height }}px"
>
  <view
    class="c-video__poster-wrap"
    wx:if="{{ !hidePoster }}"
  >
    <image
      class="c-video__poster"
      src="{{ poster }}"
      mode="aspectFit"
      style="height: {{ height }}px"
    />
    <view
      class="c-video__button"
      bind:tap="handlePosterClick"
    ></view>
    <view class="c-video__mask"></view>
  </view>
  <video
    id="video-id"
    class="c-video__player {{ !showVideo ? 'c-video__player-hide' : ''}}"
    show-center-play-btn="{{ false }}"
    objectFit="{{ objectFit }}"
    src="{{ src }}"
    style="height: {{ height }}px"
    bindplay="handleVideoPlay"
    bindpause="handleVideoPause"
    bindended="handleVideoEnded"
    bindtimeupdate="handleUpdateTime"
  />
</view>
