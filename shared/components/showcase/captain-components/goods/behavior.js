import pick from '@youzan/weapp-utils/lib/pick';

export default Behavior({
  methods: {
    computeItemSoldout(item) {
      return +item.soldStatus === 2 && +item.buyWay !== 0;
    },

    computeInfoData(data) {
      return pick(
        data || this.data,
        'showTitle',
        'showSubTitle',
        'showPrice',
        'showBuyButton',
        'buyButtonType',
        'showSoldNum',
        'buttonText',
        'textAlignType',
        'textStyleType',
        'isPlugin',
        'showOriginPrice',
        'showTags'
      );
    },

    /**
     * info 单个配置
     */
    computeInfoItemData(key, value) {
      const infoItem = {};
      infoItem[`infoData.${key}`] = value;
      this.setData({
        ...infoItem
      });
    }
  }
});
