## Goods 商品列表

### 使用指南

在 index.json 中引入组件。

```json
{
  "usingComponents": {
    "cap-goods": "path/to/@youzan/captain-weapp/dist/goods/index"
  }
}
```

### 代码演示

#### 基础用法

```html
<cap-goods
  list="{{ list }}"
  layout="{{ 0 }}"
/>
```

### API

| 参数       | 说明      | 类型       | 默认值       | 必须      |
|-----------|-----------|-----------|-------------|-------------|
| appId | 小程序appId，商品跳转时用 |  String |  |  |
| list | 商品列表数据 |  Array | `[]` |  |
| layout | 商品布局方式( 0:大图 1:小图 2:一大两小 3:详情列表 5:一行三个) |  Number | `0` |  |
| sizeType | 商品展示模式(0:卡片模式 1:瀑布流 2:极简样式 3:促销 4:多图(不可用，被微小店占用) 5:一行三个 6:左右滑动) |  Number | `0` |  |
| showTitle |  是否展示标题 |  Boolean | `true` |  |
| showSubTitle |  是否展示副标题 |  Boolean | `true` |  |
| showPrice |  是否展示价格 |  Boolean | `true` |  |
| showBuyButton |  是否展示购买按钮 |  Boolean | `true` |  |
| buyButtonType | 购买按钮的样式(1,2,3,4 四种可选) |  Number | `1` |  |
| showCornerMark |  是否显示角标 |  Boolean | `false` |  |
| cornerMarkType | 角标样式(0,1,2,3 四种可选) |  Number | `0` |  |
| cornerMarkImage | 自定义角标图片（36x36） |  String | `''` |  |
| imageRatio | 图片比例(0：`3:2`， 1：`1:1`)。默认为无，显示整个图片 |  Number | - |  |
| buttonText | 自定义按钮文字 |  String | - | - |
