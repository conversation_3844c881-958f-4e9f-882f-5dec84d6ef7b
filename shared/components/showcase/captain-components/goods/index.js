import pick from '@youzan/weapp-utils/lib/pick';
import WscComponent from 'pages/common/wsc-component/index';
import goodsLayoutBehavior from '../goods-layout/behaviors/goods-layout-behavior';
import countdownUtilBehavior from '../ump-goods-layout/behaviors/countdown-util-behavior';

WscComponent({
  type: 'goods',

  behaviors: [countdownUtilBehavior, goodsLayoutBehavior],

  properties: {
    /**
     * 商品列表数据
     */
    list: {
      type: Array,
      value: [],
      observer(newVal) {
        this.setYZData({
          // ?? ob obj
          curList: this.computeGoodsLayoutList(newVal)
        });
      }
    },

    // 是否展示营销氛围
    showTags: {
      type: Boolean,
      value: false,
      observer(newVal) {
        this.computeInfoItemData('showTags', newVal);
      },
    },

    skeletonNumber: {
      type: Number,
      value: 0,
    },

    /**
     * 是否展示销量
     */
    showSoldNum: {
      type: Boolean,
      value: false,
      observer(newVal) {
        this.computeInfoItemData('showSoldNum', newVal);
      },
    },

    /**
     * 是否展示划线价
     */
    showOriginPrice: Boolean,

    /**
     * 是否展示 `查看更多` 按钮
     */
    isShowMore: Boolean,

    /**
     * `查看更多` 链接
     */
    moreUrl: String,

    /**
     * `查看更多` 按钮类型
     */
    moreButtonType: {
      type: Number,
      value: 1
    },

    /**
     * 是否是在插件里
     */
    isPlugin: Boolean,

    /**
     * 是否开启横向滑动的分页
     */
    openSwipePagination: Boolean
  },

  data: {
    curList: [],
    infoData: {}
  },

  attached() {
    this.setData({
      infoData: this.computeInfoData()
    });
  },

  methods: {
    computeGoodsLayoutList(list) {
      const { isPlugin } = this.data;
      return list.map((item) => {
        item.isSoldout = +item.soldStatus === 2 && +item.buyWay !== 0;
        if (isPlugin) {
          item.url = `/pages/goods/detail/index?alias=${item.alias}`;
        }
        return item;
      });
    },

    computeInfoData() {
      return pick(
        this.data,
        'showTitle',
        'showSubTitle',
        'showPrice',
        'showBuyButton',
        'buyButtonType',
        'showSoldNum',
        'buttonText',
        'textAlignType',
        'textStyleType',
        'isPlugin',
        'showOriginPrice',
        'showTags'
      );
    },

    /**
     * info 单个配置
     */
    computeInfoItemData(key, value) {
      const infoItem = {};
      infoItem[`infoData.${key}`] = value;
      this.setData({
        ...infoItem
      });
    }
  }
});
