<form-button bindtap="goToDetail" business-module="owlMiniProgram">
<view class="cap-knowledege-wrap">
    <view 
      class="{{classWrap}}"
      style="margin: {{ goodsDetail.goodsPadding / 2 }}px"
      bindtap="goToDetail">
      <!-- 缩略图 -->
        <view class='cap-picture-part'>
          <view
          style="border-top-left-radius: {{ goodsDetail.goodsRadius === 1 ? 8 : 0 }}px; border-top-right-radius: {{ goodsDetail.listMode !== 3 && goodsDetail.goodsRadius === 1 ? 8 : 0 }}px; border-bottom-left-radius:  {{ (goodsDetail.listMode === 3 || goodsDetail.goodsStyle === 3 ) && goodsDetail.goodsRadius === 1 ? 8 : 0 }}px; border-bottom-right-radius:  {{goodsDetail.goodsStyle === 3 && goodsDetail.goodsRadius === 1 ? 8 : 0 }}px;"
          class="imgWrap">
            <block wx:if="{{ goodsDetail.imgSize === 1 }}">
              <image
                  class="imgWrap__bg" 
                  src="{{ picUrl }}"
                  mode="aspectFill">
                </image>
              <image
                class="knowledge-item__img" 
                src="{{ picUrl }}"
                mode="aspectFit">
              </image>
            </block>
            <block wx:else>
              <image
                style="border-top-left-radius: {{ goodsDetail.goodsRadius === 1 ? 8 : 0 }}px; border-top-right-radius: {{ goodsDetail.listMode !== 3 && goodsDetail.goodsRadius === 1 ? 8 : 0 }}px; border-bottom-left-radius:  {{ (goodsDetail.listMode === 3 || goodsDetail.goodsStyle === 3 ) && goodsDetail.goodsRadius === 1 ? 8 : 0 }}px; border-bottom-right-radius:  {{goodsDetail.goodsStyle === 3 && goodsDetail.goodsRadius === 1 ? 8 : 0 }}px;"
                class="knowledge-item__img"
                src="{{ picUrl }}"
                mode="aspectFill">
              </image>
            </block>
          </view>
          <view class="knowledge-picture-picinfo">
            <view wx:if="{{ picBottomLeftTag }}" style="background-color: {{ itemType === 'live' && content.liveStatus !==2 ? '#999' : themeColor }};  {{goodsDetail.goodsStyle === 3 && goodsDetail.goodsRadius === 1 ? 'border-bottom-left-radius: 8px;' : '' }}" class='knowledge-picture-left__tag'>{{ picBottomLeftTag }}</view>
            <view wx:if="{{ picBottomRightTag }}" class='knowledge-picture-right__tag'>{{ picBottomRightTag }}</view>
          </view>
        </view>
          <view class="cap-description-part {{ goodsDetail.textAlign === 1 ? 'align-center' : '' }}">
            <!-- 公有 -->
            <view class='cap-description-info-part'>
              <block wx:for="{{displayContent}}" wx:key="{{ index }}">
                <slot wx:if="{{ item === 0 }}" name="title"></slot>
                <slot wx:if="{{ item === 1 }}" name="description"></slot>
                <!-- 线下课 -->
                <view class="course-tags" wx:if="{{ item === 2 && content.tagList.length }}">
                  <block wx:for="{{content.tagList}}" wx:key="{{ index }}">
                    <text>{{ item.tag }}</text>
                  </block>
                </view>
                <!-- <slot wx:if="{{ item === 2 }}" name="course-tag"></slot> -->
                <slot wx:if="{{ item === 3 }}" name="time-range"></slot>
                <!-- 内容 -->
                <slot wx:if="{{ item === 2 }}" name="belong-column"></slot>
                <!-- 直播 -->
                <slot wx:if="{{ item === 2 }}" name="live-time"></slot>
              </block>
            </view>
            <view class='cap-description-price-part'>
              <block wx:for="{{displayContent}}" wx:key="{{ index }}">
                <slot wx:if="{{ item === 4 }}" name="course-price"></slot>
                <!-- 专栏 -->
                <slot wx:if="{{ !isIOS && item === 3 }}" name="column-price"></slot>
                <!-- 内容 -->
                <slot wx:if="{{ !isIOS && item === 4 }}" name="content-price"></slot>
                <!-- 直播 -->
                <slot wx:if="{{ !isIOS && item === 3 }}" name="live-price"></slot>
              </block>
            </view>
          </view>
      </view>
</view>
</form-button>
