import { LIVE_STATUS, MEDIA_TYPE_SUFFIX_MAP } from './constant';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import getSystemInfo from 'shared/utils/browser/system-info';

const { platform } = getSystemInfo();

Component({
  options: {
    multipleSlots: true,
  },

  properties: {
    itemType: {
      type: String,
      value: 'course',
    },
    content: {
      type: Object,
      observer(newValue) {
        this.updateContent(newValue);
      },
      value: {},
    },
    goodsDetail: {
      type: Object,
      value: {},
    },
    themeColor: {
      type: String,
      value: '',
    },
  },

  data: {
    picUrl: '',
    picBottomLeftTag: '',
    picBottomRightTag: '',
    displayContent: [],
    listMode: 0,
    classWrap: '',
    url: '',
    isIOS: platform === 'ios',
  },

  attached() {
    this.setData({
      picUrl: cdnImage(this.data.picUrl, '!950x0.jpg'),
    });
  },

  methods: {
    goToDetail() {
      wx.navigateTo({
        url: this.data.url,
      });
    },

    updateContent(content) {
      const { goodsDetail, themeColor } = this.data;
      if (goodsDetail.displayContent instanceof Array) {
        goodsDetail.displayContent.sort();
      }
      this.setData({
        picUrl: this.getPicUrl(content),
        url: this.getLink(content),
        picBottomLeftTag: this.getPicBottomLeftTag(content),
        picBottomRightTag: this.getPicBottomRightTag(content),
        displayContent: goodsDetail.displayContent || [],
        listMode: goodsDetail.listMode,
        themeColor,
        classWrap: `goods-text-align${goodsDetail.textAlign} goods-radius${goodsDetail.goodsRadius} knowledge-listmode${goodsDetail.listMode} goods-style-mode${goodsDetail.goodsStyle}`,
      });
    },

    getLink(content = {}) {
      const { itemType } = this.data;
      switch (itemType) {
        case 'course':
          // TODO: link to course detail
          return `/packages/edu/webview/index?targetUrl=${encodeURIComponent(
            `https://h5.youzan.com/wscvis/edu/prod-detail?alias=${content.alias}&kdt_id=${content.kdtId}`
          )}`;
        case 'column':
          return `/packages/paidcontent/column/index?alias=${content.alias}&kdt_id=${content.kdtId}`;
        case 'content':
          return `/packages/paidcontent/content/index?alias=${content.alias}&kdt_id=${content.kdtId}`;
        case 'live':
          return `/packages/paidcontent/live/index?alias=${content.alias}&kdt_id=${content.kdtId}`;
        default:
          return '';
      }
    },

    getPicUrl(content = {}) {
      const { itemType } = this.data;
      if (content.imageUrl) {
        return content.imageUrl;
      }
      switch (itemType) {
        case 'course':
          return content.pictureWrapDTO ? content.pictureWrapDTO.url : '';
        case 'column':
        case 'content':
          return content.cover || content.picture.cover;
        case 'live':
          return content.cover || '';
        default:
          return '';
      }
    },

    getPicBottomRightTag(content = {}) {
      const { itemType } = this.data;
      switch (itemType) {
        case 'course':
          return content.totalSoldNum ? `${content.totalSoldNum}人学过` : '';
        case 'column':
          return content.subscriptionsCount
            ? `${content.subscriptionsCount}人已购`
            : '';
        case 'content':
          if (content.pageView) {
            const suffix = MEDIA_TYPE_SUFFIX_MAP[content.mediaType];
            return `${content.pageView}${suffix}`;
          }
        // return '';
        case 'live':
          return content.pageView ? `${content.pageView}次观看` : '';
        default:
          return '';
      }
    },

    getPicBottomLeftTag(content = {}) {
      const { itemType, goodsDetail = {} } = this.data;
      const { displayContent = [] } = goodsDetail;
      switch (itemType) {
        case 'course':
          return content.courseTypeName;
        case 'column':
          return content.contentsCount &&
            displayContent.find((item) => item === 2)
            ? `已更新${content.contentsCount}期`
            : '';
        case 'content':
          if (
            !content.updatedAt ||
            !displayContent.find((item) => item === 3)
          ) {
            return '';
          }
          // eslint-disable-next-line no-case-declarations
          const updateDate = new Date(content.updatedAt);
          // eslint-disable-next-line no-case-declarations
          const updateMonth = updateDate.getMonth() + 1;
          // eslint-disable-next-line no-case-declarations
          const updateDay = updateDate.getDate();

          return `${updateMonth < 10 ? `0${updateMonth}` : updateMonth}-${
            updateDay < 10 ? `0${updateDay}` : updateDay
          }`;
        case 'live':
          return content.liveStatus ? LIVE_STATUS[content.liveStatus - 1] : '';
        default:
          return '';
      }
    },
  },
});
