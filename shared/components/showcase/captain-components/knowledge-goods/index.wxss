/* .cap-knowledge-goods {
  height: 100%;
} */
.cap-knowledege-wrap {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  flex-direction: column;
}

.knowledge-listmode0 {
  display: flex;
  flex-direction: column;
}

.knowledge-listmode1,
.knowledge-listmode2,
.knowledge-listmode4,
.knowledge-listmode5 {
  display: flex;
  flex-direction: column;
  flex-grow: 1
}

.cap-picture-part {
  position: relative;
  height: 0;
  padding-bottom: 56.25%
}

.cap-description-part {
  /* padding: 10px; */
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
}

.align-center {
  align-items: center;
}

.cap-picture-part .imgWrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
}

.cap-picture-part .imgWrap .imgWrap__bg {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(10px);
}

.cap-picture-part .imgWrap .knowledge-item__img {
  position: relative;
  display: block;
  object-fit: contain;
  width: 100%;
  height: 100%;
}

.cap-description-part .cap-description-info-part {
  padding-top: 10px;
  flex-grow: 1;
}

.cap-description-part .course-tags {
  display: flex;
  margin: 0 10px 5px 10px;
  overflow: hidden;
  flex-wrap:wrap;
  height:15px;
}

.cap-description-part .course-tags text {
  margin-right: 5px;
  font-size: 8px;
  border: 1px #e0e0e0 solid;
  color: #e0e0e0;
  border-radius: 8px;
  padding: 1px 6px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.knowledge-picture-picinfo {
  position: absolute;
  display: flex;
  font-size: 10px;
  color: #fff;
  height: 18px;
  line-height: 18px;
  background-color: rgba(0,0,0,.6);
  border-radius: 9px;
  max-width: 93%;
}

.knowledge-picture-left__tag {
  border-radius: 9px;
  padding: 0 8px;
  z-index: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.knowledge-picture-right__tag {
  border-radius: 9px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.knowledge-listmode3 {
  display: flex;
  height: 120px;
  width: 100%;
}

.knowledge-item__img {
  width: 100%;
}

/* .knowledge-picture-picinfo {
  position: absolute;
  bottom: 10px;
} */

.knowledge-listmode0 .knowledge-picture-picinfo {
  left: 10px;
  bottom: 10px;
}

.knowledge-listmode1 .knowledge-picture-picinfo, 
.knowledge-listmode4 .knowledge-picture-picinfo {
  left: 5px;
  bottom: 5px;
}

.knowledge-listmode2 .knowledge-picture-picinfo,
.knowledge-listmode5 .knowledge-picture-picinfo {
  left: 0;
  bottom: 0;
  font-size: 10px !important;
}


.knowledge-listmode3 .knowledge-picture-picinfo {
  position: absolute;
  top: 10px;
}

.knowledge-listmode2 .knowledge-picture-left__tag,
.knowledge-listmode5 .knowledge-picture-left__tag,
.knowledge-listmode3 .knowledge-picture-left__tag {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.knowledge-listmode3 .cap-picture-part {
  position: relative;
  width: 120px;
  height: 120px;
  padding-bottom: 0;
  min-width: 120px;
}

.knowledge-listmode3 .cap-description-part {
  flex-grow: 1;
  text-align: left !important;
}

.knowledge-listmode3 .knowledge-item__img {
  width: 120px;
  height: 120px;
}

.goods-text-align1 .cap-description-part {
  text-align: center;
}

/* .goods-text-align1 .cap-description-part .course-tags {
  justify-content: center;
} */

/* 商品样式 */
/* 无边白底 */
.goods-style-mode0 {
  background-color: #fff;
  border-width: 0; 
}

/* 卡片投影 */
.goods-style-mode1 {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(93, 113, 127, 0.08);
}

/* 白底描边 */
.goods-style-mode2 {
  background-color: #fff;
  border: 0.5px solid #e6e6e6;
}

/* 无边透明底 */
.goods-style-mode3 {
  border-width: 0; 
}

/* 商品倒角 */
/* 直角 */
.goods-radius0 {
  border-radius: 0;
}

/* 圆角 */
.goods-radius1 {
  border-radius: 8px;
}

/* 一行两个 */
/* .knowledge-twoinrow {
  width: 50%;
} */

/* .knowledge-twoinrow .knowledge-picture-picinfo {
  position: absolute;
  bottom: 10px;
}

.knowledge-twoinrow .knowledge-item__img {
  width: 100%;
  overflow: hidden;
} */