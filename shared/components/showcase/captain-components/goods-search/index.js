import throttle from '@youzan/weapp-utils/lib/throttle';
import StickyControl from 'shared/utils/sticky-control';
import pageScrollBehavior from 'shared/components/showcase/behaviors/page-scroll-control-behavior';

const STICKY_NAME = 'searchTop';

function createScrollCheckFunc(cb) {
  var { scrollCheckFunc } = this.data;
  if (scrollCheckFunc) {
    return;
  }

  scrollCheckFunc = throttle((e) => {
    const {
      isStartFixed,
      isNeedFixedScroll,
      isFixed,
      searchScrollTop,
      componentHeight,
    } = this.data;
    // 是否满足开始固定定位条件
    if (!isStartFixed || !isNeedFixedScroll) return;
    // 上滑下滑 逻辑
    const _isFixed = e.scrollTop < searchScrollTop;
    if (
      isFixed !== _isFixed &&
      StickyControl.hasStickyControlItem(STICKY_NAME)
    ) {
      // 改变高度 发布订阅
      StickyControl.publicStickyControlSubscribe(
        STICKY_NAME,
        _isFixed,
        componentHeight
      );
      const { positionTop } =
        StickyControl.getStickyControlItem(STICKY_NAME) || {};
      const hideStickyTop =
        StickyControl.getStickyControlNextTop(STICKY_NAME) - componentHeight;
      let stickyTop = _isFixed ? positionTop : hideStickyTop;
      this.setData({
        isFixed: _isFixed,
        stickyTop,
      });
    }

    this.setData({
      searchScrollTop: e.scrollTop,
    });
  }, 300);

  this.setData(
    {
      scrollCheckFunc,
    },
    () => {
      cb && cb();
    }
  );
}

Component({
  behaviors: [pageScrollBehavior],

  options: {
    addGlobalClass: true,
  },

  properties: {
    positionType: {
      type: Number,
      value: 0,
    },
    positionShowMethod: {
      type: Number,
      value: 0,
    },
    textAlignMethod: {
      type: Number,
      value: 0,
    },
    hotSearchKeys: {
      type: Array,
      value: [],
    },
    textColor: String,
    searchBg: String,
    borderMethodStyle: String,
    borderStyleColor: String,
    borderStyleHeight: {
      type: Number,
      value: 40,
    },
    zIndex: Number,
    scrollWords: Boolean,
    scrollInterval: Number,
    showScan: Boolean,
  },

  data: {
    inputWidth: 'auto',
    /**
     * 是否需要固定定位 mounted 中 为false 则不执行相关逻辑
     */
    isNeedFixed: false,
    /**
     * 是否需要固定定位
     */
    isStartFixed: false,
    /**
     * 是否处于上滑时固定定位
     */
    isFixed: false,
    /**
     * 是否需要执行上滑下滑逻辑
     */
    isNeedFixedScroll: false,
    /**
     * 获取在页面中定位
     */
    hasGetSearchTop: false,
    /**
     * 滚动高度记录
     */
    searchTop: 0,
    isAlignLeft: true,
    searchScrollTop: 0,
    scrollCheckFunc: null,
    stickyTop: 0,
    transitionTime: 300,
    componentHeight: 40,
  },

  attached() {
    let {
      isNeedFixed,
      isFixed,
      isNeedFixedScroll,
      isAlignLeft,
      positionType,
      textAlignMethod,
      positionShowMethod,
      borderStyleHeight,
    } = this.data;

    isNeedFixed = isFixed = +positionType === 1;
    isNeedFixedScroll = +positionShowMethod === 1;
    isAlignLeft = +textAlignMethod === 0;
    const componentHeight = borderStyleHeight + 10;
    this.setData({
      isNeedFixed,
      isFixed,
      isNeedFixedScroll,
      isAlignLeft,
      componentHeight,
      stickyTop: isNeedFixedScroll ? -componentHeight : 0,
    });
    // 方便删除 __wxExparserNodeId__
    // 上滑 下滑
    if (isNeedFixed) {
      StickyControl.setStickyControlSubscribe(
        this.handelStickyTop,
        this,
        STICKY_NAME
      );
      StickyControl.setStickyControlCheckItem(STICKY_NAME, 0);
      if (isNeedFixedScroll) {
        this.pageKey = this.setPageScrollControlSubscribe(
          this.searchScroll,
          this
        );
      }
    }
  },

  ready() {
    const { isNeedFixed } = this.data;
    if (!isNeedFixed) return;
    this.searchScrollObserve();
  },

  detached() {
    this.delPageScrollControlSubscribe(this.searchScroll, this, this.pageKey);
  },

  methods: {
    /**
     * 相交监听
     */
    searchScrollObserve() {
      // 上滑 下滑相交比不同
      const { isNeedFixedScroll, componentHeight } = this.data;
      // 提前监听
      wx.createIntersectionObserver(this, {
        thresholds: [1],
      })
        .relativeToViewport()
        .observe('.search-position__view', (result) => {
          const { top } = result.boundingClientRect;
          const isStartFixed = top <= 0;

          // 先占优先级
          const { zIndex } = this.data;
          // z-index 顺序 修改请查看 docs/z-index.md
          if (zIndex !== 121) {
            StickyControl.setStickyControlCheckItem(
              STICKY_NAME,
              isStartFixed ? zIndex : 0,
              true
            );
          }
          // 不是上下滚动的话 可以设置 stickyTop 了
          if (!isNeedFixedScroll) {
            this.searchStickyControl(isStartFixed);
          } else {
            // 上下滚动 先设置 ControlItem 防止后面的吸顶
            StickyControl.setStickyControlItem(STICKY_NAME, 0, isStartFixed);
          }
        });

      if (!isNeedFixedScroll) return;

      wx.createIntersectionObserver(this, {
        thresholds: [0],
      })
        .relativeToViewport()
        .observe('.search-position__view', (result) => {
          const { top } = result.boundingClientRect;
          const isStartFixed = top <= -componentHeight;
          const stickyTop = -componentHeight;
          this.setData({
            isStartFixed,
            stickyTop,
          });
        });
    },
    /**
     * 设置吸顶
     * @param isStartFixed
     */
    searchStickyControl(isStartFixed) {
      const { isNeedFixedScroll, componentHeight } = this.data;

      const itemHeight = isNeedFixedScroll ? 0 : componentHeight;
      StickyControl.setStickyControlItem(STICKY_NAME, itemHeight, isStartFixed);
      const { positionTop = 0 } =
        StickyControl.getStickyControlItem(STICKY_NAME) || {};
      const stickyTop = isNeedFixedScroll ? -componentHeight : positionTop;

      this.setData({
        isStartFixed,
        stickyTop,
      });
    },
    /**
     * 滚动订阅执行
     * @param e
     */
    searchScroll(e) {
      this.handelScrollPosition(e);
    },
    /**
     * 吸顶订阅执行
     * @param stickyTop
     */
    handelStickyTop({ type, stickyTop = 0 }) {
      if (type === 'stickyTop') {
        this.setData({
          stickyTop,
        });
      }
    },
    /**
     * 滚动截流
     * @param e
     */
    handelScrollPosition(e) {
      const { scrollCheckFunc } = this.data;
      if (!scrollCheckFunc) {
        createScrollCheckFunc.call(this, () => {
          const { scrollCheckFunc } = this.data;
          scrollCheckFunc(e);
        });
      } else {
        scrollCheckFunc(e);
      }
    },

    handleSwiperChange(current) {
      this.triggerEvent('swipe-change', current);
    },

    handleScanIconTap() {
      this.triggerEvent('scan-icon-click');
    },

    noop() {},
  },
});
