<view class="search-position__view" style="height: {{ componentHeight }}px;">
  <view class="search-position__wrap {{ isStartFixed ? 'search-position__wrap--fixed' : ''}}"
        style="top:{{ stickyTop }}px; transition: linear top {{transitionTime}}ms;">
    <showcase-cap-search
      alignLeft="{{ isAlignLeft }}"
      searchStyle="height: {{ componentHeight }}px;padding: 5px 15px; background-color: {{ searchBg }};"
      inputStyle="background-color: {{ borderStyleColor }};height: {{ borderStyleHeight }}px;"
      inputClass="search-input__{{ borderMethodStyle }}"
      searchIconStyle="{{ textAlignMethod === 1 && scrollWords ? 'margin-right: 200px' : '' }}"
      placeholder="{{ scrollWords ? '' : hotSearchKeys[0] || '搜索商品' }}"
      disabled
      textColor="{{ textColor }}"
    />
    <swiper wx:if="{{ scrollWords }}" bindchange="handleSwiperChange" class="search-swipe {{ textAlignMethod === 1 ? 'is-align-center' : 'is-align-left' }}" style="top: {{ (componentHeight-36)/2 }}px" vertical="true" autoplay="true" circular="true" interval="{{ scrollInterval }}" display-multiple-items='1'>
      <swiper-item class="search-swipe__item" catch:touchmove="noop" wx:for="{{ hotSearchKeys }}" wx:key="*this">
        <view class='content-item' style="color: {{ textColor }}">
          {{ item }}
        </view>
      </swiper-item>
    </swiper>
    <van-icon wx:if="{{ showScan }}" catchtap="handleScanIconTap" name="scan" class="scan_icon" color="{{ textColor }}" />
  </view>
</view>
