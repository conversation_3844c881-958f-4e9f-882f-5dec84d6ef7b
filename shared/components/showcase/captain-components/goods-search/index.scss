.search-view {
  position: relative;
}

.search-position__view {
  position: relative;
  width: 100vw;
}

.search-position__wrap {
  width: 100vw;
  transition: linear top 300ms;
}

.search-position__wrap--fixed {
  position: fixed;
  left: 0;

  /** z-index 顺序 修改请查看 docs/z-index.md **/
  z-index: 110;
}

.search-input__rect {
  border-radius: 4rpx !important;
}

.search-input__circle {
  border-radius: 40rpx !important;
}

.goods-search-wrap {
  position: relative;
}

.search-swipe {
  position: absolute;
  left: 0;
  right: 0;
  height: 36px;
  z-index: 2;

  .search-swipe__item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #9b9b9b;

    .content-item {
      padding-left: 50px;
    }
  }

  &.is-align-center {
    .search-swipe__item {
      justify-content: center;

      .content-item {
        min-width: 210px;
        padding-left: 30px;
        text-align: center;
      }
    }
  }
}

.scan_icon {
  position: absolute;
  font-size: 18px;
  right: 18px;
  padding: 0 8px;
  top: 50%;
  margin-top: -8px;
  z-index: 2;
}
