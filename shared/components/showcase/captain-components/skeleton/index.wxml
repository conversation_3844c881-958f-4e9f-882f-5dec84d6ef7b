<view class="cap-skeleton cap-skeleton--{{ type }} custom-class">
  <block wx:if="{{ type == 'text' }}">
    <view
      wx:for="{{ rowsList }}"
      wx:key="index"
      class="cap-skeleton__text"
      style="{{ rowStyle }}"
    >
    </view>
  </block>

  <block wx:elif="{{ type == 'rect' }}">
    <view class="cap-skeleton__rect" style="background-color: {{ color }}; {{ rectStyle }}"></view>
  </block>
</view>
