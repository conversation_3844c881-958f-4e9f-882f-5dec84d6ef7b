Component({
  externalClasses: ['custom-class'],

  properties: {
    /**
     * 占位类型：目前支持`text`和`rect`
     */
    type: {
      type: String,
      value: 'text'
    },

    /**
     * 文本行数
     */
    rows: {
      type: Number,
      value: 1
    },

    fontSize: {
      type: Number,
      value: 12
    },

    lineHeight: {
      type: Number,
      value: 16
    },

    /**
     * 占位背景颜色
     */
    color: {
      type: String,
      value: '#f2f2f2'
    },

    /**
     * 矩形自定义样式
     */
    rectStyle: {
      type: String,
      value: 'width: 20px; height: 20px;'
    }
  },

  data: {
    rowStyle: ''
  },

  attached() {
    this.setData({
      rowStyle: this.computeRowStyle(),
      rowsList: new Array(this.data.rows)
    });
  },

  methods: {
    computeRowStyle() {
      const { color, fontSize, lineHeight } = this.data;
      const padding = Math.max((lineHeight - fontSize) / 2, 2);

      return `background-color: ${color}; height: ${fontSize}px; padding: ${padding}px 0`;
    }
  }
});
