Component({
  externalClasses: ['sticky-wrap-class'],

  properties: {
    offsetTop: {
      type: Number,
      value: 0,
      observer: 'setWrapStyle'
    },
    zIndex: {
      type: Number,
      value: 1
    },
    stickyHeight: {
      type: Number,
      value: 10,
      observer(stickyHeight) {
        this.setData({
          indexStyle: `height: ${stickyHeight}px`
        });
      }
    }
  },

  attached() {
    const {
      stickyHeight
    } = this.data;

    this.setData({
      indexStyle: `height: ${stickyHeight}px`
    });
  },

  detached() {
    this.createIntersectionObserver().disconnect();
  },

  ready() {
    this.observerContentScroll();
  },

  data: {
    indexStyle: '',
    wrapStyle: '',
    position: ''
  },

  methods: {
    observerContentScroll() {
      this.createIntersectionObserver().disconnect();

      this.createIntersectionObserver({
        thresholds: [1]
      }).relativeToViewport()
        .observe('.showcase-sticky-box', res => {
          const { intersectionRatio } = res;

          const position = intersectionRatio !== 1 ? 'top' : '';

          this.triggerEvent('scroll', {
            isFixed: position === 'top'
          });

          this.setPosition(position);
        });
    },

    setPosition(position) {
      if (position !== this.data.position) {
        this.setData({ position }, () => {
          this.setWrapStyle();
        });
      }
    },

    setWrapStyle() {
      const { offsetTop, position, zIndex } = this.data;
      let wrapStyle = '';

      switch (position) {
        case 'top':
          wrapStyle = `
            top: ${offsetTop}px;
            position: fixed;
            z-index: ${zIndex};
          `;
          break;
        case 'bottom':
          wrapStyle = `
            top: auto;
            bottom: 0;
          `;
          break;
        default:
          wrapStyle = '';
      }

      if (wrapStyle === this.data.wrapStyle) return;

      this.setData({ wrapStyle });
    },
  }
});
