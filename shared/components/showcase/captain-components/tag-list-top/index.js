import StickyControl from 'shared/utils/sticky-control';
import themeViewBehavior from 'shared/common/components/theme-view/behavior';

Component({
  type: 'tag-list-top',

  behaviors: [themeViewBehavior],

  options: {
    addGlobalClass: true
  },

  properties: {
    activeTab: {
      type: Number,
      value: 0
    },

    // tags 配置信息
    tags: {
      type: Object,
      value: {}
    },

    // 商品布局配置信息
    goodsListConfig: {
      type: Object,
      value: {}
    },

    // { kdtId, appId, offlineId, extraData, isPlugin }
    extraData: {
      type: Object,
      value: {}
    },

    // { extraParams bannerId }
    loggerParams: {
      type: Object,
      value: {}
    },

    // tabs 分组列表
    tabs: {
      type: Array,
      value: []
    }
  },

  data: {
    stickyItemName: '',
    stickyItemHeight: 44,
    tabFixedTop: 0,
    showNavStyleBorder: true,
    isFixed: false,
    stickyIndex: 0
  },

  attached() {
    this.setNavStyleBorder();
  },

  ready() {
    const stickyItemName = `TagListTop-${this.__wxExparserNodeId__}`;
    this.setData({
      stickyItemName
    });
    StickyControl.setStickyControlCheckItem(stickyItemName, 0);
    // 多个页面商品分组会影响别的组件
    StickyControl.setStickyControlSubscribe(this.handelStickyTop, this, stickyItemName);
  },

  detached() {
    const { stickyItemName } = this.data;
    StickyControl.delStickyControlSubscribe(stickyItemName);
  },

  methods: {
    setNavStyleBorder() {
      const { navStyle } = this.data.tags;
      this.setData({
        showNavStyleBorder: +navStyle !== 2
      });
    },

    handleGoodsBuy(e) {
      this.triggerEvent('buy', e.detail);
    },

    /**
     * 切换tab
     * @param e
     */
    tabChange(e) {
      this.triggerEvent('tabChange', { index: e.detail.index });
      const { tags: { isSticky = false } = {}, isFixed } = this.data;
      // 开启吸顶 并且已经吸顶 切换tab跳回顶部
      if (isSticky && isFixed) {
        // 下一个分组没请求时页面如果没高度 跳不到顶部 to do
        this.tabViewScrollToTop();
      }
    },

    /**
     * 判断相同 taglist 能否添加 用于两个相同类型吸顶组件贴的很近
     * @returns {boolean}
     */
    checkTagListStickySameName() {
      const { stickyItemName } = this.data;
      const { __stickyControlMap: stickyControlMap = {} } = StickyControl.getStickyControl();
      const itemList = Object.keys(stickyControlMap);
      const reg = /^TagListTop-.+$/;
      let check = true;
      itemList.forEach((item) => {
        if (item !== stickyItemName && reg.test(item)) {
          check = false;
          StickyControl.setStickyControlItem(item, 0, false);
        }
      });
      return check;
    },

    /**
     * 回调
     * @param type
     * @param stickyTop
     */
    handelStickyTop({ type, stickyTop = 0 }) {
      if (type === 'stickyTop' && this.checkTagListStickySameName()) {
        this.setData({
          tabFixedTop: stickyTop
        });
      }
    },

    handleTabScroll(event) {
      const { stickyItemName, stickyItemHeight } = this.data;
      let { isFixed } = event.detail;
      // z-index 顺序 修改请查看 docs/z-index.md
      const stickyIndex = isFixed ? 100 : 0;
      if (isFixed) {
        if (!StickyControl.checkStickyControlItem(stickyItemName)) {
          isFixed = false;
        }
        /**
         * 多个 taglist 时错位
         */
        this.checkTagListStickySameName();
      }

      StickyControl.setStickyControlItem(stickyItemName, stickyItemHeight, isFixed);

      const { positionTop = 0 } = StickyControl.getStickyControlItem(stickyItemName) || {};

      this.setData({
        tabFixedTop: positionTop,
        isFixed,
        stickyIndex
      });
    },

    /**
     * 获取距离顶部高度
     */
    tabViewScrollToTop() {
      wx.createSelectorQuery()
        .select('.feature-page__top-hook')
        .boundingClientRect((scrollHook) => {
          if (!scrollHook) return;
          wx.createSelectorQuery()
            .in(this)
            .select('.th_vw-t-t')
            .boundingClientRect((rect) => {
              const tabTop = scrollHook ? Math.abs(scrollHook.top) + rect.top : rect.top;
              this.scrollToTabScrollTop(tabTop);
            })
            .exec();
        })
        .exec();
    },

    /**
     * 跳转到吸顶位置
     * @param top
     */
    scrollToTabScrollTop(top) {
      const { stickyItemName } = this.data;
      const nextTop = StickyControl.getStickyControlNextTop(stickyItemName);
      // 跳到距离吸顶位置 -1 防止吸顶边值干扰
      wx.pageScrollTo({
        scrollTop: top - nextTop + 1,
        duration: 0
      });
      // setTimeout(() => {
      //   const _nextTop = StickyControl.getStickyControlNextTop(stickyItemName);
      //   console.log(nextTop, _nextTop);
      //   if (nextTop === _nextTop) return;
      //   console.log('next jump');
      //   wx.pageScrollTo({
      //     scrollTop: top - _nextTop + 1,
      //     duration: 0
      //   }, 300);
      // })
    }
  }
});
