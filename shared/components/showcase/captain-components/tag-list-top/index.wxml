<view class="th_vw-t-t th_vw-s-{{tags.navStyle}} {{ isFixed ? 'tags-tab--fixed' : 'tags-tab--normal'}}">
  <van-tabs custom-class="showcase-tags-tab__tabs" nav-class="showcase-tags-nav" tab-class="showcase-tags-nav-tab" tab-active-class="showcase-tags-nav-tab-active" wx:if="{{ tabs.length > 1 }}" z-index="{{ stickyIndex }}" sticky="{{ tags.isSticky }}" active="{{ activeTab }}" offset-top="{{ tabFixedTop }}" border="{{ showNavStyleBorder }}" color="{{ themeColor }}" type="{{ tags.navStyle == 0 ? 'line' : 'card'}}" ellipsis="{{false}}" bind:scroll="handleTabScroll" bind:change="tabChange">
    <van-tab wx:for="{{ tabs }}" wx:for-item="tab" wx:for-index="index" wx:key="unique" title="{{ tab.title }}" title-style="{{ activeTab === index ? 'color:' + themeColor: '' }}">
      <tag-item-tab tag-item="{{ tab }}" goods-list-config="{{ goodsListConfig }}" logger-params="{{ loggerParams }}" active-tab="{{ activeTab }}" extra-data="{{ extraData }}" bind:buy="handleGoodsBuy" />
    </van-tab>
  </van-tabs>
  <tag-item-tab wx:elif="{{ tabs && tabs.length }}" tag-item="{{ tabs[0] }}" goods-list-config="{{ goodsListConfig }}" logger-params="{{ loggerParams }}" active-tab="{{ activeTab }}" extra-data="{{ extraData }}" bind:buy="handleGoodsBuy" />
</view>