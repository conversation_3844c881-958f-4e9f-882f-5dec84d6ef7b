<view class="cap-tag-list-top__list">
  <showcase-goods
    bind:buy="handleGoodsBuy"
    component-data="{{ {...tagItem.goodsListConfig, goods: tagItem.dataList} }}"
    app-id="{{ appId }}"
    open-swipe-pagination="{{ false }}"
  />
  <view wx:if="{{ tagItem.nodata }}" class="taglist-top__no-item">
    暂无数据
  </view>
  <view wx:if="{{ tagItem.loading && (!isHorizontalList || tagItem.p === 0) }}" class="loadmore">
    <loadmore type="loading" />
  </view>
</view>

