import pick from '@youzan/weapp-utils/lib/pick';
import { ACTIVITY_STATUS, ACTIVITY_STATUS_BUTTON_TEXT } from './constants';
import countdownUtilBehavior from '../ump-goods-layout/behaviors/countdown-util-behavior';
import goodsLayoutBehavior from '../goods-layout/behaviors/goods-layout-behavior';

Component({
  behaviors: [countdownUtilBehavior, goodsLayoutBehavior],

  properties: {
    /**
     * 活动商品列表
     */
    list: {
      type: Array,
      value: [],
      observer(newVal) {
        // ?? ob obj
        this.setData({
          curList: this.computeGoodsLayoutList(newVal),
          infoData: this.computeInfoData()
        });
      }
    },

    showOriginPrice: {
      type: Boolean,
      value: true
    },

    /**
     * 是否展示倒计时
     * 0 不展示 1 展示
     */
    showCountDown: {
      type: Boolean,
      value: true
    },

    /**
     * 是否展示库存
     * 1 展示 0 不展示
     */
    showStockNum: {
      type: Boolean,
      value: true
    },

    pageRandomNumber: String,

    /**
     * 是否开启横向滑动的分页
     */
    openSwipePagination: Boolean,

    skeletonNumber: {
      type: Number,
      value: 0
    }
  },

  data: {
    infoData: {},
    curList: []
  },

  attached() {
    this.setData({
      infoData: this.computeInfoData(),
      curList: this.computeGoodsLayoutList(this.data.list)
    });
  },

  methods: {
    computeGoodsLayoutList(list) {
      return list.map((item, index) => {
        const { activityStatus, isSoldout = false, isCheckRight = 0 } = item;
        // 售罄状态按钮显示去看看
        const buttonStatus = isSoldout ? ACTIVITY_STATUS.END : activityStatus;
        const buttonText = ACTIVITY_STATUS_BUTTON_TEXT[buttonStatus];
        // 秒杀马上预约逻辑
        item.buttonText =
          +isCheckRight === 1 && ACTIVITY_STATUS.NOT_START === activityStatus
            ? '马上预约'
            : buttonText;
        item.itemIndex = index;
        return item;
      });
    },

    computeInfoData() {
      return pick(
        this.data,
        'showCountDown',
        'showStockNum',
        'showTitle',
        'showSubTitle',
        'showPrice',
        'showOriginPrice',
        'showBuyButton',
        'buyButtonType',
        'showSoldNum',
        'buttonText',
        'textAlignType',
        'textStyleType'
      );
    },

    // 处理秒杀状态变更
    countdownEnd({ detail }) {
      const { itemIndex } = detail;
      const { curList } = this.data;
      const goodsInfo = curList[itemIndex];
      const activityStatus = +goodsInfo.activityStatus + 1;
      const isEnd = activityStatus === ACTIVITY_STATUS.END;
      this.setData({
        [`curList[${itemIndex}].isEnd`]: isEnd,
        [`curList[${itemIndex}].activityStatus`]: activityStatus,
        [`curList[${itemIndex}].buttonText`]: ACTIVITY_STATUS_BUTTON_TEXT[activityStatus]
      });
    }
  }
});
