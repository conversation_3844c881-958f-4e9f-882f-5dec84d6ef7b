import goodsItemInfoBehavior from '../../../goods-layout/behaviors/goods-item-info-behavior';
import { LAYOUT_TYPE, GOODS_TYPE } from '../../../goods-layout/constants';
import { ACTIVITY_STATUS } from '../../constants';

Component({
  behaviors: [goodsItemInfoBehavior],

  options: {
    addGlobalClass: true
  },

  properties: {
    goodsInfo: {
      type: Object,
      value: {},
      observer() {
        this.setData({
          limitText: this.computeLimitText(),
        });
      }
    },
  },

  data: {
    textAlignStyle: '',
    limitText: '',
    containerClass: '',
  },

  attached() {
    this.setData({
      containerClass: this.computeContainerClass(),
      textAlignStyle: this.computeTextAlignStyle(),
      limitText: this.computeLimitText()
    });
  },

  methods: {
    computeEduGoods() {
      const {
        goodsInfo: { goodsType = 0 } = {},
      } = this.data;
      return +goodsType === GOODS_TYPE.KNOWLEDGE;
    },

    computeLimitText() {
      const {
        goodsInfo: { currentStock = 0, status = 0, isEnd = false, isSoldout = false } = {},
      } = this.data;
      if (isSoldout) return '已售罄';

      if (isEnd) return '已结束';

      const isEduGoods = this.computeEduGoods();

      const isNotStart = +status === ACTIVITY_STATUS.NOT_START;

      let limitText = isNotStart ? '限量' : '仅剩';

      const stockText = isEduGoods ? '名' : '件';

      if (isEduGoods) {
        limitText = isNotStart ? '限前' : '剩余';
      }
      return limitText + currentStock + stockText;
    },
    /**
     * 计算标题对齐样式
     */
    computeTextAlignStyle() {
      const {
        textAlignType = 'left',
        showTitle,
        showSubTitle
      } = this.data.infoData || {};
      const itemAlign = `text-align: ${textAlignType};`;
      const marginAlign = !showTitle && !showSubTitle ? 'margin: 10px 0 4px;' : '';
      return itemAlign + marginAlign;
    },

    computeContainerClass() {
      const { layout } = this.data;

      const result = [`${LAYOUT_TYPE[layout]}`];
      const containerClass = [result];

      return containerClass;
    }
  }
});
