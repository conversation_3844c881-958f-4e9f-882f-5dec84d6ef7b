<view class="c-seckill">
  <goods-layout
    id="cap-goods-layout"
    generic:goods-item-tag-info="goods-item-tag-info"
    generic:goods-item-price="goods-item-price"
    generic:goods-item-expand="goods-item-expand"
    generic:buy-button="buy-button"
    app-id="{{ appId }}"
    list="{{ curList }}"
    image-fill-style="{{ imageFillStyle }}"
    image-ratio="{{ imageRatio }}"
    layout="{{ layout }}"
    size-type="{{ sizeType }}"
    extra-data="{{ extraData }}"
    redirect-type="{{ redirectType }}"
    info-data="{{ infoData }}"
    show-corner-mark="{{ showCornerMark }}"
    corner-mark-type="{{ cornerMarkType }}"
    corner-mark-image="{{ cornerMarkImage }}"
    is-show-more="{{ isShowMore }}"
    more-url="{{ moreUrl }}"
    more-button-type="{{ moreButtonType }}"
    page-margin="{{ pageMargin }}"
    goods-margin="{{ goodsMargin }}"
    border-radius-type="{{ borderRadiusType }}"
    page-data="{{ pageData }}"
    open-swipe-pagination="{{ openSwipePagination }}"
    skeleton-number="{{ skeletonNumber }}"
    swipe-load-max-times="{{ 9999999 }}"
    bind:tag-info-change="countdownEnd"
    bind:item-click="handleGoodsItemClick"
    bind:buy="handleGoodsBuyClick"
    bind:load-more="handleLoadMore"
  />
</view>
