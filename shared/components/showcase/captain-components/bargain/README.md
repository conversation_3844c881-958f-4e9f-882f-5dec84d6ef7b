## UmpBargain 限时砍价活动组件

### 使用指南

在 index.json 中引入组件。

```json
{
  "usingComponents": {
    "cap-goods": "path/to/@youzan/captain-weapp/dist/ump-timelimited-discount/index"
  }
}
```

### 代码演示

#### 基础用法

```html
<cap-ump-timelimited-bargain
  goods-list="{{ goodsList }}"
  size="1"
  ratio="3,2"
  show-count-down="1"
  show-buy-btn="1"
  show-title="1"
  show-stock-num="1"
  buy-btn-type="3"
  image-fill-style="2"
  show-time-limit="1"
  activity-status="0"
/>
```


### API

| 参数       | 说明      | 类型       | 默认值       | 可选值       |
|-----------|-----------|-----------|-------------|-------------|
| goodsList | 活动商品列表 | `array`  | []  |  |
| activityStatus | 活动状态 0 未开始 1 开始中 2 已结束 | `number` |  | 0 \| 1 \| 2 |
| size | 列表展示类型 0 大图 1 小图 2 列表 | `number` |  | 0 \| 1 \| 2 |
| ratio | 主图宽高比例 | `string` | '1,1' | '1,1' \| '2,3' |
| showCountdown | 是否展示倒计时 | `number` | 1 | 0 \| 1 |
| showBuyBtn | 是否展示购买按钮 | `number` | 1 | 0 \| 1 |
| showTitle | 是否展示标题 | `number` | 1 | 0 \| 1 |
| showTimeLimit | 是否展示折扣进度条 | `number` | 1 | 0 \| 1 |
| showStockNum | 是否展示库存 | `number` | 1 | 0 \| 1 |
| buyBtnType | 按钮风格 | `number` | 0 | 0 \| 1 \| 2 \| 3 |
| imageFillStyle | 主图填充类型 | `number` | 2 | 1 \| 2 |

#### goodsList数据结构
```javascript
[
  {
    itemId: 'xxx', // 商品id
    title: 'xxxx', // 商品标题
    imageUrl: 'xxxx',// 商品图
    kdtUrl: 'xxx', //商品链接
    stockNum: 'xxx', // 库存
    discountType: 'xxx', // 折扣类型
    discountValue: 'xxx', // 折扣数值
    discountPrice: 'xxx', // 折扣价
    description: 'xxx', // 活动标签
    startAt: 'xxx', //活动开始时间
    endAt: 'xxx', // 活动结束时间
    totalSoldNum: 'xxx', // 销售量
    price: 'xxx', // 商品原价
  },
  ...
]
```
