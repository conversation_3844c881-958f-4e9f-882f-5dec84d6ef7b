import { LAYOUT_MAP } from '../goods-layout/constants';
import { ACTIVITY_STATUS } from './constants';

export function isNotStart(status) {
  return +status === ACTIVITY_STATUS.NOT_START;
}

export function getLimitText(status) {
  return isNotStart(status) ? '限量' : '仅剩';
}

export function getCountdown(goods) {
  const {
    startAt,
    endAt,
    activityStatus
  } = goods;

  let remain;

  const nowTime = (new Date()).getTime();
  if (isNotStart(activityStatus)) {
    remain = startAt - nowTime;
  } else if (activityStatus === ACTIVITY_STATUS.END) {
    remain = 0;
  } else {
    remain = endAt - nowTime;
  }

  return remain;
}

export function getCountdownText(goods, layout) {
  const isBig = +layout === LAYOUT_MAP.BIG;
  if (isNotStart(goods.activityStatus)) {
    return isBig ? '距开始还有' : '距开始';
  }

  return isBig ? '距结束仅剩' : '距结束';
}
