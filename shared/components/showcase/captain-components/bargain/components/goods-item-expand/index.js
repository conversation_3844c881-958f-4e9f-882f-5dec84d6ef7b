import goodsItemInfoBehavior from '../../../goods-layout/behaviors/goods-item-info-behavior';
import { getLimitText } from '../../helper';
import { LAYOUT_MAP, LAYOUT_TYPE } from '../../../goods-layout/constants';

Component({
  behaviors: [goodsItemInfoBehavior],

  options: {
    addGlobalClass: true
  },

  data: {
    textAlignStyle: '',
    limitText: '',
    containerClass: '',
  },

  attached() {
    const {
      goodsInfo
    } = this.data;
    this.setData({
      containerClass: this.computeContainerClass(),
      textAlignStyle: this.computeTextAlignStyle(),
      limitText: getLimitText(goodsInfo.status)
    })
  },

  methods: {
    /**
     * 计算标题对齐样式
     */
    computeTextAlignStyle() {
      const {
        textAlignType = 'left',
        showTitle,
        showSubTitle
      } = this.data.infoData || {};
      const itemAlign = `text-align: ${textAlignType};`;
      const marginAlign = !showTitle && !showSubTitle ? 'margin: 10px 0 4px;' : '';
      return itemAlign + marginAlign;
    },

    computeContainerClass() {
      const { layout } = this.data;
      return `${LAYOUT_TYPE[layout]}`;
    }
  }
});
