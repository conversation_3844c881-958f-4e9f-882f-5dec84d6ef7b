import { ACTIVITY_STATUS, ACTIVITY_STATUS_BUTTON_TEXT } from './constants';
import countdownUtilBehavior from '../ump-goods-layout/behaviors/countdown-util-behavior';
import goodsLayoutBehavior from '../goods-layout/behaviors/goods-layout-behavior';

Component({
  behaviors: [countdownUtilBehavior, goodsLayoutBehavior],

  properties: {
    /**
     * 活动商品列表
     */
    list: {
      type: Array,
      value: [],
      observer(newVal) {
        this.setData({
          // ?? ob obj
          curList: this.computeGoodsLayoutList(newVal),
          infoData: this.computeInfoData()
        });
      }
    },

    showOriginPrice: {
      type: Boolean,
      value: true
    },

    /**
     * 是否展示倒计时
     * 0 不展示 1 展示
     */
    showCountDown: {
      type: Boolean,
      value: true
    },

    /**
     * 是否展示库存
     * 1 展示 0 不展示
     */
    showStockNum: {
      type: Boolean,
      value: true
    },

    pageRandomNumber: String
  },

  data: {
    infoData: {},
    curList: []
  },

  methods: {
    computeGoodsLayoutList(list) {
      return list.map((item, index) => {
        const time = new Date().getTime();
        const { minPrice, originPrice: oPrice } = item;

        if (time < item.startAt) {
          item.status = ACTIVITY_STATUS.NOT_START;
          item.activityStatus = ACTIVITY_STATUS.NOT_START;
        } else if (item > item.endAt) {
          item.status = ACTIVITY_STATUS.END;
          item.activityStatus = ACTIVITY_STATUS.END;
        } else {
          item.status = ACTIVITY_STATUS.START;
          item.activityStatus = ACTIVITY_STATUS.START;
        }
        item.tag = '砍价';
        item.buttonText = ACTIVITY_STATUS_BUTTON_TEXT[item.status];
        item.itemIndex = index;
        item.price = (minPrice / 100).toFixed(2);
        item.originPrice = (oPrice / 100).toFixed(2);
        return item;
      });
    },

    computeInfoData() {
      const {
        showCountDown,
        showStockNum,
        showTitle,
        showSubTitle,
        showPrice,
        showOriginPrice,
        showBuyButton,
        buyButtonType,
        showSoldNum,
        buttonText,
        textAlignType,
        textStyleType
      } = this.data;
      return {
        showCountDown,
        showStockNum,
        showTitle,
        showSubTitle,
        showPrice,
        showOriginPrice,
        showBuyButton,
        buyButtonType,
        showSoldNum,
        buttonText,
        textAlignType,
        textStyleType
      };
    },

    // 处理砍价状态变更
    countdownEnd({ detail }) {
      const { itemIndex } = detail;
      const { curList } = this.data;
      const goodsInfo = curList[itemIndex];
      const activityStatus = +goodsInfo.activityStatus + 1;
      const isEnd = activityStatus === ACTIVITY_STATUS.END;
      this.setData({
        [`curList[${itemIndex}].isEnd`]: isEnd,
        [`curList[${itemIndex}].activityStatus`]: activityStatus,
        [`curList[${itemIndex}].buttonText`]: ACTIVITY_STATUS_BUTTON_TEXT[
          activityStatus
        ]
      });
    },
  }
});
