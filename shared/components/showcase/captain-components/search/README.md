## search 搜索

自定义搜索组件

### 示例代码

```wxml
  <search show="{{['icon', 'input', 'cancel']}}" bindconfirm="confirm" bindchange="change"></search>
```

### 属性与事件

| 名称             | 类型        | 是否必须 | 默认  | 描述                                                |
| ---------------- | ----------- | -------- | ----- | --------------------------------------------------- |
| keyword          | String      | 否       | 无    | 默认搜索关键字                                      |
| useCancel        | Boolean     | 否       | false | 是否显示取消按钮                                    |
| cancelText       | String      | 否       | 取消  | 取消按钮文字                                        |
| pickerWidth      | Number      | 否       | 55    | picker 组件宽度                                     |
| placeholder      | String      | 否       | 无    | 输入框占位字符串                                    |
| range            | Array       | 否       | 无    | picker 组件 range，只支持数组，数组元素只能说字符串 |
| rangeIndex       | Number      | 否       | 0     | picker 组件 value 属性                              |
| bindchange       | EventHandle | 否       | 无    | 关键字发生变化时触发                                |
| bindpickerchange | EventHandle | 否       | 无    | picker 选中值发生改变时触发                         |
| bindcancel       | EventHandle | 否       | 无    | 取消按钮点击时触发                                  |
| bindsearch       | EventHandle | 否       | 无    | 键盘点击确认时触发
| alignLeft        | Boolean     | 否       | false | placeholder 是否靠左                              |
| focus            | Boolean     | 否       | false | 是否获取焦点                                       |
| disabled         | Boolean     | 否       | false | 是否禁用                                          |
| cancelStyle      | String      | 否       | 无     | “取消”的样式                                      |
| cancelClass      | String      | 否       | 无     | “取消”的类名                                      |
| inputStyle       | String      | 否       | 无     | “输入框”的样式                                     |
| inputClass       | String      | 否       | 无     | “输入框”的类名                                    |
| searchStyle      | String      | 否       | 无     | “整个搜索”的样式                                   |
| searchClass      | String      | 否       | 无     | “整个搜索”的类名                                   |
| textColor        | String      | 否       | 无     | 字体颜色                                   |


> 当不显示`取消按钮`和`picker组件`时占位符居中显示

### 其他

- 新增 clearInput 方法，实际是触发 change 事件，值为空
- 样式的修改
- 修改居中
