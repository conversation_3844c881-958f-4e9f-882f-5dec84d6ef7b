<view class="zan-search zan-cell {{ (onlySearch && !alignLeft) ? 'center-placeholder' : ''}} {{ searchClass }}" style="{{ searchStyle }}">
  <view class="zan-cell__bd zan-search__form {{ useCancel ? 'zan-search__form--cancel' : '' }} input-class" style="max-width: auto; {{ inputStyle }}">
    <!-- style="max-width: {{ (onlySearch && !alignLeft) ? inputWidth : 'auto' }}; {{ inputStyle }}" -->
    <van-icon class="search__icon" style="{{ searchIconStyle }}" name="search" color="{{ textColor }}" />
    <picker mode="selector" range="{{ range }}" value="{{ rangeIndex }}" bindchange="pickerChange" class="zan-search__selector" style="width: {{ pickerWidth || 55 }}px" wx:if="{{ range.length > 0}}">
      <view class="picker">{{ range[rangeIndex] }}</view>
    </picker>
    <input class="zan-search__input" style="{{ (onlySearch && !alignLeft) ? 'width:' + inputWidth : ''}};{{innerInputStyle}}" placeholder="{{ placeholderText }}" placeholder-class="zan-search__placeholder" placeholder-style="color: {{ textColor }}" adjust-position="{{ adjustPosition }}" confirm-type="search" bindfocus="focus" bindblur="blur" value="{{ keyword }}" bindconfirm="search" bindinput="inputChange" focus="{{ focus }}" disabled="{{ disabled }}" />
    <icon wx:if="{{keyword}}" class="zan-search__clear" type="clear" bindtap="clearInput" size="14" color="#bbb"></icon>
  </view>
  <view wx:if="{{ useCancel }}" bindtap="cancelSearch" class="zan-search__cancel zan-cell__ft cancel-class" style="{{ cancelStyle }}">
    {{cancelText || '取消'}}
  </view>
</view>