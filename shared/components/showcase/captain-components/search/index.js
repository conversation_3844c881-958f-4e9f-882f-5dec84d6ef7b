Component({
  externalClasses: ['search-class', 'input-class', 'cancel-class'],

  properties: {
    show: {
      type: Array,
      value: ['icon', 'cancel'],
    },

    cancelText: {
      type: String,
      value: '取消',
    },

    keyword: {
      type: String,
      value: '',
    },

    pickerWidth: {
      type: Number,
      value: 55,
    },

    searchClass: String,

    placeholder: {
      type: String,
      value: '请输入查询关键字',
      observer(newVal) {
        // 非等宽字体 数字字母 大致3/4
        let inputWidth = 0;
        const _reg = /^[A-Za-z1-9]$/;
        newVal.split('').forEach((item) => {
          inputWidth += _reg.test(item) ? 10.5 : 15;
        });
        this.setData({
          // inputWidth: `${(newVal.length * 14) + 45}px`
          placeholderText: newVal,
          inputWidth: `${inputWidth}px`,
        });
      },
    },

    range: {
      type: Array,
      value: [],
    },

    rangeIndex: {
      type: Number,
      value: 0,
    },

    useCancel: {
      type: Boolean,
    },

    onlySearch: {
      type: Boolean,
      value: false,
    },

    alignLeft: {
      type: Boolean,
      value: false,
    },

    searchStyle: String,

    cancelStyle: String,

    inputStyle: {
      type: String,
      value: 'height:36px;',
    },
    innerInputStyle: String,

    focus: {
      type: Boolean,
      value: false,
    },

    disabled: {
      type: Boolean,
      value: false,
    },

    textColor: {
      type: String,
      value: '#969799',
    },

    adjustPosition: {
      type: Boolean,
      value: true,
    },

    searchIconStyle: String,

    showScan: Boolean,
  },

  data: {
    inputWidth: 'auto',
    placeholderText: '',
  },

  attached() {
    if (!this.data.useCancel && !this.data.range.length) {
      this.setData({ onlySearch: true });
    }

    setTimeout(() => {
      this.setData({
        placeholderText: this.data.placeholder,
      });
    }, 0);
  },
  methods: {
    pickerChange(e) {
      const { value } = e.detail;
      this.triggerEvent('pickerchange', {
        value,
        text: this.data.range[value],
      });
    },

    search(e) {
      this.triggerEvent('search', { value: e.detail.value });
    },

    inputChange(e) {
      this._inputvalue = e.detail.value;
      this.triggerEvent('change', { value: e.detail.value });
    },

    cancelSearch() {
      this.triggerEvent('cancel');
      this.setData({ keyword: '' });
    },

    focus() {
      if (this.data.onlySearch) {
        this.setData({ onlySearch: false });
      }
      this.triggerEvent('focus');
    },

    blur() {
      if (
        !this.data.useCancel &&
        !this.data.range.length &&
        !this._inputvalue
      ) {
        this.setData({ onlySearch: true });
      }
      this.triggerEvent('blur');
    },

    clearInput() {
      this.setData({
        focus: true,
      });
      this.triggerEvent('change', { value: '' });
    },
  },
});
