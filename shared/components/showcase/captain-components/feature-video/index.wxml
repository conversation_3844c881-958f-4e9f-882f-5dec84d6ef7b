<view class="feat-video">
  <cap-video
    class="capVideo"
    src="{{ src }}"
    poster="{{ video.displayCoverUrl }}"
    bind:play="handlePlay"
  />

  <view class="feat-video__extra-block">
    <view class="feat-video__title-block">
      <view class="feat-video__title">【{{video.title}}】</view>
      <view class="feat-video__decp">{{video.description}}</view>
    </view>

    <view class="feat-video__extra">
      <view class="feat-video__date">
        {{ video.createdAtStr }}
      </view>
      <view class="feat-video__more-goods" >
        同款好物
      </view>
    </view>

    <view class="feat-video__goods" wx:if="{{video.itemList}}">
      <goods-item
        wx:for="{{ video.itemList }}"
        wx:for-item="good"
        wx:key="id"
        good="{{good}}"
      />
    </view>
    <view
      class="feat-video__comments-block"
      wx:if="{{ comments.length }}"
    >
      <view class="feat-video__loading-mask" wx:if="{{ loadingComments }}">
        <view class="zan-loading"/>
        <view>加载中</view>
      </view>
      <scroll-view
        scroll-y
        class="feat-video__comments-scroll"
        bindscrolltolower="handleScrollComments"
      > 
        <view class="feat-video__comments">
          <comment-item
            wx:for="{{comments}}"
            wx:key="id"
            wx:for-item="commentItem"
            wx:for-index="commentIndex"
            index="{{ commentIndex }}"
            comment="{{ commentItem }}"
            bind:tapheart="handleTapHeart"
          />
        </view>
      </scroll-view>
    </view>
    {{ comment }}
    <comment-bar
      bind:addcomment="handleAddComment"
    />
  </view>
</view>
