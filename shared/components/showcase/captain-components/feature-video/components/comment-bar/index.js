Component({
  options: {
    addGlobalClass: true
  },

  data: {
    comment: ''
  },

  methods: {
    onCommentChanged(e) {
      const comment = e.detail.value;
      this.setData({
        comment
      });
    },
    handleTapCommentBtn() {
      const { comment } = this.data;
      if (!comment) {
        return;
      }
      this.triggerEvent('addcomment', comment);
      this.setData({
        comment: ''
      });
    }
  }
});
