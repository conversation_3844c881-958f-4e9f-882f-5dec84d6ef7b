Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    good: Object
  },
  data: {
    comment: ''
  },

  methods: {
    onCommentChanged(e) {
      const comment = e.detail.value;
      this.setData({
        comment
      });
    },
    handleTapCommentBtn() {
      const { comment } = this.data;
      if (!comment) {
        return;
      }
      this.triggerEvent('comment', comment);
    }
  }
});
