import Events from '@youzan/weapp-utils/lib/event';

Component({
  type: 'feature-video',

  options: {
    addGlobalClass: true
  },

  properties: {
    poster: String,
    objectFit: {
      type: String,
      value: 'contain'
    },
    src: String,
    video: Object,
    comments: Array,
    loadingComments: Boolean,
    loadingCommentsFinished: Boolean
  },

  data: {
    src: '',
    poster: '',
    countUrl: '',
    videoId: '',
    comment: ''
  },

  attached() {
    this.initEvents();
  },

  methods: {
    initEvents() {
      Events.on('pauseOtherVideo', context => {
        if (this !== context) {
          let video = this.selectComponent('.capVideo');
          video.pause();
        }
      });
    },

    handlePlay(e) {
      Events.trigger('pauseOtherVideo', this);
      this.triggerEvent('play', e);
    },

    handleAddComment(e) {
      this.triggerEvent('addcomment', e);
    },
    handleScrollComments(e) {
      if (this.data.loadingCommentsFinished) {
        return;
      }
      this.triggerEvent('loadcomments', e);
    },
    handleTapHeart(e) {
      this.triggerEvent('tapheart', e);
    }
  }
});
