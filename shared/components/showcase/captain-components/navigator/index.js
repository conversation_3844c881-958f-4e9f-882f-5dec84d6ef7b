/**
 * 统一导航方式；接口尽量和微信navigator保持一致
 * 包括小程序跳转，在线客服，分享，页面跳转
 * 使用注意：由于微信的slot的坑，slot内容并没有包含到标签内部，所以外侧的样式一般都要自己写一下。。。
 * 样式调整：可以利用custom-style和custom-class，进行一些样式调整
 * 使用方法，参考title组件
 */
import navigatorBehavior from './behavior';

Component({
  behaviors: [navigatorBehavior],

  externalClasses: ['custom-class'],

  properties: {
    // 自定义样式, 方便迁移使用
    customStyle: String
  },

  methods: {
    handleTap({ detail }) {
      this.triggerEvent('navigate', {
        ...detail,
        ...this.properties.itemInfo
      });
    },

    handleContactBack({ detail }) {
      this.triggerEvent('contactback', detail);
    }
  }
});
