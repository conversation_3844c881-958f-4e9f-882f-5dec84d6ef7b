/*
  提取出来方便其他组件直接引入
*/
export default Behavior({
  properties: {
    // otherWeapp | chat | share | link
    linkType: {
      type: Number | String,
      value: 'link',
    },

    // 小程序id
    appId: String,

    // 小程序路径，如果为空就打开首页
    path: String,

    itemInfo: {
      type: Object,
      value: {},
    },

    im: {
      type: Object,
      value: {},
    },

    useShortLink: {
      type: Number,
      value: 0,
    },

    shortLink: {
      type: String,
      value: '',
    },
  },
});
