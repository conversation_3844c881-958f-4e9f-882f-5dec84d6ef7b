<!-- 打开其他小程序 -->
<navigator
  wx:if="{{ linkType === 'otherWeapp' && useShortLink === 1 }}"
  class="custom-class"
  style="{{ customStyle }}"
  target="miniProgram"
  short-link="{{ shortLink }}"
  hover-class="none"
>
  <slot></slot>
</navigator>

<navigator
  wx:elif="{{ linkType === 'otherWeapp' && !useShortLink }}"
  class="custom-class"
  style="{{ customStyle }}"
  target="miniProgram"
  app-id="{{ appId }}"
  path="{{ path }}"
  hover-class="none"
>
  <slot></slot>
</navigator>

<!-- 客服 -->
<button
  wx:elif="{{ linkType === 'chat' }}"
  class="btn custom-class"
  style="{{ customStyle }}"
  open-type="contact"
  session-from="{{ im.sourceParam || '' }}"
  business-id="{{ im.businessId || '' }}"
  bind:contact="handleContactBack"
>
  <slot></slot>
</button>

<!-- 分享 -->
<button
  wx:elif="{{ linkType === 'share' }}"
  class="btn custom-class"
  style="{{ customStyle }}"
  open-type="share"
  bind:contact="handleContactBack"
>
  <slot></slot>
</button>

<!-- 其他 -->
<view
  wx:else
  class="custom-class"
  style="{{ customStyle }}"
  bind:tap="handleTap"
>
  <slot></slot>
</view>
