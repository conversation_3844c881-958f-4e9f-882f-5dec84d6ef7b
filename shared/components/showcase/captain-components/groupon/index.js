import pick from '@youzan/weapp-utils/lib/pick';
import countdownUtilBehavior from '../ump-goods-layout/behaviors/countdown-util-behavior';
import goodsLayoutBehavior from '../goods-layout/behaviors/goods-layout-behavior';

const ACTIVITY_STATUS = {
  NOT_START: 0,
  START: 1,
  END: 2
};

Component({
  behaviors: [countdownUtilBehavior, goodsLayoutBehavior],

  properties: {
    list: {
      type: Array,
      value: [],
      observer(newVal) {
        // ?? ob obj
        this.setInfoData();
        this.setData({
          grouponList: this.computeGrouponList(newVal)
        });
      }
    },

    showOriginPrice: {
      type: Boolean,
      value: true
    },

    showCountDown: {
      type: Boolean,
      value: false
    },

    showGrouponNum: {
      type: Boolean,
      value: true
    },

    buttonText: {
      type: String,
      value: '去开团'
    },

    showMoreButton: Boolean,

    moreUrl: String,

    openSwipePagination: Boolean,

    skeletonNumber: {
      type: Number,
      value: 0
    }
  },

  data: {
    infoData: {},
    grouponList: []
  },

  // attached() {
  //   this.setInfoData();
  //   this.setGrouponList(this.properties.list);
  // },

  methods: {
    setInfoData() {
      const infoData = pick(this.properties, [
        'textStyleType',
        'textAlignType',
        'showTitle',
        'showSubTitle',
        'showPrice',
        'showOriginPrice',
        'showCountDown',
        'showBuyButton',
        'buyButtonType',
        'buttonText'
      ]);

      this.setData({ infoData });
    },

    computeGrouponList(list) {
      return list.map((item, index) => ({
        ...item,
        itemIndex: index
      }));
    },

    handleCountDownEnd({ detail }) {
      const { itemIndex } = detail;
      const goodsInfo = this.data.grouponList[itemIndex];
      const activityStatus = +goodsInfo.activityStatus + 1;
      const isEnd = activityStatus === ACTIVITY_STATUS.END;
      this.setData({
        [`grouponList[${itemIndex}].isEnd`]: isEnd,
        [`grouponList[${itemIndex}].activityStatus`]: activityStatus
      });
    }
  }
});
