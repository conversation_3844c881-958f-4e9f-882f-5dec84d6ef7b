<view class="c-groupon">
  <goods-layout
    id="cap-goods-layout"
    generic:goods-item-tag-info="goods-item-tag-info"
    generic:goods-item-price="goods-item-price"
    generic:goods-item-corner-mark="goods-item-corner-mark"
    generic:buy-button="buy-button"
    app-id="{{ appId }}"
    list="{{ grouponList }}"
    layout="{{ layout }}"
    page-margin="{{ pageMargin }}"
    goods-margin="{{ goodsMargin }}"
    size-type="{{ sizeType }}"
    border-radius-type="{{ borderRadiusType }}"
    image-ratio="{{ imageRatio }}"
    image-fill-style="{{ imageFillStyle }}"
    text-style-type="{{ textStyleType }}"
    text-align-type="{{ textAlignType }}"
    is-show-more="{{ showMoreButton }}"
    more-url="{{ moreUrl }}"
    show-corner-mark
    info-data="{{ infoData }}"
    extra-data="{{ extraData }}"
    redirect-type="{{ redirectType }}"
    page-data="{{ pageData }}"
    page-utils="{{ pageUtils }}"
    open-swipe-pagination="{{ openSwipePagination }}"
    skeleton-number="{{ skeletonNumber }}"
    bind:tag-info-change="handleCountDownEnd"
    bind:item-click="handleGoodsItemClick"
    bind:buy="handleGoodsBuyClick"
    bind:load-more="handleLoadMore"
  />
</view>
