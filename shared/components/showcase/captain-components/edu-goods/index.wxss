.knowleadge-container {
}

.template-type1 {
  background-color: #fff;
}

.knowleadge-container .title-text {
  margin-top: 15px;
  font-size: 16px;
}

.knowleadge-container .theme-feature-column__view-all {
  margin-top: 15px;
  font-size: 12px;
  color: #666;
  display: flex;
  line-height: 15px;
}

.knowledge-goods-title {
  position: relative;
  display: flex;
  padding: 0 15px;
  flex-direction: row;
  height: 40px;
  overflow-x: hidden;
  align-items: center;
  justify-content: space-between;
}

.knowleadge-item-container {
  position: relative;
  width: 100%;
  overflow: auto;
}

.knowledge-title-groupon{
  display: flex;
}

.cap-knowleadge-wrap {
  display: flex;
  flex-flow: wrap;
}

.cap-knowleadge-item {
  width: 100%;
  position: relative;
  margin-top: 5px;
}

.knowledge-subtitle {
  color: #999;
  font-size: 12px;
  margin: 0 10px 5px 10px ;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* .template-type0 .knowledge-twoinrow .knowledge-subtitle {
  width: 70%;
} */

.knowledge-title-wrap {
  margin: 0 10px 5px 10px ;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.knowledge-title {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.knowledge-timerange text {
  margin-left: 5px;
}

.knowledge-price-wrap {
  margin: 10px ;
}

.knowledge-horizontal-layout .knowledge-price-wrap {
  margin: 0 10px 10px 10px;
}

/* .knowledge-price {
  color: #f44;
} */

.knowledge-isgroupon {
  color: #f44;
  margin-right: 5px;
  font-size: 10px;
  background-color: #ffdada;
  padding: 2px 4px;
  border-radius: 2px;
}

.knowledge-isvip {
  font-size: 12px;
  color: #B29A37;
  background-color: #FFEFAD;
  margin-left: 10px;
  padding: 2px 4px;
}

/* 大图模式 */
.knowledge-huge {
  width: 100%
}

/* 一行两个 */
.knowledge-twoinrow {
  width: 50%;
  /* width: 50%; */
}

/* 一行三个 */
.knowledge-threeinrow {
  width: 33.3%;
}

/* 一大两小 */
.knowledge-huge-twosmall-big {
  width: 100%;
}

.knowledge-huge-twosmall-big .knowledge-title {
  font-size: 16px !important;
}

.knowledge-huge-twosmall-small {
  width: 50%;
}

/* 滑动模式 */
.knowleadge-scroll-wrap {
  display: flex;
  overflow: visible;
}

.knowledge-scroll {
  min-width: 40%;
}

/* 文本样式 */
.goods-text-style0 .knowledge-price,
.goods-text-style0 .knowledge-title{
 font-weight: bold;
}