// import pick from '@youzan/weapp-utils/lib/pick';
import { node as request } from 'shared/utils/request';
import WscComponent from 'shared/common/base/wsc-component';
import componentBehavior from '../../behaviors/component-behavior';
import loggerBehavior from '../../behaviors/logger-behavior';
import { shopCanPayForKnowledge } from '../../utils/pctUtils';
import { LIST_LAYOUTS } from './constants';
import { format } from '@youzan/weapp-utils/lib/time-utils';

const app = getApp();

WscComponent({
  options: {
    addGlobalClass: true
  },

  type: 'edu-goods',

  behaviors: [componentBehavior, loggerBehavior],

  properties: {
    eduGoods: {
      type: Object,
      observer() {
        this.updateContent();
      },
      value: {},
    },
    kdtId: {
      type: String
    },
    themeColor: {
      type: String
    },
    totalGroupList: {
      type: Array,
      value: [],
    },
    currentGroup: {
      type: Number,
      observer() {
        this.updateContent();
      },
      value: 0,
    },
    finished: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    // goodsFromMode: 0, // 选择方式
    // maxNewestGoods: 6, // 最新商品个数
    goodList: [],
    goodsPadding: 10, // 商品边距
    goodsRadius: 0, // 商品边角
    goodsStyle: 0, // 商品样式
    imgSize: 0, // 图片填充
    listMode: 0, // 列表样式
    pageMargin: 15, // 页面边距
    textAlign: 1, // 文本对齐
    textStyle: 0, // 文本样式
    displayContent: [], // 线下课名称， 线下课简介，线下课标签，上课时间，价格
    classWrap: 'cap-knowleadge-item',
    isGoodsCanBuy: true,
    currentPage: 1,
    fontSizes: {
      title: 0,
      subtitle: 0,
      price: 0,
    },
    loading: false,
  },

  // attached() {
  // },

  methods: {

    onReachBottom() {
    },

    requestMoreData() {
      const { totalGroupList = [], currentGroup = 0, currentPage = 1, goodList = [] } = this.data;
      const groupData = totalGroupList[currentGroup] || {};
      this.requestData(groupData.alias, currentPage, 9).then((finalResult) => {
        const list = goodList.concat(this.getShowPrice(finalResult));
        if (!finalResult || !finalResult.length) {
          this.setYZData({
            goodList: list,
            loading: false,
            currentPage: currentPage > 1 ? currentPage - 1 : 1,
            finished: true,
          });
        } else {
          this.setYZData({ goodList: list, loading: false });
        }
      });
    },

    showPriceInfo() {
      return shopCanPayForKnowledge();
    },

    updateContent() {
      const itemData = this.data.eduGoods;
      this.setYZData({
        classWrap: `cap-knowleadge-item goods-text-style${itemData.textStyle} goods-radius${itemData.goodsRadius} ${LIST_LAYOUTS[itemData.listMode]}`,
        isGoodsCanBuy: this.showPriceInfo(),
        fontSizes: this.getFontSizes(itemData),
        themeColor: this.data.themeColor,
        currentPage: 1,
        finished: false,
        loading: true,
        ...itemData
      }, () => {
        this.getEduGoodsList();
      });
    },

    getFontSizes(itemData) {
      const { listMode = 0 } = itemData;
      let fontSizes = {
        title: 0,
        subtitle: 0,
        price: 0,
      };
      switch (listMode) {
        case 0:
          fontSizes = {
            title: 16,
            price: 16,
          };
          break;
        case 1:
        case 3:
        case 4:
          fontSizes = {
            title: 14,
            price: 14,
          };
          break;
        case 2:
        case 5:
          fontSizes = {
            title: 12,
            price: 14,
          };
          break;
        default:
          fontSizes = {
            title: 16,
            price: 16,
          };
      }
      return fontSizes;
    },

    getShowPrice(goodList) {
      // const { goodList } = this.data;
      const goodListWithPrice = [].concat(goodList);
      return goodListWithPrice.map(item => {
        // eslint-disable-next-line no-nested-ternary
        item.showPrice = item.sellerType !== 2 ? (item.price ? `¥${item.price / 100}` : '免费') : '';
        if (item.buyStatus) {
          if (item.buyStatus.isBought) {
            item.showPrice = '已购买';
          } else if (item.buyStatus.isFree) {
            item.showPrice = '免费试读';
          } else if (item.buyStatus.isFreeForVip) {
            item.showPrice = '会员免费';
          } else if (item.buyStatus.isGroupOn || item.buyStatus.isVipDiscount) {
            item.showPrice = item.buyStatus.price ? `¥${item.buyStatus.price / 100}` : '免费';
          }
        }
        return item;
      });
    },

    getGoodsFrom(type) {
      switch (type) {
        case 1:
          return 'column';
        case 2:
          return 'content';
        case 4:
          return 'live';
        case 10:
          return 'course';
        default:
          return '';
      }
    },

    getDisplayContent(type) {
      const { eduGoods = {} } = this.data;
      const { displayContent = {} } = eduGoods;
      switch (type) {
        case 1:
          return displayContent.column;
        case 2:
          return displayContent.content;
        case 4:
          return displayContent.live;
        case 10:
          return displayContent.course;
        default:
          return '';
      }
    },

    getTimeStamp(time) {
      return format(new Date(time), 'yyyy-MM-dd hh:mm');
    },

    requestData(alias, pageNumber, pageSize) {
      const { kdtId } = this.data;
      const buyerId = app.getBuyerId();
      return request({
        path: 'wscshop/edu/course/group/findItemGroupPageForWym.json',
        data: {
          kdtId,
          pageNumber: pageNumber || 1,
          buyerId,
          alias: alias || '',
          pageSize: pageSize || 9,
        },
      }).then(data => {
        if (data && data.content) {
          const finalResult = data.content.map((item) => {
            item.goodsFrom = this.getGoodsFrom(item.type);
            item.kdtId = kdtId;
            item.displayContent = this.getDisplayContent(item.type);
            if (item.liveStartAt) {
              item.liveStartAt = this.getTimeStamp(item.liveStartAt);
            }
            if (item.courseStartAt) {
              item.courseStartAt = this.getTimeStamp(item.courseStartAt);
            }
            if (item.courseEndAt) {
              item.courseEndAt = this.getTimeStamp(item.courseEndAt);
            }
            return item;
          });
          return finalResult;
        }
        return [];
      });
    },

    getEduGoodsList() {
      const { totalGroupList = [], currentGroup = 0 } = this.data;
      const groupData = totalGroupList[currentGroup] || {};
      if (totalGroupList.length && !groupData.alias) {
        this.requestData('', 1, 9).then((finalResult) => {
          this.setYZData({ goodList: this.getShowPrice(finalResult), loading: false });
        });
      } else {
        const pageSize = groupData.displayType ? 9 : groupData.displayNumber;
        this.requestData(groupData.alias, 1, pageSize).then((finalResult) => {
          this.setYZData({ goodList: this.getShowPrice(finalResult), loading: false });
        });
      }
    },

    handleLoadMore() {
      const { totalGroupList = [], currentGroup = 0, currentPage = 1 } = this.data;
      const groupData = totalGroupList[currentGroup] || {};
      if (((totalGroupList.length && !groupData.alias) || groupData.displayType)) {
        this.setYZData({
          currentPage: currentPage + 1,
          loading: true,
          finished: false,
        }, () => {
          this.requestMoreData();
        });
      } else {
        this.setYZData({
          finished: true,
        });
      }
    }
  },
});
