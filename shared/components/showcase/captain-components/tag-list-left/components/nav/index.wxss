.nav-container {
  max-height: 100vh;
}

.scroll-nav {
  max-height: 100vh;
}

.scroll-nav::-webkit-scrollbar {
  height: 0;
  width: 0;
  color: transparent;
}

.scroll-nav.-fixed {
  position: fixed;
  left: 0;
  top: 0;
}

.scroll-nav.-bottom {
  position: absolute;
  left: 0;
  bottom: 0;
}

.nav-tab {
  font-size: 14px;
  line-height: 20px;
  box-sizing: border-box;
  color: #646566;
  background: #f8f8f8;
  word-break: break-all;
  overflow: hidden;
  border-left: 4px solid transparent;
  padding: 16px 8px 16px 16px;
}

.nav-tab__active {
  color: #323233;
  font-weight: 700;
  border-color: #f44;
  background: #fff;
}
