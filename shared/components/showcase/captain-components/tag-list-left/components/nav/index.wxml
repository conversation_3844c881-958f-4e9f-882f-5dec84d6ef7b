<view class="nav-container" style="width: {{ navWidth }}px">
  <scroll-view
    scroll-y
    class="scroll-nav theme-taglist-left"
    scroll-into-view="{{ 'NAV' + selectedGroup }}"
    bindscroll="handleScroll">
    <view
      class="nav-tab {{ selectedGroup === item.alias ? 'nav-tab__active' : '' }}"
      style="border-color: {{ selectedGroup === item.alias ? themeColor : 'transparent'}}"
      wx:for="{{ navList }}"
      wx:for-index="navIndex"
      wx:key="alias"
      data-alias="{{ item.alias }}"
      bindtap="handleChange"
      id="{{ 'NAV' + item.alias }}"
    >
      {{ item.title }}
    </view>
  </scroll-view>
</view>
