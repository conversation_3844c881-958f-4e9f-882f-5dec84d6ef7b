import themeViewBehavior from 'shared/common/components/theme-view/behavior';

Component({
  type: 'tag-nav',

  behaviors: [themeViewBehavior],

  options: {
    addGlobalClass: true
  },

  properties: {
    navWidth: {
      type: Number,
      value: 88
    },

    selectedGroup: {
      type: String,
      value: ''
    },

    navList: {
      type: Array,
      value: []
    }
  },

  methods: {
    handleScroll(e) {
      this.triggerEvent('handleScroll', e.detail);
    },

    handleChange(e) {
      if (!e || !e.currentTarget) return;
      const { alias } = e.currentTarget.dataset;
      this.triggerEvent('handleChange', { alias });
    }
  }
});
