<view class="group-container">
  <scroll-view
    class="group-container"
    scroll-y
    scroll-top="{{ contentScrollTop }}"
    scroll-with-animation="{{ true }}"
    bindscroll="handleScroll"
    bindtouchstart="handleTouchStart"
  >
    <view
      wx:for="{{ groupList }}"
      wx:for-index="navIndex"
      wx:for-item="group"
      wx:key="alias"
      id="{{ 'GROUP' + group.alias }}"
      style="{{'height:' + group.tagHeight + 'px' }}"
    >
      <view class="group-title" style="padding: 0 {{ pageMargin }}px;">{{ group.title }}</view>
      <block wx:if="{{ group.number > 0 }}">
        <block
          wx:for="{{ group.dataList }}"
          wx:for-index="idx"
          wx:for-item="groupItem"
          wx:key="alias"
        >
          <group-item
            bind:buy="handleGoodsBuy"
            show-title="{{ showTitle }}"
            show-sub-title="{{ showSubTitle }}"
            show-price="{{ showPrice }}"
            image-fill-mode="{{ imageFillMode }}"
            page-margin="{{ pageMargin }}"
            goods-margin="{{ goodsMargin }}"
            text-weight="{{ textWeight }}"
            border-circle="{{ borderCircle }}"
            show-buy-btn="{{ showBuyBtn }}"
            buy-btn-type="{{ buyBtnType }}"
            button-text="{{ buttonText }}"
            layout-item-style="{{ layoutItemStyle }}"
            group-item="{{ groupItem }}">
          </group-item>
        </block>

      </block>

      <view wx:elif="{{ group.isPreview }}" class="group-empty">
        <loadmore type="loading" />
      </view>

      <view wx:else class="group-empty">
        此类下暂时没有商品
      </view>

    </view>
  </scroll-view>
</view>

