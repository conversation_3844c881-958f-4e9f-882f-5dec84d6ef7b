Component({
  type: 'tag-group',

  options: {
    addGlobalClass: true
  },

  properties: {
    contentScrollTop: {
      type: Number,
      value: 0
    },
    groupList: {
      type: Array,
      value: []
    },

    goodsListConfig: {
      type: Object,
      value: {}
    }
  },

  data: {
    showTitle: true,
    showSubTitle: true,
    showPrice: true,
    imageFillMode: 'aspectFit',
    pageMargin: 12,
    goodsMargin: 16,
    textWeight: true,
    borderCircle: false,
    showBuyBtn: true,
    buyBtnType: 1,
    buttonText: '',
    layoutItemStyle: ''
  },

  attached() {
    this.initGoodsLayoutConfig();
  },

  methods: {
    /**
     * 商品布局
     */
    initGoodsLayoutConfig() {
      const { goodsListConfig } = this.data;
      const {
        showTitle,
        showSubTitle,
        showPrice,
        showBuyButton: showBuyBtn,
        buyButtonType: buyBtnType,
        imageFillStyle,
        pageMargin,
        goodsMargin,
        borderRadiusType,
        buttonText,
        textStyleType
      } = goodsListConfig;

      this.setData({
        showTitle,
        showSubTitle,
        showPrice,
        showBuyBtn,
        buyBtnType,
        buttonText,
        imageFillMode: +imageFillStyle === 1 ? 'aspectFill' : 'aspectFit',
        pageMargin: +pageMargin,
        goodsMargin: +goodsMargin / 2,
        textWeight: +textStyleType === 2,
        borderCircle: +borderRadiusType === 2,
        layoutItemStyle: `padding: ${+goodsMargin / 2}px ${+pageMargin}px;`
      });
    },
    /**
     * 购买
     * @param e
     */
    handleGoodsBuy(e) {
      this.triggerEvent('buy', e.detail);
    },
    /**
     * 滚动
     * @param e
     */
    handleScroll(e) {
      this.triggerEvent('handleScroll', e.detail);
    },

    /**
     * 右侧 滚动
     */
    handleTouchStart() {
      this.triggerEvent('handleTouchStart');
    }
  }
});
