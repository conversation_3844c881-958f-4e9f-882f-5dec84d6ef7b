<view style="{{ layoutItemStyle }}" catch:tap="handleGoodsItemClick">
  <view class="goods-item">
    <view class="goods-item__img-wp goods-item__img-holder {{ imageInfoClass }}">
      <image wx:if="{{ groupItem.image_url }}" src="{{ groupItem.image_url }}" mode="{{ imageFillMode }}" class="goods-item__img" />
    </view>
    <view class="goods-item-content-wp">
      <view class="goods-item__title-wp" wx:if="{{ showTitle }}">
        <view wx:if="{{ groupItem.title }}" class="goods-item__title {{ titleInfoClass }}">
          {{ groupItem.title }}
        </view>
        <view wx:else class="goods-item__title-holder" />
      </view>
      <view class="goods-item__sub-title-wp" wx:if="{{ showSubTitle && groupItem.sub_title }}">
        <view class="goods-item__sub-title">{{ groupItem.sub_title }}</view>
      </view>
      <view class="goods-item__sub-title-wp" wx:if="{{ isWorkProcess }}">
        <view class="goods-item__retail-stock">{{ retailStockSkuInfoCount }}</view>
      </view>
      <view class="goods-item__info-price">
        <view class="goods-item__price-wp" wx:if="{{ showPrice }}">
        <theme-view wx:if="{{ groupItem.price }}" custom-class="goods-item__price" color="general">
          <text class="goods-item__price-tag">￥</text>
          {{ goodsPrice[0] }}
          <text class="goods-item__price-decimal" wx:if="{{goodsPrice[1]}}">{{goodsPrice[1]}}</text>
        </theme-view>
        <view wx:else class="goods-item__price-holder" />
      </view>
      <view>
        <buy-button wx:if="{{ showBuyBtn && groupItem.alias }}" class="goods-item__cart-wp" type="{{ buyBtnType }}" redirect-type="1" alias="{{ groupItem.alias }}" button-text="{{ buttonText }}" button-size="small" btn-wrap-class="goods__btn-wrap" bind:buy="handleGoodsBuy" />
      <view>
      </view>
      <goods-item-extra goodsInfo="{{ groupItem }}"/>
    </view>
  </view>
  <view wx:if="{{ isWorkProcess && showRetailPrepare }}" class="goods-item__work">
    <view class="goods-item__work-time">{{ extensionSkuCount }}</view>
    <view class="goods-item__work-content">
      <view class="goods-item__work-content-step">
        <block wx:for="{{ process }}" wx:for-index="idx" wx:for-item="item" wx:key="order">
          <view class="{{ item.current == 1 ? 'goods-item__work-content-step--active' : 'goods-item__work-content-step-msg' }}">
            {{item.name}}
          </view>
          <van-icon wx:if="{{ idx !== process.length - 1 }}" custom-class="goods-item__work-content-step-icon" name="arrow" />
        </block>
      </view>
    </view>
  </view>
</view>