import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import { moment } from '@youzan/weapp-utils/lib/time';
import appLoggerBehavior from 'shared/components/showcase/behaviors/app-logger-behavior';
import navigate from 'shared/utils/navigate';

Component({
  type: 'group-good',

  behaviors: [appLoggerBehavior],

  options: {
    addGlobalClass: true
  },

  properties: {
    groupItem: {
      type: Object,
      value: {},
      observe() {
        this.initPriceInfo();
        this.initExtension();
      }
    },

    showTitle: {
      type: Boolean,
      value: true
    },

    showSubTitle: {
      type: Boolean,
      value: true
    },

    showPrice: {
      type: Boolean,
      value: true
    },

    imageFillMode: {
      type: String,
      value: 'aspectFit'
    },

    pageMargin: {
      type: Number,
      value: 15
    },

    goodsMargin: {
      type: Number,
      value: 20
    },

    textWeight: {
      type: Boolean,
      value: true
    },
    borderCircle: {
      type: Boolean,
      value: false
    },
    showBuyBtn: {
      type: Boolean,
      value: true
    },
    buyBtnType: {
      type: Number,
      value: 1
    },
    buttonText: String,
    layoutItemStyle: String
  },

  data: {
    goodsPrice: null,
    imageInfoClass: '',
    titleInfoClass: '',
    showRetailPrepare: false, // 是否展示 剩余出炉库存
    isWorkProcess: false, // 是否展示工序
    process: [], // 工序步骤
    extensionSkuCount: '', // 目前预计出炉
    retailStockSkuInfoCount: '' // 剩余出炉库存
  },

  attached() {
    this.initPriceInfo();
    this.initExtension();
  },

  methods: {
    initExtension() {
      const {
        groupItem: {
          extension = null,
          isWorkProcess = false,
          ability_mark_code_list: abilityMarkCodeList = []
        }
      } = this.data;
      if (!isWorkProcess) return;
      try {
        const {
          retail_prepare: retailPrepare = {},
          retail_stock_sku_info: retailStockSkuInfo
        } = extension;
        const {
          process = [],
          business_end_time: businessEndTime = '',
          sku_amount_str: skuAmountStr = ''
        } = retailPrepare;
        const { sell_stock_num_str: sellStockNum = 0 } = retailStockSkuInfo;
        // 10014 10015 能力标显示
        const showSkuInfoCount =
          abilityMarkCodeList.indexOf(10014) > -1 && abilityMarkCodeList.indexOf(10015) > -1;
        this.setData({
          showRetailPrepare: !isEmpty(retailPrepare),
          isWorkProcess,
          process,
          extensionSkuCount: this.getExtensionSkuCount(
            businessEndTime,
            Number.parseInt(skuAmountStr, 10)
          ),
          retailStockSkuInfoCount: showSkuInfoCount
            ? `新出炉剩余${Number.parseInt(sellStockNum, 10)}份`
            : ''
        });
      } catch (e) {
        // error
      }
    },

    getExtensionSkuCount(end, count) {
      const timeInterval = new Date(end).getTime() - new Date().getTime();
      // 小于当前时间
      if (timeInterval < 0) return `即将出炉 ${count} 份`;
      // 当前时间
      const nowHour = new Date().getHours();
      // 间隔时间
      const hoursInterval = timeInterval / 1000 / 60 / 60;
      // 间隔天数
      const day = Math.floor(hoursInterval / 24);
      // 额外小时
      const modHours = Math.ceil(hoursInterval % 24);

      // 0 - 23 个小时
      if (day === 0) {
        return `${nowHour + modHours > 24 ? '预计明天' : '预计'}${moment(
          end,
          'HH:mm'
        )}出炉 ${count} 份`;
      }

      // 24 - 47 个小时
      if (day === 1) {
        return nowHour + modHours > 24 ? '' : `预计明天${moment(end, 'HH:mm')}出炉 ${count} 份`;
      }

      // 超过 2 天
      return '';
    },

    initPriceInfo() {
      this.setData({
        goodsPrice: this.computeSalePrice(),
        imageInfoClass: this.computeImageInfoClass(),
        titleInfoClass: this.computeTitleInfoClass(),
      });
    },

    computeSalePrice() {
      const { groupItem = {} } = this.data;
      const { price = '' } = groupItem;
      const goodsPrice = (price + '').replace(/\.?0+$/, '').split('.');
      goodsPrice[1] && (goodsPrice[1] = '.' + goodsPrice[1]);

      return goodsPrice;
    },

    computeImageInfoClass() {
      const { borderCircle = false, groupItem = {} } = this.data;
      const { sold_status: soldStatus } = groupItem;
      const classArr = [];
      if (borderCircle) classArr.push('goods-item__img-wp--circle');

      if (soldStatus == '2') classArr.push('goods-item__img-holder--sold-out');

      return classArr;
    },

    computeTitleInfoClass() {
      const { textWeight = true } = this.data;

      return textWeight ? 'goods-item__title--weight' : '';
    },

    handleGoodsItemClick() {
      const {
        groupItem: { loggerParams = {}, url }
      } = this.data;

      if (!isEmpty(loggerParams)) {
        this.ensureAppLogger('open_goods', loggerParams);
      }
      navigate.navigate({
        url
      });
    },

    handleGoodsBuy() {
      const { groupItem } = this.data;
      const { loggerParams = {} } = groupItem;
      if (!isEmpty(loggerParams)) {
        this.ensureAppLogger('click_buy', loggerParams);
      }
      this.triggerEvent('buy', groupItem);
    }
  }
});
