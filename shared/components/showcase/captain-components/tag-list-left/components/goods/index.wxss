.goods-item {
  position: relative;
  display: flex;
  height: 108px;
  background: #fff;
}

.goods-item__img-wp {
  position: relative;
  width: 108px;
  height: 108px;
}

.goods-item__img-wp--circle {
  overflow: hidden;
  border-radius: 4px;
}

.goods-item__img-holder {
  background: #f8f8f8;
}

.goods-item__img {
  width: 100%;
  height: 100%;
}

.goods-item__img-holder--sold-out::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  background-image: url(https://img01.yzcdn.cn/weapp/wsc/RbrTwN.png);
  background-color: rgba(0, 0, 0, 0.3);
  background-position: center;
  background-size: 45px 45px;
  background-repeat: no-repeat;
}

.goods-item-content-wp {
  position: relative;
  padding-left: 10px;
  flex: 1;
}

.goods-item__title {
  font-size: 14px;
  line-height: 20px;
  max-height: 36px;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-item__sub-title {
  font-size: 12px;
  line-height: 16px;
  color: #999;
  height: 16px;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.goods-item__retail-stock {
  font-size: 12px;
  line-height: 14px;
  color: #faab0c;
  height: 14px;
  margin-top: 5px;
}

.goods-item__title--weight {
  font-weight: bold;
}

.goods-item__title-holder {
  width: 100%;
  height: 20px;
  background: #f8f8f8;
}

.goods-item__price-wp {
  /* position: absolute; */
  /* bottom: 0px; */
}

.goods-item__info-price{
  position: absolute;
  width: 100%;
  bottom: 0px;

}

.goods-item__price {
  color: #f44;
  font-size: 18px;
  height: 20px;
  line-height: 20px;
  width: 100%;
}

.goods-item__price-tag {
  font-size: 12px;
  margin-right: 2px;
}
.goods-item__price-decimal {
  font-size: 12px;
}

.goods-item__price-holder {
  width: 120px;
  height: 24px;
  background: #f8f8f8;
}

.goods-item__cart-wp {
  display: inline-block;
  position: absolute;
  font-size: 0;
  right: 0;
  top: 0px;
}

.goods-item__cart-wp__disabled {
  color: #f6f6f6;
}

.goods-item__work {
  position: relative;
  display: flex;
  height: 24px;
  background: #fff;
  font-size: 12px;
}

.goods-item__work-time {
  position: relative;
  margin-top: 2px;
  width: 100px;
  line-height: 14px;
  color: #999;
}

.goods-item__work-content {
  position: relative;
  padding-left: 10px;
  flex: 1;
}

.goods-item__work-content-step {
  position: absolute;
  bottom: 0;
  left: 10px;
  display: flex;
  align-items: center;
  color: #999;
  font-size: 12px;
}

.goods-item__work-content-step-msg {
  font-size: 12px;
  line-height: 14px;
}

.goods-item__work-content-step--active {
  color: #323232;
  font-weight: bold;
  animation: active-color 1s linear infinite;
}

@keyframes active-color {
  0% {
    color: #323232;
  }
  100% {
    color: #999999;
  }
}

.goods-item__work-content-step-icon {
  color: #c8c9cc;
  display: block !important;
}

