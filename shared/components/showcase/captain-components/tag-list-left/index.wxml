<view class="cap-tag-list-left th_vw-buy">
  <view class="tag-list-left theme-tag-list-left {{ isFixed ? 'tag-list-left--fixed' : ''}}">
    <view class="tag-list-left__nav">
      <tag-nav
        nav-width="{{ navWidth }}"
        selected-group="{{ selectedGroup }}"
        nav-list="{{ tags }}"
        bind:handleScroll="handleNavScroll"
        bind:handleChange="handleNavChange"
      />
    </view>
    <view class="tag-list-left__group">
      <tag-group
        content-scroll-top="{{ contentScrollTop }}"
        group-list="{{ tags }}"
        goods-list-config="{{ goodsListConfig }}"
        bind:handleScroll="handleGoodsListScroll"
        bind:handleTouchStart="handleGoodsListTouchStart"
        bind:buy="handleGoodsBuy"
        generic:group-item="group-item"
      />
    </view>
  </view>
</view>


