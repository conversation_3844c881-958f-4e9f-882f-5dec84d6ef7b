import throttle from '@youzan/weapp-utils/lib/throttle';
import getSystemInfo from 'shared/utils/browser/system-info';
// import StickyControl from 'shared/utils/sticky-control';
import Event from '@youzan/weapp-utils/lib/event';
import constants from './constants';
import lifetimesBehavior from '../../behaviors/page-lifetimes-behavior';

function createScrollCheckFunc() {
  let { scrollCheckFunc } = this;

  if (scrollCheckFunc) return;

  scrollCheckFunc = throttle((deltaY) => {
    const { notNeedScrollToTop } = this.data;
    // 真机 滚动限制 暂不生效
    if (notNeedScrollToTop) return;
    if (Math.abs(deltaY) < 20) {
      return;
    }
    this.checkScroll2Top();
  }, 300);

  this.scrollCheckFunc = scrollCheckFunc;
}

function createScrollCheckLoadFunc() {
  let { scrollCheckLoadFunc } = this;

  if (scrollCheckLoadFunc) return;

  scrollCheckLoadFunc = throttle((ev) => {
    this.focusTabOnScroll(ev);
  }, 300);

  this.scrollCheckLoadFunc = scrollCheckLoadFunc;
}

const { LOAD_OFFSET } = constants;

Component({
  type: 'tag-list-left',

  options: {
    addGlobalClass: true
  },

  behaviors: [lifetimesBehavior],

  properties: {
    tags: {
      type: Array,
      value: []
    },

    goodsListConfig: {
      type: Object,
      value: {}
    },

    pageLifetimes: {
      type: Array,
      value: []
    }
  },

  data: {
    /**
     * group 滚动时能否激活tab
     */
    shouldCheck: false,
    /**
     * 当前激活的 tab
     */
    selectedGroup: '',
    /**
     * nav 宽度
     */
    navWidth: 88,
    /**
     * group 滚动高度
     */
    contentScrollTop: 0,
    /**
     * tagListTop 文档高度
     */
    tagListLeftPositionTop: null,
    /**
     * 是否获取过 tagListTop 文档高度
     */
    hasGetTagListLeftPositionTop: false,
    /**
     * 是否需要滚动至顶部
     */
    notNeedScrollToTop: false,
    /**
     * 点击 tab 时是否触发固定顶部
     */
    shouldCheckScrollTop: false,
    isFixed: false
  },

  attached() {
    const { tags } = this.data;
    const selectedGroup = (tags[0] || {}).alias;
    this.setData({
      selectedGroup
    });
    Event.once('tags-list:success', () => {
      // 全部设置完成 重新 check 下
      this.loadCheck();
    });
  },

  ready() {
    // this.scrollViewObserve();
    this.loadCheck();
  },

  methods: {
    initSelectedGroup() {
      const { tags } = this.data;
      const selectedGroup = (tags[0] || {}).alias;
      // 下拉初始化第一个tab
      this.setData(
        {
          selectedGroup,
          contentScrollTop: 0,
          shouldCheck: false,
          shouldCheckScrollTop: false
        },
        () => {
          this.loadCheck();
        }
      );
    },
    /**
     * 滚动触发
     */
    onPageScroll() {
      this.setScrollStatus(false);
    },

    /**
     * 下拉跟新加载数据
     */
    onPullDownRefresh() {
      if (!this.canPullDownRefresh) {
        this.canPullDownRefresh = true;
        return;
      }
      this.initSelectedGroup();
    },

    /**
     * 设置高度获取状态
     * @param status
     */
    setScrollStatus(status) {
      const { notNeedScrollToTop } = this.data;
      if (notNeedScrollToTop === status) return;
      this.setData({
        isFixed: status,
        notNeedScrollToTop: status,
        hasGetTagListLeftPositionTop: status
      });
    },

    handleGoodsBuy(e) {
      this.triggerEvent('buy', e.detail);
    },

    /**
     * 滚动至可视区域时 加载商品
     */
    scrollViewObserve() {
      wx.createIntersectionObserver(this)
        .relativeToViewport({ bottom: 100 })
        .observe('.tag-list-left', (result) => {
          const isFixed = result.intersectionRatio > 0;
          isFixed && this.loadCheck();
        });
    },

    /**
     * 滚动超过一定阀值 滚动到顶部
     * 考虑加个截流
     * @param deltaY
     */
    handleNavScroll({ detail: { deltaY } }) {
      const { scrollCheckFunc } = this;
      if (!scrollCheckFunc) {
        createScrollCheckFunc.call(this);
      } else {
        scrollCheckFunc(deltaY);
      }
    },

    /**
     * 左侧导航点击切换
     * @param e
     */
    handleNavChange({ detail: { alias } }) {
      this.setData({
        selectedGroup: alias,
        shouldCheck: false,
        shouldCheckScrollTop: true
      });
      this.scrollToTargetGroup(alias);
    },

    /**
     * nav 切换 group 定位
     * @param alias
     */
    scrollToTargetGroup(alias) {
      const { tags } = this.data;
      const targetGroupData = tags.find((item) => item.alias === alias);
      if (!targetGroupData) return;
      const scrollTop = targetGroupData.range[0];
      this.setData(
        {
          contentScrollTop: scrollTop
        },
        () => {
          this.loadCheck(scrollTop);
        }
      );
    },

    /**
     * group 滚动过程中 focus 到对应 nav
     * @param { Object }ev
     */
    focusTabOnScroll(ev) {
      const {
        detail: { scrollTop }
      } = ev;

      const { tags, selectedGroup, shouldCheck, shouldCheckScrollTop } = this.data;

      // 点击tab 时只加载对应tab 不触发滚动加载逻辑 但触发吸顶
      if (!shouldCheck) {
        // 限制下拉刷新时 固定顶部
        if (shouldCheckScrollTop) {
          this.handleNavScroll(ev);
        }
        return;
      }

      // 加载数据
      this.loadCheck(scrollTop);

      // 寻找要激活的 tab
      const focusTab = tags.find((item) => {
        const [start, end] = item.range;
        return scrollTop >= start && scrollTop < end;
      });
      if (focusTab && focusTab.alias !== selectedGroup) {
        this.setData({
          selectedGroup: focusTab.alias
        });
      }
      this.handleNavScroll(ev);
    },

    /**
     * group 滚动事件 增加 throttle
     * @param { Object }ev
     */
    handleGoodsListScroll(ev) {
      const { scrollCheckLoadFunc } = this;
      if (!scrollCheckLoadFunc) {
        createScrollCheckLoadFunc.call(this);
      } else {
        scrollCheckLoadFunc(ev);
      }
    },

    /**
     * group 开始滚动 可以激活 滚动时切换tab
     */
    handleGoodsListTouchStart() {
      this.setData({
        shouldCheck: true
      });
    },

    /**
     * 固定到顶部
     */
    checkScroll2Top() {
      const { tagListLeftPositionTop, hasGetTagListLeftPositionTop } = this.data;
      if (hasGetTagListLeftPositionTop) {
        this.scroll2Top(tagListLeftPositionTop);
      } else {
        this.getTagListLeftPositionTop();
      }
    },

    /**
     * 获取商品分组文档流位置
     */
    getTagListLeftPositionTop() {
      wx.createSelectorQuery()
        .select('.feature-page__top-hook')
        .boundingClientRect((scrollHook) => {
          if (!scrollHook) return;
          wx.createSelectorQuery()
            .in(this)
            .select('.tag-list-left')
            .fields(
              {
                size: true,
                rect: true,
                scrollOffset: true
              },
              (taglist) => {
                if (!taglist) {
                  return;
                }
                const tagListLeftPositionTop = Math.abs(scrollHook.top) + taglist.top;
                this.setData({
                  tagListLeftPositionTop
                });
                this.scroll2Top(tagListLeftPositionTop);
                this.setScrollStatus(true);
              }
            )
            .exec();
        })
        .exec();
    },

    /**
     * 执行跳转
     */
    scroll2Top() {
      // const nextTop = StickyControl.getStickyControlNextTop('TagListLeft');
      // 设置吸顶
      this.setData({
        isFixed: true
      });
      wx.pageScrollTo({
        // scrollTop: top,
        duration: 0
      });
    },

    loadCheck(scrollTop = 0) {
      // 可用屏幕高度
      const MAX_HEIGHT = getSystemInfo().windowHeight;
      const { tags } = this.data;
      // 获取需要查询数据的分组
      const loadTabList = tags.filter((item) => {
        if (item.isPreview) return false;
        let [start, end] = item.range;
        let inLoadingArea = true;
        const groupTop = start - scrollTop;
        const groupBottom = end - scrollTop;
        if (groupBottom < -LOAD_OFFSET || groupTop > LOAD_OFFSET + MAX_HEIGHT) {
          inLoadingArea = false;
        }
        return inLoadingArea && !item.loaded && !item.loading && !item.nodata && item.number;
      });
      // 查询数据
      this.queryData(loadTabList);
    },

    queryData(loadTabList) {
      loadTabList.forEach((item) => this.fetchGoodsByAlias(item.alias));
    },

    fetchGoodsByAlias(alias) {
      this.triggerEvent('openLoadAlias', {
        alias
      });
    }
  }
});
