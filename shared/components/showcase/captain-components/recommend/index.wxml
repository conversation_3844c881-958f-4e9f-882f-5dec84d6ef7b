<view class="c-goods-recommend">
  <goods-layout
    id="cap-goods-layout"
    generic:goods-item-tag-info="goods-item-tag-info"
    generic:goods-item-price="goods-item-price"
    generic:goods-item-corner-mark="goods-item-corner-mark"
    generic:item-extra="item-extra"
    generic:goods-item-ump-tags="goods-item-ump-tags"
    generic:goods-item-extra="goods-item-extra"
    app-id="{{ appId }}"
    list="{{ curList }}"
    image-fill-style="{{ imageFillStyle }}"
    image-ratio="{{ imageRatio }}"
    layout="{{ layout }}"
    size-type="{{ sizeType }}"
    extra-data="{{ extraData }}"
    redirect-type="{{ redirectType }}"
    info-data="{{ infoData }}"
    show-ump-tags="{{ showUmpTags }}"
    ump-tags-max-count="{{ umpTagsMaxCount }}"
    show-corner-mark="{{ showCornerMark }}"
    corner-mark-type="{{ cornerMarkType }}"
    corner-mark-image="{{ cornerMarkImage }}"
    is-show-more="{{ isShowMore }}"
    more-url="{{ moreUrl }}"
    more-button-type="{{ moreButtonType }}"
    page-margin="{{ pageMargin }}"
    goods-margin="{{ goodsMargin }}"
    border-radius-type="{{ borderRadiusType }}"
    open-swipe-pagination="{{ openSwipePagination }}"
    show-last-self-define-item="{{ showLastSelfDefineItem }}"
    height-style="{{ heightStyle }}"
    skeleton-number="{{ skeletonNumber }}"
    bind:item-click="handleGoodsItemClick"
    bind:buy="handleGoodsBuyClick"
    bind:load-more="handleLoadMore"
  >
    <view slot="last-item-in-list">
      <slot name="last-item-in-list"/>
    </view>
  </goods-layout>
</view>
