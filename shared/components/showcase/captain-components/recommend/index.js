import pick from '@youzan/weapp-utils/lib/pick';
import goodsLayoutBehavior from '../goods-layout/behaviors/goods-layout-behavior';
import countdownUtilBehavior from '../ump-goods-layout/behaviors/countdown-util-behavior';

Component({
  type: 'goods',

  options: {
    multipleSlots: true,
  },

  behaviors: [countdownUtilBehavior, goodsLayoutBehavior],

  properties: {
    /**
     * 商品列表数据
     */
    list: {
      type: Array,
      value: [],
      observer(newVal) {
        this.setData({
          // ?? ob obj
          curList: this.computeGoodsLayoutList(newVal),
        });
      },
    },

    skeletonNumber: {
      type: Number,
      value: 0,
    },

    /**
     * 是否展示销量
     */
    showSoldNum: {
      type: Boolean,
      value: false,
      observer(newVal) {
        this.computeInfoItemData('showSoldNum', newVal);
      },
    },

    /**
     * 是否展示划线价
     */
    showOriginPrice: Boolean,

    /**
     * 是否展示 `查看更多` 按钮
     */
    isShowMore: Boolean,

    /**
     * `查看更多` 链接
     */
    moreUrl: String,

    /**
     * `查看更多` 按钮类型
     */
    moreButtonType: {
      type: Number,
      value: 1,
    },

    /**
     * 是否是在插件里
     */
    isPlugin: Boolean,

    /**
     * 是否开启横向滑动的分页
     */
    openSwipePagination: Boolean,

    heightStyle: {
      type: String,
      value: '',
    },
  },

  data: {
    curList: [],
    infoData: {},
  },

  attached() {
    this.setData({
      infoData: this.computeInfoData(),
    });
  },

  methods: {
    computeGoodsLayoutList(list) {
      return list.map((item) => {
        item.isSoldout = +item.soldStatus === 2 && +item.buyWay !== 0;
        return item;
      });
    },

    computeInfoData() {
      return pick(
        this.data,
        'showTitle',
        'showSubTitle',
        'showPrice',
        'showBuyButton',
        'buyButtonType',
        'showSoldNum',
        'buttonText',
        'textAlignType',
        'textStyleType',
        'isPlugin',
        'showOriginPrice',
        'imageRatio',
        'imageFillStyle',
        'borderRadiusType'
      );
    },

    /**
     * info 单个配置
     */
    computeInfoItemData(key, value) {
      const infoItem = {};
      infoItem[`infoData.${key}`] = value;
      this.setData({
        ...infoItem,
      });
    },
  },
});
