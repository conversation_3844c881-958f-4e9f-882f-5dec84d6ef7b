@import './components/groupon/groupon.scss';
@import './components/time-limited-discount/time-limited-discount.scss';

.rishiji-ump {
  margin: 5px 3px 0 5px;
  background-color: #f5f4f2;
  border-radius: 8px;
  position: relative;
}

.rishiji-ump .col-12 {
  width: 50%;
  display: inline-block;
  vertical-align: top;
}

.rishiji-ump__good-pic {
  width: 100%;
  height: 110px;
}

.rishiji-ump .col-12 > view {
  padding: 5px 10px;
}

.rishiji-ump .split-line-container {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;
  padding: 13.6px 0 18.2px;
  box-sizing: border-box;
  top: 0;
}

.rishiji-ump .split-line-container .split-line {
  width: 1px;
  height: 100%;
  background-color: #dcdcdb;
  opacity: 0.5;
}
  
.rishiji-ump__goods__price {
  white-space:nowrap;
  vertical-align:base-line;
  line-height:14px;
  margin-left: -3px;
  padding-top: 5px;
  display:flex;
  align-items:baseline;
}

.rishiji-ump__goods__price .span:first-child {
  color: #b42b2f;
  font-size: 14px;
  line-height: 20px;
}

.rishiji-ump__goods__price .span:last-child {
  font-size: 10px;
  color: #666;
  text-decoration: line-through;
  margin-left: 5px;
}

.rishiji-ump .span {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
}

.rishiji-ump__footer {
  background-color: #fff;
  margin-top: -3px;
  padding: 0px 5px 5px;
}

.rishiji-ump__footer > .span:first-child {
  color: #666;
  font-size: 8px;
}