<import src="./components/groupon/index.wxml" />
<import src="./components/time-limited-discount/index.wxml" />

<view class="rishiji-ump">
  <view class="rishiji-ump__container">
    <view 
      class="col-12"
      wx:for="{{ umpData }}"
      wx:key="index"
    >
      <template is="rishiji-ump-groupon" wx:if="{{ item.type }}" data="{{ item }}"/>

      <template is="rishiji-ump-discount" wx:else data="{{ item }}"/>
    </view>
    <view class="split-line-container" wx:if="{{ umpData.length === 2 }}">
      <view class="split-line"/>
    </view>
  </view>
</view>