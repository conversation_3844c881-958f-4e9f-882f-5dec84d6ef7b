import WscComponent from 'shared/common/base/wsc-component';
import { startPageCountdown, stopPageCountdown } from 'shared/utils/countdown-list';

WscComponent({
  properties: {
    countdown: {
      type: Number,
      observer(countdown) {
        this.startCountDown(countdown);
      }
    }
  },

  data: {
    countdownData: {
      hour: '00',
      minute: '00',
      second: '00'
    }
  },

  ready() {
    this.startCountDown(this.data.countdown);
  },

  detached() {
    this.stopCountDown();
  },

  methods: {
    startCountDown(countdown) {
      if (!countdown) {
        return;
      }

      if (this.countdownKey) {
        this.stopCountDown();
      }

      // 初始化计时器
      this.countdownKey = startPageCountdown(countdown, {
        change: (timeDataArr, strDataArr) => {
          const hour = timeDataArr.day * 24 + timeDataArr.hour;
          strDataArr.hour = hour > 9 ? hour : '0' + hour;
          this.setData({
            countdownData: strDataArr
          });
        }
      });
    },

    stopCountDown() {
      this.setData({
        countdownData: {
          hour: '00',
          minute: '00',
          second: '00'
        }
      });

      if (!this.countdownKey) {
        return;
      }

      stopPageCountdown(this.countdownKey);
      this.countdownKey = '';
    }
  }
});
