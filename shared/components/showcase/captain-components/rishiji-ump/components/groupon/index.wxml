<template name="rishiji-ump-groupon">
  <view
    class="rishiji-ump-groupon"
    bindtap="handleGoodsTap"
    data-alias="{{ item.alias }}"
    data-owl-type="{{ item.owlType }}"
    data-owl-alias="{{ item.owlAlias }}"
  >
    <view class="rishiji-ump-groupon__header">
      <view class="p">{{ item.title }}</view>
      <view class="p">{{ item.description }}</view>
    </view>

    <image mode="aspectFit" src="{{ item.imageUrl }}" class="rishiji-ump__good-pic"/>

    <view class="rishiji-ump__footer">
      <view class="span">{{ item.conditionNum }}人拼团享优惠</view>
      <view class="rishiji-ump__goods__price">
        <view class="span rishiji-ump-groupon__info__discount-price">￥{{ item.discountPrice }}</view>
        <view class="span rishiji-ump-groupon__info__origin-price">￥{{ item.originalPrice }}</view>
      </view>
    </view>

    <view class="rishiji-ump-groupon__more-btn__container">
      <button catchtap="handleGrouponLookMore">
        查看更多拼团商品
      </button>
    </view>
  </view>
</template>