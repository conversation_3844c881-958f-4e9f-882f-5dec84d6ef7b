<template name="rishiji-ump-discount">
  <view
    class="time-limited-discount"
    bindtap="handleGoodsTap"
    data-alias="{{ item.alias }}"
    data-owltype="{{ item.owlType }}"
    data-owl_alias="{{ item.owlAlias }}"
  >
    <view class="time-limited-discount__header">
      <image class="icon-clock" src="https://b.yzcdn.cn/public_files/2018/08/29/12e6af2cd866e745e469e51dc989eb73.png"/>
      <view class="span time-limited-discount__header__info">
        <view class="p">
          {{ item.title }}
        </view>
        <rsjump-countdown
          countdown="{{ item.countdown }}"
        />
      </view>
    </view>
    <image mode="aspectFit" src="{{ item.imageUrl }}" class="rishiji-ump__good-pic"/>
    <view class="rishiji-ump__footer">
      <view class="span">下一场{{ item.nextStartAtStr }}开始</view>
      <view class="rishiji-ump__goods__price">
        <view class="span">￥{{ item.discountPrice }}</view>
        <view class="span">￥{{ item.originalPrice }}</view>
      </view>
    </view>
  </view>
</template>