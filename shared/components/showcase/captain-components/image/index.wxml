<view
  class="cap-image {{  'cap-image__' + useScene }} {{ imageStyle === IMAGE_STYLE_MAP.shadow ? 'cap-image__img-shadow' : '' }} {{ cornerType === CORNER_TYPE_MAP.fillet ? 'cap-image__fillet' : '' }} {{ currentItemHasTitle ? 'has-title' : '' }}"
  style="margin: 0 {{ (SHOW_TYPES.SWIPE === showType || SHOW_TYPES.SLIDE === showType) ? 0 : pageMargin }}px; background-color: {{ backgroundColor && backgroundColor || 'transparent' }}"
>
  <!-- 图片广告：轮播模式（默认） -->
  <block wx:if="{{ showType === 'swipe' }}">
    <swiper
      class="cap-image-swiper"
      autoplay="{{ true }}"
      circular="{{ true }}"
      style="height: {{ rpxHeight ? rpxHeight : swipeHeight + ( imageStyle === IMAGE_STYLE_MAP.shadow ? 60 : 0) }}rpx;"
      bind:change="handleImageChange"
    >
      <block wx:for="{{ listData }}" wx:key="unique" wx:for-item="item">
        <swiper-item
          wx:if="{{ item.linkType !== 'chat' }}"
          style="position: absolute;"
          data-img-index="{{ index }}"
          bind:tap="handleImageClick"
          data-img-index="{{ index }}"
          style="box-sizing: border-box;"
        >
          <template is="image-item-swiper" data="{{ rpxHeight, swipeHeight, imageFillStyle, ...item, pageMargin }}"/>
        </swiper-item>
        <swiper-item
          wx:else
          style="position: absolute;"
          data-component-index="{{ componentIndex }}"
          data-img-index="{{ index }}"
        >
          <button
            class="image-swipe-btn--transparent chat-btn"
            open-type="contact"
            session-from="{{ im.sourceParam || '' }}"
            business-id="{{ im.businessId || '' }}"
            bind:contact="handleContactBack"
          >
            <template is="image-item-swiper" data="{{ swipeHeight, ...item, pageMargin }}"/>
          </button>
        </swiper-item>
      </block>
    </swiper>
    <block wx:if="{{ listData.length > 1 }}">
      <view class="cap-indicator cap-indicator--{{ indicator }} " wx:if="{{ indicator == 1 || indicator == 2 }}">
        <block
          wx:for="{{ listData }}"
          wx:key="unique"
        >
          <view
            class="cap-indicator__dot {{index === current?'cap-indicator__dot--active':''}}"
          />
        </block>
      </view>
      <view
        class="cap-indicator cap-indicator--3"
        wx:elif="{{ indicator == 3 }}"
        style="right: {{15 + pageMargin}}px;"
      >
        <theme-view
          custom-class="cap-indicator__dot--active"
          color="main-bg"
        >{{ current + 1 }}</theme-view>/{{ listData.length }}
      </view>
      <view
        class="cap-indicator cap-indicator--4"
        wx:elif="{{ indicator == 4 }}"
        style="right: {{15 + pageMargin}}px;"
      >
        <text class="cap-indicator__dot">{{ current + 1 }}/{{ listData.length }}</text>
      </view>
    </block>
  </block>

  <!-- 图片广告：横向滑动 -->
  <block wx:elif="{{ showType === 'slide' }}">
    <scroll-view scroll-x="true" class="image__scrollview">
      <view
        class="image__scrollview-container"
        style="box-sizing: border-box; width: {{ slideTotalWidth }}rpx; padding-left: {{ pageMargin }}px; padding-right: {{ pageMargin }}px; "
      >
        <block
          wx:for="{{ listData }}"
          wx:for-item="item"
          wx:for-index="index"
          wx:key="unique"
        >
          <button
            class="image-slide-btn--transparent chat-btn image__scrollview-item"
            open-type="contact"
            session-from="{{ im.sourceParam || '' }}"
            business-id="{{ im.businessId || '' }}"
            bind:contact="handleContactBack"
            wx:if="{{ item.linkType === 'chat' }}"
          >
            <template is="image-item-scroll" data="{{ borderWidth, slideShowCnt, imageFillStyle, ...item }}"/>
          </button>
          <view
            wx:else
            data-img-index="{{ index }}"
            bind:tap="handleImageClick"
            class="image__scrollview-item"
          >
            <template is="image-item-scroll" data="{{ imageFillStyle, borderWidth, slideShowCnt, ...item }}"/>
            <!-- 跳转小程序 -->
          </view>
        </block>
      </view>
    </scroll-view>
  </block>

  <!-- 图片导航 -->
  <block wx:elif="{{ showType === 'image_nav' }}">
    <scroll-view scroll-x="{{ fixedMode ? false : true }}" class="image__scrollview">
      <view class="image__scrollview-container">
        <block
          wx:for="{{ listData }}"
          wx:for-item="item"
          wx:for-index="index"
          wx:key="unique"
        >
          <!-- 客服链接特殊处理 -->
          <button
            wx:if="{{ item.linkType === 'chat' }}"
            open-type="contact"
            session-from="{{ im.sourceParam || '' }}"
            business-id="{{ im.businessId || '' }}"
            bind:contact="handleContactBack"
            class="image-imgnav-btn--transparent chat-btn"
          >
            <template is="image-item-nav" data="{{ imageFillStyle, backgroundColor, color, ...item }}"/>
          </button>
          <view
            wx:else
            data-img-index="{{ index }}"
            bind:tap="handleImageClick"
          >
              <template is="image-item-nav" data="{{ imageFillStyle, backgroundColor, color, ...item }}"/>
          </view>
        </block>
      </view>
    </scroll-view>
  </block>

  <!-- 文字导航 -->
  <block wx:elif="{{showType === 'text_nav'}}">
    <scroll-view scroll-x="{{fixedMode ? false : true}}" class="image__scrollview">
      <view class="text-nav__item-wraper">
        <block
          wx:for="{{ listData }}"
          wx:for-item="item"
          wx:for-index="index"
          wx:key="unique"
        >
          <!-- 客服链接特殊处理 -->
          <button
            wx:if="{{ item.linkType === 'chat' }}"
            open-type="contact"
            session-from="{{ im.sourceParam || '' }}"
            business-id="{{ im.businessId || '' }}"
            bind:contact="handleContactBack"
            class="text-nav__itemview image-textnav-btn--transparent chat-btn"
            style="width: {{ fixedMode ? 'auto' : textNavShowWidth + 'rpx' }}; background-color: {{backgroundColor}}; flex: {{ fixedMode ? 1 : 'none' }};"
          >
            <template is="text-item-nav" data="{{ imageFillStyle, color, ...item, fixedMode }}"/>
          </button>
          <view
            wx:else
            class="text-nav__itemview"
            data-img-index="{{ index }}"
            bind:tap="handleImageClick"
            style="width: {{ fixedMode ? 'auto' : textNavShowWidth + 'rpx' }}; background-color: {{backgroundColor}}; flex: {{ fixedMode ? 1 : 'none' }};"
          >
            <template is="text-item-nav" data="{{ imageFillStyle, color, ...item, fixedMode }}"/>
            <!-- 跳转小程序 -->
          </view>
        </block>
      </view>
    </scroll-view>
  </block>

  <!-- 图片广告：从上往下平铺显示 -->
  <block wx:else>
    <view class="image__top2end-wrapper">
      <block
        wx:for="{{ listData }}"
        wx:for-item="item"
        wx:for-index="index"
        wx:key="index"
      >
        <template
          is="image-item-top2end"
          data="{{ index, ...item, componentIndex: componentIndex, hasHotArea: hasHotArea, margin: borderWidth, imageFillStyle, im }}"
        />
      </block>
    </view>
  </block>
</view>

<!-- 滑动显示的图片 -->
<template name="image-item-scroll">
  <view>
    <view
      class="image__itemview"
      style="height: {{ showHeight }}rpx; width: {{ showWidth }}rpx; margin-right: {{ borderWidth }}px;"
    >
      <image
        class="scroll-image__item image-item"
        src="{{ imageUrl }}"
        mode="aspectFill"
        lazy-load="true"
        style="height: {{ showHeight }}rpx; width: {{ showWidth }}rpx"
      ></image>
      <view
        wx:if="{{ title }}"
        class="scroll-image__item-title scroll-image__item-title-{{ slideShowCnt > 2 ? 'small' : 'big' }}"
      >
        {{ title }}
      </view>
    </view>
  </view>
</template>

<!-- 图片导航显示的图片 -->
<template name="image-item-nav">
  <view>
    <view
      class="image-nav__itemview"
      style="background-color: {{backgroundColor}}; color: {{color}}"
    >
      <image
        class="scroll-image__item image-item"
        src="{{ imageUrl }}"
        mode="aspectFill"
        style="height: {{ showHeight }}px; width: {{ showWidth }}px"
      ></image>
      <view
        wx:if="{{ title }}"
        class="image-nav__text"
        style="max-width: {{ showWidth }}px"
      >
        {{ title }}
      </view>
    </view>
  </view>
</template>

<!-- 文字导航显示的文本 -->
<template name="text-item-nav">
  <view>
      <view
        style="color: {{color}}"
        class="text-nav__title"
      >
        {{ title }}
      </view>
  </view>
</template>

  <!-- 轮播显示的文本 -->
<template name="image-item-swiper">
  <view
    class="image-item__wrapper"
    style="padding-left: {{ pageMargin }}px; padding-right: {{ pageMargin }}px;"
  >
    <image
      class="image-item"
      src="{{ imageUrl }}"
      mode="{{ imageFillStyle }}"
      lazy-load="true"
      style="width: 100%; height: {{ rpxHeight ? rpxHeight : swipeHeight }}rpx;"
    />
    <view
      wx:if="{{ title }}"
      class="image__title zan-ellipsis"
      style="margin: 0 {{ pageMargin }}px;"
    >
      {{ title }}
    </view>
  </view>
</template>

<!-- 从上往下显示的图片 -->
<template name="image-item-top2end">
    <view
      class="image__itemview image__top2end"
      style="margin-bottom: {{ margin }}px"
    >
      <!-- 客服链接特殊处理 -->
      <button
        wx:if="{{ linkType === 'chat' }}"
        open-type="contact"
        session-from="{{ im.sourceParam || '' }}"
        business-id="{{ im.businessId || '' }}"
        bind:contact="handleContactBack"
        class="image-top2end-btn--transparent chat-btn"
      >
        <image
          class="image-item"
          src="{{ imageUrl }}"
          id="img-ad-{{ index }}"
          mode="aspectFill"
          lazy-load="true"
          data-img-index="{{ index }}"
          style="width: {{ showWidth }}rpx; height: {{ showHeight }}rpx;"
        />
      </button>
      <view
        wx:else
        data-img-index="{{ index }}"
        bind:tap="handleImageClick"
      >
        <image
          class="image-item"
          src="{{ imageUrl }}"
          id="img-ad-{{ index }}"
          mode="aspectFill"
          show-menu-by-longpress="{{ !linkUrl && !linkType }}"
          lazy-load="true"
          style="width: {{ showWidth }}rpx; height: {{ showHeight }}rpx;"
        />
      </view>
        <block
          wx:if="{{ linkType == 'hotarea' && hotAreas && hotAreas.length }}"
        >
          <cap-navigator
            wx:for="{{ hotAreas }}"
            wx:for-item="hotarea"
            wx:key="unique"
            data-hotarea="{{ hotarea }}"
            data-imgurl="{{ imageUrl }}"
            link-type="{{ hotarea.linkType }}"
            app-id="{{ hotarea.extraData.otherWeappAppid }}"
            path="{{ hotarea.extraData.otherWeappLink }}"
            use-short-link="{{ (hotarea.extraData.useShortLink === '1' || hotarea.extraData.useShortLink === 1) ? 1 : 0 }}"
            short-link="{{ hotarea.extraData.shortLink }}"
            style="position: absolute; left: {{ hotarea.left }}rpx; top: {{ hotarea.top }}rpx; width: {{ hotarea.width }}rpx; height: {{ hotarea.height }}rpx;"
            custom-style="position: absolute; left: 0; right: 0; top: 0; bottom: 0;"
            im="{{ im }}"
            bind:navigate="handleHotareaNavigate"
            bind:contactback="handleContactBack"
          />
        </block>
        <view wx:if="{{ title }}" class="image__title zan-ellipsis">{{ title }}</view>
    </view>
</template>
