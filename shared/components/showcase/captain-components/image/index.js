import getSystemInfo from 'shared/utils/browser/system-info';
import {
  SHOW_TYPES,
  CORNER_TYPE_MAP,
  IMAGE_STYLE_MAP,
  SHOW_SIZE_MAP,
} from './constants';
import { cdnImage } from '@youzan/tee-biz-util';

Component({
  options: {
    addGlobalClass: true,
  },

  type: 'image',

  properties: {
    useScene: {
      type: String,
      value: '',
    },
    showMethod: {
      type: Number,
      value: 0,
    },

    size: {
      type: Number,
      value: 0,
    },

    images: {
      type: Array,
      value: [],
    },

    count: {
      type: Number,
      value: 3,
    },

    swipeFill: {
      type: Boolean,
      value: false,
    },

    hotareaLinkBorder: {
      type: Boolean,
      value: false,
    },

    borderWidth: {
      type: Number,
      value: 0,
    },

    backgroundColor: {
      type: String,
    },

    color: {
      type: String,
    },

    slideSetting: {
      type: Number,
      value: 0,
    },

    slideUnivisibleRatio: {
      type: Number,
      value: 0.8,
    },

    pageMargin: {
      type: [String, Number],
      value: 0,
    },

    // 图片样式 1. 常规 2. 阴影
    imageStyle: {
      type: Number,
      value: 1,
    },

    // 图片倒角样式 1. 直角 2. 圆角
    cornerType: {
      type: Number,
      value: 1,
    },

    indicator: {
      type: [String, Number],
      value: 1, // 1, 2, 3, 4
    },

    im: {
      type: Object,
      value: {},
    },

    rpxHeight: {
      type: Number,
      value: 0,
    },
  },

  data: {
    showType: SHOW_TYPES.SWIPE,
    listData: [],
    imageFillStyle: 'aspectFit',
    swipeHeight: 0,
    slideHeight: 0,
    slideTotalWidth: 0,
    slideShowCnt: 3,
    fixedMode: true,
    textNavShowWidth: 0,
    hasHotArea: false,
    current: 0,
    currentItemHasTitle: false,
    SHOW_TYPES,
    IMAGE_STYLE_MAP,
    CORNER_TYPE_MAP,
  },

  attached() {
    const {
      showMethod,
      size,
      images = [],
      count,
      swipeFill,
      slideSetting,
      height,
      pageMargin,
      borderWidth,
      imageStyle,
      cornerType,
      indicator,
    } = this.properties;

    if (images.length <= 0) {
      return;
    }

    // 通用设置
    this.pixelRatio = 750 / (getSystemInfo().windowWidth || 375);
    const rpxPageMargin = pageMargin * this.pixelRatio;
    const pageWidth = 750 - rpxPageMargin * 2;
    const showType = this.getShowType(showMethod); // 获取显示类型
    const { firstPageDataHeight, firstPageDataWidth } = this.getFirstPageData(
      height,
      images
    ); // 获取第一张图片的宽高

    // 特有设置
    const {
      featureOptions,
      imgNavShowPerWidth,
      imgNavShowHeight,
      slideHeight,
      slideWidth,
    } = this.getFeatureOptions(
      showType,
      pageWidth,
      firstPageDataHeight,
      firstPageDataWidth,
      slideSetting,
      count,
      images,
      size,
      rpxPageMargin,
      borderWidth
    );

    // 通过设置，修改列表数据，并得到热区设置
    const {
      list: listData,
      hasHotArea,
      slideTotalWidth,
    } = this.getFormatedList(
      images,
      showType,
      pageWidth,
      slideHeight,
      slideWidth,
      imgNavShowHeight,
      imgNavShowPerWidth
    );

    this.setData({
      showType,
      listData,
      imageFillStyle: swipeFill ? 'aspectFill' : 'aspectFit',
      slideHeight,
      slideTotalWidth,
      hasHotArea,
      pageMargin,
      imageStyle,
      cornerType,
      indicator,
      currentItemHasTitle: this.computeCurrentHasTitle(images, 0),
      ...featureOptions,
    });
  },

  methods: {
    // 获取不同类型特有属性和设置项
    getFeatureOptions(
      showType,
      pageWidth,
      firstPageDataHeight,
      firstPageDataWidth,
      slideSetting,
      count,
      images,
      size,
      rpxPageMargin,
      borderWidth
    ) {
      let imgNavShowPerWidth = 0;
      let imgNavShowHeight = 0;
      let slideHeight = 0;
      let slideWidth = 0;
      const featureOptions = {}; // 不同类型的特有属性；并计算出不同类型需要的特殊值

      if (showType === SHOW_TYPES.SWIPE) {
        featureOptions.swipeHeight = this.getSwipeHeight(
          pageWidth,
          firstPageDataHeight,
          firstPageDataWidth
        );
      } else if (
        showType === SHOW_TYPES.TEXT_NAV ||
        showType === SHOW_TYPES.IMAGE_NAV
      ) {
        featureOptions.fixedMode = slideSetting === 0;
        const { offset, navShowCount } = this.getNavData(
          featureOptions.fixedMode,
          count,
          images
        ); // 导航相关的数据计算
        ({ imgNavShowPerWidth, imgNavShowHeight } = this.getImgNavData(
          showType,
          firstPageDataWidth,
          firstPageDataHeight,
          offset,
          navShowCount,
          pageWidth
        )); // 计算图片导航
        featureOptions.textNavShowWidth = this.getTextNavData(
          showType,
          offset,
          navShowCount,
          pageWidth
        ); // 计算文字导航
      } else if (showType == SHOW_TYPES.SLIDE) {
        featureOptions.slideShowCnt = this.getSlideShowCnt(size, count); // 获取滑动图片一行显示的个数
        ({ slideHeight } = this.getSlideWidthAndHeight(
          showType,
          size,
          featureOptions.slideShowCnt,
          firstPageDataHeight,
          firstPageDataWidth,
          pageWidth
        )); // 计算横向滑动
        slideWidth = this.getSlideWidth(rpxPageMargin, images, borderWidth);
      }

      return {
        featureOptions,
        imgNavShowPerWidth,
        imgNavShowHeight,
        slideHeight,
        slideWidth,
      };
    },

    getFormatedList(
      originList,
      showType,
      pageWidth,
      slideHeight,
      slideWidth,
      imgNavShowHeight,
      imgNavShowPerWidth
    ) {
      // 计算图片的url、宽高
      let hasHotArea = false; // 是否有热点图
      const list = [...originList].concat();

      list.forEach((imageData) => {
        if (showType == SHOW_TYPES.TOP2END) {
          // 从上而下平铺展示，计算图片大小
          imageData.showHeight = Math.ceil(
            imageData.imageHeight * (pageWidth / imageData.imageWidth)
          );
          imageData.showWidth = pageWidth;
        } else if (showType == SHOW_TYPES.SLIDE) {
          imageData.showHeight = slideHeight;
          imageData.showWidth = Math.floor(
            slideHeight * (imageData.imageWidth / imageData.imageHeight)
          );
          slideWidth += imageData.showWidth;
        } else if (showType == SHOW_TYPES.IMAGE_NAV) {
          imageData.showWidth = imgNavShowPerWidth / this.pixelRatio;
          imageData.showHeight = imgNavShowHeight / this.pixelRatio;
        }

        if (imageData.linkType === 'hotarea') {
          hasHotArea = true;
        }

        imageData.imageUrl = cdnImage(imageData.imageUrl, '!900x0.jpg');

        this.setHotAreas(imageData, pageWidth);

        // 删除渲染不需要的数据
        delete imageData.type;
        delete imageData.imageId;
        delete imageData.imageThumbUrl;
        delete imageData.templateId;
      });

      return {
        hasHotArea,
        slideTotalWidth: slideWidth,
        list,
      };
    },

    // 计算显示类型
    getShowType(showMethod) {
      let showType = SHOW_TYPES.SWIPE;
      switch (showMethod) {
        case 1: // 横向滑动
        case 6:
          showType = SHOW_TYPES.SLIDE;
          break;
        case 7: // 从上而下
          showType = SHOW_TYPES.TOP2END;
          break;
        case 8: // 图文导航
          showType = SHOW_TYPES.IMAGE_NAV;
          break;
        case 9: // 文本导航
          showType = SHOW_TYPES.TEXT_NAV;
          break;
        default:
          showType = SHOW_TYPES.SWIPE;
      }
      return showType;
    },

    // 计算图片导航
    getSlideShowCnt(size, count) {
      let slideShowCnt = 3;
      switch (size) {
        case 0: // 大图
          slideShowCnt = 2;
          break;
        case 1: // 小图
          slideShowCnt = 3;
          break;
        case 2: // 小图
          slideShowCnt = count || 3;
          break;
        default: // 小图
          slideShowCnt = 2;
      }
      return slideShowCnt;
    },

    getSwipeHeight(pageWidth, firstPageDataHeight, firstPageDataWidth) {
      return Math.ceil(pageWidth * (firstPageDataHeight / firstPageDataWidth));
    },

    getSlideWidth(rpxPageMargin, images = [], borderWidth) {
      return rpxPageMargin * 2 + (images.length - 1) * borderWidth * 2;
    },

    // 获取第一张图片的宽高
    getFirstPageData(height, images = []) {
      let firstPageDataWidth = 320;
      let firstPageDataHeight = height || 320;
      if (images.length > 0) {
        firstPageDataHeight = images[0].imageHeight || 320;
        firstPageDataWidth = images[0].imageWidth || 320;
      }
      return { firstPageDataWidth, firstPageDataHeight };
    },

    // 计算横向滑动
    getSlideWidthAndHeight(
      showType,
      size,
      slideShowCnt,
      firstPageDataHeight,
      firstPageDataWidth,
      pageWidth
    ) {
      let slideHeight = 0;
      if (showType === SHOW_TYPES.SLIDE) {
        // 屏幕宽度 减去空白后的宽度，再计算每个图片的宽度==>再根据第一张图的显示宽度计算第一张图的显示高度
        if (+size === SHOW_SIZE_MAP.big) {
          // 活动大图，第一张图显示宽度为父元素宽度的88%
          slideHeight = Math.ceil(
            (pageWidth * 0.88 * firstPageDataHeight) / firstPageDataWidth
          );
        } else if (+size === SHOW_SIZE_MAP.small) {
          // 活动小图，第一张图显示宽度为父元素宽度的40%
          slideHeight = Math.ceil(
            (pageWidth * 0.4 * firstPageDataHeight) / firstPageDataWidth
          );
        } else {
          slideHeight = Math.floor(
            ((pageWidth - (slideShowCnt - 1) * 2) / (slideShowCnt - 0.8)) *
              (firstPageDataHeight / firstPageDataWidth)
          );
        }
      }
      return { slideHeight };
    },

    // 导航相关的数据计算
    getNavData(fixedMode, count, images = []) {
      const offset = fixedMode ? 0 : 0.8;
      let navShowCount = count;
      if (fixedMode) {
        navShowCount = images.length;
      }
      return { fixedMode, offset, navShowCount };
    },

    // 计算图片导航
    getImgNavData(
      showType,
      firstPageDataWidth,
      firstPageDataHeight,
      offset,
      navShowCount,
      pageWidth
    ) {
      let imgNavShowHeight = 0;
      let imgNavShowPerWidth = 0;

      if (showType === SHOW_TYPES.IMAGE_NAV) {
        imgNavShowPerWidth = Math.ceil(pageWidth / (navShowCount - offset));
        // 图片导航中每个图片的高度通过首图的宽高比换算
        imgNavShowHeight = Math.floor(
          imgNavShowPerWidth * (firstPageDataHeight / firstPageDataWidth)
        );
      }
      return { imgNavShowPerWidth, imgNavShowHeight };
    },

    // 计算文字导航
    getTextNavData(showType, offset, navShowCount, pageWidth) {
      let textNavShowWidth = 0;
      if (showType === SHOW_TYPES.TEXT_NAV) {
        textNavShowWidth = Math.ceil(pageWidth / (navShowCount - offset));
      }
      return textNavShowWidth;
    },

    computeCurrentHasTitle(listData, current) {
      return !!listData[current].title;
    },

    calcActualTapPoint(imgData, event, boundingClientRect) {
      const {
        top,
        left,
        height: showHeightPx,
        width: showWidthPx,
      } = boundingClientRect;
      // 点击事件的触点
      const touch = event.touches[0];
      // 图片的显示高宽度 实际高度
      const { imageWidth: realWidth, imageHeight: realHeight } = imgData;
      // 计算触点在元素内的偏移位置位置
      const offsetWidthPx = touch.clientX - left;
      const offsetHeightPx = touch.clientY - top;
      // 计算在真实图片上的点击区域
      const pointX = realWidth * (offsetWidthPx / showWidthPx);
      const pointY = realHeight * (offsetHeightPx / showHeightPx);
      return { x: pointX, y: pointY };
    },

    // 计算点击所在的热点区域
    findContainedHotArea(point, hotAreaList) {
      if (!hotAreaList) return;
      const finded = hotAreaList.find((hotArea) => {
        if (
          point.x >= hotArea.startX &&
          point.x <= hotArea.endX &&
          point.y >= hotArea.startY &&
          point.y <= hotArea.endY
        ) {
          return true;
        }
        return false;
      });
      return finded;
    },

    setHotAreas(imageData, pageWidth) {
      const rate = pageWidth / imageData.imageWidth;
      // imageData.showHeight = Math.floor(rate * imageData.imageHeight);
      if (imageData.linkType === 'hotarea') {
        imageData.hotAreas = imageData.hotAreas || [];
        imageData.hotAreas.forEach((area) => {
          area.top = Math.floor(area.startY * rate);
          area.left = Math.floor(area.startX * rate);
          area.width = Math.floor((area.endX - area.startX) * rate);
          area.height = Math.floor((area.endY - area.startY) * rate);
        });
      }
    },

    handleImageClick(e) {
      // 计算热区的点击位置
      const { imgIndex } = e.currentTarget.dataset;
      const { listData: images } = this.data;
      const image = images[imgIndex];

      this.triggerEvent('itemClick', image);
    },

    handleImageChange(e) {
      const { listData } = this.data;
      const { current } = e.detail;
      this.setData({
        current,
        currentItemHasTitle: this.computeCurrentHasTitle(listData, current),
      });

      this.triggerEvent('itemChange', { value: current });
    },

    handleHotareaNavigate(event) {
      const { hotarea = {}, imgurl = '' } = event.currentTarget.dataset;
      this.triggerEvent('navigate', {
        ...hotarea,
        imageUrl: imgurl,
      });
    },

    handleContactBack(e) {
      this.triggerEvent('contactback', e.detail);
    },
  },
});
