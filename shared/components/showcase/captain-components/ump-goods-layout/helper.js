import { ACTIVITY_STATUS } from './constants';
import { LAYOUT_MAP } from '../goods-layout/constants';

export function isNotStart(status) {
  return +status === ACTIVITY_STATUS.NOT_START;
}

export function getCountdown(goods) {
  const {
    startAt,
    endAt,
    activityStatus
  } = goods;

  let remain;

  const nowTime = (new Date()).getTime();
  if (isNotStart(activityStatus)) {
    remain = startAt - nowTime;
  } else if (activityStatus === ACTIVITY_STATUS.END) {
    remain = 0;
  } else {
    remain = endAt - nowTime;
  }

  return remain;
}

export function getCountdownText(goods, layout) {
  const { itemIndex } = goods;
  const isHybridBigLayout = +layout === LAYOUT_MAP.HYBRID && itemIndex % 3 === 0;
  const isBig = +layout === LAYOUT_MAP.BIG || isHybridBigLayout;

  if (isNotStart(goods.activityStatus)) {
    return isBig ? '距开始仅剩' : '距开始';
  }

  return isBig ? '距结束仅剩' : '距结束';
}

/**
 *
 * @param {String} prefix 组件 list 的路径
 * @param {Array} list 组件 list 数据
 * @param {*} key key。用于触发列表更新
 * @param {Function} otherUpdateFunc 当状态变化时会被调用，用于设置一些其他的数据。可选
 */
export function getUpdateCountdownGoodsList(prefix, list, key, otherUpdateFunc) {
  const curGoodsList = {};

  list.forEach((item, index) => {
    const { activityStatus, startAt, endAt } = item;
    const status = getActivityStatus(activityStatus, null, startAt, endAt);

    if (status !== activityStatus) {
      const isEnd = +status === ACTIVITY_STATUS.END;
      curGoodsList[`${prefix}[${index}].activityStatus`] = status;
      curGoodsList[`${prefix}[${index}].isEnd`] = isEnd;

      if (typeof otherUpdateFunc === 'function') {
        const res = otherUpdateFunc(item, index, +status) || {};

        Object.keys(res).forEach(key => {
          curGoodsList[`${prefix}[${index}].${key}`] = res[key];
        });
      }
    }

    curGoodsList[`${prefix}[${index}].pageRandomString`] = key;
  });

  return curGoodsList;
}

const isUndefined = val => Object.prototype.toString.call(val) === '[object Undefined]';

/**
 * 获取活动状态
 * @param { Number | Undefined } status 状态
 * @param { Data | Null } current 当前时间
 * @param { Data } startAt 开始时间
 * @param { Data } endAt 结束时间
 * @return {*}
 */
export function getActivityStatus(status, current, startAt, endAt) {
  const now = current ? current * 1000 : Date.now();
  let activityStatus = status;
  if (isUndefined(activityStatus)) {
    if (now < startAt) {
      activityStatus = ACTIVITY_STATUS.NOT_START;
    } else if (now >= startAt && now < endAt) {
      activityStatus = ACTIVITY_STATUS.START;
    } else {
      activityStatus = ACTIVITY_STATUS.END;
    }
  }

  return activityStatus;
}
