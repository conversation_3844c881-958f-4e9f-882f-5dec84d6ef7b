import { startPageCountdown } from 'shared/utils/countdown-list';
import countdownUtilBehavior from '../../behaviors/countdown-util-behavior';
import goodsItemInfoBehavior from '../../../goods-layout/behaviors/goods-item-info-behavior';

Component({
  type: 'countdown',

  behaviors: [goodsItemInfoBehavior, countdownUtilBehavior],

  externalClasses: ['custom-class'],

  properties: {
    type: String,

    countdown: {
      type: Number,
      observer(countdown) {
        if (countdown && !this.data.controlled) {
          this.stopCountDown();
          this.startCountDown(countdown);
        }
      }
    },

    controlled: {
      type: Boolean,
      value: false,
    },

    countdownData: {
      type: Object,
      value: null
    },

    hideZeroDay: {
      type: Boolean,
      value: true
    },

    timeSeparator: {
      type: Array,
      value: [':', ':', ':', '']
    }
  },

  detached() {
    this.stopCountDown();
  },

  methods: {
    startCountDown(countdown) {
      if (!countdown || this.data.controlled) {
        return;
      }

      // 初始化计时器，返回
      this.recycleCdFn = startPageCountdown(countdown, {
        change: (timeDataArr, strDataArr) => {
          this.triggerEvent('countdown-change', {
            strDataArr,
            timeDataArr
          });
        },
        end: () => {
          this.triggerEvent('countdown-end');
        }
      });
    },

    stopCountDown() {
      if (!this.recycleCdFn) return;

      this.recycleCdFn();
      this.recycleCdFn = '';
    }
  }
});
