.countdown {
  color: #fff;
  height: 16px;
  line-height: 16px;
  font-size: 12px;
}

.countdown__day,
.countdown__hour,
.countdown__minute,
.countdown__second {
  display: inline-block;
  padding: 0 2px;
  font-size: 12px;
  font-weight: bold;
  border-radius: 2px;
}

.countdown__second {
  width: 14px;
}

.countdown__time-text {
  display: inline-block;
  transform: scale(0.8);
  font-size: 12px;
  margin: 0 2px;

  &:last-child {
    margin-right: 0;
  }
}

.countdown__day,
.countdown__hour.hide-zero-day {
  padding-left: 0;
}

.countdown--small {
  height: 24px;
  line-height: 25px;

  .countdown__time-text {
    margin: 0;
  }

  .countdown__second {
    padding-right: 0;
  }
}

.countdown--diamond {
  height: 20px;
  line-height: 20px;
  font-size: 12px;

  .countdown__time-text {
    color: #646566;
    font-weight: bold;
  }

  .countdown__day,
  .countdown__hour,
  .countdown__minute,
  .countdown__second {
    // min-width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    background: #f44;
    color: #fff;
    margin: 0;
    padding: 0 3px;
  }

  /* 防止小程序倒计时跳动 */
  .countdown__second {
    min-width: 15px;
    text-align: center;
  }
}
