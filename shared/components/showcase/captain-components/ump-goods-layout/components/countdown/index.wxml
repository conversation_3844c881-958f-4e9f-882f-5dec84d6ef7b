<view
  wx:if="{{ countdownData }}"
  class="countdown countdown--{{ type }} custom-class"
>
  <block wx:if="{{ !hideZeroDay || countdownData.day > 0 }}">
    <text class="countdown__day">{{ countdownData.day }}</text>
    <text class="countdown__time-text">{{ timeSeparator[0] }}</text>
    <text class="countdown__hour">{{ countdownData.hour }}</text>
  </block>
  <text wx:else class="countdown__hour hide-zero-day">{{ countdownData.hour }}</text>
  <text class="countdown__time-text">{{ timeSeparator[1] }}</text>
  <text class="countdown__minute">{{ countdownData.minute }}</text>
  <text class="countdown__time-text">{{ timeSeparator[2] }}</text>
  <text class="countdown__second">{{ countdownData.second }}</text>
  <text wx:if="{{ timeSeparator[3] }}" class="countdown__time-text">{{ timeSeparator[3] }}</text>
</view>
