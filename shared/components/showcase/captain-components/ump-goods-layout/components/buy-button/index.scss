$bigButtonSize: 24px;
$smallButtonSize: 20px;
$red: #f44;

.btn-userinfo {
  overflow: visible;
}

.goods__btn-wrap.big {
  .icon-btn {
    .van-icon {
      font-size: $bigButtonSize;
      line-height: $bigButtonSize;
      height: $bigButtonSize;
    }
  }

  .cap-btn {
    .van-button {
      line-height: 24px;
      height: $bigButtonSize;
      padding: 0 7px;
    }
  }

  .btn-3,
  .btn-8 {
    .van-button {
      border-radius: 12px;
    }
  }

  .btn-1,
  .btn-2,
  .btn-5 {
    height: $bigButtonSize;
    font-size: $bigButtonSize;
    line-height: $bigButtonSize;
  }
}

.goods__btn-wrap.small {
  .icon-btn {
    .van-icon {
      font-size: $smallButtonSize;
      line-height: $smallButtonSize;
      height: $smallButtonSize;
    }
  }

  .cap-btn {
    .van-button {
      line-height: $smallButtonSize;
      height: $smallButtonSize;
      font-size: 12px;
      padding: 0 5px;
    }
  }

  .btn-3,
  .btn-8 {
    .van-button {
      border-radius: 10px;
      padding: 0 7px;
    }
  }
}

.icon-btn {
  .van-icon {
    color: $red;
  }
}

.btn-4,
.btn-8 {
  .van-button {
    font-size: 14px;
    border-color: $red;
    color: $red;
    min-width: auto;
    cursor: pointer;
    border-radius: 2px;
    background-color: inherit;
  }
}

.btn-3,
.btn-7 {
  .van-button {
    min-width: auto;
  }
}

.btn-9 {
  .van-button {
    height: 40px;
    line-height: 40px;
    font-size: 12px;
    padding: 0 11px;
    border-radius: 0;
  }
}