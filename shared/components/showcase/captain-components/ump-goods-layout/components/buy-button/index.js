/* eslint-disable */
import ThemeBehavior from 'shared/common/components/theme-view/behavior';
/* eslint-enable */

Component({
  options: {
    addGlobalClass: true
  },

  externalClasses: ['btn-wrap-class'],

  behaviors: [ThemeBehavior],

  properties: {
    /**
     * 按钮类型
     */
    type: {
      type: Number,
      value: 1
    },

    /**
     * 自定义按钮文字
     */
    buttonText: {
      type: String,
      observer(val) {
        if (val === undefined) return;
        this.triggerEvent('on-load');
      }
    },

    redirectType: Number,

    appId: String,

    alias: String,

    extraData: {
      type: Object,
      value: {}
    },

    buttonSize: String,

    needWxAuth: {
      type: Boolean,
      value: true
    }
  },

  methods: {
    handleGoodsBuy() {
      const { redirectType } = this.data;

      if ((redirectType & 1) === 0) return;

      this.triggerEvent('buy');
    }
  }
});
