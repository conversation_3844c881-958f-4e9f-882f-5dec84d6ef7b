<view
  class="goods__btn-wrap goods__btn-wrap--type-{{ type }} btn-wrap-class {{ buttonSize }}"
  btn-class="btn-userinfo"
  catch:tap="handleGoodsBuy"
>
  <template is="button-content" data="{{ type, buttonText, themeColor }}"></template>
</view>

<template name="button-content">
  <van-icon
    wx:if="{{ type == 1 }}"
    custom-class="van-icon"
    name="cart-circle-o"
    class="btn-{type} icon-btn"
    color="{{ themeColor }}"
  />

  <van-icon
    wx:if="{{ type == 2 }}"
    custom-class="van-icon"
    name="add-o"
    class="btn-2 icon-btn"
    color="{{ themeColor }}"
  />

  <van-button
    wx:if="{{ type == 3 }}"
    class="cap-btn btn-3"
    type="danger"
    custom-class="van-button"
  >
    {{ buttonText ? buttonText : '马上抢' }}
  </van-button>

  <van-button
    wx:if="{{ type == 4 }}"
    class="cap-btn btn-4"
    custom-class="van-button"
  >
    {{ buttonText ? buttonText : '购买' }}
  </van-button>

  <van-icon
    wx:if="{{ type == 5 }}"
    custom-class="van-icon"
    name="add"
    class="btn-2 icon-btn"
    color="{{ themeColor }}"
  />

  <van-icon
    wx:if="{{ type == 6 }}"
    custom-class="van-icon"
    name="shopping-cart-o"
    class="btn-2 icon-btn"
    color="{{ themeColor }}"
  />

  <van-button
    wx:if="{{ type == 7 }}"
    class="cap-btn btn-7"
    type="danger"
    custom-class="van-button"
  >
    {{ buttonText ? buttonText : '马上抢' }}
  </van-button>

  <van-button
    wx:if="{{ type == 8 }}"
    class="cap-btn btn-8"
    custom-class="van-button"
  >
    {{ buttonText ? buttonText : '购买' }}
  </van-button>

  <van-button
    wx:if="{{ type == 9 }}"
    class="btn-9"
    type="danger"
    custom-class="van-button"
  >
    我要抢购
  </van-button>

  <van-button
    wx:if="{{ type == 10 }}"
    class="btn-10 cap-btn"
    type="danger"
    custom-class="van-button"
  >
    {{ buttonText ? buttonText : '兑换' }}
  </van-button>
</template>
