import countdownUtilBehavior from '../../behaviors/countdown-util-behavior';
import goodsItemInfoBehavior from '../../../goods-layout/behaviors/goods-item-info-behavior';
import { LAYOUT_MAP, LAYOUT_TYPE } from '../../../goods-layout/constants';
import { getCountdownText, getCountdown } from '../../helper';

Component({
  behaviors: [goodsItemInfoBehavior, countdownUtilBehavior],

  options: {
    addGlobalClass: true
  },

  properties: {
    goodsInfo: {
      type: Object,
      value: {},
      observer() {
        this.initCountdown();
      }
    },
  },

  data: {
    isShowTagText: false,
    containerClass: '',
    countdownType: '',
    isShowCountdown: false,
    timeSeparator: [':', ':', ':'],
    countdownText: '',
    countdown: '',
    controlled: false,
  },

  attached() {
    this.initCountdown();
  },

  methods: {
    isHybridBigLayout() {
      const { layout, goodsInfo } = this.data;
      const { itemIndex } = goodsInfo;
      return +layout === LAYOUT_MAP.HYBRID && itemIndex % 3 === 0;
    },

    initCountdown() {
      const { layout, infoData = {}, goodsInfo } = this.data;
      const { showCountDown, controlled = false } = infoData;
      const isBigLayout = +layout === LAYOUT_MAP.BIG || this.isHybridBigLayout();
      const isShowCountdown = showCountDown && !goodsInfo.isSoldout && !goodsInfo.isEnd && +layout < 4;
      this.setData({
        isShowTagText: isBigLayout || this.isHybridBigLayout(),
        containerClass: this.computeContainerClass(isShowCountdown),
        countdownType: this.computeCountdownType(),
        isShowCountdown,
        timeSeparator: isBigLayout ? ['天', '时', '分', '秒'] : [':', ':', ':'],
        countdownText: getCountdownText(goodsInfo, layout),
        countdown: getCountdown(goodsInfo),
        isShowExtraTag: Boolean(goodsInfo.extraTagText),
        controlled,
      });
    },

    computeContainerClass(isShowCountdown) {
      const { layout } = this.data;

      const result = `${LAYOUT_TYPE[layout]}`;
      const containerClass = [result];

      if (layout === LAYOUT_MAP.HYBRID) {
        const isHybridBigLayout = this.isHybridBigLayout();
        if (isHybridBigLayout) {
          containerClass.push('hybrid-big');
        } else {
          containerClass.push('hybrid-list');
        }
      }

      if (!isShowCountdown) {
        containerClass.push('no-countdown');
      }

      return containerClass;
    },

    computeCountdownType() {
      const { layout } = this.data;
      const isHybridBigLayout = this.isHybridBigLayout();

      return +layout === LAYOUT_MAP.BIG || isHybridBigLayout ? 'big' : 'small';
    },

    countdownEnd() {
      const { goodsInfo } = this.data;
      this.triggerEvent('tag-info-change', { itemIndex: goodsInfo.itemIndex });
    }
  }
});
