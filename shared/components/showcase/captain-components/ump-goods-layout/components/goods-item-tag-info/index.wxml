<view
  class="c-goods-layout__tag-info {{ containerClass }}"
  wx:if="{{ isShowCountdown || isShowExtraTag }}"
>
  <theme-view wx:if="{{ infoData.closeCoundown ? !infoData.closeCoundown : isShowCountdown }}" custom-class="c-goods-layout__tag-info-bg" bg="general" gradient gradientDeg="90">
    <text class="discount__tag" wx:if="{{ isShowTagText }}">{{ goodsInfo.tag }}</text>

    <view class="discount__tag-countdown">
      <text class="countdown-text" wx:if="{{ infoData.countdownText || countdownText }}">{{ infoData.countdownText || countdownText }}</text>

      <countdown
        bind:countdown-end="countdownEnd"
        class="countdown-container"
        type="{{ countdownType }}"
        countdown="{{ countdown }}"
        controlled="{{ controlled }}"
        countdown-data="{{ infoData.countdownData || pageData[pageData.countdownKey][countdown] }}"
        time-separator="{{ timeSeparator }}"
      />
    </view>
  </theme-view>

  <!-- 左下角的 tag -->
  <view
    class="c-goods-layout__tag-extra"
    wx:if="{{ isShowExtraTag }}"
  >
    {{ goodsInfo.extraTagText }}
  </view>
</view>
