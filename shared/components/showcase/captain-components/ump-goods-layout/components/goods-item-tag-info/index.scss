.c-goods-layout__tag-info {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  color: #fff;
  text-align: left;

  &-bg {
    padding: 0 12px;
    background-color: #fd3e20;
  }

  &.big,
  &.hybrid-big {
    height: 40px;
    line-height: 40px;

    .discount__tag-countdown {
      float: right;
      height: 40px;
    }

    .countdown-text {
      height: 16px;
      font-size: 12px;
      line-height: 16px;
      color: #fff;
      display: block;
      margin: 5px 0 0;
      opacity: 0.6;
    }
  }

  &.small,
  &.list,
  &.hybrid-list {
    display: block;

    .c-goods-layout__tag-info-bg {
      padding: 0 12px;
    }

    .discount__tag-countdown {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
    }

    &.no-countdown-text {
      .discount__tag-countdown {
        justify-content: center;
      }
    }

    .countdown-text {
      font-size: 12px;
      color: #fff;
      display: block;
      line-height: 25px;
      height: 24px;
      margin: 0;
      opacity: 0.6;
    }
  }

  &.no-countdown {
    height: 0;
  }
}

.discount__tag {
  font-size: 20px;
  font-weight: bold;
}

.c-goods-layout__tag-extra {
  position: absolute;
  left: 0;
  bottom: 100%;
  margin: 0 0 6px 6px;
  padding: 0 4px;
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  line-height: 18px;
  color: #fff;
}
