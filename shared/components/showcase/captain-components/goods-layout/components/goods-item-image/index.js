import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import paidContentBehavior from '../../behaviors/paid-content-behavior';

import { LAYOUT_MAP, IMAGE_RATIO, SKELETON_IMAGE } from '../../constants';

Component({
  behaviors: [paidContentBehavior],

  options: {
    addGlobalClass: true,
    multipleSlots: true,
  },

  externalClasses: ['photo-class', 'image-class'],

  properties: {
    layout: Number,

    imageUrl: {
      type: String,
      observer() {
        const { curImageUrl, skeletonShow } = this.computeImageUrl();

        this.setData({
          curImageUrl,
          skeletonShow,
        });
      },
    },

    soldoutFlag: String,

    imageFillStyle: {
      type: Number,
      value: 2,
    },

    imageRatio: {
      type: Number,
      value: 1,
    },

    /**
     * 是否售罄
     */
    isSoldout: {
      type: Boolean,
      value: false,
      observer() {
        this.setData({
          photoClass: this.computePhotoClass(),
        });
      },
    },

    /**
     * 活动是否结束
     */
    isEnd: {
      type: Boolean,
      value: false,
      observer() {
        this.setData({
          photoClass: this.computePhotoClass(),
        });
      },
    },

    /**
     * 是否隐藏图片售罄、结束状态
     */
    hideImageStatus: Boolean,

    isWaterfall: Boolean,

    goodsInfo: Object,
  },

  data: {
    imageFillMode: 'aspectFit',
    photoClass: '',
    photoStyle: null,
    curImageUrl: '',
    skeletonShow: true,
    couponData: {},
    containerStyle: '',
  },

  attached() {
    const { curImageUrl, skeletonShow } = this.computeImageUrl();
    const { goodsInfo } = this.data;
    this.setData({
      imageFillMode: this.computeImageFillMode(),
      photoClass: this.computePhotoClass(),
      photoStyle: this.computePhotoStyle(),
      curImageUrl,
      skeletonShow,
      couponData: goodsInfo && goodsInfo.couponData,
    });
  },

  ready() {
    /**
     * 如果是优惠券 计算优惠券缩小比例
     */
    const { goodsInfo, layout } = this.data;
    if (goodsInfo && goodsInfo.isCoupon && +layout !== LAYOUT_MAP.BIG) {
      const query = this.createSelectorQuery();
      query
        .select('#points-coupon-box')
        .boundingClientRect((rect) => {
          if (rect && rect.width) {
            // 优惠券高度显示占比为90% 所需的缩放比
            const rate = ((rect.width * 0.9) / 165).toFixed(2);
            this.setData({
              containerStyle: `transform: scale(${rate})`,
            });
          }
        })
        .exec();
    }
  },

  methods: {
    computeImageUrl() {
      const {
        // 骨架屏图片
        imageUrl = SKELETON_IMAGE,
        layout,
        goodsInfo: { skeleton = false },
      } = this.data;

      if (skeleton) {
        return {
          skeletonShow: true,
          curImageUrl: '',
        };
      }

      let imgPrefix = '!400x0.jpg';

      if (+layout === LAYOUT_MAP.BIG || +layout === LAYOUT_MAP.HYBRID) {
        imgPrefix = '!730x0.jpg';
      }

      return {
        skeletonShow: false,
        curImageUrl: cdnImage(imageUrl, imgPrefix),
      };
    },

    /**
     * 计算图片 imageFillMode
     */
    computeImageFillMode() {
      const { imageFillStyle = 2, isWaterfall } = this.data;

      // 瀑布流高度自适应
      if (isWaterfall) return 'widthFix';

      return +imageFillStyle === 2 ? 'aspectFit' : 'aspectFill';
    },

    /**
     * 计算售罄 class
     */
    computePhotoClass() {
      const { isSoldout, isEnd, hideImageStatus } = this.data;
      if (hideImageStatus) return '';
      /* eslint-disable-next-line */
      return isSoldout ? 'photo--soldout' : isEnd ? 'photo--end' : '';
    },

    computePhotoStyle() {
      const { imageRatio = 1, isWaterfall, soldoutFlag } = this.data;
      const curImageRatio = imageRatio;
      const soldOutStyle = `--soldOutBgColor: ${
        soldoutFlag ? '' : 'rgba(0, 0, 0, 0.3)'
      } ;--soldOutImageUrl: url(${
        soldoutFlag ||
        'https://img01.yzcdn.cn/v2/image/wsc-wap/showcase/soldout.png'
      });`;

      if (isWaterfall) return soldOutStyle;

      const [w, h] = IMAGE_RATIO[curImageRatio].split(':');

      return `padding-top: ${
        (+h / +w).toFixed(4) * 100 + '%'
      }; ${soldOutStyle}`;
    },
  },
});
