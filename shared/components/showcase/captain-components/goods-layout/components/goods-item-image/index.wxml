<view class="photo {{ photoClass }} {{ skeletonShow ? 'photo-skeleton-bg' : '' }} photo-class" style="{{ photoStyle }}">
  <!-- <image
    wx:if="{{ imageFillStyle === 2 }}"
    class="photo__image-bg"
    mode="aspectFill"
    lazy-load="true"
    src="{{ curImageUrl }}"
  /> -->
  <image class="photo__image image-class" mode="{{ imageFillMode }}" lazy-load="true" src="{{ curImageUrl }}" />

  <view class="coupon-container" id="points-coupon-box" wx:if="{{ goodsInfo.isCoupon }}">
    <view class="coupon-bg" style="{{containerStyle}}">
      <view class="coupon-title">{{ couponData.title }}</view>
      <view wx:if="{{!couponData.isExchange}}" class="price-box {{ couponData.bigNum ? 'small-font' : '' }} {{ couponData.biggerNum ? 'smaller-font' : '' }}">
        <view>{{ couponData.value }}</view>
        <view class="unit">{{ couponData.unit }}</view>
      </view>
      <view wx:else class="desc-box">
        <text class="desc-text">商品</text>
        <text class="desc-text">兑换</text>
      </view>
      <view class="coupon-desc">{{ couponData.conditionText }}</view>
    </view>
  </view>
  <image wx:elif="{{ isPaidContent && mediaIconUrlAndFreeText[0] }}" class="photo__media-icon" src="{{ mediaIconUrlAndFreeText[0] }}" mode="aspectFill" />
  <slot name="tag-info" />
  <slot name="corner-mark" />
  <slot name="label" />
</view>
