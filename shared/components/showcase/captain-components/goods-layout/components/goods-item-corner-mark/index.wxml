<view class="corner-mark-wrap">
  <block wx:if="{{ type != 4 }}">
    <view class="corner-back corner-back--{{ type }} back-type-class"></view>
    <van-icon
      class="corner-mark corner-mark--icon corner-mark--{{ type }} mark-type-class"
      name="{{ iconName }}"
      custom-class="van-icon"
      color="{{ themeColor }}"
    />
  </block>
  
  <image wx:else src="{{ markImage }}" mode="widthFix" class="corner-mark corner-mark--custom"></image>
</view>
