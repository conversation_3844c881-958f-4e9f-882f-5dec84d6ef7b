/* eslint-disable */
import Theme<PERSON>ehavior from 'shared/common/components/theme-view/behavior';
import {
  CORNER_TYPE
} from '../../constants';
/* eslint-enable */

Component({
  options: {
    addGlobalClass: true
  },

  externalClasses: ['mark-type-class', 'back-type-class'],

  behaviors: [ThemeBehavior],

  properties: {
    /**
     * 角标类型
     */
    type: {
      type: Number,
      value: 1
    },

    /**
     * 角标自定义图片
     */
    markImage: String
  },

  data: {
    iconName: ''
  },

  attached() {
    const { type } = this.data;

    if (+type !== 4) {
      this.setData({
        iconName: CORNER_TYPE[type]
      });
    }
  }
});
