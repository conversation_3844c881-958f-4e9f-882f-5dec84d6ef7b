<view wx:if="{{ infoData.showTags }}" class="c-goods-layout__info-goods-tag {{exhibitionTag ? 'good_info_tag' : ''}}">
  <view class="goods-tag  {{ arrowDirection ? '' : 'exhibition-tag'}}"   catchtap="showMoreTag" style="flex-wrap:{{exhibitionTag? 'wrap': 'nowrap'}}">
    <theme-view
      wx:for="{{ curTagsList }}"
      wx:for-item="item"
      wx:for-index="index"
      wx:if="{{ item.label }}"
      wx:key="index"
      border="{{ item.theme === 'primary' ? item.color || 'var(--ump-main-bg, #323233)' : 'var(--ump-tag-bg, #f2f2ff)' }}"
      color="{{ item.theme === 'primary' ? 'var(--ump-main-text, #fff)' : item.color || 'var(--ump-tag-text, #323233)' }}"
      bg="{{ item.theme === 'primary' ? item.color || 'var(--ump-main-bg, #323233)' : 'var(--ump-tag-bg, #f2f2ff)' }}"
      custom-class="goods-tag_item"
      style="flex: none"
    >

      <theme-view wx:if="{{ item.preLabel }}" border="var(--ump-border, #C9C9FF)" custom-class="goods-tag_item--sign" class="good-sign">{{ item.preLabel }}</theme-view>

      <view class="goods-tag_item--padding">{{ item.label }}</view>
    </theme-view>
  </view>
  <view class=" goods-tag-img {{ arrowDirection ? 'goods-tag-down' : ''}}" catchtap="showMoreTag" wx:if="{{ showDropDown }}">
  </view>
</view>