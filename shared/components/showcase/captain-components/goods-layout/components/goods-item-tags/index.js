import goodsItemInfoBehavior from '../../behaviors/goods-item-info-behavior';

import { LAYOUT_MAP } from '../../constants';

Component({
  behaviors: [goodsItemInfoBehavior],

  externalClasses: ['custom-class'],

  properties: {
    goodsInfo: {
      type: Object,
      value: {},
    },
    itemIndex: Number,
    exhibitionTag: Boolean,
  },

  options: {
    addGlobalClass: true,
  },

  data: {
    curTagsList: [],
    showDropDown: false,
    arrowDirection: true,
    tagListWidth: '',
    preLabelListWidth: '',
  },

  attached() {
    this.data.exhibitionTag
      ? this.getTagList()
      : this.setData({
          curTagsList: this.computedTagsList(),
        });
  },

  methods: {
    computedTagsList() {
      const {
        itemIndex,
        layout,
        goodsInfo: { tagsList = [] },
      } = this.data;

      let num = 3;
      // 一行三个、横向滚动 只展示1个标
      if (layout === LAYOUT_MAP.THREE || layout === LAYOUT_MAP.SWIPE) {
        num = 1;
      }
      // 一行两个、列表 只展示2个标
      if (layout === LAYOUT_MAP.SMALL || layout === LAYOUT_MAP.LIST) {
        num = 2;
      }

      if (layout === LAYOUT_MAP.HYBRID) {
        num = itemIndex % 3 === 0 ? 3 : 2;
      }

      return tagsList.splice(0, num);
    },

    getTagList() {
      const {
        goodsInfo: { tagsList = [] },
      } = this.data;
      this.setData({
        curTagsList: tagsList,
      });

      wx.createSelectorQuery()
        .in(this)
        .selectAll('.goods-tag')
        .boundingClientRect((rect) => {
          this.setData({
            tagListWidth: rect[0].width,
          });
        })
        .exec();

      wx.createSelectorQuery()
        .in(this)
        .selectAll('.good-sign')
        .boundingClientRect((rect) => {
          const preLabelWidth = rect.reduce((prev, cur) => {
            return cur.width + prev;
          }, 0);
          this.setData({
            preLabelListWidth: preLabelWidth,
          });
        })
        .exec();

      wx.createSelectorQuery()
        .in(this)
        .selectAll('.goods-tag_item--padding')
        .boundingClientRect((rect) => {
          const allTagWidth = rect.reduce((prev, cur) => {
            return cur.width + prev;
          }, 0);
          if (
            allTagWidth + this.data.preLabelListWidth >
            this.data.tagListWidth - rect.length * 5
          ) {
            this.setData({
              showDropDown: true,
            });
          }
        })
        .exec();
    },

    showMoreTag() {
      if (this.data.arrowDirection) {
        return this.setData({
          curTagsList: this.data.curTagsList.splice(0, 10),
          arrowDirection: false,
        });
      }
      if (!this.data.arrowDirection) {
        this.setData({
          arrowDirection: true,
        });
      }
    },
  },
});
