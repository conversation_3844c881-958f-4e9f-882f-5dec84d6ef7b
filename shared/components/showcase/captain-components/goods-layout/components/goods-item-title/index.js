import paidContent<PERSON>ehavior from '../../behaviors/paid-content-behavior';
import goodsItemInfoBehavior from '../../behaviors/goods-item-info-behavior';

import { LAYOUT_MAP, LAYOUT_TYPE, TEXT_STYLE, SIZE_TYPE, SIZE_MAP } from '../../constants';

Component({
  behaviors: [goodsItemInfoBehavior, paidContentBehavior],

  externalClasses: ['custom-class'],

  properties: {
    goodsInfo: {
      type: Object,
      value: {},
      observer() {
        this.setData({
          isShowSubTitle: this.computeIsShowSubTitle(),
          isShowTitleTag: this.computeIsShowTitleTag()
        });
      }
    },
    goodsIndex: Number
  },

  options: {
    addGlobalClass: true
  },

  data: {
    titleStyle: '',
    textAlignStyle: '',
    isShowTitle: false,
    isShowSubTitle: false,
    isShowTitleTag: false,
    infoTitleClass: ''
  },

  attached() {
    this.setData({
      titleStyle: this.computeTitleStyle(),
      textAlignStyle: this.computeTextAlignStyle(),
      isShowTitle: this.computeIsShowTitle(),
      isShowSubTitle: this.computeIsShowSubTitle(),
      isShowTitleTag: this.computeIsShowTitleTag(),
      infoTitleClass: this.computeInfoTitleClass()
    });
  },

  methods: {
    // 一大二小：是否为大
    computeHybridClass() {
      return this.data.goodsIndex % 3 === 0 ? 'one-big' : 'two-small';
    },
    /**
     * 是否为小图促销样式
     */
    computeIsPromotion() {
      const { layout, sizeType } = this.data;

      return layout === LAYOUT_MAP.SMALL && sizeType === SIZE_MAP.PROMOTION;
    },

    /**
     * 计算标题加粗样式
     */
    computeTitleStyle() {
      const { textStyleType = 1 } = this.data.infoData || {};

      return `font-weight: ${TEXT_STYLE[textStyleType]}`;
    },

    /**
     * 计算标题对齐样式
     */
    computeTextAlignStyle() {
      const { textAlignType = 'left' } = this.data.infoData || {};

      return `text-align: ${textAlignType}`;
    },

    computeIsShowTitle() {
      const { layout, infoData = {} } = this.data;
      const { showTitle } = infoData;
      const isPromotion = this.computeIsPromotion();

      // 促销模式不显示标题
      if (isPromotion) return false;

      // 详细列表始终显示标题
      if (layout === LAYOUT_MAP.LIST) return true;

      return showTitle;
    },

    computeIsShowSubTitle() {
      const { layout, goodsInfo = {}, infoData = {} } = this.data || {};

      if (!infoData) return false;

      const { showSubTitle = false } = infoData;
      const isPromotion = this.computeIsPromotion();
      const { subTitle = '' } = goodsInfo;

      if (isPromotion) return false;

      if (
        layout === LAYOUT_MAP.SMALL ||
        layout === LAYOUT_MAP.HYBRID ||
        layout === LAYOUT_MAP.THREE ||
        layout === LAYOUT_MAP.SWIPE
      ) {
        return showSubTitle;
      }

      return subTitle.length !== 0 && showSubTitle;
    },

    computeIsShowTitleTag() {
      const { layout, goodsInfo = {} } = this.data || {};

      const { showTitleTag = true, titleTagText = '' } = goodsInfo;

      if (layout === LAYOUT_MAP.THREE || layout === LAYOUT_MAP.SWIPE) return false;

      return showTitleTag && titleTagText;
    },

    computeInfoTitleClass() {
      const { layout, sizeType, goodsIndex } = this.data;
      const isShowTitle = this.computeIsShowTitle();
      const isShowSubTitle = this.computeIsShowSubTitle();
      const hybridClass = this.computeHybridClass()

      const classArr = [
        `has-title-${+isShowTitle}`,
        `has-subtitle-${+isShowSubTitle}`,
        `${LAYOUT_TYPE[layout]}`,
        `${SIZE_TYPE[sizeType]}`,
        `${hybridClass}`
      ];
      if (+layout === LAYOUT_MAP.HYBRID) {
        const hybridSize = goodsIndex % 3 === 0 ? 'big' : 'small';
        classArr.push(`hybrid-${hybridSize}`)
      }

      return classArr.join(' ');
    }
  }
});
