@mixin multi-ellipsis ($lines) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}

.c-goods-layout__info-title {
  margin: 12px 0 0;

  &.list {
    display: block;
  }

  &.hybrid {


    &.one-big {
      .title {
        font-size: 16px;
      }
    }

    &.two-small {
      .title {
        height: 34px;
        max-height: 34px;
        font-size: 13px;
        line-height: 17px;
        margin-bottom: 4px;
        @include multi-ellipsis(2)
      }

      .sub-title {
        font-size: 12px;
      }
    }
  }


  .title {
    max-height: 20px;
    margin-bottom: 4px;
    font-size: 15px;
    line-height: 20px;
    vertical-align: middle;
    height: 20px;
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    color: #323232;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .title-tag {
    display: inline-block;
    vertical-align: middle;
    height: 16px;
    line-height: 16.5px;
    padding: 0 4px;
    border-radius: 2px;
    background-color: rgba(255, 68, 68, 0.1);
    color: #f44;
    font-size: 12px;
    text-align: center;
    margin: -2px 4px 0 0;
    border-radius: 16px;
  }

  .sub-title {
    font-size: 14px;
    line-height: 17px;
    color: #969799;
    height: 17px;
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .goods-type {
    display: inline-block;
    padding: 0 5px;
    border-radius: 2px;
    border: 1px solid #f44;
    font-size: 12px;
    color: #f44;
  }

  &.has-title-1.has-subtitle-0 {
    .title {
      margin-bottom: 0;
    }
  }

  &.big {
    .title {
      font-size: 16px;
    }
  }

  &.small,
  &.three,
  &.swipe {
    margin: 8px 0 0;
    .title {
      height: 34px;
      max-height: 34px;
      font-size: 13px;
      line-height: 17px;
      -webkit-line-clamp: 2;
      margin-bottom: 4px;
    }
    .sub-title {
      font-size: 12px;
    }
  }

  &.small,
  &.three,
  &.swipe {
    .title {
      height: 34px;
      max-height: 34px;
      font-size: 13px;
      line-height: 17px;
      @include multi-ellipsis(2)
    }
  }
  
  &.small,
  &.list {
    .title {
      font-size: 13px;
      line-height: 17px;
      height: 34px;
    }
  }

  &.hybrid {
    &-big {
      .title {
        font-size: 16px;
      }
    }
    &-small {
      .title {
        height: 34px;
        line-height: 17px;
        font-size: 13px;
        max-height: 34px;
        margin-bottom: 4px;
        -webkit-line-clamp: 2;
      }
      .sub-title {
        font-size: 12px;
      }
    }
  }

  &.list {
    display: block;
    .title {
      font-size: 14px;
      line-height: 20px;
      margin-top: 12px;
      max-height: 40px;
      font-size: 14px;
      line-height: 18px;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }


  &.tag-left {
    margin: 0 0 10px 0;
  }
}