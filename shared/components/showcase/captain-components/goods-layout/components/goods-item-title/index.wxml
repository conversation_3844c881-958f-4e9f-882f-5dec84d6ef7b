<view wx:if="{{ isShowTitle || isShowSubTitle }}" class="c-goods-layout__info-title custom-class {{ infoTitleClass }}" style="{{ textAlignStyle }}">
  <view class="title" wx:if="{{ isShowTitle }}" style="{{ titleStyle }}">
    <block wx:if="{{ !goodsInfo.skeleton }}">
      <theme-view custom-class="title-tag" wx:if="{{ isShowTitleTag }}" color="general" bg="general" opacity="0.1">
        {{ goodsInfo.titleTagText }}
      </theme-view>
      {{ goodsInfo.title }}
    </block>
    <cap-skeleton wx:else rows="{{ layout == 0 || layout == 2 ? 1 : 2 }}" font-size="{{ 14 }}" line-height="{{ 20 }}" />
  </view>
  <view class="sub-title" wx:if="{{ isShowSubTitle }}">
    <block wx:if="{{ !goodsInfo.skeleton }}">{{ goodsInfo.subTitle }}</block>
    <cap-skeleton wx:else />
  </view>
  <theme-view wx:if="{{ isPaidContent && goodsType }}" custom-class="goods-type" color="general" border="general">
    {{ goodsType }}
  </theme-view>
</view>