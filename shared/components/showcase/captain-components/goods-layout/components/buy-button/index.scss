$bigButtonSize: 24px;
$smallButtonSize: 18px;
$red: #f44;

.btn-userinfo {
  overflow: visible;
}

.goods__btn-wrap.big {
  .icon-btn {
    .van-icon {
      font-size: $bigButtonSize;
      line-height: $bigButtonSize;
      height: $bigButtonSize;
    }
  }

  .cap-btn {
    .van-button {
      line-height: $bigButtonSize;
      height: $bigButtonSize;
      padding: 0 7px;
    }
  }

  .btn-3,
  .btn-8 {
    .van-button {
      border-radius: 12px;
    }
  }
  .btn-2.icon-btn .van-icon {
    font-size: 20px;
    line-height: 20px;
    height: 20px;
  }
}

.goods__btn-wrap.small {
  .icon-btn {
    .van-icon {
      font-size: $smallButtonSize;
      line-height: $smallButtonSize;
      height: $smallButtonSize;
    }
  }

  .cap-btn {
    .van-button {
      line-height: 18px;
      height: $smallButtonSize;
      font-size: 12px;
      padding: 0 5px;
    }
  }

  .btn-3,
  .btn-8 {
    .van-button {
      border-radius: 10px;
      padding: 0 7px;
    }
  }
}

.icon-btn {
  .van-icon {
    color: $red;
  }
}

.btn-4,
.btn-8 {
  .van-button {
    font-size: 14px;
    border-color: $red;
    color: $red;
    min-width: auto;
    cursor: pointer;
    border-radius: 2px;
    background-color: inherit;
  }
}

.btn-3,
.btn-7 {
  .van-button {
    min-width: auto;
  }
}

.btn-9 {
  .van-button {
    width: 72px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    padding: 0;
    border-radius: 12px;
    transform: translateY(8px);
  }
}
