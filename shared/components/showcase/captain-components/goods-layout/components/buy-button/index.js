/* eslint-disable */
import ThemeBehavior from 'shared/common/components/theme-view/behavior';
/* eslint-enable */

Component({
  options: {
    addGlobalClass: true
  },

  externalClasses: ['btn-wrap-class'],

  behaviors: [ThemeBehavior],

  properties: {
    /**
     * 按钮类型
     */
    type: {
      type: Number,
      value: 1
    },

    /**
     * 自定义按钮文字
     */
    buttonText: String,

    redirectType: Number,

    appId: String,

    alias: String,

    extraData: {
      type: Object,
      value: {}
    },

    buttonSize: String,

    needWxAuth: {
      type: Boolean,
      value: true
    }
  },

  methods: {
    handleGoodsBuy() {
      const { redirectType } = this.data;

      if ((redirectType & 1) === 0) return;
      this.triggerEvent('user-authorize-change', false);
      this.triggerEvent('buy');
    },

    handleUserAuthorizeOpen() {
      this.triggerEvent('user-authorize-change', true);
    },

    handleUserAuthorizeClose() {
      this.triggerEvent('user-authorize-change', false);
    },

    /**
     * 啥也不干
     */
    handleTap() {
    }
  }
});

