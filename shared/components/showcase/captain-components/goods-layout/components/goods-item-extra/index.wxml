<block>
  <view class="goods-extra-wrapper" wx:if = "{{ goodsInfo.showExtra }}">  
    <block>
      <block wx:if="{{goodsInfo.extra && goodsInfo.extra.length > 0}}">
        <view 
          wx:for="{{ goodsInfo.extra }}"
          wx:for-item="item"
          wx:for-index="index"
          wx:key="index"
          style="color: {{item.color ? item.color: '#ff720d'}}"
          class="goods-extra">
          {{item.text}}
        </view>
      </block>
      <view class="goods-extra empty" wx:else />
    </block>
  </view>
</block>

