<view class="c-goods-layout__more {{ buttonTypeClass }} custom-class" style="{{ buttonMarginStyle }}">
  <van-cell-group
    wx:if="{{ buttonType === 2 || buttonType === 3 }}"
    border="{{ false }}"
  >
    <van-cell
      title="{{ buttonType === 3 ? moreText : '' }}"
      value="{{ buttonType === 2 ? moreText : '' }}"
      is-link
      url="{{ moreUrl }}"
    />
  </van-cell-group>

  <!-- 横向滑动 查看更多 -->
  <view wx:elif="{{ buttonType === 5 }}" class="more-link--5">
    <view class="more-text">
      <van-loading wx:if="{{ loading }}" />

      <block wx:else>
        <view class="arrow-left-icon" />
        <text>查看更多</text>
      </block>
    </view>
  </view>

  <!-- 横向滑动 查看全部 -->
  <navigator wx:elif="{{ buttonType === 6 }}" url="{{ moreUrl }}" class="more-link--6" style="margin-right: {{ goodsMargin }}px;">
    <view class="more-text">
      {{ moreText }}
      <view class="arrow-right-icon" />
    </view>
  </navigator>

  <navigator wx:else url="{{ moreUrl }}">
    <van-button
      custom-class="van-button"
      type="default"
      size="normal"
      block
    >
      {{ moreText }}
    </van-button>
  </navigator>
</view>
