const BUTTON_TYPE_MAP = {
  SWIPE_LOADING: 5,
  SWIPE: 6
};

Component({
  externalClasses: ['custom-class'],

  properties: {
    moreUrl: String,

    buttonType: {
      type: Number,
      value: 1
    },

    goodsMargin: {
      type: Number,
      value: 10
    },

    moreText: {
      type: String,
      value: '查看更多'
    },

    loading: Boolean
  },

  data: {
    buttonTypeClass: ''
  },

  attached() {
    this.setData({
      buttonTypeClass: this.computeButtonTypeClass(),
      buttonMarginStyle: this.computeButtonMargin()
    });
  },

  methods: {
    computeButtonTypeClass() {
      return `c-goods-layout__more--${this.data.buttonType}`;
    },

    computeButtonMargin() {
      const { goodsMargin, buttonType } = this.data;
      const halfMargin = `${goodsMargin / 2}px`;

      let buttonMarginStyle;

      if (buttonType === BUTTON_TYPE_MAP.SWIPE || buttonType === BUTTON_TYPE_MAP.SWIPE_LOADING) {
        buttonMarginStyle = `padding-top: ${halfMargin};padding-bottom: ${halfMargin};margin-left: ${halfMargin};`;
      } else {
        buttonMarginStyle = `margin-top: ${halfMargin};`;
      }

      return buttonMarginStyle;
    }
  }
});
