<view wx:if="{{ infoData.showPrice || infoData.showOriginPrice || infoData.showBuyButton }}" class="c-goods-layout__info-price custom-class {{ showcaseClass }} {{ containerClass }}">
  <view class="price-info" style="{{ priceInfoStyle }}">
    <block wx:if="{{ isPointsGoods }}">
      <view class="points-price">
        <van-icon name="exchange" class="van-icon-exchange" />
        <theme-view class="points-price__value" color="main-bg">
          {{ goodsInfo.pointsPrice }}
        </theme-view>
        <text class="points-price__unit">{{ goodsInfo.customUnit || '积分' }}</text>
        <block wx:if="{{ goodsInfo.remainPrice }}">
          <text class="points-price__unit">+</text>
          <theme-view color="main-bg" class="points-price__value">
            {{ goodsInfo.remainPrice }}
          </theme-view>
          <text class="points-price__unit">元</text>
        </block>
      </view>
    </block>
    <block wx:else>
      <view wx:if="{{ infoData.showPrice }}" class="sale-price" style="color: var(--price, #323233)">
        <block wx:if="{{ !goodsInfo.skeleton }}">
          <text wx:if="{{ isPaidContent && goodsInfo.isFree && mediaIconUrlAndFreeText[1] }}" class="free">
            {{ mediaIconUrlAndFreeText[1] }}
          </text>
          <block wx:else>
            <text class="price-tag">¥</text>
            <text class="price-num" style="{{ priceNumStyle }}">{{ goodsPrice }}</text>
            <text class="price-unit" wx:if="{{ hasUnit }}">万</text>
            <text wx:if="{{ goodsInfo.isMinPrice }}" class="sale-price__min">起</text>
            <slot name="price-label" />
          </block>
          <theme-view wx:if="{{ goodsInfo.priceLabel }}" bg="vice-bg" opacity="0.5" custom-class="price-sign">
            {{ goodsInfo.priceLabel }}
          </theme-view>
          <text class="sold-num" wx:if="{{ showSoldNum }}">{{ goodsInfo.totalSoldNum }}件已售</text>
        </block>
        <cap-skeleton wx:else type="rect" rect-style="width: 30px; height: {{ infoData.textAlignType == 'center' || !infoData.showBuyButton ? 16 : 20 }}px;" />
      </view>
      <view wx:if="{{ infoData.showOriginPrice && goodsInfo.originPrice && (showOriginPriceByGoodsInfo ? goodsInfo.showOriginPrice : true) }}" class="sale-price__origin">
        <block wx:if="{{ !goodsInfo.skeleton }}">
          ¥{{ goodsInfo.originPrice }}
          <text wx:if="{{ goodsInfo.isMinPrice }}">起</text>
        </block>
        <cap-skeleton wx:else type="rect" rect-style="width: 20px; height: 12px;" />
      </view>
    </block>
  </view>
  <buy-button bind:on-load="btnLoad" wx:if="{{ showItemBuyButton }}" id="buy-button" class="buy-button {{ isPromotion ? 'buy-button--promotion' : '' }}" type="{{ buyButtonType }}" button-text="{{ goodsInfo.buttonText || infoData.buttonText }}" redirect-type="{{ redirectType }}" app-id="{{ appId }}" alias="{{ goodsInfo.alias }}" extra-data="{{ extraData }}" button-size="{{ buttonSize }}" btn-wrap-class="goods__btn-wrap" need-wx-auth="{{ !infoData.isPlugin }}" bind:buy="handleGoodsBuyClick" />
  <goods-item-extra goods-info="{{ goodsInfo }}" />
</view>