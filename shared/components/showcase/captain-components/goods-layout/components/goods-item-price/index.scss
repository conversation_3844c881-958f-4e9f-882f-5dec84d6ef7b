.c-goods-layout__info-price {
  position: relative;
  width: 100%;

  .price-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-content: center;
    vertical-align: baseline;
  }

  .buy-button {
    display: inline-block;
    position: absolute;
    font-size: 0;
    top: 12px;
    right: 0;

    &--promotion {
      top: 0;
      right: -10px;
    }
  }

  &.size--big {
    .price-info {
      height: 44px;
    }
    .buy-button {
      top: 10px;
    }
  }

  &.size--small {
    .price-info {
      height: 44px;
    }
  }

  &.member-goods {

    &.big,
    &.hybrid.size--big {
      .member-tag {
        margin-top: 3px;
      }
    }

    &.center {
      .member-tag {
        margin-left: auto !important;
        margin-right: auto !important;
      }
    }

    .sale-price {
      white-space: nowrap;
      margin-right: 8px;

      &>view {
        display: inline;
      }
    }
  }
  &.small {
    .sale-price {
      font-size: 18px;
      line-height: 18px;
      height: 18px;
    }
    .price-tag,
    .price-unit {
      font-size: 12px;
    }
  }

  &.three,
  &.swipe {
    min-height: 42px;
    .price-info {
      height: 42px;
    }

    .sale-price {
      font-size: 16px;
      line-height: 16px;
      height: 16px;
    }
    
    .price-tag,
    .price-unit {
      font-size: 11px;
    }

    .sale-price__origin {
      font-size: 11px;
    }

    &.center {
      .sale-price {
        justify-content: center;
      }
    }

    .price-tag,
    .price-unit,
    .sale-price__origin {
      font-size: 11px;
    }
  }

  &.small {
    .goods__btn-wrap {
      // margin-top: 4px;
    }
  }

  &.list,
  &.small {

    .price-tag,
    .price-unit,
    .sale-price__origin {
      font-size: 12px;
    }

    .sale-price {
      height: 18px;
      font-size: 18px;
      line-height: 18px;

    }
  }

  &.list {
    bottom: 0 !important;
    position: absolute;
    .price-info {
      transform: translateY(4px);
    }
    .sale-price {
      font-size: 18px;
      height: 18px;
      line-height: 18px;
    }
    .price-tag, .price-unit, .sale-price__origin {
      font-size: 12px;
    }
    .sale-price__origin {
      transform: translateY(-2px);
    }
  }

  &.center {
    text-align: center;

    .price-info {
      height: 44px;
      justify-content: center;
    }

    .buy-button {
      margin-top: 0;
      position: relative;
      top: 0;
      margin-bottom: 10px;
      display: block;
    }
  }

}

.sale-price,
.sale-price__origin {
  height: 20px;
  color: #f44;
  font-size: 20px;
  line-height: 20px;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.sale-price {
  height: 20px;
  line-height: 20px;
  font-size: 20px;
  // margin-right: 8px;
  vertical-align: baseline;
  
  &__origin {
    height: 20px;
    line-height: 24px;
    font-size: 12px;
    color: #c8c9cc;
    font-weight: 400;
    vertical-align: baseline;
    text-decoration: line-through;
    font-weight: normal !important;
  }

  &__min {
    font-size: 12px;
  }

  .free {
    font-size: 14px;
  }
}

.price-tag,
.price-unit {
  height: 14px;
  align-self: center;
  font-size: 14px;
  margin-right: 2px;
  position: relative;
  bottom: 0.05em;
}

.price-num {
  // margin-right: 4px;
}

.price-unit {
  margin-right: 0 !important;
  margin-left: 2px;
}

.price-sign {
  display: inline-block;
  margin-left: 4px;
  padding: 0 4px;
  vertical-align: middle;
  line-height: 16px;
  font-size: 12px;
  text-align: center;
  border-radius: 2px;
  background-color: #fde6e9;
}

.sold-num {
  margin-left: 4px;
  font-size: 12px;
  color: #9b9b9b;
}

// 积分商品价格
.points-price {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  line-height: 12px;
  min-height: 20px;

  .van-icon-exchange {
    font-size: 12px;
    color: #999;
    position: relative;
    top: 1px;
    margin-right: 1px;
  }

  &__unit {
    font-size: 12px;
    color: #999;
  }

  &__value {
    font-size: 14px;
    color: #f44;
  }
}