import { SIZE_MAP, LAYOUT_MAP } from '../../constants';
import paidContentBehavior from '../../behaviors/paid-content-behavior';
import goodsItemPriceBehavior from '../../behaviors/goods-item-price-behavior';

Component({
  behaviors: [goodsItemPriceBehavior, paidContentBehavior],

  properties: {
    goodsInfo: {
      type: Object,
      value: {},
      observer() {
        if (!this.data.infoData) return;
        this.initPriceInfo();
        this.computePaidContent();
      },
    },
    showcaseClass: String,

    /** 显示原价需要同时满足 goodsItem 的 ShowOriginPrice 为 true */
    showOriginPriceByGoodsInfo: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    containerClass: '',
    buttonSize: '',
    goodsPrice: null,
    priceInfoStyle: '',
    priceNumStyle: '',
    hasUnit: false,
    isPromotion: false,
    isPointsGoods: false,
    buyButtonType: 1,
    showSoldNum: false,
    showItemBuyButton: false,
  },

  externalClasses: ['custom-class'],

  options: {
    styleIsolation: 'shared',
    multipleSlots: true,
  },

  ready() {
    this.setPriceInfoStyle();
  },

  methods: {
    btnLoad() {
      setTimeout(() => {
        // 等其 btn内文字最终渲染完毕
        this.setPriceInfoStyle();
      });
    },
    setPriceInfoStyle() {
      const {
        textStyleType = 1,
        textAlignType = 'left',
        showOriginPrice,
        showPrice,
        priceInfoStyle = '',
      } = this.data.infoData || {};

      const isPromotion = this.computeIsPromotion();
      const textStyle = +textStyleType === 2 ? 'font-weight: bold;' : '';

      const buyButtonComponent = this.selectComponent('#buy-button');
      const { layout, goodsInfo: { price } = {} } = this.data || {};
      // 价格不需要缩写
      const priceIsOriginStyle = this.isOriginPriceStyle();
      // 价格字号不需要缩小
      const maxPLen = LAYOUT_MAP.SWIPE === layout ? 4 : 5;
      const priceIsOriginSize = !(
        !priceIsOriginStyle && this.goodsPrice?.length > maxPLen
      );

      if (buyButtonComponent) {
        buyButtonComponent
          .createSelectorQuery()
          .select('.goods__btn-wrap')
          .boundingClientRect((rect) => {
            const { width = 50 } = rect || {};
            const paddingRight = isPromotion ? width - 2 : width + 2;
            const paddingStyle =
              textAlignType === 'center'
                ? 'padding-right: 0;'
                : `padding-right: ${paddingRight}px;`;
            let heightStyle = '';

            if (textAlignType === 'center' && !showPrice && !showOriginPrice) {
              heightStyle = 'height: 0;';
            }
            this.setData({
              priceInfoStyle:
                textStyle + paddingStyle + heightStyle + priceInfoStyle,
              priceNumStyle:
                textAlignType === 'center' ||
                priceIsOriginSize ||
                priceIsOriginStyle
                  ? ''
                  : 'font-size: 13px;',
              hasUnit: !priceIsOriginStyle && +(price || 0) >= 10000,
            });
          })
          .exec();
      } else {
        this.setData({
          priceInfoStyle: textStyle + priceInfoStyle,
          hasUnit: !priceIsOriginStyle && +(price || 0) >= 10000,
        });
      }
    },
    initPriceInfo() {
      this.setData({
        containerClass: this.computeContainerClass(),
        goodsPrice: this.computeSalePrice(),
        isPromotion: this.computeIsPromotion(),
        isPointsGoods: this.computeIsPointsGoods(),
        buyButtonType: this.computeBuyButtonType(),
        showSoldNum: this.computeShowSoldNum(),
        showItemBuyButton: this.computeShowBuyButton(),
      });
    },

    computeShowSoldNum() {
      const { showSoldNum = false } = this.data.infoData || {};

      const { totalSoldNum = -1 } = this.data.goodsInfo || {};

      return showSoldNum && Number(totalSoldNum) > -1;
    },

    computeIsPointsGoods() {
      const { sizeType } = this.data || {};

      return sizeType === SIZE_MAP.POINTS;
    },
    computeBuyButtonType() {
      const { buyButtonType = 1 } = this.data.infoData || {};
      const isPromotion = this.computeIsPromotion();

      if (isPromotion) {
        return 9;
      }

      return buyButtonType;
    },
  },
});
