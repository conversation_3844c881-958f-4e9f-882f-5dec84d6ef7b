export default Behavior({
  properties: {
    appId: String,

    goodsInfo: {
      type: Object,
      value: {}
    },

    infoData: {
      type: Object,
      value: {}
    },

    goodsIndex: Number,

    layout: {
      type: Number,
      value: 0
    },

    sizeType: {
      type: Number,
      value: 0
    },

    redirectType: {
      type: Number,
      value: 1
    },

    extraData: {
      type: Object,
      value: {}
    },

    textStyleType: {
      type: Number,
      value: 1
    },

    textAlignType: {
      type: String,
      value: 'left'
    }
  }
});
