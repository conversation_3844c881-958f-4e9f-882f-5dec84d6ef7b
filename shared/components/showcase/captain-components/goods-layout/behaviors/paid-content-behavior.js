import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { FREE_TEXT_MAP, CDN_ICON_URL_MAP, MEDIA_ICON_URL_MAP } from '../constants';

export default Behavior({
  properties: {
    goodsInfo: {
      type: Object,
      value: {},
      observer() {
        this.computePaidContent();
      }
    },
  },

  data: {
    isPaidContent: false,
    goodsType: '',
    mediaIconUrlAndFreeText: ['', ''],
  },

  attached() {
    this.computePaidContent();
  },

  methods: {
    computePaidContent() {
      const { goodsInfo: { owlType, showPaidContent = false } } = this.data;

      const isPaidContent = !!owlType && showPaidContent;

      this.setData({
        isPaidContent,
        goodsType: isPaidContent ? '知识付费' : '',
        mediaIconUrlAndFreeText: this.computeMediaIconUrlAndFreeText(),
      });
    },

    computeMediaIconUrlAndFreeText() {
      const { goodsInfo: { owlType, mediaType } } = this.data;

      if (owlType === 1) { // 专栏
        return [this.getCdnIconUrl('COLUMN'), ''];
      }
      if (owlType === 2) {
        switch (mediaType) {
          case 1: // 图文
            return [this.getCdnIconUrl('TEXT'), FREE_TEXT_MAP.READ];
          case 2: // 音频
            return [this.getCdnIconUrl('AUDIO'), FREE_TEXT_MAP.LISTEN];
          case 3: // 视频
            return [this.getCdnIconUrl('VIDEO'), FREE_TEXT_MAP.WATCH];
          default:
            return ['', ''];
        }
      } else if (owlType === 4) { // 直播
        return [this.getCdnIconUrl('LIVE'), FREE_TEXT_MAP.WATCH];
      }

      return ['', ''];
    },

    getCdnIconUrl(type) {
      if (!CDN_ICON_URL_MAP[type]) {
        CDN_ICON_URL_MAP[type] = cdnImage(MEDIA_ICON_URL_MAP[type], '!40x40.png');
      }
      return CDN_ICON_URL_MAP[type];
    }
  }
});
