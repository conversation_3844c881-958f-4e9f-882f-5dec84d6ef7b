/**
 * 商品公共布局 Behavior
 */
export default Behavior({
  properties: {
    appId: String,
    /**
     * 列表样式：
     * 0: 大图，1: 小图， 2: 一大两小， 3: 详细列表，5: 一行三个，6: 左右滑动
     */
    layout: {
      type: Number,
      value: 0
    },

    /**
     * 0: 卡片样式，1: 瀑布流， 2: 极简样式， 3: 促销， 4: 多图商品
     */
    sizeType: {
      type: Number,
      value: 0
    },

    /**
     * 是否显示商品名
     */
    showTitle: {
      type: Boolean,
      value: true
    },

    /**
     * 是否显示商品简介
     */
    showSubTitle: Boolean,

    /**
     * 是否显示价格
     */
    showPrice: {
      type: Boolean,
      value: true
    },

    /**
     * 是否显示购买按钮
     */
    showBuyButton: {
      type: Boolean,
      value: true
    },

    /**
     * 购买按钮样式
     */
    buyButtonType: {
      type: Number,
      value: 1
    },

    /**
     * 是否显示角标
     */
    showCornerMark: Boolean,

    /**
     * 角标样式（0: 新品, 1: 热卖, 2: NEW, 3: HOT）
     */
    cornerMarkType: {
      type: Number,
      value: 0
    },

    /**
     * 自定义角标图片
     */
    cornerMarkImage: String,

    /**
     * 图片显示比例，只有大图样式才有
     */
    imageRatio: Number,

    /**
     * 图片显示模式（1: 填充，2: 留白）
     */
    imageFillStyle: {
      type: Number,
      value: 2
    },

    /**
     * 自定义购买按钮文案
     */
    buttonText: String,

    extraData: {
      type: Object,
      value: {}
    },

    /**
     * 跳转类型，目前会涉及到商品区域和购买按钮的点击跳转逻辑
     * 我们使用二进制，定义 0 为跳转，1 为触发事件， 比如 00 最终得到 0， 即为点击商品区域和购买按钮都跳转
     * 0-全部跳转 1-点击商品跳转，按钮触发事件 2-点击商品触发事件，按钮跳转 3-全部触发事件
     */
    redirectType: {
      type: Number,
      value: 3
    },

    /**
     * 页面边距
     */
    pageMargin: {
      type: Number,
      value: 15
    },

    /**
     * 商品间边距
     */
    goodsMargin: {
      type: Number,
      value: 10
    },

    /**
     * 文本样式： 1-常规体 2-加粗体
     */
    textStyleType: {
      type: Number,
      value: 1
    },

    /**
     * 文本对齐
     */
    textAlignType: {
      type: String,
      value: 'left'
    },

    /**
     * 商品倒角： 1-直角 2-圆角
     */
    borderRadiusType: {
      type: Number,
      value: 1
    },

    // 是否展示营销标签
    showUmpTags: Boolean,

    // 营销标签展示个数
    umpTagsMaxCount: Number,

    // 列表最后一项自定义组件展示
    showLastSelfDefineItem: Boolean,
  },

  methods: {
    setLoadStatus(status) {
      this.selectComponent('#cap-goods-layout').setSwipeStatus(status);
    },

    handleGoodsBuyClick(e) {
      this.triggerEvent('buy', e.detail);
    },

    handleGoodsItemClick(e) {
      this.triggerEvent('item-click', e.detail);
    },

    handleLoadMore() {
      this.triggerEvent('load-more');
    }
  }
});
