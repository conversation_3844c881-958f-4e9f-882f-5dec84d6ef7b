import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import appLoggerBehavior from 'shared/components/showcase/behaviors/app-logger-behavior';
import goodsItemInfoBehavior from './goods-item-info-behavior';

import { BUTTON_SIZE, LAYOUT_MAP, LAYOUT_TYPE, SIZE_MAP } from '../constants';

const app = getApp();

export default Behavior({
  behaviors: [goodsItemInfoBehavior, appLoggerBehavior],

  properties: {
    goodsInfo: {
      type: Object,
      value: {},
      observer() {
        this.initPriceInfo();
      },
    },
  },

  data: {
    buttonSize: '',
  },

  attached() {
    this.initPriceInfo();
    this.setData({
      buttonSize: this.computeBuyButtonSize(),
    });
  },

  methods: {
    /**
     * 缩略价格：上万
     */
    formatPrice({ oldPrice, priceIsOriginStyle } = {}) {
      if (priceIsOriginStyle) {
        return oldPrice;
      }
      // 不到一万直接返回原数即可
      if (+oldPrice < 10000) {
        return oldPrice;
      }

      // 价格上万处理：保留2位精度 & 小数点抹零
      const price = `${Math.round(+oldPrice / 100) / 100}`;
      return price.replace(/(\d+)\.0+$/, '$1').replace(/(\d+\.\d)0+$/, '$1');
    },
    /**
     * 是否展示购买按钮
     */
    computeShowBuyButton() {
      const {
        infoData: { showBuyButton = false } = {},
        isPaidContent = false,
      } = this.data;
      return showBuyButton && !isPaidContent;
    },
    /**
     * 价格是否保持原样（不缩写）
     */
    isOriginPriceStyle() {
      const { layout = LAYOUT_MAP.BIG, goodsIndex, goodsInfo = {} } = this.data;
      if (!goodsInfo.price) {
        return true;
      }
      const price = `${goodsInfo.price}`;
      const { infoData = {} } = this.data;
      const shouldShowBuyButton = this.computeShowBuyButton();
      const isSmallBuyButton =
        [1, 2, 5, 6].indexOf(infoData.buyButtonType) > -1;
      const priceClearZero = price
        .replace(/(\d+)\.0+$/, '$1')
        .replace(/(\d+\.\d)0+$/, '$1');
      const priceLen = priceClearZero.length;
      const buttonLimit =
        (!shouldShowBuyButton || isSmallBuyButton) && priceLen < 10;
      const priceIsOriginStyle =
        [LAYOUT_MAP.BIG, LAYOUT_MAP.LIST].indexOf(layout) !== -1 ||
        (LAYOUT_MAP.HYBRID === layout && goodsIndex % 3 === 0) ||
        (LAYOUT_MAP.HYBRID === layout &&
          [1, 2].indexOf(goodsIndex % 3) !== -1 &&
          buttonLimit) ||
        (LAYOUT_MAP.SMALL === layout && buttonLimit);

      return priceIsOriginStyle;
    },
    /**
     * 计算价格：小数点为0、过万、按钮样式、布局类型。
     */
    computeSalePrice() {
      const { goodsInfo = {} } = this.data;
      if (!goodsInfo.price) return 0;
      // 价格不需要缩写
      const priceIsOriginStyle = this.isOriginPriceStyle();
      const price = `${goodsInfo.activityPrice || goodsInfo.price}`;
      const priceClearZero = price
        .replace(/(\d+)\.0+$/, '$1')
        .replace(/(\d+\.\d)0+$/, '$1');

      return this.formatPrice({ oldPrice: priceClearZero, priceIsOriginStyle });
    },

    computeContainerClass() {
      const buttonSize = this.computeBuyButtonSize();
      const { layout, infoData = {} } = this.data;
      const { showPrice, showBuyButton, textAlignType } = infoData;

      const classArr = [
        `has-price-${+showPrice}`,
        `has-btn-${+showBuyButton}`,
        `size--${buttonSize}`,
        `${LAYOUT_TYPE[layout]}`,
      ];

      if (layout !== LAYOUT_MAP.LIST) {
        classArr.push(textAlignType);
      }

      return classArr;
    },

    computeBuyButtonSize() {
      const { layout } = this.data;

      if (layout === LAYOUT_MAP.THREE || layout === LAYOUT_MAP.SWIPE) {
        return BUTTON_SIZE.SMALL;
      }
      return BUTTON_SIZE.BIG;
    },

    /**
     * 是否为小图促销样式
     */
    computeIsPromotion() {
      const { layout, sizeType } = this.data;

      return layout === LAYOUT_MAP.SMALL && sizeType === SIZE_MAP.PROMOTION;
    },

    handleGoodsBuyClick() {
      const { goodsInfo } = this.data;
      const { loggerParams = {}, extraInfo = {} } = goodsInfo;
      // 商品分组顶部加购打点 slg uuid
      const uuid = app.logger?.options?.user?.uuid || '';
      if (!isEmpty(loggerParams)) {
        const slg = extraInfo.slg || '0';
        this.ensureAppLogger('click_buy', {
          ...loggerParams,
          slg,
          uuid,
        });
      }

      this.triggerEvent('buy', goodsInfo);
    },
  },
});
