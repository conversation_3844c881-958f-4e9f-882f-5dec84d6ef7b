/* eslint-disable */
import getSystemInfo from 'shared/utils/browser/system-info';
import { setGoodsPreloadData } from 'shared/utils/goods';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import appLoggerBehavior from 'shared/components/showcase/behaviors/app-logger-behavior';
import ThemeBehavior from 'shared/common/components/theme-view/behavior';
/* eslint-enable */
import {
  SIZE_MAP,
  LAYOUT_MAP,
  LAYOUT_TYPE,
  SIZE_TYPE,
  RADIUS_TYPE_MAP,
} from './constants';

import { touch } from '../../behaviors/touch-behavior';
import countdownUtilBehavior from '../ump-goods-layout/behaviors/countdown-util-behavior';

const app = getApp();
// 将一个数组分隔为指定个数的数组
function splitArrayByRow(arr, rowNum) {
  const result = [];
  rowNum = rowNum || 2;

  for (let i = 0; i < arr.length; i += rowNum) {
    for (let j = 0; j < rowNum; j++) {
      if (!result[j]) {
        result[j] = [];
      }

      if (arr[i + j]) {
        result[j].push(arr[i + j]);
      }
    }
  }

  return result;
}

Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true,
  },

  behaviors: [touch, countdownUtilBehavior, appLoggerBehavior, ThemeBehavior],

  properties: {
    componentIndex: Number,
    appId: String,

    list: {
      type: Array,
      value: [],
      observer(newVal) {
        this.setData({
          // ?? ob obj
          waterfallGoodsList: this.computeWaterfallGoods(newVal),
        });
      },
    },
    // 组件类：member-goods, goods-recommend ...
    showcaseClass: String,

    /**
     * 列表样式：
     * 0: 大图，1: 小图， 2: 一大两小， 3: 详细列表，5: 一行三个，6: 左右滑动
     */
    layout: {
      type: Number,
      value: 0,
    },

    /**
     * 0: 卡片样式，1: 瀑布流， 2: 极简样式， 3: 促销， 4: 多图商品， 5: 卡片2， 6: 卡片2， 7: 卡片3（拼团、秒杀和限时折扣大图模式）
     */
    sizeType: {
      type: Number,
      value: 0,
    },

    /**
     * 图片填充方式：1：填充 2:留白
     */
    imageFillStyle: {
      type: Number,
      value: 2,
    },

    /**
     * 图片展示比例：0：3:2 1：1:1
     */
    imageRatio: {
      type: Number,
      value: 1,
    },

    /**
     * 是否显示角标
     */
    showCornerMark: Boolean,

    /**
     * 角标样式（0: 新品, 1: 热卖, 2: NEW, 3: HOT）
     */
    cornerMarkType: {
      type: Number,
      value: 0,
    },

    /**
     * 自定义角标图片
     */
    cornerMarkImage: String,

    /**
     * 是否展示 `查看更多` 按钮
     */
    isShowMore: Boolean,

    /**
     * `查看更多` 链接
     */
    moreUrl: String,

    moreButtonType: {
      type: Number,
      value: 1,
    },

    extraData: {
      type: Object,
      value: {},
    },

    /**
     * 跳转类型，目前会涉及到商品区域和购买按钮的点击跳转逻辑
     * 我们使用二进制，定义 0 为跳转，1 为触发事件， 比如 00 最终得到 0， 即为点击商品区域和购买按钮都跳转
     * 0-全部跳转 1-点击商品跳转，按钮触发事件 2-点击商品触发事件，按钮跳转 3-全部触发事件
     */
    redirectType: {
      type: Number,
      value: 3,
    },

    /**
     * `goods-item-info` 组件需要用到的数据
     */
    infoData: {
      type: Object,
      value: {},
    },

    pageMargin: {
      type: Number,
      value: 15,
    },

    goodsMargin: {
      type: Number,
      value: 10,
    },

    /**
     * 文本样式： 1-常规体 2-加粗体
     */
    textStyleType: {
      type: Number,
      value: 1,
    },

    /**
     * 文本对齐
     */
    textAlignType: {
      type: String,
      value: 'left',
    },

    /**
     * 商品倒角： 1-直角 2-圆角
     */
    borderRadiusType: {
      type: Number,
      value: 2,
    },

    /**
     * 是否开启横向滑动的分页
     */
    openSwipePagination: Boolean,

    swipeLoadMaxTimes: {
      type: Number,
      value: 2,
    },

    showPriceLabel: Boolean,
    heightStyle: String,

    skeletonNumber: {
      type: Number,
      value: 0,
    },

    /** 营销-DMP-默认 推荐标签展示 */
    showUmpTags: Boolean,
    umpTagsMaxCount: Number,

    /** 商品列表最后一项-自定义组件（slot: last-item-in-list） 是否展示 */
    showLastSelfDefineItem: Boolean,

    /** 是否展示标签 */
    showLabel: Boolean,

    /** 是否展示多标签 */
    exhibitionTag: Boolean,

    /** 显示原价需要同时满足 goodsItem 的 ShowOriginPrice 为 true */
    showOriginPriceByGoodsInfo: {
      type: Boolean,
      value: false,
    },
    /** 是否使用flex布局 */
    isUseFlexLayout: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    isSwipeLoadFinished: false,
    isSwipeLoading: false,
    swipeLoadTimes: 0,
    isWaterfall: false,
    layoutName: '',
    sizeName: '',
    containerClass: '',
    waterfallGoodsList: [],
    pageMarginStyle: '',
    goodsMarginStyle: '',
    borderRadiusTypeClass: '',
    skeletonGoodsConfig: {
      goodsInfo: {
        skeleton: true,
      },
      goodsIndex: 0,
    },
    soldOutFlag: '',
    flexClass: '',
  },

  attached() {
    this.getShopConfigData();
    this.setData({
      isWaterfall: this.computeIsWaterfall(),
      isSwipeLayout: this.computeIsSwipeLayout(),
      layoutName: this.computeLayoutName(),
      sizeName: this.computeSizeName(),
      containerClass: this.computeContainerClass(),
      waterfallGoodsList: this.computeWaterfallGoods(this.data.list),
      pageMarginStyle: this.computePageMarginStyle(),
      goodsMarginStyle: this.computeGoodsMarginStyle(),
      borderRadiusTypeClass: this.computeBorderRadiusTypeClass(),
      flexClass: this.computeFlexClass(),
    });
  },

  methods: {
    getShopConfigData() {
      app.getShopConfigData().then((shopConfig = {}) => {
        const { sold_out_goods_flag } = shopConfig;

        this.setData({
          soldOutFlag: sold_out_goods_flag,
        });
      });
    },
    /**
     * 页面边距
     */
    computePageMarginStyle() {
      const { pageMargin, goodsMargin } = this.data;
      const isSwipeLayout = this.computeIsSwipeLayout();
      const halfOfGoodsMargin = `-${goodsMargin / 2}px`;
      let pageMarginStyle = `padding-left: ${pageMargin}px; padding-right: ${pageMargin}px; margin-left: ${halfOfGoodsMargin};`;

      if (!isSwipeLayout) {
        pageMarginStyle += `margin-right: ${halfOfGoodsMargin}`;
      }

      return pageMarginStyle;
    },

    computeGoodsMarginStyle() {
      const { goodsMargin } = this.data;
      const halfOfGoodsMargin = `${goodsMargin / 2}px`;

      return `margin: ${halfOfGoodsMargin}`;
    },

    /**
     * 是否为瀑布流，瀑布流渲染方式不一样
     */
    computeIsWaterfall() {
      const { sizeType } = this.data;

      return SIZE_MAP.WATERFALL === +sizeType;
    },

    /**
     * 是否为横向滑动布局
     */
    computeIsSwipeLayout() {
      const { layout } = this.data;

      return LAYOUT_MAP.SWIPE === +layout;
    },

    /**
     * 计算 layout 名称
     */
    computeLayoutName() {
      const { layout } = this.data;
      return LAYOUT_TYPE[layout];
    },

    computeSizeName() {
      const { sizeType } = this.data;

      return SIZE_TYPE[sizeType];
    },

    /**
     * 计算瀑布流商品数据
     */
    computeWaterfallGoods(list) {
      let waterfallGoodsList = [];

      if (list && this.computeIsWaterfall()) {
        waterfallGoodsList = splitArrayByRow(list, 2);
      }

      return waterfallGoodsList;
    },

    computeContainerClass() {
      return [
        `layout-container--${this.computeLayoutName()}`,
        this.computeSizeName(),
      ].join(' ');
    },

    computeBorderRadiusTypeClass() {
      const { borderRadiusType = 1 } = this.data;

      return RADIUS_TYPE_MAP[borderRadiusType];
    },

    computeFlexClass() {
      const { isUseFlexLayout = false } = this.data;

      return isUseFlexLayout ? 'flex-layout ' : '';
    },

    handleGoodsItemClick(e) {
      const { index } = e.currentTarget.dataset;
      const { isWaterfall, waterfallGoodsList, list } = this.data;

      let goodsInfo;
      if (isWaterfall) {
        const [listInex, goodsIndex] = index.split('-');
        goodsInfo = waterfallGoodsList[+listInex][+goodsIndex];
      } else {
        goodsInfo = list[+index];
      }

      if (goodsInfo?.skeleton) {
        return;
      }
      const { loggerParams = {}, extraInfo = {} } = goodsInfo;
      // 商品分组顶部点击打点 slg uuid
      const slg = extraInfo.slg || '0';
      const uuid = app.logger?.options?.user?.uuid || '';
      // 商品点击 slg alg_id 上下文 透传埋点
      app.logger.setContext(
        {
          slg: extraInfo.slg || '',
          alg_id: extraInfo.alg || '',
        },
        30
      );
      if (!isEmpty(loggerParams)) {
        this.ensureAppLogger('open_goods', {
          ...loggerParams,
          slg,
          uuid,
        });
      }
      this.setLsForGoodsDetail(goodsInfo);
      this.triggerEvent('item-click', goodsInfo);
    },

    // 设置本地存储供商详页使用，图片，标题，价格
    setLsForGoodsDetail(goodsInfo) {
      const {
        picture = [],
        title = '',
        price = '0.00',
        alias = '',
        imageUrl = '',
        width,
        height,
      } = goodsInfo;
      let image = {};

      if (picture.length) {
        image = picture[0];
      } else if (imageUrl) {
        image = {
          url: imageUrl,
          width,
          height,
        };
      }

      if (image && alias) {
        setGoodsPreloadData({
          image,
          alias,
          title,
          price,
        });
      }
    },

    handleBuyClick(e) {
      this.triggerEvent('buy', e.detail);
    },

    tagInfoChange(e) {
      this.triggerEvent('tag-info-change', e.detail);
    },

    checkSwipeable() {
      const isSwipeLayout = this.computeIsSwipeLayout();
      const {
        openSwipePagination,
        swipeLoadTimes,
        isSwipeLoading,
        list,
        isSwipeLoadFinished,
        swipeLoadMaxTimes,
      } = this.data;

      return new Promise((resolve) => {
        this.createSelectorQuery()
          .select(`.goods-wrap--${list.length - 1}`)
          .boundingClientRect((rect = {}) => {
            if (!this.windowWidth) {
              const systemInfo = getSystemInfo();
              const { windowWidth = 375 } = systemInfo;
              this.windowWidth = windowWidth;
            }
            /**
             * 1. 检查横向滑动商品最后一个商品是否在屏幕内
             * 2. 滑动布局
             * 3. 开启了滑动分页
             * 4. 滑动分页次数小于最大分页次数
             * 5. 未在加载中
             * 6. 分页没有结束
             */
            if (
              rect.left >= 0 &&
              rect.right < this.windowWidth &&
              isSwipeLayout &&
              openSwipePagination &&
              swipeLoadTimes < swipeLoadMaxTimes &&
              !isSwipeLoading &&
              !isSwipeLoadFinished
            ) {
              resolve();
            }
          })
          .exec();
      });
    },

    onTouchStart(event) {
      // 横向滚动 触发
      if (!this.data.isSwipeLayout) return;

      this.checkSwipeable()
        .then(() => {
          this.touchStart(event);
          this.isTouchStart = true;
        })
        .catch(() => {});
    },

    onTouchMove(event) {
      // 横向滚动 触发
      if (!this.data.isSwipeLayout) return;

      if (this.isTouchStart) {
        this.touchMove(event);
      }
    },

    onTouchEnd() {
      // 横向滚动 触发
      if (!this.data.isSwipeLayout) return;

      if (this.isTouchStart) {
        const { direction, deltaX } = this;
        const minSwipeDistance = 60;

        if (direction === 'horizontal' && -deltaX >= minSwipeDistance) {
          // 大于最小滑动距离时开始加载
          this.setData(
            {
              isSwipeLoading: true,
              swipeLoadTimes: ++this.data.swipeLoadTimes,
            },
            () => {
              this.triggerEvent('load-more');
            }
          );
        }
        this.isTouchStart = false;
      }
    },

    setSwipeStatus(options = {}) {
      const { loading, swipeFinished, isPullDownRefresh } = options;
      const swipeLoadTimes = isPullDownRefresh ? 0 : this.data.swipeLoadTimes;

      this.setData({
        isSwipeLoading: loading,
        // 是否结束横向滑动分页加载：1. 分页次数大于2；商品来源为商品分组时
        isSwipeLoadFinished:
          swipeLoadTimes >= this.data.swipeLoadMaxTimes || swipeFinished,
        swipeLoadTimes,
      });
    },
  },
});
