export const SIZE_TYPE = {
  0: 'card',
  1: 'waterfall',
  2: 'simple',
  3: 'promotion',
  4: 'multi',
  5: 'card2',
  6: 'points',
  7: 'card-shadow',
  8: 'tag-left'
};

export const SIZE_MAP = {
  CARD: 0,
  WATERFALL: 1,
  SIMPLE: 2,
  PROMOTION: 3,
  MULTI: 4,
  CARD2: 5,
  POINTS: 6,
  CARD_SHADOW: 7,
  TAG_LEFT: 8
};

export const IMAGE_FILL_STYLE = {
  1: 'padding',
  2: 'whitespace'
};

export const LAYOUT_TYPE = {
  0: 'big',
  1: 'small',
  2: 'hybrid',
  3: 'list',
  5: 'three',
  6: 'swipe'
};

export const LAYOUT_MAP = {
  BIG: 0, // 一行一（大图）
  SMALL: 1, // 一行二
  HYBRID: 2, // 一大两小
  LIST: 3, // 一行一（列表）
  MULTI: 4,
  THREE: 5, // 一行三
  SWIPE: 6 // 横向滚动
};

export const CORNER_TYPE = {
  0: 'new-arrival',
  1: 'hot-sale',
  2: 'new',
  3: 'hot'
};

export const IMAGE_RATIO = {
  0: '3:2',
  1: '1:1',
  2: '3:4',
  3: '16:9'
};

export const TEXT_STYLE = {
  1: 'normal',
  2: 'bold'
};

export const RADIUS_TYPE_MAP = {
  1: 'rect',
  2: 'circle'
};

export const BUTTON_SIZE = {
  SMALL: 'small',
  BIG: 'big'
};

const iconUrl = 'https://img01.yzcdn.cn/public_files/2018/08/20';
export const MEDIA_ICON_URL_MAP = {
  COLUMN: `${iconUrl}/d5b6ccfc9383b976fc02a88f43f49ef2.png`,
  TEXT: `${iconUrl}/b24699aee714351838f730cdb59de84a.png`,
  AUDIO: `${iconUrl}/e05ef730df7b6fb43844b908734a9fc0.png`,
  VIDEO: `${iconUrl}/0bfab52c6a9c77b58ca686567479b4ab.png`,
  LIVE: `${iconUrl}/aac3b43a16195809ab479746236a457c.png`,
};

export const CDN_ICON_URL_MAP = {
  COLUMN: '',
  TEXT: '',
  AUDIO: '',
  VIDEO: '',
  LIVE: ''
};

export const FREE_TEXT_MAP = {
  READ: '免费试读',
  LISTEN: '免费试听',
  WATCH: '免费试看'
};

export default {
  SIZE_TYPE,
  SIZE_MAP,
  IMAGE_FILL_STYLE,
  LAYOUT_TYPE,
  CORNER_TYPE,
  IMAGE_RATIO,
  LAYOUT_MAP,
  TEXT_STYLE,
  RADIUS_TYPE_MAP,
  BUTTON_SIZE
};

// 商品类型 枚举
export const GOODS_TYPE = {
  COMMON: 0, // 普通类型商品

  AUCTION: 1, // 拍卖商品

  FOOD: 5, // 餐饮商品

  FENXIAO: 10, // 分销商品

  MEMBER_CARD: 20, // 会员卡商品

  GIFT_CARD: 21, // 礼品卡商品

  MEETINGS: 23, // 有赞会议商品

  PERIOD_BUY: 24, // 周期购

  KNOWLEDGE: 31, // 知识付费商品

  HOTEL: 35, // 酒店商品

  SERVICE: 40, // 普通服务类商品

  NORMAL_VIRTUAL: 182, // 普通虚拟商品

  VIRTUAL_TICKET: 183, // 电子卡券商品

  OUT_MEMBER_CARD: 201, // 外部会员卡商品

  OUT_CASH: 202, // 外部直接收款商品

  OUT_COMMON: 203, // 外部普通商品

  MOCK: 205 // mock不存在商品
};

export const ABILITY_TYPE = {
  LIVE: 90001, // 直播

  CONTENT: 90002, // 内容

  COLUMN: 90003, // 专栏

  COURSE: 10007 // 课程
};

export const SKELETON_IMAGE = 'https://img01.yzcdn.cn/weapp/wsc/lPgl6z.png';
