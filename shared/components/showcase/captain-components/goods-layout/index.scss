@import './styles/big-style.scss';
@import './styles/small-style.scss';
@import './styles/hybrid-style.scss';
@import './styles/three-style.scss';
@import './styles/list-style.scss';

.c-goods-layout,
.goods-wrap {
  overflow: hidden;
}

.c-goods-layout__info {
  padding: 0 12px 4px;
  position: relative;

  &-size2 {
    padding: 0;
  }
  &-layout6,
  &-layout5 {
    padding: 0 8px 2px;
  }
}

.goods-item {
  overflow: hidden;
  position: relative;
  background-color: #fff;

  /* 卡片投影 */
  &.card-shadow {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  }

  /* 描边白底 */
  &.card2 {
    border: 1px solid rgba(50, 50, 51, 0.1);
  }

  /* 无边透明 */
  &.simple {
    background-color: inherit;

    .c-goods-layout__info {
      margin: 0;
      padding: 0;
    }
  }

  &.goods-item--list {
    .c-goods-layout__info {
      padding: 0 10px 0 0;
      min-height: 145px;
    }
  }

  /* 圆角 */
  &.circle {
    border-radius: 8px;

    .photo {
      border-radius: 8px 8px 0 0;
    }

    &.goods-item--list {
      .photo {
        border-radius: 8px 0 0 0;
      }
    }

    &.simple {
      border-radius: 8px 8px 0 0;

      .photo {
        border-radius: 0 0 8px 8px;
      }
    }

    .label {
      border-top-left-radius: 8px;
    }
  }

  .label {
    position: absolute;
    display: flex;
    top: 0;
    left: 0;
    padding: 0 4px;
    height: 24px;
    font-size: 16px;
    color: #fff;
    line-height: 24px;
    font-weight: 700;
    justify-content: center;
    align-items: center;
    border-bottom-right-radius: 8px;
  }
}
.flex-layout {
  display: flex;
  flex-wrap: wrap;
}
.layout-container--small,
.layout-container--hybrid,
.layout-container--three {
  /* 小图、瀑布流和一大两小 布局样式 */
  .goods-wrap {
    overflow: hidden;
    float: left;
  }
}

.layout-container--swipe,
.layout-container--three {
  .label {
    font-size: 12px !important;
    height: 16px !important;
    line-height: 16px !important;
  }
}
