/* 一行三个 和 横向滑动  布局样式 */
.layout-container--three {
  overflow: hidden;

  .goods-wrap {
    width: 33.33%;
  }
}

.layout-container--swipe {
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  flex-wrap: nowrap;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 10px;
  margin-bottom: -10px;
  box-sizing: border-box;

  .goods-wrap,
  .more-button--6 {
    flex: 0 0 30%;
    width: 30%;
  }

  .goods-item {
    white-space: normal;
  }
}

.goods-item--three,
.goods-item--swipe {
  .photo {
    &::after {
      background-size: 60px 60px;
    }
  }

  .corner-mark--0, .corner-mark--1 {
    font-size: 28px;
    height: 28px;
    width: 28px;
  }

  .corner-back--0 {
    width: 26px;
    height: 12px;
    top: 8px;
  }

  .corner-back--1 {
    width: 12px;
    height: 26px;
    left: 8px;
  }

  .corner-mark--2 {
    font-size: 28px;
    height: 28px;
    width: 28px;
  }

  .corner-back--2 {
    width: 16px;
    height: 16px;
    left: 11px;
  }

  .corner-mark--3 {
    font-size: 28px;
    height: 28px;
    width: 28px;
  }

  .corner-back--3 {
    width: 18px;
    height: 12px;
    top: 12px;
    left: 10px;
  }
}
