.layout-container--hybrid {
  overflow: hidden;

  .goods-wrap {
    /* 一大两小 大图展示100% */
    &:nth-child(3n + 1) {
      width: 100%;

      .photo::after {
        background-size: 100px 100px;
      }

      .corner-mark--0 {
        top: 11px;
      }
    
      .corner-back--0 {
        top: 20px;
      }
    
      .corner-mark--2 {
        left: 10px;
      }
    
      .corner-back--2 {
        left: 18px;
      }
    
      .corner-mark--3 {
        top: 10px;
        left: 10px;
      }
    
      .corner-back--3 {
        top: 22px;
        left: 18px;
      }
    }

    &:nth-child(3n),
    &:nth-child(3n + 2) {
      width: 50%;

      .photo::after {
        background-size: 60px 60px;
      }
    }
  }
}
