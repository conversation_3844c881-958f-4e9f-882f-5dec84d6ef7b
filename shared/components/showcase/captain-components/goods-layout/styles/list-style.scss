/* 详情列表 布局样式 */
.layout-container--list {
  .photo {
    float: left;
    padding-top: 0 !important;

    &::after {
      background-size: 60px 60px;
    }
  }

  &.tag-left {
    .photo {
      &::after {
        background-size: 35px 35px;
      }
    }
  }
}

.goods-item--list {
  // padding: 10px 15px 10px 0;
  background: #fff;


  .c-goods-layout__info {
    position: relative;
    padding: 0 10px 0 0;
    margin: 0 12px 0 151px !important;
    overflow: hidden;
    background-color: inherit;
  }

  .c-goods-layout__info-price {
    position: absolute;
    left: 0;
    bottom: 10px;
    margin: 0;
    right: 0;
  }

  .photo {
    width: 145px;
    height: 145px;
    margin-right: 10px;
  }

  .photo__image {
    max-width: 145px;
    max-height: 145px;
  }

  &.tag-left {
    background-color: inherit;

    .c-goods-layout__info {
      margin: 0 10px 0 98px !important;
      height: 88px;
    }

    .c-goods-layout__info-price {
      bottom: 0;
    }

    .photo {
      width: 88px;
      height: 88px;
    }

    .photo__image {
      max-width: 88px;
      max-height: 88px;
    }
  }
}