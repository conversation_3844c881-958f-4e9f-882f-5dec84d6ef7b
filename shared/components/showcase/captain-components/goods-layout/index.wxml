<view class="c-goods-layout th_vw-buy {{ showcaseClass }}">
  <!-- 瀑布流单独处理 -->
  <block wx:if="{{ isWaterfall }}">
    <view class="layout-container {{flexClass}} {{ containerClass }} layout-container--waterfall" style="{{ pageMarginStyle }}">
      <view wx:for="{{ waterfallGoodsList }}" wx:for-item="listCols" wx:for-index="listIndex" wx:key="{{ listIndex }}-{{ listCols.length }}" class="goods-wrap">
        <view wx:for="{{ listCols }}" wx:for-item="goodsInfo" wx:for-index="goodsIndex" wx:key="alias" style="overflow: hidden;">
          <block wx:if="{{ goodsInfo.isExtra }}">
            <item-extra layout-class="{{ ['waterfall', borderRadiusTypeClass] }}" item="{{ { info: goodsInfo, heightStyle: heightStyle, goodsMarginStyle: goodsMarginStyle, index: goodsIndex, layoutConfig: { layout, sizeType, ...infoData } } }}" />
          </block>
          <block wx:else>
            <view class="goods-item goods-item--{{ layoutName }} waterfall {{ borderRadiusTypeClass }}" data-index="{{ listIndex + '-' + goodsIndex }}" style="{{ goodsMarginStyle }}" catch:tap="handleGoodsItemClick">
              <template is="goods-item" data="{{ appId, goodsInfo, imageFillStyle, imageRatio, showCornerMark, cornerMarkType, cornerMarkImage, infoData, layout, sizeType, redirectType, extraData, isWaterfall, pageData, pageUtils, showPriceLabel, soldOutFlag, showLabel, borderRadiusType, themeColor, showOriginPriceByGoodsInfo }}"></template>
            </view>
          </block>
        </view>
        <block wx:if="{{ skeletonNumber > 0 }}">
          <view class="goods-wrap">
            <view class="goods-item goods-item--{{ layoutName }} waterfall {{ borderRadiusTypeClass }}" style="{{ goodsMarginStyle }}">
              <template is="goods-item" data="{{ appId, ...skeletonGoodsConfig, imageFillStyle, imageRatio, showCornerMark, cornerMarkType, cornerMarkImage, infoData, layout, sizeType, redirectType, extraData, pageData, showPriceLabel, soldOutFlag, showLabel, borderRadiusType, themeColor, showOriginPriceByGoodsInfo }}"></template>
            </view>
          </view>
        </block>
      </view>
    </view>
  </block>
  <!-- 其他布局 -->
  <block wx:else>
    <view class="layout-container {{flexClass}}  {{ showcaseClass }} {{ containerClass }}" style="{{ pageMarginStyle }}" bind:touchstart="onTouchStart" bind:touchmove="onTouchMove" bind:touchend="onTouchEnd" bind:touchcancel="onTouchEnd">
      <view wx:for="{{ list }}" wx:for-index="goodsIndex" wx:for-item="goodsInfo" wx:key="alias" class="goods-wrap goods-wrap--{{ goodsIndex }}">
        <block wx:if="{{ goodsInfo.isExtra }}">
          <item-extra layout-class="{{ [sizeName, borderRadiusTypeClass] }}" item="{{ { info: goodsInfo, heightStyle: heightStyle, goodsMarginStyle: goodsMarginStyle, index: goodsIndex, layoutConfig: { layout, sizeType, ...infoData } } }}" />
        </block>
        <block wx:else>
          <view
            class="goods-item goods-item--{{ layoutName }} {{ sizeName }} {{ borderRadiusTypeClass }}"
            data-index="{{ goodsIndex }}"
            style="{{ goodsMarginStyle +';'+ heightStyle }}"
            catch:tap="handleGoodsItemClick"
          >
            <template is="goods-item" data="{{ showcaseClass, appId, goodsInfo, goodsIndex, imageFillStyle, imageRatio, showCornerMark, cornerMarkType, cornerMarkImage, infoData, layout, sizeType, redirectType, extraData, pageData, showPriceLabel, showUmpTags, umpTagsMaxCount, soldOutFlag, showLabel, themeColor, borderRadiusType, exhibitionTag, showOriginPriceByGoodsInfo }}"></template>
          </view>
        </block> 
      </view>
      <view class="goods-wrap goods-wrap--last" wx:if="{{ showLastSelfDefineItem }}">
        <block>
          <view
            class="goods-item goods-item--last {{ sizeName }} {{ borderRadiusTypeClass }}"
            style="{{ goodsMarginStyle +';'+ heightStyle }}"
          >
            <slot name="last-item-in-list" />
          </view>
        </block>
      </view>
      <block wx:if="{{ skeletonNumber > 0 }}">
        <view class="goods-wrap" wx:for="{{ skeletonNumber }}" wx:key="item">
          <view class="goods-item goods-item--{{ layoutName }} {{ sizeName }} {{ borderRadiusTypeClass }}" style="{{ goodsMarginStyle }}">
            <template is="goods-item" data="{{ showcaseClass, appId, ...skeletonGoodsConfig, imageFillStyle, imageRatio, showCornerMark, cornerMarkType, cornerMarkImage, infoData, layout, sizeType, redirectType, extraData, pageData, showPriceLabel, showUmpTags, umpTagsMaxCount, soldOutFlag, borderRadiusType, themeColor, showOriginPriceByGoodsInfo }}"></template>
          </view>
        </view>
      </block>
      <block wx:if="{{ isSwipeLayout && openSwipePagination }}">
        <more-button wx:if="{{ isSwipeLoadFinished }}" class="more-button--6" app-id="{{ appId }}" more-url="{{ moreUrl }}" button-type="{{ 6 }}" goods-margin="{{ goodsMargin }}" more-text="查看全部" />
        <more-button wx:else button-type="{{ 5 }}" goods-margin="{{ goodsMargin }}" loading="{{ isSwipeLoading }}" />
      </block>
    </view>
  </block>
  <!-- 查看更多（横向滑动查看更多显示在左侧） -->
  <more-button wx:if="{{ isShowMore }}" app-id="{{ appId }}" more-url="{{ moreUrl }}" button-type="{{ moreButtonType }}" goods-margin="{{ goodsMargin }}" />
</view>
<!-- goods-items -->
<template name="goods-item">
  <block>
    <goods-item-image soldout-flag="{{ soldOutFlag }}" image-url="{{ goodsInfo.imageUrl || goodsInfo.thumbUrl }}" image-fill-style="{{ imageFillStyle }}" image-ratio="{{ imageRatio }}" photo-class="photo" image-class="photo__image" is-waterfall="{{ isWaterfall }}" is-soldout="{{ goodsInfo.isSoldout }}" is-end="{{ infoData.isEnd || goodsInfo.isEnd }}" hide-image-status="{{ goodsInfo.hideImageStatus }}" layout="{{ layout }}" goods-info="{{ goodsInfo }}">
      <view slot="tag-info">
        <goods-item-tag-info page-data="{{ pageData }}" goods-info="{{ goodsInfo }}" info-data="{{ infoData }}" layout="{{ layout }}" size="{{ sizeType }}" bind:tag-info-change="tagInfoChange" />
      </view>
      <!-- corner mark 区域 -->
      <view slot="corner-mark">
        <goods-item-corner-mark wx:if="{{ showCornerMark }}" type="{{ cornerMarkType }}" mark-image="{{ cornerMarkImage }}" mark-type-class="{{ 'corner-mark--' + cornerMarkType }}" back-type-class="{{ 'corner-back--' + cornerMarkType }}" goods-info="{{ goodsInfo }}" />
      </view>

      <!-- 标签 区域 -->
      <view slot="label">
        <view
          wx:if="{{ showLabel && goodsInfo.keyLabel }}"
          style="background-color: {{themeColor}}"
          class="label">
          {{ goodsInfo.keyLabel }}
        </view>
      </view>
    </goods-item-image>

    <view class="c-goods-layout__info c-goods-layout__info-size{{sizeType}} c-goods-layout__info-layout{{layout}}">
      <goods-item-title
        custom-class="c-goods-layout__info-title"
        goods-info="{{ goodsInfo }}"
        layout="{{ layout }}"
        size-type="{{ sizeType }}"
        info-data="{{ infoData }}"
        goods-index="{{ goodsIndex }}"
      />

      <goods-item-tags
        custom-class="c-goods-layout__tags"
        goods-info="{{ goodsInfo }}"
        layout="{{ layout }}"
        size-type="{{ sizeType }}"
        info-data="{{ infoData }}"
        item-index="{{ goodsIndex }}"
        exhibition-tag="{{ exhibitionTag }}"
      />

      <goods-item-ump-tags wx:if="{{ !goodsInfo.skeleton && showUmpTags }}" show="{{ showUmpTags }}" max-count="{{ umpTagsMaxCount }}" goods-info="{{ goodsInfo }}" />
      <cap-skeleton wx:if="{{ goodsInfo.skeleton && showUmpTags }}" type="rect" rect-style="width: 56px; height: 16px;" />
      <goods-item-expand
        goods-info="{{ goodsInfo }}"
        layout="{{ layout }}"
        size-type="{{ sizeType }}"
        info-data="{{ infoData }}"
      />

      <goods-item-price
        custom-class="c-goods-layout__info-price"
        showcase-class="{{ showcaseClass }}"
        app-id="{{ appId }}"
        goods-info="{{ goodsInfo }}"
        layout="{{ layout }}"
        size-type="{{ sizeType }}"
        info-data="{{ infoData }}"
        goods-index="{{ goodsIndex }}"
        extra-data="{{ extraData }}"
        redirect-type="{{ redirectType }}"
        generic:buy-button="buy-button"
        generic:goods-item-extra="goods-item-extra"
        bind:buy="handleBuyClick"
        show-origin-price-by-goods-info="{{ showOriginPriceByGoodsInfo }}"
      >
        <view wx:if="{{ showPriceLabel }}" class="goods-price-label" slot="price-label">
          <goods-price-label
            custom-class="member-tag" 
            showcase-class="{{ showcaseClass }}"
            text-align-type="{{ textAlignType }}"
            page-data="{{ pageData }}"
            goods-info="{{ goodsInfo }}"
            info-data="{{ infoData }}"
            layout="{{ layout }}"
            size="{{ sizeType }}"
          />
        </view>
      </goods-item-price>
    </view>
  </block>
</template>