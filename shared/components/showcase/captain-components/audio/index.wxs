var STATUS = {
  STOP: 0,
  PLAY: 1,
  PAUSE: 2
};

function getAudioClass(isLoaded, status) {
  var className = 'cap-audio';

  if (isLoaded) {
    if (status === STATUS.PLAY) className += ' cap-audio-status-play';
    if (status === STATUS.STOP) className += ' cap-audio-status-stop';
    if (status === STATUS.PAUSE) className += ' cap-audio-status-pause';
  }
  return className;
}

function getAudioWeixinClass(bubble) {
  var className = 'cap-audio-weixin cap-action--clearfix';
  if (bubble === 'right') className += ' cap-audio-weixin--right';

  return className;
}

function getStatusClass(status, reload) {
  var className = 'zan-icon';
  if (status === STATUS.PLAY) {
    className += (reload ? ' zan-icon-stop' : ' zan-icon-pause');
  } else {
    className += ' zan-icon-play';
  }

  return className;
}

module.exports = {
  getAudioClass,
  getAudioWeixinClass,
  getStatusClass
};
