import audioEventBus from './audio-event';
import componentBehavior from '../../behaviors/component-behavior';

// 根据组件顺序索引音频
const STATUS = {
  STOP: 0,
  PLAY: 1,
  PAUSE: 2
};

function pad(number) {
  return number < 10 ? `0${number}` : `${number}`;
}

function getTime(seconds) {
  const minute = parseInt(seconds / 60, 10);
  const second = parseInt(seconds % 60, 10);
  return `${pad(minute)}:${pad(second)}`;
}

Component({
  behaviors: [componentBehavior],
  properties: {
    index: Number,
    audioStyle: Number,
    title: String,
    loadingIcon: String,
    playerImg: String,
    logo: String,
    bubble: String,
    src: String,
    loop: Boolean,
    reload: Boolean,
    duration: Number,
    status: {
      type: Number,
      observer: 'handleStatusChange'
    },
    formatedDuration: String,
    canPlay: {
      type: Boolean,
      value: false
    }
  },

  data: {
    audioClass: 'cap-audio',
    statusClass: 'cap-audio-btn',
    percentage: 0,
    isLoaded: false,
    isLoading: false,
    formatedCurrentTime: '00:00'
  },

  ready() {
    const { status } = this.data;

    // 初始化一些样式类
    this.handleStatusChange(status);
    if (this.data.audioStyle === 2) {
      // 语音初始化,创建并返回canvas上下文context对象
      this.canvasArcCtx = wx.createCanvasContext('capAudioCanvasArcCir-' + this.data.index, this);
      this.canvasArcCtx.scale(4, 4);
    }
    audioEventBus.on('timeupdate', this.handleTimeUpdate, this);
    audioEventBus.on('reset', this.handleReset, this);
  },

  methods: {
    handleReset(e) {
      // 直接是index，没有detail
      const audioIndex = e;
      const { index } = this.data;
      if (audioIndex !== index) return;

      this.setData({
        formatedCurrentTime: '00:00',
        percentage: 0
      });
      // reset
      if (this.data.audioStyle === 2) {
        this.audio__drawArc(index, 0);
      }
    },

    handleTimeUpdate(e) {
      const { index, duration } = this.data;
      const { audioIndex, currentTime } = e;
      if (index !== audioIndex) return;

      this.setData({
        formatedCurrentTime: getTime(currentTime),
        percentage: (currentTime / duration) * 100,
        isLoading: false,
        isLoaded: true
      });
      // audioStyle 为2 绘制canvas 圆环
      const percentage = (currentTime / duration) * 100;
      if (this.data.audioStyle === 2) {
        this.audio__drawArc(index, percentage);
      }
    },

    handleStatusChange(status) {
      const { isLoaded, reload } = this.data;
      this.updateAudioClass(isLoaded, status);
      this.updateStatusClass(status, reload);
    },

    updateAudioClass(isLoaded, status) {
      let audioClass = 'cap-audio';

      if (isLoaded) {
        if (status === STATUS.PLAY) audioClass += ' cap-audio-status-play';
        if (status === STATUS.STOP) audioClass += ' cap-audio-status-stop';
        if (status === STATUS.PAUSE) audioClass += ' cap-audio-status-pause';
      }

      this.setData({ audioClass });
    },

    updateStatusClass(status, reload) {
      let statusClass = 'cap-audio-btn zan-icon';
      if (status === STATUS.PLAY) {
        statusClass += (reload ? ' zan-icon-stop' : ' zan-icon-pause');
      } else {
        statusClass += ' zan-icon-play';
      }

      this.setData({ statusClass });
    },

    audio__updateProgress(e) {
      const { index } = e.currentTarget.dataset;
      const percentage = e.detail;

      this.triggerEvent('slider-drag', {
        index,
        percentage
      });
    },

    audio__trigger(e) {
      const { src, index, reload } = e.currentTarget.dataset;

      this.triggerEvent('btn-click', {
        src,
        index,
        reload
      });
    },
    audio__drawArc(currentId, percentage) {
      let startAngle = 1.5 * Math.PI;
      let endAngle = 0;
      let color = '#3387FD';
      let canvasArcCtx = this.canvasArcCtx;
      if (percentage <= 100) {
        endAngle = percentage * 2 * Math.PI / 100 + 1.5 * Math.PI;
        this.drawCanvasArc(canvasArcCtx, color, startAngle, endAngle);
      }
    },
    drawCanvasArc(ctx, c, s, e) {
      ctx.setLineWidth(2);
      ctx.setStrokeStyle(c);
      ctx.setLineCap('round');
      ctx.beginPath();
      ctx.arc(12.5, 12.5, 11.5, s, e);
      ctx.stroke();
      ctx.draw();
    },

  }
});
