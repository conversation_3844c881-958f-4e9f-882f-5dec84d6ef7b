@import "shared/common/css/helper/index.wxss";

.cap-action--clearfix {
  zoom: 1
}

.cap-action--clearfix:after {
  content: "";
  display: table;
  clear: both
}

.cap-audio {
  padding: 15px
}

.cap-audio--none-padding {
  padding: 0;
}

.cap-audio-weixin {
  position: relative;
  min-height: 40px
}

.cap-audio-logo {
  width: 40px;
  height: 40px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .3)
}

.cap-audio-animation {
  position: absolute;
  left: 21px;
  top: 12px;
  width: 13px;
  height: 17px;
  z-index: 2
}

.cap-audio-loading {
  position: absolute;
  right: 10px;
  top: 12px;
  width: 16px;
  height: 16px;
}

.cap-audio-animation-static,
.cap-audio-bar {
  background: url(https://img01.yzcdn.cn/v2/image/wap/audio/sprite_v5.png) no-repeat scroll 0 0;
  background-size: 400px 175px
}

.cap-audio-animation-static {
  background-position: -180px -105px;
  width: 13px;
  height: 17px;
  display: block;
  position: absolute;
  left: 21px;
  top: 12px;
  z-index: 1
}

.cap-audio-bar {
  width: 185px;
  height: 42px;
  display: inline-block;
  position: absolute;
  left: 50px;
  cursor: pointer
}

.cap-audio-time {
  color: #999;
  font-size: 14px;
  position: absolute;
  left: 240px;
  bottom: 5px
}

.cap-audio-weixin--right .cap-audio-logo {
  float: right
}

.cap-audio-weixin--right .cap-audio-animation-static {
  background-position: -180px -83px;
  right: 21px;
  left: auto
}

.cap-audio-weixin--right .cap-audio-bar {
  background-position: -187px 0;
  left: auto;
  right: 50px
}

.cap-audio-weixin--right .cap-audio-bar .cap-audio-loading {
  position: absolute;
  left: 20px;
  right: auto
}

.cap-audio-weixin--right .cap-audio-bar .cap-audio-animation {
  right: 21px;
  left: auto
}

.cap-audio-weixin--right .cap-audio-time {
  left: auto;
  right: 240px
}

.cap-audio--music {
  position: relative;
  padding: 0 15px 0 70px;
  width: 100%;
  height: 74px;
  border: 1px solid #e5e5e5;
  background-color: #fff;
  box-sizing: border-box
}

.cap-audio--music .cap-audio-btn {
  position: absolute;
  left: 15px;
  top: 9px;
  font-size: 40px;
  color: #4b0;
}

.cap-audio--music .cap-audio-title {
  padding-right: 15px;
  height: 38px;
  line-height: 38px;
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.cap-audio--music .cap-audio-loading {
  top: 10px;
  right: 20px
}

.cap-audio--music .cap-audio-duration,
.cap-audio--music .cap-audio-time {
  position: absolute;
  bottom: 0;
  line-height: 34px;
  font-size: 12px
}

.cap-audio--music .cap-audio-time {
  left: 70px;
  right: auto
}

.cap-audio--music .cap-audio-duration {
  left: auto;
  right: 15px;
  color: #999
}

.cap-audio-status-play .cap-audio-bar .cap-audio-animation {
  display: block !important;
  visibility: visible
}

.cap-audio--simple {
  position:relative;
  padding:0 15px 0 50px;
  height: 46px;
  background-color:#fff;
}
.cap-audio--simple-btn {
  width: 25px;
  height: 25px;
  background-size: contain;
  background-position: 100%;
  background-repeat: no-repeat;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
}
.cap-audio--simple-title-wrapper {
  height: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.cap-audio--simple-title {
  vertical-align: middle;
  display: inline-block;
  font-size: 14px;
  color: #111111;
  line-height: 46px;
}

.cap-audio--simple-box {
  position: absolute;
  width: 50px;
  height: 46px;
  left: 0;
}

.cap-audio--simple-circle {
  width: 25px;
  height: 25px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  z-index: 1;
}

/* slider */
.van-slider__button {
  width: 14px;
  height: 14px;
  border: 1px solid #e5e5e5;
  box-shadow: none;
  border-radius: 50%;
  background-color: #fff;
}

.van-slider__button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  width: 4px;
  height: 4px;
  border-radius: 4px;
  background: #4b0;
}
