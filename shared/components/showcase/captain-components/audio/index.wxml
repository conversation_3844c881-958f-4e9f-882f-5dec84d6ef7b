<view class="{{ audioClass }} {{audioStyle === 2 ? 'cap-audio--none-padding' : ''}}" >
  <block wx:if="{{ audioStyle === 0 }}">
    <view class="{{ utils.getAudioWeixinClass(bubble) }}" bind:tap="audio__trigger"
      data-src="{{ src }}" data-index="{{ index }}" data-reload="{{ reload }}" data-loop="{{ loop }}">
      <image class="cap-audio-logo" src="{{ logo }}" alt="音频播放logo" />
      <view class="cap-audio-bar">
        <image hidden="{{ status !== 1 }}" class="cap-audio-animation" src="{{ playerImg }}" />
        <text class="cap-audio-animation-static"></text>
        <image hidden="{{ !isLoading }}" class="cap-audio-loading" src="{{ loadingIcon }}" />
      </view>
      <text wx:if="{{ isLoaded }}" class="cap-audio-time">{{ formatedDuration }}</text>
    </view>
  </block>
  <block wx:if="{{ audioStyle === 1 }}">
      <view class="cap-audio--music">
        <text class="{{ statusClass }}" bind:tap="audio__trigger"
          data-src="{{ src }}" data-index="{{ index }}" data-reload="{{ reload }}" data-loop="{{ loop }}" />
        <view class="cap-audio-info">
          <view class="cap-audio-title">{{ title }}</view>
          <image hidden="{{ !isLoading }}" class="cap-audio-loading" src="{{ loadingIcon }}" />
          <van-slider
            custom-class="van-slider"
            use-button-slot
            value="{{ percentage }}"
            active-color="#4b0"
            inactive-color="#f2f2f2"
            data-index="{{ index }}"
            disabled="{{ !canPlay }}"
            bind:change="audio__updateProgress"
          >
            <view class="van-slider__button" slot="button"></view>
          </van-slider>
          <text wx:if="{{ isLoaded }}" class="cap-audio-time">{{ formatedCurrentTime }}</text>
          <text wx:if="{{ isLoaded }}" class="cap-audio-duration">{{ formatedDuration }}</text>
        </view>
      </view>
  </block>
  <block wx:if="{{ audioStyle === 2 }}">
    <view class="cap-audio--simple">
      <view class="cap-audio--simple-box" bind:tap="audio__trigger" data-src="{{ src }}" data-index="{{ index }}" data-reload="{{ reload }}" data-loop="{{ loop }}">
        <canvas class="cap-audio--simple-circle" height="200rpx" width="200rpx" canvas-id="capAudioCanvasArcCir-{{index}}">
        </canvas>
        <text class="cap-audio--simple-btn" style="background-image: url({{status === 1 ?'https://img01.yzcdn.cn/weapp/wsc/6t3mqd.png' : 'https://img01.yzcdn.cn/weapp/wsc/l3JqDRF.png'}})"/>

      </view>
      <view class="cap-audio--simple-title-wrapper">
        <view class="cap-audio--simple-title">{{ title }}</view>
      </view>
    </view>
  </block>

</view>

<wxs src="./index.wxs" module="utils" />
