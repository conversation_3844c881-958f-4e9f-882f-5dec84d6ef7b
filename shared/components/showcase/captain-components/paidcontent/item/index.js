import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import theme from 'shared/common/components/theme-view/theme';
import getSystemInfo from 'shared/utils/browser/system-info';

const { platform } = getSystemInfo();

Component({
  externalClasses: ['external-class'],

  options: {
    multipleSlots: true,
    addGlobalClass: true,
  },

  properties: {
    slots: {
      type: Object,
      value: {
        thumbnail: false,
        icon: false,
        title: false,
        subtitle: false,
        status: false,
        label: false,
      },
    },
    navigateType: {
      type: String,
      value: 'navigate',
    },
    url: String,
    thumbnail: String,
    title: String,
    titleTag: String,
    titleTagClass: String,
    supportsMultiLines: Boolean,
    subtitle: {
      type: String,
      observer(subtitle) {
        this.setData({
          _subtitle: subtitle.replace(/\n/g, ' '),
        });
      },
    },
    statusList: Array,
    topCornerText: String,
    topTagText: String,
    topCornerClass: String,
    bottomCornerText: String,
    bottomTagText: String,
    bottomCornerClass: String,
    thumbnailHeight: {
      type: Number,
      value: 64,
    },
    thumbnailWidth: {
      type: Number,
      value: 110,
    },
    icon: String,
    collectFormId: {
      type: Boolean,
      value: false,
    },
    courseTypeName: {
      type: String,
      value: '',
    },
  },

  data: {
    _subtitle: '',
    isIOS: platform === 'ios',
  },

  attached() {
    this.setData({
      _thumbnail: cdnImage(this.data.thumbnail, '!220x220.jpg'),
    });
    // 给van-tag 设置全店主题色
    theme.getThemeColor('general').then((color) => {
      this.setData({
        themeGeneral: color,
      });
    });
  },

  methods: {
    goTo() {
      if (!this.data.url) return;

      wx.navigateTo({
        url: this.data.url,
      });
    },
  },
});
