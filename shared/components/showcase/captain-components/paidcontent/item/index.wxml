<navigator
  class="paidcontent-item external-class"
  type="{{ navigateType }}"
  path="{{ url }}"
  hover-class="none"
  url="{{ url }}">
  <!-- 缩略图 -->
  <view class="paidcontent-item__thumbnail-wrapper">
    <slot wx:if="{{ slots.thumbnail }}" name="thumbnail"></slot>
    <image wx:else
      class="paidcontent-item__thumbnail"
      style="width: {{ thumbnailWidth }}px; height: {{ thumbnailHeight }}px"
      src="{{ _thumbnail }}"
      mode="aspectFill">
    </image>
    <view wx:if="{{ statusTag }}" class="paidcontent-item__status-tag">{{ statusTag }}</view>
    <slot wx:if="{{ slots.icon }}" name="icon"></slot>
    <view wx:else class="paidcontent-item__thumbnail-icon">
      <image class="paidcontent-item__thumbnail-icon-image" src="{{ icon }}"></image>
    </view>
    <view wx:if="{{ courseTypeName }}" class="paidcontent-item__course-type-name">{{ courseTypeName }}</view>
  </view>

  <view class="paidcontent-item-detail" style="height: {{ thumbnailHeight }}px">
    <view class="paidcontent-item-row">
      <view class="paidcontent-item-col">
        <!-- 标题 -->
        <view class="paidcontent-item__title-bar">
          <slot wx:if="{{ slots.title }}" name="title"></slot>
          <block wx:else>
            <van-tag
              wx:if="{{ titleTag }}"
              type="{{ titleTagClass }}"
              custom-class="paidcontent-item__title-tag">
              {{ titleTag }}
            </van-tag>
            <text
              class="paidcontent-item__title {{ supportsMultiLines ? 'paidcontent-item__title--multi' : ''}}"
            >{{ title }}</text>
          </block>
        </view>

        <!-- 副标题 -->
        <view wx:if="{{ slots.subtitle || _subtitle }}" class="paidcontent-item__subtitle {{ bottomTagText ? '' : 'paidcontent-item__subtitle--notag' }}">
          <slot wx:if="{{ slots.subtitle }}" name="subtitle"></slot>
          <text wx:else>{{ _subtitle }}</text>
        </view>
      </view>
      <view wx:if="{{ !isIOS() }}" class="paidcontent-item-col paidcontent-item__corner-top">
        <!-- 右上角 -->
        <slot wx:if="{{ slots.topTag }}" name="topTag"></slot>
        <block wx:else>
          <view class="paidcontent-item__corner-top-text {{ topCornerClass }}">{{ topCornerText }}</view>
          <van-tag
            wx:if="{{ topTagText }}"
            plain
            type="danger"
            custom-class="paidcontent-item__top-tag paidcontent-item-tag">
            {{ topTagText }}
          </van-tag>
        </block>
      </view>
    </view>

    <view class="paidcontent-item-row paidcontent-item-detail__footer">
      <view class="paidcontent-item-col paidcontent-item__status-list">
        <!-- 状态信息 -->
        <slot wx:if="{{ slots.status }}" name="status"></slot>
        <block wx:else>
          <text
            wx:for="{{ statusList }}"
            wx:key="item"
            class="paidcontent-item__status"
            style="{{ item.color ? 'color:' + item.color : '' }}"
          >{{ item.text ? item.text : item }}</text>
        </block>
      </view>
      <view wx:if="{{ !isIOS }}" class="paidcontent-item-col paidcontent-item__corner-bottom">
        <!-- 右下角标签 -->
        <slot wx:if="{{ slots.bottomTag }}" name="bottomTag"></slot>
        <block wx:else>
          <van-tag
            wx:if="{{ bottomTagText }}"
            color="{{ themeGeneral }}"
            custom-class="paidcontent-item__bottom-tag paidcontent-item-tag"
            plain
          >
            {{ bottomTagText }}
          </van-tag>
          <theme-view
            custom-class="paidcontent-item__corner-bottom-text"
            color="{{ bottomCornerClass ? 'general' : '' }}">
            {{ bottomCornerText  }}
          </theme-view>
        </block>
      </view>
    </view>
  </view>
</navigator>
