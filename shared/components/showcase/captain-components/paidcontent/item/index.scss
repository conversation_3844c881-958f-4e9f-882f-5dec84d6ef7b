.paidcontent-item {
  position: relative;
  padding: 15px;
  overflow: hidden;
  background: #fff;
  display: block;
}

.paidcontent-item-row {
  display: flex;
}

.paidcontent-item-col {
  flex: 1 1 auto;
  max-width: 100%;
  overflow: hidden;
}
.paidcontent-item-col:last-child {
  flex: 0 0 auto;
}
.paidcontent-item-col:first-child {
  flex: 1 1 auto;
}

/* 缩略图 */
.paidcontent-item__thumbnail-wrapper {
  float: left;
  position: relative;
}
.paidcontent-item__thumbnail {
  display: block;
  border-radius: 3px;
}
.paidcontent-item__thumbnail-icon {
  position: absolute;
  right: 5px;
  bottom: 5px;
}
.paidcontent-item__thumbnail-icon-image {
  display: block;
  width: 20px;
  height: 20px;
}
.paidcontent-item__status-tag {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  font-size: 10px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.4);
}

/* 中间信息 */
.paidcontent-item-detail {
  position: relative;
  padding-left: 10px;
  overflow: hidden;
}
.paidcontent-item-detail__footer {
  position: absolute;
  bottom: 0;
  width: calc(100% - 10px);
}
.paidcontent-item__title-bar {
  display: flex;
  line-height: 0;
}
.paidcontent-item__title-tag {
  flex: 0 0 auto;
  margin: 2px 4px 0 0;
  padding: 0 3px !important;
  height: 14px;

  /* line-height: 12px !important; */
  font-size: 10px;
  min-width: 38px;
  text-align: center;
  justify-content: center;
}

/* 避免被主题色覆盖掉 */
.paidcontent-item__title-tag.zan-tag.zan-tag--danger {
  background-color: #f44 !important;
}
.paidcontent-item__title-tag.zan-tag.zan-tag--danger::after {
  border-color: #f44 !important;
}
.paidcontent-item__title {
  flex: 1 1 auto;
  display: inline-block;
  line-height: 18px;
  vertical-align: middle;
  color: #333;
  font-size: 14px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.paidcontent-item__title--multi {
  white-space: initial;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.paidcontent-item__subtitle {
  margin-top: 1px;
  line-height: 12px;
  color: #999;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.paidcontent-item__subtitle--notag {
  margin-top: 8px;
}

/* 状态信息 */
.paidcontent-item__status-list {
  line-height: 0;
  align-self: flex-end;
}
.paidcontent-item__status {
  display: inline-block;
  padding: 0 8px;
  line-height: 12px;
  vertical-align: middle;
  color: #999;
  font-size: 12px;
  border-right: 1px solid #e5e5e5;
}
.paidcontent-item__status:first-child {
  padding-left: 0;
}
.paidcontent-item__status:last-child {
  border-right: none;
}

/* 右上角 */
.paidcontent-item__corner-top {
  line-height: 0;
  text-align: right;
}
.paidcontent-item__corner-top-text {
  line-height: 14px;
  text-align: right;
  color: #f44;
  font-size: 14px;
}
.paidcontent-item__top-tag {
  line-height: 1.2 !important;
  margin-top: 5px;
  padding: 2px 3px !important;
}
.paidcontent-item-tag {
  line-height: 14px;
  font-size: 10px;
}

/* 右下角 */
.paidcontent-item__corner-bottom {
  line-height: 0;
}
.paidcontent-item__corner-bottom-text {
  line-height: 12px;
  text-align: right;
  color: #999;
  font-size: 12px;
}
.paidcontent-item__bottom-tag {
  line-height: 1.1 !important;
  white-space: nowrap;
  margin-bottom: 2px;
  padding: 2px 3px !important;
}

.paidcontent-item__info-status-list {
  position: absolute;
  bottom: 0;
  line-height: 12px;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #999;
  font-size: 12px;
}

.paidcontent-item__info {
  flex: 1 1 auto;
  overflow: hidden;
}
.paidcontent-item__info-title {
  width: 100%;
  overflow: hidden;
  line-height: 14px;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333;
  font-size: 14px;
  font-weight: 700;
}

/* 标签 */
.paidcontent-item__labels {
  position: relative;
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.paidcontent-item__label {
  position: absolute;
  bottom: 0;
  right: 0;
  white-space: nowrap;
}
.paidcontent-item__label-top {
  align-self: flex-end;
  top: 15px;
  right: 15px;
}

/* flex */
.paidcontent-item__flex-container {
  display: flex;
}

.paidcontent-item__course-type-name {
  position: absolute;
  left: 8px;
  bottom: 8px;
  z-index: 1;
  padding: 0 6px;
  line-height: 18px;
  border-radius: 9px;
  display: inline-block;
  box-sizing: border-box;
  vertical-align: top;
  margin-right: 3px;
  font-size: 12px;
  color: #fff;
  background-color: #f44;
}
