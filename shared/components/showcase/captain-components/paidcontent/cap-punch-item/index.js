const PUNCH_STATUS = {
  NOT_START: 1,
  STARTED: 2,
  ENDED: 3
};

Component({
  properties: {
    themeClass: String,
    mode: String,
    bought: Boolean,
    disabled: Boolean,
    price: Number,
    punch: {
      type: Object,
      observer: 'initState'
    }
  },

  data: {
    statusList: [],
    url: '',
    tagText: '',
    slots: {
      icon: true
    }
  },

  methods: {
    initState(punch) {
      // 确定跳转页面
      let url = `/packages/new-punch/introduction/index?alias=${punch.alias}`;
      if (this.data.bought || punch.bought) {
        url = `/packages/new-punch/task/index?alias=${punch.alias}&startDate=${punch.startAt}&proceedStatus=${punch.proceedStatus}`;
      }

      // 确定描述, 标签文案
      const statusList = [];
      let tagText = '';
      let daysDesc = '';
      let timesDesc = '';
      switch (punch.proceedStatus) {
        case PUNCH_STATUS.NOT_START:
          tagText = '未开始';
          daysDesc = `共计${punch.totalDays || 0}天`;
          timesDesc = '暂未开始打卡';
          break;
        case PUNCH_STATUS.STARTED:
          tagText = '进行中';
          daysDesc = `已进行${punch.proceedingDays || 0}天`;
          timesDesc = `你已打卡${punch.customerGciTimes || 0}次`;
          break;
        case PUNCH_STATUS.ENDED:
          tagText = '已结束';
          daysDesc = `共计${punch.totalDays || 0}天`;
          timesDesc = `你已打卡${punch.customerGciTimes || 0}次`;
          break;
        default:
          break;
      }
      // 处理已删除的数据
      // status: 0 -> 已删除 2 -> 已下架
      if (punch.status === 0) {
        tagText = '已删除';
        url = '';
      }

      if (!this.data.bought && !punch.bought) timesDesc = '';
      statusList.push(daysDesc);
      if (timesDesc) statusList.push(timesDesc);

      this.setData({
        tagText,
        statusList,
        url: this.data.disabled ? '' : url
      });
    },

    onMyPunchTap() {
      wx.navigateTo({
        url: this.data.bought || this.data.punch.bought
          ? `/packages/new-punch/rank/index?alias=${this.data.punch.alias}`
          : `/packages/new-punch/introduction/index?alias=${this.data.punch.alias}`
      });
    },

    onIntroTap() {
      wx.navigateTo({
        url: `/packages/new-punch/introduction/index?alias=${this.data.punch.alias}`
      });
    }
  }
});
