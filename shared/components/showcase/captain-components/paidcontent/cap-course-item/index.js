import computed from './computed';

Component({
  properties: {
    themeClass: String,
    course: {
      type: Object,
      value: {}
    },
  },

  data: {
    computed: {}
  },

  attached() {
    this.parseComputed();
  },

  methods: {
    /**
     * 手动计算之前wxs里面的字段，wxs坑太多，废弃
     */
    parseComputed() {
      const { course } = this.data;
      this.setData({
        'computed.subtitle': computed.subtitle(course),
        'computed.thumbnailUrl': computed.thumbnailUrl(course),
        'computed.statusList': computed.statusList(course),
        'computed.courseUrl': computed.courseUrl(course)
      });
    }
  }
});
