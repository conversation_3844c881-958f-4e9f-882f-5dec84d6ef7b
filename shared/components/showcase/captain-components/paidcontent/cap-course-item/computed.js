export default {
  subtitle(course) {
    return course.courseProductDTO.skuVal;
  },

  statusList(course) {
    const list = [];

    const studentName = course.userAssetDTO.studentName;
    list.push(studentName);

    return list;
  },

  courseUrl(course) {
    const url = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(`https://h5.youzan.com${course.courseDetailUrl}`)}`;
    return url;
  },

  thumbnailUrl(course) {
    const cover = course.courseProductDTO.pictureWrapDTO.url || '';
    return cover;
  }
};
