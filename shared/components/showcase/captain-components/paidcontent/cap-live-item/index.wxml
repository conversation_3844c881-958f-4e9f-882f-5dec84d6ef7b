<item
  class="cap-live-item"
  title="{{ live.title }}"
  icon="https://img01.yzcdn.cn/weapp/wsc/f1QVqZ.png"
  title-tag="{{ statusText[live.liveStatus] }}"
  title-tag-class="{{ statusColor[live.liveStatus] }}"
  status-list="{{ computed.statusList }}"
  subtitle="{{ live.summary }}"
  thumbnail="{{ live.cover }}"
  bottom-corner-class="{{ live.buyStatus && live.buyStatus.isBought ? '' : 'theme-color' }}"
  bottom-corner-text="{{ computed.priceText }}"
  url="{{ live.url }}">
</item>
