import { moment as formatDate } from 'utils/time';

export default {
  statusList(live) {
    const list = [];
    if (live.liveStartAt) {
      // safiri 下这个时间格式有坑，需要转化
      list.push(formatDate(live.liveStartAt.replace(/-/g, '/'), 'MM-DD HH:mm'));
    }
    if (live.pageView) {
      list.push(live.pageView + '次观看');
    }
    return list;
  },
  priceText(live) {
    if (live.showPriceInfo && live.sellerType !== 2) {
      var price = 0;
      if (live.buyStatus) {
        price = live.buyStatus.price;
      } else {
        price = live.price;
      }

      if (live.buyStatus && live.buyStatus.isBought) return '已购买';
      if (price == 0) return '免费';
      const _price = price / 100;
      return '￥ ' + _price.toFixed(2);
    }
    return '';
  },
};
