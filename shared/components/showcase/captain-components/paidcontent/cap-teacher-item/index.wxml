<form-button bindtap="onTeacherTap" business-module="owlMiniProgram">
<view
  class="teacher-item teacher-item--size{{size}} teacher-item--cardStyle{{cardStyle}} {{ size === 2 ? 'teacher-item--avatarPos' + avatarPos : '' }} teacher-item--avatarShape{{avatarShape}} teacher-item--goodsRadius{{goodsRadius}} teacher-item--imgSize{{imgSize}} teacher-item--textStyle{{textStyle}} teacher-item--textAlign{{textAlign}}"
>
  <view class="teacher-item__link" style="margin: {{ goodsPadding / 2 }}px">
    <view class="teacher-item__img" >
      <image
        src="{{ teacher.icon }}"
        alt="{{ teacher.description }}"
      />
    </view>
    <view class="teacher-item__info">
      <view wx:if="{{ displayContent.label }}" class="teacher-item__label">
        {{ teacher.teacherName ? teacher.teacherName : teacher.staffName }}
      </view>
      <view wx:if="{{ displayContent.duty }}" class="teacher-item__duty">
        {{ teacher.duty }}
      </view>
      <view wx:if="{{ displayContent.desc }}" class="teacher-item__desc">
        {{ teacher.description }}
      </view>
    </view>
  </view>
</view>
</form-button>
