Component({
  properties: {
    themeClass: String,
    mode: String,
    size: Number,
    goodsPadding: Number,
    cardStyle: Number,
    avatarPos: Number,
    avatarShape: Number,
    goodsRadius: Number,
    imgSize: Number,
    textStyle: Number,
    textAlign: Number,
    displayContent: {
      type: Object
    },
    teacher: {
      type: Object
    }
  },

  data: {
    /*
    prefix: 'teacherItem',
    statusList: [],
    url: '',
    tagText: '',
    slots: {
      icon: true
    }
    */
  },

  methods: {
    onTeacherTap() {
      const app = getApp();
      // https://h5.youzan.com/wscvis/edu/master-detail?kdt_id=43202000&teacherId=1033453361&reft=1565681283797&spm=f.80653649
      const url = `/packages/edu/webview/index?targetUrl=${encodeURIComponent(`https://h5.youzan.com/wscvis/edu/master-detail?&kdt_id=${app.getKdtId()}&teacherId=${this.data.teacher.id}`)}`;

      wx.navigateTo({ url });
    }
  }
});
