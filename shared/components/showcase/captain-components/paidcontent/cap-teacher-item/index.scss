.teacher-item {
  height: 100%;
  position: relative;
  display: flex;
  &__link {
    display: flex;
    color: inherit;
    overflow: hidden;
    background: #fff;
    box-sizing: border-box;
    flex: 1;
  }
  &__img {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 0;
    padding-bottom: 100%;
    image {
      position: absolute;
      top: 0;
      left: 0;
      display: block;
      width: 100%;
      height: 100%;
    }
  }
  &__info {}
  &__label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #333;
    line-height: 22px;
  }
  &__duty {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #999;
    line-height: 16px;
  }
  &__desc {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: #666;
    line-height: 16px;
  }

  /* 一行两个 */
  &--size0 {
    .teacher-item__link {
      padding: 15px;
      flex-direction: column;
    }
    .teacher-item__img {
      /* width: 100%;
      height: 138px; */
    }
    .teacher-item__label {
      font-size: 16px;
      margin-top: 15px;
    }
    .teacher-item__duty {
      font-size: 12px;
      margin-top: 5px;
    }
    .teacher-item__desc {
      font-size: 12px;
      margin-top: 5px;
    }

    &.teacher-item--avatarShape1 {
      .teacher-item__img {
        /* width: 100px;
        height: 100px; */
      }
    }
  }
  /* 一行三个 */
  &--size1 {
    .teacher-item__link {
      padding: 10px;
      flex-direction: column;
    }
    .teacher-item__img {
      /* width: 100%;
      height: 88px; */
    }
    .teacher-item__label {
      font-size: 14px;
      margin-top: 15px;
      line-height: 20px;
    }
    .teacher-item__duty {
      font-size: 12px;
      margin-top: 5px;
    }
    .teacher-item__desc {
      font-size: 12px;
      margin-top: 5px;
    }
  }
  /* 详细列表 */
  &--size2 {
    .teacher-item__link {
      height: 118px;
      padding: 15px;
      justify-content: space-between;
      flex: 1;
      width: 0;
    }
    .teacher-item__img {
      width: 88px;
      height: 88px;
      margin-right: 15px;
      flex-shrink: 0;
      padding-bottom: 0;
    }
    .teacher-item__label {
      font-size: 16px;
    }
    .teacher-item__duty {
      font-size: 12px;
    }
    .teacher-item__desc {
      font-size: 12px;
    }
    .teacher-item__info {
      flex: 1;
      text-align: left !important;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      width: 0;
    }
  }
  /* 横向滚动 */
  &--size3 {
    .teacher-item__link {
      /* width: 98px;
      height: 186px; */
      padding: 10px;
      flex-direction: column;
    }
    .teacher-item__img {
      /* width: 100%;
      height: 78px; */
    }
    .teacher-item__label {
      font-size: 14px;
      margin-top: 15px;
      line-height: 20px;
    }
    .teacher-item__duty {
      font-size: 12px;
      margin-top: 5px;
    }
    .teacher-item__desc {
      font-size: 12px;
      margin-top: 5px;
    }
  }

  &--avatarShape0 {
    .teacher-item__img {
      image {
        border-radius: 50%;
      }
    }
  }
  &--avatarShape1 {
    .teacher-item__img {
      image {
        border-radius: 8px;
      }
    }
  }

  &--textStyle0 {
    .teacher-item__label {
      font-weight: bold;
    }
  }
  &--textStyle1 {
    .teacher-item__label {
      font-weight: normal;
    }
  }

  &--textAlign0 {}
  &--textAlign1 {
    .teacher-item__info {
      text-align: center;
    }
  }

  &--avatarPos1 {}
  &--avatarPos2 {
    .teacher-item__link {
      flex-direction: row-reverse;
    }
    .teacher-item__img {
      margin-left: 15px;
      margin-right: 0;
    }
  }

  &--cardStyle0 {}
  &--cardStyle1 {
    .teacher-item__link {
      box-shadow: 0 2px 8px rgba(93, 113, 127, .08);
    }
  }
  &--cardStyle2 {
    .teacher-item__link {
      border: 1px solid #E6E6E6;
    }
  }
  &--cardStyle3 {
    .teacher-item__link {
      background-color: transparent;
    }
  }

  &--goodsRadius0 {
    .teacher-item__link {
      border-radius: 0px;
    }
  }
  &--goodsRadius1 {
    .teacher-item__link {
      border-radius: 8px;
    }
  }
}

cap-teacher-item:nth-child(odd) {
  .teacher-item--avatarPos0 {
    .teacher-item__link {
      flex-direction: row-reverse;
    }
    .teacher-item__img {
      margin-left: 15px;
      margin-right: 0;
    }
  }
}
