import computed from './computed';

const BUY_STATUS = {
  PAID: 0,
  FREE: 1,
  NORMAL: 2,
  VIP_FREE: 3,
  VIP_DISCOUNT: 4,
  GROUPON: 5,
  HIDE: 6
};

Component({
  properties: {
    themeClass: String,
    column: {
      type: Object,
      value: {}
    },
    showPriceInfo: Boolean
  },

  data: {
    buyStatus: 0,
    isPaid: false,
    computed: {}
  },

  attached() {
    this.initState();
  },

  methods: {
    getPaidStatus(buyStatus) {
      return buyStatus === BUY_STATUS.PAID;
    },

    getBuyStatus(column) {
      const { showPriceInfo } = this.data;
      const {
        price,
        isBought = false,
        isFree = false,
        isVipDiscount = false,
        isFreeForVip = false,
        isGroupOn = false
      } = column;

      // 店铺允许小程序购买则展示价格相关的信息
      if (showPriceInfo) {
        if (isBought) return BUY_STATUS.PAID;
        if (isFree) return BUY_STATUS.FREE;
        if (isFreeForVip) return BUY_STATUS.VIP_FREE;
        if (price === 0) return BUY_STATUS.FREE;
        /* if (isGroupOn) return BUY_STATUS.GROUPON;
        if (isVipDiscount) return BUY_STATUS.VIP_DISCOUNT; */
        if (isGroupOn) return BUY_STATUS.GROUPON;
        if (isVipDiscount) return BUY_STATUS.VIP_DISCOUNT;
        return BUY_STATUS.NORMAL;
      }
      return BUY_STATUS.HIDE;
    },

    initState() {
      const { column = {} } = this.data;
      const buyStatus = this.getBuyStatus(column);
      this.setData({
        buyStatus,
        isPaid: this.getPaidStatus(buyStatus),
      });
      this.parseComputed();
    },

    /**
     * 手动计算之前wxs里面的字段，wxs坑太多，废弃
     */
    parseComputed() {
      const {
        column, isPaid, buyStatus
      } = this.data;
      this.setData({
        'computed.subtitle': computed.subtitle(column, isPaid),
        'computed.thumbnailUrl': computed.thumbnailUrl(column),
        'computed.statusList': computed.statusList(column, isPaid),
        'computed.priceText': computed.priceText(column, buyStatus),
        'computed.priceTagText': computed.priceTagText(column, buyStatus),
        'computed.columnUrl': computed.columnUrl(column)
      });
    }
  }
});
