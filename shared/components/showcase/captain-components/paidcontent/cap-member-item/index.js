import computed from './computed';

Component({
  properties: {
    hideTag: {
      type: Boolean,
      value: false
    },
    themeClass: String,
    alias: {
      type: String,
      value: ''
    },
    cover: {
      type: String,
      value: ''
    },
    count: {
      type: Number,
      value: 0
    },
    subCount: {
      type: Number,
      value: 0
    },
    summary: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      value: ''
    },
    price: null,
    time: {
      type: String,
      value: ''
    },
    noBorder: {
      type: Boolean,
      value: false
    },
    hasBuy: {
      type: Boolean,
      value: false
    },
    isGroupon: {
      type: Boolean,
      value: false
    },
    grouponNum: {
      type: Number,
      value: 0
    },
    isFree: Number,
    isPaid: Boolean,
    isVip: {
      type: Boolean,
      value: false
    },
    tagName: {
      type: Number,
      value: 0
    },
    showPriceInfo: Boolean
  },

  data: {
    computed: {}
  },

  attached() {
    this.parseComputed();
  },

  methods: {
    goToMember(e) {
      const { alias } = e.currentTarget.dataset;
      if (!alias) return;

      wx.navigateTo({
        url: `/packages/paidcontent/rights/index?alias=${alias}`
      });
    },

    /**
     * 手动计算之前wxs里面的字段，wxs坑太多，废弃
     */
    parseComputed() {
      const {
        count, subCount, isPaid, price, showPriceInfo
      } = this.data;
      this.setData({
        'computed.statusList': computed.statusList(count, subCount, isPaid),
        'computed.priceText': computed.priceText(isPaid, price, showPriceInfo)
      });
    }
  },
});
