import computed from './computed';

const BUY_STATUS = {
  PAID: 0,
  FREE_TRY: 1,
  NORMAL: 2,
  COLUMN_ONLY: 3,
  VIP_FREE: 4,
  VIP_DISCOUNT: 5,
  GROUPON: 6,
  FREE: 7,
  HIDE: 8
};

Component({
  properties: {
    content: {
      type: Object,
      value: {}
    },
    showPriceInfo: Boolean
  },

  data: {
    buyStatus: 0,
    isPaid: false,
    inColumn: false,
    columnOnly: false,
    typeSuffix: '',
    computed: {}
  },

  attached() {
    this.initState();
  },

  methods: {
    getPaidStatus(buyStatus) {
      return buyStatus === BUY_STATUS.PAID;
    },

    getBuyStatus(content, columnOnly) {
      const { showPriceInfo } = this.data;
      const {
        price,
        isBought = false,
        isFree = false,
        isVipDiscount = false,
        isFreeForVip = false,
        isGroupOn = false
      } = content;

      if (showPriceInfo) {
        if (isBought) return BUY_STATUS.PAID;
        if (isFree) return BUY_STATUS.FREE_TRY;
        if (columnOnly) return BUY_STATUS.COLUMN_ONLY;
        if (isFreeForVip) return BUY_STATUS.VIP_FREE;
        if (price === 0) return BUY_STATUS.FREE;
        if (isGroupOn) return BUY_STATUS.GROUPON;
        if (isVipDiscount) return BUY_STATUS.VIP_DISCOUNT;
        return BUY_STATUS.NORMAL;
      }
      return BUY_STATUS.HIDE;
    },

    getTypeSuffix(mediaType) {
      return [
        '',
        '读',
        '听',
        '看',
        '看'
      ][+mediaType];
    },

    getCountSuffix(mediaType) {
      return [
        '',
        '学习',
        '学习',
        '观看',
        '观看'
      ][+mediaType];
    },

    initState() {
      const { content = {} } = this.data;
      const inColumn = !!content.columnAlias;
      const columnOnly = content.sellerType === 2;
      const buyStatus = this.getBuyStatus(content, columnOnly);

      this.setData({
        columnOnly,
        inColumn,
        buyStatus,
        isPaid: this.getPaidStatus(buyStatus),
        typeSuffix: this.getTypeSuffix(content.mediaType),
        countSuffix: this.getCountSuffix(content.mediaType),
      });
      this.parseComputed();
    },

    goToContent(e) {
      const { alias } = e.currentTarget.dataset;
      if (!alias) return;
      wx.navigateTo({
        url: `/packages/paidcontent/content/index?alias=${alias}`
      });
    },

    /**
     * 手动计算之前wxs里面的字段，wxs坑太多，废弃
     */
    parseComputed() {
      const {
        content, inColumn, isPaid, typeSuffix, countSuffix, buyStatus
      } = this.data;
      this.setData({
        'computed.subtitle': computed.subtitle(content, inColumn),
        'computed.thumbnailUrl': computed.thumbnailUrl(content),
        'computed.icon': computed.icon(content),
        'computed.statusList': computed.statusList(content, isPaid, countSuffix),
        'computed.priceText': computed.priceText(content, buyStatus, typeSuffix),
        'computed.priceTagText': computed.priceTagText(content, buyStatus),
        'computed.contentUrl': computed.contentUrl(content)
      });
    }
  }
});
