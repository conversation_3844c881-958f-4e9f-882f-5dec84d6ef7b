<item
  class="cap-content-item"
  title="{{ content.title }}"
  subtitle="{{ computed.subtitle }}"
  thumbnail="{{ computed.thumbnailUrl }}"
  icon="{{ computed.icon }}"
  status-list="{{ computed.statusList }}"
  bottom-corner-class="{{ isPaid ? '' : 'theme-color' }}"
  bottom-corner-text="{{ computed.priceText }}"
  bottom-tag-text="{{ computed.priceTagText }}"
  url="{{ computed.contentUrl }}">
</item>
