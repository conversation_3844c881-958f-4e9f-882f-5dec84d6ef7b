# 列表

使用 intersectionObserver 来监听是否滑动到底部，是否需要加载更多。

- 当 list 组件滑动到底部时，会抛出`load`事件，父组件可以通过监听这个事件来进行加载数据的操作
- 父组件加载数据时，需要给 list 组件设置`loading: true`，之后 list 组件不会再抛出`load`事件
- 加载完成后需要设置`loading: false`
- 当父组件加载完全部数据后，可以设置`finished: true`，这会移除底部 loading 的占位
- 如果想完全移除 observer，可以设置`disabled: true`

## 手动 check
在某些时候，接口返回的一页数据的 length 会小于 pageSize，极端情况下会导致组件的高度不足以占满一屏，所以为了能继续触发加载下一页，可以手动调用 list 组件的`check`方法。

该方法会检查 list 组件的底部 hook 是否还在屏幕内，如果满足则会抛出`load`事件。

## 自动 check
设置`autoCheck: true`后，会监听`loading: false`的设置，然后检测是否满足加载下一页的条件

## Props

| 属性名 | 类型 | 默认值 | 描述 |
|-------|-----|--------|----|
| `loading` | `Boolean` | `false` | 加载中 |
| `finished` | `Boolean` | `false` | 全部加载完成 |
| `relativeTo` | `String` | `viewport` | 相对区域 |
| `bottomDistance` | `Number` | `200` | 扩展相交边界 |
| `disabled` | `Boolean` | `false` | 禁用 |
