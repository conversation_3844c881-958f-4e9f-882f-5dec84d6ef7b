let windowHeight = 0;

const getWindowHeight = () => {
  if (!windowHeight) {
    windowHeight = wx.getSystemInfoSync().windowHeight;
  }

  return windowHeight;
};

Component({
  properties: {
    loading: {
      type: Boolean,
      value: false,
      observer(value) {
        if (!value && this.properties.autoCheck) {
          setTimeout(() => {
            this.check();
          }, 500);
        }
      }
    },
    finished: {
      type: Boolean,
      value: false
    },
    relativeTo: {
      type: String,
      value: 'viewport'
    },
    bottomDistance: {
      type: Number,
      value: 200
    },
    disabled: {
      type: Boolean,
      value: false,
      observer: 'removeObserver'
    },
    autoCheck: {
      type: Boolean,
      value: false,
    },
  },

  ready() {
    this.initObserver();
  },

  detached() {
    this.removeObserver();
  },

  methods: {
    initObserver() {
      const {
        relativeTo,
        bottomDistance,
        disabled
      } = this.properties;

      if (disabled) {
        return;
      }

      const margins = { bottom: bottomDistance };
      const ob = this.createIntersectionObserver();

      if (relativeTo === 'viewport') {
        ob.relativeToViewport(margins);
      } else {
        ob.relativeTo(relativeTo, margins);
      }

      ob.observe('.c-hook', ({ intersectionRatio: ratio }) => {
        const {
          loading,
          finished,
          disabled
        } = this.properties;

        if (!loading && !finished && !disabled && ratio > 0) {
          this.triggerEvent('load');
        }
      });

      this.ob = ob;
    },

    removeObserver() {
      if (this.ob) {
        this.ob.disconnect();
        this.ob = null;
      }
    },

    check() {
      const {
        loading,
        finished,
        disabled,
        bottomDistance
      } = this.properties;

      if (loading || finished || disabled) {
        return;
      }

      this.createSelectorQuery()
        .select('.c-hook')
        .boundingClientRect(rect => {
          if (!rect) {
            return;
          }
          if (rect.top >= 0 && rect.top <= getWindowHeight() + bottomDistance) {
            this.triggerEvent('load');
          }
        })
        .exec();
    }
  }
});
