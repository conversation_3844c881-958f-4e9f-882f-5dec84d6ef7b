Component({
  properties: {
    config: {
      type: Object,
      default: () => {},
    },
    hotStyle: {
      type: String,
      default: '',
    },
    hot: {
      type: Array,
      default: () => [],
    },
    normal: {
      type: Array,
      default: () => [],
    },
    isSingle: {
      type: Boolean,
      default: false,
    },
    needOpacity: {
      type: Boolean,
      default: false,
    },
  },
  data: {
    extraCls: '',
  },
  attached() {
    this.initData();
  },
  methods: {
    handleSearch(e) {
      const { wordItem, index } = e?.currentTarget?.dataset;
      this.triggerEvent('search', {
        wordItem,
        index,
      });
    },
    initData() {
      const { isSingle, needOpacity } = this.data;
      const lightCls = needOpacity ? 'hot-words-tags-light' : '';
      const singleCls = isSingle ? 'hot-words-tags-inline' : '';

      this.setData({
        extraCls: `${lightCls} ${singleCls}`,
      });
    },
  },
});
