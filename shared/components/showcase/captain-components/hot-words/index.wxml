<view
    class="cap-hot-words-box {{ isSingle ? 'cap-hot-words-box-inline' : '' }}"
  >
    <view
      class="hot-words-title" 
      wx:if="{{ config.title }}"
    >
      {{ config.title }}
    </view>
    <view 
      class="hot-words-fire"
      wx:if="{{ isSingle }}"
    >
      <van-icon name="fire" color="#ee0a24" />
      热搜
    </view>
    <cap-hot-words-tag
      wx:for="{{ hot }}"
      wx:for-item="item"
      wx:for-index="index"
      wx:key="{{ index }}"
      data-word-item="{{ item }}"
      data-index="{{ index }}"
      item="{{ item }}"
      showHot="{{ config.is_hot }}"
      extraCls="{{ extraCls }}"
      themeStyle="{{ hotStyle }}"
      bind:search="handleSearch"
    />
    <cap-hot-words-tag
      wx:for="{{ normal }}"
      wx:for-item="item"
      wx:for-index="index"
      wx:key="{{ index }}"
      data-word-item="{{ item }}"
      data-index="{{ hot.length + index }}"
      item="{{ item }}"
      bind:search="handleSearch"
    />
  </view>