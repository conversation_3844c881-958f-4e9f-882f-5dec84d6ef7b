<view class="wxvideo-reserve-item-wrap" style="{{wrapStyle}}">
    <view class="wxvideo-reserve-item" style="{{itemStyle}}">
      <view class="item-left">
        <view class="item-title-wrap">
          <view class="reserve-icon" style="{{reserveIconStyle}}">预告</view>
          <view class="item-title">{{ description }}</view>
        </view>
        <view class="item-time">{{ startTime }}</view>
      </view>
      <view
        class="item-right"
        style="{{reserveBtnStyle}}"
        bindtap="handleReserve"
        >{{ isReserved ? '取消预约' : '预约直播' }}</view
      >
    </view>
  </view>