import componentBehavior from '../../behaviors/component-behavior';
import { moment as formatDate } from 'utils/time';
import api from '../api';
import { cutNoticeId } from '../utils';
import { borderRadioMap, stateMap } from '../const';

const app = getApp();

Component({
  behaviors: [componentBehavior],

  attached() {
    api
      .queryLiveNotice({
        noticeId: cutNoticeId(this.data.liveNoticeInfo.noticeId),
      })
      .then((res) => {
        const { noticeStatus } = res;
        if (noticeStatus == 1) {
          this.setData({ isReserved: true, reserveBtnStyle: '' });
        }
      });
  },

  properties: {
    bannerId: {
      type: String,
      value: '',
    },
    liveNoticeInfo: {
      type: Object,
      value: {},
      observer(newInfo) {
        const startTime = newInfo.startTime + '000';
        const startTimeDate = new Date(+startTime);
        const startDay = startTimeDate.getDate();
        const now = new Date().getDate();
        let time = '';
        if (startDay === now) {
          time = `今天 ${formatDate(startTimeDate, 'hh:mm:ss')}`;
        } else {
          time = formatDate(startTimeDate, 'M月dd日 hh:mm:ss');
        }

        this.setData({
          description: `${newInfo.nickname}视频号直播`,
          startTime: `${time} 开播`,
        });
      },
    },
    opt: {
      type: Object,
      value: {},
      observer(newOpt) {
        const { page_margin, corner_type } = newOpt;

        const borderRadius =
          corner_type == borderRadioMap.round ? '8rpx' : 'none';

        this.setData({
          wrapStyle: `padding: 0 ${page_margin}px;`,
          itemStyle: `border-radius: ${borderRadius}`,
        });
      },
    },
    themeColors: {
      type: Object,
      value: {},
      observer(newThemeColor) {
        const { general } = newThemeColor;
        this.setData({
          reserveIconStyle: `color: ${general}`,
        });
      },
    },
  },

  data: {
    isReserved: false,
    wrapStyle: '',
    itemStyle: '',
    reserveIconStyle: '',
    reserveBtnStyle: '',
    description: '',
    startTime: '',
  },

  methods: {
    handleReserve() {
      wx.reserveChannelsLive &&
        wx.reserveChannelsLive({
          noticeId: this.data.liveNoticeInfo.noticeId,
          success: (res) => {
            const { state } = res;
            // 埋点
            app.logger.log({
              et: 'click', // 事件类型
              ei: 'click_contnet', // 事件标识
              en: '内容点击', // 事件名称
              params: {
                state,
                banner_id: this.data.bannerId,
                component: 'wxvideo_live',
                type: 'click_wxvideo_reserve',
                noticeInfo: JSON.stringify(res),
              },
            });

            if (state == stateMap.reserve) {
              api
                .subscribe({
                  noticeId: cutNoticeId(this.data.liveNoticeInfo.noticeId),
                })
                .then(() => {
                  this.setData({
                    isReserved: true,
                    reserveBtnStyle: '',
                  });
                });
            }
            if (state == stateMap.cancel) {
              api
                .cancelSubscribe({
                  noticeId: cutNoticeId(this.data.liveNoticeInfo.noticeId),
                })
                .then(() => {
                  this.setData({
                    isReserved: false,
                    reserveBtnStyle: `color: ${this.themeColors.general};border-color: ${this.themeColors.general}`,
                  });
                });
            }
          },
        });
    },
  },
});
