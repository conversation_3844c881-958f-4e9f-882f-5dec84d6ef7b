import spm from 'shared/utils/spm';
import EventUtil from '@youzan/weapp-utils/lib/event';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import componentBehavior from '../behaviors/component-behavior';
import { liveStatusMap, showReserveMap } from './const';
import api from './api';

const app = getApp();

Component({
  behaviors: [componentBehavior],

  attached() {
    this.getChannelId().then(() => {
      this.initData();
    });
    this.setData({
      bannerId: this.getBannerId(),
    });
  },

  detached() {
    EventUtil.off('FeatureAliasLoaded');
  },

  properties: {
    themeColors: {
      type: Object,
      value: {},
    },
  },

  data: {
    finderUserName: '', // 视频号id
    liveInfo: null,

    liveNoticeInfo: null,
    isLiving: false,
    hasNotice: false,
  },

  ready() {
    EventUtil.on('FeatureAliasLoaded', () => {
      this.initData();
    });
  },

  methods: {
    onPullDownRefresh() {
      this.initData();
    },

    initData() {
      this.initChannelsLiveInfo();
      this.initChannelsLiveNoticeInfo();
    },

    handleToLive() {
      app.logger.log({
        et: 'click', // 事件类型
        ei: 'click_contnet', // 事件标识
        en: '内容点击', // 事件名称
        params: {
          status: this.data.liveInfo.status,
          banner_id: this.data.bannerId,
          component: 'wxvideo_live',
          type: 'click_wxvideo_living',
        },
      });

      wx.openChannelsLive &&
        wx.openChannelsLive({
          feedId: this.data.liveInfo.feedId,
          nonceId: this.data.liveInfo.nonceId,
          finderUserName: this.data.finderUserName,
        });
    },

    // 直播信息
    initChannelsLiveInfo() {
      const { finderUserName } = this.data;

      if (finderUserName) {
        wx.getChannelsLiveInfo &&
          wx.getChannelsLiveInfo({
            finderUserName,
            success: (res) => {
              const { status } = res;

              // 埋点
              app.logger.log({
                et: 'view',
                ei: 'component_view',
                en: '组件曝光',
                params: {
                  status,
                  banner_id: this.data.bannerId,
                  component: 'wxvideo_live',
                  type: 'view_wxvideo_living',
                  noticeInfo: JSON.stringify(res),
                },
              });

              this.setData({
                isLiving: status == liveStatusMap.isLiving,
                liveInfo: res,
              });
            },
            fail: (e = {}) => {
              // 埋点
              app.logger.log({
                et: 'view',
                ei: 'component_view_fail',
                en: '视频号组件曝光失败',
                params: {
                  component: 'wxvideo_live',
                  errCode: e.err_code,
                  finderUserName,
                  noticeInfo: JSON.stringify(e),
                },
              });

              api.setErrorVideoId({ videoId: finderUserName });
            },
          });
      }
    },

    // 预告信息
    initChannelsLiveNoticeInfo() {
      const { finderUserName } = this.data;

      if (showReserveMap.hide == this.data.componentData.show_reserve) return;

      if (finderUserName) {
        wx.getChannelsLiveNoticeInfo &&
          wx.getChannelsLiveNoticeInfo({
            finderUserName,
            success: (res) => {
              const { status } = res;

              if (status == 0) {
                this.setData({
                  liveNoticeInfo: res,
                  hasNotice: true,
                });

                // 埋点
                app.logger.log({
                  et: 'view',
                  ei: 'component_view',
                  en: '组件曝光',
                  params: {
                    status,
                    banner_id: this.data.bannerId,
                    component: 'wxvideo_live',
                    type: 'view_wxvideo_reserve',
                    noticeInfo: JSON.stringify(res),
                  },
                });
              } else {
                this.setData({
                  hasNotice: false,
                });
              }
            },
          });
      }
    },

    getChannelId() {
      return api.getWeixinVideoInfo().then((res) => {
        const { videoChannelId = '' } = res || {};
        const finderUserName = videoChannelId;
        this.setData({
          finderUserName,
        });
      });
    },

    getBannerId() {
      return `${spm.getPageSpmTypeId()}~cpsShopRecommend.0~0~${makeRandomString(
        8
      )}`;
    },
  },
});
