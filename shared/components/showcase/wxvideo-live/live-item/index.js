import componentBehavior from '../../behaviors/component-behavior';
import { borderRadioMap } from '../const';

Component({
  behaviors: [componentBehavior],

  attached() {},

  properties: {
    liveInfo: {
      type: Object,
      observer(newInfo) {
        this.setData({
          bgStyle: `background-image: url('${newInfo.headUrl}')`,
          title: newInfo.description || `${newInfo.nickname}视频号直播`,
        });
      },
    },
    opt: {
      type: Object.apply,
      observer(newOpt) {
        const { page_margin, corner_type } = newOpt;

        const borderRadius =
          corner_type == borderRadioMap.round ? '8rpx' : 'none';
        this.setData({
          wrapStyle: `padding: 0 ${page_margin}px;`,
          itemStyle: `border-radius: ${borderRadius}`,
        });
      },
    },
    isLiving: Boolean,
  },

  data: {
    wrapStyle: '',
    itemStyle: '',
    bgStyle: '',
    title: '',
  },

  methods: {
    onClick() {
      this.triggerEvent('item-click');
    },
  },
});
