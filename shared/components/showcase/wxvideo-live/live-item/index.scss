@keyframes wave_new {
  0% {
    height: 10rpx;
  }

  50% {
    height: 20rpx;
  }

  100% {
    height: 10rpx;
  }
}

@keyframes wave_low_new {
  0% {
    height: 10rpx;
  }
  50% {
    height: 16rpx;
  }
  100% {
    height: 10rpx;
  }
}

.wxvideo-live-item-wrap {
  box-sizing: border-box;
  width: 100%;
  height: 176rpx;
  position: relative;
}

.wxvideo-live-item {
  height: 176rpx;
  display: flex;
  width: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  position: relative;
  overflow: hidden;

  .item-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: inherit;
    filter: blur(10px); /*为了模糊更明显，调高模糊度*/
    z-index: 2;
  }
}

.wxvideo-live-item-content {
  height: 100%;
  display: flex;
  width: 100%;
  overflow: hidden;
  position: absolute;
  left: 0;
  top: 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  z-index: 3;

  .item-banner {
    width: 176rpx;
    height: 176rpx;
    flex-shrink: 0;
  }

  .item-content {
    padding: 24rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    background: rgba(0, 0, 0, 0.2);
    overflow: hidden;

    .item-title {
      font-weight: bold;
      font-size: 28rpx;
      color: #ffffff;
      line-height: 40rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 100%;
    }

    .item-bottom-end {
      width: 120rpx;
      height: 36rpx;
      border-radius: 4rpx;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 36rpx;
      text-align: center;
      background: rgba(0, 0, 0, 0.2);
    }

    .item-bottom {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 100%;

      .icon-wrap {
        width: 120rpx;
        height: 36rpx;
        background-image: linear-gradient(146deg, #ff2282 0%, #e23d49 100%);
        border-radius: 4rpx;
        font-size: 24rpx;
        color: #ffffff;
        line-height: 36rpx;
        display: flex;
        align-items: center;
        padding: 0 12rpx;
        box-sizing: border-box;
      }

      .handle-btn {
        width: 144rpx;
        height: 50rpx;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 25rpx;
        font-size: 26rpx;
        color: #ffffff;
        line-height: 50rpx;
        text-align: center;
      }
    }
  }

  .default-live-icon-wave {
    width: 20rpx;
    height: 20rpx;
    box-sizing: border-box;
    margin-right: 4rpx;

    .wave-wrap {
      height: 20rpx;
      display: flex;
      align-items: flex-end;
      justify-content: center;
    }

    .wave-item {
      width: 4rpx;
      height: 10rpx;
      border-radius: 2rpx;
      background: #fff;

      + .wave-item {
        margin-left: 4rpx;
      }

      &.wave-1 {
        animation: wave_low_new 1s ease-in-out infinite;
      }

      &.wave-2 {
        animation: wave_new 1s ease-in-out 0.4s infinite;
      }

      &.wave-3 {
        animation: wave_low_new 1s ease-in-out 0.8s infinite;
      }
    }
  }
}