import { node as Api } from 'shared/utils/request';

export default {
  // 获取视频号基本信息
  getWeixinVideoInfo() {
    return Api({
      path: '/wscdeco/weapp/WeappAccount/getWeixinVideoInfo.json',
    })
      .then((data) => {
        return data || {};
      })
      .catch(() => {});
  },

  // 预约
  subscribe(data) {
    return Api({
      path: '/wscdeco/wxvideo/LiveNotice/subscribe.json',
      data,
    });
  },

  // 取消预约
  cancelSubscribe(data) {
    return Api({
      path: '/wscdeco/wxvideo/LiveNotice/cancelSubscribe.json',
      data,
      method: 'POST',
    });
  },

  // 获取直播状态
  queryLiveNotice(data) {
    return Api({
      path: '/wscdeco/wxvideo/LiveNotice/queryLiveNotice.json',
      data,
    });
  },

  // 上报视频号id错误
  setErrorVideoId(data) {
    return Api({
      path: '/wscdeco/wxvideo/LiveNotice/setErrorVideoId.json',
      data,
      method: 'POST',
    });
  },
};
