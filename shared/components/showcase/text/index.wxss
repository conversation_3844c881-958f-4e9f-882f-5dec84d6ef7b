.cap-text {
  position: relative;
  padding: 0 10px;
}

.cap-text__content {
  line-height: 44px;
  word-break: break-all;
}

.cap-text__icon {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  font-size: 12px;
  color: #cbc9cc;
}

/* 客服链接用透明按钮 */
.text-transparent-btn {
  display: block;
  border-radius: 0;
  background: transparent;
  background-color: transparent;
  border-color: transparent;
  border: none;
  outline: none;
}

.text-transparent-btn::after {
  display: none;
  visibility: hidden;
}

.cap-text--has-link .cap-text__content {
  padding-right: 20px;
}

.cap-text--has-line .cap-text__content {
  border-bottom: 1rpx solid #e5e5e5;
}


