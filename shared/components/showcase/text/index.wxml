<view
  class="cap-text {{ linkTextClass }}"
  style="{{ bgColor ? 'background: ' + bgColor : '' }}">
  <cap-navigator
    link-type="{{ linkType }}"
    app-id="{{ appId }}"
    path="{{ path }}"
    use-short-link="{{ useShortLink }}"
    short-link="{{ shortLink }}"
    custom-style="{{ linkTextStyle }}"
    custom-class="cap-text__content"
    im="{{ extraData }}"
    bind:navigate="handleNavigate"
    bind:contactback="handleContactBack"
  >
    {{ text }}
  </cap-navigator>
  <van-icon wx:if="{{ hasLink }}" name="arrow" class="cap-text__icon" />
</view>
