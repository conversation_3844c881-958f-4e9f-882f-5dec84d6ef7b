import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { getLink } from '../utils/linkAdaptor';

Component({
  behaviors: [componentBehavior, loggerBehavior],

  data: {
    // 有无跳转链接
    hasLink: false
  },

  attached() {
    const { componentData } = this.properties;

    const itemData = mapKeysCase.toCamelCase(componentData);
    itemData.extraData = mapKeysCase.toCamelCase(componentData.extraData);

    const {
      text,
      bgColor,
    } = itemData;

    this.setData({
      hasLink: itemData.linkType || itemData.linkUrl,
      ...getLink(itemData),
      text,
      bgColor,
      linkTextStyle: this.computeLinkTextStyle(itemData),
      linkTextClass: this.computeLinkTextClass(itemData),
    });
  },

  methods: {
    computeLinkTextStyle(itemData) {
      const { textAlign, color, fontSize } = itemData;

      const computeStyle = `text-align:${textAlign}; color:${color}; font-size: ${fontSize}px;`;
      return computeStyle;
    },

    computeLinkTextClass(itemData) {
      const { isLinkText, showSplitLine } = itemData;
      let linkTextClass = [];

      if (isLinkText) linkTextClass.push('cap-text--has-link');
      if (showSplitLine) linkTextClass.push('cap-text--has-line');

      return linkTextClass;
    },

    handleNavigate() {
      const { componentData } = this.properties;
      const bannerId = this.getBannerId();
      const item = mapKeysCase.toSnakeCase(componentData);

      this.triggerEvent('jumpToLink', {
        type: item.link_type,
        item,
        extra: {
          banner_id: bannerId
        }
      });
    },

    handleContactBack({ detail = {} }) {
      this.triggerEvent('contactback', detail);
    }
  }
});
