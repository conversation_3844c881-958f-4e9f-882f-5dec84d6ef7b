
import { node as request } from 'shared/utils/request';
import componentBehavior from '../behaviors/component-behavior';
import { shopCanPayForKnowledge } from '../utils/pctUtils';

Component({
  options: {
    addGlobalClass: true
  },

  type: 'punch',

  behaviors: [componentBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
    },
    kdtId: {
      type: String
    }
  },

  data: {
    itemType: 'punch',
    title: '',
    showTitle: false,
    showTitleAll: false,
    list: []
  },

  attached() {
    const itemData = this.data.componentData;
    this.setData({
      itemType: 'punch',
      title: itemData.title || itemData.name || '',
      showTitle: +itemData.show_title === 1,
      showTitleAll: +itemData.show_title_all === 1,
      list: []
    });
    this.getPunchList(itemData);
  },

  methods: {
    getPunchList(itemData) {
      if (itemData.punch_from === 'custom') {
        itemData.aliases = itemData.sub_entry.map(item => item.alias).join(',');
      }

      request({
        path: '/wscvis/punch/getFeatureList',
        data: {
          source: itemData.punch_from === 'custom' ? 1 : 0,
          aliasList: itemData.aliases ? itemData.aliases : '',
          kdtId: this.data.kdtId,
          pageSize: 3
        }
      }).then((result) => {
        if (!result) {
          return;
        }
        this.setData({ list: result });
      });
    },

    onAllTap() {
      wx.navigateTo({
        url: '/packages/new-punch/all/index'
      });
    }
  },
});
