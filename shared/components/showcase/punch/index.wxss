.theme-feature-punch {
  background: #fff;
  border-top: 1rpx solid #eee;
  border-bottom: 1rpx solid #eee;
}

.punch-header {
  display: flex;
  position: relative;
  padding: 0 15px;
  flex-direction: row;
  height: 45px;
  overflow-x: hidden;
  align-items: center;
  justify-content: space-between;
}

.punch-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 1rpx;
  background: #eee;
}

.punch-header__title {
  color: #333;
  font-weight: 700;
  font-size: 15px;
}

.punch-header__all {
  display: flex;
  justify-content: center;
  align-items: center;
}

.punch-header__all-text {
  font-size: 13px;
  color: #999;
}

.punch-header__van-icon {
  display: block !important;
  font-size: 10px !important;
  margin-left: 4px;
  color: #999;
}

.live-item__price {
  font-size: 12px;
  color: #f44;
}
