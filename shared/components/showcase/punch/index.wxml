<view>
  <view wx:if="{{ !list || !!list.length }}" class="theme-feature-punch">
    <view wx:if="{{ showTitle }}" class="punch-header">
      <text class="punch-header__title theme-feature-member__title-text">{{ title }}</text>
      <view wx:if="{{ showTitleAll }}" class="punch-header__all">
        <text class="punch-header__all-text" bindtap="onAllTap">全部</text>
        <van-icon name="arrow" custom-class="punch-header__van-icon" />
        <!-- <text class="zan-icon zan-icon-arrow punch-header__zan-icon"></text> -->
      </view>
    </view>
    <cap-punch-item
      wx:for="{{ list }}"
      wx:key="alias"
      theme-class="{{ themeClass }}"
      mode="card"
      punch="{{ item }}"
    />
  </view>
</view>
