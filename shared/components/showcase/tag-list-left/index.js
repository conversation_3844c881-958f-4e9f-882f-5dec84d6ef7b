import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import Args from '@youzan/weapp-utils/lib/args';
import omit from '@youzan/weapp-utils/lib/omit';
import Event from '@youzan/weapp-utils/lib/event';
import formatPrice from '@youzan/utils/money/formatPrice';

import { fetchGoodsByTagAlias } from './api';

import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import constants from '../captain-components/tag-list-left/constants';
import { setNavRange } from './utils';

const {
  INIT_GOODS_ITEM,
  MAX_DEFAULT_NUMBER,
  MAX_PAGE,
  PAGE_SIZE,
  PAGE_TIME,
  DEFAULT_PREVIEW_TAG
} = constants;

Component({
  behaviors: [componentBehavior, loggerBehavior],

  options: {
    addGlobalClass: true
  },

  data: {
    tags: [],
    /**
     * 商品配置数据
     */
    goodsListConfig: {}
  },

  attached() {
    this.initComponent();
  },

  methods: {
    /**
     * 下拉
     */
    onPullDownRefresh() {
      if (this.isComponentHide) return;
      if (!this.canPullDownRefresh) {
        this.canPullDownRefresh = true;
        return;
      }
      this.initComponent();
    },

    /**
     * 初始化组件
     */
    initComponent() {
      const { componentData } = this.data;
      const tagListData = mapKeysCase.toCamelCase(componentData);
      let { tags = [], goodsListConfig } = tagListData;
      tags =
        tags
          .map((item) => {
            const { number } = item;
            item.number = Math.min(number, MAX_DEFAULT_NUMBER);
            item.dataList = number > 0 ? new Array(item.number).fill(INIT_GOODS_ITEM) : [];
            item.loading = false;
            item.nodata = false;
            item.loaded = false;
            item.tabRange = [];
            item.navScrollTop = 0;
            item.goodsNumber = Math.min(number, MAX_PAGE);
            item.goodsMargin = goodsListConfig.goodsMargin;
            return item;
          })
          .map(setNavRange) || [];
      // 需要预览的tags 第一个展示 后面只展示 title
      const previewTagsList = {};
      const tagsList = {};
      tags.forEach((item, index) => {
        if (index < DEFAULT_PREVIEW_TAG) {
          previewTagsList[`tags[${index}]`] = item;
        } else {
          previewTagsList[`tags[${index}]`] = {
            title: item.title,
            isPreview: true
          };
          tagsList[`tags[${index}]`] = item;
        }
      });
      this.setData(
        {
          ...previewTagsList,
          goodsListConfig
        },
        () => {
          // 预览的数据塞完之后 在设置其余的tags
          this.setData(
            {
              ...tagsList
            },
            () => {
              Event.trigger('tags-list:success');
            }
          );
        }
      );
    },

    handleGoodsBuy(e) {
      const { alias = '' } = e.detail || {};
      if (!alias) return;

      this.triggerEvent('buy', e.detail);
    },

    openLoadAlias(e) {
      const { alias } = e.detail;
      this.fetchGoodsByAlias(alias);
    },

    fetchGoodsByAlias(alias) {
      const { tags, kdtId, appId, offlineId } = this.data;
      const navIndex = tags.findIndex((item) => item.alias == alias);
      if (navIndex < 0) return;
      const groupData = tags[navIndex];
      if (groupData.loading || groupData.nodata || !groupData.number || groupData.loaded) {
        return;
      }
      // 先设置loading
      this.setData({
        [`tags[${navIndex}].loading`]: true
      });
      fetchGoodsByTagAlias({
        alias,
        pageSize: groupData.goodsNumber,
        page: 1,
        kdt_id: kdtId,
        offlineId,
        app_id: appId,
        json: 1
      }).then(
        (res) => {
          this.setFetchGoods(navIndex, mapKeysCase.toSnakeCase(res.list) || []);
        },
        () => {
          this.setFetchGoods(navIndex, []);
        }
      );
    },

    /**
     * 分销员与导购员角色互斥，故只存在一种角色，返佣字段展示位置复用一处
     */
    setSalesManRebateInfo(item) {
      // 是否展示分销员返佣价格
      const showRebate = !!+item.salesman_rebate_price;
      // 是否展示导购员佣金价格
      const showGuideCommission = !!+item.guide_commission;
      item.showExtra =
        showRebate ||
        showGuideCommission ||
        (item.extension &&
          (item.extension.open_salesman_booth ||
            item.extension.open_guide_booth));
      item.extra = [];
      if (item.showExtra) {
        if (showRebate) {
          const rebatePrice = parseFloat(
            +item.salesman_rebate_price / 100
          ).toFixed(2);
          item.extra.push({
            text: `预计返${rebatePrice}元`,
          });
        } else if (showGuideCommission) {
          const guideCommissionPrice = formatPrice(
            Number(item.guide_commission)
          );
          item.extra.push({
            text: `预计赚${guideCommissionPrice}元`,
          });
        }
      }

      return item;
    },

    setMenberDiscount(item) {
      // 如果有标识了，就不加了
      if (
        item &&
        item.extra &&
        Array.isArray(item.extra) &&
        item.extra.length > 0 &&
        item.extra.find((x) => x.text && x.text.trim() !== '')
      ) {
        return item;
      }
      // 用于解决瀑布流炸掉的问题
      const showMenberDiscount = !!item.showRecommendedCard;
      const hasRecommendedCard = !!item.recommendedCard;
      if (showMenberDiscount) {
        item.showExtra = true;
      }

      if (
        item &&
        item.extra &&
        Array.isArray(item.extra) &&
        item.extra.length === 0 &&
        hasRecommendedCard
      ) {
        item.extra.push({
          text: item.recommendedCard,
          color: '#999999',
        });
      } else if (
        item &&
        item.extra &&
        Array.isArray(item.extra) &&
        item.extra.length > 0 &&
        item.extra[item.extra.length - 1] &&
        hasRecommendedCard
      ) {
        // 适配插了个空extra text的case
        item.extra[item.extra.length - 1].text = item.recommendedCard;
        item.extra[item.extra.length - 1].color = '#999999';
      } else {
        item.extra = [];
        if (hasRecommendedCard) {
          item.extra.push({
            text: item.recommendedCard,
            color: '#999999',
          });
        }
      }
      return item;
    },

    setFetchGoods(index, items) {
      const { tags } = this.data;
      const groupData = tags[index];
      const loggerList = [];
      let workProcessItemCount = 0;
      const newGoods = items.map((item, itemIndex) => {
        const bannerId = this.getBannerId(`${index + 1}.${itemIndex + 1}`);
        item.loggerParams = loggerParams;

        // 是否展示返佣价格
        item = this.setSalesManRebateInfo(item);
        // 会员再享受优惠展示
        item = this.setMenberDiscount(item);

        const loggerParams = {
          goods_id: item.id,
          item_id: item.id,
          item_type: 'goods',
          banner_id: bannerId,
          ...this.getComponentLoggerExtraParams()
        };
        loggerList.push(loggerParams);
        item.loggerParams = loggerParams;

        item = omit(item, [
          'id',
          'url',
          'buy_url',
          'image_id',
          'total_stock',
          'postage',
          'picture',
          'pre_sale_type',
          'height',
          'width'
        ]);

        item.url = Args.add('/pages/goods/detail/index', {
          alias: item.alias,
          banner_id: bannerId,
          ...this.getComponentLoggerExtraParams()
        });
        item.image_url = cdnImage(item.image_url, '!200x0.jpg');
        item.item_index = itemIndex;
        const { extension = {} } = item;
        if (!isEmpty(extension)) {
          item.isWorkProcess = true;
          const { retail_prepare: retailPrepare = {} } = extension;
          if (!isEmpty(retailPrepare)) {
            workProcessItemCount++;
          }
        }
        return item;
      });

      this.ensureAppLogger('view', loggerList);

      let reCalcTabRangeList = {};
      // 真实商品数和请求商品数不同 重置tag 高度
      if (items.length !== groupData.number || workProcessItemCount) {
        this.setData({
          [`tags[${index}].workProcessItemCount`]: workProcessItemCount
        });
        reCalcTabRangeList = this.reCalcTabRange(index, items.length);
      }
      // 先加载一页商品
      this.setData({
        [`tags[${index}].dataList`]: newGoods.slice(0, PAGE_SIZE),
        [`tags[${index}].loaded`]: true,
        [`tags[${index}].loading`]: false,
        [`tags[${index}].nodata`]: newGoods.length === 0,
        ...reCalcTabRangeList
      });
      // 剩余商品间隔一定时间加载 150ms 伪分类
      this.setPageTagList(index, newGoods.slice(PAGE_SIZE));
    },

    // 间隔一定时间渲染商品
    setPageTagList(index, list) {
      if (!list.length) return;
      let time = null;
      time = setTimeout(() => {
        clearTimeout(time);
        const { tags } = this.data;
        let isLoading = tags.some((item) => item.loading);
        // 有别的分组商品在加载中 就跳过这一次
        if (isLoading) this.setPageTagList(index, list);
        else {
          let pageTagGoods = {};
          let newGoods = list.slice(0, PAGE_SIZE);
          newGoods.forEach((item) => {
            pageTagGoods[`tags[${index}].dataList[${item.item_index}]`] = item;
          });
          this.setData(
            {
              ...pageTagGoods
            },
            () => {
              this.setPageTagList(index, list.slice(PAGE_SIZE));
            }
          );
        }
      }, PAGE_TIME);
    },

    reCalcTabRange(navIndex, num) {
      const { tags } = this.data;
      tags[navIndex].number = num;

      const tagsList = {};
      tags.map(setNavRange).forEach((item, index) => {
        tagsList[`tags[${index}].range`] = item.range;
        tagsList[`tags[${index}].tagHeight`] = item.tagHeight;
      });

      tagsList[`tags[${navIndex}].number`] = num;
      return tagsList;
    }
  }
});
