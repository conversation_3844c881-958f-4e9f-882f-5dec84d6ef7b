import constants from '../captain-components/tag-list-left/constants';

const {
  GOODS_HEIGHT,
  TITLE_HEIGHT,
  EMPTY_CONTAINER,
  WORK_PROCESS_HEIGHT
} = constants;

export function setNavRange(item = {}, index, arr) {
  let start = 0;
  const {
    goodsMargin = 0, workProcessItemCount = 0
  } = item;
  const itemHeight = +goodsMargin + GOODS_HEIGHT;
  let height = item.number ? item.number * itemHeight : EMPTY_CONTAINER;
  height += workProcessItemCount * WORK_PROCESS_HEIGHT;
  if (index) {
    start = arr[index - 1].range[1];
  }
  item.tagHeight = TITLE_HEIGHT + height;
  item.range = [start, start + TITLE_HEIGHT + height];
  return item;
}