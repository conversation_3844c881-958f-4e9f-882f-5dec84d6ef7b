<view class="showcase-ump-groupon">
  <cap-list
    id="cap-list"
    loading="{{ loading }}"
    finished="{{ finished }}"
    disabled="{{ itemData.skipFetch || disabled || (isSwipe && itemData.list && itemData.list.length > 0) }}"
    auto-check="{{ true }}"
    bind:load="handleLoad"
  >
    <cap-groupon
      id="cap-groupon"
      app-id="{{ appId }}"
      list="{{ itemData.list }}"
      layout="{{ itemData.layout }}"
      size-type="{{ itemData.sizeType }}"
      page-margin="{{ itemData.pageMargin }}"
      goods-margin="{{ itemData.goodsMargin }}"
      border-radius-type="{{ itemData.borderRadiusType }}"
      image-ratio="{{ itemData.imageRatio }}"
      image-fill-style="{{ itemData.imageFillStyle }}"
      text-style-type="{{ itemData.textStyleType }}"
      text-align-type="{{ itemData.textAlignType }}"
      show-title="{{ itemData.showTitle }}"
      show-sub-title="{{ itemData.showSubTitle }}"
      show-price="{{ itemData.showPrice }}"
      show-origin-price="{{ itemData.showOriginPrice }}"
      show-count-down="{{ itemData.showCountDown }}"
      show-groupon-num="{{ itemData.showGrouponNum }}"
      show-buy-button="{{ itemData.showBuyButton }}"
      buy-button-type="{{ itemData.buyButtonType }}"
      button-text="{{ itemData.buttonText }}"
      show-more-button="{{ finished && itemData.showMoreButton && itemData.list && itemData.list.length > 0 && !isSwipe }}"
      more-url="{{ itemData.moreUrl }}"
      redirect-type="{{ redirectType }}"
      extra-data="{{ extraData }}"
      page-data="{{ pageData }}"
      page-utils="{{ pageUtils }}"
      page-random-number="{{ pageRandomNumber }}"
      openSwipePagination="{{ itemData.list && itemData.list.length > 0 && (!finished || itemData.showMoreButton) }}"
      skeleton-number="{{ skeletonNumber }}"
      bind:buy="handleItemClick"
      bind:item-click="handleItemClick"
      bind:load-more="handleLoad"
    />
  </cap-list>
</view>