import { node as request } from 'shared/utils/request';

// 基础请求数据
const base = {
  from: 'weapp',
  v: '2'
};

// 自动获取
export const autoObtain = data => {
  return request({
    path: '/wscshop/ump/groupon/autoObtain.json',
    data: { ...base, ...data }
  });
};

// 手动获取
export const manualObtain = data => {
  return request({
    path: '/wscshop/ump/groupon/manualObtain.json',
    data: { ...base, ...data }
  });
};

// 抽奖拼团
export const getLuckyList = data => {
  return request({
    path: '/wscshop/ump/groupon/grouponLuckyList.json',
    data: { ...base, ...data }
  });
};

// 通用拼团接口。目前用于获取老带新拼团和阶梯拼团
export const getActivityList = data => {
  return request({
    path: '/wscshop/ump/groupon/activityList.json',
    data: { ...base, ...data },
  });
};
