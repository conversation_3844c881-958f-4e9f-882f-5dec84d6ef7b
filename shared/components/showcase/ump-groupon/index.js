import Args from '@youzan/weapp-utils/lib/args';
import get from '@youzan/weapp-utils/lib/get';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import pick from '@youzan/weapp-utils/lib/pick';
import navigate from '../../../utils/navigate';
import componentBehavior from '../behaviors/component-behavior';
import countdownUtilBehavior from '../captain-components/ump-goods-layout/behaviors/countdown-util-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import { getUpdateCountdownGoodsList } from '../captain-components/ump-goods-layout/helper';
import { ACTIVITY_STATUS } from '../captain-components/ump-goods-layout/constants';
import { autoObtain, manualObtain, getLuckyList, getActivityList } from './source';

const pageSize = 10; // 自动获取拼团的每页加载数量
const LAYOUT_SWIPE = 6;
const skeletonDefaultNumber = 2;

const END_BUTTON_TEXT = '去看看';

// 拼团类型
const GROUPON_TYPE = {
  NORMAL: 0, // 普通拼团
  LUCKY: 1, // 0元抽奖团
  OLD_NEW: 2, // 老带新拼团
  LADDER: 3 // 阶梯拼团
};

Component({
  behaviors: [componentBehavior, countdownUtilBehavior, loggerBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
      observer(data) {
        if (data.skipFetch) {
          this.setItemData(data);
        }
      },
    },

    redirectType: {
      type: Number,
      value: 3
    },

    pageRandomString: {
      type: String,
      value: '',
      observer: 'updateCountDown'
    }
  },

  data: {
    itemData: { list: [] }, // 配置数据
    loading: false, // 加载中
    canFinish: false, // 是否可以结束了
    finished: false, // 已结束
    page: 0, // 当前页，用于分页
    isSwipe: false, // 横向滑动
    skeletonNumber: 0,
  },

  attached() {
    this.setItemData(this.data.componentData);
  },

  methods: {
    onPullDownRefresh() {
      // 首次下拉，不再重新获取数据
      if (!this.isManualPullDown) {
        this.isManualPullDown = true;

        // featurejs 的下拉刷新会清掉原有倒计时，所以在这里需要重新触发倒计时
        this.updateCountDown(this.properties.pageRandomString);
        return;
      }

      this.handleLoad(true);
    },

    setItemData(data) {
      // 商品分组页（全部拼团页），不需经过 feature-adaptor 的转换
      if (data.skipFetch) {
        this.setData({
          itemData: mapKeysCase.toCamelCase(data)
        });
        return;
      }

      const itemData = mapKeysCase.toCamelCase(data);
      const query = pick(itemData, ['grouponType', 'showPrice', 'hideGoodsSold', 'isGrouponPro']);

      itemData.moreUrl = `/packages/shop/goods/group/index?pageType=groupon&component=${JSON.stringify(query)}`;
      this.setData({ itemData, isSwipe: itemData.layout === LAYOUT_SWIPE });
    },

    // 更新倒计时
    updateCountDown(randomStr) {
      const { itemData, loading } = this.data;

      if (loading) {
        return;
      }

      const needUpdate = getUpdateCountdownGoodsList('itemData.list', itemData.list, randomStr, (item, index, status) => {
        // 如果活动状态为已结束，并且购买按钮文字不是“去看看”
        if (status === ACTIVITY_STATUS.END && item.buttonText !== END_BUTTON_TEXT) {
          return { buttonText: END_BUTTON_TEXT };
        }
      });

      this.setData(needUpdate);
    },

    handleLoad(isNew) {
      const { loading } = this.data;

      if (loading) {
        return;
      }

      this.isPullDownRefresh = false;

      // 如果是下拉刷新
      if (typeof isNew === 'boolean' && isNew) {
        this.isPullDownRefresh = true;
        this.setData({
          canFinish: false,
          finished: false,
          page: 0
        });
      }

      this.obtain()
        .then(this.handleLoadSuccess.bind(this))
        .catch(this.handleLoadFail.bind(this));
    },

    obtain() {
      const {
        kdtId,
        itemData,
        loading,
        page,
      } = this.data;
      const {
        grouponType = '0',
        goodsSource,
        orderRule,
        hideGoodsSold: hideSoldOutGoods,
        activityIds = [],
        isGrouponPro = false,
      } = itemData;
      const ids = this.getIdsByPage(activityIds, page);
      const idsStr = ids.join(',');

      if (loading) {
        return Promise.reject();
      }

      // 通知 list 组件：开始加载
      this.setData({
        loading: true,
        skeletonNumber: +goodsSource === 0 || ids.length > skeletonDefaultNumber ? skeletonDefaultNumber : ids.length,
      });

      // 抽奖拼团↓↓↓
      if (+grouponType === GROUPON_TYPE.LUCKY) {
        return getLuckyList({
          kdtId,
          pageSize,
          page: page + 1,
          activityIds: idsStr
        });
      }

      if ([GROUPON_TYPE.OLD_NEW, GROUPON_TYPE.LADDER].indexOf(+grouponType) >= 0) {
        return getActivityList({
          kdtId,
          grouponType,
          hideSoldOutGoods,
          pageSize,
          page: page + 1,
          activityIds: idsStr,
        });
      }

      // 自动获取 ↓↓↓
      if (+goodsSource === 0) {
        return autoObtain({
          kdtId,
          orderRule,
          hideSoldOutGoods,
          pageSize,
          page: page + 1,
          ...isGrouponPro && { activityMode: 0 },
        });
      }

      // 手动获取 ↓↓↓
      return manualObtain({
        kdtId,
        activityIds: idsStr,
        hideSoldOutGoods
      });
    },

    // 加载成功
    handleLoadSuccess({ list: newList = [], count }) {
      const {
        itemData,
        page,
        canFinish,
      } = this.data;
      const {
        list = [],
        goodsSource,
        goodsNum,
        grouponType = 0,
        hideGoodsSold = 0,
      } = itemData;

      // 需要更新的数据
      const needUpdate = {
        page: page + 1,
        loading: false, // 通知 list 组件：本次加载已完成
        skeletonNumber: 0,
      };

      // 如果是加载第一页，则 oldList 设为空数组，否则用现有的 list
      let oldList = list;

      if (page === 0) {
        oldList = [];
        this.diffCount = 0;
      }

      newList = newList.filter(item => {
        // 过滤 isDisplay 为 0 的商品
        if (+get(item, 'goodsInfo.isDisplay', 1) === 0) {
          this.diffCount++;
          return false;
        }

        // 老带新拼团、阶梯拼团过滤售罄和隐藏分组的商品
        if ([GROUPON_TYPE.OLD_NEW, GROUPON_TYPE.LADDER].indexOf(+grouponType) >= 0) {
          if (+hideGoodsSold && +item.isSoldout) {
            this.diffCount++;
            return false;
          }

          if (+goodsSource === 0 && get(item, 'goodsInfo.inHideGroup')) {
            this.diffCount++;
            return false;
          }
        }

        return true;
      });

      // 转换数据
      newList = this.format(newList, oldList.length);

      // 自动获取
      if (+goodsSource === 0) {
        // slice 是由于需要将数量控制在预设的值，而分页获取到的数据量有可能比预设的值大
        newList = oldList.concat(newList).slice(0, +goodsNum);

        // 通知 list 组件：已全部加载完成，
        // 后端返回的count与总数有时候对不上, 实际数量可能会比count少，后端暂时无法修改，前端通过(page + 1) * pageSize >= +count 进行兜底
        if (newList.length >= Math.min(+goodsNum, +count - this.diffCount) || (page + 1) * pageSize >= +count ) {
          needUpdate.finished = true;
        }
      } else {
        // 手动获取
        newList = oldList.concat(newList);

        // 通知 list 组件：已全部加载完成
        if (canFinish) {
          needUpdate.finished = true;
        }
      }

      // 如果是第一页，直接赋值
      if (page === 0) {
        needUpdate['itemData.list'] = newList;
      } else {
        // 只设置需要的数据
        for (let i = oldList.length; i < newList.length; i++) {
          needUpdate[`itemData.list[${i}]`] = newList[i];
        }
      }

      this.setData(needUpdate, () => {
        this.updateSwipeStatus();
      });
    },

    // 加载失败
    handleLoadFail(err) {
      console.log(err || '拼团活动获取失败');

      this.setData({ loading: false, finished: true, skeletonNumber: 0 }, () => {
        this.updateSwipeStatus();
      });
    },

    // 点击商品，跳转详情页
    handleItemClick({ detail: { url } }) {
      navigate.navigate({ url });
    },

    // 设置横向滑动的状态（用于横向分页加载）
    updateSwipeStatus() {
      const { isSwipe, loading, finished } = this.data;

      if (!isSwipe) {
        return;
      }

      this.selectComponent('#cap-groupon').setLoadStatus({
        loading,
        swipeFinished: finished,
        isPullDownRefresh: this.isPullDownRefresh,
      });
    },

    // 把 activityIds 分页
    getIdsByPage(ids, page) {
      const fromIndex = page * pageSize;
      const toIndex = fromIndex + pageSize;

      // 可以结束了
      if (toIndex >= ids.length) {
        this.setData({
          canFinish: true
        });
      }

      return ids.slice(fromIndex, toIndex);
    },

    // 格式化接口列表数据
    format(list = [], fromIndex = 0) {
      const {
        grouponType, goodsSource, showGrouponNum, goodsNum
      } = this.data.itemData;

      const loggerList = [];
      const formatList = list.map(item => {
        const {
          alias, id, extraTagText, goodsId, goodsInfo = {}
        } = item;
        let url;
        const bannerId = this.getBannerId(++fromIndex);
        const loggerParams = {
          banner_id: bannerId,
          item_id: goodsId || goodsInfo.id,
          item_type: 'goods',
          goods_id: goodsId,
          act_type: 4,
          act_id: id + '',
          component: this.getComponentLoggerType(),
          ...this.getComponentLoggerExtraParams()
        };
        // 抽奖拼团
        if (+grouponType === GROUPON_TYPE.LUCKY) {
          url = Args.add('/packages/goods/lucky-draw-group/index', {
            alias,
            type: 'luckyDrawGroup',
            activityId: id,
            banner_id: bannerId,
            ...this.getComponentLoggerExtraParams()
          });
        } else {
          url = Args.add('/packages/goods/groupon/index', { alias, banner_id: bannerId, ...this.getComponentLoggerExtraParams() });
        }

        // 手动添加，或者自动添加但未超出预设数量
        if (+goodsSource === 1 || fromIndex <= goodsNum) {
          loggerList.push(loggerParams);
        }

        item.loggerParams = loggerParams;
        item.url = url;
        item.extraTagText = showGrouponNum ? extraTagText : ''; // 如果不显示成团人数，则把 tagText 置为空串
        return item;
      });

      this.ensureAppLogger('view', loggerList);

      return formatList;
    }
  }
});
