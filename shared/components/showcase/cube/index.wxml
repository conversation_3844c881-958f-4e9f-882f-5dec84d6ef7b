<view
  class="feature-cube-container"
  style="height: {{ height }}px; margin: -{{ borderWidth / 2 }}px;"
>
  <block
    wx:for="{{ items }}"
    wx:for-item="item"
    wx:for-index="itemIndex"
    wx:key="itemIndex"
  >
    <cap-navigator
      link-type="{{ item.linkType }}"
      app-id="{{ item.appId }}"
      path="{{ item.path }}"
      use-short-link="{{ item.useShortLink }}"
      short-link="{{ item.shortLink }}"
      data-index="{{ itemIndex }}"
	  data-imageUrl="{{ item.imageUrl }}"
      custom-style="width: {{ item.showWidth }}px; height: {{ item.showHeight }}px;"
      style="position: absolute; width: {{ item.showWidth }}px; height: {{ item.showHeight }}px; left: {{ item.showLeft }}px; top:{{ item.showTop }}px; margin:{{ borderWidth / 2 }}px;"
      im="{{ extraData }}"
      bind:navigate="handleNavigate"
      bind:contactback="handleContactBack"
    >
      <image
        src="{{ item.imageUrl }}"
        mode="aspectFill"
        lazy-load="true"
        class="feature-cube-image"
        style="postion:absolute; left: 0; top: 0; width: {{ item.showWidth }}px; height: {{ item.showHeight }}px;"
      />
    </cap-navigator>
  </block>
</view>
