import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import getSystemInfo from 'shared/utils/browser/system-info';
import componentBehavior from '../behaviors/component-behavior';
import loggerBehavior from '../behaviors/logger-behavior';
import navigationToBehavior from '../behaviors/navigation-to-behavior';
import { getLink, isValidLink } from '../utils/linkAdaptor';
import { SHELF_LINK_TYPE_ENUM } from '../utils/config';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
import jumpVideoNumber from '../utils/jumpVideoNumber';

Component({
  behaviors: [navigationToBehavior, componentBehavior, loggerBehavior],

  attached() {
    this.initComponentData();
  },

  methods: {
    initComponentData() {
      const { componentData = {} } = this.data;
      const cubeData = mapKeysCase.toCamelCase(componentData);
      const {
        borderWidth,
        width,
        height,
        items = [],
        isCustom,
        pageMargin = 0,
      } = cubeData;

      // 产品要的逻辑：只有所有选项都不支持的时候才隐藏掉
      if (items.length === 0 || this.isAllLinkInvalid(items)) {
        return;
      }

      const systemInfo = getSystemInfo();
      const { windowWidth = 375 } = systemInfo;

      // 屏幕可显示的宽度 + 边宽 - 页面间距
      const showWidthTotal = windowWidth + borderWidth - pageMargin * 2;
      // 魔方单元格的宽度
      const perWidth = Math.ceil(showWidthTotal / width);
      // 魔方单元格的高度
      let perHeight = perWidth;

      // 当魔方是nx1时，单元格的高度为第一张图片等比缩放后的高度
      if (isCustom && height === 1) {
        perHeight = this.getPerHeight(items, perWidth);
      }

      const first = items[0] || {};

      const wrapHeight =
        isCustom && first.imageWidth && first.imageHeight
          ? (first.imageHeight / first.imageWidth) * (perWidth - borderWidth) +
            borderWidth
          : perWidth * height;

      this.setData({
        ...cubeData,
        height: wrapHeight,
        width: showWidthTotal,
        items: this.getFormatedItems(
          items,
          perWidth,
          perHeight,
          borderWidth,
          pageMargin,
          isCustom,
          wrapHeight
        ),
      });
    },

    isAllLinkInvalid(items) {
      const selfLink = Object.values(SHELF_LINK_TYPE_ENUM);
      return items.every((item) => !isValidLink(item, selfLink));
    },

    getPerHeight(items, perWidth) {
      const [firstImg] = items;
      if (firstImg) {
        const rate = (perWidth * firstImg.width) / firstImg.imageWidth;
        return Math.ceil(firstImg.imageHeight * rate);
      }

      return perWidth;
    },

    // 计算每个图片的宽高 偏移位置, 链接，图片url
    getFormatedItems(
      items,
      perWidth,
      perHeight,
      borderWidth,
      pageMargin,
      isCustom,
      wrapHeight
    ) {
      return items.map((item) => {
        const { imageUrl = '' } = item;
        const height = perWidth * item.height - borderWidth;

        return {
          ...item,
          ...getLink(item),
          imageUrl: cdnImage(imageUrl, '!730x0.jpg'),
          showWidth: perWidth * item.width - borderWidth,
          showHeight: isCustom ? wrapHeight - borderWidth : height,
          showLeft: pageMargin + perWidth * item.x,
          showTop: perHeight * item.y,
        };
      });
    },

    handleNavigate(event) {
      const { index = 0, imageurl } = event.currentTarget.dataset;
      const { items = [], componentData = {} } = this.data;
      const item = items[index];
      const cloudLoggerInfo = getLoggerExtraParams(
        componentData.type,
        this.getCloudLoggerInfo({ imageUrl: imageurl })
      );

      const { linkType, linkId } = item;
      if (linkType === 'video_number' && jumpVideoNumber(linkId)) return;
      const extra = { banner_id: this.getBannerId(index + 1) };

      // logger
      const loggerParams = {
        ...extra,
        ...cloudLoggerInfo,
      };

      // loggrt
      this.ensureAppLogger('click_content', loggerParams);

      this.navigateIncludeShelf(mapKeysCase.toSnakeCase(item), extra);
    },

    handleContactBack({ detail = {} }) {
      this.triggerEvent('contactback', detail);
    },
  },
});
