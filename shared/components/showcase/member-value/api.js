import { node as request } from 'shared/utils/request';

export const getGiftCardList = (data) => {
  return request({
    path: '/wscshop/ump/membervalue/giftcard/list.json',
    data
  })
    .then((data) => {
      return data;
    })
    .catch(() => {
      return [];
    });
};

export const getRechargeRuleList = (data) => {
  return request({
    path: '/wscshop/ump/membervalue/rechargerule/list.json',
    data
  })
    .then((data) => {
      return data;
    })
    .catch(() => {
      return [];
    });
};
