Component({
  data: {
    giftCardList: [],
    memberValueType: '0',
    rechargeRules: [],
    ruleCardColor: 1
  },

  properties: {
    kdtId: {
      type: String
    },
    componentData: {
      type: Object,
      value: {}
    }
  },

  attached() {
    const {
      gift_card_list: giftCardList = [],
      member_value_type: memberValueType = '0',
      recharge_rule: rechargeRules = [],
      rule_color: ruleCardColor = 1
    } = this.data.componentData || {};
    this.setData({
      giftCardList,
      memberValueType,
      rechargeRules,
      ruleCardColor
    });
  },

  methods: {}
});
