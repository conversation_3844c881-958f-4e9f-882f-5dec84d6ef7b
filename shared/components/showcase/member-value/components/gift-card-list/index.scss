.member-value-gift-card-list {
  display: flex;
  margin: 0 16px;
  overflow-y: scroll;
  padding: 12px 0;

  &::-webkit-scrollbar {
    width: 0 !important;
    display: none;
  }

  &_item {
    &_name {
      height: 20px;
      line-height: 20px;
      font-size: 14px;
      color: #323233;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &_price {
      height: 25px;
      line-height: 25px;
      font-size: 18px;
      font-weight: 500;
      color: #f44;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &::before {
        content: '¥';
        height: 18px;
        line-height: 18px;
        font-size: 13px;
        font-weight: 500;
        color: #f44;
      }
    }
    &-mode1 {
      width: 100%;
      .member-value-gift-card-list_mode {
        &_item {
          width: 100%;
          position: relative;
          &_tip {
            position: absolute;
            width: 52px;
            height: 22px;
            line-height: 22px;
            border-radius: 11px;
            background-color: rgba(0, 0, 0, 0.5);
            font-size: 12px;
            text-align: center;
            color: #fff;
            left: calc(50% - 26px);
            top: calc(38%);
          }
        }
        &_img-container {
          position: relative;
          width: 91.46vw;
          height: 52.3vw;
          margin-bottom: 10px;
          border-radius: 8.6px;
          overflow: hidden;
        }
        &_img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          &-disable {
            opacity: 0.5;
          }
        }
      }
    }
    &-mode2 {
      flex: 1;
      padding-right: 9px;
      &:first-child {
        padding-left: 0;
      }
      &:last-child {
        padding-right: 0;
      }

      .member-value-gift-card-list_mode {
        &_item {
          width: 100%;
          position: relative;
          &_tip {
            position: absolute;
            width: 52px;
            height: 22px;
            line-height: 22px;
            border-radius: 11px;
            background-color: rgba(0, 0, 0, 0.5);
            font-size: 12px;
            text-align: center;
            color: #fff;
            left: 61.5px;
            top: 39px;
          }
        }
        &_img-container {
          position: relative;
          width: 100%;
          height: 26.2vw;
          margin-bottom: 10px;
          border-radius: 7px;
          overflow: hidden;
        }
        &_img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          &-disable {
            opacity: 0.5;
          }
        }
      }
    }
    &-mode3 {
      padding-right: 9px;
      &:first-child {
        padding-left: 0;
      }
      &:last-child {
        padding-right: 0;
      }
      .member-value-gift-card-list_mode {
        &_item {
          width: 136px;
          position: relative;
          display: block;
          &_tip {
            position: absolute;
            width: 52px;
            height: 22px;
            line-height: 22px;
            border-radius: 11px;
            background-color: rgba(0, 0, 0, 0.5);
            font-size: 12px;
            text-align: center;
            color: #fff;
            left: 42px;
            top: 28px;
          }
        }
        &_img-container {
          position: relative;
          width: 100%;
          height: 18vw;
          margin-bottom: 10px;
          border-radius: 5px;
          overflow: hidden;
        }
        &_img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          &-disable {
            opacity: 0.5;
          }
        }
      }
    }
  }
}
