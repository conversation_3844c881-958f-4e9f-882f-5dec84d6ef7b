<view class="member-value-gift-card-list">
  <view
    wx:for="{{ cardInfoList }}"
    wx:for-item="item"
    wx:for-index="index"
    wx:key="index"
    class="{{ className }}"
  >
    <view class="member-value-gift-card-list_mode">
      <view wx:if="{{ item.status === 1 }}" href="{{ link }}" class="member-value-gift-card-list_mode_item" data-item="{{ item }}" bind:tap="redirectToBuyCardCenter">
        <view class='member-value-gift-card-list_mode_img-container'>
          <image src="{{ item.productImage }}" class="member-value-gift-card-list_mode_img" mode="aspectFit"/>
        </view>
        <view class="member-value-gift-card-list_item_name">
          {{ item.productName }}
        </view>
        <view class="member-value-gift-card-list_item_price">
          {{ item.formatedAmount }}
        </view>
      </view>
      <view wx:if="{{ item.status === 0 }}" class="member-value-gift-card-list_mode_item">
        <view class='member-value-gift-card-list_mode_img-container'>
          <image src="{{ item.productImage }}" class="member-value-gift-card-list_mode_img member-value-gift-card-list_mode_img-disable" mode="aspectFit"/>
        </view>
        <view class="member-value-gift-card-list_item_name">
          {{ item.productName }}
        </view>
        <view class="member-value-gift-card-list_item_price">
          {{ item.formatedAmount }}
        </view>
        <view class="member-value-gift-card-list_mode_item_tip">
          已售罄
        </view>
      </view>
      <view wx:if="{{ item.status === -1 }}" class="member-value-gift-card-list_mode_item">
        <view class='member-value-gift-card-list_mode_img-container'>
          <image src="{{ item.productImage }}" class="member-value-gift-card-list_mode_img member-value-gift-card-list_mode_img-disable" mode="aspectFit"/>
        </view>
        <view class="member-value-gift-card-list_item_name">
          {{ item.productName }}
        </view>
        <view class="member-value-gift-card-list_item_price">
          {{ item.formatedAmount }}
        </view>
        <view class="member-value-gift-card-list_mode_item_tip">
          已停售
        </view>
      </view>
    </view>
  </view>
</view>