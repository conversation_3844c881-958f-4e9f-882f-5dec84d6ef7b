import * as api from '../../api';
import navigate from 'shared/utils/navigate';
import get from '@youzan/weapp-utils/lib/get';

function classNameMode(cardInfoList) {
  const className = 'member-value-gift-card-list_item';
  if (cardInfoList.length <= 1) {
    return `${className} ${className + '-mode1'}`;
  }
  if (cardInfoList.length === 2) {
    return `${className} ${className + '-mode2'}`;
  }
  if (cardInfoList.length >= 3) {
    return `${className} ${className + '-mode3'}`;
  }
}

function formatPrice(price, decimalLength = 2) {
  return (Math.abs(price) / 100).toFixed(decimalLength);
}

const GIFT_CARD_STATUS = {
  invalid: -3, // 已失效
  delete: -2, // 已删除
  stop: -1, // 停售
  out: 0, // 没有库存
  normal: 1, // 正常
};

Component({
  data: {
    cardInfoList: [],
    className: '',
  },

  properties: {
    list: {
      type: Array,
      value: [],
    },
    kdtId: {
      type: String,
    },
  },

  attached() {
    this.fetchGiftCardList(this.data.list);
  },

  methods: {
    async fetchGiftCardList(list) {
      if (list.length === 0) {
        this.setData({
          cardInfoList: [],
        });
        return;
      }
      try {
        const cardInfoList = await api.getGiftCardList({
          productNos: list.map((item) => item.productNo).join(','),
        });

        // 过滤已删除、已失效状态的卡
        const cardList = cardInfoList
          .map((card) => {
            if (
              card.status === GIFT_CARD_STATUS.invalid ||
              card.status === GIFT_CARD_STATUS.delete
            ) {
              return;
            }
            return {
              ...card,
              formatedAmount: formatPrice(card.amount || 0, 0),
            };
          })
          .filter((item) => !!item);

        const className = classNameMode(cardList);

        this.setData({
          cardInfoList: cardList,
          className,
        });
      } catch (error) {
        console.error(error);
      }
    },
    redirectToBuyCardCenter(event) {
      const templateNo = get(
        event,
        'currentTarget.dataset.item.templateNo',
        ''
      );
      const linkUrl = `https://cashier.youzan.com/pay/prepaid_card_order?kdt_id=${this.data.kdtId}&template_no=${templateNo}`;
      const url = `/pages/common/webview-page/index?src=${encodeURIComponent(
        linkUrl + '&entry=11'
      )}`;
      navigate.navigate({
        url,
      });
    },
  },
});
