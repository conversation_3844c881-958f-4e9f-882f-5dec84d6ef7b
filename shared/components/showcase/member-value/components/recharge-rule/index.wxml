<view class="member-value-recharge-rule">
  <view
      wx:for="{{ rechargeRuleList }}"
      wx:for-item="item"
      wx:key="productNos"
      class="{{ className }}"
    >
      <view
        class="member-value-recharge-rule_item_card"
        data-item="{{ item }}"
        bind:tap="clickRechageRule"
      >
        <view class="member-value-recharge-rule_item_content">
          <span class="member-value-recharge-rule_item_name">
            充<span>{{ item.formatAmount }}元</span>
          </span>
          <span class="member-value-recharge-rule_item_slogan">
            <span>{{ item.slogan || item.giftPackContext }}</span>
          </span>
        </view>
        <span wx:if="{{ isMode1 }}" class="recharge-btn">
          立即充值
        </span>
      </view>
    </view>
</view>