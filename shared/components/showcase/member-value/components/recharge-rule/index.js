import navigate from 'shared/utils/navigate';
import get from '@youzan/weapp-utils/lib/get';
import * as api from '../../api';

const colorMap = ['', 'red', 'yellow', 'white', 'black', 'green'];

function classNameMode(ruleCardColor, len) {
  const className = 'member-value-recharge-rule_item';
  const colorMode = colorMap[ruleCardColor];

  if (len <= 1) {
    return `${className} ${className + '-mode1'} ${className}_${colorMode}`;
  }
  if (len === 2) {
    return `${className} ${className + '-mode2'} ${className}_${colorMode}`;
  }
  if (len >= 3) {
    return `${className} ${className + '-mode3'} ${className}_${colorMode}`;
  }
}

function formatPrice(price, decimalLength = 2) {
  return (Math.abs(price) / 100).toFixed(decimalLength);
}

Component({
  data: {
    rechargeRuleList: [],
    isMode1: false,
    listEmpty: false,
    className: ''
  },

  properties: {
    rechargeRules: {
      type: Array,
      value: []
    },
    kdtId: {
      type: String
    },
    ruleCardColor: {
      type: Number,
      value: 1
    }
  },

  attached() {
    this.fetchRuleList();
  },

  methods: {
    async fetchRuleList(newList) {
      const rules = newList || this.data.rechargeRules;
      if (!rules || rules.length === 0) {
        this.setData({
          rechargeRuleList: [],
          listEmpty: true,
          isMode1: false
        });
        return;
      }
      this.setData({
        listEmpty: false
      });

      const rechargeRule = await api.getRechargeRuleList({
        productNos: rules.map((item) => item.productNos).join(',')
      });
      // set slogan
      rechargeRule.forEach((item) => {
        item.productNos = `${item.productNo}-${item.giftPackId}`;
        item.formatAmount = formatPrice(item.amount);
        rules.every((rule) => {
          if (item.productNos === rule.productNos && rule.slogan) {
            item.slogan = rule.slogan;
            return false;
          }
          return true;
        });
      });
      this.setData({
        rechargeRuleList: rechargeRule.filter((item) => item.status === 'NORM'),
        listEmpty: true,
        className: classNameMode(this.data.ruleCardColor, rechargeRule.length),
        isMode1: rechargeRule.length === 1
      });
    },
    clickRechageRule(event) {
      const productNo = get(event, 'currentTarget.dataset.item.productNo', '');
      const giftPackId = get(event, 'currentTarget.dataset.item.giftPackId', '');
      const linkUrl = `https://cashier.youzan.com/pay/prepaid_balance_recharge?kdt_id=${this.data.kdtId}&product_no=${productNo}&giftpack_id=${giftPackId}`;
      const url = `/pages/common/webview-page/index?src=${encodeURIComponent(
        linkUrl + '&entry=10'
      )}`;
      navigate.navigate({
        url
      });
    }
  }
});
