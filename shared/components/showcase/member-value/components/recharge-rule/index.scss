.member-value-recharge-rule {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 0 auto;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    width: 0 !important;
    display: none;
  }

  &_item {
    position: relative;

    &_card {
      padding: 10px;
      border-radius: 4px;
    }

    &_disable {
      opacity: 0.5;
    }

    &_status {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);

      span {
        display: inline-block;
        background: #000;
        border-radius: 11px;
        font-size: 12px;
        color: #fff;
        text-align: center;
        width: 52px;
        height: 22px;
        line-height: 22px;
      }
    }

    &_name,
    &_slogan {
      display: block;
      font-size: 16px;
      height: 22px;
      line-height: 22px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      span {
        font-weight: bold;
      }
    }

    &-mode1 {
      width: 100%;
      .member-value-recharge-rule_item_content {
        display: inline-block;
        width: calc(100% - 120px);
      }
      .recharge-btn {
        display: inline-block;
        vertical-align: top;
        margin-top: 4px;
      }
    }
    &-mode2 {
      flex: 1;
      min-width: 0;

      &:first-child {
        margin-right: 5px;
      }
      &:last-child {
        margin-left: 5px;
      }
    }
    &-mode3 {
      margin-right: 10px;
      .member-value-recharge-rule_item_card {
        width: 136px;
      }
      &:last-child {
        padding-right: 16px;
      }
    }
    &_red {
      .member-value-recharge-rule_item_card {
        background-image: linear-gradient(0deg, #f58357 0%, #e74c2c 100%);
      }
      .member-value-recharge-rule_item_name,
      .member-value-recharge-rule_item_slogan {
        color: #fff;
      }
      .recharge-btn {
        background-image: linear-gradient(0deg, #fff8d9 0%, #ffeeb1 100%);
        color: #4a2214;
      }
    }
    &_yellow {
      .member-value-recharge-rule_item_card {
        background-image: linear-gradient(0deg, #fff8d9 0%, #fffeb1 100%);
      }
      .member-value-recharge-rule_item_name,
      .member-value-recharge-rule_item_slogan {
        color: #4a2214;
      }
      .recharge-btn {
        background-image: linear-gradient(0deg, #4a2214 0%, #6d3a27 100%);
        color: #fff;
      }
    }
    &_white {
      .member-value-recharge-rule_item_card {
        background-image: linear-gradient(0deg, #f7f7f7 0%, #f7f7f7 100%);
      }
      .member-value-recharge-rule_item_name,
      .member-value-recharge-rule_item_slogan {
        color: #f44;
      }
      .recharge-btn {
        background-image: linear-gradient(0deg, #f58357 0%, #e74c2c 100%);
        color: #fff;
      }
    }
    &_black {
      .member-value-recharge-rule_item_card {
        background-image: linear-gradient(179deg, #292929 0%, #626262 100%);
      }
      .member-value-recharge-rule_item_name,
      .member-value-recharge-rule_item_slogan {
        color: #fff;
      }
      .recharge-btn {
        background-image: linear-gradient(0deg, #fff 0%, #e6e6e6 100%);
        color: #323233;
      }
    }
    &_green {
      .member-value-recharge-rule_item_card {
        background-image: linear-gradient(0deg, #f0faf2 0%, #dcede0 100%);
      }
      .member-value-recharge-rule_item_name,
      .member-value-recharge-rule_item_slogan {
        color: #2da641;
      }
      .recharge-btn {
        background-image: linear-gradient(180deg, #2da641 0%, #75be83 100%);
        color: #fff;
      }
    }
  }
}

.recharge-btn {
  border-radius: 18px;
  width: 96px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  margin-left: 12px;
  font-weight: bold;
}
