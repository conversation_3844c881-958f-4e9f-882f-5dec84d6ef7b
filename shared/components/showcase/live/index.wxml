<view>
  <view wx:if="{{ !list || !!list.length }}" class="theme-feature-live">
    <view wx:if="{{ showTitle }}" class="live-title">
      <text class="theme-feature-live__title-text">{{ title }}</text>
      <view wx:if="{{ showTitleAll }}" class="theme-feature-live__view-all">
        <text class="theme-feature-live__all-text" bindtap="goToAllLive">全部</text>
        <!-- <text class="zan-icon zan-icon-arrow theme-feature-live__zan-icon"></text> -->
        <van-icon name="arrow" custom-class="theme-feature-live__van-icon" />
      </view>
    </view>
    <block wx:for="{{ list }}" wx:key="id">
      <cap-live-item
        live-item="{{ item}}"
        show-price-info="{{ item.showPriceInfo }}">
      </cap-live-item>
    </block>
  </view>
</view>
