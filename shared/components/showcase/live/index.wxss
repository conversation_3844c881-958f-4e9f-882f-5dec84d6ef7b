.theme-feature-live {
  background: #fff;
  border-top: 1rpx solid #eee;
  border-bottom: 1rpx solid #eee;
}

.live-title {
  display: flex;
  position: relative;
  padding: 0 15px;
  flex-direction: row;
  height: 45px;
  overflow-x: hidden;
  align-items: center;
  justify-content: space-between;
}

.live-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 1rpx;
  background: #eee;
}

.theme-feature-live__all-text {
  font-size: 13px;
  color: #999;
}

.theme-feature-live__view-all {
  display: flex;
  justify-content: center;
  align-items: center;
}

.theme-feature-live__van-icon {
  display: block !important;
  font-size: 10px !important;
  margin-left: 4px;
  color: #999;
}

.theme-feature-live__title-text {
  color: #333;
  font-weight: 700;
  font-size: 15px;
}

.live-item__price {
  font-size: 12px;
  color: #f44;
}

.live-item__price-tag {
  display: inline-block;
  font-size: 8px;
  color: #f44;
  background-color: rgba(255, 68, 68, 0.1);
  border-radius: 2px;
  padding: 2px 4px;
}

.live-item__starttime {
  margin-left: 5px;
}

.live-item__buytext {
  font-size: 12px;
  color: #999;
}

.live-item__freetext {
  font-size: 12px;
  color: #f44;
}
