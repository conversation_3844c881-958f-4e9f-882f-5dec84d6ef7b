import { node as request } from 'shared/utils/request';
import componentBehavior from '../behaviors/component-behavior';
import { shopCanPayForKnowledge } from '../utils/pctUtils';

Component({
  options: {
    addGlobalClass: true
  },

  type: 'live',

  behaviors: [componentBehavior],

  properties: {
    componentData: {
      type: Object,
      value: {},
    },
    kdtId: {
      type: String
    }
  },

  data: {
    itemType: 'content',
    title: '',
    showTitle: false,
    showTitleAll: false,
    list: [],
    slots: [],
    statusText: [],
    statusColor: []
  },

  attached() {
    const itemData = this.data.componentData;
    this.setData({
      itemType: 'live',
      title: itemData.title || itemData.name || '',
      showTitle: parseInt(itemData.show_title, 10) === 1,
      showTitleAll: parseInt(itemData.show_title_all, 10) === 1,
      list: [],
      slots: {
        status: true,
        label: true
      },
      statusText: [
        '已删除',
        '未开始',
        '直播中',
        '回看'
      ],
      statusColor: [
        '',
        'primary',
        'danger',
        ''
      ]
    });
    this.getLiveList(itemData);
  },

  methods: {
    showPriceInfo() {
      return shopCanPayForKnowledge();
    },

    getLiveList(itemData) {
      // 字段应该改为live_from
      if (itemData.content_from === 'custom') {
        itemData.ids = itemData.sub_entry.map(item => item.id).join(',');
      }

      request({
        path: '/wscshop/showcase/knowledge/livelist',
        data: {
          source: itemData.content_from === 'custom' ? 1 : 0,
          ids: itemData.ids ? itemData.ids : '',
          kdt_id: this.data.kdtId,
          pageSize: 6,
          liveQueryScene: 1
        }
      }).then((result) => {
        this.setData({ list: this.parseLiveData(result) });
      });
    },

    parseLiveData(itemData) {
      return itemData.filter((item) => item != null).map((item) => {
        item.showPriceInfo = this.showPriceInfo();
        return {
          ...item
        };
      });
    },

    goToAllLive() {
      wx.navigateTo({
        url: '/packages/paidcontent/list/index?type=live'
      });
    }
  },
});
