// import Tee from '@youzan/tee';
// import Toast from '@vant/weapp/dist/toast/toast';
// import request from '@youzan/tee-biz-request';
// import { navigateToNextPage } from '@youzan/tee-biz-prefetch';
// import Args from '@youzan/utils/url/args';
// import Event from '@youzan/weapp-utils/lib/event';
// import { nativeSwitchTab } from '@youzan/wsc-tab-bar-utils/lib/weapp/navigate';
// import args from '@youzan/weapp-utils/lib/args';
// // import { getPlugins } from '@youzan/ranta-helper-tee';
// import {
//   SHELF_LINK_TYPE_ENUM,
//   SHELF_LINK_URL_MAP,
//   SHELF_LINK_TYPE_TO_MODE,
//   SHELF_PAGE_PATH,
// } from 'shared/components/showcase/utils/config';
// import { Hummer } from 'shared/utils/hummer';

// const app = Tee.getApp();
// // const { dmc } = getPlugins();

// function getConfigs(data) {
//   return request({
//     path: 'retail/h5/miniprogram/shelf-config/getFirstLevelConfigs',
//     method: 'POST',
//     data: {
//       ...data,
//       useSwitch: 'v2',
//       supportUnavailableGoods: 2,
//     },
//   });
// }

// function prefetch(linkUrl) {
//   // console.clear();
//   // console.time('init');
//   const kdtId = app.getKdtId();
//   navigateToNextPage({
//     navigatePath: 'retail-shelf',
//     navigateCb: (prefetchKey) => {
//       setTimeout(() => {
//         Hummer.mark.start(
//           `retail_shelf_init_${app.globalData.shelfParams.mode}`
//         );
//         Hummer.mark.start('retail_shelf_config');
//         const url = `/${linkUrl}&prefetchKey=${prefetchKey}&prefetchKdtId=${kdtId}`;
//         // eslint-disable-next-line no-undef
//         if (LS_COMPILE_MODE === 'kq') {
//           const { tabbarOriginList } = getApp().globalData;
//           const query = args.getAll(url);
//           Event.trigger('retail-shelf:switch-tab', query);
//           nativeSwitchTab(tabbarOriginList, {
//             path: url.split('?')[0],
//             query,
//             fail: () => {
//               Tee.$native.navigateTo({ url });
//             },
//           });
//           return;
//         }
//         Tee.$native.navigateTo({ url });
//       }, 150);
//     },
//     prefetchCb: () => {
//       const { mode, filter } = args.getAll(linkUrl) || {};
//       // 全部商品才走预请求
//       if (mode === 0 && !filter) {
//         return getConfigs({
//           mode,
//         });
//       }
//       return Promise.reject();
//     },
//   });
// }

// const navigateToShelfOrderPage = (linkUrl, context) => {
//   Tee.$native.getSetting({
//     success: (res) => {
//       const isAuthLocation = res.authSetting['scope.userLocation'];
//       if (isAuthLocation === undefined || isAuthLocation === true) {
//         // 第一次请求授权 或者 已经授权过
//         Tee.$native.getLocation({
//           type: 'gcj02',
//           fail: () => {
//             // 引导授权
//             if (context?.handleShowAuthDialog) context.handleShowAuthDialog();
//           },
//           success: () => {
//             prefetch(linkUrl);
//           },
//         });
//       } else if (isAuthLocation === false) {
//         // 已经拒绝过授权，则直接让进
//         prefetch(linkUrl);
//       }
//     },
//   });
// };

// /**
//  * 获取点单页 Mode
//  *
//  * @param {*} isGoodsItem
//  * @param {*} groupDeliveryTypes
//  * @param {*} goodsDeliveryTypes
//  * @returns {Boolean} 0 - 自提，1 - 外送
//  */
// const getShelfMode = (isGoodsItem, groupDeliveryTypes, goodsDeliveryTypes) => {
//   // 商品
//   if (isGoodsItem) {
//     if (groupDeliveryTypes && goodsDeliveryTypes) {
//       return groupDeliveryTypes.includes(1) && goodsDeliveryTypes.includes(1)
//         ? 0
//         : 1;
//     }
//     return 0;
//   }

//   // 商品分组
//   // if (groupDeliveryTypes) {
//   //   return groupDeliveryTypes.includes(1) ? 0 : 1;
//   // }

//   return 0;
// };

// const shelfGoodsNavigator = (linkItem) => {
//   const shelfGoodsType = 'shelf_goods';
//   const { linkId, linkType, alias, extraData = {} } = linkItem;
//   const isGoodsItem = linkType === shelfGoodsType;

//   // 判断 mode
//   const deliveryTypes =
//     extraData[isGoodsItem ? 'groupSupportDeliveryType' : 'deliveryType'];
//   const goodsDeliveryTypes = isGoodsItem
//     ? extraData.goodSupportDeliveryType // 后端 goods 拼成了 good
//     : [1, 2, 3];

//   // 包含自提
//   const mode = getShelfMode(isGoodsItem, deliveryTypes, goodsDeliveryTypes);

//   const navigateParams = {
//     mode,
//   };
//   app.globalData.shelfParams = {
//     mode,
//   };

//   // 处理参数
//   // 1. 点单宝商品
//   if (linkType === shelfGoodsType) {
//     Object.assign(navigateParams, {
//       fromShared: 1,
//       sharedGoodsIdProp: linkId,
//       sharedGoodsAliasProp: alias,
//     });
//   }

//   // 注释点单宝商品分组
//   // else if (linkType === 'shelf_group') {
//   //   // 2. 点单宝商品分组
//   //   // 处理二级分组
//   //   if (extraData.level === 2) {
//   //     navigateParams.groupId = extraData.parentGroupId;
//   //     navigateParams.secondGroupId = extraData.groupId;
//   //   } else {
//   //     navigateParams.groupId = linkId;
//   //   }
//   // }

//   navigateToShelfOrderPage(Args.add(SHELF_PAGE_PATH, navigateParams));
// };

// const handleWxScanCode = () => {
//   app.logger &&
//     app.logger.log({
//       et: 'custom', // 事件类型
//       ei: 'click_wx_scan_code', // 事件标识
//       en: '点击扫一扫链接', // 事件名称
//     });
//   wx.scanCode({
//     success: (res) => {
//       console.log('res', res);
//       if (res.path) {
//         Toast({
//           type: 'success',
//           message: '识别成功，正在跳转',
//           duration: 0,
//         });
//         // eslint-disable-next-line @youzan/dmc/wx-check
//         wx.navigateTo({
//           url: `/${res.path}`,
//           complete: () => {
//             Toast.clear();
//           },
//         });
//       } else {
//         Toast.fail('仅能扫此商家桌码/店铺码哦');
//       }
//     },
//     fail: () => {
//       Toast.error('识别失败');
//     },
//   });
// };

// export const retailShelfNavigator = (linkItem, context) => {
//   const { linkType, linkUrl } = linkItem;
//   // 到店自提和外卖到家需要绑定手机号，而且只有小程序链接，不需要进行转换
//   if (
//     [
//       SHELF_LINK_TYPE_ENUM.SELF,
//       SHELF_LINK_TYPE_ENUM.TAKEOUT,
//       SHELF_LINK_TYPE_ENUM.SaleToday,
//       SHELF_LINK_TYPE_ENUM.SaleAdvance,
//     ].includes(linkType)
//   ) {
//     app.globalData.shelfParams = {
//       mode: SHELF_LINK_TYPE_TO_MODE[linkType],
//     };
//     navigateToShelfOrderPage(SHELF_LINK_URL_MAP[linkType], context);
//   } else if (
//     [SHELF_LINK_TYPE_ENUM.SCAN_GO, SHELF_LINK_TYPE_ENUM.FREE_GO].includes(
//       linkType
//     )
//   ) {
//     // 性能埋点
//     Hummer.mark.start('scan_order_decorate');
//     // 扫码点单 / 自助结账
//     Tee.navigate({ url: `/${linkUrl}` });
//   } else if (
//     [
//       SHELF_LINK_TYPE_ENUM.SHELF_GOODS,
//       SHELF_LINK_TYPE_ENUM.SHELF_GROUP,
//     ].includes(linkType)
//   ) {
//     // 点单宝商品和商品分组
//     shelfGoodsNavigator(linkItem);
//   } else if (linkType === SHELF_LINK_TYPE_ENUM.WX_SCAN_CODE) {
//     handleWxScanCode();
//   }
// };

export { retailShelfNavigator } from '@youzan/decorate-tee/src/common/utils/jump-link/biz-utils/retail-shelf-navigator';
