## 微页面容器组件

### 组件的分页逻辑

#### 分页

之前分页逻辑是在 `showcase-container` 中处理的。这样会有一个问题，需要分页的组件实际上已经从上层传了进来，实际上并没有减少 `setData` 的量。现在的方案是通过 `Event`，由 `showcase-container` 触发分页，由上层业务方分页传入。这样只需要通过传入组件个数即可实现分页。

```javascript
// showcase-container
Event.trigger('feature-load:more');
// page
Event.on('feature-load:more');
```

但是，这样会存在两个问题：

- 无法监听组件的变更。
- 打开多个微页面事件会出现数据串掉的问题。

所以引入了一个新的属性 `uniqueKey`。通过这个属性确定 `showcase-container` 和 业务方的唯一事件。并且通过监听 `uniqueKey` 的变更来实现组件的变更。

```javascript
// showcase-container
Event.trigger(`feature-load:more${uniqueKey}`);
// page
Event.on(`feature-load:more${uniqueKey}`);
```

> 如何使用

```html
<showcase-container
  components="{{ theme.feature }}"
  unique-key="{{ theme.uniqueKey }}"
  components-length="{{ theme.componentsLength }}"
/>
```

```javascript
// 参考 src/components/showcase/index.js
// page
import FeatureLoadControl from 'shared/components/showcase-options/feature-load/index';
theme['theme.componentsLength'] = components.length;
theme['theme.uniqueKey'] = uniqueKey;
FeatureLoadControl.setShowcaseComponents(components, false, false, uniqueKey);

// onUnload 移除
FeatureLoadControl.clearShowcaseComponents(this.uniqueKey);
```

#### 不分页

```html
<showcase-container components="{{ theme.feature }}" />
```

### 引入的业务方

> 通过 src/components/showcase/index.wxml 引入

- 微页面 src/pages/home/<USER>/feature
- 首页 src/pages/home/<USER>/feature
- 个人中心 src/pages/usercenter/dashboard/index
- 签到 src/packages/shop/ump/sign-in/index
- 详情 src/packages/shop/shopnote/detail/index
- 全部拼团商品页 src/packages/shop/goods/group/index?pageType=groupon&component={"grouponType":0,"showPrice":true,"hideGoodsSold":0}

> 通过 showcase-container

- 商品详情 shared/packages/goods/containers/goods-detail/index
