import Event from '@youzan/weapp-utils/lib/event';
import TeeEvent from '@youzan/tee-event';
import StickyControl from '@youzan/decorate-tee/native/weapp/common/utils/sticky-control';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import get from '@youzan/weapp-utils/lib/get';
import throttle from '@youzan/weapp-utils/lib/throttle';
import { isArray } from '@youzan/tee-util/lib/common/object';
import { Hummer } from 'shared/utils/hummer';

import { getStorage } from '@youzan/tee-api';
import spm from 'shared/utils/spm';
import navigate from 'shared/utils/navigate';
import {
  initPageCountdown,
  clearPageCountdown,
} from 'shared/utils/countdown-list';
import getApp from 'shared/utils/get-safe-app';
import { isValidLink } from 'shared/components/showcase/utils/linkAdaptor';
import getComponentType from 'shared/components/showcase/utils/getComponentType';
import { SHELF_LINK_TYPE_ENUM } from 'shared/components/showcase/utils/config';
import { getLoggerParams, loggerRandomKey } from 'shared/utils/log-params';
import LifetimeBehavior from 'shared/components/showcase/behaviors/page-lifetimes-behavior';
import { jumpToLink } from './jump-to-link';
import {
  requestSubscribeMessage,
  wscSubscribeMessage,
} from '@/utils/subscribe-message';
import FeatureLoadControl from './feature-load';
import AudioBehavior from './behaviors/audio';
import ElevatorNavigationBehavior from './behaviors/elevator-navigation';
import getUniquePageKey from 'shared/utils/unique';
import VideoBehavior from './behaviors/video';
import loggerBehavior from 'shared/components/showcase/behaviors/logger-behavior';
import { getCurrentPage } from 'shared/utils/url';

/* #ifdef BUILD_ENV=youzanyun */
import {
  getLoggerExtraParams,
  getExtLoggerParams,
} from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
/* #endif */

const app = getApp();
const { windowHeight } = app.getSystemInfoSync();
const COMPONENTS_PER_PAGE = 8;
const PRELOAD_COMPONENT_OFFSET = windowHeight * 0.2;
const COUNTDOWN_KEY = 'featureCountdown';
const CONTENT_VIEW_LOGGER_TYPES = ['hot_words_reference'];

const LOAD_PAGE_RANDOM_KEY = loggerRandomKey;
const HISTORY_CITY_KEY = 'hotel-city';
const getUUid = () => {
  const { user: { uuid } = {} } = app.logger.getGlobal() || {};
  return uuid;
};

const hotAreaKey = 'components_style_is_hotarea';
const logKeyMethod = 'components_style_show_method';
const logKeySize = 'components_style_size';
const loadedFlag = 'featureComponentsAllLoaded';

export default {
  behaviors: [
    LifetimeBehavior,
    AudioBehavior,
    ElevatorNavigationBehavior,
    VideoBehavior,
    loggerBehavior,
  ],
  externalClasses: ['showcase-contact-us'],
  properties: {
    components: {
      type: Array,
      value: [],
      observer(components) {
        if (components && components.length) {
          this.initSyncFeatureComponents(components);
        }
      },
    },

    hqKdtId: Number,

    extra: {
      type: Object,
      value: {},
    },

    hasSearchSticky: Boolean,
    alias: String,
    componentsLength: Number,
    featureHotUpdate: Boolean,

    uniqueKey: {
      type: String,
      value: '',
      observer(uniqueKey, preKey) {
        // 如果 uniqueKey 存在的话 则初始化
        if (uniqueKey) {
          this.initUniqueKey(uniqueKey, preKey);
        }
      },
    },
    // 防止两个showcase-container带来的冲突
    notForbid: {
      type: Boolean,
      value: true,
    },
    customStyle: {
      type: String,
      value: '',
    },
  },

  data: {
    curComponents: [],
    curUniqueKey: '',
    scenes: '',
    [loadedFlag]: false,
    triggerFeatureLoaded: false,
    loading: true,
    startIndex: 0,
    pageRandomNumber: LOAD_PAGE_RANDOM_KEY,
    pageCommonData: {
      countdownKey: COUNTDOWN_KEY,
      pageRandomString: makeRandomString(8),
    },
    showAuthDialog: false,
    deliverySetting: {
      mode: 1,
      isLocal: false,
      isSelf: false,
    },
    isShowcaseContainerShow: true,
    uuid: getUUid(),
    themeColors: {},
    themeCss: '',
    pageScrollEvent: '',
    shopInfo: {},
    city: '',
    userId: '',
    showFeatureSubscribeMask: false, // 订阅消息蒙尘
    cloudLoggerField: {},
    tableCode: '',
  },

  pageLifetimes: {
    show() {
      if (this.data.notForbid) {
        this.isComponentHide = false;
        this.setFeatureLoggerParams();
        this.initCountDownKey();
        initPageCountdown(this, `pageCommonData.${COUNTDOWN_KEY}`);
        getStorage(HISTORY_CITY_KEY).then(({ data = {} }) => {
          const curCity = data?.value;
          if (curCity && this.data.city !== curCity) {
            this.setYZData({
              city: curCity,
            });
          }
        });
        // 页面隐藏又展示，需要重新监听页面滚动
        if (this.isPageHide) {
          Event.on(this.pageScrollEvent, this.pageScrollFn);
          this.isPageHide = false;
        }
        const { uniqueKey } = this.data;
        if (this.oldStickyControl && this.oldStickyControl[uniqueKey]) {
          const stickyInfo = this.oldStickyControl[uniqueKey];
          StickyControl.__map = stickyInfo.map;
          StickyControl.__sub = stickyInfo.sub;
          StickyControl.__list = stickyInfo.list;
          StickyControl.__willStickyComponentList = stickyInfo.willSticky;
        } else {
          StickyControl.resetData();
        }
      }
    },

    hide() {
      this.isComponentHide = true;
      clearPageCountdown();
      if (this.pageScrollEvent) {
        Event.off(this.pageScrollEvent, this.pageScrollFn);
      }
      this.isPageHide = true;
      // 此时需要记住 StickyControl 的信息, 页面回退时存入
      const { uniqueKey } = this.data;
      const oldStickyControl = {
        map: StickyControl.__map,
        sub: StickyControl.__sub,
        list: StickyControl.__list,
        willSticky: StickyControl.__willStickyComponentList,
      };
      this.oldStickyControl = { [uniqueKey]: oldStickyControl };
      StickyControl.resetData();
    },
  },
  ready() {
    if (this.data.notForbid) {
      // todo 整个pagecommondata可能都没用了，但是这里可能涉及到其他地方的模板，慎重！！！
      this.initCountDownKey();
      initPageCountdown(this, `pageCommonData.${COUNTDOWN_KEY}`);

      /* #ifdef BUILD_ENV=youzanyun */
      this.setYZData({
        cloudLoggerField: { goods: getExtLoggerParams('goods') },
      });
      /* #endif */
    }
  },

  detached() {
    this.clearFeatureComponents();
    clearPageCountdown();
    StickyControl.resetData();
  },

  methods: {
    onPageScroll(e) {
      if (this.hasElevator) {
        this.onPageScrollSetElevatorNavigationPosition();
      }

      if (this.hasVideo) {
        this.videoAutoPlay(e);
      }

      if (this.needContentViewLogger) {
        TeeEvent.trigger('setcontentviewlog');
      }
    },
    initData() {
      getStorage(HISTORY_CITY_KEY).then(({ data = {} }) => {
        this.setYZData({
          city: data?.value || '全国',
        });
      });
      const { userId } = app.getUserInfoSync();
      try {
        const { tableCode = '' } = app.globalData.scanCodeDecoConfig || {};
        const { componentsFormatConfig = {}, themeColors = {} } =
          app.globalData.featurePageConfig;
        this.componentsFormatConfig = componentsFormatConfig;
        // 预加载的情况下，首页数据比主题色接口快，导致页面颜色变化，所以从接口中取主题色
        let themeCss = '';
        for (const [key, value] of Object.entries(themeColors.colors || {})) {
          themeCss += '--' + key + ':' + value + ';';
        }
        this.setYZData({
          themeColors: themeColors?.colors,
          themeCss,
          tableCode,
        });
      } catch (e) {}

      app.getShopInfo().then((data) => {
        // // 非首页情况下，
        // this.componentsFormatConfig = {
        //   soldOutGoodsFlag: data.config?.sold_out_goods_flag,
        //   logo: data.logo,
        // };
        this.setYZData({
          shopInfo: {
            shopName: data.shop_name,
            shopLogo: data.logo,
            isEduChainStore: data.chainStoreInfo?.isEduChainStore,
            isMultiStore: data.isMultiStore,
          },
          userId,
        });
      });
    },
    initUniqueKey(key, preKey) {
      this.initData();
      const {
        componentsLength,
        featureHotUpdate,
        extra,
        shopInfo: { isMultiStore },
      } = this.data;
      const firstScreenData = FeatureLoadControl.getFirstScreenComponents(key);
      const isShowcaseContainerShow = firstScreenData
        ? true
        : componentsLength.length &&
          (+extra.templateId === 83 || featureHotUpdate || isMultiStore);
      this.pageScrollEvent = 'onPageScroll' + getUniquePageKey();
      if (this.pageScrollFn) {
        Event.off(this.pageScrollEvent, this.pageScrollFn);
      }
      this.pageScrollFn = throttle(this.onPageScroll.bind(this), 300);

      this.needPageScroll = false;
      // `fd530a5cCBuWKys6&DELAY_ENTER_SHOP&`
      let scenes = '';
      try {
        const [, match = ''] = key.match(/&(.*)&$/) || [];
        scenes = match;
      } catch (error) {
        // error
      }

      // 初始化分页状态
      let optData = {
        scenes,
        curUniqueKey: key,
        [loadedFlag]: false,
        loading: !firstScreenData,
        startIndex: firstScreenData ? 8 : 0,
        isShowcaseContainerShow,
        pageScrollEvent: this.pageScrollEvent,
      };

      if (scenes !== 'DELAY_ENTER_SHOP') {
        if (firstScreenData) {
          optData = {
            ...optData,
            ...this._dealComponentsData(firstScreenData, 0, [], {
              scenes,
              isFirstScreenData: !!firstScreenData,
            }),
          };
        } else {
          optData.curComponents = [];
        }
      }

      this.setYZData(optData, {
        immediate: true,
        cb: () => {
          if (!isShowcaseContainerShow) {
            this.setYZData({
              isShowcaseContainerShow: true,
            });
          }
        },
      });

      // 处理视频
      if (firstScreenData) {
        const allComponents = FeatureLoadControl.getCurPageAllComponents(key);
        this._dealVideoData(allComponents);
      }
      // 处理视频结束

      // 如果之前存在 则清空
      if (preKey) this.clearFeatureComponents(preKey);
      // 初始化 event 事件
      if (firstScreenData) {
        setTimeout(() => {
          this.initFeatureComponents(key);
        }, 1000);
      } else {
        this.initFeatureComponents(key);
      }
    },

    _dealVideoData(allComponents) {
      this.videoData = allComponents.filter((i) => i.type === 'video');
      if (
        this.isPullRefresh &&
        this.videoData.length !== this.realVideoContext.length
      ) {
        this.videoContext = [];
        this.isPullRefresh = false;
      }
      this.hasVideo = this.videoData.length !== 0;
    },

    /**
     * 清除 uniqueKey Event
     * @param key
     */
    clearFeatureComponents(key) {
      let { uniqueKey = '' } = this.data;
      // key 如果外部有设置key 则用外部
      uniqueKey = key || uniqueKey;

      FeatureLoadControl.clearShowcaseComponents(uniqueKey);
    },

    /**
     * 不通过懒加载分页
     * @param components
     */
    initSyncFeatureComponents(components) {
      // 存在 uniqueKey 不往下执行
      if (this.data.uniqueKey) return;
      if (!Array.isArray(components)) return;
      this.setData({ isShowcaseContainerShow: false });
      this.initData();
      const curComponents = this._formatComponents(components);

      this.loggerShowedComponents(curComponents, 0);
      // 只能用setData，不然数据不更新，组件不展示
      this.setYZData(
        {
          curComponents,
          loading: false,
        },
        {
          immediate: true,
          cb: () => {
            if (!this.data.isShowcaseContainerShow) {
              this.setData({
                isShowcaseContainerShow: true,
              });
            }
          },
        }
      );
    },
    /**
     * 根据 uniqueKey 分页组件
     * @param key
     */
    initFeatureComponents(key) {
      let { uniqueKey = '' } = this.data;
      // key 如果外部有设置key 则用外部
      uniqueKey = key || uniqueKey;

      // 初始化 分页 Event 相关
      Event.on(`feature-load:init${uniqueKey}`, () => {
        // 进入分页
        this._loadMoreFeatureThemeComponents();
      });
      // 绑定显示组件
      Event.on(
        `feature-load:show-component${uniqueKey}`,
        ({ components, startIndex, allComponents, hasLoadFirstScreen }) => {
          const hasElevator = allComponents.find(
            (i) => i.type === 'elevator_navigation'
          );
          this._showFeatureThemeComponents(
            hasElevator ? allComponents : components,
            startIndex,
            allComponents,
            { isFirstScreenData: hasLoadFirstScreen }
          )
            .then(() => {
              setTimeout(
                () => {
                  this._checkIsFeatureBottomVisible()
                    .then(this._loadMoreFeatureThemeComponents.bind(this))
                    .catch((err) => {
                      console.log(err);
                    });
                },
                startIndex === 0 && this.hasGoodsOrImage ? 1000 : 500
              );
            })
            .catch((err) => {
              app.logger.appError({
                name: 'feature_components_parse_error',
                message: '微页面组件解析报错',
                // eslint-disable-next-line prefer-object-spread
                detail: Object.assign(
                  {},
                  err,
                  __wxConfig && __wxConfig.appLaunchInfo
                ),
              });
            });
        }
      );
      // 开始
      Event.trigger(`feature-load:start${uniqueKey}`);
    },

    setFeatureLoggerParams() {
      if (!this.data.extra) return;
      const { featureId, templateId, isHomePage } = this.data.extra;
      if (featureId) {
        Event.trigger('feature:show', {
          pageId: featureId,
          logParams: {
            template_id: templateId,
            is_home: Number(isHomePage),
          },
        });
      }
    },
    initCountDownKey() {
      this.setYZData({
        'pageCommonData.pageRandomString': makeRandomString(8),
      });
    },

    onPullDownRefresh() {
      if (this.isComponentHide) return;
      this.audio__clearAudios();

      // 这里先清除倒计时，再触发下拉刷新事件
      clearPageCountdown();
      this.isPullRefresh = true;
    },

    onReachBottom() {
      if (this.isComponentHide) return;
      const allLoaded = get(this.data, loadedFlag, null);
      if (allLoaded || allLoaded === null) return;
      this._loadMoreFeatureThemeComponents();
    },

    /**
     * 唤起sku
     */
    handleBuyClick(e) {
      // 多端 or 中台化 加了payload
      this.triggerEvent('buy', e.detail && e.detail.payload);
    },

    /**
     * 判断是否滚到了底部
     */
    _checkIsFeatureBottomVisible() {
      return new Promise((resolve, reject) => {
        this.createSelectorQuery()
          .select('#theme-feature__content-end-hook')
          .boundingClientRect((rect) => {
            if (!rect) {
              return reject();
            }
            // 触发首屏打点
            Event.trigger('feature:first_screen_cover');
            const top = rect.top || 0;
            // 内容需要大于屏幕高度与加载阈值之和才能在滑动屏幕时触发加载更多
            if (top > windowHeight + PRELOAD_COMPONENT_OFFSET + 100) {
              return reject();
            }

            resolve();
          })
          .exec();
      });
    },

    _loadMoreFeatureThemeComponents() {
      const {
        startIndex = 0,
        uniqueKey = '',
        componentsLength = 0,
      } = this.data;

      if (startIndex + COMPONENTS_PER_PAGE >= componentsLength) {
        this.setYZData({
          [loadedFlag]: true,
          loading: this.data.loading ? false : this.data.loading,
        });

        this._checkTriggerFeatureLoaded();

        if (startIndex >= componentsLength) return Promise.resolve();
      }

      // 触发 加载更多
      Event.trigger(`feature-load:more${uniqueKey}`, {
        startIndex,
        size: COMPONENTS_PER_PAGE,
      });
    },

    _checkTriggerFeatureLoaded() {
      const { triggerFeatureLoaded = false, uniqueKey = '' } = this.data;

      if (triggerFeatureLoaded) return;

      this.setData(
        {
          triggerFeatureLoaded: true,
        },
        () => {
          this.triggerEvent('feature-loaded');
          Event.trigger(`feature-load:loaded${uniqueKey}`);
        }
      );
    },

    _formatComponents(components) {
      const shelfLink = Object.values(SHELF_LINK_TYPE_ENUM);
      const { soldOutGoodsFlag: soldOutGoodsImg } =
        this.componentsFormatConfig || {};
      return components
        .map((item) => {
          const type = getComponentType(item.type);
          let uuidClass = '';
          let uuidId = '';
          if (item.uuid) {
            if (item.uuid.startsWith('custom_start_')) {
              uuidId = item.uuid.replace('custom_start_', '');
              uuidClass = `position_component_${uuidId} ${item.uuid}`;
            } else if (item.uuid.startsWith('custom_end_')) {
              uuidId = item.uuid;
              uuidClass = `${item.uuid}`;
            } else {
              uuidId = item.uuid;
              uuidClass = `position_component_${item.uuid}`;
            }
          }
          item.uuidClass = uuidClass;
          item.uuidId = uuidId;
          item.itemType = type || item.type;

          if (item.type === 'image_text_nav' && isArray(item.imgs)) {
            return {
              ...item,
              imgs: item.imgs.filter((i) =>
                isValidLink(i, ['hotarea'].concat(shelfLink))
              ),
            };
          }

          if (
            item.component === 'dc-goods' ||
            item.type === 'ump_limitdiscount' ||
            item.type === 'member_goods' ||
            item.type === 'ump_seckill' ||
            item.type === 'period_buy' ||
            item.type === 'groupon' ||
            item.type === 'point_goods' ||
            item.type === 'tag_list_top'
          ) {
            return {
              ...item,
              itemCardOpt: {
                ...item.itemCardOpt,
                imgOpt: item.itemCardOpt.imgOpt
                  ? {
                      ...item.itemCardOpt.imgOpt,
                      soldOutGoodsImg,
                    }
                  : item.itemCardOpt.imgOpt,
              },
            };
          }

          if (item && item.type === 'tag_list_left') {
            return {
              ...item,
              tagGroupOpt: {
                ...(item?.tagGroupOpt || {}),
                itemCardOpt: {
                  ...(item?.tagGroupOpt?.itemCardOpt || {}),
                  imgOpt: {
                    ...(item?.tagGroupOpt?.itemCardOpt?.imgOpt || {}),
                    soldOutGoodsImg,
                  },
                },
              },
            };
          }

          if (item && item.type === 'offline_shop_info') {
            //  优先用当前店铺的 logo
            item.logo = this.componentsFormatConfig?.logo || item.logo;
          }

          return item;
        })
        .filter((filterItem) => {
          if (filterItem.type === 'cube' && isArray(filterItem.imgs)) {
            return !filterItem.imgs.every((i) => !isValidLink(i, shelfLink));
          }
          return true;
        });
    },

    _dealComponentsData(components, prefixIndex = 0, allComponents, opt) {
      const componentsData = this._formatComponents(components);
      const { scenes, isFirstScreenData } = opt || {};

      const newComponentsData = {};

      // 此处是否能拿到 this.data.scenes 存疑， 先从参数中传过来
      for (let i = 0; i < componentsData.length; i++) {
        const index = i + prefixIndex;
        if (
          !this.data.curComponents[index] ||
          (scenes || this.data.scenes) === 'DELAY_ENTER_SHOP'
        ) {
          newComponentsData[`curComponents[${index}]`] = componentsData[i];
        }
      }

      // 提前渲染首屏数据白名单控制的
      if (!isFirstScreenData) {
        this._dealVideoData(allComponents);
      }

      // 只有首屏需要
      if (prefixIndex === 0) {
        this.hasGoodsOrImage = components.some((item) => {
          return (
            // 去除纯文字导航
            (item.component === 'dc-image' && +item.tpl !== 5) ||
            item.component === 'dc-goods' ||
            item.type === 'tag_list_top'
          );
        });
        if (!this.hasGoodsOrImage) {
          const app = getApp();
          app.trigger('home_dashboard_com_loaded');
          Hummer?.markRendered('fs');
        }
        // 包括电梯导航，所有组件在第一次事件中都会抛出，所以只用判断第一页
        this.hasElevator = components.find(
          (i) => i.type === 'elevator_navigation'
        );
      }

      // 判断是否需要监听页面滚动事件
      if (!this.needPageScroll) {
        this.needPageScroll = components.some((item) => {
          return (
            item.type === 'elevator_navigation' ||
            item.type === 'video' ||
            CONTENT_VIEW_LOGGER_TYPES.includes(item.type)
          );
        });
        if (this.needPageScroll) {
          Event.on(this.pageScrollEvent, this.pageScrollFn);
        }
      }

      if (!this.needContentViewLogger) {
        this.needContentViewLogger = components.some((i) =>
          CONTENT_VIEW_LOGGER_TYPES.includes(i.type)
        );
      }

      this.loggerShowedComponents(componentsData, prefixIndex);
      return newComponentsData;
    },

    _showFeatureThemeComponents(
      components,
      prefixIndex = 0,
      allComponents,
      opt
    ) {
      return new Promise((resolve) => {
        const { isFirstScreenData } = opt || {};
        const newComponentsData = this._dealComponentsData(
          components,
          prefixIndex,
          allComponents,
          { isFirstScreenData }
        );

        this.setYZData(
          {
            ...newComponentsData,
            startIndex: prefixIndex + components.length,
            // loading: false,
            requestId: app.globalData.wscHomeRequestId,
          },
          {
            immediate: true,
            cb: () => {
              resolve();
            },
          }
        );
      });
    },

    loggerShowedComponents(componentsData, prefixIndex) {
      // 直接上报的组件
      const loggerComponents = [];
      // 获取完spm上报
      const storageLoggerComponents = [];
      const spmType = spm.getPageSpmTypeId();

      for (let i = 0; i < componentsData.length; i++) {
        const index = i + prefixIndex;
        const { componentIndex = index } = componentsData[i];
        const loggerItem = getLoggerParams(
          componentsData[i],
          componentIndex,
          LOAD_PAGE_RANDOM_KEY,
          spmType
        );

        if (spmType) {
          const { loggerEnable = true } = componentsData[i];
          loggerEnable && loggerComponents.push(loggerItem);
        } else {
          storageLoggerComponents.push(loggerItem);
        }

        componentsData[i].loggerItem = loggerItem;
      }
      // 微页面组件曝光
      this._setLoggerComponents(loggerComponents, storageLoggerComponents);
    },

    _setLoggerComponents(loggerComponents, storageLoggerComponents) {
      if (storageLoggerComponents.length) {
        Event.once('SPM:success', () => {
          const spmType = this.ctx.logger.getPageSpm();
          storageLoggerComponents.map((item) => {
            item.banner_id = spmType + item.banner_id;
            return item;
          });
          this.__ensureComponentsLogger(storageLoggerComponents);
        });
      }
      // 批量曝光埋点上报
      if (!loggerComponents.length) return;

      this.__ensureComponentsLogger(loggerComponents);
    },

    // 打点：曝光打点
    __ensureComponentsLogger(loggerComponents) {
      const {
        extra: { templateId = 0 },
      } = this.data;
      loggerComponents.forEach((one) => {
        const loggerMsg = {
          et: 'view',
          ei: 'component_view',
          en: '组件曝光',
          params: {
            ...one,
            template_id: templateId,
          },
        };
        const { imgs } = one;
        if (Array.isArray(imgs)) {
          this.__ensureVideoNumberDynamicLogger(imgs);
        }
        app.logger?.log(loggerMsg);
      });
    },
    __ensureVideoNumberDynamicLogger(imgs = []) {
      imgs.forEach((imgConfig) => {
        if (imgConfig?.link_type === 'video_number_dynamic') {
          const { video_dynamic_params, image_url, link_title } = imgConfig;
          const videoNumberDynamicParam = {
            ...video_dynamic_params,
            image_url,
            link_title,
          };
          app.logger?.log({
            et: 'view',
            ei: 'wxvideo_video_show',
            en: '视频号动态曝光',
            params: videoNumberDynamicParam,
          });
        }
      });
    },

    // 联系客服的回调
    onContactBack: navigate.contactBack,

    // 打点：组件点击打点（不包含内容点击）
    loggerComponentClick(component, componentExtra) {
      const { loggerItem } = component;
      if (!loggerItem) {
        return;
      }

      if (componentExtra?.detail?.[hotAreaKey]) {
        loggerItem[hotAreaKey] = componentExtra?.detail?.[hotAreaKey] || 0;
      }

      let cloudLoggerInfo = {};

      /* #ifdef BUILD_ENV=youzanyun */
      const cloudExtInfo = {
        imageUrl: componentExtra?.imageUrl || componentExtra?.detail?.imageUrl,
        top: componentExtra?.detail?.top,
        left: componentExtra?.detail?.left,
      };
      cloudLoggerInfo = getLoggerExtraParams(
        component.type === 'cube' ? 'cube_v3' : component.type,
        this.getCloudLoggerInfo({
          ...component,
          type: component.type === 'cube' ? 'cube_v3' : component.type,
          ...cloudExtInfo,
          loggerParams: loggerItem,
        })
      );
      /* #endif */

      const { extra = {} } = this.data;
      const { templateId = 0 } = extra;

      const index = (componentExtra?.detail?._index || 0) + 1;
      const loggerMsg = {
        et: 'click',
        ei: 'click_content',
        en: '组件点击',
        params: {
          ...loggerItem,
          banner_id: loggerItem.banner_id,
          component: loggerItem.component,
          template_id: templateId,
          component_index: index,
          scene: component?.scene,
          ...cloudLoggerInfo,
        },
      };
      app.logger?.log(loggerMsg);
    },
    jumpToLink(component, extra = {}, needComponentClickLogger = true) {
      const link = extra.detail || {};
      const { banner_id } = component?.loggerItem || {};
      let extraData = {
        kdtId: this.data.extra.kdtId,
        banner_id,
        context: this,
      };
      if (extra && extra !== null && typeof extra === 'object') {
        extraData = { ...extra, ...extraData };
      }

      // 打点：组件点击打点（不包含内容点击）
      needComponentClickLogger && this.loggerComponentClick(component, extra);
      jumpToLink(link, extraData);
    },
    handleItemClick(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail: { payload: data },
      } = e;
      this.jumpToLink(component, { detail: data });
    },

    shopClick(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail: { payload: data },
      } = e;
      const link = data || {};
      const { type } = link;
      const loggerMsg = {
        et: 'click',
        params: {
          components_style_store_info_style: '',
          component: 'shop_banner_weapp',
        },
      };
      if (type === 'allgoods') {
        loggerMsg.ei = 'click_all_goods';
        loggerMsg.en = '点击全部商品';
        // Warn：上新，当前没有上报，type 是否为 newgoods 待定
      } else if (type === 'newgoods') {
        loggerMsg.ei = 'click_new_goods';
        loggerMsg.en = '点击上新商品';
      }
      if (loggerMsg.ei) {
        app.logger?.log(loggerMsg);
      }
      const extraData = {
        detail: link,
      };
      this.jumpToLink(component, extraData);
    },

    rankingMoreClick(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail: { payload: data },
      } = e;
      const { tabType } = data || {};
      const extraData = {
        detail: data,
      };
      component.loggerItem.tab_type = tabType;
      this.jumpToLink(component, extraData);
    },

    rankingClick(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail: { payload: data },
      } = e;
      const { tabType } = data || {};
      component.loggerItem.tab_type = tabType;
      this.loggerComponentClick(component, { detail: {} });
    },

    rankingItemClick(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail: { payload: data },
      } = e;
      const { link } = data || {};
      const extraData = {
        detail: link,
      };
      this.jumpToLink(component, extraData, false);
    },

    handleGoodsClick(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail: { payload: data },
      } = e;
      const extraData = {
        detail: data.link,
        imageUrl: data.imageUrl,
        goodsPreloadOpt: data.goodsPreloadOpt,
        title: data.title,
      };
      this.jumpToLink(component, extraData);
    },

    /**
     * 埋点
     */
    logger({ detail: params }) {
      app.logger &&
        app.logger.log({
          ...params,
          si: this.data.extra.kdtId,
        });
    },

    onNavigate(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail,
      } = e;

      const { linkType, extra = {} } = detail;
      this.logTab(linkType, extra);

      this.jumpToLink(component, { ...detail, detail });
    },

    // 跳转tab的无法携带 banner_id 手动上报
    logTab(type, extra) {
      const tabList = ['shopnote', 'usercenter', 'cart', 'homepage'];
      if (tabList.some((tabItem) => tabItem === type)) {
        const loggerMsg = {
          et: 'click',
          ei: 'click_content',
          en: '组件点击',
          params: { ...extra },
        };
        app.logger.log(loggerMsg);
      }
    },
    handleShowAuthDialog() {
      this.setData({ showAuthDialog: true });
    },

    handleHideAuthDialog() {
      this.setData({ showAuthDialog: false });
    },
    handleComponentLoaded() {
      const app = getApp();
      app.trigger('home_dashboard_com_loaded');
      Hummer?.markRendered('fs');
    },
    requestSubscribeMessage(e) {
      // 调用 utils/requestSubscribeMessage
      requestSubscribeMessage(e.detail.payload);
    },
    requestSubscribeMessagePushForTpl(e) {
      const {
        detail: { payload: params },
      } = e;
      this.requestSubscribeMessagePush(params);
    },
    requestSubscribeMessagePush(params) {
      const self = this;
      const { next, ...extra } = params || {};
      const { route } = getCurrentPage();

      // 仅支持微页面
      if (
        !['pages/home/<USER>/index', 'pages/home/<USER>/index'].includes(
          route
        )
      ) {
        next();
        return;
      }

      wscSubscribeMessage({
        ...extra,
        authorizationType: 'coupon',
        subscribePage: '微页面',
        scene: 'coupon_notice_scene',
        zIndex: 10010,
        options: {
          onComplete: next,
          next,
          onShowTips: () => {
            self.toggleFeatureSubscribeMask({ detail: true });
          },
          onCloseTips: () => {
            self.toggleFeatureSubscribeMask({ detail: false });
          },
        },
      });

      // await featureSubscribeMessagePush({
      //   ...extra,
      //   authorizationType: 'coupon',
      //   subscribePage: '微页面',
      //   scene: 'coupon_notice_scene',
      //   zIndex: 10010,
      //   options: {
      //     onComplete: next,
      //     next,
      //   },
      // });
    },
    onImageIndexChange(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail: { payload: data },
      } = e;
      const index = data._index + 1;
      this.imageLogger(component, index, {
        et: 'view', // 事件类型
        ei: 'view', // 事件标识
        en: '内容曝光', // 事件名称
      });
    },

    onCrowdsImageIndexChange(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail: { payload: data },
      } = e;
      const index = data._index + 1;
      this.imageLogger(
        component,
        index,
        {
          et: 'view', // 事件类型
          ei: 'view', // 事件标识
          en: '内容曝光', // 事件名称
        },
        {
          tplMap: {
            1: {
              [logKeyMethod]: 0,
            },
            2: {
              [logKeyMethod]: 1,
            },
          },
        }
      );
    },

    onCrowdsImageClick(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail: { payload: data },
      } = e;
      const index = data._index + 1;
      this.imageLogger(
        component,
        index,
        {
          et: 'click', // 事件类型
          ei: 'click_contnet', // 事件标识
          en: '内容点击', // 事件名称
        },
        {
          tplMap: {
            1: {
              [logKeyMethod]: 0,
            },
            2: {
              [logKeyMethod]: 1,
            },
          },
        }
      );
      const extra = {
        detail: data,
      };
      this.jumpToLink(component, extra, false);
    },

    imageLogger(component, index, oriLoggerMsg, { tplMap } = {}) {
      const { loggerItem, tpl } = component;
      const ids = loggerItem.banner_id.split('~');
      ids[2] = index;
      const bannerId = ids.join('~');
      /**
       * tpl = 1: show_method 7 一行一个
       * tpl = 2: show_method 5 轮播
       * tpl = 3: show_method 6 size 0 大图横向滑动
       * tpl = 4: show_method 6 size 1 小图横向滑动
       * tpl = 5: show_method 6 size 2 导航横向滑动
       * tpl = 6: show_method 7 is_hotarea 1 热区
       */
      tplMap = tplMap || {
        1: {
          [logKeyMethod]: 7,
        },
        2: {
          [logKeyMethod]: 5,
        },
        3: {
          [logKeyMethod]: 6,
          [logKeySize]: 0,
        },
        4: {
          [logKeyMethod]: 6,
          [logKeySize]: 1,
        },
        5: {
          [logKeyMethod]: 6,
          [logKeySize]: 2,
        },
        6: {
          [logKeyMethod]: 7,
          [hotAreaKey]: 1,
        },
      };
      const loggerMsg = {
        ...oriLoggerMsg,
        params: {
          [hotAreaKey]: 0,
          [logKeyMethod]: 0,
          [logKeySize]: 0,
          ...(tplMap[tpl] || {}),
          banner_id: bannerId,
          component: loggerItem.component,
          component_index: index,
        },
      };
      app.logger?.log(loggerMsg);
    },
    onCouponImageClick(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
        detail: { payload: data },
      } = e;
      const { clickParams, ...extra } = data || {};
      const index = (clickParams?._index || 0) + 1;

      this.imageLogger(component, index, {
        et: 'click', // 事件类型
        ei: 'click_contnet', // 事件标识
        en: '内容点击', // 事件名称
      });

      this.requestSubscribeMessagePush(extra);
    },
    onFansClick(e) {
      const {
        currentTarget: {
          dataset: { component },
        },
      } = e;
      this.loggerComponentClick(component, { detail: {} });
    },
    toggleFeatureSubscribeMask(e) {
      this.setYZData({
        showFeatureSubscribeMask: e.detail,
      });
    },
  },
};
