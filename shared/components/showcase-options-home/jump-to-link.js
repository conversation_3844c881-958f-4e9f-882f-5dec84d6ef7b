// import tee from '@youzan/tee';
// import request from '@youzan/tee-biz-request';
// import Toast from '@vant/weapp/dist/toast/toast';
// import isEmpty from '@youzan/utils/object/is-empty';
// import Args from '@youzan/utils/url/args';
// import buildUrl from '@youzan/utils/url/buildUrl';
// // import { generateUrl } from '@youzan/imc-components/js/utils/generateUrl';
// import {
//   videoDynamicClickEvent,
//   videoDynamicJumpSucc,
//   videoDynamicJumpFail,
// } from './logger-event';
// import { getPlugins } from '@youzan/ranta-helper-tee';
// import { SHELF_LINK_TYPE_ENUM } from 'shared/components/showcase/utils/config';
// // import {
// //   setApplicationScene,
// //   EXT_SCENE_ID,
// //   isHome,
// // } from '@youzan/tee-chain-store';
// import { retailShelfNavigator } from './retail-shelf-navigator';

// // const app = getApp();
// const MY_WEAPP_TYPE = '1';
// const OTHER_WEAPP_TYPE = '2'; // 其他小程序

// const APPJSON_TAB_BAR_LIST = [
//   '/pages/home/<USER>/index',
//   '/pages/goods/cart/index',
//   '/pages/usercenter/dashboard/index',
//   '/pages-retail/usercenter/dashboard-v2/index',
// ];

// // const FEATURE_SOURCE = {
// //   ONLINE: 1,
// //   HQ: 2,
// // };

// export const webAndWeappJump = (weappLink) => {
//   if (weappLink) tee.navigate({ url: weappLink });
// };

// // function getCurrentPagePath() {
// //   const pages = getCurrentPages();
// //   if (pages.length > 0) {
// //     const currentPage = pages[pages.length - 1];
// //     return currentPage.route;
// //   }
// //   return '';
// // }

// /**
//  * 获取链接跳转
//  * @param {Object} item item 格式 { type, alias, link_title, link_id, extra_data }
//  * @param {Object} extra extra 格式 { banner_id }
//  */
// export const jumpToLink = (item, extra = {}) => {
//   const { dmc } = getPlugins();

//   let { type = '' } = item;
//   let url = '';
//   let isTab = false;
//   const {
//     alias,
//     linkType = '',
//     linkId = '',
//     linkUrl = '',
//     linkTitle = '',
//     extraData = {},
//     query = {},
//     goodsAlias,
//   } = item;
//   const { kdtId, banner_id, imageUrl, goodsPreloadOpt, title } = extra;
//   const webkdtIdOpt = { kdt_id: kdtId };
//   const bannerIdOpt = {};
//   if (banner_id) {
//     bannerIdOpt.banner_id = banner_id;
//   }

//   type = linkType || type;

//   if (type === 'chat') {
//     // if (tee.getEnv() === 'web' && extra) {
//     //   const url = generateUrl(extra);
//     //   if (url) {
//     //     dmc.navigate(Args.add(url, bannerIdOpt));
//     //   }
//     // }
//   } else if (type === 'homepage') {
//     dmc.switchTab('Home', { ...bannerIdOpt, ...webkdtIdOpt }).catch(() => {
//       dmc.navigate('Home', { ...bannerIdOpt, ...webkdtIdOpt });
//     });
//   } else if (type === 'goods' && alias) {
//     const { image = {} } = goodsPreloadOpt || {};
//     dmc.navigate(
//       'GoodsDetail',
//       { alias, ...query, ...bannerIdOpt },
//       {
//         bizParams: {
//           alias,
//           image: {
//             url: imageUrl || image.url,
//             width: image.width,
//             height: image.height,
//           },
//           title,
//         },
//       }
//     );
//   } else if (type === 'feature' && alias) {
//     // 渠道页
//     dmc.navigate('Feature', { alias, ...bannerIdOpt });
//   } else if (type === 'tag') {
//     // 兼容alias为空的情况
//     const parameter = alias ? { alias } : Args.getAll(linkUrl);
//     if (parameter.alias) {
//       dmc.navigate('GoodsTag', {
//         alias: parameter.alias,
//         title: linkTitle,
//         ...bannerIdOpt,
//       });
//     }
//   } else if (type === 'cart') {
//     dmc.switchTab('Cart', { ...bannerIdOpt, ...webkdtIdOpt }).catch(() => {
//       dmc.navigate('Cart', { ...bannerIdOpt, ...webkdtIdOpt });
//     });
//   } else if (type === 'usercenter') {
//     dmc
//       .switchTab('Usercenter', { ...bannerIdOpt, ...webkdtIdOpt })
//       .catch(() => {
//         dmc.navigate('Usercenter', { ...bannerIdOpt, ...webkdtIdOpt });
//       });
//   } else if (type === 'shopnote') {
//     dmc.switchTab('Shopnote', { ...bannerIdOpt, ...webkdtIdOpt }).catch(() => {
//       dmc.navigate('Shopnote', { ...bannerIdOpt, ...webkdtIdOpt });
//     });
//   } else if (type === 'allgoods') {
//     dmc.navigate('AllGoodsList', {
//       title: linkTitle,
//       ...bannerIdOpt,
//     });
//   } else if (type === 'coupon') {
//     dmc.navigate('CouponDetail', {
//       id: item.link_id || item.linkId,
//       type: (item.coupon_type || item.couponType) === 7 ? 'promocard' : '',
//       ...bannerIdOpt,
//     });
//   } else if (type === 'point_card') {
//     webAndWeappJump(
//       `/packages/pointstore/goods-details/index?goods_id=${item.pointGoodsId}&${banner_id}=${banner_id}`
//     );
//   } else if (type === 'pointsstore') {
//     dmc.navigate('PointGoodsList', { ...bannerIdOpt, ...webkdtIdOpt });
//   } else if (type === 'seckill') {
//     dmc.navigate('Seckill', {
//       alias,
//       ...(goodsAlias ? { goodsAlias } : {}),
//       ...bannerIdOpt,
//     });
//   } else if (type === 'bargain') {
//     dmc.navigate('GoodsDetail', {
//       alias,
//       umpType: item.umpType || '',
//       activityId: item.activityId,
//       ...bannerIdOpt,
//     });
//   } else if (type === 'paidcolumn') {
//     dmc.navigate('PaidContentColumn', { alias, ...bannerIdOpt });
//   } else if (type === 'paidcontent') {
//     dmc.navigate('PaidContentContent', { alias, ...bannerIdOpt });
//   } else if (type === 'mypaidcontent') {
//     dmc.navigate('PaidContentList', { ...bannerIdOpt, ...webkdtIdOpt });
//   } else if (type === 'paidlive') {
//     dmc.navigate('PaidContentLive', { alias, ...bannerIdOpt });
//   } else if (type === 'allcourse') {
//     // 教育 - 全部课程
//     dmc.navigate('EduGoodsList', { ...bannerIdOpt, ...webkdtIdOpt });
//   } else if (
//     type === 'course' ||
//     type === 'educourse' ||
//     type === 'allofflinecourse' ||
//     type === 'eduappointment' ||
//     type === 'course_group' ||
//     type === 'course_category' ||
//     type === 'edumoments'
//   ) {
//     const url = linkUrl.replace(/^http(s)?:\/\/shop\d+-?\d+/, 'https://h5');
//     dmc.navigate('EduWebview', {
//       targetUrl: url,
//       ...bannerIdOpt,
//     });
//   } else if (type === 'link') {
//     // 自定义链接只支持公众号文章 并且只在专享版小程序里
//     const { linkUrl } = item;
//     if (linkUrl && /mp.weixin.qq.com\/s/.test(linkUrl)) {
//       dmc.navigate('CommonWebview', {
//         src: linkUrl,
//         ...bannerIdOpt,
//       });
//     }
//   } else if (type === 'weapplink' && extraData.linkType === MY_WEAPP_TYPE) {
//     url = extraData.myWeappLink;
//     if (url[0] !== '/') {
//       url = '/' + url;
//     }
//     const urlWithNoParam = url.split('?')[0];
//     if (APPJSON_TAB_BAR_LIST.indexOf(urlWithNoParam) > -1) {
//       isTab = true;
//     }
//     // 移除小程序内跳转移除shopAutoEnter，避免推广链接携带进店参数额外触发进店逻辑
//     url = Args.remove(url, 'shopAutoEnter');
//     // 空白页的话，需要移除内部的标志，重新组装URL
//     if (url.indexOf('/pages/common/blank-page/index') === 0) {
//       const args = Args.getAll(url);
//       if (args.weappSharePath) {
//         let weappSharePath = decodeURIComponent(args.weappSharePath);
//         weappSharePath = Args.remove(weappSharePath, 'shopAutoEnter');
//         // args.weappSharePath = encodeURIComponent(weappSharePath);
//         // Args 内部会 encodeURIComponent
//         args.weappSharePath = weappSharePath;
//         url = Args.add('/pages/common/blank-page/index', args);
//       }
//     }

//     if (!isEmpty(extra) && !isTab) {
//       // 此处冗余信息过多
//       const { banner_id } = extra;
//       url = Args.add(url, { banner_id });
//     }

//     tee
//       .navigate({
//         url,
//       })
//       .catch(() => {
//         wx.reLaunch({
//           url,
//         });
//       });
//   } else if (type === 'weapplink' && extraData.linkType === OTHER_WEAPP_TYPE) {
//     if (+extraData.useShortLink || +extraData.use_short_link) {
//       tee.$native.navigateToMiniProgram({
//         shortLink: extraData.shortLink || extraData.short_link,
//       });
//     } else {
//       tee.$native.navigateToMiniProgram({
//         appId: extraData.otherWeappAppid || extraData.other_weapp_appid,
//         path: extraData.otherWeappLink || extraData.other_weapp_link,
//       });
//     }
//   } else if (type === 'calendar_checkin') {
//     dmc.navigate('Checkin', { ...webkdtIdOpt, ...bannerIdOpt });
//   } else if (type === 'zodiac') {
//     dmc.navigate('LuckyLottery', { alias, ...bannerIdOpt });
//   } else if (type === 'shopnote_detail') {
//     dmc.navigate('ShopnoteDetail', { noteAlias: alias, ...bannerIdOpt });
//   } else if (type === 'mp_article') {
//     dmc.navigate('MpArticle', { noteAlias: alias, ...bannerIdOpt });
//   } else if (
//     type === 'hotellist' ||
//     type === 'recharge_center' ||
//     type === 'point_coupon' ||
//     type === 'room_typelist' ||
//     type === 'combolist'
//   ) {
//     dmc.navigate('CommonWebview', { src: linkUrl, ...bannerIdOpt });
//   } else if (type === 'period_list') {
//     dmc.navigate('PeriodBuyList', { ...webkdtIdOpt, ...bannerIdOpt });
//   } else if (type === 'point_store') {
//     dmc.navigate('PointGoodsList', { ...webkdtIdOpt, ...bannerIdOpt });
//   } else if (
//     type === 'groupon' ||
//     type === 'collector_card' ||
//     type === 'gift_card'
//   ) {
//     // 小程序也走 linkUrl
//     if (linkUrl) {
//       tee.navigate({ url: linkUrl });
//     }
//   } else if (type === 'weapp_marketing_page' || type === 'marketing_page') {
//     dmc.navigate('MarketingPage', {
//       ...webkdtIdOpt,
//       ...bannerIdOpt,
//       id: extraData.id || '',
//     });
//   } else if (type === 'survey' || type === 'guaguale' || type === 'history') {
//     // 小程序没有
//     webAndWeappJump('');
//   } else if (type === 'video_number') {
//     const channelsLiveInfo =
//       tee.$native.getStorageSync('channelsLiveInfo') || '';
//     if (!channelsLiveInfo) return;
//     const { feedId, nonceId } = JSON.parse(channelsLiveInfo);
//     tee.$native.openChannelsLive &&
//       tee.$native.openChannelsLive({
//         feedId,
//         nonceId,
//         finderUserName: linkId,
//       });
//   } else if (type === 'social_fans') {
//     const { qrcode } = item;
//     const h5QrCodeUrl = buildUrl(
//       Args.add('/v3/message/live-qrcode/member', {
//         kdtId,
//         activitiesId: qrcode,
//       }),
//       'h5',
//       kdtId
//     );
//     tee.$native.navigateTo({
//       url: `/pages/common/webview-page/index?src=${encodeURIComponent(
//         h5QrCodeUrl
//       )}`,
//     });
//   } else if (type === 'weapp_web_link') {
//     webAndWeappJump(Args.add(linkUrl));
//   } else if (type === 'red-package') {
//     tee.$native.showRedPackage({
//       url: linkUrl,
//     });
//     return {};
//   } else if (type === 'member_code') {
//     tee.$native.navigateTo({
//       url: '/packages/member-code/index',
//     });
//   } else if (type === 'vipcenter') {
//     tee.$native.navigateTo({
//       url: '/packages/shop/levelcenter/free/index',
//     });
//   } else if (type === 'pay_vipcenter') {
//     tee.$native.navigateTo({
//       url: '/packages/shop/levelcenter/plus/index',
//     });
//   } else if (type === 'shop_ranking_list') {
//     const { tabType } = item;
//     if (tabType) {
//       const shopRankingListUrl = buildUrl(
//         Args.add('/wscshop/showcase/shop-ranking-list', {
//           type: tabType,
//         }),
//         'h5',
//         kdtId
//       );

//       tee.$native.navigateTo({
//         url: `/pages/common/webview-page/index?src=${encodeURIComponent(
//           shopRankingListUrl
//         )}`,
//       });
//     }
//   } else if (type === 'video_number_dynamic') {
//     const {
//       videoDynamicParams: { type: _type, ...channelParam },
//     } = item;
//     videoDynamicClickEvent(item);
//     tee.$native.openChannelsActivity &&
//       tee.$native.openChannelsActivity({
//         ...channelParam,
//         success: () => {
//           videoDynamicJumpSucc(item);
//         },
//         fail: (error) => {
//           videoDynamicJumpFail(item, error);
//         },
//       });
//   } else if (type === 'storelist' || type === 'nearby_store_way') {
//     tee.$native.navigateTo({
//       url: '/packages/shop/physical-store/index',
//     });
//   } else if (type === 'goods_classify') {
//     dmc.navigate('CommonWebview', { src: linkUrl, ...bannerIdOpt });
//   } else if (Object.values(SHELF_LINK_TYPE_ENUM).includes(type)) {
//     const { context } = extra;
//     retailShelfNavigator(item, context, { alias, ...extraData });
//   } else if (['booking_mall', 'express_mall'].includes(type)) {
//     request({
//       method: 'GET',
//       path: 'wscdeco/decorate-api/getMallFeatureByType.json',
//       data: {
//         type,
//       },
//     }).then((res) => {
//       const { alias } = res;
//       if (alias) {
//         dmc.navigate('Feature', { alias });
//       } else {
//         Toast('未启用相关微页面');
//       }
//     });
//   }
// };

export { jumpToLink } from '@youzan/decorate-tee/src/common/utils/jump-link/util.js';
