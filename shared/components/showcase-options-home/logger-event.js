import { getLogger } from '@youzan/tee-logger';

/**
 * 获取视频号动态埋点参数
 * @param {Object} item item 格式 { alias, link_title, link_id, extra_data }
 */
export const getVideoDynamicLogParams = (item) => {
  const { videoDynamicParams, linkTitle } = item;
  const loggerParams = {
    ...videoDynamicParams,
    image_url: item?.imageUrl,
    link_title: linkTitle,
  };
  return loggerParams;
};

export const videoDynamicClickEvent = (item) => {
  const loggerParams = getVideoDynamicLogParams(item);
  getLogger().log({
    et: 'click',
    ei: 'wxvideo_video_click',
    en: '视频号动态点击',
    params: loggerParams,
  });
};

export const videoDynamicJumpSucc = (item) => {
  const loggerParams = getVideoDynamicLogParams(item);
  getLogger().log({
    et: 'custom',
    ei: 'wxvideo_video_jump_success',
    en: '视频号动态跳转成功',
    params: loggerParams,
  });
};

export const videoDynamicJumpFail = (item, error) => {
  const loggerParams = getVideoDynamicLogParams(item);
  getLogger().log({
    et: 'custom',
    ei: 'wxvideo_video_jump_fail',
    en: '视频号动态跳转失败',
    params: {
      ...loggerParams,
      error_msg: error?.errMsg,
    },
  });
};
