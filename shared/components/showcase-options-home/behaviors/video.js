import tee from '@youzan/tee';
import cloneDeep from '@youzan/utils/object/clone-deep/index';

const { windowHeight } = wx.getSystemInfoSync();
const VideoPlayThreshold = 300;
export default Behavior({
  attached() {
    this.initVideoData();
    this.onNetwork();
  },

  detached() {
    this.offNetwork();
  },

  methods: {
    initVideoData() {
      this.videoIndex = 0;
      this.videoData = [];
      this.videoContext = [];
      this.realVideoContext = [];
      this.networkCanPlay = false;
      this.videoReady = false;
      this.isPullRefres = false;
      this.scrollTop = 0;
      this.manualPlayId = '';
    },
    resetVideo() {
      this.videoIndex = 0;
      this.videoData = [];
      this.videoContext = [];
    },

    offNetwork() {
      tee.$native.offNetworkStatusChange &&
        tee.$native.offNetworkStatusChange();
    },

    onNetwork() {
      tee.$native.onNetworkStatusChange &&
        tee.$native.onNetworkStatusChange((res) => {
          const { networkType } = res;
          this.networkCanPlay = this.checkNetworkIsPlay(networkType);
        });
      tee.$native.getNetworkType &&
        tee.$native.getNetworkType({
          success: (res) => {
            const { networkType } = res;
            this.networkCanPlay = this.checkNetworkIsPlay(networkType);
          },
        });
    },

    checkNetworkIsPlay(networkType) {
      if (
        networkType === '5g' ||
        networkType === '4g' ||
        networkType === 'wifi'
      )
        return true;
      return false;
    },

    // 监听播放事件， 如果手动播放则需要切换视频索引到当前
    handlePlay(e = {}) {
      const { currentTarget = {} } = e.detail.payload;
      const { id } = currentTarget;
      const { uuid } = this.realVideoContext[this.videoIndex];
      if (uuid === id) return;
      const playVideoIndex = this.realVideoContext.findIndex(
        (el) => el.uuid === id
      );
      if (playVideoIndex !== -1) {
        const { uuid, autoPlay } = this.realVideoContext[playVideoIndex];
        this.videoIndex = playVideoIndex;
        // 如果非自动播放且手动触发需要记录标识
        if (autoPlay === 2) {
          this.manualPlayId = uuid;
        }
      }
    },

    handleEnded(e) {
      this.resetManualPlayId(e.detail.payload);
    },

    handlePause(e) {
      this.resetManualPlayId(e.detail.payload);
    },

    resetManualPlayId(e) {
      const { currentTarget = {} } = e;
      const { id } = currentTarget;
      if (id === this.manualPlayId) {
        this.manualPlayId = '';
      }
    },

    handleVideoContext(event) {
      const context = event.detail.payload || {};
      const { uuid } = context;
      if (!this.videoContext.some((el) => el.uuid === uuid)) {
        this.videoContext.push(context);
        this.assembleVideoData();
      } else {
        this.assembleVideoData();
      }
    },

    assembleVideoData() {
      // this.isPullRefresh &&
      if (
        this.videoContext.length === this.videoData.length ||
        !this.compareVideoData(this.videoData, this.realVideoContext)
      ) {
        this.realVideoContext = cloneDeep(this.videoData);
        this.realVideoContext.forEach((el) => {
          const findVideo = this.videoContext.find((i) => i.uuid === el.uuid);
          if (findVideo) {
            el.videoContext = findVideo.videoContext;
          }
        });
        this.currentViewVideo();
        this.videoReady = true;
        if (this.isPullRefresh) {
          this.isPullRefresh = false;
        }
      } else {
        this.videoReady = false;
      }
    },

    compareVideoData(newData, oldData) {
      const { length } = newData;
      for (let i = 0; i < length; i++) {
        const { uuid } = newData[i];
        if (oldData.findIndex((el) => el.uuid === uuid) === -1) return false;
      }
      return true;
    },

    // 第一屏内如果存在自动播放的视频，需要自动播放
    currentViewVideo() {
      const currentVideo = this.realVideoContext
        .slice(0, 3)
        .find((i) => i.autoPlay === 1);
      if (!currentVideo || !currentVideo.videoContext) return;
      const currentVideoClass = '.position_component_' + currentVideo.uuid;

      const query = this.createSelectorQuery();
      query.select(currentVideoClass).boundingClientRect();
      query.exec((res) => {
        const { videoContext, autoPlay } = currentVideo;
        const { top } = res[0];
        if (
          windowHeight - top <= windowHeight &&
          autoPlay === 1 &&
          this.networkCanPlay
        ) {
          setTimeout(() => {
            videoContext.play();
          }, VideoPlayThreshold);
        }
      });
    },

    videoAutoPlay(e) {
      if (!this.videoReady) return;
      const query = this.createSelectorQuery();
      const className =
        '.position_component_' + this.realVideoContext[this.videoIndex].uuid;
      query.select(className).boundingClientRect();
      query.exec((res) => {
        const { videoContext, autoPlay } =
          this.realVideoContext[this.videoIndex] || {};
        const { top, bottom } = res[0];
        // 视频底部距离顶部高度 <= 0，说明是上滑且滑出屏幕， 获取下一个视频的实列
        if (bottom <= 0) {
          videoContext.pause();
          if (this.videoIndex < this.realVideoContext.length - 1) {
            this.videoIndex++;
            this.manualPlayId = '';
          }
          return;
        }
        // 窗口高度—视频顶部距离窗口高度 <= 0 说明是下滑且滑出屏幕， 获取上一个视频的实列
        if (top > 0 && windowHeight - top <= 0) {
          videoContext.pause();
          if (this.videoIndex > 0) {
            this.videoIndex--;
            this.manualPlayId = '';
          }
          return;
        }
        // 视频在可视区内 且 处于4g/5g/wifi环境
        if (
          (bottom > 0 || windowHeight - top <= windowHeight) &&
          this.networkCanPlay
        ) {
          if (autoPlay === 1) {
            setTimeout(() => {
              videoContext.play();
            }, VideoPlayThreshold);
          } else {
            // 如果当前为非自动播放且不是手动触发播放 ，当上滑时取下一个视频索引 下滑时取上一个视频索引
            if (this.manualPlayId) return;
            // 上滑
            if (
              e.scrollTop > this.scrollTop &&
              this.videoIndex < this.realVideoContext.length - 1
            ) {
              this.videoIndex++;
            }
            // 下滑
            if (e.scrollTop < this.scrollTop && this.videoIndex > 0) {
              this.videoIndex--;
            }
          }
          this.scrollTop = e.scrollTop;
        }
      });
    },
  },
});
