import StickyControl from '@youzan/decorate-tee/native/weapp/common/utils/sticky-control';
import { pageScrollTo, getSystemInfoSync } from '@youzan/tee-api';
import TeeEvent from '@youzan/tee-event';

const { windowHeight } = getSystemInfoSync();

export default Behavior({
  attached() {},

  methods: {
    handleElevatorNavigationClick(e) {
      if (this.elevatorNavigationTimeOut) {
        clearTimeout(this.elevatorNavigationTimeOut);
      }
      const activeSubEntry = e.detail.payload;
      // 程序只有在外层才能拿到需要跳转的位置， 只有图文型支持跳转
      this.elevatorNavigationTabClick = true;
      const query = this.createSelectorQuery();
      query.selectViewport().scrollOffset();
      query
        .select(`.position_component_${activeSubEntry.position_component}`)
        .boundingClientRect()
        .exec((res) => {
          if (!res || res.length < 2) return;

          const [scrollEle, targetEle] = res;
          if (!scrollEle || !targetEle) return;

          // 计算即将置顶的高度， 比如说电梯组件未置顶，滚动后会置顶，搜索滚动后会置顶，要计算这部分高度
          // 如果已经置顶，下划时可不考虑，上划消失类型的需要考虑高度计算
          let willFixedHeight = StickyControl.getHasTop();
          // 判断即将上划，还是下划
          const isToBottom = targetEle.top > scrollEle.scrollTop;
          const willStickyComponent = StickyControl.getWillStickyComponent();
          const hasStickyComponent = StickyControl.__map;
          const bodyHeight = scrollEle.scrollHeight;
          // 如果是要到底部
          if (isToBottom) {
            // 如果是往下滑动， 查看 willStickyComponent 的高度，或者已经置顶
            willStickyComponent.forEach((item) => {
              // 如果下滑消失的
              if (item.type === 'canChange') {
                return;
              }
              if (!hasStickyComponent[item.name]) {
                try {
                  // 判断滚动后是否会置顶
                  if (
                    bodyHeight - item.positionTop >= windowHeight &&
                    targetEle.top + scrollEle.scrollTop > item.positionTop
                  ) {
                    willFixedHeight += item.elementHeight;
                  }
                } catch (error) {}
              }
            });
          } else {
            // 如果要去顶部
            willStickyComponent.forEach((item) => {
              // 如果下滑消失的
              if (item.type === 'canChange') {
                return;
              }
              if (hasStickyComponent[item.name]) {
                try {
                  // 会脱离置顶的组件
                  if (targetEle.top + scrollEle.scrollTop <= item.positionTop) {
                    willFixedHeight -= item.elementHeight;
                  }
                } catch (error) {}
              }
            });
          }

          const shouldPosition =
            targetEle.top - willFixedHeight + scrollEle.scrollTop;
          // 如果存在搜索，则需要加上搜索的高度，当快速滑动的时候，搜索并未置顶

          try {
            pageScrollTo({
              scrollTop: shouldPosition,
              duration: 300,
            }).then(() => {
              this.elevatorNavigationTimeOut = setTimeout(() => {
                this.elevatorNavigationTabClick = false;
              }, 1000);
            });
          } catch {}
        });
    },

    onPageScrollSetElevatorNavigationPosition() {
      if (this.elevatorNavigationTabClick) {
        return;
      }
      const elevatorNavigation =
        (this.data.curComponents || []).find(
          (i) => i.type === 'elevator_navigation'
        ) || {};
      const query = this.createSelectorQuery();
      // 获取自定义模块结尾处的uuid

      (this.data.curComponents || []).map((i) => {
        if (i.uuid && i.uuid.startsWith('custom_end_')) {
          query.select(`.${i.uuid}`).boundingClientRect();
        }
      });

      (elevatorNavigation.tabs || []).forEach((item) => {
        if (
          item.position_component &&
          !(item.use_link && elevatorNavigation.showMethod == 2)
        ) {
          query
            .select(`.position_component_${item.position_component}`)
            .boundingClientRect();
        }
      });

      query.exec(function (res) {
        const endArr = res.filter((i) => i && i.id.startsWith('custom_end_'));
        const endMap = endArr.reduce((pre, cur) => {
          return { ...pre, [cur.id.replace('custom_end_', '')]: cur.bottom };
        }, {});
        const scrollArr = res
          .filter((i) => i && !i.id.startsWith('custom_end_'))
          .map((v) => {
            if (v && endMap[v.id]) {
              v.bottom = endMap[v.id];
            }

            return v;
          });

        const elementArr = scrollArr.sort((a, b) => {
          return a.top - b.top;
        });
        let activeEl = null;
        const stickTop = StickyControl.getHasTop();
        for (let i = elementArr.length - 1; i >= 0; i--) {
          if (elementArr[i].top < stickTop) {
            activeEl = elementArr[i];
            break;
          }
        }
        if (!activeEl) return;

        const activeId = activeEl.id.replace('position_component_', '');
        const index = (elevatorNavigation.tabs || []).findIndex(
          (item) => item.position_component === activeId
        );
        if (index === -1) return;
        TeeEvent.trigger(
          'showcase-container:elevator-navigation-active-index',
          index,
          activeId
        );
      });
    },
  },
});
