import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    businessModule: {
      type: String,
      value: ''
    }
  },
  methods: {
    onButtonTap(e) {
      let formId = e.detail.formId || '';
      this.createFormId({
        weapp_type: 'custom',
        form_id: formId,
        business_module: this.data.businessModule
      });
    },
    createFormId(data, success, fail) {
      getApp().carmen({
        api: 'wsc.weapp.formid/1.0.0/add',
        data,
        method: 'GET',
        success: (res) => {
          success && success(res);
        },
        fail: (res) => {
          fail && fail(res.msg, res);
        }
      });
    }
  }
});
