import { getStartTime } from './time';

/**
 * 获取直播状态
 * state = 0 编辑中
 * state = 5 待审核
 * state = 10 待播放
 * state = 15 播放中
 * state = 20 暂停中
 * state = 25 禁播
 * state = 30 待播失效
 * state = 35 播放失效
 * state = 40 结束
 * state = 255 删除
 */
export function getLiveStatus({ state, startedAt, memberCount }) {
  if (state <= 10) {
    let statusText = '待直播';
    if (startedAt) {
      statusText += ` | ${getStartTime(startedAt)}`;
    }

    return {
      statusText,
      statusType: 'unstarted'
    };
  }

  if (state <= 20) {
    let statusText = '直播中';
    if (memberCount) {
      statusText += ` | ${memberCount}观看`;
    }

    return {
      statusText,
      statusType: 'playing'
    };
  }

  return {
    statusText: '直播结束',
    statusType: 'finished'
  };
}
