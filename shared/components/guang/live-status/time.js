import { moment } from '@youzan/weapp-utils/lib/time';

function isSameDay(date1, date2) {
  const methods = ['getFullYear', 'getMonth', 'getDate'];
  return methods.every(method => date1[method]() === date2[method]());
}

function getDay(offset = 0) {
  const now = new Date();
  return new Date(now.getFullYear(), now.getMonth(), now.getDate() + offset);
}

// 根据 Date 返回 今天/明天/后天/具体时间
export function getStartTime(date) {
  date = new Date(date);
  const time = moment(date, 'hh:mm');
  let day = '';

  if (isSameDay(getDay(), date)) {
    day = '今天';
  } else if (isSameDay(getDay(1), date)) {
    day = '明天';
  } else if (isSameDay(getDay(2), date)) {
    day = '后天';
  } else {
    day = moment(date, 'YYYY-MM-DD');
  }

  return `${day} ${time}`;
}
