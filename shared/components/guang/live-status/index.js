import { getLiveStatus } from './utils';

Component({
  externalClasses: ['custom-class'],

  properties: {
    state: {
      type: Number,
      observer: 'setState'
    },
    startedAt: Number,
    memberCount: Number
  },

  attached() {
    this.setState();
  },

  methods: {
    setState() {
      this.setData(getLiveStatus({
        state: this.data.state,
        startedAt: this.data.startedAt,
        memberCount: this.data.memberCount
      }));
    }
  }
});
