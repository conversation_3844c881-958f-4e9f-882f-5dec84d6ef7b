import { node } from 'shared/utils/request';
import WscComponent from 'shared/common/base/wsc-component';
import appLogger<PERSON>ehavior from 'shared/components/showcase/behaviors/app-logger-behavior';

WscComponent({
  behaviors: [appLoggerBehavior],

  properties: {
    bizName: {
      type: String,
    },
    loadMore: {
      type: Boolean,
    },
  },

  data: {
    isOpen: false,
  },

  ready() {
    const { bizName } = this.data;
    let path = '/wsctrade/recommend-config.json';

    if (bizName === 'cs~mg') {
      // 日历签到请求 ump
      path = '/wscump/recommend-config.json';
    } else if (bizName === 'uc~mg') {
      path = '/wscuser/scrm/api/benefitcard/recommend-config.json';
    }

    node({
      method: 'POST',
      path,
      data: {
        bizName,
      },
    })
      .then((res = {}) => {
        const { isOpen = false } = res;
        this.setYZData({
          isOpen,
        });
      })
      .catch(() => {
        // do nothing
      });
  },

  methods: {
    onAfterload({ detail }) {
      this.isFetching = false;
      this.triggerEvent('afterload', detail.recommendList);
    },

    refresh() {
      if (this.isFetching) {
        return;
      }

      const recommendGoods = this.selectComponent('#recommend');
      recommendGoods && recommendGoods.fetchList && recommendGoods.fetchList();

      this.isFetching = true;
    },
  },
});
