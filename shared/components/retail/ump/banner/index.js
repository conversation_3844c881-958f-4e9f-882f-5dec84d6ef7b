const app = getApp();

Component({
  properties: {
    show: <PERSON><PERSON><PERSON>,
    coupons: {
      type: <PERSON><PERSON>y,
      observer(value) {
        console.log('coupons value: ', value);
        this.initCoupons();
      },
    },
    shopName: String,
    notice: String,
  },

  data: {
    tags: [],
    groups: [],
    vouchers: [],
    showPop: false,
    showMoreText: false,
  },

  lifetimes: {
    attached() {
      console.log('attached.');
      this.initActivities();
    },
  },

  methods: {
    initCoupons() {
      console.log('coupons: ', this.data.coupons);

      const voucherIds = this.data.coupons.map((coupon) => +coupon.id);

      if (voucherIds.length > 0) {
        app
          .request({
            path: '/retail/h5/ump/voucher.json',
            data: {
              ids: voucherIds,
            },
          })
          .then((res) => {
            this.setData({
              vouchers: res,
            });
          });
      }
    },

    initActivities() {
      app
        .request({
          path: '/retail/h5/ump/activities.json',
        })
        .then((res) => {
          const { allPreferentialTag = [], groups = [] } = res;

          this.setData({
            tags: allPreferentialTag.slice(0, 3), // 最多展示 3 个活动（不然不够宽）
            showMoreText: allPreferentialTag.length > 3,
            groups,
          });

          this.triggerEvent('onSyncUmpData');
        });
    },

    closePop() {
      this.setData({ showPop: false });
    },

    togglePop() {
      this.setData({
        showPop: !this.data.showPop,
      });
    },
  },
});
