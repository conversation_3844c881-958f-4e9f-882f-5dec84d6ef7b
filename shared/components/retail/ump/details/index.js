const app = getApp();

Component({
  properties: {
    showState: {
      type: <PERSON><PERSON><PERSON>,
      observer(value) {
        // 关闭的时候需要将优惠券的展示状态重置
        if (!value) {
          this.setData({
            showMoreVouchers: false,
          });
        }
      },
    },
    activities: {
      type: Array,
    },
    vouchers: {
      type: <PERSON><PERSON><PERSON>,
      observer(value) {
        this.setData({
          displayVouchers: value,
        });
      },
    },
  },

  data: {
    showMoreVouchers: false,
    displayVouchers: [],
  },

  methods: {
    // 领取优惠券
    takeCoupon(evt) {
      const { id } = evt.currentTarget.dataset;

      app
        .request({
          path: '/wscshop/ump/coupon/fetchCoupon.json',
          method: 'POST',
          data: {
            source: 'retail_minapp_shelf',
            groupId: id,
          },
        })
        .then(() => {
          wx.showToast({
            icon: 'none',
            title: '领取成功',
          });

          // 领取成功后再查询一下
          return app.request({
            path: '/retail/h5/ump/voucher.json',
            data: {
              ids: [id],
            },
          });
        })
        .then((res) => {
          const replaceVoucher = res?.[0];

          this.setData({
            displayVouchers: this.data.displayVouchers.map((voucher) => {
              if (voucher.activityId === +id) {
                return { ...voucher, ...replaceVoucher };
              }

              return voucher;
            }),
          });
        })
        .catch((err) => {
          wx.showToast({
            icon: 'none',
            title: err.msg || '领取失败',
          });
        });
    },
    // 展示更多优惠券
    toggleMoreVoucher() {
      this.setData({
        showMoreVouchers: !this.data.showMoreVouchers,
      });
    },
  },
});
