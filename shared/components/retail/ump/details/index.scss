.ump-block {
  display: flex;
  flex-direction: column;
  margin-top: 32rpx;

  &__title {
    font-size: 28rpx;
    color: #313133;
    font-weight: bold;
    margin-bottom: 16rpx;
  }

  &__content {
    font-size: 24rpx;
    color: #636566;
  }

  &__more {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #969799;
    font-size: 28rpx;
    margin: 16rpx 0 0;

    &--text {
      margin-right: 8rpx;
    }
  }
}

.ump-activity {
  display: flex;
  flex-direction: row;

  &:not(:last-child) {
    margin-bottom: 16rpx;
  }

  &__tag {
    display: flex;
    justify-content: center;
    box-sizing: border-box;
    width: 88rpx;
    margin-top: 4rpx;
    border-color: #ee0a2499;
  }

  &__content {
    display: flex;
    flex-direction: column;
    font-size: 24rpx;
    line-height: 36rpx;
    color: #636566;
    margin-left: 16rpx;
    margin-top: 2rpx;
  }
}

// 优惠券
.ump-coupon {
  display: flex;
  background-color: #fee9b6;
  border-radius: 8rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  overflow: hidden;

  &__left,
  &__mid,
  &__right {
    display: flex;
    flex-direction: column;
  }

  &__left {
    width: 30%;

    .condition-price,
    .condition-unit {
      color: #794919;
    }

    .condition-price {
      font-family: Avenir, sans-serif;
      font-weight: bolder;
      font-size: 60rpx;

      &.medium {
        font-size: 48rpx;
      }
    }

    .condition-unit {
      margin-left: 4rpx;
      font-size: 24rpx;
    }

    .condition-text {
      margin-top: 12rpx;
      font-size: 24rpx;
      color: #dd9e5d;
    }
  }

  &__mid {
    width: 45%;
    justify-content: flex-end;

    .coupon-name {
      transform: translateY(-10rpx);
      font-size: 28rpx;
      color: #313133;
      font-weight: bold;
    }

    .coupon-date {
      margin-top: 24rpx;
      font-size: 24rpx;
      color: #323233;
    }
  }

  &__right {
    position: relative;
    width: 25%;
    align-items: flex-end;
    justify-content: center;
  }

  .coupon-btn {
    padding: 4rpx 12rpx;
    color: white;
    border-radius: 32rpx;
    text-align: center;
    background: linear-gradient(-45deg, #a15c15, #efa961);
  }

  .coupon-mark {
    position: absolute;
    bottom: -54rpx;
    right: -54rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    width: 88rpx;
    border: 1px solid rgba(121, 73, 25, 0.4);
    border-radius: 50%;
    transform: rotate(-30deg);

    &__inner {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 72rpx;
      height: 72rpx;
      background-color: rgba(121, 73, 25, 0.06);
      border: 1rpx dotted rgba(121, 73, 25, 0.64);
      text-align: center;
      border-radius: 50%;
    }

    &__text {
      color: rgba(122, 74, 25, 0.5);
      font-size: 20rpx;
    }
  }
}
