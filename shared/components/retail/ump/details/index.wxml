<view class="ump-block" wx:if="{{ vouchers && vouchers.length > 0 }}">
  <text class="ump-block__title">
    优惠券
  </text>
  <view class="ump-block__content">
    <template
      is="voucher"
      wx:for="{{ util.slice(displayVouchers, 0, 2) }}"
      wx:key="activityId"
      wx:for-item="voucher"
      data="{{ voucher }}"
    />
    <template
      wx:if="{{ showMoreVouchers }}"
      is="voucher"
      wx:for="{{ util.slice(displayVouchers, 2, displayVouchers.length) }}"
      wx:key="activityId"
      wx:for-item="voucher"
      data="{{ voucher }}"
    />
    <view
      class="ump-block__more"
      wx:if="{{ displayVouchers.length - 2 > 0 && !showMoreVouchers }}"
      bind:tap="toggleMoreVoucher"
    >
      <text class="ump-block__more--text">
        展开剩余{{ displayVouchers.length - 2 }}张优惠券
      </text>
      <van-icon name="arrow-down" />
    </view>
  </view>
</view>

<view class="ump-block" wx:if="{{ activities && activities.length > 0 }}">
  <text class="ump-block__title">
    优惠活动
  </text>
  <view class="ump-block__content">
    <view
      class="ump-activity"
      wx:for="{{ activities }}"
      wx:for-item="activity"
      wx:key="activityId"
    >
      <van-tag custom-class="ump-activity__tag" plain type="danger">
        {{ activity.groupTagName }}
      </van-tag>
      <view class="ump-activity__content">
        <text
          wx:for="{{ activity.briefs }}"
          wx:for-item="activityItem"
          wx:key="*this"
        >
          {{ activityItem }}
        </text>
      </view>
    </view>
  </view>
</view>

<template name="voucher">
  <view class="ump-coupon">
    <view class="ump-coupon__left">
      <view>
        <text class="condition-price {{ voucher.valueCopywriting.length > 4 ? 'medium' : '' }}">{{ voucher.valueCopywriting }}</text>
        <text class="condition-unit">{{ voucher.unitCopywriting }}</text>
      </view>
      <text class="condition-text">{{ voucher.useThresholdCopywriting }}</text>
    </view>
    <view class="ump-coupon__mid">
      <text class="coupon-name">{{ voucher.title }}</text>
      <text class="coupon-date">{{ util.removeSpace(voucher.validTimeCopywriting) }}</text>
    </view>
    <view class="ump-coupon__right">
      <view class="coupon-btn" data-id="{{ voucher.activityId }}" bind:tap="takeCoupon">
        {{ voucher.hasTaken ? '继续领取' : '立即领取' }}
      </view>
      <view class="coupon-mark" wx:if="{{ voucher.hasTaken }}">
        <view class="coupon-mark__inner">
          <text class="coupon-mark__text">已领</text>
        </view>
      </view>
    </view>
  </view>
</template>

<wxs module="util">
  module.exports.removeSpace = function(str) {
    return str.replace(getRegExp('\s', 'g'), '')
  }
  module.exports.slice = function(arr, start, end) {
    return arr.slice(start, end);
  }
</wxs>
