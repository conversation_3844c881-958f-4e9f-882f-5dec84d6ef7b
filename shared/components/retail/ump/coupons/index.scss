
@mixin beveled-corners-right($bg) {
  //实现效果不是很完美，有一些锯齿
  background: $bg;
  background: radial-gradient(circle at top right, transparent 3px, $bg 0) top right,
              radial-gradient(circle at bottom right, transparent 3px, $bg 0) bottom right;
  background-size: 100% 50%;
  background-repeat: no-repeat;
  border-radius: 2px;
}
@mixin beveled-corners-left($bg) {
  background: $bg;
  background:  radial-gradient(circle at top left, transparent 2px, $bg 0) top left,
  radial-gradient(circle at bottom left, transparent 2px, $bg 0) bottom left;
  background-size: 100% 50%;
  background-repeat: no-repeat;
  border-radius: 2px;
}

.coupon {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  height: 48rpx;
  margin-top: 16rpx;
  margin-bottom: 16rpx;

  &-item {
    display: flex;
    position: relative;
    overflow: hidden;
    align-items: center;
    height: 48rpx;
    min-width: 286rpx;
    margin-right: 20rpx;
    border-radius: 2px;
    color: #7a4a19;
    font-size: 22rpx;
    line-height: 48rpx;
    text-align: center;


    &__value {
      width: 212rpx;
      box-sizing: border-box;
      font-size: 28rpx;
      font-weight: 500;
      padding-left: 16rpx;
      text-align: left;
      @include beveled-corners-right(#ffe9b7);
      &-hint {
        font-size: 20rpx;
      }

      &-yen {
        font-size: 20rpx;
        margin-left: -2rpx;
      }

      &-threshold {
        font-weight: normal;
        font-size: 20rpx;
        margin-left: 8rpx;
      }
    }

    &__value,
    &__type {
      flex-grow: 1;
    }

    &__type {
      padding: 0 4rpx;
    }

    &__opt {
      flex-shrink: 0;
      width: 68rpx;
      font-size: 20rpx;
      @include beveled-corners-left(#ffe9b7);
    }
  }
}
