import { moment } from '@youzan/weapp-utils/lib/time';

Component({
  properties: {
    couponData: {
      type: <PERSON><PERSON>y,
      observer(data) {
        this.getDisplayCoupons(data);
      },
    },
  },

  data: {
    expanded: false,
    displayCoupons: [],
  },

  methods: {
    togglePopup() {
      this.setData({ expanded: !this.data.expanded });
    },

    /**
     * 获取优惠券可用时间字符串
     *
     * @param {number} dateType
     * @param {number} validStartTime
     * @param {number} validEndTime
     * @param {number} fixedBeginTerm
     * @param {number} fixedTerm
     *
     * @returns {string}
     */
    getCouponTimeRange(
      dateType,
      validStartTime,
      validEndTime,
      fixedBeginTerm,
      fixedTerm
    ) {
      if (dateType === 1) {
        return `${moment(validStartTime, 'YYYY.MM.DD')}-${moment(
          validEndTime,
          'YYYY.MM.DD'
        )}`;
      }

      if (dateType === 2) {
        return `领券${fixedBeginTerm ? '次' : '当'}日起${fixedTerm}天内可用`;
      }

      return '';
    },

    /**
     * 处理优惠券展示数据
     *
     * @param {*} data
     */
    getDisplayCoupons(data) {
      const displayCoupons = data.map((coupon) => {
        const {
          dateType,
          fixedTerm,
          fixedBeginTerm,
          denominations,
          discount,
          validEndTime,
          validStartTime,
          condition,
          ...rest
        } = coupon;

        return {
          ...rest,
          acquired: false,
          disabled: false,
          threshold: condition ? `满${condition / 100}元使用` : '无门槛',
          priceValue: discount ? discount / 10 : denominations / 100,
          discount,
          denominations,
          timeRange: this.getCouponTimeRange(
            dateType,
            validStartTime,
            validEndTime,
            fixedBeginTerm,
            fixedTerm
          ),
        };
      });

      this.setData({ displayCoupons });
    },
  },
});
