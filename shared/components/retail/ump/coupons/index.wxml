<view class="coupon" wx:if="{{ displayCoupons.length > 0 }}">
  <view
    class="coupon-item"
    wx:for="{{ displayCoupons }}"
    wx:for-item="coupon"
    wx:key="id"
  >
    <view class="coupon-item__value">
      <text wx:if="{{ coupon.discount }}">
        {{ coupon.priceValue }}
        <text class="coupon-item__value-hint">折</text>
      </text>
      <block wx:else>
        <text class="coupon-item__value-yen">¥</text>
        <text style="font-family: Avenir;">
          {{ coupon.priceValue }}
        </text>
      </block>
      <text
        class="coupon-item__value-hint"
        wx:if="{{ coupon.valueRandomTo }}"
      >
        起
      </text>
      <text class="coupon-item__value-threshold">
        {{ coupon.threshold }}
      </text>
    </view>
    <view class="coupon-item__opt">领取</view>
  </view>
</view>
