Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
      observer(v) {
        this.setData({
          showState: v,
        });
      },
    },
    activities: {
      type: Array,
      value: [],
    },
    vouchers: {
      type: Array,
      value: [],
    },
    shopName: String,
    notice: String,
  },

  data: {
    showState: false,
  },

  methods: {
    onClose() {
      this.setData({
        showState: false,
      });

      this.triggerEvent('close-pop');
    },
  },
});
