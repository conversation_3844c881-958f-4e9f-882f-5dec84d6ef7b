.title {
  display: flex;
  justify-content: space-between;
  flex-shrink: 0;
  text-align: center;
  padding: 0 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;

  .title-text {
    font-size: 32rpx;
    font-weight: 500;
    transform: translateX(24rpx);
  }
}

.content {
  box-sizing: border-box;
  padding: 32rpx 32rpx 132rpx 32rpx;
  text-align: left;

  &-block {
    display: flex;
    flex-direction: column;
    margin-top: 32rpx;
  }

  &-title {
    font-size: 28rpx;
    color: #313133;
    font-weight: bold;
    margin-bottom: 16rpx;
  }

  &-body {
    font-size: 24rpx;
    color: #636566;
  }
}
