<van-popup
  show="{{ showState }}"
  round
  position="bottom"
  custom-style="height: 80%"
>
  <view class="title">
    <view></view>
    <text class="title-text">
      {{ shopName }}
    </text>
    <van-icon name="cross" size="24px" bind:click="onClose" />
  </view>

  <scroll-view
    scroll-y
    class="content"
  >
    <ump-details showState="{{ showState }}" activities="{{ activities }}" vouchers="{{ vouchers }}" />
    <view class="content-block" wx:if="{{ notice && notice.length }}">
      <text class="content-title">公告</text>
      <text class="content-body">
        {{ notice }}
      </text>
    </view>
  </scroll-view>
</van-popup>
