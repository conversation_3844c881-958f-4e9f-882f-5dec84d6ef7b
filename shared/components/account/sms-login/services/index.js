import getApp from 'shared/utils/get-safe-app';
import { getAjaxConfig } from '../../_utils';

const app = getApp();
const COUNTRY_CODE_CN = '+86';

const Post = function (options = {}, thisArg) {
  const cfg = getAjaxConfig(thisArg);
  // 合并默认选项
  options = {
    config: {
      skipShopInfo: true,
    },
    method: 'POST',
    header: {
      'content-type': 'application/x-www-form-urlencoded',
      ...cfg.header,
    },
    origin: 'uic',
    data: {},
    success: () => {},
    fail: () => {},
    ...options,
  };
  return app.request(options);
};

/**
 * 获取验证码
 * @param data
 * @param success
 * @param fail
 * @returns {Promise | Promise<unknown>}
 */
function fetchCode(data, success, fail, thisArg) {
  return Post(
    {
      origin: 'uic',
      pathname: '/passport/weapp/login/sms.json',
      path: '/passport/weapp/login/sms.json',
      data: {
        countryCode: COUNTRY_CODE_CN,
        ...data,
        sessionId: app.getSessionId(),
      },
      success: (data) => {
        success(data);
      },
      fail,
    },
    thisArg
  );
}

/**
 * @description 验证码登录
 * @param params 参数
 */
function loginBySms(data, success, fail, thisArg) {
  return Post(
    {
      origin: 'uic',
      pathname: '/passport/login.json',
      path: '/passport/login.json',
      data,
      success: (data) => {
        success(data);
      },
      fail,
    },
    thisArg
  );
}

export default {
  fetchCode,
  loginBySms,
};
