.container {
  padding: 28px 16px 0;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: #fff;
}

.container .title {
  color: #333;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
}

.input {
  caret-color: #07c160 !important;
  font-size: 14px !important;
  line-height: 1em !important;
  padding: 4px 0 !important;
  height: 24px !important;
  background-color: #fff;
}

.section {
  margin-top: 2px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.fetch-captcha-btn {
  color: #586b95;
  font-size: 14px;
  margin-left: 10px;
}

.countdown {
  font-size: 12px;
  color: #ccc;
}

.login-btn {
  background: #07c160;
}

.login-type-change {
  color: #586b95;
  font-size: 14px;
  margin: 15px 0 0 0;
}

.mobile-input {
  flex: 1;
}

.sms-input {
  flex: 1;
}
