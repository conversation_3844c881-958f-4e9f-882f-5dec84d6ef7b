import Dialog from '@vant/weapp/dist/dialog/dialog';
import getApp from 'shared/utils/get-safe-app';
import Services from './services';

const app = getApp();
Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    title: {
      type: String,
      value: '验证码登录',
    },
    redirectUrl: {
      type: String,
      value: '/packages/account/settings/index',
    },
    isFromAuthComponent: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    formData: {
      countryCode: '+86',
      mobile: '',
      wxMobile: '',
      captcha: '',
      captchaTime: null,
      agreement: '《用户使用协议》',
    },
    captcha: {
      text: '获取验证码',
      code: '',
      countdown: 60,
      textStyle: 'acc-code__btn--enabled',
      btnStyle: '',
      timer: null,
    },
    loginBtn: {
      text: '登录',
      disabled: true,
      wxDisabled: false,
    },
    agreement: {
      text: '《用户使用协议》',
      url: 'https://www.youzan.com/intro/rule/detail?alias=14nykbyyf&pageType=rules',
    },
    isProtocolAgreed: false,
  },
  methods: {
    changeLoginType() {
      this.triggerEvent('changeLoginType', { type: 'password' });
    },
    agreementChange(val) {
      const {
        formData: { captcha, mobile },
      } = this.data;
      this.setData({
        isProtocolAgreed: val.detail,
        'loginBtn.disabled': !(captcha && mobile && val.detail), // 按钮状态
      });
    },
    // 发送验证码
    fetchCaptcha() {
      const { captcha, formData } = this.data;

      if (captcha.countdown !== 60) return;
      if (!this._checkMobile(formData.mobile)) return;
      if (!this._checkProtocol()) return;

      import('@youzan/behavior-verify/src/miniapp/weapp/main').then(
        ({ default: behaviorVerify }) => {
          behaviorVerify({
            bizType: 11, // 申请的业务标识 bizType
            onSuccess: (ticket) => {
              Services.fetchCode(
                {
                  mobile: formData.mobile,
                  ticket,
                },
                () => {
                  this._countDownForCaptchaCode();
                  this.setData({
                    'captcha.btnStyle': 'countdown',
                    'captcha.textStyle': 'acc-code__btn--disabled',
                  });
                },
                (error) => {
                  const title = error.msg || '服务请求失败，请稍后再试';
                  wx.showToast({
                    icon: 'none',
                    title,
                  });
                },
                this
              );
            },
          });
        }
      );
    },

    checkFlag(condition, tipMsg) {
      if (condition) {
        return true;
      }

      if (tipMsg) {
        wx.showToast({
          icon: 'none',
          title: tipMsg,
        });
      }

      return false;
    },

    _checkMobile(value) {
      return this.checkFlag(/^\d{11}$/.test(value), '请输入正确的手机号');
    },

    _checkCaptcha(value) {
      return this.checkFlag(/^\d{6}$/.test(value), '请正确输入收到的6位验证码');
    },

    _checkProtocol() {
      return this.checkFlag(this.data.isProtocolAgreed, '请阅读并勾选协议');
    },

    /**
     * @description sms登录
     */
    login() {
      const { disabled } = this.data.loginBtn;
      const { mobile, captcha, countryCode } = this.data.formData;

      if (disabled) return;
      if (!this._checkMobile(mobile)) return;
      if (!this._checkCaptcha(captcha)) return;
      if (!this._checkProtocol()) return;

      const data = {
        countryCode,
        mobile,
        captcha,
      };

      this._beforeLogin();
      // 发送登录
      Services.loginBySms(
        data,
        () => {
          return app.login().then(() => this._loginSuccess());
        },
        (error) => {
          if (error.code === 135200018 || error.code === 135200019) {
            return this.configDialog();
          }
          this.setData({
            'loginBtn.disabled': false,
          });

          wx.hideLoading();
          wx.showToast({
            icon: 'none',
            title: error.msg || '服务请求出错，请稍后再试',
          });
        },
        this
      );
    },
    /**
     * @description 登录成功后触发的行为
     */
    _loginSuccess() {
      this.setData({ show: false });

      wx.hideLoading();
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        mask: false,
      });

      this.triggerEvent(
        'loginSuccess',
        {
          mb: this.data.formData.mobile,
          authPopType: 'mobile',
          verifyType: 1,
          hasAuth: true, // 已有有赞授权
        },
        {}
      );
    },
    // 绑定手机号码输入状态
    // eslint-disable-next-line no-dupe-keys
    bindMobileInput(e) {
      const {
        formData: { captcha },
        isProtocolAgreed,
      } = this.data;
      this.setData({
        'formData.mobile': e.detail,
        'loginBtn.disabled': !(e.detail && captcha && isProtocolAgreed), // 按钮状态
      });
    },
    // 绑定验证码输入状态
    bindCaptchaInput(e) {
      const {
        formData: { mobile },
        isProtocolAgreed,
      } = this.data;

      this.setData({
        'formData.captcha': e.detail,
        'loginBtn.disabled': !(e.detail && mobile && isProtocolAgreed), // 按钮状态
      });
    },
    /**
     * @description 触发登录前行为
     */
    _beforeLogin() {
      this.loginLoading();
      this.setData({
        'loginBtn.disabled': true,
      });
    },
    /**
     * @description 手机或微信已和其它账号绑定时，需要唤起弹层提示是否继续登录
     * @param {*} wxMobile
     * @param {*} auth 是否为授权唤起的弹层对话
     */
    configDialog(auth = false, option = {}) {
      let mobile;
      if (auth) {
        if (!option.mobile) {
          return wx.showToast({
            icon: 'none',
            title: '授权获取手机号码失败，请重启小程序重新授权',
          });
        }
        mobile = option.mobile;
      } else {
        if (!this.data.formData.mobile) {
          return wx.showToast({
            icon: 'none',
            title: '请正确填写手机号码',
          });
        }
        mobile = this.data.formData.mobile;
      }

      const blurredMobile = []; // 手机号码脱敏
      blurredMobile.push(mobile.substring(0, 3));
      blurredMobile.push('****');
      blurredMobile.push(mobile.substring(7));

      const message = `手机号${blurredMobile.join('')}已与其他微信账号绑定`;
      wx.hideLoading();
      Dialog.confirm({
        message,
        confirmButtonText: '继续登录',
        cancelButtonText: '换个账号',
        context: this,
        zIndex: 10000,
      })
        .then(() => {
          this.confirmLogin(option);
        })
        .catch(() => {});
    },
    /**
     * @description 账号存在提醒后，点确认登录触发
     */
    confirmLogin(option = {}) {
      const mobile = option.mobile || this.data.formData.mobile;
      const countryCode = option.countryCode || this.data.formData.countryCode;
      const { redirectUrl } = this.data;

      const url = `/packages/account/to-bind/index?mobile=${mobile}&countryCode=${encodeURIComponent(
        countryCode
      )}&redirectUrl=${encodeURIComponent(redirectUrl)}`;
      return wx.redirectTo({
        url,
        error: () => {
          wx.showToast({
            icon: 'none',
            title: '页面跳转失败，请重启小程序重新进入',
          });
        },
      });
    },
    /**
     * 验证码输入倒计时读秒
     */
    _countDownForCaptchaCode() {
      let { countdown } = this.data.captcha;
      if (countdown === 0) {
        this.setData({
          'captcha.countdown': 60,
          'captcha.text': '重新发送',
          'captcha.btnStyle': '',
          'captcha.textStyle': 'acc-code__btn--enabled',
        });
        return;
      }
      countdown--;

      this.setData({
        'captcha.countdown': countdown,
        'captcha.text': '已发送(' + countdown + 's)',
      });

      this.data.captcha.timer = setTimeout(() => {
        this._countDownForCaptchaCode();
      }, 1000);
    },
    loginLoading(title = '正在登录...') {
      wx.showLoading({
        title,
        mask: true,
      });
    },
  },
});
