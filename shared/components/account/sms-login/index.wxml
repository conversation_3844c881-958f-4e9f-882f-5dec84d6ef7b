<view class="container" hover-class="none" hover-stop-propagation="false">
  <view class="title">{{ title }}</view>
  <view class="section">
    <van-field
      clearable
      type="number"
      maxlength="11"
      border="{{ false }}"
      placeholder="请输入手机号"
      value="{{ formData.mobile }}"
      class="mobile-input"
      custom-style="padding-left:0px !important;"
      bind:input="bindMobileInput"
    ></van-field>
  </view>
  <view class="section">
    <van-field
      clearable
      type="number"
      maxlength="6"
      border="{{ false }}"
      placeholder-style=""
      placeholder="请输入 6 位验证码"
      value="{{ formData.captcha }}"
      class="sms-input"
      custom-style="padding-left:0px !important;"
      bind:input="bindCaptchaInput"
    ></van-field>
    <view class="fetch-captcha-btn {{ captcha.btnStyle }}" catchtap="fetchCaptcha">
      {{ captcha.text }}
    </view>
  </view>
  <view class="login-type-change" catchtap="changeLoginType">账号密码登录</view>
  <protocol-field bind:agreementChange="agreementChange" />

  <van-button catchtap="login" class="button" custom-class="login-btn" type="primary" block
              disabled="{{ loginBtn.disabled }}">
    {{ loginBtn.text }}
  </van-button>
</view>
<van-dialog id="van-dialog"/>
