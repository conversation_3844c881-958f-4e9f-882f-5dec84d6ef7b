import constant from './constant';
import methods from './methods';


Component({
  properties: {
    selector: {
      type: String,
      value: constant.DEFAULT_DIALOG_ID
    },
    forbidClose: {
      type: Boolean,
      value: true,
    },
  },
  data: {
    accountLogin: false,
    // 调用源，是否来自账号授权组件，用于统计
    isFromAuthComponent: false,
  },
  lifetimes: {
    attached() {},
    detached() {}
  },
  methods
});
