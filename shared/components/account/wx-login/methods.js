import Dialog from '@vant/weapp/dist/dialog/dialog';
import {
  platform,
  getErrorMsg,
  emitUserAuthSuccess,
  AuthType,
  AuthPopType,
} from '@youzan/passport-tee-shared';

import getApp from 'shared/utils/get-safe-app';
import { getStorageLBS } from 'shared/utils/lbs/cache-lbs';
import { getFromParams } from 'shared/utils/guide-share';
import { enterShopOnLogin } from '@/helpers/shop';

import { loginByWx } from './service';
import constant from './constant';

const app = getApp();
const { authLogger } = platform;

const isFunction = (param) => typeof param === 'function';

export default {
  changeLoginType(successCallback, failCallback) {
    this.setData({
      accountLogin: true,
    });
    this.successCallback = successCallback;
    this.failCallback = failCallback;
  },
  showLoading(title = '') {
    wx.showLoading({
      title,
      mask: true,
    });
  },
  hideLoading() {
    wx.hideLoading();
  },
  loginSuccess(event) {
    this.setData({
      accountLogin: false,
    });

    if (isFunction(this.successCallback)) {
      this.successCallback(event.detail);
    }
  },
  loginFail() {
    this.hideLoading();
    this.setData({ accountLogin: false });

    if (isFunction(this.failCallback)) {
      this.failCallback();
    }
  },

  conflictDialog(option = {}) {
    const { mobile } = option;
    if (!mobile) {
      return wx.showToast({
        icon: 'none',
        title: '授权获取手机号码失败，请重启小程序重新授权',
      });
    }

    const blurredMobile = []; // 手机号码脱敏
    blurredMobile.push(mobile.substring(0, 3));
    blurredMobile.push('****');
    blurredMobile.push(mobile.substring(7));

    const message = `手机号${blurredMobile.join('')}已与其他微信账号绑定`;

    let context;
    if (option.selectComponent) {
      context = option.selectComponent;
    } else {
      const currentPages = getCurrentPages();
      const currentPage =
        option.context || currentPages[currentPages.length - 1];
      context = currentPage.selectComponent(
        option.selector || '#account-wx-login'
      );
    }

    const dialogOption = {
      message,
      confirmButtonText: '继续登录',
      cancelButtonText: '换个手机号',
      context,
      selector: `#${constant.DEFAULT_DIALOG_ID}`,
    };

    Dialog.confirm(dialogOption)
      .then(() => {
        this.confirmLogin(option);
      })
      .catch(() => {
        this.changeLoginType(option.success, option.fail);
        option.fail && typeof option.fail === 'function' && option.fail();
      });
  },

  /**
   * @description 微信授权获取手机号码登录
   * @param {Object} options
   * @param {String} options.redirectUrl
   * @param {function} options.start
   * @param {function} options.success
   * @param {function} options.fail
   * @param {function} options.error
   */
  wxLogin(options = {}) {
    options = {
      before: () => {},
      success: () => {},
      fail: () => {},
      error: () => {},
      redirectUrl: '/packages/account/settings/index',
      context: null,
      bizDataMap: {},
      ...options,
    };

    const { event, noMobileFallback } = options;
    if (!event) {
      const throwError = new Error('param event is required');
      typeof options.error === 'function' && options.error(throwError);
      throw new Error('param event is required');
    }
    options.before && typeof options.before === 'function' && options.before();
    if (options.source === 'auth-cmp') {
      this.setData({
        isFromAuthComponent: true,
      });
    }

    const showToast = (title, options = {}) => {
      return wx.showToast({
        title,
        icon: 'none',
        ...options,
      });
    };

    const handleSuccess = (detail) => {
      return typeof options.success === 'function' && options.success(detail);
    };

    this.showLoading();

    platform
      .nativeMobileCallback(event, { noMobileFallback })
      .then(({ detail }) => {
        const loginParams = {
          code: detail.code,
          encryptedData: detail.encryptedData,
          iv: detail.iv,
          appId: app.getAppId(),
          sessionId: app.getSessionId(),
          isUserAuthorize: options.isUserAuthorize,
          bizDataMap: JSON.stringify({
            ...getStorageLBS(),
            ...options.bizDataMap,
            from_params: getFromParams(),
          }),
        };

        this.showLoading('正在登录...');
        const successCallback = (data) => {
          return app
            .login()
            .then(() => {
              if (!this.data.isFromAuthComponent) {
                // 独立使用（非授权组件内）时，触发事件，否则授权组件内会触发
                emitUserAuthSuccess({
                  authTypeList: [AuthType.MOBILE],
                  authPopTypeList: [AuthPopType.MOBILE],
                });
              }

              enterShopOnLogin()
                // handleEnterShopAfterLoginForWeapp 如果需要重新进店 零售会返reject 我们也需要捕获一下
                .finally(() => {
                  this.hideLoading();
                  showToast('登录成功', {
                    icon: 'success',
                    mask: true,
                  });
                  handleSuccess({
                    mb: data.mobile,
                    authPopType: AuthPopType.MOBILE,
                    verifyType: 3,
                  });
                });
            })
            .catch((e) => {
              console.warn('Login error', e);
              this.hideLoading();
              showToast('登录失败');
            });
        };

        const errorCallback = (error) => {
          this.hideLoading();

          authLogger.logAll({
            logName: '授权-手机号登录失败',
            logData: {
              type: 'native',
              env: 'weapp',
              weapp: true, // 微信原生，非多端授权组件
              errMsg: getErrorMsg(error),
              err: error,
            },
          });

          if (error.code === -9999) {
            const { res } = error;
            if (
              res &&
              res.data &&
              (res.data.code === 135200018 || res.data.code === 135200019) &&
              res.data.mobile
            ) {
              return this.conflictDialog(
                Object.assign(options, {
                  mobile: res.data.mobile,
                  countryCode: res.data.countryCode,
                })
              );
            }
          }
          if (error.code === 135000049) {
            if (options.fail && typeof options.fail === 'function') {
              options.fail({
                error: 'DECRYPT-ERROR',
                msg: '数据解密失败，微信授权失败',
              });
            }
            return app
              .login()
              .then(() => {
                showToast('微信手机号授权失败，请重试');
              })
              .catch(() => {
                showToast('微信手机号授权失败，请重试');
                app.logger.appError({
                  name: 'wx-mobile-decryption',
                  level: 'info',
                  message: 'login-error-with-decryption',
                  detail: {
                    ...loginParams,
                  },
                });
              });
          }
          showToast(error.msg || '服务请求出错，请稍后再试');

          if (options.fail && typeof options.fail === 'function') {
            return options.fail({
              error: 'REQUEST-ERROR',
              msg: error.msg || '服务请求出错，请稍后再试',
            });
          }
        };

        loginByWx(loginParams, this).then(successCallback).catch(errorCallback);
      })
      .catch(({ detail, disagree, errType, noMobileFallback }) => {
        this.hideLoading();

        if (disagree) {
          options.fail({
            error: 'USER-DENY',
            msg: '微信授权失败',
          });
          return;
        }

        // 非用户拒绝，异常时不降级
        if (noMobileFallback) {
          // 不降级需要提示信息
          const msg = errType.noQuota
            ? '手机号验证组件额度不足'
            : getErrorMsg(detail) || '手机号验证失败，请稍后重试';

          showToast(msg);

          options.fail({ msg });
          return;
        }

        // 降级，异常可能是: 主体是海外、微信自己的一些异常（比如手机号需要验证、system error），启用有赞手机号登录
        this.changeLoginType((...args) => {
          this.enterShopLogin().then(() => handleSuccess(...args));
        }, options.fail);
      });
  },

  confirmLogin(option = {}) {
    const { mobile } = option;
    const { countryCode } = option;
    const logined = option.redirectUrl;

    const redirectUrl = `/packages/account/to-bind/index?mobile=${mobile}&countryCode=${encodeURIComponent(
      countryCode
    )}&redirectUrl=${encodeURIComponent(logined)}`;

    // eslint-disable-next-line @youzan/dmc/wx-check
    return wx.redirectTo({
      url: redirectUrl,
    });
  },

  /**
   * 关闭有赞登录弹窗时
   */
  handleCloseAccountLogin() {
    this.triggerEvent('userClose');
  },
};
