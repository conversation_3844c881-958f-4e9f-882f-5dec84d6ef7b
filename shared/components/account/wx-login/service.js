// eslint-disable-next-line import/no-unresolved
import { node } from '../../../utils/request';
import { getAjaxConfig } from '../_utils';

const Post = function (options = {}, thisArg) {
  const cfg = getAjaxConfig(thisArg);
  // 合并默认选项
  return node({
    config: {
      skipShopInfo: true,
    },
    method: 'POST',
    header: {
      'content-type': 'application/x-www-form-urlencoded',
      ...cfg.header,
    },
    origin: 'uic',
    data: {},
    ...options,
  });
};

export function loginByWx(data, thisArg) {
  return Post(
    {
      origin: 'uic',
      pathname: '/passport/login/wx.json',
      path: '/passport/login/wx.json',
      data,
    },
    thisArg
  );
}
