## 微信授权弹窗组件使用说明


### 组件说明
* 组件使用微信授权用户手机号码进行登录：因为需要用户主动触发才能发起获取手机号接口，所以该功能不由 API 来调用，需用 `<button>` 组件的点击来触发。
* 开发者文档：https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/getPhoneNumber.html

### 组件引入步骤：
1、在 app.json 或 index.json 中引入组件依赖

```
"usingComponents": {
  "account-wx-login": "shared/components/account/wx-login/index"
}

```

2、wxml文件加入组件依赖

```
<account-wx-login id="account-wx-login" />


```

3、触发代码示例：

```
<button open-type="getPhoneNumber" bindgetphonenumber="loginBywxAuthorize">
         
            使用微信授权组件登录
</button>
```
4、代码调用:

```
//引入组件
import wxLogin from 'shared/utils/wx-login';

Page({
  data: {},
  //定义方法
  loginBywxAuthorize(event) {
    const options = {
      event
      //其它可选参数选项
    };
    return wxLogin(options);
  }
});

```



### `wxLogin`方法参数说明
```
/**
 * @description 微信授权获取手机号码登录
 * @param {Object} options
 * @param {Object} options.event 微信授权触发自带event参数，必选
 * @param {String} options.redirectUrl 
 * 		登录成功后跳转地址，可设为false,不跳转
 * 		触发帐后合并流程后，流程完成回跳地址
 * 	 	默认值：/packages/account/settings/index
 * @param {function} options.before 登录前回调，可选
 * @param {function} options.success 登录成功回调，可选
 * @param {function} options.fail 登录成功回调fail(error)，可选
 * @param {function} options.error 登录出错回调error(error)，可选
 */
const wxLogin = (options = {}) => {

}

```
