import { VanxComponent } from 'shared/common/base/wsc-component';
import {
  platform,
  request,
  emitUserAuthSuccess,
} from '@youzan/passport-tee-shared';
import WeappEvent from '@youzan/weapp-utils/lib/event';

import Tee from '@youzan/tee';

import {
  SHOP_USER_PRIVACY,
  PLATFORM_USER_USE,
  PLATFORM_USER_PRIVACY,
  ERROR_MSG,
  isUIComponent,
  getProtocolCode,
} from '../protocol/shared';

const app = getApp();
const { logSkynet } = platform.authLogger;

const COMMON_PROTOCOL_CODES = [
  SHOP_USER_PRIVACY,
  PLATFORM_USER_USE,
  PLATFORM_USER_PRIVACY,
];

const PROTOCOL_DENIED_KEY = 'PROTOCOL_DENIED';
const PROTOCOL_AUTO_AUTH_KEY = 'PROTOCOL_AUTO_AUTH';

const judgeIsBespokeProtocol = () => {
  if (isUIComponent()) {
    return Promise.resolve(true);
  }
  return getProtocolCode().then((codes) => {
    // 存在不同与标品的协议，说明有定制
    return codes.some((code) => !COMMON_PROTOCOL_CODES.includes(code));
  });
};

VanxComponent({
  properties: {
    /** 是否不自动申请授权，默认会自动申请授权 */
    noAutoAuth: {
      type: Boolean,
      value: false,
    },
    zIndex: {
      type: Number,
      value: 10100,
    },
    rootPortal: {
      type: Boolean,
      value: true,
    },
  },

  data: {
    showProtocol: false,
    needSkipSigned: true,
    popupDuration: 300,
    kdtId: app.getKdtId(),
  },

  attached() {
    this.initProtocol();
    this.autoAuth();

    // 同步连锁 kdtId 变更逻辑
    this.updateKdtId(app.getKdtId());
    this.on('app:chainstore:kdtid:update', ({ kdtId }) => {
      this.updateKdtId(kdtId);
    });
  },

  detached() {
    this.getProtocol().then((protocol) =>
      protocol.off({ source: protocol.source })
    );
  },

  pageLifetimes: {
    show() {
      this.autoAuth();
    },
  },

  methods: {
    updateKdtId(kdtId) {
      this.setYZData({ kdtId }, { immediate: true });
    },

    getProtocol() {
      if (!this.$_protocolPs) {
        const PROTOCOL_SOURCE = `inject-protocol_${Date.now()}`;

        // 异步分包
        this.$_protocolPs = import('@youzan/passport-protocol')
          .then(({ InvokeProtocol }) => {
            return new InvokeProtocol({
              source: PROTOCOL_SOURCE,
            });
          })
          .catch((e) => {
            this.$_protocolPs = null; // 失败后下次重试
            logSkynet('Resolve passport-protocol failed', {
              from: PROTOCOL_SOURCE,
              error: e.message || e.msg || e,
            });
            console.error('Resolve passport-protocol failed', e);
            throw e;
          });
      }
      return this.$_protocolPs;
    },

    /**
     * 同意逻辑
     */
    onAgree() {
      // 授权成功后更新授权数据缓存
      app.refreshUserAuthData().finally(() => {
        // 授权成功事件。在数据更新结束后触发，否则可能数据不一致
        emitUserAuthSuccess({
          authTypeList: ['protocol'],
          authPopTypeList: ['protocol'],
        });
      });

      this.close();

      this.getProtocol().then((protocol) => {
        protocol.trigger('agree');
      });
    },

    /**
     * 拒绝
     */
    onDisagree() {
      this.close();
    },

    /**
     * 关闭弹窗
     */
    close() {
      this.data.showProtocol &&
        WeappEvent.trigger('account:user-authorize:behavior-finish');

      this.setYZData({
        showProtocol: false,
      });
    },

    handleBespoke({ needSkipSigned = false }) {
      judgeIsBespokeProtocol().then((isBespoke) => {
        isBespoke
          ? this.setYZData({ showProtocol: true, needSkipSigned })
          : this.getProtocol().then((protocol) =>
              protocol.trigger('disagree', ERROR_MSG.NOT_BESPOKE)
            ); // 定制场景弹出，没有定制协议时不弹出协议
      });
    },

    /**
     * 初始化事件
     */
    initProtocol() {
      // 监听 show 事件
      this.getProtocol().then((protocol) => {
        const handleShow = (data = {}) => {
          if (data.bizType === 'vip-bespoke') {
            return this.handleBespoke(data);
          }

          app.resolveTeeAPI().then((api) => {
            api.getUserPrivacy().then(({ protocol: protocolState }) => {
              // 协议是否已授权
              if (protocolState) {
                this.close();
                protocol.trigger('agree'); // 直接通过
                return;
              }
              this.setYZData({
                showProtocol: true,
                needSkipSigned: true,
              });
            });
          });
        };

        // playback: 当组件加载（事件绑定）晚于触发时刻时，支持绑定后回放
        protocol.on('show', handleShow, { playback: true });

        protocol.on('disagree', () => {
          Tee.setGlobal(PROTOCOL_DENIED_KEY, true);
        });

        // 监听 close 事件
        protocol.on('close', ({ immediately, excludeSource } = {}) => {
          if (
            this.data.showProtocol &&
            (!excludeSource || excludeSource !== protocol.source)
          ) {
            const lastDuration = this.data.popupDuration;

            const data = { showProtocol: false };
            if (immediately) {
              // 立即关闭，防止出现动画过渡
              data.popupDuration = 0;
              // 300ms后恢复
              setTimeout(() => {
                this.setYZData({ popupDuration: lastDuration });
              }, 300);
            }
            this.setYZData(data);
          }
        });

        protocol.init();

        this.triggerEvent('onProtocolInitListen');
      });
    },

    /**
     * 自动请求协议授权
     */
    autoAuth() {
      if (this.properties.noAutoAuth) {
        return;
      }

      // 用户已显式拒绝时，不再自动请求授权
      const hasDenied = Tee.getGlobal(PROTOCOL_DENIED_KEY);
      if (hasDenied) {
        return;
      }

      // 在自动弹出白名单内，且未授权过的用户，进行授权
      this.checkAutoAuth().then((autoAuth) => {
        if (!autoAuth) {
          return;
        }

        app.resolveTeeAPI().then((api) => {
          api.getUserPrivacy().then(({ protocol }) => {
            if (!protocol) {
              this.getProtocol().then((invoker) =>
                invoker.auth({
                  bizType: 'auto-auth',
                })
              );
            }
          });
        });
      });
    },

    /**
     * 获取合规协议弹窗灰度配置
     */
    checkAutoAuth() {
      const globalAutoAuth = Tee.getGlobal(PROTOCOL_AUTO_AUTH_KEY);
      // 在生命周期内有效
      if (typeof globalAutoAuth === 'boolean') {
        return Promise.resolve(globalAutoAuth);
      }

      if (this.__checkAuto) {
        return this.__checkAuto;
      }

      this.__checkAuto = request({
        origin: 'passport',
        path: '/api/authorize/protocol/can-auto-auth.json',
      })
        .then(({ support }) => {
          this.__checkAuto = null;
          Tee.setGlobal(PROTOCOL_AUTO_AUTH_KEY, support);
          return support;
        })
        .catch(() => {
          this.__checkAuto = null;
          return false;
        });

      return this.__checkAuto;
    },
  },
});
