.simple-popup {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 160;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
  /* transition: opacity 0.3s;
  opacity: 1; */
}

.popup-visible-false {
  display: none;
}

.popup-visible-true {
  display: flex;
}

.simple-popup-overlay {
  position: absolute;
  z-index: 99;
  background: rgba(0, 0, 0, .7);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  transition: opacity 0.3s;
}

.content-visible-false {
  opacity: 0;
}

.content-visible-true {
  opacity: 1;
}

.simple-popup-container {
  position: relative;
  padding: 10px;
  z-index: 999;
  background: #fff;
  border-radius: 10px;
  text-align: center;
  transition: opacity 0.3s;
}