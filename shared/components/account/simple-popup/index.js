Component({
  properties: {
    /**
     * 是否显示
     */
    show: {
      type: Boolean,
      default: false,
    },
    /**
     * 自定义样式
     */
    customPopupStyle: {
      type: String,
      default: '',
    },
    /**
     * 自定义蒙层样式
     */
    customOverlayStyle: {
      type: String,
      default: '',
    },
    /**
     * 自定义内容样式
     */
    customContainerStyle: {
      type: String,
      default: '',
    },
  },

  data: {
    /**
     * 外层容器的样式
     */
    containerClass: 'popup-visible-false',
    /**
     * 内部的样式
     */
    contentClass: 'content-visible-false'
  },

  observers: {
    /**
     * 监听 show 的变更
     */
    show(val) {
      if (val) {
        this.setData({
          containerClass: 'popup-visible-true'
        });

        return setTimeout(() => {
          this.setData({
            contentClass: 'content-visible-true'
          });
        }, 20);
      }

      this.setData({
        contentClass: 'content-visible-false'
      });
    }
  },

  methods: {
    /**
     * 点击关闭
     */
    handleClose() {
      this.triggerEvent('close');
    },

    /**
     * overlay 动画结束
     */
    handleOverlayTransionEnd() {
      if (!this.properties.show) {
        this.setData({
          containerClass: 'popup-visible-false'
        });
      }
    }
  }
});
