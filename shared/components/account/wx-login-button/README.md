# 小程序内获取手机号组件
> 兼容微信小程序 + 企业微信小程序

## 特别注意
代码里不要直接使用如下所示的 `button` 组件，因为在企业微信里存在兼容问题
```html
<van-button open-type="getPhoneNumber" bindgetphonenumber="wxLogin">微信快速登录</van-button>
```

## 使用姿势
1、在 `index.json` 中引入组件依赖

```
"usingComponents": {
  "account-wx-login-button": "shared/components/account/wx-login-button/index"
}

```

2、wxml文件加入组件依赖

```
<account-wx-login-button>微信登录</account-wx-login-button>

```
