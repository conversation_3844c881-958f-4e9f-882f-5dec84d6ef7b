import { platform } from '@youzan/passport-tee-shared';
import Toast from '@vant/weapp/dist/toast/toast';

import WscComponent from 'shared/common/base/wsc-component/index';
import { showWxLoginButton } from '../../../utils/wx-login';

const { authLogger } = platform;
const app = getApp();

WscComponent({
  properties: {
    customStyle: String,
    autoShowYzLogin: Boolean,
    mobileQuotaToast: Boolean,
    isFromAuthComponent: {
      type: Boolean,
      value: false,
    },
    needPlatformPrivacyAuth: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    showWxButton: showWxLoginButton(),
    showYzLogin: false,
    btnId: `native-mobile-btn_${Date.now()}`,
    openType: 'getPhoneNumber',
  },
  externalClasses: ['btn-class'],
  observers: {
    needPlatformPrivacyAuth(value) {
      this.setYZData({
        openType: `getPhoneNumber${value ? '|agreePrivacyAuthorization' : ''}`,
      });
    },
  },

  attached() {
    this.init();
  },

  methods: {
    init() {
      app.getAuthorizeData().then((data) => {
        if (data.mobileAuthDisabled) {
          // 当手机号登录/授权能力禁用时，禁用渠道原生获取手机号能力
          this.setYZData({ showWxButton: false }, { immediate: true });
        }
      });
    },

    handleClick() {
      authLogger.logNativeMobileClick({});

      // 微信手机号按钮点击后，会有原生 loading（微信请求数据），但是此 loading 没有禁止穿透，导致可以点击其他手机号按钮，
      // 导致最终可能弹出多个手机号验证弹窗，造成重复验证收费
      // 所以手动增加一个禁止点击的 mask 防止额外点击
      this.showLoading('', 1e3);
    },

    showLoading(title, duration = 0) {
      Toast.loading({
        message: title,
        duration,
        forbidClick: true,
        mask: true,
        zIndex: 20009, // 最高
      });
    },

    /**
     * 手机号授权回调
     */
    handleGetMobile(event) {
      this.triggerEvent('getMobile', event);
    },

    /**
     * 显示有赞手机号登录 popup
     */
    handleShowLogin() {
      this.setYZData({
        showYzLogin: true,
      });
    },

    /**
     * 关闭有赞手机号登录 popup
     */
    handleCloseLogin() {
      this.setYZData({
        showYzLogin: false,
      });
    },

    /**
     * 关闭弹窗
     */
    handlePopupClose() {
      this.handleCloseLogin();
      this.triggerEvent('onclose');
    },

    /**
     * 有赞登录成功
     */
    handleLoginYzSuccess(event) {
      this.handleCloseLogin();
      this.triggerEvent('onSuccess', event.detail);
    },

    /**
     * 有赞登录失败
     */
    handleLoginYzFail(event) {
      this.triggerEvent('onFail', event.detail);
    },

    handleAgreePrivacyAuth() {
      if (this.data.needPlatformPrivacyAuth) {
        platform.resolvePrivacyAuth(this.data.btnId);
      }
    },
  },
});
