<block>
  <button
    wx:if="{{ showWxButton }}"
    class="account-wx-button btn-class"
    style="{{ customStyle }}"
    open-type="{{ openType }}"
    phone-number-no-quota-toast="{{mobileQuotaToast}}"
    button-id="{{ btnId }}"
    bindtap="handleClick"
    bindgetphonenumber="handleGetMobile"
    bindagreeprivacyauthorization="handleAgreePrivacyAuth"
  >
    <slot/>
  </button>
  <block wx:else>
    <button
      class="account-wx-button btn-class"
      style="{{ customStyle }}"
      bind:tap="handleShowLogin"
    >
      <slot/>
    </button>
    <yz-account-login
      show="{{ showYzLogin || autoShowYzLogin }}"
      bind:closeAccountLogin="handlePopupClose"
      bind:loginSuccess="handleLoginYzSuccess"
      bind:loginFail="handleLoginYzFail"
      is-from-auth-component="{{ isFromAuthComponent }}"
    />
  </block>

  <van-toast id="van-toast" />
</block>
