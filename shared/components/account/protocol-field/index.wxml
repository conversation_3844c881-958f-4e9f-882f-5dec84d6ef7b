<view class="agreement">
  <van-checkbox
    custom-class="agreement__check"
    label-class="agreement__check-label"
    icon-size="14px"
    value="{{ isProtocolAgreed }}"
    bind:change="handleAgreementChange"
  >
    阅读并同意
    <block wx:for="{{ protocols }}" wx:key="name">
      <view class="agreement__link" data-protocol="{{ item }}" catchtap="readProtocol">
        {{item.name}}
      </view>
      <text wx:if="{{ index < protocols.length - 1 }}">、</text>
    </block>
  </van-checkbox>
</view>