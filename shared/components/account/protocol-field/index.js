import navigate from 'shared/utils/navigate';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

const { resolveProtocol } = getApp();

VanxComponent({
  properties: {},
  data: {
    isProtocolAgreed: false,
    protocols: [],
  },
  attached() {
    this.getProtocol();
  },
  methods: {
    getProtocol() {
      resolveProtocol().then(({ getProtocolData }) => {
        getProtocolData().then((res) => {
          const { protocolData = [] } = res;
          this.setYZData({
            protocols: protocolData.map((data) => ({
              name: data.agreementTplName,
              url: data.url,
            })),
          });
        });
      });
    },
    handleAgreementChange(e) {
      this.setYZData({
        isProtocolAgreed: !!e.detail,
      });
      this.triggerEvent('agreementChange', !!e.detail);
    },
    /**
     * 跳转到登录协议展示页
     */
    readProtocol(e) {
      const { protocol } = e.target.dataset || {};
      if (!protocol) {
        return;
      }

      const protocolUrl = encodeURIComponent(protocol.url);

      navigate.navigate({
        url: `/pages/common/webview-page/index?src=${protocolUrl}`,
        fail: () => {
          wx.showToast({
            icon: 'none',
            title: `打开《${protocol.name}》失败`,
          });
        },
      });
    },
  },
});
