<view class="user-authorize-native wrapper-class" capture-bind:tap="onTap">
  <!-- 注意：虽然这里引用是 native/weapp 目录的，但编译后实际用的是 lib 目录下的组件，是为了解决体积过大的问题 -->
  <!-- 配置文件：koko.config.js convertPathConfig 字段 -->
  <tee-user-authorize
    type="{{ type }}"
    kdt-id="{{ kdtId }}"
    scene="{{ scene }}"
    auth-type-list="{{ authTypeList }}"
    z-index="{{ zIndex }}"
    allow-deny="{{ allowDeny }}"
    trigger="{{ trigger }}"
    custom-style="{{ customStyle }}"
    button-class="btn-class"
    popup-custom-style="{{ popupCustomStyle }}"
    protocol-source="{{ protocolSource }}"
    protocol-is-cloud-slot="{{ protocolIsCloud }}"
    biz-data-map="{{ bizDataMap }}"
    bind:next="onSuccess"
    bind:fail="onFail"
    bind:auth-popup-show="onPopupShow"
  >
    <slot />
    <protocol-content wx:if="{{ protocolIsCloud }}" slot="ui-component" />
  </tee-user-authorize>
</view>
