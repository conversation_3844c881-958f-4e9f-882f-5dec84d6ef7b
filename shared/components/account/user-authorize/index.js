/**
 * ATTENTION: 此文件仅用于原生授权组件历史兼容，功能迭代需在 NPM 内部实现
 *
 * ifdef youzanyun 条件编译配置：build/loaders/youzanyun-utils.js
 */

import YunComponent from '@/youzanyun-sdk/yun-component';
import getApp from 'shared/utils/get-safe-app';

/* #ifdef BUILD_ENV=youzanyun */
import { cloudProtocolBehavior } from './mixins/cloud';
/* #endif */

YunComponent({
  /* #ifdef BUILD_ENV=youzanyun */
  behaviors: [cloudProtocolBehavior],
  /* #endif */

  properties: {
    /** 授权组件模式，仅支持 'separate' */
    type: String,

    /** 授权场景值，配置路径：店铺后台-设置-用户授权 */
    scene: String,

    /** 自定义授权项 */
    authTypeList: { type: Array, value: [] },

    /** 弹窗层级 */
    zIndex: { type: Number, value: 10100 },

    /** 是否允许拒绝后继续流程 */
    allowDeny: Boolean,

    /** 按钮自定义样式 */
    customStyle: String,

    /** 弹窗自定义样式 */
    popupCustomStyle: String,

    /** 业务方自定义数据，会传给授权、手机号登录接口 */
    bizDataMap: Object,

    /** 弹窗触发方式，'auto' 自动触发，默认为“点击”触发。仅有赞授权有效 */
    trigger: String,
  },

  externalClasses: ['wrapper-class', 'btn-class'],

  data: {
    kdtId: getApp().getKdtId(),
  },

  attached() {
    this.updateKdtId(getApp().getKdtId());

    // 当小程序 kdtId 变更时更新传给授权组件的 kdtId
    // this.on 会随着组件销毁自动解除监听，不需要手动解绑
    this.on('app:chainstore:kdtid:update', ({ kdtId }) => {
      this.updateKdtId(kdtId);
    });
  },

  methods: {
    /** 触发多端组件事件 */
    emit(name, teeEvent) {
      this.triggerEvent(name, teeEvent.detail.payload);
    },

    updateKdtId(kdtId) {
      this.setYZData({ kdtId }, { immediate: true });
    },

    onSuccess(event) {
      this.emit('next', event);
    },

    onFail(event) {
      this.emit('fail', event);
    },

    onPopupShow(event) {
      this.emit('popup-show', event);
    },

    onTap(event) {
      this.triggerEvent('tap', event.detail);
    },
  },
});
