import {
  platform,
  hooks,
  getErrorMsg,
  InvokeProtocolEvent,
} from '@youzan/passport-tee-shared';
import { createBridgeProtocolHandlers } from '@youzan/passport-tee-shared/lib/cloud/bridge';
import {
  isUIComponent as isCloudProtocol,
  PROTOCOL_SHOW_EVENT,
  PROTOCOL_CLOSE_EVENT,
  PROTOCOL_AGREE_EVENT,
  PROTOCOL_DISAGREE_EVENT,
} from 'shared/components/account/protocol/shared';

import { AppAuthEvents } from '../constants/event';

const importProtocol = () => import('@youzan/passport-protocol');

const { authLogger } = platform;
const { protocolHook } = hooks;

const OFF_KEY = Symbol('offList');

export const cloudProtocolBehavior = Behavior({
  data: {
    /** 协议是否定制 */
    protocolIsCloud: isCloudProtocol(),
    /** 协议 InvokeProtocol 来源 */
    protocolSource: `@weapp/authorize_${Date.now()}`,
  },

  created() {
    this.initProtocol();
  },

  detached() {
    this.runOff();
  },

  methods: {
    addOff(callback) {
      this[OFF_KEY] = (this[OFF_KEY] || []).concat(callback);
    },

    runOff() {
      if (this[OFF_KEY]) {
        this[OFF_KEY].forEach((off) => off());
        this[OFF_KEY] = null;
      }
    },

    getProtocol() {
      if (!this.$_protocolPs) {
        this.$_protocolPs = importProtocol()
          .then((p) => {
            return new p.InvokeProtocol({ source: this.data.protocolSource });
          })
          .catch((e) => {
            this.$_protocolPs = null; // 失败后下次重试
            throw e;
          });
      }

      return this.$_protocolPs;
    },

    initProtocol() {
      this.getProtocol()
        .then((protocol) => {
          // 开放 1.0 事件兼容
          const protocolBridge = createBridgeProtocolHandlers({
            isCustomUI: this.data.protocolIsCloud,
            invokeCloud: this.invokeCloud.bind(this),
            log: authLogger.logAll,
          });

          // Hook：协议“允许”前
          const beforeAgree = ({ source }) => {
            if (source !== protocol.source) return Promise.resolve();
            return protocolBridge.beforeAgree();
          };
          protocolHook.beforeAgree.add(beforeAgree);

          // 绑定开放事件，仅监听自身组件触发的事件，防止被多次被触发
          const options = {
            /**
             * 使用 sameSource 的原因：
             *
             * 目前一个页面中可能存在多个 passport-protocol 实例，且任意代码都可以调用 InvokeProtocol 的实例方法来触发相关事件；
             * 如果把开放事件绑定在全局（如 class 中），那当多个实例触发事件时（多个监听多个触发），开放事件也能监听到然后被触发多次。
             * 所以为了保证一对一触发（只触发一次开放事件），需要保证 source 一致，限定只接收同一个 source 的事件
             */
            sameSource: true,
          };
          const EVENT = InvokeProtocolEvent; // 太长了

          protocol.on(
            EVENT.DISAGREE,
            () => protocolBridge.onDisagree(),
            options
          );
          protocol.on(
            EVENT.SHOW_BEFORE,
            () => protocolBridge.onBeforeShow(),
            options
          );
          protocol.on(EVENT.CLOSE, () => protocolBridge.onClose(), options);

          // 清理
          this.addOff(() => {
            protocol.off();
            protocolHook.beforeAgree.remove(beforeAgree);
            protocolBridge.cleanup();
          });
        })
        .catch((e) => {
          authLogger.logAll({
            errTitle: 'protocol init error',
            errInfo: {
              source: this.data.protocolSource,
              msg: getErrorMsg(e),
              stack: e && e.stack,
            },
          });
          console.error('协议组件初始化失败，功能将异常', e);
          throw e;
        });
    },

    /**
     * 调用协议组件的开放方法，集中处理
     */
    invokeCloud(alias, payload) {
      /**
       * cloudHook -> 开放 1.0 事件名映射
       * 同 extension 中的映射关系：src/ext-tee-passport/extensions/protocol/cloud/extension.ts
       */
      const bridgeEventMap = {
        beforeBridgeProtocolUIShow: AppAuthEvents.PROTOCOL_NEED_SHOW,
        beforeBridgeUserAuthProtocolAgree: AppAuthEvents.PROTOCOL_AGREE,
        afterBridgeProtocolUIShow: PROTOCOL_SHOW_EVENT,
        afterBridgeProtocolUIClose: PROTOCOL_CLOSE_EVENT,
        beforeBridgeProtocolUIAgree: PROTOCOL_AGREE_EVENT,
        afterBridgeProtocolUIDisagree: PROTOCOL_DISAGREE_EVENT,
      };

      const bridgeEvent = bridgeEventMap[alias];

      if (!bridgeEvent) {
        authLogger.logAll({ errMsg: `开放事件不存在:${bridgeEvent}` });

        // 不存在不阻塞
        return Promise.resolve();
      }

      return this.triggerAsync(bridgeEvent, payload);
    },
  },
});
