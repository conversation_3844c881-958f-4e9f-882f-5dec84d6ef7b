<protocol
  visible="{{ visible }}"
  theme-colors="{{ themeColors }}"
  code="{{ code }}"
  kdtId="{{ kdtId }}"
  zIndex="{{ zIndex }}"
  popupCustomStyle="{{ popupCustomStyle }}"
  contentCustomStyle="padding-bottom: {{ yunBottomExtHeight }}px"
  useNativeSlot="{{ !agreeBtnDisabled ? useNativeSlot : false }}"
  useUIComponentSlot="{{ isUIComponent }}"
  needSkipSigned="{{ needSkipSigned }}"
  duration="{{ duration }}"
  bind:agree="handleAgree"
  bind:disagree="handleDisagree"
  bind:close="handlePopupAfterLeave"
  bind:show-before="handlePopupBeforeEnter"
  btnConfig="{{ btnConfig }}"
  rootPortal="{{ rootPortal }}"
>
  <slot slot="agree-btn" name="agree-btn" />
  <ecloud-custom slot="ui-component" bind:agree-disable-changed="handleAgreeDisableChanged"/>
</protocol>
