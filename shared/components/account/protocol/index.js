import Event from '@youzan/weapp-utils/lib/event';
import YunComponent from '@/youzanyun-sdk/yun-component';
import pick from '@youzan/weapp-utils/lib/pick';
import Tee from '@youzan/tee';
import getApp from 'shared/utils/get-safe-app';
import {
  PROTOCOL_SHOW_EVENT,
  PROTOCOL_CLOSE_EVENT,
  PROTOCOL_AGREE_EVENT,
  PROTOCOL_DISAGREE_EVENT,
  getEcloudCustomProtocolCode,
  initComponentData,
  triggerEcloudEvent,
  protocolBatchSign,
} from './shared';

const app = getApp();

YunComponent({
  options: {
    multipleSlots: true,
  },
  properties: {
    // popup 弹窗自定样式
    popupCustomStyle: {
      type: String,
      value: '',
    },
    kdtId: {
      type: Number,
      value: 0,
    },
    zIndex: {
      type: Number,
      value: 10100,
    },
    visible: {
      type: Boolean,
      value: false,
    },
    useNativeSlot: {
      type: Boolean,
      value: false,
    },
    needSkipSigned: {
      type: Boolean,
      value: true,
    },
    duration: {
      type: Number,
      value: 300,
    },
    rootPortal: {
      type: Boolean,
      value: true,
    },
  },

  data: initComponentData({
    themeColors: null,
    // 是否为 UI 定制
    isUIComponent: false,
    // 大客定制协议 codes（有则指定，否则为默认标品）
    code: getEcloudCustomProtocolCode(),
    // 协议框的按钮配置
    btnConfig: {
      agreeBtn: '',
      disagreeBtn: '',
      hideDisagree: false,
      customStyle: '',
    },
    // 云定制，底部额外高度
    yunBottomExtHeight: 0,
    // 同意按钮是否可点击
    agreeBtnDisabled: false,
  }),

  attached() {
    Tee.setGlobal(
      'yun.account.protocol.config',
      pick(this.data, [
        'isUIComponent',
        'theme',
        'yunBottomExtHeight',
        'btnConfig',
      ])
    );

    Event.trigger('yun.account.protocol.init');
  },

  methods: {
    /**
     * 协议组件同意
     */
    handleAgree(payload) {
      const triggerAgree = () => {
        this.triggerEvent('agree', {
          protocol: true,
        });

        app.logger.log({
          et: 'click', // 事件类型
          ei: 'protocol_click', // 事件标识
          en: '协议授权组件授权同意', // 事件名称
        });
      };

      if (!this.data.isUIComponent) {
        triggerAgree();
        return;
      }

      // 以下为 UI 定制逻辑

      const callback = (param) => {
        payload.detail && payload.detail(param);
      };

      triggerEcloudEvent(this, PROTOCOL_AGREE_EVENT)
        .then(() => protocolBatchSign())
        .then(() => {
          callback({ success: true });
          triggerAgree();
        })
        .catch((error) => {
          callback({ success: false, error });
        });
    },

    /**
     * 协议不同意
     */
    handleDisagree() {
      this.triggerEvent('disagree', {
        protocol: true,
      });

      triggerEcloudEvent(this, PROTOCOL_DISAGREE_EVENT);
    },

    /**
     * popup 出现前
     */
    handlePopupBeforeEnter() {
      this.triggerEvent('open');
      triggerEcloudEvent(this, PROTOCOL_SHOW_EVENT);

      app.logger.log({
        et: 'view', // 事件类型
        ei: 'protocol_exposure', // 事件标识
        en: '协议授权组件曝光', // 事件名称
      });
    },

    /**
     * popup 消失
     */
    handlePopupAfterLeave() {
      this.triggerEvent('close');
      triggerEcloudEvent(this, PROTOCOL_CLOSE_EVENT);
    },

    /**
     * 自定义 ui 时如果同意按钮的 disabled 改变了
     */
    handleAgreeDisableChanged(event) {
      if (this.data.isUIComponent) {
        this.setYZData({ agreeBtnDisabled: event.detail });
      }
    },
  },
});
