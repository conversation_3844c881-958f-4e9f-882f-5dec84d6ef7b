/**
 * 用户授权相关方法
 */
import cloneDeep from '@youzan/weapp-utils/lib/clone-deep/index';
import ecloudCustomConfig from './config/ecloud-custom.json';

const resolveProtocol = () => getApp().resolveProtocol();

// 用户隐私协议 code
export const PLATFORM_USER_PRIVACY = 'PLATFORM_USER_PRIVACY';
// 用户协议 code
export const PLATFORM_USER_USE = 'PLATFORM_USER_USE';
// 标品商家隐私协议
export const SHOP_USER_PRIVACY = 'SHOP_USER_PRIVACY';

export const ERROR_MSG = {
  NOT_BESPOKE: { code: 1001, msg: '该商家没有定制协议' },
  USER_DISAGREE: { code: 1002, msg: '用户拒绝签署协议' },
};

// 定制 UI 的事件 - 协议弹窗出现
export const PROTOCOL_SHOW_EVENT =
  'app:account:authorize:custom-ui-protocol-show';
// 定制 UI 的事件 - 协议弹窗关闭
export const PROTOCOL_CLOSE_EVENT =
  'app:account:authorize:custom-ui-protocol-close';
// 定制 UI 的事件 - 协议同意
export const PROTOCOL_AGREE_EVENT =
  'app:account:authorize:custom-ui-protocol-agree';
export const PROTOCOL_DISAGREE_EVENT =
  'app:account:authorize:custom-ui-protocol-disagree';

/**
 * 获取大客协议配置
 * @returns {Object}
 */
export const getProcolConfig = () => {
  try {
    const { design = [] } = ecloudCustomConfig || {};

    return cloneDeep(design[0].profile || {});
  } catch (_e) {
    return {};
  }
};

/**
 * 是否为 UI 定制
 * @returns {Boolean}
 */
export const isUIComponent = () => {
  return getProcolConfig().protocolEcloudType === 1;
};

/**
 * 获取大客的协议 code 列表
 * @returns {Array}
 */
export const getEcloudCustomProtocolCode = () => {
  const {
    protocolEcloudType,
    shopPrivacyCode,
    showYouzanUserPrivacy,
    showYouzanUserUse,
  } = getProcolConfig();

  // 如果是进行了 UI 定制
  if (protocolEcloudType === 1) {
    const protocolCodes = Array.isArray(shopPrivacyCode)
      ? shopPrivacyCode
      : [shopPrivacyCode];

    if (showYouzanUserPrivacy) {
      protocolCodes.push(PLATFORM_USER_PRIVACY);
    }

    if (showYouzanUserUse) {
      protocolCodes.push(PLATFORM_USER_USE);
    }

    return protocolCodes;
  }

  return [];
};

/**
 * 获取商家的协议 code 列表
 * @description 注意这里是通用的方法，如果是大客 ui 定制的会读取指定的前端配置，否则读取后端接口
 * @returns {Promise<Array>}
 */
export const getProtocolCode = ({ ignoreCache = false } = {}) => {
  const { protocolEcloudType } = getProcolConfig();

  // 如果是进行了 UI 定制
  if (protocolEcloudType === 1) {
    return Promise.resolve(getEcloudCustomProtocolCode());
  }

  return resolveProtocol().then(({ getProtocolData }) =>
    getProtocolData(undefined, { ignoreCache }).then(
      ({ protocolCode }) => protocolCode
    )
  );
};

/**
 * 获取指定协议 code 对应的协议内容
 * @return {Promise} 协议内容
 */
export const getProcolListByCode = () => {
  return new Promise((resolve) => {
    getProtocolCode().then((protocolCode) => {
      resolveProtocol().then(({ getProtocolData }) => {
        getProtocolData(protocolCode).then(({ protocolData }) => {
          resolve(protocolData);
        });
      });
    });
  });
};

/**
 * 签署协议
 */
export const protocolBatchSign = () => {
  return getProcolListByCode().then((protocolList) => {
    return resolveProtocol().then(({ protocolSign }) =>
      protocolSign(protocolList.map((v) => v.agreementTplId))
    );
  });
};

/**
 * 初始化协议组件 data
 */
export const initComponentData = (data) => {
  if (isUIComponent()) {
    const {
      theme,
      agreeBtn,
      buttonBorderRaduis,
      showCancelButton,
      bottomExtHeight,
    } = getProcolConfig();

    data.isUIComponent = true;
    data.themeColors = { 'main-bg': theme, 'main-text': '#fff' };
    data.yunBottomExtHeight = bottomExtHeight || 0;
    data.btnConfig.agreeBtn = agreeBtn;
    data.btnConfig.hideDisagree = !showCancelButton;
    data.btnConfig.customStyle = buttonBorderRaduis
      ? `border-radius: ${buttonBorderRaduis}px !important;`
      : '';
  }

  return data;
};

/**
 * 触发协议组件的大客协议事件
 */
export const triggerEcloudEvent = (ctx, eventName, args = []) => {
  // 非大客定制，不触发事件
  if (!isUIComponent()) {
    return;
  }

  return ctx.triggerAsync(eventName, ...args);
};
