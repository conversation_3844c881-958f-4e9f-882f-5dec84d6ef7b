import YunComponent from '@/youzanyun-sdk/yun-component';

YunComponent({
  properties: {
    authTypeList: {
      type: Array,
      value: [],
    },
    customStyle: String,
    // 触发方式：click (默认)、auto
    trigger: {
      type: String,
      value: 'click',
    },
  },
  externalClasses: ['btn-class'],
  methods: {
    /**
     * 授权成功或不需要授权
     * @param event.detail
     * 需要授权且授权成功返回如下: 没授权项取值为空
     * {
     *   mobile, // 手机号
     *   nickname, // 昵称
     *   avatar, // 头像
     * }
     */
    handleNext(event) {
      this.triggerEvent('next', event.detail);
    },
    /**
     * 授权失败
     * @param typeList 拒绝的授权类型
     * ['protocol', 'mobile', 'nicknameAndAvatar', 'mobileDeny', 'userInfoDeny'] 返回一项或多项
     * 字段解释
     * protocol:            有赞协议拒绝
     * mobile:              有赞手机号拒绝
     * nicknameAndAvatar:   有赞头像昵称拒绝
     * mobileDeny:          微信获取手机号拒绝
     * userInfoDeny:        微信获取头像昵称拒绝
     */
    handleFail(event) {
      const type = event.detail;
      this.triggerEvent('fail', type);
    },
  },
});
