import getApp from 'shared/utils/get-safe-app';
import { getAjaxConfig } from '../../_utils';

const app = getApp();

const Post = function (options = {}, thisArg) {
  const cfg = getAjaxConfig(thisArg);
  // 合并默认选项
  options = {
    config: {
      skipShopInfo: true,
    },
    method: 'POST',
    header: {
      'content-type': 'application/x-www-form-urlencoded',
      ...cfg.header,
    },
    origin: 'uic',
    data: {},
    success: () => {},
    fail: () => {},
    ...options,
  };
  return app.request(options);
};

async function loginByPassword(data, success, fail, thisArg) {
  const crypto = await import('@youzan/crypto');
  data.password = crypto.aes.legacyEncrypt(data.password);
  return Post(
    {
      origin: 'uic',
      path: '/passport/weapp/login/password.json',
      data,
      success: (data) => {
        success(data);
      },
      fail,
    },
    thisArg
  );
}

export default {
  loginByPassword,
};
