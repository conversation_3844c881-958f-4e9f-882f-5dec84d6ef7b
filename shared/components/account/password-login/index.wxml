<view class="container" hover-class="none" hover-stop-propagation="false">
  <view class="title">{{ title }}</view>
  <view class="section">
    <van-field
      type="number"
      value="{{ formData.mobile }}"
      bind:input="bindMobileInput"
      placeholder="请输入手机号"
      maxlength="11"
      clearable
      class="mobile-input"
      custom-style="padding-left:0px !important;"
      border="{{ false }}"
    ></van-field>
  </view>
  <view class="section">
    <van-field
      placeholder-style=""
      custom-style="padding-left:0px !important;"
      type="password"
      value="{{ formData.password }}"
      bind:input="bindPasswordInput"
      placeholder="请输入密码"
      maxlength="30"
      clearable
      border="{{ false }}"
      class="password-input"
    ></van-field>
  </view>
  <view class="login-type-change" catchtap="changeLoginType">验证码登录</view>
  <protocol-field bind:agreementChange="agreementChange" />

  <van-button catchtap="login" class="button" custom-class="login-btn" type="primary" block
              disabled="{{ loginBtn.disabled }}">
    {{ loginBtn.text }}
  </van-button>
</view>
<van-dialog id="van-dialog"/>
