import Dialog from '@vant/weapp/dist/dialog/dialog';
import getApp from 'shared/utils/get-safe-app';
import Services from './services';

const app = getApp();
Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    title: {
      type: String,
      value: '账号密码登录',
    },
    subTitle: {
      type: String,
      value: '为了你的账号安全，请用手机号登录',
    },
    redirectUrl: {
      type: String,
      value: '/packages/account/settings/index',
    },
    isFromAuthComponent: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    formData: {
      countryCode: '+86',
      mobile: '',
      wxMobile: '',
      captcha: '',
      captchaTime: null,
      password: '',
    },
    captcha: {
      text: '获取验证码',
      code: '',
      times: 1,
      countdown: 60,
      textStyle: 'acc-code__btn--enabled',
      btnStyle: '',
      timer: null,
    },
    loginBtn: {
      text: '登录',
      disabled: true,
      wxDisabled: false,
    },
    isProtocolAgreed: false,
  },
  methods: {
    changeLoginType() {
      this.triggerEvent('changeLoginType', { type: 'sms' });
    },
    agreementChange(val) {
      const {
        formData: { password, mobile },
      } = this.data;
      this.setData({
        isProtocolAgreed: val.detail,
        'loginBtn.disabled': !(password && mobile && val.detail), // 按钮状态
      });
    },
    _checkMobile(value) {
      return /^\d{11}$/.test(value);
    },
    _checkPassword(value) {
      return value && value.length >= 6;
    },
    /**
     * @description sms登录
     */
    login() {
      const { disabled } = this.data.loginBtn;
      const { mobile, password, countryCode } = this.data.formData;
      if (disabled) {
        return;
      }
      if (!this._checkMobile(mobile)) {
        return wx.showToast({
          icon: 'none',
          title: '请输入正确的手机号',
        });
      }
      if (!this._checkPassword(password)) {
        return wx.showToast({
          icon: 'none',
          title: '请输入正确的密码',
        });
      }
      const data = {
        countryCode,
        mobile,
        password,
      };

      import('@youzan/behavior-verify/src/miniapp/weapp/main').then(
        ({ default: behaviorVerify }) => {
          behaviorVerify({
            bizType: 10,
            onSuccess: (ticket) => {
              this._beforeLogin();
              // 发送登录
              Services.loginByPassword(
                {
                  ...data,
                  ticket,
                },
                () => {
                  return app.login().then(() => this._loginSuccess());
                },
                (error) => {
                  if (error.code === 135200018 || error.code === 135200019) {
                    return this.configDialog();
                  }
                  this.setData({
                    'loginBtn.disabled': false,
                  });

                  wx.hideLoading();
                  wx.showToast({
                    icon: 'none',
                    title: error.msg || '服务请求出错，请稍后再试',
                  });
                },
                this
              );
            },
          });
        }
      );
    },
    /**
     * @description 登录成功后触发的行为
     */
    _loginSuccess() {
      const detail = {
        mb: this.data.formData.mobile,
        authPopType: 'mobile',
        verifyType: 0,
        hasAuth: true, // 已有有赞授权
      }; // detail对象，提供给事件监听函数
      const option = {}; // 触发事件的选项
      this.triggerEvent('loginSuccess', detail, option);
      this.setData({
        show: false,
      });

      wx.hideLoading();
      return wx.showToast({
        title: '登录成功',
        icon: 'success',
        mask: false,
      });
    },
    // 绑定手机号码输入状态
    // eslint-disable-next-line no-dupe-keys
    bindMobileInput(e) {
      const {
        formData: { password },
        isProtocolAgreed,
      } = this.data;
      this.setData({
        'formData.mobile': e.detail,
        'loginBtn.disabled': !(e.detail && password && isProtocolAgreed), // 按钮状态
      });
    },
    // 绑定验证码输入状态
    bindPasswordInput(e) {
      const {
        formData: { mobile },
        isProtocolAgreed,
      } = this.data;
      this.setData({
        'formData.password': e.detail,
        'loginBtn.disabled': !(e.detail && mobile && isProtocolAgreed), // 按钮状态
      });
    },
    /**
     * @description 触发登录前行为
     */
    _beforeLogin() {
      this.loginLoading();
      this.setData({
        'loginBtn.disabled': true,
      });
    },
    /**
     * @description 手机或微信已和其它账号绑定时，需要唤起弹层提示是否继续登录
     * @param {*} wxMobile
     * @param {*} auth 是否为授权唤起的弹层对话
     */
    configDialog(auth = false, option = {}) {
      let mobile;
      if (auth) {
        if (!option.mobile) {
          return wx.showToast({
            icon: 'none',
            title: '授权获取手机号码失败，请重启小程序重新授权',
          });
        }
        mobile = option.mobile;
      } else {
        if (!this.data.formData.mobile) {
          return wx.showToast({
            icon: 'none',
            title: '请正确填写手机号码',
          });
        }
        mobile = this.data.formData.mobile;
      }

      const blurredMobile = []; // 手机号码脱敏
      blurredMobile.push(mobile.substring(0, 3));
      blurredMobile.push('****');
      blurredMobile.push(mobile.substring(7));

      const message = `手机号${blurredMobile.join('')}已与其他微信账号绑定`;
      Dialog.confirm({
        message,
        confirmButtonText: '继续登录',
        cancelButtonText: '换个手机号',
        context: this,
        zIndex: 99999,
      })
        .then(() => {
          this.confirmLogin(option);
        })
        .catch((error) => {
          console.log(error);
        });
    },
    /**
     * @description 账号存在提醒后，点确认登录触发
     */
    confirmLogin(option = {}) {
      const mobile = option.mobile || this.data.formData.mobile;
      const countryCode = option.countryCode || this.data.formData.countryCode;
      const { redirectUrl } = this.data;

      const url = `/packages/account/to-bind/index?mobile=${mobile}&countryCode=${encodeURIComponent(
        countryCode
      )}&redirectUrl=${encodeURIComponent(redirectUrl)}`;
      return wx.redirectTo({
        url,
      });
    },
    /**
     * 验证码输入倒计时读秒
     */
    _countDownForCaptchaCode() {
      let { countdown } = this.data.captcha;
      if (countdown === 0) {
        this.setData({
          'captcha.countdown': 60,
          'captcha.text': '重新发送',
          'captcha.btnStyle': '',
          'captcha.textStyle': 'acc-code__btn--enabled',
        });
        return;
      }
      countdown--;

      this.setData({
        'captcha.countdown': countdown,
        'captcha.text': '已发送(' + countdown + 's)',
      });

      this.data.captcha.timer = setTimeout(() => {
        this._countDownForCaptchaCode();
      }, 1000);
    },
    loginLoading(title = '正在登录...') {
      wx.showLoading({
        title,
        mask: true,
      });
    },
  },
});
