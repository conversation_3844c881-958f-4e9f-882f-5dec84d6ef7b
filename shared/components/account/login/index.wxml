<van-popup
  position="bottom"
  zIndex="10000"
  show="{{ show }}"
  custom-class="mobile-popup"
  custom-style="padding-bottom: {{ popupBottom }}px"
  round="{{ true }}"
  bind:close="onClose"
>
  <view class="account-login-dialog">
    <view wx:if="{{ !forbidClose }}" class="close-icon" catchtap="onClose">
      <van-icon name="cross" />
    </view>
    <sms-login
      wx:if="{{ loginType === 'sms'}}"
      redirectUrl="{{ redirectUrl }}"
      bind:loginSuccess="loginSuccess"
      bind:loginFail="loginFail"
      bind:changeLoginType="changeLoginType"
      refresh="{{ refresh }}"
      is-from-auth-component="{{ isFromAuthComponent }}"
    ></sms-login>
    <password-login
      wx:else
      redirectUrl="{{ redirectUrl }}"
      bind:loginSuccess="loginSuccess"
      bind:loginFail="loginFail"
      bind:changeLoginType="changeLoginType"
      refresh="{{ refresh }}"
      is-from-auth-component="{{ isFromAuthComponent }}"
    ></password-login>
    <view class="copyright">有赞提供技术支持</view>
  </view>
</van-popup>
<behavior-verify id="behavior-verify" bind:behaviorComponentReady="behaviorComponentReady"></behavior-verify>
