## 弹窗登录组件使用说法
### 组件引入
在 app.json 或 index.json 中引入组件

```
"usingComponents": {
  "account-login": "shared/components/account/login/index"
}

```

### Options
* show
	* 类型: `Bolean`
	* 默认值：`false`
	* 说明：是否展示弹窗
* loginType
	* 类型:`String`
	* 默认值:`sms`
	* 说明 :打开的登录方式，默认验证码登录，可选值为 `password`
* redirectUrl
	* 类型: String
	* 默认值:/packages/account/settings/index
	* 说明 : 当帐流登录流程触发账号合并等流程，合并成功后回跳地址page
* refresh
	* 类型:`Bolean`
	* 默认值:`true`
	* 说明 : 登录成功后，是否刷新当前页面(page)，可选项`false`,不刷新


### Event
* bind:loginSuccess
	* 说明：登录成功后回调
	* 回调参数：`event`
* bind:loginFail
	* 说明：登录失败后回调
	* 回调参数：`event`，event.detail登录失败原因