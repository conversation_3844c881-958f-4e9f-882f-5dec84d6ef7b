import tabBehavior from '@/custom-tab-bar-v2/tab-behavior';

Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    loginType: {
      type: String,
      value: 'sms',
    },
    redirectUrl: {
      type: String,
      value: '/packages/account/settings/index',
    },
    refresh: {
      type: Boolean,
      value: true,
    },
    forbidClose: {
      type: Boolean,
      value: true,
    },
    isFromAuthComponent: {
      type: Boolean,
      value: false,
    },
  },
  data: {},
  behaviors: [tabBehavior],
  lifetimes: {
    attached() {},
  },
  methods: {
    behaviorComponentReady() {
      import('@youzan/behavior-verify/src/miniapp/weapp/main').then(
        ({ default: behaviorVerify }) => {
          behaviorVerify.init(this);
        }
      );
    },
    /**
     * @description 关闭弹层窗口
     */
    onClose() {
      this.setData({
        show: false,
      });
      this.triggerEvent('closeAccountLogin');
    },
    changeLoginType(e) {
      this.setData({
        loginType: e.detail.type,
      });
    },
    loginSuccess(event) {
      this.triggerEvent('loginSuccess', event.detail);
    },
    loginFail(event) {
      this.triggerEvent('loginFail', event.detail);
    },
  },
});
