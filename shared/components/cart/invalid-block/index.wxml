<view class="invalid-block custom-class" wx:if="{{ goodsList.length }}">
  <view class="goods-header--wrapper">
    <view class="goods-header">
      <view class="goods-hader--title">失效商品</view>
      <form-view>
        <view
          class="goods-header--btn van-hairline--surround"
          bind:tap="clearGoods"
        >
          清空失效商品
        </view>
      </form-view>
    </view>
  </view>

  <view class="goods-list">
    <goods-item
      custom-class="valid-goods-item"
      wx:for="{{ goodsList }}"
      wx:for-item="goods"
      wx:key="{{ index }}"
      goods="{{ goods }}"
      theme-main-bg-color="{{ themeMainBgColor }}"
      is-can-stepper="{{ false }}"
      is-can-choose="{{ false }}"
      is-invalid="{{ true }}"
      bind:item-delete="handleItemDelete"
    />
  </view>
</view>