Component({
  externalClasses: ['custom-class'],

  properties: {
    goodsList: {
      type: Array
    },
    themeMainBgColor: String,
  },

  methods: {
    clearGoods() {
      this.triggerEvent('clear');
    },

    handleGoodsTap({ currentTarget }) {
      const { alias } = currentTarget.dataset;
      if (!alias) return;
      this.triggerEvent('goods-img-click', { alias });
    },

    handleItemDelete({ detail }) {
      const { cartId } = detail;
      const { goods, goodsIndex, goodsGroupIndex } = this._findGoodsInList(cartId);

      const eventData = {
        goods,
        goodsIndex,
        goodsGroupIndex,
        isUnAvailable: true
      };

      console.log('eventData', eventData);

      this.triggerEvent('change-item-delete', eventData);
    },

    _findGoodsInList(cartId) {
      const { goodsList } = this.data;
      let goodsIndex = -1;
      let goods;
      goodsList.forEach((item, index) => {
        if (item.cartId === cartId) {
          goodsIndex = index;
          goods = item;
        }
      });
      return {
        goodsGroupIndex: -1,
        goodsIndex,
        goods,
      };
    },
  }
});
