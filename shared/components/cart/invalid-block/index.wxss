@import '@vant/weapp/dist/common/index.wxss';

.invalid-block {
  margin: 0 12px;
  border-radius: 8px;
  overflow: hidden;
}

.goods-header {
  align-items: center;
  display: flex;
  font-size: 14px;
  line-height: 1.4;
  padding: 12px 15px 12px 0;
  position: relative;
  background: #fff;
  justify-content: space-between;
  margin-left: 15px;
}

.goods-header--wrapper {
  background: #fff;
}

.goods-header--btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 88px;
  height: 26px;
  border-radius: 26px;
  font-size: 12px;
  box-sizing: border-box;
  color: #111;
}

.goods-header--btn::after {
  border-radius: 26px;
}