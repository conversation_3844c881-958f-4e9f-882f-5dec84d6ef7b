<theme-view class="sc-sku-price__show" color="{{ themeGeneral }}">
  <text class="sc-sku-price__symbol">￥</text><text class="sc-sku-price__num">{{ price }}</text>
  <view
    wx:if="{{ util.comparePriceString(originPrice, price) }}"
    class="sc-sku-price__origin"
  >
    {{ originPrice }}
  </view>
</theme-view>

<wxs module="util">
  module.exports.comparePriceString = function(originPrice, price) {
    return (+originPrice || 0) > (+price || 0)
  }
</wxs>