import WscComponent from 'shared/common/base/wsc-component/index';
import theme from 'shared/common/components/theme-view/theme';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import api from '../api';
import get from '@youzan/weapp-utils/lib/get';

function parsePrice(price) {
  return typeof price === 'number' ? Number(price / 100).toFixed(2) : price;
}

WscComponent({
  properties: {
    goods: {
      type: Object,
    },
    sku: {
      type: Object,
      observer: 'setPrice',
    },
    selectedSkuComb: {
      type: Object,
      observer: 'setPrice',
    },
    extraData: {
      type: Object,
      observer: 'setPrice',
    },
  },

  data: {
    themeGeneral: '',
    price: '',
    originPrice: '',
  },

  ready() {
    theme.getThemeColor('general').then((color) => {
      this.setYZData({
        themeGeneral: color,
      });
    });
  },

  methods: {
    // 异步计算商品属性价格
    onAsyncPropPrice(selectedSkuComb) {
      const { goods } = this.data;

      api
        .handleAsyncPropPriceApiDebounce({
          itemId: goods.goodsId,
          kdtId: goods.kdtId,
          skuId: selectedSkuComb.id,
          itemSalePropList: JSON.stringify(
            mapKeysCase.toCamelCase(selectedSkuComb.properties || [])
          ),
          activity: '{}',
        })
        .then((res) => {
          const price = get(res, 'umpTotalPrice', 0);
          const totalPrice = get(res, 'totalPrice', 0);

          this.setYZData({
            price: parsePrice(price),
            originPrice: parsePrice(totalPrice),
          });
        });
    },

    setPrice() {
      const { sku, selectedSkuComb, extraData } = this.properties;

      if (extraData && extraData.asyncPropPrice && selectedSkuComb) {
        this.onAsyncPropPrice(selectedSkuComb);
        return;
      }

      if (!sku) {
        return;
      }

      // sku.price是一个格式化好的价格区间
      let { price = '', originPrice = '' } = sku;

      if (selectedSkuComb) {
        const {
          price: _price,
          originPrice: _originPrice,
          oldPrice: _oldPrice,
        } = selectedSkuComb;

        if (_price) {
          price = parsePrice(_price);
        }

        if (_originPrice || _oldPrice) {
          originPrice = parsePrice(_originPrice || _oldPrice);
        }
      }

      this.setYZData({ price, originPrice });
    },
  },
});
