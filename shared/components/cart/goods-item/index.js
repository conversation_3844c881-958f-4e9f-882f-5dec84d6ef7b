import Dialog from '@vant/weapp/dist/dialog/dialog';
import Toast from '@vant/weapp/dist/toast/toast';
import { isCheckboxEnabled } from '../utils';

Component({
  externalClasses: ['custom-class'],

  properties: {
    goods: {
      type: Object,
      observer: 'formatData'
    },
    editMode: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'editModeWatcher'
    },
    activityType: String,
    // 是否可以选中
    isCanChoose: {
      type: Boolean,
      value: true
    },
    // 是否可以删除
    isCanDelete: {
      type: Boolean,
      value: true
    },
    // 是否可以调整数量
    isCanStepper: {
      type: Boolean,
      value: true
    },
    // 是否是活动商品
    isActivity: {
      type: Boolean,
      default: false
    },
    // 是否是附属商品
    isSub: {
      type: Boolean,
      default: false
    },
    // 是否是失效商品
    isInvalid: {
      type: Boolean,
      default: false
    },
    // 是否隐藏活动商品
    hideTag: {
      type: Boolean,
      value: false
    },
    index: {
      type: Number,
      value: 0
    },
    total: {
      type: Number,
      value: 0
    },
    hasPresent: {
      type: <PERSON><PERSON><PERSON>,
      value: false
    },
    themeMainBgColor: String,
    themeMainBgAlpha10Color: String
  },

  data: {
    iconName: '',
    iconStyle: '',
    startSaleNum: 0, // 起售数量
    checkboxEnabled: true,
    isNotStartSold: false // 是否开售
  },

  methods: {
    formatData(goods) {
      const startSaleNum = this.computedStartSaleNum(goods.startSaleNum);
      const checkboxEnabled = isCheckboxEnabled(goods, this.data.editMode);
      const iconStyle = this.computedIconStyle(checkboxEnabled);
      const iconName = this.computedIconName(goods, checkboxEnabled);
      const isNotStartSold = this.computedStartSold(goods);

      this.setData({
        iconName,
        iconStyle,
        startSaleNum,
        checkboxEnabled,
        isNotStartSold
      });
    },

    editModeWatcher() {
      console.log('editModeWatcher');
      const { editMode, goods } = this.data;
      const checkboxEnabled = isCheckboxEnabled(goods, editMode);
      const iconStyle = this.computedIconStyle(checkboxEnabled);

      this.setData({
        checkboxEnabled,
        iconStyle
      });
    },

    computedStartSaleNum(startSaleNum = 0) {
      const { activityType } = this.data;

      if (activityType === 'plusBuy') {
        // 加价购不限制起售
        return 0;
      }
      return startSaleNum;
    },

    computedIconStyle(checkboxEnabled = true) {
      let iconStyle = '';

      if (!checkboxEnabled) {
        iconStyle = 'background-color: #ebedf0; border-radius: 18px; color: #c8c9cc;';
      }

      return iconStyle;
    },

    computedIconName(goods, checkboxEnabled) {
      if (goods.checked && !checkboxEnabled) {
        // 选中不可选商品，先取消选中
        setTimeout(() => {
          this.handleCheckboxTap(false);
        });
      }

      if (goods.checked && checkboxEnabled) {
        return 'checked';
      }

      return 'circle';
    },

    computedStartSold(goods) {
      const nowTime = new Date().getTime() / 1000;
      let isNotStartSold = false;
      if (goods.startSoldTime && goods.startSoldTime > nowTime) {
        isNotStartSold = true;
      }
      return isNotStartSold;
    },

    /**
     *
     * @param {boolean} isVerifyCheckboxEnabled 是否校验 当前是否可选
     */
    handleCheckboxTap(isVerifyCheckboxEnabled = true) {
      if (!this.data.isCanChoose || (isVerifyCheckboxEnabled && !this.data.checkboxEnabled)) {
        // 不能低于起售
        if (this.data.goods.startSaleNum && this.data.goods.num < this.data.goods.startSaleNum) {
          return Toast(`该商品${this.data.goods.startSaleNum}件起售哦`);
        }

        // 待开售商品不能选中
        if (this.data.isNotStartSold) {
          return Toast(this.data.goods.disableSelectMsg);
        }
        return;
      }
      const { checked, cartId } = this.data.goods;
      const { isActivity } = this.data;
      const eventData = {
        checked: !checked,
        cartId,
        isActivity
      };

      this.triggerEvent('item-checked', eventData);
    },

    handleItemNumChange({ detail }) {
      const { isActivity } = this.data;
      this.triggerEvent('item-num-change', {
        ...detail,
        isActivity
      });
    },

    handleChangeGoodsSku() {
      this.triggerEvent('change-goods-sku', {
        goods: this.data.goods
      });
    },

    handleItemDelete({ detail }) {
      const { instance, position } = detail;
      const { isActivity, editMode } = this.data;
      if (position === 'cell') return instance.close();
      const eventData = {
        cartId: this.data.goods.cartId,
        isActivity
      };
      if (editMode) {
        Dialog.confirm({
          message: '确定要删除这个商品么?'
        })
          .then(() => {
            this.triggerEvent('item-delete', eventData);
            instance.close();
          })
          .catch(() => {
            instance.close();
          });
      } else {
        this.triggerEvent('item-delete', eventData);
        instance.close();
      }
    },
  }
});
