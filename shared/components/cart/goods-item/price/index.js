import WscComponent from 'shared/common/base/wsc-component/index';
import money from '@youzan/weapp-utils/lib/money';

WscComponent({
  externalClasses: ['custom-class', 'origin-class'],

  properties: {
    price: {
      type: String,
      optionalTypes: [Number],
      observer: 'formatPrice'
    },
    originPrice: {
      type: String,
      optionalTypes: [Number],
      observer: 'formatOriginPrice'
    }
  },

  data: {
    formatPriceArr: [],
    originPriceCN: ''
  },

  methods: {
    formatOriginPrice(val) {
      if (!val) return;

      let originPriceCN = '';
      if (val > this.data.price) {
        originPriceCN = money(val).toYuan();
      } else {
        this.setYZData({ originPriceCN: '' });
        return;
      }

      const formatPriceArr = originPriceCN.split('.');
      // 小数去 0
      const decimalReverse = +formatPriceArr[1].split('').reverse().join('');
      formatPriceArr[1] = decimalReverse.toString().split('').reverse().join('');

      if (!+formatPriceArr[1]) {
        formatPriceArr.splice(1, 1);
      }

      this.setYZData({
        originPriceCN: formatPriceArr.join('.')
      });
    },

    formatPrice(val) {
      let newPrice = val.toString();
      if (newPrice.indexOf(',') === -1) {
        newPrice = money(newPrice).toYuan();
      }

      const formatPriceArr = newPrice.split('.');
      // 小数去 0
      const decimalReverse = +formatPriceArr[1].split('').reverse().join('');
      formatPriceArr[1] = decimalReverse.toString().split('').reverse().join('');

      if (!+formatPriceArr[1]) {
        formatPriceArr.splice(1, 1);
      }

      this.setYZData({
        formatPriceArr
      });
    }
  }
});
