<view class="goods-item {{ isInvalid ? 'goods-item_invalid' : '' }}" catchtap="handleGoodsImgTap">
  <view class="goods-img">
    <image
      src="{{ goods.imgUrl }}"
      mode="aspectFit"
    />
  </view>

  <view class="goods-item--right">
    <view 
      wx:if="{{ !editMode }}" 
      class="{{ goods.title ? 'goods-title' : 'goods-title-not-exist'}} {{ isSub ? 'van-multi-ellipsis--l1' : 'van-multi-ellipsis--l2' }}"
    >
      <image 
        wx:if="{{ goods.settlementRule && GOODS_TAG_MAP[goods.settlementRule.settlementMark] }}" 
        class="goods-title-tag {{ goods.settlementRule.settlementMark }}"
        src="{{ GOODS_TAG_MAP[goods.settlementRule.settlementMark] }}"
      />
      <text class="goods-title-text">{{ goods.title }}</text>
    </view>

    <block wx:else>
      <view 
        class="goods-title {{ isSub ? 'van-multi-ellipsis--l1' : 'van-multi-ellipsis--l2' }}"
      >
        <text class="goods-title-text">{{ goods.title }}</text>
      </view>
    </block>
    
    <!-- 非待复活商品 -->
    <block wx:if="{{ !goods.revive }}">
      <view wx:if="{{ goods.sku && !isInvalid }}" class="goods-sku van-ellipsis {{ !!tariffPrice ? mb16 : ''  }}">
        <view class="goods-sku-container {{ !isCanChangeSku ? 'goods-sku-container-normal' : '' }}" catchtap="handleChangeGoodsSku">
          <text>{{ goods.sku }}</text>
          <van-icon wx:if="{{ isCanChangeSku }}"  name="arrow-down" custom-class="goods-sku-arrow" />
        </view>
      </view>

      <sale-count-down
        wx:if="{{ isNotStartSold && !isInvalid }}"
        start-sold-time="{{ goods.startSoldTime }}"
        bind:hide-countdown="hideCountdown"
      />
      
      <view wx:if="{{ !isInvalid && goods.isSevenDayUnconditionalReturn }}" class="seven-return">
        7天无理由退货
      </view>

      <view class="goods-into-bottom" style="{{ priceStyle }}">
        <!-- 商品图片标 -->
        <view wx:if="{{ !isInvalid && goods.yzGuarantee }}" class="cart-goods__img-tags">
          <!-- 有赞担保 -->
          <image
            src="{{ GOODS_IMG_TAG_MAP.SECURED }}"
            alt="有赞担保"
            class="cart-goods__secured"
          />
        </view>

        <view 
          wx:if="{{ !isInvalid && goodsTagList.length }}" 
          class="goods-tags"
        >
          <van-tag
            wx:for="{{ goodsTagList }}"
            wx:key="index"
            round
            plain="{{ isPlainTag }}"
            color="{{ themeMainBgAlpha10Color }}"
            text-color="{{ themeMainBgColor }}"
            custom-class="goods-tags__tag"
          >
            {{ item }}
          </van-tag>
        </view>

        <theme-view
          wx:if="{{ !isInvalid && isStockLess }}"
          color="main-bg"
          custom-class="stock-less"
        >
          库存紧张
        </theme-view>

        <theme-view wx:if="{{ !isInvalid && startSaleNumAndLimitDesc }}" color="main-bg" custom-class="start-sale-num">
          {{ startSaleNumAndLimitDesc }}
        </theme-view>

        <theme-view wx:if="{{ !isInvalid && !!tariffPrice }}" color="main-bg" custom-class="goods-tax">
          <text>进口税：{{ tariffPrice }}</text>
        </theme-view>
        
        <theme-view wx:if="{{ !isInvalid && !!cutPriceDesc }}" color="main-bg" custom-class="goods-cut-price">
          <text>{{ cutPriceDesc }}</text>
        </theme-view>

        <view 
          wx:if="{{ !isInvalid && goods.price }}" 
          class="goods-bottom {{ isCanStepper ? 'align-top' : '' }}" 
        >
          <view class="goods-price {{ isCanStepper ? 'pt6' : '' }}" style="{{ priceColorStyle }}" >
            <price 
              price="{{ goods.price }}"
              originPrice="{{ goods.originPrice }}"
              origin-class="{{ isCanStepper ? 'origin-block' : '' }}"
            />
          </view>

          <view class="goods-num">
            <van-stepper
              wx:if="{{ isCanStepperFormater }}"
              custom-class="stepper"
              value="{{ goods.num }}"
              integer
              min="1"
              step="1"
              catchtap="noopFn"
              long-press="{{ false }}"
              disable-minus="{{ stepperDisableMinus }}"
              disable-plus="{{ stepperDisablePlus }}"
              bind:change="handleStepChange"
              bind:overlimit="handleStepOverLimit"
              bind:plus="handleStepPlus"
              bind:minus="handleStepMinus"
            />

            <view wx:else>
              x{{ goods.num }}
            </view>
          </view>
        </view>

        <view wx:if="{{ goods.errorMsg }}" class="err-msg">
          {{ goods.errorMsg }}
        </view>
      </view>
    </block>
    <!-- 待复活商品 -->
    <block wx:else>
      <view class="goods-revive" style="{{ priceStyle }}">
        <text>请重新选择商品规格</text>

        <van-tag
          color="{{ themeMainBgAlpha10Color }}"
          text-color="{{ themeMainBgColor }}"
          round
          plain
          catch:tap="handleChangeGoodsSku"
        >
          重选
        </van-tag>
      </view>
    </block>

  </view>
</view>