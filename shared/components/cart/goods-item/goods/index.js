import Toast from '@vant/weapp/dist/toast/toast';
import WscComponent from 'shared/common/base/wsc-component/index';
import money from '@youzan/weapp-utils/lib/money';

const app = getApp();

const GOODS_IMG_TAG_MAP = {
  // 有赞担保
  SECURED: 'https://b.yzcdn.cn/guarantee/<EMAIL>',
};

const GOODS_TAG_MAP = {
  HAITAO:
    'https://img01.yzcdn.cn/public_files/2019/08/19/fbd4c38994578e951ef1cdfd9104606d.png', // 海淘
  PERIOD_BUY:
    'https://img01.yzcdn.cn/public_files/2019/08/19/aea27fff45f6edb02bfd31c0b7ff3f04.png', // 周期购
  MEMBER_DISCOUNT: 'https://b.yzcdn.cn/cdn/FkhVnpHh7ZwFAvBaUwO8B0F2Gf4V-1.png', // 会员折扣
};

const ACTIVITY_TAGS = ['换购', '赠品'];

WscComponent({
  properties: {
    goods: {
      type: Object,
      observer: 'formatData',
    },
    editMode: Boolean,
    // 是否隐藏活动商品
    hideTag: Boolean,
    // 是否可以调整数量
    isCanStepper: {
      type: Boolean,
      value: true,
    },
    // 是否是附属商品
    isSub: {
      type: Boolean,
      value: false,
    },
    // 是否是失效商品
    isInvalid: {
      type: Boolean,
      value: false,
    },
    themeMainBgColor: {
      type: String,
      observer: 'computedColor',
    },
    themeMainBgAlpha10Color: {
      type: String,
      observer: 'computedColor',
    },
    // 起售件数
    startSaleNum: {
      type: Number,
      value: 0,
    },
  },

  data: {
    GOODS_TAG_MAP,
    GOODS_IMG_TAG_MAP,
    tariffPrice: '',
    cutPriceDesc: '', // 降价提示
    startSaleNumAndLimitDesc: '', // 起售和限购描述
    isStockLess: false, // 库存紧张
    priceStyle: '',
    priceColorStyle: '',
    isPlainTag: true,
    stepperDisableMinus: false,
    stepperDisablePlus: false,
    isMemberDiscount: false, // 是否是会员折扣
    isNotStartSold: false,
  },

  methods: {
    handleChangeGoodsSku() {
      if (this.data.isCanChangeSku || this.data.goods.revive) {
        this.triggerEvent('change-goods-sku');
      }
    },

    computedColor() {
      const { themeMainBgColor, goods } = this.data;
      const { activityTag } = goods;

      let isPlainTag = true;

      if (ACTIVITY_TAGS.indexOf(activityTag) === -1) {
        isPlainTag = false;
      }

      this.setYZData({
        isPlainTag,
        priceColorStyle: `color: ${themeMainBgColor};`,
      });
    },

    computedPriceStyle() {
      this.setYZData({
        priceStyle: '',
      });
      const query = this.createSelectorQuery();
      const goodsRight = query.select('.goods-item--right');
      goodsRight
        .boundingClientRect((res) => {
          if (!res) return;
          const height = res.height;
          if (height < 96) {
            this.setYZData(
              {
                priceStyle: `padding-top: ${96 - height}px`,
              },
              { immediate: true }
            );
          }
        })
        .exec();
    },

    formatData(goods) {
      const { stock } = goods;

      this.setYZData({
        tariffPrice: this.computedTariffPrice(goods),
        goodsTagList: this.computedGoodsTagList(goods),
        isMemberDiscount: this.computedIsMemberDiscount(goods),
        isStockLess: stock < 10,
        stepperDisablePlus: this.computedStepperDisablePlus(goods),
        stepperDisableMinus: this.computedStepperDisableMinus(goods),
        startSaleNumAndLimitDesc: this.computedStartSaleNumAndLimitDesc(goods),
        cutPriceDesc: this.computedCutPrice(goods),
        isCanChangeSku: this.computedIsCanChangeSku(goods),
        isCanStepperFormater: this.isCanStepperComputed(goods),
        isNotStartSold: this.computedStartSold(goods),
      });
    },

    isCanStepperComputed(goods) {
      const { isCanStepper } = this.data;
      const isPlusBuyGoods = +goods.activityType === 24;

      return isCanStepper && !isPlusBuyGoods;
    },

    computedIsCanChangeSku(goods) {
      const { startSaleNum } = this.data;
      const isNotMeetStartSaleNum = !!startSaleNum && goods.num < startSaleNum;
      const isPlusBuyGoods = +goods.activityType === 24;

      return (
        !isPlusBuyGoods &&
        !isNotMeetStartSaleNum &&
        !this.data.isSub &&
        !goods.canyinId
      );
    },

    computedCutPrice(goods) {
      const { cutPrice } = goods;

      if (!+cutPrice) return '';

      const toYuan = money(cutPrice).toYuan();

      return `比加入时便宜${toYuan}元`;
    },

    computedStartSaleNumAndLimitDesc(goods) {
      const { startSaleNum = 0 } = this.data;
      const { limitNum = 0 } = goods;
      let desc = '';

      if (startSaleNum > 1 && limitNum) {
        desc = `${startSaleNum}件起售，限购${limitNum}件`;
      } else if (startSaleNum > 1) {
        desc = `${startSaleNum}件起售`;
      }

      return desc;
    },

    computedIsMemberDiscount(goods) {
      const { activityTag } = goods;

      return activityTag && activityTag === '会员折扣';
    },

    computedGoodsTagList(goods) {
      const { activityTag } = goods;

      const goodsTagList = [];

      this.isMemberDiscount && goodsTagList.push('会员价');

      if (activityTag && activityTag !== '会员折扣') {
        goodsTagList.push(activityTag);
      }

      return goodsTagList;
    },

    computedStepperDisableMinus(goods) {
      const { startSaleNum } = this.data;
      return !!startSaleNum && goods.num <= startSaleNum;
    },

    computedStepperDisablePlus(goods) {
      const { maxNum } = goods;
      return !!maxNum && goods.num >= maxNum;
    },

    computedTariffPrice(goods) {
      const { tariffRule, tariffPrice, num } = goods;

      let msg = '';
      if (tariffRule === 0) {
        msg = `预计 ￥ ${((+tariffPrice * num) / 100).toFixed(2)}`;
      } else if (tariffRule === 1) {
        msg = '商品已含税';
      } else {
        msg = '';
      }

      return msg;
    },

    computedStartSold(goods) {
      const nowTime = new Date().getTime() / 1000;
      const { startSoldTime } = goods;
      let isNotStartSold = false;
      if (startSoldTime && startSoldTime > nowTime) {
        isNotStartSold = true;
      }
      return isNotStartSold;
    },

    handleStepChange(e) {
      const eventData = {
        cartId: this.data.goods.cartId,
        num: +e.detail,
      };

      this.triggerEvent('item-num-change', eventData);
    },

    handleStepPlus() {
      this.handleStepPlusorMinusLogger(true);
    },
    handleStepMinus() {
      this.handleStepPlusorMinusLogger(false);
    },

    handleStepPlusorMinusLogger(isPlus) {
      const { sku, skuData, skuId, goodsId, title, num } = this.data.goods;
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: `cart_${isPlus ? 'increase' : 'decrease'}_goods_num`,
          en: `购物车页面-${isPlus ? '增加' : '减少'}商品数量`,
          params: {
            no_sku: sku ? 0 : 1,
            sku_id: skuId,
            sku_name: skuData,
            goods_id: goodsId,
            goods_name: title,
            num: isPlus ? num + 1 : num - 1,
          },
        });
    },

    handleGoodsImgTap() {
      if (this.data.editMode) return;
      const { alias } = this.data.goods;
      this.triggerEvent(
        'goods-img-click',
        { alias },
        { bubbles: true, composed: true }
      );
    },

    handleStepOverLimit({ detail }) {
      const { goods, stepperDisableMinus, startSaleNum } = this.data;

      if (stepperDisableMinus) {
        Toast(`该商品${startSaleNum}件起售哦`);
      } else if (detail === 'minus') {
        Toast('最少购买1件哦');
      } else if (!goods.limitNum || goods.limitNum > goods.stockNum) {
        Toast('该商品不能购买更多哦');
      } else {
        Toast(`商品限购${goods.limitNum}件`);
      }
    },

    hideCountdown() {
      this.trigger('change-start-sold-time', this.data.goods);
    },

    noopFn() {},
  },

  ready() {
    this.computedPriceStyle();
  },
});
