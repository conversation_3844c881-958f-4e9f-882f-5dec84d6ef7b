import WscComponent from 'pages/common/wsc-component/index';
import CountDown from 'utils/countdown';

const TIME_MAP = {
  DAY: 'day',
  HOUR: 'hour',
  MIN: 'min',
  SEC: 'sec'
};
const countdownData = [
  {
    type: TIME_MAP.DAY,
    value: '',
    text: '天',
    width: '0'
  },
  {
    type: TIME_MAP.HOUR,
    value: '',
    text: '时',
    width: '0'
  },
  {
    type: TIME_MAP.MIN,
    value: '',
    text: '分',
    width: '0'
  },
  {
    type: TIME_MAP.SEC,
    value: '',
    text: '秒',
    width: '0'
  }
];

WscComponent({
  properties: {
    startSoldTime: Number
  },

  data: {
    descArray: []
  },

  lifetimes: {
    attached() {
      this.saleCountDown();
    },
    detached() {
      this.stopCountDown();
    }
  },

  methods: {
    stopCountDown() {
      this.sendedCountDown && this.sendedCountDown.stop();
    },

    // 待开售商品倒计时
    saleCountDown() {
      const nowTime = new Date().getTime() / 1000;
      const successTime = this.data.startSoldTime;
      const leftTime = successTime > nowTime ? successTime - nowTime : 0;

      this.sendedCountDown = new CountDown(leftTime * 1000, {
        onChange: (timeData, strData) => {
          const { day, hour, minute, second } = strData;
          const count = countdownData.map((item) => {
            let value = '';
            switch (item.type) {
              case TIME_MAP.DAY:
                value = day;
                break;
              case TIME_MAP.HOUR:
                value = hour;
                break;
              case TIME_MAP.MIN:
                value = minute;
                break;
              default:
                value = second;
                break;
            }
            return {
              ...item,
              width: `${value.length * 8}px`,
              value
            };
          });
          let descArray = [];
          if (day > 0) {
            descArray = count.slice(0);
          } else if (hour == 0 && minute == 0 && second == 0) {
            this.triggerEvent('hide-countdown');
          } else {
            const countdown = [hour, minute, second];
            for (let i = 0; i < 3; i++) {
              if (countdown[i] != 0) {
                descArray = count.slice(i + 1);
                break;
              }
            }
          }
          this.setYZData({
            descArray
          });
        }
      });
    }
  }
});
