.goods-item {
  min-height: 96px;
  padding: 12px 12px 12px 0;
  margin-left: 44px;

  &_invalid {
    margin-left: 15px;
  }
}

.goods-img {
  float: left;
  height: 96px;
  width: 96px;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  background-size: cover;
  border-radius: 8px;
  overflow: hidden;

  image {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    left: 0;
    height: auto;
    max-height: 100%;
    max-width: 100%;
    width: auto;
    background-color: #fff;
  }
}

.goods-tags {
  margin-bottom: 8px;
  font-size: 0;

  &__tag {
    font-size: 12px;
    line-height: 16px;
    height: 16px;
    padding: 0 4px;
    margin-right: 8px;
    vertical-align: middle;
  }
}

.goods-title {
  margin-bottom: 8px;
}

.goods-title-not-exist {
  margin-bottom: 0;
}

.goods-title-text {
  font-size: 14px;
  color: #323233;
  line-height: 20px;
  vertical-align: middle;
}

.goods-title-tag {
  margin-right: 2px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;

  &.HAITAO {
    width: 28px;
  }

  &.PERIOD_BUY {
    width: 38px;
  }
}

.goods-sku {
  margin-bottom: 8px;
  line-height: 12px;

  &.mb16 {
    margin-bottom: 16px;
  }
}

.goods-sku-container {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  padding: 4px 8px;
  background: #f7f8fa;
  border-radius: 4px;
  font-size: 0;
  color: #969799;

  text {
    font-size: 12px;
    margin-right: 10px;
  }

  .goods-sku-arrow {
    font-size: 12px;
    height: 12px;
    line-height: 12px;
    vertical-align: middle;
  }
}

.goods-sku-container-normal {
  padding: 0;
  background: transparent;

  text {
    margin: 0;
  }
}

.goods-item--right {
  position: relative;
  margin-left: 104px;
}

.goods-bottom {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  flex-wrap: wrap;
}

.align-top {
  align-items: flex-start;
}

.origin-block {
  display: block;
  margin-left: 0;
  margin-top: 4px;
}

.goods-price {
  display: inline-block;
  font-size: 14px;
  color: #f44;
}

.pt6 {
  padding-top: 6px;
}

.goods-num {
  color: #666;
  font-size: 12px;
  flex-shrink: 0;
}

.goods-tax {
  font-size: 12px;
  line-height: 16px;
  color: #f44;
  margin-bottom: 8px;
}

.seven-return {
  font-size: 12px;
  color: #faab0c;
  letter-spacing: 0;
  line-height: 18px;
  margin-bottom: 8px;
}

.activity-tag {
  background: #f44;
  border-bottom-right-radius: 8px;
  border-top-right-radius: 8px;
  bottom: 10%;
  color: #fff;
  display: inline-block;
  font-size: 10px;
  padding: 0 4px;
  position: absolute;
  z-index: 1;
}

.stock-less,
.start-sale-num {
  font-size: 12px;
  line-height: 16px;
  color: #f44;
  margin-bottom: 8px;
}

.err-msg {
  font-size: 12px;
  color: #323233;
  line-height: 16px;
}

.cart-goods {
  &__img-tags {
    margin-bottom: 8px;
    padding-top: 8px;
  }

  &__secured {
    height: 14px;
    width: 62px;
  }
}

.goods-cut-price {
  font-size: 12px;
  line-height: 16px;
  color: #f44;
  letter-spacing: 0;
  margin-bottom: 8px;
}

.goods-revive {
  font-size: 12px;
  color: #323233;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
