<view class="goods-item custom-class" id="cart-id-{{ goods.cartId }}">
  <van-swipe-cell
    right-width="{{ isCanDelete ? 64 : 0 }}"
    async-close
    bind:close="handleItemDelete"
  >
    {{ goods.skeleton ? 'skeleton' : '' }}
    <form-view  wx:if="{{ isCanChoose }}">
      <van-icon
        class="checkbox-wrap"
        size="18px"
        color="{{ themeMainBgColor }}"
        name="{{ iconName }}"
        custom-style="{{ iconStyle }}"
        catch:tap="handleCheckboxTap"
      />
    </form-view>

    <goods
      goods="{{ goods }}"
      edit-mode="{{ editMode }}"
      activity-type="{{ activityType }}"
      is-can-stepper="{{ isCanStepper }}"
      is-invalid="{{ isInvalid }}"
      is-sub="{{ isSub }}"
      hide-tag="{{ hideTag }}"
      start-sale-num="{{ startSaleNum }}"
      theme-main-bg-color="{{ themeMainBgColor }}"
      theme-main-bg-alpha-10-color="{{ themeMainBgAlpha10Color }}"
      bind:item-num-change="handleItemNumChange"
      bind:change-goods-sku="handleChangeGoodsSku"
    />

    <view
      wx:if="{{ isCanDelete }}"
      slot="right"
      class="delete-btn {{ index===0 && !isInvalid && !isActivity ? 'border-top-right' : '' }} {{ index === total - 1 && !hasPresent ? 'border-bottom-right' : '' }}"
    >
      删除
    </view>
  </van-swipe-cell>
</view>
