import each from '@youzan/weapp-utils/lib/each';
import getApp from 'shared/utils/get-safe-app';
import { SEPARATE_BUY_TYPE } from '../../constant';
import Theme from 'shared/common/components/theme-view/theme';

const app = getApp();

const TIP_MAP = {
  [SEPARATE_BUY_TYPE.MIX_TYPE]: '以下商品暂不支持同时结算，请分开下单',
  [SEPARATE_BUY_TYPE.LOGISTICS]: '不同配送方式的商品暂不支持同时结算，请分开下单'
};

Component({
  properties: {
    separateBuy: Object
  },

  data: {
    gradient: false,
    TIP_MAP
  },

  methods: {
    goPay({ target: { dataset: { index } } }) {
      const { type, data: separateList } = this.data.separateBuy;
      const { list } = separateList[index];
      if (type === SEPARATE_BUY_TYPE.MIX_TYPE) {
        this.triggerEvent('buy', { list }, { bubbles: true, composed: true });
      } else {
        this.logisticsBuy(index);
      }
    },

    logisticsBuy(index) {
      const {
        data: separateList
      } = this.data.separateBuy;
      const {
        expressType,
        list: currentBuyGoods
      } = separateList[index];
      // 去下单的商品
      const currentBuyGoodsIds = currentBuyGoods.map(item => item.cartId);
      // 排除掉去下单的剩余商品，支付完成后提示继续购买
      const waitBuyGoodsIds = [];

      each(separateList, (value, oIndex) => {
        if (index === oIndex) {
          return;
        }

        each(value.list, ({ cartId }) => {
          if (
            waitBuyGoodsIds.indexOf(cartId) === -1
            && currentBuyGoodsIds.indexOf(cartId) === -1
          ) {
            waitBuyGoodsIds.push(cartId);
          }
        });
      });

      if (waitBuyGoodsIds.length) {
        app.storage.set('waitBuyGoodsIds', waitBuyGoodsIds, {
          expire: 0.02 // 30min
        });
      }

      this.triggerEvent('buy', {
        list: currentBuyGoods,
        expressType
      }, { bubbles: true, composed: true });
    },

    onClose() {
      this.triggerEvent('close');
    },

    setBtnGradient() {
      Theme.getThemeType().then(type => {
        let gradient = false;

        if (type === 13) {
          gradient = true;
        }

        this.setData({
          gradient
        });
      });
    }
  },

  attached() {
    this.setBtnGradient();
  },
});
