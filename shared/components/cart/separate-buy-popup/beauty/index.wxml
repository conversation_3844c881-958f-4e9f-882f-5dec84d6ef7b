<view class="title">
  选择结算商品
  <van-icon class="icon" name="cross" bind:click="onClose" />
</view>
<view class="tip">{{ TIP_MAP[separateBuy.type] }}</view>
<view class="container">
  <view
    wx:for="{{ separateBuy.data }}"
    wx:for-item="items"
    wx:for-index="index"
    wx:key="{{ index }}"
    class="cell"
  >
    <view class="van-clearfix cell-title-wrap">
      <view class="cell-title">{{ items.title }}</view>
      <view wx:if="{{ util.isHasHaiTao(items.list) }}" class="prompt">
        <text>（含</text>
        <text class="icon-haitao" />
        <text>的商品）</text>
      </view>
    </view>
    
    <view class="cell-content">
      <view>
        <view wx:for="{{ util.goodItemsFilter(items.list) }}" wx:for-item="item" wx:key="{{ index }}" class="img-container">
          <image src="{{ item.imgUrl }}" mode="aspectFit" />
        </view>
        <text wx:if="{{ items.list.length > 3 }}" class="cell-dot">
          ...
        </text>
      </view>
      <view>
        <theme-view custom-class="button-wrap" color="main-text" bg="general" gradient="{{ gradient }}" gradientDeg="90">
          <view 
            class="button" 
            data-index="{{ index }}"
            bindtap="goPay"
          >
            去结算
          </view>
        </theme-view>
      </view>
    </view>


    <view class="total">
      共\t<text class="num">{{ items.list.length }}</text>\t件，合计：
      <theme-view  color="main-bg" custom-class="price">
        <price
          price="{{ items.totalPrice }}"
        />
      </theme-view>
    </view>
  </view>
</view>

<wxs module="util">
  module.exports.goodItemsFilter = function(list) {
    if (list.length > 3) return list.slice(0, 3);
    else return list;
  }
  module.exports.isHasHaiTao = function(list) {
    return list.some(function(item) {
      var rule = item.settlementRule || {};
      var mark = rule.settlementMark || '';
      return mark === 'HAITAO';
    });
  }
</wxs>