@import '@vant/weapp/dist/common/index.wxss';

.logistics {
  padding-top: 50px;
}

.title {
  background-color: #fff;
  font-size: 16px;
  color: #323233;
  padding: 11px 0;
  text-align: center;
  position: relative;
}

.icon {
  position: absolute;
  right: 15px;
  top: 13px;
  color: #999;
  font-size: 20px;
}

.tip {
  font-size: 12px;
  padding: 0 16px;
  color: #999;
  margin-bottom: 16px;
}

.container {
  overflow: scroll;
  height: 65vh;
}

.cell {
  position: relative;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
  margin: 0 16px 12px;
}

.cell-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cell-title-wrap {
  margin-bottom: 8px;
}

.cell-title {
  font-weight: 500;
  font-size: 14px;
  color: #323233;
  vertical-align: middle;
  display: inline-block;
}

.count {
  float: left;
  margin-top: 2px;
  font-size: 12px;
  color: #9b9b9b;
}

.items {
  width: auto;
  margin-right: 75px;
  white-space: nowrap;
}

.cell-dot {
  display: inline-block;
  vertical-align: bottom;
  color: #323233;
}

.img-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 4px;
  margin-right: 8px;
  vertical-align: middle;
  box-sizing: border-box;
}

.img-container image {
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
  background-color: #fff;
}

.ellipsis {
  font-size: 14px;
}

.button-wrap {
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 12px;
  width: 56px;
  font-size: 12px;
}

.total {
  margin-top: 10px;
  font-size: 13px;
  color: #323233;
  line-height: 17px;
}

.price {
  display: inline-block;
  color: #f44;
}

.prompt {
  display: inline-flex;
  align-items: center;
  font-size: 12px;
  color: #969799;
  vertical-align: middle;
}

.icon-haitao {
  display: inline-flex;
  width: 28px;
  height: 16px;
  background-image: url('https://img01.yzcdn.cn/public_files/2019/08/19/fbd4c38994578e951ef1cdfd9104606d.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  vertical-align: middle;
  margin: 0 3px;
}