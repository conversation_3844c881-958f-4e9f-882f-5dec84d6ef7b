<view class="shop-list-container">
  <view class="title van-hairline--bottom">请分开结算以下店铺商品</view>
  <van-icon class="icon-cancel" name="close" bind:click="onClose" />
  <view class="shop-list">
    <view
      wx:for="{{ separateBuy.data }}"
      wx:for-item="item"
      wx:key="{{ index }}"
      class="shop van-hairline--bottom"
    >
      <view class="name van-ellipsis">{{ item.title }}</view>
      <view class="num">共{{ item.list.length }}件</view>
      <view class="price">
        合计:
        <text>￥{{ item.totalPrice }}</text>
      </view>
      <view
        class="button"
        data-index="{{ index }}"
        bind:tap="goPay"
      >
        去结算
      </view>
    </view>
  </view>
</view>