@import '@vant/weapp/dist/common/index.wxss';
 
.shop-list-container {
  position: relative;
  color: #333;
}

.title {
  box-sizing: border-box;
  height: 50px;
  font-size: 16px;
  line-height: 50px;
  position: relative;
  text-align: center;
  font-weight: normal;
  margin: 0;
  color: #323233;
}

.icon-cancel {
  position: absolute;
  top: 4px;
  right: 2px;
  height: 42px;
  width: 42px;
  font-size: 20px;
  box-sizing: border-box;
  padding: 10px;
  background-size: 22px 22px;
  background-position: center center;
}

.shop-list {
  overflow: auto;
  height: 65vh;
}

.shop {
  box-sizing: border-box;
  height: 67px;
  padding: 15px 120px 15px 15px;
  position: relative;
}

.name {
  font-size: 14px;
  line-height: 16px;
}

.num,
.price {
  display: inline-block;
  font-size: 12px;
  color: #666;
  line-height: 14px;
}

.price em{
  color: #f60;
}

.button {
  border: 1px solid #f60;
  color: #f60;
  font-size: 14px;
  height: 34px;
  width: 83px;
  position: absolute;
  line-height: 34px;
  top: 15px;
  right: 15px;
  text-align: center;
}
