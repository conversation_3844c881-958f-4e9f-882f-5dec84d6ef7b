import { SEPARATE_BUY_TYPE } from '../constant';
import tabBehavi<PERSON> from '@/custom-tab-bar-v2/tab-behavior';

Component({
  properties: {
    separateBuy: {
      type: Object,
      observer: 'handleSeparateBuyChange'
    }
  },

  behaviors: [tabBehavior],

  data: {
    showNormal: false,
    showBeauty: false
  },

  methods: {
    onClose() {
      this.triggerEvent('close-separate-buy-popup');
    },

    handleSeparateBuyChange() {
      const { type } = this.data.separateBuy;
      const showNormal = type === SEPARATE_BUY_TYPE.MULTISHOP;
      const showBeauty = [SEPARATE_BUY_TYPE.MIX_TYPE, SEPARATE_BUY_TYPE.LOGISTICS].indexOf(type) !== -1;
      const waitSetMap = {
        showNormal,
        showBeauty
      };

      this.setData(waitSetMap);
    }
  }
});
