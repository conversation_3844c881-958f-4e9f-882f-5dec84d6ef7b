<view class="shop-header">
  <view class="shop-name" bindtap="goHome">
    <van-icon name="shop-o" size="16px" color="#323233" class="shop-o-icon" />
    <text class="shop-name-text">{{ shopName }}</text>
    <van-icon name="arrow" size="16px" color="#969799" class="arrow-icon" />
  </view>
  <form-view
    class="edit-text"
    wx:if="{{ showEdit }}"
    bindtap="handleEditModeChange"
  >
    <text>{{ editMode ? '完成' : '编辑' }}</text>
  </form-view>
</view>
