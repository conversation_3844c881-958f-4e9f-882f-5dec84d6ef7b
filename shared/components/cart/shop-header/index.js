Component({
  properties: {
    shopName: String,
    editMode: <PERSON><PERSON><PERSON>,
    checkedAll: <PERSON><PERSON><PERSON>,
    showEdit: <PERSON>olean,
  },

  methods: {
    goHome() {
      wx.reLaunch({
        url: '/packages/home/<USER>/index'
      });
    },
    handleEditModeChange() {
      const { editMode } = this.data;
      this.triggerEvent('edit-mode-change', !editMode);
    }
  }
});
