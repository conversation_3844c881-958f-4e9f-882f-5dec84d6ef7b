Component({
  properties: {
    shop: {
      type: Object,
    },
    unAvailableGoodsList: {
      type: Array,
    },
    editMode: Boolean,
    showEdit: {
      type: Boolean,
      value: true,
    },
    themeMainBgColor: String,
    themeMainBgAlpha10Color: String,
    designChildren: {
      type: Array,
      value: [],
    },
  },

  data: {},

  methods: {
    handleItemNumChange({ detail }) {
      const { cartId, num, isActivity } = detail;
      const { goods, goodsIndex, goodsGroupIndex } =
        this._findGoodsInList(cartId);

      const eventData = {
        goods,
        goodsIndex,
        goodsGroupIndex,
        num,
        isActivity,
      };

      this.triggerEvent('change-item-num', eventData);
    },

    handleItemChecked({ detail }) {
      const { cartId, checked, isActivity } = detail;
      const { goods, goodsIndex, goodsGroupIndex } =
        this._findGoodsInList(cartId);

      const eventData = {
        goods,
        goodsIndex,
        goodsGroupIndex,
        checked,
        isActivity,
      };

      this.triggerEvent('change-item-checked', eventData);
    },

    handleBatchChecked({ detail }) {
      const { goodsList, checked, isActivity } = detail;
      const list = (goodsList || []).map((item) => {
        const { goods, goodsIndex, goodsGroupIndex } = this._findGoodsInList(
          item.cartId
        );
        return {
          goods,
          goodsIndex,
          goodsGroupIndex,
        };
      });

      const eventData = {
        goodsList: list,
        checked,
        isActivity,
      };

      this.triggerEvent('change-batch-checked', eventData);
    },

    handleItemDelete({ detail }) {
      const app = getApp();
      const { cartId, isActivity } = detail;
      const { goods, goodsIndex, goodsGroupIndex, isUnAvailable } =
        this._findGoodsInList(cartId);

      const { sku, skuData, skuId, goodsId, title } = goods;

      const eventParams = {
        no_sku: sku ? 0 : 1,
        sku_id: skuId,
        sku_name: skuData,
        goods_id: goodsId,
        goods_name: title,
      };

      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'cart_decrease_goods_num',
          en: '购物车页面-删除商品数量',
          params: eventParams,
        });

      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'remove_from_cart',
          en: '购物车页面-删除商品数量',
          params: eventParams,
        });

      const eventData = {
        goods,
        goodsIndex,
        goodsGroupIndex,
        isUnAvailable,
        isActivity,
      };

      this.triggerEvent('change-item-delete', eventData);
    },

    handleChangeGoodsSku(e) {
      this.triggerEvent('change-goods-sku', e.detail);
    },

    _findGoodsInList(cartId) {
      const { unAvailableGoodsList } = this.data;
      const { goodsGroupList } = this.data.shop;
      let goodsGroupIndex = -1;
      let goodsIndex = -1;
      let goods;
      let isUnAvailable = false; // 是否是失效商品
      goodsGroupList.forEach((goodsGroup = {}, currentGoodsGroupListIndex) => {
        (goodsGroup.goodsList || []).forEach((item, index) => {
          if (item.cartId === cartId) {
            goodsGroupIndex = currentGoodsGroupListIndex;
            goodsIndex = index;
            goods = item;
          }
        });
      });

      if (goodsIndex === -1) {
        unAvailableGoodsList.forEach((item, index) => {
          if (item.cartId === cartId) {
            isUnAvailable = true;
            goodsIndex = index;
            goodsGroupIndex = -1;
            goods = item;
          }
        });
      }
      return {
        goodsIndex,
        goods,
        isUnAvailable,
        goodsGroupIndex,
      };
    },

    handleRefreshCart() {
      this.triggerEvent('refresh-cart');
    },

    handleShowPopup({ detail }) {
      this.triggerEvent('handle-show-popup', detail);
    },

    activityBtnClick({ detail }) {
      this.triggerEvent('activity-btn-click', detail);
    },
  },
});
