<view class="valid-goods">

  <goods-group
    wx:for="{{ shop.goodsGroupList }}"
    wx:key="index"
    design-children="{{ designChildren }}"
    goods-group="{{ item }}"
    kdt-id="{{ shop.kdtId }}"
    edit-mode="{{ editMode }}"
    theme-main-bg-color="{{ themeMainBgColor }}"
    theme-main-bg-alpha-10-color="{{ themeMainBgAlpha10Color }}"
    bind:item-checked="handleItemChecked"
    bind:batch-checked="handleBatchChecked"
    bind:item-num-change="handleItemNumChange"
    bind:item-delete="handleItemDelete"
    bind:refresh-cart="handleRefreshCart"
    bind:change-goods-sku="handleChangeGoodsSku"
    bind:handle-show-popup="handleShowPopup"
    bind:activity-btn-click="activityBtnClick"
  />
</view>