.activity-info {
  padding-left: 16px;
  padding-right: 16px;
  border-radius: 12px 12px 0 0;
  box-sizing: border-box;

  &__title {
    padding: 11px 0;
    text-align: center;
    font-size: 16px;
    color: #323233;
  }

  &__body {
    /* 兼容 */
    min-height: 40vh;
    min-height: calc(50vh - 122px);
    min-height: calc(50vh - 122px - constant(safe-area-inset-bottom));
    min-height: calc(50vh - 122px - env(safe-area-inset-bottom));
    max-height: 70vh;
    max-height: calc(80vh - 122px);
    max-height: calc(80vh - 122px - constant(safe-area-inset-bottom));
    max-height: calc(80vh - 122px - env(safe-area-inset-bottom));
    overflow: auto;
    box-sizing: border-box;
  }

  &__subtitle {
    font-size: 14px;
    margin-bottom: 12px;
    font-weight: bold;
    color: #323233;
  }

  &__time,
  &__explain {
    font-size: 12px;
    margin-bottom: 20px;
    color: #323233;
  }

  &__desc {
    font-size: 13px;
    color: #323233;
  }

  &__tag-wrap {
    display: inline-flex;
    margin-right: 4px;
    vertical-align: middle;
  }

  &__tag {
    position: relative;
    font-size: 12px !important;
    line-height: 16px !important;
    padding: 0 4px !important;
  }

  &__desc_text {
    line-height: 16px;
    vertical-align: middle;
  }

  &__btn-wrap {
    height: 36px;
    line-height: 36px;
    border-radius: 18px;
    margin-top: 32px;
    margin-bottom: 10px;
  }

  &__btn {
    color: #fff;
    text-align: center;
    font-size: 14px;
  }
}
