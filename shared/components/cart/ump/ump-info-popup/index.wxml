<van-popup 
  show="{{ showPopup }}" 
  custom-style="bottom: {{ popupBottom }}px"
  custom-class="activity-info" 
  position="bottom"
  bind:click-overlay="close"
  safe-area-inset-bottom
>
  <view class="activity-info__title">
    活动详细规则
  </view>

  <view class="activity-info__body">
    <view class="activity-info__subtitle">
      活动说明
    </view>

    <view class="activity-info__explain">
      参与的满减满赠活动仅含虚拟商品时不送实物赠品。
    </view>

    <view class="activity-info__subtitle">
      活动时间
    </view>

    <view class="activity-info__time">
      {{ activity.activityDuration }}
    </view>

    <view class="activity-info__subtitle">
      活动内容
    </view>

    <view class="activity-info__desc">
      <van-tag
        wx:for="{{ activity.activityTags }}"
        wx:for-item="tag"
        wx:key="{{ index }}"
        class="activity-info__tag-wrap"
        custom-class="activity-info__tag"
        round
        color="{{ themeMainBgAlpha10Color }}"
        text-color="{{ themeMainBgColor }}"
        size="medium"
      >
        {{ tag }}
      </van-tag>
      <span class="activity-info__desc_text">{{ activity.activityDesc }}</span>
    </view>
  </view>

  <theme-view
    color="main-text" 
    bg="general" 
    gradient ="{{ gradient }}"
    gradientDeg="90"
    custom-class="activity-info__btn-wrap"
  >
    <view class="activity-info__btn" bindtap="close">
      知道了
    </view>
  </theme-view>
</van-popup>