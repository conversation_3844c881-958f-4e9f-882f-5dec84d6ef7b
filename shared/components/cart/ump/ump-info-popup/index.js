import tabBehavior from '@/custom-tab-bar-v2/tab-behavior';
import Theme from 'shared/common/components/theme-view/theme';

Component({
  properties: {
    activity: {
      type: Object,
    },
    show: {
      type: Boolean
    },
    themeMainBgColor: String,
    themeMainBgAlpha10Color: String
  },

  behaviors: [tabBehavior],

  observers: {
    show(val) {
      if (this.data.showPopup === val) return;
      this.setData({
        showPopup: val
      });
    }
  },

  data: {
    gradient: false,
    showPopup: false
  },

  methods: {
    close() {
      this.triggerEvent('close');
    },

    setBtnGradient() {
      Theme.getThemeType().then(type => {
        let gradient = false;

        if (type === 13) {
          gradient = true;
        }

        this.setData({
          gradient
        });
      });
    }
  },

  attached() {
    this.setBtnGradient();
  },
});
