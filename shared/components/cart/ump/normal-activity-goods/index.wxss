@import "@vant/weapp/dist/common/index.wxss";

.activity-goods {
  overflow: hidden;
  margin: 0 12px 12px;
  background: #fff;
  border-radius: 4px;
}

.activity-info {
  padding: 10px 10px 10px 0;
  font-size: 12px;
  line-height: 22px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.activity-text-wrap {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-right: 12px;
  box-sizing: border-box;
  vertical-align: middle;
}

.activity-tag {
  font-size: 12px!important;
  line-height: 16px!important;
  padding: 0 4px!important;
  vertical-align: middle;
}

.checkbox-wrap {
  width: 44px;
  height: 18px;
  text-align: center;
}

.icon--unchecked {
  color: #bbb;
}

.activity-desc-wrap {
  flex: 1;
  padding-right: 12px;
  box-sizing: border-box;
  position: relative;
  width: 0;
  white-space: nowrap;
  overflow: visible;
  vertical-align: middle;
}

.activity-desc {
  display: inline-block;
  max-width: 100%;
  font-size: 12px;
  color: #323233;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: keep-all;
  vertical-align: middle;
  box-sizing: border-box;
  padding-right: 5px; 
}

.icon-info {
  color: #969799;
  width: 12px;
  height: 12px;
  vertical-align: middle;
  font-size: 0;
  display: inline-block;
}

.activity-action {
  font-size: 12px;
  height: 22px;
  display: flex;
  align-items: center;
}

.activity-action-arrow {
  display: block;
  height: 12px;
  line-height: 12px;
}
