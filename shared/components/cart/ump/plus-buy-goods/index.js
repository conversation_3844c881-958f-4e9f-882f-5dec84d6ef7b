import money from '@youzan/weapp-utils/lib/money';
import args from '@youzan/weapp-utils/lib/args';
import debounce from '@youzan/weapp-utils/lib/debounce';

const BTNMAP = [
  '去凑单',
  '去换购',
  '重新换购'
];

Component({
  properties: {
    activity: {
      type: Object,
      observer: 'handleActivityChange'
    },
    editMode: Boolean,
    kdtId: Number,
    themeMainBgColor: String,
    themeMainBgAlpha10Color: String
  },

  data: {
    // 活动商品
    activityGoods: [],
    // 活动商品的总价
    activityGoodsTotalPrice: 0,
    // 加购商品
    plusBuyGoods: [],
    // 满足加价购条件价格
    conditionPrice: '',
    // 目前活动商品是否已经达到换购条件
    isMeet: false,
    // 是否已经添加了换购商品
    plusGoodsAdded: false,
    // 各种价格的元表示
    activityGoodsTotalPriceYuan: 0,
    conditionPriceYuan: 0,
    gapPriceYuan: 0,
    // 是否展示加购组件
    showExchangeModal: false,
    exchangeModalDesc: '',
    activityBtnTxt: '',
    showPopup: false,
    isAllActivityGoodsChoose: false
  },

  methods: {
    chooseAllActivityGoods() {
      const { isAllActivityGoodsChoose, activity } = this.data;
      const { goodsList } = activity;
      const eventData = {
        checked: !isAllActivityGoodsChoose,
        goodsList,
        isActivity: true
      };
      this.triggerEvent('batch-checked', eventData);
    },

    computedIsAllActivityGoodsChoose() {
      const goodsList = this.data.activity.goodsList;
      this.setData({
        isAllActivityGoodsChoose: goodsList.every(item => item.checked)
      });
    },

    closePopup() {
      this.setData({
        showPopup: false
      });
    },

    handleShowPopup() {
      this.setData({
        showPopup: true
      });
    },

    activityBtnClick() {
      if (this.data.activityBtnTxt === BTNMAP[0]) {
        this.handleGoActivityPage();
      } else {
        this.handleShowExchangeModal();
      }
    },

    handleItemChecked({ detail }) {
      this.triggerEvent('item-checked', detail);
    },

    handleItemNumChange({ detail }) {
      this.triggerEvent('item-num-change', detail);
    },

    handleItemDelete({ detail }) {
      this.triggerEvent('item-delete', detail);
    },

    handleGoActivityPage() {
      wx.navigateTo({
        url: args.add('/packages/ump/plusbuy/index', {
          activityId: this.data.activity.activityId
        })
      });
    },

    handleShowExchangeModal() {
      this.setData({
        showExchangeModal: true
      });
    },

    handlePlusBuyGoodsChange() {
      this.triggerEvent('refresh-cart');
    },

    checkPlusBuyGoods() {
      const { activity } = this.data;
      const { activityItems, hasMeet, alias } = activity;
      const plusBuyGoodsItems = activityItems.filter(item => !!item.activityId);
      if (plusBuyGoodsItems.length && !hasMeet) {
        // 有加购商品，但是没满足加购条件，需要联动删除
        const goodsCartId = plusBuyGoodsItems.map(item => item.cartId);
        this.triggerEvent('item-multi-delete', {
          goodsCartId,
          alias
        });
      }
    },

    // 根据现存的商品状态计算活动信息文案, 以及根据条件删除联动商品
    calculatePlusBuyStatus() {
      const {
        activityGoods,
        plusBuyGoods,
        conditionPrice
      } = this.data;

      const { hasMeet } = this.data.activity;

      let activityGoodsTotalPrice = 0;
      let plusGoodsAdded = false;
      activityGoods.forEach(goods => {
        activityGoodsTotalPrice += goods.payPrice * goods.num;
      });
      if (plusBuyGoods.length && hasMeet) {
        // 存在加购商品，总价高于活动价，正常显示
        plusGoodsAdded = true;
      }

      // 设置按钮文案
      let activityBtnTxt = '';
      let exchangeModalDesc = '';
      if (!hasMeet) {
        activityBtnTxt = BTNMAP[0];
      } else if (!plusGoodsAdded) {
        exchangeModalDesc = `已购满${+money(conditionPrice).toYuan()}元，`;
        activityBtnTxt = BTNMAP[1];
      } else {
        exchangeModalDesc = `已购满${+money(conditionPrice).toYuan()}元，`;
        activityBtnTxt = BTNMAP[2];
      }

      this.plusBuyGoodsDelete();

      this.setData({
        isMeet: hasMeet,
        plusGoodsAdded,
        activityBtnTxt,
        exchangeModalDesc,
        activityGoodsTotalPrice,
        activityGoodsTotalPriceYuan: +money(activityGoodsTotalPrice).toYuan(),
        gapPriceYuan: +money(conditionPrice - activityGoodsTotalPrice).toYuan()
      });
    },

    // 区分普通商品和加购商品
    handleActivityChange(activity) {
      const { conditionPrice } = activity;

      let plusBuyUniqueList = activity.activityItems.filter(activity => activity.activityId !== 0);
      plusBuyUniqueList = plusBuyUniqueList.map(
        item => `${this.data.kdtId}-${item.goodsId}-${item.skuId}-${item.activityId}`
      );
      // 分类
      const plusBuyGoods = activity.goodsList
        .filter(item => {
          return plusBuyUniqueList.indexOf(item.unique) > -1;
        });
      plusBuyGoods.forEach(item => {
        item.activityTag = '换购';
      });
      const activityGoods = activity.goodsList
        .filter(item => plusBuyUniqueList.indexOf(item.unique) === -1);

      this.setData({
        plusBuyGoods,
        activityGoods,
        conditionPrice,
        conditionPriceYuan: +money(conditionPrice).toYuan()
      });
      // 当有活动商品和普通商品变化时，当前活动文案自动刷新
      this.calculatePlusBuyStatus();

      this.computedIsAllActivityGoodsChoose();
    },

    closeExchange() {
      this.setData({
        showExchangeModal: false
      });
    }
  },
  created() {
    this.plusBuyGoodsDelete = debounce(this.checkPlusBuyGoods, 300);
  }
});
