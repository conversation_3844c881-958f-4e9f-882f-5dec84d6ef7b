import get from '@youzan/weapp-utils/lib/get';
import each from '@youzan/weapp-utils/lib/each';
import money from '@youzan/weapp-utils/lib/money';

/**
 * 获取商品属性ID列表
 * @param {Array} properties
 */
function getPropertyIds(properties = []) {
  const propertyIds = [];
  properties.forEach((property) => {
    const propValueList = get(property, 'propValueList', []);
    propValueList.forEach((value) => {
      propertyIds.push(value.propValueId);
    });
  });

  return propertyIds;
}

export function generateBuyGoodsList(goodsList = []) {
  const order = goodsList.map((goods) => {
    let bizTracePointExt = '';
    let message = '';
    try {
      const extraAttribute = JSON.parse(goods.extraAttribute);
      bizTracePointExt = extraAttribute.bizData || '';
      message = JSON.parse(goods.messages || null);
    } catch (e) {
      console.warn(e);
    }
    const result = {
      activityAlias: '',
      activityId: 0,
      activityType: 0,
      message,
      num: goods.num,
      price: goods.payPrice,
      skuId: goods.skuId,
      goodsId: goods.goodsId,
      kdtId: goods.kdtId,
      bizTracePointExt,
      propertyIds: getPropertyIds(goods.properties)
    };
    if (+goods.activityType === 24) {
      Object.assign(result, {
        activityId: goods.activityId,
        activityType: 24
      });
    }
    return result;
  });
  return order;
}

// 生成下单数据
export const generateBuyOrderData = (goodsList = []) => {
  const config = {
    canyinChannel: 1,
    canyinIds: []
  };
  const items = [];
  const itemSources = [];
  const activities = [];

  const sellers = [
    {
      kdtId: get(goodsList, '[0].kdtId'),
      storeId: get(goodsList, '[0].storeId', 0)
    }
  ];

  goodsList.forEach((goods) => {
    let bizData;
    try {
      /* eslint-disable-next-line */
      bizData = JSON.parse(goods.extraAttribute || '{}').bizData;
    } catch (e) {
      /* eslint-disable-next-line */
      console.log(e);
    }
    const baseId = {
      kdtId: goods.kdtId,
      goodsId: goods.goodsId,
      skuId: goods.skuId,
      propertyIds: goods.propertyIds || [],
      activityId: goods.activityId,
      activityType: +goods.activityType || 0
    };

    // 外部订单来源
    const tpps = get(goods, 'bizExtension.cartBizMark.tpps');
    const item = {
      ...baseId,
      storeId: goods.storeId || 0,
      price: goods.payPrice || 0,
      num: goods.num,
      itemMessage: goods.messages || '',
      extensions: { tpps },
      isSevenDayUnconditionalReturn: goods.isSevenDayUnconditionalReturn || false
    };

    if (goods.deliverTime) item.deliverTime = goods.deliverTime;

    // 0元抽奖活动标识
    const promotionMark = get(goods, 'bizExtension.cartBizMark.promotionMark', '');
    if (promotionMark) item.promotionMark = promotionMark;

    const itemSource = {
      ...baseId,
      bizTracePointExt: bizData,
      cartCreateTime: goods.createdTime,
      cartUpdateTime: goods.updatedTime
    };

    const activity = {
      ...baseId,
      activityAlias: goods.activityAlias
    };

    items.push(item);
    itemSources.push(itemSource);
    activities.push(activity);

    if (goods.canyinId) {
      config.canyinIds.push(goods.canyinId);
    }

    // 餐饮加料按单个商品计算
    (goods.ingredientInfoList || []).forEach((feedGoods) => {
      items.push({
        kdtId: feedGoods.kdtId,
        goodsId: feedGoods.goodsId,
        skuId: feedGoods.skuId,
        num: goods.num,
        price: feedGoods.price,
        extensions: { tpps }
      });

      activities.push({
        kdtId: feedGoods.kdtId,
        goodsId: feedGoods.goodsId,
        skuId: feedGoods.skuId
      });
    });
  });

  return {
    config,
    items,
    sellers,
    source: {
      itemSources,
      orderFrom: 'cart'
    },
    ump: {
      activities
    }
  };
};

// 根据kdtId goodsId skuId 在商品列表中找出对应勾选的商品列表
export function findGoodsBuyIds(goodsIdsList = [], goodsList = []) {
  return goodsList.filter((goods) => {
    const { kdtId, goodsId, skuId, activityId } = goods;
    const id = [kdtId, goodsId, skuId, activityId].join('-');
    return goodsIdsList.indexOf(id) !== -1;
  });
}

export function separateGoods(kvGoods, titleMap) {
  const result = [];
  each(kvGoods, (list, key) => {
    if (!list || !list.length) {
      return;
    }
    const item = {
      title: titleMap[key],
      list
    };
    let totalPrice = 0;
    each(list, (goods) => {
      totalPrice += goods.payPrice;
    });
    item.totalPrice = money(totalPrice).toYuan();
    result.push(item);
  });
  return result;
}

export function separateGoodsForMixType(goodsList) {
  // 分类普通，周期购，海淘商品
  const goodsMap = {
    HAITAO: {
      title: '海淘商品',
      list: [],
      totalPrice: 0
    },
    PERIOD_BUY: {
      title: '周期购商品',
      list: [],
      totalPrice: 0
    },
    COMMON: {
      title: '普通商品',
      list: [],
      totalPrice: 0
    }
  };
  const data = [];
  // 分类
  each(goodsList, (goods) => {
    let mark = get(goods, 'settlementRule.settlementMark');
    if (!goodsMap[mark]) {
      mark = 'COMMON';
    }
    goodsMap[mark].totalPrice += goods.payPrice * goods.num;
    goodsMap[mark].list.push(goods);
  });

  // 过滤
  each(goodsMap, (item) => {
    if (item.list.length) {
      data.push(item);
    }
  });

  if (data.length > 1) {
    return {
      needSeparate: true,
      data
    };
  }
  return {
    needSeparate: false
  };
}

export function separateGoodsForLogistics(goods) {
  const map = {
    NORMAL_EXPRESS: {
      title: '快递发货',
      expressType: 0,
      list: []
    },
    LOCAL_DELIVERY: {
      title: '同城配送',
      expressType: 2,
      list: []
    },
    SELF_TAKE: {
      title: '到店自提',
      expressType: 1,
      list: []
    }
  };
  const data = [];
  const itemsLength = goods.length;
  // 是否存在共同的物流方式
  let needSeparate = true;

  // 无物流商品 -- 非实物商品
  let noLogisticsGoodsNum = 0;

  each(goods, (item) => {
    const { logisticsTypeList } = item;

    // 虚拟商品没有物流方式，需要推到所有物流方式中
    if (Array.isArray(logisticsTypeList) && logisticsTypeList.length === 0) {
      noLogisticsGoodsNum++;
      each(map, (logistics) => {
        logistics.list.push(item);
      });
    }

    each(logisticsTypeList, (type) => {
      const logistics = map[type];
      if (!logistics) {
        return;
      }

      logistics.list.push(item);
    });
  });

  each(map, (val) => {
    const listLength = val.list.length;
    let totalPrice = 0;

    if (listLength > noLogisticsGoodsNum || listLength === itemsLength) {
      each(val.list, (item) => {
        totalPrice += item.payPrice * item.num;
      });

      val.totalPrice = totalPrice;
      data.push(val);
    }

    if (listLength === itemsLength) {
      needSeparate = false;
    }
  });

  return {
    needSeparate,
    data
  };
}
