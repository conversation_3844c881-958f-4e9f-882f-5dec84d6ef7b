<view class="activity-info">
  <view class="activity-info__text-wrap">
    <van-icon
      class="activity-info__checkbox-wrap"
      size="18px"
      color="{{ themeMainBgColor }}"
      custom-class="checkbox {{ isAllActivityGoodsChoose ? '' : 'icon--unchecked' }}"
      name="{{ isAllActivityGoodsChoose ? 'checked' : 'circle'}}"
      catch:tap="chooseAllActivityGoods"
    />

    <van-tag
      wx:for="{{ activityInfo.activityTags }}"
      wx:for-item="activityName"
      wx:key="{{ index }}"
      color="{{ themeMainBgAlpha10Color }}"
      text-color="{{ themeMainBgColor }}"
      round
      class="activity-info__tag-wrap"
      custom-class="activity-info__tag"
      bind:tap="handleShowPopup"
    >
      {{ activityName }}
    </van-tag>

    <div class="activity-info__desc-wrap">
      <view class="activity-info__desc" bind:tap="handleShowPopup">
        {{ activityInfo.activityDesc }}
      </view>
      <van-icon
        size="12px"
        class="icon-info"
        name="info-o"
        bind:tap="handleShowPopup"
      />
    </div>

  </view>

  <theme-view wx:if="{{ activityBtnTxt }}" color="main-bg">
    <view class="activity-info__action van-ellipsis" bindtap="activityBtnClick">
      <text class="activity-info__action-text item-text">{{ activityBtnTxt }}</text>
      <van-icon class="activity-info__action-arrow" name="arrow" size="11px" />
    </view>
  </theme-view>
</view>