const BTNMAP = ['再逛逛', '去凑单'];

// 活动
const ACTIVITY_TYPE_ALIAS_MAP = {
  104: 'packageBuy',
  101: 'meetReduce',
  24: 'plusBuy',
  115: 'secondHalf' // 第二支半价
};

Component({
  properties: {
    activityInfo: {
      type: Object,
      observer(activityInfo) {
        const { activityType } = activityInfo;
        const activityTypeAlias = ACTIVITY_TYPE_ALIAS_MAP[activityType];

        this.setData({
          activityTypeAlias
        });

        this.setActivityInfo();
        this.computedIsAllActivityGoodsChoose();
      }
    },
    goodsList: Array,
    editMode: Boolean,
    kdtId: Number,
    themeMainBgColor: String,
    themeMainBgAlpha10Color: String,
  },

  data: {
    activityTypeAlias: '',
    activityBtnTxt: '',
    isAllActivityGoodsChoose: false,
  },

  methods: {
    chooseAllActivityGoods() {
      const { isAllActivityGoodsChoose, goodsList } = this.data;
      const eventData = {
        checked: !isAllActivityGoodsChoose,
        goodsList,
        isActivity: true
      };
      this.triggerEvent('batch-checked', eventData);
    },

    setActivityInfo() {

      const { activityTypeAlias } = this.data;
      if (['meetReduce', 'packageBuy', 'secondHalf'].indexOf(activityTypeAlias) !== -1) {
        this.setCommonActivityInfo();
      } else if (['plusBuy'].indexOf(activityTypeAlias) !== -1) {
        this.setPlusBuyActivityInfo();
      }
    },

    // 设置普通活动
    setCommonActivityInfo() {
      const { activityInfo } = this.data;
      let activityBtnTxt = '';
      if (activityInfo.meet) {
        activityBtnTxt = BTNMAP[0];
      } else if (!activityInfo.meet && activityInfo.activityUrl) {
        activityBtnTxt = BTNMAP[1];
      }

      this.setData({
        activityBtnTxt
      });
    },

    setPlusBuyActivityInfo() {
      const { goodsList, activityInfo } = this.data;
      // 获取换购商品
      const plusBuyGoods = goodsList.filter((item) => item.activityId);

      // 设置按钮文案
      let activityBtnTxt = '';
      if (!activityInfo.meet) {
        activityBtnTxt = '去凑单';
      } else if (!plusBuyGoods.length) {
        activityBtnTxt = '去换购';
      } else {
        activityBtnTxt = '重新换购';
      }

      this.setData({
        activityBtnTxt,
      });
    },

    handleShowPopup() {
      this.triggerEvent('handle-show-popup', this.data.activityInfo);
    },
    
    activityBtnClick() {
      const { activityInfo, goodsList } = this.data;
      this.triggerEvent('activity-btn-click', { activityInfo, goodsList });
    },

    goHome() {
      wx.reLaunch({
        url: '/packages/home/<USER>/index'
      });
    },

    // 计算当前活动是否全选
    computedIsAllActivityGoodsChoose() {
      const { goodsList } = this.data;

      this.setData({
        isAllActivityGoodsChoose: goodsList.every((item) => item.checked)
      });
    }
  }
});
