import Toast from '@vant/weapp/dist/toast/toast';
import {
  setYZData,
  cancelYZData,
} from '@youzan/weapp-utils/lib/set-yz-data/index';
// import { setYZData, cancelYZData } from 'shared/common/base/wsc-data-helper';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import get from '@youzan/weapp-utils/lib/get';
import api, { setCartStatus } from './api';
import {
  isCheckboxEnabled,
  getGoodsPropertiesIds,
  parseGoodsProperties,
} from './utils';
import { getGoodSkusJson } from 'shared/components/cart/exchange-goods/common/api';
import { design } from './design.json';

export default () => {
  let isCrossShop = false;

  return {
    data: {
      design,
      // 给个假数据让骨架可以正常展示
      shopList: [
        {
          shopName: 'skeleton',
          goodsGroupList: [
            {
              goodsList: [
                {
                  title: 'skeleton,skeleton',
                  num: 1,
                  price: 1,
                  sku: 'skeleton',
                },
              ],
            },
          ],
        },
      ],
      unAvailableGoodsList: [],
      totalCheckedPrice: '',
      cartTips: '不含运费',
      hasHaitaoGoods: false,
      // 标识当前编辑的店铺
      editShopIndex: -1,
      checkedAll: false,
      totalCheckedNum: 0,
      isFetching: true,
      isPullDownRefresh: false,
      // 分隔商户，周期购与普通商品，物流等
      separateBuy: {
        show: false,
        type: '',
        data: [],
      },
      isShowSkuPopup: false,
      skuData: {},
      skuDataGoods: {},
      initialSku: {},
      skuExtraData: {
        hideSkuStepper: true,
        useCustomHeaderPrice: true,
      },
      skuMessageConfig: {},
      submitLoading: false,
      bookKey: '',
      themeMainBgColor: '',
      themeMainBgAlpha10Color: '',
      showOrderkeep: false,
      displayOrderKeepData: {},
      orderKeepData: {},
      cartGroupId: '',
      showPopup: false,
      showExchangeModal: false,
      activityInfo: {},
      goodsList:[],
      activityTypeAlias: '',
      plusBuyGoods: [],
      activityBtnTxt: '',
      exchangeModalDesc: '',
    },

    onLoad(options = {}) {
      // 监听多网点进店
      this.on('app:offlineId:change', this.offlineIdChange);

      const { cartType, channelId } = mapKeysCase.toCamelCase(options);
      isCrossShop = cartType === 'all';
      setCartStatus({ isCrossShop, channelId });

      this.setYZData({
        isCrossShop,
      });

      // 监听待开售商品倒计时为0时修改开售时间
      this.on('change-start-sold-time', this.changeStartSoldTime);
    },

    onUnload() {
      cancelYZData(this);
    },

    setYZData,

    onPullDownRefresh() {
      if (this.data.isPullDownRefresh) return;
      this.cartProcess.refreshCartData();
      this.setYZData({
        editShopIndex: -1,
        isPullDownRefresh: true,
      });
    },

    offlineIdChange() {
      // 使用setTimeout 解决loading不显示
      setTimeout(() => {
        this.cartProcess.refreshCartData();
      }, 300);
    },

    // 修改开售时间
    changeStartSoldTime(goods) {
      const { shopList } = this.data;
      const { kdtId: sellKdtId, goodsId: sellGoodsId } = goods;
      shopList.forEach((shop, shopIndex) => {
        const { goodsGroupList = [], kdtId } = shop;
        if (kdtId === sellKdtId) {
          goodsGroupList.forEach((goodsGroup = {}, goodsGroupIndex) => {
            (goodsGroup.goodsList || []).forEach((goods, goodsIndex) => {
              if (goods.goodsId === sellGoodsId) {
                const key = `shopList[${shopIndex}].goodsGroupList[${goodsGroupIndex}].goodsList[${goodsIndex}].startSoldTime`;
                this.setYZData({
                  [key]: 0,
                });
              }
            });
          });
        }
      });
      this.cartProcess._updateCheckedAll();
    },

    onConfirmSku({ detail: selectedSku = {} }) {
      const { skuDataGoods } = this.data;

      const { messages, selectedSkuComb } = selectedSku;
      const { id: skuId, properties = [] } = selectedSkuComb;
      const formatMessages = Object.keys(messages).map((key) => messages[key]);

      const messagesStr = formatMessages.length
        ? JSON.stringify(formatMessages)
        : '';

      // 获取属性ID
      const propertyIds = getGoodsPropertiesIds(
        parseGoodsProperties(properties)
      );

      let skuDataGoodsMessage = {};
      try {
        skuDataGoodsMessage = JSON.parse(skuDataGoods.messages);
      } catch (error) {
        skuDataGoodsMessage = {};
      }
      const skuDataGoodsMessageArr = Object.keys(skuDataGoodsMessage).map(
        (key) => skuDataGoodsMessage[key]
      );

      const skuDataGoodsMessageStr = skuDataGoodsMessageArr.length
        ? JSON.stringify(skuDataGoodsMessageArr)
        : '';

      // 没有修改不需要发送请求
      if (
        skuId === skuDataGoods.skuId &&
        messagesStr === skuDataGoodsMessageStr &&
        propertyIds.join(',') === (skuDataGoods.propertyIds || []).join(',') // 属性
      ) {
        this.setYZData({
          isShowSkuPopup: false,
        });
        return;
      }

      this.cartProcess
        .reselectGoods({
          goods: {
            ...skuDataGoods,
            skuId,
            messages: formatMessages.length
              ? JSON.stringify(formatMessages)
              : '',
            propertyIds,
          },
        })
        .then((res) => {
          const { value: cartId } = res;
          this.cartProcess.refreshCartData().then(() => {
            this.animationScrollToElement(cartId);
          });
        });

      this.setYZData({
        isShowSkuPopup: false,
      });
    },

    // 滚动到相关元素
    animationScrollToElement(cartId) {
      const query = wx.createSelectorQuery().in(this);
      query.select(`.valid-block >>> #cart-id-${cartId}`).boundingClientRect();
      query.selectViewport().scrollOffset();

      query.exec((res) => {
        if (!res[0] || !res[1]) return;
        const elementRectTop = res[0].top;
        const currentScrollTop = res[1].scrollTop;

        wx.pageScrollTo({
          scrollTop: elementRectTop + currentScrollTop,
          duration: 200,
        });
      });
    },

    fetchGoodsSku({ detail }) {
      const { goods } = detail;

      const params = {
        alias: goods.alias,
      };

      if (goods.storeId) params.offlineId = goods.storeId;

      getGoodSkusJson(params)
        .then((skuData = {}) => {
          skuData = mapKeysCase.toCamelCase(skuData);

          // 获取活动 计算后的 sku
          const activity = (skuData.itemActivitySpuModels || []).find(
            (activity) => activity.type === goods.activityTypeStr
          );
          if (activity) {
            skuData = activity;
          }

          const goodsProperties = get(skuData, 'itemSalePropList', []);
          const isShowSkuPopup =
            !skuData.noneSku ||
            !!(skuData.messages && skuData.messages.length) ||
            !!goodsProperties.length;

          // 获取初始化sku
          const initialSku = this.getInitialSku(goods);
          const skuMessageConfig = this.getSkuMessageConfig(goods);

          const skuExtraData = {
            ...this.data.skuExtraData,
            asyncPropPrice:
              !!goods.activityTag && !!(goods.properties || []).length,
          };

          this.setYZData({
            skuData,
            goodsProperties,
            skuMessageConfig,
            initialSku,
            skuDataGoods: {
              picture: goods.imgUrl,
              ...goods,
            },
            isShowSkuPopup,
            skuExtraData,
          });
        })
        .catch(() => {
          Toast('获取商品数据失败，请重试');
        });
    },

    getSkuMessageConfig(goods) {
      let initialMessages = {};
      try {
        initialMessages = JSON.parse(goods.messages);
      } catch (error) {
        initialMessages = {};
      }
      return {
        initialMessages,
      };
    },

    getInitialSku(goods) {
      const initialSku = {};
      let { skuData } = goods;
      const { properties = [] } = goods;
      if (!skuData) return initialSku;
      skuData = mapKeysCase.toCamelCase(skuData);

      skuData.forEach(({ kS, vId }) => {
        if (kS && vId) {
          initialSku[kS] = String(vId);
        }
      });

      properties.forEach((property) => {
        initialSku.selectedProp = initialSku.selectedProp || {};
        initialSku.selectedProp[property.propId] = (
          property.propValueList || []
        ).map((value) => value.propValueId);
      });

      return initialSku;
    },

    onSkuClose() {
      this.setYZData({
        isShowSkuPopup: false,
      });
    },

    handleEditModeChange({ detail, currentTarget }) {
      const { shopList } = this.data;
      const editMode = detail;
      const { shopIndex } = currentTarget.dataset;
      const waitSetMap = {};

      // 需要重新请求购物车, 数量变更后活动也在变化
      if (!editMode) {
        this.setYZData({
          editShopIndex: -1,
        });
        this.cartProcess.refreshCartData();
      } else {
        waitSetMap.editShopIndex = shopIndex;
        waitSetMap.totalCheckedNum = 0;
        waitSetMap.checkedAll = false;
        // 取消选中状态
        shopList.forEach((shop, shopIndex) => {
          const { goodsGroupList = [], checkedAll } = shop;
          // 店铺取消选中
          if (checkedAll) {
            waitSetMap[`shopList[${shopIndex}].checkedAll`] = false;
          }

          goodsGroupList.forEach((goodsGroup = {}, goodsGroupIndex) => {
            (goodsGroup.goodsList || []).forEach((goods, goodsIndex) => {
              // 商品取消选中
              if (goods.checked) {
                const key = `shopList[${shopIndex}].goodsGroupList[${goodsGroupIndex}].goodsList[${goodsIndex}].checked`;
                waitSetMap[key] = false;
              }
            });
          });
        });
      }
      this.cartProcess._updateYzData(waitSetMap);
    },

    handleShopCheck({ detail, currentTarget }) {
      const { shopIndex } = currentTarget.dataset;

      this.checkShop(shopIndex, !!detail);
    },

    handleAllShopCheck() {
      const { checkedAll, shopList } = this.data;
      const willChecked = !checkedAll;

      shopList.forEach((shop, index) => {
        if (shop.checkedAll === willChecked) {
          return;
        }
        this.checkShop(index, willChecked);
      });
      this.cartProcess._updateYzData(willChecked);
    },

    checkShop(shopIndex, checked) {
      const { editShopIndex, shopList } = this.data;
      const { goodsGroupList, kdtId } = shopList[shopIndex];
      const checkboxEnabledGoodsList = [];
      goodsGroupList.forEach((goodsGroup = {}, goodsGroupIndex) => {
        (goodsGroup.goodsList || []).forEach((goods, goodsIndex) => {
          if (isCheckboxEnabled(goods, editShopIndex !== -1)) {
            checkboxEnabledGoodsList.push({
              ...goods,
              goodsGroupIndex,
              goodsIndex,
            });
          }
        });
      });
      const waitSetMap = {};
      // 非编辑模式
      if (editShopIndex === -1) {
        this.cartProcess._changeCurrentSelectGoods({
          rangeType: 'shop',
          checked,
          kdtId,
          isCrossShop,
        });
      } else {
        // 更新商品选中状态
        checkboxEnabledGoodsList.forEach((goods) => {
          if (goods.checked !== checked) {
            const { goodsGroupIndex, goodsIndex } = goods;
            waitSetMap[
              `shopList[${shopIndex}].goodsGroupList[${goodsGroupIndex}].goodsList[${goodsIndex}].checked`
            ] = checked;
          }
        });
        this.setYZData(waitSetMap);
        this.cartProcess._computeCheckLinkData();
      }
    },

    handleBatchChecked({ detail, currentTarget }) {
      const { editShopIndex } = this.data;
      const { shopIndex } = currentTarget.dataset;
      const { goodsList, checked, isActivity } = detail;

      if (editShopIndex === -1) {
        api
          .checkBatch(
            checked,
            goodsList.map((item) => item.goods)
          )
          .then(() => {
            if (isActivity) {
              this.cartProcess.refreshCartData();
            }
          })
          .catch((err) => {
            // 勾选接口失败，将勾选状态重置回去
            Toast(err.msg || '勾选失败啦，尝试刷新页面');
          });
      }
      // 选中
      goodsList.forEach((item) => {
        const key = `shopList[${shopIndex}].goodsGroupList[${item.goodsGroupIndex}].goodsList[${item.goodsIndex}].checked`;
        this.setYZData({
          [key]: checked,
        });
      });

      this.cartProcess._computeCheckLinkData();
    },

    handleItemChecked({ detail, currentTarget }) {
      const { editShopIndex } = this.data;
      const { shopIndex } = currentTarget.dataset;
      const {
        checked,
        goods,
        goodsIndex,
        isActivity,
        goodsGroupIndex,
      } = detail;
      const key = `shopList[${shopIndex}].goodsGroupList[${goodsGroupIndex}].goodsList[${goodsIndex}].checked`;

      // 非编辑模式
      if (editShopIndex === -1) {
        this.cartProcess._changeCurrentSelectGoods({
          checked,
          goods,
          shopIndex,
          goodsIndex,
          goodsGroupIndex,
          isActivity,
        });
      } else {
        this.setYZData({
          [key]: checked,
        });

        this.cartProcess._computeCheckLinkData();
      }
    },

    handleItemNumChange({ detail, currentTarget }) {
      const { shopIndex } = currentTarget.dataset;
      const { num, goods, goodsIndex, isActivity, goodsGroupIndex } = detail;

      this.cartProcess.setGoodsNum({
        goods,
        val: num,
        shopIndex,
        isActivity,
        goodsIndex,
        goodsGroupIndex,
      });
    },

    handleItemDelete({ detail, currentTarget }) {
      const {
        goods,
        goodsIndex,
        goodsGroupIndex,
        isActivity,
        isUnAvailable = false,
      } = detail;
      const { shopIndex } = currentTarget.dataset;

      this.cartProcess
        .handleItemDelete({
          goods,
          isActivity,
          shopIndex,
          goodsGroupIndex,
          goodsIndex,
          isUnAvailable,
        })
        .then(() => {
          this.cartProcess._removeEmptyShop();
        });
    },

    // 批量删除
    handleBatchDelete() {
      const { shopList } = this.data;
      const requestData = [];

      shopList.forEach((shop) => {
        const { goodsGroupList = [] } = shop;
        goodsGroupList.forEach(({ goodsList = [] }) => {
          goodsList.forEach((goods) => {
            if (goods.checked) {
              requestData.push({
                cartId: goods.cartId,
                kdtId: goods.kdtId,
                goodsId: goods.goodsId,
                skuId: goods.skuId,
                channelId: goods.channelId,
                activityId: goods.activityId,
              });
            }
          });
        });
      });

      this.cartProcess
        .batchDeleteGoods({
          goodsList: requestData,
        })
        .then(() => {
          this.cartProcess._removeEmptyShop();
        });
    },

    handleUnavailableClear() {
      const { unAvailableGoodsList } = this.data;
      this.cartProcess._batchDeleteGoods(unAvailableGoodsList).then(() => {
        this.setYZData({ unAvailableGoodsList: [] });
      });
    },

    handleSeparateBuy({ detail: { list = [], expressType } }) {
      this.cartProcess._buyWithGoods({
        list,
        expressType,
      });
    },

    handlePayCheckedGoods() {
      this.cartProcess.handlePayCheckedGoods();
    },

    handleShowPopup({ detail }) {
      this.cartProcess.handleShowPopup(detail);
    },

    closePopup() {
      this.setYZData({
        showPopup: false
      });
    },

    closeExchange() {
      this.setYZData({
        showExchangeModal: false
      });
    },

    activityBtnClick({ detail }) {
      const { activityInfo, goodsList } = detail;
      this.cartProcess.activityBtnClick({ activityInfo, goodsList });
    },
  };
};
