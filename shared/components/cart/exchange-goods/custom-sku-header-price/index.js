import WscComponent from 'shared/common/base/wsc-component/index';
import theme from 'shared/common/components/theme-view/theme';

function parsePrice(price) {
  return typeof price === 'number' ? Number(price / 100).toFixed(2) : price;
}

WscComponent({

  properties: {
    goods: {
      type: Object,
    },
    sku: {
      type: Object,
      observer: 'setPrice',
    },
    selectedSkuComb: {
      type: Object,
      observer: 'setPrice',
    },
    extraData: {
      type: Object,
      observer: 'setPrice',
    },
  },

  data: {
    themeGeneral: ''
  },

  ready() {
    theme.getThemeColor('general')
      .then(color => {
        this.setYZData({
          themeGeneral: color
        });
      });
  },

  methods: {
    setPrice() {
      const { sku, selectedSkuComb } = this.properties;

      if (!sku) {
        return;
      }

      // sku.price是一个格式化好的价格区间
      let { price = '', originPrice = '' } = sku;

      if (selectedSkuComb) {
        const { price: _price, originPrice: _originPrice } = selectedSkuComb;

        if (_price) {
          price = parsePrice(_price);
        }

        if (_originPrice) {
          originPrice = parsePrice(_originPrice);
        }
      }

      this.setYZData({ price, originPrice });
    }
  }
});
