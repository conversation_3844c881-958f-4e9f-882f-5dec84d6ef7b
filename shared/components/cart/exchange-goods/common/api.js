const app = getApp();

// 获取换购商品
const findExchangeSkusJson = (query) =>
  app.request({
    query,
    method: 'GET',
    path: '/wsctrade/cart/find-exchange-goods.json'
  });

// 加入购物车
const batchAddGoods = (data) =>
  app.request({
    data: {
      items: data
    },
    method: 'POST',
    path: '/wsctrade/cart/batchAddGoods.json'
  });

// 获取sku
const getGoodSkusJson = (query) =>
  app.request({
    query,
    method: 'GET',
    path: '/wsctrade/fetch-sku.json'
  });

// 删除购物车
const deleteBatch = (data) =>
  app.request({
    data: {
      ids: data
    },
    method: 'POST',
    path: '/wsctrade/cart/deleteBatchList.json'
  });

export { deleteBatch, batchAddGoods, getGoodSkusJson, findExchangeSkusJson };
