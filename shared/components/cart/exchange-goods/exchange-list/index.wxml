<!-- 换购商品列表 -->
<view class="exchange-good__body">
  <van-checkbox-group
    value="{{ checkedArr }}"
    bind:change="onChange"
  >
    <!-- 换购商品 -->
    <van-checkbox
      wx:key="index" 
      wx:for="{{ exchangeGoodsList }}"
      name="{{ item.goodsId }}"
      custom-class="exchange-good__item"
      label-class="good-radio-label"
      checked-color="{{ themeMainBgColor }}"
    > 
      <view class="radio-content">
        <view class="good-item__pitcure">
          <image
            class="good-img"
            src="{{ item.picture }}"
            mode="aspectFit"
            data-alias="{{ item.alias }}"
            catch:tap="goDetail"
          />
        </view>
        <view class="good-item__info">
          <view class="good-item__title">{{ item.title }}</view>

          <view wx:if="{{ item.desc }}" class="good-item__sku" catch:tap="handleClickSku" data-item="{{ item }}">
            <view class="sku-info {{ item.thinSkus.length > 1 ? 'multi-sku' : '' }}">
              <view>{{ item.desc }}</view>

              <van-icon
                wx:if="{{ item.thinSkus.length > 1 }}"
                class="multi-sku-icon"
                size="10px"
                name="arrow-down"
              />
            </view>
          </view>

          <view class="good-price" style="{{ priceStyle[index] }}">
            <theme-view color="main-bg" custom-class="goods-price">
              <price 
                price="{{ item.exchangePrice }}"
                originPrice="{{ item.originalPrice }}"
              />
            </theme-view>
          </view>
        </view>
      </view>
    </van-checkbox>
  </van-checkbox-group>
</view>

<base-sku
  theme-class="{{ themeClass }}"
  goods="{{ { title: skuDataGoods.title, picture: skuDataGoods.picture } }}"
  sku="{{ skuData }}"
  show="{{ isShowSkuPopup }}"
  initial-sku="{{ initialSku }}"
  message-config="{{ skuMessageConfig }}"
  properties="{{ goodsProperties }}"
  buy-text="确定"
  quota="1"
  show-add-cart-btn="{{ false }}"
  reset-stepper-on-hide="{{ true }}"
  show-add-cart-btn="{{ false }}"
  extra-data="{{ skuExtraData }}"
  generic:sku-header-price="custom-sku-header-price"
  show-buy-btn
  bind:buy="onConfirmSku"
  bind:sku-close="onSkuClose"
/>