import WscComponent from 'pages/common/wsc-component/index';
import Theme from 'shared/common/components/theme-view/theme';
import navigate from '@/helpers/navigate';
import get from '@youzan/weapp-utils/lib/get';
import money from '@youzan/weapp-utils/lib/money';
import { getGoodSkusJson } from '../common/api';
import Toast from '@vant/weapp/dist/toast/toast';
import {
  getSelectedSkuFromSkuData,
  getSkuData,
  getGoodsPropertiesStr,
  parseGoodsProperties
} from '../../utils';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

WscComponent({
  properties: {
    kdtId: Number,
    hasTheme: {
      type: Boolean,
      value: false,
      observer(hasTheme) {
        this.setYZData({
          btnClass: hasTheme ? '' : 'no-theme'
        });
      }
    },
    exchangeGoods: {
      type: Array,
      observer(val = []) {
        this.setYZData(
          {
            exchangeGoodsList: val.map((item) => ({
              ...item,
              picture: cdnImage(item.picture, '!300x300.jpg'),
              desc: item.skuStr
            }))
          },
          { immediate: true }
        );
        this.resetSelectedGoods();

        setTimeout(() => {
          this.computedGoodsPricePadding();
        });
      }
    },
    activityId: {
      type: Number,
      value: 0
    },
    activityType: {
      type: String,
      value: 0,
      observer(activityType) {
        this.data.activityType = activityType;
      }
    },
    isShowExchange: {
      type: Boolean,
      value: false,
      observer() {
        setTimeout(() => {
          this.computedGoodsPricePadding(); // 弹框动画完成后设置 价格落底
        }, 300);
      }
    },
    // 是否支持多选
    supportMulti: {
      type: Boolean,
      value: false
    }
  },

  data: {
    isShowSkuPopup: false,
    checkedArr: [],
    exchangeGoodsList: [],
    btnClass: 'no-theme',
    priceStyle: [],
    themeMainBgColor: '',
    selectedGoods: [],
    skuData: {}, // sku数据
    goodsProperties: [],
    skuDataGoods: {}, // 当前显示sku商品
    skuMessageConfig: {},
    initialSku: {},
    skuExtraData: {
      useCustomHeaderPrice: true
    }
  },

  methods: {
    onConfirmSku({ detail: selectedSku = {} }) {
      const { skuData: originSkuData, skuDataGoods } = this.data;

      const skuArr = getSelectedSkuFromSkuData(originSkuData.tree, selectedSku.selectedSkuComb);
      const skuData = getSkuData(selectedSku.selectedSkuComb);
      const skuStr = skuArr.map((sku) => sku.name).join(';');
      const properties = get(selectedSku.selectedSkuComb, 'properties', []);
      const picture = (skuArr[0] && skuArr[0].imgUrl) || skuDataGoods.picture || '';

      const { messages, selectedSkuComb } = selectedSku;
      const { id: skuId, price } = selectedSkuComb;
      const formatMessages = Object.keys(messages).map((key) => messages[key]);

      this.updateExchangeGoodsList(skuDataGoods, {
        skuStr,
        properties: parseGoodsProperties(properties),
        messages: formatMessages.length ? JSON.stringify(formatMessages) : '',
        skuId,
        skuData,
        exchangePrice: price,
        selected: true,
        picture: cdnImage(picture, '!300x300.jpg')
      });

      this.setYZData({
        isShowSkuPopup: false
      });

      setTimeout(() => {
        this.computedGoodsPricePadding();
      });
    },

    // 选择换购商品
    onChange({ detail: ids = [] }) {
      ids = ids.map((id) => +id); // 准成数字格式
      const { selectedGoods, supportMulti, exchangeGoodsList } = this.data;

      const selectedGoodsIds = selectedGoods.map((goods) => goods.goodsId);

      if (selectedGoodsIds.length < ids.length) {
        if (!supportMulti) this.cancelSelectAll();
        // 选中商品
        const selectedId = ids.find((id) => selectedGoodsIds.indexOf(id) === -1);
        const currentGoods = exchangeGoodsList.find((goods) => goods.goodsId === selectedId);

        currentGoods && this.fetchGoodsSku(currentGoods);
      } else {
        // 取消选中
        const cancelId = selectedGoodsIds.find((id) => ids.indexOf(id) === -1);
        const currentGoods = exchangeGoodsList.find((goods) => goods.goodsId === cancelId);
        currentGoods &&
          this.updateExchangeGoodsList(currentGoods, {
            skuStr: '',
            properties: [],
            skuData: [],
            selected: false
          });
      }
    },

    fetchGoodsSku(goods) {
      this.setInitialSku(goods);
      const goodsSkuIds = goods.thinSkus.map((item) => item.id);

      getGoodSkusJson({
        alias: goods.alias
      })
        .then((skuData) => {
          skuData = mapKeysCase.toCamelCase(skuData);
          const goodsProperties = get(skuData, 'itemSalePropList', []);

          const isShowSkuPopup =
            !skuData.noneSku ||
            !!(skuData.messages && skuData.messages.length) ||
            !!goodsProperties.length;

          // 过滤不可用sku
          skuData.list = skuData.list.filter((item) => goodsSkuIds.indexOf(item.id) > -1);

          // ================ 设置sku换购价
          const thinSku = goods.thinSkus.find((item) => item.id === skuData.collectionId) || {};
          skuData.price = thinSku.exchangePrice
            ? money(thinSku.exchangePrice).toYuan()
            : skuData.price;

          skuData.list.forEach((sku) => {
            const thinSku = goods.thinSkus.find((item) => item.id === sku.id) || {};
            sku.price = thinSku.exchangePrice || sku.price;
          });
          // ================ 设置sku换购价 end

          if (!isShowSkuPopup) {
            this.updateExchangeGoodsList(goods, {
              selected: true
            });
          }

          this.setYZData({
            skuDataGoods: goods,
            skuData,
            goodsProperties,
            isShowSkuPopup
          });
        })
        .catch((e) => {
          console.log(e);
          Toast('获取商品数据失败，请重试');
        });
    },

    handleClickSku(event) {
      const { currentTarget = {} } = event;
      const { dataset = {} } = currentTarget;
      const { item } = dataset;

      if (!item || item.thinSkus.length <= 1) return;
      this.fetchGoodsSku(item);
    },

    setInitialSku(skuDataGoods) {
      const initialSku = {};
      let initialMessages = {};
      const { skuData = [], properties = [] } = skuDataGoods;
      if (!skuData.length && !properties.length) return initialSku;
      const skuDataCamelCase = mapKeysCase.toCamelCase(skuData);

      skuDataCamelCase.forEach(({ kS, vId }) => {
        if (kS && vId) {
          initialSku[kS] = String(vId);
        }
      });

      const { messages } = skuDataGoods;
      try {
        initialMessages = JSON.parse(messages);
      } catch (error) {
        initialMessages = {};
      }

      properties.forEach((property) => {
        initialSku.selectedProp = initialSku.selectedProp || {};
        initialSku.selectedProp[property.propId] = (property.propValueList || []).map(
          (value) => value.propValueId
        );
      });

      this.setYZData(
        {
          initialSku,
          skuMessageConfig: {
            initialMessages
          }
        },
        { immediate: true }
      );
    },

    cancelSelectAll() {
      const { selectedGoods } = this.data;
      selectedGoods.forEach((goods) => {
        this.updateExchangeGoodsList(goods, {
          selected: false
        });
      });
    },

    // 获取商品属性和 sku 拼接字符串
    getSkuAndPropertiesStr(goods) {
      const propertiesStr = getGoodsPropertiesStr(goods.properties);

      return [goods.skuStr, propertiesStr].filter((item) => !!item).join('，');
    },

    resetSelectedGoods() {
      const { exchangeGoodsList } = this.data;
      const selectedGoods = exchangeGoodsList.filter((goods) => !!goods.selected);

      this.triggerEvent('exchage:selected-change', {
        selectedGoods
      });

      this.setYZData({
        selectedGoods,
        checkedArr: selectedGoods.map((goods) => String(goods.goodsId))
      });
    },

    // 添加已选换购商品
    updateExchangeGoodsList(currentGoods, options = {}) {
      const { exchangeGoodsList } = this.data;
      const currentGoodsIndex = exchangeGoodsList.findIndex(
        (goods) => goods.goodsId === currentGoods.goodsId
      );

      // 更新商品展示信息
      exchangeGoodsList[currentGoodsIndex] = {
        ...exchangeGoodsList[currentGoodsIndex],
        ...options
      };
      // 计算商品描述
      exchangeGoodsList[currentGoodsIndex].desc = this.getSkuAndPropertiesStr(
        exchangeGoodsList[currentGoodsIndex]
      );

      this.setYZData(
        {
          exchangeGoodsList
        },
        { immediate: true }
      );

      this.resetSelectedGoods();
    },

    computedGoodsPricePadding() {
      this.setYZData({ priceStyle: [] }, { immediate: true });
      const goodsItems = this.createSelectorQuery().selectAll('.good-item__info');
      goodsItems
        .boundingClientRect((rects) => {
          rects.forEach((rect, index) => {
            const height = rect.height;
            if (height && height < 96) {
              this.setYZData({
                [`priceStyle[${index}]`]: `padding-top: ${96 - height}px`
              });
            }
          });
        })
        .exec();
    },

    // 跳转到商品详情页
    goDetail(e) {
      const { alias = '' } = e.currentTarget.dataset;
      navigate.navigate({
        url: `/pages/goods/detail/index?alias=${alias}`
      });
    },

    onSkuClose() {
      this.setYZData({
        isShowSkuPopup: false
      });
    }
  },
  attached() {
    Theme.getThemeColor('main-bg').then((color) => {
      this.setYZData({
        themeMainBgColor: color
      });
    });
  }
});
