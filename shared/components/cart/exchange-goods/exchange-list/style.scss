.exchange-good__body {
  height: 65vh;
  overflow: scroll;
  margin: 0 12px;
  box-sizing: border-box;
}

.exchange-disabled {
  .good-item__title,
  .good-item__sku,
  .original-price,
  .exchange-price {
    color: #9b9b9b !important;
  }

  .exchange-good-btn {
    background-color: #9b9b9b;
  }
}

.exchange-good__item {
  margin-bottom: 12px;
  border-radius: 8px;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 12px;
  box-sizing: border-box;
}

.good-radio-item {
  display: flex;
  align-items: center;
  padding: 12px;
  box-sizing: border-box;
}

.good-radio-label {
  flex-grow: 1;
}

.radio-content {
  flex-grow: 1;
  display: flex;
  font-size: 12px;
  align-items: flex-start;
}

.good-item__pitcure {
  width: 96px;
  height: 96px;
  float: left;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
  background: #f2f2f2;
  background-size: cover;
  border-radius: 4px;
}

.good-img {
  position: absolute;
  margin: auto;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  background-color: #fff;
}

.good-item__info {
  flex: 1;
  position: relative;
  width: 100%;
  margin-left: 8px;
}

.good-item__info:not(:last-child) {
  border-bottom: 1px solid #f2f2f2;
}

.good-item__title {
  margin-bottom: 4px;
  line-height: 17px;
  color: #323233;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  font-size: 12px;
}

.good-item__sku {
  margin-bottom: 4px;
  color: #969799;
  font-size: 10px;
  width: 160px;
}

.sku-info {
  line-height: 16px;
  display: inline-flex;
  align-items: center;

  &.multi-sku {
    padding: 4px 8px;
    background: #f7f8fa;
    border-radius: 4px;
  }

  .multi-sku-icon {
    margin-left: 8px;
    height: 10px;
    line-height: 10px;
  }
}

.good-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.good-item__price > view {
  display: inline-block;
}

.exchange-price {
  font-size: 14px;
  color: #f44;
  margin-right: 5px;
}
.original-price {
  text-decoration: line-through;
  color: #999;
}

.msg-item {
  background-color: #f7f8fa;
}

.msg-item .van-cell {
  background-color: #f7f8fa;
}

.exchange-good-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 50px;
  background: #fcfcfc;
  padding: 0 12px;
  box-sizing: border-box;
  // position: absolute;
  // bottom: 0;
}

.combined-num {
  flex: 1;
  font-size: 14px;
}

.exchange-good-btn-wrap {
  width: 96px;
  height: 36px;
  border-radius: 18px;
  overflow: hidden;
}

.exchange-good-btn {
  font-size: 14px;
  line-height: 36px;
  color: #fff;
  text-align: center;

  &.no-theme {
    background-color: #f44;
    color: #fff;
  }
}
