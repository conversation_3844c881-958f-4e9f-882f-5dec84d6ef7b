.exchange-good {
  &-popup {
    border-radius: 12px 12px 0 0;
    overflow: hidden;
    padding-bottom: 0;
  }

  &__header {
    text-align: center;
    position: relative;
    background: #fff;
  }

  &__desc {
    padding: 12px;
    color: #323233;
    font-size: 14px;
    view {
      display: inline;
    }
  }

  &__total {
    display: inline;
  }

  &__close {
    position: absolute;
    right: 15px;
    top: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    font-size: 12px;
  }

  &__body {
    min-height: 60vh;
  }

  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    background: #fcfcfc;
    padding: 0 12px;
    box-sizing: border-box;
  }

  &__btn-wrap {
    width: 96px;
    height: 36px;
    border-radius: 18px;
    overflow: hidden;
  }

  &__btn {
    font-size: 14px;
    line-height: 36px;
    color: #fff;
    text-align: center;

    &.no-theme {
      background-color: #f44;
      color: #fff;
    }
  }
}

.header-title {
  font-size: 16px;
  letter-spacing: 0.3px;
  text-align: center;
  padding: 11px 0;
  color: #000;
}

.combined-num {
  flex: 1;
  font-size: 14px;
}
