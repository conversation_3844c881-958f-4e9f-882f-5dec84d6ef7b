import WscComponent from 'pages/common/wsc-component/index';
import { findExchange<PERSON>kus<PERSON>son, deleteBatch, batchAddGoods } from '../common/api';
import Toast from '@vant/weapp/dist/toast/toast';
import tabBehavior from '@/custom-tab-bar-v2/tab-behavior';
import { getGoodsPropertiesIds } from '../../utils';
import Theme from 'shared/common/components/theme-view/theme';

const app = getApp();

WscComponent({
  properties: {
    kdtId: Number,
    hasTheme: {
      type: Boolean,
      value: false,
    },
    plusBuyGoods: {
      type: Array,
      value: []
    },
    isShow: {
      type: Boolean,
      value: false,
      observer(isShow) {
        if (isShow && this.data.activityId) {
          const { activityId = 0 } = this.data;
          this.getExchangeGoods(activityId);
        } else {
          // 优化关闭 弹框 恢复初始数据
          setTimeout(() => {
            this.setYZData({
              supportMulti: false,
              fetchExchangeListIng: false,
              selectedGoods: [],
              exchangeGoods: [],
            });
          }, 300);
        }

        this.setYZData({
          isShowExchange: isShow
        });
      }
    },

    activityId: {
      type: Number,
      value: 0,
      observer(activityId) {
        if (this.data.isShow) {
          this.getExchangeGoods(activityId);
        }
      }
    },
    activityType: {
      type: String,
      value: 0,
      observer(activityType) {
        this.setYZData({ activityType });
      }
    },
    activityDesc: {
      type: String,
      value: ''
    },
  },

  behaviors: [tabBehavior],

  data: {
    totalNum: 1,
    isShowExchange: false,
    supportMulti: false, // 是否支持多选
    exchangeGoods: [],
    selectedGoods: [], // 当前选中的换购商品
    cartExistExchangeGoods: [], // 当前存在的换购商品
    btnLoading: false,
    fetchExchangeListIng: false,
    gradient: false // 按钮是否渐变
  },

  methods: {
    // 获取换购商品
    getExchangeGoods() {
      const { kdtId, activityId } = this.data;

      this.setYZData({
        fetchExchangeListIng: true
      });

      Toast({
        type: 'loading',
        duration: 0
      });

      findExchangeSkusJson({ kdtId, activityId, offlineId: app.getOfflineId() || 0 })
        .then((exchangeGoods = []) => {
          if (!exchangeGoods.length) {
            Toast.clear();
            this.setYZData({
              fetchExchangeListIng: false,
              exchangeGoods: []
            });
            return;
          }

          const supportMulti = exchangeGoods[0] ? exchangeGoods[0].supportMulti : false;

          this.setYZData({ exchangeGoods, supportMulti });

          // 获取到换购商品后，查询购物车中的换购商品
          this.searchCartGoods();
          Toast.clear();
          this.setYZData({
            fetchExchangeListIng: false
          });
        }).catch(() => {
          Toast.clear();
          this.setYZData({
            fetchExchangeListIng: false,
            exchangeGoods: []
          });
        });
    },

    // 查看购物车中的换购商品
    searchCartGoods() {
      const { exchangeGoods, plusBuyGoods } = this.data;
      plusBuyGoods.forEach(item => {
        const { goodsId, sku, skuData, skuId, payPrice, attachmentUrl, properties = [], messages = '' } = item;
        const goodsListIndex = exchangeGoods.findIndex(g => g.goodsId === goodsId);
        if (goodsListIndex > -1) {
          exchangeGoods[goodsListIndex] = {
            ...exchangeGoods[goodsListIndex],
            selected: true,
            skuStr: sku,
            properties,
            skuData,
            skuId,
            messages,
            exchangePrice: payPrice,
            picture: attachmentUrl
          };
        }
      });

      this.setYZData({
        exchangeGoods,
      });

      Toast.clear();
    },

    // 换购商品添加购物车校验
    onGoodsAdd() {
      const { selectedGoods } = this.data;
      const { btnLoading, fetchExchangeListIng, exchangeGoods } = this.data;

      if (!fetchExchangeListIng && !exchangeGoods.length) {
        this.onClose();
        return;
      }

      if (btnLoading) return;

      this.setYZData({
        btnLoading: true,
      }, { immediate: true });

      let refresh = true;

      const shouldDeleteGoods = this.getShouldDeleteGoods(selectedGoods);
      const shouldAddGoods = this.getShouldAddGoods(selectedGoods);

      if (!shouldDeleteGoods.length && !shouldAddGoods.length) {
        refresh = false;
        this.btnLoading = false;
      }

      this.deleteCartGoods(shouldDeleteGoods).then(async () => {
        await this.addCart(shouldAddGoods, refresh);
        this.setYZData({
          btnLoading: false,
        });
      }).catch(() => {
        this.setYZData({
          btnLoading: false,
        });
        Toast('添加购物车失败，请重试');
      });
    },

    // 从购物车中删除换购商品
    deleteCartGoods(goods) {
      if (!goods.length) return Promise.resolve();

      const parseGoods = this.parseDeleteGoods(goods);

      return deleteBatch(parseGoods)
        .then(data => {
          if (!data) {
            throw new Error('添加购物车失败，请重试');
          }
        });
    },

    // 换购商品加入购物车
    addCart(goodsItems, refresh = true) {
      if (!goodsItems.length) {
        this.triggerEvent('exchange:close-modal', true);
        if (refresh) this.triggerEvent('exchange:add-succeed', true);
        return Promise.resolve();
      }

      const parseGoodsItems = this.parseAddGoods(goodsItems);

      return batchAddGoods(parseGoodsItems).then(() => {
        Toast('已成功添加到购物车');
        this.triggerEvent('exchange:close-modal', true);
        this.triggerEvent('exchange:add-succeed', true);
      }).catch(err => {
        const { msg = '添加购物车失败，请重试' } = err || {};
        Toast(msg);
      });
    },

    parseDeleteGoods(goods) {
      const { kdtId, activityId } = this;

      return goods.map(item => ({
        skuId: item.skuId,
        cartId: item.cartId,
        goodsId: item.goodsId,
        kdtId,
        activityId,
      }));
    },

    parseAddGoods(goods) {
      const { kdtId, activityId, activityType } = this.data;

      return goods.map(item => ({
        skuId: item.skuId || item.thinSkus[0].id,
        goodsId: item.goodsId,
        payPrice: +item.exchangePrice * 100,
        messages: item.messages || '',
        kdtId,
        activityId,
        propertyIds: getGoodsPropertiesIds(item.properties),
        activityType: activityType.toString(),
        num: 1,
      }));
    },

    // 获取应该加购商品
    getShouldAddGoods(selectedGoods) {
      const addGoods = [];
      const { plusBuyGoods } = this.data;

      selectedGoods.forEach(goods => {
        const goodsInExistGoods = plusBuyGoods.find(item => item.goodsId === goods.goodsId);

        if (!goodsInExistGoods || !this.compareGoodsIsSame(goods, goodsInExistGoods)) {
          addGoods.push(goods);
        }
      });
      return addGoods;
    },

    // 获取应该删除的商品
    getShouldDeleteGoods(selectedGoods) {
      const deleteGoods = [];
      const { plusBuyGoods } = this.data;

      plusBuyGoods.forEach(goods => {
        const goodsInSelectedGoods = selectedGoods.find(item => item.goodsId === goods.goodsId);

        if (!goodsInSelectedGoods || !this.compareGoodsIsSame(goods, goodsInSelectedGoods)) {
          deleteGoods.push(goods);
        }
      });

      return deleteGoods;
    },

    // 对比两个换购商品是否一致
    compareGoodsIsSame(goods, newGoods) {
      const KEYS = ['goodsId', 'skuId'];
      let flag = true;

      KEYS.forEach(key => {
        flag = flag && goods[key] === newGoods[key];
      });
      // 对比商品留言
      flag = flag && this.parseMessageToArrString(goods.messages) === this.parseMessageToArrString(newGoods.messages);
      // 对比商品属性
      flag = flag && JSON.stringify(goods.properties || []) === JSON.stringify(newGoods.properties || []);

      return flag;
    },

    // 把留言格式化成数组字符串
    parseMessageToArrString(messages) {
      let parseMessage = [];
      try {
        parseMessage = JSON.parse(messages);
      } catch (error) {
        parseMessage = [];
      }
      if (Object.prototype.toString.call(parseMessage) === '[object Object]') {
        parseMessage = Object.keys(parseMessage).map((key) => parseMessage[key]);
      }

      return JSON.stringify(parseMessage);
    },

    selectedChange({ detail }) {
      this.setYZData({
        selectedGoods: detail.selectedGoods || []
      }, { immediate: true });
    },

    onClose() {
      this.triggerEvent('exchange:close-modal', true);
    },

    setBtnGradient() {
      Theme.getThemeType().then(type => {
        let gradient = false;

        if (type === 13) {
          gradient = true;
        }

        console.log('gradient', gradient);

        this.setYZData({
          gradient
        });
      });
    },
  },

  created() {
    this.setBtnGradient();
  },
});
