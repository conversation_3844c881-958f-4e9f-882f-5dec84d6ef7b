<view class="exchange-good-container">
  <!-- 换购商品弹框组件 -->
  <van-popup show="{{ isShowExchange }}" bind:close="onClose" position="bottom" custom-class="exchange-good-popup" custom-style="background: #f6f7f9;bottom: {{ popupBottom }}px">
    <!-- 标题 -->
    <view class="exchange-good__header">
      <view class="header-title">换购商品</view>
      <van-icon
        class="exchange-good__close"
        size="16px"
        name="cross"
        bind:click="onClose"
      />
    </view>

    <view wx:if="{{ exchangeGoods.length }}" class="exchange-good__desc">
      {{ activityDesc }}
      
      <view wx:if="{{ supportMulti }}">可换购以下任意商品</view>
      <view wx:else>
        可换购
        <theme-view color="main-bg" custom-class="exchange-good__total">
          <text>{{ totalNum }}件</text>
        </theme-view>
        商品
      </view>
    </view>

    <view class="exchange-good__body">
      <exchange-error
        wx:if="{{ !fetchExchangeListIng && !exchangeGoods.length }}"
      />
    
      <!-- 换购商品列表 -->
      <exchange-list
        wx:else
        kdt-id="{{ kdtId }}"
        has-theme="{{ hasTheme }}"
        disabled="{{ disabled }}"
        addedIndex="{{ addedIndex }}"
        activity-id="{{ activityId }}"
        activity-type="{{ activityType }}"
        exchangeGoods="{{ exchangeGoods }}"
        is-show-exchange="{{ isShowExchange }}"
        support-multi="{{ supportMulti }}"
        bind:exchage:close-model="onClose"
        bind:exchage:select-cart="onSelectExchange"
        bind:exchage:goods-added="onGoodsAdd"
        bind:exchage:selected-change="selectedChange"
      />
    </view>

    <!-- 底部导航 -->
    <view class="exchange-good__footer">
      <view class="combined-num">
        <span>已选 {{ selectedGoods.length }} 件</span>
      </view>
      <theme-view 
        custom-class="exchange-good__btn-wrap" 
        color="main-text" 
        bg="main-bg" 
        gradient="{{ gradient }}"
        gradientDeg="90"
      >
        <view class="exchange-good__btn {{ hasTheme ? '' : 'no-theme' }}" bind:tap="onGoodsAdd">
          <van-loading
            wx:if="{{ btnLoading }}"
            type="spinner"
            color="#fff"
            size="20px"
          />
          <text wx:else>确定</text>
        </view>
      </theme-view>
    </view>

  </van-popup>
</view>