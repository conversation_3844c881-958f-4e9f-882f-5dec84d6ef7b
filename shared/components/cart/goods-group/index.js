
import get from '@youzan/weapp-utils/lib/get';
import { getSkuStr } from '../utils';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

Component({
  properties: {
    goodsGroup: {
      type: Object,
      observer(goodsGroup) {
        this.setData({
          presentList: get(goodsGroup.groupActivityInfo, 'presentList', []).map(present => ({
            ...present,
            sku: getSkuStr(present.sku),
            imgUrl: cdnImage(present.attachmentUrl, '!300x300.jpg'),
            activityTag: '赠品',
            payPrice: 0,
          }))
        });
      }
    },
    kdtId: Number,
    editMode: Boolean,
    themeMainBgColor: String,
    themeMainBgAlpha10Color: String,
    designChildren: {
      type: Array,
      value: [],
    },
  },

  data: {
    goodsCartIds: '',
    presentList: [] // 赠品列表
  },

  methods: {
    handleBatchChecked({ detail }) {
      this.triggerEvent('batch-checked', detail);
    },

    handleItemChecked({ detail }) {
      this.triggerEvent('item-checked', detail);
    },

    handleItemDelete({ detail }) {
      this.triggerEvent('item-delete', detail);
    },

    handleItemNumChange({ detail }) {
      this.triggerEvent('item-num-change', detail);
    },

    handleRefreshCart() {
      this.triggerEvent('refresh-cart');
    },

    handleChangeGoodsSku({ detail }) {
      this.triggerEvent('change-goods-sku', detail);
    },

    handleShowPopup({ detail }) {
      this.triggerEvent('handle-show-popup', detail);
    },

    activityBtnClick({ detail }) {
      this.triggerEvent('activity-btn-click', detail);
    },
  }
});
