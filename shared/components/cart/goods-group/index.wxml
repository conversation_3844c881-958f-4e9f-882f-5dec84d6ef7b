<import src="./custom-tpl.wxml" />

<view class="goods-group">

  <block lock wx:for="{{ designChildren }}" wx:key="type">
    <block wx:if="{{ item.type === 'activity-info' }}">
      <activity-info
        wx:if="{{ goodsGroup.groupActivityInfo }}"
        activity-info="{{ goodsGroup.groupActivityInfo }}"
        goods-list="{{ goodsGroup.goodsList }}"
        kdt-id="{{ kdtId }}"
        theme-main-bg-color="{{ themeMainBgColor }}"
        theme-main-bg-alpha-10-color="{{ themeMainBgAlpha10Color }}"
        bind:batch-checked="handleBatchChecked"
        bind:refresh-cart="handleRefreshCart"
        bind:handle-show-popup="handleShowPopup"
        bind:activity-btn-click="activityBtnClick"
      />
    </block>

    <block wx:if="{{ item.type === 'goods-list' }}">
      <goods-item
        custom-class="goods-group__goods-item"
        wx:for="{{ goodsGroup.goodsList }}"
        wx:for-item="goods"
        wx:for-index="index"
        wx:key="cartId"
        goods="{{ goods }}"
        edit-mode="{{ editMode }}"
        is-activity="{{ !!goodsGroup.groupActivityInfo }}"
        has-present="{{ presentList && presentList.length > 0 }}"
        index="{{ index }}"
        total="{{ goodsGroup.goodsList && goodsGroup.goodsList.length }}"
        theme-main-bg-color="{{ themeMainBgColor }}"
        theme-main-bg-alpha-10-color="{{ themeMainBgAlpha10Color }}"
        bind:item-checked="handleItemChecked"
        bind:item-num-change="handleItemNumChange"
        bind:item-delete="handleItemDelete"
        bind:change-goods-sku="handleChangeGoodsSku"
      />
    </block>

    <block wx:if="{{ item.custom }}">
      <template is="{{ item.type }}" data="{{data: { goodsGroup } }}" />
    </block>
  </block>
  
  <!-- 活动赠品 -->
  <goods-item
    wx:for="{{ presentList }}"
    wx:for-item="goods"
    wx:key="index"
    goods="{{ goods }}"
    is-invalid="{{ false }}"
    is-activity="{{ true }}"
    is-can-choose="{{ false }}"
    is-can-stepper="{{ false }}"
    is-can-delete="{{ false }}"
    is-sub="{{ true }}"
    is-activity="{{ true }}"
    theme-main-bg-color="{{ themeMainBgColor }}"
    theme-main-bg-alpha-10-color="{{ themeMainBgAlpha10Color }}"
  />
</view>