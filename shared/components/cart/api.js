import { node, carmen } from 'shared/utils/request';
import { debouncePromise } from './utils';

let isCrossShop = false;
let channelId = 0;
const app = getApp();

const REQUEST_PATH = {
  // supportReviveGroup 使用新版商品分组
  list: '/wsctrade/cartGoodstList.json',
  update: '/wsctrade/cart/updateCartGoodsNum.json',
  deleteSingle: '/wsctrade/cart/deleteGoods.json',
  deleteBatch: '/wsctrade/cart/deleteBatchList.json',
  selectSingle: '/wsctrade/cart/selectGoods.json',
  unSelectSingle: '/wsctrade/cart/unselectGoods.json',
  selectBatch: '/wsctrade/cart/batchSelectGoods.json',
  unSelectBatch: '/wsctrade/cart/batchUnselectGoods.json',
  selectAll: '/wsctrade/cart/selectALLGoods.json',
  unSelectAll: '/wsctrade/cart/unselectAllGoods.json',
  reselectGoods: '/wsctrade/cart/reselect-goods.json',
  bookKey: '/wsctrade/order/book.json',
  calculatePrice: '/wscgoods/detail-api/calculate-price.json',
};

function pickIdFromGoods(goods) {
  const {
    kdtId, goodsId, skuId, storeId, channelId, activityId, cartId = null, activityAlias = ''
  } = goods;
  const data = {
    cartId,
    kdtId,
    goodsId,
    skuId,
    activityId,
    activityAlias
  };

  if (storeId > 0) {
    data.storeId = storeId;
  }

  if (channelId > 0) {
    data.channelId = channelId;
  }

  return data;
}

function fetch(path, data, method = 'POST') {
  let config = {};
  if (isCrossShop) {
    config = {
      skipKdtId: true,
      skipShopInfo: true
    };
  }

  return node({
    path,
    data,
    method,
    config
  });
}

function postBookKey(params) {
  return fetch(REQUEST_PATH.bookKey, params);
}

function fetchCartList({ cartGroupId }) {  //获取购物车列表
  const data = {
    store_id: app.getOfflineId() || 0,
    supportReviveGroup: true
  };
  if (isCrossShop) {
    data.cart_type = 'all';
  }

  if (channelId > 0) {
    data.channelId = channelId;
  }

  if(cartGroupId){
    data.groupId = cartGroupId;
  }
  return fetch(REQUEST_PATH.list, data, 'GET');
}

function updateCartItem(goods) {
  const { num } = goods;
  return fetch(REQUEST_PATH.update, {
    ...pickIdFromGoods(goods),
    num
  });
}

function deleteCartItem(goods) {
  return fetch(REQUEST_PATH.deleteSingle, pickIdFromGoods(goods));
}

function deleteCartBatch(goodsList) {
  const ids = goodsList.map(goods => pickIdFromGoods(goods));
  return fetch(REQUEST_PATH.deleteBatch, {
    ids
  });
}

function checkItem(checked, goods) {
  const data = pickIdFromGoods(goods);
  // 选中
  if (checked) return fetch(REQUEST_PATH.selectSingle, data);

  // 取消选中
  return fetch(REQUEST_PATH.unSelectSingle, data);
}

function checkBatch(checked, goods) {
  const data = {
    goodsList: (goods || []).map(item => pickIdFromGoods(item))
  };
  // 选中
  if (checked) return fetch(REQUEST_PATH.selectBatch, data);

  // 取消选中
  return fetch(REQUEST_PATH.unSelectBatch, data);
}

function groupCheckAll(checked, goods) {  //分组购物车全选，全取消
  if (channelId > 0) {
    data.channelId = channelId;
  }
  let data={
    goodsList: (goods || []).map(item => pickIdFromGoods(item))
  }
  // 全勾选
  if (checked) return fetch(REQUEST_PATH.selectBatch, data);

  // 全取消选中
  return fetch(REQUEST_PATH.unSelectBatch, data);
}

function checkAll(checked, data) {  //购物车全选，全取消
  if (channelId > 0) {
    data.channelId = channelId;
  }
  // 全勾选
  if (checked) return fetch(REQUEST_PATH.selectAll, data);

  // 全取消选中
  return fetch(REQUEST_PATH.unSelectAll, data);
}

function reselectGoods(goods) {
  return fetch(REQUEST_PATH.reselectGoods, {
    ...pickIdFromGoods(goods),
    messages: goods.messages,
    propertyIds: goods.propertyIds,
    extraAttribute: goods.extraAttribute, // 扩展信息
  });
}

function bill(data) {
  return carmen({
    api: 'youzan.trade.bill.goods.url/3.0.0/get',
    data
  });
}

// 计算商品属性
function calculatePrice(data) {
  return fetch(REQUEST_PATH.calculatePrice, data);
}

// 获取属性价格 去抖 函数
const handleAsyncPropPriceApiDebounce = debouncePromise((data) => calculatePrice(data), 300);

export default {
  fetchCartList,
  updateCartItem,
  deleteCartItem,
  deleteCartBatch,
  postBookKey,
  checkItem,
  checkBatch,
  groupCheckAll,
  checkAll,
  bill,
  reselectGoods,
  calculatePrice,
  handleAsyncPropPriceApiDebounce
};

export function setCartStatus(obj = {}) {
  isCrossShop = !!obj.isCrossShop;
  channelId = obj.channelId || 0;
}
