@import '@vant/weapp/dist/common/index.wxss';

.cart-bottom {
  background: #fff;
  position: fixed;
  bottom: 0;
  z-index: 10;
  width: 100%;
  padding: 0 12px 0 13px;
  box-sizing: border-box;
}

.cart-bottom--safe {
  padding-bottom: 34px;
}

.right-container {
  float: right;
  display: flex;
  align-items: center;
  height: 47px;
}

.pay-btn,
.pay-btn--disabled {
  display: inline-block;
  min-width: 96px;
  height: 36px;
  line-height: 36px;
  border-radius: 18px;
  background: #f44;
  color: #fff;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
}

.pay-btn--disabled {
  background: #e5e5e5 !important;
  color: #999 !important;
}

.pay-btn text,
.pay-btn--disabled text {
  display: block;
}

.checkbox-wrap {
  float: left;
  padding: 15px 0 8px;
}

.checkbox {
  display: inline-block;
  font-size: 18px;
  height: 18px;
  line-height: 1;
}

.desc {
  display: inline-block;
  font-size: 14px;
  margin-left: 5px;
  color: #333;
  vertical-align: middle;
  margin-top: -8px;
}

.icon--unchecked {
  color: #bbb;
}

.price {
  margin-right: 10px;
  font-size: 12px;
  font-weight: bold;
  line-height: 1.5;
  color: #111;
  text-align: right;
}

.tips {
  font-weight: normal;
  color: #666;
}

.total-price {
  display: inline;
  color: #f60;
}

.warning-bar {
  width: 100%;
  height: 35px;
  background: #fff7cc;
  color: #f60;
  font-size: 12px;
  text-align: center;
  line-height: 35px;
}

.loading-class {
  width: 100%;
  height: 100%;
}