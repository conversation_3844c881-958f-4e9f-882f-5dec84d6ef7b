<view class="cart-bottom van-hairline--top custom-class {{ safeBottom ? 'cart-bottom--safe' : '' }}" style="bottom: {{ popupBottom }}px">
  <view
    wx:if="{{ isCrossShop }}"
    class="warning-bar"
  >
    你需要分开结算每个店铺的商品哦
  </view>
  <form-view class="checkbox-wrap" bindtap="handleCheckAllGoods">
    <theme-view
      color="main-bg"
      custom-class="checkbox"
    >
      <van-icon
        size="18px"
        custom-class="{{ checkedAll ? '' : 'icon--unchecked' }}"
        name="{{ checkedAll ? 'checked' : 'circle'}}"
      />
    </theme-view>
    <view class="desc">
      <text>全选</text>
    </view>
  </form-view>

  <view class="right-container van-clearfix">
    <view class="price" wx:if="{{ !editMode }}">
      <view>
        <text>合计：</text>
        <theme-view color="main-bg" custom-class="total-price">
          <price price="{{ totalPrice }}"></price>
        </theme-view>
      </view>
      <text class="tips">{{ cartTips }}</text>
    </view>

    <user-authorize id="user-authorize" authTypeList="{{ hasHaitaoGoods && !editMode ? ['mobile'] : [] }}" bind:next="handleBtnTap">
      <form-view>
        <theme-view
          color="main-text" 
          bg="main-bg" 
          gradient="{{ gradient }}"
          gradientDeg="90"
          custom-class="{{ totalNum ? 'pay-btn' : 'pay-btn--disabled' }}"
        >
          <van-loading
            wx:if="{{ loading }}"
            size="20px"
            custom-class="loading-class"
            color="{{ type === 'default' ? '#c9c9c9' : '' }}"
          />
          <text wx:else>{{ editMode ? '删除' : '结算' }}</text>
        </theme-view>
      </form-view> 
    </user-authorize>
  </view>
</view>
