import Dialog from '@vant/weapp/dist/dialog/dialog';
import tabBehavior from '@/custom-tab-bar-v2/tab-behavior';
import Theme from 'shared/common/components/theme-view/theme';

Component({
  properties: {
    totalNum: Number,
    totalPrice: {
      type: String,
    },
    cartTips: String,
    editMode: <PERSON><PERSON><PERSON>,
    checkedAll: <PERSON>olean,
    isCrossShop: <PERSON>olean,
    loading: <PERSON><PERSON><PERSON>,
    hasHaitaoGoods: Boolean,
  },

  behaviors: [tabBehavior],

  externalClasses: ['custom-class'],

  data: {
    gradient: false,
  },

  observers: {
    'hasHaitaoGoods, editMode': function () {
      this.selectComponent('#user-authorize').init();
    },
  },

  created() {
    this.setBtnGradient();
  },

  methods: {
    setBtnGradient() {
      Theme.getThemeType().then((type) => {
        let gradient = false;

        if (type === 13) {
          gradient = true;
        }

        this.setData({
          gradient,
        });
      });
    },

    handleBtnTap() {
      const { editMode, totalNum, loading } = this.data;
      if (!totalNum || loading) return;
      if (editMode) {
        return Dialog.confirm({
          message: `确定要删除这${this.data.totalNum}个商品么？`,
        })
          .then(() => {
            this.triggerEvent('batch-delete-goods');
          })
          .catch(() => {
            Dialog.close();
          });
      }

      return this.triggerEvent('pay-checked-goods');
    },

    handleCheckAllGoods() {
      const { checkedAll } = this.data;
      this.triggerEvent('check-all-goods', !checkedAll);
    },
  },
});
