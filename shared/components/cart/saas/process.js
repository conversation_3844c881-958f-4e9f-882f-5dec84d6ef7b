import Dialog from '@vant/weapp/dist/dialog/dialog';
import Toast from '@vant/weapp/dist/toast/toast';
import get from '@youzan/weapp-utils/lib/get';
import { SEPARATE_BUY_TYPE } from '../constant';
import navigate from '@/helpers/navigate';
import money from '@youzan/weapp-utils/lib/money';
import args from '@youzan/weapp-utils/lib/args';
import Api from '../api';
import {
  debouncePromise,
  cartParser,
  isCheckboxEnabled,
  getIsNewHopeShop,
} from '../utils';
import {
  generateBuyGoodsList,
  generateBuyOrderData,
  separateGoods,
  separateGoodsForMixType,
  separateGoodsForLogistics,
} from '../buy';

import { setYunSdkState } from './state';

const app = getApp();

let cartProcess = null;

const ACTIVITY_TYPE_ALIAS_MAP = {
  104: 'packageBuy',
  101: 'meetReduce',
  24: 'plusBuy',
  115: 'secondHalf', // 第二支半价
};

const BTNMAP = ['再逛逛', '去凑单'];

const ACTIVITY_URL = {
  meetReduce: '/packages/ump/meet-reduce-goods/index',
  packageBuy: '/packages/ump/bundle-purchase/goods-list/index',
  secondHalf: '/packages/ump/second-half-discount/index',
};

class CartProcess {
  constructor(ctx) {
    this.$ctx = ctx;
    this.$setYZData = (...args) => {
      ctx.setYZData(...args);
      // 设置 yunskd 数据
      setYunSdkState(this.$ctx);
    };
    this.$data = ctx.data;
    this.$dialog = Dialog;
    this.$toast = Toast;

    // 设置商品数量
    this.setGoodsNum = debouncePromise(
      this._handleItemNumChange.bind(this),
      30
    );
  }

  // 选中商品
  selectGoods({ goods, isActivity, goodsGroupIndex }) {
    return this._changeCurrentSelectGoods({
      checked: true,
      goods,
      isActivity,
      goodsGroupIndex,
    });
  }

  // 取消选中
  cancelSelectGoods({ goods, isActivity, goodsGroupIndex }) {
    return this._changeCurrentSelectGoods({
      checked: false,
      goods,
      isActivity,
      goodsGroupIndex,
    });
  }

  // 全选商品
  selectAllGoods() {
    const { shopList, isCrossShop } = this.$data;

    const promises = shopList.map((shop, index) => {
      if (shop.checkedAll) {
        return;
      }
      const { kdtId } = shopList[index];
      return this._changeCurrentSelectGoods({
        rangeType: 'shop',
        checked: true,
        kdtId,
        isCrossShop,
      });
    });
    return Promise.all(promises);
  }

  // 取消全选商品
  cancelSelectAllGoods() {
    const { shopList, isCrossShop } = this.$data;

    const promises = shopList.map((shop, index) => {
      const { kdtId } = shopList[index];
      return this._changeCurrentSelectGoods({
        rangeType: 'shop',
        checked: false,
        kdtId,
        isCrossShop,
      });
    });
    return Promise.all(promises);
  }

  // 结算并跳转到提单页
  createAndGoOrder() {
    this.handlePayCheckedGoods();
  }

  // 结算
  handlePayCheckedGoods() {
    this.$setYZData(
      {
        submitLoading: true,
      },
      { immediate: true }
    );

    const checkedGoods = [];
    const checkedShopGoodsMap = {};
    const checkedShopNameMap = {};

    this._iterateCheckedGoods((item, index, shop) => {
      const { kdtId } = item;
      const { shopName } = shop;
      if (!checkedShopGoodsMap[kdtId]) {
        checkedShopGoodsMap[kdtId] = [];
      }
      if (!checkedShopNameMap[kdtId]) {
        checkedShopNameMap[kdtId] = shopName;
      }
      checkedShopGoodsMap[kdtId].push(item);
      checkedGoods.push(item);
    });

    // 跨店购物车分开结算
    if (Object.keys(checkedShopGoodsMap).length > 1) {
      this.$setYZData({
        submitLoading: false,
        separateBuy: {
          show: true,
          type: SEPARATE_BUY_TYPE.MULTISHOP,
          data: separateGoods(checkedShopGoodsMap, checkedShopNameMap),
        },
      });
    } else {
      this._buyWithGoods({ list: checkedGoods });
    }
  }

  // 删除单个商品
  handleItemDelete({
    goods,
    isActivity,
    shopIndex,
    goodsIndex,
    goodsGroupIndex,
    isUnAvailable = false,
  }) {
    if (
      !this._isNum(shopIndex) ||
      !this._isNum(goodsGroupIndex) ||
      !this._isNum(goodsIndex)
    ) {
      [shopIndex, goodsGroupIndex, goodsIndex] = this._getIndex(goods);
    }

    let currentGoods = null;
    if (!isUnAvailable) {
      currentGoods =
        this.$data.shopList[shopIndex].goodsGroupList[goodsGroupIndex]
          .goodsList[goodsIndex];
    } else {
      currentGoods = this.$data.unAvailableGoodsList[goodsIndex];
    }

    return Api.deleteCartItem(currentGoods)
      .then(() => {
        if (isUnAvailable) {
          // 删除失效商品
          const { unAvailableGoodsList } = this.$data;
          unAvailableGoodsList.splice(goodsIndex, 1);
          this.$setYZData(
            {
              unAvailableGoodsList,
            },
            { immediate: true }
          );
        } else {
          const { goodsGroupList = [] } = this.$data.shopList[shopIndex];
          const { goodsList = [] } = goodsGroupList[goodsGroupIndex];
          goodsList.splice(goodsIndex, 1);
          this.$setYZData(
            {
              [`shopList[${shopIndex}].goodsGroupList[${goodsGroupIndex}].goodsList`]:
                goodsList,
            },
            { immediate: true }
          );
        }

        this._computeCheckLinkData();
        this.refreshCartData();
      })
      .catch((err) => {
        this.$toast(err.msg || '删除商品失败啦，重新试试');
      });
  }

  // 批量删除
  batchDeleteGoods({ goodsList: goodsDeleteList = [] }) {
    const { shopList } = this.$data;
    const goodsDeleteListFind = [];

    goodsDeleteList.forEach((goods) => {
      const [shopIndex, goodsGroupIndex, goodsIndex] = this._getIndex(goods);

      const currentGoods =
        shopList[shopIndex].goodsGroupList[goodsGroupIndex].goodsList[
          goodsIndex
        ];
      goodsDeleteListFind.push(currentGoods);

      shopList[shopIndex].goodsGroupList[goodsGroupIndex].goodsList.splice(
        goodsIndex,
        1
      );
    });

    return this._batchDeleteGoods(goodsDeleteListFind).then(() => {
      this.refreshCartData();

      this.$setYZData({
        shopList,
      });

      this._computeCheckLinkData();
    });
  }

  // 清空购物车
  emptyCart() {
    const { shopList } = this.$data;
    let allGoods = [];
    shopList.forEach(({ goodsGroupList = [] }) => {
      goodsGroupList.forEach(({ goodsList = [] }) => {
        allGoods = allGoods.concat(goodsList);
      });
    });

    return this._batchDeleteGoods(allGoods)
      .then(() => {
        this.refreshCartData();
      })
      .catch((err) => {
        this.$toast(err.msg || '清空失败啦，重新试试');
        return Promise.reject(err);
      });
  }

  /**
   * 更新商品sku
   * @param {*} { goods }
   * @memberof CartProcess
   */
  reselectGoods({ goods }) {
    const { shopList } = this.$data;
    this.$toast.loading({
      message: '加载中...',
    });
    const [shopIndex, goodsGroupIndex, goodsIndex] = this._getIndex(goods);
    const curGoods =
      shopList[shopIndex].goodsGroupList[goodsGroupIndex].goodsList[
        goodsIndex
      ] || [];

    return Api.reselectGoods({
      ...curGoods,
      ...goods, // 使用新数据覆盖老数据
    })
      .then((res) => {
        this.$toast.clear();
        return res;
      })
      .catch((err) => {
        this.$toast(err.msg || '更新失败啦，重新试试');
      });
  }

  _batchDeleteGoods(goodsList) {
    return Api.deleteCartBatch(goodsList).catch((err) => {
      this.$toast(err.msg || '删除商品失败啦，重新试试');
      return Promise.reject(err);
    });
  }

  // 结算购物车
  _goOrder({ goToBuyData, expressType }) {
    app.logger.log({
      et: 'click',
      ei: 'cartpage_buy',
      en: '结算购物车',
      si: app.getKdtId(),
    });

    if (typeof expressType === 'number') {
      goToBuyData.delivery = {
        expressTypeChoice: +expressType,
      };
    }

    // 跨店购物车，更新全局kdtId
    const currentKdtId = goToBuyData.items[0].kdtId;
    if (app.getKdtId() !== currentKdtId) {
      app.updateKdtId(currentKdtId, false, {
        mark: '900',
      });
    }

    Api.postBookKey(goToBuyData)
      .then(({ bookKey }) => {
        this.$setYZData({
          submitLoading: false,
          bookKey,
        });

        navigate.navigate({
          url: `/packages/order/index?orderFrom=cart&bookKey=${bookKey}`,
        });
      })
      .catch((err) => {
        this.$setYZData({
          submitLoading: false,
        });
        Toast(err.msg || '结算失败，请重试');
      });
  }

  /**
   * 新希望店铺下单
   * @param {*} param0
   */
  _goOrderNewShop({ goodsList, expressType }) {
    app.logger.log({
      et: 'click',
      ei: 'cartpage_buy',
      en: '结算购物车',
      si: app.getKdtId(),
    });

    const params = {
      type: 'goods',
      goods_list: goodsList,
    };

    if (typeof expressType === 'number') {
      params.expressTypeChoice = expressType;
    }

    // 跨店购物车，更新全局kdtId
    const currentKdtId = goodsList[0].kdtId;
    if (app.getKdtId() !== currentKdtId) {
      app.updateKdtId(currentKdtId, false, {
        mark: '901',
      });
    }

    this.$setYZData({
      submitLoading: false,
    });

    const dbid = app.db.set(params);
    navigate.navigate({
      url: '/packages/order/index?orderFrom=cart&dbid=' + dbid,
    });
  }

  _buyWithGoods({ list = [], expressType } = {}) {
    let tmp = separateGoodsForMixType(list);
    if (tmp.needSeparate) {
      this.$setYZData({
        submitLoading: false,
        separateBuy: {
          show: true,
          type: SEPARATE_BUY_TYPE.MIX_TYPE,
          data: tmp.data,
        },
      });
      return;
    }

    tmp = separateGoodsForLogistics(list);
    if (tmp.needSeparate) {
      this.$setYZData({
        submitLoading: false,
        separateBuy: {
          show: true,
          type: SEPARATE_BUY_TYPE.LOGISTICS,
          data: tmp.data,
        },
      });
      return;
    }

    const kdtId = get(list, '[0].kdtId');
    const isNewHopeShop = getIsNewHopeShop(kdtId, this.$data.shopList);

    if (isNewHopeShop) {
      // 新希望店铺下单，只支持本地存储的方式
      const goodsList = generateBuyGoodsList(list);

      if (goodsList.length) {
        this.$setYZData({
          'separateBuy.show': false,
        });

        this._goOrderNewShop.call(this, {
          goodsList,
          expressType,
        });
      } else {
        Toast('请选择商品');
      }
    } else {
      const goToBuyData = generateBuyOrderData(list);
      if (goToBuyData.items.length) {
        this.$setYZData({
          'separateBuy.show': false,
        });

        this._goOrder.call(this, {
          goToBuyData,
          expressType,
        });
      } else {
        this.$toast('请选择商品');
      }
    }
  }

  _handleItemNumChange({
    goods,
    val: num,
    shopIndex,
    goodsIndex,
    isActivity,
    goodsGroupIndex,
  }) {
    if (
      !this._isNum(shopIndex) ||
      !this._isNum(goodsGroupIndex) ||
      !this._isNum(goodsIndex)
    ) {
      [shopIndex, goodsGroupIndex, goodsIndex] = this._getIndex(goods);
    }

    const currentGoods =
      this.$data.shopList[shopIndex].goodsGroupList[goodsGroupIndex].goodsList[
        goodsIndex
      ];
    const key = `shopList[${shopIndex}].goodsGroupList[${goodsGroupIndex}].goodsList[${goodsIndex}].num`;
    const lastNum = currentGoods.num;

    this.$setYZData({
      [key]: num,
    });
    if (currentGoods.checked) {
      this._updateTotalCheckedPrice();
    }

    return Api.updateCartItem({
      ...currentGoods,
      num,
    })
      .catch((err) => {
        this.$toast(err.msg || '数据更新失败啦，重新试试');
        this.$setYZData({
          [key]: lastNum,
        });
        if (currentGoods.checked) {
          this._updateTotalCheckedPrice();
        }
      })
      .then(() => {
        this.refreshCartData();
      });
  }

  /**
   * 改变商品选中状态
   * @param {Boolean} isCrossShop 是否是跨店购物车，小程序不支持跨店，所以为 false
   */
  _changeCurrentSelectGoods({
    rangeType = 'single',
    checked,
    goods,
    kdtId,
    isCrossShop = false,
    shopIndex,
    goodsIndex,
    isActivity,
    goodsGroupIndex,
  }) {
    if (rangeType === 'single') {
      if (
        !this._isNum(shopIndex) ||
        !this._isNum(goodsGroupIndex) ||
        !this._isNum(goodsIndex)
      ) {
        [shopIndex, goodsGroupIndex, goodsIndex] = this._getIndex(goods);
      }

      const currentGoods =
        this.$data.shopList[shopIndex].goodsGroupList[goodsGroupIndex]
          .goodsList[goodsIndex];

      return this._selectGoodsSingle({
        goods: currentGoods,
        checked,
        shopIndex,
        goodsIndex,
        goodsGroupIndex,
      }).then(() => {
        this.refreshCartData();
      });
    }

    if (rangeType === 'shop') {
      if (!this._isNum(shopIndex)) {
        shopIndex = this.$data.shopList.findIndex(
          (shop) => shop.kdtId === kdtId
        );
      }

      return this._selectGoodsShop({
        shopIndex,
        checked,
        kdtId,
        isCrossShop,
      }).then(() => {
        this.refreshCartData();
      });
    }
  }

  setGroupId(val) {
    this.$data.cartGroupId = val;
    this.refreshCartData(val);
  }

  // 唤起购物车活动弹窗
  handleShowPopup(detail) {
    this.$setYZData({
      showPopup: true,
      activityInfo: detail,
    });
  }

  setActivityTypeAlias(activityInfo) {
    const { activityType } = activityInfo;
    const activityTypeAlias = ACTIVITY_TYPE_ALIAS_MAP[activityType];
    this.$setYZData({
      activityTypeAlias,
    });
  }

  setActivityInfo({ activityInfo, goodsList }) {
    this.$setYZData({
      activityInfo,
      goodsList,
    });

    this.setActivityTypeAlias(activityInfo);

    if (
      ['meetReduce', 'packageBuy', 'secondHalf'].indexOf(
        this.$data.activityTypeAlias
      ) !== -1
    ) {
      this.setCommonActivityInfo();
    } else if (['plusBuy'].indexOf(this.$data.activityTypeAlias) !== -1) {
      this.setPlusBuyActivityInfo();
    }
  }

  // 设置普通活动
  setCommonActivityInfo() {
    const { activityInfo } = this.$data;
    let activityBtnTxt = '';
    if (activityInfo.meet) {
      activityBtnTxt = BTNMAP[0];
    } else if (!activityInfo.meet && activityInfo.activityUrl) {
      activityBtnTxt = BTNMAP[1];
    }
    this.$setYZData({
      activityBtnTxt,
    });
  }

  setPlusBuyActivityInfo() {
    const { goodsList, activityInfo } = this.$data;
    // 获取换购商品
    const plusBuyGoods = goodsList.filter((item) => item.activityId);

    let activityBtnTxt = '';
    let exchangeModalDesc = '';
    if (!activityInfo.meet) {
      activityBtnTxt = '去凑单';
    } else if (!plusBuyGoods.length) {
      exchangeModalDesc = `已购满${+money(
        activityInfo.conditionPrice
      ).toYuan()}元，`;
      activityBtnTxt = '去换购';
    } else {
      exchangeModalDesc = `已购满${+money(
        activityInfo.conditionPrice
      ).toYuan()}元，`;
      activityBtnTxt = '重新换购';
    }
    this.$setYZData({
      plusBuyGoods,
      exchangeModalDesc,
      activityBtnTxt,
    });
  }

  activityBtnClick({ activityInfo, goodsList }) {
    this.setActivityInfo({ activityInfo, goodsList });
    const { activityTypeAlias, activityBtnTxt } = this.$data;

    if (['plusBuy'].indexOf(activityTypeAlias) !== -1) {
      // 加价购活动
      if (activityBtnTxt !== '去凑单') {
        // 展示换购组件
        this.$setYZData({
          showExchangeModal: true,
        });
      } else {
        wx.navigateTo({
          url: args.add('/packages/ump/plusbuy/index', {
            activityId: activityInfo.activityId,
          }),
        });
      }

      return;
    }

    if (BTNMAP.includes(activityBtnTxt)) {
      this.toActivityUrl();
    }
  }

  toActivityUrl() {
    const { activityInfo, activityTypeAlias } = this.$data;
    const { activityAlias } = activityInfo;
    navigate.navigate({
      url: `${ACTIVITY_URL[activityTypeAlias]}?alias=${activityAlias}`,
    });
  }

  // 刷刷新购物车数据
  refreshCartData(groupId) {
    const { editShopIndex, cartGroupId } = this.$data;
    // 编辑状态不刷新
    if (editShopIndex !== -1) return;
    this.$toast.loading({
      message: '加载中...',
    });
    return Api.fetchCartList({
      cartGroupId: groupId === undefined ? cartGroupId : groupId,
    })
      .then((res) => {
        this.$toast.clear();
        wx.stopPullDownRefresh();
        // 购物车存在跨店，是个list
        if (!res.length) {
          this.$setYZData(
            {
              shopList: [],
              allGoodsIds: [],
              isFetching: false,
              isPullDownRefresh: false,
            },
            { immediate: true }
          );
        } else {
          const cartData = cartParser(res);
          this.$setYZData(
            {
              ...cartData,
              isFetching: false,
              isPullDownRefresh: false,
            },
            { immediate: true }
          );
          this._removeEmptyShop();
          this._computeCheckLinkData();
        }
      })
      .catch((e) => {
        this.$toast((e && e.msg) || '网络异常，请刷新重试');
        return Promise.reject(e);
      });
  }

  // 选中店铺商品
  _selectGoodsShop({ shopIndex, checked, kdtId, isCrossShop }) {
    const waitSetMap = {};

    // 更新商品选中状态
    this.$data.shopList[shopIndex].goodsGroupList.forEach(
      (goodsGroup, goodsGroupIndex) => {
        (goodsGroup.goodsList || []).forEach((goods, index) => {
          if (isCheckboxEnabled(goods) && goods.checked !== checked) {
            waitSetMap[
              `shopList[${shopIndex}].goodsGroupList[${goodsGroupIndex}].goodsList[${index}].checked`
            ] = checked;
          }
        });
      }
    );

    this.$setYZData(waitSetMap);
    this._computeCheckLinkData();
    if (this.$data.cartGroupId) {
      return Api.groupCheckAll(
        checked,
        this.$data.shopList[0].goodsGroupList.map((item) => item.goodsList[0]),
        app
      ).catch((err) => {
        this.$toast(err.msg || '勾选失败啦，重新试试');
        // 重置
        Object.keys(waitSetMap).forEach((item) => {
          waitSetMap[item] = !checked;
        });
        this.$setYZData(waitSetMap);
        this._computeCheckLinkData();
      });
    }
    return Api.checkAll(checked, { kdtId, isPlatCart: isCrossShop }).catch(
      (err) => {
        this.$toast(err.msg || '勾选失败啦，重新试试');
        // 重置
        Object.keys(waitSetMap).forEach((item) => {
          waitSetMap[item] = !checked;
        });
        this.$setYZData(waitSetMap);
        this._computeCheckLinkData();
      }
    );
  }

  // 切换单个商品选中
  _selectGoodsSingle({
    checked,
    goods,
    shopIndex,
    goodsIndex,
    goodsGroupIndex,
  }) {
    const key = `shopList[${shopIndex}].goodsGroupList[${goodsGroupIndex}].goodsList[${goodsIndex}].checked`;

    this.$setYZData({
      [key]: checked,
    });

    this._computeCheckLinkData();

    return Api.checkItem(checked, goods).catch((err) => {
      // 勾选接口失败，将勾选状态重置回去
      this.$toast(err.msg || '勾选失败啦，重新试试');

      this.$setYZData({
        [key]: !checked,
      });

      this._computeCheckLinkData();
      return Promise.reject();
    });
  }

  _computeCheckLinkData() {
    this._updateCheckedAll();
    this._updateTotalCheckedNum();
    this._updateTotalCheckedPrice();
    this._updateHasHaitaoGoods();
  }

  _updateTotalCheckedNum() {
    let totalCheckedNum = 0;
    this._iterateCheckedGoods(() => totalCheckedNum++);

    this.$setYZData({
      totalCheckedNum,
    });
  }

  _updateTotalCheckedPrice() {
    let totalCheckedPrice = 0;
    let cartTips = '不含运费';
    let totalPreferencePrice = 0; // 店铺优惠金额
    const selectedPreferencePriceObj = {}; // 店铺优惠金额
    this._iterateCheckedGoods((item, index, shop) => {
      selectedPreferencePriceObj[shop.kdtId] =
        shop.selectedPreferencePrice || 0;
      totalCheckedPrice += item.payPrice * item.num;
      if (item.tariffRule === 0) {
        cartTips = '不含运费，不含进口税';
      }
    });
    Object.keys(selectedPreferencePriceObj).forEach((key) => {
      totalPreferencePrice += selectedPreferencePriceObj[key];
    });

    this.$setYZData({
      cartTips,
      totalCheckedPrice: Math.max(totalCheckedPrice - totalPreferencePrice, 0),
    });
  }

  _removeEmptyShop() {
    const { shopList, editShopIndex } = this.$data;
    const curShopList = [];
    let curEditShopIndex = editShopIndex;
    // 无商品时删除店铺
    shopList.forEach((shop) => {
      const { goodsGroupList = [] } = shop;
      const hasGoods = goodsGroupList.some(
        (goodsGroup) => !!goodsGroup.goodsList.length
      );
      if (hasGoods) {
        curShopList.push(shop);
      } else {
        // 店铺商品删除完后，取消编辑状态
        curEditShopIndex = -1;
      }
    });

    if (curShopList.length !== shopList.length) {
      this.$setYZData({
        shopList: curShopList,
      });
    }

    if (curEditShopIndex !== editShopIndex) {
      this.$setYZData({
        editShopIndex: curEditShopIndex,
      });
      this.refreshCartData();
    }
  }

  _updateCheckedAll() {
    const { shopList, editShopIndex } = this.$data;
    shopList.forEach((shop, index) => {
      const { goodsGroupList = [] } = shop;
      let allGoodsList = [];
      goodsGroupList.forEach((goodsGroup) => {
        allGoodsList = allGoodsList.concat(goodsGroup.goodsList);
      });
      let checkboxEnabledGoodsList = allGoodsList.filter((goods) =>
        isCheckboxEnabled(goods)
      );
      if (editShopIndex !== -1) {
        checkboxEnabledGoodsList = allGoodsList;
      }

      let shopCheckedAll = checkboxEnabledGoodsList.every(
        (item) => item.checked
      );
      if (checkboxEnabledGoodsList.length == 0) {
        shopCheckedAll = false;
      }

      if (shopCheckedAll !== shop.checkedAll) {
        this.$setYZData({
          [`shopList[${index}].checkedAll`]: shopCheckedAll,
        });
      }
    });

    const checkedAll = shopList.every((item) => item.checkedAll);
    this.$setYZData({
      checkedAll,
    });
  }

  /**
   *
   * @param {*} goods { kdtId, cartId, unique }
   * @returns
   * @memberof CartProcess
   */
  _getIndex(goods = {}) {
    const { kdtId, cartId = 0, unique = 0 } = goods;
    let goodsIndex = -1;
    const { shopList } = this.$data;
    const shopIndex = shopList.findIndex((shop) => shop.kdtId === kdtId);

    const goodsGroupIndex = (
      shopList[shopIndex].goodsGroupList || []
    ).findIndex((goodsGroup) => {
      const goodsList = goodsGroup.goodsList || [];
      goodsIndex = goodsList.findIndex((item) => {
        if (cartId) {
          return item.cartId === cartId;
        }
        // unique：兼容有赞云，有赞云历史没有 cartId
        return item.unique === unique;
      });
      if (goodsIndex !== -1) {
        return true;
      }
      return false;
    });

    return [shopIndex, goodsGroupIndex, goodsIndex];
  }

  _iterateGoods(fn) {
    const { shopList } = this.$data;
    shopList.forEach((shop) => {
      const { goodsGroupList = [] } = shop;
      goodsGroupList.forEach((goodsGroup) => {
        const { goodsList = [] } = goodsGroup;
        goodsList.forEach((item, index) => {
          fn(item, index, shop);
        });
      });
    });
  }

  _iterateCheckedGoods(fn) {
    this._iterateGoods((...args) => {
      const item = args[0];
      if (item && item.checked) {
        fn(...args);
      }
    });
  }

  _isNum(val) {
    return /^[\d]+$/.test(Math.abs(val));
  }

  _updateHasHaitaoGoods() {
    let hasHaitaoGoods = false;
    this._iterateCheckedGoods((item) => {
      const rule = item.settlementRule || {};
      const mark = rule.settlementMark || '';
      if (mark === 'HAITAO') {
        hasHaitaoGoods = true;
      }
    });
    this.$setYZData({ hasHaitaoGoods });
  }

  _updateYzData(waitSetMap) {
    this.$setYZData(waitSetMap);
  }
}

export const resetCartProcess = () => {
  cartProcess = null;
};

export default function getCartProcess(...arg) {
  if (cartProcess) return cartProcess;
  cartProcess = new CartProcess(...arg);
  return cartProcess;
}
