import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import getCartProcess, { resetCartProcess } from './process';
import { onGoodsListChange } from './event';

export const initCartProcess = {
  onLoad() {
    resetCartProcess();
    this.cartProcess = getCartProcess(this);
  }
};

export default ({
  ...YunPageConfig,
  onLoad() {
    this.getYunSdk().resetPage();
    resetCartProcess();
    this.cartProcess = getCartProcess(this);
    // //////////////yunsdk初始//////////////////
    const sdk = this.getYunSdk();

    // 注册开放数据
    sdk.page.__setData('goodsList', this.data.shopList);

    // 注册开放事件
    sdk.setPageEvent('onGoodsListChange', onGoodsListChange);

    // 清空购物车
    sdk.setPageProcess('emptyCart', this.cartProcess.emptyCart.bind(this.cartProcess));
    // 删除单个商品
    sdk.setPageProcess('deleteGoods', this.cartProcess.handleItemDelete.bind(this.cartProcess));
    // 批量删除商品
    sdk.setPageProcess('batchDeleteGoods', this.cartProcess.batchDeleteGoods.bind(this.cartProcess));
    // 购物车选中流程
    sdk.setPageProcess('selectGoods', this.cartProcess.selectGoods.bind(this.cartProcess));
    // 购物车取消选中流程
    sdk.setPageProcess('cancelSelectGoods', this.cartProcess.cancelSelectGoods.bind(this.cartProcess));
    // 购物车全选流程
    sdk.setPageProcess('selectAllGoods', this.cartProcess.selectAllGoods.bind(this.cartProcess));
    // 购物车取消全选流程
    sdk.setPageProcess('cancelSelectAllGoods', this.cartProcess.cancelSelectAllGoods.bind(this.cartProcess));
    // 商品数量设置流程
    sdk.setPageProcess('setGoodsNum', this.cartProcess.setGoodsNum.bind(this.cartProcess));
    // 购物车下单流程
    sdk.setPageProcess('createAndGoOrder', this.cartProcess.createAndGoOrder.bind(this.cartProcess));
    // 设置购物车分组id,刷新购物车
    sdk.setPageProcess('setGroupId', this.cartProcess.setGroupId.bind(this.cartProcess));
    // 设置是否唤起购物车活动弹窗
    sdk.setPageProcess('showPopup', this.cartProcess.handleShowPopup.bind(this.cartProcess));
    // 设置是否唤起弹窗和页面跳转
    sdk.setPageProcess('showExchangeModal', this.cartProcess.activityBtnClick.bind(this.cartProcess));
  }
});

