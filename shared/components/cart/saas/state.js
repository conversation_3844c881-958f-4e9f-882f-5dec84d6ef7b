import pick from '@youzan/weapp-utils/lib/pick';
import { onGoodsListChange } from './event';

function getShopList(ctx) {
  return ctx.data.shopList.map(shop => {
    const goodsGroupList = (shop.goodsGroupList || []).map(goodsGroup => {
      const goodsList = (goodsGroup.goodsList || []).map(goods => {
        return pick(goods, [
          'cartId',
          'kdtId',
          'goodsId',
          'skuId',
          'sku',
          'checked',
          'activityId',
          'storeId',
          'channelId',
          'canyinId',
          'alias',
          'title',
          'attachmentUrl',
          'num',
          'payPrice',
          'originPrice',
          'stock',
          'limitNum',
          'activityType',
          'activityAlias',
          'messages',
          'unique',
          'revive'
        ]);
      });

      return {
        goodsList,
        groupActivityInfo: goodsGroup.groupActivityInfo,
      };
    });

    return {
      goodsGroupList,
      kdtId: shop.kdtId,
      shopName: shop.shopName,
    };
  });
}

function getGoodsList(ctx) {
  return ctx.data.shopList.map(shop => {
    const items = [];

    (shop.goodsGroupList || []).forEach(goodsGroup => {
      (goodsGroup.goodsList || []).forEach(goods => {
        const item = pick(goods, [
          'cartId',
          'kdtId',
          'goodsId',
          'skuId',
          'propertyIds',
          'properties',
          'sku',
          'checked',
          'activityId',
          'storeId',
          'channelId',
          'canyinId',
          'alias',
          'title',
          'attachmentUrl',
          'num',
          'payPrice',
          'originPrice',
          'stock',
          'limitNum',
          'activityType',
          'activityAlias',
          'messages',
          'unique',
          'revive',
        ]);

        items.push(item);
      });
    });

    return {
      activities: shop.activities,
      items,
      kdtId: shop.kdtId,
      shopName: shop.shopName,
    };
  });
}

function getCartState(ctx) {
  const { editShopIndex, checkedAll, totalCheckedPrice } = ctx.data;
  return {
    editShopIndex,
    checkedAll,
    totalCheckedPrice,
  };
}

export const setYunSdkState = (ctx) => {
  if (!ctx.getYunSdk) return;

  // shopList
  ctx.getYunSdk().page.__setData('shopList', getShopList(ctx));

  // goodsList
  ctx.getYunSdk().page.__setData('goodsList', getGoodsList(ctx));

  // cartState
  ctx.getYunSdk().page.__setData('cartState', getCartState(ctx));

  onGoodsListChange.trigger();
};
