import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import get from '@youzan/weapp-utils/lib/get';

// 获取属性值 id 数组
export function getGoodsPropertiesIds(properties = []) {
  const propertiesIds = [];

  mapKeysCase.toCamelCase(properties).forEach((currentProperty) => {
    const propValueList = get(currentProperty, 'propValueList', []);
    propValueList.forEach((currentValue) => {
      propertiesIds.push(currentValue.propValueId);
    });
  });

  return propertiesIds;
}

// 获取商品属性
export function getGoodsPropertiesStr(properties = []) {
  const propertiesArr = [];

  properties.forEach(currentProperty => {
    const propValueList = get(currentProperty, 'propValueList', []);
    propValueList.forEach(currentValue => {
      propertiesArr.push(currentValue.propValueName);
    });
  });

  return propertiesArr.join('，');
}

/**
 * 把sku 组件获取的数据格式化成下面格式
[
  {
    "prop_id": 131127,
    "prop_name": "杯型",
    "prop_value_list": [
      {
          "prop_value_id": 571382,
          "prop_value_name": "大杯"
      }
    ]
  }
]
 */
export function parseGoodsProperties(properties = []) {
  return mapKeysCase.toCamelCase(properties).map((property) => {
    return {
      propId: property.kId,
      propName: property.k,
      propValueList: (property.v || []).map((value) => ({
        propValueId: value.id,
        propValueName: value.name,
      })),
    };
  });
}
