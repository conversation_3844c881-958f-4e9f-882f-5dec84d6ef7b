import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import get from '@youzan/weapp-utils/lib/get';
import { getSkuStr, formatSku } from './sku';
import { getGoodsPropertiesStr } from './property';

export {
  getSkuStr,
  normalizeSkuTree,
  formatSku,
  getSelectedSkuFromSkuData,
  getSkuData
} from './sku';
export { getGoodsPropertiesStr, getGoodsPropertiesIds, parseGoodsProperties } from './property';

// 获取商品sku 和属性信息
export function getGoodsSkuProperty(goods) {
  const skuStr = getSkuStr(goods.sku);
  const propertiesStr = getGoodsPropertiesStr(goods.properties);

  return [skuStr, propertiesStr].filter((item) => !!item).join('，');
}

// 去抖函数 Promise 版
export function debouncePromise(fn, wait) {
  let timer = null;

  return function (...arg) {
    return new Promise((reslove, reject) => {
      const context = this;

      clearTimeout(timer);
      timer = setTimeout(() => {
        fn.apply(context, arg)
          .then((res) => {
            reslove(res);
          })
          .catch((err) => {
            reject(err);
          });
      }, wait);
    });
  };
}

// 判断商品是否可选
export function isCheckboxEnabled(goods, editMode = false) {
  // 编辑模式都可选
  if (editMode) return true;

  // 不满足起售商品禁止选中
  if (goods.startSaleNum && goods.num < goods.startSaleNum) return false;

  // 待复活商品
  if (goods.revive) return false;

  // 待开售商品禁止选中
  const nowTime = new Date().getTime() / 1000;
  if (goods.startSoldTime && goods.startSoldTime > nowTime) return false;

  return true;
}

// 获取商品 unique
function getUnique(goods) {
  const goodsPropertiesIds = goods.propertyIds || [];
  return `${goods.kdtId}-${goods.goodsId}-${goods.skuId}-${
    goods.activityId || 0
  }-${goodsPropertiesIds.join('-')}`;
}

// 判断是否是新希望店铺
export function getIsNewHopeShop(kdtId, shopList = []) {
  const findShop = shopList.find((shop) => shop.kdtId === kdtId);
  return get(findShop, 'isNewHopeShop', false);
}

export function cartParser(rawData) {
  const unAvailableGoodsList = [];

  const rawDataCamel = mapKeysCase.toCamelCase(rawData);

  const allGoodsIds = [];
  const shopList = (rawDataCamel || []).map((item) => {
    const {
      unavailableItems,
      activities,
      shopName,
      kdtId,
      selectedPreferencePrice = 0,
      goodsGroupList = [],
      isNewHopeShop = false
    } = item;

    const goodsGroupListFormated = goodsGroupList.map((goodsGroup = {}) => {
      const goodsList = (goodsGroup.goodsList || []).map((item) => {
        const { activityId, goodsId } = item;
        if (allGoodsIds.indexOf(goodsId) === -1) {
          allGoodsIds.push(goodsId);
        }
        return {
          cartId: item.cartId,
          kdtId: item.kdtId,
          channelId: item.channelId,
          canyinId: item.canyinId,
          goodsId,
          skuId: item.skuId,
          storeId: item.storeId,
          title: item.title,
          num: item.num,
          stock: item.stockNum,
          limitNum: item.limitNum || 0,
          cutPrice: item.cutPrice, // 比加入时降价
          price: item.payPrice,
          payPrice: item.payPrice,
          originPrice: item.originPrice,
          sku: getGoodsSkuProperty(item),
          skuData: formatSku(item.sku),
          propertyIds: item.propertyIds,
          checked: !!item.selectState && !item.disableSelect,
          maxNum: +item.limitNum ? Math.min(item.limitNum, item.stockNum) : item.stockNum,
          attachmentUrl: item.attachmentUrl,
          imgUrl: cdnImage(item.attachmentUrl, '!300x300.jpg'),
          unique: getUnique(item),
          extraAttribute: item.extraAttribute || '{}',
          messages: item.messages || '{}',
          alias: item.alias,
          logisticsTypeList: item.logisticsTypeList,
          goodsType: item.goodsType,
          activityId,
          activityType: item.activityType,
          activityAlias: item.activityAlias || '',
          activityTag: item.activityTag || '',
          activityTypeStr: item.activityTypeStr || '',
          isInstallment: item.isInstallment || false,
          settlementRule: item.settlementRule,
          tariffPrice: item.tariffPrice,
          tariffRule: item.tariffRule,
          properties: item.properties || [],
          createdTime: item.createdTime,
          updatedTime: item.updatedTime,
          deliverTime: item.deliverTime,
          startSaleNum: item.startSaleNum || 0,
          revive: item.revive || false, // 是否是待复活商品
          isSevenDayUnconditionalReturn: item.isSevenDayUnconditionalReturn || false, // 七天无理由退货
          yzGuarantee: item.yzGuarantee || false, // 有赞担保
          startSoldTime: item.startSoldTime || '', // 开售时间
          disableSelectMsg: item.disableSelectMsg || '',
          bizExtension: item.bizExtension
        };
      });

      return {
        ...goodsGroup,
        goodsList
      };
    });

    (unavailableItems || []).forEach((item) => {
      const imgUrl = item.attachmentUrl || 'https://b.yzcdn.cn/v2/image/wap/no_pic.png';
      const activityId = item.activityId || 0;
      unAvailableGoodsList.push({
        cartId: item.cartId,
        kdtId: item.kdtId,
        channelId: item.channelId,
        goodsId: item.goodsId,
        skuId: item.skuId,
        storeId: item.storeId,
        productSkuId: item.skuId,
        title: item.title || '',
        sku: getSkuStr(item.sku),
        num: item.num,
        errorMsg: item.errorMsg,
        imgUrl: cdnImage(imgUrl, '!300x300.jpg'),
        unique: getUnique(item),
        alias: item.alias,
        activityId,
        activityType: item.activityType,
        activityTag: item.activityTag === '加价购' ? '换购' : item.activityTag || ''
      });
    });

    return {
      kdtId,
      shopName,
      isNewHopeShop, // 是否是新希望店铺
      goodsGroupList: goodsGroupListFormated,
      activities,
      selectedPreferencePrice // 活动优惠金额
    };
  });

  return {
    shopList,
    unAvailableGoodsList,
    allGoodsIds
  };
}
