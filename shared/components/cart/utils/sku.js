export function getSkuStr(sku) {
  let parsedSku = [];
  try {
    parsedSku = JSON.parse(sku || '[]') || [];
  } catch (e) {
    console.warn(e);
  }
  return parsedSku
    .map(item => {
      return item.v;
    })
    .join('，');
}

export const formatSku = (sku) => {
  let parsedSku = [];
  try {
    parsedSku = JSON.parse(sku || '[]') || [];
  } catch (e) {
    parsedSku = [];
    console.warn(e);
  }

  return parsedSku;
};

/*
  normalize sku tree

  [
    {
      count: 2,
      k: "品种", // 规格名称 skuKeyName
      k_id: "1200", // skuKeyId
      k_s: "s1" // skuKeyStr
      v: [ // skuValues
        { // skuValue
          id: "1201", // skuValueId
          name: "萌" // 具体的规格值 skuValueName
        }, {
          id: "973",
          name: "帅"
        }
      ]
    },
    ...
  ]
                |
                v
  {
    s1: [{
      id: "1201",
      name: "萌"
    }, {
      id: "973",
      name: "帅"
    }],
    ...
  }
 */
export const normalizeSkuTree = skuTree => {
  const normalizedTree = {};
  skuTree.forEach(treeItem => {
    normalizedTree[treeItem.kS] = treeItem.v;
  });
  return normalizedTree;
};

// 获取已选择的sku名称
export const getSelectedSkuFromSkuData = (skuTree, selectedSku) => {
  const normalizedTree = normalizeSkuTree(skuTree);
  return Object.keys(selectedSku).filter(key => /^s\d$/.test(key)).reduce((selectedValues, skuKeyStr) => {
    const skuValues = normalizedTree[skuKeyStr];
    const skuValueId = selectedSku[skuKeyStr];

    if (+skuValueId) {
      const skuValue = skuValues.filter(value => +value.id === +skuValueId)[0];
      skuValue && selectedValues.push(skuValue);
    }
    return selectedValues;
  }, []);
};

// 将选中sku格式化为 [{ k_s: xx, v_id: xx }]
export const getSkuData = comb => {
  return Object.keys(comb).filter(key => /^s\d$/.test(key)).reduce((selectedValues, skuKeyStr) => {
    if (+comb[skuKeyStr]) {
      selectedValues.push({
        kS: skuKeyStr,
        vId: comb[skuKeyStr]
      });
    }
    return selectedValues;
  }, []);
};
