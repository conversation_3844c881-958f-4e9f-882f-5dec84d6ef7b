import audioEventBus from 'shared/components/showcase/captain-components/audio/audio-event';

// 根据组件顺序索引音频
const STATUS = {
  STOP: 0,
  PLAY: 1,
  PAUSE: 2,
};

function pad(number) {
  return number < 10 ? `0${number}` : `${number}`;
}

function getTime(seconds) {
  const minute = parseInt(seconds / 60, 10);
  const second = parseInt(seconds % 60, 10);
  return `${pad(minute)}:${pad(second)}`;
}

export default Behavior({
  audio__indexList: [],
  audio__durationMap: {},
  audio__currentTimeMap: {},
  audio__backgroundAudio: '',
  audio__currentId: '',
  isAudioEnded: false,

  attached() {
    this.audio__durationMap = {};
    this.audio__currentTimeMap = {};
    this.audio__indexList = [];
  },

  detached() {
    this.audio__clearAudios();
  },

  methods: {
    audio__srcChange(e) {
      const { index, src } = e.detail;
      this.setData({
        [`curComponents[${index}].src`]: src,
      });
    },

    audio__clearAudios() {
      if (this.audio__backgroundAudio) {
        this.audio__backgroundAudio.stop();
        this.audio__backgroundAudio = undefined;
      }
      this.audio__indexList = [];
      this.audio__currentId = '';
      this.audio__currentTimeMap = {};
      this.audio__durationMap = {};
    },

    audio__updateProgress(e) {
      const { index, percentage } = e.detail;
      if (index === this.audio__currentId) {
        const audio = this.audio__backgroundAudio;
        if (!audio) return;
        audio.seek(parseInt((audio.duration * percentage) / 100, 10));
      } else {
        // 拖动其他未播放的 audio
      }
    },

    audio__resetProgress(index) {
      this.setData({
        [`curComponents[${index}].status`]: STATUS.STOP,
      });
      audioEventBus.trigger('reset', index);
    },

    audio__initBackgroundAudio() {
      this.audio__backgroundAudio = wx.getBackgroundAudioManager();
      const currentAudio = this.audio__backgroundAudio;
      // 播放
      currentAudio.onPlay(() => {
        if (this.audio__currentId === '') return;
        this.setData({
          [`curComponents[${this.audio__currentId}].status`]: STATUS.PLAY,
        });
        this.isAudioEnded = false;
      });
      // 暂停
      currentAudio.onPause(() => {
        if (this.audio__currentId === '') return;
        this.setData({
          [`curComponents[${this.audio__currentId}].status`]: STATUS.PAUSE,
        });
      });
      // 停止
      currentAudio.onStop(() => {
        if (this.audio__currentId === '') return;
        this.setData({
          [`curComponents[${this.audio__currentId}].status`]: STATUS.STOP,
        });
      });
      // 播放结束
      currentAudio.onEnded(() => {
        if (this.audio__currentId === '') return;
        this.audio__resetProgress(this.audio__currentId);
        this.isAudioEnded = true;
        if (+currentAudio.loop) {
          this.audio__replay(currentAudio);
        }
        audioEventBus.trigger(`stop-${this.audio__currentId}`);
      });
      // 进度更新
      currentAudio.onTimeUpdate(() => {
        if (this.audio__currentId === '') return;
        if (
          this.audio__currentTimeMap &&
          this.audio__currentTimeMap[this.audio__currentId]
        ) {
          currentAudio.seek(this.audio__currentTimeMap[this.audio__currentId]);
          this.audio__currentTimeMap[this.audio__currentId] = 0;
          return;
        }
        const currentTime = currentAudio.currentTime;
        const duration = currentAudio.duration;

        const data = {};
        // 只更新一次duration和canPlay状态
        if (
          this.audio__durationMap &&
          !this.audio__durationMap[this.audio__currentId]
        ) {
          this.audio__durationMap[this.audio__currentId] = duration;
          data[`curComponents[${this.audio__currentId}].duration`] = duration;
          data[
            `curComponents[${this.audio__currentId}].formatedDuration`
          ] = getTime(duration);
          data[`curComponents[${this.audio__currentId}].canPlay`] = true;
          this.setData(data);
        }

        audioEventBus.trigger('timeupdate', {
          currentTime,
          audioIndex: this.audio__currentId,
        });
      });
      // 可播放状态
      currentAudio.onCanplay(() => {
        if (this.audio__currentId === '') return;
        this.setData({
          [`curComponents[${this.audio__currentId}].canPlay`]: true,
          [`curComponents[${this.audio__currentId}].isLoading`]: false,
        });
      });

      currentAudio.onPrev(() => {
        const currentId = this.audio__currentId;
        this.audio__triggerPrev();
        this.audio__resetProgress(currentId);
      });

      currentAudio.onNext(() => {
        const currentId = this.audio__currentId;
        this.audio__triggerNext();
        this.audio__resetProgress(currentId);
      });

      // 缓冲
      currentAudio.onWaiting(() => {
        if (this.audio__currentId === '') return;
        this.setData({
          [`curComponents[${this.audio__currentId}].isLoading`]: true,
        });
      });

      return currentAudio;
    },

    audio__setBackgroundAudioInfo(index) {
      if (!this.audio__backgroundAudio) return;
      const componentData = this.data.curComponents[index] || {};
      const currentAudio = this.audio__backgroundAudio;
      // title 为空字符串会报错
      currentAudio.title = componentData.title || ' ';
      currentAudio.coverImgUrl = componentData.logo;
      currentAudio.loop = componentData.loop;
      currentAudio.epname = ' ';
      currentAudio.singer = ' ';
      currentAudio.src = componentData.src;
    },

    audio__triggerNext() {
      const length = this.audio__indexList.length;
      if (length <= 1) return;
      const currentIndex = this.audio__indexList.indexOf(this.audio__currentId);
      this.audio__triggerWithIndex((currentIndex + 1) % length);
    },

    audio__triggerPrev() {
      const length = this.audio__indexList.length;
      if (length <= 1) return;
      const currentIndex = this.audio__indexList.indexOf(this.audio__currentId);
      this.audio__triggerWithIndex((currentIndex + length - 1) % length);
    },

    audio__triggerWithIndex(indexOfList) {
      const index = this.audio__indexList[indexOfList];
      this.audio__currentTimeMap[index] = 0;
      this.audio__currentId = index;
      this.audio__setBackgroundAudioInfo(index);
    },

    audio__replay(currentAudio) {
      const index = this.audio__currentId;
      const componentData = this.data.curComponents[index] || {};
      // title 为空字符串会报错
      currentAudio.title = componentData.title || ' ';
      currentAudio.src = componentData.src;
    },

    audio__trigger(e) {
      const { src, index, reload } = e.detail;
      if (!src) return;
      const componentData = this.data.curComponents[index] || {};
      const currentStatus = componentData.status;
      let isChangeSrc = false;
      let currentAudio = this.audio__backgroundAudio;
      if (!currentAudio || this.audio__backgroundChanged) {
        isChangeSrc = true;
        // 重置页面切换导致的音频重新初始化
        this.audio__backgroundChanged = false;
        this.audio__currentId = index;
        currentAudio = this.audio__initBackgroundAudio();
      }

      // 切换播放音频时保存当前音频播放进度
      if (index !== this.audio__currentId) {
        if (this.audio__currentId !== '') {
          if (this.audio__currentTimeMap) {
            this.audio__currentTimeMap[
              this.audio__currentId
            ] = this.audio__backgroundAudio.currentTime;
          }
          this.audio__backgroundAudio.pause();
          audioEventBus.trigger(`pause-${this.audio__currentId}`, {
            currentTime: currentAudio.currentTime,
          });

          this.setData({
            [`curComponents[${this.audio__currentId}].status`]: STATUS.PAUSE,
          });
        }
        isChangeSrc = true;
      }

      // 播放控制
      if (currentStatus !== STATUS.PLAY) {
        // 切换 src 直接播放
        if (isChangeSrc) {
          this.audio__setBackgroundAudioInfo(index);
          this.audio__currentId = index;
        } else {
          // 重新设置，不然会使得src为''
          const componentData = this.data.curComponents[index] || {};
          if (this.isAudioEnded || currentAudio.currentTime < 1) {
            // title 为空字符串会报错
            currentAudio.title = componentData.title || ' ';
            currentAudio.src = src;
          }
          currentAudio.play();
        }
      } else if (reload) {
        currentAudio.stop();
        audioEventBus.trigger(`pause-${this.audio__currentId}`, {
          currentTime: currentAudio.currentTime,
        });

        // 重置时间和播放进度
        this.audio__resetProgress(index);
        this.audio__backgroundAudio = '';
      } else {
        currentAudio.pause();
        audioEventBus.trigger(`pause-${this.audio__currentId}`, {
          currentTime: currentAudio.currentTime,
        });
      }
    },
  },
});
