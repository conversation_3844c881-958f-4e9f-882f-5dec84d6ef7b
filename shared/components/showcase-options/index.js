import Event from '@youzan/weapp-utils/lib/event';
import StickyControl from 'shared/utils/sticky-control';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import get from '@youzan/weapp-utils/lib/get';
import spm from 'shared/utils/spm';
import navigate from 'shared/utils/navigate';
import {
  initPageCountdown,
  clearPageCountdown,
} from 'shared/utils/countdown-list';
import getApp from 'shared/utils/get-safe-app';
import jumpToLink from 'shared/components/showcase/utils/jumpToLink';
import jumpLink from 'shared/components/showcase/utils/jumpLink';
import getComponentType from 'shared/components/showcase/utils/getComponentType';
import { getLoggerParams, loggerRandomKey } from 'shared/utils/log-params';
import LifetimeBehavior from 'shared/components/showcase/behaviors/page-lifetimes-behavior';
import FeatureLoadControl from './feature-load';
import AudioBehavior from './behaviors/audio';

const app = getApp();
const { windowHeight } = app.getSystemInfoSync();
const COMPONENTS_PER_PAGE = 8;
const PRELOAD_COMPONENT_OFFSET = windowHeight;
const COUNTDOWN_KEY = 'featureCountdown';
const AUTO_LOGGER_NOT_INCLUDE = ['hot_words_reference'];

const LOAD_PAGE_RANDOM_KEY = loggerRandomKey;

export default {
  behaviors: [LifetimeBehavior, AudioBehavior],
  externalClasses: ['showcase-contact-us'],
  properties: {
    components: {
      type: Array,
      value: [],
      observer(components) {
        if (this.data.notForbid) {
          this.initSyncFeatureComponents(components);
        }
      },
    },

    // 防止两个showcase-container带来的冲突
    notForbid: {
      type: Boolean,
      value: true,
    },
    customStyle: {
      type: String,
      value: '',
    },

    hqKdtId: Number,

    extra: {
      type: Object,
      value: {},
    },

    hasSearchSticky: Boolean,
    alias: String,
    componentsLength: Number,

    uniqueKey: {
      type: String,
      value: '',
      observer(uniqueKey, preKey) {
        // 如果 uniqueKey 存在的话 则初始化
        if (uniqueKey && this.data.notForbid) {
          this.initUniqueKey(uniqueKey, preKey);
        }
      },
    },
  },

  data: {
    curComponents: [],
    featureComponentsAllLoaded: false,
    triggerFeatureLoaded: false,
    loading: true,
    startIndex: 0,
    pageRandomNumber: LOAD_PAGE_RANDOM_KEY,
    pageCommonData: {
      countdownKey: COUNTDOWN_KEY,
      pageRandomString: makeRandomString(8),
    },
    showAuthDialog: false,
    deliverySetting: {
      mode: 1,
      isLocal: false,
      isSelf: false,
    },
  },

  pageLifetimes: {
    show() {
      if (this.data.notForbid) {
        this.isComponentHide = false;
        this.setFeatureLoggerParams();
        StickyControl.releaseStickyControl();
        if (this.stickyControlBack) {
          StickyControl.setStickyControl(this.stickyControlBack);
        }
        this.initCountDownKey();
        initPageCountdown(this, `pageCommonData.${COUNTDOWN_KEY}`);
      }
    },

    hide() {
      this.isComponentHide = true;
      this.stickyControlBack = StickyControl.getStickyControl();
      StickyControl.releaseStickyControl();
      clearPageCountdown();
    },
  },

  ready() {
    // StickyControl.releaseStickyControl();
    // if (this.stickyControlBack) {
    //   StickyControl.setStickyControl(this.stickyControlBack);
    // }
    if (this.data.notForbid) {
      this.initCountDownKey();
      initPageCountdown(this, `pageCommonData.${COUNTDOWN_KEY}`);
    }
  },

  detached() {
    this.clearFeatureComponents();
    StickyControl.releaseStickyControl();
    clearPageCountdown();
  },

  methods: {
    initUniqueKey(key, preKey) {
      // 初始化分页状态
      this.setYZData(
        {
          curComponents: [],
          featureComponentsAllLoaded: false,
          loading: true,
          startIndex: 0,
        },
        {
          immediate: true,
          cb: () => {
            // 如果之前存在 则清空
            if (preKey) this.clearFeatureComponents(preKey);
            // 初始化 event 事件
            this.initFeatureComponents(key);
          },
        }
      );
    },

    /**
     * 清除 uniqueKey Event
     * @param key
     */
    clearFeatureComponents(key) {
      let { uniqueKey = '' } = this.data;
      // key 如果外部有设置key 则用外部
      uniqueKey = key || uniqueKey;

      FeatureLoadControl.clearShowcaseComponents(uniqueKey);
    },

    /**
     * 不通过懒加载分页
     * @param components
     */
    initSyncFeatureComponents(components) {
      // 存在 uniqueKey 不往下执行
      if (this.data.uniqueKey) return;
      if (!Array.isArray(components)) return;
      this.setYZData({
        curComponents: this._formatComponents(components),
        loading: false,
      });
    },
    /**
     * 根据 uniqueKey 分页组件
     * @param key
     */
    initFeatureComponents(key) {
      let { uniqueKey = '' } = this.data;
      // key 如果外部有设置key 则用外部
      uniqueKey = key || uniqueKey;

      // 初始化 分页 Event 相关
      Event.on(`feature-load:init${uniqueKey}`, () => {
        // 进入分页
        this._loadMoreFeatureThemeComponents();
      });
      // 绑定显示组件
      Event.on(
        `feature-load:show-component${uniqueKey}`,
        ({ components, startIndex }) => {
          this._showFeatureThemeComponents(components, startIndex)
            .then(() => {
              setTimeout(() => {
                this._checkIsFeatureBottomVisible()
                  .then(this._loadMoreFeatureThemeComponents.bind(this))
                  .catch(() => {});
              }, 500);
            })
            .catch((err) => {
              app.logger.appError({
                name: 'feature_components_parse_error',
                message: '微页面组件解析报错',
                // eslint-disable-next-line prefer-object-spread
                detail: Object.assign(
                  {},
                  err,
                  __wxConfig && __wxConfig.appLaunchInfo
                ),
              });
            });
        }
      );
      // 开始
      Event.trigger(`feature-load:start${uniqueKey}`);
    },

    setFeatureLoggerParams() {
      if (!this.data.extra) return;
      const { featureId, templateId, isHomePage } = this.data.extra;
      if (featureId) {
        Event.trigger('feature:show', {
          pageId: featureId,
          logParams: {
            template_id: templateId,
            is_home: Number(isHomePage),
          },
        });
      }
    },
    initCountDownKey() {
      this.setYZData({
        'pageCommonData.pageRandomString': makeRandomString(8),
      });
    },

    onPullDownRefresh() {
      if (this.isComponentHide) return;
      this.audio__clearAudios();
      StickyControl.releaseStickyControlMap();

      // 这里先清除倒计时，再触发下拉刷新事件
      clearPageCountdown();
    },

    onReachBottom() {
      if (this.isComponentHide) return;
      const allLoaded = get(this.data, 'featureComponentsAllLoaded', null);
      if (allLoaded || allLoaded === null) return;
      this._loadMoreFeatureThemeComponents();
    },

    /**
     * 唤起sku
     */
    handleGoodsBuy(e) {
      this.triggerEvent('buy', e.detail);
    },

    /**
     * 判断是否滚到了底部
     */
    _checkIsFeatureBottomVisible() {
      return new Promise((resolve, reject) => {
        this.createSelectorQuery()
          .select('#theme-feature__content-end-hook')
          .boundingClientRect((rect) => {
            if (!rect) {
              return reject();
            }
            // 触发首屏打点
            Event.trigger('feature:first_screen_cover');
            const top = rect.top || 0;
            // 内容需要大于屏幕高度与加载阈值之和才能在滑动屏幕时触发加载更多
            if (top > windowHeight + PRELOAD_COMPONENT_OFFSET + 100) {
              return reject();
            }

            resolve();
          })
          .exec();
      });
    },

    _loadMoreFeatureThemeComponents() {
      const {
        startIndex = 0,
        uniqueKey = '',
        componentsLength = 0,
      } = this.data;

      if (startIndex + COMPONENTS_PER_PAGE >= componentsLength) {
        this.setYZData({
          featureComponentsAllLoaded: true,
          loading: this.data.loading ? false : this.data.loading,
        });

        this._checkTriggerFeatureLoaded();

        if (startIndex >= componentsLength) return Promise.resolve();
      }

      // 触发 加载更多
      Event.trigger(`feature-load:more${uniqueKey}`, {
        startIndex,
        size: COMPONENTS_PER_PAGE,
      });
    },

    _checkTriggerFeatureLoaded() {
      const { triggerFeatureLoaded = false, uniqueKey = '' } = this.data;

      if (triggerFeatureLoaded) return;

      this.setData(
        {
          triggerFeatureLoaded: true,
        },
        () => {
          this.triggerEvent('feature-loaded');
          Event.trigger(`feature-load:loaded${uniqueKey}`);
        }
      );
    },

    _formatComponents(components) {
      return components.map((itemData) => {
        const type = getComponentType(itemData.type);

        itemData.itemType = type || itemData.type;

        return itemData;
      });
    },

    _showFeatureThemeComponents(components, prefixIndex = 0) {
      return new Promise((resolve) => {
        const componentsData = this._formatComponents(components);

        const newComponentsData = {};
        // 直接上报的组件
        const loggerComponents = [];
        // 获取完spm上报
        const storageLoggerComponents = [];
        const spmType = spm.getPageSpmTypeId();

        for (let i = 0; i < componentsData.length; i++) {
          const index = i + prefixIndex;
          if (!this.data.curComponents[index]) {
            const { componentIndex = index } = componentsData[i];
            newComponentsData[`curComponents[${index}]`] = componentsData[i];
            const loggerItem = getLoggerParams(
              componentsData[i],
              componentIndex,
              LOAD_PAGE_RANDOM_KEY,
              spmType
            );
            if (spmType) {
              const { type } = componentsData[i];
              !AUTO_LOGGER_NOT_INCLUDE.includes(type) &&
                loggerComponents.push(loggerItem);
            } else {
              storageLoggerComponents.push(loggerItem);
            }
          }
        }
        // 微页面组件曝光
        this._setLoggerComponents(loggerComponents, storageLoggerComponents);

        this.setYZData(
          {
            ...newComponentsData,
            startIndex: prefixIndex + COMPONENTS_PER_PAGE,
          },
          {
            cb: () => {
              resolve();
            },
          }
        );
      });
    },

    /**
     * 设置微页面组件数据上报逻辑
     * @param loggerComponents
     * @param storageLoggerComponents
     * @private
     */
    _setLoggerComponents(loggerComponents, storageLoggerComponents) {
      if (storageLoggerComponents.length) {
        Event.once('SPM:success', () => {
          const spmType = spm.getPageSpmTypeId();
          storageLoggerComponents.map((item) => {
            item.banner_id = spmType + item.banner_id;
            return item;
          });
          this.__ensureComponentsLogger(storageLoggerComponents);
        });
      }
      // 批量曝光埋点上报
      if (!loggerComponents.length) return;

      this.__ensureComponentsLogger(loggerComponents);
    },

    /**
     * 微页面组件曝光上报
     * @param loggerComponents
     * @private
     */
    __ensureComponentsLogger(loggerComponents) {
      const loggerMsg = {
        et: 'view',
        ei: 'component_view',
        en: '组件曝光',
        params: {
          view_objs: loggerComponents,
        },
      };
      app.logger.log(loggerMsg);
    },

    // 联系客服的回调
    onContactBack: navigate.contactBack,

    jumpToLink({ detail: { type = '', item = {}, extra = {} } }) {
      this.logTab(type, extra);
      jumpToLink(type, item, extra);
    },

    /**
     * 埋点
     */
    logger({ detail: params }) {
      app.logger &&
        app.logger.log({
          ...params,
          si: this.data.extra.kdtId,
        });
    },

    onNavigate({ detail }) {
      const { linkType, extra = {} } = detail;
      this.logTab(linkType, extra);
      jumpLink(detail, true);
    },

    // 跳转tab的无法携带 banner_id 手动上报
    logTab(type, extra) {
      const tabList = ['shopnote', 'usercenter', 'cart', 'homepage'];
      if (tabList.some((tabItem) => tabItem === type)) {
        const loggerMsg = {
          et: 'click',
          ei: 'click_content',
          en: '组件点击',
          params: { ...extra },
        };
        app.logger.log(loggerMsg);
      }
    },
    handleShowAuthDialog() {
      this.setData({ showAuthDialog: true });
    },

    handleHideAuthDialog() {
      this.setData({ showAuthDialog: false });
    },
  },
};
