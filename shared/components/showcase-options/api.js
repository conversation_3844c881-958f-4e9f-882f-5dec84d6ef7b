const app = getApp();

const checkOnlineSupplyMode = (kdtId) => {
  return app
    .request({
      path: '/wscshop/shop/config.json',
      method: 'GET',
      data: {
        kdtId,
        key: 'online_subshop_supply_mode',
      },
    })
    .then(({ online_subshop_supply_mode: mode = 1 }) => +mode)
    .catch(() => 1);
};

const getDeliverySetting = () => {
  return app
    .request({
      path: '/retail/h5/miniprogram/store/getDeliverySetting',
      method: 'GET',
    })
    .catch(() => {
      return {
        isLocal: false,
        isSelf: false,
      };
    });
};

export { checkOnlineSupplyMode, getDeliverySetting };
