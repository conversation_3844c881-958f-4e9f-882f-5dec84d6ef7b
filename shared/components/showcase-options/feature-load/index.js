import Event from '@youzan/weapp-utils/lib/event';
import uniqueSearchInter from '../feature-interceptor/unique-search';
import setRightOrderIdForComponent from '../feature-interceptor/order-component';

const FeatureLoadControl = {
  // 全部微页面组件会挂在这
  __featurePages: {},

  /**
   * 设置 微页面组件 && 分页逻辑
   * @param components
   * @param isTopNav
   * @param uniqueSearch
   * @param uniqueKey
   */
  setShowcaseComponents(components, isTopNav, uniqueSearch, uniqueKey) {
    // feature-components 拦截器
    components = uniqueSearchInter(components, uniqueSearch);
    // 设置正常的banner_id
    components = setRightOrderIdForComponent(components);
    // 赋值到 this.__featurePages
    this.__featurePages[uniqueKey] = components;
    // container 初始化完成
    Event.on(`feature-load:start${uniqueKey}`, () => {
      // 开始塞入 组件列表 是否需要分页可以在这里判断
      Event.trigger(`feature-load:init${uniqueKey}`, []);
    });

    // 加载更多
    Event.on(`feature-load:more${uniqueKey}`, ({ startIndex, size }) => {
      const components = this.__featurePages[uniqueKey].slice(
        startIndex,
        startIndex + size
      );
      // 触发 showcase-container 加载更多
      Event.trigger(`feature-load:show-component${uniqueKey}`, {
        components,
        startIndex,
      });
    });
  },

  /**
   * 清除 event
   * @param uniqueKey
   */
  clearShowcaseComponents(uniqueKey) {
    // 移除初始化
    Event.off(`feature-load:init${uniqueKey}`);

    // 移除显示组件
    Event.off(`feature-load:show-component${uniqueKey}`);

    // 清除 container 初始化完成
    Event.off(`feature-load:start${uniqueKey}`);

    // 清除 container 初始化完成
    Event.off(`feature-load:more${uniqueKey}`);

    // 移除 this.__featurePages
    delete this.__featurePages[uniqueKey];
  },
};
export default FeatureLoadControl;
