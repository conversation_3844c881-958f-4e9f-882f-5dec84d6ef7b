/**
 * 埋点事件的banner_id：详见https://doc.qima-inc.com/pages/viewpage.action?pageId=39947773
 * banner_id中item所在组件域：包含组件类型（component_type）和组件序号（component_id）。比如如果一个页面同时用到2个商品魔方组件，第一个魔方的组件序号是1，第二个魔方的组件序号是2。
 */
const getSameTypeMap = (components) => {
  return components.reduce((obj, component, index) => {
    const { type } = component;

    try {
      const id = Object.keys(obj[type] || {}).length;
      obj[type] = {
        ...(obj[type] || {}),
        [index]: id + 1,
      };

      return obj;
    } catch (e) {
      console.log('获取组件埋点事件的banner_id组件序号失败', { e });
      return obj;
    }
  }, {});
};

export default (components) => {
  const sameComponentTypeMap = getSameTypeMap(components);

  return components.map((component, index) => {
    const { type } = component;
    const componentMap = sameComponentTypeMap[type] || {};
    const componentIndex = componentMap[index];
    const extra = componentIndex ? { componentIndex: componentIndex - 1 } : {};

    return {
      ...component,
      ...extra,
    };
  });
};
