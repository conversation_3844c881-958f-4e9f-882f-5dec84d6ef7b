# shared 目录使用规范

shared 目录适用于放插件和小程序公用代码。

## 目录规范
```
.
├── README.md
├── common # 只能用于 shared 目录的通用文件
│   ├── components # 通用组件
│   └── css # 通用样式
├── packages # 模块通用组件，模块以业务返回划分，这些文件在构建时自动输出到与模块中
│   └── goods
└── utils # 业务通用 js 函数或API，工具函数请使用 weapp-utils
    ├── authorize.js
    ├── browser
    ├── get-safe-app.js
    ├── navigate.js
    ├── qrcode.js
    ├── request
    └── shop
```

**注：** 按照 mini-loader 逻辑，根据传入到插件的 sources 参数已经 entry 自动计算输出目录。实际就是使用文件完整路径然后与 sources 列表进行对比，删除匹配到的路径剩余的路径既是最后输出的路径。如：
```
sources: ['user/path/wsc-wxapp/shared', 'user/path/wsc-wxapp/src']
fileA: ['user/path/wsc-wxapp/src/packages/goods/components/goods-buy-tip/index.wxml']
fileB: ['user/path/wsc-wxapp/shared/packages/goods/components/buy-info/index.wxml']
outputpath: ['user/path/wsc-wxapp/dist']
```
这样的配置最后输出的路径将是：
```
outputA: user/path/wsc-wxapp/dist/packages/goods/components/goods-buy-tip/index.wxml
outputB: user/path/wsc-wxapp/dist/packages/goods/components/buy-info/index.wxml
```

所以必须注意文件的命名，同一模块文件名绝对不能相同。

## 编码规范

### 不允许直接使用 getApp
因为 shared 目录的代码存在于插件共用，插件不支持 getApp 方法，所以禁止使用 getApp。可以使用下面代码代替

~~~ javascript
import getApp from 'shared/utils/get-safe-app'
~~~

## 路径使用规范
我们应该把 shared 理解为独立的仓库，不应该存在直接以目录的方式来引用外部文件。
- shared 中引用 src 目录下的内容，这是不允许的。
- shared 下业务模块对通用模块的引用，必须使用 `shared/module/path` 的方式，使用相对路径是不允许的。
- shared 下个业务模块应该配置各自的路径别名（build/webpack.config.shared.resolve.js），使用 `shared/module/path` 的方式是不合理的。
- shared 外引用 shared 只能通过 `shared/module/path` 引用