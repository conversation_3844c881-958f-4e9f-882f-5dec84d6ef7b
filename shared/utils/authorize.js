/**
 * @param {String} scope
 * @returns {Promise} authorize
 */
export default (right) => {
  return new Promise((resolve, reject) => {
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting[right]) {
          wx.authorize({
            scope: right,
            success: resolve,
            fail: reject
          });
        } else {
          resolve();
        }
      },
      fail: reject
    });
  });
};
