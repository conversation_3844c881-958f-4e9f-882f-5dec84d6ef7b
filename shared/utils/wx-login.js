import { judgePersonWeapp } from './person-weapp';

/**
 * @description 该方法负责找到授权公共组件并唤起该组件
 * @param {Object} options
 * @param {String} options.selector 授权公共组件的selector属性，默认 #account-wx-login
 * @param {String} options.context 授权公共组件放置的上下文，默认为当前Page
 */
const wxLogin = (options = {}) => {
  const currentPages = getCurrentPages();

  options = Object.assign(
    {
      selector: '#account-wx-login',
      context: currentPages.length ? currentPages[currentPages.length - 1] : null // 默认为当前Page
    },
    options
  );
  // 获取到对应的组件context和seletor
  let { context } = options;

  // 找不到context，报错
  if (!context || !context.selectComponent) {
    throw new Error('context is required');
  }

  const selectComponent = context.selectComponent(options.selector);
  if (!selectComponent) {
    throw new Error(`Can not find the selector ${options.selector}`);
  }

  options.selectComponent = selectComponent;

  // 寻找到微信授权公共组件，唤起该组件
  selectComponent.wxLogin && selectComponent.wxLogin(options);
};

/**
 * 判断是否显示微信快速登录按钮
 * 企业微信下 IOS 系统不显示微信快速登录按钮
 */
export const showWxLoginButton = () => {
  const app = getApp();
  const info = app.getSystemInfoSync();
  const isPersonWeapp = judgePersonWeapp();

  if (isPersonWeapp) {
    return false;
  }

  if (info.environment && info.environment === 'wxwork' && info.platform === 'ios') {
    return false;
  }

  return true;
};

export default wxLogin;
