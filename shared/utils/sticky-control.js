const StickyControl = {
  __stickyControlMap: {},

  __stickySubscribe: [],

  __stickyCheckList: [],

  /**
   * 计算已经使用的高度
   * @param key
   * @returns {number}
   */
  getHasStickyTop(key) {
    let hasStickyTop = 0;
    const { __stickyControlMap: stickyControlMap = {} } = this;
    for (const _key in stickyControlMap) {
      if (_key !== key) {
        hasStickyTop += +stickyControlMap[_key].height;
      }
    }
    return hasStickyTop;
  },
  /**
   * 设置吸顶权重
   * @param key
   * @param weight
   */
  setStickyControlCheckItem(key, weight = 0, rep) {
    const { __stickyCheckList: stickyCheckList = [] } = this;
    const index = stickyCheckList.findIndex(item => item.key === key);
    if (index < 0) {
      stickyCheckList.push({ key, weight });
      this.__stickyCheckList = stickyCheckList;
    }
    if (index > -1 && rep) {
      stickyCheckList[index] = { key, weight };
      this.__stickyCheckList = stickyCheckList;
    }
  },
  /**
   * getFilterStickyCheckList
   * @param stickyCheckList
   * @param key
   * @param isHigher
   * @returns {*}
   */
  getFilterStickyCheckList(stickyCheckList, key, isHigher) {
    const stickyItem = stickyCheckList.find(item => item.key === key) || { weight: 0 };

    const checkItems = isHigher
      ? stickyCheckList.filter(item => item.weight > stickyItem.weight)
      : stickyCheckList.filter(item => item.weight < stickyItem.weight);

    return checkItems;
  },
  /**
   * 判断是否可以吸顶
   * @param key
   * @returns {boolean|*}
   */
  checkStickyControlItem(key) {
    const { __stickyCheckList: stickyCheckList = [] } = this;

    const checkItems = this.getFilterStickyCheckList(stickyCheckList, key, true);

    const checkResult = checkItems.reduce((_checkResult, { key: _key }) => {
      const itemCheckResult = this.hasStickyControlItem(_key);
      return _checkResult && itemCheckResult;
    }, true);

    return checkResult;
  },

  /**
   * 清楚优先级弱的
   * @param key
   */
  clearCheckWeightStickyControlItem(key) {
    const {
      __stickyCheckList: stickyCheckList = [],
      __stickyControlMap: stickyControlMap = {}
    } = this;

    const checkItems = this.getFilterStickyCheckList(stickyCheckList, key, false);

    checkItems.forEach(item => {
      delete stickyControlMap[item.key];
    });
    this.__stickyControlMap = stickyControlMap;
  },
  /**
   * has sticky item
   * @param key
   * @returns {boolean}
   */
  hasStickyControlItem(key) {
    const { __stickyControlMap: stickyControlMap } = this;
    return Object.prototype.hasOwnProperty.call(stickyControlMap, key);
  },

  /**
   * set item height
   * @param key
   * @param height
   */
  setStickyControlItemHeight(key, height) {
    this.__stickyControlMap[key].height = height;
  },

  /**
   * set sticky item
   * @param key
   * @param value
   * @param isSticky
   */
  setStickyControlItem(key, value, isSticky) {
    this.clearCheckWeightStickyControlItem(key);
    const { __stickyControlMap: stickyControlMap = {} } = this;
    if (isSticky) {
      const opts = {
        height: value,
        positionTop: this.getHasStickyTop(key),
        nextTop: this.getHasStickyTop(key) + value
      };
      if (!this.hasStickyControlItem(key)) {
        stickyControlMap[key] = opts;
        this.setStickyControlItemSubscribe();
      }
    } else if (this.hasStickyControlItem(key)) {
      delete stickyControlMap[key];
      this.setStickyControlItemSubscribe();
    }
    this.__stickyControlMap = stickyControlMap;
  },

  /**
   * setStickyControlItemSubscribe
   */
  setStickyControlItemSubscribe() {
    const { __stickySubscribe: stickySubscribe } = this;
    stickySubscribe.forEach(item => {
      item.fn.call(item.ctx, { type: 'setItem' });
    });
  },

  /**
   * get sticky item
   * @param key
   * @returns {*}
   */
  getStickyControlItem(key) {
    return this.__stickyControlMap[key];
  },

  /**
   * get next sticky top
   * @param key
   * @returns {number}
   */
  getStickyControlNextTop(key) {
    const {
      __stickyCheckList: stickyCheckList = [],
      __stickyControlMap: stickyControlMap = {}
    } = this;

    const checkItems = this.getFilterStickyCheckList(stickyCheckList, key, true);

    const nextHeight = checkItems.reduce((_nextHeight, { key: _key }) => {
      return _nextHeight + (stickyControlMap[_key] || { height: 0 }).height;
    }, 0);

    return nextHeight;
  },

  /**
   * set sticky subscribe
   * @param fn
   * @param ctx
   * @param type
   */
  setStickyControlSubscribe(fn, ctx, type) {
    const { __stickySubscribe: stickySubscribe = [] } = this;
    if (stickySubscribe.findIndex((item) => item.type === type) < 0) {
      stickySubscribe.push({
        type,
        ctx,
        fn
      });
      this.__stickySubscribe = stickySubscribe;
    }
  },

  /**
   * delStickyControlSubscribe
   * @param type
   */
  delStickyControlSubscribe(type) {
    const { __stickySubscribe: stickySubscribe = [] } = this;
    const itemIndex = stickySubscribe.findIndex((item) => item.type === type);
    if (itemIndex > -1) {
      stickySubscribe.splice(itemIndex, 1);
      this.__stickySubscribe = stickySubscribe;
    }
  },
  /**
   * public sticky subscribe
   * @param type
   * @param isSticky
   * @param scrollTop
   */
  publicStickyControlSubscribe(type, isSticky, scrollTop) {
    const {
      __stickySubscribe: stickySubscribe = [],
      __stickyControlMap: stickyControlMap = {}
    } = this;

    const { positionTop: stickyPositionTop } = stickyControlMap[type];

    stickySubscribe.forEach(item => {
      const { type: itemType } = item;
      if (itemType != type && this.hasStickyControlItem(itemType)) {
        const { positionTop } = stickyControlMap[itemType];
        const stickyTop = positionTop + (isSticky ? scrollTop : 0);
        // 在当前 位置下面的吸顶
        if (positionTop >= stickyPositionTop) {
          item.fn.call(item.ctx, { type: 'stickyTop', stickyTop });
        }
      }
    });
  },

  /**
   * 获取 getStickyControl
   * @returns {{this.__stickyControlMap, this.__stickySubscribe: Array}}
   */
  getStickyControl() {
    return {
      __stickyControlMap: this.__stickyControlMap,
      __stickySubscribe: this.__stickySubscribe,
      __stickyCheckList: this.__stickyCheckList
    };
  },

  /**
   * 设置 setStickyControl
   * @param stickyControl
   */
  setStickyControl(stickyControl) {
    this.__stickyControlMap = stickyControl.__stickyControlMap;
    this.__stickySubscribe = stickyControl.__stickySubscribe;
    this.__stickyCheckList = stickyControl.__stickyCheckList;
  },

  /**
   * release all
   */
  releaseStickyControl() {
    this.__stickyControlMap = {};
    this.__stickySubscribe = [];
    this.__stickyCheckList = [];
  },
  /**
   * release __stickyControlMap
   */
  releaseStickyControlMap() {
    this.__stickyControlMap = {};
  }
};

export default StickyControl;
