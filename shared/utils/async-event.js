const triggerAsyncEvents = function triggerEvents(events, args) {
  const returnPromises = [];
  events.forEach(evt => {
    try {
      const returnPromise = evt.callback.apply(evt.ctx, args);
      returnPromises.push(returnPromise);
    } catch (e) {
      returnPromises.push(Promise.reject(e));
    }
  });
  return Promise.all(returnPromises);
};

const AsyncEvents = {
  onAsync: function on(name, callback, context) {
    this._events || (this._events = {});
    var events = this._events[name] || (this._events[name] = []);
    events.push({
      callback,
      context,
      ctx: context || {}
    });
    return this;
  },
  offAsync: function off(name, callback, context) {
    if (!name && !callback && !context) {
      this._events = {};
      return this;
    }

    const names = name ? [name] : Object.keys(this._events);
    for (let i = 0, l = names.length; i < l; i++) {
      name = names[i];
      const events = this._events[name];
      if (events) {
        this._events[name] = [];
        delete this._events[name];
      }
    }

    return this;
  },
  triggerAsync: function trigger(name, ...args) {
    if (!this._events) return Promise.resolve([]);
    const events = this._events[name];
    if (events) return triggerAsyncEvents(events, args);
    return Promise.resolve([]);
  }
};

export default AsyncEvents;
