import Args from '@youzan/weapp-utils/lib/args';

/**
 * 设置商品预加载数据
 * @param {*} params
 * {
    alias: '', // 必填
    price: 0,
    title: '',

    // 必填，因为商详主图需要计算宽高来展示
    image: {
      url: '',
      width: 0,
      height: 0
    },
  }
 */
export function setGoodsPreloadData(params = {}) {
  const { image = {}, alias, price, title } = params;
  const { url, width, height } = image;

  if (!url || !width || !height || !alias) {
    console.log('setGoodsPreloadData 参数错误');
    return;
  }

  wx.setStorage({
    key: `goodsDetail-${alias}`,
    data: {
      image,
      title,
      price
    }
  });
}

/**
  * 跳转商品，并支持预加载数据
  * @query {Object} 页面链接参数，必填
    {
      // 必填
      alias: '',
    }
  * @preload {Object} 商品预加载数据，选填
  * 如：
    {
      alias: String, // 商品alias
      price: String, // 单位元。直接用的后端返回的price，字符串格式的字段
      title: String, // 商品标题

      // 商详主图需要计算宽高来展示
      image: {
        url: String, // 图片原始链接
        width: Number, // 真实图片大小
        height: Number,
      },
    }

  * @routeType {String} 跳转类型，'navigateTo'(默认) | 'redirectTo'，选填
  * @return {Promise}
*/
export function jumpGoodsPage(query, preload = null, routeType = 'navigateTo') {
  return new Promise((resolve, reject) => {
    const { alias } = query;

    if (!alias) {
      return reject('invalid alias');
    }

    if (!wx[routeType]) {
      return reject('invalid routeType');
    }

    if (preload) {
      setGoodsPreloadData(preload);
    }

    const url = Args.add('/pages/goods/detail/index', query);
    wx[routeType]({
      url,
      success() {
        resolve(url);
      },
      fail(e) {
        reject(e);
      }
    });
  });
}
