// 全部商品列表请求参数
const getAllGoodsParams = (options) => {
  const app = getApp();
  const { extra = {} } = options;

  return {
    pageSize: 20,
    page: 1,
    order_by: 'algPVD30',
    json: 1,
    openIndependentPrice: app.getOfflineSeparatePrice(),
    activityPriceIndependent: 1,
    needGoodsRank: true,
    isRetailShop: +(app.globalData.isRetailApp || false),
    supportFixGroupOptionalCombo: true,
    ...extra,
  };
};

export default getAllGoodsParams;
