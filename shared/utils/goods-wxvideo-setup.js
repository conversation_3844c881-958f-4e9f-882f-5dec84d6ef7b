import { checkFromChannelsLive } from './channel';
import { getFromParams, parseTrackData } from '@youzan/salesman-params-handler';
import get from '@youzan/utils/object/get';
import request from '@youzan/tee-biz-request';

const app = getApp();

const SHARER_HOST = '/wscwxvideo/sharer';
const bindCustomer = (data) => {
  return request({
    method: 'POST',
    path: `${SHARER_HOST}/bind-customer.json`,
    data,
  });
};

// 处理小程序分享员接口调用
const handleSharerApiInvoke = () => {
  return new Promise((resolve) => {
    // 在微信视频号直播间场景下
    if (checkFromChannelsLive()) {
      // 需要基础库达到2.22.1
      if (!wx.getChannelsShareKey) {
        app.logger.appError({
          name: '[weapp]-wxvideo-bind-sharer',
          level: 'info',
          message: 'wx.getChannelsShareKey方法不存在',
        });
        return resolve(null);
      }
      return wx.getChannelsShareKey?.({
        success(res) {
          app.logger.appError({
            name: '[weapp]-wxvideo-bind-sharer',
            level: 'info',
            message: '调用wx.getChannelsShareKey成功',
            detail: res,
          });
          return resolve(res);
        },
        fail(error) {
          app.logger.appError({
            name: '[weapp]-wxvideo-bind-sharer',
            level: 'info',
            message: '调用wx.getChannelsShareKey失败',
            detail: error,
          });
          resolve(null);
        },
      });
    }
    return resolve(null);
  });
};

// 分享员绑定客户关系
const sharerBindCustomer = (channelsShareData) => {
  const { sharerOpenId } = channelsShareData || {};
  const { openId } = app?.getUserInfoSync() || {};

  if (sharerOpenId && openId) {
    return bindCustomer({
      sharerOpenId,
      userOpenId: openId,
    });
  }
  return Promise.resolve(null);
};

// 合并并写入from_parmas至context
const handleMergeFromParams = (sl) => {
  const kdtId = app.getKdtId();
  const loggerData = app.logger.getLogParams();
  const oldFromParams =
    (loggerData && get(loggerData, 'context.from_params')) || '';
  const fromParams = getFromParams({
    kdtId,
    sl,
    oldFromParamsData: oldFromParams
      ? parseTrackData(oldFromParams)
      : oldFromParams,
  });
  app.logger.setContext(
    {
      from_params: fromParams,
    },
    1
  );
};

export const bindWxvideoSharer = () => {
  // 执行微信视频号分享员绑客逻辑
  handleSharerApiInvoke().then((channelsShareData) => {
    if (channelsShareData) {
      sharerBindCustomer(channelsShareData).then((sl) => {
        sl && handleMergeFromParams(sl);
      });
    }
  });
};
