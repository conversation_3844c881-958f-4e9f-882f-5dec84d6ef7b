const p = 'packages';
const homeTab = 'home/tab/';
const homeTabOne = `${homeTab}one`;
const homeTabTwo = `${homeTab}two`;
const homeTabThree = `${homeTab}three`;
const dashboardIndex = '/dashboard/index';
const homeDashboardIndex = `home${dashboardIndex}`;
const usercenterDashboardIndex = `usercenter${dashboardIndex}`;

// 微页面 TAB ROUTE
export const FEATURE_TAB_ONE = {
  TRANS: `${p}/${homeTabOne}`,
  EXT: `${p}/ext-${homeTabOne}`,
  OLD: `${p}/old-${homeTabOne}`,
};

export const FEATURE_TAB_TWO = {
  TRANS: `${p}/${homeTabTwo}`,
  EXT: `${p}/ext-${homeTabTwo}`,
  OLD: `${p}/old-${homeTabTwo}`,
};

export const FEATURE_TAB_THREE = {
  TRANS: `${p}/${homeTabThree}`,
  EXT: `${p}/ext-${homeTabThree}`,
  OLD: `${p}/old-${homeTabThree}`,
};

export const FEATURE_HOME = {
  TRANS: `${p}/${homeDashboardIndex}`,
  EXT: `${p}/ext-${homeDashboardIndex}`,
  OLD: `${p}/old-${homeDashboardIndex}`,
};

export const USER_CENTER = {
  TRANS: `${p}/${usercenterDashboardIndex}`,
  PAGE: `pages/${usercenterDashboardIndex}`,
};
