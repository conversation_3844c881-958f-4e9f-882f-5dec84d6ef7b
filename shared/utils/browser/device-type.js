import { SystemInfo } from './system-info';

// 目前 iPhone 新版的就直接返回 iPhone-X
// getDeviceType 不应该暴露，内部逻辑有问题
function getDeviceType() {
  const { model } = SystemInfo;
  return model.replace(/\s/g, '-');
}

export function isNewIphone() {
  const deviceType = getDeviceType() || '';
  // iphone x iphone-x
  // iphone xs unknown<iphone11,> iphone-xs
  // iphone xr iphone-xr
  // iPhone 11 unknown<iphone12,1>
  // iPhone 12 unknown<iPhone13,2>
  // iPhone 14 unknown<iPhone15,2>
  // iPhone-14 iPhone-12 兼容模拟器
  // 全面屏 需特殊兼容处理

  // iPhone SE unknown<iPhone12,8> 不需要加高
  if (/iPhone SE/i.test(deviceType)) {
    return false;
  }
  return /iphone-x|iPhone11|iPhone13|iPhone12(?!,8>)|iPhone14|iPhone15|iPhone-14|iPhone-12|iPhone16|iPhone17/i.test(
    deviceType
  );
}

export default getDeviceType;
