const cache = {
  // 是否初始化，如果已初始化，则置为true
  inited: false,
  // 是否正在获取中，如果是则等待
  isFetching: false,
  systemInfo: {
    SDKVersion: '',
    batteryLevel: 100,
    benchmarkLevel: 1,
    brand: '',
    fontSizeSetting: 16,
    language: 'zh',
    model: '',
    pixelRatio: 1,
    platform: '',
    screenHeight: 0,
    screenWidth: 0,
    statusBarHeight: 0,
    system: '',
    version: '',
    windowHeight: 0,
    windowWidth: 0,
  },
};

const updateSystemInfoAsync = () => {
  if (cache.isFetching) {
    console.warn('[system-info] already fetching...');
    return;
  }
  cache.isFetching = true;
  wx.getSystemInfo({
    success(systemInfo = {}) {
      cache.systemInfo = systemInfo;
      cache.inited = true;
    },
    fail(error) {
      console.error('[system-info] error', error);
    },
    complete() {
      cache.isFetching = false;
    },
  });
};

const updateSystemInfoSync = () => {
  cache.systemInfo = wx.getSystemInfoSync();
  cache.inited = true;
  return cache.systemInfo;
};

// 初始化
updateSystemInfoAsync();

const SystemInfo = Object.create(null);

Object.keys(cache.systemInfo).forEach((key) => {
  Object.defineProperty(SystemInfo, key, {
    get() {
      if (!cache.inited) {
        const systemInfo = updateSystemInfoSync();
        return systemInfo[key];
      }
      return cache.systemInfo[key];
    },
  });
});

Object.defineProperty(SystemInfo, 'isAndroid', {
  get() {
    return /android/i.test(SystemInfo.system);
  }
});

Object.defineProperty(SystemInfo, 'isIphoneX', {
  get() {
    return /iphone x/i.test(SystemInfo.model);
  }
});

// 是应用类型，外部不可篡改
export {
  SystemInfo,
};

// 兼容老代码
export default function (withCache = true) {
  if (!cache.inited || !withCache) {
    cache.systemInfo = wx.getSystemInfoSync();
    cache.inited = true;
  }
  return cache.systemInfo;
}
