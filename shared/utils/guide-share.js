import { getCurrentPageOption } from '@/base-api/shop/chain-store.js';

/**
 * 获取导购参数。优先级： 1. query 2. context 3. globalData
 * @returns string
 */
export const getFromParams = () => {
  let fromParams = '';

  // 从 query 上获取
  const { query } = getCurrentPageOption();
  if (query && query.from_params) {
    fromParams = query.from_params;
  }

  // 从 logContext 中获取
  const app = getApp();
  if (!fromParams) {
    const loggerData = app.logger.getLogParams();
    fromParams = (loggerData && loggerData?.context?.from_params) || '';
  }

  // 从 globalData 中获取
  if (!fromParams) {
    fromParams = (app.globalData && app.globalData.from_params) || '';
  }

  return fromParams;
};
