import args from '@youzan/weapp-utils/lib/args';
import getApp from './get-safe-app';

const ALLOWED_METHOD_MAP = {
  navigate,
  redirect,
};

function navigate(options = {}) {
  const pages = getCurrentPages() || [];
  if (pages.length >= 10) {
    redirect(options);
    return;
  }

  wx.navigateTo(options);
}

function navigateWithBack(options) {
  const delta = navigateBackDelta(options.url);

  if (delta > -1) {
    return wx.navigateBack({ delta });
  }

  navigate(options);
}

function navigateBackDelta(url) {
  let delta = -1;

  const pages = getCurrentPages() || [];

  const targetPageIndex = pages.findIndex(({ route, options }) => {
    let path = args.add(route, options);
    if (path === url || `/${path}` === url) {
      return true;
    }

    return false;
  });

  if (targetPageIndex > -1) {
    delta = pages.length - targetPageIndex - 1;
  }

  return delta;
}

function redirect(options = {}) {
  wx.redirectTo(options);
}

function reLaunch(options = {}) {
  wx.reLaunch(options);
}

function switchTab(
  options = {},
  backupMethod = 'navigate',
  preferUse = 'navigate'
) {
  const app = getApp();
  const switchMethod = ALLOWED_METHOD_MAP[backupMethod] || navigate;

  app.isSwitchTab(options.url).then((isSwitchTab) => {
    if (isSwitchTab) {
      if (preferUse === 'switchTab') {
        return wx.switchTab(options);
      }
      return wx.reLaunch(options);
    }

    switchMethod(options);
  });
}

function contactBack({ detail }) {
  if (detail.path) {
    wx.navigateTo({
      url: args.add(detail.path, detail.query),
    });
  }
}

export default {
  switchTab,
  navigate,
  redirect,
  reLaunch,
  contactBack,
  navigateWithBack,
};
