import { node } from 'shared/utils/request';
import { getDomainByScene } from 'shared/utils/dmc';

function noop() {}

function upload(options) {
  const file = options.file;
  const success = options.success || noop;
  const fail = options.fail || noop;
  const progress = options.progress || noop;
  getUploadToken({
    success(uploadToken) {
      getDomainByScene('uploadUp').then((url) => {
        const uploadTask = wx.uploadFile({
          url,
          filePath: file,
          name: 'file',
          formData: {
            token: uploadToken,
            'x:skip_save': 1,
          },
          success: (res) => {
            try {
              res = JSON.parse(res.data);
            } catch (e) {
              fail({
                type: 'yz:uploadFile',
                code: -99999,
                msg: 'JSON解析错误',
              });
            }

            if (+res.code === 0) {
              success(res.data);
            } else {
              fail({
                type: 'yz:uploadFile',
                code: res.code,
                msg: res.msg
              });
            }
          },
          fail: (res) => {
            fail({
              type: 'wx:uploadFile',
              code: -99999,
              msg: res.errMsg
            });
          },
        });

        if (progress !== noop) {
          uploadTask.onProgressUpdate(progress);
        }
      });
    },
    fail(res) {
      fail(res);
    },
  });
}

function getUploadToken(options) {
  var success = options.success || noop;
  var fail = options.fail || noop;

  node({
    path: '/wscshop/token/upload-image.json',
    success: (res) => {
      success(res.token);
    },
    fail: (res) => {
      fail(res);
    }
  });
}

export default upload;
