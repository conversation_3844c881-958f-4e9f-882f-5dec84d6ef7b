import { mapGetters, mapState } from '@youzan/vanx';

export function wrapH5Vuex(component = {}) {
  let mapData = component.mapData || {};

  if (component.getters) {
    mapData = {
      ...mapData,
      ...mapGetters(component.getters)
    };
  }

  if (component.state) {
    mapData = {
      ...mapData,
      ...mapState(component.state)
    };
  }

  component.mapData = mapData;

  return component;
}
