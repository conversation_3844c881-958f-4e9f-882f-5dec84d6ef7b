import getSystemInfo from 'shared/utils/browser/system-info';
import { checkIsChannels, checkFromShowcase } from 'shared/utils/channel-mini';

const app = getApp();

let cache = {};

wx &&
  wx.onAppHide?.(() => {
    // 每次启动清除缓存
    cache = {};
  });

// 获取场景值
export function getScene() {
  if (cache.scene) {
    return cache.scene;
  }

  const { scene } =
    wx && wx.getEnterOptionsSync
      ? wx.getEnterOptionsSync() || {}
      : wx.getLaunchOptionsSync() || {};

  cache.scene = scene;

  return cache.scene;
}

// 是否为半屏模式【外部依赖这个方法的业务方，会用这个方法来判断是否是视频号，来隐藏业务展示，Windows|Mac默认不作为视频号场景】
export function checkIsHalfScreen() {
  const { statusBarHeight, platform } = getSystemInfo();
  return statusBarHeight === 0 && /(ios|android|devtools)/i.test(platform);
}

// 是否为视频号场景【视频号业务场景已下线】
export { checkIsChannels, checkFromShowcase };

// 是否为视频号场景 异步获取 方便后续使用apollo统一收敛
export function checkIsChannelsAsync() {
  const scene = getScene();
  const channelScene = [1177, 1176, 1175, 1195, 1208];
  return Promise.resolve(channelScene.indexOf(scene) > -1);
}
/**
 * @description 是否通过直播间商品转发打开
 * @returns boolean
 */
export function checkIsOpenByLiveGoodsShare() {
  const scene = getScene();
  return scene === 1208;
}
/**
 * @description 是否从视频号直播间打开
 * @returns boolean
 */
export function checkFromChannelsLive() {
  const scene = getScene();
  return scene === 1177;
}

/**
 * @description 是否在直播购物袋中打开
 * @returns boolean
 */
export function checkIsOpenByLiveBag() {
  const scene = getScene();
  return scene === 1177 || scene === 1176;
}

// 是否视频号直播场景【视频号业务场景已下线】
export function checkIsWxvideoLive(query) {
  const scene = getScene();
  const channelScene = [1177, 1176];
  const { from_source: fromSource } = query;

  return channelScene.includes(scene) && fromSource === 'wechatChannel';
}

export const isChannels = checkIsChannels();

// 是否有状态栏高度
export function checkHasStatusBar() {
  const { statusBarHeight } = getSystemInfo();
  return statusBarHeight > 0;
}

/**
 * @description 是否处于需要同步订单到微信场景的商详页
 * @returns Promise<boolean>
 */
export function checkIsNeedSyncOrderScene(query) {
  return new Promise((resolve) => {
    // 处于微信视频号开通场景的商详页
    const isWxvideoOpenFlowGoodsDetail = query?.TRADE_MODULE_ORDER === 'TRUE';

    return resolve(isWxvideoOpenFlowGoodsDetail || checkIsChannelsAsync());
  });
}

// 判断交易组件开通状态
export async function getTradeModuleStatus(useCache = true) {
  if (useCache && cache.tradeModuleStatus != undefined)
    return cache.tradeModuleStatus;
  const tradeModuleStatus = await app.request({
    path: '/wscwxvideo/trade-module/weapp-trade-module-status.json',
    method: 'GET',
  });
  cache.tradeModuleStatus = tradeModuleStatus;

  return cache.tradeModuleStatus;
}

// 是否已开通交易组件3.0
export async function checkIsOpenedWxV3() {
  const tradeModuleStatus = await getTradeModuleStatus();
  return tradeModuleStatus.WEAPP_TRADE_MODULE_V3_IS_ENABLE;
}

// scene/check 请求
export const requestSceneCheck = () => {
  const { scene } =
    wx.getEnterOptionsSync() || wx.getLaunchOptionsSync?.() || {};
  if (cache?.sceneCheckResult) {
    return Promise.resolve(cache?.sceneCheckResult);
  }
  return app
    .request({
      origin: 'cashier',
      path: '/pay/wsctrade/order/buy/scene/check-compatible',
      data: { scene },
    })
    .then((res) => {
      cache.sceneCheckResult = res;
      return res;
    });
};

// 交易组件下单前置校验 小程序api
export const checkBeforeAddOrderPromise = () => {
  return new Promise((resolve, reject) => {
    if (cache?.checkBeforeAddOrderResult) {
      return resolve(cache?.checkBeforeAddOrderResult);
    }
    // 基础库版本大于>= 2.23.0 则使用checkBeforeAddOrder
    // https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/ministore/minishopopencomponent2/API/order/checkBeforeAddOrder.html
    if (wx.checkBeforeAddOrder) {
      return wx.checkBeforeAddOrder({
        success(res) {
          cache.checkBeforeAddOrderResult = res?.data;
          resolve(res?.data);
        },
        fail(error) {
          app.logger.appError({
            name: '[weapp]-wxvideo',
            message: '交易组件下单--调用wx.checkBeforeAddOrder失败',
            detail: error,
          });
          reject(error);
        },
      });
    }
    app.logger.appError({
      name: '[weapp]-wxvideo',
      message: '交易组件下单--基础库版本不够无法调用wx.checkBeforeAddOrder',
      detail: {},
    });
    reject({});
  });
};

export const checkOrderSyncWechatPromiseWithCache = (orderNo) => {
  if (
    cache?.checkOrderSyncWechatResult &&
    cache?.checkOrderSyncWechatResult[orderNo]
  ) {
    return cache?.checkOrderSyncWechatResult[orderNo].value;
  }
  return app
    .request({
      path: '/wscwxvideo/trade-module/checkOrderSyncWechat.json',
      data: { orderNo },
    })
    .then((res) => {
      if (!cache?.checkOrderSyncWechatResult) {
        cache.checkOrderSyncWechatResult = {
          [orderNo]: res,
        };
      } else {
        cache.checkOrderSyncWechatResult[orderNo] = res;
      }
      return res.value;
    })
    .catch((error) => {
      app.logger.appError({
        name: '[weapp]-wxvideo',
        message: '交易组件下单--待支付页查询订单是否同步微信失败',
        detail: {
          orderNo,
          error,
        },
      });
      return false;
    });
};
