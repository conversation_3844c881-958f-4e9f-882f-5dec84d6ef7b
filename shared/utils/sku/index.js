import * as request from 'shared/utils/request';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

const app = getApp();
const skuCache = {};
const skuIronCache = {};
const hasOwnProperty = Object.prototype.hasOwnProperty;

let isRequesting = false;
let isInSkuWhite = true;

function getSkuDataV1(alias, options = {}) {
  const { useCache = false, singleRequest = true } = options;

  if (useCache && hasOwnProperty.call(skuCache, alias)) {
    return Promise.resolve(skuCache[alias]);
  }

  return new Promise((resolve, reject) => {
    if (isRequesting && singleRequest) return;
    isRequesting = true;

    app.carmen({
      api: 'weapp.wsc.item.detail/1.0.0/get',
      query: {
        alias,
        fans_type: app.getFansType(),
      },
      success: (res) => {
        isRequesting = false;

        if (useCache) skuCache[alias] = res;
        res.dataVersion = 'v1';
        resolve(res);
      },
      fail: (res) => {
        isRequesting = false;
        reject(res.msg || '获取商品信息失败');
      },
    });
  });
}

function getSkuData(alias, options = {}) {
  if (!isInSkuWhite) {
    return getSkuDataV1(alias, options);
  }
  const { useCache = false, singleRequest = true, activityTypes } = options;
  const { goodsIdForBirthday = '' } = options;

  if (useCache && hasOwnProperty.call(skuCache, alias)) {
    return Promise.resolve(skuCache[alias]);
  }
  if (isRequesting && singleRequest) return;
  isRequesting = true;
  return request
    .node({
      path: '/wscshop/sku/skudata.json',
      data: {
        alias,
        fans_type: app.getFansType(),
        offlineId: app.getOfflineId(),
        bizScene: 'goods_sku_component',
        activityTypes,
        isCombo: true,
        birthdayScene: 'WEAAPP_DECORATE_GOODS',
        goodsIdForBirthday,
      },
    })
    .then((res) => {
      isRequesting = false;
      if (res && res.value === false) {
        isInSkuWhite = false;
        return getSkuDataV1(alias, options);
      }
      if (useCache) skuCache[alias] = res;
      return parseSkuData(res, alias);
    })
    .catch((err) => {
      isRequesting = false;
      // 有过jira复现不出来记录下
      app.logger.requestError({
        name: 'common-sku-error',
        message: err.msg || '获取商品信息失败',
        alert: 'warn',
        detail: err,
      });
      return Promise.reject(err.msg || '获取商品信息失败');
    });
}

function getSkuDataIron(alias, options = {}) {
  const { useCache = false } = options;

  if (useCache && hasOwnProperty.call(skuIronCache, alias)) {
    return Promise.resolve(skuIronCache[alias]);
  }

  return app
    .request({
      path: 'v2/showcase/sku/skudata.json',
      query: {
        alias,
        oid: app.getOfflineId(), // iron里面需要oid
      },
    })
    .then((skuData) => {
      if (useCache) skuIronCache[alias] = skuData;
      return skuData;
    });
}

function getActivity(goodsId) {
  return app.request({
    path: '/wscshop/ump/activityInfo/activity.json',
    query: {
      goods_id: goodsId,
    },
  });
}

/**
 * 替换sku卡门接口，卡门接口会返回一些商品数据，新接口只有少数必要字段
 * 有问题@chenmo
 * @param {*} skuData 新数据
 * @returns 将新数据按照老数据的方式重新组装一下
 */
function parseSkuData(skuData, alias) {
  const newData = {};
  const {
    price,
    quota,
    quotaUsed,
    itemDataModel: {
      itemId,
      isVirtual,
      goodsType,
      title,
      startSaleNum,
      pictures,
      origin,
      validityType,
      startSoldTime,
    },
    preSaleExtraModel,
    itemActivitySpuModels,
    maxPrice,
    minPrice,
    option: { hideCart = false } = {},
  } = skuData;
  const preSaleInfo = {};
  newData.sku = mapKeysCase.toSnakeCase(skuData, false);
  // 抹平预售和定金
  if (preSaleExtraModel) {
    const {
      skuDepositModels = [],
      deposit,
      maxDeposit,
      minDeposit,
    } = preSaleExtraModel;
    preSaleInfo.extend = mapKeysCase.toSnakeCase(preSaleExtraModel);
    deposit !== undefined &&
      (preSaleInfo.deposit = {
        deposit,
        max: maxDeposit,
        min: minDeposit,
      });
    const skuDeposit = {};
    skuDepositModels.reduce((m, s) => {
      m[s.skuId] = s.deposit;
      return m;
    }, skuDeposit);
    newData.sku.list.forEach((item) => {
      skuDeposit[item.id] && (item.deposit = skuDeposit[item.id]);
      return item;
    });
  }
  newData.sku = Object.assign(newData.sku, preSaleInfo, {
    goods_type: goodsType,
    max_price: maxPrice / 100 + '',
    min_price: minPrice / 100 + '',
  });
  newData.sku.list.forEach((item) => {
    item.price = item.price / 100 + '';
  });
  newData.brief = {
    item_id: itemId,
    title,
    presale: preSaleExtraModel ? 1 : 0,
    presale_info_new: newData.sku.pre_sale_extra_model,
    quota,
    quota_used: quotaUsed,
    start_sale_num: startSaleNum,
    is_virtual: isVirtual,
    picture: pictures,
    alias,
    price,
    origin,
    hide_shopcart: +hideCart,
    startSoldTime,
  };
  if (+isVirtual === 3) newData.virtual = { validity_type: validityType };
  // 抹平营销活动
  if (itemActivitySpuModels && itemActivitySpuModels[0]) {
    const {
      type,
      price,
      minPrice,
      maxPrice,
      list = [],
      oldPrice,
      priceTitle,
    } = itemActivitySpuModels[0];
    if (price !== oldPrice) {
      newData.activity = {
        goods_preference: {
          show_price: price,
          price_range: { min: minPrice / 100 + '', max: maxPrice / 100 + '' },
          type,
          skus: list.reduce((map, item) => {
            map[item.id] = { price: item.price / 100 + '' };
            return map;
          }, {}),
          // 外层的限购是计算过的最终限购
          quota,
          quota_used: quotaUsed,
          priceTitle,
        },
      };
    }
  }
  newData.components = [
    {
      picture: pictures,
      type: 'config',
      is_virtual: isVirtual,
    },
  ];
  newData.item_sale_prop_list = newData.sku.item_sale_prop_list;
  return newData;
}

export { getSkuDataIron, getSkuData, getActivity };
