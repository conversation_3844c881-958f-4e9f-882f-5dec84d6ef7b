import { node as request } from './request';
import {
  init,
  getWeappGuideSourceType,
  bindSalesman,
  getSalesman,
} from '@youzan/salesman-share';
import Args from '@youzan/weapp-utils/lib/args';
import { getCurrentPage } from 'shared/common/base/wsc-component';
import omit from '@youzan/weapp-utils/lib/omit';
import getApp from 'shared/utils/get-safe-app';

/** 设置分销员页面分享 */
export const setSalesmanShare = function (err, salesman) {
  if (err) return;
  // 增加白名单控制分享
  // 以后接口下线白名单默认都分享

  const isNeedshowMore = salesman.isNeedShareMore !== false;
  if (isNeedshowMore) {
    this.setYZData({ salesman });
  }
};
/** 微信小程序请求 */
export const newRequest = (params) => {
  const obj = omit({ ...params, path: params.url }, 'url');
  return request(obj);
};

/** 初始化分销员能力 */
export const initSalesman = function ({
  scene,
  sst,
  gst,
  getSalesmanData = setSalesmanShare,
  query,
}) {
  const app = getApp() || {};
  const token = app.getToken() || {};
  const { route = '', options = {} } = getCurrentPage();
  const opt = query || options;
  const url = Args.add(route, opt);
  gst = gst || getWeappGuideSourceType(scene);
  const params = { gst, sst, sourceUrl: url };
  const { yzUserId } = token;

  init({
    url,
    logParams: app.logger.getLogParams && app.logger.getLogParams(),
    userId: yzUserId,
    request: newRequest,
  });
  bindSalesman({ ...params });
  getSalesman({}, getSalesmanData.bind(this));
};

export const batchBudgetCommission = (data) => {
  return request({
    method: 'POST',
    path: '/guide/goods/batchBudgetCommission.json',
    data,
  });
};

export const getSalesmanShareInfo = (data) => {
  return request({
    path: '/wscump/salesman/share.json',
    data,
    config: {
      cache: true,
      expire: 10,
    },
  });
};
