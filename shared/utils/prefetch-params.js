import getApp from 'shared/utils/get-safe-app';

const getUUid = (app) => {
  const { user: { uuid } = {} } = app.logger.getGlobal() || {};
  return uuid;
};

// 获取搜索结果接口参数
export const getSearchResultParams = (options) => {
  const app = getApp();
  const { keyword, exhibitionTag = false, type, extra = {} } = options;
  return {
    // 组装查询参数
    page_size: 20,
    page: 1,
    keyword: encodeURIComponent(keyword),
    order_by: '',
    offline_id: app.getOfflineId(),
    from: 'weapp',
    support_paid_content: true, // 是否支持显示知识付费商品，用于 node 兼容老版本（老版本不会返回知识付费商品）
    json: 1,
    openIndependentPrice: app.getOfflineSeparatePrice(),
    activityPriceIndependent: 1,
    uuid: getUUid(app),
    words_type: type,
    showMultiStagePromotion: exhibitionTag,
    needGoodsRank: true,
    ...extra,
  };
};

// 获取商品分组基础信息接口参数
export const getTagBaseParams = (options) => {
  const app = getApp();
  const { alias } = options || {};
  return {
    alias,
    pageSize: 20,
    page: 1,
    order_by: '',
    offline_id: app.getOfflineId(),
    json: 1,
    isShowPeriod: 1,
    openIndependentPrice: app.getOfflineSeparatePrice(),
    activityPriceIndependent: 1,
    uuid: getUUid(app),
    tagAlias: alias,
    needGoodsRank: true,
    supportCombo: true,
    excludedComboSubType: '["none"]',
  };
};
