import url from 'shared/utils/url';
import logv3 from 'utils/log/logv3';
import args from '@youzan/weapp-utils/lib/args';
import getApp from 'shared/utils/get-safe-app';
import { getShareParams } from 'shared/utils/share-params';

const shareParams = getShareParams('poster');

/**
 * 小程序码图片(base64字符串)转换为七牛图片
 * @param {String} mimeType 图片的mimeType类型
 * @param {String} base64Str 小程序码图片(base64字符串)
 */
function _convertCodeImage(base64Str, resolve, reject) {
  const app = getApp();
  const logger = app.logger;
  app.carmen({
    api: 'youzan.shop.weapp.codeimage/1.0.0/convert',
    method: 'POST',
    data: {
      mimeType: 'image/png',
      base64Str: `base64,${base64Str}`
    },
    success: (res) => {
      let url = res.attachment_url || '';
      if (typeof url === 'string') {
        url = url.replace(/^http:/, 'https:');
      }
      resolve(url);
    },
    fail: (err) => {
      logger.requestError({
        alert: 'warn',
        message: '小程序码转换失败',
        response: `err: ${JSON.stringify(err)}`,
        request: {
          options: { base64Str }
        }
      });
      reject('小程序码地址获取失败');
    }
  });
}

/**
 * 获取小程序码
 * @param {String} path 分享地址 - 不要带参数
 * @param {Object} query 分享参数
 */
export function fetchQRCode(path, query = {}) {
  const app = getApp();
  const logger = app.logger;
  const dcPs = logv3.getDCPS();
  const offlineId = app.getOfflineId();
  const { chainStoreInfo = {} } = app.getShopInfoSync();
  const { isMultiOnlineShop } = chainStoreInfo;
  const _kdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId(); // 零售需要用总店的店铺ID，才能获取到小程序码
  const kdtId = _kdtId;
  const scene = {
    kdtId,
    page: path,
    guestKdtId: _kdtId // 零售需要用总店的店铺ID，才能获取到小程序码
  };
  Object.assign(scene, query);

  // nostoreid：路径上不追加offlineId, 由于路径过长，会导致获取小程序码出错，待短链服务上线后，再做替换调整
  if (!query.nostoreid) {
    Object.assign(scene, {
      dcPs,
      offlineId
    });
  }

  query = {
    scene: JSON.stringify(scene),
    page: 'pages/common/blank-page/index',
    hostKdtId: kdtId,
    guestKdtId: _kdtId
  };

  return new Promise((resolve, reject) => {
    app.carmen({
      query,
      api: 'youzan.shop.weapp/1.0.0/codeultra',
      config: {
        skipKdtId: true
      },
      success: (res) => {
        if (!res.image) {
          logger.requestError({
            alert: 'warn',
            message: '获取小程序码失败',
            response: `res: ${JSON.stringify(res)}`,
            request: {
              options: query
            }
          });
          return reject('获取小程序码失败');
        }
        _convertCodeImage(res.image || '', resolve, reject);
      },
      fail: (err) => {
        logger.requestError({
          alert: 'warn',
          message: '获取小程序码失败',
          response: `err: ${JSON.stringify(err)}`,
          request: {
            options: query
          }
        });
        reject('获取小程序码失败');
      }
    });
  });
}

/**
 * https: //developers.weixin.qq.com/miniprogram/dev/api/qrcode.html 中接口 A
 * @param { String } page 小程序页面路径
 * @param { String } query 参数
 * @returns { String } 图片 tempImageFile
 */
export function fetchCode(page, query = {}) {
  const app = getApp();
  const kdtId = app.getKdtId();

  if (!kdtId) throw new Error('必须登录后才能调用该接口');

  let path = args.add(page, { ...query });

  path = encodeURIComponent(path);
  return url({
    origin: 'h5',
    query: {
      path,
      kdtId,
    },
    pathname: '/v3/weapp/code.png',
  }).then((downloadUrl) => {
    return downloadFile(downloadUrl);
  });
}

/**
 * https://developers.weixin.qq.com/miniprogram/dev/api/qrcode.html 中接口 B
 * @param { String } page 页面路径
 * @param { String } scene scene 参数
 * @returns { String } 图片 tempImageFile
 */
export function fetchUnlimitCode(page, scene = '') {
  const app = getApp();
  const kdtId = app.getKdtId();

  if (!kdtId) throw new Error('必须先等了后才能调用该接口');

  if (typeof scene !== typeof '') throw new Error('scene 参数必须是一个字符串，长度 32 位以下');

  return url({
    origin: 'h5',
    query: {
      page,
      scene,
      kdtId,
    },
    pathname: '/v3/weapp/unlimit-code.png',
  }).then((downloadUrl) => {
    return downloadFile(downloadUrl);
  });
}

/**
 * 无限小程序码
 * https://doc.qima-inc.com/pages/viewpage.action?pageId=47037959
 * @param {*} path
 * @param {*} query
 * @param {Number} 指定 kdtId 获取小程序码，即获取这个店铺对应小程序的码
 */
export function fetchUltraCode(path, query = {}, kdtId) {
  const app = getApp();
  const dcPs = logv3.getDCPS();
  const offlineId = app.getOfflineId();
  const { chainStoreInfo = {} } = app.getShopInfoSync();
  const { isMultiOnlineShop } = chainStoreInfo;
  const _kdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId(); // 连锁需要用总店的店铺ID，才能获取到小程序码

  kdtId = kdtId || (app.getAppKdtId && app.getAppKdtId()) || _kdtId;

  const scene = {
    kdtId,
    page: path,
    guestKdtId: app.getKdtId()
  };
  Object.assign(scene, query);

  // nostoreid：路径上不追加offlineId, 由于路径过长，会导致获取小程序码出错，待短链服务上线后，再做替换调整
  if (!query.nostoreid) {
    Object.assign(scene, {
      dcPs,
      offlineId
    });
  }
  query = {
    scene: encodeURIComponent(JSON.stringify(scene)),
    page: 'pages/common/blank-page/index',
    kdtId,
    sid: app.getSessionId(),
    isWeapp: 1,
    hyaLine: query.hyaLine ? 1 : 0
  };

  return url({
    origin: 'h5',
    query,
    pathname: '/v3/weapp/ultra-code.png',
  }).then((downloadUrl) => {
    return downloadFile(downloadUrl);
  });
}

/**
 * 无限小程序码接口 - base64
 *
 * http://zanapi.qima-inc.com/site/service/view/266047
 */
export function fetchUltraCodePost(path, query = {}, kdtId) {
  const app = getApp();
  const logger = app.logger;
  const { isYouzanApp } = app.globalData;
  const dcPs = logv3.getDCPS();
  const offlineId = app.getOfflineId();
  const { chainStoreInfo = {} } = app.getShopInfoSync();
  const { isMultiOnlineShop } = chainStoreInfo;
  const _kdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId(); // 连锁需要用总店的店铺ID，才能获取到小程序码

  kdtId = kdtId || (app.getAppKdtId && app.getAppKdtId()) || (isYouzanApp ? 40419900 : _kdtId);

  const scene = {
    kdtId,
    page: path,
    guestKdtId: query.dsKdtId || app.getKdtId() // 销售员是所属店铺id
  };
  Object.assign(scene, query, shareParams);

  // nostoreid：路径上不追加offlineId, 由于路径过长，会导致获取小程序码出错，待短链服务上线后，再做替换调整
  if (!query.nostoreid) {
    Object.assign(scene, {
      dcPs,
      offlineId
    });
  }

  return new Promise((resolve, reject) => {
    app
      .request({
        data: {
          kdtId,
          page: 'pages/common/blank-page/index',
          query: JSON.stringify(scene)
        },
        method: 'POST',
        path: '/v3/weapp/ultra-code.json'
      })
      .then((res) => {
        if (!res.image_base64) {
          logger.requestError({
            alert: 'warn',
            message: '获取小程序码失败',
            response: `res: ${JSON.stringify(res)}`,
            request: {
              options: query
            }
          });
          return reject('获取小程序码失败');
        }
        _convertCodeImage(res.image_base64 || '', resolve, reject);
      })
      .catch((err) => {
        logger.requestError({
          alert: 'warn',
          message: '获取小程序码失败',
          response: `err: ${JSON.stringify(err)}`,
          request: {
            options: query
          }
        });
        reject('获取小程序码失败');
      });
  });
}

function downloadFile(url) {
  const app = getApp();
  return new Promise((resolve, reject) => {
    app.downloadFile({
      url,
      success(res) {
        const { tempFilePath, header } = res;
        if (
          header &&
          !/image/.test(header['content-type'] || header['Content-Type'] || header.contentType)
        )
          return reject('获取图片失败');
        resolve(tempFilePath);
      },
      fail: reject
    });
  });
}
