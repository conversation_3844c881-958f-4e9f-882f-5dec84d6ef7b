import args from '@youzan/weapp-utils/lib/args';
import { isValidFromParams } from 'utils/guide';

export function isSuccessResponse(res = {}) {
  // 根据类型，判断正确的请求，存到缓存中。
  // 防止缓存 http 响应码正确，但是内容是错误的
  const isNodeRequestSuccess = res.code === 0;
  const isBaymaxRequestSuccess = res.success === true;
  const isCarmenRequestSuccess =
    typeof res.response !== 'undefined' && !res.error_response;
  return (
    isCarmenRequestSuccess || isNodeRequestSuccess || isBaymaxRequestSuccess
  );
}

export function generateCacheKey(url, requestData = {}) {
  // 这个 key 会因为参数的顺序无效（是不是应该对参数排序）
  const formatedUrl = args.add(url, requestData);

  return (
    formatedUrl
      // 去除 http 头 + 域名
      .replace(/^http(s)?:\/\/[^/]*\//, 'storage://') // 逐渐用新的 key 替代老版本中以及缓存的数据
      // 去除 access_token
      .replace(/access_token=([^&]*)/, '')
      // 去除 appid
      .replace(/app_id=([^&]*)/, '')
  );
}

// 在小程序请求中放入埋点相关数据
export function setLoggerDataToExtraData(extraData) {
  const app = getApp();
  if (!app || !app.logger) {
    return;
  }

  const logData = app.logger.getLogParams();
  const { user, context } = logData;

  const fromParamsStr = context?.from_params;
  if (isValidFromParams(fromParamsStr)) {
    extraData.from_params = fromParamsStr;
  }

  if (user.uuid) {
    extraData.uuid = user.uuid;
    extraData.ftime = user.ftime;
  }
}
