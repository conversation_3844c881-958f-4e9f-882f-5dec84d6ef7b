/* eslint-disable no-use-before-define */
import Event from '@youzan/weapp-utils/lib/event';
import extend from '@youzan/weapp-utils/lib/extend';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import formatUrl from 'shared/utils/url';
// eslint-disable-next-line import/no-cycle
import getSafeApp from 'shared/utils/get-safe-app';
import { getExtSync } from 'shared/utils/browser/ext-config';
import { readDataFromCache, writeDataToCache } from './cache';
import { isSuccessResponse, setLoggerDataToExtraData } from './utils';
import { Hummer } from 'shared/utils/hummer';
// import RontgenWeapp from '@youzan/rontgen-weapp/dist';
import {
  setRequestDep as setTeeBizRequestDep,
  beforeHooks,
} from '@youzan/tee-biz-request';

const defaultOptions = {
  config: {},
  method: 'GET',
  header: {},
};
const urlEncodedHeader = {
  header: {
    'content-type': 'application/x-www-form-urlencoded',
  },
};
const defaultCarmenOptions = {
  origin: 'carmen',
  ...urlEncodedHeader,
};
const defaultNodeOptions = {
  origin: 'h5',
  header: {
    'content-type': 'application/json',
  },
};

const defaultBaymaxOption = {
  origin: 'baymax',
  ...urlEncodedHeader,
};

/**
 * 一个请求依赖的参数信息
 *
 * 可以考虑加个 timeout
 */
let requestDepConfig = {
  appId: '',
  version: '',
  kdtId: '',
  offlineId: '',
  accessToken: '',
  sessionId: '',
  hasShop: false,
  hasToken: false,
};

/**
 * 由于条件不充分，未发出的请求队列
 */
const waitingTokenRequests = [];
const waitingKdtIdRequests = [];
const waitingShopInfoRequests = [];
/**
 * 初始化调用链 SDK
 */

// const app = getApp();
// const { isRetailApp } = app.globalData;

// 主包体积优化 下线rontgen - https://qima.feishu.cn/docx/doxcn04SH3AfpoQoFpRMQEeEjHd
// RontgenWeapp.init({
//   appName: 'wsc',
//   configFetchDelay: 5000,
//   // 小程序请求比较多，延迟两秒首屏上报的次数较少
//   reportInterval: 2000,
//   watermark: {
//     containerElmId: 'yzRontgenWatermark',
//     autoRemove: 5000,
//   },
// });
// const rontgenWeapp = RontgenWeapp.getInstance();
// const rontgenWrapperedRequest = rontgenWeapp.weixinRequestWrapper(wx);

/**
 * 请求默认配置
 */

/**
 * 监听的事件
 * auth:token:fail 获取 token 失败
 * auth:token:success 获取 token 成功，依赖返回的 token
 * 触发的事件
 * auth:login
 * request:api:fail
 * request:code:fail
 * request:server:fail
 * request:business:fail
 * @param {*} config
 */

const execQueue = (queue = []) => {
  let next = queue.shift();
  while (next) {
    next();
    next = queue.shift();
  }
};

export function setKdtId(kdtId = '') {
  requestDepConfig.kdtId = kdtId;
  requestDepConfig.hasKdtId = !!kdtId;

  execQueue(waitingKdtIdRequests);
}

export function updateShop(config) {
  const { offlineId, kdtId } = config;

  if ('offlineId' in config) {
    requestDepConfig.offlineId = offlineId;
  }

  requestDepConfig.hasShop = true;

  kdtId && setKdtId(kdtId);

  execQueue(waitingShopInfoRequests);
}

export function updateToken(token) {
  token = mapKeysCase.toCamelCase(token);
  requestDepConfig.hasToken = true;
  requestDepConfig.tokenTime = Date.now();
  requestDepConfig.accessToken = token.accessToken;
  requestDepConfig.sessionId = token.sessionId;

  execQueue(waitingTokenRequests);
}

export function setRequestDep(config = {}) {
  requestDepConfig = {
    ...requestDepConfig,
    ...config,
  };

  setTeeBizRequestDep(requestDepConfig);
}

// 调用栈: node -> invoke | 劫持option.success，回调 -> waiting -> request -> doRequest -> requestSuccess
/**
 * 使用node接口请求
 * @param {*} options 请求配置
 */
export function node(options) {
  options = extend({}, defaultNodeOptions, options);

  return invoke(options);
}

/**
 * 使用node接口并使用CDN
 * @param {*} options
 * @returns
 */
export function nodeUseCdn(options) {
  options = extend({}, options, { config: { useCdn: true } });
  return node(options);
}

export function carmen(options) {
  options = extend({}, defaultCarmenOptions, options);
  options = plugRetailSource(options); // 给零售的接口带上来源标示

  if (!options.api) throw new Error('Carmen 接口必须提供 api');

  options.path = ('/api/oauthentry/' + options.api).replace('//', '/');

  return invoke(options);
}

// 使用彩虹桥接口请求
export function baymax(options) {
  options = extend({}, defaultBaymaxOption, options);
  options = plugRetailSource(options); // 给零售的接口带上来源标示

  if (!options.api) throw new Error('彩虹桥接口必须提供 api');

  options.path = ('/api/' + options.api).replace('//', '/');

  return invoke(options);
}

function plugRetailSource(options = {}) {
  // 获取请求接口
  const injectSource = (source) => ({
    ...options,
    query: {
      ...options.query,
      retail_source: source,
    },
  });

  const app = getSafeApp() || {};
  const globalData = app.globalData || {};
  let userVersion = getExtSync('userVersion');
  switch (globalData.scene) {
    case 1: {
      userVersion = userVersion || '3.8.0';
      return injectSource(`MINAPP-SCAN-${userVersion}`);
    }
    case 2: {
      userVersion = userVersion || '3.12.0';
      return injectSource(`MINAPP-FREE-${userVersion}`);
    }
    case 3: {
      userVersion = userVersion || '1.0.0';
      return injectSource(`MINAPP-SHELF-${userVersion}`);
    }
    default:
      return options;
  }
}

function invoke(options) {
  options = extend({}, defaultOptions, options);

  return new Promise((resolve, reject) => {
    // 容错以及 promise 处理
    const { success, fail, complete } = options;
    options.success = (response) => {
      try {
        if (success) {
          success(response) && resolve(response);
        } else {
          resolve(response);
        }
        complete && complete(response);
      } catch (e) {
        console.log(e);
      }
    };

    options.fail = (response) => {
      fail ? fail(response) && reject(response) : reject(response);
      complete && complete(response);
    };

    waiting(options.config, () => request(options));
  });
}
/**
 * 执行请求，对于已经从缓存中获取数据返回的请求要特殊处理
 * @param {Boolean} needCache 返回内容需要缓存
 * @param { Boolean } needResponse 返回内容需要调用 options.success， 需要注意的是不管这个参数是否为真都会返回请求响应的内容。 实际上只有 refreshPromise 才会拿到数据
 */
function doRequest(requestOptions) {
  return (needCache, needResponse) => {
    const requestPromise = new Promise((resolve, reject) => {
      const tempApp = getSafeApp(); // TODO http2上线稳定后, 将移除
      const enableHttp2 =
        tempApp && tempApp.globalData && tempApp.globalData.enableHttp2;
      wx.request({
        ...requestOptions,
        success: (resp) => resolve(resp),
        fail: (err) => reject(err),
        enableHttp2,
        complete: (res) => {
          if (PRODUCTION && !ECLOUD_MODE) {
            try {
              Hummer.captureXhr({
                statusCode: res.statusCode || 0,
                method: requestOptions.method,
                url: requestOptions.url,
                params: requestOptions.data,
              });
            } catch (error) {
              console.error('Hummer: 监控平台SDK错误, 请联系 刘海红', error);

              const app = getSafeApp();

              app.logger &&
                app.logger.appError({
                  name: 'hummer_sdk_error',
                  message: error.message,
                  detail: {},
                });
            }
          }
        },
      });
    });

    // catch 先 then 后的注意点在于
    // 这里的 catch 处理的是服务端返回时，响应的业务错误。保证这里只处理服务端的业务错误
    // 如果 then 放在前面，catch 放在后面。then 逻辑中有执行报错，就会出现代码执行错误也会运行到原有的 catch 服务端业务错误处理逻辑中
    // so 请勿随意改顺序，除非有极大的把握
    return requestPromise
      .catch(requestFail(requestOptions, needCache, needResponse))
      .then(requestSuccess(requestOptions, needCache, needResponse));
  };
}

/**
 * 发起请求
 * @param {Object} options.config 配置
 * @param {Object} options.config.cache 是否使用缓存
 * @param {Object} options.config.expire 缓存过期时间，以秒为单位，有cache生效
 * @param {Object} options.config.forceRefreshCache 强制刷新缓存
 * @param {Object} options.config.needRefresh 同时获取缓存数据和新数据
 * @param {Object} options.config.noQuery,
 * @param {Object} options.config.noStoreId,
 */
async function request(options = {}) {
  const requestOptions = await formatOptions(options);
  const { success } = options;
  // doRequest return为Promise函数
  const startRequest = doRequest(requestOptions);

  if (!options.config) {
    // 做保护
    options.config = {};
  }
  if (options.config.cache) {
    const cacheData = readDataFromCache(requestOptions, options.config.expire);

    // 非 GET 请求直接跳过
    if (requestOptions.method && requestOptions.method !== 'GET') {
      console.warn('暂不支持非 GET 请求缓存');
      return startRequest(false /* no cache */, true /* need response */);
    }

    // 强制更新缓存或者没有缓存数据的，直接发起请求，在请求完成后缓存数据并响应
    if (options.config.forceRefreshCache || !cacheData) {
      return startRequest(true /* need cache */, true /* need response */);
    }

    // 来自缓存的数据加上标志
    if (cacheData) {
      cacheData.fromCache = true;
    }

    // 需要同时获取缓存数据和新数据
    if (options.config.needRefresh && cacheData) {
      return success({
        ...cacheData,
        cacheData: {
          refresh: true,
          refreshPromise: startRequest(
            true /* need cache */,
            false /* no response */
          ),
        },
      });
    }

    // 直接从缓存里面获取数据，更新缓存中请求的数据
    success(cacheData);
    // ?? success了，为啥还要请求？
    // startRequest(true /* need cache */, false /* no response */);
  } else {
    // 不需要缓存的直接请求
    startRequest(false /* no cache */, true /* need response */);
  }
}

function escapeErrorChars(string) {
  // 从json2里面提取出来的js不支持的特殊字符正则； https://github.com/douglascrockford/JSON-js/blob/4982ffbecf8175f233b0c9626bc8629c70faf5a2/json2.js#L371
  // eslint-disable-next-line
  const escapable =
    // eslint-disable-next-line no-control-regex
    /[\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;
  return string.replace(escapable, '');
}

function parseResponse(requestOptions, response) {
  // 后续加上白名单控制逻辑
  let _response = response;
  if (requestOptions.dataType === 'string' && typeof response === 'string') {
    try {
      _response = JSON.parse(response);
    } catch (error) {
      const escapedStr = escapeErrorChars(response.toString());

      // 套娃, 修复 404 error
      try {
        _response = JSON.parse(escapedStr);
      } catch (escapeErr) {
        console.log(escapeErr);
      }
    }
  }

  return _response;
}
/**
 * wx.request 调用成功
 * @param {*} requestOptions
 * @param {*} needCache
 * @param {*} needResponse
 */
function requestSuccess(requestOptions, needCache, needResponse) {
  return (response) => {
    const responseData = parseResponse(requestOptions, response.data);
    const errorMsg = {
      code: response.code || -9999,
      msg: response.errMsg || response.msg || '服务器请求失败，请稍后重试',
      res: responseData,
    };

    // 接口调用失败（请求到有赞服务器，返回状态码不对）
    if (response.statusCode !== 200) {
      Event.trigger('request:server:fail', {
        requestOptions,
        response,
      });

      needResponse && requestOptions.fail(errorMsg);
      return Promise.reject(errorMsg);
    }

    response = responseData;

    /**
     * node
     * {
     *  code: ,
     *  data: ,
     *  msg: ,
     * }
     *
     * carmen
     * {
     *  response: ,
     *  error_response: ,
     * }
     */
    const isCarmen = requestOptions.origin === 'carmen';
    const isBaymax = requestOptions.origin === 'baymax';

    if (isSuccessResponse(response)) {
      let data = isCarmen ? response.response : response.data;
      if (typeof data !== 'object') {
        if (!PRODUCTION) {
          console.warn('请求返回内容非对象格式，可能会导致数据丢失');
        }
        data = {
          value: data,
        };
      }
      if (needCache) {
        writeDataToCache(
          {
            ...requestOptions,
            expire: requestOptions._options.config.expire,
          },
          data
        );
      }

      // 处理业务逻辑错误
      try {
        needResponse && requestOptions.success(data);
      } catch (error) {
        Event.trigger('request:business:fail', {
          requestOptions,
          response,
          error,
        });
      }
      return data;
    }

    if (isCarmen) {
      response = response.error_response;
    }
    if (isBaymax) {
      if (response.gw_err_resp) {
        response = response.gw_err_resp;
        response.code = response.err_code;
        response.msg = response.err_msg;
      } else {
        response.msg = response.message;
      }
    }
    // 分别对应 node 和 Carmen 和 彩虹桥 返回的会话过期
    if (
      response.code === -1 ||
      ((response.code === 40010 || response.code === 40009) && isCarmen) ||
      response.code === 4205
    ) {
      return new Promise((resolve, reject) => {
        function fail() {
          needResponse && requestOptions.fail(errorMsg);
          reject();
        }

        const authTokenFailEventName = 'auth:token:fail';
        // 如果登录失败
        Event.once(authTokenFailEventName, fail);

        // 添加请求到成功队列，重新设置后会调用
        function request() {
          Event.off(authTokenFailEventName, fail);
          // 更新请求中的 token 信息
          formatOptions(requestOptions._options).then((options) => {
            requestOptions = options;
            const startRequest = doRequest(requestOptions);
            resolve(startRequest(needCache, needResponse));
          });
        }

        // 说明请求过程中正在登录，现在已经登录完成，直接进入等待
        if (
          requestDepConfig.hasToken &&
          requestOptions.requestTime < requestDepConfig.tokenTime
        ) {
          return waiting(requestOptions._options.config, request);
        }

        // 触发登录事件
        if (requestDepConfig.hasToken) {
          Event.trigger('auth:login');
          // 后边所有请求都会被添加到请求队列
          requestDepConfig.hasKdtId = false;
          requestDepConfig.hasToken = false;
          requestDepConfig.hasShop = false;
        }

        // hasToken 为 false 的情况，表示正在登录过程，所有失败的请求都需要进入等待
        waiting(requestOptions._options.config, request);
      }).catch((e) => console.log(e));
    }

    Event.trigger('request:code:fail', { requestOptions, response });

    errorMsg.code = response.code;
    errorMsg.msg = response.msg;

    needResponse && requestOptions.fail(errorMsg);
    return Promise.reject(errorMsg);
  };
}

/**
 * wx.request 调用失败或者代码哪里出错了
 * @param {*} requestOptions
 * @param {*} needCache
 * @param {*} needResponse
 */
function requestFail(requestOptions, needCache, needResponse) {
  return (response) => {
    const res = response.res || response.message || response || {};
    const isCarmen = requestOptions.origin === 'carmen';
    const errorResponse = isCarmen ? res.error_response : res;
    const errorResponseData = errorResponse || {};

    Event.trigger('request:api:fail', {
      requestOptions,
      response: errorResponseData,
    });

    // 如果需要返回就返回
    if (needResponse) {
      const message =
        errorResponseData.errMsg ||
        errorResponseData.msg ||
        errorResponseData.message ||
        '接口调用失败，请稍后重试';
      requestOptions.fail({
        code: errorResponseData.code || -9999,
        msg: message,
        res: errorResponseData,
      });
    }

    return Promise.reject('请求失败');
  };
}
/**
 * 根据请求配置进行等待参数齐全
 * @param {Object} options 请求配置
 */
function waiting(config, request) {
  // 等待 kdtId
  const waitingKdtId = () => {
    if (!config.skipKdtId && !requestDepConfig.hasKdtId) {
      return addNewRequest(waitingKdtIdRequests, request, config);
    }

    request();
  };

  // 等到店铺信息获取到（主要是因为多网点的店铺需要传一个网点 ID）
  const waitingShopInfo = () => {
    if (!config.skipShopInfo && !requestDepConfig.hasShop) {
      return addNewRequest(waitingShopInfoRequests, waitingKdtId, config);
    }
    waitingKdtId();
  };

  // 等待 token 数据获取到
  const waitingToken = () => {
    if (!config.skipToken && !requestDepConfig.hasToken) {
      return addNewRequest(waitingTokenRequests, waitingShopInfo, config);
    }
    waitingShopInfo();
  };

  // 进入等待
  if (config.priority === 'low') {
    setTimeout(() => waitingToken(), 800);
  } else {
    waitingToken();
  }
}

function addNewRequest(requestTaskList, request, config = {}) {
  if (config.priority === 'high') {
    requestTaskList.unshift(request);
  } else {
    requestTaskList.push(request);
  }
}

/**
 * 组装请求对象
 * 此方法已为异步，使用时请注意
 * @param {Object} options 请求参数
 * @param {Getters} getters @src/bootstrap-tem/getters.js
 */
function formatOptions(options) {
  const {
    method,
    query = {},
    path: pathname,
    success,
    fail,
    dataType = 'string',
  } = options;

  let { data = {}, origin, header = {} } = options;

  const { noQuery, noStoreId, skipToken, useCdn } = options.config;

  if (!origin) {
    throw new Error('所有请求必须指定 origin:', pathname);
  }

  if (!noStoreId) query.store_id = requestDepConfig.offlineId || '';

  if (!noQuery) {
    query.app_id = requestDepConfig.appId;
    query.kdt_id = query.kdt_id || query.kdtId || requestDepConfig.kdtId || '';
    query.access_token = requestDepConfig.accessToken || '';
  }

  // 请求 header 拼装
  const extraData = {
    is_weapp: 1,
    sid: requestDepConfig.sessionId,
    version: requestDepConfig.version,
    client: 'weapp',
    bizEnv: BUILD_TARGET || 'wsc',
  };
  // 如果 token 需要跳过，要带特殊标志
  // 目前只有部分接口会识别这个参数
  if (skipToken) {
    extraData.skip_sid = 1;
  }

  setLoggerDataToExtraData(extraData);

  try {
    const app = getApp();
    const enableCdn = app && app.globalData && app.globalData.enableCdn;
    if (enableCdn && useCdn) {
      header = { ...header };
      delete extraData.sid;
      delete extraData.uuid;
      delete query.access_token;

      // 转换动态域名到静态域名
      if (origin === 'h5') {
        origin = 'h5m';
      }
    }
  } catch (error) {
    console.error(error);
  }

  // 发往 wsc-h5-account 的接口，带上指定 header 头
  try {
    const isToWscH5Account = /^\/(wscaccount|passport)/.test(pathname);
    if (isToWscH5Account && !header['page-path']) {
      const pages = getCurrentPages();
      const p = pages[pages.length - 1]?.route;
      header['page-path'] = p;
    }
  } catch {}

  header['Extra-Data'] = JSON.stringify(extraData);

  return formatUrl({
    origin,
    pathname,
    query,
  }).then(async (url) => {
    // 对特定接口数据加密
    const app = getSafeApp() || {};
    const { key: publicKey, apis = [] } = app.getCryptoInfo() || {};

    const [path] = url.split('?');
    // 请求path在apis中，则要加密
    if (apis.indexOf(path) > -1 && publicKey) {
      const { encryptedData, encryptedRSAKey } = await encryptRequest(
        data,
        publicKey
      );
      header['x-auth-token'] = encryptedRSAKey;
      data = { encryptedData };
    }
    const result = {
      origin,
      _options: options,
      requestTime: Date.now(),
      url,
      data,
      header,
      method: (method || 'GET').toUpperCase(),
      success,
      fail,
      dataType,
    };
    return result;
  });
}

function encryptRequest(data, publicKey) {
  return import('@youzan/crypto').then((crypto) => {
    const { key, iv } = crypto.utils.generateKeyAndIv();

    // 用aes 加密请求data
    const encryptedData = crypto.aes.encrypt({
      data: JSON.stringify(data),
      key,
      iv,
    });
    // 用rsa加密publicKey
    const encryptedRSAKey = crypto.rsa.encrypt({
      data: `${key}:${iv}`,
      publicKey,
    });

    return { encryptedData, encryptedRSAKey };
  });
}

// tee-biz-request前置hook
// 将埋点数据放入request
beforeHooks.add((options) => {
  const extraData = JSON.parse(options.header['Extra-Data'] || '{}');

  setLoggerDataToExtraData(extraData);

  options.header['Extra-Data'] = JSON.stringify(extraData);

  return true;
});
