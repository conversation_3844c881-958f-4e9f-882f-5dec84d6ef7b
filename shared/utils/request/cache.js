import debounce from '@youzan/weapp-utils/lib/debounce';
import { generateCacheKey } from './utils';

const LS_KEY = 'request-cache';
const MAX_CACHE_LENGTH = 25;

const Cache = {
  inited: false,
  // cacheKeyMap 存储键值集合
  cacheKeySet: new Set(),
  // cacheKeyMap 储存对象，包含键值及存储时间
  // 如: { key: { key, time } }
  cacheKeyMap: Object.create(null),
  cacheStorage: {},
};

const syncStorage = debounce(() => {
  wx.setStorage({
    key: LS_KEY,
    data: {
      cacheKeyMap: Cache.cacheKeyMap,
      cacheStorage: Cache.cacheStorage,
    },
  });
}, 1000);

// 如果内存中还没有就把本地已缓存的所有数据获取出来
const initCacheStorage = () => {
  if (!Cache.inited) {
    Cache.inited = true;
    let cacheData = Object.create(null);
    try {
      cacheData = wx.getStorageSync(LS_KEY) || {};
    } catch (err) {
      console.error('[request cache] initCacheStorage error', err);
    }
    const { cacheKeyMap = {}, cacheStorage = {} } = cacheData;
    const currTime = +new Date() / 1000;
    Object.keys(cacheKeyMap).forEach((cacheKey) => {
      const item = cacheKeyMap[cacheKey];
      const expire = item.expireTime - currTime;
      if (expire > 0) {
        saveToCacheStorage(cacheKey, cacheStorage[cacheKey], expire);
      }
    });
  }
};

function delFromCacheStorage(cacheKey) {
  Cache.cacheKeySet.delete(cacheKey);
  delete Cache.cacheKeyMap[cacheKey];
  delete Cache.cacheStorage[cacheKey];
}

function saveToCacheStorage(cacheKey, cacheVal, expire = 86400) {
  const createTime = +new Date() / 1000;
  Cache.cacheKeySet.add(cacheKey);
  Cache.cacheKeyMap[cacheKey] = {
    key: cacheKey,
    createTime,
    expireTime: createTime + expire,
  };
  Cache.cacheStorage[cacheKey] = cacheVal;
}

/**
 * 读取请求缓存
 * @param {*} url 链接
 * @param {*} data 请求参数
 * @param {*} expire 过期时间
 */
export function readDataFromCache({ url, data: requestData }, expire = null) {
  initCacheStorage();
  const cacheKey = generateCacheKey(url, requestData);
  if (Cache.cacheKeySet.has(cacheKey)) {
    const currTime = +new Date() / 1000;
    const cacheData = Cache.cacheStorage[cacheKey];
    if (cacheData) {
      if (typeof expire === 'number' && currTime < (Cache.cacheKeyMap[cacheKey].createTime + expire)) {
        return deepClone(cacheData);
      }
      if (typeof expire !== 'number' && currTime < Cache.cacheKeyMap[cacheKey].expireTime) {
        return deepClone(cacheData);
      }
    }
    // 过期或者失效了
    delFromCacheStorage(cacheKey);
  }
  return null;
}

// expireTime仅用于过期比较，因为如果多个地方调用，expire不同，就会有问题
export function writeDataToCache({ url, data: requestData, expire = 86400 }, data) {
  initCacheStorage();
  const cacheKey = generateCacheKey(url, requestData);

  if (Cache.cacheKeySet.size > MAX_CACHE_LENGTH) {
    const cacheKeyVals = [...Cache.cacheKeySet].map((cacheKey) => {
      return Cache.cacheKeyMap[cacheKey];
    });
    cacheKeyVals.sort((a, b) => a.expireTime - b.expireTime);
    // 凡是超出空间或者过期的都要删除
    const exceedNum = Cache.cacheKeySet.size - MAX_CACHE_LENGTH;
    const currTime = +new Date() / 1000;
    for (let i = 0; i < cacheKeyVals.length; i += 1) {
      const item = cacheKeyVals[i];
      // 删除过期的或者剩余时间最短的
      delFromCacheStorage(item.key);
      if (i > exceedNum && item.expireTime > currTime) {
        break;
      }
    }
  }
  saveToCacheStorage(cacheKey, deepClone(data), expire);
  syncStorage();
}

function deepClone(data) {
  if (typeof data === 'object') {
    return JSON.parse(JSON.stringify(data));
  }
  return data;
}
