function hash(text) {
  let hash = 5381;
  let index = text.length;

  while (index) {
    hash = (hash * 33) ^ text.charCodeAt(--index);
  }

  return hash >>> 0;
}

/**
 * 通过页面栈当前route的path和入参生成一个页面唯一标识
 */
export default function getUniquePageKey(ctx) {
  let cur;
  if (ctx) {
    cur = ctx;
  } else {
    const pages = getCurrentPages();
    cur = pages[pages.length - 1];
  }
  if (!cur.__uniqueKey__) {
    const hashCode = hash(cur.route + JSON.stringify(cur.options));
    cur.__uniqueKey__ = hashCode;
  }
  return cur.__uniqueKey__;
}
