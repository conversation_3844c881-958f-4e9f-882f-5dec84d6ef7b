import Event from '@youzan/weapp-utils/lib/event';
import extend from '@youzan/weapp-utils/lib/extend';
import { transformDomain, DOMAIN_TYPE } from 'shared/utils/dmc';

let requestDepConfig = {
  appId: '',
  version: '',
};

export const downloadFile = (options) => {
  const extraData = {
    is_weapp: 1,
    client: 'weapp',
    ...requestDepConfig,
  };

  options = extend({}, options, {
    header: {
      'Extra-Data': JSON.stringify(extraData),
    },
  });

  return new Promise((resolve, reject) => {
    const { success, fail, complete } = options;
    options.success = (response) => {
      try {
        if (success) {
          success(response) && resolve(response);
        } else {
          resolve(response);
        }
        complete && complete(response);
      } catch (e) {
        console.log(e);
      }
    };

    options.fail = (response) => {
      Event.trigger('downloadFile:fail', {
        options,
        response,
      });

      fail ? fail(response) && reject(response) : reject(response);
      complete && complete(response);
    };

    transformDomain(options.url, DOMAIN_TYPE.DOWNLOAD)
      .catch(() => options.url)
      .then((downloadUrl) => {
        wx.downloadFile({ ...options, url: downloadUrl });
      });
  });
};

export const setDownloadFileDep = (config) => {
  requestDepConfig = {
    ...requestDepConfig,
    ...config,
  };
};
