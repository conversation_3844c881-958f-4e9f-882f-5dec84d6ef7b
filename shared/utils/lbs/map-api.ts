/**
 * 使用webserver接口包装一层，使用qqmap模式返回腾讯地图数据并兼容腾讯地图sdk返回格式
 */

import request from '@youzan/tee-biz-request';

const baseRequest = (name, data) => {
  // 业务场景定义
  data.biz = 'shared';
  const path = `/wsctrade/qqmap/api/${name}.json`;
  return request({
    path,
    data,
    method: 'GET',
  });
};

interface GetSuggestionOptions {
  keyword: string;
  policy?: number;
  region?: string;
  region_fix?: number;
  page_size?: number;
  page_index?: number;
}
// 关键字查询地址
export const getSuggestion = (options: GetSuggestionOptions) => {
  const data = {
    ...options,
    city: options.region || '',
    address_format: '', // node接口默认为short，这里显示定义为默认格式
    policy: options.policy || 0,
  };
  return baseRequest('getSuggestion', data).then((res) => {
    return {
      status: 0,
      data: res,
    };
  });
};

interface GeocoderOptions {
  address: string;
  region: string;
}
// 地址解析
export const geocoder = (options: GeocoderOptions) => {
  const data = {
    address: (options.region || '') + options.address,
  };
  return baseRequest('geocoder', data).then((res) => {
    return {
      status: 0,
      result: res,
    };
  });
};

interface Location {
  latitude: number;
  longitude: number;
}

interface ReverseGeocoderOptions {
  location: string | Location;
  coord_type: number;
  get_poi: number;
  poi_options: string;
}
// 逆地址解析
export const reverseGeocoder = (options: ReverseGeocoderOptions) => {
  const { location: locat, ...rest } = options;
  const location =
    typeof locat === 'string' ? locat : `${locat.longitude},${locat.latitude}`;
  const data = {
    ...rest,
    location,
  };
  return baseRequest('reverseGeocoder', data).then((data) => {
    return {
      status: 0,
      result: data,
    };
  });
};
