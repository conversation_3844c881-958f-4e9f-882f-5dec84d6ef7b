import { baiduToGcj, gcjToBaidu } from '@youzan/shop-tee-shared';

import {
  getSuggestion,
  geocoder as geocoder<PERSON>pi,
  reverseGeocoder as reverseGeocoderApi,
} from './map-api';

const EARTH_RADIUS = 6378.137;

/**
 * 根据关键字搜索地址
 * @param {Object} options 地图搜索参数
 * @returns {Promise}
 * http://lbs.qq.com/qqmap_wx_jssdk/index.html
 */
function searchAddress(options) {
  return getSuggestion(options).catch((err) => {
    // 记录下
    const app = getApp();
    app.logger.requestError({
      message: 'search-address-error',
      detail: err,
    });
    return Promise.reject(err);
  });
}

/**
 * 根据 地址 获取GPS信息
 * @param {Object} options 地址转换参数
 * @returns {Promise}
 * http://lbs.qq.com/qqmap_wx_jssdk/index.html
 */
function geocoder(options) {
  return geocoderApi(options);
}

/**
 * 根据 GPS 获取地址信息
 * @param {Object} options 地址转换参数
 * @returns {Promise}
 * http://lbs.qq.com/qqmap_wx_jssdk/index.html
 */
function reverseGeocoder(options) {
  return reverseGeocoderApi(options);
}
/**
 * 定位
 * @param {*} success
 * @param {*} fail
 * @param {*} cb 可能需要进行第二次重新利用 success，fail 参数的回调
 * @returns {success} 第一个参数百度坐标，第二个参数高德坐标
 */
function tryLocation(success, fail, cb) {
  wx.getLocation({
    type: 'gcj02',
    success: ({ latitude, longitude }) => {
      const { lng, lat } = gcjToBaidu(longitude, latitude);
      success({ lng, lat }, { latitude, longitude });
    },
    fail: cb ? cb(success, fail) : fail,
  });
}

function rad(d) {
  return (d * Math.PI) / 180;
}

/**
 * 计算两个坐标距离
 * @param {LatLng} a
 * @param {LatLng} b
 */
function calcDistance(a, b) {
  const radLat1 = rad(+a.lat);
  const radLat2 = rad(+b.lat);
  const rLat = radLat1 - radLat2;
  const rLng = rad(+a.lng) - rad(+b.lng);

  /* eslint-disable */
  let s =
    2 *
    Math.asin(
      Math.sqrt(
        Math.pow(Math.sin(rLat / 2), 2) +
          Math.cos(radLat1) *
            Math.cos(radLat2) *
            Math.pow(Math.sin(rLng / 2), 2)
      )
    );
  s *= EARTH_RADIUS;
  s = Math.round(s * 10000) / 10000;
  return s;
}

/**
 * 格式化距离
 * @param {Number} distance
 */
function parseDistance(distance) {
  if (!distance) return 0;
  distance = +distance || 0;
  if (distance > 10000) {
    distance = '> 10km';
  } else if (distance > 1000) {
    distance = (distance / 1000).toFixed(2) + 'km';
  } else if (distance < 100) {
    distance = '< 100m';
  } else {
    distance = distance.toFixed(2) + 'm';
  }
  return distance;
}

export {
  tryLocation,
  parseDistance,
  calcDistance,
  baiduToGcj,
  gcjToBaidu,
  searchAddress,
  geocoder,
  reverseGeocoder,
};
