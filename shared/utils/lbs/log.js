function getCurrentRoute() {
  const pages = getCurrentPages() || [];

  return pages.length ? pages[pages.length - 1].route : '';
}

export function logReverseGeocoder(key) {
  const app = getApp();

  app && app.logger.log({
    et: 'custom', // 事件类型
    ei: 'reverse_geocoder', // 事件标识
    en: '逆地址解析', // 事件名称
    params: {
      route: getCurrentRoute(),
      key
    },
    si: app.getKdtId()
  });
}

export function logGetSuggestion(key) {
  const app = getApp();

  app && app.logger.log({
    et: 'custom', // 事件类型
    ei: 'get_suggestion', // 事件标识
    en: '关键词输入提示', // 事件名称
    params: {
      route: getCurrentRoute(),
      key
    },
    si: app.getKdtId()
  });
}
