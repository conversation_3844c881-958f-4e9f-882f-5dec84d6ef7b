import tee from '@youzan/tee';

export const CUSTOMER_LATITUDE_CACHE = '_customer_latitude_cache';

/**
 * 获取经纬度信息，从进店逻辑调起的LBS
 * 使用的业务方 scrm
 * 使用场景 客户归属店铺 LBS 规则
 */
export const getStorageLBS = () => {
  let cache = {};
  try {
    cache = tee.$native.getStorageSync(CUSTOMER_LATITUDE_CACHE) || {};
  } catch (error) {}

  if (cache?.lat && cache?.lng) {
    return {
      lat: String(cache.lat),
      lng: String(cache.lng),
    };
  }

  return {};
};
