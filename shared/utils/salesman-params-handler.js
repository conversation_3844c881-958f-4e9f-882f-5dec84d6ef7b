import {
  getFromParams as getFromParamsHandler,
  getSalesmanParamsObject as getSalesmanParamsObjectHandler,
  addSalesmanParams as addSalesmanParamsHandler
} from '@youzan/salesman-params-handler';

export { parseTrackData, stringifyTrackData } from '@youzan/salesman-params-handler';

let app = getApp();

const getAppKdtId = () => {
  app = app || getApp();
  const kdtId = app.getKdtId();
  return kdtId;
};

/**
 * 获得 form_params 参数，返回字符串，如：sl~IR5yIq!online_kdt_id~123456
 */
export const getFromParams = ({ sl, sls, oldFromParamsData = {} }) => {
  const kdtId = getAppKdtId();
  return getFromParamsHandler({ sl, sls, oldFromParamsData, kdtId });
};

/**
 * 获取销售员参数的对象
 * 返回值举例：{ sl: 'IR5yIq', from_params: 'sl~IR5yIq!online_kdt_id~123456' }
 */
export const getSalesmanParamsObject = ({ sl, sls, oldFromParamsData }) => {
  const kdtId = getAppKdtId();
  return getSalesmanParamsObjectHandler({ sl, sls, oldFromParamsData, kdtId });
};

/**
 * 给分享链接添加销售员的参数，form_params(下单绑定参数)，sl(销售员信息)，sls(知识付费)
 * 传入原 url，会直接返回添加上销售员参数的销售员分享链接
 */
export function addSalesmanParams({ url, sl, sls }) {
  const kdtId = getAppKdtId();
  return addSalesmanParamsHandler({ url, sl, sls, kdtId });
}
