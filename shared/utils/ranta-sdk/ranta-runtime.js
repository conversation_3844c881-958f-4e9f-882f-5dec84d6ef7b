import {
  MiniAppRuntimeV2,
  parseLocalConfigV2,
  parseLocalEcloudBizConfigV2,
} from '@youzan-open/ranta/lib/miniapp/index.miniapp';
/* #ifdef BUILD_ENV=youzanyun */
import { genRantaYunExtension } from '@youzan/ranta-mp-ext-adapter';
/* #endif */
export class RantaRuntime extends MiniAppRuntimeV2 {
  constructor(options) {
    const { bizConfig, ecloudBizConfig, extensions: localExtensions } = options;

    const nextBizConfig = { ...bizConfig };
    const nextExtensions = [...localExtensions];

    /* #ifdef BUILD_ENV=youzanyun */
    const { ext, module, extMapping } =
      parseLocalEcloudBizConfigV2(ecloudBizConfig);
    const yunExt = genRantaYunExtension(extMapping);
    nextBizConfig.modules = [
      ...nextBizConfig.modules,
      { id: module.moduleId, extensionId: module.extensionId },
    ];
    nextExtensions.push({ config: ext, extension: yunExt });
    /* #endif */

    const { modules, extensions, containers, builtinBundle } =
      parseLocalConfigV2(nextBizConfig, nextExtensions);

    super({
      config: {
        modules,
        extensions,
        containers,
      },
      builtinBundle,
    });
  }
}
