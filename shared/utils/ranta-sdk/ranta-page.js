import { VanxPage } from 'pages/common/wsc-page/index';
/* #ifdef BUILD_ENV=youzanyun */
import yunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { getYunSdk } from '@youzan/weapp-ecloud-sdk';
/* #endif */
import { RANTA_SDK } from './constants';
import { RantaRuntime } from './ranta-runtime';

const createDesignPageConfig = (yunDesign) => {
  return {
    data: {
      design: [],
    },
    onLoad() {
      const framework = this[RANTA_SDK];

      let defaultDesign = [];
      const keepDesign = [];
      framework.containers.forEach((item) => {
        if (item.blockName) {
          defaultDesign.push({ type: item.blockName });
        } else {
          keepDesign.push({ type: item.moduleId });
        }
      });

      // 云定制下外部传入design之后就替换ranta的containers
      if (yunDesign) {
        const { design = [] } = yunDesign;
        const index = defaultDesign.findIndex((v) => v.type === 'config');
        defaultDesign = index === 0 ? design.slice(1) : design;
      }

      // keep的ext放在design顶部
      const design = [...keepDesign, ...defaultDesign];

      this.setData({
        design,
      });
    },
  };
};

const createRantaYunPage = (
  bizConfig,
  extensions,
  ecloudBizConfig,
  ecloudSpaceBiz
) => {
  if (ECLOUD_MODE) {
    return {
      ...yunPageConfig,
      onLoad() {
        const framework = new RantaRuntime({
          bizConfig,
          extensions,
          ecloudBizConfig,
        });

        this[RANTA_SDK] = framework;

        const sdk = getYunSdk();
        try {
          ecloudSpaceBiz && ecloudSpaceBiz(sdk);
        } catch (e) {}
      },
    };
  }

  // 标品走这个逻辑
  return {
    onLoad() {
      const framework = new RantaRuntime({
        bizConfig,
        extensions,
        ecloudBizConfig,
      });

      this[RANTA_SDK] = framework;
    },
  };
};

export const createRantaPage = (ecloudBizConfig, ecloudSpaceBiz, yunDesign) => {
  return function RantaPage({ bizConfig, extensions }, ...rest) {
    return VanxPage(
      createRantaYunPage(
        bizConfig,
        extensions,
        ecloudBizConfig,
        ecloudSpaceBiz
      ),
      createDesignPageConfig(yunDesign),
      ...rest
    );
  };
};

export const RantaPage = createRantaPage({}, () => {});
