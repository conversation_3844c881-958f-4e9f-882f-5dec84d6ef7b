import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { getCurrentPage } from 'shared/common/base/wsc-component';
import { RANTA_SDK } from './constants';

const ctxBehavior = Behavior({
  properties: {
    rmId: String,
  },

  attached() {
    const page = getCurrentPage(this);
    const framework = page[RANTA_SDK];
    if (!framework) {
      const app = getApp();
      app.logger.appError({
        name: 'ranta_nofound_framework',
        message: 'Ranta 未找到 framework',
      });
    } else {
      this.ctx = framework.getCtx(this.data.rmId);
    }
  },
});

export function RantaWidget(res) {
  res.behaviors = res.behaviors || [];
  res.behaviors.push(ctxBehavior);
  return VanxComponent(res);
}
