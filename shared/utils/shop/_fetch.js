import { carmen } from 'shared/utils/request';

/**
 * 目前后端提供的商品页店铺信息需通过下列两个接口请求获取，影响性能，都先延迟1s后再加载，保证页面先展示（暂时过度方案）。
 * -  weapp.wsc.shop.cert/1.0.0/get
 * -  weapp.wsc.shopq.cert/1.0.0/get
 */


/**
 * 获取店铺认证信息
 */
export function fetchShopCert() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const shopCert = [];
      carmen({
        api: 'weapp.wsc.shop.cert/1.0.0/get',
        success: (res) => {
          if ([2, 4].indexOf(+res.group_cert_type) !== -1) {
            shopCert.push(['企业认证', '', '个人认证'][res.group_cert_type - 2]);
          }

          res.team_certification == 1 && shopCert.push('店铺认证');
          res.is_secured_transactions == 1 && shopCert.push('担保交易');
          res.team_physical == 1 && shopCert.push('线下门店');

          resolve({
            certs: shopCert
          });
        },
        fail: reject
      });
    }, 1000);
  });
}

/**
 * 获取官字店标识信息
 */
export function fetchShopOfficial() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      carmen({
        api: 'youzan.pay.cert.teamcertification/1.0.0/get',
        success: (res) => {
          resolve({
            type: +res.shop_cert_type,
            status: +res.shop_cert_status
          });
        },
        fail: reject
      });
    }, 1000);
  });
}
