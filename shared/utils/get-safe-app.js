import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import db from 'shared/utils/db';
import { carmen, node as request } from 'shared/utils/request';
import injectStorage from '@youzan/weapp-utils/lib/storage';

const _f = () => {};

const store = {};

// 给插件用的假的app
const mockApp = {
  globalData: {
    // 更新店铺数据
    shopInfo: {
      isMultiStore: false
    }
  },

  db,

  carmen,
  request,

  logger: {
    requestError: _f,
    onError: _f,
    getGlobal: _f,
    appError: _f
  },

  isSwitchTab() {
    return Promise.resolve(false);
  },

  updateYouzanUserInfo() {},

  // @@ offlineId
  getOfflineId() {
    const { shopInfo = {} } = store;
    return shopInfo.offlineId || 0;
  },

  // @@ kdtId
  getKdtId() {
    return store.kdtId || 0;
  },

  // @@ fansType
  getFansType() {
    return '';
  },

  // @@ sessionId
  getSessionId() {
    const { token = {} } = store;
    return token.sessionId || '';
  },

  /**
   * 获取店铺配置信息
   * @@@@
   */
  getShopConfigData() {
    return new Promise((resolve) => {
      // 配置一些默认的显示
      resolve({
        show_buy_btn: true,
        hide_shopping_cart: false
      });
    });
  },

  getShopInfo() {
    return Promise.resolve({});
  },

  /**
   * 获取店铺 im 信息
   * @@@@
   */
  getImData() {
    return new Promise((resolve) => {
      resolve({});
    });
  },

  getUserInfo() {},

  // 积分名称
  getPointsName() {
    return Promise.resolve({
      pointsName: '积分'
    });
  },

  getPoints() {
    return request({
      path: 'wscump/integral/user_points.json'
    });
  }
};

mockApp.storage = injectStorage();

export function updateToken(token = {}) {
  token = mapKeysCase.toCamelCase(token);
  store.token = {
    sessionId: token.sessionId || '',
    accessToken: token.accessToken || ''
  };
}

export function setKdtId(kdtId = 0) {
  store.kdtId = kdtId;
}

export function setShopInfo(shopData = {}) {
  const { shopInfo = {} } = store;
  store.shopInfo = {
    ...shopInfo,
    ...shopData
  };
}

export default function () {
  if (getApp) {
    return getApp();
  }

  return mockApp;
}
