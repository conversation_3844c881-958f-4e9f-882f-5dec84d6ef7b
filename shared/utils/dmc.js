import { getPlugins } from '@youzan/ranta-helper-tee';

const { dmc } = getPlugins();

export const DOMAIN_TYPE = {
  REQUEST: 'request',
  DOWNLOAD: 'download',
  UPLOAD: 'upload',
  WS_REQUEST: 'wsRequest',
  WEBVIEW: 'webview',
};

export const getDomainByScene = (scene) => {
  return dmc.domain.readDomain(scene).then((domainItem) => {
    return `https://${domainItem.domain}`;
  });
};

export const transformDomain = (src, group = DOMAIN_TYPE.WEBVIEW) => {
  return dmc.transformDomain(src, {
    priorityCache: true,
    group,
  });
};
