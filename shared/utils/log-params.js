import get from '@youzan/weapp-utils/lib/get';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';

import { makeRandomString } from '@youzan/weapp-utils/lib/str';

export const mapKeys = (object, iteratee) => {
  object = Object(object);
  const result = {};

  Object.keys(object).forEach((key) => {
    const value = object[key];
    result[iteratee(value, key, object)] = value;
  });
  return result;
};

/**
 * pick deep
 * @param { Object } object { goods_components: {size: '0'}, type: 0}
 * @param { Array } paths ['goods_components.size', 'type']
 * @return { Object } { size: '0', type: '0'}
 */
export const pickDeep = (object, paths) => {
  let index = -1;
  const length = paths.length;
  const result = {};

  while (++index < length) {
    const path = paths[index];
    const key = path.split('.').pop();
    const value = get(object, path);
    result[key] = value;
  }
  return result;
};

export const extraPrefixName = 'components_style_';

export const loggerRandomKey = makeRandomString(8);

/**
 * 组件 type 转换成 埋点组件 type
 */
export const loggerNameMap = {
  ump_seckill: 'seckill',
  ump_limitdiscount: 'limitdiscount',
  goods_recommend: 'recommend',
  cube_v3: 'cube',
  image_text_nav: 'image_nav',
  coupon: 'promocard',
};

/**
 * 埋点组件所需额外参数
 */
export const componentsPickParams = {
  goods: 'size', // 商品 列表样式
  tag_list_top: ['size'], // 顶部商品分组 列表样式
  tag_list_left: ['size'], // 左侧商品分组 列表样式
  recommend: ['goods_components.size'], // 个性化推荐 列表样式
  seckill: ['size'], // 秒杀 列表样式
  groupon: ['size', 'groupon_type'], // 拼团 列表样式
  limitdiscount: ['layout'], // 限时折扣 列表样式
  shop_banner_weapp: ['store_info_style'], // 店铺信息 样式
  image_ad: data => { // 图片广告 样式
    const extraParams = {};
    const showMethod = get(data, 'show_method', 0);
    extraParams[`${extraPrefixName}show_method`] = showMethod;
    extraParams[`${extraPrefixName}size`] = get(data, 'size', 0);

    // 判断是否是热区
    if (+showMethod === 7) {
      const hasHotarea = get(data, 'sub_entry[0].hot_areas', false);
      extraParams[`${extraPrefixName}is_hotarea`] = hasHotarea ? 1 : 0;
    }

    return extraParams;
  },
  image_nav: ['show_method', 'slide_setting'], // 图文导航 样式\模版
  audio: ['style'], // 语音 样式
  points_goods: ['size'], // 积分商城 样式
  period_buy: ['size'], // 周期购 样式
  goods_new: ['layout'], // 新版商品 样式
  bargain: ['size'], // 砍价 样式
  social_fans: (data) => {
    // 1 群码 2 个人码 3关注公众号
    let type = '';
    const { scene, subType } = data || {};
    if (+subType === 2) {
      type = 3;
    } else if (scene === 'WeiXin') {
      type = 2;
    } else if (scene === 'WeiXinGroup') {
      type = 1;
    }
    return {
      sub_type: type,
    };
  }, // 社区涨粉
};

/**
 *
 * @param type
 * @returns {*}
 */
export const getLoggerType = type => {
  return loggerNameMap[type] || type;
};

/**
 * 获取组件额外需要上报的字段
 * @param cd
 */
export const getLoggerExtraParams = cd => {
  const type = getLoggerType(cd.type);

  const pickItems = componentsPickParams[type];
  const pickType = Object.prototype.toString.call(pickItems);

  let extraParams = {};
  switch (pickType) {
    case '[object String]':
      extraParams = mapKeys(pickDeep(cd, [pickItems]), (value, key) => extraPrefixName + key);
      break;
    case '[object Array]':
      extraParams = mapKeys(pickDeep(cd, pickItems), (value, key) => extraPrefixName + key);
      break;
    case '[object Function]':
      extraParams = pickItems(cd);
      break;
    default:
      extraParams = {};
      break;
  }

  return extraParams;
};

/**
 * 获取组件曝光所需数据
 * @param cd
 * @param index
 * @param number
 * @returns {{item_type: string, component_index: *, component: *, banner_id: string}}
 */
export const getLoggerParams = (cd, index, number, loggerSpm) => {
  // 埋点数据下划线
  const component = mapKeysCase.toSnakeCase(cd);

  const type = getLoggerType(component.type || component.item_type);

  // banner id
  // const banner_id = `${loggerSpm.logType}.${loggerSpm.logId}~${type}~0~${number}`;
  const bannerId = `${loggerSpm}~${type}.${index + 1}~0~${number}`;

  // 组件额外样式埋点数据
  const extraParams = getLoggerExtraParams(component);

  const params = {
    item_type: 'component', // 默认写死
    component: type, // 组件类型
    banner_id: bannerId,
    ...extraParams
  };

  return params;
};
