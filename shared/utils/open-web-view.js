import args from '@youzan/weapp-utils/lib/args';

const defaultOptions = {
  title: '',
  domain: 'https://h5.youzan.com',
  webViewPage: '/pages/common/webview-page/index',
  method: 'navigateTo'
};

const hasDomain = url => url.indexOf('http') === 0 || url.indexOf('//') === 0;

export function prefixDomain(url, domain = defaultOptions.domain) {
  if (domain && !hasDomain(url)) {
    return domain + url;
  }
  // 小程序的服务器域名只支持 https
  if (url.startsWith('http://')) {
    url = url.replace('http://', 'https://');
  }
  return url;
}

/**
 * 打开 WebView 页面
 * @param {string} url
 * @param {object} options
 * ```
 *  openWebView('/path/page', {
 *    title:'页面标题',             // 页面标题
 *    domain:'cashier.youzan.com', // 域名，默认为 h5.youzan.com
 *    query: {                     // queryString 参数
 *      name: 'JD'
 *    }
 *  });
 * ```
 */
export default function openWebView(url, options) {
  options = {
    ...defaultOptions,
    ...options
  };

  url = prefixDomain(url, options.domain)

  if (options.query) {
    url = args.add(url, options.query, true);
  }

  const query = {
    src: url,
    title: options.title
  };

  wx[options.method]({
    url: args.add(options.webViewPage, query, true)
  });
}
