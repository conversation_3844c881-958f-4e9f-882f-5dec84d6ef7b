import event from '@youzan/weapp-utils/lib/event';
import getApp from './get-safe-app';

function getCurrentRoute() {
  const pages = getCurrentPages() || [];

  return pages.length ? pages[pages.length - 1].route : '';
}

export default {
  // 来源标识
  _isAppEvent: true,

  /**
   * 注册事件
   * @param name     事件名
   * @param callback 回调
   * @param context  上下文
   */
  on(name, callback, context) {
    const app = getApp();

    context = context || this || {};
    context._route = getCurrentRoute();

    event.on.apply(app, [name, callback, context]);
  },

  once(name, callback, context) {
    const app = getApp();

    context = context || this || {};
    context._route = getCurrentRoute();

    event.once.apply(app, [name, callback, context]);
  },

  off(name, callback, context) {
    event.off.apply(getApp(), [name, callback, context || this || {}]);
  },

  trigger(...args) {
    event.trigger.apply(getApp(), args);
  }
};
