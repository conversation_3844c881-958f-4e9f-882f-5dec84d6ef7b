import getApp from 'shared/utils/get-safe-app';

const app = getApp();

/**
 * @param {string} shareCmpt 分享来源
 * 微信右上角分享按钮：native_wechat
 * 邀请好友[成团|助力|xx 自定义按钮唤起原生分享]：native_custom
 * 生成邀请海报二维码：poster
 * 长按复制链接：copylink
 */

export const getShareParams = shareCmpt => {
  const logGlobalInfo = app.logger.getGlobal() || {};
  const userInfo = logGlobalInfo.user || {};

  const params = {
    is_share: 1,
    share_cmpt: shareCmpt,
  };

  if (userInfo.uuid) {
    params.from_uuid = userInfo.uuid || '';
  }

  return params;
};
