/**
 * 根据源、路径及查询参数生成URL地址
 *
 * url({
 *   origin: 'uic',
 *   pathname: '/sso/login'
 * });
 * // => https://uic.koudaitong.com/sso/login
 *
 * url({
 *   origin: 'carmen',
 *   pathname: '/gw/oauththentry/weapp.wsc.shop/1.0.0/get',
 *   query: {
 *      access_token: '573501bf13573b318fdcf70ad91db13b',
 *      kdt_id: '63077'
 *    }
 * })
 * // => https://carmen.koudaitong.com/gw/oauthentry/gw/oauththentry/weapp.wsc.shop/1.0.0/get?access_token=573501bf13573b318fdcf70ad91db13b&kdt_id=63077
 *
 */
import args from '@youzan/weapp-utils/lib/args';
import extend from '@youzan/weapp-utils/lib/extend';
import { getDomainByScene } from 'shared/utils/dmc';

const ORIGIN_MAP = {
  cashier: 'https://cashier.youzan.com',
  uic: 'https://uic.youzan.com',
  carmen: 'https://open.youzan.com',
  h5: 'https://h5.youzan.com',
  h5m: 'https://h5.m.youzan.com',
  trade: 'https://trade.youzan.com',
  qiniu: 'https://img.yzcdn.cn',
  money: 'https://money.youzan.com',
  guang: 'https://g.youzan.com',
  baymax: 'https://open.youzan.com/bifrost',
};

const defaultOptions = {
  // 源
  origin: 'carmen',
  // 路径
  pathname: '',
  // 查询参数
  query: {},
};

/**
 * url 拼接
 * @remarks 此方法已改为异步，使用时请注意
 * @param {IReqOptions} options
 * interface IReqOptions {
 *  pathname: string;
 *  origin: string;
 *  query: Record<string, any>;
 * }
 * @returns {Promise<string>} 完整url
 */
function url(options) {
  options = extend({}, defaultOptions, options);

  const hasSlash = (options.pathname || '').startsWith('/');

  return getDomainByScene(options.origin)
    .then((domain) => domain || ORIGIN_MAP[options.origin])
    .catch(() => ORIGIN_MAP[options.origin])
    .then((domain) => {
      const url = domain + (hasSlash ? '' : '/') + options.pathname;
      return args.add(url, options.query);
    });
}

const isHttp = (url) => /^http:\/\//.test(url);

export const toHttps = (url) => {
  if (isHttp(url)) {
    url = url.replace('http://', 'https://');
  }

  return url;
};

export const getCurrentPage = () => {
  const pages = getCurrentPages() || [];
  return pages[pages.length - 1] || {};
};

export const getCurrentRoute = (params = {}) => {
  const { withSlash, withQuery } = params;
  const { route, options = {} } = getCurrentPage();

  if (!route) {
    return '';
  }

  let url = route;

  if (withSlash) {
    url = `/${url}`;
  }

  if (withQuery) {
    url = args.add(url, options);
  }

  return url;
};

export default url;
