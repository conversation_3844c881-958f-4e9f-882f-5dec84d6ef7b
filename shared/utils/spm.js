/**
 * spm = 页面类型+页面标识
 * 业务方需要改成自己项目中的路径和对应类型
 */

/**
 * pageTypes已移至src/ranta.app.config.js下plugin.logger.spm中
 * 请在新文件中更新
 */

// const pageTypes = {
//   'packages/home/<USER>/index': 'f',
//   'packages/home/<USER>/one': 'f',
//   'packages/home/<USER>/two': 'f',
//   'packages/home/<USER>/three': 'f',
//   'packages/home/<USER>/index': 'f',
//   'packages/shop/goods/group/index': 'ag',
//   'packages/shop/goods/all/index': 'ag',
//   'packages/shop/goods/tag-list/index': 'ag',
//   'packages/shop/goods/search/index': 'search',
// 'packages/usercenter/dashboard/index': 'uc',
//   // 商品详情
//   'pages/goods/detail/index': 'g',
//   // 拼团商详
//   'packages/goods/groupon/index': 'g',
//   // 砍价商详
//   'packages/goods/help-cut/index': 'g',
//   // 抽奖团商详
//   'packages/goods/lucky-draw-group/index': 'g',
//   // 团购返现商详
//   'packages/goods/tuan/index': 'g',
//   // 赠品商详
//   'packages/goods/present/index': 'g',
//   // 积分兑换商详
//   'packages/goods/points/index': 'g',
//   // 秒杀
//   'packages/goods/seckill/index': 'seckg',
//   // 购物车
//   'packages/goods/cart/index': 'cart',
//   'packages/trade/cart-v1/index': 'cart',
//   'packages/trade/cart/index': 'cart',
//   'packages/trade/order/result/index': 'od',
//   'packages/trade/order/list/index': 'ol',
//   'packages/trade/order/express/index': 'traceDetail',
//   'packages/trade/order/safe/info/index': 'refundDetail',
//   'packages/order/index': 'trade',
//   'packages/order/address-list/index': 'addresslist',
//   'packages/order/address-edit/index': 'editaddress',
//   'packages/order/address-map/index': 'selectaddress',
//   'packages/order/paid/index': 'paySuccess',
//   'packages/order/paid-v1/index': 'paySuccess',
//   'packages/paid/pay-result/wait/index': 'paySuccess',
//   'packages/paid/pay-result/success/index': 'paySuccess',
//   'packages/paid/pay-result/oversale/index': 'paySuccess',
//   'packages/paid/pay-result/auction/index': 'paySuccess',
//   'pages-youzan/dashboard/home/<USER>': 'yzh',
//   'pages-youzan/usercenter/dashboard/index': 'uc',
//   'pages-youzan/mars/index': 'yzc',
//   'pages-youzan/shop/new/index': 'yzoc',
//   'pages-youzan/shop/create-user/step-1/index': 'yzom',
//   'pages-youzan/shop/create-user/step-2/index': 'yzov',
//   'pages-youzan/shop/create-user/step-3/index': 'yzop',
//   'pages-youzan/shop/status/index': 'yzo',
//   'packages/order/share-page/index': 'shareorder',

//   // 知识付费
//   'packages/paidcontent/list/index?type=content': 'pcat',
//   'packages/paidcontent/list/index?type=column': 'pcam',
//   'packages/paidcontent/list/index?type=live': 'pcal',
//   'packages/paidcontent/list/index': 'scpc',
//   'packages/paidcontent/content/index': 'pct',
//   'packages/paidcontent/column/index': 'pcm',
//   'packages/paidcontent/live/index': 'pcl',
//   // 群打卡
//   'packages/new-punch/introduction/index': 'pci',
//   'packages/new-punch/task/index': 'pcc',
//   // 砍价
//   'packages/ump/bargain-purchase/home/<USER>': 'cutd',
//   'packages/ump/helpcut/index': 'cutd',
//   // 抽奖拼团
//   'packages/collage/lottery/detail/index': 'spld',
//   'packages/collage/lottery/result/index': 'splr',
//   // 拼团详情
//   'packages/collage/groupon/detail/index': 'groupon',
//   // 会员卡
//   'packages/card/detail/index': 'g',
//   // 返现列表
//   'packages/user/cashback/list/index': 'cashback',
//   // 返现详情
//   'packages/user/cashback/detail/index': 'cashbackDetail',
//   // 好友瓜分券
//   'packages/ump/carve-coupon/index': 'carveCoupon',
//   // 助力好友瓜分券
//   'packages/ump/split-coupon-friend/index': 'sharecoupon',
//   // 日历签到
//   'packages/shop/ump/sign-in/index': 'sign',
//   // 幸运大抽奖
//   'packages/ump/new-lottery/casino/index': 'verb',
//   // 详情页
//   'packages/shop/shopnote/detail/index': 'shopnote',
//   'packages/short-video/index': 'shopnote',
//   // 零售导购
//   'packages/retail/sales/bi-card/index': 'retailvc',
//   'packages/retail/chat/index': 'retailinfo',
//   // 免费等级
//   'packages/shop/levelcenter/free/index': 'f',
//   // 付费等级
//   'packages/levelcenter/plus/index': 'f',
//   // 零售
//   // - 24小时货架首页
//   'pages-retail/home-shelf/index': 'retailshelfhome',
//   // - 24小时货架点单页
//   'packages/retail/goods-shelf/index': 'retailshelfgoods',
//   // - 24小时货架订单确认页
//   'packages/retail/shelf-confirm/index': 'retailshelforderconfirm',
//   // 周边好物推荐落地页
//   'packages/statcenter/cps-goods-list/index': 'cpsShopRecommend',
// };

const spmUtils = () => {
  return {
    initSpm() {
      // 清空访问历史
      const logger = getApp().logger;
      logger && logger.spmHelper.initSpm();
    },

    // ${spm.page_type}.${spm.page_id}
    getPageSpmTypeId() {
      const logger = getApp().logger;
      return logger && logger.spmHelper.gettCurrentPageSpm();
    },

    setCurrentSpm(path, pid, type, pages = []) {
      const logger = getApp().logger;
      return logger && logger.spmHelper.setCurrentSpm(path, pid, type, pages);
    },

    getSpm() {
      const logger = getApp().logger;
      return logger && logger.spmHelper.getSpm();
    },

    removePageSpm() {
      const logger = getApp().logger;
      return logger && logger.spmHelper.removePageSpm();
    },
  };
};

function getSpmInst() {
  if (wx.spm) {
    return wx.spm;
  }
  wx.spm = spmUtils();
  return wx.spm;
}

const spm = getSpmInst();

export default spm;
