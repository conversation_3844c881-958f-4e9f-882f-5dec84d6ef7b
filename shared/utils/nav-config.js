/**
 * @file <EMAIL>
 * 自定义顶部导航高度
 */

import { getCurrentPage } from 'shared/utils/url';
import { judgeEmbedded } from '@/helpers/app';

import { SystemInfo } from './browser/system-info';
import compareVersion from './compare-version';

const pkgs = 'packages';
const pg = 'pages';
const homeTab = 'home/tab';
const index = 'index';
const pathMap = {
  homeTabOne: `${homeTab}/one`,
  homeTabTwo: `${homeTab}/two`,
  homeTabThree: `${homeTab}/three`,
  shopLevelCenterPkgs: `${pkgs}/shop/levelcenter`,
  homeDashboard: `home/dashboard/${index}`,
  useCenterDashboard: `usercenter/dashboard/${index}`,
  goodsDetail: `goods/detail/${index}`,
  homeFeature: `home/feature/${index}`,
};

const HOME_ROUTE = `${pg}/${pathMap.homeDashboard}`;
const GOODS_DETAIL_TOUTE = `${pg}/${pathMap.goodsDetail}`;
const KQ_GOODS_DETAIL = `${pkgs}/${pathMap.goodsDetail}`;
const RETAIL_HOME = 'pages-retail/home-shelf/index';

const supportRouteMap = {
  [`${pg}/${pathMap.homeFeature}`]: 0,
  [`${pkgs}/${pathMap.homeTabThree}`]: 0,
  [`${pg}/${pathMap.homeTabThree}`]: 0,
  [`${pkgs}/${pathMap.homeTabTwo}`]: 0,
  [`${pg}/${pathMap.homeTabTwo}`]: 0,
  [`${pkgs}/${pathMap.homeTabOne}`]: 0,
  [`${pg}/${pathMap.homeTabOne}`]: 0,
  [`${pkgs}/showcase-template/${index}`]: 5,
  [GOODS_DETAIL_TOUTE]: 2,
  [`${pkgs}/${pathMap.goodsDetail}`]: 2,
  [`${pg}/goods/`]: 2,
  [`${pkgs}/goods/`]: 2,
  [HOME_ROUTE]: 5,
  [`${pg}/tab/one/${index}`]: 0,
  [`${pg}/tab/two/${index}`]: 0,
  [`${pg}/tab/three/${index}`]: 0,
  [`${pkgs}/ext-marketing-page/${index}`]: 0,
  [`${pkgs}/shop/shopnote/detail/${index}`]: 6,
  [`${pg}/${pathMap.useCenterDashboard}`]: 7,
  [`${pkgs}/${pathMap.useCenterDashboard}`]: 7,
  'pages-retail/usercenter/dashboard-v2/index': 7,
};

const SPECIAL_ROUTE_ARR = [
  `${pathMap.shopLevelCenterPkgs}/free/${index}`,
  `${pathMap.shopLevelCenterPkgs}/plus/${index}`,
];

const iPhone6Rect = {
  bottom: 58,
  height: 32,
  left: 278,
  right: 365,
  top: 26,
  width: 87,
  canUseNav: true,
};

const cache = {
  // 是否初始化，如果已初始化，则置为true
  inited: false,
  rectInfo: {},
  // 当前小程序是否进过半屏
  enterEmbedded: false,
};

const updateRectInfoSync = () => {
  const { statusBarHeight = 20, SDKVersion, isAndroid } = SystemInfo;
  let defaultTop = statusBarHeight + (isAndroid ? 48 : 44);
  if (judgeEmbedded()) defaultTop = 6;

  let rect;
  try {
    rect = wx.getMenuButtonBoundingClientRect();
  } catch (error) {
    console.log('获取胶囊信息错误', error);
  }
  if (!rect) {
    rect = Object.create(null);
  }
  if (SDKVersion) {
    rect.canUseNav = compareVersion('2.5.2', SDKVersion) < 1;
  }
  rect.top = rect.top || defaultTop;

  cache.rectInfo = rect;
  cache.inited = true;

  return rect;
};

export const needUpdateRect = () => {
  const isEmbedded = judgeEmbedded();
  // 如果打开过半屏，并且当前是非半屏打开态，则要更新rect
  if (cache.enterEmbedded && !isEmbedded) {
    cache.enterEmbedded = false;
    updateRectInfoSync();
    return;
  }
  // 如果未打开过半屏，且当前是半屏打开态，则要更新rect
  if (!cache.enterEmbedded && isEmbedded) {
    cache.enterEmbedded = true;
    updateRectInfoSync();
  }
};

const MenuButtonBoundingClientRect = Object.create(null);

Object.keys(iPhone6Rect).forEach((key) => {
  Object.defineProperty(MenuButtonBoundingClientRect, key, {
    get() {
      if (!cache.inited || (!cache.rectInfo[key] && key !== 'canUseNav')) {
        const rectInfo = updateRectInfoSync();
        const value = rectInfo[key];
        if (!value && value !== false) {
          setTimeout(updateRectInfoSync, 300);
          return iPhone6Rect[key];
        }
        return value;
      }
      return cache.rectInfo[key];
    },
  });
});

const getHeight = (useCache = true) => {
  const {
    top,
    height: menuButtonHeight,
    canUseNav,
  } = MenuButtonBoundingClientRect;
  const systemInfo = useCache ? SystemInfo : wx.getSystemInfoSync();
  const { statusBarHeight = 20 } = systemInfo;
  const topBarHeight =
    top - statusBarHeight + 6 + menuButtonHeight + statusBarHeight;

  const height = topBarHeight <= 105 ? topBarHeight : 105; // iphone 14pro 整体高度较高 原来的 90不适用了
  return canUseNav ? height : 0;
};

const isEmpty = (value) => value === null || value === undefined;

const getPaddingTop = (height) => {
  if (!isEmpty(height)) {
    return height - 44;
  }

  const barHeight = wx.getSystemInfoSync()?.statusBarHeight;
  return isEmpty(barHeight) ? 20 : barHeight;
};

const specialRules = [];
const BASE_TYPE = ['home', 'back', 'menu'];

const setSpecialRule = (config = {}) => {
  if (JSON.stringify(config) === '{}') {
    return;
  }

  const { route, type, open = true, systemPath = [], customPath = [] } = config;
  if (!route) {
    console.warn('specialRule route不可为空');
    return;
  }

  if (BASE_TYPE.indexOf(type) === -1) {
    console.warn('specialRule不支持当前type');
    return;
  }

  if (type === 'menu' && systemPath.length === 0 && customPath.length === 0) {
    console.warn('specialRule在type为menu时路径不能为空');
    return;
  }

  const index = specialRules.findIndex((_) => _.type === type);
  if (index > -1) {
    specialRules.push({ ...config, open });
  } else {
    specialRules.splice(index, 1, { ...config, open });
  }
};

/**
 * 获取特殊规则
 * @param {string} route 路径
 */
const getSpecialRule = (route) => {
  const item = specialRules.find((_) => _.route.indexOf(route));
  return item;
};

/**
 * 检查是否是开启了自定义顶部导航的页面
 * 这个开启只判断是否在json里定义了自定义导航
 */
const checkCustomNav = (route = '') => {
  if (!route) {
    const currentPage = getCurrentPage();
    route = currentPage.route;
  }
  return [...Object.keys(supportRouteMap), KQ_GOODS_DETAIL, RETAIL_HOME].some(
    (key) => {
      return route.indexOf(key) > -1;
    }
  );
};

export {
  SPECIAL_ROUTE_ARR,
  MenuButtonBoundingClientRect,
  getHeight,
  checkCustomNav,
  setSpecialRule,
  getSpecialRule,
  getPaddingTop,
  supportRouteMap,
  HOME_ROUTE,
  GOODS_DETAIL_TOUTE,
  KQ_GOODS_DETAIL,
};
