/**
 * 注意：该组件用于维护page级别的倒计时，
 * 需要在page onShow时初始化page实例，
 * onHide时清空所有倒计时
 */
import CountDown from '@youzan/weapp-utils/lib/countdown';
import { format } from '@youzan/weapp-utils/lib/time';

/*
 * countDownMap 时间对象，具体结构如下：
 * timeLeft 需要倒计时的时间
 * startTime 开始倒计时的时间戳
 * change 时间改变时回调函数
 * end 时间结束时回调函数
 */
const countDownMap = {};
const VERY_LARGE_SECOND = 24 * 3600 * 1000;

let recycleFns = [];
let countDownInstance = null;
let pageInstance = null;
let pageCountdownKey;

function ensureCountDownInstance(options) {
  if (countDownInstance) {
    return;
  }

  const { timeout = 1000 } = options;
  countDownInstance = new CountDown(VERY_LARGE_SECOND, {
    timeout,
    onEnd: () => {
      countDownInstance = null;
      ensureCountDownInstance();
    },
    onChange: () => {
      const cdData = {};

      Object.keys(countDownMap).forEach(countdownKey => {
        const {
          timeLeft, startTime, changeFns, endFns
        } = countDownMap[countdownKey] || {};
        const nowTime = Date.now();
        const nowLeftTime = timeLeft - (nowTime - startTime);

        const timeData = format(nowLeftTime);
        if (pageInstance && pageCountdownKey) {
          const strData = timeData.strData;
          delete strData.hundredMilliseconds;
          cdData[`${pageCountdownKey}.${countdownKey}`] = strData;
        }

        // 处理每次时间改动
        if (nowLeftTime > 0) {
          changeFns.forEach(change => {
            change(timeData.data, timeData.strData);
          });
        }

        // 如果时间小于0，就结束这个倒计时
        if (nowLeftTime <= 0) {
          endFns.forEach(end => {
            end();
          });

          stopPageCountdown(countdownKey);
        }
      });

      if (Object.keys(cdData).length) {
        const setData = pageInstance.setYZData || pageInstance.setData;
        setData.call(pageInstance, cdData);
      }
    }
  });
}

function startPageCountdown(timeLeft, options = {}) {
  // 如果没有事件的话，就直接不处理
  const { change, end } = options;

  if (!countDownMap[timeLeft]) {
    countDownMap[timeLeft] = {
      timeLeft,
      startTime: Date.now(),
      changeFns: [],
      endFns: []
    };
  }

  const { changeFns, endFns } = countDownMap[timeLeft];
  if (change) changeFns.push(change);
  if (end) endFns.push(end);

  ensureCountDownInstance(options);

  // 直接返回倒计时清除函数
  const recycleFn = () => {
    if (change) {
      const changeIndex = changeFns.indexOf(change);
      if (changeIndex > -1) changeFns.splice(changeIndex, 1);
    }
    if (end) {
      const endIndex = endFns.indexOf(end);
      if (endIndex > -1) endFns.splice(endIndex, 1);
    }
  };
  recycleFns.push(recycleFn);

  return recycleFn;
}

function stopPageCountdown(key) {
  delete countDownMap[key];

  if (Object.keys(countDownMap).length === 0) {
    countDownInstance && countDownInstance.stop();
    countDownInstance = null;
  }
}

function initPageCountdown(page, key = 'pageCountdown') {
  if (!page) throw new Error('page can no be empty');
  pageInstance = page;
  pageCountdownKey = key;
}

function clearPageCountdown() {
  recycleFns.forEach(fn => fn());
  recycleFns = [];
  Object.keys(countDownMap).forEach(countdownKey => stopPageCountdown(countdownKey));

  // if (pageInstance && pageCountdownKey) {
  //   const setData = pageInstance.setYZData || pageInstance.setData;
  //   setData.call(pageInstance, {
  //     [`${pageCountdownKey}`]: {}
  //   });
  // }
}

export {
  initPageCountdown,
  clearPageCountdown,
  startPageCountdown,
  stopPageCountdown
};
