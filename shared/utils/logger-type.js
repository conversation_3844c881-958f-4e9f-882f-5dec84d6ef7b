import getApp from 'shared/utils/get-safe-app';

const app = getApp();

/**
 * 获取微页面组件曝光格式
 * @param { String }type
 * @param { Array| Object } params
 */
export const getComponentLoggerParams = (type, params) => {
  let loggerMsg = {}
  switch (type) {
    case 'view':
      // 商品曝光、页面曝光
      loggerMsg = {
        et: 'view',
        ei: 'view',
        en: '商品曝光',
        params: {
          view_objs: params
        }
      };
      break;
    case 'open_goods':
      // 打开商品详情
      loggerMsg = {
        et: 'click',
        ei: 'open_goods',
        en: '打开商品详情',
        params,
      };
      break;
    case 'click_buy':
      // 商品点击购买
      loggerMsg = {
        et: 'click',
        ei: 'click_buy',
        en: '点击购买',
        params,
      };
      break;
    case 'click_content':
      loggerMsg = {
        et: 'click',
        ei: 'click_content',
        en: '组件点击',
        params,
      };
      break;
    case 'logger':
      // 自定义
      loggerMsg = params;
      break;
    default:
      break;
  }
  return loggerMsg;
}

/**
 * 埋点上报
 * @param { Object } loggerParams
 */
export const ensureAppLogger = (loggerParams) => {
  app.logger.log(loggerParams);
}
