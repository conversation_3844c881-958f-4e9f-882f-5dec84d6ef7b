// 小程序体积优化 - 移除hummer相关能力
// 技术方案 - https://qima.feishu.cn/docx/doxcn04SH3AfpoQoFpRMQEeEjHd
import {
  mark,
  capture,
  setEnv,
  setExtra,
  setOptions,
} from '@youzan/hummer-tee';

const HummerShell = {
  /** hummer-types */
  init: () => {
    return HummerShell;
  },
  mark,
  setEnv,
  setExtra,
  addTrack: () => {},
  capture,
  captureEvent: () => {},
  captureXhr: () => {},
  addProcessor: () => {
    return 0;
  },
  removeProcessor: () => {},
  addFilter: () => {
    return 0;
  },
  removeFilter: () => {},
  addCaptor: () => {
    return 0;
  },
  removeCaptor: () => {},
  /** hummer-types */

  /** biz usage */
  setPageGroup: () => {},
  setOptions,
  markRendered: () => {},
  /** biz usage */
};

const emitListener = () => {};
const listener = () => {};
const lifeListener = () => {};

export { HummerShell as Hummer };
export { emitListener, listener, lifeListener };
