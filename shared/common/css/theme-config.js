import { colorsType } from '@youzan/shop-core-tee';

export { defaultThemeColors, switchHexToRgb } from '@youzan/shop-core-tee';
// 主题颜色列表
const colors = [
  // 0 $DEFAULT_CONFIG:
  ['#f44', '#f44', '#fff', '#f85', '#fff', '#FF6060', '#FF4444'],

  // 1 $ORANGE_CONF:
  ['#ff5e15', '#ff5e15', '#fff', '#ff9300', '#fff', '#FF8717', '#FF5E16'],

  // 2 $PINK_CONF:
  ['#ff547b', '#ff547b', '#fff', '#ffe6e8', '#ff547b', '#FF73AB', '#FF5179'],

  // 3 $RED_BLACK_CONF:
  ['#f44', '#f44', '#fff', '#555', '#fff', '#FF4F8D', '#FF4444'],

  // 4 $GOLD_CONF:
  ['#fcc600', '#fcc600', '#fff', '#1d262e', '#fff', '#EABE00', '#FFAA00'],

  // 5 $LIGHT_GREEN_CONF:
  ['#65c4aa', '#65c4aa', '#fff', '#ddf2ec', '#65c4aa', '#00D6B9', '#00C291'],

  // 6 $GREEN_BLACK_CONF:
  ['#09bb07', '#09bb07', '#fff', '#333', '#fff', '#56C700', '#09BB07'],

  // 7 $MIDDLE_GREEN_CONF:
  ['#6cbe72', '#6cbe72', '#fff', '#e1f4e3', '#6cbe72', '#77D330', '#5BB161'],

  // 8 $BLUE_CONF:
  ['#4a90e2', '#4990e2', '#fff', '#dbe9f9', '#4990e2', '#0DA6F3', '#0080FF'],

  // 9 $BROWN_CONF:
  ['#c3a769', '#c3a769', '#fff', '#f3eee1', '#c3a769', '#D9BC3D', '#C3A769'],

  // 10 $BLACK_WHIRE_CONF:
  ['#2f2f34', '#2f2f34', '#fff', '#ebecf2', '#2f2f34', '#4a4d52', '#333'],

  // 11 $FANTASH_CONF
  ['#9a875f', '#9a875f', '#fff', '#f6edec', '#9a875f', '#CFB67F', '#B8A06F'],

  // 12 $HAITAO_CONF
  ['#884cff', '#884cff', '#fff', '#efe6ff', '#884cff', '#9673ff', '#884cff'],

  // 13 $RED_LINEAR_THEME
  ['#EE0A24', '#EE0A24', '#fff', '#FFB31E', '#fff', '#FF6034', '#EE0A24'],

  // 14 自定义颜色，这里的颜色不准确，只是用来兜底，防止报错
  ['#f44', '#f44', '#fff', '#f85', '#fff', '#FF6060', '#FF4444'],
];

// 获取主题色
export const themeMainColor = (type) => {
  console.warn('themeMainColor 不支持自定义全店风格，请使用新的全店风格接入');
  if (type >= colors.length) type = 1;
  return colors[type][1];
};

export const themeColorLinear = (type) => {
  console.warn('themeColorLinear 不支持自定义全店风格，请使用新的全店风格接入');
  if (type >= colors.length) type = 1;
  return `linear-gradient(to right, ${colors[type][5]} 0%, ${colors[type][6]} 100%)`;
};

export default colorsType;
