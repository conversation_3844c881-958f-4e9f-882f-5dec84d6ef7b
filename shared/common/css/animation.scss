@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes rotate-reverse {
  0% {
    -webkit-transform: rotate(0deg);
  }
  50% {
    -webkit-transform: rotate(-180deg);
  }
  100% {
    -webkit-transform: rotate(-360deg);
  }
}

.a-rotate {
  animation: rotate 10s linear infinite;
}

.a-rotate-reverse {
  animation: rotate-reverse 10s linear infinite;
}

@keyframes breath {
  0% {
    -webkit-transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.9);
  }
  100% {
    -webkit-transform: scale(1);
  }
}

@keyframes breath-reverse {
  0% {
    -webkit-transform: scale(0.9);
  }
  50% {
    -webkit-transform: scale(1);
  }
  100% {
    -webkit-transform: scale(0.9);
  }
}

.a-breath {
  animation: breath 0.8s linear infinite;
}

.a-breath-reverse {
  animation: breath-reverse 0.8s linear infinite;
}

@keyframes goods-breath {
  0% {
    -webkit-transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.9);
  }
  100% {
    -webkit-transform: scale(1);
  }
}

@keyframes goods-breath-reverse {
  0% {
    -webkit-transform: scale(0.85);
  }
  50% {
    -webkit-transform: scale(1);
  }
  100% {
    -webkit-transform: scale(0.85);
  }
}

.a-goods-breath {
  animation: breath 1.2s linear infinite;
}

.a-goods-breath-reverse {
  animation: breath-reverse 1.2s linear infinite;
}

@keyframes fade-show {
  0% {
    -webkit-transform: scale(0.3);
  }
  60% {
    -webkit-transform: scale(1.05);
  }
  100% {
    -webkit-transform: scale(1);
  }
}

@keyframes fade-hide {
  0% {
    -webkit-transform: scale(1);
  }
  55% {
    -webkit-transform: scale(0.8);
  }
  100% {
    opacity: 0;
  }
}

.a-fade-show {
  animation: fade-show 0.55s linear 1;
}

.a-fade-hide {
  animation: fade-hide 0.65s linear 1;
}
