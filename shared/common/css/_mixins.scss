@mixin border {
  content: '';
  position: absolute;
  box-sizing: border-box;
  width: 200%;
  height: 200%;
  left: 0;
  transform: scale(.5);
  transform-origin: left top;
  -webkit-perspective: 1000;
  -webkit-backface-visibility: hidden;
  pointer-events: none;
}

@mixin border-line($poses:top right bottom left, $border-retina-color: #e5e5e5) {
  @include border();
  @each $pos in $poses {
    border-#{$pos}: 1px solid $border-retina-color;
  }
}

@mixin border-all($border-retina-color) {
  @include border();
  border: 1px solid $border-retina-color;
}

@mixin gradient-banner ($general, $main-bg, $main-text, $vice-bg, $vice-text, $gradient-start, $gradient-end) {
  background-image: linear-gradient(to right, $gradient-start, $gradient-end);
  color: $main-text;
}

@mixin customer-arrow {
  position: absolute;
  top: 50%;
  right: 2px;
  content: " ";
  display: inline-block;
  height: 6px;
  width: 6px;
  border-width: 2px 2px 0 0;
  border-color: #c8c8c8;
  border-style: solid;
  transform: translateY(-50%) matrix(.71, .71, -.71, .71, 0, 0)
}

@mixin iphone-x-after ($iphone-x-bottom) {
  content: ' ';
  height: $iphone-x-bottom;
  background: #fff;
  width: 100%;
  display: block;
}

@mixin multi-ellipsis ($lines) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}

@mixin ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

@mixin border-retina($pos: surround, $border-retina-color: #e5e5e5) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid $border-retina-color;

  @if $pos == surround {
    border-width: 1px;
  }
  @if $pos == top-bottom {
    border-width:  1px 0;
  }
  @if $pos == left-right {
    border-width: 0 1px;
  }
  @if $pos == top {
    border-top-width: 1px;
  }
  @if $pos == right {
    border-right-width: 1px;
  }
  @if $pos == bottom {
    border-bottom-width: 1px;
  }
  @if $pos == left {
    border-left-width: 1px;
  }
}
