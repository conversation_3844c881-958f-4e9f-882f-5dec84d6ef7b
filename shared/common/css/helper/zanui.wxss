.zan-badge {
  position: relative;
}
.zan-badge__count {
  position: absolute;
  top: -16px;
  right: 0;
  height: 1.6em;
  min-width: 1.6em;
  line-height: 1.6;
  padding: 0 0.4em;
  font-size: 20px;
  border-radius: 0.8em;
  background: #f44;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  transform: translateX(50%) scale(0.5);
  transform-origin: center;
  z-index: 10;
  box-shadow: 0 0 0 2px #fff;
  box-sizing: border-box;
}
.zan-btn {
  position: relative;
  color: #333;
  background-color: #fff;
  margin-bottom: 10px;
  padding-left: 15px;
  padding-right: 15px;
  border-radius: 2px;
  font-size: 16px;
  line-height: 45px;
  height: 45px;
  box-sizing: border-box;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
}
.zan-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid #e5e5e5;
  border-width: 1px;
  border-radius: 4px;
}
.zan-btns {
  margin: 15px;
}
.zan-btn--primary {
  color: #fff;
  background-color: #4b0;
}
.zan-btn--primary::after {
  border-color: #0a0;
}
.zan-btn--danger {
  color: #fff;
  background-color: #f44;
}
.zan-btn--danger::after {
  border-color: #e33;
}
.zan-btn--small {
  display: inline-block;
  height: 30px;
  line-height: 30px;
  font-size: 12px;
  margin-right: 5px;
  margin-bottom: 0;
}
.zan-btn--mini {
  display: inline-block;
  line-height: 21px;
  height: 22px;
  font-size: 10px;
  margin-right: 5px;
  margin-bottom: 0;
  padding-left: 5px;
  padding-right: 5px;
}
.zan-btn--large {
  border-radius: 0;
  margin-bottom: 0;
  border: none;
  line-height: 50px;
  height: 50px;
}
.zan-btn--plain.zan-btn {
  background-color: transparent;
}
.zan-btn--plain.zan-btn--primary {
  color: #06bf04;
}
.zan-btn--plain.zan-btn--danger {
  color: #f44;
}
.button-hover {
  opacity: 0.9;
}
.zan-btn--loading {
  color: transparent;
  opacity: 1;
}
.zan-btn--loading::before {
  position: absolute;
  left: 50%;
  top: 50%;
  content: ' ';
  width: 16px;
  height: 16px;
  margin-left: -8px;
  margin-top: -8px;
  border: 3px solid #e5e5e5;
  border-color: #666 #e5e5e5 #e5e5e5 #e5e5e5;
  border-radius: 8px;
  box-sizing: border-box;
  animation: btn-spin 0.6s linear;
  animation-iteration-count: infinite;
}
.zan-btn--danger.zan-btn--loading::before,
.zan-btn--primary.zan-btn--loading::before {
  border-color: #fff rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1);
}
@keyframes btn-spin {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.zan-btn.zan-btn--disabled {
  color: #999 !important;
  background: #f8f8f8 !important;
  border-color: #e5e5e5 !important;
  cursor: not-allowed !important;
  opacity: 1 !important;
}
.zan-btn.disabled {
  pointer-events: none;
}
.zan-btn.zan-btn--disabled::after {
  border-color: #e5e5e5 !important;
}
.zan-btn--last-child,
.zan-btn:last-child {
  margin-bottom: 0;
  margin-right: 0;
}
.zan-card {
  margin-left: 0;
  width: auto;
  padding: 5px 15px;
  overflow: hidden;
  position: relative;
  font-size: 14px;
}
.zan-card__thumb {
  width: 90px;
  height: 90px;
  float: left;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
  background-size: cover;
}
.zan-card__img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
}
.zan-card__detail {
  margin-left: 100px;
  width: auto;
  position: relative;
}
.zan-card__detail-row {
  overflow: hidden;
  line-height: 20px;
  min-height: 20px;
  margin-bottom: 3px;
}
.zan-card__right-col {
  float: right;
}
.zan-card__left-col {
  margin-right: 80px;
}
.zan-cell {
  position: relative;
  padding: 12px 15px;
  display: flex;
  align-items: center;
  line-height: 1.4;
  font-size: 14px;
}
.zan-cell::after {
  content: '';
  position: absolute;
  top: 0;
  width: 200%;
  height: 200%;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid #e5e5e5;
  border-bottom-width: 1px;
  left: 15px;
  right: 0;
}
.zan-cell__icon {
  margin-right: 5px;
}
.zan-cell__bd {
  flex: 1;
}
.zan-cell__text {
  line-height: 24px;
  font-size: 14px;
}
.zan-cell__desc {
  line-height: 1.2;
  font-size: 12px;
  color: #666;
}
.zan-cell__ft {
  position: relative;
  text-align: right;
  color: #666;
}
.zan-cell--last-child::after,
.zan-cell:last-child::after {
  display: none;
}
.zan-cell--access .zan-cell__ft {
  padding-right: 13px;
}
.zan-cell--access .zan-cell__ft::after {
  position: absolute;
  top: 50%;
  right: 2px;
  content: ' ';
  display: inline-block;
  height: 6px;
  width: 6px;
  border-width: 2px 2px 0 0;
  border-color: #c8c8c8;
  border-style: solid;
  transform: translateY(-50%) matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
}
.zan-field {
  padding: 7px 15px;
  color: #333;
}
.zan-field--wrapped {
  margin: 0 15px;
  background-color: #fff;
}
.zan-field--wrapped::after {
  left: 0;
  border-width: 1px;
  border-radius: 4px;
}
.zan-field.zan-field--wrapped::after {
  display: block;
}
.zan-field--wrapped + .zan-field--wrapped {
  margin-top: 10px;
}
.zan-field__title {
  color: #333;
  min-width: 65px;
  padding-right: 10px;
}
.zan-field__input {
  flex: 1;
  line-height: 1.6;
  padding: 4px 0;
  min-height: 22px;
  height: auto;
  font-size: 14px;
}
@font-face {
  font-family: zanui-weapp-icon-old;
  src: url(https://b.yzcdn.cn/zanui-weapp/zanui-weapp-icon-4381aded05.woff2)
      format('woff2'),
    url(https://b.yzcdn.cn/zanui-weapp/zanui-weapp-icon-4381aded05.woff)
      format('woff');
}
.zan-icon {
  display: inline-block;
}
.zan-icon::before {
  font-family: zanui-weapp-icon-old !important;
  font-style: normal;
  font-weight: 400;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  -webkit-font-smoothing: antialiased;
}
.zan-icon-close:before {
  content: '\e803';
}
.zan-icon-location:before {
  content: '\e804';
}
.zan-icon-shop:before {
  content: '\e80b';
}
.zan-icon-arrow:before {
  content: '\e815';
}
.zan-icon-play:before {
  content: '\e852';
}
.zan-icon-pause:before {
  content: '\e853';
}
.zan-icon-stop:before {
  content: '\e854';
}
.zan-panel {
  position: relative;
  background: #fff;
  margin-top: 10px;
  overflow: hidden;
}
.zan-panel::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid #e5e5e5;
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.zan-panel-title {
  font-size: 14px;
  line-height: 1;
  color: #999;
  padding: 20px 15px 0 15px;
}
.zan-panel--without-margin-top {
  margin-top: 0;
}
.zan-panel--without-border::after {
  border: 0 none;
}
.zan-popup {
  visibility: hidden;
}
.zan-popup--show {
  visibility: visible;
}
.zan-popup__mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  display: none;
}
.zan-popup__container {
  position: fixed;
  left: 50%;
  top: 50%;
  background: #fff;
  transform: translate3d(-50%, -50%, 0);
  transform-origin: center;
  transition: all 0.4s ease;
  z-index: 11;
  opacity: 0;
}
.zan-popup--show .zan-popup__container {
  opacity: 1;
}
.zan-popup--show .zan-popup__mask {
  display: block;
}
.zan-popup--left .zan-popup__container {
  left: 0;
  top: auto;
  transform: translate3d(-100%, 0, 0);
}
.zan-popup--show.zan-popup--left .zan-popup__container {
  transform: translate3d(0, 0, 0);
}
.zan-popup--right .zan-popup__container {
  right: 0;
  top: auto;
  left: auto;
  transform: translate3d(100%, 0, 0);
}
.zan-popup--show.zan-popup--right .zan-popup__container {
  transform: translate3d(0, 0, 0);
}
.zan-popup--bottom .zan-popup__container {
  top: auto;
  left: auto;
  bottom: 0;
  transform: translate3d(0, 100%, 0);
}
.zan-popup--show.zan-popup--bottom .zan-popup__container {
  transform: translate3d(0, 0, 0);
}
.zan-popup--top .zan-popup__container {
  top: 0;
  left: auto;
  transform: translate3d(0, -100%, 0);
}
.zan-popup--show.zan-popup--top .zan-popup__container {
  transform: translate3d(0, 0, 0);
}
.zan-tab {
  height: 45px;
}
.zan-tab__bd {
  width: 750rpx;
  display: flex;
  flex-direction: row;
  border-bottom: 1rpx solid #e5e5e5;
  background: #fff;
}
.zan-tab__bd--fixed {
  position: fixed;
  top: 0;
  z-index: 2;
}
.zan-tab__item {
  flex: 1;
  display: inline-block;
  padding: 0 10px;
  line-height: 0;
  box-sizing: border-box;
  overflow: hidden;
  text-align: center;
}
.zan-tab__title {
  display: inline-block;
  max-width: 100%;
  height: 44px;
  line-height: 44px;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  word-break: keep-all;
  font-size: 14px;
  color: #666;
}
.zan-tab__item--selected .zan-tab__title {
  color: #f44;
  border-bottom: 2px solid #f44;
}
.zan-tab__bd--scroll {
  display: block;
  white-space: nowrap;
}
.zan-tab__bd--scroll .zan-tab__item {
  min-width: 80px;
}
.zan-tag {
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  line-height: 16px;
  padding: 0 5px;
  border-radius: 2px;
  font-size: 11px;
  background: #c9c9c9;
  text-align: center;
  color: #fff;
}
.zan-tag::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid #e5e5e5;
  border-width: 1px;
  border-radius: 4px;
}
.zan-tag--plain {
  color: #c9c9c9;
  background: #fff;
}
.zan-tag--primary {
  color: #fff;
  background-color: #4b0;
}
.zan-tag--primary::after {
  border-color: #4b0;
}
.zan-tag--primary.zan-tag--plain {
  color: #4b0;
  background: #fff;
}
.zan-tag--danger {
  color: #fff;
  background: #f44;
}
.zan-tag--danger::after {
  border-color: #f44;
}
.zan-tag--danger.zan-tag--plain {
  color: #f44;
  background: #fff;
}
.zan-tag--disabled {
  color: #999 !important;
  background: #e5e5e5;
}
.zan-tag--disabled::after {
  border-color: #ccc;
}
