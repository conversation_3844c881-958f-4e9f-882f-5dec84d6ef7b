.button {
  position: static;
  height: 100%;
  display: block;
  margin-left: 0;
  margin-right: 0;
  padding-left: 0;
  padding-right: 0;
  box-sizing: border-box;
  font-size: inherit;
  text-align: inherit;
  text-decoration: inherit;
  line-height: inherit;
  border-radius: 0;
  -webkit-tap-highlight-color: transparent;
  overflow: inherit;
  color: inherit;
  background-color: inherit;
}

.button::after {
  display: none;
}
