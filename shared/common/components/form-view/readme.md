## form-view组件使用说明

##### 入参

+ `type` 上报业务模块 business_module，可不传
+ `disabled` 禁用formId上报

#### 事件

+ `report` 日志上报事件，出参可参考[form组件的submit事件](https://developers.weixin.qq.com/miniprogram/dev/component/form.html)


#### 基本用法

```html
<form-view bind:report="handleReport" disabled type="carve-coupon">
  hello world!
</form-view>
```

#### 已知问题

1. 在`form-view`上设置`display: flex;`不会获得预期的效果，因为组件内部结构为如下
  ```html
  <form class="form" report-submit bindsubmit="submit">
    <button class="button btn-class" form-type="submit">
      <slot></slot>
    </button>
  </form>
  ```
  如果需要用flex布局，可自行嵌套一层view，如
  ```html
  <form-view bind:report="handleReport" disabled type="carve-coupon">
    <view style="display: flex;">
      <view>1</view>
      <view>2</view>
      <view>3</view>
    </view>
  </form-view>
  ```

2. `form-view`内部如果拦截了点击事件，formId上报将不会触发，如
  ```html
  <form-view bind:report="handleReport" disabled type="carve-coupon">
    <button catchtap="handleTapButton">hello world!</button>
  </form-view>
  ```
