import WscComponent from 'shared/common/base/wsc-component/index';
import { carmen } from 'shared/utils/request';

WscComponent({
  properties: {
    type: String, // 上报业务模块 business_module
    openType: String,
    customBtnStyle: String,
    disabled: Boolean // 禁用formId自动上报，外部可监听report事件获取formId自行处理
  },

  externalClasses: ['btn-class'],

  methods: {
    submit({ detail }) {
      const formId = detail.formId;

      if (!formId) {
        return;
      }

      this.triggerEvent('report', detail);

      const { disabled, type } = this.data;

      if (disabled) {
        return;
      }

      const params = {
        weapp_type: 'custom',
        form_id: formId
      };

      carmen({
        api: 'wsc.weapp.formid/1.0.0/add',
        data: type ? { ...params, business_module: type } : params
      });
    }
  }
});
