<van-popup z-index="{{ 299 }}" show="{{ open }}" custom-class="order-pay-prompt-popup" close-on-click-overlay="{{ false }}">
  <view class="order-pay-prompt-popup-container">
    <view class="order-pay-prompt-popup-title">
      <text class="order-pay-prompt-popup-title_text">{{ orderInfo.title || '你有一份订单待支付' }}</text>
    </view>
    <prompt-desc bindbtnevent="timeEndHandle" tip="{{ orderInfo.descTemplate }}"></prompt-desc>
    <prompt-evaluation evaluateList="{{ orderInfo.evaluateList }}"></prompt-evaluation>
    <single-goods wx:if="{{ orderInfo.goods.length === 1 }}" goods="{{ orderInfo.goods }}" payPrice="{{ orderInfo.payPrice }}" orderPath="{{ orderPath }}"></single-goods>
    <multi-goods wx:if="{{ orderInfo.goods.length > 1 }}" goods="{{ orderInfo.goods }}" payPrice="{{ orderInfo.payPrice }}" orderPath="{{ orderPath }}"></multi-goods>
    <prompt-btns bindbtnevent="btnEventHandle"></prompt-btns>
  </view>
</van-popup>