import { batchPayPromptReceive, getLevel } from './api';
import money from '@youzan/weapp-utils/lib/money';
import navigate from 'shared/utils/navigate';
import getApp from 'shared/utils/get-safe-app';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import { getLoggerExtraParams } from '@/bootstrap/yun-sdk/yun-logger/cloud-logger';
import spm from 'shared/utils/spm';
import args from '@youzan/weapp-utils/lib/args';

function genBannerId(index = 0) {
  return `${spm.getPageSpmTypeId()}~pending_payment_popup~${
    index + 1
  }~${makeRandomString(8)}`;
}

function getCloudLoggerInfo() {
  const pages = getCurrentPages();
  return {
    ad_name: '催付弹窗',
    page_url: pages[pages.length - 1].route,
  };
}

wx.onAppHide(() => {
  wx.removeStorageSync('order-pay-prompt-popup');
});
Component({
  properties: {
    source: {
      type: String,
      value: 'ITEM_DETAIL',
    },
  },
  data: {
    open: false,
    orderInfo: {},
    abTraceId: null,
    orderPath: '',
  },
  attached() {
    const popup = wx.getStorageSync('order-pay-prompt-popup');
    if (popup) {
      this.triggerEvent('close-pop');
      return;
    }
    this._batchPayPromptReceive();
  },
  methods: {
    _batchPayPromptReceive() {
      return batchPayPromptReceive({ source: this.data.source })
        .then((res) => {
          if (!res) {
            this.triggerEvent('close-pop');
          } else if (res.popup) {
            res.payPrice = money(res.payPrice).toYuan();
            const path = `/packages/order/index?orderNo=${
              res.orderNo
            }&banner_id=${genBannerId()}`;
            this.setData({
              orderInfo: res,
              open: res.popup,
              abTraceId: res.abTraceId,
              orderPath: path,
            });
            wx.setStorageSync('order-pay-prompt-popup', true);
            this.logHandle(this.exposureLog());
          } else if (res.abTraceId) {
            this.setData({
              orderInfo: res,
              abTraceId: res.abTraceId,
            });
            wx.setStorageSync('order-pay-prompt-popup', true);
            this.triggerEvent('close-pop');
            this.logHandle(this.exposureLog());
          } else {
            this.triggerEvent('close-pop');
          }
        })
        .catch(() => {
          this.triggerEvent('close-pop');
        });
    },
    timeEndHandle(e) {
      if (e.detail === 'close') {
        this.setData({
          open: false,
        });
        this.triggerEvent('close-pop', {
          isRealClose: true,
        });
      }
    },
    handleLevelGoodsUrl() {
      // 查询是否属于连续包月商品
      const { goods = [] } = this.data.orderInfo || {};
      const { kdtId, goodsId, skuId, payGradeCard = false } = goods[0] || [];
      if (payGradeCard === false) {
        return '';
      }
      return getLevel({ kdtId, goodsId }).then((data) => {
        const { autoRenew, levelAlias, levelGoods = {} } = data;
        const { skuList } = levelGoods;
        const sku = skuList.find((s) => s.goodsSkuId == skuId);
        const { goodsSkuType } = sku || [];
        // 1 付费规则 2.自动续费规则
        if (autoRenew && goodsSkuType == 2) {
          const payLevelUrl = args.add(
            'https://cashier.youzan.com/pay/wscuser_paylevel',
            {
              kdt_id: kdtId,
              alias: levelAlias,
              banner_id: genBannerId(),
            }
          );
          const wxUrl = `/pages/common/webview-page/index?src=${encodeURIComponent(
            payLevelUrl
          )}`;
          return wxUrl;
        }
        return '';
      });
    },
    async btnEventHandle(e) {
      if (e.detail === 'pay') {
        let url = '';
        // 判断是否为连续包月月卡商品，是则跳转至会员下单页
        url = await this.handleLevelGoodsUrl();
        if (url == '') {
          url = this.data.orderPath;
        }
        this.logHandle(this.payLog());
        navigate.navigate({ url });
        return;
      }
      this.setData({
        open: false,
      });
      this.logHandle(this.abandonLog());
      this.triggerEvent('close-pop', {
        isRealClose: true,
      });
    },
    exposureLog() {
      const cloudLoggerInfo = getLoggerExtraParams('shop_ad_pop', {
        ...getCloudLoggerInfo(),
      });
      const logParams = {
        et: 'view', // 事件类型
        ei: 'component_view', // 事件标识
        en: '组件曝光', // 事件名称
        params: {
          banner_id: genBannerId(),
          order_no: this.data.orderInfo.orderNo || '',
          popup_type: this.data.orderInfo.descTemplate
            ? this.data.orderInfo.descTemplate.popupType
            : '',
          component: 'pending_payment_popup',
          abTraceId: this.data.abTraceId || null,
          ...cloudLoggerInfo,
        },
      };
      return logParams;
    },
    payLog() {
      const cloudLoggerInfo = getLoggerExtraParams('shop_ad_pop', {
        ...getCloudLoggerInfo(),
      });
      const logParams = {
        et: 'click',
        ei: 'pay_click',
        en: '去支付点击',
        params: {
          banner_id: genBannerId(),
          order_no: this.data.orderInfo.orderNo,
          popup_type: this.data.orderInfo.descTemplate.popupType,
          component: 'pending_payment_popup',
          abTraceId: this.data.abTraceId || null,
          ...cloudLoggerInfo,
        },
      };
      return logParams;
    },
    abandonLog() {
      const cloudLoggerInfo = getLoggerExtraParams('shop_ad_pop', {
        ...getCloudLoggerInfo(),
      });
      const logParams = {
        et: 'click',
        ei: 'temporarily_abandon_click',
        en: '点击暂时放弃',
        params: {
          banner_id: genBannerId(),
          order_no: this.data.orderInfo.orderNo,
          popup_type: this.data.orderInfo.descTemplate.popupType,
          component: 'pending_payment_popup',
          abTraceId: this.data.abTraceId || null,
          ...cloudLoggerInfo,
        },
      };
      return logParams;
    },
    logHandle(logParams) {
      const app = getApp();
      app.logger && app.logger.log(logParams);
    },
  },
});
