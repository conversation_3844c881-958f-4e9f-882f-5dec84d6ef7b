import navigate from 'shared/utils/navigate';

Component({
  properties: {
    goods: {
      type: Array,
      value: [],
      observer(val) {
        this.singleItemHandle(val);
      }
    },
    payPrice: {
      type: String,
      value: '',
      observer(val) {
        this.payPriceHandle(val);
      }
    },
    orderPath: {
      type: String,
      value: '',
    },
  },
  data: {
    imgUrl: '',
    goodsName: '',
    service: '',
    yuan: '',
    fen: '',
  },
  attached() { },
  methods: {
    singleItemHandle(val) {
      this.setData({
        imgUrl: val && val[0].imgUrl,
        goodsName: val && val[0].goodsName,
        service: val && val[0].service
      });
    },
    payPriceHandle(val) {
      const paric = val.split('.');
      this.setData({
        yuan: paric && paric[0],
        fen: paric && paric[1],
      });
    },
    viewOrderHandle() {
      navigate.navigate({ url: this.data.orderPath });
    },
  },
});
