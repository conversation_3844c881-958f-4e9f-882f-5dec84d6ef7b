import navigate from 'shared/utils/navigate';

Component({
  properties: {
    goods: {
      type: Array,
      value: [],
      observer(val) {
        this.goodsSliceHandle(val);
      },
    },
    payPrice: {
      type: String,
      value: '',
      observer(val) {
        this.payPriceHandle(val);
      },
    },
    orderPath: {
      type: String,
      value: '',
    },
  },
  data: {
    yuan: '',
    fen: '',
    showgoods: [],
  },
  methods: {
    payPriceHandle(val) {
      const paric = val.split('.');
      this.setData({
        yuan: paric && paric[0],
        fen: paric && paric[1],
      });
    },
    goodsSliceHandle(val) {
      const curGoods = val.slice(0, 4);
      this.setData({
        showgoods: curGoods,
      });
    },
    viewOrderHandle() {
      navigate.navigate({ url: this.data.orderPath });
    },
  },
});
