<view class="multi-goods" bindtap="viewOrderHandle">
  <viwe class="goods-wrap">
    <viwe class="goods-img {{ showgoods.length < 4 ? 'goods-image-center': '' }}">
      <viwe
        wx:for="{{ showgoods }}"
        wx:for-item="goodsValue"
        class="goods-image"
      >
        <image class="goods-image-item" src="{{ goodsValue.imgUrl }}" mode="aspectFill"></image>
      </viwe>
    </viwe>
    <viwe class="goods-desc">
      <text class="normal-text">共{{ goods.length }}件，应付</text>
      <text class="unit-text">¥</text>
      <text class="price-text-yuan">{{ yuan }}</text>
      <text class="price-text-fen">.</text>
      <text class="price-text-fen">{{ fen }}</text>
    </viwe>
  </viwe>
  <viwe class="arrow">
    <van-icon class="arrow-icon" name="arrow" size="10px"/>
  </viwe>
</view>