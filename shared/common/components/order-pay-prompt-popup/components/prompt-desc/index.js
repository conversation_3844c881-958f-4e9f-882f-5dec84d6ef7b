import { format } from '@youzan/weapp-utils/lib/time';

Component({
  properties: {
    tip: {
      type: Object,
      observer(val) {
        this.showTip(val);
      },
    },
  },
  data: {
    expireTime: 0,
    tplsArr: [],
    remainTime: '',
  },
  attached() {},
  created() {
    this.count = 0;
    this.tid = null;
  },
  methods: {
    showTip(descTemplate) {
      const tpls = descTemplate.template;
      const tplsArr = tpls.split(/(\${\S*})/);
      const newTplsArr = this.tplsHandle(descTemplate, tplsArr);
      this.setData({
        tplsArr: newTplsArr,
        expireTime: descTemplate.expireTime,
      });
      this.timeRunner();
    },
    tplsHandle(tpls, arr) {
      const newTpls = [];
      arr.forEach((item, index) => {
        if (item.indexOf('field') > -1) {
          const key = item.match(/\${(\S*)}/)[1];
          newTpls.push({
            color: 'general',
            value: tpls[key],
            classname: 'general',
            index,
          });
        } else if (item.indexOf('expireTime') > -1) {
          newTpls.push({
            type: 'countdown',
            color: 'general',
            value: '',
            classname: 'countdown',
            index,
          });
        } else {
          newTpls.push({
            color: '',
            value: item,
            classname: '',
            index,
          });
        }
      });
      return newTpls;
    },
    timeRunner() {
      clearTimeout(this.tid);
      const end = this.data.expireTime;
      const now = Date.now();
      const remain = end - now;
      // 倒计时结束
      if (remain <= 0 && this.count > 0) {
        this.count = 0;
        this.triggerEvent('btnevent', 'close');
        return;
      }
      this.count += 1;

      const remainObj = format(remain).strData;
      remainObj.day = +remainObj.day;
      remainObj.milliseconds = `0${remainObj.hundredMilliseconds}`;

      let remainTime =
        remainObj.hour + ':' + remainObj.minute + ':' + remainObj.second;
      if (remainObj.day) {
        remainTime = `${remainObj.day}天 ` + remainTime;
      }
      this.setData({
        remainTime,
      });

      let delta = 1000;
      if (!remainObj.day) {
        delta = 50;
      }
      this.tid = setTimeout(this.timeRunner.bind(this), delta);
    },
  },
  detached() {
    clearTimeout(this.tid);
    this.tid = null;
  },
});
