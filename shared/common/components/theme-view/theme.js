import { defaultThemeColors, switchHexToRgb } from '../../css/theme-config';

const themeColorKeyMap = {
  general: 'general',
  'main-bg': 'main-bg',
  'main-text': 'main-text',
  'vice-bg': 'vice-bg',
  'vice-text': 'vice-text',
  'gradient-start': 'start-bg',
  'gradient-end': 'end-bg',
};
let colors = null;

export default {
  /**
   * 获取主题类型
   */
  getThemeType() {
    return this.getTheme().then((res) => res.type);
  },

  getTheme() {
    return new Promise((resolve, reject) => {
      if (!getApp) {
        return resolve({ type: 0, colors: { ...defaultThemeColors } });
      }

      const app = getApp();
      app
        .getShopTheme()
        .then((res) => {
          resolve(res);
        })
        .catch(() => reject());
    });
  },

  /**
   * 获取对应的颜色值.
   * @param {*} colorAbstract 颜色类型
   * @param {*} type 主题类型
   */
  //  这个方法不应该被外部引用
  getThemeColorWithType(colorAbstract) {
    // 查找主题对应索引
    const themeVarKey = themeColorKeyMap[colorAbstract];

    if (themeVarKey) {
      const result = colors
        ? colors[themeVarKey]
        : defaultThemeColors[themeVarKey];
      return result;
    }

    return colorAbstract;
  },

  /**
   * 获取对应当前配色值
   * @param {*} colorAbstract 颜色类型，可选'general', 'main-bg', 'main-text', 'vice-bg', 'vice-text', 'gradient-start', 'gradient-end'
   */
  getThemeColor(colorAbstract) {
    return this.getTheme().then((data) => {
      colors = data.colors;
      return this.getThemeColorWithType(colorAbstract);
    });
  },

  /**
   * hex 转 rgb
   * @param {String} hex '#ffffff'
   */
  switchHexToRgb(hex) {
    return switchHexToRgb(hex);
  },
};
