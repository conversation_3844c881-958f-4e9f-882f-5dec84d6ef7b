# theme-view 自定义组件主题

## 说明
自定义组件主题显示方案，通过JS生成内联样式的方式，减少样式重复带来的主包体积压力。

## 主题色值抽象

| 名称 | 含义 |
| ---- | ---- |
| general | 主色调 |
| main-bg | 主背景色 |
| main-text | 主字体色 |
| vice-bg | 辅背景色 |
| vice-text|  辅字体色 |
| gradient-start | 渐变初始色 |
| gradient-end | 渐变结束色 |

## API

| 参数 | 说明 | 类型 | 默认值 | 备注|
| ---- | ---- | ---- | ---- | ---- |
| bg | 背景底色 | String | default | |
| opacity | 背景透明度 | String | 1 | 此值等同于rgba 中的alpha 值 |
| color | 字体颜色 | String | default |
| border | 边线颜色 | String | default |
| borderAfter | 模拟伪类元素border 的样式 | String | default | 只设置border颜色，其余样式请通过外部样式类定义|
|gradient| 是否渐变背景 | Boolean| false |
| gradientDeg | 渐变角度 | Number | - | |
| innerStyle | 内联样式 | String | default |

## 外部样式类

| 类名 | 说明 |
| ---- | ---- |
| custom-class | 根节点样式类 |
| custom-after-class | 模拟伪类元素的样式类 |

## 已知问题

- component 中传入externalClass 当前只能有一个class 生效
- page 中传入externalClass 可以传入多个
- 外部传入的样式优先级不确定，当样式有问题时，可能（但不总是）需要important
- [微信开发者社区参考](https://developers.weixin.qq.com/community/develop/doc/000046750e80180c07b63826151400)

## demo

![用例展示图片](https://img.yzcdn.cn/weapp/wsc/rrcwYB.png)

```html
<theme-view bg="main-bg" color="main-text">
  main-bg 以及main-text 区块
</theme-view>

<theme-view bg="main-bg" opacity="0.5" color="main-text">
  透明为0.5的区块
</theme-view>

<theme-view gradient color="main-text" gradient-deg="90">
  一段渐变的区块
</theme-view>

<theme-view border="main-bg" custom-class="with-border">
  这是一段有border的元素
</theme-view>

<theme-view
  border-after="main-bg"
  custom-class="with-after"
  custom-after-class="border-after-class"
>
  这是一段使用了模拟的after的区块
</theme-view>
```

## 更新日志

11.20 初始版本
11.28 移除不必要的rgb 转换，新增observer, 减少一步setData操作
05.21 增加获取主题色behavior，在组件中引入直接可以使用 `themeColor` 
