import WscComponent from 'shared/common/base/wsc-component/index';
import theme from './theme';

WscComponent({
  properties: {
    bg: {
      type: String,
      observer: 'generateStyle',
    },
    opacity: {
      type: String,
      value: 1,
      observer: 'generateStyle',
    },
    color: {
      type: String,
      observer: 'generateStyle',
    },
    border: {
      type: String,
      observer: 'generateStyle',
    },
    borderAfter: {
      type: String,
      observer: 'generateStyle',
    },
    gradient: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'generateStyle',
    },
    reverseGradient: Boolean,
    gradientDeg: {
      type: Number,
      value: 0,
    },
    /** 斜条纹渐变 */
    twillGradient: {
      type: Number,
    },
    gradientStart: String,
    gradientEnd: String,
    innerStyle: String,
  },

  data: {
    elementStyle: '',
    borderAfterStyle: '',
  },

  _themeType: 0,
  _colors: [],

  externalClasses: ['custom-class', 'custom-after-class'],

  methods: {
    generateStyle() {
      // observe 时不一定存在_themeType
      const themeType = this._themeType;
      if (typeof themeType !== 'number') return;
      const themeColors = this._colors;
      const {
        bg,
        opacity,
        color,
        border,
        borderAfter,
        gradient,
        gradientDeg,
        innerStyle,
        twillGradient,
        reverseGradient,
      } = this.data;
      const gradientEnd = this.data.gradientEnd || themeColors[6];
      const gradientStart = this.data.gradientStart || themeColors[5];
      let backgroundColor = '';
      let textColor = '';
      let borderColor = '';
      let borderAfterStyle = '';

      if (bg) {
        const styleValue = this.convertToStyle(bg);

        backgroundColor = `background-color: ${styleValue};`;

        if (+opacity !== 1) {
          const [r, g, b] = theme.switchHexToRgb(styleValue);
          backgroundColor = `background-color: rgba(${r}, ${g}, ${b}, ${opacity});`;
        }
      }

      if (color) {
        const styleValue = this.convertToStyle(color);
        textColor = `color: ${styleValue};`;
      }

      if (border) {
        const styleValue = this.convertToStyle(border);
        borderColor = `border-color: ${styleValue};`;
      }

      if (borderAfter) {
        const styleValue = this.convertToStyle(borderAfter);
        borderAfterStyle = `border-color: ${styleValue}`;
      }

      if (gradient) {
        const styleValue = `linear-gradient(${gradientDeg}deg, ${gradientStart}, ${gradientEnd})`;
        backgroundColor = `background: ${styleValue};`;
      }

      if (reverseGradient) {
        const [r6, g6, b6] = theme.switchHexToRgb(themeColors[6]);
        const [r5, g5, b5] = theme.switchHexToRgb(themeColors[5]);
        const styleValue = `linear-gradient(${gradientDeg}deg, rgba(${r6}, ${g6}, ${b6}, 0.8), rgba(${r5}, ${g5}, ${b5}, 0.8))`;
        backgroundColor = `background: ${styleValue};`;
      }

      if (twillGradient) {
        const styleValue = `linear-gradient(135deg, ${themeColors[6]} 0, ${themeColors[6]} 25%, transparent 25%, transparent 50%, ${themeColors[6]} 50%, ${themeColors[6]} 75%, transparent 75%, transparent)`;
        backgroundColor = `background: ${styleValue}; background-size: 8px 8px;`;
      }

      const elementStyle = `${backgroundColor} ${borderColor} ${textColor} ${innerStyle}`;

      this.setYZData({
        elementStyle,
        borderAfterStyle,
      });
    },

    convertToStyle(colorAbstract) {
      return theme.getThemeColorWithType(colorAbstract);
    },
  },
  created() {
    // 小程序环境中
    theme.getTheme().then((res) => {
      this._themeType = +res.type;
      const { colors } = res;
      this._colors = [
        colors.general,
        colors['main-bg'],
        colors['main-text'],
        colors['vice-bg'],
        colors['vice-text'],
        colors['start-bg'],
        colors['end-bg'],
      ];
      this.generateStyle();
    });
  },
});
