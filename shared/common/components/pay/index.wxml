<view class="cap-cashier">
  <van-popup position="bottom" show="{{ showPayWays }}" custom-class="custom-class" bind:click-overlay="onClickOverlay">
    <pay-ways
      pay-ways="{{ payWays }}"
      bind:pay-item-click="onPayClick"
      loading-pay-way="{{ loadingPayWay }}"
    />
  </van-popup>
  <!-- E卡密码框 -->
  <password
    show-password="{{ showPassword }}"
    secure-account="{{ secureAccount }}"
    is-paying="{{ isPaying }}"
    bind:close-password="onClosePassword"
    bind:password-pay="onPasswordPay"
  />
</view>