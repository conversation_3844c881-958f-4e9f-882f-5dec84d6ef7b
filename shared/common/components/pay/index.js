Component({
  externalClasses: ['custom-class'],

  properties: {
    showPayWays: {
      type: Boolean,
      value: false
    },
    payWays: {
      type: Array,
      value: []
    },
    showPassword: {
      type: Boolean,
      value: false
    },
    secureAccount: {
      type: String,
      value: ''
    },
    loadingPayWay: {
      type: Object,
      value: {}
    },
    isPaying: {
      type: Boolean,
      value: false
    },
    showAdjustPrice: {
      type: Boolean,
      value: false,
      observer(newVal) {
        if (newVal) {
          wx.showModal({
            title: '改价提醒',
            content: `商家已将交易金额修改为${(this.data.newPrice / 100).toFixed(
              2
            )}元，是否继续支付？`,
            confirmText: '确认',
            cancelText: '取消',
            success: res => {
              // 同意改价
              if (res.confirm) {
                const {
                  payChannel = 'ECARD',
                  payChannelName = '',
                  password = ''
                } = this.data.loadingPayWay;
                const filteredPayWay = this.data.payWays.filter(_payWay => {
                  return _payWay.payChannel === payChannel;
                });
                const currentPayWay = filteredPayWay.length > 0
                  ? filteredPayWay[0]
                  : { payChannel, payChannelName, password };
                this.setData({ acceptPrice: 1 });
                this.triggerEvent('pay-way-selected', {
                  payWay: currentPayWay,
                  password: currentPayWay.password, // 兼容性，可以不传
                  acceptPrice: 1,
                  newPrice: this.data.newPrice
                });
              } else {
                this.triggerEvent('cancel-adjust-price');
              }
            }
          });
        }
      }
    },
    newPrice: {
      type: Number,
      value: -1
    }
  },

  data: {
    // 改价独立于支付方式，作为组件级属性使用
    acceptPrice: 0
  },

  methods: {
    onPayClick({ detail = {} }) {
      // console.log(detail);
      this.triggerEvent('pay-way-selected', {
        payWay: detail.payWay || {},
        acceptPrice: this.data.acceptPrice,
        newPrice: this.data.newPrice
      });
    },

    handleActionClick(index) {
      console.log(index);
    },

    onClickOverlay() {
      // console.log('cancel...');
      this.triggerEvent('close', {});
    },

    onClosePassword() {
      this.triggerEvent('close-password', {});
    },

    onPasswordPay(e) {
      // console.log('onPay');
      // console.log(e);
      const {
        payChannel = 'ECARD',
        payChannelName = '有赞E卡'
      } = this.data.loadingPayWay;
      this.triggerEvent('pay-way-selected', {
        payWay: { payChannel, payChannelName },
        password: (e.detail && e.detail.password) || '',
        acceptPrice: this.data.acceptPrice,
        newPrice: this.data.newPrice
      });
    }
  }
});
