<van-cell-group>
  <van-cell
    class="cap-pay-item"
    wx:for="{{ computedPayWays }}"
    wx:for-item="payWay"
    wx:key="payChannel"
    bind:click="onPayItemClick"
    data-pay-way="{{ payWay }}"
    custom-class="cap-pay-item-cell"
    value-class="cap-pay-item__value"
  >
    <view wx:if="{{ payWay.payChannel !== loadingPayWay.payChannel }}" class="cap-pay-item__value-wrapper {{ payWay.payChannel }} {{ !payWay.available ? 'disabled' : '' }}">
      <view class="cap-pay-item__value-content">{{ payWay.payChannelName }}</view>
      <view wx:if="{{ payWay.availableDesc }}" class="cap-pay-item__value-desc">{{ payWay.availableDesc }}</view>
    </view>
    <van-loading wx:else size="24px" />
  </van-cell>
</van-cell-group>