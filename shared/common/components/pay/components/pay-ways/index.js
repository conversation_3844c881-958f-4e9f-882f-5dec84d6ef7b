Component({
  properties: {
    payWays: {
      type: Array,
      value: [],
      observer() {
        const _payWays = this._computePayWays();
        if (_payWays.length === 0) {
          return;
        }
        // console.log('observe payWays change...' + _payWays.length);
        // console.log(_payWays);
        this.setData({ computedPayWays: _payWays });
        if (_payWays.length === 1) {
          this.triggerEvent('pay-item-click', { payWay: _payWays[0] });
        }
      }
    },
    loadingPayWay: {
      type: Object,
      value: {}
    }
  },

  data: {
    computedPayWays: [],
    showAllPayWays: false
  },

  methods: {
    onPayItemClick({ target = {} }) {
      // console.log(target);
      const payWay = (target.dataset && target.dataset.payWay) || {};
      if (!payWay.available || payWay.payChannel === this.data.loadingPayWay.payChannel) {
        return;
      }

      if (payWay.payChannel === 'more') {
        this.setData({
          showAllPayWays: true,
          computedPayWays: this._computePayWays(true)
        });
        return;
      }

      this.triggerEvent('pay-item-click', { payWay });
    },

    _computePayWays(_showAllPayWays) {
      if (_showAllPayWays || this.data.showAllPayWays) {
        return this.data.payWays;
      }

      const displayPayWays = this.data.payWays.filter(payWay => {
        if (payWay.shouldWrap) {
          return false;
        }

        return true;
      });

      if (displayPayWays.length !== this.data.payWays.length) {
        displayPayWays.push({
          payChannelName: '更多支付方式',
          available: true,
          payChannel: 'more'
        });
      }

      return displayPayWays;
    }
  }
});
