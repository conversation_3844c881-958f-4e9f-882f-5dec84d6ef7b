<view class="{{ showPassword }}">
  <van-overlay
    mask
    show="{{ showPassword }}"
    z-index="{{ zIndex }}"
    bind:click="onClickOverlay"
  />
  <view wx:if="{{ showPassword }}" class="cap-cashier__password">
    <view class="cap-cashier__password__header">
      安全验证
      <van-icon name="close" catchtap="closePassword" />
    </view>
    <view class="cap-cashier__password-container">
      <view class="cap-cashier__password__desc">为了保障您的账户安全，请输入手机账号{{ secureAccount }}的登录密码</view>
      <view class="cap-cashier__password__input van-hairline--surround">
        <input
          type="password"
          placeholder="请输入登录密码"
          bindinput="onPasswordInput"
        />
      </view>
    </view>
    <view class="cap-cashier__password__action-container">
      <van-button type="primary" catch:click="passwordPay" custom-class="cap-cashier__password__action" loading="{{ isPaying }}" >付款</van-button>
    </view>
  </view>
</view>