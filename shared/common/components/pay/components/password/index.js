Component({
  properties: {
    showPassword: {
      type: Boolean,
      value: false,
    },
    secureAccount: {
      type: String,
      value: '',
    },
    loading: {
      type: Boolean,
      value: false,
    },
    isPaying: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    zIndex: 110,
    overlay: true,
    password: '',
  },

  methods: {
    onClickOverlay() {
      this.triggerEvent('click-overlay');
    },

    closePassword() {
      this.triggerEvent('close-password');
    },

    onPasswordInput(e) {
      const { value: password = '' } = e.detail;
      this.setData({ password });
    },

    async passwordPay() {
      const crypto = await import('@youzan/crypto');
      const password = crypto.aes.legacyEncrypt(this.data.password);
      if (this.data.isPaying) {
        return;
      }
      this.triggerEvent('password-pay', { password });
    },
  },
});
