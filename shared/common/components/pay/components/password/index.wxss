@import '@vant/weapp/dist/common/index.wxss';

.cap-cashier__password {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 112;
  background: #fff;
  max-width: 80%;
  min-width: 280px;
  padding: 15px;
}

.cap-cashier__password__header {
  line-height: 25px;
  font-size: 16px;
  color: #333;
}

.cap-cashier__password__header van-icon {
  float: right;
}

.cap-cashier__password-container {
  margin: 15px 0;
}

.cap-cashier__password__input {
  background-color: #fff;
  box-sizing: border-box;
  color: #333;
  font-size: 14px;
  line-height: 24px;
  padding: 10px 15px;
  position: relative;
  width: 100%;
  margin-top: 8px;
}

.cap-cashier__password__input input {
  border: 0;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 24px;
  resize: none;
  display: block;
  text-align: left;
  min-height: 24px;
  line-height: inherit;
  box-sizing: border-box;
  background-color: transparent;
}

.cap-cashier__password__desc {
  font-size: 14px;
  color: #999;
}

.cap-cashier__password__action {
  width: 100%;
}
