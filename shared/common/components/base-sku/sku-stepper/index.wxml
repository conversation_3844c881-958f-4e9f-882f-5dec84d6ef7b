<view class="van-sku-stepper-stock">
  <view class="van-sku-stepper-container">
    <view class="van-sku__stepper-title">{{ stepperTitle }}</view>
    <van-stepper
      class="van-sku__stepper"
      size="small"
      value="{{ selectedNum }}"
      min="{{ min }}"
      max="{{ max }}"
      integer
      bind:change="onSelectNumChange"
      bind:overlimit="onOverLimit"
    />
    <theme-view 
      wx:if="{{ quotaText }}"
      color="general"
      class="van-sku__quota"
    >
      {{ quotaText }}
    </theme-view>
  </view>
</view>
