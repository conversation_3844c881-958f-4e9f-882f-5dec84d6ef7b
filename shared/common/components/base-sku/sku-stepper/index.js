import { VanxComponent } from 'shared/common/base/wsc-component/index';
import skuBehavior from '../common/sku-behavior';
import { LIMIT_TYPE } from '../common/constants';

VanxComponent({
  behaviors: [skuBehavior],

  properties: {
    hideStock: Boolean,
    stepperTitle: String,
    quotaText: String,
    quota: Number,
    quotaUsed: Number,
    quotaCycle: Number,
    selectedNum: Number,
    // 可能库存变化，需要侦听
    selectedSkuComb: {
      type: Object,
      observer: 'updateStockNum',
    },
    sku: {
      type: Object,
      observer: 'updateStockNum',
    },
    startSaleNum: {
      type: Number,
      observer: 'updateStockNum',
      value: 1,
    },
    calendarStock: {
      type: null,
      observer: 'updateStockNum',
    },
  },

  data: {
    min: 1,
    max: Number.MAX_VALUE,
    stockNum: Number.MAX_VALUE,
    limitType: LIMIT_TYPE.STOCK_LIMIT,
  },

  methods: {
    // 数据变更影响链路， sku.stockNum/selectedSkuComb -> stockNum -> max -> selectedNum
    updateStockNum() {
      const { selectedSkuComb, sku, calendarStock } = this.data;

      let stockNum = 0;
      if (typeof calendarStock === 'number') {
        stockNum = calendarStock;
      } else {
        stockNum =
          (selectedSkuComb && selectedSkuComb.stockNum) ||
          (sku && sku.stockNum);
      }

      if (typeof stockNum === 'number') {
        const stepperData = this.updateStepperLimit(stockNum);
        this.setYZData({ stockNum, ...stepperData });
      }
    },

    updateStepperLimit(stockNum) {
      const { quota, quotaUsed, selectedNum, startSaleNum = 1 } = this.data;
      const quotaLimit = quota - quotaUsed;
      let max;
      let limitType;
      const min = startSaleNum < 1 ? 1 : startSaleNum;

      // 无限购时直接取库存，有限购时取限购数和库存数中小的那个
      if (quota > 0 && quotaLimit <= stockNum) {
        // 修正负的limit
        max = quotaLimit < 0 ? 0 : quotaLimit;
        limitType = LIMIT_TYPE.QUOTA_LIMIT;
      } else {
        max = stockNum;
        limitType = LIMIT_TYPE.STOCK_LIMIT;
      }

      // 如果选择小于起售，则强制变为起售
      if (selectedNum < min || min > max) {
        this.updateSelectedNum(min);
      } else if (selectedNum > max) {
        // 当前选择数量大于最大可选时，需要重置已选数量
        this.updateSelectedNum(max);
      }

      this.onInitState({
        valid: min <= max,
        min,
        max,
        limitType,
      });

      return { min, max, limitType };
    },

    updateSelectedNum(num) {
      this.triggerEvent('sku:numChange', num);
    },

    onSelectNumChange(e) {
      const value = e.detail;
      this.updateSelectedNum(value);
    },

    onOverLimit(e) {
      const action = e.detail;
      const {
        quota,
        quotaUsed,
        quotaCycle,
        limitType,
        startSaleNum,
      } = this.data;

      this.triggerEvent('sku:overlimit', {
        action,
        quota,
        quotaUsed,
        quotaCycle,
        limitType,
        startSaleNum,
      });
    },

    onInitState(data) {
      const {
        quota,
        quotaUsed,
        quotaCycle,
        limitType,
        startSaleNum,
      } = this.data;
      this.triggerEvent('sku:initstate', {
        quota,
        quotaUsed,
        quotaCycle,
        limitType,
        startSaleNum,
        ...data,
      });
    },
  },
});
