import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import get from '@youzan/weapp-utils/lib/get';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import upload from 'shared/utils/upload';
import skuBehavior from '../common/sku-behavior';
import {
  MESSAGE_PLACEHOLDER,
  MESSAGE_FIELD_TYPE,
  MESSAGE_IMAGE_MAX_SIZE,
} from '../common/constants';

VanxComponent({
  behaviors: [skuBehavior],

  properties: {
    // 组件外部传入
    messages: {
      type: Array,
      observer: 'resetMassages',
    },
    messageConfig: {
      type: Object,
      observer: 'resetMessageConfig',
    },
  },

  data: {
    messageValues: [],
    messagesInfo: [],
  },

  attached() {
    this.triggerEvent('sku:addRef', {
      refKey: 'skuMessages',
      ref: this,
    });
  },

  methods: {
    showDatePicker(e) {
      const index = get(e, 'target.dataset.index', -1);
      if (index > -1) {
        const message = this.data.messages[index];
        this.triggerEvent('sku:showDatePicker', {
          type: +message.datetime === 1 ? 'datetime' : message.type,
          index,
          currentDate: this.data.messageValues[index].value,
        });
      }
    },

    confirmDate(e) {
      const { index, currentDate } = e.detail;
      if (index > -1) {
        this.setYZData({
          [`messageValues[${index}].value`]: currentDate,
        });
      }
    },

    resetMassages(val) {
      this.resetData(val, this.data.messageConfig);
    },

    resetMessageConfig(val) {
      this.resetData(this.data.messages, val);
    },

    resetData(messages, messageConfig) {
      const messageValues = [];
      const messagesInfo = [];
      const initialMessages = get(messageConfig, 'initialMessages', {});
      const placeholderMap = get(messageConfig, 'placeholderMap', {});
      (messages || []).forEach((message) => {
        const messageValue = {
          value: initialMessages[message.name] || '',
        };
        const messageInfo = {
          ...message,
          type: this.getType(message),
          placeholder: this.getPlaceholder(message, placeholderMap),
          required: +message.required === 1,
          center: true,
        };
        if (message.type === 'image') {
          messageValue.fileList = [];
          messageInfo.maxSize =
            (message.maxSize || MESSAGE_IMAGE_MAX_SIZE) * 1024 * 1024;
        }
        // TODO 小程序暂时不支持多行文本
        // if (+message.multiple === 1) {
        //   messageInfo.fixed = true;
        //   messageInfo.center = false;
        //   messageInfo.autoSize = { maxHeight: 70, minHeight: 70 };
        // }
        messageValues.push(messageValue);
        messagesInfo.push(messageInfo);
      });
      this.setYZData({
        messageValues,
        messagesInfo,
      });
    },

    getType(message) {
      // TODO 小程序暂时不支持多行文本
      // const type = +message.multiple === 1 ? 'textarea' : message.type;
      const type = message.type;
      return MESSAGE_FIELD_TYPE[type] || type;
    },

    getPlaceholder(message, placeholderMap = {}) {
      // TODO 小程序暂时不支持多行文本
      // const type = +message.multiple === 1 ? 'textarea' : message.type;
      const type = message.type;
      return (
        message.placeholder || placeholderMap[type] || MESSAGE_PLACEHOLDER[type]
      );
    },

    onMessageValueChange(e) {
      const { index } = e.currentTarget.dataset;

      this.setYZData({
        [`messageValues[${index}].value`]: e.detail,
      });
    },

    onUploadAfterRead(e) {
      const index = e.currentTarget.dataset.index;
      const file = e.detail.file;
      this.setYZData({
        [`messageValues[${index}].fileList`]: [
          {
            status: 'uploading',
            message: '上传中...',
          },
        ],
      });
      upload({
        file: file.url,
        success: (data) => {
          const url = data.attachment_url;

          this.setYZData({
            [`messageValues[${index}].value`]: cdnImage(url),
            [`messageValues[${index}].fileList`]: [
              {
                status: 'done',
                url: cdnImage(url, '!120x120.jpg'),
                isImage: true,
              },
            ],
          });
        },
        fail: (res) => {
          wx.showToast({
            title: res.msg,
            icon: 'none',
          });

          this.setYZData({
            [`messageValues[${index}].fileList`]: [
              {
                status: 'failed',
                message: '上传失败',
              },
            ],
          });
        },
      });
    },

    onUploadOversize(e) {
      const index = e.currentTarget.dataset.index;
      const { maxSize } = this.data.messages[index] || {};
      wx.showToast({
        title: `最大可上传图片为${
          maxSize || MESSAGE_IMAGE_MAX_SIZE
        }MB，请尝试压缩图片尺寸`,
        icon: 'none',
      });
    },

    onUploadDelete(e) {
      const index = e.currentTarget.dataset.index;
      this.setYZData({
        [`messageValues[${index}].value`]: '',
        [`messageValues[${index}].fileList`]: [],
      });
    },

    getCartMessages() {
      const messages = this.data.messageValues.map((item) => {
        return item.value;
      });
      return messages;
    },

    getMessages() {
      const returnMessages = {};
      const { messages = [], messageValues = [] } = this.data;

      messages.forEach((item, index) => {
        const msg = messageValues[index];
        if (msg && msg.value) {
          returnMessages[`message_${index}`] = msg.value;
        } else {
          returnMessages[`message_${index}`] = '';
        }
      });

      return returnMessages;
    },

    validateMessages() {
      const values = this.data.messageValues;

      for (let i = 0; i < values.length; i++) {
        let value = values[i].value;
        value = value.trim ? value.trim() : value;
        const message = this.data.messages[i];

        if (value === '') {
          // 必填字段的校验
          if (+message.required === 1) {
            const textType = message.type === 'image' ? '请上传' : '请填写';
            return textType + message.name;
          }
        } else {
          const errorMsg = this.validateMessage(message.type, value);
          if (errorMsg) return errorMsg;
        }

        if (value.length > 200) {
          return '写的太多了，不要超过200字';
        }
      }
    },

    validateMessage(type, value) {
      const msg = '';

      const validateObj = {
        mobile: /^\d{6,20}$/,
        email: /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/,
        tel: /^\d+$/,
        id_no: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
      };
      const validateMsg = {
        mobile: '手机号请填写6-20位的数字',
        email: '请填写正确的邮箱',
        tel: '请填写数字',
        id_no: '请填写正确的身份证',
      };
      const rule = validateObj[type];

      if (rule && !rule.test(value)) {
        return validateMsg[type];
      }

      return msg;
    },
  },
});
