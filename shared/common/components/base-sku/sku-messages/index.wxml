<view class="van-sku-messages">
  <block
    wx:for="{{ messagesInfo }}"
    wx:for-item="message"
    wx:key="name">

    <van-cell
      wx:if="{{ message.type === 'image' }}"
      title-width="6.2em"
      title-style="margin-right: 16px;"
      title="{{ message.name }}"
      required="{{ message.required }}"
      title-class="image-cell-title"
      value-class="image-cell-value"
    >
      <van-uploader
        max-count="1"
        preview-size="80"
        data-index="{{ index }}"
        max-size="{{ message.maxSize }}"
        file-list="{{ messageValues[index].fileList }}"
        sizeType="{{ ['compressed'] }}"
        bind:after-read="onUploadAfterRead"
        bind:oversize="onUploadOversize"
        bind:delete="onUploadDelete"
        />
      <view class="image-cell-label">仅限一张</view>
    </van-cell>

    <van-field
      wx:elif="{{message.type === 'time'}}"
      readonly
      is-link
      center
      placeholder="{{ message.placeholder }}"
      label="{{ message.name }}"
      required="{{ message.required }}"
      value="{{ messageValues[index].value }}"
      data-index="{{ index }}"
      bind:tap="showDatePicker"
    />

    <view  wx:elif="{{message.type === 'idcard'}}" class="sku-messages_id-card-container">
          <van-field
            border="{{false}}"
            type="{{ message.type }}"
            fixed="{{ message.fixed }}"
            center="{{ message.center }}"
            autosize="{{ message.autoSize }}"
            placeholder="{{ message.placeholder }}"
            label="{{ message.name }}"
            required="{{ message.required }}"
            value="{{ messageValues[index].value }}"
            data-index="{{ index }}"
            maxlength="200"
            bind:change="onMessageValueChange"
        />
         <view class="sku-messages_id-card-msg">
          身份证号码为敏感信息，系统将会对其进行安全处理，请放心。如对收集原因有疑问，请联系商家。
        </view>
    </view>

    <van-field
      wx:else
      type="{{ message.type }}"
      fixed="{{ message.fixed }}"
      center="{{ message.center }}"
      autosize="{{ message.autoSize }}"
      placeholder="{{ message.placeholder }}"
      label="{{ message.name }}"
      required="{{ message.required }}"
      value="{{ messageValues[index].value }}"
      data-index="{{ index }}"
      maxlength="200"
      bind:change="onMessageValueChange"
      />
  </block>
</view>
