<wxs src="./index.wxs" module="tools"></wxs>
<!-- 头部 开始 -->
<view class="m-calendar-header">
  <view 
    wx:key="{{ item.paramName }}" 
    wx:for="{{ monthList }}"
    wx:for-index="idx"
  >
    <theme-view 
      custom-class="m-calendar-select" 
      border="{{activeMonth === idx ? 'general' : ''}}"
      data-year="{{item.year}}"
      data-month="{{item.month}}"
      data-index="{{idx}}"
      bindtap="onChangeMonth"
    >
      <view class="u-month-show">
        {{ item.month }}月
      </view>
      <theme-view custom-class="u-price-show" color="general">
        {{ nearlyFourMonthMinPriceMap[item.paramName] !== -1 ? ('￥' + tools.price(nearlyFourMonthMinPriceMap[item.paramName]) + '起') : '' }}
      </theme-view>
    </theme-view>
  </view>
</view>
<!-- 头部 结束 -->

<!-- 星期列表 开始 -->
<view class="m-calendar-week-list">
  <view 
    wx:key="weekList" 
    wx:for="{{ weekList }}" 
    class="u-week-item {{ item === '六' || item === '日' ? 'u-week-item-week' : ''}}"
  >
    {{ item }}
  </view>
</view>
<!-- 星期列表 结束 -->

<!-- 日期列表 开始 -->
<view class="m-calendar-date-list">
  <view 
    wx:key="dateList" 
    wx:for="{{ dateList }}" 
    class="u-data-item-box" 
    data-year="{{ currentYear }}" 
    data-month="{{ currentMonth }}" 
    data-date="{{ item.taskDate }}"
    data-infodate="{{ item.infoDate }}"
    data-activityprice="{{ item.info.activityPrice ? item.info.activityPrice : item.info.originPrice }}"
    data-originprice="{{ item.info.originPrice }}"
    data-disable="{{ item.info.isEnable === 0 }}"
    data-dateskuid="{{ item.info.dateSkuId }}"
    data-stocknum="{{ item.info.stockNum }}"
    bindtap="onDateItemClick"
  >
    <theme-view
      wx:if="{{ item.taskDate }}" 
      custom-class="u-date-item"
      bg="{{ item.active ? 'main-bg' : '' }}"
    >
      <theme-view custom-class="u-date-item-left" color="{{ item.active ? 'main-text' : 'main-bg' }}">
        {{ item.info.isEnable === 1 ? '余'+item.info.stockNum : '' }}
      </theme-view>
      <theme-view custom-class="{{ item.info.isEnable === 1 ? '' : 'u-date-item-date' }}" color="{{ item.active ? 'main-text' : '' }}">
        {{ item.taskDate }}
      </theme-view>
      <theme-view custom-class="u-date-item-price" color="{{ item.active ? 'main-text' : '' }}">
        {{ item.info.isEnable === 1 ? '￥'+tools.price(item.info.originPrice) : '' }}
      </theme-view>
    </theme-view>
  </view>
</view>
<!-- 日期列表 结束 -->