# 日历组件

## API
| 属性 / 方法 | 类型 | 默认值 | 选项 | 说明 |
| --- | --- | --- | --- | --- |
| pre | Boolean | true | 可选 | 是否显示上个月按钮 |
| next | Boolean | true | 可选 | 是否显示下个月按钮 |
| currentYear | String | 当前年份 | 可选 | 初始化日历显示的年份 |
| currentMonth | String | 当前月份 | 可选 | 初始化日历显示的月份 |
| activeStyle | String | "" | 可选 | 点击某个日期，激活时的style |
| dateItemClick | Func | 无 | 可选 | 点击某个日期，暴露的钩子 |

## DEMO
```html
<calendar
  pre="{{ false }}"
  next="{{ false }}"
  currentYear="2018"
  currentMonth="9"
  activeStyle="color: #FFFFFF; background-color: #00B389;"
  bind:dateItemClick="onDateItemClick"
/>
```

```javascript
onDateItemClick(event) {
  console.log(event.detail); // event.detail: { year, month, date }，当前点击的年月日
}
```

## 效果
注：红框内为```<calendar>```日历组件，红框外为外置自定义内容

![图一](https://b.yzcdn.cn/public_files/2018/10/11/813e69606a1f5b71e2a3d466d5d73133.png)
![图二](https://b.yzcdn.cn/public_files/2018/10/11/6cd20ca04f61631c2c357da5b1e36940.png)
