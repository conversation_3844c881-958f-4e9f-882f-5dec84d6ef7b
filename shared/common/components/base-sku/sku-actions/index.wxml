<view class="{{ themeClass }} van-sku-actions">
  <user-authorize
    scene="add_shopping_car"
    allowDeny="{{ allowDeny }}"
    wx:if="{{ showAddCartBtn }}"
    bind:next="onAddCartClicked"
    popupCustomStyle="{{ popupCustomStyle }}"
    class="van-sku-actions__btn-wrap {{ showAddCartBtn ? 'btn-first' : '' }}"
  >
    <theme-view
      bg="vice-bg"
      color="vice-text"
      custom-class="van-sku-actions__btn van-sku-actions__btn--vice"
    >
    {{ cartBtnText }}
    </theme-view>
  </user-authorize>
  <user-authorize
    scene="click_buy_now"
    wx:if="{{ showBuyBtn }}"
    bind:next="onBuyClicked"
    allowDeny="{{ allowDeny }}"
    popupCustomStyle="{{ popupCustomStyle }}"
    class="van-sku-actions__btn-wrap {{ !showAddCartBtn ? 'btn-first' : '' }}"
  >
    <theme-view
      bg="main-bg"
      custom-class="van-sku-actions__btn van-sku-actions__btn--main"
    >
      {{ buyBtnText }}
    </theme-view>
  </user-authorize>
</view>
