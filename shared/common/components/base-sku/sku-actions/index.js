import WscComponent from 'shared/common/base/wsc-component';
import skuBehavior from '../common/sku-behavior';

WscComponent({
  behaviors: [skuBehavior],

  properties: {
    buyBtnText: String,
    cartBtnText: String,
    cartCount: Number,
    showBuyBtn: {
      type: Boolean,
      value: true,
    },
    popupCustomStyle: {
      type: String,
      value: '',
    },
    showAddCartBtn: Boolean,
    allowDeny: {
      type: Boolean,
      value: false,
    },
  },

  methods: {
    onAddCartClicked() {
      this.triggerEvent('sku:addCart');
    },

    onBuyClicked() {
      this.triggerEvent('sku:buy');
    },
  },
});
