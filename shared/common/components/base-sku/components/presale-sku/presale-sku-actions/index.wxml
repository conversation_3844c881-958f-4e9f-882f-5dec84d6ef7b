<view
  wx:if="{{ extraData && extraData.presaleInfo }}"
  class="van-sku-actions"
>
  <view class="van-sku-actions__btn van-sku-actions__btn--presale van-hairline--top">
    <view class="van-sku-actions__time-wrap">
      <view class="van-sku-actions__presale-time">{{ extraData.presaleInfo.presaleEndTimeStr }}</view>
      <view class="van-sku-actions__presale-time">{{ extraData.presaleInfo.presaleShipTimeStr }}</view>
    </view>
  </view>
  <view
    wx:if="{{ extraData.presaleInfo.isPresaleOutDate }}"
    class="van-sku-actions__btn van-sku-actions__btn--disabled"
  >
    已结束
  </view>
  <user-authorize
    wx:else
    class="van-sku-actions__btn-wrap"
    bind:next="onPayPresale"
  >
    <theme-view
      custom-class="van-sku-actions__btn van-sku-actions__btn--main"
      bg="main-bg"
    >
      支付定金
    </theme-view>
  </user-authorize>
</view>
