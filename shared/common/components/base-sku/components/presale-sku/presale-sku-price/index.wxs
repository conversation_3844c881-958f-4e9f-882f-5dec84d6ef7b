function presaleCentToYuan(val) {
  // 抹去分
  if (val > 10) {
    val -= (val % 10);
  }
  // 预售商品不足一分返回为1分
  if (val < 1) {
    val = 1;
  }

  val /= 100;

  return val.toFixed(2);
}

function getGoodsPrice(selectedSkuComb, sku, isRetailApp) {
  if (!sku) return;

  // sku.price是一个格式化好的价格区间
  var result = sku.price;

  if (selectedSkuComb) {
    result = selectedSkuComb.price;
  }

  // 修复定金预售总价展示异常问题 20230201
  // 参考 微页面sku弹框处理价格处理逻辑 shared/components/feature-sku/components/header-price/index.js 6:38
  // 4年前的代码了，不确定影响面，先加个零售判断，后续微商城可放开
  if (isRetailApp && typeof result === 'number') {
    result = Number(result / 100).toFixed(2);
  }

  return result;
}

module.exports = {
  presaleCentToYuan,
  getGoodsPrice,
};
