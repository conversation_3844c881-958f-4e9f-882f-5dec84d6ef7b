<template name="sku">
  <view class="van-sku-container van-sku-container-class">
    <!-- sku header 商品标题、价格、图片信息 -->
    <sku-header
      quota="{{ quota }}"
      hide-stock="{{ hideStock }}"
      quota-text="{{ quotaText }}"
      quota-used="{{ quotaUsed }}"
      quota-cycle="{{ quotaCycle }}"
      theme-class="{{ themeClass }}"
      goods="{{ goods }}"
      sku="{{ sku }}"
      selected-sku="{{ selectedSku }}"
      selected-sku-comb="{{ selectedSkuComb }}"
      extra-data="{{ extraData }}"
      properties="{{ currentSkuProperties }}"
      selectedProp="{{ selectedProp }}"
      bind:sku:close="onClose"
    >
      <sku-header-price
        theme-class="{{ themeClass }}"
        goods="{{ goods }}"
        sku="{{ sku }}"
        selected-sku="{{ selectedSku }}"
        selected-sku-comb="{{ selectedSkuComb }}"
        extra-data="{{ extraData }}"
      ></sku-header-price>
    </sku-header>

    <!-- sku body -->
    <view class="van-sku-body van-sku-body-class" style="{{ showDatePicker ? 'overflow-y: visible;' : '' }}">
      <!-- sku选择区域 -->
      <slot wx:if="{{ extraData.useCustomSkuGroup }}" name="sku-group"></slot>
      <block wx:else>
        <view
          wx:if="{{ !sku.noneSku || currentSkuProperties.length > 0 }}"
          class="van-sku-group-container"
        >
          <sku-row
            wx:for="{{ sku.tree }}"
            wx:for-item="skuTreeItem"
            wx:key="index"
            index="{{ index }}"
            theme-class="{{ themeClass }}"
            sku-row="{{ skuTreeItem }}"
            last-child="{{ index === sku.tree.length - 1 && currentSkuProperties.length === 0 }}"
            selected-sku="{{ selectedSku }}"
            selected-sku-comb="{{ selectedSkuComb }}"
            extra-data="{{ extraData }}"
          >
            <sku-row-item
              wx:for="{{ skuTreeItem.v }}"
              wx:for-item="skuValue"
              wx:key="index"
              theme-class="{{ themeClass }}"
              sku-value="{{ skuValue }}"
              sku-key-str="{{ skuTreeItem.kS }}"
              sku-list="{{ simpleSkuList }}"
              selected-sku="{{ selectedSku }}"
              selected-sku-comb="{{ selectedSkuComb }}"
              disable-soldout-sku="{{ disableSoldoutSku }}"
              bind:sku:select="onSelect"
            >
            </sku-row-item>
          </sku-row>
          <sku-row
            wx:for="{{ currentSkuProperties }}"
            wx:for-item="propItem"
            wx:key="index"
            index="{{ index }}"
            theme-class="{{ themeClass }}"
            sku-row="{{ propItem }}"
            selected-sku="{{ selectedProp }}"
          >
            <sku-row-prop-item
              wx:for="{{ propItem.v }}"
              wx:for-item="propValue"
              wx:key="index"
              theme-class="{{ themeClass }}"
              sku-value="{{ propValue }}"
              sku-key-str="{{ propItem.kId }}"
              selected-prop="{{ selectedProp }}"
              multiple="{{ propItem.isMultiple }}"
              disabled="{{ propValue.textStatus === 0 }}"
              bind:sku:propSelect="onPropSelect"
            >
            </sku-row-prop-item>
          </sku-row>
        </view>
      </block>

      <!-- 扩展sku选择区 -->
      <sku-group-extra
        theme-class="{{ themeClass }}"
        sku="{{ sku }}"
        selected-sku="{{ selectedSku }}"
        selected-sku-comb="{{ selectedSkuComb }}"
        extra-data="{{ extraData }}"
      ></sku-group-extra>
      <!-- 支持抽象组件和slot两种方式，按需使用 -->
      <slot name="extra-sku-group"></slot>
      <view class="calendar-container" wx:if="{{ (isVirtualTicket && virtualTicket.validityType === 3) || isPriceCalendar }}">
        <view class="calendar-container-box">
          <view class="calendar-header-tip">
            <text class="tip-text">选择日期</text>
            <text wx:if="{{ virtualTicket.itemPreOrderTime }}">{{ virtualTicket.itemPreOrderTimeStr }}</text>
          </view>
          <price-calendar
            id="price-calendar-component"
            nearly-four-months-price-range="{{ priceCalendarData.nearlyFourMonthsPriceRange }}"
            nearly-four-month-min-price-map="{{ priceCalendarData.nearlyFourMonthMinPriceMap }}"
            nearly-four-day-marketable-map="{{ priceCalendarData.nearlyFourDayMarketableMap }}"
            ecard-price-calendar-model-map="{{ priceCalendarData.ecardPriceCalendarModelMap }}"
            activeClass="calendar-active-class"
            bind:dateItemClick="onDateItemClick"
            bind:dateItemReset="onDateItemReset"
          />
        </view>
      </view>
      <!-- 数量选择 -->
      <sku-stepper
        wx:if="{{ !extraData.hideSkuStepper }}"
        theme-class="{{ themeClass }}"
        sku="{{ sku }}"
        quota="{{ quota }}"
        hide-stock="{{ hideStock }}"
        quota-text="{{ quotaText }}"
        quota-used="{{ quotaUsed }}"
        quota-cycle="{{ quotaCycle }}"
        sku-stock-num="{{ sku.stockNum }}"
        calendar-stock="{{ calendarStock }}"
        selected-sku="{{ selectedSku }}"
        selected-sku-comb="{{ selectedSkuComb }}"
        extra-data="{{ extraData }}"
        selected-num="{{ selectedNum }}"
        stepper-title="{{ stepperTitle }}"
        quota="{{ quota }}"
        quota-text="{{ quotaText }}"
        quota-used="{{ quotaUsed }}"
        quota-cycle="{{ quotaCycle }}"
        start-sale-num="{{ startSaleNum }}"
        hide-stock="{{ hideStock }}"
        bind:sku:numChange="onNumChange"
        bind:sku:overlimit="onOverLimit"
        bind:sku:initstate="onInitState"
      ></sku-stepper>

      <!-- 数量步进器下边内容，满足组合商品，视觉需求 -->
      <sku-stepper-bottom-block
        sku="{{ sku }}"
        selected-sku="{{ selectedSku }}"
        selected-sku-comb="{{ selectedSkuComb }}"
        extra-data="{{ extraData }}"
      />

      <slot name="sku-stepper-bottom-block"></slot>

      <sku-extra
        wx:if="{{ sku.birthdayInfo }}"
        extra="{{ sku.birthdayInfo }}"
        bind:onChooseRelation="onChooseRelation"
      />

      <!-- sku留言 -->
      <sku-messages
        theme-class="{{ themeClass }}"
        sku="{{ sku }}"
        selected-sku="{{ selectedSku }}"
        selected-sku-comb="{{ selectedSkuComb }}"
        extra-data="{{ extraData }}"
        messages="{{ sku.messages }}"
        message-config="{{ messageConfig }}"
        bind:sku:addRef="addSkuMsgRef"
        bind:sku:showDatePicker="showDateTimePicker"
      ></sku-messages>
    </view>

    <slot name="sku-actions-top" />
    <slot wx:if="{{ extraData.useCustomSkuActions }}" name="sku-actions"></slot>
    <sku-actions
      wx:else
      theme-class="{{ themeClass }}"
      sku="{{ sku }}"
      cartCount="{{ cartCount }}"
      selected-sku="{{ selectedSku }}"
      selected-sku-comb="{{ selectedSkuComb }}"
      extra-data="{{ extraData }}"
      buy-btn-text="{{ buyText }}"
      cart-btn-text="{{ cartText }}"
      show-buy-btn="{{ showBuyBtn }}"
      allow-deny="{{ !goods.isHaitao }}"
      show-add-cart-btn="{{ showAddCartBtn }}"
      bind:sku:addCart="onAddCart"
      bind:sku:buy="onBuy"
      popup-custom-style="{{ authorizePopupCustomStyle }}"

    ></sku-actions>
  </view>
</template>

<!--z-index 顺序 修改请查看 docs/z-index.md-->
<van-popup
  wx:if="{{ usePopup }}"
  position="bottom"
  round
  custom-style="overflow-y:visible;bottom: {{ popupBottom }}px;"
  show="{{ show }}"
  z-index="{{ zIndex }}"
  bind:close="onClose"
>
  <template is="sku" data="{{ simpleSkuList,themeClass, sku, calendarStock, goods, extraData, stepperTitle, quota, quotaText, quotaUsed, quotaCycle, hideStock, buyText, cartText, resetStepperOnHide, showAddCartBtn, showBuyBtn, bodyOffsetTop, show, selectedSku, selectedNum, selectedSkuComb, refs, cartCount, isVirtualTicket, virtualTicket, isPriceCalendar, priceCalendarData, showDatePicker, startSaleNum, messageConfig, currentSkuProperties, selectedProp, disableSoldoutSku, authorizePopupCustomStyle }}"></template>
</van-popup>
<template wx:else is="sku" data="{{ simpleSkuList,themeClass, sku, calendarStock, goods, extraData, stepperTitle, quota, quotaText, quotaUsed, quotaCycle, hideStock, buyText, cartText, resetStepperOnHide, showAddCartBtn, showBuyBtn, bodyOffsetTop, show, selectedSku, selectedNum, selectedSkuComb, refs, cartCount, isVirtualTicket, virtualTicket, isPriceCalendar, priceCalendarData, startSaleNum, messageConfig, currentSkuProperties, selectedProp, disableSoldoutSku, authorizePopupCustomStyle }}"></template>

<sku-datepicker
  show="{{ showDatePicker }}"
  z-index="{{ zIndex + 1 }}"
  info="{{ datePickerData }}"
  bind:close="closeDateTimePicker"
  bind:confirm="confirmDateTimePicker"
  />
