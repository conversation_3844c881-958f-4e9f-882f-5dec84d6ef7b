@import "shared/common/css/helper/index.wxss";

.van-sku-container {
  width: 100vw;
  background: #fff;
  border-radius: 20px 20px 0 0;
}

.van-sku-body {
  max-height: 350px;
  overflow-x: hidden;
  overflow-y: auto;
  flex: 1;
}

.van-sku-body::-webkit-scrollbar {
  display: none
}

.van-sku-group-container {
  margin-left: 16px;
  padding: 12px 0 0px;
}

@media (max-width: 320px) {
  .van-sku-body {
    max-height: 280px;
  }
}

.calendar-container{
  position: relative;
  background-color: #F2F2F2;
  padding: 10px 0;
}

.calendar-container .calendar-container-box{
  background-color: #fff;
}

.calendar-header-tip{
  padding: 10px 15px;
  font-size: 12px;
  color: #9C9C9C;
}

.calendar-header-tip .tip-text{
  font-size: 14px;
  color: #111;
  margin-right: 5px;
}

.sku-row__last-child {
  margin-bottom: 12px;
}
