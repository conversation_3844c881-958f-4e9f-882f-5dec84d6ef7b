import WscComponent from 'shared/common/base/wsc-component';
import skuBehavior from '../common/sku-behavior';

WscComponent({
  behaviors: [skuBehavior],

  properties: {
    extra: {
      type: Object,
    },
  },

  data: {
    chosen: '',
    msgValue: '',
    isShowField: false,
  },

  methods: {
    onSelect(e) {
      const { dataset } = e.currentTarget;
      const { value: chosen = '' } = dataset;
      const { blessingMessage } = this.data.extra;

      this.setYZData({
        chosen,
        msgValue: blessingMessage[chosen],
        isShowField: !!blessingMessage[chosen],
      });
      this.sync();
    },
    onInput(payload) {
      const msgValue = payload.detail;

      this.setYZData({ msgValue });
      this.sync();
    },
    sync() {
      const { chosen, msgValue, extra } = this.data;
      const { customerRelation } = extra;

      this.triggerEvent('onChooseRelation', {
        BIRTHDAY_RELATION_NET_ORDER_MARK: 'true',
        RELATION_TYPE_NAME: customerRelation[chosen],
        RELATION_BLESSING: msgValue,
        RELATION_TYPE_ID: chosen,
      });
    },
  },
});
