<view class="extra van-hairline--top">
  <view class="extra-title">
    {{ extra.greetingCardName }}
    <view class="extra-title__tip">{{extra.remark ? '('+extra.remark+')' : ''}}</view>
  </view>
  <view class="extra-body">
    <theme-view
      wx:for="{{ extra.customerRelation }}"
      wx:for-index="key"
      wx:for-item="value"
      wx:key="key"
      data-value="{{ key }}"
      custom-class="extra-body__item"
      bg="{{ key === chosen ? 'main-bg' : '' }}"
      color="{{ key === chosen ? 'general' : '' }}"
      bindtap="onSelect"
      opacity="0.1"
    >
      {{ value }}
    </theme-view>
  </view>
  <view 
    wx:if="{{ isShowField }}"
    class="van-hairline--bottom van-hairline--top"
  >
    <van-field
      placeholder="请输入祝福语"
      border="{{ false }}"
      maxlength="{{ 100 }}"
      value="{{ msgValue }}"
      custom-style="padding: 12px 0"
      bind:input="onInput"
    >
      <view slot="label" class="blessing-label">
        祝福语
      </view>
    </van-field>
  </view>
</view>