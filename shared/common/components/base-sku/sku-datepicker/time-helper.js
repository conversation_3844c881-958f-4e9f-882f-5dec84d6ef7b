function length2(int) {
  return int > 9 ? `${int}` : `0${int}`;
}

// 字符串转 Date
// 只处理 YYYY-MM-DD 或者 YYYY-MM-DD HH:MM 格式
export function stringToTimestamp(timeString) {
  if (!timeString) {
    return null;
  }
  return new Date(timeString.replace(/-/g, '/')).getTime();
}

// Date 转字符串
// type: date or datetime
export function timestampToString(timestamp, type = 'date') {
  if (!timestamp) {
    return '';
  }
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  let timeString = `${year}-${length2(month)}-${length2(day)}`;
  if (type === 'datetime') {
    const hours = date.getHours();
    const minute = date.getMinutes();
    timeString += ` ${length2(hours)}:${length2(minute)}`;
  }
  return timeString;
}
