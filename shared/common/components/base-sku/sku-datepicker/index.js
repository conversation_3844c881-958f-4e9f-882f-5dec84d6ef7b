import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { stringToTimestamp, timestampToString } from './time-helper';
import { MESSAGE_DATEPICKER_TITLE, MESSAGE_DATEPICKER_FORMAT } from '../common/constants';

VanxComponent({
  properties: {
    // 组件外部传入
    zIndex: {
      type: Number,
      default: 1
    },
    show: {
      type: Boolean,
      default: false,
      observer: 'onShowChange'
    },
    info: {
      type: Object,
      observer: 'onInfoChange'
    }
  },

  data: {
    currentDate: '',
    minDate: new Date(new Date().getFullYear() - 60, 0, 1).getTime(),
    type: 'time',
    showPicker: false,
    title: MESSAGE_DATEPICKER_TITLE.time,
    formatter(type, value) {
      return `${value}${MESSAGE_DATEPICKER_FORMAT[type]}`;
    }
  },

  methods: {
    onShowChange(val) {
      if (val) {
        this.setYZData({
          showPicker: true
        });
      }
    },

    hidePicker() {
      this.setYZData({
        showPicker: false
      });
    },

    getCurrentDate(val, type) {
      let currentDate = '';
      switch (type) {
        case 'time':
          currentDate = val;
          break;
        case 'date':
        case 'datetime':
          currentDate = stringToTimestamp(val) || new Date().getTime();
          break;
        default:
          break;
      }
      return currentDate;
    },

    onInfoChange(val) {
      const type = val.type || 'time';
      this.setYZData({
        type,
        title: MESSAGE_DATEPICKER_TITLE[type],
        currentDate: this.getCurrentDate(val.currentDate, type)
      });
    },

    closeDateTimePicker() {
      this.triggerEvent('close');
    },

    confirmDateTimePicker(ev) {
      let currentDate = ev.detail;
      if (this.data.type !== 'time') {
        currentDate = timestampToString(currentDate, this.data.type);
      }
      this.triggerEvent('confirm', {
        ...this.data.info,
        currentDate
      });
      this.closeDateTimePicker();
    }
  }
});
