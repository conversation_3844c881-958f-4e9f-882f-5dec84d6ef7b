import navigate from 'shared/utils/navigate';
import getApp from 'shared/utils/get-safe-app';
import logv3 from 'utils/log/logv3';
import { node as request } from 'shared/utils/request';
import { formatBuyData, formatAddCartData } from './sku-trade-param';

const app = getApp();

const defaultBuyLogOptions = {
  actName: 'buy',
  et: 'click',
  ei: 'buy',
  en: '购买',
};

/**
 * 跳转下单页辅助函数--单个商品
 * @param  {Object} options
 * @param  {String} options.goodsId 商品ID
 * @param  {Array}  options.num 商品数量
 * @param  {Number} options.skuId
 * @param  {Number} options.price 商品价格
 * @param  {Array} options.messages 留言消息
 * ---- 以上base-sku bind:buy会返回
 * @param  {Boolean} options.isPoints 是否积分商品
 * @param  {Number} options.pointsPrice 积分商品积分价格
 * @param  {Number} options.remainPrice 积分商品剩余价格
 * @param  {String} options.orderFrom 下单来源
 * @param  {Number} options.postage 运费
 * @param  {String} options.activityAlias 活动信息
 * @param  {Number} options.activityId 活动信息
 * @param  {Number} options.activityType 活动信息
 * @return {Promise}
 * /wsctrade/order/goodsBook.json 提交参数如
 */
// 购买单个商品
export function goToBuySingle(postData = {}) {
  const { et, ei, en } = defaultBuyLogOptions;

  app.logger &&
    app.logger.log({
      et,
      ei,
      en,
      params: {
        price: (postData.price || 0) * 100,
        goods_id: postData.goodsId,
        goods_name: postData.itemDataModel?.title,
        num: postData.num,
        sku_id: postData.skuId,
      },
      si: app.getKdtId(),
    });
  const params = formatBuyData(postData);
  return goToBuy(params);
}

function hookCommonEvent(type, data) {
  const page = getCurrentPages().pop();
  if (page && page[type]) {
    return page[type](data);
  }
  return Promise.resolve();
}

/**
 * 跳转下单页辅助函数--多个个商品
 * @param {*} options
 * @param {Array} options.goodsList
 * @param {Object} options.extraData 下单需要的额外参数（activityAlias之类的）
 * orderFrom、postage都在extraData中
 */
export function goToBuy(options = {}) {
  const { goodsList = [], extraData = {} } = options;

  if (!goodsList || goodsList.length === 0) {
    wx.showToast({
      title: '购买未选择商品',
      icon: 'none',
      duration: 2000,
    });
    return;
  }

  // 多网点
  const storeId = extraData.storeId || extraData.store_id || app.getOfflineId();
  if (storeId) {
    extraData.store_id = storeId;
  }

  wx.showLoading();
  return handleCoupon(extraData)
    .then(() =>
      hookCommonEvent('beforeBuyEvent', {
        ...(goodsList[0] || {}),
      })
    )
    .then(() =>
      request({
        header: {
          'content-type': 'application/x-www-form-urlencoded',
        },
        path: '/wsctrade/order/goodsBook.json',
        data: {
          goodsList: JSON.stringify(goodsList),
          common: JSON.stringify(extraData),
          biz_trace_point_ext: logv3.getTradeLog(),
        },
        method: 'POST',
      })
    )
    .then((res) => {
      const { bookKey } = res;
      if (bookKey) {
        let url = `/packages/order/index?bookKey=${bookKey}`;
        if (extraData.orderFrom) {
          url += `&orderFrom=${extraData.orderFrom}`;
        }
        return hookCommonEvent('afterBuyEvent', {
          goodsList,
          extraData,
          url,
          bookKey,
        })
          .then(() => {
            navigate.navigate({
              url,
            });
            wx.hideLoading();
            return Promise.resolve();
          })
          .catch((e) => {
            wx.hideLoading();
            return Promise.reject(e);
          });
      }
      wx.hideLoading();
      return Promise.reject();
    })
    .catch((err = {}) => {
      wx.hideLoading();
      !err.noToast &&
        wx.showToast({
          title: err.msg || err.message || '购买失败，重新试试',
          icon: 'none',
          duration: 2000,
        });
    });
}

/**
 * 跳转下单页辅助函数
 * @param  {Array}  options.goodsList 商品列表
 * @param  {Object} options.extraData 下单需要的额外参数（activityAlias之类的）
 * @param  {String} orderFrom         下单来源（购物车/商品页）
 * @param  {Object} logOptions        日志参数
 * @return {undefined}
 */
export function goToOrder({
  goodsList = [],
  extraData = {},
  orderFrom = '',
  logOptions = defaultBuyLogOptions,
}) {
  const { et, ei, en } = logOptions;

  const bizTracePointExt = logv3.getTradeLog();

  goodsList = goodsList.map((item) => {
    item.bizTracePointExt = bizTracePointExt;

    return item;
  });

  const dbid = app.db.set({
    type: 'goods',
    goods_list: goodsList,
    ...extraData,
  });

  const goods = goodsList[0] || {};
  app.logger &&
    app.logger.log({
      et,
      ei,
      en,
      params: {
        currentPrice: (goods.price || 0) * 100,
        goods_id: goods.goodsId,
        num: goods.num,
        sku_id: goods.skuId,
      },
      si: app.getKdtId(),
    });

  let url = `/packages/order/index?dbid=${dbid}`;
  if (orderFrom) {
    url += `&orderFrom=${orderFrom}`;
  }

  navigate.navigate({
    url,
  });
}

/**
 * 提交请求加入购物车
 * @param  {Object} options
 * @param  {String} options.goodsId 商品ID
 * @param  {Array}  options.num 商品数量
 * @param  {Number} options.skuId
 * @param  {Number} options.price 商品价格
 * @param  {Array} options.cartMessages 留言消息
 * ---- 以上base-sku bind:add-cart会返回
 * @param  {String} options.orderFrom 下单来源
 * @param  {String} options.activityAlias 活动信息
 * @param  {Number} options.activityId 活动信息
 * @param  {Number} options.activityType 活动信息
 * /wscshop/trade/cart/goods.json
 */

export function addToCart(postData, log = {}, contentType) {
  app.logger &&
    app.logger.log({
      et: log.et || 'click',
      ei: log.ei || 'add_cart',
      en: log.en || '添加购物车',
      params: log.params || {
        goods_id: postData.goodsId,
        num: postData.num,
        sku_id: postData.skuId,
      },
    });

  const data = formatAddCartData(postData);
  const { birthdayInfo = {} } = postData;
  data.extra = JSON.stringify(birthdayInfo);

  return request({
    path: '/wscshop/trade/cart/goods.json',
    header: {
      'content-type': contentType || 'application/x-www-form-urlencoded',
    },
    data,
    method: 'POST',
  });
}

/**
 * 计算价格
 * @param {object} params 传递参数
 * @param {number} params.kdtId 店铺 kdtid
 * @param {number} params.itemId 商品 id
 * @param {number} params.skuId sku id
 * @param {number[]} params.itemSalePropList
 */
export function calcPrice(params) {
  return request({
    path: '/wscgoods/detail-api/calculate-price.json',
    method: 'POST',
    data: {
      ...params,
      itemSalePropList: JSON.stringify(params.itemSalePropList),
    },
  });
}

function getCoupon(item) {
  return request({
    path: '/wsctrade/order/detail/getVoucher.json',
    method: 'POST',
    data: {
      activityId: item.id,
      // 业务名称
      bizName: 'showcase',
      // 来源，可以与业务名称一致
      source: 'wap_showcase',
    },
  });
}

function handleCoupon(sku = {}) {
  const result = { success: true };

  if (sku.couponId) {
    return getCoupon({ id: sku.couponId })
      .then(() => {
        return result;
      })
      .catch(() => {
        return result;
      });
  }

  return Promise.resolve(result);
}
