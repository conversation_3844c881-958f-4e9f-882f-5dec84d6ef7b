import args from '@youzan/weapp-utils/lib/args';
import logv3 from 'utils/log/logv3';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();

// const objectToQuery = (data = {}) => Object.keys(data).map((key) => {
//   return `${key}=${encodeURIComponent(data[key])}`;
// }).join('&');

export function formatBaseData(data) {
  const querys = args.getAll() || {};
  const goodsData = {
    goods_id: data.goodsId,
    num: data.num,
    sku_id: data.skuId,
    price: data.price || 0,
    dc_ps: logv3.getDCPS(),
    qr: data.qr || '',
    tpps: querys.tp_ps || '',
    ...data.messages,
    extensions: data.extensions || {},
    isInstallment: Boolean(data.isInstallment),
    propertyIds: data.propertyIds || []
  };
  const commonData = {
    kdt_id: data.kdtId || app.getKdtId(),
    postage: data.postage || 0,
    activity_alias: data.activityAlias,
    activity_id: data.activityId,
    activity_type: data.activityType,
    use_wxpay: data.useWxpay || 0,
    order_from: data.orderFrom || '',
    cloud_order_ext: data.cloudOrderExt || '',
    couponId: data.couponId,
  };

  if (typeof data.price === 'string' && data.price.indexOf('.' >= 0)) {
    goodsData.price = +data.price * 100;
  }

  // 积分兑换商品
  if (data.isPoints) {
    goodsData.points_price = data.pointsPrice;
    goodsData.price = data.remainPrice;
  }
  // f码商品
  if (data.fcode) {
    goodsData.fcode = data.fcode;
  }
  // 周期购商品
  if (typeof data.deliverTime === 'number') {
    goodsData.deliver_time = data.deliverTime;
  }
  // 广点通来源追踪参数
  if (data.gdtClickId) {
    goodsData.gdt_id = data.gdtClickId;
  }

  if (data.channelId) {
    // 送礼商品channelId 30
    goodsData.channel_id = data.channelId;
  }

  const storeId = data.storeId || app.getOfflineId();
  if (storeId) {
    // 多网点
    commonData.store_id = storeId;
  }

  if (data.umpStepPerson) {
    commonData.ladder_num = data.umpStepPerson;
  }

  // 酒店商品
  if (data.hotelGoods) {
    commonData.hotel_goods = data.hotelGoods;
  }

  // 电子卡券价格日历
  if (data.appointmentTime) {
    commonData.card_goods = {
      appointment_time: data.appointmentTime
    };
  }

  // 送礼
  if (data.orderType) {
    commonData.order_type = data.orderType;
  }

  // 积分商品
  if (data.isPoints) {
    commonData.is_points = data.isPoints;
  }

  // 来源参数
  if (data.from) {
    commonData.from = data.from;
  }
  // 登录凭证
  if (data.loginTicket) {
    commonData.loginTicket = data.loginTicket;
  }

  // 下单成功后的跳转页面
  if (data.paymentSuccessRedirect) {
    commonData.payment_success_redirect = data.paymentSuccessRedirect;
  }

  // 分期信息
  if (data.selectedInstallmentPeriod) {
    commonData.installmentRate = data.installmentRate;
    commonData.selectedInstallmentPeriod = data.selectedInstallmentPeriod;
  }

  return {
    goodsData,
    commonData
  };
}

export const ORDER_SCENE = {
  BUY_NOW: 'buyNow',
  ADD_CART: 'addCart',
};
// 格式化组合套餐商品数据
export function getSubCombList({ comboDetailModel, skuId, flag }) {
  const { comboGroupModels = [] } = comboDetailModel;
  let findItem = {};
  // 说明是无规格的组合商品 直接购买
  if (comboGroupModels.length === 1 && !comboGroupModels[0].skuId) {
    findItem = comboGroupModels[0];
  } else {
    findItem = comboGroupModels.find((item) => item.skuId === skuId) || {};
  }
  const { comboSubItemModels = [], goodsComboGroupId = '' } = findItem;
  const subComboList = comboSubItemModels.reduce(
    (subComboList, curSubCombo) => {
      if (curSubCombo.isDisplay === 0) {
        return subComboList;
      }
      const { skuRelatedModels = [], propModels = [] } = curSubCombo;
      const processedCurItem = [];
      const propertyIds = propModels.reduce((propertyIds, propModel) => {
        const { textModels } = propModel;
        textModels.forEach((item) => {
          propertyIds.push(item.id);
        });
        return propertyIds;
      }, []);
      skuRelatedModels.forEach(({ combineNum, itemId, price, skuId }) => {
        const subComboItem = {
          goodsId: itemId,
          num: combineNum,
          price,
          skuId,
        };
        if (flag === ORDER_SCENE.BUY_NOW) {
          subComboItem.groupId = goodsComboGroupId;
        }
        if (propertyIds.length) {
          subComboItem.propertyIds = propertyIds;
        }
        processedCurItem.push(subComboItem);
      });
      subComboList.push(...processedCurItem);
      return subComboList;
    },
    []
  );
  return {
    subComboList,
    goodsComboGroupId,
  };
}

// 格式化购物车请求数据
export function formatAddCartData(data) {
  const { goodsData, commonData } = formatBaseData(data);

  const result = {
    ...goodsData,
    ...commonData,
    messages: JSON.stringify(data.cartMessages || []),
    biz_trace_point_ext: logv3.getTradeLog(),
    extensions: JSON.stringify(goodsData.extensions),
    propertyIds: JSON.stringify(goodsData.propertyIds)
  };
  // 支持组合商品 
  const { comboDetailModel, comboMark } = data.itemDataModel || {};
  if (comboMark && comboMark.isCombo) {
    const { isCombo = false, comboType = 0 } = comboMark;
    if (isCombo) {
      const { subComboList, goodsComboGroupId } = getSubCombList({
        comboDetailModel,
        skuId: goodsData.sku_id,
        flag: ORDER_SCENE.ADD_CART,
      });
      const groupList = [
        {
          id: goodsComboGroupId,
          subComboList,
        },
      ];
      result.comboType = comboType;
      result.isCombo = isCombo;
      result.groupList = JSON.stringify(groupList);
    }
  }
  if (data.cartBizMarkDTO) {
    result.cartBizMarkDTO = data.cartBizMarkDTO || {};
  }

  return result;
  // return objectToQuery(result);
}

// 格式化下单请求数据
export function formatBuyData(data) {
  const { goodsData, commonData } = formatBaseData(data);
  // 支持组合商品
  const { itemDataModel } = data;
  const { comboMark = {}, comboDetailModel = {} } = itemDataModel || {};
  const { isCombo = false, comboType = 0 } = comboMark;
  if (isCombo) {
    const subCombo = getSubCombList({
      comboDetailModel,
      skuId: goodsData.sku_id,
      flag: ORDER_SCENE.BUY_NOW,
    });
    const { subComboList = [] } = subCombo;
    goodsData.extra = {
      COMBO_INFO: {
        comboType,
        subComboList,
      },
    };
  }

  const { birthdayInfo = {} } = data;
  if (!goodsData.extra) {
    goodsData.extra = {};
  }
  goodsData.extra = Object.assign({}, goodsData.extra, birthdayInfo);

  const result = {
    goodsList: [
      {
        ...goodsData,
      },
    ],
    extraData: commonData,
  };
  return result;
}
