import { formatSkuTree } from './sku-format-tree';
import { getCouponId } from './sku-get-coupon-id';

export { getCouponId, formatSkuTree };
/*
  normalize sku tree

  [
    {
      count: 2,
      k: "品种", // 规格名称 skuKeyName
      kId: "1200", // skuKeyId
      kS: "s1" // skuKeyStr
      v: [ // skuValues
        { // skuValue
          id: "1201", // skuValueId
          name: "萌" // 具体的规格值 skuValueName
        }, {
          id: "973",
          name: "帅"
        }
      ]
    },
    ...
  ]
                |
                v
  {
    s1: [{
      id: "1201",
      name: "萌"
    }, {
      id: "973",
      name: "帅"
    }],
    ...
  }
 */

export const normalizeSkuTree = (skuTree = []) => {
  const normalizedTree = {};
  skuTree.forEach((treeItem) => {
    normalizedTree[treeItem.kS] = treeItem.v;
  });
  return normalizedTree;
};

// 判断是否所有的sku都已经选中
export const isAllSelected = (skuTree = [], selectedSku) => {
  // 筛选selectedSku对象中key值不为空的值
  const selected = Object.keys(selectedSku).filter(
    (skuKeyStr) => selectedSku[skuKeyStr] !== ''
  );
  return skuTree.length === selected.length;
};

// 根据已选择的sku获取skuComb
export const getSkuComb = (skuList = [], selectedSku) => {
  const skuComb = skuList.filter((skuComb) => {
    return Object.keys(selectedSku).every((skuKeyStr) => {
      return String(skuComb[skuKeyStr]) === String(selectedSku[skuKeyStr]); // eslint-disable-line
    });
  });
  const result = skuComb[0];
  // 组合商品 需要展示sku下的name数据，先注释掉
  // result && delete result.sku;
  return result;
};

// 获取已选择的sku名称
export const getSelectedSkuValues = (skuTree = [], selectedSku) => {
  if (skuTree.length === 0) {
    return [];
  }
  const normalizedTree = normalizeSkuTree(skuTree);
  return Object.keys(selectedSku).reduce((selectedValues, skuKeyStr) => {
    const skuValues = normalizedTree[skuKeyStr];
    const skuValueId = selectedSku[skuKeyStr];

    if (skuValueId !== '') {
      const skuValue = skuValues.filter(
        (skuValue) => skuValue.id === skuValueId
      )[0];
      skuValue && selectedValues.push(skuValue);
    }
    return selectedValues;
  }, []);
};

// 判断sku是否可选
export const isSkuChoosable = (skuList = [], selectedSku, skuToChoose) => {
  const { key, valueId } = skuToChoose;
  // 先假设sku已选中，拼入已选中sku对象中
  const matchedSku = {
    ...(selectedSku || {}),
    [key]: valueId,
  };
  // 再判断剩余sku是否全部不可选，若不可选则当前sku不可选中
  const skusToCheck = Object.keys(matchedSku).filter(
    (skuKey) => matchedSku[skuKey] !== ''
  );
  const filteredSku = skuList.filter((sku) => {
    return skusToCheck.every((skuKey) => {
      return String(matchedSku[skuKey]) === String(sku[skuKey]);
    });
  });

  const stock = filteredSku.reduce((total, sku) => (total += sku.stockNum), 0);
  return stock > 0;
};

export const getSelectedProperties = (propList, selectedProp) => {
  const list = [];
  (propList || []).forEach((prop) => {
    if (selectedProp[prop.kId] && selectedProp[prop.kId].length > 0) {
      const v = [];
      prop.v.forEach((it) => {
        if (selectedProp[prop.kId].indexOf(it.id) > -1) {
          v.push({ ...it });
        }
      });
      list.push({
        ...prop,
        v,
      });
    }
  });
  return list;
};

/**
 * 判断是否所有的必选属性都已经选中
 */
export const isAllRequiredPropertiesSelected = (properties, selectedProp) => {
  if (properties.length === 0) {
    return true;
  }
  const isHasUnSelected = properties
    .filter(({ isNecessary }) => isNecessary !== false)
    .some(({ kId }) => (selectedProp[kId] || []).length === 0);

  return !isHasUnSelected;
};

/**
 * 规格、属性 未完整选择的提示
 */
export const getUnSelectedErrorMsg = ({
  skuTree = [],
  selectedSku,
  properties,
  selectedProp,
}) => {
  const skus = skuTree
    .filter((item) => !selectedSku[item.kS])
    .map(({ k }) => k);

  const props = properties
    .filter(({ isNecessary }) => isNecessary !== false)
    .filter(({ kId }) => (selectedProp[kId] || []).length === 0)
    .map(({ k }) => k);

  return [...skus, ...props].length > 0
    ? `请选择${[...skus, ...props].join('、')}`
    : '';
};

export default {
  normalizeSkuTree,
  getSkuComb,
  getSelectedSkuValues,
  isAllSelected,
  isSkuChoosable,
  getSelectedProperties,
  isAllRequiredPropertiesSelected,
  getUnSelectedErrorMsg,
};
