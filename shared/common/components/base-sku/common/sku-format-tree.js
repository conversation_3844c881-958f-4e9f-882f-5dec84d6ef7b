import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

export const formatSkuTree = (sku = {}) => {
  sku = mapKeysCase.toCamelCase(sku);

  const tree =
    sku.tree &&
    sku.tree.map((item) => {
      const v = item.v.map((t) => {
        t.imgUrl = cdnImage(t.imgUrl);
        return t;
      });
      return {
        ...item,
        v,
      };
    });
  return {
    ...sku,
    tree,
  };
};
