## 基本用法
```js
<base-sku
  theme-class="{{ themeClass }}"
  goods="{{ featureSkuData.skuGoodsDetail }}"
  sku="{{ featureSkuData.sku }}"
  show="{{ featureSkuData.showGoodsSku }}"
  buy-btn-text="{{ featureSkuData.buyBtnText }}"
  hide-stock="{{ featureSkuData.hideStock }}"
  extra-data="{{ featureSkuData.extraData }}"
  bind:buy="handlePresaleGoodsBuy"
  show-add-cart-btn="{{ false }}"
  reset-stepper-on-hide
/>
```
## sku数据格式说明

### themeClass<String>
全店风格类名

### sku<Object>
商品规格数据
```javascript
sku: {
  // 所有sku规格类目与其值的从属关系，比如商品有颜色和尺码两大类规格，颜色下面又有红色和蓝色两个规格值。
  // 可以理解为一个商品可以有多个规格类目，一个规格类目下可以有多个规格值。
  tree: [
    {
      k: '颜色', // skuKeyName：规格类目名称
      v: [
        {
          id: '30349', // skuValueId：规格值 id
          name: '红色', // skuValueName：规格值名称
          imgUrl: 'https://img.yzcdn.cn/1.jpg' // 规格类目图片，只有第一个规格类目可以定义图片
        },
        {
          id: '1215',
          name: '蓝色',
          imgUrl: 'https://img.yzcdn.cn/2.jpg'
        }
      ],
      kS: 's1' // skuKeyStr：sku 组合列表（下方 list）中当前类目对应的 key 值，value 值会是从属于当前类目的一个规格值 id
    }
  ],
  // 所有 sku 的组合列表，比如红色、M 码为一个 sku 组合，红色、S 码为另一个组合
  list: [
    {
      id: 2259, // skuId，下单时后端需要
      price: 100, // 价格（单位分）
      s1: '1215', // 规格类目 kS 为 s1 的对应规格值 id
      s2: '1193', // 规格类目 kS 为 s2 的对应规格值 id
      s3: '0', // 最多包含3个规格值，为0表示不存在该规格
      stockNum: 110 // 当前 sku 组合对应的库存
    }
  ],
  price: '1.00', // 默认价格（单位元）
  stockNum: 227, // 商品总库存
  collectionId: 2261, // 无规格商品 skuId 取 collectionId，否则取所选 sku 组合对应的 id
  noneSku: false, // 是否无规格商品
  messages: [
    {
      // 商品留言
      datetime: '0', // 留言类型为 time 时，是否含日期。'1' 表示包含
      multiple: '0', // 留言类型为 text 时，是否多行文本。'1' 表示多行
      name: '留言', // 留言名称
      type: 'text', // 留言类型，可选: id_no（身份证）, text, tel, date, time, email
      required: '1' // 是否必填 '1' 表示必填
    }
  ],
  hideStock: false // 是否隐藏剩余库存
}
```

### goods<Object>
商品数据
```javascript
goods: {
  id, // 商品id
  alias, // 商品alias
  title, // 商品标题
  picture // 商品图片
}
```

### extraData<Object>
额外参数，不需要自定义组件的话一般用不到

需要自定义价格展示/规格展示的话需要传:
```js
extraData: {
  useCustomHeaderPrice: true,
  useCustomSkuGroup: true
}
```

然后base-sku组件参数上需要设置为自己重新写的抽象组件：
```
generic:sku-header-price="presale-header-price" // 自定义价格展示时需要
generic:sku-actions="presale-actions" // 自定义规格展示时需要
```

### stepperTitle<String>
商品数量选择左侧标题

### quota<Number>
限购数量

### quotaUsed<Number>
限购商品已购买数量

### hideStock<Boolean>
是否隐藏库存

### buyBtnText<String>
立即购买按钮文案

### resetStepperOnHide<Boolean>
关闭sku时是否重置商品数量选择

### showAddCartBtn<Boolean>
是否显示加入购物车按钮

### bodyOffsetTop<Number>
弹层到顶部距离

### show<Boolean>
是否显示sku弹层

## 组件触发的自定义事件
### sku-close
关闭sku时触发，参数对象：
```js
{
  selectedSkuValues,
  selectedNum,
  selectedSkuComb
}
```

### sku-selected
选择sku时触发，参数对象：
```js
{
  skuValue,
  selectedSku,
  selectedSkuComb
}
```

### buy/add-cart
立即购买/加入购物车时触发，参数对象
```js
{
  selectedNum,
  selectedSkuComb,
  selectedSkuValues,
  goodsId,
  messages
}
```

