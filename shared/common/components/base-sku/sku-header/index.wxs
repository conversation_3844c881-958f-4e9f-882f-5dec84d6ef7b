function cent2yuan(value) {
  return Number(value / 100).toFixed(2);
}

function getGoodsPrice(selectedSkuComb, sku) {
  if (!sku) return;

  // sku.price是一个格式化好的价格区间
  var result = sku.price;

  if (selectedSkuComb) {
    // 如果是分做一次转换
    const price = selectedSkuComb.price;
    const propertyPrice = selectedSkuComb.propertyPrice || 0;
    result = typeof price === 'number' ? cent2yuan(price + propertyPrice) : price;
  }

  return result;
}

module.exports = {
  getGoodsPrice
};
