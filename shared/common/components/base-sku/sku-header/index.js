import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import skuBehavior from '../common/sku-behavior';
import { getSelectedSkuValues } from '../common/sku-helper';
import { imageTransform } from '../common/image';

function getSkuImg(skuTree = [], id) {
  if (!id) {
    return;
  }

  let treeItem = {};

  // eslint-disable-next-line no-unused-vars
  for (const item of skuTree) {
    if (item.kS === 's1') {
      treeItem = item;
      break;
    }
  }

  if (!treeItem.v) {
    return;
  }

  const matchedSku = treeItem.v.filter(({ id: _id }) => _id === id)[0];

  if (matchedSku && matchedSku.imgUrl) {
    return matchedSku.imgUrl;
  }
}

VanxComponent({
  options: {
    multipleSlots: true,
  },

  behaviors: [skuBehavior],

  properties: {
    goods: {
      type: Object,
      observer: 'setImg',
    },
    selectedSku: {
      type: Object,
      observer: 'selectedChange',
    },
    selectedSkuComb: {
      type: Object,
      observer: 'selectedSkuChange',
    },
    sku: {
      type: Object,
      observer: 'skuChange',
    },
    properties: {
      type: Array,
      value: [],
      observer: 'skuChange',
    },
    selectedProp: {
      type: Object,
      value: {},
      observer: 'selectedChange',
    },
    hideStock: Boolean,
    quota: Number,
    quotaUsed: Number,
    quotaCycle: Number,
  },

  data: {
    stockNum: Number.MAX_VALUE,
  },
  methods: {
    skuChange() {
      this.updateStockNum();

      this.setYZData({
        hasSku:
          this.data.sku &&
          (!this.data.sku.noneSku || this.data.properties.length),
      });
    },
    selectedChange() {
      this.setImg();
      this.updateSelectText();
    },
    selectedSkuChange() {
      this.updateStockNum();
      this.updateSelectText();
    },

    updateSelectText() {
      const {
        sku,
        selectedSku,
        selectedSkuComb,
        selectedProp,
        properties,
      } = this.data;
      const selectedSkuValues = getSelectedSkuValues(sku.tree, selectedSku);

      const selected = [];
      const unselected = [];

      properties.forEach((prop) => {
        if (selectedProp[prop.kId] && selectedProp[prop.kId].length > 0) {
          prop.v.forEach((v) => {
            if (selectedProp[prop.kId].indexOf(v.id) !== -1) {
              selected.push(v.name);
            }
          });
        } else {
          unselected.push(prop.k);
        }
      });

      const isAllSelected = unselected.length === 0;

      if (selectedSkuComb && isAllSelected) {
        return this.setYZData({
          selectText: `已选择 ${selectedSkuValues
            .map((item) => item.name)
            .concat(selected)
            .join('；')}`,
        });
      }
      const unselectedStr = (sku.tree || [])
        .filter((item) => selectedSku[item.kS] === '')
        .map((item) => item.k)
        .concat(unselected)
        .join('；');

      return this.setYZData({
        selectText: `请选择 ${unselectedStr}`,
      });
    },

    updateStockNum() {
      const { selectedSkuComb, sku } = this.data;
      const selectedSkuNum = (selectedSkuComb && selectedSkuComb.stockNum);
      const stockNum = selectedSkuNum || (sku && sku.stockNum);
      if (typeof stockNum === 'number') {
        selectedSkuNum === 0 ? this.setYZData({ stockNum: selectedSkuNum }) :  this.setYZData({ stockNum });
      }
    },
    setImg() {
      const { selectedSku, sku, goods } = this.properties;

      if (!selectedSku || !sku || !goods) {
        return;
      }

      const { picture, originPicture } = goods;
      const skuImg = getSkuImg(sku.tree, selectedSku.s1);

      this.originImgUrl = skuImg || originPicture || picture;

      this.setYZData({
        imgUrl: imageTransform(skuImg || picture),
      });
    },

    onSkuCloseClicked() {
      this.triggerEvent('sku:close');
    },

    onImgClick() {
      if (this.originImgUrl) {
        wx.previewImage({ urls: [cdnImage(this.originImgUrl, '!730x0.jpg')] });
      }
    },
  },
});
