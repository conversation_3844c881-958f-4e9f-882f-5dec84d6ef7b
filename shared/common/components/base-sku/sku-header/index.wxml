<view class="van-sku-header van-hairline--bottom">
  <view class="van-sku-header__img-wrap">
    <image mode="aspectFit" src="{{ imgUrl }}" bind:tap="onImgClick" />
  </view>
  <view class="van-sku-header__goods-info">
    <!-- 价格区域可以用自定义组件替换 -->
    <slot wx:if="{{ extraData.useCustomHeaderPrice }}"></slot>
    <theme-view
      wx:else
      color="general"
      class="van-sku__goods-price">
      <text class="van-sku__price-symbol">￥</text><text class="van-sku__price-num">{{ utils.getGoodsPrice(selectedSkuComb, sku) }}</text>
    </theme-view>

    <view wx:if="{{ !hideStock }}" class="van-sku-header__item">
      剩余
      <theme-view
        color="{{ stockNum < 50 ? 'general' : '' }}"
        class="van-sku-header__item-num">
          {{ stockNum }}
      </theme-view>
      件
    </view>

    <view wx:if="{{ hasSku && selectText }}" class="van-sku-header__item">{{ selectText }}</view>
    <van-icon
      name="cross"
      class="van-sku__close-icon"
      bind:tap="onSkuCloseClicked"
    />
  </view>
</view>

<wxs src="./index.wxs" module="utils" />
