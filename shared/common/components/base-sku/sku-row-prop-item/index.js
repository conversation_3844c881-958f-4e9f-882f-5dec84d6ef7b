import WscComponent from 'shared/common/base/wsc-component';
import skuBehavior from '../common/sku-behavior';

WscComponent({
  behaviors: [skuBehavior],

  properties: {
    skuValue: Object,
    skuKeyStr: String,
    selectedProp: {
      type: Object,
      observer: 'updateRowItemClass',
    },
    multiple: <PERSON><PERSON><PERSON>,
    disabled: Boolean,
  },

  data: {
    rowItemClass: 'van-sku-row__item',
    isActive: false,
  },

  relations: {
    '../sku-row/index': {
      type: 'parent',
    },
  },

  methods: {
    updateRowItemClass() {
      let className = 'van-sku-row__item';
      let isActive = false;

      if (this.isChoosed()) {
        className = 'van-sku-row__item--active';
        isActive = true;
      }

      if (this.data.disabled) {
        className = 'van-sku-row__item--disabled';
      }

      this.setYZData({
        rowItemClass: className,
        isActive,
      });
    },

    isChoosed() {
      const { selectedProp, skuKeyStr, skuValue = {} } = this.data;
      return (
        selectedProp &&
        (selectedProp[skuKeyStr] || []).indexOf(skuValue.id) > -1
      );
    },

    onPropSelect() {
      if (this.data.disabled) return;

      this.triggerEvent('sku:propSelect', {
        ...this.data.skuValue,
        skuKeyStr: this.data.skuKeyStr,
        multiple: this.data.multiple,
      });
    },
  },
});
