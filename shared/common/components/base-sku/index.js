import { VanxComponent } from 'shared/common/base/wsc-component';
import money from '@youzan/weapp-utils/lib/money';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import Toast from '@vant/weapp/dist/toast/toast';
import asyncEvent from 'shared/utils/async-event';
import {
  isSkuChoosable,
  isAllSelected,
  getSkuComb,
  getSelectedSkuValues,
  getSelectedProperties,
  isAllRequiredPropertiesSelected,
  getUnSelectedErrorMsg,
  getCouponId,
} from './common/sku-helper';
import { getOverLimitText } from './common/quota-utils';

import tabBehavior from '@/custom-tab-bar-v2/tab-behavior';

VanxComponent({
  options: {
    multipleSlots: true,
  },

  behaviors: [tabBehavior],

  properties: {
    themeClass: String,
    sku: {
      type: Object,
      value: {},
      observer: 'resetSelectedSku',
    },
    goods: Object,
    extraData: {
      type: Object,
      value: {},
    },
    stepperTitle: {
      type: String,
      value: '购买数量',
    },
    cartCount: Number, // guang 小程序依赖该参数
    quota: {
      type: Number,
      value: 0,
    },
    quotaText: {
      type: String,
      value: '',
    },
    quotaUsed: {
      type: Number,
      value: 0,
    },
    quotaCycle: {
      type: Number,
      value: 0,
    },
    startSaleNum: {
      type: Number,
      value: 1,
    },
    hideStock: {
      type: Boolean,
      value: false,
    },
    buyText: {
      type: String,
      value: '立即购买',
    },
    cartText: {
      type: String,
      value: '加入购物车',
    },
    resetStepperOnHide: {
      type: Boolean,
      value: false,
    },
    // 一般为主按钮，可使用buyText修改
    showBuyBtn: {
      type: Boolean,
      value: true,
    },
    // 专指购物车按钮
    showAddCartBtn: {
      type: Boolean,
      value: true,
    },
    bodyOffsetTop: {
      type: Number,
      value: 200,
    },
    show: {
      type: Boolean,
      value: false,
      observer: 'onShowChange',
    },
    usePopup: {
      type: Boolean,
      value: true,
    },
    // 价格日历只有电子卡券支持
    isVirtualTicket: {
      type: Boolean,
      value: false,
    },
    // 原来的virtualForPriceCalendar
    virtualTicket: {
      type: Object,
      value: {},
    },
    // 是否为价格日历商品
    isPriceCalendar: {
      type: Boolean,
      value: false,
    },
    // 价格日历数据，原来的calendarInfoData
    priceCalendarData: {
      type: Object,
      value: {},
    },
    // 初始化选中的sku
    initialSku: {
      type: Object,
      value: {},
    },
    // 留言配置
    /*
    * {
      initialMessages: {
        messageName: messageValue
      }
    }
    */
    messageConfig: {
      type: Object,
      value: {},
    },
    // 商品属性
    properties: {
      type: Array,
      value: [],
      observer: 'resetSelectedSku',
    },
    skuProperties: {
      type: Array,
      value: [],
    },
    // 自定义步进器配置
    customStepperConfig: {
      type: Object,
      value: {},
    },
    // 价格日历库存
    calendarStock: {
      type: null,
    },
    disableSoldoutSku: {
      type: Boolean,
      value: true,
    },
  },

  data: {
    selectedSku: {},
    selectedProp: {},
    selectedNum: 1,
    selectedSkuComb: null,
    // 日历价格选择器
    selectedSkuDate: '',
    // 日期时间选择
    showDatePicker: false,
    datePickerData: {},
    zIndex: 300,
    // 最简单的skuList，只有库存和规格值ids
    simpleSkuList: [],
    birthdayInfo: {},
    currentSkuProperties: [],
  },

  ready() {
    const { properties } = this.data;
    this.setYZData({
      currentSkuProperties: properties,
    });
  },

  methods: {
    /* ============== sku-组件初始化 ============ */

    getSimpleSkuList(skuList) {
      if (!skuList) return [];
      // 后端返回最多五个规格项，先写死，可能有坑，但是为了速度快也不建议写多。
      const sList = [1, 2, 3, 4, 5];
      return skuList.map((sku) => {
        const model = {
          stockNum: sku.stockNum,
        };
        sList.forEach((sNumber) => {
          const sItem = `s${sNumber}`;
          if (sku[sItem] !== undefined) {
            // 构建最简单的skulist，只有库存和规格值ids
            model[sItem] = sku[sItem];
          }
        });
        return model;
      });
    },
    resetSelectedSku(sku = {}, oldSku = {}) {
      // console.log('[base-sku]', JSON.stringify(sku));
      const selectedSku = {};
      let selectedProp = {};
      if (!sku) {
        return;
      }
      if (oldSku && JSON.stringify(sku) === JSON.stringify(oldSku)) {
        return;
      }
      const skuTree = sku.tree || [];
      // 重置selectedSku
      skuTree.forEach((item) => {
        selectedSku[item.kS] = '';
      });
      const simpleSkuList = this.getSimpleSkuList(sku.list);
      skuTree.forEach((item) => {
        // 只有一个sku规格值时默认选中
        // 只有一个sku可选，也默认选中
        let canChooseCount = 0;
        for (let i = 0; i < item.v.length; i++) {
          const key = item.kS;
          const valueId = item.v[i].id;
          if (isSkuChoosable(sku.list, selectedSku, { key, valueId })) {
            selectedSku[key] = valueId;
            canChooseCount += 1;
          }
          if (canChooseCount > 1) {
            // 支持 initialSku 初始化sku 转下number类型，初始有些传过来value是string
            selectedSku[key] = +this.data.initialSku[item.kS] || '';
            break;
          }
        }
      });

      // 如果传了初始化 属性，加入默认选中
      if (this.data.initialSku.selectedProp) {
        selectedProp = {
          ...selectedProp,
          ...this.data.initialSku.selectedProp,
        };
      }

      if (isEmpty(selectedProp)) {
        // 未配置价格的属性项，默认选中第一个未被禁用的
        this.data.currentSkuProperties.forEach((item) => {
          if (item?.v?.length > 0) {
            const { v, kId } = item;
            const allPropFree = !v.some((i) => +i.price !== 0);
            const firstNotFreeEnabledPropIndex = allPropFree
              ? v.findIndex((i) => i.textStatus !== 0)
              : -1;
            if (firstNotFreeEnabledPropIndex > -1) {
              selectedProp[kId] = [v[firstNotFreeEnabledPropIndex].id];
            }
          }
        });
      }

      const selectedSkuComb = this.getSelectedSkuComb(
        selectedSku,
        selectedProp
      );

      // 新的 setYZData 会把 object 类型的数据中不需要的字段设为 `null`
      // 在某些场景下会导致非预期的表现，如：`Object.keys()`。所以要先置空(FIXME？)
      const mark = !!sku.birthdayInfo;
      this.setYZData(
        {
          simpleSkuList,
          selectedSku,
          selectedProp,
          selectedSkuComb,
          birthdayInfo: { BIRTHDAY_RELATION_NET_ORDER_MARK: mark },
        },
        {
          immediate: true,
        }
      );

      wx.nextTick(() => {
        // 重新初始化需要更新外部状态
        this.triggerEvent('sku-selected', {
          type: 'item-select',
          selectedSku,
          selectedSkuComb,
        });
        this.triggerEvent('sku-prop-selected', {
          type: 'item-prop-select',
          selectedProp,
          selectedSkuComb,
        });
      });
    },

    /* ============== sku-message 自定义留言 =========== */
    // 给sku-message追加引用，获取内部信息
    addSkuMsgRef({ detail: data }) {
      const { refKey, ref } = data;
      this.skuMsgRef = this.skuMsgRef || {};
      if (refKey === 'skuMessages') {
        this.skuMsgRef = ref;
      }
    },

    getSkuMessages() {
      return (this.skuMsgRef.getMessages && this.skuMsgRef.getMessages()) || {};
    },

    getCartSkuMessages() {
      return (
        (this.skuMsgRef.getCartMessages && this.skuMsgRef.getCartMessages()) ||
        []
      );
    },

    validateSkuMessages() {
      return (
        (this.skuMsgRef.validateMessages &&
          this.skuMsgRef.validateMessages()) ||
        ''
      );
    },

    /* =========== price-calendar 价格日历 ========= */

    // 刷新价格日历，重置日历选择
    onDateItemReset() {
      this.setYZData({
        selectedSkuDate: '',
      });
    },

    // 价格日历选择日期后触发的内容
    onDateItemClick(e) {
      const { dataset = {} } = e.detail;
      // 获取选择日期
      const selectedSkuDate = dataset.infodate;
      const { selectedSku } = this.data;
      // 修改skuid
      let selectedSkuComb = this.getSelectedSkuComb(this.data.selectedSku);

      selectedSkuComb = {
        ...selectedSkuComb,
        id: dataset.dateskuid,
        price: dataset.activityprice,
        oldPrice:
          dataset.originprice === dataset.activityprice
            ? ''
            : dataset.originprice,
        stockNum: dataset.stocknum,
      };

      this.setYZData({
        selectedSkuComb,
        selectedSkuDate,
      });
      this.triggerEvent('sku-selected', {
        type: 'date-select',
        selectedSku,
        selectedSkuComb,
        selectedSkuDate,
      });
    },

    /* ================== sku-stepper 数量选择 =========== */

    onNumChange({ detail: num }) {
      // 蜜汁 bug。相关 jira: https://jira.qima-inc.com/browse/ONLINE-117428
      this.setYZData(
        {
          selectedNum: num,
        },
        { immediate: true }
      );
      this.triggerEvent('stepper-change', num);
    },

    // 数量限制文案
    stepperOverWord(data) {
      const { action, limitType, quota, quotaUsed, quotaCycle, startSaleNum } =
        data;
      const { isVirtualTicket } = this.data;
      const msg = getOverLimitText({
        action,
        limitType,
        quota,
        quotaUsed,
        quotaPeriod: quotaCycle,
        // isHotel,
        // isPoints,
        isVirtualTicket,
        // isHaitao,
        startSaleNum,
      });
      return msg;
    },

    // 数量校验
    onOverLimit({ detail: data }) {
      // 如果自定义步进器配置，则用配置的校验
      if (this.data.customStepperConfig) {
        const {
          customStepperConfig: { handleOverLimit },
        } = this.data;
        if (typeof handleOverLimit === 'function') {
          handleOverLimit(data);
        }
      } else {
        wx.showToast({
          title: this.stepperOverWord(data),
          icon: 'none',
        });
      }
    },

    onInitState({ detail: data }) {
      if (data.valid) {
        this.errorMsg = '';
      } else {
        this.errorMsg = this.stepperOverWord({
          ...data,
          action: 'plus',
        });
      }
    },

    /* ====================== 获取sku组合 ============== */

    getSelectedSkuComb(selectedSku, selectedProp = {}) {
      let skuComb = null;
      if (!this.data.sku) {
        return skuComb;
      }
      const { sku, properties, skuProperties } = this.data;

      if (this.isSkuCombSelected(selectedSku, selectedProp)) {
        if (sku.noneSku) {
          skuComb = {
            id: sku.collectionId,
            price: money(+sku.price).toCent(),
            oldPrice: sku.oldPrice,
            remainPrice: sku.remainPrice || 0,
            pointsPrice: sku.collectionPointsPrice || sku.pointsPrice || 0,
            pointsPriceStr: sku.pointsPrice,
            stockNum: sku.stockNum,
          };
        } else {
          // 修复多规格加购价格变大的问题
          // getSkuData price（"6"） + propertyPrice（0）
          skuComb = getSkuComb(sku.list, selectedSku);
        }
        if (skuComb) {
          let currentSkuProperties;
          if (skuProperties.length) {
            const currentSku = skuProperties.find(
              (i) => i.skuId === skuComb.id
            );
            currentSkuProperties = currentSku?.itemSalePropModels || [];
            this.setYZData({
              currentSkuProperties,
            });
          }
          skuComb.properties = getSelectedProperties(
            currentSkuProperties || properties,
            selectedProp
          );
          skuComb.propertyPrice = skuComb.properties.reduce((acc, cur) => {
            (cur.v || []).forEach((it) => {
              if (it.price) {
                acc += it.price;
              }
            });
            return acc;
          }, 0);
        }
      }
      return skuComb;
    },

    isSkuCombSelected(selectedSku, selectedProp = {}) {
      const { sku, currentSkuProperties } = this.data;
      if (!sku) {
        return false;
      }
      // SKU 未选完
      if (!sku.noneSku && !isAllSelected(sku.tree, selectedSku)) {
        return false;
      }

      return isAllRequiredPropertiesSelected(
        currentSkuProperties,
        selectedProp
      );
    },

    // 选中sku组合
    onSelect({ detail: skuValue }) {
      // 点击已选中的sku时则取消选中
      const isSelected =
        this.data.selectedSku[skuValue.skuKeyStr] === skuValue.id;
      const selectedSku = isSelected
        ? { ...this.data.selectedSku, [skuValue.skuKeyStr]: '' }
        : { ...this.data.selectedSku, [skuValue.skuKeyStr]: skuValue.id };

      const selectedSkuComb = this.getSelectedSkuComb(
        selectedSku,
        this.data.selectedProp
      );

      this.setYZData({
        selectedSku,
        selectedSkuComb,
      });

      this.triggerEvent('sku-selected', {
        type: 'item-select',
        selectedSku,
        selectedSkuComb,
      });
    },

    // 选中的商品属性
    onPropSelect({ detail: propValue }) {
      const arr = this.data.selectedProp[propValue.skuKeyStr] || [];
      const pos = arr.indexOf(propValue.id);
      if (pos > -1) {
        arr.splice(pos, 1);
      } else if (propValue.multiple) {
        arr.push(propValue.id);
      } else {
        arr.splice(0, 1, propValue.id);
      }
      const selectedProp = {
        ...this.data.selectedProp,
        [propValue.skuKeyStr]: arr,
      };

      const selectedSkuComb = this.getSelectedSkuComb(
        this.data.selectedSku,
        selectedProp
      );

      this.setYZData({
        selectedProp,
        selectedSkuComb,
      });

      this.triggerEvent('sku-prop-selected', {
        type: 'item-prop-select',
        selectedProp,
        selectedSkuComb,
      });
    },

    /* =================== 下单相关 ================ */
    triggerAsync(...args) {
      return asyncEvent.triggerAsync.apply(getApp(), args);
    },
    triggerCustomeValidate() {
      return this.triggerAsync('app:sku:pay:checked:goods', {
        data: this.data, // 把整个data透传出去
      });
    },
    validateNormalMes() {
      const {
        selectedNum,
        isVirtualTicket,
        virtualTicket,
        selectedSkuDate,
        sku,
        selectedSku,
        currentSkuProperties,
        selectedProp,
      } = this.data;

      if (selectedNum === 0) {
        return '商品已经无法购买啦';
      }

      // 如果有前置错误，直接报错
      if (this.errorMsg) {
        return this.errorMsg;
      }
      if (isVirtualTicket && virtualTicket.validityType === 3) {
        // 价格日历情况下
        if (!selectedSkuDate) {
          return '请选择日期';
        }
      }

      // sku-message 留言
      if (this.isSkuCombSelected(selectedSku, selectedProp)) {
        return this.validateSkuMessages();
      }

      const unSelectedErrorMsg = getUnSelectedErrorMsg({
        skuTree: sku?.tree,
        selectedSku,
        currentSkuProperties,
        selectedProp,
      });
      return unSelectedErrorMsg;
    },
    validateSku() {
      let resolve = null;
      const promise = new Promise((r) => {
        resolve = r;
      });

      const mes = this.validateNormalMes();
      if (mes) {
        resolve(mes);
      }
      // 云扩展事件
      this.triggerCustomeValidate().then((res) => {
        const cloudMes = res[0];
        if (cloudMes) {
          resolve(cloudMes);
        } else {
          resolve();
        }
      });
      return promise;
    },

    getSkuData() {
      const {
        goods: { id },
        sku,
        selectedSku,
        selectedNum,
        selectedSkuComb,
        selectedSkuDate,
        birthdayInfo,
      } = this.data;
      const selectedSkuValues = getSelectedSkuValues(sku.tree, selectedSku).map(
        (value) => value.name
      );

      return {
        couponId: getCouponId(sku),
        goodsId: id,
        skuId: selectedSkuComb.id,
        num: selectedNum,
        price: selectedSkuComb.price + selectedSkuComb.propertyPrice,
        pointsPrice: selectedSkuComb.pointsPrice,
        remainPrice: selectedSkuComb.remainPrice,
        selectedNum,
        selectedSkuComb,
        selectedSkuValues,
        selectedSkuDate,
        messages: this.getSkuMessages(),
        cartMessages: this.getCartSkuMessages(),
        birthdayInfo,
      };
    },

    // 触发购买或者加入购物车
    onAddCart() {
      this.onBuyOrAddCart('add-cart');
    },

    onBuy() {
      this.onBuyOrAddCart('buy');
    },

    onChooseRelation(payload) {
      this.data.birthdayInfo = payload.detail;
    },

    onBuyOrAddCart(type) {
      const errorPromise = this.validateSku();
      errorPromise.then((error) => {
        if (error) {
          Toast({
            message: error,
            position: 'bottom',
          });
        } else {
          return this.triggerEvent(type, this.getSkuData());
        }
      });
    },

    showDateTimePicker(e) {
      this.setYZData({ showDatePicker: true, datePickerData: e.detail });
    },

    closeDateTimePicker() {
      this.setYZData({ showDatePicker: false });
    },

    confirmDateTimePicker(ev) {
      if (this.skuMsgRef.confirmDate) {
        this.skuMsgRef.confirmDate(ev);
      }
    },

    /* ================ 关闭sku浮层 ================ */

    onClose() {
      // 关闭sku触发事件
      const { show, selectedSku, selectedNum, selectedSkuComb } = this.data;
      if (show) {
        this.triggerEvent('sku-close', {
          selectedNum,
          selectedSku,
          selectedSkuComb,
        });
      }
    },

    onShowChange(show) {
      if (!show && this.data.resetStepperOnHide) {
        this.onNumChange({ detail: this.data.startSaleNum || 1 });
      }
    },
  },
});
