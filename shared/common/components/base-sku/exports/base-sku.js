
import {
  isAllSelected,
  isSkuChoosable,
  getSkuComb,
  getSelectedSkuValues
} from '../common/sku-helper';

import { LIMIT_TYPE, UNSELECTED_SKU_VALUE_ID } from '../common/constants';

export default {
  options: {
    multipleSlots: true
  },

  properties: {
    themeClass: String,
    sku: {
      type: Object,
      observer: 'resetSelectedSku'
    },
    goods: Object,
    extraData: {
      type: Object,
      value: {}
    },
    stepperTitle: {
      type: String,
      value: '购买数量'
    },
    quotaText: {
      type: String,
      value: ''
    },
    cartCount: Number, // guang 小程序依赖该参数
    quota: {
      type: Number,
      value: 0
    },
    quotaUsed: {
      type: Number,
      value: 0
    },
    hideStock: {
      type: Boolean,
      value: false
    },
    buyText: {
      type: String,
      value: '立即购买'
    },
    resetStepperOnHide: {
      type: Boolean,
      value: false
    },
    showBuyBtn: {
      type: Boolean,
      value: true
    },
    showAddCartBtn: {
      type: <PERSON>olean,
      value: true
    },
    bodyOffsetTop: {
      type: Number,
      value: 200
    },
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'onShowChange'
    },
    usePopup: {
      type: <PERSON><PERSON>an,
      value: true
    },
    isVirtual: {
      type: Boolean,
      value: false
    },
    virtualForPriceCalendar: {
      type: Object,
      value: {}
    },
    calendarInfoData: {
      type: Object,
      value: {}
    },
    ecardExtra: {
      type: Object,
      value: {}
    }
  },

  data: {
    selectedSku: {},
    selectedNum: 1,
    selectedSkuComb: null,
    popupShow: false,
  },

  methods: {
    addRef({ detail: data }) {
      const { refKey, ref } = data;
      this.refs = this.refs || {};
      this.refs[refKey] = ref;
    },

    resetSelectedSku(sku) {
      const skuTree = sku.tree;
      const selectedSku = {};
      // 重置selectedSku
      skuTree.forEach(item => {
        selectedSku[item.kS] = UNSELECTED_SKU_VALUE_ID;
      });
      // 只有一个sku规格值时默认选中
      skuTree.forEach(item => {
        const key = item.kS;
        const valueId = item.v[0].id;
        if (item.v.length === 1 && isSkuChoosable(sku.list, selectedSku, { key, valueId })) {
          selectedSku[key] = valueId;
        }
      });
      const selectedSkuComb = this.getSelectedSkuComb(selectedSku);

      this.setYZData({
        selectedSku,
        selectedSkuComb
      });
    },

    getSelectedSkuComb(selectedSku) {
      const { sku } = this.data;

      if (sku.none_sku) {
        return {
          id: sku.collection_id,
          price: sku.price,
          stock_num: sku.stock_num
        };
      }
      if (this.isSkuCombSelected(selectedSku)) {
        return getSkuComb(sku.list, selectedSku);
      }
      return null;
    },

    isSkuCombSelected(selectedSku) {
      return isAllSelected(this.data.sku.tree, selectedSku);
    },

    getSkuMessages() {
      const refs = this.refs || {};
      return refs.skuMessages ? refs.skuMessages.getMessages() : {};
    },

    getCartSkuMessages() {
      const refs = this.refs || {};
      return refs.skuMessages ? refs.skuMessages.getCartMessages() : [];
    },

    validateSkuMessages() {
      const refs = this.refs || {};
      return refs.skuMessages ? refs.skuMessages.validateMessages() : '';
    },

    validateSku() {
      if (this.data.selectedNum === 0) {
        return '商品已经无法购买啦';
      }

      // 价格日历情况下
      if (this.data.virtualForPriceCalendar && this.data.isVirtual && this.data.virtualForPriceCalendar.validity_type == 3) {
        if (!this.data.componentSKU || !this.data.componentSKU.isPriceCalendar || !this.data.calendarInfoData.dateHasSelect) {
          return '请选择日期';
        }
      }

      // 价格日历参团情况下
      if (this.data.goods.ecardExtra && this.data.goods.ecardExtra.validityType == 3) {
        if (!this.data.componentSKU || !this.data.componentSKU.isPriceCalendar || !this.data.calendarInfoData.dateHasSelect) {
          return '请选择日期';
        }
      }

      if (this.isSkuCombSelected(this.data.selectedSku)) {
        return this.validateSkuMessages();
      }

      return '请选择完整的规格';
    },

    onSelect({ detail: skuValue }) {
      // 点击已选中的sku时则取消选中
      const selectedSku = this.data.selectedSku[skuValue.skuKeyStr] === skuValue.id
        ? { ...this.data.selectedSku, [skuValue.skuKeyStr]: UNSELECTED_SKU_VALUE_ID }
        : { ...this.data.selectedSku, [skuValue.skuKeyStr]: skuValue.id };

      const selectedSkuComb = this.getSelectedSkuComb(selectedSku);

      this.setYZData(
        {
          selectedSku,
          selectedSkuComb
        },
        () => {
          this.triggerEvent('sku-selected', {
            skuValue,
            selectedSku,
            selectedSkuComb
          });
          if ((this.data.isVirtual && this.data.virtualForPriceCalendar.validity_type == 3) || (this.data.goods.ecardExtra && this.data.goods.ecardExtra.validityType == 3)) {
            this.triggerEvent('get-calendar', selectedSkuComb);
          }
        }
      );
    },

    onClose() {
      this.togglePopup();
    },

    onNumChange({ detail: num }) {
      this.setYZData({
        selectedNum: num
      });
    },

    onOverLimit({ detail: data }) {
      const {
        action, limitType, quota, quotaUsed
      } = data;

      if (action === 'minus') {
        wx.showToast({
          title: '至少选择一件',
          icon: 'none'
        });
      } else if (action === 'plus') {
        if (limitType === LIMIT_TYPE.QUOTA_LIMIT) {
          let msg = `限购${quota}件`;
          if (quotaUsed > 0) msg += `，你已购买${quotaUsed}件`;
          wx.showToast({
            title: msg,
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: '库存不足',
            icon: 'none'
          });
        }
      }
    },

    onAddCart() {
      this.onBuyOrAddCart('addcart');
    },

    onBuy() {
      this.onBuyOrAddCart('buy');
    },

    onBuyOrAddCart(type) {
      const error = this.validateSku();
      if (error) {
        wx.showToast({
          title: error,
          icon: 'none'
        });
      } else {
        this.triggerEvent(type, this.getSkuData());
      }
    },

    onShowChange(val) {
      this.setYZData({
        popupShow: val,
      });
    },

    onSkuClose() {
      const {
        sku, selectedSku, selectedNum, selectedSkuComb, resetStepperOnHide
      } = this.data;

      const selectedSkuValues = getSelectedSkuValues(sku.tree, selectedSku);

      this.triggerEvent('sku-close', {
        selectedSkuValues,
        selectedNum,
        selectedSkuComb
      });

      if (resetStepperOnHide) {
        this.onNumChange({ detail: 1 });
      }
    },

    getSkuData() {
      const {
        goods: { id },
        sku,
        selectedSku,
        selectedNum,
        selectedSkuComb
      } = this.data;
      const selectedSkuValues = getSelectedSkuValues(sku.tree, selectedSku).map(
        value => value.name
      );
      return {
        selectedNum,
        selectedSkuComb,
        selectedSkuValues,
        goodsId: id,
        messages: this.getSkuMessages(),
        cartMessages: this.getCartSkuMessages()
      };
    },

    togglePopup() {
      // 没有v-model的hack写法=。=
      this.setYZData({
        popupShow: false,
      });
      this.onSkuClose();
    },

    // 价格日历选择日期后触发的内容
    onDateItemClick(e) {
      // 获取选择日期
      const selectDate = e.detail.dataset.infodate;
      this.setYZData({
        'calendarInfoData.dateHasSelect': selectDate,
        'componentSKU.isPriceCalendar': true
      });
      // 修改skuid
      let selectedSkuComb = this.getSelectedSkuComb(this.data.selectedSku);

      selectedSkuComb = {
        ...selectedSkuComb,
        parent_sku_id: e.detail.dataset.dateskuid,
        id: e.detail.dataset.dateskuid
      };

      this.setYZData({
        selectedSkuComb
      });
      console.log('selectedSkuComb', this.data.selectedSkuComb);
      this.triggerEvent('sku-date', e.detail);
    }
  }
};
