@mixin base-item {
  padding: 0 8px;
  margin: 0 12px 12px 0;
  min-width: 52px;
  font-size: 12px;
  line-height: 12px;
  border-radius: 3px;
  height: 32px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.van-sku-row__item {
  color: #323233;
  background-color: #f7f8fa;

  &,
  &--active,
  &--disabled {
    @include base-item;
  }

  &--active {
    color: #fff;
  }

  &--disabled {
    background: #f7f8fa;
    color: #c8c9cc;

    &::after {
      content: '';
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba($color: #fff, $alpha: 0.67);
    }
  }

  &-img {
    z-index: 1;
    width: 24px;
    height: 24px;
    object-fit: cover;
    border-radius: 2px;
    margin: 4px 8px 4px -4px;
  }
}
