import WscComponent from 'shared/common/base/wsc-component';
import skuBehavior from '../common/sku-behavior';
import { isSkuChoosable } from '../common/sku-helper';
import { imageTransform } from '../common/image';

WscComponent({
  behaviors: [skuBehavior],

  properties: {
    skuValue: {
      type: Object,
      observer: 'updateRowItemValue',
    },
    skuKeyStr: String,
    selectedSku: {
      type: Object,
      observer: 'updateRowItemClass',
    },
    disableSoldoutSku: {
      type: Boolean,
      value: true,
    },
    skuList: {
      type: Object,
    },
  },

  data: {
    rowItemClass: 'van-sku-row__item',
    isActive: false,
    value: {},
  },

  relations: {
    '../sku-row/index': {
      type: 'parent',
    },
  },

  methods: {
    updateRowItemValue() {
      const { skuValue } = this.data;

      if (skuValue.imgUrl) {
        skuValue.imgUrl = imageTransform(skuValue.imgUrl);
      }

      this.setYZData({
        value: skuValue,
      });
    },
    updateRowItemClass() {
      let className = 'van-sku-row__item';
      let isActive = false;
      if (!this.isChoosable()) {
        className = 'van-sku-row__item--disabled';
      } else if (this.isChoosed()) {
        className = 'van-sku-row__item--active';
        isActive = true;
      }

      this.setYZData({
        rowItemClass: className,
        isActive,
      });
    },

    isChoosed() {
      return (
        +this.data.skuValue.id === +this.data.selectedSku[this.data.skuKeyStr]
      );
    },

    isChoosable() {
      const { skuList, selectedSku, skuKeyStr, skuValue } = this.data;
      if (!this.data.disableSoldoutSku) {
        return true;
      }

      return isSkuChoosable(skuList, selectedSku, {
        key: skuKeyStr,
        valueId: skuValue.id,
      });
    },

    onSkuSelect() {
      if (this.isChoosable()) {
        this.triggerEvent('sku:select', {
          ...this.data.skuValue,
          skuKeyStr: this.data.skuKeyStr,
        });
      }
    },
  },
});
