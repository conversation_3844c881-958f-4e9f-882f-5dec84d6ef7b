import WscComponent from 'shared/common/base/wsc-component/index';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();

WscComponent({
  externalClasses: ['btn-class'],

  properties: {
    isAuthorize: Boolean,
    // 订阅消息的场景值
    subscribeScene: {
      type: String,
      observer(val) {
        if (val) {
          this.getTemplateIds();
        }
      }
    },
    // 手机号登录的场景值
    scene: String
  },

  methods: {
    handleBtnClick() {
      if (!this.data.subscribeScene) {
        this.triggerEvent('next', true);
        return;
      }
      if (this.tmplIds) {
        this.handleSubscribeMessage();
      } else {
        this.getTemplateIds()
          .then(() => {
            this.handleSubscribeMessage();
          })
          .catch(() => {
            this.triggerEvent('next', true);
          });
      }
    },

    // 获取模板id
    getTemplateIds() {
      const { subscribeScene } = this.data;
      return app
        .request({
          path: '/wscump/common/get-template.json',
          data: {
            scene: subscribeScene
          }
        })
        .then((data = {}) => {
          this.tmplIds = data.templateIdList || [];
        });
    },

    // 唤起订阅弹窗
    handleSubscribeMessage() {
      console.log('handleSubscribeMessage tmpId:', this.tmplIds);
      const that = this;
      if (wx.requestSubscribeMessage && this.tmplIds && this.tmplIds.length) {
        console.log('wx.requestSubscribeMessage:', this.tmplIds);
        wx.requestSubscribeMessage({
          tmplIds: this.tmplIds,
          success() {
            that.subscribeCallback();
          },
          fail(err) {
            console.log(err);
          },
          complete() {
            that.triggerEvent('next', true);
          }
        });
        return;
      }
      this.triggerEvent('next', true);
    },

    // 订阅回调
    subscribeCallback() {
      const { subscribeScene } = this.data;

      app.request({
        path: '/wscump/common/subscription-callback.json',
        data: {
          scene: subscribeScene,
          templateIdList: this.tmplIds
        }
      });
    }
  }
});
