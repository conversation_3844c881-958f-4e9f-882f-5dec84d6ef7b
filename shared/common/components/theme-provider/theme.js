function getTheme() {
  if (!getApp) {
    return Promise.resolve(0);
  }

  const app = getApp();
  return app.getShopTheme();
}

export function getThemeStyle() {
  return getTheme().then(({ colors }) => {
    return [
      colors.general,
      colors['main-bg'],
      colors['main-text'],
      colors['vice-bg'],
      colors['vice-text'],
      colors['start-bg'],
      colors['end-bg'],
    ];
  });
}

export const themeTemplate = (config) => `
--theme-general: ${config[0]};
--theme-main-bg: ${config[1]};
--theme-main-text: ${config[2]};
--theme-vice-bg: ${config[3]};
--theme-vice-text: ${config[4]};
--theme-gradient-start: ${config[5]};
--theme-gradient-end: ${config[6]};
`;

export const getThemeVars = () => {
  return getTheme().then(({ colors }) => {
    return Object.keys(colors).reduce((themeVars, curVar) => {
      return themeVars + `--${curVar}:${colors[curVar]};`;
    }, '');
  });
};
