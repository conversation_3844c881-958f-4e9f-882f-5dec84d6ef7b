import WscComponent from 'shared/common/base/wsc-component/index';
import { getThemeStyle, themeTemplate, getThemeVars } from './theme';

WscComponent({
  properties: {
    template: {
      type: Function,
      observer: 'updateStyle',
    },
  },

  data: {
    style: '',
  },

  attached() {
    this.updateStyle();
  },

  methods: {
    updateStyle() {
      const { template } = this.data;
      Promise.all([getThemeStyle(), getThemeVars()]).then(
        ([config, themeVars]) => {
          let style = themeTemplate(config) + themeVars;
          if (typeof template === 'function') {
            style += template(config);
          }
          this.setYZData({ style });
        }
      );
    },
  },
});
