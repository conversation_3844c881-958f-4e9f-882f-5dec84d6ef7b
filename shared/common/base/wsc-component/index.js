import { setYZData, cancelYZData, watchYZData } from '@youzan/weapp-utils/lib/set-yz-data/index';
// import { setYZData, cancelYZData, watchYZData } from 'shared/common/base/wsc-data-helper';
import event from 'shared/utils/app-event';
import { wrapH5Vuex } from 'shared/utils/wrap-h5-vuex';
import { Component as VanxComponentConstructor, mapGetters, mapState } from '@youzan/vanx';
import { wxPropsTypeCheck } from './props-check';

// 使用工厂模式，因为definitionFilter没有this指针
export class ComponentBehavior {
  constructor() {
    this.props = {};
    this.behavior = null;
    this._init();
  }

  _init() {
    const self = this;
    this.behavior = Behavior({
      created() {
        // 开发环境，对 setData 进行劫持
        if (!PRODUCTION) {
          const originSetData = this.setData;
          Object.defineProperty(this, 'setData', {
            writable: true,
            value: (data, cb = () => {}) => {
              if (!data.__fromSetYZData && !data.__isAnimation) {
                console.error('请使用 setYZData', data);
              }
              delete data.__fromSetYZData;
              originSetData.call(this, data, cb);
            }
          });
        }
        watchYZData(this, {
          props: self.props,
          methods: self.methods
        });
      },

      detached() {
        cancelYZData(this);
        this.off(null, null);
      },

      definitionFilter(defFields) {
        self.props = defFields.properties || {};
        self.methods = defFields.methods || {};
        // 目前没有处理 definitionFilterArr
      },

      methods: {
        setYZData,
        ...event
      }
    });
  }

  getBehavior() {
    return this.behavior;
  }
}

const externalClasses = [
  'theme-color',
  'theme-bg-color',
  'theme-border-color',
  'theme-button',
  'theme-button-plain'
];

export default function (res = {}) {
  res.behaviors = res.behaviors || [];
  const inst = new ComponentBehavior();
  res.behaviors.push(inst.getBehavior());

  res.externalClasses = externalClasses.concat(res.externalClasses || []);

  // 开发环境检查 wx props 类型定义
  if (!PRODUCTION) {
    wxPropsTypeCheck(res);
  }

  return Component(res);
}

const VanxComponentBehavior = Behavior({
  created() {
    Object.defineProperty(this, '$dispatch', {
      get() {
        return this.$store.dispatch;
      }
    });
    Object.defineProperty(this, '$commit', {
      get() {
        return this.$store.commit;
      }
    });
  }
});

export function VanxComponent(res = {}) {
  wrapH5Vuex(res);
  res.behaviors = res.behaviors || [];
  const inst = new ComponentBehavior();
  res.behaviors.push(inst.getBehavior());
  res.behaviors.push(VanxComponentBehavior);
  res.externalClasses = externalClasses.concat(res.externalClasses || []);

  res.mapData = res.mapData || {};
  if (res.getters) {
    res.mapData = {
      ...res.mapData,
      ...mapGetters(res.getters)
    };
  }

  if (res.state) {
    res.mapData = {
      ...res.mapData,
      ...mapState(res.state)
    };
  }
  return VanxComponentConstructor(res);
}

export function getCurrentPage(component) {
  const pages = (getCurrentPages && getCurrentPages()) || [];
  let page = pages.length > 0 ? pages[pages.length - 1] : {};

  if (!component) return page;

  // 从后往前找到组件所在页面
  for (let index = pages.length - 1; index >= 0; index--) {
    const element = pages[index];
    if (element && element.__wxWebviewId__ === component.__wxWebviewId__) {
      page = element;
      break;
    }
  }

  return page;
}
