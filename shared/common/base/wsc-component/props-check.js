/**
 * 微信 props 合法性检测，参考文档
 * https://developers.weixin.qq.com/miniprogram/dev/reference/api/Component.html
 *
 */
const ALLOW_PROPS_TYPES = [String, Number, Boolean, Object, Array, null];

export function wxPropsTypeCheck(res) {
  const { properties } = res;

  if (!properties) {
    return;
  }

  Object.keys(properties).forEach((key) => {
    const prop = properties[key];

    /**
     * 有 2 种写法，{ min: String } || { min: { type: String, optionalTypes: [String, Number]}}
     */
    const types =
      typeof prop === 'object' && prop !== null
        ? [prop.type, ...(prop.optionalTypes || [])]
        : [prop];

    const otherTypes = types.filter((v) => !ALLOW_PROPS_TYPES.includes(v));

    if (otherTypes.length > 0) {
      console.error(`存在不合法的 properties: ${key} ，请检查`);
    }
  });
}
