import { createStore } from '@ranta/store';

function stateAdapter(state) {
  return () => state;
}

function gettersAdapter(getters) {
  return Object.keys(getters).reduce((r, key) => {
    const func = getters[key];
    r[key] = function () {
      const self = this;
      const state = self;
      const getters = self;
      return func(state, getters);
    };
    return r;
  }, {});
}

function mutationAdapter(mutations) {
  return Object.keys(mutations).reduce((r, key) => {
    const func = mutations[key];
    r[key] = function (...args) {
      const self = this;
      const state = self;
      return func(state, ...args);
    };
    return r;
  }, {});
}

function actionAdapter(actions) {
  return Object.keys(actions).reduce((r, key) => {
    const func = actions[key];
    r[key] = function (...args) {
      const self = this;

      const doAction = (key, value) => {
        return self[key](value);
      };

      return func(
        {
          state: self,
          getters: self,
          commit: doAction,
          dispatch: doAction,
        },
        ...args
      );
    };
    return r;
  }, {});
}

export function vanxToRantaStore(vanxStore) {
  const { state, getters, actions, mutations } = vanxStore;

  return createStore({
    state: stateAdapter(state),
    getters: gettersAdapter(getters),
    actions: {
      ...actionAdapter(actions),
      ...mutationAdapter(mutations),
    },
  });
}
