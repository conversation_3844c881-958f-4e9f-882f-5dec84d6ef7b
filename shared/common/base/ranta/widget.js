import { RantaWidget as PureRantaWidget } from '@youzan/ranta-weapp';
import { ComponentBehavior } from '../wsc-component';

/**
 * @type {import('@youzan/ranta-weapp').RantaWidget}
 */
const RantaWidget = function (res) {
  res.behaviors = res.behaviors || [];
  // use setYzData
  const inst = new ComponentBehavior();
  res.behaviors.push(inst.getBehavior());

  return PureRantaWidget(res);
};

export { RantaWidget };
