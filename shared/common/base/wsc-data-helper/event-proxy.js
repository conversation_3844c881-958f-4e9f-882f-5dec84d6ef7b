import EventEmitter from './event-emitter';

export default class EventProxy extends EventEmitter {
  constructor(options = {}) {
    super();
    const { idle = 10, maxTask = 20 } = options;
    this.status = {
      idle,
      maxTask,
      emitTimer: null,
      lastTime: +new Date(),
    };
    this.queue = [];
  }

  /**
   * 注册任务，侦听.on('run', cb)触发
   * @param {*} event 任务队列
   */
  registEvent(event) {
    this.queue.push(event);

    const currTime = +new Date();
    const deltaTime = currTime - this.status.lastTime;
    const { emitTimer, idle } = this.status;
    if (!emitTimer && deltaTime > idle) {
      // 先前没有事件注册，且大于idle间隔，可以立即执行
      this.callImmediate();
    } else if (deltaTime > idle) {
      // 先前事件已注册，idle间隔较大，需要正常等待上一次执行
    } else {
      const afterInt = Math.max(idle - deltaTime, 0);
      // 这里afterInt一定比idle小
      this.callLater(afterInt);
    }
  }

  runEvent(event, cmpFn = () => {}, fn = () => {}) {
    const delIndex = [];
    const queue = [];
    this.queue.forEach((item, index) => {
      if (cmpFn(item, event)) {
        queue.push(item);
        delIndex.push(index);
      }
    });
    while (queue.length > 0) {
      fn(queue.shift());
    }
    delIndex.reverse();
    delIndex.forEach(index => {
      this.queue.splice(index, 1);
    });
  }

  callLater(afterInt = 10) {
    clearTimeout(this.status.emitTimer);
    if (afterInt > 0) {
      this.status.emitTimer = setTimeout(() => {
        this.status.emitTimer = null;
        this.callImmediate();
      }, afterInt);
    } else {
      this.status.emitTimer = null;
      this.callImmediate();
    }
  }

  callImmediate() {
    const { idle, maxTask } = this.status;
    const queue = this.queue;
    const tasks = [];
    // 是否需要后续继续执行
    let needAfterCalls = false;
    let count = 0;
    // 先执行正常队列
    while (queue.length > 0) {
      if (count >= maxTask) {
        // 超过单次最大任务数队列延时执行
        needAfterCalls = true;
        break;
      }
      tasks.push(queue.shift());
      count++;
    }
    if (needAfterCalls) {
      this.callLater(idle);
    }
    this.status.lastTime = +new Date();
    this.emit('run', { tasks });
  }

  destroy() {
    clearTimeout(this.status.emitTimer);
    this.status.emitTimer = null;
    this.queue = [];
    super.eventReset();
  }
}
