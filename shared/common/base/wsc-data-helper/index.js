import EventProxy from './event-proxy';
import {
  clone as cloneTempData,
  save as saveTempData,
  get as getTempData,
  reset as resetTempData,
  diff as diffTempData,
  // splitForData,
  // splitForRootKey,
  getRootKey,
} from './data-manager';

const _f = () => {};

const _global = {
  pageId: 1,
  setCount: 0,
  setDiffCount: 0,
  setRealCount: 0,
};

const SUPPORT_CHANGE_DATA = {
  tested: false,
  support: false
};

// 检测是否支持this.data属性变更
function getIsSupportChangeData(pageCtx) {
  if (SUPPORT_CHANGE_DATA.tested) {
    return SUPPORT_CHANGE_DATA.support;
  }

  // 测试是否支持 data
  pageCtx.data.__testIsChangeData = true;
  SUPPORT_CHANGE_DATA.tested = true;
  if (pageCtx.data.__testIsChangeData) {
    SUPPORT_CHANGE_DATA.support = true;
    delete pageCtx.data.__testIsChangeData;
  }

  return SUPPORT_CHANGE_DATA.support;
}

// 子任务中，对数据进行diff操作
const taskDiffData = (pageCtx) => {
  const {
    preData, data, dataKeys, cbList = [],
  } = getTempData(pageCtx);
  const callback = () => cbList.forEach(cb => cb.call(pageCtx));

  const newData = Object.create(null);
  const oldData = Object.create(null);

  // 如果setYZData中没有注册的key，则不需要setData
  Object.keys(data).forEach((key) => {
    const rootKey = getRootKey(key);
    if (dataKeys.has(rootKey)) {
      newData[key] = data[key];
      oldData[key] = preData[key];
    }
  });

  const diffData = diffTempData(oldData, newData);

  // console.log('pre', JSON.stringify(preData));
  // console.log('data', JSON.stringify(data));
  // console.log('oldData', JSON.stringify(oldData));
  // console.log('newData', JSON.stringify(newData));
  // console.log('diff', JSON.stringify(diffData));

  if (!diffData || Object.keys(diffData).length === 0) {
    return {
      diffData: null,
      callback,
    };
  }

  return {
    diffData,
    callback,
  };
};

// 去除重复key的任务
// const taskRemoveDuplicate = (tasks) => {
//   // tasks有时序，先逆序，把后面的key枚举
//   tasks.reverse();
//   const pageCtxMap = {};
//   tasks.forEach(item => {
//     const { pageCtx, data } = item;
//     const pageId = pageCtx.__pageId;
//     if (pageId) {
//       if (!pageCtxMap[pageId]) {
//         pageCtxMap[pageId] = new Set();
//       }
//       Object.keys(data).forEach((key) => {
//         if (pageCtxMap[pageId].has(key)) {
//           delete data[key];
//         }
//         pageCtxMap[pageId].add(key);
//       });
//     }
//   });
//   // 回到原来的顺序
//   tasks.reverse();
//   return tasks;
// };

const taskSetData = (pageCtx, data = {}) => {
  if (!data || Object.keys(data).length === 0) {
    return;
  }
  if (!PRODUCTION) {
    _global.setRealCount++;
    data.__fromSetYZData = true;
  }
  try {
    // console.log('[setYZData]', +(new Date()), JSON.stringify(data), pageCtx.__pageId);
    pageCtx.setData(data);
    // console.log('[setData]', +(new Date()), JSON.stringify(pageCtx.data));
  } catch (error) {
    console.error('[setYZData] taskSetData ', error);
  }
};

// diff前做throttle
const beforeDataProxy = new EventProxy({
  idle: 15,
  maxTask: 150,
});

// ?? 这里有一个隐藏问题，超级大的数据会降优先级分批处理，而后续如果依赖超大数据的数据，如果优先级较高，可能会产生冲突
beforeDataProxy.on('run', (event) => {
  let { tasks = [] } = event;
  const usedPageCtxSet = new Set();
  tasks.forEach(item => {
    const { pageCtx } = item;
    // 保证同一个page只执行一次
    if (usedPageCtxSet.has(pageCtx.__pageId)) {
      return;
    }
    usedPageCtxSet.add(pageCtx.__pageId);

    const { diffData, callback } = taskDiffData(pageCtx);
    if (diffData) {
      // // 后处理有时序问题，后面再继续深入
      // taskSetData(pageCtx, diffData);
      // // 如果有diff，reset更新preData
      // resetTempData(pageCtx);

      if (!PRODUCTION) {
        _global.setDiffCount++;
      }

      // const waitKeySets = pageCtx.__waitKeySets;

      // // 立即setData
      // const dataNeedSet = Object.create(null);
      // // 进入队列等待
      // const dataWaitQueue = [];
      // // 队列里单个元素的键值大小
      // const keySizeSingleQueue = 36;
      // // 需要低权重的判断条件
      // const lowWeightKeySize = 36;
      // // 对数据进行分解，分解第一层
      // const temp = splitForData(diffData);
      // // const temp = diffData;
      // // 分解第二层，并存储rootKey
      // const dataSets = splitForRootKey(temp);

      // let treeCount = 0;
      // Object.keys(dataSets).forEach(rootKey => {
      //   treeCount++;
      //   // 如果单个可以下子树分支过多，或已被降权，或整树分支过多，都需要被降权
      //   if ((dataSets[rootKey].length < lowWeightKeySize) && (!waitKeySets.has(rootKey)) && (treeCount < lowWeightKeySize)) {
      //     dataSets[rootKey].forEach(item => {
      //       Object.assign(dataNeedSet, item);
      //     });
      //   } else {
      //     let temp = {};
      //     let count = 0;
      //     while (dataSets[rootKey].length > 0) {
      //       Object.assign(temp, dataSets[rootKey].shift());
      //       count++;
      //       if (count > keySizeSingleQueue) {
      //         dataWaitQueue.push({
      //           rootKey,
      //           data: temp,
      //         });
      //         count = 0;
      //         temp = {};
      //       }
      //     }
      //     dataWaitQueue.push({
      //       rootKey,
      //       data: temp,
      //     });
      //   }
      // });
      // // 进入等待队列
      // while (dataWaitQueue.length > 0) {
      //   const item = dataWaitQueue.shift();
      //   pageCtx.__waitKeySets.add(item.rootKey);
      //   afterDataProxy.registEvent({
      //     pageCtx,
      //     dataSets: item,
      //   });
      // }
      // 后处理有时序问题，后面再继续深入
      const dataNeedSet = {
        ...diffData,
      };
      taskSetData(pageCtx, dataNeedSet);
      // 如果有diff，reset更新preData
      resetTempData(pageCtx);

      // 只有更新数据才callback
      callback();
    }
  });
});

// diff 后做throttle， 最终setData
const afterDataProxy = new EventProxy({
  idle: 10,
  maxTask: 3,
});

afterDataProxy.on('run', (event = {}) => {
  let { tasks = [] } = event;
  const pageRefs = {};
  const pageIds = new Set();
  tasks.forEach(item => {
    const { pageCtx, dataSets } = item;
    pageCtx.__waitKeySets = new Set();
    taskSetData(pageCtx, dataSets.data);
    const pageId = pageCtx.__pageId;
    pageRefs[pageId] = pageCtx;
    pageIds.add(pageId);
  });
  pageIds.forEach(pageId => {
    // 只对相应的key做快照
    resetTempData(pageRefs[pageId], pageRefs[pageId].__waitKeySets);
    pageRefs[pageId].__waitKeySets = new Set();
  });
  const queue = afterDataProxy.queue;
  queue.forEach(item => {
    const { pageCtx, dataSets } = item;
    pageCtx.__waitKeySets.add(dataSets.rootKey);
  });
});

// 立即执行setData，意味着需要把之前队列中的都先执行掉，避免时序问题
const setYZDataImmediately = (pageCtx, dataToSet, cb = () => {}) => {
  if (!PRODUCTION) {
    console.warn('[setYZData] set immediate data!', dataToSet);
  }
  // 依次执行任务队列里的任务
  afterDataProxy.runEvent(
    pageCtx,
    (a, b) => a.pageCtx === b,
    ({ data }) => {
      taskSetData(pageCtx, data);
    }
  );

  let hasBeenSet = false;
  beforeDataProxy.runEvent(
    pageCtx,
    (a, b) => a.pageCtx === b,
    () => {
      if (hasBeenSet) {
        return;
      }
      hasBeenSet = true;
      const { diffData, callback } = taskDiffData(pageCtx);
      if (diffData) {
        taskSetData(pageCtx, diffData);
        callback();
      }
    }
  );

  taskSetData(pageCtx, dataToSet);
  resetTempData(pageCtx);
  cb();
};

/**
 * 优化原生setData
 * @param {*} data 数据
 * @param {*} cbOrOptions 配置
 * @param {*} cbOrOptions.cb 回调函数
 * @param {*} cbOrOptions.immediate 是否立即执行
 */
const setYZData = function (data = {}, cbOrOptions = {}) {
  // 参数处理
  const options = cbOrOptions;
  if (typeof cbOrOptions === 'function') {
    options.cb = cbOrOptions;
  }
  // 测试是否支持直接修改 this.data
  // 不支持就直接调用 setData
  if (!getIsSupportChangeData(this)) {
    this.setData(data, options.cb || _f);
    return;
  }

  if (!PRODUCTION) {
    // 统计性能
    _global.setCount++;
    // console.log('count setYZData', _global);
  }
  data = cloneTempData(data);

  if (cbOrOptions.immediate) {
    // 立即设置data
    setYZDataImmediately(this, data, options.cb || _f);
  } else {
    // console.log('[setYZData]', JSON.stringify(data));
    // 将需要同步的数据写入缓存数据中
    saveTempData(this, data, options.cb || _f);
    // 将需要渲染的数据发布到事件队列里
    registDataSetting(this);
  }
};

/**
 * 流程：
 * 1. 对page原始对象记录 preData(原来data状态) afterData(未来data状态)
 * 2. 在pre中注册事件，待特定时隙统一处理
 * 3. diff pageCtx中的afterData 与 preData， diff完以后，清除pageCtx以便后处理
 * 4. 在after中注册事件，待特定时隙统一处理
 * 5. 处理真正的setData
 * @param {*} pageCtx 上下文
 * @param {*} options
 */
function registDataSetting(pageCtx) {
  if (!pageCtx.__pageId) {
    // 组件第一次渲染，需要记录__pageId
    pageCtx.__pageId = Symbol(`page-id-${_global.pageId++}`);
  }

  // 将任务推入预处理队列
  beforeDataProxy.registEvent({
    pageCtx,
  });
}

const cancelYZData = (pageCtx) => {
  beforeDataProxy.runEvent(pageCtx, (a, b) => a.pageCtx === b);
  afterDataProxy.runEvent(pageCtx, (a, b) => a.pageCtx === b);
};

const watchYZData = (pageCtx, { props = {} }) => {
  resetTempData(pageCtx);
  Object.keys(props).forEach(propKey => {
    pageCtx.__propKeySets.add(propKey);
  });
};

export {
  setYZData,
  cancelYZData,
  watchYZData,
  EventProxy,
};
