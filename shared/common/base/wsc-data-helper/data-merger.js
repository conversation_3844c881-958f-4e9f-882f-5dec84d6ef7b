/**
 * 按照微信小程序setData的规则将数据塞到data中
 * @param {*} pageData
 * @param {*} newData
 */
export const mergePageData = (pageData = {}, newData = {}) => {
  Object.keys(newData).forEach((key) => {
    const assignValue = newData[key];
    const pathArr = parsePath(key);
    let prevValue = pageData;
    const pathLen = pathArr.length;
    pathArr.forEach((key, index) => {
      if (index === pathLen - 1) {
        return prevValue[key] = assignValue;
      }

      if (!prevValue[key]) {
        prevValue[key] = {};
      }

      prevValue = prevValue[key];
    });
  });
  return pageData;
};

export const parsePath = (pathStr) => {
  const length = pathStr.length;
  let path = [];
  let variabal = '';
  let number = 0;
  let hasNumber = false;
  let inBracket = false;
  for (let i = 0; i < length; i++) {
    const c = pathStr[i];
    if (c === '\\') {
      if (i + 1 < length
        && (pathStr[i + 1] === '.' || pathStr[i + 1] === '[' || pathStr[i + 1] === ']')) {
        variabal += pathStr[i + 1];
        i++;
      } else {
        variabal += '\\';
      }
    } else if (c === '.') {
      if (variabal) {
        path.push(variabal);
        variabal = '';
      }
    } else if (c === '[') {
      if (variabal) {
        path.push(variabal);
        variabal = '';
      }
      if (path.length === 0) {
        throw new Error('path can not start with []: ' + pathStr);
      }
      inBracket = true;
      hasNumber = false;
    } else if (c === ']') {
      if (!hasNumber) {
        throw new Error('must have number in []: ' + pathStr);
      }
      inBracket = false;
      path.push(number);
      number = 0;
    } else if (inBracket) {
      if (c < '0' || c > '9') {
        throw new Error('only number 0-9 could inside []: ' + pathStr);
      }
      hasNumber = true;
      number = ((number * 10) + c.charCodeAt(0)) - 48;
    } else {
      variabal += c;
    }
  }
  if (variabal) {
    path.push(variabal);
  }
  if (path.length === 0) {
    throw new Error('path can not be empty');
  }
  return path;
};
