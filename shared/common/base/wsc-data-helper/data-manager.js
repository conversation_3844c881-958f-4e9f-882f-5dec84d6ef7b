import { mergePageData } from './data-merger';

const getType = obj => {
  const objType = Object.prototype.toString.call(obj);
  return objType.substring(8, objType.length - 1);
};

export const clone = (obj) => {
  if (typeof obj !== 'object') {
    return obj;
  }
  if (obj === null) {
    return null;
  }
  const objType = getType(obj);
  switch (objType) {
    case 'Array':
      return obj.map(item => clone(item));
    case 'Object':
      return Object.keys(obj).reduce((prev, curr) => {
        prev[curr] = clone(obj[curr]);
        return prev;
      }, Object.create(null));
    case 'RegExp':
      return new RegExp(obj);
    case 'Date':
      return new Date(obj);
    default:
      return obj;
  }
};

export const getRootKey = (keyStr = '') => {
  keyStr = keyStr.split('.')[0];
  const len = keyStr.indexOf('[');
  if (len > 0) {
    keyStr = keyStr.substr(0, len);
  }
  return keyStr;
};

const storeDataKeys = (pageCtx, data = {}) => {
  if (!pageCtx.__dataKeySets) {
    pageCtx.__dataKeySets = new Set();
  }
  Object.keys(data).forEach((item) => {
    if (item === '') {
      return;
    }
    pageCtx.__dataKeySets.add(getRootKey(item));
  });
};

// 如果setData的key值跟props相同，需要watch props变更
const checkPropKeys = (pageCtx) => {
  let warning = false;
  if (pageCtx.__propKeySets.size === 0) {
    return warning;
  }
  pageCtx.__dataKeySets.forEach((dataKey) => {
    if (pageCtx.__propKeySets.has(dataKey)) {
      warning = true;
      if (!PRODUCTION) {
        console.warn(`[setYZData] should not use setData into props, key: ${dataKey}`);
      }
    }
  });
  return warning;
};

export function save(pageCtx, data = {}, cb = () => {}) {
  // 直接写入 this.data，数据层生效，模板层不生效
  // this.data 直接赋值不可以，底层做了 defineProperty，拦截了 set
  // 在 2.0.9 以后的版本会支持直接赋值
  // pageCtx.data = mergePageData(pageCtx.data, data);

  // 记录当前次和模板层需要同步的数据，之后一次性同步
  // setData 前数据
  if (!pageCtx.__preData) {
    // 说明是第一次设置
    reset(pageCtx);
  }

  // 将需要setData的key储存下来
  storeDataKeys(pageCtx, data);

  // 检查是否有对props setData的情况
  checkPropKeys(pageCtx);

  // 将数据塞入真正的 data 中
  // 理论上setData 后数据，即为pageCtx.data
  mergePageData(pageCtx.data, data);

  // mergePageData(pageCtx.__tempData, data);

  // 回调函数列表
  const tempCbList = pageCtx.__tempCbList;
  tempCbList.push(cb);
}

export function get(pageCtx) {
  return {
    dataKeys: pageCtx.__dataKeySets,
    propKeys: pageCtx.__propKeySets,
    waitKeys: pageCtx.__waitKeySets,
    preData: pageCtx.__preData,
    data: pageCtx.data,
    cbList: pageCtx.__tempCbList
  };
}

export function reset(pageCtx, keySets = null) {
  // 如果指定了重置的keySets，则只对相应的keySets做重置
  if (keySets) {
    Object.keys(pageCtx.data).forEach(key => {
      if (keySets.has(key)) {
        // console.log(key)
        pageCtx.__preData[key] = clone(pageCtx.data[key]);
      }
    });
    return;
  }
  if (!pageCtx.__propKeySets) {
    // 用于记录properties，只初始化一次
    pageCtx.__propKeySets = new Set();
  }
  if (!pageCtx.__waitKeySets) {
    // 用于记录低权重set的key，只初始化一次
    pageCtx.__waitKeySets = new Set();
  }
  // 只记录当前需要设置
  delete pageCtx.__dataKeySets;
  pageCtx.__dataKeySets = new Set();

  delete pageCtx.__preData;
  pageCtx.__preData = clone(pageCtx.data);

  // delete pageCtx.__tempData;
  // pageCtx.__tempData = clone(pageCtx.data);

  delete pageCtx.__tempCbList;
  pageCtx.__tempCbList = [];
}

// 比较两个对象数据差异，如果取增量，差量置为null
export const diff = (tpl = null, data = null) => {
  if (!data) {
    return null;
  }
  if (!tpl) {
    return clone(data);
  }
  // 存放差集，即data存在且与tpl不一致的
  const diffData = Object.create(null);
  // 存放补集，即tpl中存在的，但是data中不存在的，需要删除
  const compData = Object.create(null);
  // 临时数据，用于存放比较过得上下文，以免重复计算
  const compTplSet = new Set([]);
  const queue = Object.keys(data).map(
    key => ({
      keyPath: key,
      currKey: key,
      currTplRef: tpl,
      currRef: data,
    })
  );
  while (queue.length > 0) {
    const {
      keyPath, currKey, currTplRef, currRef,
    } = queue.shift();
    // 利用广度遍历，先把补集捞出来
    if (!compTplSet.has(currTplRef)) {
      const setB = new Set(Object.keys(currRef));
      const compKey = Object.keys(currTplRef).filter(x => !setB.has(x));
      compKey.forEach(item => {
        const path = keyPath.split('.');
        path.pop();
        path.push(item);
        compData[path.join('.')] = null;
      });
      compTplSet.add(currTplRef);
    }

    // 队列保证了 dataVal 一定存在
    const tplVal = currTplRef[currKey];
    const dataVal = currRef[currKey];
    const tplTpye = getType(tplVal);
    const dataTpye = getType(dataVal);
    if (dataTpye !== tplTpye) {
      // 两者类型不相同，包含了undefined的情况
      if (typeof dataVal === 'undefined') {
        // 小程序里做保护
        diffData[keyPath] = null;
      } else if (dataTpye === 'Array') {
        if (dataVal.length > 20) {
          // 如果数组超长，需要剪枝
          // 先赋予数组类型
          diffData[keyPath] = [dataVal[0]];
          dataVal.forEach((val, index) => {
            if (typeof val === 'undefined') {
              val = null;
            }
            diffData[`${keyPath}[${index}]`] = val;
          });
          delete diffData[`${keyPath}[0]`];
        } else {
          // 小程序有个bug，直接设置a[0]，子组件a.length不更新
          diffData[keyPath] = dataVal;
        }
      } else {
        diffData[keyPath] = dataVal;
      }
    } else if (dataTpye === 'Object') {
      const dataKeys = Object.keys(dataVal);
      const dataKeyLen = dataKeys.length;
      const tplKeyLen = Object.keys(tplVal).length;

      if (dataKeyLen === 0) {
        if (tplKeyLen > 0) {
          // 相当于重置 {}
          diffData[keyPath] = dataVal;
        }
        // 相当于 {} 与 {} 作比较
      } else {
        dataKeys.forEach(
          item => {
            queue.push({
              keyPath: `${keyPath}.${item}`,
              currKey: item,
              currTplRef: tplVal,
              currRef: dataVal,
            });
          }
        );
      }
    } else if (dataTpye === 'Array') {
      const dataLen = dataVal.length;
      const tplLen = tplVal.length;
      if (dataLen < tplLen) {
        // 如果新数组长度小于模板长度
        diffData[keyPath] = dataVal;
      } else if (tplLen === 0) {
        // dataLen >= tplLen && tplLen === 0
        // 相当于 [] 与 [] 或 [...] 与 []
        if (dataLen > 0) {
          diffData[keyPath] = [dataVal[0]];
          for (let i = 1; i < dataLen; i++) {
            queue.push({
              keyPath: `${keyPath}[${i}]`,
              currKey: i,
              currTplRef: tplVal,
              currRef: dataVal,
            });
          }
        }
      } else {
        // 比较两者相同的部分
        for (let i = 0; i < tplLen; i++) {
          queue.push({
            keyPath: `${keyPath}[${i}]`,
            currKey: i,
            currTplRef: tplVal,
            currRef: dataVal,
          });
        }
        // 如果比匹配数组更长，直接赋值多余的部分
        for (let i = tplVal.length; i < dataVal.length; i++) {
          diffData[`${keyPath}[${i}]`] = dataVal[i];
        }
      }
    } else if (tplVal !== dataVal) {
      // 键值原来存在，数据类型一致，且为简单对象/基本类型，数值不一致
      if (dataTpye === 'RegExp') {
        // 正则对象
        if (dataVal.toString() !== tplVal.toString()) {
          diffData[keyPath] = dataVal;
        }
      } else if (dataTpye === 'Date') {
        // 日期对象
        if (dataVal.getTime() !== tplVal.getTime()) {
          diffData[keyPath] = dataVal;
        }
      } else {
        diffData[keyPath] = dataVal;
      }
    }
  }
  return clone(Object.assign(diffData, compData));
};

export const split = (data = {}, storeDataFn = () => {}) => {
  let result = null;
  Object.keys(data).forEach(key => {
    const target = data[key];
    if (getType(target) === 'Array') {
      if (target.length > 1) {
        result = storeDataFn(result, key, [target[0]]);
        for (let i = 1; i < target.length; i++) {
          result = storeDataFn(result, `${key}[${i}]`, target[i]);
        }
      } else {
        result = storeDataFn(result, key, target);
      }
    } else if (getType(target) === 'Object') {
      const objKeys = Object.keys(target);
      if (objKeys.length > 1) {
        result = storeDataFn(result, key, {});
        objKeys.forEach(subKey => {
          result = storeDataFn(result, `${key}.${subKey}`, target[subKey]);
        });
      } else {
        result = storeDataFn(result, key, target);
      }
    } else {
      result = storeDataFn(result, key, target);
    }
  });

  return result;
};

export const splitForRootKey = (data = {}) => {
  const storeDataFn = (dataSets, key, target) => {
    if (!dataSets) {
      dataSets = {};
    }
    const rootKey = getRootKey(key);
    if (!dataSets[rootKey]) {
      dataSets[rootKey] = [];
    }
    dataSets[rootKey].push({
      [key]: target,
    });
    return dataSets;
  };
  return split(data, storeDataFn);
};

export const splitForData = (data = {}) => {
  const storeDataFn = (dataSets, key, target) => {
    if (!dataSets) {
      dataSets = {};
    }
    dataSets[key] = target;
    return dataSets;
  };
  return split(data, storeDataFn);
};
