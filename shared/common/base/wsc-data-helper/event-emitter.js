/**
 * 事件侦听器
 */
export default class EventEmitter {
  constructor() {
    this.eventReset();
  }

  eventReset() {
    if (this._eventListeners) {
      Object.keys(this._eventListeners).forEach(key => {
        delete this._eventListeners[key];
      });
    }
    this._eventListeners = Object.create(null);
  }

  on(funKey, callback) {
    if (!funKey) {
      throw Error({
        message: 'event listener funkey undefined',
        callFunc: 'adapter::wb:_on'
      });
    }
    if (!(callback instanceof Function)) {
      throw Error({
        message: 'event listener next param should be function',
        callFunc: 'adapter::wb:_on'
      });
    }
    this._eventListeners[funKey] = callback;
  }

  emit(funKey, params) {
    if (this._eventListeners && (this._eventListeners[funKey] instanceof Function)) {
      this._eventListeners[funKey](params);
    }
  }
}
