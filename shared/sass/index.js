import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { mapState } from '@youzan/vanx';
import extendCreator from 'utils/extend-creator';
import {
  jumpToLink,
  mapKeysCase,
  cdnImage,
  getUserInfo,
} from './common/process';
import { onProtocolAgree, cmProtocolAgree } from './common/event';
import featurePageConfig from './feature/page-config';
import userCenterPageConfig from './user-center/page-config';
import memberCenterPageConfig from './member-center/page-config';

const extend = extendCreator();

export default extend(
  // 通用能力的开放
  {
    mapData: {
      ...mapState([
        'groupAlias',
        'neededMoreStoreBalance',
        'isWeixinCardEnabled',
        'pageQuery',
      ]),
    },
    ...YunPageConfig,
    onLoad() {
      const sdk = this.getYunSdk();
      // 同意协议
      sdk.setPageEvent('procotolAgree', onProtocolAgree);
      cmProtocolAgree().then(() => {
        onProtocolAgree.trigger();
      });

      // jumpToLink能力
      sdk.setPageProcess('jumpToLink', jumpToLink.bind(this));
      // key名转换工具
      sdk.setPageProcess('mapKeysCase', mapKeysCase);
      // 压缩图片能力
      sdk.setPageProcess('cdnImage', cdnImage);
      // 获取用户信息
      sdk.setPageProcess('getUserInfo', getUserInfo);
    },
  },
  // 微页面能力开放
  featurePageConfig,
  // 个人中心能力开放
  userCenterPageConfig,
  // 会员中心能力开放
  memberCenterPageConfig
);
