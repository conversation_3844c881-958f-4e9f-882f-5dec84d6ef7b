import getApp from 'shared/utils/get-safe-app';
import {
  getMemberCode,
  getUserLevelOfFree,
  getUserLevelOfPay,
  getLevelListOfFree,
  getLevelListOfPay,
  getSalesmanData,
  removeLevel,
} from './api';
import { addLevelBenefitList } from './utils';
import { getLevelColorCodeMapPromise } from 'shared/utils/async-level-map';

const LevelType = {
  FREE: 1, // 免费
  PAY: 2, // 付费
};

export function mcGetMemberCode() {
  const app = getApp();

  return getMemberCode({
    kdt_id: app.getKdtId(),
  });
}

export function mcGetUserLevelOfFree(data = {}) {
  return getUserLevelOfFree({
    type: LevelType.FREE, // 免费等级
    withSyncInfo: 1,
    withConsumerData: '1', // * 获取用户近x个月的消费金额数据
    ...data,
  });
}

export function mcGetUserLevelOfPay(data = {}) {
  return getUserLevelOfPay({
    type: LevelType.PAY,
    ...data,
  });
}

export function mcGetLevelListOfFree(data = {}) {
  return getLevelListOfFree({
    type: LevelType.FREE,
    ...data,
  }).then((res) => addLevelBenefitList(res));
}
export function mcGetColorCodeMap() {
  return new Promise((resolve) => {
    getLevelColorCodeMapPromise().then(({ LevelColorCodeMap }) => {
      resolve(LevelColorCodeMap);
    });
  });
}
export function mcGetLevelListOfPay(data = {}) {
  return getLevelListOfPay({
    type: LevelType.PAY,
    ...data,
  }).then((res) => addLevelBenefitList(res));
}

export function mcGetSalesmanData() {
  return getSalesmanData();
}

// 退出当前等级
export function mcRemoveLevel() {
  if (this.data.userLevel?.level?.levelAlias) {
    return removeLevel({
      operatorType: 'INITIATIVE_QUIT', // LevelUpgradeType.INITIATIVE_QUIT 5 退出，后端接口要求传字符串
      levelAlias: this.data.userLevel?.level?.levelAlias,
    });
  }
  console.warn('等级信息还未获取完成，无法退出等级！');
  return Promise.reject('等级信息还未获取完成，无法退出等级！');
}
