// 等级列表数据塞入等级对应权益列表数据，逻辑参考src/packages/shop/levelcenter/level-common/utils/adapter.js:503 levelAdapter方法
export function addLevelBenefitList(leveListInfo) {
  const { levelList, cardBenefitList } = leveListInfo;
  levelList[0].levelV2List.forEach((level) => {
    const levelBenefitList = cardBenefitList.find(
      (benefitList) => benefitList.levelAlias === level.levelAlias
    );
    level.benefitList = levelBenefitList?.benefitList || [];
  });

  return leveListInfo;
}
