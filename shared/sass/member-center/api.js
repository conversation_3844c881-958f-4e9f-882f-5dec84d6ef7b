import { requestWrapper, requestWrapperWithGuideParams } from '../common/api';

export function getMemberCode(data) {
  return requestWrapper({
    path: '/wscuser/scrm/member-code/api/getCode.json',
    method: 'get',
    data,
  });
}

export function getUserLevelOfFree(data) {
  return requestWrapperWithGuideParams({
    path: '/wscuser/levelcenter/api/userLevelDetail.json',
    data,
  });
}

export function getUserLevelOfPay(data) {
  return requestWrapper({
    origin: 'cashier',
    path: '/pay/wscuser/pluscenter/api/userLevelDetail.json',
    data,
  });
}

export const getLevelListOfFree = (data) => {
  return requestWrapper({
    path: '/wscuser/levelcenter/api/getLevelListV2.json',
    data,
  });
};

export const getLevelListOfPay = (data) => {
  return requestWrapper({
    origin: 'cashier',
    path: '/pay/wscuser/pluscenter/api/getLevelListV2.json',
    data,
  });
};

export const getSalesmanData = () => {
  const app = getApp();
  return new Promise((resolve, reject) => {
    app.carmen({
      api: 'youzan.salesman.wap.account/1.0.0/get',
      success: (resp) => {
        resolve(resp);
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};

// 退出当前等级
export const removeLevel = (data) => {
  return requestWrapper({
    path: '/wscuser/level/api/removeLevel.json',
    data,
  });
};
