import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { mapState } from '@youzan/vanx';
import {
  mcGetMemberCode,
  mcGetUserLevelOfFree,
  mcGetUserLevelOfPay,
  mcGetLevelListOfFree,
  mcGetLevelListOfPay,
  mcGetSalesmanData,
  mcRemoveLevel,
} from './process';

export default {
  mapData: {
    ...mapState(['userLevel']),
  },

  ...YunPageConfig,

  onLoad() {
    const sdk = this.getYunSdk();

    // 获取会员码信息
    sdk.setPageProcess('mcGetMemberCode', mcGetMemberCode);

    sdk.setPageProcess('mcGetUserLevelOfFree', mcGetUserLevelOfFree);

    sdk.setPageProcess('mcGetUserLevelOfPay', mcGetUserLevelOfPay);

    sdk.setPageProcess('mcGetLevelListOfFree', mcGetLevelListOfFree);

    sdk.setPageProcess('mcGetLevelListOfPay', mcGetLevelListOfPay);

    sdk.setPageProcess('mcGetSalesmanData', mcGetSalesmanData);

    // 退出当前等级
    sdk.setPageProcess('mcRemoveLevel', mcRemoveLevel.bind(this));
  },
};
