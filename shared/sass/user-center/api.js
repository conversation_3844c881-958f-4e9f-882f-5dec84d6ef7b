import { requestWrapper } from '../common/api';

export function getOrders(data) {
  return requestWrapper({
    path: '/wscuser/membercenter/orders.json',
    method: 'get',
    data,
  });
}

export function getStats(data) {
  return requestWrapper({
    path: '/wscuser/membercenter/stats.json',
    method: 'get',
    data,
  });
}

export function getBalance(data) {
  return requestWrapper({
    path: '/wscuser/membercenter/getBalanceV2Json',
    method: 'get',
    data,
  });
}

export function getLevelAndGrowth(data) {
  return requestWrapper({
    path: '/wscuser/membercenter/level-growth.json',
    method: 'get',
    data,
  });
}

export function getOpenData(data) {
  return requestWrapper({
    path: '/wscuser/membercenter/openData.json',
    method: 'get',
    data,
    checkProtocol: false,
  });
}

export function getRelationInfo(data) {
  return requestWrapper({
    path: '/wscuser/scrm/api/getRelationInfo.json',
    method: 'get',
    data,
  });
}
