import { AsyncEvent } from '@youzan/weapp-ecloud-sdk';
import EventUtil from '@youzan/weapp-utils/lib/event';
import deepClone from '@youzan/weapp-utils/lib/clone-deep/index';

import { updateUCOrders, updateUCAssetsInfo } from './open-state';

export const ucPageInfoReady = new AsyncEvent();

export function ucOrdersPromise(sdk) {
  return new Promise((resolve) => {
    EventUtil.on('UCOrdersLoaded', (data) => {
      updateUCOrders(sdk, deepClone(data));
      resolve({ orderInfo: deepClone(data) });
    });
  });
}

export function ucAssetsInfoPromise(sdk) {
  return new Promise((resolve) => {
    EventUtil.on('UCAssetsInfoLoaded', (data) => {
      updateUCAssetsInfo(sdk, deepClone(data));
      resolve({ assetsInfo: deepClone(data) });
    });
  });
}
