import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { ucOrdersPromise, ucAssetsInfoPromise, ucPageInfoReady } from './event';
import {
  ucGetOrders,
  ucGetStats,
  ucGetBalance,
  ucGetLevelAndGrowth,
  ucGetDynamicsOpenData,
  ucGetRelationInfo,
  ucGetPointsName,
} from './process';

export default {
  ...YunPageConfig,

  onLoad() {
    const sdk = this.getYunSdk();

    sdk.setPageEvent('ucPageInfoReady', ucPageInfoReady);

    // 合并
    Promise.all([ucOrdersPromise(sdk), ucAssetsInfoPromise(sdk)]).then(
      ([orderInfo = {}, assetsInfo = {}]) => {
        const info = {
          ...orderInfo,
          ...assetsInfo,
        };

        ucPageInfoReady.trigger(info).catch((e) => {
          throw e;
        });
      }
    );
    // 动态开放数据
    sdk.setPageProcess('ucGetDynamicsOpenData', ucGetDynamicsOpenData);
    // 获取订单信息
    sdk.setPageProcess('ucGetOrders', ucGetOrders);
    // 获取积分、卡券等信息
    sdk.setPageProcess('ucGetStats', ucGetStats);
    // 获取零钱
    sdk.setPageProcess('ucGetBalance', ucGetBalance);
    // 会员等级和名称
    sdk.setPageProcess('ucGetLevelAndGrowth', ucGetLevelAndGrowth);
    // 获取关系人信息
    sdk.setPageProcess('ucGetRelationInfo', ucGetRelationInfo);
    // 获取积分名称
    sdk.setPageProcess('ucGetPointsName', ucGetPointsName);
  },
};
