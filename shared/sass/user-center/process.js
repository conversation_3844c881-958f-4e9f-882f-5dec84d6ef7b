import getApp from 'shared/utils/get-safe-app';

import {
  getOrders,
  getStats,
  getBalance,
  getLevelAndGrowth,
  getOpenData,
  getRelationInfo,
} from './api';

export function ucGetOrders() {
  const app = getApp();

  return getOrders({
    kdt_id: app.getKdtId(),
  });
}

export function ucGetStats() {
  const app = getApp();

  return getStats({
    kdt_id: app.getKdtId(),
    currentKdtId: app.getKdtId(), // 兼容连锁
    version: app.getVersion(),
    needConsumptionAboveCoupon: 1,
  });
}

export function ucGetBalance() {
  const app = getApp();

  return getBalance({
    kdt_id: app.getKdtId(),
  });
}

export function ucGetLevelAndGrowth() {
  const app = getApp();

  return getLevelAndGrowth({
    kdt_id: app.getKdtId(),
  });
}

export function ucGetDynamicsOpenData(params) {
  const app = getApp();

  return getOpenData({
    kdt_id: app.getKdtId(),
    ...params,
  });
}

// 获取关系人信息
export function ucGetRelationInfo() {
  const app = getApp();

  return getRelationInfo({
    kdtId: app.getKdtId(),
  })
    .then(({ valid, relationPersonInfoList = [] }) => {
      if (!valid) {
        return {
          valid: false,
        };
      }

      return {
        valid,
        relationPersons: relationPersonInfoList.map(
          ({
            attributeValueInfos = [],
            birthday,
            relationAppellation,
            childrenMonthAge,
          }) => {
            const attributes = attributeValueInfos.map(
              ({
                name,
                value,
                attributeId,
                attributeType,
                dataType,
                extValue,
              }) => ({
                name,
                dataType,
                value,
                extValue,
                isBirthday: +attributeId === 5 && +attributeType === 1, // 是否为生日项
              })
            );

            return {
              birthday,
              relationAppellation,
              childrenMonthAge,
              attributes,
            };
          }
        ),
      };
    })
    .catch(() => {
      return {
        valid: false,
      };
    });
}

// 获取积分名称
export function ucGetPointsName() {
  const app = getApp();

  return app.getPointsName();
}
