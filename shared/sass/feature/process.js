import Event from '@youzan/weapp-utils/lib/event';
import { getPlayInfo } from 'shared/components/showcase/video/api.js';
import { getGoodsList } from 'shared/components/showcase/goods/api.js';
import { getStoreLocation, getGoodsList as getGoodsListV2 } from './api';

export function fetchSpecificAlias(alias) {
  Event.trigger('home:fetchedSpecAlias', { alias });
}

export function fetchVideoInfoById(options = {}) {
  return getPlayInfo(options);
}

export const fetchStoreLocation = ({ storeId }) => {
  return getStoreLocation({ storeId });
};
export const fetchGoodsInfo = ({
  tagId,
  page,
  pageSize: goodsListPageSize,
  goodsIds: fetchGoodsIds,
  goodsFrom,
  kdtId,
  offlineId,
  goodsNumber,
  needActivity = 0,
  intelligentSortConfig,
  showSoltOutDisplay,
  allGoodsIds,
  activityPriceIndependent = 1,
  templateId,
  orderBy,
  containsComponentsPicture,
}) => {
  return getGoodsList({
    tagId,
    page,
    pageSize: goodsListPageSize,
    goodsIds: fetchGoodsIds,
    goodsFrom,
    kdtId,
    offlineId,
    goodsNumber,
    needActivity,
    intelligentSortConfig,
    showSoltOutDisplay,
    allGoodsIds,
    activityPriceIndependent,
    templateId,
    orderBy,
    containsComponentsPicture,
  });
};

export const fetchGoodsList = (data) => {
  return getGoodsListV2(data);
};
