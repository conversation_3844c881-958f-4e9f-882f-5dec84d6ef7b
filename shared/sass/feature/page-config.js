import YunPageConfig from '@/youzanyun-sdk/yun-page-config';
import { getHeight } from 'shared/utils/nav-config';
import {
  beforeFetchHomepage,
  featureAliasPromise,
  onPageScroll,
} from './event';
import {
  fetchSpecificAlias,
  fetchVideoInfoById,
  fetchGoodsInfo,
  fetchStoreLocation,
  fetchGoodsList,
} from './process';
import { mapKeysCase, cdnImage } from '../common/process';
import { updateFeatureAlias } from './open-state';

export default {
  ...YunPageConfig,

  onLoad() {
    const sdk = this.getYunSdk();
    this.__yunPage = sdk.page;

    // 注册开放事件
    sdk.setPageEvent('beforeFetchHomepage', beforeFetchHomepage);
    sdk.setPageEvent('onPageScroll', onPageScroll);

    this.on('sdk:featurePageScroll' + this.getPageKey(), (...args) =>
      onPageScroll.trigger(...args)
    );

    // 标准页面的事件-创建订单前
    this.onAsync('beforeFetchHomepage', (args) => {
      const res = beforeFetchHomepage.trigger(args);
      if (res && Array.isArray(res)) {
        return res[0];
      }
      return res;
    });

    // 合并
    Promise.all([featureAliasPromise(sdk)]).then(([alias]) => {
      updateFeatureAlias(sdk, alias);
    });

    // 注册开放流程
    sdk.setPageProcess('fetchSpecificAlias', fetchSpecificAlias);
    // sdk查询视频媒体详情
    sdk.setPageProcess('fetchVideoInfoById', fetchVideoInfoById);
    // 查询商品，商品分组
    sdk.setPageProcess('fetchGoodsInfo', fetchGoodsInfo);
    // 查询门店位置信息
    sdk.setPageProcess('fetchStoreLocation', fetchStoreLocation);
    // 获取商品、商品分组接口，支持排序
    sdk.setPageProcess('fetchGoodsList', fetchGoodsList);
    // key名转换工具
    sdk.setPageProcess('mapKeysCase', mapKeysCase);
    // 压缩图片能力
    sdk.setPageProcess('cdnImage', cdnImage);
    // 获取顶部导航高度
    sdk.setPageProcess('topNavHeight', getHeight);
  },

  onUnload() {
    this.offAsync('beforeFetchHomepage');
    this.off('sdk:featurePageScroll');
    this.__yunPage && this.__yunPage.unload();
  },
};
