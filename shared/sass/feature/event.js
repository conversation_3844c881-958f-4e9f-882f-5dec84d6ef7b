import EventUtil from '@youzan/weapp-utils/lib/event';
import { SyncEvent } from '@youzan/weapp-ecloud-sdk';

// 异步事件
const beforeFetchHomepage = new SyncEvent();

const onPageScroll = new SyncEvent();

function featureAliasPromise() {
  return new Promise((resolve) => {
    EventUtil.on('FeatureAliasLoaded', (alias) => {
      resolve(alias);
    });
  });
}
export { beforeFetchHomepage, featureAliasPromise, onPageScroll };
