import { jumpToLink as jumpLink } from 'shared/components/showcase-options-home/jump-to-link.js';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { getPlugins } from '@youzan/ranta-helper-tee';
import Args from '@youzan/utils/url/args';
import { getGuideBizDataMap } from '@/utils/guide';
import { tryToBindShoppingGuideRelation } from '@/utils/level';
import { openWechatCard } from '@/utils/wechat-card';

const SHOULD_CHECK_AUTH_TYPE = {
  balance: 'Balance',
  coin: 'Coin',
  points: 'Points',
  benefitCard: 'BenefitCard',
  coupons: 'Coupons',
  orderList: 'OrderList',
  refundList: 'RefundList',
  addressList: 'AddressList',
  accountSetting: 'AccountSetting',
  memberCode: 'MemberCode',
  // 任务中心
  taskCenter: 'TaskCenter',
  // 返现
  cashback: 'Cashback',
  // 赠品
  gifts: 'Gifts',
  // 销售员招募计划页面
  salesmanTutorial: 'SalesmanTutorial',
  // 销售员中心页面
  salesmanCenter: 'SalesmanCenter',
  // 关系人
  relationPerson: 'RelationPerson',
  // 个人信息设置页
  userInfoSetting: 'UserInfoSetting',
  // 会员规则
  levelRule: 'LevelRule',
  // 特权详情
  benefit: 'Benefit',
  // 完善信息页
  fill: 'Fill',
  // 专属导购链接
  exclusiveGuide: 'ExclusiveGuide',
};

export function jumpToLink(item = {}) {
  const app = getApp();
  const { dmc } = getPlugins();
  const { type, query = {} } = item;
  if (SHOULD_CHECK_AUTH_TYPE[type]) {
    app
      .resolveTeeAPI()
      .getUserPrivacy()
      .then(({ protocol, mobile, nicknameAndAvatar }) => {
        if (protocol && mobile && nicknameAndAvatar) {
          const kdtId = app.getKdtId();

          if (type === 'coin') {
            dmc.navigate('CommonWebview', {
              src: Args.add('/wscassets/change/profile'),
            });
          } else if (type === 'relationPerson') {
            // 关系人页面
            dmc.navigate('CommonWebview', {
              src: Args.add('/wscuser/scrm/relationPerson'),
            });
          } else if (type === 'levelRule') {
            // 会员规则
            dmc.navigate('CommonWebview', {
              src: Args.add('/wscuser/levelcenter/rule'),
            });
          } else if (type === 'benefit') {
            // 特权详情
            const params = {
              kdt_id: app.getKdtId(),
              alias: query.levelAlias,
              benefit: query.index,
              benefit_format: '1', // 当作新版本权益数据结构，如果出现旧版数据结构的数据，逻辑再调整(可参考src/packages/shop/levelcenter/level-common/simple-benefit-list/index.js:94)
            };
            const weappUrl = Args.add(
              '/packages/levelcenter/benefit/index',
              params
            );
            dmc.navigate(weappUrl);
          } else if (type === 'fill') {
            // 完善信息页
            const {
              groupAlias,
              neededMoreStoreBalance,
              isWeixinCardEnabled,
              pageQuery,
            } = this.data;

            const jumpQuery = {
              ...getGuideBizDataMap(pageQuery), // 携带导购标
              kdtId,
              alias: groupAlias,
              fromScene: 'complete',
            };
            tryToBindShoppingGuideRelation(pageQuery).then(() => {
              // 是储值模式&&未达到储值门槛，跳转充值中心
              if (neededMoreStoreBalance) {
                dmc.redirectTo({
                  url: `/packages/pre-card/recharge/index?entry=1&kdtId=${kdtId}&fromScene=MemberCenter`,
                });
              } else {
                if (isWeixinCardEnabled) {
                  const { from_params, bizType } = pageQuery;

                  openWechatCard(
                    {
                      from_params,
                      bizType,
                    },
                    () => this.reloadData && this.reloadData(true)
                  );
                  return;
                }

                dmc.navigate('CommonWebview', {
                  src: Args.add('/wscuser/levelcenter/fill', {
                    ...pageQuery,
                    ...jumpQuery,
                    fromBiz: 'levelcenter',
                    eT: Date.now(),
                  }),
                });
              }
            });
          } else if (type === 'exclusiveGuide') {
            // 专属导购链接
            dmc.navigate('CommonWebview', {
              src: Args.add('/guide/customer/exclusive-guide/home', {
                kdt_id: kdtId,
                title: '专属导购',
              }),
            });
          } else {
            dmc.navigate(SHOULD_CHECK_AUTH_TYPE[type], { ...query });
          }
        }
      });
    return;
  }
  jumpLink(mapKeysCase.toCamelCase(item), {});
}

const getUserInfo = () => {
  const app = getApp();

  return new Promise((resolve, reject) => {
    app.getUserInfo(
      (res) => {
        res && resolve(res.userInfo);
      },
      (err) => {
        reject(err);
      }
    );
  });
};

export { mapKeysCase, cdnImage, getUserInfo };
