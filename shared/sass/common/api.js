import { node as request } from 'shared/utils/request';
import { getProtocolStatus } from '@youzan/passport-protocol';
import { requestWithGuideParams } from 'utils/guide';
import { cmProtocolAgree } from './event';

export function requestWrapper({ checkProtocol = true, ...rest }) {
  if (checkProtocol) {
    // 个人中心的大部分数据开放都需要协议通过
    return getProtocolStatus()
      .then((status) => {
        if (!status) {
          return cmProtocolAgree();
        }
      })
      .then(() => {
        return request(rest);
      })
      .catch((err) => {
        console.error('checkProtocolFunc error:', err);
        throw err;
      });
  }
  return request(rest);
}

export function requestWrapperWithGuideParams({
  checkProtocol = true,
  ...rest
}) {
  if (checkProtocol) {
    // 个人中心的大部分数据开放都需要协议通过
    return getProtocolStatus()
      .then((status) => {
        if (!status) {
          return cmProtocolAgree();
        }
      })
      .then(() => {
        return requestWithGuideParams(rest);
      })
      .catch((err) => {
        console.error('checkProtocolFunc error:', err);
        throw err;
      });
  }
  return request(rest);
}
