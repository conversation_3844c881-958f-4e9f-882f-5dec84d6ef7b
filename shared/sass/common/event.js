import { SyncEvent } from '@youzan/weapp-ecloud-sdk';
import { onUserAuthSuccess, AuthType } from '@youzan/passport-tee-shared';

export const onProtocolAgree = new SyncEvent();

export function cmProtocolAgree() {
  return new Promise((resolve) => {
    let off = onUserAuthSuccess(({ authTypeList }) => {
      if (authTypeList.includes(AuthType.PROTOCOL)) {
        resolve();
        off();
        off = null;
      }
    });
  });
}
