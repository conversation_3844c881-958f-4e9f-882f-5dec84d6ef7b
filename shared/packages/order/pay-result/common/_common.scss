.result-header {
  position: relative;
  padding: 15px 0 15px 60px;

  &__icon {
    position: absolute;
    top: 16px;
    left: 30px;
    font-size: 20px;
  }

  &__title {
    font-size: 14px;
    color: #333;
  }

  &__desc {
    font-size: 12px;
    color: #ccc;
  }

  &__btn {
    position: absolute;
    top: 14px;
    right: 10px;
    padding: 5px 4px;
  }
}

.action {
  &--normal {
    display: flex;
    margin: 20px 5px 10px;

    .action__button {
      flex: 1;
      padding: 0 5px;
    }
  }

  &--multi {
    display: block;
    margin: 20px 10px 10px;

    .action__button {
      width: 100%;
      margin-top: 10px;
      display: block;
    }
  }
}