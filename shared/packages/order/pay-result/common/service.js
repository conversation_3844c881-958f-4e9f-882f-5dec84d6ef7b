import { findGoodsBuyIds, separateGoodsForLogistics } from 'shared/components/cart/buy';
import get from '@youzan/weapp-utils/lib/get';
import each from '@youzan/weapp-utils/lib/each';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { node } from 'shared/utils/request';

/**
 * 配送方式优先级：同城(2) > 快递(0) > 自提(1)
 */
const EXPRESS_TYPE_PRIORITY = [2, 0, 1];

/**
 * 付款时候，卖家价格变更，提醒买家
 */
export function showChangePriceDialog({ dialog, jumpHandler } = {}) {
  if (!dialog) {
    return;
  }

  const { dialogMsg: content, buttonDTO } = dialog;
  const { defaultText: confirmText, needJump } = buttonDTO || {};

  if (!content || !confirmText) {
    return;
  }

  wx.showModal({
    content,
    showCancel: false,
    confirmText,
    success() {
      if (needJump) {
        jumpHandler();
      }
    }
  });
}

/**
 * 获取待购买商品
 * 商品由于不同配送方式需要分开下单，用于交易成功后提示买家继续购买剩余商品
 */
export function getWaitBuyList({ waitBuyGoodsIds }) {
  return node({
    path: '/wsctrade/cart/goodsList.json'
  })
    .then(data => {
      // "待支付商品列表" 和 "购物车商品" 重叠的商品
      const items = findGoodsBuyIds(waitBuyGoodsIds, get(data, '0.items'));

      if (!items || !items.length) {
        return Promise.reject();
      }

      const separateGoodsList = get(separateGoodsForLogistics(items), 'data') || [];
      // 按配送方式进行优先级排序
      separateGoodsList.sort((o1, o2) => {
        const o1Priority = EXPRESS_TYPE_PRIORITY.indexOf(+get(o1, 'expressType'));
        const o2Priority = EXPRESS_TYPE_PRIORITY.indexOf(+get(o2, 'expressType'));
        if (o1Priority > o2Priority) {
          return 1;
        }
        if (o1Priority < o2Priority) {
          return -1;
        }
        return 0;
      });
      const firstSeparateGoods = separateGoodsList[0];
      const finalGoodsList = get(firstSeparateGoods, 'list');
      const expressType = get(firstSeparateGoods, 'expressType');
      if (!finalGoodsList || !finalGoodsList.length) {
        return Promise.reject();
      }

      each(finalGoodsList, item => {
        item.imgUrl = cdnImage(item.attachmentUrl, '!112x112.jpg');
      });

      return {
        goodsList: finalGoodsList,
        expressType
      };
    });
}
