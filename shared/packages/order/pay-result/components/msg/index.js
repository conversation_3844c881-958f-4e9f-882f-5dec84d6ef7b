import WscComponent from 'shared/common/base/wsc-component';
import openWebView from 'shared/utils/open-web-view';
import args from '@youzan/weapp-utils/lib/args';

const app = getApp();

WscComponent({
  properties: {
    payResult: Object
  },

  methods: {
    openPayDetailWv() {
      const { payResult = {} } = this.data;
      const url = payResult.displayPrompt && payResult.displayPrompt.payTip && payResult.displayPrompt.payTip.url || '';
      if (url) {
        app.logger && app.logger.log({
          et: 'click',
          ei: 'installment_detail',
          en: '分期详情',
          si: app.getKdtId()
        });

        const hashArray = url.split('#');
        const domainUrl = hashArray[0].split('?')[0];

        openWebView(domainUrl, {
          query: args.getAll(url),
          title: '分期详情'
        });
      }
    }
  }
});
