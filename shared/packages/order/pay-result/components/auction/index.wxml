<view class="auction">
  <view class="result-header">
    <van-icon
      class="result-header__icon"
      name="checked"
      color="#4b0"
    />
    <view class="result-header__title">订单支付成功</view>
    <view class="result-header__desc">我们将尽快给您发货</view>
  </view>
  <van-cell-group>
    <van-cell custom-class="auction__title" title="{{ payResult.overSold ? '被他抢拍走了' : '恭喜你抢到了' }}" />
    <view class="auction__body">
      <view class="auction__thumb">
        <image
          wx:if="{{ auctionBuyer.fansAvatar }}"
          src="{{ auctionBuyer.fansAvatar }}" />
      </view>
      <view class="auction__buyer">
        <view>
          买家：
          <text class="auction__buyer__name">{{ auctionBuyer.nickName }}</text>
          <text class="auction__buyer__tel">({{ auctionBuyer.fansTel }})</text>
        </view>
        <view wx:if="{{ auctionBuyer.auctionPrice !== undefined }}">
          抢拍价：<text class="auction__buyer__price">¥{{ auctionBuyer.auctionPrice / 100 }}</text>
        </view>
        <view>
          付款时间：{{ auctionBuyer.payAt }}
        </view>
      </view>
    </view>
  </van-cell-group>
</view>
<view class="action--multi">
  <van-button
    class="action__button"
    type="primary"
    open-type="share"
    block="{{ true }}"
  >
    炫耀一下
  </van-button>
  <van-button
    wx:if="{{ payResult.buttonGroup.DETAIL }}"
    class="action__button"
    block="{{ true }}"
    bind:click="orderDetailClick"
  >
    订单详情
  </van-button>
  <van-button
    class="action__button"
    block="{{ true }}"
    bind:click="goodsDetailClick"
  >
    中拍买家
  </van-button>
</view>
