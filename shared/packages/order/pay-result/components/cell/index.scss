$red-color: #ee0d27;
$red-color-active: #eb7784;
$cell-bg-active: #f2f3f5;
$text-color: #323233;

.cell {
  background: #fff;

  &__wrap {
    background: #f7f8fa;
    margin: 0 24px;
    padding: 4px 8px;
    border-radius: 8px;
  }

  .van-cell-group {
    border: none;
  }

  &__item {
    justify-content: space-between;
    align-items: center;
    color: $text-color;
    padding: 8px 0;
    height: 34px;
    line-height: 34px;
    background: transparent;

    &:active {
      background: $cell-bg-active;
    }

    &-left {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: flex;
      align-items: center;
    }

    &-right {
      flex: none;
    }
  }

  &__icon-out {
    display: flex;
    align-items: center;
  }

  &__icon-wrap {
    position: relative;
    display: inline-block;
    width: 16px;
    height: 16px;
    line-height: 16px;
    margin-right: 6px;
    text-align: center;
  }

  &__icon-bg {
    position: absolute;
    display: inline-block;
    width: 16px;
    height: 16px;
    top: 0;
    left: 0;
    border-radius: 2px;
    opacity: 0.2;
  }

  &__icon {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  &__blod {
    font-weight: 600;
  }

  &__btn {
    color: $red-color;
    font-size: 12px;
    padding: 0 6px;
    margin-right: -6px;
    border: none;
    background: transparent;
    outline: none;
    vertical-align: top;
    position: absolute;
    right: 4px;
    display: inline-block;
    line-height: 30px;
    height: 30px;

    &::after {
      border: none;
    }

    &:active {
      color: $red-color-active;
    }
  }

  &__num {
    font-weight: 600;
    padding: 0 4px;
  }

  &__bold {
    font-weight: 500;
    padding: 0 3px;
  }

  &__text {
    display: inline-block;
  }
}
