import WscComponent from 'shared/common/base/wsc-component';
import theme from 'shared/common/components/theme-view/theme';
import moneyHelper from '@youzan/weapp-utils/lib/money';
import asyncEvent from 'shared/utils/async-event';

const app = getApp();

WscComponent({
  properties: {
    payResult: {
      type: Object,
      observer: 'setPayResult',
    },
    beSalesmanInfo: {
      type: Object,
    },
  },

  data: {
    themeGeneral: '', // 店铺装修颜色
    cash: null,
    creditInfo: null,
    memberCard: null,
    hasAward: false,
    hasSalesmanInfo: false,
  },

  ready() {
    theme.getThemeColor('general').then((color) => {
      this.setYZData({
        themeGeneral: color,
      });
    });

    // 整理优惠信息
    const { award = {}, orderNo } = this.data.payResult;
    let cash = null; // 获得返现
    let creditInfo = null; // 获得的积分
    let memberCard = null; // 权益卡

    const { cashInfo, credit, hasMemberCard, memberCards = [] } = award;

    // 返现
    if (this.isNotNull(cashInfo)) {
      // 返现金
      cash = {};
      if (cashInfo.cash && typeof cashInfo.cash === 'number') {
        cash.money = moneyHelper(cashInfo.cash).toYuan();
        cash.url = `/packages/user/cashback/detail/index?order_no=${orderNo}`;
      }
      // 返储值金
      if (cashInfo.valueCard && typeof cashInfo.valueCard === 'number') {
        cash.valueCard = moneyHelper(cashInfo.valueCard).toYuan();
      }
      cash.needActivated = cashInfo.needActivated;
      app.logger &&
        app.logger.log({
          et: 'view',
          ei: 'show_fanxian',
          en: '返现/储值金曝光',
          si: app.getKdtId(),
        });
      if (cash.needActivated) {
        app.logger &&
          app.logger.log({
            et: 'view',
            ei: 'show_fanchuzhi_jihuo',
            en: '返储值金“立即激活”按钮曝光',
            si: app.getKdtId(),
          });
      }
    }

    // 积分
    if (this.isNotNull(credit)) {
      creditInfo = {};
      creditInfo.num = credit.credit;
      creditInfo.name = credit.creditName;
      creditInfo.url = '/packages/pointstore/point-center/index';

      app.logger &&
        app.logger.log({
          et: 'view',
          ei: 'show_fanjifen',
          en: '返积分曝光',
          si: app.getKdtId(),
        });
    }

    // 权益卡信息
    if (this.isNotNull(hasMemberCard)) {
      memberCard = {};
      memberCard.name =
        memberCards.map((item) => item.name).join('、') || '权益卡';
      memberCard.url = '/packages/card/all/index';
      memberCard.needActivated = memberCards[0] && memberCards[0].needActivated;
      app.logger &&
        app.logger.log({
          et: 'view',
          ei: 'show_quanyika',
          en: '返权益卡曝光',
          si: app.getKdtId(),
        });
      if (memberCard.needActivated) {
        app.logger &&
          app.logger.log({
            et: 'view',
            ei: 'show_quanyika_jihuo',
            en: '返权益卡“立即激活”曝光',
            si: app.getKdtId(),
          });
      }
    }

    this.setYZData({
      cash,
      creditInfo,
      memberCard,
      hasAward: cash || creditInfo || memberCard,
      hasSalesmanInfo: this.isNotNull(this.data.beSalesmanInfo),
    });
  },

  methods: {
    isNotNull(obj) {
      return !!obj && JSON.stringify(obj) !== '{}';
    },

    memberCardClick() {
      this.triggerEvent('member-card-click');
    },

    gotoUrl(event) {
      const { url, type } = event.currentTarget.dataset;
      switch (type) {
        case 'cash':
          app.logger &&
            app.logger.log({
              et: 'click',
              ei: 'click_detail_fanchuzhi',
              en: '返储值金/返现查看详情点击',
              si: app.getKdtId(),
            });
          break;
        case 'creditInfo':
          app.logger &&
            app.logger.log({
              et: 'click',
              ei: 'click_fanjifen_xiangqing',
              en: '返积分“查看详情”点击',
              si: app.getKdtId(),
            });
          break;
        case 'memberCard':
          app.logger &&
            app.logger.log({
              et: 'click',
              ei: 'click_detail_quanyika',
              en: '返权益卡查看详情点击',
              si: app.getKdtId(),
            });
          break;
        case 'salesman':
          app.logger &&
          app.logger.log({
            et: 'click',
            ei: 'click_salesmancenter',
            en: '点击成为销售员',
            pt: 'paySuccess',
            si: app.getKdtId(),
          });
          break;
        default:
          break;
      }
      if (url) {
        this.beforeGoToUrl(url, type);
      }
    },

    doActivate(event) {
      const { type } = event.currentTarget.dataset;
      if (type === 'cash') {
        app.logger &&
          app.logger.log({
            et: 'click',
            ei: 'click_fanchuzhi_jihuo',
            en: '返储值金“立即激活”点击',
            si: app.getKdtId(),
          });
      } else if (type === 'memberCard') {
        app.logger &&
          app.logger.log({
            et: 'click',
            ei: 'click_quanyika_jihuo',
            en: '返权益卡“立即激活”点击',
            si: app.getKdtId(),
          });
      }
      // if (url) {
      //   wx.navigateTo({
      //     url
      //   });
      // }
    },

    beforeGoToUrl(url, type) {
      asyncEvent.triggerAsync
      .apply(getApp(), [
        'beforeGoToUrl', 
        {
          type,
        }
      ])
      .then(() => {
        wx.navigateTo({
          url,
        });
      })
      .catch(() => {});
    },
  },
});
