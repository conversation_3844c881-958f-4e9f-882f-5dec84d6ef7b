<view class="cell">
  <view wx:if="{{ hasAward || hasSalesmanInfo }}" class="cell__wrap">
    <van-cell
      wx:if="{{ hasSalesmanInfo }}"
      border="{{ false }}"
      is-link="{{ true }}"
      title-class="cell__item-left"
      value-class="cell__item-right"
      custom-class="cell__item"
      data-url="{{ beSalesmanInfo.url }}"
      data-type="salesman"
      bind:tap="gotoUrl"
    >
      <view class="cell__icon-out" slot="title">
        <view class="cell__icon-wrap">
          <text class="cell__icon-bg" style="background: {{ themeGeneral }}" />
          <text class="cell__icon" style="color: {{ themeGeneral }}">佣</text>
        </view>
        <view class="cell__text">
          <yz-rich-text html="{{ beSalesmanInfo.content }}"></yz-rich-text>
        </view>
      </view>
    </van-cell>
    <van-cell
      wx:if="{{ cash }}"
      border="{{ false }}"
      is-link="{{ !cash.needActivated }}"
      title-class="cell__item-left"
      value-class="cell__item-right"
      custom-class="cell__item"
      data-url="{{ cash.url }}"
      data-type="cash"
      bind:tap="gotoUrl"
    >
      <view class="cell__icon-out" slot="title">
        <view class="cell__icon-wrap">
          <text class="cell__icon-bg" style="background: {{ themeGeneral }}" />
          <text class="cell__icon" style="color: {{ themeGeneral }}">返</text>
        </view>
        <view class="cell__text">
          恭喜获得返现
          <text wx:if="{{ cash.money > 0 }}" class="cell__num" decode="{{ true }}">
            {{ cash.money }}&nbsp;元
          </text>
          <text wx:if="{{ cash.money > 0 && cash.valueCard > 0 }}">以及</text>
          <text wx:if="{{ cash.valueCard > 0 }}" class="cell__num" decode="{{ true }}">
            {{ cash.valueCard }}&nbsp;元储值金
          </text>
        </view>
        <text
          wx:if="{{ cash.needActivated }}"
          class="cell__btn"
          style="color: {{ themeGeneral }}"
          data-url="{{ cash.url }}"
          data-type="cash"
          bindtap="doActivate"
        >
          立即激活
        </text>
      </view>
    </van-cell>
    <van-cell
      wx:if="{{ creditInfo }}"
      border="{{ false }}"
      is-link="{{ true }}"
      title-class="cell__item-left"
      value-class="cell__item-right"
      custom-class="cell__item"
      data-url="{{ creditInfo.url }}"
      data-type="creditInfo"
      bind:tap="gotoUrl"
    >
      <view class="cell__icon-out" slot="title">
        <view class="cell__icon-wrap">
          <text class="cell__icon-bg" style="background: {{ themeGeneral }}" />
          <text class="cell__icon" style="color: {{ themeGeneral }}">积</text>
        </view>
        <text>
          恭喜获得
          <text class="cell__num" decode="{{ true }}">
            {{ creditInfo.num }}&nbsp;{{ creditInfo.name || '积分' }}
          </text>
        </text>
      </view>
    </van-cell>
    <van-cell
      wx:if="{{ memberCard }}"
      border="{{ false }}"
      is-link="{{ !memberCard.needActivated }}"
      title-class="cell__item-left"
      value-class="cell__item-right"
      custom-class="cell__item"
      data-url="{{ memberCard.url }}"
      data-type="memberCard"
      bind:tap="gotoUrl"
    >
      <view class="cell__icon-out" slot="title">
        <view class="cell__icon-wrap">
          <text class="cell__icon-bg" style="background: {{ themeGeneral }}" />
          <text class="cell__icon" style="color: {{ themeGeneral }}">卡</text>
        </view>
        <text>
          恭喜获得
          <text class="cell__blod" decode="{{ true }}">
            &nbsp;{{ memberCard.name }}
          </text>
        </text>
        <text
          wx:if="{{ memberCard.needActivated }}"
          class="cell__btn"
          style="color: {{ themeGeneral }}"
          data-url="{{ memberCard.url }}"
          data-type="memberCard"
          bindtap="doActivate"
        >
          立即激活
        </text>
      </view>
    </van-cell>
  </view>
</view>
