import WscComponent from 'shared/common/base/wsc-component';
import getApp from 'shared/utils/get-safe-app';
import theme from 'shared/common/components/theme-view/theme';
import getSystemInfo from 'shared/utils/browser/system-info';
import compareVersion from 'shared/utils/compare-version';

const app = getApp();

WscComponent({
  properties: {
    templateIds: {
      type: Array
    },

    hasSubscribe: Boolean
  },

  data: {
    themeGeneral: '', // 店铺装修颜色
    showTips: false // 是否展示gif引导
  },

  ready() {
    theme.getThemeColor('general').then((color) => {
      this.setYZData({
        themeGeneral: color
      });
    });
  },

  methods: {
    clickSubscription() {
      let { templateIds = [] } = this.data;
      if (this.data.hasSubscribe) return false; // 已经订阅不在重复执行
      // iOS客户端7.0.6版本、Android客户端7.0.7版本之后的一次性订阅/长期订阅才支持多个模板消息
      if (templateIds instanceof Array && templateIds.length >= 2) {
        const { system, version } = getSystemInfo();
        const androidSuportedMuti =
          system.indexOf('Android') > -1 && compareVersion(version, '7.0.6') >= 0;
        const iosSuportedMuti = system.indexOf('IOS') > -1 && compareVersion(version, '7.0.5') >= 0;
        if (!androidSuportedMuti || !iosSuportedMuti) {
          templateIds = new Array(templateIds[0]);
        }
      }
      if (
        templateIds instanceof Array &&
        templateIds.length > 0 &&
        wx.canIUse('requestSubscribeMessage')
      ) {
        // 延时打开弹窗
        this.timer = setTimeout(() => {
          this.setYZData({ showTips: true });
        }, 300);
        wx.requestSubscribeMessage({
          tmplIds: templateIds,
          success: (res) => {
            if (JSON.stringify(res).indexOf('accept') > -1) {
              wx.showToast({ title: '订阅通知成功', icon: 'success' });
              this.triggerEvent('success');
            } else if (JSON.stringify(res).indexOf('reject') > -1) {
              wx.showToast({ title: '请在小程序设置中允许订阅消息', icon: 'none' });
              app.logger.requestError({
                name: 'wxSubscribeError',
                message: '请在小程序设置中允许订阅消息',
                alert: 'info',
                detail: res
              });
            }
          },
          fail: (err) => {
            let errorMsg = '订阅微信通知失败';
            if (err.errMsg && err.errMsg.indexOf('switched off') > -1) {
              errorMsg = '请在小程序设置中允许订阅消息';
            }
            wx.showToast({ title: errorMsg, icon: 'none' });
            app.logger.requestError({
              name: 'wxSubscribeError',
              message: errorMsg,
              alert: 'info',
              detail: err
            });
          },
          complete: () => {
            // 关闭动画指引
            if (this.timer) clearTimeout(this.timer);
            this.setYZData({ showTips: false });
            this.triggerEvent('complete');
          }
        });
      } else {
        wx.showToast({ title: '订阅微信通知失败', icon: 'none' });
        app.logger.requestError({
          name: 'wxSubscribeError',
          message: '订阅微信通知失败',
          alert: 'info',
          detail: new Error('当前订单或者小程序基础库不支持订阅')
        });
      }

      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'click_wx_subscription',
          en: '点击订阅消息',
          si: app.getKdtId()
        });
    },

    closeTips() {
      this.setYZData({ showTips: false });
    }
  }
});
