import WscComponent from 'shared/common/base/wsc-component';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();

WscComponent({
  properties: {
    coupon: {
      type: Object,
      observer: 'couponObserver'
    }
  },

  methods: {
    couponObserver(coupon = {}) {
      if (coupon.quantity > 0) {
        app.logger && app.logger.log({
          et: 'view',
          ei: 'show_liebianquan',
          en: '裂变券曝光',
          si: app.getKdtId()
        });
      }
    },

    clickFissionCoupon() {
      app.logger && app.logger.log({
        et: 'click',
        ei: 'click_liebianquan',
        en: '裂变券点击',
        si: app.getKdtId()
      });
    }
  }
});
