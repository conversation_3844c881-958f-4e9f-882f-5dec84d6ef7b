.selffetch {
  background-color: #fff;
  font-size: 14px;
}

.card {
  border: 1px solid #e5e5e5;
  background-color: #f7f8fa;
  margin: 0 10px;
  position: relative;
}
.title {
  line-height: 45px;
  padding: 0 10px 0 25px;
  margin: 0 10px;
  border-bottom: 1px dashed #e5e5e5;
  background: url('https://img01.yzcdn.cn/v2/image/wap/cashier/<EMAIL>') no-repeat
    left center;
  background-size: 15px 15px;
}

.code {
  margin: 15px;
  padding: 10px 20px;
  line-height: 0;
  background-color: #fff;
  text-align: center;

  .bar {
    max-width: 100%;
    height: 60px;
    margin-bottom: 30px;
  }

  .qr {
    margin-top: 30px;
    width: 140px;
    height: 140px;
  }

  .text {
    color: #666;
    font-size: 18px;
    line-height: 1;
  }

  &--waiting {
    position: relative;
    height: 255px;
    background: url('//img01.yzcdn.cn/public_files/2018/12/03/447b85788ff5d3c111d78bee926daef9.png')
      center no-repeat;
    background-size: contain;
  }
}

.loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  line-height: 255px;

  .van-loading {
    display: inline-block;
    margin-right: 5px;
  }
}

.tip {
  position: absolute;
  right: 5px;
  top: -10px;
  line-height: 36px;
  background-color: #262728;
  opacity: 0.8;
  border-radius: 8px;
  width: 150px;
  text-align: center;
  color: #fff;
  font-size: 14px;

  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 9px solid transparent;
    border-top-color: #262728;
    bottom: -18px;
    right: 15px;
  }
}

.address,
.goods {
  padding: 10px;
  color: #333;
  line-height: 2;
}

.address {
  border: dashed #e5e5e5;
  border-width: 1px 0;
}

.icon {
  display: inline-block;
  vertical-align: middle;
  margin-right: 3px;
}

.left-circle,
.right-circle {
  background-color: #e5e5e5;
  width: 6px;
  height: 12px;
  position: absolute;
  top: 40px;

  &::before {
    content: '';
    position: absolute;
    width: 5px;
    height: 10px;
    top: 1px;
    background-color: #fff;
  }
}

.left-circle {
  left: -1px;
  border-radius: 0 12px 12px 0;

  &::before {
    left: 0;
    border-radius: 0 10px 10px 0;
  }
}

.right-circle {
  right: -1px;
  border-radius: 12px 0 0 12px;

  &::before {
    right: 0;
    border-radius: 10px 0 0 10px;
  }
}
