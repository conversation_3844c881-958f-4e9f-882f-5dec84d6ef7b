<!-- 裂变券弹框 -->
<van-popup
  show="{{ coupon.show }}"
  custom-class="coupon-popup"
>
  <form-view
    bindtap="onClose"
    class="close"
  >
  </form-view>
  <view class="main-container">
    <text class="coupon">拼手气优惠券</text>
    <view class="count">
      <view class="count-number">{{coupon.quantity}}</view>
      <view class="count-page">张</view>
    </view>
  </view>
  <view class="tip">恭喜你获得 {{coupon.quantity}} 张优惠券</view>
  <form-view class="btn-container">
    <button
      class="share-btn"
      data-type="fission"
      open-type="share"
    >
      转发给好友
    </button>
  </form-view>
</van-popup>
