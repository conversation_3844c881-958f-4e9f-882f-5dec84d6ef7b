.coupon-popup {
  width: 580rpx;
  height: 680rpx;
  background-color: transparent !important;
  background-image: url(https://img01.yzcdn.cn/public_files/2018/06/05/7e85fed2311c0610fc2054cb42372f1e.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow-y: visible !important;
}

// 增加层级覆盖van-button样式
.share-btn {
  display: block;
  height: 40px;
  line-height: 40px;
  border-radius: 2px;
  overflow: hidden;
  border: none;
  background-color: #ffc55f;
  color: #f83c2d;
  font-size: 16px;
}

.share-btn::after {
  border: 0;
}

.close {
  position: absolute;
  right: 0;
  top: -64px;
  width: 64rpx;
  height: 64rpx;
  font-size: 32px;
  color: #999;
  background-image: url(https://img01.yzcdn.cn/public_files/2018/05/16/c29879b23772965a3af7f5689ee61f58.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.btn-container {
  position: absolute;
  bottom: 60rpx;
  left: 60rpx;
  right: 60rpx;
}

.main-container {
  display: flex;
  flex-flow: column;
  align-items: center;
}

.coupon {
  margin: 35px 0 5px 0;
  font-size: 14px;
  color: #f83c2d;
}

.count {
  vertical-align: middle;
}

.count-number {
  display: inline;
  margin-right: 5px;
  font-size: 54px;
  font-weight: bold;
  color: #f83c2d;
}

.count-page {
  position: relative;
  top: -2px;
  display: inline;
  font-size: 24px;
  color: #f83c2d;
}

.tip {
  position: absolute;
  top: 465rpx;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 14px;
  color: #fff;
}
