import WscComponent from 'shared/common/base/wsc-component';
import theme from 'shared/common/components/theme-view/theme';
import getApp from 'shared/utils/get-safe-app';

/**
 * 支付成功页面订单按钮逻辑梳理∂
 * 1. 普通订单 -> 订阅物流(标红)、再次购买、分享、订单详情
 * 2. 自提订单 -> 查看提货码(标红)、再次购买、分享、订单详情
 * 3. 电子卡券订单 -> 查看券码(标红)、再次购买、分享、订单详情
 * 4. app精选 -> 订单详情(标红)、再次购买、分享
 * 5. 再次购买、分享、订单详情这些按钮保持原来的逻辑不变
 */
const BUTTON_GROUP = [
  'SUBSCRIPTION', // 订阅物流
  'PICK_UP_CODE', // 查看提货码
  'CARD_VOUCHER', // 查看券码
  // 'BUY_AGAIN', // 再次购买
  // 'GIVE_AWAY', // 立即赠送
  'DETAIL', // 查看订单
  'GROUPBUY', // 社区团购再次购买
  'SHARE' // 分享一下
];
const app = getApp();
const SAVE_TAG = `pay_success_dialog_click_${app.getKdtId()}`;
const BUTTON_CLASS = {
  primary: 'action-block__btn-primary',
  default: 'action-block__btn-default'
};

WscComponent({
  properties: {
    payResult: {
      type: Object,
      observer: 'computedPayResult',
    },
    hidePickUpCodeBtn: {
      type: Boolean,
      observer() {
        this.computedPayResult(this.data.payResult);
      },
    },
  },

  data: {
    buttons: [], // 按钮列表
    themeGeneral: '', // 店铺装修颜色
  },

  ready() {
    theme.getThemeColor('general').then((color) => {
      this.setYZData({
        themeGeneral: color
      });
    });
    /**
     * 自动打开弹窗逻辑
     * 1. 订阅弹窗如果点击了我知道则不再自动弹出
     * 2. 其他的自提码、电子卡券弹窗每次都弹出
     */
    const clickRecord = wx.getStorageSync(SAVE_TAG);
    const firstButtonType = this.data.buttons[0].type;
    if (
      ['PICK_UP_CODE', 'CARD_VOUCHER'].indexOf(firstButtonType) > -1 ||
      (firstButtonType === 'SUBSCRIPTION' && !clickRecord)
    ) {
      setTimeout(() => {
        this.triggerEvent('click', { type: firstButtonType, loading: false, first: true });
      }, 500);
    }
  },

  methods: {
    computedPayResult(payResult) {
      // 计算按钮的type和icon值
      const buttonGroup = [];
      const buttons = this.getButtons(payResult);
      if (this.data.hidePickUpCodeBtn) {
        delete buttons.PICK_UP_CODE;
      }
      const showButtons = Object.keys(buttons).filter((key) => BUTTON_GROUP.indexOf(key) > -1);
      // 是否社区团购订单
      showButtons.forEach((key) => {
        buttons[key].className = this.getButtonClass(showButtons, key);
        buttons[key].block = buttons[key].className === BUTTON_CLASS.primary;
        buttonGroup.push(buttons[key]);
      });
      buttonGroup.sort((item1, item2) => {
        return BUTTON_GROUP.indexOf(item1.type) - BUTTON_GROUP.indexOf(item2.type);
      });
      this.setYZData({ buttons: buttonGroup });
      // 按钮曝光统计
      const buttonNames = Object.keys(buttons);
      if (buttonNames.indexOf('SUBSCRIPTION')) {
        app.logger &&
          app.logger.log({
            et: 'view',
            ei: 'show_button_wuliu',
            en: '“订阅物流”按钮曝光',
            si: app.getKdtId()
          });
      }
    },

    getButtons(payResult) {
      const { buttonGroup = {}, isGroupBuy } = payResult;
      // 开出process防止删除payResult里面的PICK_UP_CODE值来支持隐藏提货码按钮，所以重新定义buttons
      const buttons = { ...buttonGroup };
      // 社区团购订单目前buttonGroup做特殊处理
      if (isGroupBuy) {
        return {
          DETAIL: {
            defaultText: '查看详情',
            needJump: true,
            type: 'DETAIL',
          },
          GROUPBUY: {
            defaultText: '继续团购',
            needJump: true,
            type: 'GROUPBUY',
          },
          SHARE: {
            defaultText: '分享一下',
            needJump: true,
            type: 'SHARE',
          },
          PICK_UP_CODE: {
            defaultText: '查看提货码',
            needJump: true,
            type: 'PICK_UP_CODE',
          },
        };
      }
      return buttons;
    },

    // 获取按钮图标名称
    getButtonClass(buttons, type) {
      const firstBtn = buttons[0];
      const mianButtonArr = ['CARD_VOUCHER', 'GIVE_AWAY', 'SUBSCRIPTION', 'PICK_UP_CODE'];
      const isMainBtn = mianButtonArr.indexOf(type) !== -1;
      const isFirstBtn = firstBtn && firstBtn.type === type;
      const onlyOneBtn = buttons.length === 1;
      const hasMainBtn = mianButtonArr.some((btn) => buttons.indexOf(btn) > -1);
      const isPrimary = isMainBtn || isFirstBtn || onlyOneBtn || (type === 'DETAIL' && !hasMainBtn);
      return isPrimary ? BUTTON_CLASS.primary : BUTTON_CLASS.default;
    },

    actionHandler({
      currentTarget: {
        dataset: { type }
      }
    }) {
      switch (type) {
        case 'SUBSCRIPTION':
          app.logger &&
            app.logger.log({
              et: 'click',
              ei: 'click_button_wuliu',
              en: '“订阅物流”按钮点击',
              si: app.getKdtId()
            });
          break;
        case 'DETAIL':
          app.logger &&
            app.logger.log({
              et: 'click',
              ei: 'click_button_chakandingdan',
              en: '“查看订单”按钮点击',
              si: app.getKdtId()
            });
          break;
        case 'GROUPBUY':
          app.logger &&
            app.logger.log({
              et: 'click',
              ei: 'groupbuy_continue',
              en: '继续团购',
              si: app.getKdtId()
            });
          this.triggerEvent('groupbuyContinue');
          break;
        default:
          break;
      }
      this.triggerEvent('click', { type, loading: true });
    }
  }
});
