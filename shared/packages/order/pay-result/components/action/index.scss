@import '../../common/common.scss';

.action-block {
  padding: 24px 20px 16px;
  background: #fff;

  &--normal {
    text-align: center;
  }

  &--mutli {
    display: block;

    > .van-button {
      width: 100%;

      & + .van-button {
        margin-top: 10px;
      }
    }
  }

  &__btn-wrap {
    position: relative;

    &:last-child .action-block__btn-split {
      display: none;
    }
  }

  &__btn-primary,
  &__btn-default {
    display: inline-block;
    height: 36px;
    line-height: 36px;
    border-radius: 18px;
    white-space: nowrap;
    font-size: 14px;
    border: none;
    overflow: hidden;
    position: relative;
  }

  &__btn-primary {
    position: relative;
    width: 118px;
    text-align: center;
    padding-left: 0;
    padding-right: 0;
    background: none;
    display: block;
    margin: 0 auto 18px;
    border-radius: 18px;
  }

  &__btn-default {
    background: #fff;

    &::before {
      background: none;
    }

    &:last-child .action-block__btn-split {
      display: none;
    }
  }

  &__btn-split {
    display: inline-block;
    width: 1px;
    height: 16px;
    background: #dcdee0;
    vertical-align: middle;
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -8px;
  }

  &__btn-icon {
    vertical-align: middle;
    margin-top: -2px;
    margin-right: 4px;
    font-size: 16px;
  }

  &__btn-bg {
    position: absolute;
    width: 118px;
    height: 36px;
    border-radius: 18px;
    opacity: 0.1;
    top: 0;
    left: 50%;
    margin-left: -59px;
    z-index: 0;
  }
}
