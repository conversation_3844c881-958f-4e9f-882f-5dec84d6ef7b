<view
  class="action-block action-block--normal"
>
  <view
    wx:for="{{ buttons }}"
    wx:key="{{ item.type }}-{{ index }}"
    wx:item="item"
    wx:for-index="index"
    class="action-block__btn-wrap"
    style="display: {{ item.block ? 'block' : 'inline-block' }}"
  >
    <van-button
      block="{{ item.block }}"
      type="{{ item.type }}"
      data-type="{{ item.type }}"
      custom-class="{{ item.className }}"
      custom-style="border: 1px solid {{ item.block ? themeGeneral : '#fff' }}"
      bind:click="actionHandler"
    >
      <text style="color: {{ themeGeneral }}">{{ item.defaultText }}</text>
    </van-button>
    <view wx:if="{{ !item.block }}" class="action-block__btn-split"></view>
  </view>
</view>
