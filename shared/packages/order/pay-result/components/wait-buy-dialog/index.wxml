<van-dialog
  class="wait-buy"
  use-slot
  title="你还有以下商品待结算"
  show="{{ show }}"
  show-cancel-button="{{ true }}"
  confirm-button-text="继续结算"
  bind:confirm="onConfirm"
>
  <view class="content">
    <view
      wx:for="{{ util.slice(goodsList, 0, 3) }}"
      wx:for-item="item"
      wx:key="index"
      class="img"
    >
      <image src="{{ item.imgUrl }}" mode="aspectFit" />
    </view>
    <text wx:if="{{ goodsList.length > 3 }}" class="ellipsis">...</text>
    <view class="count">等{{ goodsList.length }}件</view>
  </view>
</van-dialog>

<wxs module="util">
  module.exports.slice = function(list, start, end) {
    return list.slice(start, end);
  }
</wxs>
