<van-popup
  show="{{ visable }}"
  custom-style="border-radius: 20px 20px 0 0; text-align: center;"
  position="bottom"
  close-on-click-overlay="{{ ture }}"
  bind:close="onClose"
  bind:click-overlay="onClose"
>
  <view class="self-fetch-dialog__title">提货码</view>
  <!-- 自提码错误提示 -->
  <block wx:if="{{ data.hasError }}">
    <view class="self-fetch-dialog__error">
      <image class="self-fetch-dialog__error-img" src="https://img01.yzcdn.cn/upload_files/2020/03/12/FoFlIZNte-bq-nCcHIiZHDdvdwNa.png" alt="自提码生成失败" />
      <view class="self-fetch-dialog__error-text">提货码生成失败，请稍后再试或前往订单详情查看</view>
    </view>
    <view class="self-fetch-dialog__buttons">
      <theme-view
        custom-class="self-fetch-dialog__btn-primary"
        color="main-text"
        bg="general"
        bindtap="onClose"
      >
        稍后再试
      </theme-view>
    </view>
  </block>
  <block wx:else>
    <block wx:if="{{ data.loading }}">
      <view class="self-fetch-dialog__loading">
        <van-loading class="self-fetch-dialog__loading-icon" />
        <view class="self-fetch-dialog__loading-text">正在生成提货码...</view>
      </view>
    </block>
    <block wx:else>
      <view class="self-fetch-dialog__address">
        <view>提货地址：{{ data.province + data.city + data.county + data.addressDetail }}</view>
        <view>提货时间：{{ data.userTime }}</view>
      </view>
      <image class="self-fetch-dialog__qrcode" src="{{ data.selfFetchQRCode }}" alt="二维码" />
      <view class="self-fetch-dialog__qrnum">
        {{ qrNum }}
      </view>
    </block>
    <view class="self-fetch-dialog__buttons">
      <van-button
        wx:if="{{ data.loading }}"
        custom-class="self-fetch-dialog__btn-disable"
        bindtap="onClose"
      >
        保存到相册
      </van-button>
      <theme-view
        wx:else
        custom-class="self-fetch-dialog__btn-primary"
        color="main-text"
        bg="general"
        gradient
        gradientDeg="90"
        bindtap="saveToLocal"
      >
        保存到相册
      </theme-view>
      <van-button
        wx:if="{{ !data.loading }}"
        custom-class="self-fetch-dialog__btn-cancel"
        bindtap="onClose"
      >
        我知道了
      </van-button>
    </view>
  </block>
  
</van-popup>
