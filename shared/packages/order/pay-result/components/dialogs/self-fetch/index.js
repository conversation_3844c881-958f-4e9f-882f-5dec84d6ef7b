import WscComponent from 'shared/common/base/wsc-component';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();

WscComponent({
  properties: {
    // 是否显示
    visable: {
      type: Boolean,
      value: false
    },

    data: {
      type: Object,
      observer: 'computedData'
    }
  },

  data: {
    qrNum: ''
  },

  methods: {
    onClose() {
      this.triggerEvent('close');
    },

    computedData(data) {
      const { selfFetchNo } = data;
      this.setYZData({
        qrNum: selfFetchNo ? selfFetchNo.replace(/(\d{4})(\d{4})(\d*)/g, '$1 $2 $3') : ''
      });
    },

    saveToLocal() {
      wx.showLoading({
        title: '生成海报中'
      });
      this.getPoster(true);
    },

    // 下载并存储海报
    getPoster(needRetry) {
      const fail = () => {
        wx.showToast({ title: '生成海报失败', icon: '' });
      };
      const {
        province,
        city,
        county,
        addressDetail,
        userTime,
        selfFetchQRCode,
        selfFetchNo,
        items
      } = this.data.data;
      app
        .request({
          method: 'POST',
          path: '/wsctrade/poster/self-fetch.json',
          data: {
            fetchAddress: province + city + county + addressDetail, // 自提地址
            fetchTime: userTime, // 自提时间
            items, // 商品列表
            qrcode: selfFetchQRCode, // 提货二维码
            number: selfFetchNo.replace(/(\d{4})(\d{4})(\d*)/g, '$1 $2 $3') // 提货码
          }
        })
        .then(({ value }) => {
          wx.hideLoading();
          if (value) {
            app.downloadFile({
              url: value,
              success: ({ statusCode, tempFilePath }) => {
                if (statusCode === 200) {
                  wx.saveImageToPhotosAlbum({
                    filePath: tempFilePath,
                    success() {
                      wx.showToast({ title: '保存成功' });
                    },
                    fail() {
                      wx.showToast({ title: '保存成功' });
                    }
                  });
                } else {
                  fail();
                }
              },
              fail
            });
          } else {
            fail();
          }
        })
        .catch(() => {
          if (needRetry) {
            setTimeout(() => {
              this.getPoster(false);
            }, 1000);
          } else {
            wx.hideLoading();
            fail();
          }
        });
    }
  }
});
