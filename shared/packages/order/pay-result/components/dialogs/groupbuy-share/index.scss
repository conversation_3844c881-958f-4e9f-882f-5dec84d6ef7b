$text-color: #323233;
$border-color: #e5e5e5;
$text-num-color: #333;
$title: #5e290f;
$btn-color: #07c160;

.groupbuy-share-dialog {
  border-radius: 20px 20px 0 0;
  text-align: center;
  background-position: top center;
  background-repeat: repeat-x;
  height: 451px;
  background-size: 375px 90px;
  background-image: url('https://img01.yzcdn.cn/upload_files/2020/10/13/FsILDjWwugz8_q_CQCmJOisR9OgS.png');

  .inline {
    display: inline;
  }

  &__title {
    padding: 33px 0 12px;
    font-size: 20px;
    line-height: 28px;
    font-weight: bold;
    color: $title;
  }

  &__condition {
    font-size: 14px;
    line-height: 20px;
    color: $text-color;

    &-num {
      color: $btn-color;
      margin: 0 4px;
    }
  }

  &__count-down {
    color: $btn-color;
    display: inline-block;
    min-width: 128px;
    text-align: left;
    margin-left: 4px;
  }

  &__card {
    background-image: url('https://img01.yzcdn.cn/upload_files/2020/10/13/FjYGXpi3Dv3paKYf-XpMGIBHhBIo.png');
    background-size: 306px 264px;
    background-repeat: no-repeat;
    width: 306px;
    height: 264px;
    margin: 20px auto 0;
    padding: 24px 19px 24px 24px;
    box-sizing: border-box;
  }

  &__buyer {
    display: flex;
    align-items: center;

    &-avatar {
      width: 28px;
      height: 28px;
      border-radius: 14px;
      margin-right: 8px;
    }

    &-name {
      font-size: 16px;
      font-weight: bold;
      color: $text-color;
    }
  }

  &__text {
    margin: 8px 0 0 36px;
    font-size: 14px;
    line-height: 24px;
    text-align: left;

    &-num {
      color: $btn-color;
      margin: 0 2px;
    }
  }

  &__goods {
    margin: 16px -11px 0 36px;
    overflow: hidden;
    text-align: left;
    white-space: nowrap;

    &-item {
      display: inline-block;
      width: 90px;
      height: 90px;
      border-radius: 4px;
      overflow: hidden;
      margin-right: 12px;
      position: relative;

      .goods-picture {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        width: 100%;
        height: 100%;
      }

      .goods-price {
        position: absolute;
        width: 100%;
        bottom: 0;
        text-align: center;
        font-size: 14px;
        font-weight: bold;
        line-height: 24px;
        color: #fff;
        background: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0.16),
          rgba(0, 0, 0, 0.48)
        );
      }
    }
  }

  &__buttons {
    margin: 0 35px 30px;
  }

  &__btn {
    width: 100% !important;
    border-radius: 22px !important;
    border: none !important;
    font-size: 16px !important;
    background: $btn-color !important;
    color: #fff !important;
    height: 44px !important;
    line-height: 44px !important;
  }

  &__error {
    padding: 100px 30px 0 30px;
    height: 377px;
    box-sizing: border-box;

    &-img {
      width: 160px;
      height: 160px;
      margin-bottom: 12px;
    }

    &-text {
      color: #969799;
      font-size: 14px;
      line-height: 20px;
    }
  }

  &__loading {
    padding: 165px 24px 0 24px;
    height: 377px;
    box-sizing: border-box;

    &-icon {
      width: 35px;
      height: 35px;
      display: block;
      margin: 0 auto;
    }

    &-text {
      color: #969799;
      font-size: 14px;
      line-height: 20px;
      margin-top: 72px;
    }
  }
}

@media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
  .groupbuy-share-dialog {
    padding-bottom: 34px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}
