import WscComponent from 'shared/common/base/wsc-component';
import getApp from 'shared/utils/get-safe-app';
import get from '@youzan/weapp-utils/lib/get';
import fullfillImage from '@youzan/weapp-utils/lib/cdn-image';

const app = getApp();

const TYPES = {
  CUSTOMER: 1,
  ORDERPAY: 2,
  ORDERNUM: 3,
  SALES: 4,
  REACH: 0,
};

const UNITS = {
  1: '人',
  2: '元',
  3: '笔订单',
  4: '件商品',
};

function format(value, cent = true) {
  value = parseFloat(value, 10);
  if (cent) {
    value /= 100;
  }
  value = value.toFixed(2);
  return value;
}

WscComponent({
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      default: false,
      observer: 'toggleDialog',
    },
    orderNo: {
      type: String,
      value: '',
    },
  },
  data: {
    items: [],
    activity: {
      lowestCustomerNum: 0,
      lowestOrderNum: 0,
      lowestOrderPay: 0,
      lowestSalesNum: 0,
      customerNum: 0,
      orderNum: 0,
      orderPay: 0,
      salesNum: 0,
    },
    customerNickname: '',
    customerAvatar: '',
    hasError: false,
    loading: false,
    countdown: 0,
    shareData: {
      title: '',
      path: '',
      imageUrl:
        'https://img01.yzcdn.cn/upload_files/2020/09/29/FouW4WjRy9UVm1p-m6VG_d3kCWtW.png',
    },
  },
  ready() {
    this.queryShare();
  },

  methods: {
    toggleDialog(newValue) {
      if (newValue && !this.data.items.length) {
        this.queryShare();
        app.logger &&
          app.logger.log({
            et: 'click', // 事件类型
            ei: 'groupbuy_share', // 事件标识
            en: '分享一下', // 事件名称
            pt: 'paySuccess', // 页面类型
            params: {
              share_uid: app.getBuyerId(),
            }, // 事件参数
          });
      }
    },
    queryShare() {
      if (this.data.loading || this.data.items.length) return;
      this.setYZData({
        loading: true,
        hasError: false,
      });
      app
        .request({
          path: '/wscindustry/groupbuying/poster/getOrderShare.json',
          data: { orderNo: this.data.orderNo },
          type: 'GET',
        })
        .then((res) => {
          const {
            joinActivityShareBO,
            headerName,
            customerNickname,
            customerMobile,
            customerAvatar,
            items,
            headerBuyerId,
            activityId,
          } = res;
          const {
            customerNum,
            orderNum,
            orderPay,
            salesNum,
            lowestCustomerNum,
            lowestOrderNum,
            lowestOrderPay,
            lowestSalesNum,
          } = joinActivityShareBO;
          const activity = {
            ...joinActivityShareBO,
            customerNum: this.getDefaultVal(customerNum, lowestCustomerNum),
            orderNum: this.getDefaultVal(orderNum, lowestOrderNum),
            orderPay: this.getDefaultVal(orderPay, lowestOrderPay),
            salesNum: this.getDefaultVal(salesNum, lowestSalesNum),
          };
          const alias = get(joinActivityShareBO, 'activityAlias');
          const formatItems = items.map((item) => {
            return {
              title: item.title, // 商品标题
              num: item.num, // 商品数量
              markPrice: item.markPrice,
              price: item.price,
              salesPrice: format(item.price, true),
              imgUrl: get(item, 'picture[0].url'), // 商品图片地址
            };
          });
          this.setYZData({
            headerName,
            customerAvatar:
              !customerAvatar ||
              /\/upload_files\/avatar.png/.test(customerAvatar)
                ? 'https://img01.yzcdn.cn/upload_files/2020/10/21/FrY8c3csodafnBSzhvk9TozB7NIA.png'
                : customerAvatar,
            customerNickname: customerNickname || customerMobile,
            countdown: Math.max(
              get(joinActivityShareBO, 'activityEndTime') -
                new Date().getTime(),
              0
            ),
            items: formatItems,
            loading: false,
          });
          this.calSalesGoal(
            activity,
            items,
            headerName,
            headerBuyerId,
            activityId,
            alias
          );
          this.getSharePic(formatItems);
          this.triggerEvent('setBuyAgainPath', `/packages/groupbuying/buyer-trade/buying/index?headerBuyerId=${headerBuyerId}&alias=${alias}&activityId=${activityId}`);
        })
        .catch(() => {
          this.setYZData({
            hasError: true,
            loading: false,
          });
        });
    },
    getDefaultVal(val, defaultVal) {
      return typeof val === 'undefined' ? defaultVal : val;
    },
    calSalesGoal(
      activity,
      items,
      headerName,
      headerBuyerId,
      activityId,
      alias
    ) {
      const type = this.calType(activity);
      const goalNum = this.calGoalNum(activity, type);
      const formatNum = this.calFormatNum(type, goalNum);
      const goodsNum = this.calGoodsNum(items);
      const savedMoney = this.calSavedMoney(items);
      const formatSavedMoney = format(savedMoney, false);
      const userId = app.getBuyerId();
      const shareData = {
        ...this.data.shareData,
        title: `我在团长【${headerName}】处团购了${goodsNum}件商品，${
          savedMoney ? '省了' + formatSavedMoney + '元，' : ''
        }快来参团吧`,
        path: `/packages/groupbuying/buyer-trade/buying/index?headerBuyerId=${headerBuyerId}&alias=${alias}&activityId=${activityId}&shareUid=${userId}`,
      };

      this.setYZData({
        type,
        goalNum,
        showGoalNum: type && goalNum,
        unit: UNITS[type],
        formatNum,
        goodsNum,
        savedMoney,
        formatSavedMoney,
        shareData,
      });
    },
    calType(activity) {
      const {
        lowestCustomerNum,
        lowestOrderNum,
        lowestOrderPay,
        lowestSalesNum,
      } = activity;
      const index =
        [
          lowestCustomerNum,
          lowestOrderPay,
          lowestOrderNum,
          lowestSalesNum,
        ].findIndex((item) => item) + 1;
      return index;
    },
    calGoalNum(activity, type) {
      const {
        lowestCustomerNum,
        lowestOrderNum,
        lowestOrderPay,
        lowestSalesNum,
        customerNum,
        orderNum,
        orderPay,
        salesNum,
      } = activity;
      switch (type) {
        case TYPES.CUSTOMER:
          return Math.max(lowestCustomerNum - customerNum, 0);
        case TYPES.ORDERPAY:
          return Math.max(lowestOrderPay - orderPay, 0);
        case TYPES.ORDERNUM:
          return Math.max(lowestOrderNum - orderNum, 0);
        case TYPES.SALES:
          return Math.max(lowestSalesNum - salesNum, 0);
        default:
          return 0;
      }
    },
    calFormatNum(type, goalNum) {
      if (type === TYPES.ORDERPAY) {
        return format(goalNum, true);
      }
      return goalNum;
    },
    calGoodsNum(items) {
      return items.reduce((prev, { num }) => {
        return prev + num;
      }, 0);
    },
    calSavedMoney(items) {
      return items.reduce((prev, { num, price, markPrice }) => {
        return (markPrice ? +markPrice - price / 100 : 0) * num + prev;
      }, 0);
    },
    close() {
      this.triggerEvent('close');
    },
    updateShareData(shareData) {
      this.setYZData({ shareData });
      this.triggerEvent('setShareData', shareData);
    },
    getSharePic(items) {
      let showItems = items;
      if (items.length > 3) {
        showItems = items.slice(0, 3);
      }
      app
        .request({
          method: 'POST',
          path: '/wscindustry/groupbuying/poster/shareCard.json',
          data: {
            items: showItems.map((item) => {
              return {
                price: `￥${item.salesPrice}`,
                imgUrl: fullfillImage(item.imgUrl, '!180x0.jpg'),
              };
            }),
            title: '立即参团',
            goodsNum: showItems.length,
          },
          contentType: 'application/json',
        })
        .then((res) => {
          const shareData = {
            ...this.data.shareData,
            imageUrl: res.value,
          };
          this.updateShareData(shareData);
        })
        .catch(() => {});
    },
  },
});
