<van-popup
  show="{{ visible }}"
  custom-class="groupbuy-share-dialog"
  custom-style="border-radius: 20px 20px 0 0; text-align: center;"
  position="bottom"
  close-on-click-overlay="{{ true }}"
  bind:close="close"
  bind:click-overlay="close"
>
  <!-- 自提码错误提示 -->
  <block wx:if="{{ hasError }}">
    <view class="groupbuy-share-dialog__error">
      <image
        class="groupbuy-share-dialog__error-img"
        src="https://img01.yzcdn.cn/upload_files/2020/03/12/FoFlIZNte-bq-nCcHIiZHDdvdwNa.png"
        alt="数据获取失败"
      />
      <view class="groupbuy-share-dialog__error-text">
        数据获取失败
      </view>
    </view>
    <view class="groupbuy-share-dialog__buttons">
      <van-button
        custom-class="groupbuy-share-dialog__btn"
        bindtap="queryShare"
      >
        点击重试
      </van-button>
    </view>
  </block>
  <block wx:elif="{{ loading }}">
    <view class="groupbuy-share-dialog__loading">
      <van-loading class="groupbuy-share-dialog__loading-icon" />
      <view class="groupbuy-share-dialog__loading-text">
        正在获取数据...
      </view>
    </view>
    <view class="groupbuy-share-dialog__buttons">
      <van-button
        custom-class="groupbuy-share-dialog__btn"
      >
        邀请好友参与
      </van-button>
    </view>
  </block>
  <block wx:else>
    <view class="groupbuy-share-dialog__title">
      恭喜你，参团成功
    </view>
    <view wx:if="{{ showGoalNum }}" class="groupbuy-share-dialog__condition">
      还差
      <view class="groupbuy-share-dialog__condition-num inline">{{ formatNum}}</view>
      {{ unit }}成团，距结束
      <count-down time="{{ countdown }}" custom-class="groupbuy-share-dialog__count-down" format="DD天HH时mm分ss秒" />
    </view>
    <view wx:else class="groupbuy-share-dialog__condition">
      快去邀请好友一起参与吧
    </view>
    <view class="groupbuy-share-dialog__card">
      <view class="groupbuy-share-dialog__buyer">
        <image
          class="groupbuy-share-dialog__buyer-avatar"
          src="{{ customerAvatar }}"
        />
        <view class="groupbuy-share-dialog__buyer-name">
          {{ customerNickname }}
        </view>
      </view>
      <view class="groupbuy-share-dialog__text">
        “我在团长【{{ headerName }}】处团购了
        <view class="groupbuy-share-dialog__text-num inline"> {{ goodsNum }} </view>
        件商品，
        <view class="inline" wx:if="{{ savedMoney }}">
          省了
          <view class="groupbuy-share-dialog__text-num inline"> {{formatSavedMoney}} </view>
          元，
        </view>
        快来参团吧”
      </view>
      <view class="groupbuy-share-dialog__goods">
        <view
          wx:for="{{ items }}"
          wx:key="index"
          wx:for-item="item"
          class="groupbuy-share-dialog__goods-item"
        >
          <image class="goods-picture" src="{{ item.imgUrl }}" />
          <view class="goods-price">￥{{ item.salesPrice }}</view>
        </view>
      </view>
    </view>
    <view class="groupbuy-share-dialog__buttons">
      <button
        class="groupbuy-share-dialog__btn"
        open-type="share"
      >
        邀请好友参与
      </button>
    </view>
  </block>
</van-popup>