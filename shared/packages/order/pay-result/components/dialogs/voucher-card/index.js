import WscComponent from 'shared/common/base/wsc-component';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();

WscComponent({
  properties: {
    // 是否显示
    visable: {
      type: Boolean,
      value: false
    },

    data: {
      type: Object,
      value: {}
    }
  },

  data: {},

  methods: {
    onClose() {
      this.triggerEvent('close');
    },

    saveToLocal() {
      wx.showLoading({
        title: '生成海报中'
      });
      this.getPoster(true);
    },

    // 下载并存储海报
    getPoster(needRetry) {
      const fail = () => {
        wx.showToast({ title: '生成海报失败', icon: 'none' });
      };
      app
        .request({
          method: 'POST',
          path: '/wsctrade/poster/card-voucher.json',
          data: {
            ...this.data.data
          }
        })
        .then(({ value }) => {
          wx.hideLoading();
          if (value) {
            app.downloadFile({
              url: value,
              success: ({ statusCode, tempFilePath }) => {
                if (statusCode === 200) {
                  wx.saveImageToPhotosAlbum({
                    filePath: tempFilePath,
                    success() {
                      wx.showToast({ title: '保存成功' });
                    },
                    fail() {
                      wx.showToast({ title: '保存成功' });
                    }
                  });
                } else {
                  fail();
                }
              },
              fail
            });
          } else {
            fail();
          }
        })
        .catch(() => {
          if (needRetry) {
            setTimeout(() => {
              this.getPoster(false);
            }, 1000);
          } else {
            wx.hideLoading();
            fail();
          }
        });
    }
  }
});
