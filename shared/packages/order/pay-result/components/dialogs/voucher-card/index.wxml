<van-popup
  show="{{ visable }}"
  custom-style="border-radius: 20px 20px 0 0; text-align: center;"
  position="bottom"
  close-on-click-overlay="{{ ture }}"
  bind:close="onClose"
  bind:click-overlay="onClose"
>
  <view class="voucher-card-dialog__title">电子卡券码</view>
  <image src="{{ data.barcode }}" alt="二维码" class="voucher-card-dialog__qrbar" />
  <image src="{{ data.qrcode }}" alt="二维码" class="voucher-card-dialog__qrcode" />
  <view class="voucher-card-dialog__buttons">
    <theme-view
      custom-class="voucher-card-dialog__btn-primary"
      color="main-text"
      bg="general"
      bindtap="saveToLocal"
    >
      保存到相册
    </theme-view>
    <van-button
      custom-class="voucher-card-dialog__btn-cancel"
      bindtap="onClose"
    >
      我知道了
    </van-button>
  </view>
</van-popup>
