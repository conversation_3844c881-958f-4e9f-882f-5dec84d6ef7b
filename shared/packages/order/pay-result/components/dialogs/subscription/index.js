import WscComponent from 'shared/common/base/wsc-component';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();
const SAVE_TAG = `pay_success_dialog_click_${app.getKdtId()}`;

WscComponent({
  properties: {
    // 是否显示
    visable: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'visableObserver'
    }
  },

  data: {},

  methods: {
    onClose() {
      this.triggerEvent('close');
    },

    onCloseAndRemember() {
      this.triggerEvent('close');
      wx.setStorage({
        key: SAVE_TAG,
        data: 1
      });
    },

    visableObserver(visable) {
      if (visable) {
        app.logger && app.logger.log({
          et: 'view',
          ei: 'show_fuchuang_dingyuewuliu',
          en: '“订阅物流”浮窗曝光',
          si: app.getKdtId()
        });
      }
    }
  }
});
