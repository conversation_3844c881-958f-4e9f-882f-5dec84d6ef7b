$text-color: #323233;
$border-color: #e5e5e5;
$qrnum-border-color: #dcdee0;
$qrnum-text-color: #7d7e80;
$text-num-color: #333;
$btn-color: #07c160;

.groupbuy-selffetch-dialog {
  &__title {
    padding: 11px 0;
    font-weight: 500;
    color: $text-color;
    font-size: 16px;
  }

  &__address {
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    padding: 14px 26px 0;
    text-align: left;

    &::after {
      content: '';
      width: 100%;
      border-bottom: 1px dashed $border-color;
      display: block;
      margin-top: 8px;
    }

    &-text {
      margin-bottom: 12px;
      line-height: 1.4em;
    }

    &-text:last-child {
      margin-bottom: 24px;
      line-height: 1.4em;
    }
  }

  &__code {
    display: inline-block;
    color: $btn-color;
    font-size: 16px;
    font-weight: 800;
    font-family: Avenir;
  }

  &__qrcode {
    width: 178px;
    height: 178px;
    margin-top: 24px;
    margin-bottom: 12px;
    user-select: none;
    position: relative;
    z-index: 10;
    padding: 8px;
    box-sizing: border-box;
    border: 1px solid $qrnum-border-color;
  }

  &__qrnum {
    text-align: center;
    color: $text-num-color;
    font-size: 16px;
    font-weight: 800;
    margin-bottom: 20px;
    user-select: none;
    position: relative;
    z-index: 10;
    font-family: Avenir;
    line-height: 20px;
  }

  &__buttons {
    margin: 0 24px;
  }

  &__save-btn,
  &__cancel-btn {
    display: block !important;
    width: 100% !important;
    background: #fff !important;
    border-radius: 22px !important;
    border: none !important;
    font-size: 14px !important;
    color: #969799 !important;
    line-height: 44px !important;
  }

  &__cancel-btn {
    margin: 4px 0 16px !important;
  }

  &__save-btn {
    font-size: 16px !important;
    background: $btn-color !important;
    color: #fff !important;
    height: 44px !important;
  }

  &__error {
    background-image: url('https://img01.yzcdn.cn/upload_files/2020/10/14/FsQNN_KCUGmq6Cje6VEB7lMD5_vK.png');
    background-size: 192px 224px;
    width: 192px;
    height: 224px;
    margin: 17px auto 13px;

    &-text {
      color: $text-color;
      font-size: 14px;
      line-height: 20px;
      padding-top: 85px;
    }
  }

  &__skeleton {
    padding: 14px 0 20px 78px;
    margin: 0 26px;
    position: relative;
    border-bottom: 1px dashed $border-color;

    &-title {
      width: 70px;
      position: absolute;
      left: 0;
      padding: 0;
    }

    &-content {
      padding: 0;
    }
  }

  &__loading {
    background-image: url('https://img01.yzcdn.cn/upload_files/2020/10/14/FsQNN_KCUGmq6Cje6VEB7lMD5_vK.png');
    background-size: 192px 224px;
    width: 192px;
    height: 224px;
    margin: 17px auto 13px;

    &-text {
      color: $text-color;
      font-size: 14px;
      line-height: 20px;
      padding-top: 85px;
    }
  }
}

@media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
  .groupbuy-selffetch-dialog {
    padding-bottom: 34px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}
