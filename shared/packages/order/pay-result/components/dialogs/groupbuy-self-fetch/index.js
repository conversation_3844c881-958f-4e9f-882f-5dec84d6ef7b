import WscComponent from 'shared/common/base/wsc-component';
import getApp from 'shared/utils/get-safe-app';
import get from '@youzan/weapp-utils/lib/get';

const app = getApp();

WscComponent({
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false,
      observer: 'toggleDialog',
    },

    orderNo: String,
  },

  data: {
    imageUrl: '',
    qrCode: '',
    headerName: '',
    payNo: '',
    verifyCode: '',
    qrNum: '',
    headerFetchAddress: '',
    items: [],
    loading: false,
    hasError: false,
  },

  methods: {
    toggleDialog(newValue) {
      if (newValue && !this.data.items.length) {
        this.queryOrder();
      }
      if (newValue) {
        app.logger &&
          app.logger.log({
            et: 'click', // 事件类型
            ei: 'groupbuy_pickupcode', // 事件标识
            en: '查看提货码', // 事件名称
            pt: 'paySuccess', // 页面类型
          });
      }
    },

    queryOrder() {
      const { loading, items, orderNo } = this.data;
      if (loading || items.length) return;
      this.setYZData({ loading: true, hasError: false });
      app
        .request({
          method: 'GET',
          path: '/wscindustry/groupbuying/poster/getSelfFetchCode.json',
          data: {
            orderNo,
          },
        })
        .then((res) => {
          const {
            qrCode,
            verifyCode,
            headerName,
            payNo,
            headerFetchAddress,
            items,
          } = res;
          this.setYZData({
            qrCode,
            verifyCode,
            headerName,
            payNo,
            qrNum: verifyCode
              ? verifyCode.replace(/(\d{4})(\d{4})(\d*)/g, '$1 $2 $3')
              : '',
            headerFetchAddress,
            items: items || [],
            loading: false,
          });
        })
        .catch(() => {
          this.setYZData({
            hasError: true,
            loading: false,
          });
        });
    },

    close() {
      this.triggerEvent('close');
    },

    saveToLocal() {
      // 调用生成自提海报的接口
      if (this.data.imageUrl) {
        this.close();
        return;
      }
      wx.showLoading({
        title: '正在生成海报',
      });

      this.getPoster(true);

      app.logger &&
        app.logger.log({
          et: 'click', // 事件类型
          ei: 'groupbuy_saveto_photoalbum', // 事件标识
          en: '提货码卡片保存至相册', // 事件名称
          pt: 'paySuccess', // 页面类型
        });
    },

    getPoster(needRetry) {
      const {
        headerFetchAddress,
        headerName,
        payNo,
        qrCode,
        qrNum,
      } = this.data;
      const fail = () => {
        wx.showToast({ title: '生成海报失败', icon: 'none' });
      };

      const items = this.data.items.map((item) => {
        let sku = [];
        try {
          sku = JSON.parse(item.sku);
          // eslint-disable-next-line no-empty
        } catch (e) {}
        return {
          title: item.title, // 商品标题
          num: item.num, // 商品数量
          sku: sku.map((sku) => `${sku.k}：${sku.v}`).join('；') || '默认规格', // sku信息, 如黄色：250;
          imgUrl: get(item, 'picture[0].url'), // 商品图片地址
        };
      });

      app
        .request({
          path: '/wscindustry/groupbuying/poster/selfFetch.json',
          method: 'POST',
          data: {
            headerFetchAddress,
            headerName,
            payNo,
            items: items.slice(0, 3), // 商品列表
            itemsLength: items.length,
            goodsNum: items.reduce((prev, { num }) => {
              return prev + num;
            }, 0),
            qrCode, // 提货二维码
            qrNum, // 提货码
          },
          dataType: 'json',
        })
        .then(({ value }) => {
          wx.hideLoading();
          if (value) {
            app.downloadFile({
              url: value,
              success: ({ statusCode, tempFilePath }) => {
                if (statusCode === 200) {
                  wx.saveImageToPhotosAlbum({
                    filePath: tempFilePath,
                    success() {
                      this.close();
                      wx.showToast({ title: '保存成功' });
                    },
                    fail() {
                      wx.showToast({ title: '保存失败', icon: 'none' });
                    },
                  });
                } else {
                  fail();
                }
              },
              fail,
            });
          } else {
            fail();
          }
        })
        .catch(() => {
          if (needRetry) {
            setTimeout(() => {
              this.getPoster(false);
            }, 1000);
          } else {
            wx.hideLoading();
            fail();
          }
        });
    },
  },
});
