<van-popup
  show="{{ visible }}"
  custom-class="groupbuy-selffetch-dialog"
  custom-style="border-radius: 20px 20px 0 0; text-align: center;"
  position="bottom"
  close-on-click-overlay="{{ true }}"
  bind:close="close"
  bind:click-overlay="close"
>
  <view class="groupbuy-selffetch-dialog__title">
    提货码
  </view>
  <!-- 自提码错误提示 -->
  <block wx:if="{{ hasError }}">
    <view class="groupbuy-selffetch-dialog__address">
      <view class="groupbuy-selffetch-dialog__address-text">提货地址：-</view>
      <view class="groupbuy-selffetch-dialog__address-text">团长名称：-</view>
      <view class="groupbuy-selffetch-dialog__address-text">提货序号：-</view>
    </view>
    <view class="groupbuy-selffetch-dialog__error">
      <view class="groupbuy-selffetch-dialog__error-text">
        提货码获取失败
      </view>
    </view>
    <view class="groupbuy-selffetch-dialog__buttons">
      <van-button
        custom-class="groupbuy-selffetch-dialog__save-btn"
        bindtap="queryOrder"
      >
        点击重试
      </van-button>
      <van-button
        custom-class="groupbuy-selffetch-dialog__cancel-btn"
        bindtap="close"
      >
        我知道了
      </van-button>
    </view>
  </block>
  <block wx:elif="{{ loading }}">
    <view class="groupbuy-selffetch-dialog__skeleton">
      <van-skeleton
        custom-class="groupbuy-selffetch-dialog__skeleton-title"
        row="3"
        row-width="{{ ['100%', '100%', '100%'] }}"
      />
      <van-skeleton
        custom-class="groupbuy-selffetch-dialog__skeleton-content"
        row="3"
        row-width="{{ ['100%', '19.6%', '12.2%'] }}"
      />
    </view>
    <view class="groupbuy-selffetch-dialog__loading">
      <view class="groupbuy-selffetch-dialog__loading-text">
        提货码生成中
      </view>
    </view>
    <view class="groupbuy-selffetch-dialog__buttons">
      <van-button
        custom-class="groupbuy-selffetch-dialog__save-btn"
      >
        保存到相册
      </van-button>
      <van-button
        custom-class="groupbuy-selffetch-dialog__cancel-btn"
        bindtap="close"
      >
        我知道了
      </van-button>
    </view>
  </block>
  <block wx:else>
    <view class="groupbuy-selffetch-dialog__address">
      <view class="groupbuy-selffetch-dialog__address-text">提货地址：{{ headerFetchAddress }}</view>
      <view class="groupbuy-selffetch-dialog__address-text">团长名称：{{ headerName }}</view>
      <view class="groupbuy-selffetch-dialog__address-text">
        提货序号：
        <view class="groupbuy-selffetch-dialog__code">{{ payNo }}</view>
      </view>
    </view>
    <image class="groupbuy-selffetch-dialog__qrcode" src="{{ qrCode }}" alt="社群团购自提码" />
    <view class="groupbuy-selffetch-dialog__qrnum">
      {{ qrNum }}
    </view>
    <view class="groupbuy-selffetch-dialog__buttons">
      <van-button
        custom-class="groupbuy-selffetch-dialog__save-btn"
        style="margin-bottom: 5px;"
        bindtap="saveToLocal"
      >
        保存到相册
      </van-button>
      <van-button
        custom-class="groupbuy-selffetch-dialog__cancel-btn"
        bindtap="close"
      >
        我知道了
      </van-button>
    </view>
  </block>
</van-popup>
