$text-color: #323233;
$border-color: #dcdee0;
$qrnum-border-color: #dcdee0;
$qrnum-text-color: #7d7e80;
$text-num-color: #333;

.solitaire-dialog {
  border-radius: 20px 20px 0 0;
  text-align: center;

  &__popup-inner-div{
    background-size: 375px 205px;
    background-repeat: no-repeat;
    background-image: url("https://b.yzcdn.cn/public_files/b30b2ac50fe84d5cc05decdbe282884a.png");
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: center;
    align-items: center;
  }
  &__bottom-share-btn {
    margin-top: 6px;
    margin-bottom: 16px;
    border-radius: 22px;
    width: 306px;
    display: flex;
    justify-content: center;
    align-content: center;
    background-image: linear-gradient(90deg,#07C160, #66BD43);
    height: 44px;
    font-size: 18px;
    font-weight: 500;
    color: #fff;
    align-items: center;
  }

  &__solitaire-number-div {
    margin-top: 30px;
    display: flex;
    align-content: center;
    justify-content: center;
    width: 119px;
    height: 99px;
    background-size: 119px 99px;
    background-image: url("https://b.yzcdn.cn/public_files/b5e9c043df474b7f002e0a913da5f643.png");
  }
  &__congratulation-text{
    margin-top: 16px;
    display: flex;
    align-content: center;
    justify-content: center;
    height: 28px;
    font-size: 20px;
    color: #5E290F;
  }
  &__order-info-div{
    align-content: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 24px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    width: 306px;
    background-image: linear-gradient(180deg,#E8FEF6, #EFFCEA);
  }
  &__order-info-line{
    flex-direction: column;
    align-content: center;
    display: flex;
    margin-top: 8px;
    align-content: center;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-style: solid;
    border-width: 1px;
    width: 290px;
    height: 166px;
    border-color: #C8F3CB;
    border-bottom-width: 0px;
  }
  &__user-head-img{
    height: 24px;
    width: 24px;
    border-radius: 12px;
  }
  &__user-order-detail{
    margin-left: 50px;
    margin-top: 2px;
    flex-direction: column;
    display: flex;
  }
  &__user-info-div{
    height: 24px;
    margin-left: 20px;
    margin-top: 16px;
    display: flex;
    flex-direction: row;
  }
  &__user-name-text{
    display: flex;
    justify-content: left;
    align-content: center;
    height: 20px;
    font-size: 14px;
    margin-left: 6px;
    align-items: center;
  }
  &__buy-item{
    display: flex;
    flex-direction: row;
    width: 220px;
    align-items: baseline;
    justify-content: space-between;
  }
  &__item-name{
    color: #323233;
    font-size: 12px;
    line-height: 18px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  &__item-num{
    color: #323233;
    font-size: 12px;
    line-height: 18px;
  }
  &__sort-no-text{
    margin-left: 5px;
    margin-top: 73px;
    justify-content: center;
    font-size: 14px;
    color: #ffffff;
    font-weight: 700;
  }
  &__items-num{
    justify-content: left;
    color:#969799;
    font-size: 12px;
    line-height: 17px;
    margin-left: 50px;
    margin-top: 9px;
    font-weight: 400;
    display: flex;
  }
}
