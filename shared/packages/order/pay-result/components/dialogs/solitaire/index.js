import WscComponent from 'shared/common/base/wsc-component';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();

// 获取海报图片
function getByKey(params) {
  return app.request({
    path: '/wscump/communitybuy/poster/getByKey.json',
    data: params,
    type: 'GET',
    withCredentials: true,
  });
}

const checkPoster = (key) =>
  new Promise((resolve, reject) => {
    let i = 5;
    const check = () => {
      setTimeout(() => {
        getByKey({
          key,
        })
          .then((res) => {
            if (res.img) {
              resolve(res);
            } else if (i > 0 && res.type === 'pending') {
              i--;
              check();
            } else {
              throw new Error('海报生成失败，请重试');
            }
          })
          .catch((e) => {
            reject(e);
          });
      }, 1000);
    };
    check();
  });

WscComponent({
  properties: {
    // 是否显示
    visable: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'visableObserver',
    },
    orderNo: {
      type: String,
      value: '',
    },
  },
  data: {
    solitaireData: {},
    showBuyItems: [],
    showData: {
      shareImg:
        'https://img01.yzcdn.cn/upload_files/2020/04/26/Flhx0PkQwvyd4HQGINelGVdl3Qo3.png',
      activityName: '',
      activityAlias: '',
      sls: '',
    },
  },
  ready() {
    app
      .request({
        method: 'GET',
        path: '/wscindustry/solitaire/getOrderShare.json',
        query: {
          orderNo: this.data.orderNo,
        },
      })
      .then((res) => {
        if (res) {
          this.setYZData({
            solitaireData: res,
            showBuyItems: res.buyItems.slice(0, 3),
            shareData: {
              ...this.data.shareData,
              activityName: res.activityName,
              activityAlias: res.activityAlias,
              shareImg:
                'https://img01.yzcdn.cn/upload_files/2020/04/26/Flhx0PkQwvyd4HQGINelGVdl3Qo3.png',
            },
          });
          this.updateShareData();
          this.getUserSl();
        }
      })
      .catch(() => {});
    app.logger &&
      app.logger.log({
        et: 'view',
        ei: 'solitaire_dialog_show',
        en: '接龙成功卡片曝光',
      });
  },

  methods: {
    getUserSl() {
      app
        .request({
          method: 'GET',
          path: '/wscindustry/solitaire/getAccountSl.json',
        })
        .then((res) => {
          if (res) {
            if (res.sls == undefined) {
              this.setYZData({
                shareData: { ...this.data.shareData, sls: '' },
              });
            } else {
              this.setYZData({
                shareData: { ...this.data.shareData, sls: res.sls },
              });
            }
            this.updateShareData();
            this.getSharePic();
          }
        })
        .catch(() => {});
    },
    onClose() {
      this.triggerEvent('close');
    },

    updateShareData() {
      this.triggerEvent('setShareData', this.data.shareData);
    },

    getSharePic() {
      let showData = this.data.showBuyItems;
      if (this.data.showBuyItems.length > 2) {
        showData = this.data.showBuyItems.slice(0, 1);
      }
      app
        .request({
          method: 'POST',
          path: '/wscindustry/solitaire/poster/getSolitaireWeappShareImg.json',
          data: {
            userAvatar: this.data.solitaireData.buyerAvatar,
            userName: this.data.solitaireData.buyerName,
            activityNum: this.data.solitaireData.sortNo,
            userBuyNums: this.data.solitaireData.buyItems.length,
            showBuyItems: showData,
          },
          contentType: 'application/json',
        })
        .then((res) => {
          if (res.redisKey) {
            return checkPoster(res.redisKey);
          }
          const reason = '海报生成失败，请重试';
          return Promise.reject(reason);
        })
        .then((res) => {
          if (res.img !== undefined) {
            this.setYZData({
              shareData: { ...this.data.shareData, shareImg: res.img },
            });
          }
          this.updateShareData();
        })
        .catch(() => {});
    },

    handleShare() {
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'solitaire_dialog_show_click',
          en: '接龙成功卡片点击分享',
        });
    },
  },
});
