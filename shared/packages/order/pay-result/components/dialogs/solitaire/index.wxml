<van-popup
  z-index="{{ 9999 }}"
  show="{{visable}}"
  custom-style="border-radius: 20px 20px 0 0; text-align: center;"
  position="bottom"
  close-on-click-overlay="{{ ture }}"
  bind:close="onClose"
  bind:click-overlay="onClose"
  bind:setShareData="updateShareData"
>
  <view class="solitaire-dialog__popup-inner-div">
    <view class="solitaire-dialog__solitaire-number-div">
      <view class="solitaire-dialog__sort-no-text">NO.{{solitaireData.sortNo}}</view>
    </view>
    <view class="solitaire-dialog__congratulation-text">恭喜你，接龙成功</view>
    <view class="solitaire-dialog__order-info-div">
      <view class="solitaire-dialog__order-info-line">
        <view class="solitaire-dialog__user-info-div">
          <image class="solitaire-dialog__user-head-img" src="{{solitaireData.buyerAvatar}}"></image>
          <view class="solitaire-dialog__user-name-text">{{solitaireData.buyerName}}</view>
        </view>
        <view class="solitaire-dialog__user-order-detail">
          <view wx:for="{{showBuyItems}}" wx:for-item="item" class="solitaire-dialog__buy-item">
            <view class="solitaire-dialog__item-name">{{item.itemName}}</view>
            <view class="solitaire-dialog__item-num">+{{item.buyNum}}</view>
          </view>
        </view>
        <view class="solitaire-dialog__items-num" wx:if="{{solitaireData.buyItems.length>3}}">
          共{{solitaireData.buyItems.length}}件商品
        </view>
      </view>
    </view>
    <button open-type="share" bind:tap="handleShare" class="solitaire-dialog__bottom-share-btn">
      通知大家接龙成功
    </button>
  </view>
</van-popup>
