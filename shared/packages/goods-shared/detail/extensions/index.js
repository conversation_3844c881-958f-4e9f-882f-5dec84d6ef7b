import { getCurrentPage } from 'shared/common/base/wsc-component';
import { ReactiveHook, EventHook, InvokeHook } from './hooks';
import initGoods from './goods';
/* #ifdef BUILD_ENV=youzanyun */
import initYouzanYun from './yun';
/* #endif */

function getHooks() {
  return {
    showTab: new ReactiveHook(),
    showPrice: new ReactiveHook(),
    showPriceBlock: new ReactiveHook(),
    showActivityBanner: new ReactiveHook(),
    showGoodsBottom: new ReactiveHook(),
    showShareIcon: new ReactiveHook(),
    showFollowShopIcon: new ReactiveHook(),
    showGuaranteeBar: new ReactiveHook(),
    showPldShopBadge: new ReactiveHook(),
    showMultiStoreInfo: new ReactiveHook(),
    showVirtualTicketIntro: new ReactiveHook(),
    setDetailTabs: new ReactiveHook(),
    showServiceBar: new ReactiveHook(),
    diyServiceDescList: new ReactiveHook(),
    // 是否展示定金预售活动模块
    showDepositPresalInfo: new ReactiveHook(),
    showPackageBuy: new ReactiveHook(),
    showPromotionList: new ReactiveHook(),
    showImage: new ReactiveHook(),

    beforeCartSubmit: new EventHook(),
    afterBuy: new EventHook(),
    beforeBuy: new EventHook(),
    detailTabClick: new EventHook(),
    beforePreviewMainImage: new EventHook(),

    showSku: new InvokeHook(),
    btnClick: new InvokeHook(),
    navToIm: new InvokeHook(),
    activeTab: new InvokeHook(),
    setShareParams: new InvokeHook(),
    addCart: new InvokeHook(),
    doShare: new InvokeHook(),
  };
}

export function setExtensions(page) {
  page.hooks = getHooks();

  /* #ifdef BUILD_ENV=youzanyun */
  initYouzanYun(page);
  /* #endif */
  initGoods(page);
}

export function getPageHooks(component) {
  const page = getCurrentPage(component);

  return page.hooks;
}
