import { setLogger } from '@goods/common/logger';
import { subscribeHooks } from '../helper';

export default function registerProcess(bridge, page) {
  const { hooks } = page;
  const subscriber = subscribeHooks(hooks, 'diy');

  // 显示隐藏分享按钮
  subscriber('showShareIcon', (show, emmiter) => {
    bridge.setProcess('showShareIcon', (isTrue) => {
      setLogger('showShareIcon');
      emmiter(show && isTrue);
    });
  });

  // 唤起分享
  bridge.setProcess('doShare', () => {
    setLogger('doShare');

    return hooks.doShare.call();
  });

  // 隐藏有赞担保
  subscriber('showGuaranteeBar', (show, emmiter) => {
    bridge.setProcess('showGuaranteeBar', (isTrue) => {
      setLogger('showGuaranteeBar', isTrue);
      emmiter(show && isTrue);
    });
  });

  // 隐藏电子卡券说明模块，反之或者不传，都按照正常逻辑显示
  subscriber('showVirtualTicketIntro', (show, emmiter) => {
    bridge.setProcess('hideVirtualTicketIntro', () => {
      setLogger('hideVirtualTicketIntro');

      emmiter(false);
    });
  });

  subscriber('showGoodsBottom', (show, emmiter) => {
    // 显示商品底部按钮块
    bridge.setProcess('showGoodsBottom', () => {
      setLogger('showGoodsBottom');
      emmiter(show && true);
    });

    // 隐藏商品底部按钮块
    bridge.setProcess('hideGoodsBottom', () => {
      setLogger('hideGoodsBottom');
      emmiter(show && false);
    });
  });

  // 弹出sku流程
  bridge.setProcess('showSKU', (type) => {
    setLogger('showSKU', type);

    return hooks.showSku.call(type);
  });

  // 底部大按钮点击流程
  bridge.setProcess('btnClick', (btn) => {
    setLogger('btnClick', btn);

    return hooks.btnClick.call(btn);
  });

  // 添加购物车
  bridge.setProcess('addCart', (data) => {
    setLogger('addCart', data);
    return hooks.addCart.call(data);
  });

  subscriber('showTab', (show, emitter) => {
    bridge.setProcess('hideTabs', () => {
      setLogger('hideTabs');
      emitter(false);
    });
  });

  bridge.setProcess('setActiveTab', (type) => {
    setLogger('setActiveTab', type);

    return hooks.activeTab.call(type);
  });

  subscriber('setDetailTabs', (val, emitter) => {
    bridge.setProcess('setTabs', (tabs) => {
      setLogger('setTabs', tabs);

      if (Array.isArray(tabs)) {
        if (tabs.length > 5) {
          // eslint-disable-next-line no-console
          console.warn('长度超出 5，超出部分被截取');
          tabs = tabs.slice(0, 5);
        }

        tabs = tabs.map(({ desc = '', type = '' }) => ({ desc, type }));
        return emitter(tabs);
      }
      throw new Error('tabs 必须是一个数组');
    });
  });

  // 设置分享参数
  bridge.setProcess('setPageShareParams', (params) => {
    setLogger('setPageShareParams', params);
    hooks.setShareParams.call(params);
  });

  // 获取 isv_ext 参数
  bridge.setPageProcess('getISVExt', () => {
    return page.options.isv_ext;
  });

  // 显示隐藏服务栏
  subscriber('showServiceBar', (show, emmiter) => {
    bridge.setProcess('showServiceBar', (isTrue) => {
      setLogger('showServiceBar');
      emmiter(show && isTrue);
    });
  });

  subscriber('diyServiceDescList', (val, emitter) => {
    bridge.setProcess('setServiceDescList', (services) => {
      setLogger('setServiceDescList', services);

      if (Array.isArray(services)) {
        return emitter(services);
      }
      throw new Error('services 必须是一个数组');
    });
  });

  // 显示隐藏定金预售活动模块
  subscriber('showDepositPresalInfo', (show, emmiter) => {
    bridge.setProcess('showDepositPresalInfo', (isTrue) => {
      setLogger('showDepositPresalInfo');
      emmiter(show && isTrue);
    });
  });

  // 显示隐藏优惠套餐模块
  subscriber('showPackageBuy', (show, emmiter) => {
    bridge.setProcess('showPackageBuy', (isTrue) => {
      setLogger('showPackageBuy', isTrue);
      emmiter(show && isTrue);
    });
  });

  // 显示隐藏促销模块
  bridge.setProcess('showGoodsPromotion', (isTrue) => {
    page.$store.commit('setIsShowGoodsPromotion', isTrue);
  });

  // 唤起促销活动弹窗
  bridge.setProcess('showPromotionCoupon', () => {
    page.$store.dispatch('handlePromotionCoupon');
  });

  // 显示隐藏价格模块
  subscriber('showPriceBlock', (show, emmiter) => {
    bridge.setProcess('showPriceBlock', (isTrue) => {
      setLogger('showPriceBlock');
      emmiter(show && isTrue);
    });
  });
  // 显示隐藏活动banner
  subscriber('showActivityBanner', (show, emmiter) => {
    bridge.setProcess('showActivityBanner', (isTrue) => {
      setLogger('showActivityBanner');
      emmiter(show && isTrue);
    });
  });
  // 显示隐藏领券模块
  subscriber('showPromotionList', (show, emmiter) => {
    bridge.setProcess('showPromotionList', (isTrue) => {
      setLogger('showPromotionList');
      emmiter(show && isTrue);
    });
  });

  subscriber('showImage', (show, emitter) => {
    // 隐藏主图
    bridge.setProcess('hidePictures', () => {
      emitter(false);
    });
  });

  bridge.setProcess('hideDesc', () => {
    const tabs =
      bridge.page.detailTabs.filter((tab) => tab.type !== 'goods') || [];
    const { process } = bridge.page;
    process.setTabs && process.setTabs(tabs);
    tabs.length > 0 &&
      process.setActiveTab &&
      process.setActiveTab(tabs[0].type);
  });
}
