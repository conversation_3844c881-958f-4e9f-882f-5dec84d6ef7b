import { mapKeysToCamelCase, get } from '@goods/utils';

const defineProperty = function(obj, key, value) {
  obj[mapKeysToCamelCase(key)] = value;
};

const getInfoFromObj = function(obj, infoKey, keys) {
  const info = (infoKey ? get(obj, infoKey) : obj) || {};
  const result = {};
  keys.forEach((it) => defineProperty(result, it, info[it]));
  return result;
};

// 商品基本信息
const getGoodsItem = function(state) {
  const result = getInfoFromObj(state, 'goods', [
    'id',
    'alias',
    'title',
    'picture',
    'sellPoint',
    'origin',
    'isVirtual',
    'isVirtualTicket',
    'isDisplay',
    'buyLimitType',
    'waitToSoldTime',
    'buyWay',
    'buyUrl',
    'forbidBuyReason',
    'isSupportFCode',
    'isGoodsCollected',
    'isInstallment',
    'soldNum',
    'sellPoint',
    'subTitle',
    'price',
    'oldPrice',
    'isPrescriptionCategory',
    'itemCatePropDetailModel'
  ]);

  result.limitBuy = result.buyLimitType === 1;

  defineProperty(
    result,
    'risk',
    getInfoFromObj(state, 'goods.risk', ['match', 'note'])
  );
  return result;
};

// 获取退款相关配置
const getRefund = function(state) {
  return getInfoFromObj(state, 'refund', ['isSupport', 'type', 'interval']);
};

// 获取多网点信息
const getMultistore = function(state) {
  return getInfoFromObj(state, 'multistore', ['name', 'id']);
};

// 获取店铺信息
const getShop = function(state) {
  return getInfoFromObj(state, 'shop', ['logo', 'name', 'url', 'certType']);
};

// 获取店铺配置信息
const getShopConfig = function(state) {
  return getInfoFromObj(state, 'shopConfig', [
    'isShowBuyBtn',
    'isSecuredTransactions',
    'showRecommendGoods',
    'showBuyRecord',
    'showCustomerReviews',
    'showBuyerShows',
    'showPersonalRecommendGoods',
    'isSimplifiedWsc',
    'supportFreightInsurance',
    'hideShoppingCart',
    'hasPhysicalStore',
  ]);
};

// 获取店铺担保配置
const getGuarantee = function(state) {
  return getInfoFromObj(state, 'guarantee', ['on', 'style']);
};

// 获取配送信息
const getDistribution = function(state) {
  return getInfoFromObj(state, 'distribution', [
    'postage',
    'supportExpress',
    'supportSelfFetch',
    'supportLocalDelivery',
    'expressFee',
    'localDeliveryFee',
  ]);
};

const getButtons = function() {
  const { bigButtonsData } = this.$store.getters;

  return bigButtonsData.map((item) => item.skuScene);
};

const getDetailTabs = function() {
  const { goodsDetailTabs } = this.$store.getters;

  return goodsDetailTabs.map((item) => ({ ...item }));
};

const getServiceDescList = function() {
  const { serviceDescList = [] } = this.$store.getters;

  return serviceDescList.map((item) => ({ ...item }));
};

// 活动信息
const getActivities = function (state) {
  const { presaleActivity, isDepositPresale } = this.$store.getters;
  let depositPresale = null;
  const { outerActivities = [] } = state;

  if (isDepositPresale) {
    const {
      preStartTime,
      preEndTime,
      payStartTime,
      payEndTime,
      shipStartTime,
      shipType,
      deposit,
      minDeposit,
      maxDeposit,
      depositPriceType,
      depositRatio,
      shipDelayDays,
      period,
      payStartDay,
      payEndDay,
    } = presaleActivity || {};

    depositPresale = {
      // 0-短期预售 1-长期预售
      period,
      // 预售开始时间
      preStartTime,
      // 预售结束时间
      preEndTime,
      // 定金预售长期：尾款在定金支付后x天开始支付
      payStartDay,
      // 定金预售长期：尾款在定金支付后y天内完成支付
      payEndDay,
      // 尾款支付开始时间
      payStartTime,
      // 尾款支付结束时间
      payEndTime,
      // 定金预售类型 0-按比例的预售，1-按金额的预售
      depositPriceType,
      // 定金比率
      depositRatio,
      // 固定金额的值
      deposit,
      // 定金最小值
      minDeposit,
      // 定金最大值
      maxDeposit,
      // 0: 定时发货 1：付款几天后发货
      shipType,
      // 定时发货时间
      shipStartTime,
      // 付款几天后发货
      shipDelayDays,
    };
  }

  return {
    depositPresale,
    outerActivities,
  };
};
// 组合套餐活动内容
const getCombineActivity = function (state) {
  const { parsedActivities = {} } = state;
  const { combine = {} } = parsedActivities;
  return combine || null;
};

// 促销活动数据
const getGoodsPromotion = function (state) {
  const { orderPromotion = [], parsedActivities = {} } = state;
  return {
    orderPromotion,
    parsedActivities,
  }
}

// 活动sku数据
const getSkuData = function (state) {
  const { activitySkuData = {} } = state;
  return {
    ...activitySkuData,
  }
};

// 付费优惠券数据
const getCouponDetailData = function (state) {
  const { couponIntroDetail = {} } = this.$store.getters;
  const {
    goods: { isVirtualCoupon },
  } = state;
  return { ...couponIntroDetail, isVirtualCoupon };
};

const pageData = {
  goodsItem: getGoodsItem,
  refund: getRefund,
  multistore: getMultistore,
  shop: getShop,
  shopConfig: getShopConfig,
  guarantee: getGuarantee,
  distribution: getDistribution,
  buttons: getButtons,
  detailTabs: getDetailTabs,
  serviceDescList: getServiceDescList,
  activities: getActivities,
  combineActivity: getCombineActivity,
  goodsPromotion: getGoodsPromotion,
  skuData: getSkuData,
  couponDetailData: getCouponDetailData,
};

export default function setPageData(bridge, page) {
  bridge.setPageData(page, pageData);
}
