// @ts-nocheck
import { AsyncEvent } from '@youzan/weapp-ecloud-sdk';
import { subscribeHooks } from '../helper';
import { setLogger } from '@goods/common/logger';

function emitter(event) {
  return (...args) => {
    return event.trigger(...args);
  };
}

export default function setPageEvents(bridge, page) {
  const beforeCartSubmitEvent = new AsyncEvent();
  const afterBuyEvent = new AsyncEvent();
  const beforeBuyEvent = new AsyncEvent();
  const detailTabClickEvent = new AsyncEvent();
  const beforeGenerateSalesmanGoodsPosterEvent = new AsyncEvent();
  const beforePreviewMainImageEvent = new AsyncEvent();

  const asyncEventOn = AsyncEvent.prototype.on;

  const eventWrapper = function (event, eventName) {
    event.on = function (...rest) {
      setLogger(eventName);
      asyncEventOn.call(event, ...rest);
    };
  };

  eventWrapper(afterBuyEvent, 'afterBuyEvent');
  eventWrapper(detailTabClickEvent, 'detailTabClickEvent');
  eventWrapper(beforeCartSubmitEvent, 'beforeCartSubmitEvent');
  eventWrapper(beforePreviewMainImageEvent, 'beforePreviewMainImage');

  bridge.setPageEvent('beforeCartSubmit', beforeCartSubmitEvent);
  bridge.setPageEvent('beforeBuy', beforeBuyEvent);
  bridge.setPageEvent('afterBuy', afterBuyEvent);
  bridge.setPageEvent('detailTabClick', detailTabClickEvent);
  bridge.setPageEvent(
    'beforeGenerateSalesmanGoodsPosterEvent',
    beforeGenerateSalesmanGoodsPosterEvent
  );
  bridge.setPageEvent('beforePreviewMainImage', beforePreviewMainImageEvent);

  const subscriber = subscribeHooks(page.hooks, 'diy');

  subscriber('afterBuy', emitter(afterBuyEvent));
  subscriber('beforeBuy', emitter(beforeBuyEvent));
  subscriber('detailTabClick', emitter(detailTabClickEvent));
  subscriber('beforeCartSubmit', emitter(beforeCartSubmitEvent));
  subscriber('beforePreviewMainImage', emitter(beforePreviewMainImageEvent));
}
