import setPageData from './data';
import setPageEvents from './events';
import setPageProcess from './process';

let curPage;
let bridge;

const app = getApp();
export default function initYun(page) {
  if (!ECLOUD_MODE) return;
  curPage = page;

  bridge = app.getYouZanYunSdk();
  bridge.setProcess = bridge.setPageProcess;

  setPageData(bridge, page);
  setPageEvents(bridge, page);
  setPageProcess(bridge, page);
}

export function updatePageData() {
  bridge && setPageData(bridge, curPage);
}
