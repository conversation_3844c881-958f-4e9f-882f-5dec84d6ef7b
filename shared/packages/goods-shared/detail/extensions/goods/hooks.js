/**
 * 商品详情接受外部调用扩展处理
 */
import { subscribeHooks } from '../helper';
import { args } from '@goods/utils';

export default function subscribeGoodsHooks(page) {
  const { $store, hooks } = page;
  const subscriber = subscribeHooks(hooks, 'goods');

  // 接收显示 sku 弹出事件
  subscriber('showSku', (type) => {
    const buttons = $store.getters.bigButtonsData;

    const btn = buttons.filter((item) => item.skuScene === type).pop();
    if (btn) {
      return $store.dispatch('triggerSku', btn);
    }
    return Promise.reject('不支持展示该类型SKU');
  });

  // 大按钮点击
  subscriber('btnClick', (type) => {
    const buttons = $store.getters.bigButtonsData;

    const btn = buttons.filter((item) => item.skuScene === type).pop();
    if (btn) {
      return $store.dispatch('handleBigButtonClick', btn);
    }
    return Promise.reject('不支持该按钮类型');
  });

  subscriber('doShare', () => {
    return $store.dispatch('handleShareClick');
  });

  subscriber('setShareParams', (params) => {
    const { isvExt } = params;
    const sharePath = $store.state.shareData.wx.path;

    if (isvExt) {
      const path = args.add(sharePath, { isv_ext: isvExt });
      $store.commit('updateGlobalShare', { wx: { path }, poster: { path } });
    }
  });

  subscriber('addCart', (orderData) => {
    const { num = 1, messages = [], sku = {}, goodsAlias } = orderData;
    const { price, id, properties = [] } = sku;
    const errorMessgaes = [];

    let { goodsId } = orderData;

    if (goodsAlias) {
      if (goodsAlias !== $store.state.goods.alias) {
        return Promise.reject(new Error('仅支持添加当前商品到购物车'));
      }
      goodsId = $store.state.goods.id;
    }

    if (goodsId !== $store.state.goods.id) {
      return Promise.reject(new Error('仅支持添加当前商品到购物车'));
    }

    !goodsId && errorMessgaes.push('goodsId 和 goodsAlias 必须传一个');
    !id && errorMessgaes.push('sku.id 必须');
    !Array.isArray(properties) &&
      errorMessgaes.push('sku.properties 必须是一个数组');
    !Array.isArray(messages) && errorMessgaes.push('messages 必须是一个数组');

    if (errorMessgaes.length) {
      return Promise.reject(errorMessgaes);
    }

    return $store.dispatch('handleAddCart', {
      detail: {
        goodsId,
        selectedNum: num,
        cartMessages: messages,
        selectedSkuComb: {
          price,
          id,
          properties,
        },
      },
    });
  });
}
