// function watchState(state, key, defaultValue, watcherHook) {
//   const keys = key.split('.');
//   key = keys.pop();

//   let next = state;
//   let newKey = key;

//   while (keys.length) {
//     const key = keys.shift();
//     if (next[key] && typeof next[key] === 'object') {
//       next = next[key];
//     } else {
//       newKey = key;
//       break;
//     }
//   }

//   const newObj = {};
//   let nextObj = newObj;
//   while (keys.length) {
//     const key = keys.shift();
//     nextObj = nextObj[key] = {};
//     if (!key) break;
//   }

//   newObj[key] = defaultValue;

//   const val = { ...next, [newKey]: newObj };
//   next = val;

//   watcherHook.call(defaultValue, (value) => {
//     set(state, key, value);
//   });
//   state = { ...state };
// }
export default function setGoodsExtensionState(pages) {
  const { $store, hooks } = pages;
  $store.state.saas = {
    showVirtualTicketIntro: true,
    submitBlock: {
      showGoodsBottom: true,
    },
    shopBlock: {
      showOldShopBadge: true,
      showMultiStoreInfo: true,
    },
    baseInfoBlock: {
      showPrice: true,
      showShareIcon: true,
      showFollowShopIcon: true,
      showGuaranteeBar: true,
      showPriceBlock: true,
      showActivityBanner: true,
    },
    detailBlock: {
      showTab: true,
    },
    groupBlock: {
      showServiceBar: true,
      serviceDescList: [],
    },
    activityBlock: {
      showDepositPresalInfo: true,
    },
    promotionBlock: {
      showPackageBuy: true,
      showPromotionList: true,
    },
    showImage: true
  };

  hooks.showVirtualTicketIntro.call(true, (value) => {
    $store.state.saas.showVirtualTicketIntro = value;
  });
  hooks.showGoodsBottom.call(true, (value) => {
    $store.state.saas.submitBlock.showGoodsBottom = value;
  });
  hooks.showPldShopBadge.call(true, (value) => {
    $store.state.saas.shopBlock.showOldShopBadge = value;
  });
  hooks.showMultiStoreInfo.call(true, (value) => {
    $store.state.saas.shopBlock.showMultiStoreInfo = value;
  });
  hooks.showPrice.call(true, (value) => {
    $store.state.saas.baseInfoBlock.showPrice = value;
  });
  hooks.showPriceBlock.call(true, (value) => {
    $store.state.saas.baseInfoBlock.showPriceBlock = value;
  });
  hooks.showActivityBanner.call(true, (value) => {
    $store.state.saas.baseInfoBlock.showActivityBanner = value;
  });
  hooks.showShareIcon.call(true, (value) => {
    $store.state.saas.baseInfoBlock.showShareIcon = value;
  });
  hooks.showFollowShopIcon.call(true, (value) => {
    $store.state.saas.baseInfoBlock.showFollowShopIcon = value;
  });
  hooks.showGuaranteeBar.call(true, (value) => {
    $store.state.saas.baseInfoBlock.showGuaranteeBar = value;
  });
  hooks.showTab.call(true, (value) => {
    $store.state.saas.detailBlock.showTab = value;
  });
  hooks.showServiceBar.call(true, (value) => {
    $store.state.saas.groupBlock.showServiceBar = value;
  });
  hooks.diyServiceDescList.call([], (value) => {
    $store.state.saas.groupBlock.serviceDescList = value;
  });
  hooks.showDepositPresalInfo.call(true, (value) => {
    $store.state.saas.activityBlock.showDepositPresalInfo = value;
  });

  hooks.showPackageBuy.call(true, (value) => {
    $store.state.saas.promotionBlock.showPackageBuy = value;
  });
  hooks.showPromotionList.call(true, (value) => {
    $store.state.saas.promotionBlock.showPromotionList = value;
  });

  hooks.showImage.call(true, (value) => {
    $store.state.saas.showImage = value;
  })

  // watchState(saas, 'saas.showVirtualTicketIntro', true, hooks.showVirtualTicketIntro);
  // watchState(saas, 'saas.submitBlock.showGoodsBottom', true, hooks.showGoodsBottom);
  // watchState(saas, 'saas.shopBlock.showOldShopBadge', true, hooks.showPldShopBadge);
  // watchState(saas, 'saas.shopBlock.showMultiStoreInfo', true, hooks.showMultiStoreInfo);
  // watchState(saas, 'saas.baseInfoBlock.showPrice', true, hooks.showPrice);
  // watchState(saas, 'saas.baseInfoBlock.showShareIcon', true, hooks.showShareIcon);
  // watchState(saas, 'saas.baseInfoBlock.showFollowShopIcon', true, hooks.showFollowShopIcon);
  // watchState(saas, 'saas.baseInfoBlock.showGuaranteeBar', true, hooks.showGuaranteeBar);
  // watchState(saas, 'saas.detailBlock.showTab', true, hooks.showTab);
}
