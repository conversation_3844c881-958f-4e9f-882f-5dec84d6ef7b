/**
 * ReactiveHook
 * 具备响应式的 Hook，建立在 Vue 依赖搜集的基础上。
 * 意味着 hook 中依赖的响应式数据的变化能够被触发 hook 的地方感知。
 * 该 hook 必须有一个返回值。
 *
 * 处理流程：
 * 1. tap hook => call hook
 * 2. tap hook => call hook = after time => tap hook => call hook
 * 3. tap hook => call hook = reactive => call hook
 */
import { Hook } from './hook';

export class ReactiveHook extends Hook {
  subs = [];

  get(value) {
    Object.keys(this.subs).forEach((key) => {
      const subscriber = this.subs[key];

      // 如果注册的钩子函数是一个使用 callback 类型的，则
      if (subscriber.isEmitter) {
        value = subscriber.isCommited ? subscriber.value : value;
        return;
      }
      // 在注册的钩子函数内部使用的是事件，而非响应式数据的情况下，可以通过第二个参数，在事件发生时返回其值
      const result = subscriber.getter(value, (val) => {
        subscriber.value = val;
        subscriber.isCommited = true;
        subscriber.isEmitter = true;
        this.emitter();
      });

      // 如果调用注册的钩子函数，返回了一个 undefined，将被认为是一个事件类型
      if (result === undefined) {
        subscriber.isEmitter = true;
        value = subscriber.value === undefined ? value : subscriber.value;
        return;
      }

      value = result;
    });
    return value;
  }

  // 通过使用 flag 改变的机制来触发重新计算而不直接在值变化时计算后改变其值
  // 是因为需要保证每次计算的参数都是外部实时的，而不能内部缓存。
  emitter() {
    this.flag = !this.flag;
  }

  call(value, setValue) {
    Object.defineProperty(this, 'flag', {
      set() {
        setValue(this.get(value));
      }
    });
    this.emitter();
  }

  tap(moduleName, getter) {
    super.tap(moduleName, getter);
    this.emitter();
  }

  tapAsync() {
    throw new Error('ReactiveHook 不支持 tapAsync 方法');
  }
}
