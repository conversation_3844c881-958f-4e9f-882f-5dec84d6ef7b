export class Hook {
  subs = {};

  isHook = true;

  get(value) {
    Object.keys(this.subs).forEach((key) => {
      const { getter } = this.subs[key];
      value = getter(value);
    });
    return value;
  }

  call(value) {
    return this.get(value);
  }

  tap(moduleName, getter) {
    this.subs[moduleName] = {
      moduleName,
      getter
    };
  }

  tapAsync() {
    throw new Error('请用 promise 来实现异步事件');
    // this.subs[moduleName] = {
    //   moduleName,
    //   getter,
    //   async: true,
    // };
  }
}
