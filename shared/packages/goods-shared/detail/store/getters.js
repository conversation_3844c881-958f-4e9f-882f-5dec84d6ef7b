import { get, args } from '@goods/utils';
import { getUrl, HOME, CART } from '@goods/common/page-path';

const getters = {
  kdtId(state) {
    return get(state, 'shop.kdtId', 0);
  },

  shopConfig(state) {
    return get(state, 'shopConfig', {});
  },

  alias(state) {
    return get(state, 'goods.alias', '');
  },

  platform(state) {
    return get(state, 'env.platform', '');
  },

  isSupportWish() {
    return false;
  },

  version(state) {
    return get(state, 'env.version', '');
  },

  bookKey(state) {
    return get(state, 'buyConfig.bookKey', '');
  },

  // 是否拼团
  isGroupOn() {
    return false;
  },

  isGift() {
    return false;
  },

  // 是否阶梯团
  isLadderGroupOn() {
    return false;
  },

  // 是否是降价拍
  isAuction() {
    return false;
  },

  // 是否是赠品
  isPresent() {
    return false;
  },

  allGrouponUrl() {
    return '';
  },

  skuId(state, getters) {
    const { skuData } = getters;
    const { skuExtraData } = state;

    if (skuData.noneSku) {
      return skuData.collectionId;
    }

    if (skuExtraData.selectedSkuComb) {
      return skuExtraData.selectedSkuComb.id;
    }

    return null;
  },

  customStock() {
    return false;
  },

  imData(state, getters) {
    const {
      goods: { title, alias, price },
      shop: { kdtId },
      multistore: { id, name },
    } = state;
    const { originGoodsPictures } = getters;

    // im需要用到的商品主图，因为im的图片发出去之后有可能在各种浏览器打开，所以图片不能转webp的
    const imgs = originGoodsPictures.map((item) => item.url).slice(0, 3);

    const imData = {
      kdt_id: kdtId,
      source: 'goods',
      endpoint: 'h5',
      detail: {
        name: title,
        alias,
        price,
        imgs,
      },
    };
    if (id) {
      imData.site_id = id;
      imData.site_name = name;
    }

    return imData;
  },

  // 店铺首页
  homepageUrl(state, getters) {
    return getUrl(HOME, { kdt_id: getters.kdtId });
  },

  // 购物车地址
  cartUrl(state, getters) {
    const query = { kdt_id: getters.kdtId };
    if (state.env.isAllCart) {
      query.cart_type = 'all';
    }

    return getUrl(CART, query);
  },

  // 原始图片
  originGoodsPictures(state) {
    const {
      picture: { pictures },
    } = state;

    return pictures || [];
  },

  // 用于活动页面重载
  currentActivity(state, getters) {
    if (getters.isTimelimitedDiscount) {
      return getters.timelimitedDiscountActivity;
    }
    if (getters.isPresale) {
      return getters.presaleActivity;
    }
    return {};
  },

  // 折扣优惠活动，会员折扣、扫码优惠
  discountActivity(state) {
    const discount = get(state, 'orderActivity.discount' || null);
    return discount;
  },

  isGoodsScan(state, getters) {
    return (
      getters.discountActivity && getters.discountActivity.type === 'goodsScan'
    );
  },

  // 查看商品是否参加了活动，用于划线价逻辑等
  hasCurrentActivity(state, getters) {
    return (
      getters.currentActivity && Object.keys(getters.currentActivity).length > 0
    );
  },

  // 预售倒计时
  // 虚函数，store/modules里重写
  presaleCountdown() {
    return null;
  },

  // 限时折扣倒计时
  // 虚函数，store/modules里重写
  timelimitedDiscountCountdown() {
    return null;
  },

  // 定时开售倒计时
  waitToSoldCountdown(state) {
    const { serverDeltaTime = 0, serverTimestamp = 0 } = state;
    const { waitToSold } = state.goodsActivity;
    if (
      waitToSold &&
      !(serverTimestamp && +waitToSold.startSoldTime < serverTimestamp)
    ) {
      return {
        desc: '距开售仅剩',
        end: +waitToSold.startSoldTime - serverDeltaTime,
      };
    }
    return null;
  },

  // activity-banner / goods-info会用到
  // 倒计时模块
  countdown(state, getters) {
    const { timelimitedDiscountCountdown, presaleCountdown } = getters;
    if (timelimitedDiscountCountdown) {
      return timelimitedDiscountCountdown;
    }
    if (presaleCountdown) {
      return presaleCountdown;
    }
    return null;
  },

  // 剩余商品进度条百分数
  leftStockNumProgress(state) {
    const stockNum = get(state, 'goods.stockNum', 0);
    const totalActivityStockNum = Math.max(
      get(state, 'originActivitySku.totalActivityStockNum', 0),
      stockNum
    );
    const progressNum =
      100 -
      Math.round(
        ((totalActivityStockNum - stockNum) * 100) / totalActivityStockNum
      );
    return +progressNum;
  },

  // Sku相关，多页面逻辑复用，且可能逻辑追加
  // 数据
  skuData(state, getters) {
    const sku = state.skuData || {};
    // 如果定金实物预售
    if (getters.isDepositPresale && !getters.isDepositPresaleOver) {
      return getters.depositPresaleSkuData;
    }

    if (getters.isFCode) {
      return state.originActivitySku;
    }

    return sku;
  },

  // 禁止点击购买按钮原因，如预览等
  forbidClickBtnReason(state) {
    const {
      shop,
      fetchSuccess,
      // user: { isAdmin },
      // env: { isMobile },
    } = state;
    const { isHqShopPreview = false } = shop;
    if (!fetchSuccess) {
      return {
        message: '抢购人数太多\n请等待',
        autoReload: 5,
      };
    }
    if (isHqShopPreview) {
      return {
        message: '总店预览商品不支持下单购买',
        autoReload: 0,
      };
    }
    // 商家需求，预览商品支持购买，便于调试
    // if (isAdmin && !isMobile) {
    //   return '预览不支持进行购买，实际效果请在手机上进行。';
    // }
    return null;
  },

  // 网点是否售罄标识 显示规格栏和运费栏都要使用
  isMultiStoreSoldout(state, getters) {
    const { stockNum = 0 } = state.goods;

    return getters.isMultiStore && +stockNum === 0;
  },

  // 是否是多网点
  isMultiStore(state) {
    return get(state, 'multistore.id', 0) > 0;
  },

  // 推荐的多网点列表
  // TODO 检查这里面在干什么
  // multistore-block / submit-block 复用
  availableStores(state) {
    const salableStores = state.multistore.salableStores || [];
    return salableStores;
  },

  // 是否展示可用网点，仅用于底部初始化浮层
  showAvailableStores(state) {
    return (
      state.goods.stockNum === 0 && state.multistore.salableStores.length > 0
    );
  },

  // 叠加判断是否展示可用网点，一些营销活动为true
  hideAvailableStores() {
    return false;
  },

  // 是否支持价格日历
  isPriceCalendar(state) {
    const { goodsActivity = {} } = state;
    return (
      goodsActivity.virtualTicket &&
      goodsActivity.virtualTicket.validityType === 3
    );
  },

  isVirtualTicket(state) {
    return state.goods.isVirtualTicket;
  },

  showVirtualTicketIntro(state) {
    return state.goods.isVirtualTicket && state.saas.showVirtualTicketIntro;
  },

  // 是否是普通版商品详情
  isNormalTplStyle() {
    // 小程序内，不支持商品详情极速版
    return true;
    // return state.goodsDetail.tplStyle === 0;
  },

  // 是否售罄
  isSellOut(state) {
    return state.goods.stockNum <= 0;
  },

  cartText(state) {
    const { showDefaultCartText, cartText } = state.shopConfig;
    return showDefaultCartText ? '购物车' : cartText;
  },
  // 链接上需要带ump信息才能展示营销信息，如秒杀、砍价0元购、0元抽奖团，现在主要用于商品属性价格计算
  activityInfoForPrice() {
    return {};
  },

  // 是否是内购活动, 会在 internal-purchase getters 中重写
  isInSourcingFission() {
    return false;
  },

  // 是否是新品发售活动, 会在 product-launch 页面重写
  isProductLaunch() {
    return false;
  },

  concatInfo(state, getters) {
    const { goods, multistore, picture } = state;
    const sourceParam = {
      kdt_id: getters.kdtId,
      source: 'goods', // 写死'goods'
      endpoint: 'wx', // 渠道，wap端写死'h5'
      detail: JSON.stringify({
        alias: goods.alias,
        name: goods.title, // 商品name
        price: goods.price || '',
        originPrice: goods.oldPrice || '',
        imgs: (picture.pictures || []).map((item) => item.url).slice(0, 2),
      }),
    };

    if (multistore.id) {
      sourceParam.site_id = multistore.id; // 网点id
    }

    const path = args.add(goods.path, {
      imFrom: 'GOODS',
      alias: goods.alias,
      price: goods.price,
      originPrice: goods.oldPrice,
    });

    return {
      sourceParam: JSON.stringify(sourceParam),
      title: goods.title,
      picture: goods.picture,
      path,
    };
  },

  // 分销员赚字使用
  cubeGoodsInfo(state, getters) {
    const { salesmanActivityInfo = {}, parsedActivities = {} } = state.goods;
    const { salesmanSku = {} } = state.salesman || {};
    const { activityInfo = {} } = state;
    const goodsActivityInfo = {
      activityId: activityInfo.activityId || salesmanActivityInfo.activityId,
      activityType:
        salesmanActivityInfo.umpType ||
        salesmanActivityInfo.activityType ||
        activityInfo.umpActivityTypeq ||
        activityInfo.activityType ||
        (parsedActivities.discount || {}).type, // 会员价格
      activityAlias:
        activityInfo.activityAlias || salesmanActivityInfo.umpAlias,
    };
    return {
      ...state.goods,
      goodsActivityInfo,
      goodsMainPictures: getters.originGoodsPictures,
      goodsPrice: {
        maxPrice: salesmanSku.maxPrice,
        minPrice: salesmanSku.minPrice,
        maxOriginPrice: state.goods.oldMaxPrice,
        minOriginPrice: state.goods.oldMinPrice,
      },
      goodsSkuData: salesmanSku
    };
  },
};

export default getters;
