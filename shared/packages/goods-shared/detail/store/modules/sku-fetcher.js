/**
 * 异步获取sku、价格日历等
 */
import { get, moneyFormat } from '@goods/utils';
import { SKU_SCENE, SKU_SCENE_PREVIEW } from '@goods/common/config';

const actions = {
  fetchSkuData({ state, dispatch }) {
    const skuScene = get(state, 'skuConfig.skuScene', '');
    let next = Promise.resolve();
    if (
      skuScene === SKU_SCENE.NORMAL_BUY ||
      skuScene === SKU_SCENE.ADD_CART ||
      skuScene === SKU_SCENE.SEL_SKU ||
      skuScene === SKU_SCENE.GIFT_BUY ||
      skuScene === SKU_SCENE.ADD_WISH ||
      SKU_SCENE_PREVIEW.has(skuScene) /* 预览sku获取普通sku数据 */
    ) {
      next = dispatch('fetchNormalSkuData');
    } else if (skuScene === SKU_SCENE.POINTS_BUY) {
      next = dispatch('fetchPointsSkuData');
    } else if (skuScene === SKU_SCENE.ACT_BUY) {
      next = dispatch('fetchActivitySkuData');
    }
    // return next.then(() => dispatch('initSelectSku'));
    return next.then(() => dispatch('updateSelectedSku', state.skuExtraData));
  },

  fetchNormalSkuData({ state, commit }) {
    commit('updateSkuLimit', state.goods.limit);
    commit('updateSkuData', state.originNormalSku);
    return Promise.resolve();
  },

  fetchActivitySkuData({ state, getters, commit }) {
    let quotaLimit = {
      ...state.goods.limit,
    };
    // 赠品不限购
    if (getters.isPresent) {
      quotaLimit = {
        quota: 0,
        quotaUsed: 0,
        quotaCycle: 0,
      };
    } else {
      const { limit: activityLimit } = getters.currentActivity;
      // 活动限购且商品限购 取最小值；
      if (
        activityLimit &&
        (activityLimit.quota > 0 || activityLimit.singleQuota > 0) &&
        quotaLimit &&
        (quotaLimit.quota > 0 || quotaLimit.singleQuota > 0)
      ) {
        const _quota = Math.min(activityLimit.quota, quotaLimit.quota);
        activityLimit.quota = _quota;
        quotaLimit = {
          ...quotaLimit,
          ...activityLimit,
        };
      }
      // 如果仅有活动限购，取活动的限购；
      if (
        activityLimit &&
        (activityLimit.quota > 0 || activityLimit.singleQuota > 0)
      ) {
        // 如果活动限购
        quotaLimit = {
          ...quotaLimit,
          ...activityLimit,
        };
      }
    }
    commit('updateSkuLimit', quotaLimit);
    commit('updateSkuData', {
      ...state.originNormalSku,
      ...state.originActivitySku,
    });
    return Promise.resolve();
  },

  fetchPointsSkuData({ state, getters, commit }) {
    commit('updateSkuLimit', getters.pointsActivity.limit);
    commit('updateSkuData', {
      ...state.originNormalSku,
      ...state.originPointsSku,
    });
    return Promise.resolve();
  },

  /**
   * 默认选中 sku 暂时不使用该方法
   * @param {*} param vue store
   */
  initSelectSku({ state, getters, dispatch, commit }) {
    const skuData = getters.skuData || {};
    const { tree: skuTree = [], list: skuList = [] } = skuData || {};

    let initialSkuId = 0;
    const selectedSku = {};
    let skuComb = null;

    skuTree.forEach((item) => {
      const v = get(item, 'v', []);
      let valueId = null;
      // 只有一个规格或者电子卡券默认选中一组完整sku
      if (v.length === 1 || state.goods.isVirtualTicket) {
        valueId = item.v[0].id;
        selectedSku[item.kS] = valueId;
      }
    });

    const keys = Object.keys(selectedSku);
    const skuCombs = skuList.filter((item) => {
      return (
        keys.filter((key) => selectedSku[key] && selectedSku[key] === item[key])
          .length === keys.length
      );
    });

    if (skuCombs.length) {
      skuComb = skuCombs[0];
      initialSkuId = skuComb.id;
    }

    commit('setSkuExtraData', {
      initialSkuId,
      initialSku: selectedSku,
    });

    return dispatch('updateSelectedSku', {
      selectedSku,
      selectedSkuComb: skuComb,
    });
  },

  // 这个方法是fetch sku之后得到的
  // 用于检查默认选择的sku
  updateSelectedSku(
    { state, getters, dispatch, commit },
    selectedSkuData = {}
  ) {
    // 这个数据和H5不一致，selectedSkuComb 是一个对象
    if (
      selectedSkuData.selectedSkuComb &&
      !Object.keys(selectedSkuData.selectedSkuComb).length
    ) {
      selectedSkuData = {
        ...selectedSkuData,
        selectedSkuComb: null,
      };
    }
    const { selectedSkuComb = null, selectedSku = {} } = selectedSkuData;
    const skuTree = get(getters, 'skuData.tree', []);
    const treeMap = skuTree.reduce(
      (prev, curr) => ({
        ...prev,
        [curr.kS]: curr,
      }),
      {}
    );
    let selectedSkuTree = [];
    const selectedSkuKeys = Object.keys(selectedSku);

    selectedSkuKeys.forEach((kS) => {
      const treeVal = treeMap[kS] || null;
      if (!treeVal) {
        return;
      }
      if (selectedSku[kS]) {
        const id = selectedSku[kS];
        let value = null;

        for (let i = 0; i < treeVal.v.length; i++) {
          if (treeVal.v[i].id === id) {
            value = treeVal.v[i];
            break;
          }
        }
        selectedSkuTree.push({
          id: treeVal.kId,
          name: treeVal.k,
          value,
        });
      }
    });
    // 如果初始化没有sku选择
    if (!selectedSku || selectedSkuKeys.length === 0) {
      selectedSkuTree = skuTree.map((item) => ({
        id: item.kId,
        name: item.k,
        value: null,
      }));
    }

    commit('setSkuExtraData', {
      initialSkuId: 0, // 使用一个虚拟的数据方便统一取值
      selectedSku,
      // initialSku: selectedSku,
      selectedSkuTree,
      selectedSkuComb,
    });

    let next = Promise.resolve();
    /**
     * 如果展示了日历
     */
    if (getters.isPriceCalendar) {
      // 清除以选择的日历
      commit('clearSelectedCalendar');
      // 没有选中的情况下使用 mock 的数据
      const hasSku = !get(getters, 'skuData.noneSku', false);
      if ((!selectedSkuComb || !selectedSkuComb.id) && hasSku) {
        next = dispatch('mockDateList');
      } else if (getters.isVirtualTicket) {
        next = dispatch(
          'fetchVirtualDateList',
          hasSku ? selectedSkuComb.id : getters.skuData.collectionId
        ); /* 电子卡券价格日历 */
      }
    }

    dispatch('startCalculate');

    // 分期购买
    if (getters.isInstallment) {
      dispatch('fetchInstallentPrice', selectedSkuComb);
    }

    return next;
  },

  // 暂时不考虑价格日历
  updateSelectedProp(
    { state, getters, dispatch, commit },
    selectedPropData = {}
  ) {
    const { selectedSkuComb = null, selectedProp = {} } = selectedPropData;
    const { skuData = {} } = getters;
    const itemSalePropList = get(state, 'goods.itemSalePropList', []);
    // 特殊情况，需要补充一些信息，以展示正常
    if (selectedSkuComb && skuData.noneSku) {
      selectedSkuComb.oldPrice = skuData.oldPrice;
      selectedSkuComb.pointsPrice = skuData.pointsPrice;
    }

    const selectedPropList = [];
    itemSalePropList.forEach((prop) => {
      let value = [];
      if (selectedProp[prop.kId] && selectedProp[prop.kId].length > 0) {
        value = (prop.v || []).filter(
          (v) => selectedProp[prop.kId].indexOf(v.id) > -1
        );
      }
      selectedPropList.push({
        id: prop.kId,
        name: prop.k,
        value,
      });
    });

    commit('setSkuExtraData', {
      selectedProp,
      selectedPropList,
      selectedSkuComb,
    });

    dispatch('startCalculate');

    // 分期购买
    if (getters.isInstallment) {
      dispatch('fetchInstallentPrice', selectedSkuComb);
    }
  },
  /**
   * mock 时间数据
   */
  mockDateList({ state, commit }) {
    const now = new Date();
    const month = now.getMonth();
    const year = now.getFullYear();
    const minPrice = '¥' + moneyFormat(state.skuData.minPrice, true, false);
    const { initialSkuId } = state.skuExtraData;
    /**
     * 防止无意义的多次 mock 数据
     */
    if (state.priceCalendarData[initialSkuId]) {
      return commit('updateCalendarActiveSkuId', initialSkuId);
    }

    const calendarMockData = {
      months: [],
      monthDataList: [],
    };

    const { months, monthDataList } = calendarMockData;

    /**
     * mock 四个月的数据
     */
    for (let index = 0; index < 4; index++) {
      let mockMonth = month + index + 1;
      let mockYear = year;

      if (mockMonth > 12) {
        mockMonth -= 12;
        mockYear += 1;
      }

      const monthDayCount = new Date(mockYear, mockMonth, 0).getDate();

      months.push({
        name: mockMonth,
        year: mockYear,
        price: minPrice,
        label: `${minPrice}起`,
      });

      const days = [];
      monthDataList.push({
        year: mockYear,
        month: mockMonth,
        days,
      });

      for (let date = 1; date <= monthDayCount; date++) {
        days.push({
          date,
          isEnable: new Date(mockYear, mockMonth - 1, date, 23, 59, 59) >= now,
          footer: minPrice,
          id: `${mockYear}-${mockMonth}-${date}`,
        });
      }
    }

    commit('updateCalendarData', {
      skuId: initialSkuId,
      calendarData: calendarMockData,
    });
  },
};

let lastSkuData = null;

const mutations = {
  // 初始化sku数据
  updateSkuData(state, sku = {}) {
    if (lastSkuData === sku) return;

    lastSkuData = sku;

    state.skuData = {
      ...sku,
    };
  },

  // 更新限购信息
  updateSkuLimit(state, limit = {}) {
    state.limit = {
      ...state.limit,
      ...limit,
    };
  },

  setCouponDisabled(state, payload = {}) {
    // 更新最佳券列表领取状态
    function setDisabled(coupon) {
      if (coupon.id === payload.id) {
        coupon.disabled = true;
      }
      return coupon;
    }
    const COUPON_TYPE = state.businessConfigs.couponType || {};
    if (payload.type === COUPON_TYPE.OPTIMAL) {
      if (state.optimalCouponList && state.optimalCouponList.length) {
        state.optimalCouponList = state.optimalCouponList.map(setDisabled);
      }
      // 更新优惠券列表状态
    } else if (payload.type === COUPON_TYPE.UMP) {
      state.umpCouponList = state.umpCouponList.map(setDisabled);
    }
  },
};

export default { actions, mutations };
