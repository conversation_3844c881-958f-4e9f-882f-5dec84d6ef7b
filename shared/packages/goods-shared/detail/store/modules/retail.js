/**
 * 零售相关的逻辑
 */
import api from '@goods/utils/api';

const state = {
  // 零售店铺信息
  retail: {
    // 是否展示零售门店标签
    show: false,
    // 零售门店地址
    address: '',
  },
};

const getters = {
  // 是否是零售店铺
  isRetailShop(state) {
    return state.shop.shopType === 7;
  },
  // 零售店铺才会有
  retailAddressContent(state) {
    return state.retail.address || '';
  },
};

const actions = {
  // 获取零售门店信息
  getRetailShopInfo({ getters, commit }) {
    api
      .get({
        url: '/v2/showcase/goods/retailSellingShops.json',
        data: {
          alias: getters.alias,
        },
      })
      .then(data => {
        if (data.length > 0) {
          commit('getRetailShopInfoSuccess', data[0]);
        }
      })
      .catch(err => {
        //
      });
  },
};

const mutations = {
  // 获取零售店铺信息
  getRetailShopInfoSuccess(state, data) {
    state.retail = {
      show: data.stock_num > 0,
      address: data.address,
    };
  },
};

export default { state, getters, actions, mutations };
