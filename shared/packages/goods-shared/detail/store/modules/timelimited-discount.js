import { get, moneyFormat, moment } from '@goods/utils';

const getters = {
  timelimitedDiscountActivity(state) {
    return get(state, 'orderActivity.timelimitedDiscount', {});
  },

  isTimelimitedDiscount(state) {
    const activity = get(state, 'orderActivity.timelimitedDiscount', null);
    return !!activity;
  },

  isSupportWish(state, getters) {
    return getters.isTimelimitedDiscount;
  },
  isTimelimitedDiscountNotStart(state, getters) {
    const activity = getters.timelimitedDiscountActivity;
    return getters.isTimelimitedDiscount && !activity.isStarted;
  },

  timelimitTimeDesc(state, getters) {
    const {
      discountType,
      discount,
      decrease,
      price,
      startTime
    } = getters.timelimitedDiscountActivity;

    let text = '';
    const discountMap = {
      1: `¥${price} `,
      2: `${(discount * 10).toFixed(1).replace(/\.0/, '')}折`,
      3: `减${moneyFormat(decrease, true, false)} `
    };

    text += discountMap[discountType] + '起 ';

    text += `${moment(startTime, 'MM.DD HH:mm:ss')} 开始`;
    return text;
  },

  // 限时折扣倒计时
  timelimitedDiscountCountdown(state, getters) {
    const activity = getters.timelimitedDiscountActivity;
    const { endAt, startAt, isStarted } = activity;
    if (!endAt) {
      return null;
    }

    if (!isStarted) {
      return {
        desc: '距开始仅剩',
        end: startAt
      };
    }
    return {
      desc: '距结束仅剩',
      end: endAt
    };
  }
};

export default { getters };
