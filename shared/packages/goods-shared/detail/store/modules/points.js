/**
 * 积分兑换，虽然有独立的activityId，但实际由template控制是否有积分兑换按钮，并且是可以和其他活动叠加的，所以不单独做页面
 */
import { get } from '@goods/utils';

const state = {};

const getters = {
  pointsActivity(state) {
    return get(state, 'marketActivity.points', {});
  },
  // 是否是积分兑换
  isPointsExchange(state) {
    return !!get(state, 'marketActivity.points', null);
  },

  showPointsButtonStatus(state) {
    return get(state, 'marketActivity.points.status', 0);
  },

  isSupportWish(state, getters) {
    return getters.isPointsExchange;
  },
  // 用于下单购买
  // 积分商品的活动标识
  pointsActivityInfo(state, getters) {
    const activity = getters.pointsActivity;
    return {
      // 活动alias
      activityAlias: '',
      // 活动id
      activityId: activity.activityId || 206404,
      // 活动类型
      activityType: 5
    };
  }
};

const actions = {};

const mutations = {};

export default { state, getters, actions, mutations };
