/* eslint-disable no-lonely-if */
/**
 * 预售
 */
import { get, formatPrice, moment, minBy, maxBy } from '@goods/utils';
import { getDepositPrice } from '../utils/helper';
import { SKU_SCENE } from '@goods/common/config';
import { getUrl, HELP_PRESALE } from '@goods/common/page-path';

// 发货时间类型
const ShipType = {
  // x天后发货
  after: 1,
  // x天内发货
  inner: 2,
};

// 预售周期类型
const PeriodType = {
  // 长期
  long: 1,
  // 短期
  short: 0,
};

// 0: 定时发货 1：付款几天【后】发货 2：付款几天【内】发货
const shipTimeTransform = ({
  payText = '付款',
  activity = {},
  isShipAfterPay,
  isShipInnerPay,
}) => {
  let text = '';
  const { shipDelayDays, shipBeforeDays, shipStartTime } = activity;
  if (isShipAfterPay) {
    text = `${payText}${shipDelayDays}天后发货`;
  } else if (isShipInnerPay) {
    text = `${payText}${shipBeforeDays}天内发货`;
  } else {
    text = `${moment(shipStartTime, 'MM.DD')}开始发货`;
  }
  return text;
};

const getters = {
  presaleActivity(state) {
    return get(state, 'orderActivity.presale', {});
  },

  isPresale(state) {
    const activity = get(state, 'orderActivity.presale', null);
    return !!activity;
  },

  // 0: 定时发货 1：付款几天后发货
  isShipAfterPay(state, getters) {
    const presale = getters.presaleActivity;
    return presale.shipType === ShipType.after;
  },

  isShipInnerPay(state, getters) {
    const presale = getters.presaleActivity;
    return presale.shipType === ShipType.inner;
  },

  // 0：全款预售 1：定金预售
  isDepositPresale(state, getters) {
    const presale = getters.presaleActivity;
    return presale.type === 1;
  },

  // 长期预售:period=1
  isPresaleLongPeriod(state, getters) {
    const presale = getters.presaleActivity;
    return +presale.period === PeriodType.long;
  },

  // 长期定金预售
  isDepositPresaleLongPeriod(state, getters) {
    return getters.isDepositPresale && getters.isPresaleLongPeriod;
  },

  // 定金预售已结束
  isDepositPresaleOver(state, getters) {
    const presale = getters.presaleActivity;

    // 如果是长期定金预售，活动不会结束
    if (getters.isDepositPresaleLongPeriod) {
      return false;
    }

    // 如果用户参加了助力的话，结束时间就变成尾款的结束时间
    if (!getters.isDepositPresaleHelpNotJoin && getters.isHelpDepositPresale) {
      return getters.isTailPayOver;
    }

    return presale.preEndTime < +new Date();
  },

  // 预售倒计时
  presaleCountdown(state, getters) {
    const presale = getters.presaleActivity;
    if (!presale) {
      return null;
    }

    // 定金预售 - 长期：一直展示活动中
    if (getters.isDepositPresaleLongPeriod) {
      return {
        desc: '活动进行中',
        end: '',
      };
    }

    const { preStartTime, preEndTime, payEndTime } = presale;
    let endTime = preEndTime;
    if (!preStartTime && !preEndTime) {
      return null;
    }

    // 如果参与了助力，那么结束时间就改成尾款结束时间
    if (!getters.isDepositPresaleHelpNotJoin && getters.isHelpDepositPresale) {
      endTime = payEndTime;
    }

    const { clientTimestamp: now, serverDeltaTime } = state;
    const startAt = preStartTime - serverDeltaTime;
    const endAt = endTime - serverDeltaTime;
    if (startAt > now) {
      return {
        desc: '距开始仅剩',
        end: startAt,
      };
    }
    if (endAt > now) {
      return {
        desc: '距结束仅剩',
        end: endAt,
      };
    }
    return {
      desc: '活动已结束',
      end: now,
    };
  },
  presaleTimeDesc(state, getters) {
    if (!getters.isPresale) {
      return '';
    }

    const { desc, subDesc } = getters.presaleDesc(true) || {};

    return `${desc} ${subDesc}`;
  },

  // 预售说明 - 预售拼团、预售秒杀 底部bottom会用到
  presaleDesc(state, getters) {
    return (isTimeDesc = false) => {
      if (!getters.isPresale) {
        return null;
      }
      const activity = getters.presaleActivity;
      const {
        isDepositPresaleHelpStart,
        isShipAfterPay,
        isShipInnerPay,
      } = getters;
      const { preEndTime, payStartTime } = activity;
      const shipModel = {
        activity,
        isShipAfterPay,
        isShipInnerPay,
      };

      let desc = '';
      let subDesc = '';
      // 0：全款预售 1：定金预售
      if (getters.isDepositPresale) {
        const shipTimeText = shipTimeTransform({
          payText: '尾款支付',
          ...shipModel,
        });
        let suffix = '预售结束';
        let endTime = preEndTime;

        // 助力膨胀 开始助力
        if (isDepositPresaleHelpStart) {
          suffix = '活动结束';
          endTime = payStartTime;
        }

        if (getters.isPresaleLongPeriod) {
          desc = shipTimeText;
        } else {
          desc = `${moment(endTime, 'MM.DD HH:mm:ss')}${suffix}${
            isTimeDesc ? ',' : ''
          }`;
          subDesc = shipTimeText;
        }
      } else {
        const shipTimeText = shipTimeTransform({
          payText: '付款',
          ...shipModel,
        });
        if (getters.isPresaleLongPeriod) {
          desc = shipTimeText;
        } else {
          if (activity.activityEnd) {
            desc = `${moment(activity.activityEnd, 'MM.DD HH:mm:ss')}活动结束${
              isTimeDesc ? ',' : ''
            }`;
            subDesc = shipTimeText;
          } else {
            // 老的全款预售没有结束时间，兼容不展示结束时间
            desc = shipTimeText;
          }
        }
      }

      return {
        desc,
        subDesc,
      };
    };
  },

  // 用于预售流程进度条
  presaleStatus(state, getters) {
    const {
      preStartTime,
      payStartTime,
      shipStartTime,
    } = getters.presaleActivity;

    const {
      isDepositPresaleHelpStart,
      isTailPayStart,
      isDepositPresaleHelpPayed,
    } = getters;
    const now = +new Date();

    if (isDepositPresaleHelpPayed) {
      return 4;
    }
    if (isTailPayStart) {
      return 3;
    }
    if (isDepositPresaleHelpStart) {
      return 2;
    }

    if (preStartTime < now) {
      return 1;
    }
    if (payStartTime < now) {
      return 2;
    }
    if (shipStartTime < now) {
      return 3;
    }
    return 0;
  },

  depositPrice(state, getters) {
    const presale = getters.presaleActivity;
    const { minDeposit, maxDeposit } = presale;

    if (maxDeposit === minDeposit) {
      return formatPrice(minDeposit);
    }
    return formatPrice({ min: minDeposit, max: maxDeposit }, ' - ');
  },

  // ￥xx 或者 ￥xx起
  depositShowPrice(state, getters) {
    const presale = getters.presaleActivity;
    const { minDeposit, maxDeposit } = presale;

    return `￥${formatPrice(minDeposit)}${
      maxDeposit !== minDeposit ? '起' : ''
    }`;
  },

  depositPaymentTime(state, getters) {
    const presale = getters.presaleActivity;
    const { payStartTime, payEndTime, payStartDay, payEndDay } = presale || {};
    let desc = '';

    const beginTimeStr = moment(payStartTime, 'YYYY.MM.DD HH:mm:ss');
    const endTimeStr = moment(payEndTime, 'YYYY.MM.DD HH:mm:ss');

    desc = `支付尾款: ${beginTimeStr} - ${endTimeStr}`;

    // 定金预售 - 长期
    if (getters.isDepositPresaleLongPeriod) {
      desc = `支付尾款: 定金支付${payStartDay}天后开始支付，${payEndDay}天内完成支付`;
    }
    return desc;
  },

  depositOfferMap(state, getters) {
    const presale = getters.presaleActivity;
    const { depositSkuList = [] } = presale;
    return depositSkuList.reduce(
      (prev, curr) => ({
        ...prev,
        [curr.skuId]: curr,
      }),
      {}
    );
  },

  // 可抵消价格范围
  presaleOffsetPriceRange(state, getters) {
    if (!getters.skuMinMaxOffer) return '';

    if (getters.skuMinMaxOffer) {
      const { offerMinOffset, offerMaxOffset } = getters.skuMinMaxOffer;
      const isHelpDepositPresale = getters.isHelpDepositPresale;
      const prefix = isHelpDepositPresale ? '最高可抵' : '可抵';

      if (offerMinOffset === offerMaxOffset || isHelpDepositPresale) {
        return `${prefix} ¥${formatPrice(offerMaxOffset)}`;
      }

      return `${prefix} ¥${formatPrice(offerMinOffset)} - ${formatPrice(
        offerMaxOffset
      )}`;
    }
  },

  // 计算出最大，最小可抵的值； skus不存在就返回null
  skuMinMaxOffer(state, getters) {
    const { depositSkuList: skus = [] } = getters.presaleActivity;

    if (skus.length > 0) {
      let offerMinOffset = 0;
      let offerMaxOffset = 0;

      // 助力膨胀数据类型： [{"deposit_offer":300,"help_offer":[300,600],"sku_id":1065478}]
      if (getters.isHelpDepositPresale) {
        const initValue = skus[0].depositOffer;

        offerMinOffset = skus.reduce((prev, currentItem) => {
          return Math.min(prev, Math.min(...currentItem.helpOffer));
        }, initValue);

        offerMaxOffset = skus.reduce((prev, currentItem) => {
          return Math.max(prev, Math.max(...currentItem.helpOffer));
        }, initValue);
      } else {
        offerMinOffset = minBy(skus, (items) => items.depositOffer)
          .depositOffer;
        offerMaxOffset = maxBy(skus, (items) => items.depositOffer)
          .depositOffer;
      }

      return {
        offerMinOffset,
        offerMaxOffset,
      };
    }

    return null;
  },

  presaleOffsetPriceMin(state, getters) {
    const { skuMinMaxOffer, isHelpDepositPresale } = getters;

    if (!skuMinMaxOffer) {
      if (isHelpDepositPresale) {
        return '定金膨胀';
      }

      return '定金预售';
    }

    const { offerMinOffset, offerMaxOffset } = skuMinMaxOffer;
    const prefix = isHelpDepositPresale ? '最高可抵' : '可抵';

    if (offerMinOffset === offerMaxOffset) {
      return `${prefix}¥${formatPrice(offerMinOffset)}`;
    }

    return `抵¥${formatPrice(offerMinOffset)}起`;
  },

  // 预售sku价格标签，用于诸如定金膨胀可抵
  presaleSkuPriceTag(state, getters) {
    const { isHelpDepositPresale } = getters;
    const presale = getters.presaleActivity;
    const { depositSkuList = [] } = presale;
    if (depositSkuList.length > 0) {
      // 助力膨胀或者初始定金膨胀
      const { selectedSkuComb } = state.skuExtraData;
      if (selectedSkuComb && selectedSkuComb.id) {
        const targetSku = getters.depositOfferMap[selectedSkuComb.id];

        let offset = 0;
        if (isHelpDepositPresale) {
          offset = Math.max(...targetSku.helpOffer) || 0;
        } else {
          offset = targetSku.depositOffer || 0;
        }

        const prefix = isHelpDepositPresale ? '最高可抵' : '可抵';

        if (offset > 0) {
          return `${prefix}￥${formatPrice(offset)}`;
        }
      }
      return getters.presaleOffsetPriceRange;
    }
    return '定金预售';
  },

  // 定金预售商品SKU信息
  depositPresaleSkuData(state, getters) {
    const sku = state.originNormalSku;
    // 定金实物预售
    const { presaleActivity } = getters;
    const depositRatio = presaleActivity.depositRatio / 100;
    const { minDeposit, maxDeposit } = presaleActivity;
    if (sku.list) {
      // 多sku的价格计算放到后端处理；后端提供了如下2个字段
      const { skuDeposit = null, deposit = null } = presaleActivity;

      let singlePrice = getDepositPrice(sku.price, depositRatio);

      // 无、单sku的定金选取定金
      if (deposit) {
        singlePrice = getDepositPrice(deposit);
      } else if (skuDeposit && skuDeposit.length) {
        singlePrice = getDepositPrice(skuDeposit[0].deposit);
      }

      return {
        ...sku,

        // 多sku，从skuDeposit中匹配到despoit；然后设置到price里；
        list: sku.list.map((item) => {
          let curSku = null;

          if (skuDeposit) {
            for (let i = 0; i < skuDeposit.length; i++) {
              if (skuDeposit[i].skuId === item.skuId) {
                curSku = skuDeposit[i];
                break;
              }
            }
          }

          const price = curSku
            ? getDepositPrice(curSku.deposit)
            : getDepositPrice(item.price, depositRatio);

          return {
            ...item,
            oldPrice: item.price,
            price,
          };
        }),
        oldPrice: sku.price,
        price: singlePrice,
        minPrice: minDeposit,
        maxPrice: maxDeposit,
      };
    }
    return { ...sku };
  },

  /* 预售定金膨胀 */
  isHelpDepositPresale(state, getters) {
    const depositType = get(getters, 'presaleActivity.depositType');
    return getters.isDepositPresale && depositType === 'help';
  },

  // 没参加助力活动
  isDepositPresaleHelpNotJoin(state, getters) {
    const joinActivityStatus = get(
      getters,
      'presaleActivity.helpDepositExpansion.joinActivityStatus',
      ''
    );
    return getters.isHelpDepositPresale && +joinActivityStatus === 0;
  },

  // 助力邀请开始状态joinActivityStatus: 0 未参与；1 开始助力 2 助力结束开始付尾款 3 参与完成
  isDepositPresaleHelpStart(state, getters) {
    const joinActivityStatus = get(
      getters,
      'presaleActivity.helpDepositExpansion.joinActivityStatus',
      ''
    );
    return getters.isHelpDepositPresale && +joinActivityStatus === 1;
  },

  // 开始付尾款
  isTailPayStart(state, getters) {
    const { payStartTime } = getters.presaleActivity;
    const joinActivityStatus = get(
      state,
      'presaleActivity.helpDepositExpansion.joinActivityStatus',
      ''
    );
    const isPaid =
      getters.isDepositPresaleHelpStart && payStartTime < +new Date();
    return isPaid || joinActivityStatus === 2;
  },

  // 尾款支付完成
  isDepositPresaleHelpPayed(state, getters) {
    const joinActivityStatus = get(
      getters,
      'presaleActivity.helpDepositExpansion.joinActivityStatus',
      ''
    );
    return getters.isHelpDepositPresale && joinActivityStatus === 3;
  },

  isTailPayOver(state, getters) {
    const { payEndTime } = getters.presaleActivity;
    return payEndTime && payEndTime < new Date();
  },

  presaleTitle(state, getters) {
    if (getters.isHelpDepositPresale) {
      return '定金膨胀流程';
    }

    return '预售流程';
  },

  helpDepositPresaleButtons(state, getters) {
    let bigButton = {
      text: '支付定金',
      primary: true,
    };

    const {
      isHelpDepositPresale,
      isDepositPresaleHelpNotJoin,
      isDepositPresaleHelpStart,
      isTailPayStart,
      isDepositPresaleHelpPayed,
      homepageUrl,
      isDepositPresaleOver,
      presaleActivity,
      kdtId,
    } = getters;

    if (isHelpDepositPresale) {
      const { helpDepositExpansion = {} } = presaleActivity;
      const { voucherId, activityId, orderNo } = helpDepositExpansion;

      // 如果已经生成订单，但是还没有支付的话，需要跳转到订单页
      if (isDepositPresaleHelpNotJoin && orderNo) {
        // ...
      } else if (isDepositPresaleHelpStart || isTailPayStart) {
        // 如果到了尾款时间就换个状态
        if (isTailPayStart) {
          bigButton.text = '支付尾款';
        } else {
          bigButton.text = '继续膨胀';
        }
        // 跳转到助力活动页
        bigButton.url = getUrl(HELP_PRESALE, {
          voucherId,
          activityId,
          kdt_id: kdtId,
        });
      } else if (isDepositPresaleHelpPayed) {
        bigButton.leftText = '';
        bigButton.leftSub = '';
        bigButton.text = '已完成购买，看看其他商品';
        bigButton.url = homepageUrl;
        bigButton.forbid = true;
      }
    }

    // 定金预售如果已结束，按钮要禁止
    if (isDepositPresaleOver) {
      bigButton.disabled = true;
      bigButton.text = '已结束';
      bigButton.forbid = true;
    }

    if (!bigButton.url && !bigButton.disabled) {
      const {
        accountUnionScene: ACCOUNT_UNION_SCENE = {},
      } = state.businessConfigs;
      bigButton = {
        ...bigButton,
        skuScene: SKU_SCENE.ACT_BUY,
        accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
      };
    }

    return [bigButton];
  },
};

export default { getters };
