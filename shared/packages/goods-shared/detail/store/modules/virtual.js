import { moment, moneyFormat, or, get } from '@goods/utils';
import api from '@goods/utils/api';
import { formatPriceCalendarData } from '../utils/parse';

export const state = {
  virtualTicket: null
};

export const getters = {
  ecardRules(state) {
    const {
      validityType,
      effectType,
      effectDelayTime,
      validityDays,
      startDate,
      endDate,
      isHolidaysAvailable,
      instructions
    } = state.goodsActivity.virtualTicket || {};

    const rules = [];

    const validityTypeMap = [
      '永久有效',
      `${validityDays}天内使用有效`,
      `${moment(startDate, 'YYYY年MM月DD日')} - ${moment(endDate, 'YYYY年MM月DD日')}内使用有效`,
      '在所选使用日期内当天有效'
    ];

    rules.push(validityTypeMap[validityType] || '');

    const effectTypeMap = ['立即生效', `${effectDelayTime}小时后生效`, '次日生效'];

    rules.push(effectTypeMap[effectType] ? `购买后${effectTypeMap[effectType]}` : '');

    rules.push(`节假日${isHolidaysAvailable === 0 ? '不' : ''}可用`);

    const { isSupport, type, interval } = state.refund;
    if (!isSupport) {
      rules.push('不支持申请退款');
    } else if (type === 0) {
      rules.push('未使用卡券随时可申请退款');
    } else if (type === 1) {
      const day = Math.floor(interval / 24 / 60 / 60 / 1000);
      const left = interval % (24 * 60 * 60 * 1000);
      const hour = Math.floor(left / 60 / 60 / 1000);
      const dayStr = day ? `${day}天` : '';
      const hourStr = hour ? `${hour}小时` : '';
      rules.push(`未使用卡券在过期前${dayStr + hourStr}随时可以申请退款`);
    }

    instructions && rules.push(instructions || '');

    return rules;
  },

  ecardInstructions(state, getters) {
    const rules = [...getters.ecardRules];
    const customRule = rules.pop();

    const instructions = rules.join('，');
    return customRule ? [instructions, `其他说明：${customRule}`] : [instructions];
  },

  dateListQuery(state) {
    const { goods, activityInfo, shop, goodsActivity } = state;
    const { virtualTicket } = goodsActivity;
    // 取四个月时间
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 4);
    endDate.setDate(0);
    const data = {
      alias: goods.alias,
      kdtId: shop.kdtId,
      activityType: activityInfo.activityType || 0,
      activityAlias: activityInfo.activityAlias,
      itemId: goods.id,
      beginDate: moment(new Date(), 'YYYY-MM-DD'),
      endDate: moment(endDate, 'YYYY-MM-DD')
    };

    if (virtualTicket && virtualTicket.itemPreOrderTime) {
      Object.assign(data, {
        itemPreOrderTime: virtualTicket.itemPreOrderTime
      });
    }

    return data;
  },

  // 优惠券商品
  virtualCoupon(state) {
    return get(state, 'goodsActivity.virtualCoupon', {});
  },

  // sku优惠券商品展示信息
  skuCouponExtraItem(state, getters) {
    const { selectedSkuComb = null } = state.skuExtraData;
    const couponList = get(getters, 'virtualCoupon.skuExtraModels', []);

    if (!selectedSkuComb) return null;
    const couponItem = couponList.filter((item) => item.skuId === selectedSkuComb.id)[0];

    return couponItem;
  }
};

export const actions = {
  fetchNearDateList({ commit, getters }) {
    return api
      .get({
        url: '/wscgoods/getNearDateList.json',
        data: getters.dateListQuery
      })
      .then((data = {}) => {
        let { nearlyFourDayMarketableDate = [] } = data;

        nearlyFourDayMarketableDate = nearlyFourDayMarketableDate.map((item) => {
          const date = new Date(item.stockDate);
          let month = date.getMonth() + 1;
          month = month > 9 ? month : '0' + month;
          const day = date.getDate();

          return {
            ...item,
            day: month + '-' + (day > 9 ? day : '0' + day),
            price: '¥' + moneyFormat(or(item.activityPrice, item.originPrice))
          };
        });

        commit('setPriceBarList', nearlyFourDayMarketableDate);
      });
  },

  fetchVirtualDateList({ state, commit, getters }, skuId) {
    const itemSkuId = skuId || state.skuExtraData.initialSkuId;
    if (state.priceCalendarData[itemSkuId]) {
      commit('updateCalendarActiveSkuId', itemSkuId);
      return state.priceCalendarData[itemSkuId];
    }

    const data = getters.dateListQuery;

    data.itemSkuId = itemSkuId;

    return api
      .get({
        url: '/wscshop/goods-api/list-date.json',
        data
      })
      .then((data) => {
        const calendarData = formatPriceCalendarData(data);

        commit('updateCalendarData', { skuId: itemSkuId, calendarData });

        return state.priceCalendarData[itemSkuId];
      });
  }
};

export const mutations = {
  setPriceBarList(state, priceBarList) {
    state.priceCalendarData.priceBarList = priceBarList;
  }
};

export default {
  state,
  getters,
  actions,
  mutations
};
