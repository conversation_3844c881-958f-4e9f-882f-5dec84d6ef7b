/**
 * 周期购数据
 */
import { get } from '@goods/utils';

const monthArr = [];
for (let i = 0; i < 33; i++) {
  monthArr.push(i);
}

const DELIVER_TIME_MAP = {
  0: ['每天送达', '工作日送达', '周末每天送达', '隔天送达'],
  1: ['', '每周一', '每周二', '每周三', '每周四', '每周五', '每周六', '每周日'],
  2: monthArr.map(index => (index === 32 ? '每月最后一天' : `每月${index}号`)),
};

const PERIOD_MAP = {
  0: '每天',
  1: '每周',
  2: '每月',
};

const state = {};

const getters = {
  periodbuyActivity(state) {
    return get(state, 'goodsActivity.periodbuy', {});
  },

  isPeriodbuy(state) {
    return !!get(state, 'goodsActivity.periodbuy', null);
  },

  // 是否已选周期购规格
  isPeriodbuyItemSelected(state, getters) {
    return typeof getters.periodbuyActivity.deliverTimeId === 'number';
  },

  // 周期购sku弹层  送达时间展示 list
  periodbuySkuExtraList(state, getters) {
    const { deliveryTimeList = [], period = 0 } = getters.periodbuyActivity;

    return deliveryTimeList.map(deliverTimeId => ({
      id: deliverTimeId,
      name: DELIVER_TIME_MAP[period][deliverTimeId],
    }));
  },

  // 周期总数
  periodbuyNum(state, getters) {
    const { selectedSkuComb = null } = state.skuExtraData;
    const periodSku = get(getters, 'periodbuyActivity.periodSku', []);

    if (periodSku.length === 1) {
      return periodSku[0].num;
    }

    const getPeriodbuyNum = skuComb => {
      if (!skuComb) {
        return '';
      }
      const skuExtras = periodSku.filter(item => item.skuId === skuComb.id)[0] || {};
      return skuExtras.num;
    };

    return getPeriodbuyNum(selectedSkuComb);
  },

  // 周期购送达时间
  periodbuyDeliverTimeStr(state, getters) {
    const { period: periodUnit } = getters.periodbuyActivity;
    const { periodbuyNum } = getters;
    if (periodbuyNum) {
      return PERIOD_MAP[periodUnit]
        ? `${PERIOD_MAP[periodUnit]}配送一次，共${periodbuyNum}次`
        : `共${periodbuyNum}次`;
    }
    return '';
  },
};

const actions = {
  selectPeriodbuySkuItem({ commit }, id) {
    commit('selectPeriodbuySkuItem', id);
  },
};

const mutations = {
  selectPeriodbuySkuItem(state, id) {
    state.goodsActivity.periodbuy.deliverTimeId = id;
  },
};

export default { state, getters, actions, mutations };
