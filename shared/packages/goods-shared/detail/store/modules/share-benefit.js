/**
 * 分享有礼活动
 */
import { get, args } from '@goods/utils';
import api from '@goods/utils/api';

const app = getApp();

const state = {
  shareBenefit: {
    shareTag: '', // 分享人标识
    isAuthorized: false // 是否已授权手机号
  }
};

const getters = {
  shareBenefitActivity(state) {
    return get(state, 'orderActivity.shareBenefit', {});
  },
  hasShareBenefitActivity(state) {
    return !!get(state, 'orderActivity.shareBenefit', null);
  }
};

const actions = {
  // 分享时获取分享人标志
  handleShareBenefitTag({ state, getters, commit }) {
    const { shareTag, isAuthorized } = get(state, 'shareBenefit', {});
    if (getters.hasShareBenefitActivity && !shareTag && isAuthorized) {
      const { activityAlias } = getters.shareBenefitActivity;
      api
        .get({
          url: '/wscgoods/activity-api/get-share-tag.json',
          data: {
            activityAlias,
            kdtId: app.getKdtId()
          }
        })
        .then((data) => {
          const { alias: shareTag } = data || {};
          commit('setShareTag', shareTag);

          const { wx } = state.shareData;
          const shareData = {
            wx: {
              path: args.add(wx.path, { shareTag })
            },
            poster: {
              scene: { shareTag }
            },
            copyInfo: {
              shareTag
            }
          };
          commit('updateGlobalShare', shareData);
        })
        .catch((err) => {
          console.error(err);
        });
    }
  }
};

const mutations = {
  setShareTag(state, data) {
    state.shareBenefit.shareTag = data;
  },

  setAuthorizedState(state, data) {
    state.shareBenefit.isAuthorized = data;
  }
};

export default { state, getters, actions, mutations };
