import { addSalesmanParams } from 'shared/utils/salesman-params-handler';
import { defaultEnterShopOptions } from 'common-api/multi-shop/multi-shop-redirect';
import { args } from '@goods/utils';
import api from '@goods/utils/api';
import logv3 from 'utils/log/logv3';

const app = getApp();

const state = {
  // 全局分享信息
  shareData: {
    // 微信分享信息，不建议直接使用，分享请使用 getters.shareWxData
    wx: {},
    // 海报分享信息
    poster: {},
    // 复制链接信息
    copyInfo: {},
  },
};

const getters = {
  shareWxData(state) {
    const {
      shareData: { wx },
      salesman,
    } = state;

    const wxShareData = { ...wx };

    // 统一收尾 sl 参数
    if (salesman && salesman.alias) {
      wxShareData.path = addSalesmanParams({
        url: wxShareData.path,
        sl: state.salesman.alias,
      });
    } else {
      // 非分销员分享添加自动进店参数
      wxShareData.path = args.add(wxShareData.path, defaultEnterShopOptions);
    }

    return wxShareData;
  },

  shareTimeLineWxData(state, getters) {
    const { shareWxData = {} } = getters;
    const { path = '' } = shareWxData;
    const query = path.slice(path.indexOf('?') + 1);

    return {
      ...shareWxData,
      query,
    };
  },

  // 分享海报参数
  sharePosterInfo(state) {
    const {
      shareData: { poster },
    } = state;

    // 分享添加自动进店标
    poster.scene = { ...poster.scene, ...defaultEnterShopOptions };

    return poster;
  },

  // 分享复制链接参数
  shareCopyInfo(state) {
    const {
      goods,
      salesman,
      shareData: { copyInfo },
    } = state;
    const { activityId, type, umpAlias, umpType } = args.getAll(goods.path);
    const params = {
      activityId,
      activityType: type === 'helpcut' ? 'helpCut' : type,
      oid: app.getOfflineId(),
      ump_alias: umpAlias,
      ump_type: umpType,
      sl: salesman.alias,
      shopAutoEnter: 1, // 分享自动进店标
      ...copyInfo,
    };

    // 修复复制链接dc_ps丢失的问题 https://jira.qima-inc.com/browse/ONLINE-587069?company=YOUZAN
    const dcPs = logv3.getDCPS();

    if (dcPs) {
      params.dc_ps = dcPs;
    }

    return params;
  },

  // 分销员生成小程序二维码自定义数据
  makeCodeCustomData(state, getters) {
    // 内购活动场景
    if (getters.isInSourcingFission && state.shareData.poster.scene) {
      return {
        path: state.shareData.poster.scene.page,
        ...getters.activityParams,
      };
    }
    return null;
  },
};

const actions = {
  // 分享前置方法，其他页面可重写
  beforeSetGlobalShare() {
    return Promise.resolve();
  },

  // 自定义微信分享内容，其他页面可重写
  // eslint-disable-next-line no-unused-vars
  beforeSetWxShare({ state }, shareData) {
    return shareData;
  },

  // 自定义海报分享内容，其他页面可重写
  beforeSetPosterShare() {
    return {};
  },

  // 自定义复制链接内容
  beforeSetCopyInfo() {
    return {};
  },

  // 海报服务获取分享信息
  setwxCardShare({ state, commit, dispatch }) {
    const {
      goods = {},
      shareData: {
        wx: { path },
      },
    } = state;

    const params = args.getAll(goods.path) || {};

    const offlineId = app.getOfflineId();

    if (offlineId) {
      params.offlineId = offlineId;
    }

    // params 中不一定有 商品 alias
    if (!params.alias) {
      params.alias = goods.alias;
    }
    params.timestamp = +new Date();
    api
      .get({
        url: '/wscgoods/poster/card/goods-v2',
        query: params,
      })
      .catch(() => {
        // 接口报错兜底
        return {
          shareTitle: goods.title,
          imgUrl: goods.picture,
        };
      })
      .then((result) => {
        const { shareTitle, alg = '', imgUrl, share_from = '' } = result || {};
        // 接口不报错 未返回图片兜底
        const shareData = {
          title: shareTitle || goods.title,
          imageUrl: imgUrl || goods.picture,
          path: args.add(path, { alg, share_from }),
        };
        dispatch('beforeSetGlobalShare')
          .then(() => Promise.all([dispatch('beforeSetWxShare', shareData)]))
          .then(([wx]) => {
            commit('updateGlobalShare', { wx });
          })
          .catch(() => commit('updateGlobalShare', { wx: shareData }));
      });
  },

  // 设置全局分享信息
  setGlobalShare({ state, commit, dispatch }) {
    // console.time('setGlobalShare');
    // 获取海报数据前点击分享兜底, 使用商品图片及标题
    const { goods = {} } = state;
    const shareData = {
      title: goods.title,
      desc: goods.subTitle,
      path: goods.path,
      imageUrl: goods.picture,
    };

    dispatch('beforeSetGlobalShare')
      .then(() =>
        Promise.all([
          dispatch('beforeSetWxShare', shareData),
          dispatch('beforeSetPosterShare'),
          dispatch('beforeSetCopyInfo'),
        ])
      )
      .then(([wx, poster, copyInfo]) => {
        commit('updateGlobalShare', { wx, poster, copyInfo });
      })
      .catch(() => commit('updateGlobalShare', { wx: shareData }))
      .then(() => dispatch('setwxCardShare'));
  },
};

const mutations = {
  // 更新全局分享信息
  updateGlobalShare(state, shareData) {
    if (shareData.wx) {
      state.shareData.wx = { ...state.shareData.wx, ...shareData.wx };
    }
    if (shareData.poster) {
      state.shareData.poster = {
        ...state.shareData.poster,
        ...shareData.poster,
      };
    }
    if (shareData.copyInfo) {
      state.shareData.copyInfo = {
        ...state.shareData.copyInfo,
        ...shareData.copyInfo,
      };
    }
  },
};

export default { state, getters, actions, mutations };
