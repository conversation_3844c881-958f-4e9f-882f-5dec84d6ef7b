/**
 * 登录组件
 */

const state = {
  loginDialogShow: false,
};

const getters = {};

const actions = {
  // 登录成功回调
  afterLogin({ state, commit, dispatch }, loginTicket) {
    const { submitType } = state.skuConfig;
    // 提交数据状态
    commit('setSkuConfig', {
      isAjaxing: true
    });
    commit('updateUserLoginStatus', true);
    commit('updatePostDataExtra', { loginTicket });
    commit('toggleLoginDialog', false);

    // 砍价商品页，登录之后刷新当前页面(耦合用户活动状态)
    if (state.marketActivity.helpCut) {
      dispatch('reload');
      return;
    }

    // submitType: buy addCart addWish orderSeckill
    dispatch(submitType);
  },
};

const mutations = {
  // 更新用户登录状态
  updateUserLoginStatus(state, status) {
    state.user.logined = status;
  },
  // 展示登录弹窗
  toggleLoginDialog(state, status = false) {
    state.loginDialogShow = status;
  },
};

export default { state, getters, actions, mutations };
