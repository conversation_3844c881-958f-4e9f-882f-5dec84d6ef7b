import Toast from '@vant/weapp/dist/toast/toast';
import api from '@goods/utils/api';
import { setLogger } from '@goods/common/logger';

const state = {
  // 是否已订阅补货提醒(spu级别)
  hasRemindSpuStock: false,
  // 是否已订阅补货提醒(sku级别)
  hasRemindSkuStock: {},
  // 是否显示补货提醒引导弹窗
  showStockReminderPop: false,
};

const remindTypeEnum = 'ITEM_STOCK_REMIND';

const getters = {
  stockReminderSkuBtnText(state, getters) {
    const { selectedSkuComb = null } = state.skuExtraData;
    const { showStockLackReminder } = state.shopConfig;
    if (showStockLackReminder && getters.isActiveSoldoutSku) {
      const { skuId } = selectedSkuComb;
      return state.hasRemindSkuStock[skuId]
        ? '已提醒补货'
        : '缺货中，提醒商家补货';
    }
    return '';
  },

  skuStockRemindStatus(state) {
    const { selectedSkuComb = null } = state.skuExtraData;

    if (selectedSkuComb) {
      return state.hasRemindSkuStock[selectedSkuComb.skuId];
    }
    return false;
  },
};

const mutations = {
  setStockRemindStatus(state, skuId) {
    if (skuId) {
      state.hasRemindSkuStock = {
        ...state.hasRemindSkuStock,
        [skuId]: true,
      };
    } else {
      state.hasRemindSpuStock = true;
    }
  },

  toggleStockReminderPop(state, status) {
    state.showStockReminderPop = status;
  },
};

const actions = {
  // 获取补货提醒状态
  getStockRemindStatus({ state, commit }) {
    const { showStockLackReminder } = state.shopConfig;
    const { selectedSkuComb = null } = state.skuExtraData;
    const { skus } = state.staticSkuInfo;
    const skuId = selectedSkuComb ? selectedSkuComb.skuId : '';
    const { id: itemId } = state.goods || {};

    if (showStockLackReminder) {
      const params = {
        itemId,
        remindTypeEnum,
      };
      if (skus && skus.length > 0) {
        params.skuIds = skus.map((sku) => sku.skuId).join(',');
      }
      return api
        .get({
          url: '/wscgoods/hasSaleReminder.json',
          data: params,
        })
        .then((data = {}) => {
          if (skus && skus.length > 0) {
            data.forEach((status, index) => {
              if (status) {
                commit('setStockRemindStatus', skus[index].skuId);
              }
            });
          } else if (data.value) {
            commit('setStockRemindStatus', skuId);
            return true;
          }
        })
        .catch((err) => {
          Toast(err.message || '获取补货提醒状态失败');
        });
    }

    return false;
  },

  // 补货提醒订阅成功
  handleStockReminderSuccess({ state, commit }, success) {
    const { selectedSkuComb = null } = state.skuExtraData;
    const skuId = selectedSkuComb ? selectedSkuComb.skuId : '';
    const { id: itemId } = state.goods || {};

    const params = {
      itemId,
      remindTypeEnum,
    };
    if (skuId) {
      params.skuId = skuId;
    }

    if (success) {
      api
        .post({
          url: '/wscgoods/addStockLackReminder.json',
          data: params,
        })
        .then(() => {
          setLogger({
            et: 'click',
            ei: 'add_reminder',
            en: '订阅提醒',
            params: {
              key: '补货提醒',
            },
          });
          Toast({
            message: '已订阅补货提醒',
            position: 'bottom',
          });
          commit('setStockRemindStatus', skuId);
        })
        .catch((error) => {
          Toast({
            message: error.msg || '订阅补货提醒失败',
            position: 'bottom',
          });
        });
    } else {
      commit('hideSkuPop');
      Toast({
        message: '订阅补货提醒失败',
        position: 'bottom',
      });
    }
  },

  // 补货提醒订阅失败
  handleStockReminderFail({ commit }, err) {
    if (err && err.errMsg && err.errMsg.indexOf('switched off') > -1) {
      commit('hideSkuPop');
      commit('toggleStockReminderPop', true);
      return;
    }

    Toast({
      message: '订阅补货提醒失败',
      position: 'bottom',
    });
  },
};

export default { state, getters, actions, mutations };
