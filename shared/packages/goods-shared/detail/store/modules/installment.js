import { formatPrice } from '@goods/utils';
import api from '@goods/utils/api';

const state = {
  installmentList: {},
  noInstallmentOrder: false,
};

const caches = {};

const mutations = {
  SET_INSTALLMENT(state, payload) {
    const installmentList = Object.keys(payload).map(key => {
      return {
        key,
        val: formatPrice(payload[key]),
      };
    });
    state.installmentList = installmentList;
  },

  SET_INSTALLMENT_COUNT(state, count) {
    const rerestIntallmentable = state.skuExtraData.selectedInstallmentPeriod === count;

    state.skuExtraData = {
      ...state.skuExtraData,
      selectedInstallmentPeriod: rerestIntallmentable ? 0 : count,
    };
  },

  REST_INSTALLMENT_COUNT(state) {
    state.skuExtraData = {
      ...state.skuExtraData,
      installmentRate: 0,
      selectedInstallmentPeriod: 0,
    };
  },

  SET_PAYWAYS(state,payWay){
    state.skuExtraData = {
      ...state.skuExtraData,
      payWay
    };
  },

  SET_INSTALLMENT_ORDER(state, isGrey) {
    state.noInstallmentOrder = isGrey;
  },
};
const actions = {
  fetchInstallentPrice({ state, commit, getters }, selectedSkuComb) {
    const { goods } = state;
    const { skuData } = getters;
    const { skuScene } = state.skuConfig;
    const query = {
      goodsId: goods.id,
      kdtId: getters.kdtId,
    };

    if (skuData.noneSku) {
      query.skuId = skuData.collectionId;
      query.price = skuData.maxPrice;
    } else {
      if (!selectedSkuComb) {
        const list = skuData.list.map(item => ({ id: item.id, price: item.price }));

        list.sort((a, b) => {
          return a.price > b.price ? 1 : -1;
        });
        selectedSkuComb = list[0];
      }

      query.skuId = selectedSkuComb.id;
      query.price = selectedSkuComb.price;
    }

    const installment = caches.installment = caches.installment || {};

    const cacheKey = query.skuId + skuScene;
    commit('REST_INSTALLMENT_COUNT');
    let next = Promise.resolve(installment[cacheKey]);
    if (!installment[cacheKey]) {
      next = api.get({
        url: '/wscgoods/sku/installment-price.json',
        data: query,
      })
        .then(data => {
          installment[cacheKey] = data;
          return data;
        });
    }

    return next.then(({ installmentRate, isGrey = false, instalmentPrices = {} }) => {
      commit('setSkuExtraData', { installmentRate });
      const stepperCount = state.skuExtraData.stepperCount || 1;

      instalmentPrices = Object.keys(instalmentPrices).reduce((res, key) => {
        res[key] = instalmentPrices[key] * stepperCount;
        return res;
      }, {});

      commit('SET_INSTALLMENT', instalmentPrices);
      // 十元一下商品不支持分多期付款，实际应该有bug，现在前端计算多件的价格。如果多件的总价大于 10，就会出现总价大于 10 也不支持分期
      commit('SET_INSTALLMENT_ORDER', isGrey);
    });
  },

  calcInstallmentPrices({ commit, state, getters }, value) {
    const { skuData } = getters;
    const { selectedSkuComb = {} } = state.skuExtraData;
    let selectedSkuId = selectedSkuComb && selectedSkuComb.id;

    if (skuData.noneSku) {
      selectedSkuId = skuData.collectionId;
    }
    const { skuScene } = state.skuConfig;
    const cacheKey = selectedSkuId + skuScene;

    const { instalmentPrices } = caches.installment[cacheKey] || {};

    if (!selectedSkuId || !instalmentPrices) return;

    const changedPrices = Object.keys(instalmentPrices)
      .reduce((res, key) => {
        res[key] = Number((instalmentPrices[key] * value).toFixed(2));

        return res;
      }, {});

    commit('SET_INSTALLMENT', changedPrices);
  },
};

const getters = {
  isInstallment(state) {
    return state.payWays.instalment;
  },

  isFreeInterest(state) {
    return state.payWays.isFreeInterest;
  },
};

export default { getters, actions, mutations, state };
