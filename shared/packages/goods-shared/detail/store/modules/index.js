import { mergeStore } from '../utils/helper';
import loginStore from './login';
import periodBuyStore from './period-buy';
import pointsStore from './points';
import presaleStore from './presale';
import retailStore from './retail';
import skuFetcherStore from './sku-fetcher';
import timelimitStore from './timelimited-discount';
import virtualStore from './virtual';
import installmentStore from './installment';
import diyStore from './diy';
import skuPrice from './sku-price';
import couponStore from './coupon';
import shareStore from './share';
import shareBenefitStore from './share-benefit';
import saleReminderStore from './sale-reminder';
import stockReminderStore from './stock-reminder';

const stores = [
  loginStore,
  periodBuyStore,
  pointsStore,
  presaleStore,
  retailStore,
  skuFetcherStore,
  timelimitStore,
  virtualStore,
  installmentStore,
  diyStore,
  couponStore,
  skuPrice,
  shareStore,
  shareBenefitStore,
  saleReminderStore,
  stockReminderStore,
];

export default stores.reduce((a, b) => mergeStore(a, b), {});
