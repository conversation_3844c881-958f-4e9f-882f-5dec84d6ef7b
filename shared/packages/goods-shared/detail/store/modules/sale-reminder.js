import Toast from '@vant/weapp/dist/toast/toast';
import { setLogger } from '@goods/common/logger';

const app = getApp();
const errText = '订阅开售提醒失败';

const state = {
  // 是否已订阅开售提醒
  hasAddSaleReminder: false,
  // 是否显示开售提醒引导弹窗
  showSaleReminderPopup: false,
};

const mutations = {
  setSaleReminder(state) {
    state.hasAddSaleReminder = true;
  },

  openSaleReminderPopup(state) {
    state.showSaleReminderPopup = true;
  },

  closeSaleReminderPopup(state) {
    state.showSaleReminderPopup = false;
  },
};

const actions = {
  initSaleReminder({ state, commit }) {
    const { goods } = state;
    // 有登陆态的情况下去获取订阅状态
    if (state.display.showSaleReminder && app.hasToken()) {
      return app
        .request({
          path: '/wscgoods/hasSaleReminder.json',
          data: {
            itemId: goods.id,
          },
        })
        .then((data = {}) => {
          if (data.value) {
            commit('setSaleReminder');
            return true;
          }
          return false;
        })
        .catch(() => {});
    }

    return false;
  },

  // 定时上架提醒处理
  saleReminderHandle({ state, commit }) {
    // 如果距离开售时间小于 5min
    if (
      state.goodsActivity.waitToSold &&
      state.goodsActivity.waitToSold.startSoldTime -
        state.serverDeltaTime -
        Date.now() <
        5 * 60 * 1000
    ) {
      Toast({
        message: '商品即将开售',
        position: 'bottom',
      });
      return;
    }

    // 确定是否已订阅消息
    if (state.hasAddSaleReminder) {
      return;
    }

    return app
      .request({
        path: '/wscgoods/addSaleReminder.json',
        method: 'post',
        data: {
          itemId: state.goods.id,
        },
      })
      .then(() => {
        setLogger({
          et: 'click',
          ei: 'add_reminder',
          en: '订阅提醒',
          params: {
            key: '开售提醒',
          },
        });
        Toast({
          message: '已订阅开售提醒',
          position: 'bottom',
        });
        commit('setSaleReminder');
      })
      .catch((error) => {
        Toast({
          message: error.msg || errText,
          position: 'bottom',
        });
      });
  },

  // 定时上架提醒处理
  handleSaleReminderSuccess({ state, commit }, success) {
    if (success) {
      app
        .request({
          path: '/wscgoods/addSaleReminder.json',
          method: 'post',
          data: {
            itemId: state.goods.id,
          },
        })
        .then(() => {
          Toast({
            message: '开售前会发送小程序订阅消息提醒你',
            position: 'bottom',
          });
          commit('setSaleReminder');
        })
        .catch((error) => {
          Toast({
            message: error.msg || errText,
            position: 'bottom',
          });
        });
    } else {
      commit('openSaleReminderPopup');
    }
  },

  handleSaleReminderFail({ commit }, err) {
    if (err && err.errMsg && err.errMsg.indexOf('switched off') > -1) {
      commit('openSaleReminderPopup');
      return;
    }

    Toast({
      message: errText,
      position: 'bottom',
    });
  },
};

export default { state, actions, mutations };
