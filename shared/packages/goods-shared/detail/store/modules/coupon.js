/**
 * 券后价活动
 */
import { get } from '@goods/utils';
import { formatPreferentialPriceTag } from '../utils/modules/coupon';

const state = {
};

const getters = {
  // 券后价活动
  couponActivity(state) {
    return get(state, 'orderActivity.coupon', {});
  },

  // 是否有券后价活动
  hasCouponActivity(state) {
    return !!get(state, 'orderActivity.coupon', null);
  },

  // 最佳券列表
  // optimalCouponList(state) {
  //   return (state.optimalCouponList || []).slice(0, 20);
  // },

  // 单sku默认展示最佳券
  defaultOptimalCoupon(state, getters) {
    const couponSkuPrices = getters.couponActivity.couponSkuPrices;
    if (couponSkuPrices && couponSkuPrices.length === 1) {
      return couponSkuPrices[0].useThresholdAndValueCopywriting;
    }
    return '';
  },

  // 券后价内容
  preferentialPrice(state, getters) {
    return getters.couponActivity.preferentialPrice || '';
  },

  // 券后价标签展示内容
  preferentialPriceTag(state, getters) {
    if (getters.hasCouponActivity) {
      return formatPreferentialPriceTag(getters.preferentialPrice);
    }
    return null;
  },

  // 下单自动领券；需要区分单sku和多sku
  autoGetCouponId(state, getters) {
    const { optimalCouponList = [] } = state;
    const { seletecdPreferentialCoupon } = getters;

    let targetCouponId = null;
    if (optimalCouponList.length) {
      targetCouponId = optimalCouponList[0].id;

      // 多sku
      if (seletecdPreferentialCoupon) {
        targetCouponId = seletecdPreferentialCoupon.activityId;
      }
    }

    return targetCouponId;
  },
};
const actions = {
};

const mutations = {
};

export default { getters, actions, mutations, state };
