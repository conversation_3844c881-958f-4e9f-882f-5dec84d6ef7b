import state from './state';
import mutations from './mutations';
import actions from './actions';
import getters from './getters';
import { mergeStore } from './utils/helper';

import containerStore from './container-store';
import moduleStore from './modules';

const rootStore = [
  {
    state,
    getters,
    actions,
    mutations,
  },
  moduleStore,
  containerStore,
].reduce((a, b) => mergeStore(a, b), {});

export default rootStore;
