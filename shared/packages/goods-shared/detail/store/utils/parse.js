import {
  or,
  get,
  args,
  omit,
  formatPrice,
  fullfillImage,
  formatDistance,
  getCdnImageUrl,
  getRedirectUrl,
  mapKeysToCamelCase,
  moneyFormat,
  getDetailPath,
} from '@goods/utils';
import pick from '@youzan/weapp-utils/lib/pick';
import {
  formatSkuData,
  formatRemainTime,
  formatSkuPrice,
  updateAsyncSku,
  parseGoodsExtendInfo,
} from './helper';
import { parseDiscountInvite, parseCouponActivity } from './modules';
import { updatePageData } from '../../extensions/yun/index';
// 根据用户session得到的信息
function formatUserData(state, payload) {
  // 下单配置
  Object.assign(state.buyConfig, {
    // 是否公众号粉丝
    isFans: payload.isFans,
    // 是否必须关注购买
    noFansBuy: !payload.isFansBuy,
  });
}

function resetPropertyToNull(target) {
  return Object.keys(target).reduce((res, key) => {
    res[key] = null;
    return res;
  }, {});
}

function formatDisplayData(state, payload) {
  // 环境变量
  Object.assign(state.env, {
    // 是否是新希望店铺
    isNewHopeKdt: payload.isNewHopeKdt,
    // 是否是全局购物车
    isAllCart: payload.isAllCart,
    // 是否隐藏底部
    hideGoodsBottom: payload.hideGoodsBottom,
  });
}

function formatGoodsStaticData(state, payload = {}) {
  const {
    pageParams = {},
    goods: goodsData = {},
    offlineData,
    shopMetaInfo,
  } = payload;
  const {
    // 商品类型
    type = '',
    // 商品
    goods = {},
    // 店铺
    shop = {},
    // 店铺设置
    shopConfig = {},
    // 配送
    delivery = {},
    // 优惠券列表
    // voucherModelList = [],
    // sku
    skuInfo: staticSkuInfo,
    // 商品扩展信息
    extendModelList,
  } = goodsData;

  const originSku = formatSkuData(staticSkuInfo);
  const originNormalSku = { ...originSku };
  const goodsExtendInfo = parseGoodsExtendInfo(extendModelList);
  // SKU数据结构
  Object.assign(state, {
    originSku,
    originNormalSku,
    staticSkuInfo,
    goodsExtendInfo,
  });

  mergeSkuData(state, 'goods', true);

  Object.assign(state, {
    // 店铺信息
    shop: {
      kdtId: shop.kdtId,
      logo: shop.logo,
      name: shop.shopName,
      city: shop.city || '',
      province: shop.province || '',
      address: shop.address || '',
      rootKdtId: shopMetaInfo.rootKdtId,
      shopMetaInfo,
      phone: '',
      // 7表示零售店铺
      shopType: shop.shopType || '',
      // 是否是总店预览模式，通过query和连锁模式判断
      isHqShopPreview: shop.isHqShopPreview,
    },
  });

  // 视频
  const videoConfig = get(goods, 'video', {});
  Object.assign(state.video, {
    width: +videoConfig.coverWidth,
    height: +videoConfig.coverHeight,
    coverUrl: videoConfig.coverUrl,
    videoUrl: videoConfig.videoUrl || '',
    // 统计播放
    countPlayedUrl: videoConfig.countPlayedUrl || '',
  });

  const { pictures } = goods;
  if (videoConfig.coverUrl) {
    pictures.unshift({
      url: videoConfig.coverUrl,
      isVideo: true,
    });
  }

  // 主图信息
  Object.assign(state.picture, {
    pictures: pictures || [],
    // 服务端评估图片高度
    height: goods.pictureHeight,
  });

  const { showPrice, showOldPrice } = formatSkuPrice(originNormalSku);
  const {
    umpAlias = '',
    umpType = '',
    activityId = '',
    activityType = '',
  } = mapKeysToCamelCase(pageParams);
  const query = omit(pageParams, [
    'ump_alias',
    'ump_type',
    'access_token',
    'app_id',
  ]);

  const path = getDetailPath({
    ...query,
    umpType,
    umpAlias,
    activityId,
    activityType,
    alias: goods.alias,
  });

  Object.assign(state.goods, {
    ...goods,
    type,
    // 小程序路径
    path,
    // 商品默认头图，sku弹层展示用
    picture: fullfillImage(get(goods, 'pictures.0.url', '')),
    // 商品卖点，页面子标题内容
    sellPoint: goods.sellPoint || '',
    // 是否是外链商品 购买方式 0：外部购买商品 1：非外部购买商品
    isOutlink: goods.buyWay === 0,
    // 商品外链
    outlinkUrl: getRedirectUrl(goods.buyUrl),

    limit: {
      // 商品限购数
      quota: goods.quota,
      // 已购买数 异步数据给
      quotaUsed: 0, // goods.quotaUsed,
      singleQuota:
        goods.isHaitao || goods.isVirtualTicket || goods.isVirtualCoupon
          ? 100
          : 0, // 海淘/电子卡券/优惠券每单限购 100
      // 限购周期
      quotaCycle: goods.quotaCycle,
      // 起售数量
      startSaleNum: goods.startSaleNum || 1,
    },
    // 库存
    stockNum: originNormalSku.stockNum,
    hideStock: originNormalSku.hideStock,
    // 显示价格
    price: showPrice,
    oldPrice: showOldPrice,
    minPrice: originNormalSku.minPrice,
    maxPrice: originNormalSku.maxPrice,
    oldMinPrice: 0,
    oldMaxPrice: 0,
    itemSalePropList: goods.itemSalePropList || [],
    // 分销员要的活动信息
    salesmanActivityInfo: {
      umpAlias,
      umpType,
      activityId,
      activityType,
    },
  });

  // 商品类目
  Object.assign(state.property, {
    keys: get(goods, 'itemProperty.keys', []),
    values: get(goods, 'itemProperty.values', []),
  });

  Object.assign(state.supplier, {
    // 分销商对应的供货商kdtId
    kdtId: goods.supplierKdtId || '',
    // 推广模式
    mode: goods.fxTradeMode,
  });

  // 分销员
  // 优先使用赚字内设置的alias
  if (!state.salesman?.alias) {
    Object.assign(state.salesman, {
      // 分销员标识 seller
      alias: pageParams.sls,
    });
  }

  const { city = '', district = '', address = '' } = offlineData.address || {};

  // 多网点
  Object.assign(state.multistore, {
    ...offlineData,
    address: `${city}${district}${address}`,
    // 目前这个开关只控制营销店铺模块下面的多网点信息，由于跳转的电商页面现在不支持，所以小程序直接隐藏；
    openMultiStoreSwitch: false,
    distance: 0,
    // 其他可售门店
    salableStores: [],
  });

  // 退款设置
  const refund = get(goods, 'refundModel', {});
  // 退款模型
  Object.assign(state.refund, {
    // 是否支持退款（包含虚拟商品和电子卡券）
    isSupport: get(refund, 'isSupportRefund', 1),
    // 退款方式
    type: refund.refundType,
    // 退款时间区间
    interval: refund.periodMillSeconds,
  });

  //   // 店铺信息
  Object.assign(state.shop, {
    // kdtId: shop.kdtId,
    // logo: shop.logo,
    // name: shop.shopName,
    phone: shop.phone,
    // 7表示零售店铺
    // shopType: shop.shopType,
  });
  // 类目参数
  Object.assign(state.goods, {
    itemCatePropDetailModel: goods.itemCatePropDetailModel,
  });

  // 店铺配置
  Object.assign(state.shopConfig, {
    // 商品页是否展示立即购买按钮
    isShowBuyBtn: shopConfig.showBuyBtn,
    // 是否有赞担保
    isYouzanSecured: shopConfig.isYouzanSecured,
    // 是否加入担保交易
    isSecuredTransactions: shopConfig.isSecuredTransactions,
    // 是否开启推荐商品
    showRecommendGoods: shopConfig.goodsRecommendForWeapp,
    // 是否开启销量与成交记录
    showBuyRecord: shopConfig.buyRecord,
    // 是否开启商品评价
    showCustomerReviews: shopConfig.customerReviews,

    // 评价会员标签展示 0:评价展示会员等级 1:评价展示会员名称）
    evaluationMemberLabel: shopConfig.evaluationMemberLabel || 0,

    // 是否点亮有赞公益
    showPublicBenefit: shopConfig.publicWelfareShopSwitch,

    // 是否开启买家秀
    showBuyerShows: Boolean(shopConfig.buyerShow),
    // 是否开启个性化商品推荐
    showPersonalRecommendGoods: shopConfig.personalRecommendWindowGoods,
    // 是否精简版微商城
    isSimplifiedWsc: shopConfig.isSimplifiedWsc,
    // 是否支持运费险
    // 店铺运费险对于分销、礼品卡、酒店、周期购、会员卡、虚拟商品无效
    supportFreightInsurance: shopConfig.freightInsurance,
    // 是否隐藏购物车按钮
    hideShoppingCart: shopConfig.hideShoppingCart,
    // 是否有线下门店
    hasPhysicalStore: shopConfig.teamPhysical,
    // 微信自有支付
    isWeixinPayOrigin: +shopConfig.weixinPayOrigin === 1,
    // 主体认证类型 0 初始化状态 1 个人认证，2企业认证，3 个体工商户 4其他组织，5政府及事业单位  98网店 99认证被驳回
    principalCertType: shopConfig.principalCertType || 0,
    // 品牌认证类型 0 初始化状态 1 旗舰店 2专卖店 3直营店 4 专营店 99 认证被驳回
    brandCertType: shopConfig.brandCertType || 0,
    // 成交记录设置 列表0， 悬浮窗1
    goodsDetailBuyRecord: shopConfig.goodsDetailBuyRecord || '',
    // 新的跑马灯开关，支持评价、好评等内容
    goodsTradeMarquee: shopConfig.goodsTradeMarquee || null,
    // 销售数量设置 // { show: 0 // 商详页销量是否展示：0-不展示，1-展示 limit: false,// 销量展示时是否限定值 limit_num:'',// 销量展示的限定值，大于0 }
    goodsDetailSales: shopConfig.goodsDetailSales || null,
    // 立即购买按钮自定义文案设置
    buyBtnConfig: shopConfig.buyBtnConfig || null,
    // 客服自定义文案设置
    webImInGoodsConfig: shopConfig.webImInGoodsConfig || null,
    // 商家持续经营的年限(int)
    shopOperateDurationYears: shopConfig.shopOperateDurationYears || 0,
    // 商家点亮配置开关，默认值0，商家开启之后为 1
    shopOperateDurationTagSwitch: shopConfig.shopOperateDurationTagSwitch || 0,
    // 是否开启微信担保
    weixinTransactionSecured: shopConfig.weixinTransactionSecured,
    actHaiGouIcon: shopConfig.actHaiGouIcon,
    // 是否显示商品补货提醒
    showStockLackReminder:
      +shopConfig.goodsSupplyReminder === 1 &&
      goods.itemType !== 'FENXIAO' &&
      goods.isDisplay === 1,
    // 是否展示类目
    showCategoryParam: shopConfig.showCategoryParam,
    // 领券模块是否前置
    goodsActivityTagsPreposition: shopConfig.goodsActivityTagsPreposition,
    // 商品主图视频好评弹幕开关
    goodsVideoDanmakuSwitch: Number(shopConfig.goodsVideoDanmakuSwitch) === 1,
  });

  try {
    const { open } = JSON.parse(shopConfig.goodsRecommendForYou || '{}');
    state.shopConfig.openRecommendForYou = open;
  } catch (error) {
    /**/
  }

  try {
    const { default: useCustomName, label = '' } = JSON.parse(
      shopConfig.customCartName || '{}'
    );
    state.shopConfig.showDefaultCartText = !+useCustomName;
    state.shopConfig.cartText = label.substr(0, 4);
  } catch (error) {
    /**/
  }

  try {
    state.shopConfig.goodsFavorableRate = JSON.parse(
      shopConfig.goodsFavorableRate || '{}'
    );
  } catch (error) {
    /**/
  }

  // 暂时禁用分期支付，如果后续需要支持，把这行去掉就行；
  goods.isInstallment = false;

  const priorUse = +(shopConfig.priorUse ?? 0);

  Object.assign(state.payWays, {
    // 是否支持货到付款，虚拟商品不支持
    cashOnDelivery: !goods.isVirtual && +shopConfig.codPay,
    // 是否支持先用后付
    priorUse,
    // 是否支持分期付款,
    instalment: +goods.isInstallment,
    // 是否3期免息
    isFreeInterest: +goods.freeInterest,
  });

  // 店铺担保配置
  const youzanGuaranteeModel = get(goods, 'youzanGuaranteeModel', {});
  Object.assign(state.guarantee, {
    // 是否加入有赞担保
    on: youzanGuaranteeModel.youzanGuarantee || 0,
    // 担保样式
    style: shopConfig.guaranteeShowStyleType,
    // 担保详细文案
    desc: youzanGuaranteeModel.guaranteedComponents || [],
  });

  // 配送信息
  Object.assign(state.distribution, {
    ...delivery,
    // 运费
    postage: get(delivery, 'postage.desc', ''),
    // 配送地址
    deliveryAddress: get(delivery, 'deliveryAddress', {}),
    // 快递费用
    expressFee: get(delivery, 'express.desc', ''),
    // 同城送费用
    localDeliveryFee: get(delivery, 'localDelivery.desc', ''),
  });

  // 商品tab栏数据
  const tplStyle = goods.itemDetailTplStyle || 0;
  Object.assign(state.goodsDetail, {
    // 默认激活的详情tab，小程序目前没有这个需求
    defaultType: 'goods',
    // 模板风格 0：普通版 1：极速版
    tplStyle,
  });

  // 先清空 goodsActivity
  state.goodsActivity = resetPropertyToNull(state.goodsActivity);

  // 商品静态属性
  const { goodsActivity } = state;

  // 定时开售
  const waitToSold = get(goods, 'startSoldTime', 0);
  // 电子卡券
  const virtualTicket = get(goods, 'ecardExtra', null);
  // 优惠券
  const virtualCoupon = get(goods, 'couponGoodsExtraModel', null);
  // 周期购
  const periodbuy = get(goods, 'periodBuyExtra', null);
  // 餐饮
  const catering = mapKeysToCamelCase(get(goods, 'cateringInfo', ''));
  // 海淘
  const haitao = get(goods, 'haoTaoItemExtra', null)
    ? mapKeysToCamelCase(get(goods, 'haoTaoItemExtra', null))
    : null;
  // 预售信息
  const preSaleInfo = get(goods, 'preSaleInfo', null);

  // 优惠券列表
  // if (Array.isArray(voucherModelList)) {
  //   state.umpCouponList = voucherModelList.map((voucher) => {
  //     voucher.disabled = false;
  //     return voucher;
  //   });
  // }

  const currTime = new Date().getTime();
  // 定时开售还未开始
  if (waitToSold && waitToSold > currTime) {
    Object.assign(goodsActivity, {
      waitToSold: {
        startSoldTime: waitToSold,
      },
    });
  }

  // 送礼商品通过url里带type=gift识别
  if (get(state, 'pageParams.type', '').toUpperCase() === 'GIFT') {
    Object.assign(goodsActivity, {
      gift: {},
    });
  }

  const { localDelivery = 1 } = shopConfig;
  if (+localDelivery === 0) {
    // 如果店铺同城配送能力开关【关闭】，不支持商品属性
    unsupportItemSaleProp(state);
  }

  // 电子卡券
  if (virtualTicket) {
    Object.assign(goodsActivity, {
      virtualTicket: {
        // 节假日是否可用，仅做展示用，实际不校验
        // 0：不可用，1：可用
        isHolidaysAvailable: virtualTicket.holidaysAvailable,
        // 卡券生效类型 0：立即生效 1：xx小时后生效 2：次日生效
        effectType: virtualTicket.effectiveType,
        // 生效延迟时间
        effectDelayTime: virtualTicket.effectiveDelayHours,
        // 卡券有效期类型 0：长期可用 1：xx天内可用 2：时间区间内可用
        validityType: virtualTicket.validityType,
        // 有效天数
        validityDays: virtualTicket.itemValidityDay,
        // 可用开始时间
        startDate: virtualTicket.itemValidityStart,
        // 可用结束时间
        endDate: virtualTicket.itemValidityEnd,
        // 使用说明，仅做展示用，实际不校验
        instructions: virtualTicket.instructions,
        // 预定时间 传给后端
        itemPreOrderTime: virtualTicket.itemPreOrderTime,
        // 预定时间 前端展示
        itemPreOrderTimeStr: virtualTicket.itemPreOrderTimeStr,
      },
    });
    // 电子卡券暂不支持商品属性
    unsupportItemSaleProp(state);
  }

  // 优惠券
  if (virtualCoupon) {
    Object.assign(goodsActivity, {
      virtualCoupon,
    });
  }

  // 周期购
  if (periodbuy) {
    Object.assign(goodsActivity, {
      periodbuy: {
        // 周期购描述
        description: periodbuy.description,
        // 配送周期 0：每天 1：每周 2：每月
        period: periodbuy.period,
        // 送达日期 周一至周日，可多选
        deliveryTimeList: periodbuy.deliverTime,
        // 规格周期
        periodSku: periodbuy.skuExtras,
        // 送达时间
        deliverTimeId: '',
      },
    });
    // 周期购暂不支持商品属性
    unsupportItemSaleProp(state);
  }

  // 预售信息
  if (preSaleInfo) {
    Object.assign(goodsActivity, {
      preSaleInfo,
    });
    // 预售暂不支持商品属性
    unsupportItemSaleProp(state);
  }

  // 海淘详细信息
  if (haitao) {
    Object.assign(goodsActivity, {
      haitao,
    });
    // 海淘暂不支持商品属性
    unsupportItemSaleProp(state);
  }

  // 餐饮
  if (catering) {
    Object.assign(goodsActivity, {
      catering: {
        // 加料
        feedListData: get(catering, 'ingredientList', []),
      },
    });
  }
  // 商品属性暂时不支持分期支付
  if (+goods.isInstallment) {
    unsupportItemSaleProp(state);
  }

  // 是否展示开售提醒提示
  // 1. 店铺打开开售提醒开关
  // 3. 商品上架时间 > 5分钟 & 商品上架时间 < 1年
  if (+shopConfig.goodsSalesReminder && state.goodsActivity.waitToSold) {
    const waitToSoldTime =
      +state.goodsActivity.waitToSold.startSoldTime - state.serverTimestamp;
    if (
      waitToSoldTime > 5 * 60 * 1000 &&
      waitToSoldTime < 365 * 24 * 60 * 600 * 1000
    ) {
      Object.assign(state.display, {
        showSaleReminder: true,
      });
    }
  }
}

// 商详页开放活动sku数据使用
function mergeSkuData(state, name, isGoods = false) {
  let newSkuData = null;

  if (state.originSku && isGoods) {
    newSkuData = pick(state.originSku, [
      'list',
      'skuId',
      'soldNum',
      'stockNum',
      'tree',
    ]);
  }

  if (state.originActivitySku) {
    newSkuData = pick(state.originActivitySku, [
      'list',
      'skuId',
      'soldNum',
      'stockNum',
      'totalActivityStockNum',
      'tree',
    ]);
  }

  Object.assign(state.activitySkuData, {
    [name]: {
      ...newSkuData,
    },
  });

  // updateSaas
  updatePageData();
}

// 动态数据接口
function formatGoodsAsyncData(state, payload = {}) {
  const {
    // 计算的地理位置
    geoLocationData = null,
    // 营销活动信息
    parsedActivities = {},
    // 促销活动数据
    parsedPromotions: orderPromotion = [],
    // 促销活动是否被限流
    promotionLimit,
    buttonUmpTimelimitModel,
    buttonUmpTextModel,
    activityInfo = {},
    // template对象
    pageFeature = {},
    // 异步的商品对象
    goodsExtra = {},
    salableStores = [],
    physicalStores,
    soldNum = null,
    skuStocks,
    spuStock,
    // 外部活动
    outerActivities = [],
  } = payload;

  const {
    // 0元拼团
    luckyDrawGroup,
    // 砍价0元购
    helpCut,
    // 拼团
    groupOn,
    // 秒杀
    seckill,
    // 团购
    tuan,
    // 降价拍
    auction,
    // fCode
    fCode,
    // 积分兑换
    pointsExchange,
    // 赠品
    presentExchange,
    // 返现
    cashBack,
    // 满减
    meetReduce,
    // 供货商满减送
    supplierMeetReduce,
    // 包邮
    carriageDiscount,
    // 打包一口价
    bale,
    // 组合优惠
    packageBuy,
    // 加价购
    plusBuy,
    // 定金膨胀-初始定金
    depositExpansion,
    // 助力定金膨胀
    helpDepositExpansion,
    // 扫码优惠或者会员折扣
    discount,
    // 会员包邮
    customerPostageFree,
    // 限时折扣
    timelimitedDiscount,
    // 引导用户升级会员折扣
    discountInvite,
    // 券后价
    coupon,
    // 第二件半价
    secondHalfDiscount,
    // 公众号涨粉
    fansBenefit,
    // 企微涨好友
    wecomFansBenefit,
    // 返现 + 返储值金
    cashbackPro,
    // 内购裂变
    inSourcingFission,
    // 分享有礼
    shareEncourage,
    // 新品发售
    productLaunch,
    // 限时秒杀
    limitedSeckill,
  } = parsedActivities;

  state.marketActivity = resetPropertyToNull(state.marketActivity);
  state.orderActivity = resetPropertyToNull(state.orderActivity);
  state.parsedActivities = parsedActivities;
  const {
    // 服务器时间 - 本地时间 产生的偏置
    serverDeltaTime = 0,
    // 拿到服务器数据时的本地时间
    serverTimestamp,
    // 静态化的SKU数据结构
    staticSkuInfo,
    // 营销活动
    marketActivity,
    // 订单活动
    orderActivity,
    // 商品信息
    goodsActivity,
  } = state;

  // 更新异步sku新数据
  updateAsyncSku(state, spuStock, skuStocks);
  const { originNormalSku } = state;

  const {
    hasPurchaseRight = true,
    isGoodsCollected = false,
    quotaUsed = 0,
    haitaoTariffAmount = '',
    buyLimitGuideInfo = {},
    buyLimitType,
  } = goodsExtra;

  Object.assign(state.goods, {
    // 商品是否被收藏，有赞精选
    isGoodsCollected,
    // 是否支持F码
    isSupportFCode: get(pageFeature, 'extra.supportFCode', false),
    // 是否仅限特定会员购买
    limitBuy: buyLimitType === 2,
    // 异步销量字段更准确
    soldNum: soldNum === null ? state.goods.soldNum : soldNum,
    // 限购引导信息
    buyLimitGuideInfo,
  });
  // 用户已购数量
  Object.assign(state.goods.limit, {
    quotaUsed,
  });

  state.multistore.salableStores = [];
  // 多网点推荐网点
  // TODO 链接需要处理
  if (salableStores) {
    Object.assign(state.multistore, {
      // 其他可售门店
      salableStores: salableStores.map((it) => ({
        ...it,
        link: args.add(state.goods.path, {
          offlineId: it.id,
        }),
        distance: formatDistance(it.distance),
      })),
    });
  }

  // 会员包邮
  if (customerPostageFree && customerPostageFree.isCustomerPostage) {
    state.distribution.postage = '免运费';
  }

  // 如果有海淘税费，诸如会员折扣海淘税费是不一样的
  if (goodsActivity.haitao && haitaoTariffAmount) {
    goodsActivity.haitao.tariffAmount = haitaoTariffAmount;
  }

  // 前端计算多网点距离
  if (geoLocationData && geoLocationData.distance) {
    Object.assign(state.multistore, {
      distance: geoLocationData.distance || 0,
    });
  }

  // 弹层、按钮、类目等展示控制
  // 不可购买原因
  Object.assign(state.display, {
    // 禁止购买原因
    forbidBuyReason: pageFeature.forbidBuyReason,
    // 是否展示购买按钮
    showBuyBtn: pageFeature.showBuyButton,
    // 是否展示加入购物车按钮
    showCartBtn: pageFeature.showCartButton,
    // 是否展示积分兑换按钮
    showPointsBtn: pageFeature.showPointsButtonStatus !== 0, // https://doc.qima-inc.com/pages/viewpage.action?pageId=282941057
    // 是否展示不可购买按钮，包含 下架、售罄等
    showForbidBuyBtn: pageFeature.showForbidBuyButton,
    // 是否展示送礼按钮
    showGiftBtn: pageFeature.showGiftButton,
    // 是否展示店铺按钮
    showMiniShopBtn: pageFeature.showMiniShopButton,
    // 是否展示小购物车按钮
    showMiniCartBtn: pageFeature.showMiniCartButton,
    // 是否展示小积分兑换按钮
    showMiniPointsBtn: pageFeature.showMiniPointsButton,
    // 是否展示分享按钮
    showShareBtn: pageFeature.showShareButton,
    // 是否展示im按钮
    showImBtn: pageFeature.showImButton,
    // 是否展示默认的按钮文案
    showDefaultBuyButtonText: pageFeature.showDefaultBuyButtonText,
    // 自定义购买按钮文案
    buyButtonText: pageFeature.buyButtonText,
    // 是否展示去活动列表的小按钮，内购列表活动下为 true
    showMiniActivityButton: pageFeature.showMiniActivityButton,
  });

  // 商品页活动购买价格，下单时有用
  Object.assign(state.activityInfo, {
    // 活动alias
    activityAlias: activityInfo.alias || '',
    // 活动id
    activityId: activityInfo.id || 0,
    // 活动类型
    activityType: activityInfo.code || '',
  });

  Object.assign(state, {
    orderPromotion,
    orderPromotionLimit: promotionLimit,
  });

  /* --------------------------
   * 活动信息
   --------------------------- */
  // 0元购 拼团
  if (luckyDrawGroup) {
    const {
      id: activityId,
      name: activityName,
      participantNum: joinNum,
      ...otherLuckyDrawGroup
    } = luckyDrawGroup;
    Object.assign(marketActivity, {
      luckyDrawGroup: {
        ...otherLuckyDrawGroup,
        activityId,
        activityName,
        joinNum,
        ...formatRemainTime({
          startTime: luckyDrawGroup.startTime,
          endTime: luckyDrawGroup.endTime,
          endRemainTime: luckyDrawGroup.endRemainTime,
          serverDeltaTime,
        }),
      },
    });
    // 模拟更新状态
    updateGoodsState(state, luckyDrawGroup);
    // goodsActivity.waitToSold = null;

    mergeSkuData(state, 'luckyDrawGroup');
  }
  // 砍价0元购
  if (helpCut) {
    Object.assign(marketActivity, {
      helpCut: {
        ...helpCut,
        // totalActivityStockNum: helpCut.totalActivityStockNum,
        ...formatRemainTime({
          startTime: helpCut.startTime,
          endTime: helpCut.endTime,
          serverDeltaTime,
        }),
      },
    });
    // 模拟更新状态
    updateGoodsState(state, helpCut);
    // goodsActivity.waitToSold = null;

    mergeSkuData(state, 'helpCut');
  }

  // 拼团信息
  if (groupOn) {
    // 找出每个阶梯中价格最低的一项，组成一组阶梯，用于没有选中 sku 的时候用
    const minGroup = {};

    const ladderList = groupOn.ladderPrice || {};

    Object.keys(ladderList).forEach((key) => {
      const ladder = ladderList[key];

      ladder.forEach((item) => {
        const { scale } = item;

        if (!minGroup[scale]) {
          minGroup[scale] = item;
          return;
        }

        minGroup[scale] =
          minGroup[scale].skuPrice > item.skuPrice ? item : minGroup[scale];
      });
    });

    ladderList[0] = Object.keys(minGroup).map((key) => minGroup[key]);

    const startTime = serverTimestamp + (groupOn.startRemainTime || 0);
    const endTime = serverTimestamp + (groupOn.endRemainTime || 0);
    Object.assign(marketActivity, {
      groupOn: {
        ...groupOn,
        ...formatRemainTime({
          // 服务器活动开始时间
          startTime,
          // 服务器活动结束时间
          endTime,
          serverDeltaTime,
        }),
        // 当前拼团活动凑团列表
        ongoingGroup: groupOn.ongoingGroup || [],
        limit: {
          // 拼团限购数据
          quota: groupOn.quota || 0,
          quotaUsed: groupOn.quotaUsed || 0,
          quotaCycle: groupOn.quotaCycle || 0,
          singleQuota: groupOn.perOrderLimit || 0,
        },
        ladderList,
        subType: groupOn.subType || 'groupOn',
      },
    });

    // 阶梯团暂不支持商品属性
    if (groupOn.subType === 'ladderGroupOn') {
      unsupportItemSaleProp(state);
    }
    // 模拟更新状态
    updateGoodsState(state, groupOn);
    // goodsActivity.waitToSold = null;

    mergeSkuData(state, 'groupOn');
  }

  // 团购
  if (tuan) {
    Object.assign(marketActivity, {
      tuan: {
        ...tuan,
        ...formatRemainTime({
          startTime: (tuan.startTime || '').replace(/-/g, '/'),
          endTime: (tuan.endTime || '').replace(/-/g, '/'),
          serverDeltaTime,
        }),
      },
    });
    // 模拟更新状态
    updateGoodsState(state, tuan);
    // goodsActivity.waitToSold = null;

    mergeSkuData(state, 'tuan');
  }

  // 秒杀
  if (seckill) {
    Object.assign(marketActivity, {
      seckill: {
        ...seckill,
        limit: {
          quota: seckill.quota || 0,
          quotaUsed: seckill.quotaUsed || 0,
          quotaCycle: seckill.quotaCycle || 0,
        },
        ...formatRemainTime({
          startTime: seckill.startAt,
          endTime: seckill.endAt,
          serverDeltaTime,
        }),
      },
    });
    // 模拟更新状态
    updateGoodsState(state, seckill);
    // goodsActivity.waitToSold = null;

    mergeSkuData(state, 'seckill');
  }

  // 限时秒杀
  if (limitedSeckill) {
    Object.assign(marketActivity, {
      limitedSeckill: {
        ...limitedSeckill,
        limit: {
          quota: limitedSeckill.quota || 0,
          quotaUsed: limitedSeckill.quotaUsed || 0,
          quotaCycle: limitedSeckill.quotaCycle || 0,
        },
        ...formatRemainTime({
          startTime: limitedSeckill.startAt,
          endTime: limitedSeckill.endAt,
          serverDeltaTime,
        }),
      },
    });
    // 模拟更新状态
    updateGoodsState(state, limitedSeckill);

    mergeSkuData(state, 'limitedSeckill');
  }

  // 积分信息
  if (pointsExchange) {
    const { id: activityId, ...otherPointsExchange } = pointsExchange;
    Object.assign(marketActivity, {
      points: {
        activityId,
        ...otherPointsExchange,
        limit: {
          quota: pointsExchange.quotaNum || 0,
          quotaUsed: pointsExchange.quotaUsed || 0,
          quotaCycle: pointsExchange.quotaCycle || 0,
        },
        // goods-info的显示单位，如金币、元、分
        pointsName: goodsExtra.pointsName,
        status: +pageFeature.showPointsButtonStatus, // 0:不展示（无积分活动、失效、未开始等），1：展示但置灰（有商品库存无积分库存，可以直接购买），2：展示（可正常兑换）
      },
    });
    // 积分暂不支持商品属性
    unsupportItemSaleProp(state);
    // 模拟更新状态
    state.originPointsSku = formatSkuData(staticSkuInfo, {
      ...staticSkuInfo,
      ...pointsExchange,
    });
    const { showPrice } = formatSkuPrice(state.originPointsSku);
    Object.assign(state.goods, {
      pointsName: goodsExtra.pointsName || '积分',
      pointsPrice: showPrice,
    });
  }

  if (presentExchange) {
    Object.assign(marketActivity, {
      present: {
        ...presentExchange,
        activityId: presentExchange.id,
        type: 'presentExchange',
        // 赠品无限购
        limit: {
          quota: 0,
          quotaCycle: 0,
          quotaUsed: 0,
        },
      },
    });
    // 赠品暂不支持商品属性
    unsupportItemSaleProp(state);

    state.originActivitySku = {
      ...originNormalSku,
      list: (originNormalSku.list || []).map((item) => {
        if (item.id === presentExchange.present.skuId) {
          item.price = item.discountPrice || 0;
          item.stockNum = 1;
        } else {
          item.stockNum = 0;
        }
        return item;
      }),
      collectionPrice: 0,
      price: 0,
      minPrice: 0,
      maxPrice: 0,
      oldPrice: originNormalSku.price,
      oldMinPrice: originNormalSku.minPrice,
      oldMaxPrice: originNormalSku.maxPrice,
      hideStock: true,
      stockNum: 1,
    };
    state.goods.price = '0.00';

    mergeSkuData(state, 'present');
  }

  // 降价拍
  if (auction) {
    Object.assign(marketActivity, {
      auction: {
        activityId: auction.id,
        ...auction,
        // totalActivityStockNum: auction.totalActivityStockNum,
        type: 'auction',
        limit: {
          quota: auction.quota || 0,
          quotaUsed: auction.quotaUsed || 0,
          quotaCycle: auction.quotaCycle || 0,
        },
      },
    });

    updateGoodsState(state, auction);
    // 降价拍原价为起拍价
    state.originActivitySku = {
      ...state.originActivitySku,
      oldPrice: auction.startPrice,
    };
    const originPrice = auction.startPrice;
    const { currentPrice } = auction;
    state.goods = {
      ...state.goods,
      price: formatPrice(currentPrice),
      oldPrice: formatPrice(originPrice),
      minPrice: currentPrice,
    };
    // goodsActivity.waitToSold = null;

    mergeSkuData(state, 'auction');
  }

  // fCode
  if (fCode) {
    Object.assign(marketActivity, {
      fCode: {
        activityId: fCode.id,
        isInvalid: fCode.isInvalid,
        ...fCode,
      },
    });
    // 模拟状态更新
    updateGoodsState(state, fCode);

    mergeSkuData(state, 'fCode');
  }

  // 券后价
  if (coupon) {
    parseCouponActivity(state, coupon);

    mergeSkuData(state, 'coupon');
  }

  // 打包一口价
  if (bale) {
    Object.assign(orderActivity, {
      bale,
    });

    mergeSkuData(state, 'bale');
  }

  // 满减送
  if (meetReduce) {
    Object.assign(orderActivity, {
      meetReduce,
    });

    mergeSkuData(state, 'meetReduce');
  }

  // 包邮工具
  if (carriageDiscount) {
    Object.assign(orderActivity, {
      carriageDiscount,
    });
  }

  // 供货商满减送
  if (supplierMeetReduce) {
    Object.assign(orderActivity, {
      supplierMeetReduce,
    });

    mergeSkuData(state, 'supplierMeetReduce');
  }

  // 门店信息
  if (physicalStores) {
    Object.assign(state.offlineStore, {
      storeList: physicalStores.physicalStoreBasicInfoModels,
      storeCount: physicalStores.shopCount,
    });
  }

  // 订单返现
  if (cashBack) {
    Object.assign(orderActivity, {
      cashBack: {
        startTime: cashBack.startTime,
        endTime: cashBack.endTime,
        // 返现方式
        type: cashBack.cashbackMethod,
        // 返现限制
        limit: cashBack.cashbackLimit,
        // 返现区间最大值
        maxCashBack: cashBack.cashbackEnd,
      },
    });
  }

  // 优惠套餐
  if (packageBuy) {
    Object.assign(orderActivity, {
      packageBuy,
    });

    mergeSkuData(state, 'packageBuy');
  }

  // 加价购
  if (plusBuy) {
    Object.assign(orderActivity, {
      plusBuy,
    });

    mergeSkuData(state, 'plusBuy');
  }

  // 第二件半价
  if (secondHalfDiscount) {
    Object.assign(orderActivity, {
      secondHalfDiscount,
    });

    mergeSkuData(state, 'secondHalfDiscount');
  }

  // 公众号涨粉
  if (fansBenefit) {
    Object.assign(orderActivity, {
      fansBenefit,
    });
  }

  // 企微涨好友
  if (wecomFansBenefit) {
    Object.assign(orderActivity, {
      wecomFansBenefit,
    });
  }

  // 分享有礼
  if (shareEncourage) {
    Object.assign(orderActivity, {
      shareBenefit: shareEncourage,
    });
  }

  // 新品发售
  if (productLaunch) {
    Object.assign(marketActivity, {
      productLaunch,
    });
  }

  // 预售
  const presale = get(state, 'goodsActivity.preSaleInfo', null);
  // 预售活动包含助力膨胀，所以放到orderActivity里
  if (presale) {
    // 定金(膨胀)类型
    // none 不参加 normal 初始膨胀金 help 助力膨胀
    let depositType = 'none';
    let depositSkuList = [];
    if (depositExpansion) {
      depositType = 'normal';
      if (originNormalSku.noneSku) {
        depositSkuList = [
          {
            skuId: originNormalSku.skuId || originNormalSku.collectionId,
            depositOffer: 0,
            ...depositExpansion.spuPrice,
          },
        ];
      } else {
        depositSkuList = get(depositExpansion, 'skuPrices', []);
      }
    } else if (helpDepositExpansion) {
      depositType = 'help';
      depositSkuList = get(helpDepositExpansion, 'skuPriceOffers', []);
    }
    Object.assign(orderActivity, {
      presale: {
        // 预售类型 0：全款预售 1：定金预售
        type: presale.preSaleType,
        // 发货类型 0：定时发货 1：付款xx天后发货
        shipType: presale.etdType,
        // 定时发货时间
        shipStartTime: presale.etdStart,
        // 付款延迟发货时间
        shipDelayDays: presale.etdDays,
        // 付款x天内发货
        shipBeforeDays: presale.etdBeforeDays,
        // 预售周期 0:短期 1:长期
        period: presale.preSalePeriod || 0,
        // 定金预售长期：尾款在定金支付后x天开始支付
        payStartDay: presale.balanceDueDayStart || 0,
        // 定金预售长期：尾款在定金支付后y天内完成支付
        payEndDay: presale.balanceDueDayEnd || 0,
        // 活动结束时间
        activityEnd: presale.preSaleActivityEnd,
        // 定金比率
        depositRatio: presale.depositRatio,
        // 定金最小值
        minDeposit: presale.minDeposit,
        // 定金最大值
        maxDeposit: presale.maxDeposit,
        // 定金支付最早时间
        preStartTime: presale.preSaleStart,
        // 定金支付截止时间
        preEndTime: presale.preSaleEnd,
        // 尾款支付最早时间
        payStartTime: presale.balanceDueStart,
        // 尾款支付最晚时间
        payEndTime: presale.balanceDueEnd,
        // 定金膨胀类型
        depositType,
        // 定金膨胀skuId对应抵消金额
        depositSkuList,
        // 定金预售类型 0-按比例的预售，1-按金额的预售
        depositPriceType: presale.depositType,
        // 无规格定金价格
        deposit: presale.deposit,
        // 有规格定金价格
        skuDeposit: presale.skuDepositModels,
        // 助力定金膨胀
        helpDepositExpansion: { ...helpDepositExpansion },
      },
    });
    // 预售暂不支持商品属性
    unsupportItemSaleProp(state);

    mergeSkuData(state, 'presale');
  }

  // 扫码优惠或者会员折扣
  if (discount) {
    Object.assign(orderActivity, {
      discount: {
        ...discount,
        title: discount.priceTitle || '',
        // 展示卡相关逻辑
        recommendedPriceModel: discount.recommendedPriceModel || {},
      },
    });
    // 模拟更新状态
    // goodsScan 扫码优惠是营销活动 customerDiscount 会员折扣不是营销活动
    updateGoodsState(state, discount);
  }

  // 限时折扣
  if (timelimitedDiscount) {
    Object.assign(orderActivity, {
      timelimitedDiscount: {
        ...timelimitedDiscount,
        title: timelimitedDiscount.priceTitle,
        // 限时折扣的quota在updateGoodsState中处理
        type: 'timelimitedDiscount',
        ...formatRemainTime({
          startTime: timelimitedDiscount.startAt,
          endTime: timelimitedDiscount.endAt,
          serverDeltaTime,
        }),
      },
    });
    // 限时折扣可以与定时开售叠加
    updateGoodsState(state, timelimitedDiscount);

    mergeSkuData(state, 'timelimitedDiscount');
  }

  // 内购裂变
  if (inSourcingFission) {
    Object.assign(marketActivity, {
      inSourcingFission: {
        ...inSourcingFission,
        // 分享标识
        inviterVoucherAlias: inSourcingFission.voucherAlias,
        ...formatRemainTime({
          startTime: inSourcingFission.startAt,
          endTime: inSourcingFission.endAt,
          serverDeltaTime,
        }),
      },
    });
    // 模拟更新状态
    updateGoodsState(state, inSourcingFission);

    mergeSkuData(state, 'inSourcingFission');
  }

  discountInvite && parseDiscountInvite(state, discountInvite);
  cashbackPro && Object.assign(orderActivity, { cashbackPro });

  // 包邮工具
  if (carriageDiscount) {
    Object.assign(orderActivity, {
      carriageDiscount,
    });
  }

  if (!state.originActivitySku) {
    // 如果不存在activitySku，用普通sku替代
    Object.assign(state, {
      originActivitySku: state.originNormalSku,
    });
  }

  if (buttonUmpTextModel) {
    Object.assign(state.buttonUmpTextModel, buttonUmpTextModel);
  }

  if (buttonUmpTimelimitModel) {
    // Object.assign(state.buttonUmpTimelimitModel, buttonUmpTimelimitModel);
    // setTimeout(() => {
    state.buttonUmpTimelimitModel = buttonUmpTimelimitModel;
    // }, 5000)
  }

  if (Array.isArray(outerActivities) && outerActivities.length) {
    const detail = outerActivities.map((val) => {
      const newVal = JSON.parse(val || '{}');
      return {
        ...newVal,
        activity: JSON.parse(newVal.activity || '{}'),
      };
    });
    state.outerActivities = detail;
  }

  state.fetchSuccess = true;
}

// 规格条需要显示已选商品及商品图片
function formatGoodsSkuBarData(state) {
  const goodsImage = state.goods.picture;
  const { itemSalePropList = [] } = state.goods;
  const { tree = [] } = state.originNormalSku;

  // 规格图片列表
  let barPictures = [];
  let barPictureName = '';
  const firstSkuTree = tree[0] || {};
  if (get(firstSkuTree, 'v.0.imgUrl', '')) {
    barPictures = firstSkuTree.v.map((value) => {
      return getCdnImageUrl(
        fullfillImage(
          value.defaultSkuImg ? goodsImage : value.imgUrl || goodsImage
        )
      );
    });
    barPictureName = firstSkuTree.k;
  }
  const barPictureCount = barPictures.length;

  // 选择规格
  const selectedSkuTree = tree.map((item) => ({
    id: item.kId,
    name: item.k,
    value: null,
  }));

  // 商品属性
  const selectedPropList = itemSalePropList.map((item) => ({
    id: item.kId,
    name: item.k,
    value: [],
  }));

  state.skuExtraData = {
    ...state.skuExtraData,
    // 是否显示规格栏
    showSkuBar: selectedSkuTree.length + selectedPropList.length > 0,
    // 规格栏图片
    barPictures: barPictures.slice(0, 5),
    // 共N种xx可选
    barPictureName,
    barPictureCount,
    // 已选规格
    selectedSkuTree,
    // 已选商品属性
    selectedPropList,
  };
}

function formatPriceCalendarData(originData) {
  const calendarData = {
    months: [],
  };

  function getDaysInfo(year, month) {
    const priceCalendar = originData.ecardPriceCalendarModelMap;

    const monthDayCount = new Date(year, month, 0).getDate();

    const days = [];
    let minPrice = Infinity;

    for (let index = 1; index <= monthDayCount; index++) {
      const info =
        priceCalendar[`${year}-${month}-${index > 9 ? index : '0' + index}`];
      let price = or(info.activityPrice, info.originPrice);

      if (typeof price !== 'undefined') {
        minPrice = price > minPrice ? minPrice : price;
        price = `¥${moneyFormat(price, true, false)}`;
      }

      days.push({
        ...info,
        date: index,
        activeHeader: info.stockNum < 50, // 库存小于某个值（是多少）才显示为高亮
        header: info.stockNum && `余${info.stockNum}`,
        footer: price,
        id: info.stockDate,
      });
    }

    return [minPrice, days];
  }

  const minPriceMap = originData.nearlyFourMonthMinPriceMap;
  const months = Object.keys(minPriceMap);

  calendarData.months = [];

  calendarData.monthDataList = months.map((monthStr) => {
    const [year, month] = monthStr.split('-');
    const price = minPriceMap[monthStr];
    const [minPrice, days] = getDaysInfo(year, month);
    calendarData.months.push({
      name: +month,
      year: +year,
      price,
      // 如果改为活动价，那么这里后端需要返回最低活动价格
      label:
        price === -1 ? '已售罄' : `¥${moneyFormat(minPrice, true, false)}起`,
    });

    return {
      year,
      month,
      days,
    };
  });

  calendarData.monthDataList.sort((a, b) => {
    const aTime = new Date(a.year, a.month);
    const bTime = new Date(b.year, b.month);
    if (aTime > bTime) return 1;

    return -1;
  });

  return calendarData;
}
// 接口拆分考虑，活动价数据得到后，需要更新商品信息，如价格等
function updateGoodsState(state = {}, activity = {}) {
  // 极速下单不参与活动
  if (state.isFastBuy) {
    activity = {};
  }

  const sku = formatSkuData(state.staticSkuInfo, {
    ...state.staticSkuInfo,
    ...activity,
  });
  if (
    // 扫码优惠
    activity.type === 'goodsScan' ||
    // 会员折扣
    activity.type === 'customerDiscount' ||
    // 限时折扣
    activity.type === 'timelimitedDiscount'
  ) {
    state.originNormalSku = sku;
    if (activity.isStarted && activity.quota) {
      // 这是普通购买所使用的限购规则(包含会员折扣、限时折扣))
      state.goods.limit.quota = activity.quota || state.goods.limit.quota;
      state.goods.limit.quotaUsed =
        activity.quota > 0
          ? activity.quotaUsed || 0
          : state.goods.limit.quotaUsed;
      state.goods.limit.quotaCycle =
        activity.quota > 0
          ? activity.quotaCycle || 0
          : state.goods.limit.quotaCycle;
    }
  } else {
    state.originActivitySku = sku;
  }

  const { showPrice, showOldPrice } = formatSkuPrice(sku);

  state.goods = {
    ...state.goods,
    price: showPrice,
    minPrice: sku.minPrice,
    maxPrice: sku.maxPrice,
    oldPrice: showOldPrice,
    oldMinPrice: sku.oldMinPrice,
    oldMaxPrice: sku.oldMaxPrice,
    stockNum: sku.stockNum,
  };
}

// 部分情况不支持商品属性，需要把相关配置删除
function unsupportItemSaleProp(state) {
  const { itemSalePropList = [] } = state.goods;
  if (itemSalePropList.length > 0) {
    state.goods.itemSalePropList = [];
    if (state.skuExtraData) {
      state.skuExtraData.selectedPropList = [];
      state.skuExtraData.showSkuBar =
        state.skuExtraData.selectedSkuTree &&
        state.skuExtraData.selectedSkuTree.length > 0;
    }
  }
}

export {
  formatUserData,
  formatDisplayData,
  formatGoodsStaticData,
  formatGoodsAsyncData,
  formatSkuData,
  formatGoodsSkuBarData,
  formatPriceCalendarData,
};
