import { get, mapKeysToCamelCase } from '@goods/utils';

export function setGlobalShare() {
  // window._global.share.otherShareData = data;
}
// 获取定金金额
// 定金金额大于0.1元，进行抹分，当计算后金额小于等于0.1元，精确到分，不足1分取1分
export function getDepositPrice(price, depositRatio = 1) {
  let depositPrice = price * depositRatio;
  // 如果定金金额大于0.1元
  if (depositPrice > 10) {
    depositPrice = Math.floor(depositPrice / 10) * 10;
  } else if (depositPrice >= 1) {
    // 当小于0.1元，精确到分，不足1分取1分
    depositPrice = Math.round(depositPrice);
  } else {
    depositPrice = 1;
  }
  return depositPrice;
}

// sku空对象
const skuEmptyObject = {
  showPrice: '',
  price: 0,
  pointsPrice: 0,
  oldPrice: 0,
  priceTitle: '',
  // 是否不显示活动标，诸如活动价和原价相同的情况
  notShowPriceTitle: false,
  soldNum: 0,
  stockNum: 0
};

export function skuListToMap(skuList = []) {
  return skuList.reduce(
    (prev, curr) => ({
      ...prev,
      [curr.skuId]: curr
    }),
    {}
  );
}

// sku外部计算时为全驼峰
export function formatSkuData(baseSkuData = {}, extraSkuData = null) {
  const { props: tree = [], messages = [], skus = [], hideStock = false } = baseSkuData;
  let skuList = [];

  let sku = {
    ...skuEmptyObject,
    hideStock,
    tree,
    list: skuList,
    messages
  };

  const baseSkuPrice = skuListToMap(baseSkuData.skuPrices);
  const baseSkuStocks = skuListToMap(baseSkuData.skuStocks);

  if (!extraSkuData) {
    // 如果没有活动叠加，使用baseSkuPrice
    skuList = skus.map((item) => {
      const skuId = item.skuId;
      return {
        ...skuEmptyObject,
        ...item,
        oldPrice: 0,
        ...baseSkuPrice[skuId],
        ...baseSkuStocks[skuId],
        id: skuId
      };
    });

    sku = {
      ...sku,
      ...baseSkuData.spuStock,
      ...baseSkuData.spuPrice,
      collectionId: get(baseSkuData, 'spuStock.skuId', 0),
      collectionPrice: get(baseSkuData, 'spuPrice.price', 0),
      list: skuList
    };
  } else {
    const extraSkuPrice = skuListToMap(extraSkuData.skuPrices);
    const extraSkuStocks = skuListToMap(extraSkuData.skuStocks);
    // 如果没有活动叠加，使用baseSkuPrice
    skuList = skus.map((item) => {
      const skuId = item.skuId;
      const oldPrice = baseSkuPrice[skuId].price;
      // 检查规格库存
      let extraSkuStockItem = extraSkuStocks[skuId];
      if (extraSkuStockItem && extraSkuStockItem.disable) {
        // 如果规格被禁用，前端重置规格库存为0
        extraSkuStockItem = {
          ...extraSkuStockItem,
          stockNum: 0
        };
      }
      // 检查规格价格
      let extraSkuPriceItem = extraSkuPrice[skuId];
      if (extraSkuPriceItem && extraSkuPriceItem.price > 0) {
        extraSkuPriceItem = {
          ...extraSkuPriceItem,
          // 活动价比原价贵，不显示活动标签
          notShowPriceTitle: extraSkuPriceItem.price >= oldPrice
        };
      }
      return {
        ...skuEmptyObject,
        ...item,
        oldPrice,
        ...extraSkuPriceItem,
        ...extraSkuStockItem,
        id: skuId
      };
    });
    sku = {
      ...sku,
      ...extraSkuData.spuStock,
      ...extraSkuData.spuPrice,
      collectionId: get(extraSkuData, 'spuStock.skuId', 0),
      collectionPrice: get(extraSkuData, 'spuPrice.price', 0),
      oldPrice: get(baseSkuData, 'spuPrice.price', 0),
      list: skuList
    };
  }

  let minPrice = +Infinity;
  let maxPrice = 0;
  let minVipSavedPrice = +Infinity;
  let maxVipSavedPrice = 0;
  let oldMinPrice = +Infinity;
  let oldMaxPrice = 0;

  const noneSku = sku.list.length === 0;
  let isAllVipPriceAdvantage = 0;
  if (noneSku) {
    sku.collectionPrice = minPrice = maxPrice = sku.price;
    oldMinPrice = oldMaxPrice = sku.oldPrice || 0;
  } else {
    // 规格总价做保护,可能spuStock跟skuStocks之和对不上,如独立库存多网点
    sku.stockNum = sku.list.reduce((prev, curr) => prev + (curr.stockNum || 0), 0);
    // 计算最大最小价
    sku.list.forEach((item) => {
      // 规格被禁用，不显示最大最小价
      if (item.disable) {
        return;
      }
      // 库存为0但总库存大于0，则不计算相应最大最小价
      if (sku.stockNum > 0 && item.stockNum === 0) {
        return;
      }

      if (item.recommendedPrice) {
        // 产品逻辑： 活动价和会员推荐价对比；
        item.isVipPriceAdvantage = item.recommendedPrice < item.price;

        if (item.isVipPriceAdvantage) {
          // 产品逻辑： 优惠的价格是原价 - 推荐价格
          const savedPrice = item.oldPrice - item.recommendedPrice;

          minVipSavedPrice = Math.min(minVipSavedPrice, savedPrice);
          maxVipSavedPrice = Math.max(maxVipSavedPrice, savedPrice);
        }
        if (isAllVipPriceAdvantage === 0) {
          isAllVipPriceAdvantage = item.isVipPriceAdvantage;
        }

        isAllVipPriceAdvantage = isAllVipPriceAdvantage && item.isVipPriceAdvantage;
      }
      minPrice = Math.min(minPrice, item.price);
      maxPrice = Math.max(maxPrice, item.price);
      oldMinPrice = Math.min(oldMinPrice, item.oldPrice);
      oldMaxPrice = Math.max(oldMaxPrice, item.oldPrice);
    });
    // 如果有规格，可能只有一个规格，所以需要赋值默认价格，使用skuPrices而非spuPrice
    sku.price = minPrice || maxPrice || 0;
    sku.oldPrice = oldMinPrice || oldMaxPrice || 0;
  }

  // 价格数据保护
  sku.price = sku.price || minPrice || maxPrice || 0;

  if (!isFinite(oldMaxPrice)) {
    oldMaxPrice = 0;
  }

  sku.oldPrice = sku.oldPrice || oldMinPrice || oldMaxPrice || 0;

  return mapKeysToCamelCase({
    ...sku,
    noneSku,
    minPrice,
    maxPrice,
    minVipSavedPrice,
    maxVipSavedPrice,
    isAllVipPriceAdvantage,
    oldMinPrice,
    oldMaxPrice
  });
}

// 格式化价格
function formatPrice(price) {
  if (price && typeof price === 'number') {
    return (price / 100).toFixed(2);
  }
  return price;
}

// 参数sku为formatSkuData之后的数据
export function formatSkuPrice(skuData = {}, pointsName = '积分') {
  const {
    pointsPriceText = '',
    price = 0,
    minPrice = -1,
    maxPrice = -1,
    pointsPrice = -1,
    oldPrice = 0,
    oldMinPrice = -1,
    oldMaxPrice = -1
  } = skuData;
  let showPrice = '';
  let showOldPrice = '';
  // 如果后端返回showPrice，则显示后端的数据
  if (pointsPriceText) {
    showPrice = pointsPriceText;
  } else if (pointsPrice > 0) {
    // 如果是单个积分价格
    showPrice = `${pointsPrice}${pointsName}`;
    if (price > 0) {
      showPrice = `${showPrice}+￥${formatPrice(price)}`;
    }
  } else if (maxPrice > minPrice && minPrice >= 0) {
    // 普通价格区间
    showPrice = `${formatPrice(minPrice)}-${formatPrice(maxPrice)}`;
  } else {
    // 非区间价格可能为0
    showPrice = `${formatPrice(price)}`;
  }
  // 原价比现价贵，则会展示
  if (oldPrice > price || oldMinPrice > minPrice) {
    if (oldMaxPrice > oldMinPrice) {
      showOldPrice = `${formatPrice(oldMinPrice)}-${formatPrice(oldMaxPrice)}`;
    } else if (oldPrice > 0) {
      // 无价格区间
      showOldPrice = `${formatPrice(oldPrice)}`;
    }
  } else {
    showOldPrice = '';
  }

  return {
    showPrice,
    showOldPrice
  };
}

// sku注入sku组件需要format
export function parseSkuData(sku) {
  const tree = sku.tree || [];
  const list = sku.list || [];

  // sku组件外部需要传入是格式化后的价格
  let price = sku.price;
  if (typeof price === 'number' && price !== 0) {
    price = (price / 100).toFixed(2);
  }
  return {
    ...sku,
    price,
    tree,
    list
  };
}

export const mergeStore = (srcStore = {}, destStore = {}) => {
  return {
    state: {
      ...srcStore.state,
      ...destStore.state
    },
    getters: {
      ...srcStore.getters,
      ...destStore.getters
    },
    actions: {
      ...srcStore.actions,
      ...destStore.actions
    },
    mutations: {
      ...srcStore.mutations,
      ...destStore.mutations
    }
  };
};

// 计算倒计时剩余时间
export const formatRemainTime = (options = {}) => {
  const { startTime = 0, endTime = 0, serverDeltaTime = 0 } = options;
  // 数据结构保护
  const serverStartAt = +new Date(startTime);
  const serverEndAt = +new Date(endTime);

  // 服务器时间与本地时间的差
  return {
    // 本地到期时间
    startAt: serverStartAt - serverDeltaTime,
    endAt: serverEndAt - serverDeltaTime,
    // 处理后的服务器时间，一定是时间戳
    serverStartAt,
    serverEndAt,
    // 保留原始服务器时间，可能为字符串
    startTime,
    endTime
  };
};

// 辛巴性能优化；如果有异步sku新数据，就要更新staticSkuInfo和originNormalSku
export function updateAsyncSku(state, spuStock, skuStocks) {
  if (spuStock || skuStocks) {
    skuStocks &&
      Object.assign(state.staticSkuInfo, {
        skuStocks
      });

    spuStock &&
      Object.assign(state.staticSkuInfo, {
        spuStock
      });

    const updatedOriginSku = formatSkuData(state.staticSkuInfo);
    state.originNormalSku = { ...updatedOriginSku };
  }
}

// 解析商品扩展信息
export function parseGoodsExtendInfo(goodsExtendData = []) {
  if (!Array.isArray(goodsExtendData) || !goodsExtendData.length) {
    return {};
  }
  const parsedGoodsExtendInfo = {};
  goodsExtendData.forEach((extendInfo) => {
    parsedGoodsExtendInfo[extendInfo.type] = extendInfo;
  });
  return parsedGoodsExtendInfo;
}
