/**
 * 会员卡推荐模块； 比如 金卡会员优惠xx
 */
import { formatSkuData } from '../helper';

export const parseDiscountInvite = (state, discountInvite = {}) => {
  const { staticSkuInfo, orderActivity } = state;

  Object.assign(orderActivity, {
    discountInvite: {
      // 展示卡相关逻辑
      recommendedPriceModel: discountInvite.recommendedPriceModel || {},
    },
  });

  const { skuPrices, spuPrice } = discountInvite;

  state.originActivitySku = formatSkuData(staticSkuInfo, {
    ...staticSkuInfo,
    skuPrices,
    spuPrice: {
      ...staticSkuInfo.spuPrice,
      ...spuPrice,
    },
  });
};
