/**
 * 券后价活动
 */
import { skuListToMap } from '../helper';

// 格式化券后价展示内容--价格部分样式特殊处理
export const formatPreferentialPriceTag = (priceTag = '') => {
  let tagContent = null;
  const priceNum = priceTag.match(/\d+(\.\d+)?/g);
  if (priceNum) {
    const tagArr = priceTag.split(priceNum[0]);
    tagContent = {
      tagPre: tagArr[0],
      price: priceNum[0],
      tagSuf: tagArr[1],
    };
  }
  return tagContent;
};

// 解析券后价活动
export const parseCouponActivity = (state, coupon = {}) => {
  const { voucherPreferenceCopyWriting, couponPreferenceModels, couponSkuPrices = [] } = coupon;
  const { orderActivity, originNormalSku } = state;
  const skuCouponPrice = {};

  (couponSkuPrices || []).forEach((it) => {
    skuCouponPrice[it.skuId] = {
      preferentialCoupon: it.useThresholdAndValueCopywriting,
      preferentialPriceTag: formatPreferentialPriceTag(it.priceTitle),
      activityId: it.activityId,
    };
  });

  Object.assign(orderActivity, {
    coupon: {
      preferentialPrice: voucherPreferenceCopyWriting,
      // optimalCouponList: couponPreferenceModels,
      couponSkuPrices,
      skuCouponPrice,
    },
  });

  const MAX_SHOW_COUNT = 20;
  state.optimalCouponList = (couponPreferenceModels || []).slice(0, MAX_SHOW_COUNT).map(coupon => {
    coupon.disabled = false;
    return coupon;
  });

  const skuPriceMap = skuListToMap(couponSkuPrices);
  state.originNormalSku = {
    ...originNormalSku,
    list: originNormalSku.list.map(item => {
      const {
        useThresholdAndValueCopywriting: preferentialCoupon,
        priceTitle: preferentialPriceTag,
      } = skuPriceMap[item.id];
      item = {
        ...item,
        preferentialCoupon,
        preferentialPriceTag: formatPreferentialPriceTag(preferentialPriceTag),
      };
      return item;
    }),
  };
};
