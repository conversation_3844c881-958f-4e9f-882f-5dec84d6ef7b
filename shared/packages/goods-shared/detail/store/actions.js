import api from '@goods/utils/api';
import Toast from '@vant/weapp/dist/toast/toast';
import setUmpLogger, { setLogger } from '@goods/common/logger';
import { setPlatfromSpecialUI } from '@goods/common/platform';

const actions = {
  fetchGoodsData({ dispatch }, query) {
    const { design, fromGoodsAutoEnterShop } = query;
    delete query.design;
    delete query.fromGoodsAutoEnterShop;
    return api
      .get({
        url: '/wscgoods/weapp/detail-v2.json',
        query: {
          ...query,
          notQueryVoucher: 1, // 不再查询优惠券列表
          notQueryYouzanGuarantee: true, // 小程序 2.67版本后的默认传true(默认不查询有赞担保信息)
          withSeckillError: true, // 为了兼容 2.49 版本错误，只有在有 withSeckillError 这个参数的情况下才会在检查到店铺不支持秒杀
        },
        config: {
          priority: 'high',
        },
      })
      .then((res) => {
        const { goods } = res.goodsData;

        if (goods.buyWay === 0) {
          return Promise.reject({
            message: '外链商品不支持展示',
            NO_TRY: true,
          });
        }
        dispatch('updateGoodsStaticData', { ...res, design });

        if (fromGoodsAutoEnterShop) {
          dispatch('getUmpCouponList');
        }
        return res;
      });
  },

  // 初始化商品 - 基础商品信息，为接口拆分做准备
  updateGoodsStaticData(
    { commit },
    {
      goodsData,
      pageParams,
      design,
      displayData,
      apolloSwitch,
      offlineData,
      shopMetaInfo,
      platformSpecialUI,
      businessConfigs,
    }
  ) {
    commit('updateBusinessConfigs', businessConfigs);
    commit('initStaticData', {
      goodsData,
      pageParams,
      offlineData,
      shopMetaInfo,
    });
    commit('SET_PAGE_SWITCH', apolloSwitch);

    if (IS_EXTENTION) {
      design.unshift({
        type: 'actions',
        custom: true,
      });

      design.push({
        custom: true,
        type: 'status',
      });
    }

    commit('updatePageDesign', design);
    commit('initDisplayData', displayData);
    setPlatfromSpecialUI(platformSpecialUI);
  },

  // 补充增量商品信息，如活动信息
  updateGoodsAsyncData({ commit, dispatch, state }, userState) {
    // 获取异步数据
    commit('updateGoodsAsyncData', {
      ...userState,
    });

    const clientTimestamp = Date.now();
    const { serverTimestamp = Date.now() } = userState;
    commit('updateServerTime', { serverTimestamp, clientTimestamp });

    dispatch('initPayChannels');
    dispatch('setSalesmanData');

    // 营销埋点
    setUmpLogger(
      userState.parsedActivities,
      state.businessConfigs.activityIdMap
    );
  },

  reload() {
    wx.startPullDownRefresh();
  },

  setSalesmanData({ state, commit }, salesman) {
    const {
      goods,
      originSku,
      originNormalSku,
      originActivitySku,
      marketActivity,
      orderActivity,
    } = state;
    const {
      luckyDrawGroup,
      helpCut,
      groupOn,
      seckill,
      tuan,
      auction,
      inSourcingFission,
    } = marketActivity;

    let salesmanSku = originSku;
    if (
      luckyDrawGroup ||
      helpCut ||
      groupOn ||
      seckill ||
      tuan ||
      auction ||
      inSourcingFission
    ) {
      salesmanSku = originActivitySku;
    } else if (orderActivity.timelimitedDiscount) {
      salesmanSku = originNormalSku;
    }

    // 把price和goods-id设置一下就行了
    // sharedStore.set('salesman-icon', {
    //   goodsData: {
    //     itemId: goods.id,
    //     minPrice: salesmanSku.minPrice,
    //     maxPrice: salesmanSku.maxPrice,
    //     imageUrl: goods.picture,
    //     title: goods.title,
    //     // url: window.location.href,
    //   },
    // });
    commit('setSalesmanData', {
      goodsPrice: salesmanSku.maxPrice,
      goodsId: goods.id,
      salesmanSku,
      ...salesman,
    });
  },

  // // 初始化页面状态
  initPageState({ state, getters, dispatch }) {
    // 判断是否展示零售店铺信息
    if (getters.isRetailShop) {
      dispatch('getRetailShopInfo');
    }

    // 电子卡券价格日历
    if (getters.isPriceCalendar) {
      // dispatch('fetchListByDate');
      dispatch('fetchNearDateList');
    }

    // 补货提醒
    const { showStockLackReminder } = state.shopConfig;
    if (showStockLackReminder) {
      dispatch('getStockRemindStatus');
    }
  },

  // // 倒计时结束
  countdownEnded({ dispatch }) {
    dispatch('reload');
  },

  // 获取导航项距离顶部值
  getNavItemTop({ getters, commit }, payload = {}) {
    const { scrollTop = 0, self } = payload;
    const navSelectorList = getters.navTabList.map(
      (it) => `.page-goods >>> #js-nav-${it.selector}`
    );
    const query = self.createSelectorQuery();
    const navItemTopList = [];
    query
      .selectAll(navSelectorList.join(','))
      .boundingClientRect((rect) => {
        if (!rect) return;
        rect.reduce((pre, cur) => {
          if (cur.id && cur.id !== pre.id) {
            navItemTopList.push(cur.top + scrollTop);
          }
          return cur;
        }, {});
        commit('setNavItemTopList', navItemTopList);
      })
      .exec();
  },

  // 无场景点击 sku，优先选择使用上次选择的 sku 场景
  showNearSku({ getters, state, dispatch }) {
    if (getters.isSkuBarDisabled) {
      return;
    }
    const skuScene = state.skuConfig.skuScene;
    if (!skuScene) {
      // 如果第一次按，则显示默认sku，根据底部按钮位置确定，最右边最优先
      dispatch('showDefaultSku');
      return;
    }

    setLogger({
      et: 'click', // 事件类型
      ei: 'click_sku', // 事件标识
      en: '点击SKU模块', // 事件名称
    });
    const {
      accountUnionScene: ACCOUNT_UNION_SCENE = {},
    } = state.businessConfigs;
    dispatch('triggerSku', {
      skuScene,
      accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
    });
  },

  // // 显示/隐藏遮罩
  toggleGoodsMask({ commit }, showMask = false) {
    commit('toggleGoodsMask', showMask);
  },

  handleShareClick({ commit, getters, state, dispatch }) {
    const app = getApp();

    // 判断分享有礼活动获取shareTag
    dispatch('handleShareBenefitTag');

    commit('showSharePop');

    app.logger.log({
      et: 'click',
      ei: 'goods_share', // 与h5埋点保持一致
      en: '分享商品',
      params: {
        alias: state.goods.alias,
        share_method: 'wx_share',
        share_benefit_id: getters.shareBenefitActivity?.activityId,
      },
    });
  },

  showCommonToast({ dispatch }, options = {}) {
    if (!options) {
      return;
    }
    // autoReload 单位为秒
    const { message = '', autoReload = 0 } = options;
    if (message) {
      const toast = Toast.loading({
        message,
        duration: (autoReload || 2) * 1000,
        forbidClick: !!autoReload,
      });
      if (autoReload) {
        let remainTime = autoReload;
        const updateTime = () => {
          remainTime--;
          toast.message = `${message}(${remainTime}s)`;
          if (remainTime > 0) {
            setTimeout(updateTime, 1000);
          } else {
            dispatch('reload');
          }
        };
        updateTime();
      }
    }
  },

  initPayChannels({ commit, state, getters }) {
    const { bigButtonsData = [] } = getters;

    /** 定制店铺不支持先用后付，因为走了老收银台 */
    if (ECLOUD_MODE) {
      return;
    }

    if (
      bigButtonsData.length === 1 &&
      bigButtonsData[0]?.skuScene === 'addCart'
    ) {
      return;
    }

    const {
      goods: { maxPrice: price, isPhysical, id: goodsId } = {},
      parsedActivities = {},
      payWays: { priorUse, instalment } = {},
      isFastBuy,
      shopConfig: { isSecuredTransactions } = {},
      yzGuarantee,
    } = state;

    api
      .get({
        url: '/wscgoods/detail-api/pay-channels',
        data: {
          price,
          activities: JSON.stringify(
            Object.keys(parsedActivities).filter(
              (key) => parsedActivities[key] && key !== 'pageType'
            )
          ),
          isPriorUse: +priorUse,
          isFastBuy: +!!isFastBuy,
          isPhysical: +!!isPhysical,
          isSecuredTransactions: +!!isSecuredTransactions,
          isInstalment: +instalment,
          isYzGuarantee: +!!yzGuarantee,
        },
      })
      .then(({ channels, abTraceId }) => {
        channels && commit('setPayChannels', channels);
        abTraceId && commit('setPayChannelsAbTraceId', abTraceId);

        const app = getApp();
        app.logger?.log({
          et: 'custom',
          ei: 'wsc_g_pay_channels_recommend_view',
          en: '商详页加载支付方式推荐位',
          params: {
            goodsId,
            abTraceId,
          },
        });
      });
  },

  setPageScrollLocked({ commit }, locked = true) {
    commit('setPageScrollLocked', locked);
  },
};

export default actions;
