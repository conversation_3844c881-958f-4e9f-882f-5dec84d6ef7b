import {
  formatDisplayData,
  formatGoodsAsyncData,
  formatGoodsStaticData,
  formatGoodsSkuBarData,
} from './utils/parse';

const mutations = {
  updatePageDesign(state, design) {
    state.design = design;
  },

  updateServerTime(state, { clientTimestamp, serverTimestamp }) {
    state.serverDeltaTime = serverTimestamp - clientTimestamp;
    state.serverTimestamp = serverTimestamp;
    state.clientTimestamp = clientTimestamp;
  },

  SET_PAGE_SWITCH(state, pageSwitch) {
    state.pageSwitch = pageSwitch;
  },

  initStaticData(state, { pageParams, goodsData, offlineData, shopMetaInfo }) {
    formatGoodsStaticData(state, {
      pageParams,
      goods: goodsData,
      offlineData,
      shopMetaInfo,
    });
    formatGoodsSkuBarData(state);

    state.pageParams = pageParams;
  },

  initUserData(state, payload) {
    Object.assign(state.buyConfig, {
      // 是否公众号粉丝
      isFans: payload.isFans,
      // 是否必须关注购买
      noFansBuy: !payload.isFansBuy,
    });
  },

  initDisplayData(state, payload) {
    formatDisplayData(state, payload);
  },

  updateGoodsAsyncData(state, payload) {
    formatGoodsAsyncData(state, payload);
  },

  updateCalendarData(state, { skuId, calendarData }) {
    state.priceCalendarData = {
      ...state.priceCalendarData,
      activeSkuId: skuId,
      [skuId]: calendarData,
    };
  },

  updateCalendarActiveSkuId(state, skuId) {
    state.priceCalendarData.activeSkuId = skuId;
  },

  // 显示/隐藏遮罩
  toggleGoodsMask(state, showMask = false) {
    state.displayPop.maskShow = showMask;
  },
  updateMiniButtons(state, btns) {
    state.miniButtons = btns;
  },
  UPDATE_SHOP_DATA(state, payload = {}) {
    state.shop = {
      ...state.shop,
      ...payload,
    };
  },
  setSalesmanData(state, payload = {}) {
    state.salesman = {
      ...state.salesman,
      ...payload,
    };
  },
  hideSharePop(state) {
    state.displayPop.sharePopShow = false;
  },

  // 显示分享
  showSharePop(state) {
    state.displayPop.sharePopShow = true;
  },

  SET_BOTTOM_HRIGHT(state, height) {
    state.bottomHeight = height;
  },

  setNavItemTopList(state, itemTopList = []) {
    state.navItemTopList = itemTopList;
  },

  setFirstPageRendered(state, rendered) {
    state.firstPageRendered = rendered;
  },

  setPayChannels(state, payload) {
    state.payChannels = payload;
  },

  setPayChannelsAbTraceId(state, payload) {
    state.payChannelsAbTraceId = payload;
  },

  setPageFinished(state, rendered) {
    state.pageFinished = rendered;
  },
  updateBusinessConfigs(state, payload) {
    if (payload) {
      state.businessConfigs = payload;
    }
  },

  setPageScrollLocked(state, locked) {
    state.isPageScrollLocked = locked;
  },
};

export default mutations;
