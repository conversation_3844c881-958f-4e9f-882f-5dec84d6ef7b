import { mergeStore } from './utils/helper';

import imageBlockStore from '@goods/containers/image-block/store';
import baseInfoStore from '@goods/components/base-info-block-common/store';
// import promotionStore from '@goods/containers/promotion-block/store';
// import baseInfoStore from '@goods/containers/base-info-block/store';
import promotionStore from '@goods/containers/promotion-block/store';
import activityStore from '@goods/containers/activity-block/store';
// import umpExtraStore from '@goods/containers/ump-extra-block/store';
import groupStore from '@goods/containers/group-block/store';
import goodsReviewStore from '@goods/containers/goods-review-block/store';
import shopStore from '@goods/containers/shop-block/store';
import multiStoreStore from '@goods/containers/multi-store-block/store';
// import goodsDetailStore from '@goods/containers/goods-detail-block/store';
import goodsDetailStore from '@goods/containers/goods-detail/store';
// import goodsFixedInfo from '@goods/containers/goods-fixed-info/store';
import recommendStore from '@goods/containers/recommend-block/store';
import submitStore from '@goods/containers/submit-block/store';
import skuStore from '@goods/containers/sku-block/store';
import tradeCarousel from '@goods/containers/trade-carousel/store';
import navBarStore from '@goods/containers/nav-bar-block/store';
import goodsRankingStore from '@goods/containers/goods-ranking-block/store';
// // import loginStore from '@goods/containers/login-block/store';
// import followStore from '@goods/containers/follow-block/store';
import goodsExtraStore from '@goods/containers/goods-extra-block/store';
import shopNoteStore from '@goods/containers/shop-note-block/store';

const stores = [
  imageBlockStore,
  baseInfoStore,
  // promotionStore,
  // baseInfoStore,
  promotionStore,
  activityStore,
  // umpExtraStore,
  groupStore,
  goodsReviewStore,
  shopStore,
  multiStoreStore,
  goodsDetailStore,
  // goodsFixedInfo,
  recommendStore,
  submitStore,
  skuStore,
  // // loginStore,
  // followStore,
  tradeCarousel,
  navBarStore,
  goodsRankingStore,
  goodsExtraStore,
  shopNoteStore
];

export default stores.reduce((a, b) => mergeStore(a, b), {});
