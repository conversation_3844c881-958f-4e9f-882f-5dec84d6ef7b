import { get } from '../utils';

const app = getApp();
const adConfig = get(app, 'globalData.adData', {});
const state = {
  firstPageRendered: false,
  isPageScrollLocked: false,
  pageFinished: false,
  bottomHeight: 40,
  // 异步数据标志位，失败不可点击购买
  fetchSuccess: false,
  // 服务器时间，用于校正本地时间
  serverDeltaTime: 0,
  clientTimestamp: +new Date(),
  serverTimestamp: +new Date(),

  // 有赞云模板 将数组转行成驼峰对象
  design: {
    top: [],
    main: [],
    pic: [],
    bottom: [],
  },

  // 环境配置
  env: {
    platform: 'weixin',

    isMobile: true,
    isWeixin: true,
    // 微信支付环境
    wxpayEnv: {},
    // 支付宝支付环境
    alipayEnv: {},
    // isIOS: ua.isIOS(),
    // isAndroid: ua.isAndroid(),
    // app版本
    version: '',
    // 是否需要自动展示sku弹层
    showSku: false,
    // 下单是否必须登录
    needAjaxLogin: false,
    // 第三方重定向登录标识
    redirectLoginByPlatform: false,
    // 是否是全局购物车
    isAllCart: false,
    // 是否是新希望店铺
    isNewHopeKdt: false,
    // 隐藏底部栏
    hideGoodsBottom: false,
  },

  pageSwitch: {},

  // 公众号配置
  mpData: {
    // 公众号账号、二维码
    account: {},
    // 公众号店铺
    shop: {},
  },

  // 广告配置
  // TODO 不确定对不对
  adConfig: {
    isGdt: adConfig.gdt_vid,
    // 广点通版本号
    version: adConfig.gdt_vid || '',
    adType: adConfig.qz_gdt || '',
  },

  // 用户状态
  user: {
    // 登录状态标识，标识是否是登录组件登录成功状态
    logined: true,
    // 是否是管理员账户
    isAdmin: false,
    // 粉丝id
    buyerId: app.getBuyerId() || 0,
  },

  // 下单配置
  buyConfig: {
    // 是否公众号粉丝
    isFans: false,
    // 非粉丝不可购买
    noFansBuy: false,
    bookKey: '',
  },

  // app engine
  appEngineConfig: null,

  // open app 配置
  appConfig: null,

  // 页面基本配置 传参等，不会被重置
  pageParams: null,

  // 主图信息
  picture: {
    // 商品图片列表
    pictures: [],
    // 服务端评估图片高度
    height: 320,
  },

  // 视频
  video: {
    width: 0,
    height: 0,
    coverUrl: '',
    videoUrl: '',
    // 统计播放
    countPlayedUrl: '',
  },

  // 商品基本信息
  goods: {
    type: 'normal',
    id: 0,
    alias: '',
    title: '',
    // 商品页面路径
    path: '',
    // 商品头图，用于sku展示
    picture: '',
    // 商品卖点，页面子标题内容
    sellPoint: '',
    // 划线价格
    origin: '',

    // 是否上架
    isDisplay: 0,
    // 是否外链商品
    isOutlink: false,
    outlinkUrl: '',
    // 商品是否被收藏，有赞精选
    isGoodsCollected: false,
    // 是否仅限特定会员购买 || 限定积分购买
    limitBuy: false,
    // 商品是否参与f码活动
    isSupportFCode: false,

    // 是否是虚拟商品，包含虚拟商品/电子卡券/优惠券
    isVirtual: 0,
    // 是否是电子卡券
    isVirtualTicket: 0,
    // 是否是优惠券
    isVirtualCoupon: 0,
    // 是否是海淘
    isHaitao: false,
    // 餐饮
    isCatering: false,
    // 是否是酒店商品
    isHotel: false,

    // 风险店铺提示
    risk: null,
    // 显示积分商品价格
    pointsPrice: '',
    // 积分显示名称，如金币、积分
    pointsName: '积分',
    // 显示价格
    price: '0.00',
    // 最小价格
    minPrice: 0,
    // 最大价格
    maxPrice: 0,
    // 销量
    soldNum: 0,
    // 库存
    stockNum: 0,
    // 隐藏库存
    hideStock: false,
    // 总库存
    totalStockNum: 0,
    // 当前商品展示的限购信息
    limit: {
      // 商品限购数
      quota: 0,
      // 限购周期
      quotaCycle: 0,
      // 已购买数
      quotaUsed: 0,
      // 每单限购
      singleQuota: 0,
      // 起售数量
      startSaleNum: 1,
    },
    // 子标题
    subTitle: '',
    // 商品属性
    itemSalePropList: [],
    // 0: 不是自建商品； 1： 是网店自建商品
    createdType: 0,
    // 商品是否支持七天无理由退换货
    supportUnconditionalReturn: false,
    // 换货
    barterModel: {
      // 是否支持换货，1 支持 0 不支持
      isSupportBarter: 0,
    },
    // 商品极速退款
    supportQuickRefund: false,
    // 商品品牌相关
    brandInfoModel: {
      // 是否展示品牌字段: 1 展示 0 不展示
      authorizationMark: 0,
      // 商品认证图
      brandAuthenticationImgs: [],
    },
    buyLimitGuideInfo: {},
  },

  // 商品类目
  property: {
    keys: [],
    values: [],
  },

  // 所有活动
  parsedActivities: {},

  payChannels: null,
  payChannelsAbTraceId: '',

  // 这个state会根据不同sku做展示
  limit: {
    // 商品限购数
    quota: 0,
    // 限购周期
    quotaCycle: 0,
    // 已购买数
    quotaUsed: 0,
    // 每单限购
    singleQuota: 0,
  },

  supplier: {
    // 分销商对应的供货商kdtId
    kdtId: '',
    // 推广模式
    mode: '',
  },

  // 分销员
  salesman: {
    // 分销员标识 seller
    alias: '',
    // 分销员应用名称
    name: '',
    // 分销员icon，分享图标
    icon: '',
    // 二维码
    qrCode: '',
    // 是否可以分享
    goodsCanShare: false,
    // 佣金比率，预计返佣
    cpsRate: 0,
    // 当前商品预计佣金
    commission: 0,
    // 短链接
    shareLink: '',
    // 商品价格
    goodsPrice: 0,
    goodsId: 0,
  },

  // 退款模型
  refund: {
    // 是否支持退款（包含虚拟商品和电子卡券）
    isSupport: 1,
    // 退款方式
    type: '',
    // 退款时间区间
    interval: '',
  },

  // 店铺信息
  shop: {
    kdtId: 0,
    rootKdtId: 0,
    logo: '',
    name: '',
    city: '',
    province: '',
    address: '',
    phone: '',
    // 7表示零售店铺
    shopType: '',
    // 是否是总店预览模式，通过query和连锁模式判断
    isHqShopPreview: '',
  },

  // 店铺配置
  shopConfig: {
    // 商品页是否展示立即购买按钮
    isShowBuyBtn: false,
    // 是否是有赞担保
    isYouzanSecured: false,
    // 是否加入担保交易
    isSecuredTransactions: false,
    // 是否店铺关闭/歇业
    isCloseBusiness: false,
    // 是否开启推荐商品
    showRecommendGoods: false,
    // 是否开启销量与成交记录
    showBuyRecord: false,
    // 是否开启商品评价
    showCustomerReviews: false,
    // 评价会员标签展示 0:评价展示会员等级 1:评价展示会员名称）
    evaluationMemberLabel: 0,
    // 是否点亮有赞公益
    showPublicBenefit: false,

    // 是否开启买家秀
    showBuyerShows: false,
    // 是否开启个性化商品推荐
    showPersonalRecommendGoods: false,
    // 是否精简版微商城
    isSimplifiedWsc: false,
    goodsDetailBuyRecord: null,
    // 新的跑马灯开关，支持评价、好评等内容
    goodsTradeMarquee: null,
    // 是否支持运费险
    // 店铺运费险对于分销、礼品卡、酒店、周期购、会员卡、虚拟商品无效
    supportFreightInsurance: false,
    // 是否隐藏购物车按钮
    hideShoppingCart: false,
    // 是否有线下门店
    hasPhysicalStore: false,
    // 微信自有支付
    isWeixinPayOrigin: false,
    // 主体认证类型 0 初始化状态 1 个人认证，2企业认证，3 个体工商户 4其他组织，5政府及事业单位  98网店 99认证被驳回
    principalCertType: 0,
    // 品牌认证类型 0 初始化状态 1 旗舰店 2专卖店 3直营店 4 专营店 5 卖场 99 认证被驳回
    brandCertType: 0,
    showDefaultCartText: true,
    cartText: '购物车',
    // 是否开启微信担保 0 未开通 1 开通
    weixinTransactionSecured: '0',
    // 好评率配置
    goodsFavorableRate: null,
    // 是否公益店铺
    publicWelfareShopSwitch: 0,
    actHaiGouIcon: null,
    // 是否开启补货提醒
    showStockLackReminder: false,
  },

  // 公众号关注信息
  followInfo: {
    qrcodeWeixinImg: '',
    // 是否请求加载过数据
    qrcodeWeixinFetched: false,
  },

  // 店铺担保配置
  guarantee: {
    // 是否加入有赞担保
    on: false,
    // 担保样式
    style: 0,
    // 担保详细文案
    desc: [],
  },

  // 多网点信息
  multistore: {
    id: 0,
    name: '',
    address: '',
    // 是否开启网点切换
    openMultiStoreSwitch: false,
    // 是否开启网点隐藏
    hideStore: false,
    distance: 0,
    // 其他可售门店
    salableStores: [],
  },

  // 支付方式，如在线支付，货到付款
  payWays: {
    online: true,
    // 是否支持货到付款
    cashOnDelivery: 0,
    // 是否支持分期付款,
    instalment: 0,
  },

  // 门店信息
  offlineStore: {
    // 门店列表
    storeList: [],
    // 门店数量
    storeCount: 0,
  },

  // 配送信息
  distribution: {
    // 运费
    postage: '',
    // 配送地址
    deliveryAddress: {},
    // 是否支持快递
    supportExpress: false,
    // 是否支持自提
    supportSelfFetch: false,
    // 是否支持同城送
    supportLocalDelivery: false,
    // 快递费用
    expressFee: '',
    // 同城送费用
    localDeliveryFee: '',
  },

  // 商品tab栏数据
  goodsDetail: {
    // 默认激活的详情tab
    defaultType: 'goods',
    // 用来确定商品详情微页面正常加载，加载后才能显示推荐商品
    featureLoaded: false,
    // 模板风格 0：普通版 1：极速版
    // 小程序目前不支持极速版
    tplStyle: 0,
    // 富文本内容
    richText: '',
    // 商品详情组件数据
    showcaseComponents: [],
  },

  // 成交记录
  tradeRecords: {
    list: [],
    page: 1,
    loading: false,
    finished: false,
  },

  // sku 详细信息，后端传入
  originSku: {}, // 原始格式化后的商品SKU，原始数据，不修改
  originNormalSku: null, // 原始格式化后的商品SKU，含会员价格
  originActivitySku: null, // 原始格式化后的活动SKU
  originPointsSku: null, // 原始格式化后的积分SKU

  // 当前商品页活动标存储一份，方便拼团或者积分兑换下单传参用
  activityInfo: {
    // 活动alias
    activityAlias: '',
    // 活动id
    activityId: 0,
    // 活动类型
    activityType: 0,
  },

  // sku组件相关数据，不依赖ajax数据
  // 用于下单前函数校验等
  skuConfig: {
    // 商品场景，如酒店、虚拟商品
    skuType: 'normal',
    // 使用的交易函数(action方法名)，buy addCart addWish
    submitType: '',
    // 显示场景/下单场景，如立即购买、加入购物车、活动购买等
    skuScene: '', // actBuy normalBuy addCart
    // 用于account-union校验
    accountUnionScene: '', // buy addCart
    // 这些均为下单前配置，setSkuConfig时根据skuScene/skuType进行设置
    // 是否同时显示购物车和立即购买
    isMultiBtn: false,
    // 是否是购物车，如果有isMultiBtn，则cart为辅按钮
    isAddCart: false,
    isGift: false,
    isPoints: false,
    isAddWish: false,
    // 下单时配置，由于登录校验是异步操作，所以需要额外字段记录下单类型
    isAjaxing: false,
  },

  // 主要用于sku自定义数据展示以及一些特殊商品的下单字段
  skuExtraData: {
    showSkuBar: false,
    // 规格图片
    barPictures: [],
    // 有图片的规格名称
    barPictureName: '',
    barPictureCount: 0,
    barCalendar: [],
    initialSkuId: 0,
    selectedSkuTree: [], // 格式化后的信息
    selectedSku: {}, // 原始sku 已选id信息
    selectedSkuComb: null,
    selectedSkuDate: [], // 选中的价格日历
    selectedSkuDateId: '', // 选中的 skuId，价格日历和普通skuId不一致
    selectedScale: null, // 阶梯价
    installmentRate: 0, // 费率
    selectedInstallmentPeriod: 0, // 分期数, 0 表示不使用分期支付
    selectedProp: {}, // 已经选中的商品属性
    selectedPropList: [], // 已经选中格式化后的商品属性
  },

  // 用于展示的sku数据
  // 流程为，按需获取sku / activitySku => 赋值给 state.sku => 运算 getters.sku => 渲染
  skuData: {},

  staticSkuInfo: {
    spuPrice: {},
    skuPrices: [],
    spuStock: {},
    skuStocks: [],
  },

  // postData，下单表单提交数据
  // 因为校验登录需要缓存数据
  postData: {},

  flowActInfo: null,

  iconUrl: '',

  // 页面展示层控制；后端获得的数据，保护goods的逻辑与display的逻辑
  display: {
    // 禁止购买原因
    forbidBuyReason: '',
    // 是否展示小按钮列表，超过一定数量底部左侧小按钮会收起用更多替代
    showPlusButtons: false,
    // 是否展示购买按钮
    showBuyBtn: true,
    // 是否展示加入购物车按钮
    showCartBtn: true,
    // 是否展示积分兑换按钮
    showPointsBtn: false,
    // 是否展示不可购买按钮，包含 下架、售罄等
    showForbidBuyBtn: false,
    // 是否展示送礼按钮
    showGiftBtn: false,
    // 是否展示店铺按钮
    showMiniShopBtn: false,
    // 是否展示小购物车按钮
    showMiniCartBtn: false,
    // 是否展示小积分兑换按钮
    showMiniPointsBtn: false,
    // 是否展示分享按钮
    showShareBtn: false,
    // 是否展示im按钮
    showImBtn: false,
    // 是否展示心愿单
    showWishBtn: false,
    // 是否展示底部推荐
    showBottomRecommendGoods: false,
    // 是否展示默认的按钮文案
    showDefaultBuyButtonText: true,
    // 自定义购买按钮文案
    buyButtonText: '',
    // 展示老店介绍
    showOldShopPopup: false,
    // 展示公益介绍
    showWelfarePopup: false,
    // 是否展示去活动列表的小按钮
    showMiniActivityButton: false,
    // 是否展示商品开售提醒
    showSaleReminder: false,
  },

  // 浮层处理，不依赖后端数据
  displayPop: {
    // 是否显示分享
    sharePopShow: false,
    // 是否sku弹层
    skuPopShow: false,
    // 全局遮罩层
    maskShow: false,
    // 显示更多按钮
    miniMorePopShow: false,
    // 是否展示关注店铺
    followPopShow: false,
    // 是否展示团购返现弹层
    tuanPopShow: false,
    // 是否展示订单返现弹层
    showCashBackPopup: false,
    // 是否展示日历
    showDatePicker: false,
    // 是否显示优惠券弹窗
    showCouponPopUp: false,
    // 是否展示先用后付弹窗
    showPriorUseFirstBar: false,
  },

  // 价格日历数据
  priceCalendarData: {
    priceBarList: [],
    // 当前 sku 日历选中项，在切换 sku 的时候需要清除
    selected: null,
    // 当前展示的 skuId
    activeSkuId: null,
  },

  // 各种活动，可能不存在，所以先观察

  // 商品级别营销活动，需要带 activityId/alias 到下单流程，才能触发活动
  marketActivity: {
    // 0元拼团
    luckyDrawGroup: null,
    // 砍价0元购
    helpCut: null,
    // 拼团商品
    groupOn: null,
    // 秒杀商品
    seckill: null,
    // 积分商品
    points: null,
    // 赠品，通过幸运大抽奖等方式获取
    present: null,
    // 团购
    tuan: null,
    // 降价拍
    auction: null,
    // fCode
    fCode: null,
    // 内购裂变
    inSourcingFission: null,
    // 新品发售
    productLaunch: null,
    // 视频号限时秒杀
    limitedSeckill: null,
  },

  // 订单级别活动
  // 不需要携带 alias，在交易流程会自动触发折扣/返现
  orderActivity: {
    // 打包一口价
    bale: null,
    // 订单返现
    cashBack: null,
    // 返储值金 + 返现
    cashbackPro: null,
    // 满减送营销活动
    meetReduce: null,
    // 供货商满减送
    supplierMeetReduce: null,
    // 包邮工具
    carriageDiscount: null,
    // 优惠套餐
    packageBuy: {},
    // 加价购
    plusBuy: null,
    // 预售信息
    presale: null,
    // 扫码会员或者会员折扣
    discount: null,
    // 限时折扣
    timelimitedDiscount: null,
    // 引导用户进行开会员卡, 小程序上暂时不支持
    discountInvite: null,
    // 第二件半价
    secondHalfDiscount: null,
    // 公众号涨粉
    fansBenefit: null,
    // 企微涨好友
    wecomFansBenefit: null,
    // 券后价
    coupon: null,
    // 分享有礼
    shareBenefit: null,
  },

  // 商品自身属性带来的折扣活动
  goodsActivity: {
    // 定时开售
    waitToSold: null,
    // 电子卡券
    virtualTicket: null,
    // 优惠券
    virtualCoupon: null,
    // 周期购 活动信息 periodBuyExtra
    periodbuy: null,
    // 预售信息
    preSaleInfo: null,
    // 海淘
    haitao: null,
    // 酒店
    hotel: null,
    // 餐饮
    catering: null,
    // 送礼商品
    gift: null,
    // 仅限会员购买
    limitBuy: null,
    // 心愿单
    wish: null,
  },

  // 促销活动限流
  orderPromotionLimit: false,
  // 促销活动内容
  orderPromotion: [],

  // 优惠券列表
  umpCouponList: [],

  // 最佳优惠券
  optimalCouponList: [],

  // 小按钮信息，方便更新
  miniButtons: {},

  // 置顶的的商品信息
  showGoodsFixedInfo: false,

  // 浮动交易记录
  tradeRecordsV2: [],

  // 个人中心标题
  userCenterTitle: '个人中心',

  // 商品扩展信息
  goodsExtendInfo: {
    // 店铺笔记
    SHOP_NOTE: null,
    // 商品热榜
    HOT_RECOMMEND: null,
  },

  // 顶部导航元素top值列表
  navItemTopList: [],
  saas: null,

  buttonUmpTextModel: {
    buyButtonText: '',
    umpTag: '',
  },

  buttonUmpTimelimitModel: {
    leftTime: 0,
    umpTitle: '',
  },
  businessConfigs: {},
  // 商详页开放活动sku数据使用
  activitySkuData: {},
  // 外部活动
  outerActivities: [],
};

export default state;
