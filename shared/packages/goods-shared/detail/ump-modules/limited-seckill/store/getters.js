import { get, formatPrice } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';

export default {
  customStock() {
    return true;
  },

  currentActivity(state) {
    return get(state, 'marketActivity.limitedSeckill', {});
  },

  showActivityBlock(state, getters) {
    return getters.activityRules.label;
  },

  // 是否显示腰峰 活动进行中
  showActivityBanner(state, getters) {
    const activity = getters.currentActivity;
    if (JSON.stringify(activity) == '{}') {
      return false;
    }
    return getters.activityStarted;
  },

  // 显示销量
  showSoldNum() {
    return true;
  },

  // 限时剩余
  showGoodsStock() {
    return true;
  },

  // 限时秒杀
  isLimitedSeckill(state, getters) {
    return getters.showActivityBanner;
  },

  // 普通商详隐藏tag
  hideOriginPriceTag(state, getters) {
    return getters.activityNotStart || getters.activityHasFinish;
  },

  // 展示限时秒杀pop
  isDisplayLimitedSeckillPop(state, getters) {
    return (getters.limitedSeckillPiece > 0 && getters.activityStarted);
  },

  // 剩余件数
  limitedSeckillPiece(state) {
    return get(state, 'goods.stockNum', 0);
  },

  // 不支持f码
  isSupportFCodeBuy() {
    return false;
  },

  // sku面板价格标签
  goodsPriceTag(state, getters) {
    const activity = getters.currentActivity;
    if (JSON.stringify(activity) == '{}') return '';
    return '限时秒杀';
  },

  // sku活动标记
  activitySkuPriceTags(state, getters) {
    return [getters.goodsPriceTag];
  },

  // 活动未开始
  activityNotStart(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = activity.startAt - now;
    return delta > 0;
  },

  // 活动已结束
  activityHasFinish(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = activity.endAt - now;
    return delta < 0;
  },

  // 活动已经开始
  activityStarted(state, getters) {
    return !getters.activityNotStart;
  },

  // 倒计时模块
  countdown(state, getters) {
    const activity = state.marketActivity.limitedSeckill;
    if (!activity) {
      // 因为是异步加载活动，初始值可能不存在
      return null;
    }
    const { endAt } = activity;
    const desc = '距结束还剩';
    const end = endAt;
    // 活动未开始或者活动已结束
    if (getters.activityHasFinish || getters.activityNotStart) {
      // 活动结束展示普通商详；
      return;
    }
    return {
      desc,
      end,
    };
  },

  // 大按钮
  bigButtons(state, getters) {
    if (getters.isBigButtonForbidBuy) {
      return getters.commonBigButtons;
    }
    /**
     * 1.库存大于0； 按钮显示： 马上抢+ pop；
     * 2.库存为0； 按钮显示： 查看店铺其他商品 并且pop提示框隐藏
     */
    const {
      accountUnionScene: ACCOUNT_UNION_SCENE = {},
    } = state.businessConfigs;
    const activity = getters.currentActivity;

    // 活动未开始、活动已经结束、失效展示普通商详
    if (getters.activityHasFinish || getters.activityNotStart || JSON.stringify(activity) == '{}') {
      return getters.commonBigButtons;
    } else if (getters.limitedSeckillPiece > 0) {
      return [
        {
          text: '马上抢',
          skuScene: SKU_SCENE.ACT_BUY,
          accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
        },
      ];
    } else {
      return [
        {
          forbid: true,
          text: '查看店铺其他商品',
          url: getters.homepageUrl,
          skuScene: SKU_SCENE.JUMP_LINK,
          primary: true,
        },
      ];
    }
  },

  // 不展示底部按钮提示条
  goodsBottomBarTip() {
    return {};
  },

  // 店铺还有其他好东西，去看看
  showNoStockRecommendGoods(state, getters) {
    return !getters.limitedSeckillPiece > 0;
  },

  // 限时秒杀不展示优惠券模块
  showGoodsCoupon() {
    return false;
  },

  // sku面板上按钮字体
  skuBuyBtnText() {
    return '马上抢';
  },

  // 活动价格
  activityPrice(state, getters) {
    const { minPrice = 0, maxPrice = 0, oldPrice = '' } = state.goods;
    const priceTag = getters.goodsPriceTag || '';
    return {
      priceTag,
      // 是否是价格区间
      isRange: minPrice !== maxPrice,
      showPrice: formatPrice(minPrice).split('.'),
      originPrice: oldPrice,
    };
  },
};
