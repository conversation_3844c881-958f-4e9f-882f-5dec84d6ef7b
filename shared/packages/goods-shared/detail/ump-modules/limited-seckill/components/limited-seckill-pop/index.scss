@import '~mixins/index.scss';

.limited-kill {
  position: absolute;
  top: 0;
  left: 0;
  width: 192px;
  height: 44px;
  border: 0 solid transparent;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.75);
  font-family: PingFangSC-Regular;

  &__left-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 47px;
    height: 44px;
    border-radius: 8px;
    font-weight: bold;
    clip-path: polygon(0 0, 0 100%, 75% 100%, 85% 0);
    font-size: 12px;
    color: white;
    text-indent: 3px;

    .describe {
      height: 18px;
      line-height: 18px;
    }
  }

  .describe-right {
    display: inline-flex;
    position: absolute;
    top: 15px;
    right: 11px;
    box-sizing: border-box;
    font-size: 14px;
    line-height: 14px;
    text-align: center;
    color: white;

    &__left-side {
      width: 30px;
      padding: 0 1px;
    }

    &__goods-number {
      flex-grow: 1;
      padding: 0 2px;
      font-weight: 700;
    }

    &__right-side {
      width: 85px;
    }
  }

  &__arrow {
    position: absolute;
    top: 40px;
    right: 25px;
    width: 0;
    height: 0;
    transform: rotate(-135deg);
    border-width: 4px;
    border-color: rgba(0, 0, 0, 0.75) transparent transparent
      rgba(0, 0, 0, 0.75);
    border-style: solid;
    border-top-left-radius: 3px;
  }
}
