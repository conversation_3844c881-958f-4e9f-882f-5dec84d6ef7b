import { GoodsComponent } from '@goods/common/vanx-component';
import Toast from '@vant/weapp/dist/toast/toast';
import { mapMutations } from '@youzan/vanx';

GoodsComponent({
  name: 'QuestionPopup',

  state: ['seckillDisplayPop', 'seckillQuestion', 'loginDialogShow'],

  getters: [
    'currentActivity',
  ],

  data: {
    selectedKey: '',
  },

  methods: {
    ...mapMutations(['showLoginPop']),
    selectKeyChange(e) {
      this.setYZData({
        selectedKey: e.detail
      });
    },
    submitAnswer() {
      if (this.data.selectedKey != this.data.seckillQuestion.answerKey) {
        Toast('哎呀，答案不对哦');
        return;
      }
      this.$dispatch('bookSeckill');
    },

    loginSuccess() {
      this.showLoginPop(false);
    }
  },
});
