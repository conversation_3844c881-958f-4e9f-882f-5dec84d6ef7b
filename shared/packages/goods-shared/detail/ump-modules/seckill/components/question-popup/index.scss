@import '~mixins/index.scss';

.question {
  &__popup {
    padding: 0;
    width: 270px;
    font-size: 13px;
    border-radius: 6px;
  }

  &__header {
    background-color: #f44;
    color: #fff;
    font-size: 16px;
    line-height: 56px;
    margin: 0;
    text-align: center;
  }

  &__block {
    margin: 0;
    padding: 10px 0 0;
    box-sizing: border-box;
    max-height: 540px;
    overflow-y: auto;
  }

  &__title {
    font-size: 16px;
    color: $gray-dark;
    padding: 18px 10px;
    border-bottom: 1px solid $gray-light;
  }

  &__item {
    font-size: 15px;
    color: $text-color;
    padding: 15px 12px;
    border-bottom: 1px solid $gray-light;
  }

  &__item-text {
    display: inline-block;
    margin-left: 8px;
  }

  &__btn {
    margin-top: -1px;
    border-top: 1px solid $gray-light;
    padding: 16px;
    text-align: center;
    color: #06bf04;
  }
}