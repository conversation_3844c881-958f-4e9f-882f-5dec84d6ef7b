<van-popup
  wx:if="{{ currentActivity.useQuestion }}"
  show="{{ seckillDisplayPop.showQuestionPop }}"
  custom-class="question__popup"
>
  <view class="question__header">答对问题，完成预约！</view>
  <view class="question__block">
    <view class="question__title">{{ seckillQuestion.title }}</view>
    <van-radio-group value="{{ selectedKey }}" bind:change="selectKeyChange">
      <van-radio
        wx:for="{{ seckillQuestion.itemList }}"
        wx:key="{{ item.key }}"
        name="{{ item.key }}"
        checked-color="#07c160"
        custom-class="question__item"
      >
        <text class="question__item-text">{{ item.content }}</text>
      </van-radio>
    </van-radio-group>
  </view>
  <view class="question__btn" bind:tap="submitAnswer">答完提交</view>
</van-popup>

<account-login 
  show="{{ seckillDisplayPop.showLoginPop }}"
  bind:loginSuccess="loginSuccess"
/>