export default {
  showLoginPop(state, show = true) {
    state.seckillDisplayPop.showLoginPop = show;
  },
  showSeckillPopup(state, type) {
    switch (type) {
      case 'follow':
        state.seckillDisplayPop.showFollowPop = true;
        break;
      case 'question':
        state.seckillDisplayPop.showQuestionPop = true;
        break;
      default:
        break;
    }
  },
  hideSeckillPopup(state, type) {
    switch (type) {
      case 'follow':
        state.seckillDisplayPop.showFollowPop = false;
        break;
      case 'question':
        state.seckillDisplayPop.showQuestionPop = false;
        break;
      default:
        break;
    }
  },
  updateSeckillQuestion(state, event = {}) {
    const { seckillQuestion } = state;
    state.seckillQuestion = {
      ...seckillQuestion,
      ...event,
    };
  },
  updateSeckillAccount(state, event = {}) {
    const { seckillAccount } = state;
    state.seckillAccount = {
      ...seckillAccount,
      ...event,
    };
  },
};
