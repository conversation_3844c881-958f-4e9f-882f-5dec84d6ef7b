import { get, omit, moment } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';
import { getUrl, GOODS } from '@goods/common/page-path';

export default {
  customStock() {
    return true;
  },
  currentActivity(state) {
    return get(state, 'marketActivity.seckill', {});
  },

  showActivityBlock(state, getters) {
    return getters.activityRules.label;
  },

  // 是否显示活动推广栏
  showActivityBanner() {
    return true;
  },

  isSupportFCodeBuy() {
    return false;
  },

  // 价格标签
  goodsPriceTag(state, getters) {
    return getters.currentActivity.tag || '秒杀价';
  },

  // sku活动标记
  activitySkuPriceTags(state, getters) {
    return [getters.goodsPriceTag];
  },

  // 活动未开始
  activityNotStart(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = activity.startAt - now;
    return delta > 0;
  },

  // 活动已结束
  activityHasFinish(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = activity.endAt - now;
    return delta < 0;
  },

  // 倒计时模块
  countdown(state, getters) {
    const activity = state.marketActivity.seckill;
    if (!activity) {
      // 因为是异步加载活动，初始值可能不存在
      return null;
    }
    const { startAt, endAt } = activity;
    const now = +new Date();
    let desc;
    let end;
    let showProgress = false;

    if (getters.activityHasFinish) {
      desc = '活动已结束';
      end = now;
    } else if (getters.activityNotStart) {
      desc = '距活动开始仅剩';
      end = startAt;
    } else if (!getters.activityHasFinish) {
      desc = '距结束仅剩';
      end = endAt;
      showProgress = true;
    }

    const stockNum = get(state, 'goods.stockNum', 0);

    return {
      showProgress,
      progressNum: getters.leftStockNumProgress,
      progressDesc: `仅剩${stockNum}件`,
      desc,
      end,
    };
  },

  // 活动介绍
  activityRules(state, getters) {
    const activity = getters.currentActivity;
    const {
      isCheckRight,
      cancelTime,
      bookingNumText = '0',
      serverStartAt = 0,
      buyNeedBook, // 仅预约用户可参与秒杀，false:否；true:是
    } = activity;
    const quota = get(activity, 'limit.quota', 0);

    // 需要预约且未预约
    const needCheckRight = isCheckRight;

    let desc = '';
    const rules = [];

    if (serverStartAt && getters.activityNotStart) {
      const startTime = moment(serverStartAt, 'MM.DD HH:mm:ss');
      rules.push(`预计 ${startTime} 活动开始。`);
    }

    if (needCheckRight) {
      rules.push(
        `${
          buyNeedBook ? '只有预约成功后才有资格参加秒杀，' : ''
        }已有${bookingNumText}人预约成功。`
      );
    }

    desc += quota ? `每人限购${quota}件。` : '';
    desc += cancelTime ? `订单${cancelTime}分钟内未支付将被取消。` : '';
    desc && rules.push(desc);

    return {
      label: rules.join(' '),
      rules,
    };
  },

  // 底部按钮提示条
  goodsBottomBarTip(state, getters) {
    const activity = getters.currentActivity;
    // isCheckRight 开启秒杀预约
    // isPassCheck 已经预约秒杀
    const { isCheckRight, isPassCheck } = activity;

    if (getters.activityHasFinish) {
      return {
        text: '秒杀活动已结束',
      };
    }
    if (getters.activityNotStart && !isCheckRight) {
      return {
        text: '秒杀活动尚未开始',
      };
    }
    if (getters.activityNotStart && isCheckRight && isPassCheck) {
      return {
        text: '您已成功预约，活动暂未开始，也可零售价购买哦',
      };
    }
    if (!getters.activityNotStart && isCheckRight && !isPassCheck) {
      return {
        text: '你未预约此活动，无法参加秒杀，下次记得预约哦',
      };
    }
    // 优先判断活动自己的逻辑
    const commonTip = getters.goodsBottomDisabledTip;
    if (commonTip.text) {
      return commonTip;
    }
    return {
      text: '',
    };
  },

  // 大按钮
  bigButtons(state, getters) {
    if (getters.isBigButtonForbidBuy) {
      return getters.commonBigButtons;
    }

    const normalBuyUrl = getUrl(GOODS, {
      ...omit(state.pageParams, [
        'activityType',
        'activityId',
        'ump_type',
        'ump_alias',
      ]),
      alias: state.goods.alias,
    });

    const normalButton = {
      forbid: true,
      text: '非活动价购买',
      url: normalBuyUrl,
      skuScene: SKU_SCENE.JUMP_LINK,
      // skuScene: SKU_SCENE.NORMAL_BUY,
      // accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
    };
    if (getters.activityHasFinish) {
      normalButton.primary = true;
      return [normalButton];
    }

    const activity = getters.currentActivity;
    // isCheckRight 开启秒杀预约 isPassCheck 已经预约秒杀
    const { isCheckRight, isPassCheck } = activity;

    // 默认其他情况都支持原价购买
    const bigButtons = [];
    const { accountUnionScene: ACCOUNT_UNION_SCENE = {} } =
      state.businessConfigs;

    if (getters.activityNotStart) {
      // 活动未开始
      bigButtons.push(normalButton);
      if (isCheckRight && !isPassCheck) {
        // 需要预约而未预约
        bigButtons.push({
          text: '立即预约',
          skuScene: SKU_SCENE.RESERVATION,
          accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
          subscribeSceneV2: 'umpSeckillBookRemind',
        });
        // 不需要预约或者已预约则显示原价购买
      }
      // 活动已开始
    } else if (!isCheckRight || isPassCheck) {
      // 不需要预约或者已预约，则秒杀价拿下
      bigButtons.push({
        text: '立即拿下',
        skuScene: SKU_SCENE.ACT_BUY,
        accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
      });
    } else {
      bigButtons.push(normalButton);
    }
    bigButtons[bigButtons.length - 1].primary = true;
    return bigButtons;
  },

  // 秒杀不展示优惠券模块
  showGoodsCoupon() {
    return false;
  },
  activityInfoForPrice(state) {
    return {
      type: 'seckill',
      identification: state.pageParams.ump_alias,
    };
  },
};
