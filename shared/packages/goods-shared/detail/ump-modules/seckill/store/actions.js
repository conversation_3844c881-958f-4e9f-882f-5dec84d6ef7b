import Toast from '@vant/weapp/dist/toast/toast';
import api from '@goods/utils/api';

export default {
  // 预约秒杀，必须要先登录，前置流程
  makeReservation({ commit, dispatch }) {
    const submitType = 'orderSeckill';

    commit('setSkuConfig', {
      submitType,
      isAjaxing: true,
    });

    // 提交数据状态
    commit('setSkuConfig', {
      isAjaxing: false
    });

    const app = getApp();

    if (!app.getBuyerId()) {
      commit('showLoginPop');
      return;
    }

    dispatch(submitType);
  },

  // 进入秒杀预约环境
  orderSeckill({ getters, commit, dispatch }) {
    const activity = getters.currentActivity;
    const useFollow = activity.useFollow || false;
    const useQuestion = activity.useQuestion || false;
    const questionId = activity.questionId || '';

    const app = getApp();
    if (useQuestion) {
      app.carmen({
        api: 'youzan.ump.seckill.question/1.0.0/get',
        query: {
          question_id: questionId,
        },
        success: (res) => {
          commit('updateSeckillQuestion', {
            wrongKey: '',
            title: res.title,
            itemList: res.options || [],
            answerKey: res.answer_key
          });
          commit('showSeckillPopup', 'question');
        },
        fail: () => {
          Toast('获取预约问题失败');
          dispatch('hideSeckillPopup', 'question');
        }
      });
      return;
    }

    if (useFollow) {
      wx.showToast({
        title: '小程序暂不支持关注公众号后预约秒杀',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 既不需要回答问题，又不需要分享，直接预约
    dispatch('bookSeckill');
  },
  // 提交答案预约秒杀
  bookSeckill({ dispatch, getters }) {
    const activity = getters.currentActivity;
    const app = getApp();
    Toast('数据提交中...');
    api
      .post({
        url: '/wscgoods/seckill-api/seckill-question-remind.json',
        data: {
          seckillId: activity.activityId,
          // 小程序传2
          pushWay: 2,
          kdtId: app.getKdtId(),
        },
      })
      .then(() => {
        dispatch('reload');
        dispatch('hideSeckillPopup', 'question');
      })
      .catch(() => {
        Toast('预约失败');
        dispatch('hideSeckillPopup', 'question');
      });
  },

  // 隐藏弹层
  hideSeckillPopup({ commit }, type) {
    commit('hideSeckillPopup', type);
  },
};
