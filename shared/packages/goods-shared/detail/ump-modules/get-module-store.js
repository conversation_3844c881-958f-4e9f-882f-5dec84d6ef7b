import HelpCut from '@goods/ump-modules/help-cut/store';
import Groupon from '@goods/ump-modules/groupon/store';
import FCode from '@goods/ump-modules/fcode/store';
import Lottery from '@goods/ump-modules/lottery/store';
import Present from '@goods/ump-modules/present/store';
import Tuan from '@goods/ump-modules/tuan/store';
import Seckill from '@goods/ump-modules/seckill/store';
import Gift from '@goods/ump-modules/gift/store';
import InSourcingFission from '@goods/ump-modules/in-sourcing-fission/store';
import ProductLaunch from '@goods/ump-modules/product-launch/store';
// import Auction from '@goods/ump-modules/auction/store';
//  微信号限时秒杀;
import LimitedSeckill from '@goods/ump-modules/limited-seckill/store';

const moduleStores = {
  [HelpCut.name]: HelpCut,
  [Groupon.name]: Groupon,
  [FCode.name]: FCode,
  [Lottery.name]: Lottery,
  [Present.name]: Present,
  [Tuan.name]: <PERSON><PERSON>,
  [Seckill.name]: Seckill,
  [Gift.name]: Gift,
  // 内购活动
  [InSourcingFission.name]: InSourcingFission,
  [ProductLaunch.name]: ProductLaunch,
  // auction: Auction, // 暂时不支持降价拍
  // 视频号限时秒杀
  [LimitedSeckill.name]: LimitedSeckill,
};

export function getModule(pageType) {
  return moduleStores[pageType] || {};
}
