import { get } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';

export default {
  isFCode(state) {
    return get(state, 'marketActivity.fCode', false);
  },
  currentActivity(state) {
    return get(state, 'marketActivity.fCode', {});
  },
  // sku活动标记
  activitySkuPriceTags() {
    return ['F码'];
  },

  showPostage() {
    return false;
  },

  showSoldNum() {
    return false;
  },

  // f码商品不展示多网点推荐商品
  hideAvailableStores() {
    return true;
  },

  isSupportFCodeBuy() {
    return false;
  },

  miniButtonsData() {
    return [];
  },

  // 底部按钮
  // 大按钮
  bigButtons(state, getters) {
    const showForbidBuyBtn = get(state, 'display.showForbidBuyBtn', '');
    if (showForbidBuyBtn) {
      return [
        {
          forbid: true,
          text: '查看店铺其他商品',
          url: getters.homepageUrl,
          skuScene: SKU_SCENE.JUMP_LINK,
          primary: true,
        },
      ];
    }
    const {
      accountUnionScene: ACCOUNT_UNION_SCENE = {},
    } = state.businessConfigs;
    return [
      {
        text: getters.bottomBuyBtnTxt,
        skuScene: SKU_SCENE.ACT_BUY,
        accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
      },
    ];
  },
};
