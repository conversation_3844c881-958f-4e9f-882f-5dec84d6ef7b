import { get } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';

export default {
  isPresent(state) {
    return !!get(state, 'marketActivity.present', false);
  },
  currentActivity(state) {
    return get(state, 'marketActivity.present', {});
  },
  // sku活动标记
  activitySkuPriceTags() {
    return ['赠品'];
  },

  miniButtons() {
    return [];
  },

  goodsHasBeenSoldout(state, getters) {
    const skuList = get(state, 'activitySku.list', []);
    return skuList.length > 0 && getters.initialSku.stockNum === 0;
  },

  showSkuBar(state, getters) {
    return (
      !getters.goodsHasBeenSoldout &&
      get(state, 'activitySku.list', []).length > 1
    );
  },

  // 底部不可购买展示内容
  goodsBottomBarTip() {
    return {
      text: '',
    };
  },

  // 标识赠品对应的sku数据
  initialSku(state) {
    const skuId = get(state, 'marketActivity.present.present.skuId');

    const defaultSku = get(state, 'activitySku.list', [])
      .filter((item) => item.id === skuId)
      .pop();

    return defaultSku;
  },

  bigButtonsData(state) {
    const {
      accountUnionScene: ACCOUNT_UNION_SCENE = {},
    } = state.businessConfigs;
    return [
      {
        text: '领取赠品',
        type: 'present',
        skuScene: SKU_SCENE.ACT_BUY,
        accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
        primary: true,
      },
    ];
  },

  // 赠品不展示优惠券模块
  showGoodsCoupon() {
    return false;
  },

  // 赠品起售固定为1
  startSaleNum() {
    return 1;
  },
};
