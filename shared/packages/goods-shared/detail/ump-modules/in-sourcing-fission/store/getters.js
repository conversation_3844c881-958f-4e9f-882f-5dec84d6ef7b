import { get, formatPrice } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';
import { getUrl, IN_SOURCING_FISSION } from '@goods/common/page-path';

export default {
  isInSourcingFission() {
    return true;
  },
  isShowInSourcingFissionTip(state, getters) {
    return (
      getters.isInSourcingFission &&
      !getters.activityNotStart &&
      !getters.activityHasFinish
    );
  },
  currentActivity(state) {
    return get(state, 'marketActivity.inSourcingFission', {});
  },
  // 是否显示活动推广栏
  showActivityBanner() {
    return true;
  },
  // 价格标签
  goodsPriceTag() {
    return '内购价';
  },

  // sku活动标记
  activitySkuPriceTags(state, getters) {
    return [getters.goodsPriceTag];
  },

  // 活动未开始
  activityNotStart(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = activity.startAt - now;
    return delta > 0;
  },

  // 活动已结束
  activityHasFinish(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = activity.endAt - now;
    return delta < 0;
  },

  // 活动跳转链接
  activityUrl(state, getters) {
    return getUrl(IN_SOURCING_FISSION, getters.activityParams);
  },

  // 活动链接参数
  activityParams(state, getters) {
    const activity = getters.currentActivity;

    return {
      umpAlias: state.pageParams.ump_alias,
      umpActivityId: activity.activityId,
      kdtId: state.shop.kdtId,
      activityId: activity.activityId,
      inviterVoucherAlias: activity.inviterVoucherAlias,
    };
  },

  // 倒计时模块
  countdown(state, getters) {
    const activity = state.marketActivity.inSourcingFission;
    if (!activity) {
      // 因为是异步加载活动，初始值可能不存在
      return null;
    }
    const { startAt, endAt } = activity;
    const now = +new Date();

    let desc;
    let end;
    if (getters.activityNotStart) {
      desc = '距开始仅剩';
      end = startAt;
    } else if (!getters.activityHasFinish) {
      desc = '距结束仅剩';
      end = endAt;
    } else {
      desc = '活动已结束';
      end = now;
    }

    const stockNum = get(state, 'goods.stockNum', 0);

    return {
      showProgress: true,
      progressNum: getters.leftStockNumProgress,
      progressDesc: `仅剩${stockNum}件`,
      desc,
      end,
    };
  },
  // 已售商品百分比
  leftStockNumProgress(state) {
    const stockNum = get(state, 'goods.stockNum', 0);
    const totalActivityStockNum = Math.max(
      get(state, 'originActivitySku.totalActivityStockNum', 0),
      stockNum
    );
    const progressNum = Math.round((stockNum * 100) / totalActivityStockNum);

    return +progressNum;
  },

  // 底部按钮提示条
  goodsBottomBarTip(state, getters) {
    if (getters.activityNotStart) {
      return {
        text: '活动即将开始',
      };
    }
    if (getters.activityHasFinish) {
      return {
        text: '活动已结束，去看看其他商品吧',
      };
    }
    // 优先判断活动自己的逻辑
    const commonTip = getters.goodsBottomDisabledTip;
    if (commonTip.text) {
      return commonTip;
    }
    return {
      text: '',
    };
  },

  // 大按钮
  bigButtons(state, getters) {
    if (getters.isBigButtonForbidBuy) {
      return getters.commonBigButtons;
    }

    if (getters.activityNotStart || getters.activityHasFinish) {
      return [
        {
          forbid: true,
          text: '查看店铺其他商品',
          url: getters.homepageUrl,
          skuScene: SKU_SCENE.JUMP_LINK,
          primary: true,
        },
      ];
    }

    // 默认其他情况都支持原价购买
    const bigButtons = [
      {
        sub: `￥${formatPrice(state.goods.minPrice, 2)}`,
        text: '内购价购买',
        primary: true,
        skuScene: SKU_SCENE.ACT_BUY,
      },
    ];

    if (getters.currentActivity.hasBuyThreshold) {
      const {
        accountUnionScene: ACCOUNT_UNION_SCENE = {},
      } = state.businessConfigs;
      bigButtons.unshift({
        sub: `￥${formatPrice(state.goods.oldMinPrice, 2)}`,
        text: '直接购买',
        skuScene: SKU_SCENE.NORMAL_BUY,
        accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
      });
    }
    return bigButtons;
  },

  activityInfoForPrice(state, getters) {
    return {
      type: 'inSourcingFission',
      identification: getters.currentActivity.activityId,
    };
  },

  // 是否有足够的内购劵
  isEnoughCoupon(state, getters) {
    return getters.acquireCoupon >= getters.needCoupon;
  },

  // 当前选中的sku下，内购劵的数量
  currentNeedCoupon(state) {
    const {
      originActivitySku = {},
      skuExtraData: { selectedSkuComb, stepperCount = 1 },
    } = state;

    let sku = originActivitySku;

    if (
      selectedSkuComb &&
      selectedSkuComb.needInSourcingCouponNum !== undefined
    ) {
      sku = selectedSkuComb;
    }

    if (!sku) {
      return 0;
    }

    return sku.needInSourcingCouponNum * stepperCount || 0;
  },

  // 显示内购不足的提示
  showNotEnoughCouponTip(state, getters) {
    const { skuConfig } = state;
    const { acquireCoupon, currentNeedCoupon } = getters;

    // 非内购活动购买不显示内购劵不足的提示
    if (skuConfig && skuConfig.skuScene !== SKU_SCENE.ACT_BUY) {
      return 0;
    }

    return acquireCoupon < currentNeedCoupon ? currentNeedCoupon : 0;
  },

  // 当前拥有的劵的数量
  acquireCoupon(state, getters) {
    const { currentActivity } = getters;

    return currentActivity.havingInSourcingCouponNum;
  },

  // 内购需要的劵的数量, 取当前所有sku中，最小的值
  needCoupon(state) {
    const { originActivitySku } = state;

    if (!originActivitySku) {
      return 0;
    }

    if (originActivitySku.list.length === 0) {
      // 无规格商品，取spu信息
      return originActivitySku.needInSourcingCouponNum || 0;
    }

    // 存在部分sku参与活动的情况，这种情况下 needInSourcingCouponNum === 0
    const list = originActivitySku.list
      // 过滤掉没有参与活动的sku,活动库存为0的sku
      .filter((sku) => !sku.notJoinActivity && sku.stockNum > 0)
      .sort((a, b) => a.needInSourcingCouponNum - b.needInSourcingCouponNum);

    const sku = list[0];

    if (sku) {
      return sku.needInSourcingCouponNum || 0;
    }

    return 0;
  },

  // 内购劵购买的文案提升
  inSourcingFissionBuyText(state, getters) {
    const {
      isEnoughCoupon,
      needCoupon,
      acquireCoupon,
      currentActivity,
    } = getters;

    // 内购劵不足
    if (!isEnoughCoupon) {
      return `需使用 ${needCoupon} 张内购券，剩余内购券 ${acquireCoupon} 张`;
    }

    // 内购劵充足 && 需要内购劵购买
    if (currentActivity.hasBuyThreshold) {
      return `你有 ${acquireCoupon} 张内购券，可直接使用内购价购买`;
    }

    // 不需要内购劵购买
    return '无需内购券，可直接内购价购买';
  },

  // 展示已售
  showSoldNum() {
    return false;
  },

  // 显示库存
  showGoodsStock() {
    return false;
  },
};
