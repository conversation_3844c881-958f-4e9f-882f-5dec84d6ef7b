import { getUrl, IN_SOURCING_FISSION } from '@goods/common/page-path';

export default {
  beforeSetWxShare({ state, getters }, shareData) {
    const { shop } = state;
    const text = `送你${shop.name}内购福利！享受超低内购价！`;

    // 设置自定义微信分享信息
    shareData.title = text;
    shareData.path = getters.activityUrl;

    return shareData;
  },
  beforeSetPosterShare({ getters }) {
    const activity = getters.currentActivity;

    return {
      // 自定义参数
      scene: {
        page: getUrl(IN_SOURCING_FISSION, {}),
        ...getters.activityParams,
      },
      type: activity.type,
    };
  },
};
