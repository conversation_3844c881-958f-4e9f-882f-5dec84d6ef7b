import { args, get } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';
import { LOTTERY_RESULT, LOTTERY, getUrl } from '@goods/common/page-path';

export default {
  currentActivity(state) {
    return get(state, 'marketActivity.luckyDrawGroup', {});
  },
  showActivityBlock() {
    return true;
  },
  // 是否显示活动推广栏
  showActivityBanner() {
    return true;
  },

  hideSkuStepper() {
    return true;
  },

  hideSkuStock() {
    return true;
  },

  quota() {
    return 1;
  },

  // 是否隐藏sku 原价
  hideSkuOriginPrice() {
    return false;
  },

  // 价格标签
  goodsPriceTag() {
    return '0元抽奖';
  },

  // sku活动标记
  activitySkuPriceTags(state, getters) {
    return [getters.goodsPriceTag];
  },

  // 活动价格
  activityPrice(state, getters) {
    const priceTag = getters.goodsPriceTag || '';
    return {
      priceTag,
      // 是否是价格区间
      isRange: false,
      showPrice: ['0', ''],
      originPrice: state.goods.oldPrice,
    };
  },

  // 活动未开始
  activityNotStart(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = activity.startAt - now;
    return (
      (delta > 0 || activity.buttonType === 3) && !getters.activityHasFinish
    );
  },

  // 活动已结束
  activityHasFinish(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = activity.endAt - now;
    return delta < 0 || activity.buttonType === 4;
  },

  activityUrl(state, getters) {
    const activity = getters.currentActivity;

    let viewUrl = getUrl(LOTTERY, {
      kdt_id: state.shop.kdtId,
      activity_id: activity.activityId,
    });

    if (activity.ownGroup) {
      const { alias, orderNo } = activity.ownGroup;
      if (alias) {
        viewUrl = args.add(viewUrl, { group_alias: alias });
      }
      if (orderNo) {
        viewUrl = args.add(viewUrl, { order_no: orderNo });
      }
    }
    return viewUrl;
  },

  // 倒计时模块
  countdown(state, getters) {
    const activity = state.marketActivity.luckyDrawGroup;
    if (!activity) {
      // 因为是异步加载活动，初始值可能不存在
      return null;
    }
    const { buttonType, startAt, endAt, ownGroup } = activity;

    const now = +new Date();
    let desc;
    let end;
    // 活动失效，时间不更新，需要依赖buttonType判断
    if (buttonType == 4) {
      desc = '活动已结束';
      end = now;
    } else if (getters.activityNotStart) {
      desc = '距活动开始';
      end = startAt;
    } else if (getters.activityHasFinish) {
      desc = '活动已结束';
      end = now;
    } else if (ownGroup) {
      desc = '距开奖仅剩';
      end = endAt;
    } else {
      desc = '距结束仅剩';
      end = endAt;
    }

    return {
      showProgress: false,
      desc,
      end,
    };
  },

  // 活动介绍
  activityRules(state, getters) {
    const {
      goods: { title },
      shop: { phone },
    } = state;

    const activity = getters.currentActivity;
    const { joinNum, couponName } = activity;

    let activityIntroRule1 = `拼团未成功不具备抽奖资格，拼团成功后可获得抽奖资格，一等奖为【${title}】`;
    let activityIntroRule2 = `【${title}】在抽奖结束后72小时内发货`;
    if (couponName) {
      activityIntroRule1 += `，二等奖为【${couponName}】`;
      activityIntroRule2 = `【${couponName}】在抽奖结束后立即发放，${activityIntroRule2}`;
    }
    const rules = [
      '活动结束后将从拼团成功的用户中，随机抽取中奖者',
      activityIntroRule1,
      '同一商品，同一用户仅限作为团员成团一次，但可以作为团长无限发起拼团，成团后，中奖概率叠加',
      activityIntroRule2,
    ].map((rule, index) => `${index + 1}、${rule}`);
    rules.unshift('开奖时间：活动结束后立即开奖');
    if (phone) {
      rules.push(`客服电话：【${phone}】`);
    }

    return {
      label: `邀请${joinNum}名好友成团，即可获得抽奖资格，重复成团中奖率提升`,
      rules,
    };
  },

  // 底部按钮提示条
  goodsBottomBarTip(state, getters) {
    if (getters.activityNotStart) {
      return {
        text: '活动即将开始',
      };
    }
    if (getters.activityHasFinish) {
      const activity = getters.currentActivity;

      const viewUrl = getUrl(LOTTERY_RESULT, {
        kdt_id: state.shop.kdtId,
        activity_id: activity.activityId,
      });

      return {
        text: '查看中奖结果',
        url: viewUrl,
      };
    }
    // 优先判断活动自己的逻辑
    const commonTip = getters.goodsBottomDisabledTip;
    if (commonTip.text) {
      return commonTip;
    }
    return {
      text: '',
    };
  },

  // 大按钮
  bigButtons(state, getters) {
    if (getters.isBigButtonForbidBuy) {
      return getters.commonBigButtons;
    }
    // 活动结束或活动未开始
    if (getters.activityHasFinish || getters.activityNotStart) {
      return [
        {
          forbid: true,
          text: '查看店铺其他商品',
          url: getters.homepageUrl,
          skuScene: SKU_SCENE.JUMP_LINK,
          primary: true,
        },
      ];
    }

    const activity = getters.currentActivity;

    // 默认其他情况都支持原价购买
    const bigButtons = [];

    let primaryBtn = null;
    const {
      accountUnionScene: ACCOUNT_UNION_SCENE = {},
    } = state.businessConfigs;

    switch (activity.buttonType) {
      // 开团
      case 1:
        primaryBtn = {
          text: '立即0元开团',
          primary: true,
          skuScene: SKU_SCENE.ACT_BUY,
          subscribeScene: 'lottery',
          accountUnionScene: ACCOUNT_UNION_SCENE.GROUP_BUY,
        };
        break;
      // 查看我的团
      case 2:
        primaryBtn = {
          text: '查看我的团',
          url: getters.activityUrl,
          skuScene: SKU_SCENE.JUMP_LINK,
          primary: true,
        };
        break;
      default:
        primaryBtn = {
          forbid: true,
          text: '看看其他商品',
          url: getters.homepageUrl,
          skuScene: SKU_SCENE.JUMP_LINK,
          primary: true,
        };
        break;
    }

    if (primaryBtn) {
      bigButtons.push(primaryBtn);
    }
    return bigButtons;
  },
  // 0元抽奖不展示优惠券模块
  showGoodsCoupon() {
    return false;
  },
  activityInfoForPrice(state, getters) {
    const activity = getters.currentActivity;
    return {
      type: 'luckyDrawGroup',
      identification: activity.activityId,
    };
  },
};
