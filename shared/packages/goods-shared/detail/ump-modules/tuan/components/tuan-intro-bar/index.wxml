<view>
    <activity-intro
      title="团购返现玩法"
      is-link
      bind:click="showTuanPop"
    >
      <view class="tuan-intro__content van-hairline--top">
        <view class="tuan-intro__content-top">
          <text>已售{{ currentActivity.soldCount }}件</text>
          <text>{{ tuanIntroTitle }}</text>
        </view>
        <view class="tuan-intro__progress">
          <van-progress
            show-pivot="{{ false }}"
            percentage="{{ currentActivity.percentage }}"
            color="#ed5050"
          />
          <view class="tuan-intro__content-txt">{{ tuanIntroContent }}</view>
        </view>
      </view>
    </activity-intro>
    <common-popup
      show="{{ displayPop.tuanPopShow }}"
      title="{{ tuanIntroBarTitle }}"
      bind:close="hideTuanPop"
      bind:btn-click="hideTuanPop"
    >
      <tuan-intro-rule />
    </common-popup>
  </view>