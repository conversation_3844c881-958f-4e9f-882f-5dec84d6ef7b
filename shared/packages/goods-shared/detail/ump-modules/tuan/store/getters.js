import { get, formatPrice } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';

export default {
  currentActivity(state) {
    return get(state, 'marketActivity.tuan', {});
  },

  showActivityBlock(state, getters) {
    // 团购返现才显示block
    return getters.currentActivity.isCash;
  },

  // 是否显示活动推广栏
  showActivityBanner() {
    return true;
  },

  // 团购返现商品不展示多网点推荐商品
  hideAvailableStores() {
    return true;
  },

  isSkuBarDisabled() {
    return true;
  },

  isSupportFCodeBuy() {
    return false;
  },

  // 价格标签
  goodsPriceTag(state, getters) {
    if (getters.currentActivity.isCash) {
      return '团购返现';
    }
    return '团购价';
  },

  // sku活动标记
  activitySkuPriceTags(state, getters) {
    return [getters.goodsPriceTag];
  },

  // 活动价格
  activityPrice(state, getters) {
    const { minPrice = 0, maxPrice = 0 } = state.goods;
    const priceTag = getters.goodsPriceTag || '';
    return {
      priceTag,
      // 是否是价格区间
      isRange: minPrice !== maxPrice,
      showPrice: formatPrice(minPrice).split('.'),
      originPrice: '',
    };
  },

  // 活动未开始
  activityNotStart(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = +new Date(activity.startAt) - now;
    return delta > 0;
  },

  // 活动已结束
  activityHasFinish(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = +new Date(activity.endAt) - now;
    return delta < 0;
  },

  activityUrl() {
    return '';
  },

  // 倒计时模块
  countdown(state) {
    const activity = state.marketActivity.tuan;
    if (!activity) {
      // 因为是异步加载活动，初始值可能不存在
      return null;
    }
    const { startAt, endAt, state: tuanState } = activity;
    // 转换成时间戳

    const now = +new Date();
    let desc;
    let end;

    switch (tuanState) {
      case 'on':
        desc = '距结束仅剩';
        end = endAt;
        break;
      case 'no':
        desc = '距开始仅剩';
        end = startAt;
        break;
      default:
        desc = '活动已结束';
        end = now;
        break;
    }

    return {
      showProgress: false,
      desc,
      end,
    };
  },

  // 活动介绍
  tuanIntroTitle(state, getters) {
    const { returnMoney, soldCount, allRules = [] } = getters.currentActivity;
    let matchMin = 0;
    let matchVal = 0;
    for (let i = 0; i < allRules.length; i++) {
      const { min, value } = allRules[i];
      if (soldCount < matchMin) {
        break;
      }
      matchMin = min;
      matchVal = value;
    }

    if (soldCount < matchMin) {
      return `满${matchMin}件每件返${formatPrice(+matchVal, 0)}元`;
    }
    return `下单即得团购返现${formatPrice(returnMoney, 0)}元`;
  },

  tuanIntroRules(state, getters) {
    const rules = getters.currentActivity.allRules || [];
    return rules
      .filter((rule) => rule.min > 0)
      .map((item) => {
        item = { ...item };
        item.value = formatPrice(item.value);
        return item;
      });
  },

  tuanIntroContent(state, getters) {
    return getters.tuanIntroRules
      .map((rule) => `满${rule.min}件每件返${rule.value}元`)
      .join('；');
  },

  tuanIntroRulesDetail(state, getters) {
    const activity = getters.currentActivity;
    // 普通团购返现
    if (activity.isCash === 1) {
      return [
        '1、通过【微信支付】付款，返现金额将通过 【微信退款】发放，请注意查收；',
        '2、通过【银行卡】付款，返现金额将在3天内，原路发放至银行卡账户；',
        '3、最高返现金额不超过你的现金支付金额；',
      ];
    }
    if (activity.isCash === 2) {
      const limitText =
        activity.limitedDays === 0 || !activity.limitedDays
          ? ''
          : activity.limitedDays + '天';
      // 团购返储值金
      return [
        `1、订单支付完成${limitText}后，且未发起退款才会收到返现金额；`,
        '2、返现金额将发放至储值卡余额，请前往个人中心查看',
      ];
    }
  },

  tuanIntroBarTitle(state, getters) {
    const activity = getters.currentActivity;
    if (activity.isCash === 1) {
      return '团购返现说明';
    }
    if (activity.isCash === 2) {
      // 团购返储值金
      return '返储值金说明';
    }
  },

  // 底部按钮提示条
  goodsBottomBarTip(state, getters) {
    const commonTip = getters.goodsBottomDisabledTip;
    if (commonTip.text) {
      return commonTip;
    }
    if (getters.activityNotStart) {
      return {
        text: '活动即将开始',
      };
    }
    if (getters.activityHasFinish) {
      return {
        text: '活动已结束',
      };
    }
    return {
      text: '',
    };
  },

  // 大按钮
  bigButtons(state, getters) {
    if (getters.isBigButtonForbidBuy) {
      return getters.commonBigButtons;
    }
    // 活动结束或活动未开始
    if (getters.activityHasFinish) {
      return [
        {
          forbid: true,
          text: '查看店铺其他商品',
          url: getters.homepageUrl,
          skuScene: SKU_SCENE.JUMP_LINK,
          primary: true,
        },
      ];
    }

    const activity = getters.currentActivity;

    const bigButtons = [];

    if (activity.isCash) {
      // 下单返现
      bigButtons.push({
        text: '邀请下单返现',
        type: 'invite',
        share: true,
        skuScene: SKU_SCENE.SHARE,
      });
    }

    let primaryBtn = null;
    const {
      accountUnionScene: ACCOUNT_UNION_SCENE = {},
    } = state.businessConfigs;
    switch (activity.state) {
      // 开团
      case 'on':
        primaryBtn = {
          text: '立即参团',
          primary: true,
          skuScene: SKU_SCENE.ACT_BUY,
          accountUnionScene: ACCOUNT_UNION_SCENE.GROUP_BUY,
        };
        break;
      default:
        primaryBtn = {
          text: '即将开始',
          primary: true,
          disabled: true,
          skuScene: SKU_SCENE.DISABLED,
        };
        break;
    }

    if (primaryBtn) {
      bigButtons.push(primaryBtn);
    }
    return bigButtons;
  },

  showGoodsCoupon() {
    return false;
  },
};
