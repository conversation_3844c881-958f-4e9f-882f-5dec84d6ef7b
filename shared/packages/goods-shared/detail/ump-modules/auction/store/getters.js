import { get, args, formatPrice } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';

export default {
  customStock() {
    return true;
  },
  isAuction(state) {
    return !!get(state, 'marketActivity.auction', null);
  },
  currentActivity(state) {
    return get(state, 'marketActivity.auction', {});
  },
  showActivityBlock() {
    return true;
  },
  // 是否显示活动推广栏
  showActivityBanner() {
    return true;
  },
  // 销量展示
  showSoldNum() {
    return false;
  },
  isSupportFCodeBuy() {
    return false;
  },
  // 降价拍商品不展示多网点推荐商品, 为啥嘞。。
  hideAvailableStores() {
    return true;
  },
  // 价格标签
  goodsPriceTag() {
    return '降价拍';
  },
  // sku活动标记
  activitySkuPriceTags() {
    return ['降价拍'];
  },

  activityPrice(state, getters) {
    const { minPrice = 0, maxPrice = 0, oldPrice = '' } = state.goods;
    const priceTag = getters.goodsPriceTag || '';
    return {
      priceTag,
      // 是否是价格区间
      isRange: minPrice !== maxPrice,
      showPrice: formatPrice(minPrice).split('.'),
      originPrice: oldPrice,
    };
  },

  // 倒计时模块
  countdown(state, getters) {
    if (!getters.isAuction) {
      // 因为是异步加载活动，初始值可能不存在
      return null;
    }

    const { status, nextCountdown } = getters.currentActivity;

    let { nextEndTime, refreshTime } = state.auctionState;
    if (!nextEndTime) {
      nextEndTime = refreshTime + nextCountdown;
    }
    const now = +new Date();
    let desc;
    let end;
    if (status === 'SOLD_OUT') {
      desc = '已售罄';
      end = now;
    } else if (status === 'ENDED') {
      desc = '活动已结束';
      end = now;
    } else {
      desc = '距下次降价';
      end = nextEndTime;
    }

    const stockNum = get(state, 'goods.stockNum', 0);

    return {
      showProgress: true,
      progressNum: getters.leftStockNumProgress,
      progressDesc: `仅剩${stockNum}件`,
      desc,
      end,
    };
  },

  // 活动介绍
  activityRules(state, getters) {
    const { stepTime, stepPrice } = getters.currentActivity;
    const stepTimeSec = stepTime / 1000;
    const stepTimeStr =
      stepTimeSec / 60 >= 1
        ? `${Math.floor(stepTimeSec / 60)}分钟降`
        : `${stepTimeSec % 60}秒降`;

    return {
      label: `降价幅度：每${stepTimeStr}${formatPrice(stepPrice)}元`,
      ruleTitle: '抢拍规则',
      rules: [
        '1. 什么是降价拍？',
        '降价拍是商品价格由高到低依次递减，直至竞买人以自己可承受的价格抢单支付成功，活动结束。',
        '2. 怎样算抢拍成功？',
        '每位竞买人，均有一次抢拍资格，支付成功后算作抢拍成功。若抢拍商品库存小于同时拍下付款用户数，则系统内先付款用户视作抢拍成功，后付款用户抢拍不成功。抢拍失败的客户，商家可对其进行退款。',
      ],
    };
  },

  // 底部按钮提示条
  goodsBottomBarTip(state, getters) {
    const commonTip = getters.goodsBottomDisabledTip;
    if (commonTip.text) {
      return commonTip;
    }
    const { status } = getters.currentActivity;
    let text = '';
    switch (status) {
      case 'ENDED':
        text = '活动已结束';
        break;
      case 'SOLD_OUT':
        text = '已售罄';
        break;
      default:
        break;
    }
    return {
      text,
    };
  },

  // 大按钮
  bigButtons(state, getters) {
    if (getters.isBigButtonForbidBuy) {
      return getters.commonBigButtons;
    }
    const { status } = getters.currentActivity;
    // 活动结束但未参加过砍价
    if (status === 'ENDED' || status === 'SOLD_OUT') {
      return [
        {
          forbid: true,
          text: '查看店铺其他商品',
          url: getters.homepageUrl,
          skuScene: SKU_SCENE.JUMP_LINK,
          primary: true,
        },
      ];
    }

    // 默认其他情况都支持原价购买
    const {
      accountUnionScene: ACCOUNT_UNION_SCENE = {},
    } = state.businessConfigs;
    const bigButtons = [
      {
        text: '立即拿下',
        skuScene: SKU_SCENE.ACT_BUY,
        accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
      },
    ];

    return bigButtons;
  },

  goodsDetailTabs(state) {
    const { showBuyRecord } = state.shopConfig;
    const tabs = [];

    tabs.push({
      type: 'goods',
      desc: '商品详情',
    });

    if (showBuyRecord) {
      tabs.push({
        type: 'sales',
        desc: '成交记录',
      });
    }

    tabs.push({
      type: 'auction',
      desc: '中拍/超卖',
    });

    return tabs;
  },

  activeDetailTabIndex(state, getters) {
    let { defaultType } = state.goodsDetail;
    const { goodsDetailTabs, currentActivity } = getters;
    const isAuctionSoldOut = currentActivity.status === 'SOLD_OUT';

    const showAuctionLog = args.get('show_auction_log') === 'true';
    let index = 0;

    if (showAuctionLog || isAuctionSoldOut) {
      defaultType = 'auction';
    }

    for (let i = 0, l = goodsDetailTabs.length; i < l; i++) {
      if (goodsDetailTabs[i].type === defaultType) {
        index = i;
        break;
      }
    }

    return index;
  },

  // 降价拍不展示优惠券模块
  showGoodsCoupon() {
    return false;
  },
};
