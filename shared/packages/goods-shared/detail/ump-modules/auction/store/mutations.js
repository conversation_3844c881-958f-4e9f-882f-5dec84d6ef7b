import { formatPrice } from '@goods/utils';

export default {
  updateAuctionState(state, status = {}) {
    const { count } = state.auctionState;
    const { price, nextEndTime } = status;
    state.goods = {
      ...state.goods,
      price: formatPrice(price),
      minPrice: price,
      maxPrice: price,
    };
    state.auctionState = {
      ...state.auctionState,
      count: count + 1,
      nextEndTime,
    };
    const priceObj = {
      price,
      collectionPrice: price,
      minPrice: price,
      maxPrice: price,
    };
    // 说明已经请求过sku数据了
    Object.assign(state.originActivitySku, priceObj);
  },

  fetchAuctionLog(state) {
    state.auctionLog.loading = true;
  },

  fetchAuctionLogSuccess(state, auctionList) {
    const { page, list, ...rest } = state.auctionLog;

    state.auctionLog = {
      ...rest,
      list: [...auctionList, ...list],
      page: page + 1,
      loading: false,
      finished: list.length < 5
    };
  },

  fetchAuctionLogFail(state) {
    state.auctionLog.loading = false;
  }
};
