import { fullfillImage } from '@goods/utils';
import api from '@goods/utils/api';

export default {
  // 倒计时结束
  countdownEnded({ state, getters, commit, dispatch }) {
    const activity = getters.currentActivity;
    const {
      currentPrice,
      stepPrice,
      endPrice,
      nextCountdown,
      stepTime,
    } = activity;
    const { refreshTime, count } = state.auctionState;
    const price = currentPrice - count * stepPrice;
    const nextEndTime = refreshTime + nextCountdown + count * stepTime;
    if (price < endPrice) {
      dispatch('reload');
      return;
    }
    commit('updateAuctionState', {
      price,
      nextEndTime,
    });
  },

  // 检查降价拍库存
  checkAuctionStock({ state, getters }) {
    const app = getApp();

    return api.get({
      url: '/v2/ump/auction/auction.json',
      data: {
        kdt_id: state.shop.kdtId,
        auction_id: getters.currentActivity.activityId,
        sid: app.getSessionId(),
        is_weapp: 1
      },
    });
  },

  // 降价拍需要获取实时数据更新库存
  fetchActivitySkuData({ state, getters, commit, dispatch }) {
    // 降价拍商品触发购买前需要先校验库存，
    return dispatch('checkAuctionStock').then(data => {
      const { stock_num: stockNum = 0 } = data;
      if (stockNum === 0) {
        dispatch('reload');
        return Promise.reject('库存不足');
      }
      commit('updateSkuData', {
        ...state.originActivitySku,
        stockNum,
      });
      // 更新限购
      commit('updateSkuLimit', getters.currentActivity.limit);
      return Promise.resolve();
    });
  },

  getAuctionLogs({ commit, state }) {
    commit('fetchAuctionLog');

    api
      .get({
        url: '/v2/showcase/goodsAuction/paidList.json',
        data: {
          kdt_id: state.shop.kdtId,
          auction_id: state.marketActivity.auction.activityId,
          page: state.auctionLog.page,
          perpage: 5,
        },
      })
      .then(data => {
        const list = data.list.map(item => ({
          ...item,
          avatar: fullfillImage(item.fans_avatar, '!160x160.jpg'),
        }));
        commit('fetchAuctionLogSuccess', list);
      })
      .catch(() => {
        commit('fetchAuctionLogFail');
      });
  },
};
