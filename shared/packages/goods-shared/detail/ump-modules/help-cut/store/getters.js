import { get, formatPrice } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';
import { getUrl, HELP_CUT } from '@goods/common/page-path';
import args from '@youzan/weapp-utils/lib/args';

export default {
  customStock() {
    return true;
  },
  currentActivity(state) {
    return get(state, 'marketActivity.helpCut', {});
  },
  showActivityBlock() {
    return true;
  },
  // 是否显示活动推广栏
  showActivityBanner() {
    return true;
  },
  // 价格标签
  goodsPriceTag() {
    return '砍价享';
  },

  // sku活动标记
  activitySkuPriceTags(state, getters) {
    return [getters.goodsPriceTag];
  },

  // 活动未开始
  activityNotStart(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = activity.startAt - now;
    return delta > 0;
  },

  // 活动已结束
  activityHasFinish(state, getters) {
    const activity = getters.currentActivity;
    const now = +new Date();
    const delta = activity.endAt - now;
    return delta < 0;
  },

  // 用户已参加活动
  hasJoinActivity(state, getters) {
    const activity = getters.currentActivity;
    return activity.status > 1;
  },

  // 活动跳转链接
  activityUrl(state, getters) {
    const activity = getters.currentActivity;
    return getUrl(HELP_CUT, {
      sponsorId: activity.sponsorId,
      umpActivityId: activity.activityId,
      kdtId: state.shop.kdtId,
    });
  },

  // 倒计时模块
  countdown(state, getters) {
    const activity = state.marketActivity.helpCut;
    if (!activity) {
      // 因为是异步加载活动，初始值可能不存在
      return null;
    }
    const { startAt, endAt } = activity;
    const now = +new Date();
    let desc;
    let end;
    if (getters.activityNotStart) {
      desc = '距开始仅剩';
      end = startAt;
    } else if (!getters.activityHasFinish) {
      desc = '距结束仅剩';
      end = endAt;
    } else {
      desc = '活动已结束';
      end = now;
    }

    const stockNum = get(state, 'goods.stockNum', 0);

    return {
      showProgress: true,
      progressNum: getters.leftStockNumProgress,
      progressDesc: `仅剩${stockNum}件`,
      desc,
      end,
    };
  },

  // 活动介绍
  activityRules(state, getters) {
    const shopName = state.shop.name;
    const price = getters.activityPrice.showPrice.join('.');
    const { marketActivity = {} } = state;
    const { helpCut = {} } = marketActivity;
    const { startNum = 1, joinNum = 1 } = helpCut;
    return {
      label: '邀请好友砍价, 超低价购买心仪产品!',
      rules: [
        `1. 活动期间消费者可邀请好友帮忙砍价，好友砍到底价 ${price} 元后，即可按 ${price} 元购买商品；`,
        `2. 好友帮忙砍到底价 ${price} 元后，消费者须在有效期内购买砍价商品，逾期商品将恢复零售价；`,
        `3. 同一商品，同一用户仅可享受${
          startNum == 1 ? '一' : startNum
        }次优惠价格`,
        `4. 同一商品，同一用户仅能帮好友砍价${
          joinNum == 1 ? '一' : joinNum
        }次；`,
        '5. 砍价商品数量有限。商品售罄后，商品将无法购买，即使你已经发起砍价；',
        `6. 若系统判断用户为恶意刷单，${shopName}针对已下单的商品有权不予发货；对于刷单金额较大或行为较为恶劣者，${shopName}有权追究用户的法律责任。`,
      ],
    };
  },

  // 底部按钮提示条
  goodsBottomBarTip(state, getters) {
    if (getters.activityNotStart) {
      return {
        text: '活动即将开始',
      };
    }
    if (getters.activityHasFinish && !getters.hasJoinActivity) {
      return {
        text: '砍价活动已结束，去看看其他商品吧',
      };
    }
    // 优先判断活动自己的逻辑
    const commonTip = getters.goodsBottomDisabledTip;
    if (commonTip.text) {
      return commonTip;
    }
    return {
      text: '',
    };
  },

  // 大按钮
  bigButtons(state, getters) {
    if (getters.isBigButtonForbidBuy) {
      return getters.commonBigButtons;
    }
    // 活动结束但未参加过砍价
    if (getters.activityHasFinish && !getters.hasJoinActivity) {
      return [
        {
          forbid: true,
          text: '查看店铺其他商品',
          url: getters.homepageUrl,
          skuScene: SKU_SCENE.JUMP_LINK,
          primary: true,
        },
      ];
    }

    const activity = getters.currentActivity;
    const {
      accountUnionScene: ACCOUNT_UNION_SCENE = {},
    } = state.businessConfigs;
    // 默认其他情况都支持原价购买
    const bigButtons = [
      {
        sub: `￥${formatPrice(state.goods.oldMinPrice, 2)}`,
        text: '单独购买',
        skuScene: SKU_SCENE.SEL_SKU,
        accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
      },
    ];

    let primaryBtn = null;

    // 活动已开始
    if (!getters.activityNotStart) {
      switch (activity.status) {
        // 已砍价购买
        case 2:
          primaryBtn = {
            forbid: true,
            text: '已砍价购买',
            disabled: true,
            skuScene: SKU_SCENE.DISABLED,
          };
          break;
        // 正在砍价
        case 3:
          primaryBtn = {
            text: '正在砍价',
            url: getters.activityUrl,
            skuScene: SKU_SCENE.JUMP_LINK,
            primary: true,
          };
          break;
        // 未参加砍价
        default:
          primaryBtn = {
            sub: `￥${formatPrice(state.goods.minPrice, 2)}`,
            text: '立即砍价拿',
            primary: true,
            skuScene: SKU_SCENE.START_CUT,
            subscribeScene: 'helpcut',
            accountUnionScene: ACCOUNT_UNION_SCENE.GROUP_BUY,
          };
          break;
      }
    }

    if (primaryBtn) {
      bigButtons.push(primaryBtn);
    }
    return bigButtons;
  },

  // 砍价不展示优惠券模块
  showGoodsCoupon() {
    return false;
  },

  activityInfoForPrice(state, getters) {
    const activity = getters.currentActivity;
    return {
      type: 'helpCut',
      identification: activity.activityId,
    };
  },

  followExtraData(state, getters) {
    const activity = getters.currentActivity;
    const activityId = activity.activityId;
    const feature = args.getAll(state.goods.path) || {};

    return {
      bizCode: 1,
      bizSubCode: 0,
      activityKey: activityId,
      feature,
      targetUrl: state.goods.path,
    };
  },
};
