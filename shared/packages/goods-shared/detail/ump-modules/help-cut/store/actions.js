import Toast from '@vant/weapp/dist/toast/toast';
import api from '@goods/utils/api';
import { args, jumpLink } from '@goods/utils';

export default {
  startCut({ state, dispatch, commit, getters }) {
    const activity = getters.currentActivity;

    if (!activity.activityId) {
      return Toast('缺少必要参数');
    }
    if (state.skuConfig.isAjaxing) {
      return Toast('正在砍价中');
    }

    commit('setSkuConfig', {
      isAjaxing: true,
    });
    Toast.loading({
      message: '正在砍价...',
      forbidClick: true,
    });
    api
      .post({
        url: args.add('/wscump/bargain-purchase/create.json', {
          activityId: activity.activityId,
        }),
      })
      .then((data = {}) => {
        Toast.clear();
        commit('updateActivity', { sponsorId: data.sponsorId, status: 3 });
        jumpLink(
          args.add(getters.activityUrl, {
            sponsorId: data.sponsorId,
          })
        );
      })
      .catch(err => {
        // 需关注后帮砍
        if (err.code === 160540352) {
          Toast.clear();
          commit('togglePopupShow');
          return;
        }
        Toast(err.msg || '发起砍价失败，请重试'); // eslint-disable-line
      })
      .then(() => {
        commit('setSkuConfig', {
          isAjaxing: false,
        });
      });
  },
};
