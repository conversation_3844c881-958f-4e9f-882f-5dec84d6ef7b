import { get } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';

export default {
  isGift(state) {
    return !!get(state, 'goodsActivity.gift', null);
  },

  miniButtons() {
    return [];
  },

  bigButtons(state) {
    const {
      accountUnionScene: ACCOUNT_UNION_SCENE = {},
    } = state.businessConfigs;
    return [
      {
        text: '我要送礼',
        skuScene: SKU_SCENE.GIFT_BUY,
        accountUnionScene: ACCOUNT_UNION_SCENE.ADD_CART,
      },
    ];
  },

  skuBuyBtnText() {
    return '去付款';
  },
};
