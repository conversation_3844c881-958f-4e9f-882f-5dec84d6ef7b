import { GoodsComponent } from '@goods/common/vanx-component';
import { getUrl, GROUPON } from '@goods/common/page-path';
import { mapState } from '@youzan/vanx';
import { jumpLink } from '@goods/utils';
import noneSkuToOrderGoodsData from '@goods/common/none-sku-order-goods-data';

GoodsComponent({
  name: 'GrouponGoingListItem',

  properties: {
    alias: String,
    kdtId: Number,
    title: String,
    gapNum: Number,
    thumbUrl: String,
    countDown: String,
  },
  state: ['goods', 'activityInfo', 'originSku', 'originActivitySku'],

  getters: [
    'grouponGoingList',
    'startSaleNum',
    'currentActivity',
    'isDirectOrder',
  ],

  computed: mapState({
    joinUrl(state) {
      const { activityType } = state.activityInfo;
      const query = {
        alias: this.data.alias,
        kdt_id: this.data.kdtId,
      };

      if (activityType) {
        query.type = activityType;
      }

      return getUrl(GROUPON, query);
    },
  }),

  methods: {
    jumpTo() {
      const {
        goods,
        startSaleNum,
        currentActivity,
        activityInfo,
        isDirectOrder,
        alias,
        originActivitySku,
      } = this.data;
      
      // 如果是无规格商品拼团
      if (isDirectOrder) {
        const { ongoingGroup = [] } = currentActivity;
        const tempArr = ongoingGroup.filter(item => item["groupAlias"] === alias );
        const { joinNum, remainJoinNum } = tempArr && tempArr[0]
        const scaleNum = Number(joinNum) + remainJoinNum;
        Object.assign(activityInfo, { scaleNum });
        const postData = noneSkuToOrderGoodsData({ goods, originActivitySku, activityInfo }, { startSaleNum });
        return this.$dispatch('handleBuy', { detail: postData });
      }

      jumpLink(this.joinUrl);
    }
  }
});
