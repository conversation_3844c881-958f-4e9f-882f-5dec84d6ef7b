<view
    wx:if="{{ !isSoldOut && grouponGoingList && grouponGoingList.length > 0 }}"
    class="van-cell-group"
  >
    <common-cell
      is-link="{{ !!grouponGoingPath }}"
      custom-class="groupon-going-list__header"
      title-class="groupon-going-list__header-title"
      title="以下小伙伴正在发起拼团，你可以直接参加"
      url="{{ grouponGoingPath }}"
    />
    <van-cell-group
      border="{{ false }}"
      class="groupon-going-list__content van-hairline--bottom"
    >
      <groupon-going-list-item
        wx:for="{{ grouponGoingList }}"
        wx:key="groupAlias"
        alias="{{ item.groupAlias }}"
        kdt-id="{{ shop.kdtId }}"
        title="{{ item.fansNickname }}"
        thumb-url="{{ item.fansPicture }}"
        gap-num="{{ item.remainJoinNum }}"
        count-down="{{ timeList[index] }}"
      />
    </van-cell-group>
  </view>