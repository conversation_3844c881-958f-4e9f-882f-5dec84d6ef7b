<view class="groupon-going-item">
  <image
    src="{{ thumbUrl }}"
    alt="头像"
    class="groupon-going-item__img"
  />
  <view class="groupon-going-item__content">
    <view class="groupon-going-item__title">{{ title }}</view>
    <view class="groupon-going-item__desc">
      还差 <text class="groupon-going-item__gap-num">{{ ' ' + gapNum + ' ' }}</text> 人成团，剩余
      <text> {{ countDown }}</text>
    </view>
  </view>
  <theme-view
    bind:tap="jumpTo"
    custom-class="groupon-going-item__button"
    color="main-text"
    bg="main-bg"
  >
    去凑团
  </theme-view>
</view>