@import '~mixins/index.scss';

.groupon-going-item {
  display: flex;
  align-items: center;
  height: 58px;
  padding: 12px 0;
  box-sizing: border-box;

  &::after {
    display: none;
  }

  &__img {
    margin-right: 16px;
    width: 34px;
    height: 34px;
    border-radius: 50%;
  }

  &__content {
    flex-grow: 1;
    font-size: 12px;
    color: $gray-dark;
  }

  &__gap-num {
    color: $red;
  }

  &__title {
    line-height: 16px;
    color: $text-color;
  }

  &__button {
    border-radius: 1rem;
    height: 24px;
    line-height: 24px;
    padding: 0 4px;
    font-size: 12px;
    min-width: 60px;
    text-align: center;
  }

  .cap-countdown {
    &__day,
    &__hour,
    &__minute,
    &__second,
    &__time-text {
      margin: 0;
      padding: 0;
      color: $gray-dark;
    }
  }

  .van-button {
    background-color: transparent;
    color: $red;
  }
}

@media screen and (max-width: 320px) {
  .groupon-going-item {
    &__desc {
      font-size: 10px;
    }

    .cap-countdown {
      &__day,
      &__hour,
      &__minute,
      &__second,
      &__time-text {
        font-size: 10px;
      }
    }
  }
}