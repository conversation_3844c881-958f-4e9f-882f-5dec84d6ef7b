import { GoodsComponent } from '@goods/common/vanx-component';
import CountDown from 'utils/countdown';

GoodsComponent({
  state: ['shop'],

  getters: ['grouponGoingList', 'grouponGoingPath', 'isSoldOut'],

  ready() {
    const times = (this.data.grouponGoingList || []).map(item => item.remainTime);
    this.runCountDown(times);
  },

  methods: {
    runCountDown(times) {
      this.countDown = new CountDown(times, {
        onChange: (timeData) => {
          const timeList = timeData.map(({ day, hour, minute, second }) => ((day > 0 ? `${day}天` : '') + `${hour}:${minute}:${second}`));

          this.setYZData({
            timeList
          });
        }
      });
    }
  },

  detached() {
    this.countDown && this.countDown.stop && this.countDown.stop();
  },
});
