import { get, formatPrice } from '@goods/utils';
import { SKU_SCENE, COUNTDOWN_STATUS } from '@goods/common/config';
import {
  getUrl,
  GROUPON_GUIDE,
  GROUPON_LIST,
  GROUPON,
  GROUPON_GOING,
} from '@goods/common/page-path';

export default {
  isGroupOn() {
    return true;
  },

  isLadderGroupOn(state, getters) {
    return (
      getters.isGroupOn && getters.currentActivity.subType === 'ladderGroupOn'
    );
  },

  activityNotStart() {
    return false;
  },

  currentActivity(state) {
    return get(state, 'marketActivity.groupOn', {});
  },

  showActivityBlock() {
    return true;
  },

  // 是否显示活动推广栏
  showActivityBanner() {
    return true;
  },

  // 是否隐藏sku 原价
  hideSkuOriginPrice() {
    return false;
  },

  isSkuBarDisabled() {
    return true;
  },

  isSupportFCodeBuy() {
    return false;
  },

  // 价格标签
  goodsPriceTag(state, getters) {
    const activity = getters.currentActivity;
    if (getters.isLadderGroupOn) {
      return '人多更优惠';
    }

    if (activity.groupType === 1) {
      // 老带新拼团
      return `老带新${getters.currentActivity.joinNum}人团`;
    }

    return `${getters.currentActivity.joinNum}人拼团价`;
  },

  ladderGrouponSkuExtraList(state, getters) {
    const skuId = getters.skuId || 0;

    return getters.currentActivity.ladderList[skuId].map((item) => {
      return {
        ...item,
        price: formatPrice(item.skuPrice) + (!skuId ? '起' : ''),
      };
    });
  },

  // sku活动标记
  activitySkuPriceTags(state, getters) {
    const { skuScene } = state.skuConfig;
    if (getters.isLadderGroupOn && skuScene === SKU_SCENE.ACT_BUY) {
      return ['拼团价'];
    }

    return [getters.goodsPriceTag];
  },

  // 活动显示销量
  activityStockNumText(state) {
    const { soldNum = 0 } = state.goods;
    if (soldNum > 99999) {
      return '10W+';
    }
    if (soldNum > 9999) {
      return '1W+';
    }
    if (soldNum > 999) {
      return '999+';
    }
    return `${soldNum}`;
  },

  ladderMinPrice(state, getters) {
    const lodderPrices = getters.ladderGrouponSkuExtraList.map(
      (item) => item.skuPrice
    );
    const minPrice = Math.min(...lodderPrices);
    const maxPrice = Math.max(...lodderPrices);

    return {
      priceString: `${formatPrice(minPrice)}${
        minPrice === maxPrice ? '' : '起'
      }`,
      price: minPrice,
    };
  },
  // 倒计时模块
  countdown(state) {
    const activity = state.marketActivity.groupOn;
    if (!activity) {
      // 因为是异步加载活动，初始值可能不存在
      return null;
    }
    const now = state.clientTimestamp;
    const { startAt, endAt } = activity;
    let desc;
    let end;
    let status;
    if (startAt - now > 0) {
      desc = '距开始仅剩';
      end = startAt;
      status = COUNTDOWN_STATUS.notStart;
    } else if (endAt - now > 0) {
      desc = '距结束仅剩';
      end = endAt;
      status = COUNTDOWN_STATUS.started;
    } else {
      desc = '活动已结束';
      end = now;
      status = COUNTDOWN_STATUS.ended;
    }

    return {
      showProgress: false, // activity.joinPeopleNum > 0,
      // hideProgressBar: true,
      // progressDesc: `已拼${activity.joinPeopleNum}人`,
      desc,
      end,
      status,
    };
  },

  groupOnActivityTitle(state, getters) {
    const activity = getters.currentActivity;
    if (getters.isLadderGroupOn) {
      return '拼团玩法-阶梯拼团';
    }

    if (activity.groupType === 1) {
      return '拼团玩法-老带新拼团';
    }

    return '拼团玩法';
  },
  // 活动介绍
  grouponActivityIntro(state, getters) {
    const activity = getters.currentActivity;
    let activityIntro = `支付开团邀请${activity.joinNum -
      1}人参团，人数不足自动退款。`;
    if (activity.groupType === 1) {
      // 老带新拼团
      activityIntro = `支付开团邀请${activity.joinNum -
        1}名新用户参团，人数不足自动退款。`;
    }

    if (getters.isLadderGroupOn) {
      const ladder = activity.ladderList[0];
      const ladderIntro = ladder.reduce((intro, item) => {
        intro.push(`${item.scale}人团￥${formatPrice(item.skuPrice)}起`);
        return intro;
      }, []);

      ladderIntro.push(
        '先选择参团人数，支付后开团邀请好友参团，人数不足自动退款。'
      );
      activityIntro = ladderIntro.join('，');
    }
    return activityIntro;
  },

  grouponIntroUrl(state, getters) {
    const activity = getters.currentActivity;

    return getUrl(GROUPON_GUIDE, {
      kdt_id: getters.kdtId,
      groupType: activity.groupType,
      activityType: state.activityInfo.activityType || '',
    });
  },

  allGrouponUrl(state, getters) {
    return getUrl(GROUPON_LIST, {
      kdt_id: getters.kdtId,
      activityType: state.activityInfo.activityType || '',
    });
  },

  // 正在拼团的列表
  grouponGoingList(state, getters) {
    const activity = getters.currentActivity;
    return activity.ongoingGroup || [];
  },

  grouponGoingPath(state, getters) {
    const { activityId, activityType } = state.activityInfo;

    return getUrl(GROUPON_GOING, {
      kdtId: getters.kdtId,
      activityId,
      activityType,
    });
  },

  // 底部按钮
  // 大按钮
  bigButtons(state, getters) {
    if (getters.isBigButtonForbidBuy) {
      return getters.commonBigButtons;
    }
    const {
      accountUnionScene: ACCOUNT_UNION_SCENE = {},
    } = state.businessConfigs;
    const activity = getters.currentActivity;
    const cart = {
      text: `加入${getters.cartText}`,
      skuScene: SKU_SCENE.ADD_CART,
      accountUnionScene: ACCOUNT_UNION_SCENE.ADD_CART,
    };

    const { buttonUmpTextModel = {} } = state;
    const { umpTag = '', buyButtonText = '' } = buttonUmpTextModel;

    const openBtn = {
      sub:
        umpTag ||
        `￥${
          getters.isLadderGroupOn
            ? getters.ladderMinPrice.priceString
            : formatPrice(state.goods.minPrice, 2)
        }`,
      text: buyButtonText || '立即开团',
      primary: true,
      skuScene: SKU_SCENE.ACT_BUY,
      subscribeScene: 'groupon',
      accountUnionScene: ACCOUNT_UNION_SCENE.GROUP_BUY,
    };

    const buyBtn = {
      sub: `￥${formatPrice(state.goods.oldMinPrice, 2)}`,
      text: '单独购买',
      skuScene: getters.isPresale ? SKU_SCENE.NORMAL_BUY : SKU_SCENE.SEL_SKU,
      accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
    };
    // const shareBtn = {
    //   text: '邀请好友参团',
    //   share: true,
    // };
    const grouponDetail = getUrl(GROUPON, {
      alias: activity.groupAlias,
      kdt_id: getters.kdtId,
      type: state.activityInfo.activityType,
    });

    const viewBtn = {
      text: '查看我的团',
      url: grouponDetail,
      primary: true,
      skuScene: SKU_SCENE.JUMP_LINK,
    };
    const btnType = activity.buttonType;

    // 万人团要过滤掉
    if (activity.groupAlias && btnType !== 4) {
      // return [shareBtn, viewBtn];
      return [buyBtn, viewBtn];
    }

    // 通过type判断
    const bigButtons = [...getters.commonBigButtons];

    switch (btnType) {
      case 0:
      case 2:
        bigButtons.push(buyBtn);
        bigButtons.push(openBtn);
        break;
      case 1:
        // bigButtons.push(shareBtn);
        bigButtons.push(buyBtn);
        bigButtons.push(viewBtn);
        break;
      case 3:
        openBtn.text = '我要参团';
        bigButtons.push(openBtn);
        break;
      case 4:
        bigButtons.push(buyBtn);
        viewBtn.text = `￥${formatPrice(state.goods.minPrice, 2)}参团`;
        bigButtons.push(viewBtn);
        break;
      case 5:
        bigButtons.push(cart);
        buyBtn.primary = true;
        bigButtons.push(buyBtn);
        break;
      default:
        break;
    }
    return bigButtons;
  },

  // 拼团单独购买
  isGrouponSingleBuy(state) {
    const { skuScene } = state.skuConfig;
    return skuScene !== SKU_SCENE.ACT_BUY;
  },

  // 是否售罄
  isSoldOut(state, getters) {
    // 商品未下架、非外链、未售罄
    return (
      state.goods.isDisplay === 1 &&
      !getters.showAvailableStores &&
      !state.goods.isOutlink &&
      !state.goods.stockNum &&
      !getters.isSupportFCodeBuy
    );
  },
};
