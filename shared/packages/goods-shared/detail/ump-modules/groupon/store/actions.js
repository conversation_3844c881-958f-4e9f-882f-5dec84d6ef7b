import api from '@goods/utils/api';
import { fullfillImage, makeRandomString, mapKeysToCamelCase } from '@goods/utils';
import { GOODS, getUrl } from '@goods/common/page-path';

export default {
  getRecommendGoods({ state, getters, dispatch, commit }) {
    commit('getRecommendGoods');

    api
      .get({
        url: '/wscshop/goods-api/recommend-groupon-goods.json',
        data: {
          bizName: 'goods_detail',
          goodsId: state.goods.id,
          alias: getters.alias,
          storeId: state.multistore.id,
          itemSize: 10,
          pageSize: 10
        },
        errorMsg: '获取拼团推荐商品错误'
      })
      .then((data) => {
        const list = data.map((item, index) => {
          const bannerId = `g.${state.goods.id}~recommend_fixed~${index + 1}~${makeRandomString(
            8
          )}`;
          const loggerParams = {
            goods_id: item.id + '',
            item_id: item.id,
            item_type: 'groupon',
            banner_id: bannerId,
            component: 'recommend_fixed',
            recommend_name: '更多精选商品',
            component_id: '', // type + index
            alg: item.algs || ''
          };

          item = mapKeysToCamelCase(item);

          item.url = getUrl(GOODS, {
            alias: item.alias,
            banner_id: bannerId,
            alg: loggerParams.alg
          });

          item.price = (+item.minPrice / 100).toFixed(2);
          if (item.joinNum) {
            item.showTitleTag = true;
            item.titleTagText = `${item.joinNum}人拼团价`;
          }

          return {
            ...item,
            loggerParams,
            imageUrl: fullfillImage(item.imageUrl, '!520x0.jpg')
          };
        });

        if (list.length > 0) {
          // 显示遮罩
        }
        commit('getRecommendGoodsSuccess', list);
        dispatch('toggleGoodsMask', getters.showNoStockRecommendGoods && getters.hasRecommendGoods);
      })
      .catch((err) => {
        commit('getRecommendGoodsFail', err);
      });
  }
};
