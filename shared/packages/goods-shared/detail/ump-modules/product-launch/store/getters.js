import { SKU_SCENE, WX_SUBSCRIBE_SCENE } from '@goods/common/config';

export default {
  isProductLaunch() {
    return true;
  },

  // 新品发售的情况把定时上架倒计时屏蔽了
  waitToSoldCountdown() {
    return false;
  },

  // 展示活动腰封
  showActivityBanner() {
    return true;
  },

  showActivityBlock() {
    return true;
  },

  countdown(state) {
    const now = +new Date();
    const isWaitToSold = state.goods.startSoldTime - now;

    let desc;
    let end;
    // 这里用waitToSold会造成下拉重渲
    if (isWaitToSold > 0) {
      desc = '距预约结束仅剩';
      end = state.goods.startSoldTime;
    } else {
      desc = '火热开售中';
      end = now;
    }

    return {
      showProgress: false,
      desc,
      end,
    };
  },

  productLaunchButtons(state) {
    const btn = {
      primary: true,
      text: '立即预约',
      skuScene: SKU_SCENE.PRODUCT_LAUNCH,
      subscribeSceneV2: state.display.showSaleReminder
        ? WX_SUBSCRIBE_SCENE.SALE_REMINDER
        : undefined,
    };

    return btn;
  },
};
