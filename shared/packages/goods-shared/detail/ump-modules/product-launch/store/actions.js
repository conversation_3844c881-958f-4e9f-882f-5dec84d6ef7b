import { get } from '@goods/utils';
import Toast from '@vant/weapp/dist/toast/toast';
import openWebView from 'shared/utils/open-web-view';

const app = getApp();

export default {
  productLaunchReservation({ state, dispatch }) {
    const kdtId = get(state, 'shop.kdtId', 0);
    const goodsId = get(state, 'goods.id', 0);
    const activityId = get(state, 'marketActivity.productLaunch.activityId', '0');
    const activityAlias = get(state, 'marketActivity.productLaunch.activityAlias', '');

    Promise.all([
      app
        .request({
          method: 'POST',
          path: '/wscgoods/activity-api/product-launch/reservation.json',
          data: {
            goodsId,
            alias: activityAlias
          }
        })
        .then(() => {
          return true;
        })
        .catch((error) => {
          Toast({
            message: error.msg || '新品发售预约失败',
            position: 'bottom'
          });
        }),
      dispatch('saleReminderHandle')
    ]).then(([result]) => {
      if (result) {
        openWebView(`/wscindustry/product-launch/${activityId}`, {
          title: '新品发售',
          query: {
            kdtId
          }
        });
      }
    });
  }
};
