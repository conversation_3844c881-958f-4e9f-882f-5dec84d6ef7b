import { args } from '@goods/utils';

let pageId = 0;
export const HOME = pageId++;
export const GOODS = pageId++;
export const CART = pageId++;
export const MEMBER = pageId++;
export const HELP_CUT = pageId++;
export const GROUPON = pageId++;
export const GROUPON_GUIDE = pageId++;
export const GROUPON_LIST = pageId++;
export const GROUPON_GOING = pageId++;
export const LOTTERY = pageId++;
export const LOTTERY_RESULT = pageId++;
export const MEMBER_CARD = pageId++;
export const GOODS_REVIEW = pageId++;
export const MEET_REDUCE = pageId++;
export const PACKAGE_BUY = pageId++;
export const SECOND_HALF = pageId++;
export const PLUS_BUY = pageId++;

export const SHOP_INFO = pageId++;
export const HELP_PRESALE = pageId++;
export const DISCOUNT_PACKAGE = pageId++;
// 内购裂变
export const IN_SOURCING_FISSION = pageId++;

export const WEBVIEW = '/pages/common/webview-page/index';

export function getWebviewPath(url, query) {
  return args.add(
    WEBVIEW,
    { src: encodeURIComponent(args.add(url, query)) }
  );
}
const p = 'packages';
export const PAGE_MAP = {
  [HOME]: `/${p}/home/<USER>/index`,
  [GOODS]: '/pages/goods/detail/index',
  [CART]: `/${p}/goods/cart/index`,
  [MEMBER]: `/${p}/ump/membercard-groupon/index`,
  [HELP_CUT]: `/${p}/ump/bargain-purchase/home/<USER>
  [GROUPON]: `/${p}/ump/pintuan/detail/index`,
  [LOTTERY]: `/${p}/collage/lottery/detail/index`,
  [LOTTERY_RESULT]: `/${p}/collage/lottery/result/index`,
  [MEMBER_CARD]: `/${p}/card/detail/index`,
  [GOODS_REVIEW]: `/${p}/evaluation/goods/list/index`,
  [GROUPON_GUIDE]: (query) => getWebviewPath('/wscump/groupon/guide', query),
  [SHOP_INFO]: (query) => getWebviewPath('/wscassets/shopinfo', query),
  [GROUPON_LIST]: query => getWebviewPath('/wscump/groupon/list', query),
  [GROUPON_GOING]: (query) => getWebviewPath('/wscump/collage/groupon/join-groups', query),
  [MEET_REDUCE]: `/${p}/ump/meet-reduce-goods/index`,
  [PACKAGE_BUY]: `/${p}/ump/bundle-purchase/goods-list/index`,
  [SECOND_HALF]: `/${p}/ump/second-half-discount/index`,
  [PLUS_BUY]: `/${p}/ump/plusbuy/index`,
  [HELP_PRESALE]: `/${p}/ump/handsel-expand/index`,
  [DISCOUNT_PACKAGE]: `/${p}/ump/discount-${p}/index`,
  // 内购裂变
  [IN_SOURCING_FISSION]: `/${p}/ump/in-sourcing-fission/index`,
};
/**
 * 统一页面路径输出
 * @param {*} pageName
 * @param {*} query
 */
export function getUrl(pageName, query) {
  const getPath = PAGE_MAP[pageName];

  if (typeof getPath === 'function') {
    return args.add(getPath(query), query);
  }

  if (!getPath || typeof getPath !== 'string') {
    throw Error(`接收到一个错误的 ${pageName}，请传入正确的 pageName`);
  }
  return args.add(getPath, query);
}
