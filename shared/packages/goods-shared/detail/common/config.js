// 按钮的场景信息，根据场景弹出相应的SKU或其他操作
export const SKU_SCENE = {
  // 活动购买，如团购、秒杀、拼团。。。
  // 区别在于postData需要带上activityId
  // sku活动标与其他区别，弹活动sku
  ACT_BUY: 'actBuy',
  // 普通购买
  // 区别在于postData不需要带上activityId
  NORMAL_BUY: 'normalBuy',
  // 单独处理积分商品，弹积分sku
  POINTS_BUY: 'pointsBuy',
  // 送礼，行为如同NORMAL_BUY，送礼享受会员折扣
  GIFT_BUY: 'giftBuy',
  // 选择SKU-会显示 加入购物车/购买
  SEL_SKU: 'selSku',
  // 加入购物车，享受会员折扣
  ADD_CART: 'addCart',
  // 加入心愿单
  ADD_WISH: 'addWish',
  // 空按钮，无法点击
  EMPTY: 'empty',
  // 分享
  SHARE: 'share',
  // 预约 如秒杀预约
  RESERVATION: 'reservation',
  // 开始砍价
  START_CUT: 'startCut',
  // 不可购买状态
  FORBID: 'forbid',
  // 预售
  PRESELL: 'presell',
  // 售罄
  SELL_OUT: 'sellOut',
  // 下架
  OFF_SHELVES: 'offShelves',
  // 商品上架提醒
  SALE_REMINDER: 'saleReminder',
  // 新品发售预约
  PRODUCT_LAUNCH: 'productLaunch',
  // 跳转
  JUMP_LINK: 'jumpLink',
  // 禁用
  DISABLED: 'disabled',
  // 补货提醒
  STOCK_REMINDER: 'stockReminder',
  // 引导办会员
  LIMIT_GUIDE: 'limitGuide',
};

// 可购买的sku集合，用于自动弹起sku
export const SKU_SCENE_BUY_SETS = new Set([
  SKU_SCENE.NORMAL_BUY,
  SKU_SCENE.POINTS_BUY,
  SKU_SCENE.SEL_SKU,
  /**
   * 1. 解决只有加入购物车按钮时，第一次点击规格模块无法弹出sku弹窗的问题
   * 2. 解决商品自定义上架时间时，只有加入购物车按钮，第一次点解规格模块无法弹出sku弹窗的问题
   */
  SKU_SCENE.ADD_CART,
]);

// 可预览sku的skuScene 的场景
export const SKU_SCENE_PREVIEW = new Set([
  // 1. 预售
  SKU_SCENE.PRESELL,
  // 2. 售罄
  SKU_SCENE.SELL_OUT,
  // 3. 下架
  SKU_SCENE.OFF_SHELVES,
]);

export const SKU_SEPERATOR = '；';

// 小程序消息订阅场景标志
export const WX_SUBSCRIBE_SCENE = {
  // 开售提醒
  SALE_REMINDER: '3ItemSaleRemind',
  // 补货提醒
  STOCK_REMINDER: '3StockRemind',
  // 秒杀
  UMP_SECKILL_BOOK: 'umpSeckillBookRemind',
};

export const COUNTDOWN_STATUS = {
  notStart: 0,
  started: 1,
  ended: 2,
};

export const SWITCH = {
  on: 1,
  off: 0,
};
