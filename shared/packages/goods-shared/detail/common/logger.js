import spm from 'utils/log/spm';

export default function setUmpLogger(activitys, ACTVITY_TYPE_ID_MAP = {}) {
  const umpParams = [];
  const app = getApp();
  const keys = Object.keys(ACTVITY_TYPE_ID_MAP);

  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const activity = activitys[key];

    if (activity) {
      // 诸如阶梯团，type是groupOn，subType是ladderGroupOn
      const subKey = activity.subType || activity.type || key;
      const actType = activity.code || ACTVITY_TYPE_ID_MAP[subKey] || '';
      let actId = '';

      if (!Array.isArray(activity)) {
        actId = activity.id || activity.activityId || '';
      }

      umpParams.push({
        act_type: String(actType),
        act_id: String(actId),
      });
    }
  }

  // 优惠套餐
  if (activitys.packageBuy) {
    activitys.packageBuy.forEach((v) => {
      umpParams.push({
        act_type: '7',
        act_id: String(v.id),
      });
    });
  }

  if (umpParams.length) {
    app.logger.log({
      et: 'custom',
      ei: 'view_goods',
      params: {
        ump_params: umpParams,
        spm: spm.getSpm(),
      },
    });
  }
}

export function getSkuLogData({ skuExtraData, originSku }) {
  const { selectedSku } = skuExtraData;
  const { skuId } = originSku;

  const skuName = [];
  selectedSku &&
    Object.keys(selectedSku).forEach((kS) => {
      const vId = selectedSku[kS];
      const originSkuItem = originSku.tree.find((item) => item.kS === kS);
      if (originSkuItem && vId) {
        const v = originSkuItem.v.find((v) => v.id === vId);
        skuName.push({
          k: originSkuItem.k,
          k_id: originSkuItem.kId,
          k_s: kS,
          v: v.name,
          v_id: v.id,
        });
      }
    });

  return {
    sku_id: skuId,
    sku_name: skuName,
  };
}

const app = getApp();
export function setLogger(logData = {}) {
  if (typeof logData !== 'object') {
    logData = {
      event: {
        message: logData,
      },
    };
  }
  app.logger.log(logData);
}
