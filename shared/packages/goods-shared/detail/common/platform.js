const global = {
  platformSpecialUI: {},
  platform: 'weixin',
};

export function setPlatfromSpecialUI(platform, platformSpecialUI = {}) {
  global.platformSpecialUI = platformSpecialUI;
}

export const isDangerousPlatform = () => {
  const dangerousPlatformSets = new Set(global.platformSpecialUI.dangerousPlatform);
  return dangerousPlatformSets.has(global.platform);
};

export const hideShareIcon = () => {
  const hideShareIconSets = new Set(global.platformSpecialUI.hideShareIcon);

  return hideShareIconSets.has(global.platform);
};
