import logv3, { reportSkynet } from 'utils/log/logv3';

const app = getApp();
const { logger } = app;

export function doSubmitLog(type, extra = {}) {
  if (!logger) {
    return;
  }

  const logMap = {
    im: {
      type: 'consult',
      name: '点击咨询',
    },
    buy: {
      type: 'buy',
      name: '立即购买',
    },
    addCart: {
      type: 'add_cart',
      name: '添加购物车',
    },
    addWish: {
      type: 'add_wish',
      name: '添加心愿单',
    },
    gift: {
      type: 'gift',
      name: '送礼',
    },
  };
  const logData = logMap[type];
  if (!logData) {
    return;
  }

  const { goods = {}, extraData = {} } = extra;
  const v3LogData = {
    et: 'click',
    ei: logData.type,
    en: logData.name,
    params: {
      goods_alias: goods.alias,
      goods_id: goods.id,
      goods_name: goods.title,
      group_ids: [],
      picture_url: goods.picture,
      price: goods.minPrice / 100,
      show_price: goods.price,
      ...extraData,
    },
  };

  return logger.log(v3LogData);
}

export function formatBaseData(data) {
  const logGlobalInfo = app.logger.getGlobal() || {};
  const contextInfo = logGlobalInfo.context || {};
  // 商品属性
  const propertyIds = (data.properties || []).reduce((acc, cur) => {
    (cur.v || []).forEach((it) => {
      acc.push(it.id);
    });
    return acc;
  }, []);

  let goodsData = {
    goods_id: data.goodsId,
    num: data.num,
    sku_id: data.skuId,
    price: data.price || 0,
    // 负责人启航; 归因参数, 由 java http 直接打到 cookie
    // atr_uuid: cookie('atr_uuid') || '',
    // 广告字段
    dc_ps: logv3.getDCPS(),
    qr: data.qr || '',
    tpps: data.tpps || '',
    // // f码商品
    fcode: data.fcode,
    // 广点通来源追踪标识
    gdtClickId: contextInfo.click_id || '',
    messages: data.messages,
    ...data.messages,
    isInstallment: data.isInstallment,
    propertyIds,
    // 是否是7天无理由退款商品
    isSevenDayUnconditionalReturn: data.isSevenDayUnconditionalReturn,
  };

  const { IS_SELECTED_PRIOR_USE_PAY_WAY = 0 } = data;

  const commonData = {
    kdt_id: data.kdtId,
    store_id: data.storeId || 0,
    store_name: data.storeName || '',
    postage: data.postage || 0,
    activity_alias: data.activityAlias || '',
    activity_id: data.activityId || 0,
    activity_type: data.activityType || 0,
    use_wxpay: data.useWxpay || 0,
    // 订单来源
    from: data.from || '',
    // 无用字段
    order_from: data.orderFrom || '',

    // 分步极速下单，订单页需要的参数
    isPartialFastAdOrder: data.isPartialFastAdOrder,
    expressTypeChoice: data.expressTypeChoice,
  };

  // 积分兑换商品
  if (data.isPoints) {
    goodsData.points_price = data.pointsPrice;
    goodsData.price = data.price;
  }
  // f码商品
  if (data.fcode) {
    goodsData.fcode = data.fcode;
  }
  // 周期购商品
  if (typeof data.deliverTime === 'number') {
    goodsData.deliver_time = data.deliverTime;
  }
  // 广点通来源追踪参数
  if (data.gdtClickId) {
    goodsData.gdt_id = data.gdtClickId;
  }

  if (data.channelId) {
    // 送礼商品channelId 30
    goodsData.channel_id = data.channelId;
  }

  if (data.scaleNum) {
    commonData.ladder_num = data.scaleNum;
  }

  // 酒店商品
  if (data.hotelGoods) {
    commonData.hotel_goods = data.hotelGoods;
  }
  // 电子卡券价格日历
  if (data.card_goods) {
    commonData.card_goods = data.card_goods;
  }

  // 分期信息
  if (data.selectedInstallmentPeriod) {
    commonData.installmentRate = data.installmentRate;
    commonData.selectedInstallmentPeriod = data.selectedInstallmentPeriod;
  }

  // 送礼
  if (data.orderType) {
    commonData.order_type = data.orderType;
  } else if (data.isGift) {
    commonData.order_type = 1;
  }
  // 积分商品
  if (data.isPoints) {
    commonData.is_points = data.isPoints;
  }
  // 来源参数
  if (data.from) {
    commonData.from = data.from;
  }
  // 登录凭证
  if (data.loginTicket) {
    commonData.loginTicket = data.loginTicket;
  }
  // 下单成功后的跳转页面
  if (data.paymentSuccessRedirect) {
    commonData.payment_success_redirect = data.paymentSuccessRedirect;
  }

  // 内购裂变需要添加内购劵
  if (data.isInSourcingFission) {
    // 当前sku需要的内购劵数量
    goodsData.item_fission_tickets_num =
      data.needInSourcingCouponNum / data.num;
    // 当前活动的type
    commonData.fission_name = data.umpType;
    // 当前购买一共需要多少内购劵
    commonData.fission_ticket_num = data.needInSourcingCouponNum;
  }

  const extraData = {
    IS_SELECTED_PRIOR_USE_PAY_WAY,
  };
  // eslint-disable-next-line camelcase
  const biz_trace_point_ext = goodsData.biz_trace_point_ext || {};
  const bizTracePointExt = logv3.getTradeLog(biz_trace_point_ext);

  // 企微助手交易归因参数
  if (data.wecom_uuid) {
    biz_trace_point_ext.wecom_uuid = data.wecom_uuid;
    goodsData = {
      ...goodsData,
      biz_trace_point_ext: bizTracePointExt,
    };
  }
  // 2021.06.23 @子一 @伴月 @洲泛 为了解决视频号直播助手无法获取正确的scene值问题，从log中取实时scene传递到下单，便于定义归因和直播助手数据统计
  const wxChannelScene = [1175, 1176, 1177, 1195];
  const goodsScene = contextInfo.scene;
  const tradeLog = logv3.getTradeLog();
  if (wxChannelScene.indexOf(goodsScene) > -1) {
    goodsData = { ...goodsData, biz_trace_point_ext: tradeLog };
  }

  return {
    goodsData,
    commonData,
    extraData,
  };
}

// 格式化购物车请求数据
export function formatAddCartData(data) {
  const { goodsData, commonData } = formatBaseData(data);

  const messages = [];
  Object.keys(data.cartMessages).forEach((key) => {
    messages.push(data.cartMessages[key]);
  });

  const result = {
    ...goodsData,
    ...commonData,
    messages: JSON.stringify(messages),
    biz_trace_point_ext: logv3.getTradeLog(goodsData.biz_trace_point_ext),
    propertyIds: JSON.stringify(goodsData.propertyIds),
  };
  return result;
  // return objectToQuery(result);
}

// 格式化下单请求数据
export function formatBuyData(data) {
  const { goodsData, commonData, extraData } = formatBaseData(data);

  const result = {
    goodsList: [goodsData],
    common: commonData,
    extra: extraData,
    biz_trace_point_ext: logv3.getTradeLog(goodsData.biz_trace_point_ext),
  };
  return result;
}

// export function doAppSdkAction({ type, skuData, goods }) {
//   const { title, alias, picture } = goods;
// action.doAction({
//   action: type,
//   num: skuData.num,
//   sku_id: skuData.skuId,
//   item_id: skuData.goodsId,
//   pay_price: skuData.price,
//   title,
//   alias,
//   picture,
// });
// }

// 将sku的留言部分格式化为 { 留言名称: 留言值 }
export function makeSkuMessageToMap(sku = {}, postMessages = {}) {
  const { messages = [] } = sku;
  const result = {};
  messages.forEach((it, index) => {
    result[it.name] = postMessages[`message_${index}`] || '';
  });
  return result;
}

export function doErrorLog(buyType) {
  reportSkynet('goodsBuyFailed', {
    appname: 'jserror-wap',
    logIndex: 'trade',
    name: 'goodsBuyFailed',
    message: JSON.stringify({
      buyType,
    }),
    level: 'info',
  });
}
