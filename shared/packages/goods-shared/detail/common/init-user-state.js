import { get } from '@goods/utils';
import api from '@goods/utils/api';
import { getUserLocation, calculateDistance } from '@goods/utils/location';
import { checkRetailMinimalistShop } from '@youzan/utils-shop';
import { getScene } from 'shared/utils/channel';

const app = getApp();

const fetchUserLocation = (pageData) => {
  const { offlineData, shopMetaInfo } = pageData;
  // 多网点&门店信息（仅D版）需要展示距离
  if (offlineData.id || checkRetailMinimalistShop(shopMetaInfo)) {
    return getUserLocation();
  }
  return Promise.resolve({ lat: 0, lng: 0 });
};

const UmpPageSets = new Set([
  'seckill',
  'groupOn',
  'helpCut',
  'tuan',
  'presentExchange',
  'luckyDrawGroup',
  'auction',
  'fCode',
  // 内购裂变活动
  'inSourcingFission',
  'productLaunch',
  // 视频号限时秒杀
  'limitedSeckill',
]);

// 设置活动全局变量
const parseMarketingActivities = (activities = []) => {
  if (activities.length === 0) {
    return {};
  }
  const parsedActivities = {};
  // 优惠套餐
  const packageBuy = activities.filter((v) => v.type === 'packageBuy');

  if (packageBuy.length) {
    parsedActivities.packageBuy = packageBuy;
  }

  activities.forEach((promotion) => {
    let { type } = promotion;
    if (type === 'auction') return; // 小程序暂时不支持降价拍
    if (type === 'customerDiscount') {
      // 如果不打折就不显示会员折扣了
      if (+promotion.discount !== 1) {
        parsedActivities.discount = promotion;
      }
      const recommendedPriceModel = promotion.recommendedPriceModel || {};
      // 当 recommendedStatus 为1 时，说明展示卡 存在; 现在会员折扣和展示卡 可以叠加了；
      if (recommendedPriceModel.recommendedStatus === 1) {
        parsedActivities.discountInvite = promotion;
      }
    } else if (type === 'goodsScan') {
      // 扫码优惠 和 会员折扣用同一个字段
      parsedActivities.discount = promotion;
    } else if (type !== 'packageBuy') {
      if (type === 'ladderGroupOn') {
        type = promotion.type = 'groupOn';
        // 阶梯拼团属于拼团大类
        promotion.subType = 'ladderGroupOn';
      }
      parsedActivities[type] = promotion;

      // 选择渲染的营销组件，否则按照普通商品处理
      if (UmpPageSets.has(type)) {
        parsedActivities.pageType = type;
      }
    }
  });
  return parsedActivities;
};

const formatUserStateData = (data = {}, pointsName) => {
  const {
    marketing,
    pageFeature,
    // 用户已购数量
    quotaUsed,
    // 是否有购买权限，如VIP限购
    hasPurchaseRight,
    // 海淘税费
    haitaoTariffAmount = '',
    salableStores,
    physicalStores,
    soldNum,
    spuStock,
    skuStocks,
    // 限购引导信息
    buyLimitGuideInfo,
    buyLimitType = 0,
  } = data;
  // umpLimit 商品限流标识
  const {
    umpLimit = false,
    activities = [],
    orderActivity = {},
    buttonUmpTimelimitModel = {},
    aggregationActivities: parsedPromotions = [],
    buttonUmpTextModel,
    outerActivities = [],
  } = marketing;

  const parsedActivities = parseMarketingActivities(activities);

  return {
    promotionLimit: umpLimit,
    // 促销活动列表
    parsedPromotions,
    // 活动倒计时模块
    buttonUmpTimelimitModel,
    // 所有的营销活动
    parsedActivities,
    // 随心订
    outerActivities,
    activityInfo: orderActivity,
    pageFeature,
    goodsExtra: {
      quotaUsed,
      hasPurchaseRight,
      haitaoTariffAmount,
      pointsName,
      buyLimitGuideInfo,
      buyLimitType,
    },
    salableStores,
    physicalStores,
    soldNum,
    spuStock,
    skuStocks,
    isFastBuy: false,
    buttonUmpTextModel,
  };
};

const postUserStateJson = (pageData, { lng = 0, lat = 0 }) => {
  const { goodsData, shopData, pageParams, offlineData, shopMetaInfo } =
    pageData;

  const {
    alias,
    goods: { id: goodsId },
    shopConfig = {},
    delivery = {},
  } = goodsData;
  // 是否是多网点 开启独立库存；offlineSettings independentStock
  const independentStock =
    offlineData.status &&
    offlineData.offlineSettings &&
    offlineData.offlineSettings.separateStock;
  const { kdtId } = shopData;
  const { id: offlineId, address: offlineAddress } = offlineData || {};
  const { shopType, shopRole } = shopMetaInfo;
  // 是否有可用配送方式
  const {
    supportExpress = true,
    supportLocalDelivery = true,
    supportSelfFetch = true,
  } = delivery;
  // 一些按钮展示的店铺设置
  const {
    hideShoppingCart = 0,
    freightInsurance = 1,
    isGift = 1,
    isWebImInGoods = 1,
    isWish = 1,
    showBuyBtn = 1,
    showShopBtn = 1,
    showWscWebIm = 1,
    scrmCreditDiyName = '{}',
    goodsActivityTagsPreposition = 1,
  } = shopConfig;

  let pointsConfig = {};
  try {
    pointsConfig = JSON.parse(scrmCreditDiyName);
  } catch (e) {
    // ...
  }
  const { name: pointsName = '积分' } = pointsConfig;
  // 获取场景值;
  const scene = getScene();

  // 参数回传给后端为了优化异步接口性能
  const requestQuery = {
    ...pageParams,
    alias,
    goodsId,
    kdtId,
    offlineId,
    stockNum: get(goodsData, 'skuInfo.spuStock.stockNum', 0),
    supportExpress,
    supportLocalDelivery,
    supportSelfFetch,
    shopType,
    shopRole,
    pointsName,
    hideShoppingCart,
    freightInsurance,
    isGift,
    isWebImInGoods,
    isWish,
    showBuyBtn,
    showShopBtn,
    showWscWebIm,
    goodsActivityTagsPreposition,
    soldOutRecommendSwitch: get(
      goodsData,
      'shop.multiStore.setting.soldOutRecommendSwitch',
      false
    ),
    isFastBuy: get(goodsData, 'fastbuyFeature.isFastBuy', false),
    buyBtnConfig: get(goodsData, 'shopConfig.buyBtnConfig', ''),
    // 限时秒杀添加的参数；
    extra: {
      sceneId: String(scene),
    },
    // refHost: location.hostname,
    // refUrl: encodeURIComponent(location.href),
  };

  // 限时秒杀添加的参数；
  if (independentStock) {
    requestQuery.extra.independentStock = '1';
  }

  // 判断是不是限时秒杀；
  const WECHAT_LIVE_SENCE = 1177;
  if (+scene !== WECHAT_LIVE_SENCE) {
    delete requestQuery.extra;
  }

  return api
    .post({
      url: '/wscgoods/weapp/user-goods-state.json',
      headers: {
        'content-type': 'application/json',
      },
      data: {
        ...requestQuery,
        lng,
        lat,
      },
    })
    .then((data = {}) => {
      const result = formatUserStateData(data, pointsName);

      // 当前用户地理位置
      const { lng: storeLng, lat: storeLat } = offlineAddress || {};
      const distance = calculateDistance(storeLat, storeLng, lat, lng);

      result.geoLocationData = {
        distance,
        lng,
        lat,
      };

      return Promise.resolve(result);
    });
};

export function initUserState(pageData) {
  return Promise.resolve()
    .then(() => fetchUserLocation(pageData))
    .then(({ lng, lat }) => postUserStateJson(pageData, { lng, lat }));
}
