/**
 * 生成周期文案
 *
 * @param {string} quotaPeriod
 *
 * @return {string}
 */
const PERIOD_TEXT_MAP = ['每天', '每周', '每月'];
function getPeriodText(quotaPeriod) {
  const periodText = PERIOD_TEXT_MAP[quotaPeriod - 1] || '';
  return periodText;
}

/**
 * 生成单位
 *
 * @param {object} data
 *
 * @return {string}
 */
function getQuotaUnit({ isHotel = 0 }) {
  return isHotel ? '间*晚' : '件';
}

/**
 * 生成购买方式
 *
 * @param {object} data
 *
 * @return {array}
 */
function getBuyWay({ isPoints = 0 }) {
  return isPoints ? ['兑', '兑换'] : ['购', '购买'];
}

/**
 * 生成单词购买数量
 *
 * @param {object} data
 *
 * @return {number}
 */
function getSingleQuota({ singleQuota = -1, isVirtualTicket, isHaitao }) {
  if (singleQuota && singleQuota > 0) {
    return singleQuota;
  }
  if (isVirtualTicket || isHaitao) {
    return 100;
  }
  return -1;
}

/**
 *
 * @param {object} data
 *
 * param quota
 * param quotaUsed
 * param quotaPeriod
 * param isHotel
 * param isPoints
 * param singleQuota
 * param isVirtualTicket
 * param isHaitao
 * param startSaleNum
 *
 * @return {string}
 */
export function getQuotaText(data) {
  const { quotaPeriod, startSaleNum = 1, quotaUsed = 0, quota = 0 } = data;

  const singleQuota = getSingleQuota(data);
  const periodText = getPeriodText(quotaPeriod);
  const quotaUnit = getQuotaUnit(data);
  const [buyWay] = getBuyWay(data);
  const arr = [];
  if (startSaleNum > 1) {
    arr.push(`${startSaleNum}${quotaUnit}起售`);
  }
  if (singleQuota > 0 && quota - quotaUsed >= singleQuota) {
    arr.push(`一次限${buyWay}${singleQuota}${quotaUnit}`);
  } else if (quota > 0) {
    arr.push(`${periodText}限${buyWay}${quota}${quotaUnit}`);
  }
  return arr.join('，');
}

/**
 *
 * @param {object} data
 *
 * param action
 * param limitType
 * param quota
 * param quotaUsed
 * param quotaPeriod
 * param isHotel
 * param isPoints
 * param singleQuota
 * param isVirtualTicket
 * param isHaitao
 * param startSaleNum
 *
 * @return {string}
 */
export function getOverLimitText(data) {
  const {
    quotaPeriod,
    startSaleNum = 1,
    limitType,
    quotaUsed = 0,
    quota = 0,
    action,
  } = data;
  const singleQuota = getSingleQuota(data);
  const periodText = getPeriodText(quotaPeriod);
  const quotaUnit = getQuotaUnit(data);
  const [buyWay, buyWayFull] = getBuyWay(data);
  if (action === 'minus') {
    return startSaleNum > 1
      ? `该商品${startSaleNum}${quotaUnit}起售哦`
      : `至少选择一${quotaUnit}商品`;
  }
  if (action === 'plus') {
    if (limitType === 0) {
      if (singleQuota > 0 && quota - quotaUsed >= singleQuota) {
        return `一次最多能${buyWayFull}${singleQuota}${quotaUnit}`;
      }
      let msg = `该商品每人${periodText}限${buyWay}${quota}${quotaUnit}`;
      if (quotaUsed > 0) {
        msg += `，\n你之前已${buyWayFull}了${quotaUsed}${quotaUnit}。`;
      }
      return msg;
    }
    return '该商品库存不足';
  }
  return '';
}
