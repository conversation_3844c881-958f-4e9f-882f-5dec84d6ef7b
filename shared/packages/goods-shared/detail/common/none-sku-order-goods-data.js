// 无规格商品直接下单；
const noneSkuToOrderGoodsData = (state = {}, getters = {}, btn = {}) => {
  const { goods, originActivitySku, activityInfo } = state || {};
  const { startSaleNum: selectedNum } = getters;
  const { id: goodsId, stockNum: stock_num } = goods || {};
  const { price, skuId: id } = originActivitySku || {};

  const postData = {
    goodsId,
    selectedNum,
    selectedSkuComb: {
      id,
      price,
      stock_num,
    },
    activityInfo,
  };
  if (btn && btn.accountUnionScene === 'click_buy_now') {
    delete postData.activityInfo;
  }
  return postData;
};

export default noneSkuToOrderGoodsData;