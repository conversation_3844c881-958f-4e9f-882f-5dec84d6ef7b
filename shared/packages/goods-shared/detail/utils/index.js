import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import args from '@youzan/weapp-utils/lib/args';
import get from '@youzan/weapp-utils/lib/get';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import navigate from 'shared/utils/navigate';
import { isObject } from './lodash-helper';
import { minBy, maxBy } from './array';
import { moment } from '@youzan/weapp-utils/lib/time';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import MoneyFormat from '@youzan/weapp-utils/lib/money';
import event from 'shared/utils/app-event';
import omit from '@youzan/weapp-utils/lib/omit';

const app = getApp();

function getCurrentDay(date) {
  return moment(date, 'YYYY-MM-DD');
}

// 为了保证前端数据和后端数据一致，原生 toFixed 方法算法和后端不一致
function toFixed(number, d) {
  let s = `${number}`;
  if (!d) d = 0;
  if (s.indexOf('.') === -1) s += '.';
  s += new Array(d + 1).join('0');
  if (new RegExp(`^(-|\\+)?(\\d+(\\.\\d{0,${d + 1}})?)\\d*$`).test(s)) {
    let s = `0${RegExp.$2}`;

    const pm = RegExp.$1;

    let a = RegExp.$3.length;

    let b = true;
    if (a === d + 2) {
      a = s.match(/\d/g);
      if (parseInt(a[a.length - 1], 10) > 4) {
        for (let i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i], 10) + 1;
          if (a[i] === 10) {
            a[i] = 0;
            b = i !== 1;
          } else break;
        }
      }
      s = a.join('').replace(new RegExp(`(\\d+)(\\d{${d}})\\d$`), '$1.$2');
    }
    if (b) s = s.substr(1);
    return (pm + s).replace(/\.$/, '');
  }
  return `${number}`;
}

function formatDistance(distance) {
  if (!distance) {
    return '';
  }
  if (distance <= 100) {
    return '<100m ';
  }
  if (distance > 100 && distance <= 1000) {
    return `${toFixed(distance, 0)}m `;
  }
  if (distance > 1000 && distance < 10000) {
    return `${toFixed(distance / 1000, 1)}km `;
  }
  return '>10km ';
}

function numberToPrice(number, decimal = 2, leaveZero = false) {
  if (typeof number !== 'number') return '';
  const price = (number / 100).toFixed(decimal);
  if (leaveZero) {
    return parseFloat(price);
  }
  return price;
}

/**
 *
 * @param {*} price string or {min: number, max: number}
 * @param {*} separator
 * @returns min ~ max or min
 */
function formatPrice(price, separator = '~') {
  if (isObject(price)) {
    return numberToPrice(price.min) + separator + numberToPrice(price.max);
  }
  return numberToPrice(price);
}

// 小程序不需要考虑链接问题，直接返回
function getRedirectUrl(url) {
  return url;
}

// 小程序不需要考虑链接问题，直接返回
function buildUrl(url) {
  return url;
}

const mapKeysToCamelCase = mapKeysCase.toCamelCase;
const mapKeysToSnakeCase = mapKeysCase.toSnakeCase;

const fullfillImage = cdnImage;

function jumpLink(url) {
  navigate.navigate({ url });
}

function getCdnImageUrl(url, formater) {
  return cdnImage(url, formater || '!100x100.jpg');
}

function appendLogParams(list, kdtId, log = {}, component = {}) {
  const randStr = makeRandomString(8);
  return list.map((item, index) => {
    const bannerId = `${log.spm}~${log.biz}~${index + 1}~${randStr}`;
    const loggerParams = {
      goods_id: item.id + '',
      item_id: item.id,
      item_type: 'goods',
      banner_id: bannerId,
      component: component.name,
      recommend_name: component.text,
      component_id: 'goods' + index,
      alg: item.algs
    };

    item = mapKeysToCamelCase(item);

    // item.url = args.add(
    //   getGoodsUrlByAlias(item.alias, kdtId),
    //   {
    //     banner_id: bannerId,
    //     alg: loggerParams.alg,
    //   }
    // );

    item.price = formatPrice(item.price);

    return {
      ...item,
      loggerParams,
      imageUrl: fullfillImage(item.imageUrl, '!520x0.jpg')
    };
  });
}

function getDetailPath(query = {}) {
  return args.add('/pages/goods/detail/index', query);
}

// 下载图片获得临时路径
function loadImage(src) {
  return new Promise((resolve, reject) => {
    app.downloadFile({
      url: src,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (e) => {
        reject(e);
      }
    });
  });
}

function moneyFormat(price = 0) {
  return MoneyFormat(price).toYuan();
}
function splitPrice(price = 0) {
  const priceForYuan = moneyFormat(price);
  const [yuan, fen = '00'] = priceForYuan.split('.');

  return {
    yuan,
    fen,
    desc: priceForYuan
  };
}

function or(v1, v2) {
  if (typeof v1 === 'undefined') {
    return v2;
  }

  return v1;
}

// 一个中文顶2个英文字符
function getRealLen(str) {
  // 这个把所有双字节的都给匹配进去了
  /* eslint-disable */
  return str.replace(/[^\x00-\xff]/g, '__').length;
}

export {
  or,
  // cookie,
  event,
  get,
  args,
  omit,
  isObject,
  minBy,
  maxBy,
  toFixed,
  buildUrl,
  fullfillImage,
  getCdnImageUrl,
  getRedirectUrl,
  formatPrice,
  numberToPrice,
  formatDistance,
  moment,
  getCurrentDay,
  makeRandomString,
  mapKeysToCamelCase,
  mapKeysToSnakeCase,
  moneyFormat,
  jumpLink,
  appendLogParams,
  loadImage,
  splitPrice,
  getDetailPath,
  getRealLen
};
