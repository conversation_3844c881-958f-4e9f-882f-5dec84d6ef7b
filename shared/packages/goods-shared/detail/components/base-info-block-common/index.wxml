  <view class="module-unit {{ firstPageRendered ? 'module-unit--fade-in' : ''}}">
    <!-- 商品活动价在activity里 -->
    <goods-activity wx:if="{{ showPriceBanner }}"/>
    <view class="m-base-info">
      <!-- 普通商品价格 -->
      <block wx:if="{{ showPriceBlock }}">
        <goods-price wx:if="{{ !showActivityBanner }}" class="u-base-info-row"/>
        <points-price wx:if="{{ isPointsExchange }}" class="u-base-info-row"/>
        <goods-old-price wx:if="{{ !showActivityBanner }}" class="u-base-info-row"/>
      </block>

      <slot name="topPromotion"  wx:if="{{ showTopGoodsPromotion }}"/>
      <!-- 公众号涨粉 -->
      <fans-benefit class="u-base-info-row" />
      <!-- 推荐会员折扣卡 -->
      <customer-discount class="u-base-info-row"/>
      <!-- 商品标题 -->
      <goods-title class="u-base-info-row last-child" />
      <!-- 导购预计赚 -->
      <guide-promote class="u-base-info-row" goods="{{ goods }}" />
      <!-- 全款预售叠加 -->
      <goods-presale-bar
        wx:if="{{ presaleTimeDesc }}"
        tag="{{ baseInfoBarTag }}"
        text="{{ presaleTimeDesc }}"
      />
      <!-- 限时折扣预告 -->
      <goods-presale-bar
        wx:if="{{ isTimelimitedDiscountNotStart }}"
        text="{{ timelimitTimeDesc }}"
        tag="{{ baseInfoBarTag }}"
      />
      <!-- 海淘税费 -->
      <goods-tax-bar />
    </view>
    <!-- 有赞担保 -->
    <guarantee-detail-bar 
      wx:if="{{ showGuaranteeBar }}"
      is-wx-promise="{{ isWxPromise }}"
      alias="{{ goods.alias }}"  
      kdt-id="{{ shop.kdtId }}"
      bind:onShow="guaranteeOnShow"
    />
  </view>
