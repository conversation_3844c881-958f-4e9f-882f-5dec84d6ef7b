@import '~mixins/index.scss';

.module-unit {
  opacity: 0;

  &--fade-in {
    opacity: 1;
  }
}

.m-base-info {
  position: relative;
  padding: 8px 16px 10px;
  background-color: $white;

  &.has-guarantee-bar {
    padding-bottom: 10px;
  }
}

.u-base-info-row {
  position: relative;
  width: 100%;
  font-size: 13px;
  line-height: 18px;
  display: block;

  &.last-child {
    margin-bottom: 8px;
  }
}

.u-base-padding-row {
  position: relative;
  width: 100%;
  padding: 10px;
  font-size: 14px;
  line-height: 18px;
  box-sizing: border-box;
}
