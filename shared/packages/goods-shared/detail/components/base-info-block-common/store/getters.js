import { get, formatPrice, numberToPrice } from '@goods/utils';
import { hideShareIcon } from '@goods/common/platform';
import args from '@youzan/weapp-utils/lib/args';
import { COUNTDOWN_STATUS } from '@goods/common/config';

const handleBenefitInfo = (benefits) => {
  const { preferentialType, description = '' } = benefits;
  let contentNormal = '';
  let contentHighLight = '';
  // 粉丝专享
  if (preferentialType === 1) {
    const matchRes = description.match(/\d+(\.\d+)?\S*$/g);
    contentHighLight = matchRes ? matchRes[0] : '';
    contentNormal = description.replace(contentHighLight, '');
    // 首次关注有礼
  } else if (preferentialType === 2) {
    contentNormal = description.slice(0, 1);
    contentHighLight = description.slice(1);
  }
  return {
    isFans: benefits.isFans,
    contentNormal,
    contentHighLight,
  };
};

const app = getApp();

function getRealLen(str) {
  // 这个把所有双字节的都给匹配进去了
  /* eslint-disable */
  return str.replace(/[^\x00-\xff]/g, '__').length;
}

// 排序方法
function compare(property) {
  return function (a, b) {
    var value1 = a[property];
    var value2 = b[property];
    return value1 - value2;
  };
}
export default {
  // 领券前置开关状态
  flagTopGoodsPromotion(state) {
    const showTopGoodsPromotion = get(
      state,
      'shopConfig.goodsActivityTagsPreposition',
      ''
    );
    return +showTopGoodsPromotion;
  },
  // 是否置顶
  showTopGoodsPromotion(state, getters) {
    if (!state.saas.promotionBlock.showPromotionList) {
      return false;
    }

    if (getters.topGoodsPromotionList && getters.topGoodsPromotionList.length) {
      let result =
        getters.topGoodsPromotionList.length && +getters.flagTopGoodsPromotion;
      return result;
    }
    return false;
  },
  // 置顶模块优惠券列表
  topGoodsPromotionList(state, getters) {
    const { umpCouponList = [] } = state;
    // 优惠卷列表
    const { goodsPromotion } = getters;
    const list = [...goodsPromotion] || [];
    const MAX_LIST_COUNT = 4;
    let arr = [];
    const couponListTags = [];

    umpCouponList.forEach((item) => {
      if (item.tag) {
        let quan = '券';
        quan += item.tag;
        couponListTags.push(quan);
      }
    });
    // 加入优惠券的行；
    if (umpCouponList.length && getters.showGoodsCoupon) {
      list.unshift({
        key: 'umpCouponList',
        title: '领券',
        index: umpCouponList[0].index,
        couponList: umpCouponList,
        tags: couponListTags || [],
      });
    }
    // 根据后端返回的index，做排序。
    list.sort(compare('index'));
    if (list.length) {
      // 共计tags数量\
      let num = 0;
      list.forEach((item) => {
        if (item.tags) {
          num += item.tags.length;
        }
      });
      // 排完序之后做一个数量限制
      const displayCouponList = list.slice(0, MAX_LIST_COUNT);
      // 促销类型长度
      const typeLength = displayCouponList.length;

      if (typeLength >= 3) {
        if (num <= 4) {
          for (let i = 0; i < typeLength; i++) {
            if (displayCouponList[i].tags) {
              if (
                displayCouponList[i].tags.length &&
                displayCouponList[i].tags[0]
              ) {
                if (i === 0) {
                  arr.push(...displayCouponList[i].tags.slice(0, 2));
                } else {
                  arr.push(displayCouponList[i].tags[0]);
                }
              }
            }
          }
        }
        if (num > 4) {
          for (let i = 0; i < typeLength; i++) {
            if (displayCouponList[i].tags) {
              if (
                displayCouponList[i].tags.length &&
                displayCouponList[i].tags[0]
              ) {
                arr[arr.length] = displayCouponList[i].tags[0];
              }
            }
          }
        }
      }

      if (typeLength === 2) {
        for (let i = 0; i < typeLength; i++) {
          if (displayCouponList[i].tags) {
            if (
              displayCouponList[i].tags.length &&
              displayCouponList[i].tags[0]
            ) {
              if (i === 0) {
                arr.push(...displayCouponList[i].tags.slice(0, 2));
              }
              if (i === 1) {
                arr.push(...displayCouponList[i].tags);
              }
            }
          }
        }
      }
      if (typeLength === 1 && displayCouponList[0].tags) {
        if (displayCouponList[0].tags.length && displayCouponList[0].tags[0]) {
          arr = [...displayCouponList[0].tags.slice(0, 4)];
        }
      }
    }

    // 3个的长度如果超过32的话，就隐藏第3个优惠券; 一个中文顶2个英文
    if (arr.length && arr.length >= 3) {
      const cnt = arr.reduce((pre, current) => {
        return pre + current;
      }, '');

      const MAX_DISPLAY_LENGTH = 28;
      if (getRealLen(cnt) >= MAX_DISPLAY_LENGTH) {
        arr = arr.slice(0, 2);
      }
    }
    return arr;
  },

  couponLabelPprocessing(state, getters) {
    const arr = [];
    getters.topGoodsPromotionList.forEach((item) => {
      let obj = {};

      if (item[0] === '券') {
        obj.title = item[0];
        obj.cont = item.substring(1);
        arr.push(obj);
      } else {
        arr.push({ title: '', cont: item });
      }
    });

    return arr;
  },

  isUmpCouponList(state, getters) {
    const { umpCouponList = [] } = state;
    if (umpCouponList.length && getters.showGoodsCoupon) {
      return '领券';
    }
    return '查看优惠';
  },
  showPriceBlock(state) {
    return state.saas.baseInfoBlock.showPriceBlock;
  },
  // 是否显示活动banner
  showPriceBanner(state, getters) {
    return (
      state.saas.baseInfoBlock.showActivityBanner && getters.showActivityBanner
    );
  },
  // 是否显示活动推广栏
  showActivityBanner(state, getters) {
    // 定金预售
    if (getters.isDepositPresale && !getters.isDepositPresaleOver) {
      return true;
    }
    // 限时折扣
    if (
      getters.isTimelimitedDiscount &&
      !getters.isTimelimitedDiscountNotStart
    ) {
      return true;
    }

    return false;
  },

  // 活动标题
  activityTitle(state, getters) {
    if (getters.isDepositPresale) {
      return '定金预售';
    }
    if (getters.discountActivity) {
      return getters.discountActivity.title;
    }
    return '';
  },

  // 券后价组合促销活动的前缀，比如 限时券后；
  activityPreferentialPrefix(state, getters) {
    const {
      preferentialPriceTag,
      isTimelimitedDiscount,
      isTimelimitedDiscountNotStart,
      isDepositPresale,
      isGroupOn,
      isVipTag,
      countdown,
      isHelpDepositPresale,
    } = getters;

    if (!preferentialPriceTag) {
      return '';
    }

    if (isTimelimitedDiscount && !isTimelimitedDiscountNotStart) {
      return '限时';
    }

    if (isGroupOn && countdown.status === COUNTDOWN_STATUS.started) {
      return '拼团';
    }

    if (isDepositPresale || isHelpDepositPresale) {
      return '预售';
    }

    if (isVipTag) {
      return '会员';
    }

    return '';
  },

  // 当券后价叠加了一些营销活动的时候，需要隐藏原来的价格标签；
  hideOriginPriceTag(state, getters) {
    const {
      isVipTag,
      isTimelimitedDiscount,
      isGoodsScan,
      activityPreferentialPrefix,
      preferentialPriceTag,
      isGrouponSingleBuy,
    } = getters;

    return (
      (activityPreferentialPrefix && (isVipTag || isTimelimitedDiscount)) ||
      (isGoodsScan && preferentialPriceTag) ||
      isGrouponSingleBuy
    );
  },

  activityPrice(state, getters) {
    // 定金预售
    const { minPrice = 0, maxPrice = 0, oldPrice = '' } = state.goods;
    const { preferentialPriceTag, presaleOffsetPriceRange } = getters;

    if (getters.isDepositPresale && !getters.isDepositPresaleOver) {
      const { presaleActivity } = getters;
      const { minDeposit } = presaleActivity;
      return {
        preferentialPriceTag,
        isRange: minPrice !== maxPrice,
        // 左上角大的价格显示原价；
        showPrice: formatPrice(minPrice).split('.'),
        // 底部显示预售价格；
        originPrice: formatPrice(minDeposit) + '',
        presaleOffsetPriceRange,
      };
    }

    const priceTag = getters.goodsPriceTag || '';
    if (getters.isLadderGroupOn) {
      return {
        priceTag,
        // 是否是价格区间
        isRange: true,
        showPrice: formatPrice(getters.ladderMinPrice.price).split('.'),
        originPrice: oldPrice,
      };
    }

    return {
      priceTag,
      // 券后价魔偶快
      preferentialPriceTag,
      // 是否是价格区间
      isRange: minPrice !== maxPrice,
      showPrice: formatPrice(minPrice).split('.'),
      originPrice: oldPrice,
    };
  },

  activityNotStart() {
    return true;
  },

  // 商品描述
  goodsSubTitle(state, getters) {
    if (getters.isPeriodbuy) {
      return getters.periodbuyActivity.description || '';
    }

    return state.goods.sellPoint || '';
  },

  // 商品标签
  goodsTag(state, getters) {
    if (get(state, 'goods.isHaitao')) {
      // 海淘商品图片
      return 'https://img01.yzcdn.cn/public_files/dc48da81df83c4e8c21db9a129a4f509.png';
    }

    if (getters.isPeriodbuy) {
      // 周期购商品图片
      return 'https://img01.yzcdn.cn/public_files/46732672ae85491670faaa3da111dd4d.png';
    }

    return '';
  },

  goodsRetailTag() {
    // 门店图片
    return 'https://img01.yzcdn.cn/public_files/2019/08/19/a4db0d30f3814d2d2117c91e5a6e2958.png';
  },

  baseInfoBarTag(state, getters) {
    let tag = getters.isPresale ? '预售' : '';

    if (getters.isTimelimitedDiscount) {
      tag = getters.timelimitedDiscountActivity.priceTitle || '限时折扣';
    }
    return tag;
  },

  // 商品标签，用于商品价格标题
  goodsPriceTag(state, getters) {
    // 小程序里 限时折扣和会员折扣互斥
    if (getters.isTimelimitedDiscount) {
      if (getters.isTimelimitedDiscountNotStart) {
        return '';
      }

      const timelimited = getters.timelimitedDiscountActivity; // 限时折扣需要显示活动栏
      if (timelimited.priceTitle) {
        return timelimited.priceTitle;
      }
      // let showTag = '限时';
      // showTag += timelimited.discountType === 2 ? timelimited.discountValue / 10 + '折' : '减价';
      // return showTag;
      return '限时折扣';
    }
    // 定金预售在activityPrice中重写过
    if (getters.discountActivity) {
      return getters.discountActivity.title;
    }
    return '';
  },

  isVipTag(state, getters) {
    const { discountActivity } = getters;
    // 会员折扣
    return (
      discountActivity.type === 'customerDiscount' && discountActivity.title
    );
  },

  // 海淘商品税费
  goodsHaitaoTax(state) {
    const haitao = get(state, 'goodsActivity.haitao', null);
    if (haitao) {
      const { tariffAmount, tariffRule } = haitao;
      if (tariffRule === 1) {
        // 0 不含税 1 含税， 含税不显示
        return '';
      }
      if (tariffAmount) {
        return `进口关税 预计 ${tariffAmount}`;
      }
    }
    return '';
  },

  // 海淘货源地
  goodsHaitaoCountry(state) {
    const haitao = get(state, 'goodsActivity.haitao', {});
    return haitao.sourceCountryName || '';
  },

  // 商品详情页标题栏是否显示分享按钮，默认显示
  showShareIcon(state, getters) {
    const { buyerShop } = state;
    // 有赞微商城app、精选、喜马拉雅等平台不展示分享按钮
    if (hideShareIcon()) {
      return false;
    }

    if (!state.saas.baseInfoBlock.showShareIcon) {
      return false;
    }

    if (getters.isBuyerShop && buyerShop.pageConfig.shareReward) {
      return false;
    }

    return true;
  },

  // 是否展示划线价/原价
  showOriginPrice(state, getters) {
    const { price, oldPrice, origin } = state.goods;

    if (getters.isPeriodbuy) {
      return {
        label: '',
        price: '',
      };
    }
    if (getters.isDepositPresale) {
      return {
        label: '总价',
        price: `￥${price}`,
      };
    }
    if (oldPrice && oldPrice !== price) {
      return {
        label: '价格',
        price: `￥${oldPrice}`,
      };
    }

    const originPrice = origin || '';
    if (/^\s*\d+(\.\d{1,2})?\s*$/.test(originPrice)) {
      return {
        label: '价格',
        price: `￥${originPrice.trim()}`,
      };
    }

    return {
      label: '',
      price: originPrice,
    };
  },

  // 会员推荐价格
  recommendedPriceModel(state) {
    const discountInvite = get(state, 'orderActivity.discountInvite', null);
    const discount = get(state, 'orderActivity.discount', null);
    if (discountInvite) {
      return discountInvite.recommendedPriceModel || {};
    }
    if (discount) {
      return discount.recommendedPriceModel || {};
    }
    return {};
  },

  // 是否展示全程护航
  showGuaranteeBar(state) {
    const show = state.saas.baseInfoBlock.showGuaranteeBar;
    // 覆盖不加载有赞担保组件的情况下，做埋点
    if (!show) {
      app.logger &&
        app.logger.log({
          et: 'view',
          ei: 'enterpage_goods_detail',
          en: '有赞担保曝光',
          params: {
            guarantee_on: show,
          },
          si: app.getKdtId(),
        });
    }
    return show;
  },

  // 推荐文案
  customerDiscountText(state, getters) {
    const { recommendedPriceModel, isFreeMember, needGuideShop } = getters;
    const { recommendedBenefitNum, recommendedMax, recommendedMin, cardName } =
      recommendedPriceModel;
    const {
      isAllVipPriceAdvantage,
      maxVipSavedPrice,
      minVipSavedPrice,
      noneSku,
      price,
      oldPrice,
    } = state.originActivitySku || {};

    // 如果没有新字段，那么就用老的逻辑；
    if (recommendedBenefitNum === undefined) {
      return {
        mainText: `会员价: ￥${getters.recommendPrice}`,
        actionText: '成为会员',
      };
    }

    const hasFreeGuideFeature = isFreeMember;
    const priceAdvantagePrefix = `${cardName}原价再减`;
    const priceAdvantageActionTxt = hasFreeGuideFeature
      ? '一键注册'
      : '立即省钱';
    const benefitAdvantageActionTxt = hasFreeGuideFeature
      ? '一键注册'
      : '立即尊享';

    // 如果是单Sku的话，价格比现在的价格便宜；对比推荐价和sku上的价格
    const singleSkuPriceAdvantage =
      noneSku && recommendedMin === recommendedMax && recommendedMin < price;

    if (singleSkuPriceAdvantage) {
      return {
        mainText: `${priceAdvantagePrefix}￥${numberToPrice(
          oldPrice - recommendedMin
        )}`,
        actionText: priceAdvantageActionTxt,
      };
    }

    // 多sku情况，是不是有价格优势，需要所有sku都有优势
    if (isAllVipPriceAdvantage) {
      if (maxVipSavedPrice === minVipSavedPrice) {
        return {
          mainText: `${priceAdvantagePrefix}￥${numberToPrice(
            maxVipSavedPrice
          )}`,
          actionText: priceAdvantageActionTxt,
        };
      }

      return {
        mainText: `${priceAdvantagePrefix}￥${numberToPrice(
          minVipSavedPrice
        )} - ${numberToPrice(maxVipSavedPrice)}`,
        actionText: priceAdvantageActionTxt,
      };
    }

    // 0项权益
    if (+recommendedBenefitNum === 0) {
      return {
        mainText: `${cardName || '会员'}尊享专属权益`,
        actionText: benefitAdvantageActionTxt,
      };
    }

    return {
      mainText: `${cardName || '会员'}可享${
        recommendedBenefitNum || '几'
      }大权益`,
      actionText: benefitAdvantageActionTxt,
    };
  },

  recommendPrice(state, getters) {
    const { recommendedMax: max, recommendedMin: min } =
      getters.recommendedPriceModel;

    const minPrice = (min / 100).toFixed(2);
    const maxPrice = (max / 100).toFixed(2);
    if (min === max) {
      return minPrice;
    }

    return `${minPrice} - ${maxPrice}`;
  },

  showCustomerDiscount(state, getters) {
    // 总店预览隐藏
    if (state.shop.isHqShopPreview) {
      return false;
    }

    if (
      getters.recommendedPriceModel.recommendedStatus &&
      getters.customerDiscountText
    ) {
      return true;
    }

    return false;
  },

  isWxPromise(state) {
    return state.shopConfig.weixinTransactionSecured === '1';
  },

  // 企微加好友优惠内容
  wecomFansBenefit(state) {
    const wecomFansBenefit = get(state, 'orderActivity.wecomFansBenefit', null);

    if (!wecomFansBenefit) return;
    return {
      ...handleBenefitInfo(wecomFansBenefit),
      contactInfo: wecomFansBenefit.contactWay,
    };
  },

  // 公众号涨粉优惠内容
  fansBenefit(state) {
    const fansBenefit = get(state, 'orderActivity.fansBenefit', null);

    if (!fansBenefit) return;

    return handleBenefitInfo(fansBenefit);
  },

  // 涨粉关注组件传递参数
  followExtraData(state) {
    const fansBenefit = get(state, 'orderActivity.fansBenefit', {});
    const { activityAlias: activityKey, preferentialType } = fansBenefit;
    const bizSubCode = preferentialType === 2 ? 0 : 1;
    const feature = args.getAll(state.goods.path) || {};

    return {
      bizCode: 3,
      bizSubCode,
      activityKey,
      feature,
    };
  },

  isShowBrandTitle(state) {
    const authorizationMark = get(
      state,
      'goods.brandInfoModel.authorizationMark',
      0
    );
    return authorizationMark;
  },

  // 有赞担保更多描述文案
  guaranteeDesc(state) {
    const GUARANTEE_STYLE = state.businessConfigs.guaranteeStyle || {};
    const { guarantee = {} } = state;
    if (
      guarantee.on &&
      guarantee.style !== GUARANTEE_STYLE.WHITE &&
      guarantee.style !== GUARANTEE_STYLE.GREEN
    ) {
      return [...(guarantee.desc || [])]
        .sort((a, b) => a.sort - b.sort)
        .slice(0, 5)
        .map((it) => it.desc)
        .join(' · ');
    }
    return '';
  },

  isFreeMember(state, getters) {
    const { recommendedPriceModel } = getters;
    const { cardType, cardSubType } = recommendedPriceModel;
    return cardType === 2 && cardSubType === 1001;
  },
  isLimitedSeckill() {
    return false;
  },
};
