import { GoodsComponent } from '@goods/common/vanx-component';
import { mapMutations, mapState, mapActions } from '@youzan/vanx';

const app = getApp();

GoodsComponent({
  name: 'BaseCommon',

  state: ['isFastBuy', 'firstPageRendered', 'goods', 'shop'],

  mapData: {
    ...mapState({
      goodsTitle: (state) => state.goods.title || '',
      goods: (state) => state.goods,
    }),
  },

  getters: [
    'showActivityBanner',
    'showGuaranteeBar',
    'isPointsExchange',
    'baseInfoBarTag',
    'isTimelimitedDiscountNotStart',
    'timelimitTimeDesc',
    'isHelpDepositPresale',
    'presaleTimeDesc',
    'isWxPromise',
    'showTopGoodsPromotion',
    'showPriceBlock',
    'showPriceBanner',
  ],

  methods: {
    ...mapMutations(['setGuaranteeOn']),
    ...mapActions(['initPayChannels']),
    guaranteeOnShow(val) {
      app.logger &&
        app.logger.log({
          et: 'view',
          ei: 'enterpage_goods_detail',
          en: '有赞担保曝光',
          params: {
            guarantee_on: val?.detail,
          },
          si: app.getKdtId(),
        });
      this.setGuaranteeOn(val);
      this.initPayChannels();
    },
  },
});
