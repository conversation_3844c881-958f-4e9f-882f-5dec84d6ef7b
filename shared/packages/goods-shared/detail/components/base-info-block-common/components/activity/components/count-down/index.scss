@import '~mixins/index.scss';

.activity-count-down {
  position: absolute;
  right: 0;
  top: 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  font-size: 12px;
  height: 44px;
  margin-right: 10px;

  &.limited-kill-background {
    position: relative;
    right: 0;
    top: -1px;
    width: 132px;
    height: 57px;
    margin-right: 0;
    background: url('https://img01.yzcdn.cn/upload_files/2021/05/11/FgPvAcpo9rM6wn1PgHPmFqUoWjKB.png')
      no-repeat;
    background-size: cover;
  }

  .flex-column {
    display: flex;
    flex-direction: column;
  }

  &__label {
    color: $white;
    text-align: right;
    font-size: 12px;
    transform: scale(1);
    font-family: Consolas, Monaco, monospace;

    &.activity-end {
      font-size: 18px;
      line-height: 36px;
    }
  }

  &__time {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 3px;
    color: $white;
    font-size: 12px;
    transform: scale(1);
  }

  &__process {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: $white;
    font-size: 12px;
    transform: scale(1);
    margin-top: 8px;
  }

  &__time-text {
    font-weight: bold;
    margin: 0 2px;
    white-space: nowrap;
  }

  &__time-text-limited-seckill {
    margin: 0;
    font-weight: normal;
  }

  &__time-box {
    width: 22px;
    height: 22px;
    line-height: 22px;
    border-radius: 0.2rem;
    text-align: center;
    font-size: 13px;
    font-weight: bold;
    transform: scale(0.8);
    background-color: $white;
  }

  &__time-progress {
    width: 64px;
    height: 6px;
  }

  .day-text {
    margin-right: 5px;
    font-weight: normal;
  }

  .progress-text {
    margin-left: 3px;
    margin-right: 0;
    font-weight: normal;
  }
}
