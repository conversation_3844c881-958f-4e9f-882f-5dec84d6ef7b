import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'ActivityPrice',

  getters: [
    'isDepositPresale',
    'isAuction',
    'activityPrice',
    'activityPreferentialPrefix',
    'isLimitedSeckill',
  ],

  mapData: mapState({
    activityOriginPrice(state, getters) {
      let { originPrice } = getters.activityPrice;

      if (originPrice.indexOf('-') !== -1) {
        originPrice = originPrice.split('-')[0] + ' 起';
      }
      return originPrice;
    },
  }),
});
