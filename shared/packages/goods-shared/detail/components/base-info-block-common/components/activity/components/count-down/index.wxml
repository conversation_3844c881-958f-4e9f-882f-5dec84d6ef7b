<view wx:if="{{ countdown }}" class=" activity-count-down {{ isLimitedSeckill ? 'limited-kill-background' : '' }}">
  <view wx:if="{{ !showProgress }}" class="flex-column">
    <theme-view color="{{ isLimitedSeckill ? 'general' : '' }}" class="activity-count-down__label {{ ended ? 'activity-end' : '' }}" style="{{ isLimitedSeckill ? ' margin-top: 7px;margin-right:10px; ': ''}}">
      {{ remainDesc }}
    </theme-view>
    <view wx:if="{{ !ended }}" class="activity-count-down__time" style="{{ isLimitedSeckill ? 'margin-right:10px; ': ''}}">
      <theme-view 
        wx:if="{{ remainObj.day }}"
        color="{{ isLimitedSeckill ? 'general' : '' }}"
        class="activity-count-down__time-text day-text"
      >{{ remainObj.day }}天</theme-view >
      <theme-view wx:if="{{ !( isLimitedSeckill &&  !hour ) }}" class="activity-count-down__time-box" color="{{ isLimitedSeckill ? '' : 'general' }}" bg="{{ isLimitedSeckill ? 'main-bg' : '' }}" innerStyle="{{ isLimitedSeckill ? 'border-radius: 2px' : '' }}">
        {{ remainObj.hour }}
      </theme-view>
       <theme-view wx:if="{{ !( isLimitedSeckill &&  !hour ) }}" class="{{ isLimitedSeckill ?  'activity-count-down__time-text activity-count-down__time-text-limited-seckill' : 'activity-count-down__time-text'}}" color="{{ isLimitedSeckill ? 'general' : '' }}"> <text >:</text> </theme-view>
      <theme-view class="activity-count-down__time-box" color="{{ isLimitedSeckill ? '' : 'general' }}" bg="{{ isLimitedSeckill ? 'main-bg' : '' }}" innerStyle="{{ isLimitedSeckill ? 'border-radius: 2px' : '' }}">
        {{ remainObj.minute }}
      </theme-view>
       <theme-view class="{{ isLimitedSeckill ?  'activity-count-down__time-text activity-count-down__time-text-limited-seckill' : 'activity-count-down__time-text'}}" color="{{ isLimitedSeckill ? 'general' : '' }}" color="{{ isLimitedSeckill ? 'general' : '' }}"> <text >:</text> </theme-view>
      <theme-view class="activity-count-down__time-box"  color="{{ isLimitedSeckill ? '' : 'general' }}" bg="{{ isLimitedSeckill ? 'main-bg' : '' }}" innerStyle="{{ isLimitedSeckill ? 'border-radius: 2px' : '' }}">
        {{ remainObj.second }}
      </theme-view>
      <theme-view wx:if="{{ (!remainObj.day && !isLimitedSeckill) || ( isLimitedSeckill && !hour ) }}" class="activity-count-down__time-text" color="{{ isLimitedSeckill ? 'general' : '' }}"> <text >:</text> </theme-view>
      <theme-view wx:if="{{ (!remainObj.day && !isLimitedSeckill) || ( isLimitedSeckill && !hour ) }}" class="activity-count-down__time-box" color="{{ isLimitedSeckill ? '' : 'general' }}" bg="{{ isLimitedSeckill ? 'main-bg' : '' }}" innerStyle="{{ isLimitedSeckill ? 'border-radius: 2px' : '' }}">
        {{ remainObj.milliseconds }}
      </theme-view>
    </view>
  </view>
  <view wx:elif="{{ !ended }}" class="flex-column">
    <view class="activity-count-down__label">
      <text>{{ remainDesc }} </text>
      <text wx:if="{{ remainObj.day }}" class="day-text">{{ remainObj.day }}天 </text>
      <text>{{ remainObj.hour + ':' + remainObj.minute + ':' + remainObj.second }}</text>
      <text wx:if="{{ !remainObj.day }}">:{{ remainObj.milliseconds }}</text>
    </view>
    <view class="activity-count-down__process">
      <van-progress
        wx:if="{{ !countdown.hideProgressBar }}"
        percentage="{{ countdown.progressNum }}"
        show-pivot="{{ false }}"
        color="#fff"
        class="activity-count-down__time-progress"
      />
      <text
        style="margin-left: 8px;"
        class="activity-count-down__time-text progress-text"
      >{{ countdown.progressDesc }}</text>
    </view>
  </view>
  <view
    wx:else
    class="activity-count-down__label flex-column {{ ended ? 'activity-end' : '' }}"
  >{{ remainDesc }}</view>
</view>