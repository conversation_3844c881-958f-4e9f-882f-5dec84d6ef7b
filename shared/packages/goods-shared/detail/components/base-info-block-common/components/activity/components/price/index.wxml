<view class="activity-price">
  <view class="activity-price__label">
    <view class="activity-price__label-up">
      <!-- 活动只显示最低价 -->
      <text class="activity-price__label-sm price-length-{{ activityPrice.showPrice[0].length }}">
        <!-- <text wx:if="{{ isDepositPresale }}">定金</text> -->
        <!-- 视频号 限时秒杀 -->
        <text wx:if="{{ isLimitedSeckill }}" class="activity-price__label-xs">秒杀价</text>
        <text>￥</text>
      </text>
      <text
        class="activity-price__label-xl price-length-{{ activityPrice.showPrice[0].length }}"
      >{{ activityPrice.showPrice[0] }}</text>
      <text
        wx:if="{{ activityPrice.showPrice[1] }}"
        class="activity-price__label-sm price-length-{{ activityPrice.showPrice[0].length }}"
      >.{{ activityPrice.showPrice[1] }}</text>
      <text wx:if="{{ activityPrice.isRange }}" class="activity-price__label-xs">起</text>

      <coupon-tag
        wx:if="{{ activityPrice.preferentialPriceTag }}"
        class="activity-price__coupon-tag"
        color-style="white"
        price-tag="{{ activityPrice.preferentialPriceTag }}"
        prefix="{{ activityPreferentialPrefix }}"
        size="{{ activityPrice.showPrice[0].length >= 6 ? 'small' : '' }}"
      />
    
      <theme-view
        wx:elif="{{ activityPrice.priceTag && !isLimitedSeckill }}"
        color="general"
        class="activity-price__more-tag"
      >
        {{ activityPrice.priceTag }}
      </theme-view>
    </view>
    <view wx:if="{{ isDepositPresale }}" class="activity-price__presale-more">
      <text>定金 </text>
      <text class="origin-price">￥{{ activityOriginPrice }}</text>
      <text
        wx:if="{{ activityPrice.presaleOffsetPriceRange }}"
        class="deduction"
      >{{ activityPrice.presaleOffsetPriceRange }}</text>
    </view>
    <view wx:elif="{{ activityPrice.originPrice }}" class="{{ isLimitedSeckill ? 'activity-price__more-price activity-price__more-price-limited-seckill' :'activity-price__more-price' }}" >
      <text wx:if="{{ isAuction }}">起拍价</text>
      <text wx:else>价格</text>
      <text class="origin-price">￥{{ activityOriginPrice }}</text>
    </view>
  </view>
</view>
