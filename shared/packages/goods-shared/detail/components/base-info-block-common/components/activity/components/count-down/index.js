import { GoodsComponent } from '@goods/common/vanx-component';
import { mapGetters, mapState } from '@youzan/vanx';
import { format } from '@youzan/weapp-utils/lib/time';
import { timerBehaivor } from '@goods/common/behaviors';

const DAY = 24 * 3600 * 1000;

GoodsComponent({
  name: 'ActivityCountDown',
  behaviors: [timerBehaivor],

  getters: ['countdown', 'activityNotStart', 'isLimitedSeckill'],

  mapData: mapState({
    hasCountDown(state, getters) {
      return !!getters.countdown;
    },

    showProgress(state, getters) {
      return getters.countdown && getters.countdown.showProgress;
    }
  }),

  data: {
    // 倒计时计数，用于确定是否已开始就已经结束了
    remainDesc: '',
    remainObj: {},
    ended: false,
    hour: 0, // 用于限时秒杀，判断显示时分秒 还是 分 秒 毫秒；
  },

  computed: mapGetters(['countdown']),

  watch: {
    countdown(val) {
      if (val) {
        this.timeRunner();
      }
    }
  },

  created() {
    this.count = 0;

    this.tid = null;
  },

  ready() {
    if (this.countdown && !this.hasDetached) {
      this.timeRunner();
    }
  },

  methods: {
    timeRunner() {
      clearTimeout(this.tid);
      const { activityNotStart } = this;
      const { end } = this.countdown;
      const now = Date.now();
      const remain = end - now;
      // 交互逻辑, 未开始是“仅剩” 不是”还剩“
      let remainDesc = '';
      if (remain > DAY && !activityNotStart) {
        remainDesc = this.countdown.desc.replace('仅剩', '还剩');
      } else {
        remainDesc = this.countdown.desc;
      }
      // 倒计时结束
      if (remain <= 0 && this.count > 0) {
        this.count = 0;
        this.$dispatch('countdownEnded');
        return;
      }
      if (remain <= 0) {
        this.setYZData({
          remainDesc,
          ended: true
        });
        return;
      }
      this.count += 1;

      const remainObj = format(remain).strData;
      remainObj.day = +remainObj.day;
      const hour = +remainObj.hour;
      // TODO 这个hundredMilliseconds有点问题
      remainObj.milliseconds = `0${remainObj.hundredMilliseconds}`;

      this.setYZData({
        remainDesc,
        remainObj,
        hour
      });

      let delta = 500;
      if (!remainObj.day) {
        delta = 50;
      }
      this.tid = setTimeout(this.timeRunner.bind(this), delta);
    }
  },

  detached() {
    this.hasDetached = true; // 这个存在比ready先执行的情况
    clearInterval(this.tid);
    this.tid = null;
  }
});
