@import '~mixins/index.scss';

.activity-price {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;

  &__coupon-tag {
    position: relative;
    top: -4px;
    margin-left: 8px;
  }

  &__label {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    height: 56px;
    box-sizing: border-box;
    padding: 0 8px 0 0;
    color: $white;
  }

  &__label-up {
    display: flex;
    align-items: baseline;
    margin-top: 6px;
    margin-left: -3px;
  }

  &__label-xs {
    font-size: 12px;
    margin-left: 2px;
    line-height: 20px;
    vertical-align: bottom;
    font-weight: bold;
  }

  &__label-sm {
    font-size: 18px;
    line-height: 24px;
    vertical-align: bottom;
    font-weight: bold;
  }

  &__label-xl {
    font-size: 30px;
    font-size: 24px;
    line-height: 1;
    vertical-align: bottom;
    font-weight: bold;
  }

  &__more {
    margin-left: 2px;
  }

  &__more-tag {
    display: inline-block;
    padding: 0px 4px 0;
    border-radius: 1rem;
    font-size: 10px;
    background-color: $white;
    height: 16px;
    line-height: 16px;
    box-sizing: border-box;
    position: relative;
    top: -4px;
    margin-left: 8px;
  }

  &__more-price {
    font-family: Avenir-Book;
    font-size: 10px;
    color: $white;
    opacity: 0.6;
    white-space: nowrap;
    position: relative;
    top: -4px;

    .origin-price {
      text-decoration: line-through;
    }
  }

  &__more-price-limited-seckill {
    font-size: 12px;
  }
  
  &__presale-more {
    font-size: 12px;
    color: $white;
    font-weight: bold;
    position: relative;
    top: -4px;

    .deduction {
      margin-left: 3px;
    }
  }
}

.price-length-6,
.price-length-7 {
  &.activity-price__label {
    &-xl {
      font-size: 20px;
    }

    &-sm {
      font-size: 15px;
    }
  }
}

.price-length-1,
.price-length-2,
.price-length-3,
.price-length-4,
.price-length-5 {
  &.activity-price__label {
    &-xl {
      font-size: 28px;
    }

    &-sm {
      font-size: 18px;
    }
  }
}

