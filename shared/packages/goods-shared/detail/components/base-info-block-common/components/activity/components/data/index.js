import { get } from '@goods/utils';
import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'activity-data',

  mapData: {
    ...mapState({
      showProductLaunchData(state, getters) {
        const value = get(
          state,
          'marketActivity.productLaunch.reservationNum',
          0
        );
        return (
          getters.isProductLaunch && state.goodsActivity.waitToSold && value > 0
        );
      },

      reservationNum(state) {
        const value = get(state, 'marketActivity.productLaunch.reservationNum');
        if (value <= 999) {
          return value;
        }

        if (value <= 9999) {
          return '999+';
        }

        return `${Math.floor(value / 10000)}w+`;
      },
    }),
  },
});
