import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';
import { setLogger } from '@goods/common/logger';

GoodsComponent({
  name: 'G<PERSON>ranteeB<PERSON>',

  state: ['guarantee', 'goods', 'shop'],

  getters: ['isWxPromise', 'guaranteeDesc'],

  data: {
    showPopup: false,
  },

  mapData: mapState({
    barClass(state) {
      return `guarantee-bar--${state.guarantee.style}`;
    },

    // state.guarantee.style === GUARANTEE_STYLE.GREEN 具有绿色背景色，图片和文字白色
    logo(state, getters) {
      const GUARANTEE_STYLE = state.businessConfigs.guaranteeStyle || {};
      if (getters.isWxPromise) {
        return state.guarantee.style === GUARANTEE_STYLE.GREEN
          ? 'https://img01.yzcdn.cn/upload_files/2020/03/27/FigFK9eFGGD5UMhCMtu1MS_oWj44.png'
          : 'https://img01.yzcdn.cn/upload_files/2020/03/27/Fm3w1kROOBNzbsrrmcCzU-XG9l4L.png';
      }

      return state.guarantee.style === GUARANTEE_STYLE.GREEN
        ? 'https://img01.yzcdn.cn/upload_files/2020/03/25/FoW-bKRELspGkOnRR2Zd5GGssM4Y.png'
        : 'https://img01.yzcdn.cn/upload_files/2020/03/25/FkigwukqJQ5GR-bf1YxV-kA8O8t-.png';
    },

    info(state, getters) {
      if (getters.guaranteeDesc) {
        return '';
      }
      return getters.isWxPromise
        ? '双重担保，请放心购买'
        : '已担保，放心买，售后有保障';
    },
  }),

  methods: {
    showGuaranteeIntro() {
      setLogger({
        et: 'click', // 事件类型
        ei: 'click_gurantee', // 事件标识
        en: '点击有赞担保', // 事件名称
      });

      this.setYZData({
        showPopup: true,
      });
    },
    closeGuaranteeIntro() {
      this.setYZData({
        showPopup: false,
      });
    },
  },
});
