<view
  wx:if="{{ showCustomerDiscount && isMobileAuthorized }}"
  class="customer-card__wrapper"
  bind:tap="handleNavigate"
>
  <view class="customer-card">
    <view class="customer-card__content">
      {{ customerDiscountText.mainText }}
    </view>
    <view class="customer-card__action">
      {{ customerDiscountText.actionText }}
      <van-icon name="arrow" />
    </view>
  </view>
</view>

<user-authorize
  wx:if="{{ showCustomerDiscount && !isMobileAuthorized }}"
  bind:next="handleAuthorize"
  authTypeList="{{ ['mobile']  }}"
>
  <view class="customer-card">
    <view class="customer-card__content">
      {{ customerDiscountText.mainText }}
    </view>
    <view class="customer-card__action">
      {{ customerDiscountText.actionText }}
      <van-icon name="arrow" />
    </view>
  </view>
</user-authorize>
