.customer-card {
  display: flex;
  align-items: flex-start;
  line-height: 18px;
  margin: 4px 0 0;
  padding: 9px 10px;
  border-radius: 4px;
  background-image: linear-gradient(
    134deg,
    #f9e1aa 0%,
    #ffe8b4 52%,
    #e1c78e 100%
  );
  color: #6c3805;
  font-size: 10px;
  text-align: left;

  &::before {
    display: block;
    content: '';
    width: 32px;
    height: 18px;
    margin-right: 8px;
    background: url('https://img01.yzcdn.cn/public_files/90f4f54062afa961fd69d58124d1226e.png')
      no-repeat;
    background-size: cover;
  }

  &__wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
  }

  &__content {
    flex: 1;
    padding-right: 21px;
    line-height: 18px;
    font-size: 13px;
  }

  &__action {
    display: flex;
    align-items: center;
    font-size: 13px;
    line-height: 18px;
  }

  &__price {
    font-size: 14px;
  }
}
