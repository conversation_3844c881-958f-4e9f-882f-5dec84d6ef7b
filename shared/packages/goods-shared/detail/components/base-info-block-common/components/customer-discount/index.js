import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState, mapGetters, mapActions } from '@youzan/vanx';
import { setLogger } from '@goods/common/logger';
import openWebView from 'shared/utils/open-web-view';
import { jumpLink } from '@goods/utils';
import getAuthorizedState from 'shared/utils/get-authorized-state';
import args from '@youzan/weapp-utils/lib/args';

GoodsComponent({
  name: 'CustomerDiscount',

  state: ['shop'],

  getters: [
    'recommendedPriceModel',
    'customerDiscountText',
    'showCustomerDiscount',
  ],

  computed: {
    ...mapState(['shop']),
    ...mapGetters(['recommendedPriceModel']),
  },

  data: {
    isMobileAuthorized: false,
  },

  methods: {
    ...mapActions(['reload']),

    handleNavigate() {
      setLogger({
        et: 'click',
        ei: 'click_lead_membercenter',
        en: '商详页_引导办卡/办会员点击',
        params: {},
      });

      let url = this.recommendedPriceModel?.cardUrl || '';
      const currentPage = getCurrentPages();
      const { route, options } = currentPage[currentPage.length - 1];
      const redirectUrl = args.add(`/${route}`, options);
      const src = decodeURIComponent(args.getAll(url)?.src || '');
      const query = {
        guideType: 'goods',
        redirectUrl,
      };
      // 降级处理：对老 url 做兼容处理，不带 query
      if (url.indexOf('packages/levelcenter/plus/index') > -1) {
        jumpLink(url);
        return;
      }

      if (!/^\/packages|pages/.test(url) || src) {
        // 后端返回的是：'/pages/common/webview-page/index?src=https%3A%2F%2Fcashier.youzan.com%2Fpay%2Fwscuser_paylevel'
        if (src) {
          url = src;
        }
        openWebView(args.add(url, query, true));
      } else {
        jumpLink(args.add(url, query, true));
      }
    },
    handleAuthorize() {
      // 授权后更新授权信息
      this.setYZData({
        isMobileAuthorized: true
      });
      this.reload();
    },
  },

  created() {
    getAuthorizedState().then((authorizedState) => {
      this.setYZData({
        isMobileAuthorized: !!authorizedState?.mobile
      });
    });
  }
});
