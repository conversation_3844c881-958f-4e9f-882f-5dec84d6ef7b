<view class="goods-price">
  <theme-view
    color="general"
    class="goods-price__current"
    wx:if="{{ isDepositPresale || goods.price }}"
  >
    <view wx:if="{{ isDepositPresale }}" class="goods-price__current-bar">
      <text class="goods-price__current-label two-space">定金</text>
      <text class="goods-price__current-label">¥</text>
      <text class="goods-price__current-price">{{ depositPrice }}</text>
    </view>
    <view wx:else class="goods-price__current-bar">
      <text class="goods-price__current-label">¥</text>
      <text class="goods-price__current-price">{{ goods.price }}</text>
    </view>
  </theme-view>

  <!-- 券后价标签展示 -->
  <coupon-tag wx:if="preferentialPriceTag" price-tag="{{ preferentialPriceTag }}" prefix="{{ activityPreferentialPrefix }}"/>

  <!-- 商品价格标签 -->
  <block
    wx:for="{{ goodsPriceTags }}"
    wx:for-item="tag"
    wx:key="tag"
  >
    <common-tag
      wx:if="{{ !hideOriginPriceTag }}"
      class="goods-price__tag"
    >
      {{ tag }}
    </common-tag>
  </block>
</view>
