import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'GoodsPrice',

  state: ['goods'],

  getters: [
    'goodsPriceTag',
    'isDepositPresale',
    'depositPrice',
    'preferentialPriceTag',
    'isVipTag',
    'activityPreferentialPrefix',
    'hideOriginPriceTag'
  ],

  mapData: mapState({
    goodsPriceTags(state, getters) {
      const tagList = [];
      const { isVipTag, goodsPriceTag } = getters;
      if (isVipTag) {
        tagList.push('会员价');
      } else if (goodsPriceTag) {
        tagList.push(goodsPriceTag);
      }
      return tagList;
    }
  })
});
