@import '~mixins/index.scss';

.goods-price {
  position: relative;
  text-align: left;
  margin: 2px 0 8px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  &__tag {
    margin: 6px 8px 6px 0;
  }

  &__tag-vip {
    display: inline-block;
    width: 50px;
    height: 16px;
    background: url(https://img01.yzcdn.cn/public_files/f2a7689f3fe84adb04aa43c68b7e9d2d.png)
      no-repeat;
    background-size: cover;
    vertical-align: middle;
    position: relative;
    top: -1px;
  }

  &__current {
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
    font-size: 16px;
    color: $red;
  }

  &__current-label {
    display: inline-block;
    font-size: 16px;
    line-height: 22px;
    margin-right: 2px;
    font-weight: bolder;
    vertical-align: bottom;
  }

  .two-space {
    margin-right: 0.5em;
  }

  &__current-price {
    display: inline-block;
    vertical-align: middle;
    font-size: 22px;
    line-height: 24px;
    font-weight: bolder;
  }

  &__current-bar {
    display: flex;
    align-items: baseline;
  }
}
