@import '~mixins/index.scss';

.goods-title {
  padding: 0;
  margin-top: 8px;

  &__tags {
    margin-bottom: 8px;

    &:empty {
      margin-bottom: 0;
    }
  }

  &__tag-label {
    display: inline-block;
    width: 30px;
    height: 16px;
    vertical-align: middle;
  }

  &__tag-desc {
    display: inline-block;
    margin-left: 6px;
    font-size: 12px;
    line-height: 20px;
    color: $gray-dark;
    vertical-align: middle;
  }

  &__retail-logo {
    background-color: $blue !important;
    color: $white;
    text-align: center;
  }

  &__box {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  &__main {
    display: block;
    margin-right: 22px;
    font-size: 16px;
    line-height: 20px;
    font-weight: 500;
    text-align: left;
    word-break: break-all;
    word-wrap: break-word;

    &-icon {
      display: inline-block;
      width: 30px;
      height: 16px;
      margin-right: 6px;
      background-image: url('https://img01.yzcdn.cn/public_files/39924d40a8fc9bd0e94ffa646fec1793.png');
      background-size: 100% auto;
      background-repeat: no-repeat;
      vertical-align: middle;
    }

    span {
      vertical-align: middle;
      font-size: 16px;
    }
  }

  &__sub {
    margin: 8px 0;
    color: $gray-dark;
    font-size: 13px;
    padding-right: 46px;
  }

  &__more {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: $gray-dark;
  }

  &__more--highlight {
    color: #de373e;
  }

  &__more-item-word {
    font-size: 12px;
    white-space: nowrap;
    line-height: 16px;
  }

  &__more-icon {
    animation: zoom 0.1s linear 0s 0 normal, shake 0.2s linear 0.1s 5 alternate;
  }

  .enjoy-container {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .enjoy-icon {
    display: inline-block;
    border-radius: 8px;
    line-height: 18px;
    padding: 0 4px;
    font-size: 12px;
    color: var(--tag-text, #323233);
    background: var(--tag-bg, #ffffff);
  }
  
  .enjoy-desc {
    margin-left: 6px;
    font-size: 12px;
    line-height: 20px;
    color: #969799;
  }
}

@keyframes zoom {
  0% {
    transform: scale(0);
  }
  100% {
    transform: translateZ(0) scale(1.43);
  }
}

@keyframes shake {
  0% {
    transform: translateZ(0) scale(1.43) rotate(-15deg);
  }
  100% {
    transform: translateZ(0) scale(1.43) rotate(15deg);
  }
}
