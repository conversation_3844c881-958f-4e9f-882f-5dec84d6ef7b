import { GoodsComponent } from '@goods/common/vanx-component';
import { checkIsChannels } from 'shared/utils/channel';
import { mapState, mapActions } from '@youzan/vanx';
import { setLogger } from '@goods/common/logger';

GoodsComponent({
  state: ['goods', 'retail', 'outerActivities'],

  getters: [
    'goodsSubTitle',
    'showShareIcon',
    'goodsTag',
    'goodsRetailTag',
    'isPeriodbuy',
    'goodsHaitaoCountry',
    'isShowBrandTitle',
  ],

  data: {
    isChannels: checkIsChannels(),
  },

  mapData: mapState({
    hasShareBenefitActivity(_, getters) {
      const { activityId } = getters.shareBenefitActivity || {};
      if (activityId) {
        setLogger({
          et: 'custom', // 事件类型
          ei: 'view_share_activity', // 事件标识
          en: '分享有礼活动曝光', // 事件名称
          params: {
            activityId,
          },
        });
        return true;
      }
      return false;
    },
  }),

  methods: {
    ...mapActions(['handleShareClick']),
  },
});
