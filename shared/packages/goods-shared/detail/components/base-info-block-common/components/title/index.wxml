<view
  wx:if="{{ goods.title }}"
  class="goods-title"
>
  <view class="goods-title__tags">
    <!-- 海淘标识 -->
    <view wx:if="{{ goodsTag }}">
      <image
        src="{{ goodsTag }}"
        class="goods-title__tag-label"
        style="width: {{ goods.isHaitao ? 30 : 42 }}px"
      />
      <text wx:if="{{ goods.isHaitao }}" class="goods-title__tag-desc">
        <text wx:if="{{ goodsHaitaoCountry }}">{{ goodsHaitaoCountry }}品牌，</text>
        跨境商品
      </text>
      <text wx:if="{{ isPeriodbuy }}" class="goods-title__tag-desc">
        按周期分期配送商品
      </text>
    </view>
    <!-- 零售门店标识 -->
    <view wx:if="{{ retail.show }}">
      <image
        src="{{ goodsRetailTag }}"
        class="goods-title__tag-label"
      />
      <text class="goods-title__tag-desc">线下同款，门店有售</text>
    </view>
    <!-- 随心订 -->
     <view  class="enjoy-container" wx:if="{{ outerActivities&&outerActivities.length}}">
      <view class="enjoy-icon">随心订</view>
      <text class="enjoy-desc">随心订购,按期配送</text>
    </view>
  </view>
  <view class="goods-title__box">
    <view class="goods-title__main">
      <view class="goods-title__main-icon" wx:if="{{ isShowBrandTitle }}"></view>
      {{ goods.title }}
    </view>

    <!-- 分享按钮 -->
    <view
      wx:if="{{ showShareIcon && !isChannels }}"
      class="goods-title__more {{ hasShareBenefitActivity && 'goods-title__more--highlight' }}"
      bind:tap="handleShareClick"
    >
      <van-icon
        wx:if="{{ hasShareBenefitActivity }}"
        color="#DE373E"
        size="20px"
        name="point-gift"
        class="goods-title__more-icon"
      />
      <van-icon
        wx:else
        color="#646566"
        size="16px"
        name="https://img01.yzcdn.cn/public_files/84f6041a4db8761dadb08a4e58efd0ea.png"
      />
      <view class="goods-title__more-item-word">分享</view>
    </view>
  </view>
  <view
    wx:if="{{ goodsSubTitle }}"
    class="goods-title__sub"
  >
    {{ goodsSubTitle }}
  </view>
</view>
