import { GoodsComponent } from '@goods/common/vanx-component';
import {
  batchBudgetCommission,
  getSalesmanShareInfo,
} from 'shared/utils/salesman-share';
import formatPrice from '@youzan/utils/money/formatPrice';
import mkc from '@youzan/weapp-utils/lib/map-keys-case';

GoodsComponent({
  name: 'GuidePromote',

  data: {
    profit: 0,
  },

  properties: {
    /**
     * 商品列表数据
     */
    goods: {
      type: Object,
      value: {},
      observer(newVal) {
        if (newVal) {
          const { id, minPrice = 0 } = newVal;
          const commissionParam = {
            budgetGoods: [
              {
                goodsId: id,
                minPrice,
              },
            ],
          };

          getSalesmanShareInfo({
            type: 'NORMAL',
            goodsId: id,
          }).then((data) => {
            const shareData = mkc.toCamelCase(data);
            const isShoppingGuide = shareData.salesmanType === 1;

            if (isShoppingGuide) {
              batchBudgetCommission(commissionParam).then((data) => {
                this.profit = data && formatPrice(data);
              });
            }
          });
        }
      },
    },
  },
});
