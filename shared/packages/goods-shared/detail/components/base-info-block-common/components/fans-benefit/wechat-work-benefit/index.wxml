<view class="wecom-fans-benefit">
  <!-- 关注前 -->
  <view wx:if="{{ !wecomFansBenefit.isFans }}" class="wecom-fans-benefit__bar">
    <view class="wecom-fans-benefit__content">
      <image
        class="wecom-fans-benefit__icon"
        alt="店铺好友"
        src="https://b.yzcdn.cn/public_files/d017080ba178766df62df5210bdd1cf1.png"
      />
      <text>{{ wecomFansBenefit.contentNormal }}</text>
      <text class="wecom-fans-benefit__text--red">{{ wecomFansBenefit.contentHighLight }}</text>
    </view>
    <wechat-work-popup
      contact-info="{{ wecomFansBenefit.contactInfo }}"
      z-index="{{ 1000 }}"
      good-alias="{{ goods.alias }}"
      bind:show="clickLogger"
      bind:close="clickLogger"
    >
      <view class="wecom-fans-benefit__action">
        <text class="wecom-fans-benefit__action-text">加好友</text>
        <van-icon name="arrow" size="12px" style="display: inline-flex;" />
      </view>
    </wechat-work-popup>
  </view>

  <!-- 关注后 -->
  <view wx:else class="wecom-fans-benefit--followed">
    <view>你已添加好友，</view>
    <text>{{ wecomFansBenefit.contentNormal }}</text>
    <text class="wecom-fans-benefit__text--red">{{ wecomFansBenefit.contentHighLight }}</text>
  </view>
</view>
