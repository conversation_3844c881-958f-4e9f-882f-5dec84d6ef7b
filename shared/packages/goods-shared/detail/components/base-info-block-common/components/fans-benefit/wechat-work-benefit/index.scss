@import '~mixins/index.scss';

.wecom-fans-benefit {
  padding: 7px 0 6px;
  font-family: 'PingFang SC', Avenir;
  font-size: $text-color;

  &__bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
    padding: 0 7px 0 12px;
    box-sizing: border-box;
    background: #fbe7e7;
    font-size: 13px;
    border-radius: 4px;
  }

  &__content {
    display: flex;
    align-items: center;
    width: calc(100% - 81px);
  }

  &__icon {
    display: inline-block;
    width: 51px;
    height: 16px;
    margin-right: 8px;
  }

  &__text--red {
    flex: 1;
    color: #f01d35;
    @include ellipsis;
  }

  &__action {
    display: flex;
    align-items: center;
    color: #f01d35;
  }

  &__action-text {
    margin-right: 4px;
  }

  &--followed {
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 12px;
    box-sizing: border-box;
    background: #f7f8fa;
    font-size: 13px;
    color: #3a4554;
    border-radius: 4px;
    background-image: linear-gradient(
      134deg,
      #d3dae452 0%,
      #d7dee87a 48%,
      #a1adbb75 100%
    );
  }
}
