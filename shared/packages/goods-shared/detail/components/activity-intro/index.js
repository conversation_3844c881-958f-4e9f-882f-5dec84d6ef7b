import { GoodsComponent } from '@goods/common/vanx-component';

GoodsComponent({
  name: 'ActivityIntro',

  properties: {
    title: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    link: {
      type: String,
      default: ''
    },
    isLink: {
      type: Boolean,
      default: true
    }
  },

  options: {
    styleIsolation: 'shared'
  },

  methods: {
    handleClick(event) {
      if (this.link) {
        // jumpLink(this.link);
        return;
      }
      this.triggerEvent('click', event);
    }
  }
});
