@import '~mixins/index.scss';

.activity-intro {
  padding: 0 16px;
  background-color: $white;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    line-height: 1.5;
  }

  &__header-text {
    font-size: 15px;
  }

  &__header-action {
    color: $gray-dark;
    font-size: 14px;

    &--link {
      color: $blue;
    }
  }

  .van-icon-arrow {
    font-size: 16px !important;
    margin-right: -3px;
  }

  &__icon {
    display: inline;
    position: relative;
    bottom: -2px;
  }
}
