import { GoodsComponent } from '@goods/common/vanx-component';
import { isNewIphone } from 'shared/utils/browser/device-type';

const isIphoneX = isNewIphone();
const app = getApp();
const systemInfo = app.getSystemInfoSync();

GoodsComponent({
  name: 'CommonPop',

  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        val && !this.data.inited && this.setYZData({
          inited: true
        });
      }
    },
    button: {
      type: String,
      value: '',
    },

    customStyle: {
      type: String,
      value: '',
    },

    contentStyle: {
      type: String,
      value: '',
    },

    title: String,
    // TODO 弹窗嵌套,这啥设计
    nested: Boolean,
    closeIcon: {
      type: String,
      default: 'arrow-left'
    },
    styles: {
      type: Object,
      default: () => {
        return {};
      }
    },
  },

  data: {
    maxScrollHeight: 0,
    minScrollHeight: 0,
    maxNavHeight: systemInfo.screenHeight * 0.8,
    minNavHeight: systemInfo.screenHeight * 0.5,
  },

  ready() {
    const { maxNavHeight, minNavHeight } = this.data;

    this.setYZData({
      maxScrollHeight: this.getNavHeight(maxNavHeight),
      minScrollHeight: this.getNavHeight(minNavHeight),
    });
  },

  methods: {

    noop() {},

    getNavHeight(navHeight) {
      const BOTTOM_HEIGHT = 50;
      const TITLE_HEIGHT = 44;
      const IPHONEX_SAFE_HEIGHT = 34;
      const { title } = this.data;

      return navHeight - BOTTOM_HEIGHT - (title ? TITLE_HEIGHT : 0) - (isIphoneX ? IPHONEX_SAFE_HEIGHT : 0);
    },

    clickBtn() {
      this.triggerEvent('btn-click');
    },

    close() {
      this.triggerEvent('close');
    }
  }
});
