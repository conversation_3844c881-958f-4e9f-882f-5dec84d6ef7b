<van-popup
  wx:if="{{ inited }}"
  show="{{ show }}"
  safe-area-inset-bottom
  round
  custom-class="common-popup"
  position="bottom"
  bind:close="close"
  custom-style="z-index: 2003; {{ customStyle }};max-height: {{navHeight}}px;"
  closeable="{{ nested }}"
  bind:close-icon="closeIcon"
  bind:style="styles"
  close-icon-position="top-left"
>
  <view
    wx:if="{{ title }}"
    class="common-popup__title"
  >
    {{ title }}
  </view>
  <scroll-view
    scroll-y="{{ true }}"
    class="common-popup__content"
    style="max-height: {{ maxScrollHeight }}px; min-height: {{ minScrollHeight }}px; {{ contentStyle }}"
  >
    <slot />
  </scroll-view>
  <view wx:if="{{ !nested }}" class="common-popup__button-wrapper">
    <theme-view
      custom-class="common-popup__button"
      block
      bg="main-bg"
      color="main-text"
      bind:tap="clickBtn"
    >
      {{ button || '我知道了' }}
    </theme-view>
  </view>
</van-popup>
