<view
  class="danmaku {{ customClass }}"
  wx:if="{{ visible && bullets.length > 0 }}"
  style="top: {{ offsetTop }}px;"
>
  <danmaku-item
    wx:for="{{ bullets }}"
    wx:for-item="bullet"
    wx:key="key"
    text="{{ bullet.data[textProp] }}"
    avatar="{{ bullet.data[avatarProp] }}"
    top="{{ bullet.top }}"
    left="{{ bullet.left }}"
    offset-x="{{ bullet.offsetX }}"
    data-bullet="{{ bullet }}"
    max-text-length="{{ maxTextLength }}"
    bind:tap="handleBulletTap"
  />
</view>
