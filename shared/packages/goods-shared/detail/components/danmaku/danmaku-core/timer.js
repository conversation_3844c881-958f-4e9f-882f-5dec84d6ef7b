/* eslint-disable max-classes-per-file */

/**
 * 作为接口定义，无实现
 */
class Timer {
  add() {}

  clear() {}

  exists() {}
}

/**
 * 定时器：延时执行
 */
export class TimeoutTimer extends Timer {
  constructor(delay) {
    super();

    this.delay = delay;
    this.timerId = undefined;
  }

  add(callback) {
    clearTimeout(this.timerId);
    this.timerId = setTimeout(callback, this.delay);
  }

  clear() {
    clearTimeout(this.timerId);
    this.timerId = undefined;
  }

  exists() {
    return !!this.timerId;
  }
}

/**
 * 定时器：下一次重绘时执行
 */
export class FrameTimer extends Timer {
  constructor() {
    super();

    if (
      typeof requestAnimationFrame === 'undefined' ||
      typeof cancelAnimationFrame === 'undefined'
    ) {
      // 降级
      // 以屏幕刷新率 60Hz 为准，约 16ms 刷新一次 (1000/60 ≈ 16.7)
      return new TimeoutTimer(16);
    }

    this.timerId = undefined;
  }

  add(callback) {
    // eslint-disable-next-line no-undef
    cancelAnimationFrame(this.timerId);
    // eslint-disable-next-line no-undef
    this.timerId = requestAnimationFrame(callback);
  }

  clear() {
    // eslint-disable-next-line no-undef
    cancelAnimationFrame(this.timerId);
    this.timerId = undefined;
  }

  exists() {
    return !!this.timerId;
  }
}
