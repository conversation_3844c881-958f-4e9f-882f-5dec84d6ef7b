import { Bullet } from './bullet';
import { random, cloneDeep } from './utils';
import { TimeoutTimer, FrameTimer } from './timer';

const defaultOptions = {
  /** 弹幕水平间距（px） */
  offsetH: 32,
  /** 弹幕垂直间距（px） */
  offsetV: 8,
  /** 单条弹幕高度（px） */
  bulletHeight: 30,
  /** 是否无限弹幕（无缝衔接） */
  infinite: false,
  /** 是否循环（整体一轮结束后重新播放） */
  loop: true,
  /** 每次移动距离（px） */
  step: 1,
  /** 获取弹幕宽度函数 */
  getBulletWidth: () => 0,
  /** 弹幕更新监听函数 */
  onUpdate: () => {},
};

/** 轨道类型 */
const TRACK_TYPE = {
  first: 0,
  second: 1,
};

export class DanmakuManager {
  constructor(options) {
    this.setOptions(options);

    /** 当前弹幕序号 */
    this.cursor = -1;
    /** 所有弹幕原始数据 */
    this.dataSet = [];
    /** 正在运行的弹幕 */
    this.bullets = [];
    /** 是否正在播放 */
    this.isPlaying = false;

    /** 是否已初始化完成 */
    this._initialized = false;
    /** 轨道类型-距离顶部的映射 */
    this.TRACK_TOP_MAP = {};
    /** frame（更新弹幕） */
    this._walkFrameTimer = new FrameTimer();
    /** timeout（重启） */
    this._restartTimeoutTimer = new TimeoutTimer(5e3);
  }

  setOptions(options) {
    this.options = { ...defaultOptions, ...options };
    return this.options;
  }

  init({ dataSet = [], viewWidth = 0 }) {
    if (this._initialized) {
      return;
    }
    if (!Array.isArray(dataSet)) {
      throw new Error('Danmaku init dataSet is not an Array');
    }

    /** 所有的弹幕 */
    this.dataSet = dataSet;
    /** 容器视图宽度 */
    this.viewWidth = viewWidth;

    const { offsetV, bulletHeight } = this.options;

    this.TRACK_TOP_MAP = {
      [TRACK_TYPE.first]: offsetV,
      [TRACK_TYPE.second]: offsetV * 2 + bulletHeight,
    };

    this.resetBullets();

    this._initialized = true;
  }

  play() {
    if (this.dataSet.length <= 0) {
      this.isPlaying = false;
      return;
    }

    this.isPlaying = true;

    const nextTick = () => {
      // 清除重启计时器
      this._restartTimeoutTimer.clear();

      this._walkFrameTimer.add(this.play.bind(this));
    };

    // 首次开始
    if (this.cursor === -1) {
      // 初始化轨道
      // 只需要最开始的弹幕有偏移，后面的会定间距排列
      this.prepareNextBullet(TRACK_TYPE.first, random(50, 100));
      this.prepareNextBullet(TRACK_TYPE.second, random(20, 80));

      // 启动失败：cursor 没变，表示没有准备好符合要求的弹幕，则不启动
      if (this.cursor === -1) {
        return;
      }

      this.emitUpdate();
      nextTick();

      return;
    }

    // 播放结束
    if (this.bullets.length <= 0) {
      this.resetBullets();
      this.isPlaying = false;

      // 循环播放
      if (this.options.loop) {
        this._restartTimeoutTimer.add(this.play.bind(this));
        return;
      }

      return;
    }

    // if (!this._walkFrameTimer.exists()) {
    //   // 从 暂停/停止 到 开始
    //   console.log('Danmaku restarted.');
    // }

    this.walkBullets();
    nextTick();
  }

  pause() {
    this._walkFrameTimer.clear();
    this._restartTimeoutTimer.clear();
    this.isPlaying = false;
  }

  stop() {
    this.pause();
    this.resetBullets();
  }

  resetBullets() {
    this.cursor = -1;
    this.bullets.splice(0, this.bullets.length);

    this.emitUpdate();
  }

  appendBullet(cursor, track, randomOffsetX = 0) {
    const data = cloneDeep(this.dataSet[cursor]);

    const width = this.options.getBulletWidth(data);
    if (width <= 0) {
      // 抛弃没有宽度的弹幕
      return null;
    }

    const bullet = new Bullet({
      data,
      track,
      width,
      top: this.TRACK_TOP_MAP[track],
      left: this.viewWidth + randomOffsetX,
      randomOffsetX,
    });

    this.bullets.push(bullet);
    this.cursor = cursor;

    return bullet;
  }

  prepareNextBullet(track, randomOffsetX = 0) {
    if (this.cursor >= this.dataSet.length - 1) {
      if (!this.options.infinite) {
        // 不循环，则停止添加
        return null;
      }
      this.cursor = -1;
    }

    return this.appendBullet(this.cursor + 1, track, randomOffsetX);
  }

  removeBullet(bullet) {
    const index = this.bullets.findIndex((b) => b.key === bullet.key);
    if (index >= 0) {
      this.bullets.splice(index, 1);
    }
  }

  refresh(options = {}) {
    const { viewWidth = this.viewWidth } = options;

    this.viewWidth = viewWidth;

    this.bullets.forEach((bullet) => {
      bullet.set({
        left: viewWidth + bullet.randomOffsetX,
      });
    });

    this.emitUpdate();
  }

  walkBullets() {
    const { step, offsetH } = this.options;

    this.bullets.forEach((bullet) => {
      // 当弹幕“右侧”超出视图左侧时，移除弹幕
      if (
        bullet.offsetX >
        bullet.width + offsetH + this.viewWidth + bullet.randomOffsetX
      ) {
        this.removeBullet(bullet);
        return;
      }

      // 当弹幕“右侧”进入视图后，追加下一个弹幕（前提是该弹幕未追加过）
      if (
        !bullet.nextKey &&
        bullet.offsetX > bullet.width + offsetH + bullet.randomOffsetX
      ) {
        const nextBullet = this.prepareNextBullet(bullet.track) || {};
        bullet.nextKey = nextBullet.key;
      }

      bullet.walk(step);
    });

    this.emitUpdate();
  }

  emitUpdate() {
    this.options.onUpdate?.(this.bullets);
  }
}
