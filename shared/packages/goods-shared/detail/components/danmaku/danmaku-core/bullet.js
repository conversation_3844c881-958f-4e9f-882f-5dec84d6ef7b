import { random } from './utils';

export class Bullet {
  constructor(options) {
    const {
      key = random(),
      track,
      width = 0,
      top = 0,
      left = 9999,
      randomOffsetX = 0,
      data,
    } = options;

    /** 轨道编号 */
    this.track = track;
    /** 当前弹幕的 key */
    this.key = key;
    /** 元素宽度 */
    this.width = width;
    /** 顶部距离 */
    this.top = top;
    /** 左侧距离 */
    this.left = left;
    /** 在现有偏移基础上的随机偏移 */
    this.randomOffsetX = randomOffsetX;
    /** 携带数据 */
    this.data = data;

    /** 距离视图右侧边框偏移量 */
    this.offsetX = 0;
    /** 下一弹幕的 key */
    this.nextKey = undefined;
  }

  set({ left }) {
    this.left = left;
  }

  walk(step = 1) {
    if (!this.width) {
      return;
    }

    this.offsetX += step;
  }
}
