import WscComponent from 'shared/common/base/wsc-component';
import getSafeApp from 'shared/utils/get-safe-app';

import { DanmakuManager } from './danmaku-core';
import { getTextLength } from './utils';

WscComponent({
  properties: {
    /** 弹幕数据源 */
    source: {
      type: Function,
    },
    /** 是否隐藏弹幕 */
    visible: {
      type: Boolean,
      value: true,
    },
    /** 整体相对顶部距离 */
    offsetTop: {
      type: Number,
      value: 0,
    },
    /** 自定义样式 */
    customClass: {
      type: String,
    },
    /** 最小弹幕数量，小于则不展示 */
    minCount: {
      type: Number,
      value: 5,
    },
    /** 最大文字长度，超出部分省略 */
    maxTextLength: {
      type: Number,
      value: 20,
    },
    /** 头像字段 */
    avatarProp: {
      type: String,
      value: 'avatar',
    },
    /** 文本字段 */
    textProp: {
      type: String,
      value: 'content',
    },
  },

  data: {
    bullets: [],
  },

  observers: {
    visible(visible) {
      if (!visible) {
        this.danmakuManager?.pause();
      }
    },
  },

  danmakuManager: undefined,

  ready() {
    this.init();
  },

  detached() {
    this.destroy();
  },

  /**
   * 页面显示和隐藏后控制弹幕开关以避免性能损耗
   */
  pageLifetimes: {
    show() {
      if (this.properties.visible && this.danmakuManager?.isPlaying) {
        this.danmakuManager?.play();
      }
    },

    hide() {
      this.danmakuManager?.pause();
    },
  },

  methods: {
    getDataSource() {
      const { source } = this.properties;

      return Promise.resolve(
        typeof source === 'function' ? source() : source || []
      );
    },

    init() {
      this.getDataSource()
        .then((dataSet) => {
          if (dataSet.length < this.properties.minCount) {
            return;
          }

          const danmaku = new DanmakuManager({
            getBulletWidth: this.getBulletWidth.bind(this),
            onUpdate: this.updateBullets.bind(this),
          });

          danmaku.init({
            dataSet,
            viewWidth: this.getViewWidth(),
          });

          this.danmakuManager = danmaku;

          this.triggerEvent('init', danmaku);
        })
        .catch((e) => {
          console.warn('Danmaku init failed.', e);
        });
    },

    destroy() {
      this.danmakuManager?.stop();
    },

    getViewWidth() {
      return getSafeApp().getSystemInfoSync().windowWidth;
    },

    getBulletWidth(data) {
      const { textProp, maxTextLength } = this.properties;
      const textLength = getTextLength(data[textProp]);

      // 排除无内容的
      if (textLength === 0) {
        return 0;
      }

      // 出现 ellipsis 后会多出 `...` 的位置
      const ellipsisCount = textLength > maxTextLength ? 1 : 0;

      // 50 是排除文字后的容器宽度
      return 50 + (Math.min(textLength, maxTextLength) + ellipsisCount) * 14;
    },

    updateBullets(bullets) {
      this.setYZData({ bullets });
    },

    handleBulletTap(e) {
      const { bullet } = e.currentTarget.dataset;
      this.triggerEvent('bullet-tap', bullet, {
        bubbles: true,
        composed: true,
        capturePhase: true,
      });
    },
  },
});
