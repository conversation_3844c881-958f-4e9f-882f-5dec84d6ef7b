<view class="showcase-components-container">
  <block wx:for="{{ curComponents }}" wx:for-item="item" wx:for-index="index" wx:key="unique">
    <showcase-goods wx:if="{{ item.itemType === 'goods' }}" component-data="{{ item }}" is-async="{{ item.is_async || item.isAsync }}" offline-id="{{ extra.offlineId }}" page-lifetimes="{{ ['onPageScroll', 'onPullDownRefresh'] }}" kdt-id="{{ extra.kdtId }}" app-id="{{ extra.appId }}" need-logger-params="{{ true }}" bind:buy="handleGoodsBuy" />
    <showcase-link wx:elif="{{ item.itemType === 'link' }}" component-data="{{ item }}" />
    <showcase-coupon wx:elif="{{ item.itemType === 'coupon' }}" kdt-id="{{ extra.kdtId }}" component-data="{{ item }}" />
    <showcase-cube wx:elif="{{ item.itemType === 'cube' }}" kdt-id="{{ extra.kdtId }}" component-data="{{ item }}" bind:navigate="onNavigate" bind:contactback="onContactBack" extra-data="{{ extra }}" />
    <showcase-line wx:elif="{{ item.itemType === 'line' }}" component-data="{{ item }}" />
    <showcase-notice wx:elif="{{ item.itemType === 'notice' }}" component-data="{{ item }}" />
    <showcase-points-goods wx:elif="{{ item.itemType === 'points_goods' }}" component-data="{{ item }}" kdt-id="{{ extra.kdtId }}" />
    <showcase-rich-text wx:elif="{{ item.itemType === 'rich_text' }}" kdt-id="{{ extra.kdtId }}" component-data="{{ item }}" index="{{ index }}" />
    <showcase-contact-us  wx:elif="{{ item.itemType === 'contact_us' }}" contact-us-class="showcase-contact-us" business-id="{{ extra.businessId }}" source-param="{{ extra.sourceParam }}" tab-page="{{ extra.inTabPage }}" component-data="{{ item }}" index="{{ index }}" />
    <showcase-present-gift wx:elif="{{ item.itemType === 'present_gift' && !extra.hideGift }}" kdt-id="{{ extra.kdtId }}" component-data="{{ item }}" index="{{ index }}" />
    <showcase-store wx:elif="{{ item.itemType === 'store' }}" component-data="{{ item }}" bind:jumpToLink="jumpToLink" />
    <showcase-title-text wx:elif="{{ item.itemType === 'title_text' }}" component-data="{{ item }}" bind:navigate="onNavigate" bind:contactback="onContactBack" />
    <showcase-title wx:elif="{{ item.itemType === 'title' }}" component-data="{{ item }}" extra-data="{{ extra }}" bind:jumpToLink="onNavigate" bind:contactback="onContactBack" />
    <showcase-text wx:elif="{{ item.itemType === 'texts' }}" component-data="{{ item }}" extra-data="{{ extra }}" bind:jumpToLink="jumpToLink" bind:contactback="onContactBack" />
    <showcase-image wx:elif="{{ item.itemType === 'image' }}" kdt-id="{{ extra.kdtId }}" component-data="{{ item }}" extra-data="{{ extra }}" bind:jumpToLink="onNavigate" bind:contactback="onContactBack" />
    <showcase-video-plugin wx:elif="{{ item.itemType === 'video' }}" app-id="{{ extra.appId }}" kdt-id="{{ extra.hqKdtId || extra.lsKdtId ||  extra.kdtId }}" hq-kdt-id="{{ hqKdtId }}" component-data="{{ item }}" />
    <showcase-white wx:elif="{{ item.itemType === 'white' }}" component-data="{{ item }}" />
    <showcase-search class="cap-search-view" wx:elif="{{ item.itemType === 'search' }}" component-data="{{ item }}" />
    <!-- 日食记定制 -->
    <showcase-feature-video-search class="cap-search-view" wx:elif="{{ item.itemType === 'feature_video_search' }}" component-data="{{ item }}" />
    <showcase-goods-new wx:elif="{{ item.itemType == 'goods_new'}}" kdt-id="{{ extra.kdtId }}" component-data="{{ item }}" />
    <showcase-new-tag-list-left wx:elif="{{ item.itemType == 'tag_list_left' }}" kdt-id="{{ extra.kdtId }}" app-id="{{ extra.appId }}" offline-id="{{ extra.offlineId }}" component-data="{{ item }}" page-lifetimes="{{ ['onPullDownRefresh', 'onPageScroll'] }}" bind:buy="handleGoodsBuy" />
    <showcase-tag-list-top wx:elif="{{ item.itemType == 'tag_list_top'}}" kdt-id="{{ extra.kdtId }}" app-id="{{ extra.appId }}" offline-id="{{ extra.offlineId }}" component-data="{{ item }}" page-lifetimes="{{ ['onPullDownRefresh'] }}" bind:buy="handleGoodsBuy" />
    <showcase-ump-seckill wx:elif="{{ item.itemType == 'ump_seckill'}}" kdt-id="{{ extra.kdtId }}" app-id="{{ extra.appId }}" offline-id="{{ extra.offlineId }}" component-data="{{ item }}" page-data="{{ pageCommonData }}" page-random-number="{{ pageRandomNumber }}" page-random-string="{{ pageCommonData.pageRandomString }}" page-lifetimes="{{ ['onPullDownRefresh'] }}" />
    <showcase-time-limitbargain wx:elif="{{ item.itemType === 'bargain'}}" component-data="{{item}}" kdt-id="{{ extra.kdtId }}" app-id="{{ extra.appId }}" offline-id="{{ extra.offlineId }}" component-data="{{ item }}" page-data="{{ pageCommonData }}" page-random-number="{{ pageRandomNumber }}" page-random-string="{{ pageCommonData.pageRandomString }}" page-lifetimes="{{ ['onPullDownRefresh'] }}" />
    <showcase-offline-shop-info wx:elif="{{ item.itemType === 'offline_shop_info' }}" kdt-id="{{ extra.kdtId }}" offline-id="{{ extra.offlineId }}" component-data="{{ item }}" />
    <showcase-storeinfo wx:elif="{{ item.itemType === 'storeinfo'}}" component-data="{{ item }}" bind:navigate="onNavigate" />
    <showcase-note-card wx:elif="{{ item.itemType === 'note_card' }}" component-data="{{ item }}" kdt-id="{{ extra.kdtId }}" />
    <showcase-oriented-poster wx:elif="{{ item.itemType === 'oriented_poster'}}" kdt-id="{{ extra.kdtId }}" buyer-id="{{ extra.buyerId }}" component-data="{{ item }}" bind:jumpToLink="onNavigate" bind:logger="logger" />
    <showcase-feature-video wx:elif="{{ item.itemType === 'feature_video'}}" kdt-id="{{ extra.kdtId }}" hq-kdt-id="{{ hqKdtId }}" buyer-id="{{ extra.buyerId }}" component-data="{{ item }}" />
    <showcase-rishiji-ump wx:elif="{{ item.itemType === 'rishiji_ump'}}" kdt-id="{{ extra.kdtId }}" buyer-id="{{ extra.buyerId }}" component-data="{{ item }}" page-lifetimes="{{ ['onPullDownRefresh'] }}" page-random-string="{{ pageCommonData.pageRandomString }}" />
    <showcase-unicashier wx:elif="{{ item.itemType === 'unicashier'}}" component-data="{{ item }}" />
    <showcase-goods-recommend wx:elif="{{ item.itemType === 'goods_recommend' }}" bind:buy="handleGoodsBuy" component-data="{{ item }}" page-random-number="{{ pageRandomNumber }}" component-index="{{ index }}" offline-id="{{ extra.offlineId }}" />
    <showcase-social-fans wx:elif="{{ item.itemType === 'official_account' || item.itemType === 'social_fans' }}" component-data="{{ item }}" kdt-id="{{ extra.kdtId }}" extra-data="{{ extra }}" />
    <showcase-audio wx:elif="{{ item.itemType === 'audio' }}" index="{{ index }}" component-data="{{ item }}" bind:btn-click="audio__trigger" bind:slider-drag="audio__updateProgress" bind:src-change="audio__srcChange" />
    <!-- 课程组件-小程序不支持 -->
    <!-- <showcase-course wx:elif="{{ item.itemType === 'knowledge-goods' }}" component-data="{{ item }}" kdt-id="{{ extra.kdtId }}" /> -->
    <!-- 知识付费组件 -->
    <showcase-content wx:elif="{{ item.itemType === 'content' }}" component-data="{{ item }}" kdt-id="{{ extra.kdtId }}" />
    <showcase-column wx:elif="{{ item.itemType === 'column' }}" component-data="{{ item }}" kdt-id="{{ extra.kdtId }}" />
    <!-- 教育直播-小程序不需要支持 -->
    <!-- <showcase-live wx:elif="{{ item.itemType === 'live' }}" component-data="{{ item }}" kdt-id="{{ extra.kdtId }}" /> -->
    <showcase-member wx:elif="{{ item.itemType === 'member' }}" component-data="{{ item }}" kdt-id="{{ extra.kdtId }}" />
    <!-- app.json没注册，有线上问题 -->
    <!-- <showcase-punch wx:elif="{{ item.itemType === 'punch' }}" component-data="{{ item }}" kdt-id="{{ extra.kdtId }}" /> -->
    <showcase-ump-groupon wx:elif="{{ item.itemType == 'groupon'}}" kdt-id="{{ extra.kdtId }}" app-id="{{ extra.appId }}" offline-id="{{ extra.offlineId }}" component-data="{{ item }}" page-lifetimes="{{ ['onPullDownRefresh'] }}" page-data="{{ pageCommonData }}" page-random-number="{{ pageRandomNumber }}" page-random-string="{{ pageCommonData.pageRandomString }}" />
    <showcase-ump-timelimited-discount wx:elif="{{ item.itemType === 'ump_limitdiscount' }}" component-data="{{ item }}" kdt-id="{{ extra.kdtId }}" page-data="{{ pageCommonData }}" page-lifetimes="{{ ['onPullDownRefresh'] }}" page-random-string="{{ pageCommonData.pageRandomString }}" />
    <showcase-period-buy wx:elif="{{ item.itemType === 'period_buy' }}" component-data="{{ item }}" />
  </block>
  <view class="showcase-loading" wx:if="{{ loading }}">
    <van-loading />
  </view>
  <view id="theme-feature__content-end-hook" style="height: 1px;"></view>
</view>
