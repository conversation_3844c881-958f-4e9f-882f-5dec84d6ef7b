@import '~mixins/index.scss';

.month-block {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background: $white;
}

.date-block {
  flex: 0 1 auto;
  height: 64px;
  text-align: center;
  min-width: 14%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Regular;
  font-size: 16px;
  color: $text-color;
  position: relative;
  border-radius: 4px;
  transition: .3s background;

  &-flex {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  &.empty {
    visibility: hidden;
  }

  &.disabled {
    pointer-events: none;

    .date {
      color: #ccc;
    }
  }
}

.header-label,
.footer-label {
  font-family: Avenir-Heavy;
  font-size: 10px;
  text-align: center;
  line-height: 14px;
  transform: scale(0.8);
}

.date {
  line-height: 22px;
}