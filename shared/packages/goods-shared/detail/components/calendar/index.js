import WscComponent from 'pages/common/wsc-component/index';

WscComponent({
  properties: {
    data: {
      type: Array,
      observer: 'getActivityDate',
    },

    year: {
      type: Number,
      observer: 'getActivityDate',
    },

    month: {
      type: Number,
      observer: 'getActivityDate',
    },

    activeId: {
      type: String,
      observer: 'activeChange'
    },
  },

  data: {
    date: {},
    renderDays: [],
  },

  methods: {
    getActivityDate(changekey) {
      console.log(changekey);
      const { data = [], year, month } = this.data;
      let activeItem = {
        days: []
      };

      if (year && month) {
        data.forEach(item => {
          if (+item.year === +year && +item.month === +month) {
            activeItem = item;
          }
        });
      } else if (data.length) {
        activeItem = data[0];
      }

      this.setYZData({
        date: activeItem
      }, () => {
        this.getRenderDays();
      });
    },
    getRenderDays() {
      const { year = 0, month = 0, days = [] } = this.data.date;
      const monthDayCount = new Date(year, month, 0).getDate();

      if (days.length !== monthDayCount) {
        throw new Error('传入的日期数据不对');
      }

      const firstDay = new Date(`${year}/${month}/${days[0].date}`);

      if (firstDay.toString() === 'Invalid Date') {
        throw new Error('传入的日期数据不能被格式化');
      }

      const day = firstDay.getDay();

      const renderDays = [];

      for (let index = 0; index < day; index++) {
        renderDays.push({ date: -1, id: -1 });
      }

      this.setYZData({
        renderDays: renderDays.concat(days)
      });
    },
    activeChange(val) {
      if (this.data.activeDate === val) return;

      this.setYZData({
        activeDate: val,
      });
      this.data.renderDays.some(item => {
        if (item.id === val) {
          this.$emit('select', item);
          return true;
        }
        return false;
      });
    },

    onItemClick({ currentTarget }) {
      const { item } = currentTarget.dataset;
      this.setYZData({
        activeDate: item.id,
      });
      this.triggerEvent('select', item);
    }
  },
});
