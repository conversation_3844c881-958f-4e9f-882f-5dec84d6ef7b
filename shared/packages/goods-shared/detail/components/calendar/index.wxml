<slot />

<view wx:if="{{ date.days.length }}" class="month-block">
  <theme-view
    wx:for="{{ renderDays }}"
    wx:for-item="dateData"
    wx:key="{{ dateData.date }}"
    bg="{{ activeDate === dateData.id ? 'main-bg' : '' }}"
    color="{{ activeDate === dateData.id ? 'general' : '' }}"
    opacity="0.1"
    custom-class="date-block-flex"
    class="date-block {{ dateData.date === -1 ? 'empty' : '' }} {{ !dateData.isEnable ? 'disabled' : '' }}"
    data-item="{{ dateData }}"
    bind:tap="onItemClick"
  >
    <theme-view
      wx:if="{{ !!dateData.isEnable && dateData.header }}"
      color="{{ dateData.activeHeader ? 'general' : '' }}"
      custom-class="header-label"
    >
      {{ dateData.header }}
    </theme-view>
    <text class="date">{{ dateData.date }}</text>
    <text wx:if="{{ !!dateData.isEnable }}" class="footer-label">{{ dateData.footer }}</text>
  </theme-view>
</view>