import { GoodsComponent } from '@goods/common/vanx-component';
import { jumpLink } from '@goods/utils';

GoodsComponent({
  name: 'CommonCell',

  properties: {
    border: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '',
    },
    url: {
      type: String,
      default: '',
    },
    value: {
      type: String,
      default: '',
    },
    isLink: {
      type: Boolean,
      default: false,
    },
    valueClass: {
      type: String,
      default: 'common-cell__value',
    },
    titleClass: {
      type: String,
      default: 'common-cell__title',
    },
  },

  options: {
    styleIsolation: 'shared',
  },

  methods: {
    onClick() {
      if (this.data.url) {
        jumpLink(this.data.url);
      }
      this.triggerEvent('click');
    },
  },
});
