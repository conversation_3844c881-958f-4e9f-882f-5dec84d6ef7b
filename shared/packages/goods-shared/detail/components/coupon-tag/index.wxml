<theme-view
  wx:if="{{ priceTag }}"
  custom-class="coupon-tag {{ colorStyle === 'white' ? 'coupon-tag--white' : '' }} {{ size === 'small' ? 'coupon-tag--small' : '' }}"
  color="{{ colorStyle === 'white' ? 'general' : '#fff' }}"
  bg="{{ colorStyle === 'white' ? '#fff' : 'main-bg' }}"
>
  <template is="inner" data="{{ priceTag, prefix }}"></template>
</theme-view>

<template name="inner">
  <text class="coupon-tag-pre">{{
    prefix + priceTag.tagPre
  }}</text>
  <text class="coupon-tag-font--xl">
    {{ priceTag.price }}
  </text>
  <text class="coupon-tag-suf">{{ priceTag.tagSuf }}</text>
</template>
