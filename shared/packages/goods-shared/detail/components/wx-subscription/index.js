import { GoodsComponent } from '@goods/common/vanx-component';
import getSystemInfo from 'shared/utils/browser/system-info';
import compareVersion from 'shared/utils/compare-version';

const app = getApp();

/** shard目前组件内不支持处理异常，临时先复制一个处理 */
GoodsComponent({
  externalClasses: ['btn-class'],

  properties: {
    // 订阅消息的场景值
    subscribeScene: {
      type: String,
      observer(val) {
        if (val) {
          this.getTemplateIdList();
        }
      },
    },

    showToast: {
      type: Boolean,
      default: false,
    },

    imgUrl: {
      type: String,
      default:
        'https://img01.yzcdn.cn/upload_files/2020/03/25/Fq24gCcTJhE3bpk9giOf7LPqC-7H.gif',
    },
  },

  data: {
    // 订阅消息模版ID
    templateIdList: [],

    showTips: false, // 是否展示gif引导
  },

  methods: {
    // 获取模板id
    getTemplateIdList() {
      const { subscribeScene } = this.data;
      return app
        .request({
          path: '/wscump/common/get-template.json',
          data: {
            scene: subscribeScene,
          },
        })
        .then((data = {}) => {
          this.setYZData({ templateIdList: data.templateIdList || [] });
        });
    },

    toast(...args) {
      if (this.data.showToast) {
        wx.showToast(...args);
      }
    },

    handleBtnClick() {
      if (!this.data.subscribeScene) {
        this.triggerEvent('next', true);
        return;
      }

      if (this.data.templateIdList) {
        this.subscriptionMessage();
      } else {
        this.getTemplateIdList()
          .then(() => {
            this.subscriptionMessage();
          })
          .catch(() => {
            this.triggerEvent('next', true);
          });
      }
    },

    subscriptionMessage() {
      let templateIdList = this.data.templateIdList || [];

      // iOS客户端7.0.6版本、Android客户端7.0.7版本之后的一次性订阅/长期订阅才支持多个模板消息
      if (templateIdList instanceof Array && templateIdList.length >= 2) {
        const { system, version } = getSystemInfo();
        const androidSuportedMuti =
          system.indexOf('Android') > -1 &&
          compareVersion(version, '7.0.6') >= 0;
        const iosSuportedMuti =
          system.indexOf('IOS') > -1 && compareVersion(version, '7.0.5') >= 0;
        if (!androidSuportedMuti || !iosSuportedMuti) {
          templateIdList = new Array(templateIdList[0]);
        }
      }

      if (
        templateIdList instanceof Array &&
        templateIdList.length > 0 &&
        wx.canIUse('requestSubscribeMessage')
      ) {
        wx.getSetting({
          withSubscriptions: true,
          success: ({ subscriptionsSetting }) => {
            // subscriptionsSetting.itemSettings 返回所有订阅过的消息
            // 未选择「总是订阅」或首次订阅，打开订阅引导弹窗
            let flag = false;
            if (subscriptionsSetting.itemSettings) {
              templateIdList.forEach((templateId) => {
                if (!subscriptionsSetting.itemSettings[templateId]) {
                  flag = true;
                }
              });
            } else {
              flag = true;
            }
            if (flag) {
              this.setYZData({ showTips: true });
            }
          },
        });

        wx.requestSubscribeMessage({
          tmplIds: templateIdList,
          success: (res) => {
            let success = false;

            if (JSON.stringify(res).indexOf('accept') > -1) {
              this.toast({ title: '订阅通知成功', icon: 'success' });
              this.subscribeCallback();
              success = true;
            } else if (JSON.stringify(res).indexOf('reject') > -1) {
              this.toast({
                title: '请在小程序设置中允许订阅消息',
                icon: 'none',
              });

              app.logger.requestError({
                name: 'wxSubscribeError',
                message: '请在小程序设置中允许订阅消息',
                alert: 'info',
                detail: res,
              });
            }

            this.triggerEvent('success', {
              scene: this.data.subscribeScene,
              success,
            });
          },

          fail: (err) => {
            let errorMsg = '订阅微信通知失败';
            if (err.errMsg && err.errMsg.indexOf('switched off') > -1) {
              errorMsg = '请在小程序设置中允许订阅消息';
            }

            this.toast({ title: errorMsg, icon: 'none' });
            this.triggerEvent('fail', { scene: this.data.subscribeScene, err });

            app.logger.requestError({
              name: 'wxSubscribeError',
              message: errorMsg,
              alert: 'info',
              detail: err,
            });
          },

          complete: () => {
            // 关闭动画指引
            this.closeTips();
          },
        });
      } else {
        this.toast({ title: '订阅微信通知失败', icon: 'none' });
        this.triggerEvent('fail', { scene: this.data.subscribeScene });

        app.logger.requestError({
          name: 'wxSubscribeError',
          message: '订阅微信通知失败',
          alert: 'info',
          detail: new Error('当前小程序基础库不支持订阅'),
        });
      }

      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'click_wx_subscription',
          en: '点击订阅消息',
          si: app.getKdtId(),
        });
    },

    subscribeCallback() {
      app.request({
        path: '/wscump/common/subscription-callback.json',
        data: {
          scene: this.data.subscribeScene,
          templateIdList: this.data.templateIdList,
        },
      });
    },

    closeTips() {
      this.setYZData({ showTips: false });
    },
  },
});
