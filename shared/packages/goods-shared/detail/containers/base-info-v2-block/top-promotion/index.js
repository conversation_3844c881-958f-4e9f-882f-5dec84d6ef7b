import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions } from '@youzan/vanx';

GoodsComponent({
  name: 'TopPromotion',
  getters: ['topGoodsPromotionList', 'isUmpCouponList', 'showTopGoodsPromotion', 'couponLabelPprocessing'],
  state: ['displayPop', 'orderPromotionLimit'],
  data: {
    isShow: false,
    isShowMore: false,
    isShowCashBackRule: false,
    moreText: {},
    dashStyle: `linear-gradient(
      to bottom,
      var(--theme-general) 20%,
      var(--theme-general) 20%,
      var(--theme-general) 20%,
      rgba(255, 255, 255, 0) 20%,
      rgba(255, 255, 255, 0) 35%,
      rgba(136, 76, 255, 1) 35%,
      var(--theme-general) 35%,
      var(--theme-general) 35%,
      var(--theme-general) 60%,
      rgba(255, 255, 255, 0) 60%,
      rgba(255, 255, 255, 0) 55%,
      rgba(255, 255, 255, 0) 75%,
      var(--theme-general) 75%,
      var(--theme-general) 75%,
      var(--theme-general) 100%
    )`
  },

  methods: {
    ...mapActions([
      'hideMoreOptimalCoupon',
      'toggleCouponPopUp',
      'handlePromotionCoupon'
    ]),
  },
});
