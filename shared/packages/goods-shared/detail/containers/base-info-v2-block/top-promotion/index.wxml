<view class="promotion-list goods-promotion__coupon-info"   data-is-preposition="true" bind:tap="handlePromotionCoupon">
     <theme-view
        wx:for="{{ couponLabelPprocessing }}"
        wx:for-item="item"
        wx:key="{{ index }}"
        class="goods-promotion__coupon-desc-item"
        color="general"
        opacity="0.1"
    >
      <view class="item-cont">
        <view class="item-cont-span" wx:if="{{ item.title }}">
          <view class="item-cont-span__title">{{ item.title }}</view>
          <view class="item-cont-span__dash" style="background: {{ dashStyle }}"></view>
        </view>
        <text>{{ item.cont }}</text>
        <view class="bg" style="background-color: var(--theme-general)"></view>
      </view>
    </theme-view>
    <view class="right-icon" wx:if="{{ topGoodsPromotionList.length }}">
        <theme-view 
        color="general"
        class="right-text"
        >
            <view>
             {{ isUmpCouponList }}
            </view>
        </theme-view>
        <theme-view class="right_text_icon" color="general">
            <van-icon
            class="goods-promotion__arrow"
            name="arrow"
            style="font-size: 14px; ;"
            />
        </theme-view>
    </view>
    <promotion-pop
    show="{{ displayPop.showCouponPopUp }}"
    />
</view>