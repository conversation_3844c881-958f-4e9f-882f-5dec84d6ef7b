@import '~shared/common/css/_variables.scss';

.promotion-list {
  display: flex;
  font-size: 13px;
  position: relative;
  white-space: nowrap;
  margin: 12px 0 16px 0;

  .goods-promotion__coupon-desc-item {
    margin-right: 6px;

    .goods-promotion__coupon-desc-item-text {
      display: inline-block;
      line-height: 13px;
      padding: 4px;
    }

    .item-cont {
      border-radius: 2px;
      height: 20px;
      padding: 0 4px;
      position: relative;
      line-height: 20px;
      font-size: 14px;

      .bg {
        width: 100%;
        height: 100%;
        position: absolute;
        opacity: 0.1;
        top: 0;
        left: 0;
        border-radius: 2px;
      }

      .item-cont-span {
        display: inline-flex;
        align-items: center;
        height: 20px;

        &__dash {
          flex: 1;
          opacity: 0.3;
          height: 100%;
          width: 1px;
          transform: scaleY(0.7);
          margin: 0 2px 0 2px;
        }
      }
    }
  }

  .right-icon {
    display: flex;
    position: absolute;
    top: 4px;
    right: -3px;
    font-size: 14px;

    .right-text {
      line-height: 11px;
    }

    .right_text_icon {
      margin-top: -1.5px;
    }
  }
}
