import { GoodsComponent } from '@goods/common/vanx-component';
import getSystemInfo from 'shared/utils/browser/system-info';
import { fullfillImage } from '@goods/utils';

GoodsComponent({
  name: 'FirstPage',

  properties: {
    alias: {
      type: String,
      value: ''
    }
  },

  data: {
    url: '',
    height: 0,
    title: '',
    price: ''
  },

  attached() {
    // 微页面跳转时会埋下图片数据，然后直接从ls中拿进行渲染
    const { alias } = this.data;
    const key = `goodsDetail-${alias}`;
    wx.getStorage({
      key,
      success: (res) => {
        if (!res.data) {
          return;
        }

        const { image = {}, title = '', price = '0.00' } = res.data;
        this.setData({
          url: fullfillImage(image.url, '!260x0.jpg'),
          height: this.getPictureHeight(image),
          title,
          price
        });
      }
    });

    // 移除掉防止有坑
    wx.removeStorage({ key });

    // 3秒后隐藏掉透明背景
    this.timer = setTimeout(() => {
      this.$commit && this.$commit('setPageFinished', true);
    }, 3000);
  },

  detached() {
    clearTimeout(this.timer);
  },

  methods: {
    getPictureHeight({ height, width }) {
      let ratio = 0.75;
      ratio = Math.max(ratio, height / width);

      ratio = Math.min(2, ratio);
      ratio = Math.max(1 / 2.5, ratio);

      const winWidth = getSystemInfo().windowWidth;

      return Math.floor(winWidth * ratio);
    },

    onImageLoadOrError() {
      this.trigger('goodsDetail:speedindex');
    }
  }
});
