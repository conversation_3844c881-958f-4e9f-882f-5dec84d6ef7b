<view
  class="first-page__container"
  wx:if="{{ url }}"
>
  <image
    mode="aspectFit"
    src="{{ url }}"
    class="first-page__image"
    style="height: {{ height }}px"
    bindload="onImageLoadOrError"
    binderror="onImageLoadOrError"
  />
  <view class="first-page__baseinfo">
    <theme-view
      color="general"
      class="first-page__price"
    >
      <view class="first-page__price-label">￥</view>
      <view class="first-page__price-value">{{ price }}</view>
    </theme-view>
    <view class="first-page__title">{{ title }}</view>
  </view>
</view>
