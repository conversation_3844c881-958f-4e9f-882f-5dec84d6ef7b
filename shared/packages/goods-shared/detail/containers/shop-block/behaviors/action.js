import navigate from 'shared/utils/navigate';
import { setLogger } from '@goods/common/logger';
import args from '@youzan/weapp-utils/lib/args';

export default Behavior({
  methods: {
    gotoShop() {
      setLogger({
        et: 'click', // 事件类型
        ei: 'seller_enter', // 事件标识
        en: '点击进店逛逛', // 事件名称
      });

      navigate.switchTab({ url: '/packages/home/<USER>/index' });
    },

    haiGouClick() {
      const {
        shopConfig: { actHaiGouIcon },
        flowActInfo = {},
        shop: { kdtId },
      } = this.data;
      const iconOpt = flowActInfo.iconOpt || {};
      const path = iconOpt[`path${actHaiGouIcon}`] || '';
      const appId = 'wxf1fdc416d4ced1b3';
      path &&
        this.setFlowClickLog(actHaiGouIcon) +
          wx.navigateToMiniProgram({
            appId,
            path: args.add(path, {
              from_source: `private_flow_${kdtId}_${actHaiGouIcon}`,
            }),
          });
    },

    setFlowClickLog() {
      const {
        alias,
        shopConfig: { actHaiGouIcon },
      } = this.data;
      setLogger({
        et: 'click', // 事件类型
        ei: 'flow_entrance_haiGou_click', // 事件标识
        en: '特色流量入口haiGou点击', // 事件名称
        params: {
          from_source: `flow_entrance_${alias}_${actHaiGouIcon}`,
          component: 'flow_entrance_haiGou',
        },
      });
    },

    setFlowViewLog() {
      const {
        alias,
        shopConfig: { actHaiGouIcon },
        shop: { kdtId },
      } = this.data;
      setLogger({
        et: 'view', // 事件类型
        ei: 'flow_entrance_haiGou_view', // 事件标识
        en: '展示haiGou入口', // 事件名称
        params: {
          from_source: `goods_${alias}_${kdtId}_${actHaiGouIcon}`,
          component: 'flow_entrance_haiGou',
        }, // 事件
      });
    },
  },
});
