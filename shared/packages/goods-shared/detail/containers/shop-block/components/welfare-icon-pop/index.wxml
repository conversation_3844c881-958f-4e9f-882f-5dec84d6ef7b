<van-popup
    wx:if="{{ inited }}"
    show="{{ show }}"
    safe-area-inset-bottom
    round
    lock-scroll
    safe-area-inset-bottom
    position="bottom"
    custom-class="goods-shop__intro-popup-welfare"
    bind:close="handleCloseModal"
  >
    <view class="goods-shop__intro-popup-welfare-cnt">
      <view class="goods-shop__intro-popup-welfare-cnt-box">
        <view class="box-content">
          <view class="goods-shop__intro-popup-welfare-cnt-box-p1">
            感谢有赞公益对中国社会公益支持，让我们在有赞开店；一路上我们遇到过很多困难，但同样也因为你们的支持，让我们更加相信，相信的力量。
          </view>
          <view class="goods-shop__intro-popup-welfare-cnt-box-p2">
            有赞公益，期待与你的同行！
          </view>
        </view>
        <i class="goods-shop__intro-popup-welfare-cnt-box-logo" />
      </view>
    </view>
    <view class="welfare-shop-pop__btn" bind:tap="handleCloseModal">我知道了</view>
    <view class="welfare-shop-pop__mask"></view>
  </van-popup>