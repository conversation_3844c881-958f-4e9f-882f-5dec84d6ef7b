import { GoodsComponent } from '@goods/common/vanx-component';

GoodsComponent({
  name: 'WelfarePop',

  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        val &&
          !this.data.inited &&
          this.setYZData({
            inited: true,
          });
      },
    },
  },

  methods: {
    handleCloseModal() {
      this.triggerEvent('close');
    },
  },
});
