.goods-shop__intro-popup-welfare {
  background: url(https://img01.yzcdn.cn/upload_files/2020/11/26/FmTtsOFg5hR3LsBA6vKkMu4h0DnZ.png)
    no-repeat;
  box-sizing: border-box;
  background-size: cover;
  z-index: 300 !important;
  border-radius: 24px 24px 0 0;

  .common-popup__content {
    padding: 0;
  }

  &-cnt {
    position: relative;
    margin: 21.6vw 16px 0;
    background-color: #fff;
    font-size: 14px;
    color: #168644;
    border: 1px solid transparent;
    border-radius: 16px;
    background-image: linear-gradient(#fff, #fff),
      linear-gradient(to bottom, #17b157, rgba(23, 177, 87, 0));
    background-origin: border-box;
    background-clip: content-box, border-box;

    &-box {
      position: relative;
      margin: 7px;
      border: 1px solid transparent;
      border-radius: 10px;
      box-sizing: border-box;
      background-image: linear-gradient(#fff, #fff),
        linear-gradient(
          to bottom,
          rgba(23, 177, 91, 0.22),
          rgba(23, 177, 87, 0.18)
        );
      background-origin: border-box;
      background-clip: content-box, border-box;
      line-height: 24px;

      .box-content {
        padding: 26px 22px 43px;
      }

      &-logo {
        position: absolute;
        bottom: 6px;
        right: 6px;
        display: inline-block;
        width: 41px;
        height: 41px;
        background-size: 100% 100%;
        background-image: url(https://img01.yzcdn.cn/upload_files/2020/11/24/Fiig2mFT399ZL-alqCanC_PgvsnL.png);
        z-index: 3;
      }

      &-p2 {
        margin-top: 15px;
      }
    }
  }

  .welfare-shop-pop__btn {
    margin: 30px 16px 12px;
    background: #6cbf72;
    height: 40px;
    border-radius: 22px;
    line-height: 40px;
    font-size: 16px;
    font-weight: 800;
    color: #fff;
    text-align: center;
  }
}
