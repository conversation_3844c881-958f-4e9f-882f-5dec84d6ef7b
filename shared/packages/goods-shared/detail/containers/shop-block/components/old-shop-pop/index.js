import { GoodsComponent } from '@goods/common/vanx-component';

GoodsComponent({
  name: 'OldShopPop',

  getters: ['oldShopYears'],

  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        val && !this.data.inited && this.setYZData({
          inited: true
        });
      }
    },
  },

  methods: {
    handleOldShopTap() {
      this.triggerEvent('close');
    }
  }
});
