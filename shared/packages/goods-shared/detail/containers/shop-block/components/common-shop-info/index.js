import { GoodsComponent } from '@goods/common/vanx-component';
import actionBehavior from '../../behaviors/action';
import { mapActions } from '@youzan/vanx';

GoodsComponent({
  name: 'CommonShopInfo',

  state: ['shopConfig', 'shop', 'flowActInfo', 'iconUrl'],

  getters: ['alias'],

  ready() {
    this.getFlowActInfo(this.setFlowViewLog.bind(this));
  },

  methods: {
    ...mapActions(['getFlowActInfo']),
  },

  behaviors: [actionBehavior],
});
