@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.common-shop {
  padding: 16px;

  &__extra {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: flex-start;
    margin-top: -4px;
  }

  &__btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 8px;
    box-sizing: border-box;
    white-space: nowrap;
    height: 27px;
    border: 1px solid #edebf0;
    color: $text-color;
    border-radius: 1rem;
    font-size: 12px;
    line-height: normal;
  }
  &__exicon {
    position: absolute;
    top: 17px;
  }
  &__btns {
    text-align: center;
    margin-top: 16px;
    &__action {
      display: inline-block;
      border: 1px solid #d8d8d8;
      border-radius: 13px;
      text-align: center;
      padding: 1px 0 4px 0;
      box-sizing: border-box;
      width: 120px;
      &__init {
        font-size: 13px;
        color: #2f2f47;
        vertical-align: bottom;
        display: inline-block;
        margin-left: 4px;
        line-height: 15px;
      }
    }
  }
}
