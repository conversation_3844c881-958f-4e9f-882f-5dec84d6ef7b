<shop-main-info custom-class="common-shop">
  <view slot="tags">
    <slot name="tags" />
  </view>

  <view slot="extraInfo" class="common-shop__extra" wx:if="{{ flowActInfo }}">
    <image
      wx:if="{{iconUrl}}"
      class="common-shop__exicon"
      src="{{ iconUrl }}"
      style="width: 45px"
      mode="widthFix"
      bind:tap="haiGouClick"
    />
  </view>
  <view slot="extraInfo" class="common-shop__extra" wx:else>
    <view class="common-shop__btn theme__color theme__border" bind:tap="gotoShop">
      进店逛逛
    </view>
  </view>

  <view slot="extraBtn" class="common-shop__btns" wx:if="{{ flowActInfo }}">
    <view class="common-shop__btns__action" bind:tap="gotoShop">
      <van-icon name="shop-o" size="15px" color="#2f2f47" custom-style="top:3px;" />
      <text class="common-shop__btns__action__init">进店逛逛</text>
    </view>
  </view>
  <view slot="shopBanner" class="goods-shop__banner">
    <flow-entrance-banner biz-name="goods" />
  </view>
</shop-main-info>