
<view wx:if="{{ shop && shop.logo }}">
  <view class="goods-shop">
    <!-- 店铺logo -->
    <image src="{{ shop.logo }}" alt="店铺logo" class="goods-shop__logo" bind:tap="gotoShop" />
    <view class="goods-shop__content">
      <view class="goods-shop__top">
        <!-- 店铺名称 -->
        <view class="goods-shop__title">{{ shopName }}</view>
        <slot name="extraInfo"/>
      </view>
      <view class="goods-shop__tags">
        <!-- 品牌标签 -->
        <view wx:if="{{ shopBrandTag }}" class="goods-shop__tag-brand" bind:tap="gotoShopInfo" />
        <!-- 企业标签 -->
        <view wx:if="{{ shopOfficialTag }}" class="goods-shop__tag-official" bind:tap="gotoShopInfo" />
        <slot name="tags"/>
        <block
          wx:for="{{ shopTags }}"
          wx:for-item="item"
          wx:key="tag"
        >
          <van-tag
            wx:if="{{ item.tag }}"
            class="goods-shop__tag"
            color="#EE0A24"
          >
            {{ item.tag }}
          </van-tag>
        </block>
      </view>
    </view>
  </view>
  <slot name="shopBanner"/>
  <view class="goods-shop__btns">
    <slot name="extraBtn"/>
  </view>
<view>


