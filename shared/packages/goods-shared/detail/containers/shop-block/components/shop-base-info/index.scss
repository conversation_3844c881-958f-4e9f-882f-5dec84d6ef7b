.goods-shop {
  display: flex;
  flex-direction: row;
  align-items: center;

  &__logo {
    display: block;
    width: 48px;
    height: 48px;
    border-radius: 2px;
  }

  &__content {
    display: flex;
    height: 48px;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 8px;
    box-sizing: border-box;
    width: calc(100% - 56px);
  }

  &__top {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
  }

  &__title {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-right: 8px;
  }

  &__tags {
    display: flex;
    align-items: center;
  }

  &__tag {
    margin-right: 6px;
    vertical-align: middle;
    text-align: center;
    white-space: nowrap;
    border-radius: 2px;
    line-height: 16px;
    padding: 0 2px;
    position: relative;
    top: 0;
  }

  &__tag-official {
    display: inline-block;
    width: 56px;
    height: 16px;
    background: url('https://img01.yzcdn.cn/public_files/cf75189501624ce4bf364525bfc9ab64.png')
      no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-right: 6px;
    vertical-align: middle;
  }

  &__tag-brand {
    width: 30px;
    height: 16px;
    background: url('https://img01.yzcdn.cn/public_files/39924d40a8fc9bd0e94ffa646fec1793.png')
      no-repeat;
    background-size: 100% 100%;
    margin-right: 6px;
  }
}
