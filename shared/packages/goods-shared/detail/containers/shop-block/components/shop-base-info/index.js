
import { GoodsComponent } from '@goods/common/vanx-component';
import navigate from 'shared/utils/navigate';
import { setLogger } from '@goods/common/logger';
import { jumpLink } from '@goods/utils';

GoodsComponent({
  name: 'ShopBaseInfo',

  state: ['shop'],

  getters: [
    'shopName',
    'shopLink',
    'shopTags',
    'shopOfficialTag',
    'shopBrandTag',
    'shopInfoLink',
  ],

  methods: {
    gotoShop() {
      setLogger({
        et: 'click', // 事件类型
        ei: 'seller_pic', // 事件标识
        en: '点击店铺头像', // 事件名称
      });

      navigate.switchTab({ url: '/packages/home/<USER>/index' });
    },

    gotoShopInfo() {
      jumpLink(this.data.shopInfoLink);
    },
  }
});
