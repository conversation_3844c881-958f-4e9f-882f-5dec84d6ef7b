@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.shop-info {
  &__address {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 15px;
    line-height: 1.5;
    color: $gray-darker;
    font-size: 14px;
    box-sizing: border-box;
  }

  &__address-text {
    margin-left: 6px;

    @include multi-ellipsis(1);
  }

  &__offline {
    &::after {
      border-bottom-width: 0 !important;
    }
    &::before {
      content: " ";
      position: absolute;
      pointer-events: none;
      box-sizing: border-box;
      transform-origin: center;
      bottom: 0;
      left: 16px;
      right: 16px;
      bottom: auto;
      transform: scaleY(.5);
      border-bottom: 1px solid #EBEDF0;
      z-index: 2;
    }
  }
}