<view>
  <!-- 定位地址 -->
  <view wx:if="{{ shopAddress }}" class="shop-info__address">
    <van-icon size="13px" name="location-o" color="#999"/>
    <text class="shop-info__address-text">{{ shopAddress }}</text>
  </view>

  <!-- 线下门店 -->
  <van-cell
    wx:if="{{ showMultiStoreInfo || retail.show }}"
    title="{{ multiStoreName }}"
    value="{{ retailAddressContent }}"
    border="{{ false }}"
    class="shop-info__offline"
    custom-class="van-hairline--top"
    center
    is-link
    url="/packages/shop/multi-store/index/index?to=/packages/home/<USER>/index"
  />
</view>