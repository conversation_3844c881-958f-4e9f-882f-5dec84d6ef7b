<view
  class="feature-shop__container"
  style="background: linear-gradient(to bottom, {{ featureShopInfo.color }}, #fff)"
>
  <view class="feature-shop__content">
    <shop-main-info>
      <view slot="tags">
        <slot name="tags" />
      </view>

      <view slot="extraInfo" class="feature-shop__extra">
        <image class="feature-shop__extra-img" src="{{ featureShopInfo.pic }}" bind:tap="togglewelfareIntro" alt="特色店铺" />
      </view>

      <view slot="extraBtn" class="feature-shop__btns">
        <view 
          class="feature-shop__btns__action"
          bind:tap="gotoShop"
          style="color: {{featureShopInfo.color}}; border: 1px solid {{featureShopInfo.color}}"
        >
          <van-icon name="shop-o" size="15px" color="{{featureShopInfo.color}}" custom-style="top:3px;" />
          <text class="feature-shop__btns__action__init">进店逛逛</text>
        </view>
      </view>

      <view slot="shopBanner" class="goods-shop__banner">
        <flow-entrance-banner biz-name="goods" />
      </view>
    </shop-main-info>
    <welfare-icon-pop show="{{ display.showWelfarePopup }}" bind:close="togglewelfareIntro"/> 
  </view>
</view>