import { GoodsComponent } from '@goods/common/vanx-component';
import { setLogger } from '@goods/common/logger';
import { mapState, mapActions } from '@youzan/vanx';
import { FEATURE_SHOP } from '../../constant';
import actionBehavior from '../../behaviors/action';

GoodsComponent({
  name: 'FeatureShopInfo',

  behaviors: [actionBehavior],

  state: ['display'],

  mapData: mapState({
    featureShopInfo(state, getters) {
      if (getters.showPublicBenefit) {
        setLogger({
          et: 'view', // 事件类型
          ei: 'view_public_benefit', // 事件标识
          en: '有赞公益曝光', // 事件名称
        });

        return FEATURE_SHOP.publicBenefit;
      }

      return {};
    },
  }),

  methods: {
    ...mapActions(['togglewelfareIntro']),
  },
});
