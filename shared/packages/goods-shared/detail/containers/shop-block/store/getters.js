import {
  get,
  args,
  buildUrl,
  getRedirectUrl,
  formatDistance,
} from '@goods/utils';
import { getUrl, SHOP_INFO } from '@goods/common/page-path';

export default {
  // 店铺链接
  shopLink(state, getters) {
    const { id = 0 } = state.multistore;
    const { kdtId } = getters;

    const url = buildUrl(
      args.add('/wscshop/showcase/homepage', {
        kdt_id: kdtId,
        oid: id,
      }),
      'h5',
      kdtId
    );

    return getRedirectUrl(url);
  },

  // 店铺全部商品链接
  shopGoodsLink(state, getters) {
    const { id = 0 } = state.multistore;
    const { kdtId } = getters;

    const url = buildUrl(
      args.add('/wscshop/feature/goods/all', {
        kdt_id: kdtId,
        oid: id,
      }),
      'h5',
      kdtId
    );

    return getRedirectUrl(url);
  },

  // 店铺信息页
  shopInfoLink(state, getters) {
    const { kdtId } = getters;

    const url = getUrl(SHOP_INFO, {
      kdt_id: kdtId,
    });

    return url;
  },

  shopName(state) {
    const { name, hideStore } = state.multistore;
    return hideStore ? state.shop.name : name || state.shop.name;
  },

  shopTags() {
    return [];
  },

  // 官方标 2, 5； 具体MAP请参考：TEAM_CERT
  shopOfficialTag(state) {
    const { principalCertType = 0 } = state.shopConfig;
    return principalCertType === 2 || principalCertType === 5;
  },

  // 品牌标 1, 2, 4；5展示品牌标识
  shopBrandTag(state) {
    const BRAND_CERT_TYPE = state.businessConfigs.brandCertType || {};
    const { brandCertType = 0 } = state.shopConfig;
    const { FLAGSHIP, EXCLUSIVE, BOUTIQUE, SHOPPING, CHAIN } = BRAND_CERT_TYPE;
    return (
      [FLAGSHIP, EXCLUSIVE, BOUTIQUE, SHOPPING, CHAIN].indexOf(
        brandCertType
      ) !== -1
    );
  },

  // 店铺公众号关注
  shopMpAccount(state) {
    return get(state, 'mpData.account.id', 0);
  },

  // 店铺网点距离信息
  shopAddress(state) {
    const { id: offlineId, address } = state.multistore || {};
    const { distance, hideStore } = state.multistore;
    let addressText = '';
    if (hideStore || !offlineId) {
      return '';
    }

    if (distance) {
      addressText = formatDistance(distance);
    }

    if (address) {
      addressText += ` ${address}`;
    }

    return addressText;
  },

  // 多网点开启的情况下，如果商家关闭了网点切换开关，这里的分店入口也要隐藏
  showMultiStoreInfo(state) {
    const {
      // env: { isNewHopeKdt },
      // shopConfig: { hasPhysicalStore },
      multistore: { id, openMultiStoreSwitch, hideStore },
    } = state;
    // 新希望店铺隐藏线下门店入口
    // return id ? !hideStore && openMultiStoreSwitch : hasPhysicalStore && !isNewHopeKdt;
    // 先隐藏线下门店入口
    return id && !hideStore && openMultiStoreSwitch;
  },

  showAddressContent(state, getters) {
    return (
      getters.retailAddressContent && getters.retailAddressContent.length > 0
    );
  },

  multiStoreName(state, getters) {
    return state.multistore.id && !getters.showAddressContent
      ? '其他分店'
      : '线下门店';
  },

  multiStoreLink(state, getters) {
    const { id } = state.multistore;
    const { kdtId, showAddressContent } = getters;
    return id && !showAddressContent
      ? buildUrl(`/wscump/multistore/index?kdt_id=${kdtId}`, 'h5', kdtId)
      : buildUrl(`/wscump/physicalstore/index?kdt_id=${kdtId}`, 'h5', kdtId);
  },
  showOldShopBadge(state) {
    const {
      shopOperateDurationTagSwitch = 0,
      shopOperateDurationYears = 0,
    } = state.shopConfig;
    const { showOldShopBadge } = state.diy.shopBlock;

    return (
      showOldShopBadge &&
      shopOperateDurationYears > 0 &&
      +shopOperateDurationTagSwitch === 1
    );
  },

  oldShopYears(state) {
    let { shopOperateDurationYears = 0 } = state.shopConfig;
    const MAX_YEAR = 20;

    // 目前最多只有到20年的标
    if (shopOperateDurationYears && shopOperateDurationYears >= MAX_YEAR) {
      shopOperateDurationYears = MAX_YEAR;
    }

    return shopOperateDurationYears;
  },

  oldShopYearsStyle(state) {
    const { oldShop = {} } = state.businessConfigs;
    const { url = '', width = '' } = oldShop;
    return `background-image: url(${url});width: ${width}`;
  },

  // 显示有赞公益
  showPublicBenefit(state) {
    const { showPublicBenefit } = state.shopConfig;

    return !!Number(showPublicBenefit);
  },
};
