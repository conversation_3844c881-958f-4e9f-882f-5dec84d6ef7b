import { fetchInfo } from 'shared/components/flow-entrance/api';
import get from '@youzan/weapp-utils/lib/get';

export default {
  toggleOldShopIntro({ state }) {
    state.display.showOldShopPopup = !state.display.showOldShopPopup;
  },
  togglewelfareIntro({ state }) {
    state.display.showWelfarePopup = !state.display.showWelfarePopup;
  },
  getFlowActInfo({ commit, state }, payload) {
    const bizName = 'goods';
    fetchInfo({
      bizName,
      kdtId: state.shop.kdtId,
    }).then((res) => {
      const info = get(res, 'info', '');
      commit('FLOW_ACT_INFO', { info, payload });
    });
  },
};
