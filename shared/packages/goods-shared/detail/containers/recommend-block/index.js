import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions } from '@youzan/vanx';
// import Toast from '@vant/weapp/dist/toast/toast';

console.warn = () => {};

GoodsComponent({
  name: 'RecommendBlock',

  getters: ['showRecommendGoods', 'showNoStockRecommendGoods', 'goodsId'],

  properties: {
    position: {
      type: String,
      value: 'flow', // flow 或者 bar 表示推荐商品展示的位置，很奇怪～外部拆分为两个组件更合理，该组件更应该作为基础组件
    },
  },

  watch: {
    'data.showRecommendGoods': function (isTrue) {
      isTrue && this.showComponent();
    },
    'data.goodsId': function (goodsId) {
      if (this.lastGoodsId !== goodsId) {
        this.data.showRecommendGoods && this.showComponent();
        this.lastGoodsId = goodsId;
      }
    },
  },

  data() {
    return {
      show: false,
      componentName: null,
    };
  },

  ready() {
    this.setYZData({
      show: true,
      componentName:
        this.data.position === 'bar'
          ? 'NoStockRecommendGoods'
          : 'RecommendGoods',
    });
  },

  methods: {
    ...mapActions(['getRecommendGoods']),
    showComponent(component, must) {
      // 非售罄的情况，需要滚动到了才展示，售罄的直接展示
      if (
        !must &&
        this.data.position === 'bar' &&
        !this.data.showNoStockRecommendGoods
      )
        return;

      const { fetched } = this;
      this.fetched = true;
      if (!fetched) {
        // Toast.loading();
        this.getRecommendGoods().then(() => {
          // Toast.clear();
        });
      }
    },
  },
});
