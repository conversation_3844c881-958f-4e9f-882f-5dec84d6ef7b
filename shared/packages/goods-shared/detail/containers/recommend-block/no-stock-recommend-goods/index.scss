@import '~shared/common/css/_variables.scss';

$goodsListHeight: 210px;

.m-recommend-bottom {
  position: relative;
  background-color: $white;

  &--has-recommend {
    transition: transform 0.3s ease-in;
    transform: translateY($goodsListHeight) translateZ(0);
  }

  &--popup {
    border-radius: 20px 20px 0 0;
    box-shadow: 0 -2px 16px rgba(100, 101, 102, 0.76);
    overflow: hidden;
    transform: translateY(0) translateZ(0);
  }

  &__top {
    position: relative;
    padding: 10px 15px;
    background-color: #f2f3f5;
    border-radius: 20px 20px 0 0;

    &-title {
      width: 93%;
      font-size: 16px;
      line-height: 24px;
      color: $text-color;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &-desc {
      margin-top: 8px;
      font-size: 13px;
      color: $gray-dark;
    }

    &-arrow {
      position: absolute;
      top: 20px;
      right: 16px;
      font-size: 18px;
      transform: rotate(-90deg);
    }

    &-arrow-reverse {
      transform: rotate(90deg);
    }
  }
}

.recommend-goodslist {
  display: block;
  height: $goodsListHeight;
}
