import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions } from '@youzan/vanx';

GoodsComponent({
  name: 'NoStockRecommendGoods',

  state: ['display'],
  getters: [
    'showNoStockRecommendGoods',
    'hasRecommendGoods',
    'showGoodsList',
    'showRecommendGoods'
  ],

  ready() {
    !this.data.showNoStockRecommendGoods && this.setBarStyleRecommendGoods();
    this.toggleGoodsShow();
  },

  methods: {
    ...mapActions(['toggleGoodsShow', 'toggleGoodsMask', 'setBarStyleRecommendGoods']),

    togglePopUp() {
      if (!this.data.hasRecommendGoods) {
        return;
      }

      this.toggleGoodsMask(!this.data.display.showBottomRecommendGoods);
      this.toggleGoodsShow();
      this.triggerEvent('togglePopUp', true);
    }
  }
});
