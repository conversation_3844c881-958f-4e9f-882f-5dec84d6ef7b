import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions, mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'RowRecommendGoods',
  state: ['recommendGoods'],
  getters: ['showRecommendGoods', 'showGoodsList'],

  mapData: mapState({
    recommendGoodsDataRow(state, getters) {
      const { recommendGoodsDataRow } = getters;
      this.logRecommendList(recommendGoodsDataRow.goods);
      return recommendGoodsDataRow;
    }
  }),

  mounted() {
    this.getRecommendGoods();
  },

  methods: {
    ...mapActions(['getRecommendGoods', 'logRecommendList']),
  }
});
