@import '~shared/common/css/_variables.scss';

.customer-goods {
  position: relative;
  padding: 0;
  width: 100%;
  background-color: $white;
  border-bottom: 1px solid #f0f0f0;

  &__show {
    padding-top: 15px;
    height: 210px !important;
  }

  &__list {
    height: 0;
    overflow-y: auto;
    // transition: all 300ms;
    max-height: 210px;

    .cap-goods-layout__container.swipe {
      .cap-goods-layout__wrapper {
        flex: 0 0 30%;
      }
    }

    .cap-goods-layout__info-title.swipe .title {
      font-size: 14px;
      font-weight: 500 !important;
    }

    .sale-price {
      font-size: 16px !important;
      font-weight: 500 !important;
    }

    .cap-goods-layout__info {
      padding: 0 !important;
    }

    .cap-goods__img--cover {
      border-radius: 6px;
    }
  }
}
