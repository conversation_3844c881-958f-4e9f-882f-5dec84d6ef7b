export default {
  // 获取推荐商品
  getRecommendGoods(state) {
    state.recommendGoods.loading = true;
  },

  getRecommendGoodsSuccess(state, goodsList) {
    state.recommendGoods = {
      ...state.recommendGoods,
      page: ++state.recommendGoods.page,
      loading: false,
      finished: goodsList.length === 0,
      list: [...goodsList],
    };
  },

  getRecommendGoodsFail(state) {
    state.recommendGoods.loading = false;
  },

  hasBarStyleRecommendGoods(state) {
    state.recommendGoods.hasBarBlock = true;
  },
};
