import { get } from '@goods/utils';

const goodsConfig = {
  goods: [],
  layout: 1,
  button_text: '',
  page_margin: 16,
  goods_margin: 8,
  border_radius_type: 1,
  text_align_type: 'left',
};

export default {
  goodsId(state) {
    return get(state, 'goods.id');
  },
  // 是否展示推荐商品
  showRecommendGoods(state) {
    const hasFeatureLoaded = get(state, 'goodsDetail.featureLoaded', false);
    const { showRecommendGoods } = state.shopConfig;
    /* 微页面在显示推荐插件情况下，最多只能显示 8 个组件的问题，通过在微页面展示后才展示推荐商品来处理 */
    return showRecommendGoods && hasFeatureLoaded;
  },

  showNoStockRecommendGoods(state, getters) {
    // 商品未下架、非外链、未售罄
    const hasFeatureLoaded = get(state, 'goodsDetail.featureLoaded', false);
    return (
      hasFeatureLoaded &&
      state.goods.isDisplay === 1 &&
      !getters.showAvailableStores &&
      !state.goods.isOutlink &&
      !state.goods.stockNum &&
      !getters.isSupportFCodeBuy
    );
  },

  // 使用网点名称，而不是网点地址
  showRecommendGoodsAddress(state) {
    const name = get(state, 'multistore.name', '');
    if (name) {
      return name;
    }

    const address = get(state, 'delivery.deliveryAddress.address', '');
    if (address) {
      return address;
    }

    return get(state, 'shop.name', '该店铺');
  },

  hasRecommendGoods(state, getters) {
    return getters.recommendGoodsData.goods.length > 0;
  },

  recommendGoodsData(state) {
    const goods = get(state, 'recommendGoods.list', []);
    const recommendGoodsConfig = get(
      state,
      'businessConfigs.recommendGoodsConfig',
      {}
    );
    return {
      ...recommendGoodsConfig,
      ...goodsConfig,
      goods,
    };
  },

  recommendGoodsDataRow(state) {
    const list = get(state, 'recommendGoods.list', []);
    const recommendGoodsConfig = get(
      state,
      'businessConfigs.recommendGoodsConfig',
      {}
    );

    const goodsConfig = {
      ...recommendGoodsConfig,
      goods_from: '0',
      layout: 6,
      type: 'goods',
    };

    return {
      ...goodsConfig,
      goods: list.map((item) => {
        const copyItem = { ...item };

        copyItem.loggerParams = { ...copyItem.loggerParams };
        copyItem.loggerParams.banner_id =
          copyItem.loggerParams.banner_id.replace(
            /recommend_fixed/,
            'stockout'
          );
        copyItem.loggerParams.component = 'stockout';
        copyItem.url = copyItem.url.replace(/recommend_fixed/, 'stockout');
        return copyItem;
      }),
    };
  },

  showGoodsList(state, getters) {
    return (
      state.display.showBottomRecommendGoods && getters.hasRecommendGoods > 0
    );
  },
};
