import api from '@goods/utils/api';
import { appendLogParams, get } from '@goods/utils';
import {
  checkEduChainStore,
  checkRetailChainStore,
  checkPureWscChainStore
} from '@youzan/utils-shop';
import { getComponentLoggerParams, ensureAppLogger } from 'shared/utils/logger-type';
import spm from 'shared/utils/spm';

export default {
  getRecommendGoods({ state, getters, dispatch, commit }) {
    commit('getRecommendGoods');

    const shopMetaInfo = get(state, 'shop.shopMetaInfo', {});

    function getChannel() {
      if (checkEduChainStore(shopMetaInfo)) {
        return 'edu';
      }

      if (checkRetailChainStore(shopMetaInfo)) {
        return 'retail';
      }

      if (checkPureWscChainStore(shopMetaInfo)) {
        return 'chain';
      }

      return 'wsc';
    }

    // 获取场景值
    function getScene() {
      return `${getChannel()}_goods_detail`;
    }

    // 无库存商品，埋点参数不一样
    const bizName = state.goods.stockNum > 0 ? 'goods_detail' : 'goods_detail_lack';
    return api
      .post({
        url: '/wscgoods/postRecommendGoods.json',
        headers: {
          'content-type': 'application/json'
        },
        data: {
          bizName,
          goodsIds: [state.goods.id],
          alias: getters.alias,
          kdtId: getters.kdtId,
          storeId: state.multistore.id,
          itemSize: 20,
          scene: getScene(),
          channel: getChannel()
        },
        errorMsg: '获取推荐商品错误'
      })
      .then((data) => {
        const currentLog = spm.getPageSpmTypeId();
        const list = appendLogParams(
          data,
          getters.kdtId,
          {
            spm: `${currentLog}.${state.goods.id}`,
            biz: 'recommend_fixed'
          },
          {
            name: 'recommend_fixed',
            text: '更多精选商品'
          }
        );

        if (list.length > 0) {
          // 显示遮罩
        }
        commit('getRecommendGoodsSuccess', list);
        dispatch('toggleGoodsMask', getters.showNoStockRecommendGoods && getters.hasRecommendGoods);
      })
      .catch((err) => {
        commit('getRecommendGoodsFail', err);
      });
  },

  hideRecommendBottom({ state, dispatch }) {
    state.display.showBottomRecommendGoods = false;
    dispatch('toggleGoodsMask', false);
  },

  toggleGoodsShow({ state }) {
    state.display.showBottomRecommendGoods = !state.display.showBottomRecommendGoods;
  },

  setBarStyleRecommendGoods({ commit }) {
    commit('hasBarStyleRecommendGoods');
  },

  logRecommendList(store, goods = []) {
    if (!goods.length) return;
    const loggerParams = goods.map(({ loggerParams }) => loggerParams);

    const loggerMsg = getComponentLoggerParams('view', loggerParams);
    ensureAppLogger(loggerMsg);
  }
};
