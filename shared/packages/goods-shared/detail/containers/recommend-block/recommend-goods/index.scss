@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.recommend-goods {
  position: relative;
  margin-top: 8px;
  padding: 7px 0 16px;
  width: 100%;
  background-color: $white;

  &::before {
    @include border-retina(top);

    transform: scaleY(0.5);
    width: auto;
    top: 22px;
    left: 16px;
    right: 16px;
  }

  &__title {
    position: relative;
    display: block;
    margin: 0 auto 7px;
    width: 110px;
    padding: 5px 0;
    font-size: 14px;
    color: $text-color;
    text-align: center;
    background-color: $white;
    font-weight: bold;
  }

  &__btn {
    margin: 4px 16px 0;
  }

  .cap-goods-list__container {
    padding: 0 4px;
  }

  .van-button {
    width: 100%;
    height: 40px;
    line-height: 38px;
    border-radius: 4px;
    border-color: #dcdee0;
    color: #323233;
    font-size: 14px;

    &__text {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .cap-goods-list__img {
    width: auto;
    max-width: 100%;
  }
}
