
<view
  wx:if="{{ hasRecommendGoods && !isFastBuy }}"
  class="recommend-goods"
>
  <view class="recommend-goods__title">{{ more.title }}</view>
  <showcase-goods component-data="{{ recommendGoodsData }}" class="goods-list-wrapper"/>

  <view wx:if="{{ !appConfig.hideStroll }}" class="recommend-goods__btn">
    <van-button
      bind:click="onClick"
      tag="a"
      block=""
    >
      {{ more.text }}
    </van-button>
  </view>
</view>
