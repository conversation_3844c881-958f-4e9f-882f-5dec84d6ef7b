import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions, mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'RecommendGoods',

  state: ['appConfig', 'isFastBuy'],

  getters: ['isGroupOn', 'allGrouponUrl', 'homepageUrl', 'hasRecommendGoods'],

  data: {
    more: {
      url: '',
      text: '',
      title: ''
    }
  },

  options: {
    styleIsolation: 'shared'
  },

  mapData: mapState({
    recommendGoodsData(state, getters) {
      const { recommendGoodsData } = getters;
      this.logRecommendList(recommendGoodsData.goods);
      return recommendGoodsData;
    }
  }),

  ready() {
    const { isGroupOn, allGrouponUrl, homepageUrl } = this.data;
    this.setYZData({
      more: {
        url: isGroupOn ? allGrouponUrl : homepageUrl,
        text: isGroupOn ? '查看全部拼团商品' : '进店逛逛',
        title: isGroupOn ? '更多拼团商品' : '更多精选商品'
      }
    });
  },

  methods: {
    ...mapActions(['logRecommendList']),
    onClick() {
      wx.navigateTo({
        url: this.data.more.url
      });
    }
  }
});
