@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.recommend-store {
  &__cell {
    padding: 10px 16px !important;

    &::after {
      border: none;
    }
  }

  &__link-title {
    font-size: 14px;
    color: $text-color;
    font-weight: 500;
    flex: none;
    margin-right: 12px;
  }

  &__link-value {
    font-size: 12px;
    margin-right: -3px;
  }

  &__title {
    display: flex;
    justify-content: space-between;
    padding: 10px 10px 0 10px;
    font-size: 12px;
    color: $text-color;

    .van-icon-close {
      font-size: 16px;
    }
  }

  &__list {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    padding: 0 0 16px 16px;
    white-space: nowrap;
    overflow: auto hidden;
    -webkit-overflow-scrolling: touch;
    z-index: 1;
    position: relative;
  }

  &__item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-shrink: 0;
    box-sizing: border-box;
    margin-right: 8px;
    width: 120px;
    height: 72px;
    padding: 8px;
    line-height: 1.2;
    vertical-align: top;
    background-color: #f7f8fa;
    font-size: 12px;
    white-space: normal;
    border-radius: 2px;

    &:last-child {
      margin-right: 0;
    }

    &-name {
      color: $text-color;
      font-size: 12px;
      line-height: 16px;

      @include multi-ellipsis(2);
    }

    &-distance {
      display: flex;
      align-items: center;
      margin-top: 8px;
      font-size: 12px;
      color: $gray-dark;
      line-height: 16px;

      &-icon {
        margin-right: 4px;
        height: 12px;
      }
    }
  }
}
