<view wx:if="{{ availableStores.length > 0 }}" class="recommend-store">
  <van-cell border="{{false}}" custom-class="recommend-store__cell" is-link title="查看其他门店商品" value="查看全部" title-class="recommend-store__link-title" value-class="recommend-store__link-value" bind:tap="showPopUp"/>
  <view class="recommend-store__list">
    <!-- TODO：url跳转-->
    <navigator
      wx:for="{{ availableStores }}"
      wx:for-item="item"
      wx:key="{{ item.id }}"
      url="{{ item.link }}"
      class="recommend-store__item"
    >
      <view class="recommend-store__item-name">{{ item.name }}</view>
      <view wx:if="{{ item.distance }}" class="recommend-store__item-distance">
        <van-icon name="location-o" class="recommend-store__item-distance-icon"/>
        距离 {{ item.distance }}
      </view>
    </navigator>
  </view>
  <common-pop show="{{ isShow }}" class="recommend-store__popup">
    <van-cell wx:for="{{ availableStores }}" wx:for-item="item" wx:key="{{ item.id }}" url="{{ item.link }}" title="{{ item.name }}" is-link>
      距离 {{ item.distance }}
    </van-cell>
  </common-pop>
</view>
