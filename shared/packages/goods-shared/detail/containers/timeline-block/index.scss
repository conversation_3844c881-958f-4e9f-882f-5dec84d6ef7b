@import '~shared/common/css/_mixins.scss';

.timeline-block {
  position: relative;
  background: #fff;
  padding: 16px;
  box-sizing: border-box;
  height: 100vh;

  &__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    margin-top: 70px;
    padding: 16px 16px 30px 16px;
    min-height: 478px;
    background-color: #fff;
    box-shadow: rgba(50, 50, 51, 0.1) 0 3px 10px;
    text-align: center;
    border-radius: 10px;
  }

  &__img {
    display: block;
    width: 100%;
    min-height: 311px;
    object-fit: contain;
    border-radius: 5px;
    background: #f7f8fa;
  }

  &__title {
    width: 205px;
    font-size: 16px;
    line-height: 20px;
    margin-top: 16px;
    font-weight: bold;

    @include multi-ellipsis(2);
  }

  &__price {
    margin-top: 12px;
    font-size: 26px;
    line-height: 32px;
    color: #ee0a24;
    display: flex;
    align-items: flex-end;
    font-weight: bold;

    &-label {
      display: inline-block;
      font-size: 15px;
      line-height: 18px;
      position: relative;
      top: -4px;
    }

    &-origin {
      margin-top: 4px;
      color: #969799;
      font-size: 12px;
      line-height: 15px;
      text-decoration: line-through;
    }
  }

  &__poptip {
    position: absolute;
    right: 16px;
    bottom: 10px;
    box-sizing: border-box;
    width: 236px;
    height: 58px;
    padding-bottom: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #fff;
    background: url('https://img01.yzcdn.cn/public_files/0e5336ac8895958f32c2e556bbd5d10e.png');
    background-size: 100% 100%;
  }
}
