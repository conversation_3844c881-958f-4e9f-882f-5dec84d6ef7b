import { fullfillImage } from '@goods/utils';
import { formatSkuData, formatSkuPrice } from '@goods/store/utils/helper.js';

const FAIL_PIC_SRC =
  'https://img01.yzcdn.cn/public_files/413568137b6de59c3d81b8917632bbf1.png';

Component({
  properties: {
    alias: String,
  },

  data: {
    title: '',
    price: '',
    originPrice: '',
    picSrc: FAIL_PIC_SRC,
    imgHeight: 0,
  },

  attached() {
    // 其他api请求都不行
    const self = this;
    wx.request({
      url: 'https://h5.youzan.com/wscgoods/weapp/detail.json',
      data: {
        alias: this.data.alias,
        withSeckillError: true, // 为了兼容 2.49 版本错误，只有在有 withSeckillError 这个参数的情况下才会在检查到店铺不支持秒杀
      },
      success({ data: res = {}, statusCode }) {
        if (statusCode === 200 && res && res.data) {
          self.processGoodsData(res.data);
        }
      },
    });
  },

  methods: {
    processGoodsData(data = {}) {
      const { goodsData = {} } = data;
      const { goods = {}, skuInfo } = goodsData;
      const { pictures = [], title, origin } = goods;

      const originSku = formatSkuData(skuInfo);
      const { showPrice } = formatSkuPrice(originSku);
      const picSrc = (pictures[0] && pictures[0].url) || FAIL_PIC_SRC;

      this.setData(
        {
          title,
          price: showPrice,
          originPrice: Number(origin).toFixed(2),
          picSrc: fullfillImage(picSrc, '!730x0.jpg'),
        },
        this.fitHeight
      );
    },

    fitHeight() {
      const query = wx.createSelectorQuery().in(this);
      query.select('#timeline-block__img').boundingClientRect();
      query.selectViewport().scrollOffset();

      query.exec((res) => {
        if (!res[0] || !res[1]) return;

        this.setData({
          imgHeight: res[0].width,
        });
      });
    },
  },
});
