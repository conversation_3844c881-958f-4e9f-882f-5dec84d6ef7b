@import '~shared/common/css/_mixins.scss';

.goods-brand-image {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  padding-top: 16px;
  margin-bottom: 16px;
  display: flex;
  max-height: 486px;
  justify-content: center;

  &__icon {
    width: 114px;
    height: 114px;
    position: absolute;
    right: 15px;
    top: 10px;
    background-image: url('https://img01.yzcdn.cn/upload_files/2020/05/14/FtyTHhit8RLBFWLlBObDKUmfqSv1.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  &__content {
    width: 343px;

    &-wrap {
      width: 343px;
    }
  }
}
