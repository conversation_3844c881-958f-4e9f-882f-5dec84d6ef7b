@import '~shared/common/css/_mixins.scss';
@import '~shared/common/css/_variables.scss';

.goods-inform {
  width: 100%;
  height: 300px;
  position: relative;
  box-sizing: border-box;
  margin: 0 auto;
  font-size: 12px;
  color: $text-color;

  &::after {
    @include border-line(top);

    top: 30px;
  }

  &__title {
    position: absolute;
    left: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    width: 100px;
    height: 18px;
    background: #fff;
    font-size: 14px;
    text-align: center;
    transform: translateX(-50%);
    z-index: 1;
    font-weight: bold;
  }

  &__content {
    position: relative;
    top: 50px;
    width: 89%;
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 12px 10px;
    font-size: 12px;
    color: $gray-dark;
    line-height: 16px;
    margin: 0 auto;

    &--title {
      text {
        color: $text-color;
      }
    }
  }

  &__line {
    margin-top: 16px;
  }
}
