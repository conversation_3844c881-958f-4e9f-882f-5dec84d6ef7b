import Toast from '@vant/weapp/dist/toast/toast';
import { GoodsComponent } from '@goods/common/vanx-component';
import { node as request } from 'shared/utils/request';
import { moment as formatDate } from 'utils/time';

const app = getApp();

GoodsComponent({
  properties: {
    goodsAlias: String,
    goodsId: String,
    rootKdtId: String,
    hidden: {
      type: <PERSON><PERSON><PERSON>,
      observer(hide) {
        if (!hide && !this.data.inited) {
          this.fetchSaleRecord();
        }
      },
    },
  },

  data: {
    saleRecord: {
      list: [],
      finished: false,
      loading: false,
    },
    loadMore: {
      show: false,
      type: 'loading',
      text: '',
    },
    inited: false,
  },

  methods: {
    fetchSaleRecord(page = 1) {
      this.setYZData({
        loadMore: {
          show: true,
          type: 'loading',
          text: '',
        },

        saleRecord: {
          ...this.data.saleRecord,
          loading: true,
        },
      });

      const { rootKdtId, goodsId } = this.data;

      request({
        path: '/wscgoods/getItemTransactionRecords.json',
        data: {
          page,
          rootKdtId,
          goodsId,
          perpage: 10,
        },
      })
        .then((data) => {
          const list = [
            ...this.data.saleRecord.list,
            ...this.tradeListAdaptor(data.list),
          ];
          const finished = list.length >= data.totalItems;
          this.setYZData({
            inited: true,
            saleRecord: {
              list,
              finished,
              loading: false,
              nextPage: page + 1,
            },
            loadMore: {
              show: finished,
              type: 'text',
              text: list.length == 0 ? '暂无成交记录' : '没有更多',
            },
          });
        })
        .catch(() => {
          this.setYZData({
            inited: true,
            loadMore: {
              show: true,
              type: 'text',
              text: '暂无成交记录',
            },
          });

          Toast({
            message: '获取成交记录失败',
            context: this,
          });
        });
    },

    tradeListAdaptor(recordList = []) {
      return recordList.map((record) => {
        return {
          nickname: record.buyerName || '**',
          item_num: record.num,
          update_time: formatDate(record.payTime, 'MM-DD HH:mm:ss'),
          // item_price: "5.00",
        };
      });
    },

    handleFetchMoreSaleRecord() {
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'pagesize',
          en: '查看更多',
          params: {
            pagesize: this.data.saleRecord.nextPage,
          },
        });
      this.fetchSaleRecord(this.data.saleRecord.nextPage);
    },
  },
});
