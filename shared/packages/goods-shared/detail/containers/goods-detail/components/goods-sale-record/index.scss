/* 成交记录 */
@import '~shared/common/css/_variables.scss';

.goods-sale-record {
  font-size: 12px;
  background-color: #fff;
  overflow: hidden;
}

.goods-sale-record__header {
  display: flex;
  padding: 0 10px;
  line-height: 44px;
  font-size: 14px;
  color: $gray-dark;
  background-color: $background-color;
}

.goods-sale-record__body {
  padding-left: 10px;
}

.goods-sale-record__item {
  display: flex;
  line-height: 44px;
  padding: 0 10px 0 0;
  font-size: 14px;
  color: $text-color;
  position: relative;

  &::after {
    position: absolute;
    content: ' ';
    pointer-events: none;
    right: 16px;
    bottom: 0;
    left: 6px;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(0.5);
  }
}

.goods-sale-record__item:last-child::after {
  border-bottom: 0 none;
}

.goods-sale-record__col-1,
.goods-sale-record__col-2,
.goods-sale-record__col-3 {
  flex: 1;
  text-align: center;
}

.goods-sale-record__view-more {
  line-height: 44px;
  font-size: 14px;
  color: #646566;
  text-align: center;
}

.empty-text {
  line-height: 40px;
  text-align: center;

  &::after {
    border-width: 0;
  }
}
