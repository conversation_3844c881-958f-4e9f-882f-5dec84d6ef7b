<import src="./components/haitao/index.wxml" />
<view id="js-nav-detail" class="goods-detail__container">
  <van-tabs
    wx:if="{{ showTabs }}"
    color="{{ mainColor }}"
    z-index="{{ 1 }}"
    border="{{ false }}"
    active="{{ activeIndex }}"
    class="goods-detail__tabs"
    tab-class="goods-detail__tab"
    tab-active-class="goods-detail__tab--active"
    line-width="{{ 40 }}"
    bind:click="handleTabChange"
  >
    <van-tab
      name="{{ item.type }}"
      title="{{ item.desc }}"
      wx:for="{{ tabs }}"
      wx:key="type"
    />
  </van-tabs>

  <view
    hidden="{{ activeType !== 'goods' }}"
    class="goods-detail {{ goodsDetailTabs.length ? 'goods-detail--in-tab' : '' }}"
  >
    <goods-brand-image wx:if="{{ isShowBrandImage }}"/>
    <showcase-container
      wx:if="{{ uniqueKey }}"
      extra="{{ showcaseExtra }}"
      shop-name="{{ shopName }}"
      shop-logo="{{ shopLogo }}"
      unique-key="{{ uniqueKey }}"
      components-length="{{ componentsLength }}"
      page-lifetimes="{{ ['onReachBottom', 'onPullDownRefresh'] }}"
      bind:buy="showcaseHandleGoodsBuy"
      bind:feature-loaded="onFeatureLoaded"
    ></showcase-container>
    <template wx:if="{{ goods.isHaitao }}" is="haitao-inform"></template>
    <goods-price-intro wx:if="{{ goods.alias }}" />
  </view>

  <goods-sale-record
    hidden="{{ activeType !== 'sales' }}"
    goods-alias="{{ goods.alias }}"
    goods-id="{{ goods.id }}"
    root-kdt-id="{{ shop.rootKdtId }}"
  />
  <goods-sku-simple
    show="{{ showShowcaseBuy }}"
    alias="{{ showcaseBuyData.alias }}"
    bind:hide="handleSkuHide"
  ></goods-sku-simple>
</view>
