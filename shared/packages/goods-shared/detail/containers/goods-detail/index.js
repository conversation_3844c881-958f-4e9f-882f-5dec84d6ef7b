import Event from '@youzan/weapp-utils/lib/event';
import theme from 'shared/common/components/theme-view/theme';
import FeatureLoadControl from 'shared/components/showcase-options/feature-load/index';
import { GoodsComponent } from '@goods/common/vanx-component';
import { getPageHooks } from '@goods/extensions';
import { mapState, mapGetters, mapActions, mapMutations } from '@youzan/vanx';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import { themeColorBehaivor } from '@goods/common/behaviors';

const app = getApp();

GoodsComponent({
  behaviors: [themeColorBehaivor],

  mapData: {
    ...mapState({
      goods: (state) => state.goods || {},
      shop: (state) => state.shop || {},
    }),
    ...mapState({
      showcaseExtra() {
        const { token = {}, shop = {} } = app.$store.state;
        const { chainStoreInfo = {} } = app.getShopInfoSync();
        const { isMultiOnlineShop } = chainStoreInfo;
        const lsKdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId();

        return {
          appId: app.getAppId(),
          kdtId: shop.kdtId,
          lsKdtId,
          buyerId: token.userId,
          offlineId: shop.offlineId || '',
        };
      },
      // 写在这里只是因为，需要通过 getters.goodsDetailTabs 作为默认值，
      notUseTabsState(state, getters) {
        const hooks = getPageHooks(this);
        hooks.setDetailTabs.call(getters.goodsDetailTabs || [], (tabs) => {
          this.setYZData({
            tabs,
            showTabs: tabs.length > 1 && state.saas.detailBlock.showTab,
          });
        });
      },
    }),
    ...mapGetters([
      'goodsDetailTabs',
      // 店铺基础信息
      'shopLogo',
      'shopName',
      'isShowBrandImage',
    ]),
  },

  data: {
    mainColor: '',
    activeType: 'goods',
    activeIndex: 0,
    showcaseBuyData: {},
    showShowcaseBuy: false,
    uniqueKey: '',
    componentsLength: 0,
  },

  computed: {
    ...mapState(['goodsDetail']),
  },

  watch: {
    goodsDetail(newVal) {
      this.initShowCase(newVal);
    },
  },

  attached() {
    theme.getThemeColor('main-bg').then((color) => {
      this.setYZData({
        themeMainColor: color,
      });
    });
  },

  ready() {
    this.getShowcaseComponents();

    const hooks = getPageHooks(this);

    hooks.activeTab.tap('goods', (activeType) => {
      let activtyIndex = -1;
      this.data.tabs.forEach(({ type }, index) => {
        if (type === activeType) {
          activtyIndex = index;
        }
      });

      if (activtyIndex !== -1) {
        this.handleTabChange({ detail: { index: activtyIndex } });
      }
    });
  },

  detached() {
    const { uniqueKey } = this.data;
    FeatureLoadControl.clearShowcaseComponents(uniqueKey);
  },

  methods: {
    ...mapActions(['getShowcaseComponents']),
    ...mapMutations(['SET_FEATURE_LOADED']),

    onFeatureLoaded() {
      this.SET_FEATURE_LOADED();
    },

    handleTabChange({ detail }) {
      const activeTab = this.data.tabs[detail.index] || {};

      this.setYZData({
        activeType: activeTab.type,
        activeIndex: detail.index,
      });
      const hooks = getPageHooks(this);

      hooks.detailTabClick.call({ ...activeTab, index: detail.index });
    },

    initShowCase(goodsDetail) {
      const { showcaseComponents } = goodsDetail;
      const uniqueKey = this.data.goods.alias + makeRandomString(8);

      // 确保该商品 只绑定一个 event key
      const reg = new RegExp('feature-load:.+' + uniqueKey);
      Object.keys(Event._events).forEach((key) => {
        if (reg.test(key)) Event.off(key);
      });

      FeatureLoadControl.setShowcaseComponents(
        showcaseComponents,
        false,
        false,
        uniqueKey
      );

      this.setYZData({
        componentsLength: showcaseComponents.length,
        uniqueKey,
      });
    },

    showcaseHandleGoodsBuy({ detail }) {
      const presale = detail.preSale || detail.pre_sale;
      const alias = detail.alias;

      if (+presale === 1) {
        wx.navigateTo({
          url: `/pages/goods/detail/index?alias=${alias}`,
        });
        return;
      }

      this.setYZData({
        showShowcaseBuy: true,
        showcaseBuyData: { alias },
      });
    },

    handleSkuHide() {
      this.setYZData({
        showShowcaseBuy: false,
      });
    },
  },
});
