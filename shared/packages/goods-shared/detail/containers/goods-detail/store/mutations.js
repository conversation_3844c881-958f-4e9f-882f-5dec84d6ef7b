import { makeRandomString } from '@youzan/weapp-utils/lib/str';

export default {
  GET_SHOWCASE_COMPONENTS_SUCCESS(state, { components = [], alias = '' }) {
    if (components.length > 0) {
      const showcaseComponents = components.map(comp => {
        if (comp.type === 'goods_template_split') {
          comp = state.goodsDetail.showcaseComponents[0];
        }
        comp.key = `${String(Math.random()).slice(0, 6)}-${comp.type}`;
        return comp;
      });
      // 商品推荐组件依赖（临时）
      state.goodsDetail = {
        ...state.goodsDetail,
        showcaseComponents,
        // 微页面组件分页用
        themeFeature: {
          uniqueKey: alias + makeRandomString(8),
          componentsLength: showcaseComponents.length,
        }
      };
    } else {
      this.commit('SET_FEATURE_LOADED');
    }
  },
  SET_FEATURE_LOADED(state) {
    state.goodsDetail.featureLoaded = true;
  }
};
