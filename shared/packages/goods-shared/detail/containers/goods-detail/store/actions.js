import adaptorComponents from 'constants/adaptor-components';
import { node as request } from 'shared/utils/request';

export default {
  getShowcaseComponents({ getters, state, commit }) {
    request({
      path: '/wscshop/goods/showcase-components.json',
      query: {
        alias: getters.alias,
        isGdt: state.adConfig.isGdt,
        version: state.adConfig.version,
        adType: state.adConfig.adType,
        adaptorComponents: adaptorComponents.join(',')
      },
      config: {
        cache: true,
        expire: 10,
      },
    })
      .then(res => {
        commit('GET_SHOWCASE_COMPONENTS_SUCCESS', res);
      })
      .catch(err => {
        console.log(err.msg || '成交记录获取失败');
      });
  },
};

