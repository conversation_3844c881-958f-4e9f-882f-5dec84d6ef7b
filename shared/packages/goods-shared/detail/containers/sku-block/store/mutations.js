import { get, mapKeysToCamelCase, moment } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';

const originSkuConfig = {
  isMultiBtn: false,
  isAddCart: false,
  isGift: false,
  isPoints: false,
  isAddWish: false,
};

export default {
  // 显示SKU浮窗
  showSkuPop(state) {
    state.displayPop.skuPopShow = true;
  },

  // 关闭SKU浮层
  hideSkuPop(state) {
    // 下单唤起登录框会调用
    state.displayPop.skuPopShow = false;
  },

  setSkuConfig(state, payload = {}) {
    // 以下逻辑为重置SKU场景；每次triggerSku时需要重置
    let tempConfig = { ...originSkuConfig };
    switch (payload.skuScene) {
      case SKU_SCENE.NORMAL_BUY:
      case SKU_SCENE.ACT_BUY:
        break;
      case SKU_SCENE.ADD_WISH:
        Object.assign(tempConfig, {
          isAddWish: true,
        });
        break;
      case SKU_SCENE.POINTS_BUY:
        Object.assign(tempConfig, {
          isPoints: true,
        });
        break;
      case SKU_SCENE.GIFT_BUY:
        Object.assign(tempConfig, {
          isGift: true,
        });
        break;
      case SKU_SCENE.ADD_CART:
        Object.assign(tempConfig, {
          isAddCart: true,
        });
        break;
      case SKU_SCENE.SEL_SKU:
        Object.assign(tempConfig, {
          isMultiBtn: true,
          isAddCart: true,
        });
        break;
      default:
        // 不传入则不操作
        tempConfig = {};
        break;
    }
    state.skuConfig = {
      ...state.skuConfig,
      ...payload,
      ...tempConfig,
    };
  },

  setSkuExtraData(state, payload = {}) {
    state.skuExtraData = {
      ...state.skuExtraData,
      ...payload,
    };
  },

  resetSkuAjaxState(state) {
    state.skuConfig = {
      ...state.skuConfig,
      isAjaxing: false,
      submitType: '',
    };
  },

  /**
   * 下单参数处理，formatBuyData / formatAddCartData 前使用
   * 大部分数据来自van-sku事件
   * @param {object} activityInfo 下单时的活动信息
   * @param {object} extra 一些额外信息，诸如酒店的日历、餐饮的加菜
   */
  updatePostData(state, submitData = {}) {
    submitData = mapKeysToCamelCase(submitData);
    const {
      selectedNum: num = 0,
      selectedSkuComb = {},
      messages = {},
      cartMessages = {},
      // 活动信息
      activityInfo = {},
      // 其他额外信息
      extra = {},
      installmentRate, // 费率
      selectedInstallmentPeriod, // 分期数
    } = submitData;
    const CAN_PRIOR_USE = state.payChannels?.find(
        (itm) => itm.payChannel === 'PRIOR_USE'
    )
        ? 0
        : -1;
    const IS_SELECTED_PRIOR_USE_PAY_WAY =
        state.skuExtraData?.payWay === 'PRIOR_USE' ? 1 : CAN_PRIOR_USE;
    const { id: skuId = 0, price = 0 } = selectedSkuComb;

    const goodsId = get(state, 'goods.id', 0);
    const postage = get(state, 'distribution.postage', 0);

    state.postData = {
      ...selectedSkuComb,
      goodsId,
      skuId,
      num,
      price,
      messages,
      cartMessages,
      kdtId: state.shop.kdtId,
      storeId: state.multistore.id || 0,
      storeName: state.multistore.name || '',
      postage: typeof postage !== 'string' ? postage : 0,
      useWxpay: state.env.wxpayEnv ? 1 : 0,
      orderType: state.skuConfig.isGift ? 1 : 0,
      isPoints: state.skuConfig.isPoints ? 1 : 0,
      ...activityInfo,
      // extra 处理活动数据、酒店数据等
      ...extra,
      isInstallment: Boolean(state.payWays.instalment),
      installmentRate,
      selectedInstallmentPeriod, // 分期数
      tpps: state.pageParams.tp_ps || '',
      // // f码商品
      fcode: state.pageParams.f_code,
      // 是否是7天无理由退款商品
      isSevenDayUnconditionalReturn: state.goods.supportUnconditionalReturn,
      // 企微助手交易归因参数
      wecom_uuid: state.pageParams.wecom_uuid || state.pageParams.wecomUuid,
      // 使用先用后付
      IS_SELECTED_PRIOR_USE_PAY_WAY,
    };

    // 价格日历选择的日期
    const selectedSkuDate = get(state, 'skuExtraData.selectedSkuDate', []);

    // 如果是有价格日历的电子卡券
    const validityType = get(state, 'goodsActivity.virtualTicket.validityType');

    if (validityType === 3) {
      const selectedSkuId = get(state, 'skuExtraData.selectedSkuDateId', []);

      state.postData.skuId = selectedSkuId;
      state.postData.card_goods = { appointment_time: String(selectedSkuDate) };
    }

    // 如果是酒店商品
    if (state.goods.isHotel) {
      const startDateStr = moment(selectedSkuDate[0], 'YYYY-MM-DD');
      const endDateStr = moment(selectedSkuDate[1], 'YYYY-MM-DD');
      state.postData.hotelGoods = {
        [goodsId]: [startDateStr, endDateStr],
      };
    }
  },
  // 补充数据 postData数据
  // 诸如可以在 formatBuyData / formatAddCartData 后使用
  updatePostDataExtra(state, extra = {}) {
    state.postData = {
      ...state.postData,
      ...extra,
    };
  },

  postDataFinished(state) {
    state.skuConfig = {
      ...state.skuConfig,
      isAjaxing: false,
      submitType: '',
    };
    state.displayPop.skuPopShow = false;
  },

  setBookKey(state, payload) {
    const { bookKey } = payload;
    state.buyConfig.bookKey = bookKey || '';
  },

  setSelectedCalendar(state, payload) {
    state.priceCalendarData.selected = payload;
  },

  // 通过 displayPop 的key 来更新 displayPop
  updateDisplayPopByKey(state, key) {
    state.displayPop[key] = !state.displayPop[key];
  },

  clearSelectedCalendar(state) {
    state.priceCalendarData.selected = null;
    state.priceCalendarData.activeSkuId = state.skuExtraData.initialSkuId;
    /**
     * 清空下单的数据
     */
    state.skuExtraData.selectedSkuDate = [];
  },
};
