import Toast from '@vant/weapp/dist/toast/toast';
import { get, /* jumpLink, */ mapKeysToCamelCase, event } from '@goods/utils';
import api from '@goods/utils/api';
import {
  doSubmitLog,
  doErrorLog,
  formatBuyData,
  formatAddCartData,
} from '@goods/common/submit-helper';
import {
  SKU_SCENE,
  SKU_SCENE_BUY_SETS,
  SKU_SCENE_PREVIEW,
} from '@goods/common/config';
import { getPageHooks } from '@goods/extensions';
import { setLogger, getSkuLogData } from '@goods/common/logger';
import args from '@youzan/weapp-utils/lib/args';
import {
  PAGE_TYPE,
  navigateToRantaPage,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import { skuTradeUtilsPromise } from 'shared/utils/async-base-sku';

const app = getApp();

export default {
  stepperChange({ dispatch, commit, getters }, { detail: value = 1 }) {
    dispatch('skuCountChangeLogger', value);
    commit('setSkuExtraData', {
      stepperCount: value || 1,
    });

    setLogger({
      et: 'click', // 事件类型
      ei: 'click_sku_number', // 事件标识
      en: '点击sku弹层数量控件', // 事件名称
    });

    getters.showInstallmentBar && dispatch('calcInstallmentPrices', value);
  },

  skuCountChangeLogger({ state }, num) {
    const { id, title } = state.goods;
    const { skuExtraData, originSku } = state;
    const prevNum = skuExtraData?.stepperCount || 1;

    const params = {
      goods_id: id,
      goods_name: title,
      num,
      ...getSkuLogData({ skuExtraData, originSku }),
    };

    if (num > prevNum) {
      setLogger({
        et: 'click', // 事件类型
        ei: 'goods_detail_increase_goods_num', // 事件标识
        en: '商详增加商品数量', // 事件名称
        params,
      });
    } else if (num < prevNum) {
      setLogger({
        et: 'click', // 事件类型
        ei: 'goods_detail_decrease_goods_num', // 事件标识
        en: '商详减少商品数量', // 事件名称
        params,
      });
    }
  },

  openSkuImgPreview() {
    setLogger({
      et: 'click', // 事件类型
      ei: 'click_sku_pic', // 事件标识
      en: '点击sku弹层中图片', // 事件名称
    });
  },

  // 触发sku组件
  triggerSku({ state, getters, commit, dispatch }, btn = {}) {
    // 如预览商品不支持点击按钮购买
    if (getters.forbidClickBtnReason) {
      return dispatch('showCommonToast', getters.forbidClickBtnReason);
    }
    const { isHotel, isCatering } = state.goods;

    let skuType = 'normal';
    if (isHotel) {
      skuType = 'hotel';
    } else if (isCatering) {
      skuType = 'catering';
    } else if (getters.isPresale) {
      skuType = 'presale';
    }

    if (btn.skuScene && state.skuConfig.skuScene !== btn.skuScene) {
      commit('setSkuExtraData', {
        initialSkuId: 0, // 使用一个虚拟的数据方便统一取值
        selectedSku: {},
        // initialSku: selectedSku,
        selectedSkuTree: [],
        selectedSkuComb: null,
      });
    }

    // 先修改sku弹层的打开type
    commit('setSkuConfig', {
      skuType,
      ...btn,
    });

    return dispatch('fetchSkuData').then(() => {
      let next = Promise.resolve();
      // 分期购买，在显示sku前获取分期数据
      if (getters.isInstallment) {
        next = dispatch(
          'fetchInstallentPrice',
          state.skuExtraData.selectedSkuComb
        );
      }
      // 接口挂了也需要继续显示 sku
      next.catch(() => {}).then(() => dispatch('beforeShowSkuPop', btn));

      return Promise.resolve();
    });
  },

  beforeShowSkuPop({ getters, dispatch, commit }, btn) {
    const { skuDataForVant, startSaleNum, skuProperties } = getters;
    if (
      btn.skuScene === SKU_SCENE.ADD_CART &&
      // 商品sku为空
      skuDataForVant.noneSku &&
      // 商品属性为空
      skuProperties.length === 0 &&
      // 商品留言为空
      skuDataForVant.messages.length === 0 &&
      // 起售为1
      startSaleNum === 1
    ) {
      // 构建sku信息
      const event = {
        messages: {},
        cartMessages: {},
        selectedNum: 1,
        selectedSkuComb: {
          id: skuDataForVant.collectionId,
          price: skuDataForVant.price,
        },
      };

      return dispatch('handleAddCart', { detail: event });
    }

    commit('showSkuPop');
  },
  // 加入购物车 - 按钮点击事件
  handleAddCart({ state, dispatch }, { detail: event }) {
    const { isAjaxing } = state.skuConfig;
    // 防止重复提交
    if (isAjaxing) {
      return Toast('数据提交中，请勿重复提交。');
    }
    return dispatch('doCheckBeforeSubmit', {
      type: 'addCart',
      submitData: event,
    });
  },

  // 下单购买 - 按钮点击事件
  // 由于登录校验导致异步执行，所以需要通过type区分是下单还是加入购物车
  handleBuy({ state, getters, commit, dispatch }, { detail: event }) {
    const { isAjaxing, isAddCart, isMultiBtn, isAddWish, skuScene } =
      state.skuConfig;
    const { skuExtraData } = state;
    // 判断是否应该调用购物车方法
    if (isAddCart && !isMultiBtn) {
      dispatch('handleAddCart', { detail: event });
      return;
    }
    // sku场景同时有购物车和购买按钮时，确认不是购物车，就需要把标志位重置
    commit('setSkuConfig', {
      isAddCart: false,
    });

    if (getters.isPeriodbuy) {
      if (!getters.isPeriodbuyItemSelected) {
        return Toast('请选择周期购送达时间');
      }
    }

    if (getters.isLadderGroupOn && skuScene === SKU_SCENE.ACT_BUY) {
      if (!skuExtraData.selectedScale) {
        return Toast('请选择拼团类型');
      }
    }

    // 超过限购数，不触发后续行为
    if (getters.isOverQuotaLimit) {
      const { quota, quotaUsed } = getters;
      return Toast(`限购${quota}件，您已购买${quotaUsed}件。`);
    }
    // 防止重复提交
    if (isAjaxing) {
      return Toast('数据提交中，请勿重复提交。');
    }

    let type = 'buy';
    if (isAddWish) {
      type = 'addWish';
    }

    if (getters.isInstallment) {
      const { isFreeInterest } = getters;
      const { installmentRate, selectedInstallmentPeriod } = state.skuExtraData;
      event = {
        ...event,
        // 3期免息的特殊逻辑处理
        installmentRate:
          selectedInstallmentPeriod === 3 && isFreeInterest
            ? 0
            : installmentRate, // 费率
        selectedInstallmentPeriod, // 分期数
      };
    }

    // 多规格商品拼团下单
    const { pageSwitch } = state || {};
    const { close_none_sku_goods_jump: close } = pageSwitch;
    if (getters.skuBuyBtnTex == '确定' && !close) {
      const {
        goodsId,
        skuId,
        num,
        price,
        messages: message,
        selectedSkuValues: skuName,
      } = event;
      const { currentActivity } = getters;
      const { activityType } = state.activityInfo;
      const {
        ongoingGroup = [],
        joinNum: totalNum,
        activityId,
      } = currentActivity;
      const { groupAlias: activityAlias } = ongoingGroup[0];
      // 如果是多规格商品，需要弹出sku，之后选择，
      const goodsList = [
        {
          kdtId: getters.kdtId,
          goodsId,
          skuId,
          num,
          price: Number(price),
          message,
          skuName,
          bizTracePointExt: '',
          activityAlias,
          activityType,
          activityId: Number(activityId),
        },
      ];
      const extraData = {
        kdtId: getters.kdtId,
        isGroupon: true,
        activityAlias,
        createGroupon: false,
        activityType,
        activityId: Number(activityId),
        totalNum,
      };
      skuTradeUtilsPromise().then(({ goToBuy }) =>
        goToBuy({
          goodsList,
          extraData,
        })
      );
      return;
    }

    dispatch('doCheckBeforeSubmit', { type, submitData: event });
  },

  handleSkuSelect({ dispatch }, { detail: event }) {
    const selectedSkuData = mapKeysToCamelCase(event);
    dispatch('updateSelectedSku', selectedSkuData);
  },

  handlePropSelect({ dispatch }, { detail: event }) {
    const selectedSkuData = mapKeysToCamelCase(event);
    dispatch('updateSelectedProp', selectedSkuData);
  },

  // 下单/加入购物车前校验
  doCheckBeforeSubmit({ state, getters, commit, dispatch }, event = {}) {
    const { goods = {}, skuConfig = {}, skuExtraData = {} } = state;
    const {
      skuData: sku = {},
      isInSourcingFission,
      showNotEnoughCouponTip,
    } = getters;
    const { type: submitType, submitData } = event;

    commit('setSkuConfig', {
      isAjaxing: true,
      submitType,
    });

    // 日志埋点
    let logType = submitType;
    if (skuConfig.isGift) {
      logType = 'gift';
    }

    const { skuId, num, selectedSkuValues = [] } = submitData;
    const extraData = {
      no_sku: +sku.noneSku,
      sku_id: skuId,
      sku_name: selectedSkuValues,
      num,
      guarantee_on: state.yzGuarantee,
    };

    doSubmitLog(logType, {
      goods,
      sku,
      extraData,
    });

    // 内购裂变活动，校验内购劵是否充足
    if (isInSourcingFission && skuConfig.skuScene === SKU_SCENE.ACT_BUY) {
      if (showNotEnoughCouponTip > 0) {
        Toast('内购券不足，请分享活动或商品获取更多内购劵');
        commit('resetSkuAjaxState');
        return Promise.reject('内购券不足，请分享活动或商品获取更多内购劵');
      }
    }

    // 酒店商品
    if (goods.isHotel && skuExtraData.selectedSkuDate.length !== 2) {
      Toast('亲，请选择入住时间');
      commit('resetSkuAjaxState');
      return Promise.reject('亲，请选择入住时间');
    }

    // 电子卡券价格日历
    if (getters.isPriceCalendar && !skuExtraData.selectedSkuDate.length) {
      Toast('亲，请选择要购买的日期');
      commit('resetSkuAjaxState');
      return Promise.reject('亲，请选择要购买的日期');
    }
    // 特殊商品提供更新数据接口
    return dispatch('updatePostData', submitData).then((data) => {
      // 更新表单数据，涉及登录场景，需要先存储对应数据
      commit('updatePostData', data);
      // 小程序直接下一步
      return dispatch(submitType, submitData);
    });
  },

  // 待提交数据处理，可根据skuScene做数据填充
  updatePostData({ state, getters }, submitData) {
    // 这个函数可用于后续活动函数重写
    const skuScene = get(state, 'skuConfig.skuScene', '');
    const { skuExtraData } = state;
    // 使用活动购买需要带上activityId
    const activityInfo = {};
    let extra = {};
    if (skuScene === SKU_SCENE.ACT_BUY) {
      Object.assign(activityInfo, state.activityInfo);
      if (getters.isLadderGroupOn) {
        Object.assign(activityInfo, {
          scaleNum: skuExtraData.selectedScale,
        });
      }
    } else if (skuScene === SKU_SCENE.POINTS_BUY) {
      Object.assign(activityInfo, getters.pointsActivityInfo);
      if (getters.skuData.noneSku) {
        // 无sku积分商品价格有问题
        extra = {
          ...extra,
          pointsPrice: getters.skuData.pointsPrice,
          price: getters.skuData.price || 0,
        };
      }
    }
    // 数据修正，sku组件无sku价格会乘以100
    if (getters.skuData.noneSku) {
      extra = {
        ...extra,
        price: getters.skuData.price || 0,
      };
    }

    // 周期购
    if (getters.isPeriodbuy) {
      extra = {
        ...extra,
        deliverTime: getters.periodbuyActivity.deliverTimeId,
      };
    }

    // 分步极速下单需要添加额外参数, 给订单那边用。。。
    if (state.isFastBuySplit) {
      extra = {
        ...extra,
        isPartialFastAdOrder: true,
        expressTypeChoice: 0,
      };
    }

    // 阶梯团价格使用选中的阶梯价格
    if (getters.isLadderGroupOn && skuScene === SKU_SCENE.ACT_BUY) {
      extra = {
        ...extra,
        price: getters.skuPrice.buyPrice,
      };
    }

    // 内购裂变需要加入内购劵的信息
    if (getters.isInSourcingFission && skuScene === SKU_SCENE.ACT_BUY) {
      extra.isInSourcingFission = true;
      extra.needInSourcingCouponNum = getters.currentNeedCoupon;
      extra.umpType = 'InSourcingFission';
    }

    return {
      activityInfo,
      extra,
      ...submitData,
    };
  },

  // 用于特殊商品重写数据格式
  beforeBuy({ state, getters, dispatch }, postData = {}) {
    const { goodsList = [], common = {}, extra = {} } = postData;
    const { activityName, isGrouponSingleBuy } = getters;

    const hooks = getPageHooks();
    const goods = goodsList[0] || {};
    const query = args.getAll(state.goods.path);
    // 视频号来源
    const fromSource = query?.fromSource;
    fromSource && (goods.from_source = fromSource);
    // 交易组件开通流程的订单
    const TRADE_MODULE_ORDER = query?.TRADE_MODULE_ORDER;
    const OPEN_TRADE_MODULE_VERSION = query?.OPEN_TRADE_MODULE_VERSION;
    if (TRADE_MODULE_ORDER) {
      // 在订单扩展字段增加
      extra.BIZ_ORDER_ATTRIBUTE = {
        ...(extra.BIZ_ORDER_ATTRIBUTE || {}),
        TRADE_MODULE_ORDER,
      };
    }
    if (OPEN_TRADE_MODULE_VERSION) {
      // 在订单扩展字段增加
      extra.BIZ_ORDER_ATTRIBUTE = {
        ...(extra.BIZ_ORDER_ATTRIBUTE || {}),
        OPEN_TRADE_MODULE_VERSION,
      };
    }

    const param = {
      goodsList: JSON.stringify(goodsList),
      common: JSON.stringify(common),
      extra: JSON.stringify(extra),
    };

    function handleResult(params) {
      const { hasCouponActivity } = getters;
      if (hasCouponActivity && !isGrouponSingleBuy) {
        const { autoGetCouponId } = getters;

        if (autoGetCouponId) {
          return dispatch('getCoupon', {
            id: autoGetCouponId,
            source: 'goods_details_auto_take',
          })
            .then(() => {
              // 下单前告诉交易弹窗
              wx.setStorage({
                key: 'goods2trade',
                data: {
                  showCouponPop: true,
                  couponText: '已为你领取1张优惠券，下单享优惠',
                },
              });

              return params;
            })
            .catch(({ msg = '领取失败' }) => {
              wx.setStorage({
                key: 'goods2trade',
                data: { showCouponPop: true, couponText: msg },
              });

              return params;
            });
        }
      }

      return params;
    }

    if (hooks && hooks.beforeBuy) {
      const data = {
        messages: goods.messages,
        goodsAlias: state.goods.alias,
        skuId: goods.sku_id,
        num: goods.num,
        activityName,
      };

      return hooks.beforeBuy.call(data).then((ext = []) => {
        if (ext.length) {
          const { umpExt } = ext[0] || {};

          if (umpExt) {
            param.cloudExtension = {
              umpExt,
            };
          }
        }

        return handleResult(param);
      });
    }

    return handleResult(param);
  },

  afterBuy({ state, getters }, { common, goodsList, bookKey }) {
    const query = {};
    let url = `/packages/order/index?bookKey=${bookKey}`;
    query.bookKey = bookKey;
    if (common.orderFrom) {
      url += `&orderFrom=${common.orderFrom}`;
      query.orderFrom = common.orderFrom;
    }

    const hooks = getPageHooks();
    const { activityName } = getters;
    const goods = goodsList[0] || {};
    const data = {
      messages: goods.messages,
      goodsAlias: state.goods.alias,
      skuId: goods.sku_id,
      num: goods.num,
      activityName,
    };

    return hooks.afterBuy
      .call({
        ...data,
        url,
        bookKey,
      })
      .then(() => {
        // jumpLink(url);
        navigateToRantaPage({
          pageType: PAGE_TYPE.ORDER,
          query,
        });
        wx.hideLoading();
      });
  },

  // 真正的购买函数
  buy({ state, commit, dispatch }) {
    const { postData = {}, skuConfig = {}, goods } = state;
    const formatedData = formatBuyData(postData);
    const pages = getCurrentPages() || [];
    const currentPageOptions = (pages[pages.length - 1] || {}).options || {};
    if (currentPageOptions.paymentSuccessRedirect) {
      formatedData.common.payment_success_redirect =
        currentPageOptions.paymentSuccessRedirect;
    }

    if (skuConfig.isPoints) {
      setLogger({
        et: 'click',
        ei: 'exchange_goods_by_score',
        en: '积分兑换商品',
        params: {
          goods_id: goods.id,
          goods_name: goods.title,
          ...getSkuLogData({
            skuExtraData: state.skuExtraData,
            originSku: state.originSku,
          }),
        },
      });
    }

    dispatch('beforeBuy', formatedData)
      .then((data) => {
        return api
          .post({
            url: '/wsctrade/order/goodsBook.json',
            errorMsg: '购买失败，请重试',
            timeout: 15000,
            data,
          })
          .then((data) => {
            // 隐藏弹层，恢复状态，部分版本微信会缓存整页
            commit('postDataFinished');
            commit('setBookKey', data);
            return dispatch('afterBuy', { ...data, ...formatedData });
          })
          .catch((err = {}) => {
            !err.noToast && Toast(err.msg || err.message);
            doErrorLog(skuConfig.submitType);
            commit('postDataFinished');
          });
      })
      .catch((err) => {
        commit('postDataFinished');
        !err.noToast && Toast(err.msg || err.message);
      });
  },

  beforeAddCart({ state }, data) {
    const hooks = getPageHooks();

    return hooks.beforeCartSubmit.call({
      messages: state.postData.cartMessages,
      num: data.num,
      skuId: data.sku_id,
      goodsAlias: state.goods.alias,
    });
  },
  afterAddCart() {},

  // 真正的加入购物车
  addCart({ state, getters, commit, dispatch }, submitData) {
    const { selectedNum } = submitData;
    let { postData = {} } = state;
    const {
      activitySkuData: { goods: goodsSku },
      goods: { id, title },
    } = state;
    // 扫码优惠加入购物车需要带上activity信息，其他都不需要
    if (
      getters.discountActivity &&
      getters.discountActivity.type === 'goodsScan'
    ) {
      postData = {
        ...postData,
        ...state.activityInfo,
      };
    }
    const formatedData = formatAddCartData(postData);

    return dispatch('beforeAddCart', formatedData)
      .then(() => {
        // 后期返回值可以考虑混入 extData 中
        return api
          .post({
            url: '/wscshop/trade/cart/goods.json',
            header: {
              'content-type': 'application/x-www-form-urlencoded',
            },
            data: formatedData,
          })
          .then(() => dispatch('afterAddCart'))
          .then(({ showToast = true } = {}) => {
            showToast && Toast('已成功添加到购物车');
            if (app.getYouZanYunSdk) {
              const pages = getCurrentPages();
              const currentPage = pages[pages.length - 1];
              const url = `/${currentPage.route}`;
              app.getYouZanYunSdk().app.trigger('beforeAddCart', {
                skuId: goodsSku.skuId,
                title,
                id,
                selectedNum,
                pageUrl: url,
              });
            }
            // 触发购物车小按钮红点展示
            event.trigger('refreshCartCount');
            commit('postDataFinished');
          })
          .catch((err) => {
            Toast(err.msg || '网络错误，请稍后重试');
            doErrorLog('add-cart', 'addCart');
            commit('postDataFinished');
            return Promise.reject(err);
          });
      })
      .catch((res) => {
        doErrorLog('saas:before-add-cart', res);
        commit('postDataFinished');
        return Promise.reject({ ...res, message: res.message || res.msg });
      });
  },

  // 检查是否自动展示sku
  checkAutoShowSku({ state, dispatch }) {
    const {
      env: { isMobile, showSku },
      user: { isAdmin },
      display: { showForbidBuyBtn },
    } = state;
    if (showSku && !(isAdmin && !isMobile) && !showForbidBuyBtn) {
      dispatch('showDefaultSku');
    }
  },

  // 弹起默认的sku组件，根据按钮来确定
  showDefaultSku({ getters, dispatch }) {
    const { bigButtonsData = [], skuSceneByState, skuSceneByBtn } = getters;

    for (let i = bigButtonsData.length - 1; i >= 0; i--) {
      let btn = bigButtonsData[i] || {};

      // 根据商品状态处理 scene，未知则保留原先逻辑
      if (skuSceneByState) {
        btn = { ...btn, skuScene: skuSceneByState };
      }
      // 根据商祥按钮处理 scene，未知则保留原先逻辑
      if (skuSceneByBtn) {
        btn = { ...btn, skuScene: skuSceneByBtn };
      }

      if (
        // 可购买的sku集合
        SKU_SCENE_BUY_SETS.has(btn.skuScene) ||
        // 活动购买
        btn.skuScene === SKU_SCENE.ACT_BUY ||
        // 预览场景
        SKU_SCENE_PREVIEW.has(btn.skuScene)
      ) {
        setTimeout(() => {
          dispatch('triggerSku', btn);
        }, 0);
        break;
      }
    }
  },

  // 自定义sku按钮事件
  customBtnClick({ getters, dispatch }) {
    const { customSkuBtn } = getters;

    // 1. 下架 goods.isDisplay !== 1
    // 2. 售罄 isSellOut === true
    // 3. 预售 && 定金预售 && 定金预售已结束 isPresale && isDepositPresale && isDepositPresaleOver
    // 4. 商祥按钮 为 forbid 的其他情况

    dispatch('handleBigButtonClick', customSkuBtn);
  },
};
