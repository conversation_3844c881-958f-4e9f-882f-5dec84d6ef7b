import Toast from '@vant/weapp/dist/toast/toast';
// import uploadImage from '@/shared/common-api/img-upload';
import { get, formatPrice, or } from '@goods/utils';
import { getQuotaText, getOverLimitText } from '@goods/common/quota-utils';
import { parseSkuData } from '@goods/store/utils/helper';
import { SKU_SCENE, WX_SUBSCRIBE_SCENE } from '@goods/common/config';

export default {
  useSingleQuota(state) {
    const { quota = 0, singleQuota = 0, quotaUsed = 0 } = state.limit;
    // 没有单次限购数据
    if (!singleQuota) return false;
    // 如果商品不限购，但是单次限购
    if (!quota) return true;
    // 购买后限购数量大于单次购买数量，使用单次限购
    return quota - quotaUsed > singleQuota;
  },
  // 由于多人拼团、积分商品、秒杀包含两种限购状态
  // 电子卡券又有默认限购100件的隐藏逻辑
  // 在唤起sku时更新state.limit状态来完成
  quota(state, getters) {
    // 助力膨胀限购一个
    if (getters.isHelpDepositPresale) {
      return 1;
    }

    if (getters.useSingleQuota) {
      return state.limit.singleQuota;
    }

    const _quota = get(state, 'limit.quota', 0);
    return _quota ? Math.max(_quota, 1) : 0;
  },
  // 商品限购周期 0 终身 1 天 2 周 3 月
  quotaPeriod(state, getters) {
    const skuScene = get(state, 'skuConfig.skuScene', '');
    let activity = {};
    if (skuScene === SKU_SCENE.ACT_BUY) {
      activity = getters.currentActivity || {};
    } else if (skuScene === SKU_SCENE.POINTS_BUY) {
      activity = getters.pointsActivity || {};
    }
    if (activity.limit) {
      return activity.limit.quotaCycle || 0;
    }
    return state.goods.limit.quotaCycle || 0;
  },
  // 已购数量
  quotaUsed(state, getters) {
    // 使用单次限购的话，说明可购买数量一定大于单次购买限制，设置已购为 0
    if (getters.useSingleQuota) {
      return 0;
    }

    return state.limit.quotaUsed || 0;
  },

  // 多个活动商品存在不同的限购文案， 用一个通用的减少其他地方的判断
  quotaText(state, getters) {
    const {
      isHotel,
      limit: { singleQuota },
    } = state.goods;
    const { quota, quotaUsed, quotaPeriod, startSaleNum } = getters;
    const { isPoints = false } = state.skuConfig || {};
    return getQuotaText({
      quota,
      quotaUsed,
      quotaPeriod,
      isHotel,
      isPoints,
      singleQuota,
      startSaleNum,
    });
  },

  startSaleNum(state) {
    const skuScene = get(state, 'skuConfig.skuScene', '');
    if (skuScene === SKU_SCENE.POINTS_BUY) {
      return 1;
    }
    return state.goods.limit.startSaleNum;
  },

  // 是否超过购买限制了
  isOverQuotaLimit(state, getters) {
    const { quota, quotaUsed } = getters;
    return quotaUsed >= quota && quota !== 0;
  },

  handleOverLimit(state, getters) {
    const {
      isHotel,
      limit: { singleQuota },
    } = state.goods;
    const { quota, quotaPeriod, startSaleNum } = getters;
    const { isPoints = false } = state.skuConfig || {};

    return (data) => {
      const { action, limitType, quotaUsed } = data;

      const msg = getOverLimitText({
        action,
        limitType,
        quota,
        quotaUsed,
        quotaPeriod,
        isHotel,
        isPoints,
        startSaleNum,
        singleQuota,
      });
      if (msg) {
        Toast(msg);
      }
    };
  },

  skuBuyBtnText(state, getters) {
    if (state.skuExtraData?.payWay === 'PRIOR_USE') {
      const { submitButtonText } =
      state.payChannels?.find((itm) => itm.payChannel === 'PRIOR_USE') || {};
      if (submitButtonText) {
        return submitButtonText;
      }
    }
    // 虚拟商品不支持加入购物车
    const { isVirtual } = state.goods;
    const { hideShoppingCart } = state.shopConfig;
    const { isMultiBtn, isAddCart, isAjaxing, ajaxType } = state.skuConfig;
    const ajaxTypeText = {
      buy: '数据提交中',
      addWish: '提交中...',
    };

    if (isAjaxing && ajaxTypeText[ajaxType]) {
      return ajaxTypeText[ajaxType];
    }

    if (isMultiBtn) {
      return isVirtual || hideShoppingCart ? '下一步' : getters.bottomBuyBtnTxt;
    }
    // 快速凑团
    const { text } = state.skuConfig;
    if ( text == '快速凑团' ) {
      return '确定';
    }

    return isAddCart ? `加入${getters.cartText}` : '下一步';
  },

  // 是否触发了售罄sku
  isActiveSoldoutSku(state) {
    const { selectedSkuComb = null } = state.skuExtraData;
    if (selectedSkuComb && selectedSkuComb.stockNum === 0) {
      return true;
    }

    return false;
  },

  // 规格选择提示
  skuValidator(state, getters) {
    const tree = get(getters.skuData, 'tree', []);
    const propList = get(state, 'goods.itemSalePropList', []);
    const ksMap = tree.reduce((obj, tree) => {
      obj[tree.kS] = tree.k;
      return obj;
    }, {});
    const propMap = propList.reduce((acc, cur) => {
      acc[cur.kId] = cur.k;
      return acc;
    }, {});
    return (data) => {
      const { selectedSku, selectedProp = {} } = data;
      const unselectedSku = Object.keys(selectedSku || {})
        .filter((key) => !selectedSku[key])
        .map((it) => ksMap[it]);
      const unselectedProp = Object.keys(propMap)
        .filter((key) => !selectedProp[key] || selectedProp[key].length < 1)
        .map((it) => propMap[it]);
      const all = unselectedSku.concat(unselectedProp);
      return all.length > 0 ? `请选择${all.join(' 和 ')}` : '';
    };
  },

  skuStepperConfig(state, getters) {
    const { goods, priceCalendarData } = state;
    const { quotaText, handleOverLimit } = getters;
    const config = {
      quotaText,
      handleOverLimit,
    };
    if (goods.isHotel) {
      config.stockFormatter = (stock) => `剩余${stock}间`;
    }
    // 价格日历库存设置
    if (priceCalendarData && priceCalendarData.selected) {
      config.stockNum = priceCalendarData.selected.stockNum;
    }
    return config;
  },

  // vant使用的sku数据，需要转数据格式
  skuDataForVant(state, getters) {
    const skuData = parseSkuData({
      ...getters.skuData,
    });

    if (skuData.tree && skuData.tree.length > 0) {
      // 处理默认图片
      skuData.tree[0].v.forEach((element) => {
        if (element.defaultSkuImg) {
          delete element.img_url;
          delete element.imgUrl;
        }
      });
    }

    return skuData;
  },

  skuProperties(state) {
    const itemSalePropList = get(state, 'goods.itemSalePropList', []);
    return itemSalePropList;
  },

  skuPrice(state, getters) {
    let { price, oldPrice } = state.skuPrice;
    let buyPrice = '';
    const { priceCalendarData, skuExtraData } = state;
    const { selectedSkuComb = null, selectedScale } = skuExtraData;
    // const { isPoints = false } = skuConfig;
    if (getters.isPriceCalendar && selectedSkuComb) {
      const { selected } = priceCalendarData;

      if (selected) {
        price = formatPrice(or(selected.activityPrice, selected.originPrice));
        oldPrice = formatPrice(selected.originPrice);

        return {
          price,
          oldPrice,
          buyPrice,
        };
      }
    }

    if (selectedSkuComb) {
      buyPrice = selectedSkuComb.price;
    } else {
      buyPrice = getters.skuData.price;
    }

    const { skuScene } = state.skuConfig;
    if (getters.isLadderGroupOn && SKU_SCENE.ACT_BUY === skuScene) {
      const ladderItem = getters.ladderGrouponSkuExtraList
        .filter((item) => item.scale === selectedScale)
        .pop();

      if (ladderItem) {
        return {
          oldPrice,
          price: ladderItem.price,
          buyPrice: ladderItem.skuPrice,
        };
      }

      return {
        oldPrice,
        buyPrice: getters.ladderMinPrice.price,
        price: getters.ladderMinPrice.priceString,
      };
    }

    return {
      price,
      oldPrice,
      buyPrice,
    };
  },

  // 可以跟任何sku叠加的tag
  commonSkuPriceTags(state, getters) {
    const tags = [];
    // SKU不展示周期购图标
    // if (getters.isPeriodbuy) {
    //   tags.push('周期购');
    // }
    if (getters.isDepositPresale && !getters.isDepositPresaleOver) {
      // 定金预售
      tags.push(getters.presaleSkuPriceTag);
    } else if (getters.isPresale && !getters.isDepositPresale) {
      tags.push('全款预售');
    }
    return tags;
  },

  seletecdPreferentialCoupon(state, getters) {
    const { selectedSkuComb = null } = state.skuExtraData;
    const { couponActivity, hasCouponActivity } = getters;

    const id = (selectedSkuComb && selectedSkuComb.id) || '';
    if (couponActivity && hasCouponActivity && id) {
      const { skuCouponPrice = {} } = couponActivity;
      const targetCoupon = skuCouponPrice[id];
      if (targetCoupon) {
        return targetCoupon;
      }
    }

    return null;
  },

  // sku最佳券提示
  skuPreferentialCoupon(state, getters) {
    const {
      seletecdPreferentialCoupon,
      defaultOptimalCoupon,
      isGrouponSingleBuy,
    } = getters;
    if (isGrouponSingleBuy) {
      return null;
    }

    return (
      (seletecdPreferentialCoupon &&
        seletecdPreferentialCoupon.preferentialCoupon) ||
      defaultOptimalCoupon
    );
  },

  // 券后价标签
  skuPreferentialPriceTag(state, getters) {
    // 拼团非活动购买隐藏券后价
    if (getters.isGrouponSingleBuy) {
      return null;
    }

    const { seletecdPreferentialCoupon, preferentialPriceTag } = getters;
    return (
      (seletecdPreferentialCoupon &&
        seletecdPreferentialCoupon.preferentialPriceTag) ||
      preferentialPriceTag
    );
  },
  // 普通购买或者会员购买的标
  // 即skuScene为actBuy之外的场景
  normalSkuPriceTags(state, getters) {
    const tags = [];
    const { skuConfig, skuExtraData } = state;

    if (skuConfig.isGift) {
      tags.push('送礼');
    }

    const { selectedSkuComb = null } = skuExtraData;
    if (selectedSkuComb) {
      const priceTitle = selectedSkuComb.priceTitle;
      if (priceTitle) {
        // 如果限定显示活动标，按照限定显示
        tags.push(priceTitle);
        return tags;
      }
      if (selectedSkuComb.notShowPriceTitle) {
        // 如果限定不显示活动标，则不显示
        return [];
      }
    }

    // 限时折扣和会员折扣互斥
    if (
      getters.isTimelimitedDiscount &&
      !getters.isTimelimitedDiscountNotStart
    ) {
      const timelimited = getters.timelimitedDiscountActivity; // 限时折扣需要显示活动栏
      if (timelimited.priceTitle) {
        tags.push(timelimited.priceTitle);
      } else {
        // let showTag = '限时';
        // showTag += timelimited.discountType === 2 ? timelimited.discountValue / 10 + '折' : '减价';
        // tags.push(showTag);
        tags.push('限时折扣');
      }
    }

    // 会员折扣
    if (getters.isVipTag) {
      tags.push('会员价');
    }
    return tags;
  },

  // 活动购买的标，即skuScene为actBuy场景
  activitySkuPriceTags(state, getters) {
    // 在活动中重写
    const tags = [];
    // 这里主要用于处理扫码优惠
    const discount = getters.discountActivity || {};
    if (discount.title) {
      tags.push(discount.title);
    }
    return tags;
  },

  skuPriceTags(state, getters) {
    const { skuScene } = state.skuConfig;
    if (skuScene === SKU_SCENE.ACT_BUY) {
      return [...getters.commonSkuPriceTags, ...getters.activitySkuPriceTags];
    }
    if (skuScene === SKU_SCENE.POINTS_BUY) {
      return ['积分兑换'];
    }
    return [...getters.commonSkuPriceTags, ...getters.normalSkuPriceTags];
  },

  showladderGrouponExtra(state, getters) {
    return (
      getters.isLadderGroupOn && state.skuConfig.skuScene === SKU_SCENE.ACT_BUY
    );
  },

  // 显示sku付费优惠券模块
  showSkuCouponExtra(state, getters) {
    return getters.skuCouponExtraItem;
  },

  hasPayChannels(state){
    return state.payChannels?.length;
  },

  priorUse(state){
    return state.payChannels?.find((itm) => itm.payChannel === 'PRIOR_USE');
  },

  stepperTitle() {
    return '购买数量';
  },

  hideSkuStock(state, getters) {
    const isHotel = get(state, 'goods.isHotel', false);
    const { skuData = {} } = getters;
    // 酒店商品日期未选中时不展示库存
    if (isHotel) {
      const hotelSelectedDate = get(state, 'skuConfig.selectedSkuDate', []);
      if (hotelSelectedDate.length !== 2) {
        return true;
      }
    }
    // 周期购不显示库存
    if (getters.isPeriodbuy) {
      return true;
    }
    return skuData.hideStock;
  },

  // 隐藏sku stepper
  hideSkuStepper(state) {
    // 心愿单需要隐藏
    return state.skuConfig.isAddWish;
  },

  // 是否隐藏sku 原价
  hideSkuOriginPrice() {
    return false;
  },

  showSkuAddCartBtn(state, getters) {
    const { isVirtual } = state.goods;
    const { hideShoppingCart } = state.shopConfig;
    const { isMultiBtn } = state.skuConfig;

    if (
      isVirtual ||
      getters.isPeriodbuy ||
      hideShoppingCart ||
      getters.isActiveSoldoutSku
    ) {
      return false;
    }

    // 有两个按钮或者只显示加入购物车
    return isMultiBtn;
  },

  // 降价拍等只能买一次
  quantityReadOnly() {
    return false;
  },

  calendarDateData(state) {
    const { priceCalendarData } = state;
    const selectedSkuId = priceCalendarData.activeSkuId;

    return priceCalendarData[selectedSkuId] || {};
  },
  // 是否展示分期支付
  showInstallmentBar(state, getters) {
    return (
      getters.isInstallment &&
      (!state.skuConfig.isAddCart || state.skuConfig.isMultiBtn)
    );
  },

  canPriorUse(state,getters){
    const result = getters.sumPrice <= getters.priorUse?.price?.max[0] && getters.skuPrice?.price > 0
        && getters.skuBarInfo?.isAllSelected;
    if(!result&&state.skuExtraData.payWay === 'PRIOR_USE'){
      state.skuExtraData.payWay = ''
    }
    return result;
  },

  sumPrice(state,getters){
    return (getters.skuPrice?.buyPrice||1) * (state.skuExtraData.stepperCount || 1)
  },

  // 是否自定义sku组件底部按钮
  isCustomSkuBtn(state, getters) {
    const { goods, goodsActivity } = state;

    // 补货提醒
    if (getters.stockReminderSkuBtnText) {
      return true;
    }

    // 1.下架
    if (goods.isDisplay === 0) {
      return true;
    }

    // 2. 售罄 && 非赠品
    if (getters.isSellOut && !getters.isPresent) {
      return true;
    }

    // 3. 预售 && 商品未上架（自定义上架时间）
    if (getters.isPresale && goodsActivity.waitToSold) {
      return true;
    }

    // 4. 预售 && 定金预售 && 定金预售已结束
    if (
      getters.isPresale &&
      getters.isDepositPresale &&
      getters.isDepositPresaleOver
    ) {
      return true;
    }

    return false;
  },

  /**
   * 自定义按钮
   * 1. 预售，即将开售按钮
   * 2. 售罄，去店铺主页按钮
   * 3. 补货提醒，订阅消息
   */
  customSkuBtn(state, getters) {
    const {
      commonBigButtons,
      stockReminderSkuBtnText,
      skuStockRemindStatus,
    } = getters;

    // 补货提醒
    if (stockReminderSkuBtnText) {
      return {
        text: stockReminderSkuBtnText,
        disabled: skuStockRemindStatus,
        skuScene: SKU_SCENE.STOCK_REMINDER,
        subscribeSceneV2: skuStockRemindStatus
          ? undefined
          : WX_SUBSCRIBE_SCENE.STOCK_REMINDER,
        imgUrl:
          'https://img01.yzcdn.cn/upload_files/2021/03/01/Fl_DS6WctFpQ5qGy31F_KSLHuTU9.png',
        disableClassName: 'remind__btn-disabled',
      };
    }

    return commonBigButtons.length > 0 ? commonBigButtons[0] : {};
  },

  // 根据当前商品状态，获取skuScene
  skuSceneByState(state, getters) {
    const { goods } = state;
    const { isPresale, isSellOut, isPresent } = getters;

    // 1.下架
    if (goods.isDisplay !== 1) {
      return SKU_SCENE.OFF_SHELVES;
    }
    // 1. 预售
    if (isPresale) {
      return SKU_SCENE.PRESELL;
    }
    // 2. 售罄 && 非赠品
    if (isSellOut && !isPresent) {
      return SKU_SCENE.SELL_OUT;
    }

    // 未知 scene 场景，默认使用按钮原先的 skuScene
    return null;
  },
  // 根据当前商祥按钮，获取skuScene
  skuSceneByBtn(state, getters) {
    const { bigButtonsData = [] } = getters;

    const hasAddCart = bigButtonsData.find(
      (btn) => btn.skuScene === SKU_SCENE.ADD_CART
    );
    const hasNormalBuy = bigButtonsData.find(
      (btn) => btn.skuScene === SKU_SCENE.NORMAL_BUY
    );

    if (hasAddCart) {
      if (hasNormalBuy) {
        return SKU_SCENE.SEL_SKU;
      }
      return SKU_SCENE.ADD_CART;
    }
    if (hasNormalBuy) {
      return SKU_SCENE.NORMAL_BUY;
    }

    // 未知 scene 场景，默认使用按钮原先的 skuScene
    return null;
  },

  // 下单活动名
  activityName(state, getters) {
    const { skuScene } = state.skuConfig;
    // 助力定金膨胀
    if (skuScene === SKU_SCENE.ACT_BUY && getters.isHelpDepositPresale) {
      const { type } = getters.currentActivity.helpDepositExpansion || {};
      return type;
    }
    // 拼团、砍价等
    if (skuScene === SKU_SCENE.ACT_BUY) {
      const { type } = getters.currentActivity || {};
      return type;
    }
    // 积分兑换
    if (skuScene === SKU_SCENE.POINTS_BUY) {
      const { type } = getters.pointsActivity || {};
      return type;
    }

    // 送礼
    if (skuScene === SKU_SCENE.GIFT_BUY) {
      return 'gift';
    }

    return 'goods';
  },

  // 内购裂变需要内购劵数量，会在 in-sourcing-fission 页面重写
  // -1 不显示，0 显示占位
  showNotEnoughCouponTip() {
    return -1;
  },
};
