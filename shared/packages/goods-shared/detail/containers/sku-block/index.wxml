<template name="custom-subscribe-btn">
  <subscribe-message-v2
    bind:next="customBtnClick"
    bind:success="handleWxSubscribeSuccess"
    bind:fail="handleWxSubScribeFail"
    subscribe-scene="{{ btn.subscribeSceneV2 }}"
    data-btn="{{ btn }}"
    img-url="{{ btn.imgUrl }}"
  >
    <theme-view
      bg="main-bg"
      color="main-text"
      custom-class="sku-action__btn"
    >
      <view>{{ btn.text }}</view>
    </theme-view>
  </subscribe-message-v2>
</template>

<base-sku
  wx:if="{{ inited }}"
  show="{{ displayPop.skuPopShow }}"
  sku="{{ skuDataForVant }}"
  goods="{{ goods }}"
  quota="{{ quota }}"
  quota-used="{{ quotaUsed }}"
  quota-text="{{ quotaText }}"
  quota-cycle="{{ quotaPeriod }}"
  buy-text="{{ skuBuyBtnText }}"
  calendar-stock="{{ calendarStock }}"
  add-cart-text="{{ addCartText }}"
  start-sale-num="{{ startSaleNum }}"
  custom-sku-validator="{{ skuValidator }}"
  custom-stepper-config="{{ skuStepperConfig }}"
  properties="{{ skuProperties }}"
  stepper-title="{{ stepperTitle }}"
  hide-stock="{{ hideSkuStock }}"
  disable-stepper-input="{{ hideSkuStepper }}"
  disable-soldout-sku="{{ disableSoldoutSku }}"
  show-add-cart-btn="{{ showSkuAddCartBtn }}"
  is-quantity-read-only="{{ quantityReadOnly }}"
  extra-data="{{ { useCustomHeaderPrice: true, useCustomSkuActions: isCustomSkuBtn } }}"
  reset-stepper-on-hide
  close-on-click-overlay
  class="sku-block"
  bind:open-preview="openSkuImgPreview"
  bind:add-cart="handleAddCart"
  bind:buy="handleBuy"
  bind:sku-selected="handleSkuSelect"
  bind:sku-prop-selected="handlePropSelect"
  bind:sku-close="hideSkuPop"
  bind:stepper-change="stepperChange"
  generic:sku-header-price="sku-header-price"
>
  <!-- 酒店sku加入日期选择条 -->
  <!-- <van-cell
    v-if="isHotel"
    slot="sku-body-top"
    :title="datePickerBarText"
    @click="$dispatch('showDatePicker')"
  /> -->
  <!-- 规格扩展 -->
  <view
    wx:if="{{ showExtraSkuGroup }}"
    slot="extra-sku-group"
    class="sku-block__extra-group van-hairline--bottom"
  >
    <!-- 阶梯拼团-->
    <ladder-groupon-extra wx:if="{{ showladderGrouponExtra }}" />
    <!-- 周期购 -->
    <period-buy-sku-extra wx:if="{{ isPeriodbuy }}" />
    <!-- 电子卡券 -->
    <ecard-sku-extra wx:if="{{ isVirtualTicket }}" />
    <!-- 分期支付 -->
<!--    <installment wx:if="{{ showInstallmentBar }}" />-->
    <!--支付方式-->
    <payways wx:if="{{ hasPayChannels }}"/>
    <!-- 付费优惠券 -->
    <coupon-sku-extra wx:if="{{ showSkuCouponExtra }}" />
  </view>

  <block wx:if="{{ skuPreferentialCoupon || showNotEnoughCouponTip > -1 || skuExtraData.payWay==='PRIOR_USE' }}">
    <!-- 先用后付 -->
    <view class="sku-block__actions-tip" wx:if="{{ skuExtraData.payWay === 'PRIOR_USE' }}" slot="sku-actions-top">
      {{ priorUse.submitNotice }}
      <img alt="" src="https://b.yzcdn.cn/wsc-h5-goods/right.png"/>
    </view>
    <!-- 最佳券提示 -->
    <view class="sku-block__actions-tip" wx:elif="{{ skuPreferentialCoupon }}" slot="sku-actions-top">
      当前商品可使用
      <theme-view color="general" inner-style="display: inline;">
        {{ skuPreferentialCoupon }}
      </theme-view>
      优惠券
    </view>
    <view class="sku-block__actions-tip {{ showNotEnoughCouponTip > 0 ? '' : 'hide' }}" wx:else slot="sku-actions-top">
      当前商品需要使用
      <theme-view color="general" inner-style="display: inline;">
        {{ showNotEnoughCouponTip }}
      </theme-view>
      内购劵
    </view>
  </block>


  <view wx:if="{{ isCustomSkuBtn }}" slot="sku-actions">
    <view class="van-sku-actions">
      <template
        wx:if="{{ customSkuBtn.subscribeSceneV2 }}"
        is="custom-subscribe-btn"
        data="{{ btn: customSkuBtn }}"
      >
      </template>

      <theme-view
        wx:else
        bg="main-bg"
        color="main-text"
        bind:tap="customBtnClick"
        custom-class="sku-action__btn"
      >
        <view wx:if="{{ customSkuBtn.disableClassName }}" class="{{ customSkuBtn.disableClassName }}">{{ customSkuBtn.text }}</view>
        <view wx:else class="{{ customSkuBtn.disabled ? 'disabled' : '' }}">{{ customSkuBtn.text }}</view>
      </theme-view>
    </view>
  </view>
</base-sku>
