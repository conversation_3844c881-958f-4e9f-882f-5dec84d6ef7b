@import '~mixins/index.scss';

.coupon-sku-extra {
  padding: 10px 16px;
  background: $white;

  &__main {
    padding: 4px 8px 12px;
    font-size: 12px;
    color: $gray-dark;
    line-height: 16px;
    font-family: PingFangSC-Regular;
    border-radius: 4px;
    background: $background-color;
  }

  &__title {
    margin-top: 8px;
    color: $text-color;
  }

  &__desc {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__desc-text {
    flex: 1;

    @include ellipsis;
  }

  &__state--fold {
    @include multi-ellipsis(2);
  }

  &__handle-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 75px;
    height: 24px;
    margin: 8px auto 0;
    padding-left: 2px;
    box-sizing: border-box;
    border: 1px solid $gray-light;
    border-radius: 18px;
    color: $text-color;
  }
}
