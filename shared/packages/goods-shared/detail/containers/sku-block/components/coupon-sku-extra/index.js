import { GoodsComponent } from '@goods/common/vanx-component';
import navigate from 'shared/utils/navigate';
import { mapState } from '@youzan/vanx';

const FOLD_HEIGHT = 32; // 优惠说明可折叠的高度临界值

GoodsComponent({
  name: 'coupon-sku-intro',

  getters: ['skuCouponExtraItem'],

  data: {
    isFolded: true, // 是否被折叠起来
    showFoldBtn: false, // 是否显示折叠按钮
  },

  mapData: mapState({
    instruction(state, getters) {
      const { skuCouponExtraItem = {} } = getters;
      if (skuCouponExtraItem && skuCouponExtraItem.description) {
        this.checkStateBoxHeight();
        return skuCouponExtraItem.description;
      }
      return '';
    },
    isAllGoodsAvailable(state, getters) {
      const { preferentialMode, applicableOnlineGoodsRangeType } = getters.skuCouponExtraItem || {};
      return preferentialMode !== 3 && applicableOnlineGoodsRangeType <= 1;
    },
  }),

  methods: {
    toggleFold() {
      this.setYZData({
        isFolded: !this.data.isFolded
      });
    },

    // 跳转优惠可用商品页
    jumpAvailableGoods() {
      if (this.data.isAllGoodsAvailable) return;
      const { couponId, alias = '' } = this.data.skuCouponExtraItem;
      navigate.navigate({
        url: `/packages/shop/goods/group/index?pageType=coupon&group_id=${couponId}&alias=${alias}`
      });
    },

    // 检查使用说明是否超过两行进行折叠
    checkStateBoxHeight() {
      const query = this.createSelectorQuery();
      setTimeout(() => {
        query.select('#instruction').boundingClientRect(rect => {
          if (rect && rect.height > FOLD_HEIGHT) {
            this.setYZData({
              showFoldBtn: true,
              isFolded: true, // 切换sku默认折叠
            });
          } else {
            this.setYZData({
              showFoldBtn: false
            });
          }
        }).exec();
      }, 50);
    }
  },
});
