<view class="coupon-sku-extra">
  <view class="coupon-sku-extra__main">
    <!-- 使用时间 -->
    <view class="coupon-sku-extra__time">
      <view class="coupon-sku-extra__title">使用时间</view>
      <view>{{ skuCouponExtraItem.validTimeCopywriting }}</view>
    </view>

    <!-- 优惠内容 -->
    <view class="coupon-sku-extra__discount">
      <view class="coupon-sku-extra__title">优惠内容</view>
      <view class="coupon-sku-extra__desc" bindtap="jumpAvailableGoods">
        <text class="coupon-sku-extra__desc-text">
          {{ skuCouponExtraItem.preferentialCopywritingDetail }}
        </text>
        <van-icon
          wx:if="{{ !isAllGoodsAvailable }}"
          name="arrow"
          color="#979797"
          size="16px"
        />
      </view>
    </view>

    <!-- 使用说明 -->
    <view wx:if="{{ instruction }}" class="coupon-sku-extra__state">
      <view class="coupon-sku-extra__title">使用说明</view>
      <view
        id="instruction"
        class="{{ showFoldBtn && isFolded ? 'coupon-sku-extra__state--fold' : ''}}"
      >
        {{ instruction }}
      </view>
    </view>

    <!-- 展开收起按钮 -->
    <view
      wx:if="{{ showFoldBtn }}"
      class="coupon-sku-extra__handle-btn"
      bindtap="toggleFold"
    >
      {{ isFolded ? '展开' : '收起' }}全部
      <van-icon
        name="{{ isFolded ? 'arrow-down' : 'arrow-up' }}"
        color="#979797"
        size="14px"
      />
    </view>
  </view>
</view>