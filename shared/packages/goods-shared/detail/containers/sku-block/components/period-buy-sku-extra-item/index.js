import { GoodsComponent } from '@goods/common/vanx-component';

GoodsComponent({
  name: 'PeriodBuySkuExtraItem',

  props: {
    id: [String, Number],
    name: String,
    selectedId: [String, Number]
  },

  computed: {
    isChoosed() {
      return this.id === this.selectedId;
    }
  },

  methods: {
    onSelect() {
      const id = this.isChoosed ? '' : this.id;
      this.$dispatch('selectPeriodbuySkuItem', id);
    }
  }
});
