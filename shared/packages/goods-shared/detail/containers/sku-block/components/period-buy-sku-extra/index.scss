@import '~mixins/index.scss';
@import "~shared/common/components/base-sku/sku-row-item/index.scss";

.period-extra-desc {
  display: inline;
  margin-left: 8px;
  vertical-align: middle;
}


.van-sku-group-container {
  margin: 0 16px;
  padding: 12px 0 2px;
}

.period-buy-items {
  display: flex;
  flex-wrap: wrap;
}

.van-sku-row {
  margin: 0 15px 10px 0
}

.van-sku-row:last-child {
  margin-bottom: 0
}

.van-sku-row__title {
  font-size: 14px;
  padding-bottom: 10px
}

.van-sku-row__extra-text {
  display: inline-block;
  color: #f44;
  font-size: 12px;
}

.van-sku-row__body {
  display: flex;
  flex-wrap: wrap;
}