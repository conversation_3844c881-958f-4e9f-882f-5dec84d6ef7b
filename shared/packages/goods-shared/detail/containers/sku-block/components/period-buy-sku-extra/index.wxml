<view class="van-sku-group-container">
  <view class="van-sku-row">
    <view class="van-sku-row__title">
      送达时间：
      <theme-view
        color="general"
        custom-class="period-extra-desc"
      >
        {{ periodbuyDeliverTimeStr }}
      </text>
    </view>
    <view class="period-buy-items">
      <theme-view
        wx:for="{{ periodbuySkuExtraList }}"
        wx:key="{{ item.id }}"
        data-id="{{ item.id }}"
        custom-class="van-sku-row__item"
        bg="{{ periodbuyActivity.deliverTimeId === item.id ? 'main-bg' : '' }}"
        color="{{ periodbuyActivity.deliverTimeId === item.id ? 'general' : '' }}"
        bindtap="selectSkuItem"
        opacity="0.1"
      >
        <view class="van-sku-row__item-name">{{ item.name }}</view>
      </theme-view>
    </view>
  </view>
</view>