import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions } from '@youzan/vanx';

GoodsComponent({
  name: 'PeriodBuySkuExtra',

  getters: [
    'periodbuyActivity',
    'periodbuySkuExtraList',
    'periodbuyDeliverTimeStr',
  ],

  methods: {
    ...mapActions(['selectPeriodbuySkuItem']),
    selectSkuItem(e) {
      let id = e.currentTarget.dataset.id;
      id = this.data.periodbuyActivity.deliverTimeId === id ? '' : id;
      this.selectPeriodbuySkuItem(id);
    }
  }
});
