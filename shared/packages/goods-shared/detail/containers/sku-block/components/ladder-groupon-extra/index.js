import { GoodsComponent } from '@goods/common/vanx-component';
import { mapMutations } from '@youzan/vanx';

GoodsComponent({
  name: 'ladder-groupon-extra',

  getters: ['ladderGrouponSkuExtraList'],

  state: ['skuExtraData'],

  methods: {
    ...mapMutations(['setSkuExtraData']),
    selectSkuItem({ currentTarget }) {
      const { selectedScale } = this.data.skuExtraData;
      const { scale } = currentTarget.dataset;
      this.setSkuExtraData({ selectedScale: selectedScale === scale ? null : scale });
    },
  },
});
