<view class="goods-sku__price">
  <theme-view
    wx:if="{{ isDepositPresale }}"
    color="general"
    class="goods-sku__price-prefix"
  >定金</theme-view>
  <theme-view
    color="general"
    class="goods-sku__price-current-view"
    custom-class="goods-sku__price-current"
  >
    <text wx:if="{{ !skuConfig.isPoints }}" class="goods-sku__price-symbol">￥</text>
    <text
      style="font-size: {{ (skuPrice.price && skuPrice.price.length > 18) ? '15px' : '18px' }}"
      class="goods-sku__price-num"
    >{{ skuPrice.price }}</text>
  </theme-view>

  <!-- 会员折扣标签 -->
  <block
    wx:for="{{ skuPriceTags }}"
    wx:for-item="tag"
    wx:key="tag"
  >
    <common-tag
      wx:if="{{ !hideOriginPriceTag }}"
      class="goods-sku__price-tag"
    >
      <text>{{ tag }}</text>
    </common-tag>
  </block>

  <!-- 券后价标签展示 -->
  <coupon-tag
    class="goods-sku__price-coupon-tag"
    wx:if="{{ skuPreferentialPriceTag }}"
    price-tag="{{ skuPreferentialPriceTag }}"
    prefix="{{ activityPreferentialPrefix }}"
  />

</view>
