@import '~mixins/index.scss';

.goods-sku__price {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  margin-right: -8px 16px 0 0;
  padding-right: 25px;

  &-current-view,
  &-prefix,
  &-tag {
    margin-top: 8px;
  }

  &-coupon-tag {
    margin-top: 4px;
  }

  &-current {
    display: flex;
    line-height: 1;
    font-size: 14px;
    color: $gray-darker;
    align-items: baseline;
  }

  &-symbol {
    top: -2px;
    font-size: 16px;
    vertical-align: middle;
    font-weight: bolder;
  }

  &-prefix {
    display: inline;
    margin-right: 2px;
    font-size: 12px;
    vertical-align: bottom;
    position: relative;
    top: 1px;
  }

  &-tag-vip {
    display: inline-block;
    width: 50px;
    height: 16px;
    background: url(https://img01.yzcdn.cn/public_files/f2a7689f3fe84adb04aa43c68b7e9d2d.png)
      no-repeat;
    background-size: cover;
    position: relative;
  }

  &-num {
    font-size: 18px;
    font-weight: bolder;
    vertical-align: baseline;
    white-space: nowrap;
    margin-right: 8px;
  }

  &-tag {
    display: inline-flex;
    align-items: center;
    margin-right: 8px;
    align-self: center;
    vertical-align: middle;
    line-height: 1.3;
    top: -2px;
  }
}
