@import '~mixins/index.scss';

.payways-list{
  margin: 0 -6px;
}

.installment-title {
  font-size: 14px;
  color: #111;
  margin-bottom: 6px;

  .tip {
    line-height: 16px;
    color: #bbb;
    margin-top: 8px;
    font-size: 12px;
  }
}

.sku-payways {
  padding: 12px 0 6px;
}

.payways-title {
  font-size: 14px;
  color: $text-color;
  margin-bottom: 6px;
  display: block;
  padding:12px 15px;
  span{
    font-size: 12px;
    color:$gray-dark;
  }
}

.payways-type {
  padding:10px 15px;
  .prior_use{
    background-color: #F7F8FA;
    width: 66px;
    height: 32px;
    line-height: 32px;
    color:$text-color;
    font-size: 13px;
    border-radius: 3px;
    display: inline-block;
    text-align:center;
    margin-right: 12px;
    position: relative;
    &.on{
      background-color: rgba(227,20,54,0.1);
      color:#EE0A24;
    }
    &.off{
      color:#C8C9CC;
      background-color: #F7F8FA;
      image{
        display: none !important;
      }
    }
    image {
      height: 12px;
      width: 44px;
      position: absolute;
      right: -6px;
      top: -6px;
    }
  }
}

.payways-item {
  font-size: 13px;
  color: #323233;
  padding: 6px;
  width: 50%;
  display: inline-block;
  box-sizing: border-box;

  &__inner {
    background: #f7f8fa;
    border-radius: 4px;
    padding: 6px 12px;
    line-height: 16px;
    box-sizing: border-box;
  }

  &.disabled {
    color: #c8c9cc;
    pointer-events: none;
    border: none;

    &__inner {
      background: #f7f8fa;
    }
  }
}