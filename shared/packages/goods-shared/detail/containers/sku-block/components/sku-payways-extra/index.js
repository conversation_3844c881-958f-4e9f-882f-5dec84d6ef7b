import { GoodsComponent } from '@goods/common/vanx-component';

GoodsComponent({
  name: 'payways',

  state: ['skuExtraData', 'skuManager','payChannels'],

  getters: ['isFreeInterest','getStepperCount','skuPrice','sumPrice','canPriorUse','priorUse','skuBarInfo'],

  methods:{
    selectPriorUse(){
        const {priorUse,sumPrice,skuBarInfo} = this.$store.getters;
        if(!skuBarInfo.isAllSelected){
              return wx.showToast({
                title: "请选择"+skuBarInfo.showText.split('；')[0],
                duration: 2000,
                icon: 'none'
              });
        }
        const {min,max} = priorUse?.price||{}
        if(sumPrice > max[0]){
         return wx.showToast({
            title: max[1],
             icon: 'none',
            duration: 2000
          });
        }else if(sumPrice < min[0]){
          return wx.showToast({
            title: max[1],
            icon: 'none',
            duration: 2000
          });
        }
      let type = 'PRIOR_USE'
      if(type === this.$store.state.skuExtraData.payWay )type = ''
      this.$commit('SET_PAYWAYS', type)
    }
  }
});
