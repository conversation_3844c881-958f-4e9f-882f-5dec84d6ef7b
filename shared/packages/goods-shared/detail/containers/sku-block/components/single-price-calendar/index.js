import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'single-price-calendar',

  getters: ['calendarDateData'],

  mapData: mapState({
    months(state, getters) {
      return getters.calendarDateData.months || [];
    },
    calendarData(state, getters) {
      return getters.calendarDateData.monthDataList || [];
    },
    activeStockDate: state => state.skuExtraData.selectedSkuDate[0] || '',
  }),

  data() {
    return {
      activeMonth: {},
    };
  },

  watch: {
    activeStockDate(val) {
      if (!val.length) {
        this.setYZData({
          activeMonth: {}
        });
        return;
      }
      const date = new Date(val);

      this.setYZData({
        activeMonth: {
          year: date.getFullYear(),
          name: date.getMonth() + 1,
        }
      });
    },
  },

  methods: {
    onItemClick({ currentTarget }) {
      this.setYZData({
        activeMonth: currentTarget.dataset.month
      });
    },

    onCalendarSelect({ detail: data }) {
      this.$commit('setSelectedCalendar', data);
      // 设置 sku 信息，下单时候从这里取
      this.$commit('setSkuExtraData', { selectedSkuDate: [data.id], selectedSkuDateId: data.dateSkuId });
    },
  },
});
