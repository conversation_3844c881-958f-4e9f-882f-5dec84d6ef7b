<view>
  <slot />

  <calendar
    wx:if="{{ calendarData.length }}"
    data="{{ calendarData }}"
    month="{{ activeMonth.name }}"
    year="{{ activeMonth.year }}"
    active-id="{{ activeStockDate }}"
    bind:select="onCalendarSelect"
  >
    <view class="e-card-calendar-header">
      <view
        wx:for="{{ months }}"
        wx:key="{{ item.name }}"
        wx:for-index="index"
        class="month"
        data-month="{{ item }}"
        bind:tap="onItemClick"
      >
        <view class="month-name">{{ item.name }}月</view>
        <theme-view color="general" class="month-label">{{ item.label }}</theme-view>
        <theme-view
          wx:if="{{ activeMonth.name === item.name || (!activeMonth.name && index === 0) }}"
          bg="main-bg"
          custom-class="border-line"
          borderAfter="main-bg"
        />
      </view>
    </view>
  </calendar>
</view>