<view class="ecard-sku-extra">
  <view class="ecard-tip" wx:if="{{ showVirtualTicketIntro }}">
    <view class="tip-title">[卡券使用说明]</view>
    <view
      wx:for="{{ ecardInstructions }}"
      wx:key="item"
    >{{ item }}</view>
  </view>
  <!-- 展示单个月的日历 -->
  <single-price-calendar wx:if="{{ isPriceCalendar }}">
    <van-cell
      border="{{ false }}"
      value="{{ virtualTicket.itemPreOrderTimeStr }}"
      title="选择日期"
      title-class="virtual-order-tip-title"
      value-class="virtual-order-tip"
    />
  </single-price-calendar>
</view>