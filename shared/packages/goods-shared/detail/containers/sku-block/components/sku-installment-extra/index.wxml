<view class="sku-installment">
  <view class="installment-title">
    <view>分期支付<text class="tip">(可选)</text></view>
    <view class="tip">分期金额、服务费以收银台展示为准，小于10元只支持分1期</view>
  </view>
  <view class="installment-list">
    <theme-view
      bg="{{ selectedInstallmentPeriod == 1 ? 'main-bg' : '#f7f8fa' }}"
      color="{{ selectedInstallmentPeriod == 1 ? 'general' : '#323233' }}"
      opacity="{{ selectedInstallmentPeriod == 1 ? 0.1 : 1 }}"
      custom-class="installment-item {{ !hasSkuId ? 'disabled' : '' }}"
      class="flex-able"
      data-key="{{ 1 }}"
      bind:tap="setInstallmentCount"
    >
      <view>分1期</view>
      <view>无服务费</view>
    </theme-view>

    <theme-view
      wx:for="{{ installment }}"
      wx:key="{{ item.key }}"
      bg="{{ selectedInstallmentPeriod == item.key ? 'main-bg' : '#f7f8fa' }}"
      color="{{ selectedInstallmentPeriod == item.key ? 'general' : '#323233' }}"
      opacity="{{ selectedInstallmentPeriod == item.key ? 0.1 : 1 }}"
      custom-class="{{ (!hasSkuId || noInstallmentOrder) ? 'installment-item__disabled' : 'installment-item' }}"
      data-key="{{ item.key }}"
      class="flex-able"
      bind:tap="setInstallmentCount"
    >
      <view>分 {{ item.key }} 期（{{ (item.key == 3 && isFreeInterest) ? '无':'含' }}服务费）</view>
      <view>¥{{ item.val }}/期</view>
    </theme-view>
  </view>
</view>