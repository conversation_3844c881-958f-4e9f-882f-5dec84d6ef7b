.sku-installment {
  padding: 10px 10px 5px;
}

.installment-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #111;
  margin: 0 5px 7px;

  > .tip {
    color: #bbb;
  }

  .tip {
    color: #bbb;
    transform: scale(0.84);
    margin-top: 8px;
    transform-origin: left;
    font-size: 12px;
  }
}

.installment-list {
  display: flex;
  flex-wrap: wrap;
}

.flex-able {
  flex: 1;
  display: block;
  margin: 6px;
  min-width: 35%;
}

.installment-item {
  background: #f7f8fa;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  color: #323233;

  border-radius: 4px;
  padding: 6px 12px;
  line-height: 18px;
  box-sizing: border-box;

  &__disabled {
    @extend .installment-item;
    background: #f7f8fa !important;
    color: #c8c9cc !important;
    pointer-events: none;
    border: none;
  }
}