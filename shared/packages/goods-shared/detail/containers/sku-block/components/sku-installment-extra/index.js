import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState, mapMutations } from '@youzan/vanx';

GoodsComponent({
  name: 'installment',

  state: ['noInstallmentOrder'],

  getters: ['skuData', 'isFreeInterest'],

  mapData: mapState({
    selectedInstallmentPeriod(state) {
      return state.skuExtraData.selectedInstallmentPeriod;
    },

    hasSkuId(state, getters) {
      return state.skuExtraData.selectedSkuComb
        && !!state.skuExtraData.selectedSkuComb.id
        || getters.skuData
        && getters.skuData.noneSku;
    },

    installment(state) {
      return state.installmentList;
    }
  }),

  methods: {
    ...mapMutations(['SET_INSTALLMENT_COUNT']),

    setInstallmentCount({ currentTarget }) {
      const count = currentTarget.dataset.key;
      this.SET_INSTALLMENT_COUNT(+count);
    }
  }
});
