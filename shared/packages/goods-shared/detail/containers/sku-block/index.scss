@import '~mixins/index.scss';

.van-sku {
  &__goods-name {
    font-size: 14px;
  }

  &__goods-price {
    .van-tag {
      margin-left: 5px;
    }
  }

  &__price-label {
    vertical-align: middle;
    font-size: 12px;
  }

  &__price-tag {
    padding: 0 5px;
    margin-left: 5px;
    line-height: 16px;
    font-size: 10px;
    border-radius: 2px;
    vertical-align: middle;
    color: $red;
    border: 1px solid $red;
  }

  &__origin-price {
    display: block;
    margin: 5px 0 0 2px;
    font-size: 10px;
    line-height: 12px;
    color: $gray-dark;
    text-decoration: line-through;
  }

  /* FIXME 等vant sku组件修复去除这段代码 */
  &-messages {
    .van-field__body {
      height: 100%;

      .van-field__control {
        height: 100%;
      }
    }
  }

  &-body {
    input[type='date'],
    input[type='time'],
    input[type='datetime-local'] {
      background-color: $white;
    }

    .feed-sku-container {
      margin-left: 15px;
      padding: 12px 0 2px;
    }
  }

  &-img-uploader__img {
    text-align: center;
  }
}

.sku-block {
  &__extra-group {
    padding-bottom: 1px;
  }

  &__actions-tip {
    line-height: 21px;
    margin: 0 auto;
    text-align: center;
    font-size: 13px;
    color: #969799;
    // 最小高度占位
    min-height: 21px;

    &.hide {
      opacity: 0;
    }
  }
}

.van-sku-actions {
  padding: 8px 16px;
}

.sku-action__btn {
  position: relative;
  box-sizing: border-box;
  height: 44px;
  margin: 0 5px;
  padding: 0;
  font-size: 16px;
  line-height: 42px;
  text-align: center;
  border-radius: 2px;
  transition: opacity .2s;
  height: 40px;
  font-weight: 500;
  font-size: 14px;
  line-height: 40px;
  border: 0;
  border-radius: 20px;

  & .disabled {
    border-radius: 20px;
    background: $gray-light !important;
    color: $gray-dark !important;
  }

  & .remind__btn-disabled {
    opacity: 0.6;
  }
}
