import Toast from '@vant/weapp/dist/toast/toast';
import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState, mapMutations, mapActions } from '@youzan/vanx';
import { setLogger } from '@goods/common/logger';
import { SKU_SCENE } from '@goods/common/config';

GoodsComponent({
  name: 'SkuBlock',

  state: ['goods', 'skuConfig', 'displayPop', 'skuExtraData', 'isFastBuySplit','payChannels', 'originSku'],

  getters: [
    'quota',
    'quotaUsed',
    'quotaText',
    'cartText',
    'quotaPeriod',
    'stepperTitle',
    'skuBuyBtnText',
    'skuValidator',
    'skuStepperConfig',
    'hideSkuStock',
    'hideSkuStepper',
    'showSkuAddCartBtn',
    'quantityReadOnly',
    'isVirtualTicket',
    'isPeriodbuy',
    'showladderGrouponExtra',
    'showInstallmentBar',
    'startSaleNum',
    'isOverQuotaLimit',
    'isCustomSkuBtn',
    'customSkuBtn',
    'skuPreferentialCoupon',
    'skuProperties',
    'skuDataForVant',
    'showSkuCouponExtra',
    'showNotEnoughCouponTip',
    'hasPayChannels',
    'priorUse',
  ],

  mapData: mapState({
    showExtraSkuGroup(state, getters) {
      return (
        getters.isPriceCalendar ||
        getters.isVirtualTicket ||
        getters.isPeriodbuy ||
        getters.showInstallmentBar ||
        getters.showladderGrouponExtra ||
        getters.showSkuCouponExtra ||
        getters.hasPayChannels
      );
    },
    addCartText(state, getters) {
      return `加入${getters.cartText}`;
    },
    calendarStock(state) {
      return (
        state.priceCalendarData.selected &&
        state.priceCalendarData.selected.stockNum
      );
    },
    disableSoldoutSku(state) {
      const { showStockLackReminder } = state.shopConfig;

      // 视频号限时秒杀的优先级高于售罄补货提醒功能
      const activity = state.marketActivity.limitedSeckill;
      if (activity) {
        return true;
      }

      if (showStockLackReminder) {
        return false;
      }

      return true;
    },
  }),

  computed: {
    ...mapState(['displayPop']),
  },

  watch: {
    'displayPop.skuPopShow': function (newVal) {
      !this.data.inited && this.setYZData({ inited: true });
      
      if (newVal && this.data.isOverQuotaLimit) {
        Toast(
          `该商品${this.data.quotaText},你之前已经购买${this.data.quotaUsed}件`
        ); // eslint-disable-line
      }

      const { skuScene = '' } = this.data.skuConfig || {};
      if (newVal && skuScene === SKU_SCENE.NORMAL_BUY && this.data.originSku.noneSku) {
        this.setSkuLogger();
      }
    },
  },

  methods: {
    ...mapMutations(['hideSkuPop']),
    ...mapActions([
      'handleBuy',
      'handleSkuSelect',
      'handleAddCart',
      'stepperChange',
      'openSkuImgPreview',
      'customBtnClick',
      'handlePropSelect',
      'handleWxSubscribeSuccess',
      'handleWxSubScribeFail',
    ]),
    setSkuLogger() {
      setLogger({
        et: 'custom', // 事件类型
        ei: 'appear_or_disappear_sku_buy', // 事件标识
        en: '是否出现立即购买sku面板', // 事件名称
        params: {
          appear_sku_buy: '1',
          component: 'sku',
        } // 事件参数
      });
    },
  },
});
