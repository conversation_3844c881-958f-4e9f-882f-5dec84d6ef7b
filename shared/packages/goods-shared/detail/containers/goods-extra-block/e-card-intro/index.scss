@import '~mixins/index.scss';

.e-card-block {
  margin-top: 8px;
}

.e-card-popup {
  font-family: PingFangSC-Regular;
  font-size: 13px;
  color: #969799;
  text-align: justify;
  line-height: 19px;

  .title {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: $text-color;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
    margin-bottom: 15px;
  }
}

.e-card-intro-item {
  margin-bottom: 15px;
  color: #969799;
  line-height: 18px;
  font-size: 14px;
}

.rule-label {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #323233;
}

.rule-list {
  white-space: pre-wrap;
}