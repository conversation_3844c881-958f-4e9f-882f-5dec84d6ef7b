<view wx:if="{{ showVirtualTicketIntro }}" class="e-card-block">
  <common-cell
    value="{{ ecardInstructions[0] }}"
    title="说明"
    is-link
    border="{{ false }}"
    value-class="u-activity-intro-title"
    bind:click="traggerPop"
  />

  <common-pop
    show="{{ show }}"
    bind:close="hidePopUp"
    bind:btn-click="hidePopUp"
  >
    <view class="e-card-popup">
      <view class="title">卡券使用说明</view>
      <view
        wx:for="{{ ecardRules }}"
        wx:key="index"
        class="e-card-intro-item"
      >
        <view class="rule-label">{{ ruleLables[index] }}</view>
        <view class="rule-list">{{ item }}</view>
      </view>
    </view>
  </common-pop>
</view>