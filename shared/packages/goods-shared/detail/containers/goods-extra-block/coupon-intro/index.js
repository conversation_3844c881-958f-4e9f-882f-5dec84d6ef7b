import { GoodsComponent } from '@goods/common/vanx-component';
import navigate from 'shared/utils/navigate';
import { mapState } from '@youzan/vanx';

const FOLD_HEIGHT = 32; // 优惠说明可折叠的高度临界值

GoodsComponent({
  name: 'coupon-intro',

  getters: ['showCouponIntro', 'couponIntroDetail'],

  data: {
    showFoldBtn: false, // 是否显示折叠按钮
    isFolded: true, // 是否被折叠起来
  },

  mapData: mapState({
    instruction(state, getters) {
      const { description = '' } = getters.couponIntroDetail || {};
      return description;
    },
    isAllGoodsAvailable(state, getters) {
      const { preferentialMode, applicableOnlineGoodsRangeType } = getters.couponIntroDetail || {};
      return preferentialMode !== 3 && applicableOnlineGoodsRangeType <= 1;
    },
  }),

  ready() {
    if (!this.data.instruction) return;
    setTimeout(() => {
      const query = this.createSelectorQuery();
      query.select('#instruction').boundingClientRect((rect) => {
        if (rect && rect.height > FOLD_HEIGHT) {
          this.setYZData({
            showFoldBtn: true
          });
        }
      }).exec();
    }, 50);
  },

  methods: {
    toggleFold() {
      this.setYZData({
        isFolded: !this.data.isFolded
      });
    },

    // 跳转优惠可用商品页
    jumpAvailableGoods() {
      if (this.data.isAllGoodsAvailable) return;
      const { couponId, alias = '' } = this.data.couponIntroDetail;
      navigate.navigate({
        url: `/packages/shop/goods/group/index?pageType=coupon&group_id=${couponId}&alias=${alias}`
      });
    },
  },
});
