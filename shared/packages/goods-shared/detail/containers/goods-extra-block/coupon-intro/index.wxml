<view wx:if="{{ showCouponIntro }}" class="coupon-intro">
  <view class="coupon-intro__main">
    <!-- 使用时间 -->
    <view class="coupon-intro__time">
      <view class="coupon-intro__title">使用时间</view>
      <view>{{ couponIntroDetail.validTimeCopywriting }}</view>
    </view>

    <!-- 优惠内容 -->
    <view class="coupon-intro__discount">
      <view class="coupon-intro__title">优惠内容</view>
      <view class="coupon-intro__desc" bindtap="jumpAvailableGoods">
        <text class="coupon-intro__desc-text">
          {{ couponIntroDetail.preferentialCopywritingDetail }}
        </text>
        <van-icon
          wx:if="{{ !isAllGoodsAvailable }}"
          name="arrow"
          color="#979797"
          size="16px"
        />
      </view>
    </view>

    <!-- 使用说明 -->
    <view wx:if="{{ instruction }}" class="coupon-intro__state">
      <view class="coupon-intro__title">使用说明</view>
      <view
        id="instruction"
        class="{{ showFoldBtn && isFolded ? 'coupon-intro__state--fold' : ''}}"
      >
        {{ instruction }}
      </view>
    </view>

    <!-- 展开收起按钮 -->
    <view
      wx:if="{{ showFoldBtn }}"
      class="coupon-intro__handle-btn"
      bindtap="toggleFold"
    >
      {{ isFolded ? '展开' : '收起' }}全部
      <van-icon
        name="{{ isFolded ? 'arrow-down' : 'arrow-up' }}"
        color="#979797"
        size="14px"
      />
    </view>
  </view>
</view>