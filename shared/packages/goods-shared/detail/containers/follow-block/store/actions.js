import Toast from '@vant/weapp/dist/toast/toast';
import api from '@goods/utils/api';
// TODO 关注公众号不需要
export default {
  // 页面唤起login
  // TODO 小程序暂时不支持关注公众号
  // doPageFollow({ commit }) {
  //   commit('showFollowPopup');
  // },
  fetchQrcodeWeixinImg({ state }) {
    const { mpData, followInfo } = state;
    if (!mpData.account.id || followInfo.qrcodeWeixinFetched) {
      return;
    }
    api.get({
      url: '/v2/weixin/scan/hosting.json',
      method: 'post',
      data: {
        mp_id: mpData.account.id,
        kdt_id: mpData.shop.kdtId,
      },
    }).then(resp => {
      if (resp) {
        // const qrcodeWeixinImg = formatYZUrl(resp.qrcodeUrl);
        // commit('fetchQrcodeWeixinImgSuccess', qrcodeWeixinImg);
      } else {
        Toast('获取店铺二维码失败');
      }
    });
  },
};
