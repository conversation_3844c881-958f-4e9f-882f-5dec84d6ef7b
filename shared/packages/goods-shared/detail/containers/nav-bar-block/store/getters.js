// 顶部导航标签
const NAV_TAB = [
  // 商品
  {
    name: '商品',
    selector: 'goods'
  },
  // 评价
  {
    name: '评价',
    selector: 'review'
  },
  // 详情
  {
    name: '详情',
    selector: 'detail'
  },
  // 推荐
  {
    name: '推荐',
    selector: 'recommend'
  }
];
export default {
  // 需要展示的导航tab列表
  navTabList(state, getters) {
    return NAV_TAB.filter((item) => {
      // 无评价内容不展示评价tab
      if (item.selector === 'review' && !getters.showCustomerReviews) {
        return false;
      }
      // 无推荐商品不展示推荐tab
      if (item.selector === 'recommend' && !getters.showRecommendGoods) {
        return false;
      }
      return true;
    });
  }
};
