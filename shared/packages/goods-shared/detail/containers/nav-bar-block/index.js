import { GoodsComponent } from '@goods/common/vanx-component';
import Event from '@youzan/weapp-utils/lib/event';
import { mapState, mapMutations } from '@youzan/vanx';
import throttle from '@youzan/weapp-utils/lib/throttle';
import { themeColorBehaivor } from '@goods/common/behaviors';
import { getHeight } from 'shared/utils/nav-config';
import { checkIsChannels, checkHasStatusBar } from 'shared/utils/channel';
import getSystemInfo from 'shared/utils/browser/system-info';
import getUniquePageKey from 'shared/utils/unique';

const NAV_HEIGHT = 41; // 导航tab栏高度
const OPACITY_TOP = 100; // 导航栏隐现top阈值
let navFlag = false; // 导航定位标志

GoodsComponent({
  name: 'NavBarBlock',

  behaviors: [themeColorBehaivor],

  getters: ['navTabList'],

  data: {
    activeIndex: 0, // 当前激活的tab
    opacityVal: 0, // 导航栏透明度
    mainColor: '', // 主题色
    topNavHeight: getHeight(false), // 顶部虚拟导航高度
  },

  options: {
    styleIsolation: 'shared',
  },

  computed: {
    ...mapState({
      navItemTopList: (state) =>
        state.navItemTopList.map((it) => it - NAV_HEIGHT - getHeight(false)),
    }),
  },

  ready() {
    this.pageScrollFn = throttle(this.handleScroll.bind(this), 100);
    this.pageKey = getUniquePageKey();
    Event.on('onPageScroll' + this.pageKey, this.pageScrollFn, this);

    // 视频号顶部tabs间距兼容
    if (checkIsChannels()) {
      const systemInfo = getSystemInfo(false);
      const { statusBarHeight = 0 } = systemInfo;
      this.setYZData({
        topNavHeight: statusBarHeight + 40,
      });
    }
  },

  methods: {
    ...mapMutations(['setTradeCarouselTop']),
    handleScroll({ scrollTop }) {
      this.handleNavOpacity(scrollTop);

      this.triggerEvent('get-tab-top', scrollTop);

      // 手动切换时不进行自动切换，防止出现冲突
      if (navFlag) return;

      // 滚动自动切换对应tab
      let tempIndex = this.data.activeIndex;
      this.navItemTopList.forEach((item, index) => {
        if (item - scrollTop < 5) {
          tempIndex = index;
        }
      });
      if (tempIndex !== this.data.activeIndex) {
        this.setYZData({
          activeIndex: tempIndex,
        });
      }
    },

    // 导航栏隐现处理
    handleNavOpacity(scrollTop) {
      let opacityVal = 0;
      if (scrollTop >= 0 && scrollTop <= OPACITY_TOP) {
        opacityVal = scrollTop / OPACITY_TOP;
      } else if (scrollTop > OPACITY_TOP) {
        opacityVal = 1;
      }

      if (this.data.opacityVal !== opacityVal) {
        this.setYZData({
          opacityVal,
        });
      }
    },

    // 页面滚动到对应tab位置
    goAnchor({ detail }) {
      navFlag = true;
      wx.pageScrollTo({
        scrollTop: this.navItemTopList[detail.index],
        complete: () => {
          setTimeout(() => {
            navFlag = false;
          }, 50);
        },
      });
    },
  },

  observers: {
    opacityVal(val) {
      if (val === 1) {
        this.setTradeCarouselTop({ top: NAV_HEIGHT + 8 });
      } else {
        this.setTradeCarouselTop({ top: 34 });
      }
    },
  },

  detached() {
    Event.off('onPageScroll' + this.pageKey, this.pageScrollFn);
  },
});
