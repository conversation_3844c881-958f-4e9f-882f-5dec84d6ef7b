
<theme-view
  wx:if="{{ opacityVal > 0 }}"
  class="m-nav-bar"
  style="opacity: {{ opacityVal }}; top: {{ topNavHeight }}px;"
>
   <van-tabs
    active="{{ activeIndex }}"
    color="{{ mainColor }}"
    border="{{ false }}"
    z-index="{{ 23 }}"
    line-width="{{ 26 }}"
    tab-class="m-nav-bar__tab"
    tab-active-class="m-nav-bar__tab--active"
    bind:click="goAnchor"
  >
    <van-tab wx:for="{{ navTabList }}" wx:key="selector" title="{{ item.name }}" />
  </van-tabs>
</theme-view>