.coupon {
  height: 100%;
  width: 100%;
  background-image: url(https://img01.yzcdn.cn/upload_files/2021/02/09/FmmFkp4VEepYDX_OFqMH7HwubSoL.png);
  background-size: cover;
  background-position: center center;
  position: relative;
  font-size: 10px;
  display: flex;
  justify-content: center;
  color: #ff1812;
  animation: move 1.8s 0s infinite;
  transform-origin: center;
}

@keyframes move {
  10% {
    transform: rotate(-6deg);
  }

  18% {
    transform: rotate(6deg);
  }

  26% {
    transform: rotate(-6deg);
  }

  34% {
    transform: rotate(6deg);
  }

  42% {
    transform: rotate(-6deg);
  }

  50% {
    transform: rotate(6deg);
  }

  0%,
  60%,
  100% {
    transform: rotate(0deg);
  }
}

.coupon-pirce {
  position: absolute;
  top: 4px;
  transform: scale(0.8);
}

.price-top-6,
.price-top-7 {
  top: 6px;
}

.coupon-pirce_text {
  font-size: 16px;
  font-weight: bold;
}

.coupon-time {
  position: absolute;
  bottom: 0;
  transform: scale(0.6);
  font-weight: bold;
  font-size: 20px;

  &__item {
    color: #ff1812;
  }
}

.price-length-1,
.price-length-2,
.price-length-3,
.price-length-4 {
  font-size: 16px;
}

.price-length-5 {
  font-size: 14px;
}

.price-length-6 {
  font-size: 12px;
}

.price-length-7 {
  font-size: 10px;
}
