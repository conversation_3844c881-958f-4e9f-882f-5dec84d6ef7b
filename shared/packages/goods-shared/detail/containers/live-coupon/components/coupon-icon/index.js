import { GoodsComponent } from '@goods/common/vanx-component';

GoodsComponent({
  properties: {
    time: {
      type: String,
      value: '',
    },
    detail: {
      type: Object,
      value: {},
    },
    formattedTime: {
      type: String,
      value: '',
    },
  },

  methods: {
    onCountDownChange(e) {
      this.triggerEvent('countDownChange', e.detail);
    },

    onClose() {
      this.triggerEvent('close');
    },
  },
});
