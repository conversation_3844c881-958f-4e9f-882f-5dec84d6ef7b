<view class="coupon">
  <view class="coupon-pirce {{ 'price-top-' + detail.miniValue.length }}">
    ¥
    <span class="coupon-pirce_text {{ 'price-length-' + detail.miniValue.length }}">{{ detail.miniValue }}</span>
  </view>
  <van-count-down use-slot time="{{ time }}" class="coupon-time" bind:change="onCountDownChange">
    <view class="coupon-time__item">{{ formattedTime }}</view>
  </van-count-down>
</view>