import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions, mapState } from '@youzan/vanx';
import { get } from '@goods/utils';
import { SKU_SCENE } from '@goods/common/config';

GoodsComponent({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    detail: {
      type: Object,
      value: {},
    },
    time: {
      type: String,
      value: '',
    },
    formattedTime: {
      type: String,
      value: '',
    },
  },

  mapData: {
    ...mapState({
      accountUnionScene: (state) =>
        get(state, 'businessConfigs.accountUnionScene'),
    }),
  },

  methods: {
    ...mapActions(['triggerSku']),

    onCountDownChange(e) {
      this.triggerEvent('countDownChange', e.detail);
    },

    onClose() {
      this.triggerEvent('close');
    },

    showSku() {
      this.triggerSku({
        skuScene: SKU_SCENE.NORMAL_BUY,
        accountUnionScene: this.data.accountUnionScene,
      });
      this.onClose();
    },
  },
});
