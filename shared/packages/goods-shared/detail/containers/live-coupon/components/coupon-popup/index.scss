.coupon-container {
  background-color: transparent;
  // animation: couponZoomIn 0.5s linear forwards;
}

.coupon-van-popup {
  background-color: transparent !important;
}

.coupon-pop-wrapper {
  background: #fff;
  border-radius: 8px;
  height: 278px;
  width: 310px;
  overflow: hidden;
  position: relative;
}

@keyframes couponZoomIn {
  0% {
    transform: scale(0.5);
  }

  20% {
    transform: scale(0.7);
  }

  40% {
    transform: scale(0.9);
  }

  60% {
    transform: scale(1);
  }

  80% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

.coupon-close-icon {
  float: right;
  margin-bottom: 15px;
  width: 32px;
  height: 32px;
  background-image: url(https://img01.yzcdn.cn/upload_files/2021/02/22/FqyC9mrw1LTY-9jO2raiVd8kdF8W.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.live-title {
  font-size: 16px;
  color: #323233;
  text-align: center;
  height: 94px;
  line-height: 94px;
}

.coupon-detail {
  position: relative;
  height: 84px;
  width: 280px;
  margin: 0 auto;
  color: #fa1919;
  border-radius: 6px;
  display: flex;
  align-items: center;
  background: #fff6f6;
  border: 1px solid #fcc;
}

.coupon-detail__price {
  font-size: 32px;
  line-height: 30px;
  width: 100px;
  text-align: center;
  margin-right: 12px;
  font-weight: bold;
}

.price-length-1,
.price-length-2,
.price-length-3,
.price-length-4 {
  font-size: 32px;
}

.price-length-5 {
  font-size: 26px;
}

.price-length-6 {
  font-size: 20px;
}

.price-length-7 {
  font-size: 18px;
}

.price-length-8 {
  font-size: 16px;
}

.price-length-9 {
  font-size: 14px;
}

.coupon-detail__unit {
  font-size: 12px;
  line-height: 16px;
  flex: 1;
  font-weight: normal;
  margin-left: 2px;
}

.coupon-detail__limit {
  font-size: 14px;
  color: #323233;
  line-height: 18px;
  font-weight: bold;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.coupon-detail__time,
.coupon-time__pop {
  font-size: 12px;
  line-height: 16px;
  color: #f44;
  display: flex;
}

.recevied-icon {
  height: 30px;
  width: 30px;
  position: absolute;
  right: 0;
  bottom: 0;
}

.breath_light {
  overflow: hidden;
  height: 40px;
  width: 260px;
  margin: 30px auto 0;
  animation: breath 0.8s linear infinite;
  transform-origin: center;
  background-image: linear-gradient(270deg, #ff572f 0%, #ff0438 100%);
  border-radius: 24px;
  font-size: 16px;
  color: #fff;
  text-align: center;
  line-height: 40px;
}

@keyframes breath {
  0% {
    transform: scale(1);
  }

  20% {
    transform: scale(1.03);
  }

  40% {
    transform: scale(1.06);
  }

  60% {
    transform: scale(1.03);
  }

  80% {
    transform: scale(1);
  }
}
