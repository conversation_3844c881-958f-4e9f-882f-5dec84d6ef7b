import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';
import { get, formatPrice } from '@goods/utils';
import { parseFormat } from '@vant/weapp/count-down/utils';
import IconController from 'components/showcase/components/floating-nav/icon-controller/index';
import api from '@goods/utils/api';
import { getScene } from 'shared/utils/channel';
import { parsePriceText } from './utils';

GoodsComponent({
  data: {
    isWechatLive: false,
    showCouponPop: false,
    couponDetail: {},
    time: 0,
    formattedTime: '',
    scene: 0,
  },

  mapData: {
    ...mapState({
      alias: (state) => get(state, 'goods.alias'),
      goodsId: (state) => get(state, 'goods.id'),
    }),
  },

  ready() {
    const scene = getScene();
    const WECHAT_LIVE_SENCE = 1177;

    if (scene === WECHAT_LIVE_SENCE) {
      this.setIcon();
      this.sendCoupon();
      this.setYZData({
        scene,
      });
    }
  },

  methods: {
    sendCoupon() {
      const { alias, goodsId } = this.data;

      api
        .post({
          url: `/wscgoods/weapp/sendCoupon.json?alias=${alias}`,
          headers: {
            'content-type': 'application/json',
          },
          data: {
            goodsId,
          },
        })
        .then((res) => {
          const { value, validEndTime, isNewSend, useThresholdCopywriting } =
            res || {};
          if (!value) return;
          this.setYZData({
            showCouponPop: true,
            isWechatLive: true,
            time: validEndTime - Date.now(),
            couponDetail: {
              value: `${formatPrice(value, 2, true)}`,
              miniValue: parsePriceText(value),
              isNewSend,
              useThresholdCopywriting,
            },
          });
          setTimeout(() => {
            this.closeCouponPop();
          }, 5000);
        })
        .catch((err) => {
          console.log('err:', err);
        });
    },

    setIcon() {
      this.iconController = new IconController().setIcon(this, {
        priority: 70,
        height: 48,
        cb: [
          (bottom) => {
            this.setYZData({ bottom: bottom + 'px' });
          },
          (goaway) => {
            this.setYZData({ goaway });
          },
        ],
      });
    },

    onCountDownChange(e) {
      const { hours = 0, minutes = 0, seconds = 0 } = e.detail;
      const format = +hours === 0 ? 'mm:ss' : 'HH:mm:ss';
      const obj = {
        formattedTime: parseFormat(format, e.detail),
      };
      if (hours === 0 && minutes === 0 && seconds === 0) {
        Object.assign(obj, {
          isWechatLive: false,
        });
      }
      this.setYZData(obj);
    },

    closeCouponPop() {
      this.setYZData({
        showCouponPop: false,
      });
    },

    checkCoupon() {
      this.setYZData({
        showCouponPop: true,
      });
    },
  },
});
