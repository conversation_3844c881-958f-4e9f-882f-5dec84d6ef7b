<view class="live-coupon-container" wx:if="{{ isWechatLive }}">
  <coupon-popup
    detail="{{ couponDetail }}"
    show="{{ showCouponPop }}"
    time="{{ time }}"
    formattedTime="{{ formattedTime }}"
    bind:countDownChange="onCountDownChange"
    bind:close="closeCouponPop"
  />
  <view
    bind:tap="checkCoupon"
    class="live-coupon {{ goaway ? 'goaway' : '' }} {{ showCouponPop ? 'icon-hide' : ''}}"
    style="{{ bottom ? 'bottom:' + bottom : '' }}; transition: {{ bottom ? 'all 0.2s' : '' }};"
  >
    <view class="hide">{{ scene }}</view>
    <coupon-icon
      detail="{{ couponDetail }}"
      time="{{ time }}"
      formattedTime="{{ formattedTime }}"
      bind:countDownChange="onCountDownChange"
    />
  </view>
</view>