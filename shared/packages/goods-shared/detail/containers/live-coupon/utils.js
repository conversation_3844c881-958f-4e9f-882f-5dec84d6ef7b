function formatPrice(number, decimal = 2, leaveZero = false) {
  if (typeof number !== 'number') return '';
  const price = (number / 100).toFixed(decimal);
  if (leaveZero) {
    return parseFloat(price);
  }
  return price;
}

export const parsePriceText = (value) => {
  const wUnit = 10000;
  const val = formatPrice(value, 2, true);

  return val >= wUnit ? `${parseFloat((val / wUnit).toFixed(1))}w` : `${val}`;
};
