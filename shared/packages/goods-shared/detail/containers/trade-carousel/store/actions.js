import {
  node as request
} from 'shared/utils/request';
import { SWITCH } from '@goods/common/config';

function fetchRecords(data) {
  return request({
    path: '/wscgoods/getItemMarqueeRecords.json',
    data,
  });
}

export default {
  fetchTradeRecords({ state, commit }) {
    const alias = state.goods.alias || '';
    const goodsTradeMarquee = state.shopConfig.goodsTradeMarquee;

    if (!alias || !goodsTradeMarquee) {
      return;
    }

    let show = false;
    try {
      show = +(JSON.parse(goodsTradeMarquee).show) === SWITCH.on;
    } catch (e) { //
    }

    if (!show) {
      return;
    }

    fetchRecords({
      alias,
    })
      .then(payload => {
        commit('setTradeRecords', payload);
      })
      .catch(() => {
        // do nothing
      });
  },
};
