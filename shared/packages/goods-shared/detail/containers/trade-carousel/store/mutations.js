import { getRandomAvatar } from './avatars';

export default {
  setTradeRecords(state, payload) {
    state.tradeRecordsV2 = payload.map((tradeRecord) => {
      tradeRecord.show = false;
      tradeRecord.avatar = tradeRecord.avatar || getRandomAvatar();
      return tradeRecord;
    });
  },
  setTradeCarouselTop(state, payload) {
    state.tradeCarousel.fixedTop = payload.top;
  },
  setTradeCarouselVisible(state, visible) {
    state.tradeCarousel.visible = visible;
  },
};
