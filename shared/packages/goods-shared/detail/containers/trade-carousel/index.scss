.trade-carousel {
  position: fixed;
  top: 15px;
  left: 16px;
  height: 32px;
  // 比 goods-fixed-info高一点
  z-index: 22;
  overflow: hidden;
  color: #fff;

  &__swipe {
    height: 32px;
    color: #fff;
    font-size: 12px;
    line-height: 16px;

    &-item {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      height: 32px;
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 16px;
      padding: 0 8px 0 4px;
    }

    &-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      margin-right: 8px;
    }

    &-text {
      flex: 1;
      margin-right: 1px;
      line-height: normal;

      // @include ellipsis;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.move-enter-active {
  animation: move-in 0.5s;
}

.move-leave-active {
  animation: move-out 0.3s;
  animation-fill-mode: forwards;
}

@keyframes move-in {
  0% {
    transform: translateY(32px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes move-out {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-32px);
  }
}
