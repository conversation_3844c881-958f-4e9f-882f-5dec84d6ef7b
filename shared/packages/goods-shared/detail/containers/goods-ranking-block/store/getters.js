import { get } from '@goods/utils';
import { checkWscSingleStore, checkEduSingleStore } from '@youzan/utils-shop';

export default {
  ranking(state) {
    const { ranking = -1 } = get(state, 'goodsExtendInfo.HOT_RECOMMEND', {});
    return parseInt(ranking, 10);
  },

  // 显示条件为：开启个性化商品推荐 & 排名在1-20 & 微商城/教育单店 & 非精简版微商城
  showGoodsRanking(state, { ranking }) {
    const showPersonalRecommendGoods = get(state, 'shopConfig.showPersonalRecommendGoods', false);
    const isSimplifiedWsc = get(state, 'shopConfig.isSimplifiedWsc', false);
    const shopMetaInfo = get(state, 'shop.shopMetaInfo', {});
    const isSupportStore = checkWscSingleStore(shopMetaInfo) || checkEduSingleStore(shopMetaInfo);
    return (
      !!Number(showPersonalRecommendGoods) &&
      ranking > 0 &&
      ranking <= 20 &&
      isSupportStore &&
      !Number(isSimplifiedWsc)
    );
  }
};
