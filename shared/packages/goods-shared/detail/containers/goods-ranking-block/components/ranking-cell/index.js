import { GoodsComponent } from '@goods/common/vanx-component';
import navigate from 'shared/utils/navigate';

const app = getApp();
const webviewPagePath = '/pages/common/webview-page/index';
GoodsComponent({
  name: 'RankingCell',

  options: {
    styleIsolation: 'shared',
  },

  getters: ['showGoodsRanking', 'ranking'],

  methods: {
    handleClick() {
      const kdtId = app.getKdtId();
      const oid = app.getOfflineId();
      const viewSrc = `/wscstatcenter/recommend-showcase-page/ranking?kdtId=${kdtId}&oid=${oid}`;
      navigate.navigate({
        url: `${webviewPagePath}?src=${encodeURIComponent(
          viewSrc
        )}&title=店铺热榜`,
      });
    },
  },
});
