import { get } from '@goods/utils';
import { getUrl, SHOP_INFO, getWebviewPath } from '@goods/common/page-path';
import { SKU_SEPERATOR } from '@goods/common/config';

// 服务交易顺序: 赠运费险 => 货到付款 => 收货后结算 => 货款直接给商家 => 店铺负责发货&售后 => 不支持申请退款 => 不支持7天无理由退 => 合作商直供 => 商品包税 => 海外直邮/保税进口 => 线下门店 => 快递发货 => 同城配送 => 到店自提

export default {
  // 物流配送列表
  deliveryList(state) {
    const {
      // 是否支持快递
      supportExpress,
      // 是否支持自提
      supportSelfFetch,
      // 是否支持同城送
      supportLocalDelivery,
      // 快递费用
      expressFee,
      // 同城送费用
      localDeliveryFee,
    } = state.distribution;

    const deliveryList = [];

    if (supportExpress) {
      const expressFeeStr = expressFee === '免运费' ? expressFee : `运费：${expressFee}`;
      deliveryList.push({
        tag: '快递发货',
        desc: `可选择快递发货配送上门，本商品${expressFeeStr}。`,
      });
    }

    if (supportSelfFetch) {
      deliveryList.push({
        tag: '到店自提',
        desc: '可选择就近自提点并预约提货时间。',
      });
    }

    if (supportLocalDelivery) {
      const localDeliveryFeeStr = localDeliveryFee
        ? `，本商品运费：${localDeliveryFee}`
        : '';
      deliveryList.push({
        tag: '同城配送',
        desc: `可选择同城配送并预约送达时间${localDeliveryFeeStr}。`,
      });
    }

    return deliveryList;
  },

  serviceDescList(state, getters) {
    const {
      env: { isNewHopeKdt, platform },
      // 店铺设置
      shopConfig: {
        hasPhysicalStore,
        // isYouzanSecured,
        isSecuredTransactions,
        supportFreightInsurance,
      },
      // 供货商
      supplier = {},
      // 支付方式
      payWays: { cashOnDelivery },
      // 退款设置
      refund: { isSupport },
      // 海淘
      goods: {
        isHaitao,
        supportUnconditionalReturn,
        barterModel,
        supportQuickRefund,
      },
      goodsActivity: { haitao },
      businessConfigs,
    } = state;

    const certList = [];
    const {
      certDesc: CERT_DESC = {},
      fxMode: FX = {},
      haitaoModeMap: HAITAO_MODE_MAP = {},
    } = businessConfigs;

    // 店铺运费险对于分销、礼品卡、酒店、周期购、会员卡、虚拟商品无效
    if (supportFreightInsurance) {
      certList.push({
        tag: '退货包运费',
        desc: CERT_DESC.freightInsurance,
      });
    }

    // 极速退款
    if (supportQuickRefund) {
      certList.push({
        tag: '自动退款',
        key: 'supportQuickRefund',
        descList: CERT_DESC.supportQuickRefund,
        url: getWebviewPath(
          'https://www.youzan.com/intro/rule/detail?alias=p8otbwpf&pageType=rules',
          {}
        ),
        noArrow: true,
        // 当无“退货包运费”时，展示第一位，有则展示在前 2 位
        sort: supportFreightInsurance ? 2 : 1,
      });
    }

    if (cashOnDelivery) {
      certList.push({
        tag: '货到付款',
        desc: CERT_DESC.cashOnDelivery,
      });
    }

    // 分销商需要供货商担保才显示
    if (isSecuredTransactions) {
      // 是否加入担保交易
      if (platform === 'kuaishou') {
        // 快手平台担保交易
        certList.push({
          tag: '收货后结算',
          desc: CERT_DESC.isSecuredTransactionsKuaishou,
        });
      } else {
        // 加入了有赞担保，就不显示担保交易
        certList.push({
          tag: '收货后结算',
          desc: CERT_DESC.isSecuredTransactions,
        });
      }
    } else {
      // 非担保店铺显示文案

      certList.push({
        tag: '货款直接给商家',
        desc: CERT_DESC.weixinPayOriginMoney,
      });

      certList.push({
        tag: '店铺负责发货&售后',
        desc: CERT_DESC.weixinPayOriginAftersale,
      });
    }

    // 虚拟商品是否支持退款
    if (!isSupport) {
      certList.push({
        tag: '不支持申请退款',
        desc: CERT_DESC.unsupportRefund,
      });
    }

    if (supplier.mode === FX.PROMOTION) {
      certList.push({
        tag: '合作商直销',
        desc: CERT_DESC.supplierDirect,
        url: getUrl(SHOP_INFO, {
          kdt_id: supplier.kdtId,
          show: 'supplier',
          no_footer: 1,
          no_btn: 1,
        }),
      });
    }

    // 海淘商品
    if (isHaitao && haitao) {
      certList.push({
        tag: '不支持7天无理由退货',
        desc: CERT_DESC.haitaoUnsupportRefund,
      });

      if (haitao.tariffRule === 0) {
        certList.push({
          tag: '单独计税',
          desc: CERT_DESC.haitaoTaxExtraPays,
        });
      } else {
        certList.push({
          tag: '商品含税',
          desc: CERT_DESC.haitaoTaxContains,
        });
      }

      const tradeMode = HAITAO_MODE_MAP[haitao.haiTaoTradeMode];
      if (tradeMode) {
        certList.push({
          tag: tradeMode.modeStr,
          desc: tradeMode.modeDesc,
        });
      }
    }

    // 新希望店铺隐藏线下门店
    if (hasPhysicalStore && !isNewHopeKdt) {
      certList.push({
        tag: '线下门店',
        desc: CERT_DESC.physicalStore,
      });
    }

    // 支持7天无理由退货
    if (supportUnconditionalReturn) {
      certList.push({
        tag: '7天无理由退货',
        // desc 和 descList 的区别： desc: string; descList: { text: '', className: ''}[]
        descList: CERT_DESC.supportUnconditionalReturn,
        // 帮助中心
        url: getWebviewPath(
          'https://www.youzan.com/intro/rule/detail?alias=13xbozjlt&pageType=rules',
          {}
        ),
        // 不需要展示箭头
        noArrow: true,
        // 展示在前 3 位
        sort: 3,
      });
    }

    if (
      barterModel
      // 支持换货
      && barterModel.isSupportBarter === 1
      // 非极速下单
      && !state.isFastBuy
    ) {
      certList.push({
        tag: '支持退换',
        desc: CERT_DESC.supportBarter,
        // 如果有 7 天无理由，则排前 4 位，否则前三位
        sort: supportUnconditionalReturn ? 4 : 3,
      });
    }

    const staticArr = [];

    // 需要移动位置的标签
    const moveArr = certList
      .reduce((arr, current) => {
        if (current.sort !== undefined) {
          arr.push(current);
        } else {
          staticArr.push(current);
        }
        return arr;
      }, [])
      .sort((a, b) => a.sort - b.sort);

    // 移动位置
    moveArr.forEach((item) => {
      if (staticArr.length < item.sort) {
        return staticArr.push(item);
      }
      staticArr.splice(item.sort - 1, 0, item);
    });

    // sort 可以指定标签的顺序，从 1 开始，例如：sort: 3; 该标签一定会是前3位，如果 sort 重复，请找产品battle
    let serviceList = [...staticArr, ...getters.deliveryList];

    const saasServiceDesc = get(state, 'saas.groupBlock.serviceDescList', []);
    if (saasServiceDesc.length > 0) {
      serviceList = saasServiceDesc;
    }
    return serviceList;
  },

  serviceBarTitle(state, getters) {
    let serviceDesc = getters.serviceDescList;
    if (serviceDesc.length > 3) {
      serviceDesc = serviceDesc.slice(0, 3);
    }
    return serviceDesc.map((item) => item.tag).join(' · ');
  },

  // 是否展示服务栏
  showServiceBar(state, getters) {
    const { showServiceBar } = state.saas.groupBlock;
    return (
      showServiceBar
      && !state.goods.isOutlink
      && getters.serviceDescList.length > 0
    );
  },

  // 是否展示商品类目栏
  showCategoryBar(state, getters) {
    const {
      shopConfig: { showCategoryParam },
    } = state;
    const result = !state.goods.isOutlink && +showCategoryParam && getters.categoryBarTitle;
    if (+showCategoryParam && getters.categoryBarTitle) {
      return result;
    }
    return result;
  },

  categoryBarTitle(state) {
    const arr = get(state, 'goods.itemCatePropDetailModel.propNameList', []);
    const keys = arr.slice(0, 2);
    return keys.join('；');
  },

  categoryBarValue(state) {
    const arr = get(
      state,
      'goods.itemCatePropDetailModel.propertiesDetailModels',
      []
    );
    const resdata = JSON.parse(JSON.stringify(arr));
    resdata.forEach((item) => {
      let str = '';
      str += item.valNames.join('，');
      item.valNames = str;
    });
    return resdata;
  },

  // 是否展示 skuBar
  showSkuBar(state, getters) {
    const { hideGoodsBottom } = state.env;
    const { showSkuBar } = state.skuExtraData;
    const { isPriceCalendar } = getters;
    if (getters.isPeriodbuy) {
      return !get(state, 'display.showForbidBuyBtn') && !getters.saleRemainTime;
    }

    // 隐藏底部购买按钮，同时需要把sku规格栏隐藏掉，不能购买
    if (hideGoodsBottom) {
      return false;
    }

    return showSkuBar || isPriceCalendar;
  },

  /** 推荐的支付方式 */
  recommend(state) {
    return state.payChannels?.find(itm => itm.recommend);
  },

  /** 是否展示支付方式推荐位 */
  showPriorUseFirstBar(state) {
    return state.displayPop.showPriorUseFirstBar;
  },

  // 是否 skuBar 不可选择
  isSkuBarDisabled(state) {
    // 是否店铺关门
    const { shopConfig } = state;

    if (shopConfig.isCloseBusiness) {
      return true;
    }
    return false;
  },

  // skubar 文案
  skuBarInfo(state, getters) {
    const { selectedSkuTree = [], selectedPropList = [] } = state.skuExtraData;
    const { tree: defaultTree = [] } = getters.skuData;
    const selected = [];
    const unselected = [];
    // 所有的都选上
    let isAllSelected = true;
    // 有任一选上
    let isAnySelected = false;

    const tree = selectedSkuTree.length
      ? selectedSkuTree
      : defaultTree.map((item) => ({ name: item.k }));
    tree.forEach((item) => {
      if (item.value) {
        selected.push(item.value.name);
        isAnySelected = true;
      } else {
        unselected.push(item.name);
        isAllSelected = false;
      }
    });

    selectedPropList.forEach((item) => {
      if (item.value && item.value.length > 0) {
        selected.push(...item.value.map((it) => it.name));
        isAnySelected = true;
      } else {
        unselected.push(item.name);
        isAllSelected = false;
      }
    });

    let showText = '';
    if (isAnySelected) {
      showText = selected.join(SKU_SEPERATOR);
    } else {
      showText = unselected.join(SKU_SEPERATOR);
    }
    return {
      isAnySelected,
      isAllSelected,
      showText,
    };
  },

  priceBarList(state) {
    const { priceCalendarData, skuExtraData } = state;
    const selectedSkuId = priceCalendarData.activeSkuId;
    const calendarData = priceCalendarData[selectedSkuId];
    const activeStockDate = skuExtraData.selectedSkuDate[0];

    if (!calendarData || !activeStockDate) {
      return priceCalendarData.priceBarList.map((item) => {
        item.selected = false;

        if (item.stockDate === activeStockDate) {
          item.selected = true;
        }

        return { ...item };
      });
    }

    const { monthDataList = [] } = calendarData;
    const date = new Date(activeStockDate);
    const activeDay = date.getDate();
    const priceBarList = [];

    // eslint-disable-next-line no-unused-vars
    for (const { year, month, days } of monthDataList) {
      const startDay = new Date(date);
      startDay.setFullYear(year);
      startDay.setMonth(month - 1);
      startDay.setDate(activeDay);

      if (startDay >= date) {
        // eslint-disable-next-line no-unused-vars
        for (const day of days) {
          startDay.setDate(day.date);

          if (day.isEnable && startDay >= date) {
            priceBarList.push({
              ...day,
              selected: activeStockDate === day.stockDate,
              price: day.footer,
              day: month + '-' + (day.date > 9 ? day.date : `0${day.date}`),
            });
          }

          if (priceBarList.length >= 4) return priceBarList;
        }
      }
    }

    return priceBarList;
  },
};
