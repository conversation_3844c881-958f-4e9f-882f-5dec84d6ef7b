export default {
  showSkuByPriceCalendarBar({ dispatch, commit, state }, item) {
    dispatch('showNearSku')
      .then(() => {
        const { itemSkuId } = item;
        const { list = [] } = state.skuData || {};
        let selectedSku = {};

        // 由于时序导致获取不到 selectedSku，导致显示 nan 的问题
        if (!state.skuData || !Object.keys(state.skuData).length) {
          return;
        }

        list.forEach(item => {
          if (item.id === +itemSkuId) {
            selectedSku = item;
          }
        });

        // 选中 sku
        return dispatch('updateSelectedSku', {
          selectedSku,
          selectedSkuComb: selectedSku,
        });
      })
      .then(() => commit('setSkuExtraData', { selectedSkuDate: [item.stockDate], selectedSkuDateId: item.dateSkuId }));
  },
};
