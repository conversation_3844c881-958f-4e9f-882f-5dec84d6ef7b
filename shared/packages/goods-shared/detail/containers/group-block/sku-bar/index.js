import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions, mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'SkuBar',
  state: ['skuConfig', 'skuExtraData'],
  getters: ['skuBarInfo', 'isSkuBarDisabled', 'isPriceCalendar'],

  mapData: mapState({
    skuBarDesc(state) {
      const { skuExtraData = {} } = state;
      return `共${skuExtraData.barPictureCount}种${skuExtraData.barPictureName}可选`;
    }
  }),

  methods: {
    ...mapActions(['showNearSku']),
    handleClick() {
      this.showNearSku();
    },
  }
});
