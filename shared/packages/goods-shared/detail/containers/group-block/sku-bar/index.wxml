<view  class="sku-bar">
  <user-authorize
    bind:next="handleClick"
    scene="click_buy_now"
    >
    <common-group
      title="{{ skuBarInfo.isAnySelected ? '已选' : '选择' }}"
      is-link="{{ !isSkuBarDisabled }}"
    >
      <view slot="value">
        <view class="sku-bar__text">
          {{ skuBarInfo.showText }}
        </view>

        <view
          wx:if="{{ skuExtraData.barPictureCount }}"
          class="sku-bar__imgs"
        >
          <image
            wx:for="{{ skuExtraData.barPictures }}"
            wx:for-item="img"
            wx:key="{{ index }}"
            src="{{ img }}"
            class="sku-bar__img"
          >
          </image>
          <text class="sku-bar__img-tag">{{ skuBarDesc }}</text>
        </view>
      </view>
    </common-group>
  </user-authorize>
  <slot/>
</view>
