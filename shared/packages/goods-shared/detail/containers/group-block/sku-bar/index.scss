@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.sku-bar {
  position: relative;
  font-size: 13px;

  &__text {
    color: $text-color;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    max-width: 90%;
    font-size: 14px;
    height: 20px;
    color: $text-color;
  }

  &__imgs {
    margin-top: 13px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }

  &__img {
    display: inline-block;
    width: 32px;
    height: 32px;
    margin-right: 5px;
    border-radius: 5px;
  }

  &__img-tag {
    display: inline-block;
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    background-color: #f7f8fa;
    color: $gray-dark;
    border-radius: 4px;
    font-size: 12px;

    @include ellipsis;
  }

  .common-group__arrow {
    position: absolute;
    top: 10px;
    right: 16px;
  }
}
