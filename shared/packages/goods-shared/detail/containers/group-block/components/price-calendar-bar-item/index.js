import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions } from '@youzan/vanx';

GoodsComponent({
  name: 'price-calendar-bar-item',

  getters: ['isSkuBarDisabled'],

  properties: {
    item: Object,
  },

  methods: {
    ...mapActions(['showSkuByPriceCalendarBar']),

    handleClick() {
      if (!this.data.isSkuBarDisabled) {
        this.showSkuByPriceCalendarBar(this.data.item);
      }
    },
  }
});
