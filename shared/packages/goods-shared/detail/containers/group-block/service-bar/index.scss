@import '~shared/common/css/_variables.scss';

.service-bar {
  &__intro {
    padding: 10px 0;

    &:first-child {
      padding-top: 0;
    }

    &-title {
      font-size: 14px;
      font-weight: bold;
      color: $text-color;
    }

    &-detail {
      margin-top: 8px;
      font-size: 13px;
      color: $gray-dark;
      line-height: 18px;
      flex: 1;
    }

    &-content {
      display: flex;
    }

    &-arrow {
      margin-top: 8px;
      color: #969799;
      font-size: 16px;
      margin-left: 15px;
    }

    &-text {
      &--blue {
        color: #155bd4;
      }
    }
  }

  &__popup {
    font-size: 14px;
    display: flex;

    .label {
      white-space: nowrap;
    }
  }
}
