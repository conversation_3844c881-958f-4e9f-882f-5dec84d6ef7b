
<view class="service-bar">
  <common-cell border="{{ false }}" value="{{ serviceBarTitle }}"
    title="服务"
    is-link="{{ true }}"
    bind:tap="showPopUp"
    value-class="u-activity-intro-title"
  />
  <common-pop
    show="{{ isShow }}"
    class="service-bar__popup"
    bind:btn-click="hidePopUp"
    bind:close="hidePopUp"
  >
    <view
      wx:for="{{ serviceDescList }}"
      wx:for-item="item"
      wx:key="tag"
      key="{{ item.tag }}"
      class="service-bar__intro"
    >
      <view class="service-bar__intro-title">{{ item.tag }}</view>
      <!-- TODO: url需要page-path处理下-->
      <navigator class="service-bar__intro-content" url="{{ item.url }}">
        <view wx:if="{{item.descList}}" class="service-bar__intro-detail">
          <text wx:for="{{item.descList}}" wx:key="index" wx:for-item="value" class="service-bar__intro-text {{value.className}}">
            {{ value.text }}
          </text>
        </view>
        <view wx:else class="service-bar__intro-detail">
          {{ item.desc }}
        </view>
        <van-icon wx:if="{{ item.url && item.noArrow !== true }}" class="service-bar__intro-arrow" size="16" name="arrow"/>
      </navigator>
    </view>
  </common-pop>
</view>
