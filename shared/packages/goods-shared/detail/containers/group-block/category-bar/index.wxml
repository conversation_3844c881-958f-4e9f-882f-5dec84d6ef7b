
<view class="category-bar">
    <common-cell 
   border="{{ false }}" 
   value="{{ categoryBarTitle }}" 
   title="参数" 
   is-link="true" 
   bind:tap="showPopUp"
   value-class="u-activity-intro-title"
   />
  <common-pop 
      show="{{ isShow }}" 
      position="bottom"
      class="category-bar__popup"       
      bind:btn-click="hidePopUp"
      bind:close="hidePopUp">
    <view class="category-title">产品参数</view>
    <view
      wx:for="{{ categoryBarValue }}"
      wx:for-item="item"
      key="{{ item.key }}"
      class="category-item"
    >
        <view class="category-item__label">
          <view class="label-title">{{ item.proName }}</view>
        </view>
        <view class="category-item__detail">
          <view class="detail-item">{{ item.valNames }}</view>
        </view>
    </view>
</view>
