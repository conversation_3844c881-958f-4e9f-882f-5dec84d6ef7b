import { GoodsComponent } from '@goods/common/vanx-component';

const app = getApp();
GoodsComponent({
  name: 'CategoryBar',

  // components: {
  //   [CommonPop.name]: CommonPop,
  //   [CommonCell.name]: CommonCell,

  // },

  getters: [
    'categoryBarTitle',
    'categoryBarValue',
  ],

  data: {
    isShow: false,
  },

  methods: {
    showPopUp() {
      this.isShow = true;
      app.logger
        && app.logger.log({
          et: 'click',
          ei: 'click_category_parameters',
          en: '点击类目参数',
          params: {
            click_category_parameters: 'true',
          },
        });
      this.setYZData({
        isShow: true,
      });
    },
    hidePopUp() {
      this.setYZData({
        isShow: false,
      });
    },
  },
});
