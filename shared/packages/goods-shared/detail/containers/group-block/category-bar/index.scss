@import '~shared/common/css/_variables.scss';

.category-bar__popup {
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .common-popup__content {
    padding-top: 0 !important;
  }
  .category-title {
    font-size: 16px;
    height: 48px;
    text-align: center;
    line-height: 48px;
    font-weight: bold;
  }
}

.category-item {
  display: flex;
  padding: 13px 0;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  max-height: 98px;
  font-size: 14px;
  box-sizing: border-box;
  border-bottom: 0.5px solid #ebedf0;
  .category-item__label {
    width: 119px;
    min-height: 18px;
    max-height: 64px;
    color: #646566;
    display: flex;
    align-items: center;
    margin-right: 16px;
    line-height: 18px;
    .label-title {
      max-width: 119px;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
  .category-item__detail {
    min-height: 18px;
    width: 100%;
    color: #323233;
    display: flex;
    align-items: center;
    line-height: 18px;
    font-size: 14px;
    scrollbar-face-color: red;
    .detail-item {
      overflow-y: auto;
      word-break: break-all;
      max-height: 72px;
      width: 100%;
    }
  }
}

.common-popup__button-wrapper {
  margin-bottom: 34px;
}

::-webkit-scrollbar {
  width: 4px;
  background-color: transparent;
  border-radius: 10px;
}
::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: transparent;
}

// 定义滑动模块颜色，边角
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #ebedf0;
}
