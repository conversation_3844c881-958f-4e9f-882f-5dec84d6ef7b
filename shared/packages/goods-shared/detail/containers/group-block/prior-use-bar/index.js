import { GoodsComponent } from '@goods/common/vanx-component';

GoodsComponent({
  name: 'prior-use-bar',

  getters: [
    'recommend',
    'hideButton',
    'showPriorUseFirstBar'
  ],

  state: ['goods'],

  methods: {
    updatePopUp() {
      const { recommend } = this.$store.getters;
      const app = getApp();
      const { payChannelsAbTraceId: abTraceId, goods: { id: goodsId } } = this.$store.state;

      app.logger?.log({
        et: 'custom',
        ei: 'wsc_g_pay_channels_recommend_click',
        en: '商详页点击支付方式推荐位',
        params: {
          goodsId,
          abTraceId,
        },
      });
      if (recommend.payChannel === 'PRIOR_USE') {
        this.$commit('updateDisplayPopByKey', 'showPriorUseFirstBar');
      } else {
        this.$dispatch('showNearSku');
        this.$commit('SET_PAYWAYS', 'installment');
      }
    },
    toUse() {
      this.$dispatch('showNearSku');
      this.$commit('SET_PAYWAYS', 'PRIOR_USE');
    },
  }
});
