@import '~mixins/index.scss';

.icon {
  display: inline-block;
  width: 16px;
  background-size: cover;
  &.PRIOR_USE{
    height: 16px;
    background-image: url(https://b.yzcdn.cn/assets-cashier/icon/prior-use-wechat2.png);
  }
  &.INSTALMENT{
    height: 12px;
    background-image: url(https://img01.yzcdn.cn/public_files/2019/03/21/0a1e88aeac0387f39b64e2930ef35407.png);
  }
}

.service-bar__intro-arrow{
  flex: 1;
  text-align: right;
  margin:0 -3px;
  color:rgb(150, 151, 153);
}

.prior_use {
  display: flex;
  align-items: center;
  height: 44px;
  box-sizing: border-box;
  padding: 9px 16px;
  border-left: 0;
  border-right: 0;

  &__first {
    background: $white;

    &__close{
      position: absolute;
      right:16px;
      top: 12px;
      width: 22px;
      height: 22px;
      padding: 4px;
      box-sizing: border-box;
    }

    div{
      font-size:12px;
      color:$gray-dark;
      text-align: center;
      margin: 4px auto;
      display: block;
      em {
        color:$text-color;
        font-weight: bold;
        font-style: normal;
      }
    }

    label{
      display: block;
      font-size: 14px;
      line-height: 18px;
      font-weight: bold;
      color:$text-color;
      padding:32px 0 2px;
    }

    span{
      font-size:12px;
      color:$gray-dark;
      line-height: 18px;
    }

    &__button {
      line-height: 36px;
      border-radius: 18px;
      text-align: center;
      font-size: 14px;
      color:#fff;
      background: #EE0A24;
      display: block;
      margin-bottom: 20px;
    }

    &__img {
      &__use {
        font-size: 18px !important;
        line-height: 24px;
        color:#323233 !important;
        display: flex !important;
        align-items: center;
        justify-content: center;
        image{
          width: 24px;
          height: 24px;
          margin-right: 4px;
        }
      }
      &__step{
        max-width: 100%;
        display: block;
        height: 16vw;
        padding: 6vw 7vw 8px;
        box-sizing: border-box;
        width: 100%;
        &+ul{
          display: flex;
          justify-content: space-around;
          font-size: 12px;
          color:#39393A;
          position: relative;
          &:before{
            content: "确认收货";
            color: #969799;
            font-size: 2.7vw;
            position: absolute;
            right: 17.5vw;
            top: -10vw;
          }
          li{
            flex:1;
            text-align: center;
          }
        }
      }
    }
  }

  &__label {
    margin: 0 4px;
    font-size: 14px;
    font-weight: 500;
    color: #323233;
  }

  &__detail {
    font-size: 14px;
    color: $gray-dark;
    display: flex;
    align-items: center;
    height: 16px;
    padding-left: 4px;
    position: relative;
    &:before{
      content:'';
      display: block;
      position: absolute;
      height: 12px;
      width: 1px;
      border-left: 1px solid $gray-dark;
      top: 2px;
      left: 0;
    }
  }
}