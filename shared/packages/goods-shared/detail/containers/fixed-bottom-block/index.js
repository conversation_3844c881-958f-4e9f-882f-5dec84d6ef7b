import { GoodsComponent } from '@goods/common/vanx-component';
import { mapMutations } from '@youzan/vanx';
import { timerBehaivor } from '@goods/common/behaviors';

GoodsComponent({
  behaviors: [timerBehaivor],
  name: 'fixed-bottom-block',

  state: ['displayPop'],

  ready() {
    this.timeRunner();
  },

  methods: {
    ...mapMutations(['SET_BOTTOM_HRIGHT']),
    timeRunner() {
      this.tid = !this.hasDetached && setInterval(() => {
        this.queryHeight();
      }, 2000);
    },
    queryHeight() {
      this.createSelectorQuery().select('#module-bottom').boundingClientRect((rect) => {
        if (!rect) return;

        const height = rect.bottom - rect.top;

        if (height === this._height) return;
        this._height = height;
        this.SET_BOTTOM_HRIGHT(height);
      }).exec();
    },
  },

  detached() {
    this.hasDetached = true; // 这个存在比ready先执行的情况
    clearInterval(this.tid);
    this.tid = null;
  },
});
