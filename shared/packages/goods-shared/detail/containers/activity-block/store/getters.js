export default {
  showActivityBlock(state, getters) {
    // 定金预售显示活动说明
    if (getters.isDepositPresale && !getters.isDepositPresaleOver) {
      // 支持定制隐藏定金预售活动模块
      if (!state.saas.activityBlock.showDepositPresalInfo) {
        return false;
      }
      return true;
    }
    return false;
  },
  // 活动规则描述
  activityRules() {
    // 支持重写
    return {
      label: '',
      rules: [],
    };
  },
};
