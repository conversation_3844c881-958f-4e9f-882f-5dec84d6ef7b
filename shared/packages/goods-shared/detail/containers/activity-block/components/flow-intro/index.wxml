<template name="tag">
  <view class="flow-intro__progress-bar" style="background-color: {{ key < activeIndex ? themeMainColor : '' }}" />
  <view class="flow-intro__progress-point" style="background-color: {{ key < activeIndex ? themeMainColor : '' }}" />
</template>

<activity-intro is-link="{{ false }}" title="{{ title }}">
  <view class="flow-intro" wx:if="{{ progress.length > 0 }}">
    <view class="flow-intro__progress">
      <view class="flow-intro__progress-point" style="background-color: {{ themeMainColor }}" />
      <template is="tag" wx:for="{{ progressList }}" wx:for-item="key" data="{{ key, activeIndex, themeMainColor }}" />
    </view>

    <view class="flow-intro__progress-text">
      <view class="flow-intro__progress-text-item"
        style="color: {{ index <= activeIndex ? themeMainColor : '' }}" wx:for="{{progress}}"
        wx:for-item="item" wx:key="index">
        {{ item.text }}
      </view>
    </view>

    <view wx:if="desc" class="flow-intro__desc">{{ desc }}</view>
  </view>
</activity-intro>