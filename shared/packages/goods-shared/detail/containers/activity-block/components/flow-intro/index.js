import { GoodsComponent } from '@goods/common/vanx-component';
import theme from 'shared/common/components/theme-view/theme';

GoodsComponent({
  name: 'flow-intro',

  data: {
    themeMainColor: '',
    progressList: -1,
    activeIndex: -1,
  },

  properties: {
    title: {
      type: String,
      default: '',
    },

    desc: {
      type: String,
      default: '',
    },

    progress: {
      type: Array,
      default: [],
      observer(val) {
        this.setYZData({
          progressList: val.length - 1,
          activeIndex: val.findIndex(({ active }) => active),
        });
      }
    }
  },

  attached() {
    theme.getThemeColor('main-bg').then((color) => {
      this.setYZData({
        themeMainColor: color
      });
    });
  },
});
