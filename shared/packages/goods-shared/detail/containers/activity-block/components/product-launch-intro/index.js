import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';
import { moment as formatDate } from 'utils/time';
import { get } from '@goods/utils';

GoodsComponent({
  name: 'product-launch-intro',

  getters: ['isProductLaunch'],

  mapData: {
    ...mapState({
      isWaitSold(state) {
        return Boolean(state.goodsActivity.waitToSold);
      },

      desc(state) {
        const startTime = get(state, 'goods.startSoldTime');
        const endTime = get(state, 'marketActivity.productLaunch.endTime');
        return `抢购时间：${formatDate(startTime, 'YYYY.MM.DD HH:mm:ss')} 至 ${formatDate(
          endTime,
          'YYYY.MM.DD HH:mm:ss'
        )}`;
      },

      title(state) {
        return state.isWaitSold
          ? '[预约中] 预约后商品将加入购物车，及时抢购哦'
          : '[抢购中] 商品火热开售中，尽快加购下单哦';
      }
    }),

    progress() {
      return [
        {
          text: '预约',
          active: this.data.isWaitSold
        },
        {
          text: '抢购',
          active: !this.data.isWaitSold
        },
        {
          text: '发货'
        }
      ];
    }
  }
});
