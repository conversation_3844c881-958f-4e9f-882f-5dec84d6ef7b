@import '~mixins/index.scss';

.m-activity {
  position: relative;
  display: block;
  width: 100%;
  margin-top: 12px;
  background-color: $white;
  font-size: 14px;
  box-sizing: border-box;
  overflow: hidden;

  &-intro {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.u-activity-intro-popup {
  display: flex;
  font-size: 13px;
  line-height: 1.5;

  .label {
    white-space: nowrap;
  }

  h5 {
    font-size: 16px;
    margin-bottom: 18px;
  }

  &__p {
    margin: 10px 0;
  }
}