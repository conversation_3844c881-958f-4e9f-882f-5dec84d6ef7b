@import '~shared/common/css/_variables.scss';

.package-buy {
  position: relative;
  display: block;
  margin-top: 12px;
  background: $white;
  font-size: 14px;

  &__header {
    padding-right: 10px;
    line-height: 20px;
    border-bottom: 1px solid #979797;

    > span {
      font-size: 12px;
    }
  }

  &__discount {
    padding-left: 5px;
    color: $orange;
  }

  &__count {
    float: right;
    color: $gray-dark;
  }

  &__icon {
    position: absolute;
    top: 14px;
    right: 10px;
    font-size: 12px;
    color: $gray-dark;
  }

  &__list {
    position: relative;
    overflow-x: auto;
    white-space: nowrap;
    font-size: 0;
    z-index: 1;
    display: flex;
    -webkit-overflow-scrolling: touch;
  }

  &__item {
    position: relative;
    box-sizing: border-box;
    font-size: 12px;
    width: 252px;
    margin-right: 32px;
    line-height: 16px;
    min-width: 252px;

    &-img {
      display: inline-block;
      margin-right: 8px;
      width: 90px;
      height: 90px;
      background-position: center;
      background-size: 90px 90px;
      border-radius: 4px;
    }

    &-price {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      padding: 0 10px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, .9);
    }

    .item-content {
      margin-top: 12px;
      float: right;
      width: calc(100% - 100px);
      text-align: left;
    }

    .item-content--top {
      margin-top: 22px;
    }

    .item__name {
      font-size: 14px;
      font-family: PingFangSC-Medium;
      color: #323233;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .item__price {
      margin: 10px 0 8px;
      font-size: 14px;
      color: #f44;
    }

    .item__save {
      color: #969799;
      font-size: 12px;
    }
  }

  &__item--long {
    margin-right: 0;
    width: 100%;
  }
}

.package-buy-lists {
  padding: 0 15px 10px !important;
}

.cell-title__class {
  font-weight: bold;
  font-size: 14px;
  font-family: PingFangSC-Semibold;
}
