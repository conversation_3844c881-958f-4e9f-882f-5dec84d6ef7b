<navigator
  url="{{ packageBuy.url }}"
  class="package-buy"
>
  <van-cell-group
    border="{{ false }}"
    class="discount-packages-container"
  >
    <van-cell
      is-link="{{ isMultiPackage }}"
      border="{{ false }}"
      title="优惠套餐"
      class="align-right"
      title-class="cell-title__class"
    >
      <span
        wx:if="{{ isMultiPackage }}"
        class="van-font-12 van-c-gray-darker"
      >
        查看全部
      </span>
    </van-cell>

    <van-cell
      border="{{ false }}"
      custom-class="package-buy-lists"
      value-class="cell-value__class"
    >
      <view class="package-buy__list">
        <view
          wx:for="{{ packageBuy.goodsList }}"
          wx:key="id"
          class="package-buy__item {{ !isMultiPackage ? 'package-buy__item--long' : '' }}"
        >
          <view
            class="package-buy__item-img"
            style="background-image: url({{ item.imageUrl }});"
          />
          <view
            class="item-content {{ item.planType === 1 || !item.showPreference ? 'item-content--top': '' }} "
          >
            <view class="item__name">
              {{ item.title }}
            </view>
            <theme-view
              color="general"
              custom-class="item__price"
            >
              套餐价：￥{{ item.price }}{{ item.planType === 1 ? '起' : '' }}
            </theme-view>
            <view
              wx:if="{{ item.planType !== 1 && item.showPreference }}"
              class="item__save"
            >
              {{ item.hasMaxSavings ? '最多' : '' }}可省￥{{ item.saveMoneyCn }}
            </view>
          </view>
        </view>
      </view>
    </van-cell>
  </van-cell-group>
</navigator>
