import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'PromotionBlock',

  state: ['goods', 'isFastBuy'],

  mapData: mapState({
    forceShowpromotion(state) {
      return !state.design.find((item) => item.type === 'base-info-v2-block');
    },
  }),
  getters: [
    // 是否展示优惠券列表
    'showGoodsCoupon',
    // 是否显示促销活动列表
    'showGoodsPromotion',
    'showPackageBuy',
    'showTopGoodsPromotion',
    'flagTopGoodsPromotion',
  ],
});
