import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions } from '@youzan/vanx';

GoodsComponent({
  name: 'CashBackRule',

  state: ['userCenterTitle'],

  getters: ['cashBackRuleStr', 'cashBackProRuleStr', 'isCashbackPro', 'isCashbackProStoredValue', 'cashbackPro'],

  ready() {
    // 获取个人中心的标题：userCenterTitle
    this.getUserCenterTitle();
  },

  methods: {
    ...mapActions(['getUserCenterTitle'])
  }
});
