@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.promotion-card {
  display: flex;
  height: 84px;
  position: relative;
  margin-bottom: 12px;
  color: #ee0a24;
  font-size: 12px;
  line-height: 16px;
  border-radius: 8px;
  overflow: hidden;

  &__inner {
    display: flex;
    width: 100%;
    height: 100%;
  }

  &--showcase {
    height: 100px;

    .promotion-card__arrow {
      top: 60%;
    }

    .promotion-card__action {
      width: 10%;
    }
  }

  &--disabled {
    background-color: #f7f8fa;
    color: #c8c9cc;

    .promotion-card__btn {
      color: #c8c9cc;
      background-color: transparent;
    }
  }

  &__head {
    display: flex;
    box-sizing: border-box;
    // align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 0 0 0 16px;
    width: 29%;
  }

  &__head-top {
    max-width: 100px;

    @include ellipsis;
  }

  &__head-value {
    font-size: 14px;
    line-height: 30px;
    font-weight: bold;

    &.price-length-0,
    &.price-length-1,
    &.price-length-2,
    &.price-length-3,
    &.price-length-4 {
      font-size: 26px;
    }

    &.price-length-5 {
      font-size: 16px;
    }

    &.price-length-6 {
      font-size: 14px;
    }

    &-unit {
      margin-left: 2px;
    }
  }

  &__tag {
    position: absolute;
    left: 0;
    top: 0;
    width: 32px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    border-bottom-right-radius: 4px;

    &-value {
      font-size: 10px;
    }
  }

  &__condition {
    margin-top: 5px;
    max-width: 100px;
    color: $gray-dark;

    &-1 {
      margin-top: 2px;
    }

    @include multi-ellipsis(2);
  }

  &__content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    padding: 10px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    &-showcase {
      display: flex;
    }

    &-image-wrapper {
      margin-left: 10px;
      width: 56px;
      height: 56px;
      position: relative;
      border-radius: 4px;
      overflow: hidden;

      &:first-child {
        margin-left: 0;
      }
    }

    &-image {
      width: 100%;
      height: 100%;
    }

    &-image-desc {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 14px;
      line-height: 14px;
      background-color: rgba(#000, 0.24);
      color: $white;
      font-size: 10px;
      text-align: center;
      font-weight: bold;
    }

    &-title2 {
      margin-bottom: 5px;
      font-size: 12px;
      color: $text-color;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  &__title {
    display: inline;
    font-size: 14px;
    line-height: 20px;
    color: $text-color;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &__sub-title {
    margin-top: 10px;
    color: $gray-dark;
  }

  &__action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 10%;

    &--has-btn {
      width: 23.5%;
    }
  }

  &__btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 22px;
    color: #fff;
    font-size: 12px;
    background-color: #ee0a24;
    border-radius: 12px;
    line-height: 22px;
    text-align: center;
    position: relative;
    z-index: 2; // 超过“已领” 标
  }

  &__arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    margin-top: -9px;
    height: 18px;
    width: 18px;
  }

  &__got {
    position: absolute;
    bottom: -6px;
    right: -2px;
    width: 40px;
    height: 40px;
    font-size: 12px;

    &-inner {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 0.5px solid;
      opacity: 0.8;
    }

    &-cnt {
      position: relative;
      font-size: 12px;
      text-align: center;
      transform: rotate(-45deg);

      &-inner {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 0.5px dotted;
      }
    }
  }
}
