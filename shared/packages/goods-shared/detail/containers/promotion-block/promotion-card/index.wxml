<theme-view
  class="promotion-card {{ goodsList && goodsList.length > 0 ? 'promotion-card--showcase' : '' }}"
  custom-class="promotion-card__inner"
  bg="main-bg"
  opacity="0.04"
  bind:tap="handleClick"
>
  <view class="promotion-card__head">
    <view class="promotion-card__head-top">
      <theme-view
        color="general"
        class="promotion-card__head-value {{ 'price-length-' + headValue.length }}"
        inner-style="display: inline-block; font-weight: bold;"
      >
        {{ headValue }}
      </theme-view>
      <theme-view
        wx:if="{{ unit }}"
        class="promotion-card__head-value-unit"
        color="general"
        inner-style="display: inline-block;"
      >{{ unit }}</theme-view>
    </view>
    <view
      wx:if="{{ condition }}"
      class="promotion-card__condition"
    >{{ condition }}</view>
    <view
      wx:if="{{ extraCondition }}"
      class="promotion-card__condition promotion-card__condition-1"
    >{{ extraCondition }}</view>
  </view>
  <theme-view
    class="promotion-card__tag"
    bg="main-bg"
    opacity="0.2"
    bind:tap="handleClick"
    inner-style="border-bottom-right-radius: 4px;"
  >
    <theme-view
      wx:if="{{ tag }}"
      color="general"
      class="promotion-card__tag-value"
      inner-style="display: inline-block;"
    >
      {{ tag }}
    </theme-view>
  </theme-view>
  <view class="promotion-card__content">
    <block wx:if="{{ goodsList && goodsList.length > 0 }}">
      <view class="promotion-card__content-title2">{{ title }}</view>
      <view class="promotion-card__content-showcase">
        <view
          wx:for="{{ goodsList }}"
          wx:key="index"
          wx:for-item="goods"
          class="promotion-card__content-image-wrapper"
        >
          <image
            class="promotion-card__content-image"
            src="{{ goods.imageUrl }}"
          />
          <view class="promotion-card__content-image-desc">￥{{ goods.price }}</view>
        </view>
      </view>
    </block>

    <block wx:else>
      <text
        class="promotion-card__title"
      >{{ title }}</text>
      <text
        wx:if="{{ valid }}"
        class="promotion-card__sub-title"
      >{{ valid }}</text>
    </block>
  </view>
  <theme-view class="promotion-card__action {{ actionText ? 'promotion-card__action--has-btn' : '' }}">
    <theme-view
      wx:if="{{ actionText && !got }}"
      bg="main-bg"
      color="main-text"
      custom-class="promotion-card__btn"
    >{{ actionText }} </theme-view>
    <theme-view
      wx:elif="{{ hasArrow }}"
      color="general"
      class="promotion-card__arrow"
    >
      <van-icon
        name="arrow"
        size="18px"
      />
    </theme-view>
  </theme-view>
  <theme-view
    wx:if="{{ got }}"
    class="promotion-card__got"
    custom-class="promotion-card__got-inner"
    border-after="general"
    color="general"
  >
    <theme-view
      class="promotion-card__got-cnt"
      custom-class="promotion-card__got-cnt-inner"
      color="general"
      bg="main-bg"
      opacity="0.1"
      border-after="general"
    >
      已领
    </theme-view>
  </theme-view>
</theme-view>
