import { GoodsComponent } from '@goods/common/vanx-component';

GoodsComponent({
  name: 'PromotionCard',

  properties: {
    // 核心卖点
    headValue: String,

    // 核心卖点单位
    unit: String,

    // 核心卖点 下面的条件
    condition: String,

    // 核心卖点 下面的条件
    extraCondition: String,

    // 左上角标签
    tag: String,

    // 是否有箭头，支持点击
    hasArrow: Boolean,

    // 主要标题
    title: String,

    // 有效期
    valid: String,

    // 行动点文案
    actionText: String,

    // 商品列表，显示图片
    goodsList: Array,

    // 是否领取优惠安群
    got: Boolean,
  },

  methods: {
    handleClick(e) {
      this.triggerEvent('click', e);
    },
  },
});
