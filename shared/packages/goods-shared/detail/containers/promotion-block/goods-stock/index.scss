@import '~shared/common/css/_variables.scss';

.goods-stock {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  &__postage {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;

    &-label {
      color: $gray-dark;
      margin-right: 12px;
      font-size: 14px;
    }
  }

  &__sold-num {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    color: $gray-dark;
    font-size: 14px;

    & > text:nth-child(2) {
      display: inline-flex;
      align-items: center;

      &::before {
        content: '|';
        display: inline-block;
        height: 18px;
        font-size: 16px;
        margin: 0 5px 2px;
        color: #dcdee0;
      }
    }
  }

  &__sep {
    color: $active-color;
    font-size: 14px;
    margin-left: 8px;
  }

  &__value {
    font-size: 14px;
    color: $text-color;
    vertical-align: middle;
  }

  &__icon {
    top: -1px;
    vertical-align: middle;
  }
}
