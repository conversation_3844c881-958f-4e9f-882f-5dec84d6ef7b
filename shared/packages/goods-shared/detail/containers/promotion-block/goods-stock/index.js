import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'GoodsStock',

  externalClasses: ['container-class'],

  getters: [
    'showPostageText',
    'showSoldNum',
    'goodsSoldNumText',
    'showDeliveryBar',
    'showGoodsStock',
    'showSoldNum',
  ],

  mapData: mapState({
    stockNum(state) {
      const number = state.goods.stockNum;
      return number >= 10000 ? `${(number / 10000).toFixed(1)}万` : number;
    }
  })
});
