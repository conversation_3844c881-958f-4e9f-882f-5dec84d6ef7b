<view class="goods-promotion container-class" wx:if="{{ isShowGoodsPromotion }}">
  <view class="goods-promotion__list" bind:tap="handlePromotionCoupon">
    <text class="goods-promotion__label label-class">优惠</text>
    <view class="goods-promotion__info">
      <view
        wx:if="{{ orderPromotionLimit }}"
        class="goods-promotion__limit-desc"
      >促销信息加载中，请以最终结算价为准</view>
      <block
        wx:else
      >
        <view
          class="goods-promotion__desc"
          wx:for="{{ goodsPromotionList }}"
          wx:key="key"
        >
          <common-tag
            class="goods-promotion__tag"
            custom-class="goods-promotion__tag-container"
            container-class="goods-promotion__tag-th-container"
          >
            <view class="theme__color">{{ item.title }}</view>
          </common-tag>

          <!-- 优惠券 -->
          <block wx:if="{{ item.key === 'umpCouponList' }}">
            <template is="coupon-list" data="{{ couponList: item.couponList }}" />
          </block>

          <!-- 赠品列表 -->
          <block wx:elif="{{ item.key === 'present' }}">
            <view class="goods-promotion__name">
              <template is="present-list-block" data="{{ present: item.presentOutList }}" />
            </view>
          </block>

          <text
            wx:else
            class="goods-promotion__name"
          >{{ item.desc }}</text>
        </view>
      </block>
    </view>
    <van-icon
      wx:if="{{ !orderPromotionLimit }}"
      class="goods-promotion__arrow"
      name="arrow"
      style="color: #969799; font-size: 16px;"
    />
  </view>
</view>

<promotion-pop
  show="{{ displayPop.showCouponPopUp }}"
  bind:close="toggleCouponPopUp"
/>

<template name="coupon-list">
  <view class="goods-promotion__coupon-info">
    <theme-view
      wx:for="{{ couponList }}"
      wx:key="id"
      wx:for-item="coupon"
      class="goods-promotion__coupon-desc-item"
      bg="main-bg"
      color="general"
      opacity="0.1"
      inner-style="border-radius: 2px;"
    >
      <text class="goods-promotion__coupon-desc-item-text">{{ coupon.useThresholdAndValueCopywriting }}</text>
    </theme-view>
  </view>
</template>

<template name="present-list-block">
  <view
    wx:for="{{ present }}"
    wx:for-item="presentLevelItem"
    wx:for-index="presentLevelIndex"
    wx:key="presentLevelIndex"
    style="display:inline;"
  >
    {{ presentLevelItem.presentPrefix }}
    <view
      wx:for="{{ presentLevelItem.goodsModelsV2 }}"
      wx:for-item="presentItem"
      wx:for-index="presentIndex"
      wx:key="presentIndex"
      class="goods-promotion__name-inner"
      style="display:inline;"
    >
      送{{ presentItem.num || 1 }}件
      <navigator
        url="/pages/goods/detail/index?alias={{ presentItem.alias }}"
        class="goods-promotion__present-link"
        style="display:inline;"
      >
        <theme-view color="general"
          inner-style="display:inline; text-overflow: ellipsis;"
        >
          {{ presentItem.title }}
        </theme-view>
      </navigator>
      {{ presentLevelItem.presentSuffix ? '、' : ';' }}
    </view>
    <text>{{ presentLevelItem.presentSuffix || '' }}</text>
  </view>
</template>
