@import '~shared/common/css/_variables.scss';

.goods-promotion {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 16px !important;
  min-height: 44px;

  &__list {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;

    .goods-promotion__label {
      top: -1px;
    }
  }

  &__info {
    position: relative;
    display: inline-block;
    max-width: 85%;
    top: -2px;
  }

  &__coupon {
    &-info {
      position: relative;
      width: 100%;
      height: 16px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: clip;
    }

    &-desc-item {
      position: relative;
      display: inline-block;
      height: 16px;
      margin-left: 8px;
      text-align: center;
      line-height: 16px;
      background-size: 100% 100%;
      border-radius: 2px;
      vertical-align: top;

      &-text {
        font-size: 12px;
        padding: 0 4px;
        display: block;
      }

      // &:first-child {
      //   margin-left: 4px;
      // }
    }
  }

  &__limit-desc {
    position: relative;
    color: $gray-dark;
    top: 2px;
  }

  &__desc {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 2px 0;
    margin-top: 8px;
    line-height: 20px;

    &:first-of-type {
      margin-top: 0;
    }
  }

  &__list &__desc {
    align-items: center;
  }

  &__tag {
    height: 16px;

    &-container {
      display: block !important;
      height: 16px;
    }

    &-th-container {
      display: block !important;
      height: 16px;
    }
  }

  &__name {
    font-size: 12px;
    margin-left: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: $text-color;
    position: relative;
    top: 1px;

    &-inner {
      display: inline;
    }
  }

  &__detail {
    margin-left: 8px;
  }

  &__time {
    color: $gray-dark;
  }

  &__arrow {
    position: absolute;
    right: 0;
    top: 0;
    margin-right: -3px;
  }

  &__nav-arrow {
    position: absolute;
    right: 0;
    top: 6px !important;
  }

  &__present-link {
    display: inline-block;
    padding: 0 2px;
  }
}
