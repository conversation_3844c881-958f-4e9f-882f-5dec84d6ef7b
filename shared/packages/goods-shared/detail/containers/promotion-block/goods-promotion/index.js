import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions } from '@youzan/vanx';

GoodsComponent({
  name: 'GoodsPromotion',

  externalClasses: ['container-class', 'label-class'],

  state: ['orderPromotionLimit', 'displayPop', 'isShowGoodsPromotion'],

  getters: ['goodsPromotionList'],

  data: {
    isShow: false,
    isShowMore: false,
    isShowCashBackRule: false,
    moreText: {},
  },

  methods: {
    ...mapActions([
      'hideMoreOptimalCoupon',
      'toggleCouponPopUp',
      'handlePromotionCoupon'
    ]),
  },
});
