import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions, mapState } from '@youzan/vanx';
import navigate from 'shared/utils/navigate';
import openWebView from 'shared/utils/open-web-view';
import {
  getUrl,
  MEET_REDUCE,
  PACKAGE_BUY,
  SECOND_HALF,
  PLUS_BUY,
} from '@goods/common/page-path';

GoodsComponent({
  name: 'PromotionPop',

  properties: {
    show: Boolean,
  },

  data: {
    isShow: false,
    isShowMore: false,
    isShowCashBackRule: false,
    moreText: {},
  },

  mapData: {
    ...mapState({
      accountUnionScene: (state = {}) =>
        state.businessConfigs && state.businessConfigs.accountUnionScene,
      couponType: (state = {}) =>
        state.businessConfigs && state.businessConfigs.couponType,
    }),
  },

  state: ['goods', 'umpCouponList', 'optimalCouponList', 'orderActivity'],

  getters: [
    'goodsPromotion',
    'optimalCouponShowList',
    'formatedGoodsPromotion',
    'umpCouponShowList',
    'kdtId',
    'cashBackProPopUpTitle',
    'optimalPreferentialPrice',
    'showOptimalCouponMoreBtn',
  ],

  methods: {
    ...mapActions([
      'toggleCouponPopUp',
      'handleGetCoupon',
      'showMoreOptimalCoupon',
    ]),

    // 查看更多最佳券
    unfoldOptimalCouponList() {
      this.showMoreOptimalCoupon();
    },

    hidePopUp() {
      this.triggerEvent('close');
    },

    handleCouponClick({ currentTarget: { dataset } }) {
      const { index = 0, umpType = this.data.couponType.UMP } = dataset;
      let coupon = null;
      if (umpType === this.data.couponType.UMP) {
        coupon = this.data.umpCouponShowList[index];
      } else {
        coupon = this.data.optimalCouponShowList[index];
      }

      if (coupon.disabled) {
        return;
      }

      this.handleGetCoupon({
        id: coupon.id,
        type: umpType,
      });
    },

    onPromotionClick({ currentTarget = {} }) {
      const { index, type } = currentTarget.dataset;
      const { goodsPromotion = [] } = this.data;

      const promotion = goodsPromotion[index];

      if (!promotion) {
        return;
      }

      if (type === 'cashbackPro') {
        this.showCashBackRule();
        this.toggleCouponPopUp();
        return;
      }

      if (type === 'meetReduce' || type === 'present') {
        this.openActivity('meetReduce', MEET_REDUCE);
      } else if (type === 'secondHalfDiscount') {
        this.openActivity(type, SECOND_HALF);
      } else if (type === 'bale') {
        this.openActivity(type, PACKAGE_BUY);
      } else if (type === 'plusBuy') {
        this.openPlusBuy(type);
      }

      if (promotion.more) {
        this.showPopUpMore(promotion.more);
        return;
      }

      if (type === 'carriageDiscount' || type === 'postage') {
        this._goToExpressDetail();
      }
    },

    showPopUpMore(moreText) {
      this.setYZData({
        isShowMore: true,
        moreText,
      });
    },

    hidePopUpMore() {
      this.setYZData({
        isShowMore: false,
      });
    },

    showCashBackRule() {
      this.setYZData({
        isShowCashBackRule: true,
      });
    },

    hideCashBackRule() {
      this.setYZData({
        isShowCashBackRule: false,
      });
      this.toggleCouponPopUp();
    },

    _goToExpressDetail() {
      const app = getApp();
      openWebView('/wscshop/showcase/postrules', {
        title: '包邮规则',
        query: {
          kdtId: app.getKdtId() || 0,
          goodsId: this.data.goods.id,
          offlineId: app.getOfflineId() || 0,
        },
      });
    },

    openPlusBuy(activityType) {
      const { id: activityId } = this.getAvtivityByType(activityType);

      if (!activityId) {
        return;
      }

      navigate.navigate({
        url: getUrl(PLUS_BUY, { activityId }),
      });
    },

    openActivity(activityType, TYPE) {
      const { activityAlias, alias } = this.getAvtivityByType(activityType);

      if (!activityAlias && !alias) {
        return;
      }

      navigate.navigate({
        url: getUrl(TYPE, {
          alias: activityAlias || alias,
        }),
      });
    },

    getAvtivityByType(type) {
      const { orderActivity } = this.data;
      return orderActivity[type] || {};
    },
  },
});
