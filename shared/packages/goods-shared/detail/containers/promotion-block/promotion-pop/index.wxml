<!-- 点击外侧促销模块显示的弹窗 -->
<common-pop
  class="goods-promotion__popup"
  title="优惠"
  show="{{ show }}"
  content-style="margin-top: 0;"
  bind:btn-click="hidePopUp"
  bind:close="hidePopUp"
>
  <view>
    <view
      wx:if="{{ optimalCouponShowList && optimalCouponShowList.length > 0 }}"
      class="goods-promotion__popup-cnt-title"
    >
      <text>使用以下优惠，预计券后</text>
      <theme-view
        color="general"
        inner-style="display:inline;"
      >
        {{ optimalPreferentialPrice }}
      </theme-view>
    </view>

    <!-- 最佳优惠券卡片，现在最多显示20个 -->
    <user-authorize
      wx:if="{{ optimalCouponShowList && optimalCouponShowList.length > 0 }}"
      scene="{{ accountUnionScene.GET_COUPON }}"
      data-ump-type="{{ couponType.OPTIMAL }}"
      bind:next="handleCouponClick"
      popup-custom-style="padding-bottom: 30px!important"
    >
      <promotion-card
        wx:for="{{ optimalCouponShowList }}"
        wx:key="id"
        wx:for-item="coupon"
        data-index="{{ index }}"
        data-ump-type="{{ couponType.OPTIMAL }}"
        got="{{ coupon.got }}"
        head-value="{{ coupon.headValue }}"
        unit="{{ coupon.unit }}"
        valid="{{ coupon.valid }}"
        condition="{{ coupon.condition }}"
        extra-condition="{{ coupon.extraCondition }}"
        title="{{ coupon.title }}"
        action-text="{{ coupon.actionText }}"
        tag="{{ coupon.tag }}"
        is-gray-text="{{ true }}"
      />
    </user-authorize>

    <view
      wx:if="{{ showOptimalCouponMoreBtn }}"
      class="goods-promotion__popup-more"
      catchtap="unfoldOptimalCouponList">
      更多推荐优惠券
      <van-icon
        size="12px"
        name="arrow-down"
        color="#969799"
        custom-class="goods-promotion__popup-more-icon"
      />
    </view>

    <view
      wx:if="{{ formatedGoodsPromotion && formatedGoodsPromotion.length > 0 }}"
      class="goods-promotion__popup-cnt-title"
    >
      <text>促销</text>
    </view>

    <!-- 促销活动卡片，显示的个数由后端返回控制  -->
    <promotion-card
      wx:for="{{ formatedGoodsPromotion }}"
      wx:key="id"
      wx:for-item="promotion"
      data-index="{{ index }}"
      data-type="{{ promotion.type }}"
      head-value="{{ promotion.headValue }}"
      unit="{{ promotion.unit }}"
      valid="{{ promotion.valid }}"
      condition="{{ promotion.condition }}"
      title="{{ promotion.title }}"
      action-text="{{ promotion.actionText }}"
      tag="{{ promotion.tag }}"
      has-arrow="{{ promotion.hasExtraDesc }}"
      goods-list="{{ promotion.goodsListV2 }}"
      is-gray-text="{{ true }}"
      bind:click="{{ promotion.eventName || 'onPromotionClick' }}"
    />


    <view
      wx:if="{{ umpCouponShowList && umpCouponShowList.length > 0 }}"
      class="goods-promotion__popup-cnt-title"
    >
      <text>可领优惠券</text>
    </view>

    <!-- 可领优惠券卡片，现在最多显示20个 -->
    <view
      class="goods-promotion__coupon-wrapper"
      wx:for="{{ umpCouponShowList }}"
      wx:key="id"
      wx:for-item="coupon"
    >
      <user-authorize
        class="goods-promotion__coupon-wrapper-ua"
        btn-class="goods-promotion__coupon-wrapper-btn"
        scene="{{ accountUnionScene.GET_COUPON }}"
        data-index="{{ index }}"
        data-ump-type="{{ couponType.UMP }}"
        bind:next="handleCouponClick"
        popup-custom-style="padding-bottom: 30px!important"
      />
      <promotion-card
        got="{{ coupon.got }}"
        head-value="{{ coupon.headValue }}"
        unit="{{ coupon.unit }}"
        valid="{{ coupon.valid }}"
        condition="{{ coupon.condition }}"
        extra-condition="{{ coupon.extraCondition }}"
        title="{{ coupon.title }}"
        action-text="{{ coupon.actionText }}"
        tag="{{ coupon.tag }}"
        is-gray-text="{{ true }}"
      />
    </view>


    <view
      wx:if="{{ optimalCouponShowList && optimalCouponShowList.length > 0 }}"
      class="goods-promotion__popup-tip"
    >
      券后价格仅为初步预估，请以最终购买价格为准
    </view>
  </view>
</common-pop>

<!-- 如果活动有more字段，就会用到这个弹窗 -->
<common-pop
  show="{{ isShowMore }}"
  bind:btn-click="hidePopUpMore"
  bind:close="hidePopUpMore"
  class="goods-promotion__popup"
  bind:btn-click="hidePopUp"
  bind:close="hidePopUp"
>
  <view class="goods-promotion__more-title">{{ moreText.title }}</view>
  <view>{{ moreText.desc }}</view>
</common-pop>

<!-- 返储值金弹窗 -->
<common-pop
  class="goods-promotion__popup"
  show="{{ isShowCashBackRule }}"
  title="{{ cashBackProPopUpTitle }}"
  button="知道了"
  bind:btn-click="hideCashBackRule"
  bind:close="hideCashBackRule"
>
  <cash-back-rule />
</common-pop>
