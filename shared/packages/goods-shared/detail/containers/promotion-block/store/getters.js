/* eslint-disable operator-linebreak */
import { get, getRealLen } from '@goods/utils';
import { moment as formatDate } from 'utils/time';
import { getUrl, DISCOUNT_PACKAGE } from '@goods/common/page-path';

function couponAdaptor(coupon) {
  return {
    got: coupon.disabled,
    // 没有图片列表
    hasShowcase: false,
    headValue: coupon.valueCopywriting,
    unit: coupon.unitCopywriting,
    condition: coupon.useThresholdCopywriting,
    extraCondition: coupon.upperLimitCopywriting,
    title: coupon.title,
    valid: coupon.validTimeCopywriting,
    key: '',
    id: coupon.id,
    actionText: coupon.disabled ? '已领取' : '立即领取',
    tag: '',
    disabled: coupon.disabled,
  };
}

export default {
  // 优惠活动列表
  goodsPromotion(state) {
    const orderPromotion = state.orderPromotion || [];

    /**
     * 促销活动展示排序
     */
    const promotionTagsOrder = [
      'meetReduce', // 满减
      'present', // 赠品
      'postage', // 包邮
      'cashBack', // 返现
      'cashbackPro', // 返现
      // 'tuanCashBack', // 团购返现
      // 'packageBuy', // 优惠套餐
      'bale', // 打包一口价
      'plusBuy', // 加价购
      'supplierMeetReduce', // 供货商满减
      'secondHalfDiscount', // 第二件半价
    ];

    const goodsPromotion = orderPromotion.filter((item) => {
      return promotionTagsOrder.indexOf(item.key) >= 0;
    });

    return goodsPromotion;
  },

  // 列表最多显示3行;
  goodsPromotionList(state, getters) {
    const { umpCouponList = [] } = state;
    const { goodsPromotion } = getters;
    const list = [...goodsPromotion] || [];
    const MAX_COUPON_COUNT = 3;
    const MAX_LIST_COUNT = 3;
    let displayCouponList = umpCouponList.slice(0, MAX_COUPON_COUNT);

    // 增加商详页活动埋点
    const app = getApp();
    app.logger &&
      app.logger.log({
        et: 'custom',
        ei: 'hascoupon',
        en: '商详页活动信息',
        params: {
          hascoupon: umpCouponList.length >= 1 ? 'true' : 'false',
          umptype: getters.currentActivity.type,
          discount: list.map((item) => {
            return item.key;
          }),
        },
      });
    // 3个的长度如果超过32的话，就隐藏第3个优惠券; 一个中文顶2个英文
    if (umpCouponList.length >= MAX_COUPON_COUNT) {
      const cnt = displayCouponList.reduce((pre, current) => {
        return pre + current.useThresholdAndValueCopywriting;
      }, '');

      const MAX_DISPLAY_LENGTH = 32;
      if (getRealLen(cnt) >= MAX_DISPLAY_LENGTH) {
        displayCouponList = displayCouponList.slice(0, MAX_COUPON_COUNT - 1);
      }
    }

    // 加入优惠券的行；
    if (umpCouponList.length && getters.showGoodsCoupon) {
      list.unshift({
        key: 'umpCouponList',
        title: '领券',
        couponList: displayCouponList,
      });
    }

    return list.slice(0, MAX_LIST_COUNT);
  },

  /**
   * 促销模块popup数据格式
   * 1. 修改字段控制内部显示，具体字段对照可以查看promotion-card组件属性
   * 2. 添加跳转标识hasExtraDesc
   */
  formatedGoodsPromotion(state, getters) {
    const { goodsPromotion = [] } = getters;

    return goodsPromotion.map((promotion) => {
      let {
        // eslint-disable-next-line prefer-const
        key = '',
        // eslint-disable-next-line prefer-const
        title = '',
        desc = '',
        // limit = 5,
        endAt = '',
        startAt = '',
        // cashbackEnd = 10,
        // cashbackMethod = "fixed"
      } = promotion;
      const { goodsListV2 = [] } = promotion;

      let valid = '';
      if (startAt && endAt) {
        try {
          endAt = formatDate(endAt.replace(/[-.]/g, '/'), 'YYYY.MM.DD');
          startAt = formatDate(startAt.replace(/[-.]/g, '/'), 'YYYY.MM.DD');
          valid = `${startAt}-${endAt}`;
        } catch (e) {
          console.log('formatDate:', e);
        }
      }

      let headValue = title || '';
      let tag = title || '';
      let eventName = null;
      let hasExtraDesc = false;
      const emptyTagActivitiesReg = /secondHalfDiscount|bale|plusBuy|postage|meetReduce|cashbackPro/i;
      if (key === 'cashbackPro') {
        const { cashbackEnd, cashbackMethod } = promotion;

        if (cashbackMethod === 'random') {
          headValue = `最高返${cashbackEnd}%`;
        } else {
          headValue = `返${cashbackEnd}%`;
        }

        eventName = 'showCashBackRule';
        hasExtraDesc = true;
        tag = '';
      } else if (key === 'present') {
        const { presentOutList = [] } = promotion;
        desc = '';
        presentOutList.forEach((present) => {
          const { presentPrefix, presentSuffix, goodsModelsV2 = [] } = present;
          let presentText = '';
          goodsModelsV2.forEach((goodsModel) => {
            const { title = '', num = 1 } = goodsModel;

            presentText += `送${num}件${title}`;
            if (presentSuffix) {
              presentText += '、';
            } else {
              presentText += ';';
            }
          });
          desc += `${presentPrefix}${presentText}${presentSuffix}` || '';
        });
        hasExtraDesc = true;

        // 几种活动不需要tag
      } else if (emptyTagActivitiesReg.test(key)) {
        tag = '';
        hasExtraDesc = true;
      }

      const MAX_GOODS_COUNT = 3;
      return {
        disabled: false,

        hasShowcase: false,
        headValue,
        // unit: String,
        condition: '',
        title: desc,
        valid,
        type: key,
        actionText: '',
        tag,
        eventName,
        hasExtraDesc,
        goodsListV2: goodsListV2.slice(0, MAX_GOODS_COUNT),
      };
    });
  },

  showGoodsPromotion(state, getters) {
    const { goods, orderPromotionLimit } = state;
    const { goodsPromotionList } = getters;

    if (!state.saas.promotionBlock.showPromotionList) {
      return false;
    }

    if (goods.isOutlink) {
      return false;
    }

    if (orderPromotionLimit) {
      return true;
    }

    return goodsPromotionList && goodsPromotionList.length > 0;
  },

  packageBuy(state, getters) {
    const { orderPromotion = [] } = state;
    const packageBuys = orderPromotion.filter(
      (item) => item.key === 'packageBuy'
    );

    if (packageBuys.length) {
      const packageBuy = {
        url: getUrl(DISCOUNT_PACKAGE, {
          goodsId: state.goods.id,
          kdt_id: getters.kdtId,
        }),
        goodsList: [],
      };

      packageBuys.forEach((item) => {
        packageBuy.goodsList.push(item.goodsModel);
      });

      return packageBuy;
    }
    return null;
  },

  // 是否展示优惠套餐模块
  showPackageBuy(state, getters) {
    const saasShowPackageBuy = get(
      state,
      'saas.promotionBlock.showPackageBuy',
      true
    );
    return getters.packageBuy && saasShowPackageBuy;
  },

  // 库存展示
  showGoodsStock(state, getters) {
    const { goods } = state;
    const { isMultiStoreSoldout } = getters;

    return (
      goods.stockNum > 0 &&
      !goods.hideStock &&
      !getters.customStock &&
      !goods.isHotel &&
      !isMultiStoreSoldout
    );
  },

  // 显示销量
  goodsSoldNumText(state) {
    const { soldNum = 0 } = state.goods;
    if (soldNum > 99999) {
      return '10万+';
    }
    return soldNum;
  },

  // 发货地展示
  showCountry(state) {
    let { cityName: city = '', provinceName: province = '' } = get(
      state,
      'distribution.deliveryAddress',
      {}
    );
    if (!city && !province) {
      city = get(state, 'shop.city');
      province = get(state, 'shop.province');
    }
    if (province === city) {
      // 如北京市 上海市
      return province;
    }
    return `${province}${city}`;
  },

  // 运费展示
  showPostage(state, getters) {
    const { goods } = state;
    const { isMultiStoreSoldout } = getters;
    return getters.showPostageText && !goods.isHotel && !isMultiStoreSoldout;
  },

  // 运费文案
  showPostageText(state) {
    const postage = get(state, 'distribution.postage', '');
    return postage;
  },

  showDeliveryBar(state) {
    return !state.goods.isOutlink;
  },

  // 是否展示优惠券模块，外链不展示
  showGoodsCoupon(state, getters) {
    // 如果助力定金预售，不显示领券
    if (getters.isHelpDepositPresale) {
      return false;
    }
    // F码购买不显示领券
    if (getters.isSupportFCodeBuy) {
      return false;
    }

    // 如果无优惠券列表，不展示优惠券模块
    const umpCouponList = state.umpCouponList;
    if (!umpCouponList || !umpCouponList.length) {
      return false;
    }

    return !state.goods.isOutlink;
  },

  // 订单返现文案
  cashBackRuleStr(state) {
    const { type, limit, startTime, endTime, maxCashBack } = get(
      state,
      'orderActivity.cashBack',
      {}
    );
    let str = '';

    if (maxCashBack) {
      const limitStr = limit === 1 ? '首' : `前${limit}`;
      const typeStr = type === 'fixed' ? '可获得' : '可随机获得';
      str = `${startTime}至${endTime}期间，在本店${limitStr}笔订单支付成功，${typeStr}返现；`;
    }

    return str;
  },

  isCashbackPro(state) {
    const cashbackPro = state.orderActivity.cashbackPro || null;

    if (!cashbackPro) {
      return false;
    }

    return true;
  },

  isCashbackProStoredValue(state, getters) {
    if (!getters.isCashbackPro) {
      return null;
    }

    const { cashbackPro } = state.orderActivity;
    const { cashbackType } = cashbackPro;

    return cashbackType === 'storedValue';
  },

  cashbackPro(state, getters) {
    if (!getters.isCashbackPro) {
      return null;
    }

    return state.orderActivity.cashbackPro;
  },

  cashBackProRuleStr(state, getters) {
    if (!getters.isCashbackPro) {
      return '';
    }

    const {
      startTime,
      endTime,
      cashbackStart,
      cashbackEnd,
      cashbackMethod,
    } = state.orderActivity.cashbackPro;

    const isNotRange = cashbackStart === cashbackEnd;
    const range = isNotRange
      ? `${cashbackStart}%`
      : `${cashbackStart}%-${cashbackEnd}%`;

    const randomText = cashbackMethod === 'random' ? '随机' : '';
    return `${startTime} 至 ${endTime} 期间，${getters.getCashbackProPopupDesc}可${randomText}获得订单实付金额${range}的${getters.cashBackTypeTitle}，运费不参与计算`;
  },

  getCashbackProPopupDesc(state, getters) {
    if (!getters.isCashbackPro) {
      return '';
    }

    const {
      limitType,
      cashbackLimit,
      // cashbackEnd,
      activityLimit,
    } = state.orderActivity.cashbackPro;

    let popupDesc = '';
    switch (limitType) {
      case 'mix':
        popupDesc = `所有在本店支付的订单中前${activityLimit}笔订单，同时也是你在本店支付的前${cashbackLimit}笔订单，`;
        break;
      case 'buyer':
        popupDesc = `你在本店支付的前${cashbackLimit}笔订单，`;
        break;
      case 'none':
      default:
        popupDesc = '在本店支付';
        break;
    }

    return popupDesc;
  },

  cashBackTypeTitle(state, getters) {
    if (!getters.isCashbackPro) {
      return '';
    }

    return getters.isCashbackProStoredValue ? '储值金返还' : '返现';
  },

  cashBackProPopUpTitle(state, getters) {
    if (!getters.isCashbackPro) {
      return '返现说明';
    }

    return getters.isCashbackProStoredValue ? '返储值金说明' : '返现说明';
  },

  // 物流栏展示内容
  deliveryBarText(state, getters) {
    const { deliveryList } = getters;
    const len = deliveryList.length;

    if (len === 1 && deliveryList[0].tag === '同城配送') {
      return `${deliveryList[0].detail}`;
    }

    return getters.deliveryList.reduce((total, cur, curIndex) => {
      return total + (curIndex === len - 1 ? cur.tag : `${cur.tag}、`);
    }, '');
  },

  /*
    销量展示
    {
      show: 0 // 商详页销量是否展示：0-不展示，1-展示
      limit: false,// 销量展示时是否限定值
      limit_num:'',// 销量展示的限定值，大于0
    }
   */
  showSoldNum(state, getters) {
    const { goods } = state;
    const goodsDetailSales = get(state, 'shopConfig.goodsDetailSales', null);
    const showBuyRecord = get(state, 'shopConfig.showBuyRecord', null);
    const { soldNum = 0 } = goods;

    if (getters.isPeriodbuy) {
      return false;
    }

    // 如果没有新值就用老的销量字段值
    let parsedGoodsDetailSales = showBuyRecord
      ? { show: 1, limit: false }
      : { show: 0, limit: false };

    if (goodsDetailSales) {
      try {
        parsedGoodsDetailSales =
          JSON.parse(goodsDetailSales) || parsedGoodsDetailSales;
      } catch (error) {
        // do nothing
      }
    }

    if (!parsedGoodsDetailSales) {
      return soldNum > 0;
    }

    const {
      show = 0,
      limit = false,
      limit_num: limitNum = 0,
    } = parsedGoodsDetailSales;

    if (+show === 0) {
      return false;
    }
    if (!limit) {
      return soldNum > 0;
    }

    return soldNum >= limitNum;
  },

  // 券后价提示
  optimalPreferentialPrice(state, getters) {
    const priceText = getters.preferentialPrice.match(/￥\d+(\.\d+)?.*/g);
    return priceText ? priceText[0] : '';
  },

  // 最佳券列表
  optimalCouponShowList(state, getters) {
    if (!getters.showGoodsCoupon || !getters.hasCouponActivity) {
      return [];
    }

    const MAX_SHOW_COUNT = 1;
    const promotion = state.promotion || {};
    const optimalCouponList = state.optimalCouponList;

    // 优惠券列表为空或点击查看更多则展开所有最佳券
    if (!getters.umpCouponShowList.length || promotion.showAllOptimalCoupon) {
      return optimalCouponList.map(couponAdaptor);
    }

    return optimalCouponList.slice(0, MAX_SHOW_COUNT).map(couponAdaptor);
  },

  // 优惠券列表
  umpCouponShowList(state, getters) {
    if (!getters.showGoodsCoupon) {
      return [];
    }

    const MAX_SHOW_COUNT = 20;

    // 需要和最佳券去重
    if (getters.hasCouponActivity) {
      const idSet = state.optimalCouponList.reduce((acc, v) => {
        acc[v.id] = true;
        return acc;
      }, {});

      const filterList = state.umpCouponList.filter((v) => !idSet[v.id]);

      return filterList.slice(0, MAX_SHOW_COUNT).map(couponAdaptor);
    }

    return state.umpCouponList.slice(0, MAX_SHOW_COUNT).map(couponAdaptor);
  },

  // 查看更多最佳券显示
  showOptimalCouponMoreBtn(state, getters) {
    const promotion = get(state, 'promotion', {});
    if (state.optimalCouponList.length > 1) {
      return (
        getters.umpCouponShowList.length && !promotion.showAllOptimalCoupon
      );
    }
    return false;
  },
};
