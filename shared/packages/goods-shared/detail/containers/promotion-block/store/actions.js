import api from '@goods/utils/api';

const app = getApp();

let ajaxing = false;

export default {
  handlePromotionCoupon({ state, commit }, pla) {
    if (state.orderPromotionLimit) {
      return;
    }
    if (pla && !!pla.currentTarget.dataset.isPreposition) {
      app.logger
        && app.logger.log({
          et: 'click',
          ei: 'is_preposition',
          en: '点击领券前置模块',
          params: {
            is_preposition: 'true',
          },
        });
    }
    commit('toggleCouponPopUp');
    commit('setOptimalCouponDisplay', { showAll: false });
  },

  toggleCouponPopUp({ commit }) {
    commit('toggleCouponPopUp');
  },

  showMoreOptimalCoupon({ commit }) {
    commit('setOptimalCouponDisplay', { showAll: true });
  },

  hideMoreOptimalCoupon({ commit }) {
    commit('setOptimalCouponDisplay', { showAll: false });
  },

  getUmpCouponList({ state, commit }) {
    const { alias } = state.goods;

    return api
      .get({
        url: '/wscump/coupon/goods-coupon-list.json',
        data: {
          alias,
        },
        withCredentials: true,
      })
      .then((res) => {
        commit('setUmpCouponList', res);
      })
      .catch((err) => {
        wx.showToast({
          title: err || '获取商品适用优惠券列表失败',
          icon: 'none',
        });
      });
  },

  getCoupon({ getters }, { source, id }) {
    return api.get({
      url: '/wscgoods/getCoupon.json',
      data: {
        activityId: id,
        kdtId: getters.kdtId,
        source,
        // 幂等用的id
        requestId: `${id}-${new Date().getTime()}`,
      },
    });
  },

  handleGetCoupon({ commit, dispatch, state }, payload) {
    if (ajaxing) {
      return;
    }

    ajaxing = true;
    const COUPON_TYPE = state.businessConfigs.couponType || {};
    dispatch('getCoupon', {
      id: payload.id,
      source: `goods_details_${
        payload.type === COUPON_TYPE.OPTIMAL ? 'rcmd_take' : 'take'
      }`,
    })
      .then(() => {
        ajaxing = false;
        commit('setCouponDisabled', payload);
        wx.showToast({
          title: '恭喜你抢到了',
          icon: 'none',
        });
      })
      .catch(({ msg = '' }) => {
        ajaxing = false;
        wx.showToast({
          title: msg || '领取失败',
          icon: 'none',
        });
      });
  },

  getUserCenterTitle({ state, commit }) {
    const { shop = {} } = state;

    api
      .get({
        url: '/wscuser/membercenter/getUserCenterConfig.json',
        data: {
          kdtId: shop.kdtId,
          platform: 1,
        },
      })
      .then((res = {}) => {
        const { title } = res;

        if (title) {
          commit('updateUserCenterTitle', title);
        }
      })
      .catch(() => {});
  },
};
