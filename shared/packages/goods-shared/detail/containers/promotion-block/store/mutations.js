export default {
  toggleCouponPopUp(state) {
    state.displayPop.showCouponPopUp = !state.displayPop.showCouponPopUp;
  },

  updateUserCenterTitle(state, payload) {
    state.userCenterTitle = payload;
  },

  setOptimalCouponDisplay(state, payload) {
    state.promotion.showAllOptimalCoupon = payload.showAll;
  },

  // 商品适用优惠券列表
  setUmpCouponList(state, payload) {
    if (Array.isArray(payload)) {
      state.umpCouponList = payload.map((voucher) => {
        voucher.disabled = false;
        return voucher;
      });
    }
  },

  // 显示隐藏促销模块
  setIsShowGoodsPromotion(state, payload) {
    state.isShowGoodsPromotion = payload;
  }
};
