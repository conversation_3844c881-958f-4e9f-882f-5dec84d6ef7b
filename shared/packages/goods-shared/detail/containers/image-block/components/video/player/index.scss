:host {
  --animation-duration-fast: 0.1s;
}

.video {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  line-height: 1;

  &-bg {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  &-play {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120rpx;
    height: 120rpx;
    background-image: url(https://img01.yzcdn.cn/public_files/355502b6f7b304267b9fda9bc43c75d4.png);
    background-size: 120rpx 120rpx;
    transform: translate(-50%, -50%);
    text-indent: -9999rpx;
  }

  &-controls {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    box-sizing: border-box;
    padding: 20rpx;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, transparent, black);
    transition: opacity ease 1s;
    color: #fff;

    &--dim {
      opacity: 0.5;
    }
  }

  &-progress {
    flex: 1;
    display: flex;
    align-items: center;

    &__time {
      flex: 0 0 auto;
      min-width: 2.8em;
      font-size: 24rpx;
      text-align: center;
    }

    &__slider {
      flex: 1;
      margin: 0 16rpx;

      &__pivot {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
        background-color: #fff;
      }
    }
  }

  &-toggle {
    margin-left: 32rpx;
    flex: 0 0 auto;

    &-danmaku {
      position: absolute;
      top: -60rpx;
      left: 20rpx;
    }
  }

  &--fullscreen {
    .video {
      &-controls {
        margin-bottom: constant(safe-area-inset-bottom);
        margin-bottom: env(safe-area-inset-bottom);
      }
    }
  }
}
