import { GoodsComponent } from '@goods/common/vanx-component';

/**
 * andriod 兼容性问题
 * 1. enable-progress-gesture 开启手势，会存在切换 swiper 时暂停/播放和手势冲突，导致播放/暂停失效
 * 2. video 在 android 手机，不能通过 display none 来控制隐藏，会导致 objectFit 变 fill
 * 3. 开发者工具设置暂停播放会触发暂停播放事件
 */
GoodsComponent({
  properties: {
    hidden: {
      type: Boolean,
    },
  },

  state: ['video', 'isImageFullScreen'],

  getters: ['navbarRect', 'videoDanmakuConfigOpen', 'videoDanmakuSource'],

  data: {
    isPlaying: false,
    isMuted: true,
    showControls: true,
    duration: 0, // s
    currentTime: 0, // s
    percentage: 0,
    currentTimeText: '00:00',
    durationText: '00:00',
    danmakuInitialized: false,
    danmakuSwitch: true, // 用户主动设置的弹幕开关
    danmakuVisible: true,
  },

  observers: {
    currentTime(currentTime) {
      const currentTimeText = this.formatTime(currentTime);
      if (currentTimeText !== this.data.currentTimeText) {
        // 无 immediate 不会生效
        this.setYZData({ currentTimeText }, { immediate: true });
      }
    },
    duration(duration) {
      const durationText = this.formatTime(duration);
      if (durationText !== this.data.durationText) {
        // 无 immediate 不会生效
        this.setYZData({ durationText }, { immediate: true });
      }
    },
  },

  ready() {
    this.videoContext = wx.createVideoContext('goods-video-id', this);
    this.triggerEvent('video-ready', this);
  },

  detached() {
    this.clearAutoHideControlsTimer();
  },

  methods: {
    onVideoPlay() {
      this.setYZData({ isPlaying: true });
      this.toggleControls(true);

      // 保持用户选择的弹幕开关状态
      if (this.data.danmakuSwitch) {
        this.toggleDanmaku(true);
      }

      this.triggerEvent('video-play');
    },

    onVideoPause() {
      this.setYZData({ isPlaying: false });
      this.toggleControls(true, false); // 暂停时一直显示
      this.toggleDanmaku(false);
      this.triggerEvent('video-pause');
    },

    onVideoEnded() {
      this.setYZData({ isPlaying: false });

      this.toggleControls(true, false); // 结束后一直显示

      this.toggleDanmaku(false);
      this.danmakuManager?.pause();

      this.triggerEvent('video-end');
    },

    onLoadedMetaData(e) {
      this.setYZData({ duration: e.detail.duration });
    },

    onTimeUpdate(e) {
      const { currentTime, duration } = e.detail;
      if (duration <= 0) {
        return;
      }
      const percentage = Math.min((currentTime / duration) * 100, 100) || 0;

      this.setYZData({ currentTime, percentage });
    },

    onProgressChange(e) {
      const percentage = e.detail;
      const currentTime = (this.data.duration * percentage) / 100;
      this.videoContext?.seek(currentTime);
      this.triggerEvent('video-seek', percentage);
    },

    padLeft(number) {
      return number < 10 ? `0${number}` : `${number}`;
    },

    formatTime(seconds) {
      const minute = parseInt(seconds / 60, 10);
      const second = Math.round(seconds % 60);
      return `${this.padLeft(minute)}:${this.padLeft(second)}`;
    },

    pause() {
      this.videoContext?.pause();
    },

    play() {
      this.videoContext?.play();
    },

    togglePlay() {
      if (this.data.isPlaying) {
        this.pause();
      } else {
        this.play();
      }
    },

    toggleMuted() {
      this.setYZData({ isMuted: !this.data.isMuted });
    },

    toggleFullScreen() {
      this.triggerEvent('toggle-fullscreen');
    },

    toggleControls(showControls = true, autoHide = true) {
      this.setYZData({ showControls });

      if (autoHide) {
        this.runAutoHideControlsTimer();
      } else {
        // 保持状态
        this.clearAutoHideControlsTimer();
      }
    },

    showControls() {
      this.toggleControls(true);
    },

    clearAutoHideControlsTimer() {
      clearTimeout(this.$_autoHideTimer);
    },
    // 启动自动隐藏操作按钮的计时器
    runAutoHideControlsTimer() {
      if (!this.data.isPlaying) {
        return;
      }

      this.clearAutoHideControlsTimer();
      this.$_autoHideTimer = setTimeout(() => {
        this.setYZData({ showControls: false });
      }, 2e3);
    },

    danmakuInit(e) {
      this.danmakuManager = e.detail;
      this.setYZData({ danmakuInitialized: true });

      if (this.data.danmakuSwitch) {
        this.toggleDanmaku(true);
      }
    },

    toggleDanmaku(danmakuVisible) {
      this.setYZData({ danmakuVisible });

      // 打开弹幕的时候，如果视频正在播放则播放
      if (danmakuVisible && this.data.isPlaying) {
        this.danmakuManager?.play();
      }
    },

    toggleDanmakuSwitch() {
      // 以实际显示为准
      const danmakuSwitch = !this.data.danmakuVisible;

      this.setYZData({ danmakuSwitch });
      this.toggleDanmaku(danmakuSwitch);

      this.triggerEvent('danmaku-toggle', danmakuSwitch);
    },
  },
});
