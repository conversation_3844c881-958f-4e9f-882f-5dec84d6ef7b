import { GoodsComponent } from '@goods/common/vanx-component';

const volumeOn =
  'https://img01.yzcdn.cn/upload_files/2021/04/30/FsKR1KB_CXphmZHREEAkcJd-5-iz.png';
const volumeOff =
  'https://img01.yzcdn.cn/upload_files/2021/04/30/FlKQL7uKI3MTxAP9HVmz0GfDVCkq.png';

GoodsComponent({
  properties: {
    muted: {
      type: Boolean,
      value: false,
    },
    fullscreen: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    volumeOn,
    volumeOff,
    imageUrls: [volumeOn, volumeOff],
  },

  methods: {
    toggleMuted() {
      this.triggerEvent('t-muted');
    },
    toggleFullScreen() {
      this.triggerEvent('t-fullscreen');
    },
  },
});
