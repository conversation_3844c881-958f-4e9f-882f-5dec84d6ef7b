<video
  id="goods-video-id"
  class="video {{ isImageFullScreen ? 'video--fullscreen' : '' }}"
  objectFit="contain"
  src="{{ video.videoUrl }}"
  hidden="{{ hidden }}"
  loop="{{ false }}"
  autoplay="{{ false }}"
  direction="{{ 0 }}"
  show-fullscreen-btn="{{ false }}"
  show-play-btn="{{ false }}"
  show-center-play-btn="{{ false }}"
  enable-progress-gesture="{{ false }}"
  show-progress="{{ false }}"
  muted="{{ isMuted }}"
  bindloadedmetadata="onLoadedMetaData"
  bindtimeupdate="onTimeUpdate"
  bindplay="onVideoPlay"
  bindpause="onVideoPause"
  bindended="onVideoEnded"
>
  <!-- 以下内容按顺序层级排列 -->

  <!-- 底层背景，用于切换播放/暂停 -->
  <view class="video-bg" catch:tap="togglePlay" />
  <view class="video-play" wx:if="{{ !isPlaying }}" catch:tap="togglePlay">播放</view>

  <!-- 弹幕 -->
  <danmaku
    wx:if="{{ videoDanmakuConfigOpen }}"
    source="{{ videoDanmakuSource }}"
    visible="{{ danmakuVisible }}"
    offset-top="{{ isImageFullScreen ? navbarRect.top + navbarRect.height : 0 }}"
    bind:init="danmakuInit"
  />

  <!-- 控制栏 -->
  <view
    class="video-controls {{ !showControls ? 'video-controls--dim' : '' }}"
    bind:tap="showControls"
  >
    <!-- 进度条 -->
    <view class="video-progress">
      <text class="video-progress__time">{{ currentTimeText }}</text>
      <van-slider
        class="video-progress__slider"
        use-button-slot
        active-color="#fff"
        inactive-color="rgba(255, 255, 255, 0.2)"
        step="{{ 0.001 }}"
        value="{{ percentage }}"
        bind:change="onProgressChange"
      >
        <view class="video-progress__slider__pivot" slot="button" />
      </van-slider>
      <text class="video-progress__time">{{ durationText }}</text>
    </view>

    <!-- 切换 -->
    <video-toggle
      class="video-toggle"
      muted="{{ isMuted }}"
      fullscreen="{{ isImageFullScreen }}"
      bind:t-muted="toggleMuted"
      bind:t-fullscreen="toggleFullScreen"
    />

    <!-- 弹幕开关 -->
    <danmaku-toggle
      class="video-toggle-danmaku"
      wx:if="{{ isImageFullScreen && videoDanmakuConfigOpen && danmakuInitialized }}"
      on="{{ danmakuVisible }}"
      bind:click="toggleDanmakuSwitch"
    />
  </view>
</video>
