<view class="video-toggle">
  <!-- 预加载 -->
  <image hidden wx:for="{{ imageUrls }}" wx:for-item="url" wx:key="{{ url }}" src="{{ url }}" />

  <!-- 切换-静音 -->
  <van-icon
    class="video-toggle__icon"
    name="{{ muted ? volumeOff : volumeOn }}"
    bind:tap="toggleMuted"
  />
  <!-- 切换-全屏 -->
  <van-icon
    class="video-toggle__icon"
    name="{{ fullscreen ? 'shrink' : 'expand-o' }}"
    bind:tap="toggleFullScreen"
  />
</view>
