import { GoodsComponent } from '@goods/common/vanx-component';
import { setLogger } from '@goods/common/logger';
import Toast from '@vant/weapp/dist/toast/toast';
import { getNetworkType } from '@goods/utils/network';
import { checkIsChannels, checkIsHalfScreen } from 'shared/utils/channel';
import { getPageHooks } from '@goods/extensions';

GoodsComponent({
  name: 'ImageBlock',

  state: ['firstPageRendered', 'isImageFullScreen'],

  getters: [
    'navbarRect',
    'goodsPictures',
    'goodsPictureHeight',
    'showGoodsVideo',
    'showImage',
  ],

  data: {
    // 是否自动轮播
    loop: true,
    /** 隐藏视频 */
    hideVideo: true,
    // 当前swp页
    currentSwpPage: 0,
    // 视频播放统计标志
    videoPaidCountFlag: false,
    /** 当前是否是视频页 */
    isVideoSwpPage: false,
    // 是否是视频号半屏进入
    isHalf: false,
  },

  observers: {
    'showGoodsVideo, currentSwpPage': function (
      showGoodsVideo,
      currentSwpPage
    ) {
      this.setYZData(
        {
          isVideoSwpPage: showGoodsVideo && currentSwpPage === 0,
        },
        { immediate: true }
      );
    },
  },

  attached() {
    // 兜底标识页面渲染完成：1.5秒内如果图片都没有触发load/error事件，那么就触发页面渲染完成
    setTimeout(() => {
      this.$commit('setFirstPageRendered', true);
    }, 1500);
  },

  ready() {
    this.setYZData({ isHalf: checkIsChannels() && checkIsHalfScreen() });
  },

  methods: {
    /**
     * 播放视频（含自动点击，由于同时支持事件，所以要写成对象）
     */
    playVideo({ auto = 0 } = {}) {
      if (!this.videoCpn) {
        return;
      }

      this.videoCpn.play();

      setLogger({
        et: 'custom',
        en: '商品主图视频-播放',
        ei: 'goods_main_video_play',
        params: { auto },
      });
    },

    onVideoReady({ detail: videoCpn }) {
      this.videoCpn = videoCpn;

      // wifi 下自动播放
      getNetworkType().then((networkType) => {
        if (networkType === 'wifi') {
          this.playVideo({ auto: 1 });
        }
      });
    },

    onVideoPlay() {
      this.setYZData({ hideVideo: false });
      this.toggleTradeCarousel(false);

      if (!this.hasPlayed) {
        this.hasPlayed = true;

        getNetworkType().then((networkType) => {
          if (networkType !== 'wifi') {
            Toast({
              message: '当前为非Wi-Fi环境，请注意流量消耗',
              position: 'top',
            });
          }
        });
      }
    },

    onVideoPause() {
      this.toggleTradeCarousel(true);

      setLogger({
        et: 'custom',
        en: '商品主图视频-暂停',
        ei: 'goods_main_video_pause',
      });
    },

    onVideoSeek(e) {
      const percentage = e.detail || 0;
      setLogger({
        et: 'custom',
        en: '商品主图视频-进度',
        ei: 'goods_main_video_seek',
        params: {
          percentage: +percentage.toFixed(1),
        },
      });
    },

    onVideoEnd() {
      this.setYZData({ hideVideo: true });
      this.toggleTradeCarousel(true);
    },

    handlePreview({ currentTarget }) {
      const hooks = getPageHooks(this);
      hooks.beforePreviewMainImage.call().then(() => {
        setLogger({
          et: 'click', // 事件类型
          ei: 'click_goods_pic', // 事件标识
          en: '点击头图', // 事件名称
        });

        const { index } = currentTarget.dataset;

        // 主图视频不直接预览
        if (+index === 0 && this.data.showGoodsVideo) {
          return;
        }

        this.toggleFullScreen();
      });
    },

    changeSwiperPage(e) {
      const { currentSwpPage: prevIndex } = this.data;
      const newIndex = e.detail.current;

      this.setYZData({ currentSwpPage: newIndex });

      // 视频展示的情况需要暂停或播放
      if (this.data.showGoodsVideo && !this.data.hideVideo) {
        const { videoCpn } = this;

        // 从视频切换到其他轮播
        const isLeaveVideo = prevIndex === 0 && newIndex !== 0;
        if (isLeaveVideo) {
          videoCpn.suspendedPlaying = videoCpn.data.isPlaying;
          videoCpn.pause();
          return;
        }

        // 从其他轮播返回到视频
        const isBackVideo = newIndex === 0 && prevIndex !== 0;
        if (isBackVideo) {
          videoCpn.suspendedPlaying && videoCpn.play();
        }
      }
    },

    onImageLoadOrError({
      target: {
        dataset: { index },
      },
    }) {
      const { showGoodsVideo } = this.data;
      const videoImgLoaded = +index === 1 && showGoodsVideo;

      // 第一张图片加载完成 或者包含主图视频情况下的第二张图片加载完成；来表示首屏的显示
      if (+index === 0 || videoImgLoaded) {
        this.sendGoodsLog();
        this.$commit('setFirstPageRendered', true);
      }
    },

    sendGoodsLog() {
      this.trigger('goodsDetail:pagefinished');
    },

    toggleFullScreen() {
      const fullScreen = !this.data.isImageFullScreen;
      this.$dispatch('setImageFullScreen', fullScreen);
      this.$dispatch('setPageScrollLocked', fullScreen);
      this.toggleTradeCarousel(!fullScreen); // 退出全屏后还原跑马灯
    },

    onDanmakuToggle(e) {
      const on = e.detail;

      setLogger({
        et: 'custom',
        en: '商品主图视频-弹幕-开关',
        ei: 'goods_main_video_danmaku_switch',
        params: { on: Number(!!on) },
      });
    },

    toggleTradeCarousel(visible) {
      // 隐藏
      if (!visible) {
        clearTimeout(this.$_carouselTimer);
        this.$store && this.$store.commit('setTradeCarouselVisible', visible);
        return;
      }

      // 显示

      // 全屏，不显示跑马灯
      if (this.data.isImageFullScreen) {
        return;
      }

      // 视频正在播放，不显示跑马灯
      if (this.videoCpn?.data.isPlaying) {
        return;
      }

      // 5s 后出现成交跑马灯
      clearTimeout(this.$_carouselTimer);
      this.$_carouselTimer = setTimeout(() => {
        this.$store && this.$store.commit('setTradeCarouselVisible', visible);
      }, 5e3);
    },
  },
});
