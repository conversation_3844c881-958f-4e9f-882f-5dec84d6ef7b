import get from '@youzan/weapp-utils/lib/get';
import { fullfillImage } from '@goods/utils';
import { getHeight } from 'shared/utils/nav-config';
import { SystemInfo } from 'shared/utils/browser/system-info';

import { fetchItemDanmakuList } from '../api';

const getMainGoodsPic = (pictures) => {
  const pic1 = get(pictures, '[0]', {});
  const pic2 = get(pictures, '[1]', {});
  // 如果是视频封面，取视频封面下一张主图
  if (pic1.isVideo) {
    return pic2;
  }
  return pic1;
};

export default {
  // 是否展示视频
  showGoodsVideo(state) {
    return !!state.video.videoUrl;
  },
  // 商品主图
  goodsPictures(state, getters) {
    return getters.originGoodsPictures.map((item) => {
      const url = fullfillImage(item.url, '!750x0.jpg');
      if (!state.showOriginImage) {
        const originUrl = /[^?!#]*/.exec(url)?.[0];
        let mediaType = /\.([a-zA-Z0-9]{1,4})$/.exec(originUrl)?.[1];
        const transReg = /(jpg|gif|png|webp)/;

        if (!mediaType) return url;

        if (!transReg.test(mediaType)) {
          mediaType = 'jpg';
        }
        return url
          .replace('/format/webp', `/format/${mediaType}`)
          .replace(/\.webp$/, `.${mediaType}`);
      }
      return url;
    });
  },

  goodsPictureHeight(state, getters) {
    let ratio = 3 / 4;

    const { height, width } = getMainGoodsPic(getters.originGoodsPictures);
    if (height && width) {
      ratio = height / width;
    }
    ratio = Math.min(2, ratio);
    ratio = Math.max(1 / 2.5, ratio);

    const { windowWidth } = SystemInfo;
    const displayHeight = Math.floor(windowWidth * ratio);
    return displayHeight;
  },

  /**
   * 是否开启弹幕店铺配置
   */
  videoDanmakuConfigOpen(state) {
    return !!state.shopConfig?.goodsVideoDanmakuSwitch;
  },

  videoDanmakuSource(state) {
    const itemId = state.goods?.id;
    return () => fetchItemDanmakuList(itemId);
  },

  navbarRect() {
    const top = SystemInfo.statusBarHeight;

    return {
      top,
      height: getHeight() - top,
    };
  },
  // 是否展示主图
  showImage(state, getters) {
    return state.saas.showImage;
  },
};
