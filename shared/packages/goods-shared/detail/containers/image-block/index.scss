.image-block {
  position: relative;
  opacity: 0;

  &--fade-in {
    opacity: 1;
  }

  &.is-half {
    max-height: 100vw !important;
  }

  .is-half {
    max-height: 100vw !important;
  }
}

.goods-video {
  height: 100%;
  width: 100%;
  display: block;
  position: absolute;
  left: 0;
  top: 0;

  &.hidden {
    z-index: -1;
  }

  &-play-bg {
    display: block;
    width: 100%;
    height: 100%;
    background: #fff;
  }

  &-play-btn {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://img01.yzcdn.cn/public_files/355502b6f7b304267b9fda9bc43c75d4.png')
      no-repeat center center;
    background-size: 120rpx;
    text-indent: -9999rpx;
  }
}

.swp {
  position: relative;
  display: block;
  margin: 0;
  padding: 0;
  width: 100%;

  &-wrap {
    position: relative;

    &--fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10002; // 超过 navbar
      background: #000;
      overflow: hidden;

      .swp {
        height: 100% !important;
      }
    }
  }

  &__item {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__img {
    display: block;
    width: 100%;
    height: 100%;
  }

  &__indicator {
    position: absolute;
    right: 32rpx;
    bottom: 32rpx;
    box-sizing: border-box;
    min-width: 2.8em;
    padding: 0 12rpx;
    font-size: 24rpx;
    line-height: 40rpx;
    color: #fff;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 142rpx;
    text-align: center;
    text-shadow: 0 2rpx 2rpx #323233;

    &--fullscreen {
      right: auto;
      bottom: auto;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  &__close-btn {
    position: absolute;
    top: 50%;
    left: 32rpx;
    font-size: 48rpx;
    color: #fff;
    text-shadow: 0 0 8rpx rgba(0, 0, 0, 0.2);
    transform: translate(0, -50%);
  }

  &__navbar {
    position: absolute;
    left: 0;
    right: 0;
    top: 40rpx;
    width: 100%;
    height: 80rpx;
  }
}
