<view
  wx:if="{{ showImage }}"
  id="js-nav-goods"
  class="image-block {{ firstPageRendered ? 'image-block--fade-in' : '' }} {{ isHalf ? 'is-half' : '' }}"
  style="height: {{ goodsPictureHeight }}px"
>
  <view class="swp-wrap {{ isImageFullScreen ? 'swp-wrap--fullscreen' : '' }}">
    <swiper
      class="swp {{ isHalf ? 'is-half' : '' }}"
      loop="{{ loop }}"
      autoplay="{{ showGoodsVideo ? 0 : 3000 }}"
      indicator-color="white"
      circular
      style="height: {{ goodsPictureHeight }}px"
      bind:change="changeSwiperPage"
    >
      <swiper-item
        class="swp__item"
        wx:for="{{ goodsPictures }}"
        wx:key="index"
        wx:for-index="index"
        data-index="{{ index }}"
        bind:tap="handlePreview"
      >
        <block wx:if="{{ index === 0 && showGoodsVideo }}">
          <block wx:if="{{ hideVideo }}">
            <image class="goods-video-play-bg" mode="aspectFit" src="{{ item }}" />
            <view class="goods-video-play-btn" catch:tap="playVideo" />
          </block>
          <goods-video
            class="goods-video {{ hideVideo ? 'hidden' : ''}}"
            wx:if="{{ showGoodsVideo }}"
            bind:video-ready="onVideoReady"
            bind:video-play="onVideoPlay"
            bind:video-pause="onVideoPause"
            bind:video-end="onVideoEnd"
            bind:video-seek="onVideoSeek"
            bind:toggle-fullscreen="toggleFullScreen"
            bind:danmaku-toggle="onDanmakuToggle"
          />
        </block>
        <image
          wx:else
          class="swp__img"
          mode="aspectFit"
          src="{{ item }}"
          data-index="{{ index }}"
          show-menu-by-longpress="{{ isImageFullScreen }}"
          bindload="onImageLoadOrError"
          binderror="onImageLoadOrError"
        >
      </swiper-item>
    </swiper>

    <!-- 全屏 -->
    <block wx:if="{{ isImageFullScreen }}">
      <!-- 导航 -->
      <view class="swp__navbar" style="top: {{ navbarRect.top }}px; height: {{ navbarRect.height }}px;">
        <!-- 数量指示器 -->
        <view
          class="swp__indicator swp__indicator--fullscreen"
          wx:if="{{ goodsPictures.length > 1 }}"
          hidden="{{ isVideoSwpPage && !hideVideo }}"
        >
          {{ currentSwpPage + 1 }}/{{ goodsPictures.length }}
        </view>

        <!-- 关闭全屏 -->
        <van-icon class="swp__close-btn" name="cross" catch:tap="toggleFullScreen" />
      </view>
    </block>
    <block wx:else><!-- 非全屏 -->
      <!-- 数量指示器 -->
      <view
        class="swp__indicator"
        wx:if="{{ goodsPictures.length > 1 }}"
        hidden="{{ isVideoSwpPage && !hideVideo }}"
      >
        {{ currentSwpPage + 1 }}/{{ goodsPictures.length }}
      </view>
    </block>
  </view>
</view>
