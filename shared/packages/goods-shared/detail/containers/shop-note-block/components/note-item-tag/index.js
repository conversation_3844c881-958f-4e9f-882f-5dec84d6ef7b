import { GoodsComponent } from '@goods/common/vanx-component';

const NOTE_TYPE_MAP = {
  up_new: '上新',
  shop_keeper: '掌柜说',
  shop_circle: '店铺圈',
  single_prod_intro: '单品介绍'
};

GoodsComponent({
  name: 'NoteItemTag',

  properties: {
    noteType: String
  },

  data: {
    tag: ''
  },

  observers: {
    noteType(value) {
      if (value) {
        this.setYZData({
          tag: NOTE_TYPE_MAP[value]
        });
      }
    }
  }
});
