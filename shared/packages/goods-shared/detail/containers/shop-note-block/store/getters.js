import { get } from '@goods/utils';

export default {
  // 店铺笔记列表
  noteList(state) {
    const { list = [] } = get(state, 'goodsExtendInfo.SHOP_NOTE', {});
    return list;
  },

  // 是否显示店铺笔记
  showShopNote(state, getters) {
    const { noteList } = getters;
    return noteList.length > 0;
  },

  // 店铺笔记模块标题
  shopNoteBlockTitle(state, getters) {
    const { noteList } = getters;
    return (noteList[0] || {}).globalTitle || '店铺笔记';
  }
};
