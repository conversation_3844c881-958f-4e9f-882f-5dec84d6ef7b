import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import { setLogger } from '@goods/common/logger';
import navigate from 'shared/utils/navigate';
import args from '@youzan/weapp-utils/lib/args';

const EVENT_TYPE = {
  VIEW: 'view',
  CLICK: 'click'
};

function genBannerId(goodsId, index) {
  return `g.${goodsId}~contentguide~${index + 1}~${makeRandomString(8)}`;
}

export default {
  onItemClick({ state, dispatch, getters }, index) {
    dispatch('reportClick', index);

    const { noteAlias } = getters.noteList[index];

    if (noteAlias) {
      const {
        salesman: { alias: sls },
        goods: { goodsId }
      } = state;
      navigate.navigate({
        url: args.add('/packages/shop/shopnote/detail/index', {
          noteAlias,
          sls,
          sourceFrom: 'goods',
          banner_id: genBannerId(goodsId, index)
        })
      });
    }
  },

  initReportView({ getters, dispatch }) {
    const { noteList } = getters;

    const stock = noteList.map((note, index) => dispatch('reportView', index));

    Promise.all(stock).catch(() => {});
  },

  // 上报view埋点
  reportView({ dispatch }, index) {
    dispatch('reportLogger', {
      type: EVENT_TYPE.VIEW,
      name: '组件曝光',
      index
    });
  },

  // 上报点击埋点
  reportClick({ dispatch }, index) {
    dispatch('reportLogger', {
      type: EVENT_TYPE.CLICK,
      name: '组件点击',
      index
    });
  },

  // 上报数据
  reportLogger({ getters }, { type, name, index }) {
    setLogger({
      et: type, // 事件类型
      ei: type, // 事件标识
      en: name, // 事件名称
      params: {
        banner_id: genBannerId(getters.goodsId, index),
        component: 'contentguide'
      }
    });
  },

  // 店铺笔记abTest曝光
  exposureLogger({ dispatch }) {
    setLogger({
      et: EVENT_TYPE.VIEW,
      ei: 'guidecontent_view',
      en: '导购内容曝光'
    });

    dispatch('initReportView');
  }
};
