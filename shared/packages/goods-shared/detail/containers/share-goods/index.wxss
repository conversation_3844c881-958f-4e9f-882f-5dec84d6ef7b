@import "shared/common/css/helper/index.wxss";

@keyframes skeleton-blink {
  50% {
    opacity: 0.6;
  }
}

.share-image__popup {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  overflow: visible !important;
  background: transparent !important;
}

.share-image__container {
  position: relative;
  width: 64vw;
  max-height: 100vh;
  margin: 0 auto;
}

.share-image__content {
  width: 100%;
  max-height: 70vh;
  overflow: auto;
  border-radius: 8px;
  -webkit-overflow-scrolling: touch;

  /* 解决 iOS overflow 无效问题 */
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

.share-image__content::-webkit-scrollbar {
  width: 2px;
}

.share-image__close-btn {
  position: absolute;
  right: 0;
  top: -32px;
  height: 24px;
  width: 24px;
  background-image: url("https://b.yzcdn.cn/public_files/43b23880f9b5c4bffe0297dc8e5952ff.png");
  background-size: 100% 100%;
}

.share-image__close-btn::after {
  transform: translateX(-50%) translateY(-50%) rotateZ(45deg);
}

.share-image__close-btn::before {
  transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
}

.share-image__img {
  display: block;
  width: 100%;
}

.share-image__skeleton {
  display: block;
  background: #fff;
}

.share-image__skeleton__img {
  display: block;
  width: 100%;
  max-height: 100%;
  overflow: hidden;
  animation: skeleton-blink 1.2s ease-in-out infinite;
}

.share-image__save {
  position: absolute;
  display: flex;
  width: 100%;
  justify-content: center;
  margin-top: 24px;
}

.share-image__save-btn {
  width: 160px;
  line-height: 36px;
  text-align: center;
  border: 1px solid #fff;
  border-radius: 50px;
  font-size: 14px;
  color: #fff;
}

.share-image__save-tip {
  margin-top: 10px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

@media screen and (max-width: 360px) {
  .share-image__container {
    transform: scale(.8);
  }
}

.action-sheet__cancel, .action-sheet__item {
  height:50px;
  font-size:16px;
  line-height:50px;
  text-align:center;
  background-color:#fff
}
.action-sheet__cancel--hover, .action-sheet__item--hover {
  background-color:#f2f3f5
}
.action-sheet__cancel {
  height:60px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.action-sheet__cancel::before {
  display:block;
  height:10px;
  background-color:#f8f8f8;
  content:" "
}
.action-recommend {
  height: 66px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.action-recommend .big-word {
  font-size: 16px;
  height: 20px;
  line-height: 20px;
  color: black;
  display: flex;
}
.action-recommend .small-word {
  font-size: 12px;
  height: 14px;
  line-height: 14px;
  color: #bbb;
  margin-top: 4px;
}
.shared-popup {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
