import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState, mapMutations } from '@youzan/vanx';
import authorize from 'shared/utils/authorize';
import getApp from 'shared/utils/get-safe-app';
import args from '@youzan/weapp-utils/lib/args';
import get from '@youzan/weapp-utils/lib/get';
import logv3 from 'utils/log/logv3';
import { loadImage } from '@goods/utils';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';

const app = getApp();

GoodsComponent({
  mapData: {
    recommendGoodThings() {
      const { state = {} } = app.$store;

      return state.shop && state.shop.recommend_good_things;
    },

    ...mapState({
      sharePopShow: (state) => {
        return state.displayPop.sharePopShow;
      },
      title: (state) => get(state, 'goods.title'),
      path: (state) => get(state, 'goods.path'),
      alias: (state) => get(state, 'goods.alias'),
      goodsId: (state) => get(state, 'goods.id'),
      virtualTicket: (state) => get(state, 'goods.virtualTicket', {}),

      // 网店自建商品不支持好物圈分享；http://xiaolv.qima-inc.com/#/demand/search?show=true&ids=42826
      isOnlineShopCreated: (state) => get(state, 'goods.createdType', 0),
      goodsImageList: (state) => get(state, 'goods.pictures', []),
      salesmanAlias: (state) => get(state, 'salesman.alias'),
      hasActivity: (state, getters) => getters.hasShareBenefitActivity,
    }),
  },

  getters: [
    'hasShareBenefitActivity',
    'shareCopyInfo',
    'sharePosterInfo',
    'activityInfoForPrice',
  ],

  data: {
    // 控制预览蒙层是否展示
    showModal: false,
    // 商品海报图片
    previewImg: null,
    previewLoaded: false,
    closeOnClickOverlay: true,
    isSupportOpenBusinessView: !!wx.openBusinessView,
  },

  observers: {
    showModal(showModal) {
      this.$dispatch('setPageScrollLocked', showModal);
    },
  },

  methods: {
    ...mapMutations(['hideSharePop']),

    /**
     * @deprecated 临时方法
     * @description 直接生成销售员海报
     */
    drawSalemanCard() {
      this.doDrawPoster();
    },

    closeActionSheet() {
      this.hideSharePop();
      this.triggerEvent('finished');
    },

    handleFriends() {
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'goods_sharefriend',
          en: '点击分享给好友',
          si: '', // 店铺id，没有则置空
          params: {},
        });
    },

    doDrawPoster() {
      this.closeActionSheet();
      this.setYZData({
        showModal: true,
      });
      if (this.data.previewLoaded) return;

      this.makePoster();
    },

    handlePoster() {
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'goods_photo',
          en: '点击生成海报',
          si: '', // 店铺id，没有则置空
          params: {},
        });

      this.doDrawPoster();
    },

    // 复制链接
    handleCopyLink() {
      const query = this.data.shareCopyInfo;
      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'share',
          en: '复制链接',
          si: '', // 店铺id，没有则置空
          params: {
            share_cmpt: 'copy_link',
          },
        });
      Object.keys(query).forEach((it) => {
        if (!query[it]) delete query[it];
      });

      this.closeActionSheet();
      app
        .request({
          path: '/wscshop/showcase/copyLink/exchange.json',
          data: {
            alias: this.data.alias,
            goodsId: this.data.goodsId,
            type: 'new_goods',
            ...query,
          },
        })
        .then((res = {}) => {
          if (!res.value) return;
          wx.setClipboardData({
            data: `【${this.data.title}】点击链接查看商品。${res.value}`,
            success: () => {
              wx.showToast({
                title: '复制成功',
                icon: 'none',
                duration: 2000,
              });
            },
            fail: () => {
              wx.showToast({
                title: '复制失败',
                icon: 'none',
                duration: 2000,
              });
            },
          });
        });
    },

    // 推荐好物
    handleRecommend() {
      wx.showLoading({
        title: '正在同步',
        mask: true,
      });
      const openBusinessView = () => {
        wx.hideLoading();
        wx.openBusinessView({
          businessType: 'friendGoodsRecommend',
          extraData: {
            product: {
              item_code: this.shareGoodsId || this.data.goodsId,
              title: this.data.title,
              image_list: this.data.goodsImageList.map((it) => it.url),
            },
          },
          fail(e) {
            const { errCode = -1, errMsg } = e;
            if (errCode !== 0 && errCode !== -3) {
              wx.showToast({
                title: '推荐失败' + errMsg,
                icon: 'none',
                duration: 2000,
              });
            }
          },
        });
      };
      app
        .request({
          path: '/wscshop/goods/query-mall.json',
          data: {
            alias: this.data.alias,
          },
        })
        .then((res) => {
          // 保留好物圈分享商品id
          this.shareGoodsId = res.goodsId;

          if (res.need_wait) {
            setTimeout(openBusinessView, 1500);
          } else {
            openBusinessView();
          }
        })
        .catch(() => {
          wx.hideLoading();
          wx.showToast({
            title: '同步失败',
            icon: 'none',
            duration: 2000,
          });
        });
      this.closeActionSheet();
    },

    makePoster() {
      const dcPs = logv3.getDCPS();
      const offlineId = app.getOfflineId();
      const { chainStoreInfo = {} } = app.getShopInfoSync();
      const { isMultiOnlineShop } = chainStoreInfo;
      const kdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId(); // 连锁需要用总店的店铺ID，才能获取到小程序码

      const path = this.data.path.split('?')[0];
      let query = args.getAll(this.data.path);
      if (this.data.salesmanAlias) {
        query = {
          ...query,
          ...getSalesmanParamsObject({ sl: this.data.salesmanAlias }),
        };
      }
      // 参照生成QRcode的做法生成入参
      const scene = {
        share_from: 'poster',
        kdtId: String(kdtId),
        page: path,
        guestKdtId: String(app.getKdtId()),
        ...query,
        dc_ps: dcPs,
        offlineId,
      };

      const poster = this.data.sharePosterInfo;

      // 合并自定义内容
      if (poster && poster.scene) {
        Object.assign(scene, poster.scene);

        // 删除scene
        delete poster.scene;
      }

      const posterParams = {
        ...query,
        kdtId,
        scene: encodeURIComponent(JSON.stringify(scene)),
        page: 'pages/common/blank-page/index',
        // sid: app.getSessionId(),
        alias: this.data.alias,
        // 支持隐藏分享人信息
        isSupportHideUserInfo: true,
      };
      if (query.activityId) {
        posterParams.activityId = query.activityId;
        if (query.type) {
          posterParams.activityType =
            query.type === 'helpcut' ? 'helpCut' : query.type;
        } else {
          posterParams.activityType = query.activityType;
        }
      }
      if (offlineId) {
        posterParams.offlineId = offlineId;
      }

      // 合并自定义内容
      if (poster) {
        Object.assign(posterParams, poster);
      }

      this.getPoster(posterParams, true)
        .then((res) => {
          if (!res.imgUrl) {
            return Promise.reject('生成失败');
          }

          return loadImage(res.imgUrl);
        })
        .then((src) => {
          this.successPoster(src);
        })
        .catch((e) => {
          this.failedPoster(e);
        });
    },

    getPoster(data, needRetry = false, retry = 0) {
      return app
        .request({
          path: '/wscgoods/weapp-poster/goods.json',
          data: {
            retry,
            ...data,
            noAuction: true,
            autoHeight: 1,
          },
        })
        .catch((e) => {
          if (needRetry) {
            return this.getPoster(data, false, 1);
          }
          return Promise.reject(e);
        });
    },

    successPoster(previewImg) {
      this.setYZData({ previewImg }, () => {
        this.triggerEvent('success');
      });
    },

    failedPoster(e) {
      const errMsg = e.msg || '';
      this.setYZData({
        showModal: false,
      });
      wx.showToast({
        title: errMsg || '生成图片路径失败',
        icon: 'none',
      });
      app.getUserInfo((res) => {
        app.logger.appError({
          name: 'draw_goods_poster_error',
          message: '绘制商品海报失败',
          detail: {
            errMsg,
            userName: get(res, 'userInfo.nickName'),
          },
        });
      });
      this.triggerEvent('failed', { err: e });
    },

    closeShareImageModal() {
      this.setYZData({
        showModal: false,
      });
    },

    // 点击保存（ 成功后自动关闭弹层，抛出 saved 事件 ）
    clickSaveImage() {
      const { previewImg } = this.data;
      if (!previewImg) {
        return;
      }

      wx.showLoading({ title: '保存中' });

      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'goods_savephoto',
          en: '点击海报保存按钮',
          si: '', // 店铺id，没有则置空
          params: {},
        });

      authorize('scope.writePhotosAlbum')
        .then(() => {
          this.saveShareImage(previewImg)
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000,
              });
              this.closeShareImageModal();
              this.triggerEvent('saved');
            })
            .catch(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000,
              });
            });
        })
        .catch(() => {
          wx.hideLoading();
          wx.showModal({
            content: '需要同意将分享图片保存到相册，点击确定后跳转至设置页操作',
            success: (e) => {
              if (e.cancel) return;
              wx.openSetting({
                success: ({ authSetting }) => {
                  if (authSetting['scope.writePhotosAlbum']) {
                    this.clickSaveImage();
                  }
                },
              });
            },
          });
        })
        .catch((err) => {
          console.log(err);
        });
    },

    // 保存图片 api 调用
    saveShareImage(tempFilePath) {
      return new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: tempFilePath,
          success: resolve,
          fail: reject,
        });
      });
    },

    onImageLoad() {
      this.setYZData({ previewLoaded: true });
    },
  },
});
