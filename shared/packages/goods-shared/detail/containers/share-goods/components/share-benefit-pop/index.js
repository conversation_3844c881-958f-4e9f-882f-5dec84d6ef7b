import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState, mapMutations } from '@youzan/vanx';
import api from '@goods/utils/api';
import Toast from '@vant/weapp/dist/toast/toast';
import { setLogger } from '@goods/common/logger';
import { get } from '@goods/utils';
import getAuthorizedState from 'shared/utils/get-authorized-state';

GoodsComponent({
  mapData: mapState({
    kdtId: (state) => state.shop.kdtId
  }),

  getters: ['shareBenefitActivity'],

  data: {
    inited: false,
    showPop: false,
    shareTag: '',
    popHeight: '',
  },

  ready() {
    this.init();
  },

  methods: {
    ...mapMutations(['setAuthorizedState']),

    togglePop() {
      this.setYZData({
        showPop: !this.data.showPop
      });
    },

    changeHeight() {
      setTimeout(() => {
        this.setYZData({
          popHeight: 293,
        });
      }, 300);
    },

    init() {
      const pages = getCurrentPages() || [];
      const query = get(pages[pages.length - 1], 'options');

      getAuthorizedState().then(data => {
        this.setAuthorizedState(data.mobile);

        if (!query.shareTag) return;

        if (data.mobile) {
          this.joinActivity(query.shareTag);
        } else {
          this.setYZData({
            inited: true,
            shareTag: query.shareTag,
          });
          this.togglePop();
        }
      });
    },

    // 处理授权成功状态
    handleSuccess() {
      this.togglePop();
      this.setAuthorizedState(true);
      this.joinActivity();
    },

    // 被分享人打开参与活动
    joinActivity(shareTag = '') {
      const { data } = this;
      const { activityId, activityAlias } = data.shareBenefitActivity;
      const params = {
        kdtId: data.kdtId,
        shareTag: shareTag || data.shareTag
      };

      // 上报埋点
      setLogger({
        et: 'custom', // 事件类型
        ei: 'visit_share_activity', // 事件标识
        en: '被分享人登录访问', // 事件名称
        params: {
          activityId,
          ...params
        }
      });

      // 参与活动
      api
        .post({
          url: '/wscgoods/activity-api/join-share-benefit.json',
          headers: {
            'content-type': 'application/json'
          },
          data: {
            activityAlias,
            ...params
          }
        })
        .then((data) => {
          if (data && data.alias) {
            Toast('已帮好友获得奖励，分享即得好礼！');
          }
        });
    },
  }
});
