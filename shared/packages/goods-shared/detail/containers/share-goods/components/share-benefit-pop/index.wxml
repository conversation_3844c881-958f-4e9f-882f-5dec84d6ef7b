<van-popup
  wx:if="{{ inited }}"
  show="{{ showPop }}"
  round
  z-index="{{ 900 }}"
  custom-class="share-benefit-pop"
  custom-style="height: {{ popHeight }}px;"
  position="bottom"
  bind:close="togglePop"
>
  <view class="share-benefit-pop__header">登录提示</view>
  <view class="share-benefit-pop__main">
    你还没有登录，登录后浏览商品好友可获得分享奖励，你也可以分享获得好礼！
  </view>
  <user-authorize
    authTypeList="{{ ['mobile'] }}"
    catchtap="changeHeight"
    bind:fail="togglePop"
    bind:next="handleSuccess"
  >
    <theme-view
      custom-class="share-benefit-pop__btn"
      block
      bg="main-bg"
      color="main-text"
    >
      立即登录
    </theme-view>
  </user-authorize>
  <view class="share-benefit-pop__bottom" bind:tap="togglePop">
    暂不登录，先逛逛
  </view>
</van-popup>