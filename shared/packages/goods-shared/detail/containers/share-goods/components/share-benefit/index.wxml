<view class="share-benefit">
  <!-- 分享有礼活动展示 -->
  <view class="share-benefit__main">
    <van-icon
      size="18px"
      color="#DE373E"
      name="https://b.yzcdn.cn/public_files/4be833507d5d019bc3fde956b2588392.png"
    />
    <text class="share-benefit__main-text">
      分享{{ shareBenefitActivity.shareNum }}位好友可得
      <text class="share-benefit__text--red">{{ shareBenefitActivity.rewardText }}</text>
      {{ shareBenefitActivity.rewardName }}
    </text>
  </view>
  <view class="share-benefit__action">
    <text
      wx:if="{{ isAuthorized }}"
      class="share-benefit__action-desc"
      bindtap="goDetailPage"
    >
      {{ activityRule }}查看详情
    </text>
    <user-authorize
      wx:else
      authTypeList="{{ ['mobile'] }}"
      bind:next="updateAuthorizedState"
    >
      <text class="share-benefit__action-login">立即登录</text>
    </user-authorize>
    <van-icon size="14px" color="#969799" name="arrow" />
  </view>
</view>
