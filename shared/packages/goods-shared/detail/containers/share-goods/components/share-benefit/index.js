import { GoodsComponent } from '@goods/common/vanx-component';
import { jumpLink, args, get } from '@goods/utils';
import { mapState, mapMutations } from '@youzan/vanx';

GoodsComponent({
  getters: ['shareWxData', 'shareBenefitActivity'],

  mapData: mapState({
    alias: (state) => get(state, 'goods.alias'),
    isAuthorized: (state) => state.shareBenefit.isAuthorized,
    activityRule(state, getters) {
      const { rewardTimesLimitText: ruleText } = getters.shareBenefitActivity;
      return ruleText ? ruleText + '，' : '';
    },
  }),

  methods: {
    ...mapMutations(['setAuthorizedState']),

    // 跳转活动规则详情页
    goDetailPage() {
      const {
        rewardText: shareText,
        activityId,
      } = this.data.shareBenefitActivity;
      const url = args.add('/packages/ump/share-benefit/index', {
        shareText,
        activityId,
        alias: this.data.alias,
      });
      jumpLink(url);
    },

    closePop() {
      this.triggerEvent('close');
    },

    updateAuthorizedState() {
      this.setAuthorizedState(true);
    },
  },
});
