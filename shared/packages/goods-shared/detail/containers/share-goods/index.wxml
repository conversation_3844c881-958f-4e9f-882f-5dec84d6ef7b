<!-- 分享遮罩层 -->
<van-popup
  show="{{ showModal }}"
  safe-area-inset-bottom
  z-index="{{ 999 }}"
  custom-class="share-image__popup"
>
  <view class="share-image__container">
    <view class="share-image__close-btn" bindtap="closeShareImageModal" />
    <view class="share-image__content">
      <image
        hidden="{{ !previewLoaded }}"
        class="share-image__img"
        src="{{ previewImg }}"
        mode="widthFix"
        bindload="onImageLoad"
      />
      <view hidden="{{ previewLoaded }}" class="share-image__skeleton">
        <image
          class="share-image__skeleton__img"
          src="https://img01.yzcdn.cn/upload_files/2021/06/17/Fqce4NoaZCrv15JKiEHygY5CRj63.png"
          mode="widthFix"
        />
      </view>
    </view>
    <view wx:if="{{ previewLoaded }}" class="share-image__save">
      <view class="share-image__save-btn" bindtap="clickSaveImage">保存图片</view>
    </view>
  </view>
</van-popup>
<van-action-sheet
  z-index="{{ 299 }}"
  show="{{ sharePopShow }}"
  class="shared-popup"
  custom-style="border-radius: {{ hasActivity ? '20px 20px 0 0' : 'none' }};"
  close-on-click-overlay="{{ closeOnClickOverlay }}"
  round
  bind:close="closeActionSheet"
  bind:cancel="closeActionSheet" >
  <share-benefit wx:if="{{ hasShareBenefitActivity }}" bind:close="closeActionSheet" />
  <view>
    <button
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--bottom"
      open-type="share"
      bind:tap="handleFriends"
    >
      发送给朋友
    </button>
    <user-authorize authTypeList="{{ ['nicknameAndAvatar'] }}" bind:next="handlePoster">
      <button
        hover-class="action-sheet__item--hover"
        class="action-sheet__item van-hairline--bottom"
      >
        生成海报
      </button>
    </user-authorize>
    <button
      wx:if="{{ activityInfoForPrice.type === 'helpCut' ? false : true  }}"
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--bottom"
      bind:tap="handleCopyLink"
    >
      复制链接
    </button>
    <!--button
      wx:if="{{ isSupportOpenBusinessView && recommendGoodThings && virtualTicket.validityType !== 3 && !isOnlineShopCreated }}"
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--left action-recommend"
      bind:tap="handleRecommend"
    >
      <view class="big-word">推荐好物</view>
      <view class="small-word">(分享到微信圈子)</view>
    </button-->
  </view>
  <view
    class="action-sheet__cancel"
    hover-class="action-sheet__cancel--hover"
    hover-stay-time="70"
    bind:tap="closeActionSheet"
  >
    取消
  </view>
</van-action-sheet>

<share-benefit-pop wx:if="{{ hasShareBenefitActivity }}" />
