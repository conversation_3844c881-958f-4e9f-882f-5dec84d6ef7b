
<van-cell-group border="{{ false }}">
  <van-cell
    border="{{ false }}"
    wx:if="{{ tags.length > 0 }}"
    value-class="review-tags"
    custom-class="review-detail-tags"
  >
    <theme-view
      bg="{{ tag.bgColor || 'main-bg' }}"
      opacity="0.1"
      color="{{ tag.color || 'main-bg' }}"
      wx:for="{{ tagList }}"
      wx:for-item="tag"
      wx:key="index"
      data-index="{{ index }}"
      class="review-detail-tag"
      custom-class="review-detail-tag__inner"
      bind:tap="showEvaluationList"
    >
      <text style="color: {{ tag.color || '' }}" class="review-detail-tag__name">{{ tag.name }}</text>
    </theme-view>
  </van-cell>

  <van-cell
    wx:for="{{ list }}"
    wx:for-item="item"
    wx:key="index"
    border="{{ false }}"
    custom-class="review-detail__content-cell"
    bind:tap="showEvaluationList"
  >
    <item-header
      rate="{{ item.score }}"
      avatar="{{ item.reviewerAvatar }}"
      is-buy-again="{{ item.userReBuyModel }}"
      member="{{ item.userLevelModel }}"
      nickname="{{ item.reviewerNickName }}"
    />

    <item-content
      content="{{ item.content }}"
      sku-desc="{{ item.specification }}"
      cdnImagesList="{{ item.cdnImagesList }}"
      bigImagesList="{{ item.bigImagesList }}"
    />
  </van-cell>
</van-cell-group>
