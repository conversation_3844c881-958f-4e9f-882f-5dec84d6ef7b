import { GoodsComponent } from '@goods/common/vanx-component';
// import { getUrl, GOODS_REVIEW } from '@goods/common/page-path';
// import { jumpLink } from '@goods/utils';
import { getPlugins } from '@youzan/ranta-helper-tee';

// 特殊 评价标签 配置
const TAG_MAP = [
  {
    name: '正品',
    color: '#16a800',
    bgColor: '#00e403',
  },
];

GoodsComponent({
  name: 'review-detail',

  properties: {
    list: Array,
    tags: {
      type: Array,
      value: [],
      observer: 'onTagsChange',
    },
    alias: String,
    showSummaryTag: Boolean,
  },

  data: {
    tagList: [],
  },

  methods: {
    showEvaluationList({ currentTarget }) {
      const { index } = currentTarget.dataset;
      const params = {
        alias: this.data.alias,
        showSummaryTag: this.data.showSummaryTag,
      };

      if (index !== undefined) {
        const tag = this.data.tags[index];
        params.tagCode = tag.code;
      }

      getPlugins().dmc.navigate('GoodsEvaluationList', params);
    },
    onTagsChange() {
      const { tags } = this.data;

      this.setYZData({
        tagList: tags.map((tag) => ({
          ...tag,
          ...this.computedTagColor(tag.name),
        })),
      });
    },

    computedTagColor(name) {
      const value = TAG_MAP.find((v) => v.name === name);

      if (value) {
        return value;
      }

      return '';
    },
  },
});
