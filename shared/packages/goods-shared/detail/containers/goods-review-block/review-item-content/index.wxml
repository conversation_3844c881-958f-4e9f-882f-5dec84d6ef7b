
<view class="review-content">
  <view>{{ content }}</view>

  <view
    class="review-images"
    bind:tap="previewImage"
  >
    <view
      wx:for="{{ cdnImagesList }}"
      wx:for-item="image"
      data-index="{{ index }}"
      wx:key="{{ index }}"
      class="image-item"
    >
      <view class="review-images_img">
        <image mode="aspectFill" src="{{ image }}" />
      </view>
    </view>
  </view>

  <view class="sku-desc"> {{ skuDesc }} </view>
</view>
