import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';
import { memberLevelIcon } from '../constant';

GoodsComponent({
  name: 'review-item-header',

  properties: {
    rate: Number,
    avatar: String,
    isBuyAgain: {
      type: Number,
      value: 0
    },
    member: Object,
    nickname: String
  },

  mapData: mapState({
    memberLevel(state, getters) {
      const { member } = this.data;
      if (+getters.evaluationMemberLabel === 0 && member) {
        return memberLevelIcon[member.level];
      }
      return '';
    },

    memberName(state, getters) {
      const { member } = this.data;
      if (+getters.evaluationMemberLabel === 1 && member) {
        return member.levelName || '';
      }
      return '';
    }
  })
});
