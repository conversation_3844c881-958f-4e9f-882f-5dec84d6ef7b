
<view class="review-item-header">
  <image class="review-item-header__avatar" src="{{ avatar }}" />
  <view class="review-item-header__info">
    <rich-text class="review-item-header__info-name" nodes="{{ nickname }}"/>
    <view class="review-item-header__flag-block">
      <!--会员等级-->
      <image
        wx:if="{{ memberLevel }}"
        src="{{ memberLevel }}"
        mode="heightFix"
        style="width: {{ member && member.level.startsWith('SVIP') ? 57 : 22 }}px;"
        class="review-item-header__member-level"
      />

      <!-- 会员名称 -->
      <view wx:if="{{ memberName }}" class="review-item-header__member-name">
        {{ memberName }}
      </view>

      <!-- 买了又买标 -->
      <view wx:if="{{ isBuyAgain }}" class="review-item-header__buy-again" >
        买了又买
      </view>
    </view>
  </view>
  <van-rate
    custom-class="review-item-header__rate"
    value="{{ rate / 20 }}"
    count="{{ 5 }}"
    size="{{ 10 }}"
    color="#FF4444"
    void-icon="star"
    void-color="#DCDEE0"
    style="flex-shrink:0"
    readonly
  />
</view>
