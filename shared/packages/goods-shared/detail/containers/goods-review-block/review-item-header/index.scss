.review-item-header {
  display: flex;
  color: #666;

  &__avatar {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    border-radius: 28px;
  }

  &__member-level {
    display: block;
    height: 16px;
    margin-left: 4px;
  }

  &__member-name {
    display: flex;
    align-items: center;
    height: 16px;
    border-radius: 8px;
    font-size: 12px;
    color: #e4c3ab;
    background-color: #3c332e;
    padding: 0 4px 0 6px;
    margin-left: 4px;

    &::before {
      display: inline-block;
      content: '';
      width: 10px;
      height: 8px;
      margin-right: 2px;
      background-image: url('https://img01.yzcdn.cn/public_files/60bb30530f013f1aa33b0ebd7c9bfd87.png');
      background-size: cover;
    }
  }

  &__buy-again {
    display: inline-flex;
    align-items: center;
    margin-left: 4px;
    padding: 0 4px;
    height: 16px;
    line-height: 16px;
    background: #ee0a24;
    color: #fff;
    border-radius: 8px;
    font-size: 12px;
  }

  &__info {
    flex: 1;
    display: flex;
  }

  &__info-name {
    white-space: nowrap;
  }

  &__rate {
    flex-shrink: 0;
    text-align: right;
  }

  &__flag-block {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
}
