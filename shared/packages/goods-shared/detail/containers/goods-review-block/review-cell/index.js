import { GoodsComponent } from '@goods/common/vanx-component';
import { setLogger } from '@goods/common/logger';
import { getPlugins } from '@youzan/ranta-helper-tee';

GoodsComponent({
  name: 'review-cell',

  properties: {
    alias: String,
    kdtId: Number,
    showSummaryTag: Bo<PERSON>an,
    count: {
      type: Number,
      value: 0,
      observer(value) {
        if (value === 0) return;
        this.formatValue();
        this.formatTitle();
      },
    },
    rate: {
      type: Number,
      value: 0,
      observer(value) {
        if (value === 0) return;
        this.formatValue();
      },
    },
  },

  data: {
    title: '商品评价',
    cellValue: '暂无评价',
  },

  methods: {
    formatTitle() {
      const { count } = this.data;
      this.setYZData({
        title: `商品评价 (${count})`,
      });
    },

    formatValue() {
      const { count, rate } = this.data;
      let value = '暂无评价';

      if (count > 0) {
        if (rate > 0) {
          value = `好评率：${rate}%`;
        } else {
          value = '查看全部';
        }
      }

      this.setYZData({
        cellValue: value,
      });
    },

    jumpPage() {
      setLogger({
        et: 'click', // 事件类型
        ei: 'customer_reviews', // 事件标识
        en: '点击评价', // 事件名称
      });

      if (this.data.count === 0) {
        return;
      }

      getPlugins().dmc.navigate('GoodsEvaluationList', {
        alias: this.data.alias,
        showSummaryTag: this.data.showSummaryTag,
      });
    },
  },
});
