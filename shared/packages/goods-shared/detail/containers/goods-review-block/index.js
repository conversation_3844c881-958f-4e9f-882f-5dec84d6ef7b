import { GoodsComponent } from '@goods/common/vanx-component';
import api from '@goods/utils/api';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { toFixed } from '@goods/utils';

GoodsComponent({
  name: 'GoodsReviewBlock',

  state: ['goods', 'shop', 'shopConfig'],

  getters: ['showCustomerReviews'],

  data: {
    review: {
      count: 0,
      reviews: [],
      labels: [],
    },
    rate: 0,
  },

  ready() {
    if (!this.data.showCustomerReviews) return;

    const { goods = {} } = this.data;

    this.fetchReviewDetail(goods.alias);
  },

  methods: {
    // 格式化内容标签数据
    formatSummaryTags(itemSummary) {
      return {
        count: itemSummary.evaluationTotalCount,
        tags: itemSummary.tagSummaryModels.map((item) => ({
          name: item.tagName,
          code: item.tagCode,
          count: item.count,
        })),
      };
    },

    // 格式化固定标签数据
    // 商详页不再展示固定标签
    formatBasicTags(reviewCountData) {
      return {
        count: reviewCountData.count,
        tags: [],
      };
    },

    formatReviewData(reviewInfo) {
      // 取评论信息中需要的信息
      const evaluationModels = reviewInfo.evaluationModels || [];

      if (evaluationModels.length > 0) {
        return [
          {
            ...reviewInfo,
            ...evaluationModels[0],
          },
        ];
      }

      return [];
    },

    fetchReviewDetail(alias) {
      api
        .get({
          url: '/wscgoods/detail-api/review-info-fast.json',
          data: { alias, allowSummaryTag: true },
          options: {
            useCdn: true,
          },
          headers: {},
        })
        .then((data) => {
          const {
            reviewCountData = {},
            reviewInfo = {},
            itemSummary,
            abTraceId,
          } = data || {};
          const showSummaryTag = !!(
            itemSummary && Array.isArray(itemSummary.tagSummaryModels)
          );
          const tagsInfo = showSummaryTag
            ? this.formatSummaryTags(itemSummary)
            : this.formatBasicTags(reviewCountData);

          this.setYZData({
            review: {
              abTraceId,
              ...tagsInfo,
              reviews: this.formatReviews(reviewInfo),
              showSummaryTag,
            },
          });

          const { goodsFavorableRate } = this.data.shopConfig;

          if (
            goodsFavorableRate &&
            goodsFavorableRate.show &&
            itemSummary.highPraiseRate
          ) {
            const rate = +toFixed(itemSummary.highPraiseRate * 100, 2);

            if (rate >= goodsFavorableRate.rate) {
              this.setYZData({
                rate,
              });
            }
          }
        });
    },

    formatReviews(reviewInfo) {
      let reviewData = [];

      const evaluationModels = reviewInfo.evaluationModels || [];
      if (evaluationModels.length > 0) {
        const review = Object.assign(
          reviewInfo || {},
          evaluationModels[0] || {}
        );

        reviewData = [review];
      }

      // 评价晒图要测一下
      return reviewData.map((item) => {
        if (Array.isArray(item.picturesList)) {
          item.cdnImagesList = item.picturesList.map((image) =>
            cdnImage(image, '!300x0.jpg')
          );
          item.bigImagesList = item.picturesList.map((image) =>
            cdnImage(image, '!730x0.jpg')
          );
        } else {
          item.cdnImagesList = [];
          item.bigImagesList = [];
        }
        return item;
      });
    },
  },
});
