import { GoodsComponent } from '@goods/common/vanx-component';
import { isNewIphone } from 'shared/utils/browser/device-type';
import { mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'GoodsBottom',

  getters: ['isGroupOn', 'isShowInSourcingFissionTip'],

  state: ['env', 'shopConfig', 'hideFastBuyBottom'],

  data: {
    deviceType: isNewIphone() ? 'iPhone-X' : ''
  },

  mapData: mapState({
    showBottomBtns(state) {
      return state.saas.submitBlock.showGoodsBottom;
    }
  })
});
