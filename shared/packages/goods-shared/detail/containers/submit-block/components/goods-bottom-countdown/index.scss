@import '~mixins/index.scss';

$height: 44px;

.wrapper {
  width: 100%;
  height: $height;
  padding: 0 12px;
  box-sizing: border-box;
}

.goods-bottom-countdown {
  display: flex;
  align-items: center;
  justify-content: left;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.75);
  border-radius: 8px;
  position: relative;

  .icon {
    width: 26px;
    height: 26px;
    margin: 0 8px;
    background-image: url(https://b.yzcdn.cn/public_files/d5cfc41d6686385de67c1834aef63ba0.png);
    background-size: 100% 100%;

    &--coupon {
      background-image: url(https://b.yzcdn.cn/public_files/cb59b2c8d555cf215bc72a1c22ae2263.png);
    }
  }

  .text {
    color: #fff;
    font-size: 14px;
  }

  .text-strong {
    margin-left: 10px;
    color: #ffc81e;
  }

  .text-countdown {
    display: inline-block;
    width: 64px;
    margin: 0 6px;
    white-space: nowrap;
    font-weight: bold;
  }

  .close {
    position: absolute;
    right: 0;
    top: 0;
    height: $height;
    width: $height;
    justify-content: flex-end;
    padding-right: 10px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }
}
