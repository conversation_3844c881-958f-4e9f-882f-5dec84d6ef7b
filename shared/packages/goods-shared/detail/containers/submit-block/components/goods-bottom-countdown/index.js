import { GoodsComponent } from '@goods/common/vanx-component';
import { setLogger } from '@goods/common/logger';
import { format } from '@youzan/weapp-utils/lib/time';
import { mapActions, mapState } from '@youzan/vanx';
import { timerBehaivor } from '@goods/common/behaviors';

GoodsComponent({
  behaviors: [timerBehaivor],

  getters: ['showBottomCountdown'],

  data: {
    show: false,
    countdown: '',
  },

  computed: {
    ...mapState(['buttonUmpTimelimitModel']),
  },

  mapData: mapState({
    isCoupon: (state) => state.buttonUmpTimelimitModel.key === 'coupon',
    activityName: (state) => {
      const { key, umpTitle = '营销' } = state.buttonUmpTimelimitModel;
      const suffix = '活动';

      // 个别活动不需要活动后缀
      if (['bale', 'secondHalfDiscount'].indexOf(key) > -1) {
        return umpTitle;
      }

      return umpTitle + suffix;
    }
  }),

  ready() {
    this.timeRunner();
  },

  detached() {
    clearTimeout(this.tid);
  },

  methods: {
    ...mapActions(['handlePromotionCoupon']),

    timeRunner() {
      if (!this.data.showBottomCountdown) {
        return;
      }

      const { leftTime } = this.buttonUmpTimelimitModel;
      const now = Date.now();
      const _leftTime = leftTime - now;

      if (!leftTime || _leftTime <= 0) {
        this.setYZData({
          show: false,
        });
        clearTimeout(this.tid);
        return;
      }

      this.setYZData({
        countdown: this.getCountDownTimeStr(_leftTime)
      });
      this.showBanner();
      this.tid = setTimeout(() => {
        this.timeRunner();
      }, 1000);
    },

    getCountDownTimeStr(leftTime) {
      const { hour, minute, second } = format(leftTime).strData;

      return `${hour}:${minute}:${second}`;
    },

    handleClick() {
      this.sendClickLogger();
      this.handlePromotionCoupon();
    },

    sendViewLogger() {
      setLogger({
        et: 'view',
        ei: 'ump_countdown_show',
        en: '商品底部活动倒计时曝光',
      });
    },

    sendClickLogger() {
      const { umpTitle, key } = this.buttonUmpTimelimitModel;

      setLogger({
        et: 'click',
        ei: 'ump_countdown_click',
        en: '商品底部营销活动banner点击',
        params: {
          umpTitle,
          key,
        },
      });
    },

    showBanner() {
      if (!this.data.show) {
        this.sendViewLogger();
      }

      this.setYZData({
        show: true,
      });
    },

    close() {
      clearTimeout(this.tid);
      this.setYZData({
        show: false,
      });
    },
  }
});
