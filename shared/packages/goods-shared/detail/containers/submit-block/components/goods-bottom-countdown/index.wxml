<view class="wrapper" wx:if="{{ show }}">
  <view class="goods-bottom-countdown" bind:tap="handleClick">
    <view class="icon {{ isCoupon ? 'icon--coupon' : '' }}"></view>
    <view class="text">{{ activityName }}仅剩 <text class="text-countdown">{{ countdown }}</text> 结束
      <text class="text-strong">查看优惠</text>
    </view>
    <view class="close" catch:tap="close">
      <van-icon name="cross" color="#999"/>
    </view>
  </view>
</view>
