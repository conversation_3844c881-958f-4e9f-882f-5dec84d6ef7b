
<view
  wx:if="{{ displayPop.miniMorePopShow }}"
  class="plus-buttons {{ displayPop.miniMorePopShow ? 'plus-buttons--show' : '' }}"
>
  <view bind:tap="maskClick" class="plus-buttons__mask"/>
  <view
    class="plus-buttons__list"
  >
    <goods-action-icon
      wx:for="{{ buttons }}"
      wx:for-item="btn"
      wx:key="{{ btn.type }}"
      text="{{ btn.text }}"
      icon="{{ btn.icon }}"
      info="{{ btn.info }}"
      data-btn="{{ btn }}"
      class="plus-buttons__item"
      bind:tap="miniButtonClick"
    />
  </view>
</view>
