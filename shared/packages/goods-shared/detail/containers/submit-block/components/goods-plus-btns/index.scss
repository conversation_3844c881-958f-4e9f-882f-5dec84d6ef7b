@import '~mixins/index.scss';

.plus-buttons {
  position: absolute;
  right: auto;
  bottom: 40px;
  max-height: inherit;
  overflow-y: initial;
  opacity: 0;
  transition: all 300ms;
  z-index: 11;

  &--show {
    bottom: 60px;
    right: 0;
    opacity: 1;
  }

  &__mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,.7);
  }

  &__list {
    background-color: $white;
    border-radius: 2px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .1);
  }

  &__item {
    height: 60px;

    &::after {
      border-width: 0 0 1px 0;
    }

    &:last-child::after {
      border-width: 0;
    }
  }

  &::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -5px;
    margin-left: -5px;
    width: 0;
    height: 0;
    border-width: 5px 5px 0;
    border-style: solid;
    border-color: $white transparent transparent;
  }
}
