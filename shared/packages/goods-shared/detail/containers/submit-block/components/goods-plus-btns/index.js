import { GoodsComponent } from '@goods/common/vanx-component';
import { mapMutations, mapActions } from '@youzan/vanx';

GoodsComponent({
  name: 'GoodsPlusBtns',
  state: ['displayPop'],
  properties: {
    buttons: Array,
  },
  methods: {
    ...mapMutations(['showMoreMiniBtns']),
    ...mapActions(['handleMiniButtonClick']),

    maskClick() {
      this.showMoreMiniBtns(false);
    },

    miniButtonClick({ currentTarget }) {
      this.handleMiniButtonClick(currentTarget.dataset.btn);
    },
  }
});
