import { GoodsComponent } from '@goods/common/vanx-component';
import { get } from '@goods/utils';
import CountDown from 'utils/countdown';
import { moment as formatDate } from 'utils/time';
import { mapActions, mapState } from '@youzan/vanx';

GoodsComponent({
  name: 'GoodsBottomBar',

  getters: ['isSupportFCodeBuy', 'showAvailableStores', 'hideAvailableStores', 'goodsBottomBarTip'],

  mapData: mapState({
    waitToSoldCountdown(state, getters) {
      const countDown = getters.waitToSoldCountdown;
      countDown && this.runCountDown(countDown);
      return countDown;
    },
    moreDayText(state) {
      const startTime = get(state, 'goodsActivity.waitToSold.startSoldTime');
      const isMoreDay = this.data.waitToSoldCountdown
        ? this.data.waitToSoldCountdown.end - state.clientTimestamp > 24 * 60 * 60 * 1000
        : false;

      return isMoreDay ? `商品将在 ${formatDate(startTime, 'YYYY-MM-DD HH:mm:ss')} 开售` : '';
    }
  }),

  methods: {
    ...mapActions(['reload']),

    runCountDown({ end }) {
      const remain = end - Date.now();
      if (remain <= 0 || isNaN(remain)) return;
      this.countDown = new CountDown(remain, {
        onChange: (timeData, timeStr) => {
          if (end - Date.now() <= 0) {
            this.countDown && this.countDown.stop && this.countDown.stop();
            return this.reload();
          }
          this.setYZData({
            timeData: timeStr
          });
        }
      });
    }
  },

  detached() {
    this.countDown && this.countDown.stop && this.countDown.stop();
  }
});
