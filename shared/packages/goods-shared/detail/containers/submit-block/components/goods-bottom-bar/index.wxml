
<view>
  <multi-store-bar wx:if="{{ showAvailableStores && !hideAvailableStores }}" />

  <view wx:elif="{{ goodsBottomBarTip.text }}" class="goods-bottom-bar">
    <navigator
      url="{{ goodsBottomBarTip.url }}"
      class="{{ !goodsBottomBarTip.url ? 'diabled' : '' }}"
      class="goods-bottom-bar__text"
    >
      {{ goodsBottomBarTip.text }}
    </navigator>
  </view>

  <view
    wx:elif="{{ waitToSoldCountdown }}"
    class="goods-bottom-bar goods-bottom-bar__countdown"
  >
    <view wx:if="{{ moreDayText }}">{{ moreDayText }}</view>
    <view wx:else>
      距离开售还剩
      <block wx:if="{{ timeData.day != 0 }}">
        <text class="num">{{ timeData.day }}</text> 天
      </block>
      <text class="num"> {{ timeData.hour }} </text> 时
      <text class="num"> {{ timeData.minute }} </text> 分
      <text class="num"> {{ timeData.second }} </text> 秒
    </view>
  </view>
</view>
