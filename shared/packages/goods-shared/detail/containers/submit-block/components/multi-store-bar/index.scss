@import '~mixins/index.scss';

.common-popup {
  border-radius: 20px 20px 0 0;
}

.goods-bottom-bar {
  margin: 0;
  min-height: 35px;
  line-height: 35px;
  background: #fff7cc;
  font-size: 12px;
  color: $orange;
  text-align: center;

  &--space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;

    > span {
      display: flex;
      align-items: center;
    }

    .van-icon-arrow {
      transform: scale(0.83);
    }
  }

  &__text {
    color: $orange;
  }
}

.available-store {
  &__title {
    position: relative;
    padding: 14px 16px;
    font-size: 14px;
    color: $text-color;

    &-text {
      width: 100%;
      font-size: 16px;
      line-height: 20px;
      color: $text-color;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-weight: 600;
    }

    &-icon {
      position: absolute;
      right: 16px;
      top: 13px;
      color: #c8c9cc;
    }

    .van-icon-close {
      font-size: 16px;
    }
  }

  &__list {
    height: 338px;
    padding: 10px 16px;
    white-space: nowrap;
    overflow: auto hidden;
    z-index: 1;
    position: relative;
    -webkit-overflow-scrolling: touch;
  }

  &__item {
    display: inline-block;
    box-sizing: border-box;
    margin-right: 8px;
    width: 120px;
    height: 72px;
    padding: 8px;
    line-height: 16px;
    vertical-align: top;
    background-color: $background-color;
    font-size: 12px;
    white-space: normal;
    border-radius: 2px;

    &:last-child {
      margin-right: 0;
    }

    &-name {
      color: $text-color;
      font-size: 12px;
      line-height: 16px;

      @include multi-ellipsis(2);
    }

    &-distance {
      display: flex;
      align-items: center;
      margin-top: 8px;
      font-size: 12px;
      color: $gray-dark;
    }

    &-icon {
      margin-right: 4px;
    }
  }
}
