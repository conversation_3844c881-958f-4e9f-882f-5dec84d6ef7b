<view>
  <view class="goods-bottom-bar goods-bottom-bar--space-between">
    <view>{{ text }}</view>
    <view bind:tap="showPop">
      去看看
      <van-icon name="arrow" />
    </view>
  </view>
  <van-popup
    show="{{ show }}"
    show-button="{{ false }}"
    custom-class="common-popup"
    position="bottom"
    bind:close="hidePop"
  >
    <view class="available-store__title">
      <view class="available-store__title-text">
        {{ text }}
      </view>
      <van-icon
        class="available-store__title-icon"
        size="22"
        name="cross"
        bind:click="hidePop"
      />
    </view>
    <view class="available-store__list">
      <navigator
        wx:for="{{ availableStores }}"
        wx:key="id"
        url="{{ item.link }}"
        class="available-store__item"
        open-type="redirectTo"
      >
        <view class="available-store__item-name">{{ item.name }}</view>
        <view
          wx:if="{{ item.distance }}"
          class="available-store__item-distance"
        >
          <van-icon class="available-store__item-icon" name="location-o" />
          距离{{ item.distance }}
        </view>
      </navigator>
    </view>
  </van-popup>
</view>
