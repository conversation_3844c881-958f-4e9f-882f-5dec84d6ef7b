import { GoodsComponent } from '@goods/common/vanx-component';
import { mapMutations, mapState } from '@youzan/vanx';
import { get } from '@goods/utils';
import { moment as formatDate } from 'utils/time';

GoodsComponent({
  name: 'SaleReminderPopup',

  state: ['showSaleReminderPopup'],

  mapData: mapState({
    startTime(state) {
      const startTime = get(state, 'goods.startSoldTime');
      return formatDate(startTime, 'YYYY-MM-DD HH:mm:ss');
    },
  }),

  methods: {
    ...mapMutations(['closeSaleReminderPopup']),
  },
});
