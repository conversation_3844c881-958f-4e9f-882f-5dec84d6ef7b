<common-popup
  show="{{ showSaleReminderPopup }}"
  position="bottom"
  custom-class="sale-reminder-popup"
  title="订阅消息，接收提醒"
  button="我知道了"
  round
  bind:close="closeSaleReminderPopup"
  bind:btn-click="closeSaleReminderPopup"
>
  <view>
    <view class="sale-reminder-popup__content">
      商品将在 <view class="sale-reminder-popup--orange">{{ startTime }}</view>
      开售，请前往小程序设置中接收订阅消息。接收后系统将在开售前5分钟发送开售提醒，请留意消息通知。
    </view>
  
    <view class="sale-reminder-popup__image">
      <van-image width="164" height="320" src="https://b.yzcdn.cn/public_files/8349457714e449e706a0ccd2841b9ca7.gif" />
    </view>
  </view>
</common-popup>