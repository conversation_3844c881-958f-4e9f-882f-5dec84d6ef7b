@import '~mixins/index.scss';

@mixin groupon-avatar {
  display: inline-block;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.goods-buttom-groupon-tip {
  background-color: $background-color !important;

  .van-cell__right-icon {
    position: relative;
    top: -1px;
  }

  &__title {
    display: flex;
    justify-items: center;
    flex: 2 !important;

    &-avatars {
      position: relative;
      margin: 0 7px 0 21px;
      height: 30px;
    }

    &-avatar {
      @include groupon-avatar;
    }

    &-avatar-up {
      position: absolute;
      left: -21px;
      top: 0;

      @include groupon-avatar;
    }

    &-desc {
      font-size: 13px;
      line-height: 30px;
      color: $text-color;

      b {
        color: $red;
      }
    }
  }
}
