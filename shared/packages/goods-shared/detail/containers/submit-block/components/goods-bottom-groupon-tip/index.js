import { mapState } from '@youzan/vanx';
import { GoodsComponent } from '@goods/common/vanx-component';
import { getUrl, GROUPON } from '@goods/common/page-path';
import { jumpLink } from '@goods/utils';
import noneSkuToOrderGoodsData from '@goods/common/none-sku-order-goods-data';

GoodsComponent({
  name: 'GoodsBottomGrouponTip',

  state: ['shop', 'goods', 'activityInfo', 'originSku', 'originActivitySku'],

  getters: [
    'grouponGoingList',
    'isSoldOut',
    'kdtId',
    'startSaleNum',
    'isDirectOrder',
    'bigButtons',
    'isLadderGroupOn',
  ],

  mapData: mapState({
    firstGrouponGoing(state, getters) {
      const grouponGoingList = getters.grouponGoingList;

      if (!grouponGoingList.length) {
        return {};
      }

      return grouponGoingList[0];
    },
    joinUrl(state, getters) {
      const { activityType } = state.activityInfo;
      const { groupAlias = '' } = this.data.firstGrouponGoing || {};
      const query = {
        alias: groupAlias,
        kdt_id: getters.kdtId,
      };

      if (activityType) {
        query.type = activityType;
      }

      return getUrl(GROUPON, query);
    },
  }),
  methods: {
    // 点击快速凑团，
    gotoGroupBuy() {
      const {
        goods,
        startSaleNum,
        activityInfo,
        originActivitySku,
        isDirectOrder,
        joinUrl,
        firstGrouponGoing,
      } = this.data;

      // 无规格商品快速凑团直接到下单页
      if (isDirectOrder) {
        const { joinNum, remainJoinNum, groupAlias } = firstGrouponGoing;
        const scaleNum = Number(joinNum) + remainJoinNum;
        Object.assign(activityInfo, { scaleNum, activityAlias: groupAlias });
        const postData = noneSkuToOrderGoodsData(
          { goods, originActivitySku, activityInfo },
          { startSaleNum }
        );
        return this.$dispatch('handleBuy', { detail: postData });
      }

      // 多规格商品拼图/或者不在白名单中 会跳转到拼团详情页；
      jumpLink(joinUrl);
    },
  },
});
