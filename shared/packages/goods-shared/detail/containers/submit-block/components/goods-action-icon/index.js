import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';
import navigate from 'shared/utils/navigate';
import { defaultThemeColors } from 'shared/common/css/theme-config';

GoodsComponent({
  name: 'GoodsPlusBtns',
  state: ['displayPop'],
  properties: {
    text: String,
    icon: String,
    info: String,
    type: String,
    theme: {
      type: String,
      observer: 'setIconColor'
    },
    iconInfo: String,
    iconDot: Boolean
  },

  mapData: mapState({
    concatInfo(state, getters) {
      if (this.data.type !== 'im') return;

      return getters.concatInfo;
    }
  }),

  methods: {
    onContactBack: navigate.contactBack,
    setIconColor() {
      this.data.theme &&
        this.getThemeType().then((data) => {
          const color = data.colors.general;
          this.setYZData({ color });
        });
    },
    getThemeType() {
      return new Promise((resolve, reject) => {
        if (!getApp) {
          return resolve({ type: 0, colors: defaultThemeColors });
        }

        const app = getApp();
        app
          .getShopTheme()
          .then((res) => {
            resolve(res);
          })
          .catch(() => reject());
      });
    }
  }
});
