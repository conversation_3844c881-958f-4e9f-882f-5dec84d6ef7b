<view class="icon-btn__view">
  <van-icon
    dot="{{ iconDot }}"
    info="{{ iconInfo }}"
    class="icon-btn__icon" 
    name="{{ icon }}"
    style="margin-left: 0"
  />
  <view class="icon-btn__text">{{ text }}</view>

  <message-contact
    wx:if="{{ type === 'im' }}"
    contact-class="message__contact-button"
    open-type="contact"
    session-from="{{ concatInfo.sourceParam }}"
    bind:contact="onContactBack"
    send-message-title="{{ concatInfo.title }}"
    send-message-path="{{ concatInfo.path }}"
    send-message-img="{{ concatInfo.picture }}"
    show-message-card
    business-id="">
  </message-contact>
</view>