import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState, mapActions } from '@youzan/vanx';
import throttle from '@youzan/weapp-utils/lib/throttle';
import { SKU_SCENE } from '@goods/common/config';
import getAuthorizedState from 'shared/utils/get-authorized-state';

// 积分商品会有渲染问题，积分兑换和原价购买两个按钮，刷新时第二个按钮还是被渲染为一个按钮的情况
const setDataThrottle = throttle(function (data) {
  wx.nextTick(() => {
    this.setData(data);
  });
}, 200);
GoodsComponent({
  name: 'GoodsBottomBtns',

  getters: [
    'bigButtonsData',
    'miniButtonsData',
    'plusButtonsData',
    'mainGradient',
    'viceGradient',
    'homepageUrl',
    'isDisplayLimitedSeckillPop',
  ],

  data: {
    isMobileAuthorized: false,
  },

  ready() {
    // 5 秒后pop消失
    setTimeout(() => {
      this.setYZData({
        isDisplayLimitedSeckillPop: false
      });
    }, 5000);

    // 获取当前用户授权状态
    getAuthorizedState().then((authorizedState) => {
      this.setYZData({
        isMobileAuthorized: !!authorizedState?.mobile
      });
    });
  },

  mapData: mapState({
    mockbigButtons(state, getters) {
      let bigButtons = [];
      bigButtons = getters.bigButtonsData;
      const isHaitao = state.goods.isHaitao;

      bigButtons.forEach((item) => {
        // 海淘商品 & 不是加购的场景需要强制手机号登录校验
        item.isHaitaoForbiden =
          isHaitao && item.skuScene !== SKU_SCENE.ADD_CART;
      });

      setDataThrottle.call(this, { bigButtons: bigButtons.slice(-2) });
      return null;
    },
  }),

  computed: mapState(['goods', 'skuData']),

  methods: {
    ...mapActions([
      'handleBigButtonClick',
      'handleMiniButtonClick',
      'handleWxSubscribeSuccess',
      'handleWxSubScribeFail',
      'reload'
    ]),

    bigButtonClick(ev) {
      const btn = ev.target.dataset.btn || ev.currentTarget.dataset.btn;
      this.handleBigButtonClick(btn); 
    },

    miniButtonClick({ currentTarget }) {
      this.handleMiniButtonClick(currentTarget.dataset.btn);
    },

    // 客户咨询按钮，埋点
    handleMessageBtnClick() {
      const app = getApp();
      const { goods = {}, skuData = {} } = this || {};

      const params = {
        goods_id: goods.id,
        goods_name: goods.title,
        goods_alias: goods.alias || '',
        picture_url: goods.picture,
        price: skuData.minPrice / 100,
        show_price: skuData.price,
        group_ids: goods.group,
      };

      app.logger &&
        app.logger.log({
          et: 'click',
          ei: 'consult',
          en: '咨询',
          params,
          si: app.getKdtId(),
        });
    },

    handleAuthorize() {
      // 授权后更新授权信息
      this.setYZData({
        isMobileAuthorized: true
      });
      this.reload();
    },
  },
});
