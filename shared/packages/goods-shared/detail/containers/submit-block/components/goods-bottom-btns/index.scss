@import '~mixins/index.scss';

.goods-action {
  &-btn {
    height: 40px;
    line-height: 40px;
    border: 0;
    border-radius: 0 !important;
    padding: 0;
    position: relative;
    font-size: 16px;
    box-sizing: border-box;
    text-decoration: none;
    text-align: center;
    vertical-align: middle;
    overflow: visible;
    flex: 1;
    width: 100%;
    background: rgba($color: #000, $alpha: 0);
    color: inherit;
    display: flex;
    justify-content: space-around;
    z-index: 1;
    font-weight: 500;

    &::after {
      content: none;
      background: rgba($color: #000, $alpha: 0);
    }
  }
}

.goods-buttons {
  height: 50px;
  display: flex;
  align-items: center;
  position: relative;

  &__mini {
    display: flex;
    position: relative;
  }

  &__big {
    flex: 1;
    height: 40px;
    overflow-x: auto;

    &-first {
      .goods-buttons__radius {
        margin-left: 5px;
        border-top-left-radius: 20px;
        border-bottom-left-radius: 20px;
      }
    }

    &:last-child {
      .goods-buttons__radius {
        margin-right: 5px;
        border-top-right-radius: 20px;
        border-bottom-right-radius: 20px;
        overflow: hidden;
      }
    }
  }

  &__big-col {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    font-size: 12px;
    padding: 6px 0;
    box-sizing: border-box;
    line-height: 1.2;

    view {
      white-space: nowrap;
    }
  }

  &__font-xl {
    font-size: 14px;
  }

  &__font-s {
    transform: scale(0.84);
    transform-origin: center center;
    white-space: nowrap;
  }

  &__font-sm {
    transform: scale(0.84);
    align-items: flex-start;
    transform-origin: left center;
    line-height: 16px;
    white-space: nowrap;
  }

  &--disabled .goods-buttons__big-col {
    opacity: 0.6;
  }

  .van-goods-action-mini-btn {
    min-width: auto;
  }

  .goods-buttons__mini--1,
  .goods-buttons__mini--2 {
    width: 70px;
  }

  .goods-buttons__mini--3,
  .goods-buttons__mini--4 {
    width: 48px;
  }

  .buyer-icon img {
    border-radius: 20px;
    object-fit: cover !important;
  }
}

.litited_kill_pop {
  position: absolute;
  top: -50px;
  right: 205px;
}

@media screen and (max-width: 320px) {
  .goods-buttons {
    .goods-buttons__mini--1,
    .goods-buttons__mini--2 {
      width: 60px;
    }

    .goods-buttons__mini--3,
    .goods-buttons__mini--4 {
      width: 43px;
    }
  }
}
